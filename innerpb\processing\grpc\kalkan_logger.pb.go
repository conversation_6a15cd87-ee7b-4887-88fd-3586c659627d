// Code generated by protoc-gen-go-mvp. DO NOT EDIT.
// versions:
// - protoc-gen-go-mvp v2.0.1
// created by mvp authors

package grpc

import (
	context "context"
	cntx "git.local/sensitive/mvp/pkg/cntx"
	ctxzap "github.com/grpc-ecosystem/go-grpc-middleware/logging/zap/ctxzap"
	zap "go.uber.org/zap"
	grpc "google.golang.org/grpc"
)

func file_inner_processing_grpc_kalkan_proto_message_MakeSignatureRequestV1ToZap(
	label string,
	in *MakeSignatureRequestV1,
) zap.Field {
	if in == nil {
		return zap.Reflect(label, in)
	}

	return zap.Dict(
		label,
		zap.Any("Payload", in.GetPayload()),
	)
}

func file_inner_processing_grpc_kalkan_proto_message_MakeSignatureResponseV1ToZap(
	label string,
	in *MakeSignatureResponseV1,
) zap.Field {
	if in == nil {
		return zap.Reflect(label, in)
	}

	return zap.Dict(
		label,
		file_inner_processing_grpc_kalkan_proto_message_MakeSignatureResponseV1_ServerResponseToZap("ServerResponse", in.GetServerResponse()),
		file_inner_processing_grpc_kalkan_proto_message_MakeSignatureResponseV1_DataToZap("Data", in.GetData()),
	)
}

func file_inner_processing_grpc_kalkan_proto_message_MakeSignatureResponseV1_DataToZap(
	label string,
	in *MakeSignatureResponseV1_Data,
) zap.Field {
	if in == nil {
		return zap.Reflect(label, in)
	}

	return zap.Dict(
		label,
		zap.Any("Signature", in.GetSignature()),
	)
}

func file_inner_processing_grpc_kalkan_proto_message_MakeSignatureResponseV1_ServerResponseToZap(
	label string,
	in *MakeSignatureResponseV1_ServerResponse,
) zap.Field {
	if in == nil {
		return zap.Reflect(label, in)
	}

	return zap.Dict(
		label,
		zap.Any("Message", in.GetMessage()),
		zap.Any("Code", in.GetCode()),
	)
}

var _ KalkanServer = (*loggedKalkanServer)(nil)

func NewLoggedKalkanServer(srv KalkanServer) KalkanServer {
	return &loggedKalkanServer{srv: srv}
}

type loggedKalkanServer struct {
	UnimplementedKalkanServer

	srv KalkanServer
}

func (s *loggedKalkanServer) MakeSignatureV1(
	ctx context.Context,
	request *MakeSignatureRequestV1,
) (
	response *MakeSignatureResponseV1,
	err error,
) {
	label := cntx.Begin(ctx, "KalkanServer_MakeSignatureV1")
	defer cntx.End(ctx, label)

	defer func() {
		if err != nil {
			ctxzap.AddFields(ctx, zap.Any(label+"error", err))
			return
		}

		ctxzap.AddFields(
			ctx,
			file_inner_processing_grpc_kalkan_proto_message_MakeSignatureResponseV1ToZap(label+"response", response),
		)
	}()

	ctxzap.AddFields(
		ctx,
		file_inner_processing_grpc_kalkan_proto_message_MakeSignatureRequestV1ToZap(label+"request", request),
	)

	response, err = s.srv.MakeSignatureV1(ctx, request)

	return
}

var _ KalkanClient = (*loggedKalkanClient)(nil)

func NewLoggedKalkanClient(client KalkanClient) KalkanClient {
	return &loggedKalkanClient{client: client}
}

type loggedKalkanClient struct {
	client KalkanClient
}

func (s *loggedKalkanClient) MakeSignatureV1(
	ctx context.Context,
	request *MakeSignatureRequestV1,
	opts ...grpc.CallOption,
) (
	response *MakeSignatureResponseV1,
	err error,
) {
	label := cntx.Begin(ctx, "KalkanClient_MakeSignatureV1")
	defer cntx.End(ctx, label)

	defer func() {
		if err != nil {
			ctxzap.AddFields(ctx, zap.Any(label+"error", err))
			return
		}

		ctxzap.AddFields(
			ctx,
			file_inner_processing_grpc_kalkan_proto_message_MakeSignatureResponseV1ToZap(label+"response", response),
		)
	}()

	ctxzap.AddFields(
		ctx,
		file_inner_processing_grpc_kalkan_proto_message_MakeSignatureRequestV1ToZap(label+"request", request),
	)

	response, err = s.client.MakeSignatureV1(ctx, request, opts...)

	return
}
