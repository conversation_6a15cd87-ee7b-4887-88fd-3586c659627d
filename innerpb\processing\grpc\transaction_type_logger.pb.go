// Code generated by protoc-gen-go-mvp. DO NOT EDIT.
// versions:
// - protoc-gen-go-mvp v2.0.1
// created by mvp authors

package grpc

import (
	context "context"
	fmt "fmt"
	cntx "git.local/sensitive/mvp/pkg/cntx"
	ctxzap "github.com/grpc-ecosystem/go-grpc-middleware/logging/zap/ctxzap"
	zap "go.uber.org/zap"
	grpc "google.golang.org/grpc"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
)

func file_inner_processing_grpc_transaction_type_proto_message_EmptyToZap(
	label string,
	in *emptypb.Empty,
) zap.Field {
	if in == nil {
		return zap.Reflect(label, in)
	}

	return zap.Dict(
		label,
	)
}

func file_inner_processing_grpc_transaction_type_proto_enum_EnumTransactionTypeToZap(
	label string,
	in EnumTransactionType,
) zap.Field {
	str, ok := EnumTransactionType_name[int32(in)]
	if !ok {
		str = fmt.Sprintf("unknown EnumTransactionType value %d", in)
	}

	return zap.Dict(
		label,
		zap.Any("value", EnumTransactionType(in)),
		zap.Any("name", str),
	)
}

func file_inner_processing_grpc_transaction_type_proto_message_GetAggregatedTransactionTypeByTypeIDRequestToZap(
	label string,
	in *GetAggregatedTransactionTypeByTypeIDRequest,
) zap.Field {
	if in == nil {
		return zap.Reflect(label, in)
	}

	return zap.Dict(
		label,
		zap.Any("TransactionTypeId", in.GetTransactionTypeId()),
	)
}

func file_inner_processing_grpc_transaction_type_proto_message_GetAggregatedTransactionTypeByTypeIDResponseToZap(
	label string,
	in *GetAggregatedTransactionTypeByTypeIDResponse,
) zap.Field {
	if in == nil {
		return zap.Reflect(label, in)
	}

	return zap.Dict(
		label,
		zap.Any("AggregatedTypeId", in.GetAggregatedTypeId()),
		zap.Any("TransactionTypeId", in.GetTransactionTypeId()),
	)
}

func file_inner_processing_grpc_transaction_type_proto_message_GetAggregatedTransactionTypeRequestV1ToZap(
	label string,
	in *GetAggregatedTransactionTypeRequestV1,
) zap.Field {
	if in == nil {
		return zap.Reflect(label, in)
	}

	return zap.Dict(
		label,
		zap.Any("TransactionTypeId", in.GetTransactionTypeId()),
		zap.Any("AggregatedTypeId", in.GetAggregatedTypeId()),
	)
}

func file_inner_processing_grpc_transaction_type_proto_message_GetAggregatedTransactionTypeResponseV1ToZap(
	label string,
	in *GetAggregatedTransactionTypeResponseV1,
) zap.Field {
	if in == nil {
		return zap.Reflect(label, in)
	}

	return zap.Dict(
		label,
		zap.Any("TransactionTypeId", in.GetTransactionTypeId()),
		zap.Any("AggregatedTypeId", in.GetAggregatedTypeId()),
		zap.Any("AggregatedTypeCode", in.GetAggregatedTypeCode()),
	)
}

func file_inner_processing_grpc_transaction_type_proto_message_GetAggregatedTypeByIDRequestV1ToZap(
	label string,
	in *GetAggregatedTypeByIDRequestV1,
) zap.Field {
	if in == nil {
		return zap.Reflect(label, in)
	}

	return zap.Dict(
		label,
		zap.Any("AggregatedTypeId", in.GetAggregatedTypeId()),
	)
}

func file_inner_processing_grpc_transaction_type_proto_message_GetAggregatedTypeByIDResponseV1ToZap(
	label string,
	in *GetAggregatedTypeByIDResponseV1,
) zap.Field {
	if in == nil {
		return zap.Reflect(label, in)
	}

	return zap.Dict(
		label,
		zap.Any("Id", in.GetId()),
		zap.Any("Name", in.GetName()),
		zap.Any("Code", in.GetCode()),
		zap.Any("Description", in.GetDescription()),
	)
}

func file_inner_processing_grpc_transaction_type_proto_message_TransactionTypeRefToZap(
	label string,
	in *TransactionTypeRef,
) zap.Field {
	if in == nil {
		return zap.Reflect(label, in)
	}

	return zap.Dict(
		label,
		zap.Any("Name", in.GetName()),
		zap.Any("Code", in.GetCode()),
	)
}

func file_inner_processing_grpc_transaction_type_proto_message_TransactionTypeResponseV1ToZap(
	label string,
	in *TransactionTypeResponseV1,
) zap.Field {
	if in == nil {
		return zap.Reflect(label, in)
	}

	return zap.Dict(
		label,
		file_inner_processing_grpc_transaction_type_proto_message_TransactionTypesResponseV1SliceToZap("Data", in.GetData()),
	)
}

func file_inner_processing_grpc_transaction_type_proto_message_TransactionTypesResponseV1ToZap(
	label string,
	in *TransactionTypesResponseV1,
) zap.Field {
	if in == nil {
		return zap.Reflect(label, in)
	}

	return zap.Dict(
		label,
		zap.Any("Id", in.GetId()),
		zap.Any("Name", in.GetName()),
		zap.Any("Code", in.GetCode()),
	)
}

func file_inner_processing_grpc_transaction_type_proto_message_TransactionTypesResponseV1SliceToZap(
	label string,
	in []*TransactionTypesResponseV1,
) zap.Field {
	var res []zap.Field
	for index, val := range in {
		res = append(res, file_inner_processing_grpc_transaction_type_proto_message_TransactionTypesResponseV1ToZap(fmt.Sprint(index), val))
	}

	return zap.Dict(label, res...)
}

var _ TransactionTypeServer = (*loggedTransactionTypeServer)(nil)

func NewLoggedTransactionTypeServer(srv TransactionTypeServer) TransactionTypeServer {
	return &loggedTransactionTypeServer{srv: srv}
}

type loggedTransactionTypeServer struct {
	UnimplementedTransactionTypeServer

	srv TransactionTypeServer
}

func (s *loggedTransactionTypeServer) GetAll(
	ctx context.Context,
	request *emptypb.Empty,
) (
	response *TransactionTypeResponseV1,
	err error,
) {
	label := cntx.Begin(ctx, "TransactionTypeServer_GetAll")
	defer cntx.End(ctx, label)

	defer func() {
		if err != nil {
			ctxzap.AddFields(ctx, zap.Any(label+"error", err))
			return
		}

		ctxzap.AddFields(
			ctx,
			file_inner_processing_grpc_transaction_type_proto_message_TransactionTypeResponseV1ToZap(label+"response", response),
		)
	}()

	ctxzap.AddFields(
		ctx,
		file_inner_processing_grpc_transaction_type_proto_message_EmptyToZap(label+"request", request),
	)

	response, err = s.srv.GetAll(ctx, request)

	return
}

func (s *loggedTransactionTypeServer) GetTransactionPayOutTypes(
	ctx context.Context,
	request *emptypb.Empty,
) (
	response *TransactionTypeResponseV1,
	err error,
) {
	label := cntx.Begin(ctx, "TransactionTypeServer_GetTransactionPayOutTypes")
	defer cntx.End(ctx, label)

	defer func() {
		if err != nil {
			ctxzap.AddFields(ctx, zap.Any(label+"error", err))
			return
		}

		ctxzap.AddFields(
			ctx,
			file_inner_processing_grpc_transaction_type_proto_message_TransactionTypeResponseV1ToZap(label+"response", response),
		)
	}()

	ctxzap.AddFields(
		ctx,
		file_inner_processing_grpc_transaction_type_proto_message_EmptyToZap(label+"request", request),
	)

	response, err = s.srv.GetTransactionPayOutTypes(ctx, request)

	return
}

func (s *loggedTransactionTypeServer) GetTransactionPayInTypes(
	ctx context.Context,
	request *emptypb.Empty,
) (
	response *TransactionTypeResponseV1,
	err error,
) {
	label := cntx.Begin(ctx, "TransactionTypeServer_GetTransactionPayInTypes")
	defer cntx.End(ctx, label)

	defer func() {
		if err != nil {
			ctxzap.AddFields(ctx, zap.Any(label+"error", err))
			return
		}

		ctxzap.AddFields(
			ctx,
			file_inner_processing_grpc_transaction_type_proto_message_TransactionTypeResponseV1ToZap(label+"response", response),
		)
	}()

	ctxzap.AddFields(
		ctx,
		file_inner_processing_grpc_transaction_type_proto_message_EmptyToZap(label+"request", request),
	)

	response, err = s.srv.GetTransactionPayInTypes(ctx, request)

	return
}

func (s *loggedTransactionTypeServer) GetAggregatedTransactionType(
	ctx context.Context,
	request *GetAggregatedTransactionTypeRequestV1,
) (
	response *GetAggregatedTransactionTypeResponseV1,
	err error,
) {
	label := cntx.Begin(ctx, "TransactionTypeServer_GetAggregatedTransactionType")
	defer cntx.End(ctx, label)

	defer func() {
		if err != nil {
			ctxzap.AddFields(ctx, zap.Any(label+"error", err))
			return
		}

		ctxzap.AddFields(
			ctx,
			file_inner_processing_grpc_transaction_type_proto_message_GetAggregatedTransactionTypeResponseV1ToZap(label+"response", response),
		)
	}()

	ctxzap.AddFields(
		ctx,
		file_inner_processing_grpc_transaction_type_proto_message_GetAggregatedTransactionTypeRequestV1ToZap(label+"request", request),
	)

	response, err = s.srv.GetAggregatedTransactionType(ctx, request)

	return
}

func (s *loggedTransactionTypeServer) GetAggregatedTypeByID(
	ctx context.Context,
	request *GetAggregatedTypeByIDRequestV1,
) (
	response *GetAggregatedTypeByIDResponseV1,
	err error,
) {
	label := cntx.Begin(ctx, "TransactionTypeServer_GetAggregatedTypeByID")
	defer cntx.End(ctx, label)

	defer func() {
		if err != nil {
			ctxzap.AddFields(ctx, zap.Any(label+"error", err))
			return
		}

		ctxzap.AddFields(
			ctx,
			file_inner_processing_grpc_transaction_type_proto_message_GetAggregatedTypeByIDResponseV1ToZap(label+"response", response),
		)
	}()

	ctxzap.AddFields(
		ctx,
		file_inner_processing_grpc_transaction_type_proto_message_GetAggregatedTypeByIDRequestV1ToZap(label+"request", request),
	)

	response, err = s.srv.GetAggregatedTypeByID(ctx, request)

	return
}

func (s *loggedTransactionTypeServer) GetAggregatedTransactionTypeByTypeID(
	ctx context.Context,
	request *GetAggregatedTransactionTypeByTypeIDRequest,
) (
	response *GetAggregatedTransactionTypeByTypeIDResponse,
	err error,
) {
	label := cntx.Begin(ctx, "TransactionTypeServer_GetAggregatedTransactionTypeByTypeID")
	defer cntx.End(ctx, label)

	defer func() {
		if err != nil {
			ctxzap.AddFields(ctx, zap.Any(label+"error", err))
			return
		}

		ctxzap.AddFields(
			ctx,
			file_inner_processing_grpc_transaction_type_proto_message_GetAggregatedTransactionTypeByTypeIDResponseToZap(label+"response", response),
		)
	}()

	ctxzap.AddFields(
		ctx,
		file_inner_processing_grpc_transaction_type_proto_message_GetAggregatedTransactionTypeByTypeIDRequestToZap(label+"request", request),
	)

	response, err = s.srv.GetAggregatedTransactionTypeByTypeID(ctx, request)

	return
}

var _ TransactionTypeClient = (*loggedTransactionTypeClient)(nil)

func NewLoggedTransactionTypeClient(client TransactionTypeClient) TransactionTypeClient {
	return &loggedTransactionTypeClient{client: client}
}

type loggedTransactionTypeClient struct {
	client TransactionTypeClient
}

func (s *loggedTransactionTypeClient) GetAll(
	ctx context.Context,
	request *emptypb.Empty,
	opts ...grpc.CallOption,
) (
	response *TransactionTypeResponseV1,
	err error,
) {
	label := cntx.Begin(ctx, "TransactionTypeClient_GetAll")
	defer cntx.End(ctx, label)

	defer func() {
		if err != nil {
			ctxzap.AddFields(ctx, zap.Any(label+"error", err))
			return
		}

		ctxzap.AddFields(
			ctx,
			file_inner_processing_grpc_transaction_type_proto_message_TransactionTypeResponseV1ToZap(label+"response", response),
		)
	}()

	ctxzap.AddFields(
		ctx,
		file_inner_processing_grpc_transaction_type_proto_message_EmptyToZap(label+"request", request),
	)

	response, err = s.client.GetAll(ctx, request, opts...)

	return
}

func (s *loggedTransactionTypeClient) GetTransactionPayOutTypes(
	ctx context.Context,
	request *emptypb.Empty,
	opts ...grpc.CallOption,
) (
	response *TransactionTypeResponseV1,
	err error,
) {
	label := cntx.Begin(ctx, "TransactionTypeClient_GetTransactionPayOutTypes")
	defer cntx.End(ctx, label)

	defer func() {
		if err != nil {
			ctxzap.AddFields(ctx, zap.Any(label+"error", err))
			return
		}

		ctxzap.AddFields(
			ctx,
			file_inner_processing_grpc_transaction_type_proto_message_TransactionTypeResponseV1ToZap(label+"response", response),
		)
	}()

	ctxzap.AddFields(
		ctx,
		file_inner_processing_grpc_transaction_type_proto_message_EmptyToZap(label+"request", request),
	)

	response, err = s.client.GetTransactionPayOutTypes(ctx, request, opts...)

	return
}

func (s *loggedTransactionTypeClient) GetTransactionPayInTypes(
	ctx context.Context,
	request *emptypb.Empty,
	opts ...grpc.CallOption,
) (
	response *TransactionTypeResponseV1,
	err error,
) {
	label := cntx.Begin(ctx, "TransactionTypeClient_GetTransactionPayInTypes")
	defer cntx.End(ctx, label)

	defer func() {
		if err != nil {
			ctxzap.AddFields(ctx, zap.Any(label+"error", err))
			return
		}

		ctxzap.AddFields(
			ctx,
			file_inner_processing_grpc_transaction_type_proto_message_TransactionTypeResponseV1ToZap(label+"response", response),
		)
	}()

	ctxzap.AddFields(
		ctx,
		file_inner_processing_grpc_transaction_type_proto_message_EmptyToZap(label+"request", request),
	)

	response, err = s.client.GetTransactionPayInTypes(ctx, request, opts...)

	return
}

func (s *loggedTransactionTypeClient) GetAggregatedTransactionType(
	ctx context.Context,
	request *GetAggregatedTransactionTypeRequestV1,
	opts ...grpc.CallOption,
) (
	response *GetAggregatedTransactionTypeResponseV1,
	err error,
) {
	label := cntx.Begin(ctx, "TransactionTypeClient_GetAggregatedTransactionType")
	defer cntx.End(ctx, label)

	defer func() {
		if err != nil {
			ctxzap.AddFields(ctx, zap.Any(label+"error", err))
			return
		}

		ctxzap.AddFields(
			ctx,
			file_inner_processing_grpc_transaction_type_proto_message_GetAggregatedTransactionTypeResponseV1ToZap(label+"response", response),
		)
	}()

	ctxzap.AddFields(
		ctx,
		file_inner_processing_grpc_transaction_type_proto_message_GetAggregatedTransactionTypeRequestV1ToZap(label+"request", request),
	)

	response, err = s.client.GetAggregatedTransactionType(ctx, request, opts...)

	return
}

func (s *loggedTransactionTypeClient) GetAggregatedTypeByID(
	ctx context.Context,
	request *GetAggregatedTypeByIDRequestV1,
	opts ...grpc.CallOption,
) (
	response *GetAggregatedTypeByIDResponseV1,
	err error,
) {
	label := cntx.Begin(ctx, "TransactionTypeClient_GetAggregatedTypeByID")
	defer cntx.End(ctx, label)

	defer func() {
		if err != nil {
			ctxzap.AddFields(ctx, zap.Any(label+"error", err))
			return
		}

		ctxzap.AddFields(
			ctx,
			file_inner_processing_grpc_transaction_type_proto_message_GetAggregatedTypeByIDResponseV1ToZap(label+"response", response),
		)
	}()

	ctxzap.AddFields(
		ctx,
		file_inner_processing_grpc_transaction_type_proto_message_GetAggregatedTypeByIDRequestV1ToZap(label+"request", request),
	)

	response, err = s.client.GetAggregatedTypeByID(ctx, request, opts...)

	return
}

func (s *loggedTransactionTypeClient) GetAggregatedTransactionTypeByTypeID(
	ctx context.Context,
	request *GetAggregatedTransactionTypeByTypeIDRequest,
	opts ...grpc.CallOption,
) (
	response *GetAggregatedTransactionTypeByTypeIDResponse,
	err error,
) {
	label := cntx.Begin(ctx, "TransactionTypeClient_GetAggregatedTransactionTypeByTypeID")
	defer cntx.End(ctx, label)

	defer func() {
		if err != nil {
			ctxzap.AddFields(ctx, zap.Any(label+"error", err))
			return
		}

		ctxzap.AddFields(
			ctx,
			file_inner_processing_grpc_transaction_type_proto_message_GetAggregatedTransactionTypeByTypeIDResponseToZap(label+"response", response),
		)
	}()

	ctxzap.AddFields(
		ctx,
		file_inner_processing_grpc_transaction_type_proto_message_GetAggregatedTransactionTypeByTypeIDRequestToZap(label+"request", request),
	)

	response, err = s.client.GetAggregatedTransactionTypeByTypeID(ctx, request, opts...)

	return
}
