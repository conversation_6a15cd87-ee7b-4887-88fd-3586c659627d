// Code generated by protoc-gen-go-mvp. DO NOT EDIT.
// versions:
// - protoc-gen-go-mvp v2.0.1
// created by mvp authors

package grpc

import (
	context "context"
	grpc "google.golang.org/grpc"
	metadata "google.golang.org/grpc/metadata"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
)

func NewIamReportMerchantWorkerServer(
	srv ReportMerchantWorkerServer,
) ReportMerchantWorkerServer {
	return &iamReportMerchantWorkerServer{
		srv: srv,
	}
}

var _ ReportMerchantWorkerServer = (*iamReportMerchantWorkerServer)(nil)

type iamReportMerchantWorkerServer struct {
	UnimplementedReportMerchantWorkerServer

	srv ReportMerchantWorkerServer
}

func (s *iamReportMerchantWorkerServer) ProcessReportScheduleByPeriodType(
	ctx context.Context,
	req *ProcessReportScheduleByPeriodTypeRequest,
) (
	*emptypb.Empty,
	error,
) {
	return s.srv.ProcessReportScheduleByPeriodType(ctx, req)
}

func NewIamReportMerchantWorkerClient(
	client ReportMerchantWorkerClient,
) ReportMerchantWorkerClient {
	return &iamReportMerchantWorkerClient{
		client: client,
	}
}

type iamReportMerchantWorkerClient struct {
	client ReportMerchantWorkerClient
}

func (s *iamReportMerchantWorkerClient) ProcessReportScheduleByPeriodType(
	ctx context.Context,
	req *ProcessReportScheduleByPeriodTypeRequest,
	opts ...grpc.CallOption,
) (
	*emptypb.Empty,
	error,
) {
	md, _ := metadata.FromIncomingContext(ctx)
	return s.client.ProcessReportScheduleByPeriodType(metadata.NewOutgoingContext(ctx, md), req)
}
