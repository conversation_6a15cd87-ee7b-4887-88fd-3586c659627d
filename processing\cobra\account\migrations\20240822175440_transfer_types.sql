-- +goose Up
-- +goose StatementBegin
CREATE TABLE "account"."transfer_types"
(
    "created_at" timestamp default now() not null,
    "updated_at" timestamp default now() not null,
    "id"         serial
        primary key,
    "code"       varchar(256)            not null
        unique,
    "name"       varchar(256)            not null
);

ALTER TABLE "account"."transfers"
    ADD COLUMN "transfer_type_id" INTEGER NOT NULL,
ADD CONSTRAINT transfers_type_id_fkey
    FOREIGN KEY (transfer_type_id)
    REFERENCES account.transfer_types(id);

ALTER TABLE "account"."transfers"
    ADD COLUMN IF NOT EXISTS "payment_purpose_code" VARCHAR (255);
-- +goose StatementEnd

-- +goose Down
-- +goose StatementBegin
-- ALTER TABLE "account"."transfers" DROP CONSTRAINT IF EXISTS transfers_type_id_fkey;
-- ALTER TABLE "account"."transfers" DROP COLUMN IF EXISTS "transfer_type_id";
-- ALTER TABLE "account"."accounts" DROP COLUMN IF EXISTS "payment_purpose_code";
-- DROP TABLE "account"."transfer_types";
-- +goose StatementEnd