package domain

type MakeOneClickPayRequest struct {
	TransactionID   uint64 `json:"transaction_id" validate:"required"`
	EncryptedID     string `json:"encrypted_id" validate:"required"`
	TransactionHash string `json:"transaction_hash" validate:"required"`
	UserEmail       string `json:"user_email,omitempty" validate:"omitempty,email"`
	UserPhone       string `json:"user_phone"`
}

// oneclick have same answer as payin
