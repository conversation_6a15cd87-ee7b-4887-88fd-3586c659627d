package test

import (
	json "encoding/json"
	"io"
	"net/http"
	"net/url"
	"strconv"
	"testing"

	"github.com/stretchr/testify/assert"

	"git.local/sensitive/local/internal/domain"
	"git.local/sensitive/local/internal/smoketesting/core/getcard"
)

func TestGetCard(t *testing.T) {
	t.Skip()

	expectedData := getcard.TestExpectedData()

	reqData := getcard.TestReqData()

	for i, data := range reqData {
		t.Run("test_get_card", func(t *testing.T) {
			getCard(t, &data, expectedData[i])
		})
	}
}

// in fact, we can use generate payin transaction in order to get cards
func getCard(t *testing.T, req *domain.GetClientCardsRequest, expected domain.SmokeData[[]domain.GetCardResponse]) string {

	query := url.Values{}
	query.Set("merchant_id", strconv.FormatUint(req.MerchantID, 10))
	query.Set("project_id", strconv.FormatUint(req.ProjectID, 10))
	query.Set("project_client_id", req.ProjectClientID)

	request, err := http.NewRequest(http.MethodGet, domainURL+getcard.UrlPath+"?"+query.Encode(), nil)
	if err != nil {
		t.Fatal(err)
	}

	request.Header.Add("Content-Type", "application/json")
	request.Header.Add("Authorization", "Bearer "+generateToken(req, ""))

	resp, err := http.DefaultClient.Do(request)
	if err != nil {
		t.Fatal(err)
	}
	defer resp.Body.Close()

	respBody, err := io.ReadAll(resp.Body)
	if err != nil {
		t.Fatal(err)
	}

	var respData domain.Response[[]domain.GetCardResponse]
	if err = json.Unmarshal(respBody, &respData); err != nil {
		t.Fatal(err)
	}

	if len(respData.Result) == 0 {
		t.Fatal("should return at least one card")
	}

	assert.Equal(t, expected.Expected.HttpStatusCode, resp.StatusCode, "http status code")
	assert.Equal(t, expected.Expected.Data[0].CardToken, respData.Result[0].CardToken, "token")
	assert.Equal(t, expected.Expected.Data[0].Month, respData.Result[0].Month, "month")
	assert.Equal(t, expected.Expected.Data[0].Year, respData.Result[0].Year, "year")

	return respData.Result[0].CardToken
}
