// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.5.1
// - protoc             v5.29.3
// source: inner/processing/grpc/transaction_type.proto

package grpc

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.64.0 or later.
const _ = grpc.SupportPackageIsVersion9

const (
	TransactionType_GetAll_FullMethodName                               = "/processing.transaction.transaction_type.TransactionType/GetAll"
	TransactionType_GetTransactionPayOutTypes_FullMethodName            = "/processing.transaction.transaction_type.TransactionType/GetTransactionPayOutTypes"
	TransactionType_GetTransactionPayInTypes_FullMethodName             = "/processing.transaction.transaction_type.TransactionType/GetTransactionPayInTypes"
	TransactionType_GetAggregatedTransactionType_FullMethodName         = "/processing.transaction.transaction_type.TransactionType/GetAggregatedTransactionType"
	TransactionType_GetAggregatedTypeByID_FullMethodName                = "/processing.transaction.transaction_type.TransactionType/GetAggregatedTypeByID"
	TransactionType_GetAggregatedTransactionTypeByTypeID_FullMethodName = "/processing.transaction.transaction_type.TransactionType/GetAggregatedTransactionTypeByTypeID"
)

// TransactionTypeClient is the client API for TransactionType service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type TransactionTypeClient interface {
	GetAll(ctx context.Context, in *emptypb.Empty, opts ...grpc.CallOption) (*TransactionTypeResponseV1, error)
	GetTransactionPayOutTypes(ctx context.Context, in *emptypb.Empty, opts ...grpc.CallOption) (*TransactionTypeResponseV1, error)
	GetTransactionPayInTypes(ctx context.Context, in *emptypb.Empty, opts ...grpc.CallOption) (*TransactionTypeResponseV1, error)
	GetAggregatedTransactionType(ctx context.Context, in *GetAggregatedTransactionTypeRequestV1, opts ...grpc.CallOption) (*GetAggregatedTransactionTypeResponseV1, error)
	GetAggregatedTypeByID(ctx context.Context, in *GetAggregatedTypeByIDRequestV1, opts ...grpc.CallOption) (*GetAggregatedTypeByIDResponseV1, error)
	GetAggregatedTransactionTypeByTypeID(ctx context.Context, in *GetAggregatedTransactionTypeByTypeIDRequest, opts ...grpc.CallOption) (*GetAggregatedTransactionTypeByTypeIDResponse, error)
}

type transactionTypeClient struct {
	cc grpc.ClientConnInterface
}

func NewTransactionTypeClient(cc grpc.ClientConnInterface) TransactionTypeClient {
	return &transactionTypeClient{cc}
}

func (c *transactionTypeClient) GetAll(ctx context.Context, in *emptypb.Empty, opts ...grpc.CallOption) (*TransactionTypeResponseV1, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(TransactionTypeResponseV1)
	err := c.cc.Invoke(ctx, TransactionType_GetAll_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *transactionTypeClient) GetTransactionPayOutTypes(ctx context.Context, in *emptypb.Empty, opts ...grpc.CallOption) (*TransactionTypeResponseV1, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(TransactionTypeResponseV1)
	err := c.cc.Invoke(ctx, TransactionType_GetTransactionPayOutTypes_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *transactionTypeClient) GetTransactionPayInTypes(ctx context.Context, in *emptypb.Empty, opts ...grpc.CallOption) (*TransactionTypeResponseV1, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(TransactionTypeResponseV1)
	err := c.cc.Invoke(ctx, TransactionType_GetTransactionPayInTypes_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *transactionTypeClient) GetAggregatedTransactionType(ctx context.Context, in *GetAggregatedTransactionTypeRequestV1, opts ...grpc.CallOption) (*GetAggregatedTransactionTypeResponseV1, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetAggregatedTransactionTypeResponseV1)
	err := c.cc.Invoke(ctx, TransactionType_GetAggregatedTransactionType_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *transactionTypeClient) GetAggregatedTypeByID(ctx context.Context, in *GetAggregatedTypeByIDRequestV1, opts ...grpc.CallOption) (*GetAggregatedTypeByIDResponseV1, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetAggregatedTypeByIDResponseV1)
	err := c.cc.Invoke(ctx, TransactionType_GetAggregatedTypeByID_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *transactionTypeClient) GetAggregatedTransactionTypeByTypeID(ctx context.Context, in *GetAggregatedTransactionTypeByTypeIDRequest, opts ...grpc.CallOption) (*GetAggregatedTransactionTypeByTypeIDResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetAggregatedTransactionTypeByTypeIDResponse)
	err := c.cc.Invoke(ctx, TransactionType_GetAggregatedTransactionTypeByTypeID_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// TransactionTypeServer is the server API for TransactionType service.
// All implementations must embed UnimplementedTransactionTypeServer
// for forward compatibility.
type TransactionTypeServer interface {
	GetAll(context.Context, *emptypb.Empty) (*TransactionTypeResponseV1, error)
	GetTransactionPayOutTypes(context.Context, *emptypb.Empty) (*TransactionTypeResponseV1, error)
	GetTransactionPayInTypes(context.Context, *emptypb.Empty) (*TransactionTypeResponseV1, error)
	GetAggregatedTransactionType(context.Context, *GetAggregatedTransactionTypeRequestV1) (*GetAggregatedTransactionTypeResponseV1, error)
	GetAggregatedTypeByID(context.Context, *GetAggregatedTypeByIDRequestV1) (*GetAggregatedTypeByIDResponseV1, error)
	GetAggregatedTransactionTypeByTypeID(context.Context, *GetAggregatedTransactionTypeByTypeIDRequest) (*GetAggregatedTransactionTypeByTypeIDResponse, error)
	mustEmbedUnimplementedTransactionTypeServer()
}

// UnimplementedTransactionTypeServer must be embedded to have
// forward compatible implementations.
//
// NOTE: this should be embedded by value instead of pointer to avoid a nil
// pointer dereference when methods are called.
type UnimplementedTransactionTypeServer struct{}

func (UnimplementedTransactionTypeServer) GetAll(context.Context, *emptypb.Empty) (*TransactionTypeResponseV1, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetAll not implemented")
}
func (UnimplementedTransactionTypeServer) GetTransactionPayOutTypes(context.Context, *emptypb.Empty) (*TransactionTypeResponseV1, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetTransactionPayOutTypes not implemented")
}
func (UnimplementedTransactionTypeServer) GetTransactionPayInTypes(context.Context, *emptypb.Empty) (*TransactionTypeResponseV1, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetTransactionPayInTypes not implemented")
}
func (UnimplementedTransactionTypeServer) GetAggregatedTransactionType(context.Context, *GetAggregatedTransactionTypeRequestV1) (*GetAggregatedTransactionTypeResponseV1, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetAggregatedTransactionType not implemented")
}
func (UnimplementedTransactionTypeServer) GetAggregatedTypeByID(context.Context, *GetAggregatedTypeByIDRequestV1) (*GetAggregatedTypeByIDResponseV1, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetAggregatedTypeByID not implemented")
}
func (UnimplementedTransactionTypeServer) GetAggregatedTransactionTypeByTypeID(context.Context, *GetAggregatedTransactionTypeByTypeIDRequest) (*GetAggregatedTransactionTypeByTypeIDResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetAggregatedTransactionTypeByTypeID not implemented")
}
func (UnimplementedTransactionTypeServer) mustEmbedUnimplementedTransactionTypeServer() {}
func (UnimplementedTransactionTypeServer) testEmbeddedByValue()                         {}

// UnsafeTransactionTypeServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to TransactionTypeServer will
// result in compilation errors.
type UnsafeTransactionTypeServer interface {
	mustEmbedUnimplementedTransactionTypeServer()
}

func RegisterTransactionTypeServer(s grpc.ServiceRegistrar, srv TransactionTypeServer) {
	// If the following call pancis, it indicates UnimplementedTransactionTypeServer was
	// embedded by pointer and is nil.  This will cause panics if an
	// unimplemented method is ever invoked, so we test this at initialization
	// time to prevent it from happening at runtime later due to I/O.
	if t, ok := srv.(interface{ testEmbeddedByValue() }); ok {
		t.testEmbeddedByValue()
	}
	s.RegisterService(&TransactionType_ServiceDesc, srv)
}

func _TransactionType_GetAll_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(emptypb.Empty)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TransactionTypeServer).GetAll(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: TransactionType_GetAll_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TransactionTypeServer).GetAll(ctx, req.(*emptypb.Empty))
	}
	return interceptor(ctx, in, info, handler)
}

func _TransactionType_GetTransactionPayOutTypes_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(emptypb.Empty)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TransactionTypeServer).GetTransactionPayOutTypes(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: TransactionType_GetTransactionPayOutTypes_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TransactionTypeServer).GetTransactionPayOutTypes(ctx, req.(*emptypb.Empty))
	}
	return interceptor(ctx, in, info, handler)
}

func _TransactionType_GetTransactionPayInTypes_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(emptypb.Empty)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TransactionTypeServer).GetTransactionPayInTypes(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: TransactionType_GetTransactionPayInTypes_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TransactionTypeServer).GetTransactionPayInTypes(ctx, req.(*emptypb.Empty))
	}
	return interceptor(ctx, in, info, handler)
}

func _TransactionType_GetAggregatedTransactionType_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetAggregatedTransactionTypeRequestV1)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TransactionTypeServer).GetAggregatedTransactionType(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: TransactionType_GetAggregatedTransactionType_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TransactionTypeServer).GetAggregatedTransactionType(ctx, req.(*GetAggregatedTransactionTypeRequestV1))
	}
	return interceptor(ctx, in, info, handler)
}

func _TransactionType_GetAggregatedTypeByID_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetAggregatedTypeByIDRequestV1)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TransactionTypeServer).GetAggregatedTypeByID(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: TransactionType_GetAggregatedTypeByID_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TransactionTypeServer).GetAggregatedTypeByID(ctx, req.(*GetAggregatedTypeByIDRequestV1))
	}
	return interceptor(ctx, in, info, handler)
}

func _TransactionType_GetAggregatedTransactionTypeByTypeID_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetAggregatedTransactionTypeByTypeIDRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TransactionTypeServer).GetAggregatedTransactionTypeByTypeID(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: TransactionType_GetAggregatedTransactionTypeByTypeID_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TransactionTypeServer).GetAggregatedTransactionTypeByTypeID(ctx, req.(*GetAggregatedTransactionTypeByTypeIDRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// TransactionType_ServiceDesc is the grpc.ServiceDesc for TransactionType service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var TransactionType_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "processing.transaction.transaction_type.TransactionType",
	HandlerType: (*TransactionTypeServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetAll",
			Handler:    _TransactionType_GetAll_Handler,
		},
		{
			MethodName: "GetTransactionPayOutTypes",
			Handler:    _TransactionType_GetTransactionPayOutTypes_Handler,
		},
		{
			MethodName: "GetTransactionPayInTypes",
			Handler:    _TransactionType_GetTransactionPayInTypes_Handler,
		},
		{
			MethodName: "GetAggregatedTransactionType",
			Handler:    _TransactionType_GetAggregatedTransactionType_Handler,
		},
		{
			MethodName: "GetAggregatedTypeByID",
			Handler:    _TransactionType_GetAggregatedTypeByID_Handler,
		},
		{
			MethodName: "GetAggregatedTransactionTypeByTypeID",
			Handler:    _TransactionType_GetAggregatedTransactionTypeByTypeID_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "inner/processing/grpc/transaction_type.proto",
}
