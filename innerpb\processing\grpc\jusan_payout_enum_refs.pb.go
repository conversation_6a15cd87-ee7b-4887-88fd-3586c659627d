// Code generated by protoc-gen-go-mvp. DO NOT EDIT.
// versions:
// - protoc-gen-go-mvp v2.0.1
// created by mvp authors

package grpc

func (x JusanResponseCodePayout) Code() string {
	switch x {
	case JusanResponseCodePayout_PayoutUnhandledError:
		return "-1"
	case JusanResponseCodePayout_PayoutValidationError:
		return "-2"
	case JusanResponseCodePayout_PayoutIncorrectLoginPassword:
		return "-3"
	case JusanResponseCodePayout_PayoutBlocked:
		return "-4"
	case JusanResponseCodePayout_PayoutRetry:
		return "-5"
	case JusanResponseCodePayout_PayoutEmptyPartnerCorrelationId:
		return "-100"
	case JusanResponseCodePayout_PayoutEmptyPartnerLogin:
		return "-101"
	case JusanResponseCodePayout_PayoutEmptyAuthorization:
		return "-102"
	case JusanResponseCodePayout_PayoutEmptyData:
		return "-103"
	case JusanResponseCodePayout_PayoutEmptyAuth:
		return "-104"
	case JusanResponseCodePayout_PayoutIncorrectAuth:
		return "-105"
	case JusanResponseCodePayout_PayoutIncorrectCurrency:
		return "-106"
	case JusanResponseCodePayout_PayoutEDNotFound:
		return "-107"
	case JusanResponseCodePayout_PayoutEDNotEnabled:
		return "-108"
	case JusanResponseCodePayout_PayoutInsufficientFunds:
		return "-10000"
	case JusanResponseCodePayout_PayoutIncorrectCard:
		return "-109"
	case JusanResponseCodePayout_PayoutAccountBlocked:
		return "-110"
	case JusanResponseCodePayout_PayoutIncorrectSession:
		return "-111"
	case JusanResponseCodePayout_PayoutPaymentNotFound:
		return "-112"
	case JusanResponseCodePayout_PayoutIncorrectAmount:
		return "-113"
	case JusanResponseCodePayout_PayoutIssuerDeclined:
		return "W05"
	case JusanResponseCodePayout_PayoutCardDisabled:
		return "W07"
	case JusanResponseCodePayout_PayoutTransactionNotActive:
		return "W12"
	case JusanResponseCodePayout_PayoutCardNumIncorrect:
		return "W14"
	case JusanResponseCodePayout_PayoutClientDisabled:
		return "W17"
	case JusanResponseCodePayout_PayoutSuspiciousClient:
		return "W18"
	case JusanResponseCodePayout_PayoutRetryTransaction:
		return "W19"
	case JusanResponseCodePayout_PayoutCardExpired:
		return "W33"
	case JusanResponseCodePayout_PayoutCardLimited:
		return "W36"
	case JusanResponseCodePayout_PayoutStolenCard:
		return "W43"
	case JusanResponseCodePayout_PayoutExpCard:
		return "W54"
	case JusanResponseCodePayout_PayoutForbiddenForClient:
		return "W57"
	case JusanResponseCodePayout_PayoutForbiddenTerminal:
		return "W58"
	case JusanResponseCodePayout_PayoutClientSuspicious:
		return "W59"
	case JusanResponseCodePayout_PayoutLimitExceeded:
		return "W61"
	case JusanResponseCodePayout_PayoutLimitedCard:
		return "W62"
	case JusanResponseCodePayout_PayoutSecurityBreached:
		return "W63"
	case JusanResponseCodePayout_PayoutUnavailableIssuer:
		return "W91"
	case JusanResponseCodePayout_PayoutTransactionNotFinished:
		return "W93"
	case JusanResponseCodePayout_PayoutSystemMalfuction:
		return "W96"
	default:
		return "0"
	}
}

func (x JusanResponseCodePayout) IntegrationError() IntegrationError {
	switch x {
	case JusanResponseCodePayout_PayoutUnhandledError:
		return IntegrationError_TransactionDeclinedByAcquirer
	case JusanResponseCodePayout_PayoutValidationError:
		return IntegrationError_TransactionDeclinedByAcquirer
	case JusanResponseCodePayout_PayoutIncorrectLoginPassword:
		return IntegrationError_TransactionDeclinedByAcquirer
	case JusanResponseCodePayout_PayoutBlocked:
		return IntegrationError_TransactionDeclinedByAcquirer
	case JusanResponseCodePayout_PayoutRetry:
		return IntegrationError_TransactionDeclinedByAcquirer
	case JusanResponseCodePayout_PayoutEmptyPartnerCorrelationId:
		return IntegrationError_TransactionDeclinedByAcquirer
	case JusanResponseCodePayout_PayoutEmptyPartnerLogin:
		return IntegrationError_TransactionDeclinedByAcquirer
	case JusanResponseCodePayout_PayoutEmptyAuthorization:
		return IntegrationError_TransactionDeclinedByAcquirer
	case JusanResponseCodePayout_PayoutEmptyData:
		return IntegrationError_TransactionDeclinedByAcquirer
	case JusanResponseCodePayout_PayoutEmptyAuth:
		return IntegrationError_TransactionDeclinedByAcquirer
	case JusanResponseCodePayout_PayoutIncorrectAuth:
		return IntegrationError_TransactionDeclinedByAcquirer
	case JusanResponseCodePayout_PayoutIncorrectCurrency:
		return IntegrationError_TransactionDeclinedByAcquirer
	case JusanResponseCodePayout_PayoutEDNotFound:
		return IntegrationError_TransactionDeclinedByAcquirer
	case JusanResponseCodePayout_PayoutEDNotEnabled:
		return IntegrationError_TransactionDeclinedByAcquirer
	case JusanResponseCodePayout_PayoutInsufficientFunds:
		return IntegrationError_InsufficientFunds
	case JusanResponseCodePayout_PayoutIncorrectCard:
		return IntegrationError_IncorrectCardNumber
	case JusanResponseCodePayout_PayoutAccountBlocked:
		return IntegrationError_TransactionDeclinedByAcquirer
	case JusanResponseCodePayout_PayoutIncorrectSession:
		return IntegrationError_TransactionDeclinedByAcquirer
	case JusanResponseCodePayout_PayoutPaymentNotFound:
		return IntegrationError_TransactionDeclinedByAcquirer
	case JusanResponseCodePayout_PayoutIncorrectAmount:
		return IntegrationError_TransactionDeclinedByAcquirer
	case JusanResponseCodePayout_PayoutIssuerDeclined:
		return IntegrationError_TransactionDeclinedByIssuer
	case JusanResponseCodePayout_PayoutCardDisabled:
		return IntegrationError_BlockedCard
	case JusanResponseCodePayout_PayoutTransactionNotActive:
		return IntegrationError_TransactionDeclinedByAcquirer
	case JusanResponseCodePayout_PayoutCardNumIncorrect:
		return IntegrationError_IncorrectCardNumber
	case JusanResponseCodePayout_PayoutClientDisabled:
		return IntegrationError_SuspiciousClient
	case JusanResponseCodePayout_PayoutSuspiciousClient:
		return IntegrationError_SuspiciousClient
	case JusanResponseCodePayout_PayoutRetryTransaction:
		return IntegrationError_TransactionDeclinedByAcquirer
	case JusanResponseCodePayout_PayoutCardExpired:
		return IntegrationError_CardHasExpired
	case JusanResponseCodePayout_PayoutCardLimited:
		return IntegrationError_InvalidCard
	case JusanResponseCodePayout_PayoutStolenCard:
		return IntegrationError_StolenCard
	case JusanResponseCodePayout_PayoutExpCard:
		return IntegrationError_CardHasExpired
	case JusanResponseCodePayout_PayoutForbiddenForClient:
		return IntegrationError_SuspiciousClient
	case JusanResponseCodePayout_PayoutForbiddenTerminal:
		return IntegrationError_PaymentForbiddenForMerchant
	case JusanResponseCodePayout_PayoutClientSuspicious:
		return IntegrationError_SuspiciousClient
	case JusanResponseCodePayout_PayoutLimitExceeded:
		return IntegrationError_TransactionDeclinedByAcquirer
	case JusanResponseCodePayout_PayoutLimitedCard:
		return IntegrationError_InvalidCard
	case JusanResponseCodePayout_PayoutSecurityBreached:
		return IntegrationError_TransactionDeclinedByAcquirer
	case JusanResponseCodePayout_PayoutUnavailableIssuer:
		return IntegrationError_UnavailableIssuer
	case JusanResponseCodePayout_PayoutTransactionNotFinished:
		return IntegrationError_TransactionDeclinedByAcquirer
	case JusanResponseCodePayout_PayoutSystemMalfuction:
		return IntegrationError_UnavailableAcquirer
	default:
		return IntegrationError_None
	}
}

func (x JusanResponseCodePayout) Message() string {
	switch x {
	case JusanResponseCodePayout_PayoutUnhandledError:
		return "Необработанная ошибка"
	case JusanResponseCodePayout_PayoutValidationError:
		return "Ошибка валидации"
	case JusanResponseCodePayout_PayoutIncorrectLoginPassword:
		return "Некорректный логин или пароль"
	case JusanResponseCodePayout_PayoutBlocked:
		return "Учетная запись заблокирована"
	case JusanResponseCodePayout_PayoutRetry:
		return "Повторите запрос"
	case JusanResponseCodePayout_PayoutEmptyPartnerCorrelationId:
		return "Пустой заголовок PartnerCorrelationId"
	case JusanResponseCodePayout_PayoutEmptyPartnerLogin:
		return "Пустой заголовок PartnerLogin"
	case JusanResponseCodePayout_PayoutEmptyAuthorization:
		return "Некорректное значение заголовка Autorization"
	case JusanResponseCodePayout_PayoutEmptyData:
		return "Пустые зашифрованные данные"
	case JusanResponseCodePayout_PayoutEmptyAuth:
		return "Пустой заголовок Authorization"
	case JusanResponseCodePayout_PayoutIncorrectAuth:
		return "Некорректный заголовок Authorization"
	case JusanResponseCodePayout_PayoutIncorrectCurrency:
		return "Некорректно указана валюта"
	case JusanResponseCodePayout_PayoutEDNotFound:
		return "Транзакция ED не найдена"
	case JusanResponseCodePayout_PayoutEDNotEnabled:
		return "Возможность зачисления с ЭД отключена"
	case JusanResponseCodePayout_PayoutInsufficientFunds:
		return "Сумма остатка на счете не позволяет провести данный платеж!"
	case JusanResponseCodePayout_PayoutIncorrectCard:
		return "Неверно указан номер карты"
	case JusanResponseCodePayout_PayoutAccountBlocked:
		return "Учетная запись партнера заблокирована"
	case JusanResponseCodePayout_PayoutIncorrectSession:
		return "Неверно указана сессия"
	case JusanResponseCodePayout_PayoutPaymentNotFound:
		return "Информация о платеже не найдена"
	case JusanResponseCodePayout_PayoutIncorrectAmount:
		return "Суммы операции и гашения отличаются"
	case JusanResponseCodePayout_PayoutIssuerDeclined:
		return "Отказ Эмитента, причина не определена"
	case JusanResponseCodePayout_PayoutCardDisabled:
		return "Карта отключена"
	case JusanResponseCodePayout_PayoutTransactionNotActive:
		return "Недействительная транзакция"
	case JusanResponseCodePayout_PayoutCardNumIncorrect:
		return "Недействительный номер карточки"
	case JusanResponseCodePayout_PayoutClientDisabled:
		return "Клиент исключен"
	case JusanResponseCodePayout_PayoutSuspiciousClient:
		return "Подозрительный клиент"
	case JusanResponseCodePayout_PayoutRetryTransaction:
		return "Повторная транзакция"
	case JusanResponseCodePayout_PayoutCardExpired:
		return "Карточка просрочена"
	case JusanResponseCodePayout_PayoutCardLimited:
		return "Карточка ограничена в использовании"
	case JusanResponseCodePayout_PayoutStolenCard:
		return "Украденная карточка"
	case JusanResponseCodePayout_PayoutExpCard:
		return "Карточка просрочена"
	case JusanResponseCodePayout_PayoutForbiddenForClient:
		return "Транзакция запрещена для клиента"
	case JusanResponseCodePayout_PayoutForbiddenTerminal:
		return "Транзакция запрещена для терминала"
	case JusanResponseCodePayout_PayoutClientSuspicious:
		return "Подозрение в мошенничестве"
	case JusanResponseCodePayout_PayoutLimitExceeded:
		return "Превышает лимит суммы карты получателя"
	case JusanResponseCodePayout_PayoutLimitedCard:
		return "Карточка с ограниченным использованием"
	case JusanResponseCodePayout_PayoutSecurityBreached:
		return "Нарушена безопасность"
	case JusanResponseCodePayout_PayoutUnavailableIssuer:
		return "Недействующий эмитент"
	case JusanResponseCodePayout_PayoutTransactionNotFinished:
		return "Транзакция не завершена. Нарушение закона"
	case JusanResponseCodePayout_PayoutSystemMalfuction:
		return "Неисправность в системе"
	default:
		return "default"
	}
}

func (x JusanResponseCodePayout) TransactionStatus() EnumTransactionStatus {
	switch x {
	case JusanResponseCodePayout_PayoutUnhandledError:
		return EnumTransactionStatus_TransactionStatusHolded
	case JusanResponseCodePayout_PayoutValidationError:
		return EnumTransactionStatus_TransactionStatusFailed
	case JusanResponseCodePayout_PayoutIncorrectLoginPassword:
		return EnumTransactionStatus_TransactionStatusFailed
	case JusanResponseCodePayout_PayoutBlocked:
		return EnumTransactionStatus_TransactionStatusFailed
	case JusanResponseCodePayout_PayoutRetry:
		return EnumTransactionStatus_TransactionStatusRetry
	case JusanResponseCodePayout_PayoutEmptyPartnerCorrelationId:
		return EnumTransactionStatus_TransactionStatusFailed
	case JusanResponseCodePayout_PayoutEmptyPartnerLogin:
		return EnumTransactionStatus_TransactionStatusFailed
	case JusanResponseCodePayout_PayoutEmptyAuthorization:
		return EnumTransactionStatus_TransactionStatusFailed
	case JusanResponseCodePayout_PayoutEmptyData:
		return EnumTransactionStatus_TransactionStatusFailed
	case JusanResponseCodePayout_PayoutEmptyAuth:
		return EnumTransactionStatus_TransactionStatusFailed
	case JusanResponseCodePayout_PayoutIncorrectAuth:
		return EnumTransactionStatus_TransactionStatusFailed
	case JusanResponseCodePayout_PayoutIncorrectCurrency:
		return EnumTransactionStatus_TransactionStatusFailed
	case JusanResponseCodePayout_PayoutEDNotFound:
		return EnumTransactionStatus_TransactionStatusFailed
	case JusanResponseCodePayout_PayoutEDNotEnabled:
		return EnumTransactionStatus_TransactionStatusFailed
	case JusanResponseCodePayout_PayoutInsufficientFunds:
		return EnumTransactionStatus_TransactionStatusFailed
	case JusanResponseCodePayout_PayoutIncorrectCard:
		return EnumTransactionStatus_TransactionStatusFailed
	case JusanResponseCodePayout_PayoutAccountBlocked:
		return EnumTransactionStatus_TransactionStatusFailed
	case JusanResponseCodePayout_PayoutIncorrectSession:
		return EnumTransactionStatus_TransactionStatusFailed
	case JusanResponseCodePayout_PayoutPaymentNotFound:
		return EnumTransactionStatus_TransactionStatusFailed
	case JusanResponseCodePayout_PayoutIncorrectAmount:
		return EnumTransactionStatus_TransactionStatusFailed
	case JusanResponseCodePayout_PayoutIssuerDeclined:
		return EnumTransactionStatus_TransactionStatusFailed
	case JusanResponseCodePayout_PayoutCardDisabled:
		return EnumTransactionStatus_TransactionStatusFailed
	case JusanResponseCodePayout_PayoutTransactionNotActive:
		return EnumTransactionStatus_TransactionStatusFailed
	case JusanResponseCodePayout_PayoutCardNumIncorrect:
		return EnumTransactionStatus_TransactionStatusFailed
	case JusanResponseCodePayout_PayoutClientDisabled:
		return EnumTransactionStatus_TransactionStatusFailed
	case JusanResponseCodePayout_PayoutSuspiciousClient:
		return EnumTransactionStatus_TransactionStatusFailed
	case JusanResponseCodePayout_PayoutRetryTransaction:
		return EnumTransactionStatus_TransactionStatusFailed
	case JusanResponseCodePayout_PayoutCardExpired:
		return EnumTransactionStatus_TransactionStatusFailed
	case JusanResponseCodePayout_PayoutCardLimited:
		return EnumTransactionStatus_TransactionStatusFailed
	case JusanResponseCodePayout_PayoutStolenCard:
		return EnumTransactionStatus_TransactionStatusFailed
	case JusanResponseCodePayout_PayoutExpCard:
		return EnumTransactionStatus_TransactionStatusFailed
	case JusanResponseCodePayout_PayoutForbiddenForClient:
		return EnumTransactionStatus_TransactionStatusFailed
	case JusanResponseCodePayout_PayoutForbiddenTerminal:
		return EnumTransactionStatus_TransactionStatusFailed
	case JusanResponseCodePayout_PayoutClientSuspicious:
		return EnumTransactionStatus_TransactionStatusFailed
	case JusanResponseCodePayout_PayoutLimitExceeded:
		return EnumTransactionStatus_TransactionStatusFailed
	case JusanResponseCodePayout_PayoutLimitedCard:
		return EnumTransactionStatus_TransactionStatusFailed
	case JusanResponseCodePayout_PayoutSecurityBreached:
		return EnumTransactionStatus_TransactionStatusFailed
	case JusanResponseCodePayout_PayoutUnavailableIssuer:
		return EnumTransactionStatus_TransactionStatusFailed
	case JusanResponseCodePayout_PayoutTransactionNotFinished:
		return EnumTransactionStatus_TransactionStatusFailed
	case JusanResponseCodePayout_PayoutSystemMalfuction:
		return EnumTransactionStatus_TransactionStatusHolded
	default:
		return EnumTransactionStatus_TransactionStatusError
	}
}

// Created reference to JusanResponseCodePayout

//	|	JusanResponseCodePayout                                	|	Code    	|	IntegrationError                              	|	Message                                                                                                        	|	TransactionStatus                            	|
//	|	JusanResponseCodePayout_PayoutUnhandledError           	|	"-1"    	|	IntegrationError_TransactionDeclinedByAcquirer	|	"Необработанная ошибка"                                                                                        	|	EnumTransactionStatus_TransactionStatusHolded	|
//	|	JusanResponseCodePayout_PayoutValidationError          	|	"-2"    	|	IntegrationError_TransactionDeclinedByAcquirer	|	"Ошибка валидации"                                                                                             	|	EnumTransactionStatus_TransactionStatusFailed	|
//	|	JusanResponseCodePayout_PayoutIncorrectLoginPassword   	|	"-3"    	|	IntegrationError_TransactionDeclinedByAcquirer	|	"Некорректный логин или пароль"                                                                                	|	EnumTransactionStatus_TransactionStatusFailed	|
//	|	JusanResponseCodePayout_PayoutBlocked                  	|	"-4"    	|	IntegrationError_TransactionDeclinedByAcquirer	|	"Учетная запись заблокирована"                                                                                 	|	EnumTransactionStatus_TransactionStatusFailed	|
//	|	JusanResponseCodePayout_PayoutRetry                    	|	"-5"    	|	IntegrationError_TransactionDeclinedByAcquirer	|	"Повторите запрос"                                                                                             	|	EnumTransactionStatus_TransactionStatusRetry 	|
//	|	JusanResponseCodePayout_PayoutEmptyPartnerCorrelationId	|	"-100"  	|	IntegrationError_TransactionDeclinedByAcquirer	|	"Пустой заголовок PartnerCorrelationId"                                                                        	|	EnumTransactionStatus_TransactionStatusFailed	|
//	|	JusanResponseCodePayout_PayoutEmptyPartnerLogin        	|	"-101"  	|	IntegrationError_TransactionDeclinedByAcquirer	|	"Пустой заголовок PartnerLogin"                                                                                	|	EnumTransactionStatus_TransactionStatusFailed	|
//	|	JusanResponseCodePayout_PayoutEmptyAuthorization       	|	"-102"  	|	IntegrationError_TransactionDeclinedByAcquirer	|	"Некорректное значение заголовка Autorization"                                                                 	|	EnumTransactionStatus_TransactionStatusFailed	|
//	|	JusanResponseCodePayout_PayoutEmptyData                	|	"-103"  	|	IntegrationError_TransactionDeclinedByAcquirer	|	"Пустые зашифрованные данные"                                                                                  	|	EnumTransactionStatus_TransactionStatusFailed	|
//	|	JusanResponseCodePayout_PayoutEmptyAuth                	|	"-104"  	|	IntegrationError_TransactionDeclinedByAcquirer	|	"Пустой заголовок Authorization"                                                                               	|	EnumTransactionStatus_TransactionStatusFailed	|
//	|	JusanResponseCodePayout_PayoutIncorrectAuth            	|	"-105"  	|	IntegrationError_TransactionDeclinedByAcquirer	|	"Некорректный заголовок Authorization"                                                                         	|	EnumTransactionStatus_TransactionStatusFailed	|
//	|	JusanResponseCodePayout_PayoutIncorrectCurrency        	|	"-106"  	|	IntegrationError_TransactionDeclinedByAcquirer	|	"Некорректно указана валюта"                                                                                   	|	EnumTransactionStatus_TransactionStatusFailed	|
//	|	JusanResponseCodePayout_PayoutEDNotFound               	|	"-107"  	|	IntegrationError_TransactionDeclinedByAcquirer	|	"Транзакция ED не найдена"                                                                                     	|	EnumTransactionStatus_TransactionStatusFailed	|
//	|	JusanResponseCodePayout_PayoutEDNotEnabled             	|	"-108"  	|	IntegrationError_TransactionDeclinedByAcquirer	|	"Возможность зачисления с ЭД отключена"                                                                        	|	EnumTransactionStatus_TransactionStatusFailed	|
//	|	JusanResponseCodePayout_PayoutInsufficientFunds        	|	"-10000"	|	IntegrationError_InsufficientFunds            	|	"Сумма остатка на счете не позволяет провести данный платеж!"                                                  	|	EnumTransactionStatus_TransactionStatusFailed	|
//	|	JusanResponseCodePayout_PayoutIncorrectCard            	|	"-109"  	|	IntegrationError_IncorrectCardNumber          	|	"Неверно указан номер карты"                                                                                   	|	EnumTransactionStatus_TransactionStatusFailed	|
//	|	JusanResponseCodePayout_PayoutAccountBlocked           	|	"-110"  	|	IntegrationError_TransactionDeclinedByAcquirer	|	"Учетная запись партнера заблокирована"                                                                        	|	EnumTransactionStatus_TransactionStatusFailed	|
//	|	JusanResponseCodePayout_PayoutIncorrectSession         	|	"-111"  	|	IntegrationError_TransactionDeclinedByAcquirer	|	"Неверно указана сессия"                                                                                       	|	EnumTransactionStatus_TransactionStatusFailed	|
//	|	JusanResponseCodePayout_PayoutPaymentNotFound          	|	"-112"  	|	IntegrationError_TransactionDeclinedByAcquirer	|	"Информация о платеже не найдена"                                                                              	|	EnumTransactionStatus_TransactionStatusFailed	|
//	|	JusanResponseCodePayout_PayoutIncorrectAmount          	|	"-113"  	|	IntegrationError_TransactionDeclinedByAcquirer	|	"Суммы операции и гашения отличаются"                                                                          	|	EnumTransactionStatus_TransactionStatusFailed	|
//	|	JusanResponseCodePayout_PayoutIssuerDeclined           	|	"W05"   	|	IntegrationError_TransactionDeclinedByIssuer  	|	"Отказ Эмитента, причина не определена"                                                                        	|	EnumTransactionStatus_TransactionStatusFailed	|
//	|	JusanResponseCodePayout_PayoutCardDisabled             	|	"W07"   	|	IntegrationError_BlockedCard                  	|	"Карта отключена"                                                                                              	|	EnumTransactionStatus_TransactionStatusFailed	|
//	|	JusanResponseCodePayout_PayoutTransactionNotActive     	|	"W12"   	|	IntegrationError_TransactionDeclinedByAcquirer	|	"Недействительная транзакция"                                                                                  	|	EnumTransactionStatus_TransactionStatusFailed	|
//	|	JusanResponseCodePayout_PayoutCardNumIncorrect         	|	"W14"   	|	IntegrationError_IncorrectCardNumber          	|	"Недействительный номер карточки"                                                                              	|	EnumTransactionStatus_TransactionStatusFailed	|
//	|	JusanResponseCodePayout_PayoutClientDisabled           	|	"W17"   	|	IntegrationError_SuspiciousClient             	|	"Клиент исключен"                                                                                              	|	EnumTransactionStatus_TransactionStatusFailed	|
//	|	JusanResponseCodePayout_PayoutSuspiciousClient         	|	"W18"   	|	IntegrationError_SuspiciousClient             	|	"Подозрительный клиент"                                                                                        	|	EnumTransactionStatus_TransactionStatusFailed	|
//	|	JusanResponseCodePayout_PayoutRetryTransaction         	|	"W19"   	|	IntegrationError_TransactionDeclinedByAcquirer	|	"Повторная транзакция"                                                                                         	|	EnumTransactionStatus_TransactionStatusFailed	|
//	|	JusanResponseCodePayout_PayoutCardExpired              	|	"W33"   	|	IntegrationError_CardHasExpired               	|	"Карточка просрочена"                                                                                          	|	EnumTransactionStatus_TransactionStatusFailed	|
//	|	JusanResponseCodePayout_PayoutCardLimited              	|	"W36"   	|	IntegrationError_InvalidCard                  	|	"Карточка ограничена в использовании"                                                                          	|	EnumTransactionStatus_TransactionStatusFailed	|
//	|	JusanResponseCodePayout_PayoutStolenCard               	|	"W43"   	|	IntegrationError_StolenCard                   	|	"Украденная карточка"                                                                                          	|	EnumTransactionStatus_TransactionStatusFailed	|
//	|	JusanResponseCodePayout_PayoutExpCard                  	|	"W54"   	|	IntegrationError_CardHasExpired               	|	"Карточка просрочена"                                                                                          	|	EnumTransactionStatus_TransactionStatusFailed	|
//	|	JusanResponseCodePayout_PayoutForbiddenForClient       	|	"W57"   	|	IntegrationError_SuspiciousClient             	|	"Транзакция запрещена для клиента"                                                                             	|	EnumTransactionStatus_TransactionStatusFailed	|
//	|	JusanResponseCodePayout_PayoutForbiddenTerminal        	|	"W58"   	|	IntegrationError_PaymentForbiddenForMerchant  	|	"Транзакция запрещена для терминала"                                                                           	|	EnumTransactionStatus_TransactionStatusFailed	|
//	|	JusanResponseCodePayout_PayoutClientSuspicious         	|	"W59"   	|	IntegrationError_SuspiciousClient             	|	"Подозрение в мошенничестве"                                                                                   	|	EnumTransactionStatus_TransactionStatusFailed	|
//	|	JusanResponseCodePayout_PayoutLimitExceeded            	|	"W61"   	|	IntegrationError_TransactionDeclinedByAcquirer	|	"Превышает лимит суммы карты получателя"                                                                       	|	EnumTransactionStatus_TransactionStatusFailed	|
//	|	JusanResponseCodePayout_PayoutLimitedCard              	|	"W62"   	|	IntegrationError_InvalidCard                  	|	"Карточка с ограниченным использованием"                                                                       	|	EnumTransactionStatus_TransactionStatusFailed	|
//	|	JusanResponseCodePayout_PayoutSecurityBreached         	|	"W63"   	|	IntegrationError_TransactionDeclinedByAcquirer	|	"Нарушена безопасность"                                                                                        	|	EnumTransactionStatus_TransactionStatusFailed	|
//	|	JusanResponseCodePayout_PayoutUnavailableIssuer        	|	"W91"   	|	IntegrationError_UnavailableIssuer            	|	"Недействующий эмитент"                                                                                        	|	EnumTransactionStatus_TransactionStatusFailed	|
//	|	JusanResponseCodePayout_PayoutTransactionNotFinished   	|	"W93"   	|	IntegrationError_TransactionDeclinedByAcquirer	|	"Транзакция не завершена. Нарушение закона"                                                                    	|	EnumTransactionStatus_TransactionStatusFailed	|
//	|	JusanResponseCodePayout_PayoutSystemMalfuction         	|	"W96"   	|	IntegrationError_UnavailableAcquirer          	|	"Неисправность в системе"                                                                                      	|	EnumTransactionStatus_TransactionStatusHolded	|

var SliceJusanResponseCodePayoutRefs *sliceJusanResponseCodePayoutRefs

type sliceJusanResponseCodePayoutRefs struct{}

func (*sliceJusanResponseCodePayoutRefs) Code(slice ...JusanResponseCodePayout) []string {
	var result []string
	for _, val := range slice {
		result = append(result, val.Code())
	}

	return result
}

func (*sliceJusanResponseCodePayoutRefs) IntegrationError(slice ...JusanResponseCodePayout) []IntegrationError {
	var result []IntegrationError
	for _, val := range slice {
		result = append(result, val.IntegrationError())
	}

	return result
}

func (*sliceJusanResponseCodePayoutRefs) Message(slice ...JusanResponseCodePayout) []string {
	var result []string
	for _, val := range slice {
		result = append(result, val.Message())
	}

	return result
}

func (*sliceJusanResponseCodePayoutRefs) TransactionStatus(slice ...JusanResponseCodePayout) []EnumTransactionStatus {
	var result []EnumTransactionStatus
	for _, val := range slice {
		result = append(result, val.TransactionStatus())
	}

	return result
}
