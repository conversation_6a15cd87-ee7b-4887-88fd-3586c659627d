// Code generated by protoc-gen-go-mvp. DO NOT EDIT.
// versions:
// - protoc-gen-go-mvp v2.0.1
// created by mvp authors

package grpc

import (
	context "context"
	grpc "google.golang.org/grpc"
	metadata "google.golang.org/grpc/metadata"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
)

func NewIamMagnetiqServer(
	srv MagnetiqServer,
) MagnetiqServer {
	return &iamMagnetiqServer{
		srv: srv,
	}
}

var _ MagnetiqServer = (*iamMagnetiqServer)(nil)

type iamMagnetiqServer struct {
	UnimplementedMagnetiqServer

	srv MagnetiqServer
}

func (s *iamMagnetiqServer) PayIn(
	ctx context.Context,
	req *PayInRequestData,
) (
	*PayInResponseData,
	error,
) {
	return s.srv.PayIn(ctx, req)
}

func (s *iamMagnetiqServer) OneClickPayIn(
	ctx context.Context,
	req *OneClickPayInRequestData,
) (
	*PayInResponseData,
	error,
) {
	return s.srv.OneClickPayIn(ctx, req)
}

func (s *iamMagnetiqServer) ThreeDSConfirm(
	ctx context.Context,
	req *ThreeDSRequestData,
) (
	*ThreeDSResponseData,
	error,
) {
	return s.srv.ThreeDSConfirm(ctx, req)
}

func (s *iamMagnetiqServer) ThreeDSResume(
	ctx context.Context,
	req *ThreeDSResumeRequest,
) (
	*ThreeDSResumeResponse,
	error,
) {
	return s.srv.ThreeDSResume(ctx, req)
}

func (s *iamMagnetiqServer) PayOut(
	ctx context.Context,
	req *PayOutRequestData,
) (
	*PayOutResponseData,
	error,
) {
	return s.srv.PayOut(ctx, req)
}

func (s *iamMagnetiqServer) GetBankTransactionStatus(
	ctx context.Context,
	req *BankTransactionStatusRequest,
) (
	*BankTransactionStatusResponse,
	error,
) {
	return s.srv.GetBankTransactionStatus(ctx, req)
}

func (s *iamMagnetiqServer) GetBankTransactionStatusUnformated(
	ctx context.Context,
	req *BankTransactionStatusUnformatedRequest,
) (
	*BankTransactionStatusUnformatedResponse,
	error,
) {
	return s.srv.GetBankTransactionStatusUnformated(ctx, req)
}

func (s *iamMagnetiqServer) Refund(
	ctx context.Context,
	req *RefundRequest,
) (
	*RefundResponse,
	error,
) {
	return s.srv.Refund(ctx, req)
}

func (s *iamMagnetiqServer) GooglePay(
	ctx context.Context,
	req *GooglePayRequestData,
) (
	*GooglePayResponseData,
	error,
) {
	return s.srv.GooglePay(ctx, req)
}

func (s *iamMagnetiqServer) ApplePay(
	ctx context.Context,
	req *ApplePayRequestData,
) (
	*ApplePayResponseData,
	error,
) {
	return s.srv.ApplePay(ctx, req)
}

func (s *iamMagnetiqServer) TwoStagePayIn(
	ctx context.Context,
	req *TwoStagePayInRequest,
) (
	*TwoStagePayInResponse,
	error,
) {
	return s.srv.TwoStagePayIn(ctx, req)
}

func (s *iamMagnetiqServer) Charge(
	ctx context.Context,
	req *ChargeRequest,
) (
	*ChargeResponse,
	error,
) {
	return s.srv.Charge(ctx, req)
}

func (s *iamMagnetiqServer) Cancel(
	ctx context.Context,
	req *CancelRequest,
) (
	*CancelResponse,
	error,
) {
	return s.srv.Cancel(ctx, req)
}

func (s *iamMagnetiqServer) MakeToken(
	ctx context.Context,
	req *PayInRequestData,
) (
	*PayInResponseData,
	error,
) {
	return s.srv.MakeToken(ctx, req)
}

func (s *iamMagnetiqServer) GetAcquirerIdentifier(
	ctx context.Context,
	req *emptypb.Empty,
) (
	*GetAcquirerIdentifierResponse,
	error,
) {
	return s.srv.GetAcquirerIdentifier(ctx, req)
}

func (s *iamMagnetiqServer) ResolveVisaAlias(
	ctx context.Context,
	req *ResolveVisaAliasRequest,
) (
	*ResolveVisaAliasResponse,
	error,
) {
	return s.srv.ResolveVisaAlias(ctx, req)
}

func (s *iamMagnetiqServer) PayOutByPhone(
	ctx context.Context,
	req *PayOutByPhoneRequestData,
) (
	*PayOutResponseByPhoneData,
	error,
) {
	return s.srv.PayOutByPhone(ctx, req)
}

func NewIamMagnetiqClient(
	client MagnetiqClient,
) MagnetiqClient {
	return &iamMagnetiqClient{
		client: client,
	}
}

type iamMagnetiqClient struct {
	client MagnetiqClient
}

func (s *iamMagnetiqClient) PayIn(
	ctx context.Context,
	req *PayInRequestData,
	opts ...grpc.CallOption,
) (
	*PayInResponseData,
	error,
) {
	md, _ := metadata.FromIncomingContext(ctx)
	return s.client.PayIn(metadata.NewOutgoingContext(ctx, md), req)
}

func (s *iamMagnetiqClient) OneClickPayIn(
	ctx context.Context,
	req *OneClickPayInRequestData,
	opts ...grpc.CallOption,
) (
	*PayInResponseData,
	error,
) {
	md, _ := metadata.FromIncomingContext(ctx)
	return s.client.OneClickPayIn(metadata.NewOutgoingContext(ctx, md), req)
}

func (s *iamMagnetiqClient) ThreeDSConfirm(
	ctx context.Context,
	req *ThreeDSRequestData,
	opts ...grpc.CallOption,
) (
	*ThreeDSResponseData,
	error,
) {
	md, _ := metadata.FromIncomingContext(ctx)
	return s.client.ThreeDSConfirm(metadata.NewOutgoingContext(ctx, md), req)
}

func (s *iamMagnetiqClient) ThreeDSResume(
	ctx context.Context,
	req *ThreeDSResumeRequest,
	opts ...grpc.CallOption,
) (
	*ThreeDSResumeResponse,
	error,
) {
	md, _ := metadata.FromIncomingContext(ctx)
	return s.client.ThreeDSResume(metadata.NewOutgoingContext(ctx, md), req)
}

func (s *iamMagnetiqClient) PayOut(
	ctx context.Context,
	req *PayOutRequestData,
	opts ...grpc.CallOption,
) (
	*PayOutResponseData,
	error,
) {
	md, _ := metadata.FromIncomingContext(ctx)
	return s.client.PayOut(metadata.NewOutgoingContext(ctx, md), req)
}

func (s *iamMagnetiqClient) GetBankTransactionStatus(
	ctx context.Context,
	req *BankTransactionStatusRequest,
	opts ...grpc.CallOption,
) (
	*BankTransactionStatusResponse,
	error,
) {
	md, _ := metadata.FromIncomingContext(ctx)
	return s.client.GetBankTransactionStatus(metadata.NewOutgoingContext(ctx, md), req)
}

func (s *iamMagnetiqClient) GetBankTransactionStatusUnformated(
	ctx context.Context,
	req *BankTransactionStatusUnformatedRequest,
	opts ...grpc.CallOption,
) (
	*BankTransactionStatusUnformatedResponse,
	error,
) {
	md, _ := metadata.FromIncomingContext(ctx)
	return s.client.GetBankTransactionStatusUnformated(metadata.NewOutgoingContext(ctx, md), req)
}

func (s *iamMagnetiqClient) Refund(
	ctx context.Context,
	req *RefundRequest,
	opts ...grpc.CallOption,
) (
	*RefundResponse,
	error,
) {
	md, _ := metadata.FromIncomingContext(ctx)
	return s.client.Refund(metadata.NewOutgoingContext(ctx, md), req)
}

func (s *iamMagnetiqClient) GooglePay(
	ctx context.Context,
	req *GooglePayRequestData,
	opts ...grpc.CallOption,
) (
	*GooglePayResponseData,
	error,
) {
	md, _ := metadata.FromIncomingContext(ctx)
	return s.client.GooglePay(metadata.NewOutgoingContext(ctx, md), req)
}

func (s *iamMagnetiqClient) ApplePay(
	ctx context.Context,
	req *ApplePayRequestData,
	opts ...grpc.CallOption,
) (
	*ApplePayResponseData,
	error,
) {
	md, _ := metadata.FromIncomingContext(ctx)
	return s.client.ApplePay(metadata.NewOutgoingContext(ctx, md), req)
}

func (s *iamMagnetiqClient) TwoStagePayIn(
	ctx context.Context,
	req *TwoStagePayInRequest,
	opts ...grpc.CallOption,
) (
	*TwoStagePayInResponse,
	error,
) {
	md, _ := metadata.FromIncomingContext(ctx)
	return s.client.TwoStagePayIn(metadata.NewOutgoingContext(ctx, md), req)
}

func (s *iamMagnetiqClient) Charge(
	ctx context.Context,
	req *ChargeRequest,
	opts ...grpc.CallOption,
) (
	*ChargeResponse,
	error,
) {
	md, _ := metadata.FromIncomingContext(ctx)
	return s.client.Charge(metadata.NewOutgoingContext(ctx, md), req)
}

func (s *iamMagnetiqClient) Cancel(
	ctx context.Context,
	req *CancelRequest,
	opts ...grpc.CallOption,
) (
	*CancelResponse,
	error,
) {
	md, _ := metadata.FromIncomingContext(ctx)
	return s.client.Cancel(metadata.NewOutgoingContext(ctx, md), req)
}

func (s *iamMagnetiqClient) MakeToken(
	ctx context.Context,
	req *PayInRequestData,
	opts ...grpc.CallOption,
) (
	*PayInResponseData,
	error,
) {
	md, _ := metadata.FromIncomingContext(ctx)
	return s.client.MakeToken(metadata.NewOutgoingContext(ctx, md), req)
}

func (s *iamMagnetiqClient) GetAcquirerIdentifier(
	ctx context.Context,
	req *emptypb.Empty,
	opts ...grpc.CallOption,
) (
	*GetAcquirerIdentifierResponse,
	error,
) {
	md, _ := metadata.FromIncomingContext(ctx)
	return s.client.GetAcquirerIdentifier(metadata.NewOutgoingContext(ctx, md), req)
}

func (s *iamMagnetiqClient) ResolveVisaAlias(
	ctx context.Context,
	req *ResolveVisaAliasRequest,
	opts ...grpc.CallOption,
) (
	*ResolveVisaAliasResponse,
	error,
) {
	md, _ := metadata.FromIncomingContext(ctx)
	return s.client.ResolveVisaAlias(metadata.NewOutgoingContext(ctx, md), req)
}

func (s *iamMagnetiqClient) PayOutByPhone(
	ctx context.Context,
	req *PayOutByPhoneRequestData,
	opts ...grpc.CallOption,
) (
	*PayOutResponseByPhoneData,
	error,
) {
	md, _ := metadata.FromIncomingContext(ctx)
	return s.client.PayOutByPhone(metadata.NewOutgoingContext(ctx, md), req)
}
