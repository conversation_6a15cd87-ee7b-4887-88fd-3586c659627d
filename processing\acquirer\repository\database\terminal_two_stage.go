package database

import (
	"context"

	"gorm.io/gorm"

	"git.local/sensitive/innerpb/processing/goerr"
	"git.local/sensitive/processing/acquirer/model"
	"git.local/sensitive/sdk/dog"
)

type TerminalTwoStageDB struct {
	db *gorm.DB
}

func NewTerminalTwoStageDB(db *gorm.DB) TerminalTwoStager {
	return &TerminalTwoStageDB{
		db: db,
	}
}

func (t TerminalTwoStageDB) UpdateTimeout(ctx context.Context, id uint64, timeout uint32) (err error) {
	ctx, span := dog.CreateSpan(ctx, "TerminalTwoStageDB_UpdateTimeout")
	defer span.End()

	if err = t.db.Where("id = ?", id).Model(&model.Terminal{}).
		Update("two_stage_timeout", timeout).Error; err != nil {
		return goerr.ErrDbUnexpected.WithErr(err).WithCtx(ctx)
	}

	return nil
}
