-- +goose Up
-- +goose StatementBegin

create table if not exists account.transfer_statuses
(
    "created_at"
    TIMESTAMP
    not
    null
    DEFAULT
    NOW
(
),
    "updated_at" TIMES<PERSON>MP not null DEFAULT NOW
(
),
    "id" serial,
    "code" varchar
(
    256
) not null,
    "name" varchar
(
    256
) not null,
    UNI<PERSON><PERSON>
(
    code
),
    PRIMARY KEY
(
    id
)
    );

create table if not exists account.transfers
(
    "created_at"
    TIMESTAMP
    not
    null
    DEFAULT
    NOW
(
),
    "updated_at" TIMESTAMP not null DEFAULT NOW
(
),
    "transfer_created_at" TIMESTAMP,
    "id" serial,
    "acquirer_id" INTEGER,
    "account_id" INTEGER,
    "bank_reference_id" VARCHAR
(
    255
),
    "bank_order_id" VARCHAR
(
    255
),
    "project_id" INTEGER NOT NULL,
    "merchant_id" INTEGER NOT NULL,
    "status_id" INTEGER REFERENCES "account"."transfer_statuses",
    "amount" FLOAT NOT NULL,
    "merchant_bin" VARCHAR
(
    12
),
    "merchant_account" VARCHAR
(
    255
),
    "merchant_beneficiary_сode" VARCHAR
(
    2
),
    "description" VARCHAR
(
    255
),
    UNIQUE
(
    id
),
    PRIMARY KEY
(
    id
)
    );

create table if not exists account.accounts
(
    "created_at"
    TIMESTAMP
    not
    null
    DEFAULT
    NOW
(
),
    "updated_at" TIMESTAMP not null DEFAULT NOW
(
),
    "id" serial,
    "number" VARCHAR
(
    255
),
    "acquirer_id" INTEGER,
    "terminal_id" INTEGER,
    UNIQUE
(
    id
),
    PRIMARY KEY
(
    id
)
    );

create table if not exists account.merchant_account_relation_statuses
(
    "created_at"
    TIMESTAMP
    not
    null
    DEFAULT
    NOW
(
),
    "updated_at" TIMESTAMP not null DEFAULT NOW
(
),
    "id" serial,
    "code" varchar
(
    256
) not null,
    "name" varchar
(
    256
) not null,
    UNIQUE
(
    code
),
    PRIMARY KEY
(
    id
)
    );

create table if not exists account.merchant_account_relations
(
    "created_at"
    TIMESTAMP
    not
    null
    DEFAULT
    NOW
(
),
    "updated_at" TIMESTAMP not null DEFAULT NOW
(
),
    "id" serial,
    "account_id" integer,
    "merchant_id" integer,
    "status_id" INTEGER REFERENCES "account"."merchant_account_relation_statuses",
    UNIQUE
(
    id
),
    PRIMARY KEY
(
    id
)
    );

create table if not exists account.project_account_relation_statuses
(
    "created_at"
    TIMESTAMP
    not
    null
    DEFAULT
    NOW
(
),
    "updated_at" TIMESTAMP not null DEFAULT NOW
(
),
    "id" serial,
    "code" varchar
(
    256
) not null,
    "name" varchar
(
    256
) not null,
    UNIQUE
(
    code
),
    PRIMARY KEY
(
    id
)
    );

create table if not exists account.project_account_relations
(
    "created_at"
    TIMESTAMP
    not
    null
    DEFAULT
    NOW
(
),
    "updated_at" TIMESTAMP not null DEFAULT NOW
(
),
    "id" serial,
    "account_id" integer,
    "project_id" integer,
    "status_id" INTEGER REFERENCES "account"."project_account_relation_statuses",
    UNIQUE
(
    id
),
    PRIMARY KEY
(
    id
)
    );

-- +goose StatementEnd

-- +goose Down
-- +goose StatementBegin
-- +goose StatementEnd
