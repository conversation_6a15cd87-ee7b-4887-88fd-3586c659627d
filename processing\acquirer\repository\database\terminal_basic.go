package database

import (
	"context"
	"errors"

	"gorm.io/gorm"

	"git.local/sensitive/innerpb/processing/goerr"
	"git.local/sensitive/pkg/middlewares"
	"git.local/sensitive/processing/acquirer/model"
	"git.local/sensitive/processing/acquirer/schema"
	"git.local/sensitive/sdk/dog"
)

type TerminalBasicDB struct {
	db *gorm.DB
}

func NewTerminalBasicDB(db *gorm.DB) TerminalBasicer {
	return &TerminalBasicDB{
		db: db,
	}
}

func (r *TerminalBasicDB) Create(ctx context.Context, terminal *model.Terminal) (_ *model.Terminal, err error) {
	ctx, span := dog.CreateSpan(ctx, "TerminalBasicDB_Create")
	defer span.End()

	err = r.db.WithContext(ctx).Model(&model.Terminal{}).Create(&terminal).Error
	if err != nil {
		return nil, goerr.ErrDbUnexpected.WithErr(err).WithCtx(ctx)
	}

	return terminal, nil
}

func (r *TerminalBasicDB) GetAll(ctx context.Context, projectID uint64) (terminals []model.Terminal, err error) {
	ctx, span := dog.CreateSpan(ctx, "TerminalBasicDB_GetAll")
	defer span.End()

	var terminalProjects model.TerminalProjects

	err = r.db.WithContext(ctx).
		Where("project_id = ?", projectID).
		Where("is_active = ?", true).
		Find(&terminalProjects).Error
	if err != nil {
		return nil, err
	}

	terminalIDs := terminalProjects.GetTerminalIds()

	err = r.db.WithContext(ctx).
		Table("acquirer.terminals").
		Where("status = ?", model.TerminalOn).
		Where("id in (?)", terminalIDs).
		Preload("Acquirer").
		Find(&terminals).Error
	if err != nil {
		return nil, goerr.ErrDbUnexpected.WithErr(err).WithCtx(ctx)
	}

	if len(terminals) == 0 {
		return nil, goerr.ErrTerminalNotFound
	}

	for i := range terminals {
		for j := range terminalProjects {
			if terminals[i].ID == terminalProjects[j].TerminalID {
				terminals[i].TerminalProjects = append(terminals[i].TerminalProjects, terminalProjects[j])
			}
		}
	}

	return terminals, nil
}

func (r *TerminalBasicDB) GetAllInfoByID(ctx context.Context, id uint64) (_ *model.Terminal, err error) {
	ctx, span := dog.CreateSpan(ctx, "TerminalBasicDB_GetAllInfoByID")
	defer span.End()

	var terminal *model.Terminal

	err = r.db.WithContext(ctx).
		Model(&model.Terminal{}).
		Where(`id = ?`, id).
		Preload("Acquirer").
		First(&terminal).
		Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, goerr.ErrTerminalNotFound
		}

		return nil, goerr.ErrDbUnexpected.WithErr(err).WithCtx(ctx)
	}

	return terminal, nil
}

func (r *TerminalBasicDB) UpdateStatus(ctx context.Context, id uint64, status model.TerminalStatus) (err error) {
	ctx, span := dog.CreateSpan(ctx, "TerminalBasicDB_UpdateStatus")
	defer span.End()

	if err = r.db.WithContext(ctx).
		Model(&model.Terminal{}).
		Where("id = ?", id).
		Update("status", status).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return goerr.ErrTerminalNotFound
		}

		return err
	}

	return nil
}

func (r *TerminalBasicDB) GetByFilters(
	ctx context.Context,
	filters schema.FiltersTerminalRequest,
	pagination *middlewares.PaginationInfo,
) (_ []model.Terminal, err error) {
	ctx, span := dog.CreateSpan(ctx, "TerminalBasicDB_GetByFilters")
	defer span.End()

	res := make([]model.Terminal, 0)

	tx := r.db.WithContext(ctx).Model(&model.Terminal{})

	if filters.ProjectID != nil {
		tx.Where(`project_id = ?`, filters.ProjectID)
	}

	tx.Preload("Acquirer")

	if !pagination.Pagination {
		if err := tx.Find(&res).Error; err != nil {
			return nil, goerr.ErrDbUnexpected.WithErr(err).WithCtx(ctx)
		}

		return res, nil
	}

	if err := tx.WithContext(ctx).Model(&res).Count(&pagination.Total).Error; err != nil {
		return nil, goerr.ErrDbUnexpected.WithErr(err).WithCtx(ctx)
	}

	if err := tx.
		Offset((pagination.Page - 1) * pagination.PerPage).
		Limit(pagination.PerPage).
		Order(`id ASC`).
		Find(&res).Error; err != nil {
		return nil, goerr.ErrDbUnexpected.WithErr(err).WithCtx(ctx)
	}

	return res, nil
}

func (r *TerminalBasicDB) GetByAcquirers(
	ctx context.Context,
	acquirers []*model.Acquirer,
) (terminals []*model.Terminal, err error) {
	ctx, span := dog.CreateSpan(ctx, "TerminalBasicDB_GetByAcquirers")
	defer span.End()

	acquirerIDs := make([]uint64, len(acquirers))

	for i, v := range acquirers {
		acquirerIDs[i] = v.ID
	}

	if err = r.db.WithContext(ctx).
		Where(`acquirer_id IN (?)`, acquirerIDs).
		Find(&terminals).
		Error; err != nil {
		return nil, goerr.ErrDbUnexpected.WithErr(err).WithCtx(ctx)
	}

	return terminals, nil
}
