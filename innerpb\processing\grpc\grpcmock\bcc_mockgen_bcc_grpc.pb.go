// Code generated by MockGen. DO NOT EDIT.
// Source: bcc_grpc.pb.go

// Package grpcmock is a generated GoMock package.
package grpcmock

import (
	context "context"
	reflect "reflect"

	grpc "git.local/sensitive/innerpb/processing/grpc"
	gomock "github.com/golang/mock/gomock"
	grpc0 "google.golang.org/grpc"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
)

// MockBCCClient is a mock of BCCClient interface.
type MockBCCClient struct {
	ctrl     *gomock.Controller
	recorder *MockBCCClientMockRecorder
}

// MockBCCClientMockRecorder is the mock recorder for MockBCCClient.
type MockBCCClientMockRecorder struct {
	mock *MockBCCClient
}

// NewMockBCCClient creates a new mock instance.
func NewMockBCCClient(ctrl *gomock.Controller) *MockBCCClient {
	mock := &MockBCCClient{ctrl: ctrl}
	mock.recorder = &MockBCCClientMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockBCCClient) EXPECT() *MockBCCClientMockRecorder {
	return m.recorder
}

// ApplePay mocks base method.
func (m *MockBCCClient) ApplePay(ctx context.Context, in *grpc.ApplePayRequestData, opts ...grpc0.CallOption) (*grpc.ApplePayResponseData, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "ApplePay", varargs...)
	ret0, _ := ret[0].(*grpc.ApplePayResponseData)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ApplePay indicates an expected call of ApplePay.
func (mr *MockBCCClientMockRecorder) ApplePay(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ApplePay", reflect.TypeOf((*MockBCCClient)(nil).ApplePay), varargs...)
}

// Cancel mocks base method.
func (m *MockBCCClient) Cancel(ctx context.Context, in *grpc.CancelRequest, opts ...grpc0.CallOption) (*grpc.CancelResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "Cancel", varargs...)
	ret0, _ := ret[0].(*grpc.CancelResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Cancel indicates an expected call of Cancel.
func (mr *MockBCCClientMockRecorder) Cancel(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Cancel", reflect.TypeOf((*MockBCCClient)(nil).Cancel), varargs...)
}

// Charge mocks base method.
func (m *MockBCCClient) Charge(ctx context.Context, in *grpc.ChargeRequest, opts ...grpc0.CallOption) (*grpc.ChargeResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "Charge", varargs...)
	ret0, _ := ret[0].(*grpc.ChargeResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Charge indicates an expected call of Charge.
func (mr *MockBCCClientMockRecorder) Charge(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Charge", reflect.TypeOf((*MockBCCClient)(nil).Charge), varargs...)
}

// GetAcquirerIdentifier mocks base method.
func (m *MockBCCClient) GetAcquirerIdentifier(ctx context.Context, in *emptypb.Empty, opts ...grpc0.CallOption) (*grpc.GetAcquirerIdentifierResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetAcquirerIdentifier", varargs...)
	ret0, _ := ret[0].(*grpc.GetAcquirerIdentifierResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAcquirerIdentifier indicates an expected call of GetAcquirerIdentifier.
func (mr *MockBCCClientMockRecorder) GetAcquirerIdentifier(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAcquirerIdentifier", reflect.TypeOf((*MockBCCClient)(nil).GetAcquirerIdentifier), varargs...)
}

// GetBankTransactionStatus mocks base method.
func (m *MockBCCClient) GetBankTransactionStatus(ctx context.Context, in *grpc.BankTransactionStatusRequest, opts ...grpc0.CallOption) (*grpc.BankTransactionStatusResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetBankTransactionStatus", varargs...)
	ret0, _ := ret[0].(*grpc.BankTransactionStatusResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetBankTransactionStatus indicates an expected call of GetBankTransactionStatus.
func (mr *MockBCCClientMockRecorder) GetBankTransactionStatus(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetBankTransactionStatus", reflect.TypeOf((*MockBCCClient)(nil).GetBankTransactionStatus), varargs...)
}

// GetBankTransactionStatusUnformated mocks base method.
func (m *MockBCCClient) GetBankTransactionStatusUnformated(ctx context.Context, in *grpc.BankTransactionStatusUnformatedRequest, opts ...grpc0.CallOption) (*grpc.BankTransactionStatusUnformatedResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetBankTransactionStatusUnformated", varargs...)
	ret0, _ := ret[0].(*grpc.BankTransactionStatusUnformatedResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetBankTransactionStatusUnformated indicates an expected call of GetBankTransactionStatusUnformated.
func (mr *MockBCCClientMockRecorder) GetBankTransactionStatusUnformated(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetBankTransactionStatusUnformated", reflect.TypeOf((*MockBCCClient)(nil).GetBankTransactionStatusUnformated), varargs...)
}

// GooglePay mocks base method.
func (m *MockBCCClient) GooglePay(ctx context.Context, in *grpc.GooglePayRequestData, opts ...grpc0.CallOption) (*grpc.GooglePayResponseData, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GooglePay", varargs...)
	ret0, _ := ret[0].(*grpc.GooglePayResponseData)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GooglePay indicates an expected call of GooglePay.
func (mr *MockBCCClientMockRecorder) GooglePay(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GooglePay", reflect.TypeOf((*MockBCCClient)(nil).GooglePay), varargs...)
}

// MakeToken mocks base method.
func (m *MockBCCClient) MakeToken(ctx context.Context, in *grpc.PayInRequestData, opts ...grpc0.CallOption) (*grpc.PayInResponseData, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "MakeToken", varargs...)
	ret0, _ := ret[0].(*grpc.PayInResponseData)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// MakeToken indicates an expected call of MakeToken.
func (mr *MockBCCClientMockRecorder) MakeToken(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "MakeToken", reflect.TypeOf((*MockBCCClient)(nil).MakeToken), varargs...)
}

// OneClickPayIn mocks base method.
func (m *MockBCCClient) OneClickPayIn(ctx context.Context, in *grpc.OneClickPayInRequestData, opts ...grpc0.CallOption) (*grpc.PayInResponseData, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "OneClickPayIn", varargs...)
	ret0, _ := ret[0].(*grpc.PayInResponseData)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// OneClickPayIn indicates an expected call of OneClickPayIn.
func (mr *MockBCCClientMockRecorder) OneClickPayIn(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "OneClickPayIn", reflect.TypeOf((*MockBCCClient)(nil).OneClickPayIn), varargs...)
}

// PayIn mocks base method.
func (m *MockBCCClient) PayIn(ctx context.Context, in *grpc.PayInRequestData, opts ...grpc0.CallOption) (*grpc.PayInResponseData, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "PayIn", varargs...)
	ret0, _ := ret[0].(*grpc.PayInResponseData)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// PayIn indicates an expected call of PayIn.
func (mr *MockBCCClientMockRecorder) PayIn(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "PayIn", reflect.TypeOf((*MockBCCClient)(nil).PayIn), varargs...)
}

// PayOut mocks base method.
func (m *MockBCCClient) PayOut(ctx context.Context, in *grpc.PayOutRequestData, opts ...grpc0.CallOption) (*grpc.PayOutResponseData, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "PayOut", varargs...)
	ret0, _ := ret[0].(*grpc.PayOutResponseData)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// PayOut indicates an expected call of PayOut.
func (mr *MockBCCClientMockRecorder) PayOut(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "PayOut", reflect.TypeOf((*MockBCCClient)(nil).PayOut), varargs...)
}

// PayOutByPhone mocks base method.
func (m *MockBCCClient) PayOutByPhone(ctx context.Context, in *grpc.PayOutByPhoneRequestData, opts ...grpc0.CallOption) (*grpc.PayOutResponseByPhoneData, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "PayOutByPhone", varargs...)
	ret0, _ := ret[0].(*grpc.PayOutResponseByPhoneData)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// PayOutByPhone indicates an expected call of PayOutByPhone.
func (mr *MockBCCClientMockRecorder) PayOutByPhone(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "PayOutByPhone", reflect.TypeOf((*MockBCCClient)(nil).PayOutByPhone), varargs...)
}

// Refund mocks base method.
func (m *MockBCCClient) Refund(ctx context.Context, in *grpc.RefundRequest, opts ...grpc0.CallOption) (*grpc.RefundResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "Refund", varargs...)
	ret0, _ := ret[0].(*grpc.RefundResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Refund indicates an expected call of Refund.
func (mr *MockBCCClientMockRecorder) Refund(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Refund", reflect.TypeOf((*MockBCCClient)(nil).Refund), varargs...)
}

// ResolveVisaAlias mocks base method.
func (m *MockBCCClient) ResolveVisaAlias(ctx context.Context, in *grpc.ResolveVisaAliasRequest, opts ...grpc0.CallOption) (*grpc.ResolveVisaAliasResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "ResolveVisaAlias", varargs...)
	ret0, _ := ret[0].(*grpc.ResolveVisaAliasResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ResolveVisaAlias indicates an expected call of ResolveVisaAlias.
func (mr *MockBCCClientMockRecorder) ResolveVisaAlias(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ResolveVisaAlias", reflect.TypeOf((*MockBCCClient)(nil).ResolveVisaAlias), varargs...)
}

// ThreeDSConfirm mocks base method.
func (m *MockBCCClient) ThreeDSConfirm(ctx context.Context, in *grpc.ThreeDSRequestData, opts ...grpc0.CallOption) (*grpc.ThreeDSResponseData, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "ThreeDSConfirm", varargs...)
	ret0, _ := ret[0].(*grpc.ThreeDSResponseData)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ThreeDSConfirm indicates an expected call of ThreeDSConfirm.
func (mr *MockBCCClientMockRecorder) ThreeDSConfirm(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ThreeDSConfirm", reflect.TypeOf((*MockBCCClient)(nil).ThreeDSConfirm), varargs...)
}

// ThreeDSResume mocks base method.
func (m *MockBCCClient) ThreeDSResume(ctx context.Context, in *grpc.ThreeDSResumeRequest, opts ...grpc0.CallOption) (*grpc.ThreeDSResumeResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "ThreeDSResume", varargs...)
	ret0, _ := ret[0].(*grpc.ThreeDSResumeResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ThreeDSResume indicates an expected call of ThreeDSResume.
func (mr *MockBCCClientMockRecorder) ThreeDSResume(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ThreeDSResume", reflect.TypeOf((*MockBCCClient)(nil).ThreeDSResume), varargs...)
}

// TwoStagePayIn mocks base method.
func (m *MockBCCClient) TwoStagePayIn(ctx context.Context, in *grpc.TwoStagePayInRequest, opts ...grpc0.CallOption) (*grpc.TwoStagePayInResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "TwoStagePayIn", varargs...)
	ret0, _ := ret[0].(*grpc.TwoStagePayInResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// TwoStagePayIn indicates an expected call of TwoStagePayIn.
func (mr *MockBCCClientMockRecorder) TwoStagePayIn(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "TwoStagePayIn", reflect.TypeOf((*MockBCCClient)(nil).TwoStagePayIn), varargs...)
}

// MockBCCServer is a mock of BCCServer interface.
type MockBCCServer struct {
	ctrl     *gomock.Controller
	recorder *MockBCCServerMockRecorder
}

// MockBCCServerMockRecorder is the mock recorder for MockBCCServer.
type MockBCCServerMockRecorder struct {
	mock *MockBCCServer
}

// NewMockBCCServer creates a new mock instance.
func NewMockBCCServer(ctrl *gomock.Controller) *MockBCCServer {
	mock := &MockBCCServer{ctrl: ctrl}
	mock.recorder = &MockBCCServerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockBCCServer) EXPECT() *MockBCCServerMockRecorder {
	return m.recorder
}

// ApplePay mocks base method.
func (m *MockBCCServer) ApplePay(arg0 context.Context, arg1 *grpc.ApplePayRequestData) (*grpc.ApplePayResponseData, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ApplePay", arg0, arg1)
	ret0, _ := ret[0].(*grpc.ApplePayResponseData)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ApplePay indicates an expected call of ApplePay.
func (mr *MockBCCServerMockRecorder) ApplePay(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ApplePay", reflect.TypeOf((*MockBCCServer)(nil).ApplePay), arg0, arg1)
}

// Cancel mocks base method.
func (m *MockBCCServer) Cancel(arg0 context.Context, arg1 *grpc.CancelRequest) (*grpc.CancelResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Cancel", arg0, arg1)
	ret0, _ := ret[0].(*grpc.CancelResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Cancel indicates an expected call of Cancel.
func (mr *MockBCCServerMockRecorder) Cancel(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Cancel", reflect.TypeOf((*MockBCCServer)(nil).Cancel), arg0, arg1)
}

// Charge mocks base method.
func (m *MockBCCServer) Charge(arg0 context.Context, arg1 *grpc.ChargeRequest) (*grpc.ChargeResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Charge", arg0, arg1)
	ret0, _ := ret[0].(*grpc.ChargeResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Charge indicates an expected call of Charge.
func (mr *MockBCCServerMockRecorder) Charge(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Charge", reflect.TypeOf((*MockBCCServer)(nil).Charge), arg0, arg1)
}

// GetAcquirerIdentifier mocks base method.
func (m *MockBCCServer) GetAcquirerIdentifier(arg0 context.Context, arg1 *emptypb.Empty) (*grpc.GetAcquirerIdentifierResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAcquirerIdentifier", arg0, arg1)
	ret0, _ := ret[0].(*grpc.GetAcquirerIdentifierResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAcquirerIdentifier indicates an expected call of GetAcquirerIdentifier.
func (mr *MockBCCServerMockRecorder) GetAcquirerIdentifier(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAcquirerIdentifier", reflect.TypeOf((*MockBCCServer)(nil).GetAcquirerIdentifier), arg0, arg1)
}

// GetBankTransactionStatus mocks base method.
func (m *MockBCCServer) GetBankTransactionStatus(arg0 context.Context, arg1 *grpc.BankTransactionStatusRequest) (*grpc.BankTransactionStatusResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetBankTransactionStatus", arg0, arg1)
	ret0, _ := ret[0].(*grpc.BankTransactionStatusResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetBankTransactionStatus indicates an expected call of GetBankTransactionStatus.
func (mr *MockBCCServerMockRecorder) GetBankTransactionStatus(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetBankTransactionStatus", reflect.TypeOf((*MockBCCServer)(nil).GetBankTransactionStatus), arg0, arg1)
}

// GetBankTransactionStatusUnformated mocks base method.
func (m *MockBCCServer) GetBankTransactionStatusUnformated(arg0 context.Context, arg1 *grpc.BankTransactionStatusUnformatedRequest) (*grpc.BankTransactionStatusUnformatedResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetBankTransactionStatusUnformated", arg0, arg1)
	ret0, _ := ret[0].(*grpc.BankTransactionStatusUnformatedResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetBankTransactionStatusUnformated indicates an expected call of GetBankTransactionStatusUnformated.
func (mr *MockBCCServerMockRecorder) GetBankTransactionStatusUnformated(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetBankTransactionStatusUnformated", reflect.TypeOf((*MockBCCServer)(nil).GetBankTransactionStatusUnformated), arg0, arg1)
}

// GooglePay mocks base method.
func (m *MockBCCServer) GooglePay(arg0 context.Context, arg1 *grpc.GooglePayRequestData) (*grpc.GooglePayResponseData, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GooglePay", arg0, arg1)
	ret0, _ := ret[0].(*grpc.GooglePayResponseData)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GooglePay indicates an expected call of GooglePay.
func (mr *MockBCCServerMockRecorder) GooglePay(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GooglePay", reflect.TypeOf((*MockBCCServer)(nil).GooglePay), arg0, arg1)
}

// MakeToken mocks base method.
func (m *MockBCCServer) MakeToken(arg0 context.Context, arg1 *grpc.PayInRequestData) (*grpc.PayInResponseData, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "MakeToken", arg0, arg1)
	ret0, _ := ret[0].(*grpc.PayInResponseData)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// MakeToken indicates an expected call of MakeToken.
func (mr *MockBCCServerMockRecorder) MakeToken(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "MakeToken", reflect.TypeOf((*MockBCCServer)(nil).MakeToken), arg0, arg1)
}

// OneClickPayIn mocks base method.
func (m *MockBCCServer) OneClickPayIn(arg0 context.Context, arg1 *grpc.OneClickPayInRequestData) (*grpc.PayInResponseData, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "OneClickPayIn", arg0, arg1)
	ret0, _ := ret[0].(*grpc.PayInResponseData)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// OneClickPayIn indicates an expected call of OneClickPayIn.
func (mr *MockBCCServerMockRecorder) OneClickPayIn(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "OneClickPayIn", reflect.TypeOf((*MockBCCServer)(nil).OneClickPayIn), arg0, arg1)
}

// PayIn mocks base method.
func (m *MockBCCServer) PayIn(arg0 context.Context, arg1 *grpc.PayInRequestData) (*grpc.PayInResponseData, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "PayIn", arg0, arg1)
	ret0, _ := ret[0].(*grpc.PayInResponseData)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// PayIn indicates an expected call of PayIn.
func (mr *MockBCCServerMockRecorder) PayIn(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "PayIn", reflect.TypeOf((*MockBCCServer)(nil).PayIn), arg0, arg1)
}

// PayOut mocks base method.
func (m *MockBCCServer) PayOut(arg0 context.Context, arg1 *grpc.PayOutRequestData) (*grpc.PayOutResponseData, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "PayOut", arg0, arg1)
	ret0, _ := ret[0].(*grpc.PayOutResponseData)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// PayOut indicates an expected call of PayOut.
func (mr *MockBCCServerMockRecorder) PayOut(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "PayOut", reflect.TypeOf((*MockBCCServer)(nil).PayOut), arg0, arg1)
}

// PayOutByPhone mocks base method.
func (m *MockBCCServer) PayOutByPhone(arg0 context.Context, arg1 *grpc.PayOutByPhoneRequestData) (*grpc.PayOutResponseByPhoneData, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "PayOutByPhone", arg0, arg1)
	ret0, _ := ret[0].(*grpc.PayOutResponseByPhoneData)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// PayOutByPhone indicates an expected call of PayOutByPhone.
func (mr *MockBCCServerMockRecorder) PayOutByPhone(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "PayOutByPhone", reflect.TypeOf((*MockBCCServer)(nil).PayOutByPhone), arg0, arg1)
}

// Refund mocks base method.
func (m *MockBCCServer) Refund(arg0 context.Context, arg1 *grpc.RefundRequest) (*grpc.RefundResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Refund", arg0, arg1)
	ret0, _ := ret[0].(*grpc.RefundResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Refund indicates an expected call of Refund.
func (mr *MockBCCServerMockRecorder) Refund(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Refund", reflect.TypeOf((*MockBCCServer)(nil).Refund), arg0, arg1)
}

// ResolveVisaAlias mocks base method.
func (m *MockBCCServer) ResolveVisaAlias(arg0 context.Context, arg1 *grpc.ResolveVisaAliasRequest) (*grpc.ResolveVisaAliasResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ResolveVisaAlias", arg0, arg1)
	ret0, _ := ret[0].(*grpc.ResolveVisaAliasResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ResolveVisaAlias indicates an expected call of ResolveVisaAlias.
func (mr *MockBCCServerMockRecorder) ResolveVisaAlias(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ResolveVisaAlias", reflect.TypeOf((*MockBCCServer)(nil).ResolveVisaAlias), arg0, arg1)
}

// ThreeDSConfirm mocks base method.
func (m *MockBCCServer) ThreeDSConfirm(arg0 context.Context, arg1 *grpc.ThreeDSRequestData) (*grpc.ThreeDSResponseData, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ThreeDSConfirm", arg0, arg1)
	ret0, _ := ret[0].(*grpc.ThreeDSResponseData)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ThreeDSConfirm indicates an expected call of ThreeDSConfirm.
func (mr *MockBCCServerMockRecorder) ThreeDSConfirm(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ThreeDSConfirm", reflect.TypeOf((*MockBCCServer)(nil).ThreeDSConfirm), arg0, arg1)
}

// ThreeDSResume mocks base method.
func (m *MockBCCServer) ThreeDSResume(arg0 context.Context, arg1 *grpc.ThreeDSResumeRequest) (*grpc.ThreeDSResumeResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ThreeDSResume", arg0, arg1)
	ret0, _ := ret[0].(*grpc.ThreeDSResumeResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ThreeDSResume indicates an expected call of ThreeDSResume.
func (mr *MockBCCServerMockRecorder) ThreeDSResume(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ThreeDSResume", reflect.TypeOf((*MockBCCServer)(nil).ThreeDSResume), arg0, arg1)
}

// TwoStagePayIn mocks base method.
func (m *MockBCCServer) TwoStagePayIn(arg0 context.Context, arg1 *grpc.TwoStagePayInRequest) (*grpc.TwoStagePayInResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "TwoStagePayIn", arg0, arg1)
	ret0, _ := ret[0].(*grpc.TwoStagePayInResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// TwoStagePayIn indicates an expected call of TwoStagePayIn.
func (mr *MockBCCServerMockRecorder) TwoStagePayIn(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "TwoStagePayIn", reflect.TypeOf((*MockBCCServer)(nil).TwoStagePayIn), arg0, arg1)
}

// mustEmbedUnimplementedBCCServer mocks base method.
func (m *MockBCCServer) mustEmbedUnimplementedBCCServer() {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "mustEmbedUnimplementedBCCServer")
}

// mustEmbedUnimplementedBCCServer indicates an expected call of mustEmbedUnimplementedBCCServer.
func (mr *MockBCCServerMockRecorder) mustEmbedUnimplementedBCCServer() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "mustEmbedUnimplementedBCCServer", reflect.TypeOf((*MockBCCServer)(nil).mustEmbedUnimplementedBCCServer))
}

// MockUnsafeBCCServer is a mock of UnsafeBCCServer interface.
type MockUnsafeBCCServer struct {
	ctrl     *gomock.Controller
	recorder *MockUnsafeBCCServerMockRecorder
}

// MockUnsafeBCCServerMockRecorder is the mock recorder for MockUnsafeBCCServer.
type MockUnsafeBCCServerMockRecorder struct {
	mock *MockUnsafeBCCServer
}

// NewMockUnsafeBCCServer creates a new mock instance.
func NewMockUnsafeBCCServer(ctrl *gomock.Controller) *MockUnsafeBCCServer {
	mock := &MockUnsafeBCCServer{ctrl: ctrl}
	mock.recorder = &MockUnsafeBCCServerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockUnsafeBCCServer) EXPECT() *MockUnsafeBCCServerMockRecorder {
	return m.recorder
}

// mustEmbedUnimplementedBCCServer mocks base method.
func (m *MockUnsafeBCCServer) mustEmbedUnimplementedBCCServer() {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "mustEmbedUnimplementedBCCServer")
}

// mustEmbedUnimplementedBCCServer indicates an expected call of mustEmbedUnimplementedBCCServer.
func (mr *MockUnsafeBCCServerMockRecorder) mustEmbedUnimplementedBCCServer() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "mustEmbedUnimplementedBCCServer", reflect.TypeOf((*MockUnsafeBCCServer)(nil).mustEmbedUnimplementedBCCServer))
}
