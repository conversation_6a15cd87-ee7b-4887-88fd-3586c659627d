edition = "2023";

package processing.jusan_payin.jusan_payin;

option go_package = "git.local/sensitive/innerpb/processing/grpc";

import "inner/processing/grpc/transaction_status.proto";
import "mvp/proto/refs.proto";
import "mvp/proto/enum.proto";
import "inner/processing/grpc/integration.proto";
import "google/protobuf/descriptor.proto";

message JusanResponseCodePayinRef {
  string code = 1;
  string message = 2;
  transaction.transaction_status.EnumTransactionStatus transaction_status = 3;
  integration.integration.IntegrationError integration_error = 4;
}

extend google.protobuf.EnumValueOptions {
  JusanResponseCodePayinRef jusan_response_code_payin_value = 100119;
}

extend google.protobuf.EnumOptions {
  JusanResponseCodePayinRef default_jusan_response_code_payin_value = 100120;
}

enum JusanResponseCodePayin {
  option(mvp.default_ref) = "default_jusan_response_code_payin_value";
  option(mvp.ref) = "jusan_response_code_payin_value";
  option(default_jusan_response_code_payin_value) = {
    code: "0"
    message: "default"
    transaction_status: TransactionStatusError
    integration_error: None
  };

  PayinServiceUnavailable = 0 [(jusan_response_code_payin_value) = {
    code: "11"
    transaction_status: TransactionStatusFailed
    integration_error: UnavailableAcquirer
    message: "Сервис временно недоступен, попробуйте позже"
  }, (mvp.from_string) = "11"];

  PayinIncorrectOrder = 1 [(jusan_response_code_payin_value) = {
    code: "12"
    transaction_status: TransactionStatusFailed
    integration_error: TransactionDeclinedByAcquirer
    message: "Неправильное значение в поле ORDER:"
  }, (mvp.from_string) = "12"];

  PayinIncorrectAmount = 2 [(jusan_response_code_payin_value) = {
    code: "13"
    transaction_status: TransactionStatusFailed
    integration_error: TransactionDeclinedByAcquirer
    message: "Неправильная сумма: "
  }, (mvp.from_string) = "13"];

  PayinCardInvalid = 3 [(jusan_response_code_payin_value) = {
    code: "14"
    transaction_status: TransactionStatusFailed
    integration_error: InvalidCard
    message: "Недействительная карта:"
  }, (mvp.from_string) = "14"];

  PayinNoSuchCard = 4 [(jusan_response_code_payin_value) = {
    code: "15"
    transaction_status: TransactionStatusFailed
    integration_error: InvalidCard
    message: "No such card"
  }, (mvp.from_string) = "15"];

  PayinDbUnavailable = 5 [(jusan_response_code_payin_value) = {
    code: "16"
    transaction_status: TransactionStatusFailed
    integration_error: UnavailableAcquirer
    message: "Сервис Db временно недоступен, попробуйте позже"
  }, (mvp.from_string) = "16"];

  PayinForbiddenMerchant = 6 [(jusan_response_code_payin_value) = {
    code: "171"
    transaction_status: TransactionStatusFailed
    integration_error: PaymentForbiddenForMerchant
    message: "Коммерсанту запрещено выполнение операций"
  }, (mvp.from_string) = "171"];

  PayinForbiddenForMerchant = 7 [(jusan_response_code_payin_value) = {
    code: "172"
    transaction_status: TransactionStatusFailed
    integration_error: PaymentForbiddenForMerchant
    message: "Коммерсанту запрещено проведение операций в соответствии с Законом о ПОД/ФТ"
  }, (mvp.from_string) = "172"];

  PayinRequestAlreadyCompleted = 8 [(jusan_response_code_payin_value) = {
    code: "18"
    transaction_status: TransactionStatusFailed
    integration_error: TransactionDeclinedByAcquirer
    message: "Запрос уже выполнялся"
  }, (mvp.from_string) = "18"];

  PayinIncorrectCardExpDate = 9 [(jusan_response_code_payin_value) = {
    code: "19"
    transaction_status: TransactionStatusFailed
    integration_error: IncorrectCardExpDate
    message: "Неправильная дата дейстия карты (MM/ГГ)"
  }, (mvp.from_string) = "19"];

  PayinIncorrectTerminal = 10 [(jusan_response_code_payin_value) = {
    code: "20"
    transaction_status: TransactionStatusFailed
    integration_error: TransactionDeclinedByAcquirer
    message: "Неправильное значение в поле TERMINAL:"
  }, (mvp.from_string) = "20"];

  PayinInvalidSign = 11 [(jusan_response_code_payin_value) = {
    code: "21"
    transaction_status: TransactionStatusFailed
    integration_error: TransactionDeclinedByAcquirer
    message: "Неправильная подпись!"
  }, (mvp.from_string) = "21"];

  PayinCurrencyNotFound = 12 [(jusan_response_code_payin_value) = {
    code: "22"
    transaction_status: TransactionStatusFailed
    integration_error: TransactionDeclinedByAcquirer
    message: "Не найден курс валюты"
  }, (mvp.from_string) = "22"];

  PayinLimitExceeded = 13 [(jusan_response_code_payin_value) = {
    code: "23"
    transaction_status: TransactionStatusFailed
    integration_error: ExceedsAmountLimit
    message: "Превышен лимит!"
  }, (mvp.from_string) = "23"];

  PayinEmptyField = 14 [(jusan_response_code_payin_value) = {
    code: "24"
    transaction_status: TransactionStatusFailed
    integration_error: TransactionDeclinedByAcquirer
    message: "Не указано значение в поле"
  }, (mvp.from_string) = "24"];

  PayinSizeLessSymbol = 15 [(jusan_response_code_payin_value) = {
    code: "25"
    transaction_status: TransactionStatusFailed
    integration_error: TransactionDeclinedByAcquirer
    message: "Размер значения в поле менее симоволов"
  }, (mvp.from_string) = "25"];

  PayinSizeMoreSymbol = 16 [(jusan_response_code_payin_value) = {
    code: "26"
    transaction_status: TransactionStatusFailed
    integration_error: TransactionDeclinedByAcquirer
    message: "Размер значения в поле больше симоволов"
  }, (mvp.from_string) = "26"];

  PayinInvalidValue = 17 [(jusan_response_code_payin_value) = {
    code: "27"
    transaction_status: TransactionStatusFailed
    integration_error: TransactionDeclinedByAcquirer
    message: "Введите валидное значение в поле"
  }, (mvp.from_string) = "27"];

  PayinErrorMPI3DS = 18 [(jusan_response_code_payin_value) = {
    code: "28"
    transaction_status: TransactionStatusFailed
    integration_error: TransactionDeclinedByAcquirer
    message: "Ошибка MPI при выполнении проверки 3DS:"
  }, (mvp.from_string) = "28"];

  PayinInvalidCardType = 19 [(jusan_response_code_payin_value) = {
    code: "29"
    transaction_status: TransactionStatusFailed
    integration_error: InvalidCard
    message: "Недопустимый тип карты"
  }, (mvp.from_string) = "29"];

  PayinPaymentNotFound = 20 [(jusan_response_code_payin_value) = {
    code: "30"
    transaction_status: TransactionStatusFailed
    integration_error: TransactionDeclinedByAcquirer
    message: "Счет на оплату не найден"
  }, (mvp.from_string) = "30"];

  PayinEmptyClientKey = 21 [(jusan_response_code_payin_value) = {
    code: "31"
    transaction_status: TransactionStatusFailed
    integration_error: TransactionDeclinedByAcquirer
    message: "Не передан ключ указанного клиента"
  }, (mvp.from_string) = "31"];

  PayinTerminalForbidden = 22 [(jusan_response_code_payin_value) = {
    code: "32"
    transaction_status: TransactionStatusFailed
    integration_error: PaymentForbiddenForMerchant
    message: "Для терминала запрещена токенизация"
  }, (mvp.from_string) = "32"];

  PayinTokenNotFound = 23 [(jusan_response_code_payin_value) = {
    code: "33"
    transaction_status: TransactionStatusFailed
    integration_error: InvalidCard
    message: "Для данного клиента в вашей организации не зарегистрирован токен"
  }, (mvp.from_string) = "33"];

  PayinIncorrectBlockSum = 24 [(jusan_response_code_payin_value) = {
    code: "34"
    transaction_status: TransactionStatusFailed
    integration_error: TransactionDeclinedByAcquirer
    message: "Неверная сумма блокирования, заявка отменена!"
  }, (mvp.from_string) = "34"];

  PayinUnknownError = 25 [(jusan_response_code_payin_value) = {
    code: "99"
    transaction_status: TransactionStatusHolded
    integration_error: TransactionDeclinedByAcquirer
    message: "Неизвестная ошибка: "
  }, (mvp.from_string) = "99"];

  PayinUnavailableService = 26 [(jusan_response_code_payin_value) = {
    code: "41"
    transaction_status: TransactionStatusFailed
    integration_error: UnavailableAcquirer
    message: "Сервис временно недоступен, попробуйте позже"
  }, (mvp.from_string) = "41"];

  PayinAmountIncorrect = 27 [(jusan_response_code_payin_value) = {
    code: "42"
    transaction_status: TransactionStatusFailed
    integration_error: TransactionDeclinedByAcquirer
    message: "Неправильная сумма"
  }, (mvp.from_string) = "42"];

  PayinStolenCard = 28 [(jusan_response_code_payin_value) = {
    code: "43"
    transaction_status: TransactionStatusFailed
    integration_error: StolenCard
    message: "Stolen card"
  }, (mvp.from_string) = "43"];

  PayinIncorrectMerchant = 29 [(jusan_response_code_payin_value) = {
    code: "44"
    transaction_status: TransactionStatusFailed
    integration_error: TransactionDeclinedByAcquirer
    message: "Неправильное значение в поле MERCHANT"
  }, (mvp.from_string) = "44"];

  PayinMerchantNotFound = 30 [(jusan_response_code_payin_value) = {
    code: "17"
    transaction_status: TransactionStatusFailed
    integration_error: PaymentForbiddenForMerchant
    message: "Коммерсант не найден"
  }, (mvp.from_string) = "17"];

  PayinOrderNotFound = 31 [(jusan_response_code_payin_value) = {
    code: "45"
    transaction_status: TransactionStatusFailed
    integration_error: TransactionDeclinedByAcquirer
    message: "Заявка ORDER не найдена"
  }, (mvp.from_string) = "45"];

  PayinSignInvalid = 32 [(jusan_response_code_payin_value) = {
    code: "46"
    transaction_status: TransactionStatusFailed
    integration_error: TransactionDeclinedByAcquirer
    message: "Неправильная подпись!"
  }, (mvp.from_string) = "46"];

  PayinIncorrectRefundSum = 33 [(jusan_response_code_payin_value) = {
    code: "47"
    transaction_status: TransactionStatusFailed
    integration_error: TransactionDeclinedByAcquirer
    message: "Сумма возврта '%s' больше чем сумма заказа"
  }, (mvp.from_string) = "47"];

  PayinIncorrectOrderStatus = 34 [(jusan_response_code_payin_value) = {
    code: "48"
    transaction_status: TransactionStatusFailed
    integration_error: TransactionDeclinedByAcquirer
    message: "Текущий статус заказа не позволяет делать возврат/отмену"
  }, (mvp.from_string) = "48"];

  PayinIncorrectValue = 35 [(jusan_response_code_payin_value) = {
    code: "50"
    transaction_status: TransactionStatusFailed
    integration_error: TransactionDeclinedByAcquirer
    message: "Неправильное значение"
  }, (mvp.from_string) = "50"];

  PayinForbiddenTerminal = 36 [(jusan_response_code_payin_value) = {
    code: "52"
    transaction_status: TransactionStatusFailed
    integration_error: PaymentForbiddenForMerchant
    message: "Операция отмены/возврата через API для терминала запрещена"
  }, (mvp.from_string) = "52"];

  PayinDuplicateDescription = 37 [(jusan_response_code_payin_value) = {
    code: "53"
    transaction_status: TransactionStatusFailed
    integration_error: TransactionDeclinedByAcquirer
    message: "Дублирование описания отмены"
  }, (mvp.from_string) = "53"];

  PayinRefundHandlerError = 38 [(jusan_response_code_payin_value) = {
    code: "F"
    transaction_status: TransactionStatusFailed
    integration_error: TransactionDeclinedByAcquirer
    message: "Ошибка при обработке возврата"
  }, (mvp.from_string) = "F"];

  PayinPaymentError = 39 [(jusan_response_code_payin_value) = {
    code: "E"
    transaction_status: TransactionStatusFailed
    integration_error: TransactionDeclinedByAcquirer
    message: "Ошибка при оплате"
  }, (mvp.from_string) = "E"];

  PayinPaymentExpired = 40 [(jusan_response_code_payin_value) = {
    code: "c"
    transaction_status: TransactionStatusFailed
    integration_error: TransactionDeclinedByAcquirer
    message: "Счет на оплату устарел"
  }, (mvp.from_string) = "c"];

  PayinAuthError = 42 [(jusan_response_code_payin_value) = {
    code: "3"
    transaction_status: TransactionStatusFailed
    integration_error: TransactionDeclinedByAcquirer
    message: "Ошибка авторизации платежа"
  }, (mvp.from_string) = "3"];

  PayinSuspectedFraud = 43 [(jusan_response_code_payin_value) = {
    code: "59"
    transaction_status: TransactionStatusFailed
    integration_error: SuspiciousClient
    message: "Suspected fraud"
  }, (mvp.from_string) = "59"];

  PayinErrorUnknown = 44 [(jusan_response_code_payin_value) = {
    code: "1"
    transaction_status: TransactionStatusFailed
    integration_error: UndefinedError
    message: "Неизвестная ошибка"
  }, (mvp.from_string) = "1"];

  PayinInsufficientFunds = 45 [(jusan_response_code_payin_value) = {
    code: "2"
    transaction_status: TransactionStatusFailed
    integration_error: InsufficientFunds
    message: "Не достаточно средств"
  }, (mvp.from_string) = "2"];

  PayinIncorrectCardNum = 46 [(jusan_response_code_payin_value) = {
    code: "37"
    transaction_status: TransactionStatusFailed
    integration_error: InvalidCard
    message: "Неправильный номер карты"
  }, (mvp.from_string) = "37"];

  PayinPaymentDeclined = 47 [(jusan_response_code_payin_value) = {
    code: "05"
    transaction_status: TransactionStatusFailed
    integration_error: TransactionDeclinedByAcquirer
    message: "Транзакция отклонена банком или МПС"
  }, (mvp.from_string) = "05"];

  PayinNotEnough = 49 [(jusan_response_code_payin_value) = {
    code: "51"
    transaction_status: TransactionStatusFailed
    integration_error: InsufficientFunds
    message: "Не достаточно средств"
  }, (mvp.from_string) = "51"];

  PayinExceedsLimit = 50 [(jusan_response_code_payin_value) = {
    code: "61"
    integration_error: ExceedsAmountLimit
    transaction_status: TransactionStatusFailed
    message: "Превышает лимит суммы"
  }, (mvp.from_string) = "61"];

  PayinLimitedCard = 51 [(jusan_response_code_payin_value) = {
    code: "62"
    integration_error: TransactionDeclinedByIssuer
    transaction_status: TransactionStatusFailed
    message: "Карта с ограниченным доступом"
  }, (mvp.from_string) = "62"];

  PayinLimitExceeds = 52 [(jusan_response_code_payin_value) = {
    code: "65"
    integration_error: ExceedsTransactionFrequencyLimit
    transaction_status: TransactionStatusFailed
    message: "Превышен лимит"
  }, (mvp.from_string) = "65"];

  PayinCardInactive = 53 [(jusan_response_code_payin_value) = {
    code: "07"
    transaction_status: TransactionStatusFailed
    integration_error: BlockedCard
    message: "Ваша карта не активна"
  }, (mvp.from_string) = "07"];

  PayinErrorAuth = 54 [(jusan_response_code_payin_value) = {
    code: "-19"
    transaction_status: TransactionStatusFailed
    integration_error: TransactionDeclinedByAcquirer
    message: "Ошибка авторизации"
  }, (mvp.from_string) = "-19"];

  PayinForbiddenClient = 55 [(jusan_response_code_payin_value) = {
    code: "57"
    integration_error: TransactionDeclinedByIssuer
    message: "Операция не разрешается клиенту"
    transaction_status: TransactionStatusFailed
  }, (mvp.from_string) = "57"];

  PayinCardExpired = 56 [(jusan_response_code_payin_value) = {
    code: "54"
    transaction_status: TransactionStatusFailed
    integration_error: CardHasExpired
    message: "Истекло время действия карты"
  }, (mvp.from_string) = "54"];

  PayinLawBreak = 57 [(jusan_response_code_payin_value) = {
    code: "93"
    integration_error: TransactionDeclinedByIssuer
    message: "Нарушение закона"
    transaction_status: TransactionStatusFailed
  }, (mvp.from_string) = "93"];

  PayinIssuerTimeout = 58 [(jusan_response_code_payin_value) = {
    code: "82"
    transaction_status: TransactionStatusFailed
    integration_error: UnavailableAcquirer
    message: "Тайм-аут при соединении с эмитентом"
  }, (mvp.from_string) = "82"];

  PayinIssuerLimit = 59 [(jusan_response_code_payin_value) = {
    code: "78"
    integration_error: TransactionDeclinedByIssuer
    message: "Ограничение на стороне Эмитента"
    transaction_status: TransactionStatusFailed
  }, (mvp.from_string) = "78"];

  PayinErrorRetry = 60 [(jusan_response_code_payin_value) = {
    code: "06"
    integration_error: UndefinedError
    message: "Ошибка - повторите"
    transaction_status: TransactionStatusError
  }, (mvp.from_string) = "06"];

  PayinIssuerUnavailable = 61 [(jusan_response_code_payin_value) = {
    code: "91"
    transaction_status: TransactionStatusFailed
    integration_error: UnavailableIssuer
    message: "Issuer unavailable"
  }, (mvp.from_string) = "91"];

  PayinPINTriesExceeded = 62 [(jusan_response_code_payin_value) = {
    code: "75"
    integration_error: TransactionDeclinedByAcquirer
    message: "PIN tries exceeded"
    transaction_status: TransactionStatusFailed
  }, (mvp.from_string) = "75"];

  PayinCallBank = 63 [(jusan_response_code_payin_value) = {
    code: "02"
    integration_error: TransactionDeclinedByIssuer
    message: "Call your bank"
    transaction_status: TransactionStatusFailed
  }, (mvp.from_string) = "02"];

  PayinSecurityViolation = 64 [(jusan_response_code_payin_value) = {
    code: "63"
    transaction_status: TransactionStatusFailed
    integration_error: SuspiciousClient
    message: "Security violation"
  }, (mvp.from_string) = "63"];

  PayinMerchantForbidden = 65 [(jusan_response_code_payin_value) = {
    code: "58"
    transaction_status: TransactionStatusFailed
    integration_error: PaymentForbiddenForMerchant
    message: "Not permitted to merchant"
  }, (mvp.from_string) = "58"];

  PayinInvalidMerchant = 66 [(jusan_response_code_payin_value) = {
    code: "03"
    integration_error: TransactionDeclinedByAcquirer
    message: "Invalid merchant"
    transaction_status: TransactionStatusFailed
  }, (mvp.from_string) = "03"];

  PayinTransactionPickupCard = 67 [(jusan_response_code_payin_value) = {
    code: "04"
    integration_error: TransactionDeclinedByAcquirer
    message: "Pickup card"
    transaction_status: TransactionStatusFailed
  }, (mvp.from_string) = "04"];
  PayinTransactionFunctionNotSupported = 68 [(jusan_response_code_payin_value) = {
    code: "40"
    integration_error: TransactionDeclinedByAcquirer
    message: "Function not supported Requested function not supported."
    transaction_status: TransactionStatusFailed
  }, (mvp.from_string) = "40"];
  PayinTransactionSystemMalfunction = 69 [(jusan_response_code_payin_value) = {
    code: "96"
    integration_error: TransactionDeclinedByAcquirer
    message: "System malfunction A system error has occurred."
    transaction_status: TransactionStatusFailed
  }, (mvp.from_string) = "96"];
}