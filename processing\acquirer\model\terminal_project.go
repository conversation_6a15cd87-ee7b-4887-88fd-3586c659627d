package model

const terminalProjectTableName = "acquirer.terminal_projects"

type TerminalProject struct {
	TimestampMixin
	ID                uint64 `gorm:"column:id" json:"id"`
	ProjectID         uint64 `gorm:"column:project_id" json:"project_id"`
	TransactionTypeID uint64 `gorm:"column:transaction_type_id" json:"transaction_type_id"`
	TerminalID        uint64 `gorm:"column:terminal_id" json:"terminal_id"`
	IsActive          bool   `gorm:"column:is_active;default:false" json:"is_active"`
}

type TerminalProjects []TerminalProject

func (t TerminalProject) TableName() string {
	return terminalProjectTableName
}

func (t TerminalProjects) TableName() string {
	return terminalProjectTableName
}

func (t TerminalProjects) GetTerminalIds() []uint64 {
	uniqueTerminals := make(map[uint64]bool)

	var terminals []uint64

	for _, element := range t {
		if !uniqueTerminals[element.TerminalID] {
			uniqueTerminals[element.TerminalID] = true

			terminals = append(terminals, element.TerminalID)
		}
	}

	return terminals
}
