// Code generated by protoc-gen-go-mvp. DO NOT EDIT.
// versions:
// - protoc-gen-go-mvp v2.0.1
// created by mvp authors

package grpc

import (
	dog "git.local/sensitive/sdk/dog"
	gin "github.com/gin-gonic/gin"
	metadata "google.golang.org/grpc/metadata"
	http "net/http"
)

func RegisterGinSplittingRouterGroup(
	in *gin.RouterGroup,
	handler func(func(c *gin.Context) error) gin.HandlerFunc,
) error {
	service, err := NewGinSplittingService()
	if err != nil {
		return err
	}

	routerGroup := in.Group("processing.splitting.splitting.Splitting")
	routerGroup.PUT("/CalculatePaymentSplitTax", handler(service.CalculatePaymentSplitTax))
	return nil
}

func NewGinSplittingService() (GinSplittingServer, error) {
	client, err := NewPreparedSplittingClient()
	if err != nil {
		return nil, err
	}

	return &ginSplittingServer{
		client: NewLoggedSplittingClient(
			NewIamSplittingClient(client),
		),
	}, nil
}

//go:generate mockgen -destination=./grpcmock/splitting.gin.pb.go -package=grpcmock -source=splitting.gin.pb.go GinSplittingServer
type GinSplittingServer interface {
	CalculatePaymentSplitTax(c *gin.Context) error
}

var _ GinSplittingServer = (*ginSplittingServer)(nil)

type ginSplittingServer struct {
	client SplittingClient
}

type Splitting_CalculatePaymentSplitTax_Success struct {
	// Status is the status of the response. Set true
	Status bool `json:"status"`
	// Message is a human-readable explanation of the status
	Message string `json:"message"`
	// StatusCode is the GoErr status. Set 0
	StatusCode int `json:"status_code"`
	// Result is the result of the request
	Result *CalculatePaymentSplitTaxResponse `json:"result"`
}

type Splitting_CalculatePaymentSplitTax_Failure struct {
	// Status is the status of the response. Set false
	Status bool `json:"status"`
	// Message is a human-readable explanation of the status
	Message string `json:"message"`
	// StatusCode is the GoErr status
	StatusCode int `json:"status_code"`
}

// CalculatePaymentSplitTax
// @Summary CalculatePaymentSplitTax
// @Security bearerAuth
// @ID Splitting_CalculatePaymentSplitTax
// @Accept json
// @Param request body CalculatePaymentSplitTaxRequest true "CalculatePaymentSplitTaxRequest"
// @Success 200 {object} Splitting_CalculatePaymentSplitTax_Success
// @Failure 401 {object} Splitting_CalculatePaymentSplitTax_Failure "goerrpkg.ErrorTypeAuthorization"
// @Failure 404 {object} Splitting_CalculatePaymentSplitTax_Failure "goerrpkg.ErrorTypeNotFound"
// @Failure 400 {object} Splitting_CalculatePaymentSplitTax_Failure "goerrpkg.ErrorTypeBadRequest"
// @Failure 500 {object} Splitting_CalculatePaymentSplitTax_Failure "goerrpkg.ErrorTypeUnexpectedError"
// @Failure 400 {object} Splitting_CalculatePaymentSplitTax_Failure "gin.ErrorTypeBind"
// @Failure 500 {object} Splitting_CalculatePaymentSplitTax_Failure "Undefined error"
// @Produce json
// @Router /processing.splitting.splitting.Splitting/CalculatePaymentSplitTax [put]
// @tags Splitting
func (s *ginSplittingServer) CalculatePaymentSplitTax(c *gin.Context) error {
	ctx, span := dog.CreateSpan(c.Request.Context(), "GinSplittingServer_CalculatePaymentSplitTax")
	defer span.End()

	var request CalculatePaymentSplitTaxRequest
	if err := c.ShouldBindJSON(&request); err != nil {
		return err
	}

	ctx = metadata.NewOutgoingContext(ctx, metadata.MD(c.Request.Header))

	response, err := s.client.CalculatePaymentSplitTax(ctx, &request)
	if err != nil {
		return err
	}

	c.JSON(http.StatusOK, &Splitting_CalculatePaymentSplitTax_Success{
		Status:     true,
		Message:    "Success",
		StatusCode: 0,
		Result:     response,
	})
	return nil
}
