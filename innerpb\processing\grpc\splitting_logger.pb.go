// Code generated by protoc-gen-go-mvp. DO NOT EDIT.
// versions:
// - protoc-gen-go-mvp v2.0.1
// created by mvp authors

package grpc

import (
	context "context"
	cntx "git.local/sensitive/mvp/pkg/cntx"
	ctxzap "github.com/grpc-ecosystem/go-grpc-middleware/logging/zap/ctxzap"
	zap "go.uber.org/zap"
	grpc "google.golang.org/grpc"
)

func file_inner_processing_grpc_splitting_proto_message_CommissionV1ToZap(
	label string,
	in *CommissionV1,
) zap.Field {
	if in == nil {
		return zap.Reflect(label, in)
	}

	return zap.Dict(
		label,
		zap.Any("TaxProjectId", in.GetTaxProjectId()),
		zap.Any("Amount", in.GetAmount()),
	)
}

func file_inner_processing_grpc_splitting_proto_message_CalculatePaymentSplitTaxRequestToZap(
	label string,
	in *CalculatePaymentSplitTaxRequest,
) zap.Field {
	if in == nil {
		return zap.Reflect(label, in)
	}

	return zap.Dict(
		label,
		zap.Any("ProjectId", in.GetProjectId()),
		zap.Any("Amount", in.GetAmount()),
	)
}

func file_inner_processing_grpc_splitting_proto_message_CalculatePaymentSplitTaxResponseToZap(
	label string,
	in *CalculatePaymentSplitTaxResponse,
) zap.Field {
	if in == nil {
		return zap.Reflect(label, in)
	}

	return zap.Dict(
		label,
		file_inner_processing_grpc_splitting_proto_message_CommissionV1ToZap("PaymentSplitTax", in.GetPaymentSplitTax()),
	)
}

var _ SplittingServer = (*loggedSplittingServer)(nil)

func NewLoggedSplittingServer(srv SplittingServer) SplittingServer {
	return &loggedSplittingServer{srv: srv}
}

type loggedSplittingServer struct {
	UnimplementedSplittingServer

	srv SplittingServer
}

func (s *loggedSplittingServer) CalculatePaymentSplitTax(
	ctx context.Context,
	request *CalculatePaymentSplitTaxRequest,
) (
	response *CalculatePaymentSplitTaxResponse,
	err error,
) {
	label := cntx.Begin(ctx, "SplittingServer_CalculatePaymentSplitTax")
	defer cntx.End(ctx, label)

	defer func() {
		if err != nil {
			ctxzap.AddFields(ctx, zap.Any(label+"error", err))
			return
		}

		ctxzap.AddFields(
			ctx,
			file_inner_processing_grpc_splitting_proto_message_CalculatePaymentSplitTaxResponseToZap(label+"response", response),
		)
	}()

	ctxzap.AddFields(
		ctx,
		file_inner_processing_grpc_splitting_proto_message_CalculatePaymentSplitTaxRequestToZap(label+"request", request),
	)

	response, err = s.srv.CalculatePaymentSplitTax(ctx, request)

	return
}

var _ SplittingClient = (*loggedSplittingClient)(nil)

func NewLoggedSplittingClient(client SplittingClient) SplittingClient {
	return &loggedSplittingClient{client: client}
}

type loggedSplittingClient struct {
	client SplittingClient
}

func (s *loggedSplittingClient) CalculatePaymentSplitTax(
	ctx context.Context,
	request *CalculatePaymentSplitTaxRequest,
	opts ...grpc.CallOption,
) (
	response *CalculatePaymentSplitTaxResponse,
	err error,
) {
	label := cntx.Begin(ctx, "SplittingClient_CalculatePaymentSplitTax")
	defer cntx.End(ctx, label)

	defer func() {
		if err != nil {
			ctxzap.AddFields(ctx, zap.Any(label+"error", err))
			return
		}

		ctxzap.AddFields(
			ctx,
			file_inner_processing_grpc_splitting_proto_message_CalculatePaymentSplitTaxResponseToZap(label+"response", response),
		)
	}()

	ctxzap.AddFields(
		ctx,
		file_inner_processing_grpc_splitting_proto_message_CalculatePaymentSplitTaxRequestToZap(label+"request", request),
	)

	response, err = s.client.CalculatePaymentSplitTax(ctx, request, opts...)

	return
}
