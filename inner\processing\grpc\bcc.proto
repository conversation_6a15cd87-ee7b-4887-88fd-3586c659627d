edition = "2023";

package processing.bcc.bcc;

option go_package = "git.local/sensitive/innerpb/processing/grpc";

import "inner/processing/grpc/multiacquiring.proto";
import "google/protobuf/empty.proto";
import "mvp/proto/refs.proto";
import "mvp/proto/enum.proto";
import "google/protobuf/descriptor.proto";
import "inner/processing/grpc/transaction_status.proto";
import "inner/processing/grpc/integration.proto";

service BCC {
  rpc PayIn(processing.multiacquiring.multiacquiring.PayInRequestData) returns (processing.multiacquiring.multiacquiring.PayInResponseData) {}
  rpc OneClickPayIn(processing.multiacquiring.multiacquiring.OneClickPayInRequestData) returns (processing.multiacquiring.multiacquiring.PayInResponseData) {}
  rpc ThreeDSConfirm(processing.multiacquiring.multiacquiring.ThreeDSRequestData) returns (processing.multiacquiring.multiacquiring.ThreeDSResponseData) {}
  rpc ThreeDSResume(processing.multiacquiring.multiacquiring.ThreeDSResumeRequest) returns (processing.multiacquiring.multiacquiring.ThreeDSResumeResponse) {}
  rpc PayOut(processing.multiacquiring.multiacquiring.PayOutRequestData) returns (processing.multiacquiring.multiacquiring.PayOutResponseData) {}
  rpc GetBankTransactionStatus(processing.multiacquiring.multiacquiring.BankTransactionStatusRequest) returns (processing.multiacquiring.multiacquiring.BankTransactionStatusResponse) {}
  rpc GetBankTransactionStatusUnformated(processing.multiacquiring.multiacquiring.BankTransactionStatusUnformatedRequest) returns (processing.multiacquiring.multiacquiring.BankTransactionStatusUnformatedResponse) {}
  rpc Refund(processing.multiacquiring.multiacquiring.RefundRequest) returns (processing.multiacquiring.multiacquiring.RefundResponse) {}
  rpc GooglePay(processing.multiacquiring.multiacquiring.GooglePayRequestData) returns (processing.multiacquiring.multiacquiring.GooglePayResponseData) {}
  rpc ApplePay(processing.multiacquiring.multiacquiring.ApplePayRequestData) returns (processing.multiacquiring.multiacquiring.ApplePayResponseData) {}
  rpc TwoStagePayIn(processing.multiacquiring.multiacquiring.TwoStagePayInRequest) returns (processing.multiacquiring.multiacquiring.TwoStagePayInResponse) {}
  rpc Charge(processing.multiacquiring.multiacquiring.ChargeRequest) returns (processing.multiacquiring.multiacquiring.ChargeResponse) {}
  rpc Cancel(processing.multiacquiring.multiacquiring.CancelRequest) returns (processing.multiacquiring.multiacquiring.CancelResponse) {}
  rpc MakeToken(processing.multiacquiring.multiacquiring.PayInRequestData) returns (processing.multiacquiring.multiacquiring.PayInResponseData) {}
  rpc GetAcquirerIdentifier(google.protobuf.Empty) returns (processing.multiacquiring.multiacquiring.GetAcquirerIdentifierResponse) {}
  rpc ResolveVisaAlias(processing.multiacquiring.multiacquiring.ResolveVisaAliasRequest) returns (processing.multiacquiring.multiacquiring.ResolveVisaAliasResponse) {}
  rpc PayOutByPhone(processing.multiacquiring.multiacquiring.PayOutByPhoneRequestData) returns (processing.multiacquiring.multiacquiring.PayOutResponseByPhoneData) {}
}

extend google.protobuf.EnumValueOptions {
  ResponseCodePayInRef response_code_pay_in_value = 100152;
}

extend google.protobuf.EnumOptions {
  ResponseCodePayInRef default_response_code_pay_in_value = 100153;
}

message ResponseCodePayInRef {
  string description = 1;
  processing.transaction.transaction_status.EnumTransactionStatus transaction_status = 2;
}

enum ResponseCodePayIn {
  option(mvp.default_ref) = "default_response_code_pay_in_value";
  option(mvp.ref) = "response_code_pay_in_value";
  option(default_response_code_pay_in_value) = {
    description: "default",
    transaction_status: TransactionStatusHolded
  };
  ResponseCodePayInSuccess = 0 [(response_code_pay_in_value) = {
    description: "Транзакция успешно завершена.",
    transaction_status: TransactionStatusSuccess
  }, (mvp.from_string) = "0"];

  ResponseCodePayInErrorProcessing = 1 [(response_code_pay_in_value) = {
    description: "Ошибка обработки транзакции.",
    transaction_status: TransactionStatusError
  }, (mvp.from_string) = "3"];

  ResponseCodePayIn3DSWaiting = 2 [(response_code_pay_in_value) = {
    description: "Проверка держателя карты по OTP-коду.",
    transaction_status: TransactionStatusFingerPrint
  }, (mvp.from_string) = "10"];

  ResponseCodePayInRequestCardForm = 3 [(response_code_pay_in_value) = {
    description: "Запрос формы ввода данных карты пользователя.",
    transaction_status: TransactionStatusThreeDSWaiting
  }, (mvp.from_string) = "17"];

  ResponseCodePayInVeResU = 4 [(response_code_pay_in_value) = {
    description: "Сообщение VeRes или PaRes со статусом 'U'.",
    transaction_status: TransactionStatusFailed
  }, (mvp.from_string) = "18"];

  ResponseCodePayInVeResA = 5 [(response_code_pay_in_value) = {
    description: "Сообщение VeRes или PaRes со статусом 'A'.",
    transaction_status: TransactionStatusThreeDSReceived
  }, (mvp.from_string) = "19"];

  ResponseCodePayInRequestFingerprint = 6 [(response_code_pay_in_value) = {
    description: "Запрос параметров fingerprint в рамках 3D-Secure.",
    transaction_status: TransactionStatusFingerPrint
  }, (mvp.from_string) = "22"];

  ResponseCodePayIn3DSResponse = 7 [(response_code_pay_in_value) = {
    description: "Ответ 3D-Secure со стороны эмитента.",
    transaction_status: TransactionStatusThreeDSReceived
  }, (mvp.from_string) = "5"];

  ResponseCodePayInMastercardInstallment = 8 [(response_code_pay_in_value) = {
    description: "Автоматическая полная оплата в рамках Mastercard installment.",
    transaction_status: TransactionStatusRefundWaiting
  }, (mvp.from_string) = "13"];

  ResponseCodePayInSCA = 9 [(response_code_pay_in_value) = {
    description: "Требования Strong Customer Authentication.",
    transaction_status: TransactionStatusThreeDSReceived
  }, (mvp.from_string) = "21"];

  ResponseCodePayInRepeatAuthError = 10 [(response_code_pay_in_value) = {
    description: "Повтор транзакции с ошибкой аутентификации.",
    transaction_status: TransactionStatusCanceled
  }, (mvp.from_string) = "7"];

  ResponseCodePayInRepeatDeclined = 11 [(response_code_pay_in_value) = {
    description: "Повтор транзакции, которая была отклонена.",
    transaction_status: TransactionStatusRefund
  }, (mvp.from_string) = "6"];

  ResponseCodePayInDeclined = 12 [(response_code_pay_in_value) = {
    description: "Транзакция отклонена.",
    transaction_status: TransactionStatusFailed
  }, (mvp.from_string) = "2"];

  ResponseCodePayInInfoMessage = 13 [(response_code_pay_in_value) = {
    description: "Информационное сообщение.",
    transaction_status: TransactionStatusProcessed
  }, (mvp.from_string) = "4"];

  ResponseCodePayInCheckCardBySum = 14 [(response_code_pay_in_value) = {
    description: "Проверка держателя карты путем генерации случайной суммы.",
    transaction_status: TransactionStatusFingerPrint
  }, (mvp.from_string) = "11"];

  ResponseCodePayInUPIRequest = 15 [(response_code_pay_in_value) = {
    description: "Запрос UPI.",
    transaction_status: TransactionStatusFailed
  }, (mvp.from_string) = "15"];

  ResponseCodePayInRepeatTransaction = 16 [(response_code_pay_in_value) = {
    description: "Обнаружена повторная транзакция.",
    transaction_status: TransactionStatusNew
  }, (mvp.from_string) = "1"];

  ResponseCodePayInNoResponse = 17 [(response_code_pay_in_value) = {
    description: "Повтор транзакции, завершившейся без ответа.",
    transaction_status: TransactionStatusHolded
  }, (mvp.from_string) = "8"];

  ResponseCodePayInInstallmentAutoCancel = 18 [(response_code_pay_in_value) = {
    description: "Автоматическая отмена транзакции Mastercard installment.",
    transaction_status: TransactionStatusCanceled
  }, (mvp.from_string) = "12"];

  ResponseCodePayInMerchantSumCheck = 19 [(response_code_pay_in_value) = {
    description: "Проверка держателя карты по запросу торговцем генерации случайной суммы.",
    transaction_status: TransactionStatusFingerPrint
  }, (mvp.from_string) = "20"];

  ResponseCodePayInInstallment = 20 [(response_code_pay_in_value) = {
    description: "Транзакция Mastercard installment.",
    transaction_status: TransactionStatusAuthorized
  }, (mvp.from_string) = "9"];

  ResponseCodePayInInstallmentCancel = 21 [(response_code_pay_in_value) = {
    description: "Отмена пользователем транзакции Mastercard installment.",
    transaction_status: TransactionStatusCanceled
  }, (mvp.from_string) = "14"];

  ResponseCodePayInUserConfirmation = 22 [(response_code_pay_in_value) = {
    description: "Запрос подтверждения пользователя.",
    transaction_status: TransactionStatusFingerPrint
  }, (mvp.from_string) = "16"];
}

extend google.protobuf.EnumValueOptions {
  ResponseCodePayOutRef response_code_pay_out_value = 100154;
}

extend google.protobuf.EnumOptions {
  ResponseCodePayOutRef default_response_code_pay_out_value = 100155;
}

message ResponseCodePayOutRef {
  processing.transaction.transaction_status.EnumTransactionStatus transaction_status = 1;
}

enum ResponseCodePayOut {
  option(mvp.default_ref) = "default_response_code_pay_out_value";
  option(mvp.ref) = "response_code_pay_out_value";
  option(default_response_code_pay_out_value) = {
    transaction_status: TransactionStatusHolded
  };
  ResponseCodePayOutBadRequest = 0 [(response_code_pay_out_value) = {
    transaction_status: TransactionStatusFailed
  }, (mvp.from_string) = "400:"];
  ResponseCodePayOutUnauthorized = 1 [(response_code_pay_out_value) = {
    transaction_status: TransactionStatusFailed
  }, (mvp.from_string) = "401:"];
  ResponseCodePayOutTooManyRequests = 2 [(response_code_pay_out_value) = {
    transaction_status: TransactionStatusFailed
  }, (mvp.from_string) = "429:"];
  ResponseCodePayOutInternalServerError = 3 [(response_code_pay_out_value) = {
    transaction_status: TransactionStatusFailed
  }, (mvp.from_string) = "500:"];
  ResponseCodePayOutNotImplemented = 4 [(response_code_pay_out_value) = {
    transaction_status: TransactionStatusFailed
  }, (mvp.from_string) = "501:"];
  ResponseCodePayOutServiceUnavailable = 5 [(response_code_pay_out_value) = {
    transaction_status: TransactionStatusFailed
  }, (mvp.from_string) = "503:"];
  ResponseCodePayOutDeclined = 6 [(response_code_pay_out_value) = {
    transaction_status: TransactionStatusFailed
  }, (mvp.from_string) = "200:Declined"];
  ResponseCodePayOutRejected = 7 [(response_code_pay_out_value) = {
    transaction_status: TransactionStatusFailed
  }, (mvp.from_string) = "200:Rejected"];
  ResponseCodePayOutTeapot = 8 [(response_code_pay_out_value) = {
    transaction_status: TransactionStatusFailed
  }, (mvp.from_string) = "418:"];
  ResponseCodePayOutActive = 9 [(response_code_pay_out_value) = {
    transaction_status: TransactionStatusHolded
  }, (mvp.from_string) = "200:Active"];
  ResponseCodePayOutWaiting = 10 [(response_code_pay_out_value) = {
    transaction_status: TransactionStatusSuccess
  }, (mvp.from_string) = "200:Waiting"];
  ResponseCodePayOutLoaded = 11 [(response_code_pay_out_value) = {
    transaction_status: TransactionStatusSuccess
  }, (mvp.from_string) = "200:Loaded"];
  ResponseCodePayOutSuspended = 12 [(response_code_pay_out_value) = {
    transaction_status: TransactionStatusHolded
  }, (mvp.from_string) = "200:Suspended"];
  ResponseCodePayOutRetry = 13 [(response_code_pay_out_value) = {
    transaction_status: TransactionStatusHolded
  }, (mvp.from_string) = "200:Retry"];
  ResponseCodePayOutRejectOut = 14 [(response_code_pay_out_value) = {
    transaction_status: TransactionStatusFailed
  }, (mvp.from_string) = "200:RejectOut"];
  ResponseCodePayOutRejectSent = 15 [(response_code_pay_out_value) = {
    transaction_status: TransactionStatusFailed
  }, (mvp.from_string) = "200:RejectSent"];
  ResponseCodePayOutCompleted = 16 [(response_code_pay_out_value) = {
    transaction_status: TransactionStatusSuccess
  }, (mvp.from_string) = "200:Completed"];
}

extend google.protobuf.EnumValueOptions {
  IntegrationErrorMapRef integration_error_map_value = 100156;
}

extend google.protobuf.EnumOptions {
  IntegrationErrorMapRef default_integration_error_map_value = 100157;
}

message IntegrationErrorMapRef {
  string iso_message = 1;
  string description = 2;
  processing.integration.integration.IntegrationError integration_error_id = 3;
}

enum IntegrationErrorMap {
  option(mvp.default_ref) = "default_integration_error_map_value";
  option(mvp.ref) = "integration_error_map_value";
  option(default_integration_error_map_value) = {
    iso_message: "None",
    description: "No error",
    integration_error_id: None
  };
  IntegrationErrorMapNone = 0 [(integration_error_map_value) = {
    iso_message: "VIP Approval",
    description: "",
    integration_error_id: None
  }, (mvp.from_string) = "11"];

  IntegrationErrorMapInvalidTransaction = 1 [(integration_error_map_value) = {
    iso_message: "Invalid transaction",
    description: "The bank has declined the transaction because of an invalid format or field. This indicates the card details were incorrect. Check card data entered and try again.",
    integration_error_id: InvalidCard
  }, (mvp.from_string) = "12"];

  IntegrationErrorMapInvalidMerchant = 2 [(integration_error_map_value) = {
    iso_message: "Invalid merchant",
    description: "This error indicates that either your merchant facility is non-functional or the details entered into eWAY are incorrect.",
    integration_error_id: PaymentForbiddenForMerchant
  }, (mvp.from_string) = "03"];

  IntegrationErrorMapCardNotExist = 3 [(integration_error_map_value) = {
    iso_message: "Card number does not exist",
    description: "The card issuing bank has declined the transaction as the credit card number is incorrectly entered, or does not exist. Check card details and try processing again.",
    integration_error_id: NonExistentCard
  }, (mvp.from_string) = "14"];

  IntegrationErrorMapNotSupportedByReceiver = 4 [(integration_error_map_value) = {
    iso_message: "File update not supported by receiverr",
    description: "",
    integration_error_id: UndefinedError
  }, (mvp.from_string) = "24"];

  IntegrationErrorMapFileUpdateNotSuccess = 5 [(integration_error_map_value) = {
    iso_message: "File update not successful",
    description: "",
    integration_error_id: UndefinedError
  }, (mvp.from_string) = "29"];

  IntegrationErrorMapNotSufficient = 6 [(integration_error_map_value) = {
    iso_message: "Not sufficient funds",
    description: "The customer’s card issuer has declined the transaction as the credit card does not have sufficient funds.",
    integration_error_id: InsufficientFunds
  }, (mvp.from_string) = "51"];

  IntegrationErrorMapNotPermitted = 7 [(integration_error_map_value) = {
    iso_message: "Transaction not permitted to card",
    description: "The customer’s card issuer has declined the transaction as this credit card cannot be used for this type of transaction.",
    integration_error_id: TransactionDeclinedByAcquirer
  }, (mvp.from_string) = "57"];

  IntegrationErrorMapInvalidDate = 8 [(integration_error_map_value) = {
    iso_message: "Invalid date",
    description: "",
    integration_error_id: UndefinedError
  }, (mvp.from_string) = "80"];

  IntegrationErrorMapReconcileError = 9 [(integration_error_map_value) = {
    iso_message: "Reconcile error",
    description: "",
    integration_error_id: UndefinedError
  }, (mvp.from_string) = "95"];

  IntegrationErrorMapForwardToIssuer = 10 [(integration_error_map_value) = {
    iso_message: "Forward to issuer",
    description: "",
    integration_error_id: UndefinedError
  }, (mvp.from_string) = "XD"];

  IntegrationErrorMapInvalidAmount = 11 [(integration_error_map_value) = {
    iso_message: "Invalid amount",
    description: "The customer’s card issuer has declined the transaction because of an invalid format or field. Check the transaction information and try processing the transaction again.",
    integration_error_id: TransactionDeclinedByIssuer
  }, (mvp.from_string) = "13"];

  IntegrationErrorMapReenterTransaction = 12 [(integration_error_map_value) = {
    iso_message: "Re-enter transaction",
    description: "The transaction has not been processed and the customer should attempt to process the transaction again. No further information is provided from the bank as to the reason why this was not processed.",
    integration_error_id: UndefinedError
  }, (mvp.from_string) = "19"];

  IntegrationErrorMapNoAction = 13 [(integration_error_map_value) = {
    iso_message: "No action taken (no match)",
    description: "The customer’s card issuer has indicated there is a problem with the credit card number. The customer should use an alternate credit card, or contact their bank.",
    integration_error_id: TransactionDeclinedByIssuer
  }, (mvp.from_string) = "21"];

  IntegrationErrorMapFormatError = 14 [(integration_error_map_value) = {
    iso_message: "Format error",
    description: "The customer’s card issuer does not recognise the transaction details being entered. This is due to a format error. The customer should check the transaction information and try processing the transaction again.",
    integration_error_id: TransactionDeclinedByIssuer
  }, (mvp.from_string) = "30"];

  IntegrationErrorMapIssuerSignOff = 15 [(integration_error_map_value) = {
    iso_message: "Issuer sign-off",
    description: "The customer’s card issuer has declined the transaction as it does not allow transactions originating through mail/telephone, fax, email or Internet orders.",
    integration_error_id: TransactionDeclinedByIssuer
  }, (mvp.from_string) = "31"];

  IntegrationErrorMapCompletedPartially = 16 [(integration_error_map_value) = {
    iso_message: "Completed partially",
    description: "",
    integration_error_id: None
  }, (mvp.from_string) = "32"];

  IntegrationErrorMapNoCardRecord = 17 [(integration_error_map_value) = {
    iso_message: "No card record",
    description: "The customer’s card issuer has declined the transaction as the credit card number does not exist.",
    integration_error_id: IncorrectCardNumber
  }, (mvp.from_string) = "56"];

  IntegrationErrorMapTransactionNotPermitted = 18 [(integration_error_map_value) = {
    iso_message: "Transaction not permitted to card",
    description: "The customer’s card issuer has declined the transaction as this credit card cannot be used for this type of transaction. This may be associated with a test credit card number.",
    integration_error_id: InvalidCard
  }, (mvp.from_string) = "58"];

  IntegrationErrorMapAmountIncorrect = 19 [(integration_error_map_value) = {
    iso_message: "Original amount incorrect",
    description: "The customer’s card issuer has declined the transaction due to the amount attempting to be processed.",
    integration_error_id: TransactionDeclinedByIssuer
  }, (mvp.from_string) = "64"];

  IntegrationErrorMapAlreadyReversed = 20 [(integration_error_map_value) = {
    iso_message: "Already reversed (by Switch)",
    description: "",
    integration_error_id: UndefinedError
  }, (mvp.from_string) = "79"];

  IntegrationErrorMapNoRoutingPath = 21 [(integration_error_map_value) = {
    iso_message: "No routing path",
    description: "The customer’s card issuer cannot be found for routing. This response code is often returned when the customer is using a test credit card number.",
    integration_error_id: TransactionDeclinedByIssuer
  }, (mvp.from_string) = "92"];

  IntegrationErrorMapSystemMalfunction = 22 [(integration_error_map_value) = {
    iso_message: "System malfunction",
    description: "The customer’s card issuer was not able to process the transaction. The customer should attempt to process this transaction again.",
    integration_error_id: TransactionDeclinedByIssuer
  }, (mvp.from_string) = "96"];

  IntegrationErrorMapSoftDecline = 23 [(integration_error_map_value) = {
    iso_message: "Soft-Decline",
    description: "",
    integration_error_id: UndefinedError
  }, (mvp.from_string) = "A1"];

  IntegrationErrorMapReferToIssuer = 24 [(integration_error_map_value) = {
    iso_message: "Refer to issuer",
    description: "The customer’s card issuer has indicated there is a problem with the credentials used in the transaction. The customer should use an alternate credit card, or contact their bank.",
    integration_error_id: InvalidCard
  }, (mvp.from_string) = "01"];

  IntegrationErrorMapReferToIssuerSpecial = 25 [(integration_error_map_value) = {
    iso_message: "Refer to issuer (special)",
    description: "The customer’s card issuer has indicated there is a problem with the card number. The customer should use an alternate credit card, or contact their bank.",
    integration_error_id: InvalidCard
  }, (mvp.from_string) = "02"];

  IntegrationErrorMapError = 26 [(integration_error_map_value) = {
    iso_message: "Error",
    description: "The customer’s card issuer has declined the transaction as there is a problem with the card number. The customer should contact their card issuer and/or use an alternate card.",
    integration_error_id: UndefinedError
  }, (mvp.from_string) = "06"];

  IntegrationErrorMapApprovedPartialAmount = 27 [(integration_error_map_value) = {
    iso_message: "Approved for partial amount",
    description: "The transaction was successful.",
    integration_error_id: None
  }, (mvp.from_string) = "10"];

  IntegrationErrorMapNoSuchIssuer = 28 [(integration_error_map_value) = {
    iso_message: "No such issuer",
    description: "The customer’s card issuer does not exist. Check the card information and try processing the transaction again.",
    integration_error_id: UnavailableIssuer
  }, (mvp.from_string) = "15"];

  IntegrationErrorMapCustomerCancellation = 29 [(integration_error_map_value) = {
    iso_message: "Customer cancellation",
    description: "",
    integration_error_id: UndefinedError
  }, (mvp.from_string) = "17"];

  IntegrationErrorMapCustomerDispute = 30 [(integration_error_map_value) = {
    iso_message: "Customer dispute",
    description: "",
    integration_error_id: UndefinedError
  }, (mvp.from_string) = "18"];

  IntegrationErrorMapNoInvestmentAccount = 31 [(integration_error_map_value) = {
    iso_message: "No investment account",
    description: "The customer’s card issuer has declined the transaction as the account type selected is not valid for this credit card number.",
    integration_error_id: InvalidCard
  }, (mvp.from_string) = "44"];

  IntegrationErrorMapCardAcceptorCallAcquirer = 32 [(integration_error_map_value) = {
    iso_message: "Card acceptor call acquirer",
    description: "The customer should use an alternative credit card. Neither eWAY nor the bank can provide more details.",
    integration_error_id: InvalidCard
  }, (mvp.from_string) = "66"];

  IntegrationErrorMapUnableToVerifyPIN = 33 [(integration_error_map_value) = {
    iso_message: "Unable to verify PIN",
    description: "",
    integration_error_id: IncorrectCVVCVC
  }, (mvp.from_string) = "83"];

  IntegrationErrorMapAuthenticationFailure = 34 [(integration_error_map_value) = {
    iso_message: "Authentication failure",
    description: "",
    integration_error_id: UndefinedError
  }, (mvp.from_string) = "89"];

  IntegrationErrorMapIssuerSwitchInoperative = 35 [(integration_error_map_value) = {
    iso_message: "Issuer or switch inoperative",
    description: "The customer’s card issuer is unable to be contacted to authorise the transaction. The customer should attempt to process this transaction again.",
    integration_error_id: TransactionDeclinedByIssuer
  }, (mvp.from_string) = "91"];

  IntegrationErrorMapInvalidResponse = 36 [(integration_error_map_value) = {
    iso_message: "Invalid response",
    description: "",
    integration_error_id: UndefinedError
  }, (mvp.from_string) = "20"];

  IntegrationErrorMapCardAcceptorContactAcquirer = 37 [(integration_error_map_value) = {
    iso_message: "Card acceptor contact acquirer",
    description: "The customer’s card issuer has declined the transaction and requested that your customer’s card be retained as the card was reported as lost or stolen.",
    integration_error_id: LostCard
  }, (mvp.from_string) = "35"];

  IntegrationErrorMapFunctionNotSupported = 38 [(integration_error_map_value) = {
    iso_message: "Function not supported",
    description: "The customer’s card issuer has declined the transaction as it does not allow this type of transaction.",
    integration_error_id: InvalidCard
  }, (mvp.from_string) = "40"];

  IntegrationErrorMapNoCheckingAccount = 39 [(integration_error_map_value) = {
    iso_message: "No checking account",
    description: "The customer’s card issuer has declined the transaction as the credit card number is associated to a cheque account that does not exist.",
    integration_error_id: InvalidCard
  }, (mvp.from_string) = "52"];

  IntegrationErrorMapPreviousMessageNotFound = 40 [(integration_error_map_value) = {
    iso_message: "Previous message not found",
    description: "",
    integration_error_id: UndefinedError
  }, (mvp.from_string) = "76"];

  IntegrationErrorMapCryptographicErrorInPIN = 41 [(integration_error_map_value) = {
    iso_message: "Cryptographic error in PIN",
    description: "",
    integration_error_id: IncorrectCVVCVC
  }, (mvp.from_string) = "81"];

  IntegrationErrorMapPINValidationNotPossible = 42 [(integration_error_map_value) = {
    iso_message: "PIN validation not possible",
    description: "",
    integration_error_id: UndefinedError
  }, (mvp.from_string) = "86"];

  IntegrationErrorMapCutoffInProcess = 43 [(integration_error_map_value) = {
    iso_message: "Cutoff is in process",
    description: "The customer’s card issuer is temporarily not able to process this customer’s credit card.",
    integration_error_id: TransactionDeclinedByIssuer
  }, (mvp.from_string) = "90"];

  IntegrationErrorMapSurchargeNotSupported = 44 [(integration_error_map_value) = {
    iso_message: "Surcharge amount not supported by deb",
    description: "",
    integration_error_id: UndefinedError
  }, (mvp.from_string) = "B2"];

  IntegrationErrorMapForceSTIP = 45 [(integration_error_map_value) = {
    iso_message: "Force STIP",
    description: "",
    integration_error_id: UndefinedError
  }, (mvp.from_string) = "N0"];

  IntegrationErrorMapExpiredCard = 46 [(integration_error_map_value) = {
    iso_message: "Expired card",
    description: "The customer’s card issuer has declined the transaction as Card has expired or the date is incorrect. Check the expiry date in the transaction and try processing the transaction again.",
    integration_error_id: CardHasExpired
  }, (mvp.from_string) = "33"];

  IntegrationErrorMapSuspectedFraud = 47 [(integration_error_map_value) = {
    iso_message: "Suspected fraud",
    description: "The customer’s card issuer has declined the transaction as there is a suspected fraud on this credit card number.",
    integration_error_id: SuspiciousClient
  }, (mvp.from_string) = "34"];

  IntegrationErrorMapNoCreditAccount = 48 [(integration_error_map_value) = {
    iso_message: "No credit account",
    description: "The customer’s card issuer has declined the transaction as the Credit Card number used is not a credit account.",
    integration_error_id: InvalidCard
  }, (mvp.from_string) = "39"];

  IntegrationErrorMapNoUniversalAccount = 49 [(integration_error_map_value) = {
    iso_message: "No universal account",
    description: "The customer’s card issuer has declined the transaction as the account type selected is not valid for this credit card number.",
    integration_error_id: InvalidCard
  }, (mvp.from_string) = "42"];

  IntegrationErrorMapExpiredCardAlternate = 50 [(integration_error_map_value) = {
    iso_message: "Expired card",
    description: "The customer’s card is expired. Contact your customer and confirm that the correct dates were entered and that there were no mistakes (e.g. 05/21 rather than 05/20).",
    integration_error_id: CardHasExpired
  }, (mvp.from_string) = "54"];

  IntegrationErrorMapSuspectedFraudAlternate = 51 [(integration_error_map_value) = {
    iso_message: "Suspected fraud",
    description: "The customer’s card issuer has declined this transaction as the credit card appears to be fraudulent. While you could contact this customer yourself, it's very possible that this transaction is fraudulent.",
    integration_error_id: SuspiciousClient
  }, (mvp.from_string) = "59"];

  IntegrationErrorMapCardAcceptorWriteToAcquirer = 52 [(integration_error_map_value) = {
    iso_message: "Card acceptor contact acquirer",
    description: "The customer’s card issuer has declined the transaction. The customer should contact their bank and retry the transaction.",
    integration_error_id: UndefinedError
  }, (mvp.from_string) = "60"];

  IntegrationErrorMapSecurityViolation = 53 [(integration_error_map_value) = {
    iso_message: "Security violation",
    description: "The customer’s card issuer has declined the transaction. The customer should use an alternate credit card, and contact their bank.",
    integration_error_id: TransactionDeclinedByIssuer
  }, (mvp.from_string) = "63"];

  IntegrationErrorMapCryptographicFailure = 54 [(integration_error_map_value) = {
    iso_message: "Cryptographic failure",
    description: "",
    integration_error_id: UndefinedError
  }, (mvp.from_string) = "88"];

  IntegrationErrorMapViolationOfLaw = 55 [(integration_error_map_value) = {
    iso_message: "Violation of law",
    description: "The customer’s card issuer has declined the transaction and request the customer to contact their bank.",
    integration_error_id: TransactionDeclinedByIssuer
  }, (mvp.from_string) = "93"];

  IntegrationErrorMapFraud = 56 [(integration_error_map_value) = {
    iso_message: "Fraud",
    description: "",
    integration_error_id: SuspiciousClient
  }, (mvp.from_string) = "B8"];

  IntegrationErrorMapCashRequestExceedsLimit = 57 [(integration_error_map_value) = {
    iso_message: "Cash request exceeds issuer or approv",
    description: "",
    integration_error_id: UndefinedError
  }, (mvp.from_string) = "N4"];

  IntegrationErrorMapIneligibleForResubmission = 58 [(integration_error_map_value) = {
    iso_message: "Ineligible for resubmission",
    description: "",
    integration_error_id: UndefinedError
  }, (mvp.from_string) = "N5"];

  IntegrationErrorMapStopPaymentOrder = 59 [(integration_error_map_value) = {
    iso_message: "Stop Payment Order",
    description: "",
    integration_error_id: UndefinedError
  }, (mvp.from_string) = "R0"];

  IntegrationErrorMapHonorWithIdentification = 60 [(integration_error_map_value) = {
    iso_message: "Honor with identification",
    description: "Transaction processed successfully - identification NOT required. This code is returned by some banks in place of 00 response.",
    integration_error_id: None
  }, (mvp.from_string) = "08"];

  IntegrationErrorMapUnacceptableTransactionFee = 61 [(integration_error_map_value) = {
    iso_message: "Unacceptable transaction fee",
    description: "An unspecified bank error has occurred. No further information is available from eWAY or the bank. The customer should attempt to process the transaction again.",
    integration_error_id: UndefinedError
  }, (mvp.from_string) = "23"];

  IntegrationErrorMapDuplicateFileUpdateRecord = 62 [(integration_error_map_value) = {
    iso_message: "Duplicate file update record",
    description: "",
    integration_error_id: UndefinedError
  }, (mvp.from_string) = "26"];

  IntegrationErrorMapActivityCountExceeded = 63 [(integration_error_map_value) = {
    iso_message: "Activity count exceeded",
    description: "The customer’s card issuer has declined the transaction as the customer has exceeded the withdrawal frequency limit.",
    integration_error_id: ExceedsTransactionFrequencyLimit
  }, (mvp.from_string) = "65"];

  IntegrationErrorMapCardPickUpAtATM = 64 [(integration_error_map_value) = {
    iso_message: "Card pick up at ATM",
    description: "The customer’s card issuer has declined the transaction as the card is suspected to be a counterfeit. The customer’s card issuer has requested that your customer’s credit card be retained by you.",
    integration_error_id: LostCard
  }, (mvp.from_string) = "67"];

  IntegrationErrorMapDataMismatchOriginalMessage = 65 [(integration_error_map_value) = {
    iso_message: "Data does not match original message",
    description: "",
    integration_error_id: UndefinedError
  }, (mvp.from_string) = "77"];

  IntegrationErrorMapInvalidAuthorizationLifeCycle = 66 [(integration_error_map_value) = {
    iso_message: "Invalid authorization life cycle",
    description: "",
    integration_error_id: UndefinedError
  }, (mvp.from_string) = "84"];

  IntegrationErrorMapDuplicateTransmission = 67 [(integration_error_map_value) = {
    iso_message: "Duplicate transmission",
    description: "The customer’s card issuer has declined the transaction as this transaction appears to be a duplicate transmission.",
    integration_error_id: TransactionDeclinedByIssuer
  }, (mvp.from_string) = "94"];

  IntegrationErrorMapDeclineForCVV2Failure = 68 [(integration_error_map_value) = {
    iso_message: "Decline for CVV2 failure",
    description: "",
    integration_error_id: IncorrectCVVCVC
  }, (mvp.from_string) = "N7"];

  IntegrationErrorMapCardAuthenticationFailed = 69 [(integration_error_map_value) = {
    iso_message: "Card Authentication failed",
    description: "",
    integration_error_id: InvalidCard
  }, (mvp.from_string) = "Q1"];

  IntegrationErrorMapPickUpCardSpecial = 70 [(integration_error_map_value) = {
    iso_message: "Pick up card (special)",
    description: "",
    integration_error_id: LostCard
  }, (mvp.from_string) = "07"];

  IntegrationErrorMapRequestInProgress = 71 [(integration_error_map_value) = {
    iso_message: "Request in progress",
    description: "The card issuer has indicated there is a problem with the card number. The customer should contact their bank and/or use an alternate credit card.",
    integration_error_id: TransactionDeclinedByIssuer
  }, (mvp.from_string) = "09"];

  IntegrationErrorMapApprovedUpdateTrack3 = 72 [(integration_error_map_value) = {
    iso_message: "Approved update track 3",
    description: "",
    integration_error_id: None
  }, (mvp.from_string) = "16"];

  IntegrationErrorMapSuspectedMalfunction = 73 [(integration_error_map_value) = {
    iso_message: "Suspected malfunction",
    description: "The customer’s card issuer could not be contacted during the transaction. The customer should check the card information and try processing the transaction again.",
    integration_error_id: TransactionDeclinedByIssuer
  }, (mvp.from_string) = "22"];

  IntegrationErrorMapRestrictedCard = 74 [(integration_error_map_value) = {
    iso_message: "Restricted card",
    description: "The customer’s card issuer has declined the transaction and requested that your customer’s card be retained.",
    integration_error_id: TransactionDeclinedByIssuer
  }, (mvp.from_string) = "36"];

  IntegrationErrorMapPickUpCardStolenCard = 75 [(integration_error_map_value) = {
    iso_message: "Pick up card (stolen card)",
    description: "The customer’s card has been reported as stolen. While you could contact this customer yourself, it's very possible that this transaction is fraudulent.",
    integration_error_id: StolenCard
  }, (mvp.from_string) = "43"];

  IntegrationErrorMapExceedsWithdrawalLimit = 76 [(integration_error_map_value) = {
    iso_message: "Exceeds withdrawal limit",
    description: "The customer’s card issuer has declined the transaction as it will exceed the customer’s card limit.",
    integration_error_id: ExceedsAmountLimit
  }, (mvp.from_string) = "61"];

  IntegrationErrorMapRestrictedCardAlternate = 77 [(integration_error_map_value) = {
    iso_message: "Restricted card",
    description: "The customer’s card issuer has declined the transaction as the credit card has some restrictions.",
    integration_error_id: InvalidCard
  }, (mvp.from_string) = "62"];

  IntegrationErrorMapResponseReceivedTooLate = 78 [(integration_error_map_value) = {
    iso_message: "Response received too late",
    description: "",
    integration_error_id: UndefinedError
  }, (mvp.from_string) = "68"];

  IntegrationErrorMapTooManyWrongPINTries = 79 [(integration_error_map_value) = {
    iso_message: "Too many wrong PIN tries",
    description: "The customer’s card issuer has declined the transaction as the customer has entered the incorrect PIN more than three times.",
    integration_error_id: BlockedCard
  }, (mvp.from_string) = "75"];

  IntegrationErrorMapIncorrectCVV = 80 [(integration_error_map_value) = {
    iso_message: "Incorrect CVV",
    description: "The customer’s card issuer has declined the transaction as the CVV is incorrect. The customer should check the CVV details (the 3 numbers on the back for Visa/MC, or 4 numbers on the front for AMEX) and try again.",
    integration_error_id: IncorrectCVVCVC
  }, (mvp.from_string) = "82"];

  IntegrationErrorMapNoReasonToDecline = 81 [(integration_error_map_value) = {
    iso_message: "No reason to decline",
    description: "",
    integration_error_id: UndefinedError
  }, (mvp.from_string) = "85"];

  IntegrationErrorMapDeclinedByGateway = 82 [(integration_error_map_value) = {
    iso_message: "Declined by gateway",
    description: "",
    integration_error_id: UndefinedError
  }, (mvp.from_string) = "B9"];

  IntegrationErrorMapTransactionAmountExceedsPreAuth = 83 [(integration_error_map_value) = {
    iso_message: "Transaction amount exceeds preauthori",
    description: "",
    integration_error_id: ExceedsAmountLimit
  }, (mvp.from_string) = "N8"];

  IntegrationErrorMapApproved = 84 [(integration_error_map_value) = {
    iso_message: "Approved",
    description: "Transaction has been processed successfully. No further action is required from you as the merchant.",
    integration_error_id: None
  }, (mvp.from_string) = "00"];

  IntegrationErrorMapPickUpCard = 85 [(integration_error_map_value) = {
    iso_message: "Pick up card",
    description: "The customer’s card issuer has declined the transaction and requested that the card be retained as the card may have been reported as lost or stolen.",
    integration_error_id: LostCard
  }, (mvp.from_string) = "04"];

  IntegrationErrorMapDoNotHonor = 86 [(integration_error_map_value) = {
    iso_message: "Do not honor",
    description: "The '05 Do Not Honour' error is a generic bank response code that has several possible causes. However, it generally indicates a card error rather than an error with your merchant facility. The '05' error indicates your bank declining the customer's card for an unspecified reason.",
    integration_error_id: InvalidCard
  }, (mvp.from_string) = "05"];

  IntegrationErrorMapUnableToLocateRecord = 87 [(integration_error_map_value) = {
    iso_message: "Unable to locate record",
    description: "The customer’s card issuer does not recognise the credit card details. The customer should check the card information and try processing the transaction again.",
    integration_error_id: TransactionDeclinedByIssuer
  }, (mvp.from_string) = "25"];

  IntegrationErrorMapFileUpdateFieldEditError = 88 [(integration_error_map_value) = {
    iso_message: "File update field edit error",
    description: "",
    integration_error_id: UndefinedError
  }, (mvp.from_string) = "27"];

  IntegrationErrorMapFileTemporarilyUnavailable = 89 [(integration_error_map_value) = {
    iso_message: "File temporarily unavailable",
    description: "",
    integration_error_id: UndefinedError
  }, (mvp.from_string) = "28"];

  IntegrationErrorMapCardAcceptor = 90 [(integration_error_map_value) = {
    iso_message: "Card acceptor call acquirer",
    description: "The customer’s card issuer has declined the transaction and requested that your customer’s card be retained.",
    integration_error_id: InvalidCard
  }, (mvp.from_string) = "37"];

  IntegrationErrorMapAllowablePINTriesExceeded = 91 [(integration_error_map_value) = {
    iso_message: "Allowable PIN tries exceeded",
    description: "The customer’s card issuer has declined the transaction as the customer has entered the incorrect PIN three times.",
    integration_error_id: BlockedCard
  }, (mvp.from_string) = "38"];

  IntegrationErrorMapPickUpCardLostCard = 92 [(integration_error_map_value) = {
    iso_message: "Pick up card (lost card)",
    description: "The customer’s card issuer has declined the transaction as the card has been reported lost.",
    integration_error_id: LostCard
  }, (mvp.from_string) = "41"];

  IntegrationErrorMapNoSavingsAccount = 93 [(integration_error_map_value) = {
    iso_message: "No savings account",
    description: "The customer’s card issuer has declined the transaction as the credit card number is associated to a savings account that does not exist.",
    integration_error_id: InvalidCard
  }, (mvp.from_string) = "53"];

  IntegrationErrorMapIncorrectPIN = 94 [(integration_error_map_value) = {
    iso_message: "Incorrect PIN",
    description: "The customer’s card issuer has declined the transaction as the customer has entered an incorrect PIN. The customer should re-enter their PIN.",
    integration_error_id: IncorrectCVVCVC
  }, (mvp.from_string) = "55"];

  IntegrationErrorMapCashServiceNotAvailable = 95 [(integration_error_map_value) = {
    iso_message: "Cash service not available",
    description: "",
    integration_error_id: UndefinedError
  }, (mvp.from_string) = "N3"];

  IntegrationErrorMapForwardIssuer = 96 [(integration_error_map_value) = {
    iso_message: "Forward to issuer",
    description: "",
    integration_error_id: UndefinedError
  }, (mvp.from_string) = "XA"];

  IntegrationErrorMapCGICheckFailed = 97 [(integration_error_map_value) = {
    iso_message: "CGI check failed",
    description: "Запрос не прошел CGI-проверку",
    integration_error_id: ThreeDSAuthFailed
  }, (mvp.from_string) = "-2"];

  IntegrationErrorMapInvalidAmountField = 98 [(integration_error_map_value) = {
    iso_message: "Invalid 'Amount' field",
    description: "Ошибка в поле 'Amount' запроса",
    integration_error_id: ExceedsAmountLimit
  }, (mvp.from_string) = "-10"];

  IntegrationErrorMapInvalidCurrencyField = 99 [(integration_error_map_value) = {
    iso_message: "Invalid 'Currency' field",
    description: "Ошибка в поле 'Currency' запроса",
    integration_error_id: UndefinedError
  }, (mvp.from_string) = "-11"];

  IntegrationErrorMapHostNotResponding = 100 [(integration_error_map_value) = {
    iso_message: "Host not responding or invalid template format",
    description: "Хост эквайера (TS) не отвечает либо неверный формат файла шаблона ответа модуля eGateway",
    integration_error_id: UnavailableIssuer
  }, (mvp.from_string) = "-3"];

  IntegrationErrorMapNoConnectionWithPINPad = 101 [(integration_error_map_value) = {
    iso_message: "No connection with PIN-pad",
    description: "Нет соединения с PIN-клавиатурой интернет-терминала",
    integration_error_id: UndefinedError
  }, (mvp.from_string) = "-14"];

  IntegrationErrorMapConnectionErrorDuringProcessing = 102 [(integration_error_map_value) = {
    iso_message: "Connection error during transaction processing",
    description: "Ошибка соединения с хостом эквайера (TS) во время обработки транзакции",
    integration_error_id: UnavailableAcquirer
  }, (mvp.from_string) = "-5"];

  IntegrationErrorMapMissingMandatoryField = 103 [(integration_error_map_value) = {
    iso_message: "Missing mandatory field",
    description: "В запросе не заполнено обязательное поле",
    integration_error_id: IncorrectCardNumber
  }, (mvp.from_string) = "-1"];

  IntegrationErrorMapNoConnectionWithHost = 104 [(integration_error_map_value) = {
    iso_message: "No connection with the host",
    description: "Нет соединения с хостом эквайера (TS)",
    integration_error_id: UnavailableAcquirer
  }, (mvp.from_string) = "-4"];

  IntegrationErrorMapInvalidMerchantIDField = 105 [(integration_error_map_value) = {
    iso_message: "Invalid 'Merchant ID' field",
    description: "Ошибка в поле 'Merchant ID' запроса",
    integration_error_id: NonExistentCard
  }, (mvp.from_string) = "-12"];

  IntegrationErrorMapInvalidRRNField = 106 [(integration_error_map_value) = {
    iso_message: "Invalid 'RRN' field",
    description: "Ошибка в поле 'RRN' запроса",
    integration_error_id: UndefinedError
  }, (mvp.from_string) = "-15"];

  IntegrationErrorMapAccessDenied = 107 [(integration_error_map_value) = {
    iso_message: "Access denied",
    description: "Терминалу отказано в доступе к модулю e-Gateway",
    integration_error_id: BlockedCard
  }, (mvp.from_string) = "-17"];

  IntegrationErrorMapTerminalBusy = 108 [(integration_error_map_value) = {
    iso_message: "Terminal busy",
    description: "На терминале выполняется другая транзакция",
    integration_error_id: ExceedsTransactionFrequencyLimit
  }, (mvp.from_string) = "-16"];

  IntegrationErrorMapIPAddressMismatch = 109 [(integration_error_map_value) = {
    iso_message: "IP address mismatch",
    description: "IP-адрес источника транзакции не соответствует ожидаемому",
    integration_error_id: SuspiciousClient
  }, (mvp.from_string) = "-13"];

  IntegrationErrorMapAuthenticationError = 110 [(integration_error_map_value) = {
    iso_message: "Authentication error",
    description: "Ошибка в запросе на аутентификационную информацию либо аутентификация неуспешна",
    integration_error_id: ThreeDSAuthFailed
  }, (mvp.from_string) = "-19"];

  IntegrationErrorMapEGatewaySetupError = 111 [(integration_error_map_value) = {
    iso_message: "E-Gateway module setup error",
    description: "Ошибка настройки модуля e-Gateway",
    integration_error_id: UndefinedError
  }, (mvp.from_string) = "-6"];

  IntegrationErrorMapIncorrectHostResponse = 112 [(integration_error_map_value) = {
    iso_message: "Incorrect host response",
    description: "Некорректный ответ хоста эквайера (TS), например, отсутствуют обязательные поля",
    integration_error_id: TransactionDeclinedByAcquirer
  }, (mvp.from_string) = "-7"];

  IntegrationErrorMapInvalidCardNumberField = 113 [(integration_error_map_value) = {
    iso_message: "Invalid 'Card number' field",
    description: "Ошибка в поле 'Card number' запроса",
    integration_error_id: IncorrectCardNumber
  }, (mvp.from_string) = "-8"];

  IntegrationErrorMapInvalidCardExpirationDateField = 114 [(integration_error_map_value) = {
    iso_message: "Invalid 'Card expiration date' field",
    description: "Ошибка в поле 'Card expiration date' запроса",
    integration_error_id: IncorrectCardExpDate
  }, (mvp.from_string) = "-9"];

  IntegrationErrorMapInvalidCVC2Field = 115 [(integration_error_map_value) = {
    iso_message: "Invalid 'CVC2' field",
    description: "Ошибка в поле 'CVC2' или 'CVC2 Description' запроса",
    integration_error_id: IncorrectCVVCVC
  }, (mvp.from_string) = "-18"];

  IntegrationErrorMapTimeExceeded = 116 [(integration_error_map_value) = {
    iso_message: "Time exceeded",
    description: "Превышен допустимый временной интервал между запросом и временем модуля e-Gateway",
    integration_error_id: UndefinedError
  }, (mvp.from_string) = "-20"];
}
