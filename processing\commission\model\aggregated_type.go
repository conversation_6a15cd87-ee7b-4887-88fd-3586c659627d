package model

type AggregatedType uint8

const (
	AggregatedTypeIn AggregatedType = iota + 1
	AggregatedTypeOut
)

const (
	AggregatedTypeNameIn  = "in"
	AggregatedTypeNameOut = "out"
)

type AggregatedTypeList []struct {
	AggregatedType AggregatedType `json:"aggregated_type" validate:"required"`
	Name           string         `json:"name" validate:"required"`
}

func GetAllAggregatedTypes() AggregatedTypeList {
	return AggregatedTypeList{
		{
			AggregatedType: AggregatedTypeIn,
			Name:           AggregatedTypeNameIn,
		},
		{
			AggregatedType: AggregatedTypeOut,
			Name:           AggregatedTypeNameOut,
		},
	}
}
