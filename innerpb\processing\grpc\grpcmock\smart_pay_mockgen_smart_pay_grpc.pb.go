// Code generated by MockGen. DO NOT EDIT.
// Source: smart_pay_grpc.pb.go

// Package grpcmock is a generated GoMock package.
package grpcmock

import (
	context "context"
	reflect "reflect"

	grpc "git.local/sensitive/innerpb/processing/grpc"
	gomock "github.com/golang/mock/gomock"
	grpc0 "google.golang.org/grpc"
)

// MockSmartPayClient is a mock of SmartPayClient interface.
type MockSmartPayClient struct {
	ctrl     *gomock.Controller
	recorder *MockSmartPayClientMockRecorder
}

// MockSmartPayClientMockRecorder is the mock recorder for MockSmartPayClient.
type MockSmartPayClientMockRecorder struct {
	mock *MockSmartPayClient
}

// NewMockSmartPayClient creates a new mock instance.
func NewMockSmartPayClient(ctrl *gomock.Controller) *MockSmartPayClient {
	mock := &MockSmartPayClient{ctrl: ctrl}
	mock.recorder = &MockSmartPayClientMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockSmartPayClient) EXPECT() *MockSmartPayClientMockRecorder {
	return m.recorder
}

// ApplePaySession mocks base method.
func (m *MockSmartPayClient) ApplePaySession(ctx context.Context, in *grpc.ApplePaySessionRequestV1, opts ...grpc0.CallOption) (*grpc.ApplePaySessionResponseV1, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "ApplePaySession", varargs...)
	ret0, _ := ret[0].(*grpc.ApplePaySessionResponseV1)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ApplePaySession indicates an expected call of ApplePaySession.
func (mr *MockSmartPayClientMockRecorder) ApplePaySession(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ApplePaySession", reflect.TypeOf((*MockSmartPayClient)(nil).ApplePaySession), varargs...)
}

// DecodeToken mocks base method.
func (m *MockSmartPayClient) DecodeToken(ctx context.Context, in *grpc.DecodeTokenRequestV1, opts ...grpc0.CallOption) (*grpc.DecodeTokenResponseV1, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "DecodeToken", varargs...)
	ret0, _ := ret[0].(*grpc.DecodeTokenResponseV1)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DecodeToken indicates an expected call of DecodeToken.
func (mr *MockSmartPayClientMockRecorder) DecodeToken(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DecodeToken", reflect.TypeOf((*MockSmartPayClient)(nil).DecodeToken), varargs...)
}

// DecryptGPayToken mocks base method.
func (m *MockSmartPayClient) DecryptGPayToken(ctx context.Context, in *grpc.DecryptGPayTokenRequestV1, opts ...grpc0.CallOption) (*grpc.DecryptGPayTokenResponseV1, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "DecryptGPayToken", varargs...)
	ret0, _ := ret[0].(*grpc.DecryptGPayTokenResponseV1)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DecryptGPayToken indicates an expected call of DecryptGPayToken.
func (mr *MockSmartPayClientMockRecorder) DecryptGPayToken(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DecryptGPayToken", reflect.TypeOf((*MockSmartPayClient)(nil).DecryptGPayToken), varargs...)
}

// GetGPayCredentials mocks base method.
func (m *MockSmartPayClient) GetGPayCredentials(ctx context.Context, in *grpc.GetGPayCredentialsRequestV1, opts ...grpc0.CallOption) (*grpc.GetGPayCredentialsResponseV1, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetGPayCredentials", varargs...)
	ret0, _ := ret[0].(*grpc.GetGPayCredentialsResponseV1)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetGPayCredentials indicates an expected call of GetGPayCredentials.
func (mr *MockSmartPayClientMockRecorder) GetGPayCredentials(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetGPayCredentials", reflect.TypeOf((*MockSmartPayClient)(nil).GetGPayCredentials), varargs...)
}

// MockSmartPayServer is a mock of SmartPayServer interface.
type MockSmartPayServer struct {
	ctrl     *gomock.Controller
	recorder *MockSmartPayServerMockRecorder
}

// MockSmartPayServerMockRecorder is the mock recorder for MockSmartPayServer.
type MockSmartPayServerMockRecorder struct {
	mock *MockSmartPayServer
}

// NewMockSmartPayServer creates a new mock instance.
func NewMockSmartPayServer(ctrl *gomock.Controller) *MockSmartPayServer {
	mock := &MockSmartPayServer{ctrl: ctrl}
	mock.recorder = &MockSmartPayServerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockSmartPayServer) EXPECT() *MockSmartPayServerMockRecorder {
	return m.recorder
}

// ApplePaySession mocks base method.
func (m *MockSmartPayServer) ApplePaySession(arg0 context.Context, arg1 *grpc.ApplePaySessionRequestV1) (*grpc.ApplePaySessionResponseV1, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ApplePaySession", arg0, arg1)
	ret0, _ := ret[0].(*grpc.ApplePaySessionResponseV1)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ApplePaySession indicates an expected call of ApplePaySession.
func (mr *MockSmartPayServerMockRecorder) ApplePaySession(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ApplePaySession", reflect.TypeOf((*MockSmartPayServer)(nil).ApplePaySession), arg0, arg1)
}

// DecodeToken mocks base method.
func (m *MockSmartPayServer) DecodeToken(arg0 context.Context, arg1 *grpc.DecodeTokenRequestV1) (*grpc.DecodeTokenResponseV1, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DecodeToken", arg0, arg1)
	ret0, _ := ret[0].(*grpc.DecodeTokenResponseV1)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DecodeToken indicates an expected call of DecodeToken.
func (mr *MockSmartPayServerMockRecorder) DecodeToken(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DecodeToken", reflect.TypeOf((*MockSmartPayServer)(nil).DecodeToken), arg0, arg1)
}

// DecryptGPayToken mocks base method.
func (m *MockSmartPayServer) DecryptGPayToken(arg0 context.Context, arg1 *grpc.DecryptGPayTokenRequestV1) (*grpc.DecryptGPayTokenResponseV1, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DecryptGPayToken", arg0, arg1)
	ret0, _ := ret[0].(*grpc.DecryptGPayTokenResponseV1)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DecryptGPayToken indicates an expected call of DecryptGPayToken.
func (mr *MockSmartPayServerMockRecorder) DecryptGPayToken(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DecryptGPayToken", reflect.TypeOf((*MockSmartPayServer)(nil).DecryptGPayToken), arg0, arg1)
}

// GetGPayCredentials mocks base method.
func (m *MockSmartPayServer) GetGPayCredentials(arg0 context.Context, arg1 *grpc.GetGPayCredentialsRequestV1) (*grpc.GetGPayCredentialsResponseV1, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetGPayCredentials", arg0, arg1)
	ret0, _ := ret[0].(*grpc.GetGPayCredentialsResponseV1)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetGPayCredentials indicates an expected call of GetGPayCredentials.
func (mr *MockSmartPayServerMockRecorder) GetGPayCredentials(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetGPayCredentials", reflect.TypeOf((*MockSmartPayServer)(nil).GetGPayCredentials), arg0, arg1)
}

// mustEmbedUnimplementedSmartPayServer mocks base method.
func (m *MockSmartPayServer) mustEmbedUnimplementedSmartPayServer() {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "mustEmbedUnimplementedSmartPayServer")
}

// mustEmbedUnimplementedSmartPayServer indicates an expected call of mustEmbedUnimplementedSmartPayServer.
func (mr *MockSmartPayServerMockRecorder) mustEmbedUnimplementedSmartPayServer() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "mustEmbedUnimplementedSmartPayServer", reflect.TypeOf((*MockSmartPayServer)(nil).mustEmbedUnimplementedSmartPayServer))
}

// MockUnsafeSmartPayServer is a mock of UnsafeSmartPayServer interface.
type MockUnsafeSmartPayServer struct {
	ctrl     *gomock.Controller
	recorder *MockUnsafeSmartPayServerMockRecorder
}

// MockUnsafeSmartPayServerMockRecorder is the mock recorder for MockUnsafeSmartPayServer.
type MockUnsafeSmartPayServerMockRecorder struct {
	mock *MockUnsafeSmartPayServer
}

// NewMockUnsafeSmartPayServer creates a new mock instance.
func NewMockUnsafeSmartPayServer(ctrl *gomock.Controller) *MockUnsafeSmartPayServer {
	mock := &MockUnsafeSmartPayServer{ctrl: ctrl}
	mock.recorder = &MockUnsafeSmartPayServerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockUnsafeSmartPayServer) EXPECT() *MockUnsafeSmartPayServerMockRecorder {
	return m.recorder
}

// mustEmbedUnimplementedSmartPayServer mocks base method.
func (m *MockUnsafeSmartPayServer) mustEmbedUnimplementedSmartPayServer() {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "mustEmbedUnimplementedSmartPayServer")
}

// mustEmbedUnimplementedSmartPayServer indicates an expected call of mustEmbedUnimplementedSmartPayServer.
func (mr *MockUnsafeSmartPayServerMockRecorder) mustEmbedUnimplementedSmartPayServer() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "mustEmbedUnimplementedSmartPayServer", reflect.TypeOf((*MockUnsafeSmartPayServer)(nil).mustEmbedUnimplementedSmartPayServer))
}
