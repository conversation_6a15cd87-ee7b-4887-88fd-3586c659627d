// Code generated by protoc-gen-go-mvp. DO NOT EDIT.
// versions:
// - protoc-gen-go-mvp v2.0.1
// created by mvp authors

package grpc

func (x EnumFrequencyType) Code() string {
	switch x {
	case EnumFrequencyType_PerDay:
		return "day"
	case EnumFrequencyType_PerWeek:
		return "week"
	case EnumFrequencyType_PerMonth:
		return "month"
	default:
		return ""
	}
}

func (x EnumFrequencyType) Name() string {
	switch x {
	case EnumFrequencyType_PerDay:
		return "День"
	case EnumFrequencyType_PerWeek:
		return "Неделя"
	case EnumFrequencyType_PerMonth:
		return "Месяц"
	default:
		return ""
	}
}

// Created reference to EnumFrequencyType

//	|	EnumFrequencyType         	|	Code   	|	Name          	|
//	|	EnumFrequencyType_PerDay  	|	"day"  	|	"День"        	|
//	|	EnumFrequencyType_PerWeek 	|	"week" 	|	"Неделя"      	|
//	|	EnumFrequencyType_PerMonth	|	"month"	|	"Месяц"       	|

var SliceEnumFrequencyTypeRefs *sliceEnumFrequencyTypeRefs

type sliceEnumFrequencyTypeRefs struct{}

func (*sliceEnumFrequencyTypeRefs) Code(slice ...EnumFrequencyType) []string {
	var result []string
	for _, val := range slice {
		result = append(result, val.Code())
	}

	return result
}

func (*sliceEnumFrequencyTypeRefs) Name(slice ...EnumFrequencyType) []string {
	var result []string
	for _, val := range slice {
		result = append(result, val.Name())
	}

	return result
}
