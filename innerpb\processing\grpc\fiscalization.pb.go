// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.5
// 	protoc        v5.29.3
// source: inner/processing/grpc/fiscalization.proto

package grpc

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type MakeFiscalizationRequestV1 struct {
	state                  protoimpl.MessageState `protogen:"open.v1"`
	TransactionId          *uint64                `protobuf:"varint,1,opt,name=transaction_id,json=transactionId" json:"transaction_id,omitempty"`
	TransactionCreatedAt   *timestamppb.Timestamp `protobuf:"bytes,2,opt,name=transaction_created_at,json=transactionCreatedAt" json:"transaction_created_at,omitempty"`
	TransactionTotalAmount *float64               `protobuf:"fixed64,3,opt,name=transaction_total_amount,json=transactionTotalAmount" json:"transaction_total_amount,omitempty"`
	UpperCommissionAmount  *float64               `protobuf:"fixed64,4,opt,name=upper_commission_amount,json=upperCommissionAmount" json:"upper_commission_amount,omitempty"`
	Description            *string                `protobuf:"bytes,5,opt,name=description" json:"description,omitempty"`
	CustomerId             *uint64                `protobuf:"varint,6,opt,name=customer_id,json=customerId" json:"customer_id,omitempty"`
	unknownFields          protoimpl.UnknownFields
	sizeCache              protoimpl.SizeCache
}

func (x *MakeFiscalizationRequestV1) Reset() {
	*x = MakeFiscalizationRequestV1{}
	mi := &file_inner_processing_grpc_fiscalization_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *MakeFiscalizationRequestV1) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MakeFiscalizationRequestV1) ProtoMessage() {}

func (x *MakeFiscalizationRequestV1) ProtoReflect() protoreflect.Message {
	mi := &file_inner_processing_grpc_fiscalization_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MakeFiscalizationRequestV1.ProtoReflect.Descriptor instead.
func (*MakeFiscalizationRequestV1) Descriptor() ([]byte, []int) {
	return file_inner_processing_grpc_fiscalization_proto_rawDescGZIP(), []int{0}
}

func (x *MakeFiscalizationRequestV1) GetTransactionId() uint64 {
	if x != nil && x.TransactionId != nil {
		return *x.TransactionId
	}
	return 0
}

func (x *MakeFiscalizationRequestV1) GetTransactionCreatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.TransactionCreatedAt
	}
	return nil
}

func (x *MakeFiscalizationRequestV1) GetTransactionTotalAmount() float64 {
	if x != nil && x.TransactionTotalAmount != nil {
		return *x.TransactionTotalAmount
	}
	return 0
}

func (x *MakeFiscalizationRequestV1) GetUpperCommissionAmount() float64 {
	if x != nil && x.UpperCommissionAmount != nil {
		return *x.UpperCommissionAmount
	}
	return 0
}

func (x *MakeFiscalizationRequestV1) GetDescription() string {
	if x != nil && x.Description != nil {
		return *x.Description
	}
	return ""
}

func (x *MakeFiscalizationRequestV1) GetCustomerId() uint64 {
	if x != nil && x.CustomerId != nil {
		return *x.CustomerId
	}
	return 0
}

type GetFiscalInfoByTransactionIDRequestV1 struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	TransactionId *uint64                `protobuf:"varint,1,opt,name=transaction_id,json=transactionId" json:"transaction_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetFiscalInfoByTransactionIDRequestV1) Reset() {
	*x = GetFiscalInfoByTransactionIDRequestV1{}
	mi := &file_inner_processing_grpc_fiscalization_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetFiscalInfoByTransactionIDRequestV1) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetFiscalInfoByTransactionIDRequestV1) ProtoMessage() {}

func (x *GetFiscalInfoByTransactionIDRequestV1) ProtoReflect() protoreflect.Message {
	mi := &file_inner_processing_grpc_fiscalization_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetFiscalInfoByTransactionIDRequestV1.ProtoReflect.Descriptor instead.
func (*GetFiscalInfoByTransactionIDRequestV1) Descriptor() ([]byte, []int) {
	return file_inner_processing_grpc_fiscalization_proto_rawDescGZIP(), []int{1}
}

func (x *GetFiscalInfoByTransactionIDRequestV1) GetTransactionId() uint64 {
	if x != nil && x.TransactionId != nil {
		return *x.TransactionId
	}
	return 0
}

type GetFiscalInfoByTransactionIDResponseV1 struct {
	state          protoimpl.MessageState `protogen:"open.v1"`
	FiscalNumber   *string                `protobuf:"bytes,1,opt,name=fiscal_number,json=fiscalNumber" json:"fiscal_number,omitempty"`
	FiscalCheckUrl *string                `protobuf:"bytes,2,opt,name=fiscal_check_url,json=fiscalCheckUrl" json:"fiscal_check_url,omitempty"`
	unknownFields  protoimpl.UnknownFields
	sizeCache      protoimpl.SizeCache
}

func (x *GetFiscalInfoByTransactionIDResponseV1) Reset() {
	*x = GetFiscalInfoByTransactionIDResponseV1{}
	mi := &file_inner_processing_grpc_fiscalization_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetFiscalInfoByTransactionIDResponseV1) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetFiscalInfoByTransactionIDResponseV1) ProtoMessage() {}

func (x *GetFiscalInfoByTransactionIDResponseV1) ProtoReflect() protoreflect.Message {
	mi := &file_inner_processing_grpc_fiscalization_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetFiscalInfoByTransactionIDResponseV1.ProtoReflect.Descriptor instead.
func (*GetFiscalInfoByTransactionIDResponseV1) Descriptor() ([]byte, []int) {
	return file_inner_processing_grpc_fiscalization_proto_rawDescGZIP(), []int{2}
}

func (x *GetFiscalInfoByTransactionIDResponseV1) GetFiscalNumber() string {
	if x != nil && x.FiscalNumber != nil {
		return *x.FiscalNumber
	}
	return ""
}

func (x *GetFiscalInfoByTransactionIDResponseV1) GetFiscalCheckUrl() string {
	if x != nil && x.FiscalCheckUrl != nil {
		return *x.FiscalCheckUrl
	}
	return ""
}

var File_inner_processing_grpc_fiscalization_proto protoreflect.FileDescriptor

var file_inner_processing_grpc_fiscalization_proto_rawDesc = string([]byte{
	0x0a, 0x29, 0x69, 0x6e, 0x6e, 0x65, 0x72, 0x2f, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69,
	0x6e, 0x67, 0x2f, 0x67, 0x72, 0x70, 0x63, 0x2f, 0x66, 0x69, 0x73, 0x63, 0x61, 0x6c, 0x69, 0x7a,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x26, 0x70, 0x72, 0x6f,
	0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x2e, 0x66, 0x69, 0x73, 0x63, 0x61, 0x6c, 0x69, 0x7a,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x66, 0x69, 0x73, 0x63, 0x61, 0x6c, 0x69, 0x7a, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x1a, 0x1f, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x62, 0x75, 0x66, 0x2f, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1b, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x65, 0x6d, 0x70, 0x74, 0x79, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x22, 0xca, 0x02, 0x0a, 0x1a, 0x4d, 0x61, 0x6b, 0x65, 0x46, 0x69, 0x73, 0x63, 0x61, 0x6c,
	0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x56, 0x31,
	0x12, 0x25, 0x0a, 0x0e, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x5f,
	0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x0d, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61,
	0x63, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x12, 0x50, 0x0a, 0x16, 0x74, 0x72, 0x61, 0x6e, 0x73,
	0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61,
	0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74,
	0x61, 0x6d, 0x70, 0x52, 0x14, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e,
	0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x12, 0x38, 0x0a, 0x18, 0x74, 0x72, 0x61,
	0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x5f, 0x61,
	0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x01, 0x52, 0x16, 0x74, 0x72, 0x61,
	0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x6f, 0x74, 0x61, 0x6c, 0x41, 0x6d, 0x6f,
	0x75, 0x6e, 0x74, 0x12, 0x36, 0x0a, 0x17, 0x75, 0x70, 0x70, 0x65, 0x72, 0x5f, 0x63, 0x6f, 0x6d,
	0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x5f, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x01, 0x52, 0x15, 0x75, 0x70, 0x70, 0x65, 0x72, 0x43, 0x6f, 0x6d, 0x6d, 0x69,
	0x73, 0x73, 0x69, 0x6f, 0x6e, 0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x20, 0x0a, 0x0b, 0x64,
	0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x1f, 0x0a,
	0x0b, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x06, 0x20, 0x01,
	0x28, 0x04, 0x52, 0x0a, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x49, 0x64, 0x22, 0x4e,
	0x0a, 0x25, 0x47, 0x65, 0x74, 0x46, 0x69, 0x73, 0x63, 0x61, 0x6c, 0x49, 0x6e, 0x66, 0x6f, 0x42,
	0x79, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x44, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x56, 0x31, 0x12, 0x25, 0x0a, 0x0e, 0x74, 0x72, 0x61, 0x6e, 0x73,
	0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52,
	0x0d, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x22, 0x77,
	0x0a, 0x26, 0x47, 0x65, 0x74, 0x46, 0x69, 0x73, 0x63, 0x61, 0x6c, 0x49, 0x6e, 0x66, 0x6f, 0x42,
	0x79, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x44, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x56, 0x31, 0x12, 0x23, 0x0a, 0x0d, 0x66, 0x69, 0x73, 0x63,
	0x61, 0x6c, 0x5f, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0c, 0x66, 0x69, 0x73, 0x63, 0x61, 0x6c, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x12, 0x28, 0x0a,
	0x10, 0x66, 0x69, 0x73, 0x63, 0x61, 0x6c, 0x5f, 0x63, 0x68, 0x65, 0x63, 0x6b, 0x5f, 0x75, 0x72,
	0x6c, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x66, 0x69, 0x73, 0x63, 0x61, 0x6c, 0x43,
	0x68, 0x65, 0x63, 0x6b, 0x55, 0x72, 0x6c, 0x32, 0xd2, 0x03, 0x0a, 0x0d, 0x46, 0x69, 0x73, 0x63,
	0x61, 0x6c, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x73, 0x0a, 0x13, 0x4d, 0x61, 0x6b,
	0x65, 0x46, 0x69, 0x73, 0x63, 0x61, 0x6c, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x56, 0x31,
	0x12, 0x42, 0x2e, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x2e, 0x66, 0x69,
	0x73, 0x63, 0x61, 0x6c, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x66, 0x69, 0x73, 0x63,
	0x61, 0x6c, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x4d, 0x61, 0x6b, 0x65, 0x46, 0x69,
	0x73, 0x63, 0x61, 0x6c, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x56, 0x31, 0x1a, 0x16, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x22, 0x00, 0x12, 0xc1,
	0x01, 0x0a, 0x1e, 0x47, 0x65, 0x74, 0x46, 0x69, 0x73, 0x63, 0x61, 0x6c, 0x49, 0x6e, 0x66, 0x6f,
	0x42, 0x79, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x44, 0x56,
	0x31, 0x12, 0x4d, 0x2e, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x2e, 0x66,
	0x69, 0x73, 0x63, 0x61, 0x6c, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x66, 0x69, 0x73,
	0x63, 0x61, 0x6c, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x47, 0x65, 0x74, 0x46, 0x69,
	0x73, 0x63, 0x61, 0x6c, 0x49, 0x6e, 0x66, 0x6f, 0x42, 0x79, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x61,
	0x63, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x44, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x56, 0x31,
	0x1a, 0x4e, 0x2e, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x2e, 0x66, 0x69,
	0x73, 0x63, 0x61, 0x6c, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x66, 0x69, 0x73, 0x63,
	0x61, 0x6c, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x47, 0x65, 0x74, 0x46, 0x69, 0x73,
	0x63, 0x61, 0x6c, 0x49, 0x6e, 0x66, 0x6f, 0x42, 0x79, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63,
	0x74, 0x69, 0x6f, 0x6e, 0x49, 0x44, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x56, 0x31,
	0x22, 0x00, 0x12, 0x3e, 0x0a, 0x0c, 0x4d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x53, 0x68, 0x69, 0x66,
	0x74, 0x73, 0x12, 0x16, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x62, 0x75, 0x66, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x1a, 0x16, 0x2e, 0x67, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x45, 0x6d, 0x70,
	0x74, 0x79, 0x12, 0x48, 0x0a, 0x16, 0x46, 0x69, 0x6e, 0x61, 0x6c, 0x69, 0x7a, 0x65, 0x46, 0x69,
	0x73, 0x63, 0x61, 0x6c, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x12, 0x16, 0x2e, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x45,
	0x6d, 0x70, 0x74, 0x79, 0x1a, 0x16, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x42, 0x2d, 0x5a, 0x2b,
	0x67, 0x69, 0x74, 0x2e, 0x6c, 0x6f, 0x63, 0x61, 0x6c, 0x2f, 0x73, 0x65, 0x6e, 0x73, 0x69, 0x74,
	0x69, 0x76, 0x65, 0x2f, 0x69, 0x6e, 0x6e, 0x65, 0x72, 0x70, 0x62, 0x2f, 0x70, 0x72, 0x6f, 0x63,
	0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x2f, 0x67, 0x72, 0x70, 0x63, 0x62, 0x08, 0x65, 0x64, 0x69,
	0x74, 0x69, 0x6f, 0x6e, 0x73, 0x70, 0xe8, 0x07,
})

var (
	file_inner_processing_grpc_fiscalization_proto_rawDescOnce sync.Once
	file_inner_processing_grpc_fiscalization_proto_rawDescData []byte
)

func file_inner_processing_grpc_fiscalization_proto_rawDescGZIP() []byte {
	file_inner_processing_grpc_fiscalization_proto_rawDescOnce.Do(func() {
		file_inner_processing_grpc_fiscalization_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_inner_processing_grpc_fiscalization_proto_rawDesc), len(file_inner_processing_grpc_fiscalization_proto_rawDesc)))
	})
	return file_inner_processing_grpc_fiscalization_proto_rawDescData
}

var file_inner_processing_grpc_fiscalization_proto_msgTypes = make([]protoimpl.MessageInfo, 3)
var file_inner_processing_grpc_fiscalization_proto_goTypes = []any{
	(*MakeFiscalizationRequestV1)(nil),             // 0: processing.fiscalization.fiscalization.MakeFiscalizationRequestV1
	(*GetFiscalInfoByTransactionIDRequestV1)(nil),  // 1: processing.fiscalization.fiscalization.GetFiscalInfoByTransactionIDRequestV1
	(*GetFiscalInfoByTransactionIDResponseV1)(nil), // 2: processing.fiscalization.fiscalization.GetFiscalInfoByTransactionIDResponseV1
	(*timestamppb.Timestamp)(nil),                  // 3: google.protobuf.Timestamp
	(*emptypb.Empty)(nil),                          // 4: google.protobuf.Empty
}
var file_inner_processing_grpc_fiscalization_proto_depIdxs = []int32{
	3, // 0: processing.fiscalization.fiscalization.MakeFiscalizationRequestV1.transaction_created_at:type_name -> google.protobuf.Timestamp
	0, // 1: processing.fiscalization.fiscalization.Fiscalization.MakeFiscalizationV1:input_type -> processing.fiscalization.fiscalization.MakeFiscalizationRequestV1
	1, // 2: processing.fiscalization.fiscalization.Fiscalization.GetFiscalInfoByTransactionIDV1:input_type -> processing.fiscalization.fiscalization.GetFiscalInfoByTransactionIDRequestV1
	4, // 3: processing.fiscalization.fiscalization.Fiscalization.ManageShifts:input_type -> google.protobuf.Empty
	4, // 4: processing.fiscalization.fiscalization.Fiscalization.FinalizeFiscalizations:input_type -> google.protobuf.Empty
	4, // 5: processing.fiscalization.fiscalization.Fiscalization.MakeFiscalizationV1:output_type -> google.protobuf.Empty
	2, // 6: processing.fiscalization.fiscalization.Fiscalization.GetFiscalInfoByTransactionIDV1:output_type -> processing.fiscalization.fiscalization.GetFiscalInfoByTransactionIDResponseV1
	4, // 7: processing.fiscalization.fiscalization.Fiscalization.ManageShifts:output_type -> google.protobuf.Empty
	4, // 8: processing.fiscalization.fiscalization.Fiscalization.FinalizeFiscalizations:output_type -> google.protobuf.Empty
	5, // [5:9] is the sub-list for method output_type
	1, // [1:5] is the sub-list for method input_type
	1, // [1:1] is the sub-list for extension type_name
	1, // [1:1] is the sub-list for extension extendee
	0, // [0:1] is the sub-list for field type_name
}

func init() { file_inner_processing_grpc_fiscalization_proto_init() }
func file_inner_processing_grpc_fiscalization_proto_init() {
	if File_inner_processing_grpc_fiscalization_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_inner_processing_grpc_fiscalization_proto_rawDesc), len(file_inner_processing_grpc_fiscalization_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   3,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_inner_processing_grpc_fiscalization_proto_goTypes,
		DependencyIndexes: file_inner_processing_grpc_fiscalization_proto_depIdxs,
		MessageInfos:      file_inner_processing_grpc_fiscalization_proto_msgTypes,
	}.Build()
	File_inner_processing_grpc_fiscalization_proto = out.File
	file_inner_processing_grpc_fiscalization_proto_goTypes = nil
	file_inner_processing_grpc_fiscalization_proto_depIdxs = nil
}
