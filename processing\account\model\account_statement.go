package model

import "time"

type AccountStatement struct {
	TimestampMixin
	ID                  int       `json:"id"`
	ExternalReferenceID string    `json:"external_reference_id"`
	DebitTurnover       float64   `json:"debit_turnover"`
	CreditTurnover      float64   `json:"credit_turnover"`
	MerchantAccount     string    `json:"merchant_account"`
	MerchantBankIDCode  string    `json:"merchant_bank_id_code"`
	MerchantBIN         string    `json:"merchant_bin"`
	MerchantName        string    `json:"merchant_name"`
	Currency            string    `json:"currency"`
	PaymentPurposeCode  string    `json:"payment_purpose_code"`
	Description         string    `json:"description"`
	FinishedAt          time.Time `json:"finished_at"`
	BeneficiaryCode     string    `json:"beneficiary_code"`
	AccountID           uint64    `json:"account_id"`
}

func (as AccountStatement) TableName() string {
	return "account.bcc_account_statement"
}
