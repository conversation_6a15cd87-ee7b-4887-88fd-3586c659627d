package database

import (
	"context"
	"errors"
	"git.local/sensitive/innerpb/processing/goerr"

	"gorm.io/gorm"

	"git.local/sensitive/processing/card/model"
	"git.local/sensitive/sdk/dog"
)

type ProjectMaskFormatDB struct {
	db *gorm.DB
}

func NewProjectMaskFormatDB(
	db *gorm.DB,
) ProjectMaskFormatter {
	return &ProjectMaskFormatDB{
		db: db,
	}
}

// GetByProjectID - функция ищет формат маски карты проекта, если не находит,
// то возвращает дефолтный формат маскирования (1234-12XXXXXX-1234)
func (p ProjectMaskFormatDB) GetByProjectID(ctx context.Context, projectID uint64) (*model.ProjectMaskFormat, error) {
	ctx, span := dog.CreateSpan(ctx, "ProjectMaskFormatDB_GetByProjectID")
	defer span.End()

	maskFormat := new(model.ProjectMaskFormat)
	defaultWithSeparator := model.DefaultWithSeparatorValue

	if err := p.db.WithContext(ctx).
		Where("project_id = ?", projectID).
		First(maskFormat).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return &model.ProjectMaskFormat{ // if record not found return default mask format
				ProjectId:       projectID,
				PlaceholderSign: model.DefaultPlaceholderSign,
				WithSeparator:   &defaultWithSeparator,
			}, nil
		}

		return nil, err
	}

	return maskFormat, nil
}

func (p ProjectMaskFormatDB) Create(ctx context.Context, maskFormat *model.ProjectMaskFormat) error {
	ctx, span := dog.CreateSpan(ctx, "ProjectMaskFormatDB_Create")
	defer span.End()

	if err := p.db.WithContext(ctx).Create(maskFormat).Error; err != nil {
		return goerr.ErrDbUnexpected.WithErr(err).WithCtx(ctx)
	}

	return nil
}

func (p ProjectMaskFormatDB) UpdateByProjectId(
	ctx context.Context,
	projectID uint64,
	placeholderSign string,
	withSeparator *bool,
) error {
	ctx, span := dog.CreateSpan(ctx, "ProjectMaskFormatDB_UpdateByProjectId")
	defer span.End()

	if err := p.db.WithContext(ctx).
		Model(&model.ProjectMaskFormat{}).
		Where("project_id = ?", projectID).
		Updates(&model.ProjectMaskFormat{
			PlaceholderSign: placeholderSign,
			WithSeparator:   withSeparator,
		}).First(&model.ProjectMaskFormat{}).Error; err != nil {
		return goerr.ErrDbUnexpected.WithErr(err).WithCtx(ctx)
	}

	return nil
}
