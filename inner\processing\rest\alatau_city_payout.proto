edition = "2023";

package alatau_city_bank;

option go_package = "git.local/sensitive/innerpb/processing/rest";

import "mvp/proto/rest.proto";
import "mvp/proto/logger.proto";

service AlatauCityBankPayout {
  option(mvp.rest_service_options) = {
    hosts :[
      {
        stand: "local",
        base_uri: "https://dbpay-test.jusan.kz/b2c"
      },
      {
        stand: "dev",
        base_uri: "https://dbpay-test.jusan.kz/b2c"
      },
      {
        stand: "test",
        base_uri: "https://dbpay.jusan.kz/b2c"
      },
      {
        stand: "stage",
        base_uri: "https://dbpay.jusan.kz/b2c"
      },
      {
        stand: "prod",
        base_uri: "https://dbpay.jusan.kz/b2c"
      },
      {
        stand: "sandbox",
        base_uri: "https://dbpay-test.jusan.kz/b2c"
      }
    ]
  };

  rpc CalculateFee(AlatauCityCalculateFeeRequest) returns (AlatauCityCalculateFeeResponse) {
    option(mvp.rest_method_options) = {
      method: Post,
      authorization:Basic,
      request_marshal: JSON,
      response_unmarshal: JSON,
      max_request_timeout: 60,
      path: "/api/a2c/calculate-fee",
    };
  }

  rpc CompletePayment(AlatauCityCompletePaymentRequest) returns (AlatauCityCompletePaymentResponse) {
    option(mvp.rest_method_options) = {
      method: Post,
      authorization:Basic,
      request_marshal: JSON,
      response_unmarshal: JSON,
      max_request_timeout: 60,
      path: "/api/a2c/complete-payment",
    };
  }

  rpc GetStatus(AlatauCityGetStatusPayoutRequest) returns (AlatauCityGetStatusPayoutResponse) {
    option(mvp.rest_method_options) = {
      method: Post,
      path: "/api/a2c/get-status-transaction",
      max_request_timeout: 60,
      authorization: Basic,
      request_marshal: JSON,
      response_unmarshal: JSON,
    };
  }

  rpc AliasInquiry(AlatauCityAliasInquiryRequest) returns (AlatauCityAliasInquiryResponse) {
    option(mvp.rest_method_options) = {
      method: Post,
      authorization: Basic,
      request_marshal: JSON,
      response_unmarshal: JSON,
      max_request_timeout: 60,
      path: "/api/a2c/alias-inquiry"
    };
  }

  rpc ResolveVisaAlias(AlatauCityResolveVisaAliasRequest) returns (AlatauCityResolveVisaAliasResponse) {
    option(mvp.rest_method_options) = {
      method: Post,
      authorization: Basic,
      request_marshal: JSON,
      response_unmarshal: JSON,
      max_request_timeout: 60,
      path: "/api/a2c/resolve-v2"
    };
  }

  rpc CalculateFeeVisaAlias(AlatauCityCalculateFeeVisaAliasRequest) returns (AlatauCityCalculateFeeVisaAliasResponse) {
    option(mvp.rest_method_options) = {
      method: Post,
      authorization: Basic,
      request_marshal: JSON,
      response_unmarshal: JSON,
      max_request_timeout: 60,
      path: "/api/a2c/calculate-fee"
    };
  }
}

message AlatauCityAliasInquiryRequest {
  message AliasInquiryRequest {
    int32  certificateVersion = 1;
    Data   data               = 2;
    bool   encrypted          = 3;
    string encryptedData      = 4;
  }

  message Data {
    repeated string aliases           = 1;
    repeated string paymentInstrTypes = 2;
  }

  AliasInquiryRequest body = 1;
}
message AlatauCityAliasInquiryResponse {
  message AliasInquiryResponse {
    Data data = 1;
    string encryptedData = 2;
    bool encrypted = 3;
    int32 code = 4;
    string message = 5;
    string validationErrors = 6;
  }

  message Data {
    // если aliasesFound 0 то поле isSuccess будет false
    bool isSuccess = 1;
    SuccessResponse successResponse = 2;
    string failureResponse = 3;
  }

  message SuccessResponse {
    Summary summary = 1;
    repeated Detail details = 2;
  }

  message Summary {
    int32 aliasesTotal = 1;
    int32 aliasesFound = 2;
    int32 aliasesNotFound = 3;
  }

  message Detail {
    string paymentInstrOwner = 1;
    repeated string paymentInstrTypes = 2;
    string aliasType = 3;
    string alias = 4;
  }

  AliasInquiryResponse alias_inquiry_response = 1;
}

message AlatauCityResolveVisaAliasRequest {
  message ResolveVisaAlias {
    int32  certificateVersion = 1;
    Data   data               = 2;
    bool   encrypted          = 3;
    string encryptedData      = 4;
  }

  message Data {
    string alias                  = 1;
    string businessApplicationId  = 2;
  }

  ResolveVisaAlias body = 1;
}

message AlatauCityResolveVisaAliasResponse {
  message ResolveVisaResponse {
    Data data = 1;
    string encryptedData = 2;
    bool encrypted = 3;
    int32 code = 4;
    string message = 5;
    int32 validationErrors = 6;
  }

  message Data {
    bool isSuccess = 1;
    SuccessResponse successResponse = 2;
    string failureResponse = 3;
    string destinationVisaAlias = 4 [(mvp.FieldLoggerLevel) = Hidden];
  }

  message SuccessResponse {
    string recipientPrimaryAccountNumber = 1;
    string recipientName = 2;
    string issuerName = 3;
    string cardType = 4;
    string address1 = 5;
    string address2 = 6;
    string city = 7;
    string country = 8;
    string postalCode = 9;
  }

  ResolveVisaResponse resolve_visa_response = 1;
}
message AlatauCityCalculateFeeRequest {
  CalculateFeeRequest body = 1;

  message CalculateFeeRequest {
    int32 certificateVersion = 1;
    Data data = 2;
    bool encrypted = 3;
    string encryptedData = 4;

    message Data {
      double amount = 1;
      string currencyCode = 2;
      DestinationCard destinationCard = 3;
      string requestId = 4;

      message DestinationCard {
        string number = 1 [(mvp.FieldLoggerLevel) = Hidden];
      }
    }
  }
}


message AlatauCityCalculateFeeResponse {
  CalculateFeeResponse fee_response = 1;

  message CalculateFeeResponse {
    int32 certificateVersion = 2;
    int32 code = 3;
    Data data = 4;
    bool encrypted = 5;
    string encryptedData = 6;
    string message = 7;
  }

  message Data {
    string createDate = 1;
    int32 currencyId = 2;
    string destinationCardCountry = 3;
    double fee = 4;
    string requestId = 5;
    string sessionCode = 6;
    VisaAlias visaAlias = 7;

    message VisaAlias {}
  }
}

message AlatauCityCalculateFeeVisaAliasRequest {
  message CalculateFeeVisaAliasRequest {
    int32  certificateVersion = 1;
    Data   data               = 2;
    bool   encrypted          = 3;
    string encryptedData      = 4;
  }

  message Data {
    double amount                  = 1;
    string currencyCode            = 2;
    int32  destinationType         = 3;
    string destinationVisaAlias    = 4 [(mvp.FieldLoggerLevel) = Hidden];
    string requestId               = 5;
  }

  CalculateFeeVisaAliasRequest body = 1;
}

message AlatauCityCalculateFeeVisaAliasResponse {
  message CalculateFeeVisaAliasResponse {
    Data data = 1;
    string encryptedData = 2;
    bool encrypted = 3;
    int32 code = 4;
    string message = 5;
    string validationErrors = 6;
  }

  message Data {
    double fee = 1;
    string destinationCardCountry = 2;
    int32  currencyId = 3;
    string createDate = 4;
    string visaAlias = 5;
    string requestId = 6;
    string  sessionCode = 7;
  }

  CalculateFeeVisaAliasResponse fee_response = 1;
}
message AlatauCityCompletePaymentRequest {
  CompletePaymentRequest body = 1;

  message CompletePaymentRequest {
    int32 certificateVersion = 1;
    Data data = 2;
    bool encrypted = 3;
    string encryptedData = 4;
  }

  message Data {
    string CInfo = 1;
    string recipientName = 2 [(mvp.FieldLoggerLevel) = Hidden];
    string recipientSurname = 3 [(mvp.FieldLoggerLevel) = Hidden];
    string requestId = 4;
    string sessionCode = 5;
  }
}
message AlatauCityCompletePaymentResponse {
  CompletePaymentResponse payment_response = 1;

  message CompletePaymentResponse {
    int32 certificateVersion = 1;
    int32 code = 2;
    Data data = 3;
    bool encrypted = 4;
    string encryptedData = 5;
    string message = 6;
  }

  message Data {
    double amount = 1;
    string EndDate = 2;
    Error error = 3;
    double fee = 4;
    string Reference = 5;
    string requestId = 6;
    string sessionCode = 7;
    int32 stage = 8;
    int32 status = 9;

    message Error {
      string Code = 1;
      string Message = 2;
    }
  }
}
message AlatauCityGetStatusPayoutRequest {
  GetStatusRequest body = 1;

  message GetStatusRequest {
    int32 certificateVersion = 1;
    Data data = 2;
    bool encrypted = 3;
    string encryptedData = 4;
  }

  message Data {
    string requestId = 1;
    string sessionCode = 2;
  }
}

message AlatauCityGetStatusPayoutResponse {
  GetStatusResponse status_response = 1;

  message GetStatusResponse {
    int32 code = 1;
    Data data = 2;
    bool encrypted = 3;
    string encryptedData = 4;
    string message = 5;

    message Error {
      string Code = 1;
      string Message = 2;
    }

    message Data {
      double amount = 1;
      string EndDate = 2;
      Error error = 3;
      double fee = 4;
      string Reference = 5;
      string requestId = 6;
      string sessionCode = 7;
      int32 stage = 8;
      int32 status = 9;
    }
  }
}
