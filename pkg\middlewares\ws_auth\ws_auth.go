package ws_auth

import (
	"crypto/rsa"
	"crypto/x509"
	"encoding/base64"
	"encoding/pem"
	"fmt"
	"git.local/sensitive/sdk/dog"
	"github.com/gin-gonic/gin"

	"github.com/golang-jwt/jwt/v5"

	"math/big"
	"net/http"
	"strings"
)

// WsAuthMiddleware проверяет наличие и правильность Bearer-токена
func WsAuthMiddleware(pubKeyPem string) gin.HandlerFunc {
	return func(c *gin.Context) {
		_, span := dog.CreateSpan(c.Request.Context(), "WsAuthMiddleware")
		defer span.End()

		//"Sec-WebSocket-Protocol" для javascript от фронта, Authorization для постман тестов локально
		authHeaderToken := c.GetHeader("Sec-WebSocket-Protocol")
		if authHeaderToken == "" {
			authHeader := c.GetHeader("Authorization")
			if authHeader == "" {
				c.<PERSON>(http.StatusUnauthorized, gin.H{"error": "Authorization header missing or invalid"})
				c.Abort()

				return
			}

			tokenParts := strings.Split(authHeader, " ")
			if len(tokenParts) != 2 || tokenParts[0] != "Bearer" {
				c.JSON(http.StatusUnauthorized, gin.H{"error": "Authorization header format must be Bearer <token>"})
				c.Abort()

				return
			}

			authHeaderToken = tokenParts[1]
		}

		token, err := jwt.Parse(authHeaderToken, func(token *jwt.Token) (interface{}, error) {
			if _, ok := token.Method.(*jwt.SigningMethodRSA); !ok {
				return nil, fmt.Errorf("unexpected signing method: %v", token.Header["alg"])
			}

			pem, err := convertPemToRsaKey(pubKeyPem)
			if err != nil {
				return nil, fmt.Errorf("failed to decode a public key: %v", err)
			}

			pubKey, err := jwt.ParseRSAPublicKeyFromPEM(pem)
			if err != nil {
				return nil, fmt.Errorf("failed to parse RSA public key: %v", err)
			}

			return pubKey, nil
		})

		if err != nil {
			c.JSON(http.StatusUnauthorized, gin.H{"error": "Invalid token", "details": err.Error()})
			c.Abort()

			return
		}

		if _, ok := token.Claims.(jwt.MapClaims); !ok || !token.Valid {
			c.JSON(http.StatusUnauthorized, gin.H{"error": "Invalid token"})
			c.Abort()

			return
		}
	}
}

// convertPemToRsaKey - base64 string pem -> byte rsa-key для проверки подписей
func convertPemToRsaKey(nStr string) ([]byte, error) {
	nBytes, err := base64.RawURLEncoding.DecodeString(nStr)
	if err != nil {
		return nil, err
	}

	n := new(big.Int).SetBytes(nBytes)

	e := 65537

	pubKey := &rsa.PublicKey{
		N: n,
		E: e,
	}

	pubKeyBytes, err := x509.MarshalPKIXPublicKey(pubKey)
	if err != nil {
		return nil, err
	}

	rsaKey := pem.EncodeToMemory(&pem.Block{
		Type:  "PUBLIC KEY",
		Bytes: pubKeyBytes,
	})

	return rsaKey, nil
}
