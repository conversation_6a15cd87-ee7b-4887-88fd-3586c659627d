edition = "2023";

package processing.fiscalization.fiscalization;

option go_package = "git.local/sensitive/innerpb/processing/grpc";

import "google/protobuf/timestamp.proto";
import "google/protobuf/empty.proto";

service Fiscalization {
  rpc MakeFiscalizationV1(MakeFiscalizationRequestV1) returns (google.protobuf.Empty) {}
  rpc GetFiscalInfoByTransactionIDV1(GetFiscalInfoByTransactionIDRequestV1) returns (GetFiscalInfoByTransactionIDResponseV1) {}

  // jobs
  rpc ManageShifts(google.protobuf.Empty) returns (google.protobuf.Empty);
  rpc FinalizeFiscalizations(google.protobuf.Empty) returns (google.protobuf.Empty);
}

message MakeFiscalizationRequestV1 {
  uint64 transaction_id = 1;
  google.protobuf.Timestamp transaction_created_at = 2;
  double transaction_total_amount = 3;
  double upper_commission_amount = 4;
  string description = 5;
  uint64 customer_id = 6;
}

message GetFiscalInfoByTransactionIDRequestV1 {
  uint64 transaction_id = 1;
}

message GetFiscalInfoByTransactionIDResponseV1 {
  string fiscal_number = 1;
  string fiscal_check_url = 2;
}