syntax = "proto3";

option go_package = "git.local/sensitive/innerpb/processing/events";

import "google/protobuf/timestamp.proto";
import "mvp/proto/events.proto";

message CreateCommission {
  option (mvp.events.is_event) = true;
  uint64 acquirer_id = 1;
  uint64 project_id = 2;
  uint64 merchant_id = 3;
  uint64 transaction_type_id = 4;
  uint64 ips_id = 5;
  uint64 issuer_id = 6;
  uint64 country_id = 7;
}

message CalculateAndSaveCommission {
  option (mvp.events.is_event) = true;
  uint64 acquirer_id = 1;
  uint64 project_id = 2;
  uint64 merchant_id = 3;
  uint64 aggregated_type_id = 4;
  uint64 ips_id = 5;
  uint64 issuer_id = 6;
  uint64 country_id = 7;
  uint64 transaction_id = 8;
  double transaction_amount = 9;
  google.protobuf.Timestamp transaction_created_at = 10;
}