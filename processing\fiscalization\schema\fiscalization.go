package schema

import (
	"fmt"
	gorpc "git.local/sensitive/innerpb/processing/grpc"
	"git.local/sensitive/processing/fiscalization/model"
	"github.com/go-playground/validator/v10"
	"time"
)

const (
	DefaultQuantity = 1000
	DefaultOrder    = "id ASC"
	CoinsMaxValue   = 100
	Description     = "Услуги платежной организации"
)

func ValidateMakeFiscalizationRequest(v1 *gorpc.MakeFiscalizationRequestV1) error {
	if v1.GetTransactionId() == 0 {
		return fmt.Errorf("empty transaction id")
	}

	return nil
}

type (
	FiscalizationRequest struct {
		DateTimeAsString string           `json:"date_time_as_string" validate:"required"`
		Operation        string           `json:"operation" validate:"required"`
		Payments         []PaymentRequest `json:"payments"`
		Items            []Item           `json:"items"`
		CustomerInfo     CustomerInfo     `json:"customer_info"`
	}

	Sum struct {
		Bills int64 `json:"bills" validate:"required"`
		Coins int64 `json:"coins"`
	}

	PaymentRequest struct {
		Sum  Sum    `json:"sum"`
		Type string `json:"type" validate:"required"`
	}

	Item struct {
		Name            string `json:"name" validate:"required"`
		Price           Sum    `json:"price"`
		Quantity        int    `json:"quantity" validate:"required"`
		Sum             Sum    `json:"sum"`
		MeasureUnitCode string `json:"measure_unit_code" validate:"required"`
		Type            string `json:"type" validate:"required"`
	}

	CustomerInfo struct {
		Email      string `json:"email"`
		CustomerID uint64 `json:"customer_id"`
	}
)

func (fr *FiscalizationRequest) Validate() error {
	return validator.New().Struct(fr)
}

func NewFiscalizationRequestFromRaw(v1 *gorpc.MakeFiscalizationRequestV1) FiscalizationRequest {
	bills, coins := splitFloat(v1.GetUpperCommissionAmount())

	return FiscalizationRequest{
		DateTimeAsString: v1.TransactionCreatedAt.AsTime().Format("2006-01-02T15:04:05"),
		Operation:        "OPERATION_SELL",
		Payments: []PaymentRequest{{
			Sum: Sum{
				Bills: bills,
				Coins: coins,
			},
			Type: "PAYMENT_CARD",
		}},
		Items: []Item{{
			Name: Description,
			Price: Sum{
				Bills: bills,
				Coins: coins,
			},
			Quantity: DefaultQuantity,
			Sum: Sum{
				Bills: bills,
				Coins: coins,
			},
			MeasureUnitCode: "796",
			Type:            "COMMODITY",
		},
		},
		CustomerInfo: CustomerInfo{
			CustomerID: v1.GetCustomerId(),
		},
	}
}

func NewFiscalizationRequestFromModel(fiscalization model.Fiscalization) FiscalizationRequest {
	bills, coins := splitFloat(fiscalization.Amount)

	return FiscalizationRequest{
		DateTimeAsString: fiscalization.CreatedAt.Format("2006-01-02T15:04:05"),
		Operation:        "OPERATION_SELL",
		Payments: []PaymentRequest{{
			Sum: Sum{
				Bills: bills,
				Coins: coins,
			},
			Type: "PAYMENT_CARD",
		}},
		Items: []Item{{
			Name: Description,
			Price: Sum{
				Bills: bills,
				Coins: coins,
			},
			Quantity: DefaultQuantity,
			Sum: Sum{
				Bills: bills,
				Coins: coins,
			},
			MeasureUnitCode: "796",
			Type:            "COMMODITY",
		},
		},
	}
}

type (
	FiscalizationResponse struct {
		Data Data `json:"data"`
	}

	Cashbox struct {
		KKMID        uint64 `json:"kkm_id"`
		SerialNumber string `json:"serial_number"`
		StateNumber  string `json:"state_number"`
	}

	Cashier struct {
		Code  int    `json:"code"`
		Title string `json:"title"`
	}

	Ofd struct {
		Title   string `json:"title"`
		Website string `json:"web_site"`
	}

	Taxpayer struct {
		Code         string      `json:"code"`
		StoreAddress string      `json:"store_address"`
		Title        string      `json:"title"`
		VATNumber    interface{} `json:"vat_number"`
		VATSeries    interface{} `json:"vat_series"`
	}

	Tax struct {
		Percent interface{} `json:"percent"`
		Sum     Sum         `json:"sum"`
	}

	ItemResponse struct {
		ExciseStamp  interface{} `json:"excise_stamp"`
		MeasureCode  string      `json:"measure_code"`
		MeasureTitle string      `json:"measure_title"`
		Price        Sum         `json:"price"`
		Quantity     int         `json:"quantity"`
		Sum          Sum         `json:"sum"`
		Tax          Tax         `json:"tax"`
		Title        string      `json:"title"`
		Type         string      `json:"type"`
	}

	Payment struct {
		PaymentDetails interface{} `json:"payment_details"`
		Sum            Sum         `json:"sum"`
		Type           string      `json:"type"`
	}

	Ticket struct {
		Change              Sum            `json:"change"`
		CustomerInfo        interface{}    `json:"customer_info"`
		Date                string         `json:"date"`
		FiscalNumber        string         `json:"fiscal_number"`
		Items               []ItemResponse `json:"items"`
		Number              uint64         `json:"number"`
		OfflineFiscalNumber *string        `json:"offline_fiscal_number"`
		Operation           string         `json:"operation"`
		Payments            []Payment      `json:"payments"`
		Sum                 Sum            `json:"sum"`
		Taxes               []Tax          `json:"taxes"`
		URL                 string         `json:"url"`
	}

	Data struct {
		DocumentType string `json:"document_type"`
		QRCode       string `json:"qr_code"`
		TicketJSON   struct {
			Cashbox  Cashbox  `json:"cashbox"`
			Cashier  Cashier  `json:"cashier"`
			Ofd      Ofd      `json:"ofd"`
			Taxpayer Taxpayer `json:"taxpayer"`
			Ticket   Ticket   `json:"ticket"`
		} `json:"ticket_json"`
		TicketText []string `json:"ticket_text"`
	}
)

func NewFiscalizationDBModel(
	request *gorpc.MakeFiscalizationRequestV1,
	statusID uint64,
) model.Fiscalization {
	return model.Fiscalization{
		TransactionID: request.GetTransactionId(),
		Amount:        request.GetUpperCommissionAmount(),
		StatusID:      statusID,
	}
}

func ValidateGetFiscalizationRequest(v1 *gorpc.GetFiscalInfoByTransactionIDRequestV1) error {
	if v1.GetTransactionId() == 0 {
		return fmt.Errorf("empty transaction id")
	}

	return nil
}

func NewRawGetPaymentFiscalizationsResponse(
	fiscalization *model.Fiscalization,
) *gorpc.GetFiscalInfoByTransactionIDResponseV1 {
	url := fiscalization.FiscalCheckURL
	number := fiscalization.FiscalNumber

	return &gorpc.GetFiscalInfoByTransactionIDResponseV1{
		FiscalNumber:   &number,
		FiscalCheckUrl: &url,
	}
}

type FiscalizationFilters struct {
	TimeTo        time.Time `form:"time_to" json:"time_to,omitempty"`
	TransactionID uint64    `form:"transaction_id" json:"transaction_id,omitempty"`
	StatusID      uint64    `form:"status_id" json:"status_id,omitempty"`
	SortBy        string    `form:"sort_by" validate:"omitempty" json:"sort_by,omitempty"`
}

func splitFloat(value float64) (int64, int64) {
	integer := int64(value)
	mantissa := (value - float64(integer)) * CoinsMaxValue

	finalMantissa := int64(mantissa)

	if mantissa > float64(finalMantissa) && (mantissa-float64(finalMantissa)) >= 0.5 {
		finalMantissa += 1
	}

	if finalMantissa >= CoinsMaxValue {
		integer += 1
		finalMantissa = 0
	}

	return integer, finalMantissa
}
