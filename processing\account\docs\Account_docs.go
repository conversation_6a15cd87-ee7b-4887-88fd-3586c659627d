// Package docs Code generated by swaggo/swag. DO NOT EDIT
package docs

import "github.com/swaggo/swag"

const docTemplateAccount = `{
    "schemes": {{ marshal .Schemes }},
    "swagger": "2.0",
    "info": {
        "description": "{{escape .Description}}",
        "title": "{{.Title}}",
        "contact": {},
        "version": "{{.Version}}"
    },
    "host": "{{.Host}}",
    "basePath": "{{.BasePath}}",
    "paths": {
        "/api/v1/manager/account/balance/{number}": {
            "get": {
                "security": [
                    {
                        "bearerAuth": []
                    }
                ],
                "description": "Получение баланса транзитного счета по его номеру",
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Get Account Balance By Number"
                ],
                "summary": "Получение баланса счета по номеру",
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/middlewares.Response-schema_AccountBalance"
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/middlewares.Response-middlewares_Empty"
                        }
                    }
                }
            }
        },
        "/api/v1/manager/accounts": {
            "get": {
                "security": [
                    {
                        "bearerAuth": []
                    }
                ],
                "description": "Получение информации о всех транзитных счетах",
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Get All Accounts"
                ],
                "summary": "Получение всех счетов",
                "parameters": [
                    {
                        "type": "integer",
                        "description": "bank_id",
                        "name": "bank_id",
                        "in": "query"
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/middlewares.Response-array_delivery_http_v1_getAccounts_response"
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/middlewares.Response-middlewares_Empty"
                        }
                    }
                }
            }
        },
        "/api/v1/manager/accounts/acquirers": {
            "get": {
                "security": [
                    {
                        "bearerAuth": []
                    }
                ],
                "description": "Получение всех банков-эквайеров аккаунта",
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Get All Account Acquirers"
                ],
                "summary": "Получение всех банков-эквайеров аккаунта",
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/middlewares.Response-schema_AccountAcquirer"
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/middlewares.Response-middlewares_Empty"
                        }
                    }
                }
            }
        },
        "/api/v1/manager/accounts/with-balance": {
            "get": {
                "security": [
                    {
                        "bearerAuth": []
                    }
                ],
                "description": "Получение информации о всех транзитных счетах",
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Get All Accounts"
                ],
                "summary": "Получение всех счетов",
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/middlewares.Response-array_delivery_http_v1_getAccountsWithBalance_response"
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/middlewares.Response-middlewares_Empty"
                        }
                    }
                }
            }
        },
        "/api/v1/manager/accounts/{id}/config": {
            "get": {
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Get Account Config"
                ],
                "summary": "Получение конфигурации аккаунта в расшифрованном виде",
                "parameters": [
                    {
                        "type": "integer",
                        "description": "Account ID",
                        "name": "id",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/middlewares.Response-schema_DecryptedAccountConfig"
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/middlewares.Response-middlewares_Empty"
                        }
                    }
                }
            },
            "patch": {
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Add Account Config"
                ],
                "summary": "Сохранение конфигурации аккаунта в зашифрованном виде",
                "parameters": [
                    {
                        "description": "Key and Value",
                        "name": "data",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "type": "object",
                            "additionalProperties": true
                        }
                    },
                    {
                        "type": "integer",
                        "description": "Account ID",
                        "name": "id",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/middlewares.Response-string"
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/middlewares.Response-middlewares_Empty"
                        }
                    }
                }
            }
        },
        "/api/v1/manager/transfer/accept": {
            "post": {
                "security": [
                    {
                        "bearerAuth": []
                    }
                ],
                "description": "Подтверждение заказов в CRM на вывод средств с транзитного счета на расчетный счет мерчанта",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Accept transfer CRM"
                ],
                "summary": "Подтверждение заказов в CRM",
                "operationId": "PostAcceptTransfers",
                "parameters": [
                    {
                        "description": "AcceptTransferRequest Data",
                        "name": "data",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/schema.AcceptTransferRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/middlewares.Response-schema_AcceptTransferResponse"
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/middlewares.Response-middlewares_Empty"
                        }
                    }
                }
            }
        },
        "/api/v1/manager/transfer/cancel": {
            "post": {
                "security": [
                    {
                        "bearerAuth": []
                    }
                ],
                "description": "Отклонение заказов в CRM на вывод средств с транзитного счета на расчетный счет мерчанта",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Cancel transfer CRM"
                ],
                "summary": "Отклонение заказов в CRM",
                "operationId": "PostCancelTransfers",
                "parameters": [
                    {
                        "description": "CancelTransferRequest Data",
                        "name": "data",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/schema.CancelTransferRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/middlewares.Response-schema_CancelTransferResponse"
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/middlewares.Response-middlewares_Empty"
                        }
                    }
                }
            }
        },
        "/api/v1/manager/transfer/create": {
            "post": {
                "security": [
                    {
                        "bearerAuth": []
                    }
                ],
                "description": "Создание нового заказа в CRM на вывод средств с транзитного счета на расчетный счет мерчанта",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Create transfer CRM"
                ],
                "summary": "Создание заказа в CRM",
                "parameters": [
                    {
                        "description": "CreateTransferRequest Data",
                        "name": "data",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/schema.CreateTransferRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/middlewares.Response-schema_CreateTransferResponse"
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/middlewares.Response-middlewares_Empty"
                        }
                    }
                }
            }
        },
        "/api/v1/manager/transfer/filtered": {
            "get": {
                "security": [
                    {
                        "bearerAuth": []
                    }
                ],
                "description": "Получение в CRM информации о заказах на вывод по фильтру",
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Get transfers By Filters in CRM"
                ],
                "summary": "Получение в CRM заказов по фильтру",
                "parameters": [
                    {
                        "type": "string",
                        "name": "account_number",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "name": "amount_from",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "name": "amount_to",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "name": "date_from",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "name": "date_to",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "name": "merchant_id",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "name": "project_id",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "name": "status_id",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "name": "type_code",
                        "in": "query"
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/middlewares.Response-array_model_Transfer"
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/middlewares.Response-middlewares_Empty"
                        }
                    }
                }
            }
        },
        "/api/v1/manager/transfer/merchant/{id}": {
            "get": {
                "security": [
                    {
                        "bearerAuth": []
                    }
                ],
                "description": "Получение в CRM информации о всех заказах на вывод по ID мерчанта",
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Get transfers By Merchant ID in CRM"
                ],
                "summary": "Получение в CRM заказов по ID мерчанта",
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/middlewares.Response-array_model_Transfer"
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/middlewares.Response-middlewares_Empty"
                        }
                    }
                }
            }
        },
        "/api/v1/manager/transfer/project/{id}": {
            "get": {
                "security": [
                    {
                        "bearerAuth": []
                    }
                ],
                "description": "Получение в CRM информации о заказах на вывод по ID проекта",
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Get transfers By Project ID in CRM"
                ],
                "summary": "Получение в CRM заказов по ID проекта",
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/middlewares.Response-array_model_Transfer"
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/middlewares.Response-middlewares_Empty"
                        }
                    }
                }
            }
        },
        "/api/v1/manager/transfer/redo": {
            "post": {
                "security": [
                    {
                        "bearerAuth": []
                    }
                ],
                "description": "Переотправка заказа через CRM на вывод средств с транзитного счета на расчетный счет мерчанта",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Redo transfer CRM"
                ],
                "summary": "Переотправка заказа через CRM",
                "parameters": [
                    {
                        "description": "RedoTransferRequest Data",
                        "name": "data",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/schema.RedoTransferRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/middlewares.Response-schema_TransferResponse"
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/middlewares.Response-middlewares_Empty"
                        }
                    }
                }
            }
        },
        "/api/v1/manager/transfer/{id}": {
            "get": {
                "security": [
                    {
                        "bearerAuth": []
                    }
                ],
                "description": "Получение в CRM информации о заказе на вывод по ID",
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Get Order By ID in CRM"
                ],
                "summary": "Получение в CRM заказа по ID",
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/middlewares.Response-model_Transfer"
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/middlewares.Response-middlewares_Empty"
                        }
                    }
                }
            }
        },
        "/api/v1/manager/transfer/{id}/accept": {
            "post": {
                "security": [
                    {
                        "bearerAuth": []
                    }
                ],
                "description": "Подтверждение заказа",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Accept transfer CRM"
                ],
                "summary": "Подтверждение заказа",
                "operationId": "PostAcceptTransferByID",
                "parameters": [
                    {
                        "type": "integer",
                        "description": "TransactionID",
                        "name": "id",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/middlewares.Response-schema_TransferResponse"
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/middlewares.Response-middlewares_Empty"
                        }
                    }
                }
            }
        },
        "/api/v1/manager/transfer/{id}/cancel": {
            "post": {
                "security": [
                    {
                        "bearerAuth": []
                    }
                ],
                "description": "Отклонение заказа",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Cancel transfer CRM"
                ],
                "summary": "Отклонение заказа",
                "operationId": "PostCancelTransferByID",
                "parameters": [
                    {
                        "type": "integer",
                        "description": "transferID",
                        "name": "id",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/middlewares.Response-schema_TransferResponse"
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/middlewares.Response-middlewares_Empty"
                        }
                    }
                }
            }
        },
        "/api/v1/merchant/transfer": {
            "get": {
                "security": [
                    {
                        "bearerAuth": []
                    }
                ],
                "description": "Получение мерчантом информации о всех его заказах на вывод",
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Get Merchant's transfers"
                ],
                "summary": "Получение мерчантом всех его заказов",
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/middlewares.Response-array_model_Transfer"
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/middlewares.Response-middlewares_Empty"
                        }
                    }
                }
            }
        },
        "/api/v1/merchant/transfer/create": {
            "post": {
                "security": [
                    {
                        "bearerAuth": []
                    }
                ],
                "description": "Создание мерчантом нового заказа на вывод средств с транзитного счета на свой расчетный счет",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Create transfer Merchant"
                ],
                "summary": "Создание заказа в ЛК мерчанта",
                "parameters": [
                    {
                        "description": "CreateTransferRequest Data",
                        "name": "data",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/schema.CreateTransferRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/middlewares.Response-schema_CreateTransferResponse"
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/middlewares.Response-middlewares_Empty"
                        }
                    }
                }
            }
        },
        "/api/v1/merchant/transfer/filtered/": {
            "get": {
                "security": [
                    {
                        "bearerAuth": []
                    }
                ],
                "description": "Получение мерчантом информации о его заказах на вывод по фильтру",
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Get Merchant's Transfers By Filters"
                ],
                "summary": "Получение мерчантом его заказов по фильтру",
                "parameters": [
                    {
                        "type": "string",
                        "name": "account_number",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "name": "amount_from",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "name": "amount_to",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "name": "date_from",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "name": "date_to",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "name": "merchant_id",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "name": "project_id",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "name": "status_id",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "name": "type_code",
                        "in": "query"
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/middlewares.Response-array_model_Transfer"
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/middlewares.Response-middlewares_Empty"
                        }
                    }
                }
            }
        },
        "/api/v1/merchant/transfer/project/{id}": {
            "get": {
                "security": [
                    {
                        "bearerAuth": []
                    }
                ],
                "description": "Получение мерчантом информации о его заказах на вывод по ID проекта",
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Get Merchant's transfers By Project ID"
                ],
                "summary": "Получение мерчантом его заказов по ID проекта",
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/middlewares.Response-array_model_Transfer"
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/middlewares.Response-middlewares_Empty"
                        }
                    }
                }
            }
        },
        "/api/v1/merchant/transfer/{id}": {
            "get": {
                "security": [
                    {
                        "bearerAuth": []
                    }
                ],
                "description": "Получение мерчантом информации о его заказе на вывод по ID",
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Get Merchant's Transfer By ID"
                ],
                "summary": "Получение мерчантом его заказа по ID",
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/middlewares.Response-model_Transfer"
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/middlewares.Response-middlewares_Empty"
                        }
                    }
                }
            }
        },
        "/api/v1/transfer/statuses": {
            "get": {
                "security": [
                    {
                        "bearerAuth": []
                    }
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "transfer-statuses"
                ],
                "summary": "Получение всех статусов трансферов",
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/middlewares.Response-array_model_TransferStatus"
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/middlewares.Response-middlewares_Empty"
                        }
                    }
                }
            }
        },
        "/api/v1/transfer/types": {
            "get": {
                "security": [
                    {
                        "bearerAuth": []
                    }
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "transfer-types"
                ],
                "summary": "Получение всех типов трансферов",
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/middlewares.Response-array_model_TransferType"
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/middlewares.Response-middlewares_Empty"
                        }
                    }
                }
            }
        }
    },
    "definitions": {
        "delivery_http_v1.getAccounts.response": {
            "type": "object",
            "required": [
                "account_number",
                "id"
            ],
            "properties": {
                "account_number": {
                    "type": "string"
                },
                "id": {
                    "type": "integer"
                }
            }
        },
        "delivery_http_v1.getAccountsWithBalance.response": {
            "type": "object",
            "required": [
                "account_number",
                "amount",
                "bank",
                "id"
            ],
            "properties": {
                "account_number": {
                    "type": "string"
                },
                "amount": {
                    "type": "number"
                },
                "bank": {
                    "type": "object",
                    "required": [
                        "bank_id",
                        "bank_name"
                    ],
                    "properties": {
                        "bank_id": {
                            "type": "string"
                        },
                        "bank_name": {
                            "type": "string"
                        }
                    }
                },
                "config": {
                    "type": "object",
                    "additionalProperties": {}
                },
                "id": {
                    "type": "integer"
                }
            }
        },
        "middlewares.Empty": {
            "type": "object"
        },
        "middlewares.Response-array_delivery_http_v1_getAccountsWithBalance_response": {
            "type": "object",
            "required": [
                "message",
                "result",
                "status"
            ],
            "properties": {
                "message": {
                    "type": "string"
                },
                "result": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/delivery_http_v1.getAccountsWithBalance.response"
                    }
                },
                "status": {
                    "type": "boolean"
                }
            }
        },
        "middlewares.Response-array_delivery_http_v1_getAccounts_response": {
            "type": "object",
            "required": [
                "message",
                "result",
                "status"
            ],
            "properties": {
                "message": {
                    "type": "string"
                },
                "result": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/delivery_http_v1.getAccounts.response"
                    }
                },
                "status": {
                    "type": "boolean"
                }
            }
        },
        "middlewares.Response-array_model_Transfer": {
            "type": "object",
            "required": [
                "message",
                "result",
                "status"
            ],
            "properties": {
                "message": {
                    "type": "string"
                },
                "result": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/model.Transfer"
                    }
                },
                "status": {
                    "type": "boolean"
                }
            }
        },
        "middlewares.Response-array_model_TransferStatus": {
            "type": "object",
            "required": [
                "message",
                "result",
                "status"
            ],
            "properties": {
                "message": {
                    "type": "string"
                },
                "result": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/model.TransferStatus"
                    }
                },
                "status": {
                    "type": "boolean"
                }
            }
        },
        "middlewares.Response-array_model_TransferType": {
            "type": "object",
            "required": [
                "message",
                "result",
                "status"
            ],
            "properties": {
                "message": {
                    "type": "string"
                },
                "result": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/model.TransferType"
                    }
                },
                "status": {
                    "type": "boolean"
                }
            }
        },
        "middlewares.Response-middlewares_Empty": {
            "type": "object",
            "required": [
                "message",
                "result",
                "status"
            ],
            "properties": {
                "message": {
                    "type": "string"
                },
                "result": {
                    "$ref": "#/definitions/middlewares.Empty"
                },
                "status": {
                    "type": "boolean"
                }
            }
        },
        "middlewares.Response-model_Transfer": {
            "type": "object",
            "required": [
                "message",
                "result",
                "status"
            ],
            "properties": {
                "message": {
                    "type": "string"
                },
                "result": {
                    "$ref": "#/definitions/model.Transfer"
                },
                "status": {
                    "type": "boolean"
                }
            }
        },
        "middlewares.Response-schema_AcceptTransferResponse": {
            "type": "object",
            "required": [
                "message",
                "result",
                "status"
            ],
            "properties": {
                "message": {
                    "type": "string"
                },
                "result": {
                    "$ref": "#/definitions/schema.AcceptTransferResponse"
                },
                "status": {
                    "type": "boolean"
                }
            }
        },
        "middlewares.Response-schema_AccountAcquirer": {
            "type": "object",
            "required": [
                "message",
                "result",
                "status"
            ],
            "properties": {
                "message": {
                    "type": "string"
                },
                "result": {
                    "$ref": "#/definitions/schema.AccountAcquirer"
                },
                "status": {
                    "type": "boolean"
                }
            }
        },
        "middlewares.Response-schema_AccountBalance": {
            "type": "object",
            "required": [
                "message",
                "result",
                "status"
            ],
            "properties": {
                "message": {
                    "type": "string"
                },
                "result": {
                    "$ref": "#/definitions/schema.AccountBalance"
                },
                "status": {
                    "type": "boolean"
                }
            }
        },
        "middlewares.Response-schema_CancelTransferResponse": {
            "type": "object",
            "required": [
                "message",
                "result",
                "status"
            ],
            "properties": {
                "message": {
                    "type": "string"
                },
                "result": {
                    "$ref": "#/definitions/schema.CancelTransferResponse"
                },
                "status": {
                    "type": "boolean"
                }
            }
        },
        "middlewares.Response-schema_CreateTransferResponse": {
            "type": "object",
            "required": [
                "message",
                "result",
                "status"
            ],
            "properties": {
                "message": {
                    "type": "string"
                },
                "result": {
                    "$ref": "#/definitions/schema.CreateTransferResponse"
                },
                "status": {
                    "type": "boolean"
                }
            }
        },
        "middlewares.Response-schema_DecryptedAccountConfig": {
            "type": "object",
            "required": [
                "message",
                "result",
                "status"
            ],
            "properties": {
                "message": {
                    "type": "string"
                },
                "result": {
                    "$ref": "#/definitions/schema.DecryptedAccountConfig"
                },
                "status": {
                    "type": "boolean"
                }
            }
        },
        "middlewares.Response-schema_TransferResponse": {
            "type": "object",
            "required": [
                "message",
                "result",
                "status"
            ],
            "properties": {
                "message": {
                    "type": "string"
                },
                "result": {
                    "$ref": "#/definitions/schema.TransferResponse"
                },
                "status": {
                    "type": "boolean"
                }
            }
        },
        "middlewares.Response-string": {
            "type": "object",
            "required": [
                "message",
                "result",
                "status"
            ],
            "properties": {
                "message": {
                    "type": "string"
                },
                "result": {
                    "type": "string"
                },
                "status": {
                    "type": "boolean"
                }
            }
        },
        "model.Transfer": {
            "type": "object",
            "properties": {
                "account_id": {
                    "type": "integer"
                },
                "acquirer_id": {
                    "type": "integer"
                },
                "amount": {
                    "type": "number"
                },
                "bank_order_id": {
                    "type": "string"
                },
                "bank_reference_id": {
                    "type": "string"
                },
                "country_code_id": {
                    "type": "integer"
                },
                "created_at": {
                    "type": "string"
                },
                "description": {
                    "type": "string"
                },
                "entity_type_id": {
                    "type": "integer"
                },
                "finished_at": {
                    "type": "string"
                },
                "foreign_id": {
                    "type": "string"
                },
                "id": {
                    "type": "integer"
                },
                "merchant_account": {
                    "type": "string"
                },
                "merchant_beneficiary_сode": {
                    "type": "string"
                },
                "merchant_bin": {
                    "type": "string"
                },
                "merchant_id": {
                    "type": "integer"
                },
                "payment_purpose_code": {
                    "type": "string"
                },
                "project_id": {
                    "type": "integer"
                },
                "status_id": {
                    "type": "integer"
                },
                "transfer_status": {
                    "$ref": "#/definitions/model.TransferStatus"
                },
                "transfer_type": {
                    "$ref": "#/definitions/model.TransferType"
                },
                "transfer_type_id": {
                    "type": "integer"
                },
                "updated_at": {
                    "type": "string"
                }
            }
        },
        "model.TransferStatus": {
            "type": "object",
            "properties": {
                "code": {
                    "$ref": "#/definitions/model.TransferStatusCode"
                },
                "created_at": {
                    "type": "string"
                },
                "id": {
                    "type": "integer"
                },
                "name": {
                    "type": "string"
                },
                "updated_at": {
                    "type": "string"
                }
            }
        },
        "model.TransferStatusCode": {
            "type": "string",
            "enum": [
                "new",
                "authorized",
                "success",
                "canceled",
                "failed",
                "holded",
                "error",
                "in_progress",
                "in_redo"
            ],
            "x-enum-varnames": [
                "StatusNew",
                "StatusAuthorized",
                "StatusSuccess",
                "StatusCanceled",
                "StatusFailed",
                "StatusHold",
                "StatusError",
                "StatusInProgress",
                "StatusInRedo"
            ]
        },
        "model.TransferType": {
            "type": "object",
            "properties": {
                "code": {
                    "$ref": "#/definitions/model.TransferTypeCode"
                },
                "created_at": {
                    "type": "string"
                },
                "id": {
                    "type": "integer"
                },
                "name": {
                    "type": "string"
                },
                "updated_at": {
                    "type": "string"
                }
            }
        },
        "model.TransferTypeCode": {
            "type": "string",
            "enum": [
                "in",
                "out"
            ],
            "x-enum-varnames": [
                "TypeIn",
                "TypeOut"
            ]
        },
        "schema.AcceptTransferRequest": {
            "type": "object",
            "required": [
                "transfer_ids"
            ],
            "properties": {
                "transfer_ids": {
                    "type": "array",
                    "items": {
                        "type": "integer"
                    }
                }
            }
        },
        "schema.AcceptTransferResponse": {
            "type": "object",
            "properties": {
                "responses": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/schema.TransferResponse"
                    }
                }
            }
        },
        "schema.AccountAcquirer": {
            "type": "object",
            "properties": {
                "bank_codes": {
                    "type": "array",
                    "items": {
                        "type": "string"
                    }
                }
            }
        },
        "schema.AccountBalance": {
            "type": "object",
            "properties": {
                "balance": {
                    "type": "number"
                }
            }
        },
        "schema.CancelTransferRequest": {
            "type": "object",
            "required": [
                "transfer_ids"
            ],
            "properties": {
                "transfer_ids": {
                    "type": "array",
                    "items": {
                        "type": "integer"
                    }
                }
            }
        },
        "schema.CancelTransferResponse": {
            "type": "object",
            "properties": {
                "responses": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/schema.TransferResponse"
                    }
                }
            }
        },
        "schema.CreateTransferRequest": {
            "type": "object",
            "required": [
                "account_number",
                "balance_owner_bin",
                "balance_owner_id",
                "beneficiary_code",
                "description",
                "payment_purpose_code",
                "recipient_account",
                "transfer_amount"
            ],
            "properties": {
                "account_number": {
                    "type": "string"
                },
                "balance_owner_bin": {
                    "type": "string"
                },
                "balance_owner_id": {
                    "type": "integer"
                },
                "beneficiary_code": {
                    "type": "string"
                },
                "description": {
                    "type": "string"
                },
                "payment_purpose_code": {
                    "type": "string"
                },
                "recipient_account": {
                    "type": "string"
                },
                "transfer_amount": {
                    "type": "number"
                }
            }
        },
        "schema.CreateTransferResponse": {
            "type": "object",
            "properties": {
                "bank_order_id": {
                    "type": "string"
                },
                "bank_reference_id": {
                    "type": "string"
                },
                "status_id": {
                    "type": "integer"
                },
                "transfer_id": {
                    "type": "integer"
                }
            }
        },
        "schema.DecryptedAccountConfig": {
            "type": "object",
            "additionalProperties": {}
        },
        "schema.RedoTransferRequest": {
            "type": "object",
            "properties": {
                "merchant_id": {
                    "type": "integer"
                },
                "project_id": {
                    "type": "integer"
                },
                "transfer_id": {
                    "type": "integer"
                },
                "transit_account": {
                    "type": "string"
                }
            }
        },
        "schema.TransferResponse": {
            "type": "object",
            "properties": {
                "status_id": {
                    "type": "integer"
                },
                "transfer_id": {
                    "type": "integer"
                }
            }
        }
    },
    "securityDefinitions": {
        "bearerAuth": {
            "type": "apiKey",
            "name": "Authorization",
            "in": "header"
        }
    }
}`

// SwaggerInfoAccount holds exported Swagger Info so clients can modify it
var SwaggerInfoAccount = &swag.Spec{
	Version:          "1.0.0",
	Host:             "api-dev.processing.kz",
	BasePath:         "/account",
	Schemes:          []string{"https", "http"},
	Title:            "account",
	Description:      "account processing",
	InfoInstanceName: "Account",
	SwaggerTemplate:  docTemplateAccount,
	LeftDelim:        "{{",
	RightDelim:       "}}",
}

func init() {
	swag.Register(SwaggerInfoAccount.InstanceName(), SwaggerInfoAccount)
}
