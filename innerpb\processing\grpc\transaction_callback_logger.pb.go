// Code generated by protoc-gen-go-mvp. DO NOT EDIT.
// versions:
// - protoc-gen-go-mvp v2.0.1
// created by mvp authors

package grpc

import (
	context "context"
	cntx "git.local/sensitive/mvp/pkg/cntx"
	ctxzap "github.com/grpc-ecosystem/go-grpc-middleware/logging/zap/ctxzap"
	zap "go.uber.org/zap"
	grpc "google.golang.org/grpc"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
)

func file_inner_processing_grpc_transaction_callback_proto_message_EmptyToZap(
	label string,
	in *emptypb.Empty,
) zap.Field {
	if in == nil {
		return zap.Reflect(label, in)
	}

	return zap.Dict(
		label,
	)
}

func file_inner_processing_grpc_transaction_callback_proto_message_TransactionSendCallbackRequestV1ToZap(
	label string,
	in *TransactionSendCallbackRequestV1,
) zap.Field {
	if in == nil {
		return zap.Reflect(label, in)
	}

	return zap.Dict(
		label,
		zap.Any("TransactionId", in.GetTransactionId()),
		zap.Any("AcquireCode", in.GetAcquireCode()),
	)
}

var _ TransactionCallbackServer = (*loggedTransactionCallbackServer)(nil)

func NewLoggedTransactionCallbackServer(srv TransactionCallbackServer) TransactionCallbackServer {
	return &loggedTransactionCallbackServer{srv: srv}
}

type loggedTransactionCallbackServer struct {
	UnimplementedTransactionCallbackServer

	srv TransactionCallbackServer
}

func (s *loggedTransactionCallbackServer) SendCallback(
	ctx context.Context,
	request *TransactionSendCallbackRequestV1,
) (
	response *emptypb.Empty,
	err error,
) {
	label := cntx.Begin(ctx, "TransactionCallbackServer_SendCallback")
	defer cntx.End(ctx, label)

	defer func() {
		if err != nil {
			ctxzap.AddFields(ctx, zap.Any(label+"error", err))
			return
		}

		ctxzap.AddFields(
			ctx,
			file_inner_processing_grpc_transaction_callback_proto_message_EmptyToZap(label+"response", response),
		)
	}()

	ctxzap.AddFields(
		ctx,
		file_inner_processing_grpc_transaction_callback_proto_message_TransactionSendCallbackRequestV1ToZap(label+"request", request),
	)

	response, err = s.srv.SendCallback(ctx, request)

	return
}

var _ TransactionCallbackClient = (*loggedTransactionCallbackClient)(nil)

func NewLoggedTransactionCallbackClient(client TransactionCallbackClient) TransactionCallbackClient {
	return &loggedTransactionCallbackClient{client: client}
}

type loggedTransactionCallbackClient struct {
	client TransactionCallbackClient
}

func (s *loggedTransactionCallbackClient) SendCallback(
	ctx context.Context,
	request *TransactionSendCallbackRequestV1,
	opts ...grpc.CallOption,
) (
	response *emptypb.Empty,
	err error,
) {
	label := cntx.Begin(ctx, "TransactionCallbackClient_SendCallback")
	defer cntx.End(ctx, label)

	defer func() {
		if err != nil {
			ctxzap.AddFields(ctx, zap.Any(label+"error", err))
			return
		}

		ctxzap.AddFields(
			ctx,
			file_inner_processing_grpc_transaction_callback_proto_message_EmptyToZap(label+"response", response),
		)
	}()

	ctxzap.AddFields(
		ctx,
		file_inner_processing_grpc_transaction_callback_proto_message_TransactionSendCallbackRequestV1ToZap(label+"request", request),
	)

	response, err = s.client.SendCallback(ctx, request, opts...)

	return
}
