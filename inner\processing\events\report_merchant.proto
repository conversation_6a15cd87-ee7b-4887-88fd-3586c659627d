syntax = "proto3";

option go_package = "git.local/sensitive/innerpb/processing/events";

import "mvp/proto/events.proto";



message SaveAdditionalDataKeys {
  option (mvp.events.is_event) = true;
  uint64 project_id = 1;
  uint64 merchant_id = 2;
  map<string, string> additional_data = 3;
}



message CreateMerchantCompany {
  option (mvp.events.is_event) = true;
    uint64 merchant_id = 1;
    string merchant_name = 2;
    string merchant_company_name = 3;
    string merchant_email = 4;
    string merchant_siteUrl = 5;
    string merchant_description = 6;
    string merchant_contacts = 7;


}
