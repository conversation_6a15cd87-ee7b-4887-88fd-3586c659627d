edition = "2023";

package processing.view_crafter.view_crafter;

option go_package = "git.local/sensitive/innerpb/processing/grpc";

import "google/protobuf/struct.proto";

service ViewCrafter {
  rpc GetProjectFormInfoV1(GetProjectFormInfoReqV1) returns (GetProjectFormInfoResV1) {}
}

message GetProjectFormInfoReqV1 {
  uint64 merchant_id = 1;
  uint64 project_id = 2;
  string lang = 3;
}

message GetProjectFormInfoResV1 {
  uint64 merchant_id = 1;
  uint64 project_id = 2;
  string store_name = 3;
  string logo_file_path = 4;
  bool has_email = 5;
  bool required_email = 6;
  bool has_phone = 7;
  bool required_phone = 8;
  string default_language = 9;
  uint64 timeout = 10;
  bool has_redirect = 11;
  bool has_default_card = 12;
  string banner_desktop_file_path = 13;
  string banner_mobile_file_path = 14;
  string banner_url = 15;
  bool show_description = 16;
  google.protobuf.Struct description_language = 17;
}