package repository

import (
	"context"
	"errors"
	"fmt"
	"git.local/sensitive/innerpb/processing/goerr"
	"git.local/sensitive/processing/account/model"
	"git.local/sensitive/sdk/dog"
	"gorm.io/gorm"
)

type TransferStatusDB struct {
	db *gorm.DB
}

func NewTransferStatusDB(db *gorm.DB) TransferStatuser {
	return &TransferStatusDB{
		db: db,
	}
}

func (ts *TransferStatusDB) GetByID(ctx context.Context, id uint64) (_ *model.TransferStatus, err error) {
	ctx, span := dog.CreateSpan(ctx, "TransferStatusDB_GetByID")
	defer span.End()

	transferStatus := new(model.TransferStatus)

	if err = ts.db.WithContext(ctx).
		Where("id = ?", id).
		First(transferStatus).
		Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, goerr.ErrTransferStatusNotFound.WithCtx(ctx)
		}

		return nil, goerr.ErrDbUnexpected.WithErr(err).WithCtx(ctx)
	}

	return transferStatus, nil
}

func (ts *TransferStatusDB) GetByCode(
	ctx context.Context,
	code model.TransferStatusCode,
) (_ *model.TransferStatus, err error) {
	ctx, span := dog.CreateSpan(ctx, "TransferStatusDB_GetByCode")
	defer span.End()

	transferStatus := new(model.TransferStatus)

	if err = ts.db.WithContext(ctx).
		Where("code = ?", code).
		First(transferStatus).
		Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, goerr.ErrTransferStatusNotFound.WithCtx(ctx)
		}

		return nil, goerr.ErrDbUnexpected.WithErr(err).WithCtx(ctx)
	}

	return transferStatus, nil
}

func (ts *TransferStatusDB) GetAll(
	ctx context.Context,
) (_ []model.TransferStatus, err error) {
	ctx, span := dog.CreateSpan(ctx, "TransferStatusDB_GetAll")
	defer span.End()

	var statuses []model.TransferStatus

	if err = ts.db.WithContext(ctx).
		Find(&statuses).
		Error; err != nil {
		return nil, fmt.Errorf("unexpected db error")

	}

	return statuses, nil
}

func (ts *TransferStatusDB) AsCodeMap(
	ctx context.Context,
) (_ map[model.TransferStatusCode]uint64, err error) {
	ctx, span := dog.CreateSpan(ctx, "TransferStatusDB_GetTransferStatusMap")
	defer span.End()

	statusesList := make([]model.TransferStatus, 0)

	if err = ts.db.
		WithContext(ctx).
		Find(&statusesList).Error; err != nil {
		return nil, goerr.ErrDbUnexpected.WithErr(err).WithCtx(ctx)
	}

	response := make(map[model.TransferStatusCode]uint64)

	for _, status := range statusesList {
		response[status.Code] = status.ID
	}

	return response, nil
}

func (ts *TransferStatusDB) GetSuccess(
	ctx context.Context,
) (*model.TransferStatus, error) {
	ctx, span := dog.CreateSpan(ctx, "TransferTypeDB_GetSuccess")
	defer span.End()

	transferStatus := new(model.TransferStatus)

	err := ts.db.WithContext(ctx).
		Where("code = ?", model.StatusSuccess).
		First(transferStatus).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, goerr.ErrTransferStatusNotFound.WithCtx(ctx)
		}

		return nil, goerr.ErrDbUnexpected.WithErr(err).WithCtx(ctx)
	}

	return transferStatus, nil
}
