// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.5.1
// - protoc             v5.29.3
// source: inner/processing/grpc/terminal.proto

package grpc

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.64.0 or later.
const _ = grpc.SupportPackageIsVersion9

const (
	Terminal_GetByTerminalID_FullMethodName              = "/processing.acquirer.terminal.Terminal/GetByTerminalID"
	Terminal_FindActiveTerminalsByProject_FullMethodName = "/processing.acquirer.terminal.Terminal/FindActiveTerminalsByProject"
	Terminal_SearchTerminal_FullMethodName               = "/processing.acquirer.terminal.Terminal/SearchTerminal"
	Terminal_ExtendedSearchTerminal_FullMethodName       = "/processing.acquirer.terminal.Terminal/ExtendedSearchTerminal"
	Terminal_GetTerminalsByProjectId_FullMethodName      = "/processing.acquirer.terminal.Terminal/GetTerminalsByProjectId"
	Terminal_GetTerminalWithJusan_FullMethodName         = "/processing.acquirer.terminal.Terminal/GetTerminalWithJusan"
	Terminal_GetRuleByActiveTerminals_FullMethodName     = "/processing.acquirer.terminal.Terminal/GetRuleByActiveTerminals"
	Terminal_GetPayInProjectTerminals_FullMethodName     = "/processing.acquirer.terminal.Terminal/GetPayInProjectTerminals"
)

// TerminalClient is the client API for Terminal service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type TerminalClient interface {
	// GetByTerminalID Запрос на получение данных терминала и его эквайера
	GetByTerminalID(ctx context.Context, in *TerminalRequestV1, opts ...grpc.CallOption) (*TerminalResponseV1, error)
	// GetAllActiveTerminals Запрос на получение всех активных терминалов
	FindActiveTerminalsByProject(ctx context.Context, in *ActiveTerminalsByProjectRequestV1, opts ...grpc.CallOption) (*ActiveTerminalsByProjectResponseV1, error)
	SearchTerminal(ctx context.Context, in *SearchTerminalReqDataV1, opts ...grpc.CallOption) (*SearchTerminalResDataV1, error)
	ExtendedSearchTerminal(ctx context.Context, in *ExtendedSearchTerminalReqDataV1, opts ...grpc.CallOption) (*SearchTerminalResDataV1, error)
	GetTerminalsByProjectId(ctx context.Context, in *GetTerminalsByProjectIdRequestV1, opts ...grpc.CallOption) (*ActiveTerminalsByProjectResponseV1, error)
	GetTerminalWithJusan(ctx context.Context, in *SearchTerminalReqDataV1, opts ...grpc.CallOption) (*GetTerminalWithJusanResponseV1, error)
	GetRuleByActiveTerminals(ctx context.Context, in *RuleByActiveTerminalsReqV1, opts ...grpc.CallOption) (*RuleByActiveTerminalsResponseV1, error)
	GetPayInProjectTerminals(ctx context.Context, in *GetPayInProjectTerminalsReqV1, opts ...grpc.CallOption) (*GetPayInProjectTerminalsResponseV1, error)
}

type terminalClient struct {
	cc grpc.ClientConnInterface
}

func NewTerminalClient(cc grpc.ClientConnInterface) TerminalClient {
	return &terminalClient{cc}
}

func (c *terminalClient) GetByTerminalID(ctx context.Context, in *TerminalRequestV1, opts ...grpc.CallOption) (*TerminalResponseV1, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(TerminalResponseV1)
	err := c.cc.Invoke(ctx, Terminal_GetByTerminalID_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *terminalClient) FindActiveTerminalsByProject(ctx context.Context, in *ActiveTerminalsByProjectRequestV1, opts ...grpc.CallOption) (*ActiveTerminalsByProjectResponseV1, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ActiveTerminalsByProjectResponseV1)
	err := c.cc.Invoke(ctx, Terminal_FindActiveTerminalsByProject_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *terminalClient) SearchTerminal(ctx context.Context, in *SearchTerminalReqDataV1, opts ...grpc.CallOption) (*SearchTerminalResDataV1, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(SearchTerminalResDataV1)
	err := c.cc.Invoke(ctx, Terminal_SearchTerminal_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *terminalClient) ExtendedSearchTerminal(ctx context.Context, in *ExtendedSearchTerminalReqDataV1, opts ...grpc.CallOption) (*SearchTerminalResDataV1, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(SearchTerminalResDataV1)
	err := c.cc.Invoke(ctx, Terminal_ExtendedSearchTerminal_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *terminalClient) GetTerminalsByProjectId(ctx context.Context, in *GetTerminalsByProjectIdRequestV1, opts ...grpc.CallOption) (*ActiveTerminalsByProjectResponseV1, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ActiveTerminalsByProjectResponseV1)
	err := c.cc.Invoke(ctx, Terminal_GetTerminalsByProjectId_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *terminalClient) GetTerminalWithJusan(ctx context.Context, in *SearchTerminalReqDataV1, opts ...grpc.CallOption) (*GetTerminalWithJusanResponseV1, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetTerminalWithJusanResponseV1)
	err := c.cc.Invoke(ctx, Terminal_GetTerminalWithJusan_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *terminalClient) GetRuleByActiveTerminals(ctx context.Context, in *RuleByActiveTerminalsReqV1, opts ...grpc.CallOption) (*RuleByActiveTerminalsResponseV1, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(RuleByActiveTerminalsResponseV1)
	err := c.cc.Invoke(ctx, Terminal_GetRuleByActiveTerminals_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *terminalClient) GetPayInProjectTerminals(ctx context.Context, in *GetPayInProjectTerminalsReqV1, opts ...grpc.CallOption) (*GetPayInProjectTerminalsResponseV1, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetPayInProjectTerminalsResponseV1)
	err := c.cc.Invoke(ctx, Terminal_GetPayInProjectTerminals_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// TerminalServer is the server API for Terminal service.
// All implementations must embed UnimplementedTerminalServer
// for forward compatibility.
type TerminalServer interface {
	// GetByTerminalID Запрос на получение данных терминала и его эквайера
	GetByTerminalID(context.Context, *TerminalRequestV1) (*TerminalResponseV1, error)
	// GetAllActiveTerminals Запрос на получение всех активных терминалов
	FindActiveTerminalsByProject(context.Context, *ActiveTerminalsByProjectRequestV1) (*ActiveTerminalsByProjectResponseV1, error)
	SearchTerminal(context.Context, *SearchTerminalReqDataV1) (*SearchTerminalResDataV1, error)
	ExtendedSearchTerminal(context.Context, *ExtendedSearchTerminalReqDataV1) (*SearchTerminalResDataV1, error)
	GetTerminalsByProjectId(context.Context, *GetTerminalsByProjectIdRequestV1) (*ActiveTerminalsByProjectResponseV1, error)
	GetTerminalWithJusan(context.Context, *SearchTerminalReqDataV1) (*GetTerminalWithJusanResponseV1, error)
	GetRuleByActiveTerminals(context.Context, *RuleByActiveTerminalsReqV1) (*RuleByActiveTerminalsResponseV1, error)
	GetPayInProjectTerminals(context.Context, *GetPayInProjectTerminalsReqV1) (*GetPayInProjectTerminalsResponseV1, error)
	mustEmbedUnimplementedTerminalServer()
}

// UnimplementedTerminalServer must be embedded to have
// forward compatible implementations.
//
// NOTE: this should be embedded by value instead of pointer to avoid a nil
// pointer dereference when methods are called.
type UnimplementedTerminalServer struct{}

func (UnimplementedTerminalServer) GetByTerminalID(context.Context, *TerminalRequestV1) (*TerminalResponseV1, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetByTerminalID not implemented")
}
func (UnimplementedTerminalServer) FindActiveTerminalsByProject(context.Context, *ActiveTerminalsByProjectRequestV1) (*ActiveTerminalsByProjectResponseV1, error) {
	return nil, status.Errorf(codes.Unimplemented, "method FindActiveTerminalsByProject not implemented")
}
func (UnimplementedTerminalServer) SearchTerminal(context.Context, *SearchTerminalReqDataV1) (*SearchTerminalResDataV1, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SearchTerminal not implemented")
}
func (UnimplementedTerminalServer) ExtendedSearchTerminal(context.Context, *ExtendedSearchTerminalReqDataV1) (*SearchTerminalResDataV1, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ExtendedSearchTerminal not implemented")
}
func (UnimplementedTerminalServer) GetTerminalsByProjectId(context.Context, *GetTerminalsByProjectIdRequestV1) (*ActiveTerminalsByProjectResponseV1, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetTerminalsByProjectId not implemented")
}
func (UnimplementedTerminalServer) GetTerminalWithJusan(context.Context, *SearchTerminalReqDataV1) (*GetTerminalWithJusanResponseV1, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetTerminalWithJusan not implemented")
}
func (UnimplementedTerminalServer) GetRuleByActiveTerminals(context.Context, *RuleByActiveTerminalsReqV1) (*RuleByActiveTerminalsResponseV1, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetRuleByActiveTerminals not implemented")
}
func (UnimplementedTerminalServer) GetPayInProjectTerminals(context.Context, *GetPayInProjectTerminalsReqV1) (*GetPayInProjectTerminalsResponseV1, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetPayInProjectTerminals not implemented")
}
func (UnimplementedTerminalServer) mustEmbedUnimplementedTerminalServer() {}
func (UnimplementedTerminalServer) testEmbeddedByValue()                  {}

// UnsafeTerminalServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to TerminalServer will
// result in compilation errors.
type UnsafeTerminalServer interface {
	mustEmbedUnimplementedTerminalServer()
}

func RegisterTerminalServer(s grpc.ServiceRegistrar, srv TerminalServer) {
	// If the following call pancis, it indicates UnimplementedTerminalServer was
	// embedded by pointer and is nil.  This will cause panics if an
	// unimplemented method is ever invoked, so we test this at initialization
	// time to prevent it from happening at runtime later due to I/O.
	if t, ok := srv.(interface{ testEmbeddedByValue() }); ok {
		t.testEmbeddedByValue()
	}
	s.RegisterService(&Terminal_ServiceDesc, srv)
}

func _Terminal_GetByTerminalID_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(TerminalRequestV1)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TerminalServer).GetByTerminalID(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Terminal_GetByTerminalID_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TerminalServer).GetByTerminalID(ctx, req.(*TerminalRequestV1))
	}
	return interceptor(ctx, in, info, handler)
}

func _Terminal_FindActiveTerminalsByProject_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ActiveTerminalsByProjectRequestV1)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TerminalServer).FindActiveTerminalsByProject(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Terminal_FindActiveTerminalsByProject_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TerminalServer).FindActiveTerminalsByProject(ctx, req.(*ActiveTerminalsByProjectRequestV1))
	}
	return interceptor(ctx, in, info, handler)
}

func _Terminal_SearchTerminal_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SearchTerminalReqDataV1)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TerminalServer).SearchTerminal(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Terminal_SearchTerminal_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TerminalServer).SearchTerminal(ctx, req.(*SearchTerminalReqDataV1))
	}
	return interceptor(ctx, in, info, handler)
}

func _Terminal_ExtendedSearchTerminal_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ExtendedSearchTerminalReqDataV1)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TerminalServer).ExtendedSearchTerminal(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Terminal_ExtendedSearchTerminal_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TerminalServer).ExtendedSearchTerminal(ctx, req.(*ExtendedSearchTerminalReqDataV1))
	}
	return interceptor(ctx, in, info, handler)
}

func _Terminal_GetTerminalsByProjectId_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetTerminalsByProjectIdRequestV1)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TerminalServer).GetTerminalsByProjectId(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Terminal_GetTerminalsByProjectId_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TerminalServer).GetTerminalsByProjectId(ctx, req.(*GetTerminalsByProjectIdRequestV1))
	}
	return interceptor(ctx, in, info, handler)
}

func _Terminal_GetTerminalWithJusan_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SearchTerminalReqDataV1)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TerminalServer).GetTerminalWithJusan(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Terminal_GetTerminalWithJusan_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TerminalServer).GetTerminalWithJusan(ctx, req.(*SearchTerminalReqDataV1))
	}
	return interceptor(ctx, in, info, handler)
}

func _Terminal_GetRuleByActiveTerminals_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RuleByActiveTerminalsReqV1)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TerminalServer).GetRuleByActiveTerminals(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Terminal_GetRuleByActiveTerminals_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TerminalServer).GetRuleByActiveTerminals(ctx, req.(*RuleByActiveTerminalsReqV1))
	}
	return interceptor(ctx, in, info, handler)
}

func _Terminal_GetPayInProjectTerminals_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetPayInProjectTerminalsReqV1)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TerminalServer).GetPayInProjectTerminals(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Terminal_GetPayInProjectTerminals_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TerminalServer).GetPayInProjectTerminals(ctx, req.(*GetPayInProjectTerminalsReqV1))
	}
	return interceptor(ctx, in, info, handler)
}

// Terminal_ServiceDesc is the grpc.ServiceDesc for Terminal service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var Terminal_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "processing.acquirer.terminal.Terminal",
	HandlerType: (*TerminalServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetByTerminalID",
			Handler:    _Terminal_GetByTerminalID_Handler,
		},
		{
			MethodName: "FindActiveTerminalsByProject",
			Handler:    _Terminal_FindActiveTerminalsByProject_Handler,
		},
		{
			MethodName: "SearchTerminal",
			Handler:    _Terminal_SearchTerminal_Handler,
		},
		{
			MethodName: "ExtendedSearchTerminal",
			Handler:    _Terminal_ExtendedSearchTerminal_Handler,
		},
		{
			MethodName: "GetTerminalsByProjectId",
			Handler:    _Terminal_GetTerminalsByProjectId_Handler,
		},
		{
			MethodName: "GetTerminalWithJusan",
			Handler:    _Terminal_GetTerminalWithJusan_Handler,
		},
		{
			MethodName: "GetRuleByActiveTerminals",
			Handler:    _Terminal_GetRuleByActiveTerminals_Handler,
		},
		{
			MethodName: "GetPayInProjectTerminals",
			Handler:    _Terminal_GetPayInProjectTerminals_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "inner/processing/grpc/terminal.proto",
}
