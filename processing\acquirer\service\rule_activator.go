package service

import (
	"context"
	"git.local/sensitive/innerpb/processing/goerr"
	"git.local/sensitive/processing/acquirer/repository/database"
	"git.local/sensitive/sdk/dog"
)

type RuleActivatorService struct {
	ruleActivatorRepo database.RuleActivator
	ruleRepo          database.Ruler
}

func NewRuleActivatorService(
	ruleActivatorRepo database.RuleActivator,
	ruleRepo database.Ruler,
) *RuleActivatorService {
	return &RuleActivatorService{
		ruleActivatorRepo: ruleActivatorRepo,
		ruleRepo:          ruleRepo,
	}
}

func (r RuleActivatorService) Activate(ctx context.Context, ruleID uint64) (err error) {
	ctx, span := dog.CreateSpan(ctx, "RuleActivatorService_Activate")
	defer span.End()

	rule, err := r.ruleRepo.GetByID(ctx, ruleID)
	if err != nil {
		return err
	}

	if rule.IsBase {
		return goerr.ErrBaseRuleActivation
	}

	return r.ruleActivatorRepo.Activate(ctx, ruleID)
}

func (r RuleActivatorService) Deactivate(ctx context.Context, ruleID uint64) (err error) {
	ctx, span := dog.CreateSpan(ctx, "RuleActivatorService_Deactivate")
	defer span.End()

	rule, err := r.ruleRepo.GetByID(ctx, ruleID)
	if err != nil {
		return err
	}

	if rule.IsBase {
		return goerr.ErrBaseRuleDeactivation
	}

	return r.ruleActivatorRepo.Deactivate(ctx, ruleID)
}
