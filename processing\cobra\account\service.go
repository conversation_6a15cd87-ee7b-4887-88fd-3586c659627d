package account

import (
	"git.local/sensitive/sdk/dog"
	"net"
	"time"

	"github.com/getsentry/sentry-go"
	"github.com/gin-gonic/gin"
	"github.com/spf13/cobra"
	"go.uber.org/zap"
	"google.golang.org/grpc/reflection"

	"git.local/sensitive/innerpb/processing/grpc"
	grpcDeliveryV1 "git.local/sensitive/processing/account/delivery/grpc/v1"
	httpDelivery "git.local/sensitive/processing/account/delivery/http"
	"git.local/sensitive/processing/account/docs"
	_ "git.local/sensitive/processing/account/docs"
	"git.local/sensitive/processing/account/repository"
	"git.local/sensitive/processing/account/service"
)

var CMD = &cobra.Command{
	Use: "account",
	Run: run,
}

const (
	sentryFlushTimeout = 5 * time.Second
)

//go:generate swag init -g ./../cobra/account/service.go -d ./../../account --parseDependencyLevel 1 --instanceName Account --output ./../../account/docs

// @title account
// @version 1.0.0
// @description account processing
//
// @host api-dev.processing.kz
// @BasePath /account
// @schemes https http
// @securityDefinitions.apikey bearerAuth
// @in header
// @name Authorization
func run(cmd *cobra.Command, args []string) {
	defer func() {
		if err, hub := recover(), sentry.CurrentHub(); err != nil && hub != nil {
			hub.Recover(err)
			sentry.Flush(sentryFlushTimeout)
			panic(err)
		}
	}()

	gormDB := dog.InitApp("processing.account.account.Account")

	logger := dog.L()

	logger.Info("begin register db healthCheck")
	dog.RegisterDBHealthCheck()
	logger.Info("end register db healthCheck")

	logger.Info("begin send swagger doc")
	err := dog.InitOpenAPI(docs.SwaggerInfoAccount)
	if err != nil {
		logger.Warn("error send swagger doc", zap.Error(err))
	}
	logger.Info("end send swagger doc")

	logger.Info("begin connect to billing service")
	billingClient, err := grpc.NewPreparedBillingClient()
	if err != nil {
		logger.Panic("error connect to billing service", zap.Error(err))
	}
	logger.Info("end connect to billing service")
	grpc.NewLoggedBillingClient(billingClient)

	logger.Info("begin connect to acquirer service")
	acquirerClient, err := grpc.NewPreparedAcquirerClient()
	if err != nil {
		logger.Panic("error connect to acquirer service", zap.Error(err))
	}
	logger.Info("end connect to acquirer service")
	grpc.NewLoggedAcquirerClient(acquirerClient)

	logger.Info("begin connect to merchant service")
	merchantClient, err := grpc.NewPreparedMerchantClient()
	if err != nil {
		logger.Panic("error connect to merchant service", zap.Error(err))
	}
	logger.Info("end connect to acquirer merchant")
	grpc.NewLoggedMerchantClient(merchantClient)

	logger.Info("begin connect to multiaccounting service")
	multiaccountingClient, err := grpc.NewPreparedMultiaccountingClient()
	if err != nil {
		logger.Panic("error connect to multiaccounting service", zap.Error(err))
	}
	logger.Info("end connect to multiaccounting service")
	grpc.NewLoggedMultiaccountingClient(multiaccountingClient)

	repo := repository.NewRepositories(
		gormDB,
	)

	services := service.NewServices(
		repo,
		billingClient,
		acquirerClient,
		merchantClient,
		dog.ConfigString("WORKER_CREATED_AT_FROM_HOURS"),
		dog.ConfigString("WORKER_CREATED_AT_TO_HOURS"),
		multiaccountingClient,
	)

	go func() {
		defer dog.Cancel()()

		serverAddr := dog.ConfigString("HTTP_PORT")
		dog.L().Info("serverAdr", zap.String("HTTP_PORT", serverAddr))

		handlerDelivery := httpDelivery.NewHandlerDelivery(services, dog.MicroService())

		engine := dog.GinEngine()

		handlerDelivery.Init(engine)
		go func() {
			<-time.NewTicker(time.Second).C
			gin.SetMode(gin.ReleaseMode)
		}()

		if err = engine.Run(":" + serverAddr); err != nil {
			logger.Panic("can not run gin engine", zap.Error(err))
		}
	}()

	go func() {
		defer dog.Cancel()()

		grpcServer := dog.GrpcServer()

		grpc.RegisterAccountServer(grpcServer, grpc.NewLoggedAccountServer(grpcDeliveryV1.NewAccountServer(services)))

		serverAddr := dog.ConfigString("GRPC_PORT")
		dog.L().Info("serverAdr", zap.String("GRPC_PORT", serverAddr))

		listenerGrpc, err := net.Listen("tcp", ":"+serverAddr)
		if err != nil {
			dog.L().Fatal("can not prepare net.Listener for grpc service", zap.Error(err))
		}

		reflection.Register(grpcServer)

		if err := grpcServer.Serve(listenerGrpc); err != nil {
			dog.L().Fatal("can not run grpc server", zap.Error(err))
		}
	}()

	<-dog.Ctx().Done()
}
