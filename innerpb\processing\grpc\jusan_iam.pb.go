// Code generated by protoc-gen-go-mvp. DO NOT EDIT.
// versions:
// - protoc-gen-go-mvp v2.0.1
// created by mvp authors

package grpc

import (
	context "context"
	grpc "google.golang.org/grpc"
	metadata "google.golang.org/grpc/metadata"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
)

func NewIamJusanServer(
	srv JusanServer,
) JusanServer {
	return &iamJusanServer{
		srv: srv,
	}
}

var _ JusanServer = (*iamJusanServer)(nil)

type iamJusanServer struct {
	UnimplementedJusanServer

	srv JusanServer
}

func (s *iamJusanServer) PayIn(
	ctx context.Context,
	req *PayInRequestData,
) (
	*PayInResponseData,
	error,
) {
	return s.srv.PayIn(ctx, req)
}

func (s *iamJusanServer) OneClickPayIn(
	ctx context.Context,
	req *OneClickPayInRequestData,
) (
	*PayInResponseData,
	error,
) {
	return s.srv.OneClickPayIn(ctx, req)
}

func (s *iamJusanServer) ThreeDSConfirm(
	ctx context.Context,
	req *ThreeDSRequestData,
) (
	*ThreeDSResponseData,
	error,
) {
	return s.srv.ThreeDSConfirm(ctx, req)
}

func (s *iamJusanServer) ThreeDSResume(
	ctx context.Context,
	req *ThreeDSResumeRequest,
) (
	*ThreeDSResumeResponse,
	error,
) {
	return s.srv.ThreeDSResume(ctx, req)
}

func (s *iamJusanServer) PayOut(
	ctx context.Context,
	req *PayOutRequestData,
) (
	*PayOutResponseData,
	error,
) {
	return s.srv.PayOut(ctx, req)
}

func (s *iamJusanServer) GetBankTransactionStatus(
	ctx context.Context,
	req *BankTransactionStatusRequest,
) (
	*BankTransactionStatusResponse,
	error,
) {
	return s.srv.GetBankTransactionStatus(ctx, req)
}

func (s *iamJusanServer) GetBankTransactionStatusUnformated(
	ctx context.Context,
	req *BankTransactionStatusUnformatedRequest,
) (
	*BankTransactionStatusUnformatedResponse,
	error,
) {
	return s.srv.GetBankTransactionStatusUnformated(ctx, req)
}

func (s *iamJusanServer) Refund(
	ctx context.Context,
	req *RefundRequest,
) (
	*RefundResponse,
	error,
) {
	return s.srv.Refund(ctx, req)
}

func (s *iamJusanServer) GooglePay(
	ctx context.Context,
	req *GooglePayRequestData,
) (
	*GooglePayResponseData,
	error,
) {
	return s.srv.GooglePay(ctx, req)
}

func (s *iamJusanServer) ApplePay(
	ctx context.Context,
	req *ApplePayRequestData,
) (
	*ApplePayResponseData,
	error,
) {
	return s.srv.ApplePay(ctx, req)
}

func (s *iamJusanServer) TwoStagePayIn(
	ctx context.Context,
	req *TwoStagePayInRequest,
) (
	*TwoStagePayInResponse,
	error,
) {
	return s.srv.TwoStagePayIn(ctx, req)
}

func (s *iamJusanServer) Charge(
	ctx context.Context,
	req *ChargeRequest,
) (
	*ChargeResponse,
	error,
) {
	return s.srv.Charge(ctx, req)
}

func (s *iamJusanServer) Cancel(
	ctx context.Context,
	req *CancelRequest,
) (
	*CancelResponse,
	error,
) {
	return s.srv.Cancel(ctx, req)
}

func (s *iamJusanServer) MakeToken(
	ctx context.Context,
	req *PayInRequestData,
) (
	*PayInResponseData,
	error,
) {
	return s.srv.MakeToken(ctx, req)
}

func (s *iamJusanServer) GetEmission(
	ctx context.Context,
	req *emptypb.Empty,
) (
	*EmissionResponse,
	error,
) {
	return s.srv.GetEmission(ctx, req)
}

func (s *iamJusanServer) ConfirmEmission(
	ctx context.Context,
	req *EmoneyRequest,
) (
	*EmoneyResponse,
	error,
) {
	return s.srv.ConfirmEmission(ctx, req)
}

func (s *iamJusanServer) GetAcquirerIdentifier(
	ctx context.Context,
	req *emptypb.Empty,
) (
	*GetAcquirerIdentifierResponse,
	error,
) {
	return s.srv.GetAcquirerIdentifier(ctx, req)
}

func (s *iamJusanServer) CheckBalance(
	ctx context.Context,
	req *CheckBalanceRequest,
) (
	*CheckBalanceResponse,
	error,
) {
	return s.srv.CheckBalance(ctx, req)
}

func (s *iamJusanServer) ResolveVisaAlias(
	ctx context.Context,
	req *ResolveVisaAliasRequest,
) (
	*ResolveVisaAliasResponse,
	error,
) {
	return s.srv.ResolveVisaAlias(ctx, req)
}

func (s *iamJusanServer) PayOutByPhone(
	ctx context.Context,
	req *PayOutByPhoneRequestData,
) (
	*PayOutResponseByPhoneData,
	error,
) {
	return s.srv.PayOutByPhone(ctx, req)
}

func NewIamJusanClient(
	client JusanClient,
) JusanClient {
	return &iamJusanClient{
		client: client,
	}
}

type iamJusanClient struct {
	client JusanClient
}

func (s *iamJusanClient) PayIn(
	ctx context.Context,
	req *PayInRequestData,
	opts ...grpc.CallOption,
) (
	*PayInResponseData,
	error,
) {
	md, _ := metadata.FromIncomingContext(ctx)
	return s.client.PayIn(metadata.NewOutgoingContext(ctx, md), req)
}

func (s *iamJusanClient) OneClickPayIn(
	ctx context.Context,
	req *OneClickPayInRequestData,
	opts ...grpc.CallOption,
) (
	*PayInResponseData,
	error,
) {
	md, _ := metadata.FromIncomingContext(ctx)
	return s.client.OneClickPayIn(metadata.NewOutgoingContext(ctx, md), req)
}

func (s *iamJusanClient) ThreeDSConfirm(
	ctx context.Context,
	req *ThreeDSRequestData,
	opts ...grpc.CallOption,
) (
	*ThreeDSResponseData,
	error,
) {
	md, _ := metadata.FromIncomingContext(ctx)
	return s.client.ThreeDSConfirm(metadata.NewOutgoingContext(ctx, md), req)
}

func (s *iamJusanClient) ThreeDSResume(
	ctx context.Context,
	req *ThreeDSResumeRequest,
	opts ...grpc.CallOption,
) (
	*ThreeDSResumeResponse,
	error,
) {
	md, _ := metadata.FromIncomingContext(ctx)
	return s.client.ThreeDSResume(metadata.NewOutgoingContext(ctx, md), req)
}

func (s *iamJusanClient) PayOut(
	ctx context.Context,
	req *PayOutRequestData,
	opts ...grpc.CallOption,
) (
	*PayOutResponseData,
	error,
) {
	md, _ := metadata.FromIncomingContext(ctx)
	return s.client.PayOut(metadata.NewOutgoingContext(ctx, md), req)
}

func (s *iamJusanClient) GetBankTransactionStatus(
	ctx context.Context,
	req *BankTransactionStatusRequest,
	opts ...grpc.CallOption,
) (
	*BankTransactionStatusResponse,
	error,
) {
	md, _ := metadata.FromIncomingContext(ctx)
	return s.client.GetBankTransactionStatus(metadata.NewOutgoingContext(ctx, md), req)
}

func (s *iamJusanClient) GetBankTransactionStatusUnformated(
	ctx context.Context,
	req *BankTransactionStatusUnformatedRequest,
	opts ...grpc.CallOption,
) (
	*BankTransactionStatusUnformatedResponse,
	error,
) {
	md, _ := metadata.FromIncomingContext(ctx)
	return s.client.GetBankTransactionStatusUnformated(metadata.NewOutgoingContext(ctx, md), req)
}

func (s *iamJusanClient) Refund(
	ctx context.Context,
	req *RefundRequest,
	opts ...grpc.CallOption,
) (
	*RefundResponse,
	error,
) {
	md, _ := metadata.FromIncomingContext(ctx)
	return s.client.Refund(metadata.NewOutgoingContext(ctx, md), req)
}

func (s *iamJusanClient) GooglePay(
	ctx context.Context,
	req *GooglePayRequestData,
	opts ...grpc.CallOption,
) (
	*GooglePayResponseData,
	error,
) {
	md, _ := metadata.FromIncomingContext(ctx)
	return s.client.GooglePay(metadata.NewOutgoingContext(ctx, md), req)
}

func (s *iamJusanClient) ApplePay(
	ctx context.Context,
	req *ApplePayRequestData,
	opts ...grpc.CallOption,
) (
	*ApplePayResponseData,
	error,
) {
	md, _ := metadata.FromIncomingContext(ctx)
	return s.client.ApplePay(metadata.NewOutgoingContext(ctx, md), req)
}

func (s *iamJusanClient) TwoStagePayIn(
	ctx context.Context,
	req *TwoStagePayInRequest,
	opts ...grpc.CallOption,
) (
	*TwoStagePayInResponse,
	error,
) {
	md, _ := metadata.FromIncomingContext(ctx)
	return s.client.TwoStagePayIn(metadata.NewOutgoingContext(ctx, md), req)
}

func (s *iamJusanClient) Charge(
	ctx context.Context,
	req *ChargeRequest,
	opts ...grpc.CallOption,
) (
	*ChargeResponse,
	error,
) {
	md, _ := metadata.FromIncomingContext(ctx)
	return s.client.Charge(metadata.NewOutgoingContext(ctx, md), req)
}

func (s *iamJusanClient) Cancel(
	ctx context.Context,
	req *CancelRequest,
	opts ...grpc.CallOption,
) (
	*CancelResponse,
	error,
) {
	md, _ := metadata.FromIncomingContext(ctx)
	return s.client.Cancel(metadata.NewOutgoingContext(ctx, md), req)
}

func (s *iamJusanClient) MakeToken(
	ctx context.Context,
	req *PayInRequestData,
	opts ...grpc.CallOption,
) (
	*PayInResponseData,
	error,
) {
	md, _ := metadata.FromIncomingContext(ctx)
	return s.client.MakeToken(metadata.NewOutgoingContext(ctx, md), req)
}

func (s *iamJusanClient) GetEmission(
	ctx context.Context,
	req *emptypb.Empty,
	opts ...grpc.CallOption,
) (
	*EmissionResponse,
	error,
) {
	md, _ := metadata.FromIncomingContext(ctx)
	return s.client.GetEmission(metadata.NewOutgoingContext(ctx, md), req)
}

func (s *iamJusanClient) ConfirmEmission(
	ctx context.Context,
	req *EmoneyRequest,
	opts ...grpc.CallOption,
) (
	*EmoneyResponse,
	error,
) {
	md, _ := metadata.FromIncomingContext(ctx)
	return s.client.ConfirmEmission(metadata.NewOutgoingContext(ctx, md), req)
}

func (s *iamJusanClient) GetAcquirerIdentifier(
	ctx context.Context,
	req *emptypb.Empty,
	opts ...grpc.CallOption,
) (
	*GetAcquirerIdentifierResponse,
	error,
) {
	md, _ := metadata.FromIncomingContext(ctx)
	return s.client.GetAcquirerIdentifier(metadata.NewOutgoingContext(ctx, md), req)
}

func (s *iamJusanClient) CheckBalance(
	ctx context.Context,
	req *CheckBalanceRequest,
	opts ...grpc.CallOption,
) (
	*CheckBalanceResponse,
	error,
) {
	md, _ := metadata.FromIncomingContext(ctx)
	return s.client.CheckBalance(metadata.NewOutgoingContext(ctx, md), req)
}

func (s *iamJusanClient) ResolveVisaAlias(
	ctx context.Context,
	req *ResolveVisaAliasRequest,
	opts ...grpc.CallOption,
) (
	*ResolveVisaAliasResponse,
	error,
) {
	md, _ := metadata.FromIncomingContext(ctx)
	return s.client.ResolveVisaAlias(metadata.NewOutgoingContext(ctx, md), req)
}

func (s *iamJusanClient) PayOutByPhone(
	ctx context.Context,
	req *PayOutByPhoneRequestData,
	opts ...grpc.CallOption,
) (
	*PayOutResponseByPhoneData,
	error,
) {
	md, _ := metadata.FromIncomingContext(ctx)
	return s.client.PayOutByPhone(metadata.NewOutgoingContext(ctx, md), req)
}
