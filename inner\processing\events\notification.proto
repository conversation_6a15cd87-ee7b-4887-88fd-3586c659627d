syntax = "proto3";

option go_package = "git.local/sensitive/innerpb/processing/events";

import "mvp/proto/events.proto";

message SendEmail {
  option (mvp.events.is_event) = true;
  repeated string receivers = 1;
  string text = 2;
  string sender = 3;
  string title = 4;
  map<string,string> payload = 5;
}

message SendEmailAttached {
  option (mvp.events.is_event) = true;
  repeated string receivers = 1;
  string text = 2;
  string sender = 3;
  string title = 4;
  map<string,string> payload = 5;
  bytes attachment = 6;
  string  attachment_name = 7;
}


message SendOtp {
  option (mvp.events.is_event) = true;
  string phone = 1;
}

message SendSms {
  option (mvp.events.is_event) = true;
  string phone = 1;
  string text = 2;
  string sender = 3;
  map<string,string> payload = 4;
}