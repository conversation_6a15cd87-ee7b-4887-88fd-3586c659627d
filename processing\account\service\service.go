package service

import (
	"context"
	"github.com/google/uuid"

	gorpc "git.local/sensitive/innerpb/processing/grpc"
	"git.local/sensitive/processing/account/model"
	"git.local/sensitive/processing/account/repository"
	"git.local/sensitive/processing/account/schema"
)

type Transferer interface {
	CreateTransfer(
		ctx context.Context,
		request schema.CreateTransferRequest,
	) (*schema.CreateTransferResponse, error)
	AcceptTransfer(ctx context.Context,
		request schema.AcceptTransferRequest,
	) (*schema.AcceptTransferResponse, error)
	CancelTransfer(ctx context.Context,
		request schema.CancelTransferRequest,
	) (*schema.CancelTransferResponse, error)
	RedoTransfer(ctx context.Context,
		request schema.RedoTransferRequest,
	) (*schema.TransferResponse, error)
	FinalizeTransfer(ctx context.Context) error
	ProcessCancelTransfer(
		ctx context.Context,
		transferID uint64,
	) (*schema.TransferResponse, error)
	ProcessAcceptTransfer(
		ctx context.Context,
		transferID uint64,
	) (*schema.TransferResponse, error)
	ExtractSuccessTransfersByForeignIDs(
		ctx context.Context,
		foreignIDs []uuid.UUID,
	) ([]model.Transfer, error)
	ExtractFailOrCanceledTransfersByForeignIDs(
		ctx context.Context,
		foreignIDs []uuid.UUID,
	) ([]model.Transfer, error)
	GetByForeignID(
		ctx context.Context,
		foreignID uuid.UUID,
	) (*model.Transfer, error)
}

type TransferInformationer interface {
	GetTransferById(ctx context.Context, transferID uint64) (*model.Transfer, error)
	GetTransfersByFilters(ctx context.Context, filters schema.TransferFilters) ([]model.Transfer, error)
	GetTransfersByMerchantId(ctx context.Context, merchantID uint64) ([]model.Transfer, error)
	GetTransfersByProjectId(ctx context.Context, projectID uint64) ([]model.Transfer, error)
}

type AccountBasic interface {
	GetAccounts(ctx context.Context) ([]model.Account, error)
	GetAccountsByBankID(ctx context.Context, bankID uint64) ([]model.Account, error)
}

type AccountInformationer interface {
	GetBalance(ctx context.Context, accountNumber string) (*schema.AccountBalance, error)
	GetAccountsWithBalance(ctx context.Context) ([]schema.Account, error)
	GetAccountByID(ctx context.Context, id uint64) (_ *model.Account, err error)
	GetAccountAcquirers(ctx context.Context) (_ schema.AccountAcquirer, err error)
	GetByNumber(ctx context.Context, accountNumber string) (*model.Account, error)
}

type Validater interface {
	CheckTransferMerchant(ctx context.Context, transferID, merchantID uint64) error
	CheckProjectMerchant(ctx context.Context, projectID, merchantID uint64) error
}

type AccountScheduler interface {
	GetAll(ctx context.Context) (_ []model.AccountSchedule, err error)
	GetByCode(ctx context.Context) (_ model.AccountSchedule, err error)
}

type AccountTyper interface {
	GetAccountTypeByID(
		ctx context.Context,
		id uint64,
	) (_ *model.AccountType, err error)
}

type AccountConfiger interface {
	GetAccountConfig(ctx context.Context, id uint64) (schema.DecryptedAccountConfig, error)
	UpdateAccountConfig(ctx context.Context, id uint64, config map[string]any) error
}

type TransferTyper interface {
	GetAll(ctx context.Context) (_ []model.TransferType, err error)
}

type TransferStatuser interface {
	GetAll(ctx context.Context) (_ []model.TransferStatus, err error)
	GetSuccess(ctx context.Context) (*model.TransferStatus, error)
}

type AccountStatementer interface {
	ProcessAccountStatement(ctx context.Context) (err error)
}

type InTransferParser interface {
	ParseIncomingTransfers(ctx context.Context) error
}

type Merchanter interface {
	CheckMerchantByOwnerID(ctx context.Context, request schema.CheckMerchantRequest) error
}

type OldTransfersFinalizer interface {
	FinalizeOldTransfers(ctx context.Context) error
}

type Services struct {
	Transfer              Transferer
	TransferInfo          TransferInformationer
	AccountInfo           AccountInformationer
	Validation            Validater
	AccountType           AccountTyper
	AccountConfig         AccountConfiger
	TransferType          TransferTyper
	TransferStatus        TransferStatuser
	InTransferParser      InTransferParser
	AccountStatement      AccountStatementer
	Merchant              Merchanter
	AccountBasic          AccountBasic
	OldTransfersFinalizer OldTransfersFinalizer
}

func NewServices(
	repos *repository.Repositories,
	billingClient gorpc.BillingClient,
	acquirerClient gorpc.AcquirerClient,
	merchantClient gorpc.MerchantClient,
	workerCreatedAtFromHours string,
	workerCreatedAtToHours string,
	multiaccountingClient gorpc.MultiaccountingClient,
) *Services {
	transferService := NewTransferService(
		repos.TransferStatusDB,
		repos.TransferManageDB,
		repos.TransferTypeDB,
		repos.TransferDB,
		repos.AccountInfoDB,
		billingClient,
		multiaccountingClient,
	)
	transferInfoService := NewTransferInfoService(repos.TransferDB)
	accountInfoService := NewAccountInfoService(repos.AccountInfoDB, acquirerClient, multiaccountingClient)
	validationService := NewValidationService(repos.TransferDB, merchantClient)
	inTransferParserService := NewInTransferParserService(
		repos.InTransferParsingScheduleDB,
		repos.ProcessedOrderStatusDB,
		repos.ProcessedOrderDB,
		repos.AccountInfoDB,
		repos.TransferStatusDB,
		repos.TransferManageDB,
		billingClient,
		merchantClient,
		multiaccountingClient,
	)
	accountTypeService := NewAccountTypeService(repos.AccountTypeDB)
	accountConfigService := NewAccountConfigService(repos.AccountInfoDB, repos.AccountUpdaterDB)
	transferTypeService := NewTransferTypeService(repos.TransferTypeDB)
	transferStatusService := NewTransferStatusService(repos.TransferStatusDB)
	accountStatementService := NewAccountStatementService(
		repos.AccountScheduleDB,
		repos.AccountInfoDB,
		repos.AccountBalanceHistoryDB,
		repos.AccountStatementDB,
		multiaccountingClient,
	)
	merchantService := NewMerchantService(billingClient)
	accountBasicService := NewAccountBasicService(repos.AccountInfoDB)
	oldTransfersFinalizerService := NewOldTransfersFinalizerService(
		repos.TransferStatusDB,
		repos.TransferDB,
		repos.AccountInfoDB,
		repos.TransferManageDB,
		multiaccountingClient,
		billingClient,
		workerCreatedAtFromHours,
		workerCreatedAtToHours,
	)

	return &Services{
		Transfer:              transferService,
		TransferInfo:          transferInfoService,
		AccountInfo:           accountInfoService,
		Validation:            validationService,
		AccountType:           accountTypeService,
		AccountConfig:         accountConfigService,
		TransferType:          transferTypeService,
		TransferStatus:        transferStatusService,
		InTransferParser:      inTransferParserService,
		AccountStatement:      accountStatementService,
		Merchant:              merchantService,
		AccountBasic:          accountBasicService,
		OldTransfersFinalizer: oldTransfersFinalizerService,
	}
}
