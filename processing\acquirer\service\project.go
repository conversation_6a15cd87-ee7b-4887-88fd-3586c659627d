package service

import (
	"context"
	"git.local/sensitive/processing/acquirer/repository/database"

	"go.uber.org/zap"

	gorpc "git.local/sensitive/innerpb/processing/grpc"
	"git.local/sensitive/processing/acquirer/model"
	"git.local/sensitive/processing/acquirer/schema"
	"git.local/sensitive/sdk/dog"
)

type ProjectService struct {
	terminalBasicRepo     database.TerminalBasicer
	acquirerRepo          database.Acquirer
	terminalRepo          database.Terminaler
	transactionTypeClient gorpc.TransactionTypeClient
}

func NewProjectService(
	terminalBasicRepo database.TerminalBasicer,
	acquirerRepo database.Acquirer,
	terminalRepo database.Terminaler,
	transactionTypeClient gorpc.TransactionTypeClient,
) *ProjectService {
	return &ProjectService{
		terminalBasicRepo:     terminalBasicRepo,
		acquirerRepo:          acquirerRepo,
		terminalRepo:          terminalRepo,
		transactionTypeClient: transactionTypeClient,
	}
}

func (ps *ProjectService) GetAcquirers(ctx context.Context, projectID uint64) (_ []*model.Acquirer, err error) {
	ctx, span := dog.CreateSpan(ctx, "ProjectService_GetAcquirers")
	defer span.End()

	terminals, err := ps.terminalBasicRepo.GetAll(ctx, projectID)
	if err != nil {
		dog.L().Error("ProjectService_GetAcquirers", zap.Uint64("project_id", projectID), zap.Error(err))
		return nil, err
	}

	acquirerIds := make([]uint64, len(terminals))

	for i := range terminals {
		acquirerIds[i] = (terminals[i]).AcquirerID
	}

	acquirers, err := ps.acquirerRepo.GetAcquirersById(ctx, acquirerIds)

	if err != nil {
		dog.L().Error("ProjectService_GetAcquirers", zap.Uint64("project_id", projectID),
			zap.Uint64s("acquirer_ids", acquirerIds), zap.Error(err))
		return nil, err
	}

	return acquirers, err
}

func (ps *ProjectService) GetTerminals(
	ctx context.Context,
	projectID uint64,
) (_ []*schema.TerminalResponse, err error) {
	ctx, span := dog.CreateSpan(ctx, "ProjectService_GetTerminals")
	defer span.End()

	terminals, err := ps.terminalBasicRepo.GetAll(ctx, projectID)
	if err != nil {
		dog.L().Error("ProjectService_GetTerminals", zap.Uint64("project_id", projectID), zap.Error(err))
		return nil, err
	}

	return schema.NewTerminalsResponses(terminals), nil
}

func (ps *ProjectService) GetPaymentTypes(
	ctx context.Context,
	projectID uint64,
) (_ []*schema.ResponsePaymentType, err error) {
	ctx, span := dog.CreateSpan(ctx, "ProjectService_GetPaymentTypes")
	defer span.End()

	var responsePaymentType = make([]*schema.ResponsePaymentType, 0)

	paymentTypes, err := ps.terminalRepo.GetAllPaymentTypes(ctx, projectID)
	if err != nil {
		return nil, err
	}

	transactionTypeDic, err := ps.transactionTypeClient.GetAll(ctx, nil)
	if err != nil {
		return nil, err
	}

	for pt := range paymentTypes {
		for ptDic := range transactionTypeDic.Data {
			if paymentTypes[pt].TransactionTypeID != transactionTypeDic.GetData()[ptDic].GetId() {
				continue
			}

			responsePaymentType = append(responsePaymentType, &schema.ResponsePaymentType{
				ProjectID:         paymentTypes[pt].ProjectID,
				TransactionTypeID: paymentTypes[pt].TransactionTypeID,
				TransactionType: schema.TransactionType{
					Name: transactionTypeDic.GetData()[ptDic].GetName(),
					Code: transactionTypeDic.GetData()[ptDic].GetCode(),
					Id:   transactionTypeDic.GetData()[ptDic].GetId(),
				},
			})
		}
	}

	return responsePaymentType, nil
}

func (ps *ProjectService) GetAcquirersByPaymentType(
	ctx context.Context,
	projectID, transactionTypeID uint64,
) (_ []*model.Acquirer, err error) {
	ctx, span := dog.CreateSpan(ctx, "ProjectService_GetAcquirersByPaymentType")
	defer span.End()

	terminals, err := ps.terminalRepo.GetAllByPaymentType(ctx, projectID, transactionTypeID)
	if err != nil {
		dog.L().Error("ProjectService_GetAcquirersByPaymentType", zap.Uint64("project_id", projectID),
			zap.Uint64("type_id", transactionTypeID), zap.Error(err))
		return nil, err
	}

	acquirerIds := make([]uint64, len(terminals))

	for i := range terminals {
		acquirerIds[i] = (*terminals[i]).AcquirerID
	}

	return ps.acquirerRepo.GetAcquirersById(ctx, acquirerIds)
}
