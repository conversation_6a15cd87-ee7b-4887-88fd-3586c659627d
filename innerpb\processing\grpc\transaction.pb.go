// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.5
// 	protoc        v5.29.3
// source: inner/processing/grpc/transaction.proto

package grpc

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
	structpb "google.golang.org/protobuf/types/known/structpb"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type SetRefundWaitingStatusRequestV1 struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	TransactionId *uint64                `protobuf:"varint,1,opt,name=transaction_id,json=transactionId" json:"transaction_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SetRefundWaitingStatusRequestV1) Reset() {
	*x = SetRefundWaitingStatusRequestV1{}
	mi := &file_inner_processing_grpc_transaction_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SetRefundWaitingStatusRequestV1) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SetRefundWaitingStatusRequestV1) ProtoMessage() {}

func (x *SetRefundWaitingStatusRequestV1) ProtoReflect() protoreflect.Message {
	mi := &file_inner_processing_grpc_transaction_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SetRefundWaitingStatusRequestV1.ProtoReflect.Descriptor instead.
func (*SetRefundWaitingStatusRequestV1) Descriptor() ([]byte, []int) {
	return file_inner_processing_grpc_transaction_proto_rawDescGZIP(), []int{0}
}

func (x *SetRefundWaitingStatusRequestV1) GetTransactionId() uint64 {
	if x != nil && x.TransactionId != nil {
		return *x.TransactionId
	}
	return 0
}

type SetRefundWaitingStatusResponseV1 struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	StatusCode    *string                `protobuf:"bytes,1,opt,name=status_code,json=statusCode" json:"status_code,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SetRefundWaitingStatusResponseV1) Reset() {
	*x = SetRefundWaitingStatusResponseV1{}
	mi := &file_inner_processing_grpc_transaction_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SetRefundWaitingStatusResponseV1) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SetRefundWaitingStatusResponseV1) ProtoMessage() {}

func (x *SetRefundWaitingStatusResponseV1) ProtoReflect() protoreflect.Message {
	mi := &file_inner_processing_grpc_transaction_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SetRefundWaitingStatusResponseV1.ProtoReflect.Descriptor instead.
func (*SetRefundWaitingStatusResponseV1) Descriptor() ([]byte, []int) {
	return file_inner_processing_grpc_transaction_proto_rawDescGZIP(), []int{1}
}

func (x *SetRefundWaitingStatusResponseV1) GetStatusCode() string {
	if x != nil && x.StatusCode != nil {
		return *x.StatusCode
	}
	return ""
}

type GetPayInTransactionsByPeriodAndAcquirerReqV1 struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	AcquirerId    *uint64                `protobuf:"varint,1,opt,name=acquirer_id,json=acquirerId" json:"acquirer_id,omitempty"`
	StartPeriod   *timestamppb.Timestamp `protobuf:"bytes,2,opt,name=start_period,json=startPeriod" json:"start_period,omitempty"`
	EndPeriod     *timestamppb.Timestamp `protobuf:"bytes,3,opt,name=end_period,json=endPeriod" json:"end_period,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetPayInTransactionsByPeriodAndAcquirerReqV1) Reset() {
	*x = GetPayInTransactionsByPeriodAndAcquirerReqV1{}
	mi := &file_inner_processing_grpc_transaction_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetPayInTransactionsByPeriodAndAcquirerReqV1) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetPayInTransactionsByPeriodAndAcquirerReqV1) ProtoMessage() {}

func (x *GetPayInTransactionsByPeriodAndAcquirerReqV1) ProtoReflect() protoreflect.Message {
	mi := &file_inner_processing_grpc_transaction_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetPayInTransactionsByPeriodAndAcquirerReqV1.ProtoReflect.Descriptor instead.
func (*GetPayInTransactionsByPeriodAndAcquirerReqV1) Descriptor() ([]byte, []int) {
	return file_inner_processing_grpc_transaction_proto_rawDescGZIP(), []int{2}
}

func (x *GetPayInTransactionsByPeriodAndAcquirerReqV1) GetAcquirerId() uint64 {
	if x != nil && x.AcquirerId != nil {
		return *x.AcquirerId
	}
	return 0
}

func (x *GetPayInTransactionsByPeriodAndAcquirerReqV1) GetStartPeriod() *timestamppb.Timestamp {
	if x != nil {
		return x.StartPeriod
	}
	return nil
}

func (x *GetPayInTransactionsByPeriodAndAcquirerReqV1) GetEndPeriod() *timestamppb.Timestamp {
	if x != nil {
		return x.EndPeriod
	}
	return nil
}

type GetTransactionsByStatusReqV1 struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Status        *string                `protobuf:"bytes,1,opt,name=status" json:"status,omitempty"`
	StartPeriod   *timestamppb.Timestamp `protobuf:"bytes,2,opt,name=start_period,json=startPeriod" json:"start_period,omitempty"`
	EndPeriod     *timestamppb.Timestamp `protobuf:"bytes,3,opt,name=end_period,json=endPeriod" json:"end_period,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetTransactionsByStatusReqV1) Reset() {
	*x = GetTransactionsByStatusReqV1{}
	mi := &file_inner_processing_grpc_transaction_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetTransactionsByStatusReqV1) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetTransactionsByStatusReqV1) ProtoMessage() {}

func (x *GetTransactionsByStatusReqV1) ProtoReflect() protoreflect.Message {
	mi := &file_inner_processing_grpc_transaction_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetTransactionsByStatusReqV1.ProtoReflect.Descriptor instead.
func (*GetTransactionsByStatusReqV1) Descriptor() ([]byte, []int) {
	return file_inner_processing_grpc_transaction_proto_rawDescGZIP(), []int{3}
}

func (x *GetTransactionsByStatusReqV1) GetStatus() string {
	if x != nil && x.Status != nil {
		return *x.Status
	}
	return ""
}

func (x *GetTransactionsByStatusReqV1) GetStartPeriod() *timestamppb.Timestamp {
	if x != nil {
		return x.StartPeriod
	}
	return nil
}

func (x *GetTransactionsByStatusReqV1) GetEndPeriod() *timestamppb.Timestamp {
	if x != nil {
		return x.EndPeriod
	}
	return nil
}

type GetTransactionsByCallbackStatusReqV1 struct {
	state          protoimpl.MessageState `protogen:"open.v1"`
	CallbackStatus *uint32                `protobuf:"varint,1,opt,name=callback_status,json=callbackStatus" json:"callback_status,omitempty"`
	StartPeriod    *timestamppb.Timestamp `protobuf:"bytes,2,opt,name=start_period,json=startPeriod" json:"start_period,omitempty"`
	EndPeriod      *timestamppb.Timestamp `protobuf:"bytes,3,opt,name=end_period,json=endPeriod" json:"end_period,omitempty"`
	unknownFields  protoimpl.UnknownFields
	sizeCache      protoimpl.SizeCache
}

func (x *GetTransactionsByCallbackStatusReqV1) Reset() {
	*x = GetTransactionsByCallbackStatusReqV1{}
	mi := &file_inner_processing_grpc_transaction_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetTransactionsByCallbackStatusReqV1) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetTransactionsByCallbackStatusReqV1) ProtoMessage() {}

func (x *GetTransactionsByCallbackStatusReqV1) ProtoReflect() protoreflect.Message {
	mi := &file_inner_processing_grpc_transaction_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetTransactionsByCallbackStatusReqV1.ProtoReflect.Descriptor instead.
func (*GetTransactionsByCallbackStatusReqV1) Descriptor() ([]byte, []int) {
	return file_inner_processing_grpc_transaction_proto_rawDescGZIP(), []int{4}
}

func (x *GetTransactionsByCallbackStatusReqV1) GetCallbackStatus() uint32 {
	if x != nil && x.CallbackStatus != nil {
		return *x.CallbackStatus
	}
	return 0
}

func (x *GetTransactionsByCallbackStatusReqV1) GetStartPeriod() *timestamppb.Timestamp {
	if x != nil {
		return x.StartPeriod
	}
	return nil
}

func (x *GetTransactionsByCallbackStatusReqV1) GetEndPeriod() *timestamppb.Timestamp {
	if x != nil {
		return x.EndPeriod
	}
	return nil
}

type GetTransactionsByFinalStatusAndPeriodWithLimitReqV1 struct {
	state            protoimpl.MessageState `protogen:"open.v1"`
	IsFinal          *bool                  `protobuf:"varint,1,opt,name=is_final,json=isFinal" json:"is_final,omitempty"`
	TransactionLimit *uint32                `protobuf:"varint,2,opt,name=transactionLimit" json:"transactionLimit,omitempty"`
	StartPeriod      *timestamppb.Timestamp `protobuf:"bytes,3,opt,name=start_period,json=startPeriod" json:"start_period,omitempty"`
	EndPeriod        *timestamppb.Timestamp `protobuf:"bytes,4,opt,name=end_period,json=endPeriod" json:"end_period,omitempty"`
	unknownFields    protoimpl.UnknownFields
	sizeCache        protoimpl.SizeCache
}

func (x *GetTransactionsByFinalStatusAndPeriodWithLimitReqV1) Reset() {
	*x = GetTransactionsByFinalStatusAndPeriodWithLimitReqV1{}
	mi := &file_inner_processing_grpc_transaction_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetTransactionsByFinalStatusAndPeriodWithLimitReqV1) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetTransactionsByFinalStatusAndPeriodWithLimitReqV1) ProtoMessage() {}

func (x *GetTransactionsByFinalStatusAndPeriodWithLimitReqV1) ProtoReflect() protoreflect.Message {
	mi := &file_inner_processing_grpc_transaction_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetTransactionsByFinalStatusAndPeriodWithLimitReqV1.ProtoReflect.Descriptor instead.
func (*GetTransactionsByFinalStatusAndPeriodWithLimitReqV1) Descriptor() ([]byte, []int) {
	return file_inner_processing_grpc_transaction_proto_rawDescGZIP(), []int{5}
}

func (x *GetTransactionsByFinalStatusAndPeriodWithLimitReqV1) GetIsFinal() bool {
	if x != nil && x.IsFinal != nil {
		return *x.IsFinal
	}
	return false
}

func (x *GetTransactionsByFinalStatusAndPeriodWithLimitReqV1) GetTransactionLimit() uint32 {
	if x != nil && x.TransactionLimit != nil {
		return *x.TransactionLimit
	}
	return 0
}

func (x *GetTransactionsByFinalStatusAndPeriodWithLimitReqV1) GetStartPeriod() *timestamppb.Timestamp {
	if x != nil {
		return x.StartPeriod
	}
	return nil
}

func (x *GetTransactionsByFinalStatusAndPeriodWithLimitReqV1) GetEndPeriod() *timestamppb.Timestamp {
	if x != nil {
		return x.EndPeriod
	}
	return nil
}

type GetTransactionByIDRequestV1 struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            *uint64                `protobuf:"varint,1,opt,name=id" json:"id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetTransactionByIDRequestV1) Reset() {
	*x = GetTransactionByIDRequestV1{}
	mi := &file_inner_processing_grpc_transaction_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetTransactionByIDRequestV1) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetTransactionByIDRequestV1) ProtoMessage() {}

func (x *GetTransactionByIDRequestV1) ProtoReflect() protoreflect.Message {
	mi := &file_inner_processing_grpc_transaction_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetTransactionByIDRequestV1.ProtoReflect.Descriptor instead.
func (*GetTransactionByIDRequestV1) Descriptor() ([]byte, []int) {
	return file_inner_processing_grpc_transaction_proto_rawDescGZIP(), []int{6}
}

func (x *GetTransactionByIDRequestV1) GetId() uint64 {
	if x != nil && x.Id != nil {
		return *x.Id
	}
	return 0
}

type TransactionDataV1 struct {
	state              protoimpl.MessageState `protogen:"open.v1"`
	CreatedAt          *timestamppb.Timestamp `protobuf:"bytes,1,opt,name=created_at,json=createdAt" json:"created_at,omitempty"`
	UpdatedAt          *timestamppb.Timestamp `protobuf:"bytes,2,opt,name=updated_at,json=updatedAt" json:"updated_at,omitempty"`
	FinishedAt         *timestamppb.Timestamp `protobuf:"bytes,3,opt,name=finished_at,json=finishedAt" json:"finished_at,omitempty"`
	Id                 *uint64                `protobuf:"varint,4,opt,name=id" json:"id,omitempty"`
	AcquirerId         *uint64                `protobuf:"varint,5,opt,name=acquirer_id,json=acquirerId" json:"acquirer_id,omitempty"`
	TerminalId         *uint64                `protobuf:"varint,6,opt,name=terminal_id,json=terminalId" json:"terminal_id,omitempty"`
	BankReferenceId    *string                `protobuf:"bytes,7,opt,name=bank_reference_id,json=bankReferenceId" json:"bank_reference_id,omitempty"`
	CardId             *uint64                `protobuf:"varint,8,opt,name=card_id,json=cardId" json:"card_id,omitempty"`
	ProjectId          *uint64                `protobuf:"varint,9,opt,name=project_id,json=projectId" json:"project_id,omitempty"`
	MerchantId         *uint64                `protobuf:"varint,10,opt,name=merchant_id,json=merchantId" json:"merchant_id,omitempty"`
	ProjectReferenceId *string                `protobuf:"bytes,11,opt,name=project_reference_id,json=projectReferenceId" json:"project_reference_id,omitempty"`
	ProjectClientId    *string                `protobuf:"bytes,12,opt,name=project_client_id,json=projectClientId" json:"project_client_id,omitempty"`
	StatusId           *uint64                `protobuf:"varint,13,opt,name=status_id,json=statusId" json:"status_id,omitempty"`
	TypeId             *uint64                `protobuf:"varint,14,opt,name=type_id,json=typeId" json:"type_id,omitempty"`
	Amount             *float64               `protobuf:"fixed64,15,opt,name=amount" json:"amount,omitempty"`
	Try                *uint64                `protobuf:"varint,16,opt,name=try" json:"try,omitempty"`
	Description        *string                `protobuf:"bytes,17,opt,name=description" json:"description,omitempty"`
	TypeCode           *string                `protobuf:"bytes,18,opt,name=type_code,json=typeCode" json:"type_code,omitempty"`
	StatusCode         *string                `protobuf:"bytes,19,opt,name=status_code,json=statusCode" json:"status_code,omitempty"`
	IsFinalStatus      *bool                  `protobuf:"varint,20,opt,name=is_final_status,json=isFinalStatus" json:"is_final_status,omitempty"`
	TransactionInfo    *TransactionInfo       `protobuf:"bytes,21,opt,name=transaction_info,json=transactionInfo" json:"transaction_info,omitempty"`
	AggregatedTypeId   *uint64                `protobuf:"varint,22,opt,name=aggregated_type_id,json=aggregatedTypeId" json:"aggregated_type_id,omitempty"`
	unknownFields      protoimpl.UnknownFields
	sizeCache          protoimpl.SizeCache
}

func (x *TransactionDataV1) Reset() {
	*x = TransactionDataV1{}
	mi := &file_inner_processing_grpc_transaction_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *TransactionDataV1) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TransactionDataV1) ProtoMessage() {}

func (x *TransactionDataV1) ProtoReflect() protoreflect.Message {
	mi := &file_inner_processing_grpc_transaction_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TransactionDataV1.ProtoReflect.Descriptor instead.
func (*TransactionDataV1) Descriptor() ([]byte, []int) {
	return file_inner_processing_grpc_transaction_proto_rawDescGZIP(), []int{7}
}

func (x *TransactionDataV1) GetCreatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.CreatedAt
	}
	return nil
}

func (x *TransactionDataV1) GetUpdatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.UpdatedAt
	}
	return nil
}

func (x *TransactionDataV1) GetFinishedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.FinishedAt
	}
	return nil
}

func (x *TransactionDataV1) GetId() uint64 {
	if x != nil && x.Id != nil {
		return *x.Id
	}
	return 0
}

func (x *TransactionDataV1) GetAcquirerId() uint64 {
	if x != nil && x.AcquirerId != nil {
		return *x.AcquirerId
	}
	return 0
}

func (x *TransactionDataV1) GetTerminalId() uint64 {
	if x != nil && x.TerminalId != nil {
		return *x.TerminalId
	}
	return 0
}

func (x *TransactionDataV1) GetBankReferenceId() string {
	if x != nil && x.BankReferenceId != nil {
		return *x.BankReferenceId
	}
	return ""
}

func (x *TransactionDataV1) GetCardId() uint64 {
	if x != nil && x.CardId != nil {
		return *x.CardId
	}
	return 0
}

func (x *TransactionDataV1) GetProjectId() uint64 {
	if x != nil && x.ProjectId != nil {
		return *x.ProjectId
	}
	return 0
}

func (x *TransactionDataV1) GetMerchantId() uint64 {
	if x != nil && x.MerchantId != nil {
		return *x.MerchantId
	}
	return 0
}

func (x *TransactionDataV1) GetProjectReferenceId() string {
	if x != nil && x.ProjectReferenceId != nil {
		return *x.ProjectReferenceId
	}
	return ""
}

func (x *TransactionDataV1) GetProjectClientId() string {
	if x != nil && x.ProjectClientId != nil {
		return *x.ProjectClientId
	}
	return ""
}

func (x *TransactionDataV1) GetStatusId() uint64 {
	if x != nil && x.StatusId != nil {
		return *x.StatusId
	}
	return 0
}

func (x *TransactionDataV1) GetTypeId() uint64 {
	if x != nil && x.TypeId != nil {
		return *x.TypeId
	}
	return 0
}

func (x *TransactionDataV1) GetAmount() float64 {
	if x != nil && x.Amount != nil {
		return *x.Amount
	}
	return 0
}

func (x *TransactionDataV1) GetTry() uint64 {
	if x != nil && x.Try != nil {
		return *x.Try
	}
	return 0
}

func (x *TransactionDataV1) GetDescription() string {
	if x != nil && x.Description != nil {
		return *x.Description
	}
	return ""
}

func (x *TransactionDataV1) GetTypeCode() string {
	if x != nil && x.TypeCode != nil {
		return *x.TypeCode
	}
	return ""
}

func (x *TransactionDataV1) GetStatusCode() string {
	if x != nil && x.StatusCode != nil {
		return *x.StatusCode
	}
	return ""
}

func (x *TransactionDataV1) GetIsFinalStatus() bool {
	if x != nil && x.IsFinalStatus != nil {
		return *x.IsFinalStatus
	}
	return false
}

func (x *TransactionDataV1) GetTransactionInfo() *TransactionInfo {
	if x != nil {
		return x.TransactionInfo
	}
	return nil
}

func (x *TransactionDataV1) GetAggregatedTypeId() uint64 {
	if x != nil && x.AggregatedTypeId != nil {
		return *x.AggregatedTypeId
	}
	return 0
}

type TransactionInfo struct {
	state                protoimpl.MessageState `protogen:"open.v1"`
	CreatedAt            *timestamppb.Timestamp `protobuf:"bytes,1,opt,name=created_at,json=createdAt" json:"created_at,omitempty"`
	UpdatedAt            *timestamppb.Timestamp `protobuf:"bytes,2,opt,name=updated_at,json=updatedAt" json:"updated_at,omitempty"`
	TransactionId        *uint64                `protobuf:"varint,3,opt,name=transaction_id,json=transactionId" json:"transaction_id,omitempty"`
	TransactionCreatedAt *timestamppb.Timestamp `protobuf:"bytes,4,opt,name=transaction_created_at,json=transactionCreatedAt" json:"transaction_created_at,omitempty"`
	CallbackUrl          *string                `protobuf:"bytes,5,opt,name=callback_url,json=callbackUrl" json:"callback_url,omitempty"`
	ConfirmUrl           *string                `protobuf:"bytes,6,opt,name=confirm_url,json=confirmUrl" json:"confirm_url,omitempty"`
	SuccessRedirectUrl   *string                `protobuf:"bytes,7,opt,name=success_redirect_url,json=successRedirectUrl" json:"success_redirect_url,omitempty"`
	FailureRedirectUrl   *string                `protobuf:"bytes,8,opt,name=failure_redirect_url,json=failureRedirectUrl" json:"failure_redirect_url,omitempty"`
	CallbackStatus       *uint32                `protobuf:"varint,9,opt,name=callback_status,json=callbackStatus" json:"callback_status,omitempty"`
	UserPhone            *string                `protobuf:"bytes,10,opt,name=user_phone,json=userPhone" json:"user_phone,omitempty"`
	UserEmail            *string                `protobuf:"bytes,11,opt,name=user_email,json=userEmail" json:"user_email,omitempty"`
	Shipment             *string                `protobuf:"bytes,12,opt,name=shipment" json:"shipment,omitempty"`
	BankOrderId          *string                `protobuf:"bytes,13,opt,name=bank_order_id,json=bankOrderId" json:"bank_order_id,omitempty"`
	JobsMessage          *structpb.Struct       `protobuf:"bytes,14,opt,name=jobs_message,json=jobsMessage" json:"jobs_message,omitempty"`
	unknownFields        protoimpl.UnknownFields
	sizeCache            protoimpl.SizeCache
}

func (x *TransactionInfo) Reset() {
	*x = TransactionInfo{}
	mi := &file_inner_processing_grpc_transaction_proto_msgTypes[8]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *TransactionInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TransactionInfo) ProtoMessage() {}

func (x *TransactionInfo) ProtoReflect() protoreflect.Message {
	mi := &file_inner_processing_grpc_transaction_proto_msgTypes[8]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TransactionInfo.ProtoReflect.Descriptor instead.
func (*TransactionInfo) Descriptor() ([]byte, []int) {
	return file_inner_processing_grpc_transaction_proto_rawDescGZIP(), []int{8}
}

func (x *TransactionInfo) GetCreatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.CreatedAt
	}
	return nil
}

func (x *TransactionInfo) GetUpdatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.UpdatedAt
	}
	return nil
}

func (x *TransactionInfo) GetTransactionId() uint64 {
	if x != nil && x.TransactionId != nil {
		return *x.TransactionId
	}
	return 0
}

func (x *TransactionInfo) GetTransactionCreatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.TransactionCreatedAt
	}
	return nil
}

func (x *TransactionInfo) GetCallbackUrl() string {
	if x != nil && x.CallbackUrl != nil {
		return *x.CallbackUrl
	}
	return ""
}

func (x *TransactionInfo) GetConfirmUrl() string {
	if x != nil && x.ConfirmUrl != nil {
		return *x.ConfirmUrl
	}
	return ""
}

func (x *TransactionInfo) GetSuccessRedirectUrl() string {
	if x != nil && x.SuccessRedirectUrl != nil {
		return *x.SuccessRedirectUrl
	}
	return ""
}

func (x *TransactionInfo) GetFailureRedirectUrl() string {
	if x != nil && x.FailureRedirectUrl != nil {
		return *x.FailureRedirectUrl
	}
	return ""
}

func (x *TransactionInfo) GetCallbackStatus() uint32 {
	if x != nil && x.CallbackStatus != nil {
		return *x.CallbackStatus
	}
	return 0
}

func (x *TransactionInfo) GetUserPhone() string {
	if x != nil && x.UserPhone != nil {
		return *x.UserPhone
	}
	return ""
}

func (x *TransactionInfo) GetUserEmail() string {
	if x != nil && x.UserEmail != nil {
		return *x.UserEmail
	}
	return ""
}

func (x *TransactionInfo) GetShipment() string {
	if x != nil && x.Shipment != nil {
		return *x.Shipment
	}
	return ""
}

func (x *TransactionInfo) GetBankOrderId() string {
	if x != nil && x.BankOrderId != nil {
		return *x.BankOrderId
	}
	return ""
}

func (x *TransactionInfo) GetJobsMessage() *structpb.Struct {
	if x != nil {
		return x.JobsMessage
	}
	return nil
}

type GetTransactionsResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Transactions  []*TransactionDataV1   `protobuf:"bytes,1,rep,name=transactions" json:"transactions,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetTransactionsResponse) Reset() {
	*x = GetTransactionsResponse{}
	mi := &file_inner_processing_grpc_transaction_proto_msgTypes[9]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetTransactionsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetTransactionsResponse) ProtoMessage() {}

func (x *GetTransactionsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_inner_processing_grpc_transaction_proto_msgTypes[9]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetTransactionsResponse.ProtoReflect.Descriptor instead.
func (*GetTransactionsResponse) Descriptor() ([]byte, []int) {
	return file_inner_processing_grpc_transaction_proto_rawDescGZIP(), []int{9}
}

func (x *GetTransactionsResponse) GetTransactions() []*TransactionDataV1 {
	if x != nil {
		return x.Transactions
	}
	return nil
}

type UpdateTransactionStatusRequestV1 struct {
	state               protoimpl.MessageState `protogen:"open.v1"`
	TransactionId       *uint64                `protobuf:"varint,1,opt,name=transaction_id,json=transactionId" json:"transaction_id,omitempty"`
	TransactionStatusId *uint64                `protobuf:"varint,2,opt,name=transaction_status_id,json=transactionStatusId" json:"transaction_status_id,omitempty"`
	unknownFields       protoimpl.UnknownFields
	sizeCache           protoimpl.SizeCache
}

func (x *UpdateTransactionStatusRequestV1) Reset() {
	*x = UpdateTransactionStatusRequestV1{}
	mi := &file_inner_processing_grpc_transaction_proto_msgTypes[10]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdateTransactionStatusRequestV1) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateTransactionStatusRequestV1) ProtoMessage() {}

func (x *UpdateTransactionStatusRequestV1) ProtoReflect() protoreflect.Message {
	mi := &file_inner_processing_grpc_transaction_proto_msgTypes[10]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateTransactionStatusRequestV1.ProtoReflect.Descriptor instead.
func (*UpdateTransactionStatusRequestV1) Descriptor() ([]byte, []int) {
	return file_inner_processing_grpc_transaction_proto_rawDescGZIP(), []int{10}
}

func (x *UpdateTransactionStatusRequestV1) GetTransactionId() uint64 {
	if x != nil && x.TransactionId != nil {
		return *x.TransactionId
	}
	return 0
}

func (x *UpdateTransactionStatusRequestV1) GetTransactionStatusId() uint64 {
	if x != nil && x.TransactionStatusId != nil {
		return *x.TransactionStatusId
	}
	return 0
}

type UpdateCallbackStatusRequestV1 struct {
	state          protoimpl.MessageState `protogen:"open.v1"`
	TransactionId  *uint64                `protobuf:"varint,1,opt,name=transaction_id,json=transactionId" json:"transaction_id,omitempty"`
	CallbackStatus *uint32                `protobuf:"varint,2,opt,name=callback_status,json=callbackStatus" json:"callback_status,omitempty"`
	unknownFields  protoimpl.UnknownFields
	sizeCache      protoimpl.SizeCache
}

func (x *UpdateCallbackStatusRequestV1) Reset() {
	*x = UpdateCallbackStatusRequestV1{}
	mi := &file_inner_processing_grpc_transaction_proto_msgTypes[11]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdateCallbackStatusRequestV1) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateCallbackStatusRequestV1) ProtoMessage() {}

func (x *UpdateCallbackStatusRequestV1) ProtoReflect() protoreflect.Message {
	mi := &file_inner_processing_grpc_transaction_proto_msgTypes[11]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateCallbackStatusRequestV1.ProtoReflect.Descriptor instead.
func (*UpdateCallbackStatusRequestV1) Descriptor() ([]byte, []int) {
	return file_inner_processing_grpc_transaction_proto_rawDescGZIP(), []int{11}
}

func (x *UpdateCallbackStatusRequestV1) GetTransactionId() uint64 {
	if x != nil && x.TransactionId != nil {
		return *x.TransactionId
	}
	return 0
}

func (x *UpdateCallbackStatusRequestV1) GetCallbackStatus() uint32 {
	if x != nil && x.CallbackStatus != nil {
		return *x.CallbackStatus
	}
	return 0
}

type GetTransactionTypeByIDRequestV1 struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	TypeId        *uint64                `protobuf:"varint,1,opt,name=type_id,json=typeId" json:"type_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetTransactionTypeByIDRequestV1) Reset() {
	*x = GetTransactionTypeByIDRequestV1{}
	mi := &file_inner_processing_grpc_transaction_proto_msgTypes[12]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetTransactionTypeByIDRequestV1) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetTransactionTypeByIDRequestV1) ProtoMessage() {}

func (x *GetTransactionTypeByIDRequestV1) ProtoReflect() protoreflect.Message {
	mi := &file_inner_processing_grpc_transaction_proto_msgTypes[12]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetTransactionTypeByIDRequestV1.ProtoReflect.Descriptor instead.
func (*GetTransactionTypeByIDRequestV1) Descriptor() ([]byte, []int) {
	return file_inner_processing_grpc_transaction_proto_rawDescGZIP(), []int{12}
}

func (x *GetTransactionTypeByIDRequestV1) GetTypeId() uint64 {
	if x != nil && x.TypeId != nil {
		return *x.TypeId
	}
	return 0
}

type GetTransactionTypeByIDResponseV1 struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            *uint64                `protobuf:"varint,1,opt,name=id" json:"id,omitempty"`
	Code          *string                `protobuf:"bytes,2,opt,name=code" json:"code,omitempty"`
	Name          *string                `protobuf:"bytes,3,opt,name=name" json:"name,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetTransactionTypeByIDResponseV1) Reset() {
	*x = GetTransactionTypeByIDResponseV1{}
	mi := &file_inner_processing_grpc_transaction_proto_msgTypes[13]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetTransactionTypeByIDResponseV1) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetTransactionTypeByIDResponseV1) ProtoMessage() {}

func (x *GetTransactionTypeByIDResponseV1) ProtoReflect() protoreflect.Message {
	mi := &file_inner_processing_grpc_transaction_proto_msgTypes[13]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetTransactionTypeByIDResponseV1.ProtoReflect.Descriptor instead.
func (*GetTransactionTypeByIDResponseV1) Descriptor() ([]byte, []int) {
	return file_inner_processing_grpc_transaction_proto_rawDescGZIP(), []int{13}
}

func (x *GetTransactionTypeByIDResponseV1) GetId() uint64 {
	if x != nil && x.Id != nil {
		return *x.Id
	}
	return 0
}

func (x *GetTransactionTypeByIDResponseV1) GetCode() string {
	if x != nil && x.Code != nil {
		return *x.Code
	}
	return ""
}

func (x *GetTransactionTypeByIDResponseV1) GetName() string {
	if x != nil && x.Name != nil {
		return *x.Name
	}
	return ""
}

type MakeAutoChargeRequestV1 struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	TransactionId *uint64                `protobuf:"varint,1,opt,name=transaction_id,json=transactionId" json:"transaction_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *MakeAutoChargeRequestV1) Reset() {
	*x = MakeAutoChargeRequestV1{}
	mi := &file_inner_processing_grpc_transaction_proto_msgTypes[14]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *MakeAutoChargeRequestV1) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MakeAutoChargeRequestV1) ProtoMessage() {}

func (x *MakeAutoChargeRequestV1) ProtoReflect() protoreflect.Message {
	mi := &file_inner_processing_grpc_transaction_proto_msgTypes[14]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MakeAutoChargeRequestV1.ProtoReflect.Descriptor instead.
func (*MakeAutoChargeRequestV1) Descriptor() ([]byte, []int) {
	return file_inner_processing_grpc_transaction_proto_rawDescGZIP(), []int{14}
}

func (x *MakeAutoChargeRequestV1) GetTransactionId() uint64 {
	if x != nil && x.TransactionId != nil {
		return *x.TransactionId
	}
	return 0
}

type GetTransactionsByProjectInfoRequestV1 struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	ProjectId     *uint64                `protobuf:"varint,1,opt,name=project_id,json=projectId" json:"project_id,omitempty"`
	MerchantId    *uint64                `protobuf:"varint,2,opt,name=merchant_id,json=merchantId" json:"merchant_id,omitempty"`
	StartPeriod   *timestamppb.Timestamp `protobuf:"bytes,3,opt,name=start_period,json=startPeriod" json:"start_period,omitempty"`
	EndPeriod     *timestamppb.Timestamp `protobuf:"bytes,4,opt,name=end_period,json=endPeriod" json:"end_period,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetTransactionsByProjectInfoRequestV1) Reset() {
	*x = GetTransactionsByProjectInfoRequestV1{}
	mi := &file_inner_processing_grpc_transaction_proto_msgTypes[15]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetTransactionsByProjectInfoRequestV1) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetTransactionsByProjectInfoRequestV1) ProtoMessage() {}

func (x *GetTransactionsByProjectInfoRequestV1) ProtoReflect() protoreflect.Message {
	mi := &file_inner_processing_grpc_transaction_proto_msgTypes[15]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetTransactionsByProjectInfoRequestV1.ProtoReflect.Descriptor instead.
func (*GetTransactionsByProjectInfoRequestV1) Descriptor() ([]byte, []int) {
	return file_inner_processing_grpc_transaction_proto_rawDescGZIP(), []int{15}
}

func (x *GetTransactionsByProjectInfoRequestV1) GetProjectId() uint64 {
	if x != nil && x.ProjectId != nil {
		return *x.ProjectId
	}
	return 0
}

func (x *GetTransactionsByProjectInfoRequestV1) GetMerchantId() uint64 {
	if x != nil && x.MerchantId != nil {
		return *x.MerchantId
	}
	return 0
}

func (x *GetTransactionsByProjectInfoRequestV1) GetStartPeriod() *timestamppb.Timestamp {
	if x != nil {
		return x.StartPeriod
	}
	return nil
}

func (x *GetTransactionsByProjectInfoRequestV1) GetEndPeriod() *timestamppb.Timestamp {
	if x != nil {
		return x.EndPeriod
	}
	return nil
}

type CheckTransactionHashRequestV1 struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	TransactionId *uint64                `protobuf:"varint,1,opt,name=transaction_id,json=transactionId" json:"transaction_id,omitempty"`
	Hash          *string                `protobuf:"bytes,2,opt,name=hash" json:"hash,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CheckTransactionHashRequestV1) Reset() {
	*x = CheckTransactionHashRequestV1{}
	mi := &file_inner_processing_grpc_transaction_proto_msgTypes[16]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CheckTransactionHashRequestV1) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CheckTransactionHashRequestV1) ProtoMessage() {}

func (x *CheckTransactionHashRequestV1) ProtoReflect() protoreflect.Message {
	mi := &file_inner_processing_grpc_transaction_proto_msgTypes[16]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CheckTransactionHashRequestV1.ProtoReflect.Descriptor instead.
func (*CheckTransactionHashRequestV1) Descriptor() ([]byte, []int) {
	return file_inner_processing_grpc_transaction_proto_rawDescGZIP(), []int{16}
}

func (x *CheckTransactionHashRequestV1) GetTransactionId() uint64 {
	if x != nil && x.TransactionId != nil {
		return *x.TransactionId
	}
	return 0
}

func (x *CheckTransactionHashRequestV1) GetHash() string {
	if x != nil && x.Hash != nil {
		return *x.Hash
	}
	return ""
}

type CreateTransactionByPhoneRequest struct {
	state              protoimpl.MessageState `protogen:"open.v1"`
	Amount             *float64               `protobuf:"fixed64,1,opt,name=amount" json:"amount,omitempty"`
	CallbackUrl        *string                `protobuf:"bytes,2,opt,name=callback_url,json=callbackUrl" json:"callback_url,omitempty"`
	Description        *string                `protobuf:"bytes,3,opt,name=description" json:"description,omitempty"`
	ProjectOrderId     *string                `protobuf:"bytes,4,opt,name=project_order_id,json=projectOrderId" json:"project_order_id,omitempty"`
	MerchantId         *uint64                `protobuf:"varint,5,opt,name=merchant_id,json=merchantId" json:"merchant_id,omitempty"`
	ProjectClientId    *string                `protobuf:"bytes,6,opt,name=project_client_id,json=projectClientId" json:"project_client_id,omitempty"`
	ProjectId          *uint64                `protobuf:"varint,7,opt,name=project_id,json=projectId" json:"project_id,omitempty"`
	ProjectReferenceId *string                `protobuf:"bytes,8,opt,name=project_reference_id,json=projectReferenceId" json:"project_reference_id,omitempty"`
	UserPhone          *string                `protobuf:"bytes,9,opt,name=user_phone,json=userPhone" json:"user_phone,omitempty"`
	Email              *string                `protobuf:"bytes,10,opt,name=email" json:"email,omitempty"`
	AdditionalData     map[string]string      `protobuf:"bytes,11,rep,name=additional_data,json=additionalData" json:"additional_data,omitempty" protobuf_key:"bytes,1,opt,name=key" protobuf_val:"bytes,2,opt,name=value"`
	VisaAliasToken     *string                `protobuf:"bytes,12,opt,name=visa_alias_token,json=visaAliasToken" json:"visa_alias_token,omitempty"`
	TerminalId         *uint64                `protobuf:"varint,13,opt,name=terminal_id,json=terminalId" json:"terminal_id,omitempty"`
	AcquirerId         *uint64                `protobuf:"varint,14,opt,name=acquirer_id,json=acquirerId" json:"acquirer_id,omitempty"`
	unknownFields      protoimpl.UnknownFields
	sizeCache          protoimpl.SizeCache
}

func (x *CreateTransactionByPhoneRequest) Reset() {
	*x = CreateTransactionByPhoneRequest{}
	mi := &file_inner_processing_grpc_transaction_proto_msgTypes[17]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreateTransactionByPhoneRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateTransactionByPhoneRequest) ProtoMessage() {}

func (x *CreateTransactionByPhoneRequest) ProtoReflect() protoreflect.Message {
	mi := &file_inner_processing_grpc_transaction_proto_msgTypes[17]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateTransactionByPhoneRequest.ProtoReflect.Descriptor instead.
func (*CreateTransactionByPhoneRequest) Descriptor() ([]byte, []int) {
	return file_inner_processing_grpc_transaction_proto_rawDescGZIP(), []int{17}
}

func (x *CreateTransactionByPhoneRequest) GetAmount() float64 {
	if x != nil && x.Amount != nil {
		return *x.Amount
	}
	return 0
}

func (x *CreateTransactionByPhoneRequest) GetCallbackUrl() string {
	if x != nil && x.CallbackUrl != nil {
		return *x.CallbackUrl
	}
	return ""
}

func (x *CreateTransactionByPhoneRequest) GetDescription() string {
	if x != nil && x.Description != nil {
		return *x.Description
	}
	return ""
}

func (x *CreateTransactionByPhoneRequest) GetProjectOrderId() string {
	if x != nil && x.ProjectOrderId != nil {
		return *x.ProjectOrderId
	}
	return ""
}

func (x *CreateTransactionByPhoneRequest) GetMerchantId() uint64 {
	if x != nil && x.MerchantId != nil {
		return *x.MerchantId
	}
	return 0
}

func (x *CreateTransactionByPhoneRequest) GetProjectClientId() string {
	if x != nil && x.ProjectClientId != nil {
		return *x.ProjectClientId
	}
	return ""
}

func (x *CreateTransactionByPhoneRequest) GetProjectId() uint64 {
	if x != nil && x.ProjectId != nil {
		return *x.ProjectId
	}
	return 0
}

func (x *CreateTransactionByPhoneRequest) GetProjectReferenceId() string {
	if x != nil && x.ProjectReferenceId != nil {
		return *x.ProjectReferenceId
	}
	return ""
}

func (x *CreateTransactionByPhoneRequest) GetUserPhone() string {
	if x != nil && x.UserPhone != nil {
		return *x.UserPhone
	}
	return ""
}

func (x *CreateTransactionByPhoneRequest) GetEmail() string {
	if x != nil && x.Email != nil {
		return *x.Email
	}
	return ""
}

func (x *CreateTransactionByPhoneRequest) GetAdditionalData() map[string]string {
	if x != nil {
		return x.AdditionalData
	}
	return nil
}

func (x *CreateTransactionByPhoneRequest) GetVisaAliasToken() string {
	if x != nil && x.VisaAliasToken != nil {
		return *x.VisaAliasToken
	}
	return ""
}

func (x *CreateTransactionByPhoneRequest) GetTerminalId() uint64 {
	if x != nil && x.TerminalId != nil {
		return *x.TerminalId
	}
	return 0
}

func (x *CreateTransactionByPhoneRequest) GetAcquirerId() uint64 {
	if x != nil && x.AcquirerId != nil {
		return *x.AcquirerId
	}
	return 0
}

type CreateTransactionByPhoneResponse struct {
	state             protoimpl.MessageState `protogen:"open.v1"`
	TransactionId     *uint64                `protobuf:"varint,1,opt,name=transaction_id,json=transactionId" json:"transaction_id,omitempty"`
	ProjectId         *uint64                `protobuf:"varint,2,opt,name=project_id,json=projectId" json:"project_id,omitempty"`
	MerchantId        *uint64                `protobuf:"varint,3,opt,name=merchant_id,json=merchantId" json:"merchant_id,omitempty"`
	Amount            *float64               `protobuf:"fixed64,4,opt,name=amount" json:"amount,omitempty"`
	CreatedAt         *timestamppb.Timestamp `protobuf:"bytes,5,opt,name=created_at,json=createdAt" json:"created_at,omitempty"`
	TransactionTypeId *uint64                `protobuf:"varint,6,opt,name=transaction_type_id,json=transactionTypeId" json:"transaction_type_id,omitempty"`
	unknownFields     protoimpl.UnknownFields
	sizeCache         protoimpl.SizeCache
}

func (x *CreateTransactionByPhoneResponse) Reset() {
	*x = CreateTransactionByPhoneResponse{}
	mi := &file_inner_processing_grpc_transaction_proto_msgTypes[18]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreateTransactionByPhoneResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateTransactionByPhoneResponse) ProtoMessage() {}

func (x *CreateTransactionByPhoneResponse) ProtoReflect() protoreflect.Message {
	mi := &file_inner_processing_grpc_transaction_proto_msgTypes[18]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateTransactionByPhoneResponse.ProtoReflect.Descriptor instead.
func (*CreateTransactionByPhoneResponse) Descriptor() ([]byte, []int) {
	return file_inner_processing_grpc_transaction_proto_rawDescGZIP(), []int{18}
}

func (x *CreateTransactionByPhoneResponse) GetTransactionId() uint64 {
	if x != nil && x.TransactionId != nil {
		return *x.TransactionId
	}
	return 0
}

func (x *CreateTransactionByPhoneResponse) GetProjectId() uint64 {
	if x != nil && x.ProjectId != nil {
		return *x.ProjectId
	}
	return 0
}

func (x *CreateTransactionByPhoneResponse) GetMerchantId() uint64 {
	if x != nil && x.MerchantId != nil {
		return *x.MerchantId
	}
	return 0
}

func (x *CreateTransactionByPhoneResponse) GetAmount() float64 {
	if x != nil && x.Amount != nil {
		return *x.Amount
	}
	return 0
}

func (x *CreateTransactionByPhoneResponse) GetCreatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.CreatedAt
	}
	return nil
}

func (x *CreateTransactionByPhoneResponse) GetTransactionTypeId() uint64 {
	if x != nil && x.TransactionTypeId != nil {
		return *x.TransactionTypeId
	}
	return 0
}

type GetByIDWithTypeRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	TransactionId *uint64                `protobuf:"varint,1,opt,name=transaction_id,json=transactionId" json:"transaction_id,omitempty"`
	Code          *string                `protobuf:"bytes,2,opt,name=code" json:"code,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetByIDWithTypeRequest) Reset() {
	*x = GetByIDWithTypeRequest{}
	mi := &file_inner_processing_grpc_transaction_proto_msgTypes[19]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetByIDWithTypeRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetByIDWithTypeRequest) ProtoMessage() {}

func (x *GetByIDWithTypeRequest) ProtoReflect() protoreflect.Message {
	mi := &file_inner_processing_grpc_transaction_proto_msgTypes[19]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetByIDWithTypeRequest.ProtoReflect.Descriptor instead.
func (*GetByIDWithTypeRequest) Descriptor() ([]byte, []int) {
	return file_inner_processing_grpc_transaction_proto_rawDescGZIP(), []int{19}
}

func (x *GetByIDWithTypeRequest) GetTransactionId() uint64 {
	if x != nil && x.TransactionId != nil {
		return *x.TransactionId
	}
	return 0
}

func (x *GetByIDWithTypeRequest) GetCode() string {
	if x != nil && x.Code != nil {
		return *x.Code
	}
	return ""
}

type GetByIDWithTypeResponse struct {
	state              protoimpl.MessageState `protogen:"open.v1"`
	CreatedAt          *timestamppb.Timestamp `protobuf:"bytes,1,opt,name=created_at,json=createdAt" json:"created_at,omitempty"`
	UpdatedAt          *timestamppb.Timestamp `protobuf:"bytes,2,opt,name=updated_at,json=updatedAt" json:"updated_at,omitempty"`
	FinishedAt         *timestamppb.Timestamp `protobuf:"bytes,3,opt,name=finished_at,json=finishedAt" json:"finished_at,omitempty"`
	Id                 *uint64                `protobuf:"varint,4,opt,name=id" json:"id,omitempty"`
	AcquirerId         *uint64                `protobuf:"varint,5,opt,name=acquirer_id,json=acquirerId" json:"acquirer_id,omitempty"`
	TerminalId         *uint64                `protobuf:"varint,6,opt,name=terminal_id,json=terminalId" json:"terminal_id,omitempty"`
	BankReferenceId    *string                `protobuf:"bytes,7,opt,name=bank_reference_id,json=bankReferenceId" json:"bank_reference_id,omitempty"`
	CardId             *uint64                `protobuf:"varint,8,opt,name=card_id,json=cardId" json:"card_id,omitempty"`
	ProjectId          *uint64                `protobuf:"varint,9,opt,name=project_id,json=projectId" json:"project_id,omitempty"`
	MerchantId         *uint64                `protobuf:"varint,10,opt,name=merchant_id,json=merchantId" json:"merchant_id,omitempty"`
	ProjectReferenceId *string                `protobuf:"bytes,11,opt,name=project_reference_id,json=projectReferenceId" json:"project_reference_id,omitempty"`
	ProjectClientId    *string                `protobuf:"bytes,12,opt,name=project_client_id,json=projectClientId" json:"project_client_id,omitempty"`
	StatusId           *uint64                `protobuf:"varint,13,opt,name=status_id,json=statusId" json:"status_id,omitempty"`
	TypeId             *uint64                `protobuf:"varint,14,opt,name=type_id,json=typeId" json:"type_id,omitempty"`
	Amount             *float64               `protobuf:"fixed64,15,opt,name=amount" json:"amount,omitempty"`
	Try                *uint64                `protobuf:"varint,16,opt,name=try" json:"try,omitempty"`
	Description        *string                `protobuf:"bytes,17,opt,name=description" json:"description,omitempty"`
	TypeCode           *string                `protobuf:"bytes,18,opt,name=type_code,json=typeCode" json:"type_code,omitempty"`
	StatusCode         *string                `protobuf:"bytes,19,opt,name=status_code,json=statusCode" json:"status_code,omitempty"`
	IsFinalStatus      *bool                  `protobuf:"varint,20,opt,name=is_final_status,json=isFinalStatus" json:"is_final_status,omitempty"`
	TransactionInfo    *TransactionInfo       `protobuf:"bytes,21,opt,name=transaction_info,json=transactionInfo" json:"transaction_info,omitempty"`
	TransactionType    *TransactionType       `protobuf:"bytes,22,opt,name=transaction_type,json=transactionType" json:"transaction_type,omitempty"`
	TransactionStatus  *TransactionStatus     `protobuf:"bytes,23,opt,name=transaction_status,json=transactionStatus" json:"transaction_status,omitempty"`
	AggregatedTypeId   *uint64                `protobuf:"varint,24,opt,name=aggregated_type_id,json=aggregatedTypeId" json:"aggregated_type_id,omitempty"`
	VisaAliasToken     *string                `protobuf:"bytes,25,opt,name=visa_alias_token,json=visaAliasToken" json:"visa_alias_token,omitempty"`
	CInfo              *string                `protobuf:"bytes,26,opt,name=c_info,json=cInfo" json:"c_info,omitempty"`
	unknownFields      protoimpl.UnknownFields
	sizeCache          protoimpl.SizeCache
}

func (x *GetByIDWithTypeResponse) Reset() {
	*x = GetByIDWithTypeResponse{}
	mi := &file_inner_processing_grpc_transaction_proto_msgTypes[20]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetByIDWithTypeResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetByIDWithTypeResponse) ProtoMessage() {}

func (x *GetByIDWithTypeResponse) ProtoReflect() protoreflect.Message {
	mi := &file_inner_processing_grpc_transaction_proto_msgTypes[20]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetByIDWithTypeResponse.ProtoReflect.Descriptor instead.
func (*GetByIDWithTypeResponse) Descriptor() ([]byte, []int) {
	return file_inner_processing_grpc_transaction_proto_rawDescGZIP(), []int{20}
}

func (x *GetByIDWithTypeResponse) GetCreatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.CreatedAt
	}
	return nil
}

func (x *GetByIDWithTypeResponse) GetUpdatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.UpdatedAt
	}
	return nil
}

func (x *GetByIDWithTypeResponse) GetFinishedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.FinishedAt
	}
	return nil
}

func (x *GetByIDWithTypeResponse) GetId() uint64 {
	if x != nil && x.Id != nil {
		return *x.Id
	}
	return 0
}

func (x *GetByIDWithTypeResponse) GetAcquirerId() uint64 {
	if x != nil && x.AcquirerId != nil {
		return *x.AcquirerId
	}
	return 0
}

func (x *GetByIDWithTypeResponse) GetTerminalId() uint64 {
	if x != nil && x.TerminalId != nil {
		return *x.TerminalId
	}
	return 0
}

func (x *GetByIDWithTypeResponse) GetBankReferenceId() string {
	if x != nil && x.BankReferenceId != nil {
		return *x.BankReferenceId
	}
	return ""
}

func (x *GetByIDWithTypeResponse) GetCardId() uint64 {
	if x != nil && x.CardId != nil {
		return *x.CardId
	}
	return 0
}

func (x *GetByIDWithTypeResponse) GetProjectId() uint64 {
	if x != nil && x.ProjectId != nil {
		return *x.ProjectId
	}
	return 0
}

func (x *GetByIDWithTypeResponse) GetMerchantId() uint64 {
	if x != nil && x.MerchantId != nil {
		return *x.MerchantId
	}
	return 0
}

func (x *GetByIDWithTypeResponse) GetProjectReferenceId() string {
	if x != nil && x.ProjectReferenceId != nil {
		return *x.ProjectReferenceId
	}
	return ""
}

func (x *GetByIDWithTypeResponse) GetProjectClientId() string {
	if x != nil && x.ProjectClientId != nil {
		return *x.ProjectClientId
	}
	return ""
}

func (x *GetByIDWithTypeResponse) GetStatusId() uint64 {
	if x != nil && x.StatusId != nil {
		return *x.StatusId
	}
	return 0
}

func (x *GetByIDWithTypeResponse) GetTypeId() uint64 {
	if x != nil && x.TypeId != nil {
		return *x.TypeId
	}
	return 0
}

func (x *GetByIDWithTypeResponse) GetAmount() float64 {
	if x != nil && x.Amount != nil {
		return *x.Amount
	}
	return 0
}

func (x *GetByIDWithTypeResponse) GetTry() uint64 {
	if x != nil && x.Try != nil {
		return *x.Try
	}
	return 0
}

func (x *GetByIDWithTypeResponse) GetDescription() string {
	if x != nil && x.Description != nil {
		return *x.Description
	}
	return ""
}

func (x *GetByIDWithTypeResponse) GetTypeCode() string {
	if x != nil && x.TypeCode != nil {
		return *x.TypeCode
	}
	return ""
}

func (x *GetByIDWithTypeResponse) GetStatusCode() string {
	if x != nil && x.StatusCode != nil {
		return *x.StatusCode
	}
	return ""
}

func (x *GetByIDWithTypeResponse) GetIsFinalStatus() bool {
	if x != nil && x.IsFinalStatus != nil {
		return *x.IsFinalStatus
	}
	return false
}

func (x *GetByIDWithTypeResponse) GetTransactionInfo() *TransactionInfo {
	if x != nil {
		return x.TransactionInfo
	}
	return nil
}

func (x *GetByIDWithTypeResponse) GetTransactionType() *TransactionType {
	if x != nil {
		return x.TransactionType
	}
	return nil
}

func (x *GetByIDWithTypeResponse) GetTransactionStatus() *TransactionStatus {
	if x != nil {
		return x.TransactionStatus
	}
	return nil
}

func (x *GetByIDWithTypeResponse) GetAggregatedTypeId() uint64 {
	if x != nil && x.AggregatedTypeId != nil {
		return *x.AggregatedTypeId
	}
	return 0
}

func (x *GetByIDWithTypeResponse) GetVisaAliasToken() string {
	if x != nil && x.VisaAliasToken != nil {
		return *x.VisaAliasToken
	}
	return ""
}

func (x *GetByIDWithTypeResponse) GetCInfo() string {
	if x != nil && x.CInfo != nil {
		return *x.CInfo
	}
	return ""
}

type TransactionStatus struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	CreatedAt     *timestamppb.Timestamp `protobuf:"bytes,1,opt,name=created_at,json=createdAt" json:"created_at,omitempty"`
	UpdatedAt     *timestamppb.Timestamp `protobuf:"bytes,2,opt,name=updated_at,json=updatedAt" json:"updated_at,omitempty"`
	Id            *uint64                `protobuf:"varint,3,opt,name=id" json:"id,omitempty"`
	Code          *string                `protobuf:"bytes,4,opt,name=code" json:"code,omitempty"`
	Name          *string                `protobuf:"bytes,5,opt,name=name" json:"name,omitempty"`
	IsFinal       *bool                  `protobuf:"varint,6,opt,name=is_final,json=isFinal" json:"is_final,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *TransactionStatus) Reset() {
	*x = TransactionStatus{}
	mi := &file_inner_processing_grpc_transaction_proto_msgTypes[21]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *TransactionStatus) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TransactionStatus) ProtoMessage() {}

func (x *TransactionStatus) ProtoReflect() protoreflect.Message {
	mi := &file_inner_processing_grpc_transaction_proto_msgTypes[21]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TransactionStatus.ProtoReflect.Descriptor instead.
func (*TransactionStatus) Descriptor() ([]byte, []int) {
	return file_inner_processing_grpc_transaction_proto_rawDescGZIP(), []int{21}
}

func (x *TransactionStatus) GetCreatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.CreatedAt
	}
	return nil
}

func (x *TransactionStatus) GetUpdatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.UpdatedAt
	}
	return nil
}

func (x *TransactionStatus) GetId() uint64 {
	if x != nil && x.Id != nil {
		return *x.Id
	}
	return 0
}

func (x *TransactionStatus) GetCode() string {
	if x != nil && x.Code != nil {
		return *x.Code
	}
	return ""
}

func (x *TransactionStatus) GetName() string {
	if x != nil && x.Name != nil {
		return *x.Name
	}
	return ""
}

func (x *TransactionStatus) GetIsFinal() bool {
	if x != nil && x.IsFinal != nil {
		return *x.IsFinal
	}
	return false
}

type TransactionType struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	CreatedAt     *timestamppb.Timestamp `protobuf:"bytes,1,opt,name=created_at,json=createdAt" json:"created_at,omitempty"`
	UpdatedAt     *timestamppb.Timestamp `protobuf:"bytes,2,opt,name=updated_at,json=updatedAt" json:"updated_at,omitempty"`
	Id            *uint64                `protobuf:"varint,3,opt,name=id" json:"id,omitempty"`
	Code          *string                `protobuf:"bytes,4,opt,name=code" json:"code,omitempty"`
	Name          *string                `protobuf:"bytes,5,opt,name=name" json:"name,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *TransactionType) Reset() {
	*x = TransactionType{}
	mi := &file_inner_processing_grpc_transaction_proto_msgTypes[22]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *TransactionType) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TransactionType) ProtoMessage() {}

func (x *TransactionType) ProtoReflect() protoreflect.Message {
	mi := &file_inner_processing_grpc_transaction_proto_msgTypes[22]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TransactionType.ProtoReflect.Descriptor instead.
func (*TransactionType) Descriptor() ([]byte, []int) {
	return file_inner_processing_grpc_transaction_proto_rawDescGZIP(), []int{22}
}

func (x *TransactionType) GetCreatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.CreatedAt
	}
	return nil
}

func (x *TransactionType) GetUpdatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.UpdatedAt
	}
	return nil
}

func (x *TransactionType) GetId() uint64 {
	if x != nil && x.Id != nil {
		return *x.Id
	}
	return 0
}

func (x *TransactionType) GetCode() string {
	if x != nil && x.Code != nil {
		return *x.Code
	}
	return ""
}

func (x *TransactionType) GetName() string {
	if x != nil && x.Name != nil {
		return *x.Name
	}
	return ""
}

type UpdateStatusRequest struct {
	state             protoimpl.MessageState `protogen:"open.v1"`
	TransactionId     *uint64                `protobuf:"varint,1,opt,name=transaction_id,json=transactionId" json:"transaction_id,omitempty"`
	TransactionStatus *uint64                `protobuf:"varint,2,opt,name=transaction_status,json=transactionStatus" json:"transaction_status,omitempty"`
	FinishedAt        *timestamppb.Timestamp `protobuf:"bytes,3,opt,name=finished_at,json=finishedAt" json:"finished_at,omitempty"`
	unknownFields     protoimpl.UnknownFields
	sizeCache         protoimpl.SizeCache
}

func (x *UpdateStatusRequest) Reset() {
	*x = UpdateStatusRequest{}
	mi := &file_inner_processing_grpc_transaction_proto_msgTypes[23]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdateStatusRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateStatusRequest) ProtoMessage() {}

func (x *UpdateStatusRequest) ProtoReflect() protoreflect.Message {
	mi := &file_inner_processing_grpc_transaction_proto_msgTypes[23]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateStatusRequest.ProtoReflect.Descriptor instead.
func (*UpdateStatusRequest) Descriptor() ([]byte, []int) {
	return file_inner_processing_grpc_transaction_proto_rawDescGZIP(), []int{23}
}

func (x *UpdateStatusRequest) GetTransactionId() uint64 {
	if x != nil && x.TransactionId != nil {
		return *x.TransactionId
	}
	return 0
}

func (x *UpdateStatusRequest) GetTransactionStatus() uint64 {
	if x != nil && x.TransactionStatus != nil {
		return *x.TransactionStatus
	}
	return 0
}

func (x *UpdateStatusRequest) GetFinishedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.FinishedAt
	}
	return nil
}

type IncreaseTryCountRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	TransactionId *uint64                `protobuf:"varint,1,opt,name=transaction_id,json=transactionId" json:"transaction_id,omitempty"`
	Try           *uint64                `protobuf:"varint,2,opt,name=try" json:"try,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *IncreaseTryCountRequest) Reset() {
	*x = IncreaseTryCountRequest{}
	mi := &file_inner_processing_grpc_transaction_proto_msgTypes[24]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *IncreaseTryCountRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*IncreaseTryCountRequest) ProtoMessage() {}

func (x *IncreaseTryCountRequest) ProtoReflect() protoreflect.Message {
	mi := &file_inner_processing_grpc_transaction_proto_msgTypes[24]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use IncreaseTryCountRequest.ProtoReflect.Descriptor instead.
func (*IncreaseTryCountRequest) Descriptor() ([]byte, []int) {
	return file_inner_processing_grpc_transaction_proto_rawDescGZIP(), []int{24}
}

func (x *IncreaseTryCountRequest) GetTransactionId() uint64 {
	if x != nil && x.TransactionId != nil {
		return *x.TransactionId
	}
	return 0
}

func (x *IncreaseTryCountRequest) GetTry() uint64 {
	if x != nil && x.Try != nil {
		return *x.Try
	}
	return 0
}

type SaveAcquirerResponseRequest struct {
	state           protoimpl.MessageState   `protogen:"open.v1"`
	TransactionId   *uint64                  `protobuf:"varint,1,opt,name=transaction_id,json=transactionId" json:"transaction_id,omitempty"`
	BankResponse    *TransactionBankResponse `protobuf:"bytes,2,opt,name=bank_response,json=bankResponse" json:"bank_response,omitempty"`
	BankReferenceId *string                  `protobuf:"bytes,3,opt,name=bank_reference_id,json=bankReferenceId" json:"bank_reference_id,omitempty"`
	BankOrderId     *string                  `protobuf:"bytes,4,opt,name=bank_order_id,json=bankOrderId" json:"bank_order_id,omitempty"`
	unknownFields   protoimpl.UnknownFields
	sizeCache       protoimpl.SizeCache
}

func (x *SaveAcquirerResponseRequest) Reset() {
	*x = SaveAcquirerResponseRequest{}
	mi := &file_inner_processing_grpc_transaction_proto_msgTypes[25]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SaveAcquirerResponseRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SaveAcquirerResponseRequest) ProtoMessage() {}

func (x *SaveAcquirerResponseRequest) ProtoReflect() protoreflect.Message {
	mi := &file_inner_processing_grpc_transaction_proto_msgTypes[25]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SaveAcquirerResponseRequest.ProtoReflect.Descriptor instead.
func (*SaveAcquirerResponseRequest) Descriptor() ([]byte, []int) {
	return file_inner_processing_grpc_transaction_proto_rawDescGZIP(), []int{25}
}

func (x *SaveAcquirerResponseRequest) GetTransactionId() uint64 {
	if x != nil && x.TransactionId != nil {
		return *x.TransactionId
	}
	return 0
}

func (x *SaveAcquirerResponseRequest) GetBankResponse() *TransactionBankResponse {
	if x != nil {
		return x.BankResponse
	}
	return nil
}

func (x *SaveAcquirerResponseRequest) GetBankReferenceId() string {
	if x != nil && x.BankReferenceId != nil {
		return *x.BankReferenceId
	}
	return ""
}

func (x *SaveAcquirerResponseRequest) GetBankOrderId() string {
	if x != nil && x.BankOrderId != nil {
		return *x.BankOrderId
	}
	return ""
}

type TransactionBankResponse struct {
	state                   protoimpl.MessageState `protogen:"open.v1"`
	Code                    *string                `protobuf:"bytes,1,opt,name=code" json:"code,omitempty"`
	Message                 *string                `protobuf:"bytes,2,opt,name=message" json:"message,omitempty"`
	IntegrationErrorCode    *string                `protobuf:"bytes,3,opt,name=integrationErrorCode" json:"integrationErrorCode,omitempty"`
	IntegrationErrorMessage *string                `protobuf:"bytes,4,opt,name=integrationErrorMessage" json:"integrationErrorMessage,omitempty"`
	unknownFields           protoimpl.UnknownFields
	sizeCache               protoimpl.SizeCache
}

func (x *TransactionBankResponse) Reset() {
	*x = TransactionBankResponse{}
	mi := &file_inner_processing_grpc_transaction_proto_msgTypes[26]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *TransactionBankResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TransactionBankResponse) ProtoMessage() {}

func (x *TransactionBankResponse) ProtoReflect() protoreflect.Message {
	mi := &file_inner_processing_grpc_transaction_proto_msgTypes[26]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TransactionBankResponse.ProtoReflect.Descriptor instead.
func (*TransactionBankResponse) Descriptor() ([]byte, []int) {
	return file_inner_processing_grpc_transaction_proto_rawDescGZIP(), []int{26}
}

func (x *TransactionBankResponse) GetCode() string {
	if x != nil && x.Code != nil {
		return *x.Code
	}
	return ""
}

func (x *TransactionBankResponse) GetMessage() string {
	if x != nil && x.Message != nil {
		return *x.Message
	}
	return ""
}

func (x *TransactionBankResponse) GetIntegrationErrorCode() string {
	if x != nil && x.IntegrationErrorCode != nil {
		return *x.IntegrationErrorCode
	}
	return ""
}

func (x *TransactionBankResponse) GetIntegrationErrorMessage() string {
	if x != nil && x.IntegrationErrorMessage != nil {
		return *x.IntegrationErrorMessage
	}
	return ""
}

type SendRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	TransactionId *uint64                `protobuf:"varint,1,opt,name=transaction_id,json=transactionId" json:"transaction_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SendRequest) Reset() {
	*x = SendRequest{}
	mi := &file_inner_processing_grpc_transaction_proto_msgTypes[27]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SendRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SendRequest) ProtoMessage() {}

func (x *SendRequest) ProtoReflect() protoreflect.Message {
	mi := &file_inner_processing_grpc_transaction_proto_msgTypes[27]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SendRequest.ProtoReflect.Descriptor instead.
func (*SendRequest) Descriptor() ([]byte, []int) {
	return file_inner_processing_grpc_transaction_proto_rawDescGZIP(), []int{27}
}

func (x *SendRequest) GetTransactionId() uint64 {
	if x != nil && x.TransactionId != nil {
		return *x.TransactionId
	}
	return 0
}

type SetAdditionalDataRequest struct {
	state          protoimpl.MessageState `protogen:"open.v1"`
	AdditionalData map[string]string      `protobuf:"bytes,1,rep,name=additional_data,json=additionalData" json:"additional_data,omitempty" protobuf_key:"bytes,1,opt,name=key" protobuf_val:"bytes,2,opt,name=value"`
	TransactionId  *uint64                `protobuf:"varint,2,opt,name=transaction_id,json=transactionId" json:"transaction_id,omitempty"`
	CreatedAt      *timestamppb.Timestamp `protobuf:"bytes,3,opt,name=created_at,json=createdAt" json:"created_at,omitempty"`
	ProjectId      *uint64                `protobuf:"varint,4,opt,name=project_id,json=projectId" json:"project_id,omitempty"`
	MerchantId     *uint64                `protobuf:"varint,5,opt,name=merchant_id,json=merchantId" json:"merchant_id,omitempty"`
	unknownFields  protoimpl.UnknownFields
	sizeCache      protoimpl.SizeCache
}

func (x *SetAdditionalDataRequest) Reset() {
	*x = SetAdditionalDataRequest{}
	mi := &file_inner_processing_grpc_transaction_proto_msgTypes[28]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SetAdditionalDataRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SetAdditionalDataRequest) ProtoMessage() {}

func (x *SetAdditionalDataRequest) ProtoReflect() protoreflect.Message {
	mi := &file_inner_processing_grpc_transaction_proto_msgTypes[28]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SetAdditionalDataRequest.ProtoReflect.Descriptor instead.
func (*SetAdditionalDataRequest) Descriptor() ([]byte, []int) {
	return file_inner_processing_grpc_transaction_proto_rawDescGZIP(), []int{28}
}

func (x *SetAdditionalDataRequest) GetAdditionalData() map[string]string {
	if x != nil {
		return x.AdditionalData
	}
	return nil
}

func (x *SetAdditionalDataRequest) GetTransactionId() uint64 {
	if x != nil && x.TransactionId != nil {
		return *x.TransactionId
	}
	return 0
}

func (x *SetAdditionalDataRequest) GetCreatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.CreatedAt
	}
	return nil
}

func (x *SetAdditionalDataRequest) GetProjectId() uint64 {
	if x != nil && x.ProjectId != nil {
		return *x.ProjectId
	}
	return 0
}

func (x *SetAdditionalDataRequest) GetMerchantId() uint64 {
	if x != nil && x.MerchantId != nil {
		return *x.MerchantId
	}
	return 0
}

type CalculateAndUpdateTransactionAmountRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	TransactionId *uint64                `protobuf:"varint,1,opt,name=transaction_id,json=transactionId" json:"transaction_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CalculateAndUpdateTransactionAmountRequest) Reset() {
	*x = CalculateAndUpdateTransactionAmountRequest{}
	mi := &file_inner_processing_grpc_transaction_proto_msgTypes[29]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CalculateAndUpdateTransactionAmountRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CalculateAndUpdateTransactionAmountRequest) ProtoMessage() {}

func (x *CalculateAndUpdateTransactionAmountRequest) ProtoReflect() protoreflect.Message {
	mi := &file_inner_processing_grpc_transaction_proto_msgTypes[29]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CalculateAndUpdateTransactionAmountRequest.ProtoReflect.Descriptor instead.
func (*CalculateAndUpdateTransactionAmountRequest) Descriptor() ([]byte, []int) {
	return file_inner_processing_grpc_transaction_proto_rawDescGZIP(), []int{29}
}

func (x *CalculateAndUpdateTransactionAmountRequest) GetTransactionId() uint64 {
	if x != nil && x.TransactionId != nil {
		return *x.TransactionId
	}
	return 0
}

type BillPayoutRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	TransactionId *uint64                `protobuf:"varint,1,opt,name=transaction_id,json=transactionId" json:"transaction_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *BillPayoutRequest) Reset() {
	*x = BillPayoutRequest{}
	mi := &file_inner_processing_grpc_transaction_proto_msgTypes[30]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BillPayoutRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BillPayoutRequest) ProtoMessage() {}

func (x *BillPayoutRequest) ProtoReflect() protoreflect.Message {
	mi := &file_inner_processing_grpc_transaction_proto_msgTypes[30]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BillPayoutRequest.ProtoReflect.Descriptor instead.
func (*BillPayoutRequest) Descriptor() ([]byte, []int) {
	return file_inner_processing_grpc_transaction_proto_rawDescGZIP(), []int{30}
}

func (x *BillPayoutRequest) GetTransactionId() uint64 {
	if x != nil && x.TransactionId != nil {
		return *x.TransactionId
	}
	return 0
}

var File_inner_processing_grpc_transaction_proto protoreflect.FileDescriptor

var file_inner_processing_grpc_transaction_proto_rawDesc = string([]byte{
	0x0a, 0x27, 0x69, 0x6e, 0x6e, 0x65, 0x72, 0x2f, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69,
	0x6e, 0x67, 0x2f, 0x67, 0x72, 0x70, 0x63, 0x2f, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74,
	0x69, 0x6f, 0x6e, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x22, 0x70, 0x72, 0x6f, 0x63, 0x65,
	0x73, 0x73, 0x69, 0x6e, 0x67, 0x2e, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f,
	0x6e, 0x2e, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x1a, 0x1f, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x74,
	0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1c,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f,
	0x73, 0x74, 0x72, 0x75, 0x63, 0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1b, 0x67, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x65, 0x6d,
	0x70, 0x74, 0x79, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x48, 0x0a, 0x1f, 0x53, 0x65, 0x74,
	0x52, 0x65, 0x66, 0x75, 0x6e, 0x64, 0x57, 0x61, 0x69, 0x74, 0x69, 0x6e, 0x67, 0x53, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x56, 0x31, 0x12, 0x25, 0x0a, 0x0e,
	0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x04, 0x52, 0x0d, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f,
	0x6e, 0x49, 0x64, 0x22, 0x43, 0x0a, 0x20, 0x53, 0x65, 0x74, 0x52, 0x65, 0x66, 0x75, 0x6e, 0x64,
	0x57, 0x61, 0x69, 0x74, 0x69, 0x6e, 0x67, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x56, 0x31, 0x12, 0x1f, 0x0a, 0x0b, 0x73, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x73, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x43, 0x6f, 0x64, 0x65, 0x22, 0xc9, 0x01, 0x0a, 0x2c, 0x47, 0x65, 0x74,
	0x50, 0x61, 0x79, 0x49, 0x6e, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e,
	0x73, 0x42, 0x79, 0x50, 0x65, 0x72, 0x69, 0x6f, 0x64, 0x41, 0x6e, 0x64, 0x41, 0x63, 0x71, 0x75,
	0x69, 0x72, 0x65, 0x72, 0x52, 0x65, 0x71, 0x56, 0x31, 0x12, 0x1f, 0x0a, 0x0b, 0x61, 0x63, 0x71,
	0x75, 0x69, 0x72, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x0a,
	0x61, 0x63, 0x71, 0x75, 0x69, 0x72, 0x65, 0x72, 0x49, 0x64, 0x12, 0x3d, 0x0a, 0x0c, 0x73, 0x74,
	0x61, 0x72, 0x74, 0x5f, 0x70, 0x65, 0x72, 0x69, 0x6f, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62,
	0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x0b, 0x73, 0x74,
	0x61, 0x72, 0x74, 0x50, 0x65, 0x72, 0x69, 0x6f, 0x64, 0x12, 0x39, 0x0a, 0x0a, 0x65, 0x6e, 0x64,
	0x5f, 0x70, 0x65, 0x72, 0x69, 0x6f, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e,
	0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x09, 0x65, 0x6e, 0x64, 0x50, 0x65,
	0x72, 0x69, 0x6f, 0x64, 0x22, 0xb0, 0x01, 0x0a, 0x1c, 0x47, 0x65, 0x74, 0x54, 0x72, 0x61, 0x6e,
	0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x42, 0x79, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x52, 0x65, 0x71, 0x56, 0x31, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x3d, 0x0a,
	0x0c, 0x73, 0x74, 0x61, 0x72, 0x74, 0x5f, 0x70, 0x65, 0x72, 0x69, 0x6f, 0x64, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52,
	0x0b, 0x73, 0x74, 0x61, 0x72, 0x74, 0x50, 0x65, 0x72, 0x69, 0x6f, 0x64, 0x12, 0x39, 0x0a, 0x0a,
	0x65, 0x6e, 0x64, 0x5f, 0x70, 0x65, 0x72, 0x69, 0x6f, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62,
	0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x09, 0x65, 0x6e,
	0x64, 0x50, 0x65, 0x72, 0x69, 0x6f, 0x64, 0x22, 0xc9, 0x01, 0x0a, 0x24, 0x47, 0x65, 0x74, 0x54,
	0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x42, 0x79, 0x43, 0x61, 0x6c,
	0x6c, 0x62, 0x61, 0x63, 0x6b, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65, 0x71, 0x56, 0x31,
	0x12, 0x27, 0x0a, 0x0f, 0x63, 0x61, 0x6c, 0x6c, 0x62, 0x61, 0x63, 0x6b, 0x5f, 0x73, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0e, 0x63, 0x61, 0x6c, 0x6c, 0x62,
	0x61, 0x63, 0x6b, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x3d, 0x0a, 0x0c, 0x73, 0x74, 0x61,
	0x72, 0x74, 0x5f, 0x70, 0x65, 0x72, 0x69, 0x6f, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75,
	0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x0b, 0x73, 0x74, 0x61,
	0x72, 0x74, 0x50, 0x65, 0x72, 0x69, 0x6f, 0x64, 0x12, 0x39, 0x0a, 0x0a, 0x65, 0x6e, 0x64, 0x5f,
	0x70, 0x65, 0x72, 0x69, 0x6f, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54,
	0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x09, 0x65, 0x6e, 0x64, 0x50, 0x65, 0x72,
	0x69, 0x6f, 0x64, 0x22, 0xf6, 0x01, 0x0a, 0x33, 0x47, 0x65, 0x74, 0x54, 0x72, 0x61, 0x6e, 0x73,
	0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x42, 0x79, 0x46, 0x69, 0x6e, 0x61, 0x6c, 0x53, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x41, 0x6e, 0x64, 0x50, 0x65, 0x72, 0x69, 0x6f, 0x64, 0x57, 0x69, 0x74,
	0x68, 0x4c, 0x69, 0x6d, 0x69, 0x74, 0x52, 0x65, 0x71, 0x56, 0x31, 0x12, 0x19, 0x0a, 0x08, 0x69,
	0x73, 0x5f, 0x66, 0x69, 0x6e, 0x61, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x52, 0x07, 0x69,
	0x73, 0x46, 0x69, 0x6e, 0x61, 0x6c, 0x12, 0x2a, 0x0a, 0x10, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61,
	0x63, 0x74, 0x69, 0x6f, 0x6e, 0x4c, 0x69, 0x6d, 0x69, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0d,
	0x52, 0x10, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x4c, 0x69, 0x6d,
	0x69, 0x74, 0x12, 0x3d, 0x0a, 0x0c, 0x73, 0x74, 0x61, 0x72, 0x74, 0x5f, 0x70, 0x65, 0x72, 0x69,
	0x6f, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73,
	0x74, 0x61, 0x6d, 0x70, 0x52, 0x0b, 0x73, 0x74, 0x61, 0x72, 0x74, 0x50, 0x65, 0x72, 0x69, 0x6f,
	0x64, 0x12, 0x39, 0x0a, 0x0a, 0x65, 0x6e, 0x64, 0x5f, 0x70, 0x65, 0x72, 0x69, 0x6f, 0x64, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d,
	0x70, 0x52, 0x09, 0x65, 0x6e, 0x64, 0x50, 0x65, 0x72, 0x69, 0x6f, 0x64, 0x22, 0x2d, 0x0a, 0x1b,
	0x47, 0x65, 0x74, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x42, 0x79,
	0x49, 0x44, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x56, 0x31, 0x12, 0x0e, 0x0a, 0x02, 0x69,
	0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x02, 0x69, 0x64, 0x22, 0xf1, 0x06, 0x0a, 0x11,
	0x54, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x44, 0x61, 0x74, 0x61, 0x56,
	0x31, 0x12, 0x39, 0x0a, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d,
	0x70, 0x52, 0x09, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x12, 0x39, 0x0a, 0x0a,
	0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62,
	0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x09, 0x75, 0x70,
	0x64, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x12, 0x3b, 0x0a, 0x0b, 0x66, 0x69, 0x6e, 0x69, 0x73,
	0x68, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54,
	0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x0a, 0x66, 0x69, 0x6e, 0x69, 0x73, 0x68,
	0x65, 0x64, 0x41, 0x74, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x04,
	0x52, 0x02, 0x69, 0x64, 0x12, 0x1f, 0x0a, 0x0b, 0x61, 0x63, 0x71, 0x75, 0x69, 0x72, 0x65, 0x72,
	0x5f, 0x69, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x04, 0x52, 0x0a, 0x61, 0x63, 0x71, 0x75, 0x69,
	0x72, 0x65, 0x72, 0x49, 0x64, 0x12, 0x1f, 0x0a, 0x0b, 0x74, 0x65, 0x72, 0x6d, 0x69, 0x6e, 0x61,
	0x6c, 0x5f, 0x69, 0x64, 0x18, 0x06, 0x20, 0x01, 0x28, 0x04, 0x52, 0x0a, 0x74, 0x65, 0x72, 0x6d,
	0x69, 0x6e, 0x61, 0x6c, 0x49, 0x64, 0x12, 0x2a, 0x0a, 0x11, 0x62, 0x61, 0x6e, 0x6b, 0x5f, 0x72,
	0x65, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x07, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0f, 0x62, 0x61, 0x6e, 0x6b, 0x52, 0x65, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65,
	0x49, 0x64, 0x12, 0x17, 0x0a, 0x07, 0x63, 0x61, 0x72, 0x64, 0x5f, 0x69, 0x64, 0x18, 0x08, 0x20,
	0x01, 0x28, 0x04, 0x52, 0x06, 0x63, 0x61, 0x72, 0x64, 0x49, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x70,
	0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x09, 0x20, 0x01, 0x28, 0x04, 0x52,
	0x09, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x49, 0x64, 0x12, 0x1f, 0x0a, 0x0b, 0x6d, 0x65,
	0x72, 0x63, 0x68, 0x61, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x04, 0x52,
	0x0a, 0x6d, 0x65, 0x72, 0x63, 0x68, 0x61, 0x6e, 0x74, 0x49, 0x64, 0x12, 0x30, 0x0a, 0x14, 0x70,
	0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x5f, 0x72, 0x65, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65,
	0x5f, 0x69, 0x64, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x09, 0x52, 0x12, 0x70, 0x72, 0x6f, 0x6a, 0x65,
	0x63, 0x74, 0x52, 0x65, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x49, 0x64, 0x12, 0x2a, 0x0a,
	0x11, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x5f, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x5f,
	0x69, 0x64, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63,
	0x74, 0x43, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x49, 0x64, 0x12, 0x1b, 0x0a, 0x09, 0x73, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x5f, 0x69, 0x64, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x04, 0x52, 0x08, 0x73, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x49, 0x64, 0x12, 0x17, 0x0a, 0x07, 0x74, 0x79, 0x70, 0x65, 0x5f, 0x69,
	0x64, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x04, 0x52, 0x06, 0x74, 0x79, 0x70, 0x65, 0x49, 0x64, 0x12,
	0x16, 0x0a, 0x06, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x01, 0x52,
	0x06, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x10, 0x0a, 0x03, 0x74, 0x72, 0x79, 0x18, 0x10,
	0x20, 0x01, 0x28, 0x04, 0x52, 0x03, 0x74, 0x72, 0x79, 0x12, 0x20, 0x0a, 0x0b, 0x64, 0x65, 0x73,
	0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x11, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b,
	0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x1b, 0x0a, 0x09, 0x74,
	0x79, 0x70, 0x65, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x12, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08,
	0x74, 0x79, 0x70, 0x65, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x1f, 0x0a, 0x0b, 0x73, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x13, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x73,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x26, 0x0a, 0x0f, 0x69, 0x73, 0x5f,
	0x66, 0x69, 0x6e, 0x61, 0x6c, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x14, 0x20, 0x01,
	0x28, 0x08, 0x52, 0x0d, 0x69, 0x73, 0x46, 0x69, 0x6e, 0x61, 0x6c, 0x53, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x12, 0x5e, 0x0a, 0x10, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e,
	0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x15, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x33, 0x2e, 0x70, 0x72,
	0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x2e, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63,
	0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e,
	0x2e, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x6e, 0x66, 0x6f,
	0x52, 0x0f, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x6e, 0x66,
	0x6f, 0x12, 0x2c, 0x0a, 0x12, 0x61, 0x67, 0x67, 0x72, 0x65, 0x67, 0x61, 0x74, 0x65, 0x64, 0x5f,
	0x74, 0x79, 0x70, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x16, 0x20, 0x01, 0x28, 0x04, 0x52, 0x10, 0x61,
	0x67, 0x67, 0x72, 0x65, 0x67, 0x61, 0x74, 0x65, 0x64, 0x54, 0x79, 0x70, 0x65, 0x49, 0x64, 0x22,
	0x8b, 0x05, 0x0a, 0x0f, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x49,
	0x6e, 0x66, 0x6f, 0x12, 0x39, 0x0a, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61,
	0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74,
	0x61, 0x6d, 0x70, 0x52, 0x09, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x12, 0x39,
	0x0a, 0x0a, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x09,
	0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x12, 0x25, 0x0a, 0x0e, 0x74, 0x72, 0x61,
	0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x04, 0x52, 0x0d, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x64,
	0x12, 0x50, 0x0a, 0x16, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x5f,
	0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62,
	0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x14, 0x74, 0x72,
	0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64,
	0x41, 0x74, 0x12, 0x21, 0x0a, 0x0c, 0x63, 0x61, 0x6c, 0x6c, 0x62, 0x61, 0x63, 0x6b, 0x5f, 0x75,
	0x72, 0x6c, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x63, 0x61, 0x6c, 0x6c, 0x62, 0x61,
	0x63, 0x6b, 0x55, 0x72, 0x6c, 0x12, 0x1f, 0x0a, 0x0b, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x72, 0x6d,
	0x5f, 0x75, 0x72, 0x6c, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x63, 0x6f, 0x6e, 0x66,
	0x69, 0x72, 0x6d, 0x55, 0x72, 0x6c, 0x12, 0x30, 0x0a, 0x14, 0x73, 0x75, 0x63, 0x63, 0x65, 0x73,
	0x73, 0x5f, 0x72, 0x65, 0x64, 0x69, 0x72, 0x65, 0x63, 0x74, 0x5f, 0x75, 0x72, 0x6c, 0x18, 0x07,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x12, 0x73, 0x75, 0x63, 0x63, 0x65, 0x73, 0x73, 0x52, 0x65, 0x64,
	0x69, 0x72, 0x65, 0x63, 0x74, 0x55, 0x72, 0x6c, 0x12, 0x30, 0x0a, 0x14, 0x66, 0x61, 0x69, 0x6c,
	0x75, 0x72, 0x65, 0x5f, 0x72, 0x65, 0x64, 0x69, 0x72, 0x65, 0x63, 0x74, 0x5f, 0x75, 0x72, 0x6c,
	0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x12, 0x66, 0x61, 0x69, 0x6c, 0x75, 0x72, 0x65, 0x52,
	0x65, 0x64, 0x69, 0x72, 0x65, 0x63, 0x74, 0x55, 0x72, 0x6c, 0x12, 0x27, 0x0a, 0x0f, 0x63, 0x61,
	0x6c, 0x6c, 0x62, 0x61, 0x63, 0x6b, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x09, 0x20,
	0x01, 0x28, 0x0d, 0x52, 0x0e, 0x63, 0x61, 0x6c, 0x6c, 0x62, 0x61, 0x63, 0x6b, 0x53, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x12, 0x1d, 0x0a, 0x0a, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x70, 0x68, 0x6f, 0x6e,
	0x65, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x75, 0x73, 0x65, 0x72, 0x50, 0x68, 0x6f,
	0x6e, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x65, 0x6d, 0x61, 0x69, 0x6c,
	0x18, 0x0b, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x75, 0x73, 0x65, 0x72, 0x45, 0x6d, 0x61, 0x69,
	0x6c, 0x12, 0x1a, 0x0a, 0x08, 0x73, 0x68, 0x69, 0x70, 0x6d, 0x65, 0x6e, 0x74, 0x18, 0x0c, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x08, 0x73, 0x68, 0x69, 0x70, 0x6d, 0x65, 0x6e, 0x74, 0x12, 0x22, 0x0a,
	0x0d, 0x62, 0x61, 0x6e, 0x6b, 0x5f, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x0d,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x62, 0x61, 0x6e, 0x6b, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x49,
	0x64, 0x12, 0x3a, 0x0a, 0x0c, 0x6a, 0x6f, 0x62, 0x73, 0x5f, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67,
	0x65, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x17, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x53, 0x74, 0x72, 0x75, 0x63, 0x74,
	0x52, 0x0b, 0x6a, 0x6f, 0x62, 0x73, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x22, 0x74, 0x0a,
	0x17, 0x47, 0x65, 0x74, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x73,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x59, 0x0a, 0x0c, 0x74, 0x72, 0x61, 0x6e,
	0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x35,
	0x2e, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x2e, 0x74, 0x72, 0x61, 0x6e,
	0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74,
	0x69, 0x6f, 0x6e, 0x2e, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x44,
	0x61, 0x74, 0x61, 0x56, 0x31, 0x52, 0x0c, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69,
	0x6f, 0x6e, 0x73, 0x22, 0x7d, 0x0a, 0x20, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x54, 0x72, 0x61,
	0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x56, 0x31, 0x12, 0x25, 0x0a, 0x0e, 0x74, 0x72, 0x61, 0x6e, 0x73,
	0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52,
	0x0d, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x12, 0x32,
	0x0a, 0x15, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x73, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x04, 0x52, 0x13, 0x74,
	0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x49, 0x64, 0x22, 0x6f, 0x0a, 0x1d, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x43, 0x61, 0x6c, 0x6c,
	0x62, 0x61, 0x63, 0x6b, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x56, 0x31, 0x12, 0x25, 0x0a, 0x0e, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69,
	0x6f, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x0d, 0x74, 0x72, 0x61,
	0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x12, 0x27, 0x0a, 0x0f, 0x63, 0x61,
	0x6c, 0x6c, 0x62, 0x61, 0x63, 0x6b, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x0d, 0x52, 0x0e, 0x63, 0x61, 0x6c, 0x6c, 0x62, 0x61, 0x63, 0x6b, 0x53, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x22, 0x3a, 0x0a, 0x1f, 0x47, 0x65, 0x74, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x61,
	0x63, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x42, 0x79, 0x49, 0x44, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x56, 0x31, 0x12, 0x17, 0x0a, 0x07, 0x74, 0x79, 0x70, 0x65, 0x5f, 0x69,
	0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x06, 0x74, 0x79, 0x70, 0x65, 0x49, 0x64, 0x22,
	0x5a, 0x0a, 0x20, 0x47, 0x65, 0x74, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f,
	0x6e, 0x54, 0x79, 0x70, 0x65, 0x42, 0x79, 0x49, 0x44, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x56, 0x31, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52,
	0x02, 0x69, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x22, 0x40, 0x0a, 0x17, 0x4d,
	0x61, 0x6b, 0x65, 0x41, 0x75, 0x74, 0x6f, 0x43, 0x68, 0x61, 0x72, 0x67, 0x65, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x56, 0x31, 0x12, 0x25, 0x0a, 0x0e, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61,
	0x63, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x0d,
	0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x22, 0xe1, 0x01,
	0x0a, 0x25, 0x47, 0x65, 0x74, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e,
	0x73, 0x42, 0x79, 0x50, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x56, 0x31, 0x12, 0x1d, 0x0a, 0x0a, 0x70, 0x72, 0x6f, 0x6a, 0x65,
	0x63, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x09, 0x70, 0x72, 0x6f,
	0x6a, 0x65, 0x63, 0x74, 0x49, 0x64, 0x12, 0x1f, 0x0a, 0x0b, 0x6d, 0x65, 0x72, 0x63, 0x68, 0x61,
	0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x04, 0x52, 0x0a, 0x6d, 0x65, 0x72,
	0x63, 0x68, 0x61, 0x6e, 0x74, 0x49, 0x64, 0x12, 0x3d, 0x0a, 0x0c, 0x73, 0x74, 0x61, 0x72, 0x74,
	0x5f, 0x70, 0x65, 0x72, 0x69, 0x6f, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e,
	0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x0b, 0x73, 0x74, 0x61, 0x72, 0x74,
	0x50, 0x65, 0x72, 0x69, 0x6f, 0x64, 0x12, 0x39, 0x0a, 0x0a, 0x65, 0x6e, 0x64, 0x5f, 0x70, 0x65,
	0x72, 0x69, 0x6f, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d,
	0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x09, 0x65, 0x6e, 0x64, 0x50, 0x65, 0x72, 0x69, 0x6f,
	0x64, 0x22, 0x5a, 0x0a, 0x1d, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x61,
	0x63, 0x74, 0x69, 0x6f, 0x6e, 0x48, 0x61, 0x73, 0x68, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x56, 0x31, 0x12, 0x25, 0x0a, 0x0e, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f,
	0x6e, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x0d, 0x74, 0x72, 0x61, 0x6e,
	0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x68, 0x61, 0x73,
	0x68, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x68, 0x61, 0x73, 0x68, 0x22, 0xad, 0x05,
	0x0a, 0x1f, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74,
	0x69, 0x6f, 0x6e, 0x42, 0x79, 0x50, 0x68, 0x6f, 0x6e, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x12, 0x16, 0x0a, 0x06, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x01, 0x52, 0x06, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x21, 0x0a, 0x0c, 0x63, 0x61, 0x6c,
	0x6c, 0x62, 0x61, 0x63, 0x6b, 0x5f, 0x75, 0x72, 0x6c, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0b, 0x63, 0x61, 0x6c, 0x6c, 0x62, 0x61, 0x63, 0x6b, 0x55, 0x72, 0x6c, 0x12, 0x20, 0x0a, 0x0b,
	0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x28,
	0x0a, 0x10, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x5f, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x5f,
	0x69, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63,
	0x74, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x49, 0x64, 0x12, 0x1f, 0x0a, 0x0b, 0x6d, 0x65, 0x72, 0x63,
	0x68, 0x61, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x04, 0x52, 0x0a, 0x6d,
	0x65, 0x72, 0x63, 0x68, 0x61, 0x6e, 0x74, 0x49, 0x64, 0x12, 0x2a, 0x0a, 0x11, 0x70, 0x72, 0x6f,
	0x6a, 0x65, 0x63, 0x74, 0x5f, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x06,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0f, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x43, 0x6c, 0x69,
	0x65, 0x6e, 0x74, 0x49, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74,
	0x5f, 0x69, 0x64, 0x18, 0x07, 0x20, 0x01, 0x28, 0x04, 0x52, 0x09, 0x70, 0x72, 0x6f, 0x6a, 0x65,
	0x63, 0x74, 0x49, 0x64, 0x12, 0x30, 0x0a, 0x14, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x5f,
	0x72, 0x65, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x08, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x12, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x52, 0x65, 0x66, 0x65, 0x72,
	0x65, 0x6e, 0x63, 0x65, 0x49, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x70,
	0x68, 0x6f, 0x6e, 0x65, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x75, 0x73, 0x65, 0x72,
	0x50, 0x68, 0x6f, 0x6e, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x18, 0x0a,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x12, 0x80, 0x01, 0x0a, 0x0f,
	0x61, 0x64, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x5f, 0x64, 0x61, 0x74, 0x61, 0x18,
	0x0b, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x57, 0x2e, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69,
	0x6e, 0x67, 0x2e, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x74,
	0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74,
	0x65, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x42, 0x79, 0x50, 0x68,
	0x6f, 0x6e, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x41, 0x64, 0x64, 0x69, 0x74,
	0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x44, 0x61, 0x74, 0x61, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x0e,
	0x61, 0x64, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x44, 0x61, 0x74, 0x61, 0x12, 0x28,
	0x0a, 0x10, 0x76, 0x69, 0x73, 0x61, 0x5f, 0x61, 0x6c, 0x69, 0x61, 0x73, 0x5f, 0x74, 0x6f, 0x6b,
	0x65, 0x6e, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x76, 0x69, 0x73, 0x61, 0x41, 0x6c,
	0x69, 0x61, 0x73, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x12, 0x1f, 0x0a, 0x0b, 0x74, 0x65, 0x72, 0x6d,
	0x69, 0x6e, 0x61, 0x6c, 0x5f, 0x69, 0x64, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x04, 0x52, 0x0a, 0x74,
	0x65, 0x72, 0x6d, 0x69, 0x6e, 0x61, 0x6c, 0x49, 0x64, 0x12, 0x1f, 0x0a, 0x0b, 0x61, 0x63, 0x71,
	0x75, 0x69, 0x72, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x04, 0x52, 0x0a,
	0x61, 0x63, 0x71, 0x75, 0x69, 0x72, 0x65, 0x72, 0x49, 0x64, 0x1a, 0x41, 0x0a, 0x13, 0x41, 0x64,
	0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x44, 0x61, 0x74, 0x61, 0x45, 0x6e, 0x74, 0x72,
	0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03,
	0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0x8c, 0x02,
	0x0a, 0x20, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74,
	0x69, 0x6f, 0x6e, 0x42, 0x79, 0x50, 0x68, 0x6f, 0x6e, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x12, 0x25, 0x0a, 0x0e, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f,
	0x6e, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x0d, 0x74, 0x72, 0x61, 0x6e,
	0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x70, 0x72, 0x6f,
	0x6a, 0x65, 0x63, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x04, 0x52, 0x09, 0x70,
	0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x49, 0x64, 0x12, 0x1f, 0x0a, 0x0b, 0x6d, 0x65, 0x72, 0x63,
	0x68, 0x61, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x04, 0x52, 0x0a, 0x6d,
	0x65, 0x72, 0x63, 0x68, 0x61, 0x6e, 0x74, 0x49, 0x64, 0x12, 0x16, 0x0a, 0x06, 0x61, 0x6d, 0x6f,
	0x75, 0x6e, 0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x01, 0x52, 0x06, 0x61, 0x6d, 0x6f, 0x75, 0x6e,
	0x74, 0x12, 0x39, 0x0a, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18,
	0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d,
	0x70, 0x52, 0x09, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x12, 0x2e, 0x0a, 0x13,
	0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x74, 0x79, 0x70, 0x65,
	0x5f, 0x69, 0x64, 0x18, 0x06, 0x20, 0x01, 0x28, 0x04, 0x52, 0x11, 0x74, 0x72, 0x61, 0x6e, 0x73,
	0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x49, 0x64, 0x22, 0x53, 0x0a, 0x16,
	0x47, 0x65, 0x74, 0x42, 0x79, 0x49, 0x44, 0x57, 0x69, 0x74, 0x68, 0x54, 0x79, 0x70, 0x65, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x25, 0x0a, 0x0e, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61,
	0x63, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x0d,
	0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x12, 0x12, 0x0a,
	0x04, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x63, 0x6f, 0x64,
	0x65, 0x22, 0xfe, 0x08, 0x0a, 0x17, 0x47, 0x65, 0x74, 0x42, 0x79, 0x49, 0x44, 0x57, 0x69, 0x74,
	0x68, 0x54, 0x79, 0x70, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x39, 0x0a,
	0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x09, 0x63,
	0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x12, 0x39, 0x0a, 0x0a, 0x75, 0x70, 0x64, 0x61,
	0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54,
	0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x09, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65,
	0x64, 0x41, 0x74, 0x12, 0x3b, 0x0a, 0x0b, 0x66, 0x69, 0x6e, 0x69, 0x73, 0x68, 0x65, 0x64, 0x5f,
	0x61, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73,
	0x74, 0x61, 0x6d, 0x70, 0x52, 0x0a, 0x66, 0x69, 0x6e, 0x69, 0x73, 0x68, 0x65, 0x64, 0x41, 0x74,
	0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x04, 0x52, 0x02, 0x69, 0x64,
	0x12, 0x1f, 0x0a, 0x0b, 0x61, 0x63, 0x71, 0x75, 0x69, 0x72, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18,
	0x05, 0x20, 0x01, 0x28, 0x04, 0x52, 0x0a, 0x61, 0x63, 0x71, 0x75, 0x69, 0x72, 0x65, 0x72, 0x49,
	0x64, 0x12, 0x1f, 0x0a, 0x0b, 0x74, 0x65, 0x72, 0x6d, 0x69, 0x6e, 0x61, 0x6c, 0x5f, 0x69, 0x64,
	0x18, 0x06, 0x20, 0x01, 0x28, 0x04, 0x52, 0x0a, 0x74, 0x65, 0x72, 0x6d, 0x69, 0x6e, 0x61, 0x6c,
	0x49, 0x64, 0x12, 0x2a, 0x0a, 0x11, 0x62, 0x61, 0x6e, 0x6b, 0x5f, 0x72, 0x65, 0x66, 0x65, 0x72,
	0x65, 0x6e, 0x63, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f, 0x62,
	0x61, 0x6e, 0x6b, 0x52, 0x65, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x49, 0x64, 0x12, 0x17,
	0x0a, 0x07, 0x63, 0x61, 0x72, 0x64, 0x5f, 0x69, 0x64, 0x18, 0x08, 0x20, 0x01, 0x28, 0x04, 0x52,
	0x06, 0x63, 0x61, 0x72, 0x64, 0x49, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x70, 0x72, 0x6f, 0x6a, 0x65,
	0x63, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x09, 0x20, 0x01, 0x28, 0x04, 0x52, 0x09, 0x70, 0x72, 0x6f,
	0x6a, 0x65, 0x63, 0x74, 0x49, 0x64, 0x12, 0x1f, 0x0a, 0x0b, 0x6d, 0x65, 0x72, 0x63, 0x68, 0x61,
	0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x04, 0x52, 0x0a, 0x6d, 0x65, 0x72,
	0x63, 0x68, 0x61, 0x6e, 0x74, 0x49, 0x64, 0x12, 0x30, 0x0a, 0x14, 0x70, 0x72, 0x6f, 0x6a, 0x65,
	0x63, 0x74, 0x5f, 0x72, 0x65, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x5f, 0x69, 0x64, 0x18,
	0x0b, 0x20, 0x01, 0x28, 0x09, 0x52, 0x12, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x52, 0x65,
	0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x49, 0x64, 0x12, 0x2a, 0x0a, 0x11, 0x70, 0x72, 0x6f,
	0x6a, 0x65, 0x63, 0x74, 0x5f, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x0c,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0f, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x43, 0x6c, 0x69,
	0x65, 0x6e, 0x74, 0x49, 0x64, 0x12, 0x1b, 0x0a, 0x09, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x5f,
	0x69, 0x64, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x04, 0x52, 0x08, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x49, 0x64, 0x12, 0x17, 0x0a, 0x07, 0x74, 0x79, 0x70, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x0e, 0x20,
	0x01, 0x28, 0x04, 0x52, 0x06, 0x74, 0x79, 0x70, 0x65, 0x49, 0x64, 0x12, 0x16, 0x0a, 0x06, 0x61,
	0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x01, 0x52, 0x06, 0x61, 0x6d, 0x6f,
	0x75, 0x6e, 0x74, 0x12, 0x10, 0x0a, 0x03, 0x74, 0x72, 0x79, 0x18, 0x10, 0x20, 0x01, 0x28, 0x04,
	0x52, 0x03, 0x74, 0x72, 0x79, 0x12, 0x20, 0x0a, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70,
	0x74, 0x69, 0x6f, 0x6e, 0x18, 0x11, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x64, 0x65, 0x73, 0x63,
	0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x1b, 0x0a, 0x09, 0x74, 0x79, 0x70, 0x65, 0x5f,
	0x63, 0x6f, 0x64, 0x65, 0x18, 0x12, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x74, 0x79, 0x70, 0x65,
	0x43, 0x6f, 0x64, 0x65, 0x12, 0x1f, 0x0a, 0x0b, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x5f, 0x63,
	0x6f, 0x64, 0x65, 0x18, 0x13, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x73, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x26, 0x0a, 0x0f, 0x69, 0x73, 0x5f, 0x66, 0x69, 0x6e, 0x61,
	0x6c, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x14, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0d,
	0x69, 0x73, 0x46, 0x69, 0x6e, 0x61, 0x6c, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x5e, 0x0a,
	0x10, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x69, 0x6e, 0x66,
	0x6f, 0x18, 0x15, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x33, 0x2e, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73,
	0x73, 0x69, 0x6e, 0x67, 0x2e, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e,
	0x2e, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x54, 0x72, 0x61,
	0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x0f, 0x74, 0x72,
	0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x5e, 0x0a,
	0x10, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x74, 0x79, 0x70,
	0x65, 0x18, 0x16, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x33, 0x2e, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73,
	0x73, 0x69, 0x6e, 0x67, 0x2e, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e,
	0x2e, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x54, 0x72, 0x61,
	0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0f, 0x74, 0x72,
	0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x12, 0x64, 0x0a,
	0x12, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x73, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x18, 0x17, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x35, 0x2e, 0x70, 0x72, 0x6f, 0x63,
	0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x2e, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69,
	0x6f, 0x6e, 0x2e, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x54,
	0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x52, 0x11, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x12, 0x2c, 0x0a, 0x12, 0x61, 0x67, 0x67, 0x72, 0x65, 0x67, 0x61, 0x74, 0x65,
	0x64, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x18, 0x20, 0x01, 0x28, 0x04, 0x52,
	0x10, 0x61, 0x67, 0x67, 0x72, 0x65, 0x67, 0x61, 0x74, 0x65, 0x64, 0x54, 0x79, 0x70, 0x65, 0x49,
	0x64, 0x12, 0x28, 0x0a, 0x10, 0x76, 0x69, 0x73, 0x61, 0x5f, 0x61, 0x6c, 0x69, 0x61, 0x73, 0x5f,
	0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x18, 0x19, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x76, 0x69, 0x73,
	0x61, 0x41, 0x6c, 0x69, 0x61, 0x73, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x12, 0x15, 0x0a, 0x06, 0x63,
	0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x1a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x63, 0x49, 0x6e,
	0x66, 0x6f, 0x22, 0xdc, 0x01, 0x0a, 0x11, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69,
	0x6f, 0x6e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x39, 0x0a, 0x0a, 0x63, 0x72, 0x65, 0x61,
	0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54,
	0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x09, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65,
	0x64, 0x41, 0x74, 0x12, 0x39, 0x0a, 0x0a, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61,
	0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74,
	0x61, 0x6d, 0x70, 0x52, 0x09, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x12, 0x0e,
	0x0a, 0x02, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x04, 0x52, 0x02, 0x69, 0x64, 0x12, 0x12,
	0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x63, 0x6f,
	0x64, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x19, 0x0a, 0x08, 0x69, 0x73, 0x5f, 0x66, 0x69, 0x6e,
	0x61, 0x6c, 0x18, 0x06, 0x20, 0x01, 0x28, 0x08, 0x52, 0x07, 0x69, 0x73, 0x46, 0x69, 0x6e, 0x61,
	0x6c, 0x22, 0xbf, 0x01, 0x0a, 0x0f, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f,
	0x6e, 0x54, 0x79, 0x70, 0x65, 0x12, 0x39, 0x0a, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64,
	0x5f, 0x61, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65,
	0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x09, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74,
	0x12, 0x39, 0x0a, 0x0a, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70,
	0x52, 0x09, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x12, 0x0e, 0x0a, 0x02, 0x69,
	0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x04, 0x52, 0x02, 0x69, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x63,
	0x6f, 0x64, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x12,
	0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e,
	0x61, 0x6d, 0x65, 0x22, 0xa8, 0x01, 0x0a, 0x13, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x53, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x25, 0x0a, 0x0e, 0x74,
	0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x04, 0x52, 0x0d, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e,
	0x49, 0x64, 0x12, 0x2d, 0x0a, 0x12, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f,
	0x6e, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x04, 0x52, 0x11,
	0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x12, 0x3b, 0x0a, 0x0b, 0x66, 0x69, 0x6e, 0x69, 0x73, 0x68, 0x65, 0x64, 0x5f, 0x61, 0x74,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61,
	0x6d, 0x70, 0x52, 0x0a, 0x66, 0x69, 0x6e, 0x69, 0x73, 0x68, 0x65, 0x64, 0x41, 0x74, 0x22, 0x52,
	0x0a, 0x17, 0x49, 0x6e, 0x63, 0x72, 0x65, 0x61, 0x73, 0x65, 0x54, 0x72, 0x79, 0x43, 0x6f, 0x75,
	0x6e, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x25, 0x0a, 0x0e, 0x74, 0x72, 0x61,
	0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x04, 0x52, 0x0d, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x64,
	0x12, 0x10, 0x0a, 0x03, 0x74, 0x72, 0x79, 0x18, 0x02, 0x20, 0x01, 0x28, 0x04, 0x52, 0x03, 0x74,
	0x72, 0x79, 0x22, 0xf6, 0x01, 0x0a, 0x1b, 0x53, 0x61, 0x76, 0x65, 0x41, 0x63, 0x71, 0x75, 0x69,
	0x72, 0x65, 0x72, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x12, 0x25, 0x0a, 0x0e, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f,
	0x6e, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x0d, 0x74, 0x72, 0x61, 0x6e,
	0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x12, 0x60, 0x0a, 0x0d, 0x62, 0x61, 0x6e,
	0x6b, 0x5f, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x3b, 0x2e, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x2e, 0x74, 0x72,
	0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61,
	0x63, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f,
	0x6e, 0x42, 0x61, 0x6e, 0x6b, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x52, 0x0c, 0x62,
	0x61, 0x6e, 0x6b, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x2a, 0x0a, 0x11, 0x62,
	0x61, 0x6e, 0x6b, 0x5f, 0x72, 0x65, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x5f, 0x69, 0x64,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f, 0x62, 0x61, 0x6e, 0x6b, 0x52, 0x65, 0x66, 0x65,
	0x72, 0x65, 0x6e, 0x63, 0x65, 0x49, 0x64, 0x12, 0x22, 0x0a, 0x0d, 0x62, 0x61, 0x6e, 0x6b, 0x5f,
	0x6f, 0x72, 0x64, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b,
	0x62, 0x61, 0x6e, 0x6b, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x49, 0x64, 0x22, 0xb5, 0x01, 0x0a, 0x17,
	0x54, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x42, 0x61, 0x6e, 0x6b, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x6d,
	0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6d, 0x65,
	0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x32, 0x0a, 0x14, 0x69, 0x6e, 0x74, 0x65, 0x67, 0x72, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x43, 0x6f, 0x64, 0x65, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x14, 0x69, 0x6e, 0x74, 0x65, 0x67, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x45, 0x72, 0x72, 0x6f, 0x72, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x38, 0x0a, 0x17, 0x69, 0x6e, 0x74,
	0x65, 0x67, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x4d, 0x65, 0x73,
	0x73, 0x61, 0x67, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x17, 0x69, 0x6e, 0x74, 0x65,
	0x67, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x4d, 0x65, 0x73, 0x73,
	0x61, 0x67, 0x65, 0x22, 0x34, 0x0a, 0x0b, 0x53, 0x65, 0x6e, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x12, 0x25, 0x0a, 0x0e, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f,
	0x6e, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x0d, 0x74, 0x72, 0x61, 0x6e,
	0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x22, 0xfa, 0x02, 0x0a, 0x18, 0x53, 0x65,
	0x74, 0x41, 0x64, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x44, 0x61, 0x74, 0x61, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x79, 0x0a, 0x0f, 0x61, 0x64, 0x64, 0x69, 0x74, 0x69,
	0x6f, 0x6e, 0x61, 0x6c, 0x5f, 0x64, 0x61, 0x74, 0x61, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x50, 0x2e, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x2e, 0x74, 0x72, 0x61,
	0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63,
	0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x53, 0x65, 0x74, 0x41, 0x64, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e,
	0x61, 0x6c, 0x44, 0x61, 0x74, 0x61, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x41, 0x64,
	0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x44, 0x61, 0x74, 0x61, 0x45, 0x6e, 0x74, 0x72,
	0x79, 0x52, 0x0e, 0x61, 0x64, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x44, 0x61, 0x74,
	0x61, 0x12, 0x25, 0x0a, 0x0e, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e,
	0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x04, 0x52, 0x0d, 0x74, 0x72, 0x61, 0x6e, 0x73,
	0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x12, 0x39, 0x0a, 0x0a, 0x63, 0x72, 0x65, 0x61,
	0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54,
	0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x09, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65,
	0x64, 0x41, 0x74, 0x12, 0x1d, 0x0a, 0x0a, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x5f, 0x69,
	0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x04, 0x52, 0x09, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74,
	0x49, 0x64, 0x12, 0x1f, 0x0a, 0x0b, 0x6d, 0x65, 0x72, 0x63, 0x68, 0x61, 0x6e, 0x74, 0x5f, 0x69,
	0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x04, 0x52, 0x0a, 0x6d, 0x65, 0x72, 0x63, 0x68, 0x61, 0x6e,
	0x74, 0x49, 0x64, 0x1a, 0x41, 0x0a, 0x13, 0x41, 0x64, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x61,
	0x6c, 0x44, 0x61, 0x74, 0x61, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65,
	0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05,
	0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x76, 0x61, 0x6c,
	0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0x53, 0x0a, 0x2a, 0x43, 0x61, 0x6c, 0x63, 0x75, 0x6c,
	0x61, 0x74, 0x65, 0x41, 0x6e, 0x64, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x54, 0x72, 0x61, 0x6e,
	0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x12, 0x25, 0x0a, 0x0e, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74,
	0x69, 0x6f, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x0d, 0x74, 0x72,
	0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x22, 0x3a, 0x0a, 0x11, 0x42,
	0x69, 0x6c, 0x6c, 0x50, 0x61, 0x79, 0x6f, 0x75, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x12, 0x25, 0x0a, 0x0e, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x5f,
	0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x0d, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61,
	0x63, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x32, 0xfd, 0x16, 0x0a, 0x0b, 0x54, 0x72, 0x61, 0x6e,
	0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x79, 0x0a, 0x17, 0x55, 0x70, 0x64, 0x61, 0x74,
	0x65, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x12, 0x44, 0x2e, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x2e,
	0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x74, 0x72, 0x61, 0x6e,
	0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x54, 0x72,
	0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x56, 0x31, 0x1a, 0x16, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79,
	0x22, 0x00, 0x12, 0x73, 0x0a, 0x14, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x43, 0x61, 0x6c, 0x6c,
	0x62, 0x61, 0x63, 0x6b, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x41, 0x2e, 0x70, 0x72, 0x6f,
	0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x2e, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74,
	0x69, 0x6f, 0x6e, 0x2e, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x2e,
	0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x43, 0x61, 0x6c, 0x6c, 0x62, 0x61, 0x63, 0x6b, 0x53, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x56, 0x31, 0x1a, 0x16, 0x2e,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e,
	0x45, 0x6d, 0x70, 0x74, 0x79, 0x22, 0x00, 0x12, 0xa3, 0x01, 0x0a, 0x20, 0x47, 0x65, 0x74, 0x54,
	0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x42, 0x79, 0x50, 0x65, 0x72,
	0x69, 0x6f, 0x64, 0x41, 0x6e, 0x64, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x40, 0x2e, 0x70,
	0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x2e, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61,
	0x63, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f,
	0x6e, 0x2e, 0x47, 0x65, 0x74, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e,
	0x73, 0x42, 0x79, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65, 0x71, 0x56, 0x31, 0x1a, 0x3b,
	0x2e, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x2e, 0x74, 0x72, 0x61, 0x6e,
	0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74,
	0x69, 0x6f, 0x6e, 0x2e, 0x47, 0x65, 0x74, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69,
	0x6f, 0x6e, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0xb3, 0x01,
	0x0a, 0x28, 0x47, 0x65, 0x74, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e,
	0x73, 0x42, 0x79, 0x50, 0x65, 0x72, 0x69, 0x6f, 0x64, 0x41, 0x6e, 0x64, 0x43, 0x61, 0x6c, 0x6c,
	0x62, 0x61, 0x63, 0x6b, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x48, 0x2e, 0x70, 0x72, 0x6f,
	0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x2e, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74,
	0x69, 0x6f, 0x6e, 0x2e, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x2e,
	0x47, 0x65, 0x74, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x42,
	0x79, 0x43, 0x61, 0x6c, 0x6c, 0x62, 0x61, 0x63, 0x6b, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52,
	0x65, 0x71, 0x56, 0x31, 0x1a, 0x3b, 0x2e, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e,
	0x67, 0x2e, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x74, 0x72,
	0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x47, 0x65, 0x74, 0x54, 0x72, 0x61,
	0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x22, 0x00, 0x12, 0xc8, 0x01, 0x0a, 0x2e, 0x47, 0x65, 0x74, 0x54, 0x72, 0x61, 0x6e, 0x73,
	0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x42, 0x79, 0x46, 0x69, 0x6e, 0x61, 0x6c, 0x53, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x41, 0x6e, 0x64, 0x50, 0x65, 0x72, 0x69, 0x6f, 0x64, 0x57, 0x69, 0x74,
	0x68, 0x4c, 0x69, 0x6d, 0x69, 0x74, 0x12, 0x57, 0x2e, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73,
	0x69, 0x6e, 0x67, 0x2e, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x2e,
	0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x47, 0x65, 0x74, 0x54,
	0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x42, 0x79, 0x46, 0x69, 0x6e,
	0x61, 0x6c, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x41, 0x6e, 0x64, 0x50, 0x65, 0x72, 0x69, 0x6f,
	0x64, 0x57, 0x69, 0x74, 0x68, 0x4c, 0x69, 0x6d, 0x69, 0x74, 0x52, 0x65, 0x71, 0x56, 0x31, 0x1a,
	0x3b, 0x2e, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x2e, 0x74, 0x72, 0x61,
	0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63,
	0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x47, 0x65, 0x74, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74,
	0x69, 0x6f, 0x6e, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0xba,
	0x01, 0x0a, 0x27, 0x47, 0x65, 0x74, 0x50, 0x61, 0x79, 0x49, 0x6e, 0x54, 0x72, 0x61, 0x6e, 0x73,
	0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x42, 0x79, 0x50, 0x65, 0x72, 0x69, 0x6f, 0x64, 0x41,
	0x6e, 0x64, 0x41, 0x63, 0x71, 0x75, 0x69, 0x72, 0x65, 0x72, 0x12, 0x50, 0x2e, 0x70, 0x72, 0x6f,
	0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x2e, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74,
	0x69, 0x6f, 0x6e, 0x2e, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x2e,
	0x47, 0x65, 0x74, 0x50, 0x61, 0x79, 0x49, 0x6e, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74,
	0x69, 0x6f, 0x6e, 0x73, 0x42, 0x79, 0x50, 0x65, 0x72, 0x69, 0x6f, 0x64, 0x41, 0x6e, 0x64, 0x41,
	0x63, 0x71, 0x75, 0x69, 0x72, 0x65, 0x72, 0x52, 0x65, 0x71, 0x56, 0x31, 0x1a, 0x3b, 0x2e, 0x70,
	0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x2e, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61,
	0x63, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f,
	0x6e, 0x2e, 0x47, 0x65, 0x74, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e,
	0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0xa5, 0x01, 0x0a, 0x16,
	0x47, 0x65, 0x74, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x79,
	0x70, 0x65, 0x42, 0x79, 0x49, 0x44, 0x12, 0x43, 0x2e, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73,
	0x69, 0x6e, 0x67, 0x2e, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x2e,
	0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x47, 0x65, 0x74, 0x54,
	0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x42, 0x79,
	0x49, 0x44, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x56, 0x31, 0x1a, 0x44, 0x2e, 0x70, 0x72,
	0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x2e, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63,
	0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e,
	0x2e, 0x47, 0x65, 0x74, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x54,
	0x79, 0x70, 0x65, 0x42, 0x79, 0x49, 0x44, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x56,
	0x31, 0x22, 0x00, 0x12, 0x8e, 0x01, 0x0a, 0x12, 0x47, 0x65, 0x74, 0x54, 0x72, 0x61, 0x6e, 0x73,
	0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x42, 0x79, 0x49, 0x44, 0x12, 0x3f, 0x2e, 0x70, 0x72, 0x6f,
	0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x2e, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74,
	0x69, 0x6f, 0x6e, 0x2e, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x2e,
	0x47, 0x65, 0x74, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x42, 0x79,
	0x49, 0x44, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x56, 0x31, 0x1a, 0x35, 0x2e, 0x70, 0x72,
	0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x2e, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63,
	0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e,
	0x2e, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x44, 0x61, 0x74, 0x61,
	0x56, 0x31, 0x22, 0x00, 0x12, 0x67, 0x0a, 0x0e, 0x4d, 0x61, 0x6b, 0x65, 0x41, 0x75, 0x74, 0x6f,
	0x43, 0x68, 0x61, 0x72, 0x67, 0x65, 0x12, 0x3b, 0x2e, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73,
	0x69, 0x6e, 0x67, 0x2e, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x2e,
	0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x4d, 0x61, 0x6b, 0x65,
	0x41, 0x75, 0x74, 0x6f, 0x43, 0x68, 0x61, 0x72, 0x67, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x56, 0x31, 0x1a, 0x16, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x22, 0x00, 0x12, 0xa5, 0x01,
	0x0a, 0x16, 0x53, 0x65, 0x74, 0x52, 0x65, 0x66, 0x75, 0x6e, 0x64, 0x57, 0x61, 0x69, 0x74, 0x69,
	0x6e, 0x67, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x43, 0x2e, 0x70, 0x72, 0x6f, 0x63, 0x65,
	0x73, 0x73, 0x69, 0x6e, 0x67, 0x2e, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f,
	0x6e, 0x2e, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x53, 0x65,
	0x74, 0x52, 0x65, 0x66, 0x75, 0x6e, 0x64, 0x57, 0x61, 0x69, 0x74, 0x69, 0x6e, 0x67, 0x53, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x56, 0x31, 0x1a, 0x44, 0x2e,
	0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x2e, 0x74, 0x72, 0x61, 0x6e, 0x73,
	0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69,
	0x6f, 0x6e, 0x2e, 0x53, 0x65, 0x74, 0x52, 0x65, 0x66, 0x75, 0x6e, 0x64, 0x57, 0x61, 0x69, 0x74,
	0x69, 0x6e, 0x67, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x56, 0x31, 0x22, 0x00, 0x12, 0xa8, 0x01, 0x0a, 0x1c, 0x47, 0x65, 0x74, 0x54, 0x72, 0x61,
	0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x42, 0x79, 0x50, 0x72, 0x6f, 0x6a, 0x65,
	0x63, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x49, 0x2e, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73,
	0x69, 0x6e, 0x67, 0x2e, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x2e,
	0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x47, 0x65, 0x74, 0x54,
	0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x42, 0x79, 0x50, 0x72, 0x6f,
	0x6a, 0x65, 0x63, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x56,
	0x31, 0x1a, 0x3b, 0x2e, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x2e, 0x74,
	0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x74, 0x72, 0x61, 0x6e, 0x73,
	0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x47, 0x65, 0x74, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x61,
	0x63, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00,
	0x12, 0x73, 0x0a, 0x14, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63,
	0x74, 0x69, 0x6f, 0x6e, 0x48, 0x61, 0x73, 0x68, 0x12, 0x41, 0x2e, 0x70, 0x72, 0x6f, 0x63, 0x65,
	0x73, 0x73, 0x69, 0x6e, 0x67, 0x2e, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f,
	0x6e, 0x2e, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x43, 0x68,
	0x65, 0x63, 0x6b, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x48, 0x61,
	0x73, 0x68, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x56, 0x31, 0x1a, 0x16, 0x2e, 0x67, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x45, 0x6d,
	0x70, 0x74, 0x79, 0x22, 0x00, 0x12, 0xa7, 0x01, 0x0a, 0x18, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65,
	0x54, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x42, 0x79, 0x50, 0x68, 0x6f,
	0x6e, 0x65, 0x12, 0x43, 0x2e, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x2e,
	0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x74, 0x72, 0x61, 0x6e,
	0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x54, 0x72,
	0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x42, 0x79, 0x50, 0x68, 0x6f, 0x6e, 0x65,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x44, 0x2e, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73,
	0x73, 0x69, 0x6e, 0x67, 0x2e, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e,
	0x2e, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x43, 0x72, 0x65,
	0x61, 0x74, 0x65, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x42, 0x79,
	0x50, 0x68, 0x6f, 0x6e, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12,
	0x8c, 0x01, 0x0a, 0x0f, 0x47, 0x65, 0x74, 0x42, 0x79, 0x49, 0x44, 0x57, 0x69, 0x74, 0x68, 0x54,
	0x79, 0x70, 0x65, 0x12, 0x3a, 0x2e, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67,
	0x2e, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x74, 0x72, 0x61,
	0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x47, 0x65, 0x74, 0x42, 0x79, 0x49, 0x44,
	0x57, 0x69, 0x74, 0x68, 0x54, 0x79, 0x70, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a,
	0x3b, 0x2e, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x2e, 0x74, 0x72, 0x61,
	0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63,
	0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x47, 0x65, 0x74, 0x42, 0x79, 0x49, 0x44, 0x57, 0x69, 0x74, 0x68,
	0x54, 0x79, 0x70, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x61,
	0x0a, 0x0c, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x37,
	0x2e, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x2e, 0x74, 0x72, 0x61, 0x6e,
	0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74,
	0x69, 0x6f, 0x6e, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x16, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x22,
	0x00, 0x12, 0x69, 0x0a, 0x10, 0x49, 0x6e, 0x63, 0x72, 0x65, 0x61, 0x73, 0x65, 0x54, 0x72, 0x79,
	0x43, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x3b, 0x2e, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69,
	0x6e, 0x67, 0x2e, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x74,
	0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x49, 0x6e, 0x63, 0x72, 0x65,
	0x61, 0x73, 0x65, 0x54, 0x72, 0x79, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x1a, 0x16, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x62, 0x75, 0x66, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x22, 0x00, 0x12, 0x71, 0x0a, 0x14,
	0x53, 0x61, 0x76, 0x65, 0x41, 0x63, 0x71, 0x75, 0x69, 0x72, 0x65, 0x72, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x12, 0x3f, 0x2e, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e,
	0x67, 0x2e, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x74, 0x72,
	0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x53, 0x61, 0x76, 0x65, 0x41, 0x63,
	0x71, 0x75, 0x69, 0x72, 0x65, 0x72, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x16, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x22, 0x00, 0x12,
	0x58, 0x0a, 0x0b, 0x53, 0x65, 0x6e, 0x64, 0x52, 0x65, 0x63, 0x65, 0x69, 0x70, 0x74, 0x12, 0x2f,
	0x2e, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x2e, 0x74, 0x72, 0x61, 0x6e,
	0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74,
	0x69, 0x6f, 0x6e, 0x2e, 0x53, 0x65, 0x6e, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a,
	0x16, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75,
	0x66, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x22, 0x00, 0x12, 0x6b, 0x0a, 0x11, 0x53, 0x65, 0x74,
	0x41, 0x64, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x44, 0x61, 0x74, 0x61, 0x12, 0x3c,
	0x2e, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x2e, 0x74, 0x72, 0x61, 0x6e,
	0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74,
	0x69, 0x6f, 0x6e, 0x2e, 0x53, 0x65, 0x74, 0x41, 0x64, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x61,
	0x6c, 0x44, 0x61, 0x74, 0x61, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x16, 0x2e, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x45,
	0x6d, 0x70, 0x74, 0x79, 0x22, 0x00, 0x12, 0x8f, 0x01, 0x0a, 0x23, 0x43, 0x61, 0x6c, 0x63, 0x75,
	0x6c, 0x61, 0x74, 0x65, 0x41, 0x6e, 0x64, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x54, 0x72, 0x61,
	0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x4e,
	0x2e, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x2e, 0x74, 0x72, 0x61, 0x6e,
	0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74,
	0x69, 0x6f, 0x6e, 0x2e, 0x43, 0x61, 0x6c, 0x63, 0x75, 0x6c, 0x61, 0x74, 0x65, 0x41, 0x6e, 0x64,
	0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f,
	0x6e, 0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x16,
	0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66,
	0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x22, 0x00, 0x12, 0x5d, 0x0a, 0x0a, 0x42, 0x69, 0x6c, 0x6c,
	0x50, 0x61, 0x79, 0x4f, 0x75, 0x74, 0x12, 0x35, 0x2e, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73,
	0x69, 0x6e, 0x67, 0x2e, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x2e,
	0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x42, 0x69, 0x6c, 0x6c,
	0x50, 0x61, 0x79, 0x6f, 0x75, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x16, 0x2e,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e,
	0x45, 0x6d, 0x70, 0x74, 0x79, 0x22, 0x00, 0x42, 0x2d, 0x5a, 0x2b, 0x67, 0x69, 0x74, 0x2e, 0x6c,
	0x6f, 0x63, 0x61, 0x6c, 0x2f, 0x73, 0x65, 0x6e, 0x73, 0x69, 0x74, 0x69, 0x76, 0x65, 0x2f, 0x69,
	0x6e, 0x6e, 0x65, 0x72, 0x70, 0x62, 0x2f, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e,
	0x67, 0x2f, 0x67, 0x72, 0x70, 0x63, 0x62, 0x08, 0x65, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73,
	0x70, 0xe8, 0x07,
})

var (
	file_inner_processing_grpc_transaction_proto_rawDescOnce sync.Once
	file_inner_processing_grpc_transaction_proto_rawDescData []byte
)

func file_inner_processing_grpc_transaction_proto_rawDescGZIP() []byte {
	file_inner_processing_grpc_transaction_proto_rawDescOnce.Do(func() {
		file_inner_processing_grpc_transaction_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_inner_processing_grpc_transaction_proto_rawDesc), len(file_inner_processing_grpc_transaction_proto_rawDesc)))
	})
	return file_inner_processing_grpc_transaction_proto_rawDescData
}

var file_inner_processing_grpc_transaction_proto_msgTypes = make([]protoimpl.MessageInfo, 33)
var file_inner_processing_grpc_transaction_proto_goTypes = []any{
	(*SetRefundWaitingStatusRequestV1)(nil),                     // 0: processing.transaction.transaction.SetRefundWaitingStatusRequestV1
	(*SetRefundWaitingStatusResponseV1)(nil),                    // 1: processing.transaction.transaction.SetRefundWaitingStatusResponseV1
	(*GetPayInTransactionsByPeriodAndAcquirerReqV1)(nil),        // 2: processing.transaction.transaction.GetPayInTransactionsByPeriodAndAcquirerReqV1
	(*GetTransactionsByStatusReqV1)(nil),                        // 3: processing.transaction.transaction.GetTransactionsByStatusReqV1
	(*GetTransactionsByCallbackStatusReqV1)(nil),                // 4: processing.transaction.transaction.GetTransactionsByCallbackStatusReqV1
	(*GetTransactionsByFinalStatusAndPeriodWithLimitReqV1)(nil), // 5: processing.transaction.transaction.GetTransactionsByFinalStatusAndPeriodWithLimitReqV1
	(*GetTransactionByIDRequestV1)(nil),                         // 6: processing.transaction.transaction.GetTransactionByIDRequestV1
	(*TransactionDataV1)(nil),                                   // 7: processing.transaction.transaction.TransactionDataV1
	(*TransactionInfo)(nil),                                     // 8: processing.transaction.transaction.TransactionInfo
	(*GetTransactionsResponse)(nil),                             // 9: processing.transaction.transaction.GetTransactionsResponse
	(*UpdateTransactionStatusRequestV1)(nil),                    // 10: processing.transaction.transaction.UpdateTransactionStatusRequestV1
	(*UpdateCallbackStatusRequestV1)(nil),                       // 11: processing.transaction.transaction.UpdateCallbackStatusRequestV1
	(*GetTransactionTypeByIDRequestV1)(nil),                     // 12: processing.transaction.transaction.GetTransactionTypeByIDRequestV1
	(*GetTransactionTypeByIDResponseV1)(nil),                    // 13: processing.transaction.transaction.GetTransactionTypeByIDResponseV1
	(*MakeAutoChargeRequestV1)(nil),                             // 14: processing.transaction.transaction.MakeAutoChargeRequestV1
	(*GetTransactionsByProjectInfoRequestV1)(nil),               // 15: processing.transaction.transaction.GetTransactionsByProjectInfoRequestV1
	(*CheckTransactionHashRequestV1)(nil),                       // 16: processing.transaction.transaction.CheckTransactionHashRequestV1
	(*CreateTransactionByPhoneRequest)(nil),                     // 17: processing.transaction.transaction.CreateTransactionByPhoneRequest
	(*CreateTransactionByPhoneResponse)(nil),                    // 18: processing.transaction.transaction.CreateTransactionByPhoneResponse
	(*GetByIDWithTypeRequest)(nil),                              // 19: processing.transaction.transaction.GetByIDWithTypeRequest
	(*GetByIDWithTypeResponse)(nil),                             // 20: processing.transaction.transaction.GetByIDWithTypeResponse
	(*TransactionStatus)(nil),                                   // 21: processing.transaction.transaction.TransactionStatus
	(*TransactionType)(nil),                                     // 22: processing.transaction.transaction.TransactionType
	(*UpdateStatusRequest)(nil),                                 // 23: processing.transaction.transaction.UpdateStatusRequest
	(*IncreaseTryCountRequest)(nil),                             // 24: processing.transaction.transaction.IncreaseTryCountRequest
	(*SaveAcquirerResponseRequest)(nil),                         // 25: processing.transaction.transaction.SaveAcquirerResponseRequest
	(*TransactionBankResponse)(nil),                             // 26: processing.transaction.transaction.TransactionBankResponse
	(*SendRequest)(nil),                                         // 27: processing.transaction.transaction.SendRequest
	(*SetAdditionalDataRequest)(nil),                            // 28: processing.transaction.transaction.SetAdditionalDataRequest
	(*CalculateAndUpdateTransactionAmountRequest)(nil),          // 29: processing.transaction.transaction.CalculateAndUpdateTransactionAmountRequest
	(*BillPayoutRequest)(nil),                                   // 30: processing.transaction.transaction.BillPayoutRequest
	nil,                                                         // 31: processing.transaction.transaction.CreateTransactionByPhoneRequest.AdditionalDataEntry
	nil,                                                         // 32: processing.transaction.transaction.SetAdditionalDataRequest.AdditionalDataEntry
	(*timestamppb.Timestamp)(nil),                               // 33: google.protobuf.Timestamp
	(*structpb.Struct)(nil),                                     // 34: google.protobuf.Struct
	(*emptypb.Empty)(nil),                                       // 35: google.protobuf.Empty
}
var file_inner_processing_grpc_transaction_proto_depIdxs = []int32{
	33, // 0: processing.transaction.transaction.GetPayInTransactionsByPeriodAndAcquirerReqV1.start_period:type_name -> google.protobuf.Timestamp
	33, // 1: processing.transaction.transaction.GetPayInTransactionsByPeriodAndAcquirerReqV1.end_period:type_name -> google.protobuf.Timestamp
	33, // 2: processing.transaction.transaction.GetTransactionsByStatusReqV1.start_period:type_name -> google.protobuf.Timestamp
	33, // 3: processing.transaction.transaction.GetTransactionsByStatusReqV1.end_period:type_name -> google.protobuf.Timestamp
	33, // 4: processing.transaction.transaction.GetTransactionsByCallbackStatusReqV1.start_period:type_name -> google.protobuf.Timestamp
	33, // 5: processing.transaction.transaction.GetTransactionsByCallbackStatusReqV1.end_period:type_name -> google.protobuf.Timestamp
	33, // 6: processing.transaction.transaction.GetTransactionsByFinalStatusAndPeriodWithLimitReqV1.start_period:type_name -> google.protobuf.Timestamp
	33, // 7: processing.transaction.transaction.GetTransactionsByFinalStatusAndPeriodWithLimitReqV1.end_period:type_name -> google.protobuf.Timestamp
	33, // 8: processing.transaction.transaction.TransactionDataV1.created_at:type_name -> google.protobuf.Timestamp
	33, // 9: processing.transaction.transaction.TransactionDataV1.updated_at:type_name -> google.protobuf.Timestamp
	33, // 10: processing.transaction.transaction.TransactionDataV1.finished_at:type_name -> google.protobuf.Timestamp
	8,  // 11: processing.transaction.transaction.TransactionDataV1.transaction_info:type_name -> processing.transaction.transaction.TransactionInfo
	33, // 12: processing.transaction.transaction.TransactionInfo.created_at:type_name -> google.protobuf.Timestamp
	33, // 13: processing.transaction.transaction.TransactionInfo.updated_at:type_name -> google.protobuf.Timestamp
	33, // 14: processing.transaction.transaction.TransactionInfo.transaction_created_at:type_name -> google.protobuf.Timestamp
	34, // 15: processing.transaction.transaction.TransactionInfo.jobs_message:type_name -> google.protobuf.Struct
	7,  // 16: processing.transaction.transaction.GetTransactionsResponse.transactions:type_name -> processing.transaction.transaction.TransactionDataV1
	33, // 17: processing.transaction.transaction.GetTransactionsByProjectInfoRequestV1.start_period:type_name -> google.protobuf.Timestamp
	33, // 18: processing.transaction.transaction.GetTransactionsByProjectInfoRequestV1.end_period:type_name -> google.protobuf.Timestamp
	31, // 19: processing.transaction.transaction.CreateTransactionByPhoneRequest.additional_data:type_name -> processing.transaction.transaction.CreateTransactionByPhoneRequest.AdditionalDataEntry
	33, // 20: processing.transaction.transaction.CreateTransactionByPhoneResponse.created_at:type_name -> google.protobuf.Timestamp
	33, // 21: processing.transaction.transaction.GetByIDWithTypeResponse.created_at:type_name -> google.protobuf.Timestamp
	33, // 22: processing.transaction.transaction.GetByIDWithTypeResponse.updated_at:type_name -> google.protobuf.Timestamp
	33, // 23: processing.transaction.transaction.GetByIDWithTypeResponse.finished_at:type_name -> google.protobuf.Timestamp
	8,  // 24: processing.transaction.transaction.GetByIDWithTypeResponse.transaction_info:type_name -> processing.transaction.transaction.TransactionInfo
	22, // 25: processing.transaction.transaction.GetByIDWithTypeResponse.transaction_type:type_name -> processing.transaction.transaction.TransactionType
	21, // 26: processing.transaction.transaction.GetByIDWithTypeResponse.transaction_status:type_name -> processing.transaction.transaction.TransactionStatus
	33, // 27: processing.transaction.transaction.TransactionStatus.created_at:type_name -> google.protobuf.Timestamp
	33, // 28: processing.transaction.transaction.TransactionStatus.updated_at:type_name -> google.protobuf.Timestamp
	33, // 29: processing.transaction.transaction.TransactionType.created_at:type_name -> google.protobuf.Timestamp
	33, // 30: processing.transaction.transaction.TransactionType.updated_at:type_name -> google.protobuf.Timestamp
	33, // 31: processing.transaction.transaction.UpdateStatusRequest.finished_at:type_name -> google.protobuf.Timestamp
	26, // 32: processing.transaction.transaction.SaveAcquirerResponseRequest.bank_response:type_name -> processing.transaction.transaction.TransactionBankResponse
	32, // 33: processing.transaction.transaction.SetAdditionalDataRequest.additional_data:type_name -> processing.transaction.transaction.SetAdditionalDataRequest.AdditionalDataEntry
	33, // 34: processing.transaction.transaction.SetAdditionalDataRequest.created_at:type_name -> google.protobuf.Timestamp
	10, // 35: processing.transaction.transaction.Transaction.UpdateTransactionStatus:input_type -> processing.transaction.transaction.UpdateTransactionStatusRequestV1
	11, // 36: processing.transaction.transaction.Transaction.UpdateCallbackStatus:input_type -> processing.transaction.transaction.UpdateCallbackStatusRequestV1
	3,  // 37: processing.transaction.transaction.Transaction.GetTransactionsByPeriodAndStatus:input_type -> processing.transaction.transaction.GetTransactionsByStatusReqV1
	4,  // 38: processing.transaction.transaction.Transaction.GetTransactionsByPeriodAndCallbackStatus:input_type -> processing.transaction.transaction.GetTransactionsByCallbackStatusReqV1
	5,  // 39: processing.transaction.transaction.Transaction.GetTransactionsByFinalStatusAndPeriodWithLimit:input_type -> processing.transaction.transaction.GetTransactionsByFinalStatusAndPeriodWithLimitReqV1
	2,  // 40: processing.transaction.transaction.Transaction.GetPayInTransactionsByPeriodAndAcquirer:input_type -> processing.transaction.transaction.GetPayInTransactionsByPeriodAndAcquirerReqV1
	12, // 41: processing.transaction.transaction.Transaction.GetTransactionTypeByID:input_type -> processing.transaction.transaction.GetTransactionTypeByIDRequestV1
	6,  // 42: processing.transaction.transaction.Transaction.GetTransactionByID:input_type -> processing.transaction.transaction.GetTransactionByIDRequestV1
	14, // 43: processing.transaction.transaction.Transaction.MakeAutoCharge:input_type -> processing.transaction.transaction.MakeAutoChargeRequestV1
	0,  // 44: processing.transaction.transaction.Transaction.SetRefundWaitingStatus:input_type -> processing.transaction.transaction.SetRefundWaitingStatusRequestV1
	15, // 45: processing.transaction.transaction.Transaction.GetTransactionsByProjectInfo:input_type -> processing.transaction.transaction.GetTransactionsByProjectInfoRequestV1
	16, // 46: processing.transaction.transaction.Transaction.CheckTransactionHash:input_type -> processing.transaction.transaction.CheckTransactionHashRequestV1
	17, // 47: processing.transaction.transaction.Transaction.CreateTransactionByPhone:input_type -> processing.transaction.transaction.CreateTransactionByPhoneRequest
	19, // 48: processing.transaction.transaction.Transaction.GetByIDWithType:input_type -> processing.transaction.transaction.GetByIDWithTypeRequest
	23, // 49: processing.transaction.transaction.Transaction.UpdateStatus:input_type -> processing.transaction.transaction.UpdateStatusRequest
	24, // 50: processing.transaction.transaction.Transaction.IncreaseTryCount:input_type -> processing.transaction.transaction.IncreaseTryCountRequest
	25, // 51: processing.transaction.transaction.Transaction.SaveAcquirerResponse:input_type -> processing.transaction.transaction.SaveAcquirerResponseRequest
	27, // 52: processing.transaction.transaction.Transaction.SendReceipt:input_type -> processing.transaction.transaction.SendRequest
	28, // 53: processing.transaction.transaction.Transaction.SetAdditionalData:input_type -> processing.transaction.transaction.SetAdditionalDataRequest
	29, // 54: processing.transaction.transaction.Transaction.CalculateAndUpdateTransactionAmount:input_type -> processing.transaction.transaction.CalculateAndUpdateTransactionAmountRequest
	30, // 55: processing.transaction.transaction.Transaction.BillPayOut:input_type -> processing.transaction.transaction.BillPayoutRequest
	35, // 56: processing.transaction.transaction.Transaction.UpdateTransactionStatus:output_type -> google.protobuf.Empty
	35, // 57: processing.transaction.transaction.Transaction.UpdateCallbackStatus:output_type -> google.protobuf.Empty
	9,  // 58: processing.transaction.transaction.Transaction.GetTransactionsByPeriodAndStatus:output_type -> processing.transaction.transaction.GetTransactionsResponse
	9,  // 59: processing.transaction.transaction.Transaction.GetTransactionsByPeriodAndCallbackStatus:output_type -> processing.transaction.transaction.GetTransactionsResponse
	9,  // 60: processing.transaction.transaction.Transaction.GetTransactionsByFinalStatusAndPeriodWithLimit:output_type -> processing.transaction.transaction.GetTransactionsResponse
	9,  // 61: processing.transaction.transaction.Transaction.GetPayInTransactionsByPeriodAndAcquirer:output_type -> processing.transaction.transaction.GetTransactionsResponse
	13, // 62: processing.transaction.transaction.Transaction.GetTransactionTypeByID:output_type -> processing.transaction.transaction.GetTransactionTypeByIDResponseV1
	7,  // 63: processing.transaction.transaction.Transaction.GetTransactionByID:output_type -> processing.transaction.transaction.TransactionDataV1
	35, // 64: processing.transaction.transaction.Transaction.MakeAutoCharge:output_type -> google.protobuf.Empty
	1,  // 65: processing.transaction.transaction.Transaction.SetRefundWaitingStatus:output_type -> processing.transaction.transaction.SetRefundWaitingStatusResponseV1
	9,  // 66: processing.transaction.transaction.Transaction.GetTransactionsByProjectInfo:output_type -> processing.transaction.transaction.GetTransactionsResponse
	35, // 67: processing.transaction.transaction.Transaction.CheckTransactionHash:output_type -> google.protobuf.Empty
	18, // 68: processing.transaction.transaction.Transaction.CreateTransactionByPhone:output_type -> processing.transaction.transaction.CreateTransactionByPhoneResponse
	20, // 69: processing.transaction.transaction.Transaction.GetByIDWithType:output_type -> processing.transaction.transaction.GetByIDWithTypeResponse
	35, // 70: processing.transaction.transaction.Transaction.UpdateStatus:output_type -> google.protobuf.Empty
	35, // 71: processing.transaction.transaction.Transaction.IncreaseTryCount:output_type -> google.protobuf.Empty
	35, // 72: processing.transaction.transaction.Transaction.SaveAcquirerResponse:output_type -> google.protobuf.Empty
	35, // 73: processing.transaction.transaction.Transaction.SendReceipt:output_type -> google.protobuf.Empty
	35, // 74: processing.transaction.transaction.Transaction.SetAdditionalData:output_type -> google.protobuf.Empty
	35, // 75: processing.transaction.transaction.Transaction.CalculateAndUpdateTransactionAmount:output_type -> google.protobuf.Empty
	35, // 76: processing.transaction.transaction.Transaction.BillPayOut:output_type -> google.protobuf.Empty
	56, // [56:77] is the sub-list for method output_type
	35, // [35:56] is the sub-list for method input_type
	35, // [35:35] is the sub-list for extension type_name
	35, // [35:35] is the sub-list for extension extendee
	0,  // [0:35] is the sub-list for field type_name
}

func init() { file_inner_processing_grpc_transaction_proto_init() }
func file_inner_processing_grpc_transaction_proto_init() {
	if File_inner_processing_grpc_transaction_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_inner_processing_grpc_transaction_proto_rawDesc), len(file_inner_processing_grpc_transaction_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   33,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_inner_processing_grpc_transaction_proto_goTypes,
		DependencyIndexes: file_inner_processing_grpc_transaction_proto_depIdxs,
		MessageInfos:      file_inner_processing_grpc_transaction_proto_msgTypes,
	}.Build()
	File_inner_processing_grpc_transaction_proto = out.File
	file_inner_processing_grpc_transaction_proto_goTypes = nil
	file_inner_processing_grpc_transaction_proto_depIdxs = nil
}
