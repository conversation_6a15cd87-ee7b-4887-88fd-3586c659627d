// Code generated by MockGen. DO NOT EDIT.
// Source: alatau_city_bank_grpc.pb.go

// Package grpcmock is a generated GoMock package.
package grpcmock

import (
	context "context"
	reflect "reflect"

	grpc "git.local/sensitive/innerpb/processing/grpc"
	gomock "github.com/golang/mock/gomock"
	grpc0 "google.golang.org/grpc"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
)

// MockAlatauCityBankClient is a mock of AlatauCityBankClient interface.
type MockAlatauCityBankClient struct {
	ctrl     *gomock.Controller
	recorder *MockAlatauCityBankClientMockRecorder
}

// MockAlatauCityBankClientMockRecorder is the mock recorder for MockAlatauCityBankClient.
type MockAlatauCityBankClientMockRecorder struct {
	mock *MockAlatauCityBankClient
}

// NewMockAlatauCityBankClient creates a new mock instance.
func NewMockAlatauCityBankClient(ctrl *gomock.Controller) *MockAlatauCityBankClient {
	mock := &MockAlatauCityBankClient{ctrl: ctrl}
	mock.recorder = &MockAlatauCityBankClientMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockAlatauCityBankClient) EXPECT() *MockAlatauCityBankClientMockRecorder {
	return m.recorder
}

// ApplePay mocks base method.
func (m *MockAlatauCityBankClient) ApplePay(ctx context.Context, in *grpc.ApplePayRequestData, opts ...grpc0.CallOption) (*grpc.ApplePayResponseData, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "ApplePay", varargs...)
	ret0, _ := ret[0].(*grpc.ApplePayResponseData)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ApplePay indicates an expected call of ApplePay.
func (mr *MockAlatauCityBankClientMockRecorder) ApplePay(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ApplePay", reflect.TypeOf((*MockAlatauCityBankClient)(nil).ApplePay), varargs...)
}

// Cancel mocks base method.
func (m *MockAlatauCityBankClient) Cancel(ctx context.Context, in *grpc.CancelRequest, opts ...grpc0.CallOption) (*grpc.CancelResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "Cancel", varargs...)
	ret0, _ := ret[0].(*grpc.CancelResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Cancel indicates an expected call of Cancel.
func (mr *MockAlatauCityBankClientMockRecorder) Cancel(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Cancel", reflect.TypeOf((*MockAlatauCityBankClient)(nil).Cancel), varargs...)
}

// Charge mocks base method.
func (m *MockAlatauCityBankClient) Charge(ctx context.Context, in *grpc.ChargeRequest, opts ...grpc0.CallOption) (*grpc.ChargeResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "Charge", varargs...)
	ret0, _ := ret[0].(*grpc.ChargeResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Charge indicates an expected call of Charge.
func (mr *MockAlatauCityBankClientMockRecorder) Charge(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Charge", reflect.TypeOf((*MockAlatauCityBankClient)(nil).Charge), varargs...)
}

// CheckBalance mocks base method.
func (m *MockAlatauCityBankClient) CheckBalance(ctx context.Context, in *grpc.CheckBalanceRequest, opts ...grpc0.CallOption) (*grpc.CheckBalanceResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "CheckBalance", varargs...)
	ret0, _ := ret[0].(*grpc.CheckBalanceResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CheckBalance indicates an expected call of CheckBalance.
func (mr *MockAlatauCityBankClientMockRecorder) CheckBalance(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CheckBalance", reflect.TypeOf((*MockAlatauCityBankClient)(nil).CheckBalance), varargs...)
}

// ConfirmEmission mocks base method.
func (m *MockAlatauCityBankClient) ConfirmEmission(ctx context.Context, in *grpc.EmoneyRequest, opts ...grpc0.CallOption) (*grpc.EmoneyResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "ConfirmEmission", varargs...)
	ret0, _ := ret[0].(*grpc.EmoneyResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ConfirmEmission indicates an expected call of ConfirmEmission.
func (mr *MockAlatauCityBankClientMockRecorder) ConfirmEmission(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ConfirmEmission", reflect.TypeOf((*MockAlatauCityBankClient)(nil).ConfirmEmission), varargs...)
}

// GetAcquirerIdentifier mocks base method.
func (m *MockAlatauCityBankClient) GetAcquirerIdentifier(ctx context.Context, in *emptypb.Empty, opts ...grpc0.CallOption) (*grpc.GetAcquirerIdentifierResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetAcquirerIdentifier", varargs...)
	ret0, _ := ret[0].(*grpc.GetAcquirerIdentifierResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAcquirerIdentifier indicates an expected call of GetAcquirerIdentifier.
func (mr *MockAlatauCityBankClientMockRecorder) GetAcquirerIdentifier(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAcquirerIdentifier", reflect.TypeOf((*MockAlatauCityBankClient)(nil).GetAcquirerIdentifier), varargs...)
}

// GetBankTransactionStatus mocks base method.
func (m *MockAlatauCityBankClient) GetBankTransactionStatus(ctx context.Context, in *grpc.BankTransactionStatusRequest, opts ...grpc0.CallOption) (*grpc.BankTransactionStatusResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetBankTransactionStatus", varargs...)
	ret0, _ := ret[0].(*grpc.BankTransactionStatusResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetBankTransactionStatus indicates an expected call of GetBankTransactionStatus.
func (mr *MockAlatauCityBankClientMockRecorder) GetBankTransactionStatus(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetBankTransactionStatus", reflect.TypeOf((*MockAlatauCityBankClient)(nil).GetBankTransactionStatus), varargs...)
}

// GetBankTransactionStatusUnformated mocks base method.
func (m *MockAlatauCityBankClient) GetBankTransactionStatusUnformated(ctx context.Context, in *grpc.BankTransactionStatusUnformatedRequest, opts ...grpc0.CallOption) (*grpc.BankTransactionStatusUnformatedResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetBankTransactionStatusUnformated", varargs...)
	ret0, _ := ret[0].(*grpc.BankTransactionStatusUnformatedResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetBankTransactionStatusUnformated indicates an expected call of GetBankTransactionStatusUnformated.
func (mr *MockAlatauCityBankClientMockRecorder) GetBankTransactionStatusUnformated(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetBankTransactionStatusUnformated", reflect.TypeOf((*MockAlatauCityBankClient)(nil).GetBankTransactionStatusUnformated), varargs...)
}

// GetEmission mocks base method.
func (m *MockAlatauCityBankClient) GetEmission(ctx context.Context, in *emptypb.Empty, opts ...grpc0.CallOption) (*grpc.EmissionResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetEmission", varargs...)
	ret0, _ := ret[0].(*grpc.EmissionResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetEmission indicates an expected call of GetEmission.
func (mr *MockAlatauCityBankClientMockRecorder) GetEmission(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetEmission", reflect.TypeOf((*MockAlatauCityBankClient)(nil).GetEmission), varargs...)
}

// GooglePay mocks base method.
func (m *MockAlatauCityBankClient) GooglePay(ctx context.Context, in *grpc.GooglePayRequestData, opts ...grpc0.CallOption) (*grpc.GooglePayResponseData, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GooglePay", varargs...)
	ret0, _ := ret[0].(*grpc.GooglePayResponseData)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GooglePay indicates an expected call of GooglePay.
func (mr *MockAlatauCityBankClientMockRecorder) GooglePay(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GooglePay", reflect.TypeOf((*MockAlatauCityBankClient)(nil).GooglePay), varargs...)
}

// MakeToken mocks base method.
func (m *MockAlatauCityBankClient) MakeToken(ctx context.Context, in *grpc.PayInRequestData, opts ...grpc0.CallOption) (*grpc.PayInResponseData, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "MakeToken", varargs...)
	ret0, _ := ret[0].(*grpc.PayInResponseData)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// MakeToken indicates an expected call of MakeToken.
func (mr *MockAlatauCityBankClientMockRecorder) MakeToken(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "MakeToken", reflect.TypeOf((*MockAlatauCityBankClient)(nil).MakeToken), varargs...)
}

// OneClickPayIn mocks base method.
func (m *MockAlatauCityBankClient) OneClickPayIn(ctx context.Context, in *grpc.OneClickPayInRequestData, opts ...grpc0.CallOption) (*grpc.PayInResponseData, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "OneClickPayIn", varargs...)
	ret0, _ := ret[0].(*grpc.PayInResponseData)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// OneClickPayIn indicates an expected call of OneClickPayIn.
func (mr *MockAlatauCityBankClientMockRecorder) OneClickPayIn(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "OneClickPayIn", reflect.TypeOf((*MockAlatauCityBankClient)(nil).OneClickPayIn), varargs...)
}

// PayIn mocks base method.
func (m *MockAlatauCityBankClient) PayIn(ctx context.Context, in *grpc.PayInRequestData, opts ...grpc0.CallOption) (*grpc.PayInResponseData, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "PayIn", varargs...)
	ret0, _ := ret[0].(*grpc.PayInResponseData)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// PayIn indicates an expected call of PayIn.
func (mr *MockAlatauCityBankClientMockRecorder) PayIn(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "PayIn", reflect.TypeOf((*MockAlatauCityBankClient)(nil).PayIn), varargs...)
}

// PayOut mocks base method.
func (m *MockAlatauCityBankClient) PayOut(ctx context.Context, in *grpc.PayOutRequestData, opts ...grpc0.CallOption) (*grpc.PayOutResponseData, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "PayOut", varargs...)
	ret0, _ := ret[0].(*grpc.PayOutResponseData)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// PayOut indicates an expected call of PayOut.
func (mr *MockAlatauCityBankClientMockRecorder) PayOut(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "PayOut", reflect.TypeOf((*MockAlatauCityBankClient)(nil).PayOut), varargs...)
}

// PayOutByPhone mocks base method.
func (m *MockAlatauCityBankClient) PayOutByPhone(ctx context.Context, in *grpc.PayOutByPhoneRequestData, opts ...grpc0.CallOption) (*grpc.PayOutResponseByPhoneData, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "PayOutByPhone", varargs...)
	ret0, _ := ret[0].(*grpc.PayOutResponseByPhoneData)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// PayOutByPhone indicates an expected call of PayOutByPhone.
func (mr *MockAlatauCityBankClientMockRecorder) PayOutByPhone(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "PayOutByPhone", reflect.TypeOf((*MockAlatauCityBankClient)(nil).PayOutByPhone), varargs...)
}

// Refund mocks base method.
func (m *MockAlatauCityBankClient) Refund(ctx context.Context, in *grpc.RefundRequest, opts ...grpc0.CallOption) (*grpc.RefundResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "Refund", varargs...)
	ret0, _ := ret[0].(*grpc.RefundResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Refund indicates an expected call of Refund.
func (mr *MockAlatauCityBankClientMockRecorder) Refund(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Refund", reflect.TypeOf((*MockAlatauCityBankClient)(nil).Refund), varargs...)
}

// ResolveVisaAlias mocks base method.
func (m *MockAlatauCityBankClient) ResolveVisaAlias(ctx context.Context, in *grpc.ResolveVisaAliasRequest, opts ...grpc0.CallOption) (*grpc.ResolveVisaAliasResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "ResolveVisaAlias", varargs...)
	ret0, _ := ret[0].(*grpc.ResolveVisaAliasResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ResolveVisaAlias indicates an expected call of ResolveVisaAlias.
func (mr *MockAlatauCityBankClientMockRecorder) ResolveVisaAlias(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ResolveVisaAlias", reflect.TypeOf((*MockAlatauCityBankClient)(nil).ResolveVisaAlias), varargs...)
}

// ThreeDSConfirm mocks base method.
func (m *MockAlatauCityBankClient) ThreeDSConfirm(ctx context.Context, in *grpc.ThreeDSRequestData, opts ...grpc0.CallOption) (*grpc.ThreeDSResponseData, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "ThreeDSConfirm", varargs...)
	ret0, _ := ret[0].(*grpc.ThreeDSResponseData)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ThreeDSConfirm indicates an expected call of ThreeDSConfirm.
func (mr *MockAlatauCityBankClientMockRecorder) ThreeDSConfirm(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ThreeDSConfirm", reflect.TypeOf((*MockAlatauCityBankClient)(nil).ThreeDSConfirm), varargs...)
}

// ThreeDSResume mocks base method.
func (m *MockAlatauCityBankClient) ThreeDSResume(ctx context.Context, in *grpc.ThreeDSResumeRequest, opts ...grpc0.CallOption) (*grpc.ThreeDSResumeResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "ThreeDSResume", varargs...)
	ret0, _ := ret[0].(*grpc.ThreeDSResumeResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ThreeDSResume indicates an expected call of ThreeDSResume.
func (mr *MockAlatauCityBankClientMockRecorder) ThreeDSResume(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ThreeDSResume", reflect.TypeOf((*MockAlatauCityBankClient)(nil).ThreeDSResume), varargs...)
}

// TwoStagePayIn mocks base method.
func (m *MockAlatauCityBankClient) TwoStagePayIn(ctx context.Context, in *grpc.TwoStagePayInRequest, opts ...grpc0.CallOption) (*grpc.TwoStagePayInResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "TwoStagePayIn", varargs...)
	ret0, _ := ret[0].(*grpc.TwoStagePayInResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// TwoStagePayIn indicates an expected call of TwoStagePayIn.
func (mr *MockAlatauCityBankClientMockRecorder) TwoStagePayIn(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "TwoStagePayIn", reflect.TypeOf((*MockAlatauCityBankClient)(nil).TwoStagePayIn), varargs...)
}

// MockAlatauCityBankServer is a mock of AlatauCityBankServer interface.
type MockAlatauCityBankServer struct {
	ctrl     *gomock.Controller
	recorder *MockAlatauCityBankServerMockRecorder
}

// MockAlatauCityBankServerMockRecorder is the mock recorder for MockAlatauCityBankServer.
type MockAlatauCityBankServerMockRecorder struct {
	mock *MockAlatauCityBankServer
}

// NewMockAlatauCityBankServer creates a new mock instance.
func NewMockAlatauCityBankServer(ctrl *gomock.Controller) *MockAlatauCityBankServer {
	mock := &MockAlatauCityBankServer{ctrl: ctrl}
	mock.recorder = &MockAlatauCityBankServerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockAlatauCityBankServer) EXPECT() *MockAlatauCityBankServerMockRecorder {
	return m.recorder
}

// ApplePay mocks base method.
func (m *MockAlatauCityBankServer) ApplePay(arg0 context.Context, arg1 *grpc.ApplePayRequestData) (*grpc.ApplePayResponseData, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ApplePay", arg0, arg1)
	ret0, _ := ret[0].(*grpc.ApplePayResponseData)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ApplePay indicates an expected call of ApplePay.
func (mr *MockAlatauCityBankServerMockRecorder) ApplePay(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ApplePay", reflect.TypeOf((*MockAlatauCityBankServer)(nil).ApplePay), arg0, arg1)
}

// Cancel mocks base method.
func (m *MockAlatauCityBankServer) Cancel(arg0 context.Context, arg1 *grpc.CancelRequest) (*grpc.CancelResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Cancel", arg0, arg1)
	ret0, _ := ret[0].(*grpc.CancelResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Cancel indicates an expected call of Cancel.
func (mr *MockAlatauCityBankServerMockRecorder) Cancel(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Cancel", reflect.TypeOf((*MockAlatauCityBankServer)(nil).Cancel), arg0, arg1)
}

// Charge mocks base method.
func (m *MockAlatauCityBankServer) Charge(arg0 context.Context, arg1 *grpc.ChargeRequest) (*grpc.ChargeResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Charge", arg0, arg1)
	ret0, _ := ret[0].(*grpc.ChargeResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Charge indicates an expected call of Charge.
func (mr *MockAlatauCityBankServerMockRecorder) Charge(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Charge", reflect.TypeOf((*MockAlatauCityBankServer)(nil).Charge), arg0, arg1)
}

// CheckBalance mocks base method.
func (m *MockAlatauCityBankServer) CheckBalance(arg0 context.Context, arg1 *grpc.CheckBalanceRequest) (*grpc.CheckBalanceResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CheckBalance", arg0, arg1)
	ret0, _ := ret[0].(*grpc.CheckBalanceResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CheckBalance indicates an expected call of CheckBalance.
func (mr *MockAlatauCityBankServerMockRecorder) CheckBalance(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CheckBalance", reflect.TypeOf((*MockAlatauCityBankServer)(nil).CheckBalance), arg0, arg1)
}

// ConfirmEmission mocks base method.
func (m *MockAlatauCityBankServer) ConfirmEmission(arg0 context.Context, arg1 *grpc.EmoneyRequest) (*grpc.EmoneyResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ConfirmEmission", arg0, arg1)
	ret0, _ := ret[0].(*grpc.EmoneyResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ConfirmEmission indicates an expected call of ConfirmEmission.
func (mr *MockAlatauCityBankServerMockRecorder) ConfirmEmission(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ConfirmEmission", reflect.TypeOf((*MockAlatauCityBankServer)(nil).ConfirmEmission), arg0, arg1)
}

// GetAcquirerIdentifier mocks base method.
func (m *MockAlatauCityBankServer) GetAcquirerIdentifier(arg0 context.Context, arg1 *emptypb.Empty) (*grpc.GetAcquirerIdentifierResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAcquirerIdentifier", arg0, arg1)
	ret0, _ := ret[0].(*grpc.GetAcquirerIdentifierResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAcquirerIdentifier indicates an expected call of GetAcquirerIdentifier.
func (mr *MockAlatauCityBankServerMockRecorder) GetAcquirerIdentifier(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAcquirerIdentifier", reflect.TypeOf((*MockAlatauCityBankServer)(nil).GetAcquirerIdentifier), arg0, arg1)
}

// GetBankTransactionStatus mocks base method.
func (m *MockAlatauCityBankServer) GetBankTransactionStatus(arg0 context.Context, arg1 *grpc.BankTransactionStatusRequest) (*grpc.BankTransactionStatusResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetBankTransactionStatus", arg0, arg1)
	ret0, _ := ret[0].(*grpc.BankTransactionStatusResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetBankTransactionStatus indicates an expected call of GetBankTransactionStatus.
func (mr *MockAlatauCityBankServerMockRecorder) GetBankTransactionStatus(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetBankTransactionStatus", reflect.TypeOf((*MockAlatauCityBankServer)(nil).GetBankTransactionStatus), arg0, arg1)
}

// GetBankTransactionStatusUnformated mocks base method.
func (m *MockAlatauCityBankServer) GetBankTransactionStatusUnformated(arg0 context.Context, arg1 *grpc.BankTransactionStatusUnformatedRequest) (*grpc.BankTransactionStatusUnformatedResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetBankTransactionStatusUnformated", arg0, arg1)
	ret0, _ := ret[0].(*grpc.BankTransactionStatusUnformatedResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetBankTransactionStatusUnformated indicates an expected call of GetBankTransactionStatusUnformated.
func (mr *MockAlatauCityBankServerMockRecorder) GetBankTransactionStatusUnformated(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetBankTransactionStatusUnformated", reflect.TypeOf((*MockAlatauCityBankServer)(nil).GetBankTransactionStatusUnformated), arg0, arg1)
}

// GetEmission mocks base method.
func (m *MockAlatauCityBankServer) GetEmission(arg0 context.Context, arg1 *emptypb.Empty) (*grpc.EmissionResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetEmission", arg0, arg1)
	ret0, _ := ret[0].(*grpc.EmissionResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetEmission indicates an expected call of GetEmission.
func (mr *MockAlatauCityBankServerMockRecorder) GetEmission(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetEmission", reflect.TypeOf((*MockAlatauCityBankServer)(nil).GetEmission), arg0, arg1)
}

// GooglePay mocks base method.
func (m *MockAlatauCityBankServer) GooglePay(arg0 context.Context, arg1 *grpc.GooglePayRequestData) (*grpc.GooglePayResponseData, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GooglePay", arg0, arg1)
	ret0, _ := ret[0].(*grpc.GooglePayResponseData)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GooglePay indicates an expected call of GooglePay.
func (mr *MockAlatauCityBankServerMockRecorder) GooglePay(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GooglePay", reflect.TypeOf((*MockAlatauCityBankServer)(nil).GooglePay), arg0, arg1)
}

// MakeToken mocks base method.
func (m *MockAlatauCityBankServer) MakeToken(arg0 context.Context, arg1 *grpc.PayInRequestData) (*grpc.PayInResponseData, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "MakeToken", arg0, arg1)
	ret0, _ := ret[0].(*grpc.PayInResponseData)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// MakeToken indicates an expected call of MakeToken.
func (mr *MockAlatauCityBankServerMockRecorder) MakeToken(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "MakeToken", reflect.TypeOf((*MockAlatauCityBankServer)(nil).MakeToken), arg0, arg1)
}

// OneClickPayIn mocks base method.
func (m *MockAlatauCityBankServer) OneClickPayIn(arg0 context.Context, arg1 *grpc.OneClickPayInRequestData) (*grpc.PayInResponseData, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "OneClickPayIn", arg0, arg1)
	ret0, _ := ret[0].(*grpc.PayInResponseData)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// OneClickPayIn indicates an expected call of OneClickPayIn.
func (mr *MockAlatauCityBankServerMockRecorder) OneClickPayIn(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "OneClickPayIn", reflect.TypeOf((*MockAlatauCityBankServer)(nil).OneClickPayIn), arg0, arg1)
}

// PayIn mocks base method.
func (m *MockAlatauCityBankServer) PayIn(arg0 context.Context, arg1 *grpc.PayInRequestData) (*grpc.PayInResponseData, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "PayIn", arg0, arg1)
	ret0, _ := ret[0].(*grpc.PayInResponseData)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// PayIn indicates an expected call of PayIn.
func (mr *MockAlatauCityBankServerMockRecorder) PayIn(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "PayIn", reflect.TypeOf((*MockAlatauCityBankServer)(nil).PayIn), arg0, arg1)
}

// PayOut mocks base method.
func (m *MockAlatauCityBankServer) PayOut(arg0 context.Context, arg1 *grpc.PayOutRequestData) (*grpc.PayOutResponseData, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "PayOut", arg0, arg1)
	ret0, _ := ret[0].(*grpc.PayOutResponseData)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// PayOut indicates an expected call of PayOut.
func (mr *MockAlatauCityBankServerMockRecorder) PayOut(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "PayOut", reflect.TypeOf((*MockAlatauCityBankServer)(nil).PayOut), arg0, arg1)
}

// PayOutByPhone mocks base method.
func (m *MockAlatauCityBankServer) PayOutByPhone(arg0 context.Context, arg1 *grpc.PayOutByPhoneRequestData) (*grpc.PayOutResponseByPhoneData, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "PayOutByPhone", arg0, arg1)
	ret0, _ := ret[0].(*grpc.PayOutResponseByPhoneData)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// PayOutByPhone indicates an expected call of PayOutByPhone.
func (mr *MockAlatauCityBankServerMockRecorder) PayOutByPhone(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "PayOutByPhone", reflect.TypeOf((*MockAlatauCityBankServer)(nil).PayOutByPhone), arg0, arg1)
}

// Refund mocks base method.
func (m *MockAlatauCityBankServer) Refund(arg0 context.Context, arg1 *grpc.RefundRequest) (*grpc.RefundResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Refund", arg0, arg1)
	ret0, _ := ret[0].(*grpc.RefundResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Refund indicates an expected call of Refund.
func (mr *MockAlatauCityBankServerMockRecorder) Refund(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Refund", reflect.TypeOf((*MockAlatauCityBankServer)(nil).Refund), arg0, arg1)
}

// ResolveVisaAlias mocks base method.
func (m *MockAlatauCityBankServer) ResolveVisaAlias(arg0 context.Context, arg1 *grpc.ResolveVisaAliasRequest) (*grpc.ResolveVisaAliasResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ResolveVisaAlias", arg0, arg1)
	ret0, _ := ret[0].(*grpc.ResolveVisaAliasResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ResolveVisaAlias indicates an expected call of ResolveVisaAlias.
func (mr *MockAlatauCityBankServerMockRecorder) ResolveVisaAlias(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ResolveVisaAlias", reflect.TypeOf((*MockAlatauCityBankServer)(nil).ResolveVisaAlias), arg0, arg1)
}

// ThreeDSConfirm mocks base method.
func (m *MockAlatauCityBankServer) ThreeDSConfirm(arg0 context.Context, arg1 *grpc.ThreeDSRequestData) (*grpc.ThreeDSResponseData, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ThreeDSConfirm", arg0, arg1)
	ret0, _ := ret[0].(*grpc.ThreeDSResponseData)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ThreeDSConfirm indicates an expected call of ThreeDSConfirm.
func (mr *MockAlatauCityBankServerMockRecorder) ThreeDSConfirm(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ThreeDSConfirm", reflect.TypeOf((*MockAlatauCityBankServer)(nil).ThreeDSConfirm), arg0, arg1)
}

// ThreeDSResume mocks base method.
func (m *MockAlatauCityBankServer) ThreeDSResume(arg0 context.Context, arg1 *grpc.ThreeDSResumeRequest) (*grpc.ThreeDSResumeResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ThreeDSResume", arg0, arg1)
	ret0, _ := ret[0].(*grpc.ThreeDSResumeResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ThreeDSResume indicates an expected call of ThreeDSResume.
func (mr *MockAlatauCityBankServerMockRecorder) ThreeDSResume(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ThreeDSResume", reflect.TypeOf((*MockAlatauCityBankServer)(nil).ThreeDSResume), arg0, arg1)
}

// TwoStagePayIn mocks base method.
func (m *MockAlatauCityBankServer) TwoStagePayIn(arg0 context.Context, arg1 *grpc.TwoStagePayInRequest) (*grpc.TwoStagePayInResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "TwoStagePayIn", arg0, arg1)
	ret0, _ := ret[0].(*grpc.TwoStagePayInResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// TwoStagePayIn indicates an expected call of TwoStagePayIn.
func (mr *MockAlatauCityBankServerMockRecorder) TwoStagePayIn(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "TwoStagePayIn", reflect.TypeOf((*MockAlatauCityBankServer)(nil).TwoStagePayIn), arg0, arg1)
}

// mustEmbedUnimplementedAlatauCityBankServer mocks base method.
func (m *MockAlatauCityBankServer) mustEmbedUnimplementedAlatauCityBankServer() {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "mustEmbedUnimplementedAlatauCityBankServer")
}

// mustEmbedUnimplementedAlatauCityBankServer indicates an expected call of mustEmbedUnimplementedAlatauCityBankServer.
func (mr *MockAlatauCityBankServerMockRecorder) mustEmbedUnimplementedAlatauCityBankServer() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "mustEmbedUnimplementedAlatauCityBankServer", reflect.TypeOf((*MockAlatauCityBankServer)(nil).mustEmbedUnimplementedAlatauCityBankServer))
}

// MockUnsafeAlatauCityBankServer is a mock of UnsafeAlatauCityBankServer interface.
type MockUnsafeAlatauCityBankServer struct {
	ctrl     *gomock.Controller
	recorder *MockUnsafeAlatauCityBankServerMockRecorder
}

// MockUnsafeAlatauCityBankServerMockRecorder is the mock recorder for MockUnsafeAlatauCityBankServer.
type MockUnsafeAlatauCityBankServerMockRecorder struct {
	mock *MockUnsafeAlatauCityBankServer
}

// NewMockUnsafeAlatauCityBankServer creates a new mock instance.
func NewMockUnsafeAlatauCityBankServer(ctrl *gomock.Controller) *MockUnsafeAlatauCityBankServer {
	mock := &MockUnsafeAlatauCityBankServer{ctrl: ctrl}
	mock.recorder = &MockUnsafeAlatauCityBankServerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockUnsafeAlatauCityBankServer) EXPECT() *MockUnsafeAlatauCityBankServerMockRecorder {
	return m.recorder
}

// mustEmbedUnimplementedAlatauCityBankServer mocks base method.
func (m *MockUnsafeAlatauCityBankServer) mustEmbedUnimplementedAlatauCityBankServer() {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "mustEmbedUnimplementedAlatauCityBankServer")
}

// mustEmbedUnimplementedAlatauCityBankServer indicates an expected call of mustEmbedUnimplementedAlatauCityBankServer.
func (mr *MockUnsafeAlatauCityBankServerMockRecorder) mustEmbedUnimplementedAlatauCityBankServer() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "mustEmbedUnimplementedAlatauCityBankServer", reflect.TypeOf((*MockUnsafeAlatauCityBankServer)(nil).mustEmbedUnimplementedAlatauCityBankServer))
}
