// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.5.1
// - protoc             v5.29.3
// source: inner/processing/grpc/billing.proto

package grpc

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.64.0 or later.
const _ = grpc.SupportPackageIsVersion9

const (
	Billing_CheckPayOutBalanceV1_FullMethodName                                         = "/processing.billing.billing.Billing/CheckPayOutBalanceV1"
	Billing_BillPayInTransactionV1_FullMethodName                                       = "/processing.billing.billing.Billing/BillPayInTransactionV1"
	Billing_BillPayOutTransactionV1_FullMethodName                                      = "/processing.billing.billing.Billing/BillPayOutTransactionV1"
	Billing_BillRefundTransactionV1_FullMethodName                                      = "/processing.billing.billing.Billing/BillRefundTransactionV1"
	Billing_CheckOutTransferBalanceV1_FullMethodName                                    = "/processing.billing.billing.Billing/CheckOutTransferBalanceV1"
	Billing_BillOutTransferV1_FullMethodName                                            = "/processing.billing.billing.Billing/BillOutTransferV1"
	Billing_BillInTransferV1_FullMethodName                                             = "/processing.billing.billing.Billing/BillInTransferV1"
	Billing_GetMerchantByBalanceOwnerIDV1_FullMethodName                                = "/processing.billing.billing.Billing/GetMerchantByBalanceOwnerIDV1"
	Billing_SetInTransferV1_FullMethodName                                              = "/processing.billing.billing.Billing/SetInTransferV1"
	Billing_BillSplitTransferV1_FullMethodName                                          = "/processing.billing.billing.Billing/BillSplitTransferV1"
	Billing_GetBalanceOwnerV1_FullMethodName                                            = "/processing.billing.billing.Billing/GetBalanceOwnerV1"
	Billing_SetBalanceOwnerSplittableV1_FullMethodName                                  = "/processing.billing.billing.Billing/SetBalanceOwnerSplittableV1"
	Billing_GetBalanceOwnerByIDV1_FullMethodName                                        = "/processing.billing.billing.Billing/GetBalanceOwnerByIDV1"
	Billing_GetEntityTypeByIDV1_FullMethodName                                          = "/processing.billing.billing.Billing/GetEntityTypeByIDV1"
	Billing_GetCountryCodeByIDV1_FullMethodName                                         = "/processing.billing.billing.Billing/GetCountryCodeByIDV1"
	Billing_GetBalanceByIDV1_FullMethodName                                             = "/processing.billing.billing.Billing/GetBalanceByIDV1"
	Billing_CheckHasBalanceV1_FullMethodName                                            = "/processing.billing.billing.Billing/CheckHasBalanceV1"
	Billing_CheckBalanceOwnerAccountExistsByBalanceOwnerIdAndAccountIdV1_FullMethodName = "/processing.billing.billing.Billing/CheckBalanceOwnerAccountExistsByBalanceOwnerIdAndAccountIdV1"
	Billing_GetBalanceAccountByNumber_FullMethodName                                    = "/processing.billing.billing.Billing/GetBalanceAccountByNumber"
	Billing_GetCurrentBalanceAmountByBalanceOwnerID_FullMethodName                      = "/processing.billing.billing.Billing/GetCurrentBalanceAmountByBalanceOwnerID"
	Billing_CheckBalanceCreditExpireDate_FullMethodName                                 = "/processing.billing.billing.Billing/CheckBalanceCreditExpireDate"
	Billing_CheckBalanceCreditStartDate_FullMethodName                                  = "/processing.billing.billing.Billing/CheckBalanceCreditStartDate"
	Billing_RecalculateProvisionalBalances_FullMethodName                               = "/processing.billing.billing.Billing/RecalculateProvisionalBalances"
	Billing_RecalculateFinalBalances_FullMethodName                                     = "/processing.billing.billing.Billing/RecalculateFinalBalances"
	Billing_RecalculateCreditBalances_FullMethodName                                    = "/processing.billing.billing.Billing/RecalculateCreditBalances"
)

// BillingClient is the client API for Billing service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type BillingClient interface {
	CheckPayOutBalanceV1(ctx context.Context, in *CheckPayOutBalanceReqV1, opts ...grpc.CallOption) (*CheckPayOutBalanceResV1, error)
	BillPayInTransactionV1(ctx context.Context, in *BillPayInTransactionRequestV1, opts ...grpc.CallOption) (*emptypb.Empty, error)
	BillPayOutTransactionV1(ctx context.Context, in *BillPayOutTransactionRequestV1, opts ...grpc.CallOption) (*emptypb.Empty, error)
	BillRefundTransactionV1(ctx context.Context, in *BillRefundTransactionRequestV1, opts ...grpc.CallOption) (*emptypb.Empty, error)
	CheckOutTransferBalanceV1(ctx context.Context, in *CheckOutTransferBalanceRequestV1, opts ...grpc.CallOption) (*CheckOutTransferBalanceResponseV1, error)
	BillOutTransferV1(ctx context.Context, in *BillOutTransferRequestV1, opts ...grpc.CallOption) (*emptypb.Empty, error)
	BillInTransferV1(ctx context.Context, in *BillInTransferRequestV1, opts ...grpc.CallOption) (*emptypb.Empty, error)
	GetMerchantByBalanceOwnerIDV1(ctx context.Context, in *GetMerchantByBalanceOwnerRequestV1, opts ...grpc.CallOption) (*GetMerchantByBalanceOwnerResponseV1, error)
	SetInTransferV1(ctx context.Context, in *SetInTransferRequestV1, opts ...grpc.CallOption) (*emptypb.Empty, error)
	BillSplitTransferV1(ctx context.Context, in *BillSplitTransferRequestV1, opts ...grpc.CallOption) (*emptypb.Empty, error)
	GetBalanceOwnerV1(ctx context.Context, in *GetBalanceOwnerRequestV1, opts ...grpc.CallOption) (*GetBalanceOwnerResponseV1, error)
	SetBalanceOwnerSplittableV1(ctx context.Context, in *SetBalanceOwnerSplittableRequestV1, opts ...grpc.CallOption) (*emptypb.Empty, error)
	GetBalanceOwnerByIDV1(ctx context.Context, in *GetBalanceOwnerByIDRequestV1, opts ...grpc.CallOption) (*GetBalanceOwnerResponseV1, error)
	GetEntityTypeByIDV1(ctx context.Context, in *GetEntityTypeByIDRequestV1, opts ...grpc.CallOption) (*GetEntityTypeResponseV1, error)
	GetCountryCodeByIDV1(ctx context.Context, in *GetCountryCodeByIDRequestV1, opts ...grpc.CallOption) (*GetCountryCodeResponseV1, error)
	GetBalanceByIDV1(ctx context.Context, in *GetBalanceByIDRequestV1, opts ...grpc.CallOption) (*GetBalanceResponseV1, error)
	CheckHasBalanceV1(ctx context.Context, in *CheckHasBalanceRequestV1, opts ...grpc.CallOption) (*emptypb.Empty, error)
	CheckBalanceOwnerAccountExistsByBalanceOwnerIdAndAccountIdV1(ctx context.Context, in *CheckBalanceOwnerAccountExistsByBalanceOwnerIdAndAccountIdRequestV1, opts ...grpc.CallOption) (*emptypb.Empty, error)
	GetBalanceAccountByNumber(ctx context.Context, in *GetBalanceAccountByNumberRequest, opts ...grpc.CallOption) (*GetBalanceAccountByNumberResponse, error)
	GetCurrentBalanceAmountByBalanceOwnerID(ctx context.Context, in *GetCurrentBalanceAmountByAccountAndOwnerIDRequest, opts ...grpc.CallOption) (*GetCurrentBalanceAmountByAccountAndOwnerIDResponse, error)
	// jobs
	CheckBalanceCreditExpireDate(ctx context.Context, in *emptypb.Empty, opts ...grpc.CallOption) (*emptypb.Empty, error)
	CheckBalanceCreditStartDate(ctx context.Context, in *emptypb.Empty, opts ...grpc.CallOption) (*emptypb.Empty, error)
	RecalculateProvisionalBalances(ctx context.Context, in *emptypb.Empty, opts ...grpc.CallOption) (*emptypb.Empty, error)
	RecalculateFinalBalances(ctx context.Context, in *emptypb.Empty, opts ...grpc.CallOption) (*emptypb.Empty, error)
	RecalculateCreditBalances(ctx context.Context, in *emptypb.Empty, opts ...grpc.CallOption) (*emptypb.Empty, error)
}

type billingClient struct {
	cc grpc.ClientConnInterface
}

func NewBillingClient(cc grpc.ClientConnInterface) BillingClient {
	return &billingClient{cc}
}

func (c *billingClient) CheckPayOutBalanceV1(ctx context.Context, in *CheckPayOutBalanceReqV1, opts ...grpc.CallOption) (*CheckPayOutBalanceResV1, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(CheckPayOutBalanceResV1)
	err := c.cc.Invoke(ctx, Billing_CheckPayOutBalanceV1_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *billingClient) BillPayInTransactionV1(ctx context.Context, in *BillPayInTransactionRequestV1, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, Billing_BillPayInTransactionV1_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *billingClient) BillPayOutTransactionV1(ctx context.Context, in *BillPayOutTransactionRequestV1, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, Billing_BillPayOutTransactionV1_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *billingClient) BillRefundTransactionV1(ctx context.Context, in *BillRefundTransactionRequestV1, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, Billing_BillRefundTransactionV1_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *billingClient) CheckOutTransferBalanceV1(ctx context.Context, in *CheckOutTransferBalanceRequestV1, opts ...grpc.CallOption) (*CheckOutTransferBalanceResponseV1, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(CheckOutTransferBalanceResponseV1)
	err := c.cc.Invoke(ctx, Billing_CheckOutTransferBalanceV1_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *billingClient) BillOutTransferV1(ctx context.Context, in *BillOutTransferRequestV1, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, Billing_BillOutTransferV1_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *billingClient) BillInTransferV1(ctx context.Context, in *BillInTransferRequestV1, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, Billing_BillInTransferV1_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *billingClient) GetMerchantByBalanceOwnerIDV1(ctx context.Context, in *GetMerchantByBalanceOwnerRequestV1, opts ...grpc.CallOption) (*GetMerchantByBalanceOwnerResponseV1, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetMerchantByBalanceOwnerResponseV1)
	err := c.cc.Invoke(ctx, Billing_GetMerchantByBalanceOwnerIDV1_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *billingClient) SetInTransferV1(ctx context.Context, in *SetInTransferRequestV1, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, Billing_SetInTransferV1_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *billingClient) BillSplitTransferV1(ctx context.Context, in *BillSplitTransferRequestV1, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, Billing_BillSplitTransferV1_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *billingClient) GetBalanceOwnerV1(ctx context.Context, in *GetBalanceOwnerRequestV1, opts ...grpc.CallOption) (*GetBalanceOwnerResponseV1, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetBalanceOwnerResponseV1)
	err := c.cc.Invoke(ctx, Billing_GetBalanceOwnerV1_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *billingClient) SetBalanceOwnerSplittableV1(ctx context.Context, in *SetBalanceOwnerSplittableRequestV1, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, Billing_SetBalanceOwnerSplittableV1_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *billingClient) GetBalanceOwnerByIDV1(ctx context.Context, in *GetBalanceOwnerByIDRequestV1, opts ...grpc.CallOption) (*GetBalanceOwnerResponseV1, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetBalanceOwnerResponseV1)
	err := c.cc.Invoke(ctx, Billing_GetBalanceOwnerByIDV1_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *billingClient) GetEntityTypeByIDV1(ctx context.Context, in *GetEntityTypeByIDRequestV1, opts ...grpc.CallOption) (*GetEntityTypeResponseV1, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetEntityTypeResponseV1)
	err := c.cc.Invoke(ctx, Billing_GetEntityTypeByIDV1_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *billingClient) GetCountryCodeByIDV1(ctx context.Context, in *GetCountryCodeByIDRequestV1, opts ...grpc.CallOption) (*GetCountryCodeResponseV1, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetCountryCodeResponseV1)
	err := c.cc.Invoke(ctx, Billing_GetCountryCodeByIDV1_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *billingClient) GetBalanceByIDV1(ctx context.Context, in *GetBalanceByIDRequestV1, opts ...grpc.CallOption) (*GetBalanceResponseV1, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetBalanceResponseV1)
	err := c.cc.Invoke(ctx, Billing_GetBalanceByIDV1_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *billingClient) CheckHasBalanceV1(ctx context.Context, in *CheckHasBalanceRequestV1, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, Billing_CheckHasBalanceV1_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *billingClient) CheckBalanceOwnerAccountExistsByBalanceOwnerIdAndAccountIdV1(ctx context.Context, in *CheckBalanceOwnerAccountExistsByBalanceOwnerIdAndAccountIdRequestV1, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, Billing_CheckBalanceOwnerAccountExistsByBalanceOwnerIdAndAccountIdV1_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *billingClient) GetBalanceAccountByNumber(ctx context.Context, in *GetBalanceAccountByNumberRequest, opts ...grpc.CallOption) (*GetBalanceAccountByNumberResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetBalanceAccountByNumberResponse)
	err := c.cc.Invoke(ctx, Billing_GetBalanceAccountByNumber_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *billingClient) GetCurrentBalanceAmountByBalanceOwnerID(ctx context.Context, in *GetCurrentBalanceAmountByAccountAndOwnerIDRequest, opts ...grpc.CallOption) (*GetCurrentBalanceAmountByAccountAndOwnerIDResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetCurrentBalanceAmountByAccountAndOwnerIDResponse)
	err := c.cc.Invoke(ctx, Billing_GetCurrentBalanceAmountByBalanceOwnerID_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *billingClient) CheckBalanceCreditExpireDate(ctx context.Context, in *emptypb.Empty, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, Billing_CheckBalanceCreditExpireDate_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *billingClient) CheckBalanceCreditStartDate(ctx context.Context, in *emptypb.Empty, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, Billing_CheckBalanceCreditStartDate_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *billingClient) RecalculateProvisionalBalances(ctx context.Context, in *emptypb.Empty, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, Billing_RecalculateProvisionalBalances_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *billingClient) RecalculateFinalBalances(ctx context.Context, in *emptypb.Empty, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, Billing_RecalculateFinalBalances_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *billingClient) RecalculateCreditBalances(ctx context.Context, in *emptypb.Empty, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, Billing_RecalculateCreditBalances_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// BillingServer is the server API for Billing service.
// All implementations must embed UnimplementedBillingServer
// for forward compatibility.
type BillingServer interface {
	CheckPayOutBalanceV1(context.Context, *CheckPayOutBalanceReqV1) (*CheckPayOutBalanceResV1, error)
	BillPayInTransactionV1(context.Context, *BillPayInTransactionRequestV1) (*emptypb.Empty, error)
	BillPayOutTransactionV1(context.Context, *BillPayOutTransactionRequestV1) (*emptypb.Empty, error)
	BillRefundTransactionV1(context.Context, *BillRefundTransactionRequestV1) (*emptypb.Empty, error)
	CheckOutTransferBalanceV1(context.Context, *CheckOutTransferBalanceRequestV1) (*CheckOutTransferBalanceResponseV1, error)
	BillOutTransferV1(context.Context, *BillOutTransferRequestV1) (*emptypb.Empty, error)
	BillInTransferV1(context.Context, *BillInTransferRequestV1) (*emptypb.Empty, error)
	GetMerchantByBalanceOwnerIDV1(context.Context, *GetMerchantByBalanceOwnerRequestV1) (*GetMerchantByBalanceOwnerResponseV1, error)
	SetInTransferV1(context.Context, *SetInTransferRequestV1) (*emptypb.Empty, error)
	BillSplitTransferV1(context.Context, *BillSplitTransferRequestV1) (*emptypb.Empty, error)
	GetBalanceOwnerV1(context.Context, *GetBalanceOwnerRequestV1) (*GetBalanceOwnerResponseV1, error)
	SetBalanceOwnerSplittableV1(context.Context, *SetBalanceOwnerSplittableRequestV1) (*emptypb.Empty, error)
	GetBalanceOwnerByIDV1(context.Context, *GetBalanceOwnerByIDRequestV1) (*GetBalanceOwnerResponseV1, error)
	GetEntityTypeByIDV1(context.Context, *GetEntityTypeByIDRequestV1) (*GetEntityTypeResponseV1, error)
	GetCountryCodeByIDV1(context.Context, *GetCountryCodeByIDRequestV1) (*GetCountryCodeResponseV1, error)
	GetBalanceByIDV1(context.Context, *GetBalanceByIDRequestV1) (*GetBalanceResponseV1, error)
	CheckHasBalanceV1(context.Context, *CheckHasBalanceRequestV1) (*emptypb.Empty, error)
	CheckBalanceOwnerAccountExistsByBalanceOwnerIdAndAccountIdV1(context.Context, *CheckBalanceOwnerAccountExistsByBalanceOwnerIdAndAccountIdRequestV1) (*emptypb.Empty, error)
	GetBalanceAccountByNumber(context.Context, *GetBalanceAccountByNumberRequest) (*GetBalanceAccountByNumberResponse, error)
	GetCurrentBalanceAmountByBalanceOwnerID(context.Context, *GetCurrentBalanceAmountByAccountAndOwnerIDRequest) (*GetCurrentBalanceAmountByAccountAndOwnerIDResponse, error)
	// jobs
	CheckBalanceCreditExpireDate(context.Context, *emptypb.Empty) (*emptypb.Empty, error)
	CheckBalanceCreditStartDate(context.Context, *emptypb.Empty) (*emptypb.Empty, error)
	RecalculateProvisionalBalances(context.Context, *emptypb.Empty) (*emptypb.Empty, error)
	RecalculateFinalBalances(context.Context, *emptypb.Empty) (*emptypb.Empty, error)
	RecalculateCreditBalances(context.Context, *emptypb.Empty) (*emptypb.Empty, error)
	mustEmbedUnimplementedBillingServer()
}

// UnimplementedBillingServer must be embedded to have
// forward compatible implementations.
//
// NOTE: this should be embedded by value instead of pointer to avoid a nil
// pointer dereference when methods are called.
type UnimplementedBillingServer struct{}

func (UnimplementedBillingServer) CheckPayOutBalanceV1(context.Context, *CheckPayOutBalanceReqV1) (*CheckPayOutBalanceResV1, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CheckPayOutBalanceV1 not implemented")
}
func (UnimplementedBillingServer) BillPayInTransactionV1(context.Context, *BillPayInTransactionRequestV1) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method BillPayInTransactionV1 not implemented")
}
func (UnimplementedBillingServer) BillPayOutTransactionV1(context.Context, *BillPayOutTransactionRequestV1) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method BillPayOutTransactionV1 not implemented")
}
func (UnimplementedBillingServer) BillRefundTransactionV1(context.Context, *BillRefundTransactionRequestV1) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method BillRefundTransactionV1 not implemented")
}
func (UnimplementedBillingServer) CheckOutTransferBalanceV1(context.Context, *CheckOutTransferBalanceRequestV1) (*CheckOutTransferBalanceResponseV1, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CheckOutTransferBalanceV1 not implemented")
}
func (UnimplementedBillingServer) BillOutTransferV1(context.Context, *BillOutTransferRequestV1) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method BillOutTransferV1 not implemented")
}
func (UnimplementedBillingServer) BillInTransferV1(context.Context, *BillInTransferRequestV1) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method BillInTransferV1 not implemented")
}
func (UnimplementedBillingServer) GetMerchantByBalanceOwnerIDV1(context.Context, *GetMerchantByBalanceOwnerRequestV1) (*GetMerchantByBalanceOwnerResponseV1, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetMerchantByBalanceOwnerIDV1 not implemented")
}
func (UnimplementedBillingServer) SetInTransferV1(context.Context, *SetInTransferRequestV1) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SetInTransferV1 not implemented")
}
func (UnimplementedBillingServer) BillSplitTransferV1(context.Context, *BillSplitTransferRequestV1) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method BillSplitTransferV1 not implemented")
}
func (UnimplementedBillingServer) GetBalanceOwnerV1(context.Context, *GetBalanceOwnerRequestV1) (*GetBalanceOwnerResponseV1, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetBalanceOwnerV1 not implemented")
}
func (UnimplementedBillingServer) SetBalanceOwnerSplittableV1(context.Context, *SetBalanceOwnerSplittableRequestV1) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SetBalanceOwnerSplittableV1 not implemented")
}
func (UnimplementedBillingServer) GetBalanceOwnerByIDV1(context.Context, *GetBalanceOwnerByIDRequestV1) (*GetBalanceOwnerResponseV1, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetBalanceOwnerByIDV1 not implemented")
}
func (UnimplementedBillingServer) GetEntityTypeByIDV1(context.Context, *GetEntityTypeByIDRequestV1) (*GetEntityTypeResponseV1, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetEntityTypeByIDV1 not implemented")
}
func (UnimplementedBillingServer) GetCountryCodeByIDV1(context.Context, *GetCountryCodeByIDRequestV1) (*GetCountryCodeResponseV1, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetCountryCodeByIDV1 not implemented")
}
func (UnimplementedBillingServer) GetBalanceByIDV1(context.Context, *GetBalanceByIDRequestV1) (*GetBalanceResponseV1, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetBalanceByIDV1 not implemented")
}
func (UnimplementedBillingServer) CheckHasBalanceV1(context.Context, *CheckHasBalanceRequestV1) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CheckHasBalanceV1 not implemented")
}
func (UnimplementedBillingServer) CheckBalanceOwnerAccountExistsByBalanceOwnerIdAndAccountIdV1(context.Context, *CheckBalanceOwnerAccountExistsByBalanceOwnerIdAndAccountIdRequestV1) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CheckBalanceOwnerAccountExistsByBalanceOwnerIdAndAccountIdV1 not implemented")
}
func (UnimplementedBillingServer) GetBalanceAccountByNumber(context.Context, *GetBalanceAccountByNumberRequest) (*GetBalanceAccountByNumberResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetBalanceAccountByNumber not implemented")
}
func (UnimplementedBillingServer) GetCurrentBalanceAmountByBalanceOwnerID(context.Context, *GetCurrentBalanceAmountByAccountAndOwnerIDRequest) (*GetCurrentBalanceAmountByAccountAndOwnerIDResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetCurrentBalanceAmountByBalanceOwnerID not implemented")
}
func (UnimplementedBillingServer) CheckBalanceCreditExpireDate(context.Context, *emptypb.Empty) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CheckBalanceCreditExpireDate not implemented")
}
func (UnimplementedBillingServer) CheckBalanceCreditStartDate(context.Context, *emptypb.Empty) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CheckBalanceCreditStartDate not implemented")
}
func (UnimplementedBillingServer) RecalculateProvisionalBalances(context.Context, *emptypb.Empty) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method RecalculateProvisionalBalances not implemented")
}
func (UnimplementedBillingServer) RecalculateFinalBalances(context.Context, *emptypb.Empty) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method RecalculateFinalBalances not implemented")
}
func (UnimplementedBillingServer) RecalculateCreditBalances(context.Context, *emptypb.Empty) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method RecalculateCreditBalances not implemented")
}
func (UnimplementedBillingServer) mustEmbedUnimplementedBillingServer() {}
func (UnimplementedBillingServer) testEmbeddedByValue()                 {}

// UnsafeBillingServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to BillingServer will
// result in compilation errors.
type UnsafeBillingServer interface {
	mustEmbedUnimplementedBillingServer()
}

func RegisterBillingServer(s grpc.ServiceRegistrar, srv BillingServer) {
	// If the following call pancis, it indicates UnimplementedBillingServer was
	// embedded by pointer and is nil.  This will cause panics if an
	// unimplemented method is ever invoked, so we test this at initialization
	// time to prevent it from happening at runtime later due to I/O.
	if t, ok := srv.(interface{ testEmbeddedByValue() }); ok {
		t.testEmbeddedByValue()
	}
	s.RegisterService(&Billing_ServiceDesc, srv)
}

func _Billing_CheckPayOutBalanceV1_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CheckPayOutBalanceReqV1)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BillingServer).CheckPayOutBalanceV1(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Billing_CheckPayOutBalanceV1_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BillingServer).CheckPayOutBalanceV1(ctx, req.(*CheckPayOutBalanceReqV1))
	}
	return interceptor(ctx, in, info, handler)
}

func _Billing_BillPayInTransactionV1_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BillPayInTransactionRequestV1)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BillingServer).BillPayInTransactionV1(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Billing_BillPayInTransactionV1_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BillingServer).BillPayInTransactionV1(ctx, req.(*BillPayInTransactionRequestV1))
	}
	return interceptor(ctx, in, info, handler)
}

func _Billing_BillPayOutTransactionV1_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BillPayOutTransactionRequestV1)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BillingServer).BillPayOutTransactionV1(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Billing_BillPayOutTransactionV1_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BillingServer).BillPayOutTransactionV1(ctx, req.(*BillPayOutTransactionRequestV1))
	}
	return interceptor(ctx, in, info, handler)
}

func _Billing_BillRefundTransactionV1_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BillRefundTransactionRequestV1)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BillingServer).BillRefundTransactionV1(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Billing_BillRefundTransactionV1_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BillingServer).BillRefundTransactionV1(ctx, req.(*BillRefundTransactionRequestV1))
	}
	return interceptor(ctx, in, info, handler)
}

func _Billing_CheckOutTransferBalanceV1_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CheckOutTransferBalanceRequestV1)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BillingServer).CheckOutTransferBalanceV1(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Billing_CheckOutTransferBalanceV1_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BillingServer).CheckOutTransferBalanceV1(ctx, req.(*CheckOutTransferBalanceRequestV1))
	}
	return interceptor(ctx, in, info, handler)
}

func _Billing_BillOutTransferV1_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BillOutTransferRequestV1)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BillingServer).BillOutTransferV1(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Billing_BillOutTransferV1_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BillingServer).BillOutTransferV1(ctx, req.(*BillOutTransferRequestV1))
	}
	return interceptor(ctx, in, info, handler)
}

func _Billing_BillInTransferV1_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BillInTransferRequestV1)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BillingServer).BillInTransferV1(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Billing_BillInTransferV1_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BillingServer).BillInTransferV1(ctx, req.(*BillInTransferRequestV1))
	}
	return interceptor(ctx, in, info, handler)
}

func _Billing_GetMerchantByBalanceOwnerIDV1_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetMerchantByBalanceOwnerRequestV1)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BillingServer).GetMerchantByBalanceOwnerIDV1(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Billing_GetMerchantByBalanceOwnerIDV1_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BillingServer).GetMerchantByBalanceOwnerIDV1(ctx, req.(*GetMerchantByBalanceOwnerRequestV1))
	}
	return interceptor(ctx, in, info, handler)
}

func _Billing_SetInTransferV1_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SetInTransferRequestV1)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BillingServer).SetInTransferV1(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Billing_SetInTransferV1_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BillingServer).SetInTransferV1(ctx, req.(*SetInTransferRequestV1))
	}
	return interceptor(ctx, in, info, handler)
}

func _Billing_BillSplitTransferV1_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BillSplitTransferRequestV1)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BillingServer).BillSplitTransferV1(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Billing_BillSplitTransferV1_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BillingServer).BillSplitTransferV1(ctx, req.(*BillSplitTransferRequestV1))
	}
	return interceptor(ctx, in, info, handler)
}

func _Billing_GetBalanceOwnerV1_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetBalanceOwnerRequestV1)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BillingServer).GetBalanceOwnerV1(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Billing_GetBalanceOwnerV1_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BillingServer).GetBalanceOwnerV1(ctx, req.(*GetBalanceOwnerRequestV1))
	}
	return interceptor(ctx, in, info, handler)
}

func _Billing_SetBalanceOwnerSplittableV1_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SetBalanceOwnerSplittableRequestV1)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BillingServer).SetBalanceOwnerSplittableV1(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Billing_SetBalanceOwnerSplittableV1_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BillingServer).SetBalanceOwnerSplittableV1(ctx, req.(*SetBalanceOwnerSplittableRequestV1))
	}
	return interceptor(ctx, in, info, handler)
}

func _Billing_GetBalanceOwnerByIDV1_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetBalanceOwnerByIDRequestV1)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BillingServer).GetBalanceOwnerByIDV1(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Billing_GetBalanceOwnerByIDV1_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BillingServer).GetBalanceOwnerByIDV1(ctx, req.(*GetBalanceOwnerByIDRequestV1))
	}
	return interceptor(ctx, in, info, handler)
}

func _Billing_GetEntityTypeByIDV1_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetEntityTypeByIDRequestV1)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BillingServer).GetEntityTypeByIDV1(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Billing_GetEntityTypeByIDV1_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BillingServer).GetEntityTypeByIDV1(ctx, req.(*GetEntityTypeByIDRequestV1))
	}
	return interceptor(ctx, in, info, handler)
}

func _Billing_GetCountryCodeByIDV1_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetCountryCodeByIDRequestV1)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BillingServer).GetCountryCodeByIDV1(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Billing_GetCountryCodeByIDV1_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BillingServer).GetCountryCodeByIDV1(ctx, req.(*GetCountryCodeByIDRequestV1))
	}
	return interceptor(ctx, in, info, handler)
}

func _Billing_GetBalanceByIDV1_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetBalanceByIDRequestV1)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BillingServer).GetBalanceByIDV1(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Billing_GetBalanceByIDV1_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BillingServer).GetBalanceByIDV1(ctx, req.(*GetBalanceByIDRequestV1))
	}
	return interceptor(ctx, in, info, handler)
}

func _Billing_CheckHasBalanceV1_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CheckHasBalanceRequestV1)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BillingServer).CheckHasBalanceV1(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Billing_CheckHasBalanceV1_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BillingServer).CheckHasBalanceV1(ctx, req.(*CheckHasBalanceRequestV1))
	}
	return interceptor(ctx, in, info, handler)
}

func _Billing_CheckBalanceOwnerAccountExistsByBalanceOwnerIdAndAccountIdV1_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CheckBalanceOwnerAccountExistsByBalanceOwnerIdAndAccountIdRequestV1)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BillingServer).CheckBalanceOwnerAccountExistsByBalanceOwnerIdAndAccountIdV1(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Billing_CheckBalanceOwnerAccountExistsByBalanceOwnerIdAndAccountIdV1_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BillingServer).CheckBalanceOwnerAccountExistsByBalanceOwnerIdAndAccountIdV1(ctx, req.(*CheckBalanceOwnerAccountExistsByBalanceOwnerIdAndAccountIdRequestV1))
	}
	return interceptor(ctx, in, info, handler)
}

func _Billing_GetBalanceAccountByNumber_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetBalanceAccountByNumberRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BillingServer).GetBalanceAccountByNumber(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Billing_GetBalanceAccountByNumber_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BillingServer).GetBalanceAccountByNumber(ctx, req.(*GetBalanceAccountByNumberRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Billing_GetCurrentBalanceAmountByBalanceOwnerID_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetCurrentBalanceAmountByAccountAndOwnerIDRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BillingServer).GetCurrentBalanceAmountByBalanceOwnerID(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Billing_GetCurrentBalanceAmountByBalanceOwnerID_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BillingServer).GetCurrentBalanceAmountByBalanceOwnerID(ctx, req.(*GetCurrentBalanceAmountByAccountAndOwnerIDRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Billing_CheckBalanceCreditExpireDate_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(emptypb.Empty)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BillingServer).CheckBalanceCreditExpireDate(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Billing_CheckBalanceCreditExpireDate_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BillingServer).CheckBalanceCreditExpireDate(ctx, req.(*emptypb.Empty))
	}
	return interceptor(ctx, in, info, handler)
}

func _Billing_CheckBalanceCreditStartDate_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(emptypb.Empty)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BillingServer).CheckBalanceCreditStartDate(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Billing_CheckBalanceCreditStartDate_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BillingServer).CheckBalanceCreditStartDate(ctx, req.(*emptypb.Empty))
	}
	return interceptor(ctx, in, info, handler)
}

func _Billing_RecalculateProvisionalBalances_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(emptypb.Empty)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BillingServer).RecalculateProvisionalBalances(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Billing_RecalculateProvisionalBalances_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BillingServer).RecalculateProvisionalBalances(ctx, req.(*emptypb.Empty))
	}
	return interceptor(ctx, in, info, handler)
}

func _Billing_RecalculateFinalBalances_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(emptypb.Empty)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BillingServer).RecalculateFinalBalances(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Billing_RecalculateFinalBalances_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BillingServer).RecalculateFinalBalances(ctx, req.(*emptypb.Empty))
	}
	return interceptor(ctx, in, info, handler)
}

func _Billing_RecalculateCreditBalances_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(emptypb.Empty)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BillingServer).RecalculateCreditBalances(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Billing_RecalculateCreditBalances_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BillingServer).RecalculateCreditBalances(ctx, req.(*emptypb.Empty))
	}
	return interceptor(ctx, in, info, handler)
}

// Billing_ServiceDesc is the grpc.ServiceDesc for Billing service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var Billing_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "processing.billing.billing.Billing",
	HandlerType: (*BillingServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "CheckPayOutBalanceV1",
			Handler:    _Billing_CheckPayOutBalanceV1_Handler,
		},
		{
			MethodName: "BillPayInTransactionV1",
			Handler:    _Billing_BillPayInTransactionV1_Handler,
		},
		{
			MethodName: "BillPayOutTransactionV1",
			Handler:    _Billing_BillPayOutTransactionV1_Handler,
		},
		{
			MethodName: "BillRefundTransactionV1",
			Handler:    _Billing_BillRefundTransactionV1_Handler,
		},
		{
			MethodName: "CheckOutTransferBalanceV1",
			Handler:    _Billing_CheckOutTransferBalanceV1_Handler,
		},
		{
			MethodName: "BillOutTransferV1",
			Handler:    _Billing_BillOutTransferV1_Handler,
		},
		{
			MethodName: "BillInTransferV1",
			Handler:    _Billing_BillInTransferV1_Handler,
		},
		{
			MethodName: "GetMerchantByBalanceOwnerIDV1",
			Handler:    _Billing_GetMerchantByBalanceOwnerIDV1_Handler,
		},
		{
			MethodName: "SetInTransferV1",
			Handler:    _Billing_SetInTransferV1_Handler,
		},
		{
			MethodName: "BillSplitTransferV1",
			Handler:    _Billing_BillSplitTransferV1_Handler,
		},
		{
			MethodName: "GetBalanceOwnerV1",
			Handler:    _Billing_GetBalanceOwnerV1_Handler,
		},
		{
			MethodName: "SetBalanceOwnerSplittableV1",
			Handler:    _Billing_SetBalanceOwnerSplittableV1_Handler,
		},
		{
			MethodName: "GetBalanceOwnerByIDV1",
			Handler:    _Billing_GetBalanceOwnerByIDV1_Handler,
		},
		{
			MethodName: "GetEntityTypeByIDV1",
			Handler:    _Billing_GetEntityTypeByIDV1_Handler,
		},
		{
			MethodName: "GetCountryCodeByIDV1",
			Handler:    _Billing_GetCountryCodeByIDV1_Handler,
		},
		{
			MethodName: "GetBalanceByIDV1",
			Handler:    _Billing_GetBalanceByIDV1_Handler,
		},
		{
			MethodName: "CheckHasBalanceV1",
			Handler:    _Billing_CheckHasBalanceV1_Handler,
		},
		{
			MethodName: "CheckBalanceOwnerAccountExistsByBalanceOwnerIdAndAccountIdV1",
			Handler:    _Billing_CheckBalanceOwnerAccountExistsByBalanceOwnerIdAndAccountIdV1_Handler,
		},
		{
			MethodName: "GetBalanceAccountByNumber",
			Handler:    _Billing_GetBalanceAccountByNumber_Handler,
		},
		{
			MethodName: "GetCurrentBalanceAmountByBalanceOwnerID",
			Handler:    _Billing_GetCurrentBalanceAmountByBalanceOwnerID_Handler,
		},
		{
			MethodName: "CheckBalanceCreditExpireDate",
			Handler:    _Billing_CheckBalanceCreditExpireDate_Handler,
		},
		{
			MethodName: "CheckBalanceCreditStartDate",
			Handler:    _Billing_CheckBalanceCreditStartDate_Handler,
		},
		{
			MethodName: "RecalculateProvisionalBalances",
			Handler:    _Billing_RecalculateProvisionalBalances_Handler,
		},
		{
			MethodName: "RecalculateFinalBalances",
			Handler:    _Billing_RecalculateFinalBalances_Handler,
		},
		{
			MethodName: "RecalculateCreditBalances",
			Handler:    _Billing_RecalculateCreditBalances_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "inner/processing/grpc/billing.proto",
}
