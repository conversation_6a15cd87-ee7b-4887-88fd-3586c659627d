package acquirer

import (
	"net"
	"time"

	"github.com/getsentry/sentry-go"
	"github.com/gin-gonic/gin"
	"github.com/spf13/cobra"
	"go.uber.org/zap"
	"google.golang.org/grpc/reflection"

	"git.local/sensitive/innerpb/processing/grpc"
	"git.local/sensitive/pkg/gtransaction"
	_ "git.local/sensitive/processing/account/docs"
	grpcDeliveryV1 "git.local/sensitive/processing/acquirer/delivery/grpc/v1"
	httpDelivery "git.local/sensitive/processing/acquirer/delivery/http"
	"git.local/sensitive/processing/acquirer/docs"
	"git.local/sensitive/processing/acquirer/repository"
	"git.local/sensitive/processing/acquirer/service"
	"git.local/sensitive/sdk/dog"
)

var CMD = &cobra.Command{
	Use: "acquirer",
	Run: run,
}

const (
	sentryFlushTimeout = 5 * time.Second
)

//go:generate swag init -g ./../cobra/acquirer/service.go -d ./../../acquirer --parseDependencyLevel 1 --instanceName Acquirer --output ./../../acquirer/docs

// @title acquirer
// @version 1.0.0
// @description acquirer processing
//
// @host api-dev.processing.kz
// @BasePath /acquirer
// @schemes https http
// @securityDefinitions.apikey bearerAuth
// @in header
// @name Authorization
func run(cmd *cobra.Command, args []string) {
	defer func() {
		if err, hub := recover(), sentry.CurrentHub(); err != nil && hub != nil {
			hub.Recover(err)
			sentry.Flush(sentryFlushTimeout)
			panic(err)
		}
	}()

	gormDB := dog.InitApp("processing.acquirer.acquirer.Acquirer")

	logger := dog.L()

	logger.Info("begin register db healthCheck")
	dog.RegisterDBHealthCheck()
	logger.Info("end register db healthCheck")

	logger.Info("begin send swagger doc")
	err := dog.InitOpenAPI(docs.SwaggerInfoAcquirer)
	if err != nil {
		logger.Warn("error send swagger doc", zap.Error(err))
	}
	logger.Info("end send swagger doc")

	logger.Info("begin connect to multi-acquiring balance service")
	multiacquiringBalanceClient, err := grpc.NewPreparedMultiacquiringBalanceClient()
	if err != nil {
		logger.Panic("error connect to multi-acquiring balance service", zap.Error(err))
	}
	logger.Info("end connect to multi-acquiring balance service")
	grpc.NewLoggedMultiacquiringBalanceClient(multiacquiringBalanceClient)

	logger.Info("begin connect to billing service")
	billingClient, err := grpc.NewPreparedBillingClient()
	if err != nil {
		logger.Panic("error connect to billing service", zap.Error(err))
	}
	logger.Info("end connect to billing service")
	grpc.NewLoggedBillingClient(billingClient)

	logger.Info("begin connect to transaction-type service")
	transactionTypeClient, err := grpc.NewPreparedTransactionTypeClient()
	if err != nil {
		logger.Panic("error connect to transaction-type service", zap.Error(err))
	}
	logger.Info("end connect to transaction-type service")
	grpc.NewLoggedTransactionTypeClient(transactionTypeClient)

	//TODO add jetStream producer
	repo := repository.NewRepositories(
		gormDB,
	)

	services := service.NewServices(
		gtransaction.NewGormTransactionManager(gormDB),
		repo,
		transactionTypeClient,
		billingClient,
		multiacquiringBalanceClient,
	)

	go func() {
		defer dog.Cancel()()

		serverAddr := dog.ConfigString("HTTP_PORT")
		dog.L().Info("serverAdr", zap.String("HTTP_PORT", serverAddr))

		handlerDelivery := httpDelivery.NewHandlerDelivery(services, dog.MicroService())

		engine := dog.GinEngine()

		handlerDelivery.Init(engine)
		go func() {
			<-time.NewTicker(time.Second).C
			gin.SetMode(gin.ReleaseMode)
		}()

		if err = engine.Run(":" + serverAddr); err != nil {
			logger.Panic("can not run gin engine", zap.Error(err))
		}
	}()

	go func() {
		defer dog.Cancel()()

		grpcServer := dog.GrpcServer()

		grpc.RegisterAcquirerServer(grpcServer, grpc.NewLoggedAcquirerServer(grpcDeliveryV1.NewAcquirerServer(services)))
		grpc.RegisterTerminalCardsServer(grpcServer, grpc.NewLoggedTerminalCardsServer(grpcDeliveryV1.NewTerminalCardsServer(services)))
		grpc.RegisterTerminalServer(grpcServer, grpc.NewLoggedTerminalServer(grpcDeliveryV1.NewTerminalServer(services)))

		serverAddr := dog.ConfigString("GRPC_PORT")
		dog.L().Info("serverAdr", zap.String("GRPC_PORT", serverAddr))

		listenerGrpc, err := net.Listen("tcp", ":"+serverAddr)
		if err != nil {
			dog.L().Fatal("can not prepare net.Listener for grpc service", zap.Error(err))
		}

		reflection.Register(grpcServer)

		if err := grpcServer.Serve(listenerGrpc); err != nil {
			dog.L().Fatal("can not run grpc server", zap.Error(err))
		}
	}()

	<-dog.Ctx().Done()
}
