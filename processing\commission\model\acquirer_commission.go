package model

import (
	"time"

	"git.local/sensitive/innerpb/processing/goerr"
)

// AcquirerCommission Комиссия эквайера
type AcquirerCommission struct {
	TimestampMixin
	ID uint64 `gorm:"primaryKey;column:id" json:"id"`
	// Опция по которой будет производиться расчёт
	AcquirerOptionID uint64 `gorm:"column:acquirer_option_id" json:"acquirer_option_id"`
	// Итоговая минимальная сумма комиссии в тенге
	MinAmountCommission float64 `gorm:"column:min_amount_commission" json:"min_amount_commission"`
	// Процентная ставка
	CommissionPercentage float64 `gorm:"column:commission_percentage" json:"commission_percentage"`
	// Фискированная сумма
	FixedAmount float64 `gorm:"column:fixed_amount" json:"fixed_amount"`
	// Дата начала действия расчёта
	StartDate time.Time `gorm:"column:start_date" json:"start_date"`
	// Дата конца действия расчёта
	EndDate *time.Time `gorm:"column:end_date" json:"end_date"`
}

type AcquirerCommissions []AcquirerCommission

func (a AcquirerCommission) TableName() string {
	return "commission.acquirer_commissions"
}

func (a AcquirerCommissions) FindCommission(transactionCreatedAt time.Time) (AcquirerCommission, error) {
	var (
		res                 AcquirerCommission
		latestStartDateTime time.Time
	)

	if len(a) == 0 {
		return AcquirerCommission{}, goerr.ErrFindAcquirerCommission
	}

	for _, commission := range a {
		if commission.StartDate.After(transactionCreatedAt) {
			return AcquirerCommission{}, goerr.ErrFindAcquirerCommission
		}

		if commission.EndDate == nil {
			return commission, nil
		}

		if commission.StartDate.After(latestStartDateTime) && transactionCreatedAt.Before(*commission.EndDate) {
			latestStartDateTime = commission.StartDate
			res = commission
		}
	}

	if res.ID == 0 {
		return AcquirerCommission{}, goerr.ErrFindAcquirerCommission
	}

	return res, nil
}
