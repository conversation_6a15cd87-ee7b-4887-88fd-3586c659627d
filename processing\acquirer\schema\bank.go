package schema

import (
	"git.local/sensitive/processing/acquirer/model"
	"github.com/go-playground/validator/v10"
)

type Bank struct {
	Name  string `json:"name" validate:"required"`
	Bik   string `json:"bik,omitempty"`
	Swift string `json:"swift,omitempty"`
}

type BankFilter struct {
	Search string `form:"search" json:"search"`
}

func (b Bank) Validate() error {
	return validator.New().Struct(b)
}

func (b Bank) ToModel() *model.Bank {
	return &model.Bank{
		Name:  b.Name,
		Bik:   b.Bik,
		Swift: b.<PERSON>,
	}
}
