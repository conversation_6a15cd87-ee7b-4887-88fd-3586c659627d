// Code generated by MockGen. DO NOT EDIT.
// Source: card_grpc.pb.go

// Package grpcmock is a generated GoMock package.
package grpcmock

import (
	context "context"
	reflect "reflect"

	grpc "git.local/sensitive/innerpb/processing/grpc"
	gomock "github.com/golang/mock/gomock"
	grpc0 "google.golang.org/grpc"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
)

// MockCardClient is a mock of CardClient interface.
type MockCardClient struct {
	ctrl     *gomock.Controller
	recorder *MockCardClientMockRecorder
}

// MockCardClientMockRecorder is the mock recorder for MockCardClient.
type MockCardClientMockRecorder struct {
	mock *MockCardClient
}

// NewMockCardClient creates a new mock instance.
func NewMockCardClient(ctrl *gomock.Controller) *MockCardClient {
	mock := &MockCardClient{ctrl: ctrl}
	mock.recorder = &MockCardClientMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockCardClient) EXPECT() *MockCardClientMockRecorder {
	return m.recorder
}

// CheckClientActiveness mocks base method.
func (m *MockCardClient) CheckClientActiveness(ctx context.Context, in *grpc.CheckClientActivenessRequestV1, opts ...grpc0.CallOption) (*emptypb.Empty, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "CheckClientActiveness", varargs...)
	ret0, _ := ret[0].(*emptypb.Empty)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CheckClientActiveness indicates an expected call of CheckClientActiveness.
func (mr *MockCardClientMockRecorder) CheckClientActiveness(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CheckClientActiveness", reflect.TypeOf((*MockCardClient)(nil).CheckClientActiveness), varargs...)
}

// CheckExpireCards mocks base method.
func (m *MockCardClient) CheckExpireCards(ctx context.Context, in *emptypb.Empty, opts ...grpc0.CallOption) (*emptypb.Empty, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "CheckExpireCards", varargs...)
	ret0, _ := ret[0].(*emptypb.Empty)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CheckExpireCards indicates an expected call of CheckExpireCards.
func (mr *MockCardClientMockRecorder) CheckExpireCards(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CheckExpireCards", reflect.TypeOf((*MockCardClient)(nil).CheckExpireCards), varargs...)
}

// CreateClientV1 mocks base method.
func (m *MockCardClient) CreateClientV1(ctx context.Context, in *grpc.CreateClientRequestV1, opts ...grpc0.CallOption) (*grpc.CreateClientResponseV1, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "CreateClientV1", varargs...)
	ret0, _ := ret[0].(*grpc.CreateClientResponseV1)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateClientV1 indicates an expected call of CreateClientV1.
func (mr *MockCardClientMockRecorder) CreateClientV1(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateClientV1", reflect.TypeOf((*MockCardClient)(nil).CreateClientV1), varargs...)
}

// CreateNewHashKey mocks base method.
func (m *MockCardClient) CreateNewHashKey(ctx context.Context, in *emptypb.Empty, opts ...grpc0.CallOption) (*emptypb.Empty, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "CreateNewHashKey", varargs...)
	ret0, _ := ret[0].(*emptypb.Empty)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateNewHashKey indicates an expected call of CreateNewHashKey.
func (mr *MockCardClientMockRecorder) CreateNewHashKey(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateNewHashKey", reflect.TypeOf((*MockCardClient)(nil).CreateNewHashKey), varargs...)
}

// DecryptPayInCard mocks base method.
func (m *MockCardClient) DecryptPayInCard(ctx context.Context, in *grpc.DecryptPayInRequest, opts ...grpc0.CallOption) (*grpc.DecryptPayInResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "DecryptPayInCard", varargs...)
	ret0, _ := ret[0].(*grpc.DecryptPayInResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DecryptPayInCard indicates an expected call of DecryptPayInCard.
func (mr *MockCardClientMockRecorder) DecryptPayInCard(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DecryptPayInCard", reflect.TypeOf((*MockCardClient)(nil).DecryptPayInCard), varargs...)
}

// DecryptPayOutCard mocks base method.
func (m *MockCardClient) DecryptPayOutCard(ctx context.Context, in *grpc.DecryptPayOutRequest, opts ...grpc0.CallOption) (*grpc.DecryptPayOutResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "DecryptPayOutCard", varargs...)
	ret0, _ := ret[0].(*grpc.DecryptPayOutResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DecryptPayOutCard indicates an expected call of DecryptPayOutCard.
func (mr *MockCardClientMockRecorder) DecryptPayOutCard(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DecryptPayOutCard", reflect.TypeOf((*MockCardClient)(nil).DecryptPayOutCard), varargs...)
}

// GetCardByPan mocks base method.
func (m *MockCardClient) GetCardByPan(ctx context.Context, in *grpc.GetCardByPanRequestV1, opts ...grpc0.CallOption) (*grpc.GetCardByPanResponseV1, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetCardByPan", varargs...)
	ret0, _ := ret[0].(*grpc.GetCardByPanResponseV1)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetCardByPan indicates an expected call of GetCardByPan.
func (mr *MockCardClientMockRecorder) GetCardByPan(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetCardByPan", reflect.TypeOf((*MockCardClient)(nil).GetCardByPan), varargs...)
}

// GetCardTokensV1 mocks base method.
func (m *MockCardClient) GetCardTokensV1(ctx context.Context, in *grpc.GetCardTokensRequestV1, opts ...grpc0.CallOption) (*grpc.GetCardTokensResponseV1, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetCardTokensV1", varargs...)
	ret0, _ := ret[0].(*grpc.GetCardTokensResponseV1)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetCardTokensV1 indicates an expected call of GetCardTokensV1.
func (mr *MockCardClientMockRecorder) GetCardTokensV1(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetCardTokensV1", reflect.TypeOf((*MockCardClient)(nil).GetCardTokensV1), varargs...)
}

// GetClientListByProjectClient mocks base method.
func (m *MockCardClient) GetClientListByProjectClient(ctx context.Context, in *grpc.GetClientListByProjectClientRequestV1, opts ...grpc0.CallOption) (*grpc.GetClientListByProjectClientResponseV1, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetClientListByProjectClient", varargs...)
	ret0, _ := ret[0].(*grpc.GetClientListByProjectClientResponseV1)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetClientListByProjectClient indicates an expected call of GetClientListByProjectClient.
func (mr *MockCardClientMockRecorder) GetClientListByProjectClient(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetClientListByProjectClient", reflect.TypeOf((*MockCardClient)(nil).GetClientListByProjectClient), varargs...)
}

// GetClientListByVerification mocks base method.
func (m *MockCardClient) GetClientListByVerification(ctx context.Context, in *grpc.GetClientListByVerificationRequestV1, opts ...grpc0.CallOption) (*grpc.GetClientListByProjectClientResponseV1, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetClientListByVerification", varargs...)
	ret0, _ := ret[0].(*grpc.GetClientListByProjectClientResponseV1)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetClientListByVerification indicates an expected call of GetClientListByVerification.
func (mr *MockCardClientMockRecorder) GetClientListByVerification(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetClientListByVerification", reflect.TypeOf((*MockCardClient)(nil).GetClientListByVerification), varargs...)
}

// GetEncryptedCardToken mocks base method.
func (m *MockCardClient) GetEncryptedCardToken(ctx context.Context, in *grpc.GetEncryptedCardRequestV1, opts ...grpc0.CallOption) (*grpc.GetEncryptedCardResponseV1, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetEncryptedCardToken", varargs...)
	ret0, _ := ret[0].(*grpc.GetEncryptedCardResponseV1)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetEncryptedCardToken indicates an expected call of GetEncryptedCardToken.
func (mr *MockCardClientMockRecorder) GetEncryptedCardToken(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetEncryptedCardToken", reflect.TypeOf((*MockCardClient)(nil).GetEncryptedCardToken), varargs...)
}

// GetOneClickPayInCardsV1 mocks base method.
func (m *MockCardClient) GetOneClickPayInCardsV1(ctx context.Context, in *grpc.GetOneClickPayInCardsRequestV1, opts ...grpc0.CallOption) (*grpc.GetOneClickPayInCardsResponseV1, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetOneClickPayInCardsV1", varargs...)
	ret0, _ := ret[0].(*grpc.GetOneClickPayInCardsResponseV1)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetOneClickPayInCardsV1 indicates an expected call of GetOneClickPayInCardsV1.
func (mr *MockCardClientMockRecorder) GetOneClickPayInCardsV1(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetOneClickPayInCardsV1", reflect.TypeOf((*MockCardClient)(nil).GetOneClickPayInCardsV1), varargs...)
}

// GetOneClickPayOutCardsV1 mocks base method.
func (m *MockCardClient) GetOneClickPayOutCardsV1(ctx context.Context, in *grpc.GetOneClickPayOutCardsRequestV1, opts ...grpc0.CallOption) (*grpc.GetOneClickPayOutCardsResponseV1, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetOneClickPayOutCardsV1", varargs...)
	ret0, _ := ret[0].(*grpc.GetOneClickPayOutCardsResponseV1)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetOneClickPayOutCardsV1 indicates an expected call of GetOneClickPayOutCardsV1.
func (mr *MockCardClientMockRecorder) GetOneClickPayOutCardsV1(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetOneClickPayOutCardsV1", reflect.TypeOf((*MockCardClient)(nil).GetOneClickPayOutCardsV1), varargs...)
}

// GetPanByCardIdV1 mocks base method.
func (m *MockCardClient) GetPanByCardIdV1(ctx context.Context, in *grpc.GetPanByCardIdRequestV1, opts ...grpc0.CallOption) (*grpc.GetPanResponseV1, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetPanByCardIdV1", varargs...)
	ret0, _ := ret[0].(*grpc.GetPanResponseV1)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetPanByCardIdV1 indicates an expected call of GetPanByCardIdV1.
func (mr *MockCardClientMockRecorder) GetPanByCardIdV1(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetPanByCardIdV1", reflect.TypeOf((*MockCardClient)(nil).GetPanByCardIdV1), varargs...)
}

// GetPanByHashedIdV1 mocks base method.
func (m *MockCardClient) GetPanByHashedIdV1(ctx context.Context, in *grpc.GetPanByHashedIdRequestV1, opts ...grpc0.CallOption) (*grpc.GetPanResponseV1, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetPanByHashedIdV1", varargs...)
	ret0, _ := ret[0].(*grpc.GetPanResponseV1)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetPanByHashedIdV1 indicates an expected call of GetPanByHashedIdV1.
func (mr *MockCardClientMockRecorder) GetPanByHashedIdV1(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetPanByHashedIdV1", reflect.TypeOf((*MockCardClient)(nil).GetPanByHashedIdV1), varargs...)
}

// GetPanInfoByProjectId mocks base method.
func (m *MockCardClient) GetPanInfoByProjectId(ctx context.Context, in *grpc.GetPanInfoByProjectIdRequest, opts ...grpc0.CallOption) (*grpc.GetPanInfoByProjectIdResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetPanInfoByProjectId", varargs...)
	ret0, _ := ret[0].(*grpc.GetPanInfoByProjectIdResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetPanInfoByProjectId indicates an expected call of GetPanInfoByProjectId.
func (mr *MockCardClientMockRecorder) GetPanInfoByProjectId(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetPanInfoByProjectId", reflect.TypeOf((*MockCardClient)(nil).GetPanInfoByProjectId), varargs...)
}

// NewKey mocks base method.
func (m *MockCardClient) NewKey(ctx context.Context, in *emptypb.Empty, opts ...grpc0.CallOption) (*emptypb.Empty, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "NewKey", varargs...)
	ret0, _ := ret[0].(*emptypb.Empty)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// NewKey indicates an expected call of NewKey.
func (mr *MockCardClientMockRecorder) NewKey(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "NewKey", reflect.TypeOf((*MockCardClient)(nil).NewKey), varargs...)
}

// ReEncryptCard mocks base method.
func (m *MockCardClient) ReEncryptCard(ctx context.Context, in *grpc.ReEncryptCardRequest, opts ...grpc0.CallOption) (*grpc.ReEncryptCardResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "ReEncryptCard", varargs...)
	ret0, _ := ret[0].(*grpc.ReEncryptCardResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ReEncryptCard indicates an expected call of ReEncryptCard.
func (mr *MockCardClientMockRecorder) ReEncryptCard(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ReEncryptCard", reflect.TypeOf((*MockCardClient)(nil).ReEncryptCard), varargs...)
}

// RotateCardKeys mocks base method.
func (m *MockCardClient) RotateCardKeys(ctx context.Context, in *emptypb.Empty, opts ...grpc0.CallOption) (*emptypb.Empty, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "RotateCardKeys", varargs...)
	ret0, _ := ret[0].(*emptypb.Empty)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// RotateCardKeys indicates an expected call of RotateCardKeys.
func (mr *MockCardClientMockRecorder) RotateCardKeys(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "RotateCardKeys", reflect.TypeOf((*MockCardClient)(nil).RotateCardKeys), varargs...)
}

// RotateHashKeys mocks base method.
func (m *MockCardClient) RotateHashKeys(ctx context.Context, in *emptypb.Empty, opts ...grpc0.CallOption) (*emptypb.Empty, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "RotateHashKeys", varargs...)
	ret0, _ := ret[0].(*emptypb.Empty)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// RotateHashKeys indicates an expected call of RotateHashKeys.
func (mr *MockCardClientMockRecorder) RotateHashKeys(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "RotateHashKeys", reflect.TypeOf((*MockCardClient)(nil).RotateHashKeys), varargs...)
}

// MockCardServer is a mock of CardServer interface.
type MockCardServer struct {
	ctrl     *gomock.Controller
	recorder *MockCardServerMockRecorder
}

// MockCardServerMockRecorder is the mock recorder for MockCardServer.
type MockCardServerMockRecorder struct {
	mock *MockCardServer
}

// NewMockCardServer creates a new mock instance.
func NewMockCardServer(ctrl *gomock.Controller) *MockCardServer {
	mock := &MockCardServer{ctrl: ctrl}
	mock.recorder = &MockCardServerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockCardServer) EXPECT() *MockCardServerMockRecorder {
	return m.recorder
}

// CheckClientActiveness mocks base method.
func (m *MockCardServer) CheckClientActiveness(arg0 context.Context, arg1 *grpc.CheckClientActivenessRequestV1) (*emptypb.Empty, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CheckClientActiveness", arg0, arg1)
	ret0, _ := ret[0].(*emptypb.Empty)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CheckClientActiveness indicates an expected call of CheckClientActiveness.
func (mr *MockCardServerMockRecorder) CheckClientActiveness(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CheckClientActiveness", reflect.TypeOf((*MockCardServer)(nil).CheckClientActiveness), arg0, arg1)
}

// CheckExpireCards mocks base method.
func (m *MockCardServer) CheckExpireCards(arg0 context.Context, arg1 *emptypb.Empty) (*emptypb.Empty, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CheckExpireCards", arg0, arg1)
	ret0, _ := ret[0].(*emptypb.Empty)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CheckExpireCards indicates an expected call of CheckExpireCards.
func (mr *MockCardServerMockRecorder) CheckExpireCards(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CheckExpireCards", reflect.TypeOf((*MockCardServer)(nil).CheckExpireCards), arg0, arg1)
}

// CreateClientV1 mocks base method.
func (m *MockCardServer) CreateClientV1(arg0 context.Context, arg1 *grpc.CreateClientRequestV1) (*grpc.CreateClientResponseV1, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateClientV1", arg0, arg1)
	ret0, _ := ret[0].(*grpc.CreateClientResponseV1)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateClientV1 indicates an expected call of CreateClientV1.
func (mr *MockCardServerMockRecorder) CreateClientV1(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateClientV1", reflect.TypeOf((*MockCardServer)(nil).CreateClientV1), arg0, arg1)
}

// CreateNewHashKey mocks base method.
func (m *MockCardServer) CreateNewHashKey(arg0 context.Context, arg1 *emptypb.Empty) (*emptypb.Empty, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateNewHashKey", arg0, arg1)
	ret0, _ := ret[0].(*emptypb.Empty)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateNewHashKey indicates an expected call of CreateNewHashKey.
func (mr *MockCardServerMockRecorder) CreateNewHashKey(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateNewHashKey", reflect.TypeOf((*MockCardServer)(nil).CreateNewHashKey), arg0, arg1)
}

// DecryptPayInCard mocks base method.
func (m *MockCardServer) DecryptPayInCard(arg0 context.Context, arg1 *grpc.DecryptPayInRequest) (*grpc.DecryptPayInResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DecryptPayInCard", arg0, arg1)
	ret0, _ := ret[0].(*grpc.DecryptPayInResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DecryptPayInCard indicates an expected call of DecryptPayInCard.
func (mr *MockCardServerMockRecorder) DecryptPayInCard(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DecryptPayInCard", reflect.TypeOf((*MockCardServer)(nil).DecryptPayInCard), arg0, arg1)
}

// DecryptPayOutCard mocks base method.
func (m *MockCardServer) DecryptPayOutCard(arg0 context.Context, arg1 *grpc.DecryptPayOutRequest) (*grpc.DecryptPayOutResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DecryptPayOutCard", arg0, arg1)
	ret0, _ := ret[0].(*grpc.DecryptPayOutResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DecryptPayOutCard indicates an expected call of DecryptPayOutCard.
func (mr *MockCardServerMockRecorder) DecryptPayOutCard(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DecryptPayOutCard", reflect.TypeOf((*MockCardServer)(nil).DecryptPayOutCard), arg0, arg1)
}

// GetCardByPan mocks base method.
func (m *MockCardServer) GetCardByPan(arg0 context.Context, arg1 *grpc.GetCardByPanRequestV1) (*grpc.GetCardByPanResponseV1, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetCardByPan", arg0, arg1)
	ret0, _ := ret[0].(*grpc.GetCardByPanResponseV1)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetCardByPan indicates an expected call of GetCardByPan.
func (mr *MockCardServerMockRecorder) GetCardByPan(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetCardByPan", reflect.TypeOf((*MockCardServer)(nil).GetCardByPan), arg0, arg1)
}

// GetCardTokensV1 mocks base method.
func (m *MockCardServer) GetCardTokensV1(arg0 context.Context, arg1 *grpc.GetCardTokensRequestV1) (*grpc.GetCardTokensResponseV1, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetCardTokensV1", arg0, arg1)
	ret0, _ := ret[0].(*grpc.GetCardTokensResponseV1)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetCardTokensV1 indicates an expected call of GetCardTokensV1.
func (mr *MockCardServerMockRecorder) GetCardTokensV1(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetCardTokensV1", reflect.TypeOf((*MockCardServer)(nil).GetCardTokensV1), arg0, arg1)
}

// GetClientListByProjectClient mocks base method.
func (m *MockCardServer) GetClientListByProjectClient(arg0 context.Context, arg1 *grpc.GetClientListByProjectClientRequestV1) (*grpc.GetClientListByProjectClientResponseV1, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetClientListByProjectClient", arg0, arg1)
	ret0, _ := ret[0].(*grpc.GetClientListByProjectClientResponseV1)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetClientListByProjectClient indicates an expected call of GetClientListByProjectClient.
func (mr *MockCardServerMockRecorder) GetClientListByProjectClient(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetClientListByProjectClient", reflect.TypeOf((*MockCardServer)(nil).GetClientListByProjectClient), arg0, arg1)
}

// GetClientListByVerification mocks base method.
func (m *MockCardServer) GetClientListByVerification(arg0 context.Context, arg1 *grpc.GetClientListByVerificationRequestV1) (*grpc.GetClientListByProjectClientResponseV1, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetClientListByVerification", arg0, arg1)
	ret0, _ := ret[0].(*grpc.GetClientListByProjectClientResponseV1)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetClientListByVerification indicates an expected call of GetClientListByVerification.
func (mr *MockCardServerMockRecorder) GetClientListByVerification(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetClientListByVerification", reflect.TypeOf((*MockCardServer)(nil).GetClientListByVerification), arg0, arg1)
}

// GetEncryptedCardToken mocks base method.
func (m *MockCardServer) GetEncryptedCardToken(arg0 context.Context, arg1 *grpc.GetEncryptedCardRequestV1) (*grpc.GetEncryptedCardResponseV1, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetEncryptedCardToken", arg0, arg1)
	ret0, _ := ret[0].(*grpc.GetEncryptedCardResponseV1)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetEncryptedCardToken indicates an expected call of GetEncryptedCardToken.
func (mr *MockCardServerMockRecorder) GetEncryptedCardToken(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetEncryptedCardToken", reflect.TypeOf((*MockCardServer)(nil).GetEncryptedCardToken), arg0, arg1)
}

// GetOneClickPayInCardsV1 mocks base method.
func (m *MockCardServer) GetOneClickPayInCardsV1(arg0 context.Context, arg1 *grpc.GetOneClickPayInCardsRequestV1) (*grpc.GetOneClickPayInCardsResponseV1, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetOneClickPayInCardsV1", arg0, arg1)
	ret0, _ := ret[0].(*grpc.GetOneClickPayInCardsResponseV1)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetOneClickPayInCardsV1 indicates an expected call of GetOneClickPayInCardsV1.
func (mr *MockCardServerMockRecorder) GetOneClickPayInCardsV1(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetOneClickPayInCardsV1", reflect.TypeOf((*MockCardServer)(nil).GetOneClickPayInCardsV1), arg0, arg1)
}

// GetOneClickPayOutCardsV1 mocks base method.
func (m *MockCardServer) GetOneClickPayOutCardsV1(arg0 context.Context, arg1 *grpc.GetOneClickPayOutCardsRequestV1) (*grpc.GetOneClickPayOutCardsResponseV1, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetOneClickPayOutCardsV1", arg0, arg1)
	ret0, _ := ret[0].(*grpc.GetOneClickPayOutCardsResponseV1)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetOneClickPayOutCardsV1 indicates an expected call of GetOneClickPayOutCardsV1.
func (mr *MockCardServerMockRecorder) GetOneClickPayOutCardsV1(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetOneClickPayOutCardsV1", reflect.TypeOf((*MockCardServer)(nil).GetOneClickPayOutCardsV1), arg0, arg1)
}

// GetPanByCardIdV1 mocks base method.
func (m *MockCardServer) GetPanByCardIdV1(arg0 context.Context, arg1 *grpc.GetPanByCardIdRequestV1) (*grpc.GetPanResponseV1, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetPanByCardIdV1", arg0, arg1)
	ret0, _ := ret[0].(*grpc.GetPanResponseV1)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetPanByCardIdV1 indicates an expected call of GetPanByCardIdV1.
func (mr *MockCardServerMockRecorder) GetPanByCardIdV1(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetPanByCardIdV1", reflect.TypeOf((*MockCardServer)(nil).GetPanByCardIdV1), arg0, arg1)
}

// GetPanByHashedIdV1 mocks base method.
func (m *MockCardServer) GetPanByHashedIdV1(arg0 context.Context, arg1 *grpc.GetPanByHashedIdRequestV1) (*grpc.GetPanResponseV1, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetPanByHashedIdV1", arg0, arg1)
	ret0, _ := ret[0].(*grpc.GetPanResponseV1)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetPanByHashedIdV1 indicates an expected call of GetPanByHashedIdV1.
func (mr *MockCardServerMockRecorder) GetPanByHashedIdV1(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetPanByHashedIdV1", reflect.TypeOf((*MockCardServer)(nil).GetPanByHashedIdV1), arg0, arg1)
}

// GetPanInfoByProjectId mocks base method.
func (m *MockCardServer) GetPanInfoByProjectId(arg0 context.Context, arg1 *grpc.GetPanInfoByProjectIdRequest) (*grpc.GetPanInfoByProjectIdResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetPanInfoByProjectId", arg0, arg1)
	ret0, _ := ret[0].(*grpc.GetPanInfoByProjectIdResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetPanInfoByProjectId indicates an expected call of GetPanInfoByProjectId.
func (mr *MockCardServerMockRecorder) GetPanInfoByProjectId(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetPanInfoByProjectId", reflect.TypeOf((*MockCardServer)(nil).GetPanInfoByProjectId), arg0, arg1)
}

// NewKey mocks base method.
func (m *MockCardServer) NewKey(arg0 context.Context, arg1 *emptypb.Empty) (*emptypb.Empty, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "NewKey", arg0, arg1)
	ret0, _ := ret[0].(*emptypb.Empty)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// NewKey indicates an expected call of NewKey.
func (mr *MockCardServerMockRecorder) NewKey(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "NewKey", reflect.TypeOf((*MockCardServer)(nil).NewKey), arg0, arg1)
}

// ReEncryptCard mocks base method.
func (m *MockCardServer) ReEncryptCard(arg0 context.Context, arg1 *grpc.ReEncryptCardRequest) (*grpc.ReEncryptCardResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ReEncryptCard", arg0, arg1)
	ret0, _ := ret[0].(*grpc.ReEncryptCardResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ReEncryptCard indicates an expected call of ReEncryptCard.
func (mr *MockCardServerMockRecorder) ReEncryptCard(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ReEncryptCard", reflect.TypeOf((*MockCardServer)(nil).ReEncryptCard), arg0, arg1)
}

// RotateCardKeys mocks base method.
func (m *MockCardServer) RotateCardKeys(arg0 context.Context, arg1 *emptypb.Empty) (*emptypb.Empty, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "RotateCardKeys", arg0, arg1)
	ret0, _ := ret[0].(*emptypb.Empty)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// RotateCardKeys indicates an expected call of RotateCardKeys.
func (mr *MockCardServerMockRecorder) RotateCardKeys(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "RotateCardKeys", reflect.TypeOf((*MockCardServer)(nil).RotateCardKeys), arg0, arg1)
}

// RotateHashKeys mocks base method.
func (m *MockCardServer) RotateHashKeys(arg0 context.Context, arg1 *emptypb.Empty) (*emptypb.Empty, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "RotateHashKeys", arg0, arg1)
	ret0, _ := ret[0].(*emptypb.Empty)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// RotateHashKeys indicates an expected call of RotateHashKeys.
func (mr *MockCardServerMockRecorder) RotateHashKeys(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "RotateHashKeys", reflect.TypeOf((*MockCardServer)(nil).RotateHashKeys), arg0, arg1)
}

// mustEmbedUnimplementedCardServer mocks base method.
func (m *MockCardServer) mustEmbedUnimplementedCardServer() {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "mustEmbedUnimplementedCardServer")
}

// mustEmbedUnimplementedCardServer indicates an expected call of mustEmbedUnimplementedCardServer.
func (mr *MockCardServerMockRecorder) mustEmbedUnimplementedCardServer() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "mustEmbedUnimplementedCardServer", reflect.TypeOf((*MockCardServer)(nil).mustEmbedUnimplementedCardServer))
}

// MockUnsafeCardServer is a mock of UnsafeCardServer interface.
type MockUnsafeCardServer struct {
	ctrl     *gomock.Controller
	recorder *MockUnsafeCardServerMockRecorder
}

// MockUnsafeCardServerMockRecorder is the mock recorder for MockUnsafeCardServer.
type MockUnsafeCardServerMockRecorder struct {
	mock *MockUnsafeCardServer
}

// NewMockUnsafeCardServer creates a new mock instance.
func NewMockUnsafeCardServer(ctrl *gomock.Controller) *MockUnsafeCardServer {
	mock := &MockUnsafeCardServer{ctrl: ctrl}
	mock.recorder = &MockUnsafeCardServerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockUnsafeCardServer) EXPECT() *MockUnsafeCardServerMockRecorder {
	return m.recorder
}

// mustEmbedUnimplementedCardServer mocks base method.
func (m *MockUnsafeCardServer) mustEmbedUnimplementedCardServer() {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "mustEmbedUnimplementedCardServer")
}

// mustEmbedUnimplementedCardServer indicates an expected call of mustEmbedUnimplementedCardServer.
func (mr *MockUnsafeCardServerMockRecorder) mustEmbedUnimplementedCardServer() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "mustEmbedUnimplementedCardServer", reflect.TypeOf((*MockUnsafeCardServer)(nil).mustEmbedUnimplementedCardServer))
}
