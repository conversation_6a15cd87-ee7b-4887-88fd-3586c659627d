// Code generated by protoc-gen-go-mvp. DO NOT EDIT.
// versions:
// - protoc-gen-go-mvp v2.0.1
// created by mvp authors

package grpc

import (
	context "context"
	grpc "google.golang.org/grpc"
	metadata "google.golang.org/grpc/metadata"
)

func NewIamSmartPayServer(
	srv SmartPayServer,
) SmartPayServer {
	return &iamSmartPayServer{
		srv: srv,
	}
}

var _ SmartPayServer = (*iamSmartPayServer)(nil)

type iamSmartPayServer struct {
	UnimplementedSmartPayServer

	srv SmartPayServer
}

func (s *iamSmartPayServer) ApplePaySession(
	ctx context.Context,
	req *ApplePaySessionRequestV1,
) (
	*ApplePaySessionResponseV1,
	error,
) {
	return s.srv.ApplePaySession(ctx, req)
}

func (s *iamSmartPayServer) DecodeToken(
	ctx context.Context,
	req *DecodeTokenRequestV1,
) (
	*DecodeTokenResponseV1,
	error,
) {
	return s.srv.DecodeToken(ctx, req)
}

func (s *iamSmartPayServer) DecryptGPayToken(
	ctx context.Context,
	req *DecryptGPayTokenRequestV1,
) (
	*DecryptGPayTokenResponseV1,
	error,
) {
	return s.srv.DecryptGPayToken(ctx, req)
}

func (s *iamSmartPayServer) GetGPayCredentials(
	ctx context.Context,
	req *GetGPayCredentialsRequestV1,
) (
	*GetGPayCredentialsResponseV1,
	error,
) {
	return s.srv.GetGPayCredentials(ctx, req)
}

func NewIamSmartPayClient(
	client SmartPayClient,
) SmartPayClient {
	return &iamSmartPayClient{
		client: client,
	}
}

type iamSmartPayClient struct {
	client SmartPayClient
}

func (s *iamSmartPayClient) ApplePaySession(
	ctx context.Context,
	req *ApplePaySessionRequestV1,
	opts ...grpc.CallOption,
) (
	*ApplePaySessionResponseV1,
	error,
) {
	md, _ := metadata.FromIncomingContext(ctx)
	return s.client.ApplePaySession(metadata.NewOutgoingContext(ctx, md), req)
}

func (s *iamSmartPayClient) DecodeToken(
	ctx context.Context,
	req *DecodeTokenRequestV1,
	opts ...grpc.CallOption,
) (
	*DecodeTokenResponseV1,
	error,
) {
	md, _ := metadata.FromIncomingContext(ctx)
	return s.client.DecodeToken(metadata.NewOutgoingContext(ctx, md), req)
}

func (s *iamSmartPayClient) DecryptGPayToken(
	ctx context.Context,
	req *DecryptGPayTokenRequestV1,
	opts ...grpc.CallOption,
) (
	*DecryptGPayTokenResponseV1,
	error,
) {
	md, _ := metadata.FromIncomingContext(ctx)
	return s.client.DecryptGPayToken(metadata.NewOutgoingContext(ctx, md), req)
}

func (s *iamSmartPayClient) GetGPayCredentials(
	ctx context.Context,
	req *GetGPayCredentialsRequestV1,
	opts ...grpc.CallOption,
) (
	*GetGPayCredentialsResponseV1,
	error,
) {
	md, _ := metadata.FromIncomingContext(ctx)
	return s.client.GetGPayCredentials(metadata.NewOutgoingContext(ctx, md), req)
}
