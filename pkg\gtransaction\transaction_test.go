package gtransaction_test

import (
	"context"
	"encoding/json"

	"gorm.io/gorm"

	"git.local/sensitive/pkg/gtransaction"
)

type Book struct {
	BookID   uint   `gorm:"primaryKey;column:book_id" json:"book_id"`
	Title    string `gorm:"column:title" json:"title"`
	AuthorID uint   `gorm:"column:author_id" json:"author_id"`
}

type Author struct {
	AuthorID uint   `gorm:"primaryKey;column:author_id" json:"author_id"`
	FullName string `gorm:"column:full_name;uniqueIndex" json:"full_name"`
}

type BulkCreateItem struct {
	Book struct {
		Title string
	}
	Author struct {
		FullName string
	}
}

type IBookRepo interface {
	CreateBatch(ctx context.Context, book []*Book) ([]*Book, error)
}

type BookRepo struct {
	db *gorm.DB
}

func (r *BookRepo) CreateBatch(ctx context.Context, books []*Book) ([]*Book, error) {
	db := gtransaction.FromContextOrFallback(ctx, r.db)

	err := db.WithContext(ctx).CreateInBatches(books, 100).Error
	return books, err
}

type IAuthorRepo interface {
	Create(ctx context.Context, author *Author) (*Author, error)
}

type AuthorRepo struct {
	db *gorm.DB
}

func (r *AuthorRepo) Create(ctx context.Context, author *Author) (*Author, error) {
	db := gtransaction.FromContextOrFallback(ctx, r.db)

	err := db.WithContext(ctx).Create(author).Error
	return author, err
}

type IBookService interface {
	CreateBatch(ctx context.Context, books []*BulkCreateItem) ([]*Book, error)
}

type BookService struct {
	bookRepo    IBookRepo
	authorRepo  IAuthorRepo
	transaction gtransaction.Manager
}

func (s *BookService) CreateBatch(ctx context.Context, books []*BulkCreateItem) (createdBooks []*Book, err error) {
	ctx = s.transaction.Begin(ctx) // создаем контекст с транзакцией
	defer func() {
		// в случае, если после возврата err == nil - произойдет коммит, иначе роллбек
		err = s.transaction.Finish(ctx, err)
	}()

	booksBatch := make([]*Book, 0, len(books))
	authors := make(map[string]*Author)

	for _, book := range books {
		author, ok := authors[book.Author.FullName]
		if !ok {
			// в каждом вызове репы используем контекст с транзакцией
			author, err = s.authorRepo.Create(ctx, &Author{
				FullName: book.Author.FullName,
			})
			if err != nil {
				return nil, err
			}

			authors[book.Author.FullName] = author
		}

		booksBatch = append(booksBatch, &Book{
			Title:    book.Book.Title,
			AuthorID: author.AuthorID,
		})
	}

	// в каждом вызове репы используем контекст с транзакцией
	createdBooks, err = s.bookRepo.CreateBatch(ctx, booksBatch)
	if err != nil {
		return nil, err
	}

	return createdBooks, nil
}

func Example() {
	db, err := gorm.Open(nil) // заменить на открытие реального подключения
	if err != nil {
		panic(err)
	}

	bookRepo := &BookRepo{db: db}
	authorRepo := &AuthorRepo{db: db}

	bookService := &BookService{
		bookRepo:    bookRepo,
		authorRepo:  authorRepo,
		transaction: gtransaction.NewGormTransactionManager(db),
	}

	rawBooks := `
		[
		  {
			"book": {
			  "title": "Высоконагруженные приложения. Программирование, масштабирование, поддержка"
			},
			"author": {
			  "full_name": "Мартин Клеппман"
			}
		  },
		  {
			"book": {
			  "title": "Go: идиомы и паттерны проектирования"
			},
			"author": {
			  "full_name": "Джон Боднер"
			}
		  }
		]
	`

	var booksBatch []*BulkCreateItem

	if err := json.Unmarshal([]byte(rawBooks), &booksBatch); err != nil {
		panic(err)
	}

	if _, err := bookService.CreateBatch(context.Background(), booksBatch); err != nil {
		panic(err)
	}
}
