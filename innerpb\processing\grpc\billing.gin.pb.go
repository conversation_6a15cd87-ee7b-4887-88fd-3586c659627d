// Code generated by protoc-gen-go-mvp. DO NOT EDIT.
// versions:
// - protoc-gen-go-mvp v2.0.1
// created by mvp authors

package grpc

import (
	dog "git.local/sensitive/sdk/dog"
	gin "github.com/gin-gonic/gin"
	metadata "google.golang.org/grpc/metadata"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
	http "net/http"
)

func RegisterGinBillingRouterGroup(
	in *gin.RouterGroup,
	handler func(func(c *gin.Context) error) gin.HandlerFunc,
) error {
	service, err := NewGinBillingService()
	if err != nil {
		return err
	}

	routerGroup := in.Group("processing.billing.billing.Billing")
	routerGroup.PUT("/CheckPayOutBalanceV1", handler(service.CheckPayOutBalanceV1))
	routerGroup.PUT("/BillPayInTransactionV1", handler(service.BillPayInTransactionV1))
	routerGroup.PUT("/BillPayOutTransactionV1", handler(service.BillPayOutTransactionV1))
	routerGroup.PUT("/BillRefundTransactionV1", handler(service.BillRefundTransactionV1))
	routerGroup.PUT("/CheckOutTransferBalanceV1", handler(service.CheckOutTransferBalanceV1))
	routerGroup.PUT("/BillOutTransferV1", handler(service.BillOutTransferV1))
	routerGroup.PUT("/BillInTransferV1", handler(service.BillInTransferV1))
	routerGroup.PUT("/GetMerchantByBalanceOwnerIDV1", handler(service.GetMerchantByBalanceOwnerIDV1))
	routerGroup.PUT("/SetInTransferV1", handler(service.SetInTransferV1))
	routerGroup.PUT("/BillSplitTransferV1", handler(service.BillSplitTransferV1))
	routerGroup.PUT("/GetBalanceOwnerV1", handler(service.GetBalanceOwnerV1))
	routerGroup.PUT("/SetBalanceOwnerSplittableV1", handler(service.SetBalanceOwnerSplittableV1))
	routerGroup.PUT("/GetBalanceOwnerByIDV1", handler(service.GetBalanceOwnerByIDV1))
	routerGroup.PUT("/GetEntityTypeByIDV1", handler(service.GetEntityTypeByIDV1))
	routerGroup.PUT("/GetCountryCodeByIDV1", handler(service.GetCountryCodeByIDV1))
	routerGroup.PUT("/GetBalanceByIDV1", handler(service.GetBalanceByIDV1))
	routerGroup.PUT("/CheckHasBalanceV1", handler(service.CheckHasBalanceV1))
	routerGroup.PUT("/CheckBalanceOwnerAccountExistsByBalanceOwnerIdAndAccountIdV1", handler(service.CheckBalanceOwnerAccountExistsByBalanceOwnerIdAndAccountIdV1))
	routerGroup.PUT("/GetBalanceAccountByNumber", handler(service.GetBalanceAccountByNumber))
	routerGroup.PUT("/GetCurrentBalanceAmountByBalanceOwnerID", handler(service.GetCurrentBalanceAmountByBalanceOwnerID))
	routerGroup.PUT("/CheckBalanceCreditExpireDate", handler(service.CheckBalanceCreditExpireDate))
	routerGroup.PUT("/CheckBalanceCreditStartDate", handler(service.CheckBalanceCreditStartDate))
	routerGroup.PUT("/RecalculateProvisionalBalances", handler(service.RecalculateProvisionalBalances))
	routerGroup.PUT("/RecalculateFinalBalances", handler(service.RecalculateFinalBalances))
	routerGroup.PUT("/RecalculateCreditBalances", handler(service.RecalculateCreditBalances))
	return nil
}

func NewGinBillingService() (GinBillingServer, error) {
	client, err := NewPreparedBillingClient()
	if err != nil {
		return nil, err
	}

	return &ginBillingServer{
		client: NewLoggedBillingClient(
			NewIamBillingClient(client),
		),
	}, nil
}

//go:generate mockgen -destination=./grpcmock/billing.gin.pb.go -package=grpcmock -source=billing.gin.pb.go GinBillingServer
type GinBillingServer interface {
	CheckPayOutBalanceV1(c *gin.Context) error
	BillPayInTransactionV1(c *gin.Context) error
	BillPayOutTransactionV1(c *gin.Context) error
	BillRefundTransactionV1(c *gin.Context) error
	CheckOutTransferBalanceV1(c *gin.Context) error
	BillOutTransferV1(c *gin.Context) error
	BillInTransferV1(c *gin.Context) error
	GetMerchantByBalanceOwnerIDV1(c *gin.Context) error
	SetInTransferV1(c *gin.Context) error
	BillSplitTransferV1(c *gin.Context) error
	GetBalanceOwnerV1(c *gin.Context) error
	SetBalanceOwnerSplittableV1(c *gin.Context) error
	GetBalanceOwnerByIDV1(c *gin.Context) error
	GetEntityTypeByIDV1(c *gin.Context) error
	GetCountryCodeByIDV1(c *gin.Context) error
	GetBalanceByIDV1(c *gin.Context) error
	CheckHasBalanceV1(c *gin.Context) error
	CheckBalanceOwnerAccountExistsByBalanceOwnerIdAndAccountIdV1(c *gin.Context) error
	GetBalanceAccountByNumber(c *gin.Context) error
	GetCurrentBalanceAmountByBalanceOwnerID(c *gin.Context) error
	CheckBalanceCreditExpireDate(c *gin.Context) error
	CheckBalanceCreditStartDate(c *gin.Context) error
	RecalculateProvisionalBalances(c *gin.Context) error
	RecalculateFinalBalances(c *gin.Context) error
	RecalculateCreditBalances(c *gin.Context) error
}

var _ GinBillingServer = (*ginBillingServer)(nil)

type ginBillingServer struct {
	client BillingClient
}

type Billing_CheckPayOutBalanceV1_Success struct {
	// Status is the status of the response. Set true
	Status bool `json:"status"`
	// Message is a human-readable explanation of the status
	Message string `json:"message"`
	// StatusCode is the GoErr status. Set 0
	StatusCode int `json:"status_code"`
	// Result is the result of the request
	Result *CheckPayOutBalanceResV1 `json:"result"`
}

type Billing_CheckPayOutBalanceV1_Failure struct {
	// Status is the status of the response. Set false
	Status bool `json:"status"`
	// Message is a human-readable explanation of the status
	Message string `json:"message"`
	// StatusCode is the GoErr status
	StatusCode int `json:"status_code"`
}

// CheckPayOutBalanceV1
// @Summary CheckPayOutBalanceV1
// @Security bearerAuth
// @ID Billing_CheckPayOutBalanceV1
// @Accept json
// @Param request body CheckPayOutBalanceReqV1 true "CheckPayOutBalanceReqV1"
// @Success 200 {object} Billing_CheckPayOutBalanceV1_Success
// @Failure 401 {object} Billing_CheckPayOutBalanceV1_Failure "goerrpkg.ErrorTypeAuthorization"
// @Failure 404 {object} Billing_CheckPayOutBalanceV1_Failure "goerrpkg.ErrorTypeNotFound"
// @Failure 400 {object} Billing_CheckPayOutBalanceV1_Failure "goerrpkg.ErrorTypeBadRequest"
// @Failure 500 {object} Billing_CheckPayOutBalanceV1_Failure "goerrpkg.ErrorTypeUnexpectedError"
// @Failure 400 {object} Billing_CheckPayOutBalanceV1_Failure "gin.ErrorTypeBind"
// @Failure 500 {object} Billing_CheckPayOutBalanceV1_Failure "Undefined error"
// @Produce json
// @Router /processing.billing.billing.Billing/CheckPayOutBalanceV1 [put]
// @tags Billing
func (s *ginBillingServer) CheckPayOutBalanceV1(c *gin.Context) error {
	ctx, span := dog.CreateSpan(c.Request.Context(), "GinBillingServer_CheckPayOutBalanceV1")
	defer span.End()

	var request CheckPayOutBalanceReqV1
	if err := c.ShouldBindJSON(&request); err != nil {
		return err
	}

	ctx = metadata.NewOutgoingContext(ctx, metadata.MD(c.Request.Header))

	response, err := s.client.CheckPayOutBalanceV1(ctx, &request)
	if err != nil {
		return err
	}

	c.JSON(http.StatusOK, &Billing_CheckPayOutBalanceV1_Success{
		Status:     true,
		Message:    "Success",
		StatusCode: 0,
		Result:     response,
	})
	return nil
}

type Billing_BillPayInTransactionV1_Success struct {
	// Status is the status of the response. Set true
	Status bool `json:"status"`
	// Message is a human-readable explanation of the status
	Message string `json:"message"`
	// StatusCode is the GoErr status. Set 0
	StatusCode int `json:"status_code"`
	// Result is the result of the request
	Result *emptypb.Empty `json:"result"`
}

type Billing_BillPayInTransactionV1_Failure struct {
	// Status is the status of the response. Set false
	Status bool `json:"status"`
	// Message is a human-readable explanation of the status
	Message string `json:"message"`
	// StatusCode is the GoErr status
	StatusCode int `json:"status_code"`
}

// BillPayInTransactionV1
// @Summary BillPayInTransactionV1
// @Security bearerAuth
// @ID Billing_BillPayInTransactionV1
// @Accept json
// @Param request body BillPayInTransactionRequestV1 true "BillPayInTransactionRequestV1"
// @Success 200 {object} Billing_BillPayInTransactionV1_Success
// @Failure 401 {object} Billing_BillPayInTransactionV1_Failure "goerrpkg.ErrorTypeAuthorization"
// @Failure 404 {object} Billing_BillPayInTransactionV1_Failure "goerrpkg.ErrorTypeNotFound"
// @Failure 400 {object} Billing_BillPayInTransactionV1_Failure "goerrpkg.ErrorTypeBadRequest"
// @Failure 500 {object} Billing_BillPayInTransactionV1_Failure "goerrpkg.ErrorTypeUnexpectedError"
// @Failure 400 {object} Billing_BillPayInTransactionV1_Failure "gin.ErrorTypeBind"
// @Failure 500 {object} Billing_BillPayInTransactionV1_Failure "Undefined error"
// @Produce json
// @Router /processing.billing.billing.Billing/BillPayInTransactionV1 [put]
// @tags Billing
func (s *ginBillingServer) BillPayInTransactionV1(c *gin.Context) error {
	ctx, span := dog.CreateSpan(c.Request.Context(), "GinBillingServer_BillPayInTransactionV1")
	defer span.End()

	var request BillPayInTransactionRequestV1
	if err := c.ShouldBindJSON(&request); err != nil {
		return err
	}

	ctx = metadata.NewOutgoingContext(ctx, metadata.MD(c.Request.Header))

	response, err := s.client.BillPayInTransactionV1(ctx, &request)
	if err != nil {
		return err
	}

	c.JSON(http.StatusOK, &Billing_BillPayInTransactionV1_Success{
		Status:     true,
		Message:    "Success",
		StatusCode: 0,
		Result:     response,
	})
	return nil
}

type Billing_BillPayOutTransactionV1_Success struct {
	// Status is the status of the response. Set true
	Status bool `json:"status"`
	// Message is a human-readable explanation of the status
	Message string `json:"message"`
	// StatusCode is the GoErr status. Set 0
	StatusCode int `json:"status_code"`
	// Result is the result of the request
	Result *emptypb.Empty `json:"result"`
}

type Billing_BillPayOutTransactionV1_Failure struct {
	// Status is the status of the response. Set false
	Status bool `json:"status"`
	// Message is a human-readable explanation of the status
	Message string `json:"message"`
	// StatusCode is the GoErr status
	StatusCode int `json:"status_code"`
}

// BillPayOutTransactionV1
// @Summary BillPayOutTransactionV1
// @Security bearerAuth
// @ID Billing_BillPayOutTransactionV1
// @Accept json
// @Param request body BillPayOutTransactionRequestV1 true "BillPayOutTransactionRequestV1"
// @Success 200 {object} Billing_BillPayOutTransactionV1_Success
// @Failure 401 {object} Billing_BillPayOutTransactionV1_Failure "goerrpkg.ErrorTypeAuthorization"
// @Failure 404 {object} Billing_BillPayOutTransactionV1_Failure "goerrpkg.ErrorTypeNotFound"
// @Failure 400 {object} Billing_BillPayOutTransactionV1_Failure "goerrpkg.ErrorTypeBadRequest"
// @Failure 500 {object} Billing_BillPayOutTransactionV1_Failure "goerrpkg.ErrorTypeUnexpectedError"
// @Failure 400 {object} Billing_BillPayOutTransactionV1_Failure "gin.ErrorTypeBind"
// @Failure 500 {object} Billing_BillPayOutTransactionV1_Failure "Undefined error"
// @Produce json
// @Router /processing.billing.billing.Billing/BillPayOutTransactionV1 [put]
// @tags Billing
func (s *ginBillingServer) BillPayOutTransactionV1(c *gin.Context) error {
	ctx, span := dog.CreateSpan(c.Request.Context(), "GinBillingServer_BillPayOutTransactionV1")
	defer span.End()

	var request BillPayOutTransactionRequestV1
	if err := c.ShouldBindJSON(&request); err != nil {
		return err
	}

	ctx = metadata.NewOutgoingContext(ctx, metadata.MD(c.Request.Header))

	response, err := s.client.BillPayOutTransactionV1(ctx, &request)
	if err != nil {
		return err
	}

	c.JSON(http.StatusOK, &Billing_BillPayOutTransactionV1_Success{
		Status:     true,
		Message:    "Success",
		StatusCode: 0,
		Result:     response,
	})
	return nil
}

type Billing_BillRefundTransactionV1_Success struct {
	// Status is the status of the response. Set true
	Status bool `json:"status"`
	// Message is a human-readable explanation of the status
	Message string `json:"message"`
	// StatusCode is the GoErr status. Set 0
	StatusCode int `json:"status_code"`
	// Result is the result of the request
	Result *emptypb.Empty `json:"result"`
}

type Billing_BillRefundTransactionV1_Failure struct {
	// Status is the status of the response. Set false
	Status bool `json:"status"`
	// Message is a human-readable explanation of the status
	Message string `json:"message"`
	// StatusCode is the GoErr status
	StatusCode int `json:"status_code"`
}

// BillRefundTransactionV1
// @Summary BillRefundTransactionV1
// @Security bearerAuth
// @ID Billing_BillRefundTransactionV1
// @Accept json
// @Param request body BillRefundTransactionRequestV1 true "BillRefundTransactionRequestV1"
// @Success 200 {object} Billing_BillRefundTransactionV1_Success
// @Failure 401 {object} Billing_BillRefundTransactionV1_Failure "goerrpkg.ErrorTypeAuthorization"
// @Failure 404 {object} Billing_BillRefundTransactionV1_Failure "goerrpkg.ErrorTypeNotFound"
// @Failure 400 {object} Billing_BillRefundTransactionV1_Failure "goerrpkg.ErrorTypeBadRequest"
// @Failure 500 {object} Billing_BillRefundTransactionV1_Failure "goerrpkg.ErrorTypeUnexpectedError"
// @Failure 400 {object} Billing_BillRefundTransactionV1_Failure "gin.ErrorTypeBind"
// @Failure 500 {object} Billing_BillRefundTransactionV1_Failure "Undefined error"
// @Produce json
// @Router /processing.billing.billing.Billing/BillRefundTransactionV1 [put]
// @tags Billing
func (s *ginBillingServer) BillRefundTransactionV1(c *gin.Context) error {
	ctx, span := dog.CreateSpan(c.Request.Context(), "GinBillingServer_BillRefundTransactionV1")
	defer span.End()

	var request BillRefundTransactionRequestV1
	if err := c.ShouldBindJSON(&request); err != nil {
		return err
	}

	ctx = metadata.NewOutgoingContext(ctx, metadata.MD(c.Request.Header))

	response, err := s.client.BillRefundTransactionV1(ctx, &request)
	if err != nil {
		return err
	}

	c.JSON(http.StatusOK, &Billing_BillRefundTransactionV1_Success{
		Status:     true,
		Message:    "Success",
		StatusCode: 0,
		Result:     response,
	})
	return nil
}

type Billing_CheckOutTransferBalanceV1_Success struct {
	// Status is the status of the response. Set true
	Status bool `json:"status"`
	// Message is a human-readable explanation of the status
	Message string `json:"message"`
	// StatusCode is the GoErr status. Set 0
	StatusCode int `json:"status_code"`
	// Result is the result of the request
	Result *CheckOutTransferBalanceResponseV1 `json:"result"`
}

type Billing_CheckOutTransferBalanceV1_Failure struct {
	// Status is the status of the response. Set false
	Status bool `json:"status"`
	// Message is a human-readable explanation of the status
	Message string `json:"message"`
	// StatusCode is the GoErr status
	StatusCode int `json:"status_code"`
}

// CheckOutTransferBalanceV1
// @Summary CheckOutTransferBalanceV1
// @Security bearerAuth
// @ID Billing_CheckOutTransferBalanceV1
// @Accept json
// @Param request body CheckOutTransferBalanceRequestV1 true "CheckOutTransferBalanceRequestV1"
// @Success 200 {object} Billing_CheckOutTransferBalanceV1_Success
// @Failure 401 {object} Billing_CheckOutTransferBalanceV1_Failure "goerrpkg.ErrorTypeAuthorization"
// @Failure 404 {object} Billing_CheckOutTransferBalanceV1_Failure "goerrpkg.ErrorTypeNotFound"
// @Failure 400 {object} Billing_CheckOutTransferBalanceV1_Failure "goerrpkg.ErrorTypeBadRequest"
// @Failure 500 {object} Billing_CheckOutTransferBalanceV1_Failure "goerrpkg.ErrorTypeUnexpectedError"
// @Failure 400 {object} Billing_CheckOutTransferBalanceV1_Failure "gin.ErrorTypeBind"
// @Failure 500 {object} Billing_CheckOutTransferBalanceV1_Failure "Undefined error"
// @Produce json
// @Router /processing.billing.billing.Billing/CheckOutTransferBalanceV1 [put]
// @tags Billing
func (s *ginBillingServer) CheckOutTransferBalanceV1(c *gin.Context) error {
	ctx, span := dog.CreateSpan(c.Request.Context(), "GinBillingServer_CheckOutTransferBalanceV1")
	defer span.End()

	var request CheckOutTransferBalanceRequestV1
	if err := c.ShouldBindJSON(&request); err != nil {
		return err
	}

	ctx = metadata.NewOutgoingContext(ctx, metadata.MD(c.Request.Header))

	response, err := s.client.CheckOutTransferBalanceV1(ctx, &request)
	if err != nil {
		return err
	}

	c.JSON(http.StatusOK, &Billing_CheckOutTransferBalanceV1_Success{
		Status:     true,
		Message:    "Success",
		StatusCode: 0,
		Result:     response,
	})
	return nil
}

type Billing_BillOutTransferV1_Success struct {
	// Status is the status of the response. Set true
	Status bool `json:"status"`
	// Message is a human-readable explanation of the status
	Message string `json:"message"`
	// StatusCode is the GoErr status. Set 0
	StatusCode int `json:"status_code"`
	// Result is the result of the request
	Result *emptypb.Empty `json:"result"`
}

type Billing_BillOutTransferV1_Failure struct {
	// Status is the status of the response. Set false
	Status bool `json:"status"`
	// Message is a human-readable explanation of the status
	Message string `json:"message"`
	// StatusCode is the GoErr status
	StatusCode int `json:"status_code"`
}

// BillOutTransferV1
// @Summary BillOutTransferV1
// @Security bearerAuth
// @ID Billing_BillOutTransferV1
// @Accept json
// @Param request body BillOutTransferRequestV1 true "BillOutTransferRequestV1"
// @Success 200 {object} Billing_BillOutTransferV1_Success
// @Failure 401 {object} Billing_BillOutTransferV1_Failure "goerrpkg.ErrorTypeAuthorization"
// @Failure 404 {object} Billing_BillOutTransferV1_Failure "goerrpkg.ErrorTypeNotFound"
// @Failure 400 {object} Billing_BillOutTransferV1_Failure "goerrpkg.ErrorTypeBadRequest"
// @Failure 500 {object} Billing_BillOutTransferV1_Failure "goerrpkg.ErrorTypeUnexpectedError"
// @Failure 400 {object} Billing_BillOutTransferV1_Failure "gin.ErrorTypeBind"
// @Failure 500 {object} Billing_BillOutTransferV1_Failure "Undefined error"
// @Produce json
// @Router /processing.billing.billing.Billing/BillOutTransferV1 [put]
// @tags Billing
func (s *ginBillingServer) BillOutTransferV1(c *gin.Context) error {
	ctx, span := dog.CreateSpan(c.Request.Context(), "GinBillingServer_BillOutTransferV1")
	defer span.End()

	var request BillOutTransferRequestV1
	if err := c.ShouldBindJSON(&request); err != nil {
		return err
	}

	ctx = metadata.NewOutgoingContext(ctx, metadata.MD(c.Request.Header))

	response, err := s.client.BillOutTransferV1(ctx, &request)
	if err != nil {
		return err
	}

	c.JSON(http.StatusOK, &Billing_BillOutTransferV1_Success{
		Status:     true,
		Message:    "Success",
		StatusCode: 0,
		Result:     response,
	})
	return nil
}

type Billing_BillInTransferV1_Success struct {
	// Status is the status of the response. Set true
	Status bool `json:"status"`
	// Message is a human-readable explanation of the status
	Message string `json:"message"`
	// StatusCode is the GoErr status. Set 0
	StatusCode int `json:"status_code"`
	// Result is the result of the request
	Result *emptypb.Empty `json:"result"`
}

type Billing_BillInTransferV1_Failure struct {
	// Status is the status of the response. Set false
	Status bool `json:"status"`
	// Message is a human-readable explanation of the status
	Message string `json:"message"`
	// StatusCode is the GoErr status
	StatusCode int `json:"status_code"`
}

// BillInTransferV1
// @Summary BillInTransferV1
// @Security bearerAuth
// @ID Billing_BillInTransferV1
// @Accept json
// @Param request body BillInTransferRequestV1 true "BillInTransferRequestV1"
// @Success 200 {object} Billing_BillInTransferV1_Success
// @Failure 401 {object} Billing_BillInTransferV1_Failure "goerrpkg.ErrorTypeAuthorization"
// @Failure 404 {object} Billing_BillInTransferV1_Failure "goerrpkg.ErrorTypeNotFound"
// @Failure 400 {object} Billing_BillInTransferV1_Failure "goerrpkg.ErrorTypeBadRequest"
// @Failure 500 {object} Billing_BillInTransferV1_Failure "goerrpkg.ErrorTypeUnexpectedError"
// @Failure 400 {object} Billing_BillInTransferV1_Failure "gin.ErrorTypeBind"
// @Failure 500 {object} Billing_BillInTransferV1_Failure "Undefined error"
// @Produce json
// @Router /processing.billing.billing.Billing/BillInTransferV1 [put]
// @tags Billing
func (s *ginBillingServer) BillInTransferV1(c *gin.Context) error {
	ctx, span := dog.CreateSpan(c.Request.Context(), "GinBillingServer_BillInTransferV1")
	defer span.End()

	var request BillInTransferRequestV1
	if err := c.ShouldBindJSON(&request); err != nil {
		return err
	}

	ctx = metadata.NewOutgoingContext(ctx, metadata.MD(c.Request.Header))

	response, err := s.client.BillInTransferV1(ctx, &request)
	if err != nil {
		return err
	}

	c.JSON(http.StatusOK, &Billing_BillInTransferV1_Success{
		Status:     true,
		Message:    "Success",
		StatusCode: 0,
		Result:     response,
	})
	return nil
}

type Billing_GetMerchantByBalanceOwnerIDV1_Success struct {
	// Status is the status of the response. Set true
	Status bool `json:"status"`
	// Message is a human-readable explanation of the status
	Message string `json:"message"`
	// StatusCode is the GoErr status. Set 0
	StatusCode int `json:"status_code"`
	// Result is the result of the request
	Result *GetMerchantByBalanceOwnerResponseV1 `json:"result"`
}

type Billing_GetMerchantByBalanceOwnerIDV1_Failure struct {
	// Status is the status of the response. Set false
	Status bool `json:"status"`
	// Message is a human-readable explanation of the status
	Message string `json:"message"`
	// StatusCode is the GoErr status
	StatusCode int `json:"status_code"`
}

// GetMerchantByBalanceOwnerIDV1
// @Summary GetMerchantByBalanceOwnerIDV1
// @Security bearerAuth
// @ID Billing_GetMerchantByBalanceOwnerIDV1
// @Accept json
// @Param request body GetMerchantByBalanceOwnerRequestV1 true "GetMerchantByBalanceOwnerRequestV1"
// @Success 200 {object} Billing_GetMerchantByBalanceOwnerIDV1_Success
// @Failure 401 {object} Billing_GetMerchantByBalanceOwnerIDV1_Failure "goerrpkg.ErrorTypeAuthorization"
// @Failure 404 {object} Billing_GetMerchantByBalanceOwnerIDV1_Failure "goerrpkg.ErrorTypeNotFound"
// @Failure 400 {object} Billing_GetMerchantByBalanceOwnerIDV1_Failure "goerrpkg.ErrorTypeBadRequest"
// @Failure 500 {object} Billing_GetMerchantByBalanceOwnerIDV1_Failure "goerrpkg.ErrorTypeUnexpectedError"
// @Failure 400 {object} Billing_GetMerchantByBalanceOwnerIDV1_Failure "gin.ErrorTypeBind"
// @Failure 500 {object} Billing_GetMerchantByBalanceOwnerIDV1_Failure "Undefined error"
// @Produce json
// @Router /processing.billing.billing.Billing/GetMerchantByBalanceOwnerIDV1 [put]
// @tags Billing
func (s *ginBillingServer) GetMerchantByBalanceOwnerIDV1(c *gin.Context) error {
	ctx, span := dog.CreateSpan(c.Request.Context(), "GinBillingServer_GetMerchantByBalanceOwnerIDV1")
	defer span.End()

	var request GetMerchantByBalanceOwnerRequestV1
	if err := c.ShouldBindJSON(&request); err != nil {
		return err
	}

	ctx = metadata.NewOutgoingContext(ctx, metadata.MD(c.Request.Header))

	response, err := s.client.GetMerchantByBalanceOwnerIDV1(ctx, &request)
	if err != nil {
		return err
	}

	c.JSON(http.StatusOK, &Billing_GetMerchantByBalanceOwnerIDV1_Success{
		Status:     true,
		Message:    "Success",
		StatusCode: 0,
		Result:     response,
	})
	return nil
}

type Billing_SetInTransferV1_Success struct {
	// Status is the status of the response. Set true
	Status bool `json:"status"`
	// Message is a human-readable explanation of the status
	Message string `json:"message"`
	// StatusCode is the GoErr status. Set 0
	StatusCode int `json:"status_code"`
	// Result is the result of the request
	Result *emptypb.Empty `json:"result"`
}

type Billing_SetInTransferV1_Failure struct {
	// Status is the status of the response. Set false
	Status bool `json:"status"`
	// Message is a human-readable explanation of the status
	Message string `json:"message"`
	// StatusCode is the GoErr status
	StatusCode int `json:"status_code"`
}

// SetInTransferV1
// @Summary SetInTransferV1
// @Security bearerAuth
// @ID Billing_SetInTransferV1
// @Accept json
// @Param request body SetInTransferRequestV1 true "SetInTransferRequestV1"
// @Success 200 {object} Billing_SetInTransferV1_Success
// @Failure 401 {object} Billing_SetInTransferV1_Failure "goerrpkg.ErrorTypeAuthorization"
// @Failure 404 {object} Billing_SetInTransferV1_Failure "goerrpkg.ErrorTypeNotFound"
// @Failure 400 {object} Billing_SetInTransferV1_Failure "goerrpkg.ErrorTypeBadRequest"
// @Failure 500 {object} Billing_SetInTransferV1_Failure "goerrpkg.ErrorTypeUnexpectedError"
// @Failure 400 {object} Billing_SetInTransferV1_Failure "gin.ErrorTypeBind"
// @Failure 500 {object} Billing_SetInTransferV1_Failure "Undefined error"
// @Produce json
// @Router /processing.billing.billing.Billing/SetInTransferV1 [put]
// @tags Billing
func (s *ginBillingServer) SetInTransferV1(c *gin.Context) error {
	ctx, span := dog.CreateSpan(c.Request.Context(), "GinBillingServer_SetInTransferV1")
	defer span.End()

	var request SetInTransferRequestV1
	if err := c.ShouldBindJSON(&request); err != nil {
		return err
	}

	ctx = metadata.NewOutgoingContext(ctx, metadata.MD(c.Request.Header))

	response, err := s.client.SetInTransferV1(ctx, &request)
	if err != nil {
		return err
	}

	c.JSON(http.StatusOK, &Billing_SetInTransferV1_Success{
		Status:     true,
		Message:    "Success",
		StatusCode: 0,
		Result:     response,
	})
	return nil
}

type Billing_BillSplitTransferV1_Success struct {
	// Status is the status of the response. Set true
	Status bool `json:"status"`
	// Message is a human-readable explanation of the status
	Message string `json:"message"`
	// StatusCode is the GoErr status. Set 0
	StatusCode int `json:"status_code"`
	// Result is the result of the request
	Result *emptypb.Empty `json:"result"`
}

type Billing_BillSplitTransferV1_Failure struct {
	// Status is the status of the response. Set false
	Status bool `json:"status"`
	// Message is a human-readable explanation of the status
	Message string `json:"message"`
	// StatusCode is the GoErr status
	StatusCode int `json:"status_code"`
}

// BillSplitTransferV1
// @Summary BillSplitTransferV1
// @Security bearerAuth
// @ID Billing_BillSplitTransferV1
// @Accept json
// @Param request body BillSplitTransferRequestV1 true "BillSplitTransferRequestV1"
// @Success 200 {object} Billing_BillSplitTransferV1_Success
// @Failure 401 {object} Billing_BillSplitTransferV1_Failure "goerrpkg.ErrorTypeAuthorization"
// @Failure 404 {object} Billing_BillSplitTransferV1_Failure "goerrpkg.ErrorTypeNotFound"
// @Failure 400 {object} Billing_BillSplitTransferV1_Failure "goerrpkg.ErrorTypeBadRequest"
// @Failure 500 {object} Billing_BillSplitTransferV1_Failure "goerrpkg.ErrorTypeUnexpectedError"
// @Failure 400 {object} Billing_BillSplitTransferV1_Failure "gin.ErrorTypeBind"
// @Failure 500 {object} Billing_BillSplitTransferV1_Failure "Undefined error"
// @Produce json
// @Router /processing.billing.billing.Billing/BillSplitTransferV1 [put]
// @tags Billing
func (s *ginBillingServer) BillSplitTransferV1(c *gin.Context) error {
	ctx, span := dog.CreateSpan(c.Request.Context(), "GinBillingServer_BillSplitTransferV1")
	defer span.End()

	var request BillSplitTransferRequestV1
	if err := c.ShouldBindJSON(&request); err != nil {
		return err
	}

	ctx = metadata.NewOutgoingContext(ctx, metadata.MD(c.Request.Header))

	response, err := s.client.BillSplitTransferV1(ctx, &request)
	if err != nil {
		return err
	}

	c.JSON(http.StatusOK, &Billing_BillSplitTransferV1_Success{
		Status:     true,
		Message:    "Success",
		StatusCode: 0,
		Result:     response,
	})
	return nil
}

type Billing_GetBalanceOwnerV1_Success struct {
	// Status is the status of the response. Set true
	Status bool `json:"status"`
	// Message is a human-readable explanation of the status
	Message string `json:"message"`
	// StatusCode is the GoErr status. Set 0
	StatusCode int `json:"status_code"`
	// Result is the result of the request
	Result *GetBalanceOwnerResponseV1 `json:"result"`
}

type Billing_GetBalanceOwnerV1_Failure struct {
	// Status is the status of the response. Set false
	Status bool `json:"status"`
	// Message is a human-readable explanation of the status
	Message string `json:"message"`
	// StatusCode is the GoErr status
	StatusCode int `json:"status_code"`
}

// GetBalanceOwnerV1
// @Summary GetBalanceOwnerV1
// @Security bearerAuth
// @ID Billing_GetBalanceOwnerV1
// @Accept json
// @Param request body GetBalanceOwnerRequestV1 true "GetBalanceOwnerRequestV1"
// @Success 200 {object} Billing_GetBalanceOwnerV1_Success
// @Failure 401 {object} Billing_GetBalanceOwnerV1_Failure "goerrpkg.ErrorTypeAuthorization"
// @Failure 404 {object} Billing_GetBalanceOwnerV1_Failure "goerrpkg.ErrorTypeNotFound"
// @Failure 400 {object} Billing_GetBalanceOwnerV1_Failure "goerrpkg.ErrorTypeBadRequest"
// @Failure 500 {object} Billing_GetBalanceOwnerV1_Failure "goerrpkg.ErrorTypeUnexpectedError"
// @Failure 400 {object} Billing_GetBalanceOwnerV1_Failure "gin.ErrorTypeBind"
// @Failure 500 {object} Billing_GetBalanceOwnerV1_Failure "Undefined error"
// @Produce json
// @Router /processing.billing.billing.Billing/GetBalanceOwnerV1 [put]
// @tags Billing
func (s *ginBillingServer) GetBalanceOwnerV1(c *gin.Context) error {
	ctx, span := dog.CreateSpan(c.Request.Context(), "GinBillingServer_GetBalanceOwnerV1")
	defer span.End()

	var request GetBalanceOwnerRequestV1
	if err := c.ShouldBindJSON(&request); err != nil {
		return err
	}

	ctx = metadata.NewOutgoingContext(ctx, metadata.MD(c.Request.Header))

	response, err := s.client.GetBalanceOwnerV1(ctx, &request)
	if err != nil {
		return err
	}

	c.JSON(http.StatusOK, &Billing_GetBalanceOwnerV1_Success{
		Status:     true,
		Message:    "Success",
		StatusCode: 0,
		Result:     response,
	})
	return nil
}

type Billing_SetBalanceOwnerSplittableV1_Success struct {
	// Status is the status of the response. Set true
	Status bool `json:"status"`
	// Message is a human-readable explanation of the status
	Message string `json:"message"`
	// StatusCode is the GoErr status. Set 0
	StatusCode int `json:"status_code"`
	// Result is the result of the request
	Result *emptypb.Empty `json:"result"`
}

type Billing_SetBalanceOwnerSplittableV1_Failure struct {
	// Status is the status of the response. Set false
	Status bool `json:"status"`
	// Message is a human-readable explanation of the status
	Message string `json:"message"`
	// StatusCode is the GoErr status
	StatusCode int `json:"status_code"`
}

// SetBalanceOwnerSplittableV1
// @Summary SetBalanceOwnerSplittableV1
// @Security bearerAuth
// @ID Billing_SetBalanceOwnerSplittableV1
// @Accept json
// @Param request body SetBalanceOwnerSplittableRequestV1 true "SetBalanceOwnerSplittableRequestV1"
// @Success 200 {object} Billing_SetBalanceOwnerSplittableV1_Success
// @Failure 401 {object} Billing_SetBalanceOwnerSplittableV1_Failure "goerrpkg.ErrorTypeAuthorization"
// @Failure 404 {object} Billing_SetBalanceOwnerSplittableV1_Failure "goerrpkg.ErrorTypeNotFound"
// @Failure 400 {object} Billing_SetBalanceOwnerSplittableV1_Failure "goerrpkg.ErrorTypeBadRequest"
// @Failure 500 {object} Billing_SetBalanceOwnerSplittableV1_Failure "goerrpkg.ErrorTypeUnexpectedError"
// @Failure 400 {object} Billing_SetBalanceOwnerSplittableV1_Failure "gin.ErrorTypeBind"
// @Failure 500 {object} Billing_SetBalanceOwnerSplittableV1_Failure "Undefined error"
// @Produce json
// @Router /processing.billing.billing.Billing/SetBalanceOwnerSplittableV1 [put]
// @tags Billing
func (s *ginBillingServer) SetBalanceOwnerSplittableV1(c *gin.Context) error {
	ctx, span := dog.CreateSpan(c.Request.Context(), "GinBillingServer_SetBalanceOwnerSplittableV1")
	defer span.End()

	var request SetBalanceOwnerSplittableRequestV1
	if err := c.ShouldBindJSON(&request); err != nil {
		return err
	}

	ctx = metadata.NewOutgoingContext(ctx, metadata.MD(c.Request.Header))

	response, err := s.client.SetBalanceOwnerSplittableV1(ctx, &request)
	if err != nil {
		return err
	}

	c.JSON(http.StatusOK, &Billing_SetBalanceOwnerSplittableV1_Success{
		Status:     true,
		Message:    "Success",
		StatusCode: 0,
		Result:     response,
	})
	return nil
}

type Billing_GetBalanceOwnerByIDV1_Success struct {
	// Status is the status of the response. Set true
	Status bool `json:"status"`
	// Message is a human-readable explanation of the status
	Message string `json:"message"`
	// StatusCode is the GoErr status. Set 0
	StatusCode int `json:"status_code"`
	// Result is the result of the request
	Result *GetBalanceOwnerResponseV1 `json:"result"`
}

type Billing_GetBalanceOwnerByIDV1_Failure struct {
	// Status is the status of the response. Set false
	Status bool `json:"status"`
	// Message is a human-readable explanation of the status
	Message string `json:"message"`
	// StatusCode is the GoErr status
	StatusCode int `json:"status_code"`
}

// GetBalanceOwnerByIDV1
// @Summary GetBalanceOwnerByIDV1
// @Security bearerAuth
// @ID Billing_GetBalanceOwnerByIDV1
// @Accept json
// @Param request body GetBalanceOwnerByIDRequestV1 true "GetBalanceOwnerByIDRequestV1"
// @Success 200 {object} Billing_GetBalanceOwnerByIDV1_Success
// @Failure 401 {object} Billing_GetBalanceOwnerByIDV1_Failure "goerrpkg.ErrorTypeAuthorization"
// @Failure 404 {object} Billing_GetBalanceOwnerByIDV1_Failure "goerrpkg.ErrorTypeNotFound"
// @Failure 400 {object} Billing_GetBalanceOwnerByIDV1_Failure "goerrpkg.ErrorTypeBadRequest"
// @Failure 500 {object} Billing_GetBalanceOwnerByIDV1_Failure "goerrpkg.ErrorTypeUnexpectedError"
// @Failure 400 {object} Billing_GetBalanceOwnerByIDV1_Failure "gin.ErrorTypeBind"
// @Failure 500 {object} Billing_GetBalanceOwnerByIDV1_Failure "Undefined error"
// @Produce json
// @Router /processing.billing.billing.Billing/GetBalanceOwnerByIDV1 [put]
// @tags Billing
func (s *ginBillingServer) GetBalanceOwnerByIDV1(c *gin.Context) error {
	ctx, span := dog.CreateSpan(c.Request.Context(), "GinBillingServer_GetBalanceOwnerByIDV1")
	defer span.End()

	var request GetBalanceOwnerByIDRequestV1
	if err := c.ShouldBindJSON(&request); err != nil {
		return err
	}

	ctx = metadata.NewOutgoingContext(ctx, metadata.MD(c.Request.Header))

	response, err := s.client.GetBalanceOwnerByIDV1(ctx, &request)
	if err != nil {
		return err
	}

	c.JSON(http.StatusOK, &Billing_GetBalanceOwnerByIDV1_Success{
		Status:     true,
		Message:    "Success",
		StatusCode: 0,
		Result:     response,
	})
	return nil
}

type Billing_GetEntityTypeByIDV1_Success struct {
	// Status is the status of the response. Set true
	Status bool `json:"status"`
	// Message is a human-readable explanation of the status
	Message string `json:"message"`
	// StatusCode is the GoErr status. Set 0
	StatusCode int `json:"status_code"`
	// Result is the result of the request
	Result *GetEntityTypeResponseV1 `json:"result"`
}

type Billing_GetEntityTypeByIDV1_Failure struct {
	// Status is the status of the response. Set false
	Status bool `json:"status"`
	// Message is a human-readable explanation of the status
	Message string `json:"message"`
	// StatusCode is the GoErr status
	StatusCode int `json:"status_code"`
}

// GetEntityTypeByIDV1
// @Summary GetEntityTypeByIDV1
// @Security bearerAuth
// @ID Billing_GetEntityTypeByIDV1
// @Accept json
// @Param request body GetEntityTypeByIDRequestV1 true "GetEntityTypeByIDRequestV1"
// @Success 200 {object} Billing_GetEntityTypeByIDV1_Success
// @Failure 401 {object} Billing_GetEntityTypeByIDV1_Failure "goerrpkg.ErrorTypeAuthorization"
// @Failure 404 {object} Billing_GetEntityTypeByIDV1_Failure "goerrpkg.ErrorTypeNotFound"
// @Failure 400 {object} Billing_GetEntityTypeByIDV1_Failure "goerrpkg.ErrorTypeBadRequest"
// @Failure 500 {object} Billing_GetEntityTypeByIDV1_Failure "goerrpkg.ErrorTypeUnexpectedError"
// @Failure 400 {object} Billing_GetEntityTypeByIDV1_Failure "gin.ErrorTypeBind"
// @Failure 500 {object} Billing_GetEntityTypeByIDV1_Failure "Undefined error"
// @Produce json
// @Router /processing.billing.billing.Billing/GetEntityTypeByIDV1 [put]
// @tags Billing
func (s *ginBillingServer) GetEntityTypeByIDV1(c *gin.Context) error {
	ctx, span := dog.CreateSpan(c.Request.Context(), "GinBillingServer_GetEntityTypeByIDV1")
	defer span.End()

	var request GetEntityTypeByIDRequestV1
	if err := c.ShouldBindJSON(&request); err != nil {
		return err
	}

	ctx = metadata.NewOutgoingContext(ctx, metadata.MD(c.Request.Header))

	response, err := s.client.GetEntityTypeByIDV1(ctx, &request)
	if err != nil {
		return err
	}

	c.JSON(http.StatusOK, &Billing_GetEntityTypeByIDV1_Success{
		Status:     true,
		Message:    "Success",
		StatusCode: 0,
		Result:     response,
	})
	return nil
}

type Billing_GetCountryCodeByIDV1_Success struct {
	// Status is the status of the response. Set true
	Status bool `json:"status"`
	// Message is a human-readable explanation of the status
	Message string `json:"message"`
	// StatusCode is the GoErr status. Set 0
	StatusCode int `json:"status_code"`
	// Result is the result of the request
	Result *GetCountryCodeResponseV1 `json:"result"`
}

type Billing_GetCountryCodeByIDV1_Failure struct {
	// Status is the status of the response. Set false
	Status bool `json:"status"`
	// Message is a human-readable explanation of the status
	Message string `json:"message"`
	// StatusCode is the GoErr status
	StatusCode int `json:"status_code"`
}

// GetCountryCodeByIDV1
// @Summary GetCountryCodeByIDV1
// @Security bearerAuth
// @ID Billing_GetCountryCodeByIDV1
// @Accept json
// @Param request body GetCountryCodeByIDRequestV1 true "GetCountryCodeByIDRequestV1"
// @Success 200 {object} Billing_GetCountryCodeByIDV1_Success
// @Failure 401 {object} Billing_GetCountryCodeByIDV1_Failure "goerrpkg.ErrorTypeAuthorization"
// @Failure 404 {object} Billing_GetCountryCodeByIDV1_Failure "goerrpkg.ErrorTypeNotFound"
// @Failure 400 {object} Billing_GetCountryCodeByIDV1_Failure "goerrpkg.ErrorTypeBadRequest"
// @Failure 500 {object} Billing_GetCountryCodeByIDV1_Failure "goerrpkg.ErrorTypeUnexpectedError"
// @Failure 400 {object} Billing_GetCountryCodeByIDV1_Failure "gin.ErrorTypeBind"
// @Failure 500 {object} Billing_GetCountryCodeByIDV1_Failure "Undefined error"
// @Produce json
// @Router /processing.billing.billing.Billing/GetCountryCodeByIDV1 [put]
// @tags Billing
func (s *ginBillingServer) GetCountryCodeByIDV1(c *gin.Context) error {
	ctx, span := dog.CreateSpan(c.Request.Context(), "GinBillingServer_GetCountryCodeByIDV1")
	defer span.End()

	var request GetCountryCodeByIDRequestV1
	if err := c.ShouldBindJSON(&request); err != nil {
		return err
	}

	ctx = metadata.NewOutgoingContext(ctx, metadata.MD(c.Request.Header))

	response, err := s.client.GetCountryCodeByIDV1(ctx, &request)
	if err != nil {
		return err
	}

	c.JSON(http.StatusOK, &Billing_GetCountryCodeByIDV1_Success{
		Status:     true,
		Message:    "Success",
		StatusCode: 0,
		Result:     response,
	})
	return nil
}

type Billing_GetBalanceByIDV1_Success struct {
	// Status is the status of the response. Set true
	Status bool `json:"status"`
	// Message is a human-readable explanation of the status
	Message string `json:"message"`
	// StatusCode is the GoErr status. Set 0
	StatusCode int `json:"status_code"`
	// Result is the result of the request
	Result *GetBalanceResponseV1 `json:"result"`
}

type Billing_GetBalanceByIDV1_Failure struct {
	// Status is the status of the response. Set false
	Status bool `json:"status"`
	// Message is a human-readable explanation of the status
	Message string `json:"message"`
	// StatusCode is the GoErr status
	StatusCode int `json:"status_code"`
}

// GetBalanceByIDV1
// @Summary GetBalanceByIDV1
// @Security bearerAuth
// @ID Billing_GetBalanceByIDV1
// @Accept json
// @Param request body GetBalanceByIDRequestV1 true "GetBalanceByIDRequestV1"
// @Success 200 {object} Billing_GetBalanceByIDV1_Success
// @Failure 401 {object} Billing_GetBalanceByIDV1_Failure "goerrpkg.ErrorTypeAuthorization"
// @Failure 404 {object} Billing_GetBalanceByIDV1_Failure "goerrpkg.ErrorTypeNotFound"
// @Failure 400 {object} Billing_GetBalanceByIDV1_Failure "goerrpkg.ErrorTypeBadRequest"
// @Failure 500 {object} Billing_GetBalanceByIDV1_Failure "goerrpkg.ErrorTypeUnexpectedError"
// @Failure 400 {object} Billing_GetBalanceByIDV1_Failure "gin.ErrorTypeBind"
// @Failure 500 {object} Billing_GetBalanceByIDV1_Failure "Undefined error"
// @Produce json
// @Router /processing.billing.billing.Billing/GetBalanceByIDV1 [put]
// @tags Billing
func (s *ginBillingServer) GetBalanceByIDV1(c *gin.Context) error {
	ctx, span := dog.CreateSpan(c.Request.Context(), "GinBillingServer_GetBalanceByIDV1")
	defer span.End()

	var request GetBalanceByIDRequestV1
	if err := c.ShouldBindJSON(&request); err != nil {
		return err
	}

	ctx = metadata.NewOutgoingContext(ctx, metadata.MD(c.Request.Header))

	response, err := s.client.GetBalanceByIDV1(ctx, &request)
	if err != nil {
		return err
	}

	c.JSON(http.StatusOK, &Billing_GetBalanceByIDV1_Success{
		Status:     true,
		Message:    "Success",
		StatusCode: 0,
		Result:     response,
	})
	return nil
}

type Billing_CheckHasBalanceV1_Success struct {
	// Status is the status of the response. Set true
	Status bool `json:"status"`
	// Message is a human-readable explanation of the status
	Message string `json:"message"`
	// StatusCode is the GoErr status. Set 0
	StatusCode int `json:"status_code"`
	// Result is the result of the request
	Result *emptypb.Empty `json:"result"`
}

type Billing_CheckHasBalanceV1_Failure struct {
	// Status is the status of the response. Set false
	Status bool `json:"status"`
	// Message is a human-readable explanation of the status
	Message string `json:"message"`
	// StatusCode is the GoErr status
	StatusCode int `json:"status_code"`
}

// CheckHasBalanceV1
// @Summary CheckHasBalanceV1
// @Security bearerAuth
// @ID Billing_CheckHasBalanceV1
// @Accept json
// @Param request body CheckHasBalanceRequestV1 true "CheckHasBalanceRequestV1"
// @Success 200 {object} Billing_CheckHasBalanceV1_Success
// @Failure 401 {object} Billing_CheckHasBalanceV1_Failure "goerrpkg.ErrorTypeAuthorization"
// @Failure 404 {object} Billing_CheckHasBalanceV1_Failure "goerrpkg.ErrorTypeNotFound"
// @Failure 400 {object} Billing_CheckHasBalanceV1_Failure "goerrpkg.ErrorTypeBadRequest"
// @Failure 500 {object} Billing_CheckHasBalanceV1_Failure "goerrpkg.ErrorTypeUnexpectedError"
// @Failure 400 {object} Billing_CheckHasBalanceV1_Failure "gin.ErrorTypeBind"
// @Failure 500 {object} Billing_CheckHasBalanceV1_Failure "Undefined error"
// @Produce json
// @Router /processing.billing.billing.Billing/CheckHasBalanceV1 [put]
// @tags Billing
func (s *ginBillingServer) CheckHasBalanceV1(c *gin.Context) error {
	ctx, span := dog.CreateSpan(c.Request.Context(), "GinBillingServer_CheckHasBalanceV1")
	defer span.End()

	var request CheckHasBalanceRequestV1
	if err := c.ShouldBindJSON(&request); err != nil {
		return err
	}

	ctx = metadata.NewOutgoingContext(ctx, metadata.MD(c.Request.Header))

	response, err := s.client.CheckHasBalanceV1(ctx, &request)
	if err != nil {
		return err
	}

	c.JSON(http.StatusOK, &Billing_CheckHasBalanceV1_Success{
		Status:     true,
		Message:    "Success",
		StatusCode: 0,
		Result:     response,
	})
	return nil
}

type Billing_CheckBalanceOwnerAccountExistsByBalanceOwnerIdAndAccountIdV1_Success struct {
	// Status is the status of the response. Set true
	Status bool `json:"status"`
	// Message is a human-readable explanation of the status
	Message string `json:"message"`
	// StatusCode is the GoErr status. Set 0
	StatusCode int `json:"status_code"`
	// Result is the result of the request
	Result *emptypb.Empty `json:"result"`
}

type Billing_CheckBalanceOwnerAccountExistsByBalanceOwnerIdAndAccountIdV1_Failure struct {
	// Status is the status of the response. Set false
	Status bool `json:"status"`
	// Message is a human-readable explanation of the status
	Message string `json:"message"`
	// StatusCode is the GoErr status
	StatusCode int `json:"status_code"`
}

// CheckBalanceOwnerAccountExistsByBalanceOwnerIdAndAccountIdV1
// @Summary CheckBalanceOwnerAccountExistsByBalanceOwnerIdAndAccountIdV1
// @Security bearerAuth
// @ID Billing_CheckBalanceOwnerAccountExistsByBalanceOwnerIdAndAccountIdV1
// @Accept json
// @Param request body CheckBalanceOwnerAccountExistsByBalanceOwnerIdAndAccountIdRequestV1 true "CheckBalanceOwnerAccountExistsByBalanceOwnerIdAndAccountIdRequestV1"
// @Success 200 {object} Billing_CheckBalanceOwnerAccountExistsByBalanceOwnerIdAndAccountIdV1_Success
// @Failure 401 {object} Billing_CheckBalanceOwnerAccountExistsByBalanceOwnerIdAndAccountIdV1_Failure "goerrpkg.ErrorTypeAuthorization"
// @Failure 404 {object} Billing_CheckBalanceOwnerAccountExistsByBalanceOwnerIdAndAccountIdV1_Failure "goerrpkg.ErrorTypeNotFound"
// @Failure 400 {object} Billing_CheckBalanceOwnerAccountExistsByBalanceOwnerIdAndAccountIdV1_Failure "goerrpkg.ErrorTypeBadRequest"
// @Failure 500 {object} Billing_CheckBalanceOwnerAccountExistsByBalanceOwnerIdAndAccountIdV1_Failure "goerrpkg.ErrorTypeUnexpectedError"
// @Failure 400 {object} Billing_CheckBalanceOwnerAccountExistsByBalanceOwnerIdAndAccountIdV1_Failure "gin.ErrorTypeBind"
// @Failure 500 {object} Billing_CheckBalanceOwnerAccountExistsByBalanceOwnerIdAndAccountIdV1_Failure "Undefined error"
// @Produce json
// @Router /processing.billing.billing.Billing/CheckBalanceOwnerAccountExistsByBalanceOwnerIdAndAccountIdV1 [put]
// @tags Billing
func (s *ginBillingServer) CheckBalanceOwnerAccountExistsByBalanceOwnerIdAndAccountIdV1(c *gin.Context) error {
	ctx, span := dog.CreateSpan(c.Request.Context(), "GinBillingServer_CheckBalanceOwnerAccountExistsByBalanceOwnerIdAndAccountIdV1")
	defer span.End()

	var request CheckBalanceOwnerAccountExistsByBalanceOwnerIdAndAccountIdRequestV1
	if err := c.ShouldBindJSON(&request); err != nil {
		return err
	}

	ctx = metadata.NewOutgoingContext(ctx, metadata.MD(c.Request.Header))

	response, err := s.client.CheckBalanceOwnerAccountExistsByBalanceOwnerIdAndAccountIdV1(ctx, &request)
	if err != nil {
		return err
	}

	c.JSON(http.StatusOK, &Billing_CheckBalanceOwnerAccountExistsByBalanceOwnerIdAndAccountIdV1_Success{
		Status:     true,
		Message:    "Success",
		StatusCode: 0,
		Result:     response,
	})
	return nil
}

type Billing_GetBalanceAccountByNumber_Success struct {
	// Status is the status of the response. Set true
	Status bool `json:"status"`
	// Message is a human-readable explanation of the status
	Message string `json:"message"`
	// StatusCode is the GoErr status. Set 0
	StatusCode int `json:"status_code"`
	// Result is the result of the request
	Result *GetBalanceAccountByNumberResponse `json:"result"`
}

type Billing_GetBalanceAccountByNumber_Failure struct {
	// Status is the status of the response. Set false
	Status bool `json:"status"`
	// Message is a human-readable explanation of the status
	Message string `json:"message"`
	// StatusCode is the GoErr status
	StatusCode int `json:"status_code"`
}

// GetBalanceAccountByNumber
// @Summary GetBalanceAccountByNumber
// @Security bearerAuth
// @ID Billing_GetBalanceAccountByNumber
// @Accept json
// @Param request body GetBalanceAccountByNumberRequest true "GetBalanceAccountByNumberRequest"
// @Success 200 {object} Billing_GetBalanceAccountByNumber_Success
// @Failure 401 {object} Billing_GetBalanceAccountByNumber_Failure "goerrpkg.ErrorTypeAuthorization"
// @Failure 404 {object} Billing_GetBalanceAccountByNumber_Failure "goerrpkg.ErrorTypeNotFound"
// @Failure 400 {object} Billing_GetBalanceAccountByNumber_Failure "goerrpkg.ErrorTypeBadRequest"
// @Failure 500 {object} Billing_GetBalanceAccountByNumber_Failure "goerrpkg.ErrorTypeUnexpectedError"
// @Failure 400 {object} Billing_GetBalanceAccountByNumber_Failure "gin.ErrorTypeBind"
// @Failure 500 {object} Billing_GetBalanceAccountByNumber_Failure "Undefined error"
// @Produce json
// @Router /processing.billing.billing.Billing/GetBalanceAccountByNumber [put]
// @tags Billing
func (s *ginBillingServer) GetBalanceAccountByNumber(c *gin.Context) error {
	ctx, span := dog.CreateSpan(c.Request.Context(), "GinBillingServer_GetBalanceAccountByNumber")
	defer span.End()

	var request GetBalanceAccountByNumberRequest
	if err := c.ShouldBindJSON(&request); err != nil {
		return err
	}

	ctx = metadata.NewOutgoingContext(ctx, metadata.MD(c.Request.Header))

	response, err := s.client.GetBalanceAccountByNumber(ctx, &request)
	if err != nil {
		return err
	}

	c.JSON(http.StatusOK, &Billing_GetBalanceAccountByNumber_Success{
		Status:     true,
		Message:    "Success",
		StatusCode: 0,
		Result:     response,
	})
	return nil
}

type Billing_GetCurrentBalanceAmountByBalanceOwnerID_Success struct {
	// Status is the status of the response. Set true
	Status bool `json:"status"`
	// Message is a human-readable explanation of the status
	Message string `json:"message"`
	// StatusCode is the GoErr status. Set 0
	StatusCode int `json:"status_code"`
	// Result is the result of the request
	Result *GetCurrentBalanceAmountByAccountAndOwnerIDResponse `json:"result"`
}

type Billing_GetCurrentBalanceAmountByBalanceOwnerID_Failure struct {
	// Status is the status of the response. Set false
	Status bool `json:"status"`
	// Message is a human-readable explanation of the status
	Message string `json:"message"`
	// StatusCode is the GoErr status
	StatusCode int `json:"status_code"`
}

// GetCurrentBalanceAmountByBalanceOwnerID
// @Summary GetCurrentBalanceAmountByBalanceOwnerID
// @Security bearerAuth
// @ID Billing_GetCurrentBalanceAmountByBalanceOwnerID
// @Accept json
// @Param request body GetCurrentBalanceAmountByAccountAndOwnerIDRequest true "GetCurrentBalanceAmountByAccountAndOwnerIDRequest"
// @Success 200 {object} Billing_GetCurrentBalanceAmountByBalanceOwnerID_Success
// @Failure 401 {object} Billing_GetCurrentBalanceAmountByBalanceOwnerID_Failure "goerrpkg.ErrorTypeAuthorization"
// @Failure 404 {object} Billing_GetCurrentBalanceAmountByBalanceOwnerID_Failure "goerrpkg.ErrorTypeNotFound"
// @Failure 400 {object} Billing_GetCurrentBalanceAmountByBalanceOwnerID_Failure "goerrpkg.ErrorTypeBadRequest"
// @Failure 500 {object} Billing_GetCurrentBalanceAmountByBalanceOwnerID_Failure "goerrpkg.ErrorTypeUnexpectedError"
// @Failure 400 {object} Billing_GetCurrentBalanceAmountByBalanceOwnerID_Failure "gin.ErrorTypeBind"
// @Failure 500 {object} Billing_GetCurrentBalanceAmountByBalanceOwnerID_Failure "Undefined error"
// @Produce json
// @Router /processing.billing.billing.Billing/GetCurrentBalanceAmountByBalanceOwnerID [put]
// @tags Billing
func (s *ginBillingServer) GetCurrentBalanceAmountByBalanceOwnerID(c *gin.Context) error {
	ctx, span := dog.CreateSpan(c.Request.Context(), "GinBillingServer_GetCurrentBalanceAmountByBalanceOwnerID")
	defer span.End()

	var request GetCurrentBalanceAmountByAccountAndOwnerIDRequest
	if err := c.ShouldBindJSON(&request); err != nil {
		return err
	}

	ctx = metadata.NewOutgoingContext(ctx, metadata.MD(c.Request.Header))

	response, err := s.client.GetCurrentBalanceAmountByBalanceOwnerID(ctx, &request)
	if err != nil {
		return err
	}

	c.JSON(http.StatusOK, &Billing_GetCurrentBalanceAmountByBalanceOwnerID_Success{
		Status:     true,
		Message:    "Success",
		StatusCode: 0,
		Result:     response,
	})
	return nil
}

type Billing_CheckBalanceCreditExpireDate_Success struct {
	// Status is the status of the response. Set true
	Status bool `json:"status"`
	// Message is a human-readable explanation of the status
	Message string `json:"message"`
	// StatusCode is the GoErr status. Set 0
	StatusCode int `json:"status_code"`
	// Result is the result of the request
	Result *emptypb.Empty `json:"result"`
}

type Billing_CheckBalanceCreditExpireDate_Failure struct {
	// Status is the status of the response. Set false
	Status bool `json:"status"`
	// Message is a human-readable explanation of the status
	Message string `json:"message"`
	// StatusCode is the GoErr status
	StatusCode int `json:"status_code"`
}

// CheckBalanceCreditExpireDate
// @Summary jobs
// @Security bearerAuth
// @ID Billing_CheckBalanceCreditExpireDate
// @Accept json
// @Param request body emptypb.Empty true "Empty"
// @Success 200 {object} Billing_CheckBalanceCreditExpireDate_Success
// @Failure 401 {object} Billing_CheckBalanceCreditExpireDate_Failure "goerrpkg.ErrorTypeAuthorization"
// @Failure 404 {object} Billing_CheckBalanceCreditExpireDate_Failure "goerrpkg.ErrorTypeNotFound"
// @Failure 400 {object} Billing_CheckBalanceCreditExpireDate_Failure "goerrpkg.ErrorTypeBadRequest"
// @Failure 500 {object} Billing_CheckBalanceCreditExpireDate_Failure "goerrpkg.ErrorTypeUnexpectedError"
// @Failure 400 {object} Billing_CheckBalanceCreditExpireDate_Failure "gin.ErrorTypeBind"
// @Failure 500 {object} Billing_CheckBalanceCreditExpireDate_Failure "Undefined error"
// @Produce json
// @Router /processing.billing.billing.Billing/CheckBalanceCreditExpireDate [put]
// @tags Billing
func (s *ginBillingServer) CheckBalanceCreditExpireDate(c *gin.Context) error {
	ctx, span := dog.CreateSpan(c.Request.Context(), "GinBillingServer_CheckBalanceCreditExpireDate")
	defer span.End()

	var request emptypb.Empty
	if err := c.ShouldBindJSON(&request); err != nil {
		return err
	}

	ctx = metadata.NewOutgoingContext(ctx, metadata.MD(c.Request.Header))

	response, err := s.client.CheckBalanceCreditExpireDate(ctx, &request)
	if err != nil {
		return err
	}

	c.JSON(http.StatusOK, &Billing_CheckBalanceCreditExpireDate_Success{
		Status:     true,
		Message:    "Success",
		StatusCode: 0,
		Result:     response,
	})
	return nil
}

type Billing_CheckBalanceCreditStartDate_Success struct {
	// Status is the status of the response. Set true
	Status bool `json:"status"`
	// Message is a human-readable explanation of the status
	Message string `json:"message"`
	// StatusCode is the GoErr status. Set 0
	StatusCode int `json:"status_code"`
	// Result is the result of the request
	Result *emptypb.Empty `json:"result"`
}

type Billing_CheckBalanceCreditStartDate_Failure struct {
	// Status is the status of the response. Set false
	Status bool `json:"status"`
	// Message is a human-readable explanation of the status
	Message string `json:"message"`
	// StatusCode is the GoErr status
	StatusCode int `json:"status_code"`
}

// CheckBalanceCreditStartDate
// @Summary CheckBalanceCreditStartDate
// @Security bearerAuth
// @ID Billing_CheckBalanceCreditStartDate
// @Accept json
// @Param request body emptypb.Empty true "Empty"
// @Success 200 {object} Billing_CheckBalanceCreditStartDate_Success
// @Failure 401 {object} Billing_CheckBalanceCreditStartDate_Failure "goerrpkg.ErrorTypeAuthorization"
// @Failure 404 {object} Billing_CheckBalanceCreditStartDate_Failure "goerrpkg.ErrorTypeNotFound"
// @Failure 400 {object} Billing_CheckBalanceCreditStartDate_Failure "goerrpkg.ErrorTypeBadRequest"
// @Failure 500 {object} Billing_CheckBalanceCreditStartDate_Failure "goerrpkg.ErrorTypeUnexpectedError"
// @Failure 400 {object} Billing_CheckBalanceCreditStartDate_Failure "gin.ErrorTypeBind"
// @Failure 500 {object} Billing_CheckBalanceCreditStartDate_Failure "Undefined error"
// @Produce json
// @Router /processing.billing.billing.Billing/CheckBalanceCreditStartDate [put]
// @tags Billing
func (s *ginBillingServer) CheckBalanceCreditStartDate(c *gin.Context) error {
	ctx, span := dog.CreateSpan(c.Request.Context(), "GinBillingServer_CheckBalanceCreditStartDate")
	defer span.End()

	var request emptypb.Empty
	if err := c.ShouldBindJSON(&request); err != nil {
		return err
	}

	ctx = metadata.NewOutgoingContext(ctx, metadata.MD(c.Request.Header))

	response, err := s.client.CheckBalanceCreditStartDate(ctx, &request)
	if err != nil {
		return err
	}

	c.JSON(http.StatusOK, &Billing_CheckBalanceCreditStartDate_Success{
		Status:     true,
		Message:    "Success",
		StatusCode: 0,
		Result:     response,
	})
	return nil
}

type Billing_RecalculateProvisionalBalances_Success struct {
	// Status is the status of the response. Set true
	Status bool `json:"status"`
	// Message is a human-readable explanation of the status
	Message string `json:"message"`
	// StatusCode is the GoErr status. Set 0
	StatusCode int `json:"status_code"`
	// Result is the result of the request
	Result *emptypb.Empty `json:"result"`
}

type Billing_RecalculateProvisionalBalances_Failure struct {
	// Status is the status of the response. Set false
	Status bool `json:"status"`
	// Message is a human-readable explanation of the status
	Message string `json:"message"`
	// StatusCode is the GoErr status
	StatusCode int `json:"status_code"`
}

// RecalculateProvisionalBalances
// @Summary RecalculateProvisionalBalances
// @Security bearerAuth
// @ID Billing_RecalculateProvisionalBalances
// @Accept json
// @Param request body emptypb.Empty true "Empty"
// @Success 200 {object} Billing_RecalculateProvisionalBalances_Success
// @Failure 401 {object} Billing_RecalculateProvisionalBalances_Failure "goerrpkg.ErrorTypeAuthorization"
// @Failure 404 {object} Billing_RecalculateProvisionalBalances_Failure "goerrpkg.ErrorTypeNotFound"
// @Failure 400 {object} Billing_RecalculateProvisionalBalances_Failure "goerrpkg.ErrorTypeBadRequest"
// @Failure 500 {object} Billing_RecalculateProvisionalBalances_Failure "goerrpkg.ErrorTypeUnexpectedError"
// @Failure 400 {object} Billing_RecalculateProvisionalBalances_Failure "gin.ErrorTypeBind"
// @Failure 500 {object} Billing_RecalculateProvisionalBalances_Failure "Undefined error"
// @Produce json
// @Router /processing.billing.billing.Billing/RecalculateProvisionalBalances [put]
// @tags Billing
func (s *ginBillingServer) RecalculateProvisionalBalances(c *gin.Context) error {
	ctx, span := dog.CreateSpan(c.Request.Context(), "GinBillingServer_RecalculateProvisionalBalances")
	defer span.End()

	var request emptypb.Empty
	if err := c.ShouldBindJSON(&request); err != nil {
		return err
	}

	ctx = metadata.NewOutgoingContext(ctx, metadata.MD(c.Request.Header))

	response, err := s.client.RecalculateProvisionalBalances(ctx, &request)
	if err != nil {
		return err
	}

	c.JSON(http.StatusOK, &Billing_RecalculateProvisionalBalances_Success{
		Status:     true,
		Message:    "Success",
		StatusCode: 0,
		Result:     response,
	})
	return nil
}

type Billing_RecalculateFinalBalances_Success struct {
	// Status is the status of the response. Set true
	Status bool `json:"status"`
	// Message is a human-readable explanation of the status
	Message string `json:"message"`
	// StatusCode is the GoErr status. Set 0
	StatusCode int `json:"status_code"`
	// Result is the result of the request
	Result *emptypb.Empty `json:"result"`
}

type Billing_RecalculateFinalBalances_Failure struct {
	// Status is the status of the response. Set false
	Status bool `json:"status"`
	// Message is a human-readable explanation of the status
	Message string `json:"message"`
	// StatusCode is the GoErr status
	StatusCode int `json:"status_code"`
}

// RecalculateFinalBalances
// @Summary RecalculateFinalBalances
// @Security bearerAuth
// @ID Billing_RecalculateFinalBalances
// @Accept json
// @Param request body emptypb.Empty true "Empty"
// @Success 200 {object} Billing_RecalculateFinalBalances_Success
// @Failure 401 {object} Billing_RecalculateFinalBalances_Failure "goerrpkg.ErrorTypeAuthorization"
// @Failure 404 {object} Billing_RecalculateFinalBalances_Failure "goerrpkg.ErrorTypeNotFound"
// @Failure 400 {object} Billing_RecalculateFinalBalances_Failure "goerrpkg.ErrorTypeBadRequest"
// @Failure 500 {object} Billing_RecalculateFinalBalances_Failure "goerrpkg.ErrorTypeUnexpectedError"
// @Failure 400 {object} Billing_RecalculateFinalBalances_Failure "gin.ErrorTypeBind"
// @Failure 500 {object} Billing_RecalculateFinalBalances_Failure "Undefined error"
// @Produce json
// @Router /processing.billing.billing.Billing/RecalculateFinalBalances [put]
// @tags Billing
func (s *ginBillingServer) RecalculateFinalBalances(c *gin.Context) error {
	ctx, span := dog.CreateSpan(c.Request.Context(), "GinBillingServer_RecalculateFinalBalances")
	defer span.End()

	var request emptypb.Empty
	if err := c.ShouldBindJSON(&request); err != nil {
		return err
	}

	ctx = metadata.NewOutgoingContext(ctx, metadata.MD(c.Request.Header))

	response, err := s.client.RecalculateFinalBalances(ctx, &request)
	if err != nil {
		return err
	}

	c.JSON(http.StatusOK, &Billing_RecalculateFinalBalances_Success{
		Status:     true,
		Message:    "Success",
		StatusCode: 0,
		Result:     response,
	})
	return nil
}

type Billing_RecalculateCreditBalances_Success struct {
	// Status is the status of the response. Set true
	Status bool `json:"status"`
	// Message is a human-readable explanation of the status
	Message string `json:"message"`
	// StatusCode is the GoErr status. Set 0
	StatusCode int `json:"status_code"`
	// Result is the result of the request
	Result *emptypb.Empty `json:"result"`
}

type Billing_RecalculateCreditBalances_Failure struct {
	// Status is the status of the response. Set false
	Status bool `json:"status"`
	// Message is a human-readable explanation of the status
	Message string `json:"message"`
	// StatusCode is the GoErr status
	StatusCode int `json:"status_code"`
}

// RecalculateCreditBalances
// @Summary RecalculateCreditBalances
// @Security bearerAuth
// @ID Billing_RecalculateCreditBalances
// @Accept json
// @Param request body emptypb.Empty true "Empty"
// @Success 200 {object} Billing_RecalculateCreditBalances_Success
// @Failure 401 {object} Billing_RecalculateCreditBalances_Failure "goerrpkg.ErrorTypeAuthorization"
// @Failure 404 {object} Billing_RecalculateCreditBalances_Failure "goerrpkg.ErrorTypeNotFound"
// @Failure 400 {object} Billing_RecalculateCreditBalances_Failure "goerrpkg.ErrorTypeBadRequest"
// @Failure 500 {object} Billing_RecalculateCreditBalances_Failure "goerrpkg.ErrorTypeUnexpectedError"
// @Failure 400 {object} Billing_RecalculateCreditBalances_Failure "gin.ErrorTypeBind"
// @Failure 500 {object} Billing_RecalculateCreditBalances_Failure "Undefined error"
// @Produce json
// @Router /processing.billing.billing.Billing/RecalculateCreditBalances [put]
// @tags Billing
func (s *ginBillingServer) RecalculateCreditBalances(c *gin.Context) error {
	ctx, span := dog.CreateSpan(c.Request.Context(), "GinBillingServer_RecalculateCreditBalances")
	defer span.End()

	var request emptypb.Empty
	if err := c.ShouldBindJSON(&request); err != nil {
		return err
	}

	ctx = metadata.NewOutgoingContext(ctx, metadata.MD(c.Request.Header))

	response, err := s.client.RecalculateCreditBalances(ctx, &request)
	if err != nil {
		return err
	}

	c.JSON(http.StatusOK, &Billing_RecalculateCreditBalances_Success{
		Status:     true,
		Message:    "Success",
		StatusCode: 0,
		Result:     response,
	})
	return nil
}
