package main

import (
	"google.golang.org/protobuf/compiler/protogen"
)

var (
	// Protobuf
	anypbPackage = protogen.GoImportPath("google.golang.org/protobuf/types/known/anypb")
	anypbNew     = anypbPackage.Ident("New")

	timestampPackage = protogen.GoImportPath("google.golang.org/protobuf/types/known/timestamppb")
	timestampNow     = timestampPackage.Ident("Now")

	// MVP-specific PB
	pbPackage   = protogen.GoImportPath("git.local/sensitive/mvp/pb")
	pbEventInfo = pbPackage.Ident("EventInfo")

	// NATS
	natsPackage             = protogen.GoImportPath("github.com/nats-io/nats.go")
	natsJetStreamContext    = natsPackage.Ident("JetStreamContext")
	natsConn                = natsPackage.Ident("Conn")
	natsMsg                 = natsPackage.Ident("Msg")
	natsNewMsg              = natsPackage.Ident("NewMsg")
	natsHeader              = natsPackage.Ident("Header")
	natsStreamConfig        = natsPackage.Ident("StreamConfig")
	natsConsumerConfig      = natsPackage.Ident("ConsumerConfig")
	natsManualAck           = natsPackage.Ident("ManualAck")
	natsDeliverAll          = natsPackage.Ident("DeliverAll")
	natsAckExplicit         = natsPackage.Ident("AckExplicit")
	natsBind                = natsPackage.Ident("Bind")
	natsMaxDeliver          = natsPackage.Ident("MaxDeliver")
	natsFileStorage         = natsPackage.Ident("FileStorage")
	natsLimitsPolicy        = natsPackage.Ident("LimitsPolicy")
	natsDiscardOld          = natsPackage.Ident("DiscardOld")
	natsAckExplicitPolicy   = natsPackage.Ident("AckExplicitPolicy")
	natsErrStreamNotFound   = natsPackage.Ident("ErrStreamNotFound")
	natsErrConsumerNotFound = natsPackage.Ident("ErrConsumerNotFound")
	natsSubOpt              = natsPackage.Ident("SubOpt")

	// Watermill
	watermillPackage       = protogen.GoImportPath("github.com/ThreeDotsLabs/watermill")
	watermillLogFields     = watermillPackage.Ident("LogFields")
	watermillLoggerAdapter = watermillPackage.Ident("LoggerAdapter")

	watermillMessagePackage = protogen.GoImportPath("github.com/ThreeDotsLabs/watermill/message")
	watermillRouter         = watermillMessagePackage.Ident("Router")
	watermillNewMessage     = watermillMessagePackage.Ident("NewMessage")
	watermillMessage        = watermillMessagePackage.Ident("Message")

	watermillNatsPackage                  = protogen.GoImportPath("github.com/ThreeDotsLabs/watermill-nats/v2/pkg/nats")
	watermillNewSubscriberWithNatsConn    = watermillNatsPackage.Ident("NewSubscriberWithNatsConn")
	watermillSubscriberSubscriptionConfig = watermillNatsPackage.Ident("SubscriberSubscriptionConfig")
	watermillSubjectDetail                = watermillNatsPackage.Ident("SubjectDetail")
	watermillJetStreamConfig              = watermillNatsPackage.Ident("JetStreamConfig")

	// OpenTelemetry
	otelPackage              = protogen.GoImportPath("go.opentelemetry.io/otel")
	otelTracer               = otelPackage.Ident("Tracer")
	otelGetTextMapPropagator = otelPackage.Ident("GetTextMapPropagator")
	otelAttributePackage     = protogen.GoImportPath("go.opentelemetry.io/otel/attribute")
	otelAttrBool             = otelAttributePackage.Ident("Bool")
	otelAttrString           = otelAttributePackage.Ident("String")
	otelPropagationPackage   = protogen.GoImportPath("go.opentelemetry.io/otel/propagation")
	otelHeaderCarrier        = otelPropagationPackage.Ident("HeaderCarrier")

	// Gin
	ginPackage     = protogen.GoImportPath("github.com/gin-gonic/gin")
	ginRouterGroup = ginPackage.Ident("RouterGroup")
	ginContext     = ginPackage.Ident("Context")
	ginHandlerFunc = ginPackage.Ident("HandlerFunc")

	// Metadata
	metadataPackage                    = protogen.GoImportPath("google.golang.org/grpc/metadata")
	fromIncomingContextMetadataPackage = metadataPackage.Ident("FromIncomingContext")
	metadataPackageNewOutgoingContext  = metadataPackage.Ident("NewOutgoingContext")
	metadataPackageMD                  = metadataPackage.Ident("MD")

	dogPackage                      = protogen.GoImportPath("git.local/sensitive/sdk/dog")
	dogPackageCreateSpan            = dogPackage.Ident("CreateSpan")
	dogPackageRegisterDBHealthCheck = dogPackage.Ident("RegisterDBHealthCheck")
	dogPackageInitOpenAPI           = dogPackage.Ident("InitOpenAPI")
	dogPackageCancel                = dogPackage.Ident("Cancel")
	dogPackageConfigString          = dogPackage.Ident("ConfigString")
	dogPackageConfigInt             = dogPackage.Ident("ConfigInt")
	dogPackageConfigFloat           = dogPackage.Ident("ConfigFloat")
	dogPackageMicroService          = dogPackage.Ident("MicroService")
	dogPackageGinEngine             = dogPackage.Ident("GinEngine")
	dogPackageGrpcServer            = dogPackage.Ident("GrpcServer")
	dogPackageCtx                   = dogPackage.Ident("Ctx")
	dogNatsMessageRouter            = dogPackage.Ident("NatsMessageRouter")

	netHttpPackage                         = protogen.GoImportPath("net/http")
	netHttpPackageStatusOK                 = netHttpPackage.Ident("StatusOK")
	netHttpPackageStatusServiceUnavailable = netHttpPackage.Ident("StatusServiceUnavailable")

	protoJsonPackage                 = protogen.GoImportPath("google.golang.org/protobuf/encoding/protojson")
	protoJsonPackageUnmarshal        = protoJsonPackage.Ident("Unmarshal")
	protoJsonPackageMarshal          = protoJsonPackage.Ident("Marshal")
	protoJsonPackageUnmarshalOptions = protoJsonPackage.Ident("UnmarshalOptions")
	protoJsonPackageMarshalOptions   = protoJsonPackage.Ident("MarshalOptions")

	goErrPackage      = protogen.GoImportPath("git.local/sensitive/mvp/pkg/errors")
	goErrPackageGoErr = goErrPackage.Ident("GoErr")

	codesPackage      = protogen.GoImportPath("google.golang.org/grpc/codes")
	codesPackageEmpty = codesPackage.Ident("")

	structPbPackage               = protogen.GoImportPath("google.golang.org/protobuf/types/known/structpb")
	structPbPackageStruct         = structPbPackage.Ident("Struct")
	structPbPackageValue          = structPbPackage.Ident("Value")
	structPbPackageNewNumberValue = structPbPackage.Ident("NewNumberValue")
	structPbPackageNewStringValue = structPbPackage.Ident("NewStringValue")
	structPbPackageNewBoolValue   = structPbPackage.Ident("NewBoolValue")

	statusPackage    = protogen.GoImportPath("google.golang.org/grpc/status")
	statusPackageNew = statusPackage.Ident("New")

	httptestPackage          = protogen.GoImportPath("net/http/httptest")
	httptestPackageNewServer = httptestPackage.Ident("NewServer")

	requirePackage  = protogen.GoImportPath("github.com/stretchr/testify/require")
	requireEqual    = requirePackage.Ident("Equal")
	requireNoError  = requirePackage.Ident("NoError")
	requireNotNil   = requirePackage.Ident("NotNil")
	requireTrue     = requirePackage.Ident("True")
	testingPackage  = protogen.GoImportPath("testing")
	testingPackageT = testingPackage.Ident("T")
	testingPackageM = testingPackage.Ident("M")

	errorsPackage    = protogen.GoImportPath("errors")
	errorsPackageNew = errorsPackage.Ident("New")
	errorsIs         = errorsPackage.Ident("Is")

	osPackage     = protogen.GoImportPath("os")
	osPackageExit = osPackage.Ident("Exit")

	ctxPackage              = protogen.GoImportPath("context")
	contextContextTODO      = ctxPackage.Ident("TODO")
	contextContextIdent     = ctxPackage.Ident("Context")
	contextWithTimeoutIdent = ctxPackage.Ident("WithTimeout")

	zapPackage = protogen.GoImportPath("go.uber.org/zap")
	zapAny     = zapPackage.Ident("Any")
	zapTime    = zapPackage.Ident("Time")
	zapField   = zapPackage.Ident("Field")
	zapDict    = zapPackage.Ident("Dict")
	zapReflect = zapPackage.Ident("Reflect")
	zapL       = zapPackage.Ident("L()")
	zapLogger  = zapPackage.Ident("Logger")
	zapError   = zapPackage.Ident("Error")
	zapString  = zapPackage.Ident("String")

	fmtPackage = protogen.GoImportPath("fmt")
	fmtSprintf = fmtPackage.Ident("Sprintf")
	fmtSprint  = fmtPackage.Ident("Sprint")

	cntxPackage = protogen.GoImportPath("git.local/sensitive/mvp/pkg/cntx")
	cntxBegin   = cntxPackage.Ident("Begin")
	cntxEnd     = cntxPackage.Ident("End")

	randPackage       = protogen.GoImportPath("math/rand")
	randPackageSeed   = randPackage.Ident("Seed")
	randPackageInt63n = randPackage.Ident("Int63n")

	// doc begin
	docPackage          = protogen.GoImportPath("git.local/sensitive/mvp/pkg/doc")
	docPackageFunc      = docPackage.Ident("Func")
	docPackageStruct    = docPackage.Ident("Struct")
	docPackageNewStruct = docPackage.Ident("NewStruct")
	// doc end

	// form begin
	formPackage               = protogen.GoImportPath("git.local/sensitive/mvp/pkg/convert/form")
	formPackageMap            = formPackage.Ident("Map")
	formPackageParse          = formPackage.Ident("Parse")
	formPackageParseEnum      = formPackage.Ident("ParseEnum")
	formPackageParseEnumPtr   = formPackage.Ident("ParseEnumPtr")
	formPackageParseInt       = formPackage.Ident("ParseInt")
	formPackageParseIntPtr    = formPackage.Ident("ParseIntPtr")
	formPackageParseUint      = formPackage.Ident("ParseUint")
	formPackageParseUintPtr   = formPackage.Ident("ParseUintPtr")
	formPackageParseFloat     = formPackage.Ident("ParseFloat")
	formPackageParseFloatPtr  = formPackage.Ident("ParseFloatPtr")
	formPackageParseBool      = formPackage.Ident("ParseBool")
	formPackageParseBoolPtr   = formPackage.Ident("ParseBoolPtr")
	formPackageParseString    = formPackage.Ident("ParseString")
	formPackageParseStringPtr = formPackage.Ident("ParseStringPtr")
	formPackageParseBytes     = formPackage.Ident("ParseBytes")
	formPackageParseBytesPtr  = formPackage.Ident("ParseBytesPtr")

	// form end
	// wraps begin
	wrapsPackage = protogen.GoImportPath("git.local/sensitive/mvp/pkg/convert/wraps")

	wrapsUnmarshalJSONPtr = wrapsPackage.Ident("UnmarshalJSONPtr")
	wrapsUnmarshalJSON    = wrapsPackage.Ident("UnmarshalJSON")
	wrapsUnmarshal_Proto  = wrapsPackage.Ident("Unmarshal_Proto")
	wrapsParseInt         = wrapsPackage.Ident("ParseInt")
	wrapsParseIntPtr      = wrapsPackage.Ident("ParseIntPtr")
	wrapsParseUint        = wrapsPackage.Ident("ParseUint")
	wrapsParseUintPtr     = wrapsPackage.Ident("ParseUintPtr")
	wrapsParseFloat       = wrapsPackage.Ident("ParseFloat")
	wrapsParseFloatPtr    = wrapsPackage.Ident("ParseFloatPtr")
	wrapsParseBool        = wrapsPackage.Ident("ParseBool")
	wrapsParseBoolPtr     = wrapsPackage.Ident("ParseBoolPtr")
	wrapsParseEnum        = wrapsPackage.Ident("ParseEnum")
	wrapsParseEnumPtr     = wrapsPackage.Ident("ParseEnumPtr")
	wrapsParseString      = wrapsPackage.Ident("ParseString")
	wrapsParseStringPtr   = wrapsPackage.Ident("ParseStringPtr")
	wrapsParseBytes       = wrapsPackage.Ident("ParseBytes")
	wrapsParseBytesPtr    = wrapsPackage.Ident("ParseBytesPtr")
	// wraps end

	// begin values
	valuesPackage = protogen.GoImportPath("git.local/sensitive/mvp/pkg/convert/values")

	valuesParseSkipError       = valuesPackage.Ident("ParseSkipError")
	valuesParseStringSkipError = valuesPackage.Ident("ParseStringSkipError")
	valuesParse                = valuesPackage.Ident("Parse")
	valuesParseString          = valuesPackage.Ident("ParseString")
	valuesInt                  = valuesPackage.Ident("Int")
	valuesUint                 = valuesPackage.Ident("Uint")
	valuesFloat                = valuesPackage.Ident("Float")
	valuesBool                 = valuesPackage.Ident("Bool")
	valuesString               = valuesPackage.Ident("String")
	valuesBytes                = valuesPackage.Ident("Bytes")
	valuesEnum                 = valuesPackage.Ident("Enum")
	valuesNewFromString        = valuesPackage.Ident("NewFromString")
	valuesNew                  = valuesPackage.Ident("New")
	valuesIntStruct            = valuesPackage.Ident("IntStruct")
	valuesUintStruct           = valuesPackage.Ident("UintStruct")
	valuesFloatStruct          = valuesPackage.Ident("FloatStruct")
	valuesBoolStruct           = valuesPackage.Ident("BoolStruct")
	valuesStringStruct         = valuesPackage.Ident("StringStruct")
	valuesBytesStruct          = valuesPackage.Ident("BytesStruct")
	valuesEnumStruct           = valuesPackage.Ident("EnumStruct")
	// end values

	testHelpersPackage    = protogen.GoImportPath("git.local/sensitive/mvp/pkg/testhelpers")
	testHelpersPackagePtr = testHelpersPackage.Ident("Ptr")

	marshallerPackage                = protogen.GoImportPath("git.local/sensitive/mvp/pkg/marshaller")
	marshallerPackageSlice           = marshallerPackage.Ident("Slice")
	marshallerPackageMap             = marshallerPackage.Ident("Map")
	marshallerPackageXMLUnmarshal    = marshallerPackage.Ident("XMLUnmarshal")
	marshallerPackageXMLUnmarshalPtr = marshallerPackage.Ident("XMLUnmarshalPtr")

	timePackage            = protogen.GoImportPath("time")
	timeNow                = timePackage.Ident("Now")
	timePackageSecond      = timePackage.Ident("Second")
	timePackageMinute      = timePackage.Ident("Minute")
	timePackageTime        = timePackage.Ident("Time")
	timePackageAfter       = timePackage.Ident("After")
	timePackageMillisecond = timePackage.Ident("Millisecond")
	timeHour               = timePackage.Ident("Hour")
	timePackageNewTicker   = timePackage.Ident("NewTicker")

	ctxZapPackage   = protogen.GoImportPath("github.com/grpc-ecosystem/go-grpc-middleware/logging/zap/ctxzap")
	ctxZapAddFields = ctxZapPackage.Ident("AddFields")

	insecurePackage               = protogen.GoImportPath("google.golang.org/grpc/credentials/insecure")
	insecurePackageNewCredentials = insecurePackage.Ident("NewCredentials")

	grpcPackage                         = protogen.GoImportPath("google.golang.org/grpc")
	grpcPackageServiceRegistrar         = grpcPackage.Ident("ServiceRegistrar")
	grpcPackageNewClient                = grpcPackage.Ident("NewClient")
	grpcPackageWithTransportCredentials = grpcPackage.Ident("WithTransportCredentials")
	grpcPackageWithStatsHandler         = grpcPackage.Ident("WithStatsHandler")
	grpcPackageCallOption               = grpcPackage.Ident("CallOption")
	grpcPackageDialOption               = grpcPackage.Ident("DialOption")
	grpcPackageClientConnInterface      = grpcPackage.Ident("ClientConnInterface")
	grpcPackageWithUnaryInterceptor     = grpcPackage.Ident("WithUnaryInterceptor")

	stringsPackage          = protogen.GoImportPath("strings")
	stringsPackageNewReader = stringsPackage.Ident("NewReader")
	toLower                 = stringsPackage.Ident("ToLower")
	stringsPackageJoin      = stringsPackage.Ident("Join")
	stringsReplaceAll       = stringsPackage.Ident("ReplaceAll")

	mathPackage         = protogen.GoImportPath("math")
	minInt32            = mathPackage.Ident("MinInt32")
	mathPackageMaxInt64 = mathPackage.Ident("MaxInt64")

	ioPackage        = protogen.GoImportPath("io")
	ioPackageReader  = ioPackage.Ident("Reader")
	ioPackageReadAll = ioPackage.Ident("ReadAll")

	bytesPackage          = protogen.GoImportPath("bytes")
	bytesPackageNewReader = bytesPackage.Ident("NewReader")
	bytesPackageBuffer    = bytesPackage.Ident("Buffer")

	uuidPackage     = protogen.GoImportPath("github.com/google/uuid")
	uuidPackageUUID = uuidPackage.Ident("UUID")
	uuidPackageNew  = uuidPackage.Ident("New")

	httpPackage                      = protogen.GoImportPath("net/http")
	httpPackageRequest               = httpPackage.Ident("Request")
	httpPackageMethod                = httpPackage.Ident("Method")
	httpPackageMethodPost            = httpPackage.Ident("MethodPost")
	httpPackageClient                = httpPackage.Ident("Client")
	httpPackageHandlerFunc           = httpPackage.Ident("HandlerFunc")
	httpPackageResponseWriter        = httpPackage.Ident("ResponseWriter")
	httpPackageResponse              = httpPackage.Ident("Response")
	httpPackageDefaultClientDo       = httpPackage.Ident("DefaultClient.Do")
	httpPackageNewRequestWithContext = httpPackage.Ident("NewRequestWithContext")

	protoPackage          = protogen.GoImportPath("google.golang.org/protobuf/proto")
	protoPackageMarshal   = protoPackage.Ident("Marshal")
	protoPackageUnmarshal = protoPackage.Ident("Unmarshal")

	mxjPackage    = protogen.GoImportPath("github.com/clbanning/mxj")
	mxjNewMapJson = mxjPackage.Ident("NewMapJson")
	mxjNewMapXml  = mxjPackage.Ident("NewMapXml")
	mxjMap        = mxjPackage.Ident("Map")

	multipartPackage   = protogen.GoImportPath("mime/multipart")
	multipartNewWriter = multipartPackage.Ident("NewWriter")
	multipartNewReader = multipartPackage.Ident("NewReader")
	multipartForm      = multipartPackage.Ident("Form")

	jsonPackage          = protogen.GoImportPath("encoding/json")
	jsonPackageMarshal   = jsonPackage.Ident("Marshal")
	jsonPackageUnmarshal = jsonPackage.Ident("Unmarshal")

	urlPackage           = protogen.GoImportPath("net/url")
	urlPackageValues     = urlPackage.Ident("Values")
	urlPackageParseQuery = urlPackage.Ident("ParseQuery")

	mimePackage               = protogen.GoImportPath("mime")
	mimePackageParseMediaType = mimePackage.Ident("ParseMediaType")

	otelgrpcPackage                 = protogen.GoImportPath("go.opentelemetry.io/contrib/instrumentation/google.golang.org/grpc/otelgrpc")
	otelgrpcPackageNewClientHandler = otelgrpcPackage.Ident("NewClientHandler")

	pkgerrorPackage                       = protogen.GoImportPath("git.local/sensitive/mvp/pkg/errors")
	pkgerrorPackageUnaryClientInterceptor = pkgerrorPackage.Ident("UnaryClientInterceptor")
	pkgerrorPackageEmpty                  = pkgerrorPackage.Ident("")

	grpcMiddlewareSentryPackage                       = protogen.GoImportPath("github.com/johnbellone/grpc-middleware-sentry")
	grpcMiddlewareSentryPackageUnaryClientInterceptor = grpcMiddlewareSentryPackage.Ident("UnaryClientInterceptor")

	goErrPackageUnaryServerInterceptor = goErrPackage.Ident("UnaryServerInterceptor")

	errorsPackageJoin = errorsPackage.Ident("Join")

	osGetEnv = osPackage.Ident("Getenv")
	osSignal = osPackage.Ident("Signal")

	signalPkg    = protogen.GoImportPath("os/signal")
	signalNotify = signalPkg.Ident("Notify")

	syscallPkg     = protogen.GoImportPath("syscall")
	syscallSigterm = syscallPkg.Ident("SIGTERM")
	syscallSigint  = syscallPkg.Ident("SIGINT")

	contextContext         = ctxPackage.Ident("Context")
	contextCancelFunc      = ctxPackage.Ident("CancelFunc")
	contextWithCancel      = ctxPackage.Ident("WithCancel")
	contextWithValue       = ctxPackage.Ident("WithValue")
	contextBackgroundIdent = ctxPackage.Ident("Background")
	contextDone            = ctxPackage.Ident("Done")

	zapNewProductionConfig = zapPackage.Ident("NewProductionConfig")
	zapRegisterEncoder     = zapPackage.Ident("RegisterEncoder")
	zapReplaceGlobals      = zapPackage.Ident("ReplaceGlobals")
	zapAddCallerSkip       = zapPackage.Ident("AddCallerSkip")

	zapcorePackage            = protogen.GoImportPath("go.uber.org/zap/zapcore")
	zapcoreNewJSONEncoder     = zapcorePackage.Ident("NewJSONEncoder")
	zapcoreEncoderConfig      = zapcorePackage.Ident("EncoderConfig")
	zapcoreEncoder            = zapcorePackage.Ident("Encoder")
	zapcoreParseLevel         = zapcorePackage.Ident("ParseLevel")
	zapcoreRFC3339TimeEncoder = zapcorePackage.Ident("RFC3339TimeEncoder")

	fmtErrorf = fmtPackage.Ident("Errorf")

	cntxNewCounter = cntxPackage.Ident("NewCounter")

	timeDuration = timePackage.Ident("Duration")
	timeNewTimer = timePackage.Ident("NewTimer")

	ctxZapToContext = ctxZapPackage.Ident("ToContext")
	ctxZapError     = ctxZapPackage.Ident("Error")
	ctxZapDebug     = ctxZapPackage.Ident("Debug")

	grpcPackageServer                = grpcPackage.Ident("Server")
	grpcPackageUnaryServerInfo       = grpcPackage.Ident("UnaryServerInfo")
	grpcPackageUnaryHandler          = grpcPackage.Ident("UnaryHandler")
	grpcPackageServerOption          = grpcPackage.Ident("ServerOption")
	grpcPackageStatsHandler          = grpcPackage.Ident("StatsHandler")
	grpcPackageChainUnaryInterceptor = grpcPackage.Ident("ChainUnaryInterceptor")
	grpcPackageUnaryInterceptor      = grpcPackage.Ident("UnaryInterceptor")
	grpcPackageNewServer             = grpcPackage.Ident("NewServer")
	grpcPackageStreamInterceptor     = grpcPackage.Ident("StreamInterceptor")

	stringsSplit     = stringsPackage.Ident("Split")
	stringsHasPrefix = stringsPackage.Ident("HasPrefix")

	protojsonPackage          = protogen.GoImportPath("google.golang.org/protobuf/encoding/protojson")
	protojsonPackageMarshal   = protojsonPackage.Ident("Marshal")
	protojsonPackageUnmarshal = protojsonPackage.Ident("Unmarshal")

	ioPackageNopCloser = ioPackage.Ident("NopCloser")

	bytesPackageNewBuffer       = bytesPackage.Ident("NewBuffer")
	bytesPackageNewBufferString = bytesPackage.Ident("NewBufferString")

	httpPackageStatusInternalServerError = httpPackage.Ident("StatusInternalServerError")
	httpPackageStatusBadRequest          = httpPackage.Ident("StatusBadRequest")
	httpPackageStatusOK                  = httpPackage.Ident("StatusOK")

	httputilPackage            = protogen.GoImportPath("net/http/httputil")
	httputilPackageDumpRequest = httputilPackage.Ident("DumpRequest")

	otelgrpcPackageNewServerHandler = otelgrpcPackage.Ident("NewServerHandler")
	otelgrpcPackageServerOption     = otelgrpcPackage.Ident("ServerOption")

	grpcMiddlewareSentryPackageUnaryServerInterceptor  = grpcMiddlewareSentryPackage.Ident("UnaryServerInterceptor")
	grpcMiddlewareSentryPackageStreamServerInterceptor = grpcMiddlewareSentryPackage.Ident("StreamServerInterceptor")

	vaultPackage                     = protogen.GoImportPath("github.com/hashicorp/vault/api")
	vaultConfig                      = vaultPackage.Ident("Config")
	vaultDefaultConfig               = vaultPackage.Ident("DefaultConfig")
	vaultClient                      = vaultPackage.Ident("Client")
	vaultNewClient                   = vaultPackage.Ident("NewClient")
	vaultK8sAuth                     = protogen.GoImportPath("github.com/hashicorp/vault/api/auth/kubernetes")
	vaultNewKubernetesAuth           = vaultK8sAuth.Ident("NewKubernetesAuth")
	vaultWithServiceAccountTokenPath = vaultK8sAuth.Ident("WithServiceAccountTokenPath")

	mongoPkg            = protogen.GoImportPath("go.mongodb.org/mongo-driver/mongo")
	mongoConnect        = mongoPkg.Ident("Connect")
	mongoClient         = mongoPkg.Ident("Client")
	mongoOptions        = protogen.GoImportPath("go.mongodb.org/mongo-driver/mongo/options")
	mongoOptionsClient  = mongoOptions.Ident("Client")
	mongoOtel           = protogen.GoImportPath("go.opentelemetry.io/contrib/instrumentation/go.mongodb.org/mongo-driver/mongo/otelmongo")
	mongoOtelNewMonitor = mongoOtel.Ident("NewMonitor")

	redisPkg                   = protogen.GoImportPath("github.com/redis/go-redis/v9")
	redisClient                = redisPkg.Ident("Client")
	redisNewClient             = redisPkg.Ident("NewClient")
	redisOptions               = redisPkg.Ident("Options")
	redisOtel                  = protogen.GoImportPath("github.com/redis/go-redis/extra/redisotel/v9")
	redisOtelInstrumentTracing = redisOtel.Ident("InstrumentTracing")

	gormPackage = protogen.GoImportPath("gorm.io/gorm")
	gormDB      = gormPackage.Ident("DB")
	gormOpen    = gormPackage.Ident("Open")

	netPkg    = protogen.GoImportPath("net")
	netConn   = netPkg.Ident("Conn")
	netDial   = netPkg.Ident("Dial")
	netListen = netPkg.Ident("Listen")

	tracePkg                    = protogen.GoImportPath("go.opentelemetry.io/otel/trace")
	traceWithSpanKind           = tracePkg.Ident("WithSpanKind")
	traceSpanContextFromContext = tracePkg.Ident("SpanContextFromContext")
	traceSpanKindConsumer       = tracePkg.Ident("SpanKindConsumer")
	traceTracer                 = tracePkg.Ident("Tracer")
	traceSpanFromContext        = tracePkg.Ident("SpanFromContext")
	jaegerPkg                   = protogen.GoImportPath("go.opentelemetry.io/otel/exporters/jaeger")
	jaegerNew                   = jaegerPkg.Ident("New")
	jaegerWithCollectorEndpoint = jaegerPkg.Ident("WithCollectorEndpoint")
	jaegerWithEndpoint          = jaegerPkg.Ident("WithEndpoint")
	noop                        = protogen.GoImportPath("go.opentelemetry.io/otel/trace/noop")
	noopTracer                  = noop.Ident("Tracer")

	sdktrace                  = protogen.GoImportPath("go.opentelemetry.io/otel/sdk/trace")
	sdktraceNewTracerProvider = sdktrace.Ident("NewTracerProvider")
	sdktraceWithBatcher       = sdktrace.Ident("WithBatcher")
	sdktraceWithResource      = sdktrace.Ident("WithResource")
	sdktraceWithSpanProcessor = sdktrace.Ident("WithSpanProcessor")

	resourcePkg               = protogen.GoImportPath("go.opentelemetry.io/otel/sdk/resource")
	resourceNewWithAttributes = resourcePkg.Ident("NewWithAttributes")

	semconvPkg                           = protogen.GoImportPath("go.opentelemetry.io/otel/semconv/v1.7.0")
	semconvSchemaURL                     = semconvPkg.Ident("SchemaURL")
	semconvServiceNameKey                = semconvPkg.Ident("ServiceNameKey")
	semconvDeploymentEnvironmentKey      = semconvPkg.Ident("DeploymentEnvironmentKey")
	semconvMessagingDestinationKindTopic = semconvPkg.Ident("MessagingDestinationKindTopic")
	semconvMessagingDestinationKey       = semconvPkg.Ident("MessagingDestinationKey")
	semconvMessagingOperationReceive     = semconvPkg.Ident("MessagingOperationReceive")

	sentryotelPkg                    = protogen.GoImportPath("github.com/getsentry/sentry-go/otel")
	sentryotelNewSentrySpanProcessor = sentryotelPkg.Ident("NewSentrySpanProcessor")
	sentryotelNewSentryPropagator    = sentryotelPkg.Ident("NewSentryPropagator")

	sentryPkg               = protogen.GoImportPath("github.com/getsentry/sentry-go")
	sentryInit              = sentryPkg.Ident("Init")
	sentryClientOptions     = sentryPkg.Ident("ClientOptions")
	sentryCurrentHub        = sentryPkg.Ident("CurrentHub")
	sentrySetHubOnContext   = sentryPkg.Ident("SetHubOnContext")
	sentryGetHubFromContext = sentryPkg.Ident("GetHubFromContext")
	sentryGetHubFromFlush   = sentryPkg.Ident("Flush")

	sentryGinPkg     = protogen.GoImportPath("github.com/getsentry/sentry-go/gin")
	sentryGinNew     = sentryPkg.Ident("New")
	sentryGinOptions = sentryPkg.Ident("Options")

	otelPkg                  = protogen.GoImportPath("go.opentelemetry.io/otel")
	otelSetTextMapPropagator = otelPkg.Ident("SetTextMapPropagator")
	otelSetTracerProvider    = otelPkg.Ident("SetTracerProvider")

	otelAttributePkg    = protogen.GoImportPath("go.opentelemetry.io/otel/attribute")
	otelAttributeBool   = otelAttributePkg.Ident("Bool")
	otelAttributeString = otelAttributePkg.Ident("String")

	propagationPkg                           = protogen.GoImportPath("go.opentelemetry.io/otel/propagation")
	propagationNewCompositeTextMapPropagator = propagationPkg.Ident("NewCompositeTextMapPropagator")
	propagationTraceContext                  = propagationPkg.Ident("TraceContext")
	propagationBaggage                       = propagationPkg.Ident("Baggage")
	propagationMapCarrier                    = propagationPkg.Ident("MapCarrier")

	syncPkg     = protogen.GoImportPath("sync")
	syncRWMutex = syncPkg.Ident("RWMutex")

	prometheusPkg                   = protogen.GoImportPath("github.com/prometheus/client_golang/prometheus")
	prometheusLabels                = prometheusPkg.Ident("Labels")
	prometheusNewRegistry           = prometheusPkg.Ident("NewRegistry")
	prometheusRegistry              = prometheusPkg.Ident("Registry")
	prometheusCollectorsPkg         = protogen.GoImportPath("github.com/prometheus/client_golang/prometheus/collectors")
	prometheusNewGoCollector        = prometheusCollectorsPkg.Ident("NewGoCollector")
	prometheusNewProcessCollector   = prometheusCollectorsPkg.Ident("NewProcessCollector")
	prometheusNewBuildInfoCollector = prometheusCollectorsPkg.Ident("NewBuildInfoCollector")
	prometheusProcessCollectorOpts  = prometheusCollectorsPkg.Ident("ProcessCollectorOpts")

	natsPkg = protogen.GoImportPath("github.com/nats-io/nats.go")

	watermillPkg                          = protogen.GoImportPath("github.com/ThreeDotsLabs/watermill")
	watermillMessagePkg                   = protogen.GoImportPath("github.com/ThreeDotsLabs/watermill/message")
	watermillMessageMessage               = watermillMessagePkg.Ident("Message")
	watermillMessageSubscribeTopicFromCtx = watermillMessagePkg.Ident("SubscribeTopicFromCtx")
	watermillMessageNewRouter             = watermillMessagePkg.Ident("NewRouter")
	watermillMessageRouter                = watermillMessagePkg.Ident("Router")
	watermillMessageRouterConfig          = watermillMessagePkg.Ident("RouterConfig")
	watermillMiddlewarePkg                = protogen.GoImportPath("github.com/ThreeDotsLabs/watermill/message/router/middleware")
	watermillMiddlewareRecoverer          = watermillMiddlewarePkg.Ident("Recoverer")
	watermillMessageHandlerNameFromCtx    = watermillMessagePkg.Ident("HandlerNameFromCtx")
	watermillMessageHandlerFunc           = watermillMessagePkg.Ident("HandlerFunc")

	grpcMiddlewarePkg                          = protogen.GoImportPath("github.com/grpc-ecosystem/go-grpc-middleware")
	grpcMiddlewarePkgChainUnaryServer          = grpcMiddlewarePkg.Ident("ChainUnaryServer")
	grpcMiddlewarePkgChainStreamServer         = grpcMiddlewarePkg.Ident("ChainStreamServer")
	grpcTagsPkg                                = protogen.GoImportPath("github.com/grpc-ecosystem/go-grpc-middleware/tags")
	grpcTagsPkgUnaryServerInterceptor          = grpcTagsPkg.Ident("UnaryServerInterceptor")
	grpcTagsPkgStreamServerInterceptor         = grpcTagsPkg.Ident("StreamServerInterceptor")
	grpcpromPkg                                = protogen.GoImportPath("github.com/grpc-ecosystem/go-grpc-middleware/providers/prometheus")
	grpcpromPkgWithHistogramBuckets            = grpcpromPkg.Ident("WithHistogramBuckets")
	grpcpromPkgWithServerHandlingTimeHistogram = grpcpromPkg.Ident("WithServerHandlingTimeHistogram")
	grpcpromPkgNewServerMetrics                = grpcpromPkg.Ident("NewServerMetrics")
	grpcpromPkgWithExemplarFromContext         = grpcpromPkg.Ident("WithExemplarFromContext")

	ginPkg            = protogen.GoImportPath("github.com/gin-gonic/gin")
	ginEngine         = ginPkg.Ident("Engine")
	ginNew            = ginPkg.Ident("New")
	ginSetMode        = ginPkg.Ident("SetMode")
	ginResponseWriter = ginPkg.Ident("ResponseWriter")
	ginReleaseMode    = ginPkg.Ident("ReleaseMode")

	otelGinPkg        = otelPkg.Ident("go.opentelemetry.io/contrib/instrumentation/github.com/gin-gonic/gin/otelgin")
	otelGinMiddleware = otelPkg.Ident("Middleware")
	otelGinWithFilter = otelPkg.Ident("WithFilter")

	runtimeDebugPkg   = protogen.GoImportPath("runtime/debug")
	runtimeDebugStack = runtimeDebugPkg.Ident("Stack")

	promHttpPkg         = protogen.GoImportPath("github.com/prometheus/client_golang/prometheus/promhttp")
	promHttpHandlerFor  = promHttpPkg.Ident("HandlerFor")
	promHttpHandlerOpts = promHttpPkg.Ident("HandlerOpts")

	gormPostgresPkg    = protogen.GoImportPath("gorm.io/driver/postgres")
	gormPostgresNew    = gormPostgresPkg.Ident("New")
	gormPostgresConfig = gormPostgresPkg.Ident("Config")
	gormPostgresOpen   = gormPostgresPkg.Ident("Open")

	gormDBResolverPkg          = protogen.GoImportPath("gorm.io/plugin/dbresolver")
	gormDBResolverRegister     = gormDBResolverPkg.Ident("Register")
	gormDBResolverConfig       = gormDBResolverPkg.Ident("Config")
	gormDBResolverRandomPolicy = gormDBResolverPkg.Ident("RandomPolicy")

	gormTracingPkg       = protogen.GoImportPath("gorm.io/plugin/opentelemetry/tracing")
	gormTracingNewPlugin = gormTracingPkg.Ident("NewPlugin")

	localGrpc = protogen.GoImportPath("git.local/sensitive/innerpb/processing/grpc")

	reflectionPkg      = protogen.GoImportPath("google.golang.org/grpc/reflection")
	reflectionRegister = reflectionPkg.Ident("Register")

	amqpPkg = protogen.GoImportPath("github.com/rabbitmq/amqp091-go")

	backoffPkg = protogen.GoImportPath("github.com/cenkalti/backoff/v3")

	cobraPkg        = protogen.GoImportPath("github.com/spf13/cobra")
	cobraPkgCommand = cobraPkg.Ident("Command")

	swagPkg     = protogen.GoImportPath("github.com/swaggo/swag")
	swagPkgSpec = swagPkg.Ident("Spec")
)
