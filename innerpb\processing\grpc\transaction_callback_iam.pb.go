// Code generated by protoc-gen-go-mvp. DO NOT EDIT.
// versions:
// - protoc-gen-go-mvp v2.0.1
// created by mvp authors

package grpc

import (
	context "context"
	grpc "google.golang.org/grpc"
	metadata "google.golang.org/grpc/metadata"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
)

func NewIamTransactionCallbackServer(
	srv TransactionCallbackServer,
) TransactionCallbackServer {
	return &iamTransactionCallbackServer{
		srv: srv,
	}
}

var _ TransactionCallbackServer = (*iamTransactionCallbackServer)(nil)

type iamTransactionCallbackServer struct {
	UnimplementedTransactionCallbackServer

	srv TransactionCallbackServer
}

func (s *iamTransactionCallbackServer) SendCallback(
	ctx context.Context,
	req *TransactionSendCallbackRequestV1,
) (
	*emptypb.Empty,
	error,
) {
	return s.srv.SendCallback(ctx, req)
}

func NewIamTransactionCallbackClient(
	client TransactionCallbackClient,
) TransactionCallbackClient {
	return &iamTransactionCallbackClient{
		client: client,
	}
}

type iamTransactionCallbackClient struct {
	client TransactionCallbackClient
}

func (s *iamTransactionCallbackClient) SendCallback(
	ctx context.Context,
	req *TransactionSendCallbackRequestV1,
	opts ...grpc.CallOption,
) (
	*emptypb.Empty,
	error,
) {
	md, _ := metadata.FromIncomingContext(ctx)
	return s.client.SendCallback(metadata.NewOutgoingContext(ctx, md), req)
}
