package service

import (
	"context"
	"errors"
	"git.local/sensitive/innerpb/processing/grpc/grpcmock"
	"git.local/sensitive/testsdk"
	"github.com/golang/mock/gomock"
	"testing"

	"github.com/stretchr/testify/require"

	"git.local/sensitive/innerpb/processing/goerr"
	gorpc "git.local/sensitive/innerpb/processing/grpc"
	"git.local/sensitive/processing/account/schema"
)

func TestCheckMerchantByOwnerID(t *testing.T) {
	type getMerchantByBalanceOwnerIDOp struct {
		input     *gorpc.GetMerchantByBalanceOwnerRequestV1
		output    *gorpc.GetMerchantByBalanceOwnerResponseV1
		outputErr error
	}

	tests := []struct {
		name                        string
		req                         schema.CheckMerchantRequest
		wantErr                     error
		getMerchantByBalanceOwnerID getMerchantByBalanceOwnerIDOp
	}{
		{
			name: "error",
			req: schema.CheckMerchantRequest{
				BalanceOwnerID: 12,
			},
			wantErr: errors.New("error in GetMerchantByBalanceOwnerID"),
			getMerchantByBalanceOwnerID: getMerchantByBalanceOwnerIDOp{
				input: &gorpc.GetMerchantByBalanceOwnerRequestV1{
					BalanceOwnerId: testsdk.Ptr(uint64(12)),
				},
				output:    nil,
				outputErr: errors.New("error in GetMerchantByBalanceOwnerID"),
			},
		},
		{
			name: "not same merchant id",
			req: schema.CheckMerchantRequest{
				BalanceOwnerID: 12,
				MerchantID:     31,
			},
			wantErr: goerr.ErrInvalidOwnerForMerchant,
			getMerchantByBalanceOwnerID: getMerchantByBalanceOwnerIDOp{
				input: &gorpc.GetMerchantByBalanceOwnerRequestV1{
					BalanceOwnerId: testsdk.Ptr(uint64(12)),
				},
				output: &gorpc.GetMerchantByBalanceOwnerResponseV1{
					MerchantId: testsdk.Ptr(uint64(30)),
					ProjectId:  testsdk.Ptr(uint64(10)),
				},
				outputErr: nil,
			},
		},
		{
			name: "success",
			req: schema.CheckMerchantRequest{
				BalanceOwnerID: 12,
				MerchantID:     31,
			},
			wantErr: nil,
			getMerchantByBalanceOwnerID: getMerchantByBalanceOwnerIDOp{
				input: &gorpc.GetMerchantByBalanceOwnerRequestV1{
					BalanceOwnerId: testsdk.Ptr(uint64(12)),
				},
				output: &gorpc.GetMerchantByBalanceOwnerResponseV1{
					MerchantId: testsdk.Ptr(uint64(31)),
					ProjectId:  testsdk.Ptr(uint64(10)),
				},
				outputErr: nil,
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctrl := gomock.NewController(t)
			defer ctrl.Finish()

			billingCliMock := grpcmock.NewMockBillingClient(ctrl)
			s := NewMerchantService(billingCliMock)

			billingCliMock.EXPECT().GetMerchantByBalanceOwnerIDV1(
				gomock.Any(),
				tt.getMerchantByBalanceOwnerID.input,
			).Return(
				tt.getMerchantByBalanceOwnerID.output,
				tt.getMerchantByBalanceOwnerID.outputErr,
			).Times(1)

			err := s.CheckMerchantByOwnerID(context.Background(), tt.req)
			if tt.wantErr != nil {
				require.Error(t, err)
				require.Equal(t, tt.wantErr, err)
			} else {
				require.NoError(t, err)
			}
		})
	}
}
