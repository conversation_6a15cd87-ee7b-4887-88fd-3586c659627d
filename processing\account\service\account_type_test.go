package service

import (
	"context"
	"errors"
	"git.local/sensitive/processing/account/repository/databasemocks"
	"github.com/golang/mock/gomock"
	"testing"

	"github.com/stretchr/testify/require"

	"git.local/sensitive/processing/account/model"
)

func TestGetAccountTypeByID(t *testing.T) {
	type getByIDOp struct {
		input     uint64
		output    *model.AccountType
		outputErr error
	}

	tests := []struct {
		name    string
		req     uint64
		want    *model.AccountType
		wantErr error
		getByID getByIDOp
	}{
		{
			name:    "error",
			req:     23,
			want:    nil,
			wantErr: errors.New("some error"),
			getByID: getByIDOp{
				input:     23,
				output:    nil,
				outputErr: errors.New("some error"),
			},
		},
		{
			name: "success",
			req:  23,
			want: &model.AccountType{
				ID:   23,
				Name: "my code",
				Code: "some code",
			},
			wantErr: nil,
			getByID: getByIDOp{
				input: 23,
				output: &model.AccountType{
					ID:   23,
					Name: "my code",
					Code: "some code",
				},
				outputErr: nil,
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctrl := gomock.NewController(t)
			defer ctrl.Finish()

			accountTypeDBMock := databasemocks.NewMockAccountTyper(ctrl)

			accountTypeDBMock.EXPECT().GetByID(
				gomock.Any(), tt.getByID.input).Return(tt.want, tt.wantErr).Times(1)

			s := NewAccountTypeService(accountTypeDBMock)

			res, err := s.GetAccountTypeByID(context.Background(), tt.req)
			if tt.wantErr != nil {
				require.Error(t, err)
				require.Equal(t, tt.wantErr, err)
			} else {
				require.NoError(t, err)
			}

			require.Equal(t, tt.want, res)
		})
	}
}
