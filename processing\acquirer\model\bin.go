package model

const (
	BankBinsTableName string = "acquirer.bank_bins"
)

type BankBin struct {
	TimestampMixin
	ID        uint64 `gorm:"column:id" json:"id"`
	BankId    uint64 `gorm:"column:bank_id" json:"bank_id"`
	Bin       string `gorm:"column:bin" json:"bin"`
	IpsId     uint64 `gorm:"column:ips_id" json:"ips_id"`
	CountryId uint64 `gorm:"column:country_id" json:"country_id"`
}

func (i BankBin) TableName() string {
	return BankBinsTableName
}
