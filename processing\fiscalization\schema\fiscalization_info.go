package schema

import (
	gorpc "git.local/sensitive/innerpb/processing/grpc"
	"git.local/sensitive/processing/fiscalization/model"
)

type FiscalizationInfo struct {
	FiscalNumber   string `json:"fiscal_number" validate:"required"`
	FiscalCheckUrl string `json:"fiscal_check_url" validate:"required"`
}

func NewRawGetFiscalInfoByTransactionIDResponse(
	response FiscalizationInfo,
) *gorpc.GetFiscalInfoByTransactionIDResponseV1 {
	number := response.FiscalNumber
	url := response.FiscalCheckUrl

	return &gorpc.GetFiscalInfoByTransactionIDResponseV1{
		FiscalNumber:   &number,
		FiscalCheckUrl: &url,
	}
}

func NewFiscalizationInfo(
	fiscalization *model.Fiscalization,
) FiscalizationInfo {
	return FiscalizationInfo{
		FiscalNumber:   fiscalization.FiscalNumber,
		FiscalCheckUrl: fiscalization.FiscalCheckURL,
	}
}
