// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.5
// 	protoc        v5.29.3
// source: inner/processing/grpc/transfer_automatic.proto

package grpc

import (
	_ "git.local/sensitive/mvp/pb"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	descriptorpb "google.golang.org/protobuf/types/descriptorpb"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type EnumFrequencyType int32

const (
	EnumFrequencyType_PerDay   EnumFrequencyType = 0
	EnumFrequencyType_PerWeek  EnumFrequencyType = 1
	EnumFrequencyType_PerMonth EnumFrequencyType = 2
)

// Enum value maps for EnumFrequencyType.
var (
	EnumFrequencyType_name = map[int32]string{
		0: "PerDay",
		1: "PerWeek",
		2: "PerMonth",
	}
	EnumFrequencyType_value = map[string]int32{
		"PerDay":   0,
		"PerWeek":  1,
		"PerMonth": 2,
	}
)

func (x EnumFrequencyType) Enum() *EnumFrequencyType {
	p := new(EnumFrequencyType)
	*p = x
	return p
}

func (x EnumFrequencyType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (EnumFrequencyType) Descriptor() protoreflect.EnumDescriptor {
	return file_inner_processing_grpc_transfer_automatic_proto_enumTypes[0].Descriptor()
}

func (EnumFrequencyType) Type() protoreflect.EnumType {
	return &file_inner_processing_grpc_transfer_automatic_proto_enumTypes[0]
}

func (x EnumFrequencyType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use EnumFrequencyType.Descriptor instead.
func (EnumFrequencyType) EnumDescriptor() ([]byte, []int) {
	return file_inner_processing_grpc_transfer_automatic_proto_rawDescGZIP(), []int{0}
}

type FrequencyRef struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Code          *string                `protobuf:"bytes,1,opt,name=code" json:"code,omitempty"`
	Name          *string                `protobuf:"bytes,2,opt,name=name" json:"name,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *FrequencyRef) Reset() {
	*x = FrequencyRef{}
	mi := &file_inner_processing_grpc_transfer_automatic_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *FrequencyRef) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FrequencyRef) ProtoMessage() {}

func (x *FrequencyRef) ProtoReflect() protoreflect.Message {
	mi := &file_inner_processing_grpc_transfer_automatic_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FrequencyRef.ProtoReflect.Descriptor instead.
func (*FrequencyRef) Descriptor() ([]byte, []int) {
	return file_inner_processing_grpc_transfer_automatic_proto_rawDescGZIP(), []int{0}
}

func (x *FrequencyRef) GetCode() string {
	if x != nil && x.Code != nil {
		return *x.Code
	}
	return ""
}

func (x *FrequencyRef) GetName() string {
	if x != nil && x.Name != nil {
		return *x.Name
	}
	return ""
}

type CreateTransferByRulesRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Frequency     *EnumFrequencyType     `protobuf:"varint,1,opt,name=frequency,enum=processing.transfer_automatic.transfer_automatic.EnumFrequencyType" json:"frequency,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CreateTransferByRulesRequest) Reset() {
	*x = CreateTransferByRulesRequest{}
	mi := &file_inner_processing_grpc_transfer_automatic_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreateTransferByRulesRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateTransferByRulesRequest) ProtoMessage() {}

func (x *CreateTransferByRulesRequest) ProtoReflect() protoreflect.Message {
	mi := &file_inner_processing_grpc_transfer_automatic_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateTransferByRulesRequest.ProtoReflect.Descriptor instead.
func (*CreateTransferByRulesRequest) Descriptor() ([]byte, []int) {
	return file_inner_processing_grpc_transfer_automatic_proto_rawDescGZIP(), []int{1}
}

func (x *CreateTransferByRulesRequest) GetFrequency() EnumFrequencyType {
	if x != nil && x.Frequency != nil {
		return *x.Frequency
	}
	return EnumFrequencyType_PerDay
}

var file_inner_processing_grpc_transfer_automatic_proto_extTypes = []protoimpl.ExtensionInfo{
	{
		ExtendedType:  (*descriptorpb.EnumValueOptions)(nil),
		ExtensionType: (*FrequencyRef)(nil),
		Field:         50001,
		Name:          "processing.transfer_automatic.transfer_automatic.frequency_value",
		Tag:           "bytes,50001,opt,name=frequency_value",
		Filename:      "inner/processing/grpc/transfer_automatic.proto",
	},
	{
		ExtendedType:  (*descriptorpb.EnumOptions)(nil),
		ExtensionType: (*FrequencyRef)(nil),
		Field:         50002,
		Name:          "processing.transfer_automatic.transfer_automatic.default_frequency_value",
		Tag:           "bytes,50002,opt,name=default_frequency_value",
		Filename:      "inner/processing/grpc/transfer_automatic.proto",
	},
}

// Extension fields to descriptorpb.EnumValueOptions.
var (
	// optional processing.transfer_automatic.transfer_automatic.FrequencyRef frequency_value = 50001;
	E_FrequencyValue = &file_inner_processing_grpc_transfer_automatic_proto_extTypes[0]
)

// Extension fields to descriptorpb.EnumOptions.
var (
	// optional processing.transfer_automatic.transfer_automatic.FrequencyRef default_frequency_value = 50002;
	E_DefaultFrequencyValue = &file_inner_processing_grpc_transfer_automatic_proto_extTypes[1]
)

var File_inner_processing_grpc_transfer_automatic_proto protoreflect.FileDescriptor

var file_inner_processing_grpc_transfer_automatic_proto_rawDesc = string([]byte{
	0x0a, 0x2e, 0x69, 0x6e, 0x6e, 0x65, 0x72, 0x2f, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69,
	0x6e, 0x67, 0x2f, 0x67, 0x72, 0x70, 0x63, 0x2f, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x66, 0x65, 0x72,
	0x5f, 0x61, 0x75, 0x74, 0x6f, 0x6d, 0x61, 0x74, 0x69, 0x63, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x12, 0x30, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x2e, 0x74, 0x72, 0x61,
	0x6e, 0x73, 0x66, 0x65, 0x72, 0x5f, 0x61, 0x75, 0x74, 0x6f, 0x6d, 0x61, 0x74, 0x69, 0x63, 0x2e,
	0x74, 0x72, 0x61, 0x6e, 0x73, 0x66, 0x65, 0x72, 0x5f, 0x61, 0x75, 0x74, 0x6f, 0x6d, 0x61, 0x74,
	0x69, 0x63, 0x1a, 0x1b, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x62, 0x75, 0x66, 0x2f, 0x65, 0x6d, 0x70, 0x74, 0x79, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a,
	0x20, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66,
	0x2f, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x6f, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x1a, 0x14, 0x6d, 0x76, 0x70, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f, 0x72, 0x65, 0x66,
	0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x14, 0x6d, 0x76, 0x70, 0x2f, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x2f, 0x65, 0x6e, 0x75, 0x6d, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x36, 0x0a,
	0x0c, 0x46, 0x72, 0x65, 0x71, 0x75, 0x65, 0x6e, 0x63, 0x79, 0x52, 0x65, 0x66, 0x12, 0x12, 0x0a,
	0x04, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x63, 0x6f, 0x64,
	0x65, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x04, 0x6e, 0x61, 0x6d, 0x65, 0x22, 0x81, 0x01, 0x0a, 0x1c, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65,
	0x54, 0x72, 0x61, 0x6e, 0x73, 0x66, 0x65, 0x72, 0x42, 0x79, 0x52, 0x75, 0x6c, 0x65, 0x73, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x61, 0x0a, 0x09, 0x66, 0x72, 0x65, 0x71, 0x75, 0x65,
	0x6e, 0x63, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x43, 0x2e, 0x70, 0x72, 0x6f, 0x63,
	0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x2e, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x66, 0x65, 0x72, 0x5f,
	0x61, 0x75, 0x74, 0x6f, 0x6d, 0x61, 0x74, 0x69, 0x63, 0x2e, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x66,
	0x65, 0x72, 0x5f, 0x61, 0x75, 0x74, 0x6f, 0x6d, 0x61, 0x74, 0x69, 0x63, 0x2e, 0x45, 0x6e, 0x75,
	0x6d, 0x46, 0x72, 0x65, 0x71, 0x75, 0x65, 0x6e, 0x63, 0x79, 0x54, 0x79, 0x70, 0x65, 0x52, 0x09,
	0x66, 0x72, 0x65, 0x71, 0x75, 0x65, 0x6e, 0x63, 0x79, 0x2a, 0xda, 0x01, 0x0a, 0x11, 0x45, 0x6e,
	0x75, 0x6d, 0x46, 0x72, 0x65, 0x71, 0x75, 0x65, 0x6e, 0x63, 0x79, 0x54, 0x79, 0x70, 0x65, 0x12,
	0x28, 0x0a, 0x06, 0x50, 0x65, 0x72, 0x44, 0x61, 0x79, 0x10, 0x00, 0x1a, 0x1c, 0x8a, 0xb5, 0x18,
	0x0f, 0x0a, 0x03, 0x64, 0x61, 0x79, 0x12, 0x08, 0xd0, 0x94, 0xd0, 0xb5, 0xd0, 0xbd, 0xd1, 0x8c,
	0xca, 0xa1, 0xec, 0x8e, 0x02, 0x03, 0x64, 0x61, 0x79, 0x12, 0x2f, 0x0a, 0x07, 0x50, 0x65, 0x72,
	0x57, 0x65, 0x65, 0x6b, 0x10, 0x01, 0x1a, 0x22, 0x8a, 0xb5, 0x18, 0x14, 0x0a, 0x04, 0x77, 0x65,
	0x65, 0x6b, 0x12, 0x0c, 0xd0, 0x9d, 0xd0, 0xb5, 0xd0, 0xb4, 0xd0, 0xb5, 0xd0, 0xbb, 0xd1, 0x8f,
	0xca, 0xa1, 0xec, 0x8e, 0x02, 0x04, 0x77, 0x65, 0x65, 0x6b, 0x12, 0x30, 0x0a, 0x08, 0x50, 0x65,
	0x72, 0x4d, 0x6f, 0x6e, 0x74, 0x68, 0x10, 0x02, 0x1a, 0x22, 0x8a, 0xb5, 0x18, 0x13, 0x0a, 0x05,
	0x6d, 0x6f, 0x6e, 0x74, 0x68, 0x12, 0x0a, 0xd0, 0x9c, 0xd0, 0xb5, 0xd1, 0x81, 0xd1, 0x8f, 0xd1,
	0x86, 0xca, 0xa1, 0xec, 0x8e, 0x02, 0x05, 0x6d, 0x6f, 0x6e, 0x74, 0x68, 0x1a, 0x38, 0x92, 0xb5,
	0x18, 0x02, 0x0a, 0x00, 0xaa, 0x82, 0xec, 0x8e, 0x02, 0x17, 0x64, 0x65, 0x66, 0x61, 0x75, 0x6c,
	0x74, 0x5f, 0x66, 0x72, 0x65, 0x71, 0x75, 0x65, 0x6e, 0x63, 0x79, 0x5f, 0x76, 0x61, 0x6c, 0x75,
	0x65, 0xb2, 0x82, 0xec, 0x8e, 0x02, 0x0f, 0x66, 0x72, 0x65, 0x71, 0x75, 0x65, 0x6e, 0x63, 0x79,
	0x5f, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x32, 0xa0, 0x01, 0x0a, 0x11, 0x54, 0x72, 0x61, 0x6e, 0x73,
	0x66, 0x65, 0x72, 0x41, 0x75, 0x74, 0x6f, 0x6d, 0x61, 0x74, 0x69, 0x63, 0x12, 0x8a, 0x01, 0x0a,
	0x20, 0x53, 0x74, 0x61, 0x72, 0x74, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x54, 0x72, 0x61, 0x6e,
	0x73, 0x66, 0x65, 0x72, 0x42, 0x79, 0x52, 0x75, 0x6c, 0x65, 0x73, 0x57, 0x6f, 0x72, 0x6b, 0x65,
	0x72, 0x12, 0x4e, 0x2e, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x2e, 0x74,
	0x72, 0x61, 0x6e, 0x73, 0x66, 0x65, 0x72, 0x5f, 0x61, 0x75, 0x74, 0x6f, 0x6d, 0x61, 0x74, 0x69,
	0x63, 0x2e, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x66, 0x65, 0x72, 0x5f, 0x61, 0x75, 0x74, 0x6f, 0x6d,
	0x61, 0x74, 0x69, 0x63, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x54, 0x72, 0x61, 0x6e, 0x73,
	0x66, 0x65, 0x72, 0x42, 0x79, 0x52, 0x75, 0x6c, 0x65, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x1a, 0x16, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x62, 0x75, 0x66, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x3a, 0x8c, 0x01, 0x0a, 0x0f, 0x66, 0x72,
	0x65, 0x71, 0x75, 0x65, 0x6e, 0x63, 0x79, 0x5f, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x12, 0x21, 0x2e,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e,
	0x45, 0x6e, 0x75, 0x6d, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73,
	0x18, 0xd1, 0x86, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x3e, 0x2e, 0x70, 0x72, 0x6f, 0x63, 0x65,
	0x73, 0x73, 0x69, 0x6e, 0x67, 0x2e, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x66, 0x65, 0x72, 0x5f, 0x61,
	0x75, 0x74, 0x6f, 0x6d, 0x61, 0x74, 0x69, 0x63, 0x2e, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x66, 0x65,
	0x72, 0x5f, 0x61, 0x75, 0x74, 0x6f, 0x6d, 0x61, 0x74, 0x69, 0x63, 0x2e, 0x46, 0x72, 0x65, 0x71,
	0x75, 0x65, 0x6e, 0x63, 0x79, 0x52, 0x65, 0x66, 0x52, 0x0e, 0x66, 0x72, 0x65, 0x71, 0x75, 0x65,
	0x6e, 0x63, 0x79, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x96, 0x01, 0x0a, 0x17, 0x64, 0x65, 0x66,
	0x61, 0x75, 0x6c, 0x74, 0x5f, 0x66, 0x72, 0x65, 0x71, 0x75, 0x65, 0x6e, 0x63, 0x79, 0x5f, 0x76,
	0x61, 0x6c, 0x75, 0x65, 0x12, 0x1c, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x45, 0x6e, 0x75, 0x6d, 0x4f, 0x70, 0x74, 0x69, 0x6f,
	0x6e, 0x73, 0x18, 0xd2, 0x86, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x3e, 0x2e, 0x70, 0x72, 0x6f,
	0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x2e, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x66, 0x65, 0x72,
	0x5f, 0x61, 0x75, 0x74, 0x6f, 0x6d, 0x61, 0x74, 0x69, 0x63, 0x2e, 0x74, 0x72, 0x61, 0x6e, 0x73,
	0x66, 0x65, 0x72, 0x5f, 0x61, 0x75, 0x74, 0x6f, 0x6d, 0x61, 0x74, 0x69, 0x63, 0x2e, 0x46, 0x72,
	0x65, 0x71, 0x75, 0x65, 0x6e, 0x63, 0x79, 0x52, 0x65, 0x66, 0x52, 0x15, 0x64, 0x65, 0x66, 0x61,
	0x75, 0x6c, 0x74, 0x46, 0x72, 0x65, 0x71, 0x75, 0x65, 0x6e, 0x63, 0x79, 0x56, 0x61, 0x6c, 0x75,
	0x65, 0x42, 0x2d, 0x5a, 0x2b, 0x67, 0x69, 0x74, 0x2e, 0x6c, 0x6f, 0x63, 0x61, 0x6c, 0x2f, 0x73,
	0x65, 0x6e, 0x73, 0x69, 0x74, 0x69, 0x76, 0x65, 0x2f, 0x69, 0x6e, 0x6e, 0x65, 0x72, 0x70, 0x62,
	0x2f, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x2f, 0x67, 0x72, 0x70, 0x63,
	0x62, 0x08, 0x65, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x70, 0xe8, 0x07,
})

var (
	file_inner_processing_grpc_transfer_automatic_proto_rawDescOnce sync.Once
	file_inner_processing_grpc_transfer_automatic_proto_rawDescData []byte
)

func file_inner_processing_grpc_transfer_automatic_proto_rawDescGZIP() []byte {
	file_inner_processing_grpc_transfer_automatic_proto_rawDescOnce.Do(func() {
		file_inner_processing_grpc_transfer_automatic_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_inner_processing_grpc_transfer_automatic_proto_rawDesc), len(file_inner_processing_grpc_transfer_automatic_proto_rawDesc)))
	})
	return file_inner_processing_grpc_transfer_automatic_proto_rawDescData
}

var file_inner_processing_grpc_transfer_automatic_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_inner_processing_grpc_transfer_automatic_proto_msgTypes = make([]protoimpl.MessageInfo, 2)
var file_inner_processing_grpc_transfer_automatic_proto_goTypes = []any{
	(EnumFrequencyType)(0),                // 0: processing.transfer_automatic.transfer_automatic.EnumFrequencyType
	(*FrequencyRef)(nil),                  // 1: processing.transfer_automatic.transfer_automatic.FrequencyRef
	(*CreateTransferByRulesRequest)(nil),  // 2: processing.transfer_automatic.transfer_automatic.CreateTransferByRulesRequest
	(*descriptorpb.EnumValueOptions)(nil), // 3: google.protobuf.EnumValueOptions
	(*descriptorpb.EnumOptions)(nil),      // 4: google.protobuf.EnumOptions
	(*emptypb.Empty)(nil),                 // 5: google.protobuf.Empty
}
var file_inner_processing_grpc_transfer_automatic_proto_depIdxs = []int32{
	0, // 0: processing.transfer_automatic.transfer_automatic.CreateTransferByRulesRequest.frequency:type_name -> processing.transfer_automatic.transfer_automatic.EnumFrequencyType
	3, // 1: processing.transfer_automatic.transfer_automatic.frequency_value:extendee -> google.protobuf.EnumValueOptions
	4, // 2: processing.transfer_automatic.transfer_automatic.default_frequency_value:extendee -> google.protobuf.EnumOptions
	1, // 3: processing.transfer_automatic.transfer_automatic.frequency_value:type_name -> processing.transfer_automatic.transfer_automatic.FrequencyRef
	1, // 4: processing.transfer_automatic.transfer_automatic.default_frequency_value:type_name -> processing.transfer_automatic.transfer_automatic.FrequencyRef
	2, // 5: processing.transfer_automatic.transfer_automatic.TransferAutomatic.StartCreateTransferByRulesWorker:input_type -> processing.transfer_automatic.transfer_automatic.CreateTransferByRulesRequest
	5, // 6: processing.transfer_automatic.transfer_automatic.TransferAutomatic.StartCreateTransferByRulesWorker:output_type -> google.protobuf.Empty
	6, // [6:7] is the sub-list for method output_type
	5, // [5:6] is the sub-list for method input_type
	3, // [3:5] is the sub-list for extension type_name
	1, // [1:3] is the sub-list for extension extendee
	0, // [0:1] is the sub-list for field type_name
}

func init() { file_inner_processing_grpc_transfer_automatic_proto_init() }
func file_inner_processing_grpc_transfer_automatic_proto_init() {
	if File_inner_processing_grpc_transfer_automatic_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_inner_processing_grpc_transfer_automatic_proto_rawDesc), len(file_inner_processing_grpc_transfer_automatic_proto_rawDesc)),
			NumEnums:      1,
			NumMessages:   2,
			NumExtensions: 2,
			NumServices:   1,
		},
		GoTypes:           file_inner_processing_grpc_transfer_automatic_proto_goTypes,
		DependencyIndexes: file_inner_processing_grpc_transfer_automatic_proto_depIdxs,
		EnumInfos:         file_inner_processing_grpc_transfer_automatic_proto_enumTypes,
		MessageInfos:      file_inner_processing_grpc_transfer_automatic_proto_msgTypes,
		ExtensionInfos:    file_inner_processing_grpc_transfer_automatic_proto_extTypes,
	}.Build()
	File_inner_processing_grpc_transfer_automatic_proto = out.File
	file_inner_processing_grpc_transfer_automatic_proto_goTypes = nil
	file_inner_processing_grpc_transfer_automatic_proto_depIdxs = nil
}
