package repository

import (
	"context"
	"git.local/sensitive/innerpb/processing/goerr"
	"git.local/sensitive/processing/account/model"
	"git.local/sensitive/sdk/dog"
	"gorm.io/gorm"
)

type AccountStatementDB struct {
	db *gorm.DB
}

func NewAccountStatementDB(db *gorm.DB) AccountStatementer {
	return &AccountStatementDB{
		db: db,
	}
}

func (as *AccountStatementDB) Create(ctx context.Context, accountStatement *model.AccountStatement) (err error) {
	ctx, span := dog.CreateSpan(ctx, "AccountStatementDB_Create")
	defer span.End()

	if err = as.db.WithContext(ctx).Create(accountStatement).Error; err != nil {
		return goerr.ErrDbUnexpected.WithErr(err).WithCtx(ctx)
	}

	return nil
}
