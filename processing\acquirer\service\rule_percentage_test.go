package service

import (
	"context"
	"errors"
	"testing"

	"github.com/golang/mock/gomock"
	"github.com/stretchr/testify/require"

	"git.local/sensitive/innerpb/processing/goerr"
	gtransactionMocks "git.local/sensitive/pkg/gtransaction/mocks"
	"git.local/sensitive/processing/acquirer/model"
	"git.local/sensitive/processing/acquirer/repository/database/databasemocks"
	"git.local/sensitive/processing/acquirer/schema"
	"git.local/sensitive/sdk/dog/e2e"
)

func TestReplace(t *testing.T) {
	desc := "some desc"

	type gtransactionBeginOp struct {
		isCalled bool
	}

	type gtransactionFinishOp struct {
		reqError  error
		outputErr error
	}

	type getAcquirerByIDOp struct {
		input     []uint64
		output    []*model.Acquirer
		outputErr error
	}

	type deleteOp struct {
		isCalled    bool
		inputRuleID uint64
		outputErr   error
	}

	type createOp struct {
		isCalled  bool
		input     []*model.RulePercentage
		outputErr error
	}

	tests := []struct {
		name               string
		req                schema.CreateRulePercentageRequest
		reqRuleID          uint64
		wantErr            error
		gtransactionBegin  gtransactionBeginOp
		gtransactionFinish gtransactionFinishOp
		getAcquirerByID    getAcquirerByIDOp
		delete             deleteOp
		create             createOp
	}{
		{
			name: "error when getting acquirer by id",
			req: schema.CreateRulePercentageRequest{
				{
					AcquirerID: 1,
					Percentage: 10,
				},
				{
					AcquirerID: 2,
					Percentage: 20,
				},
			},
			reqRuleID: 20,
			gtransactionBegin: gtransactionBeginOp{
				isCalled: true,
			},
			gtransactionFinish: gtransactionFinishOp{
				reqError:  errors.New("some error"),
				outputErr: errors.New("some error"),
			},
			getAcquirerByID: getAcquirerByIDOp{
				input:     []uint64{1, 2},
				output:    nil,
				outputErr: errors.New("some error"),
			},
			wantErr: errors.New("some error"),
		},
		{
			name: "error len in acquirers",
			req: schema.CreateRulePercentageRequest{
				{
					AcquirerID: 1,
					Percentage: 10,
				},
				{
					AcquirerID: 2,
					Percentage: 20,
				},
			},
			reqRuleID: 20,
			gtransactionBegin: gtransactionBeginOp{
				isCalled: true,
			},
			gtransactionFinish: gtransactionFinishOp{
				reqError:  goerr.ErrAcquirerNotFound,
				outputErr: goerr.ErrAcquirerNotFound,
			},
			getAcquirerByID: getAcquirerByIDOp{
				input: []uint64{1, 2},
				output: []*model.Acquirer{
					{
						ID:          2,
						Code:        "bcc",
						BankID:      25,
						CountryID:   52,
						IsActive:    true,
						Description: &desc,
						CountryName: "kaz",
						Name:        "bcc kz",
					},
				},
				outputErr: nil,
			},
			wantErr: goerr.ErrAcquirerNotFound,
		},
		{
			name: "error in delete op",
			req: schema.CreateRulePercentageRequest{
				{
					AcquirerID: 1,
					Percentage: 10,
				},
				{
					AcquirerID: 2,
					Percentage: 20,
				},
			},
			reqRuleID: 20,
			gtransactionBegin: gtransactionBeginOp{
				isCalled: true,
			},
			gtransactionFinish: gtransactionFinishOp{
				reqError:  errors.New("some error"),
				outputErr: errors.New("some error"),
			},
			getAcquirerByID: getAcquirerByIDOp{
				input: []uint64{1, 2},
				output: []*model.Acquirer{
					{
						ID:          1,
						Code:        "epay",
						BankID:      25,
						CountryID:   52,
						IsActive:    true,
						Description: &desc,
						CountryName: "kaz",
						Name:        "epay kz",
					},
					{
						ID:          2,
						Code:        "bcc",
						BankID:      25,
						CountryID:   52,
						IsActive:    true,
						Description: &desc,
						CountryName: "kaz",
						Name:        "bcc kz",
					},
				},
				outputErr: nil,
			},
			delete: deleteOp{
				isCalled:    true,
				inputRuleID: 20,
				outputErr:   errors.New("some error"),
			},
			wantErr: errors.New("some error"),
		},
		{
			name: "error in create op",
			req: schema.CreateRulePercentageRequest{
				{
					AcquirerID: 1,
					Percentage: 10,
				},
				{
					AcquirerID: 2,
					Percentage: 20,
				},
			},
			reqRuleID: 20,
			gtransactionBegin: gtransactionBeginOp{
				isCalled: true,
			},
			gtransactionFinish: gtransactionFinishOp{
				reqError:  errors.New("some error"),
				outputErr: errors.New("some error"),
			},
			getAcquirerByID: getAcquirerByIDOp{
				input: []uint64{1, 2},
				output: []*model.Acquirer{
					{
						ID:          1,
						Code:        "epay",
						BankID:      25,
						CountryID:   52,
						IsActive:    true,
						Description: &desc,
						CountryName: "kaz",
						Name:        "epay kz",
					},
					{
						ID:          2,
						Code:        "bcc",
						BankID:      25,
						CountryID:   52,
						IsActive:    true,
						Description: &desc,
						CountryName: "kaz",
						Name:        "bcc kz",
					},
				},
				outputErr: nil,
			},
			delete: deleteOp{
				isCalled:    true,
				inputRuleID: 20,
				outputErr:   nil,
			},
			create: createOp{
				isCalled: true,
				input: []*model.RulePercentage{
					{
						RuleID:     20,
						AcquirerID: 1,
						Percentage: 10,
					},
					{
						RuleID:     20,
						AcquirerID: 2,
						Percentage: 20,
					},
				},
				outputErr: errors.New("some error"),
			},
			wantErr: errors.New("some error"),
		},
		{
			name: "different error returned by gtransaction finish op",
			req: schema.CreateRulePercentageRequest{
				{
					AcquirerID: 1,
					Percentage: 10,
				},
				{
					AcquirerID: 2,
					Percentage: 20,
				},
			},
			reqRuleID: 20,
			gtransactionBegin: gtransactionBeginOp{
				isCalled: true,
			},
			gtransactionFinish: gtransactionFinishOp{
				reqError:  errors.New("some error"),
				outputErr: errors.New("some another error"),
			},
			getAcquirerByID: getAcquirerByIDOp{
				input: []uint64{1, 2},
				output: []*model.Acquirer{
					{
						ID:          1,
						Code:        "epay",
						BankID:      25,
						CountryID:   52,
						IsActive:    true,
						Description: &desc,
						CountryName: "kaz",
						Name:        "epay kz",
					},
					{
						ID:          2,
						Code:        "bcc",
						BankID:      25,
						CountryID:   52,
						IsActive:    true,
						Description: &desc,
						CountryName: "kaz",
						Name:        "bcc kz",
					},
				},
				outputErr: nil,
			},
			delete: deleteOp{
				isCalled:    true,
				inputRuleID: 20,
				outputErr:   nil,
			},
			create: createOp{
				isCalled: true,
				input: []*model.RulePercentage{
					{
						RuleID:     20,
						AcquirerID: 1,
						Percentage: 10,
					},
					{
						RuleID:     20,
						AcquirerID: 2,
						Percentage: 20,
					},
				},
				outputErr: errors.New("some error"),
			},
			wantErr: errors.New("some another error"),
		},
		{
			name: "success",
			req: schema.CreateRulePercentageRequest{
				{
					AcquirerID: 1,
					Percentage: 10,
				},
				{
					AcquirerID: 2,
					Percentage: 20,
				},
			},
			reqRuleID: 20,
			gtransactionBegin: gtransactionBeginOp{
				isCalled: true,
			},
			gtransactionFinish: gtransactionFinishOp{
				reqError:  nil,
				outputErr: nil,
			},
			getAcquirerByID: getAcquirerByIDOp{
				input: []uint64{1, 2},
				output: []*model.Acquirer{
					{
						ID:          1,
						Code:        "epay",
						BankID:      25,
						CountryID:   52,
						IsActive:    true,
						Description: &desc,
						CountryName: "kaz",
						Name:        "epay kz",
					},
					{
						ID:          2,
						Code:        "bcc",
						BankID:      25,
						CountryID:   52,
						IsActive:    true,
						Description: &desc,
						CountryName: "kaz",
						Name:        "bcc kz",
					},
				},
				outputErr: nil,
			},
			delete: deleteOp{
				isCalled:    true,
				inputRuleID: 20,
				outputErr:   nil,
			},
			create: createOp{
				isCalled: true,
				input: []*model.RulePercentage{
					{
						RuleID:     20,
						AcquirerID: 1,
						Percentage: 10,
					},
					{
						RuleID:     20,
						AcquirerID: 2,
						Percentage: 20,
					},
				},
				outputErr: nil,
			},
			wantErr: nil,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctrl := gomock.NewController(t)
			acquirerDBMock := databasemocks.NewMockAcquirer(ctrl)
			rulePercentageDBMock := databasemocks.NewMockRulePercentager(ctrl)
			gtransactionMock := gtransactionMocks.NewMockManager(ctrl)

			c := context.Background()
			ctx, cancel := context.WithCancel(c)
			e2e.InitAppE2E(t, ctx, cancel, nil, nil, nil)

			gtransactionMock.EXPECT().Begin(
				gomock.Any(),
			).Return(context.TODO()).Times(1)

			gtransactionMock.EXPECT().Finish(
				gomock.Any(),
				tt.gtransactionFinish.reqError,
			).Return(tt.gtransactionFinish.outputErr).Times(1)

			acquirerDBMock.EXPECT().GetAcquirersById(
				gomock.Any(),
				tt.getAcquirerByID.input,
			).Return(tt.getAcquirerByID.output, tt.getAcquirerByID.outputErr).Times(1)

			if tt.delete.isCalled {
				rulePercentageDBMock.EXPECT().Delete(
					gomock.Any(),
					tt.delete.inputRuleID,
				).Return(tt.delete.outputErr).Times(1)
			}

			if tt.create.isCalled {
				rulePercentageDBMock.EXPECT().Create(
					gomock.Any(),
					tt.create.input,
				).Return(tt.create.outputErr).Times(1)
			}

			s := NewRulePercentageService(acquirerDBMock, rulePercentageDBMock, gtransactionMock)

			err := s.Replace(context.Background(), tt.req, tt.reqRuleID)
			if tt.wantErr != nil {
				require.Error(t, err)
				require.Equal(t, tt.wantErr, err)
			} else {
				require.NoError(t, err)
			}
		})
	}
}

func TestGetAllByRuleID(t *testing.T) {
	type getAllByRuleIDOp struct {
		input     uint64
		output    model.RulePercentages
		outputErr error
	}

	tests := []struct {
		name         string
		reqRuleID    uint64
		want         model.RulePercentages
		wantErr      error
		getAllByRule getAllByRuleIDOp
	}{
		{
			name:      "error when getting rule by id",
			reqRuleID: 1,
			wantErr:   errors.New("some error"),
			getAllByRule: getAllByRuleIDOp{
				input:     1,
				outputErr: errors.New("some error"),
				output:    model.RulePercentages{},
			},
			want: model.RulePercentages{},
		},
		{
			name:      "success",
			reqRuleID: 1,
			wantErr:   nil,
			getAllByRule: getAllByRuleIDOp{
				input:     1,
				outputErr: nil,
				output: model.RulePercentages{
					{
						ID:         98,
						RuleID:     1,
						AcquirerID: 1,
						Percentage: 10,
					},
					{
						ID:         99,
						RuleID:     1,
						AcquirerID: 2,
						Percentage: 90,
					},
				},
			},
			want: model.RulePercentages{
				{
					ID:         98,
					RuleID:     1,
					AcquirerID: 1,
					Percentage: 10,
				},
				{
					ID:         99,
					RuleID:     1,
					AcquirerID: 2,
					Percentage: 90,
				},
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctrl := gomock.NewController(t)
			rulePercentageDBMock := databasemocks.NewMockRulePercentager(ctrl)

			rulePercentageDBMock.EXPECT().GetAllByRuleID(
				gomock.Any(),
				tt.getAllByRule.input,
			).Return(tt.getAllByRule.output, tt.getAllByRule.outputErr).Times(1)

			s := NewRulePercentageService(nil, rulePercentageDBMock, nil)

			resp, err := s.GetAllByRuleID(context.Background(), tt.reqRuleID)
			if tt.wantErr != nil {
				require.Error(t, err)
				require.Equal(t, tt.wantErr, err)
			} else {
				require.NoError(t, err)
			}

			require.Equal(t, tt.want, resp)
		})
	}

}
