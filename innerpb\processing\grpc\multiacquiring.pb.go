// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.5
// 	protoc        v5.29.3
// source: inner/processing/grpc/multiacquiring.proto

package grpc

import (
	_ "git.local/sensitive/mvp/pb"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
	structpb "google.golang.org/protobuf/types/known/structpb"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type GetAcquirerIdentifierResponse struct {
	state              protoimpl.MessageState `protogen:"open.v1"`
	AcquirerIdentifier *string                `protobuf:"bytes,1,opt,name=acquirer_identifier,json=acquirerIdentifier" json:"acquirer_identifier,omitempty"`
	unknownFields      protoimpl.UnknownFields
	sizeCache          protoimpl.SizeCache
}

func (x *GetAcquirerIdentifierResponse) Reset() {
	*x = GetAcquirerIdentifierResponse{}
	mi := &file_inner_processing_grpc_multiacquiring_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetAcquirerIdentifierResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAcquirerIdentifierResponse) ProtoMessage() {}

func (x *GetAcquirerIdentifierResponse) ProtoReflect() protoreflect.Message {
	mi := &file_inner_processing_grpc_multiacquiring_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAcquirerIdentifierResponse.ProtoReflect.Descriptor instead.
func (*GetAcquirerIdentifierResponse) Descriptor() ([]byte, []int) {
	return file_inner_processing_grpc_multiacquiring_proto_rawDescGZIP(), []int{0}
}

func (x *GetAcquirerIdentifierResponse) GetAcquirerIdentifier() string {
	if x != nil && x.AcquirerIdentifier != nil {
		return *x.AcquirerIdentifier
	}
	return ""
}

type PayInRequestData struct {
	state                protoimpl.MessageState `protogen:"open.v1"`
	TransactionId        *uint64                `protobuf:"varint,1,opt,name=transaction_id,json=transactionId" json:"transaction_id,omitempty"`
	IsHold               *bool                  `protobuf:"varint,2,opt,name=is_hold,json=isHold" json:"is_hold,omitempty"`
	Amount               *float64               `protobuf:"fixed64,3,opt,name=amount" json:"amount,omitempty"`
	Description          *string                `protobuf:"bytes,4,opt,name=description" json:"description,omitempty"`
	Card                 *CardDataForBank       `protobuf:"bytes,5,opt,name=card" json:"card,omitempty"`
	User                 *UserDataForBank       `protobuf:"bytes,6,opt,name=user" json:"user,omitempty"`
	Terminal             *TerminalDataForBank   `protobuf:"bytes,7,opt,name=terminal" json:"terminal,omitempty"`
	AggregatedTypeId     *uint64                `protobuf:"varint,8,opt,name=aggregated_type_id,json=aggregatedTypeId" json:"aggregated_type_id,omitempty"`
	CardInfo             *CardInfo              `protobuf:"bytes,9,opt,name=card_info,json=cardInfo" json:"card_info,omitempty"`
	MerchantInfo         *MerchantInfo          `protobuf:"bytes,10,opt,name=merchant_info,json=merchantInfo" json:"merchant_info,omitempty"`
	TransactionCreatedAt *timestamppb.Timestamp `protobuf:"bytes,11,opt,name=transaction_created_at,json=transactionCreatedAt" json:"transaction_created_at,omitempty"`
	GetForm              *bool                  `protobuf:"varint,12,opt,name=get_form,json=getForm" json:"get_form,omitempty"`
	UserIpAddress        *string                `protobuf:"bytes,13,opt,name=user_ip_address,json=userIpAddress" json:"user_ip_address,omitempty"`
	unknownFields        protoimpl.UnknownFields
	sizeCache            protoimpl.SizeCache
}

func (x *PayInRequestData) Reset() {
	*x = PayInRequestData{}
	mi := &file_inner_processing_grpc_multiacquiring_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PayInRequestData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PayInRequestData) ProtoMessage() {}

func (x *PayInRequestData) ProtoReflect() protoreflect.Message {
	mi := &file_inner_processing_grpc_multiacquiring_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PayInRequestData.ProtoReflect.Descriptor instead.
func (*PayInRequestData) Descriptor() ([]byte, []int) {
	return file_inner_processing_grpc_multiacquiring_proto_rawDescGZIP(), []int{1}
}

func (x *PayInRequestData) GetTransactionId() uint64 {
	if x != nil && x.TransactionId != nil {
		return *x.TransactionId
	}
	return 0
}

func (x *PayInRequestData) GetIsHold() bool {
	if x != nil && x.IsHold != nil {
		return *x.IsHold
	}
	return false
}

func (x *PayInRequestData) GetAmount() float64 {
	if x != nil && x.Amount != nil {
		return *x.Amount
	}
	return 0
}

func (x *PayInRequestData) GetDescription() string {
	if x != nil && x.Description != nil {
		return *x.Description
	}
	return ""
}

func (x *PayInRequestData) GetCard() *CardDataForBank {
	if x != nil {
		return x.Card
	}
	return nil
}

func (x *PayInRequestData) GetUser() *UserDataForBank {
	if x != nil {
		return x.User
	}
	return nil
}

func (x *PayInRequestData) GetTerminal() *TerminalDataForBank {
	if x != nil {
		return x.Terminal
	}
	return nil
}

func (x *PayInRequestData) GetAggregatedTypeId() uint64 {
	if x != nil && x.AggregatedTypeId != nil {
		return *x.AggregatedTypeId
	}
	return 0
}

func (x *PayInRequestData) GetCardInfo() *CardInfo {
	if x != nil {
		return x.CardInfo
	}
	return nil
}

func (x *PayInRequestData) GetMerchantInfo() *MerchantInfo {
	if x != nil {
		return x.MerchantInfo
	}
	return nil
}

func (x *PayInRequestData) GetTransactionCreatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.TransactionCreatedAt
	}
	return nil
}

func (x *PayInRequestData) GetGetForm() bool {
	if x != nil && x.GetForm != nil {
		return *x.GetForm
	}
	return false
}

func (x *PayInRequestData) GetUserIpAddress() string {
	if x != nil && x.UserIpAddress != nil {
		return *x.UserIpAddress
	}
	return ""
}

type OneClickPayInRequestData struct {
	state                protoimpl.MessageState `protogen:"open.v1"`
	TransactionId        *uint64                `protobuf:"varint,1,opt,name=transaction_id,json=transactionId" json:"transaction_id,omitempty"`
	CardId               *string                `protobuf:"bytes,2,opt,name=card_id,json=cardId" json:"card_id,omitempty"`
	Amount               *float64               `protobuf:"fixed64,3,opt,name=amount" json:"amount,omitempty"`
	Description          *string                `protobuf:"bytes,4,opt,name=description" json:"description,omitempty"`
	User                 *UserDataForBank       `protobuf:"bytes,5,opt,name=user" json:"user,omitempty"`
	Terminal             *TerminalDataForBank   `protobuf:"bytes,6,opt,name=terminal" json:"terminal,omitempty"`
	AggregatedTypeId     *uint64                `protobuf:"varint,7,opt,name=aggregated_type_id,json=aggregatedTypeId" json:"aggregated_type_id,omitempty"`
	CardInfo             *CardInfo              `protobuf:"bytes,8,opt,name=card_info,json=cardInfo" json:"card_info,omitempty"`
	MerchantInfo         *MerchantInfo          `protobuf:"bytes,9,opt,name=merchant_info,json=merchantInfo" json:"merchant_info,omitempty"`
	TransactionCreatedAt *timestamppb.Timestamp `protobuf:"bytes,10,opt,name=transaction_created_at,json=transactionCreatedAt" json:"transaction_created_at,omitempty"`
	UserIpAddress        *string                `protobuf:"bytes,11,opt,name=user_ip_address,json=userIpAddress" json:"user_ip_address,omitempty"`
	unknownFields        protoimpl.UnknownFields
	sizeCache            protoimpl.SizeCache
}

func (x *OneClickPayInRequestData) Reset() {
	*x = OneClickPayInRequestData{}
	mi := &file_inner_processing_grpc_multiacquiring_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *OneClickPayInRequestData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*OneClickPayInRequestData) ProtoMessage() {}

func (x *OneClickPayInRequestData) ProtoReflect() protoreflect.Message {
	mi := &file_inner_processing_grpc_multiacquiring_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use OneClickPayInRequestData.ProtoReflect.Descriptor instead.
func (*OneClickPayInRequestData) Descriptor() ([]byte, []int) {
	return file_inner_processing_grpc_multiacquiring_proto_rawDescGZIP(), []int{2}
}

func (x *OneClickPayInRequestData) GetTransactionId() uint64 {
	if x != nil && x.TransactionId != nil {
		return *x.TransactionId
	}
	return 0
}

func (x *OneClickPayInRequestData) GetCardId() string {
	if x != nil && x.CardId != nil {
		return *x.CardId
	}
	return ""
}

func (x *OneClickPayInRequestData) GetAmount() float64 {
	if x != nil && x.Amount != nil {
		return *x.Amount
	}
	return 0
}

func (x *OneClickPayInRequestData) GetDescription() string {
	if x != nil && x.Description != nil {
		return *x.Description
	}
	return ""
}

func (x *OneClickPayInRequestData) GetUser() *UserDataForBank {
	if x != nil {
		return x.User
	}
	return nil
}

func (x *OneClickPayInRequestData) GetTerminal() *TerminalDataForBank {
	if x != nil {
		return x.Terminal
	}
	return nil
}

func (x *OneClickPayInRequestData) GetAggregatedTypeId() uint64 {
	if x != nil && x.AggregatedTypeId != nil {
		return *x.AggregatedTypeId
	}
	return 0
}

func (x *OneClickPayInRequestData) GetCardInfo() *CardInfo {
	if x != nil {
		return x.CardInfo
	}
	return nil
}

func (x *OneClickPayInRequestData) GetMerchantInfo() *MerchantInfo {
	if x != nil {
		return x.MerchantInfo
	}
	return nil
}

func (x *OneClickPayInRequestData) GetTransactionCreatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.TransactionCreatedAt
	}
	return nil
}

func (x *OneClickPayInRequestData) GetUserIpAddress() string {
	if x != nil && x.UserIpAddress != nil {
		return *x.UserIpAddress
	}
	return ""
}

type UserDataForBank struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            *string                `protobuf:"bytes,1,opt,name=id" json:"id,omitempty"`
	Email         *string                `protobuf:"bytes,2,opt,name=email" json:"email,omitempty"`
	Name          *string                `protobuf:"bytes,3,opt,name=name" json:"name,omitempty"`
	Phone         *string                `protobuf:"bytes,4,opt,name=phone" json:"phone,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UserDataForBank) Reset() {
	*x = UserDataForBank{}
	mi := &file_inner_processing_grpc_multiacquiring_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UserDataForBank) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UserDataForBank) ProtoMessage() {}

func (x *UserDataForBank) ProtoReflect() protoreflect.Message {
	mi := &file_inner_processing_grpc_multiacquiring_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UserDataForBank.ProtoReflect.Descriptor instead.
func (*UserDataForBank) Descriptor() ([]byte, []int) {
	return file_inner_processing_grpc_multiacquiring_proto_rawDescGZIP(), []int{3}
}

func (x *UserDataForBank) GetId() string {
	if x != nil && x.Id != nil {
		return *x.Id
	}
	return ""
}

func (x *UserDataForBank) GetEmail() string {
	if x != nil && x.Email != nil {
		return *x.Email
	}
	return ""
}

func (x *UserDataForBank) GetName() string {
	if x != nil && x.Name != nil {
		return *x.Name
	}
	return ""
}

func (x *UserDataForBank) GetPhone() string {
	if x != nil && x.Phone != nil {
		return *x.Phone
	}
	return ""
}

type TerminalDataForBank struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            *uint64                `protobuf:"varint,1,opt,name=id" json:"id,omitempty"`
	AcquirerId    *uint64                `protobuf:"varint,2,opt,name=acquirer_id,json=acquirerId" json:"acquirer_id,omitempty"`
	AcquirerCode  *string                `protobuf:"bytes,3,opt,name=acquirer_code,json=acquirerCode" json:"acquirer_code,omitempty"`
	Config        *structpb.Struct       `protobuf:"bytes,4,opt,name=config" json:"config,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *TerminalDataForBank) Reset() {
	*x = TerminalDataForBank{}
	mi := &file_inner_processing_grpc_multiacquiring_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *TerminalDataForBank) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TerminalDataForBank) ProtoMessage() {}

func (x *TerminalDataForBank) ProtoReflect() protoreflect.Message {
	mi := &file_inner_processing_grpc_multiacquiring_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TerminalDataForBank.ProtoReflect.Descriptor instead.
func (*TerminalDataForBank) Descriptor() ([]byte, []int) {
	return file_inner_processing_grpc_multiacquiring_proto_rawDescGZIP(), []int{4}
}

func (x *TerminalDataForBank) GetId() uint64 {
	if x != nil && x.Id != nil {
		return *x.Id
	}
	return 0
}

func (x *TerminalDataForBank) GetAcquirerId() uint64 {
	if x != nil && x.AcquirerId != nil {
		return *x.AcquirerId
	}
	return 0
}

func (x *TerminalDataForBank) GetAcquirerCode() string {
	if x != nil && x.AcquirerCode != nil {
		return *x.AcquirerCode
	}
	return ""
}

func (x *TerminalDataForBank) GetConfig() *structpb.Struct {
	if x != nil {
		return x.Config
	}
	return nil
}

type CardDataForBank struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Pan           []byte                 `protobuf:"bytes,1,opt,name=pan" json:"pan,omitempty"`
	ExpMonth      []byte                 `protobuf:"bytes,2,opt,name=exp_month,json=expMonth" json:"exp_month,omitempty"`
	ExpYear       []byte                 `protobuf:"bytes,3,opt,name=exp_year,json=expYear" json:"exp_year,omitempty"`
	Cvc           []byte                 `protobuf:"bytes,4,opt,name=cvc" json:"cvc,omitempty"`
	FullName      []byte                 `protobuf:"bytes,5,opt,name=full_name,json=fullName" json:"full_name,omitempty"`
	Token         *string                `protobuf:"bytes,6,opt,name=token" json:"token,omitempty"`
	Save          *bool                  `protobuf:"varint,7,opt,name=save" json:"save,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CardDataForBank) Reset() {
	*x = CardDataForBank{}
	mi := &file_inner_processing_grpc_multiacquiring_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CardDataForBank) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CardDataForBank) ProtoMessage() {}

func (x *CardDataForBank) ProtoReflect() protoreflect.Message {
	mi := &file_inner_processing_grpc_multiacquiring_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CardDataForBank.ProtoReflect.Descriptor instead.
func (*CardDataForBank) Descriptor() ([]byte, []int) {
	return file_inner_processing_grpc_multiacquiring_proto_rawDescGZIP(), []int{5}
}

func (x *CardDataForBank) GetPan() []byte {
	if x != nil {
		return x.Pan
	}
	return nil
}

func (x *CardDataForBank) GetExpMonth() []byte {
	if x != nil {
		return x.ExpMonth
	}
	return nil
}

func (x *CardDataForBank) GetExpYear() []byte {
	if x != nil {
		return x.ExpYear
	}
	return nil
}

func (x *CardDataForBank) GetCvc() []byte {
	if x != nil {
		return x.Cvc
	}
	return nil
}

func (x *CardDataForBank) GetFullName() []byte {
	if x != nil {
		return x.FullName
	}
	return nil
}

func (x *CardDataForBank) GetToken() string {
	if x != nil && x.Token != nil {
		return *x.Token
	}
	return ""
}

func (x *CardDataForBank) GetSave() bool {
	if x != nil && x.Save != nil {
		return *x.Save
	}
	return false
}

type PayInResponseData struct {
	state             protoimpl.MessageState `protogen:"open.v1"`
	TransactionId     *uint64                `protobuf:"varint,1,opt,name=transaction_id,json=transactionId" json:"transaction_id,omitempty"`
	BankReferenceId   *string                `protobuf:"bytes,2,opt,name=bank_reference_id,json=bankReferenceId" json:"bank_reference_id,omitempty"`
	TransactionStatus *EnumTransactionStatus `protobuf:"varint,3,opt,name=transaction_status,json=transactionStatus,enum=processing.transaction.transaction_status.EnumTransactionStatus" json:"transaction_status,omitempty"`
	CardId            *string                `protobuf:"bytes,4,opt,name=card_id,json=cardId" json:"card_id,omitempty"`
	ThreeDS           *ThreeDSDataFromBank   `protobuf:"bytes,5,opt,name=threeDS" json:"threeDS,omitempty"`
	BankResponse      *BankResponse          `protobuf:"bytes,6,opt,name=bank_response,json=bankResponse" json:"bank_response,omitempty"`
	BankOrderId       *string                `protobuf:"bytes,7,opt,name=bank_order_id,json=bankOrderId" json:"bank_order_id,omitempty"`
	Fingerprint       *Fingerprint           `protobuf:"bytes,8,opt,name=fingerprint" json:"fingerprint,omitempty"`
	unknownFields     protoimpl.UnknownFields
	sizeCache         protoimpl.SizeCache
}

func (x *PayInResponseData) Reset() {
	*x = PayInResponseData{}
	mi := &file_inner_processing_grpc_multiacquiring_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PayInResponseData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PayInResponseData) ProtoMessage() {}

func (x *PayInResponseData) ProtoReflect() protoreflect.Message {
	mi := &file_inner_processing_grpc_multiacquiring_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PayInResponseData.ProtoReflect.Descriptor instead.
func (*PayInResponseData) Descriptor() ([]byte, []int) {
	return file_inner_processing_grpc_multiacquiring_proto_rawDescGZIP(), []int{6}
}

func (x *PayInResponseData) GetTransactionId() uint64 {
	if x != nil && x.TransactionId != nil {
		return *x.TransactionId
	}
	return 0
}

func (x *PayInResponseData) GetBankReferenceId() string {
	if x != nil && x.BankReferenceId != nil {
		return *x.BankReferenceId
	}
	return ""
}

func (x *PayInResponseData) GetTransactionStatus() EnumTransactionStatus {
	if x != nil && x.TransactionStatus != nil {
		return *x.TransactionStatus
	}
	return EnumTransactionStatus_Unknown
}

func (x *PayInResponseData) GetCardId() string {
	if x != nil && x.CardId != nil {
		return *x.CardId
	}
	return ""
}

func (x *PayInResponseData) GetThreeDS() *ThreeDSDataFromBank {
	if x != nil {
		return x.ThreeDS
	}
	return nil
}

func (x *PayInResponseData) GetBankResponse() *BankResponse {
	if x != nil {
		return x.BankResponse
	}
	return nil
}

func (x *PayInResponseData) GetBankOrderId() string {
	if x != nil && x.BankOrderId != nil {
		return *x.BankOrderId
	}
	return ""
}

func (x *PayInResponseData) GetFingerprint() *Fingerprint {
	if x != nil {
		return x.Fingerprint
	}
	return nil
}

type ThreeDSDataFromBank struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Action        *string                `protobuf:"bytes,1,opt,name=action" json:"action,omitempty"`
	Params        *structpb.Struct       `protobuf:"bytes,2,opt,name=params" json:"params,omitempty"`
	Template      []byte                 `protobuf:"bytes,3,opt,name=template" json:"template,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ThreeDSDataFromBank) Reset() {
	*x = ThreeDSDataFromBank{}
	mi := &file_inner_processing_grpc_multiacquiring_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ThreeDSDataFromBank) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ThreeDSDataFromBank) ProtoMessage() {}

func (x *ThreeDSDataFromBank) ProtoReflect() protoreflect.Message {
	mi := &file_inner_processing_grpc_multiacquiring_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ThreeDSDataFromBank.ProtoReflect.Descriptor instead.
func (*ThreeDSDataFromBank) Descriptor() ([]byte, []int) {
	return file_inner_processing_grpc_multiacquiring_proto_rawDescGZIP(), []int{7}
}

func (x *ThreeDSDataFromBank) GetAction() string {
	if x != nil && x.Action != nil {
		return *x.Action
	}
	return ""
}

func (x *ThreeDSDataFromBank) GetParams() *structpb.Struct {
	if x != nil {
		return x.Params
	}
	return nil
}

func (x *ThreeDSDataFromBank) GetTemplate() []byte {
	if x != nil {
		return x.Template
	}
	return nil
}

type ThreeDSRequestData struct {
	state           protoimpl.MessageState `protogen:"open.v1"`
	TransactionId   *uint64                `protobuf:"varint,1,opt,name=transaction_id,json=transactionId" json:"transaction_id,omitempty"`
	BankReferenceId *string                `protobuf:"bytes,2,opt,name=bank_reference_id,json=bankReferenceId" json:"bank_reference_id,omitempty"`
	Pares           *string                `protobuf:"bytes,3,opt,name=pares" json:"pares,omitempty"`
	Md              *string                `protobuf:"bytes,4,opt,name=md" json:"md,omitempty"`
	Amount          *float64               `protobuf:"fixed64,5,opt,name=amount" json:"amount,omitempty"`
	Cres            *string                `protobuf:"bytes,6,opt,name=cres" json:"cres,omitempty"`
	Terminal        *TerminalDataForBank   `protobuf:"bytes,7,opt,name=terminal" json:"terminal,omitempty"`
	BankOrderId     *string                `protobuf:"bytes,8,opt,name=bank_order_id,json=bankOrderId" json:"bank_order_id,omitempty"`
	IsHold          *bool                  `protobuf:"varint,9,opt,name=is_hold,json=isHold" json:"is_hold,omitempty"`
	unknownFields   protoimpl.UnknownFields
	sizeCache       protoimpl.SizeCache
}

func (x *ThreeDSRequestData) Reset() {
	*x = ThreeDSRequestData{}
	mi := &file_inner_processing_grpc_multiacquiring_proto_msgTypes[8]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ThreeDSRequestData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ThreeDSRequestData) ProtoMessage() {}

func (x *ThreeDSRequestData) ProtoReflect() protoreflect.Message {
	mi := &file_inner_processing_grpc_multiacquiring_proto_msgTypes[8]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ThreeDSRequestData.ProtoReflect.Descriptor instead.
func (*ThreeDSRequestData) Descriptor() ([]byte, []int) {
	return file_inner_processing_grpc_multiacquiring_proto_rawDescGZIP(), []int{8}
}

func (x *ThreeDSRequestData) GetTransactionId() uint64 {
	if x != nil && x.TransactionId != nil {
		return *x.TransactionId
	}
	return 0
}

func (x *ThreeDSRequestData) GetBankReferenceId() string {
	if x != nil && x.BankReferenceId != nil {
		return *x.BankReferenceId
	}
	return ""
}

func (x *ThreeDSRequestData) GetPares() string {
	if x != nil && x.Pares != nil {
		return *x.Pares
	}
	return ""
}

func (x *ThreeDSRequestData) GetMd() string {
	if x != nil && x.Md != nil {
		return *x.Md
	}
	return ""
}

func (x *ThreeDSRequestData) GetAmount() float64 {
	if x != nil && x.Amount != nil {
		return *x.Amount
	}
	return 0
}

func (x *ThreeDSRequestData) GetCres() string {
	if x != nil && x.Cres != nil {
		return *x.Cres
	}
	return ""
}

func (x *ThreeDSRequestData) GetTerminal() *TerminalDataForBank {
	if x != nil {
		return x.Terminal
	}
	return nil
}

func (x *ThreeDSRequestData) GetBankOrderId() string {
	if x != nil && x.BankOrderId != nil {
		return *x.BankOrderId
	}
	return ""
}

func (x *ThreeDSRequestData) GetIsHold() bool {
	if x != nil && x.IsHold != nil {
		return *x.IsHold
	}
	return false
}

type ThreeDSResponseData struct {
	state             protoimpl.MessageState `protogen:"open.v1"`
	TransactionId     *uint64                `protobuf:"varint,1,opt,name=transaction_id,json=transactionId" json:"transaction_id,omitempty"`
	BankReferenceId   *string                `protobuf:"bytes,2,opt,name=bank_reference_id,json=bankReferenceId" json:"bank_reference_id,omitempty"`
	BankOrderId       *string                `protobuf:"bytes,3,opt,name=bank_order_id,json=bankOrderId" json:"bank_order_id,omitempty"`
	TransactionStatus *EnumTransactionStatus `protobuf:"varint,4,opt,name=transaction_status,json=transactionStatus,enum=processing.transaction.transaction_status.EnumTransactionStatus" json:"transaction_status,omitempty"`
	CardToken         *string                `protobuf:"bytes,5,opt,name=card_token,json=cardToken" json:"card_token,omitempty"`
	BankResponse      *BankResponse          `protobuf:"bytes,6,opt,name=bank_response,json=bankResponse" json:"bank_response,omitempty"`
	unknownFields     protoimpl.UnknownFields
	sizeCache         protoimpl.SizeCache
}

func (x *ThreeDSResponseData) Reset() {
	*x = ThreeDSResponseData{}
	mi := &file_inner_processing_grpc_multiacquiring_proto_msgTypes[9]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ThreeDSResponseData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ThreeDSResponseData) ProtoMessage() {}

func (x *ThreeDSResponseData) ProtoReflect() protoreflect.Message {
	mi := &file_inner_processing_grpc_multiacquiring_proto_msgTypes[9]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ThreeDSResponseData.ProtoReflect.Descriptor instead.
func (*ThreeDSResponseData) Descriptor() ([]byte, []int) {
	return file_inner_processing_grpc_multiacquiring_proto_rawDescGZIP(), []int{9}
}

func (x *ThreeDSResponseData) GetTransactionId() uint64 {
	if x != nil && x.TransactionId != nil {
		return *x.TransactionId
	}
	return 0
}

func (x *ThreeDSResponseData) GetBankReferenceId() string {
	if x != nil && x.BankReferenceId != nil {
		return *x.BankReferenceId
	}
	return ""
}

func (x *ThreeDSResponseData) GetBankOrderId() string {
	if x != nil && x.BankOrderId != nil {
		return *x.BankOrderId
	}
	return ""
}

func (x *ThreeDSResponseData) GetTransactionStatus() EnumTransactionStatus {
	if x != nil && x.TransactionStatus != nil {
		return *x.TransactionStatus
	}
	return EnumTransactionStatus_Unknown
}

func (x *ThreeDSResponseData) GetCardToken() string {
	if x != nil && x.CardToken != nil {
		return *x.CardToken
	}
	return ""
}

func (x *ThreeDSResponseData) GetBankResponse() *BankResponse {
	if x != nil {
		return x.BankResponse
	}
	return nil
}

type PayOutRequestData struct {
	state                protoimpl.MessageState `protogen:"open.v1"`
	TransactionId        *uint64                `protobuf:"varint,1,opt,name=transaction_id,json=transactionId" json:"transaction_id,omitempty"`
	Pan                  []byte                 `protobuf:"bytes,2,opt,name=pan" json:"pan,omitempty"`
	Amount               *float64               `protobuf:"fixed64,3,opt,name=amount" json:"amount,omitempty"`
	User                 *UserDataForBank       `protobuf:"bytes,4,opt,name=user" json:"user,omitempty"`
	Terminal             *TerminalDataForBank   `protobuf:"bytes,5,opt,name=terminal" json:"terminal,omitempty"`
	Card                 *PayOutCard            `protobuf:"bytes,6,opt,name=card" json:"card,omitempty"`
	AggregatedTypeId     *uint64                `protobuf:"varint,7,opt,name=aggregated_type_id,json=aggregatedTypeId" json:"aggregated_type_id,omitempty"`
	CardInfo             *CardInfo              `protobuf:"bytes,8,opt,name=card_info,json=cardInfo" json:"card_info,omitempty"`
	MerchantInfo         *MerchantInfo          `protobuf:"bytes,9,opt,name=merchant_info,json=merchantInfo" json:"merchant_info,omitempty"`
	TransactionCreatedAt *timestamppb.Timestamp `protobuf:"bytes,10,opt,name=transaction_created_at,json=transactionCreatedAt" json:"transaction_created_at,omitempty"`
	CInfo                *string                `protobuf:"bytes,11,opt,name=c_info,json=cInfo" json:"c_info,omitempty"`
	unknownFields        protoimpl.UnknownFields
	sizeCache            protoimpl.SizeCache
}

func (x *PayOutRequestData) Reset() {
	*x = PayOutRequestData{}
	mi := &file_inner_processing_grpc_multiacquiring_proto_msgTypes[10]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PayOutRequestData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PayOutRequestData) ProtoMessage() {}

func (x *PayOutRequestData) ProtoReflect() protoreflect.Message {
	mi := &file_inner_processing_grpc_multiacquiring_proto_msgTypes[10]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PayOutRequestData.ProtoReflect.Descriptor instead.
func (*PayOutRequestData) Descriptor() ([]byte, []int) {
	return file_inner_processing_grpc_multiacquiring_proto_rawDescGZIP(), []int{10}
}

func (x *PayOutRequestData) GetTransactionId() uint64 {
	if x != nil && x.TransactionId != nil {
		return *x.TransactionId
	}
	return 0
}

func (x *PayOutRequestData) GetPan() []byte {
	if x != nil {
		return x.Pan
	}
	return nil
}

func (x *PayOutRequestData) GetAmount() float64 {
	if x != nil && x.Amount != nil {
		return *x.Amount
	}
	return 0
}

func (x *PayOutRequestData) GetUser() *UserDataForBank {
	if x != nil {
		return x.User
	}
	return nil
}

func (x *PayOutRequestData) GetTerminal() *TerminalDataForBank {
	if x != nil {
		return x.Terminal
	}
	return nil
}

func (x *PayOutRequestData) GetCard() *PayOutCard {
	if x != nil {
		return x.Card
	}
	return nil
}

func (x *PayOutRequestData) GetAggregatedTypeId() uint64 {
	if x != nil && x.AggregatedTypeId != nil {
		return *x.AggregatedTypeId
	}
	return 0
}

func (x *PayOutRequestData) GetCardInfo() *CardInfo {
	if x != nil {
		return x.CardInfo
	}
	return nil
}

func (x *PayOutRequestData) GetMerchantInfo() *MerchantInfo {
	if x != nil {
		return x.MerchantInfo
	}
	return nil
}

func (x *PayOutRequestData) GetTransactionCreatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.TransactionCreatedAt
	}
	return nil
}

func (x *PayOutRequestData) GetCInfo() string {
	if x != nil && x.CInfo != nil {
		return *x.CInfo
	}
	return ""
}

type PayOutByPhoneRequestData struct {
	state                protoimpl.MessageState `protogen:"open.v1"`
	TransactionId        *uint64                `protobuf:"varint,1,opt,name=transaction_id,json=transactionId" json:"transaction_id,omitempty"`
	VisaAliasToken       *string                `protobuf:"bytes,2,opt,name=visa_alias_token,json=visaAliasToken" json:"visa_alias_token,omitempty"`
	Amount               *float64               `protobuf:"fixed64,3,opt,name=amount" json:"amount,omitempty"`
	User                 *UserDataForBank       `protobuf:"bytes,4,opt,name=user" json:"user,omitempty"`
	Terminal             *TerminalDataForBank   `protobuf:"bytes,5,opt,name=terminal" json:"terminal,omitempty"`
	Card                 *PayOutCard            `protobuf:"bytes,6,opt,name=card" json:"card,omitempty"`
	AggregatedTypeId     *uint64                `protobuf:"varint,7,opt,name=aggregated_type_id,json=aggregatedTypeId" json:"aggregated_type_id,omitempty"`
	CardInfo             *CardInfo              `protobuf:"bytes,8,opt,name=card_info,json=cardInfo" json:"card_info,omitempty"`
	MerchantInfo         *MerchantInfo          `protobuf:"bytes,9,opt,name=merchant_info,json=merchantInfo" json:"merchant_info,omitempty"`
	TransactionCreatedAt *timestamppb.Timestamp `protobuf:"bytes,10,opt,name=transaction_created_at,json=transactionCreatedAt" json:"transaction_created_at,omitempty"`
	CInfo                *string                `protobuf:"bytes,11,opt,name=c_info,json=cInfo" json:"c_info,omitempty"`
	unknownFields        protoimpl.UnknownFields
	sizeCache            protoimpl.SizeCache
}

func (x *PayOutByPhoneRequestData) Reset() {
	*x = PayOutByPhoneRequestData{}
	mi := &file_inner_processing_grpc_multiacquiring_proto_msgTypes[11]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PayOutByPhoneRequestData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PayOutByPhoneRequestData) ProtoMessage() {}

func (x *PayOutByPhoneRequestData) ProtoReflect() protoreflect.Message {
	mi := &file_inner_processing_grpc_multiacquiring_proto_msgTypes[11]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PayOutByPhoneRequestData.ProtoReflect.Descriptor instead.
func (*PayOutByPhoneRequestData) Descriptor() ([]byte, []int) {
	return file_inner_processing_grpc_multiacquiring_proto_rawDescGZIP(), []int{11}
}

func (x *PayOutByPhoneRequestData) GetTransactionId() uint64 {
	if x != nil && x.TransactionId != nil {
		return *x.TransactionId
	}
	return 0
}

func (x *PayOutByPhoneRequestData) GetVisaAliasToken() string {
	if x != nil && x.VisaAliasToken != nil {
		return *x.VisaAliasToken
	}
	return ""
}

func (x *PayOutByPhoneRequestData) GetAmount() float64 {
	if x != nil && x.Amount != nil {
		return *x.Amount
	}
	return 0
}

func (x *PayOutByPhoneRequestData) GetUser() *UserDataForBank {
	if x != nil {
		return x.User
	}
	return nil
}

func (x *PayOutByPhoneRequestData) GetTerminal() *TerminalDataForBank {
	if x != nil {
		return x.Terminal
	}
	return nil
}

func (x *PayOutByPhoneRequestData) GetCard() *PayOutCard {
	if x != nil {
		return x.Card
	}
	return nil
}

func (x *PayOutByPhoneRequestData) GetAggregatedTypeId() uint64 {
	if x != nil && x.AggregatedTypeId != nil {
		return *x.AggregatedTypeId
	}
	return 0
}

func (x *PayOutByPhoneRequestData) GetCardInfo() *CardInfo {
	if x != nil {
		return x.CardInfo
	}
	return nil
}

func (x *PayOutByPhoneRequestData) GetMerchantInfo() *MerchantInfo {
	if x != nil {
		return x.MerchantInfo
	}
	return nil
}

func (x *PayOutByPhoneRequestData) GetTransactionCreatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.TransactionCreatedAt
	}
	return nil
}

func (x *PayOutByPhoneRequestData) GetCInfo() string {
	if x != nil && x.CInfo != nil {
		return *x.CInfo
	}
	return ""
}

type PayOutResponseByPhoneData struct {
	state             protoimpl.MessageState `protogen:"open.v1"`
	TransactionId     *uint64                `protobuf:"varint,1,opt,name=transaction_id,json=transactionId" json:"transaction_id,omitempty"`
	BankReferenceId   *string                `protobuf:"bytes,2,opt,name=bank_reference_id,json=bankReferenceId" json:"bank_reference_id,omitempty"`
	BankOrderId       *string                `protobuf:"bytes,3,opt,name=bank_order_id,json=bankOrderId" json:"bank_order_id,omitempty"`
	TransactionStatus *EnumTransactionStatus `protobuf:"varint,4,opt,name=transaction_status,json=transactionStatus,enum=processing.transaction.transaction_status.EnumTransactionStatus" json:"transaction_status,omitempty"`
	BankResponse      *BankResponse          `protobuf:"bytes,5,opt,name=bank_response,json=bankResponse" json:"bank_response,omitempty"`
	unknownFields     protoimpl.UnknownFields
	sizeCache         protoimpl.SizeCache
}

func (x *PayOutResponseByPhoneData) Reset() {
	*x = PayOutResponseByPhoneData{}
	mi := &file_inner_processing_grpc_multiacquiring_proto_msgTypes[12]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PayOutResponseByPhoneData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PayOutResponseByPhoneData) ProtoMessage() {}

func (x *PayOutResponseByPhoneData) ProtoReflect() protoreflect.Message {
	mi := &file_inner_processing_grpc_multiacquiring_proto_msgTypes[12]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PayOutResponseByPhoneData.ProtoReflect.Descriptor instead.
func (*PayOutResponseByPhoneData) Descriptor() ([]byte, []int) {
	return file_inner_processing_grpc_multiacquiring_proto_rawDescGZIP(), []int{12}
}

func (x *PayOutResponseByPhoneData) GetTransactionId() uint64 {
	if x != nil && x.TransactionId != nil {
		return *x.TransactionId
	}
	return 0
}

func (x *PayOutResponseByPhoneData) GetBankReferenceId() string {
	if x != nil && x.BankReferenceId != nil {
		return *x.BankReferenceId
	}
	return ""
}

func (x *PayOutResponseByPhoneData) GetBankOrderId() string {
	if x != nil && x.BankOrderId != nil {
		return *x.BankOrderId
	}
	return ""
}

func (x *PayOutResponseByPhoneData) GetTransactionStatus() EnumTransactionStatus {
	if x != nil && x.TransactionStatus != nil {
		return *x.TransactionStatus
	}
	return EnumTransactionStatus_Unknown
}

func (x *PayOutResponseByPhoneData) GetBankResponse() *BankResponse {
	if x != nil {
		return x.BankResponse
	}
	return nil
}

type PayOutCard struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Pan           []byte                 `protobuf:"bytes,1,opt,name=pan" json:"pan,omitempty"`
	ExpMonth      []byte                 `protobuf:"bytes,2,opt,name=exp_month,json=expMonth" json:"exp_month,omitempty"`
	ExpYear       []byte                 `protobuf:"bytes,3,opt,name=exp_year,json=expYear" json:"exp_year,omitempty"`
	Cvc           []byte                 `protobuf:"bytes,4,opt,name=cvc" json:"cvc,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *PayOutCard) Reset() {
	*x = PayOutCard{}
	mi := &file_inner_processing_grpc_multiacquiring_proto_msgTypes[13]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PayOutCard) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PayOutCard) ProtoMessage() {}

func (x *PayOutCard) ProtoReflect() protoreflect.Message {
	mi := &file_inner_processing_grpc_multiacquiring_proto_msgTypes[13]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PayOutCard.ProtoReflect.Descriptor instead.
func (*PayOutCard) Descriptor() ([]byte, []int) {
	return file_inner_processing_grpc_multiacquiring_proto_rawDescGZIP(), []int{13}
}

func (x *PayOutCard) GetPan() []byte {
	if x != nil {
		return x.Pan
	}
	return nil
}

func (x *PayOutCard) GetExpMonth() []byte {
	if x != nil {
		return x.ExpMonth
	}
	return nil
}

func (x *PayOutCard) GetExpYear() []byte {
	if x != nil {
		return x.ExpYear
	}
	return nil
}

func (x *PayOutCard) GetCvc() []byte {
	if x != nil {
		return x.Cvc
	}
	return nil
}

type PayOutResponseData struct {
	state             protoimpl.MessageState `protogen:"open.v1"`
	TransactionId     *uint64                `protobuf:"varint,1,opt,name=transaction_id,json=transactionId" json:"transaction_id,omitempty"`
	BankReferenceId   *string                `protobuf:"bytes,2,opt,name=bank_reference_id,json=bankReferenceId" json:"bank_reference_id,omitempty"`
	BankOrderId       *string                `protobuf:"bytes,3,opt,name=bank_order_id,json=bankOrderId" json:"bank_order_id,omitempty"`
	TransactionStatus *EnumTransactionStatus `protobuf:"varint,4,opt,name=transaction_status,json=transactionStatus,enum=processing.transaction.transaction_status.EnumTransactionStatus" json:"transaction_status,omitempty"`
	BankResponse      *BankResponse          `protobuf:"bytes,5,opt,name=bank_response,json=bankResponse" json:"bank_response,omitempty"`
	unknownFields     protoimpl.UnknownFields
	sizeCache         protoimpl.SizeCache
}

func (x *PayOutResponseData) Reset() {
	*x = PayOutResponseData{}
	mi := &file_inner_processing_grpc_multiacquiring_proto_msgTypes[14]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PayOutResponseData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PayOutResponseData) ProtoMessage() {}

func (x *PayOutResponseData) ProtoReflect() protoreflect.Message {
	mi := &file_inner_processing_grpc_multiacquiring_proto_msgTypes[14]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PayOutResponseData.ProtoReflect.Descriptor instead.
func (*PayOutResponseData) Descriptor() ([]byte, []int) {
	return file_inner_processing_grpc_multiacquiring_proto_rawDescGZIP(), []int{14}
}

func (x *PayOutResponseData) GetTransactionId() uint64 {
	if x != nil && x.TransactionId != nil {
		return *x.TransactionId
	}
	return 0
}

func (x *PayOutResponseData) GetBankReferenceId() string {
	if x != nil && x.BankReferenceId != nil {
		return *x.BankReferenceId
	}
	return ""
}

func (x *PayOutResponseData) GetBankOrderId() string {
	if x != nil && x.BankOrderId != nil {
		return *x.BankOrderId
	}
	return ""
}

func (x *PayOutResponseData) GetTransactionStatus() EnumTransactionStatus {
	if x != nil && x.TransactionStatus != nil {
		return *x.TransactionStatus
	}
	return EnumTransactionStatus_Unknown
}

func (x *PayOutResponseData) GetBankResponse() *BankResponse {
	if x != nil {
		return x.BankResponse
	}
	return nil
}

type BankTransactionStatusRequest struct {
	state                protoimpl.MessageState `protogen:"open.v1"`
	TransactionId        *uint64                `protobuf:"varint,1,opt,name=transaction_id,json=transactionId" json:"transaction_id,omitempty"`
	TransactionType      *string                `protobuf:"bytes,2,opt,name=transaction_type,json=transactionType" json:"transaction_type,omitempty"`
	AcquirerCode         *string                `protobuf:"bytes,3,opt,name=acquirer_code,json=acquirerCode" json:"acquirer_code,omitempty"`
	Config               *structpb.Struct       `protobuf:"bytes,4,opt,name=config" json:"config,omitempty"`
	BankReferenceId      *string                `protobuf:"bytes,5,opt,name=bank_reference_id,json=bankReferenceId" json:"bank_reference_id,omitempty"`
	BankOrderId          *string                `protobuf:"bytes,6,opt,name=bank_order_id,json=bankOrderId" json:"bank_order_id,omitempty"`
	Terminal             *TerminalDataForBank   `protobuf:"bytes,7,opt,name=terminal" json:"terminal,omitempty"`
	TransactionCreatedAt *timestamppb.Timestamp `protobuf:"bytes,8,opt,name=transaction_created_at,json=transactionCreatedAt" json:"transaction_created_at,omitempty"`
	unknownFields        protoimpl.UnknownFields
	sizeCache            protoimpl.SizeCache
}

func (x *BankTransactionStatusRequest) Reset() {
	*x = BankTransactionStatusRequest{}
	mi := &file_inner_processing_grpc_multiacquiring_proto_msgTypes[15]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BankTransactionStatusRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BankTransactionStatusRequest) ProtoMessage() {}

func (x *BankTransactionStatusRequest) ProtoReflect() protoreflect.Message {
	mi := &file_inner_processing_grpc_multiacquiring_proto_msgTypes[15]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BankTransactionStatusRequest.ProtoReflect.Descriptor instead.
func (*BankTransactionStatusRequest) Descriptor() ([]byte, []int) {
	return file_inner_processing_grpc_multiacquiring_proto_rawDescGZIP(), []int{15}
}

func (x *BankTransactionStatusRequest) GetTransactionId() uint64 {
	if x != nil && x.TransactionId != nil {
		return *x.TransactionId
	}
	return 0
}

func (x *BankTransactionStatusRequest) GetTransactionType() string {
	if x != nil && x.TransactionType != nil {
		return *x.TransactionType
	}
	return ""
}

func (x *BankTransactionStatusRequest) GetAcquirerCode() string {
	if x != nil && x.AcquirerCode != nil {
		return *x.AcquirerCode
	}
	return ""
}

func (x *BankTransactionStatusRequest) GetConfig() *structpb.Struct {
	if x != nil {
		return x.Config
	}
	return nil
}

func (x *BankTransactionStatusRequest) GetBankReferenceId() string {
	if x != nil && x.BankReferenceId != nil {
		return *x.BankReferenceId
	}
	return ""
}

func (x *BankTransactionStatusRequest) GetBankOrderId() string {
	if x != nil && x.BankOrderId != nil {
		return *x.BankOrderId
	}
	return ""
}

func (x *BankTransactionStatusRequest) GetTerminal() *TerminalDataForBank {
	if x != nil {
		return x.Terminal
	}
	return nil
}

func (x *BankTransactionStatusRequest) GetTransactionCreatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.TransactionCreatedAt
	}
	return nil
}

type BankTransactionStatusResponse struct {
	state             protoimpl.MessageState `protogen:"open.v1"`
	TransactionId     *uint64                `protobuf:"varint,1,opt,name=transaction_id,json=transactionId" json:"transaction_id,omitempty"`
	BankReferenceId   *string                `protobuf:"bytes,2,opt,name=bank_reference_id,json=bankReferenceId" json:"bank_reference_id,omitempty"`
	TransactionStatus *EnumTransactionStatus `protobuf:"varint,3,opt,name=transaction_status,json=transactionStatus,enum=processing.transaction.transaction_status.EnumTransactionStatus" json:"transaction_status,omitempty"`
	BankResponse      *BankResponse          `protobuf:"bytes,4,opt,name=bank_response,json=bankResponse" json:"bank_response,omitempty"`
	unknownFields     protoimpl.UnknownFields
	sizeCache         protoimpl.SizeCache
}

func (x *BankTransactionStatusResponse) Reset() {
	*x = BankTransactionStatusResponse{}
	mi := &file_inner_processing_grpc_multiacquiring_proto_msgTypes[16]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BankTransactionStatusResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BankTransactionStatusResponse) ProtoMessage() {}

func (x *BankTransactionStatusResponse) ProtoReflect() protoreflect.Message {
	mi := &file_inner_processing_grpc_multiacquiring_proto_msgTypes[16]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BankTransactionStatusResponse.ProtoReflect.Descriptor instead.
func (*BankTransactionStatusResponse) Descriptor() ([]byte, []int) {
	return file_inner_processing_grpc_multiacquiring_proto_rawDescGZIP(), []int{16}
}

func (x *BankTransactionStatusResponse) GetTransactionId() uint64 {
	if x != nil && x.TransactionId != nil {
		return *x.TransactionId
	}
	return 0
}

func (x *BankTransactionStatusResponse) GetBankReferenceId() string {
	if x != nil && x.BankReferenceId != nil {
		return *x.BankReferenceId
	}
	return ""
}

func (x *BankTransactionStatusResponse) GetTransactionStatus() EnumTransactionStatus {
	if x != nil && x.TransactionStatus != nil {
		return *x.TransactionStatus
	}
	return EnumTransactionStatus_Unknown
}

func (x *BankTransactionStatusResponse) GetBankResponse() *BankResponse {
	if x != nil {
		return x.BankResponse
	}
	return nil
}

type BankTransactionStatusUnformatedRequest struct {
	state                protoimpl.MessageState `protogen:"open.v1"`
	TransactionId        *uint64                `protobuf:"varint,1,opt,name=transaction_id,json=transactionId" json:"transaction_id,omitempty"`
	TransactionType      *string                `protobuf:"bytes,2,opt,name=transaction_type,json=transactionType" json:"transaction_type,omitempty"`
	AcquirerCode         *string                `protobuf:"bytes,3,opt,name=acquirer_code,json=acquirerCode" json:"acquirer_code,omitempty"`
	Config               *structpb.Struct       `protobuf:"bytes,4,opt,name=config" json:"config,omitempty"`
	BankReferenceId      *string                `protobuf:"bytes,5,opt,name=bank_reference_id,json=bankReferenceId" json:"bank_reference_id,omitempty"`
	BankOrderId          *string                `protobuf:"bytes,6,opt,name=bank_order_id,json=bankOrderId" json:"bank_order_id,omitempty"`
	Terminal             *TerminalDataForBank   `protobuf:"bytes,7,opt,name=terminal" json:"terminal,omitempty"`
	TransactionCreatedAt *timestamppb.Timestamp `protobuf:"bytes,8,opt,name=transaction_created_at,json=transactionCreatedAt" json:"transaction_created_at,omitempty"`
	unknownFields        protoimpl.UnknownFields
	sizeCache            protoimpl.SizeCache
}

func (x *BankTransactionStatusUnformatedRequest) Reset() {
	*x = BankTransactionStatusUnformatedRequest{}
	mi := &file_inner_processing_grpc_multiacquiring_proto_msgTypes[17]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BankTransactionStatusUnformatedRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BankTransactionStatusUnformatedRequest) ProtoMessage() {}

func (x *BankTransactionStatusUnformatedRequest) ProtoReflect() protoreflect.Message {
	mi := &file_inner_processing_grpc_multiacquiring_proto_msgTypes[17]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BankTransactionStatusUnformatedRequest.ProtoReflect.Descriptor instead.
func (*BankTransactionStatusUnformatedRequest) Descriptor() ([]byte, []int) {
	return file_inner_processing_grpc_multiacquiring_proto_rawDescGZIP(), []int{17}
}

func (x *BankTransactionStatusUnformatedRequest) GetTransactionId() uint64 {
	if x != nil && x.TransactionId != nil {
		return *x.TransactionId
	}
	return 0
}

func (x *BankTransactionStatusUnformatedRequest) GetTransactionType() string {
	if x != nil && x.TransactionType != nil {
		return *x.TransactionType
	}
	return ""
}

func (x *BankTransactionStatusUnformatedRequest) GetAcquirerCode() string {
	if x != nil && x.AcquirerCode != nil {
		return *x.AcquirerCode
	}
	return ""
}

func (x *BankTransactionStatusUnformatedRequest) GetConfig() *structpb.Struct {
	if x != nil {
		return x.Config
	}
	return nil
}

func (x *BankTransactionStatusUnformatedRequest) GetBankReferenceId() string {
	if x != nil && x.BankReferenceId != nil {
		return *x.BankReferenceId
	}
	return ""
}

func (x *BankTransactionStatusUnformatedRequest) GetBankOrderId() string {
	if x != nil && x.BankOrderId != nil {
		return *x.BankOrderId
	}
	return ""
}

func (x *BankTransactionStatusUnformatedRequest) GetTerminal() *TerminalDataForBank {
	if x != nil {
		return x.Terminal
	}
	return nil
}

func (x *BankTransactionStatusUnformatedRequest) GetTransactionCreatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.TransactionCreatedAt
	}
	return nil
}

type BankTransactionStatusUnformatedResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	TransactionId *uint64                `protobuf:"varint,1,opt,name=transaction_id,json=transactionId" json:"transaction_id,omitempty"`
	BankMessage   *string                `protobuf:"bytes,2,opt,name=bank_message,json=bankMessage" json:"bank_message,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *BankTransactionStatusUnformatedResponse) Reset() {
	*x = BankTransactionStatusUnformatedResponse{}
	mi := &file_inner_processing_grpc_multiacquiring_proto_msgTypes[18]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BankTransactionStatusUnformatedResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BankTransactionStatusUnformatedResponse) ProtoMessage() {}

func (x *BankTransactionStatusUnformatedResponse) ProtoReflect() protoreflect.Message {
	mi := &file_inner_processing_grpc_multiacquiring_proto_msgTypes[18]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BankTransactionStatusUnformatedResponse.ProtoReflect.Descriptor instead.
func (*BankTransactionStatusUnformatedResponse) Descriptor() ([]byte, []int) {
	return file_inner_processing_grpc_multiacquiring_proto_rawDescGZIP(), []int{18}
}

func (x *BankTransactionStatusUnformatedResponse) GetTransactionId() uint64 {
	if x != nil && x.TransactionId != nil {
		return *x.TransactionId
	}
	return 0
}

func (x *BankTransactionStatusUnformatedResponse) GetBankMessage() string {
	if x != nil && x.BankMessage != nil {
		return *x.BankMessage
	}
	return ""
}

type RefundRequest struct {
	state                protoimpl.MessageState `protogen:"open.v1"`
	TransactionId        *uint64                `protobuf:"varint,1,opt,name=transaction_id,json=transactionId" json:"transaction_id,omitempty"`
	RefundAmount         *float64               `protobuf:"fixed64,2,opt,name=refund_amount,json=refundAmount" json:"refund_amount,omitempty"`
	Terminal             *TerminalDataForBank   `protobuf:"bytes,3,opt,name=terminal" json:"terminal,omitempty"`
	BankReferenceId      *string                `protobuf:"bytes,4,opt,name=bank_reference_id,json=bankReferenceId" json:"bank_reference_id,omitempty"`
	BankOrderId          *string                `protobuf:"bytes,5,opt,name=bank_order_id,json=bankOrderId" json:"bank_order_id,omitempty"`
	TransactionCreatedAt *timestamppb.Timestamp `protobuf:"bytes,6,opt,name=transaction_created_at,json=transactionCreatedAt" json:"transaction_created_at,omitempty"`
	TransactionAmount    *float64               `protobuf:"fixed64,7,opt,name=transaction_amount,json=transactionAmount" json:"transaction_amount,omitempty"`
	unknownFields        protoimpl.UnknownFields
	sizeCache            protoimpl.SizeCache
}

func (x *RefundRequest) Reset() {
	*x = RefundRequest{}
	mi := &file_inner_processing_grpc_multiacquiring_proto_msgTypes[19]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RefundRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RefundRequest) ProtoMessage() {}

func (x *RefundRequest) ProtoReflect() protoreflect.Message {
	mi := &file_inner_processing_grpc_multiacquiring_proto_msgTypes[19]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RefundRequest.ProtoReflect.Descriptor instead.
func (*RefundRequest) Descriptor() ([]byte, []int) {
	return file_inner_processing_grpc_multiacquiring_proto_rawDescGZIP(), []int{19}
}

func (x *RefundRequest) GetTransactionId() uint64 {
	if x != nil && x.TransactionId != nil {
		return *x.TransactionId
	}
	return 0
}

func (x *RefundRequest) GetRefundAmount() float64 {
	if x != nil && x.RefundAmount != nil {
		return *x.RefundAmount
	}
	return 0
}

func (x *RefundRequest) GetTerminal() *TerminalDataForBank {
	if x != nil {
		return x.Terminal
	}
	return nil
}

func (x *RefundRequest) GetBankReferenceId() string {
	if x != nil && x.BankReferenceId != nil {
		return *x.BankReferenceId
	}
	return ""
}

func (x *RefundRequest) GetBankOrderId() string {
	if x != nil && x.BankOrderId != nil {
		return *x.BankOrderId
	}
	return ""
}

func (x *RefundRequest) GetTransactionCreatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.TransactionCreatedAt
	}
	return nil
}

func (x *RefundRequest) GetTransactionAmount() float64 {
	if x != nil && x.TransactionAmount != nil {
		return *x.TransactionAmount
	}
	return 0
}

type RefundResponse struct {
	state           protoimpl.MessageState `protogen:"open.v1"`
	TransactionId   *uint64                `protobuf:"varint,1,opt,name=transaction_id,json=transactionId" json:"transaction_id,omitempty"`
	BankResponse    *BankResponse          `protobuf:"bytes,2,opt,name=bank_response,json=bankResponse" json:"bank_response,omitempty"`
	BankReferenceId *string                `protobuf:"bytes,3,opt,name=bank_reference_id,json=bankReferenceId" json:"bank_reference_id,omitempty"`
	unknownFields   protoimpl.UnknownFields
	sizeCache       protoimpl.SizeCache
}

func (x *RefundResponse) Reset() {
	*x = RefundResponse{}
	mi := &file_inner_processing_grpc_multiacquiring_proto_msgTypes[20]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RefundResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RefundResponse) ProtoMessage() {}

func (x *RefundResponse) ProtoReflect() protoreflect.Message {
	mi := &file_inner_processing_grpc_multiacquiring_proto_msgTypes[20]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RefundResponse.ProtoReflect.Descriptor instead.
func (*RefundResponse) Descriptor() ([]byte, []int) {
	return file_inner_processing_grpc_multiacquiring_proto_rawDescGZIP(), []int{20}
}

func (x *RefundResponse) GetTransactionId() uint64 {
	if x != nil && x.TransactionId != nil {
		return *x.TransactionId
	}
	return 0
}

func (x *RefundResponse) GetBankResponse() *BankResponse {
	if x != nil {
		return x.BankResponse
	}
	return nil
}

func (x *RefundResponse) GetBankReferenceId() string {
	if x != nil && x.BankReferenceId != nil {
		return *x.BankReferenceId
	}
	return ""
}

type CardInfo struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	IpsId         *uint64                `protobuf:"varint,1,opt,name=ips_id,json=ipsId" json:"ips_id,omitempty"`
	IssuerId      *uint64                `protobuf:"varint,2,opt,name=issuer_id,json=issuerId" json:"issuer_id,omitempty"`
	CountryId     *uint64                `protobuf:"varint,3,opt,name=country_id,json=countryId" json:"country_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CardInfo) Reset() {
	*x = CardInfo{}
	mi := &file_inner_processing_grpc_multiacquiring_proto_msgTypes[21]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CardInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CardInfo) ProtoMessage() {}

func (x *CardInfo) ProtoReflect() protoreflect.Message {
	mi := &file_inner_processing_grpc_multiacquiring_proto_msgTypes[21]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CardInfo.ProtoReflect.Descriptor instead.
func (*CardInfo) Descriptor() ([]byte, []int) {
	return file_inner_processing_grpc_multiacquiring_proto_rawDescGZIP(), []int{21}
}

func (x *CardInfo) GetIpsId() uint64 {
	if x != nil && x.IpsId != nil {
		return *x.IpsId
	}
	return 0
}

func (x *CardInfo) GetIssuerId() uint64 {
	if x != nil && x.IssuerId != nil {
		return *x.IssuerId
	}
	return 0
}

func (x *CardInfo) GetCountryId() uint64 {
	if x != nil && x.CountryId != nil {
		return *x.CountryId
	}
	return 0
}

type MerchantInfo struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	ProjectId     *uint64                `protobuf:"varint,1,opt,name=project_id,json=projectId" json:"project_id,omitempty"`
	MerchantId    *uint64                `protobuf:"varint,2,opt,name=merchant_id,json=merchantId" json:"merchant_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *MerchantInfo) Reset() {
	*x = MerchantInfo{}
	mi := &file_inner_processing_grpc_multiacquiring_proto_msgTypes[22]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *MerchantInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MerchantInfo) ProtoMessage() {}

func (x *MerchantInfo) ProtoReflect() protoreflect.Message {
	mi := &file_inner_processing_grpc_multiacquiring_proto_msgTypes[22]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MerchantInfo.ProtoReflect.Descriptor instead.
func (*MerchantInfo) Descriptor() ([]byte, []int) {
	return file_inner_processing_grpc_multiacquiring_proto_rawDescGZIP(), []int{22}
}

func (x *MerchantInfo) GetProjectId() uint64 {
	if x != nil && x.ProjectId != nil {
		return *x.ProjectId
	}
	return 0
}

func (x *MerchantInfo) GetMerchantId() uint64 {
	if x != nil && x.MerchantId != nil {
		return *x.MerchantId
	}
	return 0
}

type GooglePayRequestData struct {
	state                protoimpl.MessageState `protogen:"open.v1"`
	TransactionId        *uint64                `protobuf:"varint,1,opt,name=transaction_id,json=transactionId" json:"transaction_id,omitempty"`
	Amount               *float64               `protobuf:"fixed64,2,opt,name=amount" json:"amount,omitempty"`
	Description          *string                `protobuf:"bytes,3,opt,name=description" json:"description,omitempty"`
	Card                 *CardDataForBank       `protobuf:"bytes,4,opt,name=card" json:"card,omitempty"`
	User                 *UserDataForBank       `protobuf:"bytes,5,opt,name=user" json:"user,omitempty"`
	Terminal             *TerminalDataForBank   `protobuf:"bytes,6,opt,name=terminal" json:"terminal,omitempty"`
	CardInfo             *CardInfo              `protobuf:"bytes,7,opt,name=card_info,json=cardInfo" json:"card_info,omitempty"`
	MerchantInfo         *MerchantInfo          `protobuf:"bytes,8,opt,name=merchant_info,json=merchantInfo" json:"merchant_info,omitempty"`
	AggregatedTypeId     *uint64                `protobuf:"varint,9,opt,name=aggregated_type_id,json=aggregatedTypeId" json:"aggregated_type_id,omitempty"`
	Token                *string                `protobuf:"bytes,10,opt,name=token" json:"token,omitempty"`
	Currency             *string                `protobuf:"bytes,11,opt,name=currency" json:"currency,omitempty"`
	EciIndicator         *string                `protobuf:"bytes,12,opt,name=eci_indicator,json=eciIndicator" json:"eci_indicator,omitempty"`
	Cryptogram           *string                `protobuf:"bytes,13,opt,name=cryptogram" json:"cryptogram,omitempty"`
	TransactionCreatedAt *timestamppb.Timestamp `protobuf:"bytes,14,opt,name=transaction_created_at,json=transactionCreatedAt" json:"transaction_created_at,omitempty"`
	UserIpAddress        *string                `protobuf:"bytes,15,opt,name=user_ip_address,json=userIpAddress" json:"user_ip_address,omitempty"`
	unknownFields        protoimpl.UnknownFields
	sizeCache            protoimpl.SizeCache
}

func (x *GooglePayRequestData) Reset() {
	*x = GooglePayRequestData{}
	mi := &file_inner_processing_grpc_multiacquiring_proto_msgTypes[23]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GooglePayRequestData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GooglePayRequestData) ProtoMessage() {}

func (x *GooglePayRequestData) ProtoReflect() protoreflect.Message {
	mi := &file_inner_processing_grpc_multiacquiring_proto_msgTypes[23]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GooglePayRequestData.ProtoReflect.Descriptor instead.
func (*GooglePayRequestData) Descriptor() ([]byte, []int) {
	return file_inner_processing_grpc_multiacquiring_proto_rawDescGZIP(), []int{23}
}

func (x *GooglePayRequestData) GetTransactionId() uint64 {
	if x != nil && x.TransactionId != nil {
		return *x.TransactionId
	}
	return 0
}

func (x *GooglePayRequestData) GetAmount() float64 {
	if x != nil && x.Amount != nil {
		return *x.Amount
	}
	return 0
}

func (x *GooglePayRequestData) GetDescription() string {
	if x != nil && x.Description != nil {
		return *x.Description
	}
	return ""
}

func (x *GooglePayRequestData) GetCard() *CardDataForBank {
	if x != nil {
		return x.Card
	}
	return nil
}

func (x *GooglePayRequestData) GetUser() *UserDataForBank {
	if x != nil {
		return x.User
	}
	return nil
}

func (x *GooglePayRequestData) GetTerminal() *TerminalDataForBank {
	if x != nil {
		return x.Terminal
	}
	return nil
}

func (x *GooglePayRequestData) GetCardInfo() *CardInfo {
	if x != nil {
		return x.CardInfo
	}
	return nil
}

func (x *GooglePayRequestData) GetMerchantInfo() *MerchantInfo {
	if x != nil {
		return x.MerchantInfo
	}
	return nil
}

func (x *GooglePayRequestData) GetAggregatedTypeId() uint64 {
	if x != nil && x.AggregatedTypeId != nil {
		return *x.AggregatedTypeId
	}
	return 0
}

func (x *GooglePayRequestData) GetToken() string {
	if x != nil && x.Token != nil {
		return *x.Token
	}
	return ""
}

func (x *GooglePayRequestData) GetCurrency() string {
	if x != nil && x.Currency != nil {
		return *x.Currency
	}
	return ""
}

func (x *GooglePayRequestData) GetEciIndicator() string {
	if x != nil && x.EciIndicator != nil {
		return *x.EciIndicator
	}
	return ""
}

func (x *GooglePayRequestData) GetCryptogram() string {
	if x != nil && x.Cryptogram != nil {
		return *x.Cryptogram
	}
	return ""
}

func (x *GooglePayRequestData) GetTransactionCreatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.TransactionCreatedAt
	}
	return nil
}

func (x *GooglePayRequestData) GetUserIpAddress() string {
	if x != nil && x.UserIpAddress != nil {
		return *x.UserIpAddress
	}
	return ""
}

type GooglePayResponseData struct {
	state             protoimpl.MessageState `protogen:"open.v1"`
	TransactionId     *uint64                `protobuf:"varint,1,opt,name=transaction_id,json=transactionId" json:"transaction_id,omitempty"`
	BankReferenceId   *string                `protobuf:"bytes,2,opt,name=bank_reference_id,json=bankReferenceId" json:"bank_reference_id,omitempty"`
	TransactionStatus *EnumTransactionStatus `protobuf:"varint,3,opt,name=transaction_status,json=transactionStatus,enum=processing.transaction.transaction_status.EnumTransactionStatus" json:"transaction_status,omitempty"`
	ThreeDS           *ThreeDSDataFromBank   `protobuf:"bytes,4,opt,name=threeDS" json:"threeDS,omitempty"`
	BankResponse      *BankResponse          `protobuf:"bytes,5,opt,name=bank_response,json=bankResponse" json:"bank_response,omitempty"`
	BankOrderId       *string                `protobuf:"bytes,6,opt,name=bank_order_id,json=bankOrderId" json:"bank_order_id,omitempty"`
	Fingerprint       *Fingerprint           `protobuf:"bytes,7,opt,name=fingerprint" json:"fingerprint,omitempty"`
	unknownFields     protoimpl.UnknownFields
	sizeCache         protoimpl.SizeCache
}

func (x *GooglePayResponseData) Reset() {
	*x = GooglePayResponseData{}
	mi := &file_inner_processing_grpc_multiacquiring_proto_msgTypes[24]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GooglePayResponseData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GooglePayResponseData) ProtoMessage() {}

func (x *GooglePayResponseData) ProtoReflect() protoreflect.Message {
	mi := &file_inner_processing_grpc_multiacquiring_proto_msgTypes[24]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GooglePayResponseData.ProtoReflect.Descriptor instead.
func (*GooglePayResponseData) Descriptor() ([]byte, []int) {
	return file_inner_processing_grpc_multiacquiring_proto_rawDescGZIP(), []int{24}
}

func (x *GooglePayResponseData) GetTransactionId() uint64 {
	if x != nil && x.TransactionId != nil {
		return *x.TransactionId
	}
	return 0
}

func (x *GooglePayResponseData) GetBankReferenceId() string {
	if x != nil && x.BankReferenceId != nil {
		return *x.BankReferenceId
	}
	return ""
}

func (x *GooglePayResponseData) GetTransactionStatus() EnumTransactionStatus {
	if x != nil && x.TransactionStatus != nil {
		return *x.TransactionStatus
	}
	return EnumTransactionStatus_Unknown
}

func (x *GooglePayResponseData) GetThreeDS() *ThreeDSDataFromBank {
	if x != nil {
		return x.ThreeDS
	}
	return nil
}

func (x *GooglePayResponseData) GetBankResponse() *BankResponse {
	if x != nil {
		return x.BankResponse
	}
	return nil
}

func (x *GooglePayResponseData) GetBankOrderId() string {
	if x != nil && x.BankOrderId != nil {
		return *x.BankOrderId
	}
	return ""
}

func (x *GooglePayResponseData) GetFingerprint() *Fingerprint {
	if x != nil {
		return x.Fingerprint
	}
	return nil
}

type ApplePayRequestData struct {
	state                protoimpl.MessageState `protogen:"open.v1"`
	TransactionId        *uint64                `protobuf:"varint,1,opt,name=transaction_id,json=transactionId" json:"transaction_id,omitempty"`
	IsHold               *bool                  `protobuf:"varint,2,opt,name=is_hold,json=isHold" json:"is_hold,omitempty"`
	Amount               *float64               `protobuf:"fixed64,3,opt,name=amount" json:"amount,omitempty"`
	Description          *string                `protobuf:"bytes,4,opt,name=description" json:"description,omitempty"`
	Card                 *CardDataForBank       `protobuf:"bytes,5,opt,name=card" json:"card,omitempty"`
	User                 *UserDataForBank       `protobuf:"bytes,6,opt,name=user" json:"user,omitempty"`
	Terminal             *TerminalDataForBank   `protobuf:"bytes,7,opt,name=terminal" json:"terminal,omitempty"`
	TransactionTypeId    *uint64                `protobuf:"varint,8,opt,name=transaction_type_id,json=transactionTypeId" json:"transaction_type_id,omitempty"`
	CardInfo             *CardInfo              `protobuf:"bytes,9,opt,name=card_info,json=cardInfo" json:"card_info,omitempty"`
	MerchantInfo         *MerchantInfo          `protobuf:"bytes,10,opt,name=merchant_info,json=merchantInfo" json:"merchant_info,omitempty"`
	Tavv                 []byte                 `protobuf:"bytes,11,opt,name=Tavv" json:"Tavv,omitempty"`
	Eci                  *string                `protobuf:"bytes,12,opt,name=Eci" json:"Eci,omitempty"`
	TransactionCreatedAt *timestamppb.Timestamp `protobuf:"bytes,13,opt,name=transaction_created_at,json=transactionCreatedAt" json:"transaction_created_at,omitempty"`
	AggregatedTypeId     *uint64                `protobuf:"varint,14,opt,name=aggregated_type_id,json=aggregatedTypeId" json:"aggregated_type_id,omitempty"`
	UserIpAddress        *string                `protobuf:"bytes,15,opt,name=user_ip_address,json=userIpAddress" json:"user_ip_address,omitempty"`
	unknownFields        protoimpl.UnknownFields
	sizeCache            protoimpl.SizeCache
}

func (x *ApplePayRequestData) Reset() {
	*x = ApplePayRequestData{}
	mi := &file_inner_processing_grpc_multiacquiring_proto_msgTypes[25]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ApplePayRequestData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ApplePayRequestData) ProtoMessage() {}

func (x *ApplePayRequestData) ProtoReflect() protoreflect.Message {
	mi := &file_inner_processing_grpc_multiacquiring_proto_msgTypes[25]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ApplePayRequestData.ProtoReflect.Descriptor instead.
func (*ApplePayRequestData) Descriptor() ([]byte, []int) {
	return file_inner_processing_grpc_multiacquiring_proto_rawDescGZIP(), []int{25}
}

func (x *ApplePayRequestData) GetTransactionId() uint64 {
	if x != nil && x.TransactionId != nil {
		return *x.TransactionId
	}
	return 0
}

func (x *ApplePayRequestData) GetIsHold() bool {
	if x != nil && x.IsHold != nil {
		return *x.IsHold
	}
	return false
}

func (x *ApplePayRequestData) GetAmount() float64 {
	if x != nil && x.Amount != nil {
		return *x.Amount
	}
	return 0
}

func (x *ApplePayRequestData) GetDescription() string {
	if x != nil && x.Description != nil {
		return *x.Description
	}
	return ""
}

func (x *ApplePayRequestData) GetCard() *CardDataForBank {
	if x != nil {
		return x.Card
	}
	return nil
}

func (x *ApplePayRequestData) GetUser() *UserDataForBank {
	if x != nil {
		return x.User
	}
	return nil
}

func (x *ApplePayRequestData) GetTerminal() *TerminalDataForBank {
	if x != nil {
		return x.Terminal
	}
	return nil
}

func (x *ApplePayRequestData) GetTransactionTypeId() uint64 {
	if x != nil && x.TransactionTypeId != nil {
		return *x.TransactionTypeId
	}
	return 0
}

func (x *ApplePayRequestData) GetCardInfo() *CardInfo {
	if x != nil {
		return x.CardInfo
	}
	return nil
}

func (x *ApplePayRequestData) GetMerchantInfo() *MerchantInfo {
	if x != nil {
		return x.MerchantInfo
	}
	return nil
}

func (x *ApplePayRequestData) GetTavv() []byte {
	if x != nil {
		return x.Tavv
	}
	return nil
}

func (x *ApplePayRequestData) GetEci() string {
	if x != nil && x.Eci != nil {
		return *x.Eci
	}
	return ""
}

func (x *ApplePayRequestData) GetTransactionCreatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.TransactionCreatedAt
	}
	return nil
}

func (x *ApplePayRequestData) GetAggregatedTypeId() uint64 {
	if x != nil && x.AggregatedTypeId != nil {
		return *x.AggregatedTypeId
	}
	return 0
}

func (x *ApplePayRequestData) GetUserIpAddress() string {
	if x != nil && x.UserIpAddress != nil {
		return *x.UserIpAddress
	}
	return ""
}

type ApplePayResponseData struct {
	state             protoimpl.MessageState `protogen:"open.v1"`
	TransactionId     *uint64                `protobuf:"varint,1,opt,name=transaction_id,json=transactionId" json:"transaction_id,omitempty"`
	BankReferenceId   *string                `protobuf:"bytes,2,opt,name=bank_reference_id,json=bankReferenceId" json:"bank_reference_id,omitempty"`
	TransactionStatus *EnumTransactionStatus `protobuf:"varint,3,opt,name=transaction_status,json=transactionStatus,enum=processing.transaction.transaction_status.EnumTransactionStatus" json:"transaction_status,omitempty"`
	CardId            *string                `protobuf:"bytes,4,opt,name=card_id,json=cardId" json:"card_id,omitempty"`
	ThreeDS           *ThreeDSDataFromBank   `protobuf:"bytes,5,opt,name=threeDS" json:"threeDS,omitempty"`
	BankResponse      *BankResponse          `protobuf:"bytes,6,opt,name=bank_response,json=bankResponse" json:"bank_response,omitempty"`
	BankOrderId       *string                `protobuf:"bytes,7,opt,name=bank_order_id,json=bankOrderId" json:"bank_order_id,omitempty"`
	unknownFields     protoimpl.UnknownFields
	sizeCache         protoimpl.SizeCache
}

func (x *ApplePayResponseData) Reset() {
	*x = ApplePayResponseData{}
	mi := &file_inner_processing_grpc_multiacquiring_proto_msgTypes[26]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ApplePayResponseData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ApplePayResponseData) ProtoMessage() {}

func (x *ApplePayResponseData) ProtoReflect() protoreflect.Message {
	mi := &file_inner_processing_grpc_multiacquiring_proto_msgTypes[26]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ApplePayResponseData.ProtoReflect.Descriptor instead.
func (*ApplePayResponseData) Descriptor() ([]byte, []int) {
	return file_inner_processing_grpc_multiacquiring_proto_rawDescGZIP(), []int{26}
}

func (x *ApplePayResponseData) GetTransactionId() uint64 {
	if x != nil && x.TransactionId != nil {
		return *x.TransactionId
	}
	return 0
}

func (x *ApplePayResponseData) GetBankReferenceId() string {
	if x != nil && x.BankReferenceId != nil {
		return *x.BankReferenceId
	}
	return ""
}

func (x *ApplePayResponseData) GetTransactionStatus() EnumTransactionStatus {
	if x != nil && x.TransactionStatus != nil {
		return *x.TransactionStatus
	}
	return EnumTransactionStatus_Unknown
}

func (x *ApplePayResponseData) GetCardId() string {
	if x != nil && x.CardId != nil {
		return *x.CardId
	}
	return ""
}

func (x *ApplePayResponseData) GetThreeDS() *ThreeDSDataFromBank {
	if x != nil {
		return x.ThreeDS
	}
	return nil
}

func (x *ApplePayResponseData) GetBankResponse() *BankResponse {
	if x != nil {
		return x.BankResponse
	}
	return nil
}

func (x *ApplePayResponseData) GetBankOrderId() string {
	if x != nil && x.BankOrderId != nil {
		return *x.BankOrderId
	}
	return ""
}

type TwoStagePayInRequest struct {
	state             protoimpl.MessageState `protogen:"open.v1"`
	TransactionId     *uint64                `protobuf:"varint,1,opt,name=transaction_id,json=transactionId" json:"transaction_id,omitempty"`
	IsHold            *bool                  `protobuf:"varint,2,opt,name=is_hold,json=isHold" json:"is_hold,omitempty"`
	Amount            *float64               `protobuf:"fixed64,3,opt,name=amount" json:"amount,omitempty"`
	Description       *string                `protobuf:"bytes,4,opt,name=description" json:"description,omitempty"`
	Card              *CardDataForBank       `protobuf:"bytes,5,opt,name=card" json:"card,omitempty"`
	User              *UserDataForBank       `protobuf:"bytes,6,opt,name=user" json:"user,omitempty"`
	Terminal          *TerminalDataForBank   `protobuf:"bytes,7,opt,name=terminal" json:"terminal,omitempty"`
	TransactionTypeId *uint64                `protobuf:"varint,8,opt,name=transaction_type_id,json=transactionTypeId" json:"transaction_type_id,omitempty"`
	CardInfo          *CardInfo              `protobuf:"bytes,9,opt,name=card_info,json=cardInfo" json:"card_info,omitempty"`
	MerchantInfo      *MerchantInfo          `protobuf:"bytes,10,opt,name=merchant_info,json=merchantInfo" json:"merchant_info,omitempty"`
	AggregatedTypeId  *uint64                `protobuf:"varint,11,opt,name=aggregated_type_id,json=aggregatedTypeId" json:"aggregated_type_id,omitempty"`
	unknownFields     protoimpl.UnknownFields
	sizeCache         protoimpl.SizeCache
}

func (x *TwoStagePayInRequest) Reset() {
	*x = TwoStagePayInRequest{}
	mi := &file_inner_processing_grpc_multiacquiring_proto_msgTypes[27]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *TwoStagePayInRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TwoStagePayInRequest) ProtoMessage() {}

func (x *TwoStagePayInRequest) ProtoReflect() protoreflect.Message {
	mi := &file_inner_processing_grpc_multiacquiring_proto_msgTypes[27]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TwoStagePayInRequest.ProtoReflect.Descriptor instead.
func (*TwoStagePayInRequest) Descriptor() ([]byte, []int) {
	return file_inner_processing_grpc_multiacquiring_proto_rawDescGZIP(), []int{27}
}

func (x *TwoStagePayInRequest) GetTransactionId() uint64 {
	if x != nil && x.TransactionId != nil {
		return *x.TransactionId
	}
	return 0
}

func (x *TwoStagePayInRequest) GetIsHold() bool {
	if x != nil && x.IsHold != nil {
		return *x.IsHold
	}
	return false
}

func (x *TwoStagePayInRequest) GetAmount() float64 {
	if x != nil && x.Amount != nil {
		return *x.Amount
	}
	return 0
}

func (x *TwoStagePayInRequest) GetDescription() string {
	if x != nil && x.Description != nil {
		return *x.Description
	}
	return ""
}

func (x *TwoStagePayInRequest) GetCard() *CardDataForBank {
	if x != nil {
		return x.Card
	}
	return nil
}

func (x *TwoStagePayInRequest) GetUser() *UserDataForBank {
	if x != nil {
		return x.User
	}
	return nil
}

func (x *TwoStagePayInRequest) GetTerminal() *TerminalDataForBank {
	if x != nil {
		return x.Terminal
	}
	return nil
}

func (x *TwoStagePayInRequest) GetTransactionTypeId() uint64 {
	if x != nil && x.TransactionTypeId != nil {
		return *x.TransactionTypeId
	}
	return 0
}

func (x *TwoStagePayInRequest) GetCardInfo() *CardInfo {
	if x != nil {
		return x.CardInfo
	}
	return nil
}

func (x *TwoStagePayInRequest) GetMerchantInfo() *MerchantInfo {
	if x != nil {
		return x.MerchantInfo
	}
	return nil
}

func (x *TwoStagePayInRequest) GetAggregatedTypeId() uint64 {
	if x != nil && x.AggregatedTypeId != nil {
		return *x.AggregatedTypeId
	}
	return 0
}

type ChargeRequest struct {
	state                protoimpl.MessageState `protogen:"open.v1"`
	TransactionId        *uint64                `protobuf:"varint,1,opt,name=transaction_id,json=transactionId" json:"transaction_id,omitempty"`
	Amount               *float64               `protobuf:"fixed64,2,opt,name=amount" json:"amount,omitempty"`
	Terminal             *TerminalDataForBank   `protobuf:"bytes,3,opt,name=terminal" json:"terminal,omitempty"`
	BankReferenceId      *string                `protobuf:"bytes,4,opt,name=bank_reference_id,json=bankReferenceId" json:"bank_reference_id,omitempty"`
	BankOrderId          *string                `protobuf:"bytes,5,opt,name=bank_order_id,json=bankOrderId" json:"bank_order_id,omitempty"`
	TransactionCreatedAt *timestamppb.Timestamp `protobuf:"bytes,6,opt,name=transaction_created_at,json=transactionCreatedAt" json:"transaction_created_at,omitempty"`
	unknownFields        protoimpl.UnknownFields
	sizeCache            protoimpl.SizeCache
}

func (x *ChargeRequest) Reset() {
	*x = ChargeRequest{}
	mi := &file_inner_processing_grpc_multiacquiring_proto_msgTypes[28]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ChargeRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ChargeRequest) ProtoMessage() {}

func (x *ChargeRequest) ProtoReflect() protoreflect.Message {
	mi := &file_inner_processing_grpc_multiacquiring_proto_msgTypes[28]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ChargeRequest.ProtoReflect.Descriptor instead.
func (*ChargeRequest) Descriptor() ([]byte, []int) {
	return file_inner_processing_grpc_multiacquiring_proto_rawDescGZIP(), []int{28}
}

func (x *ChargeRequest) GetTransactionId() uint64 {
	if x != nil && x.TransactionId != nil {
		return *x.TransactionId
	}
	return 0
}

func (x *ChargeRequest) GetAmount() float64 {
	if x != nil && x.Amount != nil {
		return *x.Amount
	}
	return 0
}

func (x *ChargeRequest) GetTerminal() *TerminalDataForBank {
	if x != nil {
		return x.Terminal
	}
	return nil
}

func (x *ChargeRequest) GetBankReferenceId() string {
	if x != nil && x.BankReferenceId != nil {
		return *x.BankReferenceId
	}
	return ""
}

func (x *ChargeRequest) GetBankOrderId() string {
	if x != nil && x.BankOrderId != nil {
		return *x.BankOrderId
	}
	return ""
}

func (x *ChargeRequest) GetTransactionCreatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.TransactionCreatedAt
	}
	return nil
}

type CancelRequest struct {
	state                protoimpl.MessageState `protogen:"open.v1"`
	TransactionId        *uint64                `protobuf:"varint,1,opt,name=transaction_id,json=transactionId" json:"transaction_id,omitempty"`
	Amount               *float64               `protobuf:"fixed64,2,opt,name=amount" json:"amount,omitempty"`
	Terminal             *TerminalDataForBank   `protobuf:"bytes,3,opt,name=terminal" json:"terminal,omitempty"`
	BankReferenceId      *string                `protobuf:"bytes,4,opt,name=bank_reference_id,json=bankReferenceId" json:"bank_reference_id,omitempty"`
	BankOrderId          *string                `protobuf:"bytes,5,opt,name=bank_order_id,json=bankOrderId" json:"bank_order_id,omitempty"`
	Description          *string                `protobuf:"bytes,6,opt,name=description" json:"description,omitempty"`
	TransactionCreatedAt *timestamppb.Timestamp `protobuf:"bytes,7,opt,name=transaction_created_at,json=transactionCreatedAt" json:"transaction_created_at,omitempty"`
	unknownFields        protoimpl.UnknownFields
	sizeCache            protoimpl.SizeCache
}

func (x *CancelRequest) Reset() {
	*x = CancelRequest{}
	mi := &file_inner_processing_grpc_multiacquiring_proto_msgTypes[29]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CancelRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CancelRequest) ProtoMessage() {}

func (x *CancelRequest) ProtoReflect() protoreflect.Message {
	mi := &file_inner_processing_grpc_multiacquiring_proto_msgTypes[29]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CancelRequest.ProtoReflect.Descriptor instead.
func (*CancelRequest) Descriptor() ([]byte, []int) {
	return file_inner_processing_grpc_multiacquiring_proto_rawDescGZIP(), []int{29}
}

func (x *CancelRequest) GetTransactionId() uint64 {
	if x != nil && x.TransactionId != nil {
		return *x.TransactionId
	}
	return 0
}

func (x *CancelRequest) GetAmount() float64 {
	if x != nil && x.Amount != nil {
		return *x.Amount
	}
	return 0
}

func (x *CancelRequest) GetTerminal() *TerminalDataForBank {
	if x != nil {
		return x.Terminal
	}
	return nil
}

func (x *CancelRequest) GetBankReferenceId() string {
	if x != nil && x.BankReferenceId != nil {
		return *x.BankReferenceId
	}
	return ""
}

func (x *CancelRequest) GetBankOrderId() string {
	if x != nil && x.BankOrderId != nil {
		return *x.BankOrderId
	}
	return ""
}

func (x *CancelRequest) GetDescription() string {
	if x != nil && x.Description != nil {
		return *x.Description
	}
	return ""
}

func (x *CancelRequest) GetTransactionCreatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.TransactionCreatedAt
	}
	return nil
}

type TwoStagePayInResponse struct {
	state             protoimpl.MessageState `protogen:"open.v1"`
	TransactionId     *uint64                `protobuf:"varint,1,opt,name=transaction_id,json=transactionId" json:"transaction_id,omitempty"`
	BankReferenceId   *string                `protobuf:"bytes,2,opt,name=bank_reference_id,json=bankReferenceId" json:"bank_reference_id,omitempty"`
	TransactionStatus *EnumTransactionStatus `protobuf:"varint,3,opt,name=transaction_status,json=transactionStatus,enum=processing.transaction.transaction_status.EnumTransactionStatus" json:"transaction_status,omitempty"`
	CardId            *string                `protobuf:"bytes,4,opt,name=card_id,json=cardId" json:"card_id,omitempty"`
	ThreeDS           *ThreeDSDataFromBank   `protobuf:"bytes,5,opt,name=threeDS" json:"threeDS,omitempty"`
	BankResponse      *BankResponse          `protobuf:"bytes,6,opt,name=bank_response,json=bankResponse" json:"bank_response,omitempty"`
	BankOrderId       *string                `protobuf:"bytes,7,opt,name=bank_order_id,json=bankOrderId" json:"bank_order_id,omitempty"`
	unknownFields     protoimpl.UnknownFields
	sizeCache         protoimpl.SizeCache
}

func (x *TwoStagePayInResponse) Reset() {
	*x = TwoStagePayInResponse{}
	mi := &file_inner_processing_grpc_multiacquiring_proto_msgTypes[30]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *TwoStagePayInResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TwoStagePayInResponse) ProtoMessage() {}

func (x *TwoStagePayInResponse) ProtoReflect() protoreflect.Message {
	mi := &file_inner_processing_grpc_multiacquiring_proto_msgTypes[30]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TwoStagePayInResponse.ProtoReflect.Descriptor instead.
func (*TwoStagePayInResponse) Descriptor() ([]byte, []int) {
	return file_inner_processing_grpc_multiacquiring_proto_rawDescGZIP(), []int{30}
}

func (x *TwoStagePayInResponse) GetTransactionId() uint64 {
	if x != nil && x.TransactionId != nil {
		return *x.TransactionId
	}
	return 0
}

func (x *TwoStagePayInResponse) GetBankReferenceId() string {
	if x != nil && x.BankReferenceId != nil {
		return *x.BankReferenceId
	}
	return ""
}

func (x *TwoStagePayInResponse) GetTransactionStatus() EnumTransactionStatus {
	if x != nil && x.TransactionStatus != nil {
		return *x.TransactionStatus
	}
	return EnumTransactionStatus_Unknown
}

func (x *TwoStagePayInResponse) GetCardId() string {
	if x != nil && x.CardId != nil {
		return *x.CardId
	}
	return ""
}

func (x *TwoStagePayInResponse) GetThreeDS() *ThreeDSDataFromBank {
	if x != nil {
		return x.ThreeDS
	}
	return nil
}

func (x *TwoStagePayInResponse) GetBankResponse() *BankResponse {
	if x != nil {
		return x.BankResponse
	}
	return nil
}

func (x *TwoStagePayInResponse) GetBankOrderId() string {
	if x != nil && x.BankOrderId != nil {
		return *x.BankOrderId
	}
	return ""
}

type ChargeResponse struct {
	state           protoimpl.MessageState `protogen:"open.v1"`
	TransactionId   *uint64                `protobuf:"varint,1,opt,name=transaction_id,json=transactionId" json:"transaction_id,omitempty"`
	BankReferenceId *string                `protobuf:"bytes,2,opt,name=bank_reference_id,json=bankReferenceId" json:"bank_reference_id,omitempty"`
	BankOrderId     *string                `protobuf:"bytes,3,opt,name=bank_order_id,json=bankOrderId" json:"bank_order_id,omitempty"`
	BankResponse    *BankResponse          `protobuf:"bytes,4,opt,name=bank_response,json=bankResponse" json:"bank_response,omitempty"`
	unknownFields   protoimpl.UnknownFields
	sizeCache       protoimpl.SizeCache
}

func (x *ChargeResponse) Reset() {
	*x = ChargeResponse{}
	mi := &file_inner_processing_grpc_multiacquiring_proto_msgTypes[31]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ChargeResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ChargeResponse) ProtoMessage() {}

func (x *ChargeResponse) ProtoReflect() protoreflect.Message {
	mi := &file_inner_processing_grpc_multiacquiring_proto_msgTypes[31]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ChargeResponse.ProtoReflect.Descriptor instead.
func (*ChargeResponse) Descriptor() ([]byte, []int) {
	return file_inner_processing_grpc_multiacquiring_proto_rawDescGZIP(), []int{31}
}

func (x *ChargeResponse) GetTransactionId() uint64 {
	if x != nil && x.TransactionId != nil {
		return *x.TransactionId
	}
	return 0
}

func (x *ChargeResponse) GetBankReferenceId() string {
	if x != nil && x.BankReferenceId != nil {
		return *x.BankReferenceId
	}
	return ""
}

func (x *ChargeResponse) GetBankOrderId() string {
	if x != nil && x.BankOrderId != nil {
		return *x.BankOrderId
	}
	return ""
}

func (x *ChargeResponse) GetBankResponse() *BankResponse {
	if x != nil {
		return x.BankResponse
	}
	return nil
}

type CancelResponse struct {
	state           protoimpl.MessageState `protogen:"open.v1"`
	TransactionId   *uint64                `protobuf:"varint,1,opt,name=transaction_id,json=transactionId" json:"transaction_id,omitempty"`
	BankReferenceId *string                `protobuf:"bytes,2,opt,name=bank_reference_id,json=bankReferenceId" json:"bank_reference_id,omitempty"`
	BankOrderId     *string                `protobuf:"bytes,3,opt,name=bank_order_id,json=bankOrderId" json:"bank_order_id,omitempty"`
	BankResponse    *BankResponse          `protobuf:"bytes,4,opt,name=bank_response,json=bankResponse" json:"bank_response,omitempty"`
	unknownFields   protoimpl.UnknownFields
	sizeCache       protoimpl.SizeCache
}

func (x *CancelResponse) Reset() {
	*x = CancelResponse{}
	mi := &file_inner_processing_grpc_multiacquiring_proto_msgTypes[32]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CancelResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CancelResponse) ProtoMessage() {}

func (x *CancelResponse) ProtoReflect() protoreflect.Message {
	mi := &file_inner_processing_grpc_multiacquiring_proto_msgTypes[32]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CancelResponse.ProtoReflect.Descriptor instead.
func (*CancelResponse) Descriptor() ([]byte, []int) {
	return file_inner_processing_grpc_multiacquiring_proto_rawDescGZIP(), []int{32}
}

func (x *CancelResponse) GetTransactionId() uint64 {
	if x != nil && x.TransactionId != nil {
		return *x.TransactionId
	}
	return 0
}

func (x *CancelResponse) GetBankReferenceId() string {
	if x != nil && x.BankReferenceId != nil {
		return *x.BankReferenceId
	}
	return ""
}

func (x *CancelResponse) GetBankOrderId() string {
	if x != nil && x.BankOrderId != nil {
		return *x.BankOrderId
	}
	return ""
}

func (x *CancelResponse) GetBankResponse() *BankResponse {
	if x != nil {
		return x.BankResponse
	}
	return nil
}

type Fingerprint struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	MethodUrl     *string                `protobuf:"bytes,1,opt,name=method_url,json=methodUrl" json:"method_url,omitempty"`
	MethodData    *string                `protobuf:"bytes,2,opt,name=method_data,json=methodData" json:"method_data,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Fingerprint) Reset() {
	*x = Fingerprint{}
	mi := &file_inner_processing_grpc_multiacquiring_proto_msgTypes[33]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Fingerprint) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Fingerprint) ProtoMessage() {}

func (x *Fingerprint) ProtoReflect() protoreflect.Message {
	mi := &file_inner_processing_grpc_multiacquiring_proto_msgTypes[33]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Fingerprint.ProtoReflect.Descriptor instead.
func (*Fingerprint) Descriptor() ([]byte, []int) {
	return file_inner_processing_grpc_multiacquiring_proto_rawDescGZIP(), []int{33}
}

func (x *Fingerprint) GetMethodUrl() string {
	if x != nil && x.MethodUrl != nil {
		return *x.MethodUrl
	}
	return ""
}

func (x *Fingerprint) GetMethodData() string {
	if x != nil && x.MethodData != nil {
		return *x.MethodData
	}
	return ""
}

type ThreeDSResumeRequest struct {
	state           protoimpl.MessageState `protogen:"open.v1"`
	TransactionId   *uint64                `protobuf:"varint,1,opt,name=transaction_id,json=transactionId" json:"transaction_id,omitempty"`
	BankReferenceId *string                `protobuf:"bytes,2,opt,name=bank_reference_id,json=bankReferenceId" json:"bank_reference_id,omitempty"`
	Terminal        *TerminalDataForBank   `protobuf:"bytes,3,opt,name=terminal" json:"terminal,omitempty"`
	BankOrderId     *string                `protobuf:"bytes,4,opt,name=bank_order_id,json=bankOrderId" json:"bank_order_id,omitempty"`
	IsHold          *bool                  `protobuf:"varint,5,opt,name=is_hold,json=isHold" json:"is_hold,omitempty"`
	unknownFields   protoimpl.UnknownFields
	sizeCache       protoimpl.SizeCache
}

func (x *ThreeDSResumeRequest) Reset() {
	*x = ThreeDSResumeRequest{}
	mi := &file_inner_processing_grpc_multiacquiring_proto_msgTypes[34]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ThreeDSResumeRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ThreeDSResumeRequest) ProtoMessage() {}

func (x *ThreeDSResumeRequest) ProtoReflect() protoreflect.Message {
	mi := &file_inner_processing_grpc_multiacquiring_proto_msgTypes[34]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ThreeDSResumeRequest.ProtoReflect.Descriptor instead.
func (*ThreeDSResumeRequest) Descriptor() ([]byte, []int) {
	return file_inner_processing_grpc_multiacquiring_proto_rawDescGZIP(), []int{34}
}

func (x *ThreeDSResumeRequest) GetTransactionId() uint64 {
	if x != nil && x.TransactionId != nil {
		return *x.TransactionId
	}
	return 0
}

func (x *ThreeDSResumeRequest) GetBankReferenceId() string {
	if x != nil && x.BankReferenceId != nil {
		return *x.BankReferenceId
	}
	return ""
}

func (x *ThreeDSResumeRequest) GetTerminal() *TerminalDataForBank {
	if x != nil {
		return x.Terminal
	}
	return nil
}

func (x *ThreeDSResumeRequest) GetBankOrderId() string {
	if x != nil && x.BankOrderId != nil {
		return *x.BankOrderId
	}
	return ""
}

func (x *ThreeDSResumeRequest) GetIsHold() bool {
	if x != nil && x.IsHold != nil {
		return *x.IsHold
	}
	return false
}

type ThreeDSResumeResponse struct {
	state             protoimpl.MessageState `protogen:"open.v1"`
	TransactionId     *uint64                `protobuf:"varint,1,opt,name=transaction_id,json=transactionId" json:"transaction_id,omitempty"`
	BankOrderId       *string                `protobuf:"bytes,2,opt,name=bank_order_id,json=bankOrderId" json:"bank_order_id,omitempty"`
	BankReferenceId   *string                `protobuf:"bytes,3,opt,name=bank_reference_id,json=bankReferenceId" json:"bank_reference_id,omitempty"`
	TransactionStatus *EnumTransactionStatus `protobuf:"varint,4,opt,name=transaction_status,json=transactionStatus,enum=processing.transaction.transaction_status.EnumTransactionStatus" json:"transaction_status,omitempty"`
	CardToken         *string                `protobuf:"bytes,5,opt,name=card_token,json=cardToken" json:"card_token,omitempty"`
	ThreeDS           *ThreeDSDataFromBank   `protobuf:"bytes,6,opt,name=threeDS" json:"threeDS,omitempty"`
	BankResponse      *BankResponse          `protobuf:"bytes,7,opt,name=bank_response,json=bankResponse" json:"bank_response,omitempty"`
	unknownFields     protoimpl.UnknownFields
	sizeCache         protoimpl.SizeCache
}

func (x *ThreeDSResumeResponse) Reset() {
	*x = ThreeDSResumeResponse{}
	mi := &file_inner_processing_grpc_multiacquiring_proto_msgTypes[35]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ThreeDSResumeResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ThreeDSResumeResponse) ProtoMessage() {}

func (x *ThreeDSResumeResponse) ProtoReflect() protoreflect.Message {
	mi := &file_inner_processing_grpc_multiacquiring_proto_msgTypes[35]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ThreeDSResumeResponse.ProtoReflect.Descriptor instead.
func (*ThreeDSResumeResponse) Descriptor() ([]byte, []int) {
	return file_inner_processing_grpc_multiacquiring_proto_rawDescGZIP(), []int{35}
}

func (x *ThreeDSResumeResponse) GetTransactionId() uint64 {
	if x != nil && x.TransactionId != nil {
		return *x.TransactionId
	}
	return 0
}

func (x *ThreeDSResumeResponse) GetBankOrderId() string {
	if x != nil && x.BankOrderId != nil {
		return *x.BankOrderId
	}
	return ""
}

func (x *ThreeDSResumeResponse) GetBankReferenceId() string {
	if x != nil && x.BankReferenceId != nil {
		return *x.BankReferenceId
	}
	return ""
}

func (x *ThreeDSResumeResponse) GetTransactionStatus() EnumTransactionStatus {
	if x != nil && x.TransactionStatus != nil {
		return *x.TransactionStatus
	}
	return EnumTransactionStatus_Unknown
}

func (x *ThreeDSResumeResponse) GetCardToken() string {
	if x != nil && x.CardToken != nil {
		return *x.CardToken
	}
	return ""
}

func (x *ThreeDSResumeResponse) GetThreeDS() *ThreeDSDataFromBank {
	if x != nil {
		return x.ThreeDS
	}
	return nil
}

func (x *ThreeDSResumeResponse) GetBankResponse() *BankResponse {
	if x != nil {
		return x.BankResponse
	}
	return nil
}

type BankResponse struct {
	state              protoimpl.MessageState `protogen:"open.v1"`
	Message            *string                `protobuf:"bytes,1,opt,name=message" json:"message,omitempty"`
	Code               *string                `protobuf:"bytes,2,opt,name=code" json:"code,omitempty"`
	IntegrationCode    *string                `protobuf:"bytes,3,opt,name=integration_code,json=integrationCode" json:"integration_code,omitempty"`
	IntegrationMessage *string                `protobuf:"bytes,4,opt,name=integration_message,json=integrationMessage" json:"integration_message,omitempty"`
	unknownFields      protoimpl.UnknownFields
	sizeCache          protoimpl.SizeCache
}

func (x *BankResponse) Reset() {
	*x = BankResponse{}
	mi := &file_inner_processing_grpc_multiacquiring_proto_msgTypes[36]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BankResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BankResponse) ProtoMessage() {}

func (x *BankResponse) ProtoReflect() protoreflect.Message {
	mi := &file_inner_processing_grpc_multiacquiring_proto_msgTypes[36]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BankResponse.ProtoReflect.Descriptor instead.
func (*BankResponse) Descriptor() ([]byte, []int) {
	return file_inner_processing_grpc_multiacquiring_proto_rawDescGZIP(), []int{36}
}

func (x *BankResponse) GetMessage() string {
	if x != nil && x.Message != nil {
		return *x.Message
	}
	return ""
}

func (x *BankResponse) GetCode() string {
	if x != nil && x.Code != nil {
		return *x.Code
	}
	return ""
}

func (x *BankResponse) GetIntegrationCode() string {
	if x != nil && x.IntegrationCode != nil {
		return *x.IntegrationCode
	}
	return ""
}

func (x *BankResponse) GetIntegrationMessage() string {
	if x != nil && x.IntegrationMessage != nil {
		return *x.IntegrationMessage
	}
	return ""
}

type ResolveVisaAliasRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	PhoneNumber   *string                `protobuf:"bytes,1,opt,name=phone_number,json=phoneNumber" json:"phone_number,omitempty"`
	Terminal      *TerminalDataForBank   `protobuf:"bytes,3,opt,name=terminal" json:"terminal,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ResolveVisaAliasRequest) Reset() {
	*x = ResolveVisaAliasRequest{}
	mi := &file_inner_processing_grpc_multiacquiring_proto_msgTypes[37]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ResolveVisaAliasRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ResolveVisaAliasRequest) ProtoMessage() {}

func (x *ResolveVisaAliasRequest) ProtoReflect() protoreflect.Message {
	mi := &file_inner_processing_grpc_multiacquiring_proto_msgTypes[37]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ResolveVisaAliasRequest.ProtoReflect.Descriptor instead.
func (*ResolveVisaAliasRequest) Descriptor() ([]byte, []int) {
	return file_inner_processing_grpc_multiacquiring_proto_rawDescGZIP(), []int{37}
}

func (x *ResolveVisaAliasRequest) GetPhoneNumber() string {
	if x != nil && x.PhoneNumber != nil {
		return *x.PhoneNumber
	}
	return ""
}

func (x *ResolveVisaAliasRequest) GetTerminal() *TerminalDataForBank {
	if x != nil {
		return x.Terminal
	}
	return nil
}

type ResolveVisaAliasResponse struct {
	state          protoimpl.MessageState `protogen:"open.v1"`
	IssuerName     *string                `protobuf:"bytes,1,opt,name=issuer_name,json=issuerName" json:"issuer_name,omitempty"`
	RecipientName  *string                `protobuf:"bytes,2,opt,name=recipient_name,json=recipientName" json:"recipient_name,omitempty"`
	CardType       *string                `protobuf:"bytes,3,opt,name=card_type,json=cardType" json:"card_type,omitempty"`
	BankResponse   *BankResponse          `protobuf:"bytes,4,opt,name=bank_response,json=bankResponse" json:"bank_response,omitempty"`
	VisaAliasToken *string                `protobuf:"bytes,5,opt,name=visa_alias_token,json=visaAliasToken" json:"visa_alias_token,omitempty"`
	unknownFields  protoimpl.UnknownFields
	sizeCache      protoimpl.SizeCache
}

func (x *ResolveVisaAliasResponse) Reset() {
	*x = ResolveVisaAliasResponse{}
	mi := &file_inner_processing_grpc_multiacquiring_proto_msgTypes[38]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ResolveVisaAliasResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ResolveVisaAliasResponse) ProtoMessage() {}

func (x *ResolveVisaAliasResponse) ProtoReflect() protoreflect.Message {
	mi := &file_inner_processing_grpc_multiacquiring_proto_msgTypes[38]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ResolveVisaAliasResponse.ProtoReflect.Descriptor instead.
func (*ResolveVisaAliasResponse) Descriptor() ([]byte, []int) {
	return file_inner_processing_grpc_multiacquiring_proto_rawDescGZIP(), []int{38}
}

func (x *ResolveVisaAliasResponse) GetIssuerName() string {
	if x != nil && x.IssuerName != nil {
		return *x.IssuerName
	}
	return ""
}

func (x *ResolveVisaAliasResponse) GetRecipientName() string {
	if x != nil && x.RecipientName != nil {
		return *x.RecipientName
	}
	return ""
}

func (x *ResolveVisaAliasResponse) GetCardType() string {
	if x != nil && x.CardType != nil {
		return *x.CardType
	}
	return ""
}

func (x *ResolveVisaAliasResponse) GetBankResponse() *BankResponse {
	if x != nil {
		return x.BankResponse
	}
	return nil
}

func (x *ResolveVisaAliasResponse) GetVisaAliasToken() string {
	if x != nil && x.VisaAliasToken != nil {
		return *x.VisaAliasToken
	}
	return ""
}

var File_inner_processing_grpc_multiacquiring_proto protoreflect.FileDescriptor

var file_inner_processing_grpc_multiacquiring_proto_rawDesc = string([]byte{
	0x0a, 0x2a, 0x69, 0x6e, 0x6e, 0x65, 0x72, 0x2f, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69,
	0x6e, 0x67, 0x2f, 0x67, 0x72, 0x70, 0x63, 0x2f, 0x6d, 0x75, 0x6c, 0x74, 0x69, 0x61, 0x63, 0x71,
	0x75, 0x69, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x28, 0x70, 0x72,
	0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x2e, 0x6d, 0x75, 0x6c, 0x74, 0x69, 0x61, 0x63,
	0x71, 0x75, 0x69, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x6d, 0x75, 0x6c, 0x74, 0x69, 0x61, 0x63, 0x71,
	0x75, 0x69, 0x72, 0x69, 0x6e, 0x67, 0x1a, 0x16, 0x6d, 0x76, 0x70, 0x2f, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x2f, 0x6c, 0x6f, 0x67, 0x67, 0x65, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1c,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f,
	0x73, 0x74, 0x72, 0x75, 0x63, 0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1f, 0x67, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x74, 0x69,
	0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1b, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x65,
	0x6d, 0x70, 0x74, 0x79, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x2e, 0x69, 0x6e, 0x6e, 0x65,
	0x72, 0x2f, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x2f, 0x67, 0x72, 0x70,
	0x63, 0x2f, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x73, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x27, 0x69, 0x6e, 0x6e, 0x65,
	0x72, 0x2f, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x2f, 0x67, 0x72, 0x70,
	0x63, 0x2f, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x22, 0x50, 0x0a, 0x1d, 0x47, 0x65, 0x74, 0x41, 0x63, 0x71, 0x75, 0x69, 0x72,
	0x65, 0x72, 0x49, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x66, 0x69, 0x65, 0x72, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x12, 0x2f, 0x0a, 0x13, 0x61, 0x63, 0x71, 0x75, 0x69, 0x72, 0x65, 0x72,
	0x5f, 0x69, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x66, 0x69, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x12, 0x61, 0x63, 0x71, 0x75, 0x69, 0x72, 0x65, 0x72, 0x49, 0x64, 0x65, 0x6e, 0x74,
	0x69, 0x66, 0x69, 0x65, 0x72, 0x22, 0xf6, 0x05, 0x0a, 0x10, 0x50, 0x61, 0x79, 0x49, 0x6e, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x44, 0x61, 0x74, 0x61, 0x12, 0x25, 0x0a, 0x0e, 0x74, 0x72,
	0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x04, 0x52, 0x0d, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x49,
	0x64, 0x12, 0x17, 0x0a, 0x07, 0x69, 0x73, 0x5f, 0x68, 0x6f, 0x6c, 0x64, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x08, 0x52, 0x06, 0x69, 0x73, 0x48, 0x6f, 0x6c, 0x64, 0x12, 0x16, 0x0a, 0x06, 0x61, 0x6d,
	0x6f, 0x75, 0x6e, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x01, 0x52, 0x06, 0x61, 0x6d, 0x6f, 0x75,
	0x6e, 0x74, 0x12, 0x20, 0x0a, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f,
	0x6e, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70,
	0x74, 0x69, 0x6f, 0x6e, 0x12, 0x4d, 0x0a, 0x04, 0x63, 0x61, 0x72, 0x64, 0x18, 0x05, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x39, 0x2e, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x2e,
	0x6d, 0x75, 0x6c, 0x74, 0x69, 0x61, 0x63, 0x71, 0x75, 0x69, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x6d,
	0x75, 0x6c, 0x74, 0x69, 0x61, 0x63, 0x71, 0x75, 0x69, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x43, 0x61,
	0x72, 0x64, 0x44, 0x61, 0x74, 0x61, 0x46, 0x6f, 0x72, 0x42, 0x61, 0x6e, 0x6b, 0x52, 0x04, 0x63,
	0x61, 0x72, 0x64, 0x12, 0x4d, 0x0a, 0x04, 0x75, 0x73, 0x65, 0x72, 0x18, 0x06, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x39, 0x2e, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x2e, 0x6d,
	0x75, 0x6c, 0x74, 0x69, 0x61, 0x63, 0x71, 0x75, 0x69, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x6d, 0x75,
	0x6c, 0x74, 0x69, 0x61, 0x63, 0x71, 0x75, 0x69, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x55, 0x73, 0x65,
	0x72, 0x44, 0x61, 0x74, 0x61, 0x46, 0x6f, 0x72, 0x42, 0x61, 0x6e, 0x6b, 0x52, 0x04, 0x75, 0x73,
	0x65, 0x72, 0x12, 0x59, 0x0a, 0x08, 0x74, 0x65, 0x72, 0x6d, 0x69, 0x6e, 0x61, 0x6c, 0x18, 0x07,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x3d, 0x2e, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e,
	0x67, 0x2e, 0x6d, 0x75, 0x6c, 0x74, 0x69, 0x61, 0x63, 0x71, 0x75, 0x69, 0x72, 0x69, 0x6e, 0x67,
	0x2e, 0x6d, 0x75, 0x6c, 0x74, 0x69, 0x61, 0x63, 0x71, 0x75, 0x69, 0x72, 0x69, 0x6e, 0x67, 0x2e,
	0x54, 0x65, 0x72, 0x6d, 0x69, 0x6e, 0x61, 0x6c, 0x44, 0x61, 0x74, 0x61, 0x46, 0x6f, 0x72, 0x42,
	0x61, 0x6e, 0x6b, 0x52, 0x08, 0x74, 0x65, 0x72, 0x6d, 0x69, 0x6e, 0x61, 0x6c, 0x12, 0x2c, 0x0a,
	0x12, 0x61, 0x67, 0x67, 0x72, 0x65, 0x67, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x74, 0x79, 0x70, 0x65,
	0x5f, 0x69, 0x64, 0x18, 0x08, 0x20, 0x01, 0x28, 0x04, 0x52, 0x10, 0x61, 0x67, 0x67, 0x72, 0x65,
	0x67, 0x61, 0x74, 0x65, 0x64, 0x54, 0x79, 0x70, 0x65, 0x49, 0x64, 0x12, 0x4f, 0x0a, 0x09, 0x63,
	0x61, 0x72, 0x64, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x09, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x32,
	0x2e, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x2e, 0x6d, 0x75, 0x6c, 0x74,
	0x69, 0x61, 0x63, 0x71, 0x75, 0x69, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x6d, 0x75, 0x6c, 0x74, 0x69,
	0x61, 0x63, 0x71, 0x75, 0x69, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x43, 0x61, 0x72, 0x64, 0x49, 0x6e,
	0x66, 0x6f, 0x52, 0x08, 0x63, 0x61, 0x72, 0x64, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x5b, 0x0a, 0x0d,
	0x6d, 0x65, 0x72, 0x63, 0x68, 0x61, 0x6e, 0x74, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x0a, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x36, 0x2e, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67,
	0x2e, 0x6d, 0x75, 0x6c, 0x74, 0x69, 0x61, 0x63, 0x71, 0x75, 0x69, 0x72, 0x69, 0x6e, 0x67, 0x2e,
	0x6d, 0x75, 0x6c, 0x74, 0x69, 0x61, 0x63, 0x71, 0x75, 0x69, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x4d,
	0x65, 0x72, 0x63, 0x68, 0x61, 0x6e, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x0c, 0x6d, 0x65, 0x72,
	0x63, 0x68, 0x61, 0x6e, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x50, 0x0a, 0x16, 0x74, 0x72, 0x61,
	0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64,
	0x5f, 0x61, 0x74, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65,
	0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x14, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69,
	0x6f, 0x6e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x12, 0x19, 0x0a, 0x08, 0x67,
	0x65, 0x74, 0x5f, 0x66, 0x6f, 0x72, 0x6d, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x08, 0x52, 0x07, 0x67,
	0x65, 0x74, 0x46, 0x6f, 0x72, 0x6d, 0x12, 0x26, 0x0a, 0x0f, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x69,
	0x70, 0x5f, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0d, 0x75, 0x73, 0x65, 0x72, 0x49, 0x70, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x22, 0x94,
	0x05, 0x0a, 0x18, 0x4f, 0x6e, 0x65, 0x43, 0x6c, 0x69, 0x63, 0x6b, 0x50, 0x61, 0x79, 0x49, 0x6e,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x44, 0x61, 0x74, 0x61, 0x12, 0x25, 0x0a, 0x0e, 0x74,
	0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x04, 0x52, 0x0d, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e,
	0x49, 0x64, 0x12, 0x17, 0x0a, 0x07, 0x63, 0x61, 0x72, 0x64, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x06, 0x63, 0x61, 0x72, 0x64, 0x49, 0x64, 0x12, 0x16, 0x0a, 0x06, 0x61,
	0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x01, 0x52, 0x06, 0x61, 0x6d, 0x6f,
	0x75, 0x6e, 0x74, 0x12, 0x20, 0x0a, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69,
	0x6f, 0x6e, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69,
	0x70, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x4d, 0x0a, 0x04, 0x75, 0x73, 0x65, 0x72, 0x18, 0x05, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x39, 0x2e, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67,
	0x2e, 0x6d, 0x75, 0x6c, 0x74, 0x69, 0x61, 0x63, 0x71, 0x75, 0x69, 0x72, 0x69, 0x6e, 0x67, 0x2e,
	0x6d, 0x75, 0x6c, 0x74, 0x69, 0x61, 0x63, 0x71, 0x75, 0x69, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x55,
	0x73, 0x65, 0x72, 0x44, 0x61, 0x74, 0x61, 0x46, 0x6f, 0x72, 0x42, 0x61, 0x6e, 0x6b, 0x52, 0x04,
	0x75, 0x73, 0x65, 0x72, 0x12, 0x59, 0x0a, 0x08, 0x74, 0x65, 0x72, 0x6d, 0x69, 0x6e, 0x61, 0x6c,
	0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x3d, 0x2e, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73,
	0x69, 0x6e, 0x67, 0x2e, 0x6d, 0x75, 0x6c, 0x74, 0x69, 0x61, 0x63, 0x71, 0x75, 0x69, 0x72, 0x69,
	0x6e, 0x67, 0x2e, 0x6d, 0x75, 0x6c, 0x74, 0x69, 0x61, 0x63, 0x71, 0x75, 0x69, 0x72, 0x69, 0x6e,
	0x67, 0x2e, 0x54, 0x65, 0x72, 0x6d, 0x69, 0x6e, 0x61, 0x6c, 0x44, 0x61, 0x74, 0x61, 0x46, 0x6f,
	0x72, 0x42, 0x61, 0x6e, 0x6b, 0x52, 0x08, 0x74, 0x65, 0x72, 0x6d, 0x69, 0x6e, 0x61, 0x6c, 0x12,
	0x2c, 0x0a, 0x12, 0x61, 0x67, 0x67, 0x72, 0x65, 0x67, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x74, 0x79,
	0x70, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x07, 0x20, 0x01, 0x28, 0x04, 0x52, 0x10, 0x61, 0x67, 0x67,
	0x72, 0x65, 0x67, 0x61, 0x74, 0x65, 0x64, 0x54, 0x79, 0x70, 0x65, 0x49, 0x64, 0x12, 0x4f, 0x0a,
	0x09, 0x63, 0x61, 0x72, 0x64, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x32, 0x2e, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x2e, 0x6d, 0x75,
	0x6c, 0x74, 0x69, 0x61, 0x63, 0x71, 0x75, 0x69, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x6d, 0x75, 0x6c,
	0x74, 0x69, 0x61, 0x63, 0x71, 0x75, 0x69, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x43, 0x61, 0x72, 0x64,
	0x49, 0x6e, 0x66, 0x6f, 0x52, 0x08, 0x63, 0x61, 0x72, 0x64, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x5b,
	0x0a, 0x0d, 0x6d, 0x65, 0x72, 0x63, 0x68, 0x61, 0x6e, 0x74, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18,
	0x09, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x36, 0x2e, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69,
	0x6e, 0x67, 0x2e, 0x6d, 0x75, 0x6c, 0x74, 0x69, 0x61, 0x63, 0x71, 0x75, 0x69, 0x72, 0x69, 0x6e,
	0x67, 0x2e, 0x6d, 0x75, 0x6c, 0x74, 0x69, 0x61, 0x63, 0x71, 0x75, 0x69, 0x72, 0x69, 0x6e, 0x67,
	0x2e, 0x4d, 0x65, 0x72, 0x63, 0x68, 0x61, 0x6e, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x0c, 0x6d,
	0x65, 0x72, 0x63, 0x68, 0x61, 0x6e, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x50, 0x0a, 0x16, 0x74,
	0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x63, 0x72, 0x65, 0x61, 0x74,
	0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69,
	0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x14, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63,
	0x74, 0x69, 0x6f, 0x6e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x12, 0x26, 0x0a,
	0x0f, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x69, 0x70, 0x5f, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73,
	0x18, 0x0b, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x75, 0x73, 0x65, 0x72, 0x49, 0x70, 0x41, 0x64,
	0x64, 0x72, 0x65, 0x73, 0x73, 0x22, 0x61, 0x0a, 0x0f, 0x55, 0x73, 0x65, 0x72, 0x44, 0x61, 0x74,
	0x61, 0x46, 0x6f, 0x72, 0x42, 0x61, 0x6e, 0x6b, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x12, 0x14, 0x0a, 0x05, 0x65, 0x6d, 0x61, 0x69,
	0x6c, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x12, 0x12,
	0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61,
	0x6d, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x70, 0x68, 0x6f, 0x6e, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x05, 0x70, 0x68, 0x6f, 0x6e, 0x65, 0x22, 0xa5, 0x01, 0x0a, 0x13, 0x54, 0x65, 0x72,
	0x6d, 0x69, 0x6e, 0x61, 0x6c, 0x44, 0x61, 0x74, 0x61, 0x46, 0x6f, 0x72, 0x42, 0x61, 0x6e, 0x6b,
	0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x02, 0x69, 0x64,
	0x12, 0x1f, 0x0a, 0x0b, 0x61, 0x63, 0x71, 0x75, 0x69, 0x72, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x04, 0x52, 0x0a, 0x61, 0x63, 0x71, 0x75, 0x69, 0x72, 0x65, 0x72, 0x49,
	0x64, 0x12, 0x23, 0x0a, 0x0d, 0x61, 0x63, 0x71, 0x75, 0x69, 0x72, 0x65, 0x72, 0x5f, 0x63, 0x6f,
	0x64, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x61, 0x63, 0x71, 0x75, 0x69, 0x72,
	0x65, 0x72, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x38, 0x0a, 0x06, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x17, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x53, 0x74, 0x72, 0x75, 0x63, 0x74, 0x42,
	0x07, 0x9a, 0xfc, 0xeb, 0x8e, 0x02, 0x01, 0x64, 0x52, 0x06, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67,
	0x22, 0xc6, 0x01, 0x0a, 0x0f, 0x43, 0x61, 0x72, 0x64, 0x44, 0x61, 0x74, 0x61, 0x46, 0x6f, 0x72,
	0x42, 0x61, 0x6e, 0x6b, 0x12, 0x19, 0x0a, 0x03, 0x70, 0x61, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x0c, 0x42, 0x07, 0x9a, 0xfc, 0xeb, 0x8e, 0x02, 0x01, 0x64, 0x52, 0x03, 0x70, 0x61, 0x6e, 0x12,
	0x1b, 0x0a, 0x09, 0x65, 0x78, 0x70, 0x5f, 0x6d, 0x6f, 0x6e, 0x74, 0x68, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x0c, 0x52, 0x08, 0x65, 0x78, 0x70, 0x4d, 0x6f, 0x6e, 0x74, 0x68, 0x12, 0x19, 0x0a, 0x08,
	0x65, 0x78, 0x70, 0x5f, 0x79, 0x65, 0x61, 0x72, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x07,
	0x65, 0x78, 0x70, 0x59, 0x65, 0x61, 0x72, 0x12, 0x19, 0x0a, 0x03, 0x63, 0x76, 0x63, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x0c, 0x42, 0x07, 0x9a, 0xfc, 0xeb, 0x8e, 0x02, 0x01, 0x64, 0x52, 0x03, 0x63,
	0x76, 0x63, 0x12, 0x1b, 0x0a, 0x09, 0x66, 0x75, 0x6c, 0x6c, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18,
	0x05, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x08, 0x66, 0x75, 0x6c, 0x6c, 0x4e, 0x61, 0x6d, 0x65, 0x12,
	0x14, 0x0a, 0x05, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05,
	0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x12, 0x12, 0x0a, 0x04, 0x73, 0x61, 0x76, 0x65, 0x18, 0x07, 0x20,
	0x01, 0x28, 0x08, 0x52, 0x04, 0x73, 0x61, 0x76, 0x65, 0x22, 0xa3, 0x04, 0x0a, 0x11, 0x50, 0x61,
	0x79, 0x49, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x44, 0x61, 0x74, 0x61, 0x12,
	0x25, 0x0a, 0x0e, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x69,
	0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x0d, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63,
	0x74, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x12, 0x2a, 0x0a, 0x11, 0x62, 0x61, 0x6e, 0x6b, 0x5f, 0x72,
	0x65, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0f, 0x62, 0x61, 0x6e, 0x6b, 0x52, 0x65, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65,
	0x49, 0x64, 0x12, 0x6f, 0x0a, 0x12, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f,
	0x6e, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x40,
	0x2e, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x2e, 0x74, 0x72, 0x61, 0x6e,
	0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74,
	0x69, 0x6f, 0x6e, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x2e, 0x45, 0x6e, 0x75, 0x6d, 0x54,
	0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x52, 0x11, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x12, 0x17, 0x0a, 0x07, 0x63, 0x61, 0x72, 0x64, 0x5f, 0x69, 0x64, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x63, 0x61, 0x72, 0x64, 0x49, 0x64, 0x12, 0x57, 0x0a, 0x07,
	0x74, 0x68, 0x72, 0x65, 0x65, 0x44, 0x53, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x3d, 0x2e,
	0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x2e, 0x6d, 0x75, 0x6c, 0x74, 0x69,
	0x61, 0x63, 0x71, 0x75, 0x69, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x6d, 0x75, 0x6c, 0x74, 0x69, 0x61,
	0x63, 0x71, 0x75, 0x69, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x54, 0x68, 0x72, 0x65, 0x65, 0x44, 0x53,
	0x44, 0x61, 0x74, 0x61, 0x46, 0x72, 0x6f, 0x6d, 0x42, 0x61, 0x6e, 0x6b, 0x52, 0x07, 0x74, 0x68,
	0x72, 0x65, 0x65, 0x44, 0x53, 0x12, 0x5b, 0x0a, 0x0d, 0x62, 0x61, 0x6e, 0x6b, 0x5f, 0x72, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x36, 0x2e, 0x70,
	0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x2e, 0x6d, 0x75, 0x6c, 0x74, 0x69, 0x61,
	0x63, 0x71, 0x75, 0x69, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x6d, 0x75, 0x6c, 0x74, 0x69, 0x61, 0x63,
	0x71, 0x75, 0x69, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x42, 0x61, 0x6e, 0x6b, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x52, 0x0c, 0x62, 0x61, 0x6e, 0x6b, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x12, 0x22, 0x0a, 0x0d, 0x62, 0x61, 0x6e, 0x6b, 0x5f, 0x6f, 0x72, 0x64, 0x65, 0x72,
	0x5f, 0x69, 0x64, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x62, 0x61, 0x6e, 0x6b, 0x4f,
	0x72, 0x64, 0x65, 0x72, 0x49, 0x64, 0x12, 0x57, 0x0a, 0x0b, 0x66, 0x69, 0x6e, 0x67, 0x65, 0x72,
	0x70, 0x72, 0x69, 0x6e, 0x74, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x35, 0x2e, 0x70, 0x72,
	0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x2e, 0x6d, 0x75, 0x6c, 0x74, 0x69, 0x61, 0x63,
	0x71, 0x75, 0x69, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x6d, 0x75, 0x6c, 0x74, 0x69, 0x61, 0x63, 0x71,
	0x75, 0x69, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x46, 0x69, 0x6e, 0x67, 0x65, 0x72, 0x70, 0x72, 0x69,
	0x6e, 0x74, 0x52, 0x0b, 0x66, 0x69, 0x6e, 0x67, 0x65, 0x72, 0x70, 0x72, 0x69, 0x6e, 0x74, 0x22,
	0x7a, 0x0a, 0x13, 0x54, 0x68, 0x72, 0x65, 0x65, 0x44, 0x53, 0x44, 0x61, 0x74, 0x61, 0x46, 0x72,
	0x6f, 0x6d, 0x42, 0x61, 0x6e, 0x6b, 0x12, 0x16, 0x0a, 0x06, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x2f,
	0x0a, 0x06, 0x70, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x17,
	0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66,
	0x2e, 0x53, 0x74, 0x72, 0x75, 0x63, 0x74, 0x52, 0x06, 0x70, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x12,
	0x1a, 0x0a, 0x08, 0x74, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x0c, 0x52, 0x08, 0x74, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x22, 0xd1, 0x02, 0x0a, 0x12,
	0x54, 0x68, 0x72, 0x65, 0x65, 0x44, 0x53, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x44, 0x61,
	0x74, 0x61, 0x12, 0x25, 0x0a, 0x0e, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f,
	0x6e, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x0d, 0x74, 0x72, 0x61, 0x6e,
	0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x12, 0x2a, 0x0a, 0x11, 0x62, 0x61, 0x6e,
	0x6b, 0x5f, 0x72, 0x65, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0f, 0x62, 0x61, 0x6e, 0x6b, 0x52, 0x65, 0x66, 0x65, 0x72, 0x65,
	0x6e, 0x63, 0x65, 0x49, 0x64, 0x12, 0x14, 0x0a, 0x05, 0x70, 0x61, 0x72, 0x65, 0x73, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x70, 0x61, 0x72, 0x65, 0x73, 0x12, 0x0e, 0x0a, 0x02, 0x6d,
	0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x6d, 0x64, 0x12, 0x16, 0x0a, 0x06, 0x61,
	0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x05, 0x20, 0x01, 0x28, 0x01, 0x52, 0x06, 0x61, 0x6d, 0x6f,
	0x75, 0x6e, 0x74, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x72, 0x65, 0x73, 0x18, 0x06, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x04, 0x63, 0x72, 0x65, 0x73, 0x12, 0x59, 0x0a, 0x08, 0x74, 0x65, 0x72, 0x6d, 0x69,
	0x6e, 0x61, 0x6c, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x3d, 0x2e, 0x70, 0x72, 0x6f, 0x63,
	0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x2e, 0x6d, 0x75, 0x6c, 0x74, 0x69, 0x61, 0x63, 0x71, 0x75,
	0x69, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x6d, 0x75, 0x6c, 0x74, 0x69, 0x61, 0x63, 0x71, 0x75, 0x69,
	0x72, 0x69, 0x6e, 0x67, 0x2e, 0x54, 0x65, 0x72, 0x6d, 0x69, 0x6e, 0x61, 0x6c, 0x44, 0x61, 0x74,
	0x61, 0x46, 0x6f, 0x72, 0x42, 0x61, 0x6e, 0x6b, 0x52, 0x08, 0x74, 0x65, 0x72, 0x6d, 0x69, 0x6e,
	0x61, 0x6c, 0x12, 0x22, 0x0a, 0x0d, 0x62, 0x61, 0x6e, 0x6b, 0x5f, 0x6f, 0x72, 0x64, 0x65, 0x72,
	0x5f, 0x69, 0x64, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x62, 0x61, 0x6e, 0x6b, 0x4f,
	0x72, 0x64, 0x65, 0x72, 0x49, 0x64, 0x12, 0x17, 0x0a, 0x07, 0x69, 0x73, 0x5f, 0x68, 0x6f, 0x6c,
	0x64, 0x18, 0x09, 0x20, 0x01, 0x28, 0x08, 0x52, 0x06, 0x69, 0x73, 0x48, 0x6f, 0x6c, 0x64, 0x22,
	0xf9, 0x02, 0x0a, 0x13, 0x54, 0x68, 0x72, 0x65, 0x65, 0x44, 0x53, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x44, 0x61, 0x74, 0x61, 0x12, 0x25, 0x0a, 0x0e, 0x74, 0x72, 0x61, 0x6e, 0x73,
	0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52,
	0x0d, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x12, 0x2a,
	0x0a, 0x11, 0x62, 0x61, 0x6e, 0x6b, 0x5f, 0x72, 0x65, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65,
	0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f, 0x62, 0x61, 0x6e, 0x6b, 0x52,
	0x65, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x49, 0x64, 0x12, 0x22, 0x0a, 0x0d, 0x62, 0x61,
	0x6e, 0x6b, 0x5f, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0b, 0x62, 0x61, 0x6e, 0x6b, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x49, 0x64, 0x12, 0x6f,
	0x0a, 0x12, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x73, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x40, 0x2e, 0x70, 0x72, 0x6f,
	0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x2e, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74,
	0x69, 0x6f, 0x6e, 0x2e, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x5f,
	0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x2e, 0x45, 0x6e, 0x75, 0x6d, 0x54, 0x72, 0x61, 0x6e, 0x73,
	0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x11, 0x74, 0x72,
	0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12,
	0x1d, 0x0a, 0x0a, 0x63, 0x61, 0x72, 0x64, 0x5f, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x18, 0x05, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x09, 0x63, 0x61, 0x72, 0x64, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x12, 0x5b,
	0x0a, 0x0d, 0x62, 0x61, 0x6e, 0x6b, 0x5f, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x18,
	0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x36, 0x2e, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69,
	0x6e, 0x67, 0x2e, 0x6d, 0x75, 0x6c, 0x74, 0x69, 0x61, 0x63, 0x71, 0x75, 0x69, 0x72, 0x69, 0x6e,
	0x67, 0x2e, 0x6d, 0x75, 0x6c, 0x74, 0x69, 0x61, 0x63, 0x71, 0x75, 0x69, 0x72, 0x69, 0x6e, 0x67,
	0x2e, 0x42, 0x61, 0x6e, 0x6b, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x52, 0x0c, 0x62,
	0x61, 0x6e, 0x6b, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0xa6, 0x05, 0x0a, 0x11,
	0x50, 0x61, 0x79, 0x4f, 0x75, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x44, 0x61, 0x74,
	0x61, 0x12, 0x25, 0x0a, 0x0e, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e,
	0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x0d, 0x74, 0x72, 0x61, 0x6e, 0x73,
	0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x12, 0x19, 0x0a, 0x03, 0x70, 0x61, 0x6e, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x0c, 0x42, 0x07, 0x9a, 0xfc, 0xeb, 0x8e, 0x02, 0x01, 0x64, 0x52, 0x03,
	0x70, 0x61, 0x6e, 0x12, 0x16, 0x0a, 0x06, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x01, 0x52, 0x06, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x4d, 0x0a, 0x04, 0x75,
	0x73, 0x65, 0x72, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x39, 0x2e, 0x70, 0x72, 0x6f, 0x63,
	0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x2e, 0x6d, 0x75, 0x6c, 0x74, 0x69, 0x61, 0x63, 0x71, 0x75,
	0x69, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x6d, 0x75, 0x6c, 0x74, 0x69, 0x61, 0x63, 0x71, 0x75, 0x69,
	0x72, 0x69, 0x6e, 0x67, 0x2e, 0x55, 0x73, 0x65, 0x72, 0x44, 0x61, 0x74, 0x61, 0x46, 0x6f, 0x72,
	0x42, 0x61, 0x6e, 0x6b, 0x52, 0x04, 0x75, 0x73, 0x65, 0x72, 0x12, 0x59, 0x0a, 0x08, 0x74, 0x65,
	0x72, 0x6d, 0x69, 0x6e, 0x61, 0x6c, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x3d, 0x2e, 0x70,
	0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x2e, 0x6d, 0x75, 0x6c, 0x74, 0x69, 0x61,
	0x63, 0x71, 0x75, 0x69, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x6d, 0x75, 0x6c, 0x74, 0x69, 0x61, 0x63,
	0x71, 0x75, 0x69, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x54, 0x65, 0x72, 0x6d, 0x69, 0x6e, 0x61, 0x6c,
	0x44, 0x61, 0x74, 0x61, 0x46, 0x6f, 0x72, 0x42, 0x61, 0x6e, 0x6b, 0x52, 0x08, 0x74, 0x65, 0x72,
	0x6d, 0x69, 0x6e, 0x61, 0x6c, 0x12, 0x48, 0x0a, 0x04, 0x63, 0x61, 0x72, 0x64, 0x18, 0x06, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x34, 0x2e, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67,
	0x2e, 0x6d, 0x75, 0x6c, 0x74, 0x69, 0x61, 0x63, 0x71, 0x75, 0x69, 0x72, 0x69, 0x6e, 0x67, 0x2e,
	0x6d, 0x75, 0x6c, 0x74, 0x69, 0x61, 0x63, 0x71, 0x75, 0x69, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x50,
	0x61, 0x79, 0x4f, 0x75, 0x74, 0x43, 0x61, 0x72, 0x64, 0x52, 0x04, 0x63, 0x61, 0x72, 0x64, 0x12,
	0x2c, 0x0a, 0x12, 0x61, 0x67, 0x67, 0x72, 0x65, 0x67, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x74, 0x79,
	0x70, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x07, 0x20, 0x01, 0x28, 0x04, 0x52, 0x10, 0x61, 0x67, 0x67,
	0x72, 0x65, 0x67, 0x61, 0x74, 0x65, 0x64, 0x54, 0x79, 0x70, 0x65, 0x49, 0x64, 0x12, 0x4f, 0x0a,
	0x09, 0x63, 0x61, 0x72, 0x64, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x32, 0x2e, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x2e, 0x6d, 0x75,
	0x6c, 0x74, 0x69, 0x61, 0x63, 0x71, 0x75, 0x69, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x6d, 0x75, 0x6c,
	0x74, 0x69, 0x61, 0x63, 0x71, 0x75, 0x69, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x43, 0x61, 0x72, 0x64,
	0x49, 0x6e, 0x66, 0x6f, 0x52, 0x08, 0x63, 0x61, 0x72, 0x64, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x5b,
	0x0a, 0x0d, 0x6d, 0x65, 0x72, 0x63, 0x68, 0x61, 0x6e, 0x74, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18,
	0x09, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x36, 0x2e, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69,
	0x6e, 0x67, 0x2e, 0x6d, 0x75, 0x6c, 0x74, 0x69, 0x61, 0x63, 0x71, 0x75, 0x69, 0x72, 0x69, 0x6e,
	0x67, 0x2e, 0x6d, 0x75, 0x6c, 0x74, 0x69, 0x61, 0x63, 0x71, 0x75, 0x69, 0x72, 0x69, 0x6e, 0x67,
	0x2e, 0x4d, 0x65, 0x72, 0x63, 0x68, 0x61, 0x6e, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x0c, 0x6d,
	0x65, 0x72, 0x63, 0x68, 0x61, 0x6e, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x50, 0x0a, 0x16, 0x74,
	0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x63, 0x72, 0x65, 0x61, 0x74,
	0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69,
	0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x14, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63,
	0x74, 0x69, 0x6f, 0x6e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x12, 0x15, 0x0a,
	0x06, 0x63, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x63,
	0x49, 0x6e, 0x66, 0x6f, 0x22, 0xbc, 0x05, 0x0a, 0x18, 0x50, 0x61, 0x79, 0x4f, 0x75, 0x74, 0x42,
	0x79, 0x50, 0x68, 0x6f, 0x6e, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x44, 0x61, 0x74,
	0x61, 0x12, 0x25, 0x0a, 0x0e, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e,
	0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x0d, 0x74, 0x72, 0x61, 0x6e, 0x73,
	0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x12, 0x28, 0x0a, 0x10, 0x76, 0x69, 0x73, 0x61,
	0x5f, 0x61, 0x6c, 0x69, 0x61, 0x73, 0x5f, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0e, 0x76, 0x69, 0x73, 0x61, 0x41, 0x6c, 0x69, 0x61, 0x73, 0x54, 0x6f, 0x6b,
	0x65, 0x6e, 0x12, 0x16, 0x0a, 0x06, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x01, 0x52, 0x06, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x4d, 0x0a, 0x04, 0x75, 0x73,
	0x65, 0x72, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x39, 0x2e, 0x70, 0x72, 0x6f, 0x63, 0x65,
	0x73, 0x73, 0x69, 0x6e, 0x67, 0x2e, 0x6d, 0x75, 0x6c, 0x74, 0x69, 0x61, 0x63, 0x71, 0x75, 0x69,
	0x72, 0x69, 0x6e, 0x67, 0x2e, 0x6d, 0x75, 0x6c, 0x74, 0x69, 0x61, 0x63, 0x71, 0x75, 0x69, 0x72,
	0x69, 0x6e, 0x67, 0x2e, 0x55, 0x73, 0x65, 0x72, 0x44, 0x61, 0x74, 0x61, 0x46, 0x6f, 0x72, 0x42,
	0x61, 0x6e, 0x6b, 0x52, 0x04, 0x75, 0x73, 0x65, 0x72, 0x12, 0x59, 0x0a, 0x08, 0x74, 0x65, 0x72,
	0x6d, 0x69, 0x6e, 0x61, 0x6c, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x3d, 0x2e, 0x70, 0x72,
	0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x2e, 0x6d, 0x75, 0x6c, 0x74, 0x69, 0x61, 0x63,
	0x71, 0x75, 0x69, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x6d, 0x75, 0x6c, 0x74, 0x69, 0x61, 0x63, 0x71,
	0x75, 0x69, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x54, 0x65, 0x72, 0x6d, 0x69, 0x6e, 0x61, 0x6c, 0x44,
	0x61, 0x74, 0x61, 0x46, 0x6f, 0x72, 0x42, 0x61, 0x6e, 0x6b, 0x52, 0x08, 0x74, 0x65, 0x72, 0x6d,
	0x69, 0x6e, 0x61, 0x6c, 0x12, 0x48, 0x0a, 0x04, 0x63, 0x61, 0x72, 0x64, 0x18, 0x06, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x34, 0x2e, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x2e,
	0x6d, 0x75, 0x6c, 0x74, 0x69, 0x61, 0x63, 0x71, 0x75, 0x69, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x6d,
	0x75, 0x6c, 0x74, 0x69, 0x61, 0x63, 0x71, 0x75, 0x69, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x50, 0x61,
	0x79, 0x4f, 0x75, 0x74, 0x43, 0x61, 0x72, 0x64, 0x52, 0x04, 0x63, 0x61, 0x72, 0x64, 0x12, 0x2c,
	0x0a, 0x12, 0x61, 0x67, 0x67, 0x72, 0x65, 0x67, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x74, 0x79, 0x70,
	0x65, 0x5f, 0x69, 0x64, 0x18, 0x07, 0x20, 0x01, 0x28, 0x04, 0x52, 0x10, 0x61, 0x67, 0x67, 0x72,
	0x65, 0x67, 0x61, 0x74, 0x65, 0x64, 0x54, 0x79, 0x70, 0x65, 0x49, 0x64, 0x12, 0x4f, 0x0a, 0x09,
	0x63, 0x61, 0x72, 0x64, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x32, 0x2e, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x2e, 0x6d, 0x75, 0x6c,
	0x74, 0x69, 0x61, 0x63, 0x71, 0x75, 0x69, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x6d, 0x75, 0x6c, 0x74,
	0x69, 0x61, 0x63, 0x71, 0x75, 0x69, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x43, 0x61, 0x72, 0x64, 0x49,
	0x6e, 0x66, 0x6f, 0x52, 0x08, 0x63, 0x61, 0x72, 0x64, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x5b, 0x0a,
	0x0d, 0x6d, 0x65, 0x72, 0x63, 0x68, 0x61, 0x6e, 0x74, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x09,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x36, 0x2e, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e,
	0x67, 0x2e, 0x6d, 0x75, 0x6c, 0x74, 0x69, 0x61, 0x63, 0x71, 0x75, 0x69, 0x72, 0x69, 0x6e, 0x67,
	0x2e, 0x6d, 0x75, 0x6c, 0x74, 0x69, 0x61, 0x63, 0x71, 0x75, 0x69, 0x72, 0x69, 0x6e, 0x67, 0x2e,
	0x4d, 0x65, 0x72, 0x63, 0x68, 0x61, 0x6e, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x0c, 0x6d, 0x65,
	0x72, 0x63, 0x68, 0x61, 0x6e, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x50, 0x0a, 0x16, 0x74, 0x72,
	0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65,
	0x64, 0x5f, 0x61, 0x74, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d,
	0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x14, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74,
	0x69, 0x6f, 0x6e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x12, 0x15, 0x0a, 0x06,
	0x63, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x63, 0x49,
	0x6e, 0x66, 0x6f, 0x22, 0xe0, 0x02, 0x0a, 0x19, 0x50, 0x61, 0x79, 0x4f, 0x75, 0x74, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x42, 0x79, 0x50, 0x68, 0x6f, 0x6e, 0x65, 0x44, 0x61, 0x74,
	0x61, 0x12, 0x25, 0x0a, 0x0e, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e,
	0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x0d, 0x74, 0x72, 0x61, 0x6e, 0x73,
	0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x12, 0x2a, 0x0a, 0x11, 0x62, 0x61, 0x6e, 0x6b,
	0x5f, 0x72, 0x65, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0f, 0x62, 0x61, 0x6e, 0x6b, 0x52, 0x65, 0x66, 0x65, 0x72, 0x65, 0x6e,
	0x63, 0x65, 0x49, 0x64, 0x12, 0x22, 0x0a, 0x0d, 0x62, 0x61, 0x6e, 0x6b, 0x5f, 0x6f, 0x72, 0x64,
	0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x62, 0x61, 0x6e,
	0x6b, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x49, 0x64, 0x12, 0x6f, 0x0a, 0x12, 0x74, 0x72, 0x61, 0x6e,
	0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x0e, 0x32, 0x40, 0x2e, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e,
	0x67, 0x2e, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x74, 0x72,
	0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x2e, 0x45, 0x6e, 0x75, 0x6d, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e,
	0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x11, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74,
	0x69, 0x6f, 0x6e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x5b, 0x0a, 0x0d, 0x62, 0x61, 0x6e,
	0x6b, 0x5f, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x36, 0x2e, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x2e, 0x6d, 0x75,
	0x6c, 0x74, 0x69, 0x61, 0x63, 0x71, 0x75, 0x69, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x6d, 0x75, 0x6c,
	0x74, 0x69, 0x61, 0x63, 0x71, 0x75, 0x69, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x42, 0x61, 0x6e, 0x6b,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x52, 0x0c, 0x62, 0x61, 0x6e, 0x6b, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x7a, 0x0a, 0x0a, 0x50, 0x61, 0x79, 0x4f, 0x75, 0x74,
	0x43, 0x61, 0x72, 0x64, 0x12, 0x19, 0x0a, 0x03, 0x70, 0x61, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x0c, 0x42, 0x07, 0x9a, 0xfc, 0xeb, 0x8e, 0x02, 0x01, 0x64, 0x52, 0x03, 0x70, 0x61, 0x6e, 0x12,
	0x1b, 0x0a, 0x09, 0x65, 0x78, 0x70, 0x5f, 0x6d, 0x6f, 0x6e, 0x74, 0x68, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x0c, 0x52, 0x08, 0x65, 0x78, 0x70, 0x4d, 0x6f, 0x6e, 0x74, 0x68, 0x12, 0x19, 0x0a, 0x08,
	0x65, 0x78, 0x70, 0x5f, 0x79, 0x65, 0x61, 0x72, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x07,
	0x65, 0x78, 0x70, 0x59, 0x65, 0x61, 0x72, 0x12, 0x19, 0x0a, 0x03, 0x63, 0x76, 0x63, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x0c, 0x42, 0x07, 0x9a, 0xfc, 0xeb, 0x8e, 0x02, 0x01, 0x64, 0x52, 0x03, 0x63,
	0x76, 0x63, 0x22, 0xd9, 0x02, 0x0a, 0x12, 0x50, 0x61, 0x79, 0x4f, 0x75, 0x74, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x44, 0x61, 0x74, 0x61, 0x12, 0x25, 0x0a, 0x0e, 0x74, 0x72, 0x61,
	0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x04, 0x52, 0x0d, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x64,
	0x12, 0x2a, 0x0a, 0x11, 0x62, 0x61, 0x6e, 0x6b, 0x5f, 0x72, 0x65, 0x66, 0x65, 0x72, 0x65, 0x6e,
	0x63, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f, 0x62, 0x61, 0x6e,
	0x6b, 0x52, 0x65, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x49, 0x64, 0x12, 0x22, 0x0a, 0x0d,
	0x62, 0x61, 0x6e, 0x6b, 0x5f, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0b, 0x62, 0x61, 0x6e, 0x6b, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x49, 0x64,
	0x12, 0x6f, 0x0a, 0x12, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x5f,
	0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x40, 0x2e, 0x70,
	0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x2e, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61,
	0x63, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f,
	0x6e, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x2e, 0x45, 0x6e, 0x75, 0x6d, 0x54, 0x72, 0x61,
	0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x11,
	0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x12, 0x5b, 0x0a, 0x0d, 0x62, 0x61, 0x6e, 0x6b, 0x5f, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x36, 0x2e, 0x70, 0x72, 0x6f, 0x63, 0x65,
	0x73, 0x73, 0x69, 0x6e, 0x67, 0x2e, 0x6d, 0x75, 0x6c, 0x74, 0x69, 0x61, 0x63, 0x71, 0x75, 0x69,
	0x72, 0x69, 0x6e, 0x67, 0x2e, 0x6d, 0x75, 0x6c, 0x74, 0x69, 0x61, 0x63, 0x71, 0x75, 0x69, 0x72,
	0x69, 0x6e, 0x67, 0x2e, 0x42, 0x61, 0x6e, 0x6b, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x52, 0x0c, 0x62, 0x61, 0x6e, 0x6b, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0xc3,
	0x03, 0x0a, 0x1c, 0x42, 0x61, 0x6e, 0x6b, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69,
	0x6f, 0x6e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12,
	0x25, 0x0a, 0x0e, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x69,
	0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x0d, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63,
	0x74, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x12, 0x29, 0x0a, 0x10, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61,
	0x63, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0f, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70,
	0x65, 0x12, 0x23, 0x0a, 0x0d, 0x61, 0x63, 0x71, 0x75, 0x69, 0x72, 0x65, 0x72, 0x5f, 0x63, 0x6f,
	0x64, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x61, 0x63, 0x71, 0x75, 0x69, 0x72,
	0x65, 0x72, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x2f, 0x0a, 0x06, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x17, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x53, 0x74, 0x72, 0x75, 0x63, 0x74, 0x52,
	0x06, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x2a, 0x0a, 0x11, 0x62, 0x61, 0x6e, 0x6b, 0x5f,
	0x72, 0x65, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x05, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0f, 0x62, 0x61, 0x6e, 0x6b, 0x52, 0x65, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63,
	0x65, 0x49, 0x64, 0x12, 0x22, 0x0a, 0x0d, 0x62, 0x61, 0x6e, 0x6b, 0x5f, 0x6f, 0x72, 0x64, 0x65,
	0x72, 0x5f, 0x69, 0x64, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x62, 0x61, 0x6e, 0x6b,
	0x4f, 0x72, 0x64, 0x65, 0x72, 0x49, 0x64, 0x12, 0x59, 0x0a, 0x08, 0x74, 0x65, 0x72, 0x6d, 0x69,
	0x6e, 0x61, 0x6c, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x3d, 0x2e, 0x70, 0x72, 0x6f, 0x63,
	0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x2e, 0x6d, 0x75, 0x6c, 0x74, 0x69, 0x61, 0x63, 0x71, 0x75,
	0x69, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x6d, 0x75, 0x6c, 0x74, 0x69, 0x61, 0x63, 0x71, 0x75, 0x69,
	0x72, 0x69, 0x6e, 0x67, 0x2e, 0x54, 0x65, 0x72, 0x6d, 0x69, 0x6e, 0x61, 0x6c, 0x44, 0x61, 0x74,
	0x61, 0x46, 0x6f, 0x72, 0x42, 0x61, 0x6e, 0x6b, 0x52, 0x08, 0x74, 0x65, 0x72, 0x6d, 0x69, 0x6e,
	0x61, 0x6c, 0x12, 0x50, 0x0a, 0x16, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f,
	0x6e, 0x5f, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x08, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x14,
	0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x43, 0x72, 0x65, 0x61, 0x74,
	0x65, 0x64, 0x41, 0x74, 0x22, 0xc0, 0x02, 0x0a, 0x1d, 0x42, 0x61, 0x6e, 0x6b, 0x54, 0x72, 0x61,
	0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x25, 0x0a, 0x0e, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61,
	0x63, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x0d,
	0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x12, 0x2a, 0x0a,
	0x11, 0x62, 0x61, 0x6e, 0x6b, 0x5f, 0x72, 0x65, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x5f,
	0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f, 0x62, 0x61, 0x6e, 0x6b, 0x52, 0x65,
	0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x49, 0x64, 0x12, 0x6f, 0x0a, 0x12, 0x74, 0x72, 0x61,
	0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x40, 0x2e, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69,
	0x6e, 0x67, 0x2e, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x74,
	0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x2e, 0x45, 0x6e, 0x75, 0x6d, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f,
	0x6e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x11, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63,
	0x74, 0x69, 0x6f, 0x6e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x5b, 0x0a, 0x0d, 0x62, 0x61,
	0x6e, 0x6b, 0x5f, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x36, 0x2e, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x2e, 0x6d,
	0x75, 0x6c, 0x74, 0x69, 0x61, 0x63, 0x71, 0x75, 0x69, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x6d, 0x75,
	0x6c, 0x74, 0x69, 0x61, 0x63, 0x71, 0x75, 0x69, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x42, 0x61, 0x6e,
	0x6b, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x52, 0x0c, 0x62, 0x61, 0x6e, 0x6b, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0xcd, 0x03, 0x0a, 0x26, 0x42, 0x61, 0x6e, 0x6b,
	0x54, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x55, 0x6e, 0x66, 0x6f, 0x72, 0x6d, 0x61, 0x74, 0x65, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x12, 0x25, 0x0a, 0x0e, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f,
	0x6e, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x0d, 0x74, 0x72, 0x61, 0x6e,
	0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x12, 0x29, 0x0a, 0x10, 0x74, 0x72, 0x61,
	0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0f, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e,
	0x54, 0x79, 0x70, 0x65, 0x12, 0x23, 0x0a, 0x0d, 0x61, 0x63, 0x71, 0x75, 0x69, 0x72, 0x65, 0x72,
	0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x61, 0x63, 0x71,
	0x75, 0x69, 0x72, 0x65, 0x72, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x2f, 0x0a, 0x06, 0x63, 0x6f, 0x6e,
	0x66, 0x69, 0x67, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x17, 0x2e, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x53, 0x74, 0x72, 0x75,
	0x63, 0x74, 0x52, 0x06, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x2a, 0x0a, 0x11, 0x62, 0x61,
	0x6e, 0x6b, 0x5f, 0x72, 0x65, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x5f, 0x69, 0x64, 0x18,
	0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f, 0x62, 0x61, 0x6e, 0x6b, 0x52, 0x65, 0x66, 0x65, 0x72,
	0x65, 0x6e, 0x63, 0x65, 0x49, 0x64, 0x12, 0x22, 0x0a, 0x0d, 0x62, 0x61, 0x6e, 0x6b, 0x5f, 0x6f,
	0x72, 0x64, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x62,
	0x61, 0x6e, 0x6b, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x49, 0x64, 0x12, 0x59, 0x0a, 0x08, 0x74, 0x65,
	0x72, 0x6d, 0x69, 0x6e, 0x61, 0x6c, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x3d, 0x2e, 0x70,
	0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x2e, 0x6d, 0x75, 0x6c, 0x74, 0x69, 0x61,
	0x63, 0x71, 0x75, 0x69, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x6d, 0x75, 0x6c, 0x74, 0x69, 0x61, 0x63,
	0x71, 0x75, 0x69, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x54, 0x65, 0x72, 0x6d, 0x69, 0x6e, 0x61, 0x6c,
	0x44, 0x61, 0x74, 0x61, 0x46, 0x6f, 0x72, 0x42, 0x61, 0x6e, 0x6b, 0x52, 0x08, 0x74, 0x65, 0x72,
	0x6d, 0x69, 0x6e, 0x61, 0x6c, 0x12, 0x50, 0x0a, 0x16, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63,
	0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18,
	0x08, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d,
	0x70, 0x52, 0x14, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x43, 0x72,
	0x65, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x22, 0x73, 0x0a, 0x27, 0x42, 0x61, 0x6e, 0x6b, 0x54,
	0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x55, 0x6e, 0x66, 0x6f, 0x72, 0x6d, 0x61, 0x74, 0x65, 0x64, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x12, 0x25, 0x0a, 0x0e, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f,
	0x6e, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x0d, 0x74, 0x72, 0x61, 0x6e,
	0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x12, 0x21, 0x0a, 0x0c, 0x62, 0x61, 0x6e,
	0x6b, 0x5f, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0b, 0x62, 0x61, 0x6e, 0x6b, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x22, 0x87, 0x03, 0x0a,
	0x0d, 0x52, 0x65, 0x66, 0x75, 0x6e, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x25,
	0x0a, 0x0e, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x69, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x0d, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74,
	0x69, 0x6f, 0x6e, 0x49, 0x64, 0x12, 0x23, 0x0a, 0x0d, 0x72, 0x65, 0x66, 0x75, 0x6e, 0x64, 0x5f,
	0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x01, 0x52, 0x0c, 0x72, 0x65,
	0x66, 0x75, 0x6e, 0x64, 0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x59, 0x0a, 0x08, 0x74, 0x65,
	0x72, 0x6d, 0x69, 0x6e, 0x61, 0x6c, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x3d, 0x2e, 0x70,
	0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x2e, 0x6d, 0x75, 0x6c, 0x74, 0x69, 0x61,
	0x63, 0x71, 0x75, 0x69, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x6d, 0x75, 0x6c, 0x74, 0x69, 0x61, 0x63,
	0x71, 0x75, 0x69, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x54, 0x65, 0x72, 0x6d, 0x69, 0x6e, 0x61, 0x6c,
	0x44, 0x61, 0x74, 0x61, 0x46, 0x6f, 0x72, 0x42, 0x61, 0x6e, 0x6b, 0x52, 0x08, 0x74, 0x65, 0x72,
	0x6d, 0x69, 0x6e, 0x61, 0x6c, 0x12, 0x2a, 0x0a, 0x11, 0x62, 0x61, 0x6e, 0x6b, 0x5f, 0x72, 0x65,
	0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0f, 0x62, 0x61, 0x6e, 0x6b, 0x52, 0x65, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x49,
	0x64, 0x12, 0x22, 0x0a, 0x0d, 0x62, 0x61, 0x6e, 0x6b, 0x5f, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x5f,
	0x69, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x62, 0x61, 0x6e, 0x6b, 0x4f, 0x72,
	0x64, 0x65, 0x72, 0x49, 0x64, 0x12, 0x50, 0x0a, 0x16, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63,
	0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18,
	0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d,
	0x70, 0x52, 0x14, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x43, 0x72,
	0x65, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x12, 0x2d, 0x0a, 0x12, 0x74, 0x72, 0x61, 0x6e, 0x73,
	0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x07, 0x20,
	0x01, 0x28, 0x01, 0x52, 0x11, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e,
	0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x22, 0xc0, 0x01, 0x0a, 0x0e, 0x52, 0x65, 0x66, 0x75, 0x6e,
	0x64, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x25, 0x0a, 0x0e, 0x74, 0x72, 0x61,
	0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x04, 0x52, 0x0d, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x64,
	0x12, 0x5b, 0x0a, 0x0d, 0x62, 0x61, 0x6e, 0x6b, 0x5f, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x36, 0x2e, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73,
	0x73, 0x69, 0x6e, 0x67, 0x2e, 0x6d, 0x75, 0x6c, 0x74, 0x69, 0x61, 0x63, 0x71, 0x75, 0x69, 0x72,
	0x69, 0x6e, 0x67, 0x2e, 0x6d, 0x75, 0x6c, 0x74, 0x69, 0x61, 0x63, 0x71, 0x75, 0x69, 0x72, 0x69,
	0x6e, 0x67, 0x2e, 0x42, 0x61, 0x6e, 0x6b, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x52,
	0x0c, 0x62, 0x61, 0x6e, 0x6b, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x2a, 0x0a,
	0x11, 0x62, 0x61, 0x6e, 0x6b, 0x5f, 0x72, 0x65, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x5f,
	0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f, 0x62, 0x61, 0x6e, 0x6b, 0x52, 0x65,
	0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x49, 0x64, 0x22, 0x5d, 0x0a, 0x08, 0x43, 0x61, 0x72,
	0x64, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x15, 0x0a, 0x06, 0x69, 0x70, 0x73, 0x5f, 0x69, 0x64, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x05, 0x69, 0x70, 0x73, 0x49, 0x64, 0x12, 0x1b, 0x0a, 0x09,
	0x69, 0x73, 0x73, 0x75, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x04, 0x52,
	0x08, 0x69, 0x73, 0x73, 0x75, 0x65, 0x72, 0x49, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x63, 0x6f, 0x75,
	0x6e, 0x74, 0x72, 0x79, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x04, 0x52, 0x09, 0x63,
	0x6f, 0x75, 0x6e, 0x74, 0x72, 0x79, 0x49, 0x64, 0x22, 0x4e, 0x0a, 0x0c, 0x4d, 0x65, 0x72, 0x63,
	0x68, 0x61, 0x6e, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x1d, 0x0a, 0x0a, 0x70, 0x72, 0x6f, 0x6a,
	0x65, 0x63, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x09, 0x70, 0x72,
	0x6f, 0x6a, 0x65, 0x63, 0x74, 0x49, 0x64, 0x12, 0x1f, 0x0a, 0x0b, 0x6d, 0x65, 0x72, 0x63, 0x68,
	0x61, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x04, 0x52, 0x0a, 0x6d, 0x65,
	0x72, 0x63, 0x68, 0x61, 0x6e, 0x74, 0x49, 0x64, 0x22, 0xbd, 0x06, 0x0a, 0x14, 0x47, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x50, 0x61, 0x79, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x44, 0x61, 0x74,
	0x61, 0x12, 0x25, 0x0a, 0x0e, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e,
	0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x0d, 0x74, 0x72, 0x61, 0x6e, 0x73,
	0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x12, 0x16, 0x0a, 0x06, 0x61, 0x6d, 0x6f, 0x75,
	0x6e, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x01, 0x52, 0x06, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74,
	0x12, 0x20, 0x0a, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69,
	0x6f, 0x6e, 0x12, 0x4d, 0x0a, 0x04, 0x63, 0x61, 0x72, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x39, 0x2e, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x2e, 0x6d, 0x75,
	0x6c, 0x74, 0x69, 0x61, 0x63, 0x71, 0x75, 0x69, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x6d, 0x75, 0x6c,
	0x74, 0x69, 0x61, 0x63, 0x71, 0x75, 0x69, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x43, 0x61, 0x72, 0x64,
	0x44, 0x61, 0x74, 0x61, 0x46, 0x6f, 0x72, 0x42, 0x61, 0x6e, 0x6b, 0x52, 0x04, 0x63, 0x61, 0x72,
	0x64, 0x12, 0x4d, 0x0a, 0x04, 0x75, 0x73, 0x65, 0x72, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x39, 0x2e, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x2e, 0x6d, 0x75, 0x6c,
	0x74, 0x69, 0x61, 0x63, 0x71, 0x75, 0x69, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x6d, 0x75, 0x6c, 0x74,
	0x69, 0x61, 0x63, 0x71, 0x75, 0x69, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x55, 0x73, 0x65, 0x72, 0x44,
	0x61, 0x74, 0x61, 0x46, 0x6f, 0x72, 0x42, 0x61, 0x6e, 0x6b, 0x52, 0x04, 0x75, 0x73, 0x65, 0x72,
	0x12, 0x59, 0x0a, 0x08, 0x74, 0x65, 0x72, 0x6d, 0x69, 0x6e, 0x61, 0x6c, 0x18, 0x06, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x3d, 0x2e, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x2e,
	0x6d, 0x75, 0x6c, 0x74, 0x69, 0x61, 0x63, 0x71, 0x75, 0x69, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x6d,
	0x75, 0x6c, 0x74, 0x69, 0x61, 0x63, 0x71, 0x75, 0x69, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x54, 0x65,
	0x72, 0x6d, 0x69, 0x6e, 0x61, 0x6c, 0x44, 0x61, 0x74, 0x61, 0x46, 0x6f, 0x72, 0x42, 0x61, 0x6e,
	0x6b, 0x52, 0x08, 0x74, 0x65, 0x72, 0x6d, 0x69, 0x6e, 0x61, 0x6c, 0x12, 0x4f, 0x0a, 0x09, 0x63,
	0x61, 0x72, 0x64, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x32,
	0x2e, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x2e, 0x6d, 0x75, 0x6c, 0x74,
	0x69, 0x61, 0x63, 0x71, 0x75, 0x69, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x6d, 0x75, 0x6c, 0x74, 0x69,
	0x61, 0x63, 0x71, 0x75, 0x69, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x43, 0x61, 0x72, 0x64, 0x49, 0x6e,
	0x66, 0x6f, 0x52, 0x08, 0x63, 0x61, 0x72, 0x64, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x5b, 0x0a, 0x0d,
	0x6d, 0x65, 0x72, 0x63, 0x68, 0x61, 0x6e, 0x74, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x08, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x36, 0x2e, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67,
	0x2e, 0x6d, 0x75, 0x6c, 0x74, 0x69, 0x61, 0x63, 0x71, 0x75, 0x69, 0x72, 0x69, 0x6e, 0x67, 0x2e,
	0x6d, 0x75, 0x6c, 0x74, 0x69, 0x61, 0x63, 0x71, 0x75, 0x69, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x4d,
	0x65, 0x72, 0x63, 0x68, 0x61, 0x6e, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x0c, 0x6d, 0x65, 0x72,
	0x63, 0x68, 0x61, 0x6e, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x2c, 0x0a, 0x12, 0x61, 0x67, 0x67,
	0x72, 0x65, 0x67, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x5f, 0x69, 0x64, 0x18,
	0x09, 0x20, 0x01, 0x28, 0x04, 0x52, 0x10, 0x61, 0x67, 0x67, 0x72, 0x65, 0x67, 0x61, 0x74, 0x65,
	0x64, 0x54, 0x79, 0x70, 0x65, 0x49, 0x64, 0x12, 0x14, 0x0a, 0x05, 0x74, 0x6f, 0x6b, 0x65, 0x6e,
	0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x12, 0x1a, 0x0a,
	0x08, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x63, 0x79, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x08, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x63, 0x79, 0x12, 0x23, 0x0a, 0x0d, 0x65, 0x63, 0x69,
	0x5f, 0x69, 0x6e, 0x64, 0x69, 0x63, 0x61, 0x74, 0x6f, 0x72, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0c, 0x65, 0x63, 0x69, 0x49, 0x6e, 0x64, 0x69, 0x63, 0x61, 0x74, 0x6f, 0x72, 0x12, 0x1e,
	0x0a, 0x0a, 0x63, 0x72, 0x79, 0x70, 0x74, 0x6f, 0x67, 0x72, 0x61, 0x6d, 0x18, 0x0d, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0a, 0x63, 0x72, 0x79, 0x70, 0x74, 0x6f, 0x67, 0x72, 0x61, 0x6d, 0x12, 0x50,
	0x0a, 0x16, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x63, 0x72,
	0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a,
	0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66,
	0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x14, 0x74, 0x72, 0x61, 0x6e,
	0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74,
	0x12, 0x26, 0x0a, 0x0f, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x69, 0x70, 0x5f, 0x61, 0x64, 0x64, 0x72,
	0x65, 0x73, 0x73, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x75, 0x73, 0x65, 0x72, 0x49,
	0x70, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x22, 0x8e, 0x04, 0x0a, 0x15, 0x47, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x50, 0x61, 0x79, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x44, 0x61,
	0x74, 0x61, 0x12, 0x25, 0x0a, 0x0e, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f,
	0x6e, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x0d, 0x74, 0x72, 0x61, 0x6e,
	0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x12, 0x2a, 0x0a, 0x11, 0x62, 0x61, 0x6e,
	0x6b, 0x5f, 0x72, 0x65, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0f, 0x62, 0x61, 0x6e, 0x6b, 0x52, 0x65, 0x66, 0x65, 0x72, 0x65,
	0x6e, 0x63, 0x65, 0x49, 0x64, 0x12, 0x6f, 0x0a, 0x12, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63,
	0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x0e, 0x32, 0x40, 0x2e, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x2e, 0x74,
	0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x74, 0x72, 0x61, 0x6e, 0x73,
	0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x2e, 0x45, 0x6e,
	0x75, 0x6d, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x52, 0x11, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e,
	0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x57, 0x0a, 0x07, 0x74, 0x68, 0x72, 0x65, 0x65, 0x44,
	0x53, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x3d, 0x2e, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73,
	0x73, 0x69, 0x6e, 0x67, 0x2e, 0x6d, 0x75, 0x6c, 0x74, 0x69, 0x61, 0x63, 0x71, 0x75, 0x69, 0x72,
	0x69, 0x6e, 0x67, 0x2e, 0x6d, 0x75, 0x6c, 0x74, 0x69, 0x61, 0x63, 0x71, 0x75, 0x69, 0x72, 0x69,
	0x6e, 0x67, 0x2e, 0x54, 0x68, 0x72, 0x65, 0x65, 0x44, 0x53, 0x44, 0x61, 0x74, 0x61, 0x46, 0x72,
	0x6f, 0x6d, 0x42, 0x61, 0x6e, 0x6b, 0x52, 0x07, 0x74, 0x68, 0x72, 0x65, 0x65, 0x44, 0x53, 0x12,
	0x5b, 0x0a, 0x0d, 0x62, 0x61, 0x6e, 0x6b, 0x5f, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x36, 0x2e, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73,
	0x69, 0x6e, 0x67, 0x2e, 0x6d, 0x75, 0x6c, 0x74, 0x69, 0x61, 0x63, 0x71, 0x75, 0x69, 0x72, 0x69,
	0x6e, 0x67, 0x2e, 0x6d, 0x75, 0x6c, 0x74, 0x69, 0x61, 0x63, 0x71, 0x75, 0x69, 0x72, 0x69, 0x6e,
	0x67, 0x2e, 0x42, 0x61, 0x6e, 0x6b, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x52, 0x0c,
	0x62, 0x61, 0x6e, 0x6b, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x22, 0x0a, 0x0d,
	0x62, 0x61, 0x6e, 0x6b, 0x5f, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x06, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0b, 0x62, 0x61, 0x6e, 0x6b, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x49, 0x64,
	0x12, 0x57, 0x0a, 0x0b, 0x66, 0x69, 0x6e, 0x67, 0x65, 0x72, 0x70, 0x72, 0x69, 0x6e, 0x74, 0x18,
	0x07, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x35, 0x2e, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69,
	0x6e, 0x67, 0x2e, 0x6d, 0x75, 0x6c, 0x74, 0x69, 0x61, 0x63, 0x71, 0x75, 0x69, 0x72, 0x69, 0x6e,
	0x67, 0x2e, 0x6d, 0x75, 0x6c, 0x74, 0x69, 0x61, 0x63, 0x71, 0x75, 0x69, 0x72, 0x69, 0x6e, 0x67,
	0x2e, 0x46, 0x69, 0x6e, 0x67, 0x65, 0x72, 0x70, 0x72, 0x69, 0x6e, 0x74, 0x52, 0x0b, 0x66, 0x69,
	0x6e, 0x67, 0x65, 0x72, 0x70, 0x72, 0x69, 0x6e, 0x74, 0x22, 0xb4, 0x06, 0x0a, 0x13, 0x41, 0x70,
	0x70, 0x6c, 0x65, 0x50, 0x61, 0x79, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x44, 0x61, 0x74,
	0x61, 0x12, 0x25, 0x0a, 0x0e, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e,
	0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x0d, 0x74, 0x72, 0x61, 0x6e, 0x73,
	0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x12, 0x17, 0x0a, 0x07, 0x69, 0x73, 0x5f, 0x68,
	0x6f, 0x6c, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x08, 0x52, 0x06, 0x69, 0x73, 0x48, 0x6f, 0x6c,
	0x64, 0x12, 0x16, 0x0a, 0x06, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x01, 0x52, 0x06, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x20, 0x0a, 0x0b, 0x64, 0x65, 0x73,
	0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b,
	0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x4d, 0x0a, 0x04, 0x63,
	0x61, 0x72, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x39, 0x2e, 0x70, 0x72, 0x6f, 0x63,
	0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x2e, 0x6d, 0x75, 0x6c, 0x74, 0x69, 0x61, 0x63, 0x71, 0x75,
	0x69, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x6d, 0x75, 0x6c, 0x74, 0x69, 0x61, 0x63, 0x71, 0x75, 0x69,
	0x72, 0x69, 0x6e, 0x67, 0x2e, 0x43, 0x61, 0x72, 0x64, 0x44, 0x61, 0x74, 0x61, 0x46, 0x6f, 0x72,
	0x42, 0x61, 0x6e, 0x6b, 0x52, 0x04, 0x63, 0x61, 0x72, 0x64, 0x12, 0x4d, 0x0a, 0x04, 0x75, 0x73,
	0x65, 0x72, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x39, 0x2e, 0x70, 0x72, 0x6f, 0x63, 0x65,
	0x73, 0x73, 0x69, 0x6e, 0x67, 0x2e, 0x6d, 0x75, 0x6c, 0x74, 0x69, 0x61, 0x63, 0x71, 0x75, 0x69,
	0x72, 0x69, 0x6e, 0x67, 0x2e, 0x6d, 0x75, 0x6c, 0x74, 0x69, 0x61, 0x63, 0x71, 0x75, 0x69, 0x72,
	0x69, 0x6e, 0x67, 0x2e, 0x55, 0x73, 0x65, 0x72, 0x44, 0x61, 0x74, 0x61, 0x46, 0x6f, 0x72, 0x42,
	0x61, 0x6e, 0x6b, 0x52, 0x04, 0x75, 0x73, 0x65, 0x72, 0x12, 0x59, 0x0a, 0x08, 0x74, 0x65, 0x72,
	0x6d, 0x69, 0x6e, 0x61, 0x6c, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x3d, 0x2e, 0x70, 0x72,
	0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x2e, 0x6d, 0x75, 0x6c, 0x74, 0x69, 0x61, 0x63,
	0x71, 0x75, 0x69, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x6d, 0x75, 0x6c, 0x74, 0x69, 0x61, 0x63, 0x71,
	0x75, 0x69, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x54, 0x65, 0x72, 0x6d, 0x69, 0x6e, 0x61, 0x6c, 0x44,
	0x61, 0x74, 0x61, 0x46, 0x6f, 0x72, 0x42, 0x61, 0x6e, 0x6b, 0x52, 0x08, 0x74, 0x65, 0x72, 0x6d,
	0x69, 0x6e, 0x61, 0x6c, 0x12, 0x2e, 0x0a, 0x13, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74,
	0x69, 0x6f, 0x6e, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x08, 0x20, 0x01, 0x28,
	0x04, 0x52, 0x11, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x79,
	0x70, 0x65, 0x49, 0x64, 0x12, 0x4f, 0x0a, 0x09, 0x63, 0x61, 0x72, 0x64, 0x5f, 0x69, 0x6e, 0x66,
	0x6f, 0x18, 0x09, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x32, 0x2e, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73,
	0x73, 0x69, 0x6e, 0x67, 0x2e, 0x6d, 0x75, 0x6c, 0x74, 0x69, 0x61, 0x63, 0x71, 0x75, 0x69, 0x72,
	0x69, 0x6e, 0x67, 0x2e, 0x6d, 0x75, 0x6c, 0x74, 0x69, 0x61, 0x63, 0x71, 0x75, 0x69, 0x72, 0x69,
	0x6e, 0x67, 0x2e, 0x43, 0x61, 0x72, 0x64, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x08, 0x63, 0x61, 0x72,
	0x64, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x5b, 0x0a, 0x0d, 0x6d, 0x65, 0x72, 0x63, 0x68, 0x61, 0x6e,
	0x74, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x36, 0x2e, 0x70,
	0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x2e, 0x6d, 0x75, 0x6c, 0x74, 0x69, 0x61,
	0x63, 0x71, 0x75, 0x69, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x6d, 0x75, 0x6c, 0x74, 0x69, 0x61, 0x63,
	0x71, 0x75, 0x69, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x4d, 0x65, 0x72, 0x63, 0x68, 0x61, 0x6e, 0x74,
	0x49, 0x6e, 0x66, 0x6f, 0x52, 0x0c, 0x6d, 0x65, 0x72, 0x63, 0x68, 0x61, 0x6e, 0x74, 0x49, 0x6e,
	0x66, 0x6f, 0x12, 0x12, 0x0a, 0x04, 0x54, 0x61, 0x76, 0x76, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x0c,
	0x52, 0x04, 0x54, 0x61, 0x76, 0x76, 0x12, 0x10, 0x0a, 0x03, 0x45, 0x63, 0x69, 0x18, 0x0c, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x03, 0x45, 0x63, 0x69, 0x12, 0x50, 0x0a, 0x16, 0x74, 0x72, 0x61, 0x6e,
	0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f,
	0x61, 0x74, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73,
	0x74, 0x61, 0x6d, 0x70, 0x52, 0x14, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f,
	0x6e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x12, 0x2c, 0x0a, 0x12, 0x61, 0x67,
	0x67, 0x72, 0x65, 0x67, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x5f, 0x69, 0x64,
	0x18, 0x0e, 0x20, 0x01, 0x28, 0x04, 0x52, 0x10, 0x61, 0x67, 0x67, 0x72, 0x65, 0x67, 0x61, 0x74,
	0x65, 0x64, 0x54, 0x79, 0x70, 0x65, 0x49, 0x64, 0x12, 0x26, 0x0a, 0x0f, 0x75, 0x73, 0x65, 0x72,
	0x5f, 0x69, 0x70, 0x5f, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x18, 0x0f, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0d, 0x75, 0x73, 0x65, 0x72, 0x49, 0x70, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73,
	0x22, 0xcd, 0x03, 0x0a, 0x14, 0x41, 0x70, 0x70, 0x6c, 0x65, 0x50, 0x61, 0x79, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x44, 0x61, 0x74, 0x61, 0x12, 0x25, 0x0a, 0x0e, 0x74, 0x72, 0x61,
	0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x04, 0x52, 0x0d, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x64,
	0x12, 0x2a, 0x0a, 0x11, 0x62, 0x61, 0x6e, 0x6b, 0x5f, 0x72, 0x65, 0x66, 0x65, 0x72, 0x65, 0x6e,
	0x63, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f, 0x62, 0x61, 0x6e,
	0x6b, 0x52, 0x65, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x49, 0x64, 0x12, 0x6f, 0x0a, 0x12,
	0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x73, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x40, 0x2e, 0x70, 0x72, 0x6f, 0x63, 0x65,
	0x73, 0x73, 0x69, 0x6e, 0x67, 0x2e, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f,
	0x6e, 0x2e, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x73, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x2e, 0x45, 0x6e, 0x75, 0x6d, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63,
	0x74, 0x69, 0x6f, 0x6e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x11, 0x74, 0x72, 0x61, 0x6e,
	0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x17, 0x0a,
	0x07, 0x63, 0x61, 0x72, 0x64, 0x5f, 0x69, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06,
	0x63, 0x61, 0x72, 0x64, 0x49, 0x64, 0x12, 0x57, 0x0a, 0x07, 0x74, 0x68, 0x72, 0x65, 0x65, 0x44,
	0x53, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x3d, 0x2e, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73,
	0x73, 0x69, 0x6e, 0x67, 0x2e, 0x6d, 0x75, 0x6c, 0x74, 0x69, 0x61, 0x63, 0x71, 0x75, 0x69, 0x72,
	0x69, 0x6e, 0x67, 0x2e, 0x6d, 0x75, 0x6c, 0x74, 0x69, 0x61, 0x63, 0x71, 0x75, 0x69, 0x72, 0x69,
	0x6e, 0x67, 0x2e, 0x54, 0x68, 0x72, 0x65, 0x65, 0x44, 0x53, 0x44, 0x61, 0x74, 0x61, 0x46, 0x72,
	0x6f, 0x6d, 0x42, 0x61, 0x6e, 0x6b, 0x52, 0x07, 0x74, 0x68, 0x72, 0x65, 0x65, 0x44, 0x53, 0x12,
	0x5b, 0x0a, 0x0d, 0x62, 0x61, 0x6e, 0x6b, 0x5f, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x36, 0x2e, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73,
	0x69, 0x6e, 0x67, 0x2e, 0x6d, 0x75, 0x6c, 0x74, 0x69, 0x61, 0x63, 0x71, 0x75, 0x69, 0x72, 0x69,
	0x6e, 0x67, 0x2e, 0x6d, 0x75, 0x6c, 0x74, 0x69, 0x61, 0x63, 0x71, 0x75, 0x69, 0x72, 0x69, 0x6e,
	0x67, 0x2e, 0x42, 0x61, 0x6e, 0x6b, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x52, 0x0c,
	0x62, 0x61, 0x6e, 0x6b, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x22, 0x0a, 0x0d,
	0x62, 0x61, 0x6e, 0x6b, 0x5f, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x07, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0b, 0x62, 0x61, 0x6e, 0x6b, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x49, 0x64,
	0x22, 0x95, 0x05, 0x0a, 0x14, 0x54, 0x77, 0x6f, 0x53, 0x74, 0x61, 0x67, 0x65, 0x50, 0x61, 0x79,
	0x49, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x25, 0x0a, 0x0e, 0x74, 0x72, 0x61,
	0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x04, 0x52, 0x0d, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x64,
	0x12, 0x17, 0x0a, 0x07, 0x69, 0x73, 0x5f, 0x68, 0x6f, 0x6c, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x08, 0x52, 0x06, 0x69, 0x73, 0x48, 0x6f, 0x6c, 0x64, 0x12, 0x16, 0x0a, 0x06, 0x61, 0x6d, 0x6f,
	0x75, 0x6e, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x01, 0x52, 0x06, 0x61, 0x6d, 0x6f, 0x75, 0x6e,
	0x74, 0x12, 0x20, 0x0a, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74,
	0x69, 0x6f, 0x6e, 0x12, 0x4d, 0x0a, 0x04, 0x63, 0x61, 0x72, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x39, 0x2e, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x2e, 0x6d,
	0x75, 0x6c, 0x74, 0x69, 0x61, 0x63, 0x71, 0x75, 0x69, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x6d, 0x75,
	0x6c, 0x74, 0x69, 0x61, 0x63, 0x71, 0x75, 0x69, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x43, 0x61, 0x72,
	0x64, 0x44, 0x61, 0x74, 0x61, 0x46, 0x6f, 0x72, 0x42, 0x61, 0x6e, 0x6b, 0x52, 0x04, 0x63, 0x61,
	0x72, 0x64, 0x12, 0x4d, 0x0a, 0x04, 0x75, 0x73, 0x65, 0x72, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x39, 0x2e, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x2e, 0x6d, 0x75,
	0x6c, 0x74, 0x69, 0x61, 0x63, 0x71, 0x75, 0x69, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x6d, 0x75, 0x6c,
	0x74, 0x69, 0x61, 0x63, 0x71, 0x75, 0x69, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x55, 0x73, 0x65, 0x72,
	0x44, 0x61, 0x74, 0x61, 0x46, 0x6f, 0x72, 0x42, 0x61, 0x6e, 0x6b, 0x52, 0x04, 0x75, 0x73, 0x65,
	0x72, 0x12, 0x59, 0x0a, 0x08, 0x74, 0x65, 0x72, 0x6d, 0x69, 0x6e, 0x61, 0x6c, 0x18, 0x07, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x3d, 0x2e, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67,
	0x2e, 0x6d, 0x75, 0x6c, 0x74, 0x69, 0x61, 0x63, 0x71, 0x75, 0x69, 0x72, 0x69, 0x6e, 0x67, 0x2e,
	0x6d, 0x75, 0x6c, 0x74, 0x69, 0x61, 0x63, 0x71, 0x75, 0x69, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x54,
	0x65, 0x72, 0x6d, 0x69, 0x6e, 0x61, 0x6c, 0x44, 0x61, 0x74, 0x61, 0x46, 0x6f, 0x72, 0x42, 0x61,
	0x6e, 0x6b, 0x52, 0x08, 0x74, 0x65, 0x72, 0x6d, 0x69, 0x6e, 0x61, 0x6c, 0x12, 0x2e, 0x0a, 0x13,
	0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x74, 0x79, 0x70, 0x65,
	0x5f, 0x69, 0x64, 0x18, 0x08, 0x20, 0x01, 0x28, 0x04, 0x52, 0x11, 0x74, 0x72, 0x61, 0x6e, 0x73,
	0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x49, 0x64, 0x12, 0x4f, 0x0a, 0x09,
	0x63, 0x61, 0x72, 0x64, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x09, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x32, 0x2e, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x2e, 0x6d, 0x75, 0x6c,
	0x74, 0x69, 0x61, 0x63, 0x71, 0x75, 0x69, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x6d, 0x75, 0x6c, 0x74,
	0x69, 0x61, 0x63, 0x71, 0x75, 0x69, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x43, 0x61, 0x72, 0x64, 0x49,
	0x6e, 0x66, 0x6f, 0x52, 0x08, 0x63, 0x61, 0x72, 0x64, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x5b, 0x0a,
	0x0d, 0x6d, 0x65, 0x72, 0x63, 0x68, 0x61, 0x6e, 0x74, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x0a,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x36, 0x2e, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e,
	0x67, 0x2e, 0x6d, 0x75, 0x6c, 0x74, 0x69, 0x61, 0x63, 0x71, 0x75, 0x69, 0x72, 0x69, 0x6e, 0x67,
	0x2e, 0x6d, 0x75, 0x6c, 0x74, 0x69, 0x61, 0x63, 0x71, 0x75, 0x69, 0x72, 0x69, 0x6e, 0x67, 0x2e,
	0x4d, 0x65, 0x72, 0x63, 0x68, 0x61, 0x6e, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x0c, 0x6d, 0x65,
	0x72, 0x63, 0x68, 0x61, 0x6e, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x2c, 0x0a, 0x12, 0x61, 0x67,
	0x67, 0x72, 0x65, 0x67, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x5f, 0x69, 0x64,
	0x18, 0x0b, 0x20, 0x01, 0x28, 0x04, 0x52, 0x10, 0x61, 0x67, 0x67, 0x72, 0x65, 0x67, 0x61, 0x74,
	0x65, 0x64, 0x54, 0x79, 0x70, 0x65, 0x49, 0x64, 0x22, 0xcb, 0x02, 0x0a, 0x0d, 0x43, 0x68, 0x61,
	0x72, 0x67, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x25, 0x0a, 0x0e, 0x74, 0x72,
	0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x04, 0x52, 0x0d, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x49,
	0x64, 0x12, 0x16, 0x0a, 0x06, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x01, 0x52, 0x06, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x59, 0x0a, 0x08, 0x74, 0x65, 0x72,
	0x6d, 0x69, 0x6e, 0x61, 0x6c, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x3d, 0x2e, 0x70, 0x72,
	0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x2e, 0x6d, 0x75, 0x6c, 0x74, 0x69, 0x61, 0x63,
	0x71, 0x75, 0x69, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x6d, 0x75, 0x6c, 0x74, 0x69, 0x61, 0x63, 0x71,
	0x75, 0x69, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x54, 0x65, 0x72, 0x6d, 0x69, 0x6e, 0x61, 0x6c, 0x44,
	0x61, 0x74, 0x61, 0x46, 0x6f, 0x72, 0x42, 0x61, 0x6e, 0x6b, 0x52, 0x08, 0x74, 0x65, 0x72, 0x6d,
	0x69, 0x6e, 0x61, 0x6c, 0x12, 0x2a, 0x0a, 0x11, 0x62, 0x61, 0x6e, 0x6b, 0x5f, 0x72, 0x65, 0x66,
	0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0f, 0x62, 0x61, 0x6e, 0x6b, 0x52, 0x65, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x49, 0x64,
	0x12, 0x22, 0x0a, 0x0d, 0x62, 0x61, 0x6e, 0x6b, 0x5f, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x5f, 0x69,
	0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x62, 0x61, 0x6e, 0x6b, 0x4f, 0x72, 0x64,
	0x65, 0x72, 0x49, 0x64, 0x12, 0x50, 0x0a, 0x16, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74,
	0x69, 0x6f, 0x6e, 0x5f, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x06,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70,
	0x52, 0x14, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x43, 0x72, 0x65,
	0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x22, 0xed, 0x02, 0x0a, 0x0d, 0x43, 0x61, 0x6e, 0x63, 0x65,
	0x6c, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x25, 0x0a, 0x0e, 0x74, 0x72, 0x61, 0x6e,
	0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04,
	0x52, 0x0d, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x12,
	0x16, 0x0a, 0x06, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x01, 0x52,
	0x06, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x59, 0x0a, 0x08, 0x74, 0x65, 0x72, 0x6d, 0x69,
	0x6e, 0x61, 0x6c, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x3d, 0x2e, 0x70, 0x72, 0x6f, 0x63,
	0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x2e, 0x6d, 0x75, 0x6c, 0x74, 0x69, 0x61, 0x63, 0x71, 0x75,
	0x69, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x6d, 0x75, 0x6c, 0x74, 0x69, 0x61, 0x63, 0x71, 0x75, 0x69,
	0x72, 0x69, 0x6e, 0x67, 0x2e, 0x54, 0x65, 0x72, 0x6d, 0x69, 0x6e, 0x61, 0x6c, 0x44, 0x61, 0x74,
	0x61, 0x46, 0x6f, 0x72, 0x42, 0x61, 0x6e, 0x6b, 0x52, 0x08, 0x74, 0x65, 0x72, 0x6d, 0x69, 0x6e,
	0x61, 0x6c, 0x12, 0x2a, 0x0a, 0x11, 0x62, 0x61, 0x6e, 0x6b, 0x5f, 0x72, 0x65, 0x66, 0x65, 0x72,
	0x65, 0x6e, 0x63, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f, 0x62,
	0x61, 0x6e, 0x6b, 0x52, 0x65, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x49, 0x64, 0x12, 0x22,
	0x0a, 0x0d, 0x62, 0x61, 0x6e, 0x6b, 0x5f, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18,
	0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x62, 0x61, 0x6e, 0x6b, 0x4f, 0x72, 0x64, 0x65, 0x72,
	0x49, 0x64, 0x12, 0x20, 0x0a, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f,
	0x6e, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70,
	0x74, 0x69, 0x6f, 0x6e, 0x12, 0x50, 0x0a, 0x16, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74,
	0x69, 0x6f, 0x6e, 0x5f, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x07,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70,
	0x52, 0x14, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x43, 0x72, 0x65,
	0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x22, 0xce, 0x03, 0x0a, 0x15, 0x54, 0x77, 0x6f, 0x53, 0x74,
	0x61, 0x67, 0x65, 0x50, 0x61, 0x79, 0x49, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x12, 0x25, 0x0a, 0x0e, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x5f,
	0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x0d, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61,
	0x63, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x12, 0x2a, 0x0a, 0x11, 0x62, 0x61, 0x6e, 0x6b, 0x5f,
	0x72, 0x65, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0f, 0x62, 0x61, 0x6e, 0x6b, 0x52, 0x65, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63,
	0x65, 0x49, 0x64, 0x12, 0x6f, 0x0a, 0x12, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69,
	0x6f, 0x6e, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0e, 0x32,
	0x40, 0x2e, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x2e, 0x74, 0x72, 0x61,
	0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63,
	0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x2e, 0x45, 0x6e, 0x75, 0x6d,
	0x54, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x52, 0x11, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x12, 0x17, 0x0a, 0x07, 0x63, 0x61, 0x72, 0x64, 0x5f, 0x69, 0x64, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x63, 0x61, 0x72, 0x64, 0x49, 0x64, 0x12, 0x57, 0x0a,
	0x07, 0x74, 0x68, 0x72, 0x65, 0x65, 0x44, 0x53, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x3d,
	0x2e, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x2e, 0x6d, 0x75, 0x6c, 0x74,
	0x69, 0x61, 0x63, 0x71, 0x75, 0x69, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x6d, 0x75, 0x6c, 0x74, 0x69,
	0x61, 0x63, 0x71, 0x75, 0x69, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x54, 0x68, 0x72, 0x65, 0x65, 0x44,
	0x53, 0x44, 0x61, 0x74, 0x61, 0x46, 0x72, 0x6f, 0x6d, 0x42, 0x61, 0x6e, 0x6b, 0x52, 0x07, 0x74,
	0x68, 0x72, 0x65, 0x65, 0x44, 0x53, 0x12, 0x5b, 0x0a, 0x0d, 0x62, 0x61, 0x6e, 0x6b, 0x5f, 0x72,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x36, 0x2e,
	0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x2e, 0x6d, 0x75, 0x6c, 0x74, 0x69,
	0x61, 0x63, 0x71, 0x75, 0x69, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x6d, 0x75, 0x6c, 0x74, 0x69, 0x61,
	0x63, 0x71, 0x75, 0x69, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x42, 0x61, 0x6e, 0x6b, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x52, 0x0c, 0x62, 0x61, 0x6e, 0x6b, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x12, 0x22, 0x0a, 0x0d, 0x62, 0x61, 0x6e, 0x6b, 0x5f, 0x6f, 0x72, 0x64, 0x65,
	0x72, 0x5f, 0x69, 0x64, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x62, 0x61, 0x6e, 0x6b,
	0x4f, 0x72, 0x64, 0x65, 0x72, 0x49, 0x64, 0x22, 0xe4, 0x01, 0x0a, 0x0e, 0x43, 0x68, 0x61, 0x72,
	0x67, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x25, 0x0a, 0x0e, 0x74, 0x72,
	0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x04, 0x52, 0x0d, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x49,
	0x64, 0x12, 0x2a, 0x0a, 0x11, 0x62, 0x61, 0x6e, 0x6b, 0x5f, 0x72, 0x65, 0x66, 0x65, 0x72, 0x65,
	0x6e, 0x63, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f, 0x62, 0x61,
	0x6e, 0x6b, 0x52, 0x65, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x49, 0x64, 0x12, 0x22, 0x0a,
	0x0d, 0x62, 0x61, 0x6e, 0x6b, 0x5f, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x62, 0x61, 0x6e, 0x6b, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x49,
	0x64, 0x12, 0x5b, 0x0a, 0x0d, 0x62, 0x61, 0x6e, 0x6b, 0x5f, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x36, 0x2e, 0x70, 0x72, 0x6f, 0x63, 0x65,
	0x73, 0x73, 0x69, 0x6e, 0x67, 0x2e, 0x6d, 0x75, 0x6c, 0x74, 0x69, 0x61, 0x63, 0x71, 0x75, 0x69,
	0x72, 0x69, 0x6e, 0x67, 0x2e, 0x6d, 0x75, 0x6c, 0x74, 0x69, 0x61, 0x63, 0x71, 0x75, 0x69, 0x72,
	0x69, 0x6e, 0x67, 0x2e, 0x42, 0x61, 0x6e, 0x6b, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x52, 0x0c, 0x62, 0x61, 0x6e, 0x6b, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0xe4,
	0x01, 0x0a, 0x0e, 0x43, 0x61, 0x6e, 0x63, 0x65, 0x6c, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x12, 0x25, 0x0a, 0x0e, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e,
	0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x0d, 0x74, 0x72, 0x61, 0x6e, 0x73,
	0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x12, 0x2a, 0x0a, 0x11, 0x62, 0x61, 0x6e, 0x6b,
	0x5f, 0x72, 0x65, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0f, 0x62, 0x61, 0x6e, 0x6b, 0x52, 0x65, 0x66, 0x65, 0x72, 0x65, 0x6e,
	0x63, 0x65, 0x49, 0x64, 0x12, 0x22, 0x0a, 0x0d, 0x62, 0x61, 0x6e, 0x6b, 0x5f, 0x6f, 0x72, 0x64,
	0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x62, 0x61, 0x6e,
	0x6b, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x49, 0x64, 0x12, 0x5b, 0x0a, 0x0d, 0x62, 0x61, 0x6e, 0x6b,
	0x5f, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x36, 0x2e, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x2e, 0x6d, 0x75, 0x6c,
	0x74, 0x69, 0x61, 0x63, 0x71, 0x75, 0x69, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x6d, 0x75, 0x6c, 0x74,
	0x69, 0x61, 0x63, 0x71, 0x75, 0x69, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x42, 0x61, 0x6e, 0x6b, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x52, 0x0c, 0x62, 0x61, 0x6e, 0x6b, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x4d, 0x0a, 0x0b, 0x46, 0x69, 0x6e, 0x67, 0x65, 0x72, 0x70,
	0x72, 0x69, 0x6e, 0x74, 0x12, 0x1d, 0x0a, 0x0a, 0x6d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x5f, 0x75,
	0x72, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x6d, 0x65, 0x74, 0x68, 0x6f, 0x64,
	0x55, 0x72, 0x6c, 0x12, 0x1f, 0x0a, 0x0b, 0x6d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x5f, 0x64, 0x61,
	0x74, 0x61, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x6d, 0x65, 0x74, 0x68, 0x6f, 0x64,
	0x44, 0x61, 0x74, 0x61, 0x22, 0x81, 0x02, 0x0a, 0x14, 0x54, 0x68, 0x72, 0x65, 0x65, 0x44, 0x53,
	0x52, 0x65, 0x73, 0x75, 0x6d, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x25, 0x0a,
	0x0e, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x69, 0x64, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x0d, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69,
	0x6f, 0x6e, 0x49, 0x64, 0x12, 0x2a, 0x0a, 0x11, 0x62, 0x61, 0x6e, 0x6b, 0x5f, 0x72, 0x65, 0x66,
	0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0f, 0x62, 0x61, 0x6e, 0x6b, 0x52, 0x65, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x49, 0x64,
	0x12, 0x59, 0x0a, 0x08, 0x74, 0x65, 0x72, 0x6d, 0x69, 0x6e, 0x61, 0x6c, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x3d, 0x2e, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x2e,
	0x6d, 0x75, 0x6c, 0x74, 0x69, 0x61, 0x63, 0x71, 0x75, 0x69, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x6d,
	0x75, 0x6c, 0x74, 0x69, 0x61, 0x63, 0x71, 0x75, 0x69, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x54, 0x65,
	0x72, 0x6d, 0x69, 0x6e, 0x61, 0x6c, 0x44, 0x61, 0x74, 0x61, 0x46, 0x6f, 0x72, 0x42, 0x61, 0x6e,
	0x6b, 0x52, 0x08, 0x74, 0x65, 0x72, 0x6d, 0x69, 0x6e, 0x61, 0x6c, 0x12, 0x22, 0x0a, 0x0d, 0x62,
	0x61, 0x6e, 0x6b, 0x5f, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0b, 0x62, 0x61, 0x6e, 0x6b, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x49, 0x64, 0x12,
	0x17, 0x0a, 0x07, 0x69, 0x73, 0x5f, 0x68, 0x6f, 0x6c, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x08,
	0x52, 0x06, 0x69, 0x73, 0x48, 0x6f, 0x6c, 0x64, 0x22, 0xd4, 0x03, 0x0a, 0x15, 0x54, 0x68, 0x72,
	0x65, 0x65, 0x44, 0x53, 0x52, 0x65, 0x73, 0x75, 0x6d, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x12, 0x25, 0x0a, 0x0e, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f,
	0x6e, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x0d, 0x74, 0x72, 0x61, 0x6e,
	0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x12, 0x22, 0x0a, 0x0d, 0x62, 0x61, 0x6e,
	0x6b, 0x5f, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0b, 0x62, 0x61, 0x6e, 0x6b, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x49, 0x64, 0x12, 0x2a, 0x0a,
	0x11, 0x62, 0x61, 0x6e, 0x6b, 0x5f, 0x72, 0x65, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x5f,
	0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f, 0x62, 0x61, 0x6e, 0x6b, 0x52, 0x65,
	0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x49, 0x64, 0x12, 0x6f, 0x0a, 0x12, 0x74, 0x72, 0x61,
	0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x40, 0x2e, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69,
	0x6e, 0x67, 0x2e, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x74,
	0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x2e, 0x45, 0x6e, 0x75, 0x6d, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f,
	0x6e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x11, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63,
	0x74, 0x69, 0x6f, 0x6e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x1d, 0x0a, 0x0a, 0x63, 0x61,
	0x72, 0x64, 0x5f, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09,
	0x63, 0x61, 0x72, 0x64, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x12, 0x57, 0x0a, 0x07, 0x74, 0x68, 0x72,
	0x65, 0x65, 0x44, 0x53, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x3d, 0x2e, 0x70, 0x72, 0x6f,
	0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x2e, 0x6d, 0x75, 0x6c, 0x74, 0x69, 0x61, 0x63, 0x71,
	0x75, 0x69, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x6d, 0x75, 0x6c, 0x74, 0x69, 0x61, 0x63, 0x71, 0x75,
	0x69, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x54, 0x68, 0x72, 0x65, 0x65, 0x44, 0x53, 0x44, 0x61, 0x74,
	0x61, 0x46, 0x72, 0x6f, 0x6d, 0x42, 0x61, 0x6e, 0x6b, 0x52, 0x07, 0x74, 0x68, 0x72, 0x65, 0x65,
	0x44, 0x53, 0x12, 0x5b, 0x0a, 0x0d, 0x62, 0x61, 0x6e, 0x6b, 0x5f, 0x72, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x36, 0x2e, 0x70, 0x72, 0x6f, 0x63,
	0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x2e, 0x6d, 0x75, 0x6c, 0x74, 0x69, 0x61, 0x63, 0x71, 0x75,
	0x69, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x6d, 0x75, 0x6c, 0x74, 0x69, 0x61, 0x63, 0x71, 0x75, 0x69,
	0x72, 0x69, 0x6e, 0x67, 0x2e, 0x42, 0x61, 0x6e, 0x6b, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x52, 0x0c, 0x62, 0x61, 0x6e, 0x6b, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22,
	0x98, 0x01, 0x0a, 0x0c, 0x42, 0x61, 0x6e, 0x6b, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x12, 0x18, 0x0a, 0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x6f,
	0x64, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x12, 0x29,
	0x0a, 0x10, 0x69, 0x6e, 0x74, 0x65, 0x67, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x63, 0x6f,
	0x64, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f, 0x69, 0x6e, 0x74, 0x65, 0x67, 0x72,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x2f, 0x0a, 0x13, 0x69, 0x6e, 0x74,
	0x65, 0x67, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x12, 0x69, 0x6e, 0x74, 0x65, 0x67, 0x72, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x22, 0x97, 0x01, 0x0a, 0x17, 0x52,
	0x65, 0x73, 0x6f, 0x6c, 0x76, 0x65, 0x56, 0x69, 0x73, 0x61, 0x41, 0x6c, 0x69, 0x61, 0x73, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x21, 0x0a, 0x0c, 0x70, 0x68, 0x6f, 0x6e, 0x65, 0x5f,
	0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x70, 0x68,
	0x6f, 0x6e, 0x65, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x12, 0x59, 0x0a, 0x08, 0x74, 0x65, 0x72,
	0x6d, 0x69, 0x6e, 0x61, 0x6c, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x3d, 0x2e, 0x70, 0x72,
	0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x2e, 0x6d, 0x75, 0x6c, 0x74, 0x69, 0x61, 0x63,
	0x71, 0x75, 0x69, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x6d, 0x75, 0x6c, 0x74, 0x69, 0x61, 0x63, 0x71,
	0x75, 0x69, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x54, 0x65, 0x72, 0x6d, 0x69, 0x6e, 0x61, 0x6c, 0x44,
	0x61, 0x74, 0x61, 0x46, 0x6f, 0x72, 0x42, 0x61, 0x6e, 0x6b, 0x52, 0x08, 0x74, 0x65, 0x72, 0x6d,
	0x69, 0x6e, 0x61, 0x6c, 0x22, 0x86, 0x02, 0x0a, 0x18, 0x52, 0x65, 0x73, 0x6f, 0x6c, 0x76, 0x65,
	0x56, 0x69, 0x73, 0x61, 0x41, 0x6c, 0x69, 0x61, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x12, 0x1f, 0x0a, 0x0b, 0x69, 0x73, 0x73, 0x75, 0x65, 0x72, 0x5f, 0x6e, 0x61, 0x6d, 0x65,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x69, 0x73, 0x73, 0x75, 0x65, 0x72, 0x4e, 0x61,
	0x6d, 0x65, 0x12, 0x25, 0x0a, 0x0e, 0x72, 0x65, 0x63, 0x69, 0x70, 0x69, 0x65, 0x6e, 0x74, 0x5f,
	0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x72, 0x65, 0x63, 0x69,
	0x70, 0x69, 0x65, 0x6e, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1b, 0x0a, 0x09, 0x63, 0x61, 0x72,
	0x64, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x63, 0x61,
	0x72, 0x64, 0x54, 0x79, 0x70, 0x65, 0x12, 0x5b, 0x0a, 0x0d, 0x62, 0x61, 0x6e, 0x6b, 0x5f, 0x72,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x36, 0x2e,
	0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x2e, 0x6d, 0x75, 0x6c, 0x74, 0x69,
	0x61, 0x63, 0x71, 0x75, 0x69, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x6d, 0x75, 0x6c, 0x74, 0x69, 0x61,
	0x63, 0x71, 0x75, 0x69, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x42, 0x61, 0x6e, 0x6b, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x52, 0x0c, 0x62, 0x61, 0x6e, 0x6b, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x12, 0x28, 0x0a, 0x10, 0x76, 0x69, 0x73, 0x61, 0x5f, 0x61, 0x6c, 0x69, 0x61,
	0x73, 0x5f, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x76,
	0x69, 0x73, 0x61, 0x41, 0x6c, 0x69, 0x61, 0x73, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x32, 0xc8, 0x13,
	0x0a, 0x0e, 0x4d, 0x75, 0x6c, 0x74, 0x69, 0x41, 0x63, 0x71, 0x75, 0x69, 0x72, 0x69, 0x6e, 0x67,
	0x12, 0x82, 0x01, 0x0a, 0x05, 0x50, 0x61, 0x79, 0x49, 0x6e, 0x12, 0x3a, 0x2e, 0x70, 0x72, 0x6f,
	0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x2e, 0x6d, 0x75, 0x6c, 0x74, 0x69, 0x61, 0x63, 0x71,
	0x75, 0x69, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x6d, 0x75, 0x6c, 0x74, 0x69, 0x61, 0x63, 0x71, 0x75,
	0x69, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x50, 0x61, 0x79, 0x49, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x44, 0x61, 0x74, 0x61, 0x1a, 0x3b, 0x2e, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73,
	0x69, 0x6e, 0x67, 0x2e, 0x6d, 0x75, 0x6c, 0x74, 0x69, 0x61, 0x63, 0x71, 0x75, 0x69, 0x72, 0x69,
	0x6e, 0x67, 0x2e, 0x6d, 0x75, 0x6c, 0x74, 0x69, 0x61, 0x63, 0x71, 0x75, 0x69, 0x72, 0x69, 0x6e,
	0x67, 0x2e, 0x50, 0x61, 0x79, 0x49, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x44,
	0x61, 0x74, 0x61, 0x22, 0x00, 0x12, 0x92, 0x01, 0x0a, 0x0d, 0x4f, 0x6e, 0x65, 0x43, 0x6c, 0x69,
	0x63, 0x6b, 0x50, 0x61, 0x79, 0x49, 0x6e, 0x12, 0x42, 0x2e, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73,
	0x73, 0x69, 0x6e, 0x67, 0x2e, 0x6d, 0x75, 0x6c, 0x74, 0x69, 0x61, 0x63, 0x71, 0x75, 0x69, 0x72,
	0x69, 0x6e, 0x67, 0x2e, 0x6d, 0x75, 0x6c, 0x74, 0x69, 0x61, 0x63, 0x71, 0x75, 0x69, 0x72, 0x69,
	0x6e, 0x67, 0x2e, 0x4f, 0x6e, 0x65, 0x43, 0x6c, 0x69, 0x63, 0x6b, 0x50, 0x61, 0x79, 0x49, 0x6e,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x44, 0x61, 0x74, 0x61, 0x1a, 0x3b, 0x2e, 0x70, 0x72,
	0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x2e, 0x6d, 0x75, 0x6c, 0x74, 0x69, 0x61, 0x63,
	0x71, 0x75, 0x69, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x6d, 0x75, 0x6c, 0x74, 0x69, 0x61, 0x63, 0x71,
	0x75, 0x69, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x50, 0x61, 0x79, 0x49, 0x6e, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x44, 0x61, 0x74, 0x61, 0x22, 0x00, 0x12, 0x8f, 0x01, 0x0a, 0x0e, 0x54,
	0x68, 0x72, 0x65, 0x65, 0x44, 0x53, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x72, 0x6d, 0x12, 0x3c, 0x2e,
	0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x2e, 0x6d, 0x75, 0x6c, 0x74, 0x69,
	0x61, 0x63, 0x71, 0x75, 0x69, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x6d, 0x75, 0x6c, 0x74, 0x69, 0x61,
	0x63, 0x71, 0x75, 0x69, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x54, 0x68, 0x72, 0x65, 0x65, 0x44, 0x53,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x44, 0x61, 0x74, 0x61, 0x1a, 0x3d, 0x2e, 0x70, 0x72,
	0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x2e, 0x6d, 0x75, 0x6c, 0x74, 0x69, 0x61, 0x63,
	0x71, 0x75, 0x69, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x6d, 0x75, 0x6c, 0x74, 0x69, 0x61, 0x63, 0x71,
	0x75, 0x69, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x54, 0x68, 0x72, 0x65, 0x65, 0x44, 0x53, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x44, 0x61, 0x74, 0x61, 0x22, 0x00, 0x12, 0x92, 0x01, 0x0a,
	0x0d, 0x54, 0x68, 0x72, 0x65, 0x65, 0x44, 0x53, 0x52, 0x65, 0x73, 0x75, 0x6d, 0x65, 0x12, 0x3e,
	0x2e, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x2e, 0x6d, 0x75, 0x6c, 0x74,
	0x69, 0x61, 0x63, 0x71, 0x75, 0x69, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x6d, 0x75, 0x6c, 0x74, 0x69,
	0x61, 0x63, 0x71, 0x75, 0x69, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x54, 0x68, 0x72, 0x65, 0x65, 0x44,
	0x53, 0x52, 0x65, 0x73, 0x75, 0x6d, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x3f,
	0x2e, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x2e, 0x6d, 0x75, 0x6c, 0x74,
	0x69, 0x61, 0x63, 0x71, 0x75, 0x69, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x6d, 0x75, 0x6c, 0x74, 0x69,
	0x61, 0x63, 0x71, 0x75, 0x69, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x54, 0x68, 0x72, 0x65, 0x65, 0x44,
	0x53, 0x52, 0x65, 0x73, 0x75, 0x6d, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22,
	0x00, 0x12, 0x85, 0x01, 0x0a, 0x06, 0x50, 0x61, 0x79, 0x4f, 0x75, 0x74, 0x12, 0x3b, 0x2e, 0x70,
	0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x2e, 0x6d, 0x75, 0x6c, 0x74, 0x69, 0x61,
	0x63, 0x71, 0x75, 0x69, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x6d, 0x75, 0x6c, 0x74, 0x69, 0x61, 0x63,
	0x71, 0x75, 0x69, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x50, 0x61, 0x79, 0x4f, 0x75, 0x74, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x44, 0x61, 0x74, 0x61, 0x1a, 0x3c, 0x2e, 0x70, 0x72, 0x6f, 0x63,
	0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x2e, 0x6d, 0x75, 0x6c, 0x74, 0x69, 0x61, 0x63, 0x71, 0x75,
	0x69, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x6d, 0x75, 0x6c, 0x74, 0x69, 0x61, 0x63, 0x71, 0x75, 0x69,
	0x72, 0x69, 0x6e, 0x67, 0x2e, 0x50, 0x61, 0x79, 0x4f, 0x75, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x44, 0x61, 0x74, 0x61, 0x22, 0x00, 0x12, 0xad, 0x01, 0x0a, 0x18, 0x47, 0x65,
	0x74, 0x42, 0x61, 0x6e, 0x6b, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e,
	0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x46, 0x2e, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73,
	0x69, 0x6e, 0x67, 0x2e, 0x6d, 0x75, 0x6c, 0x74, 0x69, 0x61, 0x63, 0x71, 0x75, 0x69, 0x72, 0x69,
	0x6e, 0x67, 0x2e, 0x6d, 0x75, 0x6c, 0x74, 0x69, 0x61, 0x63, 0x71, 0x75, 0x69, 0x72, 0x69, 0x6e,
	0x67, 0x2e, 0x42, 0x61, 0x6e, 0x6b, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f,
	0x6e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x47,
	0x2e, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x2e, 0x6d, 0x75, 0x6c, 0x74,
	0x69, 0x61, 0x63, 0x71, 0x75, 0x69, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x6d, 0x75, 0x6c, 0x74, 0x69,
	0x61, 0x63, 0x71, 0x75, 0x69, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x42, 0x61, 0x6e, 0x6b, 0x54, 0x72,
	0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0xcb, 0x01, 0x0a, 0x22, 0x47, 0x65,
	0x74, 0x42, 0x61, 0x6e, 0x6b, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e,
	0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x55, 0x6e, 0x66, 0x6f, 0x72, 0x6d, 0x61, 0x74, 0x65, 0x64,
	0x12, 0x50, 0x2e, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x2e, 0x6d, 0x75,
	0x6c, 0x74, 0x69, 0x61, 0x63, 0x71, 0x75, 0x69, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x6d, 0x75, 0x6c,
	0x74, 0x69, 0x61, 0x63, 0x71, 0x75, 0x69, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x42, 0x61, 0x6e, 0x6b,
	0x54, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x55, 0x6e, 0x66, 0x6f, 0x72, 0x6d, 0x61, 0x74, 0x65, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x1a, 0x51, 0x2e, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x2e,
	0x6d, 0x75, 0x6c, 0x74, 0x69, 0x61, 0x63, 0x71, 0x75, 0x69, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x6d,
	0x75, 0x6c, 0x74, 0x69, 0x61, 0x63, 0x71, 0x75, 0x69, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x42, 0x61,
	0x6e, 0x6b, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x55, 0x6e, 0x66, 0x6f, 0x72, 0x6d, 0x61, 0x74, 0x65, 0x64, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x7d, 0x0a, 0x06, 0x52, 0x65, 0x66, 0x75, 0x6e,
	0x64, 0x12, 0x37, 0x2e, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x2e, 0x6d,
	0x75, 0x6c, 0x74, 0x69, 0x61, 0x63, 0x71, 0x75, 0x69, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x6d, 0x75,
	0x6c, 0x74, 0x69, 0x61, 0x63, 0x71, 0x75, 0x69, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x52, 0x65, 0x66,
	0x75, 0x6e, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x38, 0x2e, 0x70, 0x72, 0x6f,
	0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x2e, 0x6d, 0x75, 0x6c, 0x74, 0x69, 0x61, 0x63, 0x71,
	0x75, 0x69, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x6d, 0x75, 0x6c, 0x74, 0x69, 0x61, 0x63, 0x71, 0x75,
	0x69, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x52, 0x65, 0x66, 0x75, 0x6e, 0x64, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x8e, 0x01, 0x0a, 0x09, 0x47, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x50, 0x61, 0x79, 0x12, 0x3e, 0x2e, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e,
	0x67, 0x2e, 0x6d, 0x75, 0x6c, 0x74, 0x69, 0x61, 0x63, 0x71, 0x75, 0x69, 0x72, 0x69, 0x6e, 0x67,
	0x2e, 0x6d, 0x75, 0x6c, 0x74, 0x69, 0x61, 0x63, 0x71, 0x75, 0x69, 0x72, 0x69, 0x6e, 0x67, 0x2e,
	0x47, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x50, 0x61, 0x79, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x44, 0x61, 0x74, 0x61, 0x1a, 0x3f, 0x2e, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e,
	0x67, 0x2e, 0x6d, 0x75, 0x6c, 0x74, 0x69, 0x61, 0x63, 0x71, 0x75, 0x69, 0x72, 0x69, 0x6e, 0x67,
	0x2e, 0x6d, 0x75, 0x6c, 0x74, 0x69, 0x61, 0x63, 0x71, 0x75, 0x69, 0x72, 0x69, 0x6e, 0x67, 0x2e,
	0x47, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x50, 0x61, 0x79, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x44, 0x61, 0x74, 0x61, 0x22, 0x00, 0x12, 0x8b, 0x01, 0x0a, 0x08, 0x41, 0x70, 0x70, 0x6c,
	0x65, 0x50, 0x61, 0x79, 0x12, 0x3d, 0x2e, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e,
	0x67, 0x2e, 0x6d, 0x75, 0x6c, 0x74, 0x69, 0x61, 0x63, 0x71, 0x75, 0x69, 0x72, 0x69, 0x6e, 0x67,
	0x2e, 0x6d, 0x75, 0x6c, 0x74, 0x69, 0x61, 0x63, 0x71, 0x75, 0x69, 0x72, 0x69, 0x6e, 0x67, 0x2e,
	0x41, 0x70, 0x70, 0x6c, 0x65, 0x50, 0x61, 0x79, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x44,
	0x61, 0x74, 0x61, 0x1a, 0x3e, 0x2e, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67,
	0x2e, 0x6d, 0x75, 0x6c, 0x74, 0x69, 0x61, 0x63, 0x71, 0x75, 0x69, 0x72, 0x69, 0x6e, 0x67, 0x2e,
	0x6d, 0x75, 0x6c, 0x74, 0x69, 0x61, 0x63, 0x71, 0x75, 0x69, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x41,
	0x70, 0x70, 0x6c, 0x65, 0x50, 0x61, 0x79, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x44,
	0x61, 0x74, 0x61, 0x22, 0x00, 0x12, 0x92, 0x01, 0x0a, 0x0d, 0x54, 0x77, 0x6f, 0x53, 0x74, 0x61,
	0x67, 0x65, 0x50, 0x61, 0x79, 0x49, 0x6e, 0x12, 0x3e, 0x2e, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73,
	0x73, 0x69, 0x6e, 0x67, 0x2e, 0x6d, 0x75, 0x6c, 0x74, 0x69, 0x61, 0x63, 0x71, 0x75, 0x69, 0x72,
	0x69, 0x6e, 0x67, 0x2e, 0x6d, 0x75, 0x6c, 0x74, 0x69, 0x61, 0x63, 0x71, 0x75, 0x69, 0x72, 0x69,
	0x6e, 0x67, 0x2e, 0x54, 0x77, 0x6f, 0x53, 0x74, 0x61, 0x67, 0x65, 0x50, 0x61, 0x79, 0x49, 0x6e,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x3f, 0x2e, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73,
	0x73, 0x69, 0x6e, 0x67, 0x2e, 0x6d, 0x75, 0x6c, 0x74, 0x69, 0x61, 0x63, 0x71, 0x75, 0x69, 0x72,
	0x69, 0x6e, 0x67, 0x2e, 0x6d, 0x75, 0x6c, 0x74, 0x69, 0x61, 0x63, 0x71, 0x75, 0x69, 0x72, 0x69,
	0x6e, 0x67, 0x2e, 0x54, 0x77, 0x6f, 0x53, 0x74, 0x61, 0x67, 0x65, 0x50, 0x61, 0x79, 0x49, 0x6e,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x7d, 0x0a, 0x06, 0x43, 0x68,
	0x61, 0x72, 0x67, 0x65, 0x12, 0x37, 0x2e, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e,
	0x67, 0x2e, 0x6d, 0x75, 0x6c, 0x74, 0x69, 0x61, 0x63, 0x71, 0x75, 0x69, 0x72, 0x69, 0x6e, 0x67,
	0x2e, 0x6d, 0x75, 0x6c, 0x74, 0x69, 0x61, 0x63, 0x71, 0x75, 0x69, 0x72, 0x69, 0x6e, 0x67, 0x2e,
	0x43, 0x68, 0x61, 0x72, 0x67, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x38, 0x2e,
	0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x2e, 0x6d, 0x75, 0x6c, 0x74, 0x69,
	0x61, 0x63, 0x71, 0x75, 0x69, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x6d, 0x75, 0x6c, 0x74, 0x69, 0x61,
	0x63, 0x71, 0x75, 0x69, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x43, 0x68, 0x61, 0x72, 0x67, 0x65, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x7d, 0x0a, 0x06, 0x43, 0x61, 0x6e,
	0x63, 0x65, 0x6c, 0x12, 0x37, 0x2e, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67,
	0x2e, 0x6d, 0x75, 0x6c, 0x74, 0x69, 0x61, 0x63, 0x71, 0x75, 0x69, 0x72, 0x69, 0x6e, 0x67, 0x2e,
	0x6d, 0x75, 0x6c, 0x74, 0x69, 0x61, 0x63, 0x71, 0x75, 0x69, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x43,
	0x61, 0x6e, 0x63, 0x65, 0x6c, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x38, 0x2e, 0x70,
	0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x2e, 0x6d, 0x75, 0x6c, 0x74, 0x69, 0x61,
	0x63, 0x71, 0x75, 0x69, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x6d, 0x75, 0x6c, 0x74, 0x69, 0x61, 0x63,
	0x71, 0x75, 0x69, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x43, 0x61, 0x6e, 0x63, 0x65, 0x6c, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x86, 0x01, 0x0a, 0x09, 0x4d, 0x61, 0x6b,
	0x65, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x12, 0x3a, 0x2e, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73,
	0x69, 0x6e, 0x67, 0x2e, 0x6d, 0x75, 0x6c, 0x74, 0x69, 0x61, 0x63, 0x71, 0x75, 0x69, 0x72, 0x69,
	0x6e, 0x67, 0x2e, 0x6d, 0x75, 0x6c, 0x74, 0x69, 0x61, 0x63, 0x71, 0x75, 0x69, 0x72, 0x69, 0x6e,
	0x67, 0x2e, 0x50, 0x61, 0x79, 0x49, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x44, 0x61,
	0x74, 0x61, 0x1a, 0x3b, 0x2e, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x2e,
	0x6d, 0x75, 0x6c, 0x74, 0x69, 0x61, 0x63, 0x71, 0x75, 0x69, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x6d,
	0x75, 0x6c, 0x74, 0x69, 0x61, 0x63, 0x71, 0x75, 0x69, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x50, 0x61,
	0x79, 0x49, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x44, 0x61, 0x74, 0x61, 0x22,
	0x00, 0x12, 0x9b, 0x01, 0x0a, 0x10, 0x52, 0x65, 0x73, 0x6f, 0x6c, 0x76, 0x65, 0x56, 0x69, 0x73,
	0x61, 0x41, 0x6c, 0x69, 0x61, 0x73, 0x12, 0x41, 0x2e, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73,
	0x69, 0x6e, 0x67, 0x2e, 0x6d, 0x75, 0x6c, 0x74, 0x69, 0x61, 0x63, 0x71, 0x75, 0x69, 0x72, 0x69,
	0x6e, 0x67, 0x2e, 0x6d, 0x75, 0x6c, 0x74, 0x69, 0x61, 0x63, 0x71, 0x75, 0x69, 0x72, 0x69, 0x6e,
	0x67, 0x2e, 0x52, 0x65, 0x73, 0x6f, 0x6c, 0x76, 0x65, 0x56, 0x69, 0x73, 0x61, 0x41, 0x6c, 0x69,
	0x61, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x42, 0x2e, 0x70, 0x72, 0x6f, 0x63,
	0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x2e, 0x6d, 0x75, 0x6c, 0x74, 0x69, 0x61, 0x63, 0x71, 0x75,
	0x69, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x6d, 0x75, 0x6c, 0x74, 0x69, 0x61, 0x63, 0x71, 0x75, 0x69,
	0x72, 0x69, 0x6e, 0x67, 0x2e, 0x52, 0x65, 0x73, 0x6f, 0x6c, 0x76, 0x65, 0x56, 0x69, 0x73, 0x61,
	0x41, 0x6c, 0x69, 0x61, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12,
	0x7a, 0x0a, 0x15, 0x47, 0x65, 0x74, 0x41, 0x63, 0x71, 0x75, 0x69, 0x72, 0x65, 0x72, 0x49, 0x64,
	0x65, 0x6e, 0x74, 0x69, 0x66, 0x69, 0x65, 0x72, 0x12, 0x16, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79,
	0x1a, 0x47, 0x2e, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x2e, 0x6d, 0x75,
	0x6c, 0x74, 0x69, 0x61, 0x63, 0x71, 0x75, 0x69, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x6d, 0x75, 0x6c,
	0x74, 0x69, 0x61, 0x63, 0x71, 0x75, 0x69, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x47, 0x65, 0x74, 0x41,
	0x63, 0x71, 0x75, 0x69, 0x72, 0x65, 0x72, 0x49, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x66, 0x69, 0x65,
	0x72, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x9a, 0x01, 0x0a, 0x0d,
	0x50, 0x61, 0x79, 0x4f, 0x75, 0x74, 0x42, 0x79, 0x50, 0x68, 0x6f, 0x6e, 0x65, 0x12, 0x42, 0x2e,
	0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x2e, 0x6d, 0x75, 0x6c, 0x74, 0x69,
	0x61, 0x63, 0x71, 0x75, 0x69, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x6d, 0x75, 0x6c, 0x74, 0x69, 0x61,
	0x63, 0x71, 0x75, 0x69, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x50, 0x61, 0x79, 0x4f, 0x75, 0x74, 0x42,
	0x79, 0x50, 0x68, 0x6f, 0x6e, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x44, 0x61, 0x74,
	0x61, 0x1a, 0x43, 0x2e, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x2e, 0x6d,
	0x75, 0x6c, 0x74, 0x69, 0x61, 0x63, 0x71, 0x75, 0x69, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x6d, 0x75,
	0x6c, 0x74, 0x69, 0x61, 0x63, 0x71, 0x75, 0x69, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x50, 0x61, 0x79,
	0x4f, 0x75, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x42, 0x79, 0x50, 0x68, 0x6f,
	0x6e, 0x65, 0x44, 0x61, 0x74, 0x61, 0x22, 0x00, 0x42, 0x2d, 0x5a, 0x2b, 0x67, 0x69, 0x74, 0x2e,
	0x6c, 0x6f, 0x63, 0x61, 0x6c, 0x2f, 0x73, 0x65, 0x6e, 0x73, 0x69, 0x74, 0x69, 0x76, 0x65, 0x2f,
	0x69, 0x6e, 0x6e, 0x65, 0x72, 0x70, 0x62, 0x2f, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69,
	0x6e, 0x67, 0x2f, 0x67, 0x72, 0x70, 0x63, 0x62, 0x08, 0x65, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e,
	0x73, 0x70, 0xe8, 0x07,
})

var (
	file_inner_processing_grpc_multiacquiring_proto_rawDescOnce sync.Once
	file_inner_processing_grpc_multiacquiring_proto_rawDescData []byte
)

func file_inner_processing_grpc_multiacquiring_proto_rawDescGZIP() []byte {
	file_inner_processing_grpc_multiacquiring_proto_rawDescOnce.Do(func() {
		file_inner_processing_grpc_multiacquiring_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_inner_processing_grpc_multiacquiring_proto_rawDesc), len(file_inner_processing_grpc_multiacquiring_proto_rawDesc)))
	})
	return file_inner_processing_grpc_multiacquiring_proto_rawDescData
}

var file_inner_processing_grpc_multiacquiring_proto_msgTypes = make([]protoimpl.MessageInfo, 39)
var file_inner_processing_grpc_multiacquiring_proto_goTypes = []any{
	(*GetAcquirerIdentifierResponse)(nil),           // 0: processing.multiacquiring.multiacquiring.GetAcquirerIdentifierResponse
	(*PayInRequestData)(nil),                        // 1: processing.multiacquiring.multiacquiring.PayInRequestData
	(*OneClickPayInRequestData)(nil),                // 2: processing.multiacquiring.multiacquiring.OneClickPayInRequestData
	(*UserDataForBank)(nil),                         // 3: processing.multiacquiring.multiacquiring.UserDataForBank
	(*TerminalDataForBank)(nil),                     // 4: processing.multiacquiring.multiacquiring.TerminalDataForBank
	(*CardDataForBank)(nil),                         // 5: processing.multiacquiring.multiacquiring.CardDataForBank
	(*PayInResponseData)(nil),                       // 6: processing.multiacquiring.multiacquiring.PayInResponseData
	(*ThreeDSDataFromBank)(nil),                     // 7: processing.multiacquiring.multiacquiring.ThreeDSDataFromBank
	(*ThreeDSRequestData)(nil),                      // 8: processing.multiacquiring.multiacquiring.ThreeDSRequestData
	(*ThreeDSResponseData)(nil),                     // 9: processing.multiacquiring.multiacquiring.ThreeDSResponseData
	(*PayOutRequestData)(nil),                       // 10: processing.multiacquiring.multiacquiring.PayOutRequestData
	(*PayOutByPhoneRequestData)(nil),                // 11: processing.multiacquiring.multiacquiring.PayOutByPhoneRequestData
	(*PayOutResponseByPhoneData)(nil),               // 12: processing.multiacquiring.multiacquiring.PayOutResponseByPhoneData
	(*PayOutCard)(nil),                              // 13: processing.multiacquiring.multiacquiring.PayOutCard
	(*PayOutResponseData)(nil),                      // 14: processing.multiacquiring.multiacquiring.PayOutResponseData
	(*BankTransactionStatusRequest)(nil),            // 15: processing.multiacquiring.multiacquiring.BankTransactionStatusRequest
	(*BankTransactionStatusResponse)(nil),           // 16: processing.multiacquiring.multiacquiring.BankTransactionStatusResponse
	(*BankTransactionStatusUnformatedRequest)(nil),  // 17: processing.multiacquiring.multiacquiring.BankTransactionStatusUnformatedRequest
	(*BankTransactionStatusUnformatedResponse)(nil), // 18: processing.multiacquiring.multiacquiring.BankTransactionStatusUnformatedResponse
	(*RefundRequest)(nil),                           // 19: processing.multiacquiring.multiacquiring.RefundRequest
	(*RefundResponse)(nil),                          // 20: processing.multiacquiring.multiacquiring.RefundResponse
	(*CardInfo)(nil),                                // 21: processing.multiacquiring.multiacquiring.CardInfo
	(*MerchantInfo)(nil),                            // 22: processing.multiacquiring.multiacquiring.MerchantInfo
	(*GooglePayRequestData)(nil),                    // 23: processing.multiacquiring.multiacquiring.GooglePayRequestData
	(*GooglePayResponseData)(nil),                   // 24: processing.multiacquiring.multiacquiring.GooglePayResponseData
	(*ApplePayRequestData)(nil),                     // 25: processing.multiacquiring.multiacquiring.ApplePayRequestData
	(*ApplePayResponseData)(nil),                    // 26: processing.multiacquiring.multiacquiring.ApplePayResponseData
	(*TwoStagePayInRequest)(nil),                    // 27: processing.multiacquiring.multiacquiring.TwoStagePayInRequest
	(*ChargeRequest)(nil),                           // 28: processing.multiacquiring.multiacquiring.ChargeRequest
	(*CancelRequest)(nil),                           // 29: processing.multiacquiring.multiacquiring.CancelRequest
	(*TwoStagePayInResponse)(nil),                   // 30: processing.multiacquiring.multiacquiring.TwoStagePayInResponse
	(*ChargeResponse)(nil),                          // 31: processing.multiacquiring.multiacquiring.ChargeResponse
	(*CancelResponse)(nil),                          // 32: processing.multiacquiring.multiacquiring.CancelResponse
	(*Fingerprint)(nil),                             // 33: processing.multiacquiring.multiacquiring.Fingerprint
	(*ThreeDSResumeRequest)(nil),                    // 34: processing.multiacquiring.multiacquiring.ThreeDSResumeRequest
	(*ThreeDSResumeResponse)(nil),                   // 35: processing.multiacquiring.multiacquiring.ThreeDSResumeResponse
	(*BankResponse)(nil),                            // 36: processing.multiacquiring.multiacquiring.BankResponse
	(*ResolveVisaAliasRequest)(nil),                 // 37: processing.multiacquiring.multiacquiring.ResolveVisaAliasRequest
	(*ResolveVisaAliasResponse)(nil),                // 38: processing.multiacquiring.multiacquiring.ResolveVisaAliasResponse
	(*timestamppb.Timestamp)(nil),                   // 39: google.protobuf.Timestamp
	(*structpb.Struct)(nil),                         // 40: google.protobuf.Struct
	(EnumTransactionStatus)(0),                      // 41: processing.transaction.transaction_status.EnumTransactionStatus
	(*emptypb.Empty)(nil),                           // 42: google.protobuf.Empty
}
var file_inner_processing_grpc_multiacquiring_proto_depIdxs = []int32{
	5,   // 0: processing.multiacquiring.multiacquiring.PayInRequestData.card:type_name -> processing.multiacquiring.multiacquiring.CardDataForBank
	3,   // 1: processing.multiacquiring.multiacquiring.PayInRequestData.user:type_name -> processing.multiacquiring.multiacquiring.UserDataForBank
	4,   // 2: processing.multiacquiring.multiacquiring.PayInRequestData.terminal:type_name -> processing.multiacquiring.multiacquiring.TerminalDataForBank
	21,  // 3: processing.multiacquiring.multiacquiring.PayInRequestData.card_info:type_name -> processing.multiacquiring.multiacquiring.CardInfo
	22,  // 4: processing.multiacquiring.multiacquiring.PayInRequestData.merchant_info:type_name -> processing.multiacquiring.multiacquiring.MerchantInfo
	39,  // 5: processing.multiacquiring.multiacquiring.PayInRequestData.transaction_created_at:type_name -> google.protobuf.Timestamp
	3,   // 6: processing.multiacquiring.multiacquiring.OneClickPayInRequestData.user:type_name -> processing.multiacquiring.multiacquiring.UserDataForBank
	4,   // 7: processing.multiacquiring.multiacquiring.OneClickPayInRequestData.terminal:type_name -> processing.multiacquiring.multiacquiring.TerminalDataForBank
	21,  // 8: processing.multiacquiring.multiacquiring.OneClickPayInRequestData.card_info:type_name -> processing.multiacquiring.multiacquiring.CardInfo
	22,  // 9: processing.multiacquiring.multiacquiring.OneClickPayInRequestData.merchant_info:type_name -> processing.multiacquiring.multiacquiring.MerchantInfo
	39,  // 10: processing.multiacquiring.multiacquiring.OneClickPayInRequestData.transaction_created_at:type_name -> google.protobuf.Timestamp
	40,  // 11: processing.multiacquiring.multiacquiring.TerminalDataForBank.config:type_name -> google.protobuf.Struct
	41,  // 12: processing.multiacquiring.multiacquiring.PayInResponseData.transaction_status:type_name -> processing.transaction.transaction_status.EnumTransactionStatus
	7,   // 13: processing.multiacquiring.multiacquiring.PayInResponseData.threeDS:type_name -> processing.multiacquiring.multiacquiring.ThreeDSDataFromBank
	36,  // 14: processing.multiacquiring.multiacquiring.PayInResponseData.bank_response:type_name -> processing.multiacquiring.multiacquiring.BankResponse
	33,  // 15: processing.multiacquiring.multiacquiring.PayInResponseData.fingerprint:type_name -> processing.multiacquiring.multiacquiring.Fingerprint
	40,  // 16: processing.multiacquiring.multiacquiring.ThreeDSDataFromBank.params:type_name -> google.protobuf.Struct
	4,   // 17: processing.multiacquiring.multiacquiring.ThreeDSRequestData.terminal:type_name -> processing.multiacquiring.multiacquiring.TerminalDataForBank
	41,  // 18: processing.multiacquiring.multiacquiring.ThreeDSResponseData.transaction_status:type_name -> processing.transaction.transaction_status.EnumTransactionStatus
	36,  // 19: processing.multiacquiring.multiacquiring.ThreeDSResponseData.bank_response:type_name -> processing.multiacquiring.multiacquiring.BankResponse
	3,   // 20: processing.multiacquiring.multiacquiring.PayOutRequestData.user:type_name -> processing.multiacquiring.multiacquiring.UserDataForBank
	4,   // 21: processing.multiacquiring.multiacquiring.PayOutRequestData.terminal:type_name -> processing.multiacquiring.multiacquiring.TerminalDataForBank
	13,  // 22: processing.multiacquiring.multiacquiring.PayOutRequestData.card:type_name -> processing.multiacquiring.multiacquiring.PayOutCard
	21,  // 23: processing.multiacquiring.multiacquiring.PayOutRequestData.card_info:type_name -> processing.multiacquiring.multiacquiring.CardInfo
	22,  // 24: processing.multiacquiring.multiacquiring.PayOutRequestData.merchant_info:type_name -> processing.multiacquiring.multiacquiring.MerchantInfo
	39,  // 25: processing.multiacquiring.multiacquiring.PayOutRequestData.transaction_created_at:type_name -> google.protobuf.Timestamp
	3,   // 26: processing.multiacquiring.multiacquiring.PayOutByPhoneRequestData.user:type_name -> processing.multiacquiring.multiacquiring.UserDataForBank
	4,   // 27: processing.multiacquiring.multiacquiring.PayOutByPhoneRequestData.terminal:type_name -> processing.multiacquiring.multiacquiring.TerminalDataForBank
	13,  // 28: processing.multiacquiring.multiacquiring.PayOutByPhoneRequestData.card:type_name -> processing.multiacquiring.multiacquiring.PayOutCard
	21,  // 29: processing.multiacquiring.multiacquiring.PayOutByPhoneRequestData.card_info:type_name -> processing.multiacquiring.multiacquiring.CardInfo
	22,  // 30: processing.multiacquiring.multiacquiring.PayOutByPhoneRequestData.merchant_info:type_name -> processing.multiacquiring.multiacquiring.MerchantInfo
	39,  // 31: processing.multiacquiring.multiacquiring.PayOutByPhoneRequestData.transaction_created_at:type_name -> google.protobuf.Timestamp
	41,  // 32: processing.multiacquiring.multiacquiring.PayOutResponseByPhoneData.transaction_status:type_name -> processing.transaction.transaction_status.EnumTransactionStatus
	36,  // 33: processing.multiacquiring.multiacquiring.PayOutResponseByPhoneData.bank_response:type_name -> processing.multiacquiring.multiacquiring.BankResponse
	41,  // 34: processing.multiacquiring.multiacquiring.PayOutResponseData.transaction_status:type_name -> processing.transaction.transaction_status.EnumTransactionStatus
	36,  // 35: processing.multiacquiring.multiacquiring.PayOutResponseData.bank_response:type_name -> processing.multiacquiring.multiacquiring.BankResponse
	40,  // 36: processing.multiacquiring.multiacquiring.BankTransactionStatusRequest.config:type_name -> google.protobuf.Struct
	4,   // 37: processing.multiacquiring.multiacquiring.BankTransactionStatusRequest.terminal:type_name -> processing.multiacquiring.multiacquiring.TerminalDataForBank
	39,  // 38: processing.multiacquiring.multiacquiring.BankTransactionStatusRequest.transaction_created_at:type_name -> google.protobuf.Timestamp
	41,  // 39: processing.multiacquiring.multiacquiring.BankTransactionStatusResponse.transaction_status:type_name -> processing.transaction.transaction_status.EnumTransactionStatus
	36,  // 40: processing.multiacquiring.multiacquiring.BankTransactionStatusResponse.bank_response:type_name -> processing.multiacquiring.multiacquiring.BankResponse
	40,  // 41: processing.multiacquiring.multiacquiring.BankTransactionStatusUnformatedRequest.config:type_name -> google.protobuf.Struct
	4,   // 42: processing.multiacquiring.multiacquiring.BankTransactionStatusUnformatedRequest.terminal:type_name -> processing.multiacquiring.multiacquiring.TerminalDataForBank
	39,  // 43: processing.multiacquiring.multiacquiring.BankTransactionStatusUnformatedRequest.transaction_created_at:type_name -> google.protobuf.Timestamp
	4,   // 44: processing.multiacquiring.multiacquiring.RefundRequest.terminal:type_name -> processing.multiacquiring.multiacquiring.TerminalDataForBank
	39,  // 45: processing.multiacquiring.multiacquiring.RefundRequest.transaction_created_at:type_name -> google.protobuf.Timestamp
	36,  // 46: processing.multiacquiring.multiacquiring.RefundResponse.bank_response:type_name -> processing.multiacquiring.multiacquiring.BankResponse
	5,   // 47: processing.multiacquiring.multiacquiring.GooglePayRequestData.card:type_name -> processing.multiacquiring.multiacquiring.CardDataForBank
	3,   // 48: processing.multiacquiring.multiacquiring.GooglePayRequestData.user:type_name -> processing.multiacquiring.multiacquiring.UserDataForBank
	4,   // 49: processing.multiacquiring.multiacquiring.GooglePayRequestData.terminal:type_name -> processing.multiacquiring.multiacquiring.TerminalDataForBank
	21,  // 50: processing.multiacquiring.multiacquiring.GooglePayRequestData.card_info:type_name -> processing.multiacquiring.multiacquiring.CardInfo
	22,  // 51: processing.multiacquiring.multiacquiring.GooglePayRequestData.merchant_info:type_name -> processing.multiacquiring.multiacquiring.MerchantInfo
	39,  // 52: processing.multiacquiring.multiacquiring.GooglePayRequestData.transaction_created_at:type_name -> google.protobuf.Timestamp
	41,  // 53: processing.multiacquiring.multiacquiring.GooglePayResponseData.transaction_status:type_name -> processing.transaction.transaction_status.EnumTransactionStatus
	7,   // 54: processing.multiacquiring.multiacquiring.GooglePayResponseData.threeDS:type_name -> processing.multiacquiring.multiacquiring.ThreeDSDataFromBank
	36,  // 55: processing.multiacquiring.multiacquiring.GooglePayResponseData.bank_response:type_name -> processing.multiacquiring.multiacquiring.BankResponse
	33,  // 56: processing.multiacquiring.multiacquiring.GooglePayResponseData.fingerprint:type_name -> processing.multiacquiring.multiacquiring.Fingerprint
	5,   // 57: processing.multiacquiring.multiacquiring.ApplePayRequestData.card:type_name -> processing.multiacquiring.multiacquiring.CardDataForBank
	3,   // 58: processing.multiacquiring.multiacquiring.ApplePayRequestData.user:type_name -> processing.multiacquiring.multiacquiring.UserDataForBank
	4,   // 59: processing.multiacquiring.multiacquiring.ApplePayRequestData.terminal:type_name -> processing.multiacquiring.multiacquiring.TerminalDataForBank
	21,  // 60: processing.multiacquiring.multiacquiring.ApplePayRequestData.card_info:type_name -> processing.multiacquiring.multiacquiring.CardInfo
	22,  // 61: processing.multiacquiring.multiacquiring.ApplePayRequestData.merchant_info:type_name -> processing.multiacquiring.multiacquiring.MerchantInfo
	39,  // 62: processing.multiacquiring.multiacquiring.ApplePayRequestData.transaction_created_at:type_name -> google.protobuf.Timestamp
	41,  // 63: processing.multiacquiring.multiacquiring.ApplePayResponseData.transaction_status:type_name -> processing.transaction.transaction_status.EnumTransactionStatus
	7,   // 64: processing.multiacquiring.multiacquiring.ApplePayResponseData.threeDS:type_name -> processing.multiacquiring.multiacquiring.ThreeDSDataFromBank
	36,  // 65: processing.multiacquiring.multiacquiring.ApplePayResponseData.bank_response:type_name -> processing.multiacquiring.multiacquiring.BankResponse
	5,   // 66: processing.multiacquiring.multiacquiring.TwoStagePayInRequest.card:type_name -> processing.multiacquiring.multiacquiring.CardDataForBank
	3,   // 67: processing.multiacquiring.multiacquiring.TwoStagePayInRequest.user:type_name -> processing.multiacquiring.multiacquiring.UserDataForBank
	4,   // 68: processing.multiacquiring.multiacquiring.TwoStagePayInRequest.terminal:type_name -> processing.multiacquiring.multiacquiring.TerminalDataForBank
	21,  // 69: processing.multiacquiring.multiacquiring.TwoStagePayInRequest.card_info:type_name -> processing.multiacquiring.multiacquiring.CardInfo
	22,  // 70: processing.multiacquiring.multiacquiring.TwoStagePayInRequest.merchant_info:type_name -> processing.multiacquiring.multiacquiring.MerchantInfo
	4,   // 71: processing.multiacquiring.multiacquiring.ChargeRequest.terminal:type_name -> processing.multiacquiring.multiacquiring.TerminalDataForBank
	39,  // 72: processing.multiacquiring.multiacquiring.ChargeRequest.transaction_created_at:type_name -> google.protobuf.Timestamp
	4,   // 73: processing.multiacquiring.multiacquiring.CancelRequest.terminal:type_name -> processing.multiacquiring.multiacquiring.TerminalDataForBank
	39,  // 74: processing.multiacquiring.multiacquiring.CancelRequest.transaction_created_at:type_name -> google.protobuf.Timestamp
	41,  // 75: processing.multiacquiring.multiacquiring.TwoStagePayInResponse.transaction_status:type_name -> processing.transaction.transaction_status.EnumTransactionStatus
	7,   // 76: processing.multiacquiring.multiacquiring.TwoStagePayInResponse.threeDS:type_name -> processing.multiacquiring.multiacquiring.ThreeDSDataFromBank
	36,  // 77: processing.multiacquiring.multiacquiring.TwoStagePayInResponse.bank_response:type_name -> processing.multiacquiring.multiacquiring.BankResponse
	36,  // 78: processing.multiacquiring.multiacquiring.ChargeResponse.bank_response:type_name -> processing.multiacquiring.multiacquiring.BankResponse
	36,  // 79: processing.multiacquiring.multiacquiring.CancelResponse.bank_response:type_name -> processing.multiacquiring.multiacquiring.BankResponse
	4,   // 80: processing.multiacquiring.multiacquiring.ThreeDSResumeRequest.terminal:type_name -> processing.multiacquiring.multiacquiring.TerminalDataForBank
	41,  // 81: processing.multiacquiring.multiacquiring.ThreeDSResumeResponse.transaction_status:type_name -> processing.transaction.transaction_status.EnumTransactionStatus
	7,   // 82: processing.multiacquiring.multiacquiring.ThreeDSResumeResponse.threeDS:type_name -> processing.multiacquiring.multiacquiring.ThreeDSDataFromBank
	36,  // 83: processing.multiacquiring.multiacquiring.ThreeDSResumeResponse.bank_response:type_name -> processing.multiacquiring.multiacquiring.BankResponse
	4,   // 84: processing.multiacquiring.multiacquiring.ResolveVisaAliasRequest.terminal:type_name -> processing.multiacquiring.multiacquiring.TerminalDataForBank
	36,  // 85: processing.multiacquiring.multiacquiring.ResolveVisaAliasResponse.bank_response:type_name -> processing.multiacquiring.multiacquiring.BankResponse
	1,   // 86: processing.multiacquiring.multiacquiring.MultiAcquiring.PayIn:input_type -> processing.multiacquiring.multiacquiring.PayInRequestData
	2,   // 87: processing.multiacquiring.multiacquiring.MultiAcquiring.OneClickPayIn:input_type -> processing.multiacquiring.multiacquiring.OneClickPayInRequestData
	8,   // 88: processing.multiacquiring.multiacquiring.MultiAcquiring.ThreeDSConfirm:input_type -> processing.multiacquiring.multiacquiring.ThreeDSRequestData
	34,  // 89: processing.multiacquiring.multiacquiring.MultiAcquiring.ThreeDSResume:input_type -> processing.multiacquiring.multiacquiring.ThreeDSResumeRequest
	10,  // 90: processing.multiacquiring.multiacquiring.MultiAcquiring.PayOut:input_type -> processing.multiacquiring.multiacquiring.PayOutRequestData
	15,  // 91: processing.multiacquiring.multiacquiring.MultiAcquiring.GetBankTransactionStatus:input_type -> processing.multiacquiring.multiacquiring.BankTransactionStatusRequest
	17,  // 92: processing.multiacquiring.multiacquiring.MultiAcquiring.GetBankTransactionStatusUnformated:input_type -> processing.multiacquiring.multiacquiring.BankTransactionStatusUnformatedRequest
	19,  // 93: processing.multiacquiring.multiacquiring.MultiAcquiring.Refund:input_type -> processing.multiacquiring.multiacquiring.RefundRequest
	23,  // 94: processing.multiacquiring.multiacquiring.MultiAcquiring.GooglePay:input_type -> processing.multiacquiring.multiacquiring.GooglePayRequestData
	25,  // 95: processing.multiacquiring.multiacquiring.MultiAcquiring.ApplePay:input_type -> processing.multiacquiring.multiacquiring.ApplePayRequestData
	27,  // 96: processing.multiacquiring.multiacquiring.MultiAcquiring.TwoStagePayIn:input_type -> processing.multiacquiring.multiacquiring.TwoStagePayInRequest
	28,  // 97: processing.multiacquiring.multiacquiring.MultiAcquiring.Charge:input_type -> processing.multiacquiring.multiacquiring.ChargeRequest
	29,  // 98: processing.multiacquiring.multiacquiring.MultiAcquiring.Cancel:input_type -> processing.multiacquiring.multiacquiring.CancelRequest
	1,   // 99: processing.multiacquiring.multiacquiring.MultiAcquiring.MakeToken:input_type -> processing.multiacquiring.multiacquiring.PayInRequestData
	37,  // 100: processing.multiacquiring.multiacquiring.MultiAcquiring.ResolveVisaAlias:input_type -> processing.multiacquiring.multiacquiring.ResolveVisaAliasRequest
	42,  // 101: processing.multiacquiring.multiacquiring.MultiAcquiring.GetAcquirerIdentifier:input_type -> google.protobuf.Empty
	11,  // 102: processing.multiacquiring.multiacquiring.MultiAcquiring.PayOutByPhone:input_type -> processing.multiacquiring.multiacquiring.PayOutByPhoneRequestData
	6,   // 103: processing.multiacquiring.multiacquiring.MultiAcquiring.PayIn:output_type -> processing.multiacquiring.multiacquiring.PayInResponseData
	6,   // 104: processing.multiacquiring.multiacquiring.MultiAcquiring.OneClickPayIn:output_type -> processing.multiacquiring.multiacquiring.PayInResponseData
	9,   // 105: processing.multiacquiring.multiacquiring.MultiAcquiring.ThreeDSConfirm:output_type -> processing.multiacquiring.multiacquiring.ThreeDSResponseData
	35,  // 106: processing.multiacquiring.multiacquiring.MultiAcquiring.ThreeDSResume:output_type -> processing.multiacquiring.multiacquiring.ThreeDSResumeResponse
	14,  // 107: processing.multiacquiring.multiacquiring.MultiAcquiring.PayOut:output_type -> processing.multiacquiring.multiacquiring.PayOutResponseData
	16,  // 108: processing.multiacquiring.multiacquiring.MultiAcquiring.GetBankTransactionStatus:output_type -> processing.multiacquiring.multiacquiring.BankTransactionStatusResponse
	18,  // 109: processing.multiacquiring.multiacquiring.MultiAcquiring.GetBankTransactionStatusUnformated:output_type -> processing.multiacquiring.multiacquiring.BankTransactionStatusUnformatedResponse
	20,  // 110: processing.multiacquiring.multiacquiring.MultiAcquiring.Refund:output_type -> processing.multiacquiring.multiacquiring.RefundResponse
	24,  // 111: processing.multiacquiring.multiacquiring.MultiAcquiring.GooglePay:output_type -> processing.multiacquiring.multiacquiring.GooglePayResponseData
	26,  // 112: processing.multiacquiring.multiacquiring.MultiAcquiring.ApplePay:output_type -> processing.multiacquiring.multiacquiring.ApplePayResponseData
	30,  // 113: processing.multiacquiring.multiacquiring.MultiAcquiring.TwoStagePayIn:output_type -> processing.multiacquiring.multiacquiring.TwoStagePayInResponse
	31,  // 114: processing.multiacquiring.multiacquiring.MultiAcquiring.Charge:output_type -> processing.multiacquiring.multiacquiring.ChargeResponse
	32,  // 115: processing.multiacquiring.multiacquiring.MultiAcquiring.Cancel:output_type -> processing.multiacquiring.multiacquiring.CancelResponse
	6,   // 116: processing.multiacquiring.multiacquiring.MultiAcquiring.MakeToken:output_type -> processing.multiacquiring.multiacquiring.PayInResponseData
	38,  // 117: processing.multiacquiring.multiacquiring.MultiAcquiring.ResolveVisaAlias:output_type -> processing.multiacquiring.multiacquiring.ResolveVisaAliasResponse
	0,   // 118: processing.multiacquiring.multiacquiring.MultiAcquiring.GetAcquirerIdentifier:output_type -> processing.multiacquiring.multiacquiring.GetAcquirerIdentifierResponse
	12,  // 119: processing.multiacquiring.multiacquiring.MultiAcquiring.PayOutByPhone:output_type -> processing.multiacquiring.multiacquiring.PayOutResponseByPhoneData
	103, // [103:120] is the sub-list for method output_type
	86,  // [86:103] is the sub-list for method input_type
	86,  // [86:86] is the sub-list for extension type_name
	86,  // [86:86] is the sub-list for extension extendee
	0,   // [0:86] is the sub-list for field type_name
}

func init() { file_inner_processing_grpc_multiacquiring_proto_init() }
func file_inner_processing_grpc_multiacquiring_proto_init() {
	if File_inner_processing_grpc_multiacquiring_proto != nil {
		return
	}
	file_inner_processing_grpc_transaction_status_proto_init()
	file_inner_processing_grpc_transaction_proto_init()
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_inner_processing_grpc_multiacquiring_proto_rawDesc), len(file_inner_processing_grpc_multiacquiring_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   39,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_inner_processing_grpc_multiacquiring_proto_goTypes,
		DependencyIndexes: file_inner_processing_grpc_multiacquiring_proto_depIdxs,
		MessageInfos:      file_inner_processing_grpc_multiacquiring_proto_msgTypes,
	}.Build()
	File_inner_processing_grpc_multiacquiring_proto = out.File
	file_inner_processing_grpc_multiacquiring_proto_goTypes = nil
	file_inner_processing_grpc_multiacquiring_proto_depIdxs = nil
}
