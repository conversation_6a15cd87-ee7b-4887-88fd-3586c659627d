package test

import (
	"bytes"
	"encoding/json"
	"io"
	"net/http"
	"testing"

	"github.com/stretchr/testify/assert"

	"git.local/sensitive/local/internal/domain"
	"git.local/sensitive/local/internal/smoketesting/core/oneclick"
)

func TestOneClick(t *testing.T) {
	t.Skip()

	t.Run("test_one_click", func(t *testing.T) {
		transactionId, hash := generatePayInTransaction(t, &domain.GenerateTransactionRequest{
			Amount:             10,
			ProjectID:          devProjectID,
			MerchantID:         devMerchantID,
			ProjectClientID:    "kambar61",
			FailureRedirectUrl: "https://www.youtube.com",
			SuccessRedirectUrl: "https://www.youtube.com",
			CallbackUrl:        devCallbackUrl,
			Description:        "afk_afk_afk",
			ProjectReferenceID: generateSecretKey(),
		}, domain.SmokeData[string]{
			Expected: domain.Expected[string]{
				HttpStatusCode: http.StatusOK,
			},
		})
		token := getCard(t, &domain.GetClientCardsRequest{
			MerchantID:      devMerchantID,
			ProjectClientID: "kambar61",
			ProjectID:       devProjectID,
		}, domain.SmokeData[[]domain.GetCardResponse]{
			Expected: domain.Expected[[]domain.GetCardResponse]{
				HttpStatusCode: http.StatusOK,
				Data: []domain.GetCardResponse{
					{
						CardToken: "AAAAAAAAAAAAAAAAAAAAAOV3HplX9zAuDTOlewBIEV79E01GeKngAYvBuZRG8fDJYH6AImkVesOCZhRPImg5T/Acc3MDbtTl2SpIr/bwnjWdD3BVgS3DkodpGhMbB1Hfs9abv+O8yrO1flDSmQtIXA==",
						MaskedPan: "4400-43XXXXXX-5314",
						Month:     "04",
						Year:      "25",
					},
				},
			},
		})
		processOneClick(t, &domain.MakeOneClickPayRequest{
			TransactionID:   transactionId,
			EncryptedID:     token,
			TransactionHash: hash,
			UserPhone:       "+77771581078",
			UserEmail:       "<EMAIL>",
		}, &domain.MakePayInResponse{
			AcquirerCode:  "jusan",
			TransactionID: transactionId,
		})
	})

}

func processOneClick(t *testing.T, req *domain.MakeOneClickPayRequest, expected *domain.MakePayInResponse) {
	body, err := json.Marshal(req)
	if err != nil {
		t.Fatal(err)
	}

	request, err := http.NewRequest(http.MethodPost, domainURL+oneclick.UrlPath, bytes.NewBuffer(body))
	if err != nil {
		t.Fatal(err)
	}

	request.Header.Add("Content-Type", "application/json")

	response, err := http.DefaultClient.Do(request)
	if err != nil {
		t.Fatal(err)
	}

	respBody, err := io.ReadAll(response.Body)
	if err != nil {
		t.Fatal(err)
	}

	var respData domain.Response[domain.MakePayInResponse]
	if err = json.Unmarshal(respBody, &respData); err != nil {
		t.Fatal(err)
	}

	assert.Equal(t, response.StatusCode, http.StatusOK, "http_status_code")
	assert.Equal(t, expected.TransactionID, respData.Result.TransactionID, "transactionID")
	assert.Equal(t, expected.AcquirerCode, respData.Result.AcquirerCode, "acquirer code")
}
