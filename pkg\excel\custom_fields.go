/*
ДАННЫЙ ОБЪЕКТ ПРЕДНАЗНАЧЕН ДЛЯ ПРЕОБРАЗОВАНИЯ КАСТОМНЫХ ПОЛЕЙ
КОНКРЕТНОЙ МОДЕЛИ В ЭКСЕЛЬНЫЙ ФОРМАТ
*/

package excel

import (
	"encoding/json"
	"gorm.io/datatypes"
	"reflect"
	"time"
)

type UserFieldable interface {
	GetInstance() interface{}
}

type FieldListSlice []FieldList

type FieldList struct {
	// ID unique field code ex: transaction_id
	ID string `json:"id" validate:"required"`
	// IsVisible pointer is used because validator won't pass if value is false
	IsVisible *bool `json:"is_visible" validate:"required"`
	// IsRequired pointer is used because validator won't pass if value is false
	IsRequired *bool `json:"is_required" validate:"required"`
	// Sequence is used to order this field
	Sequence uint8 `json:"sequence" validate:"required,gt=0"`
	// Label is used as translated value of a field ID
	Label string `json:"label" validate:"required"`
	// Code is a name of struct field used for translation
	Code string `json:"code" validate:"required"`
}

func GetDataLen[T UserFieldable](data T) uint64 {
	v := reflect.ValueOf(data)

	if v.Kind() == reflect.Ptr {
		v = v.Elem()
	}

	if v.Kind() != reflect.Slice {
		return 0
	}

	return uint64(v.Len())
}

func ApplyUserFields(
	sourceData UserFieldable,
	userFields []FieldList,
) (headersRow []interface{}, dataRow [][]interface{}) {
	t := reflect.TypeOf(sourceData.GetInstance())

	if t.Kind() == reflect.Ptr {
		t = t.Elem()
	}

	matchedIndexes := make(map[int]string)
	headersNames := make([]interface{}, 0, t.NumField())
	headersCounter := 0

	for i := 0; i < t.NumField(); i++ {
		field := t.Field(i)
		jsonTagValue := field.Tag.Get(JsonTag)

		for j := range userFields {
			if userFields[j].ID == jsonTagValue {
				if *userFields[j].IsRequired || *userFields[j].IsVisible {
					matchedIndexes[i] = userFields[j].Label
					headersCounter++
				}
			}
		}
	}

	v := reflect.ValueOf(sourceData)

	if v.Kind() == reflect.Ptr {
		v = v.Elem()
	}

	if v.Kind() != reflect.Slice {
		return nil, nil
	}

	return processFields(matchedIndexes, v, headersNames, headersCounter)
}

func processFields(
	matchedIndexes map[int]string,
	v reflect.Value,
	headersNames []interface{},
	headersCounter int,
) ([]interface{}, [][]interface{}) {
	finalData := make([][]interface{}, v.Len())

	for i := 0; i < v.Len(); i++ {
		row := v.Index(i)

		// если элемент слайса - указатель, развернем его
		if row.Kind() == reflect.Ptr {
			row = row.Elem()
		}

		//записываем дату и хедеры если индекс был помечен в мапе
		for j := 0; j < row.NumField(); j++ {
			if matchedIndexes[j] == "" {
				continue
			}

			field := row.Field(j)
			// writing time in fomat string with UTC ("2 Jan 06 15:04 UTC -07:00")
			switch field.Type() {
			case reflect.TypeOf(time.Time{}):
				formattedTime := field.Interface().(time.Time).Format("02 Jan 06 15:04 UTC -07:00")
				finalData[i] = append(finalData[i], formattedTime)
			case reflect.TypeOf(datatypes.JSONMap{}):
				jsonMap, ok := field.Interface().(datatypes.JSONMap)
				if !ok || jsonMap == nil || len(jsonMap) == 0 {
					finalData[i] = append(finalData[i], "") // Если пустой JSONMap, записываем пустую строку
				} else {
					jsonBytes, err := json.Marshal(jsonMap)
					if err != nil {
						finalData[i] = append(finalData[i], "") // В случае ошибки записываем пустую строку
					} else {
						finalData[i] = append(finalData[i], string(jsonBytes)) // Добавляем строку JSON
					}
				}
			default:
				finalData[i] = append(finalData[i], field.Interface())
			}

			//хедеры запиываются один раз, но их порядок должен совпадать с датой
			if len(headersNames) < headersCounter {
				headersNames = append(headersNames, matchedIndexes[j])
			}
		}
	}

	return headersNames, finalData
}
