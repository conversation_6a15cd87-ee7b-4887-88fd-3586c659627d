edition = "2023";

package processing.smart_pay.smart_pay;

option go_package = "git.local/sensitive/innerpb/processing/grpc";

import "mvp/proto/logger.proto";

service SmartPay {
  rpc ApplePaySession(ApplePaySessionRequestV1) returns (ApplePaySessionResponseV1) {}
  rpc DecodeToken(DecodeTokenRequestV1) returns (DecodeTokenResponseV1) {}
  rpc DecryptGPayToken(DecryptGPayTokenRequestV1) returns (DecryptGPayTokenResponseV1) {}
  rpc GetGPayCredentials(GetGPayCredentialsRequestV1) returns (GetGPayCredentialsResponseV1) {}
}

message ApplePaySessionRequestV1{
  string validationURL = 1;
}

message ApplePaySessionResponseV1{
  string response = 1;
}

message DecodeTokenRequestV1{
  string transaction_identifier = 1;
  PaymentMethod method = 2;
  PaymentData data = 3;
}

message PaymentMethod{
  string type = 1;
  string network = 2;
  string display_name = 3;
}

message PaymentData{
  string version = 1;
  bytes signature = 2;
  Header header = 3;
  bytes data = 4;
}

message Header {
  string application_name = 1;
  bytes ephemeral_public_key = 2;
  bytes wrapped_key = 3;
  bytes public_key_hash = 4;
  string transaction_id = 5;
}

message DecodeTokenResponseV1{
  bytes application_primary_account_number = 1 [(mvp.FieldLoggerLevel) = Hidden];
  bytes application_expiration_month = 2 [(mvp.FieldLoggerLevel) = Hidden];
  bytes application_expiration_year = 3 [(mvp.FieldLoggerLevel) = Hidden];
  string currency_code = 4;
  double transaction_amount = 5;
  bytes cardholder_name = 6;
  string device_manufacturer_identifier = 7;
  string payment_data_type = 8;
  PaymentDataResponse payment_data = 9;
  string version = 10;
}

message PaymentDataResponse{
  bytes online_payment_cryptogram = 1;
  string eci_indicator = 2;
  bytes emv_data = 3;
  string encrypted_pin_data = 4;
}

message DecryptGPayTokenRequestV1{
  string token = 1;
}

message DecryptGPayTokenResponseV1{
  string gateway_merchant_id = 1;
  string message_expiration = 2;
  string message_id = 3;
  string payment_method = 4;
  PaymentMethodDetails payment_method_details = 5;
}

message PaymentMethodDetails{
  bytes expiration_year = 1;
  bytes expiration_month = 2;
  bytes pan = 3 [(mvp.FieldLoggerLevel) = Hidden];
  string auth_method = 4;
  string eci_indicator = 5;
  string cryptogram = 6;
  AssuranceDetails assurance_details = 7;
}

message AssuranceDetails{
  bool account_verified = 1;
  bool card_holder_authenticated = 2;
}

message GetGPayCredentialsRequestV1{
  uint64 project_id = 1;
}

message GetGPayCredentialsResponseV1{
  string google_pay_gateway_merchant = 1;
  string google_pay_merchant_id = 2;
}