// Code generated by protoc-gen-go-mvp. DO NOT EDIT.
// versions:
// - protoc-gen-go-mvp v2.0.1
// created by mvp authors

package grpc

func (x BillingOperationBalanceType) Code() string {
	switch x {
	case BillingOperationBalanceType_BillingOperationBalanceTypeUnknown:
		return "unknown"
	case BillingOperationBalanceType_BillingOperationBalanceTypeMain:
		return "main"
	case BillingOperationBalanceType_BillingOperationBalanceTypeCredit:
		return "credit"
	default:
		return "unknown"
	}
}

func (x BillingOperationBalanceType) Name() string {
	switch x {
	case BillingOperationBalanceType_BillingOperationBalanceTypeUnknown:
		return "unknown operation balance type"
	case BillingOperationBalanceType_BillingOperationBalanceTypeMain:
		return "Основной баланс"
	case BillingOperationBalanceType_BillingOperationBalanceTypeCredit:
		return "Кредитный баланс"
	default:
		return "unknown operation balance type"
	}
}

// Created reference to BillingOperationBalanceType

//	|	BillingOperationBalanceType                                   	|	Code     	|	Name                             	|
//	|	BillingOperationBalanceType_BillingOperationBalanceTypeUnknown	|	"unknown"	|	"unknown operation balance type" 	|
//	|	BillingOperationBalanceType_BillingOperationBalanceTypeMain   	|	"main"   	|	"Основной баланс"                	|
//	|	BillingOperationBalanceType_BillingOperationBalanceTypeCredit 	|	"credit" 	|	"Кредитный баланс"               	|

var SliceBillingOperationBalanceTypeRefs *sliceBillingOperationBalanceTypeRefs

type sliceBillingOperationBalanceTypeRefs struct{}

func (*sliceBillingOperationBalanceTypeRefs) Code(slice ...BillingOperationBalanceType) []string {
	var result []string
	for _, val := range slice {
		result = append(result, val.Code())
	}

	return result
}

func (*sliceBillingOperationBalanceTypeRefs) Name(slice ...BillingOperationBalanceType) []string {
	var result []string
	for _, val := range slice {
		result = append(result, val.Name())
	}

	return result
}

func (x BillingOperationType) Code() string {
	switch x {
	case BillingOperationType_BillingOperationTypeUnknown:
		return "unknown"
	case BillingOperationType_BillingOperationTypePayIn:
		return "pay_in"
	case BillingOperationType_BillingOperationTypePayOut:
		return "pay_out"
	case BillingOperationType_BillingOperationTypeRefund:
		return "refund"
	case BillingOperationType_BillingOperationTypeInTransfer:
		return "in_transfer"
	case BillingOperationType_BillingOperationTypeOutTransfer:
		return "out_transfer"
	case BillingOperationType_BillingOperationTypePayInCommissionOutAcquirer:
		return "pay_in_cms_out_acr"
	case BillingOperationType_BillingOperationTypePayInCommissionInSystem:
		return "pay_in_cms_in_sys"
	case BillingOperationType_BillingOperationTypePayInCommissionOutSystem:
		return "pay_in_cms_out_sys"
	case BillingOperationType_BillingOperationTypePayInCommissionInMerchant:
		return "pay_in_cms_in_mrc"
	case BillingOperationType_BillingOperationTypePayInCommissionOutMerchant:
		return "pay_in_cms_out_mrc"
	case BillingOperationType_BillingOperationTypeInternalInTransfer:
		return "internal_in_transfer"
	case BillingOperationType_BillingOperationTypeInternalOutTransfer:
		return "internal_out_transfer"
	case BillingOperationType_BillingOperationTypeSplitOutTransfer:
		return "split_out_transfer"
	case BillingOperationType_BillingOperationTypeSplitInTransfer:
		return "split_in_transfer"
	case BillingOperationType_BillingOperationTypePayOutCommissionOutAcquirer:
		return "pay_out_cms_out_acr"
	case BillingOperationType_BillingOperationTypePayOutCommissionInSystem:
		return "pay_out_cms_in_sys"
	case BillingOperationType_BillingOperationTypePayOutCommissionOutSystem:
		return "pay_out_cms_out_sys"
	case BillingOperationType_BillingOperationTypePayOutCommissionInMerchant:
		return "pay_out_cms_in_mrc"
	case BillingOperationType_BillingOperationTypePayOutCommissionOutMerchant:
		return "pay_out_cms_out_mrc"
	case BillingOperationType_BillingOperationTypeSplitCommissionInSystem:
		return "split_cms_in_sys"
	case BillingOperationType_BillingOperationTypeSplitCommissionOutSystem:
		return "split_cms_out_sys"
	case BillingOperationType_BillingOperationTypeSplitCommissionInMerchant:
		return "split_cms_in_mrc"
	case BillingOperationType_BillingOperationTypeSplitCommissionOutMerchant:
		return "split_cms_out_mrc"
	default:
		return "unknown"
	}
}

func (x BillingOperationType) Name() string {
	switch x {
	case BillingOperationType_BillingOperationTypeUnknown:
		return "unknown operation type"
	case BillingOperationType_BillingOperationTypePayIn:
		return "Прием"
	case BillingOperationType_BillingOperationTypePayOut:
		return "Вывод"
	case BillingOperationType_BillingOperationTypeRefund:
		return "Возврат"
	case BillingOperationType_BillingOperationTypeInTransfer:
		return "Фин. операция прием"
	case BillingOperationType_BillingOperationTypeOutTransfer:
		return "Фин. операция вывод"
	case BillingOperationType_BillingOperationTypePayInCommissionOutAcquirer:
		return "Удержание комиссии эквайера (прием)"
	case BillingOperationType_BillingOperationTypePayInCommissionInSystem:
		return "Пополнение комиссии Тарлан (прием)"
	case BillingOperationType_BillingOperationTypePayInCommissionOutSystem:
		return "Удержание комиссии Тарлан (прием)"
	case BillingOperationType_BillingOperationTypePayInCommissionInMerchant:
		return "Пополнение комиссии мерчанта (прием)"
	case BillingOperationType_BillingOperationTypePayInCommissionOutMerchant:
		return "Удержание комиссии мерчанта (прием)"
	case BillingOperationType_BillingOperationTypeInternalInTransfer:
		return "Внутренний прием"
	case BillingOperationType_BillingOperationTypeInternalOutTransfer:
		return "Внутренний вывод"
	case BillingOperationType_BillingOperationTypeSplitOutTransfer:
		return "Удержание сплит. средств"
	case BillingOperationType_BillingOperationTypeSplitInTransfer:
		return "Пополнение сплит. средств"
	case BillingOperationType_BillingOperationTypePayOutCommissionOutAcquirer:
		return "Удержание комиссии эквайера (вывод)"
	case BillingOperationType_BillingOperationTypePayOutCommissionInSystem:
		return "Пополнение комиссии Тарлан (вывод)"
	case BillingOperationType_BillingOperationTypePayOutCommissionOutSystem:
		return "Удержание комиссии Тарлан (вывод)"
	case BillingOperationType_BillingOperationTypePayOutCommissionInMerchant:
		return "Пополнение комиссии мерчанта (вывод)"
	case BillingOperationType_BillingOperationTypePayOutCommissionOutMerchant:
		return "Удержание комиссии мерчанта (вывод)"
	case BillingOperationType_BillingOperationTypeSplitCommissionInSystem:
		return "Пополнение комиссии Тарлан (сплитование)"
	case BillingOperationType_BillingOperationTypeSplitCommissionOutSystem:
		return "Удержание комиссии Тарлан (сплитование)"
	case BillingOperationType_BillingOperationTypeSplitCommissionInMerchant:
		return "Пополнение комиссии мерчанта (сплитование)"
	case BillingOperationType_BillingOperationTypeSplitCommissionOutMerchant:
		return "Удержание комиссии мерчанта (сплитование)"
	default:
		return "unknown operation type"
	}
}

// Created reference to BillingOperationType

//	|	BillingOperationType                                                	|	Code                   	|	Name                                                                             	|
//	|	BillingOperationType_BillingOperationTypeUnknown                    	|	"unknown"              	|	"unknown operation type"                                                         	|
//	|	BillingOperationType_BillingOperationTypePayIn                      	|	"pay_in"               	|	"Прием"                                                                          	|
//	|	BillingOperationType_BillingOperationTypePayOut                     	|	"pay_out"              	|	"Вывод"                                                                          	|
//	|	BillingOperationType_BillingOperationTypeRefund                     	|	"refund"               	|	"Возврат"                                                                        	|
//	|	BillingOperationType_BillingOperationTypeInTransfer                 	|	"in_transfer"          	|	"Фин. операция прием"                                                            	|
//	|	BillingOperationType_BillingOperationTypeOutTransfer                	|	"out_transfer"         	|	"Фин. операция вывод"                                                            	|
//	|	BillingOperationType_BillingOperationTypePayInCommissionOutAcquirer 	|	"pay_in_cms_out_acr"   	|	"Удержание комиссии эквайера (прием)"                                            	|
//	|	BillingOperationType_BillingOperationTypePayInCommissionInSystem    	|	"pay_in_cms_in_sys"    	|	"Пополнение комиссии Тарлан (прием)"                                             	|
//	|	BillingOperationType_BillingOperationTypePayInCommissionOutSystem   	|	"pay_in_cms_out_sys"   	|	"Удержание комиссии Тарлан (прием)"                                              	|
//	|	BillingOperationType_BillingOperationTypePayInCommissionInMerchant  	|	"pay_in_cms_in_mrc"    	|	"Пополнение комиссии мерчанта (прием)"                                           	|
//	|	BillingOperationType_BillingOperationTypePayInCommissionOutMerchant 	|	"pay_in_cms_out_mrc"   	|	"Удержание комиссии мерчанта (прием)"                                            	|
//	|	BillingOperationType_BillingOperationTypeInternalInTransfer         	|	"internal_in_transfer" 	|	"Внутренний прием"                                                               	|
//	|	BillingOperationType_BillingOperationTypeInternalOutTransfer        	|	"internal_out_transfer"	|	"Внутренний вывод"                                                               	|
//	|	BillingOperationType_BillingOperationTypeSplitOutTransfer           	|	"split_out_transfer"   	|	"Удержание сплит. средств"                                                       	|
//	|	BillingOperationType_BillingOperationTypeSplitInTransfer            	|	"split_in_transfer"    	|	"Пополнение сплит. средств"                                                      	|
//	|	BillingOperationType_BillingOperationTypePayOutCommissionOutAcquirer	|	"pay_out_cms_out_acr"  	|	"Удержание комиссии эквайера (вывод)"                                            	|
//	|	BillingOperationType_BillingOperationTypePayOutCommissionInSystem   	|	"pay_out_cms_in_sys"   	|	"Пополнение комиссии Тарлан (вывод)"                                             	|
//	|	BillingOperationType_BillingOperationTypePayOutCommissionOutSystem  	|	"pay_out_cms_out_sys"  	|	"Удержание комиссии Тарлан (вывод)"                                              	|
//	|	BillingOperationType_BillingOperationTypePayOutCommissionInMerchant 	|	"pay_out_cms_in_mrc"   	|	"Пополнение комиссии мерчанта (вывод)"                                           	|
//	|	BillingOperationType_BillingOperationTypePayOutCommissionOutMerchant	|	"pay_out_cms_out_mrc"  	|	"Удержание комиссии мерчанта (вывод)"                                            	|
//	|	BillingOperationType_BillingOperationTypeSplitCommissionInSystem    	|	"split_cms_in_sys"     	|	"Пополнение комиссии Тарлан (сплитование)"                                       	|
//	|	BillingOperationType_BillingOperationTypeSplitCommissionOutSystem   	|	"split_cms_out_sys"    	|	"Удержание комиссии Тарлан (сплитование)"                                        	|
//	|	BillingOperationType_BillingOperationTypeSplitCommissionInMerchant  	|	"split_cms_in_mrc"     	|	"Пополнение комиссии мерчанта (сплитование)"                                     	|
//	|	BillingOperationType_BillingOperationTypeSplitCommissionOutMerchant 	|	"split_cms_out_mrc"    	|	"Удержание комиссии мерчанта (сплитование)"                                      	|

var SliceBillingOperationTypeRefs *sliceBillingOperationTypeRefs

type sliceBillingOperationTypeRefs struct{}

func (*sliceBillingOperationTypeRefs) Code(slice ...BillingOperationType) []string {
	var result []string
	for _, val := range slice {
		result = append(result, val.Code())
	}

	return result
}

func (*sliceBillingOperationTypeRefs) Name(slice ...BillingOperationType) []string {
	var result []string
	for _, val := range slice {
		result = append(result, val.Name())
	}

	return result
}

func (x BillingOperationStatus) Code() string {
	switch x {
	case BillingOperationStatus_BillingOperationStatusUnknown:
		return "unknown"
	case BillingOperationStatus_BillingOperationStatusFinalized:
		return "finalized"
	case BillingOperationStatus_BillingOperationStatusInProgress:
		return "in_progress"
	default:
		return "unknown"
	}
}

func (x BillingOperationStatus) Name() string {
	switch x {
	case BillingOperationStatus_BillingOperationStatusUnknown:
		return "unknown operation status"
	case BillingOperationStatus_BillingOperationStatusFinalized:
		return "Операция финализирована"
	case BillingOperationStatus_BillingOperationStatusInProgress:
		return "Операция в процессе"
	default:
		return "unknown operation status"
	}
}

// Created reference to BillingOperationStatus

//	|	BillingOperationStatus                                 	|	Code         	|	Name                                           	|
//	|	BillingOperationStatus_BillingOperationStatusUnknown   	|	"unknown"    	|	"unknown operation status"                     	|
//	|	BillingOperationStatus_BillingOperationStatusFinalized 	|	"finalized"  	|	"Операция финализирована"                      	|
//	|	BillingOperationStatus_BillingOperationStatusInProgress	|	"in_progress"	|	"Операция в процессе"                          	|

var SliceBillingOperationStatusRefs *sliceBillingOperationStatusRefs

type sliceBillingOperationStatusRefs struct{}

func (*sliceBillingOperationStatusRefs) Code(slice ...BillingOperationStatus) []string {
	var result []string
	for _, val := range slice {
		result = append(result, val.Code())
	}

	return result
}

func (*sliceBillingOperationStatusRefs) Name(slice ...BillingOperationStatus) []string {
	var result []string
	for _, val := range slice {
		result = append(result, val.Name())
	}

	return result
}

func (x BillingOperationTypeGroup) Code() string {
	switch x {
	case BillingOperationTypeGroup_BillingOperationTypeGroupUnknown:
		return "unknown"
	case BillingOperationTypeGroup_BillingOperationTypeGroupIn:
		return "in"
	case BillingOperationTypeGroup_BillingOperationTypeGroupOut:
		return "out"
	case BillingOperationTypeGroup_BillingOperationTypeGroupRefund:
		return "refund"
	case BillingOperationTypeGroup_BillingOperationTypeGroupPayIn:
		return "pay_in"
	default:
		return "unknown"
	}
}

func (x BillingOperationTypeGroup) Name() string {
	switch x {
	case BillingOperationTypeGroup_BillingOperationTypeGroupUnknown:
		return "unknown operation type group"
	case BillingOperationTypeGroup_BillingOperationTypeGroupIn:
		return "in"
	case BillingOperationTypeGroup_BillingOperationTypeGroupOut:
		return "out"
	case BillingOperationTypeGroup_BillingOperationTypeGroupRefund:
		return "refund"
	case BillingOperationTypeGroup_BillingOperationTypeGroupPayIn:
		return "pay_in"
	default:
		return "unknown operation type group"
	}
}

// Created reference to BillingOperationTypeGroup

//	|	BillingOperationTypeGroup                                 	|	Code     	|	Name                          	|
//	|	BillingOperationTypeGroup_BillingOperationTypeGroupUnknown	|	"unknown"	|	"unknown operation type group"	|
//	|	BillingOperationTypeGroup_BillingOperationTypeGroupIn     	|	"in"     	|	"in"                          	|
//	|	BillingOperationTypeGroup_BillingOperationTypeGroupOut    	|	"out"    	|	"out"                         	|
//	|	BillingOperationTypeGroup_BillingOperationTypeGroupRefund 	|	"refund" 	|	"refund"                      	|
//	|	BillingOperationTypeGroup_BillingOperationTypeGroupPayIn  	|	"pay_in" 	|	"pay_in"                      	|

var SliceBillingOperationTypeGroupRefs *sliceBillingOperationTypeGroupRefs

type sliceBillingOperationTypeGroupRefs struct{}

func (*sliceBillingOperationTypeGroupRefs) Code(slice ...BillingOperationTypeGroup) []string {
	var result []string
	for _, val := range slice {
		result = append(result, val.Code())
	}

	return result
}

func (*sliceBillingOperationTypeGroupRefs) Name(slice ...BillingOperationTypeGroup) []string {
	var result []string
	for _, val := range slice {
		result = append(result, val.Name())
	}

	return result
}

func (x BillingEarnType) Code() string {
	switch x {
	case BillingEarnType_BillingEarnTypeUnknown:
		return "unknown"
	case BillingEarnType_BillingEarnTypeCommission:
		return "commission"
	case BillingEarnType_BillingEarnTypeFee:
		return "fee"
	default:
		return "unknown"
	}
}

func (x BillingEarnType) Name() string {
	switch x {
	case BillingEarnType_BillingEarnTypeUnknown:
		return "unknown earn type"
	case BillingEarnType_BillingEarnTypeCommission:
		return "Комиссионная модель дохода"
	case BillingEarnType_BillingEarnTypeFee:
		return "Подписочная модель дохода"
	default:
		return "unknown earn type"
	}
}

// Created reference to BillingEarnType

//	|	BillingEarnType                          	|	Code        	|	Name                                                	|
//	|	BillingEarnType_BillingEarnTypeUnknown   	|	"unknown"   	|	"unknown earn type"                                 	|
//	|	BillingEarnType_BillingEarnTypeCommission	|	"commission"	|	"Комиссионная модель дохода"                        	|
//	|	BillingEarnType_BillingEarnTypeFee       	|	"fee"       	|	"Подписочная модель дохода"                         	|

var SliceBillingEarnTypeRefs *sliceBillingEarnTypeRefs

type sliceBillingEarnTypeRefs struct{}

func (*sliceBillingEarnTypeRefs) Code(slice ...BillingEarnType) []string {
	var result []string
	for _, val := range slice {
		result = append(result, val.Code())
	}

	return result
}

func (*sliceBillingEarnTypeRefs) Name(slice ...BillingEarnType) []string {
	var result []string
	for _, val := range slice {
		result = append(result, val.Name())
	}

	return result
}

func (x BillingBalanceOwnerType) Code() string {
	switch x {
	case BillingBalanceOwnerType_BillingBalanceOwnerTypeUnknown:
		return "unknown"
	case BillingBalanceOwnerType_BillingBalanceOwnerTypeSystem:
		return "system"
	case BillingBalanceOwnerType_BillingBalanceOwnerTypeMerchant:
		return "merchant"
	case BillingBalanceOwnerType_BillingBalanceOwnerTypeProject:
		return "project"
	default:
		return "unknown"
	}
}

func (x BillingBalanceOwnerType) Name() string {
	switch x {
	case BillingBalanceOwnerType_BillingBalanceOwnerTypeUnknown:
		return "unknown balance owner type"
	case BillingBalanceOwnerType_BillingBalanceOwnerTypeSystem:
		return "Система"
	case BillingBalanceOwnerType_BillingBalanceOwnerTypeMerchant:
		return "Мерчант"
	case BillingBalanceOwnerType_BillingBalanceOwnerTypeProject:
		return "Проект"
	default:
		return "unknown balance owner type"
	}
}

// Created reference to BillingBalanceOwnerType

//	|	BillingBalanceOwnerType                                	|	Code      	|	Name                        	|
//	|	BillingBalanceOwnerType_BillingBalanceOwnerTypeUnknown 	|	"unknown" 	|	"unknown balance owner type"	|
//	|	BillingBalanceOwnerType_BillingBalanceOwnerTypeSystem  	|	"system"  	|	"Система"                   	|
//	|	BillingBalanceOwnerType_BillingBalanceOwnerTypeMerchant	|	"merchant"	|	"Мерчант"                   	|
//	|	BillingBalanceOwnerType_BillingBalanceOwnerTypeProject 	|	"project" 	|	"Проект"                    	|

var SliceBillingBalanceOwnerTypeRefs *sliceBillingBalanceOwnerTypeRefs

type sliceBillingBalanceOwnerTypeRefs struct{}

func (*sliceBillingBalanceOwnerTypeRefs) Code(slice ...BillingBalanceOwnerType) []string {
	var result []string
	for _, val := range slice {
		result = append(result, val.Code())
	}

	return result
}

func (*sliceBillingBalanceOwnerTypeRefs) Name(slice ...BillingBalanceOwnerType) []string {
	var result []string
	for _, val := range slice {
		result = append(result, val.Name())
	}

	return result
}

func (x BillingBalanceOwnerStatus) Code() string {
	switch x {
	case BillingBalanceOwnerStatus_BillingBalanceOwnerStatusUnknown:
		return "unknown"
	case BillingBalanceOwnerStatus_BillingBalanceOwnerStatusActive:
		return "active"
	case BillingBalanceOwnerStatus_BillingBalanceOwnerStatusInactive:
		return "inactive"
	default:
		return "unknown"
	}
}

func (x BillingBalanceOwnerStatus) Name() string {
	switch x {
	case BillingBalanceOwnerStatus_BillingBalanceOwnerStatusUnknown:
		return "unknown balance owner status"
	case BillingBalanceOwnerStatus_BillingBalanceOwnerStatusActive:
		return "Активный"
	case BillingBalanceOwnerStatus_BillingBalanceOwnerStatusInactive:
		return "Неактивный"
	default:
		return "unknown balance owner status"
	}
}

// Created reference to BillingBalanceOwnerStatus

//	|	BillingBalanceOwnerStatus                                  	|	Code      	|	Name                          	|
//	|	BillingBalanceOwnerStatus_BillingBalanceOwnerStatusUnknown 	|	"unknown" 	|	"unknown balance owner status"	|
//	|	BillingBalanceOwnerStatus_BillingBalanceOwnerStatusActive  	|	"active"  	|	"Активный"                    	|
//	|	BillingBalanceOwnerStatus_BillingBalanceOwnerStatusInactive	|	"inactive"	|	"Неактивный"                  	|

var SliceBillingBalanceOwnerStatusRefs *sliceBillingBalanceOwnerStatusRefs

type sliceBillingBalanceOwnerStatusRefs struct{}

func (*sliceBillingBalanceOwnerStatusRefs) Code(slice ...BillingBalanceOwnerStatus) []string {
	var result []string
	for _, val := range slice {
		result = append(result, val.Code())
	}

	return result
}

func (*sliceBillingBalanceOwnerStatusRefs) Name(slice ...BillingBalanceOwnerStatus) []string {
	var result []string
	for _, val := range slice {
		result = append(result, val.Name())
	}

	return result
}

func (x BillingBalanceCreditStatus) Code() string {
	switch x {
	case BillingBalanceCreditStatus_BillingBalanceCreditStatusUnknown:
		return "unknown"
	case BillingBalanceCreditStatus_BillingBalanceCreditStatusActive:
		return "active"
	case BillingBalanceCreditStatus_BillingBalanceCreditStatusInactive:
		return "inactive"
	case BillingBalanceCreditStatus_BillingBalanceCreditStatusIdle:
		return "idle"
	default:
		return "unknown"
	}
}

func (x BillingBalanceCreditStatus) Name() string {
	switch x {
	case BillingBalanceCreditStatus_BillingBalanceCreditStatusUnknown:
		return "unknown balance credit status"
	case BillingBalanceCreditStatus_BillingBalanceCreditStatusActive:
		return "Активный"
	case BillingBalanceCreditStatus_BillingBalanceCreditStatusInactive:
		return "Неактивный"
	case BillingBalanceCreditStatus_BillingBalanceCreditStatusIdle:
		return "В ожидании активации"
	default:
		return "unknown balance credit status"
	}
}

// Created reference to BillingBalanceCreditStatus

//	|	BillingBalanceCreditStatus                                   	|	Code      	|	Name                                    	|
//	|	BillingBalanceCreditStatus_BillingBalanceCreditStatusUnknown 	|	"unknown" 	|	"unknown balance credit status"         	|
//	|	BillingBalanceCreditStatus_BillingBalanceCreditStatusActive  	|	"active"  	|	"Активный"                              	|
//	|	BillingBalanceCreditStatus_BillingBalanceCreditStatusInactive	|	"inactive"	|	"Неактивный"                            	|
//	|	BillingBalanceCreditStatus_BillingBalanceCreditStatusIdle    	|	"idle"    	|	"В ожидании активации"                  	|

var SliceBillingBalanceCreditStatusRefs *sliceBillingBalanceCreditStatusRefs

type sliceBillingBalanceCreditStatusRefs struct{}

func (*sliceBillingBalanceCreditStatusRefs) Code(slice ...BillingBalanceCreditStatus) []string {
	var result []string
	for _, val := range slice {
		result = append(result, val.Code())
	}

	return result
}

func (*sliceBillingBalanceCreditStatusRefs) Name(slice ...BillingBalanceCreditStatus) []string {
	var result []string
	for _, val := range slice {
		result = append(result, val.Name())
	}

	return result
}

func (x BillingBalanceAccountStatus) Code() string {
	switch x {
	case BillingBalanceAccountStatus_BillingBalanceAccountStatusUnknown:
		return "unknown"
	case BillingBalanceAccountStatus_BillingBalanceAccountStatusActive:
		return "active"
	case BillingBalanceAccountStatus_BillingBalanceAccountStatusInactive:
		return "inactive"
	default:
		return "unknown"
	}
}

func (x BillingBalanceAccountStatus) Name() string {
	switch x {
	case BillingBalanceAccountStatus_BillingBalanceAccountStatusUnknown:
		return "unknown balance account status"
	case BillingBalanceAccountStatus_BillingBalanceAccountStatusActive:
		return "Активный"
	case BillingBalanceAccountStatus_BillingBalanceAccountStatusInactive:
		return "Неактивный"
	default:
		return "unknown balance account status"
	}
}

// Created reference to BillingBalanceAccountStatus

//	|	BillingBalanceAccountStatus                                    	|	Code      	|	Name                            	|
//	|	BillingBalanceAccountStatus_BillingBalanceAccountStatusUnknown 	|	"unknown" 	|	"unknown balance account status"	|
//	|	BillingBalanceAccountStatus_BillingBalanceAccountStatusActive  	|	"active"  	|	"Активный"                      	|
//	|	BillingBalanceAccountStatus_BillingBalanceAccountStatusInactive	|	"inactive"	|	"Неактивный"                    	|

var SliceBillingBalanceAccountStatusRefs *sliceBillingBalanceAccountStatusRefs

type sliceBillingBalanceAccountStatusRefs struct{}

func (*sliceBillingBalanceAccountStatusRefs) Code(slice ...BillingBalanceAccountStatus) []string {
	var result []string
	for _, val := range slice {
		result = append(result, val.Code())
	}

	return result
}

func (*sliceBillingBalanceAccountStatusRefs) Name(slice ...BillingBalanceAccountStatus) []string {
	var result []string
	for _, val := range slice {
		result = append(result, val.Name())
	}

	return result
}

func (x BillingOperationMode) Code() string {
	switch x {
	case BillingOperationMode_BillingOperationModeUnknown:
		return "unknown"
	case BillingOperationMode_BillingOperationModeRealtime:
		return "realtime"
	case BillingOperationMode_BillingOperationModeBankProcess:
		return "bank_process"
	default:
		return "unknown"
	}
}

func (x BillingOperationMode) Name() string {
	switch x {
	case BillingOperationMode_BillingOperationModeUnknown:
		return "unknown operation mode"
	case BillingOperationMode_BillingOperationModeRealtime:
		return "Операция мгновенно подтверждена"
	case BillingOperationMode_BillingOperationModeBankProcess:
		return "Операция в обработке у банка"
	default:
		return "unknown operation mode"
	}
}

// Created reference to BillingOperationMode

//	|	BillingOperationMode                                	|	Code          	|	Name                                                          	|
//	|	BillingOperationMode_BillingOperationModeUnknown    	|	"unknown"     	|	"unknown operation mode"                                      	|
//	|	BillingOperationMode_BillingOperationModeRealtime   	|	"realtime"    	|	"Операция мгновенно подтверждена"                             	|
//	|	BillingOperationMode_BillingOperationModeBankProcess	|	"bank_process"	|	"Операция в обработке у банка"                                	|

var SliceBillingOperationModeRefs *sliceBillingOperationModeRefs

type sliceBillingOperationModeRefs struct{}

func (*sliceBillingOperationModeRefs) Code(slice ...BillingOperationMode) []string {
	var result []string
	for _, val := range slice {
		result = append(result, val.Code())
	}

	return result
}

func (*sliceBillingOperationModeRefs) Name(slice ...BillingOperationMode) []string {
	var result []string
	for _, val := range slice {
		result = append(result, val.Name())
	}

	return result
}

func (x BillingOperationTypeGroupRelation) OperationMode() BillingOperationMode {
	switch x {
	case BillingOperationTypeGroupRelation_BillingOperationTypeGroupRelationUnknown:
		return BillingOperationMode_BillingOperationModeUnknown
	case BillingOperationTypeGroupRelation_BillingOperationTypeGroupRelationPayIn:
		return BillingOperationMode_BillingOperationModeBankProcess
	case BillingOperationTypeGroupRelation_BillingOperationTypeGroupRelationPayOut:
		return BillingOperationMode_BillingOperationModeRealtime
	case BillingOperationTypeGroupRelation_BillingOperationTypeGroupRelationRefund:
		return BillingOperationMode_BillingOperationModeBankProcess
	case BillingOperationTypeGroupRelation_BillingOperationTypeGroupRelationInTransfer:
		return BillingOperationMode_BillingOperationModeRealtime
	case BillingOperationTypeGroupRelation_BillingOperationTypeGroupRelationOutTransfer:
		return BillingOperationMode_BillingOperationModeRealtime
	case BillingOperationTypeGroupRelation_BillingOperationTypeGroupRelationPayInCmsOutAcquirer:
		return BillingOperationMode_BillingOperationModeBankProcess
	case BillingOperationTypeGroupRelation_BillingOperationTypeGroupRelationPayInCmsInSystem:
		return BillingOperationMode_BillingOperationModeBankProcess
	case BillingOperationTypeGroupRelation_BillingOperationTypeGroupRelationPayInCmsOutSystem:
		return BillingOperationMode_BillingOperationModeBankProcess
	case BillingOperationTypeGroupRelation_BillingOperationTypeGroupRelationPayInCmsInMerchant:
		return BillingOperationMode_BillingOperationModeBankProcess
	case BillingOperationTypeGroupRelation_BillingOperationTypeGroupRelationPayInCmsOutMerchant:
		return BillingOperationMode_BillingOperationModeBankProcess
	case BillingOperationTypeGroupRelation_BillingOperationTypeGroupRelationInternalInTransfer:
		return BillingOperationMode_BillingOperationModeRealtime
	case BillingOperationTypeGroupRelation_BillingOperationTypeGroupRelationInternalOutTransfer:
		return BillingOperationMode_BillingOperationModeRealtime
	case BillingOperationTypeGroupRelation_BillingOperationTypeGroupRelationSplitOutTransfer:
		return BillingOperationMode_BillingOperationModeRealtime
	case BillingOperationTypeGroupRelation_BillingOperationTypeGroupRelationSplitInTransfer:
		return BillingOperationMode_BillingOperationModeRealtime
	case BillingOperationTypeGroupRelation_BillingOperationTypeGroupRelationPayOutCmsOutAcquirer:
		return BillingOperationMode_BillingOperationModeRealtime
	case BillingOperationTypeGroupRelation_BillingOperationTypeGroupRelationPayOutCmsInSystem:
		return BillingOperationMode_BillingOperationModeRealtime
	case BillingOperationTypeGroupRelation_BillingOperationTypeGroupRelationPayOutCmsOutSystem:
		return BillingOperationMode_BillingOperationModeRealtime
	case BillingOperationTypeGroupRelation_BillingOperationTypeGroupRelationPayOutCmsInMerchant:
		return BillingOperationMode_BillingOperationModeRealtime
	case BillingOperationTypeGroupRelation_BillingOperationTypeGroupRelationPayOutCmsOutMerchant:
		return BillingOperationMode_BillingOperationModeRealtime
	case BillingOperationTypeGroupRelation_BillingOperationTypeGroupRelationSplitCmsInSystem:
		return BillingOperationMode_BillingOperationModeRealtime
	case BillingOperationTypeGroupRelation_BillingOperationTypeGroupRelationSplitCmsOutSystem:
		return BillingOperationMode_BillingOperationModeRealtime
	case BillingOperationTypeGroupRelation_BillingOperationTypeGroupRelationSplitCmsInMerchant:
		return BillingOperationMode_BillingOperationModeRealtime
	case BillingOperationTypeGroupRelation_BillingOperationTypeGroupRelationSplitCmsOutMerchant:
		return BillingOperationMode_BillingOperationModeRealtime
	default:
		return BillingOperationMode_BillingOperationModeUnknown
	}
}

func (x BillingOperationTypeGroupRelation) OperationType() BillingOperationType {
	switch x {
	case BillingOperationTypeGroupRelation_BillingOperationTypeGroupRelationUnknown:
		return BillingOperationType_BillingOperationTypePayIn
	case BillingOperationTypeGroupRelation_BillingOperationTypeGroupRelationPayIn:
		return BillingOperationType_BillingOperationTypePayIn
	case BillingOperationTypeGroupRelation_BillingOperationTypeGroupRelationPayOut:
		return BillingOperationType_BillingOperationTypePayOut
	case BillingOperationTypeGroupRelation_BillingOperationTypeGroupRelationRefund:
		return BillingOperationType_BillingOperationTypeRefund
	case BillingOperationTypeGroupRelation_BillingOperationTypeGroupRelationInTransfer:
		return BillingOperationType_BillingOperationTypeInTransfer
	case BillingOperationTypeGroupRelation_BillingOperationTypeGroupRelationOutTransfer:
		return BillingOperationType_BillingOperationTypeOutTransfer
	case BillingOperationTypeGroupRelation_BillingOperationTypeGroupRelationPayInCmsOutAcquirer:
		return BillingOperationType_BillingOperationTypePayInCommissionOutAcquirer
	case BillingOperationTypeGroupRelation_BillingOperationTypeGroupRelationPayInCmsInSystem:
		return BillingOperationType_BillingOperationTypePayInCommissionInSystem
	case BillingOperationTypeGroupRelation_BillingOperationTypeGroupRelationPayInCmsOutSystem:
		return BillingOperationType_BillingOperationTypePayInCommissionOutSystem
	case BillingOperationTypeGroupRelation_BillingOperationTypeGroupRelationPayInCmsInMerchant:
		return BillingOperationType_BillingOperationTypePayInCommissionInMerchant
	case BillingOperationTypeGroupRelation_BillingOperationTypeGroupRelationPayInCmsOutMerchant:
		return BillingOperationType_BillingOperationTypePayInCommissionOutMerchant
	case BillingOperationTypeGroupRelation_BillingOperationTypeGroupRelationInternalInTransfer:
		return BillingOperationType_BillingOperationTypeInternalInTransfer
	case BillingOperationTypeGroupRelation_BillingOperationTypeGroupRelationInternalOutTransfer:
		return BillingOperationType_BillingOperationTypeInternalOutTransfer
	case BillingOperationTypeGroupRelation_BillingOperationTypeGroupRelationSplitOutTransfer:
		return BillingOperationType_BillingOperationTypeSplitOutTransfer
	case BillingOperationTypeGroupRelation_BillingOperationTypeGroupRelationSplitInTransfer:
		return BillingOperationType_BillingOperationTypeSplitInTransfer
	case BillingOperationTypeGroupRelation_BillingOperationTypeGroupRelationPayOutCmsOutAcquirer:
		return BillingOperationType_BillingOperationTypePayOutCommissionOutAcquirer
	case BillingOperationTypeGroupRelation_BillingOperationTypeGroupRelationPayOutCmsInSystem:
		return BillingOperationType_BillingOperationTypePayOutCommissionInSystem
	case BillingOperationTypeGroupRelation_BillingOperationTypeGroupRelationPayOutCmsOutSystem:
		return BillingOperationType_BillingOperationTypePayOutCommissionOutSystem
	case BillingOperationTypeGroupRelation_BillingOperationTypeGroupRelationPayOutCmsInMerchant:
		return BillingOperationType_BillingOperationTypePayOutCommissionInMerchant
	case BillingOperationTypeGroupRelation_BillingOperationTypeGroupRelationPayOutCmsOutMerchant:
		return BillingOperationType_BillingOperationTypePayOutCommissionOutMerchant
	case BillingOperationTypeGroupRelation_BillingOperationTypeGroupRelationSplitCmsInSystem:
		return BillingOperationType_BillingOperationTypeSplitCommissionInSystem
	case BillingOperationTypeGroupRelation_BillingOperationTypeGroupRelationSplitCmsOutSystem:
		return BillingOperationType_BillingOperationTypeSplitCommissionOutSystem
	case BillingOperationTypeGroupRelation_BillingOperationTypeGroupRelationSplitCmsInMerchant:
		return BillingOperationType_BillingOperationTypeSplitCommissionInMerchant
	case BillingOperationTypeGroupRelation_BillingOperationTypeGroupRelationSplitCmsOutMerchant:
		return BillingOperationType_BillingOperationTypeSplitCommissionOutMerchant
	default:
		return BillingOperationType_BillingOperationTypeUnknown
	}
}

func (x BillingOperationTypeGroupRelation) OperationTypeGroup() BillingOperationTypeGroup {
	switch x {
	case BillingOperationTypeGroupRelation_BillingOperationTypeGroupRelationUnknown:
		return BillingOperationTypeGroup_BillingOperationTypeGroupUnknown
	case BillingOperationTypeGroupRelation_BillingOperationTypeGroupRelationPayIn:
		return BillingOperationTypeGroup_BillingOperationTypeGroupPayIn
	case BillingOperationTypeGroupRelation_BillingOperationTypeGroupRelationPayOut:
		return BillingOperationTypeGroup_BillingOperationTypeGroupOut
	case BillingOperationTypeGroupRelation_BillingOperationTypeGroupRelationRefund:
		return BillingOperationTypeGroup_BillingOperationTypeGroupRefund
	case BillingOperationTypeGroupRelation_BillingOperationTypeGroupRelationInTransfer:
		return BillingOperationTypeGroup_BillingOperationTypeGroupIn
	case BillingOperationTypeGroupRelation_BillingOperationTypeGroupRelationOutTransfer:
		return BillingOperationTypeGroup_BillingOperationTypeGroupOut
	case BillingOperationTypeGroupRelation_BillingOperationTypeGroupRelationPayInCmsOutAcquirer:
		return BillingOperationTypeGroup_BillingOperationTypeGroupOut
	case BillingOperationTypeGroupRelation_BillingOperationTypeGroupRelationPayInCmsInSystem:
		return BillingOperationTypeGroup_BillingOperationTypeGroupIn
	case BillingOperationTypeGroupRelation_BillingOperationTypeGroupRelationPayInCmsOutSystem:
		return BillingOperationTypeGroup_BillingOperationTypeGroupOut
	case BillingOperationTypeGroupRelation_BillingOperationTypeGroupRelationPayInCmsInMerchant:
		return BillingOperationTypeGroup_BillingOperationTypeGroupIn
	case BillingOperationTypeGroupRelation_BillingOperationTypeGroupRelationPayInCmsOutMerchant:
		return BillingOperationTypeGroup_BillingOperationTypeGroupOut
	case BillingOperationTypeGroupRelation_BillingOperationTypeGroupRelationInternalInTransfer:
		return BillingOperationTypeGroup_BillingOperationTypeGroupIn
	case BillingOperationTypeGroupRelation_BillingOperationTypeGroupRelationInternalOutTransfer:
		return BillingOperationTypeGroup_BillingOperationTypeGroupOut
	case BillingOperationTypeGroupRelation_BillingOperationTypeGroupRelationSplitOutTransfer:
		return BillingOperationTypeGroup_BillingOperationTypeGroupOut
	case BillingOperationTypeGroupRelation_BillingOperationTypeGroupRelationSplitInTransfer:
		return BillingOperationTypeGroup_BillingOperationTypeGroupIn
	case BillingOperationTypeGroupRelation_BillingOperationTypeGroupRelationPayOutCmsOutAcquirer:
		return BillingOperationTypeGroup_BillingOperationTypeGroupOut
	case BillingOperationTypeGroupRelation_BillingOperationTypeGroupRelationPayOutCmsInSystem:
		return BillingOperationTypeGroup_BillingOperationTypeGroupIn
	case BillingOperationTypeGroupRelation_BillingOperationTypeGroupRelationPayOutCmsOutSystem:
		return BillingOperationTypeGroup_BillingOperationTypeGroupOut
	case BillingOperationTypeGroupRelation_BillingOperationTypeGroupRelationPayOutCmsInMerchant:
		return BillingOperationTypeGroup_BillingOperationTypeGroupIn
	case BillingOperationTypeGroupRelation_BillingOperationTypeGroupRelationPayOutCmsOutMerchant:
		return BillingOperationTypeGroup_BillingOperationTypeGroupOut
	case BillingOperationTypeGroupRelation_BillingOperationTypeGroupRelationSplitCmsInSystem:
		return BillingOperationTypeGroup_BillingOperationTypeGroupIn
	case BillingOperationTypeGroupRelation_BillingOperationTypeGroupRelationSplitCmsOutSystem:
		return BillingOperationTypeGroup_BillingOperationTypeGroupOut
	case BillingOperationTypeGroupRelation_BillingOperationTypeGroupRelationSplitCmsInMerchant:
		return BillingOperationTypeGroup_BillingOperationTypeGroupIn
	case BillingOperationTypeGroupRelation_BillingOperationTypeGroupRelationSplitCmsOutMerchant:
		return BillingOperationTypeGroup_BillingOperationTypeGroupOut
	default:
		return BillingOperationTypeGroup_BillingOperationTypeGroupUnknown
	}
}

// Created reference to BillingOperationTypeGroupRelation

//	|	BillingOperationTypeGroupRelation                                                      	|	OperationMode                                       	|	OperationType                                                       	|	OperationTypeGroup                                        	|
//	|	BillingOperationTypeGroupRelation_BillingOperationTypeGroupRelationUnknown             	|	BillingOperationMode_BillingOperationModeUnknown    	|	BillingOperationType_BillingOperationTypePayIn                      	|	BillingOperationTypeGroup_BillingOperationTypeGroupUnknown	|
//	|	BillingOperationTypeGroupRelation_BillingOperationTypeGroupRelationPayIn               	|	BillingOperationMode_BillingOperationModeBankProcess	|	BillingOperationType_BillingOperationTypePayIn                      	|	BillingOperationTypeGroup_BillingOperationTypeGroupPayIn  	|
//	|	BillingOperationTypeGroupRelation_BillingOperationTypeGroupRelationPayOut              	|	BillingOperationMode_BillingOperationModeRealtime   	|	BillingOperationType_BillingOperationTypePayOut                     	|	BillingOperationTypeGroup_BillingOperationTypeGroupOut    	|
//	|	BillingOperationTypeGroupRelation_BillingOperationTypeGroupRelationRefund              	|	BillingOperationMode_BillingOperationModeBankProcess	|	BillingOperationType_BillingOperationTypeRefund                     	|	BillingOperationTypeGroup_BillingOperationTypeGroupRefund 	|
//	|	BillingOperationTypeGroupRelation_BillingOperationTypeGroupRelationInTransfer          	|	BillingOperationMode_BillingOperationModeRealtime   	|	BillingOperationType_BillingOperationTypeInTransfer                 	|	BillingOperationTypeGroup_BillingOperationTypeGroupIn     	|
//	|	BillingOperationTypeGroupRelation_BillingOperationTypeGroupRelationOutTransfer         	|	BillingOperationMode_BillingOperationModeRealtime   	|	BillingOperationType_BillingOperationTypeOutTransfer                	|	BillingOperationTypeGroup_BillingOperationTypeGroupOut    	|
//	|	BillingOperationTypeGroupRelation_BillingOperationTypeGroupRelationPayInCmsOutAcquirer 	|	BillingOperationMode_BillingOperationModeBankProcess	|	BillingOperationType_BillingOperationTypePayInCommissionOutAcquirer 	|	BillingOperationTypeGroup_BillingOperationTypeGroupOut    	|
//	|	BillingOperationTypeGroupRelation_BillingOperationTypeGroupRelationPayInCmsInSystem    	|	BillingOperationMode_BillingOperationModeBankProcess	|	BillingOperationType_BillingOperationTypePayInCommissionInSystem    	|	BillingOperationTypeGroup_BillingOperationTypeGroupIn     	|
//	|	BillingOperationTypeGroupRelation_BillingOperationTypeGroupRelationPayInCmsOutSystem   	|	BillingOperationMode_BillingOperationModeBankProcess	|	BillingOperationType_BillingOperationTypePayInCommissionOutSystem   	|	BillingOperationTypeGroup_BillingOperationTypeGroupOut    	|
//	|	BillingOperationTypeGroupRelation_BillingOperationTypeGroupRelationPayInCmsInMerchant  	|	BillingOperationMode_BillingOperationModeBankProcess	|	BillingOperationType_BillingOperationTypePayInCommissionInMerchant  	|	BillingOperationTypeGroup_BillingOperationTypeGroupIn     	|
//	|	BillingOperationTypeGroupRelation_BillingOperationTypeGroupRelationPayInCmsOutMerchant 	|	BillingOperationMode_BillingOperationModeBankProcess	|	BillingOperationType_BillingOperationTypePayInCommissionOutMerchant 	|	BillingOperationTypeGroup_BillingOperationTypeGroupOut    	|
//	|	BillingOperationTypeGroupRelation_BillingOperationTypeGroupRelationInternalInTransfer  	|	BillingOperationMode_BillingOperationModeRealtime   	|	BillingOperationType_BillingOperationTypeInternalInTransfer         	|	BillingOperationTypeGroup_BillingOperationTypeGroupIn     	|
//	|	BillingOperationTypeGroupRelation_BillingOperationTypeGroupRelationInternalOutTransfer 	|	BillingOperationMode_BillingOperationModeRealtime   	|	BillingOperationType_BillingOperationTypeInternalOutTransfer        	|	BillingOperationTypeGroup_BillingOperationTypeGroupOut    	|
//	|	BillingOperationTypeGroupRelation_BillingOperationTypeGroupRelationSplitOutTransfer    	|	BillingOperationMode_BillingOperationModeRealtime   	|	BillingOperationType_BillingOperationTypeSplitOutTransfer           	|	BillingOperationTypeGroup_BillingOperationTypeGroupOut    	|
//	|	BillingOperationTypeGroupRelation_BillingOperationTypeGroupRelationSplitInTransfer     	|	BillingOperationMode_BillingOperationModeRealtime   	|	BillingOperationType_BillingOperationTypeSplitInTransfer            	|	BillingOperationTypeGroup_BillingOperationTypeGroupIn     	|
//	|	BillingOperationTypeGroupRelation_BillingOperationTypeGroupRelationPayOutCmsOutAcquirer	|	BillingOperationMode_BillingOperationModeRealtime   	|	BillingOperationType_BillingOperationTypePayOutCommissionOutAcquirer	|	BillingOperationTypeGroup_BillingOperationTypeGroupOut    	|
//	|	BillingOperationTypeGroupRelation_BillingOperationTypeGroupRelationPayOutCmsInSystem   	|	BillingOperationMode_BillingOperationModeRealtime   	|	BillingOperationType_BillingOperationTypePayOutCommissionInSystem   	|	BillingOperationTypeGroup_BillingOperationTypeGroupIn     	|
//	|	BillingOperationTypeGroupRelation_BillingOperationTypeGroupRelationPayOutCmsOutSystem  	|	BillingOperationMode_BillingOperationModeRealtime   	|	BillingOperationType_BillingOperationTypePayOutCommissionOutSystem  	|	BillingOperationTypeGroup_BillingOperationTypeGroupOut    	|
//	|	BillingOperationTypeGroupRelation_BillingOperationTypeGroupRelationPayOutCmsInMerchant 	|	BillingOperationMode_BillingOperationModeRealtime   	|	BillingOperationType_BillingOperationTypePayOutCommissionInMerchant 	|	BillingOperationTypeGroup_BillingOperationTypeGroupIn     	|
//	|	BillingOperationTypeGroupRelation_BillingOperationTypeGroupRelationPayOutCmsOutMerchant	|	BillingOperationMode_BillingOperationModeRealtime   	|	BillingOperationType_BillingOperationTypePayOutCommissionOutMerchant	|	BillingOperationTypeGroup_BillingOperationTypeGroupOut    	|
//	|	BillingOperationTypeGroupRelation_BillingOperationTypeGroupRelationSplitCmsInSystem    	|	BillingOperationMode_BillingOperationModeRealtime   	|	BillingOperationType_BillingOperationTypeSplitCommissionInSystem    	|	BillingOperationTypeGroup_BillingOperationTypeGroupIn     	|
//	|	BillingOperationTypeGroupRelation_BillingOperationTypeGroupRelationSplitCmsOutSystem   	|	BillingOperationMode_BillingOperationModeRealtime   	|	BillingOperationType_BillingOperationTypeSplitCommissionOutSystem   	|	BillingOperationTypeGroup_BillingOperationTypeGroupOut    	|
//	|	BillingOperationTypeGroupRelation_BillingOperationTypeGroupRelationSplitCmsInMerchant  	|	BillingOperationMode_BillingOperationModeRealtime   	|	BillingOperationType_BillingOperationTypeSplitCommissionInMerchant  	|	BillingOperationTypeGroup_BillingOperationTypeGroupIn     	|
//	|	BillingOperationTypeGroupRelation_BillingOperationTypeGroupRelationSplitCmsOutMerchant 	|	BillingOperationMode_BillingOperationModeRealtime   	|	BillingOperationType_BillingOperationTypeSplitCommissionOutMerchant 	|	BillingOperationTypeGroup_BillingOperationTypeGroupOut    	|

var SliceBillingOperationTypeGroupRelationRefs *sliceBillingOperationTypeGroupRelationRefs

type sliceBillingOperationTypeGroupRelationRefs struct{}

func (*sliceBillingOperationTypeGroupRelationRefs) OperationMode(slice ...BillingOperationTypeGroupRelation) []BillingOperationMode {
	var result []BillingOperationMode
	for _, val := range slice {
		result = append(result, val.OperationMode())
	}

	return result
}

func (*sliceBillingOperationTypeGroupRelationRefs) OperationType(slice ...BillingOperationTypeGroupRelation) []BillingOperationType {
	var result []BillingOperationType
	for _, val := range slice {
		result = append(result, val.OperationType())
	}

	return result
}

func (*sliceBillingOperationTypeGroupRelationRefs) OperationTypeGroup(slice ...BillingOperationTypeGroupRelation) []BillingOperationTypeGroup {
	var result []BillingOperationTypeGroup
	for _, val := range slice {
		result = append(result, val.OperationTypeGroup())
	}

	return result
}
