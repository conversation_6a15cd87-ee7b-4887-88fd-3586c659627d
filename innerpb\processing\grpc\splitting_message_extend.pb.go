// Code generated by protoc-gen-go-mvp. DO NOT EDIT.
// versions:
// - protoc-gen-go-mvp v2.0.1
// created by mvp authors

package grpc

import (
	protojson "google.golang.org/protobuf/encoding/protojson"
)

func (m *CalculatePaymentSplitTaxRequest) UnmarshalJSON(in []byte) error {
	val := new(CalculatePaymentSplitTaxRequest)
	if err := (&protojson.UnmarshalOptions{
		DiscardUnknown: true,
	}).Unmarshal(in, val); err != nil {
		return err
	}

	*m = *val
	return nil
}

func (m *CalculatePaymentSplitTaxRequest) MarshalJSON() ([]byte, error) {
	return (&protojson.MarshalOptions{
		UseProtoNames: true,
	}).Marshal(m)
}

func (m *CalculatePaymentSplitTaxResponse) UnmarshalJSON(in []byte) error {
	val := new(CalculatePaymentSplitTaxResponse)
	if err := (&protojson.UnmarshalOptions{
		DiscardUnknown: true,
	}).Unmarshal(in, val); err != nil {
		return err
	}

	*m = *val
	return nil
}

func (m *CalculatePaymentSplitTaxResponse) MarshalJSON() ([]byte, error) {
	return (&protojson.MarshalOptions{
		UseProtoNames: true,
	}).Marshal(m)
}
