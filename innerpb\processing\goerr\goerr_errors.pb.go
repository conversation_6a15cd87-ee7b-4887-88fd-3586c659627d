// Code generated by protoc-gen-go-mvp. DO NOT EDIT.
// versions:
// - protoc-gen-go-mvp v2.0.1
// created by mvp authors

package goerr

import (
	errors "git.local/sensitive/mvp/pkg/errors"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
	structpb "google.golang.org/protobuf/types/known/structpb"
)

var (
	statusErrOperationNotPermitted, _ = status.New(
		codes.PermissionDenied,
		"operation not permitted",
	).WithDetails(&structpb.Struct{
		Fields: map[string]*structpb.Value{
			"GrpcCode":   structpb.NewNumberValue(float64(codes.PermissionDenied)),
			"Message":    structpb.NewStringValue("operation not permitted"),
			"ErrorType":  structpb.NewStringValue(errors.ErrorTypeForbidden), // "ErrorTypeForbidden"
			"IsExpected": structpb.NewBoolValue(true),
			"Code":       structpb.NewNumberValue(float64(Error_ErrOperationNotPermitted)), // 1601
		},
	})

	ErrOperationNotPermitted = errors.GoErr{
		ErrType:    errors.ErrorTypeForbidden, // "ErrorTypeForbidden"
		Status:     statusErrOperationNotPermitted,
		Code:       int(Error_ErrOperationNotPermitted), // 1601
		Message:    "operation not permitted",
		GrpcCode:   codes.PermissionDenied,
		IsExpected: true,
	}
)

var (
	statusErrPayNotFoundInMovementsLogic, _ = status.New(
		codes.PermissionDenied,
		"pay not found in movements logic",
	).WithDetails(&structpb.Struct{
		Fields: map[string]*structpb.Value{
			"GrpcCode":   structpb.NewNumberValue(float64(codes.PermissionDenied)),
			"Message":    structpb.NewStringValue("pay not found in movements logic"),
			"ErrorType":  structpb.NewStringValue(errors.ErrorTypeForbidden), // "ErrorTypeForbidden"
			"IsExpected": structpb.NewBoolValue(true),
			"Code":       structpb.NewNumberValue(float64(Error_ErrPayNotFoundInMovementsLogic)), // 8007
		},
	})

	ErrPayNotFoundInMovementsLogic = errors.GoErr{
		ErrType:    errors.ErrorTypeForbidden, // "ErrorTypeForbidden"
		Status:     statusErrPayNotFoundInMovementsLogic,
		Code:       int(Error_ErrPayNotFoundInMovementsLogic), // 8007
		Message:    "pay not found in movements logic",
		GrpcCode:   codes.PermissionDenied,
		IsExpected: true,
	}
)

var (
	statusErrActionNotFound, _ = status.New(
		codes.NotFound,
		"action not found",
	).WithDetails(&structpb.Struct{
		Fields: map[string]*structpb.Value{
			"GrpcCode":   structpb.NewNumberValue(float64(codes.NotFound)),
			"Message":    structpb.NewStringValue("action not found"),
			"ErrorType":  structpb.NewStringValue(errors.ErrorTypeNotFound), // "ErrorTypeNotFound"
			"IsExpected": structpb.NewBoolValue(true),
			"Code":       structpb.NewNumberValue(float64(Error_ErrActionNotFound)), // 8100
		},
	})

	ErrActionNotFound = errors.GoErr{
		ErrType:    errors.ErrorTypeNotFound, // "ErrorTypeNotFound"
		Status:     statusErrActionNotFound,
		Code:       int(Error_ErrActionNotFound), // 8100
		Message:    "action not found",
		GrpcCode:   codes.NotFound,
		IsExpected: true,
	}
)

var (
	statusErrActionReasonNotFound, _ = status.New(
		codes.NotFound,
		"action's reason not found",
	).WithDetails(&structpb.Struct{
		Fields: map[string]*structpb.Value{
			"GrpcCode":   structpb.NewNumberValue(float64(codes.NotFound)),
			"Message":    structpb.NewStringValue("action's reason not found"),
			"ErrorType":  structpb.NewStringValue(errors.ErrorTypeNotFound), // "ErrorTypeNotFound"
			"IsExpected": structpb.NewBoolValue(true),
			"Code":       structpb.NewNumberValue(float64(Error_ErrActionReasonNotFound)), // 8101
		},
	})

	ErrActionReasonNotFound = errors.GoErr{
		ErrType:    errors.ErrorTypeNotFound, // "ErrorTypeNotFound"
		Status:     statusErrActionReasonNotFound,
		Code:       int(Error_ErrActionReasonNotFound), // 8101
		Message:    "action's reason not found",
		GrpcCode:   codes.NotFound,
		IsExpected: true,
	}
)

var (
	statusErrCommentaryNotFound, _ = status.New(
		codes.NotFound,
		"commentary not found",
	).WithDetails(&structpb.Struct{
		Fields: map[string]*structpb.Value{
			"GrpcCode":   structpb.NewNumberValue(float64(codes.NotFound)),
			"Message":    structpb.NewStringValue("commentary not found"),
			"ErrorType":  structpb.NewStringValue(errors.ErrorTypeNotFound), // "ErrorTypeNotFound"
			"IsExpected": structpb.NewBoolValue(true),
			"Code":       structpb.NewNumberValue(float64(Error_ErrCommentaryNotFound)), // 8103
		},
	})

	ErrCommentaryNotFound = errors.GoErr{
		ErrType:    errors.ErrorTypeNotFound, // "ErrorTypeNotFound"
		Status:     statusErrCommentaryNotFound,
		Code:       int(Error_ErrCommentaryNotFound), // 8103
		Message:    "commentary not found",
		GrpcCode:   codes.NotFound,
		IsExpected: true,
	}
)

var (
	statusErrSupervisorNotFound, _ = status.New(
		codes.NotFound,
		"supervisor doesn't exist",
	).WithDetails(&structpb.Struct{
		Fields: map[string]*structpb.Value{
			"GrpcCode":   structpb.NewNumberValue(float64(codes.NotFound)),
			"Message":    structpb.NewStringValue("supervisor doesn't exist"),
			"ErrorType":  structpb.NewStringValue(errors.ErrorTypeNotFound), // "ErrorTypeNotFound"
			"IsExpected": structpb.NewBoolValue(true),
			"Code":       structpb.NewNumberValue(float64(Error_ErrSupervisorNotFound)), // 8026
		},
	})

	ErrSupervisorNotFound = errors.GoErr{
		ErrType:    errors.ErrorTypeNotFound, // "ErrorTypeNotFound"
		Status:     statusErrSupervisorNotFound,
		Code:       int(Error_ErrSupervisorNotFound), // 8026
		Message:    "supervisor doesn't exist",
		GrpcCode:   codes.NotFound,
		IsExpected: true,
	}
)

var (
	statusErrSystemAuthentication, _ = status.New(
		codes.InvalidArgument,
		"system authentication is failed",
	).WithDetails(&structpb.Struct{
		Fields: map[string]*structpb.Value{
			"GrpcCode":   structpb.NewNumberValue(float64(codes.InvalidArgument)),
			"Message":    structpb.NewStringValue("system authentication is failed"),
			"ErrorType":  structpb.NewStringValue(errors.ErrorTypeBadRequest), // "ErrorTypeBadRequest"
			"IsExpected": structpb.NewBoolValue(true),
			"Code":       structpb.NewNumberValue(float64(Error_ErrSystemAuthentication)), // 5509
		},
	})

	ErrSystemAuthentication = errors.GoErr{
		ErrType:    errors.ErrorTypeBadRequest, // "ErrorTypeBadRequest"
		Status:     statusErrSystemAuthentication,
		Code:       int(Error_ErrSystemAuthentication), // 5509
		Message:    "system authentication is failed",
		GrpcCode:   codes.InvalidArgument,
		IsExpected: true,
	}
)

var (
	statusErrCheckAccountFailed, _ = status.New(
		codes.Unknown,
		"check account failed",
	).WithDetails(&structpb.Struct{
		Fields: map[string]*structpb.Value{
			"GrpcCode":   structpb.NewNumberValue(float64(codes.Unknown)),
			"Message":    structpb.NewStringValue("check account failed"),
			"ErrorType":  structpb.NewStringValue(errors.ErrorTypeUnexpectedError), // "ErrorTypeUnexpectedError"
			"IsExpected": structpb.NewBoolValue(true),
			"Code":       structpb.NewNumberValue(float64(Error_ErrCheckAccountFailed)), // 1700
		},
	})

	ErrCheckAccountFailed = errors.GoErr{
		ErrType:    errors.ErrorTypeUnexpectedError, // "ErrorTypeUnexpectedError"
		Status:     statusErrCheckAccountFailed,
		Code:       int(Error_ErrCheckAccountFailed), // 1700
		Message:    "check account failed",
		GrpcCode:   codes.Unknown,
		IsExpected: true,
	}
)

var (
	statusErrAccountDoesntExist, _ = status.New(
		codes.InvalidArgument,
		"account doesn't exist",
	).WithDetails(&structpb.Struct{
		Fields: map[string]*structpb.Value{
			"GrpcCode":   structpb.NewNumberValue(float64(codes.InvalidArgument)),
			"Message":    structpb.NewStringValue("account doesn't exist"),
			"ErrorType":  structpb.NewStringValue(errors.ErrorTypeBadRequest), // "ErrorTypeBadRequest"
			"IsExpected": structpb.NewBoolValue(true),
			"Code":       structpb.NewNumberValue(float64(Error_ErrAccountDoesntExist)), // 8067
		},
	})

	ErrAccountDoesntExist = errors.GoErr{
		ErrType:    errors.ErrorTypeBadRequest, // "ErrorTypeBadRequest"
		Status:     statusErrAccountDoesntExist,
		Code:       int(Error_ErrAccountDoesntExist), // 8067
		Message:    "account doesn't exist",
		GrpcCode:   codes.InvalidArgument,
		IsExpected: true,
	}
)

var (
	statusErrAmountWithSign, _ = status.New(
		codes.InvalidArgument,
		"amount must be with amount sign",
	).WithDetails(&structpb.Struct{
		Fields: map[string]*structpb.Value{
			"GrpcCode":   structpb.NewNumberValue(float64(codes.InvalidArgument)),
			"Message":    structpb.NewStringValue("amount must be with amount sign"),
			"ErrorType":  structpb.NewStringValue(errors.ErrorTypeBadRequest), // "ErrorTypeBadRequest"
			"IsExpected": structpb.NewBoolValue(true),
			"Code":       structpb.NewNumberValue(float64(Error_ErrAmountWithSign)), // 3000
		},
	})

	ErrAmountWithSign = errors.GoErr{
		ErrType:    errors.ErrorTypeBadRequest, // "ErrorTypeBadRequest"
		Status:     statusErrAmountWithSign,
		Code:       int(Error_ErrAmountWithSign), // 3000
		Message:    "amount must be with amount sign",
		GrpcCode:   codes.InvalidArgument,
		IsExpected: true,
	}
)

var (
	statusErrPercentage, _ = status.New(
		codes.InvalidArgument,
		"percentage must equal to 100",
	).WithDetails(&structpb.Struct{
		Fields: map[string]*structpb.Value{
			"GrpcCode":   structpb.NewNumberValue(float64(codes.InvalidArgument)),
			"Message":    structpb.NewStringValue("percentage must equal to 100"),
			"ErrorType":  structpb.NewStringValue(errors.ErrorTypeBadRequest), // "ErrorTypeBadRequest"
			"IsExpected": structpb.NewBoolValue(true),
			"Code":       structpb.NewNumberValue(float64(Error_ErrPercentage)), // 3001
		},
	})

	ErrPercentage = errors.GoErr{
		ErrType:    errors.ErrorTypeBadRequest, // "ErrorTypeBadRequest"
		Status:     statusErrPercentage,
		Code:       int(Error_ErrPercentage), // 3001
		Message:    "percentage must equal to 100",
		GrpcCode:   codes.InvalidArgument,
		IsExpected: true,
	}
)

var (
	statusErrAmountSign, _ = status.New(
		codes.InvalidArgument,
		"wrong amount sign",
	).WithDetails(&structpb.Struct{
		Fields: map[string]*structpb.Value{
			"GrpcCode":   structpb.NewNumberValue(float64(codes.InvalidArgument)),
			"Message":    structpb.NewStringValue("wrong amount sign"),
			"ErrorType":  structpb.NewStringValue(errors.ErrorTypeBadRequest), // "ErrorTypeBadRequest"
			"IsExpected": structpb.NewBoolValue(true),
			"Code":       structpb.NewNumberValue(float64(Error_ErrAmountSign)), // 3002
		},
	})

	ErrAmountSign = errors.GoErr{
		ErrType:    errors.ErrorTypeBadRequest, // "ErrorTypeBadRequest"
		Status:     statusErrAmountSign,
		Code:       int(Error_ErrAmountSign), // 3002
		Message:    "wrong amount sign",
		GrpcCode:   codes.InvalidArgument,
		IsExpected: true,
	}
)

var (
	statusErrAmountIsGreaterThanLimit, _ = status.New(
		codes.InvalidArgument,
		"transaction amount is greater than limit",
	).WithDetails(&structpb.Struct{
		Fields: map[string]*structpb.Value{
			"GrpcCode":   structpb.NewNumberValue(float64(codes.InvalidArgument)),
			"Message":    structpb.NewStringValue("transaction amount is greater than limit"),
			"ErrorType":  structpb.NewStringValue(errors.ErrorTypeBadRequest), // "ErrorTypeBadRequest"
			"IsExpected": structpb.NewBoolValue(true),
			"Code":       structpb.NewNumberValue(float64(Error_ErrAmountIsGreaterThanLimit)), // 8058
		},
	})

	ErrAmountIsGreaterThanLimit = errors.GoErr{
		ErrType:    errors.ErrorTypeBadRequest, // "ErrorTypeBadRequest"
		Status:     statusErrAmountIsGreaterThanLimit,
		Code:       int(Error_ErrAmountIsGreaterThanLimit), // 8058
		Message:    "transaction amount is greater than limit",
		GrpcCode:   codes.InvalidArgument,
		IsExpected: true,
	}
)

var (
	statusErrNotEnoughBalance, _ = status.New(
		codes.InvalidArgument,
		"not enough balance",
	).WithDetails(&structpb.Struct{
		Fields: map[string]*structpb.Value{
			"GrpcCode":   structpb.NewNumberValue(float64(codes.InvalidArgument)),
			"Message":    structpb.NewStringValue("not enough balance"),
			"ErrorType":  structpb.NewStringValue(errors.ErrorTypeBadRequest), // "ErrorTypeBadRequest"
			"IsExpected": structpb.NewBoolValue(true),
			"Code":       structpb.NewNumberValue(float64(Error_ErrNotEnoughBalance)), // 1410
		},
	})

	ErrNotEnoughBalance = errors.GoErr{
		ErrType:    errors.ErrorTypeBadRequest, // "ErrorTypeBadRequest"
		Status:     statusErrNotEnoughBalance,
		Code:       int(Error_ErrNotEnoughBalance), // 1410
		Message:    "not enough balance",
		GrpcCode:   codes.InvalidArgument,
		IsExpected: true,
	}
)

var (
	statusErrMonthlyLimitExceeded, _ = status.New(
		codes.InvalidArgument,
		"monthly limit exceeded",
	).WithDetails(&structpb.Struct{
		Fields: map[string]*structpb.Value{
			"GrpcCode":   structpb.NewNumberValue(float64(codes.InvalidArgument)),
			"Message":    structpb.NewStringValue("monthly limit exceeded"),
			"ErrorType":  structpb.NewStringValue(errors.ErrorTypeBadRequest), // "ErrorTypeBadRequest"
			"IsExpected": structpb.NewBoolValue(true),
			"Code":       structpb.NewNumberValue(float64(Error_ErrMonthlyLimitExceeded)), // 1411
		},
	})

	ErrMonthlyLimitExceeded = errors.GoErr{
		ErrType:    errors.ErrorTypeBadRequest, // "ErrorTypeBadRequest"
		Status:     statusErrMonthlyLimitExceeded,
		Code:       int(Error_ErrMonthlyLimitExceeded), // 1411
		Message:    "monthly limit exceeded",
		GrpcCode:   codes.InvalidArgument,
		IsExpected: true,
	}
)

var (
	statusErrDailyLimitExceeded, _ = status.New(
		codes.InvalidArgument,
		"daily limit exceeded",
	).WithDetails(&structpb.Struct{
		Fields: map[string]*structpb.Value{
			"GrpcCode":   structpb.NewNumberValue(float64(codes.InvalidArgument)),
			"Message":    structpb.NewStringValue("daily limit exceeded"),
			"ErrorType":  structpb.NewStringValue(errors.ErrorTypeBadRequest), // "ErrorTypeBadRequest"
			"IsExpected": structpb.NewBoolValue(true),
			"Code":       structpb.NewNumberValue(float64(Error_ErrDailyLimitExceeded)), // 1412
		},
	})

	ErrDailyLimitExceeded = errors.GoErr{
		ErrType:    errors.ErrorTypeBadRequest, // "ErrorTypeBadRequest"
		Status:     statusErrDailyLimitExceeded,
		Code:       int(Error_ErrDailyLimitExceeded), // 1412
		Message:    "daily limit exceeded",
		GrpcCode:   codes.InvalidArgument,
		IsExpected: true,
	}
)

var (
	statusErrEmptyAmount, _ = status.New(
		codes.InvalidArgument,
		"empty amount",
	).WithDetails(&structpb.Struct{
		Fields: map[string]*structpb.Value{
			"GrpcCode":   structpb.NewNumberValue(float64(codes.InvalidArgument)),
			"Message":    structpb.NewStringValue("empty amount"),
			"ErrorType":  structpb.NewStringValue(errors.ErrorTypeBadRequest), // "ErrorTypeBadRequest"
			"IsExpected": structpb.NewBoolValue(true),
			"Code":       structpb.NewNumberValue(float64(Error_ErrEmptyAmount)), // 1400
		},
	})

	ErrEmptyAmount = errors.GoErr{
		ErrType:    errors.ErrorTypeBadRequest, // "ErrorTypeBadRequest"
		Status:     statusErrEmptyAmount,
		Code:       int(Error_ErrEmptyAmount), // 1400
		Message:    "empty amount",
		GrpcCode:   codes.InvalidArgument,
		IsExpected: true,
	}
)

var (
	statusErrMustBeMoreThanZero, _ = status.New(
		codes.Unknown,
		"amount must be more than 0",
	).WithDetails(&structpb.Struct{
		Fields: map[string]*structpb.Value{
			"GrpcCode":   structpb.NewNumberValue(float64(codes.Unknown)),
			"Message":    structpb.NewStringValue("amount must be more than 0"),
			"ErrorType":  structpb.NewStringValue(errors.ErrorTypeUnexpectedError), // "ErrorTypeUnexpectedError"
			"IsExpected": structpb.NewBoolValue(true),
			"Code":       structpb.NewNumberValue(float64(Error_ErrMustBeMoreThanZero)), // 5508
		},
	})

	ErrMustBeMoreThanZero = errors.GoErr{
		ErrType:    errors.ErrorTypeUnexpectedError, // "ErrorTypeUnexpectedError"
		Status:     statusErrMustBeMoreThanZero,
		Code:       int(Error_ErrMustBeMoreThanZero), // 5508
		Message:    "amount must be more than 0",
		GrpcCode:   codes.Unknown,
		IsExpected: true,
	}
)

var (
	statusErrAmountLimit, _ = status.New(
		codes.InvalidArgument,
		"amount limit exceeded",
	).WithDetails(&structpb.Struct{
		Fields: map[string]*structpb.Value{
			"GrpcCode":   structpb.NewNumberValue(float64(codes.InvalidArgument)),
			"Message":    structpb.NewStringValue("amount limit exceeded"),
			"ErrorType":  structpb.NewStringValue(errors.ErrorTypeBadRequest), // "ErrorTypeBadRequest"
			"IsExpected": structpb.NewBoolValue(true),
			"Code":       structpb.NewNumberValue(float64(Error_ErrAmountLimit)), // 1600
		},
	})

	ErrAmountLimit = errors.GoErr{
		ErrType:    errors.ErrorTypeBadRequest, // "ErrorTypeBadRequest"
		Status:     statusErrAmountLimit,
		Code:       int(Error_ErrAmountLimit), // 1600
		Message:    "amount limit exceeded",
		GrpcCode:   codes.InvalidArgument,
		IsExpected: true,
	}
)

var (
	statusErrOTPExpired, _ = status.New(
		codes.InvalidArgument,
		"otp expired",
	).WithDetails(&structpb.Struct{
		Fields: map[string]*structpb.Value{
			"GrpcCode":   structpb.NewNumberValue(float64(codes.InvalidArgument)),
			"Message":    structpb.NewStringValue("otp expired"),
			"ErrorType":  structpb.NewStringValue(errors.ErrorTypeBadRequest), // "ErrorTypeBadRequest"
			"IsExpected": structpb.NewBoolValue(true),
			"Code":       structpb.NewNumberValue(float64(Error_ErrOTPExpired)), // 9000
		},
	})

	ErrOTPExpired = errors.GoErr{
		ErrType:    errors.ErrorTypeBadRequest, // "ErrorTypeBadRequest"
		Status:     statusErrOTPExpired,
		Code:       int(Error_ErrOTPExpired), // 9000
		Message:    "otp expired",
		GrpcCode:   codes.InvalidArgument,
		IsExpected: true,
	}
)

var (
	statusErrOTPMismatch, _ = status.New(
		codes.InvalidArgument,
		"otp is not matching",
	).WithDetails(&structpb.Struct{
		Fields: map[string]*structpb.Value{
			"GrpcCode":   structpb.NewNumberValue(float64(codes.InvalidArgument)),
			"Message":    structpb.NewStringValue("otp is not matching"),
			"ErrorType":  structpb.NewStringValue(errors.ErrorTypeBadRequest), // "ErrorTypeBadRequest"
			"IsExpected": structpb.NewBoolValue(true),
			"Code":       structpb.NewNumberValue(float64(Error_ErrOTPMismatch)), // 9001
		},
	})

	ErrOTPMismatch = errors.GoErr{
		ErrType:    errors.ErrorTypeBadRequest, // "ErrorTypeBadRequest"
		Status:     statusErrOTPMismatch,
		Code:       int(Error_ErrOTPMismatch), // 9001
		Message:    "otp is not matching",
		GrpcCode:   codes.InvalidArgument,
		IsExpected: true,
	}
)

var (
	statusErrRoleUsedByUsers, _ = status.New(
		codes.InvalidArgument,
		"role have users",
	).WithDetails(&structpb.Struct{
		Fields: map[string]*structpb.Value{
			"GrpcCode":   structpb.NewNumberValue(float64(codes.InvalidArgument)),
			"Message":    structpb.NewStringValue("role have users"),
			"ErrorType":  structpb.NewStringValue(errors.ErrorTypeBadRequest), // "ErrorTypeBadRequest"
			"IsExpected": structpb.NewBoolValue(true),
			"Code":       structpb.NewNumberValue(float64(Error_ErrRoleUsedByUsers)), // 9104
		},
	})

	ErrRoleUsedByUsers = errors.GoErr{
		ErrType:    errors.ErrorTypeBadRequest, // "ErrorTypeBadRequest"
		Status:     statusErrRoleUsedByUsers,
		Code:       int(Error_ErrRoleUsedByUsers), // 9104
		Message:    "role have users",
		GrpcCode:   codes.InvalidArgument,
		IsExpected: true,
	}
)

var (
	statusErrRoleNotFound, _ = status.New(
		codes.NotFound,
		"role doesn't exist",
	).WithDetails(&structpb.Struct{
		Fields: map[string]*structpb.Value{
			"GrpcCode":   structpb.NewNumberValue(float64(codes.NotFound)),
			"Message":    structpb.NewStringValue("role doesn't exist"),
			"ErrorType":  structpb.NewStringValue(errors.ErrorTypeNotFound), // "ErrorTypeNotFound"
			"IsExpected": structpb.NewBoolValue(true),
			"Code":       structpb.NewNumberValue(float64(Error_ErrRoleNotFound)), // 9100
		},
	})

	ErrRoleNotFound = errors.GoErr{
		ErrType:    errors.ErrorTypeNotFound, // "ErrorTypeNotFound"
		Status:     statusErrRoleNotFound,
		Code:       int(Error_ErrRoleNotFound), // 9100
		Message:    "role doesn't exist",
		GrpcCode:   codes.NotFound,
		IsExpected: true,
	}
)

var (
	statusErrUserIsBlocked, _ = status.New(
		codes.Unauthenticated,
		"user is blocked",
	).WithDetails(&structpb.Struct{
		Fields: map[string]*structpb.Value{
			"GrpcCode":   structpb.NewNumberValue(float64(codes.Unauthenticated)),
			"Message":    structpb.NewStringValue("user is blocked"),
			"ErrorType":  structpb.NewStringValue(errors.ErrorTypeAuthorization), // "ErrorTypeAuthorization"
			"IsExpected": structpb.NewBoolValue(true),
			"Code":       structpb.NewNumberValue(float64(Error_ErrUserIsBlocked)), // 9101
		},
	})

	ErrUserIsBlocked = errors.GoErr{
		ErrType:    errors.ErrorTypeAuthorization, // "ErrorTypeAuthorization"
		Status:     statusErrUserIsBlocked,
		Code:       int(Error_ErrUserIsBlocked), // 9101
		Message:    "user is blocked",
		GrpcCode:   codes.Unauthenticated,
		IsExpected: true,
	}
)

var (
	statusErrRoleIsBinded, _ = status.New(
		codes.InvalidArgument,
		"role is binded to user",
	).WithDetails(&structpb.Struct{
		Fields: map[string]*structpb.Value{
			"GrpcCode":   structpb.NewNumberValue(float64(codes.InvalidArgument)),
			"Message":    structpb.NewStringValue("role is binded to user"),
			"ErrorType":  structpb.NewStringValue(errors.ErrorTypeBadRequest), // "ErrorTypeBadRequest"
			"IsExpected": structpb.NewBoolValue(true),
			"Code":       structpb.NewNumberValue(float64(Error_ErrRoleIsBinded)), // 9102
		},
	})

	ErrRoleIsBinded = errors.GoErr{
		ErrType:    errors.ErrorTypeBadRequest, // "ErrorTypeBadRequest"
		Status:     statusErrRoleIsBinded,
		Code:       int(Error_ErrRoleIsBinded), // 9102
		Message:    "role is binded to user",
		GrpcCode:   codes.InvalidArgument,
		IsExpected: true,
	}
)

var (
	statusErrUserNotFound, _ = status.New(
		codes.NotFound,
		"user doesn't exist",
	).WithDetails(&structpb.Struct{
		Fields: map[string]*structpb.Value{
			"GrpcCode":   structpb.NewNumberValue(float64(codes.NotFound)),
			"Message":    structpb.NewStringValue("user doesn't exist"),
			"ErrorType":  structpb.NewStringValue(errors.ErrorTypeNotFound), // "ErrorTypeNotFound"
			"IsExpected": structpb.NewBoolValue(true),
			"Code":       structpb.NewNumberValue(float64(Error_ErrUserNotFound)), // 9103
		},
	})

	ErrUserNotFound = errors.GoErr{
		ErrType:    errors.ErrorTypeNotFound, // "ErrorTypeNotFound"
		Status:     statusErrUserNotFound,
		Code:       int(Error_ErrUserNotFound), // 9103
		Message:    "user doesn't exist",
		GrpcCode:   codes.NotFound,
		IsExpected: true,
	}
)

var (
	statusErrInvalidPassword, _ = status.New(
		codes.Unauthenticated,
		"the provided password is invalid",
	).WithDetails(&structpb.Struct{
		Fields: map[string]*structpb.Value{
			"GrpcCode":   structpb.NewNumberValue(float64(codes.Unauthenticated)),
			"Message":    structpb.NewStringValue("the provided password is invalid"),
			"ErrorType":  structpb.NewStringValue(errors.ErrorTypeAuthorization), // "ErrorTypeAuthorization"
			"IsExpected": structpb.NewBoolValue(true),
			"Code":       structpb.NewNumberValue(float64(Error_ErrInvalidPassword)), // 7201
		},
	})

	ErrInvalidPassword = errors.GoErr{
		ErrType:    errors.ErrorTypeAuthorization, // "ErrorTypeAuthorization"
		Status:     statusErrInvalidPassword,
		Code:       int(Error_ErrInvalidPassword), // 7201
		Message:    "the provided password is invalid",
		GrpcCode:   codes.Unauthenticated,
		IsExpected: true,
	}
)

var (
	statusErrJWSExpNotSatisfied, _ = status.New(
		codes.PermissionDenied,
		"exp not satisfied",
	).WithDetails(&structpb.Struct{
		Fields: map[string]*structpb.Value{
			"GrpcCode":   structpb.NewNumberValue(float64(codes.PermissionDenied)),
			"Message":    structpb.NewStringValue("exp not satisfied"),
			"ErrorType":  structpb.NewStringValue(errors.ErrorTypeForbidden), // "ErrorTypeForbidden"
			"IsExpected": structpb.NewBoolValue(true),
			"Code":       structpb.NewNumberValue(float64(Error_ErrJWSExpNotSatisfied)), // 1603
		},
	})

	ErrJWSExpNotSatisfied = errors.GoErr{
		ErrType:    errors.ErrorTypeForbidden, // "ErrorTypeForbidden"
		Status:     statusErrJWSExpNotSatisfied,
		Code:       int(Error_ErrJWSExpNotSatisfied), // 1603
		Message:    "exp not satisfied",
		GrpcCode:   codes.PermissionDenied,
		IsExpected: true,
	}
)

var (
	statusErrLoginAlreadyExists, _ = status.New(
		codes.Unknown,
		"login already exists",
	).WithDetails(&structpb.Struct{
		Fields: map[string]*structpb.Value{
			"GrpcCode":   structpb.NewNumberValue(float64(codes.Unknown)),
			"Message":    structpb.NewStringValue("login already exists"),
			"ErrorType":  structpb.NewStringValue(errors.ErrorTypeUnexpectedError), // "ErrorTypeUnexpectedError"
			"IsExpected": structpb.NewBoolValue(true),
			"Code":       structpb.NewNumberValue(float64(Error_ErrLoginAlreadyExists)), // 1300
		},
	})

	ErrLoginAlreadyExists = errors.GoErr{
		ErrType:    errors.ErrorTypeUnexpectedError, // "ErrorTypeUnexpectedError"
		Status:     statusErrLoginAlreadyExists,
		Code:       int(Error_ErrLoginAlreadyExists), // 1300
		Message:    "login already exists",
		GrpcCode:   codes.Unknown,
		IsExpected: true,
	}
)

var (
	statusErrLoginIsNotVerified, _ = status.New(
		codes.Unknown,
		"login is not verified",
	).WithDetails(&structpb.Struct{
		Fields: map[string]*structpb.Value{
			"GrpcCode":   structpb.NewNumberValue(float64(codes.Unknown)),
			"Message":    structpb.NewStringValue("login is not verified"),
			"ErrorType":  structpb.NewStringValue(errors.ErrorTypeUnexpectedError), // "ErrorTypeUnexpectedError"
			"IsExpected": structpb.NewBoolValue(true),
			"Code":       structpb.NewNumberValue(float64(Error_ErrLoginIsNotVerified)), // 1301
		},
	})

	ErrLoginIsNotVerified = errors.GoErr{
		ErrType:    errors.ErrorTypeUnexpectedError, // "ErrorTypeUnexpectedError"
		Status:     statusErrLoginIsNotVerified,
		Code:       int(Error_ErrLoginIsNotVerified), // 1301
		Message:    "login is not verified",
		GrpcCode:   codes.Unknown,
		IsExpected: true,
	}
)

var (
	statusErrCredentialNotFound, _ = status.New(
		codes.NotFound,
		"no credential meets the request",
	).WithDetails(&structpb.Struct{
		Fields: map[string]*structpb.Value{
			"GrpcCode":   structpb.NewNumberValue(float64(codes.NotFound)),
			"Message":    structpb.NewStringValue("no credential meets the request"),
			"ErrorType":  structpb.NewStringValue(errors.ErrorTypeNotFound), // "ErrorTypeNotFound"
			"IsExpected": structpb.NewBoolValue(true),
			"Code":       structpb.NewNumberValue(float64(Error_ErrCredentialNotFound)), // 7101
		},
	})

	ErrCredentialNotFound = errors.GoErr{
		ErrType:    errors.ErrorTypeNotFound, // "ErrorTypeNotFound"
		Status:     statusErrCredentialNotFound,
		Code:       int(Error_ErrCredentialNotFound), // 7101
		Message:    "no credential meets the request",
		GrpcCode:   codes.NotFound,
		IsExpected: true,
	}
)

var (
	statusErrInvalidCredentials, _ = status.New(
		codes.NotFound,
		"invalid credentials",
	).WithDetails(&structpb.Struct{
		Fields: map[string]*structpb.Value{
			"GrpcCode":   structpb.NewNumberValue(float64(codes.NotFound)),
			"Message":    structpb.NewStringValue("invalid credentials"),
			"ErrorType":  structpb.NewStringValue(errors.ErrorTypeNotFound), // "ErrorTypeNotFound"
			"IsExpected": structpb.NewBoolValue(true),
			"Code":       structpb.NewNumberValue(float64(Error_ErrInvalidCredentials)), // 8006
		},
	})

	ErrInvalidCredentials = errors.GoErr{
		ErrType:    errors.ErrorTypeNotFound, // "ErrorTypeNotFound"
		Status:     statusErrInvalidCredentials,
		Code:       int(Error_ErrInvalidCredentials), // 8006
		Message:    "invalid credentials",
		GrpcCode:   codes.NotFound,
		IsExpected: true,
	}
)

var (
	statusErrDecodePEM, _ = status.New(
		codes.InvalidArgument,
		"failed to decode PEM block containing public key",
	).WithDetails(&structpb.Struct{
		Fields: map[string]*structpb.Value{
			"GrpcCode":   structpb.NewNumberValue(float64(codes.InvalidArgument)),
			"Message":    structpb.NewStringValue("failed to decode PEM block containing public key"),
			"ErrorType":  structpb.NewStringValue(errors.ErrorTypeBadRequest), // "ErrorTypeBadRequest"
			"IsExpected": structpb.NewBoolValue(true),
			"Code":       structpb.NewNumberValue(float64(Error_ErrDecodePEM)), // 7016
		},
	})

	ErrDecodePEM = errors.GoErr{
		ErrType:    errors.ErrorTypeBadRequest, // "ErrorTypeBadRequest"
		Status:     statusErrDecodePEM,
		Code:       int(Error_ErrDecodePEM), // 7016
		Message:    "failed to decode PEM block containing public key",
		GrpcCode:   codes.InvalidArgument,
		IsExpected: true,
	}
)

var (
	statusErrPublicKeyCasting, _ = status.New(
		codes.InvalidArgument,
		"could not cast the public key into the structure",
	).WithDetails(&structpb.Struct{
		Fields: map[string]*structpb.Value{
			"GrpcCode":   structpb.NewNumberValue(float64(codes.InvalidArgument)),
			"Message":    structpb.NewStringValue("could not cast the public key into the structure"),
			"ErrorType":  structpb.NewStringValue(errors.ErrorTypeBadRequest), // "ErrorTypeBadRequest"
			"IsExpected": structpb.NewBoolValue(true),
			"Code":       structpb.NewNumberValue(float64(Error_ErrPublicKeyCasting)), // 7017
		},
	})

	ErrPublicKeyCasting = errors.GoErr{
		ErrType:    errors.ErrorTypeBadRequest, // "ErrorTypeBadRequest"
		Status:     statusErrPublicKeyCasting,
		Code:       int(Error_ErrPublicKeyCasting), // 7017
		Message:    "could not cast the public key into the structure",
		GrpcCode:   codes.InvalidArgument,
		IsExpected: true,
	}
)

var (
	statusErrTerminalConfigEncryption, _ = status.New(
		codes.Unknown,
		"terminal config encryption failed",
	).WithDetails(&structpb.Struct{
		Fields: map[string]*structpb.Value{
			"GrpcCode":   structpb.NewNumberValue(float64(codes.Unknown)),
			"Message":    structpb.NewStringValue("terminal config encryption failed"),
			"ErrorType":  structpb.NewStringValue(errors.ErrorTypeUnexpectedError), // "ErrorTypeUnexpectedError"
			"IsExpected": structpb.NewBoolValue(true),
			"Code":       structpb.NewNumberValue(float64(Error_ErrTerminalConfigEncryption)), // 3300
		},
	})

	ErrTerminalConfigEncryption = errors.GoErr{
		ErrType:    errors.ErrorTypeUnexpectedError, // "ErrorTypeUnexpectedError"
		Status:     statusErrTerminalConfigEncryption,
		Code:       int(Error_ErrTerminalConfigEncryption), // 3300
		Message:    "terminal config encryption failed",
		GrpcCode:   codes.Unknown,
		IsExpected: true,
	}
)

var (
	statusErrMessageNotFound, _ = status.New(
		codes.Unknown,
		"message doesn't exist",
	).WithDetails(&structpb.Struct{
		Fields: map[string]*structpb.Value{
			"GrpcCode":   structpb.NewNumberValue(float64(codes.Unknown)),
			"Message":    structpb.NewStringValue("message doesn't exist"),
			"ErrorType":  structpb.NewStringValue(errors.ErrorTypeUnexpectedError), // "ErrorTypeUnexpectedError"
			"IsExpected": structpb.NewBoolValue(true),
			"Code":       structpb.NewNumberValue(float64(Error_ErrMessageNotFound)), // 3301
		},
	})

	ErrMessageNotFound = errors.GoErr{
		ErrType:    errors.ErrorTypeUnexpectedError, // "ErrorTypeUnexpectedError"
		Status:     statusErrMessageNotFound,
		Code:       int(Error_ErrMessageNotFound), // 3301
		Message:    "message doesn't exist",
		GrpcCode:   codes.Unknown,
		IsExpected: true,
	}
)

var (
	statusErrSmsNotSent, _ = status.New(
		codes.Unknown,
		"failed to send sms",
	).WithDetails(&structpb.Struct{
		Fields: map[string]*structpb.Value{
			"GrpcCode":   structpb.NewNumberValue(float64(codes.Unknown)),
			"Message":    structpb.NewStringValue("failed to send sms"),
			"ErrorType":  structpb.NewStringValue(errors.ErrorTypeUnexpectedError), // "ErrorTypeUnexpectedError"
			"IsExpected": structpb.NewBoolValue(true),
			"Code":       structpb.NewNumberValue(float64(Error_ErrSmsNotSent)), // 3302
		},
	})

	ErrSmsNotSent = errors.GoErr{
		ErrType:    errors.ErrorTypeUnexpectedError, // "ErrorTypeUnexpectedError"
		Status:     statusErrSmsNotSent,
		Code:       int(Error_ErrSmsNotSent), // 3302
		Message:    "failed to send sms",
		GrpcCode:   codes.Unknown,
		IsExpected: true,
	}
)

var (
	statusErrInvalidValueDbReadDSN, _ = status.New(
		codes.Unknown,
		"could not read a valid string value from [DATABASE_READ_DSN]",
	).WithDetails(&structpb.Struct{
		Fields: map[string]*structpb.Value{
			"GrpcCode":   structpb.NewNumberValue(float64(codes.Unknown)),
			"Message":    structpb.NewStringValue("could not read a valid string value from [DATABASE_READ_DSN]"),
			"ErrorType":  structpb.NewStringValue(errors.ErrorTypeUnexpectedError), // "ErrorTypeUnexpectedError"
			"IsExpected": structpb.NewBoolValue(true),
			"Code":       structpb.NewNumberValue(float64(Error_ErrInvalidValueDbReadDSN)), // 1414
		},
	})

	ErrInvalidValueDbReadDSN = errors.GoErr{
		ErrType:    errors.ErrorTypeUnexpectedError, // "ErrorTypeUnexpectedError"
		Status:     statusErrInvalidValueDbReadDSN,
		Code:       int(Error_ErrInvalidValueDbReadDSN), // 1414
		Message:    "could not read a valid string value from [DATABASE_READ_DSN]",
		GrpcCode:   codes.Unknown,
		IsExpected: true,
	}
)

var (
	statusErrInvalidValueDbWriteDSN, _ = status.New(
		codes.Unknown,
		"could not read a valid string value from [DATABASE_WRITE_DSN]",
	).WithDetails(&structpb.Struct{
		Fields: map[string]*structpb.Value{
			"GrpcCode":   structpb.NewNumberValue(float64(codes.Unknown)),
			"Message":    structpb.NewStringValue("could not read a valid string value from [DATABASE_WRITE_DSN]"),
			"ErrorType":  structpb.NewStringValue(errors.ErrorTypeUnexpectedError), // "ErrorTypeUnexpectedError"
			"IsExpected": structpb.NewBoolValue(true),
			"Code":       structpb.NewNumberValue(float64(Error_ErrInvalidValueDbWriteDSN)), // 1415
		},
	})

	ErrInvalidValueDbWriteDSN = errors.GoErr{
		ErrType:    errors.ErrorTypeUnexpectedError, // "ErrorTypeUnexpectedError"
		Status:     statusErrInvalidValueDbWriteDSN,
		Code:       int(Error_ErrInvalidValueDbWriteDSN), // 1415
		Message:    "could not read a valid string value from [DATABASE_WRITE_DSN]",
		GrpcCode:   codes.Unknown,
		IsExpected: true,
	}
)

var (
	statusErrInvalidValueRunEchoWorker, _ = status.New(
		codes.Unknown,
		"could not read a valid string value from [RUN_ECHO_WORKER]",
	).WithDetails(&structpb.Struct{
		Fields: map[string]*structpb.Value{
			"GrpcCode":   structpb.NewNumberValue(float64(codes.Unknown)),
			"Message":    structpb.NewStringValue("could not read a valid string value from [RUN_ECHO_WORKER]"),
			"ErrorType":  structpb.NewStringValue(errors.ErrorTypeUnexpectedError), // "ErrorTypeUnexpectedError"
			"IsExpected": structpb.NewBoolValue(true),
			"Code":       structpb.NewNumberValue(float64(Error_ErrInvalidValueRunEchoWorker)), // 1416
		},
	})

	ErrInvalidValueRunEchoWorker = errors.GoErr{
		ErrType:    errors.ErrorTypeUnexpectedError, // "ErrorTypeUnexpectedError"
		Status:     statusErrInvalidValueRunEchoWorker,
		Code:       int(Error_ErrInvalidValueRunEchoWorker), // 1416
		Message:    "could not read a valid string value from [RUN_ECHO_WORKER]",
		GrpcCode:   codes.Unknown,
		IsExpected: true,
	}
)

var (
	statusErrEmptyDataToAuthorizeToVault, _ = status.New(
		codes.Unknown,
		"received empty data to process the authorization to vault",
	).WithDetails(&structpb.Struct{
		Fields: map[string]*structpb.Value{
			"GrpcCode":   structpb.NewNumberValue(float64(codes.Unknown)),
			"Message":    structpb.NewStringValue("received empty data to process the authorization to vault"),
			"ErrorType":  structpb.NewStringValue(errors.ErrorTypeUnexpectedError), // "ErrorTypeUnexpectedError"
			"IsExpected": structpb.NewBoolValue(true),
			"Code":       structpb.NewNumberValue(float64(Error_ErrEmptyDataToAuthorizeToVault)), // 1417
		},
	})

	ErrEmptyDataToAuthorizeToVault = errors.GoErr{
		ErrType:    errors.ErrorTypeUnexpectedError, // "ErrorTypeUnexpectedError"
		Status:     statusErrEmptyDataToAuthorizeToVault,
		Code:       int(Error_ErrEmptyDataToAuthorizeToVault), // 1417
		Message:    "received empty data to process the authorization to vault",
		GrpcCode:   codes.Unknown,
		IsExpected: true,
	}
)

var (
	statusErrInvalidAuthDetailsFromVault, _ = status.New(
		codes.Unknown,
		"did not receive valid auth details from vault",
	).WithDetails(&structpb.Struct{
		Fields: map[string]*structpb.Value{
			"GrpcCode":   structpb.NewNumberValue(float64(codes.Unknown)),
			"Message":    structpb.NewStringValue("did not receive valid auth details from vault"),
			"ErrorType":  structpb.NewStringValue(errors.ErrorTypeUnexpectedError), // "ErrorTypeUnexpectedError"
			"IsExpected": structpb.NewBoolValue(true),
			"Code":       structpb.NewNumberValue(float64(Error_ErrInvalidAuthDetailsFromVault)), // 1418
		},
	})

	ErrInvalidAuthDetailsFromVault = errors.GoErr{
		ErrType:    errors.ErrorTypeUnexpectedError, // "ErrorTypeUnexpectedError"
		Status:     statusErrInvalidAuthDetailsFromVault,
		Code:       int(Error_ErrInvalidAuthDetailsFromVault), // 1418
		Message:    "did not receive valid auth details from vault",
		GrpcCode:   codes.Unknown,
		IsExpected: true,
	}
)

var (
	statusErrInvalidTerminalConfig, _ = status.New(
		codes.Unknown,
		"invalid terminal config",
	).WithDetails(&structpb.Struct{
		Fields: map[string]*structpb.Value{
			"GrpcCode":   structpb.NewNumberValue(float64(codes.Unknown)),
			"Message":    structpb.NewStringValue("invalid terminal config"),
			"ErrorType":  structpb.NewStringValue(errors.ErrorTypeUnexpectedError), // "ErrorTypeUnexpectedError"
			"IsExpected": structpb.NewBoolValue(true),
			"Code":       structpb.NewNumberValue(float64(Error_ErrInvalidTerminalConfig)), // 6010
		},
	})

	ErrInvalidTerminalConfig = errors.GoErr{
		ErrType:    errors.ErrorTypeUnexpectedError, // "ErrorTypeUnexpectedError"
		Status:     statusErrInvalidTerminalConfig,
		Code:       int(Error_ErrInvalidTerminalConfig), // 6010
		Message:    "invalid terminal config",
		GrpcCode:   codes.Unknown,
		IsExpected: true,
	}
)

var (
	statusErrNilValueOfSyncOnce, _ = status.New(
		codes.Unknown,
		"received a nil value of sync.Once",
	).WithDetails(&structpb.Struct{
		Fields: map[string]*structpb.Value{
			"GrpcCode":   structpb.NewNumberValue(float64(codes.Unknown)),
			"Message":    structpb.NewStringValue("received a nil value of sync.Once"),
			"ErrorType":  structpb.NewStringValue(errors.ErrorTypeUnexpectedError), // "ErrorTypeUnexpectedError"
			"IsExpected": structpb.NewBoolValue(true),
			"Code":       structpb.NewNumberValue(float64(Error_ErrNilValueOfSyncOnce)), // 1413
		},
	})

	ErrNilValueOfSyncOnce = errors.GoErr{
		ErrType:    errors.ErrorTypeUnexpectedError, // "ErrorTypeUnexpectedError"
		Status:     statusErrNilValueOfSyncOnce,
		Code:       int(Error_ErrNilValueOfSyncOnce), // 1413
		Message:    "received a nil value of sync.Once",
		GrpcCode:   codes.Unknown,
		IsExpected: true,
	}
)

var (
	statusErrEmptyProviderConfig, _ = status.New(
		codes.InvalidArgument,
		"empty provider config",
	).WithDetails(&structpb.Struct{
		Fields: map[string]*structpb.Value{
			"GrpcCode":   structpb.NewNumberValue(float64(codes.InvalidArgument)),
			"Message":    structpb.NewStringValue("empty provider config"),
			"ErrorType":  structpb.NewStringValue(errors.ErrorTypeBadRequest), // "ErrorTypeBadRequest"
			"IsExpected": structpb.NewBoolValue(true),
			"Code":       structpb.NewNumberValue(float64(Error_ErrEmptyProviderConfig)), // 1100
		},
	})

	ErrEmptyProviderConfig = errors.GoErr{
		ErrType:    errors.ErrorTypeBadRequest, // "ErrorTypeBadRequest"
		Status:     statusErrEmptyProviderConfig,
		Code:       int(Error_ErrEmptyProviderConfig), // 1100
		Message:    "empty provider config",
		GrpcCode:   codes.InvalidArgument,
		IsExpected: true,
	}
)

var (
	statusErrInvalidProviderConfig, _ = status.New(
		codes.InvalidArgument,
		"invalid provider config",
	).WithDetails(&structpb.Struct{
		Fields: map[string]*structpb.Value{
			"GrpcCode":   structpb.NewNumberValue(float64(codes.InvalidArgument)),
			"Message":    structpb.NewStringValue("invalid provider config"),
			"ErrorType":  structpb.NewStringValue(errors.ErrorTypeBadRequest), // "ErrorTypeBadRequest"
			"IsExpected": structpb.NewBoolValue(true),
			"Code":       structpb.NewNumberValue(float64(Error_ErrInvalidProviderConfig)), // 1101
		},
	})

	ErrInvalidProviderConfig = errors.GoErr{
		ErrType:    errors.ErrorTypeBadRequest, // "ErrorTypeBadRequest"
		Status:     statusErrInvalidProviderConfig,
		Code:       int(Error_ErrInvalidProviderConfig), // 1101
		Message:    "invalid provider config",
		GrpcCode:   codes.InvalidArgument,
		IsExpected: true,
	}
)

var (
	statusErrEmptyProviderCode, _ = status.New(
		codes.InvalidArgument,
		"empty provider code",
	).WithDetails(&structpb.Struct{
		Fields: map[string]*structpb.Value{
			"GrpcCode":   structpb.NewNumberValue(float64(codes.InvalidArgument)),
			"Message":    structpb.NewStringValue("empty provider code"),
			"ErrorType":  structpb.NewStringValue(errors.ErrorTypeBadRequest), // "ErrorTypeBadRequest"
			"IsExpected": structpb.NewBoolValue(true),
			"Code":       structpb.NewNumberValue(float64(Error_ErrEmptyProviderCode)), // 1102
		},
	})

	ErrEmptyProviderCode = errors.GoErr{
		ErrType:    errors.ErrorTypeBadRequest, // "ErrorTypeBadRequest"
		Status:     statusErrEmptyProviderCode,
		Code:       int(Error_ErrEmptyProviderCode), // 1102
		Message:    "empty provider code",
		GrpcCode:   codes.InvalidArgument,
		IsExpected: true,
	}
)

var (
	statusErrEmptyProviderServiceId, _ = status.New(
		codes.InvalidArgument,
		"empty provider service id",
	).WithDetails(&structpb.Struct{
		Fields: map[string]*structpb.Value{
			"GrpcCode":   structpb.NewNumberValue(float64(codes.InvalidArgument)),
			"Message":    structpb.NewStringValue("empty provider service id"),
			"ErrorType":  structpb.NewStringValue(errors.ErrorTypeBadRequest), // "ErrorTypeBadRequest"
			"IsExpected": structpb.NewBoolValue(true),
			"Code":       structpb.NewNumberValue(float64(Error_ErrEmptyProviderServiceId)), // 1103
		},
	})

	ErrEmptyProviderServiceId = errors.GoErr{
		ErrType:    errors.ErrorTypeBadRequest, // "ErrorTypeBadRequest"
		Status:     statusErrEmptyProviderServiceId,
		Code:       int(Error_ErrEmptyProviderServiceId), // 1103
		Message:    "empty provider service id",
		GrpcCode:   codes.InvalidArgument,
		IsExpected: true,
	}
)

var (
	statusErrEmptyProviderName, _ = status.New(
		codes.InvalidArgument,
		"empty provider name",
	).WithDetails(&structpb.Struct{
		Fields: map[string]*structpb.Value{
			"GrpcCode":   structpb.NewNumberValue(float64(codes.InvalidArgument)),
			"Message":    structpb.NewStringValue("empty provider name"),
			"ErrorType":  structpb.NewStringValue(errors.ErrorTypeBadRequest), // "ErrorTypeBadRequest"
			"IsExpected": structpb.NewBoolValue(true),
			"Code":       structpb.NewNumberValue(float64(Error_ErrEmptyProviderName)), // 1104
		},
	})

	ErrEmptyProviderName = errors.GoErr{
		ErrType:    errors.ErrorTypeBadRequest, // "ErrorTypeBadRequest"
		Status:     statusErrEmptyProviderName,
		Code:       int(Error_ErrEmptyProviderName), // 1104
		Message:    "empty provider name",
		GrpcCode:   codes.InvalidArgument,
		IsExpected: true,
	}
)

var (
	statusErrDbUnexpected, _ = status.New(
		codes.Unknown,
		"unexpected db error",
	).WithDetails(&structpb.Struct{
		Fields: map[string]*structpb.Value{
			"GrpcCode":   structpb.NewNumberValue(float64(codes.Unknown)),
			"Message":    structpb.NewStringValue("unexpected db error"),
			"ErrorType":  structpb.NewStringValue(errors.ErrorTypeUnexpectedError), // "ErrorTypeUnexpectedError"
			"IsExpected": structpb.NewBoolValue(true),
			"Code":       structpb.NewNumberValue(float64(Error_ErrDbUnexpected)), // 8301
		},
	})

	ErrDbUnexpected = errors.GoErr{
		ErrType:    errors.ErrorTypeUnexpectedError, // "ErrorTypeUnexpectedError"
		Status:     statusErrDbUnexpected,
		Code:       int(Error_ErrDbUnexpected), // 8301
		Message:    "unexpected db error",
		GrpcCode:   codes.Unknown,
		IsExpected: true,
	}
)

var (
	statusErrDuplicatedKey, _ = status.New(
		codes.Unknown,
		"parameters must be unique",
	).WithDetails(&structpb.Struct{
		Fields: map[string]*structpb.Value{
			"GrpcCode":   structpb.NewNumberValue(float64(codes.Unknown)),
			"Message":    structpb.NewStringValue("parameters must be unique"),
			"ErrorType":  structpb.NewStringValue(errors.ErrorTypeUnexpectedError), // "ErrorTypeUnexpectedError"
			"IsExpected": structpb.NewBoolValue(true),
			"Code":       structpb.NewNumberValue(float64(Error_ErrDuplicatedKey)), // 8302
		},
	})

	ErrDuplicatedKey = errors.GoErr{
		ErrType:    errors.ErrorTypeUnexpectedError, // "ErrorTypeUnexpectedError"
		Status:     statusErrDuplicatedKey,
		Code:       int(Error_ErrDuplicatedKey), // 8302
		Message:    "parameters must be unique",
		GrpcCode:   codes.Unknown,
		IsExpected: true,
	}
)

var (
	statusErrEmailDuplicatedKey, _ = status.New(
		codes.Unknown,
		"email must be unique",
	).WithDetails(&structpb.Struct{
		Fields: map[string]*structpb.Value{
			"GrpcCode":   structpb.NewNumberValue(float64(codes.Unknown)),
			"Message":    structpb.NewStringValue("email must be unique"),
			"ErrorType":  structpb.NewStringValue(errors.ErrorTypeUnexpectedError), // "ErrorTypeUnexpectedError"
			"IsExpected": structpb.NewBoolValue(true),
			"Code":       structpb.NewNumberValue(float64(Error_ErrEmailDuplicatedKey)), // 8303
		},
	})

	ErrEmailDuplicatedKey = errors.GoErr{
		ErrType:    errors.ErrorTypeUnexpectedError, // "ErrorTypeUnexpectedError"
		Status:     statusErrEmailDuplicatedKey,
		Code:       int(Error_ErrEmailDuplicatedKey), // 8303
		Message:    "email must be unique",
		GrpcCode:   codes.Unknown,
		IsExpected: true,
	}
)

var (
	statusErrForeignKeyViolation, _ = status.New(
		codes.Unknown,
		"non-existent parameters",
	).WithDetails(&structpb.Struct{
		Fields: map[string]*structpb.Value{
			"GrpcCode":   structpb.NewNumberValue(float64(codes.Unknown)),
			"Message":    structpb.NewStringValue("non-existent parameters"),
			"ErrorType":  structpb.NewStringValue(errors.ErrorTypeUnexpectedError), // "ErrorTypeUnexpectedError"
			"IsExpected": structpb.NewBoolValue(true),
			"Code":       structpb.NewNumberValue(float64(Error_ErrForeignKeyViolation)), // 8305
		},
	})

	ErrForeignKeyViolation = errors.GoErr{
		ErrType:    errors.ErrorTypeUnexpectedError, // "ErrorTypeUnexpectedError"
		Status:     statusErrForeignKeyViolation,
		Code:       int(Error_ErrForeignKeyViolation), // 8305
		Message:    "non-existent parameters",
		GrpcCode:   codes.Unknown,
		IsExpected: true,
	}
)

var (
	statusErrCodeDuplicatedKey, _ = status.New(
		codes.Unknown,
		"code must be unique",
	).WithDetails(&structpb.Struct{
		Fields: map[string]*structpb.Value{
			"GrpcCode":   structpb.NewNumberValue(float64(codes.Unknown)),
			"Message":    structpb.NewStringValue("code must be unique"),
			"ErrorType":  structpb.NewStringValue(errors.ErrorTypeUnexpectedError), // "ErrorTypeUnexpectedError"
			"IsExpected": structpb.NewBoolValue(true),
			"Code":       structpb.NewNumberValue(float64(Error_ErrCodeDuplicatedKey)), // 8306
		},
	})

	ErrCodeDuplicatedKey = errors.GoErr{
		ErrType:    errors.ErrorTypeUnexpectedError, // "ErrorTypeUnexpectedError"
		Status:     statusErrCodeDuplicatedKey,
		Code:       int(Error_ErrCodeDuplicatedKey), // 8306
		Message:    "code must be unique",
		GrpcCode:   codes.Unknown,
		IsExpected: true,
	}
)

var (
	statusErrAccountNumDuplicatedKey, _ = status.New(
		codes.Unknown,
		"account number must be unique",
	).WithDetails(&structpb.Struct{
		Fields: map[string]*structpb.Value{
			"GrpcCode":   structpb.NewNumberValue(float64(codes.Unknown)),
			"Message":    structpb.NewStringValue("account number must be unique"),
			"ErrorType":  structpb.NewStringValue(errors.ErrorTypeUnexpectedError), // "ErrorTypeUnexpectedError"
			"IsExpected": structpb.NewBoolValue(true),
			"Code":       structpb.NewNumberValue(float64(Error_ErrAccountNumDuplicatedKey)), // 8307
		},
	})

	ErrAccountNumDuplicatedKey = errors.GoErr{
		ErrType:    errors.ErrorTypeUnexpectedError, // "ErrorTypeUnexpectedError"
		Status:     statusErrAccountNumDuplicatedKey,
		Code:       int(Error_ErrAccountNumDuplicatedKey), // 8307
		Message:    "account number must be unique",
		GrpcCode:   codes.Unknown,
		IsExpected: true,
	}
)

var (
	statusErrPayNull, _ = status.New(
		codes.Unknown,
		"pay code is null",
	).WithDetails(&structpb.Struct{
		Fields: map[string]*structpb.Value{
			"GrpcCode":   structpb.NewNumberValue(float64(codes.Unknown)),
			"Message":    structpb.NewStringValue("pay code is null"),
			"ErrorType":  structpb.NewStringValue(errors.ErrorTypeUnexpectedError), // "ErrorTypeUnexpectedError"
			"IsExpected": structpb.NewBoolValue(true),
			"Code":       structpb.NewNumberValue(float64(Error_ErrPayNull)), // 8308
		},
	})

	ErrPayNull = errors.GoErr{
		ErrType:    errors.ErrorTypeUnexpectedError, // "ErrorTypeUnexpectedError"
		Status:     statusErrPayNull,
		Code:       int(Error_ErrPayNull), // 8308
		Message:    "pay code is null",
		GrpcCode:   codes.Unknown,
		IsExpected: true,
	}
)

var (
	statusErrDublicateKeyValue, _ = status.New(
		codes.DataLoss,
		"value already in use",
	).WithDetails(&structpb.Struct{
		Fields: map[string]*structpb.Value{
			"GrpcCode":   structpb.NewNumberValue(float64(codes.DataLoss)),
			"Message":    structpb.NewStringValue("value already in use"),
			"ErrorType":  structpb.NewStringValue(errors.ErrorTypeDbViolation), // "ErrorTypeDbViolation"
			"IsExpected": structpb.NewBoolValue(true),
			"Code":       structpb.NewNumberValue(float64(Error_ErrDublicateKeyValue)), // 8309
		},
	})

	ErrDublicateKeyValue = errors.GoErr{
		ErrType:    errors.ErrorTypeDbViolation, // "ErrorTypeDbViolation"
		Status:     statusErrDublicateKeyValue,
		Code:       int(Error_ErrDublicateKeyValue), // 8309
		Message:    "value already in use",
		GrpcCode:   codes.DataLoss,
		IsExpected: true,
	}
)

var (
	statusErrNoDataFound, _ = status.New(
		codes.InvalidArgument,
		"there is no data found by the key",
	).WithDetails(&structpb.Struct{
		Fields: map[string]*structpb.Value{
			"GrpcCode":   structpb.NewNumberValue(float64(codes.InvalidArgument)),
			"Message":    structpb.NewStringValue("there is no data found by the key"),
			"ErrorType":  structpb.NewStringValue(errors.ErrorTypeBadRequest), // "ErrorTypeBadRequest"
			"IsExpected": structpb.NewBoolValue(true),
			"Code":       structpb.NewNumberValue(float64(Error_ErrNoDataFound)), // 8310
		},
	})

	ErrNoDataFound = errors.GoErr{
		ErrType:    errors.ErrorTypeBadRequest, // "ErrorTypeBadRequest"
		Status:     statusErrNoDataFound,
		Code:       int(Error_ErrNoDataFound), // 8310
		Message:    "there is no data found by the key",
		GrpcCode:   codes.InvalidArgument,
		IsExpected: true,
	}
)

var (
	statusErrEntityNotFound, _ = status.New(
		codes.NotFound,
		"entity does not exist",
	).WithDetails(&structpb.Struct{
		Fields: map[string]*structpb.Value{
			"GrpcCode":   structpb.NewNumberValue(float64(codes.NotFound)),
			"Message":    structpb.NewStringValue("entity does not exist"),
			"ErrorType":  structpb.NewStringValue(errors.ErrorTypeNotFound), // "ErrorTypeNotFound"
			"IsExpected": structpb.NewBoolValue(true),
			"Code":       structpb.NewNumberValue(float64(Error_ErrEntityNotFound)), // 8311
		},
	})

	ErrEntityNotFound = errors.GoErr{
		ErrType:    errors.ErrorTypeNotFound, // "ErrorTypeNotFound"
		Status:     statusErrEntityNotFound,
		Code:       int(Error_ErrEntityNotFound), // 8311
		Message:    "entity does not exist",
		GrpcCode:   codes.NotFound,
		IsExpected: true,
	}
)

var (
	statusErrActivationError, _ = status.New(
		codes.Unknown,
		"activation error",
	).WithDetails(&structpb.Struct{
		Fields: map[string]*structpb.Value{
			"GrpcCode":   structpb.NewNumberValue(float64(codes.Unknown)),
			"Message":    structpb.NewStringValue("activation error"),
			"ErrorType":  structpb.NewStringValue(errors.ErrorTypeUnexpectedError), // "ErrorTypeUnexpectedError"
			"IsExpected": structpb.NewBoolValue(true),
			"Code":       structpb.NewNumberValue(float64(Error_ErrActivationError)), // 8312
		},
	})

	ErrActivationError = errors.GoErr{
		ErrType:    errors.ErrorTypeUnexpectedError, // "ErrorTypeUnexpectedError"
		Status:     statusErrActivationError,
		Code:       int(Error_ErrActivationError), // 8312
		Message:    "activation error",
		GrpcCode:   codes.Unknown,
		IsExpected: true,
	}
)

var (
	statusErrDeactivationError, _ = status.New(
		codes.Unknown,
		"deactivation error",
	).WithDetails(&structpb.Struct{
		Fields: map[string]*structpb.Value{
			"GrpcCode":   structpb.NewNumberValue(float64(codes.Unknown)),
			"Message":    structpb.NewStringValue("deactivation error"),
			"ErrorType":  structpb.NewStringValue(errors.ErrorTypeUnexpectedError), // "ErrorTypeUnexpectedError"
			"IsExpected": structpb.NewBoolValue(true),
			"Code":       structpb.NewNumberValue(float64(Error_ErrDeactivationError)), // 8313
		},
	})

	ErrDeactivationError = errors.GoErr{
		ErrType:    errors.ErrorTypeUnexpectedError, // "ErrorTypeUnexpectedError"
		Status:     statusErrDeactivationError,
		Code:       int(Error_ErrDeactivationError), // 8313
		Message:    "deactivation error",
		GrpcCode:   codes.Unknown,
		IsExpected: true,
	}
)

var (
	statusErrEmailDublicate, _ = status.New(
		codes.Unknown,
		"email must be unique",
	).WithDetails(&structpb.Struct{
		Fields: map[string]*structpb.Value{
			"GrpcCode":   structpb.NewNumberValue(float64(codes.Unknown)),
			"Message":    structpb.NewStringValue("email must be unique"),
			"ErrorType":  structpb.NewStringValue(errors.ErrorTypeUnexpectedError), // "ErrorTypeUnexpectedError"
			"IsExpected": structpb.NewBoolValue(true),
			"Code":       structpb.NewNumberValue(float64(Error_ErrEmailDublicate)), // 8314
		},
	})

	ErrEmailDublicate = errors.GoErr{
		ErrType:    errors.ErrorTypeUnexpectedError, // "ErrorTypeUnexpectedError"
		Status:     statusErrEmailDublicate,
		Code:       int(Error_ErrEmailDublicate), // 8314
		Message:    "email must be unique",
		GrpcCode:   codes.Unknown,
		IsExpected: true,
	}
)

var (
	statusErrPanLengthValidation, _ = status.New(
		codes.InvalidArgument,
		"pan length must be greater than 10",
	).WithDetails(&structpb.Struct{
		Fields: map[string]*structpb.Value{
			"GrpcCode":   structpb.NewNumberValue(float64(codes.InvalidArgument)),
			"Message":    structpb.NewStringValue("pan length must be greater than 10"),
			"ErrorType":  structpb.NewStringValue(errors.ErrorTypeBadRequest), // "ErrorTypeBadRequest"
			"IsExpected": structpb.NewBoolValue(true),
			"Code":       structpb.NewNumberValue(float64(Error_ErrPanLengthValidation)), // 3200
		},
	})

	ErrPanLengthValidation = errors.GoErr{
		ErrType:    errors.ErrorTypeBadRequest, // "ErrorTypeBadRequest"
		Status:     statusErrPanLengthValidation,
		Code:       int(Error_ErrPanLengthValidation), // 3200
		Message:    "pan length must be greater than 10",
		GrpcCode:   codes.InvalidArgument,
		IsExpected: true,
	}
)

var (
	statusErrCardDecryption, _ = status.New(
		codes.Unknown,
		"card decryption err",
	).WithDetails(&structpb.Struct{
		Fields: map[string]*structpb.Value{
			"GrpcCode":   structpb.NewNumberValue(float64(codes.Unknown)),
			"Message":    structpb.NewStringValue("card decryption err"),
			"ErrorType":  structpb.NewStringValue(errors.ErrorTypeUnexpectedError), // "ErrorTypeUnexpectedError"
			"IsExpected": structpb.NewBoolValue(true),
			"Code":       structpb.NewNumberValue(float64(Error_ErrCardDecryption)), // 3201
		},
	})

	ErrCardDecryption = errors.GoErr{
		ErrType:    errors.ErrorTypeUnexpectedError, // "ErrorTypeUnexpectedError"
		Status:     statusErrCardDecryption,
		Code:       int(Error_ErrCardDecryption), // 3201
		Message:    "card decryption err",
		GrpcCode:   codes.Unknown,
		IsExpected: true,
	}
)

var (
	statusErrReceivingCardInformation, _ = status.New(
		codes.InvalidArgument,
		"couldn't get any card information",
	).WithDetails(&structpb.Struct{
		Fields: map[string]*structpb.Value{
			"GrpcCode":   structpb.NewNumberValue(float64(codes.InvalidArgument)),
			"Message":    structpb.NewStringValue("couldn't get any card information"),
			"ErrorType":  structpb.NewStringValue(errors.ErrorTypeBadRequest), // "ErrorTypeBadRequest"
			"IsExpected": structpb.NewBoolValue(true),
			"Code":       structpb.NewNumberValue(float64(Error_ErrReceivingCardInformation)), // 3202
		},
	})

	ErrReceivingCardInformation = errors.GoErr{
		ErrType:    errors.ErrorTypeBadRequest, // "ErrorTypeBadRequest"
		Status:     statusErrReceivingCardInformation,
		Code:       int(Error_ErrReceivingCardInformation), // 3202
		Message:    "couldn't get any card information",
		GrpcCode:   codes.InvalidArgument,
		IsExpected: true,
	}
)

var (
	statusErrCardNotFound, _ = status.New(
		codes.NotFound,
		"card doesn't exist",
	).WithDetails(&structpb.Struct{
		Fields: map[string]*structpb.Value{
			"GrpcCode":   structpb.NewNumberValue(float64(codes.NotFound)),
			"Message":    structpb.NewStringValue("card doesn't exist"),
			"ErrorType":  structpb.NewStringValue(errors.ErrorTypeNotFound), // "ErrorTypeNotFound"
			"IsExpected": structpb.NewBoolValue(true),
			"Code":       structpb.NewNumberValue(float64(Error_ErrCardNotFound)), // 3108
		},
	})

	ErrCardNotFound = errors.GoErr{
		ErrType:    errors.ErrorTypeNotFound, // "ErrorTypeNotFound"
		Status:     statusErrCardNotFound,
		Code:       int(Error_ErrCardNotFound), // 3108
		Message:    "card doesn't exist",
		GrpcCode:   codes.NotFound,
		IsExpected: true,
	}
)

var (
	statusErrClientNotFound, _ = status.New(
		codes.NotFound,
		"client doesn't exist",
	).WithDetails(&structpb.Struct{
		Fields: map[string]*structpb.Value{
			"GrpcCode":   structpb.NewNumberValue(float64(codes.NotFound)),
			"Message":    structpb.NewStringValue("client doesn't exist"),
			"ErrorType":  structpb.NewStringValue(errors.ErrorTypeNotFound), // "ErrorTypeNotFound"
			"IsExpected": structpb.NewBoolValue(true),
			"Code":       structpb.NewNumberValue(float64(Error_ErrClientNotFound)), // 4000
		},
	})

	ErrClientNotFound = errors.GoErr{
		ErrType:    errors.ErrorTypeNotFound, // "ErrorTypeNotFound"
		Status:     statusErrClientNotFound,
		Code:       int(Error_ErrClientNotFound), // 4000
		Message:    "client doesn't exist",
		GrpcCode:   codes.NotFound,
		IsExpected: true,
	}
)

var (
	statusErrClientAlreadyExists, _ = status.New(
		codes.InvalidArgument,
		"client already exists",
	).WithDetails(&structpb.Struct{
		Fields: map[string]*structpb.Value{
			"GrpcCode":   structpb.NewNumberValue(float64(codes.InvalidArgument)),
			"Message":    structpb.NewStringValue("client already exists"),
			"ErrorType":  structpb.NewStringValue(errors.ErrorTypeBadRequest), // "ErrorTypeBadRequest"
			"IsExpected": structpb.NewBoolValue(true),
			"Code":       structpb.NewNumberValue(float64(Error_ErrClientAlreadyExists)), // 4001
		},
	})

	ErrClientAlreadyExists = errors.GoErr{
		ErrType:    errors.ErrorTypeBadRequest, // "ErrorTypeBadRequest"
		Status:     statusErrClientAlreadyExists,
		Code:       int(Error_ErrClientAlreadyExists), // 4001
		Message:    "client already exists",
		GrpcCode:   codes.InvalidArgument,
		IsExpected: true,
	}
)

var (
	statusErrCardTransactionNotFound, _ = status.New(
		codes.NotFound,
		"card transaction doesn't exist",
	).WithDetails(&structpb.Struct{
		Fields: map[string]*structpb.Value{
			"GrpcCode":   structpb.NewNumberValue(float64(codes.NotFound)),
			"Message":    structpb.NewStringValue("card transaction doesn't exist"),
			"ErrorType":  structpb.NewStringValue(errors.ErrorTypeNotFound), // "ErrorTypeNotFound"
			"IsExpected": structpb.NewBoolValue(true),
			"Code":       structpb.NewNumberValue(float64(Error_ErrCardTransactionNotFound)), // 4002
		},
	})

	ErrCardTransactionNotFound = errors.GoErr{
		ErrType:    errors.ErrorTypeNotFound, // "ErrorTypeNotFound"
		Status:     statusErrCardTransactionNotFound,
		Code:       int(Error_ErrCardTransactionNotFound), // 4002
		Message:    "card transaction doesn't exist",
		GrpcCode:   codes.NotFound,
		IsExpected: true,
	}
)

var (
	statusErrCardAlreadyExists, _ = status.New(
		codes.InvalidArgument,
		"card already exists",
	).WithDetails(&structpb.Struct{
		Fields: map[string]*structpb.Value{
			"GrpcCode":   structpb.NewNumberValue(float64(codes.InvalidArgument)),
			"Message":    structpb.NewStringValue("card already exists"),
			"ErrorType":  structpb.NewStringValue(errors.ErrorTypeBadRequest), // "ErrorTypeBadRequest"
			"IsExpected": structpb.NewBoolValue(true),
			"Code":       structpb.NewNumberValue(float64(Error_ErrCardAlreadyExists)), // 4005
		},
	})

	ErrCardAlreadyExists = errors.GoErr{
		ErrType:    errors.ErrorTypeBadRequest, // "ErrorTypeBadRequest"
		Status:     statusErrCardAlreadyExists,
		Code:       int(Error_ErrCardAlreadyExists), // 4005
		Message:    "card already exists",
		GrpcCode:   codes.InvalidArgument,
		IsExpected: true,
	}
)

var (
	statusErrCardTransactionAlreadyExists, _ = status.New(
		codes.InvalidArgument,
		"card transaction already exists",
	).WithDetails(&structpb.Struct{
		Fields: map[string]*structpb.Value{
			"GrpcCode":   structpb.NewNumberValue(float64(codes.InvalidArgument)),
			"Message":    structpb.NewStringValue("card transaction already exists"),
			"ErrorType":  structpb.NewStringValue(errors.ErrorTypeBadRequest), // "ErrorTypeBadRequest"
			"IsExpected": structpb.NewBoolValue(true),
			"Code":       structpb.NewNumberValue(float64(Error_ErrCardTransactionAlreadyExists)), // 4006
		},
	})

	ErrCardTransactionAlreadyExists = errors.GoErr{
		ErrType:    errors.ErrorTypeBadRequest, // "ErrorTypeBadRequest"
		Status:     statusErrCardTransactionAlreadyExists,
		Code:       int(Error_ErrCardTransactionAlreadyExists), // 4006
		Message:    "card transaction already exists",
		GrpcCode:   codes.InvalidArgument,
		IsExpected: true,
	}
)

var (
	statusErrTokenAlreadyExists, _ = status.New(
		codes.InvalidArgument,
		"token already exists",
	).WithDetails(&structpb.Struct{
		Fields: map[string]*structpb.Value{
			"GrpcCode":   structpb.NewNumberValue(float64(codes.InvalidArgument)),
			"Message":    structpb.NewStringValue("token already exists"),
			"ErrorType":  structpb.NewStringValue(errors.ErrorTypeBadRequest), // "ErrorTypeBadRequest"
			"IsExpected": structpb.NewBoolValue(true),
			"Code":       structpb.NewNumberValue(float64(Error_ErrTokenAlreadyExists)), // 4007
		},
	})

	ErrTokenAlreadyExists = errors.GoErr{
		ErrType:    errors.ErrorTypeBadRequest, // "ErrorTypeBadRequest"
		Status:     statusErrTokenAlreadyExists,
		Code:       int(Error_ErrTokenAlreadyExists), // 4007
		Message:    "token already exists",
		GrpcCode:   codes.InvalidArgument,
		IsExpected: true,
	}
)

var (
	statusErrTokenTransactionAlreadyExists, _ = status.New(
		codes.InvalidArgument,
		"token transaction already exists",
	).WithDetails(&structpb.Struct{
		Fields: map[string]*structpb.Value{
			"GrpcCode":   structpb.NewNumberValue(float64(codes.InvalidArgument)),
			"Message":    structpb.NewStringValue("token transaction already exists"),
			"ErrorType":  structpb.NewStringValue(errors.ErrorTypeBadRequest), // "ErrorTypeBadRequest"
			"IsExpected": structpb.NewBoolValue(true),
			"Code":       structpb.NewNumberValue(float64(Error_ErrTokenTransactionAlreadyExists)), // 4008
		},
	})

	ErrTokenTransactionAlreadyExists = errors.GoErr{
		ErrType:    errors.ErrorTypeBadRequest, // "ErrorTypeBadRequest"
		Status:     statusErrTokenTransactionAlreadyExists,
		Code:       int(Error_ErrTokenTransactionAlreadyExists), // 4008
		Message:    "token transaction already exists",
		GrpcCode:   codes.InvalidArgument,
		IsExpected: true,
	}
)

var (
	statusErrPanLength, _ = status.New(
		codes.InvalidArgument,
		"pan length must be equal 16",
	).WithDetails(&structpb.Struct{
		Fields: map[string]*structpb.Value{
			"GrpcCode":   structpb.NewNumberValue(float64(codes.InvalidArgument)),
			"Message":    structpb.NewStringValue("pan length must be equal 16"),
			"ErrorType":  structpb.NewStringValue(errors.ErrorTypeBadRequest), // "ErrorTypeBadRequest"
			"IsExpected": structpb.NewBoolValue(true),
			"Code":       structpb.NewNumberValue(float64(Error_ErrPanLength)), // 4009
		},
	})

	ErrPanLength = errors.GoErr{
		ErrType:    errors.ErrorTypeBadRequest, // "ErrorTypeBadRequest"
		Status:     statusErrPanLength,
		Code:       int(Error_ErrPanLength), // 4009
		Message:    "pan length must be equal 16",
		GrpcCode:   codes.InvalidArgument,
		IsExpected: true,
	}
)

var (
	statusErrEmptyTerminalID, _ = status.New(
		codes.InvalidArgument,
		"received an empty terminal ID value",
	).WithDetails(&structpb.Struct{
		Fields: map[string]*structpb.Value{
			"GrpcCode":   structpb.NewNumberValue(float64(codes.InvalidArgument)),
			"Message":    structpb.NewStringValue("received an empty terminal ID value"),
			"ErrorType":  structpb.NewStringValue(errors.ErrorTypeBadRequest), // "ErrorTypeBadRequest"
			"IsExpected": structpb.NewBoolValue(true),
			"Code":       structpb.NewNumberValue(float64(Error_ErrEmptyTerminalID)), // 4010
		},
	})

	ErrEmptyTerminalID = errors.GoErr{
		ErrType:    errors.ErrorTypeBadRequest, // "ErrorTypeBadRequest"
		Status:     statusErrEmptyTerminalID,
		Code:       int(Error_ErrEmptyTerminalID), // 4010
		Message:    "received an empty terminal ID value",
		GrpcCode:   codes.InvalidArgument,
		IsExpected: true,
	}
)

var (
	statusErrEmptyAcquirerID, _ = status.New(
		codes.InvalidArgument,
		"received an empty acquirer ID value",
	).WithDetails(&structpb.Struct{
		Fields: map[string]*structpb.Value{
			"GrpcCode":   structpb.NewNumberValue(float64(codes.InvalidArgument)),
			"Message":    structpb.NewStringValue("received an empty acquirer ID value"),
			"ErrorType":  structpb.NewStringValue(errors.ErrorTypeBadRequest), // "ErrorTypeBadRequest"
			"IsExpected": structpb.NewBoolValue(true),
			"Code":       structpb.NewNumberValue(float64(Error_ErrEmptyAcquirerID)), // 4011
		},
	})

	ErrEmptyAcquirerID = errors.GoErr{
		ErrType:    errors.ErrorTypeBadRequest, // "ErrorTypeBadRequest"
		Status:     statusErrEmptyAcquirerID,
		Code:       int(Error_ErrEmptyAcquirerID), // 4011
		Message:    "received an empty acquirer ID value",
		GrpcCode:   codes.InvalidArgument,
		IsExpected: true,
	}
)

var (
	statusErrEmptyToken, _ = status.New(
		codes.InvalidArgument,
		"received an empty token",
	).WithDetails(&structpb.Struct{
		Fields: map[string]*structpb.Value{
			"GrpcCode":   structpb.NewNumberValue(float64(codes.InvalidArgument)),
			"Message":    structpb.NewStringValue("received an empty token"),
			"ErrorType":  structpb.NewStringValue(errors.ErrorTypeBadRequest), // "ErrorTypeBadRequest"
			"IsExpected": structpb.NewBoolValue(true),
			"Code":       structpb.NewNumberValue(float64(Error_ErrEmptyToken)), // 4012
		},
	})

	ErrEmptyToken = errors.GoErr{
		ErrType:    errors.ErrorTypeBadRequest, // "ErrorTypeBadRequest"
		Status:     statusErrEmptyToken,
		Code:       int(Error_ErrEmptyToken), // 4012
		Message:    "received an empty token",
		GrpcCode:   codes.InvalidArgument,
		IsExpected: true,
	}
)

var (
	statusErrCardAlreadyDeactivated, _ = status.New(
		codes.InvalidArgument,
		"card already deactivated",
	).WithDetails(&structpb.Struct{
		Fields: map[string]*structpb.Value{
			"GrpcCode":   structpb.NewNumberValue(float64(codes.InvalidArgument)),
			"Message":    structpb.NewStringValue("card already deactivated"),
			"ErrorType":  structpb.NewStringValue(errors.ErrorTypeBadRequest), // "ErrorTypeBadRequest"
			"IsExpected": structpb.NewBoolValue(true),
			"Code":       structpb.NewNumberValue(float64(Error_ErrCardAlreadyDeactivated)), // 4013
		},
	})

	ErrCardAlreadyDeactivated = errors.GoErr{
		ErrType:    errors.ErrorTypeBadRequest, // "ErrorTypeBadRequest"
		Status:     statusErrCardAlreadyDeactivated,
		Code:       int(Error_ErrCardAlreadyDeactivated), // 4013
		Message:    "card already deactivated",
		GrpcCode:   codes.InvalidArgument,
		IsExpected: true,
	}
)

var (
	statusErrClientBlocked, _ = status.New(
		codes.InvalidArgument,
		"client is blocked",
	).WithDetails(&structpb.Struct{
		Fields: map[string]*structpb.Value{
			"GrpcCode":   structpb.NewNumberValue(float64(codes.InvalidArgument)),
			"Message":    structpb.NewStringValue("client is blocked"),
			"ErrorType":  structpb.NewStringValue(errors.ErrorTypeBadRequest), // "ErrorTypeBadRequest"
			"IsExpected": structpb.NewBoolValue(true),
			"Code":       structpb.NewNumberValue(float64(Error_ErrClientBlocked)), // 4014
		},
	})

	ErrClientBlocked = errors.GoErr{
		ErrType:    errors.ErrorTypeBadRequest, // "ErrorTypeBadRequest"
		Status:     statusErrClientBlocked,
		Code:       int(Error_ErrClientBlocked), // 4014
		Message:    "client is blocked",
		GrpcCode:   codes.InvalidArgument,
		IsExpected: true,
	}
)

var (
	statusErrWhileValidatingClientActiveness, _ = status.New(
		codes.InvalidArgument,
		"error while validating client activeness",
	).WithDetails(&structpb.Struct{
		Fields: map[string]*structpb.Value{
			"GrpcCode":   structpb.NewNumberValue(float64(codes.InvalidArgument)),
			"Message":    structpb.NewStringValue("error while validating client activeness"),
			"ErrorType":  structpb.NewStringValue(errors.ErrorTypeBadRequest), // "ErrorTypeBadRequest"
			"IsExpected": structpb.NewBoolValue(true),
			"Code":       structpb.NewNumberValue(float64(Error_ErrWhileValidatingClientActiveness)), // 4015
		},
	})

	ErrWhileValidatingClientActiveness = errors.GoErr{
		ErrType:    errors.ErrorTypeBadRequest, // "ErrorTypeBadRequest"
		Status:     statusErrWhileValidatingClientActiveness,
		Code:       int(Error_ErrWhileValidatingClientActiveness), // 4015
		Message:    "error while validating client activeness",
		GrpcCode:   codes.InvalidArgument,
		IsExpected: true,
	}
)

var (
	statusErrEmptyID, _ = status.New(
		codes.InvalidArgument,
		"received an empty ID value",
	).WithDetails(&structpb.Struct{
		Fields: map[string]*structpb.Value{
			"GrpcCode":   structpb.NewNumberValue(float64(codes.InvalidArgument)),
			"Message":    structpb.NewStringValue("received an empty ID value"),
			"ErrorType":  structpb.NewStringValue(errors.ErrorTypeBadRequest), // "ErrorTypeBadRequest"
			"IsExpected": structpb.NewBoolValue(true),
			"Code":       structpb.NewNumberValue(float64(Error_ErrEmptyID)), // 7001
		},
	})

	ErrEmptyID = errors.GoErr{
		ErrType:    errors.ErrorTypeBadRequest, // "ErrorTypeBadRequest"
		Status:     statusErrEmptyID,
		Code:       int(Error_ErrEmptyID), // 7001
		Message:    "received an empty ID value",
		GrpcCode:   codes.InvalidArgument,
		IsExpected: true,
	}
)

var (
	statusErrEmptyCredential, _ = status.New(
		codes.InvalidArgument,
		"received an empty credential body",
	).WithDetails(&structpb.Struct{
		Fields: map[string]*structpb.Value{
			"GrpcCode":   structpb.NewNumberValue(float64(codes.InvalidArgument)),
			"Message":    structpb.NewStringValue("received an empty credential body"),
			"ErrorType":  structpb.NewStringValue(errors.ErrorTypeBadRequest), // "ErrorTypeBadRequest"
			"IsExpected": structpb.NewBoolValue(true),
			"Code":       structpb.NewNumberValue(float64(Error_ErrEmptyCredential)), // 7002
		},
	})

	ErrEmptyCredential = errors.GoErr{
		ErrType:    errors.ErrorTypeBadRequest, // "ErrorTypeBadRequest"
		Status:     statusErrEmptyCredential,
		Code:       int(Error_ErrEmptyCredential), // 7002
		Message:    "received an empty credential body",
		GrpcCode:   codes.InvalidArgument,
		IsExpected: true,
	}
)

var (
	statusErrEmptyLogin, _ = status.New(
		codes.InvalidArgument,
		"received an empty login",
	).WithDetails(&structpb.Struct{
		Fields: map[string]*structpb.Value{
			"GrpcCode":   structpb.NewNumberValue(float64(codes.InvalidArgument)),
			"Message":    structpb.NewStringValue("received an empty login"),
			"ErrorType":  structpb.NewStringValue(errors.ErrorTypeBadRequest), // "ErrorTypeBadRequest"
			"IsExpected": structpb.NewBoolValue(true),
			"Code":       structpb.NewNumberValue(float64(Error_ErrEmptyLogin)), // 7003
		},
	})

	ErrEmptyLogin = errors.GoErr{
		ErrType:    errors.ErrorTypeBadRequest, // "ErrorTypeBadRequest"
		Status:     statusErrEmptyLogin,
		Code:       int(Error_ErrEmptyLogin), // 7003
		Message:    "received an empty login",
		GrpcCode:   codes.InvalidArgument,
		IsExpected: true,
	}
)

var (
	statusErrCannotCreateRequestForToken, _ = status.New(
		codes.InvalidArgument,
		"can not create request for token",
	).WithDetails(&structpb.Struct{
		Fields: map[string]*structpb.Value{
			"GrpcCode":   structpb.NewNumberValue(float64(codes.InvalidArgument)),
			"Message":    structpb.NewStringValue("can not create request for token"),
			"ErrorType":  structpb.NewStringValue(errors.ErrorTypeBadRequest), // "ErrorTypeBadRequest"
			"IsExpected": structpb.NewBoolValue(true),
			"Code":       structpb.NewNumberValue(float64(Error_ErrCannotCreateRequestForToken)), // 7004
		},
	})

	ErrCannotCreateRequestForToken = errors.GoErr{
		ErrType:    errors.ErrorTypeBadRequest, // "ErrorTypeBadRequest"
		Status:     statusErrCannotCreateRequestForToken,
		Code:       int(Error_ErrCannotCreateRequestForToken), // 7004
		Message:    "can not create request for token",
		GrpcCode:   codes.InvalidArgument,
		IsExpected: true,
	}
)

var (
	statusErrEmptyHolderName, _ = status.New(
		codes.InvalidArgument,
		"did not receive a card holder name",
	).WithDetails(&structpb.Struct{
		Fields: map[string]*structpb.Value{
			"GrpcCode":   structpb.NewNumberValue(float64(codes.InvalidArgument)),
			"Message":    structpb.NewStringValue("did not receive a card holder name"),
			"ErrorType":  structpb.NewStringValue(errors.ErrorTypeBadRequest), // "ErrorTypeBadRequest"
			"IsExpected": structpb.NewBoolValue(true),
			"Code":       structpb.NewNumberValue(float64(Error_ErrEmptyHolderName)), // 7005
		},
	})

	ErrEmptyHolderName = errors.GoErr{
		ErrType:    errors.ErrorTypeBadRequest, // "ErrorTypeBadRequest"
		Status:     statusErrEmptyHolderName,
		Code:       int(Error_ErrEmptyHolderName), // 7005
		Message:    "did not receive a card holder name",
		GrpcCode:   codes.InvalidArgument,
		IsExpected: true,
	}
)

var (
	statusErrEmptyCard, _ = status.New(
		codes.InvalidArgument,
		"did not receive a card information",
	).WithDetails(&structpb.Struct{
		Fields: map[string]*structpb.Value{
			"GrpcCode":   structpb.NewNumberValue(float64(codes.InvalidArgument)),
			"Message":    structpb.NewStringValue("did not receive a card information"),
			"ErrorType":  structpb.NewStringValue(errors.ErrorTypeBadRequest), // "ErrorTypeBadRequest"
			"IsExpected": structpb.NewBoolValue(true),
			"Code":       structpb.NewNumberValue(float64(Error_ErrEmptyCard)), // 7006
		},
	})

	ErrEmptyCard = errors.GoErr{
		ErrType:    errors.ErrorTypeBadRequest, // "ErrorTypeBadRequest"
		Status:     statusErrEmptyCard,
		Code:       int(Error_ErrEmptyCard), // 7006
		Message:    "did not receive a card information",
		GrpcCode:   codes.InvalidArgument,
		IsExpected: true,
	}
)

var (
	statusErrEmptyTerminal, _ = status.New(
		codes.InvalidArgument,
		"did not receive a terminal information",
	).WithDetails(&structpb.Struct{
		Fields: map[string]*structpb.Value{
			"GrpcCode":   structpb.NewNumberValue(float64(codes.InvalidArgument)),
			"Message":    structpb.NewStringValue("did not receive a terminal information"),
			"ErrorType":  structpb.NewStringValue(errors.ErrorTypeBadRequest), // "ErrorTypeBadRequest"
			"IsExpected": structpb.NewBoolValue(true),
			"Code":       structpb.NewNumberValue(float64(Error_ErrEmptyTerminal)), // 7007
		},
	})

	ErrEmptyTerminal = errors.GoErr{
		ErrType:    errors.ErrorTypeBadRequest, // "ErrorTypeBadRequest"
		Status:     statusErrEmptyTerminal,
		Code:       int(Error_ErrEmptyTerminal), // 7007
		Message:    "did not receive a terminal information",
		GrpcCode:   codes.InvalidArgument,
		IsExpected: true,
	}
)

var (
	statusErrEmptyUser, _ = status.New(
		codes.InvalidArgument,
		"did not receive an information about the user",
	).WithDetails(&structpb.Struct{
		Fields: map[string]*structpb.Value{
			"GrpcCode":   structpb.NewNumberValue(float64(codes.InvalidArgument)),
			"Message":    structpb.NewStringValue("did not receive an information about the user"),
			"ErrorType":  structpb.NewStringValue(errors.ErrorTypeBadRequest), // "ErrorTypeBadRequest"
			"IsExpected": structpb.NewBoolValue(true),
			"Code":       structpb.NewNumberValue(float64(Error_ErrEmptyUser)), // 7008
		},
	})

	ErrEmptyUser = errors.GoErr{
		ErrType:    errors.ErrorTypeBadRequest, // "ErrorTypeBadRequest"
		Status:     statusErrEmptyUser,
		Code:       int(Error_ErrEmptyUser), // 7008
		Message:    "did not receive an information about the user",
		GrpcCode:   codes.InvalidArgument,
		IsExpected: true,
	}
)

var (
	statusErrEmptyPaymentRequest, _ = status.New(
		codes.InvalidArgument,
		"did not receive a payment request information",
	).WithDetails(&structpb.Struct{
		Fields: map[string]*structpb.Value{
			"GrpcCode":   structpb.NewNumberValue(float64(codes.InvalidArgument)),
			"Message":    structpb.NewStringValue("did not receive a payment request information"),
			"ErrorType":  structpb.NewStringValue(errors.ErrorTypeBadRequest), // "ErrorTypeBadRequest"
			"IsExpected": structpb.NewBoolValue(true),
			"Code":       structpb.NewNumberValue(float64(Error_ErrEmptyPaymentRequest)), // 7009
		},
	})

	ErrEmptyPaymentRequest = errors.GoErr{
		ErrType:    errors.ErrorTypeBadRequest, // "ErrorTypeBadRequest"
		Status:     statusErrEmptyPaymentRequest,
		Code:       int(Error_ErrEmptyPaymentRequest), // 7009
		Message:    "did not receive a payment request information",
		GrpcCode:   codes.InvalidArgument,
		IsExpected: true,
	}
)

var (
	statusErrEmptyInvoiceID, _ = status.New(
		codes.InvalidArgument,
		"received an empty invoice ID",
	).WithDetails(&structpb.Struct{
		Fields: map[string]*structpb.Value{
			"GrpcCode":   structpb.NewNumberValue(float64(codes.InvalidArgument)),
			"Message":    structpb.NewStringValue("received an empty invoice ID"),
			"ErrorType":  structpb.NewStringValue(errors.ErrorTypeBadRequest), // "ErrorTypeBadRequest"
			"IsExpected": structpb.NewBoolValue(true),
			"Code":       structpb.NewNumberValue(float64(Error_ErrEmptyInvoiceID)), // 7010
		},
	})

	ErrEmptyInvoiceID = errors.GoErr{
		ErrType:    errors.ErrorTypeBadRequest, // "ErrorTypeBadRequest"
		Status:     statusErrEmptyInvoiceID,
		Code:       int(Error_ErrEmptyInvoiceID), // 7010
		Message:    "received an empty invoice ID",
		GrpcCode:   codes.InvalidArgument,
		IsExpected: true,
	}
)

var (
	statusErrEmptyTransactionID, _ = status.New(
		codes.InvalidArgument,
		"received an empty transaction ID",
	).WithDetails(&structpb.Struct{
		Fields: map[string]*structpb.Value{
			"GrpcCode":   structpb.NewNumberValue(float64(codes.InvalidArgument)),
			"Message":    structpb.NewStringValue("received an empty transaction ID"),
			"ErrorType":  structpb.NewStringValue(errors.ErrorTypeBadRequest), // "ErrorTypeBadRequest"
			"IsExpected": structpb.NewBoolValue(true),
			"Code":       structpb.NewNumberValue(float64(Error_ErrEmptyTransactionID)), // 7011
		},
	})

	ErrEmptyTransactionID = errors.GoErr{
		ErrType:    errors.ErrorTypeBadRequest, // "ErrorTypeBadRequest"
		Status:     statusErrEmptyTransactionID,
		Code:       int(Error_ErrEmptyTransactionID), // 7011
		Message:    "received an empty transaction ID",
		GrpcCode:   codes.InvalidArgument,
		IsExpected: true,
	}
)

var (
	statusEmptyThreeDSRequest, _ = status.New(
		codes.InvalidArgument,
		"received an empty 3ds request data",
	).WithDetails(&structpb.Struct{
		Fields: map[string]*structpb.Value{
			"GrpcCode":   structpb.NewNumberValue(float64(codes.InvalidArgument)),
			"Message":    structpb.NewStringValue("received an empty 3ds request data"),
			"ErrorType":  structpb.NewStringValue(errors.ErrorTypeBadRequest), // "ErrorTypeBadRequest"
			"IsExpected": structpb.NewBoolValue(true),
			"Code":       structpb.NewNumberValue(float64(Error_EmptyThreeDSRequest)), // 7013
		},
	})

	EmptyThreeDSRequest = errors.GoErr{
		ErrType:    errors.ErrorTypeBadRequest, // "ErrorTypeBadRequest"
		Status:     statusEmptyThreeDSRequest,
		Code:       int(Error_EmptyThreeDSRequest), // 7013
		Message:    "received an empty 3ds request data",
		GrpcCode:   codes.InvalidArgument,
		IsExpected: true,
	}
)

var (
	statusErrEmptyCheckStatusRequest, _ = status.New(
		codes.InvalidArgument,
		"did not receive a request for get transaction status method",
	).WithDetails(&structpb.Struct{
		Fields: map[string]*structpb.Value{
			"GrpcCode":   structpb.NewNumberValue(float64(codes.InvalidArgument)),
			"Message":    structpb.NewStringValue("did not receive a request for get transaction status method"),
			"ErrorType":  structpb.NewStringValue(errors.ErrorTypeBadRequest), // "ErrorTypeBadRequest"
			"IsExpected": structpb.NewBoolValue(true),
			"Code":       structpb.NewNumberValue(float64(Error_ErrEmptyCheckStatusRequest)), // 7014
		},
	})

	ErrEmptyCheckStatusRequest = errors.GoErr{
		ErrType:    errors.ErrorTypeBadRequest, // "ErrorTypeBadRequest"
		Status:     statusErrEmptyCheckStatusRequest,
		Code:       int(Error_ErrEmptyCheckStatusRequest), // 7014
		Message:    "did not receive a request for get transaction status method",
		GrpcCode:   codes.InvalidArgument,
		IsExpected: true,
	}
)

var (
	statusErrEmptyOnboardingRequest, _ = status.New(
		codes.InvalidArgument,
		"received empty onboarding request",
	).WithDetails(&structpb.Struct{
		Fields: map[string]*structpb.Value{
			"GrpcCode":   structpb.NewNumberValue(float64(codes.InvalidArgument)),
			"Message":    structpb.NewStringValue("received empty onboarding request"),
			"ErrorType":  structpb.NewStringValue(errors.ErrorTypeBadRequest), // "ErrorTypeBadRequest"
			"IsExpected": structpb.NewBoolValue(true),
			"Code":       structpb.NewNumberValue(float64(Error_ErrEmptyOnboardingRequest)), // 7021
		},
	})

	ErrEmptyOnboardingRequest = errors.GoErr{
		ErrType:    errors.ErrorTypeBadRequest, // "ErrorTypeBadRequest"
		Status:     statusErrEmptyOnboardingRequest,
		Code:       int(Error_ErrEmptyOnboardingRequest), // 7021
		Message:    "received empty onboarding request",
		GrpcCode:   codes.InvalidArgument,
		IsExpected: true,
	}
)

var (
	statusErrSavingClientToken, _ = status.New(
		codes.Unknown,
		"client card saving has been failed",
	).WithDetails(&structpb.Struct{
		Fields: map[string]*structpb.Value{
			"GrpcCode":   structpb.NewNumberValue(float64(codes.Unknown)),
			"Message":    structpb.NewStringValue("client card saving has been failed"),
			"ErrorType":  structpb.NewStringValue(errors.ErrorTypeUnexpectedError), // "ErrorTypeUnexpectedError"
			"IsExpected": structpb.NewBoolValue(true),
			"Code":       structpb.NewNumberValue(float64(Error_ErrSavingClientToken)), // 4100
		},
	})

	ErrSavingClientToken = errors.GoErr{
		ErrType:    errors.ErrorTypeUnexpectedError, // "ErrorTypeUnexpectedError"
		Status:     statusErrSavingClientToken,
		Code:       int(Error_ErrSavingClientToken), // 4100
		Message:    "client card saving has been failed",
		GrpcCode:   codes.Unknown,
		IsExpected: true,
	}
)

var (
	statusErrReceivingClientCard, _ = status.New(
		codes.Unknown,
		"could not receive client cards",
	).WithDetails(&structpb.Struct{
		Fields: map[string]*structpb.Value{
			"GrpcCode":   structpb.NewNumberValue(float64(codes.Unknown)),
			"Message":    structpb.NewStringValue("could not receive client cards"),
			"ErrorType":  structpb.NewStringValue(errors.ErrorTypeUnexpectedError), // "ErrorTypeUnexpectedError"
			"IsExpected": structpb.NewBoolValue(true),
			"Code":       structpb.NewNumberValue(float64(Error_ErrReceivingClientCard)), // 4101
		},
	})

	ErrReceivingClientCard = errors.GoErr{
		ErrType:    errors.ErrorTypeUnexpectedError, // "ErrorTypeUnexpectedError"
		Status:     statusErrReceivingClientCard,
		Code:       int(Error_ErrReceivingClientCard), // 4101
		Message:    "could not receive client cards",
		GrpcCode:   codes.Unknown,
		IsExpected: true,
	}
)

var (
	statusErrApprovingClientCard, _ = status.New(
		codes.Unknown,
		"client card approving has been failed",
	).WithDetails(&structpb.Struct{
		Fields: map[string]*structpb.Value{
			"GrpcCode":   structpb.NewNumberValue(float64(codes.Unknown)),
			"Message":    structpb.NewStringValue("client card approving has been failed"),
			"ErrorType":  structpb.NewStringValue(errors.ErrorTypeUnexpectedError), // "ErrorTypeUnexpectedError"
			"IsExpected": structpb.NewBoolValue(true),
			"Code":       structpb.NewNumberValue(float64(Error_ErrApprovingClientCard)), // 4102
		},
	})

	ErrApprovingClientCard = errors.GoErr{
		ErrType:    errors.ErrorTypeUnexpectedError, // "ErrorTypeUnexpectedError"
		Status:     statusErrApprovingClientCard,
		Code:       int(Error_ErrApprovingClientCard), // 4102
		Message:    "client card approving has been failed",
		GrpcCode:   codes.Unknown,
		IsExpected: true,
	}
)

var (
	statusErrEncryptingCardMonth, _ = status.New(
		codes.InvalidArgument,
		"an error occurred while encrypting card month",
	).WithDetails(&structpb.Struct{
		Fields: map[string]*structpb.Value{
			"GrpcCode":   structpb.NewNumberValue(float64(codes.InvalidArgument)),
			"Message":    structpb.NewStringValue("an error occurred while encrypting card month"),
			"ErrorType":  structpb.NewStringValue(errors.ErrorTypeBadRequest), // "ErrorTypeBadRequest"
			"IsExpected": structpb.NewBoolValue(true),
			"Code":       structpb.NewNumberValue(float64(Error_ErrEncryptingCardMonth)), // 4106
		},
	})

	ErrEncryptingCardMonth = errors.GoErr{
		ErrType:    errors.ErrorTypeBadRequest, // "ErrorTypeBadRequest"
		Status:     statusErrEncryptingCardMonth,
		Code:       int(Error_ErrEncryptingCardMonth), // 4106
		Message:    "an error occurred while encrypting card month",
		GrpcCode:   codes.InvalidArgument,
		IsExpected: true,
	}
)

var (
	statusErrEncryptingCardYear, _ = status.New(
		codes.InvalidArgument,
		"an error occurred while encrypting card year",
	).WithDetails(&structpb.Struct{
		Fields: map[string]*structpb.Value{
			"GrpcCode":   structpb.NewNumberValue(float64(codes.InvalidArgument)),
			"Message":    structpb.NewStringValue("an error occurred while encrypting card year"),
			"ErrorType":  structpb.NewStringValue(errors.ErrorTypeBadRequest), // "ErrorTypeBadRequest"
			"IsExpected": structpb.NewBoolValue(true),
			"Code":       structpb.NewNumberValue(float64(Error_ErrEncryptingCardYear)), // 4107
		},
	})

	ErrEncryptingCardYear = errors.GoErr{
		ErrType:    errors.ErrorTypeBadRequest, // "ErrorTypeBadRequest"
		Status:     statusErrEncryptingCardYear,
		Code:       int(Error_ErrEncryptingCardYear), // 4107
		Message:    "an error occurred while encrypting card year",
		GrpcCode:   codes.InvalidArgument,
		IsExpected: true,
	}
)

var (
	statusErrEncryptingCardName, _ = status.New(
		codes.InvalidArgument,
		"an error occurred while encrypting card name",
	).WithDetails(&structpb.Struct{
		Fields: map[string]*structpb.Value{
			"GrpcCode":   structpb.NewNumberValue(float64(codes.InvalidArgument)),
			"Message":    structpb.NewStringValue("an error occurred while encrypting card name"),
			"ErrorType":  structpb.NewStringValue(errors.ErrorTypeBadRequest), // "ErrorTypeBadRequest"
			"IsExpected": structpb.NewBoolValue(true),
			"Code":       structpb.NewNumberValue(float64(Error_ErrEncryptingCardName)), // 4108
		},
	})

	ErrEncryptingCardName = errors.GoErr{
		ErrType:    errors.ErrorTypeBadRequest, // "ErrorTypeBadRequest"
		Status:     statusErrEncryptingCardName,
		Code:       int(Error_ErrEncryptingCardName), // 4108
		Message:    "an error occurred while encrypting card name",
		GrpcCode:   codes.InvalidArgument,
		IsExpected: true,
	}
)

var (
	statusErrDecryptingCardMonth, _ = status.New(
		codes.InvalidArgument,
		"an error occurred while decrypting card month",
	).WithDetails(&structpb.Struct{
		Fields: map[string]*structpb.Value{
			"GrpcCode":   structpb.NewNumberValue(float64(codes.InvalidArgument)),
			"Message":    structpb.NewStringValue("an error occurred while decrypting card month"),
			"ErrorType":  structpb.NewStringValue(errors.ErrorTypeBadRequest), // "ErrorTypeBadRequest"
			"IsExpected": structpb.NewBoolValue(true),
			"Code":       structpb.NewNumberValue(float64(Error_ErrDecryptingCardMonth)), // 4109
		},
	})

	ErrDecryptingCardMonth = errors.GoErr{
		ErrType:    errors.ErrorTypeBadRequest, // "ErrorTypeBadRequest"
		Status:     statusErrDecryptingCardMonth,
		Code:       int(Error_ErrDecryptingCardMonth), // 4109
		Message:    "an error occurred while decrypting card month",
		GrpcCode:   codes.InvalidArgument,
		IsExpected: true,
	}
)

var (
	statusErrDecryptingCardYear, _ = status.New(
		codes.InvalidArgument,
		"an error occurred while decrypting card year",
	).WithDetails(&structpb.Struct{
		Fields: map[string]*structpb.Value{
			"GrpcCode":   structpb.NewNumberValue(float64(codes.InvalidArgument)),
			"Message":    structpb.NewStringValue("an error occurred while decrypting card year"),
			"ErrorType":  structpb.NewStringValue(errors.ErrorTypeBadRequest), // "ErrorTypeBadRequest"
			"IsExpected": structpb.NewBoolValue(true),
			"Code":       structpb.NewNumberValue(float64(Error_ErrDecryptingCardYear)), // 4110
		},
	})

	ErrDecryptingCardYear = errors.GoErr{
		ErrType:    errors.ErrorTypeBadRequest, // "ErrorTypeBadRequest"
		Status:     statusErrDecryptingCardYear,
		Code:       int(Error_ErrDecryptingCardYear), // 4110
		Message:    "an error occurred while decrypting card year",
		GrpcCode:   codes.InvalidArgument,
		IsExpected: true,
	}
)

var (
	statusErrDecryptingCardName, _ = status.New(
		codes.InvalidArgument,
		"an error occurred while decrypting card name",
	).WithDetails(&structpb.Struct{
		Fields: map[string]*structpb.Value{
			"GrpcCode":   structpb.NewNumberValue(float64(codes.InvalidArgument)),
			"Message":    structpb.NewStringValue("an error occurred while decrypting card name"),
			"ErrorType":  structpb.NewStringValue(errors.ErrorTypeBadRequest), // "ErrorTypeBadRequest"
			"IsExpected": structpb.NewBoolValue(true),
			"Code":       structpb.NewNumberValue(float64(Error_ErrDecryptingCardName)), // 4111
		},
	})

	ErrDecryptingCardName = errors.GoErr{
		ErrType:    errors.ErrorTypeBadRequest, // "ErrorTypeBadRequest"
		Status:     statusErrDecryptingCardName,
		Code:       int(Error_ErrDecryptingCardName), // 4111
		Message:    "an error occurred while decrypting card name",
		GrpcCode:   codes.InvalidArgument,
		IsExpected: true,
	}
)

var (
	statusErrEncryptingCardPan, _ = status.New(
		codes.InvalidArgument,
		"an error occurred while encrypting card pan",
	).WithDetails(&structpb.Struct{
		Fields: map[string]*structpb.Value{
			"GrpcCode":   structpb.NewNumberValue(float64(codes.InvalidArgument)),
			"Message":    structpb.NewStringValue("an error occurred while encrypting card pan"),
			"ErrorType":  structpb.NewStringValue(errors.ErrorTypeBadRequest), // "ErrorTypeBadRequest"
			"IsExpected": structpb.NewBoolValue(true),
			"Code":       structpb.NewNumberValue(float64(Error_ErrEncryptingCardPan)), // 4112
		},
	})

	ErrEncryptingCardPan = errors.GoErr{
		ErrType:    errors.ErrorTypeBadRequest, // "ErrorTypeBadRequest"
		Status:     statusErrEncryptingCardPan,
		Code:       int(Error_ErrEncryptingCardPan), // 4112
		Message:    "an error occurred while encrypting card pan",
		GrpcCode:   codes.InvalidArgument,
		IsExpected: true,
	}
)

var (
	statusErrDecryptingCardPan, _ = status.New(
		codes.InvalidArgument,
		"an error occurred while decrypting card pan",
	).WithDetails(&structpb.Struct{
		Fields: map[string]*structpb.Value{
			"GrpcCode":   structpb.NewNumberValue(float64(codes.InvalidArgument)),
			"Message":    structpb.NewStringValue("an error occurred while decrypting card pan"),
			"ErrorType":  structpb.NewStringValue(errors.ErrorTypeBadRequest), // "ErrorTypeBadRequest"
			"IsExpected": structpb.NewBoolValue(true),
			"Code":       structpb.NewNumberValue(float64(Error_ErrDecryptingCardPan)), // 4113
		},
	})

	ErrDecryptingCardPan = errors.GoErr{
		ErrType:    errors.ErrorTypeBadRequest, // "ErrorTypeBadRequest"
		Status:     statusErrDecryptingCardPan,
		Code:       int(Error_ErrDecryptingCardPan), // 4113
		Message:    "an error occurred while decrypting card pan",
		GrpcCode:   codes.InvalidArgument,
		IsExpected: true,
	}
)

var (
	statusErrInvalidCardToken, _ = status.New(
		codes.InvalidArgument,
		"invalid card token",
	).WithDetails(&structpb.Struct{
		Fields: map[string]*structpb.Value{
			"GrpcCode":   structpb.NewNumberValue(float64(codes.InvalidArgument)),
			"Message":    structpb.NewStringValue("invalid card token"),
			"ErrorType":  structpb.NewStringValue(errors.ErrorTypeBadRequest), // "ErrorTypeBadRequest"
			"IsExpected": structpb.NewBoolValue(true),
			"Code":       structpb.NewNumberValue(float64(Error_ErrInvalidCardToken)), // 4103
		},
	})

	ErrInvalidCardToken = errors.GoErr{
		ErrType:    errors.ErrorTypeBadRequest, // "ErrorTypeBadRequest"
		Status:     statusErrInvalidCardToken,
		Code:       int(Error_ErrInvalidCardToken), // 4103
		Message:    "invalid card token",
		GrpcCode:   codes.InvalidArgument,
		IsExpected: true,
	}
)

var (
	statusErrDecryptingCardToken, _ = status.New(
		codes.InvalidArgument,
		"an error occurred while decrypting card token",
	).WithDetails(&structpb.Struct{
		Fields: map[string]*structpb.Value{
			"GrpcCode":   structpb.NewNumberValue(float64(codes.InvalidArgument)),
			"Message":    structpb.NewStringValue("an error occurred while decrypting card token"),
			"ErrorType":  structpb.NewStringValue(errors.ErrorTypeBadRequest), // "ErrorTypeBadRequest"
			"IsExpected": structpb.NewBoolValue(true),
			"Code":       structpb.NewNumberValue(float64(Error_ErrDecryptingCardToken)), // 4104
		},
	})

	ErrDecryptingCardToken = errors.GoErr{
		ErrType:    errors.ErrorTypeBadRequest, // "ErrorTypeBadRequest"
		Status:     statusErrDecryptingCardToken,
		Code:       int(Error_ErrDecryptingCardToken), // 4104
		Message:    "an error occurred while decrypting card token",
		GrpcCode:   codes.InvalidArgument,
		IsExpected: true,
	}
)

var (
	statusErrEncryptingCardToken, _ = status.New(
		codes.InvalidArgument,
		"an error occurred while encrypting card token",
	).WithDetails(&structpb.Struct{
		Fields: map[string]*structpb.Value{
			"GrpcCode":   structpb.NewNumberValue(float64(codes.InvalidArgument)),
			"Message":    structpb.NewStringValue("an error occurred while encrypting card token"),
			"ErrorType":  structpb.NewStringValue(errors.ErrorTypeBadRequest), // "ErrorTypeBadRequest"
			"IsExpected": structpb.NewBoolValue(true),
			"Code":       structpb.NewNumberValue(float64(Error_ErrEncryptingCardToken)), // 4105
		},
	})

	ErrEncryptingCardToken = errors.GoErr{
		ErrType:    errors.ErrorTypeBadRequest, // "ErrorTypeBadRequest"
		Status:     statusErrEncryptingCardToken,
		Code:       int(Error_ErrEncryptingCardToken), // 4105
		Message:    "an error occurred while encrypting card token",
		GrpcCode:   codes.InvalidArgument,
		IsExpected: true,
	}
)

var (
	statusErrMakingPayment, _ = status.New(
		codes.Unknown,
		"received error while making a payment",
	).WithDetails(&structpb.Struct{
		Fields: map[string]*structpb.Value{
			"GrpcCode":   structpb.NewNumberValue(float64(codes.Unknown)),
			"Message":    structpb.NewStringValue("received error while making a payment"),
			"ErrorType":  structpb.NewStringValue(errors.ErrorTypeUnexpectedError), // "ErrorTypeUnexpectedError"
			"IsExpected": structpb.NewBoolValue(true),
			"Code":       structpb.NewNumberValue(float64(Error_ErrMakingPayment)), // 7018
		},
	})

	ErrMakingPayment = errors.GoErr{
		ErrType:    errors.ErrorTypeUnexpectedError, // "ErrorTypeUnexpectedError"
		Status:     statusErrMakingPayment,
		Code:       int(Error_ErrMakingPayment), // 7018
		Message:    "received error while making a payment",
		GrpcCode:   codes.Unknown,
		IsExpected: true,
	}
)

var (
	statusErrInvalidUIN, _ = status.New(
		codes.InvalidArgument,
		"invalid uin",
	).WithDetails(&structpb.Struct{
		Fields: map[string]*structpb.Value{
			"GrpcCode":   structpb.NewNumberValue(float64(codes.InvalidArgument)),
			"Message":    structpb.NewStringValue("invalid uin"),
			"ErrorType":  structpb.NewStringValue(errors.ErrorTypeBadRequest), // "ErrorTypeBadRequest"
			"IsExpected": structpb.NewBoolValue(true),
			"Code":       structpb.NewNumberValue(float64(Error_ErrInvalidUIN)), // 2201
		},
	})

	ErrInvalidUIN = errors.GoErr{
		ErrType:    errors.ErrorTypeBadRequest, // "ErrorTypeBadRequest"
		Status:     statusErrInvalidUIN,
		Code:       int(Error_ErrInvalidUIN), // 2201
		Message:    "invalid uin",
		GrpcCode:   codes.InvalidArgument,
		IsExpected: true,
	}
)

var (
	statusErrEmptyCardID, _ = status.New(
		codes.InvalidArgument,
		"received an empty card ID",
	).WithDetails(&structpb.Struct{
		Fields: map[string]*structpb.Value{
			"GrpcCode":   structpb.NewNumberValue(float64(codes.InvalidArgument)),
			"Message":    structpb.NewStringValue("received an empty card ID"),
			"ErrorType":  structpb.NewStringValue(errors.ErrorTypeBadRequest), // "ErrorTypeBadRequest"
			"IsExpected": structpb.NewBoolValue(true),
			"Code":       structpb.NewNumberValue(float64(Error_ErrEmptyCardID)), // 7012
		},
	})

	ErrEmptyCardID = errors.GoErr{
		ErrType:    errors.ErrorTypeBadRequest, // "ErrorTypeBadRequest"
		Status:     statusErrEmptyCardID,
		Code:       int(Error_ErrEmptyCardID), // 7012
		Message:    "received an empty card ID",
		GrpcCode:   codes.InvalidArgument,
		IsExpected: true,
	}
)

var (
	statusErrEmptyMerchantRequest, _ = status.New(
		codes.InvalidArgument,
		"received empty merchant request",
	).WithDetails(&structpb.Struct{
		Fields: map[string]*structpb.Value{
			"GrpcCode":   structpb.NewNumberValue(float64(codes.InvalidArgument)),
			"Message":    structpb.NewStringValue("received empty merchant request"),
			"ErrorType":  structpb.NewStringValue(errors.ErrorTypeBadRequest), // "ErrorTypeBadRequest"
			"IsExpected": structpb.NewBoolValue(true),
			"Code":       structpb.NewNumberValue(float64(Error_ErrEmptyMerchantRequest)), // 8069
		},
	})

	ErrEmptyMerchantRequest = errors.GoErr{
		ErrType:    errors.ErrorTypeBadRequest, // "ErrorTypeBadRequest"
		Status:     statusErrEmptyMerchantRequest,
		Code:       int(Error_ErrEmptyMerchantRequest), // 8069
		Message:    "received empty merchant request",
		GrpcCode:   codes.InvalidArgument,
		IsExpected: true,
	}
)

var (
	statusErrInvalidCardData, _ = status.New(
		codes.InvalidArgument,
		"invalid card data",
	).WithDetails(&structpb.Struct{
		Fields: map[string]*structpb.Value{
			"GrpcCode":   structpb.NewNumberValue(float64(codes.InvalidArgument)),
			"Message":    structpb.NewStringValue("invalid card data"),
			"ErrorType":  structpb.NewStringValue(errors.ErrorTypeBadRequest), // "ErrorTypeBadRequest"
			"IsExpected": structpb.NewBoolValue(true),
			"Code":       structpb.NewNumberValue(float64(Error_ErrInvalidCardData)), // 8070
		},
	})

	ErrInvalidCardData = errors.GoErr{
		ErrType:    errors.ErrorTypeBadRequest, // "ErrorTypeBadRequest"
		Status:     statusErrInvalidCardData,
		Code:       int(Error_ErrInvalidCardData), // 8070
		Message:    "invalid card data",
		GrpcCode:   codes.InvalidArgument,
		IsExpected: true,
	}
)

var (
	statusErrTransactionIsNotPayInType, _ = status.New(
		codes.InvalidArgument,
		"transaction is not pay-in type",
	).WithDetails(&structpb.Struct{
		Fields: map[string]*structpb.Value{
			"GrpcCode":   structpb.NewNumberValue(float64(codes.InvalidArgument)),
			"Message":    structpb.NewStringValue("transaction is not pay-in type"),
			"ErrorType":  structpb.NewStringValue(errors.ErrorTypeBadRequest), // "ErrorTypeBadRequest"
			"IsExpected": structpb.NewBoolValue(true),
			"Code":       structpb.NewNumberValue(float64(Error_ErrTransactionIsNotPayInType)), // 5500
		},
	})

	ErrTransactionIsNotPayInType = errors.GoErr{
		ErrType:    errors.ErrorTypeBadRequest, // "ErrorTypeBadRequest"
		Status:     statusErrTransactionIsNotPayInType,
		Code:       int(Error_ErrTransactionIsNotPayInType), // 5500
		Message:    "transaction is not pay-in type",
		GrpcCode:   codes.InvalidArgument,
		IsExpected: true,
	}
)

var (
	statusErrTransactionIsNotSuccess, _ = status.New(
		codes.InvalidArgument,
		"transaction is not in success status",
	).WithDetails(&structpb.Struct{
		Fields: map[string]*structpb.Value{
			"GrpcCode":   structpb.NewNumberValue(float64(codes.InvalidArgument)),
			"Message":    structpb.NewStringValue("transaction is not in success status"),
			"ErrorType":  structpb.NewStringValue(errors.ErrorTypeBadRequest), // "ErrorTypeBadRequest"
			"IsExpected": structpb.NewBoolValue(true),
			"Code":       structpb.NewNumberValue(float64(Error_ErrTransactionIsNotSuccess)), // 5501
		},
	})

	ErrTransactionIsNotSuccess = errors.GoErr{
		ErrType:    errors.ErrorTypeBadRequest, // "ErrorTypeBadRequest"
		Status:     statusErrTransactionIsNotSuccess,
		Code:       int(Error_ErrTransactionIsNotSuccess), // 5501
		Message:    "transaction is not in success status",
		GrpcCode:   codes.InvalidArgument,
		IsExpected: true,
	}
)

var (
	statusErrLimitTransactionRefundAmount, _ = status.New(
		codes.InvalidArgument,
		"refunded amount must be less than amount",
	).WithDetails(&structpb.Struct{
		Fields: map[string]*structpb.Value{
			"GrpcCode":   structpb.NewNumberValue(float64(codes.InvalidArgument)),
			"Message":    structpb.NewStringValue("refunded amount must be less than amount"),
			"ErrorType":  structpb.NewStringValue(errors.ErrorTypeBadRequest), // "ErrorTypeBadRequest"
			"IsExpected": structpb.NewBoolValue(true),
			"Code":       structpb.NewNumberValue(float64(Error_ErrLimitTransactionRefundAmount)), // 5502
		},
	})

	ErrLimitTransactionRefundAmount = errors.GoErr{
		ErrType:    errors.ErrorTypeBadRequest, // "ErrorTypeBadRequest"
		Status:     statusErrLimitTransactionRefundAmount,
		Code:       int(Error_ErrLimitTransactionRefundAmount), // 5502
		Message:    "refunded amount must be less than amount",
		GrpcCode:   codes.InvalidArgument,
		IsExpected: true,
	}
)

var (
	statusErrNumberMoreThanExpected, _ = status.New(
		codes.InvalidArgument,
		"the number of attempts is more than expected",
	).WithDetails(&structpb.Struct{
		Fields: map[string]*structpb.Value{
			"GrpcCode":   structpb.NewNumberValue(float64(codes.InvalidArgument)),
			"Message":    structpb.NewStringValue("the number of attempts is more than expected"),
			"ErrorType":  structpb.NewStringValue(errors.ErrorTypeBadRequest), // "ErrorTypeBadRequest"
			"IsExpected": structpb.NewBoolValue(true),
			"Code":       structpb.NewNumberValue(float64(Error_ErrNumberMoreThanExpected)), // 5503
		},
	})

	ErrNumberMoreThanExpected = errors.GoErr{
		ErrType:    errors.ErrorTypeBadRequest, // "ErrorTypeBadRequest"
		Status:     statusErrNumberMoreThanExpected,
		Code:       int(Error_ErrNumberMoreThanExpected), // 5503
		Message:    "the number of attempts is more than expected",
		GrpcCode:   codes.InvalidArgument,
		IsExpected: true,
	}
)

var (
	statusErrMoreThanInitialAmount, _ = status.New(
		codes.InvalidArgument,
		"can't request amount more than initial amount",
	).WithDetails(&structpb.Struct{
		Fields: map[string]*structpb.Value{
			"GrpcCode":   structpb.NewNumberValue(float64(codes.InvalidArgument)),
			"Message":    structpb.NewStringValue("can't request amount more than initial amount"),
			"ErrorType":  structpb.NewStringValue(errors.ErrorTypeBadRequest), // "ErrorTypeBadRequest"
			"IsExpected": structpb.NewBoolValue(true),
			"Code":       structpb.NewNumberValue(float64(Error_ErrMoreThanInitialAmount)), // 5504
		},
	})

	ErrMoreThanInitialAmount = errors.GoErr{
		ErrType:    errors.ErrorTypeBadRequest, // "ErrorTypeBadRequest"
		Status:     statusErrMoreThanInitialAmount,
		Code:       int(Error_ErrMoreThanInitialAmount), // 5504
		Message:    "can't request amount more than initial amount",
		GrpcCode:   codes.InvalidArgument,
		IsExpected: true,
	}
)

var (
	statusErrAmountDoesNotMatch, _ = status.New(
		codes.InvalidArgument,
		"the amount does not match",
	).WithDetails(&structpb.Struct{
		Fields: map[string]*structpb.Value{
			"GrpcCode":   structpb.NewNumberValue(float64(codes.InvalidArgument)),
			"Message":    structpb.NewStringValue("the amount does not match"),
			"ErrorType":  structpb.NewStringValue(errors.ErrorTypeBadRequest), // "ErrorTypeBadRequest"
			"IsExpected": structpb.NewBoolValue(true),
			"Code":       structpb.NewNumberValue(float64(Error_ErrAmountDoesNotMatch)), // 5505
		},
	})

	ErrAmountDoesNotMatch = errors.GoErr{
		ErrType:    errors.ErrorTypeBadRequest, // "ErrorTypeBadRequest"
		Status:     statusErrAmountDoesNotMatch,
		Code:       int(Error_ErrAmountDoesNotMatch), // 5505
		Message:    "the amount does not match",
		GrpcCode:   codes.InvalidArgument,
		IsExpected: true,
	}
)

var (
	statusErrNumberOfDaysIsOver, _ = status.New(
		codes.InvalidArgument,
		"number of days is over",
	).WithDetails(&structpb.Struct{
		Fields: map[string]*structpb.Value{
			"GrpcCode":   structpb.NewNumberValue(float64(codes.InvalidArgument)),
			"Message":    structpb.NewStringValue("number of days is over"),
			"ErrorType":  structpb.NewStringValue(errors.ErrorTypeBadRequest), // "ErrorTypeBadRequest"
			"IsExpected": structpb.NewBoolValue(true),
			"Code":       structpb.NewNumberValue(float64(Error_ErrNumberOfDaysIsOver)), // 5506
		},
	})

	ErrNumberOfDaysIsOver = errors.GoErr{
		ErrType:    errors.ErrorTypeBadRequest, // "ErrorTypeBadRequest"
		Status:     statusErrNumberOfDaysIsOver,
		Code:       int(Error_ErrNumberOfDaysIsOver), // 5506
		Message:    "number of days is over",
		GrpcCode:   codes.InvalidArgument,
		IsExpected: true,
	}
)

var (
	statusErrRefundNotAllowed, _ = status.New(
		codes.InvalidArgument,
		"not allowed to refund",
	).WithDetails(&structpb.Struct{
		Fields: map[string]*structpb.Value{
			"GrpcCode":   structpb.NewNumberValue(float64(codes.InvalidArgument)),
			"Message":    structpb.NewStringValue("not allowed to refund"),
			"ErrorType":  structpb.NewStringValue(errors.ErrorTypeBadRequest), // "ErrorTypeBadRequest"
			"IsExpected": structpb.NewBoolValue(true),
			"Code":       structpb.NewNumberValue(float64(Error_ErrRefundNotAllowed)), // 5507
		},
	})

	ErrRefundNotAllowed = errors.GoErr{
		ErrType:    errors.ErrorTypeBadRequest, // "ErrorTypeBadRequest"
		Status:     statusErrRefundNotAllowed,
		Code:       int(Error_ErrRefundNotAllowed), // 5507
		Message:    "not allowed to refund",
		GrpcCode:   codes.InvalidArgument,
		IsExpected: true,
	}
)

var (
	statusErrEmptyBankOrderID, _ = status.New(
		codes.InvalidArgument,
		"received an empty bank order ID value",
	).WithDetails(&structpb.Struct{
		Fields: map[string]*structpb.Value{
			"GrpcCode":   structpb.NewNumberValue(float64(codes.InvalidArgument)),
			"Message":    structpb.NewStringValue("received an empty bank order ID value"),
			"ErrorType":  structpb.NewStringValue(errors.ErrorTypeBadRequest), // "ErrorTypeBadRequest"
			"IsExpected": structpb.NewBoolValue(true),
			"Code":       structpb.NewNumberValue(float64(Error_ErrEmptyBankOrderID)), // 5510
		},
	})

	ErrEmptyBankOrderID = errors.GoErr{
		ErrType:    errors.ErrorTypeBadRequest, // "ErrorTypeBadRequest"
		Status:     statusErrEmptyBankOrderID,
		Code:       int(Error_ErrEmptyBankOrderID), // 5510
		Message:    "received an empty bank order ID value",
		GrpcCode:   codes.InvalidArgument,
		IsExpected: true,
	}
)

var (
	statusErrRefundSettingCreation, _ = status.New(
		codes.InvalidArgument,
		"create refund setting failed",
	).WithDetails(&structpb.Struct{
		Fields: map[string]*structpb.Value{
			"GrpcCode":   structpb.NewNumberValue(float64(codes.InvalidArgument)),
			"Message":    structpb.NewStringValue("create refund setting failed"),
			"ErrorType":  structpb.NewStringValue(errors.ErrorTypeBadRequest), // "ErrorTypeBadRequest"
			"IsExpected": structpb.NewBoolValue(true),
			"Code":       structpb.NewNumberValue(float64(Error_ErrRefundSettingCreation)), // 5511
		},
	})

	ErrRefundSettingCreation = errors.GoErr{
		ErrType:    errors.ErrorTypeBadRequest, // "ErrorTypeBadRequest"
		Status:     statusErrRefundSettingCreation,
		Code:       int(Error_ErrRefundSettingCreation), // 5511
		Message:    "create refund setting failed",
		GrpcCode:   codes.InvalidArgument,
		IsExpected: true,
	}
)

var (
	statusErrRefundReasonCreation, _ = status.New(
		codes.InvalidArgument,
		"create refund reason failed",
	).WithDetails(&structpb.Struct{
		Fields: map[string]*structpb.Value{
			"GrpcCode":   structpb.NewNumberValue(float64(codes.InvalidArgument)),
			"Message":    structpb.NewStringValue("create refund reason failed"),
			"ErrorType":  structpb.NewStringValue(errors.ErrorTypeBadRequest), // "ErrorTypeBadRequest"
			"IsExpected": structpb.NewBoolValue(true),
			"Code":       structpb.NewNumberValue(float64(Error_ErrRefundReasonCreation)), // 5512
		},
	})

	ErrRefundReasonCreation = errors.GoErr{
		ErrType:    errors.ErrorTypeBadRequest, // "ErrorTypeBadRequest"
		Status:     statusErrRefundReasonCreation,
		Code:       int(Error_ErrRefundReasonCreation), // 5512
		Message:    "create refund reason failed",
		GrpcCode:   codes.InvalidArgument,
		IsExpected: true,
	}
)

var (
	statusErrNoTransactionRefundToUpdate, _ = status.New(
		codes.NotFound,
		"сould not find the appropriate refund transaction for the update",
	).WithDetails(&structpb.Struct{
		Fields: map[string]*structpb.Value{
			"GrpcCode":   structpb.NewNumberValue(float64(codes.NotFound)),
			"Message":    structpb.NewStringValue("сould not find the appropriate refund transaction for the update"),
			"ErrorType":  structpb.NewStringValue(errors.ErrorTypeNotFound), // "ErrorTypeNotFound"
			"IsExpected": structpb.NewBoolValue(true),
			"Code":       structpb.NewNumberValue(float64(Error_ErrNoTransactionRefundToUpdate)), // 5513
		},
	})

	ErrNoTransactionRefundToUpdate = errors.GoErr{
		ErrType:    errors.ErrorTypeNotFound, // "ErrorTypeNotFound"
		Status:     statusErrNoTransactionRefundToUpdate,
		Code:       int(Error_ErrNoTransactionRefundToUpdate), // 5513
		Message:    "сould not find the appropriate refund transaction for the update",
		GrpcCode:   codes.NotFound,
		IsExpected: true,
	}
)

var (
	statusErrTransactionRefundCreation, _ = status.New(
		codes.InvalidArgument,
		"create transaction refund failed",
	).WithDetails(&structpb.Struct{
		Fields: map[string]*structpb.Value{
			"GrpcCode":   structpb.NewNumberValue(float64(codes.InvalidArgument)),
			"Message":    structpb.NewStringValue("create transaction refund failed"),
			"ErrorType":  structpb.NewStringValue(errors.ErrorTypeBadRequest), // "ErrorTypeBadRequest"
			"IsExpected": structpb.NewBoolValue(true),
			"Code":       structpb.NewNumberValue(float64(Error_ErrTransactionRefundCreation)), // 5514
		},
	})

	ErrTransactionRefundCreation = errors.GoErr{
		ErrType:    errors.ErrorTypeBadRequest, // "ErrorTypeBadRequest"
		Status:     statusErrTransactionRefundCreation,
		Code:       int(Error_ErrTransactionRefundCreation), // 5514
		Message:    "create transaction refund failed",
		GrpcCode:   codes.InvalidArgument,
		IsExpected: true,
	}
)

var (
	statusErrCurrencyNotFound, _ = status.New(
		codes.NotFound,
		"currency doesn't exist",
	).WithDetails(&structpb.Struct{
		Fields: map[string]*structpb.Value{
			"GrpcCode":   structpb.NewNumberValue(float64(codes.NotFound)),
			"Message":    structpb.NewStringValue("currency doesn't exist"),
			"ErrorType":  structpb.NewStringValue(errors.ErrorTypeNotFound), // "ErrorTypeNotFound"
			"IsExpected": structpb.NewBoolValue(true),
			"Code":       structpb.NewNumberValue(float64(Error_ErrCurrencyNotFound)), // 3100
		},
	})

	ErrCurrencyNotFound = errors.GoErr{
		ErrType:    errors.ErrorTypeNotFound, // "ErrorTypeNotFound"
		Status:     statusErrCurrencyNotFound,
		Code:       int(Error_ErrCurrencyNotFound), // 3100
		Message:    "currency doesn't exist",
		GrpcCode:   codes.NotFound,
		IsExpected: true,
	}
)

var (
	statusErrAcquirerNotFound, _ = status.New(
		codes.NotFound,
		"acquirer doesn't exist",
	).WithDetails(&structpb.Struct{
		Fields: map[string]*structpb.Value{
			"GrpcCode":   structpb.NewNumberValue(float64(codes.NotFound)),
			"Message":    structpb.NewStringValue("acquirer doesn't exist"),
			"ErrorType":  structpb.NewStringValue(errors.ErrorTypeNotFound), // "ErrorTypeNotFound"
			"IsExpected": structpb.NewBoolValue(true),
			"Code":       structpb.NewNumberValue(float64(Error_ErrAcquirerNotFound)), // 3101
		},
	})

	ErrAcquirerNotFound = errors.GoErr{
		ErrType:    errors.ErrorTypeNotFound, // "ErrorTypeNotFound"
		Status:     statusErrAcquirerNotFound,
		Code:       int(Error_ErrAcquirerNotFound), // 3101
		Message:    "acquirer doesn't exist",
		GrpcCode:   codes.NotFound,
		IsExpected: true,
	}
)

var (
	statusErrAcquirerCardNotFound, _ = status.New(
		codes.NotFound,
		"acquirer card doesn't exist",
	).WithDetails(&structpb.Struct{
		Fields: map[string]*structpb.Value{
			"GrpcCode":   structpb.NewNumberValue(float64(codes.NotFound)),
			"Message":    structpb.NewStringValue("acquirer card doesn't exist"),
			"ErrorType":  structpb.NewStringValue(errors.ErrorTypeNotFound), // "ErrorTypeNotFound"
			"IsExpected": structpb.NewBoolValue(true),
			"Code":       structpb.NewNumberValue(float64(Error_ErrAcquirerCardNotFound)), // 3102
		},
	})

	ErrAcquirerCardNotFound = errors.GoErr{
		ErrType:    errors.ErrorTypeNotFound, // "ErrorTypeNotFound"
		Status:     statusErrAcquirerCardNotFound,
		Code:       int(Error_ErrAcquirerCardNotFound), // 3102
		Message:    "acquirer card doesn't exist",
		GrpcCode:   codes.NotFound,
		IsExpected: true,
	}
)

var (
	statusErrAcquirerMccNotFound, _ = status.New(
		codes.NotFound,
		"acquirer mcc doesn't exist",
	).WithDetails(&structpb.Struct{
		Fields: map[string]*structpb.Value{
			"GrpcCode":   structpb.NewNumberValue(float64(codes.NotFound)),
			"Message":    structpb.NewStringValue("acquirer mcc doesn't exist"),
			"ErrorType":  structpb.NewStringValue(errors.ErrorTypeNotFound), // "ErrorTypeNotFound"
			"IsExpected": structpb.NewBoolValue(true),
			"Code":       structpb.NewNumberValue(float64(Error_ErrAcquirerMccNotFound)), // 3103
		},
	})

	ErrAcquirerMccNotFound = errors.GoErr{
		ErrType:    errors.ErrorTypeNotFound, // "ErrorTypeNotFound"
		Status:     statusErrAcquirerMccNotFound,
		Code:       int(Error_ErrAcquirerMccNotFound), // 3103
		Message:    "acquirer mcc doesn't exist",
		GrpcCode:   codes.NotFound,
		IsExpected: true,
	}
)

var (
	statusErrBankNotFound, _ = status.New(
		codes.NotFound,
		"bank doesn't exist",
	).WithDetails(&structpb.Struct{
		Fields: map[string]*structpb.Value{
			"GrpcCode":   structpb.NewNumberValue(float64(codes.NotFound)),
			"Message":    structpb.NewStringValue("bank doesn't exist"),
			"ErrorType":  structpb.NewStringValue(errors.ErrorTypeNotFound), // "ErrorTypeNotFound"
			"IsExpected": structpb.NewBoolValue(true),
			"Code":       structpb.NewNumberValue(float64(Error_ErrBankNotFound)), // 3104
		},
	})

	ErrBankNotFound = errors.GoErr{
		ErrType:    errors.ErrorTypeNotFound, // "ErrorTypeNotFound"
		Status:     statusErrBankNotFound,
		Code:       int(Error_ErrBankNotFound), // 3104
		Message:    "bank doesn't exist",
		GrpcCode:   codes.NotFound,
		IsExpected: true,
	}
)

var (
	statusErrBankBinNotFound, _ = status.New(
		codes.NotFound,
		"bank bin doesn't exist",
	).WithDetails(&structpb.Struct{
		Fields: map[string]*structpb.Value{
			"GrpcCode":   structpb.NewNumberValue(float64(codes.NotFound)),
			"Message":    structpb.NewStringValue("bank bin doesn't exist"),
			"ErrorType":  structpb.NewStringValue(errors.ErrorTypeNotFound), // "ErrorTypeNotFound"
			"IsExpected": structpb.NewBoolValue(true),
			"Code":       structpb.NewNumberValue(float64(Error_ErrBankBinNotFound)), // 3105
		},
	})

	ErrBankBinNotFound = errors.GoErr{
		ErrType:    errors.ErrorTypeNotFound, // "ErrorTypeNotFound"
		Status:     statusErrBankBinNotFound,
		Code:       int(Error_ErrBankBinNotFound), // 3105
		Message:    "bank bin doesn't exist",
		GrpcCode:   codes.NotFound,
		IsExpected: true,
	}
)

var (
	statusErrBankLimitNotFound, _ = status.New(
		codes.NotFound,
		"bank limit doesn't exist",
	).WithDetails(&structpb.Struct{
		Fields: map[string]*structpb.Value{
			"GrpcCode":   structpb.NewNumberValue(float64(codes.NotFound)),
			"Message":    structpb.NewStringValue("bank limit doesn't exist"),
			"ErrorType":  structpb.NewStringValue(errors.ErrorTypeNotFound), // "ErrorTypeNotFound"
			"IsExpected": structpb.NewBoolValue(true),
			"Code":       structpb.NewNumberValue(float64(Error_ErrBankLimitNotFound)), // 3106
		},
	})

	ErrBankLimitNotFound = errors.GoErr{
		ErrType:    errors.ErrorTypeNotFound, // "ErrorTypeNotFound"
		Status:     statusErrBankLimitNotFound,
		Code:       int(Error_ErrBankLimitNotFound), // 3106
		Message:    "bank limit doesn't exist",
		GrpcCode:   codes.NotFound,
		IsExpected: true,
	}
)

var (
	statusErrBankServiceNotFound, _ = status.New(
		codes.NotFound,
		"bank service doesn't exist",
	).WithDetails(&structpb.Struct{
		Fields: map[string]*structpb.Value{
			"GrpcCode":   structpb.NewNumberValue(float64(codes.NotFound)),
			"Message":    structpb.NewStringValue("bank service doesn't exist"),
			"ErrorType":  structpb.NewStringValue(errors.ErrorTypeNotFound), // "ErrorTypeNotFound"
			"IsExpected": structpb.NewBoolValue(true),
			"Code":       structpb.NewNumberValue(float64(Error_ErrBankServiceNotFound)), // 3107
		},
	})

	ErrBankServiceNotFound = errors.GoErr{
		ErrType:    errors.ErrorTypeNotFound, // "ErrorTypeNotFound"
		Status:     statusErrBankServiceNotFound,
		Code:       int(Error_ErrBankServiceNotFound), // 3107
		Message:    "bank service doesn't exist",
		GrpcCode:   codes.NotFound,
		IsExpected: true,
	}
)

var (
	statusErrCountryNotFound, _ = status.New(
		codes.NotFound,
		"country doesn't exist",
	).WithDetails(&structpb.Struct{
		Fields: map[string]*structpb.Value{
			"GrpcCode":   structpb.NewNumberValue(float64(codes.NotFound)),
			"Message":    structpb.NewStringValue("country doesn't exist"),
			"ErrorType":  structpb.NewStringValue(errors.ErrorTypeNotFound), // "ErrorTypeNotFound"
			"IsExpected": structpb.NewBoolValue(true),
			"Code":       structpb.NewNumberValue(float64(Error_ErrCountryNotFound)), // 3109
		},
	})

	ErrCountryNotFound = errors.GoErr{
		ErrType:    errors.ErrorTypeNotFound, // "ErrorTypeNotFound"
		Status:     statusErrCountryNotFound,
		Code:       int(Error_ErrCountryNotFound), // 3109
		Message:    "country doesn't exist",
		GrpcCode:   codes.NotFound,
		IsExpected: true,
	}
)

var (
	statusErrIpsNotFound, _ = status.New(
		codes.NotFound,
		"ips doesn't exist",
	).WithDetails(&structpb.Struct{
		Fields: map[string]*structpb.Value{
			"GrpcCode":   structpb.NewNumberValue(float64(codes.NotFound)),
			"Message":    structpb.NewStringValue("ips doesn't exist"),
			"ErrorType":  structpb.NewStringValue(errors.ErrorTypeNotFound), // "ErrorTypeNotFound"
			"IsExpected": structpb.NewBoolValue(true),
			"Code":       structpb.NewNumberValue(float64(Error_ErrIpsNotFound)), // 3110
		},
	})

	ErrIpsNotFound = errors.GoErr{
		ErrType:    errors.ErrorTypeNotFound, // "ErrorTypeNotFound"
		Status:     statusErrIpsNotFound,
		Code:       int(Error_ErrIpsNotFound), // 3110
		Message:    "ips doesn't exist",
		GrpcCode:   codes.NotFound,
		IsExpected: true,
	}
)

var (
	statusErrBalancerNotFound, _ = status.New(
		codes.NotFound,
		"balancer doesn't exist",
	).WithDetails(&structpb.Struct{
		Fields: map[string]*structpb.Value{
			"GrpcCode":   structpb.NewNumberValue(float64(codes.NotFound)),
			"Message":    structpb.NewStringValue("balancer doesn't exist"),
			"ErrorType":  structpb.NewStringValue(errors.ErrorTypeNotFound), // "ErrorTypeNotFound"
			"IsExpected": structpb.NewBoolValue(true),
			"Code":       structpb.NewNumberValue(float64(Error_ErrBalancerNotFound)), // 3111
		},
	})

	ErrBalancerNotFound = errors.GoErr{
		ErrType:    errors.ErrorTypeNotFound, // "ErrorTypeNotFound"
		Status:     statusErrBalancerNotFound,
		Code:       int(Error_ErrBalancerNotFound), // 3111
		Message:    "balancer doesn't exist",
		GrpcCode:   codes.NotFound,
		IsExpected: true,
	}
)

var (
	statusErrCountryCurrencyNotFound, _ = status.New(
		codes.NotFound,
		"country-currency doesn't exist",
	).WithDetails(&structpb.Struct{
		Fields: map[string]*structpb.Value{
			"GrpcCode":   structpb.NewNumberValue(float64(codes.NotFound)),
			"Message":    structpb.NewStringValue("country-currency doesn't exist"),
			"ErrorType":  structpb.NewStringValue(errors.ErrorTypeNotFound), // "ErrorTypeNotFound"
			"IsExpected": structpb.NewBoolValue(true),
			"Code":       structpb.NewNumberValue(float64(Error_ErrCountryCurrencyNotFound)), // 3112
		},
	})

	ErrCountryCurrencyNotFound = errors.GoErr{
		ErrType:    errors.ErrorTypeNotFound, // "ErrorTypeNotFound"
		Status:     statusErrCountryCurrencyNotFound,
		Code:       int(Error_ErrCountryCurrencyNotFound), // 3112
		Message:    "country-currency doesn't exist",
		GrpcCode:   codes.NotFound,
		IsExpected: true,
	}
)

var (
	statusErrIpsCountryNotFound, _ = status.New(
		codes.NotFound,
		"ips-country doesn't exist",
	).WithDetails(&structpb.Struct{
		Fields: map[string]*structpb.Value{
			"GrpcCode":   structpb.NewNumberValue(float64(codes.NotFound)),
			"Message":    structpb.NewStringValue("ips-country doesn't exist"),
			"ErrorType":  structpb.NewStringValue(errors.ErrorTypeNotFound), // "ErrorTypeNotFound"
			"IsExpected": structpb.NewBoolValue(true),
			"Code":       structpb.NewNumberValue(float64(Error_ErrIpsCountryNotFound)), // 3113
		},
	})

	ErrIpsCountryNotFound = errors.GoErr{
		ErrType:    errors.ErrorTypeNotFound, // "ErrorTypeNotFound"
		Status:     statusErrIpsCountryNotFound,
		Code:       int(Error_ErrIpsCountryNotFound), // 3113
		Message:    "ips-country doesn't exist",
		GrpcCode:   codes.NotFound,
		IsExpected: true,
	}
)

var (
	statusErrEmitentNotFound, _ = status.New(
		codes.NotFound,
		"emitent doesn't exist",
	).WithDetails(&structpb.Struct{
		Fields: map[string]*structpb.Value{
			"GrpcCode":   structpb.NewNumberValue(float64(codes.NotFound)),
			"Message":    structpb.NewStringValue("emitent doesn't exist"),
			"ErrorType":  structpb.NewStringValue(errors.ErrorTypeNotFound), // "ErrorTypeNotFound"
			"IsExpected": structpb.NewBoolValue(true),
			"Code":       structpb.NewNumberValue(float64(Error_ErrEmitentNotFound)), // 3114
		},
	})

	ErrEmitentNotFound = errors.GoErr{
		ErrType:    errors.ErrorTypeNotFound, // "ErrorTypeNotFound"
		Status:     statusErrEmitentNotFound,
		Code:       int(Error_ErrEmitentNotFound), // 3114
		Message:    "emitent doesn't exist",
		GrpcCode:   codes.NotFound,
		IsExpected: true,
	}
)

var (
	statusErrAcquirerPercentageNotFound, _ = status.New(
		codes.NotFound,
		"acquirer percentage doesn't exist",
	).WithDetails(&structpb.Struct{
		Fields: map[string]*structpb.Value{
			"GrpcCode":   structpb.NewNumberValue(float64(codes.NotFound)),
			"Message":    structpb.NewStringValue("acquirer percentage doesn't exist"),
			"ErrorType":  structpb.NewStringValue(errors.ErrorTypeNotFound), // "ErrorTypeNotFound"
			"IsExpected": structpb.NewBoolValue(true),
			"Code":       structpb.NewNumberValue(float64(Error_ErrAcquirerPercentageNotFound)), // 3115
		},
	})

	ErrAcquirerPercentageNotFound = errors.GoErr{
		ErrType:    errors.ErrorTypeNotFound, // "ErrorTypeNotFound"
		Status:     statusErrAcquirerPercentageNotFound,
		Code:       int(Error_ErrAcquirerPercentageNotFound), // 3115
		Message:    "acquirer percentage doesn't exist",
		GrpcCode:   codes.NotFound,
		IsExpected: true,
	}
)

var (
	statusErrCountryBankNotFound, _ = status.New(
		codes.NotFound,
		"country bank doesn't exist",
	).WithDetails(&structpb.Struct{
		Fields: map[string]*structpb.Value{
			"GrpcCode":   structpb.NewNumberValue(float64(codes.NotFound)),
			"Message":    structpb.NewStringValue("country bank doesn't exist"),
			"ErrorType":  structpb.NewStringValue(errors.ErrorTypeNotFound), // "ErrorTypeNotFound"
			"IsExpected": structpb.NewBoolValue(true),
			"Code":       structpb.NewNumberValue(float64(Error_ErrCountryBankNotFound)), // 3116
		},
	})

	ErrCountryBankNotFound = errors.GoErr{
		ErrType:    errors.ErrorTypeNotFound, // "ErrorTypeNotFound"
		Status:     statusErrCountryBankNotFound,
		Code:       int(Error_ErrCountryBankNotFound), // 3116
		Message:    "country bank doesn't exist",
		GrpcCode:   codes.NotFound,
		IsExpected: true,
	}
)

var (
	statusErrIpsDigitCodeNotFound, _ = status.New(
		codes.NotFound,
		"ips digit code doesn't exist",
	).WithDetails(&structpb.Struct{
		Fields: map[string]*structpb.Value{
			"GrpcCode":   structpb.NewNumberValue(float64(codes.NotFound)),
			"Message":    structpb.NewStringValue("ips digit code doesn't exist"),
			"ErrorType":  structpb.NewStringValue(errors.ErrorTypeNotFound), // "ErrorTypeNotFound"
			"IsExpected": structpb.NewBoolValue(true),
			"Code":       structpb.NewNumberValue(float64(Error_ErrIpsDigitCodeNotFound)), // 3117
		},
	})

	ErrIpsDigitCodeNotFound = errors.GoErr{
		ErrType:    errors.ErrorTypeNotFound, // "ErrorTypeNotFound"
		Status:     statusErrIpsDigitCodeNotFound,
		Code:       int(Error_ErrIpsDigitCodeNotFound), // 3117
		Message:    "ips digit code doesn't exist",
		GrpcCode:   codes.NotFound,
		IsExpected: true,
	}
)

var (
	statusErrTerminalProjectNotFound, _ = status.New(
		codes.NotFound,
		"terminal project doesn't exist",
	).WithDetails(&structpb.Struct{
		Fields: map[string]*structpb.Value{
			"GrpcCode":   structpb.NewNumberValue(float64(codes.NotFound)),
			"Message":    structpb.NewStringValue("terminal project doesn't exist"),
			"ErrorType":  structpb.NewStringValue(errors.ErrorTypeNotFound), // "ErrorTypeNotFound"
			"IsExpected": structpb.NewBoolValue(true),
			"Code":       structpb.NewNumberValue(float64(Error_ErrTerminalProjectNotFound)), // 3118
		},
	})

	ErrTerminalProjectNotFound = errors.GoErr{
		ErrType:    errors.ErrorTypeNotFound, // "ErrorTypeNotFound"
		Status:     statusErrTerminalProjectNotFound,
		Code:       int(Error_ErrTerminalProjectNotFound), // 3118
		Message:    "terminal project doesn't exist",
		GrpcCode:   codes.NotFound,
		IsExpected: true,
	}
)

var (
	statusErrVisaAliasNotFound, _ = status.New(
		codes.NotFound,
		"phone number doesn't exists",
	).WithDetails(&structpb.Struct{
		Fields: map[string]*structpb.Value{
			"GrpcCode":   structpb.NewNumberValue(float64(codes.NotFound)),
			"Message":    structpb.NewStringValue("phone number doesn't exists"),
			"ErrorType":  structpb.NewStringValue(errors.ErrorTypeNotFound), // "ErrorTypeNotFound"
			"IsExpected": structpb.NewBoolValue(true),
			"Code":       structpb.NewNumberValue(float64(Error_ErrVisaAliasNotFound)), // 3119
		},
	})

	ErrVisaAliasNotFound = errors.GoErr{
		ErrType:    errors.ErrorTypeNotFound, // "ErrorTypeNotFound"
		Status:     statusErrVisaAliasNotFound,
		Code:       int(Error_ErrVisaAliasNotFound), // 3119
		Message:    "phone number doesn't exists",
		GrpcCode:   codes.NotFound,
		IsExpected: true,
	}
)

var (
	statusErrSanctionInfoNotFound, _ = status.New(
		codes.NotFound,
		"sanction info not found",
	).WithDetails(&structpb.Struct{
		Fields: map[string]*structpb.Value{
			"GrpcCode":   structpb.NewNumberValue(float64(codes.NotFound)),
			"Message":    structpb.NewStringValue("sanction info not found"),
			"ErrorType":  structpb.NewStringValue(errors.ErrorTypeNotFound), // "ErrorTypeNotFound"
			"IsExpected": structpb.NewBoolValue(true),
			"Code":       structpb.NewNumberValue(float64(Error_ErrSanctionInfoNotFound)), // 9600
		},
	})

	ErrSanctionInfoNotFound = errors.GoErr{
		ErrType:    errors.ErrorTypeNotFound, // "ErrorTypeNotFound"
		Status:     statusErrSanctionInfoNotFound,
		Code:       int(Error_ErrSanctionInfoNotFound), // 9600
		Message:    "sanction info not found",
		GrpcCode:   codes.NotFound,
		IsExpected: true,
	}
)

var (
	statusErrPaymentTypeNotFound, _ = status.New(
		codes.NotFound,
		"payment type doesn't exist",
	).WithDetails(&structpb.Struct{
		Fields: map[string]*structpb.Value{
			"GrpcCode":   structpb.NewNumberValue(float64(codes.NotFound)),
			"Message":    structpb.NewStringValue("payment type doesn't exist"),
			"ErrorType":  structpb.NewStringValue(errors.ErrorTypeNotFound), // "ErrorTypeNotFound"
			"IsExpected": structpb.NewBoolValue(true),
			"Code":       structpb.NewNumberValue(float64(Error_ErrPaymentTypeNotFound)), // 8004
		},
	})

	ErrPaymentTypeNotFound = errors.GoErr{
		ErrType:    errors.ErrorTypeNotFound, // "ErrorTypeNotFound"
		Status:     statusErrPaymentTypeNotFound,
		Code:       int(Error_ErrPaymentTypeNotFound), // 8004
		Message:    "payment type doesn't exist",
		GrpcCode:   codes.NotFound,
		IsExpected: true,
	}
)

var (
	statusErrAcquirerCommissionNotFound, _ = status.New(
		codes.NotFound,
		"acquirer commission not found",
	).WithDetails(&structpb.Struct{
		Fields: map[string]*structpb.Value{
			"GrpcCode":   structpb.NewNumberValue(float64(codes.NotFound)),
			"Message":    structpb.NewStringValue("acquirer commission not found"),
			"ErrorType":  structpb.NewStringValue(errors.ErrorTypeNotFound), // "ErrorTypeNotFound"
			"IsExpected": structpb.NewBoolValue(true),
			"Code":       structpb.NewNumberValue(float64(Error_ErrAcquirerCommissionNotFound)), // 2011
		},
	})

	ErrAcquirerCommissionNotFound = errors.GoErr{
		ErrType:    errors.ErrorTypeNotFound, // "ErrorTypeNotFound"
		Status:     statusErrAcquirerCommissionNotFound,
		Code:       int(Error_ErrAcquirerCommissionNotFound), // 2011
		Message:    "acquirer commission not found",
		GrpcCode:   codes.NotFound,
		IsExpected: true,
	}
)

var (
	statusErrAcquirerOptionNotFound, _ = status.New(
		codes.NotFound,
		"acquirer option not found",
	).WithDetails(&structpb.Struct{
		Fields: map[string]*structpb.Value{
			"GrpcCode":   structpb.NewNumberValue(float64(codes.NotFound)),
			"Message":    structpb.NewStringValue("acquirer option not found"),
			"ErrorType":  structpb.NewStringValue(errors.ErrorTypeNotFound), // "ErrorTypeNotFound"
			"IsExpected": structpb.NewBoolValue(true),
			"Code":       structpb.NewNumberValue(float64(Error_ErrAcquirerOptionNotFound)), // 2014
		},
	})

	ErrAcquirerOptionNotFound = errors.GoErr{
		ErrType:    errors.ErrorTypeNotFound, // "ErrorTypeNotFound"
		Status:     statusErrAcquirerOptionNotFound,
		Code:       int(Error_ErrAcquirerOptionNotFound), // 2014
		Message:    "acquirer option not found",
		GrpcCode:   codes.NotFound,
		IsExpected: true,
	}
)

var (
	statusErrProjectCommissionNotFound, _ = status.New(
		codes.NotFound,
		"project commission not found",
	).WithDetails(&structpb.Struct{
		Fields: map[string]*structpb.Value{
			"GrpcCode":   structpb.NewNumberValue(float64(codes.NotFound)),
			"Message":    structpb.NewStringValue("project commission not found"),
			"ErrorType":  structpb.NewStringValue(errors.ErrorTypeNotFound), // "ErrorTypeNotFound"
			"IsExpected": structpb.NewBoolValue(true),
			"Code":       structpb.NewNumberValue(float64(Error_ErrProjectCommissionNotFound)), // 2012
		},
	})

	ErrProjectCommissionNotFound = errors.GoErr{
		ErrType:    errors.ErrorTypeNotFound, // "ErrorTypeNotFound"
		Status:     statusErrProjectCommissionNotFound,
		Code:       int(Error_ErrProjectCommissionNotFound), // 2012
		Message:    "project commission not found",
		GrpcCode:   codes.NotFound,
		IsExpected: true,
	}
)

var (
	statusErrBrokenTransaction, _ = status.New(
		codes.DataLoss,
		"transaction missing required fields",
	).WithDetails(&structpb.Struct{
		Fields: map[string]*structpb.Value{
			"GrpcCode":   structpb.NewNumberValue(float64(codes.DataLoss)),
			"Message":    structpb.NewStringValue("transaction missing required fields"),
			"ErrorType":  structpb.NewStringValue(errors.ErrorTypeDbViolation), // "ErrorTypeDbViolation"
			"IsExpected": structpb.NewBoolValue(true),
			"Code":       structpb.NewNumberValue(float64(Error_ErrBrokenTransaction)), // 9700
		},
	})

	ErrBrokenTransaction = errors.GoErr{
		ErrType:    errors.ErrorTypeDbViolation, // "ErrorTypeDbViolation"
		Status:     statusErrBrokenTransaction,
		Code:       int(Error_ErrBrokenTransaction), // 9700
		Message:    "transaction missing required fields",
		GrpcCode:   codes.DataLoss,
		IsExpected: true,
	}
)

var (
	statusErrUpdatingCurrentBalance, _ = status.New(
		codes.Unknown,
		"couldn't update current balance",
	).WithDetails(&structpb.Struct{
		Fields: map[string]*structpb.Value{
			"GrpcCode":   structpb.NewNumberValue(float64(codes.Unknown)),
			"Message":    structpb.NewStringValue("couldn't update current balance"),
			"ErrorType":  structpb.NewStringValue(errors.ErrorTypeUnexpectedError), // "ErrorTypeUnexpectedError"
			"IsExpected": structpb.NewBoolValue(true),
			"Code":       structpb.NewNumberValue(float64(Error_ErrUpdatingCurrentBalance)), // 1443
		},
	})

	ErrUpdatingCurrentBalance = errors.GoErr{
		ErrType:    errors.ErrorTypeUnexpectedError, // "ErrorTypeUnexpectedError"
		Status:     statusErrUpdatingCurrentBalance,
		Code:       int(Error_ErrUpdatingCurrentBalance), // 1443
		Message:    "couldn't update current balance",
		GrpcCode:   codes.Unknown,
		IsExpected: true,
	}
)

var (
	statusErrFinalizeTransactionStatus, _ = status.New(
		codes.InvalidArgument,
		"failed to finalize transaction status",
	).WithDetails(&structpb.Struct{
		Fields: map[string]*structpb.Value{
			"GrpcCode":   structpb.NewNumberValue(float64(codes.InvalidArgument)),
			"Message":    structpb.NewStringValue("failed to finalize transaction status"),
			"ErrorType":  structpb.NewStringValue(errors.ErrorTypeBadRequest), // "ErrorTypeBadRequest"
			"IsExpected": structpb.NewBoolValue(true),
			"Code":       structpb.NewNumberValue(float64(Error_ErrFinalizeTransactionStatus)), // 9709
		},
	})

	ErrFinalizeTransactionStatus = errors.GoErr{
		ErrType:    errors.ErrorTypeBadRequest, // "ErrorTypeBadRequest"
		Status:     statusErrFinalizeTransactionStatus,
		Code:       int(Error_ErrFinalizeTransactionStatus), // 9709
		Message:    "failed to finalize transaction status",
		GrpcCode:   codes.InvalidArgument,
		IsExpected: true,
	}
)

var (
	statusErrBankStatusNotChanged, _ = status.New(
		codes.InvalidArgument,
		"bank status has not been changed",
	).WithDetails(&structpb.Struct{
		Fields: map[string]*structpb.Value{
			"GrpcCode":   structpb.NewNumberValue(float64(codes.InvalidArgument)),
			"Message":    structpb.NewStringValue("bank status has not been changed"),
			"ErrorType":  structpb.NewStringValue(errors.ErrorTypeBadRequest), // "ErrorTypeBadRequest"
			"IsExpected": structpb.NewBoolValue(true),
			"Code":       structpb.NewNumberValue(float64(Error_ErrBankStatusNotChanged)), // 9716
		},
	})

	ErrBankStatusNotChanged = errors.GoErr{
		ErrType:    errors.ErrorTypeBadRequest, // "ErrorTypeBadRequest"
		Status:     statusErrBankStatusNotChanged,
		Code:       int(Error_ErrBankStatusNotChanged), // 9716
		Message:    "bank status has not been changed",
		GrpcCode:   codes.InvalidArgument,
		IsExpected: true,
	}
)

var (
	statusErrAggregatedTransactionTypeNotFound, _ = status.New(
		codes.NotFound,
		"aggregated transaction type not found",
	).WithDetails(&structpb.Struct{
		Fields: map[string]*structpb.Value{
			"GrpcCode":   structpb.NewNumberValue(float64(codes.NotFound)),
			"Message":    structpb.NewStringValue("aggregated transaction type not found"),
			"ErrorType":  structpb.NewStringValue(errors.ErrorTypeNotFound), // "ErrorTypeNotFound"
			"IsExpected": structpb.NewBoolValue(true),
			"Code":       structpb.NewNumberValue(float64(Error_ErrAggregatedTransactionTypeNotFound)), // 2016
		},
	})

	ErrAggregatedTransactionTypeNotFound = errors.GoErr{
		ErrType:    errors.ErrorTypeNotFound, // "ErrorTypeNotFound"
		Status:     statusErrAggregatedTransactionTypeNotFound,
		Code:       int(Error_ErrAggregatedTransactionTypeNotFound), // 2016
		Message:    "aggregated transaction type not found",
		GrpcCode:   codes.NotFound,
		IsExpected: true,
	}
)

var (
	statusErrTransactionsAreNotFinalized, _ = status.New(
		codes.InvalidArgument,
		"transaction(s) is(are) not finalized",
	).WithDetails(&structpb.Struct{
		Fields: map[string]*structpb.Value{
			"GrpcCode":   structpb.NewNumberValue(float64(codes.InvalidArgument)),
			"Message":    structpb.NewStringValue("transaction(s) is(are) not finalized"),
			"ErrorType":  structpb.NewStringValue(errors.ErrorTypeBadRequest), // "ErrorTypeBadRequest"
			"IsExpected": structpb.NewBoolValue(true),
			"Code":       structpb.NewNumberValue(float64(Error_ErrTransactionsAreNotFinalized)), // 5108
		},
	})

	ErrTransactionsAreNotFinalized = errors.GoErr{
		ErrType:    errors.ErrorTypeBadRequest, // "ErrorTypeBadRequest"
		Status:     statusErrTransactionsAreNotFinalized,
		Code:       int(Error_ErrTransactionsAreNotFinalized), // 5108
		Message:    "transaction(s) is(are) not finalized",
		GrpcCode:   codes.InvalidArgument,
		IsExpected: true,
	}
)

var (
	statusErrTransactionsAreNotTheSame, _ = status.New(
		codes.InvalidArgument,
		"transactions are not the same",
	).WithDetails(&structpb.Struct{
		Fields: map[string]*structpb.Value{
			"GrpcCode":   structpb.NewNumberValue(float64(codes.InvalidArgument)),
			"Message":    structpb.NewStringValue("transactions are not the same"),
			"ErrorType":  structpb.NewStringValue(errors.ErrorTypeBadRequest), // "ErrorTypeBadRequest"
			"IsExpected": structpb.NewBoolValue(true),
			"Code":       structpb.NewNumberValue(float64(Error_ErrTransactionsAreNotTheSame)), // 5109
		},
	})

	ErrTransactionsAreNotTheSame = errors.GoErr{
		ErrType:    errors.ErrorTypeBadRequest, // "ErrorTypeBadRequest"
		Status:     statusErrTransactionsAreNotTheSame,
		Code:       int(Error_ErrTransactionsAreNotTheSame), // 5109
		Message:    "transactions are not the same",
		GrpcCode:   codes.InvalidArgument,
		IsExpected: true,
	}
)

var (
	statusErrProjectNotFound, _ = status.New(
		codes.NotFound,
		"project doesn't exist",
	).WithDetails(&structpb.Struct{
		Fields: map[string]*structpb.Value{
			"GrpcCode":   structpb.NewNumberValue(float64(codes.NotFound)),
			"Message":    structpb.NewStringValue("project doesn't exist"),
			"ErrorType":  structpb.NewStringValue(errors.ErrorTypeNotFound), // "ErrorTypeNotFound"
			"IsExpected": structpb.NewBoolValue(true),
			"Code":       structpb.NewNumberValue(float64(Error_ErrProjectNotFound)), // 8008
		},
	})

	ErrProjectNotFound = errors.GoErr{
		ErrType:    errors.ErrorTypeNotFound, // "ErrorTypeNotFound"
		Status:     statusErrProjectNotFound,
		Code:       int(Error_ErrProjectNotFound), // 8008
		Message:    "project doesn't exist",
		GrpcCode:   codes.NotFound,
		IsExpected: true,
	}
)

var (
	statusErrProjectTerminalNotFound, _ = status.New(
		codes.NotFound,
		"project-terminal doesn't exist",
	).WithDetails(&structpb.Struct{
		Fields: map[string]*structpb.Value{
			"GrpcCode":   structpb.NewNumberValue(float64(codes.NotFound)),
			"Message":    structpb.NewStringValue("project-terminal doesn't exist"),
			"ErrorType":  structpb.NewStringValue(errors.ErrorTypeNotFound), // "ErrorTypeNotFound"
			"IsExpected": structpb.NewBoolValue(true),
			"Code":       structpb.NewNumberValue(float64(Error_ErrProjectTerminalNotFound)), // 8018
		},
	})

	ErrProjectTerminalNotFound = errors.GoErr{
		ErrType:    errors.ErrorTypeNotFound, // "ErrorTypeNotFound"
		Status:     statusErrProjectTerminalNotFound,
		Code:       int(Error_ErrProjectTerminalNotFound), // 8018
		Message:    "project-terminal doesn't exist",
		GrpcCode:   codes.NotFound,
		IsExpected: true,
	}
)

var (
	statusErrProjectTerminalAlreadyExists, _ = status.New(
		codes.InvalidArgument,
		"project-terminal already exists",
	).WithDetails(&structpb.Struct{
		Fields: map[string]*structpb.Value{
			"GrpcCode":   structpb.NewNumberValue(float64(codes.InvalidArgument)),
			"Message":    structpb.NewStringValue("project-terminal already exists"),
			"ErrorType":  structpb.NewStringValue(errors.ErrorTypeBadRequest), // "ErrorTypeBadRequest"
			"IsExpected": structpb.NewBoolValue(true),
			"Code":       structpb.NewNumberValue(float64(Error_ErrProjectTerminalAlreadyExists)), // 8020
		},
	})

	ErrProjectTerminalAlreadyExists = errors.GoErr{
		ErrType:    errors.ErrorTypeBadRequest, // "ErrorTypeBadRequest"
		Status:     statusErrProjectTerminalAlreadyExists,
		Code:       int(Error_ErrProjectTerminalAlreadyExists), // 8020
		Message:    "project-terminal already exists",
		GrpcCode:   codes.InvalidArgument,
		IsExpected: true,
	}
)

var (
	statusErrProjectReportScheduleNotFound, _ = status.New(
		codes.NotFound,
		"project report schedule doesn't exist",
	).WithDetails(&structpb.Struct{
		Fields: map[string]*structpb.Value{
			"GrpcCode":   structpb.NewNumberValue(float64(codes.NotFound)),
			"Message":    structpb.NewStringValue("project report schedule doesn't exist"),
			"ErrorType":  structpb.NewStringValue(errors.ErrorTypeNotFound), // "ErrorTypeNotFound"
			"IsExpected": structpb.NewBoolValue(true),
			"Code":       structpb.NewNumberValue(float64(Error_ErrProjectReportScheduleNotFound)), // 1508
		},
	})

	ErrProjectReportScheduleNotFound = errors.GoErr{
		ErrType:    errors.ErrorTypeNotFound, // "ErrorTypeNotFound"
		Status:     statusErrProjectReportScheduleNotFound,
		Code:       int(Error_ErrProjectReportScheduleNotFound), // 1508
		Message:    "project report schedule doesn't exist",
		GrpcCode:   codes.NotFound,
		IsExpected: true,
	}
)

var (
	statusErrProjectOptionNotFound, _ = status.New(
		codes.NotFound,
		"project option not found",
	).WithDetails(&structpb.Struct{
		Fields: map[string]*structpb.Value{
			"GrpcCode":   structpb.NewNumberValue(float64(codes.NotFound)),
			"Message":    structpb.NewStringValue("project option not found"),
			"ErrorType":  structpb.NewStringValue(errors.ErrorTypeNotFound), // "ErrorTypeNotFound"
			"IsExpected": structpb.NewBoolValue(true),
			"Code":       structpb.NewNumberValue(float64(Error_ErrProjectOptionNotFound)), // 2015
		},
	})

	ErrProjectOptionNotFound = errors.GoErr{
		ErrType:    errors.ErrorTypeNotFound, // "ErrorTypeNotFound"
		Status:     statusErrProjectOptionNotFound,
		Code:       int(Error_ErrProjectOptionNotFound), // 2015
		Message:    "project option not found",
		GrpcCode:   codes.NotFound,
		IsExpected: true,
	}
)

var (
	statusErrProjectSupplierNotFound, _ = status.New(
		codes.NotFound,
		"project-supplier doesn't exist",
	).WithDetails(&structpb.Struct{
		Fields: map[string]*structpb.Value{
			"GrpcCode":   structpb.NewNumberValue(float64(codes.NotFound)),
			"Message":    structpb.NewStringValue("project-supplier doesn't exist"),
			"ErrorType":  structpb.NewStringValue(errors.ErrorTypeNotFound), // "ErrorTypeNotFound"
			"IsExpected": structpb.NewBoolValue(true),
			"Code":       structpb.NewNumberValue(float64(Error_ErrProjectSupplierNotFound)), // 8030
		},
	})

	ErrProjectSupplierNotFound = errors.GoErr{
		ErrType:    errors.ErrorTypeNotFound, // "ErrorTypeNotFound"
		Status:     statusErrProjectSupplierNotFound,
		Code:       int(Error_ErrProjectSupplierNotFound), // 8030
		Message:    "project-supplier doesn't exist",
		GrpcCode:   codes.NotFound,
		IsExpected: true,
	}
)

var (
	statusErrMerchantNotFound, _ = status.New(
		codes.NotFound,
		"merchant doesn't exist",
	).WithDetails(&structpb.Struct{
		Fields: map[string]*structpb.Value{
			"GrpcCode":   structpb.NewNumberValue(float64(codes.NotFound)),
			"Message":    structpb.NewStringValue("merchant doesn't exist"),
			"ErrorType":  structpb.NewStringValue(errors.ErrorTypeNotFound), // "ErrorTypeNotFound"
			"IsExpected": structpb.NewBoolValue(true),
			"Code":       structpb.NewNumberValue(float64(Error_ErrMerchantNotFound)), // 8005
		},
	})

	ErrMerchantNotFound = errors.GoErr{
		ErrType:    errors.ErrorTypeNotFound, // "ErrorTypeNotFound"
		Status:     statusErrMerchantNotFound,
		Code:       int(Error_ErrMerchantNotFound), // 8005
		Message:    "merchant doesn't exist",
		GrpcCode:   codes.NotFound,
		IsExpected: true,
	}
)

var (
	statusErrMerchantOwnerTypeRelationNotFound, _ = status.New(
		codes.NotFound,
		"merchant owner type relation doesn't exist",
	).WithDetails(&structpb.Struct{
		Fields: map[string]*structpb.Value{
			"GrpcCode":   structpb.NewNumberValue(float64(codes.NotFound)),
			"Message":    structpb.NewStringValue("merchant owner type relation doesn't exist"),
			"ErrorType":  structpb.NewStringValue(errors.ErrorTypeNotFound), // "ErrorTypeNotFound"
			"IsExpected": structpb.NewBoolValue(true),
			"Code":       structpb.NewNumberValue(float64(Error_ErrMerchantOwnerTypeRelationNotFound)), // 8206
		},
	})

	ErrMerchantOwnerTypeRelationNotFound = errors.GoErr{
		ErrType:    errors.ErrorTypeNotFound, // "ErrorTypeNotFound"
		Status:     statusErrMerchantOwnerTypeRelationNotFound,
		Code:       int(Error_ErrMerchantOwnerTypeRelationNotFound), // 8206
		Message:    "merchant owner type relation doesn't exist",
		GrpcCode:   codes.NotFound,
		IsExpected: true,
	}
)

var (
	statusErrInvalidMerchantOwnerTypeRelation, _ = status.New(
		codes.InvalidArgument,
		"invalid merchant owner type relation",
	).WithDetails(&structpb.Struct{
		Fields: map[string]*structpb.Value{
			"GrpcCode":   structpb.NewNumberValue(float64(codes.InvalidArgument)),
			"Message":    structpb.NewStringValue("invalid merchant owner type relation"),
			"ErrorType":  structpb.NewStringValue(errors.ErrorTypeBadRequest), // "ErrorTypeBadRequest"
			"IsExpected": structpb.NewBoolValue(true),
			"Code":       structpb.NewNumberValue(float64(Error_ErrInvalidMerchantOwnerTypeRelation)), // 8207
		},
	})

	ErrInvalidMerchantOwnerTypeRelation = errors.GoErr{
		ErrType:    errors.ErrorTypeBadRequest, // "ErrorTypeBadRequest"
		Status:     statusErrInvalidMerchantOwnerTypeRelation,
		Code:       int(Error_ErrInvalidMerchantOwnerTypeRelation), // 8207
		Message:    "invalid merchant owner type relation",
		GrpcCode:   codes.InvalidArgument,
		IsExpected: true,
	}
)

var (
	statusErrMerchantInfoNotFound, _ = status.New(
		codes.NotFound,
		"merchant info doesn't exist",
	).WithDetails(&structpb.Struct{
		Fields: map[string]*structpb.Value{
			"GrpcCode":   structpb.NewNumberValue(float64(codes.NotFound)),
			"Message":    structpb.NewStringValue("merchant info doesn't exist"),
			"ErrorType":  structpb.NewStringValue(errors.ErrorTypeNotFound), // "ErrorTypeNotFound"
			"IsExpected": structpb.NewBoolValue(true),
			"Code":       structpb.NewNumberValue(float64(Error_ErrMerchantInfoNotFound)), // 8016
		},
	})

	ErrMerchantInfoNotFound = errors.GoErr{
		ErrType:    errors.ErrorTypeNotFound, // "ErrorTypeNotFound"
		Status:     statusErrMerchantInfoNotFound,
		Code:       int(Error_ErrMerchantInfoNotFound), // 8016
		Message:    "merchant info doesn't exist",
		GrpcCode:   codes.NotFound,
		IsExpected: true,
	}
)

var (
	statusErrMerchantDocNotFound, _ = status.New(
		codes.NotFound,
		"merchant document doesn't exist",
	).WithDetails(&structpb.Struct{
		Fields: map[string]*structpb.Value{
			"GrpcCode":   structpb.NewNumberValue(float64(codes.NotFound)),
			"Message":    structpb.NewStringValue("merchant document doesn't exist"),
			"ErrorType":  structpb.NewStringValue(errors.ErrorTypeNotFound), // "ErrorTypeNotFound"
			"IsExpected": structpb.NewBoolValue(true),
			"Code":       structpb.NewNumberValue(float64(Error_ErrMerchantDocNotFound)), // 8025
		},
	})

	ErrMerchantDocNotFound = errors.GoErr{
		ErrType:    errors.ErrorTypeNotFound, // "ErrorTypeNotFound"
		Status:     statusErrMerchantDocNotFound,
		Code:       int(Error_ErrMerchantDocNotFound), // 8025
		Message:    "merchant document doesn't exist",
		GrpcCode:   codes.NotFound,
		IsExpected: true,
	}
)

var (
	statusErrMerchantAlreadyExists, _ = status.New(
		codes.InvalidArgument,
		"merchant already exists",
	).WithDetails(&structpb.Struct{
		Fields: map[string]*structpb.Value{
			"GrpcCode":   structpb.NewNumberValue(float64(codes.InvalidArgument)),
			"Message":    structpb.NewStringValue("merchant already exists"),
			"ErrorType":  structpb.NewStringValue(errors.ErrorTypeBadRequest), // "ErrorTypeBadRequest"
			"IsExpected": structpb.NewBoolValue(true),
			"Code":       structpb.NewNumberValue(float64(Error_ErrMerchantAlreadyExists)), // 8029
		},
	})

	ErrMerchantAlreadyExists = errors.GoErr{
		ErrType:    errors.ErrorTypeBadRequest, // "ErrorTypeBadRequest"
		Status:     statusErrMerchantAlreadyExists,
		Code:       int(Error_ErrMerchantAlreadyExists), // 8029
		Message:    "merchant already exists",
		GrpcCode:   codes.InvalidArgument,
		IsExpected: true,
	}
)

var (
	statusErrMerchantDuplicatedKey, _ = status.New(
		codes.Unknown,
		"merchant must be unique",
	).WithDetails(&structpb.Struct{
		Fields: map[string]*structpb.Value{
			"GrpcCode":   structpb.NewNumberValue(float64(codes.Unknown)),
			"Message":    structpb.NewStringValue("merchant must be unique"),
			"ErrorType":  structpb.NewStringValue(errors.ErrorTypeUnexpectedError), // "ErrorTypeUnexpectedError"
			"IsExpected": structpb.NewBoolValue(true),
			"Code":       structpb.NewNumberValue(float64(Error_ErrMerchantDuplicatedKey)), // 8304
		},
	})

	ErrMerchantDuplicatedKey = errors.GoErr{
		ErrType:    errors.ErrorTypeUnexpectedError, // "ErrorTypeUnexpectedError"
		Status:     statusErrMerchantDuplicatedKey,
		Code:       int(Error_ErrMerchantDuplicatedKey), // 8304
		Message:    "merchant must be unique",
		GrpcCode:   codes.Unknown,
		IsExpected: true,
	}
)

var (
	statusErrBrokerageNotFound, _ = status.New(
		codes.NotFound,
		"brokerage doesn't exist",
	).WithDetails(&structpb.Struct{
		Fields: map[string]*structpb.Value{
			"GrpcCode":   structpb.NewNumberValue(float64(codes.NotFound)),
			"Message":    structpb.NewStringValue("brokerage doesn't exist"),
			"ErrorType":  structpb.NewStringValue(errors.ErrorTypeNotFound), // "ErrorTypeNotFound"
			"IsExpected": structpb.NewBoolValue(true),
			"Code":       structpb.NewNumberValue(float64(Error_ErrBrokerageNotFound)), // 2003
		},
	})

	ErrBrokerageNotFound = errors.GoErr{
		ErrType:    errors.ErrorTypeNotFound, // "ErrorTypeNotFound"
		Status:     statusErrBrokerageNotFound,
		Code:       int(Error_ErrBrokerageNotFound), // 2003
		Message:    "brokerage doesn't exist",
		GrpcCode:   codes.NotFound,
		IsExpected: true,
	}
)

var (
	statusErrBrokerageProjectNotFound, _ = status.New(
		codes.NotFound,
		"brokerage project doesn't exist",
	).WithDetails(&structpb.Struct{
		Fields: map[string]*structpb.Value{
			"GrpcCode":   structpb.NewNumberValue(float64(codes.NotFound)),
			"Message":    structpb.NewStringValue("brokerage project doesn't exist"),
			"ErrorType":  structpb.NewStringValue(errors.ErrorTypeNotFound), // "ErrorTypeNotFound"
			"IsExpected": structpb.NewBoolValue(true),
			"Code":       structpb.NewNumberValue(float64(Error_ErrBrokerageProjectNotFound)), // 2006
		},
	})

	ErrBrokerageProjectNotFound = errors.GoErr{
		ErrType:    errors.ErrorTypeNotFound, // "ErrorTypeNotFound"
		Status:     statusErrBrokerageProjectNotFound,
		Code:       int(Error_ErrBrokerageProjectNotFound), // 2006
		Message:    "brokerage project doesn't exist",
		GrpcCode:   codes.NotFound,
		IsExpected: true,
	}
)

var (
	statusErrChannelNotFound, _ = status.New(
		codes.NotFound,
		"channel doesn't exist",
	).WithDetails(&structpb.Struct{
		Fields: map[string]*structpb.Value{
			"GrpcCode":   structpb.NewNumberValue(float64(codes.NotFound)),
			"Message":    structpb.NewStringValue("channel doesn't exist"),
			"ErrorType":  structpb.NewStringValue(errors.ErrorTypeNotFound), // "ErrorTypeNotFound"
			"IsExpected": structpb.NewBoolValue(true),
			"Code":       structpb.NewNumberValue(float64(Error_ErrChannelNotFound)), // 8001
		},
	})

	ErrChannelNotFound = errors.GoErr{
		ErrType:    errors.ErrorTypeNotFound, // "ErrorTypeNotFound"
		Status:     statusErrChannelNotFound,
		Code:       int(Error_ErrChannelNotFound), // 8001
		Message:    "channel doesn't exist",
		GrpcCode:   codes.NotFound,
		IsExpected: true,
	}
)

var (
	statusErrStatusNotFound, _ = status.New(
		codes.NotFound,
		"status doesn't exist",
	).WithDetails(&structpb.Struct{
		Fields: map[string]*structpb.Value{
			"GrpcCode":   structpb.NewNumberValue(float64(codes.NotFound)),
			"Message":    structpb.NewStringValue("status doesn't exist"),
			"ErrorType":  structpb.NewStringValue(errors.ErrorTypeNotFound), // "ErrorTypeNotFound"
			"IsExpected": structpb.NewBoolValue(true),
			"Code":       structpb.NewNumberValue(float64(Error_ErrStatusNotFound)), // 8003
		},
	})

	ErrStatusNotFound = errors.GoErr{
		ErrType:    errors.ErrorTypeNotFound, // "ErrorTypeNotFound"
		Status:     statusErrStatusNotFound,
		Code:       int(Error_ErrStatusNotFound), // 8003
		Message:    "status doesn't exist",
		GrpcCode:   codes.NotFound,
		IsExpected: true,
	}
)

var (
	statusErrFilenameNotFound, _ = status.New(
		codes.NotFound,
		"filename doesn't exist",
	).WithDetails(&structpb.Struct{
		Fields: map[string]*structpb.Value{
			"GrpcCode":   structpb.NewNumberValue(float64(codes.NotFound)),
			"Message":    structpb.NewStringValue("filename doesn't exist"),
			"ErrorType":  structpb.NewStringValue(errors.ErrorTypeNotFound), // "ErrorTypeNotFound"
			"IsExpected": structpb.NewBoolValue(true),
			"Code":       structpb.NewNumberValue(float64(Error_ErrFilenameNotFound)), // 8009
		},
	})

	ErrFilenameNotFound = errors.GoErr{
		ErrType:    errors.ErrorTypeNotFound, // "ErrorTypeNotFound"
		Status:     statusErrFilenameNotFound,
		Code:       int(Error_ErrFilenameNotFound), // 8009
		Message:    "filename doesn't exist",
		GrpcCode:   codes.NotFound,
		IsExpected: true,
	}
)

var (
	statusErrMsgIsEmpty, _ = status.New(
		codes.NotFound,
		"message can't be empty",
	).WithDetails(&structpb.Struct{
		Fields: map[string]*structpb.Value{
			"GrpcCode":   structpb.NewNumberValue(float64(codes.NotFound)),
			"Message":    structpb.NewStringValue("message can't be empty"),
			"ErrorType":  structpb.NewStringValue(errors.ErrorTypeNotFound), // "ErrorTypeNotFound"
			"IsExpected": structpb.NewBoolValue(true),
			"Code":       structpb.NewNumberValue(float64(Error_ErrMsgIsEmpty)), // 8010
		},
	})

	ErrMsgIsEmpty = errors.GoErr{
		ErrType:    errors.ErrorTypeNotFound, // "ErrorTypeNotFound"
		Status:     statusErrMsgIsEmpty,
		Code:       int(Error_ErrMsgIsEmpty), // 8010
		Message:    "message can't be empty",
		GrpcCode:   codes.NotFound,
		IsExpected: true,
	}
)

var (
	statusErrTerminalNotFound, _ = status.New(
		codes.NotFound,
		"terminal doesn't exist",
	).WithDetails(&structpb.Struct{
		Fields: map[string]*structpb.Value{
			"GrpcCode":   structpb.NewNumberValue(float64(codes.NotFound)),
			"Message":    structpb.NewStringValue("terminal doesn't exist"),
			"ErrorType":  structpb.NewStringValue(errors.ErrorTypeNotFound), // "ErrorTypeNotFound"
			"IsExpected": structpb.NewBoolValue(true),
			"Code":       structpb.NewNumberValue(float64(Error_ErrTerminalNotFound)), // 8011
		},
	})

	ErrTerminalNotFound = errors.GoErr{
		ErrType:    errors.ErrorTypeNotFound, // "ErrorTypeNotFound"
		Status:     statusErrTerminalNotFound,
		Code:       int(Error_ErrTerminalNotFound), // 8011
		Message:    "terminal doesn't exist",
		GrpcCode:   codes.NotFound,
		IsExpected: true,
	}
)

var (
	statusErrTerminalHasAnAccount, _ = status.New(
		codes.NotFound,
		"this terminal already has such an account",
	).WithDetails(&structpb.Struct{
		Fields: map[string]*structpb.Value{
			"GrpcCode":   structpb.NewNumberValue(float64(codes.NotFound)),
			"Message":    structpb.NewStringValue("this terminal already has such an account"),
			"ErrorType":  structpb.NewStringValue(errors.ErrorTypeNotFound), // "ErrorTypeNotFound"
			"IsExpected": structpb.NewBoolValue(true),
			"Code":       structpb.NewNumberValue(float64(Error_ErrTerminalHasAnAccount)), // 8012
		},
	})

	ErrTerminalHasAnAccount = errors.GoErr{
		ErrType:    errors.ErrorTypeNotFound, // "ErrorTypeNotFound"
		Status:     statusErrTerminalHasAnAccount,
		Code:       int(Error_ErrTerminalHasAnAccount), // 8012
		Message:    "this terminal already has such an account",
		GrpcCode:   codes.NotFound,
		IsExpected: true,
	}
)

var (
	statusErrSupplierNotFound, _ = status.New(
		codes.NotFound,
		"supplier doesn't exist",
	).WithDetails(&structpb.Struct{
		Fields: map[string]*structpb.Value{
			"GrpcCode":   structpb.NewNumberValue(float64(codes.NotFound)),
			"Message":    structpb.NewStringValue("supplier doesn't exist"),
			"ErrorType":  structpb.NewStringValue(errors.ErrorTypeNotFound), // "ErrorTypeNotFound"
			"IsExpected": structpb.NewBoolValue(true),
			"Code":       structpb.NewNumberValue(float64(Error_ErrSupplierNotFound)), // 8013
		},
	})

	ErrSupplierNotFound = errors.GoErr{
		ErrType:    errors.ErrorTypeNotFound, // "ErrorTypeNotFound"
		Status:     statusErrSupplierNotFound,
		Code:       int(Error_ErrSupplierNotFound), // 8013
		Message:    "supplier doesn't exist",
		GrpcCode:   codes.NotFound,
		IsExpected: true,
	}
)

var (
	statusErrAccountNotFound, _ = status.New(
		codes.NotFound,
		"account doesn't exist",
	).WithDetails(&structpb.Struct{
		Fields: map[string]*structpb.Value{
			"GrpcCode":   structpb.NewNumberValue(float64(codes.NotFound)),
			"Message":    structpb.NewStringValue("account doesn't exist"),
			"ErrorType":  structpb.NewStringValue(errors.ErrorTypeNotFound), // "ErrorTypeNotFound"
			"IsExpected": structpb.NewBoolValue(true),
			"Code":       structpb.NewNumberValue(float64(Error_ErrAccountNotFound)), // 8015
		},
	})

	ErrAccountNotFound = errors.GoErr{
		ErrType:    errors.ErrorTypeNotFound, // "ErrorTypeNotFound"
		Status:     statusErrAccountNotFound,
		Code:       int(Error_ErrAccountNotFound), // 8015
		Message:    "account doesn't exist",
		GrpcCode:   codes.NotFound,
		IsExpected: true,
	}
)

var (
	statusErrBeneficiaryNotFound, _ = status.New(
		codes.NotFound,
		"beneficiary doesn't exist",
	).WithDetails(&structpb.Struct{
		Fields: map[string]*structpb.Value{
			"GrpcCode":   structpb.NewNumberValue(float64(codes.NotFound)),
			"Message":    structpb.NewStringValue("beneficiary doesn't exist"),
			"ErrorType":  structpb.NewStringValue(errors.ErrorTypeNotFound), // "ErrorTypeNotFound"
			"IsExpected": structpb.NewBoolValue(true),
			"Code":       structpb.NewNumberValue(float64(Error_ErrBeneficiaryNotFound)), // 8017
		},
	})

	ErrBeneficiaryNotFound = errors.GoErr{
		ErrType:    errors.ErrorTypeNotFound, // "ErrorTypeNotFound"
		Status:     statusErrBeneficiaryNotFound,
		Code:       int(Error_ErrBeneficiaryNotFound), // 8017
		Message:    "beneficiary doesn't exist",
		GrpcCode:   codes.NotFound,
		IsExpected: true,
	}
)

var (
	statusErrMccNotFound, _ = status.New(
		codes.NotFound,
		"mcc doesn't exist",
	).WithDetails(&structpb.Struct{
		Fields: map[string]*structpb.Value{
			"GrpcCode":   structpb.NewNumberValue(float64(codes.NotFound)),
			"Message":    structpb.NewStringValue("mcc doesn't exist"),
			"ErrorType":  structpb.NewStringValue(errors.ErrorTypeNotFound), // "ErrorTypeNotFound"
			"IsExpected": structpb.NewBoolValue(true),
			"Code":       structpb.NewNumberValue(float64(Error_ErrMccNotFound)), // 8019
		},
	})

	ErrMccNotFound = errors.GoErr{
		ErrType:    errors.ErrorTypeNotFound, // "ErrorTypeNotFound"
		Status:     statusErrMccNotFound,
		Code:       int(Error_ErrMccNotFound), // 8019
		Message:    "mcc doesn't exist",
		GrpcCode:   codes.NotFound,
		IsExpected: true,
	}
)

var (
	statusErrAffiliatedNotFound, _ = status.New(
		codes.NotFound,
		"affiliated doesn't exist",
	).WithDetails(&structpb.Struct{
		Fields: map[string]*structpb.Value{
			"GrpcCode":   structpb.NewNumberValue(float64(codes.NotFound)),
			"Message":    structpb.NewStringValue("affiliated doesn't exist"),
			"ErrorType":  structpb.NewStringValue(errors.ErrorTypeNotFound), // "ErrorTypeNotFound"
			"IsExpected": structpb.NewBoolValue(true),
			"Code":       structpb.NewNumberValue(float64(Error_ErrAffiliatedNotFound)), // 8021
		},
	})

	ErrAffiliatedNotFound = errors.GoErr{
		ErrType:    errors.ErrorTypeNotFound, // "ErrorTypeNotFound"
		Status:     statusErrAffiliatedNotFound,
		Code:       int(Error_ErrAffiliatedNotFound), // 8021
		Message:    "affiliated doesn't exist",
		GrpcCode:   codes.NotFound,
		IsExpected: true,
	}
)

var (
	statusErrCommentNotFound, _ = status.New(
		codes.NotFound,
		"comment doesn't exist",
	).WithDetails(&structpb.Struct{
		Fields: map[string]*structpb.Value{
			"GrpcCode":   structpb.NewNumberValue(float64(codes.NotFound)),
			"Message":    structpb.NewStringValue("comment doesn't exist"),
			"ErrorType":  structpb.NewStringValue(errors.ErrorTypeNotFound), // "ErrorTypeNotFound"
			"IsExpected": structpb.NewBoolValue(true),
			"Code":       structpb.NewNumberValue(float64(Error_ErrCommentNotFound)), // 8022
		},
	})

	ErrCommentNotFound = errors.GoErr{
		ErrType:    errors.ErrorTypeNotFound, // "ErrorTypeNotFound"
		Status:     statusErrCommentNotFound,
		Code:       int(Error_ErrCommentNotFound), // 8022
		Message:    "comment doesn't exist",
		GrpcCode:   codes.NotFound,
		IsExpected: true,
	}
)

var (
	statusErrTestCardNotFound, _ = status.New(
		codes.NotFound,
		"test card doesn't exist",
	).WithDetails(&structpb.Struct{
		Fields: map[string]*structpb.Value{
			"GrpcCode":   structpb.NewNumberValue(float64(codes.NotFound)),
			"Message":    structpb.NewStringValue("test card doesn't exist"),
			"ErrorType":  structpb.NewStringValue(errors.ErrorTypeNotFound), // "ErrorTypeNotFound"
			"IsExpected": structpb.NewBoolValue(true),
			"Code":       structpb.NewNumberValue(float64(Error_ErrTestCardNotFound)), // 8023
		},
	})

	ErrTestCardNotFound = errors.GoErr{
		ErrType:    errors.ErrorTypeNotFound, // "ErrorTypeNotFound"
		Status:     statusErrTestCardNotFound,
		Code:       int(Error_ErrTestCardNotFound), // 8023
		Message:    "test card doesn't exist",
		GrpcCode:   codes.NotFound,
		IsExpected: true,
	}
)

var (
	statusErrOnboardingNotFound, _ = status.New(
		codes.NotFound,
		"onboarding doesn't exist",
	).WithDetails(&structpb.Struct{
		Fields: map[string]*structpb.Value{
			"GrpcCode":   structpb.NewNumberValue(float64(codes.NotFound)),
			"Message":    structpb.NewStringValue("onboarding doesn't exist"),
			"ErrorType":  structpb.NewStringValue(errors.ErrorTypeNotFound), // "ErrorTypeNotFound"
			"IsExpected": structpb.NewBoolValue(true),
			"Code":       structpb.NewNumberValue(float64(Error_ErrOnboardingNotFound)), // 8024
		},
	})

	ErrOnboardingNotFound = errors.GoErr{
		ErrType:    errors.ErrorTypeNotFound, // "ErrorTypeNotFound"
		Status:     statusErrOnboardingNotFound,
		Code:       int(Error_ErrOnboardingNotFound), // 8024
		Message:    "onboarding doesn't exist",
		GrpcCode:   codes.NotFound,
		IsExpected: true,
	}
)

var (
	statusErrPublicOfficialNotFound, _ = status.New(
		codes.NotFound,
		"public-official doesn't exist",
	).WithDetails(&structpb.Struct{
		Fields: map[string]*structpb.Value{
			"GrpcCode":   structpb.NewNumberValue(float64(codes.NotFound)),
			"Message":    structpb.NewStringValue("public-official doesn't exist"),
			"ErrorType":  structpb.NewStringValue(errors.ErrorTypeNotFound), // "ErrorTypeNotFound"
			"IsExpected": structpb.NewBoolValue(true),
			"Code":       structpb.NewNumberValue(float64(Error_ErrPublicOfficialNotFound)), // 8027
		},
	})

	ErrPublicOfficialNotFound = errors.GoErr{
		ErrType:    errors.ErrorTypeNotFound, // "ErrorTypeNotFound"
		Status:     statusErrPublicOfficialNotFound,
		Code:       int(Error_ErrPublicOfficialNotFound), // 8027
		Message:    "public-official doesn't exist",
		GrpcCode:   codes.NotFound,
		IsExpected: true,
	}
)

var (
	statusErrRequestNotFound, _ = status.New(
		codes.NotFound,
		"request doesn't exist",
	).WithDetails(&structpb.Struct{
		Fields: map[string]*structpb.Value{
			"GrpcCode":   structpb.NewNumberValue(float64(codes.NotFound)),
			"Message":    structpb.NewStringValue("request doesn't exist"),
			"ErrorType":  structpb.NewStringValue(errors.ErrorTypeNotFound), // "ErrorTypeNotFound"
			"IsExpected": structpb.NewBoolValue(true),
			"Code":       structpb.NewNumberValue(float64(Error_ErrRequestNotFound)), // 8028
		},
	})

	ErrRequestNotFound = errors.GoErr{
		ErrType:    errors.ErrorTypeNotFound, // "ErrorTypeNotFound"
		Status:     statusErrRequestNotFound,
		Code:       int(Error_ErrRequestNotFound), // 8028
		Message:    "request doesn't exist",
		GrpcCode:   codes.NotFound,
		IsExpected: true,
	}
)

var (
	statusErrStakeholderNotFound, _ = status.New(
		codes.NotFound,
		"stakeholder doesn't exist",
	).WithDetails(&structpb.Struct{
		Fields: map[string]*structpb.Value{
			"GrpcCode":   structpb.NewNumberValue(float64(codes.NotFound)),
			"Message":    structpb.NewStringValue("stakeholder doesn't exist"),
			"ErrorType":  structpb.NewStringValue(errors.ErrorTypeNotFound), // "ErrorTypeNotFound"
			"IsExpected": structpb.NewBoolValue(true),
			"Code":       structpb.NewNumberValue(float64(Error_ErrStakeholderNotFound)), // 8031
		},
	})

	ErrStakeholderNotFound = errors.GoErr{
		ErrType:    errors.ErrorTypeNotFound, // "ErrorTypeNotFound"
		Status:     statusErrStakeholderNotFound,
		Code:       int(Error_ErrStakeholderNotFound), // 8031
		Message:    "stakeholder doesn't exist",
		GrpcCode:   codes.NotFound,
		IsExpected: true,
	}
)

var (
	statusErrProjectStoreNotFound, _ = status.New(
		codes.NotFound,
		"project-store doesn't exist",
	).WithDetails(&structpb.Struct{
		Fields: map[string]*structpb.Value{
			"GrpcCode":   structpb.NewNumberValue(float64(codes.NotFound)),
			"Message":    structpb.NewStringValue("project-store doesn't exist"),
			"ErrorType":  structpb.NewStringValue(errors.ErrorTypeNotFound), // "ErrorTypeNotFound"
			"IsExpected": structpb.NewBoolValue(true),
			"Code":       structpb.NewNumberValue(float64(Error_ErrProjectStoreNotFound)), // 8032
		},
	})

	ErrProjectStoreNotFound = errors.GoErr{
		ErrType:    errors.ErrorTypeNotFound, // "ErrorTypeNotFound"
		Status:     statusErrProjectStoreNotFound,
		Code:       int(Error_ErrProjectStoreNotFound), // 8032
		Message:    "project-store doesn't exist",
		GrpcCode:   codes.NotFound,
		IsExpected: true,
	}
)

var (
	statusErrTransferSplitTaxSettingsNotFound, _ = status.New(
		codes.NotFound,
		"transfer split tax settings not found",
	).WithDetails(&structpb.Struct{
		Fields: map[string]*structpb.Value{
			"GrpcCode":   structpb.NewNumberValue(float64(codes.NotFound)),
			"Message":    structpb.NewStringValue("transfer split tax settings not found"),
			"ErrorType":  structpb.NewStringValue(errors.ErrorTypeNotFound), // "ErrorTypeNotFound"
			"IsExpected": structpb.NewBoolValue(true),
			"Code":       structpb.NewNumberValue(float64(Error_ErrTransferSplitTaxSettingsNotFound)), // 9105
		},
	})

	ErrTransferSplitTaxSettingsNotFound = errors.GoErr{
		ErrType:    errors.ErrorTypeNotFound, // "ErrorTypeNotFound"
		Status:     statusErrTransferSplitTaxSettingsNotFound,
		Code:       int(Error_ErrTransferSplitTaxSettingsNotFound), // 9105
		Message:    "transfer split tax settings not found",
		GrpcCode:   codes.NotFound,
		IsExpected: true,
	}
)

var (
	statusErrSystemTaxSettingsNotFound, _ = status.New(
		codes.NotFound,
		"system tax settings not found",
	).WithDetails(&structpb.Struct{
		Fields: map[string]*structpb.Value{
			"GrpcCode":   structpb.NewNumberValue(float64(codes.NotFound)),
			"Message":    structpb.NewStringValue("system tax settings not found"),
			"ErrorType":  structpb.NewStringValue(errors.ErrorTypeNotFound), // "ErrorTypeNotFound"
			"IsExpected": structpb.NewBoolValue(true),
			"Code":       structpb.NewNumberValue(float64(Error_ErrSystemTaxSettingsNotFound)), // 9106
		},
	})

	ErrSystemTaxSettingsNotFound = errors.GoErr{
		ErrType:    errors.ErrorTypeNotFound, // "ErrorTypeNotFound"
		Status:     statusErrSystemTaxSettingsNotFound,
		Code:       int(Error_ErrSystemTaxSettingsNotFound), // 9106
		Message:    "system tax settings not found",
		GrpcCode:   codes.NotFound,
		IsExpected: true,
	}
)

var (
	statusErrRowOperationsNotFound, _ = status.New(
		codes.NotFound,
		"row operations not found",
	).WithDetails(&structpb.Struct{
		Fields: map[string]*structpb.Value{
			"GrpcCode":   structpb.NewNumberValue(float64(codes.NotFound)),
			"Message":    structpb.NewStringValue("row operations not found"),
			"ErrorType":  structpb.NewStringValue(errors.ErrorTypeNotFound), // "ErrorTypeNotFound"
			"IsExpected": structpb.NewBoolValue(true),
			"Code":       structpb.NewNumberValue(float64(Error_ErrRowOperationsNotFound)), // 9107
		},
	})

	ErrRowOperationsNotFound = errors.GoErr{
		ErrType:    errors.ErrorTypeNotFound, // "ErrorTypeNotFound"
		Status:     statusErrRowOperationsNotFound,
		Code:       int(Error_ErrRowOperationsNotFound), // 9107
		Message:    "row operations not found",
		GrpcCode:   codes.NotFound,
		IsExpected: true,
	}
)

var (
	statusErrPaymentSplitTaxSettingNotFound, _ = status.New(
		codes.NotFound,
		"payment split tax settings not found",
	).WithDetails(&structpb.Struct{
		Fields: map[string]*structpb.Value{
			"GrpcCode":   structpb.NewNumberValue(float64(codes.NotFound)),
			"Message":    structpb.NewStringValue("payment split tax settings not found"),
			"ErrorType":  structpb.NewStringValue(errors.ErrorTypeNotFound), // "ErrorTypeNotFound"
			"IsExpected": structpb.NewBoolValue(true),
			"Code":       structpb.NewNumberValue(float64(Error_ErrPaymentSplitTaxSettingNotFound)), // 9108
		},
	})

	ErrPaymentSplitTaxSettingNotFound = errors.GoErr{
		ErrType:    errors.ErrorTypeNotFound, // "ErrorTypeNotFound"
		Status:     statusErrPaymentSplitTaxSettingNotFound,
		Code:       int(Error_ErrPaymentSplitTaxSettingNotFound), // 9108
		Message:    "payment split tax settings not found",
		GrpcCode:   codes.NotFound,
		IsExpected: true,
	}
)

var (
	statusErrProjectColorNotFound, _ = status.New(
		codes.InvalidArgument,
		"project colors does not exist",
	).WithDetails(&structpb.Struct{
		Fields: map[string]*structpb.Value{
			"GrpcCode":   structpb.NewNumberValue(float64(codes.InvalidArgument)),
			"Message":    structpb.NewStringValue("project colors does not exist"),
			"ErrorType":  structpb.NewStringValue(errors.ErrorTypeBadRequest), // "ErrorTypeBadRequest"
			"IsExpected": structpb.NewBoolValue(true),
			"Code":       structpb.NewNumberValue(float64(Error_ErrProjectColorNotFound)), // 2008
		},
	})

	ErrProjectColorNotFound = errors.GoErr{
		ErrType:    errors.ErrorTypeBadRequest, // "ErrorTypeBadRequest"
		Status:     statusErrProjectColorNotFound,
		Code:       int(Error_ErrProjectColorNotFound), // 2008
		Message:    "project colors does not exist",
		GrpcCode:   codes.InvalidArgument,
		IsExpected: true,
	}
)

var (
	statusErrProjectPayFormInfoNotFound, _ = status.New(
		codes.InvalidArgument,
		"project pay form info does not exist",
	).WithDetails(&structpb.Struct{
		Fields: map[string]*structpb.Value{
			"GrpcCode":   structpb.NewNumberValue(float64(codes.InvalidArgument)),
			"Message":    structpb.NewStringValue("project pay form info does not exist"),
			"ErrorType":  structpb.NewStringValue(errors.ErrorTypeBadRequest), // "ErrorTypeBadRequest"
			"IsExpected": structpb.NewBoolValue(true),
			"Code":       structpb.NewNumberValue(float64(Error_ErrProjectPayFormInfoNotFound)), // 2009
		},
	})

	ErrProjectPayFormInfoNotFound = errors.GoErr{
		ErrType:    errors.ErrorTypeBadRequest, // "ErrorTypeBadRequest"
		Status:     statusErrProjectPayFormInfoNotFound,
		Code:       int(Error_ErrProjectPayFormInfoNotFound), // 2009
		Message:    "project pay form info does not exist",
		GrpcCode:   codes.InvalidArgument,
		IsExpected: true,
	}
)

var (
	statusErrProjectBannerNotFound, _ = status.New(
		codes.InvalidArgument,
		"project banner does not exist",
	).WithDetails(&structpb.Struct{
		Fields: map[string]*structpb.Value{
			"GrpcCode":   structpb.NewNumberValue(float64(codes.InvalidArgument)),
			"Message":    structpb.NewStringValue("project banner does not exist"),
			"ErrorType":  structpb.NewStringValue(errors.ErrorTypeBadRequest), // "ErrorTypeBadRequest"
			"IsExpected": structpb.NewBoolValue(true),
			"Code":       structpb.NewNumberValue(float64(Error_ErrProjectBannerNotFound)), // 2010
		},
	})

	ErrProjectBannerNotFound = errors.GoErr{
		ErrType:    errors.ErrorTypeBadRequest, // "ErrorTypeBadRequest"
		Status:     statusErrProjectBannerNotFound,
		Code:       int(Error_ErrProjectBannerNotFound), // 2010
		Message:    "project banner does not exist",
		GrpcCode:   codes.InvalidArgument,
		IsExpected: true,
	}
)

var (
	statusErrCantCreateMoreOnboardings, _ = status.New(
		codes.InvalidArgument,
		"there are already 2 onboardings created",
	).WithDetails(&structpb.Struct{
		Fields: map[string]*structpb.Value{
			"GrpcCode":   structpb.NewNumberValue(float64(codes.InvalidArgument)),
			"Message":    structpb.NewStringValue("there are already 2 onboardings created"),
			"ErrorType":  structpb.NewStringValue(errors.ErrorTypeBadRequest), // "ErrorTypeBadRequest"
			"IsExpected": structpb.NewBoolValue(true),
			"Code":       structpb.NewNumberValue(float64(Error_ErrCantCreateMoreOnboardings)), // 7000
		},
	})

	ErrCantCreateMoreOnboardings = errors.GoErr{
		ErrType:    errors.ErrorTypeBadRequest, // "ErrorTypeBadRequest"
		Status:     statusErrCantCreateMoreOnboardings,
		Code:       int(Error_ErrCantCreateMoreOnboardings), // 7000
		Message:    "there are already 2 onboardings created",
		GrpcCode:   codes.InvalidArgument,
		IsExpected: true,
	}
)

var (
	statusErrServiceNotExist, _ = status.New(
		codes.InvalidArgument,
		"service doesn't exists",
	).WithDetails(&structpb.Struct{
		Fields: map[string]*structpb.Value{
			"GrpcCode":   structpb.NewNumberValue(float64(codes.InvalidArgument)),
			"Message":    structpb.NewStringValue("service doesn't exists"),
			"ErrorType":  structpb.NewStringValue(errors.ErrorTypeBadRequest), // "ErrorTypeBadRequest"
			"IsExpected": structpb.NewBoolValue(true),
			"Code":       structpb.NewNumberValue(float64(Error_ErrServiceNotExist)), // 1900
		},
	})

	ErrServiceNotExist = errors.GoErr{
		ErrType:    errors.ErrorTypeBadRequest, // "ErrorTypeBadRequest"
		Status:     statusErrServiceNotExist,
		Code:       int(Error_ErrServiceNotExist), // 1900
		Message:    "service doesn't exists",
		GrpcCode:   codes.InvalidArgument,
		IsExpected: true,
	}
)

var (
	statusErrDataKeysNotFound, _ = status.New(
		codes.NotFound,
		"data keys don't exist",
	).WithDetails(&structpb.Struct{
		Fields: map[string]*structpb.Value{
			"GrpcCode":   structpb.NewNumberValue(float64(codes.NotFound)),
			"Message":    structpb.NewStringValue("data keys don't exist"),
			"ErrorType":  structpb.NewStringValue(errors.ErrorTypeNotFound), // "ErrorTypeNotFound"
			"IsExpected": structpb.NewBoolValue(true),
			"Code":       structpb.NewNumberValue(float64(Error_ErrDataKeysNotFound)), // 1507
		},
	})

	ErrDataKeysNotFound = errors.GoErr{
		ErrType:    errors.ErrorTypeNotFound, // "ErrorTypeNotFound"
		Status:     statusErrDataKeysNotFound,
		Code:       int(Error_ErrDataKeysNotFound), // 1507
		Message:    "data keys don't exist",
		GrpcCode:   codes.NotFound,
		IsExpected: true,
	}
)

var (
	statusErrReportNotFound, _ = status.New(
		codes.NotFound,
		"report doesn't exist",
	).WithDetails(&structpb.Struct{
		Fields: map[string]*structpb.Value{
			"GrpcCode":   structpb.NewNumberValue(float64(codes.NotFound)),
			"Message":    structpb.NewStringValue("report doesn't exist"),
			"ErrorType":  structpb.NewStringValue(errors.ErrorTypeNotFound), // "ErrorTypeNotFound"
			"IsExpected": structpb.NewBoolValue(true),
			"Code":       structpb.NewNumberValue(float64(Error_ErrReportNotFound)), // 1509
		},
	})

	ErrReportNotFound = errors.GoErr{
		ErrType:    errors.ErrorTypeNotFound, // "ErrorTypeNotFound"
		Status:     statusErrReportNotFound,
		Code:       int(Error_ErrReportNotFound), // 1509
		Message:    "report doesn't exist",
		GrpcCode:   codes.NotFound,
		IsExpected: true,
	}
)

var (
	statusErrHttpRequestUnexpected, _ = status.New(
		codes.Unknown,
		"unexpected http request error",
	).WithDetails(&structpb.Struct{
		Fields: map[string]*structpb.Value{
			"GrpcCode":   structpb.NewNumberValue(float64(codes.Unknown)),
			"Message":    structpb.NewStringValue("unexpected http request error"),
			"ErrorType":  structpb.NewStringValue(errors.ErrorTypeUnexpectedError), // "ErrorTypeUnexpectedError"
			"IsExpected": structpb.NewBoolValue(true),
			"Code":       structpb.NewNumberValue(float64(Error_ErrHttpRequestUnexpected)), // 6500
		},
	})

	ErrHttpRequestUnexpected = errors.GoErr{
		ErrType:    errors.ErrorTypeUnexpectedError, // "ErrorTypeUnexpectedError"
		Status:     statusErrHttpRequestUnexpected,
		Code:       int(Error_ErrHttpRequestUnexpected), // 6500
		Message:    "unexpected http request error",
		GrpcCode:   codes.Unknown,
		IsExpected: true,
	}
)

var (
	statusErrBadStatusCode, _ = status.New(
		codes.Unknown,
		"bad status code",
	).WithDetails(&structpb.Struct{
		Fields: map[string]*structpb.Value{
			"GrpcCode":   structpb.NewNumberValue(float64(codes.Unknown)),
			"Message":    structpb.NewStringValue("bad status code"),
			"ErrorType":  structpb.NewStringValue(errors.ErrorTypeUnexpectedError), // "ErrorTypeUnexpectedError"
			"IsExpected": structpb.NewBoolValue(true),
			"Code":       structpb.NewNumberValue(float64(Error_ErrBadStatusCode)), // 7020
		},
	})

	ErrBadStatusCode = errors.GoErr{
		ErrType:    errors.ErrorTypeUnexpectedError, // "ErrorTypeUnexpectedError"
		Status:     statusErrBadStatusCode,
		Code:       int(Error_ErrBadStatusCode), // 7020
		Message:    "bad status code",
		GrpcCode:   codes.Unknown,
		IsExpected: true,
	}
)

var (
	statusErrInvalidBankApiURL, _ = status.New(
		codes.Unknown,
		"could not form the valid api url",
	).WithDetails(&structpb.Struct{
		Fields: map[string]*structpb.Value{
			"GrpcCode":   structpb.NewNumberValue(float64(codes.Unknown)),
			"Message":    structpb.NewStringValue("could not form the valid api url"),
			"ErrorType":  structpb.NewStringValue(errors.ErrorTypeUnexpectedError), // "ErrorTypeUnexpectedError"
			"IsExpected": structpb.NewBoolValue(true),
			"Code":       structpb.NewNumberValue(float64(Error_ErrInvalidBankApiURL)), // 7019
		},
	})

	ErrInvalidBankApiURL = errors.GoErr{
		ErrType:    errors.ErrorTypeUnexpectedError, // "ErrorTypeUnexpectedError"
		Status:     statusErrInvalidBankApiURL,
		Code:       int(Error_ErrInvalidBankApiURL), // 7019
		Message:    "could not form the valid api url",
		GrpcCode:   codes.Unknown,
		IsExpected: true,
	}
)

var (
	statusErrCreateCheckRequestURL, _ = status.New(
		codes.InvalidArgument,
		"could not form valid account check API URL",
	).WithDetails(&structpb.Struct{
		Fields: map[string]*structpb.Value{
			"GrpcCode":   structpb.NewNumberValue(float64(codes.InvalidArgument)),
			"Message":    structpb.NewStringValue("could not form valid account check API URL"),
			"ErrorType":  structpb.NewStringValue(errors.ErrorTypeBadRequest), // "ErrorTypeBadRequest"
			"IsExpected": structpb.NewBoolValue(true),
			"Code":       structpb.NewNumberValue(float64(Error_ErrCreateCheckRequestURL)), // 1500
		},
	})

	ErrCreateCheckRequestURL = errors.GoErr{
		ErrType:    errors.ErrorTypeBadRequest, // "ErrorTypeBadRequest"
		Status:     statusErrCreateCheckRequestURL,
		Code:       int(Error_ErrCreateCheckRequestURL), // 1500
		Message:    "could not form valid account check API URL",
		GrpcCode:   codes.InvalidArgument,
		IsExpected: true,
	}
)

var (
	statusErrFailedAccountCheck, _ = status.New(
		codes.InvalidArgument,
		"failed to check account",
	).WithDetails(&structpb.Struct{
		Fields: map[string]*structpb.Value{
			"GrpcCode":   structpb.NewNumberValue(float64(codes.InvalidArgument)),
			"Message":    structpb.NewStringValue("failed to check account"),
			"ErrorType":  structpb.NewStringValue(errors.ErrorTypeBadRequest), // "ErrorTypeBadRequest"
			"IsExpected": structpb.NewBoolValue(true),
			"Code":       structpb.NewNumberValue(float64(Error_ErrFailedAccountCheck)), // 1501
		},
	})

	ErrFailedAccountCheck = errors.GoErr{
		ErrType:    errors.ErrorTypeBadRequest, // "ErrorTypeBadRequest"
		Status:     statusErrFailedAccountCheck,
		Code:       int(Error_ErrFailedAccountCheck), // 1501
		Message:    "failed to check account",
		GrpcCode:   codes.InvalidArgument,
		IsExpected: true,
	}
)

var (
	statusErrCreateCashInRequestURL, _ = status.New(
		codes.InvalidArgument,
		"could not form valid cash in API URL",
	).WithDetails(&structpb.Struct{
		Fields: map[string]*structpb.Value{
			"GrpcCode":   structpb.NewNumberValue(float64(codes.InvalidArgument)),
			"Message":    structpb.NewStringValue("could not form valid cash in API URL"),
			"ErrorType":  structpb.NewStringValue(errors.ErrorTypeBadRequest), // "ErrorTypeBadRequest"
			"IsExpected": structpb.NewBoolValue(true),
			"Code":       structpb.NewNumberValue(float64(Error_ErrCreateCashInRequestURL)), // 1502
		},
	})

	ErrCreateCashInRequestURL = errors.GoErr{
		ErrType:    errors.ErrorTypeBadRequest, // "ErrorTypeBadRequest"
		Status:     statusErrCreateCashInRequestURL,
		Code:       int(Error_ErrCreateCashInRequestURL), // 1502
		Message:    "could not form valid cash in API URL",
		GrpcCode:   codes.InvalidArgument,
		IsExpected: true,
	}
)

var (
	statusErrFailedCashIn, _ = status.New(
		codes.InvalidArgument,
		"failed to cash in",
	).WithDetails(&structpb.Struct{
		Fields: map[string]*structpb.Value{
			"GrpcCode":   structpb.NewNumberValue(float64(codes.InvalidArgument)),
			"Message":    structpb.NewStringValue("failed to cash in"),
			"ErrorType":  structpb.NewStringValue(errors.ErrorTypeBadRequest), // "ErrorTypeBadRequest"
			"IsExpected": structpb.NewBoolValue(true),
			"Code":       structpb.NewNumberValue(float64(Error_ErrFailedCashIn)), // 1503
		},
	})

	ErrFailedCashIn = errors.GoErr{
		ErrType:    errors.ErrorTypeBadRequest, // "ErrorTypeBadRequest"
		Status:     statusErrFailedCashIn,
		Code:       int(Error_ErrFailedCashIn), // 1503
		Message:    "failed to cash in",
		GrpcCode:   codes.InvalidArgument,
		IsExpected: true,
	}
)

var (
	statusErrCreateStatusRequestURL, _ = status.New(
		codes.InvalidArgument,
		"could not form valid get status API URL",
	).WithDetails(&structpb.Struct{
		Fields: map[string]*structpb.Value{
			"GrpcCode":   structpb.NewNumberValue(float64(codes.InvalidArgument)),
			"Message":    structpb.NewStringValue("could not form valid get status API URL"),
			"ErrorType":  structpb.NewStringValue(errors.ErrorTypeBadRequest), // "ErrorTypeBadRequest"
			"IsExpected": structpb.NewBoolValue(true),
			"Code":       structpb.NewNumberValue(float64(Error_ErrCreateStatusRequestURL)), // 1504
		},
	})

	ErrCreateStatusRequestURL = errors.GoErr{
		ErrType:    errors.ErrorTypeBadRequest, // "ErrorTypeBadRequest"
		Status:     statusErrCreateStatusRequestURL,
		Code:       int(Error_ErrCreateStatusRequestURL), // 1504
		Message:    "could not form valid get status API URL",
		GrpcCode:   codes.InvalidArgument,
		IsExpected: true,
	}
)

var (
	statusErrFailedGetStatus, _ = status.New(
		codes.InvalidArgument,
		"failed to get status",
	).WithDetails(&structpb.Struct{
		Fields: map[string]*structpb.Value{
			"GrpcCode":   structpb.NewNumberValue(float64(codes.InvalidArgument)),
			"Message":    structpb.NewStringValue("failed to get status"),
			"ErrorType":  structpb.NewStringValue(errors.ErrorTypeBadRequest), // "ErrorTypeBadRequest"
			"IsExpected": structpb.NewBoolValue(true),
			"Code":       structpb.NewNumberValue(float64(Error_ErrFailedGetStatus)), // 1505
		},
	})

	ErrFailedGetStatus = errors.GoErr{
		ErrType:    errors.ErrorTypeBadRequest, // "ErrorTypeBadRequest"
		Status:     statusErrFailedGetStatus,
		Code:       int(Error_ErrFailedGetStatus), // 1505
		Message:    "failed to get status",
		GrpcCode:   codes.InvalidArgument,
		IsExpected: true,
	}
)

var (
	statusErrEmptyErrorResponse, _ = status.New(
		codes.InvalidArgument,
		"empty error response",
	).WithDetails(&structpb.Struct{
		Fields: map[string]*structpb.Value{
			"GrpcCode":   structpb.NewNumberValue(float64(codes.InvalidArgument)),
			"Message":    structpb.NewStringValue("empty error response"),
			"ErrorType":  structpb.NewStringValue(errors.ErrorTypeBadRequest), // "ErrorTypeBadRequest"
			"IsExpected": structpb.NewBoolValue(true),
			"Code":       structpb.NewNumberValue(float64(Error_ErrEmptyErrorResponse)), // 1506
		},
	})

	ErrEmptyErrorResponse = errors.GoErr{
		ErrType:    errors.ErrorTypeBadRequest, // "ErrorTypeBadRequest"
		Status:     statusErrEmptyErrorResponse,
		Code:       int(Error_ErrEmptyErrorResponse), // 1506
		Message:    "empty error response",
		GrpcCode:   codes.InvalidArgument,
		IsExpected: true,
	}
)

var (
	statusErrEmailIntegrationError, _ = status.New(
		codes.FailedPrecondition,
		"email integration error",
	).WithDetails(&structpb.Struct{
		Fields: map[string]*structpb.Value{
			"GrpcCode":   structpb.NewNumberValue(float64(codes.FailedPrecondition)),
			"Message":    structpb.NewStringValue("email integration error"),
			"ErrorType":  structpb.NewStringValue(errors.ErrorTypeIntegration), // "ErrorTypeIntegration"
			"IsExpected": structpb.NewBoolValue(true),
			"Code":       structpb.NewNumberValue(float64(Error_ErrEmailIntegrationError)), // 8200
		},
	})

	ErrEmailIntegrationError = errors.GoErr{
		ErrType:    errors.ErrorTypeIntegration, // "ErrorTypeIntegration"
		Status:     statusErrEmailIntegrationError,
		Code:       int(Error_ErrEmailIntegrationError), // 8200
		Message:    "email integration error",
		GrpcCode:   codes.FailedPrecondition,
		IsExpected: true,
	}
)

var (
	statusErrSmsIntegrationError, _ = status.New(
		codes.FailedPrecondition,
		"sms integration error",
	).WithDetails(&structpb.Struct{
		Fields: map[string]*structpb.Value{
			"GrpcCode":   structpb.NewNumberValue(float64(codes.FailedPrecondition)),
			"Message":    structpb.NewStringValue("sms integration error"),
			"ErrorType":  structpb.NewStringValue(errors.ErrorTypeIntegration), // "ErrorTypeIntegration"
			"IsExpected": structpb.NewBoolValue(true),
			"Code":       structpb.NewNumberValue(float64(Error_ErrSmsIntegrationError)), // 8201
		},
	})

	ErrSmsIntegrationError = errors.GoErr{
		ErrType:    errors.ErrorTypeIntegration, // "ErrorTypeIntegration"
		Status:     statusErrSmsIntegrationError,
		Code:       int(Error_ErrSmsIntegrationError), // 8201
		Message:    "sms integration error",
		GrpcCode:   codes.FailedPrecondition,
		IsExpected: true,
	}
)

var (
	statusErrBankResponseCodeNotFound, _ = status.New(
		codes.NotFound,
		"bank response code doesn't exist",
	).WithDetails(&structpb.Struct{
		Fields: map[string]*structpb.Value{
			"GrpcCode":   structpb.NewNumberValue(float64(codes.NotFound)),
			"Message":    structpb.NewStringValue("bank response code doesn't exist"),
			"ErrorType":  structpb.NewStringValue(errors.ErrorTypeNotFound), // "ErrorTypeNotFound"
			"IsExpected": structpb.NewBoolValue(true),
			"Code":       structpb.NewNumberValue(float64(Error_ErrBankResponseCodeNotFound)), // 1200
		},
	})

	ErrBankResponseCodeNotFound = errors.GoErr{
		ErrType:    errors.ErrorTypeNotFound, // "ErrorTypeNotFound"
		Status:     statusErrBankResponseCodeNotFound,
		Code:       int(Error_ErrBankResponseCodeNotFound), // 1200
		Message:    "bank response code doesn't exist",
		GrpcCode:   codes.NotFound,
		IsExpected: true,
	}
)

var (
	statusErrBankResponseStatusNotFound, _ = status.New(
		codes.NotFound,
		"bank response status doesn't exists",
	).WithDetails(&structpb.Struct{
		Fields: map[string]*structpb.Value{
			"GrpcCode":   structpb.NewNumberValue(float64(codes.NotFound)),
			"Message":    structpb.NewStringValue("bank response status doesn't exists"),
			"ErrorType":  structpb.NewStringValue(errors.ErrorTypeNotFound), // "ErrorTypeNotFound"
			"IsExpected": structpb.NewBoolValue(true),
			"Code":       structpb.NewNumberValue(float64(Error_ErrBankResponseStatusNotFound)), // 1201
		},
	})

	ErrBankResponseStatusNotFound = errors.GoErr{
		ErrType:    errors.ErrorTypeNotFound, // "ErrorTypeNotFound"
		Status:     statusErrBankResponseStatusNotFound,
		Code:       int(Error_ErrBankResponseStatusNotFound), // 1201
		Message:    "bank response status doesn't exists",
		GrpcCode:   codes.NotFound,
		IsExpected: true,
	}
)

var (
	statusErrIntegrationErrorNotFound, _ = status.New(
		codes.NotFound,
		"integration error not found",
	).WithDetails(&structpb.Struct{
		Fields: map[string]*structpb.Value{
			"GrpcCode":   structpb.NewNumberValue(float64(codes.NotFound)),
			"Message":    structpb.NewStringValue("integration error not found"),
			"ErrorType":  structpb.NewStringValue(errors.ErrorTypeNotFound), // "ErrorTypeNotFound"
			"IsExpected": structpb.NewBoolValue(true),
			"Code":       structpb.NewNumberValue(float64(Error_ErrIntegrationErrorNotFound)), // 1203
		},
	})

	ErrIntegrationErrorNotFound = errors.GoErr{
		ErrType:    errors.ErrorTypeNotFound, // "ErrorTypeNotFound"
		Status:     statusErrIntegrationErrorNotFound,
		Code:       int(Error_ErrIntegrationErrorNotFound), // 1203
		Message:    "integration error not found",
		GrpcCode:   codes.NotFound,
		IsExpected: true,
	}
)

var (
	statusErrCallbackStatusNotFound, _ = status.New(
		codes.NotFound,
		"callback status doesn't exist",
	).WithDetails(&structpb.Struct{
		Fields: map[string]*structpb.Value{
			"GrpcCode":   structpb.NewNumberValue(float64(codes.NotFound)),
			"Message":    structpb.NewStringValue("callback status doesn't exist"),
			"ErrorType":  structpb.NewStringValue(errors.ErrorTypeNotFound), // "ErrorTypeNotFound"
			"IsExpected": structpb.NewBoolValue(true),
			"Code":       structpb.NewNumberValue(float64(Error_ErrCallbackStatusNotFound)), // 2007
		},
	})

	ErrCallbackStatusNotFound = errors.GoErr{
		ErrType:    errors.ErrorTypeNotFound, // "ErrorTypeNotFound"
		Status:     statusErrCallbackStatusNotFound,
		Code:       int(Error_ErrCallbackStatusNotFound), // 2007
		Message:    "callback status doesn't exist",
		GrpcCode:   codes.NotFound,
		IsExpected: true,
	}
)

var (
	statusErrFailedToSendCallback, _ = status.New(
		codes.InvalidArgument,
		"failed to send initial callback",
	).WithDetails(&structpb.Struct{
		Fields: map[string]*structpb.Value{
			"GrpcCode":   structpb.NewNumberValue(float64(codes.InvalidArgument)),
			"Message":    structpb.NewStringValue("failed to send initial callback"),
			"ErrorType":  structpb.NewStringValue(errors.ErrorTypeBadRequest), // "ErrorTypeBadRequest"
			"IsExpected": structpb.NewBoolValue(true),
			"Code":       structpb.NewNumberValue(float64(Error_ErrFailedToSendCallback)), // 9715
		},
	})

	ErrFailedToSendCallback = errors.GoErr{
		ErrType:    errors.ErrorTypeBadRequest, // "ErrorTypeBadRequest"
		Status:     statusErrFailedToSendCallback,
		Code:       int(Error_ErrFailedToSendCallback), // 9715
		Message:    "failed to send initial callback",
		GrpcCode:   codes.InvalidArgument,
		IsExpected: true,
	}
)

var (
	statusErrCanNotSaveFile, _ = status.New(
		codes.Unknown,
		"file can't be saved",
	).WithDetails(&structpb.Struct{
		Fields: map[string]*structpb.Value{
			"GrpcCode":   structpb.NewNumberValue(float64(codes.Unknown)),
			"Message":    structpb.NewStringValue("file can't be saved"),
			"ErrorType":  structpb.NewStringValue(errors.ErrorTypeUnexpectedError), // "ErrorTypeUnexpectedError"
			"IsExpected": structpb.NewBoolValue(true),
			"Code":       structpb.NewNumberValue(float64(Error_ErrCanNotSaveFile)), // 8014
		},
	})

	ErrCanNotSaveFile = errors.GoErr{
		ErrType:    errors.ErrorTypeUnexpectedError, // "ErrorTypeUnexpectedError"
		Status:     statusErrCanNotSaveFile,
		Code:       int(Error_ErrCanNotSaveFile), // 8014
		Message:    "file can't be saved",
		GrpcCode:   codes.Unknown,
		IsExpected: true,
	}
)

var (
	statusErrInfrastructureNotFound, _ = status.New(
		codes.NotFound,
		"infrastructures don't exist",
	).WithDetails(&structpb.Struct{
		Fields: map[string]*structpb.Value{
			"GrpcCode":   structpb.NewNumberValue(float64(codes.NotFound)),
			"Message":    structpb.NewStringValue("infrastructures don't exist"),
			"ErrorType":  structpb.NewStringValue(errors.ErrorTypeNotFound), // "ErrorTypeNotFound"
			"IsExpected": structpb.NewBoolValue(true),
			"Code":       structpb.NewNumberValue(float64(Error_ErrInfrastructureNotFound)), // 7103
		},
	})

	ErrInfrastructureNotFound = errors.GoErr{
		ErrType:    errors.ErrorTypeNotFound, // "ErrorTypeNotFound"
		Status:     statusErrInfrastructureNotFound,
		Code:       int(Error_ErrInfrastructureNotFound), // 7103
		Message:    "infrastructures don't exist",
		GrpcCode:   codes.NotFound,
		IsExpected: true,
	}
)

var (
	statusErrBankResponseMessage, _ = status.New(
		codes.InvalidArgument,
		"failed to insert bank response message",
	).WithDetails(&structpb.Struct{
		Fields: map[string]*structpb.Value{
			"GrpcCode":   structpb.NewNumberValue(float64(codes.InvalidArgument)),
			"Message":    structpb.NewStringValue("failed to insert bank response message"),
			"ErrorType":  structpb.NewStringValue(errors.ErrorTypeBadRequest), // "ErrorTypeBadRequest"
			"IsExpected": structpb.NewBoolValue(true),
			"Code":       structpb.NewNumberValue(float64(Error_ErrBankResponseMessage)), // 9706
		},
	})

	ErrBankResponseMessage = errors.GoErr{
		ErrType:    errors.ErrorTypeBadRequest, // "ErrorTypeBadRequest"
		Status:     statusErrBankResponseMessage,
		Code:       int(Error_ErrBankResponseMessage), // 9706
		Message:    "failed to insert bank response message",
		GrpcCode:   codes.InvalidArgument,
		IsExpected: true,
	}
)

var (
	statusErrGetBankStatus, _ = status.New(
		codes.InvalidArgument,
		"failed to get bank status",
	).WithDetails(&structpb.Struct{
		Fields: map[string]*structpb.Value{
			"GrpcCode":   structpb.NewNumberValue(float64(codes.InvalidArgument)),
			"Message":    structpb.NewStringValue("failed to get bank status"),
			"ErrorType":  structpb.NewStringValue(errors.ErrorTypeBadRequest), // "ErrorTypeBadRequest"
			"IsExpected": structpb.NewBoolValue(true),
			"Code":       structpb.NewNumberValue(float64(Error_ErrGetBankStatus)), // 9707
		},
	})

	ErrGetBankStatus = errors.GoErr{
		ErrType:    errors.ErrorTypeBadRequest, // "ErrorTypeBadRequest"
		Status:     statusErrGetBankStatus,
		Code:       int(Error_ErrGetBankStatus), // 9707
		Message:    "failed to get bank status",
		GrpcCode:   codes.InvalidArgument,
		IsExpected: true,
	}
)

var (
	statusErrFileNotFound, _ = status.New(
		codes.NotFound,
		"file not found",
	).WithDetails(&structpb.Struct{
		Fields: map[string]*structpb.Value{
			"GrpcCode":   structpb.NewNumberValue(float64(codes.NotFound)),
			"Message":    structpb.NewStringValue("file not found"),
			"ErrorType":  structpb.NewStringValue(errors.ErrorTypeNotFound), // "ErrorTypeNotFound"
			"IsExpected": structpb.NewBoolValue(true),
			"Code":       structpb.NewNumberValue(float64(Error_ErrFileNotFound)), // 8102
		},
	})

	ErrFileNotFound = errors.GoErr{
		ErrType:    errors.ErrorTypeNotFound, // "ErrorTypeNotFound"
		Status:     statusErrFileNotFound,
		Code:       int(Error_ErrFileNotFound), // 8102
		Message:    "file not found",
		GrpcCode:   codes.NotFound,
		IsExpected: true,
	}
)

var (
	statusErrInvalidQueryParam, _ = status.New(
		codes.InvalidArgument,
		"invalid query param",
	).WithDetails(&structpb.Struct{
		Fields: map[string]*structpb.Value{
			"GrpcCode":   structpb.NewNumberValue(float64(codes.InvalidArgument)),
			"Message":    structpb.NewStringValue("invalid query param"),
			"ErrorType":  structpb.NewStringValue(errors.ErrorTypeBadRequest), // "ErrorTypeBadRequest"
			"IsExpected": structpb.NewBoolValue(true),
			"Code":       structpb.NewNumberValue(float64(Error_ErrInvalidQueryParam)), // 1017
		},
	})

	ErrInvalidQueryParam = errors.GoErr{
		ErrType:    errors.ErrorTypeBadRequest, // "ErrorTypeBadRequest"
		Status:     statusErrInvalidQueryParam,
		Code:       int(Error_ErrInvalidQueryParam), // 1017
		Message:    "invalid query param",
		GrpcCode:   codes.InvalidArgument,
		IsExpected: true,
	}
)

var (
	statusErrInvalidPathParam, _ = status.New(
		codes.InvalidArgument,
		"invalid path param",
	).WithDetails(&structpb.Struct{
		Fields: map[string]*structpb.Value{
			"GrpcCode":   structpb.NewNumberValue(float64(codes.InvalidArgument)),
			"Message":    structpb.NewStringValue("invalid path param"),
			"ErrorType":  structpb.NewStringValue(errors.ErrorTypeBadRequest), // "ErrorTypeBadRequest"
			"IsExpected": structpb.NewBoolValue(true),
			"Code":       structpb.NewNumberValue(float64(Error_ErrInvalidPathParam)), // 1018
		},
	})

	ErrInvalidPathParam = errors.GoErr{
		ErrType:    errors.ErrorTypeBadRequest, // "ErrorTypeBadRequest"
		Status:     statusErrInvalidPathParam,
		Code:       int(Error_ErrInvalidPathParam), // 1018
		Message:    "invalid path param",
		GrpcCode:   codes.InvalidArgument,
		IsExpected: true,
	}
)

var (
	statusErrParseErrorBody, _ = status.New(
		codes.InvalidArgument,
		"can't parse request body",
	).WithDetails(&structpb.Struct{
		Fields: map[string]*structpb.Value{
			"GrpcCode":   structpb.NewNumberValue(float64(codes.InvalidArgument)),
			"Message":    structpb.NewStringValue("can't parse request body"),
			"ErrorType":  structpb.NewStringValue(errors.ErrorTypeBadRequest), // "ErrorTypeBadRequest"
			"IsExpected": structpb.NewBoolValue(true),
			"Code":       structpb.NewNumberValue(float64(Error_ErrParseErrorBody)), // 1019
		},
	})

	ErrParseErrorBody = errors.GoErr{
		ErrType:    errors.ErrorTypeBadRequest, // "ErrorTypeBadRequest"
		Status:     statusErrParseErrorBody,
		Code:       int(Error_ErrParseErrorBody), // 1019
		Message:    "can't parse request body",
		GrpcCode:   codes.InvalidArgument,
		IsExpected: true,
	}
)

var (
	statusErrLimitReached, _ = status.New(
		codes.InvalidArgument,
		"limit exceeded",
	).WithDetails(&structpb.Struct{
		Fields: map[string]*structpb.Value{
			"GrpcCode":   structpb.NewNumberValue(float64(codes.InvalidArgument)),
			"Message":    structpb.NewStringValue("limit exceeded"),
			"ErrorType":  structpb.NewStringValue(errors.ErrorTypeBadRequest), // "ErrorTypeBadRequest"
			"IsExpected": structpb.NewBoolValue(true),
			"Code":       structpb.NewNumberValue(float64(Error_ErrLimitReached)), // 1020
		},
	})

	ErrLimitReached = errors.GoErr{
		ErrType:    errors.ErrorTypeBadRequest, // "ErrorTypeBadRequest"
		Status:     statusErrLimitReached,
		Code:       int(Error_ErrLimitReached), // 1020
		Message:    "limit exceeded",
		GrpcCode:   codes.InvalidArgument,
		IsExpected: true,
	}
)

var (
	statusErrRequestValidation, _ = status.New(
		codes.InvalidArgument,
		"request validation error",
	).WithDetails(&structpb.Struct{
		Fields: map[string]*structpb.Value{
			"GrpcCode":   structpb.NewNumberValue(float64(codes.InvalidArgument)),
			"Message":    structpb.NewStringValue("request validation error"),
			"ErrorType":  structpb.NewStringValue(errors.ErrorTypeBadRequest), // "ErrorTypeBadRequest"
			"IsExpected": structpb.NewBoolValue(true),
			"Code":       structpb.NewNumberValue(float64(Error_ErrRequestValidation)), // 1021
		},
	})

	ErrRequestValidation = errors.GoErr{
		ErrType:    errors.ErrorTypeBadRequest, // "ErrorTypeBadRequest"
		Status:     statusErrRequestValidation,
		Code:       int(Error_ErrRequestValidation), // 1021
		Message:    "request validation error",
		GrpcCode:   codes.InvalidArgument,
		IsExpected: true,
	}
)

var (
	statusErrParseResponseBody, _ = status.New(
		codes.Unknown,
		"couldn't parse response body",
	).WithDetails(&structpb.Struct{
		Fields: map[string]*structpb.Value{
			"GrpcCode":   structpb.NewNumberValue(float64(codes.Unknown)),
			"Message":    structpb.NewStringValue("couldn't parse response body"),
			"ErrorType":  structpb.NewStringValue(errors.ErrorTypeUnexpectedError), // "ErrorTypeUnexpectedError"
			"IsExpected": structpb.NewBoolValue(true),
			"Code":       structpb.NewNumberValue(float64(Error_ErrParseResponseBody)), // 1022
		},
	})

	ErrParseResponseBody = errors.GoErr{
		ErrType:    errors.ErrorTypeUnexpectedError, // "ErrorTypeUnexpectedError"
		Status:     statusErrParseResponseBody,
		Code:       int(Error_ErrParseResponseBody), // 1022
		Message:    "couldn't parse response body",
		GrpcCode:   codes.Unknown,
		IsExpected: true,
	}
)

var (
	statusErrEndDateRequired, _ = status.New(
		codes.InvalidArgument,
		"end_date is required",
	).WithDetails(&structpb.Struct{
		Fields: map[string]*structpb.Value{
			"GrpcCode":   structpb.NewNumberValue(float64(codes.InvalidArgument)),
			"Message":    structpb.NewStringValue("end_date is required"),
			"ErrorType":  structpb.NewStringValue(errors.ErrorTypeBadRequest), // "ErrorTypeBadRequest"
			"IsExpected": structpb.NewBoolValue(true),
			"Code":       structpb.NewNumberValue(float64(Error_ErrEndDateRequired)), // 1003
		},
	})

	ErrEndDateRequired = errors.GoErr{
		ErrType:    errors.ErrorTypeBadRequest, // "ErrorTypeBadRequest"
		Status:     statusErrEndDateRequired,
		Code:       int(Error_ErrEndDateRequired), // 1003
		Message:    "end_date is required",
		GrpcCode:   codes.InvalidArgument,
		IsExpected: true,
	}
)

var (
	statusErrStartDateRequired, _ = status.New(
		codes.InvalidArgument,
		"start_date is required",
	).WithDetails(&structpb.Struct{
		Fields: map[string]*structpb.Value{
			"GrpcCode":   structpb.NewNumberValue(float64(codes.InvalidArgument)),
			"Message":    structpb.NewStringValue("start_date is required"),
			"ErrorType":  structpb.NewStringValue(errors.ErrorTypeBadRequest), // "ErrorTypeBadRequest"
			"IsExpected": structpb.NewBoolValue(true),
			"Code":       structpb.NewNumberValue(float64(Error_ErrStartDateRequired)), // 1002
		},
	})

	ErrStartDateRequired = errors.GoErr{
		ErrType:    errors.ErrorTypeBadRequest, // "ErrorTypeBadRequest"
		Status:     statusErrStartDateRequired,
		Code:       int(Error_ErrStartDateRequired), // 1002
		Message:    "start_date is required",
		GrpcCode:   codes.InvalidArgument,
		IsExpected: true,
	}
)

var (
	statusErrParametersRequired, _ = status.New(
		codes.InvalidArgument,
		"parameters are required",
	).WithDetails(&structpb.Struct{
		Fields: map[string]*structpb.Value{
			"GrpcCode":   structpb.NewNumberValue(float64(codes.InvalidArgument)),
			"Message":    structpb.NewStringValue("parameters are required"),
			"ErrorType":  structpb.NewStringValue(errors.ErrorTypeBadRequest), // "ErrorTypeBadRequest"
			"IsExpected": structpb.NewBoolValue(true),
			"Code":       structpb.NewNumberValue(float64(Error_ErrParametersRequired)), // 1001
		},
	})

	ErrParametersRequired = errors.GoErr{
		ErrType:    errors.ErrorTypeBadRequest, // "ErrorTypeBadRequest"
		Status:     statusErrParametersRequired,
		Code:       int(Error_ErrParametersRequired), // 1001
		Message:    "parameters are required",
		GrpcCode:   codes.InvalidArgument,
		IsExpected: true,
	}
)

var (
	statusErrExceededLimitNumber, _ = status.New(
		codes.InvalidArgument,
		"limit can't be more than 20",
	).WithDetails(&structpb.Struct{
		Fields: map[string]*structpb.Value{
			"GrpcCode":   structpb.NewNumberValue(float64(codes.InvalidArgument)),
			"Message":    structpb.NewStringValue("limit can't be more than 20"),
			"ErrorType":  structpb.NewStringValue(errors.ErrorTypeBadRequest), // "ErrorTypeBadRequest"
			"IsExpected": structpb.NewBoolValue(true),
			"Code":       structpb.NewNumberValue(float64(Error_ErrExceededLimitNumber)), // 1004
		},
	})

	ErrExceededLimitNumber = errors.GoErr{
		ErrType:    errors.ErrorTypeBadRequest, // "ErrorTypeBadRequest"
		Status:     statusErrExceededLimitNumber,
		Code:       int(Error_ErrExceededLimitNumber), // 1004
		Message:    "limit can't be more than 20",
		GrpcCode:   codes.InvalidArgument,
		IsExpected: true,
	}
)

var (
	statusErrTimeCompareValidation, _ = status.New(
		codes.InvalidArgument,
		"start date must comes before the end date",
	).WithDetails(&structpb.Struct{
		Fields: map[string]*structpb.Value{
			"GrpcCode":   structpb.NewNumberValue(float64(codes.InvalidArgument)),
			"Message":    structpb.NewStringValue("start date must comes before the end date"),
			"ErrorType":  structpb.NewStringValue(errors.ErrorTypeBadRequest), // "ErrorTypeBadRequest"
			"IsExpected": structpb.NewBoolValue(true),
			"Code":       structpb.NewNumberValue(float64(Error_ErrTimeCompareValidation)), // 1007
		},
	})

	ErrTimeCompareValidation = errors.GoErr{
		ErrType:    errors.ErrorTypeBadRequest, // "ErrorTypeBadRequest"
		Status:     statusErrTimeCompareValidation,
		Code:       int(Error_ErrTimeCompareValidation), // 1007
		Message:    "start date must comes before the end date",
		GrpcCode:   codes.InvalidArgument,
		IsExpected: true,
	}
)

var (
	statusErrTimeRangeValidation, _ = status.New(
		codes.InvalidArgument,
		"time range can not be more than 2 hours",
	).WithDetails(&structpb.Struct{
		Fields: map[string]*structpb.Value{
			"GrpcCode":   structpb.NewNumberValue(float64(codes.InvalidArgument)),
			"Message":    structpb.NewStringValue("time range can not be more than 2 hours"),
			"ErrorType":  structpb.NewStringValue(errors.ErrorTypeBadRequest), // "ErrorTypeBadRequest"
			"IsExpected": structpb.NewBoolValue(true),
			"Code":       structpb.NewNumberValue(float64(Error_ErrTimeRangeValidation)), // 1006
		},
	})

	ErrTimeRangeValidation = errors.GoErr{
		ErrType:    errors.ErrorTypeBadRequest, // "ErrorTypeBadRequest"
		Status:     statusErrTimeRangeValidation,
		Code:       int(Error_ErrTimeRangeValidation), // 1006
		Message:    "time range can not be more than 2 hours",
		GrpcCode:   codes.InvalidArgument,
		IsExpected: true,
	}
)

var (
	statusErrInvalidRequestParam, _ = status.New(
		codes.InvalidArgument,
		"invalid request parameters",
	).WithDetails(&structpb.Struct{
		Fields: map[string]*structpb.Value{
			"GrpcCode":   structpb.NewNumberValue(float64(codes.InvalidArgument)),
			"Message":    structpb.NewStringValue("invalid request parameters"),
			"ErrorType":  structpb.NewStringValue(errors.ErrorTypeBadRequest), // "ErrorTypeBadRequest"
			"IsExpected": structpb.NewBoolValue(true),
			"Code":       structpb.NewNumberValue(float64(Error_ErrInvalidRequestParam)), // 1409
		},
	})

	ErrInvalidRequestParam = errors.GoErr{
		ErrType:    errors.ErrorTypeBadRequest, // "ErrorTypeBadRequest"
		Status:     statusErrInvalidRequestParam,
		Code:       int(Error_ErrInvalidRequestParam), // 1409
		Message:    "invalid request parameters",
		GrpcCode:   codes.InvalidArgument,
		IsExpected: true,
	}
)

var (
	statusErrSourceCodeEnum, _ = status.New(
		codes.InvalidArgument,
		"source code unexpected value",
	).WithDetails(&structpb.Struct{
		Fields: map[string]*structpb.Value{
			"GrpcCode":   structpb.NewNumberValue(float64(codes.InvalidArgument)),
			"Message":    structpb.NewStringValue("source code unexpected value"),
			"ErrorType":  structpb.NewStringValue(errors.ErrorTypeBadRequest), // "ErrorTypeBadRequest"
			"IsExpected": structpb.NewBoolValue(true),
			"Code":       structpb.NewNumberValue(float64(Error_ErrSourceCodeEnum)), // 8050
		},
	})

	ErrSourceCodeEnum = errors.GoErr{
		ErrType:    errors.ErrorTypeBadRequest, // "ErrorTypeBadRequest"
		Status:     statusErrSourceCodeEnum,
		Code:       int(Error_ErrSourceCodeEnum), // 8050
		Message:    "source code unexpected value",
		GrpcCode:   codes.InvalidArgument,
		IsExpected: true,
	}
)

var (
	statusErrPayIDRequired, _ = status.New(
		codes.InvalidArgument,
		"pay_id is required",
	).WithDetails(&structpb.Struct{
		Fields: map[string]*structpb.Value{
			"GrpcCode":   structpb.NewNumberValue(float64(codes.InvalidArgument)),
			"Message":    structpb.NewStringValue("pay_id is required"),
			"ErrorType":  structpb.NewStringValue(errors.ErrorTypeBadRequest), // "ErrorTypeBadRequest"
			"IsExpected": structpb.NewBoolValue(true),
			"Code":       structpb.NewNumberValue(float64(Error_ErrPayIDRequired)), // 8051
		},
	})

	ErrPayIDRequired = errors.GoErr{
		ErrType:    errors.ErrorTypeBadRequest, // "ErrorTypeBadRequest"
		Status:     statusErrPayIDRequired,
		Code:       int(Error_ErrPayIDRequired), // 8051
		Message:    "pay_id is required",
		GrpcCode:   codes.InvalidArgument,
		IsExpected: true,
	}
)

var (
	statusErrPaymentsRequired, _ = status.New(
		codes.InvalidArgument,
		"payments is required",
	).WithDetails(&structpb.Struct{
		Fields: map[string]*structpb.Value{
			"GrpcCode":   structpb.NewNumberValue(float64(codes.InvalidArgument)),
			"Message":    structpb.NewStringValue("payments is required"),
			"ErrorType":  structpb.NewStringValue(errors.ErrorTypeBadRequest), // "ErrorTypeBadRequest"
			"IsExpected": structpb.NewBoolValue(true),
			"Code":       structpb.NewNumberValue(float64(Error_ErrPaymentsRequired)), // 8052
		},
	})

	ErrPaymentsRequired = errors.GoErr{
		ErrType:    errors.ErrorTypeBadRequest, // "ErrorTypeBadRequest"
		Status:     statusErrPaymentsRequired,
		Code:       int(Error_ErrPaymentsRequired), // 8052
		Message:    "payments is required",
		GrpcCode:   codes.InvalidArgument,
		IsExpected: true,
	}
)

var (
	statusErrCodeRequired, _ = status.New(
		codes.InvalidArgument,
		"code is required",
	).WithDetails(&structpb.Struct{
		Fields: map[string]*structpb.Value{
			"GrpcCode":   structpb.NewNumberValue(float64(codes.InvalidArgument)),
			"Message":    structpb.NewStringValue("code is required"),
			"ErrorType":  structpb.NewStringValue(errors.ErrorTypeBadRequest), // "ErrorTypeBadRequest"
			"IsExpected": structpb.NewBoolValue(true),
			"Code":       structpb.NewNumberValue(float64(Error_ErrCodeRequired)), // 8053
		},
	})

	ErrCodeRequired = errors.GoErr{
		ErrType:    errors.ErrorTypeBadRequest, // "ErrorTypeBadRequest"
		Status:     statusErrCodeRequired,
		Code:       int(Error_ErrCodeRequired), // 8053
		Message:    "code is required",
		GrpcCode:   codes.InvalidArgument,
		IsExpected: true,
	}
)

var (
	statusErrQuantityIsNegative, _ = status.New(
		codes.InvalidArgument,
		"quantity can't be lower than 1",
	).WithDetails(&structpb.Struct{
		Fields: map[string]*structpb.Value{
			"GrpcCode":   structpb.NewNumberValue(float64(codes.InvalidArgument)),
			"Message":    structpb.NewStringValue("quantity can't be lower than 1"),
			"ErrorType":  structpb.NewStringValue(errors.ErrorTypeBadRequest), // "ErrorTypeBadRequest"
			"IsExpected": structpb.NewBoolValue(true),
			"Code":       structpb.NewNumberValue(float64(Error_ErrQuantityIsNegative)), // 8054
		},
	})

	ErrQuantityIsNegative = errors.GoErr{
		ErrType:    errors.ErrorTypeBadRequest, // "ErrorTypeBadRequest"
		Status:     statusErrQuantityIsNegative,
		Code:       int(Error_ErrQuantityIsNegative), // 8054
		Message:    "quantity can't be lower than 1",
		GrpcCode:   codes.InvalidArgument,
		IsExpected: true,
	}
)

var (
	statusErrEmailRequired, _ = status.New(
		codes.InvalidArgument,
		"email is required",
	).WithDetails(&structpb.Struct{
		Fields: map[string]*structpb.Value{
			"GrpcCode":   structpb.NewNumberValue(float64(codes.InvalidArgument)),
			"Message":    structpb.NewStringValue("email is required"),
			"ErrorType":  structpb.NewStringValue(errors.ErrorTypeBadRequest), // "ErrorTypeBadRequest"
			"IsExpected": structpb.NewBoolValue(true),
			"Code":       structpb.NewNumberValue(float64(Error_ErrEmailRequired)), // 8055
		},
	})

	ErrEmailRequired = errors.GoErr{
		ErrType:    errors.ErrorTypeBadRequest, // "ErrorTypeBadRequest"
		Status:     statusErrEmailRequired,
		Code:       int(Error_ErrEmailRequired), // 8055
		Message:    "email is required",
		GrpcCode:   codes.InvalidArgument,
		IsExpected: true,
	}
)

var (
	statusErrPasswordRequired, _ = status.New(
		codes.InvalidArgument,
		"pass is required",
	).WithDetails(&structpb.Struct{
		Fields: map[string]*structpb.Value{
			"GrpcCode":   structpb.NewNumberValue(float64(codes.InvalidArgument)),
			"Message":    structpb.NewStringValue("pass is required"),
			"ErrorType":  structpb.NewStringValue(errors.ErrorTypeBadRequest), // "ErrorTypeBadRequest"
			"IsExpected": structpb.NewBoolValue(true),
			"Code":       structpb.NewNumberValue(float64(Error_ErrPasswordRequired)), // 8056
		},
	})

	ErrPasswordRequired = errors.GoErr{
		ErrType:    errors.ErrorTypeBadRequest, // "ErrorTypeBadRequest"
		Status:     statusErrPasswordRequired,
		Code:       int(Error_ErrPasswordRequired), // 8056
		Message:    "pass is required",
		GrpcCode:   codes.InvalidArgument,
		IsExpected: true,
	}
)

var (
	statusErrCreatingMerchant, _ = status.New(
		codes.InvalidArgument,
		"create merchant failed",
	).WithDetails(&structpb.Struct{
		Fields: map[string]*structpb.Value{
			"GrpcCode":   structpb.NewNumberValue(float64(codes.InvalidArgument)),
			"Message":    structpb.NewStringValue("create merchant failed"),
			"ErrorType":  structpb.NewStringValue(errors.ErrorTypeBadRequest), // "ErrorTypeBadRequest"
			"IsExpected": structpb.NewBoolValue(true),
			"Code":       structpb.NewNumberValue(float64(Error_ErrCreatingMerchant)), // 8057
		},
	})

	ErrCreatingMerchant = errors.GoErr{
		ErrType:    errors.ErrorTypeBadRequest, // "ErrorTypeBadRequest"
		Status:     statusErrCreatingMerchant,
		Code:       int(Error_ErrCreatingMerchant), // 8057
		Message:    "create merchant failed",
		GrpcCode:   codes.InvalidArgument,
		IsExpected: true,
	}
)

var (
	statusErrPasswordsDontMatch, _ = status.New(
		codes.InvalidArgument,
		"password is not matching",
	).WithDetails(&structpb.Struct{
		Fields: map[string]*structpb.Value{
			"GrpcCode":   structpb.NewNumberValue(float64(codes.InvalidArgument)),
			"Message":    structpb.NewStringValue("password is not matching"),
			"ErrorType":  structpb.NewStringValue(errors.ErrorTypeBadRequest), // "ErrorTypeBadRequest"
			"IsExpected": structpb.NewBoolValue(true),
			"Code":       structpb.NewNumberValue(float64(Error_ErrPasswordsDontMatch)), // 8059
		},
	})

	ErrPasswordsDontMatch = errors.GoErr{
		ErrType:    errors.ErrorTypeBadRequest, // "ErrorTypeBadRequest"
		Status:     statusErrPasswordsDontMatch,
		Code:       int(Error_ErrPasswordsDontMatch), // 8059
		Message:    "password is not matching",
		GrpcCode:   codes.InvalidArgument,
		IsExpected: true,
	}
)

var (
	statusErrEndBeforeStartDate, _ = status.New(
		codes.InvalidArgument,
		"end date is before start date",
	).WithDetails(&structpb.Struct{
		Fields: map[string]*structpb.Value{
			"GrpcCode":   structpb.NewNumberValue(float64(codes.InvalidArgument)),
			"Message":    structpb.NewStringValue("end date is before start date"),
			"ErrorType":  structpb.NewStringValue(errors.ErrorTypeBadRequest), // "ErrorTypeBadRequest"
			"IsExpected": structpb.NewBoolValue(true),
			"Code":       structpb.NewNumberValue(float64(Error_ErrEndBeforeStartDate)), // 8060
		},
	})

	ErrEndBeforeStartDate = errors.GoErr{
		ErrType:    errors.ErrorTypeBadRequest, // "ErrorTypeBadRequest"
		Status:     statusErrEndBeforeStartDate,
		Code:       int(Error_ErrEndBeforeStartDate), // 8060
		Message:    "end date is before start date",
		GrpcCode:   codes.InvalidArgument,
		IsExpected: true,
	}
)

var (
	statusErrNameRequired, _ = status.New(
		codes.InvalidArgument,
		"name is required",
	).WithDetails(&structpb.Struct{
		Fields: map[string]*structpb.Value{
			"GrpcCode":   structpb.NewNumberValue(float64(codes.InvalidArgument)),
			"Message":    structpb.NewStringValue("name is required"),
			"ErrorType":  structpb.NewStringValue(errors.ErrorTypeBadRequest), // "ErrorTypeBadRequest"
			"IsExpected": structpb.NewBoolValue(true),
			"Code":       structpb.NewNumberValue(float64(Error_ErrNameRequired)), // 8061
		},
	})

	ErrNameRequired = errors.GoErr{
		ErrType:    errors.ErrorTypeBadRequest, // "ErrorTypeBadRequest"
		Status:     statusErrNameRequired,
		Code:       int(Error_ErrNameRequired), // 8061
		Message:    "name is required",
		GrpcCode:   codes.InvalidArgument,
		IsExpected: true,
	}
)

var (
	statusErrBinRequired, _ = status.New(
		codes.InvalidArgument,
		"bin is required",
	).WithDetails(&structpb.Struct{
		Fields: map[string]*structpb.Value{
			"GrpcCode":   structpb.NewNumberValue(float64(codes.InvalidArgument)),
			"Message":    structpb.NewStringValue("bin is required"),
			"ErrorType":  structpb.NewStringValue(errors.ErrorTypeBadRequest), // "ErrorTypeBadRequest"
			"IsExpected": structpb.NewBoolValue(true),
			"Code":       structpb.NewNumberValue(float64(Error_ErrBinRequired)), // 8062
		},
	})

	ErrBinRequired = errors.GoErr{
		ErrType:    errors.ErrorTypeBadRequest, // "ErrorTypeBadRequest"
		Status:     statusErrBinRequired,
		Code:       int(Error_ErrBinRequired), // 8062
		Message:    "bin is required",
		GrpcCode:   codes.InvalidArgument,
		IsExpected: true,
	}
)

var (
	statusErrIsActiveRequired, _ = status.New(
		codes.InvalidArgument,
		"is_active is required",
	).WithDetails(&structpb.Struct{
		Fields: map[string]*structpb.Value{
			"GrpcCode":   structpb.NewNumberValue(float64(codes.InvalidArgument)),
			"Message":    structpb.NewStringValue("is_active is required"),
			"ErrorType":  structpb.NewStringValue(errors.ErrorTypeBadRequest), // "ErrorTypeBadRequest"
			"IsExpected": structpb.NewBoolValue(true),
			"Code":       structpb.NewNumberValue(float64(Error_ErrIsActiveRequired)), // 8063
		},
	})

	ErrIsActiveRequired = errors.GoErr{
		ErrType:    errors.ErrorTypeBadRequest, // "ErrorTypeBadRequest"
		Status:     statusErrIsActiveRequired,
		Code:       int(Error_ErrIsActiveRequired), // 8063
		Message:    "is_active is required",
		GrpcCode:   codes.InvalidArgument,
		IsExpected: true,
	}
)

var (
	statusErrBankRequired, _ = status.New(
		codes.InvalidArgument,
		"bank is required",
	).WithDetails(&structpb.Struct{
		Fields: map[string]*structpb.Value{
			"GrpcCode":   structpb.NewNumberValue(float64(codes.InvalidArgument)),
			"Message":    structpb.NewStringValue("bank is required"),
			"ErrorType":  structpb.NewStringValue(errors.ErrorTypeBadRequest), // "ErrorTypeBadRequest"
			"IsExpected": structpb.NewBoolValue(true),
			"Code":       structpb.NewNumberValue(float64(Error_ErrBankRequired)), // 8064
		},
	})

	ErrBankRequired = errors.GoErr{
		ErrType:    errors.ErrorTypeBadRequest, // "ErrorTypeBadRequest"
		Status:     statusErrBankRequired,
		Code:       int(Error_ErrBankRequired), // 8064
		Message:    "bank is required",
		GrpcCode:   codes.InvalidArgument,
		IsExpected: true,
	}
)

var (
	statusErrMerchantRequired, _ = status.New(
		codes.InvalidArgument,
		"merchant is required",
	).WithDetails(&structpb.Struct{
		Fields: map[string]*structpb.Value{
			"GrpcCode":   structpb.NewNumberValue(float64(codes.InvalidArgument)),
			"Message":    structpb.NewStringValue("merchant is required"),
			"ErrorType":  structpb.NewStringValue(errors.ErrorTypeBadRequest), // "ErrorTypeBadRequest"
			"IsExpected": structpb.NewBoolValue(true),
			"Code":       structpb.NewNumberValue(float64(Error_ErrMerchantRequired)), // 8065
		},
	})

	ErrMerchantRequired = errors.GoErr{
		ErrType:    errors.ErrorTypeBadRequest, // "ErrorTypeBadRequest"
		Status:     statusErrMerchantRequired,
		Code:       int(Error_ErrMerchantRequired), // 8065
		Message:    "merchant is required",
		GrpcCode:   codes.InvalidArgument,
		IsExpected: true,
	}
)

var (
	statusErrAccountNumberRequired, _ = status.New(
		codes.InvalidArgument,
		"account number is required",
	).WithDetails(&structpb.Struct{
		Fields: map[string]*structpb.Value{
			"GrpcCode":   structpb.NewNumberValue(float64(codes.InvalidArgument)),
			"Message":    structpb.NewStringValue("account number is required"),
			"ErrorType":  structpb.NewStringValue(errors.ErrorTypeBadRequest), // "ErrorTypeBadRequest"
			"IsExpected": structpb.NewBoolValue(true),
			"Code":       structpb.NewNumberValue(float64(Error_ErrAccountNumberRequired)), // 8066
		},
	})

	ErrAccountNumberRequired = errors.GoErr{
		ErrType:    errors.ErrorTypeBadRequest, // "ErrorTypeBadRequest"
		Status:     statusErrAccountNumberRequired,
		Code:       int(Error_ErrAccountNumberRequired), // 8066
		Message:    "account number is required",
		GrpcCode:   codes.InvalidArgument,
		IsExpected: true,
	}
)

var (
	statusErrIicRequired, _ = status.New(
		codes.InvalidArgument,
		"iic is required",
	).WithDetails(&structpb.Struct{
		Fields: map[string]*structpb.Value{
			"GrpcCode":   structpb.NewNumberValue(float64(codes.InvalidArgument)),
			"Message":    structpb.NewStringValue("iic is required"),
			"ErrorType":  structpb.NewStringValue(errors.ErrorTypeBadRequest), // "ErrorTypeBadRequest"
			"IsExpected": structpb.NewBoolValue(true),
			"Code":       structpb.NewNumberValue(float64(Error_ErrIicRequired)), // 8068
		},
	})

	ErrIicRequired = errors.GoErr{
		ErrType:    errors.ErrorTypeBadRequest, // "ErrorTypeBadRequest"
		Status:     statusErrIicRequired,
		Code:       int(Error_ErrIicRequired), // 8068
		Message:    "iic is required",
		GrpcCode:   codes.InvalidArgument,
		IsExpected: true,
	}
)

var (
	statusErrBaseRuleNotFound, _ = status.New(
		codes.NotFound,
		"base rule not found",
	).WithDetails(&structpb.Struct{
		Fields: map[string]*structpb.Value{
			"GrpcCode":   structpb.NewNumberValue(float64(codes.NotFound)),
			"Message":    structpb.NewStringValue("base rule not found"),
			"ErrorType":  structpb.NewStringValue(errors.ErrorTypeNotFound), // "ErrorTypeNotFound"
			"IsExpected": structpb.NewBoolValue(true),
			"Code":       structpb.NewNumberValue(float64(Error_ErrBaseRuleNotFound)), // 3003
		},
	})

	ErrBaseRuleNotFound = errors.GoErr{
		ErrType:    errors.ErrorTypeNotFound, // "ErrorTypeNotFound"
		Status:     statusErrBaseRuleNotFound,
		Code:       int(Error_ErrBaseRuleNotFound), // 3003
		Message:    "base rule not found",
		GrpcCode:   codes.NotFound,
		IsExpected: true,
	}
)

var (
	statusErrBaseRuleAlreadyExists, _ = status.New(
		codes.InvalidArgument,
		"base rule already exists",
	).WithDetails(&structpb.Struct{
		Fields: map[string]*structpb.Value{
			"GrpcCode":   structpb.NewNumberValue(float64(codes.InvalidArgument)),
			"Message":    structpb.NewStringValue("base rule already exists"),
			"ErrorType":  structpb.NewStringValue(errors.ErrorTypeBadRequest), // "ErrorTypeBadRequest"
			"IsExpected": structpb.NewBoolValue(true),
			"Code":       structpb.NewNumberValue(float64(Error_ErrBaseRuleAlreadyExists)), // 3004
		},
	})

	ErrBaseRuleAlreadyExists = errors.GoErr{
		ErrType:    errors.ErrorTypeBadRequest, // "ErrorTypeBadRequest"
		Status:     statusErrBaseRuleAlreadyExists,
		Code:       int(Error_ErrBaseRuleAlreadyExists), // 3004
		Message:    "base rule already exists",
		GrpcCode:   codes.InvalidArgument,
		IsExpected: true,
	}
)

var (
	statusErrBaseRuleDeactivation, _ = status.New(
		codes.InvalidArgument,
		"base rule can not be deactivated",
	).WithDetails(&structpb.Struct{
		Fields: map[string]*structpb.Value{
			"GrpcCode":   structpb.NewNumberValue(float64(codes.InvalidArgument)),
			"Message":    structpb.NewStringValue("base rule can not be deactivated"),
			"ErrorType":  structpb.NewStringValue(errors.ErrorTypeBadRequest), // "ErrorTypeBadRequest"
			"IsExpected": structpb.NewBoolValue(true),
			"Code":       structpb.NewNumberValue(float64(Error_ErrBaseRuleDeactivation)), // 3005
		},
	})

	ErrBaseRuleDeactivation = errors.GoErr{
		ErrType:    errors.ErrorTypeBadRequest, // "ErrorTypeBadRequest"
		Status:     statusErrBaseRuleDeactivation,
		Code:       int(Error_ErrBaseRuleDeactivation), // 3005
		Message:    "base rule can not be deactivated",
		GrpcCode:   codes.InvalidArgument,
		IsExpected: true,
	}
)

var (
	statusErrRuleNotFound, _ = status.New(
		codes.NotFound,
		"rule not found",
	).WithDetails(&structpb.Struct{
		Fields: map[string]*structpb.Value{
			"GrpcCode":   structpb.NewNumberValue(float64(codes.NotFound)),
			"Message":    structpb.NewStringValue("rule not found"),
			"ErrorType":  structpb.NewStringValue(errors.ErrorTypeNotFound), // "ErrorTypeNotFound"
			"IsExpected": structpb.NewBoolValue(true),
			"Code":       structpb.NewNumberValue(float64(Error_ErrRuleNotFound)), // 3006
		},
	})

	ErrRuleNotFound = errors.GoErr{
		ErrType:    errors.ErrorTypeNotFound, // "ErrorTypeNotFound"
		Status:     statusErrRuleNotFound,
		Code:       int(Error_ErrRuleNotFound), // 3006
		Message:    "rule not found",
		GrpcCode:   codes.NotFound,
		IsExpected: true,
	}
)

var (
	statusErrRuleAlreadyExists, _ = status.New(
		codes.InvalidArgument,
		"rule already exists",
	).WithDetails(&structpb.Struct{
		Fields: map[string]*structpb.Value{
			"GrpcCode":   structpb.NewNumberValue(float64(codes.InvalidArgument)),
			"Message":    structpb.NewStringValue("rule already exists"),
			"ErrorType":  structpb.NewStringValue(errors.ErrorTypeBadRequest), // "ErrorTypeBadRequest"
			"IsExpected": structpb.NewBoolValue(true),
			"Code":       structpb.NewNumberValue(float64(Error_ErrRuleAlreadyExists)), // 3007
		},
	})

	ErrRuleAlreadyExists = errors.GoErr{
		ErrType:    errors.ErrorTypeBadRequest, // "ErrorTypeBadRequest"
		Status:     statusErrRuleAlreadyExists,
		Code:       int(Error_ErrRuleAlreadyExists), // 3007
		Message:    "rule already exists",
		GrpcCode:   codes.InvalidArgument,
		IsExpected: true,
	}
)

var (
	statusErrRuleWhileSearch, _ = status.New(
		codes.Unknown,
		"something went wrong while searching the rule",
	).WithDetails(&structpb.Struct{
		Fields: map[string]*structpb.Value{
			"GrpcCode":   structpb.NewNumberValue(float64(codes.Unknown)),
			"Message":    structpb.NewStringValue("something went wrong while searching the rule"),
			"ErrorType":  structpb.NewStringValue(errors.ErrorTypeUnexpectedError), // "ErrorTypeUnexpectedError"
			"IsExpected": structpb.NewBoolValue(true),
			"Code":       structpb.NewNumberValue(float64(Error_ErrRuleWhileSearch)), // 3008
		},
	})

	ErrRuleWhileSearch = errors.GoErr{
		ErrType:    errors.ErrorTypeUnexpectedError, // "ErrorTypeUnexpectedError"
		Status:     statusErrRuleWhileSearch,
		Code:       int(Error_ErrRuleWhileSearch), // 3008
		Message:    "something went wrong while searching the rule",
		GrpcCode:   codes.Unknown,
		IsExpected: true,
	}
)

var (
	statusErrNoPreviousRule, _ = status.New(
		codes.NotFound,
		"no previous rule",
	).WithDetails(&structpb.Struct{
		Fields: map[string]*structpb.Value{
			"GrpcCode":   structpb.NewNumberValue(float64(codes.NotFound)),
			"Message":    structpb.NewStringValue("no previous rule"),
			"ErrorType":  structpb.NewStringValue(errors.ErrorTypeNotFound), // "ErrorTypeNotFound"
			"IsExpected": structpb.NewBoolValue(true),
			"Code":       structpb.NewNumberValue(float64(Error_ErrNoPreviousRule)), // 3013
		},
	})

	ErrNoPreviousRule = errors.GoErr{
		ErrType:    errors.ErrorTypeNotFound, // "ErrorTypeNotFound"
		Status:     statusErrNoPreviousRule,
		Code:       int(Error_ErrNoPreviousRule), // 3013
		Message:    "no previous rule",
		GrpcCode:   codes.NotFound,
		IsExpected: true,
	}
)

var (
	statusErrNoNextRule, _ = status.New(
		codes.NotFound,
		"no next rule",
	).WithDetails(&structpb.Struct{
		Fields: map[string]*structpb.Value{
			"GrpcCode":   structpb.NewNumberValue(float64(codes.NotFound)),
			"Message":    structpb.NewStringValue("no next rule"),
			"ErrorType":  structpb.NewStringValue(errors.ErrorTypeNotFound), // "ErrorTypeNotFound"
			"IsExpected": structpb.NewBoolValue(true),
			"Code":       structpb.NewNumberValue(float64(Error_ErrNoNextRule)), // 3014
		},
	})

	ErrNoNextRule = errors.GoErr{
		ErrType:    errors.ErrorTypeNotFound, // "ErrorTypeNotFound"
		Status:     statusErrNoNextRule,
		Code:       int(Error_ErrNoNextRule), // 3014
		Message:    "no next rule",
		GrpcCode:   codes.NotFound,
		IsExpected: true,
	}
)

var (
	statusErrBaseRuleActivation, _ = status.New(
		codes.InvalidArgument,
		"base rule can not be activated",
	).WithDetails(&structpb.Struct{
		Fields: map[string]*structpb.Value{
			"GrpcCode":   structpb.NewNumberValue(float64(codes.InvalidArgument)),
			"Message":    structpb.NewStringValue("base rule can not be activated"),
			"ErrorType":  structpb.NewStringValue(errors.ErrorTypeBadRequest), // "ErrorTypeBadRequest"
			"IsExpected": structpb.NewBoolValue(true),
			"Code":       structpb.NewNumberValue(float64(Error_ErrBaseRuleActivation)), // 3015
		},
	})

	ErrBaseRuleActivation = errors.GoErr{
		ErrType:    errors.ErrorTypeBadRequest, // "ErrorTypeBadRequest"
		Status:     statusErrBaseRuleActivation,
		Code:       int(Error_ErrBaseRuleActivation), // 3015
		Message:    "base rule can not be activated",
		GrpcCode:   codes.InvalidArgument,
		IsExpected: true,
	}
)

var (
	statusErrGetTerminal, _ = status.New(
		codes.InvalidArgument,
		"failed to get terminal",
	).WithDetails(&structpb.Struct{
		Fields: map[string]*structpb.Value{
			"GrpcCode":   structpb.NewNumberValue(float64(codes.InvalidArgument)),
			"Message":    structpb.NewStringValue("failed to get terminal"),
			"ErrorType":  structpb.NewStringValue(errors.ErrorTypeBadRequest), // "ErrorTypeBadRequest"
			"IsExpected": structpb.NewBoolValue(true),
			"Code":       structpb.NewNumberValue(float64(Error_ErrGetTerminal)), // 9710
		},
	})

	ErrGetTerminal = errors.GoErr{
		ErrType:    errors.ErrorTypeBadRequest, // "ErrorTypeBadRequest"
		Status:     statusErrGetTerminal,
		Code:       int(Error_ErrGetTerminal), // 9710
		Message:    "failed to get terminal",
		GrpcCode:   codes.InvalidArgument,
		IsExpected: true,
	}
)

var (
	statusErrCheckTransactionTimeout, _ = status.New(
		codes.InvalidArgument,
		"failed to check transaction timeout",
	).WithDetails(&structpb.Struct{
		Fields: map[string]*structpb.Value{
			"GrpcCode":   structpb.NewNumberValue(float64(codes.InvalidArgument)),
			"Message":    structpb.NewStringValue("failed to check transaction timeout"),
			"ErrorType":  structpb.NewStringValue(errors.ErrorTypeBadRequest), // "ErrorTypeBadRequest"
			"IsExpected": structpb.NewBoolValue(true),
			"Code":       structpb.NewNumberValue(float64(Error_ErrCheckTransactionTimeout)), // 9704
		},
	})

	ErrCheckTransactionTimeout = errors.GoErr{
		ErrType:    errors.ErrorTypeBadRequest, // "ErrorTypeBadRequest"
		Status:     statusErrCheckTransactionTimeout,
		Code:       int(Error_ErrCheckTransactionTimeout), // 9704
		Message:    "failed to check transaction timeout",
		GrpcCode:   codes.InvalidArgument,
		IsExpected: true,
	}
)

var (
	statusErrProcessedByOtherGoRoutine, _ = status.New(
		codes.InvalidArgument,
		"transaction processed by other go routine",
	).WithDetails(&structpb.Struct{
		Fields: map[string]*structpb.Value{
			"GrpcCode":   structpb.NewNumberValue(float64(codes.InvalidArgument)),
			"Message":    structpb.NewStringValue("transaction processed by other go routine"),
			"ErrorType":  structpb.NewStringValue(errors.ErrorTypeBadRequest), // "ErrorTypeBadRequest"
			"IsExpected": structpb.NewBoolValue(true),
			"Code":       structpb.NewNumberValue(float64(Error_ErrProcessedByOtherGoRoutine)), // 9713
		},
	})

	ErrProcessedByOtherGoRoutine = errors.GoErr{
		ErrType:    errors.ErrorTypeBadRequest, // "ErrorTypeBadRequest"
		Status:     statusErrProcessedByOtherGoRoutine,
		Code:       int(Error_ErrProcessedByOtherGoRoutine), // 9713
		Message:    "transaction processed by other go routine",
		GrpcCode:   codes.InvalidArgument,
		IsExpected: true,
	}
)

var (
	statusErrTooEarlyToFinalize, _ = status.New(
		codes.InvalidArgument,
		"too early to finalize transaction",
	).WithDetails(&structpb.Struct{
		Fields: map[string]*structpb.Value{
			"GrpcCode":   structpb.NewNumberValue(float64(codes.InvalidArgument)),
			"Message":    structpb.NewStringValue("too early to finalize transaction"),
			"ErrorType":  structpb.NewStringValue(errors.ErrorTypeBadRequest), // "ErrorTypeBadRequest"
			"IsExpected": structpb.NewBoolValue(true),
			"Code":       structpb.NewNumberValue(float64(Error_ErrTooEarlyToFinalize)), // 9712
		},
	})

	ErrTooEarlyToFinalize = errors.GoErr{
		ErrType:    errors.ErrorTypeBadRequest, // "ErrorTypeBadRequest"
		Status:     statusErrTooEarlyToFinalize,
		Code:       int(Error_ErrTooEarlyToFinalize), // 9712
		Message:    "too early to finalize transaction",
		GrpcCode:   codes.InvalidArgument,
		IsExpected: true,
	}
)

var (
	statusErrAutoRedirectInfoNotFound, _ = status.New(
		codes.NotFound,
		"auto redirect info not found",
	).WithDetails(&structpb.Struct{
		Fields: map[string]*structpb.Value{
			"GrpcCode":   structpb.NewNumberValue(float64(codes.NotFound)),
			"Message":    structpb.NewStringValue("auto redirect info not found"),
			"ErrorType":  structpb.NewStringValue(errors.ErrorTypeNotFound), // "ErrorTypeNotFound"
			"IsExpected": structpb.NewBoolValue(true),
			"Code":       structpb.NewNumberValue(float64(Error_ErrAutoRedirectInfoNotFound)), // 8104
		},
	})

	ErrAutoRedirectInfoNotFound = errors.GoErr{
		ErrType:    errors.ErrorTypeNotFound, // "ErrorTypeNotFound"
		Status:     statusErrAutoRedirectInfoNotFound,
		Code:       int(Error_ErrAutoRedirectInfoNotFound), // 8104
		Message:    "auto redirect info not found",
		GrpcCode:   codes.NotFound,
		IsExpected: true,
	}
)

var (
	statusErrTerminalRefundSettingNotFound, _ = status.New(
		codes.NotFound,
		"terminal refund setting not found",
	).WithDetails(&structpb.Struct{
		Fields: map[string]*structpb.Value{
			"GrpcCode":   structpb.NewNumberValue(float64(codes.NotFound)),
			"Message":    structpb.NewStringValue("terminal refund setting not found"),
			"ErrorType":  structpb.NewStringValue(errors.ErrorTypeNotFound), // "ErrorTypeNotFound"
			"IsExpected": structpb.NewBoolValue(true),
			"Code":       structpb.NewNumberValue(float64(Error_ErrTerminalRefundSettingNotFound)), // 5110
		},
	})

	ErrTerminalRefundSettingNotFound = errors.GoErr{
		ErrType:    errors.ErrorTypeNotFound, // "ErrorTypeNotFound"
		Status:     statusErrTerminalRefundSettingNotFound,
		Code:       int(Error_ErrTerminalRefundSettingNotFound), // 5110
		Message:    "terminal refund setting not found",
		GrpcCode:   codes.NotFound,
		IsExpected: true,
	}
)

var (
	statusErrRefundReasonNotFound, _ = status.New(
		codes.NotFound,
		"refund reason not found",
	).WithDetails(&structpb.Struct{
		Fields: map[string]*structpb.Value{
			"GrpcCode":   structpb.NewNumberValue(float64(codes.NotFound)),
			"Message":    structpb.NewStringValue("refund reason not found"),
			"ErrorType":  structpb.NewStringValue(errors.ErrorTypeNotFound), // "ErrorTypeNotFound"
			"IsExpected": structpb.NewBoolValue(true),
			"Code":       structpb.NewNumberValue(float64(Error_ErrRefundReasonNotFound)), // 5111
		},
	})

	ErrRefundReasonNotFound = errors.GoErr{
		ErrType:    errors.ErrorTypeNotFound, // "ErrorTypeNotFound"
		Status:     statusErrRefundReasonNotFound,
		Code:       int(Error_ErrRefundReasonNotFound), // 5111
		Message:    "refund reason not found",
		GrpcCode:   codes.NotFound,
		IsExpected: true,
	}
)

var (
	statusErrOperationTypeNotFound, _ = status.New(
		codes.NotFound,
		"operation type not found",
	).WithDetails(&structpb.Struct{
		Fields: map[string]*structpb.Value{
			"GrpcCode":   structpb.NewNumberValue(float64(codes.NotFound)),
			"Message":    structpb.NewStringValue("operation type not found"),
			"ErrorType":  structpb.NewStringValue(errors.ErrorTypeNotFound), // "ErrorTypeNotFound"
			"IsExpected": structpb.NewBoolValue(true),
			"Code":       structpb.NewNumberValue(float64(Error_ErrOperationTypeNotFound)), // 5112
		},
	})

	ErrOperationTypeNotFound = errors.GoErr{
		ErrType:    errors.ErrorTypeNotFound, // "ErrorTypeNotFound"
		Status:     statusErrOperationTypeNotFound,
		Code:       int(Error_ErrOperationTypeNotFound), // 5112
		Message:    "operation type not found",
		GrpcCode:   codes.NotFound,
		IsExpected: true,
	}
)

var (
	statusErrOperationNotFound, _ = status.New(
		codes.NotFound,
		"operation not found",
	).WithDetails(&structpb.Struct{
		Fields: map[string]*structpb.Value{
			"GrpcCode":   structpb.NewNumberValue(float64(codes.NotFound)),
			"Message":    structpb.NewStringValue("operation not found"),
			"ErrorType":  structpb.NewStringValue(errors.ErrorTypeNotFound), // "ErrorTypeNotFound"
			"IsExpected": structpb.NewBoolValue(true),
			"Code":       structpb.NewNumberValue(float64(Error_ErrOperationNotFound)), // 5113
		},
	})

	ErrOperationNotFound = errors.GoErr{
		ErrType:    errors.ErrorTypeNotFound, // "ErrorTypeNotFound"
		Status:     statusErrOperationNotFound,
		Code:       int(Error_ErrOperationNotFound), // 5113
		Message:    "operation not found",
		GrpcCode:   codes.NotFound,
		IsExpected: true,
	}
)

var (
	statusErrProjectOrderNotFound, _ = status.New(
		codes.NotFound,
		"project order not found",
	).WithDetails(&structpb.Struct{
		Fields: map[string]*structpb.Value{
			"GrpcCode":   structpb.NewNumberValue(float64(codes.NotFound)),
			"Message":    structpb.NewStringValue("project order not found"),
			"ErrorType":  structpb.NewStringValue(errors.ErrorTypeNotFound), // "ErrorTypeNotFound"
			"IsExpected": structpb.NewBoolValue(true),
			"Code":       structpb.NewNumberValue(float64(Error_ErrProjectOrderNotFound)), // 5114
		},
	})

	ErrProjectOrderNotFound = errors.GoErr{
		ErrType:    errors.ErrorTypeNotFound, // "ErrorTypeNotFound"
		Status:     statusErrProjectOrderNotFound,
		Code:       int(Error_ErrProjectOrderNotFound), // 5114
		Message:    "project order not found",
		GrpcCode:   codes.NotFound,
		IsExpected: true,
	}
)

var (
	statusErrTransactionCallbackStatusNotFound, _ = status.New(
		codes.NotFound,
		"transaction callback status not found",
	).WithDetails(&structpb.Struct{
		Fields: map[string]*structpb.Value{
			"GrpcCode":   structpb.NewNumberValue(float64(codes.NotFound)),
			"Message":    structpb.NewStringValue("transaction callback status not found"),
			"ErrorType":  structpb.NewStringValue(errors.ErrorTypeNotFound), // "ErrorTypeNotFound"
			"IsExpected": structpb.NewBoolValue(true),
			"Code":       structpb.NewNumberValue(float64(Error_ErrTransactionCallbackStatusNotFound)), // 5115
		},
	})

	ErrTransactionCallbackStatusNotFound = errors.GoErr{
		ErrType:    errors.ErrorTypeNotFound, // "ErrorTypeNotFound"
		Status:     statusErrTransactionCallbackStatusNotFound,
		Code:       int(Error_ErrTransactionCallbackStatusNotFound), // 5115
		Message:    "transaction callback status not found",
		GrpcCode:   codes.NotFound,
		IsExpected: true,
	}
)

var (
	statusErrUnsuppportedTerminalType, _ = status.New(
		codes.InvalidArgument,
		"unsupported terminal type",
	).WithDetails(&structpb.Struct{
		Fields: map[string]*structpb.Value{
			"GrpcCode":   structpb.NewNumberValue(float64(codes.InvalidArgument)),
			"Message":    structpb.NewStringValue("unsupported terminal type"),
			"ErrorType":  structpb.NewStringValue(errors.ErrorTypeBadRequest), // "ErrorTypeBadRequest"
			"IsExpected": structpb.NewBoolValue(true),
			"Code":       structpb.NewNumberValue(float64(Error_ErrUnsuppportedTerminalType)), // 5116
		},
	})

	ErrUnsuppportedTerminalType = errors.GoErr{
		ErrType:    errors.ErrorTypeBadRequest, // "ErrorTypeBadRequest"
		Status:     statusErrUnsuppportedTerminalType,
		Code:       int(Error_ErrUnsuppportedTerminalType), // 5116
		Message:    "unsupported terminal type",
		GrpcCode:   codes.InvalidArgument,
		IsExpected: true,
	}
)

var (
	statusErrSetNewTransactionToCache, _ = status.New(
		codes.Unknown,
		"failed to set new transaction to cache",
	).WithDetails(&structpb.Struct{
		Fields: map[string]*structpb.Value{
			"GrpcCode":   structpb.NewNumberValue(float64(codes.Unknown)),
			"Message":    structpb.NewStringValue("failed to set new transaction to cache"),
			"ErrorType":  structpb.NewStringValue(errors.ErrorTypeUnexpectedError), // "ErrorTypeUnexpectedError"
			"IsExpected": structpb.NewBoolValue(true),
			"Code":       structpb.NewNumberValue(float64(Error_ErrSetNewTransactionToCache)), // 9702
		},
	})

	ErrSetNewTransactionToCache = errors.GoErr{
		ErrType:    errors.ErrorTypeUnexpectedError, // "ErrorTypeUnexpectedError"
		Status:     statusErrSetNewTransactionToCache,
		Code:       int(Error_ErrSetNewTransactionToCache), // 9702
		Message:    "failed to set new transaction to cache",
		GrpcCode:   codes.Unknown,
		IsExpected: true,
	}
)

var (
	statusErrUpdateTtlToCache, _ = status.New(
		codes.Unknown,
		"failed to update ttl in cache",
	).WithDetails(&structpb.Struct{
		Fields: map[string]*structpb.Value{
			"GrpcCode":   structpb.NewNumberValue(float64(codes.Unknown)),
			"Message":    structpb.NewStringValue("failed to update ttl in cache"),
			"ErrorType":  structpb.NewStringValue(errors.ErrorTypeUnexpectedError), // "ErrorTypeUnexpectedError"
			"IsExpected": structpb.NewBoolValue(true),
			"Code":       structpb.NewNumberValue(float64(Error_ErrUpdateTtlToCache)), // 9703
		},
	})

	ErrUpdateTtlToCache = errors.GoErr{
		ErrType:    errors.ErrorTypeUnexpectedError, // "ErrorTypeUnexpectedError"
		Status:     statusErrUpdateTtlToCache,
		Code:       int(Error_ErrUpdateTtlToCache), // 9703
		Message:    "failed to update ttl in cache",
		GrpcCode:   codes.Unknown,
		IsExpected: true,
	}
)

var (
	statusErrGetTransactionFromCache, _ = status.New(
		codes.Unknown,
		"failed to get item from cache",
	).WithDetails(&structpb.Struct{
		Fields: map[string]*structpb.Value{
			"GrpcCode":   structpb.NewNumberValue(float64(codes.Unknown)),
			"Message":    structpb.NewStringValue("failed to get item from cache"),
			"ErrorType":  structpb.NewStringValue(errors.ErrorTypeUnexpectedError), // "ErrorTypeUnexpectedError"
			"IsExpected": structpb.NewBoolValue(true),
			"Code":       structpb.NewNumberValue(float64(Error_ErrGetTransactionFromCache)), // 9714
		},
	})

	ErrGetTransactionFromCache = errors.GoErr{
		ErrType:    errors.ErrorTypeUnexpectedError, // "ErrorTypeUnexpectedError"
		Status:     statusErrGetTransactionFromCache,
		Code:       int(Error_ErrGetTransactionFromCache), // 9714
		Message:    "failed to get item from cache",
		GrpcCode:   codes.Unknown,
		IsExpected: true,
	}
)

var (
	statusErrStatusIsNotInProgress, _ = status.New(
		codes.InvalidArgument,
		"transaction status is not `in progress`",
	).WithDetails(&structpb.Struct{
		Fields: map[string]*structpb.Value{
			"GrpcCode":   structpb.NewNumberValue(float64(codes.InvalidArgument)),
			"Message":    structpb.NewStringValue("transaction status is not `in progress`"),
			"ErrorType":  structpb.NewStringValue(errors.ErrorTypeBadRequest), // "ErrorTypeBadRequest"
			"IsExpected": structpb.NewBoolValue(true),
			"Code":       structpb.NewNumberValue(float64(Error_ErrStatusIsNotInProgress)), // 1043
		},
	})

	ErrStatusIsNotInProgress = errors.GoErr{
		ErrType:    errors.ErrorTypeBadRequest, // "ErrorTypeBadRequest"
		Status:     statusErrStatusIsNotInProgress,
		Code:       int(Error_ErrStatusIsNotInProgress), // 1043
		Message:    "transaction status is not `in progress`",
		GrpcCode:   codes.InvalidArgument,
		IsExpected: true,
	}
)

var (
	statusErrCommissionNotFound, _ = status.New(
		codes.NotFound,
		"commission not found",
	).WithDetails(&structpb.Struct{
		Fields: map[string]*structpb.Value{
			"GrpcCode":   structpb.NewNumberValue(float64(codes.NotFound)),
			"Message":    structpb.NewStringValue("commission not found"),
			"ErrorType":  structpb.NewStringValue(errors.ErrorTypeNotFound), // "ErrorTypeNotFound"
			"IsExpected": structpb.NewBoolValue(true),
			"Code":       structpb.NewNumberValue(float64(Error_ErrCommissionNotFound)), // 1044
		},
	})

	ErrCommissionNotFound = errors.GoErr{
		ErrType:    errors.ErrorTypeNotFound, // "ErrorTypeNotFound"
		Status:     statusErrCommissionNotFound,
		Code:       int(Error_ErrCommissionNotFound), // 1044
		Message:    "commission not found",
		GrpcCode:   codes.NotFound,
		IsExpected: true,
	}
)

var (
	statusErrTokenIsExpided, _ = status.New(
		codes.Unknown,
		"token is expired",
	).WithDetails(&structpb.Struct{
		Fields: map[string]*structpb.Value{
			"GrpcCode":   structpb.NewNumberValue(float64(codes.Unknown)),
			"Message":    structpb.NewStringValue("token is expired"),
			"ErrorType":  structpb.NewStringValue(errors.ErrorTypeUnexpectedError), // "ErrorTypeUnexpectedError"
			"IsExpected": structpb.NewBoolValue(true),
			"Code":       structpb.NewNumberValue(float64(Error_ErrTokenIsExpided)), // 9200
		},
	})

	ErrTokenIsExpided = errors.GoErr{
		ErrType:    errors.ErrorTypeUnexpectedError, // "ErrorTypeUnexpectedError"
		Status:     statusErrTokenIsExpided,
		Code:       int(Error_ErrTokenIsExpided), // 9200
		Message:    "token is expired",
		GrpcCode:   codes.Unknown,
		IsExpected: true,
	}
)

var (
	statusErrNoToken, _ = status.New(
		codes.InvalidArgument,
		"empty token",
	).WithDetails(&structpb.Struct{
		Fields: map[string]*structpb.Value{
			"GrpcCode":   structpb.NewNumberValue(float64(codes.InvalidArgument)),
			"Message":    structpb.NewStringValue("empty token"),
			"ErrorType":  structpb.NewStringValue(errors.ErrorTypeBadRequest), // "ErrorTypeBadRequest"
			"IsExpected": structpb.NewBoolValue(true),
			"Code":       structpb.NewNumberValue(float64(Error_ErrNoToken)), // 9201
		},
	})

	ErrNoToken = errors.GoErr{
		ErrType:    errors.ErrorTypeBadRequest, // "ErrorTypeBadRequest"
		Status:     statusErrNoToken,
		Code:       int(Error_ErrNoToken), // 9201
		Message:    "empty token",
		GrpcCode:   codes.InvalidArgument,
		IsExpected: true,
	}
)

var (
	statusErrCantGetClaimsFromToken, _ = status.New(
		codes.Unknown,
		"error get user claims from token",
	).WithDetails(&structpb.Struct{
		Fields: map[string]*structpb.Value{
			"GrpcCode":   structpb.NewNumberValue(float64(codes.Unknown)),
			"Message":    structpb.NewStringValue("error get user claims from token"),
			"ErrorType":  structpb.NewStringValue(errors.ErrorTypeUnexpectedError), // "ErrorTypeUnexpectedError"
			"IsExpected": structpb.NewBoolValue(true),
			"Code":       structpb.NewNumberValue(float64(Error_ErrCantGetClaimsFromToken)), // 9202
		},
	})

	ErrCantGetClaimsFromToken = errors.GoErr{
		ErrType:    errors.ErrorTypeUnexpectedError, // "ErrorTypeUnexpectedError"
		Status:     statusErrCantGetClaimsFromToken,
		Code:       int(Error_ErrCantGetClaimsFromToken), // 9202
		Message:    "error get user claims from token",
		GrpcCode:   codes.Unknown,
		IsExpected: true,
	}
)

var (
	statusErrFailedToParseRefresh, _ = status.New(
		codes.Unknown,
		"failed to parse refresh",
	).WithDetails(&structpb.Struct{
		Fields: map[string]*structpb.Value{
			"GrpcCode":   structpb.NewNumberValue(float64(codes.Unknown)),
			"Message":    structpb.NewStringValue("failed to parse refresh"),
			"ErrorType":  structpb.NewStringValue(errors.ErrorTypeUnexpectedError), // "ErrorTypeUnexpectedError"
			"IsExpected": structpb.NewBoolValue(true),
			"Code":       structpb.NewNumberValue(float64(Error_ErrFailedToParseRefresh)), // 9203
		},
	})

	ErrFailedToParseRefresh = errors.GoErr{
		ErrType:    errors.ErrorTypeUnexpectedError, // "ErrorTypeUnexpectedError"
		Status:     statusErrFailedToParseRefresh,
		Code:       int(Error_ErrFailedToParseRefresh), // 9203
		Message:    "failed to parse refresh",
		GrpcCode:   codes.Unknown,
		IsExpected: true,
	}
)

var (
	statusErrFailedToParseAccess, _ = status.New(
		codes.Unknown,
		"failed to parse access",
	).WithDetails(&structpb.Struct{
		Fields: map[string]*structpb.Value{
			"GrpcCode":   structpb.NewNumberValue(float64(codes.Unknown)),
			"Message":    structpb.NewStringValue("failed to parse access"),
			"ErrorType":  structpb.NewStringValue(errors.ErrorTypeUnexpectedError), // "ErrorTypeUnexpectedError"
			"IsExpected": structpb.NewBoolValue(true),
			"Code":       structpb.NewNumberValue(float64(Error_ErrFailedToParseAccess)), // 9204
		},
	})

	ErrFailedToParseAccess = errors.GoErr{
		ErrType:    errors.ErrorTypeUnexpectedError, // "ErrorTypeUnexpectedError"
		Status:     statusErrFailedToParseAccess,
		Code:       int(Error_ErrFailedToParseAccess), // 9204
		Message:    "failed to parse access",
		GrpcCode:   codes.Unknown,
		IsExpected: true,
	}
)

var (
	statusErrFailedToBuildToken, _ = status.New(
		codes.Unknown,
		"failed to build token",
	).WithDetails(&structpb.Struct{
		Fields: map[string]*structpb.Value{
			"GrpcCode":   structpb.NewNumberValue(float64(codes.Unknown)),
			"Message":    structpb.NewStringValue("failed to build token"),
			"ErrorType":  structpb.NewStringValue(errors.ErrorTypeUnexpectedError), // "ErrorTypeUnexpectedError"
			"IsExpected": structpb.NewBoolValue(true),
			"Code":       structpb.NewNumberValue(float64(Error_ErrFailedToBuildToken)), // 9205
		},
	})

	ErrFailedToBuildToken = errors.GoErr{
		ErrType:    errors.ErrorTypeUnexpectedError, // "ErrorTypeUnexpectedError"
		Status:     statusErrFailedToBuildToken,
		Code:       int(Error_ErrFailedToBuildToken), // 9205
		Message:    "failed to build token",
		GrpcCode:   codes.Unknown,
		IsExpected: true,
	}
)

var (
	statusErrFailedToSignToken, _ = status.New(
		codes.Unknown,
		"failed to sign token",
	).WithDetails(&structpb.Struct{
		Fields: map[string]*structpb.Value{
			"GrpcCode":   structpb.NewNumberValue(float64(codes.Unknown)),
			"Message":    structpb.NewStringValue("failed to sign token"),
			"ErrorType":  structpb.NewStringValue(errors.ErrorTypeUnexpectedError), // "ErrorTypeUnexpectedError"
			"IsExpected": structpb.NewBoolValue(true),
			"Code":       structpb.NewNumberValue(float64(Error_ErrFailedToSignToken)), // 9206
		},
	})

	ErrFailedToSignToken = errors.GoErr{
		ErrType:    errors.ErrorTypeUnexpectedError, // "ErrorTypeUnexpectedError"
		Status:     statusErrFailedToSignToken,
		Code:       int(Error_ErrFailedToSignToken), // 9206
		Message:    "failed to sign token",
		GrpcCode:   codes.Unknown,
		IsExpected: true,
	}
)

var (
	statusErrFailedToOpenToken, _ = status.New(
		codes.Unknown,
		"failed to open token",
	).WithDetails(&structpb.Struct{
		Fields: map[string]*structpb.Value{
			"GrpcCode":   structpb.NewNumberValue(float64(codes.Unknown)),
			"Message":    structpb.NewStringValue("failed to open token"),
			"ErrorType":  structpb.NewStringValue(errors.ErrorTypeUnexpectedError), // "ErrorTypeUnexpectedError"
			"IsExpected": structpb.NewBoolValue(true),
			"Code":       structpb.NewNumberValue(float64(Error_ErrFailedToOpenToken)), // 9207
		},
	})

	ErrFailedToOpenToken = errors.GoErr{
		ErrType:    errors.ErrorTypeUnexpectedError, // "ErrorTypeUnexpectedError"
		Status:     statusErrFailedToOpenToken,
		Code:       int(Error_ErrFailedToOpenToken), // 9207
		Message:    "failed to open token",
		GrpcCode:   codes.Unknown,
		IsExpected: true,
	}
)

var (
	statusErrFailedToGetPublicKey, _ = status.New(
		codes.Unknown,
		"failed to get public key",
	).WithDetails(&structpb.Struct{
		Fields: map[string]*structpb.Value{
			"GrpcCode":   structpb.NewNumberValue(float64(codes.Unknown)),
			"Message":    structpb.NewStringValue("failed to get public key"),
			"ErrorType":  structpb.NewStringValue(errors.ErrorTypeUnexpectedError), // "ErrorTypeUnexpectedError"
			"IsExpected": structpb.NewBoolValue(true),
			"Code":       structpb.NewNumberValue(float64(Error_ErrFailedToGetPublicKey)), // 9208
		},
	})

	ErrFailedToGetPublicKey = errors.GoErr{
		ErrType:    errors.ErrorTypeUnexpectedError, // "ErrorTypeUnexpectedError"
		Status:     statusErrFailedToGetPublicKey,
		Code:       int(Error_ErrFailedToGetPublicKey), // 9208
		Message:    "failed to get public key",
		GrpcCode:   codes.Unknown,
		IsExpected: true,
	}
)

var (
	statusErrFailedToVerifyJWS, _ = status.New(
		codes.Unknown,
		"failed to verify JWS",
	).WithDetails(&structpb.Struct{
		Fields: map[string]*structpb.Value{
			"GrpcCode":   structpb.NewNumberValue(float64(codes.Unknown)),
			"Message":    structpb.NewStringValue("failed to verify JWS"),
			"ErrorType":  structpb.NewStringValue(errors.ErrorTypeUnexpectedError), // "ErrorTypeUnexpectedError"
			"IsExpected": structpb.NewBoolValue(true),
			"Code":       structpb.NewNumberValue(float64(Error_ErrFailedToVerifyJWS)), // 9209
		},
	})

	ErrFailedToVerifyJWS = errors.GoErr{
		ErrType:    errors.ErrorTypeUnexpectedError, // "ErrorTypeUnexpectedError"
		Status:     statusErrFailedToVerifyJWS,
		Code:       int(Error_ErrFailedToVerifyJWS), // 9209
		Message:    "failed to verify JWS",
		GrpcCode:   codes.Unknown,
		IsExpected: true,
	}
)

var (
	statusErrFailedToParseJWK, _ = status.New(
		codes.Unknown,
		"failed to parse JWK",
	).WithDetails(&structpb.Struct{
		Fields: map[string]*structpb.Value{
			"GrpcCode":   structpb.NewNumberValue(float64(codes.Unknown)),
			"Message":    structpb.NewStringValue("failed to parse JWK"),
			"ErrorType":  structpb.NewStringValue(errors.ErrorTypeUnexpectedError), // "ErrorTypeUnexpectedError"
			"IsExpected": structpb.NewBoolValue(true),
			"Code":       structpb.NewNumberValue(float64(Error_ErrFailedToParseJWK)), // 9210
		},
	})

	ErrFailedToParseJWK = errors.GoErr{
		ErrType:    errors.ErrorTypeUnexpectedError, // "ErrorTypeUnexpectedError"
		Status:     statusErrFailedToParseJWK,
		Code:       int(Error_ErrFailedToParseJWK), // 9210
		Message:    "failed to parse JWK",
		GrpcCode:   codes.Unknown,
		IsExpected: true,
	}
)

var (
	statusErrTokenNotFound, _ = status.New(
		codes.NotFound,
		"token doesn't exist",
	).WithDetails(&structpb.Struct{
		Fields: map[string]*structpb.Value{
			"GrpcCode":   structpb.NewNumberValue(float64(codes.NotFound)),
			"Message":    structpb.NewStringValue("token doesn't exist"),
			"ErrorType":  structpb.NewStringValue(errors.ErrorTypeNotFound), // "ErrorTypeNotFound"
			"IsExpected": structpb.NewBoolValue(true),
			"Code":       structpb.NewNumberValue(float64(Error_ErrTokenNotFound)), // 4003
		},
	})

	ErrTokenNotFound = errors.GoErr{
		ErrType:    errors.ErrorTypeNotFound, // "ErrorTypeNotFound"
		Status:     statusErrTokenNotFound,
		Code:       int(Error_ErrTokenNotFound), // 4003
		Message:    "token doesn't exist",
		GrpcCode:   codes.NotFound,
		IsExpected: true,
	}
)

var (
	statusErrTokenTransactionNotFound, _ = status.New(
		codes.NotFound,
		"token transaction doesn't exist",
	).WithDetails(&structpb.Struct{
		Fields: map[string]*structpb.Value{
			"GrpcCode":   structpb.NewNumberValue(float64(codes.NotFound)),
			"Message":    structpb.NewStringValue("token transaction doesn't exist"),
			"ErrorType":  structpb.NewStringValue(errors.ErrorTypeNotFound), // "ErrorTypeNotFound"
			"IsExpected": structpb.NewBoolValue(true),
			"Code":       structpb.NewNumberValue(float64(Error_ErrTokenTransactionNotFound)), // 4004
		},
	})

	ErrTokenTransactionNotFound = errors.GoErr{
		ErrType:    errors.ErrorTypeNotFound, // "ErrorTypeNotFound"
		Status:     statusErrTokenTransactionNotFound,
		Code:       int(Error_ErrTokenTransactionNotFound), // 4004
		Message:    "token transaction doesn't exist",
		GrpcCode:   codes.NotFound,
		IsExpected: true,
	}
)

var (
	statusErrNoCredentialToUpdate, _ = status.New(
		codes.NotFound,
		"could not find the corresponding documents to update",
	).WithDetails(&structpb.Struct{
		Fields: map[string]*structpb.Value{
			"GrpcCode":   structpb.NewNumberValue(float64(codes.NotFound)),
			"Message":    structpb.NewStringValue("could not find the corresponding documents to update"),
			"ErrorType":  structpb.NewStringValue(errors.ErrorTypeNotFound), // "ErrorTypeNotFound"
			"IsExpected": structpb.NewBoolValue(true),
			"Code":       structpb.NewNumberValue(float64(Error_ErrNoCredentialToUpdate)), // 7102
		},
	})

	ErrNoCredentialToUpdate = errors.GoErr{
		ErrType:    errors.ErrorTypeNotFound, // "ErrorTypeNotFound"
		Status:     statusErrNoCredentialToUpdate,
		Code:       int(Error_ErrNoCredentialToUpdate), // 7102
		Message:    "could not find the corresponding documents to update",
		GrpcCode:   codes.NotFound,
		IsExpected: true,
	}
)

var (
	statusErrTransactionAlreadyExists, _ = status.New(
		codes.InvalidArgument,
		"transaction already exists",
	).WithDetails(&structpb.Struct{
		Fields: map[string]*structpb.Value{
			"GrpcCode":   structpb.NewNumberValue(float64(codes.InvalidArgument)),
			"Message":    structpb.NewStringValue("transaction already exists"),
			"ErrorType":  structpb.NewStringValue(errors.ErrorTypeBadRequest), // "ErrorTypeBadRequest"
			"IsExpected": structpb.NewBoolValue(true),
			"Code":       structpb.NewNumberValue(float64(Error_ErrTransactionAlreadyExists)), // 5000
		},
	})

	ErrTransactionAlreadyExists = errors.GoErr{
		ErrType:    errors.ErrorTypeBadRequest, // "ErrorTypeBadRequest"
		Status:     statusErrTransactionAlreadyExists,
		Code:       int(Error_ErrTransactionAlreadyExists), // 5000
		Message:    "transaction already exists",
		GrpcCode:   codes.InvalidArgument,
		IsExpected: true,
	}
)

var (
	statusErrTransactionTryLimitIsOver, _ = status.New(
		codes.InvalidArgument,
		"transaction try limit is over",
	).WithDetails(&structpb.Struct{
		Fields: map[string]*structpb.Value{
			"GrpcCode":   structpb.NewNumberValue(float64(codes.InvalidArgument)),
			"Message":    structpb.NewStringValue("transaction try limit is over"),
			"ErrorType":  structpb.NewStringValue(errors.ErrorTypeBadRequest), // "ErrorTypeBadRequest"
			"IsExpected": structpb.NewBoolValue(true),
			"Code":       structpb.NewNumberValue(float64(Error_ErrTransactionTryLimitIsOver)), // 5001
		},
	})

	ErrTransactionTryLimitIsOver = errors.GoErr{
		ErrType:    errors.ErrorTypeBadRequest, // "ErrorTypeBadRequest"
		Status:     statusErrTransactionTryLimitIsOver,
		Code:       int(Error_ErrTransactionTryLimitIsOver), // 5001
		Message:    "transaction try limit is over",
		GrpcCode:   codes.InvalidArgument,
		IsExpected: true,
	}
)

var (
	statusErrTimeoutError, _ = status.New(
		codes.InvalidArgument,
		"timeout error",
	).WithDetails(&structpb.Struct{
		Fields: map[string]*structpb.Value{
			"GrpcCode":   structpb.NewNumberValue(float64(codes.InvalidArgument)),
			"Message":    structpb.NewStringValue("timeout error"),
			"ErrorType":  structpb.NewStringValue(errors.ErrorTypeBadRequest), // "ErrorTypeBadRequest"
			"IsExpected": structpb.NewBoolValue(true),
			"Code":       structpb.NewNumberValue(float64(Error_ErrTimeoutError)), // 5002
		},
	})

	ErrTimeoutError = errors.GoErr{
		ErrType:    errors.ErrorTypeBadRequest, // "ErrorTypeBadRequest"
		Status:     statusErrTimeoutError,
		Code:       int(Error_ErrTimeoutError), // 5002
		Message:    "timeout error",
		GrpcCode:   codes.InvalidArgument,
		IsExpected: true,
	}
)

var (
	statusErrTransactionCreation, _ = status.New(
		codes.Unknown,
		"an error occurred while creating transaction",
	).WithDetails(&structpb.Struct{
		Fields: map[string]*structpb.Value{
			"GrpcCode":   structpb.NewNumberValue(float64(codes.Unknown)),
			"Message":    structpb.NewStringValue("an error occurred while creating transaction"),
			"ErrorType":  structpb.NewStringValue(errors.ErrorTypeUnexpectedError), // "ErrorTypeUnexpectedError"
			"IsExpected": structpb.NewBoolValue(true),
			"Code":       structpb.NewNumberValue(float64(Error_ErrTransactionCreation)), // 5003
		},
	})

	ErrTransactionCreation = errors.GoErr{
		ErrType:    errors.ErrorTypeUnexpectedError, // "ErrorTypeUnexpectedError"
		Status:     statusErrTransactionCreation,
		Code:       int(Error_ErrTransactionCreation), // 5003
		Message:    "an error occurred while creating transaction",
		GrpcCode:   codes.Unknown,
		IsExpected: true,
	}
)

var (
	statusErrPanValidation, _ = status.New(
		codes.InvalidArgument,
		"the pan is incorrect",
	).WithDetails(&structpb.Struct{
		Fields: map[string]*structpb.Value{
			"GrpcCode":   structpb.NewNumberValue(float64(codes.InvalidArgument)),
			"Message":    structpb.NewStringValue("the pan is incorrect"),
			"ErrorType":  structpb.NewStringValue(errors.ErrorTypeBadRequest), // "ErrorTypeBadRequest"
			"IsExpected": structpb.NewBoolValue(true),
			"Code":       structpb.NewNumberValue(float64(Error_ErrPanValidation)), // 5004
		},
	})

	ErrPanValidation = errors.GoErr{
		ErrType:    errors.ErrorTypeBadRequest, // "ErrorTypeBadRequest"
		Status:     statusErrPanValidation,
		Code:       int(Error_ErrPanValidation), // 5004
		Message:    "the pan is incorrect",
		GrpcCode:   codes.InvalidArgument,
		IsExpected: true,
	}
)

var (
	statusErrCardExpiration, _ = status.New(
		codes.InvalidArgument,
		"the card is expired",
	).WithDetails(&structpb.Struct{
		Fields: map[string]*structpb.Value{
			"GrpcCode":   structpb.NewNumberValue(float64(codes.InvalidArgument)),
			"Message":    structpb.NewStringValue("the card is expired"),
			"ErrorType":  structpb.NewStringValue(errors.ErrorTypeBadRequest), // "ErrorTypeBadRequest"
			"IsExpected": structpb.NewBoolValue(true),
			"Code":       structpb.NewNumberValue(float64(Error_ErrCardExpiration)), // 5005
		},
	})

	ErrCardExpiration = errors.GoErr{
		ErrType:    errors.ErrorTypeBadRequest, // "ErrorTypeBadRequest"
		Status:     statusErrCardExpiration,
		Code:       int(Error_ErrCardExpiration), // 5005
		Message:    "the card is expired",
		GrpcCode:   codes.InvalidArgument,
		IsExpected: true,
	}
)

var (
	statusErrTransactionAmountLimitIsOver, _ = status.New(
		codes.InvalidArgument,
		"transaction amount limit is over",
	).WithDetails(&structpb.Struct{
		Fields: map[string]*structpb.Value{
			"GrpcCode":   structpb.NewNumberValue(float64(codes.InvalidArgument)),
			"Message":    structpb.NewStringValue("transaction amount limit is over"),
			"ErrorType":  structpb.NewStringValue(errors.ErrorTypeBadRequest), // "ErrorTypeBadRequest"
			"IsExpected": structpb.NewBoolValue(true),
			"Code":       structpb.NewNumberValue(float64(Error_ErrTransactionAmountLimitIsOver)), // 5006
		},
	})

	ErrTransactionAmountLimitIsOver = errors.GoErr{
		ErrType:    errors.ErrorTypeBadRequest, // "ErrorTypeBadRequest"
		Status:     statusErrTransactionAmountLimitIsOver,
		Code:       int(Error_ErrTransactionAmountLimitIsOver), // 5006
		Message:    "transaction amount limit is over",
		GrpcCode:   codes.InvalidArgument,
		IsExpected: true,
	}
)

var (
	statusErrAmountValidation, _ = status.New(
		codes.InvalidArgument,
		"inoperable amount for this operation type",
	).WithDetails(&structpb.Struct{
		Fields: map[string]*structpb.Value{
			"GrpcCode":   structpb.NewNumberValue(float64(codes.InvalidArgument)),
			"Message":    structpb.NewStringValue("inoperable amount for this operation type"),
			"ErrorType":  structpb.NewStringValue(errors.ErrorTypeBadRequest), // "ErrorTypeBadRequest"
			"IsExpected": structpb.NewBoolValue(true),
			"Code":       structpb.NewNumberValue(float64(Error_ErrAmountValidation)), // 5007
		},
	})

	ErrAmountValidation = errors.GoErr{
		ErrType:    errors.ErrorTypeBadRequest, // "ErrorTypeBadRequest"
		Status:     statusErrAmountValidation,
		Code:       int(Error_ErrAmountValidation), // 5007
		Message:    "inoperable amount for this operation type",
		GrpcCode:   codes.InvalidArgument,
		IsExpected: true,
	}
)

var (
	statusErrOperationTypeValidation, _ = status.New(
		codes.InvalidArgument,
		"inoperable operation type",
	).WithDetails(&structpb.Struct{
		Fields: map[string]*structpb.Value{
			"GrpcCode":   structpb.NewNumberValue(float64(codes.InvalidArgument)),
			"Message":    structpb.NewStringValue("inoperable operation type"),
			"ErrorType":  structpb.NewStringValue(errors.ErrorTypeBadRequest), // "ErrorTypeBadRequest"
			"IsExpected": structpb.NewBoolValue(true),
			"Code":       structpb.NewNumberValue(float64(Error_ErrOperationTypeValidation)), // 5008
		},
	})

	ErrOperationTypeValidation = errors.GoErr{
		ErrType:    errors.ErrorTypeBadRequest, // "ErrorTypeBadRequest"
		Status:     statusErrOperationTypeValidation,
		Code:       int(Error_ErrOperationTypeValidation), // 5008
		Message:    "inoperable operation type",
		GrpcCode:   codes.InvalidArgument,
		IsExpected: true,
	}
)

var (
	statusErrTransactionCallbackEmpty, _ = status.New(
		codes.InvalidArgument,
		"received an empty transaction callback request",
	).WithDetails(&structpb.Struct{
		Fields: map[string]*structpb.Value{
			"GrpcCode":   structpb.NewNumberValue(float64(codes.InvalidArgument)),
			"Message":    structpb.NewStringValue("received an empty transaction callback request"),
			"ErrorType":  structpb.NewStringValue(errors.ErrorTypeBadRequest), // "ErrorTypeBadRequest"
			"IsExpected": structpb.NewBoolValue(true),
			"Code":       structpb.NewNumberValue(float64(Error_ErrTransactionCallbackEmpty)), // 5009
		},
	})

	ErrTransactionCallbackEmpty = errors.GoErr{
		ErrType:    errors.ErrorTypeBadRequest, // "ErrorTypeBadRequest"
		Status:     statusErrTransactionCallbackEmpty,
		Code:       int(Error_ErrTransactionCallbackEmpty), // 5009
		Message:    "received an empty transaction callback request",
		GrpcCode:   codes.InvalidArgument,
		IsExpected: true,
	}
)

var (
	statusErrTransactionStatusNotFound, _ = status.New(
		codes.NotFound,
		"undefined transaction status",
	).WithDetails(&structpb.Struct{
		Fields: map[string]*structpb.Value{
			"GrpcCode":   structpb.NewNumberValue(float64(codes.NotFound)),
			"Message":    structpb.NewStringValue("undefined transaction status"),
			"ErrorType":  structpb.NewStringValue(errors.ErrorTypeNotFound), // "ErrorTypeNotFound"
			"IsExpected": structpb.NewBoolValue(true),
			"Code":       structpb.NewNumberValue(float64(Error_ErrTransactionStatusNotFound)), // 5101
		},
	})

	ErrTransactionStatusNotFound = errors.GoErr{
		ErrType:    errors.ErrorTypeNotFound, // "ErrorTypeNotFound"
		Status:     statusErrTransactionStatusNotFound,
		Code:       int(Error_ErrTransactionStatusNotFound), // 5101
		Message:    "undefined transaction status",
		GrpcCode:   codes.NotFound,
		IsExpected: true,
	}
)

var (
	statusErrTransactionTypeNotFound, _ = status.New(
		codes.NotFound,
		"undefined transaction type",
	).WithDetails(&structpb.Struct{
		Fields: map[string]*structpb.Value{
			"GrpcCode":   structpb.NewNumberValue(float64(codes.NotFound)),
			"Message":    structpb.NewStringValue("undefined transaction type"),
			"ErrorType":  structpb.NewStringValue(errors.ErrorTypeNotFound), // "ErrorTypeNotFound"
			"IsExpected": structpb.NewBoolValue(true),
			"Code":       structpb.NewNumberValue(float64(Error_ErrTransactionTypeNotFound)), // 5102
		},
	})

	ErrTransactionTypeNotFound = errors.GoErr{
		ErrType:    errors.ErrorTypeNotFound, // "ErrorTypeNotFound"
		Status:     statusErrTransactionTypeNotFound,
		Code:       int(Error_ErrTransactionTypeNotFound), // 5102
		Message:    "undefined transaction type",
		GrpcCode:   codes.NotFound,
		IsExpected: true,
	}
)

var (
	statusErrTransactionNotFound, _ = status.New(
		codes.NotFound,
		"transaction not found",
	).WithDetails(&structpb.Struct{
		Fields: map[string]*structpb.Value{
			"GrpcCode":   structpb.NewNumberValue(float64(codes.NotFound)),
			"Message":    structpb.NewStringValue("transaction not found"),
			"ErrorType":  structpb.NewStringValue(errors.ErrorTypeNotFound), // "ErrorTypeNotFound"
			"IsExpected": structpb.NewBoolValue(true),
			"Code":       structpb.NewNumberValue(float64(Error_ErrTransactionNotFound)), // 5103
		},
	})

	ErrTransactionNotFound = errors.GoErr{
		ErrType:    errors.ErrorTypeNotFound, // "ErrorTypeNotFound"
		Status:     statusErrTransactionNotFound,
		Code:       int(Error_ErrTransactionNotFound), // 5103
		Message:    "transaction not found",
		GrpcCode:   codes.NotFound,
		IsExpected: true,
	}
)

var (
	statusErrTransactionInfoNotFound, _ = status.New(
		codes.NotFound,
		"transaction has no information",
	).WithDetails(&structpb.Struct{
		Fields: map[string]*structpb.Value{
			"GrpcCode":   structpb.NewNumberValue(float64(codes.NotFound)),
			"Message":    structpb.NewStringValue("transaction has no information"),
			"ErrorType":  structpb.NewStringValue(errors.ErrorTypeNotFound), // "ErrorTypeNotFound"
			"IsExpected": structpb.NewBoolValue(true),
			"Code":       structpb.NewNumberValue(float64(Error_ErrTransactionInfoNotFound)), // 5104
		},
	})

	ErrTransactionInfoNotFound = errors.GoErr{
		ErrType:    errors.ErrorTypeNotFound, // "ErrorTypeNotFound"
		Status:     statusErrTransactionInfoNotFound,
		Code:       int(Error_ErrTransactionInfoNotFound), // 5104
		Message:    "transaction has no information",
		GrpcCode:   codes.NotFound,
		IsExpected: true,
	}
)

var (
	statusErrMerchantOrderStatusNotFound, _ = status.New(
		codes.NotFound,
		"merchant transaction status not found",
	).WithDetails(&structpb.Struct{
		Fields: map[string]*structpb.Value{
			"GrpcCode":   structpb.NewNumberValue(float64(codes.NotFound)),
			"Message":    structpb.NewStringValue("merchant transaction status not found"),
			"ErrorType":  structpb.NewStringValue(errors.ErrorTypeNotFound), // "ErrorTypeNotFound"
			"IsExpected": structpb.NewBoolValue(true),
			"Code":       structpb.NewNumberValue(float64(Error_ErrMerchantOrderStatusNotFound)), // 5105
		},
	})

	ErrMerchantOrderStatusNotFound = errors.GoErr{
		ErrType:    errors.ErrorTypeNotFound, // "ErrorTypeNotFound"
		Status:     statusErrMerchantOrderStatusNotFound,
		Code:       int(Error_ErrMerchantOrderStatusNotFound), // 5105
		Message:    "merchant transaction status not found",
		GrpcCode:   codes.NotFound,
		IsExpected: true,
	}
)

var (
	statusErrMerchantOrderTypeNotFound, _ = status.New(
		codes.NotFound,
		"merchant transaction type not found",
	).WithDetails(&structpb.Struct{
		Fields: map[string]*structpb.Value{
			"GrpcCode":   structpb.NewNumberValue(float64(codes.NotFound)),
			"Message":    structpb.NewStringValue("merchant transaction type not found"),
			"ErrorType":  structpb.NewStringValue(errors.ErrorTypeNotFound), // "ErrorTypeNotFound"
			"IsExpected": structpb.NewBoolValue(true),
			"Code":       structpb.NewNumberValue(float64(Error_ErrMerchantOrderTypeNotFound)), // 5106
		},
	})

	ErrMerchantOrderTypeNotFound = errors.GoErr{
		ErrType:    errors.ErrorTypeNotFound, // "ErrorTypeNotFound"
		Status:     statusErrMerchantOrderTypeNotFound,
		Code:       int(Error_ErrMerchantOrderTypeNotFound), // 5106
		Message:    "merchant transaction type not found",
		GrpcCode:   codes.NotFound,
		IsExpected: true,
	}
)

var (
	statusErrTransactionLimitNotFound, _ = status.New(
		codes.NotFound,
		"transaction limit doesn't exist",
	).WithDetails(&structpb.Struct{
		Fields: map[string]*structpb.Value{
			"GrpcCode":   structpb.NewNumberValue(float64(codes.NotFound)),
			"Message":    structpb.NewStringValue("transaction limit doesn't exist"),
			"ErrorType":  structpb.NewStringValue(errors.ErrorTypeNotFound), // "ErrorTypeNotFound"
			"IsExpected": structpb.NewBoolValue(true),
			"Code":       structpb.NewNumberValue(float64(Error_ErrTransactionLimitNotFound)), // 5107
		},
	})

	ErrTransactionLimitNotFound = errors.GoErr{
		ErrType:    errors.ErrorTypeNotFound, // "ErrorTypeNotFound"
		Status:     statusErrTransactionLimitNotFound,
		Code:       int(Error_ErrTransactionLimitNotFound), // 5107
		Message:    "transaction limit doesn't exist",
		GrpcCode:   codes.NotFound,
		IsExpected: true,
	}
)

var (
	statusErrFailCallbackSending, _ = status.New(
		codes.Unknown,
		"something went wrong during callback sending",
	).WithDetails(&structpb.Struct{
		Fields: map[string]*structpb.Value{
			"GrpcCode":   structpb.NewNumberValue(float64(codes.Unknown)),
			"Message":    structpb.NewStringValue("something went wrong during callback sending"),
			"ErrorType":  structpb.NewStringValue(errors.ErrorTypeUnexpectedError), // "ErrorTypeUnexpectedError"
			"IsExpected": structpb.NewBoolValue(true),
			"Code":       structpb.NewNumberValue(float64(Error_ErrFailCallbackSending)), // 5201
		},
	})

	ErrFailCallbackSending = errors.GoErr{
		ErrType:    errors.ErrorTypeUnexpectedError, // "ErrorTypeUnexpectedError"
		Status:     statusErrFailCallbackSending,
		Code:       int(Error_ErrFailCallbackSending), // 5201
		Message:    "something went wrong during callback sending",
		GrpcCode:   codes.Unknown,
		IsExpected: true,
	}
)

var (
	statusErrRequestFailed, _ = status.New(
		codes.Unknown,
		"request failed",
	).WithDetails(&structpb.Struct{
		Fields: map[string]*structpb.Value{
			"GrpcCode":   structpb.NewNumberValue(float64(codes.Unknown)),
			"Message":    structpb.NewStringValue("request failed"),
			"ErrorType":  structpb.NewStringValue(errors.ErrorTypeUnexpectedError), // "ErrorTypeUnexpectedError"
			"IsExpected": structpb.NewBoolValue(true),
			"Code":       structpb.NewNumberValue(float64(Error_ErrRequestFailed)), // 5202
		},
	})

	ErrRequestFailed = errors.GoErr{
		ErrType:    errors.ErrorTypeUnexpectedError, // "ErrorTypeUnexpectedError"
		Status:     statusErrRequestFailed,
		Code:       int(Error_ErrRequestFailed), // 5202
		Message:    "request failed",
		GrpcCode:   codes.Unknown,
		IsExpected: true,
	}
)

var (
	statusErrTransactionAlreadyFinished, _ = status.New(
		codes.InvalidArgument,
		"transaction already finished",
	).WithDetails(&structpb.Struct{
		Fields: map[string]*structpb.Value{
			"GrpcCode":   structpb.NewNumberValue(float64(codes.InvalidArgument)),
			"Message":    structpb.NewStringValue("transaction already finished"),
			"ErrorType":  structpb.NewStringValue(errors.ErrorTypeBadRequest), // "ErrorTypeBadRequest"
			"IsExpected": structpb.NewBoolValue(true),
			"Code":       structpb.NewNumberValue(float64(Error_ErrTransactionAlreadyFinished)), // 5413
		},
	})

	ErrTransactionAlreadyFinished = errors.GoErr{
		ErrType:    errors.ErrorTypeBadRequest, // "ErrorTypeBadRequest"
		Status:     statusErrTransactionAlreadyFinished,
		Code:       int(Error_ErrTransactionAlreadyFinished), // 5413
		Message:    "transaction already finished",
		GrpcCode:   codes.InvalidArgument,
		IsExpected: true,
	}
)

var (
	statusErrStatusCannotBeChanged, _ = status.New(
		codes.Unknown,
		"status cannot be changed",
	).WithDetails(&structpb.Struct{
		Fields: map[string]*structpb.Value{
			"GrpcCode":   structpb.NewNumberValue(float64(codes.Unknown)),
			"Message":    structpb.NewStringValue("status cannot be changed"),
			"ErrorType":  structpb.NewStringValue(errors.ErrorTypeUnexpectedError), // "ErrorTypeUnexpectedError"
			"IsExpected": structpb.NewBoolValue(true),
			"Code":       structpb.NewNumberValue(float64(Error_ErrStatusCannotBeChanged)), // 5203
		},
	})

	ErrStatusCannotBeChanged = errors.GoErr{
		ErrType:    errors.ErrorTypeUnexpectedError, // "ErrorTypeUnexpectedError"
		Status:     statusErrStatusCannotBeChanged,
		Code:       int(Error_ErrStatusCannotBeChanged), // 5203
		Message:    "status cannot be changed",
		GrpcCode:   codes.Unknown,
		IsExpected: true,
	}
)

var (
	statusErrCallBackUrlIsEmpty, _ = status.New(
		codes.Unknown,
		"callback url is empty",
	).WithDetails(&structpb.Struct{
		Fields: map[string]*structpb.Value{
			"GrpcCode":   structpb.NewNumberValue(float64(codes.Unknown)),
			"Message":    structpb.NewStringValue("callback url is empty"),
			"ErrorType":  structpb.NewStringValue(errors.ErrorTypeUnexpectedError), // "ErrorTypeUnexpectedError"
			"IsExpected": structpb.NewBoolValue(true),
			"Code":       structpb.NewNumberValue(float64(Error_ErrCallBackUrlIsEmpty)), // 5204
		},
	})

	ErrCallBackUrlIsEmpty = errors.GoErr{
		ErrType:    errors.ErrorTypeUnexpectedError, // "ErrorTypeUnexpectedError"
		Status:     statusErrCallBackUrlIsEmpty,
		Code:       int(Error_ErrCallBackUrlIsEmpty), // 5204
		Message:    "callback url is empty",
		GrpcCode:   codes.Unknown,
		IsExpected: true,
	}
)

var (
	statusErrProjectTransactionConfirmation, _ = status.New(
		codes.Unknown,
		"unavailable project server",
	).WithDetails(&structpb.Struct{
		Fields: map[string]*structpb.Value{
			"GrpcCode":   structpb.NewNumberValue(float64(codes.Unknown)),
			"Message":    structpb.NewStringValue("unavailable project server"),
			"ErrorType":  structpb.NewStringValue(errors.ErrorTypeUnexpectedError), // "ErrorTypeUnexpectedError"
			"IsExpected": structpb.NewBoolValue(true),
			"Code":       structpb.NewNumberValue(float64(Error_ErrProjectTransactionConfirmation)), // 5205
		},
	})

	ErrProjectTransactionConfirmation = errors.GoErr{
		ErrType:    errors.ErrorTypeUnexpectedError, // "ErrorTypeUnexpectedError"
		Status:     statusErrProjectTransactionConfirmation,
		Code:       int(Error_ErrProjectTransactionConfirmation), // 5205
		Message:    "unavailable project server",
		GrpcCode:   codes.Unknown,
		IsExpected: true,
	}
)

var (
	statusErrUnPayableOrderStatus, _ = status.New(
		codes.InvalidArgument,
		"transaction has finalized status on the merchant side",
	).WithDetails(&structpb.Struct{
		Fields: map[string]*structpb.Value{
			"GrpcCode":   structpb.NewNumberValue(float64(codes.InvalidArgument)),
			"Message":    structpb.NewStringValue("transaction has finalized status on the merchant side"),
			"ErrorType":  structpb.NewStringValue(errors.ErrorTypeBadRequest), // "ErrorTypeBadRequest"
			"IsExpected": structpb.NewBoolValue(true),
			"Code":       structpb.NewNumberValue(float64(Error_ErrUnPayableOrderStatus)), // 5300
		},
	})

	ErrUnPayableOrderStatus = errors.GoErr{
		ErrType:    errors.ErrorTypeBadRequest, // "ErrorTypeBadRequest"
		Status:     statusErrUnPayableOrderStatus,
		Code:       int(Error_ErrUnPayableOrderStatus), // 5300
		Message:    "transaction has finalized status on the merchant side",
		GrpcCode:   codes.InvalidArgument,
		IsExpected: true,
	}
)

var (
	statusErrReceivingProjectSecretKey, _ = status.New(
		codes.InvalidArgument,
		"couldn't receive project data",
	).WithDetails(&structpb.Struct{
		Fields: map[string]*structpb.Value{
			"GrpcCode":   structpb.NewNumberValue(float64(codes.InvalidArgument)),
			"Message":    structpb.NewStringValue("couldn't receive project data"),
			"ErrorType":  structpb.NewStringValue(errors.ErrorTypeBadRequest), // "ErrorTypeBadRequest"
			"IsExpected": structpb.NewBoolValue(true),
			"Code":       structpb.NewNumberValue(float64(Error_ErrReceivingProjectSecretKey)), // 5400
		},
	})

	ErrReceivingProjectSecretKey = errors.GoErr{
		ErrType:    errors.ErrorTypeBadRequest, // "ErrorTypeBadRequest"
		Status:     statusErrReceivingProjectSecretKey,
		Code:       int(Error_ErrReceivingProjectSecretKey), // 5400
		Message:    "couldn't receive project data",
		GrpcCode:   codes.InvalidArgument,
		IsExpected: true,
	}
)

var (
	statusErrInvalidHash, _ = status.New(
		codes.InvalidArgument,
		"invalid hash",
	).WithDetails(&structpb.Struct{
		Fields: map[string]*structpb.Value{
			"GrpcCode":   structpb.NewNumberValue(float64(codes.InvalidArgument)),
			"Message":    structpb.NewStringValue("invalid hash"),
			"ErrorType":  structpb.NewStringValue(errors.ErrorTypeBadRequest), // "ErrorTypeBadRequest"
			"IsExpected": structpb.NewBoolValue(true),
			"Code":       structpb.NewNumberValue(float64(Error_ErrInvalidHash)), // 5401
		},
	})

	ErrInvalidHash = errors.GoErr{
		ErrType:    errors.ErrorTypeBadRequest, // "ErrorTypeBadRequest"
		Status:     statusErrInvalidHash,
		Code:       int(Error_ErrInvalidHash), // 5401
		Message:    "invalid hash",
		GrpcCode:   codes.InvalidArgument,
		IsExpected: true,
	}
)

var (
	statusErrCreatingClient, _ = status.New(
		codes.InvalidArgument,
		"create client failed",
	).WithDetails(&structpb.Struct{
		Fields: map[string]*structpb.Value{
			"GrpcCode":   structpb.NewNumberValue(float64(codes.InvalidArgument)),
			"Message":    structpb.NewStringValue("create client failed"),
			"ErrorType":  structpb.NewStringValue(errors.ErrorTypeBadRequest), // "ErrorTypeBadRequest"
			"IsExpected": structpb.NewBoolValue(true),
			"Code":       structpb.NewNumberValue(float64(Error_ErrCreatingClient)), // 5402
		},
	})

	ErrCreatingClient = errors.GoErr{
		ErrType:    errors.ErrorTypeBadRequest, // "ErrorTypeBadRequest"
		Status:     statusErrCreatingClient,
		Code:       int(Error_ErrCreatingClient), // 5402
		Message:    "create client failed",
		GrpcCode:   codes.InvalidArgument,
		IsExpected: true,
	}
)

var (
	statusErrSearchingTerminal, _ = status.New(
		codes.InvalidArgument,
		"search terminal failed",
	).WithDetails(&structpb.Struct{
		Fields: map[string]*structpb.Value{
			"GrpcCode":   structpb.NewNumberValue(float64(codes.InvalidArgument)),
			"Message":    structpb.NewStringValue("search terminal failed"),
			"ErrorType":  structpb.NewStringValue(errors.ErrorTypeBadRequest), // "ErrorTypeBadRequest"
			"IsExpected": structpb.NewBoolValue(true),
			"Code":       structpb.NewNumberValue(float64(Error_ErrSearchingTerminal)), // 5403
		},
	})

	ErrSearchingTerminal = errors.GoErr{
		ErrType:    errors.ErrorTypeBadRequest, // "ErrorTypeBadRequest"
		Status:     statusErrSearchingTerminal,
		Code:       int(Error_ErrSearchingTerminal), // 5403
		Message:    "search terminal failed",
		GrpcCode:   codes.InvalidArgument,
		IsExpected: true,
	}
)

var (
	statusErrMakingPayIn, _ = status.New(
		codes.Unknown,
		"an error occurred while making pay in",
	).WithDetails(&structpb.Struct{
		Fields: map[string]*structpb.Value{
			"GrpcCode":   structpb.NewNumberValue(float64(codes.Unknown)),
			"Message":    structpb.NewStringValue("an error occurred while making pay in"),
			"ErrorType":  structpb.NewStringValue(errors.ErrorTypeUnexpectedError), // "ErrorTypeUnexpectedError"
			"IsExpected": structpb.NewBoolValue(true),
			"Code":       structpb.NewNumberValue(float64(Error_ErrMakingPayIn)), // 5404
		},
	})

	ErrMakingPayIn = errors.GoErr{
		ErrType:    errors.ErrorTypeUnexpectedError, // "ErrorTypeUnexpectedError"
		Status:     statusErrMakingPayIn,
		Code:       int(Error_ErrMakingPayIn), // 5404
		Message:    "an error occurred while making pay in",
		GrpcCode:   codes.Unknown,
		IsExpected: true,
	}
)

var (
	statusErrConfirmingThreeDS, _ = status.New(
		codes.Unknown,
		"an error occurred while confirming three ds",
	).WithDetails(&structpb.Struct{
		Fields: map[string]*structpb.Value{
			"GrpcCode":   structpb.NewNumberValue(float64(codes.Unknown)),
			"Message":    structpb.NewStringValue("an error occurred while confirming three ds"),
			"ErrorType":  structpb.NewStringValue(errors.ErrorTypeUnexpectedError), // "ErrorTypeUnexpectedError"
			"IsExpected": structpb.NewBoolValue(true),
			"Code":       structpb.NewNumberValue(float64(Error_ErrConfirmingThreeDS)), // 5405
		},
	})

	ErrConfirmingThreeDS = errors.GoErr{
		ErrType:    errors.ErrorTypeUnexpectedError, // "ErrorTypeUnexpectedError"
		Status:     statusErrConfirmingThreeDS,
		Code:       int(Error_ErrConfirmingThreeDS), // 5405
		Message:    "an error occurred while confirming three ds",
		GrpcCode:   codes.Unknown,
		IsExpected: true,
	}
)

var (
	statusErrInvalidProjectSecret, _ = status.New(
		codes.InvalidArgument,
		"invalid project secret",
	).WithDetails(&structpb.Struct{
		Fields: map[string]*structpb.Value{
			"GrpcCode":   structpb.NewNumberValue(float64(codes.InvalidArgument)),
			"Message":    structpb.NewStringValue("invalid project secret"),
			"ErrorType":  structpb.NewStringValue(errors.ErrorTypeBadRequest), // "ErrorTypeBadRequest"
			"IsExpected": structpb.NewBoolValue(true),
			"Code":       structpb.NewNumberValue(float64(Error_ErrInvalidProjectSecret)), // 5406
		},
	})

	ErrInvalidProjectSecret = errors.GoErr{
		ErrType:    errors.ErrorTypeBadRequest, // "ErrorTypeBadRequest"
		Status:     statusErrInvalidProjectSecret,
		Code:       int(Error_ErrInvalidProjectSecret), // 5406
		Message:    "invalid project secret",
		GrpcCode:   codes.InvalidArgument,
		IsExpected: true,
	}
)

var (
	statusErrMakingOneClickPayIn, _ = status.New(
		codes.Unknown,
		"an error occurred while making one click pay in",
	).WithDetails(&structpb.Struct{
		Fields: map[string]*structpb.Value{
			"GrpcCode":   structpb.NewNumberValue(float64(codes.Unknown)),
			"Message":    structpb.NewStringValue("an error occurred while making one click pay in"),
			"ErrorType":  structpb.NewStringValue(errors.ErrorTypeUnexpectedError), // "ErrorTypeUnexpectedError"
			"IsExpected": structpb.NewBoolValue(true),
			"Code":       structpb.NewNumberValue(float64(Error_ErrMakingOneClickPayIn)), // 5407
		},
	})

	ErrMakingOneClickPayIn = errors.GoErr{
		ErrType:    errors.ErrorTypeUnexpectedError, // "ErrorTypeUnexpectedError"
		Status:     statusErrMakingOneClickPayIn,
		Code:       int(Error_ErrMakingOneClickPayIn), // 5407
		Message:    "an error occurred while making one click pay in",
		GrpcCode:   codes.Unknown,
		IsExpected: true,
	}
)

var (
	statusErrMakingPayOut, _ = status.New(
		codes.Unknown,
		"an error occurred while making pay out",
	).WithDetails(&structpb.Struct{
		Fields: map[string]*structpb.Value{
			"GrpcCode":   structpb.NewNumberValue(float64(codes.Unknown)),
			"Message":    structpb.NewStringValue("an error occurred while making pay out"),
			"ErrorType":  structpb.NewStringValue(errors.ErrorTypeUnexpectedError), // "ErrorTypeUnexpectedError"
			"IsExpected": structpb.NewBoolValue(true),
			"Code":       structpb.NewNumberValue(float64(Error_ErrMakingPayOut)), // 5408
		},
	})

	ErrMakingPayOut = errors.GoErr{
		ErrType:    errors.ErrorTypeUnexpectedError, // "ErrorTypeUnexpectedError"
		Status:     statusErrMakingPayOut,
		Code:       int(Error_ErrMakingPayOut), // 5408
		Message:    "an error occurred while making pay out",
		GrpcCode:   codes.Unknown,
		IsExpected: true,
	}
)

var (
	statusErrOrderNotFound, _ = status.New(
		codes.InvalidArgument,
		"Order not found",
	).WithDetails(&structpb.Struct{
		Fields: map[string]*structpb.Value{
			"GrpcCode":   structpb.NewNumberValue(float64(codes.InvalidArgument)),
			"Message":    structpb.NewStringValue("Order not found"),
			"ErrorType":  structpb.NewStringValue(errors.ErrorTypeBadRequest), // "ErrorTypeBadRequest"
			"IsExpected": structpb.NewBoolValue(true),
			"Code":       structpb.NewNumberValue(float64(Error_ErrOrderNotFound)), // 1041
		},
	})

	ErrOrderNotFound = errors.GoErr{
		ErrType:    errors.ErrorTypeBadRequest, // "ErrorTypeBadRequest"
		Status:     statusErrOrderNotFound,
		Code:       int(Error_ErrOrderNotFound), // 1041
		Message:    "Order not found",
		GrpcCode:   codes.InvalidArgument,
		IsExpected: true,
	}
)

var (
	statusErrUpdateTransactionStatus, _ = status.New(
		codes.InvalidArgument,
		"failed to update transaction status",
	).WithDetails(&structpb.Struct{
		Fields: map[string]*structpb.Value{
			"GrpcCode":   structpb.NewNumberValue(float64(codes.InvalidArgument)),
			"Message":    structpb.NewStringValue("failed to update transaction status"),
			"ErrorType":  structpb.NewStringValue(errors.ErrorTypeBadRequest), // "ErrorTypeBadRequest"
			"IsExpected": structpb.NewBoolValue(true),
			"Code":       structpb.NewNumberValue(float64(Error_ErrUpdateTransactionStatus)), // 9708
		},
	})

	ErrUpdateTransactionStatus = errors.GoErr{
		ErrType:    errors.ErrorTypeBadRequest, // "ErrorTypeBadRequest"
		Status:     statusErrUpdateTransactionStatus,
		Code:       int(Error_ErrUpdateTransactionStatus), // 9708
		Message:    "failed to update transaction status",
		GrpcCode:   codes.InvalidArgument,
		IsExpected: true,
	}
)

var (
	statusErrFutureTransaction, _ = status.New(
		codes.DataLoss,
		"transaction created time can not be greater than current time",
	).WithDetails(&structpb.Struct{
		Fields: map[string]*structpb.Value{
			"GrpcCode":   structpb.NewNumberValue(float64(codes.DataLoss)),
			"Message":    structpb.NewStringValue("transaction created time can not be greater than current time"),
			"ErrorType":  structpb.NewStringValue(errors.ErrorTypeDbViolation), // "ErrorTypeDbViolation"
			"IsExpected": structpb.NewBoolValue(true),
			"Code":       structpb.NewNumberValue(float64(Error_ErrFutureTransaction)), // 9701
		},
	})

	ErrFutureTransaction = errors.GoErr{
		ErrType:    errors.ErrorTypeDbViolation, // "ErrorTypeDbViolation"
		Status:     statusErrFutureTransaction,
		Code:       int(Error_ErrFutureTransaction), // 9701
		Message:    "transaction created time can not be greater than current time",
		GrpcCode:   codes.DataLoss,
		IsExpected: true,
	}
)

var (
	statusErrProviderDriverNotFound, _ = status.New(
		codes.InvalidArgument,
		"provider driver not found",
	).WithDetails(&structpb.Struct{
		Fields: map[string]*structpb.Value{
			"GrpcCode":   structpb.NewNumberValue(float64(codes.InvalidArgument)),
			"Message":    structpb.NewStringValue("provider driver not found"),
			"ErrorType":  structpb.NewStringValue(errors.ErrorTypeBadRequest), // "ErrorTypeBadRequest"
			"IsExpected": structpb.NewBoolValue(true),
			"Code":       structpb.NewNumberValue(float64(Error_ErrProviderDriverNotFound)), // 9705
		},
	})

	ErrProviderDriverNotFound = errors.GoErr{
		ErrType:    errors.ErrorTypeBadRequest, // "ErrorTypeBadRequest"
		Status:     statusErrProviderDriverNotFound,
		Code:       int(Error_ErrProviderDriverNotFound), // 9705
		Message:    "provider driver not found",
		GrpcCode:   codes.InvalidArgument,
		IsExpected: true,
	}
)

var (
	statusErrEmptyDateFrom, _ = status.New(
		codes.InvalidArgument,
		"received an empty dateFrom parameter",
	).WithDetails(&structpb.Struct{
		Fields: map[string]*structpb.Value{
			"GrpcCode":   structpb.NewNumberValue(float64(codes.InvalidArgument)),
			"Message":    structpb.NewStringValue("received an empty dateFrom parameter"),
			"ErrorType":  structpb.NewStringValue(errors.ErrorTypeBadRequest), // "ErrorTypeBadRequest"
			"IsExpected": structpb.NewBoolValue(true),
			"Code":       structpb.NewNumberValue(float64(Error_ErrEmptyDateFrom)), // 1045
		},
	})

	ErrEmptyDateFrom = errors.GoErr{
		ErrType:    errors.ErrorTypeBadRequest, // "ErrorTypeBadRequest"
		Status:     statusErrEmptyDateFrom,
		Code:       int(Error_ErrEmptyDateFrom), // 1045
		Message:    "received an empty dateFrom parameter",
		GrpcCode:   codes.InvalidArgument,
		IsExpected: true,
	}
)

var (
	statusErrEmptyDateTo, _ = status.New(
		codes.InvalidArgument,
		"received an empty dateTo parameter",
	).WithDetails(&structpb.Struct{
		Fields: map[string]*structpb.Value{
			"GrpcCode":   structpb.NewNumberValue(float64(codes.InvalidArgument)),
			"Message":    structpb.NewStringValue("received an empty dateTo parameter"),
			"ErrorType":  structpb.NewStringValue(errors.ErrorTypeBadRequest), // "ErrorTypeBadRequest"
			"IsExpected": structpb.NewBoolValue(true),
			"Code":       structpb.NewNumberValue(float64(Error_ErrEmptyDateTo)), // 1046
		},
	})

	ErrEmptyDateTo = errors.GoErr{
		ErrType:    errors.ErrorTypeBadRequest, // "ErrorTypeBadRequest"
		Status:     statusErrEmptyDateTo,
		Code:       int(Error_ErrEmptyDateTo), // 1046
		Message:    "received an empty dateTo parameter",
		GrpcCode:   codes.InvalidArgument,
		IsExpected: true,
	}
)

var (
	statusErrBillingTransactionTypeNotFound, _ = status.New(
		codes.DataLoss,
		"billing transaction type not found",
	).WithDetails(&structpb.Struct{
		Fields: map[string]*structpb.Value{
			"GrpcCode":   structpb.NewNumberValue(float64(codes.DataLoss)),
			"Message":    structpb.NewStringValue("billing transaction type not found"),
			"ErrorType":  structpb.NewStringValue(errors.ErrorTypeUnexpectedError), // "ErrorTypeUnexpectedError"
			"IsExpected": structpb.NewBoolValue(true),
			"Code":       structpb.NewNumberValue(float64(Error_ErrBillingTransactionTypeNotFound)), // 1047
		},
	})

	ErrBillingTransactionTypeNotFound = errors.GoErr{
		ErrType:    errors.ErrorTypeUnexpectedError, // "ErrorTypeUnexpectedError"
		Status:     statusErrBillingTransactionTypeNotFound,
		Code:       int(Error_ErrBillingTransactionTypeNotFound), // 1047
		Message:    "billing transaction type not found",
		GrpcCode:   codes.DataLoss,
		IsExpected: true,
	}
)

var (
	statusErrEmptyPayInRequest, _ = status.New(
		codes.InvalidArgument,
		"received empty payin request",
	).WithDetails(&structpb.Struct{
		Fields: map[string]*structpb.Value{
			"GrpcCode":   structpb.NewNumberValue(float64(codes.InvalidArgument)),
			"Message":    structpb.NewStringValue("received empty payin request"),
			"ErrorType":  structpb.NewStringValue(errors.ErrorTypeBadRequest), // "ErrorTypeBadRequest"
			"IsExpected": structpb.NewBoolValue(true),
			"Code":       structpb.NewNumberValue(float64(Error_ErrEmptyPayInRequest)), // 6000
		},
	})

	ErrEmptyPayInRequest = errors.GoErr{
		ErrType:    errors.ErrorTypeBadRequest, // "ErrorTypeBadRequest"
		Status:     statusErrEmptyPayInRequest,
		Code:       int(Error_ErrEmptyPayInRequest), // 6000
		Message:    "received empty payin request",
		GrpcCode:   codes.InvalidArgument,
		IsExpected: true,
	}
)

var (
	statusErrEmptyPayOutRequest, _ = status.New(
		codes.InvalidArgument,
		"received empty payout request",
	).WithDetails(&structpb.Struct{
		Fields: map[string]*structpb.Value{
			"GrpcCode":   structpb.NewNumberValue(float64(codes.InvalidArgument)),
			"Message":    structpb.NewStringValue("received empty payout request"),
			"ErrorType":  structpb.NewStringValue(errors.ErrorTypeBadRequest), // "ErrorTypeBadRequest"
			"IsExpected": structpb.NewBoolValue(true),
			"Code":       structpb.NewNumberValue(float64(Error_ErrEmptyPayOutRequest)), // 6001
		},
	})

	ErrEmptyPayOutRequest = errors.GoErr{
		ErrType:    errors.ErrorTypeBadRequest, // "ErrorTypeBadRequest"
		Status:     statusErrEmptyPayOutRequest,
		Code:       int(Error_ErrEmptyPayOutRequest), // 6001
		Message:    "received empty payout request",
		GrpcCode:   codes.InvalidArgument,
		IsExpected: true,
	}
)

var (
	statusErrEmptyCardRequest, _ = status.New(
		codes.InvalidArgument,
		"received empty card body",
	).WithDetails(&structpb.Struct{
		Fields: map[string]*structpb.Value{
			"GrpcCode":   structpb.NewNumberValue(float64(codes.InvalidArgument)),
			"Message":    structpb.NewStringValue("received empty card body"),
			"ErrorType":  structpb.NewStringValue(errors.ErrorTypeBadRequest), // "ErrorTypeBadRequest"
			"IsExpected": structpb.NewBoolValue(true),
			"Code":       structpb.NewNumberValue(float64(Error_ErrEmptyCardRequest)), // 6002
		},
	})

	ErrEmptyCardRequest = errors.GoErr{
		ErrType:    errors.ErrorTypeBadRequest, // "ErrorTypeBadRequest"
		Status:     statusErrEmptyCardRequest,
		Code:       int(Error_ErrEmptyCardRequest), // 6002
		Message:    "received empty card body",
		GrpcCode:   codes.InvalidArgument,
		IsExpected: true,
	}
)

var (
	statusErrEmptyUserRequest, _ = status.New(
		codes.InvalidArgument,
		"received empty user body",
	).WithDetails(&structpb.Struct{
		Fields: map[string]*structpb.Value{
			"GrpcCode":   structpb.NewNumberValue(float64(codes.InvalidArgument)),
			"Message":    structpb.NewStringValue("received empty user body"),
			"ErrorType":  structpb.NewStringValue(errors.ErrorTypeBadRequest), // "ErrorTypeBadRequest"
			"IsExpected": structpb.NewBoolValue(true),
			"Code":       structpb.NewNumberValue(float64(Error_ErrEmptyUserRequest)), // 6003
		},
	})

	ErrEmptyUserRequest = errors.GoErr{
		ErrType:    errors.ErrorTypeBadRequest, // "ErrorTypeBadRequest"
		Status:     statusErrEmptyUserRequest,
		Code:       int(Error_ErrEmptyUserRequest), // 6003
		Message:    "received empty user body",
		GrpcCode:   codes.InvalidArgument,
		IsExpected: true,
	}
)

var (
	statusErrEmptyAcquirerRequest, _ = status.New(
		codes.InvalidArgument,
		"received empty acquirer body",
	).WithDetails(&structpb.Struct{
		Fields: map[string]*structpb.Value{
			"GrpcCode":   structpb.NewNumberValue(float64(codes.InvalidArgument)),
			"Message":    structpb.NewStringValue("received empty acquirer body"),
			"ErrorType":  structpb.NewStringValue(errors.ErrorTypeBadRequest), // "ErrorTypeBadRequest"
			"IsExpected": structpb.NewBoolValue(true),
			"Code":       structpb.NewNumberValue(float64(Error_ErrEmptyAcquirerRequest)), // 6004
		},
	})

	ErrEmptyAcquirerRequest = errors.GoErr{
		ErrType:    errors.ErrorTypeBadRequest, // "ErrorTypeBadRequest"
		Status:     statusErrEmptyAcquirerRequest,
		Code:       int(Error_ErrEmptyAcquirerRequest), // 6004
		Message:    "received empty acquirer body",
		GrpcCode:   codes.InvalidArgument,
		IsExpected: true,
	}
)

var (
	statusErrEmptyTerminalRequest, _ = status.New(
		codes.InvalidArgument,
		"received empty terminal body",
	).WithDetails(&structpb.Struct{
		Fields: map[string]*structpb.Value{
			"GrpcCode":   structpb.NewNumberValue(float64(codes.InvalidArgument)),
			"Message":    structpb.NewStringValue("received empty terminal body"),
			"ErrorType":  structpb.NewStringValue(errors.ErrorTypeBadRequest), // "ErrorTypeBadRequest"
			"IsExpected": structpb.NewBoolValue(true),
			"Code":       structpb.NewNumberValue(float64(Error_ErrEmptyTerminalRequest)), // 6005
		},
	})

	ErrEmptyTerminalRequest = errors.GoErr{
		ErrType:    errors.ErrorTypeBadRequest, // "ErrorTypeBadRequest"
		Status:     statusErrEmptyTerminalRequest,
		Code:       int(Error_ErrEmptyTerminalRequest), // 6005
		Message:    "received empty terminal body",
		GrpcCode:   codes.InvalidArgument,
		IsExpected: true,
	}
)

var (
	statusErrEmptyPayInResponse, _ = status.New(
		codes.InvalidArgument,
		"received empty payin response",
	).WithDetails(&structpb.Struct{
		Fields: map[string]*structpb.Value{
			"GrpcCode":   structpb.NewNumberValue(float64(codes.InvalidArgument)),
			"Message":    structpb.NewStringValue("received empty payin response"),
			"ErrorType":  structpb.NewStringValue(errors.ErrorTypeBadRequest), // "ErrorTypeBadRequest"
			"IsExpected": structpb.NewBoolValue(true),
			"Code":       structpb.NewNumberValue(float64(Error_ErrEmptyPayInResponse)), // 6006
		},
	})

	ErrEmptyPayInResponse = errors.GoErr{
		ErrType:    errors.ErrorTypeBadRequest, // "ErrorTypeBadRequest"
		Status:     statusErrEmptyPayInResponse,
		Code:       int(Error_ErrEmptyPayInResponse), // 6006
		Message:    "received empty payin response",
		GrpcCode:   codes.InvalidArgument,
		IsExpected: true,
	}
)

var (
	statusErrEmptyPayOutResponse, _ = status.New(
		codes.InvalidArgument,
		"received empty payout response",
	).WithDetails(&structpb.Struct{
		Fields: map[string]*structpb.Value{
			"GrpcCode":   structpb.NewNumberValue(float64(codes.InvalidArgument)),
			"Message":    structpb.NewStringValue("received empty payout response"),
			"ErrorType":  structpb.NewStringValue(errors.ErrorTypeBadRequest), // "ErrorTypeBadRequest"
			"IsExpected": structpb.NewBoolValue(true),
			"Code":       structpb.NewNumberValue(float64(Error_ErrEmptyPayOutResponse)), // 6007
		},
	})

	ErrEmptyPayOutResponse = errors.GoErr{
		ErrType:    errors.ErrorTypeBadRequest, // "ErrorTypeBadRequest"
		Status:     statusErrEmptyPayOutResponse,
		Code:       int(Error_ErrEmptyPayOutResponse), // 6007
		Message:    "received empty payout response",
		GrpcCode:   codes.InvalidArgument,
		IsExpected: true,
	}
)

var (
	statusErrEmptyChargeRequest, _ = status.New(
		codes.InvalidArgument,
		"received empty charge request",
	).WithDetails(&structpb.Struct{
		Fields: map[string]*structpb.Value{
			"GrpcCode":   structpb.NewNumberValue(float64(codes.InvalidArgument)),
			"Message":    structpb.NewStringValue("received empty charge request"),
			"ErrorType":  structpb.NewStringValue(errors.ErrorTypeBadRequest), // "ErrorTypeBadRequest"
			"IsExpected": structpb.NewBoolValue(true),
			"Code":       structpb.NewNumberValue(float64(Error_ErrEmptyChargeRequest)), // 6008
		},
	})

	ErrEmptyChargeRequest = errors.GoErr{
		ErrType:    errors.ErrorTypeBadRequest, // "ErrorTypeBadRequest"
		Status:     statusErrEmptyChargeRequest,
		Code:       int(Error_ErrEmptyChargeRequest), // 6008
		Message:    "received empty charge request",
		GrpcCode:   codes.InvalidArgument,
		IsExpected: true,
	}
)

var (
	statusErrEmptyCancelRequest, _ = status.New(
		codes.InvalidArgument,
		"received empty cancel request",
	).WithDetails(&structpb.Struct{
		Fields: map[string]*structpb.Value{
			"GrpcCode":   structpb.NewNumberValue(float64(codes.InvalidArgument)),
			"Message":    structpb.NewStringValue("received empty cancel request"),
			"ErrorType":  structpb.NewStringValue(errors.ErrorTypeBadRequest), // "ErrorTypeBadRequest"
			"IsExpected": structpb.NewBoolValue(true),
			"Code":       structpb.NewNumberValue(float64(Error_ErrEmptyCancelRequest)), // 6009
		},
	})

	ErrEmptyCancelRequest = errors.GoErr{
		ErrType:    errors.ErrorTypeBadRequest, // "ErrorTypeBadRequest"
		Status:     statusErrEmptyCancelRequest,
		Code:       int(Error_ErrEmptyCancelRequest), // 6009
		Message:    "received empty cancel request",
		GrpcCode:   codes.InvalidArgument,
		IsExpected: true,
	}
)

var (
	statusErrEmptyRefundRequest, _ = status.New(
		codes.InvalidArgument,
		"received empty refund request",
	).WithDetails(&structpb.Struct{
		Fields: map[string]*structpb.Value{
			"GrpcCode":   structpb.NewNumberValue(float64(codes.InvalidArgument)),
			"Message":    structpb.NewStringValue("received empty refund request"),
			"ErrorType":  structpb.NewStringValue(errors.ErrorTypeBadRequest), // "ErrorTypeBadRequest"
			"IsExpected": structpb.NewBoolValue(true),
			"Code":       structpb.NewNumberValue(float64(Error_ErrEmptyRefundRequest)), // 6200
		},
	})

	ErrEmptyRefundRequest = errors.GoErr{
		ErrType:    errors.ErrorTypeBadRequest, // "ErrorTypeBadRequest"
		Status:     statusErrEmptyRefundRequest,
		Code:       int(Error_ErrEmptyRefundRequest), // 6200
		Message:    "received empty refund request",
		GrpcCode:   codes.InvalidArgument,
		IsExpected: true,
	}
)

var (
	statusErrEmptyRefundResponse, _ = status.New(
		codes.InvalidArgument,
		"received empty refund response",
	).WithDetails(&structpb.Struct{
		Fields: map[string]*structpb.Value{
			"GrpcCode":   structpb.NewNumberValue(float64(codes.InvalidArgument)),
			"Message":    structpb.NewStringValue("received empty refund response"),
			"ErrorType":  structpb.NewStringValue(errors.ErrorTypeBadRequest), // "ErrorTypeBadRequest"
			"IsExpected": structpb.NewBoolValue(true),
			"Code":       structpb.NewNumberValue(float64(Error_ErrEmptyRefundResponse)), // 6201
		},
	})

	ErrEmptyRefundResponse = errors.GoErr{
		ErrType:    errors.ErrorTypeBadRequest, // "ErrorTypeBadRequest"
		Status:     statusErrEmptyRefundResponse,
		Code:       int(Error_ErrEmptyRefundResponse), // 6201
		Message:    "received empty refund response",
		GrpcCode:   codes.InvalidArgument,
		IsExpected: true,
	}
)

var (
	statusErrEmptyAmountRequest, _ = status.New(
		codes.InvalidArgument,
		"received empty amount request",
	).WithDetails(&structpb.Struct{
		Fields: map[string]*structpb.Value{
			"GrpcCode":   structpb.NewNumberValue(float64(codes.InvalidArgument)),
			"Message":    structpb.NewStringValue("received empty amount request"),
			"ErrorType":  structpb.NewStringValue(errors.ErrorTypeBadRequest), // "ErrorTypeBadRequest"
			"IsExpected": structpb.NewBoolValue(true),
			"Code":       structpb.NewNumberValue(float64(Error_ErrEmptyAmountRequest)), // 6202
		},
	})

	ErrEmptyAmountRequest = errors.GoErr{
		ErrType:    errors.ErrorTypeBadRequest, // "ErrorTypeBadRequest"
		Status:     statusErrEmptyAmountRequest,
		Code:       int(Error_ErrEmptyAmountRequest), // 6202
		Message:    "received empty amount request",
		GrpcCode:   codes.InvalidArgument,
		IsExpected: true,
	}
)

var (
	statusErrEmptyTransactionIDRequest, _ = status.New(
		codes.InvalidArgument,
		"received empty transaction_id request",
	).WithDetails(&structpb.Struct{
		Fields: map[string]*structpb.Value{
			"GrpcCode":   structpb.NewNumberValue(float64(codes.InvalidArgument)),
			"Message":    structpb.NewStringValue("received empty transaction_id request"),
			"ErrorType":  structpb.NewStringValue(errors.ErrorTypeBadRequest), // "ErrorTypeBadRequest"
			"IsExpected": structpb.NewBoolValue(true),
			"Code":       structpb.NewNumberValue(float64(Error_ErrEmptyTransactionIDRequest)), // 6203
		},
	})

	ErrEmptyTransactionIDRequest = errors.GoErr{
		ErrType:    errors.ErrorTypeBadRequest, // "ErrorTypeBadRequest"
		Status:     statusErrEmptyTransactionIDRequest,
		Code:       int(Error_ErrEmptyTransactionIDRequest), // 6203
		Message:    "received empty transaction_id request",
		GrpcCode:   codes.InvalidArgument,
		IsExpected: true,
	}
)

var (
	statusErrThreeDSParametersNotFound, _ = status.New(
		codes.NotFound,
		"threeDS parameters doesn't exist",
	).WithDetails(&structpb.Struct{
		Fields: map[string]*structpb.Value{
			"GrpcCode":   structpb.NewNumberValue(float64(codes.NotFound)),
			"Message":    structpb.NewStringValue("threeDS parameters doesn't exist"),
			"ErrorType":  structpb.NewStringValue(errors.ErrorTypeNotFound), // "ErrorTypeNotFound"
			"IsExpected": structpb.NewBoolValue(true),
			"Code":       structpb.NewNumberValue(float64(Error_ErrThreeDSParametersNotFound)), // 6100
		},
	})

	ErrThreeDSParametersNotFound = errors.GoErr{
		ErrType:    errors.ErrorTypeNotFound, // "ErrorTypeNotFound"
		Status:     statusErrThreeDSParametersNotFound,
		Code:       int(Error_ErrThreeDSParametersNotFound), // 6100
		Message:    "threeDS parameters doesn't exist",
		GrpcCode:   codes.NotFound,
		IsExpected: true,
	}
)

var (
	statusErrZoneNumOrCoordinatesRequired, _ = status.New(
		codes.InvalidArgument,
		"zone number or coordinates are required",
	).WithDetails(&structpb.Struct{
		Fields: map[string]*structpb.Value{
			"GrpcCode":   structpb.NewNumberValue(float64(codes.InvalidArgument)),
			"Message":    structpb.NewStringValue("zone number or coordinates are required"),
			"ErrorType":  structpb.NewStringValue(errors.ErrorTypeBadRequest), // "ErrorTypeBadRequest"
			"IsExpected": structpb.NewBoolValue(true),
			"Code":       structpb.NewNumberValue(float64(Error_ErrZoneNumOrCoordinatesRequired)), // 6101
		},
	})

	ErrZoneNumOrCoordinatesRequired = errors.GoErr{
		ErrType:    errors.ErrorTypeBadRequest, // "ErrorTypeBadRequest"
		Status:     statusErrZoneNumOrCoordinatesRequired,
		Code:       int(Error_ErrZoneNumOrCoordinatesRequired), // 6101
		Message:    "zone number or coordinates are required",
		GrpcCode:   codes.InvalidArgument,
		IsExpected: true,
	}
)

var (
	statusErrCommissionIsRequired, _ = status.New(
		codes.InvalidArgument,
		"commission is required",
	).WithDetails(&structpb.Struct{
		Fields: map[string]*structpb.Value{
			"GrpcCode":   structpb.NewNumberValue(float64(codes.InvalidArgument)),
			"Message":    structpb.NewStringValue("commission is required"),
			"ErrorType":  structpb.NewStringValue(errors.ErrorTypeBadRequest), // "ErrorTypeBadRequest"
			"IsExpected": structpb.NewBoolValue(true),
			"Code":       structpb.NewNumberValue(float64(Error_ErrCommissionIsRequired)), // 6102
		},
	})

	ErrCommissionIsRequired = errors.GoErr{
		ErrType:    errors.ErrorTypeBadRequest, // "ErrorTypeBadRequest"
		Status:     statusErrCommissionIsRequired,
		Code:       int(Error_ErrCommissionIsRequired), // 6102
		Message:    "commission is required",
		GrpcCode:   codes.InvalidArgument,
		IsExpected: true,
	}
)

var (
	statusErrEmptyDate, _ = status.New(
		codes.InvalidArgument,
		"empty date",
	).WithDetails(&structpb.Struct{
		Fields: map[string]*structpb.Value{
			"GrpcCode":   structpb.NewNumberValue(float64(codes.InvalidArgument)),
			"Message":    structpb.NewStringValue("empty date"),
			"ErrorType":  structpb.NewStringValue(errors.ErrorTypeBadRequest), // "ErrorTypeBadRequest"
			"IsExpected": structpb.NewBoolValue(true),
			"Code":       structpb.NewNumberValue(float64(Error_ErrEmptyDate)), // 6103
		},
	})

	ErrEmptyDate = errors.GoErr{
		ErrType:    errors.ErrorTypeBadRequest, // "ErrorTypeBadRequest"
		Status:     statusErrEmptyDate,
		Code:       int(Error_ErrEmptyDate), // 6103
		Message:    "empty date",
		GrpcCode:   codes.InvalidArgument,
		IsExpected: true,
	}
)

var (
	statusErrEmptyCustomerInfo, _ = status.New(
		codes.InvalidArgument,
		"empty customer info",
	).WithDetails(&structpb.Struct{
		Fields: map[string]*structpb.Value{
			"GrpcCode":   structpb.NewNumberValue(float64(codes.InvalidArgument)),
			"Message":    structpb.NewStringValue("empty customer info"),
			"ErrorType":  structpb.NewStringValue(errors.ErrorTypeBadRequest), // "ErrorTypeBadRequest"
			"IsExpected": structpb.NewBoolValue(true),
			"Code":       structpb.NewNumberValue(float64(Error_ErrEmptyCustomerInfo)), // 6104
		},
	})

	ErrEmptyCustomerInfo = errors.GoErr{
		ErrType:    errors.ErrorTypeBadRequest, // "ErrorTypeBadRequest"
		Status:     statusErrEmptyCustomerInfo,
		Code:       int(Error_ErrEmptyCustomerInfo), // 6104
		Message:    "empty customer info",
		GrpcCode:   codes.InvalidArgument,
		IsExpected: true,
	}
)

var (
	statusErrFiscalizationDataNotFound, _ = status.New(
		codes.NotFound,
		"fiscalization data not found",
	).WithDetails(&structpb.Struct{
		Fields: map[string]*structpb.Value{
			"GrpcCode":   structpb.NewNumberValue(float64(codes.NotFound)),
			"Message":    structpb.NewStringValue("fiscalization data not found"),
			"ErrorType":  structpb.NewStringValue(errors.ErrorTypeNotFound), // "ErrorTypeNotFound"
			"IsExpected": structpb.NewBoolValue(true),
			"Code":       structpb.NewNumberValue(float64(Error_ErrFiscalizationDataNotFound)), // 6105
		},
	})

	ErrFiscalizationDataNotFound = errors.GoErr{
		ErrType:    errors.ErrorTypeNotFound, // "ErrorTypeNotFound"
		Status:     statusErrFiscalizationDataNotFound,
		Code:       int(Error_ErrFiscalizationDataNotFound), // 6105
		Message:    "fiscalization data not found",
		GrpcCode:   codes.NotFound,
		IsExpected: true,
	}
)

var (
	statusErrIssuerValidation, _ = status.New(
		codes.InvalidArgument,
		"incompatible values of is_acquirer and issuer_id",
	).WithDetails(&structpb.Struct{
		Fields: map[string]*structpb.Value{
			"GrpcCode":   structpb.NewNumberValue(float64(codes.InvalidArgument)),
			"Message":    structpb.NewStringValue("incompatible values of is_acquirer and issuer_id"),
			"ErrorType":  structpb.NewStringValue(errors.ErrorTypeBadRequest), // "ErrorTypeBadRequest"
			"IsExpected": structpb.NewBoolValue(true),
			"Code":       structpb.NewNumberValue(float64(Error_ErrIssuerValidation)), // 3009
		},
	})

	ErrIssuerValidation = errors.GoErr{
		ErrType:    errors.ErrorTypeBadRequest, // "ErrorTypeBadRequest"
		Status:     statusErrIssuerValidation,
		Code:       int(Error_ErrIssuerValidation), // 3009
		Message:    "incompatible values of is_acquirer and issuer_id",
		GrpcCode:   codes.InvalidArgument,
		IsExpected: true,
	}
)

var (
	statusErrStatusValidation, _ = status.New(
		codes.InvalidArgument,
		"inoperable transaction status for this method",
	).WithDetails(&structpb.Struct{
		Fields: map[string]*structpb.Value{
			"GrpcCode":   structpb.NewNumberValue(float64(codes.InvalidArgument)),
			"Message":    structpb.NewStringValue("inoperable transaction status for this method"),
			"ErrorType":  structpb.NewStringValue(errors.ErrorTypeBadRequest), // "ErrorTypeBadRequest"
			"IsExpected": structpb.NewBoolValue(true),
			"Code":       structpb.NewNumberValue(float64(Error_ErrStatusValidation)), // 3010
		},
	})

	ErrStatusValidation = errors.GoErr{
		ErrType:    errors.ErrorTypeBadRequest, // "ErrorTypeBadRequest"
		Status:     statusErrStatusValidation,
		Code:       int(Error_ErrStatusValidation), // 3010
		Message:    "inoperable transaction status for this method",
		GrpcCode:   codes.InvalidArgument,
		IsExpected: true,
	}
)

var (
	statusErrDateValidation, _ = status.New(
		codes.InvalidArgument,
		"incorrect format of date",
	).WithDetails(&structpb.Struct{
		Fields: map[string]*structpb.Value{
			"GrpcCode":   structpb.NewNumberValue(float64(codes.InvalidArgument)),
			"Message":    structpb.NewStringValue("incorrect format of date"),
			"ErrorType":  structpb.NewStringValue(errors.ErrorTypeBadRequest), // "ErrorTypeBadRequest"
			"IsExpected": structpb.NewBoolValue(true),
			"Code":       structpb.NewNumberValue(float64(Error_ErrDateValidation)), // 3011
		},
	})

	ErrDateValidation = errors.GoErr{
		ErrType:    errors.ErrorTypeBadRequest, // "ErrorTypeBadRequest"
		Status:     statusErrDateValidation,
		Code:       int(Error_ErrDateValidation), // 3011
		Message:    "incorrect format of date",
		GrpcCode:   codes.InvalidArgument,
		IsExpected: true,
	}
)

var (
	statusErrCvcValidation, _ = status.New(
		codes.InvalidArgument,
		"incorrect format of cvc",
	).WithDetails(&structpb.Struct{
		Fields: map[string]*structpb.Value{
			"GrpcCode":   structpb.NewNumberValue(float64(codes.InvalidArgument)),
			"Message":    structpb.NewStringValue("incorrect format of cvc"),
			"ErrorType":  structpb.NewStringValue(errors.ErrorTypeBadRequest), // "ErrorTypeBadRequest"
			"IsExpected": structpb.NewBoolValue(true),
			"Code":       structpb.NewNumberValue(float64(Error_ErrCvcValidation)), // 3012
		},
	})

	ErrCvcValidation = errors.GoErr{
		ErrType:    errors.ErrorTypeBadRequest, // "ErrorTypeBadRequest"
		Status:     statusErrCvcValidation,
		Code:       int(Error_ErrCvcValidation), // 3012
		Message:    "incorrect format of cvc",
		GrpcCode:   codes.InvalidArgument,
		IsExpected: true,
	}
)

var (
	statusErrReportScheduleUnauthorized, _ = status.New(
		codes.InvalidArgument,
		"project report schedule unauthorized for your merchant",
	).WithDetails(&structpb.Struct{
		Fields: map[string]*structpb.Value{
			"GrpcCode":   structpb.NewNumberValue(float64(codes.InvalidArgument)),
			"Message":    structpb.NewStringValue("project report schedule unauthorized for your merchant"),
			"ErrorType":  structpb.NewStringValue(errors.ErrorTypeBadRequest), // "ErrorTypeBadRequest"
			"IsExpected": structpb.NewBoolValue(true),
			"Code":       structpb.NewNumberValue(float64(Error_ErrReportScheduleUnauthorized)), // 1304
		},
	})

	ErrReportScheduleUnauthorized = errors.GoErr{
		ErrType:    errors.ErrorTypeBadRequest, // "ErrorTypeBadRequest"
		Status:     statusErrReportScheduleUnauthorized,
		Code:       int(Error_ErrReportScheduleUnauthorized), // 1304
		Message:    "project report schedule unauthorized for your merchant",
		GrpcCode:   codes.InvalidArgument,
		IsExpected: true,
	}
)

var (
	statusErrInvalidThreeDSParams, _ = status.New(
		codes.InvalidArgument,
		"invalid secure params",
	).WithDetails(&structpb.Struct{
		Fields: map[string]*structpb.Value{
			"GrpcCode":   structpb.NewNumberValue(float64(codes.InvalidArgument)),
			"Message":    structpb.NewStringValue("invalid secure params"),
			"ErrorType":  structpb.NewStringValue(errors.ErrorTypeBadRequest), // "ErrorTypeBadRequest"
			"IsExpected": structpb.NewBoolValue(true),
			"Code":       structpb.NewNumberValue(float64(Error_ErrInvalidThreeDSParams)), // 1202
		},
	})

	ErrInvalidThreeDSParams = errors.GoErr{
		ErrType:    errors.ErrorTypeBadRequest, // "ErrorTypeBadRequest"
		Status:     statusErrInvalidThreeDSParams,
		Code:       int(Error_ErrInvalidThreeDSParams), // 1202
		Message:    "invalid secure params",
		GrpcCode:   codes.InvalidArgument,
		IsExpected: true,
	}
)

var (
	statusErrExceedsSizeFile, _ = status.New(
		codes.InvalidArgument,
		"file exceeds allowed size",
	).WithDetails(&structpb.Struct{
		Fields: map[string]*structpb.Value{
			"GrpcCode":   structpb.NewNumberValue(float64(codes.InvalidArgument)),
			"Message":    structpb.NewStringValue("file exceeds allowed size"),
			"ErrorType":  structpb.NewStringValue(errors.ErrorTypeBadRequest), // "ErrorTypeBadRequest"
			"IsExpected": structpb.NewBoolValue(true),
			"Code":       structpb.NewNumberValue(float64(Error_ErrExceedsSizeFile)), // 1204
		},
	})

	ErrExceedsSizeFile = errors.GoErr{
		ErrType:    errors.ErrorTypeBadRequest, // "ErrorTypeBadRequest"
		Status:     statusErrExceedsSizeFile,
		Code:       int(Error_ErrExceedsSizeFile), // 1204
		Message:    "file exceeds allowed size",
		GrpcCode:   codes.InvalidArgument,
		IsExpected: true,
	}
)

var (
	statusErrEmptyValidationRequest, _ = status.New(
		codes.InvalidArgument,
		"empty request",
	).WithDetails(&structpb.Struct{
		Fields: map[string]*structpb.Value{
			"GrpcCode":   structpb.NewNumberValue(float64(codes.InvalidArgument)),
			"Message":    structpb.NewStringValue("empty request"),
			"ErrorType":  structpb.NewStringValue(errors.ErrorTypeBadRequest), // "ErrorTypeBadRequest"
			"IsExpected": structpb.NewBoolValue(true),
			"Code":       structpb.NewNumberValue(float64(Error_ErrEmptyValidationRequest)), // 1205
		},
	})

	ErrEmptyValidationRequest = errors.GoErr{
		ErrType:    errors.ErrorTypeBadRequest, // "ErrorTypeBadRequest"
		Status:     statusErrEmptyValidationRequest,
		Code:       int(Error_ErrEmptyValidationRequest), // 1205
		Message:    "empty request",
		GrpcCode:   codes.InvalidArgument,
		IsExpected: true,
	}
)

var (
	statusErrEmptyUserLogin, _ = status.New(
		codes.InvalidArgument,
		"empty user login",
	).WithDetails(&structpb.Struct{
		Fields: map[string]*structpb.Value{
			"GrpcCode":   structpb.NewNumberValue(float64(codes.InvalidArgument)),
			"Message":    structpb.NewStringValue("empty user login"),
			"ErrorType":  structpb.NewStringValue(errors.ErrorTypeBadRequest), // "ErrorTypeBadRequest"
			"IsExpected": structpb.NewBoolValue(true),
			"Code":       structpb.NewNumberValue(float64(Error_ErrEmptyUserLogin)), // 1206
		},
	})

	ErrEmptyUserLogin = errors.GoErr{
		ErrType:    errors.ErrorTypeBadRequest, // "ErrorTypeBadRequest"
		Status:     statusErrEmptyUserLogin,
		Code:       int(Error_ErrEmptyUserLogin), // 1206
		Message:    "empty user login",
		GrpcCode:   codes.InvalidArgument,
		IsExpected: true,
	}
)

var (
	statusErrEmptyDescription, _ = status.New(
		codes.InvalidArgument,
		"empty description",
	).WithDetails(&structpb.Struct{
		Fields: map[string]*structpb.Value{
			"GrpcCode":   structpb.NewNumberValue(float64(codes.InvalidArgument)),
			"Message":    structpb.NewStringValue("empty description"),
			"ErrorType":  structpb.NewStringValue(errors.ErrorTypeBadRequest), // "ErrorTypeBadRequest"
			"IsExpected": structpb.NewBoolValue(true),
			"Code":       structpb.NewNumberValue(float64(Error_ErrEmptyDescription)), // 1423
		},
	})

	ErrEmptyDescription = errors.GoErr{
		ErrType:    errors.ErrorTypeBadRequest, // "ErrorTypeBadRequest"
		Status:     statusErrEmptyDescription,
		Code:       int(Error_ErrEmptyDescription), // 1423
		Message:    "empty description",
		GrpcCode:   codes.InvalidArgument,
		IsExpected: true,
	}
)

var (
	statusErrWrongEndDateFormat, _ = status.New(
		codes.InvalidArgument,
		"end date must be later than start date",
	).WithDetails(&structpb.Struct{
		Fields: map[string]*structpb.Value{
			"GrpcCode":   structpb.NewNumberValue(float64(codes.InvalidArgument)),
			"Message":    structpb.NewStringValue("end date must be later than start date"),
			"ErrorType":  structpb.NewStringValue(errors.ErrorTypeBadRequest), // "ErrorTypeBadRequest"
			"IsExpected": structpb.NewBoolValue(true),
			"Code":       structpb.NewNumberValue(float64(Error_ErrWrongEndDateFormat)), // 2013
		},
	})

	ErrWrongEndDateFormat = errors.GoErr{
		ErrType:    errors.ErrorTypeBadRequest, // "ErrorTypeBadRequest"
		Status:     statusErrWrongEndDateFormat,
		Code:       int(Error_ErrWrongEndDateFormat), // 2013
		Message:    "end date must be later than start date",
		GrpcCode:   codes.InvalidArgument,
		IsExpected: true,
	}
)

var (
	statusErrEmptyUINValue, _ = status.New(
		codes.InvalidArgument,
		"empty uin value",
	).WithDetails(&structpb.Struct{
		Fields: map[string]*structpb.Value{
			"GrpcCode":   structpb.NewNumberValue(float64(codes.InvalidArgument)),
			"Message":    structpb.NewStringValue("empty uin value"),
			"ErrorType":  structpb.NewStringValue(errors.ErrorTypeBadRequest), // "ErrorTypeBadRequest"
			"IsExpected": structpb.NewBoolValue(true),
			"Code":       structpb.NewNumberValue(float64(Error_ErrEmptyUINValue)), // 2202
		},
	})

	ErrEmptyUINValue = errors.GoErr{
		ErrType:    errors.ErrorTypeBadRequest, // "ErrorTypeBadRequest"
		Status:     statusErrEmptyUINValue,
		Code:       int(Error_ErrEmptyUINValue), // 2202
		Message:    "empty uin value",
		GrpcCode:   codes.InvalidArgument,
		IsExpected: true,
	}
)

var (
	statusErrInvalidUINLength, _ = status.New(
		codes.InvalidArgument,
		"invalid uin length",
	).WithDetails(&structpb.Struct{
		Fields: map[string]*structpb.Value{
			"GrpcCode":   structpb.NewNumberValue(float64(codes.InvalidArgument)),
			"Message":    structpb.NewStringValue("invalid uin length"),
			"ErrorType":  structpb.NewStringValue(errors.ErrorTypeBadRequest), // "ErrorTypeBadRequest"
			"IsExpected": structpb.NewBoolValue(true),
			"Code":       structpb.NewNumberValue(float64(Error_ErrInvalidUINLength)), // 2203
		},
	})

	ErrInvalidUINLength = errors.GoErr{
		ErrType:    errors.ErrorTypeBadRequest, // "ErrorTypeBadRequest"
		Status:     statusErrInvalidUINLength,
		Code:       int(Error_ErrInvalidUINLength), // 2203
		Message:    "invalid uin length",
		GrpcCode:   codes.InvalidArgument,
		IsExpected: true,
	}
)

var (
	statusErrEmptyUsername, _ = status.New(
		codes.InvalidArgument,
		"empty username",
	).WithDetails(&structpb.Struct{
		Fields: map[string]*structpb.Value{
			"GrpcCode":   structpb.NewNumberValue(float64(codes.InvalidArgument)),
			"Message":    structpb.NewStringValue("empty username"),
			"ErrorType":  structpb.NewStringValue(errors.ErrorTypeBadRequest), // "ErrorTypeBadRequest"
			"IsExpected": structpb.NewBoolValue(true),
			"Code":       structpb.NewNumberValue(float64(Error_ErrEmptyUsername)), // 1401
		},
	})

	ErrEmptyUsername = errors.GoErr{
		ErrType:    errors.ErrorTypeBadRequest, // "ErrorTypeBadRequest"
		Status:     statusErrEmptyUsername,
		Code:       int(Error_ErrEmptyUsername), // 1401
		Message:    "empty username",
		GrpcCode:   codes.InvalidArgument,
		IsExpected: true,
	}
)

var (
	statusErrEmptyShowCaseId, _ = status.New(
		codes.InvalidArgument,
		"empty show case id",
	).WithDetails(&structpb.Struct{
		Fields: map[string]*structpb.Value{
			"GrpcCode":   structpb.NewNumberValue(float64(codes.InvalidArgument)),
			"Message":    structpb.NewStringValue("empty show case id"),
			"ErrorType":  structpb.NewStringValue(errors.ErrorTypeBadRequest), // "ErrorTypeBadRequest"
			"IsExpected": structpb.NewBoolValue(true),
			"Code":       structpb.NewNumberValue(float64(Error_ErrEmptyShowCaseId)), // 1402
		},
	})

	ErrEmptyShowCaseId = errors.GoErr{
		ErrType:    errors.ErrorTypeBadRequest, // "ErrorTypeBadRequest"
		Status:     statusErrEmptyShowCaseId,
		Code:       int(Error_ErrEmptyShowCaseId), // 1402
		Message:    "empty show case id",
		GrpcCode:   codes.InvalidArgument,
		IsExpected: true,
	}
)

var (
	statusErrEmptyProjectId, _ = status.New(
		codes.InvalidArgument,
		"empty project id",
	).WithDetails(&structpb.Struct{
		Fields: map[string]*structpb.Value{
			"GrpcCode":   structpb.NewNumberValue(float64(codes.InvalidArgument)),
			"Message":    structpb.NewStringValue("empty project id"),
			"ErrorType":  structpb.NewStringValue(errors.ErrorTypeBadRequest), // "ErrorTypeBadRequest"
			"IsExpected": structpb.NewBoolValue(true),
			"Code":       structpb.NewNumberValue(float64(Error_ErrEmptyProjectId)), // 1403
		},
	})

	ErrEmptyProjectId = errors.GoErr{
		ErrType:    errors.ErrorTypeBadRequest, // "ErrorTypeBadRequest"
		Status:     statusErrEmptyProjectId,
		Code:       int(Error_ErrEmptyProjectId), // 1403
		Message:    "empty project id",
		GrpcCode:   codes.InvalidArgument,
		IsExpected: true,
	}
)

var (
	statusErrInvalidActionRequest, _ = status.New(
		codes.InvalidArgument,
		"invalid action request",
	).WithDetails(&structpb.Struct{
		Fields: map[string]*structpb.Value{
			"GrpcCode":   structpb.NewNumberValue(float64(codes.InvalidArgument)),
			"Message":    structpb.NewStringValue("invalid action request"),
			"ErrorType":  structpb.NewStringValue(errors.ErrorTypeBadRequest), // "ErrorTypeBadRequest"
			"IsExpected": structpb.NewBoolValue(true),
			"Code":       structpb.NewNumberValue(float64(Error_ErrInvalidActionRequest)), // 1404
		},
	})

	ErrInvalidActionRequest = errors.GoErr{
		ErrType:    errors.ErrorTypeBadRequest, // "ErrorTypeBadRequest"
		Status:     statusErrInvalidActionRequest,
		Code:       int(Error_ErrInvalidActionRequest), // 1404
		Message:    "invalid action request",
		GrpcCode:   codes.InvalidArgument,
		IsExpected: true,
	}
)

var (
	statusErrEndDateBeforeStartDate, _ = status.New(
		codes.InvalidArgument,
		"end date is before start date",
	).WithDetails(&structpb.Struct{
		Fields: map[string]*structpb.Value{
			"GrpcCode":   structpb.NewNumberValue(float64(codes.InvalidArgument)),
			"Message":    structpb.NewStringValue("end date is before start date"),
			"ErrorType":  structpb.NewStringValue(errors.ErrorTypeBadRequest), // "ErrorTypeBadRequest"
			"IsExpected": structpb.NewBoolValue(true),
			"Code":       structpb.NewNumberValue(float64(Error_ErrEndDateBeforeStartDate)), // 1419
		},
	})

	ErrEndDateBeforeStartDate = errors.GoErr{
		ErrType:    errors.ErrorTypeBadRequest, // "ErrorTypeBadRequest"
		Status:     statusErrEndDateBeforeStartDate,
		Code:       int(Error_ErrEndDateBeforeStartDate), // 1419
		Message:    "end date is before start date",
		GrpcCode:   codes.InvalidArgument,
		IsExpected: true,
	}
)

var (
	statusErrDateArrayLength, _ = status.New(
		codes.InvalidArgument,
		"the length of date array is not 2",
	).WithDetails(&structpb.Struct{
		Fields: map[string]*structpb.Value{
			"GrpcCode":   structpb.NewNumberValue(float64(codes.InvalidArgument)),
			"Message":    structpb.NewStringValue("the length of date array is not 2"),
			"ErrorType":  structpb.NewStringValue(errors.ErrorTypeBadRequest), // "ErrorTypeBadRequest"
			"IsExpected": structpb.NewBoolValue(true),
			"Code":       structpb.NewNumberValue(float64(Error_ErrDateArrayLength)), // 1420
		},
	})

	ErrDateArrayLength = errors.GoErr{
		ErrType:    errors.ErrorTypeBadRequest, // "ErrorTypeBadRequest"
		Status:     statusErrDateArrayLength,
		Code:       int(Error_ErrDateArrayLength), // 1420
		Message:    "the length of date array is not 2",
		GrpcCode:   codes.InvalidArgument,
		IsExpected: true,
	}
)

var (
	statusErrTimeValidation, _ = status.New(
		codes.InvalidArgument,
		"time validation error",
	).WithDetails(&structpb.Struct{
		Fields: map[string]*structpb.Value{
			"GrpcCode":   structpb.NewNumberValue(float64(codes.InvalidArgument)),
			"Message":    structpb.NewStringValue("time validation error"),
			"ErrorType":  structpb.NewStringValue(errors.ErrorTypeBadRequest), // "ErrorTypeBadRequest"
			"IsExpected": structpb.NewBoolValue(true),
			"Code":       structpb.NewNumberValue(float64(Error_ErrTimeValidation)), // 1421
		},
	})

	ErrTimeValidation = errors.GoErr{
		ErrType:    errors.ErrorTypeBadRequest, // "ErrorTypeBadRequest"
		Status:     statusErrTimeValidation,
		Code:       int(Error_ErrTimeValidation), // 1421
		Message:    "time validation error",
		GrpcCode:   codes.InvalidArgument,
		IsExpected: true,
	}
)

var (
	statusErrUpdateJobsMessage, _ = status.New(
		codes.InvalidArgument,
		"failed to update jobs message",
	).WithDetails(&structpb.Struct{
		Fields: map[string]*structpb.Value{
			"GrpcCode":   structpb.NewNumberValue(float64(codes.InvalidArgument)),
			"Message":    structpb.NewStringValue("failed to update jobs message"),
			"ErrorType":  structpb.NewStringValue(errors.ErrorTypeBadRequest), // "ErrorTypeBadRequest"
			"IsExpected": structpb.NewBoolValue(true),
			"Code":       structpb.NewNumberValue(float64(Error_ErrUpdateJobsMessage)), // 1422
		},
	})

	ErrUpdateJobsMessage = errors.GoErr{
		ErrType:    errors.ErrorTypeBadRequest, // "ErrorTypeBadRequest"
		Status:     statusErrUpdateJobsMessage,
		Code:       int(Error_ErrUpdateJobsMessage), // 1422
		Message:    "failed to update jobs message",
		GrpcCode:   codes.InvalidArgument,
		IsExpected: true,
	}
)

var (
	statusErrItemNotFound, _ = status.New(
		codes.NotFound,
		"cache: item not found",
	).WithDetails(&structpb.Struct{
		Fields: map[string]*structpb.Value{
			"GrpcCode":   structpb.NewNumberValue(float64(codes.NotFound)),
			"Message":    structpb.NewStringValue("cache: item not found"),
			"ErrorType":  structpb.NewStringValue(errors.ErrorTypeNotFound), // "ErrorTypeNotFound"
			"IsExpected": structpb.NewBoolValue(true),
			"Code":       structpb.NewNumberValue(float64(Error_ErrItemNotFound)), // 1407
		},
	})

	ErrItemNotFound = errors.GoErr{
		ErrType:    errors.ErrorTypeNotFound, // "ErrorTypeNotFound"
		Status:     statusErrItemNotFound,
		Code:       int(Error_ErrItemNotFound), // 1407
		Message:    "cache: item not found",
		GrpcCode:   codes.NotFound,
		IsExpected: true,
	}
)

var (
	statusErrInvalidTerminalData, _ = status.New(
		codes.InvalidArgument,
		"received invalid terminal data",
	).WithDetails(&structpb.Struct{
		Fields: map[string]*structpb.Value{
			"GrpcCode":   structpb.NewNumberValue(float64(codes.InvalidArgument)),
			"Message":    structpb.NewStringValue("received invalid terminal data"),
			"ErrorType":  structpb.NewStringValue(errors.ErrorTypeBadRequest), // "ErrorTypeBadRequest"
			"IsExpected": structpb.NewBoolValue(true),
			"Code":       structpb.NewNumberValue(float64(Error_ErrInvalidTerminalData)), // 7015
		},
	})

	ErrInvalidTerminalData = errors.GoErr{
		ErrType:    errors.ErrorTypeBadRequest, // "ErrorTypeBadRequest"
		Status:     statusErrInvalidTerminalData,
		Code:       int(Error_ErrInvalidTerminalData), // 7015
		Message:    "received invalid terminal data",
		GrpcCode:   codes.InvalidArgument,
		IsExpected: true,
	}
)

var (
	statusErrDateArrayLengthMismatch, _ = status.New(
		codes.Unknown,
		"date array length is not equal to 2",
	).WithDetails(&structpb.Struct{
		Fields: map[string]*structpb.Value{
			"GrpcCode":   structpb.NewNumberValue(float64(codes.Unknown)),
			"Message":    structpb.NewStringValue("date array length is not equal to 2"),
			"ErrorType":  structpb.NewStringValue(errors.ErrorTypeUnexpectedError), // "ErrorTypeUnexpectedError"
			"IsExpected": structpb.NewBoolValue(true),
			"Code":       structpb.NewNumberValue(float64(Error_ErrDateArrayLengthMismatch)), // 1408
		},
	})

	ErrDateArrayLengthMismatch = errors.GoErr{
		ErrType:    errors.ErrorTypeUnexpectedError, // "ErrorTypeUnexpectedError"
		Status:     statusErrDateArrayLengthMismatch,
		Code:       int(Error_ErrDateArrayLengthMismatch), // 1408
		Message:    "date array length is not equal to 2",
		GrpcCode:   codes.Unknown,
		IsExpected: true,
	}
)

var (
	statusErrExternalIdDuplicate, _ = status.New(
		codes.Unknown,
		"duplicate external id",
	).WithDetails(&structpb.Struct{
		Fields: map[string]*structpb.Value{
			"GrpcCode":   structpb.NewNumberValue(float64(codes.Unknown)),
			"Message":    structpb.NewStringValue("duplicate external id"),
			"ErrorType":  structpb.NewStringValue(errors.ErrorTypeUnexpectedError), // "ErrorTypeUnexpectedError"
			"IsExpected": structpb.NewBoolValue(true),
			"Code":       structpb.NewNumberValue(float64(Error_ErrExternalIdDuplicate)), // 1042
		},
	})

	ErrExternalIdDuplicate = errors.GoErr{
		ErrType:    errors.ErrorTypeUnexpectedError, // "ErrorTypeUnexpectedError"
		Status:     statusErrExternalIdDuplicate,
		Code:       int(Error_ErrExternalIdDuplicate), // 1042
		Message:    "duplicate external id",
		GrpcCode:   codes.Unknown,
		IsExpected: true,
	}
)

var (
	statusErrProcessedTransferNotFound, _ = status.New(
		codes.NotFound,
		"processed transfer doesn't exists",
	).WithDetails(&structpb.Struct{
		Fields: map[string]*structpb.Value{
			"GrpcCode":   structpb.NewNumberValue(float64(codes.NotFound)),
			"Message":    structpb.NewStringValue("processed transfer doesn't exists"),
			"ErrorType":  structpb.NewStringValue(errors.ErrorTypeNotFound), // "ErrorTypeNotFound"
			"IsExpected": structpb.NewBoolValue(true),
			"Code":       structpb.NewNumberValue(float64(Error_ErrProcessedTransferNotFound)), // 1147
		},
	})

	ErrProcessedTransferNotFound = errors.GoErr{
		ErrType:    errors.ErrorTypeNotFound, // "ErrorTypeNotFound"
		Status:     statusErrProcessedTransferNotFound,
		Code:       int(Error_ErrProcessedTransferNotFound), // 1147
		Message:    "processed transfer doesn't exists",
		GrpcCode:   codes.NotFound,
		IsExpected: true,
	}
)

var (
	statusErrTransferAlreadySplitted, _ = status.New(
		codes.InvalidArgument,
		"transfer is already splitted",
	).WithDetails(&structpb.Struct{
		Fields: map[string]*structpb.Value{
			"GrpcCode":   structpb.NewNumberValue(float64(codes.InvalidArgument)),
			"Message":    structpb.NewStringValue("transfer is already splitted"),
			"ErrorType":  structpb.NewStringValue(errors.ErrorTypeBadRequest), // "ErrorTypeBadRequest"
			"IsExpected": structpb.NewBoolValue(true),
			"Code":       structpb.NewNumberValue(float64(Error_ErrTransferAlreadySplitted)), // 1145
		},
	})

	ErrTransferAlreadySplitted = errors.GoErr{
		ErrType:    errors.ErrorTypeBadRequest, // "ErrorTypeBadRequest"
		Status:     statusErrTransferAlreadySplitted,
		Code:       int(Error_ErrTransferAlreadySplitted), // 1145
		Message:    "transfer is already splitted",
		GrpcCode:   codes.InvalidArgument,
		IsExpected: true,
	}
)

var (
	statusErrInoperableProcessedTransferStatus, _ = status.New(
		codes.InvalidArgument,
		"inoperable processed transfer status",
	).WithDetails(&structpb.Struct{
		Fields: map[string]*structpb.Value{
			"GrpcCode":   structpb.NewNumberValue(float64(codes.InvalidArgument)),
			"Message":    structpb.NewStringValue("inoperable processed transfer status"),
			"ErrorType":  structpb.NewStringValue(errors.ErrorTypeBadRequest), // "ErrorTypeBadRequest"
			"IsExpected": structpb.NewBoolValue(true),
			"Code":       structpb.NewNumberValue(float64(Error_ErrInoperableProcessedTransferStatus)), // 1146
		},
	})

	ErrInoperableProcessedTransferStatus = errors.GoErr{
		ErrType:    errors.ErrorTypeBadRequest, // "ErrorTypeBadRequest"
		Status:     statusErrInoperableProcessedTransferStatus,
		Code:       int(Error_ErrInoperableProcessedTransferStatus), // 1146
		Message:    "inoperable processed transfer status",
		GrpcCode:   codes.InvalidArgument,
		IsExpected: true,
	}
)

var (
	statusErrInvalidBalanceOwnerID, _ = status.New(
		codes.InvalidArgument,
		"invalid balance owner id",
	).WithDetails(&structpb.Struct{
		Fields: map[string]*structpb.Value{
			"GrpcCode":   structpb.NewNumberValue(float64(codes.InvalidArgument)),
			"Message":    structpb.NewStringValue("invalid balance owner id"),
			"ErrorType":  structpb.NewStringValue(errors.ErrorTypeBadRequest), // "ErrorTypeBadRequest"
			"IsExpected": structpb.NewBoolValue(true),
			"Code":       structpb.NewNumberValue(float64(Error_ErrInvalidBalanceOwnerID)), // 1148
		},
	})

	ErrInvalidBalanceOwnerID = errors.GoErr{
		ErrType:    errors.ErrorTypeBadRequest, // "ErrorTypeBadRequest"
		Status:     statusErrInvalidBalanceOwnerID,
		Code:       int(Error_ErrInvalidBalanceOwnerID), // 1148
		Message:    "invalid balance owner id",
		GrpcCode:   codes.InvalidArgument,
		IsExpected: true,
	}
)

var (
	statusErrInvalidFrequency, _ = status.New(
		codes.InvalidArgument,
		"invalid frequency",
	).WithDetails(&structpb.Struct{
		Fields: map[string]*structpb.Value{
			"GrpcCode":   structpb.NewNumberValue(float64(codes.InvalidArgument)),
			"Message":    structpb.NewStringValue("invalid frequency"),
			"ErrorType":  structpb.NewStringValue(errors.ErrorTypeBadRequest), // "ErrorTypeBadRequest"
			"IsExpected": structpb.NewBoolValue(true),
			"Code":       structpb.NewNumberValue(float64(Error_ErrInvalidFrequency)), // 1149
		},
	})

	ErrInvalidFrequency = errors.GoErr{
		ErrType:    errors.ErrorTypeBadRequest, // "ErrorTypeBadRequest"
		Status:     statusErrInvalidFrequency,
		Code:       int(Error_ErrInvalidFrequency), // 1149
		Message:    "invalid frequency",
		GrpcCode:   codes.InvalidArgument,
		IsExpected: true,
	}
)

var (
	statusErrInvalidAccountNumber, _ = status.New(
		codes.InvalidArgument,
		"invalid account number",
	).WithDetails(&structpb.Struct{
		Fields: map[string]*structpb.Value{
			"GrpcCode":   structpb.NewNumberValue(float64(codes.InvalidArgument)),
			"Message":    structpb.NewStringValue("invalid account number"),
			"ErrorType":  structpb.NewStringValue(errors.ErrorTypeBadRequest), // "ErrorTypeBadRequest"
			"IsExpected": structpb.NewBoolValue(true),
			"Code":       structpb.NewNumberValue(float64(Error_ErrInvalidAccountNumber)), // 1150
		},
	})

	ErrInvalidAccountNumber = errors.GoErr{
		ErrType:    errors.ErrorTypeBadRequest, // "ErrorTypeBadRequest"
		Status:     statusErrInvalidAccountNumber,
		Code:       int(Error_ErrInvalidAccountNumber), // 1150
		Message:    "invalid account number",
		GrpcCode:   codes.InvalidArgument,
		IsExpected: true,
	}
)

var (
	statusErrInvalidBeneficiaryCode, _ = status.New(
		codes.InvalidArgument,
		"invalid beneficiary code",
	).WithDetails(&structpb.Struct{
		Fields: map[string]*structpb.Value{
			"GrpcCode":   structpb.NewNumberValue(float64(codes.InvalidArgument)),
			"Message":    structpb.NewStringValue("invalid beneficiary code"),
			"ErrorType":  structpb.NewStringValue(errors.ErrorTypeBadRequest), // "ErrorTypeBadRequest"
			"IsExpected": structpb.NewBoolValue(true),
			"Code":       structpb.NewNumberValue(float64(Error_ErrInvalidBeneficiaryCode)), // 1151
		},
	})

	ErrInvalidBeneficiaryCode = errors.GoErr{
		ErrType:    errors.ErrorTypeBadRequest, // "ErrorTypeBadRequest"
		Status:     statusErrInvalidBeneficiaryCode,
		Code:       int(Error_ErrInvalidBeneficiaryCode), // 1151
		Message:    "invalid beneficiary code",
		GrpcCode:   codes.InvalidArgument,
		IsExpected: true,
	}
)

var (
	statusErrInvalidPaymentPurposeCode, _ = status.New(
		codes.InvalidArgument,
		"invalid payment purpose code",
	).WithDetails(&structpb.Struct{
		Fields: map[string]*structpb.Value{
			"GrpcCode":   structpb.NewNumberValue(float64(codes.InvalidArgument)),
			"Message":    structpb.NewStringValue("invalid payment purpose code"),
			"ErrorType":  structpb.NewStringValue(errors.ErrorTypeBadRequest), // "ErrorTypeBadRequest"
			"IsExpected": structpb.NewBoolValue(true),
			"Code":       structpb.NewNumberValue(float64(Error_ErrInvalidPaymentPurposeCode)), // 1152
		},
	})

	ErrInvalidPaymentPurposeCode = errors.GoErr{
		ErrType:    errors.ErrorTypeBadRequest, // "ErrorTypeBadRequest"
		Status:     statusErrInvalidPaymentPurposeCode,
		Code:       int(Error_ErrInvalidPaymentPurposeCode), // 1152
		Message:    "invalid payment purpose code",
		GrpcCode:   codes.InvalidArgument,
		IsExpected: true,
	}
)

var (
	statusErrInvalidRecipientAccount, _ = status.New(
		codes.InvalidArgument,
		"invalid recipient account",
	).WithDetails(&structpb.Struct{
		Fields: map[string]*structpb.Value{
			"GrpcCode":   structpb.NewNumberValue(float64(codes.InvalidArgument)),
			"Message":    structpb.NewStringValue("invalid recipient account"),
			"ErrorType":  structpb.NewStringValue(errors.ErrorTypeBadRequest), // "ErrorTypeBadRequest"
			"IsExpected": structpb.NewBoolValue(true),
			"Code":       structpb.NewNumberValue(float64(Error_ErrInvalidRecipientAccount)), // 1153
		},
	})

	ErrInvalidRecipientAccount = errors.GoErr{
		ErrType:    errors.ErrorTypeBadRequest, // "ErrorTypeBadRequest"
		Status:     statusErrInvalidRecipientAccount,
		Code:       int(Error_ErrInvalidRecipientAccount), // 1153
		Message:    "invalid recipient account",
		GrpcCode:   codes.InvalidArgument,
		IsExpected: true,
	}
)

var (
	statusErrInvalidAmount, _ = status.New(
		codes.InvalidArgument,
		"invalid amount",
	).WithDetails(&structpb.Struct{
		Fields: map[string]*structpb.Value{
			"GrpcCode":   structpb.NewNumberValue(float64(codes.InvalidArgument)),
			"Message":    structpb.NewStringValue("invalid amount"),
			"ErrorType":  structpb.NewStringValue(errors.ErrorTypeBadRequest), // "ErrorTypeBadRequest"
			"IsExpected": structpb.NewBoolValue(true),
			"Code":       structpb.NewNumberValue(float64(Error_ErrInvalidAmount)), // 1154
		},
	})

	ErrInvalidAmount = errors.GoErr{
		ErrType:    errors.ErrorTypeBadRequest, // "ErrorTypeBadRequest"
		Status:     statusErrInvalidAmount,
		Code:       int(Error_ErrInvalidAmount), // 1154
		Message:    "invalid amount",
		GrpcCode:   codes.InvalidArgument,
		IsExpected: true,
	}
)

var (
	statusErrBrokenTerminal, _ = status.New(
		codes.DataLoss,
		"terminal missing required fields",
	).WithDetails(&structpb.Struct{
		Fields: map[string]*structpb.Value{
			"GrpcCode":   structpb.NewNumberValue(float64(codes.DataLoss)),
			"Message":    structpb.NewStringValue("terminal missing required fields"),
			"ErrorType":  structpb.NewStringValue(errors.ErrorTypeDbViolation), // "ErrorTypeDbViolation"
			"IsExpected": structpb.NewBoolValue(true),
			"Code":       structpb.NewNumberValue(float64(Error_ErrBrokenTerminal)), // 9711
		},
	})

	ErrBrokenTerminal = errors.GoErr{
		ErrType:    errors.ErrorTypeDbViolation, // "ErrorTypeDbViolation"
		Status:     statusErrBrokenTerminal,
		Code:       int(Error_ErrBrokenTerminal), // 9711
		Message:    "terminal missing required fields",
		GrpcCode:   codes.DataLoss,
		IsExpected: true,
	}
)

var (
	statusErrProviderServiceRelationNotFound, _ = status.New(
		codes.DataLoss,
		"provider service relation not found",
	).WithDetails(&structpb.Struct{
		Fields: map[string]*structpb.Value{
			"GrpcCode":   structpb.NewNumberValue(float64(codes.DataLoss)),
			"Message":    structpb.NewStringValue("provider service relation not found"),
			"ErrorType":  structpb.NewStringValue(errors.ErrorTypeDbViolation), // "ErrorTypeDbViolation"
			"IsExpected": structpb.NewBoolValue(true),
			"Code":       structpb.NewNumberValue(float64(Error_ErrProviderServiceRelationNotFound)), // 9717
		},
	})

	ErrProviderServiceRelationNotFound = errors.GoErr{
		ErrType:    errors.ErrorTypeDbViolation, // "ErrorTypeDbViolation"
		Status:     statusErrProviderServiceRelationNotFound,
		Code:       int(Error_ErrProviderServiceRelationNotFound), // 9717
		Message:    "provider service relation not found",
		GrpcCode:   codes.DataLoss,
		IsExpected: true,
	}
)

var (
	statusErrProviderNotFound, _ = status.New(
		codes.DataLoss,
		"provider not found",
	).WithDetails(&structpb.Struct{
		Fields: map[string]*structpb.Value{
			"GrpcCode":   structpb.NewNumberValue(float64(codes.DataLoss)),
			"Message":    structpb.NewStringValue("provider not found"),
			"ErrorType":  structpb.NewStringValue(errors.ErrorTypeDbViolation), // "ErrorTypeDbViolation"
			"IsExpected": structpb.NewBoolValue(true),
			"Code":       structpb.NewNumberValue(float64(Error_ErrProviderNotFound)), // 9718
		},
	})

	ErrProviderNotFound = errors.GoErr{
		ErrType:    errors.ErrorTypeDbViolation, // "ErrorTypeDbViolation"
		Status:     statusErrProviderNotFound,
		Code:       int(Error_ErrProviderNotFound), // 9718
		Message:    "provider not found",
		GrpcCode:   codes.DataLoss,
		IsExpected: true,
	}
)

var (
	statusErrProviderServiceNotFound, _ = status.New(
		codes.DataLoss,
		"provider service not found",
	).WithDetails(&structpb.Struct{
		Fields: map[string]*structpb.Value{
			"GrpcCode":   structpb.NewNumberValue(float64(codes.DataLoss)),
			"Message":    structpb.NewStringValue("provider service not found"),
			"ErrorType":  structpb.NewStringValue(errors.ErrorTypeDbViolation), // "ErrorTypeDbViolation"
			"IsExpected": structpb.NewBoolValue(true),
			"Code":       structpb.NewNumberValue(float64(Error_ErrProviderServiceNotFound)), // 9719
		},
	})

	ErrProviderServiceNotFound = errors.GoErr{
		ErrType:    errors.ErrorTypeDbViolation, // "ErrorTypeDbViolation"
		Status:     statusErrProviderServiceNotFound,
		Code:       int(Error_ErrProviderServiceNotFound), // 9719
		Message:    "provider service not found",
		GrpcCode:   codes.DataLoss,
		IsExpected: true,
	}
)

var (
	statusErrProviderServiceStatusNotFound, _ = status.New(
		codes.DataLoss,
		"provider service status not found",
	).WithDetails(&structpb.Struct{
		Fields: map[string]*structpb.Value{
			"GrpcCode":   structpb.NewNumberValue(float64(codes.DataLoss)),
			"Message":    structpb.NewStringValue("provider service status not found"),
			"ErrorType":  structpb.NewStringValue(errors.ErrorTypeDbViolation), // "ErrorTypeDbViolation"
			"IsExpected": structpb.NewBoolValue(true),
			"Code":       structpb.NewNumberValue(float64(Error_ErrProviderServiceStatusNotFound)), // 9720
		},
	})

	ErrProviderServiceStatusNotFound = errors.GoErr{
		ErrType:    errors.ErrorTypeDbViolation, // "ErrorTypeDbViolation"
		Status:     statusErrProviderServiceStatusNotFound,
		Code:       int(Error_ErrProviderServiceStatusNotFound), // 9720
		Message:    "provider service status not found",
		GrpcCode:   codes.DataLoss,
		IsExpected: true,
	}
)

var (
	statusErrShowcaseServiceNotFound, _ = status.New(
		codes.DataLoss,
		"showcase service not found",
	).WithDetails(&structpb.Struct{
		Fields: map[string]*structpb.Value{
			"GrpcCode":   structpb.NewNumberValue(float64(codes.DataLoss)),
			"Message":    structpb.NewStringValue("showcase service not found"),
			"ErrorType":  structpb.NewStringValue(errors.ErrorTypeDbViolation), // "ErrorTypeDbViolation"
			"IsExpected": structpb.NewBoolValue(true),
			"Code":       structpb.NewNumberValue(float64(Error_ErrShowcaseServiceNotFound)), // 9721
		},
	})

	ErrShowcaseServiceNotFound = errors.GoErr{
		ErrType:    errors.ErrorTypeDbViolation, // "ErrorTypeDbViolation"
		Status:     statusErrShowcaseServiceNotFound,
		Code:       int(Error_ErrShowcaseServiceNotFound), // 9721
		Message:    "showcase service not found",
		GrpcCode:   codes.DataLoss,
		IsExpected: true,
	}
)

var (
	statusErrProjectStatusNotFound, _ = status.New(
		codes.DataLoss,
		"project status not found",
	).WithDetails(&structpb.Struct{
		Fields: map[string]*structpb.Value{
			"GrpcCode":   structpb.NewNumberValue(float64(codes.DataLoss)),
			"Message":    structpb.NewStringValue("project status not found"),
			"ErrorType":  structpb.NewStringValue(errors.ErrorTypeDbViolation), // "ErrorTypeDbViolation"
			"IsExpected": structpb.NewBoolValue(true),
			"Code":       structpb.NewNumberValue(float64(Error_ErrProjectStatusNotFound)), // 9722
		},
	})

	ErrProjectStatusNotFound = errors.GoErr{
		ErrType:    errors.ErrorTypeDbViolation, // "ErrorTypeDbViolation"
		Status:     statusErrProjectStatusNotFound,
		Code:       int(Error_ErrProjectStatusNotFound), // 9722
		Message:    "project status not found",
		GrpcCode:   codes.DataLoss,
		IsExpected: true,
	}
)

var (
	statusErrShowcaseStatusNotFound, _ = status.New(
		codes.DataLoss,
		"showcase status not found",
	).WithDetails(&structpb.Struct{
		Fields: map[string]*structpb.Value{
			"GrpcCode":   structpb.NewNumberValue(float64(codes.DataLoss)),
			"Message":    structpb.NewStringValue("showcase status not found"),
			"ErrorType":  structpb.NewStringValue(errors.ErrorTypeDbViolation), // "ErrorTypeDbViolation"
			"IsExpected": structpb.NewBoolValue(true),
			"Code":       structpb.NewNumberValue(float64(Error_ErrShowcaseStatusNotFound)), // 9723
		},
	})

	ErrShowcaseStatusNotFound = errors.GoErr{
		ErrType:    errors.ErrorTypeDbViolation, // "ErrorTypeDbViolation"
		Status:     statusErrShowcaseStatusNotFound,
		Code:       int(Error_ErrShowcaseStatusNotFound), // 9723
		Message:    "showcase status not found",
		GrpcCode:   codes.DataLoss,
		IsExpected: true,
	}
)

var (
	statusErrShowcaseNotFound, _ = status.New(
		codes.DataLoss,
		"showcase not found",
	).WithDetails(&structpb.Struct{
		Fields: map[string]*structpb.Value{
			"GrpcCode":   structpb.NewNumberValue(float64(codes.DataLoss)),
			"Message":    structpb.NewStringValue("showcase not found"),
			"ErrorType":  structpb.NewStringValue(errors.ErrorTypeDbViolation), // "ErrorTypeDbViolation"
			"IsExpected": structpb.NewBoolValue(true),
			"Code":       structpb.NewNumberValue(float64(Error_ErrShowcaseNotFound)), // 9724
		},
	})

	ErrShowcaseNotFound = errors.GoErr{
		ErrType:    errors.ErrorTypeDbViolation, // "ErrorTypeDbViolation"
		Status:     statusErrShowcaseNotFound,
		Code:       int(Error_ErrShowcaseNotFound), // 9724
		Message:    "showcase not found",
		GrpcCode:   codes.DataLoss,
		IsExpected: true,
	}
)

var (
	statusErrShowcaseServiceStatusNotFound, _ = status.New(
		codes.DataLoss,
		"showcase service status not found",
	).WithDetails(&structpb.Struct{
		Fields: map[string]*structpb.Value{
			"GrpcCode":   structpb.NewNumberValue(float64(codes.DataLoss)),
			"Message":    structpb.NewStringValue("showcase service status not found"),
			"ErrorType":  structpb.NewStringValue(errors.ErrorTypeDbViolation), // "ErrorTypeDbViolation"
			"IsExpected": structpb.NewBoolValue(true),
			"Code":       structpb.NewNumberValue(float64(Error_ErrShowcaseServiceStatusNotFound)), // 9725
		},
	})

	ErrShowcaseServiceStatusNotFound = errors.GoErr{
		ErrType:    errors.ErrorTypeDbViolation, // "ErrorTypeDbViolation"
		Status:     statusErrShowcaseServiceStatusNotFound,
		Code:       int(Error_ErrShowcaseServiceStatusNotFound), // 9725
		Message:    "showcase service status not found",
		GrpcCode:   codes.DataLoss,
		IsExpected: true,
	}
)

var (
	statusErrProjectLimitNotFound, _ = status.New(
		codes.DataLoss,
		"project limit not found",
	).WithDetails(&structpb.Struct{
		Fields: map[string]*structpb.Value{
			"GrpcCode":   structpb.NewNumberValue(float64(codes.DataLoss)),
			"Message":    structpb.NewStringValue("project limit not found"),
			"ErrorType":  structpb.NewStringValue(errors.ErrorTypeDbViolation), // "ErrorTypeDbViolation"
			"IsExpected": structpb.NewBoolValue(true),
			"Code":       structpb.NewNumberValue(float64(Error_ErrProjectLimitNotFound)), // 9726
		},
	})

	ErrProjectLimitNotFound = errors.GoErr{
		ErrType:    errors.ErrorTypeDbViolation, // "ErrorTypeDbViolation"
		Status:     statusErrProjectLimitNotFound,
		Code:       int(Error_ErrProjectLimitNotFound), // 9726
		Message:    "project limit not found",
		GrpcCode:   codes.DataLoss,
		IsExpected: true,
	}
)

var (
	statusErrUserNotFoundAccessList, _ = status.New(
		codes.DataLoss,
		"user not found in access list",
	).WithDetails(&structpb.Struct{
		Fields: map[string]*structpb.Value{
			"GrpcCode":   structpb.NewNumberValue(float64(codes.DataLoss)),
			"Message":    structpb.NewStringValue("user not found in access list"),
			"ErrorType":  structpb.NewStringValue(errors.ErrorTypeDbViolation), // "ErrorTypeDbViolation"
			"IsExpected": structpb.NewBoolValue(true),
			"Code":       structpb.NewNumberValue(float64(Error_ErrUserNotFoundAccessList)), // 9727
		},
	})

	ErrUserNotFoundAccessList = errors.GoErr{
		ErrType:    errors.ErrorTypeDbViolation, // "ErrorTypeDbViolation"
		Status:     statusErrUserNotFoundAccessList,
		Code:       int(Error_ErrUserNotFoundAccessList), // 9727
		Message:    "user not found in access list",
		GrpcCode:   codes.DataLoss,
		IsExpected: true,
	}
)

var (
	statusErrValidation, _ = status.New(
		codes.InvalidArgument,
		"Request validation error",
	).WithDetails(&structpb.Struct{
		Fields: map[string]*structpb.Value{
			"GrpcCode":   structpb.NewNumberValue(float64(codes.InvalidArgument)),
			"Message":    structpb.NewStringValue("Request validation error"),
			"ErrorType":  structpb.NewStringValue(errors.ErrorTypeBadRequest), // "ErrorTypeBadRequest"
			"IsExpected": structpb.NewBoolValue(true),
			"Code":       structpb.NewNumberValue(float64(Error_ErrValidation)), // 1010
		},
	})

	ErrValidation = errors.GoErr{
		ErrType:    errors.ErrorTypeBadRequest, // "ErrorTypeBadRequest"
		Status:     statusErrValidation,
		Code:       int(Error_ErrValidation), // 1010
		Message:    "Request validation error",
		GrpcCode:   codes.InvalidArgument,
		IsExpected: true,
	}
)

var (
	statusErrUserNameFieldType, _ = status.New(
		codes.InvalidArgument,
		"Invalid username field type",
	).WithDetails(&structpb.Struct{
		Fields: map[string]*structpb.Value{
			"GrpcCode":   structpb.NewNumberValue(float64(codes.InvalidArgument)),
			"Message":    structpb.NewStringValue("Invalid username field type"),
			"ErrorType":  structpb.NewStringValue(errors.ErrorTypeBadRequest), // "ErrorTypeBadRequest"
			"IsExpected": structpb.NewBoolValue(true),
			"Code":       structpb.NewNumberValue(float64(Error_ErrUserNameFieldType)), // 1011
		},
	})

	ErrUserNameFieldType = errors.GoErr{
		ErrType:    errors.ErrorTypeBadRequest, // "ErrorTypeBadRequest"
		Status:     statusErrUserNameFieldType,
		Code:       int(Error_ErrUserNameFieldType), // 1011
		Message:    "Invalid username field type",
		GrpcCode:   codes.InvalidArgument,
		IsExpected: true,
	}
)

var (
	statusErrAgentField, _ = status.New(
		codes.InvalidArgument,
		"Invalid agent field type",
	).WithDetails(&structpb.Struct{
		Fields: map[string]*structpb.Value{
			"GrpcCode":   structpb.NewNumberValue(float64(codes.InvalidArgument)),
			"Message":    structpb.NewStringValue("Invalid agent field type"),
			"ErrorType":  structpb.NewStringValue(errors.ErrorTypeBadRequest), // "ErrorTypeBadRequest"
			"IsExpected": structpb.NewBoolValue(true),
			"Code":       structpb.NewNumberValue(float64(Error_ErrAgentField)), // 1012
		},
	})

	ErrAgentField = errors.GoErr{
		ErrType:    errors.ErrorTypeBadRequest, // "ErrorTypeBadRequest"
		Status:     statusErrAgentField,
		Code:       int(Error_ErrAgentField), // 1012
		Message:    "Invalid agent field type",
		GrpcCode:   codes.InvalidArgument,
		IsExpected: true,
	}
)

var (
	statusErrProjectField, _ = status.New(
		codes.InvalidArgument,
		"Invalid project field type",
	).WithDetails(&structpb.Struct{
		Fields: map[string]*structpb.Value{
			"GrpcCode":   structpb.NewNumberValue(float64(codes.InvalidArgument)),
			"Message":    structpb.NewStringValue("Invalid project field type"),
			"ErrorType":  structpb.NewStringValue(errors.ErrorTypeBadRequest), // "ErrorTypeBadRequest"
			"IsExpected": structpb.NewBoolValue(true),
			"Code":       structpb.NewNumberValue(float64(Error_ErrProjectField)), // 1013
		},
	})

	ErrProjectField = errors.GoErr{
		ErrType:    errors.ErrorTypeBadRequest, // "ErrorTypeBadRequest"
		Status:     statusErrProjectField,
		Code:       int(Error_ErrProjectField), // 1013
		Message:    "Invalid project field type",
		GrpcCode:   codes.InvalidArgument,
		IsExpected: true,
	}
)

var (
	statusErrInvalidSignature, _ = status.New(
		codes.InvalidArgument,
		"Invalid signature",
	).WithDetails(&structpb.Struct{
		Fields: map[string]*structpb.Value{
			"GrpcCode":   structpb.NewNumberValue(float64(codes.InvalidArgument)),
			"Message":    structpb.NewStringValue("Invalid signature"),
			"ErrorType":  structpb.NewStringValue(errors.ErrorTypeBadRequest), // "ErrorTypeBadRequest"
			"IsExpected": structpb.NewBoolValue(true),
			"Code":       structpb.NewNumberValue(float64(Error_ErrInvalidSignature)), // 1014
		},
	})

	ErrInvalidSignature = errors.GoErr{
		ErrType:    errors.ErrorTypeBadRequest, // "ErrorTypeBadRequest"
		Status:     statusErrInvalidSignature,
		Code:       int(Error_ErrInvalidSignature), // 1014
		Message:    "Invalid signature",
		GrpcCode:   codes.InvalidArgument,
		IsExpected: true,
	}
)

var (
	statusErrRootKeys, _ = status.New(
		codes.InvalidArgument,
		"an error is occured while filtering root keys",
	).WithDetails(&structpb.Struct{
		Fields: map[string]*structpb.Value{
			"GrpcCode":   structpb.NewNumberValue(float64(codes.InvalidArgument)),
			"Message":    structpb.NewStringValue("an error is occured while filtering root keys"),
			"ErrorType":  structpb.NewStringValue(errors.ErrorTypeBadRequest), // "ErrorTypeBadRequest"
			"IsExpected": structpb.NewBoolValue(true),
			"Code":       structpb.NewNumberValue(float64(Error_ErrRootKeys)), // 5600
		},
	})

	ErrRootKeys = errors.GoErr{
		ErrType:    errors.ErrorTypeBadRequest, // "ErrorTypeBadRequest"
		Status:     statusErrRootKeys,
		Code:       int(Error_ErrRootKeys), // 5600
		Message:    "an error is occured while filtering root keys",
		GrpcCode:   codes.InvalidArgument,
		IsExpected: true,
	}
)

var (
	statusErrParseJson, _ = status.New(
		codes.InvalidArgument,
		"parse json",
	).WithDetails(&structpb.Struct{
		Fields: map[string]*structpb.Value{
			"GrpcCode":   structpb.NewNumberValue(float64(codes.InvalidArgument)),
			"Message":    structpb.NewStringValue("parse json"),
			"ErrorType":  structpb.NewStringValue(errors.ErrorTypeBadRequest), // "ErrorTypeBadRequest"
			"IsExpected": structpb.NewBoolValue(true),
			"Code":       structpb.NewNumberValue(float64(Error_ErrParseJson)), // 5601
		},
	})

	ErrParseJson = errors.GoErr{
		ErrType:    errors.ErrorTypeBadRequest, // "ErrorTypeBadRequest"
		Status:     statusErrParseJson,
		Code:       int(Error_ErrParseJson), // 5601
		Message:    "parse json",
		GrpcCode:   codes.InvalidArgument,
		IsExpected: true,
	}
)

var (
	statusErrValidateTime, _ = status.New(
		codes.InvalidArgument,
		"failed checking expiration date",
	).WithDetails(&structpb.Struct{
		Fields: map[string]*structpb.Value{
			"GrpcCode":   structpb.NewNumberValue(float64(codes.InvalidArgument)),
			"Message":    structpb.NewStringValue("failed checking expiration date"),
			"ErrorType":  structpb.NewStringValue(errors.ErrorTypeBadRequest), // "ErrorTypeBadRequest"
			"IsExpected": structpb.NewBoolValue(true),
			"Code":       structpb.NewNumberValue(float64(Error_ErrValidateTime)), // 5602
		},
	})

	ErrValidateTime = errors.GoErr{
		ErrType:    errors.ErrorTypeBadRequest, // "ErrorTypeBadRequest"
		Status:     statusErrValidateTime,
		Code:       int(Error_ErrValidateTime), // 5602
		Message:    "failed checking expiration date",
		GrpcCode:   codes.InvalidArgument,
		IsExpected: true,
	}
)

var (
	statusErrPrivateKey, _ = status.New(
		codes.InvalidArgument,
		"failed loading private key",
	).WithDetails(&structpb.Struct{
		Fields: map[string]*structpb.Value{
			"GrpcCode":   structpb.NewNumberValue(float64(codes.InvalidArgument)),
			"Message":    structpb.NewStringValue("failed loading private key"),
			"ErrorType":  structpb.NewStringValue(errors.ErrorTypeBadRequest), // "ErrorTypeBadRequest"
			"IsExpected": structpb.NewBoolValue(true),
			"Code":       structpb.NewNumberValue(float64(Error_ErrPrivateKey)), // 5603
		},
	})

	ErrPrivateKey = errors.GoErr{
		ErrType:    errors.ErrorTypeBadRequest, // "ErrorTypeBadRequest"
		Status:     statusErrPrivateKey,
		Code:       int(Error_ErrPrivateKey), // 5603
		Message:    "failed loading private key",
		GrpcCode:   codes.InvalidArgument,
		IsExpected: true,
	}
)

var (
	statusErrPublicKey, _ = status.New(
		codes.InvalidArgument,
		"failed loading public key",
	).WithDetails(&structpb.Struct{
		Fields: map[string]*structpb.Value{
			"GrpcCode":   structpb.NewNumberValue(float64(codes.InvalidArgument)),
			"Message":    structpb.NewStringValue("failed loading public key"),
			"ErrorType":  structpb.NewStringValue(errors.ErrorTypeBadRequest), // "ErrorTypeBadRequest"
			"IsExpected": structpb.NewBoolValue(true),
			"Code":       structpb.NewNumberValue(float64(Error_ErrPublicKey)), // 5604
		},
	})

	ErrPublicKey = errors.GoErr{
		ErrType:    errors.ErrorTypeBadRequest, // "ErrorTypeBadRequest"
		Status:     statusErrPublicKey,
		Code:       int(Error_ErrPublicKey), // 5604
		Message:    "failed loading public key",
		GrpcCode:   codes.InvalidArgument,
		IsExpected: true,
	}
)

var (
	statusErrTyping, _ = status.New(
		codes.InvalidArgument,
		"error while typing",
	).WithDetails(&structpb.Struct{
		Fields: map[string]*structpb.Value{
			"GrpcCode":   structpb.NewNumberValue(float64(codes.InvalidArgument)),
			"Message":    structpb.NewStringValue("error while typing"),
			"ErrorType":  structpb.NewStringValue(errors.ErrorTypeBadRequest), // "ErrorTypeBadRequest"
			"IsExpected": structpb.NewBoolValue(true),
			"Code":       structpb.NewNumberValue(float64(Error_ErrTyping)), // 5605
		},
	})

	ErrTyping = errors.GoErr{
		ErrType:    errors.ErrorTypeBadRequest, // "ErrorTypeBadRequest"
		Status:     statusErrTyping,
		Code:       int(Error_ErrTyping), // 5605
		Message:    "error while typing",
		GrpcCode:   codes.InvalidArgument,
		IsExpected: true,
	}
)

var (
	statusErrVerifySignature, _ = status.New(
		codes.InvalidArgument,
		"could not verify intermediate signing key signature",
	).WithDetails(&structpb.Struct{
		Fields: map[string]*structpb.Value{
			"GrpcCode":   structpb.NewNumberValue(float64(codes.InvalidArgument)),
			"Message":    structpb.NewStringValue("could not verify intermediate signing key signature"),
			"ErrorType":  structpb.NewStringValue(errors.ErrorTypeBadRequest), // "ErrorTypeBadRequest"
			"IsExpected": structpb.NewBoolValue(true),
			"Code":       structpb.NewNumberValue(float64(Error_ErrVerifySignature)), // 5606
		},
	})

	ErrVerifySignature = errors.GoErr{
		ErrType:    errors.ErrorTypeBadRequest, // "ErrorTypeBadRequest"
		Status:     statusErrVerifySignature,
		Code:       int(Error_ErrVerifySignature), // 5606
		Message:    "could not verify intermediate signing key signature",
		GrpcCode:   codes.InvalidArgument,
		IsExpected: true,
	}
)

var (
	statusErrLengthDoesnotMatch, _ = status.New(
		codes.InvalidArgument,
		"length of key does not match",
	).WithDetails(&structpb.Struct{
		Fields: map[string]*structpb.Value{
			"GrpcCode":   structpb.NewNumberValue(float64(codes.InvalidArgument)),
			"Message":    structpb.NewStringValue("length of key does not match"),
			"ErrorType":  structpb.NewStringValue(errors.ErrorTypeBadRequest), // "ErrorTypeBadRequest"
			"IsExpected": structpb.NewBoolValue(true),
			"Code":       structpb.NewNumberValue(float64(Error_ErrLengthDoesnotMatch)), // 5607
		},
	})

	ErrLengthDoesnotMatch = errors.GoErr{
		ErrType:    errors.ErrorTypeBadRequest, // "ErrorTypeBadRequest"
		Status:     statusErrLengthDoesnotMatch,
		Code:       int(Error_ErrLengthDoesnotMatch), // 5607
		Message:    "length of key does not match",
		GrpcCode:   codes.InvalidArgument,
		IsExpected: true,
	}
)

var (
	statusErrInvalidAdditionalData, _ = status.New(
		codes.InvalidArgument,
		"invalid additional data",
	).WithDetails(&structpb.Struct{
		Fields: map[string]*structpb.Value{
			"GrpcCode":   structpb.NewNumberValue(float64(codes.InvalidArgument)),
			"Message":    structpb.NewStringValue("invalid additional data"),
			"ErrorType":  structpb.NewStringValue(errors.ErrorTypeBadRequest), // "ErrorTypeBadRequest"
			"IsExpected": structpb.NewBoolValue(true),
			"Code":       structpb.NewNumberValue(float64(Error_ErrInvalidAdditionalData)), // 5608
		},
	})

	ErrInvalidAdditionalData = errors.GoErr{
		ErrType:    errors.ErrorTypeBadRequest, // "ErrorTypeBadRequest"
		Status:     statusErrInvalidAdditionalData,
		Code:       int(Error_ErrInvalidAdditionalData), // 5608
		Message:    "invalid additional data",
		GrpcCode:   codes.InvalidArgument,
		IsExpected: true,
	}
)

var (
	statusErrInvalidServiceProviderConfig, _ = status.New(
		codes.InvalidArgument,
		"invalid service provider config",
	).WithDetails(&structpb.Struct{
		Fields: map[string]*structpb.Value{
			"GrpcCode":   structpb.NewNumberValue(float64(codes.InvalidArgument)),
			"Message":    structpb.NewStringValue("invalid service provider config"),
			"ErrorType":  structpb.NewStringValue(errors.ErrorTypeBadRequest), // "ErrorTypeBadRequest"
			"IsExpected": structpb.NewBoolValue(true),
			"Code":       structpb.NewNumberValue(float64(Error_ErrInvalidServiceProviderConfig)), // 5609
		},
	})

	ErrInvalidServiceProviderConfig = errors.GoErr{
		ErrType:    errors.ErrorTypeBadRequest, // "ErrorTypeBadRequest"
		Status:     statusErrInvalidServiceProviderConfig,
		Code:       int(Error_ErrInvalidServiceProviderConfig), // 5609
		Message:    "invalid service provider config",
		GrpcCode:   codes.InvalidArgument,
		IsExpected: true,
	}
)

var (
	statusErrInvalidProjectCode, _ = status.New(
		codes.InvalidArgument,
		"invalid project code",
	).WithDetails(&structpb.Struct{
		Fields: map[string]*structpb.Value{
			"GrpcCode":   structpb.NewNumberValue(float64(codes.InvalidArgument)),
			"Message":    structpb.NewStringValue("invalid project code"),
			"ErrorType":  structpb.NewStringValue(errors.ErrorTypeBadRequest), // "ErrorTypeBadRequest"
			"IsExpected": structpb.NewBoolValue(true),
			"Code":       structpb.NewNumberValue(float64(Error_ErrInvalidProjectCode)), // 5610
		},
	})

	ErrInvalidProjectCode = errors.GoErr{
		ErrType:    errors.ErrorTypeBadRequest, // "ErrorTypeBadRequest"
		Status:     statusErrInvalidProjectCode,
		Code:       int(Error_ErrInvalidProjectCode), // 5610
		Message:    "invalid project code",
		GrpcCode:   codes.InvalidArgument,
		IsExpected: true,
	}
)

var (
	statusErrInvalidShowcaseServiceCode, _ = status.New(
		codes.InvalidArgument,
		"invalid showcase service code",
	).WithDetails(&structpb.Struct{
		Fields: map[string]*structpb.Value{
			"GrpcCode":   structpb.NewNumberValue(float64(codes.InvalidArgument)),
			"Message":    structpb.NewStringValue("invalid showcase service code"),
			"ErrorType":  structpb.NewStringValue(errors.ErrorTypeBadRequest), // "ErrorTypeBadRequest"
			"IsExpected": structpb.NewBoolValue(true),
			"Code":       structpb.NewNumberValue(float64(Error_ErrInvalidShowcaseServiceCode)), // 5611
		},
	})

	ErrInvalidShowcaseServiceCode = errors.GoErr{
		ErrType:    errors.ErrorTypeBadRequest, // "ErrorTypeBadRequest"
		Status:     statusErrInvalidShowcaseServiceCode,
		Code:       int(Error_ErrInvalidShowcaseServiceCode), // 5611
		Message:    "invalid showcase service code",
		GrpcCode:   codes.InvalidArgument,
		IsExpected: true,
	}
)

var (
	statusErrInvalidShowcaseCode, _ = status.New(
		codes.InvalidArgument,
		"invalid showcase code",
	).WithDetails(&structpb.Struct{
		Fields: map[string]*structpb.Value{
			"GrpcCode":   structpb.NewNumberValue(float64(codes.InvalidArgument)),
			"Message":    structpb.NewStringValue("invalid showcase code"),
			"ErrorType":  structpb.NewStringValue(errors.ErrorTypeBadRequest), // "ErrorTypeBadRequest"
			"IsExpected": structpb.NewBoolValue(true),
			"Code":       structpb.NewNumberValue(float64(Error_ErrInvalidShowcaseCode)), // 5612
		},
	})

	ErrInvalidShowcaseCode = errors.GoErr{
		ErrType:    errors.ErrorTypeBadRequest, // "ErrorTypeBadRequest"
		Status:     statusErrInvalidShowcaseCode,
		Code:       int(Error_ErrInvalidShowcaseCode), // 5612
		Message:    "invalid showcase code",
		GrpcCode:   codes.InvalidArgument,
		IsExpected: true,
	}
)

var (
	statusErrInvalidDriverResponse, _ = status.New(
		codes.InvalidArgument,
		"invalid driver response",
	).WithDetails(&structpb.Struct{
		Fields: map[string]*structpb.Value{
			"GrpcCode":   structpb.NewNumberValue(float64(codes.InvalidArgument)),
			"Message":    structpb.NewStringValue("invalid driver response"),
			"ErrorType":  structpb.NewStringValue(errors.ErrorTypeBadRequest), // "ErrorTypeBadRequest"
			"IsExpected": structpb.NewBoolValue(true),
			"Code":       structpb.NewNumberValue(float64(Error_ErrInvalidDriverResponse)), // 5613
		},
	})

	ErrInvalidDriverResponse = errors.GoErr{
		ErrType:    errors.ErrorTypeBadRequest, // "ErrorTypeBadRequest"
		Status:     statusErrInvalidDriverResponse,
		Code:       int(Error_ErrInvalidDriverResponse), // 5613
		Message:    "invalid driver response",
		GrpcCode:   codes.InvalidArgument,
		IsExpected: true,
	}
)

var (
	statusErrDurationGreaterThan12, _ = status.New(
		codes.InvalidArgument,
		"duration can't be greater than 12",
	).WithDetails(&structpb.Struct{
		Fields: map[string]*structpb.Value{
			"GrpcCode":   structpb.NewNumberValue(float64(codes.InvalidArgument)),
			"Message":    structpb.NewStringValue("duration can't be greater than 12"),
			"ErrorType":  structpb.NewStringValue(errors.ErrorTypeBadRequest), // "ErrorTypeBadRequest"
			"IsExpected": structpb.NewBoolValue(true),
			"Code":       structpb.NewNumberValue(float64(Error_ErrDurationGreaterThan12)), // 5614
		},
	})

	ErrDurationGreaterThan12 = errors.GoErr{
		ErrType:    errors.ErrorTypeBadRequest, // "ErrorTypeBadRequest"
		Status:     statusErrDurationGreaterThan12,
		Code:       int(Error_ErrDurationGreaterThan12), // 5614
		Message:    "duration can't be greater than 12",
		GrpcCode:   codes.InvalidArgument,
		IsExpected: true,
	}
)

var (
	statusErrParseTime, _ = status.New(
		codes.InvalidArgument,
		"parse time",
	).WithDetails(&structpb.Struct{
		Fields: map[string]*structpb.Value{
			"GrpcCode":   structpb.NewNumberValue(float64(codes.InvalidArgument)),
			"Message":    structpb.NewStringValue("parse time"),
			"ErrorType":  structpb.NewStringValue(errors.ErrorTypeBadRequest), // "ErrorTypeBadRequest"
			"IsExpected": structpb.NewBoolValue(true),
			"Code":       structpb.NewNumberValue(float64(Error_ErrParseTime)), // 5615
		},
	})

	ErrParseTime = errors.GoErr{
		ErrType:    errors.ErrorTypeBadRequest, // "ErrorTypeBadRequest"
		Status:     statusErrParseTime,
		Code:       int(Error_ErrParseTime), // 5615
		Message:    "parse time",
		GrpcCode:   codes.InvalidArgument,
		IsExpected: true,
	}
)

var (
	statusErrCoordinatesNotInParkingZone, _ = status.New(
		codes.InvalidArgument,
		"coordinates are not in parking zone",
	).WithDetails(&structpb.Struct{
		Fields: map[string]*structpb.Value{
			"GrpcCode":   structpb.NewNumberValue(float64(codes.InvalidArgument)),
			"Message":    structpb.NewStringValue("coordinates are not in parking zone"),
			"ErrorType":  structpb.NewStringValue(errors.ErrorTypeBadRequest), // "ErrorTypeBadRequest"
			"IsExpected": structpb.NewBoolValue(true),
			"Code":       structpb.NewNumberValue(float64(Error_ErrCoordinatesNotInParkingZone)), // 5616
		},
	})

	ErrCoordinatesNotInParkingZone = errors.GoErr{
		ErrType:    errors.ErrorTypeBadRequest, // "ErrorTypeBadRequest"
		Status:     statusErrCoordinatesNotInParkingZone,
		Code:       int(Error_ErrCoordinatesNotInParkingZone), // 5616
		Message:    "coordinates are not in parking zone",
		GrpcCode:   codes.InvalidArgument,
		IsExpected: true,
	}
)

var (
	statusErrEmptyProviderServiceCode, _ = status.New(
		codes.InvalidArgument,
		"empty provider service code",
	).WithDetails(&structpb.Struct{
		Fields: map[string]*structpb.Value{
			"GrpcCode":   structpb.NewNumberValue(float64(codes.InvalidArgument)),
			"Message":    structpb.NewStringValue("empty provider service code"),
			"ErrorType":  structpb.NewStringValue(errors.ErrorTypeBadRequest), // "ErrorTypeBadRequest"
			"IsExpected": structpb.NewBoolValue(true),
			"Code":       structpb.NewNumberValue(float64(Error_ErrEmptyProviderServiceCode)), // 5617
		},
	})

	ErrEmptyProviderServiceCode = errors.GoErr{
		ErrType:    errors.ErrorTypeBadRequest, // "ErrorTypeBadRequest"
		Status:     statusErrEmptyProviderServiceCode,
		Code:       int(Error_ErrEmptyProviderServiceCode), // 5617
		Message:    "empty provider service code",
		GrpcCode:   codes.InvalidArgument,
		IsExpected: true,
	}
)

var (
	statusErrEmptyPayload, _ = status.New(
		codes.InvalidArgument,
		"empty payload",
	).WithDetails(&structpb.Struct{
		Fields: map[string]*structpb.Value{
			"GrpcCode":   structpb.NewNumberValue(float64(codes.InvalidArgument)),
			"Message":    structpb.NewStringValue("empty payload"),
			"ErrorType":  structpb.NewStringValue(errors.ErrorTypeBadRequest), // "ErrorTypeBadRequest"
			"IsExpected": structpb.NewBoolValue(true),
			"Code":       structpb.NewNumberValue(float64(Error_ErrEmptyPayload)), // 5618
		},
	})

	ErrEmptyPayload = errors.GoErr{
		ErrType:    errors.ErrorTypeBadRequest, // "ErrorTypeBadRequest"
		Status:     statusErrEmptyPayload,
		Code:       int(Error_ErrEmptyPayload), // 5618
		Message:    "empty payload",
		GrpcCode:   codes.InvalidArgument,
		IsExpected: true,
	}
)

var (
	statusErrUnexpectedFailedRestRequest, _ = status.New(
		codes.InvalidArgument,
		"unexpected failed rest request",
	).WithDetails(&structpb.Struct{
		Fields: map[string]*structpb.Value{
			"GrpcCode":   structpb.NewNumberValue(float64(codes.InvalidArgument)),
			"Message":    structpb.NewStringValue("unexpected failed rest request"),
			"ErrorType":  structpb.NewStringValue(errors.ErrorTypeBadRequest), // "ErrorTypeBadRequest"
			"IsExpected": structpb.NewBoolValue(true),
			"Code":       structpb.NewNumberValue(float64(Error_ErrUnexpectedFailedRestRequest)), // 5619
		},
	})

	ErrUnexpectedFailedRestRequest = errors.GoErr{
		ErrType:    errors.ErrorTypeBadRequest, // "ErrorTypeBadRequest"
		Status:     statusErrUnexpectedFailedRestRequest,
		Code:       int(Error_ErrUnexpectedFailedRestRequest), // 5619
		Message:    "unexpected failed rest request",
		GrpcCode:   codes.InvalidArgument,
		IsExpected: true,
	}
)

var (
	statusErrPaydalaResponseValidation, _ = status.New(
		codes.InvalidArgument,
		"paydala response validation",
	).WithDetails(&structpb.Struct{
		Fields: map[string]*structpb.Value{
			"GrpcCode":   structpb.NewNumberValue(float64(codes.InvalidArgument)),
			"Message":    structpb.NewStringValue("paydala response validation"),
			"ErrorType":  structpb.NewStringValue(errors.ErrorTypeBadRequest), // "ErrorTypeBadRequest"
			"IsExpected": structpb.NewBoolValue(true),
			"Code":       structpb.NewNumberValue(float64(Error_ErrPaydalaResponseValidation)), // 5620
		},
	})

	ErrPaydalaResponseValidation = errors.GoErr{
		ErrType:    errors.ErrorTypeBadRequest, // "ErrorTypeBadRequest"
		Status:     statusErrPaydalaResponseValidation,
		Code:       int(Error_ErrPaydalaResponseValidation), // 5620
		Message:    "paydala response validation",
		GrpcCode:   codes.InvalidArgument,
		IsExpected: true,
	}
)

var (
	statusErrEmptyBankName, _ = status.New(
		codes.InvalidArgument,
		"empty bank name",
	).WithDetails(&structpb.Struct{
		Fields: map[string]*structpb.Value{
			"GrpcCode":   structpb.NewNumberValue(float64(codes.InvalidArgument)),
			"Message":    structpb.NewStringValue("empty bank name"),
			"ErrorType":  structpb.NewStringValue(errors.ErrorTypeBadRequest), // "ErrorTypeBadRequest"
			"IsExpected": structpb.NewBoolValue(true),
			"Code":       structpb.NewNumberValue(float64(Error_ErrEmptyBankName)), // 5621
		},
	})

	ErrEmptyBankName = errors.GoErr{
		ErrType:    errors.ErrorTypeBadRequest, // "ErrorTypeBadRequest"
		Status:     statusErrEmptyBankName,
		Code:       int(Error_ErrEmptyBankName), // 5621
		Message:    "empty bank name",
		GrpcCode:   codes.InvalidArgument,
		IsExpected: true,
	}
)

var (
	statusErrInvalidReceiptFormat, _ = status.New(
		codes.InvalidArgument,
		"invalid receipt format",
	).WithDetails(&structpb.Struct{
		Fields: map[string]*structpb.Value{
			"GrpcCode":   structpb.NewNumberValue(float64(codes.InvalidArgument)),
			"Message":    structpb.NewStringValue("invalid receipt format"),
			"ErrorType":  structpb.NewStringValue(errors.ErrorTypeBadRequest), // "ErrorTypeBadRequest"
			"IsExpected": structpb.NewBoolValue(true),
			"Code":       structpb.NewNumberValue(float64(Error_ErrInvalidReceiptFormat)), // 5622
		},
	})

	ErrInvalidReceiptFormat = errors.GoErr{
		ErrType:    errors.ErrorTypeBadRequest, // "ErrorTypeBadRequest"
		Status:     statusErrInvalidReceiptFormat,
		Code:       int(Error_ErrInvalidReceiptFormat), // 5622
		Message:    "invalid receipt format",
		GrpcCode:   codes.InvalidArgument,
		IsExpected: true,
	}
)

var (
	statusErrEmptyReceipt, _ = status.New(
		codes.InvalidArgument,
		"empty receipt",
	).WithDetails(&structpb.Struct{
		Fields: map[string]*structpb.Value{
			"GrpcCode":   structpb.NewNumberValue(float64(codes.InvalidArgument)),
			"Message":    structpb.NewStringValue("empty receipt"),
			"ErrorType":  structpb.NewStringValue(errors.ErrorTypeBadRequest), // "ErrorTypeBadRequest"
			"IsExpected": structpb.NewBoolValue(true),
			"Code":       structpb.NewNumberValue(float64(Error_ErrEmptyReceipt)), // 5623
		},
	})

	ErrEmptyReceipt = errors.GoErr{
		ErrType:    errors.ErrorTypeBadRequest, // "ErrorTypeBadRequest"
		Status:     statusErrEmptyReceipt,
		Code:       int(Error_ErrEmptyReceipt), // 5623
		Message:    "empty receipt",
		GrpcCode:   codes.InvalidArgument,
		IsExpected: true,
	}
)

var (
	statusErrEmptyContractNumber, _ = status.New(
		codes.InvalidArgument,
		"empty contract number",
	).WithDetails(&structpb.Struct{
		Fields: map[string]*structpb.Value{
			"GrpcCode":   structpb.NewNumberValue(float64(codes.InvalidArgument)),
			"Message":    structpb.NewStringValue("empty contract number"),
			"ErrorType":  structpb.NewStringValue(errors.ErrorTypeBadRequest), // "ErrorTypeBadRequest"
			"IsExpected": structpb.NewBoolValue(true),
			"Code":       structpb.NewNumberValue(float64(Error_ErrEmptyContractNumber)), // 5624
		},
	})

	ErrEmptyContractNumber = errors.GoErr{
		ErrType:    errors.ErrorTypeBadRequest, // "ErrorTypeBadRequest"
		Status:     statusErrEmptyContractNumber,
		Code:       int(Error_ErrEmptyContractNumber), // 5624
		Message:    "empty contract number",
		GrpcCode:   codes.InvalidArgument,
		IsExpected: true,
	}
)

var (
	statusErrEmptyAuthCode, _ = status.New(
		codes.InvalidArgument,
		"empty auth code",
	).WithDetails(&structpb.Struct{
		Fields: map[string]*structpb.Value{
			"GrpcCode":   structpb.NewNumberValue(float64(codes.InvalidArgument)),
			"Message":    structpb.NewStringValue("empty auth code"),
			"ErrorType":  structpb.NewStringValue(errors.ErrorTypeBadRequest), // "ErrorTypeBadRequest"
			"IsExpected": structpb.NewBoolValue(true),
			"Code":       structpb.NewNumberValue(float64(Error_ErrEmptyAuthCode)), // 5625
		},
	})

	ErrEmptyAuthCode = errors.GoErr{
		ErrType:    errors.ErrorTypeBadRequest, // "ErrorTypeBadRequest"
		Status:     statusErrEmptyAuthCode,
		Code:       int(Error_ErrEmptyAuthCode), // 5625
		Message:    "empty auth code",
		GrpcCode:   codes.InvalidArgument,
		IsExpected: true,
	}
)

var (
	statusErrEmptyPhone, _ = status.New(
		codes.InvalidArgument,
		"empty phone",
	).WithDetails(&structpb.Struct{
		Fields: map[string]*structpb.Value{
			"GrpcCode":   structpb.NewNumberValue(float64(codes.InvalidArgument)),
			"Message":    structpb.NewStringValue("empty phone"),
			"ErrorType":  structpb.NewStringValue(errors.ErrorTypeBadRequest), // "ErrorTypeBadRequest"
			"IsExpected": structpb.NewBoolValue(true),
			"Code":       structpb.NewNumberValue(float64(Error_ErrEmptyPhone)), // 5626
		},
	})

	ErrEmptyPhone = errors.GoErr{
		ErrType:    errors.ErrorTypeBadRequest, // "ErrorTypeBadRequest"
		Status:     statusErrEmptyPhone,
		Code:       int(Error_ErrEmptyPhone), // 5626
		Message:    "empty phone",
		GrpcCode:   codes.InvalidArgument,
		IsExpected: true,
	}
)

var (
	statusErrInvalidPhoneFormat, _ = status.New(
		codes.InvalidArgument,
		"invalid phone format",
	).WithDetails(&structpb.Struct{
		Fields: map[string]*structpb.Value{
			"GrpcCode":   structpb.NewNumberValue(float64(codes.InvalidArgument)),
			"Message":    structpb.NewStringValue("invalid phone format"),
			"ErrorType":  structpb.NewStringValue(errors.ErrorTypeBadRequest), // "ErrorTypeBadRequest"
			"IsExpected": structpb.NewBoolValue(true),
			"Code":       structpb.NewNumberValue(float64(Error_ErrInvalidPhoneFormat)), // 5627
		},
	})

	ErrInvalidPhoneFormat = errors.GoErr{
		ErrType:    errors.ErrorTypeBadRequest, // "ErrorTypeBadRequest"
		Status:     statusErrInvalidPhoneFormat,
		Code:       int(Error_ErrInvalidPhoneFormat), // 5627
		Message:    "invalid phone format",
		GrpcCode:   codes.InvalidArgument,
		IsExpected: true,
	}
)

var (
	statusErrEmptyIIN, _ = status.New(
		codes.InvalidArgument,
		"empty iin",
	).WithDetails(&structpb.Struct{
		Fields: map[string]*structpb.Value{
			"GrpcCode":   structpb.NewNumberValue(float64(codes.InvalidArgument)),
			"Message":    structpb.NewStringValue("empty iin"),
			"ErrorType":  structpb.NewStringValue(errors.ErrorTypeBadRequest), // "ErrorTypeBadRequest"
			"IsExpected": structpb.NewBoolValue(true),
			"Code":       structpb.NewNumberValue(float64(Error_ErrEmptyIIN)), // 5628
		},
	})

	ErrEmptyIIN = errors.GoErr{
		ErrType:    errors.ErrorTypeBadRequest, // "ErrorTypeBadRequest"
		Status:     statusErrEmptyIIN,
		Code:       int(Error_ErrEmptyIIN), // 5628
		Message:    "empty iin",
		GrpcCode:   codes.InvalidArgument,
		IsExpected: true,
	}
)

var (
	statusErrEmptyServiceCode, _ = status.New(
		codes.InvalidArgument,
		"empty service code",
	).WithDetails(&structpb.Struct{
		Fields: map[string]*structpb.Value{
			"GrpcCode":   structpb.NewNumberValue(float64(codes.InvalidArgument)),
			"Message":    structpb.NewStringValue("empty service code"),
			"ErrorType":  structpb.NewStringValue(errors.ErrorTypeBadRequest), // "ErrorTypeBadRequest"
			"IsExpected": structpb.NewBoolValue(true),
			"Code":       structpb.NewNumberValue(float64(Error_ErrEmptyServiceCode)), // 5629
		},
	})

	ErrEmptyServiceCode = errors.GoErr{
		ErrType:    errors.ErrorTypeBadRequest, // "ErrorTypeBadRequest"
		Status:     statusErrEmptyServiceCode,
		Code:       int(Error_ErrEmptyServiceCode), // 5629
		Message:    "empty service code",
		GrpcCode:   codes.InvalidArgument,
		IsExpected: true,
	}
)

var (
	statusErrInvalidShowcaseServiceID, _ = status.New(
		codes.InvalidArgument,
		"invalid showcase service id",
	).WithDetails(&structpb.Struct{
		Fields: map[string]*structpb.Value{
			"GrpcCode":   structpb.NewNumberValue(float64(codes.InvalidArgument)),
			"Message":    structpb.NewStringValue("invalid showcase service id"),
			"ErrorType":  structpb.NewStringValue(errors.ErrorTypeBadRequest), // "ErrorTypeBadRequest"
			"IsExpected": structpb.NewBoolValue(true),
			"Code":       structpb.NewNumberValue(float64(Error_ErrInvalidShowcaseServiceID)), // 5630
		},
	})

	ErrInvalidShowcaseServiceID = errors.GoErr{
		ErrType:    errors.ErrorTypeBadRequest, // "ErrorTypeBadRequest"
		Status:     statusErrInvalidShowcaseServiceID,
		Code:       int(Error_ErrInvalidShowcaseServiceID), // 5630
		Message:    "invalid showcase service id",
		GrpcCode:   codes.InvalidArgument,
		IsExpected: true,
	}
)

var (
	statusErrEmptySignature, _ = status.New(
		codes.InvalidArgument,
		"empty signature",
	).WithDetails(&structpb.Struct{
		Fields: map[string]*structpb.Value{
			"GrpcCode":   structpb.NewNumberValue(float64(codes.InvalidArgument)),
			"Message":    structpb.NewStringValue("empty signature"),
			"ErrorType":  structpb.NewStringValue(errors.ErrorTypeBadRequest), // "ErrorTypeBadRequest"
			"IsExpected": structpb.NewBoolValue(true),
			"Code":       structpb.NewNumberValue(float64(Error_ErrEmptySignature)), // 5631
		},
	})

	ErrEmptySignature = errors.GoErr{
		ErrType:    errors.ErrorTypeBadRequest, // "ErrorTypeBadRequest"
		Status:     statusErrEmptySignature,
		Code:       int(Error_ErrEmptySignature), // 5631
		Message:    "empty signature",
		GrpcCode:   codes.InvalidArgument,
		IsExpected: true,
	}
)

var (
	statusErrEmptyCategory, _ = status.New(
		codes.InvalidArgument,
		"empty category",
	).WithDetails(&structpb.Struct{
		Fields: map[string]*structpb.Value{
			"GrpcCode":   structpb.NewNumberValue(float64(codes.InvalidArgument)),
			"Message":    structpb.NewStringValue("empty category"),
			"ErrorType":  structpb.NewStringValue(errors.ErrorTypeBadRequest), // "ErrorTypeBadRequest"
			"IsExpected": structpb.NewBoolValue(true),
			"Code":       structpb.NewNumberValue(float64(Error_ErrEmptyCategory)), // 5632
		},
	})

	ErrEmptyCategory = errors.GoErr{
		ErrType:    errors.ErrorTypeBadRequest, // "ErrorTypeBadRequest"
		Status:     statusErrEmptyCategory,
		Code:       int(Error_ErrEmptyCategory), // 5632
		Message:    "empty category",
		GrpcCode:   codes.InvalidArgument,
		IsExpected: true,
	}
)

var (
	statusErrOperationRequired, _ = status.New(
		codes.InvalidArgument,
		"operation is required",
	).WithDetails(&structpb.Struct{
		Fields: map[string]*structpb.Value{
			"GrpcCode":   structpb.NewNumberValue(float64(codes.InvalidArgument)),
			"Message":    structpb.NewStringValue("operation is required"),
			"ErrorType":  structpb.NewStringValue(errors.ErrorTypeBadRequest), // "ErrorTypeBadRequest"
			"IsExpected": structpb.NewBoolValue(true),
			"Code":       structpb.NewNumberValue(float64(Error_ErrOperationRequired)), // 11001
		},
	})

	ErrOperationRequired = errors.GoErr{
		ErrType:    errors.ErrorTypeBadRequest, // "ErrorTypeBadRequest"
		Status:     statusErrOperationRequired,
		Code:       int(Error_ErrOperationRequired), // 11001
		Message:    "operation is required",
		GrpcCode:   codes.InvalidArgument,
		IsExpected: true,
	}
)

var (
	statusErrResourceRequired, _ = status.New(
		codes.InvalidArgument,
		"resource is required",
	).WithDetails(&structpb.Struct{
		Fields: map[string]*structpb.Value{
			"GrpcCode":   structpb.NewNumberValue(float64(codes.InvalidArgument)),
			"Message":    structpb.NewStringValue("resource is required"),
			"ErrorType":  structpb.NewStringValue(errors.ErrorTypeBadRequest), // "ErrorTypeBadRequest"
			"IsExpected": structpb.NewBoolValue(true),
			"Code":       structpb.NewNumberValue(float64(Error_ErrResourceRequired)), // 11002
		},
	})

	ErrResourceRequired = errors.GoErr{
		ErrType:    errors.ErrorTypeBadRequest, // "ErrorTypeBadRequest"
		Status:     statusErrResourceRequired,
		Code:       int(Error_ErrResourceRequired), // 11002
		Message:    "resource is required",
		GrpcCode:   codes.InvalidArgument,
		IsExpected: true,
	}
)

var (
	statusErrInvalidTokenPayload, _ = status.New(
		codes.InvalidArgument,
		"invalid type in payload",
	).WithDetails(&structpb.Struct{
		Fields: map[string]*structpb.Value{
			"GrpcCode":   structpb.NewNumberValue(float64(codes.InvalidArgument)),
			"Message":    structpb.NewStringValue("invalid type in payload"),
			"ErrorType":  structpb.NewStringValue(errors.ErrorTypeBadRequest), // "ErrorTypeBadRequest"
			"IsExpected": structpb.NewBoolValue(true),
			"Code":       structpb.NewNumberValue(float64(Error_ErrInvalidTokenPayload)), // 11003
		},
	})

	ErrInvalidTokenPayload = errors.GoErr{
		ErrType:    errors.ErrorTypeBadRequest, // "ErrorTypeBadRequest"
		Status:     statusErrInvalidTokenPayload,
		Code:       int(Error_ErrInvalidTokenPayload), // 11003
		Message:    "invalid type in payload",
		GrpcCode:   codes.InvalidArgument,
		IsExpected: true,
	}
)

var (
	statusErrLoginDuplicate, _ = status.New(
		codes.Unknown,
		"login must be unique",
	).WithDetails(&structpb.Struct{
		Fields: map[string]*structpb.Value{
			"GrpcCode":   structpb.NewNumberValue(float64(codes.Unknown)),
			"Message":    structpb.NewStringValue("login must be unique"),
			"ErrorType":  structpb.NewStringValue(errors.ErrorTypeUnexpectedError), // "ErrorTypeUnexpectedError"
			"IsExpected": structpb.NewBoolValue(true),
			"Code":       structpb.NewNumberValue(float64(Error_ErrLoginDuplicate)), // 11004
		},
	})

	ErrLoginDuplicate = errors.GoErr{
		ErrType:    errors.ErrorTypeUnexpectedError, // "ErrorTypeUnexpectedError"
		Status:     statusErrLoginDuplicate,
		Code:       int(Error_ErrLoginDuplicate), // 11004
		Message:    "login must be unique",
		GrpcCode:   codes.Unknown,
		IsExpected: true,
	}
)

var (
	statusErrPhoneDuplicate, _ = status.New(
		codes.Unknown,
		"phone must be unique",
	).WithDetails(&structpb.Struct{
		Fields: map[string]*structpb.Value{
			"GrpcCode":   structpb.NewNumberValue(float64(codes.Unknown)),
			"Message":    structpb.NewStringValue("phone must be unique"),
			"ErrorType":  structpb.NewStringValue(errors.ErrorTypeUnexpectedError), // "ErrorTypeUnexpectedError"
			"IsExpected": structpb.NewBoolValue(true),
			"Code":       structpb.NewNumberValue(float64(Error_ErrPhoneDuplicate)), // 1005
		},
	})

	ErrPhoneDuplicate = errors.GoErr{
		ErrType:    errors.ErrorTypeUnexpectedError, // "ErrorTypeUnexpectedError"
		Status:     statusErrPhoneDuplicate,
		Code:       int(Error_ErrPhoneDuplicate), // 1005
		Message:    "phone must be unique",
		GrpcCode:   codes.Unknown,
		IsExpected: true,
	}
)

var (
	statusErrEmptyActionRequest, _ = status.New(
		codes.InvalidArgument,
		"invalid action request",
	).WithDetails(&structpb.Struct{
		Fields: map[string]*structpb.Value{
			"GrpcCode":   structpb.NewNumberValue(float64(codes.InvalidArgument)),
			"Message":    structpb.NewStringValue("invalid action request"),
			"ErrorType":  structpb.NewStringValue(errors.ErrorTypeBadRequest), // "ErrorTypeBadRequest"
			"IsExpected": structpb.NewBoolValue(true),
			"Code":       structpb.NewNumberValue(float64(Error_ErrEmptyActionRequest)), // 11404
		},
	})

	ErrEmptyActionRequest = errors.GoErr{
		ErrType:    errors.ErrorTypeBadRequest, // "ErrorTypeBadRequest"
		Status:     statusErrEmptyActionRequest,
		Code:       int(Error_ErrEmptyActionRequest), // 11404
		Message:    "invalid action request",
		GrpcCode:   codes.InvalidArgument,
		IsExpected: true,
	}
)

var (
	statusErrInvalidCheckAccountRequest, _ = status.New(
		codes.InvalidArgument,
		"invalid check account request",
	).WithDetails(&structpb.Struct{
		Fields: map[string]*structpb.Value{
			"GrpcCode":   structpb.NewNumberValue(float64(codes.InvalidArgument)),
			"Message":    structpb.NewStringValue("invalid check account request"),
			"ErrorType":  structpb.NewStringValue(errors.ErrorTypeBadRequest), // "ErrorTypeBadRequest"
			"IsExpected": structpb.NewBoolValue(true),
			"Code":       structpb.NewNumberValue(float64(Error_ErrInvalidCheckAccountRequest)), // 1405
		},
	})

	ErrInvalidCheckAccountRequest = errors.GoErr{
		ErrType:    errors.ErrorTypeBadRequest, // "ErrorTypeBadRequest"
		Status:     statusErrInvalidCheckAccountRequest,
		Code:       int(Error_ErrInvalidCheckAccountRequest), // 1405
		Message:    "invalid check account request",
		GrpcCode:   codes.InvalidArgument,
		IsExpected: true,
	}
)

var (
	statusErrInvalidGetStatusRequest, _ = status.New(
		codes.InvalidArgument,
		"invalid get status request",
	).WithDetails(&structpb.Struct{
		Fields: map[string]*structpb.Value{
			"GrpcCode":   structpb.NewNumberValue(float64(codes.InvalidArgument)),
			"Message":    structpb.NewStringValue("invalid get status request"),
			"ErrorType":  structpb.NewStringValue(errors.ErrorTypeBadRequest), // "ErrorTypeBadRequest"
			"IsExpected": structpb.NewBoolValue(true),
			"Code":       structpb.NewNumberValue(float64(Error_ErrInvalidGetStatusRequest)), // 1406
		},
	})

	ErrInvalidGetStatusRequest = errors.GoErr{
		ErrType:    errors.ErrorTypeBadRequest, // "ErrorTypeBadRequest"
		Status:     statusErrInvalidGetStatusRequest,
		Code:       int(Error_ErrInvalidGetStatusRequest), // 1406
		Message:    "invalid get status request",
		GrpcCode:   codes.InvalidArgument,
		IsExpected: true,
	}
)

var (
	statusErrApplicationNotFound, _ = status.New(
		codes.NotFound,
		"application doesn't exist",
	).WithDetails(&structpb.Struct{
		Fields: map[string]*structpb.Value{
			"GrpcCode":   structpb.NewNumberValue(float64(codes.NotFound)),
			"Message":    structpb.NewStringValue("application doesn't exist"),
			"ErrorType":  structpb.NewStringValue(errors.ErrorTypeNotFound), // "ErrorTypeNotFound"
			"IsExpected": structpb.NewBoolValue(true),
			"Code":       structpb.NewNumberValue(float64(Error_ErrApplicationNotFound)), // 2000
		},
	})

	ErrApplicationNotFound = errors.GoErr{
		ErrType:    errors.ErrorTypeNotFound, // "ErrorTypeNotFound"
		Status:     statusErrApplicationNotFound,
		Code:       int(Error_ErrApplicationNotFound), // 2000
		Message:    "application doesn't exist",
		GrpcCode:   codes.NotFound,
		IsExpected: true,
	}
)

var (
	statusErrApplicationStatusNotFound, _ = status.New(
		codes.NotFound,
		"application status doesn't exist",
	).WithDetails(&structpb.Struct{
		Fields: map[string]*structpb.Value{
			"GrpcCode":   structpb.NewNumberValue(float64(codes.NotFound)),
			"Message":    structpb.NewStringValue("application status doesn't exist"),
			"ErrorType":  structpb.NewStringValue(errors.ErrorTypeNotFound), // "ErrorTypeNotFound"
			"IsExpected": structpb.NewBoolValue(true),
			"Code":       structpb.NewNumberValue(float64(Error_ErrApplicationStatusNotFound)), // 2001
		},
	})

	ErrApplicationStatusNotFound = errors.GoErr{
		ErrType:    errors.ErrorTypeNotFound, // "ErrorTypeNotFound"
		Status:     statusErrApplicationStatusNotFound,
		Code:       int(Error_ErrApplicationStatusNotFound), // 2001
		Message:    "application status doesn't exist",
		GrpcCode:   codes.NotFound,
		IsExpected: true,
	}
)

var (
	statusErrApplicationTypeNotFound, _ = status.New(
		codes.NotFound,
		"application type doesn't exist",
	).WithDetails(&structpb.Struct{
		Fields: map[string]*structpb.Value{
			"GrpcCode":   structpb.NewNumberValue(float64(codes.NotFound)),
			"Message":    structpb.NewStringValue("application type doesn't exist"),
			"ErrorType":  structpb.NewStringValue(errors.ErrorTypeNotFound), // "ErrorTypeNotFound"
			"IsExpected": structpb.NewBoolValue(true),
			"Code":       structpb.NewNumberValue(float64(Error_ErrApplicationTypeNotFound)), // 2002
		},
	})

	ErrApplicationTypeNotFound = errors.GoErr{
		ErrType:    errors.ErrorTypeNotFound, // "ErrorTypeNotFound"
		Status:     statusErrApplicationTypeNotFound,
		Code:       int(Error_ErrApplicationTypeNotFound), // 2002
		Message:    "application type doesn't exist",
		GrpcCode:   codes.NotFound,
		IsExpected: true,
	}
)

var (
	statusErrApplicationInfoNotFound, _ = status.New(
		codes.NotFound,
		"application info doesn't exist",
	).WithDetails(&structpb.Struct{
		Fields: map[string]*structpb.Value{
			"GrpcCode":   structpb.NewNumberValue(float64(codes.NotFound)),
			"Message":    structpb.NewStringValue("application info doesn't exist"),
			"ErrorType":  structpb.NewStringValue(errors.ErrorTypeNotFound), // "ErrorTypeNotFound"
			"IsExpected": structpb.NewBoolValue(true),
			"Code":       structpb.NewNumberValue(float64(Error_ErrApplicationInfoNotFound)), // 2004
		},
	})

	ErrApplicationInfoNotFound = errors.GoErr{
		ErrType:    errors.ErrorTypeNotFound, // "ErrorTypeNotFound"
		Status:     statusErrApplicationInfoNotFound,
		Code:       int(Error_ErrApplicationInfoNotFound), // 2004
		Message:    "application info doesn't exist",
		GrpcCode:   codes.NotFound,
		IsExpected: true,
	}
)

var (
	statusErrApplicationDuplicate, _ = status.New(
		codes.Unknown,
		"application already exists",
	).WithDetails(&structpb.Struct{
		Fields: map[string]*structpb.Value{
			"GrpcCode":   structpb.NewNumberValue(float64(codes.Unknown)),
			"Message":    structpb.NewStringValue("application already exists"),
			"ErrorType":  structpb.NewStringValue(errors.ErrorTypeUnexpectedError), // "ErrorTypeUnexpectedError"
			"IsExpected": structpb.NewBoolValue(true),
			"Code":       structpb.NewNumberValue(float64(Error_ErrApplicationDuplicate)), // 2100
		},
	})

	ErrApplicationDuplicate = errors.GoErr{
		ErrType:    errors.ErrorTypeUnexpectedError, // "ErrorTypeUnexpectedError"
		Status:     statusErrApplicationDuplicate,
		Code:       int(Error_ErrApplicationDuplicate), // 2100
		Message:    "application already exists",
		GrpcCode:   codes.Unknown,
		IsExpected: true,
	}
)

var (
	statusErrInvalidOwnerForMerchant, _ = status.New(
		codes.InvalidArgument,
		"invalid balance owner for this merchant",
	).WithDetails(&structpb.Struct{
		Fields: map[string]*structpb.Value{
			"GrpcCode":   structpb.NewNumberValue(float64(codes.InvalidArgument)),
			"Message":    structpb.NewStringValue("invalid balance owner for this merchant"),
			"ErrorType":  structpb.NewStringValue(errors.ErrorTypeBadRequest), // "ErrorTypeBadRequest"
			"IsExpected": structpb.NewBoolValue(true),
			"Code":       structpb.NewNumberValue(float64(Error_ErrInvalidOwnerForMerchant)), // 20000
		},
	})

	ErrInvalidOwnerForMerchant = errors.GoErr{
		ErrType:    errors.ErrorTypeBadRequest, // "ErrorTypeBadRequest"
		Status:     statusErrInvalidOwnerForMerchant,
		Code:       int(Error_ErrInvalidOwnerForMerchant), // 20000
		Message:    "invalid balance owner for this merchant",
		GrpcCode:   codes.InvalidArgument,
		IsExpected: true,
	}
)

var (
	statusErrMerchantAccountNotFound, _ = status.New(
		codes.NotFound,
		"merchant account doesn't exist",
	).WithDetails(&structpb.Struct{
		Fields: map[string]*structpb.Value{
			"GrpcCode":   structpb.NewNumberValue(float64(codes.NotFound)),
			"Message":    structpb.NewStringValue("merchant account doesn't exist"),
			"ErrorType":  structpb.NewStringValue(errors.ErrorTypeNotFound), // "ErrorTypeNotFound"
			"IsExpected": structpb.NewBoolValue(true),
			"Code":       structpb.NewNumberValue(float64(Error_ErrMerchantAccountNotFound)), // 20001
		},
	})

	ErrMerchantAccountNotFound = errors.GoErr{
		ErrType:    errors.ErrorTypeNotFound, // "ErrorTypeNotFound"
		Status:     statusErrMerchantAccountNotFound,
		Code:       int(Error_ErrMerchantAccountNotFound), // 20001
		Message:    "merchant account doesn't exist",
		GrpcCode:   codes.NotFound,
		IsExpected: true,
	}
)

var (
	statusErrAccountBalanceHistoryCreation, _ = status.New(
		codes.Internal,
		"account balance history creation failed",
	).WithDetails(&structpb.Struct{
		Fields: map[string]*structpb.Value{
			"GrpcCode":   structpb.NewNumberValue(float64(codes.Internal)),
			"Message":    structpb.NewStringValue("account balance history creation failed"),
			"ErrorType":  structpb.NewStringValue(errors.ErrorTypeUnexpectedError), // "ErrorTypeUnexpectedError"
			"IsExpected": structpb.NewBoolValue(true),
			"Code":       structpb.NewNumberValue(float64(Error_ErrAccountBalanceHistoryCreation)), // 20002
		},
	})

	ErrAccountBalanceHistoryCreation = errors.GoErr{
		ErrType:    errors.ErrorTypeUnexpectedError, // "ErrorTypeUnexpectedError"
		Status:     statusErrAccountBalanceHistoryCreation,
		Code:       int(Error_ErrAccountBalanceHistoryCreation), // 20002
		Message:    "account balance history creation failed",
		GrpcCode:   codes.Internal,
		IsExpected: true,
	}
)

var (
	statusErrProjectAccountNotFound, _ = status.New(
		codes.NotFound,
		"project account doesn't exist",
	).WithDetails(&structpb.Struct{
		Fields: map[string]*structpb.Value{
			"GrpcCode":   structpb.NewNumberValue(float64(codes.NotFound)),
			"Message":    structpb.NewStringValue("project account doesn't exist"),
			"ErrorType":  structpb.NewStringValue(errors.ErrorTypeNotFound), // "ErrorTypeNotFound"
			"IsExpected": structpb.NewBoolValue(true),
			"Code":       structpb.NewNumberValue(float64(Error_ErrProjectAccountNotFound)), // 20003
		},
	})

	ErrProjectAccountNotFound = errors.GoErr{
		ErrType:    errors.ErrorTypeNotFound, // "ErrorTypeNotFound"
		Status:     statusErrProjectAccountNotFound,
		Code:       int(Error_ErrProjectAccountNotFound), // 20003
		Message:    "project account doesn't exist",
		GrpcCode:   codes.NotFound,
		IsExpected: true,
	}
)

var (
	statusErrInvalidProjectAccount, _ = status.New(
		codes.InvalidArgument,
		"invalid project for this account",
	).WithDetails(&structpb.Struct{
		Fields: map[string]*structpb.Value{
			"GrpcCode":   structpb.NewNumberValue(float64(codes.InvalidArgument)),
			"Message":    structpb.NewStringValue("invalid project for this account"),
			"ErrorType":  structpb.NewStringValue(errors.ErrorTypeBadRequest), // "ErrorTypeBadRequest"
			"IsExpected": structpb.NewBoolValue(true),
			"Code":       structpb.NewNumberValue(float64(Error_ErrInvalidProjectAccount)), // 20004
		},
	})

	ErrInvalidProjectAccount = errors.GoErr{
		ErrType:    errors.ErrorTypeBadRequest, // "ErrorTypeBadRequest"
		Status:     statusErrInvalidProjectAccount,
		Code:       int(Error_ErrInvalidProjectAccount), // 20004
		Message:    "invalid project for this account",
		GrpcCode:   codes.InvalidArgument,
		IsExpected: true,
	}
)

var (
	statusErrProcessedOrderCreation, _ = status.New(
		codes.Internal,
		"processed order creation failed",
	).WithDetails(&structpb.Struct{
		Fields: map[string]*structpb.Value{
			"GrpcCode":   structpb.NewNumberValue(float64(codes.Internal)),
			"Message":    structpb.NewStringValue("processed order creation failed"),
			"ErrorType":  structpb.NewStringValue(errors.ErrorTypeUnexpectedError), // "ErrorTypeUnexpectedError"
			"IsExpected": structpb.NewBoolValue(true),
			"Code":       structpb.NewNumberValue(float64(Error_ErrProcessedOrderCreation)), // 20005
		},
	})

	ErrProcessedOrderCreation = errors.GoErr{
		ErrType:    errors.ErrorTypeUnexpectedError, // "ErrorTypeUnexpectedError"
		Status:     statusErrProcessedOrderCreation,
		Code:       int(Error_ErrProcessedOrderCreation), // 20005
		Message:    "processed order creation failed",
		GrpcCode:   codes.Internal,
		IsExpected: true,
	}
)

var (
	statusErrOrderIDNotFound, _ = status.New(
		codes.NotFound,
		"order id not found",
	).WithDetails(&structpb.Struct{
		Fields: map[string]*structpb.Value{
			"GrpcCode":   structpb.NewNumberValue(float64(codes.NotFound)),
			"Message":    structpb.NewStringValue("order id not found"),
			"ErrorType":  structpb.NewStringValue(errors.ErrorTypeNotFound), // "ErrorTypeNotFound"
			"IsExpected": structpb.NewBoolValue(true),
			"Code":       structpb.NewNumberValue(float64(Error_ErrOrderIDNotFound)), // 20006
		},
	})

	ErrOrderIDNotFound = errors.GoErr{
		ErrType:    errors.ErrorTypeNotFound, // "ErrorTypeNotFound"
		Status:     statusErrOrderIDNotFound,
		Code:       int(Error_ErrOrderIDNotFound), // 20006
		Message:    "order id not found",
		GrpcCode:   codes.NotFound,
		IsExpected: true,
	}
)

var (
	statusErrTransferNotFound, _ = status.New(
		codes.NotFound,
		"transfer not found",
	).WithDetails(&structpb.Struct{
		Fields: map[string]*structpb.Value{
			"GrpcCode":   structpb.NewNumberValue(float64(codes.NotFound)),
			"Message":    structpb.NewStringValue("transfer not found"),
			"ErrorType":  structpb.NewStringValue(errors.ErrorTypeNotFound), // "ErrorTypeNotFound"
			"IsExpected": structpb.NewBoolValue(true),
			"Code":       structpb.NewNumberValue(float64(Error_ErrTransferNotFound)), // 20007
		},
	})

	ErrTransferNotFound = errors.GoErr{
		ErrType:    errors.ErrorTypeNotFound, // "ErrorTypeNotFound"
		Status:     statusErrTransferNotFound,
		Code:       int(Error_ErrTransferNotFound), // 20007
		Message:    "transfer not found",
		GrpcCode:   codes.NotFound,
		IsExpected: true,
	}
)

var (
	statusErrTransferCreation, _ = status.New(
		codes.Internal,
		"transfer creation failed",
	).WithDetails(&structpb.Struct{
		Fields: map[string]*structpb.Value{
			"GrpcCode":   structpb.NewNumberValue(float64(codes.Internal)),
			"Message":    structpb.NewStringValue("transfer creation failed"),
			"ErrorType":  structpb.NewStringValue(errors.ErrorTypeUnexpectedError), // "ErrorTypeUnexpectedError"
			"IsExpected": structpb.NewBoolValue(true),
			"Code":       structpb.NewNumberValue(float64(Error_ErrTransferCreation)), // 20008
		},
	})

	ErrTransferCreation = errors.GoErr{
		ErrType:    errors.ErrorTypeUnexpectedError, // "ErrorTypeUnexpectedError"
		Status:     statusErrTransferCreation,
		Code:       int(Error_ErrTransferCreation), // 20008
		Message:    "transfer creation failed",
		GrpcCode:   codes.Internal,
		IsExpected: true,
	}
)

var (
	statusErrTransferRuleCreation, _ = status.New(
		codes.Internal,
		"transfer rule creation failed",
	).WithDetails(&structpb.Struct{
		Fields: map[string]*structpb.Value{
			"GrpcCode":   structpb.NewNumberValue(float64(codes.Internal)),
			"Message":    structpb.NewStringValue("transfer rule creation failed"),
			"ErrorType":  structpb.NewStringValue(errors.ErrorTypeUnexpectedError), // "ErrorTypeUnexpectedError"
			"IsExpected": structpb.NewBoolValue(true),
			"Code":       structpb.NewNumberValue(float64(Error_ErrTransferRuleCreation)), // 20032
		},
	})

	ErrTransferRuleCreation = errors.GoErr{
		ErrType:    errors.ErrorTypeUnexpectedError, // "ErrorTypeUnexpectedError"
		Status:     statusErrTransferRuleCreation,
		Code:       int(Error_ErrTransferRuleCreation), // 20032
		Message:    "transfer rule creation failed",
		GrpcCode:   codes.Internal,
		IsExpected: true,
	}
)

var (
	statusErrTransferHistoryCreation, _ = status.New(
		codes.Internal,
		"transfer history creation failed",
	).WithDetails(&structpb.Struct{
		Fields: map[string]*structpb.Value{
			"GrpcCode":   structpb.NewNumberValue(float64(codes.Internal)),
			"Message":    structpb.NewStringValue("transfer history creation failed"),
			"ErrorType":  structpb.NewStringValue(errors.ErrorTypeUnexpectedError), // "ErrorTypeUnexpectedError"
			"IsExpected": structpb.NewBoolValue(true),
			"Code":       structpb.NewNumberValue(float64(Error_ErrTransferHistoryCreation)), // 20033
		},
	})

	ErrTransferHistoryCreation = errors.GoErr{
		ErrType:    errors.ErrorTypeUnexpectedError, // "ErrorTypeUnexpectedError"
		Status:     statusErrTransferHistoryCreation,
		Code:       int(Error_ErrTransferHistoryCreation), // 20033
		Message:    "transfer history creation failed",
		GrpcCode:   codes.Internal,
		IsExpected: true,
	}
)

var (
	statusErrTransferStatusNotFound, _ = status.New(
		codes.NotFound,
		"transfer status not found",
	).WithDetails(&structpb.Struct{
		Fields: map[string]*structpb.Value{
			"GrpcCode":   structpb.NewNumberValue(float64(codes.NotFound)),
			"Message":    structpb.NewStringValue("transfer status not found"),
			"ErrorType":  structpb.NewStringValue(errors.ErrorTypeNotFound), // "ErrorTypeNotFound"
			"IsExpected": structpb.NewBoolValue(true),
			"Code":       structpb.NewNumberValue(float64(Error_ErrTransferStatusNotFound)), // 20009
		},
	})

	ErrTransferStatusNotFound = errors.GoErr{
		ErrType:    errors.ErrorTypeNotFound, // "ErrorTypeNotFound"
		Status:     statusErrTransferStatusNotFound,
		Code:       int(Error_ErrTransferStatusNotFound), // 20009
		Message:    "transfer status not found",
		GrpcCode:   codes.NotFound,
		IsExpected: true,
	}
)

var (
	statusErrTransferTypeNotFound, _ = status.New(
		codes.NotFound,
		"transfer type not found",
	).WithDetails(&structpb.Struct{
		Fields: map[string]*structpb.Value{
			"GrpcCode":   structpb.NewNumberValue(float64(codes.NotFound)),
			"Message":    structpb.NewStringValue("transfer type not found"),
			"ErrorType":  structpb.NewStringValue(errors.ErrorTypeNotFound), // "ErrorTypeNotFound"
			"IsExpected": structpb.NewBoolValue(true),
			"Code":       structpb.NewNumberValue(float64(Error_ErrTransferTypeNotFound)), // 20010
		},
	})

	ErrTransferTypeNotFound = errors.GoErr{
		ErrType:    errors.ErrorTypeNotFound, // "ErrorTypeNotFound"
		Status:     statusErrTransferTypeNotFound,
		Code:       int(Error_ErrTransferTypeNotFound), // 20010
		Message:    "transfer type not found",
		GrpcCode:   codes.NotFound,
		IsExpected: true,
	}
)

var (
	statusErrMerchantOrderNotFound, _ = status.New(
		codes.NotFound,
		"merchant order not found",
	).WithDetails(&structpb.Struct{
		Fields: map[string]*structpb.Value{
			"GrpcCode":   structpb.NewNumberValue(float64(codes.NotFound)),
			"Message":    structpb.NewStringValue("merchant order not found"),
			"ErrorType":  structpb.NewStringValue(errors.ErrorTypeNotFound), // "ErrorTypeNotFound"
			"IsExpected": structpb.NewBoolValue(true),
			"Code":       structpb.NewNumberValue(float64(Error_ErrMerchantOrderNotFound)), // 20011
		},
	})

	ErrMerchantOrderNotFound = errors.GoErr{
		ErrType:    errors.ErrorTypeNotFound, // "ErrorTypeNotFound"
		Status:     statusErrMerchantOrderNotFound,
		Code:       int(Error_ErrMerchantOrderNotFound), // 20011
		Message:    "merchant order not found",
		GrpcCode:   codes.NotFound,
		IsExpected: true,
	}
)

var (
	statusErrInsufficientBalance, _ = status.New(
		codes.Internal,
		"insufficient balance",
	).WithDetails(&structpb.Struct{
		Fields: map[string]*structpb.Value{
			"GrpcCode":   structpb.NewNumberValue(float64(codes.Internal)),
			"Message":    structpb.NewStringValue("insufficient balance"),
			"ErrorType":  structpb.NewStringValue(errors.ErrorTypeUnexpectedError), // "ErrorTypeUnexpectedError"
			"IsExpected": structpb.NewBoolValue(true),
			"Code":       structpb.NewNumberValue(float64(Error_ErrInsufficientBalance)), // 20012
		},
	})

	ErrInsufficientBalance = errors.GoErr{
		ErrType:    errors.ErrorTypeUnexpectedError, // "ErrorTypeUnexpectedError"
		Status:     statusErrInsufficientBalance,
		Code:       int(Error_ErrInsufficientBalance), // 20012
		Message:    "insufficient balance",
		GrpcCode:   codes.Internal,
		IsExpected: true,
	}
)

var (
	statusErrAntifraudSessionNotFound, _ = status.New(
		codes.NotFound,
		"antifraud session not found",
	).WithDetails(&structpb.Struct{
		Fields: map[string]*structpb.Value{
			"GrpcCode":   structpb.NewNumberValue(float64(codes.NotFound)),
			"Message":    structpb.NewStringValue("antifraud session not found"),
			"ErrorType":  structpb.NewStringValue(errors.ErrorTypeNotFound), // "ErrorTypeNotFound"
			"IsExpected": structpb.NewBoolValue(true),
			"Code":       structpb.NewNumberValue(float64(Error_ErrAntifraudSessionNotFound)), // 20013
		},
	})

	ErrAntifraudSessionNotFound = errors.GoErr{
		ErrType:    errors.ErrorTypeNotFound, // "ErrorTypeNotFound"
		Status:     statusErrAntifraudSessionNotFound,
		Code:       int(Error_ErrAntifraudSessionNotFound), // 20013
		Message:    "antifraud session not found",
		GrpcCode:   codes.NotFound,
		IsExpected: true,
	}
)

var (
	statusErrEmptyAgentCode, _ = status.New(
		codes.InvalidArgument,
		"agent code is required",
	).WithDetails(&structpb.Struct{
		Fields: map[string]*structpb.Value{
			"GrpcCode":   structpb.NewNumberValue(float64(codes.InvalidArgument)),
			"Message":    structpb.NewStringValue("agent code is required"),
			"ErrorType":  structpb.NewStringValue(errors.ErrorTypeBadRequest), // "ErrorTypeBadRequest"
			"IsExpected": structpb.NewBoolValue(true),
			"Code":       structpb.NewNumberValue(float64(Error_ErrEmptyAgentCode)), // 20014
		},
	})

	ErrEmptyAgentCode = errors.GoErr{
		ErrType:    errors.ErrorTypeBadRequest, // "ErrorTypeBadRequest"
		Status:     statusErrEmptyAgentCode,
		Code:       int(Error_ErrEmptyAgentCode), // 20014
		Message:    "agent code is required",
		GrpcCode:   codes.InvalidArgument,
		IsExpected: true,
	}
)

var (
	statusErrRequiredExternalReferenceID, _ = status.New(
		codes.InvalidArgument,
		"external reference is required",
	).WithDetails(&structpb.Struct{
		Fields: map[string]*structpb.Value{
			"GrpcCode":   structpb.NewNumberValue(float64(codes.InvalidArgument)),
			"Message":    structpb.NewStringValue("external reference is required"),
			"ErrorType":  structpb.NewStringValue(errors.ErrorTypeBadRequest), // "ErrorTypeBadRequest"
			"IsExpected": structpb.NewBoolValue(true),
			"Code":       structpb.NewNumberValue(float64(Error_ErrRequiredExternalReferenceID)), // 20015
		},
	})

	ErrRequiredExternalReferenceID = errors.GoErr{
		ErrType:    errors.ErrorTypeBadRequest, // "ErrorTypeBadRequest"
		Status:     statusErrRequiredExternalReferenceID,
		Code:       int(Error_ErrRequiredExternalReferenceID), // 20015
		Message:    "external reference is required",
		GrpcCode:   codes.InvalidArgument,
		IsExpected: true,
	}
)

var (
	statusErrRequiredTransferID, _ = status.New(
		codes.InvalidArgument,
		"transfer id is required",
	).WithDetails(&structpb.Struct{
		Fields: map[string]*structpb.Value{
			"GrpcCode":   structpb.NewNumberValue(float64(codes.InvalidArgument)),
			"Message":    structpb.NewStringValue("transfer id is required"),
			"ErrorType":  structpb.NewStringValue(errors.ErrorTypeBadRequest), // "ErrorTypeBadRequest"
			"IsExpected": structpb.NewBoolValue(true),
			"Code":       structpb.NewNumberValue(float64(Error_ErrRequiredTransferID)), // 20016
		},
	})

	ErrRequiredTransferID = errors.GoErr{
		ErrType:    errors.ErrorTypeBadRequest, // "ErrorTypeBadRequest"
		Status:     statusErrRequiredTransferID,
		Code:       int(Error_ErrRequiredTransferID), // 20016
		Message:    "transfer id is required",
		GrpcCode:   codes.InvalidArgument,
		IsExpected: true,
	}
)

var (
	statusErrBalanceNotFound, _ = status.New(
		codes.NotFound,
		"balance doesn't exist",
	).WithDetails(&structpb.Struct{
		Fields: map[string]*structpb.Value{
			"GrpcCode":   structpb.NewNumberValue(float64(codes.NotFound)),
			"Message":    structpb.NewStringValue("balance doesn't exist"),
			"ErrorType":  structpb.NewStringValue(errors.ErrorTypeNotFound), // "ErrorTypeNotFound"
			"IsExpected": structpb.NewBoolValue(true),
			"Code":       structpb.NewNumberValue(float64(Error_ErrBalanceNotFound)), // 20017
		},
	})

	ErrBalanceNotFound = errors.GoErr{
		ErrType:    errors.ErrorTypeNotFound, // "ErrorTypeNotFound"
		Status:     statusErrBalanceNotFound,
		Code:       int(Error_ErrBalanceNotFound), // 20017
		Message:    "balance doesn't exist",
		GrpcCode:   codes.NotFound,
		IsExpected: true,
	}
)

var (
	statusErrBalanceAccountNotFound, _ = status.New(
		codes.NotFound,
		"balance account doesn't exist",
	).WithDetails(&structpb.Struct{
		Fields: map[string]*structpb.Value{
			"GrpcCode":   structpb.NewNumberValue(float64(codes.NotFound)),
			"Message":    structpb.NewStringValue("balance account doesn't exist"),
			"ErrorType":  structpb.NewStringValue(errors.ErrorTypeNotFound), // "ErrorTypeNotFound"
			"IsExpected": structpb.NewBoolValue(true),
			"Code":       structpb.NewNumberValue(float64(Error_ErrBalanceAccountNotFound)), // 20018
		},
	})

	ErrBalanceAccountNotFound = errors.GoErr{
		ErrType:    errors.ErrorTypeNotFound, // "ErrorTypeNotFound"
		Status:     statusErrBalanceAccountNotFound,
		Code:       int(Error_ErrBalanceAccountNotFound), // 20018
		Message:    "balance account doesn't exist",
		GrpcCode:   codes.NotFound,
		IsExpected: true,
	}
)

var (
	statusErrNegativeCreditBalanceAmount, _ = status.New(
		codes.Internal,
		"credit balance cannot be negative",
	).WithDetails(&structpb.Struct{
		Fields: map[string]*structpb.Value{
			"GrpcCode":   structpb.NewNumberValue(float64(codes.Internal)),
			"Message":    structpb.NewStringValue("credit balance cannot be negative"),
			"ErrorType":  structpb.NewStringValue(errors.ErrorTypeUnexpectedError), // "ErrorTypeUnexpectedError"
			"IsExpected": structpb.NewBoolValue(true),
			"Code":       structpb.NewNumberValue(float64(Error_ErrNegativeCreditBalanceAmount)), // 20019
		},
	})

	ErrNegativeCreditBalanceAmount = errors.GoErr{
		ErrType:    errors.ErrorTypeUnexpectedError, // "ErrorTypeUnexpectedError"
		Status:     statusErrNegativeCreditBalanceAmount,
		Code:       int(Error_ErrNegativeCreditBalanceAmount), // 20019
		Message:    "credit balance cannot be negative",
		GrpcCode:   codes.Internal,
		IsExpected: true,
	}
)

var (
	statusErrCreditBalanceNotFound, _ = status.New(
		codes.NotFound,
		"credit balance doesn't exist",
	).WithDetails(&structpb.Struct{
		Fields: map[string]*structpb.Value{
			"GrpcCode":   structpb.NewNumberValue(float64(codes.NotFound)),
			"Message":    structpb.NewStringValue("credit balance doesn't exist"),
			"ErrorType":  structpb.NewStringValue(errors.ErrorTypeNotFound), // "ErrorTypeNotFound"
			"IsExpected": structpb.NewBoolValue(true),
			"Code":       structpb.NewNumberValue(float64(Error_ErrCreditBalanceNotFound)), // 20020
		},
	})

	ErrCreditBalanceNotFound = errors.GoErr{
		ErrType:    errors.ErrorTypeNotFound, // "ErrorTypeNotFound"
		Status:     statusErrCreditBalanceNotFound,
		Code:       int(Error_ErrCreditBalanceNotFound), // 20020
		Message:    "credit balance doesn't exist",
		GrpcCode:   codes.NotFound,
		IsExpected: true,
	}
)

var (
	statusErrBalanceOwnerNotFound, _ = status.New(
		codes.NotFound,
		"balance owner doesn't exist",
	).WithDetails(&structpb.Struct{
		Fields: map[string]*structpb.Value{
			"GrpcCode":   structpb.NewNumberValue(float64(codes.NotFound)),
			"Message":    structpb.NewStringValue("balance owner doesn't exist"),
			"ErrorType":  structpb.NewStringValue(errors.ErrorTypeNotFound), // "ErrorTypeNotFound"
			"IsExpected": structpb.NewBoolValue(true),
			"Code":       structpb.NewNumberValue(float64(Error_ErrBalanceOwnerNotFound)), // 20021
		},
	})

	ErrBalanceOwnerNotFound = errors.GoErr{
		ErrType:    errors.ErrorTypeNotFound, // "ErrorTypeNotFound"
		Status:     statusErrBalanceOwnerNotFound,
		Code:       int(Error_ErrBalanceOwnerNotFound), // 20021
		Message:    "balance owner doesn't exist",
		GrpcCode:   codes.NotFound,
		IsExpected: true,
	}
)

var (
	statusErrNegativeBalanceAmount, _ = status.New(
		codes.NotFound,
		"balance cannot be negative",
	).WithDetails(&structpb.Struct{
		Fields: map[string]*structpb.Value{
			"GrpcCode":   structpb.NewNumberValue(float64(codes.NotFound)),
			"Message":    structpb.NewStringValue("balance cannot be negative"),
			"ErrorType":  structpb.NewStringValue(errors.ErrorTypeNotFound), // "ErrorTypeNotFound"
			"IsExpected": structpb.NewBoolValue(true),
			"Code":       structpb.NewNumberValue(float64(Error_ErrNegativeBalanceAmount)), // 20022
		},
	})

	ErrNegativeBalanceAmount = errors.GoErr{
		ErrType:    errors.ErrorTypeNotFound, // "ErrorTypeNotFound"
		Status:     statusErrNegativeBalanceAmount,
		Code:       int(Error_ErrNegativeBalanceAmount), // 20022
		Message:    "balance cannot be negative",
		GrpcCode:   codes.NotFound,
		IsExpected: true,
	}
)

var (
	statusErrFindAcquirerCommission, _ = status.New(
		codes.NotFound,
		"could not find proper acquirer commission",
	).WithDetails(&structpb.Struct{
		Fields: map[string]*structpb.Value{
			"GrpcCode":   structpb.NewNumberValue(float64(codes.NotFound)),
			"Message":    structpb.NewStringValue("could not find proper acquirer commission"),
			"ErrorType":  structpb.NewStringValue(errors.ErrorTypeNotFound), // "ErrorTypeNotFound"
			"IsExpected": structpb.NewBoolValue(true),
			"Code":       structpb.NewNumberValue(float64(Error_ErrFindAcquirerCommission)), // 20023
		},
	})

	ErrFindAcquirerCommission = errors.GoErr{
		ErrType:    errors.ErrorTypeNotFound, // "ErrorTypeNotFound"
		Status:     statusErrFindAcquirerCommission,
		Code:       int(Error_ErrFindAcquirerCommission), // 20023
		Message:    "could not find proper acquirer commission",
		GrpcCode:   codes.NotFound,
		IsExpected: true,
	}
)

var (
	statusErrFindProjectLowerCommissions, _ = status.New(
		codes.NotFound,
		"could not find proper project lower commission",
	).WithDetails(&structpb.Struct{
		Fields: map[string]*structpb.Value{
			"GrpcCode":   structpb.NewNumberValue(float64(codes.NotFound)),
			"Message":    structpb.NewStringValue("could not find proper project lower commission"),
			"ErrorType":  structpb.NewStringValue(errors.ErrorTypeNotFound), // "ErrorTypeNotFound"
			"IsExpected": structpb.NewBoolValue(true),
			"Code":       structpb.NewNumberValue(float64(Error_ErrFindProjectLowerCommissions)), // 20024
		},
	})

	ErrFindProjectLowerCommissions = errors.GoErr{
		ErrType:    errors.ErrorTypeNotFound, // "ErrorTypeNotFound"
		Status:     statusErrFindProjectLowerCommissions,
		Code:       int(Error_ErrFindProjectLowerCommissions), // 20024
		Message:    "could not find proper project lower commission",
		GrpcCode:   codes.NotFound,
		IsExpected: true,
	}
)

var (
	statusErrFindProjectUpperCommissions, _ = status.New(
		codes.NotFound,
		"could not find proper project upper commission",
	).WithDetails(&structpb.Struct{
		Fields: map[string]*structpb.Value{
			"GrpcCode":   structpb.NewNumberValue(float64(codes.NotFound)),
			"Message":    structpb.NewStringValue("could not find proper project upper commission"),
			"ErrorType":  structpb.NewStringValue(errors.ErrorTypeNotFound), // "ErrorTypeNotFound"
			"IsExpected": structpb.NewBoolValue(true),
			"Code":       structpb.NewNumberValue(float64(Error_ErrFindProjectUpperCommissions)), // 20025
		},
	})

	ErrFindProjectUpperCommissions = errors.GoErr{
		ErrType:    errors.ErrorTypeNotFound, // "ErrorTypeNotFound"
		Status:     statusErrFindProjectUpperCommissions,
		Code:       int(Error_ErrFindProjectUpperCommissions), // 20025
		Message:    "could not find proper project upper commission",
		GrpcCode:   codes.NotFound,
		IsExpected: true,
	}
)

var (
	statusErrCommissionDateRangeOverlapping, _ = status.New(
		codes.Internal,
		"overlap in commission date ranges",
	).WithDetails(&structpb.Struct{
		Fields: map[string]*structpb.Value{
			"GrpcCode":   structpb.NewNumberValue(float64(codes.Internal)),
			"Message":    structpb.NewStringValue("overlap in commission date ranges"),
			"ErrorType":  structpb.NewStringValue(errors.ErrorTypeUnexpectedError), // "ErrorTypeUnexpectedError"
			"IsExpected": structpb.NewBoolValue(true),
			"Code":       structpb.NewNumberValue(float64(Error_ErrCommissionDateRangeOverlapping)), // 20026
		},
	})

	ErrCommissionDateRangeOverlapping = errors.GoErr{
		ErrType:    errors.ErrorTypeUnexpectedError, // "ErrorTypeUnexpectedError"
		Status:     statusErrCommissionDateRangeOverlapping,
		Code:       int(Error_ErrCommissionDateRangeOverlapping), // 20026
		Message:    "overlap in commission date ranges",
		GrpcCode:   codes.Internal,
		IsExpected: true,
	}
)

var (
	statusErrProjectOptionTypeNotFound, _ = status.New(
		codes.NotFound,
		"undefined project option type",
	).WithDetails(&structpb.Struct{
		Fields: map[string]*structpb.Value{
			"GrpcCode":   structpb.NewNumberValue(float64(codes.NotFound)),
			"Message":    structpb.NewStringValue("undefined project option type"),
			"ErrorType":  structpb.NewStringValue(errors.ErrorTypeNotFound), // "ErrorTypeNotFound"
			"IsExpected": structpb.NewBoolValue(true),
			"Code":       structpb.NewNumberValue(float64(Error_ErrProjectOptionTypeNotFound)), // 20027
		},
	})

	ErrProjectOptionTypeNotFound = errors.GoErr{
		ErrType:    errors.ErrorTypeNotFound, // "ErrorTypeNotFound"
		Status:     statusErrProjectOptionTypeNotFound,
		Code:       int(Error_ErrProjectOptionTypeNotFound), // 20027
		Message:    "undefined project option type",
		GrpcCode:   codes.NotFound,
		IsExpected: true,
	}
)

var (
	statusErrDateTimeFormat, _ = status.New(
		codes.InvalidArgument,
		"wrong date format. Must be yyyy-mm-dd hh:mm:ss",
	).WithDetails(&structpb.Struct{
		Fields: map[string]*structpb.Value{
			"GrpcCode":   structpb.NewNumberValue(float64(codes.InvalidArgument)),
			"Message":    structpb.NewStringValue("wrong date format. Must be yyyy-mm-dd hh:mm:ss"),
			"ErrorType":  structpb.NewStringValue(errors.ErrorTypeBadRequest), // "ErrorTypeBadRequest"
			"IsExpected": structpb.NewBoolValue(true),
			"Code":       structpb.NewNumberValue(float64(Error_ErrDateTimeFormat)), // 20028
		},
	})

	ErrDateTimeFormat = errors.GoErr{
		ErrType:    errors.ErrorTypeBadRequest, // "ErrorTypeBadRequest"
		Status:     statusErrDateTimeFormat,
		Code:       int(Error_ErrDateTimeFormat), // 20028
		Message:    "wrong date format. Must be yyyy-mm-dd hh:mm:ss",
		GrpcCode:   codes.InvalidArgument,
		IsExpected: true,
	}
)

var (
	statusErrCommissionStartDate, _ = status.New(
		codes.InvalidArgument,
		"commission start date must be current or future",
	).WithDetails(&structpb.Struct{
		Fields: map[string]*structpb.Value{
			"GrpcCode":   structpb.NewNumberValue(float64(codes.InvalidArgument)),
			"Message":    structpb.NewStringValue("commission start date must be current or future"),
			"ErrorType":  structpb.NewStringValue(errors.ErrorTypeBadRequest), // "ErrorTypeBadRequest"
			"IsExpected": structpb.NewBoolValue(true),
			"Code":       structpb.NewNumberValue(float64(Error_ErrCommissionStartDate)), // 20029
		},
	})

	ErrCommissionStartDate = errors.GoErr{
		ErrType:    errors.ErrorTypeBadRequest, // "ErrorTypeBadRequest"
		Status:     statusErrCommissionStartDate,
		Code:       int(Error_ErrCommissionStartDate), // 20029
		Message:    "commission start date must be current or future",
		GrpcCode:   codes.InvalidArgument,
		IsExpected: true,
	}
)

var (
	statusErrCurrentCommission, _ = status.New(
		codes.InvalidArgument,
		"unable to change current commission",
	).WithDetails(&structpb.Struct{
		Fields: map[string]*structpb.Value{
			"GrpcCode":   structpb.NewNumberValue(float64(codes.InvalidArgument)),
			"Message":    structpb.NewStringValue("unable to change current commission"),
			"ErrorType":  structpb.NewStringValue(errors.ErrorTypeBadRequest), // "ErrorTypeBadRequest"
			"IsExpected": structpb.NewBoolValue(true),
			"Code":       structpb.NewNumberValue(float64(Error_ErrCurrentCommission)), // 20030
		},
	})

	ErrCurrentCommission = errors.GoErr{
		ErrType:    errors.ErrorTypeBadRequest, // "ErrorTypeBadRequest"
		Status:     statusErrCurrentCommission,
		Code:       int(Error_ErrCurrentCommission), // 20030
		Message:    "unable to change current commission",
		GrpcCode:   codes.InvalidArgument,
		IsExpected: true,
	}
)

var (
	statusErrPaymentOrderOwnerNotFound, _ = status.New(
		codes.NotFound,
		"payment order owner not found",
	).WithDetails(&structpb.Struct{
		Fields: map[string]*structpb.Value{
			"GrpcCode":   structpb.NewNumberValue(float64(codes.NotFound)),
			"Message":    structpb.NewStringValue("payment order owner not found"),
			"ErrorType":  structpb.NewStringValue(errors.ErrorTypeNotFound), // "ErrorTypeNotFound"
			"IsExpected": structpb.NewBoolValue(true),
			"Code":       structpb.NewNumberValue(float64(Error_ErrPaymentOrderOwnerNotFound)), // 20031
		},
	})

	ErrPaymentOrderOwnerNotFound = errors.GoErr{
		ErrType:    errors.ErrorTypeNotFound, // "ErrorTypeNotFound"
		Status:     statusErrPaymentOrderOwnerNotFound,
		Code:       int(Error_ErrPaymentOrderOwnerNotFound), // 20031
		Message:    "payment order owner not found",
		GrpcCode:   codes.NotFound,
		IsExpected: true,
	}
)
