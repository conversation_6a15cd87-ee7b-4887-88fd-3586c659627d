// Code generated by MockGen. DO NOT EDIT.
// Source: transfer_automatic.gin.pb.go

// Package grpcmock is a generated GoMock package.
package grpcmock

import (
	reflect "reflect"

	gin "github.com/gin-gonic/gin"
	gomock "github.com/golang/mock/gomock"
)

// MockGinTransferAutomaticServer is a mock of GinTransferAutomaticServer interface.
type MockGinTransferAutomaticServer struct {
	ctrl     *gomock.Controller
	recorder *MockGinTransferAutomaticServerMockRecorder
}

// MockGinTransferAutomaticServerMockRecorder is the mock recorder for MockGinTransferAutomaticServer.
type MockGinTransferAutomaticServerMockRecorder struct {
	mock *MockGinTransferAutomaticServer
}

// NewMockGinTransferAutomaticServer creates a new mock instance.
func NewMockGinTransferAutomaticServer(ctrl *gomock.Controller) *MockGinTransferAutomaticServer {
	mock := &MockGinTransferAutomaticServer{ctrl: ctrl}
	mock.recorder = &MockGinTransferAutomaticServerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockGinTransferAutomaticServer) EXPECT() *MockGinTransferAutomaticServerMockRecorder {
	return m.recorder
}

// StartCreateTransferByRulesWorker mocks base method.
func (m *MockGinTransferAutomaticServer) StartCreateTransferByRulesWorker(c *gin.Context) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "StartCreateTransferByRulesWorker", c)
	ret0, _ := ret[0].(error)
	return ret0
}

// StartCreateTransferByRulesWorker indicates an expected call of StartCreateTransferByRulesWorker.
func (mr *MockGinTransferAutomaticServerMockRecorder) StartCreateTransferByRulesWorker(c interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "StartCreateTransferByRulesWorker", reflect.TypeOf((*MockGinTransferAutomaticServer)(nil).StartCreateTransferByRulesWorker), c)
}
