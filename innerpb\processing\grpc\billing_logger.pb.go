// Code generated by protoc-gen-go-mvp. DO NOT EDIT.
// versions:
// - protoc-gen-go-mvp v2.0.1
// created by mvp authors

package grpc

import (
	context "context"
	fmt "fmt"
	cntx "git.local/sensitive/mvp/pkg/cntx"
	ctxzap "github.com/grpc-ecosystem/go-grpc-middleware/logging/zap/ctxzap"
	zap "go.uber.org/zap"
	grpc "google.golang.org/grpc"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
)

func file_inner_processing_grpc_billing_proto_message_EmptyToZap(
	label string,
	in *emptypb.Empty,
) zap.Field {
	if in == nil {
		return zap.Reflect(label, in)
	}

	return zap.Dict(
		label,
	)
}

func file_inner_processing_grpc_billing_proto_message_TimestampToZap(
	label string,
	in *timestamppb.Timestamp,
) zap.Field {
	if in == nil {
		return zap.Reflect(label, in)
	}

	return zap.Dict(
		label,
		zap.Any("Seconds", in.GetSeconds()),
		zap.Any("Nanos", in.GetNanos()),
	)
}

func file_inner_processing_grpc_billing_proto_message_BillInTransferRequestV1ToZap(
	label string,
	in *BillInTransferRequestV1,
) zap.Field {
	if in == nil {
		return zap.Reflect(label, in)
	}

	return zap.Dict(
		label,
		zap.Any("ProjectId", in.GetProjectId()),
		zap.Any("AccountNumber", in.GetAccountNumber()),
		zap.Any("Amount", in.GetAmount()),
		zap.Any("MerchantId", in.GetMerchantId()),
		zap.Any("TransferId", in.GetTransferId()),
	)
}

func file_inner_processing_grpc_billing_proto_message_BillOutTransferRequestV1ToZap(
	label string,
	in *BillOutTransferRequestV1,
) zap.Field {
	if in == nil {
		return zap.Reflect(label, in)
	}

	return zap.Dict(
		label,
		zap.Any("ProjectId", in.GetProjectId()),
		zap.Any("AccountNumber", in.GetAccountNumber()),
		zap.Any("Amount", in.GetAmount()),
		zap.Any("MerchantId", in.GetMerchantId()),
		zap.Any("TransferId", in.GetTransferId()),
	)
}

func file_inner_processing_grpc_billing_proto_message_BillPayInTransactionRequestV1ToZap(
	label string,
	in *BillPayInTransactionRequestV1,
) zap.Field {
	if in == nil {
		return zap.Reflect(label, in)
	}

	return zap.Dict(
		label,
		zap.Any("MerchantId", in.GetMerchantId()),
		zap.Any("ProjectId", in.GetProjectId()),
		zap.Any("TransactionId", in.GetTransactionId()),
		zap.Any("Amount", in.GetAmount()),
		zap.Any("TerminalId", in.GetTerminalId()),
	)
}

func file_inner_processing_grpc_billing_proto_message_BillPayOutTransactionRequestV1ToZap(
	label string,
	in *BillPayOutTransactionRequestV1,
) zap.Field {
	if in == nil {
		return zap.Reflect(label, in)
	}

	return zap.Dict(
		label,
		zap.Any("MerchantId", in.GetMerchantId()),
		zap.Any("ProjectId", in.GetProjectId()),
		zap.Any("TransactionId", in.GetTransactionId()),
		zap.Any("Amount", in.GetAmount()),
		zap.Any("TerminalId", in.GetTerminalId()),
		file_inner_processing_grpc_billing_proto_message_TimestampToZap("TransactionCreatedAt", in.GetTransactionCreatedAt()),
	)
}

func file_inner_processing_grpc_billing_proto_message_BillRefundTransactionRequestV1ToZap(
	label string,
	in *BillRefundTransactionRequestV1,
) zap.Field {
	if in == nil {
		return zap.Reflect(label, in)
	}

	return zap.Dict(
		label,
		zap.Any("MerchantId", in.GetMerchantId()),
		zap.Any("ProjectId", in.GetProjectId()),
		zap.Any("RefundId", in.GetRefundId()),
		zap.Any("Amount", in.GetAmount()),
		zap.Any("TerminalId", in.GetTerminalId()),
	)
}

func file_inner_processing_grpc_billing_proto_message_BillSplitTransferRequestV1ToZap(
	label string,
	in *BillSplitTransferRequestV1,
) zap.Field {
	if in == nil {
		return zap.Reflect(label, in)
	}

	return zap.Dict(
		label,
		file_inner_processing_grpc_billing_proto_message_SplitTransferOperationV1SliceToZap("Operations", in.GetOperations()),
	)
}

func file_inner_processing_grpc_billing_proto_enum_BillingBalanceAccountStatusToZap(
	label string,
	in BillingBalanceAccountStatus,
) zap.Field {
	str, ok := BillingBalanceAccountStatus_name[int32(in)]
	if !ok {
		str = fmt.Sprintf("unknown BillingBalanceAccountStatus value %d", in)
	}

	return zap.Dict(
		label,
		zap.Any("value", BillingBalanceAccountStatus(in)),
		zap.Any("name", str),
	)
}

func file_inner_processing_grpc_billing_proto_message_BillingBalanceAccountStatusRefToZap(
	label string,
	in *BillingBalanceAccountStatusRef,
) zap.Field {
	if in == nil {
		return zap.Reflect(label, in)
	}

	return zap.Dict(
		label,
		zap.Any("Code", in.GetCode()),
		zap.Any("Name", in.GetName()),
	)
}

func file_inner_processing_grpc_billing_proto_enum_BillingBalanceCreditStatusToZap(
	label string,
	in BillingBalanceCreditStatus,
) zap.Field {
	str, ok := BillingBalanceCreditStatus_name[int32(in)]
	if !ok {
		str = fmt.Sprintf("unknown BillingBalanceCreditStatus value %d", in)
	}

	return zap.Dict(
		label,
		zap.Any("value", BillingBalanceCreditStatus(in)),
		zap.Any("name", str),
	)
}

func file_inner_processing_grpc_billing_proto_message_BillingBalanceCreditStatusRefToZap(
	label string,
	in *BillingBalanceCreditStatusRef,
) zap.Field {
	if in == nil {
		return zap.Reflect(label, in)
	}

	return zap.Dict(
		label,
		zap.Any("Code", in.GetCode()),
		zap.Any("Name", in.GetName()),
	)
}

func file_inner_processing_grpc_billing_proto_enum_BillingBalanceOwnerStatusToZap(
	label string,
	in BillingBalanceOwnerStatus,
) zap.Field {
	str, ok := BillingBalanceOwnerStatus_name[int32(in)]
	if !ok {
		str = fmt.Sprintf("unknown BillingBalanceOwnerStatus value %d", in)
	}

	return zap.Dict(
		label,
		zap.Any("value", BillingBalanceOwnerStatus(in)),
		zap.Any("name", str),
	)
}

func file_inner_processing_grpc_billing_proto_message_BillingBalanceOwnerStatusRefToZap(
	label string,
	in *BillingBalanceOwnerStatusRef,
) zap.Field {
	if in == nil {
		return zap.Reflect(label, in)
	}

	return zap.Dict(
		label,
		zap.Any("Code", in.GetCode()),
		zap.Any("Name", in.GetName()),
	)
}

func file_inner_processing_grpc_billing_proto_enum_BillingBalanceOwnerTypeToZap(
	label string,
	in BillingBalanceOwnerType,
) zap.Field {
	str, ok := BillingBalanceOwnerType_name[int32(in)]
	if !ok {
		str = fmt.Sprintf("unknown BillingBalanceOwnerType value %d", in)
	}

	return zap.Dict(
		label,
		zap.Any("value", BillingBalanceOwnerType(in)),
		zap.Any("name", str),
	)
}

func file_inner_processing_grpc_billing_proto_message_BillingBalanceOwnerTypeRefToZap(
	label string,
	in *BillingBalanceOwnerTypeRef,
) zap.Field {
	if in == nil {
		return zap.Reflect(label, in)
	}

	return zap.Dict(
		label,
		zap.Any("Code", in.GetCode()),
		zap.Any("Name", in.GetName()),
	)
}

func file_inner_processing_grpc_billing_proto_enum_BillingEarnTypeToZap(
	label string,
	in BillingEarnType,
) zap.Field {
	str, ok := BillingEarnType_name[int32(in)]
	if !ok {
		str = fmt.Sprintf("unknown BillingEarnType value %d", in)
	}

	return zap.Dict(
		label,
		zap.Any("value", BillingEarnType(in)),
		zap.Any("name", str),
	)
}

func file_inner_processing_grpc_billing_proto_message_BillingEarnTypeRefToZap(
	label string,
	in *BillingEarnTypeRef,
) zap.Field {
	if in == nil {
		return zap.Reflect(label, in)
	}

	return zap.Dict(
		label,
		zap.Any("Code", in.GetCode()),
		zap.Any("Name", in.GetName()),
	)
}

func file_inner_processing_grpc_billing_proto_enum_BillingOperationBalanceTypeToZap(
	label string,
	in BillingOperationBalanceType,
) zap.Field {
	str, ok := BillingOperationBalanceType_name[int32(in)]
	if !ok {
		str = fmt.Sprintf("unknown BillingOperationBalanceType value %d", in)
	}

	return zap.Dict(
		label,
		zap.Any("value", BillingOperationBalanceType(in)),
		zap.Any("name", str),
	)
}

func file_inner_processing_grpc_billing_proto_message_BillingOperationBalanceTypeRefToZap(
	label string,
	in *BillingOperationBalanceTypeRef,
) zap.Field {
	if in == nil {
		return zap.Reflect(label, in)
	}

	return zap.Dict(
		label,
		zap.Any("Code", in.GetCode()),
		zap.Any("Name", in.GetName()),
	)
}

func file_inner_processing_grpc_billing_proto_enum_BillingOperationModeToZap(
	label string,
	in BillingOperationMode,
) zap.Field {
	str, ok := BillingOperationMode_name[int32(in)]
	if !ok {
		str = fmt.Sprintf("unknown BillingOperationMode value %d", in)
	}

	return zap.Dict(
		label,
		zap.Any("value", BillingOperationMode(in)),
		zap.Any("name", str),
	)
}

func file_inner_processing_grpc_billing_proto_message_BillingOperationModeRefToZap(
	label string,
	in *BillingOperationModeRef,
) zap.Field {
	if in == nil {
		return zap.Reflect(label, in)
	}

	return zap.Dict(
		label,
		zap.Any("Code", in.GetCode()),
		zap.Any("Name", in.GetName()),
	)
}

func file_inner_processing_grpc_billing_proto_enum_BillingOperationStatusToZap(
	label string,
	in BillingOperationStatus,
) zap.Field {
	str, ok := BillingOperationStatus_name[int32(in)]
	if !ok {
		str = fmt.Sprintf("unknown BillingOperationStatus value %d", in)
	}

	return zap.Dict(
		label,
		zap.Any("value", BillingOperationStatus(in)),
		zap.Any("name", str),
	)
}

func file_inner_processing_grpc_billing_proto_message_BillingOperationStatusRefToZap(
	label string,
	in *BillingOperationStatusRef,
) zap.Field {
	if in == nil {
		return zap.Reflect(label, in)
	}

	return zap.Dict(
		label,
		zap.Any("Code", in.GetCode()),
		zap.Any("Name", in.GetName()),
	)
}

func file_inner_processing_grpc_billing_proto_enum_BillingOperationTypeToZap(
	label string,
	in BillingOperationType,
) zap.Field {
	str, ok := BillingOperationType_name[int32(in)]
	if !ok {
		str = fmt.Sprintf("unknown BillingOperationType value %d", in)
	}

	return zap.Dict(
		label,
		zap.Any("value", BillingOperationType(in)),
		zap.Any("name", str),
	)
}

func file_inner_processing_grpc_billing_proto_enum_BillingOperationTypeGroupToZap(
	label string,
	in BillingOperationTypeGroup,
) zap.Field {
	str, ok := BillingOperationTypeGroup_name[int32(in)]
	if !ok {
		str = fmt.Sprintf("unknown BillingOperationTypeGroup value %d", in)
	}

	return zap.Dict(
		label,
		zap.Any("value", BillingOperationTypeGroup(in)),
		zap.Any("name", str),
	)
}

func file_inner_processing_grpc_billing_proto_message_BillingOperationTypeGroupRefToZap(
	label string,
	in *BillingOperationTypeGroupRef,
) zap.Field {
	if in == nil {
		return zap.Reflect(label, in)
	}

	return zap.Dict(
		label,
		zap.Any("Code", in.GetCode()),
		zap.Any("Name", in.GetName()),
	)
}

func file_inner_processing_grpc_billing_proto_enum_BillingOperationTypeGroupRelationToZap(
	label string,
	in BillingOperationTypeGroupRelation,
) zap.Field {
	str, ok := BillingOperationTypeGroupRelation_name[int32(in)]
	if !ok {
		str = fmt.Sprintf("unknown BillingOperationTypeGroupRelation value %d", in)
	}

	return zap.Dict(
		label,
		zap.Any("value", BillingOperationTypeGroupRelation(in)),
		zap.Any("name", str),
	)
}

func file_inner_processing_grpc_billing_proto_message_BillingOperationTypeGroupRelationRefToZap(
	label string,
	in *BillingOperationTypeGroupRelationRef,
) zap.Field {
	if in == nil {
		return zap.Reflect(label, in)
	}

	return zap.Dict(
		label,
		file_inner_processing_grpc_billing_proto_enum_BillingOperationTypeToZap("OperationType", in.GetOperationType()),
		file_inner_processing_grpc_billing_proto_enum_BillingOperationTypeGroupToZap("OperationTypeGroup", in.GetOperationTypeGroup()),
		file_inner_processing_grpc_billing_proto_enum_BillingOperationModeToZap("OperationMode", in.GetOperationMode()),
	)
}

func file_inner_processing_grpc_billing_proto_message_BillingOperationTypeRefToZap(
	label string,
	in *BillingOperationTypeRef,
) zap.Field {
	if in == nil {
		return zap.Reflect(label, in)
	}

	return zap.Dict(
		label,
		zap.Any("Code", in.GetCode()),
		zap.Any("Name", in.GetName()),
	)
}

func file_inner_processing_grpc_billing_proto_message_CheckBalanceOwnerAccountExistsByBalanceOwnerIdAndAccountIdRequestV1ToZap(
	label string,
	in *CheckBalanceOwnerAccountExistsByBalanceOwnerIdAndAccountIdRequestV1,
) zap.Field {
	if in == nil {
		return zap.Reflect(label, in)
	}

	return zap.Dict(
		label,
		zap.Any("BalanceOwnerId", in.GetBalanceOwnerId()),
		zap.Any("AccountId", in.GetAccountId()),
	)
}

func file_inner_processing_grpc_billing_proto_message_CheckHasBalanceRequestV1ToZap(
	label string,
	in *CheckHasBalanceRequestV1,
) zap.Field {
	if in == nil {
		return zap.Reflect(label, in)
	}

	return zap.Dict(
		label,
		zap.Any("ProjectId", in.GetProjectId()),
		zap.Any("MerchantId", in.GetMerchantId()),
		zap.Any("AccountNumber", in.GetAccountNumber()),
	)
}

func file_inner_processing_grpc_billing_proto_message_CheckOutTransferBalanceRequestV1ToZap(
	label string,
	in *CheckOutTransferBalanceRequestV1,
) zap.Field {
	if in == nil {
		return zap.Reflect(label, in)
	}

	return zap.Dict(
		label,
		zap.Any("ProjectId", in.GetProjectId()),
		zap.Any("AccountNumber", in.GetAccountNumber()),
		zap.Any("Amount", in.GetAmount()),
		zap.Any("MerchantId", in.GetMerchantId()),
	)
}

func file_inner_processing_grpc_billing_proto_message_CheckOutTransferBalanceResponseV1ToZap(
	label string,
	in *CheckOutTransferBalanceResponseV1,
) zap.Field {
	if in == nil {
		return zap.Reflect(label, in)
	}

	return zap.Dict(
		label,
		zap.Any("IsSufficient", in.GetIsSufficient()),
	)
}

func file_inner_processing_grpc_billing_proto_message_CheckPayOutBalanceReqV1ToZap(
	label string,
	in *CheckPayOutBalanceReqV1,
) zap.Field {
	if in == nil {
		return zap.Reflect(label, in)
	}

	return zap.Dict(
		label,
		zap.Any("MerchantId", in.GetMerchantId()),
		zap.Any("ProjectId", in.GetProjectId()),
		zap.Any("AccountNumber", in.GetAccountNumber()),
		zap.Any("Amount", in.GetAmount()),
	)
}

func file_inner_processing_grpc_billing_proto_message_CheckPayOutBalanceResV1ToZap(
	label string,
	in *CheckPayOutBalanceResV1,
) zap.Field {
	if in == nil {
		return zap.Reflect(label, in)
	}

	return zap.Dict(
		label,
		zap.Any("HasSufficientBalance", in.GetHasSufficientBalance()),
	)
}

func file_inner_processing_grpc_billing_proto_message_GetBalanceAccountByNumberRequestToZap(
	label string,
	in *GetBalanceAccountByNumberRequest,
) zap.Field {
	if in == nil {
		return zap.Reflect(label, in)
	}

	return zap.Dict(
		label,
		zap.Any("AccountNumber", in.GetAccountNumber()),
	)
}

func file_inner_processing_grpc_billing_proto_message_GetBalanceAccountByNumberResponseToZap(
	label string,
	in *GetBalanceAccountByNumberResponse,
) zap.Field {
	if in == nil {
		return zap.Reflect(label, in)
	}

	return zap.Dict(
		label,
		zap.Any("AccountId", in.GetAccountId()),
		zap.Any("AccountNumber", in.GetAccountNumber()),
		zap.Any("StatusId", in.GetStatusId()),
	)
}

func file_inner_processing_grpc_billing_proto_message_GetBalanceByIDRequestV1ToZap(
	label string,
	in *GetBalanceByIDRequestV1,
) zap.Field {
	if in == nil {
		return zap.Reflect(label, in)
	}

	return zap.Dict(
		label,
		zap.Any("Id", in.GetId()),
	)
}

func file_inner_processing_grpc_billing_proto_message_GetBalanceOwnerByIDRequestV1ToZap(
	label string,
	in *GetBalanceOwnerByIDRequestV1,
) zap.Field {
	if in == nil {
		return zap.Reflect(label, in)
	}

	return zap.Dict(
		label,
		zap.Any("BalanceOwnerId", in.GetBalanceOwnerId()),
	)
}

func file_inner_processing_grpc_billing_proto_message_GetBalanceOwnerRequestV1ToZap(
	label string,
	in *GetBalanceOwnerRequestV1,
) zap.Field {
	if in == nil {
		return zap.Reflect(label, in)
	}

	return zap.Dict(
		label,
		zap.Any("ProjectId", in.GetProjectId()),
		zap.Any("MerchantId", in.GetMerchantId()),
	)
}

func file_inner_processing_grpc_billing_proto_message_GetBalanceOwnerResponseV1ToZap(
	label string,
	in *GetBalanceOwnerResponseV1,
) zap.Field {
	if in == nil {
		return zap.Reflect(label, in)
	}

	return zap.Dict(
		label,
		zap.Any("Id", in.GetId()),
		zap.Any("Name", in.GetName()),
		zap.Any("StatusId", in.GetStatusId()),
		zap.Any("HasCreditFirst", in.GetHasCreditFirst()),
		zap.Any("MerchantId", in.GetMerchantId()),
		zap.Any("ProjectId", in.GetProjectId()),
		zap.Any("Bin", in.GetBin()),
		zap.Any("EarnTypeId", in.GetEarnTypeId()),
		zap.Any("IsSplittable", in.GetIsSplittable()),
		zap.Any("EntityTypeId", in.GetEntityTypeId()),
		zap.Any("CountryCodeId", in.GetCountryCodeId()),
	)
}

func file_inner_processing_grpc_billing_proto_message_GetBalanceResponseV1ToZap(
	label string,
	in *GetBalanceResponseV1,
) zap.Field {
	if in == nil {
		return zap.Reflect(label, in)
	}

	return zap.Dict(
		label,
		zap.Any("Id", in.GetId()),
		zap.Any("OwnerId", in.GetOwnerId()),
		zap.Any("AccountId", in.GetAccountId()),
		zap.Any("Amount", in.GetAmount()),
		file_inner_processing_grpc_billing_proto_message_TimestampToZap("LastCalcTime", in.GetLastCalcTime()),
		zap.Any("HoldAmount", in.GetHoldAmount()),
		zap.Any("TypeId", in.GetTypeId()),
	)
}

func file_inner_processing_grpc_billing_proto_message_GetCountryCodeByIDRequestV1ToZap(
	label string,
	in *GetCountryCodeByIDRequestV1,
) zap.Field {
	if in == nil {
		return zap.Reflect(label, in)
	}

	return zap.Dict(
		label,
		zap.Any("Id", in.GetId()),
	)
}

func file_inner_processing_grpc_billing_proto_message_GetCountryCodeResponseV1ToZap(
	label string,
	in *GetCountryCodeResponseV1,
) zap.Field {
	if in == nil {
		return zap.Reflect(label, in)
	}

	return zap.Dict(
		label,
		zap.Any("Id", in.GetId()),
		zap.Any("Code", in.GetCode()),
		zap.Any("KazName", in.GetKazName()),
		zap.Any("EngName", in.GetEngName()),
		zap.Any("RuName", in.GetRuName()),
	)
}

func file_inner_processing_grpc_billing_proto_message_GetCurrentBalanceAmountByAccountAndOwnerIDRequestToZap(
	label string,
	in *GetCurrentBalanceAmountByAccountAndOwnerIDRequest,
) zap.Field {
	if in == nil {
		return zap.Reflect(label, in)
	}

	return zap.Dict(
		label,
		zap.Any("BalanceOwnerId", in.GetBalanceOwnerId()),
		zap.Any("AccountId", in.GetAccountId()),
	)
}

func file_inner_processing_grpc_billing_proto_message_GetCurrentBalanceAmountByAccountAndOwnerIDResponseToZap(
	label string,
	in *GetCurrentBalanceAmountByAccountAndOwnerIDResponse,
) zap.Field {
	if in == nil {
		return zap.Reflect(label, in)
	}

	return zap.Dict(
		label,
		zap.Any("Amount", in.GetAmount()),
	)
}

func file_inner_processing_grpc_billing_proto_message_GetEntityTypeByIDRequestV1ToZap(
	label string,
	in *GetEntityTypeByIDRequestV1,
) zap.Field {
	if in == nil {
		return zap.Reflect(label, in)
	}

	return zap.Dict(
		label,
		zap.Any("Id", in.GetId()),
	)
}

func file_inner_processing_grpc_billing_proto_message_GetEntityTypeResponseV1ToZap(
	label string,
	in *GetEntityTypeResponseV1,
) zap.Field {
	if in == nil {
		return zap.Reflect(label, in)
	}

	return zap.Dict(
		label,
		zap.Any("Id", in.GetId()),
		zap.Any("Code", in.GetCode()),
		zap.Any("Name", in.GetName()),
	)
}

func file_inner_processing_grpc_billing_proto_message_GetMerchantByBalanceOwnerRequestV1ToZap(
	label string,
	in *GetMerchantByBalanceOwnerRequestV1,
) zap.Field {
	if in == nil {
		return zap.Reflect(label, in)
	}

	return zap.Dict(
		label,
		zap.Any("BalanceOwnerId", in.GetBalanceOwnerId()),
	)
}

func file_inner_processing_grpc_billing_proto_message_GetMerchantByBalanceOwnerResponseV1ToZap(
	label string,
	in *GetMerchantByBalanceOwnerResponseV1,
) zap.Field {
	if in == nil {
		return zap.Reflect(label, in)
	}

	return zap.Dict(
		label,
		zap.Any("MerchantId", in.GetMerchantId()),
		zap.Any("ProjectId", in.GetProjectId()),
	)
}

func file_inner_processing_grpc_billing_proto_message_SetBalanceOwnerSplittableRequestV1ToZap(
	label string,
	in *SetBalanceOwnerSplittableRequestV1,
) zap.Field {
	if in == nil {
		return zap.Reflect(label, in)
	}

	return zap.Dict(
		label,
		zap.Any("BalanceOwnerId", in.GetBalanceOwnerId()),
	)
}

func file_inner_processing_grpc_billing_proto_message_SetInTransferRequestV1ToZap(
	label string,
	in *SetInTransferRequestV1,
) zap.Field {
	if in == nil {
		return zap.Reflect(label, in)
	}

	return zap.Dict(
		label,
		zap.Any("TransferId", in.GetTransferId()),
		zap.Any("MerchantId", in.GetMerchantId()),
		zap.Any("ProjectId", in.GetProjectId()),
		zap.Any("AccountNumber", in.GetAccountNumber()),
		zap.Any("Amount", in.GetAmount()),
	)
}

func file_inner_processing_grpc_billing_proto_message_SplitTransferOperationV1ToZap(
	label string,
	in *SplitTransferOperationV1,
) zap.Field {
	if in == nil {
		return zap.Reflect(label, in)
	}

	return zap.Dict(
		label,
		zap.Any("TransferId", in.GetTransferId()),
		zap.Any("SplitOperationId", in.GetSplitOperationId()),
		zap.Any("MerchantId", in.GetMerchantId()),
		zap.Any("ProjectId", in.GetProjectId()),
		zap.Any("AccountNumber", in.GetAccountNumber()),
		zap.Any("Amount", in.GetAmount()),
		zap.Any("SystemTax", in.GetSystemTax()),
		file_inner_processing_grpc_billing_proto_message_CommissionV1SliceToZap("CommissionList", in.GetCommissionList()),
	)
}

func file_inner_processing_grpc_billing_proto_message_SplitTransferOperationV1SliceToZap(
	label string,
	in []*SplitTransferOperationV1,
) zap.Field {
	var res []zap.Field
	for index, val := range in {
		res = append(res, file_inner_processing_grpc_billing_proto_message_SplitTransferOperationV1ToZap(fmt.Sprint(index), val))
	}

	return zap.Dict(label, res...)
}

func file_inner_processing_grpc_billing_proto_message_CommissionV1ToZap(
	label string,
	in *CommissionV1,
) zap.Field {
	if in == nil {
		return zap.Reflect(label, in)
	}

	return zap.Dict(
		label,
		zap.Any("TaxProjectId", in.GetTaxProjectId()),
		zap.Any("Amount", in.GetAmount()),
	)
}

func file_inner_processing_grpc_billing_proto_message_CommissionV1SliceToZap(
	label string,
	in []*CommissionV1,
) zap.Field {
	var res []zap.Field
	for index, val := range in {
		res = append(res, file_inner_processing_grpc_billing_proto_message_CommissionV1ToZap(fmt.Sprint(index), val))
	}

	return zap.Dict(label, res...)
}

var _ BillingServer = (*loggedBillingServer)(nil)

func NewLoggedBillingServer(srv BillingServer) BillingServer {
	return &loggedBillingServer{srv: srv}
}

type loggedBillingServer struct {
	UnimplementedBillingServer

	srv BillingServer
}

func (s *loggedBillingServer) CheckPayOutBalanceV1(
	ctx context.Context,
	request *CheckPayOutBalanceReqV1,
) (
	response *CheckPayOutBalanceResV1,
	err error,
) {
	label := cntx.Begin(ctx, "BillingServer_CheckPayOutBalanceV1")
	defer cntx.End(ctx, label)

	defer func() {
		if err != nil {
			ctxzap.AddFields(ctx, zap.Any(label+"error", err))
			return
		}

		ctxzap.AddFields(
			ctx,
			file_inner_processing_grpc_billing_proto_message_CheckPayOutBalanceResV1ToZap(label+"response", response),
		)
	}()

	ctxzap.AddFields(
		ctx,
		file_inner_processing_grpc_billing_proto_message_CheckPayOutBalanceReqV1ToZap(label+"request", request),
	)

	response, err = s.srv.CheckPayOutBalanceV1(ctx, request)

	return
}

func (s *loggedBillingServer) BillPayInTransactionV1(
	ctx context.Context,
	request *BillPayInTransactionRequestV1,
) (
	response *emptypb.Empty,
	err error,
) {
	label := cntx.Begin(ctx, "BillingServer_BillPayInTransactionV1")
	defer cntx.End(ctx, label)

	defer func() {
		if err != nil {
			ctxzap.AddFields(ctx, zap.Any(label+"error", err))
			return
		}

		ctxzap.AddFields(
			ctx,
			file_inner_processing_grpc_billing_proto_message_EmptyToZap(label+"response", response),
		)
	}()

	ctxzap.AddFields(
		ctx,
		file_inner_processing_grpc_billing_proto_message_BillPayInTransactionRequestV1ToZap(label+"request", request),
	)

	response, err = s.srv.BillPayInTransactionV1(ctx, request)

	return
}

func (s *loggedBillingServer) BillPayOutTransactionV1(
	ctx context.Context,
	request *BillPayOutTransactionRequestV1,
) (
	response *emptypb.Empty,
	err error,
) {
	label := cntx.Begin(ctx, "BillingServer_BillPayOutTransactionV1")
	defer cntx.End(ctx, label)

	defer func() {
		if err != nil {
			ctxzap.AddFields(ctx, zap.Any(label+"error", err))
			return
		}

		ctxzap.AddFields(
			ctx,
			file_inner_processing_grpc_billing_proto_message_EmptyToZap(label+"response", response),
		)
	}()

	ctxzap.AddFields(
		ctx,
		file_inner_processing_grpc_billing_proto_message_BillPayOutTransactionRequestV1ToZap(label+"request", request),
	)

	response, err = s.srv.BillPayOutTransactionV1(ctx, request)

	return
}

func (s *loggedBillingServer) BillRefundTransactionV1(
	ctx context.Context,
	request *BillRefundTransactionRequestV1,
) (
	response *emptypb.Empty,
	err error,
) {
	label := cntx.Begin(ctx, "BillingServer_BillRefundTransactionV1")
	defer cntx.End(ctx, label)

	defer func() {
		if err != nil {
			ctxzap.AddFields(ctx, zap.Any(label+"error", err))
			return
		}

		ctxzap.AddFields(
			ctx,
			file_inner_processing_grpc_billing_proto_message_EmptyToZap(label+"response", response),
		)
	}()

	ctxzap.AddFields(
		ctx,
		file_inner_processing_grpc_billing_proto_message_BillRefundTransactionRequestV1ToZap(label+"request", request),
	)

	response, err = s.srv.BillRefundTransactionV1(ctx, request)

	return
}

func (s *loggedBillingServer) CheckOutTransferBalanceV1(
	ctx context.Context,
	request *CheckOutTransferBalanceRequestV1,
) (
	response *CheckOutTransferBalanceResponseV1,
	err error,
) {
	label := cntx.Begin(ctx, "BillingServer_CheckOutTransferBalanceV1")
	defer cntx.End(ctx, label)

	defer func() {
		if err != nil {
			ctxzap.AddFields(ctx, zap.Any(label+"error", err))
			return
		}

		ctxzap.AddFields(
			ctx,
			file_inner_processing_grpc_billing_proto_message_CheckOutTransferBalanceResponseV1ToZap(label+"response", response),
		)
	}()

	ctxzap.AddFields(
		ctx,
		file_inner_processing_grpc_billing_proto_message_CheckOutTransferBalanceRequestV1ToZap(label+"request", request),
	)

	response, err = s.srv.CheckOutTransferBalanceV1(ctx, request)

	return
}

func (s *loggedBillingServer) BillOutTransferV1(
	ctx context.Context,
	request *BillOutTransferRequestV1,
) (
	response *emptypb.Empty,
	err error,
) {
	label := cntx.Begin(ctx, "BillingServer_BillOutTransferV1")
	defer cntx.End(ctx, label)

	defer func() {
		if err != nil {
			ctxzap.AddFields(ctx, zap.Any(label+"error", err))
			return
		}

		ctxzap.AddFields(
			ctx,
			file_inner_processing_grpc_billing_proto_message_EmptyToZap(label+"response", response),
		)
	}()

	ctxzap.AddFields(
		ctx,
		file_inner_processing_grpc_billing_proto_message_BillOutTransferRequestV1ToZap(label+"request", request),
	)

	response, err = s.srv.BillOutTransferV1(ctx, request)

	return
}

func (s *loggedBillingServer) BillInTransferV1(
	ctx context.Context,
	request *BillInTransferRequestV1,
) (
	response *emptypb.Empty,
	err error,
) {
	label := cntx.Begin(ctx, "BillingServer_BillInTransferV1")
	defer cntx.End(ctx, label)

	defer func() {
		if err != nil {
			ctxzap.AddFields(ctx, zap.Any(label+"error", err))
			return
		}

		ctxzap.AddFields(
			ctx,
			file_inner_processing_grpc_billing_proto_message_EmptyToZap(label+"response", response),
		)
	}()

	ctxzap.AddFields(
		ctx,
		file_inner_processing_grpc_billing_proto_message_BillInTransferRequestV1ToZap(label+"request", request),
	)

	response, err = s.srv.BillInTransferV1(ctx, request)

	return
}

func (s *loggedBillingServer) GetMerchantByBalanceOwnerIDV1(
	ctx context.Context,
	request *GetMerchantByBalanceOwnerRequestV1,
) (
	response *GetMerchantByBalanceOwnerResponseV1,
	err error,
) {
	label := cntx.Begin(ctx, "BillingServer_GetMerchantByBalanceOwnerIDV1")
	defer cntx.End(ctx, label)

	defer func() {
		if err != nil {
			ctxzap.AddFields(ctx, zap.Any(label+"error", err))
			return
		}

		ctxzap.AddFields(
			ctx,
			file_inner_processing_grpc_billing_proto_message_GetMerchantByBalanceOwnerResponseV1ToZap(label+"response", response),
		)
	}()

	ctxzap.AddFields(
		ctx,
		file_inner_processing_grpc_billing_proto_message_GetMerchantByBalanceOwnerRequestV1ToZap(label+"request", request),
	)

	response, err = s.srv.GetMerchantByBalanceOwnerIDV1(ctx, request)

	return
}

func (s *loggedBillingServer) SetInTransferV1(
	ctx context.Context,
	request *SetInTransferRequestV1,
) (
	response *emptypb.Empty,
	err error,
) {
	label := cntx.Begin(ctx, "BillingServer_SetInTransferV1")
	defer cntx.End(ctx, label)

	defer func() {
		if err != nil {
			ctxzap.AddFields(ctx, zap.Any(label+"error", err))
			return
		}

		ctxzap.AddFields(
			ctx,
			file_inner_processing_grpc_billing_proto_message_EmptyToZap(label+"response", response),
		)
	}()

	ctxzap.AddFields(
		ctx,
		file_inner_processing_grpc_billing_proto_message_SetInTransferRequestV1ToZap(label+"request", request),
	)

	response, err = s.srv.SetInTransferV1(ctx, request)

	return
}

func (s *loggedBillingServer) BillSplitTransferV1(
	ctx context.Context,
	request *BillSplitTransferRequestV1,
) (
	response *emptypb.Empty,
	err error,
) {
	label := cntx.Begin(ctx, "BillingServer_BillSplitTransferV1")
	defer cntx.End(ctx, label)

	defer func() {
		if err != nil {
			ctxzap.AddFields(ctx, zap.Any(label+"error", err))
			return
		}

		ctxzap.AddFields(
			ctx,
			file_inner_processing_grpc_billing_proto_message_EmptyToZap(label+"response", response),
		)
	}()

	ctxzap.AddFields(
		ctx,
		file_inner_processing_grpc_billing_proto_message_BillSplitTransferRequestV1ToZap(label+"request", request),
	)

	response, err = s.srv.BillSplitTransferV1(ctx, request)

	return
}

func (s *loggedBillingServer) GetBalanceOwnerV1(
	ctx context.Context,
	request *GetBalanceOwnerRequestV1,
) (
	response *GetBalanceOwnerResponseV1,
	err error,
) {
	label := cntx.Begin(ctx, "BillingServer_GetBalanceOwnerV1")
	defer cntx.End(ctx, label)

	defer func() {
		if err != nil {
			ctxzap.AddFields(ctx, zap.Any(label+"error", err))
			return
		}

		ctxzap.AddFields(
			ctx,
			file_inner_processing_grpc_billing_proto_message_GetBalanceOwnerResponseV1ToZap(label+"response", response),
		)
	}()

	ctxzap.AddFields(
		ctx,
		file_inner_processing_grpc_billing_proto_message_GetBalanceOwnerRequestV1ToZap(label+"request", request),
	)

	response, err = s.srv.GetBalanceOwnerV1(ctx, request)

	return
}

func (s *loggedBillingServer) SetBalanceOwnerSplittableV1(
	ctx context.Context,
	request *SetBalanceOwnerSplittableRequestV1,
) (
	response *emptypb.Empty,
	err error,
) {
	label := cntx.Begin(ctx, "BillingServer_SetBalanceOwnerSplittableV1")
	defer cntx.End(ctx, label)

	defer func() {
		if err != nil {
			ctxzap.AddFields(ctx, zap.Any(label+"error", err))
			return
		}

		ctxzap.AddFields(
			ctx,
			file_inner_processing_grpc_billing_proto_message_EmptyToZap(label+"response", response),
		)
	}()

	ctxzap.AddFields(
		ctx,
		file_inner_processing_grpc_billing_proto_message_SetBalanceOwnerSplittableRequestV1ToZap(label+"request", request),
	)

	response, err = s.srv.SetBalanceOwnerSplittableV1(ctx, request)

	return
}

func (s *loggedBillingServer) GetBalanceOwnerByIDV1(
	ctx context.Context,
	request *GetBalanceOwnerByIDRequestV1,
) (
	response *GetBalanceOwnerResponseV1,
	err error,
) {
	label := cntx.Begin(ctx, "BillingServer_GetBalanceOwnerByIDV1")
	defer cntx.End(ctx, label)

	defer func() {
		if err != nil {
			ctxzap.AddFields(ctx, zap.Any(label+"error", err))
			return
		}

		ctxzap.AddFields(
			ctx,
			file_inner_processing_grpc_billing_proto_message_GetBalanceOwnerResponseV1ToZap(label+"response", response),
		)
	}()

	ctxzap.AddFields(
		ctx,
		file_inner_processing_grpc_billing_proto_message_GetBalanceOwnerByIDRequestV1ToZap(label+"request", request),
	)

	response, err = s.srv.GetBalanceOwnerByIDV1(ctx, request)

	return
}

func (s *loggedBillingServer) GetEntityTypeByIDV1(
	ctx context.Context,
	request *GetEntityTypeByIDRequestV1,
) (
	response *GetEntityTypeResponseV1,
	err error,
) {
	label := cntx.Begin(ctx, "BillingServer_GetEntityTypeByIDV1")
	defer cntx.End(ctx, label)

	defer func() {
		if err != nil {
			ctxzap.AddFields(ctx, zap.Any(label+"error", err))
			return
		}

		ctxzap.AddFields(
			ctx,
			file_inner_processing_grpc_billing_proto_message_GetEntityTypeResponseV1ToZap(label+"response", response),
		)
	}()

	ctxzap.AddFields(
		ctx,
		file_inner_processing_grpc_billing_proto_message_GetEntityTypeByIDRequestV1ToZap(label+"request", request),
	)

	response, err = s.srv.GetEntityTypeByIDV1(ctx, request)

	return
}

func (s *loggedBillingServer) GetCountryCodeByIDV1(
	ctx context.Context,
	request *GetCountryCodeByIDRequestV1,
) (
	response *GetCountryCodeResponseV1,
	err error,
) {
	label := cntx.Begin(ctx, "BillingServer_GetCountryCodeByIDV1")
	defer cntx.End(ctx, label)

	defer func() {
		if err != nil {
			ctxzap.AddFields(ctx, zap.Any(label+"error", err))
			return
		}

		ctxzap.AddFields(
			ctx,
			file_inner_processing_grpc_billing_proto_message_GetCountryCodeResponseV1ToZap(label+"response", response),
		)
	}()

	ctxzap.AddFields(
		ctx,
		file_inner_processing_grpc_billing_proto_message_GetCountryCodeByIDRequestV1ToZap(label+"request", request),
	)

	response, err = s.srv.GetCountryCodeByIDV1(ctx, request)

	return
}

func (s *loggedBillingServer) GetBalanceByIDV1(
	ctx context.Context,
	request *GetBalanceByIDRequestV1,
) (
	response *GetBalanceResponseV1,
	err error,
) {
	label := cntx.Begin(ctx, "BillingServer_GetBalanceByIDV1")
	defer cntx.End(ctx, label)

	defer func() {
		if err != nil {
			ctxzap.AddFields(ctx, zap.Any(label+"error", err))
			return
		}

		ctxzap.AddFields(
			ctx,
			file_inner_processing_grpc_billing_proto_message_GetBalanceResponseV1ToZap(label+"response", response),
		)
	}()

	ctxzap.AddFields(
		ctx,
		file_inner_processing_grpc_billing_proto_message_GetBalanceByIDRequestV1ToZap(label+"request", request),
	)

	response, err = s.srv.GetBalanceByIDV1(ctx, request)

	return
}

func (s *loggedBillingServer) CheckHasBalanceV1(
	ctx context.Context,
	request *CheckHasBalanceRequestV1,
) (
	response *emptypb.Empty,
	err error,
) {
	label := cntx.Begin(ctx, "BillingServer_CheckHasBalanceV1")
	defer cntx.End(ctx, label)

	defer func() {
		if err != nil {
			ctxzap.AddFields(ctx, zap.Any(label+"error", err))
			return
		}

		ctxzap.AddFields(
			ctx,
			file_inner_processing_grpc_billing_proto_message_EmptyToZap(label+"response", response),
		)
	}()

	ctxzap.AddFields(
		ctx,
		file_inner_processing_grpc_billing_proto_message_CheckHasBalanceRequestV1ToZap(label+"request", request),
	)

	response, err = s.srv.CheckHasBalanceV1(ctx, request)

	return
}

func (s *loggedBillingServer) CheckBalanceOwnerAccountExistsByBalanceOwnerIdAndAccountIdV1(
	ctx context.Context,
	request *CheckBalanceOwnerAccountExistsByBalanceOwnerIdAndAccountIdRequestV1,
) (
	response *emptypb.Empty,
	err error,
) {
	label := cntx.Begin(ctx, "BillingServer_CheckBalanceOwnerAccountExistsByBalanceOwnerIdAndAccountIdV1")
	defer cntx.End(ctx, label)

	defer func() {
		if err != nil {
			ctxzap.AddFields(ctx, zap.Any(label+"error", err))
			return
		}

		ctxzap.AddFields(
			ctx,
			file_inner_processing_grpc_billing_proto_message_EmptyToZap(label+"response", response),
		)
	}()

	ctxzap.AddFields(
		ctx,
		file_inner_processing_grpc_billing_proto_message_CheckBalanceOwnerAccountExistsByBalanceOwnerIdAndAccountIdRequestV1ToZap(label+"request", request),
	)

	response, err = s.srv.CheckBalanceOwnerAccountExistsByBalanceOwnerIdAndAccountIdV1(ctx, request)

	return
}

func (s *loggedBillingServer) GetBalanceAccountByNumber(
	ctx context.Context,
	request *GetBalanceAccountByNumberRequest,
) (
	response *GetBalanceAccountByNumberResponse,
	err error,
) {
	label := cntx.Begin(ctx, "BillingServer_GetBalanceAccountByNumber")
	defer cntx.End(ctx, label)

	defer func() {
		if err != nil {
			ctxzap.AddFields(ctx, zap.Any(label+"error", err))
			return
		}

		ctxzap.AddFields(
			ctx,
			file_inner_processing_grpc_billing_proto_message_GetBalanceAccountByNumberResponseToZap(label+"response", response),
		)
	}()

	ctxzap.AddFields(
		ctx,
		file_inner_processing_grpc_billing_proto_message_GetBalanceAccountByNumberRequestToZap(label+"request", request),
	)

	response, err = s.srv.GetBalanceAccountByNumber(ctx, request)

	return
}

func (s *loggedBillingServer) GetCurrentBalanceAmountByBalanceOwnerID(
	ctx context.Context,
	request *GetCurrentBalanceAmountByAccountAndOwnerIDRequest,
) (
	response *GetCurrentBalanceAmountByAccountAndOwnerIDResponse,
	err error,
) {
	label := cntx.Begin(ctx, "BillingServer_GetCurrentBalanceAmountByBalanceOwnerID")
	defer cntx.End(ctx, label)

	defer func() {
		if err != nil {
			ctxzap.AddFields(ctx, zap.Any(label+"error", err))
			return
		}

		ctxzap.AddFields(
			ctx,
			file_inner_processing_grpc_billing_proto_message_GetCurrentBalanceAmountByAccountAndOwnerIDResponseToZap(label+"response", response),
		)
	}()

	ctxzap.AddFields(
		ctx,
		file_inner_processing_grpc_billing_proto_message_GetCurrentBalanceAmountByAccountAndOwnerIDRequestToZap(label+"request", request),
	)

	response, err = s.srv.GetCurrentBalanceAmountByBalanceOwnerID(ctx, request)

	return
}

func (s *loggedBillingServer) CheckBalanceCreditExpireDate(
	ctx context.Context,
	request *emptypb.Empty,
) (
	response *emptypb.Empty,
	err error,
) {
	label := cntx.Begin(ctx, "BillingServer_CheckBalanceCreditExpireDate")
	defer cntx.End(ctx, label)

	defer func() {
		if err != nil {
			ctxzap.AddFields(ctx, zap.Any(label+"error", err))
			return
		}

		ctxzap.AddFields(
			ctx,
			file_inner_processing_grpc_billing_proto_message_EmptyToZap(label+"response", response),
		)
	}()

	ctxzap.AddFields(
		ctx,
		file_inner_processing_grpc_billing_proto_message_EmptyToZap(label+"request", request),
	)

	response, err = s.srv.CheckBalanceCreditExpireDate(ctx, request)

	return
}

func (s *loggedBillingServer) CheckBalanceCreditStartDate(
	ctx context.Context,
	request *emptypb.Empty,
) (
	response *emptypb.Empty,
	err error,
) {
	label := cntx.Begin(ctx, "BillingServer_CheckBalanceCreditStartDate")
	defer cntx.End(ctx, label)

	defer func() {
		if err != nil {
			ctxzap.AddFields(ctx, zap.Any(label+"error", err))
			return
		}

		ctxzap.AddFields(
			ctx,
			file_inner_processing_grpc_billing_proto_message_EmptyToZap(label+"response", response),
		)
	}()

	ctxzap.AddFields(
		ctx,
		file_inner_processing_grpc_billing_proto_message_EmptyToZap(label+"request", request),
	)

	response, err = s.srv.CheckBalanceCreditStartDate(ctx, request)

	return
}

func (s *loggedBillingServer) RecalculateProvisionalBalances(
	ctx context.Context,
	request *emptypb.Empty,
) (
	response *emptypb.Empty,
	err error,
) {
	label := cntx.Begin(ctx, "BillingServer_RecalculateProvisionalBalances")
	defer cntx.End(ctx, label)

	defer func() {
		if err != nil {
			ctxzap.AddFields(ctx, zap.Any(label+"error", err))
			return
		}

		ctxzap.AddFields(
			ctx,
			file_inner_processing_grpc_billing_proto_message_EmptyToZap(label+"response", response),
		)
	}()

	ctxzap.AddFields(
		ctx,
		file_inner_processing_grpc_billing_proto_message_EmptyToZap(label+"request", request),
	)

	response, err = s.srv.RecalculateProvisionalBalances(ctx, request)

	return
}

func (s *loggedBillingServer) RecalculateFinalBalances(
	ctx context.Context,
	request *emptypb.Empty,
) (
	response *emptypb.Empty,
	err error,
) {
	label := cntx.Begin(ctx, "BillingServer_RecalculateFinalBalances")
	defer cntx.End(ctx, label)

	defer func() {
		if err != nil {
			ctxzap.AddFields(ctx, zap.Any(label+"error", err))
			return
		}

		ctxzap.AddFields(
			ctx,
			file_inner_processing_grpc_billing_proto_message_EmptyToZap(label+"response", response),
		)
	}()

	ctxzap.AddFields(
		ctx,
		file_inner_processing_grpc_billing_proto_message_EmptyToZap(label+"request", request),
	)

	response, err = s.srv.RecalculateFinalBalances(ctx, request)

	return
}

func (s *loggedBillingServer) RecalculateCreditBalances(
	ctx context.Context,
	request *emptypb.Empty,
) (
	response *emptypb.Empty,
	err error,
) {
	label := cntx.Begin(ctx, "BillingServer_RecalculateCreditBalances")
	defer cntx.End(ctx, label)

	defer func() {
		if err != nil {
			ctxzap.AddFields(ctx, zap.Any(label+"error", err))
			return
		}

		ctxzap.AddFields(
			ctx,
			file_inner_processing_grpc_billing_proto_message_EmptyToZap(label+"response", response),
		)
	}()

	ctxzap.AddFields(
		ctx,
		file_inner_processing_grpc_billing_proto_message_EmptyToZap(label+"request", request),
	)

	response, err = s.srv.RecalculateCreditBalances(ctx, request)

	return
}

var _ BillingClient = (*loggedBillingClient)(nil)

func NewLoggedBillingClient(client BillingClient) BillingClient {
	return &loggedBillingClient{client: client}
}

type loggedBillingClient struct {
	client BillingClient
}

func (s *loggedBillingClient) CheckPayOutBalanceV1(
	ctx context.Context,
	request *CheckPayOutBalanceReqV1,
	opts ...grpc.CallOption,
) (
	response *CheckPayOutBalanceResV1,
	err error,
) {
	label := cntx.Begin(ctx, "BillingClient_CheckPayOutBalanceV1")
	defer cntx.End(ctx, label)

	defer func() {
		if err != nil {
			ctxzap.AddFields(ctx, zap.Any(label+"error", err))
			return
		}

		ctxzap.AddFields(
			ctx,
			file_inner_processing_grpc_billing_proto_message_CheckPayOutBalanceResV1ToZap(label+"response", response),
		)
	}()

	ctxzap.AddFields(
		ctx,
		file_inner_processing_grpc_billing_proto_message_CheckPayOutBalanceReqV1ToZap(label+"request", request),
	)

	response, err = s.client.CheckPayOutBalanceV1(ctx, request, opts...)

	return
}

func (s *loggedBillingClient) BillPayInTransactionV1(
	ctx context.Context,
	request *BillPayInTransactionRequestV1,
	opts ...grpc.CallOption,
) (
	response *emptypb.Empty,
	err error,
) {
	label := cntx.Begin(ctx, "BillingClient_BillPayInTransactionV1")
	defer cntx.End(ctx, label)

	defer func() {
		if err != nil {
			ctxzap.AddFields(ctx, zap.Any(label+"error", err))
			return
		}

		ctxzap.AddFields(
			ctx,
			file_inner_processing_grpc_billing_proto_message_EmptyToZap(label+"response", response),
		)
	}()

	ctxzap.AddFields(
		ctx,
		file_inner_processing_grpc_billing_proto_message_BillPayInTransactionRequestV1ToZap(label+"request", request),
	)

	response, err = s.client.BillPayInTransactionV1(ctx, request, opts...)

	return
}

func (s *loggedBillingClient) BillPayOutTransactionV1(
	ctx context.Context,
	request *BillPayOutTransactionRequestV1,
	opts ...grpc.CallOption,
) (
	response *emptypb.Empty,
	err error,
) {
	label := cntx.Begin(ctx, "BillingClient_BillPayOutTransactionV1")
	defer cntx.End(ctx, label)

	defer func() {
		if err != nil {
			ctxzap.AddFields(ctx, zap.Any(label+"error", err))
			return
		}

		ctxzap.AddFields(
			ctx,
			file_inner_processing_grpc_billing_proto_message_EmptyToZap(label+"response", response),
		)
	}()

	ctxzap.AddFields(
		ctx,
		file_inner_processing_grpc_billing_proto_message_BillPayOutTransactionRequestV1ToZap(label+"request", request),
	)

	response, err = s.client.BillPayOutTransactionV1(ctx, request, opts...)

	return
}

func (s *loggedBillingClient) BillRefundTransactionV1(
	ctx context.Context,
	request *BillRefundTransactionRequestV1,
	opts ...grpc.CallOption,
) (
	response *emptypb.Empty,
	err error,
) {
	label := cntx.Begin(ctx, "BillingClient_BillRefundTransactionV1")
	defer cntx.End(ctx, label)

	defer func() {
		if err != nil {
			ctxzap.AddFields(ctx, zap.Any(label+"error", err))
			return
		}

		ctxzap.AddFields(
			ctx,
			file_inner_processing_grpc_billing_proto_message_EmptyToZap(label+"response", response),
		)
	}()

	ctxzap.AddFields(
		ctx,
		file_inner_processing_grpc_billing_proto_message_BillRefundTransactionRequestV1ToZap(label+"request", request),
	)

	response, err = s.client.BillRefundTransactionV1(ctx, request, opts...)

	return
}

func (s *loggedBillingClient) CheckOutTransferBalanceV1(
	ctx context.Context,
	request *CheckOutTransferBalanceRequestV1,
	opts ...grpc.CallOption,
) (
	response *CheckOutTransferBalanceResponseV1,
	err error,
) {
	label := cntx.Begin(ctx, "BillingClient_CheckOutTransferBalanceV1")
	defer cntx.End(ctx, label)

	defer func() {
		if err != nil {
			ctxzap.AddFields(ctx, zap.Any(label+"error", err))
			return
		}

		ctxzap.AddFields(
			ctx,
			file_inner_processing_grpc_billing_proto_message_CheckOutTransferBalanceResponseV1ToZap(label+"response", response),
		)
	}()

	ctxzap.AddFields(
		ctx,
		file_inner_processing_grpc_billing_proto_message_CheckOutTransferBalanceRequestV1ToZap(label+"request", request),
	)

	response, err = s.client.CheckOutTransferBalanceV1(ctx, request, opts...)

	return
}

func (s *loggedBillingClient) BillOutTransferV1(
	ctx context.Context,
	request *BillOutTransferRequestV1,
	opts ...grpc.CallOption,
) (
	response *emptypb.Empty,
	err error,
) {
	label := cntx.Begin(ctx, "BillingClient_BillOutTransferV1")
	defer cntx.End(ctx, label)

	defer func() {
		if err != nil {
			ctxzap.AddFields(ctx, zap.Any(label+"error", err))
			return
		}

		ctxzap.AddFields(
			ctx,
			file_inner_processing_grpc_billing_proto_message_EmptyToZap(label+"response", response),
		)
	}()

	ctxzap.AddFields(
		ctx,
		file_inner_processing_grpc_billing_proto_message_BillOutTransferRequestV1ToZap(label+"request", request),
	)

	response, err = s.client.BillOutTransferV1(ctx, request, opts...)

	return
}

func (s *loggedBillingClient) BillInTransferV1(
	ctx context.Context,
	request *BillInTransferRequestV1,
	opts ...grpc.CallOption,
) (
	response *emptypb.Empty,
	err error,
) {
	label := cntx.Begin(ctx, "BillingClient_BillInTransferV1")
	defer cntx.End(ctx, label)

	defer func() {
		if err != nil {
			ctxzap.AddFields(ctx, zap.Any(label+"error", err))
			return
		}

		ctxzap.AddFields(
			ctx,
			file_inner_processing_grpc_billing_proto_message_EmptyToZap(label+"response", response),
		)
	}()

	ctxzap.AddFields(
		ctx,
		file_inner_processing_grpc_billing_proto_message_BillInTransferRequestV1ToZap(label+"request", request),
	)

	response, err = s.client.BillInTransferV1(ctx, request, opts...)

	return
}

func (s *loggedBillingClient) GetMerchantByBalanceOwnerIDV1(
	ctx context.Context,
	request *GetMerchantByBalanceOwnerRequestV1,
	opts ...grpc.CallOption,
) (
	response *GetMerchantByBalanceOwnerResponseV1,
	err error,
) {
	label := cntx.Begin(ctx, "BillingClient_GetMerchantByBalanceOwnerIDV1")
	defer cntx.End(ctx, label)

	defer func() {
		if err != nil {
			ctxzap.AddFields(ctx, zap.Any(label+"error", err))
			return
		}

		ctxzap.AddFields(
			ctx,
			file_inner_processing_grpc_billing_proto_message_GetMerchantByBalanceOwnerResponseV1ToZap(label+"response", response),
		)
	}()

	ctxzap.AddFields(
		ctx,
		file_inner_processing_grpc_billing_proto_message_GetMerchantByBalanceOwnerRequestV1ToZap(label+"request", request),
	)

	response, err = s.client.GetMerchantByBalanceOwnerIDV1(ctx, request, opts...)

	return
}

func (s *loggedBillingClient) SetInTransferV1(
	ctx context.Context,
	request *SetInTransferRequestV1,
	opts ...grpc.CallOption,
) (
	response *emptypb.Empty,
	err error,
) {
	label := cntx.Begin(ctx, "BillingClient_SetInTransferV1")
	defer cntx.End(ctx, label)

	defer func() {
		if err != nil {
			ctxzap.AddFields(ctx, zap.Any(label+"error", err))
			return
		}

		ctxzap.AddFields(
			ctx,
			file_inner_processing_grpc_billing_proto_message_EmptyToZap(label+"response", response),
		)
	}()

	ctxzap.AddFields(
		ctx,
		file_inner_processing_grpc_billing_proto_message_SetInTransferRequestV1ToZap(label+"request", request),
	)

	response, err = s.client.SetInTransferV1(ctx, request, opts...)

	return
}

func (s *loggedBillingClient) BillSplitTransferV1(
	ctx context.Context,
	request *BillSplitTransferRequestV1,
	opts ...grpc.CallOption,
) (
	response *emptypb.Empty,
	err error,
) {
	label := cntx.Begin(ctx, "BillingClient_BillSplitTransferV1")
	defer cntx.End(ctx, label)

	defer func() {
		if err != nil {
			ctxzap.AddFields(ctx, zap.Any(label+"error", err))
			return
		}

		ctxzap.AddFields(
			ctx,
			file_inner_processing_grpc_billing_proto_message_EmptyToZap(label+"response", response),
		)
	}()

	ctxzap.AddFields(
		ctx,
		file_inner_processing_grpc_billing_proto_message_BillSplitTransferRequestV1ToZap(label+"request", request),
	)

	response, err = s.client.BillSplitTransferV1(ctx, request, opts...)

	return
}

func (s *loggedBillingClient) GetBalanceOwnerV1(
	ctx context.Context,
	request *GetBalanceOwnerRequestV1,
	opts ...grpc.CallOption,
) (
	response *GetBalanceOwnerResponseV1,
	err error,
) {
	label := cntx.Begin(ctx, "BillingClient_GetBalanceOwnerV1")
	defer cntx.End(ctx, label)

	defer func() {
		if err != nil {
			ctxzap.AddFields(ctx, zap.Any(label+"error", err))
			return
		}

		ctxzap.AddFields(
			ctx,
			file_inner_processing_grpc_billing_proto_message_GetBalanceOwnerResponseV1ToZap(label+"response", response),
		)
	}()

	ctxzap.AddFields(
		ctx,
		file_inner_processing_grpc_billing_proto_message_GetBalanceOwnerRequestV1ToZap(label+"request", request),
	)

	response, err = s.client.GetBalanceOwnerV1(ctx, request, opts...)

	return
}

func (s *loggedBillingClient) SetBalanceOwnerSplittableV1(
	ctx context.Context,
	request *SetBalanceOwnerSplittableRequestV1,
	opts ...grpc.CallOption,
) (
	response *emptypb.Empty,
	err error,
) {
	label := cntx.Begin(ctx, "BillingClient_SetBalanceOwnerSplittableV1")
	defer cntx.End(ctx, label)

	defer func() {
		if err != nil {
			ctxzap.AddFields(ctx, zap.Any(label+"error", err))
			return
		}

		ctxzap.AddFields(
			ctx,
			file_inner_processing_grpc_billing_proto_message_EmptyToZap(label+"response", response),
		)
	}()

	ctxzap.AddFields(
		ctx,
		file_inner_processing_grpc_billing_proto_message_SetBalanceOwnerSplittableRequestV1ToZap(label+"request", request),
	)

	response, err = s.client.SetBalanceOwnerSplittableV1(ctx, request, opts...)

	return
}

func (s *loggedBillingClient) GetBalanceOwnerByIDV1(
	ctx context.Context,
	request *GetBalanceOwnerByIDRequestV1,
	opts ...grpc.CallOption,
) (
	response *GetBalanceOwnerResponseV1,
	err error,
) {
	label := cntx.Begin(ctx, "BillingClient_GetBalanceOwnerByIDV1")
	defer cntx.End(ctx, label)

	defer func() {
		if err != nil {
			ctxzap.AddFields(ctx, zap.Any(label+"error", err))
			return
		}

		ctxzap.AddFields(
			ctx,
			file_inner_processing_grpc_billing_proto_message_GetBalanceOwnerResponseV1ToZap(label+"response", response),
		)
	}()

	ctxzap.AddFields(
		ctx,
		file_inner_processing_grpc_billing_proto_message_GetBalanceOwnerByIDRequestV1ToZap(label+"request", request),
	)

	response, err = s.client.GetBalanceOwnerByIDV1(ctx, request, opts...)

	return
}

func (s *loggedBillingClient) GetEntityTypeByIDV1(
	ctx context.Context,
	request *GetEntityTypeByIDRequestV1,
	opts ...grpc.CallOption,
) (
	response *GetEntityTypeResponseV1,
	err error,
) {
	label := cntx.Begin(ctx, "BillingClient_GetEntityTypeByIDV1")
	defer cntx.End(ctx, label)

	defer func() {
		if err != nil {
			ctxzap.AddFields(ctx, zap.Any(label+"error", err))
			return
		}

		ctxzap.AddFields(
			ctx,
			file_inner_processing_grpc_billing_proto_message_GetEntityTypeResponseV1ToZap(label+"response", response),
		)
	}()

	ctxzap.AddFields(
		ctx,
		file_inner_processing_grpc_billing_proto_message_GetEntityTypeByIDRequestV1ToZap(label+"request", request),
	)

	response, err = s.client.GetEntityTypeByIDV1(ctx, request, opts...)

	return
}

func (s *loggedBillingClient) GetCountryCodeByIDV1(
	ctx context.Context,
	request *GetCountryCodeByIDRequestV1,
	opts ...grpc.CallOption,
) (
	response *GetCountryCodeResponseV1,
	err error,
) {
	label := cntx.Begin(ctx, "BillingClient_GetCountryCodeByIDV1")
	defer cntx.End(ctx, label)

	defer func() {
		if err != nil {
			ctxzap.AddFields(ctx, zap.Any(label+"error", err))
			return
		}

		ctxzap.AddFields(
			ctx,
			file_inner_processing_grpc_billing_proto_message_GetCountryCodeResponseV1ToZap(label+"response", response),
		)
	}()

	ctxzap.AddFields(
		ctx,
		file_inner_processing_grpc_billing_proto_message_GetCountryCodeByIDRequestV1ToZap(label+"request", request),
	)

	response, err = s.client.GetCountryCodeByIDV1(ctx, request, opts...)

	return
}

func (s *loggedBillingClient) GetBalanceByIDV1(
	ctx context.Context,
	request *GetBalanceByIDRequestV1,
	opts ...grpc.CallOption,
) (
	response *GetBalanceResponseV1,
	err error,
) {
	label := cntx.Begin(ctx, "BillingClient_GetBalanceByIDV1")
	defer cntx.End(ctx, label)

	defer func() {
		if err != nil {
			ctxzap.AddFields(ctx, zap.Any(label+"error", err))
			return
		}

		ctxzap.AddFields(
			ctx,
			file_inner_processing_grpc_billing_proto_message_GetBalanceResponseV1ToZap(label+"response", response),
		)
	}()

	ctxzap.AddFields(
		ctx,
		file_inner_processing_grpc_billing_proto_message_GetBalanceByIDRequestV1ToZap(label+"request", request),
	)

	response, err = s.client.GetBalanceByIDV1(ctx, request, opts...)

	return
}

func (s *loggedBillingClient) CheckHasBalanceV1(
	ctx context.Context,
	request *CheckHasBalanceRequestV1,
	opts ...grpc.CallOption,
) (
	response *emptypb.Empty,
	err error,
) {
	label := cntx.Begin(ctx, "BillingClient_CheckHasBalanceV1")
	defer cntx.End(ctx, label)

	defer func() {
		if err != nil {
			ctxzap.AddFields(ctx, zap.Any(label+"error", err))
			return
		}

		ctxzap.AddFields(
			ctx,
			file_inner_processing_grpc_billing_proto_message_EmptyToZap(label+"response", response),
		)
	}()

	ctxzap.AddFields(
		ctx,
		file_inner_processing_grpc_billing_proto_message_CheckHasBalanceRequestV1ToZap(label+"request", request),
	)

	response, err = s.client.CheckHasBalanceV1(ctx, request, opts...)

	return
}

func (s *loggedBillingClient) CheckBalanceOwnerAccountExistsByBalanceOwnerIdAndAccountIdV1(
	ctx context.Context,
	request *CheckBalanceOwnerAccountExistsByBalanceOwnerIdAndAccountIdRequestV1,
	opts ...grpc.CallOption,
) (
	response *emptypb.Empty,
	err error,
) {
	label := cntx.Begin(ctx, "BillingClient_CheckBalanceOwnerAccountExistsByBalanceOwnerIdAndAccountIdV1")
	defer cntx.End(ctx, label)

	defer func() {
		if err != nil {
			ctxzap.AddFields(ctx, zap.Any(label+"error", err))
			return
		}

		ctxzap.AddFields(
			ctx,
			file_inner_processing_grpc_billing_proto_message_EmptyToZap(label+"response", response),
		)
	}()

	ctxzap.AddFields(
		ctx,
		file_inner_processing_grpc_billing_proto_message_CheckBalanceOwnerAccountExistsByBalanceOwnerIdAndAccountIdRequestV1ToZap(label+"request", request),
	)

	response, err = s.client.CheckBalanceOwnerAccountExistsByBalanceOwnerIdAndAccountIdV1(ctx, request, opts...)

	return
}

func (s *loggedBillingClient) GetBalanceAccountByNumber(
	ctx context.Context,
	request *GetBalanceAccountByNumberRequest,
	opts ...grpc.CallOption,
) (
	response *GetBalanceAccountByNumberResponse,
	err error,
) {
	label := cntx.Begin(ctx, "BillingClient_GetBalanceAccountByNumber")
	defer cntx.End(ctx, label)

	defer func() {
		if err != nil {
			ctxzap.AddFields(ctx, zap.Any(label+"error", err))
			return
		}

		ctxzap.AddFields(
			ctx,
			file_inner_processing_grpc_billing_proto_message_GetBalanceAccountByNumberResponseToZap(label+"response", response),
		)
	}()

	ctxzap.AddFields(
		ctx,
		file_inner_processing_grpc_billing_proto_message_GetBalanceAccountByNumberRequestToZap(label+"request", request),
	)

	response, err = s.client.GetBalanceAccountByNumber(ctx, request, opts...)

	return
}

func (s *loggedBillingClient) GetCurrentBalanceAmountByBalanceOwnerID(
	ctx context.Context,
	request *GetCurrentBalanceAmountByAccountAndOwnerIDRequest,
	opts ...grpc.CallOption,
) (
	response *GetCurrentBalanceAmountByAccountAndOwnerIDResponse,
	err error,
) {
	label := cntx.Begin(ctx, "BillingClient_GetCurrentBalanceAmountByBalanceOwnerID")
	defer cntx.End(ctx, label)

	defer func() {
		if err != nil {
			ctxzap.AddFields(ctx, zap.Any(label+"error", err))
			return
		}

		ctxzap.AddFields(
			ctx,
			file_inner_processing_grpc_billing_proto_message_GetCurrentBalanceAmountByAccountAndOwnerIDResponseToZap(label+"response", response),
		)
	}()

	ctxzap.AddFields(
		ctx,
		file_inner_processing_grpc_billing_proto_message_GetCurrentBalanceAmountByAccountAndOwnerIDRequestToZap(label+"request", request),
	)

	response, err = s.client.GetCurrentBalanceAmountByBalanceOwnerID(ctx, request, opts...)

	return
}

func (s *loggedBillingClient) CheckBalanceCreditExpireDate(
	ctx context.Context,
	request *emptypb.Empty,
	opts ...grpc.CallOption,
) (
	response *emptypb.Empty,
	err error,
) {
	label := cntx.Begin(ctx, "BillingClient_CheckBalanceCreditExpireDate")
	defer cntx.End(ctx, label)

	defer func() {
		if err != nil {
			ctxzap.AddFields(ctx, zap.Any(label+"error", err))
			return
		}

		ctxzap.AddFields(
			ctx,
			file_inner_processing_grpc_billing_proto_message_EmptyToZap(label+"response", response),
		)
	}()

	ctxzap.AddFields(
		ctx,
		file_inner_processing_grpc_billing_proto_message_EmptyToZap(label+"request", request),
	)

	response, err = s.client.CheckBalanceCreditExpireDate(ctx, request, opts...)

	return
}

func (s *loggedBillingClient) CheckBalanceCreditStartDate(
	ctx context.Context,
	request *emptypb.Empty,
	opts ...grpc.CallOption,
) (
	response *emptypb.Empty,
	err error,
) {
	label := cntx.Begin(ctx, "BillingClient_CheckBalanceCreditStartDate")
	defer cntx.End(ctx, label)

	defer func() {
		if err != nil {
			ctxzap.AddFields(ctx, zap.Any(label+"error", err))
			return
		}

		ctxzap.AddFields(
			ctx,
			file_inner_processing_grpc_billing_proto_message_EmptyToZap(label+"response", response),
		)
	}()

	ctxzap.AddFields(
		ctx,
		file_inner_processing_grpc_billing_proto_message_EmptyToZap(label+"request", request),
	)

	response, err = s.client.CheckBalanceCreditStartDate(ctx, request, opts...)

	return
}

func (s *loggedBillingClient) RecalculateProvisionalBalances(
	ctx context.Context,
	request *emptypb.Empty,
	opts ...grpc.CallOption,
) (
	response *emptypb.Empty,
	err error,
) {
	label := cntx.Begin(ctx, "BillingClient_RecalculateProvisionalBalances")
	defer cntx.End(ctx, label)

	defer func() {
		if err != nil {
			ctxzap.AddFields(ctx, zap.Any(label+"error", err))
			return
		}

		ctxzap.AddFields(
			ctx,
			file_inner_processing_grpc_billing_proto_message_EmptyToZap(label+"response", response),
		)
	}()

	ctxzap.AddFields(
		ctx,
		file_inner_processing_grpc_billing_proto_message_EmptyToZap(label+"request", request),
	)

	response, err = s.client.RecalculateProvisionalBalances(ctx, request, opts...)

	return
}

func (s *loggedBillingClient) RecalculateFinalBalances(
	ctx context.Context,
	request *emptypb.Empty,
	opts ...grpc.CallOption,
) (
	response *emptypb.Empty,
	err error,
) {
	label := cntx.Begin(ctx, "BillingClient_RecalculateFinalBalances")
	defer cntx.End(ctx, label)

	defer func() {
		if err != nil {
			ctxzap.AddFields(ctx, zap.Any(label+"error", err))
			return
		}

		ctxzap.AddFields(
			ctx,
			file_inner_processing_grpc_billing_proto_message_EmptyToZap(label+"response", response),
		)
	}()

	ctxzap.AddFields(
		ctx,
		file_inner_processing_grpc_billing_proto_message_EmptyToZap(label+"request", request),
	)

	response, err = s.client.RecalculateFinalBalances(ctx, request, opts...)

	return
}

func (s *loggedBillingClient) RecalculateCreditBalances(
	ctx context.Context,
	request *emptypb.Empty,
	opts ...grpc.CallOption,
) (
	response *emptypb.Empty,
	err error,
) {
	label := cntx.Begin(ctx, "BillingClient_RecalculateCreditBalances")
	defer cntx.End(ctx, label)

	defer func() {
		if err != nil {
			ctxzap.AddFields(ctx, zap.Any(label+"error", err))
			return
		}

		ctxzap.AddFields(
			ctx,
			file_inner_processing_grpc_billing_proto_message_EmptyToZap(label+"response", response),
		)
	}()

	ctxzap.AddFields(
		ctx,
		file_inner_processing_grpc_billing_proto_message_EmptyToZap(label+"request", request),
	)

	response, err = s.client.RecalculateCreditBalances(ctx, request, opts...)

	return
}
