// Code generated by protoc-gen-go-mvp. DO NOT EDIT.
// versions:
// - protoc-gen-go-mvp v2.0.1
// created by mvp authors

package grpc

import (
	math "math"
	strings "strings"
)

func (val EnumTransactionStatus) Synonym() EnumTransactionStatus {
	if _, ok := EnumTransactionStatus_name[int32(val)]; ok {
		return val
	}

	return EnumTransactionStatus(math.MinInt32)
}

func (val EnumTransactionStatus) Int() int {
	return int(val.Synonym())
}

func (val EnumTransactionStatus) IntPtr() *int {
	result := int(val.Synonym())
	return &result
}

func (val EnumTransactionStatus) Int32() int32 {
	return int32(val.Synonym())
}

func (val EnumTransactionStatus) Int32Ptr() *int32 {
	result := int32(val.Synonym())
	return &result
}

func (val EnumTransactionStatus) Int64() int64 {
	return int64(val.Synonym())
}

func (val EnumTransactionStatus) Int64Ptr() *int64 {
	result := int64(val.Synonym())
	return &result
}

func (val EnumTransactionStatus) Uint() uint {
	return uint(val.Synonym())
}

func (val EnumTransactionStatus) UintPtr() *uint {
	result := uint(val.Synonym())
	return &result
}

func (val EnumTransactionStatus) Uint32() uint32 {
	return uint32(val.Synonym())
}

func (val EnumTransactionStatus) Uint32Ptr() *uint32 {
	result := uint32(val.Synonym())
	return &result
}

func (val EnumTransactionStatus) Uint64() uint64 {
	return uint64(val.Synonym())
}

func (val EnumTransactionStatus) Uint64Ptr() *uint64 {
	result := uint64(val.Synonym())
	return &result
}

func (val EnumTransactionStatus) IsKnown() bool {
	return val.Synonym() != EnumTransactionStatus(math.MinInt32)
}

func ConvertIntToEnumTransactionStatus(in int) EnumTransactionStatus {
	return EnumTransactionStatus(in).Synonym()
}

func ConvertUintToEnumTransactionStatus(in uint) EnumTransactionStatus {
	return EnumTransactionStatus(in).Synonym()
}

func ConvertInt32ToEnumTransactionStatus(in int32) EnumTransactionStatus {
	return EnumTransactionStatus(in).Synonym()
}

func ConvertUint32ToEnumTransactionStatus(in uint32) EnumTransactionStatus {
	return EnumTransactionStatus(in).Synonym()
}

func ConvertInt64ToEnumTransactionStatus(in int64) EnumTransactionStatus {
	return EnumTransactionStatus(in).Synonym()
}

func ConvertUint64ToEnumTransactionStatus(in uint64) EnumTransactionStatus {
	return EnumTransactionStatus(in).Synonym()
}

var EnumTransactionStatus_Lower_value = map[string]EnumTransactionStatus{
	"unknown":                          0,
	"transactionstatusnew":             2,
	"transactionstatusthreedswaiting":  3,
	"transactionstatusprocessed":       4,
	"transactionstatusfailed":          5,
	"transactionstatusrefund":          6,
	"transactionstatuscanceled":        7,
	"transactionstatusretry":           8,
	"transactionstatussuccess":         9,
	"transactionstatusthreedsreceived": 10,
	"transactionstatusholded":          11,
	"transactionstatusrefundwaiting":   13,
	"transactionstatusauthorized":      14,
	"transactionstatuserror":           15,
	"transactionstatusfingerprint":     16,
}

func ConvertStringToEnumTransactionStatus(in string) EnumTransactionStatus {
	if result, ok := EnumTransactionStatus_value[in]; ok {
		return EnumTransactionStatus(result)
	}

	if result, ok := EnumTransactionStatus_Lower_value[strings.ToLower(in)]; ok {
		return EnumTransactionStatus(result)
	}

	return EnumTransactionStatus(math.MinInt32)
}

var SliceEnumTransactionStatusConvert *sliceEnumTransactionStatusConvert

type sliceEnumTransactionStatusConvert struct{}

func (*sliceEnumTransactionStatusConvert) Synonym(in []EnumTransactionStatus) []EnumTransactionStatus {
	result := make([]EnumTransactionStatus, len(in))
	for i, v := range in {
		result[i] = v.Synonym()
	}

	return result
}

func (*sliceEnumTransactionStatusConvert) Int32(in []EnumTransactionStatus) []int32 {
	result := make([]int32, len(in))
	for i, v := range in {
		result[i] = v.Int32()
	}

	return result
}

func (*sliceEnumTransactionStatusConvert) Uint32(in []EnumTransactionStatus) []uint32 {
	result := make([]uint32, len(in))
	for i, v := range in {
		result[i] = v.Uint32()
	}

	return result
}

func (*sliceEnumTransactionStatusConvert) Uint64(in []EnumTransactionStatus) []uint64 {
	result := make([]uint64, len(in))
	for i, v := range in {
		result[i] = v.Uint64()
	}

	return result
}

func (*sliceEnumTransactionStatusConvert) Int64(in []EnumTransactionStatus) []int64 {
	result := make([]int64, len(in))
	for i, v := range in {
		result[i] = v.Int64()
	}

	return result
}

func (*sliceEnumTransactionStatusConvert) Uint(in []EnumTransactionStatus) []uint {
	result := make([]uint, len(in))
	for i, v := range in {
		result[i] = v.Uint()
	}

	return result
}

func (*sliceEnumTransactionStatusConvert) Int(in []EnumTransactionStatus) []int {
	result := make([]int, len(in))
	for i, v := range in {
		result[i] = v.Int()
	}

	return result
}

func (*sliceEnumTransactionStatusConvert) IsKnown(in []EnumTransactionStatus) []bool {
	result := make([]bool, len(in))
	for i, v := range in {
		result[i] = v.IsKnown()
	}

	return result
}

func (*sliceEnumTransactionStatusConvert) ConvertIntToEnumTransactionStatus(in []int) []EnumTransactionStatus {
	result := make([]EnumTransactionStatus, len(in))
	for i, v := range in {
		result[i] = ConvertIntToEnumTransactionStatus(v)
	}

	return result
}

func (*sliceEnumTransactionStatusConvert) ConvertUintToEnumTransactionStatus(in []uint) []EnumTransactionStatus {
	result := make([]EnumTransactionStatus, len(in))
	for i, v := range in {
		result[i] = ConvertUintToEnumTransactionStatus(v)
	}

	return result
}

func (*sliceEnumTransactionStatusConvert) ConvertInt32ToEnumTransactionStatus(in []int32) []EnumTransactionStatus {
	result := make([]EnumTransactionStatus, len(in))
	for i, v := range in {
		result[i] = ConvertInt32ToEnumTransactionStatus(v)
	}

	return result
}

func (*sliceEnumTransactionStatusConvert) ConvertUint32ToEnumTransactionStatus(in []uint32) []EnumTransactionStatus {
	result := make([]EnumTransactionStatus, len(in))
	for i, v := range in {
		result[i] = ConvertUint32ToEnumTransactionStatus(v)
	}

	return result
}

func (*sliceEnumTransactionStatusConvert) ConvertInt64ToEnumTransactionStatus(in []int64) []EnumTransactionStatus {
	result := make([]EnumTransactionStatus, len(in))
	for i, v := range in {
		result[i] = ConvertInt64ToEnumTransactionStatus(v)
	}

	return result
}

func (*sliceEnumTransactionStatusConvert) ConvertUint64ToEnumTransactionStatus(in []uint64) []EnumTransactionStatus {
	result := make([]EnumTransactionStatus, len(in))
	for i, v := range in {
		result[i] = ConvertUint64ToEnumTransactionStatus(v)
	}

	return result
}

func (*sliceEnumTransactionStatusConvert) ConvertStringToEnumTransactionStatus(in []string) []EnumTransactionStatus {
	result := make([]EnumTransactionStatus, len(in))
	for i, v := range in {
		result[i] = ConvertStringToEnumTransactionStatus(v)
	}

	return result
}

func NewEnumTransactionStatusUsage() *EnumTransactionStatusUsage {
	return &EnumTransactionStatusUsage{
		enumMap: map[EnumTransactionStatus]bool{
			EnumTransactionStatus_Unknown:                          false,
			EnumTransactionStatus_TransactionStatusNew:             false,
			EnumTransactionStatus_TransactionStatusThreeDSWaiting:  false,
			EnumTransactionStatus_TransactionStatusProcessed:       false,
			EnumTransactionStatus_TransactionStatusFailed:          false,
			EnumTransactionStatus_TransactionStatusRefund:          false,
			EnumTransactionStatus_TransactionStatusCanceled:        false,
			EnumTransactionStatus_TransactionStatusRetry:           false,
			EnumTransactionStatus_TransactionStatusSuccess:         false,
			EnumTransactionStatus_TransactionStatusThreeDSReceived: false,
			EnumTransactionStatus_TransactionStatusHolded:          false,
			EnumTransactionStatus_TransactionStatusRefundWaiting:   false,
			EnumTransactionStatus_TransactionStatusAuthorized:      false,
			EnumTransactionStatus_TransactionStatusError:           false,
			EnumTransactionStatus_TransactionStatusFingerPrint:     false,
		},
	}
}

func IsEnumTransactionStatus(target EnumTransactionStatus, matches ...EnumTransactionStatus) bool {
	for _, v := range matches {
		if target == v {
			return true
		}
	}

	return false
}

type EnumTransactionStatusUsage struct {
	enumMap map[EnumTransactionStatus]bool
}

func (u *EnumTransactionStatusUsage) Use(slice ...EnumTransactionStatus) *EnumTransactionStatusUsage {
	for _, in := range slice {
		u.enumMap[in] = true
	}

	return u
}

func (u *EnumTransactionStatusUsage) Check() []string {
	var result []string
	for k, v := range u.enumMap {
		if v {
			continue
		}

		result = append(result, k.String())
	}

	return result
}

func (u *EnumTransactionStatusUsage) EnumTransactionStatus_Unknown() EnumTransactionStatus {
	u.Use(EnumTransactionStatus_Unknown)
	return EnumTransactionStatus_Unknown
}

func (u *EnumTransactionStatusUsage) EnumTransactionStatus_TransactionStatusNew() EnumTransactionStatus {
	u.Use(EnumTransactionStatus_TransactionStatusNew)
	return EnumTransactionStatus_TransactionStatusNew
}

func (u *EnumTransactionStatusUsage) EnumTransactionStatus_TransactionStatusThreeDSWaiting() EnumTransactionStatus {
	u.Use(EnumTransactionStatus_TransactionStatusThreeDSWaiting)
	return EnumTransactionStatus_TransactionStatusThreeDSWaiting
}

func (u *EnumTransactionStatusUsage) EnumTransactionStatus_TransactionStatusProcessed() EnumTransactionStatus {
	u.Use(EnumTransactionStatus_TransactionStatusProcessed)
	return EnumTransactionStatus_TransactionStatusProcessed
}

func (u *EnumTransactionStatusUsage) EnumTransactionStatus_TransactionStatusFailed() EnumTransactionStatus {
	u.Use(EnumTransactionStatus_TransactionStatusFailed)
	return EnumTransactionStatus_TransactionStatusFailed
}

func (u *EnumTransactionStatusUsage) EnumTransactionStatus_TransactionStatusRefund() EnumTransactionStatus {
	u.Use(EnumTransactionStatus_TransactionStatusRefund)
	return EnumTransactionStatus_TransactionStatusRefund
}

func (u *EnumTransactionStatusUsage) EnumTransactionStatus_TransactionStatusCanceled() EnumTransactionStatus {
	u.Use(EnumTransactionStatus_TransactionStatusCanceled)
	return EnumTransactionStatus_TransactionStatusCanceled
}

func (u *EnumTransactionStatusUsage) EnumTransactionStatus_TransactionStatusRetry() EnumTransactionStatus {
	u.Use(EnumTransactionStatus_TransactionStatusRetry)
	return EnumTransactionStatus_TransactionStatusRetry
}

func (u *EnumTransactionStatusUsage) EnumTransactionStatus_TransactionStatusSuccess() EnumTransactionStatus {
	u.Use(EnumTransactionStatus_TransactionStatusSuccess)
	return EnumTransactionStatus_TransactionStatusSuccess
}

func (u *EnumTransactionStatusUsage) EnumTransactionStatus_TransactionStatusThreeDSReceived() EnumTransactionStatus {
	u.Use(EnumTransactionStatus_TransactionStatusThreeDSReceived)
	return EnumTransactionStatus_TransactionStatusThreeDSReceived
}

func (u *EnumTransactionStatusUsage) EnumTransactionStatus_TransactionStatusHolded() EnumTransactionStatus {
	u.Use(EnumTransactionStatus_TransactionStatusHolded)
	return EnumTransactionStatus_TransactionStatusHolded
}

func (u *EnumTransactionStatusUsage) EnumTransactionStatus_TransactionStatusRefundWaiting() EnumTransactionStatus {
	u.Use(EnumTransactionStatus_TransactionStatusRefundWaiting)
	return EnumTransactionStatus_TransactionStatusRefundWaiting
}

func (u *EnumTransactionStatusUsage) EnumTransactionStatus_TransactionStatusAuthorized() EnumTransactionStatus {
	u.Use(EnumTransactionStatus_TransactionStatusAuthorized)
	return EnumTransactionStatus_TransactionStatusAuthorized
}

func (u *EnumTransactionStatusUsage) EnumTransactionStatus_TransactionStatusError() EnumTransactionStatus {
	u.Use(EnumTransactionStatus_TransactionStatusError)
	return EnumTransactionStatus_TransactionStatusError
}

func (u *EnumTransactionStatusUsage) EnumTransactionStatus_TransactionStatusFingerPrint() EnumTransactionStatus {
	u.Use(EnumTransactionStatus_TransactionStatusFingerPrint)
	return EnumTransactionStatus_TransactionStatusFingerPrint
}
