package test

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"testing"

	"github.com/stretchr/testify/assert"

	"git.local/sensitive/local/internal/domain"
)

func TestCardLink(t *testing.T) {
	t.Skip()

	testData := []struct {
		name         string
		req          *domain.VCardLinkRequest
		expectedResp map[string]interface{}
		expectedCode int
	}{
		{
			name: "success_response",
			req: &domain.VCardLinkRequest{
				Description:        "card link test",
				FailureRedirectUrl: "https://www.youtube.com",
				MerchantId:         devMerchantID,
				ProjectClientID:    devProjectClientID,
				ProjectId:          devProjectID,
				SuccessRedirectUrl: "https://www.youtube.com",
				CallbackUrl:        devCallbackUrl,
			},
			expectedResp: map[string]interface{}{
				"status":  true,
				"message": "Success",
			},
			expectedCode: http.StatusOK,
		},
		{
			name: "validation_error",
			expectedResp: map[string]interface{}{
				"status":      false,
				"status_code": float64(1021),
				"message":     "request validation error: EOF",
				"result": map[string]interface{}{
					"error": "EOF",
				},
			},
			expectedCode: http.StatusBadRequest,
		},
	}

	for _, tt := range testData {
		t.Run(tt.name, func(t *testing.T) {
			CardLink(t, tt.req, tt.expectedResp, tt.expectedCode)
		})
	}
}

func CardLink(t *testing.T, req *domain.VCardLinkRequest, expectedResp map[string]interface{}, expectedCode int) {
	var body []byte
	var err error

	if req != nil {
		body, err = json.Marshal(req)
		if err != nil {
			t.Fatal(err)
		}
	}

	request, err := http.NewRequest(http.MethodPost, domainURL+PrimalCardLinkUrl, bytes.NewBuffer(body))
	if err != nil {
		t.Fatal(err)
	}

	request.Header.Add("Authorization", "Bearer "+generateToken(req, ""))
	request.Header.Add("Content-Type", "application/json")

	resp, err := http.DefaultClient.Do(request)
	if err != nil {
		t.Fatal(err)
	}
	defer resp.Body.Close()

	assert.Equal(t, expectedCode, resp.StatusCode, "unexpected HTTP status code")

	bs, err := io.ReadAll(resp.Body)
	if err != nil {
		t.Fatal(err)
	}

	var actualResp map[string]interface{}
	if err := json.Unmarshal(bs, &actualResp); err != nil {
		t.Fatalf("failed to unmarshal response: %s", err)
	}

	for key, expectedValue := range expectedResp {
		if key == "result" {
			expectedResult := expectedValue.(map[string]interface{})
			actualResult := actualResp[key].(map[string]interface{})

			for resultKey, resultValue := range expectedResult {
				assert.Equal(t, resultValue, actualResult[resultKey], "unexpected value in result.%s", resultKey)
			}
		} else {
			assert.Equal(t, expectedValue, actualResp[key], "unexpected value for key %s", key)
		}
	}

	if expectedCode == http.StatusOK {
		assert.Contains(t, actualResp["result"], "https://", "result should contain a valid URL")
	}

	formattedResponse, err := json.MarshalIndent(actualResp, "", "  ")
	if err != nil {
		t.Fatal("Error formatting response: ", err)
	}
	fmt.Println("Formatted Response:", string(formattedResponse))
}
