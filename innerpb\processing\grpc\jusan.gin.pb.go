// Code generated by protoc-gen-go-mvp. DO NOT EDIT.
// versions:
// - protoc-gen-go-mvp v2.0.1
// created by mvp authors

package grpc

import (
	dog "git.local/sensitive/sdk/dog"
	gin "github.com/gin-gonic/gin"
	metadata "google.golang.org/grpc/metadata"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
	http "net/http"
)

func RegisterGinJusanRouterGroup(
	in *gin.RouterGroup,
	handler func(func(c *gin.Context) error) gin.HandlerFunc,
) error {
	service, err := NewGinJusanService()
	if err != nil {
		return err
	}

	routerGroup := in.Group("processing.jusan.jusan.Jusan")
	routerGroup.PUT("/PayIn", handler(service.PayIn))
	routerGroup.PUT("/OneClickPayIn", handler(service.OneClickPayIn))
	routerGroup.PUT("/ThreeDSConfirm", handler(service.ThreeDSConfirm))
	routerGroup.PUT("/ThreeDSResume", handler(service.ThreeDSResume))
	routerGroup.PUT("/PayOut", handler(service.PayOut))
	routerGroup.PUT("/GetBankTransactionStatus", handler(service.GetBankTransactionStatus))
	routerGroup.PUT("/GetBankTransactionStatusUnformated", handler(service.GetBankTransactionStatusUnformated))
	routerGroup.PUT("/Refund", handler(service.Refund))
	routerGroup.PUT("/GooglePay", handler(service.GooglePay))
	routerGroup.PUT("/ApplePay", handler(service.ApplePay))
	routerGroup.PUT("/TwoStagePayIn", handler(service.TwoStagePayIn))
	routerGroup.PUT("/Charge", handler(service.Charge))
	routerGroup.PUT("/Cancel", handler(service.Cancel))
	routerGroup.PUT("/MakeToken", handler(service.MakeToken))
	routerGroup.PUT("/GetEmission", handler(service.GetEmission))
	routerGroup.PUT("/ConfirmEmission", handler(service.ConfirmEmission))
	routerGroup.PUT("/GetAcquirerIdentifier", handler(service.GetAcquirerIdentifier))
	routerGroup.PUT("/CheckBalance", handler(service.CheckBalance))
	routerGroup.PUT("/ResolveVisaAlias", handler(service.ResolveVisaAlias))
	routerGroup.PUT("/PayOutByPhone", handler(service.PayOutByPhone))
	return nil
}

func NewGinJusanService() (GinJusanServer, error) {
	client, err := NewPreparedJusanClient()
	if err != nil {
		return nil, err
	}

	return &ginJusanServer{
		client: NewLoggedJusanClient(
			NewIamJusanClient(client),
		),
	}, nil
}

//go:generate mockgen -destination=./grpcmock/jusan.gin.pb.go -package=grpcmock -source=jusan.gin.pb.go GinJusanServer
type GinJusanServer interface {
	PayIn(c *gin.Context) error
	OneClickPayIn(c *gin.Context) error
	ThreeDSConfirm(c *gin.Context) error
	ThreeDSResume(c *gin.Context) error
	PayOut(c *gin.Context) error
	GetBankTransactionStatus(c *gin.Context) error
	GetBankTransactionStatusUnformated(c *gin.Context) error
	Refund(c *gin.Context) error
	GooglePay(c *gin.Context) error
	ApplePay(c *gin.Context) error
	TwoStagePayIn(c *gin.Context) error
	Charge(c *gin.Context) error
	Cancel(c *gin.Context) error
	MakeToken(c *gin.Context) error
	GetEmission(c *gin.Context) error
	ConfirmEmission(c *gin.Context) error
	GetAcquirerIdentifier(c *gin.Context) error
	CheckBalance(c *gin.Context) error
	ResolveVisaAlias(c *gin.Context) error
	PayOutByPhone(c *gin.Context) error
}

var _ GinJusanServer = (*ginJusanServer)(nil)

type ginJusanServer struct {
	client JusanClient
}

type Jusan_PayIn_Success struct {
	// Status is the status of the response. Set true
	Status bool `json:"status"`
	// Message is a human-readable explanation of the status
	Message string `json:"message"`
	// StatusCode is the GoErr status. Set 0
	StatusCode int `json:"status_code"`
	// Result is the result of the request
	Result *PayInResponseData `json:"result"`
}

type Jusan_PayIn_Failure struct {
	// Status is the status of the response. Set false
	Status bool `json:"status"`
	// Message is a human-readable explanation of the status
	Message string `json:"message"`
	// StatusCode is the GoErr status
	StatusCode int `json:"status_code"`
}

// PayIn
// @Summary PayIn
// @Security bearerAuth
// @ID Jusan_PayIn
// @Accept json
// @Param request body PayInRequestData true "PayInRequestData"
// @Success 200 {object} Jusan_PayIn_Success
// @Failure 401 {object} Jusan_PayIn_Failure "goerrpkg.ErrorTypeAuthorization"
// @Failure 404 {object} Jusan_PayIn_Failure "goerrpkg.ErrorTypeNotFound"
// @Failure 400 {object} Jusan_PayIn_Failure "goerrpkg.ErrorTypeBadRequest"
// @Failure 500 {object} Jusan_PayIn_Failure "goerrpkg.ErrorTypeUnexpectedError"
// @Failure 400 {object} Jusan_PayIn_Failure "gin.ErrorTypeBind"
// @Failure 500 {object} Jusan_PayIn_Failure "Undefined error"
// @Produce json
// @Router /processing.jusan.jusan.Jusan/PayIn [put]
// @tags Jusan
func (s *ginJusanServer) PayIn(c *gin.Context) error {
	ctx, span := dog.CreateSpan(c.Request.Context(), "GinJusanServer_PayIn")
	defer span.End()

	var request PayInRequestData
	if err := c.ShouldBindJSON(&request); err != nil {
		return err
	}

	ctx = metadata.NewOutgoingContext(ctx, metadata.MD(c.Request.Header))

	response, err := s.client.PayIn(ctx, &request)
	if err != nil {
		return err
	}

	c.JSON(http.StatusOK, &Jusan_PayIn_Success{
		Status:     true,
		Message:    "Success",
		StatusCode: 0,
		Result:     response,
	})
	return nil
}

type Jusan_OneClickPayIn_Success struct {
	// Status is the status of the response. Set true
	Status bool `json:"status"`
	// Message is a human-readable explanation of the status
	Message string `json:"message"`
	// StatusCode is the GoErr status. Set 0
	StatusCode int `json:"status_code"`
	// Result is the result of the request
	Result *PayInResponseData `json:"result"`
}

type Jusan_OneClickPayIn_Failure struct {
	// Status is the status of the response. Set false
	Status bool `json:"status"`
	// Message is a human-readable explanation of the status
	Message string `json:"message"`
	// StatusCode is the GoErr status
	StatusCode int `json:"status_code"`
}

// OneClickPayIn
// @Summary OneClickPayIn
// @Security bearerAuth
// @ID Jusan_OneClickPayIn
// @Accept json
// @Param request body OneClickPayInRequestData true "OneClickPayInRequestData"
// @Success 200 {object} Jusan_OneClickPayIn_Success
// @Failure 401 {object} Jusan_OneClickPayIn_Failure "goerrpkg.ErrorTypeAuthorization"
// @Failure 404 {object} Jusan_OneClickPayIn_Failure "goerrpkg.ErrorTypeNotFound"
// @Failure 400 {object} Jusan_OneClickPayIn_Failure "goerrpkg.ErrorTypeBadRequest"
// @Failure 500 {object} Jusan_OneClickPayIn_Failure "goerrpkg.ErrorTypeUnexpectedError"
// @Failure 400 {object} Jusan_OneClickPayIn_Failure "gin.ErrorTypeBind"
// @Failure 500 {object} Jusan_OneClickPayIn_Failure "Undefined error"
// @Produce json
// @Router /processing.jusan.jusan.Jusan/OneClickPayIn [put]
// @tags Jusan
func (s *ginJusanServer) OneClickPayIn(c *gin.Context) error {
	ctx, span := dog.CreateSpan(c.Request.Context(), "GinJusanServer_OneClickPayIn")
	defer span.End()

	var request OneClickPayInRequestData
	if err := c.ShouldBindJSON(&request); err != nil {
		return err
	}

	ctx = metadata.NewOutgoingContext(ctx, metadata.MD(c.Request.Header))

	response, err := s.client.OneClickPayIn(ctx, &request)
	if err != nil {
		return err
	}

	c.JSON(http.StatusOK, &Jusan_OneClickPayIn_Success{
		Status:     true,
		Message:    "Success",
		StatusCode: 0,
		Result:     response,
	})
	return nil
}

type Jusan_ThreeDSConfirm_Success struct {
	// Status is the status of the response. Set true
	Status bool `json:"status"`
	// Message is a human-readable explanation of the status
	Message string `json:"message"`
	// StatusCode is the GoErr status. Set 0
	StatusCode int `json:"status_code"`
	// Result is the result of the request
	Result *ThreeDSResponseData `json:"result"`
}

type Jusan_ThreeDSConfirm_Failure struct {
	// Status is the status of the response. Set false
	Status bool `json:"status"`
	// Message is a human-readable explanation of the status
	Message string `json:"message"`
	// StatusCode is the GoErr status
	StatusCode int `json:"status_code"`
}

// ThreeDSConfirm
// @Summary ThreeDSConfirm
// @Security bearerAuth
// @ID Jusan_ThreeDSConfirm
// @Accept json
// @Param request body ThreeDSRequestData true "ThreeDSRequestData"
// @Success 200 {object} Jusan_ThreeDSConfirm_Success
// @Failure 401 {object} Jusan_ThreeDSConfirm_Failure "goerrpkg.ErrorTypeAuthorization"
// @Failure 404 {object} Jusan_ThreeDSConfirm_Failure "goerrpkg.ErrorTypeNotFound"
// @Failure 400 {object} Jusan_ThreeDSConfirm_Failure "goerrpkg.ErrorTypeBadRequest"
// @Failure 500 {object} Jusan_ThreeDSConfirm_Failure "goerrpkg.ErrorTypeUnexpectedError"
// @Failure 400 {object} Jusan_ThreeDSConfirm_Failure "gin.ErrorTypeBind"
// @Failure 500 {object} Jusan_ThreeDSConfirm_Failure "Undefined error"
// @Produce json
// @Router /processing.jusan.jusan.Jusan/ThreeDSConfirm [put]
// @tags Jusan
func (s *ginJusanServer) ThreeDSConfirm(c *gin.Context) error {
	ctx, span := dog.CreateSpan(c.Request.Context(), "GinJusanServer_ThreeDSConfirm")
	defer span.End()

	var request ThreeDSRequestData
	if err := c.ShouldBindJSON(&request); err != nil {
		return err
	}

	ctx = metadata.NewOutgoingContext(ctx, metadata.MD(c.Request.Header))

	response, err := s.client.ThreeDSConfirm(ctx, &request)
	if err != nil {
		return err
	}

	c.JSON(http.StatusOK, &Jusan_ThreeDSConfirm_Success{
		Status:     true,
		Message:    "Success",
		StatusCode: 0,
		Result:     response,
	})
	return nil
}

type Jusan_ThreeDSResume_Success struct {
	// Status is the status of the response. Set true
	Status bool `json:"status"`
	// Message is a human-readable explanation of the status
	Message string `json:"message"`
	// StatusCode is the GoErr status. Set 0
	StatusCode int `json:"status_code"`
	// Result is the result of the request
	Result *ThreeDSResumeResponse `json:"result"`
}

type Jusan_ThreeDSResume_Failure struct {
	// Status is the status of the response. Set false
	Status bool `json:"status"`
	// Message is a human-readable explanation of the status
	Message string `json:"message"`
	// StatusCode is the GoErr status
	StatusCode int `json:"status_code"`
}

// ThreeDSResume
// @Summary ThreeDSResume
// @Security bearerAuth
// @ID Jusan_ThreeDSResume
// @Accept json
// @Param request body ThreeDSResumeRequest true "ThreeDSResumeRequest"
// @Success 200 {object} Jusan_ThreeDSResume_Success
// @Failure 401 {object} Jusan_ThreeDSResume_Failure "goerrpkg.ErrorTypeAuthorization"
// @Failure 404 {object} Jusan_ThreeDSResume_Failure "goerrpkg.ErrorTypeNotFound"
// @Failure 400 {object} Jusan_ThreeDSResume_Failure "goerrpkg.ErrorTypeBadRequest"
// @Failure 500 {object} Jusan_ThreeDSResume_Failure "goerrpkg.ErrorTypeUnexpectedError"
// @Failure 400 {object} Jusan_ThreeDSResume_Failure "gin.ErrorTypeBind"
// @Failure 500 {object} Jusan_ThreeDSResume_Failure "Undefined error"
// @Produce json
// @Router /processing.jusan.jusan.Jusan/ThreeDSResume [put]
// @tags Jusan
func (s *ginJusanServer) ThreeDSResume(c *gin.Context) error {
	ctx, span := dog.CreateSpan(c.Request.Context(), "GinJusanServer_ThreeDSResume")
	defer span.End()

	var request ThreeDSResumeRequest
	if err := c.ShouldBindJSON(&request); err != nil {
		return err
	}

	ctx = metadata.NewOutgoingContext(ctx, metadata.MD(c.Request.Header))

	response, err := s.client.ThreeDSResume(ctx, &request)
	if err != nil {
		return err
	}

	c.JSON(http.StatusOK, &Jusan_ThreeDSResume_Success{
		Status:     true,
		Message:    "Success",
		StatusCode: 0,
		Result:     response,
	})
	return nil
}

type Jusan_PayOut_Success struct {
	// Status is the status of the response. Set true
	Status bool `json:"status"`
	// Message is a human-readable explanation of the status
	Message string `json:"message"`
	// StatusCode is the GoErr status. Set 0
	StatusCode int `json:"status_code"`
	// Result is the result of the request
	Result *PayOutResponseData `json:"result"`
}

type Jusan_PayOut_Failure struct {
	// Status is the status of the response. Set false
	Status bool `json:"status"`
	// Message is a human-readable explanation of the status
	Message string `json:"message"`
	// StatusCode is the GoErr status
	StatusCode int `json:"status_code"`
}

// PayOut
// @Summary PayOut
// @Security bearerAuth
// @ID Jusan_PayOut
// @Accept json
// @Param request body PayOutRequestData true "PayOutRequestData"
// @Success 200 {object} Jusan_PayOut_Success
// @Failure 401 {object} Jusan_PayOut_Failure "goerrpkg.ErrorTypeAuthorization"
// @Failure 404 {object} Jusan_PayOut_Failure "goerrpkg.ErrorTypeNotFound"
// @Failure 400 {object} Jusan_PayOut_Failure "goerrpkg.ErrorTypeBadRequest"
// @Failure 500 {object} Jusan_PayOut_Failure "goerrpkg.ErrorTypeUnexpectedError"
// @Failure 400 {object} Jusan_PayOut_Failure "gin.ErrorTypeBind"
// @Failure 500 {object} Jusan_PayOut_Failure "Undefined error"
// @Produce json
// @Router /processing.jusan.jusan.Jusan/PayOut [put]
// @tags Jusan
func (s *ginJusanServer) PayOut(c *gin.Context) error {
	ctx, span := dog.CreateSpan(c.Request.Context(), "GinJusanServer_PayOut")
	defer span.End()

	var request PayOutRequestData
	if err := c.ShouldBindJSON(&request); err != nil {
		return err
	}

	ctx = metadata.NewOutgoingContext(ctx, metadata.MD(c.Request.Header))

	response, err := s.client.PayOut(ctx, &request)
	if err != nil {
		return err
	}

	c.JSON(http.StatusOK, &Jusan_PayOut_Success{
		Status:     true,
		Message:    "Success",
		StatusCode: 0,
		Result:     response,
	})
	return nil
}

type Jusan_GetBankTransactionStatus_Success struct {
	// Status is the status of the response. Set true
	Status bool `json:"status"`
	// Message is a human-readable explanation of the status
	Message string `json:"message"`
	// StatusCode is the GoErr status. Set 0
	StatusCode int `json:"status_code"`
	// Result is the result of the request
	Result *BankTransactionStatusResponse `json:"result"`
}

type Jusan_GetBankTransactionStatus_Failure struct {
	// Status is the status of the response. Set false
	Status bool `json:"status"`
	// Message is a human-readable explanation of the status
	Message string `json:"message"`
	// StatusCode is the GoErr status
	StatusCode int `json:"status_code"`
}

// GetBankTransactionStatus
// @Summary GetBankTransactionStatus
// @Security bearerAuth
// @ID Jusan_GetBankTransactionStatus
// @Accept json
// @Param request body BankTransactionStatusRequest true "BankTransactionStatusRequest"
// @Success 200 {object} Jusan_GetBankTransactionStatus_Success
// @Failure 401 {object} Jusan_GetBankTransactionStatus_Failure "goerrpkg.ErrorTypeAuthorization"
// @Failure 404 {object} Jusan_GetBankTransactionStatus_Failure "goerrpkg.ErrorTypeNotFound"
// @Failure 400 {object} Jusan_GetBankTransactionStatus_Failure "goerrpkg.ErrorTypeBadRequest"
// @Failure 500 {object} Jusan_GetBankTransactionStatus_Failure "goerrpkg.ErrorTypeUnexpectedError"
// @Failure 400 {object} Jusan_GetBankTransactionStatus_Failure "gin.ErrorTypeBind"
// @Failure 500 {object} Jusan_GetBankTransactionStatus_Failure "Undefined error"
// @Produce json
// @Router /processing.jusan.jusan.Jusan/GetBankTransactionStatus [put]
// @tags Jusan
func (s *ginJusanServer) GetBankTransactionStatus(c *gin.Context) error {
	ctx, span := dog.CreateSpan(c.Request.Context(), "GinJusanServer_GetBankTransactionStatus")
	defer span.End()

	var request BankTransactionStatusRequest
	if err := c.ShouldBindJSON(&request); err != nil {
		return err
	}

	ctx = metadata.NewOutgoingContext(ctx, metadata.MD(c.Request.Header))

	response, err := s.client.GetBankTransactionStatus(ctx, &request)
	if err != nil {
		return err
	}

	c.JSON(http.StatusOK, &Jusan_GetBankTransactionStatus_Success{
		Status:     true,
		Message:    "Success",
		StatusCode: 0,
		Result:     response,
	})
	return nil
}

type Jusan_GetBankTransactionStatusUnformated_Success struct {
	// Status is the status of the response. Set true
	Status bool `json:"status"`
	// Message is a human-readable explanation of the status
	Message string `json:"message"`
	// StatusCode is the GoErr status. Set 0
	StatusCode int `json:"status_code"`
	// Result is the result of the request
	Result *BankTransactionStatusUnformatedResponse `json:"result"`
}

type Jusan_GetBankTransactionStatusUnformated_Failure struct {
	// Status is the status of the response. Set false
	Status bool `json:"status"`
	// Message is a human-readable explanation of the status
	Message string `json:"message"`
	// StatusCode is the GoErr status
	StatusCode int `json:"status_code"`
}

// GetBankTransactionStatusUnformated
// @Summary GetBankTransactionStatusUnformated
// @Security bearerAuth
// @ID Jusan_GetBankTransactionStatusUnformated
// @Accept json
// @Param request body BankTransactionStatusUnformatedRequest true "BankTransactionStatusUnformatedRequest"
// @Success 200 {object} Jusan_GetBankTransactionStatusUnformated_Success
// @Failure 401 {object} Jusan_GetBankTransactionStatusUnformated_Failure "goerrpkg.ErrorTypeAuthorization"
// @Failure 404 {object} Jusan_GetBankTransactionStatusUnformated_Failure "goerrpkg.ErrorTypeNotFound"
// @Failure 400 {object} Jusan_GetBankTransactionStatusUnformated_Failure "goerrpkg.ErrorTypeBadRequest"
// @Failure 500 {object} Jusan_GetBankTransactionStatusUnformated_Failure "goerrpkg.ErrorTypeUnexpectedError"
// @Failure 400 {object} Jusan_GetBankTransactionStatusUnformated_Failure "gin.ErrorTypeBind"
// @Failure 500 {object} Jusan_GetBankTransactionStatusUnformated_Failure "Undefined error"
// @Produce json
// @Router /processing.jusan.jusan.Jusan/GetBankTransactionStatusUnformated [put]
// @tags Jusan
func (s *ginJusanServer) GetBankTransactionStatusUnformated(c *gin.Context) error {
	ctx, span := dog.CreateSpan(c.Request.Context(), "GinJusanServer_GetBankTransactionStatusUnformated")
	defer span.End()

	var request BankTransactionStatusUnformatedRequest
	if err := c.ShouldBindJSON(&request); err != nil {
		return err
	}

	ctx = metadata.NewOutgoingContext(ctx, metadata.MD(c.Request.Header))

	response, err := s.client.GetBankTransactionStatusUnformated(ctx, &request)
	if err != nil {
		return err
	}

	c.JSON(http.StatusOK, &Jusan_GetBankTransactionStatusUnformated_Success{
		Status:     true,
		Message:    "Success",
		StatusCode: 0,
		Result:     response,
	})
	return nil
}

type Jusan_Refund_Success struct {
	// Status is the status of the response. Set true
	Status bool `json:"status"`
	// Message is a human-readable explanation of the status
	Message string `json:"message"`
	// StatusCode is the GoErr status. Set 0
	StatusCode int `json:"status_code"`
	// Result is the result of the request
	Result *RefundResponse `json:"result"`
}

type Jusan_Refund_Failure struct {
	// Status is the status of the response. Set false
	Status bool `json:"status"`
	// Message is a human-readable explanation of the status
	Message string `json:"message"`
	// StatusCode is the GoErr status
	StatusCode int `json:"status_code"`
}

// Refund
// @Summary Refund
// @Security bearerAuth
// @ID Jusan_Refund
// @Accept json
// @Param request body RefundRequest true "RefundRequest"
// @Success 200 {object} Jusan_Refund_Success
// @Failure 401 {object} Jusan_Refund_Failure "goerrpkg.ErrorTypeAuthorization"
// @Failure 404 {object} Jusan_Refund_Failure "goerrpkg.ErrorTypeNotFound"
// @Failure 400 {object} Jusan_Refund_Failure "goerrpkg.ErrorTypeBadRequest"
// @Failure 500 {object} Jusan_Refund_Failure "goerrpkg.ErrorTypeUnexpectedError"
// @Failure 400 {object} Jusan_Refund_Failure "gin.ErrorTypeBind"
// @Failure 500 {object} Jusan_Refund_Failure "Undefined error"
// @Produce json
// @Router /processing.jusan.jusan.Jusan/Refund [put]
// @tags Jusan
func (s *ginJusanServer) Refund(c *gin.Context) error {
	ctx, span := dog.CreateSpan(c.Request.Context(), "GinJusanServer_Refund")
	defer span.End()

	var request RefundRequest
	if err := c.ShouldBindJSON(&request); err != nil {
		return err
	}

	ctx = metadata.NewOutgoingContext(ctx, metadata.MD(c.Request.Header))

	response, err := s.client.Refund(ctx, &request)
	if err != nil {
		return err
	}

	c.JSON(http.StatusOK, &Jusan_Refund_Success{
		Status:     true,
		Message:    "Success",
		StatusCode: 0,
		Result:     response,
	})
	return nil
}

type Jusan_GooglePay_Success struct {
	// Status is the status of the response. Set true
	Status bool `json:"status"`
	// Message is a human-readable explanation of the status
	Message string `json:"message"`
	// StatusCode is the GoErr status. Set 0
	StatusCode int `json:"status_code"`
	// Result is the result of the request
	Result *GooglePayResponseData `json:"result"`
}

type Jusan_GooglePay_Failure struct {
	// Status is the status of the response. Set false
	Status bool `json:"status"`
	// Message is a human-readable explanation of the status
	Message string `json:"message"`
	// StatusCode is the GoErr status
	StatusCode int `json:"status_code"`
}

// GooglePay
// @Summary GooglePay
// @Security bearerAuth
// @ID Jusan_GooglePay
// @Accept json
// @Param request body GooglePayRequestData true "GooglePayRequestData"
// @Success 200 {object} Jusan_GooglePay_Success
// @Failure 401 {object} Jusan_GooglePay_Failure "goerrpkg.ErrorTypeAuthorization"
// @Failure 404 {object} Jusan_GooglePay_Failure "goerrpkg.ErrorTypeNotFound"
// @Failure 400 {object} Jusan_GooglePay_Failure "goerrpkg.ErrorTypeBadRequest"
// @Failure 500 {object} Jusan_GooglePay_Failure "goerrpkg.ErrorTypeUnexpectedError"
// @Failure 400 {object} Jusan_GooglePay_Failure "gin.ErrorTypeBind"
// @Failure 500 {object} Jusan_GooglePay_Failure "Undefined error"
// @Produce json
// @Router /processing.jusan.jusan.Jusan/GooglePay [put]
// @tags Jusan
func (s *ginJusanServer) GooglePay(c *gin.Context) error {
	ctx, span := dog.CreateSpan(c.Request.Context(), "GinJusanServer_GooglePay")
	defer span.End()

	var request GooglePayRequestData
	if err := c.ShouldBindJSON(&request); err != nil {
		return err
	}

	ctx = metadata.NewOutgoingContext(ctx, metadata.MD(c.Request.Header))

	response, err := s.client.GooglePay(ctx, &request)
	if err != nil {
		return err
	}

	c.JSON(http.StatusOK, &Jusan_GooglePay_Success{
		Status:     true,
		Message:    "Success",
		StatusCode: 0,
		Result:     response,
	})
	return nil
}

type Jusan_ApplePay_Success struct {
	// Status is the status of the response. Set true
	Status bool `json:"status"`
	// Message is a human-readable explanation of the status
	Message string `json:"message"`
	// StatusCode is the GoErr status. Set 0
	StatusCode int `json:"status_code"`
	// Result is the result of the request
	Result *ApplePayResponseData `json:"result"`
}

type Jusan_ApplePay_Failure struct {
	// Status is the status of the response. Set false
	Status bool `json:"status"`
	// Message is a human-readable explanation of the status
	Message string `json:"message"`
	// StatusCode is the GoErr status
	StatusCode int `json:"status_code"`
}

// ApplePay
// @Summary ApplePay
// @Security bearerAuth
// @ID Jusan_ApplePay
// @Accept json
// @Param request body ApplePayRequestData true "ApplePayRequestData"
// @Success 200 {object} Jusan_ApplePay_Success
// @Failure 401 {object} Jusan_ApplePay_Failure "goerrpkg.ErrorTypeAuthorization"
// @Failure 404 {object} Jusan_ApplePay_Failure "goerrpkg.ErrorTypeNotFound"
// @Failure 400 {object} Jusan_ApplePay_Failure "goerrpkg.ErrorTypeBadRequest"
// @Failure 500 {object} Jusan_ApplePay_Failure "goerrpkg.ErrorTypeUnexpectedError"
// @Failure 400 {object} Jusan_ApplePay_Failure "gin.ErrorTypeBind"
// @Failure 500 {object} Jusan_ApplePay_Failure "Undefined error"
// @Produce json
// @Router /processing.jusan.jusan.Jusan/ApplePay [put]
// @tags Jusan
func (s *ginJusanServer) ApplePay(c *gin.Context) error {
	ctx, span := dog.CreateSpan(c.Request.Context(), "GinJusanServer_ApplePay")
	defer span.End()

	var request ApplePayRequestData
	if err := c.ShouldBindJSON(&request); err != nil {
		return err
	}

	ctx = metadata.NewOutgoingContext(ctx, metadata.MD(c.Request.Header))

	response, err := s.client.ApplePay(ctx, &request)
	if err != nil {
		return err
	}

	c.JSON(http.StatusOK, &Jusan_ApplePay_Success{
		Status:     true,
		Message:    "Success",
		StatusCode: 0,
		Result:     response,
	})
	return nil
}

type Jusan_TwoStagePayIn_Success struct {
	// Status is the status of the response. Set true
	Status bool `json:"status"`
	// Message is a human-readable explanation of the status
	Message string `json:"message"`
	// StatusCode is the GoErr status. Set 0
	StatusCode int `json:"status_code"`
	// Result is the result of the request
	Result *TwoStagePayInResponse `json:"result"`
}

type Jusan_TwoStagePayIn_Failure struct {
	// Status is the status of the response. Set false
	Status bool `json:"status"`
	// Message is a human-readable explanation of the status
	Message string `json:"message"`
	// StatusCode is the GoErr status
	StatusCode int `json:"status_code"`
}

// TwoStagePayIn
// @Summary TwoStagePayIn
// @Security bearerAuth
// @ID Jusan_TwoStagePayIn
// @Accept json
// @Param request body TwoStagePayInRequest true "TwoStagePayInRequest"
// @Success 200 {object} Jusan_TwoStagePayIn_Success
// @Failure 401 {object} Jusan_TwoStagePayIn_Failure "goerrpkg.ErrorTypeAuthorization"
// @Failure 404 {object} Jusan_TwoStagePayIn_Failure "goerrpkg.ErrorTypeNotFound"
// @Failure 400 {object} Jusan_TwoStagePayIn_Failure "goerrpkg.ErrorTypeBadRequest"
// @Failure 500 {object} Jusan_TwoStagePayIn_Failure "goerrpkg.ErrorTypeUnexpectedError"
// @Failure 400 {object} Jusan_TwoStagePayIn_Failure "gin.ErrorTypeBind"
// @Failure 500 {object} Jusan_TwoStagePayIn_Failure "Undefined error"
// @Produce json
// @Router /processing.jusan.jusan.Jusan/TwoStagePayIn [put]
// @tags Jusan
func (s *ginJusanServer) TwoStagePayIn(c *gin.Context) error {
	ctx, span := dog.CreateSpan(c.Request.Context(), "GinJusanServer_TwoStagePayIn")
	defer span.End()

	var request TwoStagePayInRequest
	if err := c.ShouldBindJSON(&request); err != nil {
		return err
	}

	ctx = metadata.NewOutgoingContext(ctx, metadata.MD(c.Request.Header))

	response, err := s.client.TwoStagePayIn(ctx, &request)
	if err != nil {
		return err
	}

	c.JSON(http.StatusOK, &Jusan_TwoStagePayIn_Success{
		Status:     true,
		Message:    "Success",
		StatusCode: 0,
		Result:     response,
	})
	return nil
}

type Jusan_Charge_Success struct {
	// Status is the status of the response. Set true
	Status bool `json:"status"`
	// Message is a human-readable explanation of the status
	Message string `json:"message"`
	// StatusCode is the GoErr status. Set 0
	StatusCode int `json:"status_code"`
	// Result is the result of the request
	Result *ChargeResponse `json:"result"`
}

type Jusan_Charge_Failure struct {
	// Status is the status of the response. Set false
	Status bool `json:"status"`
	// Message is a human-readable explanation of the status
	Message string `json:"message"`
	// StatusCode is the GoErr status
	StatusCode int `json:"status_code"`
}

// Charge
// @Summary Charge
// @Security bearerAuth
// @ID Jusan_Charge
// @Accept json
// @Param request body ChargeRequest true "ChargeRequest"
// @Success 200 {object} Jusan_Charge_Success
// @Failure 401 {object} Jusan_Charge_Failure "goerrpkg.ErrorTypeAuthorization"
// @Failure 404 {object} Jusan_Charge_Failure "goerrpkg.ErrorTypeNotFound"
// @Failure 400 {object} Jusan_Charge_Failure "goerrpkg.ErrorTypeBadRequest"
// @Failure 500 {object} Jusan_Charge_Failure "goerrpkg.ErrorTypeUnexpectedError"
// @Failure 400 {object} Jusan_Charge_Failure "gin.ErrorTypeBind"
// @Failure 500 {object} Jusan_Charge_Failure "Undefined error"
// @Produce json
// @Router /processing.jusan.jusan.Jusan/Charge [put]
// @tags Jusan
func (s *ginJusanServer) Charge(c *gin.Context) error {
	ctx, span := dog.CreateSpan(c.Request.Context(), "GinJusanServer_Charge")
	defer span.End()

	var request ChargeRequest
	if err := c.ShouldBindJSON(&request); err != nil {
		return err
	}

	ctx = metadata.NewOutgoingContext(ctx, metadata.MD(c.Request.Header))

	response, err := s.client.Charge(ctx, &request)
	if err != nil {
		return err
	}

	c.JSON(http.StatusOK, &Jusan_Charge_Success{
		Status:     true,
		Message:    "Success",
		StatusCode: 0,
		Result:     response,
	})
	return nil
}

type Jusan_Cancel_Success struct {
	// Status is the status of the response. Set true
	Status bool `json:"status"`
	// Message is a human-readable explanation of the status
	Message string `json:"message"`
	// StatusCode is the GoErr status. Set 0
	StatusCode int `json:"status_code"`
	// Result is the result of the request
	Result *CancelResponse `json:"result"`
}

type Jusan_Cancel_Failure struct {
	// Status is the status of the response. Set false
	Status bool `json:"status"`
	// Message is a human-readable explanation of the status
	Message string `json:"message"`
	// StatusCode is the GoErr status
	StatusCode int `json:"status_code"`
}

// Cancel
// @Summary Cancel
// @Security bearerAuth
// @ID Jusan_Cancel
// @Accept json
// @Param request body CancelRequest true "CancelRequest"
// @Success 200 {object} Jusan_Cancel_Success
// @Failure 401 {object} Jusan_Cancel_Failure "goerrpkg.ErrorTypeAuthorization"
// @Failure 404 {object} Jusan_Cancel_Failure "goerrpkg.ErrorTypeNotFound"
// @Failure 400 {object} Jusan_Cancel_Failure "goerrpkg.ErrorTypeBadRequest"
// @Failure 500 {object} Jusan_Cancel_Failure "goerrpkg.ErrorTypeUnexpectedError"
// @Failure 400 {object} Jusan_Cancel_Failure "gin.ErrorTypeBind"
// @Failure 500 {object} Jusan_Cancel_Failure "Undefined error"
// @Produce json
// @Router /processing.jusan.jusan.Jusan/Cancel [put]
// @tags Jusan
func (s *ginJusanServer) Cancel(c *gin.Context) error {
	ctx, span := dog.CreateSpan(c.Request.Context(), "GinJusanServer_Cancel")
	defer span.End()

	var request CancelRequest
	if err := c.ShouldBindJSON(&request); err != nil {
		return err
	}

	ctx = metadata.NewOutgoingContext(ctx, metadata.MD(c.Request.Header))

	response, err := s.client.Cancel(ctx, &request)
	if err != nil {
		return err
	}

	c.JSON(http.StatusOK, &Jusan_Cancel_Success{
		Status:     true,
		Message:    "Success",
		StatusCode: 0,
		Result:     response,
	})
	return nil
}

type Jusan_MakeToken_Success struct {
	// Status is the status of the response. Set true
	Status bool `json:"status"`
	// Message is a human-readable explanation of the status
	Message string `json:"message"`
	// StatusCode is the GoErr status. Set 0
	StatusCode int `json:"status_code"`
	// Result is the result of the request
	Result *PayInResponseData `json:"result"`
}

type Jusan_MakeToken_Failure struct {
	// Status is the status of the response. Set false
	Status bool `json:"status"`
	// Message is a human-readable explanation of the status
	Message string `json:"message"`
	// StatusCode is the GoErr status
	StatusCode int `json:"status_code"`
}

// MakeToken
// @Summary MakeToken
// @Security bearerAuth
// @ID Jusan_MakeToken
// @Accept json
// @Param request body PayInRequestData true "PayInRequestData"
// @Success 200 {object} Jusan_MakeToken_Success
// @Failure 401 {object} Jusan_MakeToken_Failure "goerrpkg.ErrorTypeAuthorization"
// @Failure 404 {object} Jusan_MakeToken_Failure "goerrpkg.ErrorTypeNotFound"
// @Failure 400 {object} Jusan_MakeToken_Failure "goerrpkg.ErrorTypeBadRequest"
// @Failure 500 {object} Jusan_MakeToken_Failure "goerrpkg.ErrorTypeUnexpectedError"
// @Failure 400 {object} Jusan_MakeToken_Failure "gin.ErrorTypeBind"
// @Failure 500 {object} Jusan_MakeToken_Failure "Undefined error"
// @Produce json
// @Router /processing.jusan.jusan.Jusan/MakeToken [put]
// @tags Jusan
func (s *ginJusanServer) MakeToken(c *gin.Context) error {
	ctx, span := dog.CreateSpan(c.Request.Context(), "GinJusanServer_MakeToken")
	defer span.End()

	var request PayInRequestData
	if err := c.ShouldBindJSON(&request); err != nil {
		return err
	}

	ctx = metadata.NewOutgoingContext(ctx, metadata.MD(c.Request.Header))

	response, err := s.client.MakeToken(ctx, &request)
	if err != nil {
		return err
	}

	c.JSON(http.StatusOK, &Jusan_MakeToken_Success{
		Status:     true,
		Message:    "Success",
		StatusCode: 0,
		Result:     response,
	})
	return nil
}

type Jusan_GetEmission_Success struct {
	// Status is the status of the response. Set true
	Status bool `json:"status"`
	// Message is a human-readable explanation of the status
	Message string `json:"message"`
	// StatusCode is the GoErr status. Set 0
	StatusCode int `json:"status_code"`
	// Result is the result of the request
	Result *EmissionResponse `json:"result"`
}

type Jusan_GetEmission_Failure struct {
	// Status is the status of the response. Set false
	Status bool `json:"status"`
	// Message is a human-readable explanation of the status
	Message string `json:"message"`
	// StatusCode is the GoErr status
	StatusCode int `json:"status_code"`
}

// GetEmission
// @Summary GetEmission
// @Security bearerAuth
// @ID Jusan_GetEmission
// @Accept json
// @Param request body emptypb.Empty true "Empty"
// @Success 200 {object} Jusan_GetEmission_Success
// @Failure 401 {object} Jusan_GetEmission_Failure "goerrpkg.ErrorTypeAuthorization"
// @Failure 404 {object} Jusan_GetEmission_Failure "goerrpkg.ErrorTypeNotFound"
// @Failure 400 {object} Jusan_GetEmission_Failure "goerrpkg.ErrorTypeBadRequest"
// @Failure 500 {object} Jusan_GetEmission_Failure "goerrpkg.ErrorTypeUnexpectedError"
// @Failure 400 {object} Jusan_GetEmission_Failure "gin.ErrorTypeBind"
// @Failure 500 {object} Jusan_GetEmission_Failure "Undefined error"
// @Produce json
// @Router /processing.jusan.jusan.Jusan/GetEmission [put]
// @tags Jusan
func (s *ginJusanServer) GetEmission(c *gin.Context) error {
	ctx, span := dog.CreateSpan(c.Request.Context(), "GinJusanServer_GetEmission")
	defer span.End()

	var request emptypb.Empty
	if err := c.ShouldBindJSON(&request); err != nil {
		return err
	}

	ctx = metadata.NewOutgoingContext(ctx, metadata.MD(c.Request.Header))

	response, err := s.client.GetEmission(ctx, &request)
	if err != nil {
		return err
	}

	c.JSON(http.StatusOK, &Jusan_GetEmission_Success{
		Status:     true,
		Message:    "Success",
		StatusCode: 0,
		Result:     response,
	})
	return nil
}

type Jusan_ConfirmEmission_Success struct {
	// Status is the status of the response. Set true
	Status bool `json:"status"`
	// Message is a human-readable explanation of the status
	Message string `json:"message"`
	// StatusCode is the GoErr status. Set 0
	StatusCode int `json:"status_code"`
	// Result is the result of the request
	Result *EmoneyResponse `json:"result"`
}

type Jusan_ConfirmEmission_Failure struct {
	// Status is the status of the response. Set false
	Status bool `json:"status"`
	// Message is a human-readable explanation of the status
	Message string `json:"message"`
	// StatusCode is the GoErr status
	StatusCode int `json:"status_code"`
}

// ConfirmEmission
// @Summary ConfirmEmission
// @Security bearerAuth
// @ID Jusan_ConfirmEmission
// @Accept json
// @Param request body EmoneyRequest true "EmoneyRequest"
// @Success 200 {object} Jusan_ConfirmEmission_Success
// @Failure 401 {object} Jusan_ConfirmEmission_Failure "goerrpkg.ErrorTypeAuthorization"
// @Failure 404 {object} Jusan_ConfirmEmission_Failure "goerrpkg.ErrorTypeNotFound"
// @Failure 400 {object} Jusan_ConfirmEmission_Failure "goerrpkg.ErrorTypeBadRequest"
// @Failure 500 {object} Jusan_ConfirmEmission_Failure "goerrpkg.ErrorTypeUnexpectedError"
// @Failure 400 {object} Jusan_ConfirmEmission_Failure "gin.ErrorTypeBind"
// @Failure 500 {object} Jusan_ConfirmEmission_Failure "Undefined error"
// @Produce json
// @Router /processing.jusan.jusan.Jusan/ConfirmEmission [put]
// @tags Jusan
func (s *ginJusanServer) ConfirmEmission(c *gin.Context) error {
	ctx, span := dog.CreateSpan(c.Request.Context(), "GinJusanServer_ConfirmEmission")
	defer span.End()

	var request EmoneyRequest
	if err := c.ShouldBindJSON(&request); err != nil {
		return err
	}

	ctx = metadata.NewOutgoingContext(ctx, metadata.MD(c.Request.Header))

	response, err := s.client.ConfirmEmission(ctx, &request)
	if err != nil {
		return err
	}

	c.JSON(http.StatusOK, &Jusan_ConfirmEmission_Success{
		Status:     true,
		Message:    "Success",
		StatusCode: 0,
		Result:     response,
	})
	return nil
}

type Jusan_GetAcquirerIdentifier_Success struct {
	// Status is the status of the response. Set true
	Status bool `json:"status"`
	// Message is a human-readable explanation of the status
	Message string `json:"message"`
	// StatusCode is the GoErr status. Set 0
	StatusCode int `json:"status_code"`
	// Result is the result of the request
	Result *GetAcquirerIdentifierResponse `json:"result"`
}

type Jusan_GetAcquirerIdentifier_Failure struct {
	// Status is the status of the response. Set false
	Status bool `json:"status"`
	// Message is a human-readable explanation of the status
	Message string `json:"message"`
	// StatusCode is the GoErr status
	StatusCode int `json:"status_code"`
}

// GetAcquirerIdentifier
// @Summary GetAcquirerIdentifier
// @Security bearerAuth
// @ID Jusan_GetAcquirerIdentifier
// @Accept json
// @Param request body emptypb.Empty true "Empty"
// @Success 200 {object} Jusan_GetAcquirerIdentifier_Success
// @Failure 401 {object} Jusan_GetAcquirerIdentifier_Failure "goerrpkg.ErrorTypeAuthorization"
// @Failure 404 {object} Jusan_GetAcquirerIdentifier_Failure "goerrpkg.ErrorTypeNotFound"
// @Failure 400 {object} Jusan_GetAcquirerIdentifier_Failure "goerrpkg.ErrorTypeBadRequest"
// @Failure 500 {object} Jusan_GetAcquirerIdentifier_Failure "goerrpkg.ErrorTypeUnexpectedError"
// @Failure 400 {object} Jusan_GetAcquirerIdentifier_Failure "gin.ErrorTypeBind"
// @Failure 500 {object} Jusan_GetAcquirerIdentifier_Failure "Undefined error"
// @Produce json
// @Router /processing.jusan.jusan.Jusan/GetAcquirerIdentifier [put]
// @tags Jusan
func (s *ginJusanServer) GetAcquirerIdentifier(c *gin.Context) error {
	ctx, span := dog.CreateSpan(c.Request.Context(), "GinJusanServer_GetAcquirerIdentifier")
	defer span.End()

	var request emptypb.Empty
	if err := c.ShouldBindJSON(&request); err != nil {
		return err
	}

	ctx = metadata.NewOutgoingContext(ctx, metadata.MD(c.Request.Header))

	response, err := s.client.GetAcquirerIdentifier(ctx, &request)
	if err != nil {
		return err
	}

	c.JSON(http.StatusOK, &Jusan_GetAcquirerIdentifier_Success{
		Status:     true,
		Message:    "Success",
		StatusCode: 0,
		Result:     response,
	})
	return nil
}

type Jusan_CheckBalance_Success struct {
	// Status is the status of the response. Set true
	Status bool `json:"status"`
	// Message is a human-readable explanation of the status
	Message string `json:"message"`
	// StatusCode is the GoErr status. Set 0
	StatusCode int `json:"status_code"`
	// Result is the result of the request
	Result *CheckBalanceResponse `json:"result"`
}

type Jusan_CheckBalance_Failure struct {
	// Status is the status of the response. Set false
	Status bool `json:"status"`
	// Message is a human-readable explanation of the status
	Message string `json:"message"`
	// StatusCode is the GoErr status
	StatusCode int `json:"status_code"`
}

// CheckBalance
// @Summary CheckBalance
// @Security bearerAuth
// @ID Jusan_CheckBalance
// @Accept json
// @Param request body CheckBalanceRequest true "CheckBalanceRequest"
// @Success 200 {object} Jusan_CheckBalance_Success
// @Failure 401 {object} Jusan_CheckBalance_Failure "goerrpkg.ErrorTypeAuthorization"
// @Failure 404 {object} Jusan_CheckBalance_Failure "goerrpkg.ErrorTypeNotFound"
// @Failure 400 {object} Jusan_CheckBalance_Failure "goerrpkg.ErrorTypeBadRequest"
// @Failure 500 {object} Jusan_CheckBalance_Failure "goerrpkg.ErrorTypeUnexpectedError"
// @Failure 400 {object} Jusan_CheckBalance_Failure "gin.ErrorTypeBind"
// @Failure 500 {object} Jusan_CheckBalance_Failure "Undefined error"
// @Produce json
// @Router /processing.jusan.jusan.Jusan/CheckBalance [put]
// @tags Jusan
func (s *ginJusanServer) CheckBalance(c *gin.Context) error {
	ctx, span := dog.CreateSpan(c.Request.Context(), "GinJusanServer_CheckBalance")
	defer span.End()

	var request CheckBalanceRequest
	if err := c.ShouldBindJSON(&request); err != nil {
		return err
	}

	ctx = metadata.NewOutgoingContext(ctx, metadata.MD(c.Request.Header))

	response, err := s.client.CheckBalance(ctx, &request)
	if err != nil {
		return err
	}

	c.JSON(http.StatusOK, &Jusan_CheckBalance_Success{
		Status:     true,
		Message:    "Success",
		StatusCode: 0,
		Result:     response,
	})
	return nil
}

type Jusan_ResolveVisaAlias_Success struct {
	// Status is the status of the response. Set true
	Status bool `json:"status"`
	// Message is a human-readable explanation of the status
	Message string `json:"message"`
	// StatusCode is the GoErr status. Set 0
	StatusCode int `json:"status_code"`
	// Result is the result of the request
	Result *ResolveVisaAliasResponse `json:"result"`
}

type Jusan_ResolveVisaAlias_Failure struct {
	// Status is the status of the response. Set false
	Status bool `json:"status"`
	// Message is a human-readable explanation of the status
	Message string `json:"message"`
	// StatusCode is the GoErr status
	StatusCode int `json:"status_code"`
}

// ResolveVisaAlias
// @Summary ResolveVisaAlias
// @Security bearerAuth
// @ID Jusan_ResolveVisaAlias
// @Accept json
// @Param request body ResolveVisaAliasRequest true "ResolveVisaAliasRequest"
// @Success 200 {object} Jusan_ResolveVisaAlias_Success
// @Failure 401 {object} Jusan_ResolveVisaAlias_Failure "goerrpkg.ErrorTypeAuthorization"
// @Failure 404 {object} Jusan_ResolveVisaAlias_Failure "goerrpkg.ErrorTypeNotFound"
// @Failure 400 {object} Jusan_ResolveVisaAlias_Failure "goerrpkg.ErrorTypeBadRequest"
// @Failure 500 {object} Jusan_ResolveVisaAlias_Failure "goerrpkg.ErrorTypeUnexpectedError"
// @Failure 400 {object} Jusan_ResolveVisaAlias_Failure "gin.ErrorTypeBind"
// @Failure 500 {object} Jusan_ResolveVisaAlias_Failure "Undefined error"
// @Produce json
// @Router /processing.jusan.jusan.Jusan/ResolveVisaAlias [put]
// @tags Jusan
func (s *ginJusanServer) ResolveVisaAlias(c *gin.Context) error {
	ctx, span := dog.CreateSpan(c.Request.Context(), "GinJusanServer_ResolveVisaAlias")
	defer span.End()

	var request ResolveVisaAliasRequest
	if err := c.ShouldBindJSON(&request); err != nil {
		return err
	}

	ctx = metadata.NewOutgoingContext(ctx, metadata.MD(c.Request.Header))

	response, err := s.client.ResolveVisaAlias(ctx, &request)
	if err != nil {
		return err
	}

	c.JSON(http.StatusOK, &Jusan_ResolveVisaAlias_Success{
		Status:     true,
		Message:    "Success",
		StatusCode: 0,
		Result:     response,
	})
	return nil
}

type Jusan_PayOutByPhone_Success struct {
	// Status is the status of the response. Set true
	Status bool `json:"status"`
	// Message is a human-readable explanation of the status
	Message string `json:"message"`
	// StatusCode is the GoErr status. Set 0
	StatusCode int `json:"status_code"`
	// Result is the result of the request
	Result *PayOutResponseByPhoneData `json:"result"`
}

type Jusan_PayOutByPhone_Failure struct {
	// Status is the status of the response. Set false
	Status bool `json:"status"`
	// Message is a human-readable explanation of the status
	Message string `json:"message"`
	// StatusCode is the GoErr status
	StatusCode int `json:"status_code"`
}

// PayOutByPhone
// @Summary PayOutByPhone
// @Security bearerAuth
// @ID Jusan_PayOutByPhone
// @Accept json
// @Param request body PayOutByPhoneRequestData true "PayOutByPhoneRequestData"
// @Success 200 {object} Jusan_PayOutByPhone_Success
// @Failure 401 {object} Jusan_PayOutByPhone_Failure "goerrpkg.ErrorTypeAuthorization"
// @Failure 404 {object} Jusan_PayOutByPhone_Failure "goerrpkg.ErrorTypeNotFound"
// @Failure 400 {object} Jusan_PayOutByPhone_Failure "goerrpkg.ErrorTypeBadRequest"
// @Failure 500 {object} Jusan_PayOutByPhone_Failure "goerrpkg.ErrorTypeUnexpectedError"
// @Failure 400 {object} Jusan_PayOutByPhone_Failure "gin.ErrorTypeBind"
// @Failure 500 {object} Jusan_PayOutByPhone_Failure "Undefined error"
// @Produce json
// @Router /processing.jusan.jusan.Jusan/PayOutByPhone [put]
// @tags Jusan
func (s *ginJusanServer) PayOutByPhone(c *gin.Context) error {
	ctx, span := dog.CreateSpan(c.Request.Context(), "GinJusanServer_PayOutByPhone")
	defer span.End()

	var request PayOutByPhoneRequestData
	if err := c.ShouldBindJSON(&request); err != nil {
		return err
	}

	ctx = metadata.NewOutgoingContext(ctx, metadata.MD(c.Request.Header))

	response, err := s.client.PayOutByPhone(ctx, &request)
	if err != nil {
		return err
	}

	c.JSON(http.StatusOK, &Jusan_PayOutByPhone_Success{
		Status:     true,
		Message:    "Success",
		StatusCode: 0,
		Result:     response,
	})
	return nil
}
