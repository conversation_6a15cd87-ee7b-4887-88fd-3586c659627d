package database

import (
	"context"
	"errors"
	"time"

	"github.com/jackc/pgerrcode"
	"github.com/jackc/pgx/v5/pgconn"
	"gorm.io/gorm"

	"git.local/sensitive/innerpb/processing/goerr"
	"git.local/sensitive/processing/commission/model"
	"git.local/sensitive/sdk/dog"
)

type ProjectLowerCommissionDB struct {
	db *gorm.DB
}

func NewProjectLowerCommissionDB(db *gorm.DB) ProjectLowerCommissioner {
	return ProjectLowerCommissionDB{
		db: db,
	}
}

func (p ProjectLowerCommissionDB) Create(ctx context.Context, projectComm model.ProjectLowerCommission) (err error) {
	ctx, span := dog.CreateSpan(ctx, "ProjectLowerCommissionDB_Create")
	defer span.End()

	if err = p.db.WithContext(ctx).Create(&projectComm).Error; err != nil {
		var pgErr *pgconn.PgError
		if errors.As(err, &pgErr) && pgErr.Code == pgerrcode.UniqueViolation {
			return goerr.ErrCommissionDateRangeOverlapping.WithCtx(ctx)
		}

		return goerr.ErrDbUnexpected.WithErr(err).WithCtx(ctx)
	}

	return nil
}

func (p ProjectLowerCommissionDB) GetByID(
	ctx context.Context,
	id uint64,
) (_ model.ProjectLowerCommission, err error) {
	ctx, span := dog.CreateSpan(ctx, "ProjectLowerCommissionDB_GetByID")
	defer span.End()

	projectCommission := new(model.ProjectLowerCommission)

	if err = p.db.WithContext(ctx).Where("id = ?", id).
		First(projectCommission).Preload("ProjectOption").Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return model.ProjectLowerCommission{}, goerr.ErrProjectCommissionNotFound.WithErr(err).WithCtx(ctx)
		}

		return model.ProjectLowerCommission{}, goerr.ErrDbUnexpected.WithErr(err).WithCtx(ctx)
	}

	return *projectCommission, nil
}

func (p ProjectLowerCommissionDB) UpdateExpiracy(
	ctx context.Context,
	id uint64,
	startDate, endDate *time.Time,
) error {
	ctx, span := dog.CreateSpan(ctx, "ProjectLowerCommissionDB_UpdateExpiracy")
	defer span.End()

	query := p.db.WithContext(ctx).Model(model.ProjectLowerCommission{}).Where("id = ?", id)

	if startDate != nil {
		query.Update("start_date", startDate)
	}

	query.Update("end_date", endDate)

	if err := query.Error; err != nil {
		if errors.Is(err, gorm.ErrInvalidTransaction) {
			return goerr.ErrCommissionDateRangeOverlapping.WithCtx(ctx)
		}

		return goerr.ErrDbUnexpected.WithErr(err).WithCtx(ctx)
	}

	if query.RowsAffected == 0 {
		return goerr.ErrProjectCommissionNotFound.WithCtx(ctx)
	}

	return nil
}

func (p ProjectLowerCommissionDB) GetCommissionsByOptionID(
	ctx context.Context,
	optionID uint64,
	transactionCreatedAt time.Time,
) (_ model.CommissionFinder[model.ProjectLowerCommission], err error) {
	ctx, span := dog.CreateSpan(ctx, "ProjectLowerCommissionDB_GetCommissionsByOptionID")
	defer span.End()

	projectCommissions := new(model.ProjectLowerCommissions)

	if err = p.db.WithContext(ctx).
		Where("project_option_id = ?", optionID).
		Where("start_date <= ?", transactionCreatedAt).
		Order("id desc").
		Find(projectCommissions).
		Error; err != nil {
		return nil, goerr.ErrDbUnexpected.WithErr(err).WithCtx(ctx)
	}

	return *projectCommissions, nil
}
