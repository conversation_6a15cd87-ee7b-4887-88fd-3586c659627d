// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.5
// 	protoc        v5.29.3
// source: inner/processing/grpc/splitting.proto

package grpc

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type CalculatePaymentSplitTaxRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	ProjectId     *uint64                `protobuf:"varint,1,opt,name=project_id,json=projectId" json:"project_id,omitempty"`
	Amount        *float64               `protobuf:"fixed64,2,opt,name=amount" json:"amount,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CalculatePaymentSplitTaxRequest) Reset() {
	*x = CalculatePaymentSplitTaxRequest{}
	mi := &file_inner_processing_grpc_splitting_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CalculatePaymentSplitTaxRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CalculatePaymentSplitTaxRequest) ProtoMessage() {}

func (x *CalculatePaymentSplitTaxRequest) ProtoReflect() protoreflect.Message {
	mi := &file_inner_processing_grpc_splitting_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CalculatePaymentSplitTaxRequest.ProtoReflect.Descriptor instead.
func (*CalculatePaymentSplitTaxRequest) Descriptor() ([]byte, []int) {
	return file_inner_processing_grpc_splitting_proto_rawDescGZIP(), []int{0}
}

func (x *CalculatePaymentSplitTaxRequest) GetProjectId() uint64 {
	if x != nil && x.ProjectId != nil {
		return *x.ProjectId
	}
	return 0
}

func (x *CalculatePaymentSplitTaxRequest) GetAmount() float64 {
	if x != nil && x.Amount != nil {
		return *x.Amount
	}
	return 0
}

type CalculatePaymentSplitTaxResponse struct {
	state           protoimpl.MessageState `protogen:"open.v1"`
	PaymentSplitTax *CommissionV1          `protobuf:"bytes,2,opt,name=payment_split_tax,json=paymentSplitTax" json:"payment_split_tax,omitempty"`
	unknownFields   protoimpl.UnknownFields
	sizeCache       protoimpl.SizeCache
}

func (x *CalculatePaymentSplitTaxResponse) Reset() {
	*x = CalculatePaymentSplitTaxResponse{}
	mi := &file_inner_processing_grpc_splitting_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CalculatePaymentSplitTaxResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CalculatePaymentSplitTaxResponse) ProtoMessage() {}

func (x *CalculatePaymentSplitTaxResponse) ProtoReflect() protoreflect.Message {
	mi := &file_inner_processing_grpc_splitting_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CalculatePaymentSplitTaxResponse.ProtoReflect.Descriptor instead.
func (*CalculatePaymentSplitTaxResponse) Descriptor() ([]byte, []int) {
	return file_inner_processing_grpc_splitting_proto_rawDescGZIP(), []int{1}
}

func (x *CalculatePaymentSplitTaxResponse) GetPaymentSplitTax() *CommissionV1 {
	if x != nil {
		return x.PaymentSplitTax
	}
	return nil
}

var File_inner_processing_grpc_splitting_proto protoreflect.FileDescriptor

var file_inner_processing_grpc_splitting_proto_rawDesc = string([]byte{
	0x0a, 0x25, 0x69, 0x6e, 0x6e, 0x65, 0x72, 0x2f, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69,
	0x6e, 0x67, 0x2f, 0x67, 0x72, 0x70, 0x63, 0x2f, 0x73, 0x70, 0x6c, 0x69, 0x74, 0x74, 0x69, 0x6e,
	0x67, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x1e, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73,
	0x69, 0x6e, 0x67, 0x2e, 0x73, 0x70, 0x6c, 0x69, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x2e, 0x73, 0x70,
	0x6c, 0x69, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x1a, 0x26, 0x69, 0x6e, 0x6e, 0x65, 0x72, 0x2f, 0x70,
	0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x2f, 0x67, 0x72, 0x70, 0x63, 0x2f, 0x63,
	0x6f, 0x6d, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22,
	0x58, 0x0a, 0x1f, 0x43, 0x61, 0x6c, 0x63, 0x75, 0x6c, 0x61, 0x74, 0x65, 0x50, 0x61, 0x79, 0x6d,
	0x65, 0x6e, 0x74, 0x53, 0x70, 0x6c, 0x69, 0x74, 0x54, 0x61, 0x78, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x12, 0x1d, 0x0a, 0x0a, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x5f, 0x69, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x09, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x49,
	0x64, 0x12, 0x16, 0x0a, 0x06, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x01, 0x52, 0x06, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x22, 0x7e, 0x0a, 0x20, 0x43, 0x61, 0x6c,
	0x63, 0x75, 0x6c, 0x61, 0x74, 0x65, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x53, 0x70, 0x6c,
	0x69, 0x74, 0x54, 0x61, 0x78, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x5a, 0x0a,
	0x11, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x73, 0x70, 0x6c, 0x69, 0x74, 0x5f, 0x74,
	0x61, 0x78, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2e, 0x2e, 0x70, 0x72, 0x6f, 0x63, 0x65,
	0x73, 0x73, 0x69, 0x6e, 0x67, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e,
	0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x2e, 0x43, 0x6f, 0x6d, 0x6d,
	0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x56, 0x31, 0x52, 0x0f, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e,
	0x74, 0x53, 0x70, 0x6c, 0x69, 0x74, 0x54, 0x61, 0x78, 0x32, 0xad, 0x01, 0x0a, 0x09, 0x53, 0x70,
	0x6c, 0x69, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x12, 0x9f, 0x01, 0x0a, 0x18, 0x43, 0x61, 0x6c, 0x63,
	0x75, 0x6c, 0x61, 0x74, 0x65, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x53, 0x70, 0x6c, 0x69,
	0x74, 0x54, 0x61, 0x78, 0x12, 0x3f, 0x2e, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e,
	0x67, 0x2e, 0x73, 0x70, 0x6c, 0x69, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x2e, 0x73, 0x70, 0x6c, 0x69,
	0x74, 0x74, 0x69, 0x6e, 0x67, 0x2e, 0x43, 0x61, 0x6c, 0x63, 0x75, 0x6c, 0x61, 0x74, 0x65, 0x50,
	0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x53, 0x70, 0x6c, 0x69, 0x74, 0x54, 0x61, 0x78, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x40, 0x2e, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69,
	0x6e, 0x67, 0x2e, 0x73, 0x70, 0x6c, 0x69, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x2e, 0x73, 0x70, 0x6c,
	0x69, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x2e, 0x43, 0x61, 0x6c, 0x63, 0x75, 0x6c, 0x61, 0x74, 0x65,
	0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x53, 0x70, 0x6c, 0x69, 0x74, 0x54, 0x61, 0x78, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x42, 0x2d, 0x5a, 0x2b, 0x67, 0x69, 0x74,
	0x2e, 0x6c, 0x6f, 0x63, 0x61, 0x6c, 0x2f, 0x73, 0x65, 0x6e, 0x73, 0x69, 0x74, 0x69, 0x76, 0x65,
	0x2f, 0x69, 0x6e, 0x6e, 0x65, 0x72, 0x70, 0x62, 0x2f, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73,
	0x69, 0x6e, 0x67, 0x2f, 0x67, 0x72, 0x70, 0x63, 0x62, 0x08, 0x65, 0x64, 0x69, 0x74, 0x69, 0x6f,
	0x6e, 0x73, 0x70, 0xe8, 0x07,
})

var (
	file_inner_processing_grpc_splitting_proto_rawDescOnce sync.Once
	file_inner_processing_grpc_splitting_proto_rawDescData []byte
)

func file_inner_processing_grpc_splitting_proto_rawDescGZIP() []byte {
	file_inner_processing_grpc_splitting_proto_rawDescOnce.Do(func() {
		file_inner_processing_grpc_splitting_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_inner_processing_grpc_splitting_proto_rawDesc), len(file_inner_processing_grpc_splitting_proto_rawDesc)))
	})
	return file_inner_processing_grpc_splitting_proto_rawDescData
}

var file_inner_processing_grpc_splitting_proto_msgTypes = make([]protoimpl.MessageInfo, 2)
var file_inner_processing_grpc_splitting_proto_goTypes = []any{
	(*CalculatePaymentSplitTaxRequest)(nil),  // 0: processing.splitting.splitting.CalculatePaymentSplitTaxRequest
	(*CalculatePaymentSplitTaxResponse)(nil), // 1: processing.splitting.splitting.CalculatePaymentSplitTaxResponse
	(*CommissionV1)(nil),                     // 2: processing.commission.commission.CommissionV1
}
var file_inner_processing_grpc_splitting_proto_depIdxs = []int32{
	2, // 0: processing.splitting.splitting.CalculatePaymentSplitTaxResponse.payment_split_tax:type_name -> processing.commission.commission.CommissionV1
	0, // 1: processing.splitting.splitting.Splitting.CalculatePaymentSplitTax:input_type -> processing.splitting.splitting.CalculatePaymentSplitTaxRequest
	1, // 2: processing.splitting.splitting.Splitting.CalculatePaymentSplitTax:output_type -> processing.splitting.splitting.CalculatePaymentSplitTaxResponse
	2, // [2:3] is the sub-list for method output_type
	1, // [1:2] is the sub-list for method input_type
	1, // [1:1] is the sub-list for extension type_name
	1, // [1:1] is the sub-list for extension extendee
	0, // [0:1] is the sub-list for field type_name
}

func init() { file_inner_processing_grpc_splitting_proto_init() }
func file_inner_processing_grpc_splitting_proto_init() {
	if File_inner_processing_grpc_splitting_proto != nil {
		return
	}
	file_inner_processing_grpc_commission_proto_init()
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_inner_processing_grpc_splitting_proto_rawDesc), len(file_inner_processing_grpc_splitting_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   2,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_inner_processing_grpc_splitting_proto_goTypes,
		DependencyIndexes: file_inner_processing_grpc_splitting_proto_depIdxs,
		MessageInfos:      file_inner_processing_grpc_splitting_proto_msgTypes,
	}.Build()
	File_inner_processing_grpc_splitting_proto = out.File
	file_inner_processing_grpc_splitting_proto_goTypes = nil
	file_inner_processing_grpc_splitting_proto_depIdxs = nil
}
