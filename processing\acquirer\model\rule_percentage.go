package model

import (
	"git.local/sensitive/innerpb/processing/goerr"
	"math/rand"
)

const rulePercentageTable = "acquirer.rule_percentage"

type RulePercentage struct {
	TimestampMixin `json:"-"`
	ID             uint64   `gorm:"id" json:"-"`
	RuleID         uint64   `gorm:"rule_id" json:"-"`
	AcquirerID     uint64   `gorm:"acquirer_id" json:"acquirer_id"`
	Acquirer       Acquirer `gorm:"foreignKey:AcquirerID" json:"acquirer"`
	Percentage     int      `gorm:"percentage" json:"percentage"`
}

func (rp RulePercentage) TableName() string {
	return rulePercentageTable
}

type RulePercentages []*RulePercentage

func (rps RulePercentages) TableName() string {
	return rulePercentageTable
}

func (rps RulePercentages) GetAcquirerIDByPercentage() (*uint64, error) {
	sum := 0
	for _, percentage := range rps {
		sum += percentage.Percentage
	}

	randomValue := rand.Intn(sum)

	for _, percentage := range rps {
		if randomValue < percentage.Percentage {
			acquirerID := percentage.AcquirerID
			return &acquirerID, nil
		}

		randomValue -= percentage.Percentage
	}

	return nil, goerr.ErrAcquirerPercentageNotFound
}

// FilterByAcquirerIDs фильтрует RulePercentages по заданным acquirerIDs,
// возвращает новый срез RulePercentages, оставляя исходный срез неизменным.
func (rps RulePercentages) FilterByAcquirerIDs(acquirerIDs []uint64) RulePercentages {
	idMap := make(map[uint64]bool, len(acquirerIDs))
	for _, id := range acquirerIDs {
		idMap[id] = true
	}

	filteredPercentages := make(RulePercentages, 0, len(rps))

	for _, percentage := range rps {
		if idMap[percentage.AcquirerID] {
			filteredPercentages = append(filteredPercentages, percentage)
		}
	}

	return filteredPercentages
}
