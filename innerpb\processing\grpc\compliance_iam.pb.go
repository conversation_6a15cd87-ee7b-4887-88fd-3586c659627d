// Code generated by protoc-gen-go-mvp. DO NOT EDIT.
// versions:
// - protoc-gen-go-mvp v2.0.1
// created by mvp authors

package grpc

import (
	context "context"
	grpc "google.golang.org/grpc"
	metadata "google.golang.org/grpc/metadata"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
)

func NewIamComplianceServer(
	srv ComplianceServer,
) ComplianceServer {
	return &iamComplianceServer{
		srv: srv,
	}
}

var _ ComplianceServer = (*iamComplianceServer)(nil)

type iamComplianceServer struct {
	UnimplementedComplianceServer

	srv ComplianceServer
}

func (s *iamComplianceServer) UpdateSanctionFinanciersList(
	ctx context.Context,
	req *emptypb.Empty,
) (
	*emptypb.Empty,
	error,
) {
	return s.srv.UpdateSanctionFinanciersList(ctx, req)
}

func (s *iamComplianceServer) UpdateSanctionInvolvedList(
	ctx context.Context,
	req *emptypb.Empty,
) (
	*emptypb.Empty,
	error,
) {
	return s.srv.UpdateSanctionInvolvedList(ctx, req)
}

func (s *iamComplianceServer) UpdateSanctionUNSCList(
	ctx context.Context,
	req *emptypb.Empty,
) (
	*emptypb.Empty,
	error,
) {
	return s.srv.UpdateSanctionUNSCList(ctx, req)
}

func (s *iamComplianceServer) UpdateSanctionWMDList(
	ctx context.Context,
	req *emptypb.Empty,
) (
	*emptypb.Empty,
	error,
) {
	return s.srv.UpdateSanctionWMDList(ctx, req)
}

func NewIamComplianceClient(
	client ComplianceClient,
) ComplianceClient {
	return &iamComplianceClient{
		client: client,
	}
}

type iamComplianceClient struct {
	client ComplianceClient
}

func (s *iamComplianceClient) UpdateSanctionFinanciersList(
	ctx context.Context,
	req *emptypb.Empty,
	opts ...grpc.CallOption,
) (
	*emptypb.Empty,
	error,
) {
	md, _ := metadata.FromIncomingContext(ctx)
	return s.client.UpdateSanctionFinanciersList(metadata.NewOutgoingContext(ctx, md), req)
}

func (s *iamComplianceClient) UpdateSanctionInvolvedList(
	ctx context.Context,
	req *emptypb.Empty,
	opts ...grpc.CallOption,
) (
	*emptypb.Empty,
	error,
) {
	md, _ := metadata.FromIncomingContext(ctx)
	return s.client.UpdateSanctionInvolvedList(metadata.NewOutgoingContext(ctx, md), req)
}

func (s *iamComplianceClient) UpdateSanctionUNSCList(
	ctx context.Context,
	req *emptypb.Empty,
	opts ...grpc.CallOption,
) (
	*emptypb.Empty,
	error,
) {
	md, _ := metadata.FromIncomingContext(ctx)
	return s.client.UpdateSanctionUNSCList(metadata.NewOutgoingContext(ctx, md), req)
}

func (s *iamComplianceClient) UpdateSanctionWMDList(
	ctx context.Context,
	req *emptypb.Empty,
	opts ...grpc.CallOption,
) (
	*emptypb.Empty,
	error,
) {
	md, _ := metadata.FromIncomingContext(ctx)
	return s.client.UpdateSanctionWMDList(metadata.NewOutgoingContext(ctx, md), req)
}
