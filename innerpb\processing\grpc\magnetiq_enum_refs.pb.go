// Code generated by protoc-gen-go-mvp. DO NOT EDIT.
// versions:
// - protoc-gen-go-mvp v2.0.1
// created by mvp authors

package grpc

func (x EnumMagnetiqStatus) Code() string {
	switch x {
	default:
		return ""
	}
}

func (x EnumMagnetiqStatus) Name() string {
	switch x {
	case EnumMagnetiqStatus_MagnetiqStatusRequested:
		return "Requested"
	case EnumMagnetiqStatus_MagnetiqStatusDeclined:
		return "Declined"
	case EnumMagnetiqStatus_MagnetiqStatusApproved:
		return "Approved"
	case EnumMagnetiqStatus_MagnetiqStatusUnapproved:
		return "Unapproved"
	case EnumMagnetiqStatus_MagnetiqStatusCancelled:
		return "Cancelled"
	case EnumMagnetiqStatus_MagnetiqStatusDeposited:
		return "Deposited"
	case EnumMagnetiqStatus_MagnetiqStatusProcessed:
		return "Processed"
	case EnumMagnetiqStatus_MagnetiqStatusReversed:
		return "Reversed"
	default:
		return "undefined"
	}
}

func (x EnumMagnetiqStatus) TransactionStatus() EnumTransactionStatus {
	switch x {
	case EnumMagnetiqStatus_MagnetiqStatusRequested:
		return EnumTransactionStatus_TransactionStatusProcessed
	case EnumMagnetiqStatus_MagnetiqStatusDeclined:
		return EnumTransactionStatus_TransactionStatusFailed
	case EnumMagnetiqStatus_MagnetiqStatusApproved:
		return EnumTransactionStatus_TransactionStatusAuthorized
	case EnumMagnetiqStatus_MagnetiqStatusUnapproved:
		return EnumTransactionStatus_TransactionStatusFailed
	case EnumMagnetiqStatus_MagnetiqStatusCancelled:
		return EnumTransactionStatus_TransactionStatusCanceled
	case EnumMagnetiqStatus_MagnetiqStatusDeposited:
		return EnumTransactionStatus_TransactionStatusSuccess
	case EnumMagnetiqStatus_MagnetiqStatusProcessed:
		return EnumTransactionStatus_TransactionStatusSuccess
	case EnumMagnetiqStatus_MagnetiqStatusReversed:
		return EnumTransactionStatus_TransactionStatusRefund
	default:
		return EnumTransactionStatus_TransactionStatusHolded
	}
}

// Created reference to EnumMagnetiqStatus

//	|	EnumMagnetiqStatus                         	|	Code	|	Name        	|	TransactionStatus                                	|
//	|	EnumMagnetiqStatus_MagnetiqStatusRequested 	|	""  	|	"Requested" 	|	EnumTransactionStatus_TransactionStatusProcessed 	|
//	|	EnumMagnetiqStatus_MagnetiqStatusDeclined  	|	""  	|	"Declined"  	|	EnumTransactionStatus_TransactionStatusFailed    	|
//	|	EnumMagnetiqStatus_MagnetiqStatusApproved  	|	""  	|	"Approved"  	|	EnumTransactionStatus_TransactionStatusAuthorized	|
//	|	EnumMagnetiqStatus_MagnetiqStatusUnapproved	|	""  	|	"Unapproved"	|	EnumTransactionStatus_TransactionStatusFailed    	|
//	|	EnumMagnetiqStatus_MagnetiqStatusCancelled 	|	""  	|	"Cancelled" 	|	EnumTransactionStatus_TransactionStatusCanceled  	|
//	|	EnumMagnetiqStatus_MagnetiqStatusDeposited 	|	""  	|	"Deposited" 	|	EnumTransactionStatus_TransactionStatusSuccess   	|
//	|	EnumMagnetiqStatus_MagnetiqStatusProcessed 	|	""  	|	"Processed" 	|	EnumTransactionStatus_TransactionStatusSuccess   	|
//	|	EnumMagnetiqStatus_MagnetiqStatusReversed  	|	""  	|	"Reversed"  	|	EnumTransactionStatus_TransactionStatusRefund    	|

var SliceEnumMagnetiqStatusRefs *sliceEnumMagnetiqStatusRefs

type sliceEnumMagnetiqStatusRefs struct{}

func (*sliceEnumMagnetiqStatusRefs) Code(slice ...EnumMagnetiqStatus) []string {
	var result []string
	for _, val := range slice {
		result = append(result, val.Code())
	}

	return result
}

func (*sliceEnumMagnetiqStatusRefs) Name(slice ...EnumMagnetiqStatus) []string {
	var result []string
	for _, val := range slice {
		result = append(result, val.Name())
	}

	return result
}

func (*sliceEnumMagnetiqStatusRefs) TransactionStatus(slice ...EnumMagnetiqStatus) []EnumTransactionStatus {
	var result []EnumTransactionStatus
	for _, val := range slice {
		result = append(result, val.TransactionStatus())
	}

	return result
}

func (x MagnetiqFaultErrCode) IntegrationError() IntegrationError {
	switch x {
	case MagnetiqFaultErrCode_MagnetiqFaultServerErr:
		return IntegrationError_UnavailableAcquirer
	case MagnetiqFaultErrCode_MagnetiqFaultLockTimeoutErr:
		return IntegrationError_UnavailableAcquirer
	case MagnetiqFaultErrCode_MagnetiqFaultEntityNotFoundErr:
		return IntegrationError_TransactionDeclinedByAcquirer
	case MagnetiqFaultErrCode_MagnetiqFaultBadRequestErr:
		return IntegrationError_TransactionDeclinedByAcquirer
	case MagnetiqFaultErrCode_MagnetiqFaultIntegrityViolationErr:
		return IntegrationError_TransactionDeclinedByAcquirer
	case MagnetiqFaultErrCode_MagnetiqFaultOrderIdMustBeUniqueErr:
		return IntegrationError_TransactionDeclinedByAcquirer
	case MagnetiqFaultErrCode_MagnetiqFaultTokenAlreadyRegisteredErr:
		return IntegrationError_TransactionDeclinedByAcquirer
	case MagnetiqFaultErrCode_MagnetiqFaultTokenNotFoundErr:
		return IntegrationError_TransactionDeclinedByAcquirer
	case MagnetiqFaultErrCode_MagnetiqFaultDirectoryServiceNotFoundErr:
		return IntegrationError_TransactionDeclinedByAcquirer
	case MagnetiqFaultErrCode_MagnetiqFaultKeyNotFoundErr:
		return IntegrationError_TransactionDeclinedByAcquirer
	case MagnetiqFaultErrCode_MagnetiqFaultRecurringNotFoundErr:
		return IntegrationError_TransactionDeclinedByAcquirer
	case MagnetiqFaultErrCode_MagnetiqFaultPaymentStateFlowViolationErr:
		return IntegrationError_TransactionDeclinedByAcquirer
	case MagnetiqFaultErrCode_MagnetiqFaultAuthNon3dPaymentErr:
		return IntegrationError_TransactionDeclinedByAcquirer
	case MagnetiqFaultErrCode_MagnetiqFaultAuthenticatePaymentErr:
		return IntegrationError_TransactionDeclinedByAcquirer
	case MagnetiqFaultErrCode_MagnetiqFaultAuthenticateNon3dPaymentErr:
		return IntegrationError_TransactionDeclinedByAcquirer
	case MagnetiqFaultErrCode_MagnetiqFaultDepositPaymentErr:
		return IntegrationError_TransactionDeclinedByAcquirer
	case MagnetiqFaultErrCode_MagnetiqFaultCancelPaymentErr:
		return IntegrationError_TransactionDeclinedByAcquirer
	case MagnetiqFaultErrCode_MagnetiqFaultCardNameRequiredErr:
		return IntegrationError_TransactionDeclinedByAcquirer
	case MagnetiqFaultErrCode_MagnetiqFaultCardNumberRequiredErr:
		return IntegrationError_TransactionDeclinedByAcquirer
	case MagnetiqFaultErrCode_MagnetiqFaultCardExpiryRequiredErr:
		return IntegrationError_TransactionDeclinedByAcquirer
	case MagnetiqFaultErrCode_MagnetiqFaultInvalidCardExpiryDateErr:
		return IntegrationError_IncorrectCardExpDate
	case MagnetiqFaultErrCode_MagnetiqFaultInvalidCardNumberErr:
		return IntegrationError_IncorrectCardNumber
	case MagnetiqFaultErrCode_MagnetiqFaultCouldNotReversePaymentErr:
		return IntegrationError_TransactionDeclinedByAcquirer
	case MagnetiqFaultErrCode_MagnetiqFaultMerchantInterfaceClosedErr:
		return IntegrationError_TransactionDeclinedByAcquirer
	case MagnetiqFaultErrCode_MagnetiqFaultInvalidCurrencyErr:
		return IntegrationError_TransactionDeclinedByAcquirer
	case MagnetiqFaultErrCode_MagnetiqFaultOriginalCreditNotAllowedErr:
		return IntegrationError_PaymentForbiddenForMerchant
	case MagnetiqFaultErrCode_MagnetiqFaultCSCRequiredErr:
		return IntegrationError_TransactionDeclinedByAcquirer
	case MagnetiqFaultErrCode_MagnetiqFaultCSCInvalidErr:
		return IntegrationError_IncorrectCVVCVC
	case MagnetiqFaultErrCode_MagnetiqFaultPaymentModeRequiredErr:
		return IntegrationError_TransactionDeclinedByAcquirer
	case MagnetiqFaultErrCode_MagnetiqFaultPaymentOrderRequiredErr:
		return IntegrationError_TransactionDeclinedByAcquirer
	case MagnetiqFaultErrCode_MagnetiqFaultPaymentOrderIDRequiredErr:
		return IntegrationError_TransactionDeclinedByAcquirer
	case MagnetiqFaultErrCode_MagnetiqFaultPaymentOrderAmountRequiredErr:
		return IntegrationError_TransactionDeclinedByAcquirer
	case MagnetiqFaultErrCode_MagnetiqFaultPaymentOrderCurrencyRequiredErr:
		return IntegrationError_TransactionDeclinedByAcquirer
	case MagnetiqFaultErrCode_MagnetiqFaultPaymentOrderDescriptionRequiredErr:
		return IntegrationError_TransactionDeclinedByAcquirer
	case MagnetiqFaultErrCode_MagnetiqFaultD3DACSParResRequiredErr:
		return IntegrationError_TransactionDeclinedByAcquirer
	case MagnetiqFaultErrCode_MagnetiqFaultRecurringExpiredErr:
		return IntegrationError_TransactionDeclinedByAcquirer
	case MagnetiqFaultErrCode_MagnetiqFaultTooFrequentRecurringErr:
		return IntegrationError_ExceedsTransactionFrequencyLimit
	case MagnetiqFaultErrCode_MagnetiqFaultRecurringEndDateRequiredErr:
		return IntegrationError_TransactionDeclinedByAcquirer
	case MagnetiqFaultErrCode_MagnetiqFaultRecurringFrequencyRequiredErr:
		return IntegrationError_TransactionDeclinedByAcquirer
	case MagnetiqFaultErrCode_MagnetiqFaultRecurringEndDateIncorrectErr:
		return IntegrationError_TransactionDeclinedByAcquirer
	case MagnetiqFaultErrCode_MagnetiqFaultRecurringEndDateFutureErr:
		return IntegrationError_TransactionDeclinedByAcquirer
	case MagnetiqFaultErrCode_MagnetiqFaultRecurringFrequencyGreaterThanZeroErr:
		return IntegrationError_TransactionDeclinedByAcquirer
	case MagnetiqFaultErrCode_MagnetiqFaultRecurringIDRequiredErr:
		return IntegrationError_TransactionDeclinedByAcquirer
	case MagnetiqFaultErrCode_MagnetiqFaultRemoteAddressRequiredErr:
		return IntegrationError_TransactionDeclinedByAcquirer
	case MagnetiqFaultErrCode_MagnetiqFaultInvalidSignatureErr:
		return IntegrationError_TransactionDeclinedByAcquirer
	case MagnetiqFaultErrCode_MagnetiqFaultRequestRequiredErr:
		return IntegrationError_TransactionDeclinedByAcquirer
	case MagnetiqFaultErrCode_MagnetiqFaultInterfaceRequiredErr:
		return IntegrationError_TransactionDeclinedByAcquirer
	case MagnetiqFaultErrCode_MagnetiqFaultKeyIndexRequiredErr:
		return IntegrationError_TransactionDeclinedByAcquirer
	case MagnetiqFaultErrCode_MagnetiqFaultKeyRequiredErr:
		return IntegrationError_TransactionDeclinedByAcquirer
	case MagnetiqFaultErrCode_MagnetiqFaultDataRequiredErr:
		return IntegrationError_TransactionDeclinedByAcquirer
	case MagnetiqFaultErrCode_MagnetiqFaultSignatureRequiredErr:
		return IntegrationError_TransactionDeclinedByAcquirer
	case MagnetiqFaultErrCode_MagnetiqFaultXMLDeserializationFailedErr:
		return IntegrationError_TransactionDeclinedByAcquirer
	case MagnetiqFaultErrCode_MagnetiqFaultXMLSerializationFailedErr:
		return IntegrationError_TransactionDeclinedByAcquirer
	case MagnetiqFaultErrCode_MagnetiqFaultDataDecryptionFailedErr:
		return IntegrationError_TransactionDeclinedByAcquirer
	case MagnetiqFaultErrCode_MagnetiqFaultDataEncryptionFailedErr:
		return IntegrationError_TransactionDeclinedByAcquirer
	case MagnetiqFaultErrCode_MagnetiqFaultSignatureVerificationFailedErr:
		return IntegrationError_TransactionDeclinedByAcquirer
	case MagnetiqFaultErrCode_MagnetiqFaultSigningFailedErr:
		return IntegrationError_TransactionDeclinedByAcquirer
	case MagnetiqFaultErrCode_MagnetiqFaultPaymentNotFoundErr:
		return IntegrationError_TransactionDeclinedByAcquirer
	case MagnetiqFaultErrCode_MagnetiqFaultInvalidPaymentModeAuthenticationErr:
		return IntegrationError_TransactionDeclinedByAcquirer
	case MagnetiqFaultErrCode_MagnetiqFaultPaymentRequiredErr:
		return IntegrationError_TransactionDeclinedByAcquirer
	case MagnetiqFaultErrCode_MagnetiqFaultInvalidPaymentModeCreationErr:
		return IntegrationError_TransactionDeclinedByAcquirer
	case MagnetiqFaultErrCode_MagnetiqFaultTokensDisabledErr:
		return IntegrationError_TransactionDeclinedByAcquirer
	case MagnetiqFaultErrCode_MagnetiqFaultInvalidTokenTypeErr:
		return IntegrationError_TransactionDeclinedByAcquirer
	case MagnetiqFaultErrCode_MagnetiqFaultUnableDecryptTokenErr:
		return IntegrationError_TransactionDeclinedByAcquirer
	case MagnetiqFaultErrCode_MagnetiqFaultIncorrectAmountErr:
		return IntegrationError_TransactionDeclinedByAcquirer
	case MagnetiqFaultErrCode_MagnetiqFaultIncorrectCurrencyErr:
		return IntegrationError_TransactionDeclinedByAcquirer
	case MagnetiqFaultErrCode_MagnetiqFaultIncorrectCryptogramErr:
		return IntegrationError_TransactionDeclinedByAcquirer
	case MagnetiqFaultErrCode_MagnetiqFaultIncorrectTokenModeErr:
		return IntegrationError_TransactionDeclinedByAcquirer
	default:
		return IntegrationError_UndefinedError
	}
}

func (x MagnetiqFaultErrCode) Message() string {
	switch x {
	case MagnetiqFaultErrCode_MagnetiqFaultServerErr:
		return "Internal server error"
	case MagnetiqFaultErrCode_MagnetiqFaultLockTimeoutErr:
		return "Lock timeout"
	case MagnetiqFaultErrCode_MagnetiqFaultEntityNotFoundErr:
		return "Entity not found"
	case MagnetiqFaultErrCode_MagnetiqFaultBadRequestErr:
		return "Bad request"
	case MagnetiqFaultErrCode_MagnetiqFaultIntegrityViolationErr:
		return "Integrity violation"
	case MagnetiqFaultErrCode_MagnetiqFaultOrderIdMustBeUniqueErr:
		return "Order ID must be unique"
	case MagnetiqFaultErrCode_MagnetiqFaultTokenAlreadyRegisteredErr:
		return "Token is already registered"
	case MagnetiqFaultErrCode_MagnetiqFaultTokenNotFoundErr:
		return "Token not found"
	case MagnetiqFaultErrCode_MagnetiqFaultDirectoryServiceNotFoundErr:
		return "Directory service not found"
	case MagnetiqFaultErrCode_MagnetiqFaultKeyNotFoundErr:
		return "Key not found. Either INTERFACE or KEY_INDEX is incorrect."
	case MagnetiqFaultErrCode_MagnetiqFaultRecurringNotFoundErr:
		return "Recurring not found"
	case MagnetiqFaultErrCode_MagnetiqFaultPaymentStateFlowViolationErr:
		return "Could not initiate payment, state flow violated"
	case MagnetiqFaultErrCode_MagnetiqFaultAuthNon3dPaymentErr:
		return "Could not initialize authentication of non-3D payment"
	case MagnetiqFaultErrCode_MagnetiqFaultAuthenticatePaymentErr:
		return "Could not authenticate payment, state flow violated"
	case MagnetiqFaultErrCode_MagnetiqFaultAuthenticateNon3dPaymentErr:
		return "Could not authenticate non-3D payment"
	case MagnetiqFaultErrCode_MagnetiqFaultDepositPaymentErr:
		return "Could not deposit payment, state flow violated"
	case MagnetiqFaultErrCode_MagnetiqFaultCancelPaymentErr:
		return "Could not cancel payment, state flow violated"
	case MagnetiqFaultErrCode_MagnetiqFaultCardNameRequiredErr:
		return "Card name is required"
	case MagnetiqFaultErrCode_MagnetiqFaultCardNumberRequiredErr:
		return "Card number is required"
	case MagnetiqFaultErrCode_MagnetiqFaultCardExpiryRequiredErr:
		return "Card expiry is required"
	case MagnetiqFaultErrCode_MagnetiqFaultInvalidCardExpiryDateErr:
		return "Invalid card expiry date"
	case MagnetiqFaultErrCode_MagnetiqFaultInvalidCardNumberErr:
		return "Invalid card number"
	case MagnetiqFaultErrCode_MagnetiqFaultCouldNotReversePaymentErr:
		return "Could not reverse payment, state flow violated"
	case MagnetiqFaultErrCode_MagnetiqFaultMerchantInterfaceClosedErr:
		return "Merchant interface is closed"
	case MagnetiqFaultErrCode_MagnetiqFaultInvalidCurrencyErr:
		return "Invalid currency"
	case MagnetiqFaultErrCode_MagnetiqFaultOriginalCreditNotAllowedErr:
		return "Original credit operations are not allowed on this interface"
	case MagnetiqFaultErrCode_MagnetiqFaultCSCRequiredErr:
		return "CSC is required"
	case MagnetiqFaultErrCode_MagnetiqFaultCSCInvalidErr:
		return "CSC is invalid"
	case MagnetiqFaultErrCode_MagnetiqFaultPaymentModeRequiredErr:
		return "Payment.Mode is required"
	case MagnetiqFaultErrCode_MagnetiqFaultPaymentOrderRequiredErr:
		return "Payment.Order is required"
	case MagnetiqFaultErrCode_MagnetiqFaultPaymentOrderIDRequiredErr:
		return "Payment.Order.ID is required"
	case MagnetiqFaultErrCode_MagnetiqFaultPaymentOrderAmountRequiredErr:
		return "Payment.Order.Amount is required"
	case MagnetiqFaultErrCode_MagnetiqFaultPaymentOrderCurrencyRequiredErr:
		return "Payment.Order.Currency is required"
	case MagnetiqFaultErrCode_MagnetiqFaultPaymentOrderDescriptionRequiredErr:
		return "Payment.Order.Description is required"
	case MagnetiqFaultErrCode_MagnetiqFaultD3DACSParResRequiredErr:
		return "D3D.ACS.PaRes is required"
	case MagnetiqFaultErrCode_MagnetiqFaultRecurringExpiredErr:
		return "Recurring expired"
	case MagnetiqFaultErrCode_MagnetiqFaultTooFrequentRecurringErr:
		return "Too frequent recurring"
	case MagnetiqFaultErrCode_MagnetiqFaultRecurringEndDateRequiredErr:
		return "Recurring.EndDate required"
	case MagnetiqFaultErrCode_MagnetiqFaultRecurringFrequencyRequiredErr:
		return "Recurring.Frequency required"
	case MagnetiqFaultErrCode_MagnetiqFaultRecurringEndDateIncorrectErr:
		return "Recurring.EndDate is incorrect"
	case MagnetiqFaultErrCode_MagnetiqFaultRecurringEndDateFutureErr:
		return "Recurring.EndDate must be in future"
	case MagnetiqFaultErrCode_MagnetiqFaultRecurringFrequencyGreaterThanZeroErr:
		return "Recurring.Frequency must be greater than 0"
	case MagnetiqFaultErrCode_MagnetiqFaultRecurringIDRequiredErr:
		return "Recurring.ID is required"
	case MagnetiqFaultErrCode_MagnetiqFaultRemoteAddressRequiredErr:
		return "RemoteAddress is required"
	case MagnetiqFaultErrCode_MagnetiqFaultInvalidSignatureErr:
		return "Invalid signature"
	case MagnetiqFaultErrCode_MagnetiqFaultRequestRequiredErr:
		return "Request is required"
	case MagnetiqFaultErrCode_MagnetiqFaultInterfaceRequiredErr:
		return "INTERFACE is required"
	case MagnetiqFaultErrCode_MagnetiqFaultKeyIndexRequiredErr:
		return "KEYINDEX is required"
	case MagnetiqFaultErrCode_MagnetiqFaultKeyRequiredErr:
		return "KEY is required"
	case MagnetiqFaultErrCode_MagnetiqFaultDataRequiredErr:
		return "DATA is required"
	case MagnetiqFaultErrCode_MagnetiqFaultSignatureRequiredErr:
		return "SIGNATURE is required"
	case MagnetiqFaultErrCode_MagnetiqFaultXMLDeserializationFailedErr:
		return "XML deserialization failed"
	case MagnetiqFaultErrCode_MagnetiqFaultXMLSerializationFailedErr:
		return "XML serialization failed"
	case MagnetiqFaultErrCode_MagnetiqFaultDataDecryptionFailedErr:
		return "Data decryption failed"
	case MagnetiqFaultErrCode_MagnetiqFaultDataEncryptionFailedErr:
		return "Data encryption failed"
	case MagnetiqFaultErrCode_MagnetiqFaultSignatureVerificationFailedErr:
		return "Signature verification failed"
	case MagnetiqFaultErrCode_MagnetiqFaultSigningFailedErr:
		return "Signing failed"
	case MagnetiqFaultErrCode_MagnetiqFaultPaymentNotFoundErr:
		return "Payment not found"
	case MagnetiqFaultErrCode_MagnetiqFaultInvalidPaymentModeAuthenticationErr:
		return "Could not authenticate payment, invalid Payment.Mode"
	case MagnetiqFaultErrCode_MagnetiqFaultPaymentRequiredErr:
		return "Payment is required"
	case MagnetiqFaultErrCode_MagnetiqFaultInvalidPaymentModeCreationErr:
		return "Could not create payment, invalid Payment.Mode"
	case MagnetiqFaultErrCode_MagnetiqFaultTokensDisabledErr:
		return "Tokens are disabled"
	case MagnetiqFaultErrCode_MagnetiqFaultInvalidTokenTypeErr:
		return "Request holds invalid Token type"
	case MagnetiqFaultErrCode_MagnetiqFaultUnableDecryptTokenErr:
		return "System was unable to decrypt the received token"
	case MagnetiqFaultErrCode_MagnetiqFaultIncorrectAmountErr:
		return "Order Amount is incorrect"
	case MagnetiqFaultErrCode_MagnetiqFaultIncorrectCurrencyErr:
		return "Order currency is incorrect"
	case MagnetiqFaultErrCode_MagnetiqFaultIncorrectCryptogramErr:
		return "Cryptogram field is incorrect"
	case MagnetiqFaultErrCode_MagnetiqFaultIncorrectTokenModeErr:
		return "Token mode value is incorrect"
	default:
		return "undefined"
	}
}

// Created reference to MagnetiqFaultErrCode

//	|	MagnetiqFaultErrCode                                                  	|	IntegrationError                                 	|	Message                                                       	|
//	|	MagnetiqFaultErrCode_MagnetiqFaultServerErr                           	|	IntegrationError_UnavailableAcquirer             	|	"Internal server error"                                       	|
//	|	MagnetiqFaultErrCode_MagnetiqFaultLockTimeoutErr                      	|	IntegrationError_UnavailableAcquirer             	|	"Lock timeout"                                                	|
//	|	MagnetiqFaultErrCode_MagnetiqFaultEntityNotFoundErr                   	|	IntegrationError_TransactionDeclinedByAcquirer   	|	"Entity not found"                                            	|
//	|	MagnetiqFaultErrCode_MagnetiqFaultBadRequestErr                       	|	IntegrationError_TransactionDeclinedByAcquirer   	|	"Bad request"                                                 	|
//	|	MagnetiqFaultErrCode_MagnetiqFaultIntegrityViolationErr               	|	IntegrationError_TransactionDeclinedByAcquirer   	|	"Integrity violation"                                         	|
//	|	MagnetiqFaultErrCode_MagnetiqFaultOrderIdMustBeUniqueErr              	|	IntegrationError_TransactionDeclinedByAcquirer   	|	"Order ID must be unique"                                     	|
//	|	MagnetiqFaultErrCode_MagnetiqFaultTokenAlreadyRegisteredErr           	|	IntegrationError_TransactionDeclinedByAcquirer   	|	"Token is already registered"                                 	|
//	|	MagnetiqFaultErrCode_MagnetiqFaultTokenNotFoundErr                    	|	IntegrationError_TransactionDeclinedByAcquirer   	|	"Token not found"                                             	|
//	|	MagnetiqFaultErrCode_MagnetiqFaultDirectoryServiceNotFoundErr         	|	IntegrationError_TransactionDeclinedByAcquirer   	|	"Directory service not found"                                 	|
//	|	MagnetiqFaultErrCode_MagnetiqFaultKeyNotFoundErr                      	|	IntegrationError_TransactionDeclinedByAcquirer   	|	"Key not found. Either INTERFACE or KEY_INDEX is incorrect."  	|
//	|	MagnetiqFaultErrCode_MagnetiqFaultRecurringNotFoundErr                	|	IntegrationError_TransactionDeclinedByAcquirer   	|	"Recurring not found"                                         	|
//	|	MagnetiqFaultErrCode_MagnetiqFaultPaymentStateFlowViolationErr        	|	IntegrationError_TransactionDeclinedByAcquirer   	|	"Could not initiate payment, state flow violated"             	|
//	|	MagnetiqFaultErrCode_MagnetiqFaultAuthNon3dPaymentErr                 	|	IntegrationError_TransactionDeclinedByAcquirer   	|	"Could not initialize authentication of non-3D payment"       	|
//	|	MagnetiqFaultErrCode_MagnetiqFaultAuthenticatePaymentErr              	|	IntegrationError_TransactionDeclinedByAcquirer   	|	"Could not authenticate payment, state flow violated"         	|
//	|	MagnetiqFaultErrCode_MagnetiqFaultAuthenticateNon3dPaymentErr         	|	IntegrationError_TransactionDeclinedByAcquirer   	|	"Could not authenticate non-3D payment"                       	|
//	|	MagnetiqFaultErrCode_MagnetiqFaultDepositPaymentErr                   	|	IntegrationError_TransactionDeclinedByAcquirer   	|	"Could not deposit payment, state flow violated"              	|
//	|	MagnetiqFaultErrCode_MagnetiqFaultCancelPaymentErr                    	|	IntegrationError_TransactionDeclinedByAcquirer   	|	"Could not cancel payment, state flow violated"               	|
//	|	MagnetiqFaultErrCode_MagnetiqFaultCardNameRequiredErr                 	|	IntegrationError_TransactionDeclinedByAcquirer   	|	"Card name is required"                                       	|
//	|	MagnetiqFaultErrCode_MagnetiqFaultCardNumberRequiredErr               	|	IntegrationError_TransactionDeclinedByAcquirer   	|	"Card number is required"                                     	|
//	|	MagnetiqFaultErrCode_MagnetiqFaultCardExpiryRequiredErr               	|	IntegrationError_TransactionDeclinedByAcquirer   	|	"Card expiry is required"                                     	|
//	|	MagnetiqFaultErrCode_MagnetiqFaultInvalidCardExpiryDateErr            	|	IntegrationError_IncorrectCardExpDate            	|	"Invalid card expiry date"                                    	|
//	|	MagnetiqFaultErrCode_MagnetiqFaultInvalidCardNumberErr                	|	IntegrationError_IncorrectCardNumber             	|	"Invalid card number"                                         	|
//	|	MagnetiqFaultErrCode_MagnetiqFaultCouldNotReversePaymentErr           	|	IntegrationError_TransactionDeclinedByAcquirer   	|	"Could not reverse payment, state flow violated"              	|
//	|	MagnetiqFaultErrCode_MagnetiqFaultMerchantInterfaceClosedErr          	|	IntegrationError_TransactionDeclinedByAcquirer   	|	"Merchant interface is closed"                                	|
//	|	MagnetiqFaultErrCode_MagnetiqFaultInvalidCurrencyErr                  	|	IntegrationError_TransactionDeclinedByAcquirer   	|	"Invalid currency"                                            	|
//	|	MagnetiqFaultErrCode_MagnetiqFaultOriginalCreditNotAllowedErr         	|	IntegrationError_PaymentForbiddenForMerchant     	|	"Original credit operations are not allowed on this interface"	|
//	|	MagnetiqFaultErrCode_MagnetiqFaultCSCRequiredErr                      	|	IntegrationError_TransactionDeclinedByAcquirer   	|	"CSC is required"                                             	|
//	|	MagnetiqFaultErrCode_MagnetiqFaultCSCInvalidErr                       	|	IntegrationError_IncorrectCVVCVC                 	|	"CSC is invalid"                                              	|
//	|	MagnetiqFaultErrCode_MagnetiqFaultPaymentModeRequiredErr              	|	IntegrationError_TransactionDeclinedByAcquirer   	|	"Payment.Mode is required"                                    	|
//	|	MagnetiqFaultErrCode_MagnetiqFaultPaymentOrderRequiredErr             	|	IntegrationError_TransactionDeclinedByAcquirer   	|	"Payment.Order is required"                                   	|
//	|	MagnetiqFaultErrCode_MagnetiqFaultPaymentOrderIDRequiredErr           	|	IntegrationError_TransactionDeclinedByAcquirer   	|	"Payment.Order.ID is required"                                	|
//	|	MagnetiqFaultErrCode_MagnetiqFaultPaymentOrderAmountRequiredErr       	|	IntegrationError_TransactionDeclinedByAcquirer   	|	"Payment.Order.Amount is required"                            	|
//	|	MagnetiqFaultErrCode_MagnetiqFaultPaymentOrderCurrencyRequiredErr     	|	IntegrationError_TransactionDeclinedByAcquirer   	|	"Payment.Order.Currency is required"                          	|
//	|	MagnetiqFaultErrCode_MagnetiqFaultPaymentOrderDescriptionRequiredErr  	|	IntegrationError_TransactionDeclinedByAcquirer   	|	"Payment.Order.Description is required"                       	|
//	|	MagnetiqFaultErrCode_MagnetiqFaultD3DACSParResRequiredErr             	|	IntegrationError_TransactionDeclinedByAcquirer   	|	"D3D.ACS.PaRes is required"                                   	|
//	|	MagnetiqFaultErrCode_MagnetiqFaultRecurringExpiredErr                 	|	IntegrationError_TransactionDeclinedByAcquirer   	|	"Recurring expired"                                           	|
//	|	MagnetiqFaultErrCode_MagnetiqFaultTooFrequentRecurringErr             	|	IntegrationError_ExceedsTransactionFrequencyLimit	|	"Too frequent recurring"                                      	|
//	|	MagnetiqFaultErrCode_MagnetiqFaultRecurringEndDateRequiredErr         	|	IntegrationError_TransactionDeclinedByAcquirer   	|	"Recurring.EndDate required"                                  	|
//	|	MagnetiqFaultErrCode_MagnetiqFaultRecurringFrequencyRequiredErr       	|	IntegrationError_TransactionDeclinedByAcquirer   	|	"Recurring.Frequency required"                                	|
//	|	MagnetiqFaultErrCode_MagnetiqFaultRecurringEndDateIncorrectErr        	|	IntegrationError_TransactionDeclinedByAcquirer   	|	"Recurring.EndDate is incorrect"                              	|
//	|	MagnetiqFaultErrCode_MagnetiqFaultRecurringEndDateFutureErr           	|	IntegrationError_TransactionDeclinedByAcquirer   	|	"Recurring.EndDate must be in future"                         	|
//	|	MagnetiqFaultErrCode_MagnetiqFaultRecurringFrequencyGreaterThanZeroErr	|	IntegrationError_TransactionDeclinedByAcquirer   	|	"Recurring.Frequency must be greater than 0"                  	|
//	|	MagnetiqFaultErrCode_MagnetiqFaultRecurringIDRequiredErr              	|	IntegrationError_TransactionDeclinedByAcquirer   	|	"Recurring.ID is required"                                    	|
//	|	MagnetiqFaultErrCode_MagnetiqFaultRemoteAddressRequiredErr            	|	IntegrationError_TransactionDeclinedByAcquirer   	|	"RemoteAddress is required"                                   	|
//	|	MagnetiqFaultErrCode_MagnetiqFaultInvalidSignatureErr                 	|	IntegrationError_TransactionDeclinedByAcquirer   	|	"Invalid signature"                                           	|
//	|	MagnetiqFaultErrCode_MagnetiqFaultRequestRequiredErr                  	|	IntegrationError_TransactionDeclinedByAcquirer   	|	"Request is required"                                         	|
//	|	MagnetiqFaultErrCode_MagnetiqFaultInterfaceRequiredErr                	|	IntegrationError_TransactionDeclinedByAcquirer   	|	"INTERFACE is required"                                       	|
//	|	MagnetiqFaultErrCode_MagnetiqFaultKeyIndexRequiredErr                 	|	IntegrationError_TransactionDeclinedByAcquirer   	|	"KEYINDEX is required"                                        	|
//	|	MagnetiqFaultErrCode_MagnetiqFaultKeyRequiredErr                      	|	IntegrationError_TransactionDeclinedByAcquirer   	|	"KEY is required"                                             	|
//	|	MagnetiqFaultErrCode_MagnetiqFaultDataRequiredErr                     	|	IntegrationError_TransactionDeclinedByAcquirer   	|	"DATA is required"                                            	|
//	|	MagnetiqFaultErrCode_MagnetiqFaultSignatureRequiredErr                	|	IntegrationError_TransactionDeclinedByAcquirer   	|	"SIGNATURE is required"                                       	|
//	|	MagnetiqFaultErrCode_MagnetiqFaultXMLDeserializationFailedErr         	|	IntegrationError_TransactionDeclinedByAcquirer   	|	"XML deserialization failed"                                  	|
//	|	MagnetiqFaultErrCode_MagnetiqFaultXMLSerializationFailedErr           	|	IntegrationError_TransactionDeclinedByAcquirer   	|	"XML serialization failed"                                    	|
//	|	MagnetiqFaultErrCode_MagnetiqFaultDataDecryptionFailedErr             	|	IntegrationError_TransactionDeclinedByAcquirer   	|	"Data decryption failed"                                      	|
//	|	MagnetiqFaultErrCode_MagnetiqFaultDataEncryptionFailedErr             	|	IntegrationError_TransactionDeclinedByAcquirer   	|	"Data encryption failed"                                      	|
//	|	MagnetiqFaultErrCode_MagnetiqFaultSignatureVerificationFailedErr      	|	IntegrationError_TransactionDeclinedByAcquirer   	|	"Signature verification failed"                               	|
//	|	MagnetiqFaultErrCode_MagnetiqFaultSigningFailedErr                    	|	IntegrationError_TransactionDeclinedByAcquirer   	|	"Signing failed"                                              	|
//	|	MagnetiqFaultErrCode_MagnetiqFaultPaymentNotFoundErr                  	|	IntegrationError_TransactionDeclinedByAcquirer   	|	"Payment not found"                                           	|
//	|	MagnetiqFaultErrCode_MagnetiqFaultInvalidPaymentModeAuthenticationErr 	|	IntegrationError_TransactionDeclinedByAcquirer   	|	"Could not authenticate payment, invalid Payment.Mode"        	|
//	|	MagnetiqFaultErrCode_MagnetiqFaultPaymentRequiredErr                  	|	IntegrationError_TransactionDeclinedByAcquirer   	|	"Payment is required"                                         	|
//	|	MagnetiqFaultErrCode_MagnetiqFaultInvalidPaymentModeCreationErr       	|	IntegrationError_TransactionDeclinedByAcquirer   	|	"Could not create payment, invalid Payment.Mode"              	|
//	|	MagnetiqFaultErrCode_MagnetiqFaultTokensDisabledErr                   	|	IntegrationError_TransactionDeclinedByAcquirer   	|	"Tokens are disabled"                                         	|
//	|	MagnetiqFaultErrCode_MagnetiqFaultInvalidTokenTypeErr                 	|	IntegrationError_TransactionDeclinedByAcquirer   	|	"Request holds invalid Token type"                            	|
//	|	MagnetiqFaultErrCode_MagnetiqFaultUnableDecryptTokenErr               	|	IntegrationError_TransactionDeclinedByAcquirer   	|	"System was unable to decrypt the received token"             	|
//	|	MagnetiqFaultErrCode_MagnetiqFaultIncorrectAmountErr                  	|	IntegrationError_TransactionDeclinedByAcquirer   	|	"Order Amount is incorrect"                                   	|
//	|	MagnetiqFaultErrCode_MagnetiqFaultIncorrectCurrencyErr                	|	IntegrationError_TransactionDeclinedByAcquirer   	|	"Order currency is incorrect"                                 	|
//	|	MagnetiqFaultErrCode_MagnetiqFaultIncorrectCryptogramErr              	|	IntegrationError_TransactionDeclinedByAcquirer   	|	"Cryptogram field is incorrect"                               	|
//	|	MagnetiqFaultErrCode_MagnetiqFaultIncorrectTokenModeErr               	|	IntegrationError_TransactionDeclinedByAcquirer   	|	"Token mode value is incorrect"                               	|

var SliceMagnetiqFaultErrCodeRefs *sliceMagnetiqFaultErrCodeRefs

type sliceMagnetiqFaultErrCodeRefs struct{}

func (*sliceMagnetiqFaultErrCodeRefs) IntegrationError(slice ...MagnetiqFaultErrCode) []IntegrationError {
	var result []IntegrationError
	for _, val := range slice {
		result = append(result, val.IntegrationError())
	}

	return result
}

func (*sliceMagnetiqFaultErrCodeRefs) Message(slice ...MagnetiqFaultErrCode) []string {
	var result []string
	for _, val := range slice {
		result = append(result, val.Message())
	}

	return result
}

func (x EnumMagnetiqActionCode) IntegrationError() IntegrationError {
	switch x {
	case EnumMagnetiqActionCode_MagnetiqActionCodeDeclineGeneral:
		return IntegrationError_TransactionDeclinedByAcquirer
	case EnumMagnetiqActionCode_MagnetiqActionCodeDeclineExpiredCard:
		return IntegrationError_CardHasExpired
	case EnumMagnetiqActionCode_MagnetiqActionCodeDeclineSuspectedFraud:
		return IntegrationError_SuspiciousClient
	case EnumMagnetiqActionCode_MagnetiqActionCodeDeclineContactAcquirer:
		return IntegrationError_TransactionDeclinedByAcquirer
	case EnumMagnetiqActionCode_MagnetiqActionCodeDeclineRestrictedCard:
		return IntegrationError_InvalidCard
	case EnumMagnetiqActionCode_MagnetiqActionCodeDeclineCallSecurityDept:
		return IntegrationError_TransactionDeclinedByAcquirer
	case EnumMagnetiqActionCode_MagnetiqActionCodeDeclineReferToIssuer:
		return IntegrationError_TransactionDeclinedByIssuer
	case EnumMagnetiqActionCode_MagnetiqActionCodeDeclineIssuerSpecialConditions:
		return IntegrationError_TransactionDeclinedByIssuer
	case EnumMagnetiqActionCode_MagnetiqActionCodeDeclineInvalidMerchant:
		return IntegrationError_PaymentForbiddenForMerchant
	case EnumMagnetiqActionCode_MagnetiqActionCodeDeclineInvalidAmount:
		return IntegrationError_TransactionDeclinedByAcquirer
	case EnumMagnetiqActionCode_MagnetiqActionCodeDeclineInvalidCardNumber:
		return IntegrationError_IncorrectCardNumber
	case EnumMagnetiqActionCode_MagnetiqActionCodeDeclineUnacceptableFee:
		return IntegrationError_TransactionDeclinedByAcquirer
	case EnumMagnetiqActionCode_MagnetiqActionCodeDeclineNoAccountType:
		return IntegrationError_TransactionDeclinedByAcquirer
	case EnumMagnetiqActionCode_MagnetiqActionCodeDeclineFunctionNotSupported:
		return IntegrationError_TransactionDeclinedByAcquirer
	case EnumMagnetiqActionCode_MagnetiqActionCodeDeclineInsufficientFunds:
		return IntegrationError_InsufficientFunds
	case EnumMagnetiqActionCode_MagnetiqActionCodeDeclineNoCardRecord:
		return IntegrationError_TransactionDeclinedByAcquirer
	case EnumMagnetiqActionCode_MagnetiqActionCodeDeclineNotPermittedToCardholder:
		return IntegrationError_TransactionDeclinedByAcquirer
	case EnumMagnetiqActionCode_MagnetiqActionCodeDeclineNotPermittedToTerminal:
		return IntegrationError_TransactionDeclinedByAcquirer
	case EnumMagnetiqActionCode_MagnetiqActionCodeDeclineExceedsWithdrawalLimit:
		return IntegrationError_ExceedsAmountLimit
	case EnumMagnetiqActionCode_MagnetiqActionCodeDeclineSecurityViolation:
		return IntegrationError_SuspiciousClient
	case EnumMagnetiqActionCode_MagnetiqActionCodeDeclineExceedsWithdrawalFrequency:
		return IntegrationError_ExceedsTransactionFrequencyLimit
	case EnumMagnetiqActionCode_MagnetiqActionCodeDeclineViolationOfLaw:
		return IntegrationError_SuspiciousClient
	case EnumMagnetiqActionCode_MagnetiqActionCodeDeclineCardNotEffective:
		return IntegrationError_TransactionDeclinedByAcquirer
	case EnumMagnetiqActionCode_MagnetiqActionCodeDeclineSuspectedCounterfeitCard:
		return IntegrationError_SuspiciousClient
	case EnumMagnetiqActionCode_MagnetiqActionCodeDeclineAdditionalAuthRequired:
		return IntegrationError_TransactionDeclinedByAcquirer
	case EnumMagnetiqActionCode_MagnetiqActionCodeDeclineWrongOnlinePinRetry:
		return IntegrationError_TransactionDeclinedByAcquirer
	case EnumMagnetiqActionCode_MagnetiqActionCodeDeclineLifecycleViolation:
		return IntegrationError_TransactionDeclinedByAcquirer
	case EnumMagnetiqActionCode_MagnetiqActionCodeDeclinePolicyViolation:
		return IntegrationError_SuspiciousClient
	case EnumMagnetiqActionCode_MagnetiqActionCodeDeclineFraudSecurityViolation:
		return IntegrationError_SuspiciousClient
	case EnumMagnetiqActionCode_MagnetiqActionCodeDeclineByCardholderWish:
		return IntegrationError_UndefinedError
	case EnumMagnetiqActionCode_MagnetiqActionCodeAdviceAcknowledgedNoLiability:
		return IntegrationError_TransactionDeclinedByAcquirer
	case EnumMagnetiqActionCode_MagnetiqActionCodeAdviceAcknowledgedLiabilityAccepted:
		return IntegrationError_TransactionDeclinedByAcquirer
	case EnumMagnetiqActionCode_MagnetiqActionCodeDeclineInvalidTransaction:
		return IntegrationError_TransactionDeclinedByAcquirer
	case EnumMagnetiqActionCode_MagnetiqActionCodeStatusReEnterTransaction:
		return IntegrationError_TransactionDeclinedByAcquirer
	case EnumMagnetiqActionCode_MagnetiqActionCodeDeclineFormatError:
		return IntegrationError_TransactionDeclinedByAcquirer
	case EnumMagnetiqActionCode_MagnetiqActionCodeDeclineIssuerOrSwitchInoperative:
		return IntegrationError_TransactionDeclinedByAcquirer
	case EnumMagnetiqActionCode_MagnetiqActionCodeDeclineDestinationNotFound:
		return IntegrationError_TransactionDeclinedByAcquirer
	case EnumMagnetiqActionCode_MagnetiqActionCodeDeclineSystemMalfunction:
		return IntegrationError_TransactionDeclinedByAcquirer
	case EnumMagnetiqActionCode_MagnetiqActionCodeDeclineIssuerSignedOff:
		return IntegrationError_TransactionDeclinedByAcquirer
	case EnumMagnetiqActionCode_MagnetiqActionCodeDeclineIssuerTimedOut:
		return IntegrationError_TransactionDeclinedByAcquirer
	case EnumMagnetiqActionCode_MagnetiqActionCodeDeclineIssuerUnavailable:
		return IntegrationError_TransactionDeclinedByAcquirer
	case EnumMagnetiqActionCode_MagnetiqActionCodeDeclineDuplicateTransmission:
		return IntegrationError_TransactionDeclinedByAcquirer
	case EnumMagnetiqActionCode_MagnetiqActionCodeDeclineNotTraceableToOriginal:
		return IntegrationError_TransactionDeclinedByAcquirer
	case EnumMagnetiqActionCode_MagnetiqActionCodeDeclineNoCommKeysAvailable:
		return IntegrationError_TransactionDeclinedByAcquirer
	case EnumMagnetiqActionCode_MagnetiqActionCodeDeclineMessageOutOfSequence:
		return IntegrationError_TransactionDeclinedByAcquirer
	case EnumMagnetiqActionCode_MagnetiqActionCodeStatusRequestInProgress:
		return IntegrationError_TransactionDeclinedByAcquirer
	case EnumMagnetiqActionCode_MagnetiqActionCodeDeclineBusinessViolation:
		return IntegrationError_TransactionDeclinedByAcquirer
	case EnumMagnetiqActionCode_MagnetiqActionCode3DSecureUnavailable:
		return IntegrationError_TransactionDeclinedByAcquirer
	case EnumMagnetiqActionCode_MagnetiqActionCode3DSecureAuthFailed:
		return IntegrationError_ThreeDSAuthFailed
	case EnumMagnetiqActionCode_MagnetiqActionCodeAgreementNotObtained:
		return IntegrationError_TransactionDeclinedByAcquirer
	case EnumMagnetiqActionCode_MagnetiqActionCodeAgreementSuspendedOrClosed:
		return IntegrationError_TransactionDeclinedByAcquirer
	case EnumMagnetiqActionCode_MagnetiqActionCodeOriginalCreditIneligible:
		return IntegrationError_TransactionDeclinedByAcquirer
	case EnumMagnetiqActionCode_MagnetiqActionCode3DSecureEnrollmentVerificationFailed:
		return IntegrationError_ThreeDSAuthFailed
	case EnumMagnetiqActionCode_MagnetiqActionCode3DSecurePaResExtractionFailed:
		return IntegrationError_InvalidThreeDSecureParameters
	case EnumMagnetiqActionCode_MagnetiqActionCodeSuspectedFraud:
		return IntegrationError_SuspiciousClient
	case EnumMagnetiqActionCode_MagnetiqActionCodeAbandonedTimeout:
		return IntegrationError_UnavailableAcquirer
	case EnumMagnetiqActionCode_MagnetiqActionCodeLimitsExceeded:
		return IntegrationError_ExceedsAmountLimit
	default:
		return IntegrationError_UndefinedError
	}
}

func (x EnumMagnetiqActionCode) Message() string {
	switch x {
	case EnumMagnetiqActionCode_MagnetiqActionCodeApproved:
		return "Approved"
	case EnumMagnetiqActionCode_MagnetiqActionCodeDeclineGeneral:
		return "Decline (general, no comments)"
	case EnumMagnetiqActionCode_MagnetiqActionCodeDeclineExpiredCard:
		return "Decline, expired card"
	case EnumMagnetiqActionCode_MagnetiqActionCodeDeclineSuspectedFraud:
		return "Decline, suspected fraud"
	case EnumMagnetiqActionCode_MagnetiqActionCodeDeclineContactAcquirer:
		return "Decline, card acceptor contact acquirer"
	case EnumMagnetiqActionCode_MagnetiqActionCodeDeclineRestrictedCard:
		return "Decline, restricted card"
	case EnumMagnetiqActionCode_MagnetiqActionCodeDeclineCallSecurityDept:
		return "Decline, card acceptor call acquirer's security department"
	case EnumMagnetiqActionCode_MagnetiqActionCodeDeclineReferToIssuer:
		return "Decline, refer to card issuer"
	case EnumMagnetiqActionCode_MagnetiqActionCodeDeclineIssuerSpecialConditions:
		return "Decline, refer to card issuer's special conditions"
	case EnumMagnetiqActionCode_MagnetiqActionCodeDeclineInvalidMerchant:
		return "Decline, invalid merchant"
	case EnumMagnetiqActionCode_MagnetiqActionCodeDeclineInvalidAmount:
		return "Decline, invalid amount"
	case EnumMagnetiqActionCode_MagnetiqActionCodeDeclineInvalidCardNumber:
		return "Decline, invalid card number"
	case EnumMagnetiqActionCode_MagnetiqActionCodeDeclineUnacceptableFee:
		return "Decline, unacceptable fee"
	case EnumMagnetiqActionCode_MagnetiqActionCodeDeclineNoAccountType:
		return "Decline, no account of type requested"
	case EnumMagnetiqActionCode_MagnetiqActionCodeDeclineFunctionNotSupported:
		return "Decline, requested function not supported"
	case EnumMagnetiqActionCode_MagnetiqActionCodeDeclineInsufficientFunds:
		return "Decline, not sufficient funds"
	case EnumMagnetiqActionCode_MagnetiqActionCodeDeclineNoCardRecord:
		return "Decline, no card record"
	case EnumMagnetiqActionCode_MagnetiqActionCodeDeclineNotPermittedToCardholder:
		return "Decline, transaction not permitted to cardholder"
	case EnumMagnetiqActionCode_MagnetiqActionCodeDeclineNotPermittedToTerminal:
		return "Decline, transaction not permitted to terminal"
	case EnumMagnetiqActionCode_MagnetiqActionCodeDeclineExceedsWithdrawalLimit:
		return "Decline, exceeds withdrawal amount limit"
	case EnumMagnetiqActionCode_MagnetiqActionCodeDeclineSecurityViolation:
		return "Decline, security violation"
	case EnumMagnetiqActionCode_MagnetiqActionCodeDeclineExceedsWithdrawalFrequency:
		return "Decline, exceeds withdrawal frequency limit"
	case EnumMagnetiqActionCode_MagnetiqActionCodeDeclineViolationOfLaw:
		return "Decline, violation of law"
	case EnumMagnetiqActionCode_MagnetiqActionCodeDeclineCardNotEffective:
		return "Decline, card not effective"
	case EnumMagnetiqActionCode_MagnetiqActionCodeDeclineSuspectedCounterfeitCard:
		return "Decline, suspected counterfeit card"
	case EnumMagnetiqActionCode_MagnetiqActionCodeDeclineAdditionalAuthRequired:
		return "Decline, additional customer authentication required"
	case EnumMagnetiqActionCode_MagnetiqActionCodeDeclineWrongOnlinePinRetry:
		return "Decline, wrong online PIN, retry as single tap"
	case EnumMagnetiqActionCode_MagnetiqActionCodeDeclineLifecycleViolation:
		return "Decline, Lifecycle violation"
	case EnumMagnetiqActionCode_MagnetiqActionCodeDeclinePolicyViolation:
		return "Decline, Policy violation"
	case EnumMagnetiqActionCode_MagnetiqActionCodeDeclineFraudSecurityViolation:
		return "Decline, Fraud/Security violation"
	case EnumMagnetiqActionCode_MagnetiqActionCodeDeclineByCardholderWish:
		return "Decline, by cardholder's wish"
	case EnumMagnetiqActionCode_MagnetiqActionCodeAdviceAcknowledgedNoLiability:
		return "Advice acknowledged, no financial liability accepted"
	case EnumMagnetiqActionCode_MagnetiqActionCodeAdviceAcknowledgedLiabilityAccepted:
		return "Advice acknowledged, financial liability accepted"
	case EnumMagnetiqActionCode_MagnetiqActionCodeDeclineInvalidTransaction:
		return "Decline reason message: invalid transaction"
	case EnumMagnetiqActionCode_MagnetiqActionCodeStatusReEnterTransaction:
		return "Status message: re-enter transaction"
	case EnumMagnetiqActionCode_MagnetiqActionCodeDeclineFormatError:
		return "Decline reason message: format error"
	case EnumMagnetiqActionCode_MagnetiqActionCodeDeclineIssuerOrSwitchInoperative:
		return "Decline reason message: card issuer or switch inoperative"
	case EnumMagnetiqActionCode_MagnetiqActionCodeDeclineDestinationNotFound:
		return "Decline reason message: transaction destination cannot be found for routing"
	case EnumMagnetiqActionCode_MagnetiqActionCodeDeclineSystemMalfunction:
		return "Decline reason message: system malfunction"
	case EnumMagnetiqActionCode_MagnetiqActionCodeDeclineIssuerSignedOff:
		return "Decline reason message: card issuer signed off"
	case EnumMagnetiqActionCode_MagnetiqActionCodeDeclineIssuerTimedOut:
		return "Decline reason message: card issuer timed out"
	case EnumMagnetiqActionCode_MagnetiqActionCodeDeclineIssuerUnavailable:
		return "Decline reason message: card issuer unavailable"
	case EnumMagnetiqActionCode_MagnetiqActionCodeDeclineDuplicateTransmission:
		return "Decline reason message: duplicate transmission"
	case EnumMagnetiqActionCode_MagnetiqActionCodeDeclineNotTraceableToOriginal:
		return "Decline reason message: not able to trace back to original transaction"
	case EnumMagnetiqActionCode_MagnetiqActionCodeDeclineNoCommKeysAvailable:
		return "Decline reason message: no communication keys available for use"
	case EnumMagnetiqActionCode_MagnetiqActionCodeDeclineMessageOutOfSequence:
		return "Decline reason message: message number out of sequence"
	case EnumMagnetiqActionCode_MagnetiqActionCodeStatusRequestInProgress:
		return "Status message: request in progress"
	case EnumMagnetiqActionCode_MagnetiqActionCodeDeclineBusinessViolation:
		return "Decline reason message: violation of business arrangement"
	case EnumMagnetiqActionCode_MagnetiqActionCode3DSecureUnavailable:
		return "3D Secure authentication is currently unavailable (Enrollment status = E)"
	case EnumMagnetiqActionCode_MagnetiqActionCode3DSecureAuthFailed:
		return "3D Secure authentication failed (Authentication status = N)"
	case EnumMagnetiqActionCode_MagnetiqActionCodeAgreementNotObtained:
		return "Could not obtain agreement for this payment (Currency or card type)"
	case EnumMagnetiqActionCode_MagnetiqActionCodeAgreementSuspendedOrClosed:
		return "Agreement suspended or closed"
	case EnumMagnetiqActionCode_MagnetiqActionCodeOriginalCreditIneligible:
		return "Original credit transaction is ineligible for this PAN (at least one deposited payment required)"
	case EnumMagnetiqActionCode_MagnetiqActionCode3DSecureEnrollmentVerificationFailed:
		return "Card enrollment verification in 3D Secure failed (Incorrect VeRes; DS not available; merchant is not participating in 3D)"
	case EnumMagnetiqActionCode_MagnetiqActionCode3DSecurePaResExtractionFailed:
		return "3D Secure field extraction from PaRes failed (Incorrect PaRes; Incorrect certificate)"
	case EnumMagnetiqActionCode_MagnetiqActionCodeSuspectedFraud:
		return "Suspected fraud (MAGNETIQ antifraud system blocked this payment. Contact your client-manager for more information)"
	case EnumMagnetiqActionCode_MagnetiqActionCodeAbandonedTimeout:
		return "Abandoned (Timed out)"
	case EnumMagnetiqActionCode_MagnetiqActionCodeLimitsExceeded:
		return "MAGNETIQ Limits exceeded (MAGNETIQ antifraud system blocked this payment due to amount limitations in force. Contact your client-manager for more information)"
	default:
		return "undefined"
	}
}

// Created reference to EnumMagnetiqActionCode

//	|	EnumMagnetiqActionCode                                                       	|	IntegrationError                                 	|	Message                                                                                                                                                         	|
//	|	EnumMagnetiqActionCode_MagnetiqActionCodeApproved                            	|	IntegrationError_UndefinedError                  	|	"Approved"                                                                                                                                                      	|
//	|	EnumMagnetiqActionCode_MagnetiqActionCodeDeclineGeneral                      	|	IntegrationError_TransactionDeclinedByAcquirer   	|	"Decline (general, no comments)"                                                                                                                                	|
//	|	EnumMagnetiqActionCode_MagnetiqActionCodeDeclineExpiredCard                  	|	IntegrationError_CardHasExpired                  	|	"Decline, expired card"                                                                                                                                         	|
//	|	EnumMagnetiqActionCode_MagnetiqActionCodeDeclineSuspectedFraud               	|	IntegrationError_SuspiciousClient                	|	"Decline, suspected fraud"                                                                                                                                      	|
//	|	EnumMagnetiqActionCode_MagnetiqActionCodeDeclineContactAcquirer              	|	IntegrationError_TransactionDeclinedByAcquirer   	|	"Decline, card acceptor contact acquirer"                                                                                                                       	|
//	|	EnumMagnetiqActionCode_MagnetiqActionCodeDeclineRestrictedCard               	|	IntegrationError_InvalidCard                     	|	"Decline, restricted card"                                                                                                                                      	|
//	|	EnumMagnetiqActionCode_MagnetiqActionCodeDeclineCallSecurityDept             	|	IntegrationError_TransactionDeclinedByAcquirer   	|	"Decline, card acceptor call acquirer's security department"                                                                                                    	|
//	|	EnumMagnetiqActionCode_MagnetiqActionCodeDeclineReferToIssuer                	|	IntegrationError_TransactionDeclinedByIssuer     	|	"Decline, refer to card issuer"                                                                                                                                 	|
//	|	EnumMagnetiqActionCode_MagnetiqActionCodeDeclineIssuerSpecialConditions      	|	IntegrationError_TransactionDeclinedByIssuer     	|	"Decline, refer to card issuer's special conditions"                                                                                                            	|
//	|	EnumMagnetiqActionCode_MagnetiqActionCodeDeclineInvalidMerchant              	|	IntegrationError_PaymentForbiddenForMerchant     	|	"Decline, invalid merchant"                                                                                                                                     	|
//	|	EnumMagnetiqActionCode_MagnetiqActionCodeDeclineInvalidAmount                	|	IntegrationError_TransactionDeclinedByAcquirer   	|	"Decline, invalid amount"                                                                                                                                       	|
//	|	EnumMagnetiqActionCode_MagnetiqActionCodeDeclineInvalidCardNumber            	|	IntegrationError_IncorrectCardNumber             	|	"Decline, invalid card number"                                                                                                                                  	|
//	|	EnumMagnetiqActionCode_MagnetiqActionCodeDeclineUnacceptableFee              	|	IntegrationError_TransactionDeclinedByAcquirer   	|	"Decline, unacceptable fee"                                                                                                                                     	|
//	|	EnumMagnetiqActionCode_MagnetiqActionCodeDeclineNoAccountType                	|	IntegrationError_TransactionDeclinedByAcquirer   	|	"Decline, no account of type requested"                                                                                                                         	|
//	|	EnumMagnetiqActionCode_MagnetiqActionCodeDeclineFunctionNotSupported         	|	IntegrationError_TransactionDeclinedByAcquirer   	|	"Decline, requested function not supported"                                                                                                                     	|
//	|	EnumMagnetiqActionCode_MagnetiqActionCodeDeclineInsufficientFunds            	|	IntegrationError_InsufficientFunds               	|	"Decline, not sufficient funds"                                                                                                                                 	|
//	|	EnumMagnetiqActionCode_MagnetiqActionCodeDeclineNoCardRecord                 	|	IntegrationError_TransactionDeclinedByAcquirer   	|	"Decline, no card record"                                                                                                                                       	|
//	|	EnumMagnetiqActionCode_MagnetiqActionCodeDeclineNotPermittedToCardholder     	|	IntegrationError_TransactionDeclinedByAcquirer   	|	"Decline, transaction not permitted to cardholder"                                                                                                              	|
//	|	EnumMagnetiqActionCode_MagnetiqActionCodeDeclineNotPermittedToTerminal       	|	IntegrationError_TransactionDeclinedByAcquirer   	|	"Decline, transaction not permitted to terminal"                                                                                                                	|
//	|	EnumMagnetiqActionCode_MagnetiqActionCodeDeclineExceedsWithdrawalLimit       	|	IntegrationError_ExceedsAmountLimit              	|	"Decline, exceeds withdrawal amount limit"                                                                                                                      	|
//	|	EnumMagnetiqActionCode_MagnetiqActionCodeDeclineSecurityViolation            	|	IntegrationError_SuspiciousClient                	|	"Decline, security violation"                                                                                                                                   	|
//	|	EnumMagnetiqActionCode_MagnetiqActionCodeDeclineExceedsWithdrawalFrequency   	|	IntegrationError_ExceedsTransactionFrequencyLimit	|	"Decline, exceeds withdrawal frequency limit"                                                                                                                   	|
//	|	EnumMagnetiqActionCode_MagnetiqActionCodeDeclineViolationOfLaw               	|	IntegrationError_SuspiciousClient                	|	"Decline, violation of law"                                                                                                                                     	|
//	|	EnumMagnetiqActionCode_MagnetiqActionCodeDeclineCardNotEffective             	|	IntegrationError_TransactionDeclinedByAcquirer   	|	"Decline, card not effective"                                                                                                                                   	|
//	|	EnumMagnetiqActionCode_MagnetiqActionCodeDeclineSuspectedCounterfeitCard     	|	IntegrationError_SuspiciousClient                	|	"Decline, suspected counterfeit card"                                                                                                                           	|
//	|	EnumMagnetiqActionCode_MagnetiqActionCodeDeclineAdditionalAuthRequired       	|	IntegrationError_TransactionDeclinedByAcquirer   	|	"Decline, additional customer authentication required"                                                                                                          	|
//	|	EnumMagnetiqActionCode_MagnetiqActionCodeDeclineWrongOnlinePinRetry          	|	IntegrationError_TransactionDeclinedByAcquirer   	|	"Decline, wrong online PIN, retry as single tap"                                                                                                                	|
//	|	EnumMagnetiqActionCode_MagnetiqActionCodeDeclineLifecycleViolation           	|	IntegrationError_TransactionDeclinedByAcquirer   	|	"Decline, Lifecycle violation"                                                                                                                                  	|
//	|	EnumMagnetiqActionCode_MagnetiqActionCodeDeclinePolicyViolation              	|	IntegrationError_SuspiciousClient                	|	"Decline, Policy violation"                                                                                                                                     	|
//	|	EnumMagnetiqActionCode_MagnetiqActionCodeDeclineFraudSecurityViolation       	|	IntegrationError_SuspiciousClient                	|	"Decline, Fraud/Security violation"                                                                                                                             	|
//	|	EnumMagnetiqActionCode_MagnetiqActionCodeDeclineByCardholderWish             	|	IntegrationError_UndefinedError                  	|	"Decline, by cardholder's wish"                                                                                                                                 	|
//	|	EnumMagnetiqActionCode_MagnetiqActionCodeAdviceAcknowledgedNoLiability       	|	IntegrationError_TransactionDeclinedByAcquirer   	|	"Advice acknowledged, no financial liability accepted"                                                                                                          	|
//	|	EnumMagnetiqActionCode_MagnetiqActionCodeAdviceAcknowledgedLiabilityAccepted 	|	IntegrationError_TransactionDeclinedByAcquirer   	|	"Advice acknowledged, financial liability accepted"                                                                                                             	|
//	|	EnumMagnetiqActionCode_MagnetiqActionCodeDeclineInvalidTransaction           	|	IntegrationError_TransactionDeclinedByAcquirer   	|	"Decline reason message: invalid transaction"                                                                                                                   	|
//	|	EnumMagnetiqActionCode_MagnetiqActionCodeStatusReEnterTransaction            	|	IntegrationError_TransactionDeclinedByAcquirer   	|	"Status message: re-enter transaction"                                                                                                                          	|
//	|	EnumMagnetiqActionCode_MagnetiqActionCodeDeclineFormatError                  	|	IntegrationError_TransactionDeclinedByAcquirer   	|	"Decline reason message: format error"                                                                                                                          	|
//	|	EnumMagnetiqActionCode_MagnetiqActionCodeDeclineIssuerOrSwitchInoperative    	|	IntegrationError_TransactionDeclinedByAcquirer   	|	"Decline reason message: card issuer or switch inoperative"                                                                                                     	|
//	|	EnumMagnetiqActionCode_MagnetiqActionCodeDeclineDestinationNotFound          	|	IntegrationError_TransactionDeclinedByAcquirer   	|	"Decline reason message: transaction destination cannot be found for routing"                                                                                   	|
//	|	EnumMagnetiqActionCode_MagnetiqActionCodeDeclineSystemMalfunction            	|	IntegrationError_TransactionDeclinedByAcquirer   	|	"Decline reason message: system malfunction"                                                                                                                    	|
//	|	EnumMagnetiqActionCode_MagnetiqActionCodeDeclineIssuerSignedOff              	|	IntegrationError_TransactionDeclinedByAcquirer   	|	"Decline reason message: card issuer signed off"                                                                                                                	|
//	|	EnumMagnetiqActionCode_MagnetiqActionCodeDeclineIssuerTimedOut               	|	IntegrationError_TransactionDeclinedByAcquirer   	|	"Decline reason message: card issuer timed out"                                                                                                                 	|
//	|	EnumMagnetiqActionCode_MagnetiqActionCodeDeclineIssuerUnavailable            	|	IntegrationError_TransactionDeclinedByAcquirer   	|	"Decline reason message: card issuer unavailable"                                                                                                               	|
//	|	EnumMagnetiqActionCode_MagnetiqActionCodeDeclineDuplicateTransmission        	|	IntegrationError_TransactionDeclinedByAcquirer   	|	"Decline reason message: duplicate transmission"                                                                                                                	|
//	|	EnumMagnetiqActionCode_MagnetiqActionCodeDeclineNotTraceableToOriginal       	|	IntegrationError_TransactionDeclinedByAcquirer   	|	"Decline reason message: not able to trace back to original transaction"                                                                                        	|
//	|	EnumMagnetiqActionCode_MagnetiqActionCodeDeclineNoCommKeysAvailable          	|	IntegrationError_TransactionDeclinedByAcquirer   	|	"Decline reason message: no communication keys available for use"                                                                                               	|
//	|	EnumMagnetiqActionCode_MagnetiqActionCodeDeclineMessageOutOfSequence         	|	IntegrationError_TransactionDeclinedByAcquirer   	|	"Decline reason message: message number out of sequence"                                                                                                        	|
//	|	EnumMagnetiqActionCode_MagnetiqActionCodeStatusRequestInProgress             	|	IntegrationError_TransactionDeclinedByAcquirer   	|	"Status message: request in progress"                                                                                                                           	|
//	|	EnumMagnetiqActionCode_MagnetiqActionCodeDeclineBusinessViolation            	|	IntegrationError_TransactionDeclinedByAcquirer   	|	"Decline reason message: violation of business arrangement"                                                                                                     	|
//	|	EnumMagnetiqActionCode_MagnetiqActionCode3DSecureUnavailable                 	|	IntegrationError_TransactionDeclinedByAcquirer   	|	"3D Secure authentication is currently unavailable (Enrollment status = E)"                                                                                     	|
//	|	EnumMagnetiqActionCode_MagnetiqActionCode3DSecureAuthFailed                  	|	IntegrationError_ThreeDSAuthFailed               	|	"3D Secure authentication failed (Authentication status = N)"                                                                                                   	|
//	|	EnumMagnetiqActionCode_MagnetiqActionCodeAgreementNotObtained                	|	IntegrationError_TransactionDeclinedByAcquirer   	|	"Could not obtain agreement for this payment (Currency or card type)"                                                                                           	|
//	|	EnumMagnetiqActionCode_MagnetiqActionCodeAgreementSuspendedOrClosed          	|	IntegrationError_TransactionDeclinedByAcquirer   	|	"Agreement suspended or closed"                                                                                                                                 	|
//	|	EnumMagnetiqActionCode_MagnetiqActionCodeOriginalCreditIneligible            	|	IntegrationError_TransactionDeclinedByAcquirer   	|	"Original credit transaction is ineligible for this PAN (at least one deposited payment required)"                                                              	|
//	|	EnumMagnetiqActionCode_MagnetiqActionCode3DSecureEnrollmentVerificationFailed	|	IntegrationError_ThreeDSAuthFailed               	|	"Card enrollment verification in 3D Secure failed (Incorrect VeRes; DS not available; merchant is not participating in 3D)"                                     	|
//	|	EnumMagnetiqActionCode_MagnetiqActionCode3DSecurePaResExtractionFailed       	|	IntegrationError_InvalidThreeDSecureParameters   	|	"3D Secure field extraction from PaRes failed (Incorrect PaRes; Incorrect certificate)"                                                                         	|
//	|	EnumMagnetiqActionCode_MagnetiqActionCodeSuspectedFraud                      	|	IntegrationError_SuspiciousClient                	|	"Suspected fraud (MAGNETIQ antifraud system blocked this payment. Contact your client-manager for more information)"                                            	|
//	|	EnumMagnetiqActionCode_MagnetiqActionCodeAbandonedTimeout                    	|	IntegrationError_UnavailableAcquirer             	|	"Abandoned (Timed out)"                                                                                                                                         	|
//	|	EnumMagnetiqActionCode_MagnetiqActionCodeLimitsExceeded                      	|	IntegrationError_ExceedsAmountLimit              	|	"MAGNETIQ Limits exceeded (MAGNETIQ antifraud system blocked this payment due to amount limitations in force. Contact your client-manager for more information)"	|

var SliceEnumMagnetiqActionCodeRefs *sliceEnumMagnetiqActionCodeRefs

type sliceEnumMagnetiqActionCodeRefs struct{}

func (*sliceEnumMagnetiqActionCodeRefs) IntegrationError(slice ...EnumMagnetiqActionCode) []IntegrationError {
	var result []IntegrationError
	for _, val := range slice {
		result = append(result, val.IntegrationError())
	}

	return result
}

func (*sliceEnumMagnetiqActionCodeRefs) Message(slice ...EnumMagnetiqActionCode) []string {
	var result []string
	for _, val := range slice {
		result = append(result, val.Message())
	}

	return result
}

func (x EnumMagnetiqModeCode) Code() string {
	switch x {
	case EnumMagnetiqModeCode_MagnetiqModeNone3DS:
		return "4"
	case EnumMagnetiqModeCode_MagnetiqMode3DS:
		return "6"
	case EnumMagnetiqModeCode_MagnetiqModeOneClickPayIn:
		return "9"
	default:
		return ""
	}
}

func (x EnumMagnetiqModeCode) Description() string {
	switch x {
	case EnumMagnetiqModeCode_MagnetiqModeNone3DS:
		return "Non-3D Secure"
	case EnumMagnetiqModeCode_MagnetiqMode3DS:
		return "3D Secure"
	case EnumMagnetiqModeCode_MagnetiqModeOneClickPayIn:
		return "Cardholder Not Present"
	default:
		return "undefined"
	}
}

// Created reference to EnumMagnetiqModeCode

//	|	EnumMagnetiqModeCode                          	|	Code	|	Description             	|
//	|	EnumMagnetiqModeCode_MagnetiqModeNone3DS      	|	"4" 	|	"Non-3D Secure"         	|
//	|	EnumMagnetiqModeCode_MagnetiqMode3DS          	|	"6" 	|	"3D Secure"             	|
//	|	EnumMagnetiqModeCode_MagnetiqModeOneClickPayIn	|	"9" 	|	"Cardholder Not Present"	|

var SliceEnumMagnetiqModeCodeRefs *sliceEnumMagnetiqModeCodeRefs

type sliceEnumMagnetiqModeCodeRefs struct{}

func (*sliceEnumMagnetiqModeCodeRefs) Code(slice ...EnumMagnetiqModeCode) []string {
	var result []string
	for _, val := range slice {
		result = append(result, val.Code())
	}

	return result
}

func (*sliceEnumMagnetiqModeCodeRefs) Description(slice ...EnumMagnetiqModeCode) []string {
	var result []string
	for _, val := range slice {
		result = append(result, val.Description())
	}

	return result
}
