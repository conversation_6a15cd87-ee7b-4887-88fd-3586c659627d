// Code generated by protoc-gen-go-mvp. DO NOT EDIT.
// versions:
// - protoc-gen-go-mvp v2.0.1
// created by mvp authors

package grpc

import (
	context "context"
	fmt "fmt"
	cntx "git.local/sensitive/mvp/pkg/cntx"
	ctxzap "github.com/grpc-ecosystem/go-grpc-middleware/logging/zap/ctxzap"
	zap "go.uber.org/zap"
	grpc "google.golang.org/grpc"
)

func file_inner_processing_grpc_project_proto_message_MerchantDataToZap(
	label string,
	in *MerchantData,
) zap.Field {
	if in == nil {
		return zap.Reflect(label, in)
	}

	return zap.Dict(
		label,
		zap.Any("MerchantId", in.GetMerchantId()),
		zap.Any("Name", in.GetName()),
		zap.Any("Email", in.GetEmail()),
		zap.Any("SiteUrl", in.GetSiteUrl()),
		zap.Any("IsActive", in.GetIsActive()),
		zap.Any("IsHold", in.GetIsHold()),
		zap.Any("IsBlocked", in.GetIsBlocked()),
		zap.Any("Description", in.GetDescription()),
		zap.Any("Contacts", in.GetContacts()),
		zap.Any("Logo", in.GetLogo()),
		zap.Any("Bin", in.GetBin()),
	)
}

func file_inner_processing_grpc_project_proto_message_ProjectRequestV1ToZap(
	label string,
	in *ProjectRequestV1,
) zap.Field {
	if in == nil {
		return zap.Reflect(label, in)
	}

	return zap.Dict(
		label,
		zap.Any("MerchantId", in.GetMerchantId()),
		zap.Any("ProjectId", in.GetProjectId()),
	)
}

func file_inner_processing_grpc_project_proto_message_GetProcessingProjectsByBUIDRequestV1ToZap(
	label string,
	in *GetProcessingProjectsByBUIDRequestV1,
) zap.Field {
	if in == nil {
		return zap.Reflect(label, in)
	}

	return zap.Dict(
		label,
		zap.Any("BusinessUnitId", in.GetBusinessUnitId()),
	)
}

func file_inner_processing_grpc_project_proto_message_GetProcessingProjectsByBUIDResponseV1ToZap(
	label string,
	in *GetProcessingProjectsByBUIDResponseV1,
) zap.Field {
	if in == nil {
		return zap.Reflect(label, in)
	}

	return zap.Dict(
		label,
		zap.Any("BusinessUnitId", in.GetBusinessUnitId()),
		file_inner_processing_grpc_project_proto_message_ProjectBasicDataSliceToZap("Projects", in.GetProjects()),
	)
}

func file_inner_processing_grpc_project_proto_message_IsSendEmailResponseV1ToZap(
	label string,
	in *IsSendEmailResponseV1,
) zap.Field {
	if in == nil {
		return zap.Reflect(label, in)
	}

	return zap.Dict(
		label,
		zap.Any("SendEmailEnabled", in.GetSendEmailEnabled()),
	)
}

func file_inner_processing_grpc_project_proto_message_ProjectBasicDataToZap(
	label string,
	in *ProjectBasicData,
) zap.Field {
	if in == nil {
		return zap.Reflect(label, in)
	}

	return zap.Dict(
		label,
		zap.Any("ProjectId", in.GetProjectId()),
		zap.Any("ProjectName", in.GetProjectName()),
	)
}

func file_inner_processing_grpc_project_proto_message_ProjectBasicDataSliceToZap(
	label string,
	in []*ProjectBasicData,
) zap.Field {
	var res []zap.Field
	for index, val := range in {
		res = append(res, file_inner_processing_grpc_project_proto_message_ProjectBasicDataToZap(fmt.Sprint(index), val))
	}

	return zap.Dict(label, res...)
}

func file_inner_processing_grpc_project_proto_message_ProjectDataToZap(
	label string,
	in *ProjectData,
) zap.Field {
	if in == nil {
		return zap.Reflect(label, in)
	}

	return zap.Dict(
		label,
		zap.Any("ProjectId", in.GetProjectId()),
		zap.Any("Name", in.GetName()),
		zap.Any("IncomeSource", in.GetIncomeSource()),
		zap.Any("MainActivityType", in.GetMainActivityType()),
		zap.Any("FirstActivityType", in.GetFirstActivityType()),
		zap.Any("SecondActivityType", in.GetSecondActivityType()),
		zap.Any("ThirdActivityType", in.GetThirdActivityType()),
		zap.Any("LicenseName", in.GetLicenseName()),
		zap.Any("LicenseNumber", in.GetLicenseNumber()),
		zap.Any("IsLicense", in.GetIsLicense()),
		zap.Any("MerchantId", in.GetMerchantId()),
		zap.Any("Logo", in.GetLogo()),
	)
}

func file_inner_processing_grpc_project_proto_message_ProjectDataSliceToZap(
	label string,
	in []*ProjectData,
) zap.Field {
	var res []zap.Field
	for index, val := range in {
		res = append(res, file_inner_processing_grpc_project_proto_message_ProjectDataToZap(fmt.Sprint(index), val))
	}

	return zap.Dict(label, res...)
}

func file_inner_processing_grpc_project_proto_message_ProjectsRequestV1ToZap(
	label string,
	in *ProjectsRequestV1,
) zap.Field {
	if in == nil {
		return zap.Reflect(label, in)
	}

	return zap.Dict(
		label,
		zap.Any("MerchantId", in.GetMerchantId()),
	)
}

func file_inner_processing_grpc_project_proto_message_ProjectsResponseV1ToZap(
	label string,
	in *ProjectsResponseV1,
) zap.Field {
	if in == nil {
		return zap.Reflect(label, in)
	}

	return zap.Dict(
		label,
		file_inner_processing_grpc_project_proto_message_ProjectDataSliceToZap("Projects", in.GetProjects()),
		file_inner_processing_grpc_project_proto_message_MerchantDataToZap("Merchant", in.GetMerchant()),
	)
}

var _ ProjectServer = (*loggedProjectServer)(nil)

func NewLoggedProjectServer(srv ProjectServer) ProjectServer {
	return &loggedProjectServer{srv: srv}
}

type loggedProjectServer struct {
	UnimplementedProjectServer

	srv ProjectServer
}

func (s *loggedProjectServer) GetProjectsByMerchantID(
	ctx context.Context,
	request *ProjectsRequestV1,
) (
	response *ProjectsResponseV1,
	err error,
) {
	label := cntx.Begin(ctx, "ProjectServer_GetProjectsByMerchantID")
	defer cntx.End(ctx, label)

	defer func() {
		if err != nil {
			ctxzap.AddFields(ctx, zap.Any(label+"error", err))
			return
		}

		ctxzap.AddFields(
			ctx,
			file_inner_processing_grpc_project_proto_message_ProjectsResponseV1ToZap(label+"response", response),
		)
	}()

	ctxzap.AddFields(
		ctx,
		file_inner_processing_grpc_project_proto_message_ProjectsRequestV1ToZap(label+"request", request),
	)

	response, err = s.srv.GetProjectsByMerchantID(ctx, request)

	return
}

func (s *loggedProjectServer) IsSendEmail(
	ctx context.Context,
	request *ProjectRequestV1,
) (
	response *IsSendEmailResponseV1,
	err error,
) {
	label := cntx.Begin(ctx, "ProjectServer_IsSendEmail")
	defer cntx.End(ctx, label)

	defer func() {
		if err != nil {
			ctxzap.AddFields(ctx, zap.Any(label+"error", err))
			return
		}

		ctxzap.AddFields(
			ctx,
			file_inner_processing_grpc_project_proto_message_IsSendEmailResponseV1ToZap(label+"response", response),
		)
	}()

	ctxzap.AddFields(
		ctx,
		file_inner_processing_grpc_project_proto_message_ProjectRequestV1ToZap(label+"request", request),
	)

	response, err = s.srv.IsSendEmail(ctx, request)

	return
}

func (s *loggedProjectServer) GetProcessingProjectsByBUIDV1(
	ctx context.Context,
	request *GetProcessingProjectsByBUIDRequestV1,
) (
	response *GetProcessingProjectsByBUIDResponseV1,
	err error,
) {
	label := cntx.Begin(ctx, "ProjectServer_GetProcessingProjectsByBUIDV1")
	defer cntx.End(ctx, label)

	defer func() {
		if err != nil {
			ctxzap.AddFields(ctx, zap.Any(label+"error", err))
			return
		}

		ctxzap.AddFields(
			ctx,
			file_inner_processing_grpc_project_proto_message_GetProcessingProjectsByBUIDResponseV1ToZap(label+"response", response),
		)
	}()

	ctxzap.AddFields(
		ctx,
		file_inner_processing_grpc_project_proto_message_GetProcessingProjectsByBUIDRequestV1ToZap(label+"request", request),
	)

	response, err = s.srv.GetProcessingProjectsByBUIDV1(ctx, request)

	return
}

var _ ProjectClient = (*loggedProjectClient)(nil)

func NewLoggedProjectClient(client ProjectClient) ProjectClient {
	return &loggedProjectClient{client: client}
}

type loggedProjectClient struct {
	client ProjectClient
}

func (s *loggedProjectClient) GetProjectsByMerchantID(
	ctx context.Context,
	request *ProjectsRequestV1,
	opts ...grpc.CallOption,
) (
	response *ProjectsResponseV1,
	err error,
) {
	label := cntx.Begin(ctx, "ProjectClient_GetProjectsByMerchantID")
	defer cntx.End(ctx, label)

	defer func() {
		if err != nil {
			ctxzap.AddFields(ctx, zap.Any(label+"error", err))
			return
		}

		ctxzap.AddFields(
			ctx,
			file_inner_processing_grpc_project_proto_message_ProjectsResponseV1ToZap(label+"response", response),
		)
	}()

	ctxzap.AddFields(
		ctx,
		file_inner_processing_grpc_project_proto_message_ProjectsRequestV1ToZap(label+"request", request),
	)

	response, err = s.client.GetProjectsByMerchantID(ctx, request, opts...)

	return
}

func (s *loggedProjectClient) IsSendEmail(
	ctx context.Context,
	request *ProjectRequestV1,
	opts ...grpc.CallOption,
) (
	response *IsSendEmailResponseV1,
	err error,
) {
	label := cntx.Begin(ctx, "ProjectClient_IsSendEmail")
	defer cntx.End(ctx, label)

	defer func() {
		if err != nil {
			ctxzap.AddFields(ctx, zap.Any(label+"error", err))
			return
		}

		ctxzap.AddFields(
			ctx,
			file_inner_processing_grpc_project_proto_message_IsSendEmailResponseV1ToZap(label+"response", response),
		)
	}()

	ctxzap.AddFields(
		ctx,
		file_inner_processing_grpc_project_proto_message_ProjectRequestV1ToZap(label+"request", request),
	)

	response, err = s.client.IsSendEmail(ctx, request, opts...)

	return
}

func (s *loggedProjectClient) GetProcessingProjectsByBUIDV1(
	ctx context.Context,
	request *GetProcessingProjectsByBUIDRequestV1,
	opts ...grpc.CallOption,
) (
	response *GetProcessingProjectsByBUIDResponseV1,
	err error,
) {
	label := cntx.Begin(ctx, "ProjectClient_GetProcessingProjectsByBUIDV1")
	defer cntx.End(ctx, label)

	defer func() {
		if err != nil {
			ctxzap.AddFields(ctx, zap.Any(label+"error", err))
			return
		}

		ctxzap.AddFields(
			ctx,
			file_inner_processing_grpc_project_proto_message_GetProcessingProjectsByBUIDResponseV1ToZap(label+"response", response),
		)
	}()

	ctxzap.AddFields(
		ctx,
		file_inner_processing_grpc_project_proto_message_GetProcessingProjectsByBUIDRequestV1ToZap(label+"request", request),
	)

	response, err = s.client.GetProcessingProjectsByBUIDV1(ctx, request, opts...)

	return
}
