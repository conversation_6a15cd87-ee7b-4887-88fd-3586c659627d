// Code generated by protoc-gen-go-mvp. DO NOT EDIT.
// versions:
// - protoc-gen-go-mvp v2.0.1
// created by mvp authors

package grpc

import (
	context "context"
	cntx "git.local/sensitive/mvp/pkg/cntx"
	ctxzap "github.com/grpc-ecosystem/go-grpc-middleware/logging/zap/ctxzap"
	zap "go.uber.org/zap"
	grpc "google.golang.org/grpc"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
)

func file_inner_processing_grpc_project_transactions_proto_message_TimestampToZap(
	label string,
	in *timestamppb.Timestamp,
) zap.Field {
	if in == nil {
		return zap.Reflect(label, in)
	}

	return zap.Dict(
		label,
		zap.Any("Seconds", in.GetSeconds()),
		zap.Any("Nanos", in.GetNanos()),
	)
}

func file_inner_processing_grpc_project_transactions_proto_message_CheckAmountLimitRequestV1ToZap(
	label string,
	in *CheckAmountLimitRequestV1,
) zap.Field {
	if in == nil {
		return zap.Reflect(label, in)
	}

	return zap.Dict(
		label,
		zap.Any("ProjectId", in.GetProjectId()),
		zap.Any("TransactionTypeId", in.GetTransactionTypeId()),
		zap.Any("TransactionAmount", in.GetTransactionAmount()),
	)
}

func file_inner_processing_grpc_project_transactions_proto_message_CheckAmountLimitResponseV1ToZap(
	label string,
	in *CheckAmountLimitResponseV1,
) zap.Field {
	if in == nil {
		return zap.Reflect(label, in)
	}

	return zap.Dict(
		label,
		zap.Any("VerifiedAmount", in.GetVerifiedAmount()),
	)
}

func file_inner_processing_grpc_project_transactions_proto_message_CheckAttemptsWithinTimeoutRequestV1ToZap(
	label string,
	in *CheckAttemptsWithinTimeoutRequestV1,
) zap.Field {
	if in == nil {
		return zap.Reflect(label, in)
	}

	return zap.Dict(
		label,
		zap.Any("ProjectId", in.GetProjectId()),
		zap.Any("TransactionTypeId", in.GetTransactionTypeId()),
		zap.Any("Try", in.GetTry()),
		zap.Any("TransactionLifeTime", in.GetTransactionLifeTime()),
	)
}

func file_inner_processing_grpc_project_transactions_proto_message_CheckAttemptsWithinTimeoutResponseV1ToZap(
	label string,
	in *CheckAttemptsWithinTimeoutResponseV1,
) zap.Field {
	if in == nil {
		return zap.Reflect(label, in)
	}

	return zap.Dict(
		label,
		zap.Any("VerifiedTimeout", in.GetVerifiedTimeout()),
		zap.Any("VerifiedTry", in.GetVerifiedTry()),
	)
}

func file_inner_processing_grpc_project_transactions_proto_message_GetTransactionLimitRequestV1ToZap(
	label string,
	in *GetTransactionLimitRequestV1,
) zap.Field {
	if in == nil {
		return zap.Reflect(label, in)
	}

	return zap.Dict(
		label,
		zap.Any("ProjectId", in.GetProjectId()),
		zap.Any("TransactionTypeId", in.GetTransactionTypeId()),
	)
}

func file_inner_processing_grpc_project_transactions_proto_message_GetTransactionLimitResponseV1ToZap(
	label string,
	in *GetTransactionLimitResponseV1,
) zap.Field {
	if in == nil {
		return zap.Reflect(label, in)
	}

	return zap.Dict(
		label,
		file_inner_processing_grpc_project_transactions_proto_message_TimestampToZap("CreatedAt", in.GetCreatedAt()),
		file_inner_processing_grpc_project_transactions_proto_message_TimestampToZap("UpdatedAt", in.GetUpdatedAt()),
		zap.Any("Id", in.GetId()),
		zap.Any("ProjectId", in.GetProjectId()),
		zap.Any("TypeId", in.GetTypeId()),
		zap.Any("Timeout", in.GetTimeout()),
		zap.Any("Try", in.GetTry()),
		zap.Any("Amount", in.GetAmount()),
	)
}

var _ ProjectTransactionsServer = (*loggedProjectTransactionsServer)(nil)

func NewLoggedProjectTransactionsServer(srv ProjectTransactionsServer) ProjectTransactionsServer {
	return &loggedProjectTransactionsServer{srv: srv}
}

type loggedProjectTransactionsServer struct {
	UnimplementedProjectTransactionsServer

	srv ProjectTransactionsServer
}

func (s *loggedProjectTransactionsServer) CheckAmountLimit(
	ctx context.Context,
	request *CheckAmountLimitRequestV1,
) (
	response *CheckAmountLimitResponseV1,
	err error,
) {
	label := cntx.Begin(ctx, "ProjectTransactionsServer_CheckAmountLimit")
	defer cntx.End(ctx, label)

	defer func() {
		if err != nil {
			ctxzap.AddFields(ctx, zap.Any(label+"error", err))
			return
		}

		ctxzap.AddFields(
			ctx,
			file_inner_processing_grpc_project_transactions_proto_message_CheckAmountLimitResponseV1ToZap(label+"response", response),
		)
	}()

	ctxzap.AddFields(
		ctx,
		file_inner_processing_grpc_project_transactions_proto_message_CheckAmountLimitRequestV1ToZap(label+"request", request),
	)

	response, err = s.srv.CheckAmountLimit(ctx, request)

	return
}

func (s *loggedProjectTransactionsServer) CheckAttemptsWithinTimeout(
	ctx context.Context,
	request *CheckAttemptsWithinTimeoutRequestV1,
) (
	response *CheckAttemptsWithinTimeoutResponseV1,
	err error,
) {
	label := cntx.Begin(ctx, "ProjectTransactionsServer_CheckAttemptsWithinTimeout")
	defer cntx.End(ctx, label)

	defer func() {
		if err != nil {
			ctxzap.AddFields(ctx, zap.Any(label+"error", err))
			return
		}

		ctxzap.AddFields(
			ctx,
			file_inner_processing_grpc_project_transactions_proto_message_CheckAttemptsWithinTimeoutResponseV1ToZap(label+"response", response),
		)
	}()

	ctxzap.AddFields(
		ctx,
		file_inner_processing_grpc_project_transactions_proto_message_CheckAttemptsWithinTimeoutRequestV1ToZap(label+"request", request),
	)

	response, err = s.srv.CheckAttemptsWithinTimeout(ctx, request)

	return
}

func (s *loggedProjectTransactionsServer) GetTransactionLimit(
	ctx context.Context,
	request *GetTransactionLimitRequestV1,
) (
	response *GetTransactionLimitResponseV1,
	err error,
) {
	label := cntx.Begin(ctx, "ProjectTransactionsServer_GetTransactionLimit")
	defer cntx.End(ctx, label)

	defer func() {
		if err != nil {
			ctxzap.AddFields(ctx, zap.Any(label+"error", err))
			return
		}

		ctxzap.AddFields(
			ctx,
			file_inner_processing_grpc_project_transactions_proto_message_GetTransactionLimitResponseV1ToZap(label+"response", response),
		)
	}()

	ctxzap.AddFields(
		ctx,
		file_inner_processing_grpc_project_transactions_proto_message_GetTransactionLimitRequestV1ToZap(label+"request", request),
	)

	response, err = s.srv.GetTransactionLimit(ctx, request)

	return
}

var _ ProjectTransactionsClient = (*loggedProjectTransactionsClient)(nil)

func NewLoggedProjectTransactionsClient(client ProjectTransactionsClient) ProjectTransactionsClient {
	return &loggedProjectTransactionsClient{client: client}
}

type loggedProjectTransactionsClient struct {
	client ProjectTransactionsClient
}

func (s *loggedProjectTransactionsClient) CheckAmountLimit(
	ctx context.Context,
	request *CheckAmountLimitRequestV1,
	opts ...grpc.CallOption,
) (
	response *CheckAmountLimitResponseV1,
	err error,
) {
	label := cntx.Begin(ctx, "ProjectTransactionsClient_CheckAmountLimit")
	defer cntx.End(ctx, label)

	defer func() {
		if err != nil {
			ctxzap.AddFields(ctx, zap.Any(label+"error", err))
			return
		}

		ctxzap.AddFields(
			ctx,
			file_inner_processing_grpc_project_transactions_proto_message_CheckAmountLimitResponseV1ToZap(label+"response", response),
		)
	}()

	ctxzap.AddFields(
		ctx,
		file_inner_processing_grpc_project_transactions_proto_message_CheckAmountLimitRequestV1ToZap(label+"request", request),
	)

	response, err = s.client.CheckAmountLimit(ctx, request, opts...)

	return
}

func (s *loggedProjectTransactionsClient) CheckAttemptsWithinTimeout(
	ctx context.Context,
	request *CheckAttemptsWithinTimeoutRequestV1,
	opts ...grpc.CallOption,
) (
	response *CheckAttemptsWithinTimeoutResponseV1,
	err error,
) {
	label := cntx.Begin(ctx, "ProjectTransactionsClient_CheckAttemptsWithinTimeout")
	defer cntx.End(ctx, label)

	defer func() {
		if err != nil {
			ctxzap.AddFields(ctx, zap.Any(label+"error", err))
			return
		}

		ctxzap.AddFields(
			ctx,
			file_inner_processing_grpc_project_transactions_proto_message_CheckAttemptsWithinTimeoutResponseV1ToZap(label+"response", response),
		)
	}()

	ctxzap.AddFields(
		ctx,
		file_inner_processing_grpc_project_transactions_proto_message_CheckAttemptsWithinTimeoutRequestV1ToZap(label+"request", request),
	)

	response, err = s.client.CheckAttemptsWithinTimeout(ctx, request, opts...)

	return
}

func (s *loggedProjectTransactionsClient) GetTransactionLimit(
	ctx context.Context,
	request *GetTransactionLimitRequestV1,
	opts ...grpc.CallOption,
) (
	response *GetTransactionLimitResponseV1,
	err error,
) {
	label := cntx.Begin(ctx, "ProjectTransactionsClient_GetTransactionLimit")
	defer cntx.End(ctx, label)

	defer func() {
		if err != nil {
			ctxzap.AddFields(ctx, zap.Any(label+"error", err))
			return
		}

		ctxzap.AddFields(
			ctx,
			file_inner_processing_grpc_project_transactions_proto_message_GetTransactionLimitResponseV1ToZap(label+"response", response),
		)
	}()

	ctxzap.AddFields(
		ctx,
		file_inner_processing_grpc_project_transactions_proto_message_GetTransactionLimitRequestV1ToZap(label+"request", request),
	)

	response, err = s.client.GetTransactionLimit(ctx, request, opts...)

	return
}
