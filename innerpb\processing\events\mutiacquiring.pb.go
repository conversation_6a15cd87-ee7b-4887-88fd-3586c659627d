// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.5
// 	protoc        v5.29.3
// source: inner/processing/events/mutiacquiring.proto

package events

import (
	_ "git.local/sensitive/mvp/pb"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type RefundSettings struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	TransactionId uint64                 `protobuf:"varint,1,opt,name=transaction_id,json=transactionId,proto3" json:"transaction_id,omitempty"`
	Amount        float64                `protobuf:"fixed64,2,opt,name=amount,proto3" json:"amount,omitempty"`
	ProjectId     uint64                 `protobuf:"varint,3,opt,name=project_id,json=projectId,proto3" json:"project_id,omitempty"`
	TerminalId    uint64                 `protobuf:"varint,4,opt,name=terminal_id,json=terminalId,proto3" json:"terminal_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *RefundSettings) Reset() {
	*x = RefundSettings{}
	mi := &file_inner_processing_events_mutiacquiring_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RefundSettings) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RefundSettings) ProtoMessage() {}

func (x *RefundSettings) ProtoReflect() protoreflect.Message {
	mi := &file_inner_processing_events_mutiacquiring_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RefundSettings.ProtoReflect.Descriptor instead.
func (*RefundSettings) Descriptor() ([]byte, []int) {
	return file_inner_processing_events_mutiacquiring_proto_rawDescGZIP(), []int{0}
}

func (x *RefundSettings) GetTransactionId() uint64 {
	if x != nil {
		return x.TransactionId
	}
	return 0
}

func (x *RefundSettings) GetAmount() float64 {
	if x != nil {
		return x.Amount
	}
	return 0
}

func (x *RefundSettings) GetProjectId() uint64 {
	if x != nil {
		return x.ProjectId
	}
	return 0
}

func (x *RefundSettings) GetTerminalId() uint64 {
	if x != nil {
		return x.TerminalId
	}
	return 0
}

var File_inner_processing_events_mutiacquiring_proto protoreflect.FileDescriptor

var file_inner_processing_events_mutiacquiring_proto_rawDesc = string([]byte{
	0x0a, 0x2b, 0x69, 0x6e, 0x6e, 0x65, 0x72, 0x2f, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69,
	0x6e, 0x67, 0x2f, 0x65, 0x76, 0x65, 0x6e, 0x74, 0x73, 0x2f, 0x6d, 0x75, 0x74, 0x69, 0x61, 0x63,
	0x71, 0x75, 0x69, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x16, 0x6d,
	0x76, 0x70, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f, 0x65, 0x76, 0x65, 0x6e, 0x74, 0x73, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x96, 0x01, 0x0a, 0x0e, 0x52, 0x65, 0x66, 0x75, 0x6e, 0x64,
	0x53, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x73, 0x12, 0x25, 0x0a, 0x0e, 0x74, 0x72, 0x61, 0x6e,
	0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04,
	0x52, 0x0d, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x12,
	0x16, 0x0a, 0x06, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x01, 0x52,
	0x06, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x1d, 0x0a, 0x0a, 0x70, 0x72, 0x6f, 0x6a, 0x65,
	0x63, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x04, 0x52, 0x09, 0x70, 0x72, 0x6f,
	0x6a, 0x65, 0x63, 0x74, 0x49, 0x64, 0x12, 0x1f, 0x0a, 0x0b, 0x74, 0x65, 0x72, 0x6d, 0x69, 0x6e,
	0x61, 0x6c, 0x5f, 0x69, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x04, 0x52, 0x0a, 0x74, 0x65, 0x72,
	0x6d, 0x69, 0x6e, 0x61, 0x6c, 0x49, 0x64, 0x3a, 0x05, 0xb8, 0xe9, 0xe4, 0x03, 0x01, 0x42, 0x2f,
	0x5a, 0x2d, 0x67, 0x69, 0x74, 0x2e, 0x6c, 0x6f, 0x63, 0x61, 0x6c, 0x2f, 0x73, 0x65, 0x6e, 0x73,
	0x69, 0x74, 0x69, 0x76, 0x65, 0x2f, 0x69, 0x6e, 0x6e, 0x65, 0x72, 0x70, 0x62, 0x2f, 0x70, 0x72,
	0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x2f, 0x65, 0x76, 0x65, 0x6e, 0x74, 0x73, 0x62,
	0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
})

var (
	file_inner_processing_events_mutiacquiring_proto_rawDescOnce sync.Once
	file_inner_processing_events_mutiacquiring_proto_rawDescData []byte
)

func file_inner_processing_events_mutiacquiring_proto_rawDescGZIP() []byte {
	file_inner_processing_events_mutiacquiring_proto_rawDescOnce.Do(func() {
		file_inner_processing_events_mutiacquiring_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_inner_processing_events_mutiacquiring_proto_rawDesc), len(file_inner_processing_events_mutiacquiring_proto_rawDesc)))
	})
	return file_inner_processing_events_mutiacquiring_proto_rawDescData
}

var file_inner_processing_events_mutiacquiring_proto_msgTypes = make([]protoimpl.MessageInfo, 1)
var file_inner_processing_events_mutiacquiring_proto_goTypes = []any{
	(*RefundSettings)(nil), // 0: RefundSettings
}
var file_inner_processing_events_mutiacquiring_proto_depIdxs = []int32{
	0, // [0:0] is the sub-list for method output_type
	0, // [0:0] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_inner_processing_events_mutiacquiring_proto_init() }
func file_inner_processing_events_mutiacquiring_proto_init() {
	if File_inner_processing_events_mutiacquiring_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_inner_processing_events_mutiacquiring_proto_rawDesc), len(file_inner_processing_events_mutiacquiring_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   1,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_inner_processing_events_mutiacquiring_proto_goTypes,
		DependencyIndexes: file_inner_processing_events_mutiacquiring_proto_depIdxs,
		MessageInfos:      file_inner_processing_events_mutiacquiring_proto_msgTypes,
	}.Build()
	File_inner_processing_events_mutiacquiring_proto = out.File
	file_inner_processing_events_mutiacquiring_proto_goTypes = nil
	file_inner_processing_events_mutiacquiring_proto_depIdxs = nil
}
