// Code generated by protoc-gen-go-mvp. DO NOT EDIT.
// versions:
// - protoc-gen-go-mvp v2.0.1
// created by mvp authors

package grpc

import (
	context "context"
	cntx "git.local/sensitive/mvp/pkg/cntx"
	ctxzap "github.com/grpc-ecosystem/go-grpc-middleware/logging/zap/ctxzap"
	zap "go.uber.org/zap"
	grpc "google.golang.org/grpc"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
)

func file_inner_processing_grpc_commission_proto_message_EmptyToZap(
	label string,
	in *emptypb.Empty,
) zap.Field {
	if in == nil {
		return zap.Reflect(label, in)
	}

	return zap.Dict(
		label,
	)
}

func file_inner_processing_grpc_commission_proto_message_TimestampToZap(
	label string,
	in *timestamppb.Timestamp,
) zap.Field {
	if in == nil {
		return zap.Reflect(label, in)
	}

	return zap.Dict(
		label,
		zap.Any("Seconds", in.GetSeconds()),
		zap.Any("Nanos", in.GetNanos()),
	)
}

func file_inner_processing_grpc_commission_proto_message_CalculateAndSaveUpperCommissionRequestV1ToZap(
	label string,
	in *CalculateAndSaveUpperCommissionRequestV1,
) zap.Field {
	if in == nil {
		return zap.Reflect(label, in)
	}

	return zap.Dict(
		label,
		zap.Any("ProjectId", in.GetProjectId()),
		zap.Any("MerchantId", in.GetMerchantId()),
		zap.Any("AggregatedTypeId", in.GetAggregatedTypeId()),
		zap.Any("TransactionId", in.GetTransactionId()),
		zap.Any("TransactionAmount", in.GetTransactionAmount()),
		file_inner_processing_grpc_commission_proto_message_TimestampToZap("TransactionCreatedAt", in.GetTransactionCreatedAt()),
	)
}

func file_inner_processing_grpc_commission_proto_message_CalculateAndSaveUpperCommissionResponseV1ToZap(
	label string,
	in *CalculateAndSaveUpperCommissionResponseV1,
) zap.Field {
	if in == nil {
		return zap.Reflect(label, in)
	}

	return zap.Dict(
		label,
		zap.Any("CommissionId", in.GetCommissionId()),
		zap.Any("TotalCommissionAmount", in.GetTotalCommissionAmount()),
	)
}

func file_inner_processing_grpc_commission_proto_message_CalculatePrimalAmountRequestV1ToZap(
	label string,
	in *CalculatePrimalAmountRequestV1,
) zap.Field {
	if in == nil {
		return zap.Reflect(label, in)
	}

	return zap.Dict(
		label,
		zap.Any("TransactionID", in.GetTransactionID()),
		zap.Any("Amount", in.GetAmount()),
	)
}

func file_inner_processing_grpc_commission_proto_message_CalculatePrimalAmountResponseV1ToZap(
	label string,
	in *CalculatePrimalAmountResponseV1,
) zap.Field {
	if in == nil {
		return zap.Reflect(label, in)
	}

	return zap.Dict(
		label,
		zap.Any("CommissionId", in.GetCommissionId()),
		zap.Any("PrimalAmount", in.GetPrimalAmount()),
	)
}

func file_inner_processing_grpc_commission_proto_message_CommissionV1ToZap(
	label string,
	in *CommissionV1,
) zap.Field {
	if in == nil {
		return zap.Reflect(label, in)
	}

	return zap.Dict(
		label,
		zap.Any("TaxProjectId", in.GetTaxProjectId()),
		zap.Any("Amount", in.GetAmount()),
	)
}

func file_inner_processing_grpc_commission_proto_message_FiscalizeUpperCommissionRequestV1ToZap(
	label string,
	in *FiscalizeUpperCommissionRequestV1,
) zap.Field {
	if in == nil {
		return zap.Reflect(label, in)
	}

	return zap.Dict(
		label,
		zap.Any("TransactionId", in.GetTransactionId()),
	)
}

func file_inner_processing_grpc_commission_proto_message_GetCommissionByTransactionIDRequestV1ToZap(
	label string,
	in *GetCommissionByTransactionIDRequestV1,
) zap.Field {
	if in == nil {
		return zap.Reflect(label, in)
	}

	return zap.Dict(
		label,
		zap.Any("TransactionId", in.GetTransactionId()),
	)
}

func file_inner_processing_grpc_commission_proto_message_GetCommissionByTransactionIDResponseV1ToZap(
	label string,
	in *GetCommissionByTransactionIDResponseV1,
) zap.Field {
	if in == nil {
		return zap.Reflect(label, in)
	}

	return zap.Dict(
		label,
		zap.Any("ProjectLowerCommissionAmount", in.GetProjectLowerCommissionAmount()),
		zap.Any("ProjectUpperCommissionAmount", in.GetProjectUpperCommissionAmount()),
	)
}

func file_inner_processing_grpc_commission_proto_message_GetCommissionForMainBalanceRequestV1ToZap(
	label string,
	in *GetCommissionForMainBalanceRequestV1,
) zap.Field {
	if in == nil {
		return zap.Reflect(label, in)
	}

	return zap.Dict(
		label,
		zap.Any("TransactionId", in.GetTransactionId()),
	)
}

func file_inner_processing_grpc_commission_proto_message_GetCommissionForMainBalanceResponseV1ToZap(
	label string,
	in *GetCommissionForMainBalanceResponseV1,
) zap.Field {
	if in == nil {
		return zap.Reflect(label, in)
	}

	return zap.Dict(
		label,
		zap.Any("TarlanCommission", in.GetTarlanCommission()),
		zap.Any("AcquirerCommission", in.GetAcquirerCommission()),
	)
}

func file_inner_processing_grpc_commission_proto_message_TransactionCommissionResponseV1ToZap(
	label string,
	in *TransactionCommissionResponseV1,
) zap.Field {
	if in == nil {
		return zap.Reflect(label, in)
	}

	return zap.Dict(
		label,
		zap.Any("Id", in.GetId()),
		zap.Any("AcquirerCommissionId", in.GetAcquirerCommissionId()),
		zap.Any("ProjectLowerCommissionId", in.GetProjectLowerCommissionId()),
		zap.Any("ProjectUpperCommissionId", in.GetProjectUpperCommissionId()),
		zap.Any("AcquirerCommissionAmount", in.GetAcquirerCommissionAmount()),
		zap.Any("ProjectLowerCommissionAmount", in.GetProjectLowerCommissionAmount()),
		zap.Any("ProjectUpperCommissionAmount", in.GetProjectUpperCommissionAmount()),
	)
}

func file_inner_processing_grpc_commission_proto_message_UpdateCommissionForCreditBalanceRequestV1ToZap(
	label string,
	in *UpdateCommissionForCreditBalanceRequestV1,
) zap.Field {
	if in == nil {
		return zap.Reflect(label, in)
	}

	return zap.Dict(
		label,
		zap.Any("TransactionId", in.GetTransactionId()),
		zap.Any("Amount", in.GetAmount()),
		zap.Any("ProjectId", in.GetProjectId()),
		file_inner_processing_grpc_commission_proto_message_TimestampToZap("TransactionCreatedAt", in.GetTransactionCreatedAt()),
	)
}

func file_inner_processing_grpc_commission_proto_message_UpdateCommissionForCreditBalanceResponseV1ToZap(
	label string,
	in *UpdateCommissionForCreditBalanceResponseV1,
) zap.Field {
	if in == nil {
		return zap.Reflect(label, in)
	}

	return zap.Dict(
		label,
		zap.Any("TarlanCommission", in.GetTarlanCommission()),
		zap.Any("AcquirerCommission", in.GetAcquirerCommission()),
	)
}

var _ CommissionServer = (*loggedCommissionServer)(nil)

func NewLoggedCommissionServer(srv CommissionServer) CommissionServer {
	return &loggedCommissionServer{srv: srv}
}

type loggedCommissionServer struct {
	UnimplementedCommissionServer

	srv CommissionServer
}

func (s *loggedCommissionServer) CalculateAndSaveUpperCommission(
	ctx context.Context,
	request *CalculateAndSaveUpperCommissionRequestV1,
) (
	response *CalculateAndSaveUpperCommissionResponseV1,
	err error,
) {
	label := cntx.Begin(ctx, "CommissionServer_CalculateAndSaveUpperCommission")
	defer cntx.End(ctx, label)

	defer func() {
		if err != nil {
			ctxzap.AddFields(ctx, zap.Any(label+"error", err))
			return
		}

		ctxzap.AddFields(
			ctx,
			file_inner_processing_grpc_commission_proto_message_CalculateAndSaveUpperCommissionResponseV1ToZap(label+"response", response),
		)
	}()

	ctxzap.AddFields(
		ctx,
		file_inner_processing_grpc_commission_proto_message_CalculateAndSaveUpperCommissionRequestV1ToZap(label+"request", request),
	)

	response, err = s.srv.CalculateAndSaveUpperCommission(ctx, request)

	return
}

func (s *loggedCommissionServer) GetCommissionForMainBalance(
	ctx context.Context,
	request *GetCommissionForMainBalanceRequestV1,
) (
	response *GetCommissionForMainBalanceResponseV1,
	err error,
) {
	label := cntx.Begin(ctx, "CommissionServer_GetCommissionForMainBalance")
	defer cntx.End(ctx, label)

	defer func() {
		if err != nil {
			ctxzap.AddFields(ctx, zap.Any(label+"error", err))
			return
		}

		ctxzap.AddFields(
			ctx,
			file_inner_processing_grpc_commission_proto_message_GetCommissionForMainBalanceResponseV1ToZap(label+"response", response),
		)
	}()

	ctxzap.AddFields(
		ctx,
		file_inner_processing_grpc_commission_proto_message_GetCommissionForMainBalanceRequestV1ToZap(label+"request", request),
	)

	response, err = s.srv.GetCommissionForMainBalance(ctx, request)

	return
}

func (s *loggedCommissionServer) UpdateCommissionForCreditBalance(
	ctx context.Context,
	request *UpdateCommissionForCreditBalanceRequestV1,
) (
	response *UpdateCommissionForCreditBalanceResponseV1,
	err error,
) {
	label := cntx.Begin(ctx, "CommissionServer_UpdateCommissionForCreditBalance")
	defer cntx.End(ctx, label)

	defer func() {
		if err != nil {
			ctxzap.AddFields(ctx, zap.Any(label+"error", err))
			return
		}

		ctxzap.AddFields(
			ctx,
			file_inner_processing_grpc_commission_proto_message_UpdateCommissionForCreditBalanceResponseV1ToZap(label+"response", response),
		)
	}()

	ctxzap.AddFields(
		ctx,
		file_inner_processing_grpc_commission_proto_message_UpdateCommissionForCreditBalanceRequestV1ToZap(label+"request", request),
	)

	response, err = s.srv.UpdateCommissionForCreditBalance(ctx, request)

	return
}

func (s *loggedCommissionServer) CalculatePayInPrimalAmount(
	ctx context.Context,
	request *CalculatePrimalAmountRequestV1,
) (
	response *CalculatePrimalAmountResponseV1,
	err error,
) {
	label := cntx.Begin(ctx, "CommissionServer_CalculatePayInPrimalAmount")
	defer cntx.End(ctx, label)

	defer func() {
		if err != nil {
			ctxzap.AddFields(ctx, zap.Any(label+"error", err))
			return
		}

		ctxzap.AddFields(
			ctx,
			file_inner_processing_grpc_commission_proto_message_CalculatePrimalAmountResponseV1ToZap(label+"response", response),
		)
	}()

	ctxzap.AddFields(
		ctx,
		file_inner_processing_grpc_commission_proto_message_CalculatePrimalAmountRequestV1ToZap(label+"request", request),
	)

	response, err = s.srv.CalculatePayInPrimalAmount(ctx, request)

	return
}

func (s *loggedCommissionServer) CalculatePayOutPrimalAmount(
	ctx context.Context,
	request *CalculatePrimalAmountRequestV1,
) (
	response *CalculatePrimalAmountResponseV1,
	err error,
) {
	label := cntx.Begin(ctx, "CommissionServer_CalculatePayOutPrimalAmount")
	defer cntx.End(ctx, label)

	defer func() {
		if err != nil {
			ctxzap.AddFields(ctx, zap.Any(label+"error", err))
			return
		}

		ctxzap.AddFields(
			ctx,
			file_inner_processing_grpc_commission_proto_message_CalculatePrimalAmountResponseV1ToZap(label+"response", response),
		)
	}()

	ctxzap.AddFields(
		ctx,
		file_inner_processing_grpc_commission_proto_message_CalculatePrimalAmountRequestV1ToZap(label+"request", request),
	)

	response, err = s.srv.CalculatePayOutPrimalAmount(ctx, request)

	return
}

func (s *loggedCommissionServer) FiscalizeUpperCommission(
	ctx context.Context,
	request *FiscalizeUpperCommissionRequestV1,
) (
	response *emptypb.Empty,
	err error,
) {
	label := cntx.Begin(ctx, "CommissionServer_FiscalizeUpperCommission")
	defer cntx.End(ctx, label)

	defer func() {
		if err != nil {
			ctxzap.AddFields(ctx, zap.Any(label+"error", err))
			return
		}

		ctxzap.AddFields(
			ctx,
			file_inner_processing_grpc_commission_proto_message_EmptyToZap(label+"response", response),
		)
	}()

	ctxzap.AddFields(
		ctx,
		file_inner_processing_grpc_commission_proto_message_FiscalizeUpperCommissionRequestV1ToZap(label+"request", request),
	)

	response, err = s.srv.FiscalizeUpperCommission(ctx, request)

	return
}

func (s *loggedCommissionServer) GetCommissionByTransactionID(
	ctx context.Context,
	request *GetCommissionByTransactionIDRequestV1,
) (
	response *GetCommissionByTransactionIDResponseV1,
	err error,
) {
	label := cntx.Begin(ctx, "CommissionServer_GetCommissionByTransactionID")
	defer cntx.End(ctx, label)

	defer func() {
		if err != nil {
			ctxzap.AddFields(ctx, zap.Any(label+"error", err))
			return
		}

		ctxzap.AddFields(
			ctx,
			file_inner_processing_grpc_commission_proto_message_GetCommissionByTransactionIDResponseV1ToZap(label+"response", response),
		)
	}()

	ctxzap.AddFields(
		ctx,
		file_inner_processing_grpc_commission_proto_message_GetCommissionByTransactionIDRequestV1ToZap(label+"request", request),
	)

	response, err = s.srv.GetCommissionByTransactionID(ctx, request)

	return
}

var _ CommissionClient = (*loggedCommissionClient)(nil)

func NewLoggedCommissionClient(client CommissionClient) CommissionClient {
	return &loggedCommissionClient{client: client}
}

type loggedCommissionClient struct {
	client CommissionClient
}

func (s *loggedCommissionClient) CalculateAndSaveUpperCommission(
	ctx context.Context,
	request *CalculateAndSaveUpperCommissionRequestV1,
	opts ...grpc.CallOption,
) (
	response *CalculateAndSaveUpperCommissionResponseV1,
	err error,
) {
	label := cntx.Begin(ctx, "CommissionClient_CalculateAndSaveUpperCommission")
	defer cntx.End(ctx, label)

	defer func() {
		if err != nil {
			ctxzap.AddFields(ctx, zap.Any(label+"error", err))
			return
		}

		ctxzap.AddFields(
			ctx,
			file_inner_processing_grpc_commission_proto_message_CalculateAndSaveUpperCommissionResponseV1ToZap(label+"response", response),
		)
	}()

	ctxzap.AddFields(
		ctx,
		file_inner_processing_grpc_commission_proto_message_CalculateAndSaveUpperCommissionRequestV1ToZap(label+"request", request),
	)

	response, err = s.client.CalculateAndSaveUpperCommission(ctx, request, opts...)

	return
}

func (s *loggedCommissionClient) GetCommissionForMainBalance(
	ctx context.Context,
	request *GetCommissionForMainBalanceRequestV1,
	opts ...grpc.CallOption,
) (
	response *GetCommissionForMainBalanceResponseV1,
	err error,
) {
	label := cntx.Begin(ctx, "CommissionClient_GetCommissionForMainBalance")
	defer cntx.End(ctx, label)

	defer func() {
		if err != nil {
			ctxzap.AddFields(ctx, zap.Any(label+"error", err))
			return
		}

		ctxzap.AddFields(
			ctx,
			file_inner_processing_grpc_commission_proto_message_GetCommissionForMainBalanceResponseV1ToZap(label+"response", response),
		)
	}()

	ctxzap.AddFields(
		ctx,
		file_inner_processing_grpc_commission_proto_message_GetCommissionForMainBalanceRequestV1ToZap(label+"request", request),
	)

	response, err = s.client.GetCommissionForMainBalance(ctx, request, opts...)

	return
}

func (s *loggedCommissionClient) UpdateCommissionForCreditBalance(
	ctx context.Context,
	request *UpdateCommissionForCreditBalanceRequestV1,
	opts ...grpc.CallOption,
) (
	response *UpdateCommissionForCreditBalanceResponseV1,
	err error,
) {
	label := cntx.Begin(ctx, "CommissionClient_UpdateCommissionForCreditBalance")
	defer cntx.End(ctx, label)

	defer func() {
		if err != nil {
			ctxzap.AddFields(ctx, zap.Any(label+"error", err))
			return
		}

		ctxzap.AddFields(
			ctx,
			file_inner_processing_grpc_commission_proto_message_UpdateCommissionForCreditBalanceResponseV1ToZap(label+"response", response),
		)
	}()

	ctxzap.AddFields(
		ctx,
		file_inner_processing_grpc_commission_proto_message_UpdateCommissionForCreditBalanceRequestV1ToZap(label+"request", request),
	)

	response, err = s.client.UpdateCommissionForCreditBalance(ctx, request, opts...)

	return
}

func (s *loggedCommissionClient) CalculatePayInPrimalAmount(
	ctx context.Context,
	request *CalculatePrimalAmountRequestV1,
	opts ...grpc.CallOption,
) (
	response *CalculatePrimalAmountResponseV1,
	err error,
) {
	label := cntx.Begin(ctx, "CommissionClient_CalculatePayInPrimalAmount")
	defer cntx.End(ctx, label)

	defer func() {
		if err != nil {
			ctxzap.AddFields(ctx, zap.Any(label+"error", err))
			return
		}

		ctxzap.AddFields(
			ctx,
			file_inner_processing_grpc_commission_proto_message_CalculatePrimalAmountResponseV1ToZap(label+"response", response),
		)
	}()

	ctxzap.AddFields(
		ctx,
		file_inner_processing_grpc_commission_proto_message_CalculatePrimalAmountRequestV1ToZap(label+"request", request),
	)

	response, err = s.client.CalculatePayInPrimalAmount(ctx, request, opts...)

	return
}

func (s *loggedCommissionClient) CalculatePayOutPrimalAmount(
	ctx context.Context,
	request *CalculatePrimalAmountRequestV1,
	opts ...grpc.CallOption,
) (
	response *CalculatePrimalAmountResponseV1,
	err error,
) {
	label := cntx.Begin(ctx, "CommissionClient_CalculatePayOutPrimalAmount")
	defer cntx.End(ctx, label)

	defer func() {
		if err != nil {
			ctxzap.AddFields(ctx, zap.Any(label+"error", err))
			return
		}

		ctxzap.AddFields(
			ctx,
			file_inner_processing_grpc_commission_proto_message_CalculatePrimalAmountResponseV1ToZap(label+"response", response),
		)
	}()

	ctxzap.AddFields(
		ctx,
		file_inner_processing_grpc_commission_proto_message_CalculatePrimalAmountRequestV1ToZap(label+"request", request),
	)

	response, err = s.client.CalculatePayOutPrimalAmount(ctx, request, opts...)

	return
}

func (s *loggedCommissionClient) FiscalizeUpperCommission(
	ctx context.Context,
	request *FiscalizeUpperCommissionRequestV1,
	opts ...grpc.CallOption,
) (
	response *emptypb.Empty,
	err error,
) {
	label := cntx.Begin(ctx, "CommissionClient_FiscalizeUpperCommission")
	defer cntx.End(ctx, label)

	defer func() {
		if err != nil {
			ctxzap.AddFields(ctx, zap.Any(label+"error", err))
			return
		}

		ctxzap.AddFields(
			ctx,
			file_inner_processing_grpc_commission_proto_message_EmptyToZap(label+"response", response),
		)
	}()

	ctxzap.AddFields(
		ctx,
		file_inner_processing_grpc_commission_proto_message_FiscalizeUpperCommissionRequestV1ToZap(label+"request", request),
	)

	response, err = s.client.FiscalizeUpperCommission(ctx, request, opts...)

	return
}

func (s *loggedCommissionClient) GetCommissionByTransactionID(
	ctx context.Context,
	request *GetCommissionByTransactionIDRequestV1,
	opts ...grpc.CallOption,
) (
	response *GetCommissionByTransactionIDResponseV1,
	err error,
) {
	label := cntx.Begin(ctx, "CommissionClient_GetCommissionByTransactionID")
	defer cntx.End(ctx, label)

	defer func() {
		if err != nil {
			ctxzap.AddFields(ctx, zap.Any(label+"error", err))
			return
		}

		ctxzap.AddFields(
			ctx,
			file_inner_processing_grpc_commission_proto_message_GetCommissionByTransactionIDResponseV1ToZap(label+"response", response),
		)
	}()

	ctxzap.AddFields(
		ctx,
		file_inner_processing_grpc_commission_proto_message_GetCommissionByTransactionIDRequestV1ToZap(label+"request", request),
	)

	response, err = s.client.GetCommissionByTransactionID(ctx, request, opts...)

	return
}
