// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.5
// 	protoc        v5.29.3
// source: inner/processing/grpc/epay.proto

package grpc

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
	reflect "reflect"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

var File_inner_processing_grpc_epay_proto protoreflect.FileDescriptor

var file_inner_processing_grpc_epay_proto_rawDesc = string([]byte{
	0x0a, 0x20, 0x69, 0x6e, 0x6e, 0x65, 0x72, 0x2f, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69,
	0x6e, 0x67, 0x2f, 0x67, 0x72, 0x70, 0x63, 0x2f, 0x65, 0x70, 0x61, 0x79, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x12, 0x14, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x2e, 0x65,
	0x70, 0x61, 0x79, 0x2e, 0x65, 0x70, 0x61, 0x79, 0x1a, 0x2a, 0x69, 0x6e, 0x6e, 0x65, 0x72, 0x2f,
	0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x2f, 0x67, 0x72, 0x70, 0x63, 0x2f,
	0x6d, 0x75, 0x6c, 0x74, 0x69, 0x61, 0x63, 0x71, 0x75, 0x69, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1b, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x65, 0x6d, 0x70, 0x74, 0x79, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x32, 0xbe, 0x13, 0x0a, 0x04, 0x45, 0x70, 0x61, 0x79, 0x12, 0x82, 0x01, 0x0a, 0x05, 0x50,
	0x61, 0x79, 0x49, 0x6e, 0x12, 0x3a, 0x2e, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e,
	0x67, 0x2e, 0x6d, 0x75, 0x6c, 0x74, 0x69, 0x61, 0x63, 0x71, 0x75, 0x69, 0x72, 0x69, 0x6e, 0x67,
	0x2e, 0x6d, 0x75, 0x6c, 0x74, 0x69, 0x61, 0x63, 0x71, 0x75, 0x69, 0x72, 0x69, 0x6e, 0x67, 0x2e,
	0x50, 0x61, 0x79, 0x49, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x44, 0x61, 0x74, 0x61,
	0x1a, 0x3b, 0x2e, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x2e, 0x6d, 0x75,
	0x6c, 0x74, 0x69, 0x61, 0x63, 0x71, 0x75, 0x69, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x6d, 0x75, 0x6c,
	0x74, 0x69, 0x61, 0x63, 0x71, 0x75, 0x69, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x50, 0x61, 0x79, 0x49,
	0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x44, 0x61, 0x74, 0x61, 0x22, 0x00, 0x12,
	0x92, 0x01, 0x0a, 0x0d, 0x4f, 0x6e, 0x65, 0x43, 0x6c, 0x69, 0x63, 0x6b, 0x50, 0x61, 0x79, 0x49,
	0x6e, 0x12, 0x42, 0x2e, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x2e, 0x6d,
	0x75, 0x6c, 0x74, 0x69, 0x61, 0x63, 0x71, 0x75, 0x69, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x6d, 0x75,
	0x6c, 0x74, 0x69, 0x61, 0x63, 0x71, 0x75, 0x69, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x4f, 0x6e, 0x65,
	0x43, 0x6c, 0x69, 0x63, 0x6b, 0x50, 0x61, 0x79, 0x49, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x44, 0x61, 0x74, 0x61, 0x1a, 0x3b, 0x2e, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69,
	0x6e, 0x67, 0x2e, 0x6d, 0x75, 0x6c, 0x74, 0x69, 0x61, 0x63, 0x71, 0x75, 0x69, 0x72, 0x69, 0x6e,
	0x67, 0x2e, 0x6d, 0x75, 0x6c, 0x74, 0x69, 0x61, 0x63, 0x71, 0x75, 0x69, 0x72, 0x69, 0x6e, 0x67,
	0x2e, 0x50, 0x61, 0x79, 0x49, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x44, 0x61,
	0x74, 0x61, 0x22, 0x00, 0x12, 0x8f, 0x01, 0x0a, 0x0e, 0x54, 0x68, 0x72, 0x65, 0x65, 0x44, 0x53,
	0x43, 0x6f, 0x6e, 0x66, 0x69, 0x72, 0x6d, 0x12, 0x3c, 0x2e, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73,
	0x73, 0x69, 0x6e, 0x67, 0x2e, 0x6d, 0x75, 0x6c, 0x74, 0x69, 0x61, 0x63, 0x71, 0x75, 0x69, 0x72,
	0x69, 0x6e, 0x67, 0x2e, 0x6d, 0x75, 0x6c, 0x74, 0x69, 0x61, 0x63, 0x71, 0x75, 0x69, 0x72, 0x69,
	0x6e, 0x67, 0x2e, 0x54, 0x68, 0x72, 0x65, 0x65, 0x44, 0x53, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x44, 0x61, 0x74, 0x61, 0x1a, 0x3d, 0x2e, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69,
	0x6e, 0x67, 0x2e, 0x6d, 0x75, 0x6c, 0x74, 0x69, 0x61, 0x63, 0x71, 0x75, 0x69, 0x72, 0x69, 0x6e,
	0x67, 0x2e, 0x6d, 0x75, 0x6c, 0x74, 0x69, 0x61, 0x63, 0x71, 0x75, 0x69, 0x72, 0x69, 0x6e, 0x67,
	0x2e, 0x54, 0x68, 0x72, 0x65, 0x65, 0x44, 0x53, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x44, 0x61, 0x74, 0x61, 0x22, 0x00, 0x12, 0x92, 0x01, 0x0a, 0x0d, 0x54, 0x68, 0x72, 0x65, 0x65,
	0x44, 0x53, 0x52, 0x65, 0x73, 0x75, 0x6d, 0x65, 0x12, 0x3e, 0x2e, 0x70, 0x72, 0x6f, 0x63, 0x65,
	0x73, 0x73, 0x69, 0x6e, 0x67, 0x2e, 0x6d, 0x75, 0x6c, 0x74, 0x69, 0x61, 0x63, 0x71, 0x75, 0x69,
	0x72, 0x69, 0x6e, 0x67, 0x2e, 0x6d, 0x75, 0x6c, 0x74, 0x69, 0x61, 0x63, 0x71, 0x75, 0x69, 0x72,
	0x69, 0x6e, 0x67, 0x2e, 0x54, 0x68, 0x72, 0x65, 0x65, 0x44, 0x53, 0x52, 0x65, 0x73, 0x75, 0x6d,
	0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x3f, 0x2e, 0x70, 0x72, 0x6f, 0x63, 0x65,
	0x73, 0x73, 0x69, 0x6e, 0x67, 0x2e, 0x6d, 0x75, 0x6c, 0x74, 0x69, 0x61, 0x63, 0x71, 0x75, 0x69,
	0x72, 0x69, 0x6e, 0x67, 0x2e, 0x6d, 0x75, 0x6c, 0x74, 0x69, 0x61, 0x63, 0x71, 0x75, 0x69, 0x72,
	0x69, 0x6e, 0x67, 0x2e, 0x54, 0x68, 0x72, 0x65, 0x65, 0x44, 0x53, 0x52, 0x65, 0x73, 0x75, 0x6d,
	0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x85, 0x01, 0x0a, 0x06,
	0x50, 0x61, 0x79, 0x4f, 0x75, 0x74, 0x12, 0x3b, 0x2e, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73,
	0x69, 0x6e, 0x67, 0x2e, 0x6d, 0x75, 0x6c, 0x74, 0x69, 0x61, 0x63, 0x71, 0x75, 0x69, 0x72, 0x69,
	0x6e, 0x67, 0x2e, 0x6d, 0x75, 0x6c, 0x74, 0x69, 0x61, 0x63, 0x71, 0x75, 0x69, 0x72, 0x69, 0x6e,
	0x67, 0x2e, 0x50, 0x61, 0x79, 0x4f, 0x75, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x44,
	0x61, 0x74, 0x61, 0x1a, 0x3c, 0x2e, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67,
	0x2e, 0x6d, 0x75, 0x6c, 0x74, 0x69, 0x61, 0x63, 0x71, 0x75, 0x69, 0x72, 0x69, 0x6e, 0x67, 0x2e,
	0x6d, 0x75, 0x6c, 0x74, 0x69, 0x61, 0x63, 0x71, 0x75, 0x69, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x50,
	0x61, 0x79, 0x4f, 0x75, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x44, 0x61, 0x74,
	0x61, 0x22, 0x00, 0x12, 0xad, 0x01, 0x0a, 0x18, 0x47, 0x65, 0x74, 0x42, 0x61, 0x6e, 0x6b, 0x54,
	0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x12, 0x46, 0x2e, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x2e, 0x6d, 0x75,
	0x6c, 0x74, 0x69, 0x61, 0x63, 0x71, 0x75, 0x69, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x6d, 0x75, 0x6c,
	0x74, 0x69, 0x61, 0x63, 0x71, 0x75, 0x69, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x42, 0x61, 0x6e, 0x6b,
	0x54, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x47, 0x2e, 0x70, 0x72, 0x6f, 0x63, 0x65,
	0x73, 0x73, 0x69, 0x6e, 0x67, 0x2e, 0x6d, 0x75, 0x6c, 0x74, 0x69, 0x61, 0x63, 0x71, 0x75, 0x69,
	0x72, 0x69, 0x6e, 0x67, 0x2e, 0x6d, 0x75, 0x6c, 0x74, 0x69, 0x61, 0x63, 0x71, 0x75, 0x69, 0x72,
	0x69, 0x6e, 0x67, 0x2e, 0x42, 0x61, 0x6e, 0x6b, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74,
	0x69, 0x6f, 0x6e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x22, 0x00, 0x12, 0xcb, 0x01, 0x0a, 0x22, 0x47, 0x65, 0x74, 0x42, 0x61, 0x6e, 0x6b, 0x54,
	0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x55, 0x6e, 0x66, 0x6f, 0x72, 0x6d, 0x61, 0x74, 0x65, 0x64, 0x12, 0x50, 0x2e, 0x70, 0x72, 0x6f,
	0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x2e, 0x6d, 0x75, 0x6c, 0x74, 0x69, 0x61, 0x63, 0x71,
	0x75, 0x69, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x6d, 0x75, 0x6c, 0x74, 0x69, 0x61, 0x63, 0x71, 0x75,
	0x69, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x42, 0x61, 0x6e, 0x6b, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x61,
	0x63, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x55, 0x6e, 0x66, 0x6f, 0x72,
	0x6d, 0x61, 0x74, 0x65, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x51, 0x2e, 0x70,
	0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x2e, 0x6d, 0x75, 0x6c, 0x74, 0x69, 0x61,
	0x63, 0x71, 0x75, 0x69, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x6d, 0x75, 0x6c, 0x74, 0x69, 0x61, 0x63,
	0x71, 0x75, 0x69, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x42, 0x61, 0x6e, 0x6b, 0x54, 0x72, 0x61, 0x6e,
	0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x55, 0x6e, 0x66,
	0x6f, 0x72, 0x6d, 0x61, 0x74, 0x65, 0x64, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22,
	0x00, 0x12, 0x7d, 0x0a, 0x06, 0x52, 0x65, 0x66, 0x75, 0x6e, 0x64, 0x12, 0x37, 0x2e, 0x70, 0x72,
	0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x2e, 0x6d, 0x75, 0x6c, 0x74, 0x69, 0x61, 0x63,
	0x71, 0x75, 0x69, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x6d, 0x75, 0x6c, 0x74, 0x69, 0x61, 0x63, 0x71,
	0x75, 0x69, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x52, 0x65, 0x66, 0x75, 0x6e, 0x64, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x1a, 0x38, 0x2e, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e,
	0x67, 0x2e, 0x6d, 0x75, 0x6c, 0x74, 0x69, 0x61, 0x63, 0x71, 0x75, 0x69, 0x72, 0x69, 0x6e, 0x67,
	0x2e, 0x6d, 0x75, 0x6c, 0x74, 0x69, 0x61, 0x63, 0x71, 0x75, 0x69, 0x72, 0x69, 0x6e, 0x67, 0x2e,
	0x52, 0x65, 0x66, 0x75, 0x6e, 0x64, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00,
	0x12, 0x8e, 0x01, 0x0a, 0x09, 0x47, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x50, 0x61, 0x79, 0x12, 0x3e,
	0x2e, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x2e, 0x6d, 0x75, 0x6c, 0x74,
	0x69, 0x61, 0x63, 0x71, 0x75, 0x69, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x6d, 0x75, 0x6c, 0x74, 0x69,
	0x61, 0x63, 0x71, 0x75, 0x69, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x47, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x50, 0x61, 0x79, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x44, 0x61, 0x74, 0x61, 0x1a, 0x3f,
	0x2e, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x2e, 0x6d, 0x75, 0x6c, 0x74,
	0x69, 0x61, 0x63, 0x71, 0x75, 0x69, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x6d, 0x75, 0x6c, 0x74, 0x69,
	0x61, 0x63, 0x71, 0x75, 0x69, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x47, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x50, 0x61, 0x79, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x44, 0x61, 0x74, 0x61, 0x22,
	0x00, 0x12, 0x8b, 0x01, 0x0a, 0x08, 0x41, 0x70, 0x70, 0x6c, 0x65, 0x50, 0x61, 0x79, 0x12, 0x3d,
	0x2e, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x2e, 0x6d, 0x75, 0x6c, 0x74,
	0x69, 0x61, 0x63, 0x71, 0x75, 0x69, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x6d, 0x75, 0x6c, 0x74, 0x69,
	0x61, 0x63, 0x71, 0x75, 0x69, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x41, 0x70, 0x70, 0x6c, 0x65, 0x50,
	0x61, 0x79, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x44, 0x61, 0x74, 0x61, 0x1a, 0x3e, 0x2e,
	0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x2e, 0x6d, 0x75, 0x6c, 0x74, 0x69,
	0x61, 0x63, 0x71, 0x75, 0x69, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x6d, 0x75, 0x6c, 0x74, 0x69, 0x61,
	0x63, 0x71, 0x75, 0x69, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x41, 0x70, 0x70, 0x6c, 0x65, 0x50, 0x61,
	0x79, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x44, 0x61, 0x74, 0x61, 0x22, 0x00, 0x12,
	0x92, 0x01, 0x0a, 0x0d, 0x54, 0x77, 0x6f, 0x53, 0x74, 0x61, 0x67, 0x65, 0x50, 0x61, 0x79, 0x49,
	0x6e, 0x12, 0x3e, 0x2e, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x2e, 0x6d,
	0x75, 0x6c, 0x74, 0x69, 0x61, 0x63, 0x71, 0x75, 0x69, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x6d, 0x75,
	0x6c, 0x74, 0x69, 0x61, 0x63, 0x71, 0x75, 0x69, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x54, 0x77, 0x6f,
	0x53, 0x74, 0x61, 0x67, 0x65, 0x50, 0x61, 0x79, 0x49, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x1a, 0x3f, 0x2e, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x2e, 0x6d,
	0x75, 0x6c, 0x74, 0x69, 0x61, 0x63, 0x71, 0x75, 0x69, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x6d, 0x75,
	0x6c, 0x74, 0x69, 0x61, 0x63, 0x71, 0x75, 0x69, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x54, 0x77, 0x6f,
	0x53, 0x74, 0x61, 0x67, 0x65, 0x50, 0x61, 0x79, 0x49, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x22, 0x00, 0x12, 0x7d, 0x0a, 0x06, 0x43, 0x68, 0x61, 0x72, 0x67, 0x65, 0x12, 0x37,
	0x2e, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x2e, 0x6d, 0x75, 0x6c, 0x74,
	0x69, 0x61, 0x63, 0x71, 0x75, 0x69, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x6d, 0x75, 0x6c, 0x74, 0x69,
	0x61, 0x63, 0x71, 0x75, 0x69, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x43, 0x68, 0x61, 0x72, 0x67, 0x65,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x38, 0x2e, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73,
	0x73, 0x69, 0x6e, 0x67, 0x2e, 0x6d, 0x75, 0x6c, 0x74, 0x69, 0x61, 0x63, 0x71, 0x75, 0x69, 0x72,
	0x69, 0x6e, 0x67, 0x2e, 0x6d, 0x75, 0x6c, 0x74, 0x69, 0x61, 0x63, 0x71, 0x75, 0x69, 0x72, 0x69,
	0x6e, 0x67, 0x2e, 0x43, 0x68, 0x61, 0x72, 0x67, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x22, 0x00, 0x12, 0x7d, 0x0a, 0x06, 0x43, 0x61, 0x6e, 0x63, 0x65, 0x6c, 0x12, 0x37, 0x2e,
	0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x2e, 0x6d, 0x75, 0x6c, 0x74, 0x69,
	0x61, 0x63, 0x71, 0x75, 0x69, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x6d, 0x75, 0x6c, 0x74, 0x69, 0x61,
	0x63, 0x71, 0x75, 0x69, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x43, 0x61, 0x6e, 0x63, 0x65, 0x6c, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x38, 0x2e, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73,
	0x69, 0x6e, 0x67, 0x2e, 0x6d, 0x75, 0x6c, 0x74, 0x69, 0x61, 0x63, 0x71, 0x75, 0x69, 0x72, 0x69,
	0x6e, 0x67, 0x2e, 0x6d, 0x75, 0x6c, 0x74, 0x69, 0x61, 0x63, 0x71, 0x75, 0x69, 0x72, 0x69, 0x6e,
	0x67, 0x2e, 0x43, 0x61, 0x6e, 0x63, 0x65, 0x6c, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x22, 0x00, 0x12, 0x86, 0x01, 0x0a, 0x09, 0x4d, 0x61, 0x6b, 0x65, 0x54, 0x6f, 0x6b, 0x65, 0x6e,
	0x12, 0x3a, 0x2e, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x2e, 0x6d, 0x75,
	0x6c, 0x74, 0x69, 0x61, 0x63, 0x71, 0x75, 0x69, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x6d, 0x75, 0x6c,
	0x74, 0x69, 0x61, 0x63, 0x71, 0x75, 0x69, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x50, 0x61, 0x79, 0x49,
	0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x44, 0x61, 0x74, 0x61, 0x1a, 0x3b, 0x2e, 0x70,
	0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x2e, 0x6d, 0x75, 0x6c, 0x74, 0x69, 0x61,
	0x63, 0x71, 0x75, 0x69, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x6d, 0x75, 0x6c, 0x74, 0x69, 0x61, 0x63,
	0x71, 0x75, 0x69, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x50, 0x61, 0x79, 0x49, 0x6e, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x44, 0x61, 0x74, 0x61, 0x22, 0x00, 0x12, 0x7a, 0x0a, 0x15, 0x47,
	0x65, 0x74, 0x41, 0x63, 0x71, 0x75, 0x69, 0x72, 0x65, 0x72, 0x49, 0x64, 0x65, 0x6e, 0x74, 0x69,
	0x66, 0x69, 0x65, 0x72, 0x12, 0x16, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x1a, 0x47, 0x2e, 0x70,
	0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x2e, 0x6d, 0x75, 0x6c, 0x74, 0x69, 0x61,
	0x63, 0x71, 0x75, 0x69, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x6d, 0x75, 0x6c, 0x74, 0x69, 0x61, 0x63,
	0x71, 0x75, 0x69, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x47, 0x65, 0x74, 0x41, 0x63, 0x71, 0x75, 0x69,
	0x72, 0x65, 0x72, 0x49, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x66, 0x69, 0x65, 0x72, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x9b, 0x01, 0x0a, 0x10, 0x52, 0x65, 0x73, 0x6f,
	0x6c, 0x76, 0x65, 0x56, 0x69, 0x73, 0x61, 0x41, 0x6c, 0x69, 0x61, 0x73, 0x12, 0x41, 0x2e, 0x70,
	0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x2e, 0x6d, 0x75, 0x6c, 0x74, 0x69, 0x61,
	0x63, 0x71, 0x75, 0x69, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x6d, 0x75, 0x6c, 0x74, 0x69, 0x61, 0x63,
	0x71, 0x75, 0x69, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x52, 0x65, 0x73, 0x6f, 0x6c, 0x76, 0x65, 0x56,
	0x69, 0x73, 0x61, 0x41, 0x6c, 0x69, 0x61, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a,
	0x42, 0x2e, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x2e, 0x6d, 0x75, 0x6c,
	0x74, 0x69, 0x61, 0x63, 0x71, 0x75, 0x69, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x6d, 0x75, 0x6c, 0x74,
	0x69, 0x61, 0x63, 0x71, 0x75, 0x69, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x52, 0x65, 0x73, 0x6f, 0x6c,
	0x76, 0x65, 0x56, 0x69, 0x73, 0x61, 0x41, 0x6c, 0x69, 0x61, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x9a, 0x01, 0x0a, 0x0d, 0x50, 0x61, 0x79, 0x4f, 0x75, 0x74,
	0x42, 0x79, 0x50, 0x68, 0x6f, 0x6e, 0x65, 0x12, 0x42, 0x2e, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73,
	0x73, 0x69, 0x6e, 0x67, 0x2e, 0x6d, 0x75, 0x6c, 0x74, 0x69, 0x61, 0x63, 0x71, 0x75, 0x69, 0x72,
	0x69, 0x6e, 0x67, 0x2e, 0x6d, 0x75, 0x6c, 0x74, 0x69, 0x61, 0x63, 0x71, 0x75, 0x69, 0x72, 0x69,
	0x6e, 0x67, 0x2e, 0x50, 0x61, 0x79, 0x4f, 0x75, 0x74, 0x42, 0x79, 0x50, 0x68, 0x6f, 0x6e, 0x65,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x44, 0x61, 0x74, 0x61, 0x1a, 0x43, 0x2e, 0x70, 0x72,
	0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x2e, 0x6d, 0x75, 0x6c, 0x74, 0x69, 0x61, 0x63,
	0x71, 0x75, 0x69, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x6d, 0x75, 0x6c, 0x74, 0x69, 0x61, 0x63, 0x71,
	0x75, 0x69, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x50, 0x61, 0x79, 0x4f, 0x75, 0x74, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x42, 0x79, 0x50, 0x68, 0x6f, 0x6e, 0x65, 0x44, 0x61, 0x74, 0x61,
	0x22, 0x00, 0x42, 0x2d, 0x5a, 0x2b, 0x67, 0x69, 0x74, 0x2e, 0x6c, 0x6f, 0x63, 0x61, 0x6c, 0x2f,
	0x73, 0x65, 0x6e, 0x73, 0x69, 0x74, 0x69, 0x76, 0x65, 0x2f, 0x69, 0x6e, 0x6e, 0x65, 0x72, 0x70,
	0x62, 0x2f, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x2f, 0x67, 0x72, 0x70,
	0x63, 0x62, 0x08, 0x65, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x70, 0xe8, 0x07,
})

var file_inner_processing_grpc_epay_proto_goTypes = []any{
	(*PayInRequestData)(nil),                        // 0: processing.multiacquiring.multiacquiring.PayInRequestData
	(*OneClickPayInRequestData)(nil),                // 1: processing.multiacquiring.multiacquiring.OneClickPayInRequestData
	(*ThreeDSRequestData)(nil),                      // 2: processing.multiacquiring.multiacquiring.ThreeDSRequestData
	(*ThreeDSResumeRequest)(nil),                    // 3: processing.multiacquiring.multiacquiring.ThreeDSResumeRequest
	(*PayOutRequestData)(nil),                       // 4: processing.multiacquiring.multiacquiring.PayOutRequestData
	(*BankTransactionStatusRequest)(nil),            // 5: processing.multiacquiring.multiacquiring.BankTransactionStatusRequest
	(*BankTransactionStatusUnformatedRequest)(nil),  // 6: processing.multiacquiring.multiacquiring.BankTransactionStatusUnformatedRequest
	(*RefundRequest)(nil),                           // 7: processing.multiacquiring.multiacquiring.RefundRequest
	(*GooglePayRequestData)(nil),                    // 8: processing.multiacquiring.multiacquiring.GooglePayRequestData
	(*ApplePayRequestData)(nil),                     // 9: processing.multiacquiring.multiacquiring.ApplePayRequestData
	(*TwoStagePayInRequest)(nil),                    // 10: processing.multiacquiring.multiacquiring.TwoStagePayInRequest
	(*ChargeRequest)(nil),                           // 11: processing.multiacquiring.multiacquiring.ChargeRequest
	(*CancelRequest)(nil),                           // 12: processing.multiacquiring.multiacquiring.CancelRequest
	(*emptypb.Empty)(nil),                           // 13: google.protobuf.Empty
	(*ResolveVisaAliasRequest)(nil),                 // 14: processing.multiacquiring.multiacquiring.ResolveVisaAliasRequest
	(*PayOutByPhoneRequestData)(nil),                // 15: processing.multiacquiring.multiacquiring.PayOutByPhoneRequestData
	(*PayInResponseData)(nil),                       // 16: processing.multiacquiring.multiacquiring.PayInResponseData
	(*ThreeDSResponseData)(nil),                     // 17: processing.multiacquiring.multiacquiring.ThreeDSResponseData
	(*ThreeDSResumeResponse)(nil),                   // 18: processing.multiacquiring.multiacquiring.ThreeDSResumeResponse
	(*PayOutResponseData)(nil),                      // 19: processing.multiacquiring.multiacquiring.PayOutResponseData
	(*BankTransactionStatusResponse)(nil),           // 20: processing.multiacquiring.multiacquiring.BankTransactionStatusResponse
	(*BankTransactionStatusUnformatedResponse)(nil), // 21: processing.multiacquiring.multiacquiring.BankTransactionStatusUnformatedResponse
	(*RefundResponse)(nil),                          // 22: processing.multiacquiring.multiacquiring.RefundResponse
	(*GooglePayResponseData)(nil),                   // 23: processing.multiacquiring.multiacquiring.GooglePayResponseData
	(*ApplePayResponseData)(nil),                    // 24: processing.multiacquiring.multiacquiring.ApplePayResponseData
	(*TwoStagePayInResponse)(nil),                   // 25: processing.multiacquiring.multiacquiring.TwoStagePayInResponse
	(*ChargeResponse)(nil),                          // 26: processing.multiacquiring.multiacquiring.ChargeResponse
	(*CancelResponse)(nil),                          // 27: processing.multiacquiring.multiacquiring.CancelResponse
	(*GetAcquirerIdentifierResponse)(nil),           // 28: processing.multiacquiring.multiacquiring.GetAcquirerIdentifierResponse
	(*ResolveVisaAliasResponse)(nil),                // 29: processing.multiacquiring.multiacquiring.ResolveVisaAliasResponse
	(*PayOutResponseByPhoneData)(nil),               // 30: processing.multiacquiring.multiacquiring.PayOutResponseByPhoneData
}
var file_inner_processing_grpc_epay_proto_depIdxs = []int32{
	0,  // 0: processing.epay.epay.Epay.PayIn:input_type -> processing.multiacquiring.multiacquiring.PayInRequestData
	1,  // 1: processing.epay.epay.Epay.OneClickPayIn:input_type -> processing.multiacquiring.multiacquiring.OneClickPayInRequestData
	2,  // 2: processing.epay.epay.Epay.ThreeDSConfirm:input_type -> processing.multiacquiring.multiacquiring.ThreeDSRequestData
	3,  // 3: processing.epay.epay.Epay.ThreeDSResume:input_type -> processing.multiacquiring.multiacquiring.ThreeDSResumeRequest
	4,  // 4: processing.epay.epay.Epay.PayOut:input_type -> processing.multiacquiring.multiacquiring.PayOutRequestData
	5,  // 5: processing.epay.epay.Epay.GetBankTransactionStatus:input_type -> processing.multiacquiring.multiacquiring.BankTransactionStatusRequest
	6,  // 6: processing.epay.epay.Epay.GetBankTransactionStatusUnformated:input_type -> processing.multiacquiring.multiacquiring.BankTransactionStatusUnformatedRequest
	7,  // 7: processing.epay.epay.Epay.Refund:input_type -> processing.multiacquiring.multiacquiring.RefundRequest
	8,  // 8: processing.epay.epay.Epay.GooglePay:input_type -> processing.multiacquiring.multiacquiring.GooglePayRequestData
	9,  // 9: processing.epay.epay.Epay.ApplePay:input_type -> processing.multiacquiring.multiacquiring.ApplePayRequestData
	10, // 10: processing.epay.epay.Epay.TwoStagePayIn:input_type -> processing.multiacquiring.multiacquiring.TwoStagePayInRequest
	11, // 11: processing.epay.epay.Epay.Charge:input_type -> processing.multiacquiring.multiacquiring.ChargeRequest
	12, // 12: processing.epay.epay.Epay.Cancel:input_type -> processing.multiacquiring.multiacquiring.CancelRequest
	0,  // 13: processing.epay.epay.Epay.MakeToken:input_type -> processing.multiacquiring.multiacquiring.PayInRequestData
	13, // 14: processing.epay.epay.Epay.GetAcquirerIdentifier:input_type -> google.protobuf.Empty
	14, // 15: processing.epay.epay.Epay.ResolveVisaAlias:input_type -> processing.multiacquiring.multiacquiring.ResolveVisaAliasRequest
	15, // 16: processing.epay.epay.Epay.PayOutByPhone:input_type -> processing.multiacquiring.multiacquiring.PayOutByPhoneRequestData
	16, // 17: processing.epay.epay.Epay.PayIn:output_type -> processing.multiacquiring.multiacquiring.PayInResponseData
	16, // 18: processing.epay.epay.Epay.OneClickPayIn:output_type -> processing.multiacquiring.multiacquiring.PayInResponseData
	17, // 19: processing.epay.epay.Epay.ThreeDSConfirm:output_type -> processing.multiacquiring.multiacquiring.ThreeDSResponseData
	18, // 20: processing.epay.epay.Epay.ThreeDSResume:output_type -> processing.multiacquiring.multiacquiring.ThreeDSResumeResponse
	19, // 21: processing.epay.epay.Epay.PayOut:output_type -> processing.multiacquiring.multiacquiring.PayOutResponseData
	20, // 22: processing.epay.epay.Epay.GetBankTransactionStatus:output_type -> processing.multiacquiring.multiacquiring.BankTransactionStatusResponse
	21, // 23: processing.epay.epay.Epay.GetBankTransactionStatusUnformated:output_type -> processing.multiacquiring.multiacquiring.BankTransactionStatusUnformatedResponse
	22, // 24: processing.epay.epay.Epay.Refund:output_type -> processing.multiacquiring.multiacquiring.RefundResponse
	23, // 25: processing.epay.epay.Epay.GooglePay:output_type -> processing.multiacquiring.multiacquiring.GooglePayResponseData
	24, // 26: processing.epay.epay.Epay.ApplePay:output_type -> processing.multiacquiring.multiacquiring.ApplePayResponseData
	25, // 27: processing.epay.epay.Epay.TwoStagePayIn:output_type -> processing.multiacquiring.multiacquiring.TwoStagePayInResponse
	26, // 28: processing.epay.epay.Epay.Charge:output_type -> processing.multiacquiring.multiacquiring.ChargeResponse
	27, // 29: processing.epay.epay.Epay.Cancel:output_type -> processing.multiacquiring.multiacquiring.CancelResponse
	16, // 30: processing.epay.epay.Epay.MakeToken:output_type -> processing.multiacquiring.multiacquiring.PayInResponseData
	28, // 31: processing.epay.epay.Epay.GetAcquirerIdentifier:output_type -> processing.multiacquiring.multiacquiring.GetAcquirerIdentifierResponse
	29, // 32: processing.epay.epay.Epay.ResolveVisaAlias:output_type -> processing.multiacquiring.multiacquiring.ResolveVisaAliasResponse
	30, // 33: processing.epay.epay.Epay.PayOutByPhone:output_type -> processing.multiacquiring.multiacquiring.PayOutResponseByPhoneData
	17, // [17:34] is the sub-list for method output_type
	0,  // [0:17] is the sub-list for method input_type
	0,  // [0:0] is the sub-list for extension type_name
	0,  // [0:0] is the sub-list for extension extendee
	0,  // [0:0] is the sub-list for field type_name
}

func init() { file_inner_processing_grpc_epay_proto_init() }
func file_inner_processing_grpc_epay_proto_init() {
	if File_inner_processing_grpc_epay_proto != nil {
		return
	}
	file_inner_processing_grpc_multiacquiring_proto_init()
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_inner_processing_grpc_epay_proto_rawDesc), len(file_inner_processing_grpc_epay_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   0,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_inner_processing_grpc_epay_proto_goTypes,
		DependencyIndexes: file_inner_processing_grpc_epay_proto_depIdxs,
	}.Build()
	File_inner_processing_grpc_epay_proto = out.File
	file_inner_processing_grpc_epay_proto_goTypes = nil
	file_inner_processing_grpc_epay_proto_depIdxs = nil
}
