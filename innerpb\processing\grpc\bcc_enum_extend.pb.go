// Code generated by protoc-gen-go-mvp. DO NOT EDIT.
// versions:
// - protoc-gen-go-mvp v2.0.1
// created by mvp authors

package grpc

import (
	math "math"
	strings "strings"
)

func (val ResponseCodePayIn) Synonym() ResponseCodePayIn {
	if _, ok := ResponseCodePayIn_name[int32(val)]; ok {
		return val
	}

	return ResponseCodePayIn(math.MinInt32)
}

func (val ResponseCodePayIn) Int() int {
	return int(val.Synonym())
}

func (val ResponseCodePayIn) IntPtr() *int {
	result := int(val.Synonym())
	return &result
}

func (val ResponseCodePayIn) Int32() int32 {
	return int32(val.Synonym())
}

func (val ResponseCodePayIn) Int32Ptr() *int32 {
	result := int32(val.Synonym())
	return &result
}

func (val ResponseCodePayIn) Int64() int64 {
	return int64(val.Synonym())
}

func (val ResponseCodePayIn) Int64Ptr() *int64 {
	result := int64(val.Synonym())
	return &result
}

func (val ResponseCodePayIn) Uint() uint {
	return uint(val.Synonym())
}

func (val ResponseCodePayIn) UintPtr() *uint {
	result := uint(val.Synonym())
	return &result
}

func (val ResponseCodePayIn) Uint32() uint32 {
	return uint32(val.Synonym())
}

func (val ResponseCodePayIn) Uint32Ptr() *uint32 {
	result := uint32(val.Synonym())
	return &result
}

func (val ResponseCodePayIn) Uint64() uint64 {
	return uint64(val.Synonym())
}

func (val ResponseCodePayIn) Uint64Ptr() *uint64 {
	result := uint64(val.Synonym())
	return &result
}

func (val ResponseCodePayIn) IsKnown() bool {
	return val.Synonym() != ResponseCodePayIn(math.MinInt32)
}

func ConvertIntToResponseCodePayIn(in int) ResponseCodePayIn {
	return ResponseCodePayIn(in).Synonym()
}

func ConvertUintToResponseCodePayIn(in uint) ResponseCodePayIn {
	return ResponseCodePayIn(in).Synonym()
}

func ConvertInt32ToResponseCodePayIn(in int32) ResponseCodePayIn {
	return ResponseCodePayIn(in).Synonym()
}

func ConvertUint32ToResponseCodePayIn(in uint32) ResponseCodePayIn {
	return ResponseCodePayIn(in).Synonym()
}

func ConvertInt64ToResponseCodePayIn(in int64) ResponseCodePayIn {
	return ResponseCodePayIn(in).Synonym()
}

func ConvertUint64ToResponseCodePayIn(in uint64) ResponseCodePayIn {
	return ResponseCodePayIn(in).Synonym()
}

var ResponseCodePayIn_Lower_value = map[string]ResponseCodePayIn{
	"0":  0,
	"3":  1,
	"10": 2,
	"17": 3,
	"18": 4,
	"19": 5,
	"22": 6,
	"5":  7,
	"13": 8,
	"21": 9,
	"7":  10,
	"6":  11,
	"2":  12,
	"4":  13,
	"11": 14,
	"15": 15,
	"1":  16,
	"8":  17,
	"12": 18,
	"20": 19,
	"9":  20,
	"14": 21,
	"16": 22,
}

func ConvertStringToResponseCodePayIn(in string) ResponseCodePayIn {
	if result, ok := ResponseCodePayIn_value[in]; ok {
		return ResponseCodePayIn(result)
	}

	if result, ok := ResponseCodePayIn_Lower_value[strings.ToLower(in)]; ok {
		return ResponseCodePayIn(result)
	}

	return ResponseCodePayIn(math.MinInt32)
}

var SliceResponseCodePayInConvert *sliceResponseCodePayInConvert

type sliceResponseCodePayInConvert struct{}

func (*sliceResponseCodePayInConvert) Synonym(in []ResponseCodePayIn) []ResponseCodePayIn {
	result := make([]ResponseCodePayIn, len(in))
	for i, v := range in {
		result[i] = v.Synonym()
	}

	return result
}

func (*sliceResponseCodePayInConvert) Int32(in []ResponseCodePayIn) []int32 {
	result := make([]int32, len(in))
	for i, v := range in {
		result[i] = v.Int32()
	}

	return result
}

func (*sliceResponseCodePayInConvert) Uint32(in []ResponseCodePayIn) []uint32 {
	result := make([]uint32, len(in))
	for i, v := range in {
		result[i] = v.Uint32()
	}

	return result
}

func (*sliceResponseCodePayInConvert) Uint64(in []ResponseCodePayIn) []uint64 {
	result := make([]uint64, len(in))
	for i, v := range in {
		result[i] = v.Uint64()
	}

	return result
}

func (*sliceResponseCodePayInConvert) Int64(in []ResponseCodePayIn) []int64 {
	result := make([]int64, len(in))
	for i, v := range in {
		result[i] = v.Int64()
	}

	return result
}

func (*sliceResponseCodePayInConvert) Uint(in []ResponseCodePayIn) []uint {
	result := make([]uint, len(in))
	for i, v := range in {
		result[i] = v.Uint()
	}

	return result
}

func (*sliceResponseCodePayInConvert) Int(in []ResponseCodePayIn) []int {
	result := make([]int, len(in))
	for i, v := range in {
		result[i] = v.Int()
	}

	return result
}

func (*sliceResponseCodePayInConvert) IsKnown(in []ResponseCodePayIn) []bool {
	result := make([]bool, len(in))
	for i, v := range in {
		result[i] = v.IsKnown()
	}

	return result
}

func (*sliceResponseCodePayInConvert) ConvertIntToResponseCodePayIn(in []int) []ResponseCodePayIn {
	result := make([]ResponseCodePayIn, len(in))
	for i, v := range in {
		result[i] = ConvertIntToResponseCodePayIn(v)
	}

	return result
}

func (*sliceResponseCodePayInConvert) ConvertUintToResponseCodePayIn(in []uint) []ResponseCodePayIn {
	result := make([]ResponseCodePayIn, len(in))
	for i, v := range in {
		result[i] = ConvertUintToResponseCodePayIn(v)
	}

	return result
}

func (*sliceResponseCodePayInConvert) ConvertInt32ToResponseCodePayIn(in []int32) []ResponseCodePayIn {
	result := make([]ResponseCodePayIn, len(in))
	for i, v := range in {
		result[i] = ConvertInt32ToResponseCodePayIn(v)
	}

	return result
}

func (*sliceResponseCodePayInConvert) ConvertUint32ToResponseCodePayIn(in []uint32) []ResponseCodePayIn {
	result := make([]ResponseCodePayIn, len(in))
	for i, v := range in {
		result[i] = ConvertUint32ToResponseCodePayIn(v)
	}

	return result
}

func (*sliceResponseCodePayInConvert) ConvertInt64ToResponseCodePayIn(in []int64) []ResponseCodePayIn {
	result := make([]ResponseCodePayIn, len(in))
	for i, v := range in {
		result[i] = ConvertInt64ToResponseCodePayIn(v)
	}

	return result
}

func (*sliceResponseCodePayInConvert) ConvertUint64ToResponseCodePayIn(in []uint64) []ResponseCodePayIn {
	result := make([]ResponseCodePayIn, len(in))
	for i, v := range in {
		result[i] = ConvertUint64ToResponseCodePayIn(v)
	}

	return result
}

func (*sliceResponseCodePayInConvert) ConvertStringToResponseCodePayIn(in []string) []ResponseCodePayIn {
	result := make([]ResponseCodePayIn, len(in))
	for i, v := range in {
		result[i] = ConvertStringToResponseCodePayIn(v)
	}

	return result
}

func NewResponseCodePayInUsage() *ResponseCodePayInUsage {
	return &ResponseCodePayInUsage{
		enumMap: map[ResponseCodePayIn]bool{
			ResponseCodePayIn_ResponseCodePayInSuccess:               false,
			ResponseCodePayIn_ResponseCodePayInErrorProcessing:       false,
			ResponseCodePayIn_ResponseCodePayIn3DSWaiting:            false,
			ResponseCodePayIn_ResponseCodePayInRequestCardForm:       false,
			ResponseCodePayIn_ResponseCodePayInVeResU:                false,
			ResponseCodePayIn_ResponseCodePayInVeResA:                false,
			ResponseCodePayIn_ResponseCodePayInRequestFingerprint:    false,
			ResponseCodePayIn_ResponseCodePayIn3DSResponse:           false,
			ResponseCodePayIn_ResponseCodePayInMastercardInstallment: false,
			ResponseCodePayIn_ResponseCodePayInSCA:                   false,
			ResponseCodePayIn_ResponseCodePayInRepeatAuthError:       false,
			ResponseCodePayIn_ResponseCodePayInRepeatDeclined:        false,
			ResponseCodePayIn_ResponseCodePayInDeclined:              false,
			ResponseCodePayIn_ResponseCodePayInInfoMessage:           false,
			ResponseCodePayIn_ResponseCodePayInCheckCardBySum:        false,
			ResponseCodePayIn_ResponseCodePayInUPIRequest:            false,
			ResponseCodePayIn_ResponseCodePayInRepeatTransaction:     false,
			ResponseCodePayIn_ResponseCodePayInNoResponse:            false,
			ResponseCodePayIn_ResponseCodePayInInstallmentAutoCancel: false,
			ResponseCodePayIn_ResponseCodePayInMerchantSumCheck:      false,
			ResponseCodePayIn_ResponseCodePayInInstallment:           false,
			ResponseCodePayIn_ResponseCodePayInInstallmentCancel:     false,
			ResponseCodePayIn_ResponseCodePayInUserConfirmation:      false,
		},
	}
}

func IsResponseCodePayIn(target ResponseCodePayIn, matches ...ResponseCodePayIn) bool {
	for _, v := range matches {
		if target == v {
			return true
		}
	}

	return false
}

type ResponseCodePayInUsage struct {
	enumMap map[ResponseCodePayIn]bool
}

func (u *ResponseCodePayInUsage) Use(slice ...ResponseCodePayIn) *ResponseCodePayInUsage {
	for _, in := range slice {
		u.enumMap[in] = true
	}

	return u
}

func (u *ResponseCodePayInUsage) Check() []string {
	var result []string
	for k, v := range u.enumMap {
		if v {
			continue
		}

		result = append(result, k.String())
	}

	return result
}

func (u *ResponseCodePayInUsage) ResponseCodePayIn_ResponseCodePayInSuccess() ResponseCodePayIn {
	u.Use(ResponseCodePayIn_ResponseCodePayInSuccess)
	return ResponseCodePayIn_ResponseCodePayInSuccess
}

func (u *ResponseCodePayInUsage) ResponseCodePayIn_ResponseCodePayInErrorProcessing() ResponseCodePayIn {
	u.Use(ResponseCodePayIn_ResponseCodePayInErrorProcessing)
	return ResponseCodePayIn_ResponseCodePayInErrorProcessing
}

func (u *ResponseCodePayInUsage) ResponseCodePayIn_ResponseCodePayIn3DSWaiting() ResponseCodePayIn {
	u.Use(ResponseCodePayIn_ResponseCodePayIn3DSWaiting)
	return ResponseCodePayIn_ResponseCodePayIn3DSWaiting
}

func (u *ResponseCodePayInUsage) ResponseCodePayIn_ResponseCodePayInRequestCardForm() ResponseCodePayIn {
	u.Use(ResponseCodePayIn_ResponseCodePayInRequestCardForm)
	return ResponseCodePayIn_ResponseCodePayInRequestCardForm
}

func (u *ResponseCodePayInUsage) ResponseCodePayIn_ResponseCodePayInVeResU() ResponseCodePayIn {
	u.Use(ResponseCodePayIn_ResponseCodePayInVeResU)
	return ResponseCodePayIn_ResponseCodePayInVeResU
}

func (u *ResponseCodePayInUsage) ResponseCodePayIn_ResponseCodePayInVeResA() ResponseCodePayIn {
	u.Use(ResponseCodePayIn_ResponseCodePayInVeResA)
	return ResponseCodePayIn_ResponseCodePayInVeResA
}

func (u *ResponseCodePayInUsage) ResponseCodePayIn_ResponseCodePayInRequestFingerprint() ResponseCodePayIn {
	u.Use(ResponseCodePayIn_ResponseCodePayInRequestFingerprint)
	return ResponseCodePayIn_ResponseCodePayInRequestFingerprint
}

func (u *ResponseCodePayInUsage) ResponseCodePayIn_ResponseCodePayIn3DSResponse() ResponseCodePayIn {
	u.Use(ResponseCodePayIn_ResponseCodePayIn3DSResponse)
	return ResponseCodePayIn_ResponseCodePayIn3DSResponse
}

func (u *ResponseCodePayInUsage) ResponseCodePayIn_ResponseCodePayInMastercardInstallment() ResponseCodePayIn {
	u.Use(ResponseCodePayIn_ResponseCodePayInMastercardInstallment)
	return ResponseCodePayIn_ResponseCodePayInMastercardInstallment
}

func (u *ResponseCodePayInUsage) ResponseCodePayIn_ResponseCodePayInSCA() ResponseCodePayIn {
	u.Use(ResponseCodePayIn_ResponseCodePayInSCA)
	return ResponseCodePayIn_ResponseCodePayInSCA
}

func (u *ResponseCodePayInUsage) ResponseCodePayIn_ResponseCodePayInRepeatAuthError() ResponseCodePayIn {
	u.Use(ResponseCodePayIn_ResponseCodePayInRepeatAuthError)
	return ResponseCodePayIn_ResponseCodePayInRepeatAuthError
}

func (u *ResponseCodePayInUsage) ResponseCodePayIn_ResponseCodePayInRepeatDeclined() ResponseCodePayIn {
	u.Use(ResponseCodePayIn_ResponseCodePayInRepeatDeclined)
	return ResponseCodePayIn_ResponseCodePayInRepeatDeclined
}

func (u *ResponseCodePayInUsage) ResponseCodePayIn_ResponseCodePayInDeclined() ResponseCodePayIn {
	u.Use(ResponseCodePayIn_ResponseCodePayInDeclined)
	return ResponseCodePayIn_ResponseCodePayInDeclined
}

func (u *ResponseCodePayInUsage) ResponseCodePayIn_ResponseCodePayInInfoMessage() ResponseCodePayIn {
	u.Use(ResponseCodePayIn_ResponseCodePayInInfoMessage)
	return ResponseCodePayIn_ResponseCodePayInInfoMessage
}

func (u *ResponseCodePayInUsage) ResponseCodePayIn_ResponseCodePayInCheckCardBySum() ResponseCodePayIn {
	u.Use(ResponseCodePayIn_ResponseCodePayInCheckCardBySum)
	return ResponseCodePayIn_ResponseCodePayInCheckCardBySum
}

func (u *ResponseCodePayInUsage) ResponseCodePayIn_ResponseCodePayInUPIRequest() ResponseCodePayIn {
	u.Use(ResponseCodePayIn_ResponseCodePayInUPIRequest)
	return ResponseCodePayIn_ResponseCodePayInUPIRequest
}

func (u *ResponseCodePayInUsage) ResponseCodePayIn_ResponseCodePayInRepeatTransaction() ResponseCodePayIn {
	u.Use(ResponseCodePayIn_ResponseCodePayInRepeatTransaction)
	return ResponseCodePayIn_ResponseCodePayInRepeatTransaction
}

func (u *ResponseCodePayInUsage) ResponseCodePayIn_ResponseCodePayInNoResponse() ResponseCodePayIn {
	u.Use(ResponseCodePayIn_ResponseCodePayInNoResponse)
	return ResponseCodePayIn_ResponseCodePayInNoResponse
}

func (u *ResponseCodePayInUsage) ResponseCodePayIn_ResponseCodePayInInstallmentAutoCancel() ResponseCodePayIn {
	u.Use(ResponseCodePayIn_ResponseCodePayInInstallmentAutoCancel)
	return ResponseCodePayIn_ResponseCodePayInInstallmentAutoCancel
}

func (u *ResponseCodePayInUsage) ResponseCodePayIn_ResponseCodePayInMerchantSumCheck() ResponseCodePayIn {
	u.Use(ResponseCodePayIn_ResponseCodePayInMerchantSumCheck)
	return ResponseCodePayIn_ResponseCodePayInMerchantSumCheck
}

func (u *ResponseCodePayInUsage) ResponseCodePayIn_ResponseCodePayInInstallment() ResponseCodePayIn {
	u.Use(ResponseCodePayIn_ResponseCodePayInInstallment)
	return ResponseCodePayIn_ResponseCodePayInInstallment
}

func (u *ResponseCodePayInUsage) ResponseCodePayIn_ResponseCodePayInInstallmentCancel() ResponseCodePayIn {
	u.Use(ResponseCodePayIn_ResponseCodePayInInstallmentCancel)
	return ResponseCodePayIn_ResponseCodePayInInstallmentCancel
}

func (u *ResponseCodePayInUsage) ResponseCodePayIn_ResponseCodePayInUserConfirmation() ResponseCodePayIn {
	u.Use(ResponseCodePayIn_ResponseCodePayInUserConfirmation)
	return ResponseCodePayIn_ResponseCodePayInUserConfirmation
}

func (val ResponseCodePayOut) Synonym() ResponseCodePayOut {
	if _, ok := ResponseCodePayOut_name[int32(val)]; ok {
		return val
	}

	return ResponseCodePayOut(math.MinInt32)
}

func (val ResponseCodePayOut) Int() int {
	return int(val.Synonym())
}

func (val ResponseCodePayOut) IntPtr() *int {
	result := int(val.Synonym())
	return &result
}

func (val ResponseCodePayOut) Int32() int32 {
	return int32(val.Synonym())
}

func (val ResponseCodePayOut) Int32Ptr() *int32 {
	result := int32(val.Synonym())
	return &result
}

func (val ResponseCodePayOut) Int64() int64 {
	return int64(val.Synonym())
}

func (val ResponseCodePayOut) Int64Ptr() *int64 {
	result := int64(val.Synonym())
	return &result
}

func (val ResponseCodePayOut) Uint() uint {
	return uint(val.Synonym())
}

func (val ResponseCodePayOut) UintPtr() *uint {
	result := uint(val.Synonym())
	return &result
}

func (val ResponseCodePayOut) Uint32() uint32 {
	return uint32(val.Synonym())
}

func (val ResponseCodePayOut) Uint32Ptr() *uint32 {
	result := uint32(val.Synonym())
	return &result
}

func (val ResponseCodePayOut) Uint64() uint64 {
	return uint64(val.Synonym())
}

func (val ResponseCodePayOut) Uint64Ptr() *uint64 {
	result := uint64(val.Synonym())
	return &result
}

func (val ResponseCodePayOut) IsKnown() bool {
	return val.Synonym() != ResponseCodePayOut(math.MinInt32)
}

func ConvertIntToResponseCodePayOut(in int) ResponseCodePayOut {
	return ResponseCodePayOut(in).Synonym()
}

func ConvertUintToResponseCodePayOut(in uint) ResponseCodePayOut {
	return ResponseCodePayOut(in).Synonym()
}

func ConvertInt32ToResponseCodePayOut(in int32) ResponseCodePayOut {
	return ResponseCodePayOut(in).Synonym()
}

func ConvertUint32ToResponseCodePayOut(in uint32) ResponseCodePayOut {
	return ResponseCodePayOut(in).Synonym()
}

func ConvertInt64ToResponseCodePayOut(in int64) ResponseCodePayOut {
	return ResponseCodePayOut(in).Synonym()
}

func ConvertUint64ToResponseCodePayOut(in uint64) ResponseCodePayOut {
	return ResponseCodePayOut(in).Synonym()
}

var ResponseCodePayOut_Lower_value = map[string]ResponseCodePayOut{
	"400:":           0,
	"401:":           1,
	"429:":           2,
	"500:":           3,
	"501:":           4,
	"503:":           5,
	"200:declined":   6,
	"200:rejected":   7,
	"418:":           8,
	"200:active":     9,
	"200:waiting":    10,
	"200:loaded":     11,
	"200:suspended":  12,
	"200:retry":      13,
	"200:rejectout":  14,
	"200:rejectsent": 15,
	"200:completed":  16,
}

func ConvertStringToResponseCodePayOut(in string) ResponseCodePayOut {
	if result, ok := ResponseCodePayOut_value[in]; ok {
		return ResponseCodePayOut(result)
	}

	if result, ok := ResponseCodePayOut_Lower_value[strings.ToLower(in)]; ok {
		return ResponseCodePayOut(result)
	}

	return ResponseCodePayOut(math.MinInt32)
}

var SliceResponseCodePayOutConvert *sliceResponseCodePayOutConvert

type sliceResponseCodePayOutConvert struct{}

func (*sliceResponseCodePayOutConvert) Synonym(in []ResponseCodePayOut) []ResponseCodePayOut {
	result := make([]ResponseCodePayOut, len(in))
	for i, v := range in {
		result[i] = v.Synonym()
	}

	return result
}

func (*sliceResponseCodePayOutConvert) Int32(in []ResponseCodePayOut) []int32 {
	result := make([]int32, len(in))
	for i, v := range in {
		result[i] = v.Int32()
	}

	return result
}

func (*sliceResponseCodePayOutConvert) Uint32(in []ResponseCodePayOut) []uint32 {
	result := make([]uint32, len(in))
	for i, v := range in {
		result[i] = v.Uint32()
	}

	return result
}

func (*sliceResponseCodePayOutConvert) Uint64(in []ResponseCodePayOut) []uint64 {
	result := make([]uint64, len(in))
	for i, v := range in {
		result[i] = v.Uint64()
	}

	return result
}

func (*sliceResponseCodePayOutConvert) Int64(in []ResponseCodePayOut) []int64 {
	result := make([]int64, len(in))
	for i, v := range in {
		result[i] = v.Int64()
	}

	return result
}

func (*sliceResponseCodePayOutConvert) Uint(in []ResponseCodePayOut) []uint {
	result := make([]uint, len(in))
	for i, v := range in {
		result[i] = v.Uint()
	}

	return result
}

func (*sliceResponseCodePayOutConvert) Int(in []ResponseCodePayOut) []int {
	result := make([]int, len(in))
	for i, v := range in {
		result[i] = v.Int()
	}

	return result
}

func (*sliceResponseCodePayOutConvert) IsKnown(in []ResponseCodePayOut) []bool {
	result := make([]bool, len(in))
	for i, v := range in {
		result[i] = v.IsKnown()
	}

	return result
}

func (*sliceResponseCodePayOutConvert) ConvertIntToResponseCodePayOut(in []int) []ResponseCodePayOut {
	result := make([]ResponseCodePayOut, len(in))
	for i, v := range in {
		result[i] = ConvertIntToResponseCodePayOut(v)
	}

	return result
}

func (*sliceResponseCodePayOutConvert) ConvertUintToResponseCodePayOut(in []uint) []ResponseCodePayOut {
	result := make([]ResponseCodePayOut, len(in))
	for i, v := range in {
		result[i] = ConvertUintToResponseCodePayOut(v)
	}

	return result
}

func (*sliceResponseCodePayOutConvert) ConvertInt32ToResponseCodePayOut(in []int32) []ResponseCodePayOut {
	result := make([]ResponseCodePayOut, len(in))
	for i, v := range in {
		result[i] = ConvertInt32ToResponseCodePayOut(v)
	}

	return result
}

func (*sliceResponseCodePayOutConvert) ConvertUint32ToResponseCodePayOut(in []uint32) []ResponseCodePayOut {
	result := make([]ResponseCodePayOut, len(in))
	for i, v := range in {
		result[i] = ConvertUint32ToResponseCodePayOut(v)
	}

	return result
}

func (*sliceResponseCodePayOutConvert) ConvertInt64ToResponseCodePayOut(in []int64) []ResponseCodePayOut {
	result := make([]ResponseCodePayOut, len(in))
	for i, v := range in {
		result[i] = ConvertInt64ToResponseCodePayOut(v)
	}

	return result
}

func (*sliceResponseCodePayOutConvert) ConvertUint64ToResponseCodePayOut(in []uint64) []ResponseCodePayOut {
	result := make([]ResponseCodePayOut, len(in))
	for i, v := range in {
		result[i] = ConvertUint64ToResponseCodePayOut(v)
	}

	return result
}

func (*sliceResponseCodePayOutConvert) ConvertStringToResponseCodePayOut(in []string) []ResponseCodePayOut {
	result := make([]ResponseCodePayOut, len(in))
	for i, v := range in {
		result[i] = ConvertStringToResponseCodePayOut(v)
	}

	return result
}

func NewResponseCodePayOutUsage() *ResponseCodePayOutUsage {
	return &ResponseCodePayOutUsage{
		enumMap: map[ResponseCodePayOut]bool{
			ResponseCodePayOut_ResponseCodePayOutBadRequest:          false,
			ResponseCodePayOut_ResponseCodePayOutUnauthorized:        false,
			ResponseCodePayOut_ResponseCodePayOutTooManyRequests:     false,
			ResponseCodePayOut_ResponseCodePayOutInternalServerError: false,
			ResponseCodePayOut_ResponseCodePayOutNotImplemented:      false,
			ResponseCodePayOut_ResponseCodePayOutServiceUnavailable:  false,
			ResponseCodePayOut_ResponseCodePayOutDeclined:            false,
			ResponseCodePayOut_ResponseCodePayOutRejected:            false,
			ResponseCodePayOut_ResponseCodePayOutTeapot:              false,
			ResponseCodePayOut_ResponseCodePayOutActive:              false,
			ResponseCodePayOut_ResponseCodePayOutWaiting:             false,
			ResponseCodePayOut_ResponseCodePayOutLoaded:              false,
			ResponseCodePayOut_ResponseCodePayOutSuspended:           false,
			ResponseCodePayOut_ResponseCodePayOutRetry:               false,
			ResponseCodePayOut_ResponseCodePayOutRejectOut:           false,
			ResponseCodePayOut_ResponseCodePayOutRejectSent:          false,
			ResponseCodePayOut_ResponseCodePayOutCompleted:           false,
		},
	}
}

func IsResponseCodePayOut(target ResponseCodePayOut, matches ...ResponseCodePayOut) bool {
	for _, v := range matches {
		if target == v {
			return true
		}
	}

	return false
}

type ResponseCodePayOutUsage struct {
	enumMap map[ResponseCodePayOut]bool
}

func (u *ResponseCodePayOutUsage) Use(slice ...ResponseCodePayOut) *ResponseCodePayOutUsage {
	for _, in := range slice {
		u.enumMap[in] = true
	}

	return u
}

func (u *ResponseCodePayOutUsage) Check() []string {
	var result []string
	for k, v := range u.enumMap {
		if v {
			continue
		}

		result = append(result, k.String())
	}

	return result
}

func (u *ResponseCodePayOutUsage) ResponseCodePayOut_ResponseCodePayOutBadRequest() ResponseCodePayOut {
	u.Use(ResponseCodePayOut_ResponseCodePayOutBadRequest)
	return ResponseCodePayOut_ResponseCodePayOutBadRequest
}

func (u *ResponseCodePayOutUsage) ResponseCodePayOut_ResponseCodePayOutUnauthorized() ResponseCodePayOut {
	u.Use(ResponseCodePayOut_ResponseCodePayOutUnauthorized)
	return ResponseCodePayOut_ResponseCodePayOutUnauthorized
}

func (u *ResponseCodePayOutUsage) ResponseCodePayOut_ResponseCodePayOutTooManyRequests() ResponseCodePayOut {
	u.Use(ResponseCodePayOut_ResponseCodePayOutTooManyRequests)
	return ResponseCodePayOut_ResponseCodePayOutTooManyRequests
}

func (u *ResponseCodePayOutUsage) ResponseCodePayOut_ResponseCodePayOutInternalServerError() ResponseCodePayOut {
	u.Use(ResponseCodePayOut_ResponseCodePayOutInternalServerError)
	return ResponseCodePayOut_ResponseCodePayOutInternalServerError
}

func (u *ResponseCodePayOutUsage) ResponseCodePayOut_ResponseCodePayOutNotImplemented() ResponseCodePayOut {
	u.Use(ResponseCodePayOut_ResponseCodePayOutNotImplemented)
	return ResponseCodePayOut_ResponseCodePayOutNotImplemented
}

func (u *ResponseCodePayOutUsage) ResponseCodePayOut_ResponseCodePayOutServiceUnavailable() ResponseCodePayOut {
	u.Use(ResponseCodePayOut_ResponseCodePayOutServiceUnavailable)
	return ResponseCodePayOut_ResponseCodePayOutServiceUnavailable
}

func (u *ResponseCodePayOutUsage) ResponseCodePayOut_ResponseCodePayOutDeclined() ResponseCodePayOut {
	u.Use(ResponseCodePayOut_ResponseCodePayOutDeclined)
	return ResponseCodePayOut_ResponseCodePayOutDeclined
}

func (u *ResponseCodePayOutUsage) ResponseCodePayOut_ResponseCodePayOutRejected() ResponseCodePayOut {
	u.Use(ResponseCodePayOut_ResponseCodePayOutRejected)
	return ResponseCodePayOut_ResponseCodePayOutRejected
}

func (u *ResponseCodePayOutUsage) ResponseCodePayOut_ResponseCodePayOutTeapot() ResponseCodePayOut {
	u.Use(ResponseCodePayOut_ResponseCodePayOutTeapot)
	return ResponseCodePayOut_ResponseCodePayOutTeapot
}

func (u *ResponseCodePayOutUsage) ResponseCodePayOut_ResponseCodePayOutActive() ResponseCodePayOut {
	u.Use(ResponseCodePayOut_ResponseCodePayOutActive)
	return ResponseCodePayOut_ResponseCodePayOutActive
}

func (u *ResponseCodePayOutUsage) ResponseCodePayOut_ResponseCodePayOutWaiting() ResponseCodePayOut {
	u.Use(ResponseCodePayOut_ResponseCodePayOutWaiting)
	return ResponseCodePayOut_ResponseCodePayOutWaiting
}

func (u *ResponseCodePayOutUsage) ResponseCodePayOut_ResponseCodePayOutLoaded() ResponseCodePayOut {
	u.Use(ResponseCodePayOut_ResponseCodePayOutLoaded)
	return ResponseCodePayOut_ResponseCodePayOutLoaded
}

func (u *ResponseCodePayOutUsage) ResponseCodePayOut_ResponseCodePayOutSuspended() ResponseCodePayOut {
	u.Use(ResponseCodePayOut_ResponseCodePayOutSuspended)
	return ResponseCodePayOut_ResponseCodePayOutSuspended
}

func (u *ResponseCodePayOutUsage) ResponseCodePayOut_ResponseCodePayOutRetry() ResponseCodePayOut {
	u.Use(ResponseCodePayOut_ResponseCodePayOutRetry)
	return ResponseCodePayOut_ResponseCodePayOutRetry
}

func (u *ResponseCodePayOutUsage) ResponseCodePayOut_ResponseCodePayOutRejectOut() ResponseCodePayOut {
	u.Use(ResponseCodePayOut_ResponseCodePayOutRejectOut)
	return ResponseCodePayOut_ResponseCodePayOutRejectOut
}

func (u *ResponseCodePayOutUsage) ResponseCodePayOut_ResponseCodePayOutRejectSent() ResponseCodePayOut {
	u.Use(ResponseCodePayOut_ResponseCodePayOutRejectSent)
	return ResponseCodePayOut_ResponseCodePayOutRejectSent
}

func (u *ResponseCodePayOutUsage) ResponseCodePayOut_ResponseCodePayOutCompleted() ResponseCodePayOut {
	u.Use(ResponseCodePayOut_ResponseCodePayOutCompleted)
	return ResponseCodePayOut_ResponseCodePayOutCompleted
}

func (val IntegrationErrorMap) Synonym() IntegrationErrorMap {
	if _, ok := IntegrationErrorMap_name[int32(val)]; ok {
		return val
	}

	return IntegrationErrorMap(math.MinInt32)
}

func (val IntegrationErrorMap) Int() int {
	return int(val.Synonym())
}

func (val IntegrationErrorMap) IntPtr() *int {
	result := int(val.Synonym())
	return &result
}

func (val IntegrationErrorMap) Int32() int32 {
	return int32(val.Synonym())
}

func (val IntegrationErrorMap) Int32Ptr() *int32 {
	result := int32(val.Synonym())
	return &result
}

func (val IntegrationErrorMap) Int64() int64 {
	return int64(val.Synonym())
}

func (val IntegrationErrorMap) Int64Ptr() *int64 {
	result := int64(val.Synonym())
	return &result
}

func (val IntegrationErrorMap) Uint() uint {
	return uint(val.Synonym())
}

func (val IntegrationErrorMap) UintPtr() *uint {
	result := uint(val.Synonym())
	return &result
}

func (val IntegrationErrorMap) Uint32() uint32 {
	return uint32(val.Synonym())
}

func (val IntegrationErrorMap) Uint32Ptr() *uint32 {
	result := uint32(val.Synonym())
	return &result
}

func (val IntegrationErrorMap) Uint64() uint64 {
	return uint64(val.Synonym())
}

func (val IntegrationErrorMap) Uint64Ptr() *uint64 {
	result := uint64(val.Synonym())
	return &result
}

func (val IntegrationErrorMap) IsKnown() bool {
	return val.Synonym() != IntegrationErrorMap(math.MinInt32)
}

func ConvertIntToIntegrationErrorMap(in int) IntegrationErrorMap {
	return IntegrationErrorMap(in).Synonym()
}

func ConvertUintToIntegrationErrorMap(in uint) IntegrationErrorMap {
	return IntegrationErrorMap(in).Synonym()
}

func ConvertInt32ToIntegrationErrorMap(in int32) IntegrationErrorMap {
	return IntegrationErrorMap(in).Synonym()
}

func ConvertUint32ToIntegrationErrorMap(in uint32) IntegrationErrorMap {
	return IntegrationErrorMap(in).Synonym()
}

func ConvertInt64ToIntegrationErrorMap(in int64) IntegrationErrorMap {
	return IntegrationErrorMap(in).Synonym()
}

func ConvertUint64ToIntegrationErrorMap(in uint64) IntegrationErrorMap {
	return IntegrationErrorMap(in).Synonym()
}

var IntegrationErrorMap_Lower_value = map[string]IntegrationErrorMap{
	"11":  0,
	"12":  1,
	"03":  2,
	"14":  3,
	"24":  4,
	"29":  5,
	"51":  6,
	"57":  7,
	"80":  8,
	"95":  9,
	"xd":  10,
	"13":  11,
	"19":  12,
	"21":  13,
	"30":  14,
	"31":  15,
	"32":  16,
	"56":  17,
	"58":  18,
	"64":  19,
	"79":  20,
	"92":  21,
	"96":  22,
	"a1":  23,
	"01":  24,
	"02":  25,
	"06":  26,
	"10":  27,
	"15":  28,
	"17":  29,
	"18":  30,
	"44":  31,
	"66":  32,
	"83":  33,
	"89":  34,
	"91":  35,
	"20":  36,
	"35":  37,
	"40":  38,
	"52":  39,
	"76":  40,
	"81":  41,
	"86":  42,
	"90":  43,
	"b2":  44,
	"n0":  45,
	"33":  46,
	"34":  47,
	"39":  48,
	"42":  49,
	"54":  50,
	"59":  51,
	"60":  52,
	"63":  53,
	"88":  54,
	"93":  55,
	"b8":  56,
	"n4":  57,
	"n5":  58,
	"r0":  59,
	"08":  60,
	"23":  61,
	"26":  62,
	"65":  63,
	"67":  64,
	"77":  65,
	"84":  66,
	"94":  67,
	"n7":  68,
	"q1":  69,
	"07":  70,
	"09":  71,
	"16":  72,
	"22":  73,
	"36":  74,
	"43":  75,
	"61":  76,
	"62":  77,
	"68":  78,
	"75":  79,
	"82":  80,
	"85":  81,
	"b9":  82,
	"n8":  83,
	"00":  84,
	"04":  85,
	"05":  86,
	"25":  87,
	"27":  88,
	"28":  89,
	"37":  90,
	"38":  91,
	"41":  92,
	"53":  93,
	"55":  94,
	"n3":  95,
	"xa":  96,
	"-2":  97,
	"-10": 98,
	"-11": 99,
	"-3":  100,
	"-14": 101,
	"-5":  102,
	"-1":  103,
	"-4":  104,
	"-12": 105,
	"-15": 106,
	"-17": 107,
	"-16": 108,
	"-13": 109,
	"-19": 110,
	"-6":  111,
	"-7":  112,
	"-8":  113,
	"-9":  114,
	"-18": 115,
	"-20": 116,
}

func ConvertStringToIntegrationErrorMap(in string) IntegrationErrorMap {
	if result, ok := IntegrationErrorMap_value[in]; ok {
		return IntegrationErrorMap(result)
	}

	if result, ok := IntegrationErrorMap_Lower_value[strings.ToLower(in)]; ok {
		return IntegrationErrorMap(result)
	}

	return IntegrationErrorMap(math.MinInt32)
}

var SliceIntegrationErrorMapConvert *sliceIntegrationErrorMapConvert

type sliceIntegrationErrorMapConvert struct{}

func (*sliceIntegrationErrorMapConvert) Synonym(in []IntegrationErrorMap) []IntegrationErrorMap {
	result := make([]IntegrationErrorMap, len(in))
	for i, v := range in {
		result[i] = v.Synonym()
	}

	return result
}

func (*sliceIntegrationErrorMapConvert) Int32(in []IntegrationErrorMap) []int32 {
	result := make([]int32, len(in))
	for i, v := range in {
		result[i] = v.Int32()
	}

	return result
}

func (*sliceIntegrationErrorMapConvert) Uint32(in []IntegrationErrorMap) []uint32 {
	result := make([]uint32, len(in))
	for i, v := range in {
		result[i] = v.Uint32()
	}

	return result
}

func (*sliceIntegrationErrorMapConvert) Uint64(in []IntegrationErrorMap) []uint64 {
	result := make([]uint64, len(in))
	for i, v := range in {
		result[i] = v.Uint64()
	}

	return result
}

func (*sliceIntegrationErrorMapConvert) Int64(in []IntegrationErrorMap) []int64 {
	result := make([]int64, len(in))
	for i, v := range in {
		result[i] = v.Int64()
	}

	return result
}

func (*sliceIntegrationErrorMapConvert) Uint(in []IntegrationErrorMap) []uint {
	result := make([]uint, len(in))
	for i, v := range in {
		result[i] = v.Uint()
	}

	return result
}

func (*sliceIntegrationErrorMapConvert) Int(in []IntegrationErrorMap) []int {
	result := make([]int, len(in))
	for i, v := range in {
		result[i] = v.Int()
	}

	return result
}

func (*sliceIntegrationErrorMapConvert) IsKnown(in []IntegrationErrorMap) []bool {
	result := make([]bool, len(in))
	for i, v := range in {
		result[i] = v.IsKnown()
	}

	return result
}

func (*sliceIntegrationErrorMapConvert) ConvertIntToIntegrationErrorMap(in []int) []IntegrationErrorMap {
	result := make([]IntegrationErrorMap, len(in))
	for i, v := range in {
		result[i] = ConvertIntToIntegrationErrorMap(v)
	}

	return result
}

func (*sliceIntegrationErrorMapConvert) ConvertUintToIntegrationErrorMap(in []uint) []IntegrationErrorMap {
	result := make([]IntegrationErrorMap, len(in))
	for i, v := range in {
		result[i] = ConvertUintToIntegrationErrorMap(v)
	}

	return result
}

func (*sliceIntegrationErrorMapConvert) ConvertInt32ToIntegrationErrorMap(in []int32) []IntegrationErrorMap {
	result := make([]IntegrationErrorMap, len(in))
	for i, v := range in {
		result[i] = ConvertInt32ToIntegrationErrorMap(v)
	}

	return result
}

func (*sliceIntegrationErrorMapConvert) ConvertUint32ToIntegrationErrorMap(in []uint32) []IntegrationErrorMap {
	result := make([]IntegrationErrorMap, len(in))
	for i, v := range in {
		result[i] = ConvertUint32ToIntegrationErrorMap(v)
	}

	return result
}

func (*sliceIntegrationErrorMapConvert) ConvertInt64ToIntegrationErrorMap(in []int64) []IntegrationErrorMap {
	result := make([]IntegrationErrorMap, len(in))
	for i, v := range in {
		result[i] = ConvertInt64ToIntegrationErrorMap(v)
	}

	return result
}

func (*sliceIntegrationErrorMapConvert) ConvertUint64ToIntegrationErrorMap(in []uint64) []IntegrationErrorMap {
	result := make([]IntegrationErrorMap, len(in))
	for i, v := range in {
		result[i] = ConvertUint64ToIntegrationErrorMap(v)
	}

	return result
}

func (*sliceIntegrationErrorMapConvert) ConvertStringToIntegrationErrorMap(in []string) []IntegrationErrorMap {
	result := make([]IntegrationErrorMap, len(in))
	for i, v := range in {
		result[i] = ConvertStringToIntegrationErrorMap(v)
	}

	return result
}

func NewIntegrationErrorMapUsage() *IntegrationErrorMapUsage {
	return &IntegrationErrorMapUsage{
		enumMap: map[IntegrationErrorMap]bool{
			IntegrationErrorMap_IntegrationErrorMapNone:                            false,
			IntegrationErrorMap_IntegrationErrorMapInvalidTransaction:              false,
			IntegrationErrorMap_IntegrationErrorMapInvalidMerchant:                 false,
			IntegrationErrorMap_IntegrationErrorMapCardNotExist:                    false,
			IntegrationErrorMap_IntegrationErrorMapNotSupportedByReceiver:          false,
			IntegrationErrorMap_IntegrationErrorMapFileUpdateNotSuccess:            false,
			IntegrationErrorMap_IntegrationErrorMapNotSufficient:                   false,
			IntegrationErrorMap_IntegrationErrorMapNotPermitted:                    false,
			IntegrationErrorMap_IntegrationErrorMapInvalidDate:                     false,
			IntegrationErrorMap_IntegrationErrorMapReconcileError:                  false,
			IntegrationErrorMap_IntegrationErrorMapForwardToIssuer:                 false,
			IntegrationErrorMap_IntegrationErrorMapInvalidAmount:                   false,
			IntegrationErrorMap_IntegrationErrorMapReenterTransaction:              false,
			IntegrationErrorMap_IntegrationErrorMapNoAction:                        false,
			IntegrationErrorMap_IntegrationErrorMapFormatError:                     false,
			IntegrationErrorMap_IntegrationErrorMapIssuerSignOff:                   false,
			IntegrationErrorMap_IntegrationErrorMapCompletedPartially:              false,
			IntegrationErrorMap_IntegrationErrorMapNoCardRecord:                    false,
			IntegrationErrorMap_IntegrationErrorMapTransactionNotPermitted:         false,
			IntegrationErrorMap_IntegrationErrorMapAmountIncorrect:                 false,
			IntegrationErrorMap_IntegrationErrorMapAlreadyReversed:                 false,
			IntegrationErrorMap_IntegrationErrorMapNoRoutingPath:                   false,
			IntegrationErrorMap_IntegrationErrorMapSystemMalfunction:               false,
			IntegrationErrorMap_IntegrationErrorMapSoftDecline:                     false,
			IntegrationErrorMap_IntegrationErrorMapReferToIssuer:                   false,
			IntegrationErrorMap_IntegrationErrorMapReferToIssuerSpecial:            false,
			IntegrationErrorMap_IntegrationErrorMapError:                           false,
			IntegrationErrorMap_IntegrationErrorMapApprovedPartialAmount:           false,
			IntegrationErrorMap_IntegrationErrorMapNoSuchIssuer:                    false,
			IntegrationErrorMap_IntegrationErrorMapCustomerCancellation:            false,
			IntegrationErrorMap_IntegrationErrorMapCustomerDispute:                 false,
			IntegrationErrorMap_IntegrationErrorMapNoInvestmentAccount:             false,
			IntegrationErrorMap_IntegrationErrorMapCardAcceptorCallAcquirer:        false,
			IntegrationErrorMap_IntegrationErrorMapUnableToVerifyPIN:               false,
			IntegrationErrorMap_IntegrationErrorMapAuthenticationFailure:           false,
			IntegrationErrorMap_IntegrationErrorMapIssuerSwitchInoperative:         false,
			IntegrationErrorMap_IntegrationErrorMapInvalidResponse:                 false,
			IntegrationErrorMap_IntegrationErrorMapCardAcceptorContactAcquirer:     false,
			IntegrationErrorMap_IntegrationErrorMapFunctionNotSupported:            false,
			IntegrationErrorMap_IntegrationErrorMapNoCheckingAccount:               false,
			IntegrationErrorMap_IntegrationErrorMapPreviousMessageNotFound:         false,
			IntegrationErrorMap_IntegrationErrorMapCryptographicErrorInPIN:         false,
			IntegrationErrorMap_IntegrationErrorMapPINValidationNotPossible:        false,
			IntegrationErrorMap_IntegrationErrorMapCutoffInProcess:                 false,
			IntegrationErrorMap_IntegrationErrorMapSurchargeNotSupported:           false,
			IntegrationErrorMap_IntegrationErrorMapForceSTIP:                       false,
			IntegrationErrorMap_IntegrationErrorMapExpiredCard:                     false,
			IntegrationErrorMap_IntegrationErrorMapSuspectedFraud:                  false,
			IntegrationErrorMap_IntegrationErrorMapNoCreditAccount:                 false,
			IntegrationErrorMap_IntegrationErrorMapNoUniversalAccount:              false,
			IntegrationErrorMap_IntegrationErrorMapExpiredCardAlternate:            false,
			IntegrationErrorMap_IntegrationErrorMapSuspectedFraudAlternate:         false,
			IntegrationErrorMap_IntegrationErrorMapCardAcceptorWriteToAcquirer:     false,
			IntegrationErrorMap_IntegrationErrorMapSecurityViolation:               false,
			IntegrationErrorMap_IntegrationErrorMapCryptographicFailure:            false,
			IntegrationErrorMap_IntegrationErrorMapViolationOfLaw:                  false,
			IntegrationErrorMap_IntegrationErrorMapFraud:                           false,
			IntegrationErrorMap_IntegrationErrorMapCashRequestExceedsLimit:         false,
			IntegrationErrorMap_IntegrationErrorMapIneligibleForResubmission:       false,
			IntegrationErrorMap_IntegrationErrorMapStopPaymentOrder:                false,
			IntegrationErrorMap_IntegrationErrorMapHonorWithIdentification:         false,
			IntegrationErrorMap_IntegrationErrorMapUnacceptableTransactionFee:      false,
			IntegrationErrorMap_IntegrationErrorMapDuplicateFileUpdateRecord:       false,
			IntegrationErrorMap_IntegrationErrorMapActivityCountExceeded:           false,
			IntegrationErrorMap_IntegrationErrorMapCardPickUpAtATM:                 false,
			IntegrationErrorMap_IntegrationErrorMapDataMismatchOriginalMessage:     false,
			IntegrationErrorMap_IntegrationErrorMapInvalidAuthorizationLifeCycle:   false,
			IntegrationErrorMap_IntegrationErrorMapDuplicateTransmission:           false,
			IntegrationErrorMap_IntegrationErrorMapDeclineForCVV2Failure:           false,
			IntegrationErrorMap_IntegrationErrorMapCardAuthenticationFailed:        false,
			IntegrationErrorMap_IntegrationErrorMapPickUpCardSpecial:               false,
			IntegrationErrorMap_IntegrationErrorMapRequestInProgress:               false,
			IntegrationErrorMap_IntegrationErrorMapApprovedUpdateTrack3:            false,
			IntegrationErrorMap_IntegrationErrorMapSuspectedMalfunction:            false,
			IntegrationErrorMap_IntegrationErrorMapRestrictedCard:                  false,
			IntegrationErrorMap_IntegrationErrorMapPickUpCardStolenCard:            false,
			IntegrationErrorMap_IntegrationErrorMapExceedsWithdrawalLimit:          false,
			IntegrationErrorMap_IntegrationErrorMapRestrictedCardAlternate:         false,
			IntegrationErrorMap_IntegrationErrorMapResponseReceivedTooLate:         false,
			IntegrationErrorMap_IntegrationErrorMapTooManyWrongPINTries:            false,
			IntegrationErrorMap_IntegrationErrorMapIncorrectCVV:                    false,
			IntegrationErrorMap_IntegrationErrorMapNoReasonToDecline:               false,
			IntegrationErrorMap_IntegrationErrorMapDeclinedByGateway:               false,
			IntegrationErrorMap_IntegrationErrorMapTransactionAmountExceedsPreAuth: false,
			IntegrationErrorMap_IntegrationErrorMapApproved:                        false,
			IntegrationErrorMap_IntegrationErrorMapPickUpCard:                      false,
			IntegrationErrorMap_IntegrationErrorMapDoNotHonor:                      false,
			IntegrationErrorMap_IntegrationErrorMapUnableToLocateRecord:            false,
			IntegrationErrorMap_IntegrationErrorMapFileUpdateFieldEditError:        false,
			IntegrationErrorMap_IntegrationErrorMapFileTemporarilyUnavailable:      false,
			IntegrationErrorMap_IntegrationErrorMapCardAcceptor:                    false,
			IntegrationErrorMap_IntegrationErrorMapAllowablePINTriesExceeded:       false,
			IntegrationErrorMap_IntegrationErrorMapPickUpCardLostCard:              false,
			IntegrationErrorMap_IntegrationErrorMapNoSavingsAccount:                false,
			IntegrationErrorMap_IntegrationErrorMapIncorrectPIN:                    false,
			IntegrationErrorMap_IntegrationErrorMapCashServiceNotAvailable:         false,
			IntegrationErrorMap_IntegrationErrorMapForwardIssuer:                   false,
			IntegrationErrorMap_IntegrationErrorMapCGICheckFailed:                  false,
			IntegrationErrorMap_IntegrationErrorMapInvalidAmountField:              false,
			IntegrationErrorMap_IntegrationErrorMapInvalidCurrencyField:            false,
			IntegrationErrorMap_IntegrationErrorMapHostNotResponding:               false,
			IntegrationErrorMap_IntegrationErrorMapNoConnectionWithPINPad:          false,
			IntegrationErrorMap_IntegrationErrorMapConnectionErrorDuringProcessing: false,
			IntegrationErrorMap_IntegrationErrorMapMissingMandatoryField:           false,
			IntegrationErrorMap_IntegrationErrorMapNoConnectionWithHost:            false,
			IntegrationErrorMap_IntegrationErrorMapInvalidMerchantIDField:          false,
			IntegrationErrorMap_IntegrationErrorMapInvalidRRNField:                 false,
			IntegrationErrorMap_IntegrationErrorMapAccessDenied:                    false,
			IntegrationErrorMap_IntegrationErrorMapTerminalBusy:                    false,
			IntegrationErrorMap_IntegrationErrorMapIPAddressMismatch:               false,
			IntegrationErrorMap_IntegrationErrorMapAuthenticationError:             false,
			IntegrationErrorMap_IntegrationErrorMapEGatewaySetupError:              false,
			IntegrationErrorMap_IntegrationErrorMapIncorrectHostResponse:           false,
			IntegrationErrorMap_IntegrationErrorMapInvalidCardNumberField:          false,
			IntegrationErrorMap_IntegrationErrorMapInvalidCardExpirationDateField:  false,
			IntegrationErrorMap_IntegrationErrorMapInvalidCVC2Field:                false,
			IntegrationErrorMap_IntegrationErrorMapTimeExceeded:                    false,
		},
	}
}

func IsIntegrationErrorMap(target IntegrationErrorMap, matches ...IntegrationErrorMap) bool {
	for _, v := range matches {
		if target == v {
			return true
		}
	}

	return false
}

type IntegrationErrorMapUsage struct {
	enumMap map[IntegrationErrorMap]bool
}

func (u *IntegrationErrorMapUsage) Use(slice ...IntegrationErrorMap) *IntegrationErrorMapUsage {
	for _, in := range slice {
		u.enumMap[in] = true
	}

	return u
}

func (u *IntegrationErrorMapUsage) Check() []string {
	var result []string
	for k, v := range u.enumMap {
		if v {
			continue
		}

		result = append(result, k.String())
	}

	return result
}

func (u *IntegrationErrorMapUsage) IntegrationErrorMap_IntegrationErrorMapNone() IntegrationErrorMap {
	u.Use(IntegrationErrorMap_IntegrationErrorMapNone)
	return IntegrationErrorMap_IntegrationErrorMapNone
}

func (u *IntegrationErrorMapUsage) IntegrationErrorMap_IntegrationErrorMapInvalidTransaction() IntegrationErrorMap {
	u.Use(IntegrationErrorMap_IntegrationErrorMapInvalidTransaction)
	return IntegrationErrorMap_IntegrationErrorMapInvalidTransaction
}

func (u *IntegrationErrorMapUsage) IntegrationErrorMap_IntegrationErrorMapInvalidMerchant() IntegrationErrorMap {
	u.Use(IntegrationErrorMap_IntegrationErrorMapInvalidMerchant)
	return IntegrationErrorMap_IntegrationErrorMapInvalidMerchant
}

func (u *IntegrationErrorMapUsage) IntegrationErrorMap_IntegrationErrorMapCardNotExist() IntegrationErrorMap {
	u.Use(IntegrationErrorMap_IntegrationErrorMapCardNotExist)
	return IntegrationErrorMap_IntegrationErrorMapCardNotExist
}

func (u *IntegrationErrorMapUsage) IntegrationErrorMap_IntegrationErrorMapNotSupportedByReceiver() IntegrationErrorMap {
	u.Use(IntegrationErrorMap_IntegrationErrorMapNotSupportedByReceiver)
	return IntegrationErrorMap_IntegrationErrorMapNotSupportedByReceiver
}

func (u *IntegrationErrorMapUsage) IntegrationErrorMap_IntegrationErrorMapFileUpdateNotSuccess() IntegrationErrorMap {
	u.Use(IntegrationErrorMap_IntegrationErrorMapFileUpdateNotSuccess)
	return IntegrationErrorMap_IntegrationErrorMapFileUpdateNotSuccess
}

func (u *IntegrationErrorMapUsage) IntegrationErrorMap_IntegrationErrorMapNotSufficient() IntegrationErrorMap {
	u.Use(IntegrationErrorMap_IntegrationErrorMapNotSufficient)
	return IntegrationErrorMap_IntegrationErrorMapNotSufficient
}

func (u *IntegrationErrorMapUsage) IntegrationErrorMap_IntegrationErrorMapNotPermitted() IntegrationErrorMap {
	u.Use(IntegrationErrorMap_IntegrationErrorMapNotPermitted)
	return IntegrationErrorMap_IntegrationErrorMapNotPermitted
}

func (u *IntegrationErrorMapUsage) IntegrationErrorMap_IntegrationErrorMapInvalidDate() IntegrationErrorMap {
	u.Use(IntegrationErrorMap_IntegrationErrorMapInvalidDate)
	return IntegrationErrorMap_IntegrationErrorMapInvalidDate
}

func (u *IntegrationErrorMapUsage) IntegrationErrorMap_IntegrationErrorMapReconcileError() IntegrationErrorMap {
	u.Use(IntegrationErrorMap_IntegrationErrorMapReconcileError)
	return IntegrationErrorMap_IntegrationErrorMapReconcileError
}

func (u *IntegrationErrorMapUsage) IntegrationErrorMap_IntegrationErrorMapForwardToIssuer() IntegrationErrorMap {
	u.Use(IntegrationErrorMap_IntegrationErrorMapForwardToIssuer)
	return IntegrationErrorMap_IntegrationErrorMapForwardToIssuer
}

func (u *IntegrationErrorMapUsage) IntegrationErrorMap_IntegrationErrorMapInvalidAmount() IntegrationErrorMap {
	u.Use(IntegrationErrorMap_IntegrationErrorMapInvalidAmount)
	return IntegrationErrorMap_IntegrationErrorMapInvalidAmount
}

func (u *IntegrationErrorMapUsage) IntegrationErrorMap_IntegrationErrorMapReenterTransaction() IntegrationErrorMap {
	u.Use(IntegrationErrorMap_IntegrationErrorMapReenterTransaction)
	return IntegrationErrorMap_IntegrationErrorMapReenterTransaction
}

func (u *IntegrationErrorMapUsage) IntegrationErrorMap_IntegrationErrorMapNoAction() IntegrationErrorMap {
	u.Use(IntegrationErrorMap_IntegrationErrorMapNoAction)
	return IntegrationErrorMap_IntegrationErrorMapNoAction
}

func (u *IntegrationErrorMapUsage) IntegrationErrorMap_IntegrationErrorMapFormatError() IntegrationErrorMap {
	u.Use(IntegrationErrorMap_IntegrationErrorMapFormatError)
	return IntegrationErrorMap_IntegrationErrorMapFormatError
}

func (u *IntegrationErrorMapUsage) IntegrationErrorMap_IntegrationErrorMapIssuerSignOff() IntegrationErrorMap {
	u.Use(IntegrationErrorMap_IntegrationErrorMapIssuerSignOff)
	return IntegrationErrorMap_IntegrationErrorMapIssuerSignOff
}

func (u *IntegrationErrorMapUsage) IntegrationErrorMap_IntegrationErrorMapCompletedPartially() IntegrationErrorMap {
	u.Use(IntegrationErrorMap_IntegrationErrorMapCompletedPartially)
	return IntegrationErrorMap_IntegrationErrorMapCompletedPartially
}

func (u *IntegrationErrorMapUsage) IntegrationErrorMap_IntegrationErrorMapNoCardRecord() IntegrationErrorMap {
	u.Use(IntegrationErrorMap_IntegrationErrorMapNoCardRecord)
	return IntegrationErrorMap_IntegrationErrorMapNoCardRecord
}

func (u *IntegrationErrorMapUsage) IntegrationErrorMap_IntegrationErrorMapTransactionNotPermitted() IntegrationErrorMap {
	u.Use(IntegrationErrorMap_IntegrationErrorMapTransactionNotPermitted)
	return IntegrationErrorMap_IntegrationErrorMapTransactionNotPermitted
}

func (u *IntegrationErrorMapUsage) IntegrationErrorMap_IntegrationErrorMapAmountIncorrect() IntegrationErrorMap {
	u.Use(IntegrationErrorMap_IntegrationErrorMapAmountIncorrect)
	return IntegrationErrorMap_IntegrationErrorMapAmountIncorrect
}

func (u *IntegrationErrorMapUsage) IntegrationErrorMap_IntegrationErrorMapAlreadyReversed() IntegrationErrorMap {
	u.Use(IntegrationErrorMap_IntegrationErrorMapAlreadyReversed)
	return IntegrationErrorMap_IntegrationErrorMapAlreadyReversed
}

func (u *IntegrationErrorMapUsage) IntegrationErrorMap_IntegrationErrorMapNoRoutingPath() IntegrationErrorMap {
	u.Use(IntegrationErrorMap_IntegrationErrorMapNoRoutingPath)
	return IntegrationErrorMap_IntegrationErrorMapNoRoutingPath
}

func (u *IntegrationErrorMapUsage) IntegrationErrorMap_IntegrationErrorMapSystemMalfunction() IntegrationErrorMap {
	u.Use(IntegrationErrorMap_IntegrationErrorMapSystemMalfunction)
	return IntegrationErrorMap_IntegrationErrorMapSystemMalfunction
}

func (u *IntegrationErrorMapUsage) IntegrationErrorMap_IntegrationErrorMapSoftDecline() IntegrationErrorMap {
	u.Use(IntegrationErrorMap_IntegrationErrorMapSoftDecline)
	return IntegrationErrorMap_IntegrationErrorMapSoftDecline
}

func (u *IntegrationErrorMapUsage) IntegrationErrorMap_IntegrationErrorMapReferToIssuer() IntegrationErrorMap {
	u.Use(IntegrationErrorMap_IntegrationErrorMapReferToIssuer)
	return IntegrationErrorMap_IntegrationErrorMapReferToIssuer
}

func (u *IntegrationErrorMapUsage) IntegrationErrorMap_IntegrationErrorMapReferToIssuerSpecial() IntegrationErrorMap {
	u.Use(IntegrationErrorMap_IntegrationErrorMapReferToIssuerSpecial)
	return IntegrationErrorMap_IntegrationErrorMapReferToIssuerSpecial
}

func (u *IntegrationErrorMapUsage) IntegrationErrorMap_IntegrationErrorMapError() IntegrationErrorMap {
	u.Use(IntegrationErrorMap_IntegrationErrorMapError)
	return IntegrationErrorMap_IntegrationErrorMapError
}

func (u *IntegrationErrorMapUsage) IntegrationErrorMap_IntegrationErrorMapApprovedPartialAmount() IntegrationErrorMap {
	u.Use(IntegrationErrorMap_IntegrationErrorMapApprovedPartialAmount)
	return IntegrationErrorMap_IntegrationErrorMapApprovedPartialAmount
}

func (u *IntegrationErrorMapUsage) IntegrationErrorMap_IntegrationErrorMapNoSuchIssuer() IntegrationErrorMap {
	u.Use(IntegrationErrorMap_IntegrationErrorMapNoSuchIssuer)
	return IntegrationErrorMap_IntegrationErrorMapNoSuchIssuer
}

func (u *IntegrationErrorMapUsage) IntegrationErrorMap_IntegrationErrorMapCustomerCancellation() IntegrationErrorMap {
	u.Use(IntegrationErrorMap_IntegrationErrorMapCustomerCancellation)
	return IntegrationErrorMap_IntegrationErrorMapCustomerCancellation
}

func (u *IntegrationErrorMapUsage) IntegrationErrorMap_IntegrationErrorMapCustomerDispute() IntegrationErrorMap {
	u.Use(IntegrationErrorMap_IntegrationErrorMapCustomerDispute)
	return IntegrationErrorMap_IntegrationErrorMapCustomerDispute
}

func (u *IntegrationErrorMapUsage) IntegrationErrorMap_IntegrationErrorMapNoInvestmentAccount() IntegrationErrorMap {
	u.Use(IntegrationErrorMap_IntegrationErrorMapNoInvestmentAccount)
	return IntegrationErrorMap_IntegrationErrorMapNoInvestmentAccount
}

func (u *IntegrationErrorMapUsage) IntegrationErrorMap_IntegrationErrorMapCardAcceptorCallAcquirer() IntegrationErrorMap {
	u.Use(IntegrationErrorMap_IntegrationErrorMapCardAcceptorCallAcquirer)
	return IntegrationErrorMap_IntegrationErrorMapCardAcceptorCallAcquirer
}

func (u *IntegrationErrorMapUsage) IntegrationErrorMap_IntegrationErrorMapUnableToVerifyPIN() IntegrationErrorMap {
	u.Use(IntegrationErrorMap_IntegrationErrorMapUnableToVerifyPIN)
	return IntegrationErrorMap_IntegrationErrorMapUnableToVerifyPIN
}

func (u *IntegrationErrorMapUsage) IntegrationErrorMap_IntegrationErrorMapAuthenticationFailure() IntegrationErrorMap {
	u.Use(IntegrationErrorMap_IntegrationErrorMapAuthenticationFailure)
	return IntegrationErrorMap_IntegrationErrorMapAuthenticationFailure
}

func (u *IntegrationErrorMapUsage) IntegrationErrorMap_IntegrationErrorMapIssuerSwitchInoperative() IntegrationErrorMap {
	u.Use(IntegrationErrorMap_IntegrationErrorMapIssuerSwitchInoperative)
	return IntegrationErrorMap_IntegrationErrorMapIssuerSwitchInoperative
}

func (u *IntegrationErrorMapUsage) IntegrationErrorMap_IntegrationErrorMapInvalidResponse() IntegrationErrorMap {
	u.Use(IntegrationErrorMap_IntegrationErrorMapInvalidResponse)
	return IntegrationErrorMap_IntegrationErrorMapInvalidResponse
}

func (u *IntegrationErrorMapUsage) IntegrationErrorMap_IntegrationErrorMapCardAcceptorContactAcquirer() IntegrationErrorMap {
	u.Use(IntegrationErrorMap_IntegrationErrorMapCardAcceptorContactAcquirer)
	return IntegrationErrorMap_IntegrationErrorMapCardAcceptorContactAcquirer
}

func (u *IntegrationErrorMapUsage) IntegrationErrorMap_IntegrationErrorMapFunctionNotSupported() IntegrationErrorMap {
	u.Use(IntegrationErrorMap_IntegrationErrorMapFunctionNotSupported)
	return IntegrationErrorMap_IntegrationErrorMapFunctionNotSupported
}

func (u *IntegrationErrorMapUsage) IntegrationErrorMap_IntegrationErrorMapNoCheckingAccount() IntegrationErrorMap {
	u.Use(IntegrationErrorMap_IntegrationErrorMapNoCheckingAccount)
	return IntegrationErrorMap_IntegrationErrorMapNoCheckingAccount
}

func (u *IntegrationErrorMapUsage) IntegrationErrorMap_IntegrationErrorMapPreviousMessageNotFound() IntegrationErrorMap {
	u.Use(IntegrationErrorMap_IntegrationErrorMapPreviousMessageNotFound)
	return IntegrationErrorMap_IntegrationErrorMapPreviousMessageNotFound
}

func (u *IntegrationErrorMapUsage) IntegrationErrorMap_IntegrationErrorMapCryptographicErrorInPIN() IntegrationErrorMap {
	u.Use(IntegrationErrorMap_IntegrationErrorMapCryptographicErrorInPIN)
	return IntegrationErrorMap_IntegrationErrorMapCryptographicErrorInPIN
}

func (u *IntegrationErrorMapUsage) IntegrationErrorMap_IntegrationErrorMapPINValidationNotPossible() IntegrationErrorMap {
	u.Use(IntegrationErrorMap_IntegrationErrorMapPINValidationNotPossible)
	return IntegrationErrorMap_IntegrationErrorMapPINValidationNotPossible
}

func (u *IntegrationErrorMapUsage) IntegrationErrorMap_IntegrationErrorMapCutoffInProcess() IntegrationErrorMap {
	u.Use(IntegrationErrorMap_IntegrationErrorMapCutoffInProcess)
	return IntegrationErrorMap_IntegrationErrorMapCutoffInProcess
}

func (u *IntegrationErrorMapUsage) IntegrationErrorMap_IntegrationErrorMapSurchargeNotSupported() IntegrationErrorMap {
	u.Use(IntegrationErrorMap_IntegrationErrorMapSurchargeNotSupported)
	return IntegrationErrorMap_IntegrationErrorMapSurchargeNotSupported
}

func (u *IntegrationErrorMapUsage) IntegrationErrorMap_IntegrationErrorMapForceSTIP() IntegrationErrorMap {
	u.Use(IntegrationErrorMap_IntegrationErrorMapForceSTIP)
	return IntegrationErrorMap_IntegrationErrorMapForceSTIP
}

func (u *IntegrationErrorMapUsage) IntegrationErrorMap_IntegrationErrorMapExpiredCard() IntegrationErrorMap {
	u.Use(IntegrationErrorMap_IntegrationErrorMapExpiredCard)
	return IntegrationErrorMap_IntegrationErrorMapExpiredCard
}

func (u *IntegrationErrorMapUsage) IntegrationErrorMap_IntegrationErrorMapSuspectedFraud() IntegrationErrorMap {
	u.Use(IntegrationErrorMap_IntegrationErrorMapSuspectedFraud)
	return IntegrationErrorMap_IntegrationErrorMapSuspectedFraud
}

func (u *IntegrationErrorMapUsage) IntegrationErrorMap_IntegrationErrorMapNoCreditAccount() IntegrationErrorMap {
	u.Use(IntegrationErrorMap_IntegrationErrorMapNoCreditAccount)
	return IntegrationErrorMap_IntegrationErrorMapNoCreditAccount
}

func (u *IntegrationErrorMapUsage) IntegrationErrorMap_IntegrationErrorMapNoUniversalAccount() IntegrationErrorMap {
	u.Use(IntegrationErrorMap_IntegrationErrorMapNoUniversalAccount)
	return IntegrationErrorMap_IntegrationErrorMapNoUniversalAccount
}

func (u *IntegrationErrorMapUsage) IntegrationErrorMap_IntegrationErrorMapExpiredCardAlternate() IntegrationErrorMap {
	u.Use(IntegrationErrorMap_IntegrationErrorMapExpiredCardAlternate)
	return IntegrationErrorMap_IntegrationErrorMapExpiredCardAlternate
}

func (u *IntegrationErrorMapUsage) IntegrationErrorMap_IntegrationErrorMapSuspectedFraudAlternate() IntegrationErrorMap {
	u.Use(IntegrationErrorMap_IntegrationErrorMapSuspectedFraudAlternate)
	return IntegrationErrorMap_IntegrationErrorMapSuspectedFraudAlternate
}

func (u *IntegrationErrorMapUsage) IntegrationErrorMap_IntegrationErrorMapCardAcceptorWriteToAcquirer() IntegrationErrorMap {
	u.Use(IntegrationErrorMap_IntegrationErrorMapCardAcceptorWriteToAcquirer)
	return IntegrationErrorMap_IntegrationErrorMapCardAcceptorWriteToAcquirer
}

func (u *IntegrationErrorMapUsage) IntegrationErrorMap_IntegrationErrorMapSecurityViolation() IntegrationErrorMap {
	u.Use(IntegrationErrorMap_IntegrationErrorMapSecurityViolation)
	return IntegrationErrorMap_IntegrationErrorMapSecurityViolation
}

func (u *IntegrationErrorMapUsage) IntegrationErrorMap_IntegrationErrorMapCryptographicFailure() IntegrationErrorMap {
	u.Use(IntegrationErrorMap_IntegrationErrorMapCryptographicFailure)
	return IntegrationErrorMap_IntegrationErrorMapCryptographicFailure
}

func (u *IntegrationErrorMapUsage) IntegrationErrorMap_IntegrationErrorMapViolationOfLaw() IntegrationErrorMap {
	u.Use(IntegrationErrorMap_IntegrationErrorMapViolationOfLaw)
	return IntegrationErrorMap_IntegrationErrorMapViolationOfLaw
}

func (u *IntegrationErrorMapUsage) IntegrationErrorMap_IntegrationErrorMapFraud() IntegrationErrorMap {
	u.Use(IntegrationErrorMap_IntegrationErrorMapFraud)
	return IntegrationErrorMap_IntegrationErrorMapFraud
}

func (u *IntegrationErrorMapUsage) IntegrationErrorMap_IntegrationErrorMapCashRequestExceedsLimit() IntegrationErrorMap {
	u.Use(IntegrationErrorMap_IntegrationErrorMapCashRequestExceedsLimit)
	return IntegrationErrorMap_IntegrationErrorMapCashRequestExceedsLimit
}

func (u *IntegrationErrorMapUsage) IntegrationErrorMap_IntegrationErrorMapIneligibleForResubmission() IntegrationErrorMap {
	u.Use(IntegrationErrorMap_IntegrationErrorMapIneligibleForResubmission)
	return IntegrationErrorMap_IntegrationErrorMapIneligibleForResubmission
}

func (u *IntegrationErrorMapUsage) IntegrationErrorMap_IntegrationErrorMapStopPaymentOrder() IntegrationErrorMap {
	u.Use(IntegrationErrorMap_IntegrationErrorMapStopPaymentOrder)
	return IntegrationErrorMap_IntegrationErrorMapStopPaymentOrder
}

func (u *IntegrationErrorMapUsage) IntegrationErrorMap_IntegrationErrorMapHonorWithIdentification() IntegrationErrorMap {
	u.Use(IntegrationErrorMap_IntegrationErrorMapHonorWithIdentification)
	return IntegrationErrorMap_IntegrationErrorMapHonorWithIdentification
}

func (u *IntegrationErrorMapUsage) IntegrationErrorMap_IntegrationErrorMapUnacceptableTransactionFee() IntegrationErrorMap {
	u.Use(IntegrationErrorMap_IntegrationErrorMapUnacceptableTransactionFee)
	return IntegrationErrorMap_IntegrationErrorMapUnacceptableTransactionFee
}

func (u *IntegrationErrorMapUsage) IntegrationErrorMap_IntegrationErrorMapDuplicateFileUpdateRecord() IntegrationErrorMap {
	u.Use(IntegrationErrorMap_IntegrationErrorMapDuplicateFileUpdateRecord)
	return IntegrationErrorMap_IntegrationErrorMapDuplicateFileUpdateRecord
}

func (u *IntegrationErrorMapUsage) IntegrationErrorMap_IntegrationErrorMapActivityCountExceeded() IntegrationErrorMap {
	u.Use(IntegrationErrorMap_IntegrationErrorMapActivityCountExceeded)
	return IntegrationErrorMap_IntegrationErrorMapActivityCountExceeded
}

func (u *IntegrationErrorMapUsage) IntegrationErrorMap_IntegrationErrorMapCardPickUpAtATM() IntegrationErrorMap {
	u.Use(IntegrationErrorMap_IntegrationErrorMapCardPickUpAtATM)
	return IntegrationErrorMap_IntegrationErrorMapCardPickUpAtATM
}

func (u *IntegrationErrorMapUsage) IntegrationErrorMap_IntegrationErrorMapDataMismatchOriginalMessage() IntegrationErrorMap {
	u.Use(IntegrationErrorMap_IntegrationErrorMapDataMismatchOriginalMessage)
	return IntegrationErrorMap_IntegrationErrorMapDataMismatchOriginalMessage
}

func (u *IntegrationErrorMapUsage) IntegrationErrorMap_IntegrationErrorMapInvalidAuthorizationLifeCycle() IntegrationErrorMap {
	u.Use(IntegrationErrorMap_IntegrationErrorMapInvalidAuthorizationLifeCycle)
	return IntegrationErrorMap_IntegrationErrorMapInvalidAuthorizationLifeCycle
}

func (u *IntegrationErrorMapUsage) IntegrationErrorMap_IntegrationErrorMapDuplicateTransmission() IntegrationErrorMap {
	u.Use(IntegrationErrorMap_IntegrationErrorMapDuplicateTransmission)
	return IntegrationErrorMap_IntegrationErrorMapDuplicateTransmission
}

func (u *IntegrationErrorMapUsage) IntegrationErrorMap_IntegrationErrorMapDeclineForCVV2Failure() IntegrationErrorMap {
	u.Use(IntegrationErrorMap_IntegrationErrorMapDeclineForCVV2Failure)
	return IntegrationErrorMap_IntegrationErrorMapDeclineForCVV2Failure
}

func (u *IntegrationErrorMapUsage) IntegrationErrorMap_IntegrationErrorMapCardAuthenticationFailed() IntegrationErrorMap {
	u.Use(IntegrationErrorMap_IntegrationErrorMapCardAuthenticationFailed)
	return IntegrationErrorMap_IntegrationErrorMapCardAuthenticationFailed
}

func (u *IntegrationErrorMapUsage) IntegrationErrorMap_IntegrationErrorMapPickUpCardSpecial() IntegrationErrorMap {
	u.Use(IntegrationErrorMap_IntegrationErrorMapPickUpCardSpecial)
	return IntegrationErrorMap_IntegrationErrorMapPickUpCardSpecial
}

func (u *IntegrationErrorMapUsage) IntegrationErrorMap_IntegrationErrorMapRequestInProgress() IntegrationErrorMap {
	u.Use(IntegrationErrorMap_IntegrationErrorMapRequestInProgress)
	return IntegrationErrorMap_IntegrationErrorMapRequestInProgress
}

func (u *IntegrationErrorMapUsage) IntegrationErrorMap_IntegrationErrorMapApprovedUpdateTrack3() IntegrationErrorMap {
	u.Use(IntegrationErrorMap_IntegrationErrorMapApprovedUpdateTrack3)
	return IntegrationErrorMap_IntegrationErrorMapApprovedUpdateTrack3
}

func (u *IntegrationErrorMapUsage) IntegrationErrorMap_IntegrationErrorMapSuspectedMalfunction() IntegrationErrorMap {
	u.Use(IntegrationErrorMap_IntegrationErrorMapSuspectedMalfunction)
	return IntegrationErrorMap_IntegrationErrorMapSuspectedMalfunction
}

func (u *IntegrationErrorMapUsage) IntegrationErrorMap_IntegrationErrorMapRestrictedCard() IntegrationErrorMap {
	u.Use(IntegrationErrorMap_IntegrationErrorMapRestrictedCard)
	return IntegrationErrorMap_IntegrationErrorMapRestrictedCard
}

func (u *IntegrationErrorMapUsage) IntegrationErrorMap_IntegrationErrorMapPickUpCardStolenCard() IntegrationErrorMap {
	u.Use(IntegrationErrorMap_IntegrationErrorMapPickUpCardStolenCard)
	return IntegrationErrorMap_IntegrationErrorMapPickUpCardStolenCard
}

func (u *IntegrationErrorMapUsage) IntegrationErrorMap_IntegrationErrorMapExceedsWithdrawalLimit() IntegrationErrorMap {
	u.Use(IntegrationErrorMap_IntegrationErrorMapExceedsWithdrawalLimit)
	return IntegrationErrorMap_IntegrationErrorMapExceedsWithdrawalLimit
}

func (u *IntegrationErrorMapUsage) IntegrationErrorMap_IntegrationErrorMapRestrictedCardAlternate() IntegrationErrorMap {
	u.Use(IntegrationErrorMap_IntegrationErrorMapRestrictedCardAlternate)
	return IntegrationErrorMap_IntegrationErrorMapRestrictedCardAlternate
}

func (u *IntegrationErrorMapUsage) IntegrationErrorMap_IntegrationErrorMapResponseReceivedTooLate() IntegrationErrorMap {
	u.Use(IntegrationErrorMap_IntegrationErrorMapResponseReceivedTooLate)
	return IntegrationErrorMap_IntegrationErrorMapResponseReceivedTooLate
}

func (u *IntegrationErrorMapUsage) IntegrationErrorMap_IntegrationErrorMapTooManyWrongPINTries() IntegrationErrorMap {
	u.Use(IntegrationErrorMap_IntegrationErrorMapTooManyWrongPINTries)
	return IntegrationErrorMap_IntegrationErrorMapTooManyWrongPINTries
}

func (u *IntegrationErrorMapUsage) IntegrationErrorMap_IntegrationErrorMapIncorrectCVV() IntegrationErrorMap {
	u.Use(IntegrationErrorMap_IntegrationErrorMapIncorrectCVV)
	return IntegrationErrorMap_IntegrationErrorMapIncorrectCVV
}

func (u *IntegrationErrorMapUsage) IntegrationErrorMap_IntegrationErrorMapNoReasonToDecline() IntegrationErrorMap {
	u.Use(IntegrationErrorMap_IntegrationErrorMapNoReasonToDecline)
	return IntegrationErrorMap_IntegrationErrorMapNoReasonToDecline
}

func (u *IntegrationErrorMapUsage) IntegrationErrorMap_IntegrationErrorMapDeclinedByGateway() IntegrationErrorMap {
	u.Use(IntegrationErrorMap_IntegrationErrorMapDeclinedByGateway)
	return IntegrationErrorMap_IntegrationErrorMapDeclinedByGateway
}

func (u *IntegrationErrorMapUsage) IntegrationErrorMap_IntegrationErrorMapTransactionAmountExceedsPreAuth() IntegrationErrorMap {
	u.Use(IntegrationErrorMap_IntegrationErrorMapTransactionAmountExceedsPreAuth)
	return IntegrationErrorMap_IntegrationErrorMapTransactionAmountExceedsPreAuth
}

func (u *IntegrationErrorMapUsage) IntegrationErrorMap_IntegrationErrorMapApproved() IntegrationErrorMap {
	u.Use(IntegrationErrorMap_IntegrationErrorMapApproved)
	return IntegrationErrorMap_IntegrationErrorMapApproved
}

func (u *IntegrationErrorMapUsage) IntegrationErrorMap_IntegrationErrorMapPickUpCard() IntegrationErrorMap {
	u.Use(IntegrationErrorMap_IntegrationErrorMapPickUpCard)
	return IntegrationErrorMap_IntegrationErrorMapPickUpCard
}

func (u *IntegrationErrorMapUsage) IntegrationErrorMap_IntegrationErrorMapDoNotHonor() IntegrationErrorMap {
	u.Use(IntegrationErrorMap_IntegrationErrorMapDoNotHonor)
	return IntegrationErrorMap_IntegrationErrorMapDoNotHonor
}

func (u *IntegrationErrorMapUsage) IntegrationErrorMap_IntegrationErrorMapUnableToLocateRecord() IntegrationErrorMap {
	u.Use(IntegrationErrorMap_IntegrationErrorMapUnableToLocateRecord)
	return IntegrationErrorMap_IntegrationErrorMapUnableToLocateRecord
}

func (u *IntegrationErrorMapUsage) IntegrationErrorMap_IntegrationErrorMapFileUpdateFieldEditError() IntegrationErrorMap {
	u.Use(IntegrationErrorMap_IntegrationErrorMapFileUpdateFieldEditError)
	return IntegrationErrorMap_IntegrationErrorMapFileUpdateFieldEditError
}

func (u *IntegrationErrorMapUsage) IntegrationErrorMap_IntegrationErrorMapFileTemporarilyUnavailable() IntegrationErrorMap {
	u.Use(IntegrationErrorMap_IntegrationErrorMapFileTemporarilyUnavailable)
	return IntegrationErrorMap_IntegrationErrorMapFileTemporarilyUnavailable
}

func (u *IntegrationErrorMapUsage) IntegrationErrorMap_IntegrationErrorMapCardAcceptor() IntegrationErrorMap {
	u.Use(IntegrationErrorMap_IntegrationErrorMapCardAcceptor)
	return IntegrationErrorMap_IntegrationErrorMapCardAcceptor
}

func (u *IntegrationErrorMapUsage) IntegrationErrorMap_IntegrationErrorMapAllowablePINTriesExceeded() IntegrationErrorMap {
	u.Use(IntegrationErrorMap_IntegrationErrorMapAllowablePINTriesExceeded)
	return IntegrationErrorMap_IntegrationErrorMapAllowablePINTriesExceeded
}

func (u *IntegrationErrorMapUsage) IntegrationErrorMap_IntegrationErrorMapPickUpCardLostCard() IntegrationErrorMap {
	u.Use(IntegrationErrorMap_IntegrationErrorMapPickUpCardLostCard)
	return IntegrationErrorMap_IntegrationErrorMapPickUpCardLostCard
}

func (u *IntegrationErrorMapUsage) IntegrationErrorMap_IntegrationErrorMapNoSavingsAccount() IntegrationErrorMap {
	u.Use(IntegrationErrorMap_IntegrationErrorMapNoSavingsAccount)
	return IntegrationErrorMap_IntegrationErrorMapNoSavingsAccount
}

func (u *IntegrationErrorMapUsage) IntegrationErrorMap_IntegrationErrorMapIncorrectPIN() IntegrationErrorMap {
	u.Use(IntegrationErrorMap_IntegrationErrorMapIncorrectPIN)
	return IntegrationErrorMap_IntegrationErrorMapIncorrectPIN
}

func (u *IntegrationErrorMapUsage) IntegrationErrorMap_IntegrationErrorMapCashServiceNotAvailable() IntegrationErrorMap {
	u.Use(IntegrationErrorMap_IntegrationErrorMapCashServiceNotAvailable)
	return IntegrationErrorMap_IntegrationErrorMapCashServiceNotAvailable
}

func (u *IntegrationErrorMapUsage) IntegrationErrorMap_IntegrationErrorMapForwardIssuer() IntegrationErrorMap {
	u.Use(IntegrationErrorMap_IntegrationErrorMapForwardIssuer)
	return IntegrationErrorMap_IntegrationErrorMapForwardIssuer
}

func (u *IntegrationErrorMapUsage) IntegrationErrorMap_IntegrationErrorMapCGICheckFailed() IntegrationErrorMap {
	u.Use(IntegrationErrorMap_IntegrationErrorMapCGICheckFailed)
	return IntegrationErrorMap_IntegrationErrorMapCGICheckFailed
}

func (u *IntegrationErrorMapUsage) IntegrationErrorMap_IntegrationErrorMapInvalidAmountField() IntegrationErrorMap {
	u.Use(IntegrationErrorMap_IntegrationErrorMapInvalidAmountField)
	return IntegrationErrorMap_IntegrationErrorMapInvalidAmountField
}

func (u *IntegrationErrorMapUsage) IntegrationErrorMap_IntegrationErrorMapInvalidCurrencyField() IntegrationErrorMap {
	u.Use(IntegrationErrorMap_IntegrationErrorMapInvalidCurrencyField)
	return IntegrationErrorMap_IntegrationErrorMapInvalidCurrencyField
}

func (u *IntegrationErrorMapUsage) IntegrationErrorMap_IntegrationErrorMapHostNotResponding() IntegrationErrorMap {
	u.Use(IntegrationErrorMap_IntegrationErrorMapHostNotResponding)
	return IntegrationErrorMap_IntegrationErrorMapHostNotResponding
}

func (u *IntegrationErrorMapUsage) IntegrationErrorMap_IntegrationErrorMapNoConnectionWithPINPad() IntegrationErrorMap {
	u.Use(IntegrationErrorMap_IntegrationErrorMapNoConnectionWithPINPad)
	return IntegrationErrorMap_IntegrationErrorMapNoConnectionWithPINPad
}

func (u *IntegrationErrorMapUsage) IntegrationErrorMap_IntegrationErrorMapConnectionErrorDuringProcessing() IntegrationErrorMap {
	u.Use(IntegrationErrorMap_IntegrationErrorMapConnectionErrorDuringProcessing)
	return IntegrationErrorMap_IntegrationErrorMapConnectionErrorDuringProcessing
}

func (u *IntegrationErrorMapUsage) IntegrationErrorMap_IntegrationErrorMapMissingMandatoryField() IntegrationErrorMap {
	u.Use(IntegrationErrorMap_IntegrationErrorMapMissingMandatoryField)
	return IntegrationErrorMap_IntegrationErrorMapMissingMandatoryField
}

func (u *IntegrationErrorMapUsage) IntegrationErrorMap_IntegrationErrorMapNoConnectionWithHost() IntegrationErrorMap {
	u.Use(IntegrationErrorMap_IntegrationErrorMapNoConnectionWithHost)
	return IntegrationErrorMap_IntegrationErrorMapNoConnectionWithHost
}

func (u *IntegrationErrorMapUsage) IntegrationErrorMap_IntegrationErrorMapInvalidMerchantIDField() IntegrationErrorMap {
	u.Use(IntegrationErrorMap_IntegrationErrorMapInvalidMerchantIDField)
	return IntegrationErrorMap_IntegrationErrorMapInvalidMerchantIDField
}

func (u *IntegrationErrorMapUsage) IntegrationErrorMap_IntegrationErrorMapInvalidRRNField() IntegrationErrorMap {
	u.Use(IntegrationErrorMap_IntegrationErrorMapInvalidRRNField)
	return IntegrationErrorMap_IntegrationErrorMapInvalidRRNField
}

func (u *IntegrationErrorMapUsage) IntegrationErrorMap_IntegrationErrorMapAccessDenied() IntegrationErrorMap {
	u.Use(IntegrationErrorMap_IntegrationErrorMapAccessDenied)
	return IntegrationErrorMap_IntegrationErrorMapAccessDenied
}

func (u *IntegrationErrorMapUsage) IntegrationErrorMap_IntegrationErrorMapTerminalBusy() IntegrationErrorMap {
	u.Use(IntegrationErrorMap_IntegrationErrorMapTerminalBusy)
	return IntegrationErrorMap_IntegrationErrorMapTerminalBusy
}

func (u *IntegrationErrorMapUsage) IntegrationErrorMap_IntegrationErrorMapIPAddressMismatch() IntegrationErrorMap {
	u.Use(IntegrationErrorMap_IntegrationErrorMapIPAddressMismatch)
	return IntegrationErrorMap_IntegrationErrorMapIPAddressMismatch
}

func (u *IntegrationErrorMapUsage) IntegrationErrorMap_IntegrationErrorMapAuthenticationError() IntegrationErrorMap {
	u.Use(IntegrationErrorMap_IntegrationErrorMapAuthenticationError)
	return IntegrationErrorMap_IntegrationErrorMapAuthenticationError
}

func (u *IntegrationErrorMapUsage) IntegrationErrorMap_IntegrationErrorMapEGatewaySetupError() IntegrationErrorMap {
	u.Use(IntegrationErrorMap_IntegrationErrorMapEGatewaySetupError)
	return IntegrationErrorMap_IntegrationErrorMapEGatewaySetupError
}

func (u *IntegrationErrorMapUsage) IntegrationErrorMap_IntegrationErrorMapIncorrectHostResponse() IntegrationErrorMap {
	u.Use(IntegrationErrorMap_IntegrationErrorMapIncorrectHostResponse)
	return IntegrationErrorMap_IntegrationErrorMapIncorrectHostResponse
}

func (u *IntegrationErrorMapUsage) IntegrationErrorMap_IntegrationErrorMapInvalidCardNumberField() IntegrationErrorMap {
	u.Use(IntegrationErrorMap_IntegrationErrorMapInvalidCardNumberField)
	return IntegrationErrorMap_IntegrationErrorMapInvalidCardNumberField
}

func (u *IntegrationErrorMapUsage) IntegrationErrorMap_IntegrationErrorMapInvalidCardExpirationDateField() IntegrationErrorMap {
	u.Use(IntegrationErrorMap_IntegrationErrorMapInvalidCardExpirationDateField)
	return IntegrationErrorMap_IntegrationErrorMapInvalidCardExpirationDateField
}

func (u *IntegrationErrorMapUsage) IntegrationErrorMap_IntegrationErrorMapInvalidCVC2Field() IntegrationErrorMap {
	u.Use(IntegrationErrorMap_IntegrationErrorMapInvalidCVC2Field)
	return IntegrationErrorMap_IntegrationErrorMapInvalidCVC2Field
}

func (u *IntegrationErrorMapUsage) IntegrationErrorMap_IntegrationErrorMapTimeExceeded() IntegrationErrorMap {
	u.Use(IntegrationErrorMap_IntegrationErrorMapTimeExceeded)
	return IntegrationErrorMap_IntegrationErrorMapTimeExceeded
}
