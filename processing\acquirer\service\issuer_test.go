package service

import (
	"context"
	"errors"
	"testing"

	"github.com/golang/mock/gomock"
	"github.com/stretchr/testify/require"

	"git.local/sensitive/pkg/middlewares"
	"git.local/sensitive/processing/acquirer/model"
	"git.local/sensitive/processing/acquirer/repository/database/databasemocks"
)

func TestGetAllIssuer(t *testing.T) {
	type getAllOp struct {
		input     *middlewares.PaginationInfo
		output    []*model.BankBins
		outputErr error
	}

	tests := []struct {
		name    string
		req     *middlewares.PaginationInfo
		want    []*model.BankBins
		wantErr error
		getAll  getAllOp
	}{
		{
			name: "error in get all operation",
			req: &middlewares.PaginationInfo{
				PerPage:    5,
				Page:       1,
				Pagination: true,
			},
			want:    nil,
			wantErr: errors.New("error in get all operation"),
			getAll: getAllOp{
				input: &middlewares.PaginationInfo{
					PerPage:    5,
					Page:       1,
					Pagination: true,
				},
				output:    nil,
				outputErr: errors.New("error in get all operation"),
			},
		},
		{
			name: "success",
			req: &middlewares.PaginationInfo{
				PerPage:    5,
				Page:       1,
				Pagination: true,
			},
			want: []*model.BankBins{
				{
					ID:     1,
					BankID: 1,
					Bin:    "***********",
					Bank: model.Bank{
						ID:    1,
						Name:  "bcc",
						Bik:   "bcc bik",
						Swift: "bcc swift",
					},
				},
				{
					ID:     2,
					BankID: 2,
					Bin:    "***********",
					Bank: model.Bank{
						ID:    2,
						Name:  "epay",
						Bik:   "epay bik",
						Swift: "epay swift",
					},
				},
			},
			wantErr: nil,
			getAll: getAllOp{
				input: &middlewares.PaginationInfo{
					PerPage:    5,
					Page:       1,
					Pagination: true,
				},
				output: []*model.BankBins{
					{
						ID:     1,
						BankID: 1,
						Bin:    "***********",
						Bank: model.Bank{
							ID:    1,
							Name:  "bcc",
							Bik:   "bcc bik",
							Swift: "bcc swift",
						},
					},
					{
						ID:     2,
						BankID: 2,
						Bin:    "***********",
						Bank: model.Bank{
							ID:    2,
							Name:  "epay",
							Bik:   "epay bik",
							Swift: "epay swift",
						},
					},
				},
				outputErr: nil,
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctrl := gomock.NewController(t)
			defer ctrl.Finish()

			issuerDBMock := databasemocks.NewMockIssuer(ctrl)

			issuerService := NewIssuerService(issuerDBMock)

			issuerDBMock.EXPECT().GetAll(
				gomock.Any(),
				tt.getAll.input,
			).Return(tt.getAll.output, tt.getAll.outputErr)

			resp, err := issuerService.GetAll(context.Background(), tt.req)
			if tt.wantErr != nil {
				require.Error(t, err)
				require.Equal(t, tt.wantErr, err)
			} else {
				require.NoError(t, err)
			}

			require.Equal(t, tt.want, resp)
		})
	}
}

func TestGetByIDIssuer(t *testing.T) {
	type getByIDOp struct {
		input     uint64
		output    model.Bank
		outputErr error
	}

	tests := []struct {
		name    string
		req     uint64
		want    model.Bank
		wantErr error
		getByID getByIDOp
	}{
		{
			name: "error",
			req:  123,
			getByID: getByIDOp{
				input:     123,
				output:    model.Bank{},
				outputErr: errors.New("error in get operation"),
			},
			wantErr: errors.New("error in get operation"),
			want:    model.Bank{},
		},
		{
			name: "success",
			req:  123,
			getByID: getByIDOp{
				input: 123,
				output: model.Bank{
					ID:    1234,
					Name:  "asd bank",
					Swift: "asd bank",
					Bik:   "asd bank",
				},
				outputErr: nil,
			},
			wantErr: nil,
			want: model.Bank{
				ID:    1234,
				Name:  "asd bank",
				Swift: "asd bank",
				Bik:   "asd bank",
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {

			ctrl := gomock.NewController(t)
			defer ctrl.Finish()

			issuerDBMock := databasemocks.NewMockIssuer(ctrl)

			issuerService := NewIssuerService(issuerDBMock)

			issuerDBMock.EXPECT().GetByID(
				gomock.Any(),
				tt.getByID.input,
			).Return(tt.getByID.output, tt.getByID.outputErr)

			resp, err := issuerService.GetByID(context.Background(), tt.req)
			if tt.wantErr != nil {
				require.Error(t, err)
				require.Equal(t, tt.wantErr, err)
			} else {
				require.NoError(t, err)
			}

			require.Equal(t, tt.want, resp)
		})
	}
}
