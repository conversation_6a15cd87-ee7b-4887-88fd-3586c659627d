edition = "2023";

package processing.billing.billing;

option go_package = "git.local/sensitive/innerpb/processing/grpc";

import "google/protobuf/timestamp.proto";
import "google/protobuf/empty.proto";
import "inner/processing/grpc/commission.proto";
import "mvp/proto/refs.proto";
import "google/protobuf/descriptor.proto";

service Billing {
  rpc CheckPayOutBalanceV1(CheckPayOutBalanceReqV1) returns (CheckPayOutBalanceResV1) {}
  rpc BillPayInTransactionV1(BillPayInTransactionRequestV1) returns (google.protobuf.Empty) {}
  rpc BillPayOutTransactionV1(BillPayOutTransactionRequestV1) returns (google.protobuf.Empty) {}
  rpc BillRefundTransactionV1(BillRefundTransactionRequestV1) returns (google.protobuf.Empty) {}
  rpc CheckOutTransferBalanceV1(CheckOutTransferBalanceRequestV1) returns (CheckOutTransferBalanceResponseV1) {}
  rpc BillOutTransferV1(BillOutTransferRequestV1) returns (google.protobuf.Empty) {}
  rpc BillInTransferV1(BillInTransferRequestV1) returns (google.protobuf.Empty) {}
  rpc GetMerchantByBalanceOwnerIDV1(GetMerchantByBalanceOwnerRequestV1) returns (GetMerchantByBalanceOwnerResponseV1) {}
  rpc SetInTransferV1(SetInTransferRequestV1) returns (google.protobuf.Empty) {}
  rpc BillSplitTransferV1(BillSplitTransferRequestV1) returns (google.protobuf.Empty) {}
  rpc GetBalanceOwnerV1(GetBalanceOwnerRequestV1) returns (GetBalanceOwnerResponseV1) {}
  rpc SetBalanceOwnerSplittableV1(SetBalanceOwnerSplittableRequestV1) returns (google.protobuf.Empty);
  rpc GetBalanceOwnerByIDV1(GetBalanceOwnerByIDRequestV1) returns (GetBalanceOwnerResponseV1) {}
  rpc GetEntityTypeByIDV1(GetEntityTypeByIDRequestV1) returns (GetEntityTypeResponseV1) {}
  rpc GetCountryCodeByIDV1(GetCountryCodeByIDRequestV1) returns (GetCountryCodeResponseV1) {}
  rpc GetBalanceByIDV1(GetBalanceByIDRequestV1) returns (GetBalanceResponseV1) {}
  rpc CheckHasBalanceV1(CheckHasBalanceRequestV1) returns (google.protobuf.Empty) {}
  rpc CheckBalanceOwnerAccountExistsByBalanceOwnerIdAndAccountIdV1(CheckBalanceOwnerAccountExistsByBalanceOwnerIdAndAccountIdRequestV1) returns (google.protobuf.Empty) {}
  rpc GetBalanceAccountByNumber(GetBalanceAccountByNumberRequest) returns (GetBalanceAccountByNumberResponse) {}
  rpc GetCurrentBalanceAmountByBalanceOwnerID(GetCurrentBalanceAmountByAccountAndOwnerIDRequest) returns (GetCurrentBalanceAmountByAccountAndOwnerIDResponse) {}

  //jobs
  rpc CheckBalanceCreditExpireDate(google.protobuf.Empty) returns (google.protobuf.Empty) {}
  rpc CheckBalanceCreditStartDate(google.protobuf.Empty) returns (google.protobuf.Empty) {}
  rpc RecalculateProvisionalBalances(google.protobuf.Empty) returns (google.protobuf.Empty) {}
  rpc RecalculateFinalBalances(google.protobuf.Empty) returns (google.protobuf.Empty) {}
  rpc RecalculateCreditBalances(google.protobuf.Empty) returns (google.protobuf.Empty) {}
}

message CheckPayOutBalanceReqV1  {
  uint64 merchant_id = 1;
  uint64 project_id = 2;
  string account_number = 3;
  double amount = 4;
}

message CheckPayOutBalanceResV1 {
  bool has_sufficient_balance = 1;
}

message BillPayInTransactionRequestV1 {
  uint64 merchant_id = 1;
  uint64 project_id = 2;
  uint64 transaction_id = 3;
  double amount = 4;
  uint64 terminal_id = 5;
}

message BillPayOutTransactionRequestV1 {
  uint64 merchant_id = 1;
  uint64 project_id = 2;
  uint64 transaction_id = 3;
  double amount = 4;
  uint64 terminal_id = 5;
  google.protobuf.Timestamp transaction_created_at = 6;
}

message GetCurrentBalanceAmountByAccountAndOwnerIDRequest {
  uint64 balance_owner_id = 1;
  uint64 account_id = 2;
}

message GetCurrentBalanceAmountByAccountAndOwnerIDResponse {
  double amount = 1;
}

message BillRefundTransactionRequestV1 {
  uint64 merchant_id = 1;
  uint64 project_id = 2;
  uint64 refund_id = 3;
  double amount = 4;
  uint64 terminal_id = 5;
}

message CheckBalanceOwnerAccountExistsByBalanceOwnerIdAndAccountIdRequestV1 {
  uint64 balance_owner_id = 1;
  uint64 account_id = 2;
}

message GetBalanceAccountByNumberRequest {
  string account_number = 1;
}

message GetBalanceAccountByNumberResponse {
  uint64 account_id = 1;
  string account_number = 2;
  uint64 status_id = 3;
}

message CheckOutTransferBalanceRequestV1 {
  uint64 project_id = 1;
  string account_number = 2;
  double amount = 3;
  uint64 merchant_id = 4;
}

message SetBalanceOwnerSplittableRequestV1 {
  uint64 balance_owner_id = 1;
}

message GetBalanceOwnerRequestV1 {
  uint64 project_id = 1;
  uint64 merchant_id = 2;
}

message CheckOutTransferBalanceResponseV1 {
  bool is_sufficient = 1;
}

message BillOutTransferRequestV1 {
  uint64 project_id = 1;
  string account_number = 2;
  double amount = 3;
  uint64 merchant_id = 4;
  uint64 transfer_id = 5;
}

message BillInTransferRequestV1 {
  uint64 project_id = 1;
  string account_number = 2;
  double amount = 3;
  uint64 merchant_id = 4;
  uint64 transfer_id = 5;
}


message GetMerchantByBalanceOwnerRequestV1 {
  uint64 balance_owner_id = 1;
}

message GetMerchantByBalanceOwnerResponseV1 {
  uint64 merchant_id = 1;
  uint64 project_id = 2;
}

message GetBalanceOwnerResponseV1 {
  uint64 id = 3;
  string name = 4;
  uint64 status_id = 5;
  bool has_credit_first = 6;
  uint64 merchant_id = 7;
  uint64 project_id = 8;
  string bin = 9;
  uint64 earn_type_id = 10;
  bool is_splittable = 11;
  uint64 entity_type_id = 12;
  uint64 country_code_id = 13;
}

message SetInTransferRequestV1 {
  uint64 transfer_id = 1;
  uint64 merchant_id = 2;
  uint64 project_id = 3;
  string account_number = 4;
  double amount = 5;
}

message SplitTransferOperationV1{
  uint64 transfer_id = 1;
  uint64 split_operation_id = 2;
  uint64 merchant_id = 3;
  uint64 project_id = 4;
  string account_number = 5;
  double amount = 6;
  double system_tax = 7;
  repeated processing.commission.commission.CommissionV1 commission_list = 8;
}

message BillSplitTransferRequestV1 {
  repeated SplitTransferOperationV1 operations = 1;
}

message GetBalanceOwnerByIDRequestV1 {
  uint64 balance_owner_id = 1;
}

message GetEntityTypeByIDRequestV1 {
  uint64 id = 1;
}

message GetEntityTypeResponseV1 {
  uint64 id = 1;
  string code = 2;
  string name = 3;
}

message GetCountryCodeByIDRequestV1 {
  uint64 id = 1;
}

message GetCountryCodeResponseV1 {
  uint64 id = 1;
  string code = 2;
  string kaz_name = 3;
  string eng_name = 4;
  string ru_name = 5;
}

message GetBalanceByIDRequestV1 {
  uint64 id = 1;
}

message GetBalanceResponseV1 {
  uint64 id = 1;
  uint64 owner_id = 2;
  uint64 account_id = 3;
  double amount = 4;
  google.protobuf.Timestamp last_calc_time = 5;
  double hold_amount = 6;
  uint64 type_id = 7;
}

message CheckHasBalanceRequestV1 {
  uint64 project_id = 1;
  uint64 merchant_id = 2;
  string account_number = 3;
}

message BillingOperationBalanceTypeRef{
  string code = 1;
  string name = 2;
}

extend google.protobuf.EnumValueOptions {
  BillingOperationBalanceTypeRef operation_balance_type_value = 100100;
}

extend google.protobuf.EnumOptions {
  BillingOperationBalanceTypeRef default_operation_balance_type_value = 100101;
}

enum BillingOperationBalanceType {
  option(mvp.default_ref) = "default_operation_balance_type_value";
  option(mvp.ref) = "operation_balance_type_value";
  option(default_operation_balance_type_value) = {
    code: "unknown",
    name: "unknown operation balance type"
  };
  BillingOperationBalanceTypeUnknown = 0 [(operation_balance_type_value) = {
    code: "unknown",
    name: "unknown operation balance type"
  }];
  BillingOperationBalanceTypeMain = 1 [(operation_balance_type_value) = {
    code: "main",
    name: "Основной баланс"
  }];
  BillingOperationBalanceTypeCredit = 2 [(operation_balance_type_value) = {
    code: "credit",
    name: "Кредитный баланс"
  }];
}

message BillingOperationTypeRef{
  string code = 1;
  string name = 2;
}

extend google.protobuf.EnumValueOptions {
  BillingOperationTypeRef operation_type_value = 100102;
}

extend google.protobuf.EnumOptions {
  BillingOperationTypeRef default_operation_type_value = 100103;
}

enum BillingOperationType {
  option(mvp.default_ref) = "default_operation_type_value";
  option(mvp.ref) = "operation_type_value";
  option(default_operation_type_value) = {
    code: "unknown",
    name: "unknown operation type"
  };
  BillingOperationTypeUnknown = 0 [(operation_type_value) = {
    code: "unknown",
    name: "unknown operation type"
  }];
  BillingOperationTypePayIn = 1 [(operation_type_value) = {
    code: "pay_in",
    name: "Прием"
  }];
  BillingOperationTypePayOut = 2 [(operation_type_value) = {
    code: "pay_out",
    name: "Вывод"
  }];
  BillingOperationTypeRefund = 3 [(operation_type_value) = {
    code: "refund",
    name: "Возврат"
  }];
  BillingOperationTypeInTransfer = 4 [(operation_type_value) = {
    code: "in_transfer",
    name: "Фин. операция прием"
  }];
  BillingOperationTypeOutTransfer = 5 [(operation_type_value) = {
    code: "out_transfer",
    name: "Фин. операция вывод"
  }];
  BillingOperationTypePayInCommissionOutAcquirer = 6 [(operation_type_value) = {
    code: "pay_in_cms_out_acr",
    name: "Удержание комиссии эквайера (прием)"
  }];
  BillingOperationTypePayInCommissionInSystem = 7 [(operation_type_value) = {
    code: "pay_in_cms_in_sys",
    name: "Пополнение комиссии Тарлан (прием)"
  }];
  BillingOperationTypePayInCommissionOutSystem = 8 [(operation_type_value) = {
    code: "pay_in_cms_out_sys",
    name: "Удержание комиссии Тарлан (прием)"
  }];
  BillingOperationTypePayInCommissionInMerchant = 9 [(operation_type_value) = {
    code: "pay_in_cms_in_mrc",
    name: "Пополнение комиссии мерчанта (прием)"
  }];
  BillingOperationTypePayInCommissionOutMerchant = 10 [(operation_type_value) = {
    code: "pay_in_cms_out_mrc",
    name: "Удержание комиссии мерчанта (прием)"
  }];
  BillingOperationTypeInternalInTransfer = 11 [(operation_type_value) = {
    code: "internal_in_transfer",
    name: "Внутренний прием"
  }];
  BillingOperationTypeInternalOutTransfer = 12 [(operation_type_value) = {
    code: "internal_out_transfer",
    name: "Внутренний вывод"
  }];
  BillingOperationTypeSplitOutTransfer = 13 [(operation_type_value) = {
    code: "split_out_transfer",
    name: "Удержание сплит. средств"
  }];
  BillingOperationTypeSplitInTransfer = 14 [(operation_type_value) = {
    code: "split_in_transfer",
    name: "Пополнение сплит. средств"
  }];
  BillingOperationTypePayOutCommissionOutAcquirer = 15 [(operation_type_value) = {
    code: "pay_out_cms_out_acr",
    name: "Удержание комиссии эквайера (вывод)"
  }];
  BillingOperationTypePayOutCommissionInSystem = 16 [(operation_type_value) = {
    code: "pay_out_cms_in_sys",
    name: "Пополнение комиссии Тарлан (вывод)"
  }];
  BillingOperationTypePayOutCommissionOutSystem = 17 [(operation_type_value) = {
    code: "pay_out_cms_out_sys",
    name: "Удержание комиссии Тарлан (вывод)"
  }];
  BillingOperationTypePayOutCommissionInMerchant = 18 [(operation_type_value) = {
    code: "pay_out_cms_in_mrc",
    name: "Пополнение комиссии мерчанта (вывод)"
  }];
  BillingOperationTypePayOutCommissionOutMerchant = 19 [(operation_type_value) = {
    code: "pay_out_cms_out_mrc",
    name: "Удержание комиссии мерчанта (вывод)"
  }];
  BillingOperationTypeSplitCommissionInSystem = 20 [(operation_type_value) = {
    code: "split_cms_in_sys",
    name: "Пополнение комиссии Тарлан (сплитование)"
  }];
  BillingOperationTypeSplitCommissionOutSystem = 21 [(operation_type_value) = {
    code: "split_cms_out_sys",
    name: "Удержание комиссии Тарлан (сплитование)"
  }];
  BillingOperationTypeSplitCommissionInMerchant = 22 [(operation_type_value) = {
    code: "split_cms_in_mrc",
    name: "Пополнение комиссии мерчанта (сплитование)"
  }];
  BillingOperationTypeSplitCommissionOutMerchant = 23 [(operation_type_value) = {
    code: "split_cms_out_mrc",
    name: "Удержание комиссии мерчанта (сплитование)"
  }];
}

message BillingOperationStatusRef {
  string code = 1;
  string name = 2;
}

extend google.protobuf.EnumValueOptions {
  BillingOperationStatusRef operation_status_value = 100104;
}

extend google.protobuf.EnumOptions {
  BillingOperationStatusRef default_operation_status_value = 100105;
}

enum BillingOperationStatus {
  option(mvp.default_ref) = "default_operation_status_value";
  option(mvp.ref) = "operation_status_value";
  option(default_operation_status_value) = {
    code: "unknown",
    name: "unknown operation status"
  };
  BillingOperationStatusUnknown = 0 [(operation_status_value) = {
    code: "unknown",
    name: "unknown operation status"
  }];
  BillingOperationStatusFinalized = 1 [(operation_status_value) = {
    code: "finalized",
    name: "Операция финализирована"
  }];
  BillingOperationStatusInProgress = 2 [(operation_status_value) = {
    code: "in_progress",
    name: "Операция в процессе"
  }];
}

message BillingOperationTypeGroupRef {
  string code = 1;
  string name = 2;
}

extend google.protobuf.EnumValueOptions {
  BillingOperationTypeGroupRef operation_type_group_value = 100106;
}

extend google.protobuf.EnumOptions {
  BillingOperationTypeGroupRef default_operation_type_group_value = 100107;
}

enum BillingOperationTypeGroup {
  option(mvp.default_ref) = "default_operation_type_group_value";
  option(mvp.ref) = "operation_type_group_value";
  option(default_operation_type_group_value) = {
    code: "unknown",
    name: "unknown operation type group"
  };
  BillingOperationTypeGroupUnknown = 0 [(operation_type_group_value) = {
    code: "unknown",
    name: "unknown operation type group"
  }];
  BillingOperationTypeGroupIn = 1 [(operation_type_group_value) = {
    code: "in",
    name: "in"
  }];
  BillingOperationTypeGroupOut = 2 [(operation_type_group_value) = {
    code: "out",
    name: "out"
  }];
  BillingOperationTypeGroupRefund = 3 [(operation_type_group_value) = {
    code: "refund",
    name: "refund"
  }];
  BillingOperationTypeGroupPayIn = 4 [(operation_type_group_value) = {
    code: "pay_in",
    name: "pay_in"
  }];
}

message BillingEarnTypeRef {
  string code = 1;
  string name = 2;
}

extend google.protobuf.EnumValueOptions {
  BillingEarnTypeRef earn_type_value = 100108;
}

extend google.protobuf.EnumOptions {
  BillingEarnTypeRef default_earn_type_value = 100109;
}

enum BillingEarnType {
  option(mvp.default_ref) = "default_earn_type_value";
  option(mvp.ref) = "earn_type_value";
  option(default_earn_type_value) = {
    code: "unknown",
    name: "unknown earn type"
  };
  BillingEarnTypeUnknown = 0 [(earn_type_value) = {
    code: "unknown",
    name: "unknown earn type"
  }];
  BillingEarnTypeCommission = 1 [(earn_type_value) = {
    code: "commission",
    name: "Комиссионная модель дохода"
  }];
  BillingEarnTypeFee = 2 [(earn_type_value) = {
    code: "fee",
    name: "Подписочная модель дохода"
  }];
}

message BillingBalanceOwnerTypeRef {
  string code = 1;
  string name = 2;
}

extend google.protobuf.EnumValueOptions {
  BillingBalanceOwnerTypeRef balance_owner_type_value = 1200110;
}

extend google.protobuf.EnumOptions {
  BillingBalanceOwnerTypeRef default_balance_owner_type_value = 1200111;
}

enum BillingBalanceOwnerType {
  option(mvp.default_ref) = "default_balance_owner_type_value";
  option(mvp.ref) = "balance_owner_type_value";
  option(default_balance_owner_type_value) = {
    code: "unknown",
    name: "unknown balance owner type"
  };
  BillingBalanceOwnerTypeUnknown = 0 [(balance_owner_type_value) = {
    code: "unknown",
    name: "unknown balance owner type"
  }];
  BillingBalanceOwnerTypeSystem = 1 [(balance_owner_type_value) = {
    code: "system",
    name: "Система"
  }];
  BillingBalanceOwnerTypeMerchant = 2 [(balance_owner_type_value) = {
    code: "merchant",
    name: "Мерчант"
  }];
  BillingBalanceOwnerTypeProject = 3 [(balance_owner_type_value) = {
    code: "project",
    name: "Проект"
  }];
}

message BillingBalanceOwnerStatusRef {
  string code = 1;
  string name = 2;
}

extend google.protobuf.EnumValueOptions {
  BillingBalanceOwnerStatusRef balance_owner_status_value = 100112;
}

extend google.protobuf.EnumOptions {
  BillingBalanceOwnerStatusRef default_balance_owner_status_value = 100113;
}

enum BillingBalanceOwnerStatus {
  option(mvp.default_ref) = "default_balance_owner_status_value";
  option(mvp.ref) = "balance_owner_status_value";
  option(default_balance_owner_status_value) = {
    code: "unknown",
    name: "unknown balance owner status"
  };
  BillingBalanceOwnerStatusUnknown = 0 [(balance_owner_status_value) = {
    code: "unknown",
    name: "unknown balance owner status"
  }];
  BillingBalanceOwnerStatusActive = 1 [(balance_owner_status_value) = {
    code: "active",
    name: "Активный"
  }];
  BillingBalanceOwnerStatusInactive = 2 [(balance_owner_status_value) = {
    code: "inactive",
    name: "Неактивный"
  }];
}

message BillingBalanceCreditStatusRef {
  string code = 1;
  string name = 2;
}

extend google.protobuf.EnumValueOptions {
  BillingBalanceCreditStatusRef balance_credit_status_value = 100114;
}

extend google.protobuf.EnumOptions {
  BillingBalanceCreditStatusRef default_balance_credit_status_value = 100115;
}

enum BillingBalanceCreditStatus {
  option(mvp.default_ref) = "default_balance_credit_status_value";
  option(mvp.ref) = "balance_credit_status_value";
  option(default_balance_credit_status_value) = {
    code: "unknown",
    name: "unknown balance credit status"
  };
  BillingBalanceCreditStatusUnknown = 0 [(balance_credit_status_value) = {
    code: "unknown",
    name: "unknown balance credit status"
  }];
  BillingBalanceCreditStatusActive = 1 [(balance_credit_status_value) = {
    code: "active",
    name: "Активный"
  }];
  BillingBalanceCreditStatusInactive = 2 [(balance_credit_status_value) = {
    code: "inactive",
    name: "Неактивный"
  }];
  BillingBalanceCreditStatusIdle = 3 [(balance_credit_status_value) = {
    code: "idle",
    name: "В ожидании активации"
  }];
}

message BillingBalanceAccountStatusRef {
  string code = 1;
  string name = 2;
}

extend google.protobuf.EnumValueOptions {
  BillingBalanceAccountStatusRef balance_account_status_value = 100116;
}

extend google.protobuf.EnumOptions {
  BillingBalanceAccountStatusRef default_balance_account_status_value = 100117;
}

enum BillingBalanceAccountStatus {
  option(mvp.default_ref) = "default_balance_account_status_value";
  option(mvp.ref) = "balance_account_status_value";
  option(default_balance_account_status_value) = {
    code: "unknown",
    name: "unknown balance account status"
  };
  BillingBalanceAccountStatusUnknown = 0 [(balance_account_status_value) = {
    code: "unknown",
    name: "unknown balance account status"
  }];
  BillingBalanceAccountStatusActive = 1 [(balance_account_status_value) = {
    code: "active",
    name: "Активный"
  }];
  BillingBalanceAccountStatusInactive = 2 [(balance_account_status_value) = {
    code: "inactive",
    name: "Неактивный"
  }];
}

message BillingOperationModeRef {
  string code = 1;
  string name = 2;
}

extend google.protobuf.EnumValueOptions {
  BillingOperationModeRef operation_mode_value = 100118;
}

extend google.protobuf.EnumOptions {
  BillingOperationModeRef default_operation_mode_value = 100119;
}

enum BillingOperationMode {
  option(mvp.default_ref) = "default_operation_mode_value";
  option(mvp.ref) = "operation_mode_value";
  option(default_operation_mode_value) = {
    code: "unknown",
    name: "unknown operation mode"
  };
  BillingOperationModeUnknown = 0 [(operation_mode_value) = {
    code: "unknown",
    name: "unknown operation mode"
  }];
  BillingOperationModeRealtime = 1 [(operation_mode_value) = {
    code: "realtime",
    name: "Операция мгновенно подтверждена"
  }];
  BillingOperationModeBankProcess = 2 [(operation_mode_value) = {
    code: "bank_process",
    name: "Операция в обработке у банка"
  }];
}


message BillingOperationTypeGroupRelationRef {
  BillingOperationType operation_type = 1;
  BillingOperationTypeGroup operation_type_group = 2;
  BillingOperationMode operation_mode = 3;
}

extend google.protobuf.EnumValueOptions {
  BillingOperationTypeGroupRelationRef group_relation_value = 100120;
}

extend google.protobuf.EnumOptions {
  BillingOperationTypeGroupRelationRef default_group_relation_value = 100121;
}

enum BillingOperationTypeGroupRelation {
  option(mvp.default_ref) = "default_group_relation_value";
  option(mvp.ref) = "group_relation_value";
  option(default_group_relation_value) = {
    operation_type: BillingOperationTypeUnknown,
  };
  BillingOperationTypeGroupRelationUnknown = 0 [(group_relation_value) = {
    operation_type: BillingOperationTypePayIn,
    operation_type_group: BillingOperationTypeGroupUnknown,
    operation_mode: BillingOperationModeUnknown
  }];
  BillingOperationTypeGroupRelationPayIn = 1 [(group_relation_value) = {
    operation_type: BillingOperationTypePayIn,
    operation_type_group: BillingOperationTypeGroupPayIn,
    operation_mode: BillingOperationModeBankProcess
  }];
  BillingOperationTypeGroupRelationPayOut = 2 [(group_relation_value) = {
    operation_type: BillingOperationTypePayOut,
    operation_type_group: BillingOperationTypeGroupOut,
    operation_mode: BillingOperationModeRealtime
  }];
  BillingOperationTypeGroupRelationRefund = 3 [(group_relation_value) = {
    operation_type: BillingOperationTypeRefund,
    operation_type_group: BillingOperationTypeGroupRefund,
    operation_mode: BillingOperationModeBankProcess
  }];
  BillingOperationTypeGroupRelationInTransfer = 4 [(group_relation_value) = {
    operation_type: BillingOperationTypeInTransfer,
    operation_type_group: BillingOperationTypeGroupIn,
    operation_mode: BillingOperationModeRealtime
  }];
  BillingOperationTypeGroupRelationOutTransfer = 5 [(group_relation_value) = {
    operation_type: BillingOperationTypeOutTransfer,
    operation_type_group: BillingOperationTypeGroupOut,
    operation_mode: BillingOperationModeRealtime
  }];
  BillingOperationTypeGroupRelationPayInCmsOutAcquirer = 6 [(group_relation_value) = {
    operation_type: BillingOperationTypePayInCommissionOutAcquirer,
    operation_type_group: BillingOperationTypeGroupOut,
    operation_mode: BillingOperationModeBankProcess
  }];
  BillingOperationTypeGroupRelationPayInCmsInSystem = 7 [(group_relation_value) = {
    operation_type: BillingOperationTypePayInCommissionInSystem,
    operation_type_group: BillingOperationTypeGroupIn,
    operation_mode: BillingOperationModeBankProcess
  }];
  BillingOperationTypeGroupRelationPayInCmsOutSystem = 8 [(group_relation_value) = {
    operation_type: BillingOperationTypePayInCommissionOutSystem,
    operation_type_group: BillingOperationTypeGroupOut,
    operation_mode: BillingOperationModeBankProcess
  }];
  BillingOperationTypeGroupRelationPayInCmsInMerchant = 9 [(group_relation_value) = {
    operation_type: BillingOperationTypePayInCommissionInMerchant,
    operation_type_group: BillingOperationTypeGroupIn,
    operation_mode: BillingOperationModeBankProcess
  }];
  BillingOperationTypeGroupRelationPayInCmsOutMerchant = 10 [(group_relation_value) = {
    operation_type: BillingOperationTypePayInCommissionOutMerchant,
    operation_type_group: BillingOperationTypeGroupOut,
    operation_mode: BillingOperationModeBankProcess
  }];
  BillingOperationTypeGroupRelationInternalInTransfer = 11 [(group_relation_value) = {
    operation_type: BillingOperationTypeInternalInTransfer,
    operation_type_group: BillingOperationTypeGroupIn,
    operation_mode: BillingOperationModeRealtime
  }];
  BillingOperationTypeGroupRelationInternalOutTransfer = 12 [(group_relation_value) = {
    operation_type: BillingOperationTypeInternalOutTransfer,
    operation_type_group: BillingOperationTypeGroupOut,
    operation_mode: BillingOperationModeRealtime
  }];
  BillingOperationTypeGroupRelationSplitOutTransfer = 13 [(group_relation_value) = {
    operation_type: BillingOperationTypeSplitOutTransfer,
    operation_type_group: BillingOperationTypeGroupOut,
    operation_mode: BillingOperationModeRealtime
  }];
  BillingOperationTypeGroupRelationSplitInTransfer = 14 [(group_relation_value) = {
    operation_type: BillingOperationTypeSplitInTransfer,
    operation_type_group: BillingOperationTypeGroupIn,
    operation_mode: BillingOperationModeRealtime
  }];
  BillingOperationTypeGroupRelationPayOutCmsOutAcquirer = 15 [(group_relation_value) = {
    operation_type: BillingOperationTypePayOutCommissionOutAcquirer,
    operation_type_group: BillingOperationTypeGroupOut,
    operation_mode: BillingOperationModeRealtime
  }];
  BillingOperationTypeGroupRelationPayOutCmsInSystem = 16 [(group_relation_value) = {
    operation_type: BillingOperationTypePayOutCommissionInSystem,
    operation_type_group: BillingOperationTypeGroupIn,
    operation_mode: BillingOperationModeRealtime
  }];
  BillingOperationTypeGroupRelationPayOutCmsOutSystem = 17 [(group_relation_value) = {
    operation_type: BillingOperationTypePayOutCommissionOutSystem,
    operation_type_group: BillingOperationTypeGroupOut,
    operation_mode: BillingOperationModeRealtime
  }];
  BillingOperationTypeGroupRelationPayOutCmsInMerchant = 18 [(group_relation_value) = {
    operation_type: BillingOperationTypePayOutCommissionInMerchant,
    operation_type_group: BillingOperationTypeGroupIn,
    operation_mode: BillingOperationModeRealtime
  }];
  BillingOperationTypeGroupRelationPayOutCmsOutMerchant = 19 [(group_relation_value) = {
    operation_type: BillingOperationTypePayOutCommissionOutMerchant,
    operation_type_group: BillingOperationTypeGroupOut,
    operation_mode: BillingOperationModeRealtime
  }];
  BillingOperationTypeGroupRelationSplitCmsInSystem = 20 [(group_relation_value) = {
    operation_type: BillingOperationTypeSplitCommissionInSystem,
    operation_type_group: BillingOperationTypeGroupIn,
    operation_mode: BillingOperationModeRealtime
  }];
  BillingOperationTypeGroupRelationSplitCmsOutSystem = 21 [(group_relation_value) = {
    operation_type: BillingOperationTypeSplitCommissionOutSystem,
    operation_type_group: BillingOperationTypeGroupOut,
    operation_mode: BillingOperationModeRealtime
  }];
  BillingOperationTypeGroupRelationSplitCmsInMerchant = 22 [(group_relation_value) = {
    operation_type: BillingOperationTypeSplitCommissionInMerchant,
    operation_type_group: BillingOperationTypeGroupIn
    operation_mode: BillingOperationModeRealtime
  }];
  BillingOperationTypeGroupRelationSplitCmsOutMerchant = 23 [(group_relation_value) = {
    operation_type: BillingOperationTypeSplitCommissionOutMerchant,
    operation_type_group: BillingOperationTypeGroupOut
    operation_mode: BillingOperationModeRealtime
  }];
}
