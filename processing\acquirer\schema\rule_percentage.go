package schema

import (
	"github.com/go-playground/validator/v10"

	"git.local/sensitive/innerpb/processing/goerr"
	"git.local/sensitive/processing/acquirer/model"
)

type CreateRulePercentageRequest []struct {
	AcquirerID uint64 `json:"acquirer_id" validate:"required"`
	Percentage int    `json:"percentage"`
}

func (cr *CreateRulePercentageRequest) Validate() error {
	if cr == nil {
		return goerr.ErrParseErrorBody
	}

	for _, v := range *cr {
		validation := validator.New()

		if err := validation.Struct(v); err != nil {
			return err
		}
	}

	return cr.validateAcquirersPercentage()
}

func (cr *CreateRulePercentageRequest) validateAcquirersPercentage() error {
	var sum = 0

	for _, arr := range *cr {
		sum += arr.Percentage
	}

	if sum != Percentage {
		return goerr.ErrPercentage
	}

	return nil
}

func (cr *CreateRulePercentageRequest) ToRulePercentages(ruleID uint64) []*model.RulePercentage {
	rulePercentages := make([]*model.RulePercentage, len(*cr))

	for i, acquirer := range *cr {
		rulePercentages[i] = &model.RulePercentage{
			RuleID:     ruleID,
			AcquirerID: acquirer.AcquirerID,
			Percentage: acquirer.Percentage,
		}
	}

	return rulePercentages
}

func (cr *CreateRulePercentageRequest) RetrieveAcquirerIds() []uint64 {
	acquirerIds := make([]uint64, len(*cr))

	for i, acquirer := range *cr {
		acquirerIds[i] = acquirer.AcquirerID
	}

	return acquirerIds
}
