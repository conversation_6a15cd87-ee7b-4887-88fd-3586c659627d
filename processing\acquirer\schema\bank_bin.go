package schema

import (
	"github.com/go-playground/validator/v10"

	"git.local/sensitive/innerpb/processing/goerr"
	"git.local/sensitive/processing/acquirer/model"
)

type BankBin struct {
	Bin       string `json:"bin" validate:"required"`
	BankId    uint64 `json:"bank_id" validate:"required"`
	IpsId     uint64 `json:"ips_id"`
	CountryId uint64 `json:"country_id"`
}

func (i *BankBin) Validate() error {
	if i == nil {
		return goerr.ErrParseErrorBody
	}

	validation := validator.New()

	return validation.Struct(i)
}

func (i *BankBin) ToModel() model.BankBin {
	return model.BankBin{
		Bin:       i.Bin,
		BankId:    i.BankId,
		IpsId:     i.IpsId,
		CountryId: i.CountryId,
	}
}
