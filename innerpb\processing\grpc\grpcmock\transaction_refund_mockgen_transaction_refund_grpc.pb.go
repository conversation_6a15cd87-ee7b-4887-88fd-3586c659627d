// Code generated by MockGen. DO NOT EDIT.
// Source: transaction_refund_grpc.pb.go

// Package grpcmock is a generated GoMock package.
package grpcmock

import (
	context "context"
	reflect "reflect"

	grpc "git.local/sensitive/innerpb/processing/grpc"
	gomock "github.com/golang/mock/gomock"
	grpc0 "google.golang.org/grpc"
)

// MockRefundClient is a mock of RefundClient interface.
type MockRefundClient struct {
	ctrl     *gomock.Controller
	recorder *MockRefundClientMockRecorder
}

// MockRefundClientMockRecorder is the mock recorder for MockRefundClient.
type MockRefundClientMockRecorder struct {
	mock *MockRefundClient
}

// NewMockRefundClient creates a new mock instance.
func NewMockRefundClient(ctrl *gomock.Controller) *MockRefundClient {
	mock := &MockRefundClient{ctrl: ctrl}
	mock.recorder = &MockRefundClientMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockRefundClient) EXPECT() *MockRefundClientMockRecorder {
	return m.recorder
}

// GetByTransactionIDV1 mocks base method.
func (m *MockRefundClient) GetByTransactionIDV1(ctx context.Context, in *grpc.TransactionRequestDataV1, opts ...grpc0.CallOption) (*grpc.TransactionResponseDataV1, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetByTransactionIDV1", varargs...)
	ret0, _ := ret[0].(*grpc.TransactionResponseDataV1)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetByTransactionIDV1 indicates an expected call of GetByTransactionIDV1.
func (mr *MockRefundClientMockRecorder) GetByTransactionIDV1(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetByTransactionIDV1", reflect.TypeOf((*MockRefundClient)(nil).GetByTransactionIDV1), varargs...)
}

// MockRefundServer is a mock of RefundServer interface.
type MockRefundServer struct {
	ctrl     *gomock.Controller
	recorder *MockRefundServerMockRecorder
}

// MockRefundServerMockRecorder is the mock recorder for MockRefundServer.
type MockRefundServerMockRecorder struct {
	mock *MockRefundServer
}

// NewMockRefundServer creates a new mock instance.
func NewMockRefundServer(ctrl *gomock.Controller) *MockRefundServer {
	mock := &MockRefundServer{ctrl: ctrl}
	mock.recorder = &MockRefundServerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockRefundServer) EXPECT() *MockRefundServerMockRecorder {
	return m.recorder
}

// GetByTransactionIDV1 mocks base method.
func (m *MockRefundServer) GetByTransactionIDV1(arg0 context.Context, arg1 *grpc.TransactionRequestDataV1) (*grpc.TransactionResponseDataV1, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetByTransactionIDV1", arg0, arg1)
	ret0, _ := ret[0].(*grpc.TransactionResponseDataV1)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetByTransactionIDV1 indicates an expected call of GetByTransactionIDV1.
func (mr *MockRefundServerMockRecorder) GetByTransactionIDV1(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetByTransactionIDV1", reflect.TypeOf((*MockRefundServer)(nil).GetByTransactionIDV1), arg0, arg1)
}

// mustEmbedUnimplementedRefundServer mocks base method.
func (m *MockRefundServer) mustEmbedUnimplementedRefundServer() {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "mustEmbedUnimplementedRefundServer")
}

// mustEmbedUnimplementedRefundServer indicates an expected call of mustEmbedUnimplementedRefundServer.
func (mr *MockRefundServerMockRecorder) mustEmbedUnimplementedRefundServer() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "mustEmbedUnimplementedRefundServer", reflect.TypeOf((*MockRefundServer)(nil).mustEmbedUnimplementedRefundServer))
}

// MockUnsafeRefundServer is a mock of UnsafeRefundServer interface.
type MockUnsafeRefundServer struct {
	ctrl     *gomock.Controller
	recorder *MockUnsafeRefundServerMockRecorder
}

// MockUnsafeRefundServerMockRecorder is the mock recorder for MockUnsafeRefundServer.
type MockUnsafeRefundServerMockRecorder struct {
	mock *MockUnsafeRefundServer
}

// NewMockUnsafeRefundServer creates a new mock instance.
func NewMockUnsafeRefundServer(ctrl *gomock.Controller) *MockUnsafeRefundServer {
	mock := &MockUnsafeRefundServer{ctrl: ctrl}
	mock.recorder = &MockUnsafeRefundServerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockUnsafeRefundServer) EXPECT() *MockUnsafeRefundServerMockRecorder {
	return m.recorder
}

// mustEmbedUnimplementedRefundServer mocks base method.
func (m *MockUnsafeRefundServer) mustEmbedUnimplementedRefundServer() {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "mustEmbedUnimplementedRefundServer")
}

// mustEmbedUnimplementedRefundServer indicates an expected call of mustEmbedUnimplementedRefundServer.
func (mr *MockUnsafeRefundServerMockRecorder) mustEmbedUnimplementedRefundServer() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "mustEmbedUnimplementedRefundServer", reflect.TypeOf((*MockUnsafeRefundServer)(nil).mustEmbedUnimplementedRefundServer))
}
