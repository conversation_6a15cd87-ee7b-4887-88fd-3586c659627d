package database

import (
	"context"
	"errors"

	"gorm.io/gorm"

	"git.local/sensitive/innerpb/processing/goerr"
	"git.local/sensitive/pkg/middlewares"
	"git.local/sensitive/processing/acquirer/model"
	"git.local/sensitive/sdk/dog"
)

type IssuerDB struct {
	db *gorm.DB
}

func NewIssuerDB(db *gorm.DB) Issuer {
	return &IssuerDB{
		db: db,
	}
}

func (r *IssuerDB) GetAll(ctx context.Context, pagination *middlewares.PaginationInfo) (_ []*model.BankBins, err error) {
	ctx, span := dog.CreateSpan(ctx, "IssuerDB_GetAll")
	defer span.End()

	result := make([]*model.BankBins, 0)

	request := r.db.WithContext(ctx).
		Model(&model.BankBins{})

	if pagination.Pagination {
		request = request.Offset((pagination.Page - 1) * pagination.PerPage).Limit(pagination.PerPage)
	}

	if err := request.Preload("Bank").Find(&result).Error; err != nil {
		return nil, err
	}

	if request.RowsAffected == 0 || len(result) == 0 {
		return nil, goerr.ErrEmitentNotFound.WithCtx(ctx)
	}

	return result, nil
}

func (r *IssuerDB) GetByID(ctx context.Context, id uint64) (_ model.Bank, err error) {
	ctx, span := dog.CreateSpan(ctx, "IssuerDB_GetByID")
	defer span.End()

	issuerBank := new(model.Bank)

	if err := r.db.WithContext(ctx).Where("id = ?", id).First(issuerBank).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return model.Bank{}, goerr.ErrBankNotFound
		}

		return model.Bank{}, err
	}

	return *issuerBank, nil
}
