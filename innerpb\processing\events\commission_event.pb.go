// Code generated by protoc-gen-go-mvp. DO NOT EDIT.
// versions:
// - protoc-gen-go-mvp v2.0.1
// created by mvp authors

package events

import (
	context "context"
	errors "errors"
	fmt "fmt"
	pb "git.local/sensitive/mvp/pb"
	watermill "github.com/ThreeDotsLabs/watermill"
	nats "github.com/ThreeDotsLabs/watermill-nats/v2/pkg/nats"
	message "github.com/ThreeDotsLabs/watermill/message"
	uuid "github.com/google/uuid"
	nats_go "github.com/nats-io/nats.go"
	otel "go.opentelemetry.io/otel"
	attribute "go.opentelemetry.io/otel/attribute"
	propagation "go.opentelemetry.io/otel/propagation"
	zap "go.uber.org/zap"
	protojson "google.golang.org/protobuf/encoding/protojson"
	anypb "google.golang.org/protobuf/types/known/anypb"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	strings "strings"
	time "time"
)

const subjectNameCreateCommission = "commission.createcommission"

type eventCreateCommission struct {
	jsContext         nats_go.JetStreamContext
	natsConn          *nats_go.Conn
	natsMessageRouter *message.Router
}

func newCreateCommission(
	jsContext nats_go.JetStreamContext,
	conn *nats_go.Conn,
	router *message.Router,
) *eventCreateCommission {
	return &eventCreateCommission{
		jsContext:         jsContext,
		natsConn:          conn,
		natsMessageRouter: router,
	}
}

var singleCreateCommission func(ctx context.Context) (<-chan *CreateCommission, error)

func NewCreateCommissionSubscriber(
	js nats_go.JetStreamContext,
	conn *nats_go.Conn,
	router *message.Router,
) func(ctx context.Context) (<-chan *CreateCommission, error) {
	if singleCreateCommission == nil {
		singleCreateCommission = newCreateCommission(js, conn, router).Subscribe
	}

	return singleCreateCommission
}

func NewCreateCommissionPublisher(
	js nats_go.JetStreamContext,
) func(ctx context.Context, src *CreateCommission) error {
	return newCreateCommission(js, nil, nil).Publish
}

func (s *eventCreateCommission) Publish(ctx context.Context, src *CreateCommission) error {
	var err error
	eventId := uuid.New().String()

	ctx, span := otel.Tracer("otel_trace").Start(ctx, "CreateCommission.Publish")
	defer span.End()
	defer func() {
		span.RecordError(err)
		span.SetAttributes(
			attribute.Bool("error", err != nil),
			attribute.String("nats_uuid", eventId),
		)
	}()

	m, err := anypb.New(src)
	if err != nil {
		return err
	}

	data := pb.EventInfo{
		EventId:   &eventId,
		EventTime: timestamppb.Now(),
		Data:      m,
	}
	marshal, err := protojson.Marshal(&data)
	if err != nil {
		return err
	}

	header := make(nats_go.Header)
	otel.GetTextMapPropagator().Inject(ctx, propagation.HeaderCarrier(header))

	_, err = s.jsContext.PublishMsg(&nats_go.Msg{
		Subject: subjectNameCreateCommission,
		Data:    marshal,
		Header:  header,
	})

	return err
}

func (s *eventCreateCommission) Subscribe(ctx context.Context) (<-chan *CreateCommission, error) {
	ch := make(chan *CreateCommission)

	streamCfg := newCreateCommissionStreamConfig()
	consumerCfg := newCreateCommissionConsumerConfig()
	watermillCfg := newCreateCommissionWatermillConfigs()

	fun := func(msg *message.Message) error {
		data := new(pb.EventInfo)
		resCreateCommission := new(CreateCommission)
		if err := protojson.Unmarshal(msg.Payload, data); err != nil {
			return err
		}

		if err := data.GetData().UnmarshalTo(resCreateCommission); err != nil {
			return err
		}

		ch <- resCreateCommission

		return nil
	}

	err := s.createConsumerCreateCommission(streamCfg, consumerCfg)
	if err != nil {
		return nil, err
	}
	subscriber, err := nats.NewSubscriberWithNatsConn(
		s.natsConn,
		*watermillCfg,
		newZapLoggerAdapterCreateCommission(zap.L()),
	)
	if err != nil {
		return nil, fmt.Errorf("err watermillNats.NewSubscriberWithNatsConn: %w", err)
	}
	go func() {
		select {
		case <-ctx.Done():
			if err := subscriber.Close(); err != nil {
				zap.L().Error("subscriber.Close", zap.Error(err))
			}
		}
	}()

	s.natsMessageRouter.AddNoPublisherHandler(
		consumerCfg.Name,
		subjectNameCreateCommission,
		subscriber,
		fun,
	)

	return ch, nil
}

func newCreateCommissionStreamConfig() *nats_go.StreamConfig {
	return &nats_go.StreamConfig{
		Name:         strings.ReplaceAll(subjectNameCreateCommission, ".", "_"),
		Subjects:     []string{subjectNameCreateCommission, fmt.Sprintf("%s.>", subjectNameCreateCommission)},
		Retention:    nats_go.LimitsPolicy,
		MaxConsumers: -1,
		Discard:      nats_go.DiscardOld,
		MaxAge:       time.Hour * 24 * 7,
		Storage:      nats_go.FileStorage,
		NoAck:        false,
		AllowRollup:  true,
	}
}

func newCreateCommissionConsumerConfig() *nats_go.ConsumerConfig {
	defaultAckWait := time.Second * 30

	streamNameWithoutDots := strings.ReplaceAll(subjectNameCreateCommission, ".", "_")

	consumerName := strings.ReplaceAll(fmt.Sprintf("%s_%s",
		"events",
		streamNameWithoutDots), ".", "_")

	return &nats_go.ConsumerConfig{
		Durable:        consumerName,
		Name:           consumerName,
		AckPolicy:      nats_go.AckExplicitPolicy,
		AckWait:        defaultAckWait,
		MaxDeliver:     -1,
		DeliverGroup:   consumerName,
		DeliverSubject: consumerName,
	}
}

func newCreateCommissionWatermillConfigs() *nats.SubscriberSubscriptionConfig {
	defaultAckWait := time.Second * 30

	streamNameWithoutDots := strings.ReplaceAll(subjectNameCreateCommission, ".", "_")

	consumerName := strings.ReplaceAll(fmt.Sprintf("%s_%s",
		"events",
		streamNameWithoutDots), ".", "_")

	return &nats.SubscriberSubscriptionConfig{
		Unmarshaler:    &eventMarshalerCreateCommission{},
		AckWaitTimeout: defaultAckWait,
		CloseTimeout:   defaultAckWait,
		SubjectCalculator: func(queueGroupPrefix, topic string) *nats.SubjectDetail {
			return &nats.SubjectDetail{
				Primary:    subjectNameCreateCommission,
				QueueGroup: consumerName,
			}
		},
		JetStream: nats.JetStreamConfig{
			Disabled:      false,
			AutoProvision: false,
			SubscribeOptions: []nats_go.SubOpt{
				nats_go.ManualAck(),
				nats_go.DeliverAll(),
				nats_go.AckExplicit(),
				nats_go.Bind(streamNameWithoutDots, consumerName),
				nats_go.MaxDeliver(-1),
			},
			TrackMsgId:        true,
			AckAsync:          false,
			DurableCalculator: func(prefix, topic string) string { return consumerName },
		},
	}
}

func (s *eventCreateCommission) createConsumerCreateCommission(streamCfg *nats_go.StreamConfig, consumerCfg *nats_go.ConsumerConfig) error {
	_, err := s.jsContext.StreamInfo(streamCfg.Name)
	if err != nil {
		if errors.Is(err, nats_go.ErrStreamNotFound) {
			_, err = s.jsContext.AddStream(streamCfg)
			if err != nil {
				return fmt.Errorf("err jsContext.AddStream: %w", err)
			}
		} else {
			return fmt.Errorf("can't get NATS JetStream Stream: %w", err)
		}
	}

	_, err = s.jsContext.UpdateStream(streamCfg)
	if err != nil {
		return fmt.Errorf("err jsContext.UpdateStream: %w", err)
	}

	_, err = s.jsContext.ConsumerInfo(streamCfg.Name, streamCfg.Name)
	if err != nil {
		if errors.Is(err, nats_go.ErrConsumerNotFound) {
			_, err = s.jsContext.AddConsumer(streamCfg.Name, consumerCfg)
			if err != nil {
				return fmt.Errorf("err jsContext.AddConsumer: %w", err)
			}
		} else {
			return fmt.Errorf("can't get NATS JetStream Consumer %s: %w", consumerCfg.Name, err)
		}
	}

	_, err = s.jsContext.UpdateConsumer(streamCfg.Name, consumerCfg)
	if err != nil {
		return fmt.Errorf("err jsContext.UpdateConsumer: %w", err)
	}

	return nil
}

type eventMarshalerCreateCommission struct{}

func (*eventMarshalerCreateCommission) Marshal(topic string, msg *message.Message) (*nats_go.Msg, error) {
	natsMsg := nats_go.NewMsg(topic)
	natsMsg.Data = msg.Payload

	return natsMsg, nil
}
func (*eventMarshalerCreateCommission) Unmarshal(msg *nats_go.Msg) (*message.Message, error) {
	pbMsg := &pb.EventInfo{}
	err := protojson.Unmarshal(msg.Data, pbMsg)
	if err != nil {
		return nil, err
	}

	wmMsg := message.NewMessage(pbMsg.GetEventId(), msg.Data)
	wmMsg.Metadata = map[string]string{}

	return wmMsg, nil
}

type zapLoggerAdapterCreateCommission struct {
	logger *zap.Logger
}

func newZapLoggerAdapterCreateCommission(logger *zap.Logger) *zapLoggerAdapterCreateCommission {
	return &zapLoggerAdapterCreateCommission{logger: logger}
}

func (z zapLoggerAdapterCreateCommission) Error(msg string, err error, fields watermill.LogFields) {
	z.logger.Error(msg, append(z.toZapFields(fields), zap.Error(err))...)
}

func (z zapLoggerAdapterCreateCommission) Info(msg string, fields watermill.LogFields) {
	z.logger.Info(msg, z.toZapFields(fields)...)
}

func (z zapLoggerAdapterCreateCommission) Debug(msg string, fields watermill.LogFields) {
	z.logger.Debug(msg, z.toZapFields(fields)...)
}

func (z zapLoggerAdapterCreateCommission) Trace(msg string, fields watermill.LogFields) {
	z.logger.Debug(msg, z.toZapFields(fields)...)
}

func (z zapLoggerAdapterCreateCommission) With(fields watermill.LogFields) watermill.LoggerAdapter {
	newLogger := z.logger.With(z.toZapFields(fields)...)
	return &zapLoggerAdapterCreateCommission{logger: newLogger}
}

func (z zapLoggerAdapterCreateCommission) toZapFields(fields watermill.LogFields) []zap.Field {
	zapFields := make([]zap.Field, 0, len(fields))
	for k, v := range fields {
		zapFields = append(zapFields, zap.Any(k, v))
	}

	return zapFields
}

const subjectNameCalculateAndSaveCommission = "commission.calculateandsavecommission"

type eventCalculateAndSaveCommission struct {
	jsContext         nats_go.JetStreamContext
	natsConn          *nats_go.Conn
	natsMessageRouter *message.Router
}

func newCalculateAndSaveCommission(
	jsContext nats_go.JetStreamContext,
	conn *nats_go.Conn,
	router *message.Router,
) *eventCalculateAndSaveCommission {
	return &eventCalculateAndSaveCommission{
		jsContext:         jsContext,
		natsConn:          conn,
		natsMessageRouter: router,
	}
}

var singleCalculateAndSaveCommission func(ctx context.Context) (<-chan *CalculateAndSaveCommission, error)

func NewCalculateAndSaveCommissionSubscriber(
	js nats_go.JetStreamContext,
	conn *nats_go.Conn,
	router *message.Router,
) func(ctx context.Context) (<-chan *CalculateAndSaveCommission, error) {
	if singleCalculateAndSaveCommission == nil {
		singleCalculateAndSaveCommission = newCalculateAndSaveCommission(js, conn, router).Subscribe
	}

	return singleCalculateAndSaveCommission
}

func NewCalculateAndSaveCommissionPublisher(
	js nats_go.JetStreamContext,
) func(ctx context.Context, src *CalculateAndSaveCommission) error {
	return newCalculateAndSaveCommission(js, nil, nil).Publish
}

func (s *eventCalculateAndSaveCommission) Publish(ctx context.Context, src *CalculateAndSaveCommission) error {
	var err error
	eventId := uuid.New().String()

	ctx, span := otel.Tracer("otel_trace").Start(ctx, "CalculateAndSaveCommission.Publish")
	defer span.End()
	defer func() {
		span.RecordError(err)
		span.SetAttributes(
			attribute.Bool("error", err != nil),
			attribute.String("nats_uuid", eventId),
		)
	}()

	m, err := anypb.New(src)
	if err != nil {
		return err
	}

	data := pb.EventInfo{
		EventId:   &eventId,
		EventTime: timestamppb.Now(),
		Data:      m,
	}
	marshal, err := protojson.Marshal(&data)
	if err != nil {
		return err
	}

	header := make(nats_go.Header)
	otel.GetTextMapPropagator().Inject(ctx, propagation.HeaderCarrier(header))

	_, err = s.jsContext.PublishMsg(&nats_go.Msg{
		Subject: subjectNameCalculateAndSaveCommission,
		Data:    marshal,
		Header:  header,
	})

	return err
}

func (s *eventCalculateAndSaveCommission) Subscribe(ctx context.Context) (<-chan *CalculateAndSaveCommission, error) {
	ch := make(chan *CalculateAndSaveCommission)

	streamCfg := newCalculateAndSaveCommissionStreamConfig()
	consumerCfg := newCalculateAndSaveCommissionConsumerConfig()
	watermillCfg := newCalculateAndSaveCommissionWatermillConfigs()

	fun := func(msg *message.Message) error {
		data := new(pb.EventInfo)
		resCalculateAndSaveCommission := new(CalculateAndSaveCommission)
		if err := protojson.Unmarshal(msg.Payload, data); err != nil {
			return err
		}

		if err := data.GetData().UnmarshalTo(resCalculateAndSaveCommission); err != nil {
			return err
		}

		ch <- resCalculateAndSaveCommission

		return nil
	}

	err := s.createConsumerCalculateAndSaveCommission(streamCfg, consumerCfg)
	if err != nil {
		return nil, err
	}
	subscriber, err := nats.NewSubscriberWithNatsConn(
		s.natsConn,
		*watermillCfg,
		newZapLoggerAdapterCalculateAndSaveCommission(zap.L()),
	)
	if err != nil {
		return nil, fmt.Errorf("err watermillNats.NewSubscriberWithNatsConn: %w", err)
	}
	go func() {
		select {
		case <-ctx.Done():
			if err := subscriber.Close(); err != nil {
				zap.L().Error("subscriber.Close", zap.Error(err))
			}
		}
	}()

	s.natsMessageRouter.AddNoPublisherHandler(
		consumerCfg.Name,
		subjectNameCalculateAndSaveCommission,
		subscriber,
		fun,
	)

	return ch, nil
}

func newCalculateAndSaveCommissionStreamConfig() *nats_go.StreamConfig {
	return &nats_go.StreamConfig{
		Name:         strings.ReplaceAll(subjectNameCalculateAndSaveCommission, ".", "_"),
		Subjects:     []string{subjectNameCalculateAndSaveCommission, fmt.Sprintf("%s.>", subjectNameCalculateAndSaveCommission)},
		Retention:    nats_go.LimitsPolicy,
		MaxConsumers: -1,
		Discard:      nats_go.DiscardOld,
		MaxAge:       time.Hour * 24 * 7,
		Storage:      nats_go.FileStorage,
		NoAck:        false,
		AllowRollup:  true,
	}
}

func newCalculateAndSaveCommissionConsumerConfig() *nats_go.ConsumerConfig {
	defaultAckWait := time.Second * 30

	streamNameWithoutDots := strings.ReplaceAll(subjectNameCalculateAndSaveCommission, ".", "_")

	consumerName := strings.ReplaceAll(fmt.Sprintf("%s_%s",
		"events",
		streamNameWithoutDots), ".", "_")

	return &nats_go.ConsumerConfig{
		Durable:        consumerName,
		Name:           consumerName,
		AckPolicy:      nats_go.AckExplicitPolicy,
		AckWait:        defaultAckWait,
		MaxDeliver:     -1,
		DeliverGroup:   consumerName,
		DeliverSubject: consumerName,
	}
}

func newCalculateAndSaveCommissionWatermillConfigs() *nats.SubscriberSubscriptionConfig {
	defaultAckWait := time.Second * 30

	streamNameWithoutDots := strings.ReplaceAll(subjectNameCalculateAndSaveCommission, ".", "_")

	consumerName := strings.ReplaceAll(fmt.Sprintf("%s_%s",
		"events",
		streamNameWithoutDots), ".", "_")

	return &nats.SubscriberSubscriptionConfig{
		Unmarshaler:    &eventMarshalerCalculateAndSaveCommission{},
		AckWaitTimeout: defaultAckWait,
		CloseTimeout:   defaultAckWait,
		SubjectCalculator: func(queueGroupPrefix, topic string) *nats.SubjectDetail {
			return &nats.SubjectDetail{
				Primary:    subjectNameCalculateAndSaveCommission,
				QueueGroup: consumerName,
			}
		},
		JetStream: nats.JetStreamConfig{
			Disabled:      false,
			AutoProvision: false,
			SubscribeOptions: []nats_go.SubOpt{
				nats_go.ManualAck(),
				nats_go.DeliverAll(),
				nats_go.AckExplicit(),
				nats_go.Bind(streamNameWithoutDots, consumerName),
				nats_go.MaxDeliver(-1),
			},
			TrackMsgId:        true,
			AckAsync:          false,
			DurableCalculator: func(prefix, topic string) string { return consumerName },
		},
	}
}

func (s *eventCalculateAndSaveCommission) createConsumerCalculateAndSaveCommission(streamCfg *nats_go.StreamConfig, consumerCfg *nats_go.ConsumerConfig) error {
	_, err := s.jsContext.StreamInfo(streamCfg.Name)
	if err != nil {
		if errors.Is(err, nats_go.ErrStreamNotFound) {
			_, err = s.jsContext.AddStream(streamCfg)
			if err != nil {
				return fmt.Errorf("err jsContext.AddStream: %w", err)
			}
		} else {
			return fmt.Errorf("can't get NATS JetStream Stream: %w", err)
		}
	}

	_, err = s.jsContext.UpdateStream(streamCfg)
	if err != nil {
		return fmt.Errorf("err jsContext.UpdateStream: %w", err)
	}

	_, err = s.jsContext.ConsumerInfo(streamCfg.Name, streamCfg.Name)
	if err != nil {
		if errors.Is(err, nats_go.ErrConsumerNotFound) {
			_, err = s.jsContext.AddConsumer(streamCfg.Name, consumerCfg)
			if err != nil {
				return fmt.Errorf("err jsContext.AddConsumer: %w", err)
			}
		} else {
			return fmt.Errorf("can't get NATS JetStream Consumer %s: %w", consumerCfg.Name, err)
		}
	}

	_, err = s.jsContext.UpdateConsumer(streamCfg.Name, consumerCfg)
	if err != nil {
		return fmt.Errorf("err jsContext.UpdateConsumer: %w", err)
	}

	return nil
}

type eventMarshalerCalculateAndSaveCommission struct{}

func (*eventMarshalerCalculateAndSaveCommission) Marshal(topic string, msg *message.Message) (*nats_go.Msg, error) {
	natsMsg := nats_go.NewMsg(topic)
	natsMsg.Data = msg.Payload

	return natsMsg, nil
}
func (*eventMarshalerCalculateAndSaveCommission) Unmarshal(msg *nats_go.Msg) (*message.Message, error) {
	pbMsg := &pb.EventInfo{}
	err := protojson.Unmarshal(msg.Data, pbMsg)
	if err != nil {
		return nil, err
	}

	wmMsg := message.NewMessage(pbMsg.GetEventId(), msg.Data)
	wmMsg.Metadata = map[string]string{}

	return wmMsg, nil
}

type zapLoggerAdapterCalculateAndSaveCommission struct {
	logger *zap.Logger
}

func newZapLoggerAdapterCalculateAndSaveCommission(logger *zap.Logger) *zapLoggerAdapterCalculateAndSaveCommission {
	return &zapLoggerAdapterCalculateAndSaveCommission{logger: logger}
}

func (z zapLoggerAdapterCalculateAndSaveCommission) Error(msg string, err error, fields watermill.LogFields) {
	z.logger.Error(msg, append(z.toZapFields(fields), zap.Error(err))...)
}

func (z zapLoggerAdapterCalculateAndSaveCommission) Info(msg string, fields watermill.LogFields) {
	z.logger.Info(msg, z.toZapFields(fields)...)
}

func (z zapLoggerAdapterCalculateAndSaveCommission) Debug(msg string, fields watermill.LogFields) {
	z.logger.Debug(msg, z.toZapFields(fields)...)
}

func (z zapLoggerAdapterCalculateAndSaveCommission) Trace(msg string, fields watermill.LogFields) {
	z.logger.Debug(msg, z.toZapFields(fields)...)
}

func (z zapLoggerAdapterCalculateAndSaveCommission) With(fields watermill.LogFields) watermill.LoggerAdapter {
	newLogger := z.logger.With(z.toZapFields(fields)...)
	return &zapLoggerAdapterCalculateAndSaveCommission{logger: newLogger}
}

func (z zapLoggerAdapterCalculateAndSaveCommission) toZapFields(fields watermill.LogFields) []zap.Field {
	zapFields := make([]zap.Field, 0, len(fields))
	for k, v := range fields {
		zapFields = append(zapFields, zap.Any(k, v))
	}

	return zapFields
}
