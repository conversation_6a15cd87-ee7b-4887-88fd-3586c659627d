// Code generated by MockGen. DO NOT EDIT.
// Source: view_crafter.gin.pb.go

// Package grpcmock is a generated GoMock package.
package grpcmock

import (
	reflect "reflect"

	gin "github.com/gin-gonic/gin"
	gomock "github.com/golang/mock/gomock"
)

// MockGinViewCrafterServer is a mock of GinViewCrafterServer interface.
type MockGinViewCrafterServer struct {
	ctrl     *gomock.Controller
	recorder *MockGinViewCrafterServerMockRecorder
}

// MockGinViewCrafterServerMockRecorder is the mock recorder for MockGinViewCrafterServer.
type MockGinViewCrafterServerMockRecorder struct {
	mock *MockGinViewCrafterServer
}

// NewMockGinViewCrafterServer creates a new mock instance.
func NewMockGinViewCrafterServer(ctrl *gomock.Controller) *MockGinViewCrafterServer {
	mock := &MockGinViewCrafterServer{ctrl: ctrl}
	mock.recorder = &MockGinViewCrafterServerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockGinViewCrafterServer) EXPECT() *MockGinViewCrafterServerMockRecorder {
	return m.recorder
}

// GetProjectFormInfoV1 mocks base method.
func (m *MockGinViewCrafterServer) GetProjectFormInfoV1(c *gin.Context) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetProjectFormInfoV1", c)
	ret0, _ := ret[0].(error)
	return ret0
}

// GetProjectFormInfoV1 indicates an expected call of GetProjectFormInfoV1.
func (mr *MockGinViewCrafterServerMockRecorder) GetProjectFormInfoV1(c interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetProjectFormInfoV1", reflect.TypeOf((*MockGinViewCrafterServer)(nil).GetProjectFormInfoV1), c)
}
