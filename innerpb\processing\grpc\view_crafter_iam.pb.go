// Code generated by protoc-gen-go-mvp. DO NOT EDIT.
// versions:
// - protoc-gen-go-mvp v2.0.1
// created by mvp authors

package grpc

import (
	context "context"
	grpc "google.golang.org/grpc"
	metadata "google.golang.org/grpc/metadata"
)

func NewIamViewCrafterServer(
	srv ViewCrafterServer,
) ViewCrafterServer {
	return &iamViewCrafterServer{
		srv: srv,
	}
}

var _ ViewCrafterServer = (*iamViewCrafterServer)(nil)

type iamViewCrafterServer struct {
	UnimplementedViewCrafterServer

	srv ViewCrafterServer
}

func (s *iamViewCrafterServer) GetProjectFormInfoV1(
	ctx context.Context,
	req *GetProjectFormInfoReqV1,
) (
	*GetProjectFormInfoResV1,
	error,
) {
	return s.srv.GetProjectFormInfoV1(ctx, req)
}

func NewIamViewCrafterClient(
	client ViewCrafterClient,
) ViewCrafterClient {
	return &iamViewCrafterClient{
		client: client,
	}
}

type iamViewCrafterClient struct {
	client ViewCrafterClient
}

func (s *iamViewCrafterClient) GetProjectFormInfoV1(
	ctx context.Context,
	req *GetProjectFormInfoReqV1,
	opts ...grpc.CallOption,
) (
	*GetProjectFormInfoResV1,
	error,
) {
	md, _ := metadata.FromIncomingContext(ctx)
	return s.client.GetProjectFormInfoV1(metadata.NewOutgoingContext(ctx, md), req)
}
