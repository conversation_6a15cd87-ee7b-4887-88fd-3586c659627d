package model

import "git.local/sensitive/innerpb/processing/goerr"

const (
	BaseRuleWeight = 1
)

const ruleTableName = "acquirer.rules"

type Rule struct {
	TimestampMixin
	ID                uint64          `gorm:"id" json:"id"`
	ProjectID         uint64          `gorm:"project_id" json:"project_id"`
	TransactionTypeID uint64          `gorm:"transaction_type_id" json:"transaction_type_id"`
	IpsID             *uint64         `gorm:"ips_id" json:"ips_id"`
	IssuerID          *uint64         `gorm:"issuer_id" json:"issuer_id,"`
	CountryID         *uint64         `gorm:"country_id" json:"country_id"`
	AmountFrom        *float64        `gorm:"amount_from" json:"amount_from,omitempty"`
	AmountTo          *float64        `gorm:"amount_to" json:"amount_to,omitempty"`
	Weight            uint8           `gorm:"weight" json:"weight"`
	IsActive          bool            `gorm:"is_active" json:"is_active"`
	IsBase            bool            `gorm:"is_base" json:"is_base"`
	RulePercentages   RulePercentages `gorm:"foreignKey:rule_id" json:"rule_percentages"`
	Issuer            Bank            `gorm:"foreignKey:IssuerID" json:"issuer"`
}

func (r Rule) TableName() string {
	return ruleTableName
}

type Rules []*Rule

func (rs Rules) TableName() string {
	return ruleTableName
}

// FilterByActiveAcquirers - использует айди-эквайеров для фильтрации правил и процентажей в них
func (rs Rules) FilterByActiveAcquirers(acquirerIDs []uint64) (filteredRules Rules) {
	idMap := make(map[uint64]bool)
	rulesMap := make(map[uint64]bool)

	for _, id := range acquirerIDs {
		idMap[id] = true
	}

	for _, rule := range rs {
		for _, percentage := range rule.RulePercentages {
			if idMap[percentage.AcquirerID] && !rulesMap[rule.ID] && percentage.Percentage > 0 {
				filteredRules = append(filteredRules, &Rule{
					TimestampMixin:    rule.TimestampMixin,
					ID:                rule.ID,
					ProjectID:         rule.ProjectID,
					TransactionTypeID: rule.TransactionTypeID,
					IpsID:             rule.IpsID,
					IssuerID:          rule.IssuerID,
					CountryID:         rule.CountryID,
					AmountFrom:        rule.AmountFrom,
					AmountTo:          rule.AmountTo,
					Weight:            rule.Weight,
					IsActive:          rule.IsActive,
					IsBase:            rule.IsBase,
					RulePercentages:   rule.RulePercentages.FilterByAcquirerIDs(acquirerIDs),
					Issuer:            rule.Issuer,
				})
				rulesMap[rule.ID] = true
			}
		}
	}

	return filteredRules
}

func (rs Rules) GetMatchingRule(issuerID *uint64, countryID *uint64, amount float64, ipsId *uint64) (*Rule, error) {
	for _, rule := range rs {
		if (rule.IssuerID == nil || *rule.IssuerID == *issuerID) &&
			(rule.IpsID == nil || *rule.IpsID == *ipsId) &&
			(rule.CountryID == nil || *rule.CountryID == *countryID) &&
			(rule.AmountFrom == nil || (*rule.AmountFrom <= amount)) &&
			(rule.AmountTo == nil || (*rule.AmountTo >= amount)) {
			return rule, nil
		}
	}

	return nil, goerr.ErrAcquirerPercentageNotFound
}
