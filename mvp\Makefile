.SILENT: proto-generate
proto-generate: proto/*.proto
	echo ' \t' $@ start; \
	for file in $^ ; do \
		echo ' \t\t' building $${file}; \
		protoc -I./ \
			--go_out=. \
			--go_opt=module=git.local/sensitive/mvp \
			--go-grpc_out=. \
			--go-grpc_opt=module=git.local/sensitive/mvp \
			 $${file}; \
	done; \
	echo ' \t' $@ end; \
	echo

.SILENT: build-generators
build-generators: proto-generate
	echo ' \t'$@ start; \
	echo ' \t\t build protoc-gen-go-mvp start'; \
	go build  -o  ./bin/protoc-gen-go-mvp ./generators/protoc-gen-go-mvp; \
	echo ' \t\t build protoc-gen-go-mvp end'; \
	echo ' \t'$@ end; \
	echo

.SILENT: generate
generate: proto-generate build-generators