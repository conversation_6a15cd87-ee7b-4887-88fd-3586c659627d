package service

import (
	"context"
	"errors"
	"testing"

	"github.com/golang/mock/gomock"
	"github.com/stretchr/testify/require"

	"git.local/sensitive/innerpb/processing/goerr"
	"git.local/sensitive/processing/acquirer/model"
	"git.local/sensitive/processing/acquirer/repository/database/databasemocks"
)

func TestActivate(t *testing.T) {
	type getByIDOp struct {
		input     uint64
		output    *model.Rule
		outputErr error
	}

	type activateOp struct {
		isCalled  bool
		input     uint64
		outputErr error
	}

	tests := []struct {
		name     string
		req      uint64
		wantErr  error
		getByID  getByIDOp
		activate activateOp
	}{
		{
			name:    "error when getting by id",
			req:     123,
			wantErr: errors.New("error activating rule"),
			getByID: getByIDOp{
				input:     123,
				output:    nil,
				outputErr: errors.New("error activating rule"),
			},
		},
		{
			name:    "base rule",
			req:     123,
			wantErr: goerr.ErrBaseRuleActivation,
			getByID: getByIDOp{
				input: 123,
				output: &model.Rule{
					ID:                55,
					ProjectID:         81,
					TransactionTypeID: 5,
					IsBase:            true,
				},
				outputErr: nil,
			},
		},
		{
			name:    "error when activating",
			req:     123,
			wantErr: errors.New("some error when activating"),
			getByID: getByIDOp{
				input: 123,
				output: &model.Rule{
					ID:                55,
					ProjectID:         81,
					TransactionTypeID: 5,
					IsBase:            false,
				},
				outputErr: nil,
			},
			activate: activateOp{
				isCalled:  true,
				input:     123,
				outputErr: errors.New("some error when activating"),
			},
		},
		{
			name:    "success",
			req:     123,
			wantErr: nil,
			getByID: getByIDOp{
				input: 123,
				output: &model.Rule{
					ID:                55,
					ProjectID:         81,
					TransactionTypeID: 5,
					IsBase:            false,
				},
				outputErr: nil,
			},
			activate: activateOp{
				isCalled:  true,
				input:     123,
				outputErr: nil,
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctrl := gomock.NewController(t)
			defer ctrl.Finish()

			ruleDBMock := databasemocks.NewMockRuler(ctrl)
			ruleActivatorDBMock := databasemocks.NewMockRuleActivator(ctrl)

			ruleDBMock.EXPECT().GetByID(
				gomock.Any(),
				tt.getByID.input,
			).Return(tt.getByID.output, tt.getByID.outputErr).Times(1)

			if tt.activate.isCalled {
				ruleActivatorDBMock.EXPECT().Activate(
					gomock.Any(),
					tt.activate.input,
				).Return(tt.activate.outputErr).Times(1)
			}

			ruleActivatorService := NewRuleActivatorService(ruleActivatorDBMock, ruleDBMock)

			err := ruleActivatorService.Activate(context.Background(), tt.req)
			if tt.wantErr != nil {
				require.Error(t, err)
				require.Equal(t, tt.wantErr, err)
			} else {
				require.NoError(t, err)
			}
		})
	}
}

func TestDeactivate(t *testing.T) {
	type getByIDOp struct {
		input     uint64
		output    *model.Rule
		outputErr error
	}

	type deactivateOp struct {
		isCalled  bool
		input     uint64
		outputErr error
	}

	tests := []struct {
		name       string
		req        uint64
		wantErr    error
		getByID    getByIDOp
		deactivate deactivateOp
	}{
		{
			name:    "error when getting by id",
			req:     123,
			wantErr: errors.New("error deactivating rule"),
			getByID: getByIDOp{
				input:     123,
				output:    nil,
				outputErr: errors.New("error deactivating rule"),
			},
		},
		{
			name:    "base rule",
			req:     123,
			wantErr: goerr.ErrBaseRuleDeactivation,
			getByID: getByIDOp{
				input: 123,
				output: &model.Rule{
					ID:                55,
					ProjectID:         81,
					TransactionTypeID: 5,
					IsBase:            true,
				},
				outputErr: nil,
			},
		},
		{
			name:    "error when deactivating",
			req:     123,
			wantErr: errors.New("some error when deactivating"),
			getByID: getByIDOp{
				input: 123,
				output: &model.Rule{
					ID:                55,
					ProjectID:         81,
					TransactionTypeID: 5,
					IsBase:            false,
				},
				outputErr: nil,
			},
			deactivate: deactivateOp{
				isCalled:  true,
				input:     123,
				outputErr: errors.New("some error when deactivating"),
			},
		},
		{
			name:    "success",
			req:     123,
			wantErr: nil,
			getByID: getByIDOp{
				input: 123,
				output: &model.Rule{
					ID:                55,
					ProjectID:         81,
					TransactionTypeID: 5,
					IsBase:            false,
				},
				outputErr: nil,
			},
			deactivate: deactivateOp{
				isCalled:  true,
				input:     123,
				outputErr: nil,
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctrl := gomock.NewController(t)
			defer ctrl.Finish()

			ruleDBMock := databasemocks.NewMockRuler(ctrl)
			ruleActivatorDBMock := databasemocks.NewMockRuleActivator(ctrl)

			ruleDBMock.EXPECT().
				GetByID(gomock.Any(), tt.getByID.input).
				Return(tt.getByID.output, tt.getByID.outputErr).
				Times(1)

			if tt.deactivate.isCalled {
				ruleActivatorDBMock.EXPECT().
					Deactivate(gomock.Any(), tt.deactivate.input).
					Return(tt.deactivate.outputErr).
					Times(1)
			}

			ruleActivatorService := NewRuleActivatorService(ruleActivatorDBMock, ruleDBMock)

			err := ruleActivatorService.Deactivate(context.Background(), tt.req)
			if tt.wantErr != nil {
				require.Error(t, err)
				require.Equal(t, tt.wantErr, err)
			} else {
				require.NoError(t, err)
			}
		})
	}
}
