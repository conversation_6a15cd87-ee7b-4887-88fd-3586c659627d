edition = "2023";

package processing.alatau_city_bank_tokenize.alatau_city_bank_tokenize;

option go_package = "git.local/sensitive/innerpb/processing/grpc";

import "inner/processing/grpc/transaction_status.proto";
import "inner/processing/grpc/integration.proto";
import "mvp/proto/refs.proto";
import "mvp/proto/enum.proto";
import "google/protobuf/descriptor.proto";

message AlatauCityBankResponseCodeTokenizeRef {
  string code = 1;
  string description = 2;
  transaction.transaction_status.EnumTransactionStatus transaction_status = 3;
  integration.integration.IntegrationError integration_error = 4;
}

extend google.protobuf.EnumValueOptions {
  AlatauCityBankResponseCodeTokenizeRef alatau_city_bank_response_code_tokenize_value = 110113;
}

extend google.protobuf.EnumOptions {
  AlatauCityBankResponseCodeTokenizeRef default_alatau_city_bank_response_code_tokenize_value = 110114;
}

enum AlatauCityBankResponseCodeTokenize {
  option(mvp.default_ref) = "default_alatau_city_bank_response_code_tokenize_value";
  option(mvp.ref) = "alatau_city_bank_response_code_tokenize_value";
  option(default_alatau_city_bank_response_code_tokenize_value) = {
    code: "0"
    description: "default"
    transaction_status: TransactionStatusError
    integration_error: None
  };

  TokenizeServiceUnavailable = 0 [(alatau_city_bank_response_code_tokenize_value) = {
    code: "11"
    description: "Сервис временно недоступен, попробуйте позже"
    transaction_status: TransactionStatusFailed
    integration_error: UnavailableAcquirer
  }, (mvp.from_string) = "11"];

  TokenizeIncorrectFieldOrder = 1 [(alatau_city_bank_response_code_tokenize_value) = {
    code: "12"
    description: "Неправильное значение в поле ORDER:"
    transaction_status: TransactionStatusFailed
    integration_error: TransactionDeclinedByAcquirer
  }, (mvp.from_string) = "12"];

  TokenizeIncorrectAmount = 2 [(alatau_city_bank_response_code_tokenize_value) = {
    code: "13"
    description: "Неправильная сумма: "
    transaction_status: TransactionStatusFailed
    integration_error: TransactionDeclinedByAcquirer
  }, (mvp.from_string) = "13"];

  TokenizeIncorrectCurrency = 3 [(alatau_city_bank_response_code_tokenize_value) = {
    code: "14"
    description: "Неправильная валюта:"
    transaction_status: TransactionStatusFailed
    integration_error: TransactionDeclinedByAcquirer
  }, (mvp.from_string) = "14"];

  TokenizeUnavailableMPI = 4 [(alatau_city_bank_response_code_tokenize_value) = {
    code: "15"
    description: "Сервис MPI временно недоступен, попробуйте позже"
    transaction_status: TransactionStatusFailed
    integration_error: UnavailableAcquirer
  }, (mvp.from_string) = "15"];

  TokenizeUnavailableDb = 5 [(alatau_city_bank_response_code_tokenize_value) = {
    code: "16"
    description: "Сервис Db временно недоступен, попробуйте позже"
    transaction_status: TransactionStatusFailed
    integration_error: UnavailableAcquirer
  }, (mvp.from_string) = "16"];

  TokenizeOperationForbidden = 6 [(alatau_city_bank_response_code_tokenize_value) = {
    code: "171"
    description: "Коммерсанту запрещено выполнение операций"
    transaction_status: TransactionStatusFailed
    integration_error: PaymentForbiddenForMerchant
  }, (mvp.from_string) = "171"];

  TokenizeOperationForbiddenByLaw = 7 [(alatau_city_bank_response_code_tokenize_value) = {
    code: "172 "
    description: "Коммерсанту запрещено проведение операций в соответствии с Законом о ПОД/ФТ"
    transaction_status: TransactionStatusFailed
    integration_error: PaymentForbiddenForMerchant
  }, (mvp.from_string) = "172 "];

  TokenizeRequestAlreadyCompleted = 8 [(alatau_city_bank_response_code_tokenize_value) = {
    code: "18"
    description: "Запрос уже выполнялся"
    transaction_status: TransactionStatusFailed
    integration_error: TransactionDeclinedByAcquirer
  }, (mvp.from_string) = "18"];

  TokenizeIncorrectCardExpDate = 9 [(alatau_city_bank_response_code_tokenize_value) = {
    code: "19"
    description: "Неправильная дата дейстия карты (MM/ГГ)"
    transaction_status: TransactionStatusFailed
    integration_error: IncorrectCardExpDate
  }, (mvp.from_string) = "19"];

  TokenizeIncorrectFieldTerminal = 10 [(alatau_city_bank_response_code_tokenize_value) = {
    code: "20"
    description: "Неправильное значение в поле TERMINAL:"
    transaction_status: TransactionStatusFailed
    integration_error: TransactionDeclinedByAcquirer
  }, (mvp.from_string) = "20"];

  TokenizeIncorrectSign = 11 [(alatau_city_bank_response_code_tokenize_value) = {
    code: "21"
    description: "Неправильная подпись!"
    transaction_status: TransactionStatusFailed
    integration_error: TransactionDeclinedByAcquirer
  }, (mvp.from_string) = "21"];

  TokenizeCurrencyNotFound = 12 [(alatau_city_bank_response_code_tokenize_value) = {
    code: "22"
    description: "Не найден курс валюты"
    transaction_status: TransactionStatusFailed
    integration_error: TransactionDeclinedByAcquirer
  }, (mvp.from_string) = "22"];

  TokenizeLimitExceeded = 13 [(alatau_city_bank_response_code_tokenize_value) = {
    code: "23"
    description: "Превышен лимит!"
    transaction_status: TransactionStatusFailed
    integration_error: ExceedsAmountLimit
  }, (mvp.from_string) = "23"];

  TokenizeEmptyField = 14 [(alatau_city_bank_response_code_tokenize_value) = {
    code: "24"
    description: "Не указано значение в поле"
    transaction_status: TransactionStatusFailed
    integration_error: TransactionDeclinedByAcquirer
  }, (mvp.from_string) = "24"];

  TokenizeFieldSizeLessSymbols = 15 [(alatau_city_bank_response_code_tokenize_value) = {
    code: "25"
    description: "Размер значения в поле менее симоволов"
    transaction_status: TransactionStatusFailed
    integration_error: TransactionDeclinedByAcquirer
  }, (mvp.from_string) = "25"];

  TokenizeFieldSizeMoreSymbols = 16 [(alatau_city_bank_response_code_tokenize_value) = {
    code: "26"
    description: "Размер значения в поле больше симоволов"
    transaction_status: TransactionStatusFailed
    integration_error: TransactionDeclinedByAcquirer
  }, (mvp.from_string) = "26"];

  TokenizeInvalidField = 17 [(alatau_city_bank_response_code_tokenize_value) = {
    code: "27"
    description: "Введите валидное значение в поле"
    transaction_status: TransactionStatusFailed
    integration_error: TransactionDeclinedByAcquirer
  }, (mvp.from_string) = "27"];

  TokenizeMPIError3DS = 18 [(alatau_city_bank_response_code_tokenize_value) = {
    code: "28"
    description: "Ошибка MPI при выполнении проверки 3DS:"
    transaction_status: TransactionStatusFailed
    integration_error: ThreeDSAuthFailed
  }, (mvp.from_string) = "28"];

  TokenizeUnacceptableCardType = 19 [(alatau_city_bank_response_code_tokenize_value) = {
    code: "29"
    description: "Недопустимый тип карты"
    transaction_status: TransactionStatusFailed
    integration_error: InvalidCard
  }, (mvp.from_string) = "29"];

  TokenizePaymentNotFound = 20 [(alatau_city_bank_response_code_tokenize_value) = {
    code: "30"
    description: "Счет на оплату не найден"
    transaction_status: TransactionStatusFailed
    integration_error: TransactionDeclinedByAcquirer
  }, (mvp.from_string) = "30"];

  TokenizeClientKeyNotFound = 21 [(alatau_city_bank_response_code_tokenize_value) = {
    code: "31"
    description: "Не передан ключ указанного клиента"
    transaction_status: TransactionStatusFailed
    integration_error: TransactionDeclinedByAcquirer
  }, (mvp.from_string) = "31"];

  TokenizeForbidden = 22 [(alatau_city_bank_response_code_tokenize_value) = {
    code: "32"
    description: "Для терминала запрещена токенизация"
    transaction_status: TransactionStatusFailed
    integration_error: PaymentForbiddenForMerchant
  }, (mvp.from_string) = "32"];

  TokenizeTokenNotFound = 23 [(alatau_city_bank_response_code_tokenize_value) = {
    code: "33"
    description: "Для данного клиента в вашей организации не зарегистрирован токен"
    transaction_status: TransactionStatusFailed
    integration_error: InvalidCard
  }, (mvp.from_string) = "33"];

  TokenizeIncorrectBlockAmount = 24 [(alatau_city_bank_response_code_tokenize_value) = {
    code: "34"
    description: "Неверная сумма блокирования, заявка отменена!"
    transaction_status: TransactionStatusFailed
    integration_error: TransactionDeclinedByAcquirer
  }, (mvp.from_string) = "34"];

  TokenizeUnknownError = 25 [(alatau_city_bank_response_code_tokenize_value) = {
    code: "99"
    description: "Неизвестная ошибка: "
    transaction_status: TransactionStatusHolded
    integration_error: TransactionDeclinedByAcquirer
  }, (mvp.from_string) = "99"];

  TokenizeServiceUnavailableTryLater = 26 [(alatau_city_bank_response_code_tokenize_value) = {
    code: "41"
    description: "Сервис временно недоступен, попробуйте позже"
    transaction_status: TransactionStatusFailed
    integration_error: UnavailableAcquirer
  }, (mvp.from_string) = "41"];

  TokenizeInvalidAmount = 27 [(alatau_city_bank_response_code_tokenize_value) = {
    code: "42"
    description: "Неправильная сумма"
    transaction_status: TransactionStatusFailed
    integration_error: TransactionDeclinedByAcquirer
  }, (mvp.from_string) = "42"];

  TokenizeServiceDbUnavailable = 28 [(alatau_city_bank_response_code_tokenize_value) = {
    code: "43"
    description: "Сервис Db временно недоступен, попробуйте позже "
    transaction_status: TransactionStatusFailed
    integration_error: UnavailableAcquirer
  }, (mvp.from_string) = "43"];

  TokenizeIncorrectFieldMerchant = 29 [(alatau_city_bank_response_code_tokenize_value) = {
    code: "44"
    description: "Неправильное значение в поле MERCHANT"
    transaction_status: TransactionStatusFailed
    integration_error: TransactionDeclinedByAcquirer
  }, (mvp.from_string) = "44"];

  TokenizeMerchantNotFound = 30 [(alatau_city_bank_response_code_tokenize_value) = {
    code: "17"
    description: "Коммерсант не найден"
    transaction_status: TransactionStatusFailed
    integration_error: PaymentForbiddenForMerchant
  }, (mvp.from_string) = "17"];

  TokenizeOrderRequestNotFound = 31 [(alatau_city_bank_response_code_tokenize_value) = {
    code: "45"
    description: "Заявка ORDER не найдена"
    transaction_status: TransactionStatusFailed
    integration_error: TransactionDeclinedByAcquirer
  }, (mvp.from_string) = "45"];

  TokenizeInvalidSign = 32 [(alatau_city_bank_response_code_tokenize_value) = {
    code: "46"
    description: "Неправильная подпись!"
    transaction_status: TransactionStatusFailed
    integration_error: TransactionDeclinedByAcquirer
  }, (mvp.from_string) = "46"];

  TokenizeIncorrectRefundSum = 33 [(alatau_city_bank_response_code_tokenize_value) = {
    code: "47 "
    description: "Сумма возврта '%s' больше чем сумма заказа"
    transaction_status: TransactionStatusFailed
    integration_error: TransactionDeclinedByAcquirer
  }, (mvp.from_string) = "47 "];

  TokenizeIncorrectStatus = 34 [(alatau_city_bank_response_code_tokenize_value) = {
    code: "48"
    description: "Текущий статус заказа не позволяет делать возврат/отмену"
    transaction_status: TransactionStatusFailed
    integration_error: TransactionDeclinedByAcquirer
  }, (mvp.from_string) = "48"];

  TokenizeIncorrectValue = 35 [(alatau_city_bank_response_code_tokenize_value) = {
    code: "50"
    description: "Неправильное значение"
    transaction_status: TransactionStatusFailed
    integration_error: TransactionDeclinedByAcquirer
  }, (mvp.from_string) = "50"];

  TokenizeIncorrectTerminalStatus = 36 [(alatau_city_bank_response_code_tokenize_value) = {
    code: "51"
    description: "Текущий статус терминала не позволяет производить операции"
    transaction_status: TransactionStatusFailed
    integration_error: PaymentForbiddenForMerchant
  }, (mvp.from_string) = "51"];

  TokenizeForbiddenOperation = 37 [(alatau_city_bank_response_code_tokenize_value) = {
    code: "52"
    description: "Операция отмены/возврата через API для терминала запрещена"
    transaction_status: TransactionStatusFailed
    integration_error: PaymentForbiddenForMerchant
  }, (mvp.from_string) = "52"];

  TokenizeDuplicateDescription = 38 [(alatau_city_bank_response_code_tokenize_value) = {
    code: "53"
    description: "Дублирование описания отмены"
    transaction_status: TransactionStatusFailed
    integration_error: TransactionDeclinedByAcquirer
  }, (mvp.from_string) = "53"];

  TokenizeRefundError = 39 [(alatau_city_bank_response_code_tokenize_value) = {
    code: "F"
    description: "Ошибка при обработке возврата"
    transaction_status: TransactionStatusFailed
    integration_error: TransactionDeclinedByAcquirer
  }, (mvp.from_string) = "F"];

  TokenizePayError = 40 [(alatau_city_bank_response_code_tokenize_value) = {
    code: "E"
    description: "Ошибка при оплате"
    transaction_status: TransactionStatusFailed
    integration_error: TransactionDeclinedByAcquirer
  }, (mvp.from_string) = "E"];

  TokenizePaymentExpired = 41 [(alatau_city_bank_response_code_tokenize_value) = {
    code: "c"
    description: "Счет на оплату устарел"
    transaction_status: TransactionStatusFailed
    integration_error: TransactionDeclinedByAcquirer
  }, (mvp.from_string) = "c"];

  TokenizeTransactionHandleError = 43 [(alatau_city_bank_response_code_tokenize_value) = {
    code: "411"
    description: "Ошибка при обработке транзакции"
    integration_error: TransactionDeclinedByAcquirer
    transaction_status: TransactionStatusFailed
  }, (mvp.from_string) = "411"];
}
