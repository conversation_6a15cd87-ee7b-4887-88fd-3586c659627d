package model

import (
	"sort"
)

const (
	acquirerTableName = "commission.acquirer_options"
)

// AcquirerOption Опция для расчёта комиссии
type (
	AcquirerOption struct {
		TimestampMixin `json:"-"`
		ID             uint64 `gorm:"column:id" json:"id"`
		// Идентификатор эквайера (обязательный параметр)
		AcquirerID uint64 `gorm:"column:acquirer_id" json:"acquirer_id"`
		// Идентификатор проекта (обязательный параметр)
		ProjectID uint64 `gorm:"column:project_id" json:"project_id"`
		// Идентификатор мерчанта (обязательный параметр)
		MerchantID uint64 `gorm:"column:merchant_id" json:"merchant_id"`
		// Агрегированная айдишка типа транзакции. Группирование типов по приему и выводу (обязательный параметр)
		AggregatedTypeID AggregatedType `gorm:"aggregated_type_id" json:"aggregated_type_id"`
		// Идентификатор страны (необязательный параметр)
		CountryID *uint64 `gorm:"column:country_id" json:"country_id"`
		// Идентификатор эмитента (необязательный параметр)
		IssuerID *uint64 `gorm:"column:issuer_id" json:"issuer_id"`
		// Идентификатор международной платежной системы (необязательный параметр)
		IpsID *uint64 `gorm:"column:ips_id" json:"ips_id"`
	}

	AcquirerOptionWithCommissions struct {
		AcquirerOption
		AcqCommissions []AcquirerCommission `gorm:"foreignKey:AcquirerOptionID;references:ID" json:"acquirer_commissions"`
	}
)

type AcquirerOptions []AcquirerOption

func (a AcquirerOption) TableName() string {
	return acquirerTableName
}

func (a AcquirerOptionWithCommissions) TableName() string {
	return acquirerTableName
}

func (a AcquirerOptions) FindOption(issuerID, countryID, ipsID *uint64) AcquirerOption {
	var (
		bestOption    = AcquirerOption{}
		defaultOption = AcquirerOption{}
	)
	// sort acquirer options slice by create_at parameter
	sort.Slice(a, func(i, j int) bool {
		return a[i].CreatedAt.Before(a[j].CreatedAt)
	})

	for _, option := range a {
		// set default option
		if option.IssuerID == nil && option.CountryID == nil && option.IpsID == nil {
			defaultOption = option
		}

		//check for matching issuer_id, country_id and ips_id
		var (
			issuerMatches  = issuerID != nil && option.IssuerID != nil && *issuerID == *option.IssuerID
			countryMatches = countryID != nil && option.CountryID != nil && *countryID == *option.CountryID
			ipsMatches     = ipsID != nil && option.IpsID != nil && *ipsID == *option.IpsID
		)

		if (issuerMatches && option.CountryID == nil && option.IpsID == nil) ||
			(countryMatches && option.IssuerID == nil && option.IpsID == nil) ||
			(ipsMatches && option.IssuerID == nil && option.CountryID == nil) {
			bestOption = option
		}
	}

	if bestOption.ID == 0 {
		return defaultOption
	}

	return bestOption
}
