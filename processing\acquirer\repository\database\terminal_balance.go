package database

import (
	"context"
	"errors"

	"gorm.io/gorm"

	"git.local/sensitive/innerpb/processing/goerr"
	"git.local/sensitive/processing/acquirer/model"
	"git.local/sensitive/sdk/dog"
)

type TerminalBalanceDB struct {
	db *gorm.DB
}

func NewTerminalBalanceDB(db *gorm.DB) TerminalBalancer {
	return &TerminalBalanceDB{
		db: db,
	}
}

func (t TerminalBalanceDB) GetTerminals(
	ctx context.Context,
	projectID, transactionTypeID uint64,
) (terminals model.Terminals, err error) {
	ctx, span := dog.CreateSpan(ctx, "TerminalBalanceDB_GetTerminals")
	defer span.End()

	var terminalProjects []model.TerminalProject
	err = t.db.WithContext(ctx).
		Where("project_id = ?", projectID).
		Where("transaction_type_id = ?", transactionTypeID).
		Where("is_active", true).
		Find(&terminalProjects).Error

	if err != nil {
		return nil, goerr.ErrDbUnexpected.WithErr(err).WithCtx(ctx)
	}

	terminalIds := make([]uint64, len(terminalProjects))
	for i, v := range terminalProjects {
		terminalIds[i] = v.ID
	}

	err = t.db.WithContext(ctx).
		Table("acquirer.terminals AS t").
		Joins("LEFT JOIN acquirer.terminal_projects AS tp on tp.terminal_id = t.id").
		Where("tp.id IN (?)", terminalIds).
		Preload("Acquirer").
		Find(&terminals).Error
	if err != nil {
		return nil, goerr.ErrDbUnexpected.WithErr(err).WithCtx(ctx)
	}

	return terminals, nil
}

func (t TerminalBalanceDB) GetExactTerminal(
	ctx context.Context,
	projectID, transactionTypeID, acquirerID uint64,
) (_ model.Terminal, err error) {
	ctx, span := dog.CreateSpan(ctx, "TerminalBalanceDB_GetExactTerminal")
	defer span.End()

	var terminal model.Terminal

	if err = t.db.WithContext(ctx).
		Table("acquirer.terminals as t").
		Where("t.acquirer_id = ?", acquirerID).
		Joins("inner join acquirer.terminal_projects tp on tp.terminal_id = t.id").
		Where("tp.transaction_type_id = ?", transactionTypeID).
		Where("tp.project_id = ?", projectID).
		Where("tp.is_active = ?", true).
		First(&terminal).
		Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return model.Terminal{}, goerr.ErrTerminalNotFound.WithErr(err).WithCtx(ctx)
		}

		return model.Terminal{}, goerr.ErrDbUnexpected.WithErr(err).WithCtx(ctx)
	}

	return terminal, nil
}

func (t TerminalBalanceDB) GetTerminalsByIDs(
	ctx context.Context,
	terminalIDs []uint64,
) ([]model.Terminal, error) {
	var terminals = make([]model.Terminal, 0)

	if err := t.db.WithContext(ctx).
		Where("id IN (?)", terminalIDs).
		Preload("Acquirer").
		Find(&terminals).
		Error; err != nil {
		return nil, goerr.ErrDbUnexpected.WithErr(err).WithCtx(ctx)
	}

	return terminals, nil
}
