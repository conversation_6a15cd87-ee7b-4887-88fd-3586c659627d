// Code generated by protoc-gen-go-mvp. DO NOT EDIT.
// versions:
// - protoc-gen-go-mvp v2.0.1
// created by mvp authors

package grpc

import (
	context "context"
	grpc "google.golang.org/grpc"
	metadata "google.golang.org/grpc/metadata"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
)

func NewIamCardServer(
	srv CardServer,
) CardServer {
	return &iamCardServer{
		srv: srv,
	}
}

var _ CardServer = (*iamCardServer)(nil)

type iamCardServer struct {
	UnimplementedCardServer

	srv CardServer
}

func (s *iamCardServer) CreateClientV1(
	ctx context.Context,
	req *CreateClientRequestV1,
) (
	*CreateClientResponseV1,
	error,
) {
	return s.srv.CreateClientV1(ctx, req)
}

func (s *iamCardServer) GetCardTokensV1(
	ctx context.Context,
	req *GetCardTokensRequestV1,
) (
	*GetCardTokensResponseV1,
	error,
) {
	return s.srv.GetCardTokensV1(ctx, req)
}

func (s *iamCardServer) GetOneClickPayInCardsV1(
	ctx context.Context,
	req *GetOneClickPayInCardsRequestV1,
) (
	*GetOneClickPayInCardsResponseV1,
	error,
) {
	return s.srv.GetOneClickPayInCardsV1(ctx, req)
}

func (s *iamCardServer) GetPanByCardIdV1(
	ctx context.Context,
	req *GetPanByCardIdRequestV1,
) (
	*GetPanResponseV1,
	error,
) {
	return s.srv.GetPanByCardIdV1(ctx, req)
}

func (s *iamCardServer) GetPanByHashedIdV1(
	ctx context.Context,
	req *GetPanByHashedIdRequestV1,
) (
	*GetPanResponseV1,
	error,
) {
	return s.srv.GetPanByHashedIdV1(ctx, req)
}

func (s *iamCardServer) GetOneClickPayOutCardsV1(
	ctx context.Context,
	req *GetOneClickPayOutCardsRequestV1,
) (
	*GetOneClickPayOutCardsResponseV1,
	error,
) {
	return s.srv.GetOneClickPayOutCardsV1(ctx, req)
}

func (s *iamCardServer) GetEncryptedCardToken(
	ctx context.Context,
	req *GetEncryptedCardRequestV1,
) (
	*GetEncryptedCardResponseV1,
	error,
) {
	return s.srv.GetEncryptedCardToken(ctx, req)
}

func (s *iamCardServer) GetClientListByVerification(
	ctx context.Context,
	req *GetClientListByVerificationRequestV1,
) (
	*GetClientListByProjectClientResponseV1,
	error,
) {
	return s.srv.GetClientListByVerification(ctx, req)
}

func (s *iamCardServer) GetClientListByProjectClient(
	ctx context.Context,
	req *GetClientListByProjectClientRequestV1,
) (
	*GetClientListByProjectClientResponseV1,
	error,
) {
	return s.srv.GetClientListByProjectClient(ctx, req)
}

func (s *iamCardServer) CheckClientActiveness(
	ctx context.Context,
	req *CheckClientActivenessRequestV1,
) (
	*emptypb.Empty,
	error,
) {
	return s.srv.CheckClientActiveness(ctx, req)
}

func (s *iamCardServer) GetCardByPan(
	ctx context.Context,
	req *GetCardByPanRequestV1,
) (
	*GetCardByPanResponseV1,
	error,
) {
	return s.srv.GetCardByPan(ctx, req)
}

func (s *iamCardServer) DecryptPayInCard(
	ctx context.Context,
	req *DecryptPayInRequest,
) (
	*DecryptPayInResponse,
	error,
) {
	return s.srv.DecryptPayInCard(ctx, req)
}

func (s *iamCardServer) DecryptPayOutCard(
	ctx context.Context,
	req *DecryptPayOutRequest,
) (
	*DecryptPayOutResponse,
	error,
) {
	return s.srv.DecryptPayOutCard(ctx, req)
}

func (s *iamCardServer) ReEncryptCard(
	ctx context.Context,
	req *ReEncryptCardRequest,
) (
	*ReEncryptCardResponse,
	error,
) {
	return s.srv.ReEncryptCard(ctx, req)
}

func (s *iamCardServer) GetPanInfoByProjectId(
	ctx context.Context,
	req *GetPanInfoByProjectIdRequest,
) (
	*GetPanInfoByProjectIdResponse,
	error,
) {
	return s.srv.GetPanInfoByProjectId(ctx, req)
}

func (s *iamCardServer) NewKey(
	ctx context.Context,
	req *emptypb.Empty,
) (
	*emptypb.Empty,
	error,
) {
	return s.srv.NewKey(ctx, req)
}

func (s *iamCardServer) RotateCardKeys(
	ctx context.Context,
	req *emptypb.Empty,
) (
	*emptypb.Empty,
	error,
) {
	return s.srv.RotateCardKeys(ctx, req)
}

func (s *iamCardServer) CheckExpireCards(
	ctx context.Context,
	req *emptypb.Empty,
) (
	*emptypb.Empty,
	error,
) {
	return s.srv.CheckExpireCards(ctx, req)
}

func (s *iamCardServer) CreateNewHashKey(
	ctx context.Context,
	req *emptypb.Empty,
) (
	*emptypb.Empty,
	error,
) {
	return s.srv.CreateNewHashKey(ctx, req)
}

func (s *iamCardServer) RotateHashKeys(
	ctx context.Context,
	req *emptypb.Empty,
) (
	*emptypb.Empty,
	error,
) {
	return s.srv.RotateHashKeys(ctx, req)
}

func NewIamCardClient(
	client CardClient,
) CardClient {
	return &iamCardClient{
		client: client,
	}
}

type iamCardClient struct {
	client CardClient
}

func (s *iamCardClient) CreateClientV1(
	ctx context.Context,
	req *CreateClientRequestV1,
	opts ...grpc.CallOption,
) (
	*CreateClientResponseV1,
	error,
) {
	md, _ := metadata.FromIncomingContext(ctx)
	return s.client.CreateClientV1(metadata.NewOutgoingContext(ctx, md), req)
}

func (s *iamCardClient) GetCardTokensV1(
	ctx context.Context,
	req *GetCardTokensRequestV1,
	opts ...grpc.CallOption,
) (
	*GetCardTokensResponseV1,
	error,
) {
	md, _ := metadata.FromIncomingContext(ctx)
	return s.client.GetCardTokensV1(metadata.NewOutgoingContext(ctx, md), req)
}

func (s *iamCardClient) GetOneClickPayInCardsV1(
	ctx context.Context,
	req *GetOneClickPayInCardsRequestV1,
	opts ...grpc.CallOption,
) (
	*GetOneClickPayInCardsResponseV1,
	error,
) {
	md, _ := metadata.FromIncomingContext(ctx)
	return s.client.GetOneClickPayInCardsV1(metadata.NewOutgoingContext(ctx, md), req)
}

func (s *iamCardClient) GetPanByCardIdV1(
	ctx context.Context,
	req *GetPanByCardIdRequestV1,
	opts ...grpc.CallOption,
) (
	*GetPanResponseV1,
	error,
) {
	md, _ := metadata.FromIncomingContext(ctx)
	return s.client.GetPanByCardIdV1(metadata.NewOutgoingContext(ctx, md), req)
}

func (s *iamCardClient) GetPanByHashedIdV1(
	ctx context.Context,
	req *GetPanByHashedIdRequestV1,
	opts ...grpc.CallOption,
) (
	*GetPanResponseV1,
	error,
) {
	md, _ := metadata.FromIncomingContext(ctx)
	return s.client.GetPanByHashedIdV1(metadata.NewOutgoingContext(ctx, md), req)
}

func (s *iamCardClient) GetOneClickPayOutCardsV1(
	ctx context.Context,
	req *GetOneClickPayOutCardsRequestV1,
	opts ...grpc.CallOption,
) (
	*GetOneClickPayOutCardsResponseV1,
	error,
) {
	md, _ := metadata.FromIncomingContext(ctx)
	return s.client.GetOneClickPayOutCardsV1(metadata.NewOutgoingContext(ctx, md), req)
}

func (s *iamCardClient) GetEncryptedCardToken(
	ctx context.Context,
	req *GetEncryptedCardRequestV1,
	opts ...grpc.CallOption,
) (
	*GetEncryptedCardResponseV1,
	error,
) {
	md, _ := metadata.FromIncomingContext(ctx)
	return s.client.GetEncryptedCardToken(metadata.NewOutgoingContext(ctx, md), req)
}

func (s *iamCardClient) GetClientListByVerification(
	ctx context.Context,
	req *GetClientListByVerificationRequestV1,
	opts ...grpc.CallOption,
) (
	*GetClientListByProjectClientResponseV1,
	error,
) {
	md, _ := metadata.FromIncomingContext(ctx)
	return s.client.GetClientListByVerification(metadata.NewOutgoingContext(ctx, md), req)
}

func (s *iamCardClient) GetClientListByProjectClient(
	ctx context.Context,
	req *GetClientListByProjectClientRequestV1,
	opts ...grpc.CallOption,
) (
	*GetClientListByProjectClientResponseV1,
	error,
) {
	md, _ := metadata.FromIncomingContext(ctx)
	return s.client.GetClientListByProjectClient(metadata.NewOutgoingContext(ctx, md), req)
}

func (s *iamCardClient) CheckClientActiveness(
	ctx context.Context,
	req *CheckClientActivenessRequestV1,
	opts ...grpc.CallOption,
) (
	*emptypb.Empty,
	error,
) {
	md, _ := metadata.FromIncomingContext(ctx)
	return s.client.CheckClientActiveness(metadata.NewOutgoingContext(ctx, md), req)
}

func (s *iamCardClient) GetCardByPan(
	ctx context.Context,
	req *GetCardByPanRequestV1,
	opts ...grpc.CallOption,
) (
	*GetCardByPanResponseV1,
	error,
) {
	md, _ := metadata.FromIncomingContext(ctx)
	return s.client.GetCardByPan(metadata.NewOutgoingContext(ctx, md), req)
}

func (s *iamCardClient) DecryptPayInCard(
	ctx context.Context,
	req *DecryptPayInRequest,
	opts ...grpc.CallOption,
) (
	*DecryptPayInResponse,
	error,
) {
	md, _ := metadata.FromIncomingContext(ctx)
	return s.client.DecryptPayInCard(metadata.NewOutgoingContext(ctx, md), req)
}

func (s *iamCardClient) DecryptPayOutCard(
	ctx context.Context,
	req *DecryptPayOutRequest,
	opts ...grpc.CallOption,
) (
	*DecryptPayOutResponse,
	error,
) {
	md, _ := metadata.FromIncomingContext(ctx)
	return s.client.DecryptPayOutCard(metadata.NewOutgoingContext(ctx, md), req)
}

func (s *iamCardClient) ReEncryptCard(
	ctx context.Context,
	req *ReEncryptCardRequest,
	opts ...grpc.CallOption,
) (
	*ReEncryptCardResponse,
	error,
) {
	md, _ := metadata.FromIncomingContext(ctx)
	return s.client.ReEncryptCard(metadata.NewOutgoingContext(ctx, md), req)
}

func (s *iamCardClient) GetPanInfoByProjectId(
	ctx context.Context,
	req *GetPanInfoByProjectIdRequest,
	opts ...grpc.CallOption,
) (
	*GetPanInfoByProjectIdResponse,
	error,
) {
	md, _ := metadata.FromIncomingContext(ctx)
	return s.client.GetPanInfoByProjectId(metadata.NewOutgoingContext(ctx, md), req)
}

func (s *iamCardClient) NewKey(
	ctx context.Context,
	req *emptypb.Empty,
	opts ...grpc.CallOption,
) (
	*emptypb.Empty,
	error,
) {
	md, _ := metadata.FromIncomingContext(ctx)
	return s.client.NewKey(metadata.NewOutgoingContext(ctx, md), req)
}

func (s *iamCardClient) RotateCardKeys(
	ctx context.Context,
	req *emptypb.Empty,
	opts ...grpc.CallOption,
) (
	*emptypb.Empty,
	error,
) {
	md, _ := metadata.FromIncomingContext(ctx)
	return s.client.RotateCardKeys(metadata.NewOutgoingContext(ctx, md), req)
}

func (s *iamCardClient) CheckExpireCards(
	ctx context.Context,
	req *emptypb.Empty,
	opts ...grpc.CallOption,
) (
	*emptypb.Empty,
	error,
) {
	md, _ := metadata.FromIncomingContext(ctx)
	return s.client.CheckExpireCards(metadata.NewOutgoingContext(ctx, md), req)
}

func (s *iamCardClient) CreateNewHashKey(
	ctx context.Context,
	req *emptypb.Empty,
	opts ...grpc.CallOption,
) (
	*emptypb.Empty,
	error,
) {
	md, _ := metadata.FromIncomingContext(ctx)
	return s.client.CreateNewHashKey(metadata.NewOutgoingContext(ctx, md), req)
}

func (s *iamCardClient) RotateHashKeys(
	ctx context.Context,
	req *emptypb.Empty,
	opts ...grpc.CallOption,
) (
	*emptypb.Empty,
	error,
) {
	md, _ := metadata.FromIncomingContext(ctx)
	return s.client.RotateHashKeys(metadata.NewOutgoingContext(ctx, md), req)
}
