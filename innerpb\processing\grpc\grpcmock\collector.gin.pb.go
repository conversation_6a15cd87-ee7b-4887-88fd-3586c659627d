// Code generated by MockGen. DO NOT EDIT.
// Source: collector.gin.pb.go

// Package grpcmock is a generated GoMock package.
package grpcmock

import (
	reflect "reflect"

	gin "github.com/gin-gonic/gin"
	gomock "github.com/golang/mock/gomock"
)

// MockGinCollectorServer is a mock of GinCollectorServer interface.
type MockGinCollectorServer struct {
	ctrl     *gomock.Controller
	recorder *MockGinCollectorServerMockRecorder
}

// MockGinCollectorServerMockRecorder is the mock recorder for MockGinCollectorServer.
type MockGinCollectorServerMockRecorder struct {
	mock *MockGinCollectorServer
}

// NewMockGinCollectorServer creates a new mock instance.
func NewMockGinCollectorServer(ctrl *gomock.Controller) *MockGinCollectorServer {
	mock := &MockGinCollectorServer{ctrl: ctrl}
	mock.recorder = &MockGinCollectorServerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockGinCollectorServer) EXPECT() *MockGinCollectorServerMockRecorder {
	return m.recorder
}

// CollectTransaction mocks base method.
func (m *MockGinCollectorServer) CollectTransaction(c *gin.Context) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CollectTransaction", c)
	ret0, _ := ret[0].(error)
	return ret0
}

// CollectTransaction indicates an expected call of CollectTransaction.
func (mr *MockGinCollectorServerMockRecorder) CollectTransaction(c interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CollectTransaction", reflect.TypeOf((*MockGinCollectorServer)(nil).CollectTransaction), c)
}

// GetTransactionStatus mocks base method.
func (m *MockGinCollectorServer) GetTransactionStatus(c *gin.Context) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetTransactionStatus", c)
	ret0, _ := ret[0].(error)
	return ret0
}

// GetTransactionStatus indicates an expected call of GetTransactionStatus.
func (mr *MockGinCollectorServerMockRecorder) GetTransactionStatus(c interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetTransactionStatus", reflect.TypeOf((*MockGinCollectorServer)(nil).GetTransactionStatus), c)
}
