// Code generated by protoc-gen-go-mvp. DO NOT EDIT.
// versions:
// - protoc-gen-go-mvp v2.0.1
// created by mvp authors

package grpc

import (
	context "context"
	grpc "google.golang.org/grpc"
	metadata "google.golang.org/grpc/metadata"
)

func NewIamTransactionStatusServer(
	srv TransactionStatusServer,
) TransactionStatusServer {
	return &iamTransactionStatusServer{
		srv: srv,
	}
}

var _ TransactionStatusServer = (*iamTransactionStatusServer)(nil)

type iamTransactionStatusServer struct {
	UnimplementedTransactionStatusServer

	srv TransactionStatusServer
}

func (s *iamTransactionStatusServer) GetBatchTransactionWithStatuses(
	ctx context.Context,
	req *BatchTransactionRequestV1,
) (
	*BatchTransactionResponseV1,
	error,
) {
	return s.srv.GetBatchTransactionWithStatuses(ctx, req)
}

func NewIamTransactionStatusClient(
	client TransactionStatusClient,
) TransactionStatusClient {
	return &iamTransactionStatusClient{
		client: client,
	}
}

type iamTransactionStatusClient struct {
	client TransactionStatusClient
}

func (s *iamTransactionStatusClient) GetBatchTransactionWithStatuses(
	ctx context.Context,
	req *BatchTransactionRequestV1,
	opts ...grpc.CallOption,
) (
	*BatchTransactionResponseV1,
	error,
) {
	md, _ := metadata.FromIncomingContext(ctx)
	return s.client.GetBatchTransactionWithStatuses(metadata.NewOutgoingContext(ctx, md), req)
}
