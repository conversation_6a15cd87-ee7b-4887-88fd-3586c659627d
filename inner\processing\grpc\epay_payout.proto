edition = "2023";

package processing.epay_payout.epay;

option go_package = "git.local/sensitive/innerpb/processing/grpc";

import "mvp/proto/refs.proto";
import "google/protobuf/descriptor.proto";
import "inner/processing/grpc/transaction_status.proto";
import "inner/processing/grpc/integration.proto";

message EpayPayOutCodeRef {
  string message = 1;
  processing.transaction.transaction_status.EnumTransactionStatus transaction_status = 2;
  processing.integration.integration.IntegrationError integration_error = 3;
}

extend google.protobuf.EnumValueOptions {
  EpayPayOutCodeRef epay_payout_code_value = 200006;
}

extend google.protobuf.EnumOptions {
  EpayPayOutCodeRef default_epay_payout_code_value = 200007;
}

enum EnumEpayPayOutCode {
  option(mvp.default_ref) = "default_epay_payout_code_value";
  option(mvp.ref) = "epay_payout_code_value";
  option(default_epay_payout_code_value) = {
    message: "undefined",
    transaction_status: TransactionStatusHolded,
    integration_error: UndefinedError,
  };
  Empty_Response_Code = 0 [(epay_payout_code_value) = {
    message: "undefined",
    transaction_status: TransactionStatusHolded,
    integration_error: UndefinedError,
  }];
  ClientauthenticationCode_33 = -33 [(epay_payout_code_value) = {
  message: "Client authentication in progress",
      transaction_status: 5,
      integration_error: ThreeDSAuthFailed,
      }];
  SystemerrorpleasetryCode_50 = -50 [(epay_payout_code_value) = {
  message: "System error, please try again",
      transaction_status: 5,
      integration_error: UnavailableAcquirer,
      }];
  ErrorinCVC2orCVC2DesCode_18 = -18 [(epay_payout_code_value) = {
  message: "Error in CVC2 or CVC2 Description fields",
      transaction_status: 5,
      integration_error: IncorrectCVVCVC,
      }];
  InvalidRetrievalrefeCode_15 = -15 [(epay_payout_code_value) = {
  message: "Invalid Retrieval reference number",
      transaction_status: 5,
      integration_error: TransactionDeclinedByAcquirer,
      }];
  TheoperationfailedplCode_454 = 454 [(epay_payout_code_value) = {
    message: "The operation failed, please check the amount on the card is not blocked and try again later",
    transaction_status: 11,
    integration_error: BlockedCard,
  }];
    ThreeDSecurecheckfailedCode_455 = 455 [(epay_payout_code_value) = {
  message: "3DSecure check failed",
      transaction_status: 5,
      integration_error: ThreeDSAuthFailed,
      }];
  AccessdeniedCode_456 = 456 [(epay_payout_code_value) = {
    message: "Access denied",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  ErrorincardexpiratioCode_457 = 457 [(epay_payout_code_value) = {
    message: "Error in card expiration date field",
    transaction_status: 5,
    integration_error: IncorrectCardExpDate,
  }];
  ServerisnotrespondinCode_458 = 458 [(epay_payout_code_value) = {
    message: "Server is not responding",
    transaction_status: 5,
    integration_error: UnavailableAcquirer,
  }];
  ServerisnotrespondinCode_459 = 459 [(epay_payout_code_value) = {
    message: "Server is not responding",
    transaction_status: 11,
    integration_error: UnavailableAcquirer,
  }];
  NoorinvalidresponcerCode_460 = 460 [(epay_payout_code_value) = {
    message: "No or invalid responce received",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  BadCGIrequestCode_461 = 461 [(epay_payout_code_value) = {
    message: "Bad CGI request",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  CallyourbankCode_462 = 462 [(epay_payout_code_value) = {
    message: "Call your bank",
    transaction_status: 5,
    integration_error: UnavailableIssuer,
  }];
  CallyourbankCode_463 = 463 [(epay_payout_code_value) = {
    message: "Call your bank",
    transaction_status: 5,
    integration_error: UnavailableIssuer,
  }];
  InvalidmerchantCode_464 = 464 [(epay_payout_code_value) = {
    message: "Invalid merchant",
    transaction_status: 5,
    integration_error: PaymentForbiddenForMerchant,
  }];
  YourcardisrestrictedCode_465 = 465 [(epay_payout_code_value) = {
    message: "Your card is restricted",
    transaction_status: 5,
    integration_error: InvalidCard,
  }];
  NotpermittedtoclientCode_466 = 466 [(epay_payout_code_value) = {
    message: "Not permitted to client by Issuing bank. Call your bank",
    transaction_status: 5,
    integration_error: UnavailableIssuer,
  }];
  YourcardisdisabledCode_467 = 467 [(epay_payout_code_value) = {
    message: "Your card is disabled",
    transaction_status: 5,
    integration_error: BlockedCard,
  }];
  AdditionalidentificaCode_468 = 468 [(epay_payout_code_value) = {
    message: "Additional identification required",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  InvalidtransactionCode_469 = 469 [(epay_payout_code_value) = {
    message: "Invalid transaction",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  InvalidamountCode_470 = 470 [(epay_payout_code_value) = {
    message: "Invalid amount",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  NosuchcardCode_471 = 471 [(epay_payout_code_value) = {
    message: "No such card",
    transaction_status: 5,
    integration_error: InvalidCard,
  }];
  NosuchcardCode_472 = 472 [(epay_payout_code_value) = {
    message: "No such card",
    transaction_status: 5,
    integration_error: InvalidCard,
  }];
  AuthenticationfailedCode_473 = 473 [(epay_payout_code_value) = {
    message: "Authentication failed",
    transaction_status: 5,
    integration_error: ThreeDSAuthFailed,
  }];
  InvalidresponseCode_475 = 475 [(epay_payout_code_value) = {
    message: "Invalid response",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  NoactiontakenCode_476 = 476 [(epay_payout_code_value) = {
    message: "No action taken",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  FormaterrorCode_477 = 477 [(epay_payout_code_value) = {
    message: "Format error",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  ExpiredcardCode_478 = 478 [(epay_payout_code_value) = {
    message: "Expired card",
    transaction_status: 5,
    integration_error: CardHasExpired,
  }];
  RestrictedcardCode_479 = 479 [(epay_payout_code_value) = {
    message: "Restricted card",
    transaction_status: 5,
    integration_error: InvalidCard,
  }];
  CallyourbankCode_480 = 480 [(epay_payout_code_value) = {
    message: "Call your bank",
    transaction_status: 5,
    integration_error: UnavailableIssuer,
  }];
  LostcardCode_481 = 481 [(epay_payout_code_value) = {
    message: "Lost card",
    transaction_status: 5,
    integration_error: LostCard,
  }];
  LostcardCode_482 = 482 [(epay_payout_code_value) = {
    message: "Lost card",
    transaction_status: 5,
    integration_error: LostCard,
  }];
  StolencardCode_483 = 483 [(epay_payout_code_value) = {
    message: "Stolen card",
    transaction_status: 5,
    integration_error: StolenCard,
  }];
  NotsufficientfundsCode_484 = 484 [(epay_payout_code_value) = {
    message: "Not sufficient funds",
    transaction_status: 5,
    integration_error: InsufficientFunds,
  }];
  ExpiredcardCode_485 = 485 [(epay_payout_code_value) = {
    message: "Expired card",
    transaction_status: 5,
    integration_error: CardHasExpired,
  }];
  NotpermittedtoclientCode_486 = 486 [(epay_payout_code_value) = {
    message: "Not permitted to client by Issuing bank. Call your bank.",
    transaction_status: 5,
    integration_error: UnavailableIssuer,
  }];
  NotpermittedtomerchaCode_487 = 487 [(epay_payout_code_value) = {
    message: "Not permitted to merchant",
    transaction_status: 5,
    integration_error: PaymentForbiddenForMerchant,
  }];
  ExceedsamountlimitCode_488 = 488 [(epay_payout_code_value) = {
    message: "Exceeds amount limit",
    transaction_status: 5,
    integration_error: ExceedsAmountLimit,
  }];
  RestrictedcardCode_489 = 489 [(epay_payout_code_value) = {
    message: "Restricted card",
    transaction_status: 5,
    integration_error: InvalidCard,
  }];
  InvalidContractcardCode_490 = 490 [(epay_payout_code_value) = {
    message: "Invalid Contract (card)",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  ExceedsfrequencylimiCode_491 = 491 [(epay_payout_code_value) = {
    message: "Exceeds frequency limit",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  PINtriesexceededCode_492 = 492 [(epay_payout_code_value) = {
    message: "PIN tries exceeded",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  TimeoutatissuersysteCode_493 = 493 [(epay_payout_code_value) = {
    message: "Time-out at issuer system",
    transaction_status: 11,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  IssuerunavailableCode_494 = 494 [(epay_payout_code_value) = {
    message: "Issuer unavailable",
    transaction_status: 11,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  CannotbecompletedvioCode_495 = 495 [(epay_payout_code_value) = {
    message: "Cannot be completed, violation of law",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
    ThreeDSecureSecureCodeabCode_496 = 496 [(epay_payout_code_value) = {
  message: "3DSecure SecureCode absent",
      transaction_status: 11,
      integration_error: ThreeDSAuthFailed,
      }];
  ServerisnotrespondinCode_497 = 497 [(epay_payout_code_value) = {
    message: "Server is not responding. Please try later",
    transaction_status: 5,
    integration_error: UnavailableAcquirer,
  }];
  ErrorincurrencyfieldCode_499 = 499 [(epay_payout_code_value) = {
    message: "Error in currency field",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
    ThreeDSecurecheckfailedCode_500 = 500 [(epay_payout_code_value) = {
  message: "3DSecure check failed",
      transaction_status: 5,
      integration_error: ThreeDSAuthFailed,
      }];
  CardcheckfailedCode_501 = 501 [(epay_payout_code_value) = {
    message: "Card check failed",
    transaction_status: 5,
    integration_error: InvalidCard,
  }];
  ThreeDSecureSecureCodeabCode_502 = 502 [(epay_payout_code_value) = {
  message: "3DSecure SecureCode absent",
      transaction_status: 5,
      integration_error: ThreeDSAuthFailed,
      }];
  ThreeDSecureSecureCodeabCode_503 = 503 [(epay_payout_code_value) = {
  message: "3DSecure SecureCode absent",
      transaction_status: 5,
      integration_error: ThreeDSAuthFailed,
      }];
  TransactiondeclinedCCode_523 = 523 [(epay_payout_code_value) = {
    message: "Transaction declined.Call your bank",
    transaction_status: 5,
    integration_error: TransactionDeclinedByIssuer,
  }];
  YourcardisrestrictedCode_524 = 524 [(epay_payout_code_value) = {
    message: "Your card is restricted. Call your bank",
    transaction_status: 5,
    integration_error: InvalidCard,
  }];
  YourcardisrestrictedCode_525 = 525 [(epay_payout_code_value) = {
    message: "Your card is restricted. Call your bank",
    transaction_status: 5,
    integration_error: InvalidCard,
  }];
  SystemerrorPleasetryCode_526 = 526 [(epay_payout_code_value) = {
    message: "System error. Please try later. If this problem continues, please contact the customer support",
    transaction_status: 5,
    integration_error: UnavailableAcquirer,
  }];
  TransactiondeclinedCCode_527 = 527 [(epay_payout_code_value) = {
    message: "Transaction declined.Call your bank",
    transaction_status: 5,
    integration_error: UnavailableIssuer,
  }];
  ThedailylimitofincomCode_528 = 528 [(epay_payout_code_value) = {
    message: "The daily limit of incoming transfers on the recipient's card has been exceeded, please provide another card for transfer",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  TransactiondeclinedCCode_531 = 531 [(epay_payout_code_value) = {
    message: "Transaction declined.Call your bank",
    transaction_status: 5,
    integration_error: UnavailableIssuer,
  }];
  UnabletoverifyPINcalCode_532 = 532 [(epay_payout_code_value) = {
    message: "Unable to verify PIN,call your bank",
    transaction_status: 5,
    integration_error: UnavailableIssuer,
  }];
  AuthenticationfailedCode_19 = -19 [(epay_payout_code_value) = {
  message: "Authentication failed (3DSecure/Securecode)",
      transaction_status: 5,
      integration_error: ThreeDSAuthFailed,
      }];
  ErrorincardexpiratioCode_9 = -9 [(epay_payout_code_value) = {
  message: "Error in card expiration date field",
      transaction_status: 5,
      integration_error: IncorrectCardExpDate,
      }];
  ServerisnotrespondinCode_8 = -8 [(epay_payout_code_value) = {
  message: "Server is not responding",
      transaction_status: 5,
      integration_error: UnavailableAcquirer,
      }];
  ServerisnotrespondinCode_4 = -4 [(epay_payout_code_value) = {
  message: "Server is not responding",
      transaction_status: 5,
      integration_error: UnavailableAcquirer,
      }];
  ErrorBindActionCreatCode_1 = 1 [(epay_payout_code_value) = {
    message: "Error Bind. Action - CreateApplication",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  ErrorcreatingrecordiCode_2 = 2 [(epay_payout_code_value) = {
    message: "Error creating record in Application. Action - CreateApplication",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  ErrorBindActionUpdatCode_3 = 3 [(epay_payout_code_value) = {
    message: "Error Bind. Action - UpdateApplication",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  ErrorwhileupdatingreCode_4 = 4 [(epay_payout_code_value) = {
    message: "Error while updating record. Action - UpdateApplicationRecord",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  ErrorwhiledeletingthCode_5 = 5 [(epay_payout_code_value) = {
    message: "Error while deleting the record. Action - DeleteApplication",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  ErrorGetByNameActionCode_6 = 6 [(epay_payout_code_value) = {
    message: "Error Get By Name. Action - GetApplication",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  ErrorGetByIDActionGeCode_7 = 7 [(epay_payout_code_value) = {
    message: "Error Get By ID. Action - GetApplicationByID",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  ErrorBindActionCreatCode_8 = 8 [(epay_payout_code_value) = {
    message: "Error Bind. Action - CreateError",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  ErrorcreatingrecordiCode_9 = 9 [(epay_payout_code_value) = {
    message: "Error creating record in ERROR. Action - CreateError",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  RedisActionCreateErrCode_10 = 10 [(epay_payout_code_value) = {
    message: "Ошибка при сохранении данных из redis. Action - CreateError",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  ErrorBindActionUpdatCode_11 = 11 [(epay_payout_code_value) = {
    message: "Error Bind. Action - UpdateError",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  ErrorwhileupdatingreCode_12 = 12 [(epay_payout_code_value) = {
    message: "Error while updating record. Action - UpdateError ",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  RedisActionUpdateErrCode_13 = 13 [(epay_payout_code_value) = {
    message: "Ошибка при изменение данных из redis. Action - UpdateError ",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  ErrorwhiledeletingthCode_14 = 14 [(epay_payout_code_value) = {
    message: "Error while deleting the record. Action - DeleteError",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  RedisActionDeleteErrCode_15 = 15 [(epay_payout_code_value) = {
    message: "Ошибка при удаление данных из redis. Action - DeleteError",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  ErrorGetByCodeinPostCode_16 = 16 [(epay_payout_code_value) = {
    message: "Error Get By Code in Postgre SQL. Action - GetErrorByCode",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  CodeActionGetErrorByCode_17 = 17 [(epay_payout_code_value) = {
    message: "Не удалось получить данные по ошибке. Проверьте введенный code. Action - GetErrorByCode",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  IDApplicationIDAppliCode_18 = 18 [(epay_payout_code_value) = {
    message: "Не удалось получить данные по ID Application. Проверьте корректность ID Application",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  CodeCode_19 = 19 [(epay_payout_code_value) = {
    message: "Некорректно введен code ошибки",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  IDApplicationIDAppliCode_20 = 20 [(epay_payout_code_value) = {
    message: "Не удалось получить ошибки по ID Application. Проверьте корректность ID Application ",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  IDApplicationApplicaCode_21 = 21 [(epay_payout_code_value) = {
    message: "Не удалось проверить наличие  ошибкок по ID Application, для удаления Application. Проверьте корректность ID Application ",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_22 = 22 [(epay_payout_code_value) = {
    message: "Для удаления Приложения, сначала необходимо удалить все ошибки. Произведите поиск ошибок по Приложению и удалите их.",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  SuccessfullyCode_23 = 23 [(epay_payout_code_value) = {
    message: "Successfully",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Test111Code_24 = 24 [(epay_payout_code_value) = {
    message: "test 111",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  InvoiceidCode_25 = 25 [(epay_payout_code_value) = {
    message: "ошибка, ссылка уже существует с таким invoice id.",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  BindJSONBindJSONCode_27 = 27 [(epay_payout_code_value) = {
    message: "Ошибка BindJSON. Невозможно демаршалировать структуру с помощью BindJSON",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  TransactionalreadyprCode_28 = 28 [(epay_payout_code_value) = {
    message: "Transaction already processed",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  TransactionalreadyprCode_29 = 29 [(epay_payout_code_value) = {
    message: "Transaction already processed",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  ErrorGetShopsByMerchCode_54 = 54 [(epay_payout_code_value) = {
    message: "Error Get Shops By Merchant",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  IdCode_55 = 55 [(epay_payout_code_value) = {
    message: "Некорректно введен параметр id",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  ErrorGetShopByIDCode_56 = 56 [(epay_payout_code_value) = {
    message: "Error Get Shop By ID",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  ErrorBindCode_57 = 57 [(epay_payout_code_value) = {
    message: "Error Bind",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  ErrorBindCode_58 = 58 [(epay_payout_code_value) = {
    message: "Error Bind",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  ErrorCreateShopCode_59 = 59 [(epay_payout_code_value) = {
    message: "Error Create Shop",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  ErrorGetMerchantByIDCode_60 = 60 [(epay_payout_code_value) = {
    message: "Error Get Merchant By ID",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  ErrorGetStatusByNameCode_61 = 61 [(epay_payout_code_value) = {
    message: "Error Get Status By Name",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  ErrorCreateDefaultCoCode_64 = 64 [(epay_payout_code_value) = {
    message: "Error Create Default Contact",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  ErrorCreateMerchantCode_65 = 65 [(epay_payout_code_value) = {
    message: "Error Create Merchant",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  ErrorVerificationSerCode_71 = 71 [(epay_payout_code_value) = {
    message: "Error Verification Service",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  ErrorCheckMerchantCode_72 = 72 [(epay_payout_code_value) = {
    message: "Error Check Merchant",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  ErrorSendVerificatioCode_73 = 73 [(epay_payout_code_value) = {
    message: "Error Send Verification Email",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  ErrorVerificationSerCode_74 = 74 [(epay_payout_code_value) = {
    message: "Error Verification Service",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  ErrorFindMerchantByECode_75 = 75 [(epay_payout_code_value) = {
    message: "Error Find Merchant By Email",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  ErrorUpdateMerchantCode_76 = 76 [(epay_payout_code_value) = {
    message: "Error Update Merchant",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  ErrorRegistrationUseCode_77 = 77 [(epay_payout_code_value) = {
    message: "Error Registration User",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  RrnCode_83 = 83 [(epay_payout_code_value) = {
    message: "Некорректно введен параметр rrn",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  ErrorGetCardTypebyRRCode_84 = 84 [(epay_payout_code_value) = {
    message: "Error Get Card Type by RRN",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  ErrorCheckTransactioCode_85 = 85 [(epay_payout_code_value) = {
    message: "Error Check Transaction",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  UnauthorizedCode_86 = 86 [(epay_payout_code_value) = {
    message: "Unauthorized",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  TokenisnotvalidCode_87 = 87 [(epay_payout_code_value) = {
    message: "Token is not valid",
    transaction_status: 5,
    integration_error: InvalidCard,
  }];
  ErrorCheckTokenCode_88 = 88 [(epay_payout_code_value) = {
    message: "Error Check Token",
    transaction_status: 5,
    integration_error: InvalidCard,
  }];
  ErrorNoScopesCode_89 = 89 [(epay_payout_code_value) = {
    message: "Error No Scopes",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  ErrorNoScopesCode_90 = 90 [(epay_payout_code_value) = {
    message: "Error No Scopes",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  ErrorCheckTokenCode_91 = 91 [(epay_payout_code_value) = {
    message: "Error Check Token",
    transaction_status: 5,
    integration_error: InvalidCard,
  }];
  Cardnumber14Code_92 = 92 [(epay_payout_code_value) = {
    message: "Некорректно введен параметр cardnumber. Номер карты должен содержать не менее 14 цифр.",
    transaction_status: 5,
    integration_error: IncorrectCardNumber,
  }];
  ErrorGetCardCorporatCode_94 = 94 [(epay_payout_code_value) = {
    message: "Error Get Card Corporate Attribute By Card Number",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  ErrorGetCountryByBINCode_95 = 95 [(epay_payout_code_value) = {
    message: "Error Get Country By BIN",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  InvaliddataCode_96 = 96 [(epay_payout_code_value) = {
    message: "invalid data",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  ErrorCheckTokenCode_97 = 97 [(epay_payout_code_value) = {
    message: "Error Check Token",
    transaction_status: 5,
    integration_error: InvalidCard,
  }];
  ErrorNoScopesCode_98 = 98 [(epay_payout_code_value) = {
    message: "Error No Scopes",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  ContractCode_99 = 99 [(epay_payout_code_value) = {
    message: "Некорректно введен параметр 'contract'",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  InvalidDateformatPleCode_111 = 111 [(epay_payout_code_value) = {
    message: "Invalid Date format. Please enter in the following format : 01.02.2011",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  ContractCode_112 = 112 [(epay_payout_code_value) = {
    message: "Некорректно введен параметр 'contract'",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  ExtcsvxlsxCode_113 = 113 [(epay_payout_code_value) = {
    message: "Некорректно введён параметр ext. Параметр может принить одно из следующих значений: csv, xlsx.",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  ErrorGetStatementByDCode_114 = 114 [(epay_payout_code_value) = {
    message: "Error Get Statement By Debit Date",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  ErrorGetStatementByOCode_115 = 115 [(epay_payout_code_value) = {
    message: "Error Get Statement By One Day",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  ErrorGetStatementAirCode_116 = 116 [(epay_payout_code_value) = {
    message: "Error Get Statement AirAstana",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  ErrorGetStatementByTCode_117 = 117 [(epay_payout_code_value) = {
    message: "Error Get Statement By Transaction Date",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  ErrorCheckTokenCode_121 = 121 [(epay_payout_code_value) = {
    message: "Error Check Token",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_122 = 122 [(epay_payout_code_value) = {
    message: "при проверке пользователя произошла ошибка",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  ErrorBindCode_123 = 123 [(epay_payout_code_value) = {
    message: "Error Bind",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_124 = 124 [(epay_payout_code_value) = {
    message: "при получении статуса возникла ошибка",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_125 = 125 [(epay_payout_code_value) = {
    message: "пользователь заблокирован",
    transaction_status: 5,
    integration_error: SuspiciousClient,
  }];
  Code_126 = 126 [(epay_payout_code_value) = {
    message: "не смогли получить информацию по токену",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_127 = 127 [(epay_payout_code_value) = {
    message: "заполненны не все необходимые поля",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  ErrorEmptyMerchantIDCode_128 = 128 [(epay_payout_code_value) = {
    message: "Error Empty MerchantID",
    transaction_status: 5,
    integration_error: PaymentForbiddenForMerchant,
  }];
  ErrorUnknownMerchantCode_129 = 129 [(epay_payout_code_value) = {
    message: "Error Unknown MerchantID",
    transaction_status: 5,
    integration_error: PaymentForbiddenForMerchant,
  }];
  ServerErrorCode_130 = 130 [(epay_payout_code_value) = {
    message: "Server Error",
    transaction_status: 5,
    integration_error: UnavailableAcquirer,
  }];
  ErrorCheckTokenCode_131 = 131 [(epay_payout_code_value) = {
    message: "Error Check Token",
    transaction_status: 5,
    integration_error: InvalidCard,
  }];
  ErrorInvalidScopeCode_132 = 132 [(epay_payout_code_value) = {
    message: "Error Invalid Scope",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  ErrorBindCode_133 = 133 [(epay_payout_code_value) = {
    message: "Error Bind",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  ErrorRequestPostFormCode_134 = 134 [(epay_payout_code_value) = {
    message: "Error RequestPostFormXML",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  ErrorBodyIsEmptyCode_135 = 135 [(epay_payout_code_value) = {
    message: "Error Body Is Empty",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  ErrorRequestIsNotSucCode_136 = 136 [(epay_payout_code_value) = {
    message: "Error Request Is Not Successful",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  UnkwonErrorCode_137 = 137 [(epay_payout_code_value) = {
    message: "Unkwon Error",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  SecretincorrecterrorCode_138 = 138 [(epay_payout_code_value) = {
    message: "Secret incorrect error",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_139 = 139 [(epay_payout_code_value) = {
    message: "не смогли получить информацию по клиенту",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  SecretincorrectCode_140 = 140 [(epay_payout_code_value) = {
    message: "Secret incorrect",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  ErrorgetclientfromreCode_141 = 141 [(epay_payout_code_value) = {
    message: "Error get client from redis",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  ErrorparseclientCode_142 = 142 [(epay_payout_code_value) = {
    message: "Error parse client",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  CheckscopesCode_143 = 143 [(epay_payout_code_value) = {
    message: "check scopes",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  UseridpasswordCode_144 = 144 [(epay_payout_code_value) = {
    message: "Нет user id или password",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  UserstatusCHANGEPASSCode_145 = 145 [(epay_payout_code_value) = {
    message: "user status = CHANGEPASSWORD",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  UsernotfoundCode_146 = 146 [(epay_payout_code_value) = {
    message: "user not found",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  StatusCode_147 = 147 [(epay_payout_code_value) = {
    message: "Не удалось получить статус из справочника status",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_148 = 148 [(epay_payout_code_value) = {
    message: "Пользователь заблокирован, требуется пройти восставноление доступа",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  UserstatusinvalidCode_149 = 149 [(epay_payout_code_value) = {
    message: "user status invalid",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  StatusisnotdefinedCode_150 = 150 [(epay_payout_code_value) = {
    message: "status is not defined",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  PASSWORDINCORRECTCode_151 = 151 [(epay_payout_code_value) = {
    message: "PASSWORD INCORRECT",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  GranttypeCode_152 = 152 [(epay_payout_code_value) = {
    message: "Не обрабатываемый grant type",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  RefreshTokenCode_153 = 153 [(epay_payout_code_value) = {
    message: "Не указан refreshToken",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_154 = 154 [(epay_payout_code_value) = {
    message: "Не удалось загрузить информацию по рефреш токену",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  ScopeCode_155 = 155 [(epay_payout_code_value) = {
    message: "Scope по рефрешу отличается от запрошенного",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  RefreshtokenCode_156 = 156 [(epay_payout_code_value) = {
    message: "Refresh token устарел",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  GENERATEACCESSERRORCode_157 = 157 [(epay_payout_code_value) = {
    message: "GENERATE ACCESS ERROR",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  GENERATEACCESSERRORCode_158 = 158 [(epay_payout_code_value) = {
    message: "GENERATE ACCESS ERROR",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  EXTENSIONFIELDADDERRCode_159 = 159 [(epay_payout_code_value) = {
    message: "EXTENSION FIELD ADD ERROR",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  EXTENSIONFIELDADDERRCode_160 = 160 [(epay_payout_code_value) = {
    message: "EXTENSION FIELD ADD ERROR",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_161 = 161 [(epay_payout_code_value) = {
    message: "не получили статусы",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_162 = 162 [(epay_payout_code_value) = {
    message: "Не корректный запрос",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  GetUserByLoginerrorcCode_163 = 163 [(epay_payout_code_value) = {
    message: "GetUserByLogin error (check exists)",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_164 = 164 [(epay_payout_code_value) = {
    message: "Пароль не соответствует требованиям",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  GetStatusByNameerrorCode_165 = 165 [(epay_payout_code_value) = {
    message: "GetStatusByName error",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  HashPassworderrorCode_166 = 166 [(epay_payout_code_value) = {
    message: "hashPassword error",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  SaveUsererrorCode_167 = 167 [(epay_payout_code_value) = {
    message: "SaveUser error",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  UpdateUsererrorCode_169 = 169 [(epay_payout_code_value) = {
    message: "UpdateUser error",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  UpdateUsererrorCode_170 = 170 [(epay_payout_code_value) = {
    message: "UpdateUser error",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  GetUserByLoginerrorcCode_171 = 171 [(epay_payout_code_value) = {
    message: "GetUserByLogin error (check exists)",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_172 = 172 [(epay_payout_code_value) = {
    message: "Не корректный запрос",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_173 = 173 [(epay_payout_code_value) = {
    message: "Не корректный запрос",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  ActionServiceGetDebiCode_178 = 178 [(epay_payout_code_value) = {
    message: "Ошибка при получение выписки по дате списания. Action - Service.GetDebitDateStatement.",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  ActionServiceGetTranCode_179 = 179 [(epay_payout_code_value) = {
    message: "Ошибка при получение выписки по дате транзакции. Action - Service.GetTransactionDateStatement.",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  ReferenceEPAYCode_180 = 180 [(epay_payout_code_value) = {
    message: "Не удалось получить данные по reference из базу EPAY для формирования выписки по дате списания.",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  RedisCode_181 = 181 [(epay_payout_code_value) = {
    message: "Ошибка при сохранение данных в Redis",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_182 = 182 [(epay_payout_code_value) = {
    message: "не удалось получить клиента из хранилища",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_185 = 185 [(epay_payout_code_value) = {
    message: "Входные параметры отсутствуют или некорректны ",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  ErrorUnmarshalCode_186 = 186 [(epay_payout_code_value) = {
    message: "Error Unmarshal",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  StartDateandEndDatesCode_193 = 193 [(epay_payout_code_value) = {
    message: "Start Date and End Date should be in format YYYY-MM-DD",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  EPayCode_194 = 194 [(epay_payout_code_value) = {
    message: "Ошибка при получение данных из ePay",
    transaction_status: 5,
    integration_error: UnavailableAcquirer,
  }];
  NoAuthorizeCode_197 = 197 [(epay_payout_code_value) = {
    message: "No Authorize",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  BadtokenCode_198 = 198 [(epay_payout_code_value) = {
    message: "Bad token",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  ParseerrorCode_199 = 199 [(epay_payout_code_value) = {
    message: "Parse error",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  PaymenterrorCode_200 = 200 [(epay_payout_code_value) = {
    message: "payment error",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_216 = 216 [(epay_payout_code_value) = {
    message: "не смогли получить оперции мерчанта",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  ErrorBindJSONCannotuCode_224 = 224 [(epay_payout_code_value) = {
    message: "Error BindJSON. Cannot unmarshal into struct with BindJSON",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  LIKECode_225 = 225 [(epay_payout_code_value) = {
    message: "Произошла ошибка при использование оператора LIKE . Некорректные параметры.",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_226 = 226 [(epay_payout_code_value) = {
    message: "Отсутствует наименование поля необходимое для осуществления поиска. По данному полю будет производится поиск в БД.",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  BETWEENCode_227 = 227 [(epay_payout_code_value) = {
    message: "Произошла ошибка при использование оператора BETWEEN. Указано не достаточное количество параметров",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_228 = 228 [(epay_payout_code_value) = {
    message: "Некорректные параметры для поиска",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_229 = 229 [(epay_payout_code_value) = {
    message: "Отсутствуют параметры для поиска",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_230 = 230 [(epay_payout_code_value) = {
    message: "По вашему запросу данные не найдены",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_231 = 231 [(epay_payout_code_value) = {
    message: "Период транзакций является обязательным параметром поиска",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_232 = 232 [(epay_payout_code_value) = {
    message: "Метод поиска отсутствует",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  ErrorCheckTokenCode_233 = 233 [(epay_payout_code_value) = {
    message: "Error Check Token",
    transaction_status: 5,
    integration_error: InvalidCard,
  }];
  ErrorNoScopesCode_234 = 234 [(epay_payout_code_value) = {
    message: "Error No Scopes",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  FromdateYYYYMMDDCode_235 = 235 [(epay_payout_code_value) = {
    message: "Параметр 'fromdate' не соответствует формату YYYY-MM-DD",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  TodateYYYYMMDDCode_236 = 236 [(epay_payout_code_value) = {
    message: "Параметр 'todate' не соответствует формату YYYY-MM-DD	",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  ErrorCheckTokenCode_237 = 237 [(epay_payout_code_value) = {
    message: "Error Check Token",
    transaction_status: 5,
    integration_error: InvalidCard,
  }];
  ErrorNoScopesCode_238 = 238 [(epay_payout_code_value) = {
    message: "Error No Scopes",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  CannotunmarshalintosCode_239 = 239 [(epay_payout_code_value) = {
    message: "Cannot unmarshal into struct with BindJSON",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_240 = 240 [(epay_payout_code_value) = {
    message: "Ошибка при создание графика выписок в БД",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_241 = 241 [(epay_payout_code_value) = {
    message: "Ошибка при изменение графика выписок в БД",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_242 = 242 [(epay_payout_code_value) = {
    message: "Ошибка при удаление  графика выписок из БД",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  IDCode_243 = 243 [(epay_payout_code_value) = {
    message: "Не удалось получить график выписок по ID",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  MerchantIDCode_244 = 244 [(epay_payout_code_value) = {
    message: "Не удалось получить  график выписок по MerchantID",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  StartDateandEndDatesCode_245 = 245 [(epay_payout_code_value) = {
    message: "Start Date and End Date should be in format YYYY-MM-DD",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_246 = 246 [(epay_payout_code_value) = {
    message: "Не удалось получить данные из Истории. Проверьте корректность введенных данных.",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  NotemailCode_247 = 247 [(epay_payout_code_value) = {
    message: "Not email",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  JsonCode_248 = 248 [(epay_payout_code_value) = {
    message: "не смогли собрать json для отправки",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_249 = 249 [(epay_payout_code_value) = {
    message: "при отправке запроса регистрации мерчанта произошла ошибка",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  MsgValueCode_250 = 250 [(epay_payout_code_value) = {
    message: "Не удалось распарсить msg.Value",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_251 = 251 [(epay_payout_code_value) = {
    message: "Не удалось выполнить сохранение в БД",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_252 = 252 [(epay_payout_code_value) = {
    message: "Неизвестная ошибка консьюмера",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_263 = 263 [(epay_payout_code_value) = {
    message: "не смогли установить пароль",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_264 = 264 [(epay_payout_code_value) = {
    message: "Ошибка при получении данных",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_265 = 265 [(epay_payout_code_value) = {
    message: "Нет необходимых данных",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  SecretincorrecterrorCode_266 = 266 [(epay_payout_code_value) = {
    message: "Secret incorrect error",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  ScopenotinchangepassCode_267 = 267 [(epay_payout_code_value) = {
    message: "scope not in changepassword",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  UnsupportedscopeCode_268 = 268 [(epay_payout_code_value) = {
    message: "unsupported scope",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  BLOCKCode_269 = 269 [(epay_payout_code_value) = {
    message: "не смогли получить статус BLOCK",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  BLOCKCode_270 = 270 [(epay_payout_code_value) = {
    message: "не смогли получить статус BLOCK",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  UsernotsaveCode_271 = 271 [(epay_payout_code_value) = {
    message: "User not save",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  UsernotsaveCode_272 = 272 [(epay_payout_code_value) = {
    message: "User not save",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  FailedtogetstatusCode_273 = 273 [(epay_payout_code_value) = {
    message: "Failed to get status",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  UseralreadyexistCode_274 = 274 [(epay_payout_code_value) = {
    message: "User already exist",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  UseralreadyexistCode_275 = 275 [(epay_payout_code_value) = {
    message: "User already exist",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  IncorrectdataCode_276 = 276 [(epay_payout_code_value) = {
    message: "incorrect data",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  IncorrectdataCode_277 = 277 [(epay_payout_code_value) = {
    message: "incorrect data",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  NotemailCode_278 = 278 [(epay_payout_code_value) = {
    message: "not email",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  NotemailCode_279 = 279 [(epay_payout_code_value) = {
    message: "not email",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_280 = 280 [(epay_payout_code_value) = {
    message: "При создании транзакции произошла ошибка в БД",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  TransactioncreateerrCode_281 = 281 [(epay_payout_code_value) = {
    message: "Transaction create error",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_282 = 282 [(epay_payout_code_value) = {
    message: "При обновлении транзакции произошла ошибка в БД",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_283 = 283 [(epay_payout_code_value) = {
    message: "Дублирующая транзакция",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  TestMECode_285 = 285 [(epay_payout_code_value) = {
    message: "testME",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  StatementCode_286 = 286 [(epay_payout_code_value) = {
    message: "Ошибка в формировании statement",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  ErrorCheckTokenCode_287 = 287 [(epay_payout_code_value) = {
    message: "Error Check Token",
    transaction_status: 5,
    integration_error: InvalidCard,
  }];
  ErrorNoScopesCode_288 = 288 [(epay_payout_code_value) = {
    message: "Error No Scopes",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  TofromYYYYMMDDCode_289 = 289 [(epay_payout_code_value) = {
    message: "Параметр 'to' или 'from' не соответствует формату YYYY-MM-DD ",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_290 = 290 [(epay_payout_code_value) = {
    message: "Входные параметры отсутствуют или некорректны ",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  FromDateandToDateshoCode_291 = 291 [(epay_payout_code_value) = {
    message: "FromDate and ToDate should be in format dd.мм.yyyy",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  ERRORwhilecreatingreCode_292 = 292 [(epay_payout_code_value) = {
    message: "ERROR while creating records in database",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_293 = 293 [(epay_payout_code_value) = {
    message: "не смогли выполнить операцию",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  RequiredclientcredenCode_294 = 294 [(epay_payout_code_value) = {
    message: "required client credential",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  IDIDCode_295 = 295 [(epay_payout_code_value) = {
    message: "Данные по ID не найдены, для обновления данных необходимо ввести существующий ID графика",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  ErrorUpdateStaffCode_296 = 296 [(epay_payout_code_value) = {
    message: "Error Update Staff",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  StaffCode_297 = 297 [(epay_payout_code_value) = {
    message: "Ошибка при получении Staff",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_298 = 298 [(epay_payout_code_value) = {
    message: "Не удалось получить общее количество транзакций",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  StaffCode_299 = 299 [(epay_payout_code_value) = {
    message: "Ошибка при получении Staff",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  BindErrorCode_300 = 300 [(epay_payout_code_value) = {
    message: "Bind Error",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  UpdateStaffCode_301 = 301 [(epay_payout_code_value) = {
    message: "Ошибка при update Staff",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  MultipartFormCode_302 = 302 [(epay_payout_code_value) = {
    message: "Ошибка при разборе MultipartForm",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  ExpireAtYYYYMMDDCode_303 = 303 [(epay_payout_code_value) = {
    message: "Ошибка формата даты для ExpireAt. Валидный формат YYYY-MM-DD",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  ExpireAtYYYYMMDDCode_304 = 304 [(epay_payout_code_value) = {
    message: "Ошибка конвертации даты для ExpireAt. Валидный формат YYYY-MM-DD",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  ExpireAtCode_305 = 305 [(epay_payout_code_value) = {
    message: "Ошибка ExpireAt долженбыть больше текущей даты",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  ExpireAtCode_306 = 306 [(epay_payout_code_value) = {
    message: "Ошибка. Не все файлы имею дату ExpireAt",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_307 = 307 [(epay_payout_code_value) = {
    message: "Ошибка при сохранении файла на сервер.",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_308 = 308 [(epay_payout_code_value) = {
    message: "Ошибка при добавлении записи о файле в БД",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_309 = 309 [(epay_payout_code_value) = {
    message: "Ошибка при изменении записи о файле в БД",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  CommitErrorCode_310 = 310 [(epay_payout_code_value) = {
    message: "Commit Error",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  BindJSONErrorCode_311 = 311 [(epay_payout_code_value) = {
    message: "BindJSON Error",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_312 = 312 [(epay_payout_code_value) = {
    message: "Ошибка при поиске записи о файле в БД",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  SearchtransactionerrCode_318 = 318 [(epay_payout_code_value) = {
    message: "Search transaction error",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  JsonCode_319 = 319 [(epay_payout_code_value) = {
    message: "Ошибка при парсинге json",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_320 = 320 [(epay_payout_code_value) = {
    message: "Переданный диапазон дат неверный",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_321 = 321 [(epay_payout_code_value) = {
    message: "Ошибка при формировании данных",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_322 = 322 [(epay_payout_code_value) = {
    message: "Входные параметры отсутствуют или некорректны",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  FromdateYYYYMMDDCode_323 = 323 [(epay_payout_code_value) = {
    message: "Параметр 'fromdate' не соответствует формату YYYY-MM-DD",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  TodateYYYYMMDDCode_324 = 324 [(epay_payout_code_value) = {
    message: "Параметр 'todate' не соответствует формату YYYY-MM-DD",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  EPayCode_325 = 325 [(epay_payout_code_value) = {
    message: "Ошибка при получение данных из ePay",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  ErrorgetMerchantinfoCode_326 = 326 [(epay_payout_code_value) = {
    message: "Error get Merchant info",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_327 = 327 [(epay_payout_code_value) = {
    message: "Не заполнены данные платежа в токене ",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_328 = 328 [(epay_payout_code_value) = {
    message: "Запрещено для пользовательской авторизации",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  InvoiceIDCode_329 = 329 [(epay_payout_code_value) = {
    message: "InvoiceID не совпадает в токене и запросе",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  TerminalCode_330 = 330 [(epay_payout_code_value) = {
    message: "Terminal не совпадает в токене и запросе",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  CurrencyCode_331 = 331 [(epay_payout_code_value) = {
    message: "Currency не совпадает в токене и запросе",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  AmountCode_332 = 332 [(epay_payout_code_value) = {
    message: "Amount не совпадает в токене и запросе",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_333 = 333 [(epay_payout_code_value) = {
    message: "Не удалось определить терминал комерсанта",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  ShopIDCode_334 = 334 [(epay_payout_code_value) = {
    message: "Не удалось получить  график выписок по Shop ID",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  MerchantIDCode_335 = 335 [(epay_payout_code_value) = {
    message: "Не найден merchantID по токену",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  ShopIDCode_336 = 336 [(epay_payout_code_value) = {
    message: "Не удалось получить ShopID по Номеру контракта",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  ErrorgetMerchantinfoCode_337 = 337 [(epay_payout_code_value) = {
    message: "Error get Merchant info",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_338 = 338 [(epay_payout_code_value) = {
    message: "Ошибка при получении списка магазинов",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_339 = 339 [(epay_payout_code_value) = {
    message: "Ошибка при получении списка терминалов",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_340 = 340 [(epay_payout_code_value) = {
    message: "Ошибка при получении расписаний по магазину",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  ContractCode_342 = 342 [(epay_payout_code_value) = {
    message: "не заполнен параметр contract",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  GetShopByContractCode_343 = 343 [(epay_payout_code_value) = {
    message: "при выполнении GetShopByContract произошла ошибка",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_344 = 344 [(epay_payout_code_value) = {
    message: "ошибка при удалении расписания",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  MarshalerrorCode_345 = 345 [(epay_payout_code_value) = {
    message: "Marshal error",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_346 = 346 [(epay_payout_code_value) = {
    message: "Ошибка при поиске данных по магазину",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_347 = 347 [(epay_payout_code_value) = {
    message: "Ошибка при формировании данных по транзакциям за текущий день",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_348 = 348 [(epay_payout_code_value) = {
    message: "Ошибка при формировании данных по транзакциям по картам за текущий день",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_349 = 349 [(epay_payout_code_value) = {
    message: "Ошибка при формировании данных по транзакциям за период",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_350 = 350 [(epay_payout_code_value) = {
    message: "Ошибка при формировании данных по транзакциям по картам за период",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_351 = 351 [(epay_payout_code_value) = {
    message: "Ошибка при получении транзакций за период",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_352 = 352 [(epay_payout_code_value) = {
    message: "Ошибка при получении транзакций по картам за текущий день",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  TopCode_353 = 353 [(epay_payout_code_value) = {
    message: "Передано значение параметра 'top' вне диапазона",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_354 = 354 [(epay_payout_code_value) = {
    message: "не верный формат даты",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  EmailCode_355 = 355 [(epay_payout_code_value) = {
    message: "не верный формат email-а",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_356 = 356 [(epay_payout_code_value) = {
    message: "не верный входные данные",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_357 = 357 [(epay_payout_code_value) = {
    message: "не смогли получить справочник валют",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_358 = 358 [(epay_payout_code_value) = {
    message: "не смогли получить справочник статусов",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_359 = 359 [(epay_payout_code_value) = {
    message: "не смогли получить справочник рассрочек",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_360 = 360 [(epay_payout_code_value) = {
    message: "не смогли получить справочник периодов выписик",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_361 = 361 [(epay_payout_code_value) = {
    message: "не смогли получить справочник форматов выписик",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_362 = 362 [(epay_payout_code_value) = {
    message: "не смогли получить справочник магазинов",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  ParseerrorCode_363 = 363 [(epay_payout_code_value) = {
    message: "parse error",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  ParseerrorCode_364 = 364 [(epay_payout_code_value) = {
    message: "parse error",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_365 = 365 [(epay_payout_code_value) = {
    message: "система статистики ответила ошибкой",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  ParseerrorCode_366 = 366 [(epay_payout_code_value) = {
    message: "parse error",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  ParseerrorCode_367 = 367 [(epay_payout_code_value) = {
    message: "parse error",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_368 = 368 [(epay_payout_code_value) = {
    message: "система статистики ответила ошибкой",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  ParseerrorCode_369 = 369 [(epay_payout_code_value) = {
    message: "parse error",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  ParseerrorCode_370 = 370 [(epay_payout_code_value) = {
    message: "parse error",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_371 = 371 [(epay_payout_code_value) = {
    message: "система статистики ответила ошибкой",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_372 = 372 [(epay_payout_code_value) = {
    message: "не верный ответ от приложения выписок",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  NoemailCode_373 = 373 [(epay_payout_code_value) = {
    message: "no email",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_374 = 374 [(epay_payout_code_value) = {
    message: "при формировании выписки произошла ошибка",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_375 = 375 [(epay_payout_code_value) = {
    message: "при формировании выписки произошла ошибка",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_376 = 376 [(epay_payout_code_value) = {
    message: "при формировании выписки произошла ошибка",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  IncorrectformatdateCode_377 = 377 [(epay_payout_code_value) = {
    message: "incorrect format date",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  GetFileFiledoesnotexCode_378 = 378 [(epay_payout_code_value) = {
    message: "GetFile: File does not exists.",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_379 = 379 [(epay_payout_code_value) = {
    message: "Не определили терминал комерсанта",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_380 = 380 [(epay_payout_code_value) = {
    message: "При обновлении транзакции произошла ошибка в БД",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_381 = 381 [(epay_payout_code_value) = {
    message: "При обновлении транзакции произошла ошибка в БД",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_382 = 382 [(epay_payout_code_value) = {
    message: "При обновлении транзакции произошла ошибка в БД",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_383 = 383 [(epay_payout_code_value) = {
    message: "При обновлении транзакции произошла ошибка в БД",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  EgatewayCode_384 = 384 [(epay_payout_code_value) = {
    message: "При вызове egateway произошла ошибка",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  EgatewayCode_385 = 385 [(epay_payout_code_value) = {
    message: "При вызове egateway произошла ошибка",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  EgatewayCode_386 = 386 [(epay_payout_code_value) = {
    message: "При вызове egateway произошла ошибка",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  EgatewayCode_387 = 387 [(epay_payout_code_value) = {
    message: "При вызове egateway произошла ошибка",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  EgatewayCode_388 = 388 [(epay_payout_code_value) = {
    message: "При вызове egateway произошла ошибка",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  UserdeleteCode_389 = 389 [(epay_payout_code_value) = {
    message: "user delete",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  ParseerrorCode_390 = 390 [(epay_payout_code_value) = {
    message: "parse error",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  CallbacksendmessageeCode_391 = 391 [(epay_payout_code_value) = {
    message: "callback send message error",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_392 = 392 [(epay_payout_code_value) = {
    message: "Не удалось получить информацию по коммерсанту",
    transaction_status: 5,
    integration_error: PaymentForbiddenForMerchant,
  }];
  Code_393 = 393 [(epay_payout_code_value) = {
    message: "Не удалось получить информацию по магазину",
    transaction_status: 5,
    integration_error: PaymentForbiddenForMerchant,
  }];
  Code_394 = 394 [(epay_payout_code_value) = {
    message: "При отправке письма о успешной операции произошла ошибка",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_395 = 395 [(epay_payout_code_value) = {
    message: "При получении токена произошла ошибка",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_396 = 396 [(epay_payout_code_value) = {
    message: "При получении контактов комерсанта произошла ошибка",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  MVisaCode_397 = 397 [(epay_payout_code_value) = {
    message: "MVisa при получении токена произошла ошибка, проверьте данные авторизации",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  ThreeDSecureCode_398 = 398 [(epay_payout_code_value) = {
  message: "ввод 3DSecure пароля",
      transaction_status: 5,
      integration_error: ThreeDSAuthFailed,
      }];
  MVisaverificationCode_399 = 399 [(epay_payout_code_value) = {
    message: "MVisa система verification ответила ошибкой",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_400 = 400 [(epay_payout_code_value) = {
    message: "При обновлении транзакции произошла ошибка в БД",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_401 = 401 [(epay_payout_code_value) = {
    message: "При обновлении транзакции произошла ошибка в БД",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_402 = 402 [(epay_payout_code_value) = {
    message: "При обновлении транзакции произошла ошибка в БД",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  CarddataisnotrequireCode_403 = 403 [(epay_payout_code_value) = {
    message: "card data is not required",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_404 = 404 [(epay_payout_code_value) = {
    message: "ошибка входящего сообщения",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_405 = 405 [(epay_payout_code_value) = {
    message: "ошибка при разборе криптограммы",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_406 = 406 [(epay_payout_code_value) = {
    message: "не верно заполненна криптограмма",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_407 = 407 [(epay_payout_code_value) = {
    message: "не удалось сформировать запрос в карточную систему",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_408 = 408 [(epay_payout_code_value) = {
    message: "ошибка при получении токена",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_409 = 409 [(epay_payout_code_value) = {
    message: "ошибка при повторном получении токена",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  MVISACode_410 = 410 [(epay_payout_code_value) = {
    message: "MVISA при проверке верификации произошла ошибка",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  NoAuthorizeCode_411 = 411 [(epay_payout_code_value) = {
    message: "No Authorize",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  BadtokenCode_412 = 412 [(epay_payout_code_value) = {
    message: "Bad token",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_413 = 413 [(epay_payout_code_value) = {
    message: "Ошибка карточной системы",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  TimeouttransactionCode_504 = 504 [(epay_payout_code_value) = {
    message: "Timeout transaction",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  ExceededattemptsCode_505 = 505 [(epay_payout_code_value) = {
    message: "exceeded attempts",
    transaction_status: 5,
    integration_error: ExceedsTransactionFrequencyLimit,
  }];
  Code_506 = 506 [(epay_payout_code_value) = {
    message: "Операцию отмены можно сделать только на следующий день",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_507 = 507 [(epay_payout_code_value) = {
    message: "При получении информации о комерсанте произошла ошибка",
    transaction_status: 5,
    integration_error: PaymentForbiddenForMerchant,
  }];
  Code_508 = 508 [(epay_payout_code_value) = {
    message: "При сохранении в БД произошла ошибка",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_509 = 509 [(epay_payout_code_value) = {
    message: "При получении информации о банке произошла ошибка",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  RabbitCode_510 = 510 [(epay_payout_code_value) = {
    message: "При отпраке в rabbit произошла ошибка",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_512 = 512 [(epay_payout_code_value) = {
    message: "при подтверждении произошла ошибка",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  PostlinkCode_513 = 513 [(epay_payout_code_value) = {
    message: "При создании postlink произошла ошибка",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_514 = 514 [(epay_payout_code_value) = {
    message: "Пустой инвойс",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_515 = 515 [(epay_payout_code_value) = {
    message: "при поиске коммерсанта произошла ошибка",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_516 = 516 [(epay_payout_code_value) = {
    message: "при поиске транзакции произошла ошибка",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  TherewasnoattempttopCode_517 = 517 [(epay_payout_code_value) = {
    message: "there was no attempt to pay",
    transaction_status: 5,
    integration_error: ExceedsTransactionFrequencyLimit,
  }];
  IpCode_518 = 518 [(epay_payout_code_value) = {
    message: "при получении информации по ip адресу произошла ошибка",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  InvoiceIDCode_520 = 520 [(epay_payout_code_value) = {
    message: "Длина invoiceID не соответствует правилам",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  TerminalIDCode_511 = 511 [(epay_payout_code_value) = {
    message: "Пустой terminalID",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  BinCode_522 = 522 [(epay_payout_code_value) = {
    message: "При получении списка bin-ов произошла ошибка",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  EGWCode_546 = 546 [(epay_payout_code_value) = {
    message: "При вызове EGW произошла ошибка",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  EGWHTTPCODE200Code_547 = 547 [(epay_payout_code_value) = {
    message: "При вызове EGW HTTP CODE не равен 200",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_559 = 559 [(epay_payout_code_value) = {
    message: "При поиске транзакции произошла ошибка",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  EGWCode_560 = 560 [(epay_payout_code_value) = {
    message: "При вызове EGW произошла ошибка",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  EGWHTTPCODE200Code_561 = 561 [(epay_payout_code_value) = {
    message: "При вызове EGW HTTP CODE не равен 200",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  MDCode_529 = 529 [(epay_payout_code_value) = {
    message: "Поиск по MD вернул ошибку",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  EGWCode_530 = 530 [(epay_payout_code_value) = {
    message: "При вызове EGW произошла ошибка",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_570 = 570 [(epay_payout_code_value) = {
    message: "При поиске транзакции произошла ошибка",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  EGWCode_571 = 571 [(epay_payout_code_value) = {
    message: "При вызове EGW произошла ошибка",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  EGWHTTPCODE200Code_533 = 533 [(epay_payout_code_value) = {
    message: "При вызове EGW HTTP CODE не равен 200	",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_534 = 534 [(epay_payout_code_value) = {
    message: "При получении терминала произошла ошибка",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_535 = 535 [(epay_payout_code_value) = {
    message: "При получении терминала произошла ошибка",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  MerchantCode_536 = 536 [(epay_payout_code_value) = {
    message: "При получении merchant-а произошла ошибка",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  MerchantIsActivefalsCode_537 = 537 [(epay_payout_code_value) = {
    message: "Merchant.IsActive = false",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_538 = 538 [(epay_payout_code_value) = {
    message: "При получении терминала произошла ошибка",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  MerchantCode_539 = 539 [(epay_payout_code_value) = {
    message: "При получении merchant-а произошла ошибка",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  ScopenotfoundcheckscCode_540 = 540 [(epay_payout_code_value) = {
    message: "Scope not found, check scope",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  ErrorparameternameCode_541 = 541 [(epay_payout_code_value) = {
    message: "Error parameter name",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  UnauthorizedChecktokCode_542 = 542 [(epay_payout_code_value) = {
    message: "Unauthorized. Check token.",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  ErrorInvalidScopeCode_543 = 543 [(epay_payout_code_value) = {
    message: "Error Invalid Scope",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  ErrorBindCode_544 = 544 [(epay_payout_code_value) = {
    message: "Error Bind",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  UnkwonErrorCode_548 = 548 [(epay_payout_code_value) = {
    message: "Unkwon Error",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  TransactionalreadycaCode_549 = 549 [(epay_payout_code_value) = {
    message: "Transaction already canceled",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  RequesttoXLSServerfaCode_550 = 550 [(epay_payout_code_value) = {
    message: "Request to XLS Server failed",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  RequesttoXLSServerreCode_551 = 551 [(epay_payout_code_value) = {
    message: "Request to XLS Server returned empty body",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  InvalidbonusamountCode_552 = 552 [(epay_payout_code_value) = {
    message: "Invalid bonus amount",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  InvalidinputdataCode_553 = 553 [(epay_payout_code_value) = {
    message: "Invalid input data",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  TerminalconfiguratioCode_554 = 554 [(epay_payout_code_value) = {
    message: "Terminal configuration error",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  UnabletogetGETrequesCode_555 = 555 [(epay_payout_code_value) = {
    message: "Unable to get GET request by Querry Parameters",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  RequesttoapixlsfaileCode_556 = 556 [(epay_payout_code_value) = {
    message: "Request to api.xls failed",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  RequesttoapixlsreturCode_557 = 557 [(epay_payout_code_value) = {
    message: "Request to api.xls returned empty body",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  GobonusconvertiontofCode_558 = 558 [(epay_payout_code_value) = {
    message: "Go bonus convertion to float failed",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_563 = 563 [(epay_payout_code_value) = {
    message: "При получении информации о терминале произошла ошибка",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  TerminalIDCode_564 = 564 [(epay_payout_code_value) = {
    message: "нет terminalID",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  RequesttogetcardinfoCode_565 = 565 [(epay_payout_code_value) = {
    message: "Request to get card information failed",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  RequesttogetcardinfoCode_566 = 566 [(epay_payout_code_value) = {
    message: "Request to get card information returned empty body",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  CardnotfoundCode_567 = 567 [(epay_payout_code_value) = {
    message: "Card not found",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  CardownernotfoundCode_568 = 568 [(epay_payout_code_value) = {
    message: "Card owner not found",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_569 = 569 [(epay_payout_code_value) = {
    message: "При подтверждении доставки произошла ошибка.",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_572 = 572 [(epay_payout_code_value) = {
    message: "Не удалось спарсить входящую транзакцию.",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  RequesttoapixlsfaileCode_573 = 573 [(epay_payout_code_value) = {
    message: "Request to api.xls failed",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  RequesttoapixlsreturCode_574 = 574 [(epay_payout_code_value) = {
    message: "Request to api.xls returned empty body",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  UnauthorizedChecktokCode_576 = 576 [(epay_payout_code_value) = {
    message: "Unauthorized. Check token.",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  CannotcancelxlstransCode_578 = 578 [(epay_payout_code_value) = {
    message: "Cannot cancel xls transaction",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  CannotsendtransactioCode_579 = 579 [(epay_payout_code_value) = {
    message: "Cannot send transaction to rabbit",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  CannotsendcancelxlstCode_580 = 580 [(epay_payout_code_value) = {
    message: "Cannot send cancel xls transaction to finalizer",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  CannotcreaterabbitinCode_581 = 581 [(epay_payout_code_value) = {
    message: "Cannot create rabbit in order to send transaction to finalizer",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  ChangePaymentCode_582 = 582 [(epay_payout_code_value) = {
    message: "не удалось выполнить ChangePayment",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_583 = 583 [(epay_payout_code_value) = {
    message: "Не удалось начислить бонусы за транзакцию во время возврата",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  XlsCode_584 = 584 [(epay_payout_code_value) = {
    message: "Не получилось отменить транзакцию в системе xls",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  GoCode_585 = 585 [(epay_payout_code_value) = {
    message: "Не достаточно Go бонусов для частичного возврата",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_586 = 586 [(epay_payout_code_value) = {
    message: "Не удалось отменить бонусы за транзакцию во время возврата",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_587 = 587 [(epay_payout_code_value) = {
    message: "Не удалось списать бонусы во время возврата",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_588 = 588 [(epay_payout_code_value) = {
    message: "Не удалось вернуть списанные бонусы во время возврата",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_589 = 589 [(epay_payout_code_value) = {
    message: "Не удалось списать возвращенные бонусы во время возврата",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_590 = 590 [(epay_payout_code_value) = {
    message: "Не удалось вернуть списанные бонусы во время платежа",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_591 = 591 [(epay_payout_code_value) = {
    message: "Не удалось начислить бонусы за транзакию во время платежа",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_592 = 592 [(epay_payout_code_value) = {
    message: "Не удалось вернуть списанные бонусы во время отмены транзакции",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_593 = 593 [(epay_payout_code_value) = {
    message: "Не удалось списать начисленные бонусы во время отмены транзакции",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_594 = 594 [(epay_payout_code_value) = {
    message: "Сумма возврата не должна превышать сумму оплаты",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_595 = 595 [(epay_payout_code_value) = {
    message: "Неправильная сумма возврата",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Epay1documentCode_596 = 596 [(epay_payout_code_value) = {
    message: "Ошибка при парсинге epay1.document",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  CrtificateIDCode_597 = 597 [(epay_payout_code_value) = {
    message: "Не удалось найти клиента с таким crtificate ID",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_598 = 598 [(epay_payout_code_value) = {
    message: "Не смогли распарсить входящий запрос",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_599 = 599 [(epay_payout_code_value) = {
    message: "Не смогли распарсить входящий запрос",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_600 = 600 [(epay_payout_code_value) = {
    message: "Не смогли распарсить входящий запрос",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_601 = 601 [(epay_payout_code_value) = {
    message: "Платежный адаптер вернул ошибку",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_602 = 602 [(epay_payout_code_value) = {
    message: "Платежный адаптер вернул ошибку",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_603 = 603 [(epay_payout_code_value) = {
    message: "При создании транзакции произошла ошибка в БД",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_604 = 604 [(epay_payout_code_value) = {
    message: "При создании транзакции произошла ошибка в БД",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_605 = 605 [(epay_payout_code_value) = {
    message: "При создании транзакции произошла ошибка в БД",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_606 = 606 [(epay_payout_code_value) = {
    message: "При создании транзакции произошла ошибка в БД",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_607 = 607 [(epay_payout_code_value) = {
    message: "При создании транзакции произошла ошибка в БД",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_608 = 608 [(epay_payout_code_value) = {
    message: "При обновлении транзакции произошла ошибка в БД",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_609 = 609 [(epay_payout_code_value) = {
    message: "При обновлении транзакции произошла ошибка в БД",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_610 = 610 [(epay_payout_code_value) = {
    message: "Дублирующая транзакция",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_611 = 611 [(epay_payout_code_value) = {
    message: "При обращении к БД произошла ошибка",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_612 = 612 [(epay_payout_code_value) = {
    message: "При обращении к БД произошла ошибка",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_613 = 613 [(epay_payout_code_value) = {
    message: "Не заполнены данные платежа в токене",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_614 = 614 [(epay_payout_code_value) = {
    message: "Не заполнены данные платежа в токене",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_615 = 615 [(epay_payout_code_value) = {
    message: "Запрещено для пользовательской авторизации",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_616 = 616 [(epay_payout_code_value) = {
    message: "Запрещено для пользовательской авторизации",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  InvoiceIDCode_617 = 617 [(epay_payout_code_value) = {
    message: "InvoiceID не совпадает в токене и запросе",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  TerminalCode_618 = 618 [(epay_payout_code_value) = {
    message: "Terminal не совпадает в токене и запросе",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  TerminalCode_619 = 619 [(epay_payout_code_value) = {
    message: "Terminal не совпадает в токене и запросе",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  CurrencyCode_620 = 620 [(epay_payout_code_value) = {
    message: "Currency не совпадает в токене и запросе",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  AmountCode_621 = 621 [(epay_payout_code_value) = {
    message: "Amount не совпадает в токене и запросе",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  AmountCode_622 = 622 [(epay_payout_code_value) = {
    message: "Amount не совпадает в токене и запросе",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  AmountCode_623 = 623 [(epay_payout_code_value) = {
    message: "Amount не совпадает в токене и запросе",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_624 = 624 [(epay_payout_code_value) = {
    message: "При обновлении транзакции произошла ошибка в БД",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_625 = 625 [(epay_payout_code_value) = {
    message: "При обновлении транзакции произошла ошибка в БД",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_626 = 626 [(epay_payout_code_value) = {
    message: "При обновлении транзакции произошла ошибка в БД",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_627 = 627 [(epay_payout_code_value) = {
    message: "При обновлении транзакции произошла ошибка в БД",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_628 = 628 [(epay_payout_code_value) = {
    message: "При обновлении транзакции произошла ошибка в БД",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_629 = 629 [(epay_payout_code_value) = {
    message: "При обновлении транзакции произошла ошибка в БД",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_630 = 630 [(epay_payout_code_value) = {
    message: "При обновлении транзакции произошла ошибка в БД",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_631 = 631 [(epay_payout_code_value) = {
    message: "При обновлении транзакции произошла ошибка в БД",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_632 = 632 [(epay_payout_code_value) = {
    message: "При обновлении транзакции произошла ошибка в БД",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_633 = 633 [(epay_payout_code_value) = {
    message: "При обновлении транзакции произошла ошибка в БД",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_634 = 634 [(epay_payout_code_value) = {
    message: "При обновлении транзакции произошла ошибка в БД",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  EgatewayCode_635 = 635 [(epay_payout_code_value) = {
    message: "При вызове egateway произошла ошибка",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  EgatewayCode_636 = 636 [(epay_payout_code_value) = {
    message: "При вызове egateway произошла ошибка",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  EgatewayCode_637 = 637 [(epay_payout_code_value) = {
    message: "При вызове egateway произошла ошибка",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_638 = 638 [(epay_payout_code_value) = {
    message: "Не смогли распарсить входящий запрос",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_639 = 639 [(epay_payout_code_value) = {
    message: "Не смогли распарсить входящий запрос",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_640 = 640 [(epay_payout_code_value) = {
    message: "Не смогли распарсить входящий запрос",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_641 = 641 [(epay_payout_code_value) = {
    message: "Не смогли распарсить входящий запрос",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_642 = 642 [(epay_payout_code_value) = {
    message: "Не смогли распарсить входящий запрос",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  CallbacksendmessageeCode_643 = 643 [(epay_payout_code_value) = {
    message: "callback send message error",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_644 = 644 [(epay_payout_code_value) = {
    message: "Не удалось получить информацию по коммерсанту",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_645 = 645 [(epay_payout_code_value) = {
    message: "Не удалось получить информацию по коммерсанту",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_646 = 646 [(epay_payout_code_value) = {
    message: "Не удалось получить информацию по магазину",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_647 = 647 [(epay_payout_code_value) = {
    message: "Не удалось получить информацию по магазину",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_648 = 648 [(epay_payout_code_value) = {
    message: "При отправке письма о успешной операции произошла ошибка",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_649 = 649 [(epay_payout_code_value) = {
    message: "При отправке письма о успешной операции произошла ошибка",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_650 = 650 [(epay_payout_code_value) = {
    message: "При получении токена произошла ошибка",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_651 = 651 [(epay_payout_code_value) = {
    message: "При получении токена произошла ошибка",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_652 = 652 [(epay_payout_code_value) = {
    message: "При получении контактов комерсанта произошла ошибка",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_653 = 653 [(epay_payout_code_value) = {
    message: "При получении контактов комерсанта произошла ошибка",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_654 = 654 [(epay_payout_code_value) = {
    message: "При обновлении транзакции произошла ошибка в БД",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_655 = 655 [(epay_payout_code_value) = {
    message: "При обновлении транзакции произошла ошибка в БД",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_656 = 656 [(epay_payout_code_value) = {
    message: "При обновлении транзакции произошла ошибка в БД",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_657 = 657 [(epay_payout_code_value) = {
    message: "При обновлении транзакции произошла ошибка в БД",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_658 = 658 [(epay_payout_code_value) = {
    message: "При обновлении транзакции произошла ошибка в БД",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_659 = 659 [(epay_payout_code_value) = {
    message: "При обновлении транзакции произошла ошибка в БД",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_660 = 660 [(epay_payout_code_value) = {
    message: "Ошибка карточной системы",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_661 = 661 [(epay_payout_code_value) = {
    message: "Ошибка карточной системы",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_662 = 662 [(epay_payout_code_value) = {
    message: "При сохранении в БД произошла ошибка",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_663 = 663 [(epay_payout_code_value) = {
    message: "При сохранении в БД произошла ошибка",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_664 = 664 [(epay_payout_code_value) = {
    message: "При сохранении в БД произошла ошибка",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_665 = 665 [(epay_payout_code_value) = {
    message: "При сохранении в БД произошла ошибка",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_666 = 666 [(epay_payout_code_value) = {
    message: "При сохранении в БД произошла ошибка",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_667 = 667 [(epay_payout_code_value) = {
    message: "При сохранении в БД произошла ошибка",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_668 = 668 [(epay_payout_code_value) = {
    message: "При сохранении в БД произошла ошибка",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_669 = 669 [(epay_payout_code_value) = {
    message: "При сохранении в БД произошла ошибка",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_670 = 670 [(epay_payout_code_value) = {
    message: "При сохранении в БД произошла ошибка",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_671 = 671 [(epay_payout_code_value) = {
    message: "При сохранении в БД произошла ошибка",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_672 = 672 [(epay_payout_code_value) = {
    message: "При сохранении в БД произошла ошибка",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_673 = 673 [(epay_payout_code_value) = {
    message: "При сохранении в БД произошла ошибка",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  EGWHTTPCODE200Code_674 = 674 [(epay_payout_code_value) = {
    message: "При вызове EGW HTTP CODE не равен 200",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_675 = 675 [(epay_payout_code_value) = {
    message: "При получении терминала произошла ошибка",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_676 = 676 [(epay_payout_code_value) = {
    message: "При получении терминала произошла ошибка",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_677 = 677 [(epay_payout_code_value) = {
    message: "При получении терминала произошла ошибка",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_678 = 678 [(epay_payout_code_value) = {
    message: "При получении терминала произошла ошибка",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_679 = 679 [(epay_payout_code_value) = {
    message: "При получении терминала произошла ошибка",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  RequesttogetcardinfoCode_680 = 680 [(epay_payout_code_value) = {
    message: "Request to get card information failed",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  RequesttogetcardinfoCode_681 = 681 [(epay_payout_code_value) = {
    message: "Request to get card information returned empty body",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  XlsCode_683 = 683 [(epay_payout_code_value) = {
    message: "Не получилось отменить транзакцию в системе xls",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  XlsCode_684 = 684 [(epay_payout_code_value) = {
    message: "Не получилось отменить транзакцию в системе xls",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_685 = 685 [(epay_payout_code_value) = {
    message: "Не удалось вернуть списанные бонусы во время возврата",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  TerminalconfiguratioCode_686 = 686 [(epay_payout_code_value) = {
    message: "Terminal configuration error",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  RequesttoXLSServerfaCode_687 = 687 [(epay_payout_code_value) = {
    message: "Request to XLS Server failed",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  RequesttoXLSServerfaCode_688 = 688 [(epay_payout_code_value) = {
    message: "Request to XLS Server failed",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_689 = 689 [(epay_payout_code_value) = {
    message: "не смогли получить операции коммерсанта, запросите статус транзакции",
    transaction_status: 11,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_690 = 690 [(epay_payout_code_value) = {
    message: "не смогли получить операции коммерсанта, запросите статус транзакции",
    transaction_status: 11,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_691 = 691 [(epay_payout_code_value) = {
    message: "не смогли получить операции коммерсанта, запросите статус транзакции",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  JsonCode_692 = 692 [(epay_payout_code_value) = {
    message: "не смогли собрать json для отправки",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_693 = 693 [(epay_payout_code_value) = {
    message: "при отправке запроса регистрации мерчанта произошла ошибка",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_694 = 694 [(epay_payout_code_value) = {
    message: "не смогли установить пароль",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  StatementCode_695 = 695 [(epay_payout_code_value) = {
    message: "Ошибка в формировании statement",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  StatementCode_696 = 696 [(epay_payout_code_value) = {
    message: "Ошибка в формировании statement",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  StatementCode_697 = 697 [(epay_payout_code_value) = {
    message: "Ошибка в формировании statement",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  StatementCode_698 = 698 [(epay_payout_code_value) = {
    message: "Ошибка в формировании statement",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  StatementCode_699 = 699 [(epay_payout_code_value) = {
    message: "Ошибка в формировании statement",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  TofromYYYYMMDDCode_700 = 700 [(epay_payout_code_value) = {
    message: "Параметр 'to' или 'from' не соответствует формату YYYY-MM-DD",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  TofromYYYYMMDDCode_701 = 701 [(epay_payout_code_value) = {
    message: "Параметр 'to' или 'from' не соответствует формату YYYY-MM-DD",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  TofromYYYYMMDDCode_702 = 702 [(epay_payout_code_value) = {
    message: "Параметр 'to' или 'from' не соответствует формату YYYY-MM-DD",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_703 = 703 [(epay_payout_code_value) = {
    message: "не смогли выполнить операцию",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_704 = 704 [(epay_payout_code_value) = {
    message: "не смогли выполнить операцию",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_705 = 705 [(epay_payout_code_value) = {
    message: "не смогли выполнить операцию",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_706 = 706 [(epay_payout_code_value) = {
    message: "не смогли выполнить операцию",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_707 = 707 [(epay_payout_code_value) = {
    message: "не смогли выполнить операцию",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  StaffCode_708 = 708 [(epay_payout_code_value) = {
    message: "Ошибка при получении Staff",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  StaffCode_709 = 709 [(epay_payout_code_value) = {
    message: "Ошибка при получении Staff",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  StaffCode_710 = 710 [(epay_payout_code_value) = {
    message: "Ошибка при получении Staff",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_711 = 711 [(epay_payout_code_value) = {
    message: "Ошибка при получении списка магазинов",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_712 = 712 [(epay_payout_code_value) = {
    message: "Ошибка при получении списка магазинов",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_713 = 713 [(epay_payout_code_value) = {
    message: "Ошибка при получении расписаний по магазину",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_714 = 714 [(epay_payout_code_value) = {
    message: "При обновлении транзакции произошла ошибка в БД",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_715 = 715 [(epay_payout_code_value) = {
    message: "При обновлении транзакции произошла ошибка в БД",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_716 = 716 [(epay_payout_code_value) = {
    message: "ошибка входящего сообщения",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_717 = 717 [(epay_payout_code_value) = {
    message: "ошибка при разборе криптограммы",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_718 = 718 [(epay_payout_code_value) = {
    message: "не верно заполненна криптограмма",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_719 = 719 [(epay_payout_code_value) = {
    message: "не удалось сформировать запрос в карточную систему",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  ErrorinservicingthecCode_720 = 720 [(epay_payout_code_value) = {
    message: "Error in servicing the card. Be sure to enter the card number correctly. If the error persists, contact support.",
    transaction_status: 5,
    integration_error: IncorrectCardNumber,
  }];
  ErrorinservicingthecCode_721 = 721 [(epay_payout_code_value) = {
    message: "Error in servicing the card. Be sure to enter the card number correctly. If the error persists, contact support.",
    transaction_status: 5,
    integration_error: IncorrectCardNumber,
  }];
  ErrorinservicingthecCode_722 = 722 [(epay_payout_code_value) = {
    message: "Error in servicing the card. Be sure to enter the card number correctly. If the error persists, contact support.",
    transaction_status: 5,
    integration_error: IncorrectCardNumber,
  }];
  ErrorinservicingthecCode_723 = 723 [(epay_payout_code_value) = {
    message: "Error in servicing the card. Be sure to enter the card number correctly. If the error persists, contact support.",
    transaction_status: 5,
    integration_error: IncorrectCardNumber,
  }];
  ErrorinservicingthecCode_724 = 724 [(epay_payout_code_value) = {
    message: "Error in servicing the card. Be sure to enter the card number correctly. If the error persists, contact support.",
    transaction_status: 5,
    integration_error: IncorrectCardNumber,
  }];
  ErrorinservicingthecCode_725 = 725 [(epay_payout_code_value) = {
    message: "Error in servicing the card. Be sure to enter the card number correctly. If the error persists, contact support.",
    transaction_status: 5,
    integration_error: IncorrectCardNumber,
  }];
  ErrorinservicingthecCode_726 = 726 [(epay_payout_code_value) = {
    message: "Error in servicing the card. Be sure to enter the card number correctly. If the error persists, contact support.",
    transaction_status: 5,
    integration_error: IncorrectCardNumber,
  }];
  ErrorinservicingthecCode_727 = 727 [(epay_payout_code_value) = {
    message: "Error in servicing the card. Be sure to enter the card number correctly. If the error persists, contact support.",
    transaction_status: 5,
    integration_error: IncorrectCardNumber,
  }];
  NoAuthorizeCode_728 = 728 [(epay_payout_code_value) = {
    message: "No Authorize",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  BadtokenCode_729 = 729 [(epay_payout_code_value) = {
    message: "Bad token	",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_730 = 730 [(epay_payout_code_value) = {
    message: "Не смогли распарсить входящий запрос",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  InvalidrequestinputCode_731 = 731 [(epay_payout_code_value) = {
    message: "Invalid request input",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_732 = 732 [(epay_payout_code_value) = {
    message: "При создании подписки произошла ошибка в БД",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_733 = 733 [(epay_payout_code_value) = {
    message: "При сохранении в БД произошла ошибка",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_734 = 734 [(epay_payout_code_value) = {
    message: "При сохранении в БД произошла ошибка",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_735 = 735 [(epay_payout_code_value) = {
    message: "При создании планового платежа произошла ошибка в БД",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_736 = 736 [(epay_payout_code_value) = {
    message: "Не смогли распарсить входящий запрос",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  IdCode_737 = 737 [(epay_payout_code_value) = {
    message: "Не удалось найти подписку с запрошенным id",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_738 = 738 [(epay_payout_code_value) = {
    message: "При обращении к БД произошла ошибка",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_739 = 739 [(epay_payout_code_value) = {
    message: "При обращении к БД произошла ошибка",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_740 = 740 [(epay_payout_code_value) = {
    message: "При сохранении в БД произошла ошибка",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_741 = 741 [(epay_payout_code_value) = {
    message: "При сохранении в БД произошла ошибка",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_742 = 742 [(epay_payout_code_value) = {
    message: "Подписка не активна",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_743 = 743 [(epay_payout_code_value) = {
    message: "Не смогли распарсить входящий запрос",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_744 = 744 [(epay_payout_code_value) = {
    message: "При обращении к БД произошла ошибка",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_745 = 745 [(epay_payout_code_value) = {
    message: "Не смогли распарсить входящий запрос",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_746 = 746 [(epay_payout_code_value) = {
    message: "При обращении к БД произошла ошибка",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_747 = 747 [(epay_payout_code_value) = {
    message: "При сохранении в БД произошла ошибка",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_748 = 748 [(epay_payout_code_value) = {
    message: "При сохранении в БД произошла ошибка",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_749 = 749 [(epay_payout_code_value) = {
    message: "При получении токена произошла ошибка",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_750 = 750 [(epay_payout_code_value) = {
    message: "При обращении к БД произошла ошибка",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_751 = 751 [(epay_payout_code_value) = {
    message: "При получении токена произошла ошибка",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_752 = 752 [(epay_payout_code_value) = {
    message: "При получении токена произошла ошибка",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_753 = 753 [(epay_payout_code_value) = {
    message: "При получении токена произошла ошибка",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  ApimakePaymentscorerCode_754 = 754 [(epay_payout_code_value) = {
    message: "Не смогли вызвать api makePayments в core.recurrent",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_756 = 756 [(epay_payout_code_value) = {
    message: "Не смогли распарсить входящий запрос",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_757 = 757 [(epay_payout_code_value) = {
    message: "При получении терминала произошла ошибка",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  MerchantCode_758 = 758 [(epay_payout_code_value) = {
    message: "При получении merchant-а произошла ошибка",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_759 = 759 [(epay_payout_code_value) = {
    message: "Не удалось определить терминал комерсанта",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_760 = 760 [(epay_payout_code_value) = {
    message: "Не определили терминал комерсанта",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_761 = 761 [(epay_payout_code_value) = {
    message: "Платежный адаптер вернул ошибку",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_762 = 762 [(epay_payout_code_value) = {
    message: "При обновлении транзакции произошла ошибка в БД",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_765 = 765 [(epay_payout_code_value) = {
    message: "При обновлении транзакции произошла ошибка в БД",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  EgatewayCode_766 = 766 [(epay_payout_code_value) = {
    message: "При вызове egateway произошла ошибка",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_767 = 767 [(epay_payout_code_value) = {
    message: "При обновлении транзакции произошла ошибка в БД",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_768 = 768 [(epay_payout_code_value) = {
    message: "При обновлении транзакции произошла ошибка в БД	",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_769 = 769 [(epay_payout_code_value) = {
    message: "При обновлении транзакции произошла ошибка в БД",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_770 = 770 [(epay_payout_code_value) = {
    message: "При сохранении в БД произошла ошибка",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_771 = 771 [(epay_payout_code_value) = {
    message: "При создании транзакции произошла ошибка в БД",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_772 = 772 [(epay_payout_code_value) = {
    message: "При обращении к БД произошла ошибка",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_773 = 773 [(epay_payout_code_value) = {
    message: "При поиске оригинальной транзакции произошла ошибка",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_774 = 774 [(epay_payout_code_value) = {
    message: "Не заполнены данные платежа в токене",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_775 = 775 [(epay_payout_code_value) = {
    message: "Запрещено для пользовательской авторизации",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  InvoiceIDCode_776 = 776 [(epay_payout_code_value) = {
    message: "InvoiceID не совпадает в токене и запросе",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  TerminalCode_777 = 777 [(epay_payout_code_value) = {
    message: "Terminal не совпадает в токене и запросе",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  CurrencyCode_778 = 778 [(epay_payout_code_value) = {
    message: "Currency не совпадает в токене и запросе",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  AmountCode_779 = 779 [(epay_payout_code_value) = {
    message: "Amount не совпадает в токене и запросе",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  AmountCode_780 = 780 [(epay_payout_code_value) = {
    message: "Amount не совпадает в токене и запросе",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  CorepaymentCode_781 = 781 [(epay_payout_code_value) = {
    message: "Не удалось сделать рекуррентный платеж в core.payment",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_782 = 782 [(epay_payout_code_value) = {
    message: "ошибка входящего сообщения",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_783 = 783 [(epay_payout_code_value) = {
    message: "не удалось сформировать запрос",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_784 = 784 [(epay_payout_code_value) = {
    message: "Не смогли распарсить входящий запрос",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  IdCode_785 = 785 [(epay_payout_code_value) = {
    message: "При поиске транзакции по id произошла ошибка",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  TerminalIdCode_786 = 786 [(epay_payout_code_value) = {
    message: "При поиске терминала по terminalId произошла ошибка",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_787 = 787 [(epay_payout_code_value) = {
    message: "не смогли выполнить операцию",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_789 = 789 [(epay_payout_code_value) = {
    message: "не удалось сформировать запрос",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_790 = 790 [(epay_payout_code_value) = {
    message: "ошибка входящего сообщения",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_791 = 791 [(epay_payout_code_value) = {
    message: "не смогли выполнить операцию",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_792 = 792 [(epay_payout_code_value) = {
    message: "ошибка входящего сообщения",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_793 = 793 [(epay_payout_code_value) = {
    message: "не удалось сформировать запрос",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_795 = 795 [(epay_payout_code_value) = {
    message: "не смогли получить список подписок",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  CorerecurrentCode_796 = 796 [(epay_payout_code_value) = {
    message: "Не смогли распарсить ответ от core.recurrent",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_797 = 797 [(epay_payout_code_value) = {
    message: "При обращении к БД произошла ошибка",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_798 = 798 [(epay_payout_code_value) = {
    message: "При обращении к БД произошла ошибка",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_799 = 799 [(epay_payout_code_value) = {
    message: "При сохранении в БД произошла ошибка",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_800 = 800 [(epay_payout_code_value) = {
    message: "При получении токена произошла ошибка",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_801 = 801 [(epay_payout_code_value) = {
    message: "При отправке письма произошла ошибка",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_802 = 802 [(epay_payout_code_value) = {
    message: "При обращении к БД произошла ошибка",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_803 = 803 [(epay_payout_code_value) = {
    message: "При сохранении в БД произошла ошибка",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_804 = 804 [(epay_payout_code_value) = {
    message: "Не смогли распарсить входящий запрос",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_805 = 805 [(epay_payout_code_value) = {
    message: "При обращении к БД произошла ошибка",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  PublicIDCode_806 = 806 [(epay_payout_code_value) = {
    message: "Произошла ошибка при получении объекта по PublicID",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_807 = 807 [(epay_payout_code_value) = {
    message: "Ошибка при получении списка ролей",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_808 = 808 [(epay_payout_code_value) = {
    message: "Ошибка при получении роли по идентификатору",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_809 = 809 [(epay_payout_code_value) = {
    message: "Ошибка при получении роли по названию",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_810 = 810 [(epay_payout_code_value) = {
    message: "Ошибка при парсинге входных данных в структура роли",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_811 = 811 [(epay_payout_code_value) = {
    message: "Ошибка при создании роли",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_812 = 812 [(epay_payout_code_value) = {
    message: "Ошибка при получении роли по идентификатору для обновления данных",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_813 = 813 [(epay_payout_code_value) = {
    message: "Ошибка при парсинге входных данных в структуру роли",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_814 = 814 [(epay_payout_code_value) = {
    message: "Ошибка при обновлении роли",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_815 = 815 [(epay_payout_code_value) = {
    message: "Ошибка при получении роли по идентификатору для удаления",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_816 = 816 [(epay_payout_code_value) = {
    message: "Ошибка при удалении роли",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_817 = 817 [(epay_payout_code_value) = {
    message: "Ошибка при получении списка действий",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_818 = 818 [(epay_payout_code_value) = {
    message: "Ошибка при получении действия по идентификатору",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_819 = 819 [(epay_payout_code_value) = {
    message: "Ошибка при получении действия по названию",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_820 = 820 [(epay_payout_code_value) = {
    message: "Ошибка при парсинге входных данных в структуру действия",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_821 = 821 [(epay_payout_code_value) = {
    message: "Ошибка при создании действия",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_822 = 822 [(epay_payout_code_value) = {
    message: "Ошибка при получении действия по идентификатору для обновления",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_823 = 823 [(epay_payout_code_value) = {
    message: "Ошибка при парсинге входных данных в структуру действия для обновления",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_824 = 824 [(epay_payout_code_value) = {
    message: "Ошибка при обновлении действия",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_825 = 825 [(epay_payout_code_value) = {
    message: "Ошибка при получении действия по идентификатору для удаления",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_826 = 826 [(epay_payout_code_value) = {
    message: "Ошибка при удалении действия",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_827 = 827 [(epay_payout_code_value) = {
    message: "Ошибка при получении списка ресурсов",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_828 = 828 [(epay_payout_code_value) = {
    message: "Ошибка при получении ресурса по идентификатору",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_829 = 829 [(epay_payout_code_value) = {
    message: "Ошибка при получении ресурса по названию",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_830 = 830 [(epay_payout_code_value) = {
    message: "Ошибка при парсинге входных данных в структуру ресурса",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_831 = 831 [(epay_payout_code_value) = {
    message: "Ошибка при создании ресурса",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_832 = 832 [(epay_payout_code_value) = {
    message: "Ошибка при получении ресурса по идентификатору для обновления",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_833 = 833 [(epay_payout_code_value) = {
    message: "Ошибка при парсинге входных данных в структуру ресурса",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_834 = 834 [(epay_payout_code_value) = {
    message: "Ошибка при обновлении ресурса",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_835 = 835 [(epay_payout_code_value) = {
    message: "Ошибка при получении ресурса по идентификатору для удаления",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_836 = 836 [(epay_payout_code_value) = {
    message: "Ошибка при удалении ресурса",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_837 = 837 [(epay_payout_code_value) = {
    message: "Ошибка при получении списка доступов",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_838 = 838 [(epay_payout_code_value) = {
    message: "Ошибка при парсинге входных данных в структуру доступа",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_839 = 839 [(epay_payout_code_value) = {
    message: "Ошибка при поиске доступа по роли, действию и ресурсу",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_840 = 840 [(epay_payout_code_value) = {
    message: "Ошибка при создании доступа",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_841 = 841 [(epay_payout_code_value) = {
    message: "Ошибка при получении доступа по идентификатору",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_842 = 842 [(epay_payout_code_value) = {
    message: "Ошибка при удалении доступа",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_843 = 843 [(epay_payout_code_value) = {
    message: "Ошибка при парсинге входных данных в структуру списка доступов",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_844 = 844 [(epay_payout_code_value) = {
    message: "Ошибка при поиске доступа",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_845 = 845 [(epay_payout_code_value) = {
    message: "Ошибка при создании доступа для роли в списков ресурсов",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  PublicIdCode_846 = 846 [(epay_payout_code_value) = {
    message: "Ошибка при получении сотрудника по publicId",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  MerchantAPICode_847 = 847 [(epay_payout_code_value) = {
    message: "Ошибка при парсинге ответа из MerchantAPI в структуру сотрудника",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_848 = 848 [(epay_payout_code_value) = {
    message: "Ошибка при получении роли по названию для проверки доступа",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_849 = 849 [(epay_payout_code_value) = {
    message: "Ошибка при получении действия по названию для проверки доступа",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_850 = 850 [(epay_payout_code_value) = {
    message: "Ошибка при получении ресурса по названию для проверки доступа",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  StaffInviteRequestCode_851 = 851 [(epay_payout_code_value) = {
    message: "Ошибка при парсинге входных данных в структуру StaffInviteRequest",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  PublicIdCode_852 = 852 [(epay_payout_code_value) = {
    message: "Ошибка при получении сотрудника по publicId",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_853 = 853 [(epay_payout_code_value) = {
    message: "Ошибка при отправке приглашения на почту сотрудника",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_854 = 854 [(epay_payout_code_value) = {
    message: "Не удалось получить подписки коммерсанта",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  PublicIdCode_855 = 855 [(epay_payout_code_value) = {
    message: "Не удалось получить коммерсанта по publicId",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  PublicIdCode_856 = 856 [(epay_payout_code_value) = {
    message: "Не удалось получить коммерсанта по publicId",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_857 = 857 [(epay_payout_code_value) = {
    message: "Не удалось спарсить ответ ",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  PublicIdCode_858 = 858 [(epay_payout_code_value) = {
    message: "Не удалось получить коммерсанта по publicId при создании подписки",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_859 = 859 [(epay_payout_code_value) = {
    message: "Не смогли распарсить входящий запрос",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_860 = 860 [(epay_payout_code_value) = {
    message: "Не смогли распарсить входящий запрос",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_861 = 861 [(epay_payout_code_value) = {
    message: "Отсутствует название поля при обращении к бд",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_862 = 862 [(epay_payout_code_value) = {
    message: "Отсутствует параметр поля при обращении к бд",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_863 = 863 [(epay_payout_code_value) = {
    message: "Неправильный метод поиска при обращении к бд",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_864 = 864 [(epay_payout_code_value) = {
    message: "Пользователь с такой почтой уже существует",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_865 = 865 [(epay_payout_code_value) = {
    message: "Ошибка при получении статуса сотрудника по названию",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  OauthCode_866 = 866 [(epay_payout_code_value) = {
    message: "Ошибка при создании пользователя в системе oauth",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_867 = 867 [(epay_payout_code_value) = {
    message: "Ошибка при сохранении сотрудника",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_868 = 868 [(epay_payout_code_value) = {
    message: "Ошибка при создании временного токена",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_869 = 869 [(epay_payout_code_value) = {
    message: "Ошибка при парсинге входных данных",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_870 = 870 [(epay_payout_code_value) = {
    message: "Ошибка при подтверждении пароля",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_871 = 871 [(epay_payout_code_value) = {
    message: "Ошибка при поиске пользователя по электронному адресу",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_872 = 872 [(epay_payout_code_value) = {
    message: "Ошибка при сравнивании токенов",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  OauthCode_873 = 873 [(epay_payout_code_value) = {
    message: "Ошибка при получении пользователя из системы oauth",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_874 = 874 [(epay_payout_code_value) = {
    message: "Ошибка при хешировании пароля",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  OauthCode_875 = 875 [(epay_payout_code_value) = {
    message: "Ошибка при обновлении пользователя в системе oauth",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_876 = 876 [(epay_payout_code_value) = {
    message: "Не смогли распарсить входящий запрос",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  MDCode_877 = 877 [(epay_payout_code_value) = {
    message: "Поиск по MD вернул ошибку",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_878 = 878 [(epay_payout_code_value) = {
    message: "Не смогли распарсить транзакцию",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_879 = 879 [(epay_payout_code_value) = {
    message: "При получении терминала произошла ошибка",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_880 = 880 [(epay_payout_code_value) = {
    message: "Не заполнены данные платежа в токене",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_881 = 881 [(epay_payout_code_value) = {
    message: "Запрещено для пользовательской авторизации",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  EGWCode_882 = 882 [(epay_payout_code_value) = {
    message: "При вызове EGW произошла ошибка",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  RedisCode_883 = 883 [(epay_payout_code_value) = {
    message: "При сохранении транзакции произошла ошибка в Redis	",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  RedisCode_884 = 884 [(epay_payout_code_value) = {
    message: "При обновлении транзакции произошла ошибка в Redis",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  RedisCode_885 = 885 [(epay_payout_code_value) = {
    message: "При обновлении транзакции произошла ошибка в Redis",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  RedisCode_886 = 886 [(epay_payout_code_value) = {
    message: "При сохранении в Redis произошла ошибка",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_887 = 887 [(epay_payout_code_value) = {
    message: "Ошибка при парсинге входных данных",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  AclapiCode_888 = 888 [(epay_payout_code_value) = {
    message: "Ошибка при получении роли пользователи из acl api",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_889 = 889 [(epay_payout_code_value) = {
    message: "Ошибка при формировании запроса для приглашения пользователя",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  MerchantapiCode_890 = 890 [(epay_payout_code_value) = {
    message: "Ошибка при отправке запроса на merchant api для приглашения пользователя",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_891 = 891 [(epay_payout_code_value) = {
    message: "Ошибка при парсинге входных данных",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_892 = 892 [(epay_payout_code_value) = {
    message: "Ошибка при формировании запроса для сброса пароля",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  MerchantapiCode_893 = 893 [(epay_payout_code_value) = {
    message: "Ошибка при запросе в merchant api для сброса пароля",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_894 = 894 [(epay_payout_code_value) = {
    message: "Не смогли распарсить входящий запрос",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  CorepaymentCode_895 = 895 [(epay_payout_code_value) = {
    message: "Не смогли сформировать запрос на core.payment",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  ConfirmCode_896 = 896 [(epay_payout_code_value) = {
    message: "Не смогли выполнить confirm",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_897 = 897 [(epay_payout_code_value) = {
    message: "Ошибка при парсинге входных данных",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  PublicIDCode_898 = 898 [(epay_payout_code_value) = {
    message: "Ошибка при поиске пользователя по publicID",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_899 = 899 [(epay_payout_code_value) = {
    message: "Ошибка при обновлении пользователя",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_900 = 900 [(epay_payout_code_value) = {
    message: "Ошибка при парсинге входных данных",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_901 = 901 [(epay_payout_code_value) = {
    message: "Ошибка при поиске роли по названию",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  PublicIDCode_902 = 902 [(epay_payout_code_value) = {
    message: "Ошибка при поиске пользователя по publicID",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_903 = 903 [(epay_payout_code_value) = {
    message: "Ошибка при формировании запроса",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_904 = 904 [(epay_payout_code_value) = {
    message: "Ошибка при обновлении пользователя",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_905 = 905 [(epay_payout_code_value) = {
    message: "Ошибка при получении статуса пользователя по названию",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_906 = 906 [(epay_payout_code_value) = {
    message: "Ошибка при поиске статуса пользователя по названию",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_907 = 907 [(epay_payout_code_value) = {
    message: "Ошибка при обновлении пользователя",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_908 = 908 [(epay_payout_code_value) = {
    message: "Ошибка при парсинге входных данных",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  MerchantapiCode_909 = 909 [(epay_payout_code_value) = {
    message: "Ошибка при формировании запроса в merchant api",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  MerchantapiCode_910 = 910 [(epay_payout_code_value) = {
    message: "Ошибка при получении ответа от merchant api для сброса пароля",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_911 = 911 [(epay_payout_code_value) = {
    message: "Ошибка при парсинге входных данных",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_912 = 912 [(epay_payout_code_value) = {
    message: "Ошибка при поиске пользователя",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_913 = 913 [(epay_payout_code_value) = {
    message: "Ошибка при создании временного токена",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_914 = 914 [(epay_payout_code_value) = {
    message: "Ошибка при отправке почты",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_915 = 915 [(epay_payout_code_value) = {
    message: "Ошибка при получении списка схем цветов",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_916 = 916 [(epay_payout_code_value) = {
    message: "Ошибка при получении коммерсанта",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_917 = 917 [(epay_payout_code_value) = {
    message: "Ошибка при получении настроек коммерсанта",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_918 = 918 [(epay_payout_code_value) = {
    message: "Ошибка при парсинге запроса",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_919 = 919 [(epay_payout_code_value) = {
    message: "Ошибка при получении настроек коммерсанта по идентификатору",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_920 = 920 [(epay_payout_code_value) = {
    message: "Ошибка при обновлении настроек коммерсанта",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_921 = 921 [(epay_payout_code_value) = {
    message: "Ошибка при поиске коммерсанта",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_922 = 922 [(epay_payout_code_value) = {
    message: "Не удалось создать запрос на обновление сертификата",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_923 = 923 [(epay_payout_code_value) = {
    message: "Ошибка при поиске запроса на обновление сертификата",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_924 = 924 [(epay_payout_code_value) = {
    message: "Запрос на обновление сертификата уже в обработке",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_925 = 925 [(epay_payout_code_value) = {
    message: "Не удалось сделать запрос на обновление сертификата",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_926 = 926 [(epay_payout_code_value) = {
    message: "Некорректный запрос при регистрации коммерсанта",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_927 = 927 [(epay_payout_code_value) = {
    message: "Ошибка создания клиента при регистрации коммерсанта",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_928 = 928 [(epay_payout_code_value) = {
    message: "Ошибка при поиске статуса пользователя по названию",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  PublicIDCode_929 = 929 [(epay_payout_code_value) = {
    message: "Ошибка при поиске пользователя по publicID",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_930 = 930 [(epay_payout_code_value) = {
    message: "Ошибка при формировании запроса для удаления пользователя",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_931 = 931 [(epay_payout_code_value) = {
    message: "Ошибка при обновлении пользователя",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  ScopesCode_932 = 932 [(epay_payout_code_value) = {
    message: "Ошибка при создании scopes для коммерсанта",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_933 = 933 [(epay_payout_code_value) = {
    message: "Ошибка добавления коммерсанта в БД при регистрации коммерсанта",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_934 = 934 [(epay_payout_code_value) = {
    message: "Некорректный запрос при создании контакта коммерсанта",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_935 = 935 [(epay_payout_code_value) = {
    message: "Ошибка создания контакта коммерсанта при регистрации коммерсанта",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_937 = 937 [(epay_payout_code_value) = {
    message: "Ошибка создания персонала коммерсанта при регистрации коммерсанта",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_938 = 938 [(epay_payout_code_value) = {
    message: "Ошибка создания предприятия коммерсанта при регистрации коммерсанта",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_939 = 939 [(epay_payout_code_value) = {
    message: "Ошибка создания терминала коммерсанта при регистрации коммерсанта",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_940 = 940 [(epay_payout_code_value) = {
    message: "Ошибка при поиске коммерсанта",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_941 = 941 [(epay_payout_code_value) = {
    message: "Ошибка при запросе на получении запроса на обновление сертификата",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_942 = 942 [(epay_payout_code_value) = {
    message: "Ошибка при получении списка запросов по обновлением сертификатов",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_943 = 943 [(epay_payout_code_value) = {
    message: "Ошибка создания коммерсанта при регистрации коммерсанта",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_944 = 944 [(epay_payout_code_value) = {
    message: "Ошибка при поиске запроса на обновление сертификата",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  PublicIDCode_945 = 945 [(epay_payout_code_value) = {
    message: "Ошибка при поиске коммерсанта по PublicID",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_946 = 946 [(epay_payout_code_value) = {
    message: "Ошибка при парсинге входных данных",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_947 = 947 [(epay_payout_code_value) = {
    message: "Ошибка при поиске настроек страницы оплаты",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_948 = 948 [(epay_payout_code_value) = {
    message: "У коммерсанта уже есть настройки страницы оплаты",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_949 = 949 [(epay_payout_code_value) = {
    message: "Ошибка при создании цветовой схемы для страницы оплаты",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_950 = 950 [(epay_payout_code_value) = {
    message: "Ошибка при создании настроек страницы оплаты",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  PublicIdCode_951 = 951 [(epay_payout_code_value) = {
    message: "Ошибка при получении коммерсанта по publicId",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_952 = 952 [(epay_payout_code_value) = {
    message: "Ошибка при парсинге запроса",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_953 = 953 [(epay_payout_code_value) = {
    message: "Ошибка при получении настроек страницы оплаты",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_954 = 954 [(epay_payout_code_value) = {
    message: "Ошибка при получении световой схемы страницы оплаты",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_955 = 955 [(epay_payout_code_value) = {
    message: "Ошибка при обновлении цветовой схемы страницы оплаты",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_956 = 956 [(epay_payout_code_value) = {
    message: "Ошибка при обновлении настроек страницы оплаты",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_957 = 957 [(epay_payout_code_value) = {
    message: "Не удалось зачислить бонусы за транзакцию",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_958 = 958 [(epay_payout_code_value) = {
    message: "Не удалось зачислить бонусы за транзакцию",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_959 = 959 [(epay_payout_code_value) = {
    message: "Не удалось найти периодичность рекуррентного платежа по названию",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_960 = 960 [(epay_payout_code_value) = {
    message: "Не удалось найти статус рекуррентного платежа по названию",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_961 = 961 [(epay_payout_code_value) = {
    message: "Не удалось найти периодичность рекуррентного платежа по названию",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_962 = 962 [(epay_payout_code_value) = {
    message: "Не удалось найти подписки коммерсанта",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  AuthorizedRequestJSOCode_963 = 963 [(epay_payout_code_value) = {
    message: "Ошибка отправки AuthorizedRequestJSON запроса в Core.Merchant при обновлении client secret коммерсанта",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  ClientsecretCode_964 = 964 [(epay_payout_code_value) = {
    message: "Несоответствие прав доступа персонала коммерсанта для обновления client secret",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  ClientidclientsecretCode_965 = 965 [(epay_payout_code_value) = {
    message: "Ошибка получения предприятий по clientid при обновлении client secret коммерсанта на уровне service",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  MerchantIDclientsecrCode_966 = 966 [(epay_payout_code_value) = {
    message: "Ошибка получения персонала по merchantID из предприятий при обновлении client secret коммерсанта на уровне service",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  ClientsecretclientIDCode_967 = 967 [(epay_payout_code_value) = {
    message: "Ошибка обновления client secret коммерсанта по clientID при обновлении client secret коммерсанта на уровне db",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  EmailclientIDclientsCode_968 = 968 [(epay_payout_code_value) = {
    message: "Ошибка отправки email по client ID при обновлении client secret коммерсанта на уровне service",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_969 = 969 [(epay_payout_code_value) = {
    message: "Ошибка при парсинге входящих данных",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_970 = 970 [(epay_payout_code_value) = {
    message: "Ошибка при получении файла ",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_971 = 971 [(epay_payout_code_value) = {
    message: "Ошибка при загрузке файла",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_972 = 972 [(epay_payout_code_value) = {
    message: "Ошибка при поиске статуса запроса на сертификат по имени",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  IDCode_973 = 973 [(epay_payout_code_value) = {
    message: "Ошибка при поиске запроса на сертификат по ID",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_974 = 974 [(epay_payout_code_value) = {
    message: "Не удалось обновить данные коммерсанта",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_975 = 975 [(epay_payout_code_value) = {
    message: "Не удалось обновить данные запроса на обновление сертификата",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  UserclientIDclientseCode_976 = 976 [(epay_payout_code_value) = {
    message: "Ошибка получения user по clientID при обновлении clientsecret коммерсанта на уровне service",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  AuthorizedRequestJSOCode_977 = 977 [(epay_payout_code_value) = {
    message: "Ошибка AuthorizedRequestJSON при отправке email коммерсанту при обновлении client secret коммерсанта на уровне service ",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  JsonMarshalsendEmailCode_978 = 978 [(epay_payout_code_value) = {
    message: "Ошибка при попытке json.Marshal(sendEmail) при отправке email при обновлении client secret коммерсанта на уровне service",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_979 = 979 [(epay_payout_code_value) = {
    message: "Не прошел проверку токена",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  FormFileCode_980 = 980 [(epay_payout_code_value) = {
    message: "Ошибка разбора FormFile.",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_981 = 981 [(epay_payout_code_value) = {
    message: "Ошибка при получении терминала",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_982 = 982 [(epay_payout_code_value) = {
    message: "Ошибка при получении параметров запроса",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_983 = 983 [(epay_payout_code_value) = {
    message: "Ошибка при получении магазина по идентификатору",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_984 = 984 [(epay_payout_code_value) = {
    message: "Ошибка при парсинге запроса",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_985 = 985 [(epay_payout_code_value) = {
    message: "Ошибка при обновлении магазина",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_986 = 986 [(epay_payout_code_value) = {
    message: "Ошибка при сохранении файла на сервер.",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_987 = 987 [(epay_payout_code_value) = {
    message: "Ошибка при получении настроек страницы платежа",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_988 = 988 [(epay_payout_code_value) = {
    message: "Ошибка при получении идентификатора магазина",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_989 = 989 [(epay_payout_code_value) = {
    message: "Ошибка при парсинге запроса",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_990 = 990 [(epay_payout_code_value) = {
    message: "Ошибка при парсинге формы",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_991 = 991 [(epay_payout_code_value) = {
    message: "Ошибка при формировании запроса на обновление настроек страницы платежа",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_992 = 992 [(epay_payout_code_value) = {
    message: "Ошибка при обновлении настроек страницы платежа",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  ServiceCode_993 = 993 [(epay_payout_code_value) = {
    message: "Ошибка обновления кэша клиента при заведении нового коммерсанта на уровне service",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  ShouldBindJSONservicCode_994 = 994 [(epay_payout_code_value) = {
    message: "Ошибка считывания данных с помощью ShouldBindJSON при регистрации коммерсанта на уровне service",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  AuthorizedRequestJSOCode_995 = 995 [(epay_payout_code_value) = {
    message: "Ошибка авторизированного запроса AuthorizedRequestJSON при обращении в oauth на уровне service",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  AuthorizedRequestJSOCode_996 = 996 [(epay_payout_code_value) = {
    message: "Ошибка авторизированного запроса AuthorizedRequestJSON при обращении в Core.Merchant на уровне service",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  UseruserusernameservCode_997 = 997 [(epay_payout_code_value) = {
    message: "Ошибка создания user по причине существования user с таким же username при заведении нового коммерсанта на уровне service",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  MerchantserviceCode_999 = 999 [(epay_payout_code_value) = {
    message: "Указатель на модель merchant является пустым при заведении нового коммерсанта на уровне service",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  NamewebserviceCode_1000 = 1000 [(epay_payout_code_value) = {
    message: "Ошибка получения статуса по name при регистрации коммерсанта на уровне webservice",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  ContactTypenameserviCode_1001 = 1001 [(epay_payout_code_value) = {
    message: "Ошибка получения contactType коммерсанта по name во время создания контакта коммерсанта при регистрации коммерсанта на уровне service",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  PublicIdCode_1002 = 1002 [(epay_payout_code_value) = {
    message: "Ошибка при получении пользователя по publicId",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_1003 = 1003 [(epay_payout_code_value) = {
    message: "Ошибка при получении статуса пользователя по названию",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_1004 = 1004 [(epay_payout_code_value) = {
    message: "Ошибка при отправке письма",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_1005 = 1005 [(epay_payout_code_value) = {
    message: "Ошибка при поиске контактов коммерсанта",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  OauthCode_1006 = 1006 [(epay_payout_code_value) = {
    message: "Ошибка при обновлении статуса пользователя в oauth",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_1007 = 1007 [(epay_payout_code_value) = {
    message: "Файл отсутствует",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_1008 = 1008 [(epay_payout_code_value) = {
    message: "Не может прочесть файл",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_1009 = 1009 [(epay_payout_code_value) = {
    message: "Не может удалить файл внутри сервера",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  IdserviceCode_1010 = 1010 [(epay_payout_code_value) = {
    message: "Ошибка получения клиента по id при проверке сертификата документа на уровне service",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  RSAserviceCode_1011 = 1011 [(epay_payout_code_value) = {
    message: "Ошибка загрузки приватного RSA ключа при проверке сертификата документа на уровне service",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  ServiceCode_1012 = 1012 [(epay_payout_code_value) = {
    message: "Ошибка создания платежного шаблона при проверке сертификата документа на уровне service",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Codebase64stringbaseCode_1013 = 1013 [(epay_payout_code_value) = {
    message: "Ошибка деcodeинга base64 string в base64  []byte при проверке сертификата документа на уровне service",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  XmlUnmarshalserviceCode_1014 = 1014 [(epay_payout_code_value) = {
    message: "Ошибка xml Unmarshal при проверке сертификата документа на уровне service",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Codebase64stringbaseCode_1015 = 1015 [(epay_payout_code_value) = {
    message: "Ошибка деcodeинга base64 string в base64 []byte во время работы с signData при проверке сертификата документа на уровне service",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  XmlMarshalserviceCode_1016 = 1016 [(epay_payout_code_value) = {
    message: "Ошибка xml Marshal при проверке сертификата документа на уровне service",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  OAUTHCode_1017 = 1017 [(epay_payout_code_value) = {
    message: "Не удалось отправить запрос в OAUTH для обновления секретного ключа коммерсанта",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  ServiceCode_1018 = 1018 [(epay_payout_code_value) = {
    message: "Ошибка генерации токена во время генерации токена по сертификату при проверке сертификата документа на уровне service",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  ShopidCode_1019 = 1019 [(epay_payout_code_value) = {
    message: "Не удалось найти терминал по shop id",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  JsonserviceCode_1020 = 1020 [(epay_payout_code_value) = {
    message: "Ошибка преобразования токена в json во время генерации токена при проверке сертификата на уровне service",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  AuthserviceCode_1021 = 1021 [(epay_payout_code_value) = {
    message: "Ошибка записи в кэш параметров модели auth во время генерации токена при проверке сертификата на уровне service",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  DeviceIDauthserviceCode_1022 = 1022 [(epay_payout_code_value) = {
    message: "Ошибка записи в кэш DeviceID из модели auth во время генерации токена при проверке сертификата на уровне service",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  ServiceCode_1023 = 1023 [(epay_payout_code_value) = {
    message: "Проверка подписи выявила ее неликвидность при проверке сертификата документа на уровне service",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  IdserviceCode_1024 = 1024 [(epay_payout_code_value) = {
    message: "Ошибка получения клиента по id сертификата при проверке сертификата на уровне service",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  BindXMLwebserviceCode_1025 = 1025 [(epay_payout_code_value) = {
    message: "Ошибка bindXML при проверке сертификата документа на уровне webservice",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  AuthorizedRequestJSOCode_1026 = 1026 [(epay_payout_code_value) = {
    message: "Ошибка отправки AuthorizedRequestJSON запроса в CoreMerchant во время сбора параметров postlink при проверке сертификата на уровне service",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  BindclientIDpostLinkCode_1027 = 1027 [(epay_payout_code_value) = {
    message: "Ошибка Bind clientID при получении postLinkInfo на уровне webservice",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  ClientIDpostLinkInfoCode_1028 = 1028 [(epay_payout_code_value) = {
    message: "Ошибка получения предприятий коммерсанта по clientID при получении postLinkInfo на уровне webservice",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  MerchantIDpostLinkInCode_1029 = 1029 [(epay_payout_code_value) = {
    message: "Ошибка получения терминала по merchantID при получении postLinkInfo на уровне webservice",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  PostlinkrabbitservicCode_1030 = 1030 [(epay_payout_code_value) = {
    message: "Ошибка отправки postlink в rabbit при проверке сертификата документа на уровне service",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  TerminalidpostLinkInCode_1031 = 1031 [(epay_payout_code_value) = {
    message: "Ошибка получения терминала по terminal_id при получении postLinkInfo на уровне service",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  CBindpostLinkInfoposCode_1032 = 1032 [(epay_payout_code_value) = {
    message: "Ошибка c.Bind postLinkInfo при получении postLinkInfo на уровне webservice",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  JsonMarshalpostlinkICode_1033 = 1033 [(epay_payout_code_value) = {
    message: "Ошибка json Marshal postlinkInfo в []byte при подготовке к AuthorizedRequestJSON в CoreMerchant для получения postLinkInfo на уровне service",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  ClientIDclientIDservCode_1034 = 1034 [(epay_payout_code_value) = {
    message: "Ошибка получения предприятия коммерсанта по clientID при поиске по clientID на уровне service",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  ShopmerchantIDclientCode_1035 = 1035 [(epay_payout_code_value) = {
    message: "Ошибка получения коммерсанта по shop.merchantID при поиске по clientID на уровне service",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  ShopMerchantIDclientCode_1036 = 1036 [(epay_payout_code_value) = {
    message: "Ошибка получения массива терминалов коммерсанта по shop.MerchantID при поиске по clientID на уровне service",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  BindinvoicewebservicCode_1037 = 1037 [(epay_payout_code_value) = {
    message: "Ошибка Bind парсинга входящих данных в модель invoice при получении токена по инвойсу на уровне webservice",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  ExpirePeriodinvoicesCode_1038 = 1038 [(epay_payout_code_value) = {
    message: "Ошибка конвертирования параметра ExpirePeriod входящего invoice при получении токена по инвойсу на уровне service",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  PostlinkRabbitservicCode_1039 = 1039 [(epay_payout_code_value) = {
    message: "Ошибка отправки модели postlink в Rabbit при получении токена по инвойсу на уровне service",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  ServiceCode_1040 = 1040 [(epay_payout_code_value) = {
    message: "Ошибка генерации токена при получении токена по инвойсу на уровне service",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  JsonserviceCode_1041 = 1041 [(epay_payout_code_value) = {
    message: "Ошибка преобразования токена в json во время генерации токена при получении токена по инвойсу на уровне service",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  EgwCode_1042 = 1042 [(epay_payout_code_value) = {
    message: "не корректный ответ от egw",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  EmailclientsecretserCode_1043 = 1043 [(epay_payout_code_value) = {
    message: "Ошибка получения токена во время отправки email при обновлении client secret коммерсанта на уровне service",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  EmailwebserviceCode_1044 = 1044 [(epay_payout_code_value) = {
    message: "Ошибка получения токена во время при восстановлении доступа по email на уровне webservice",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  AuthorizedRequestJSOCode_1045 = 1045 [(epay_payout_code_value) = {
    message: "Ошибка отправки AuthorizedRequestJSON запроса на получение токена HomeBank при валидации метода SetPassword при смене пароля по email на уровне webservice",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  JsonMarshalmerchantoCode_1046 = 1046 [(epay_payout_code_value) = {
    message: "Ошибка json Marshal модели merchant после обращения в oauth во время процесса заведения нового коммерсанта на уровне service",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  JsonMarshalmerchantoCode_1047 = 1047 [(epay_payout_code_value) = {
    message: "Ошибка json Marshal модели merchant до обращения в oauth во время процесса заведения нового коммерсанта на уровне service",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  BindJSONmerchantwebsCode_1048 = 1048 [(epay_payout_code_value) = {
    message: "Ошибка BindJSON модели merchant при отмене регистрации коммерсанта на уровне webservice",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  ClientidserviceCode_1049 = 1049 [(epay_payout_code_value) = {
    message: "Ошибка удаления client по id при отмене регистрации коммерсанта на уровне service",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  ClientScopesclientIDCode_1050 = 1050 [(epay_payout_code_value) = {
    message: "Ошибка удаления clientScopes по clientID при отмене регистрации коммерсанта на уровне service",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  UserloginserviceCode_1051 = 1051 [(epay_payout_code_value) = {
    message: "Ошибка удаления user по login при отмене регистрации коммерсанта на уровне service",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  ClientclientscopesusCode_1052 = 1052 [(epay_payout_code_value) = {
    message: "Ошибка отправки запроса на удаление ранее созданного client, clientscopes и user в случае ошибки AuthorizedRequestJSON запроса в Core.Merchant при заведении нового коммерсанта",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  BindJSONinvoiceCode_1053 = 1053 [(epay_payout_code_value) = {
    message: "Ошибка BindJSON получения данных в модель invoice",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  InvoiceCode_1054 = 1054 [(epay_payout_code_value) = {
    message: "Ошибка получения статуса по invoice из БД  ",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  XMLway4Code_1055 = 1055 [(epay_payout_code_value) = {
    message: "Ошибка парсинга XML строки из БД way4",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  InvoiceCode_1056 = 1056 [(epay_payout_code_value) = {
    message: "Полученное время жизни токена недопустимо при получении токена по invoice",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_1057 = 1057 [(epay_payout_code_value) = {
    message: "Ошибка при парсинге входных данных",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_1058 = 1058 [(epay_payout_code_value) = {
    message: "Ошибка при получении магазина по идентификатору",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_1059 = 1059 [(epay_payout_code_value) = {
    message: "Ошибка при формировании запроса на ссылку инвойса",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  CoreinvoiceCode_1060 = 1060 [(epay_payout_code_value) = {
    message: "Ошибка при получении ответа от core.invoice для создания инвойса",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  BindJSONinvoiceInfoCode_1061 = 1061 [(epay_payout_code_value) = {
    message: "Ошибка BindJSON считывания данных в модель invoiceInfo",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_1062 = 1062 [(epay_payout_code_value) = {
    message: "Ошибка открытия лог файла ",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_1063 = 1063 [(epay_payout_code_value) = {
    message: "Искомая строка не найдена в лог файле по полученному параметру",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  ScannererrCode_1064 = 1064 [(epay_payout_code_value) = {
    message: "Ошибка сканирования лог-файла scanner.err",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  BindCode_1065 = 1065 [(epay_payout_code_value) = {
    message: "Ошибка при Bind объекта",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  ShopIdCode_1066 = 1066 [(epay_payout_code_value) = {
    message: "Ошибка при запросе терминала по shopId",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  InvoiceLinkCode_1067 = 1067 [(epay_payout_code_value) = {
    message: "Ошибка при получении токена для InvoiceLink",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  MarshalinvoicelinkCode_1068 = 1068 [(epay_payout_code_value) = {
    message: "Ошибка при Marshal invoicelink",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  InvoiceLinkCode_1069 = 1069 [(epay_payout_code_value) = {
    message: "Ошибка при запросе на создание invoiceLink",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  InvoiceLinkCode_1070 = 1070 [(epay_payout_code_value) = {
    message: "Ошибка при запросе на получение списка invoiceLink",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  InvoiceLinkCode_1071 = 1071 [(epay_payout_code_value) = {
    message: "Ошибка при запросе на перенаправление по invoiceLink",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  PublicIDinvoiceCode_1072 = 1072 [(epay_payout_code_value) = {
    message: "Ошибка записи publicID токена в кэш при получении токена для invoice",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  ParamsinvoiceCode_1073 = 1073 [(epay_payout_code_value) = {
    message: "Ошибка записи params токена в кэш при получении токена для invoice",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  InvoiceLinkCode_1074 = 1074 [(epay_payout_code_value) = {
    message: "Ошибка при запросе на отключение invoiceLink",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  BindCode_1075 = 1075 [(epay_payout_code_value) = {
    message: "Ошибка при Bind объекта",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  StrconvAtoiExpirePerCode_1076 = 1076 [(epay_payout_code_value) = {
    message: "Ошибка при strconv.Atoi на ExpirePeriod",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  InvoiceLinkCode_1077 = 1077 [(epay_payout_code_value) = {
    message: "Ошибка при создание invoiceLink",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  EmailCode_1078 = 1078 [(epay_payout_code_value) = {
    message: "Ошибка при отправке email",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  SmsCode_1079 = 1079 [(epay_payout_code_value) = {
    message: "Ошибка при отправке sms",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  InvoiceLinkCode_1080 = 1080 [(epay_payout_code_value) = {
    message: "Ошибка при аннулировании invoiceLink",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  BindCode_1081 = 1081 [(epay_payout_code_value) = {
    message: "Ошибка при Bind объекта",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  PublicidCode_1082 = 1082 [(epay_payout_code_value) = {
    message: "Ошибка при отправке запроса на получение мерчанта по public id",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  NameinvoicelinkCode_1083 = 1083 [(epay_payout_code_value) = {
    message: "отсутствует name в параметрах на получение списка invoice link",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  InvoiceLinkCode_1084 = 1084 [(epay_payout_code_value) = {
    message: "Ошибка при получении статуса invoiceLink по названию",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  SearchparamsinvoicelCode_1085 = 1085 [(epay_payout_code_value) = {
    message: "отсутствует search params в параметрах на получение списка invoice link",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  IncorrectsearchmethoCode_1086 = 1086 [(epay_payout_code_value) = {
    message: "incorrect search method при получение списка invoiceLink",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_1087 = 1087 [(epay_payout_code_value) = {
    message: "Ошибка при запросе в БД",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  InvoiceLinkidCode_1088 = 1088 [(epay_payout_code_value) = {
    message: "Ошибка при получении на invoiceLink по id",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  InvoiceLinkCode_1089 = 1089 [(epay_payout_code_value) = {
    message: "InvoiceLink больше не активен",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_1090 = 1090 [(epay_payout_code_value) = {
    message: "Ошибка при получении токена для отправки смс",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  InvoiceLinkCode_1091 = 1091 [(epay_payout_code_value) = {
    message: "Ошибка при авто отключении invoiceLink",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  PublicIDCode_1092 = 1092 [(epay_payout_code_value) = {
    message: "Ошибка при получении пользователя по publicID",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_1093 = 1093 [(epay_payout_code_value) = {
    message: "Ошибка при получении статуса по наименованию",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_1096 = 1096 [(epay_payout_code_value) = {
    message: "Ошибка при обновлении инвойса",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  HomeBankCode_1097 = 1097 [(epay_payout_code_value) = {
    message: "Ошибка при получении токена от HomeBank",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  EmailCode_1098 = 1098 [(epay_payout_code_value) = {
    message: "Ошибка при отправке инвойса по email",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_1099 = 1099 [(epay_payout_code_value) = {
    message: "Ошибка при отправке инвойса по смс",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_1100 = 1100 [(epay_payout_code_value) = {
    message: "ошибка при обработке данных для отправки",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  CoreinvoiceCode_1101 = 1101 [(epay_payout_code_value) = {
    message: "Ошибка при запросе в core.invoice",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  AuthorizedRequestJSOCode_1103 = 1103 [(epay_payout_code_value) = {
    message: "Ошибка AuthorizedRequestJSON запроса в oauth на блокировку клиента по id",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  AuthorizedRequestJSOCode_1104 = 1104 [(epay_payout_code_value) = {
    message: "Ошибка AuthorizedRequestJSON запроса в Core.Merchant на блокировку клиента по id",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  IdserviceCode_1105 = 1105 [(epay_payout_code_value) = {
    message: "Ошибка блокировки клиента по id на уровне service",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  ClientIDserviceCode_1106 = 1106 [(epay_payout_code_value) = {
    message: "Ошибка блокировки коммерсанта по clientID на уровне service",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  TerminalXLSMIDterminCode_1107 = 1107 [(epay_payout_code_value) = {
    message: "terminal.XLSMID или terminal.XLSTID пустые",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_1130 = 1130 [(epay_payout_code_value) = {
    message: "Ошибка при получении коммерсантов из базы",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_1132 = 1132 [(epay_payout_code_value) = {
    message: "Ошибка при поиске транзакции коммерсанта",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  NameCode_1133 = 1133 [(epay_payout_code_value) = {
    message: "Во время процесса создания коммерсанта был обнаружен клиент с таким же Name как и у новосоздаваемого",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  PostlinkcorecallbackCode_1144 = 1144 [(epay_payout_code_value) = {
    message: "Ошибка отправки быстрого запроса по postlink на core.callback",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_1145 = 1145 [(epay_payout_code_value) = {
    message: "при поиске терминала произошла ошибка",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  CannotsendpostlinkCode_1146 = 1146 [(epay_payout_code_value) = {
    message: "Cannot send postlink",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  CannotsendpostlinkCode_1147 = 1147 [(epay_payout_code_value) = {
    message: "Cannot send postlink",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  BindJsonCode_1148 = 1148 [(epay_payout_code_value) = {
    message: "Ошибка при BindJson",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Base64Code_1149 = 1149 [(epay_payout_code_value) = {
    message: "Не удалось расшифровать входящие данные Base64 ",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  UnmarshalCode_1150 = 1150 [(epay_payout_code_value) = {
    message: "Ошибка при Unmarshal документа",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  HttpcoremigrationCode_1151 = 1151 [(epay_payout_code_value) = {
    message: "Ошибка при получении коммерсанта по терминалу через http запрос в core.migration",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_1152 = 1152 [(epay_payout_code_value) = {
    message: "Не удалось отправить запрос на получение токена по сертификату",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  UnmarshalouathCode_1153 = 1153 [(epay_payout_code_value) = {
    message: "Ошибка при Unmarshal ответа от ouath",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  EPAY1Code_1154 = 1154 [(epay_payout_code_value) = {
    message: "Не удалось отправить запрос в EPAY1 для авторизации платежа",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  UnmarshalEPAY1Code_1155 = 1155 [(epay_payout_code_value) = {
    message: "Ошибка при Unmarshal ответа от EPAY1 при авторизации платежа",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  CannotsendpostlinkCode_1156 = 1156 [(epay_payout_code_value) = {
    message: "Cannot send postlink",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_1157 = 1157 [(epay_payout_code_value) = {
    message: "Ошибка при обновлении терминала для коммерсанта под миграцию",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  UserserviceCode_1158 = 1158 [(epay_payout_code_value) = {
    message: "Ошибка обновления user при создании коммерсанта на уровне service",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  UserserviceCode_1159 = 1159 [(epay_payout_code_value) = {
    message: "Ошибка пересоздания user при регистрации коммерсанта на уровне service",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  UserserviceCode_1160 = 1160 [(epay_payout_code_value) = {
    message: "Пароль не соответствует требованиям сервиса при пересоздании user при процессе регистрации коммерсанта на уровне service",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  UserserviceCode_1161 = 1161 [(epay_payout_code_value) = {
    message: "Ошибка проверки статуса при пересоздании user при процессе регистрации коммерсанта на уровне service",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  UserserviceCode_1162 = 1162 [(epay_payout_code_value) = {
    message: "Ошибка хэширования пароля при пересоздании user при процессе регистрации коммерсанта на уровне service",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  MerchantcompanynamesCode_1163 = 1163 [(epay_payout_code_value) = {
    message: "Ошибка поиска merchant по company_name при регистрации коммерсанта на уровне service",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  MerchantserviceCode_1164 = 1164 [(epay_payout_code_value) = {
    message: "Ошибка перезаписи merchant при регистрациии коммерсанта на уровне service",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  MerchantmerchantCode_1165 = 1165 [(epay_payout_code_value) = {
    message: "Ошибка создания merchant, merchant уже существует при процессе регистрации коммерсанта",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  UserloginuserclientCode_1166 = 1166 [(epay_payout_code_value) = {
    message: "Ошибка при получения user по login во время проверки существования user и client с такими параметрами при регистрации коммерсанта",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  MerchantnameuserclieCode_1167 = 1167 [(epay_payout_code_value) = {
    message: "Ошибка поиска merchant по name при существовании user и client с такими же как входящие, параметрами, при регистрации коммерсанта",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  AuthorizedRequestJSOCode_1168 = 1168 [(epay_payout_code_value) = {
    message: "Ошибка AuthorizedRequestJSON в Core.Merchant при попытке получения merchant по name при регистрации коммерсанта",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  MerchantnameCode_1169 = 1169 [(epay_payout_code_value) = {
    message: "Ошибка поиска merchant по name при поиске существующего коммерсанта при регистрации коммерсанта",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  EmailmerchantCode_1170 = 1170 [(epay_payout_code_value) = {
    message: "Ошибка поиска email для составления merchant  при поиске существующего коммерсанта при регистрации коммерсанта",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  ShopmerchantCode_1171 = 1171 [(epay_payout_code_value) = {
    message: "Ошибка поиска shop для составления merchant при поиске существующего коммерсанта при регистрации коммерсанта",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  TerminalmerchantCode_1172 = 1172 [(epay_payout_code_value) = {
    message: "Ошибка поиска terminal для составления merchant при поиске существующего коммерсанта при регистрации коммерсанта",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  UseruserCode_1173 = 1173 [(epay_payout_code_value) = {
    message: "Ошибка удаления существующего user перед пересозданием user во время регистрации коммерсанта",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  UserclientIDCode_1174 = 1174 [(epay_payout_code_value) = {
    message: "Ошибка блокировки user по clientID",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  UserclientuserCode_1175 = 1175 [(epay_payout_code_value) = {
    message: "Ошибка создания user при регистрации коммерсанта. client был создан, но user уже существует",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  TerminalterminalIDCode_1176 = 1176 [(epay_payout_code_value) = {
    message: "Ошибка поиска terminal по terminalID при проверке страны на разрешенность для терминала коммерсанта",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  CurrencycurrencyNameCode_1177 = 1177 [(epay_payout_code_value) = {
    message: "Ошибка поиска currency по currencyName во время создания terminal при регистрации коммерсанта",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  TerminalIDCode_1178 = 1178 [(epay_payout_code_value) = {
    message: "Ошибка записи разрешенных и запрещенных стран по terminalID в кэш при регистрации коммерсанта",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  TerminalterminalIDteCode_1179 = 1179 [(epay_payout_code_value) = {
    message: "Ошибка получения terminal по terminalID при обновлении кэша стран терминала по terminalID",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  TerminalIDCode_1180 = 1180 [(epay_payout_code_value) = {
    message: "Ошибка обновления разрешенных и запрещенных стран по terminalID в кэше при обновлении кэша стран терминала ",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  AuthorizedRequestJSOCode_1182 = 1182 [(epay_payout_code_value) = {
    message: "Ошибка AuthorizedRequestJSON из oauth в Core.Merchant на обновление кэша terminal при получении токена для платежа",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_1184 = 1184 [(epay_payout_code_value) = {
    message: "При получении токена на платеж кэш терминала был обновлен",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  TerminalCode_1185 = 1185 [(epay_payout_code_value) = {
    message: "При проведении транзакции terminal получен из БД",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  TerminalCode_1186 = 1186 [(epay_payout_code_value) = {
    message: "При проведении транзакции terminal получен из кэша",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_1187 = 1187 [(epay_payout_code_value) = {
    message: "Отображение результатов процедуры из карточной БД",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  MerchantContactTypenCode_1188 = 1188 [(epay_payout_code_value) = {
    message: "Ошибка получения merchantContactType по name при создании контакта коммерсанта при регистрации коммерсанта",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  SenderrorreportcontaCode_1189 = 1189 [(epay_payout_code_value) = {
    message: "Ошибка создания send_error_report contact коммерсанта при создании коммерсанта",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_1190 = 1190 [(epay_payout_code_value) = {
    message: "Ошибка получения всех коммерсантов для отправки отчетов",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  IdCode_1191 = 1191 [(epay_payout_code_value) = {
    message: "Ошибка получения транзакций с полями по id коммерсанта",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  ExcelCode_1192 = 1192 [(epay_payout_code_value) = {
    message: "Ошибка сохранения отчета как excel по списку транзакций коммерсанта",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  CurrencycurrencyIDCode_1193 = 1193 [(epay_payout_code_value) = {
    message: "Ошибка получения currency по currencyID",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  CardTypeCardTypeIDCode_1194 = 1194 [(epay_payout_code_value) = {
    message: "Ошибка получения cardType по CardTypeID",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  EmailCode_1195 = 1195 [(epay_payout_code_value) = {
    message: "Ошибка получения токена для отправки email при отправке отчета о транзакциях",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  JsonMarshalemailCode_1196 = 1196 [(epay_payout_code_value) = {
    message: "Ошибка json.Marshal для отправки email  при отправке отчета о транзакциях",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  AuthorizedRequestJSOCode_1197 = 1197 [(epay_payout_code_value) = {
    message: "Ошибка отправки AuthorizedRequestJSON на отправку email при отправке отчета о транзакциях",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  MerchantContactTypeeCode_1198 = 1198 [(epay_payout_code_value) = {
    message: "Ошибка получения merchantContactType при отправке email при отправке отчета о транзакциях",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  MerchantContactemailCode_1199 = 1199 [(epay_payout_code_value) = {
    message: "Ошибка получения merchantContact при отправке email при отправке отчета о транзакциях",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  ExcelfileemailCode_1200 = 1200 [(epay_payout_code_value) = {
    message: "Ошибка ранее созданного excel file для отправки по email при отправке отчета о транзакциях",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_1201 = 1201 [(epay_payout_code_value) = {
    message: "Ошибка формирования и отправки отчета по транзакциям по данному коммерсанту",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  UserloginCode_1202 = 1202 [(epay_payout_code_value) = {
    message: "Ошибка удаления кэша user по login при отмене регистрации коммерсанта",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  RowsexcelfilereportCode_1203 = 1203 [(epay_payout_code_value) = {
    message: "Ошибка получения rows из excel file report во время отправки отчета по транзакциям коммерсанту",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  UserConnectionsuserICode_1205 = 1205 [(epay_payout_code_value) = {
    message: "Ошибка получения кэша userConnections по userID при получении токена для авторизации",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  KafkaCode_1206 = 1206 [(epay_payout_code_value) = {
    message: "Ошибка записи в kafka",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_1207 = 1207 [(epay_payout_code_value) = {
    message: "По данному коммерсанту нет транзакций за указанный период",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_1208 = 1208 [(epay_payout_code_value) = {
    message: "Ошибка формирования файла из полученных данных при загрузке логотипа предприятия",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_1209 = 1209 [(epay_payout_code_value) = {
    message: "Ошибка открытия полученного файла при загрузке логотипа предприятия",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_1210 = 1210 [(epay_payout_code_value) = {
    message: "Ошибка чтения полученного файла при загрузке логотипа предприятия",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  AuthorizedRequestMulCode_1211 = 1211 [(epay_payout_code_value) = {
    message: "Ошибка отправки AuthorizedRequestMultipart запроса в api.cdn загрузки логотипа при загрузке логотипа предприятия",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  FormatdataheaderwritCode_1212 = 1212 [(epay_payout_code_value) = {
    message: "Ошибка связывания formatdata header writer  с writer файла для записи данных для запроса multipart/form-data при загрузке логотипа предприятия",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  WriterreadermultiparCode_1213 = 1213 [(epay_payout_code_value) = {
    message: "Ошибка копирования данных в writer из reader для записи данных для запроса multipart/form-data при загрузке логотипа предприятия",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  ApicdnCode_1214 = 1214 [(epay_payout_code_value) = {
    message: "Ошибка распознавания ответа от api.cdn на запроса на загрузку логотипа",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  LogourlshopIDCode_1215 = 1215 [(epay_payout_code_value) = {
    message: "Ошибка обновления в БД поля logo_url у shop по ID",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  BindJSONshopshopIDCode_1216 = 1216 [(epay_payout_code_value) = {
    message: "Ошибка BindJSON в модель shop при обновлении shop по ID",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  ShopIDdbshopIDCode_1217 = 1217 [(epay_payout_code_value) = {
    message: "Ошибка обновления shop по ID на уровне db при обновлении shop по ID",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  JSONSamsungPayCode_1219 = 1219 [(epay_payout_code_value) = {
    message: "Ошибка при отправки данных JSON при запросе SamsungPay",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  RequestJSONSamsungPaCode_1220 = 1220 [(epay_payout_code_value) = {
    message: "Ошибка при запросе RequestJSON в SamsungPay API или ref_id просрочен",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  ShopIDshopIDCode_1221 = 1221 [(epay_payout_code_value) = {
    message: "Ошибка получения параметра shopID из входящего запроса при получении списка терминалов по shopID",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  TerminalshopIDCode_1222 = 1222 [(epay_payout_code_value) = {
    message: "Ошибка получения списка terminal по shopID при получении списка терминалов",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  AuthorizedRequestJSOCode_1223 = 1223 [(epay_payout_code_value) = {
    message: "Ошибка запроса AuthorizedRequestJSON на получение списка терминалов по shopID при обращении в Core.Merchant из API",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  ShopIDshopIDCode_1224 = 1224 [(epay_payout_code_value) = {
    message: "Ошибка получения параметра shopID из входящего запроса при получении списка терминалов по shopID",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  BindJSONinOutTerminaCode_1225 = 1225 [(epay_payout_code_value) = {
    message: "Ошибка BindJSON inOutTerminal при изменении терминала по id",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  IdidCode_1226 = 1226 [(epay_payout_code_value) = {
    message: "Ошибка получения параметра id из входящего запроса при изменении терминала по id",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  AuthorizedRequestJSOCode_1227 = 1227 [(epay_payout_code_value) = {
    message: "Ошибка запроса AuthorizedRequestJSON на изменении терминала по id при обращении в Core.Merchant из API",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  TerminalterminalidCode_1228 = 1228 [(epay_payout_code_value) = {
    message: "Ошибка парсинга terminal при изменении terminal по id",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  TerminalidterminalidCode_1229 = 1229 [(epay_payout_code_value) = {
    message: "Ошибка получения terminal по id из бд при изменении terminal по id",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  TerminalterminalidCode_1230 = 1230 [(epay_payout_code_value) = {
    message: "Ошибка изменения terminal в бд при изменении terminal по id",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  JsonMarshalterminaltCode_1231 = 1231 [(epay_payout_code_value) = {
    message: "Ошибка json.Marshal terminal при изменении terminal по id",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  PANCode_1232 = 1232 [(epay_payout_code_value) = {
    message: "Ошибка - PAN карты отсутствует (является пустой строкой) или слишком короткий при проверке транзакции",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  SamsungPaycallbackreCode_1233 = 1233 [(epay_payout_code_value) = {
    message: "Ошибка, samsungPay callback не вернул reference id",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  StaffstaffCode_1234 = 1234 [(epay_payout_code_value) = {
    message: "Ошибка получения staff во время процесса изменения staff коммерсанта",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  BindCreateMVisaTransCode_1235 = 1235 [(epay_payout_code_value) = {
    message: "Ошибка Bind в методе CreateMVisaTrans",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  RequestPostFormXMLCrCode_1236 = 1236 [(epay_payout_code_value) = {
    message: "Ошибка RequestPostFormXML в методе CreateMVisaTrans, httpCode==200, err!=nil",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  RequestPostFormXMLCrCode_1237 = 1237 [(epay_payout_code_value) = {
    message: "Ошибка RequestPostFormXML в методе CreateMVisaTrans, httpCode!=200, err!=nil",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  RequestPostFormXMLCrCode_1238 = 1238 [(epay_payout_code_value) = {
    message: "Ошибка RequestPostFormXML в методе CreateMVisaTrans, возвращаемое body=nil",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  RequestPostFormXMLCrCode_1239 = 1239 [(epay_payout_code_value) = {
    message: "Ошибка RequestPostFormXML в методе CreateMVisaTrans, httpCode!=200, err==nil",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  BindGet3DSecureCode_1240 = 1240 [(epay_payout_code_value) = {
    message: "Ошибка Bind в методе Get3DSecure",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  RequestPostFormXMLGeCode_1241 = 1241 [(epay_payout_code_value) = {
    message: "Ошибка RequestPostFormXML в методе Get3DSecure, httpCode==200, err!=nil",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  RequestPostFormXMLGeCode_1242 = 1242 [(epay_payout_code_value) = {
    message: "Ошибка RequestPostFormXML в методе Get3DSecure, httpCode!=200, err!=nil",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  RequestPostFormXMLGeCode_1243 = 1243 [(epay_payout_code_value) = {
    message: "Ошибка RequestPostFormXML в методе Get3DSecure, возвращаемое body=nil",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  RequestPostFormXMLGeCode_1244 = 1244 [(epay_payout_code_value) = {
    message: "Ошибка RequestPostFormXML в методе Get3DSecure, httpCode!=200, err==nil",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  BindManageTransCode_1245 = 1245 [(epay_payout_code_value) = {
    message: "Ошибка Bind в методе ManageTrans",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  RequestPostFormXMLMaCode_1246 = 1246 [(epay_payout_code_value) = {
    message: "Ошибка RequestPostFormXML в методе ManageTrans, httpCode==200, err!=nil",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  RequestPostFormXMLMaCode_1247 = 1247 [(epay_payout_code_value) = {
    message: "Ошибка RequestPostFormXML в методе ManageTrans, httpCode!=200, err!=nil",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  RequestPostFormXMLMaCode_1248 = 1248 [(epay_payout_code_value) = {
    message: "Ошибка RequestPostFormXML в методе ManageTrans, возвращаемое body=nil",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  RequestPostFormXMLMaCode_1249 = 1249 [(epay_payout_code_value) = {
    message: "Ошибка RequestPostFormXML в методе ManageTrans, httpCode!=200, err==nil",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  BindCreateTransCode_1250 = 1250 [(epay_payout_code_value) = {
    message: "Ошибка Bind в методе CreateTrans",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  RequestPostFormXMLCrCode_1251 = 1251 [(epay_payout_code_value) = {
    message: "Ошибка RequestPostFormXML в методе CreateTrans, httpCode==200, err!=nil",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  RequestPostFormXMLCrCode_1252 = 1252 [(epay_payout_code_value) = {
    message: "Ошибка RequestPostFormXML в методе CreateTrans, httpCode!=200, err!=nil",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  RequestPostFormXMLCrCode_1253 = 1253 [(epay_payout_code_value) = {
    message: "Ошибка RequestPostFormXML в методе CreateTrans, возвращаемое body=nil",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  RequestPostFormXMLCrCode_1254 = 1254 [(epay_payout_code_value) = {
    message: "Ошибка RequestPostFormXML в методе CreateTrans, httpCode!=200, err==nil",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  BindRePaymentCode_1255 = 1255 [(epay_payout_code_value) = {
    message: "Ошибка Bind в методе RePayment",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  RequestPostFormXMLReCode_1256 = 1256 [(epay_payout_code_value) = {
    message: "Ошибка RequestPostFormXML в методе RePayment, httpCode==200, err!=nil",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  RequestPostFormXMLReCode_1257 = 1257 [(epay_payout_code_value) = {
    message: "Ошибка RequestPostFormXML в методе RePayment, httpCode!=200, err!=nil",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  RequestPostFormXMLReCode_1258 = 1258 [(epay_payout_code_value) = {
    message: "Ошибка RequestPostFormXML в методе RePayment, возвращаемое body=nil",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  RequestPostFormXMLReCode_1259 = 1259 [(epay_payout_code_value) = {
    message: "Ошибка RequestPostFormXML в методе RePayment, httpCode!=200, err==nil",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  BindGetTokenCode_1260 = 1260 [(epay_payout_code_value) = {
    message: "Ошибка Bind в методе GetToken",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  RequestPostFormXMLGeCode_1261 = 1261 [(epay_payout_code_value) = {
    message: "Ошибка RequestPostFormXML в методе GetToken, httpCode==200, err!=nil",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  RequestPostFormXMLGeCode_1262 = 1262 [(epay_payout_code_value) = {
    message: "Ошибка RequestPostFormXML в методе GetToken, httpCode!=200, err!=nil",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  RequestPostFormXMLGeCode_1263 = 1263 [(epay_payout_code_value) = {
    message: "Ошибка RequestPostFormXML в методе GetToken, возвращаемое body=nil",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  RequestPostFormXMLGeCode_1264 = 1264 [(epay_payout_code_value) = {
    message: "Ошибка RequestPostFormXML в методе GetToken, httpCode!=200, err==nil",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  BindTransactionCode_1265 = 1265 [(epay_payout_code_value) = {
    message: "Ошибка Bind в методе Transaction",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  RequestPostFormXMLTrCode_1266 = 1266 [(epay_payout_code_value) = {
    message: "Ошибка RequestPostFormXML в методе Transaction, httpCode==200, err!=nil",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  CardsystemcommunicatCode_1267 = 1267 [(epay_payout_code_value) = {
    message: "Card system communication error",
    transaction_status: 11,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  RequestPostFormXMLTrCode_1268 = 1268 [(epay_payout_code_value) = {
    message: "Ошибка RequestPostFormXML в методе Transaction, возвращаемое body=nil",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  RequestPostFormXMLTrCode_1269 = 1269 [(epay_payout_code_value) = {
    message: "Ошибка RequestPostFormXML в методе Transaction, httpCode!=200, err==nil",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  RequestPostFormXMLTrCode_1270 = 1270 [(epay_payout_code_value) = {
    message: "Ошибка RequestPostFormXML в методе Transaction, data.Result.Code != 00",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  BindTransactionConfiCode_1271 = 1271 [(epay_payout_code_value) = {
    message: "Ошибка Bind в методе TransactionConfirm",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  RequestPostFormXMLTrCode_1272 = 1272 [(epay_payout_code_value) = {
    message: "Ошибка RequestPostFormXML в методе TransactionConfirm, httpCode==200, err!=nil",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  RequestPostFormXMLTrCode_1273 = 1273 [(epay_payout_code_value) = {
    message: "Ошибка RequestPostFormXML в методе TransactionConfirm, httpCode!=200, err!=nil",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  RequestPostFormXMLTrCode_1274 = 1274 [(epay_payout_code_value) = {
    message: "Ошибка RequestPostFormXML в методе TransactionConfirm, возвращаемое body=nil",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  RequestPostFormXMLTrCode_1275 = 1275 [(epay_payout_code_value) = {
    message: "Ошибка RequestPostFormXML в методе TransactionConfirm, httpCode!=200, err==nil",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  InvoiceLinksGetInvoiCode_1276 = 1276 [(epay_payout_code_value) = {
    message: "Ошибка получения количества InvoiceLinks во время процесса GetInvoiceLinks ",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  PublicIDuserIDCode_1277 = 1277 [(epay_payout_code_value) = {
    message: "Ошибка получения publicID по userID токена из кэша при проверке токена",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  ParamsCode_1278 = 1278 [(epay_payout_code_value) = {
    message: "Ошибка получения params токена из кэша при проверке токена",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  PostlinkcorecallbackCode_1279 = 1279 [(epay_payout_code_value) = {
    message: "Ошибка отправки мгновенного ответа по postlink в core.callback",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  RabbitpostlinkCode_1280 = 1280 [(epay_payout_code_value) = {
    message: "Запрос в rabbit на отправку postlink успешно выполнен",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  PublicIDtokeninvoiceCode_1281 = 1281 [(epay_payout_code_value) = {
    message: "Ошибка записи publicID в кэш при получении token по invoice",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  SlowpostlinkcorecallCode_1282 = 1282 [(epay_payout_code_value) = {
    message: "Slow postlink успешно отправлен на core.callback",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  GetOpenWayIDHalykBonCode_1283 = 1283 [(epay_payout_code_value) = {
    message: "Ошибка getOpenWayID при использовании HalykBonus при оплате",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  CardTypenilCode_1284 = 1284 [(epay_payout_code_value) = {
    message: "CardType == nil при проведении оплаты",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  CardtypeCode_1285 = 1285 [(epay_payout_code_value) = {
    message: "Card type не был определен при оплате",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  PostlinkcorecallbackCode_1286 = 1286 [(epay_payout_code_value) = {
    message: "Быстрый postlink успешно отправлен на core.callback",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  PostlinkCode_1287 = 1287 [(epay_payout_code_value) = {
    message: "Ошибка получения postlink из кэша",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  PostlinkinvoiceIDCode_1288 = 1288 [(epay_payout_code_value) = {
    message: "Количество транзакций по терминалу из postlink с данным invoiceID равно нулю",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  CallbackcorecallbackCode_1289 = 1289 [(epay_payout_code_value) = {
    message: "Callback из core.callback в http-proxy отправлен успешно ",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  InvoiceLinkInvoiceIDCode_1290 = 1290 [(epay_payout_code_value) = {
    message: "Ошибка получения invoiceLink по InvoiceID при создании InvoiceLink",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  InvoiceLinkInvoiceIDCode_1291 = 1291 [(epay_payout_code_value) = {
    message: "InvoiceLink с таким InvoiceID уже существует",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  InvoiceLinkinvoiceLiCode_1293 = 1293 [(epay_payout_code_value) = {
    message: "Ошибка создания invoiceLink по причине существования invoiceLink с таким же invoiceID",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  ErrorMessageinvoiceLCode_1294 = 1294 [(epay_payout_code_value) = {
    message: "Ошибка распознавания структуры ошибки ErrorMessage при ошибке создания invoiceLink по причине существования invoiceLink с таким же invoiceID. Для подробностей ошибки см логи",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  TransactionStatusAUTCode_1295 = 1295 [(epay_payout_code_value) = {
    message: "Ошибка transaction Status не AUTH или CHARGE",
    transaction_status: 11,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  FilePathFileIDCode_1296 = 1296 [(epay_payout_code_value) = {
    message: "Ошибка генерации и сохранения файла выписки. FilePath или FileID пустой",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  BindChargeCode_1297 = 1297 [(epay_payout_code_value) = {
    message: "Ошибка Bind при проведении операции Charge ",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_1298 = 1298 [(epay_payout_code_value) = {
    message: "Транзакции отсутствуют при запросе из карточной БД",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  InformationStatementCode_1299 = 1299 [(epay_payout_code_value) = {
    message: "При получении выписки транзакций по датам при помощи запроса в Information.Statement был возвращен пустой массив данных",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  RefIDCode_1301 = 1301 [(epay_payout_code_value) = {
    message: "При получении транзакций по RefID был возвращен пустой массив",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  EmailCode_1302 = 1302 [(epay_payout_code_value) = {
    message: "Ошибка отправки email при создании выписки",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  StatementHistoryOmitCode_1303 = 1303 [(epay_payout_code_value) = {
    message: "Ошибка обновления StatementHistory с Omit при выполнении Worker",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  StaffListCode_1304 = 1304 [(epay_payout_code_value) = {
    message: "Ошибка имя параметра StaffList",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  GETCode_1305 = 1305 [(epay_payout_code_value) = {
    message: "Невозможно получить запрос GET",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  AuthorizedRequestJSOCode_1306 = 1306 [(epay_payout_code_value) = {
    message: "Ошибка при запросе AuthorizedRequestJSON в Core.Merchant",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_1307 = 1307 [(epay_payout_code_value) = {
    message: "Неправильно введен параметр (ошибка параметра)",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  CoreinvoiceinvoiceLiCode_1308 = 1308 [(epay_payout_code_value) = {
    message: "Ошибка отправки запроса в core.invoice при изменении статуса invoiceLink",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  CoreinvoiceinvoiceLiCode_1309 = 1309 [(epay_payout_code_value) = {
    message: "Ошибка отправки запроса в core.invoice при изменении статуса invoiceLink. Ошибка произошла внутри core.invoice",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  InvoiceLinkCode_1310 = 1310 [(epay_payout_code_value) = {
    message: "Ошибка получения статуса транзакции при смене статуса invoiceLink",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  InvoiceLinkinvoiceIDCode_1311 = 1311 [(epay_payout_code_value) = {
    message: "Ошибка получения invoiceLink по invoiceID ",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  CoreinvoiceinvoiceInCode_1312 = 1312 [(epay_payout_code_value) = {
    message: "Ошибка отправки запроса в core.invoice на получение invoice по InvoiceID ",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  KafkaCode_1315 = 1315 [(epay_payout_code_value) = {
    message: "Ошибка записи сообщения в kafka",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  HomebankaCode_1317 = 1317 [(epay_payout_code_value) = {
    message: "Ошибка получения токена Homebank-a при отправке почты",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_1318 = 1318 [(epay_payout_code_value) = {
    message: "Ошибка парсинга даты при создании выписки. Указанная дата неверна",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  UseraCode_1319 = 1319 [(epay_payout_code_value) = {
    message: "Ошибка записи user-a в кэш ",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  StaffupdatepasswordCode_1320 = 1320 [(epay_payout_code_value) = {
    message: "Ошибка при получении токена для staff update password ",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  UpdateStaffRoleCode_1321 = 1321 [(epay_payout_code_value) = {
    message: "Ошибка, админ не может сменить собственную роль UpdateStaffRole",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_1322 = 1322 [(epay_payout_code_value) = {
    message: "Ошибка подготовки запроса для получения таблицы выписки при создании выписки",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  COMMITCode_1323 = 1323 [(epay_payout_code_value) = {
    message: "Ошибка COMMIT при создании выписки",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_1324 = 1324 [(epay_payout_code_value) = {
    message: "Ошибка, админ не может удалить собственную учетную запись.",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_1325 = 1325 [(epay_payout_code_value) = {
    message: "Ошибка выполнения запроса для получения таблицы выписки при создании выписки",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_1326 = 1326 [(epay_payout_code_value) = {
    message: "Ошибка считывания данных из процедуры",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Float64FeeAmountstriCode_1327 = 1327 [(epay_payout_code_value) = {
    message: "Ошибка парсинга float64 FeeAmount из  string поля FeeAmount из transactionDateStatement ",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Float64SettlAmountstCode_1328 = 1328 [(epay_payout_code_value) = {
    message: "Ошибка парсинга float64 SettlAmount из string поля SettlAmount из transactionDateStatement",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Float64TrAmountstrinCode_1329 = 1329 [(epay_payout_code_value) = {
    message: "Ошибка парсинга float64 TrAmount из string поля TrAmount из transactionDateStatement",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_1330 = 1330 [(epay_payout_code_value) = {
    message: "Ошибка создания запроса при скачивании файла",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_1331 = 1331 [(epay_payout_code_value) = {
    message: "Ошибка выполнения запроса при скачивании файла",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  ParseMediaTypeCode_1332 = 1332 [(epay_payout_code_value) = {
    message: "Ошибка ParseMediaType при скачивании файла",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  CreateFileCode_1333 = 1333 [(epay_payout_code_value) = {
    message: "Ошибка CreateFile при скачивании файла",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  EmailCode_1334 = 1334 [(epay_payout_code_value) = {
    message: "Ошибка создания токена для отправки email",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  HBCode_1335 = 1335 [(epay_payout_code_value) = {
    message: "Ошибка получения токена HB",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_1336 = 1336 [(epay_payout_code_value) = {
    message: "Ошибка преобразования скачиваемого файла в байты",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_1337 = 1337 [(epay_payout_code_value) = {
    message: "Ошибка при обновлении пользователя (приглашения сотрудника)",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  ACTIVECode_1338 = 1338 [(epay_payout_code_value) = {
    message: "Ошибка получения статуса ACTIVE по имени",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  CHARGEDCode_1339 = 1339 [(epay_payout_code_value) = {
    message: "Ошибка получения статуса CHARGED по имени",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  MerchantterminaltermCode_1340 = 1340 [(epay_payout_code_value) = {
    message: "Ошибка получения merchant по terminal при получении terminal по ShopID",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  PostingDatestringOrdCode_1341 = 1341 [(epay_payout_code_value) = {
    message: "Ошибка парсинга PostingDate из string поля OrderDate из transactionDateStatement",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  HBCode_1342 = 1342 [(epay_payout_code_value) = {
    message: "Ошибка при получении токена (HB)",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  FTPCode_1343 = 1343 [(epay_payout_code_value) = {
    message: "Ошибка подключение к серверу (FTP)",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  JobExecutiongocronCode_1344 = 1344 [(epay_payout_code_value) = {
    message: "Ошибка job Execution gocron",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Float64FeeAmountstriCode_1346 = 1346 [(epay_payout_code_value) = {
    message: "Ошибка парсинга float64 FeeAmount из string поля FeeAmount из transactionDateStatement",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Float64SettlAmountstCode_1347 = 1347 [(epay_payout_code_value) = {
    message: "Ошибка парсинга float64 SettlAmount из string поля SettlAmount из transactionDateStatement",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Float64TrAmountstrinCode_1348 = 1348 [(epay_payout_code_value) = {
    message: "Ошибка парсинга float64 TrAmount из string поля TrAmount из transactionDateStatement",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  MerchantPublicIDCode_1361 = 1361 [(epay_payout_code_value) = {
    message: "Ошибка получения merchant-а по PublicID",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  MerchantInfoPublicIDCode_1366 = 1366 [(epay_payout_code_value) = {
    message: "Ошибка получения merchantInfo по PublicID и токену",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  PublicIDCode_1367 = 1367 [(epay_payout_code_value) = {
    message: "Ошибка получения персонального PublicID сотрудника.",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_1368 = 1368 [(epay_payout_code_value) = {
    message: "Доступ с данным параметрам отказан",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_1369 = 1369 [(epay_payout_code_value) = {
    message: "Ошибка получения запроса в БД",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_1370 = 1370 [(epay_payout_code_value) = {
    message: "Ошибка, проверьте корректность имя параметра.",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  AuthorizedRequestJSOCode_1371 = 1371 [(epay_payout_code_value) = {
    message: "Ошибка при запросе AuthorizedRequestJSON в Core.getCard",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  CoreGetCardCode_1372 = 1372 [(epay_payout_code_value) = {
    message: "Ошибка, проверьте корректность имя параметра, при запросе в core.GetCard",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_1373 = 1373 [(epay_payout_code_value) = {
    message: "Запись не найдена!",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  OauthCode_1374 = 1374 [(epay_payout_code_value) = {
    message: "Ошибка обновления статуса oauth",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  RequestClientCode_1375 = 1375 [(epay_payout_code_value) = {
    message: "Ошибка при парсинге входных данных в структуру requestClient",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_1376 = 1376 [(epay_payout_code_value) = {
    message: "Ошибка создания клиента (коммерсанта)",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_1378 = 1378 [(epay_payout_code_value) = {
    message: "Клиент уже существует.",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  ScopeCode_1379 = 1379 [(epay_payout_code_value) = {
    message: "Scope уже существует",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  ScopeCode_1380 = 1380 [(epay_payout_code_value) = {
    message: "Ошибка при создание scope при заведение клиента",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  ClientScopesCode_1381 = 1381 [(epay_payout_code_value) = {
    message: "Ошибка создание ClientScopes",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_1382 = 1382 [(epay_payout_code_value) = {
    message: "При получении статистики транзакций за текущий день произошла ошибка",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_1383 = 1383 [(epay_payout_code_value) = {
    message: "При получении статистики транзакций по картам за текущий день",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_1384 = 1384 [(epay_payout_code_value) = {
    message: "При получении статистики по статусам произошла ошибка",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_1385 = 1385 [(epay_payout_code_value) = {
    message: "При получении статистики транзакций по картам за все дни кроме текущего произошла ошибка",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_1386 = 1386 [(epay_payout_code_value) = {
    message: "При получении статистики транзакций по картам за текущий день",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  HomeBankCode_1387 = 1387 [(epay_payout_code_value) = {
    message: "Ошибка при получение токена от HomeBank для отправки почты",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  HMCode_1388 = 1388 [(epay_payout_code_value) = {
    message: "Ошибка при отправке коммерсанту на почту для доступа HM",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  RequestClientUpdateCode_1389 = 1389 [(epay_payout_code_value) = {
    message: "Ошибка при парсинге входных данных в структуру requestClientUpdate",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  ClientIDCode_1390 = 1390 [(epay_payout_code_value) = {
    message: "Ошибка имя параметра ClientID",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  GetClientclientCode_1391 = 1391 [(epay_payout_code_value) = {
    message: "Ошибка запроса GetClient, client не найден",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  GetClientScopeCode_1392 = 1392 [(epay_payout_code_value) = {
    message: "Ошибка запроса в БД GetClientScope",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  UpdateScopeCode_1393 = 1393 [(epay_payout_code_value) = {
    message: "Ошибка обновление UpdateScope",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  ScopesCode_1394 = 1394 [(epay_payout_code_value) = {
    message: "Ошибка, не удалось удалить scopes",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  ClientCode_1395 = 1395 [(epay_payout_code_value) = {
    message: "Ошибка при отправке коммерсанту на почту обновленный Client ",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  ScopeCode_1396 = 1396 [(epay_payout_code_value) = {
    message: "Ошибка при создание scope при обновления клиента",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  ClientScopesCode_1397 = 1397 [(epay_payout_code_value) = {
    message: "Ошибка, не удалось удалить ClientScopes",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  ReadererrorCode_1398 = 1398 [(epay_payout_code_value) = {
    message: "Reader error",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  TerminaldbmerchantIDCode_1400 = 1400 [(epay_payout_code_value) = {
    message: "Ошибка получения terminal из db по merchantID",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  ClientclientIDdbTildCode_1403 = 1403 [(epay_payout_code_value) = {
    message: "Ошибка получения client по clientID из db при платеже по Tilda",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  ClientclientIDdbCode_1405 = 1405 [(epay_payout_code_value) = {
    message: "Ошибка получения client по clientID из db",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  ClientIPTildaCode_1406 = 1406 [(epay_payout_code_value) = {
    message: "Ошибка получения адреса домена по clientIP при платеже Tilda",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  BindmultipartformdatCode_1407 = 1407 [(epay_payout_code_value) = {
    message: "Ошибка Bind multipart form/data",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  TildaTildaCode_1408 = 1408 [(epay_payout_code_value) = {
    message: "Ошибка проверка валидности запроса из Tilda при платеже по Tilda",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  TildaCode_1409 = 1409 [(epay_payout_code_value) = {
    message: "Ошибка получения токена для оплаты при платеже по Tilda",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Float64amountTildaCode_1410 = 1410 [(epay_payout_code_value) = {
    message: "Ошибка парсинга float64 amount при платеже по Tilda",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  AuthorizedRequestJSOCode_1411 = 1411 [(epay_payout_code_value) = {
    message: "Ошибка отправки AuthorizedRequestJSON запроса в Core.Dictionary на получение source_list",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  SourceCode_1412 = 1412 [(epay_payout_code_value) = {
    message: "Ошибка проверки source, указанного для создания токена",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  CallbackcorecallbackCode_1413 = 1413 [(epay_payout_code_value) = {
    message: "Ошибка отправки callback-а из core.callback в http-proxy",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  PostlinkTildaCode_1414 = 1414 [(epay_payout_code_value) = {
    message: "Ошибка отправки postlink на Tilda",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_1415 = 1415 [(epay_payout_code_value) = {
    message: "Ошибка, проверьте корректность параметра.",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_1416 = 1416 [(epay_payout_code_value) = {
    message: "Ошибка получения транзакции при запросе в БД",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  AuthorizedRequestJSOCode_1418 = 1418 [(epay_payout_code_value) = {
    message: "Ошибка отправки AuthorizedRequestJSON запроса в Core.Statement на получение TransactionStatus",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  CoreStatementCode_1419 = 1419 [(epay_payout_code_value) = {
    message: "Ошибка, проверьте корректность имя параметра, при запросе в core.Statement",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_1421 = 1421 [(epay_payout_code_value) = {
    message: "ошибка получения профиля",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_1422 = 1422 [(epay_payout_code_value) = {
    message: "Ошибка получения профиля",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  MerchantcompanynameCode_1423 = 1423 [(epay_payout_code_value) = {
    message: "Ошибка поиска merchant по company name",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  ClientTypeCode_1424 = 1424 [(epay_payout_code_value) = {
    message: "Ошибка проверки клиента. ClientType не соответствует запрашиваемому токену",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  ClientTypenameTildaCode_1425 = 1425 [(epay_payout_code_value) = {
    message: "Ошибка поиска ClientType по name при платеже по Tilda",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  ClientTypeTildaClienCode_1426 = 1426 [(epay_payout_code_value) = {
    message: "Ошибка проверки ClientType при платеже по Tilda. ClientType не соответствует типу платежа",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  BindJSONBindJSONCode_1427 = 1427 [(epay_payout_code_value) = {
    message: "Ошибка BindJSON. Невозможно демаршалировать структуру с помощью BindJSON",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  ParsingerrorCode_1428 = 1428 [(epay_payout_code_value) = {
    message: "Parsing error",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_1429 = 1429 [(epay_payout_code_value) = {
    message: "Ошибка запроса по получению информации по коммерсанту",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_1430 = 1430 [(epay_payout_code_value) = {
    message: "Отсутствует наименование поля необходимое для осуществления поиска. По данному полю будет производится поиск в БД.",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_1431 = 1431 [(epay_payout_code_value) = {
    message: "Отсутствуют параметры для поиска",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  LIKECode_1432 = 1432 [(epay_payout_code_value) = {
    message: "Произошла ошибка при использование оператора LIKE . Некорректные параметры.",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  BETWEENCode_1433 = 1433 [(epay_payout_code_value) = {
    message: "Произошла ошибка при использование оператора BETWEEN. Указано не достаточное количество параметров",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_1434 = 1434 [(epay_payout_code_value) = {
    message: "Некорректные параметры для поиска",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_1436 = 1436 [(epay_payout_code_value) = {
    message: "Метод поиска отсутствует",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_1437 = 1437 [(epay_payout_code_value) = {
    message: "Период транзакций является обязательным параметром поиска",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_1438 = 1438 [(epay_payout_code_value) = {
    message: "Ошибка при получение данных из БД",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  CountCode_1439 = 1439 [(epay_payout_code_value) = {
    message: "Ошибка просчета (count) в БД ",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_1440 = 1440 [(epay_payout_code_value) = {
    message: "не смогли получить операции мерчанта",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_1441 = 1441 [(epay_payout_code_value) = {
    message: "При получении информации о коммерсанте произошла ошибка",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  ReferenceEPAYCode_1442 = 1442 [(epay_payout_code_value) = {
    message: "Ошибка получения данных по reference из базы EPAY для формирования выписки по дате транзакции",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_1443 = 1443 [(epay_payout_code_value) = {
    message: "Ошибка получения транзакций для графика при получении статистики",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_1444 = 1444 [(epay_payout_code_value) = {
    message: "Ошибка получения массива данных по статусам при получении статистики",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_1445 = 1445 [(epay_payout_code_value) = {
    message: "ошибка при запросе в БД",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  HMHMBCode_1446 = 1446 [(epay_payout_code_value) = {
    message: "Ошибка, нет соответствия префикса (HM_ или HMB_)",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  JSONJSONCode_1447 = 1447 [(epay_payout_code_value) = {
    message: "Ошибка, невозможно распарсить JSON структуру, проверьте правильность JSON",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  UrlbodyCode_1448 = 1448 [(epay_payout_code_value) = {
    message: "Ошибка списания платежа. Суммы в url и body запроса отличаются",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_1449 = 1449 [(epay_payout_code_value) = {
    message: "Ошибка списания платежа. Сумма списания не является числом",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  GetUseScopeByClientCode_1451 = 1451 [(epay_payout_code_value) = {
    message: "Ошибка, запроса в БД GetUseScopeByClient",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_1452 = 1452 [(epay_payout_code_value) = {
    message: "Ошибка списания платежа. Невозможно считать тело запроса",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  JSONUnmarshalCode_1453 = 1453 [(epay_payout_code_value) = {
    message: "Ошибка списания платежа. JSON Unmarshal вернул ошибку",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_1454 = 1454 [(epay_payout_code_value) = {
    message: "Отказано, клиент в токене не совпадает с клиентом в транзакции",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_1455 = 1455 [(epay_payout_code_value) = {
    message: "При поиске магазина произошла ошибка",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  WebsocketCode_1457 = 1457 [(epay_payout_code_value) = {
    message: "Websocket соединение закрыто с ошибкой",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  WebsocketCode_1458 = 1458 [(epay_payout_code_value) = {
    message: "ошибка чтения из websocket соединения",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  WebsocketCode_1459 = 1459 [(epay_payout_code_value) = {
    message: "ошибка чтения из websocket соединения",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  WebsocketCode_1460 = 1460 [(epay_payout_code_value) = {
    message: "Websocket соединение неожиданно закрылось",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  WebsocketCode_1461 = 1461 [(epay_payout_code_value) = {
    message: "Websocket соединение вернуло ошибку при чтении сообщения",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  WebsocketCode_1462 = 1462 [(epay_payout_code_value) = {
    message: "Websocket соединение вернуло ошибку при записи сообщения",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_1463 = 1463 [(epay_payout_code_value) = {
    message: "Установлено соединение с новым клиентом",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_1464 = 1464 [(epay_payout_code_value) = {
    message: "Установлено новое соединение с клиентом. Старое соединение разорвано",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_1465 = 1465 [(epay_payout_code_value) = {
    message: "Соединение с клиентом разорвано",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_1466 = 1466 [(epay_payout_code_value) = {
    message: "Сообщение отослано клиенту",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_1467 = 1467 [(epay_payout_code_value) = {
    message: "Не удалось найти клиента и отослать сообщение",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  UpdateMerchantCode_1468 = 1468 [(epay_payout_code_value) = {
    message: "Ошибка при запросе в БД UpdateMerchant",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  UpdateMerchantShopCode_1469 = 1469 [(epay_payout_code_value) = {
    message: "Ошибка при запросе в БД UpdateMerchantShop",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  UpdateMerchantTerminCode_1470 = 1470 [(epay_payout_code_value) = {
    message: "Ошибка при запросе в БД UpdateMerchantTerminal",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_1471 = 1471 [(epay_payout_code_value) = {
    message: "Ошибка получения статистики по банкам",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_1472 = 1472 [(epay_payout_code_value) = {
    message: "Ошибка получения статистики по компаниям",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  EmailCode_1473 = 1473 [(epay_payout_code_value) = {
    message: "Ошибка обновления выписки в БД при отправке выписки по email при формировании ",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  NameserviceCode_1475 = 1475 [(epay_payout_code_value) = {
    message: "Ошибка получения статуса по name при редактирование коммерсанта на уровне service",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  IdparamsCode_1476 = 1476 [(epay_payout_code_value) = {
    message: "отсутствует id params в параметре на получение коммерсанта.",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  MerchantpublicIDCode_1477 = 1477 [(epay_payout_code_value) = {
    message: "Ошибка получения merchant по publicID ",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  TerminalmerchantIDCode_1478 = 1478 [(epay_payout_code_value) = {
    message: "Ошибка получения terminal по merchantID",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  ShopidshopwebserviceCode_1479 = 1479 [(epay_payout_code_value) = {
    message: "Ошибка получения shop по id при редактирование shop на уровне webservice",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_1480 = 1480 [(epay_payout_code_value) = {
    message: "Не пройдена валидация даты при получении статистики по банкам",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_1481 = 1481 [(epay_payout_code_value) = {
    message: "Не пройдена валидация даты при получении статистики по компаниям",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  IdparamsCode_1482 = 1482 [(epay_payout_code_value) = {
    message: "отсутствует id params в параметре на получение коммерсанта",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  JSONJSONCode_1483 = 1483 [(epay_payout_code_value) = {
    message: "Ошибка, невозможно распарсить JSON структуру, проверьте правильность JSON",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  JSONjsonMarshalJSONCode_1484 = 1484 [(epay_payout_code_value) = {
    message: "Невозможно преобразовать JSON (json.Marshal), проверьте правильность JSON объекта.",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  IdparamsCode_1486 = 1486 [(epay_payout_code_value) = {
    message: "Отсутствует id params в параметре на получение терминала",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_1487 = 1487 [(epay_payout_code_value) = {
    message: "Ошибка парсинга входящих данных при получении дней месяца",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_1488 = 1488 [(epay_payout_code_value) = {
    message: "Ошибка парсинга даты при получении дней месяца",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_1489 = 1489 [(epay_payout_code_value) = {
    message: "Не пройдена валидация даты при получении статистики по банкам",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_1490 = 1490 [(epay_payout_code_value) = {
    message: "Не пройдена валидация даты при получении статистики по компаниям",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  PaymentRequestCode_1491 = 1491 [(epay_payout_code_value) = {
    message: "Ошибка создания PaymentRequest при нулевой транзакции по собственному терминалу",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_1492 = 1492 [(epay_payout_code_value) = {
    message: "Ошибка расшифровки криптограммы при нулевой транзакции по собственному терминалу",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  JsonUnmarshalCode_1493 = 1493 [(epay_payout_code_value) = {
    message: "Ошибка json.Unmarshal криптограммы при нулевой транзакции по собственному терминалу",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  JsonMarshalCode_1494 = 1494 [(epay_payout_code_value) = {
    message: "Ошибка json.Marshal при подготовке массива байтов данных для отправки запроса на платеж при нулевой транзакции по собственному терминалу",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_1495 = 1495 [(epay_payout_code_value) = {
    message: "Ошибка отправки запроса на платеж при нулевой транзакции по собственному терминалу",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  JSONJSONCode_1496 = 1496 [(epay_payout_code_value) = {
    message: "Ошибка, невозможно распарсить JSON структуру, проверьте правильность JSON",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_1497 = 1497 [(epay_payout_code_value) = {
    message: "Ошибка отправки запроса на получение списка терминалов коммерсанта при нулевой транзакции по собственному терминалу",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_1498 = 1498 [(epay_payout_code_value) = {
    message: "Ошибка проверки валидности терминала при нулевой транзакции по собственному терминалу",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  AuthorizedRequestJSOCode_1499 = 1499 [(epay_payout_code_value) = {
    message: "Ошибка отправки AuthorizedRequestJSON запроса в api.online-bank  на создания QR-code",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  AuthorizedRequestJSOCode_1500 = 1500 [(epay_payout_code_value) = {
    message: "Ошибка отправки AuthorizedRequestJSON запроса в api.online-bank на получения QR-code статуса",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  AuthorizedRequestJSOCode_1502 = 1502 [(epay_payout_code_value) = {
    message: "Ошибка отправки AuthorizedRequestJSON запроса в qr-service (api.onlinebank.kz) на создания QR-code",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  AuthorizedRequestJSOCode_1503 = 1503 [(epay_payout_code_value) = {
    message: "Ошибка отправки AuthorizedRequestJSON запроса в qr-service (api.onlinebank.kz) на получения QR-code статуса",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  AuthorizedRequestJSOCode_1504 = 1504 [(epay_payout_code_value) = {
    message: "Ошибка отправки AuthorizedRequestJSON запроса в core.qr на получения QR-code статуса",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  TerminaluuidCode_1505 = 1505 [(epay_payout_code_value) = {
    message: "Ошибка запроса в БД на получения terminal по uuid",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  BindgrafanaCode_1506 = 1506 [(epay_payout_code_value) = {
    message: "Ошибка Bind при получении токена для grafana",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  HttpgrafanaCode_1507 = 1507 [(epay_payout_code_value) = {
    message: "Ошибка отправки Http запроса на получение токена для grafana",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  UnauthorizedCode_1508 = 1508 [(epay_payout_code_value) = {
    message: "Ошибка Unauthorized при проверке токена",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  BadtokenCode_1509 = 1509 [(epay_payout_code_value) = {
    message: "Ошибка Bad token при проверке токена",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  JsonUnmarshalCode_1510 = 1510 [(epay_payout_code_value) = {
    message: "Ошибка json.Unmarshal при получении данных из кэша при получении статистики по банкам",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  JsonMarshalCode_1511 = 1511 [(epay_payout_code_value) = {
    message: "Ошибка json.Marshal при подготовке данных для записи в кэш при получении статистики по банкам",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  RequestJSONgeoIPCode_1512 = 1512 [(epay_payout_code_value) = {
    message: "Ошибка отправки RequestJSON запроса в geoIP на получения местоположения",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_1513 = 1513 [(epay_payout_code_value) = {
    message: "Ошибка записи в кэш при получении статистики по банкам",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  JsonUnmarshalCode_1514 = 1514 [(epay_payout_code_value) = {
    message: "Ошибка json.Unmarshal при получении данных из кэша при получении статистики по компаниям",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  JsonMarshalCode_1515 = 1515 [(epay_payout_code_value) = {
    message: "Ошибка json.Marshal при подготовке данных для записи в кэш при получении статистики по компаниям",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_1516 = 1516 [(epay_payout_code_value) = {
    message: "Ошибка записи в кэш при получении статистики по компаниям",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  QrTransactionInfoCode_1517 = 1517 [(epay_payout_code_value) = {
    message: "Ошибка проверки qrTransactionInfo",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  CardTypeIDCode_1518 = 1518 [(epay_payout_code_value) = {
    message: "Ошибка запроса в БД на получения CardTypeID по имени",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  CurrencyIDCode_1519 = 1519 [(epay_payout_code_value) = {
    message: "Ошибка запроса в БД на получения CurrencyID по имени",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  StatusIDCode_1520 = 1520 [(epay_payout_code_value) = {
    message: "Ошибка запроса в БД на получения StatusID по имени",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_1521 = 1521 [(epay_payout_code_value) = {
    message: "Ошибка парсинга входящих данных при получении названий компаний",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_1522 = 1522 [(epay_payout_code_value) = {
    message: "Ошибка получения статистики по компаниям при получении названий компаний",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_1523 = 1523 [(epay_payout_code_value) = {
    message: "Ошибка парсинга входящих данных при получении статистики по коммерсанту",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_1524 = 1524 [(epay_payout_code_value) = {
    message: "Ошибка разбивания даты на периоды при получении статистики по коммерсанту",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  ShopIDmerchantIDclieCode_1525 = 1525 [(epay_payout_code_value) = {
    message: "Ошибка запроса в БД на получения shopID и merchantID по clientID",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_1526 = 1526 [(epay_payout_code_value) = {
    message: "Ошибка получения статистики по компаниям при получении статистики по коммерсанту",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  QRCode_1527 = 1527 [(epay_payout_code_value) = {
    message: "Ошибка создания QR транзакции",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  SourceListIDCode_1528 = 1528 [(epay_payout_code_value) = {
    message: "Ошибка запроса в БД на получения SourceListID по имени",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  AuthorizedRequestJSOCode_1529 = 1529 [(epay_payout_code_value) = {
    message: "Ошибка отправки AuthorizedRequestJSON запроса в core.getCard на получения cardID и tokenCard",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  XLSIDCode_1530 = 1530 [(epay_payout_code_value) = {
    message: "Ошибка поиска транзакций по XLSID при получении выписки за период",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  JSONJSONCode_1531 = 1531 [(epay_payout_code_value) = {
    message: "Ошибка, невозможно распарсить JSON структуру, проверьте правильность JSON",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  RedispartialTransactCode_1532 = 1532 [(epay_payout_code_value) = {
    message: "Ошибка получение данных с redis (partialTransactionData)",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_1533 = 1533 [(epay_payout_code_value) = {
    message: "Ошибка получения бинов банков при получении статистики по банкам",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_1534 = 1534 [(epay_payout_code_value) = {
    message: "Ошибка получения статистики по банкам при получении суммы транзакций по банкам",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_1535 = 1535 [(epay_payout_code_value) = {
    message: "Ошибка парсинга входящих данных при получении суммы транзакций по банкам",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_1536 = 1536 [(epay_payout_code_value) = {
    message: "Ошибка получения статистики по банкам при получении количества транзакций по банкам",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_1537 = 1537 [(epay_payout_code_value) = {
    message: "Ошибка парсинга входящих данных при получении количества транзакций по банкам",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  RedisinvoiceIdCode_1538 = 1538 [(epay_payout_code_value) = {
    message: "Ошибка получение данных с redis (invoiceId)",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Epay1Code_1539 = 1539 [(epay_payout_code_value) = {
    message: "Ошибка парсинга входящих данных при получении статистики по банкам из epay1 ",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Epay1Code_1540 = 1540 [(epay_payout_code_value) = {
    message: "Ошибка парсинга входящих данных при получении статистики по компаниям из epay1 ",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Epay1Code_1541 = 1541 [(epay_payout_code_value) = {
    message: "Ошибка получения данных из БД при получении статистики по банкам из epay1",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Epay1Code_1542 = 1542 [(epay_payout_code_value) = {
    message: "Ошибка получения данных из БД при получении статистики по компаниям из epay1",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  TransactionTypeIDCode_1543 = 1543 [(epay_payout_code_value) = {
    message: "Ошибка запроса в БД на получения transactionTypeID по имени",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  ClientTypeIDCode_1544 = 1544 [(epay_payout_code_value) = {
    message: "Ошибка запроса в БД на получения clientTypeID по имени (создания клиента)",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  JsonMarshalucsCode_1545 = 1545 [(epay_payout_code_value) = {
    message: "Ошибка json.Marshal при подготовке данных для отправки почты через ucs сервис.",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_1546 = 1546 [(epay_payout_code_value) = {
    message: "Не удалось сохранить запись в БД",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  ScopeCode_1547 = 1547 [(epay_payout_code_value) = {
    message: "Ошибка получение scope по имени",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  RedisqrstatusCode_1548 = 1548 [(epay_payout_code_value) = {
    message: "Ошибка записи данных в redis (qr status)",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  AuthorizedRequestJSOCode_1549 = 1549 [(epay_payout_code_value) = {
    message: "Ошибка отправки AuthorizedRequestJSON запроса в core.qr на получения QR-code для оплаты",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_1550 = 1550 [(epay_payout_code_value) = {
    message: "Ошибка чтения тела запроса",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  RedisrealIPCode_1551 = 1551 [(epay_payout_code_value) = {
    message: "Ошибка получение данных с redis (realIP)",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  RequestJSONcoreqrQRSCode_1552 = 1552 [(epay_payout_code_value) = {
    message: "Ошибка отправки RequestJSON запроса в core.qr для отправки данных QRStatusKafka",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  KafkaReadMessageCode_1553 = 1553 [(epay_payout_code_value) = {
    message: "Ошибка чтение сообщении в kafka (ReadMessage)",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  JsonUnmarshalkafkameCode_1554 = 1554 [(epay_payout_code_value) = {
    message: "Ошибка json.Unmarshal при получении данных из kafka message",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  JSONjsonMarshalJSONCode_1555 = 1555 [(epay_payout_code_value) = {
    message: "Ошибка невозможно преобразовать JSON (json.Marshal), проверьте правильность JSON объекта",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  KafkaCode_1556 = 1556 [(epay_payout_code_value) = {
    message: "Ошибка закрытие сессии с kafka",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  SetOffsetkafkaCode_1557 = 1557 [(epay_payout_code_value) = {
    message: "Ошибка SetOffset kafka",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_1558 = 1558 [(epay_payout_code_value) = {
    message: "Ошибка в создании записи отправителя",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_1559 = 1559 [(epay_payout_code_value) = {
    message: "Ошибка в создании записи получателя",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_1560 = 1560 [(epay_payout_code_value) = {
    message: "Ошибка в создании транзакции",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_1561 = 1561 [(epay_payout_code_value) = {
    message: "Ошибка чтения тела запроса",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  HTTPCode_1562 = 1562 [(epay_payout_code_value) = {
    message: "Ошибка в создании HTTP запроса",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  HTTPCode_1563 = 1563 [(epay_payout_code_value) = {
    message: "Ошибка выполнения HTTP запроса",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  HTTPCode_1564 = 1564 [(epay_payout_code_value) = {
    message: "Получен HTTP-ответ с неожиданным кодом",
    transaction_status: 11,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  HTTPCode_1565 = 1565 [(epay_payout_code_value) = {
    message: "Ошибка чтения тела ответа HTTP запроса",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  AUTHCode_1566 = 1566 [(epay_payout_code_value) = {
    message: "Нельзя выполнить операцию так как транзакция не имеет статус AUTH",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  EGateWayCode_1567 = 1567 [(epay_payout_code_value) = {
    message: "Не удалось создать запрос к EGateWay",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  EGateWayCode_1568 = 1568 [(epay_payout_code_value) = {
    message: "Не удалось обработать ответ EGateWay",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_1569 = 1569 [(epay_payout_code_value) = {
    message: "Не удалось обновить транзакцию",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  JsonCode_1570 = 1570 [(epay_payout_code_value) = {
    message: "Не удалось собрать json ответ",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_1571 = 1571 [(epay_payout_code_value) = {
    message: "Ошибка получения статистики по банкам из собранных данных",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_1572 = 1572 [(epay_payout_code_value) = {
    message: "Ошибка парсинга входящих данных",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_1578 = 1578 [(epay_payout_code_value) = {
    message: "Ошибка получения токена при получении статистики по банкам",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_1579 = 1579 [(epay_payout_code_value) = {
    message: "Ошибка получения токена при получении статистики по компаниям",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  ReadAllfromRequestBoCode_1604 = 1604 [(epay_payout_code_value) = {
    message: "Ошибка ReadAll from Request Body при refund",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  ParseFloatrefundCode_1605 = 1605 [(epay_payout_code_value) = {
    message: "Ошибка ParseFloat при refund",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  AmountbodyurlrefundCode_1606 = 1606 [(epay_payout_code_value) = {
    message: "Amount не совпадают в body и url при refund",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_1607 = 1607 [(epay_payout_code_value) = {
    message: "Ошибка получения статистики по компаниям из собранных данных",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  ChargeCode_1608 = 1608 [(epay_payout_code_value) = {
    message: "Конфликт взаимодействия с транзакцией при charge. Транзакция заблокирована, так как процессится",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  CancelCode_1609 = 1609 [(epay_payout_code_value) = {
    message: "Конфликт взаимодействия с транзакцией при cancel. Транзакция заблокирована, так как процессится",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  RefundCode_1610 = 1610 [(epay_payout_code_value) = {
    message: "Конфликт взаимодействия с транзакцией при refund. Транзакция заблокирована, так как процессится",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  HttpCode_1611 = 1611 [(epay_payout_code_value) = {
    message: "Ошибка http запроса в сервис бинов на получение информации по бину при проведении платежа",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_1612 = 1612 [(epay_payout_code_value) = {
    message: "Не пройдена проверка по разрешенным и запрещенным странам коммерсанта на терминале",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_1614 = 1614 [(epay_payout_code_value) = {
    message: "Не пройдена проверка по разрешенным и запрещенным странам коммерсанта на терминале при проведении пустого платежа",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  TerminalIDCode_1615 = 1615 [(epay_payout_code_value) = {
    message: "Ошибка получения терминала по TerminalID при проведении пустого платежа",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_1616 = 1616 [(epay_payout_code_value) = {
    message: "Ошибка получения информации по бин-у из кэша при проверке бина по странам",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  JsonUnmarshalbinCode_1617 = 1617 [(epay_payout_code_value) = {
    message: "Ошибка json.Unmarshal при парсинге структуры bin-а из кэша при проверке бина по странам",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  JsonMarshalCode_1618 = 1618 [(epay_payout_code_value) = {
    message: "Ошибка json.Marshal при записи в кэш бина при проверке бина по странам",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_1619 = 1619 [(epay_payout_code_value) = {
    message: "Ошибка записи в кэш при проверке бина по странам",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  JSONapiosuvoxCode_1620 = 1620 [(epay_payout_code_value) = {
    message: "Ошибка JSON парсинга при отправки данных api.osuvox",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  ApiosuvoxCode_1621 = 1621 [(epay_payout_code_value) = {
    message: "При вызове api.osuvox произошла ошибка",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  ApiosuvoxHTTPCODE200Code_1622 = 1622 [(epay_payout_code_value) = {
    message: "При вызове api.osuvox HTTP CODE не равен 200",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_1623 = 1623 [(epay_payout_code_value) = {
    message: "Ошибка удаления дубликатов по компаниям из БД",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  SourceListCode_1624 = 1624 [(epay_payout_code_value) = {
    message: "Ошибка получения sourceList по имени",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_1636 = 1636 [(epay_payout_code_value) = {
    message: "Ошибка! Не удалось установить владельца карты",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_1637 = 1637 [(epay_payout_code_value) = {
    message: "Ошибка! Указанная карта не принадлежит пользователю",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  OsuvoxCode_1638 = 1638 [(epay_payout_code_value) = {
    message: "Ошибка при проверке возможности оплаты через osuvox",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  InvalidCardIDCode_1639 = 1639 [(epay_payout_code_value) = {
    message: "Invalid CardID",
    transaction_status: 5,
    integration_error: InvalidCard,
  }];
  OsuvoxCode_1640 = 1640 [(epay_payout_code_value) = {
    message: "Не удалось оплатить через osuvox",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_1641 = 1641 [(epay_payout_code_value) = {
    message: "Не удалось обновить транзакцию",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  OsuvoxCode_1642 = 1642 [(epay_payout_code_value) = {
    message: "Ошибка при оплате через osuvox",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  SingleMessageSchemeCode_1650 = 1650 [(epay_payout_code_value) = {
    message: "Ошибка запроса в БД на получения SingleMessageScheme",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_1651 = 1651 [(epay_payout_code_value) = {
    message: "Ошибка запроса в БД на получения транзакции",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  OTPCode_1652 = 1652 [(epay_payout_code_value) = {
    message: "Ошибка при отправке OTP",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  OTPCode_1653 = 1653 [(epay_payout_code_value) = {
    message: "Ошибка! Нужна OTP верификация",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  OTPcodeCode_1654 = 1654 [(epay_payout_code_value) = {
    message: "Ошибка! Не удалось верифицировать OTP code",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  OTPcodeCode_1655 = 1655 [(epay_payout_code_value) = {
    message: "Ошибка! Не верный OTP code",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  RedisterminaluuidinvCode_1656 = 1656 [(epay_payout_code_value) = {
    message: "Ошибка получение данных с redis по terminal uuid и invoiceId",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  InvalidterminalIdCode_1658 = 1658 [(epay_payout_code_value) = {
    message: "Invalid terminalId",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  InvalidAmoutCode_1659 = 1659 [(epay_payout_code_value) = {
    message: "Invalid Amout",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_1660 = 1660 [(epay_payout_code_value) = {
    message: "Не удалось подтвердить платеж",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_1661 = 1661 [(epay_payout_code_value) = {
    message: "Не удалось вернуть платеж",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  AuthorizedRequestJSOCode_1663 = 1663 [(epay_payout_code_value) = {
    message: "Ошибка отправки AuthorizedRequestJSON запроса в api.osuvox на получения CardInfo",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  BindGetTokenByCardCode_1664 = 1664 [(epay_payout_code_value) = {
    message: "Ошибка Bind в методе GetTokenByCard",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  RequestPostFormXMLGeCode_1665 = 1665 [(epay_payout_code_value) = {
    message: "Ошибка RequestPostFormXML в методе GetTokenByCard, httpCode==200, err!=nil",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  RequestPostFormXMLGeCode_1666 = 1666 [(epay_payout_code_value) = {
    message: "Ошибка RequestPostFormXML в методе GetTokenByCard, httpCode!=200, err!=nil",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  RequestPostFormXMLGeCode_1667 = 1667 [(epay_payout_code_value) = {
    message: "Ошибка RequestPostFormXML в методе GetTokenByCard, возвращаемое body=nil",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  RequestPostFormXMLGeCode_1668 = 1668 [(epay_payout_code_value) = {
    message: "Ошибка RequestPostFormXML в методе GetTokenByCard, httpCode!=200, err==nil",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Codebase64stringbaseCode_1670 = 1670 [(epay_payout_code_value) = {
    message: "Ошибка деcodeинга base64 string в base64 []byte",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  AuthorizedRequestJSOCode_1671 = 1671 [(epay_payout_code_value) = {
    message: "Ошибка отправки AuthorizedRequestJSON запроса в egateway.api",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  CardCode_1672 = 1672 [(epay_payout_code_value) = {
    message: "Ошибка запроса в БД на получения Card по имени",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  HomebankPayOsuvoxCarCode_1673 = 1673 [(epay_payout_code_value) = {
    message: "При типе оплаты homebankPay, OsuvoxCardId не должен быть пустым",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  OsuvoxCode_1675 = 1675 [(epay_payout_code_value) = {
    message: "Ошибка при оплате через Osuvox",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  OsuvoxCode_1677 = 1677 [(epay_payout_code_value) = {
    message: "Ошибка при оплате через Osuvox",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  OsuvoxCode_1679 = 1679 [(epay_payout_code_value) = {
    message: "Ошибка при отмене платежа в Osuvox",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  OsuvoxCode_1680 = 1680 [(epay_payout_code_value) = {
    message: "Ошибка при работе с Osuvox. Неверные входные данные",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  OsuvoxCode_1681 = 1681 [(epay_payout_code_value) = {
    message: "Ошибка при работе с Osuvox. Не удалось создать документ",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_1682 = 1682 [(epay_payout_code_value) = {
    message: "Ошибка при валидации данных для возврата. Транзакция не доступна для возврата",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  OsuvoxCode_1683 = 1683 [(epay_payout_code_value) = {
    message: "Ошибка при полном возврате через Osuvox",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  OsuvoxCode_1685 = 1685 [(epay_payout_code_value) = {
    message: "Ошибка при частичном возврате через Osuvox",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  TransactionCode_1690 = 1690 [(epay_payout_code_value) = {
    message: "Ошибка запроса в БД на получения Transaction по имени",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  CouldnotgetphoneNumbCode_1692 = 1692 [(epay_payout_code_value) = {
    message: "Could not get phoneNumber from HB token",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_1693 = 1693 [(epay_payout_code_value) = {
    message: "Ошибка записи в буффер файла отчета",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_1694 = 1694 [(epay_payout_code_value) = {
    message: "Ошибка чтения файла отчета из буффера",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  OsuvoxCode_1695 = 1695 [(epay_payout_code_value) = {
    message: "Ошибка при оплате через Osuvox",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  XMLbodyCode_1699 = 1699 [(epay_payout_code_value) = {
    message: "Ошибка считывания XML body при проверке сертификата",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  XMLMarhsalswitchpaymCode_1700 = 1700 [(epay_payout_code_value) = {
    message: "Ошибка XML Marhsal при switch payment",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  TerminalIdCode_1703 = 1703 [(epay_payout_code_value) = {
    message: "Не удалось получить терминал по данному terminalId",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  TerminalXLSMIDterminCode_1704 = 1704 [(epay_payout_code_value) = {
    message: "terminal.XLSMID или terminal.XLSTID пустые",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  IbanCode_1705 = 1705 [(epay_payout_code_value) = {
    message: "Возникла ошибка при получение Iban",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  IbanCode_1706 = 1706 [(epay_payout_code_value) = {
    message: "Iban не должен быть пустым",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  TerminalIdCode_1707 = 1707 [(epay_payout_code_value) = {
    message: "terminalId не должен быть пустым",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  IbanCode_1708 = 1708 [(epay_payout_code_value) = {
    message: "Не удалось получить бонусы по Iban",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_1709 = 1709 [(epay_payout_code_value) = {
    message: "Ошибка создания элемента в списке валют",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_1710 = 1710 [(epay_payout_code_value) = {
    message: "Ошибка обновления элемента в списке валют",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_1711 = 1711 [(epay_payout_code_value) = {
    message: "Ошибка получения списка валют",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_1712 = 1712 [(epay_payout_code_value) = {
    message: "Ошибка получения элемента из списка валют",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  InvoiceIdCode_1713 = 1713 [(epay_payout_code_value) = {
    message: "Параметр invoiceId не должен быть пустым",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_1714 = 1714 [(epay_payout_code_value) = {
    message: "Не удалось обновить транзакцию",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  AUTHCode_1715 = 1715 [(epay_payout_code_value) = {
    message: "Отмена транзакции доступна только со статусом AUTH",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  PaymentTypeCode_1716 = 1716 [(epay_payout_code_value) = {
    message: "Указанного PaymentType не существует",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  OsuvoxCode_1717 = 1717 [(epay_payout_code_value) = {
    message: "Не удалось получить сведения о карте в системе Osuvox",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  PaymentTypeCode_1719 = 1719 [(epay_payout_code_value) = {
    message: "Не удалось получить справочник PaymentType",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  PaymentSystemCode_1720 = 1720 [(epay_payout_code_value) = {
    message: "не удалось получить справочник PaymentSystem",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_1723 = 1723 [(epay_payout_code_value) = {
    message: "При получении контактов коммерсанта произошла ошибка",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_1724 = 1724 [(epay_payout_code_value) = {
    message: "При получении токена произошла ошибка для отправки почты",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_1759 = 1759 [(epay_payout_code_value) = {
    message: "Не удалось провести транзакцию",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_1760 = 1760 [(epay_payout_code_value) = {
    message: "Некорректные входные данные",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_1761 = 1761 [(epay_payout_code_value) = {
    message: "Некорректные входные данные",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_1762 = 1762 [(epay_payout_code_value) = {
    message: "Не удалось создать заказ",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  ShopIdCode_1763 = 1763 [(epay_payout_code_value) = {
    message: "Параметр shopId не должен быть пустым",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_1764 = 1764 [(epay_payout_code_value) = {
    message: "Не удалось получить статус",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  OrderIdCode_1765 = 1765 [(epay_payout_code_value) = {
    message: "Параметр orderId не должен быть пустым",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  CoreMerchantCode_1771 = 1771 [(epay_payout_code_value) = {
    message: "Не удалось распознать ответ от Core.Merchant",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  PostlinkcorecallbackCode_1780 = 1780 [(epay_payout_code_value) = {
    message: "Ошибка отправки быстрого запроса по postlink на core.callback",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  CardTypenilCode_1781 = 1781 [(epay_payout_code_value) = {
    message: "CardType == nil при проведении оплаты",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  SwitchpaymentCode_1786 = 1786 [(epay_payout_code_value) = {
    message: "Ошибка получения токена при switch payment",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_1787 = 1787 [(epay_payout_code_value) = {
    message: "Ошибка получения токена при инициализации словаря",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  HttpGetMerchantCertiCode_1788 = 1788 [(epay_payout_code_value) = {
    message: "Ошибка http запроса GetMerchantCertificateStatuses при обращении в core.migration ",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  HttpGetMerchantCreatCode_1789 = 1789 [(epay_payout_code_value) = {
    message: "Ошибка http запроса GetMerchantCreatedStatuses при обращении в core.migration",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  EnabledCode_1849 = 1849 [(epay_payout_code_value) = {
    message: "Неверный параметр enabled",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  ShopCode_1850 = 1850 [(epay_payout_code_value) = {
    message: "Не удалось обновить shop",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_1855 = 1855 [(epay_payout_code_value) = {
    message: "Не удалось получить отчет",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  TerminalIDCode_1856 = 1856 [(epay_payout_code_value) = {
    message: "Не удалось найти terminal по ID",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  ShopIDCode_1857 = 1857 [(epay_payout_code_value) = {
    message: "Не удалось получить shop по ID",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  IDCode_1858 = 1858 [(epay_payout_code_value) = {
    message: "Ошибка при получении терминала по ID",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  ShopidCode_1859 = 1859 [(epay_payout_code_value) = {
    message: "Ошибка при получении времени жизни токена по shop id",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  TokenExpireInSecondsCode_1860 = 1860 [(epay_payout_code_value) = {
    message: "Не удалось получить TokenExpireInSeconds по terminal id",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Apiepay1Code_1899 = 1899 [(epay_payout_code_value) = {
    message: "Ошибка при запросе статистики по терминалу из api.epay1",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  RequestCode_1900 = 1900 [(epay_payout_code_value) = {
    message: "Ошибка при создании переменной *Request",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_1901 = 1901 [(epay_payout_code_value) = {
    message: "Ошибка при получении токена",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_1902 = 1902 [(epay_payout_code_value) = {
    message: "Ошибка при чтении ответа",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  JSONCode_1903 = 1903 [(epay_payout_code_value) = {
    message: "Ошибка при преобразовании тела ответа в JSON",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  IdserviceCode_1944 = 1944 [(epay_payout_code_value) = {
    message: "Ошибка получения контакты коммерсанта по id при редактирование коммерсанта на уровне service",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  UpdateMerchantContacCode_1945 = 1945 [(epay_payout_code_value) = {
    message: "Ошибка при запросе в БД UpdateMerchantContacts",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  ContactTypenameserviCode_1946 = 1946 [(epay_payout_code_value) = {
    message: "Ошибка получения contactType по name при редактирование коммерсанта на уровне service",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_1954 = 1954 [(epay_payout_code_value) = {
    message: "Не удалось найти магазины",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_1955 = 1955 [(epay_payout_code_value) = {
    message: "Не валидные данные",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_1956 = 1956 [(epay_payout_code_value) = {
    message: "Не удалось создать упрощенную заявку",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_1961 = 1961 [(epay_payout_code_value) = {
    message: "Не удалось получить статус транзакции",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_1962 = 1962 [(epay_payout_code_value) = {
    message: "Не удалось получить мерчанта ",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_1964 = 1964 [(epay_payout_code_value) = {
    message: "Ошибка получения списка терминалов по токену",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  TerminalshopNameCode_1965 = 1965 [(epay_payout_code_value) = {
    message: "Ошибка получения списка terminal по shopName при получении списка терминалов",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_1970 = 1970 [(epay_payout_code_value) = {
    message: "Не заполнены данные перевода в токене",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  InvoiceIDCode_1971 = 1971 [(epay_payout_code_value) = {
    message: "InvoiceID не совпадает в токене и запросе",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Amount0Code_1975 = 1975 [(epay_payout_code_value) = {
    message: "Amount меньше или равен 0",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_1976 = 1976 [(epay_payout_code_value) = {
    message: "Не удалось спарсить дату",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_1977 = 1977 [(epay_payout_code_value) = {
    message: "Не удалось получить данные для отчета",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  TokenexpiredtryagainCode_1979 = 1979 [(epay_payout_code_value) = {
    message: "Token expired, try again",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  MPANCode_1980 = 1980 [(epay_payout_code_value) = {
    message: "отсутствует MPAN, введите корректные данные",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_2008 = 2008 [(epay_payout_code_value) = {
    message: "Оплата бонусами в долларах США запрещена",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  RecordnotfoundCode_2009 = 2009 [(epay_payout_code_value) = {
    message: "record not found",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_2015 = 2015 [(epay_payout_code_value) = {
    message: "сумма возврата не соответствует требованиям",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_2016 = 2016 [(epay_payout_code_value) = {
    message: "Попробуйте позже",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  RedismaxReqPerSecCode_2017 = 2017 [(epay_payout_code_value) = {
    message: "Не установлен лимит запросов в Redis по ключу maxReqPerSec",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_2018 = 2018 [(epay_payout_code_value) = {
    message: "Операция недоступна для данного банка",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  GooglePayCode_2028 = 2028 [(epay_payout_code_value) = {
    message: "Ошибка при инициализации данных для расшифровки GooglePay токена",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  GooglePayCode_2030 = 2030 [(epay_payout_code_value) = {
    message: "Ошибка при попытке расшифровать GooglePay токен, номер карты пуст",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  AccountIDCode_2031 = 2031 [(epay_payout_code_value) = {
    message: "Невозможно сохранить карту при пустом accountID",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  CallbackCode_2032 = 2032 [(epay_payout_code_value) = {
    message: "Не удалось отправить Callback",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_2033 = 2033 [(epay_payout_code_value) = {
    message: "Срок действия карты пуст либо заполнен неверно",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_2046 = 2046 [(epay_payout_code_value) = {
    message: "Не удалось получить список операций",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  JSONCode_2048 = 2048 [(epay_payout_code_value) = {
    message: "Ошибка JSON парсинга",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_2049 = 2049 [(epay_payout_code_value) = {
    message: "Ошибка получения профайл сотрудника",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_2050 = 2050 [(epay_payout_code_value) = {
    message: "Ошибка получения ролей сотрудников",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_2051 = 2051 [(epay_payout_code_value) = {
    message: "Ошибка получения список сотрудников",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  XLSURLCode_2052 = 2052 [(epay_payout_code_value) = {
    message: "Не удалось выполнить запрос на XLSURL",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_2053 = 2053 [(epay_payout_code_value) = {
    message: "Ошибка при редактирование сотрудника",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  JSONUpdateProfileStaCode_2057 = 2057 [(epay_payout_code_value) = {
    message: "Ошибка при парсинге входных данных JSON в UpdateProfileStaff",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  JSONCreateStaffProfiCode_2058 = 2058 [(epay_payout_code_value) = {
    message: "Ошибка при парсинге входных данных JSON в CreateStaffProfile",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_2059 = 2059 [(epay_payout_code_value) = {
    message: "Ошибка запроса при создание сотрудников",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_2060 = 2060 [(epay_payout_code_value) = {
    message: "Ошибка запроса при обновление сотрудников",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  InvalidamountamountmCode_2061 = 2061 [(epay_payout_code_value) = {
    message: "Invalid amount, amount must not exceed transaction amount",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  CorebusinessreportCode_2067 = 2067 [(epay_payout_code_value) = {
    message: "Ошибка при запросе статистики по терминалу из core.business.report",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_2069 = 2069 [(epay_payout_code_value) = {
    message: "Некорректная дата",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  EmailCode_2070 = 2070 [(epay_payout_code_value) = {
    message: "Некорректный email",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  GinBindCode_2074 = 2074 [(epay_payout_code_value) = {
    message: "gin Bind",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  XLSTIDXLSMIDCode_2075 = 2075 [(epay_payout_code_value) = {
    message: "отсутствует XLST_ID или XLSM_ID",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_2076 = 2076 [(epay_payout_code_value) = {
    message: "Ошибка парсинга сертификата или срока действия",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  MPGSCode_2085 = 2085 [(epay_payout_code_value) = {
    message: "ошибка MPGS при обновлении транзакции в БД",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  MPGSCode_2086 = 2086 [(epay_payout_code_value) = {
    message: "ошибка MPGS при обновлении транзакции в БД",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  MPGSCode_2087 = 2087 [(epay_payout_code_value) = {
    message: "срок действия карты пустой / заполнен неверно MPGS",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  MPGSCode_2088 = 2088 [(epay_payout_code_value) = {
    message: "ошибка MPGS при обновлении транзакции в БД",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  MPGSCode_2089 = 2089 [(epay_payout_code_value) = {
    message: "ошибка MPGS при обновлении транзакции в БД",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  MPGSCode_2090 = 2090 [(epay_payout_code_value) = {
    message: "ошибка MPGS при обновлении транзакции в БД",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  PANBINCode_2091 = 2091 [(epay_payout_code_value) = {
    message: "Не удалось проверить ограничения по PAN/BIN",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  BINorPANrestrictedCode_2092 = 2092 [(epay_payout_code_value) = {
    message: "BIN or PAN restricted",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  BindCode_2122 = 2122 [(epay_payout_code_value) = {
    message: "ошибка Bind",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_2123 = 2123 [(epay_payout_code_value) = {
    message: "Транзакция заблокирована, так как процессится",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_2124 = 2124 [(epay_payout_code_value) = {
    message: "не удалось получить терминал",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  MerchantCode_2125 = 2125 [(epay_payout_code_value) = {
    message: "не удалось получить merchant",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  MerchantCode_2126 = 2126 [(epay_payout_code_value) = {
    message: "merchant не активный",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_2127 = 2127 [(epay_payout_code_value) = {
    message: "не удалось определить терминал комерсанта",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  ThreeCode_2128 = 2128 [(epay_payout_code_value) = {
  message: "ошибка: более 3 попыток",
      transaction_status: 5,
      integration_error: TransactionDeclinedByAcquirer,
      }];
  Code_2129 = 2129 [(epay_payout_code_value) = {
    message: "не удалось определить терминал продавца",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  OpenwayIDCode_2130 = 2130 [(epay_payout_code_value) = {
    message: "не удалось проверить openwayID",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  USDCode_2131 = 2131 [(epay_payout_code_value) = {
    message: "оплата бонусами в USD запрещена",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_2132 = 2132 [(epay_payout_code_value) = {
    message: "бонусный счет заблокирован",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_2133 = 2133 [(epay_payout_code_value) = {
    message: "не удалось создать транзакцию",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_2134 = 2134 [(epay_payout_code_value) = {
    message: "не удалось обновить транзакцию",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_2135 = 2135 [(epay_payout_code_value) = {
    message: "не удалось обновить транзакцию",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  XlsCode_2136 = 2136 [(epay_payout_code_value) = {
    message: "не удалось отправить запрос в xls",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_2137 = 2137 [(epay_payout_code_value) = {
    message: "не удалось выполнить десериализацию",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  PostlinkcorecallbackCode_2138 = 2138 [(epay_payout_code_value) = {
    message: "не удалось отправить postlink в core.callback",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_2139 = 2139 [(epay_payout_code_value) = {
    message: "Ошибка, ссылка уже существует с таким номером заказа.",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  CryptogramopenwayIDCode_2146 = 2146 [(epay_payout_code_value) = {
    message: "ошибка: существует как cryptogram, так и openwayID во входных данных",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  IPCode_2147 = 2147 [(epay_payout_code_value) = {
    message: "не удалось получить IP-адрес",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_2148 = 2148 [(epay_payout_code_value) = {
    message: "не удалось обновить базу данных",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  FormdatasftpproxyCode_2152 = 2152 [(epay_payout_code_value) = {
    message: "При создании параметра в form-data для отправки данных в sftp.proxy произошла ошибка",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  HttpNewRequestsftpprCode_2153 = 2153 [(epay_payout_code_value) = {
    message: "При создании http.NewRequest для отправки данных в sftp.proxy произошла ошибка",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  RedispipelineCode_2154 = 2154 [(epay_payout_code_value) = {
    message: "ошибка redis pipeline",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  TransactionamountshoCode_2156 = 2156 [(epay_payout_code_value) = {
    message: "transaction amount should be more than 10 KZT",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_2157 = 2157 [(epay_payout_code_value) = {
    message: "сумма возврата не соответствует требованиям",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  URLpagesizeCode_2158 = 2158 [(epay_payout_code_value) = {
    message: "В URL нет параметра page или size",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_2159 = 2159 [(epay_payout_code_value) = {
    message: "Слишком много параметров поиска",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  URLpagesizeCode_2191 = 2191 [(epay_payout_code_value) = {
    message: "В URL нет параметра page или size",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  PagesizeCode_2193 = 2193 [(epay_payout_code_value) = {
    message: "Параметры page и size должны быть целым числом",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  PagesizeCode_2194 = 2194 [(epay_payout_code_value) = {
    message: "Параметры page и size должны быть целым числом",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_2200 = 2200 [(epay_payout_code_value) = {
    message: "не удалось оновить статус юзера",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_2203 = 2203 [(epay_payout_code_value) = {
    message: "Не удалось узнать страну карты.",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_2204 = 2204 [(epay_payout_code_value) = {
    message: "Не удалось проверить БИН.",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_2205 = 2205 [(epay_payout_code_value) = {
    message: "Ошибка при регистрации иностранной карты.",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  UnmarshalCode_2206 = 2206 [(epay_payout_code_value) = {
    message: "не удалось Unmarshal",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_2207 = 2207 [(epay_payout_code_value) = {
    message: "не удалось получить токен",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_2210 = 2210 [(epay_payout_code_value) = {
    message: "Ошибка при регистрации международной карты.",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  PublicIDonboardingCode_2211 = 2211 [(epay_payout_code_value) = {
    message: "не удалось отправить publicID в onboarding",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  AclserviceCode_2212 = 2212 [(epay_payout_code_value) = {
    message: "не удалось получить роли из acl service",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  StaffCode_2213 = 2213 [(epay_payout_code_value) = {
    message: "не удалось создать staff",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  OpenWayCardIdCode_2214 = 2214 [(epay_payout_code_value) = {
    message: "На терминале запрещена оплата по OpenWayCardId",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_2218 = 2218 [(epay_payout_code_value) = {
    message: "не удалось создать клиента.",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_2237 = 2237 [(epay_payout_code_value) = {
    message: "Не удалось найти транзакцию.",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  PostlinkaCode_2238 = 2238 [(epay_payout_code_value) = {
    message: "Не удалось изменить статус postlink'a",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  PostlinkaCode_2239 = 2239 [(epay_payout_code_value) = {
    message: "Не удалось изменить статус postlink'a",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  PostlinkaCode_2240 = 2240 [(epay_payout_code_value) = {
    message: "Не удалось изменить статус postlink'a",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  PostlinkaCode_2241 = 2241 [(epay_payout_code_value) = {
    message: "Не удалось изменить статус postlink'a",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  PostlinkCode_2242 = 2242 [(epay_payout_code_value) = {
    message: "Postlink не найден",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_2243 = 2243 [(epay_payout_code_value) = {
    message: "Статус транзакции не найден",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_2244 = 2244 [(epay_payout_code_value) = {
    message: "Ошибка при поиске транзакции",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_2245 = 2245 [(epay_payout_code_value) = {
    message: "Не удалось удалить сохраненную карту",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_2249 = 2249 [(epay_payout_code_value) = {
    message: "Произошла непредвиденная ошибка. Попробуйте повторить операцию позже",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  P2PCode_2250 = 2250 [(epay_payout_code_value) = {
    message: "Ошибка при P2P переводе",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_2257 = 2257 [(epay_payout_code_value) = {
    message: "Ошибка БД при поиске транзакции",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  JSONjsonMarshalJSONCode_2268 = 2268 [(epay_payout_code_value) = {
    message: "Невозможно преобразовать JSON (json.Marshal), проверьте правильность JSON объекта.",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_2271 = 2271 [(epay_payout_code_value) = {
    message: "Карта не активна",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_2301 = 2301 [(epay_payout_code_value) = {
    message: "Ошибка при получении информации о банке",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_2305 = 2305 [(epay_payout_code_value) = {
    message: "Ошибка при попытке сохранить карты при переводе",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  QrbyqrCode_2322 = 2322 [(epay_payout_code_value) = {
    message: "запрет генерации qr по by_qr",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_2323 = 2323 [(epay_payout_code_value) = {
    message: "ошибка запроса в БД",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_2326 = 2326 [(epay_payout_code_value) = {
    message: "Карта не найдена или деактивирована",
    transaction_status: 5,
    integration_error: InvalidCard,
  }];
  CardIDCode_2339 = 2339 [(epay_payout_code_value) = {
    message: "Ошибка получения карты по cardID",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_2349 = 2349 [(epay_payout_code_value) = {
    message: "не удалось отправить в кафку",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_2350 = 2350 [(epay_payout_code_value) = {
    message: "не удалось сериализовать",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_2351 = 2351 [(epay_payout_code_value) = {
    message: "не удалось обновить статус",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_2352 = 2352 [(epay_payout_code_value) = {
    message: "ошибка при обновлении статуса транзакции в БД",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_2353 = 2353 [(epay_payout_code_value) = {
    message: "не удалось десериализовать",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_2354 = 2354 [(epay_payout_code_value) = {
    message: "Некорректный терминал",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  ClientIDCode_2355 = 2355 [(epay_payout_code_value) = {
    message: "Коммерсант не найден по clientID",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_2356 = 2356 [(epay_payout_code_value) = {
    message: "У коммерсанта нет терминалов",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  ResultCodestatusIDCode_2360 = 2360 [(epay_payout_code_value) = {
    message: "отсутствует resultCode или statusID",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  PaymentsystemCode_2362 = 2362 [(epay_payout_code_value) = {
    message: "При вызове payment system произошла ошибка",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  KafkaCode_2365 = 2365 [(epay_payout_code_value) = {
    message: "Не удалось отправить сообщение в kafka",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  ShopInfoCode_2366 = 2366 [(epay_payout_code_value) = {
    message: "ошибка при получении shopInfo",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_2367 = 2367 [(epay_payout_code_value) = {
    message: "Не удалось получить детальную информацию по коммерсанту",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_2433 = 2433 [(epay_payout_code_value) = {
    message: "Ошибка, при получении статуса постлинка транзакции",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  UZGWCode_2435 = 2435 [(epay_payout_code_value) = {
    message: "При вызове UZGW произошла ошибка",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  Code_2624 = 2624 [(epay_payout_code_value) = {
    message: "частичный возврат запрещен",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  ChecktransactionstatCode_2660 = 2660 [(epay_payout_code_value) = {
    message: "check transaction status",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
    CorePaymentCode_2678 = 2678 [(epay_payout_code_value) = {
  message: "Превышен суточный лимит на терминале 400 Core.Payment",
      transaction_status: 5,
      integration_error: TransactionDeclinedByAcquirer,
      }];
    CorePaymentCode_2679 = 2679 [(epay_payout_code_value) = {
  message: "Не удалось получить текущий суточный лимит 400 Core.Payment",
      transaction_status: 5,
      integration_error: TransactionDeclinedByAcquirer,
      }];
  OtpcodeapiuzgatewayCode_2704 = 2704 [(epay_payout_code_value) = {
    message: "ошибка отправки создание otp codeа api.uz-gateway",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  CodeapiuzgatewayCode_2705 = 2705 [(epay_payout_code_value) = {
    message: "ошибка верификации codeа api.uz-gateway",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  IncorrectcurrencyCode_101 = 101 [(epay_payout_code_value) = {
    message: "Incorrect currency",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
  DonotreattemptrestriCode_2872 = 2872 [(epay_payout_code_value) = {
    message: "Do not reattempt (restricted error code)",
    transaction_status: 5,
    integration_error: TransactionDeclinedByAcquirer,
  }];
}