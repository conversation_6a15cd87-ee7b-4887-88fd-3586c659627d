package test

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"testing"

	"github.com/stretchr/testify/assert"

	"git.local/sensitive/local/internal/domain"
	"git.local/sensitive/local/internal/smoketesting/core/payout"
)

func TestPayOut(t *testing.T) {
	t.Skip()

	testData := payout.TestData()
	projectReferenceID := generateSecretKey()

	hash, transactionID := MakePrimalPayOut(t, &domain.PrimalPayoutRequest{
		MerchantId:         devMerchantID,
		ProjectId:          devProjectID,
		ProjectClientId:    devProjectClientID,
		Amount:             10.0,
		FailureRedirectUrl: "https://www.youtube.com",
		SuccessRedirectUrl: "https://www.youtube.com",
		CallbackUrl:        devCallbackUrl,
		Description:        "kambartest",
		ProjectReferenceId: projectReferenceID,
		AdditionalData: map[string]string{
			"bonus": "0",
		},
		IsHold:         false,
		ProjectOrderID: "2",
	}, nil, http.StatusOK)

	for _, data := range testData {
		t.Run(data.Name, func(t *testing.T) {
			MakePayout(&domain.MakePayOutRequest{
				TransactionHash: hash,
				TransactionID:   transactionID,
				EncryptedPan:    "AAAAAAAAAAAAAAAAAAAAAPqixmy2pa6syHU9o6+9scCc0SEYNBZFGznJ55CnV8JA",
			}, t, &domain.Expected[*domain.MakePayOutResponse]{
				HttpStatusCode: http.StatusOK,
				Message:        "Success",
				Status:         true,
				Data: &domain.MakePayOutResponse{
					TransactionID:         transactionID,
					TransactionStatusCode: "success",
				},
			})
		})
	}
}

func MakePayout(req *domain.MakePayOutRequest, t *testing.T, expected *domain.Expected[*domain.MakePayOutResponse]) {
	body, err := json.Marshal(req)

	if err != nil {
		t.Fatal(err)
	}

	request, err := http.NewRequest(
		"POST",
		domainURL+"/transaction/api/v1/transaction/pay-out",
		bytes.NewBuffer(body))
	if err != nil {
		t.Fatal(err)
	}

	request.Header.Add("Content-Type", "application/json")

	response, err := http.DefaultClient.Do(request)
	if err != nil {
		panic(err)
	}
	defer response.Body.Close()

	respBody, err := io.ReadAll(response.Body)
	if err != nil {
		t.Fatal(err)
	}
	defer response.Body.Close()

	var res domain.Response[*domain.MakePayOutResponse]
	if err = json.Unmarshal(respBody, &res); err != nil {
		t.Fatal(err)
	}

	fmt.Println("status code", response.StatusCode)

	assert.Equal(t, expected.Data.TransactionID, res.Result.TransactionID, "transactionID")
	assert.Equal(t, expected.Data.TransactionStatusCode, res.Result.TransactionStatusCode, "transactionStatusCode")
}
