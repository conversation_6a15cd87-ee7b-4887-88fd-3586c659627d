// Code generated by protoc-gen-go-mvp. DO NOT EDIT.
// versions:
// - protoc-gen-go-mvp v2.0.1
// created by mvp authors

package grpc

import (
	context "context"
	fmt "fmt"
	cntx "git.local/sensitive/mvp/pkg/cntx"
	ctxzap "github.com/grpc-ecosystem/go-grpc-middleware/logging/zap/ctxzap"
	zap "go.uber.org/zap"
	grpc "google.golang.org/grpc"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
)

func file_inner_processing_grpc_card_proto_string_String(
	label string,
	in map[string]string,
) zap.Field {
	var res []zap.Field
	for key, val := range in {
		res = append(res, zap.Any(fmt.Sprint(key), val))
	}

	return zap.Dict(label, res...)
}

func file_inner_processing_grpc_card_proto_message_EmptyToZap(
	label string,
	in *emptypb.Empty,
) zap.Field {
	if in == nil {
		return zap.Reflect(label, in)
	}

	return zap.Dict(
		label,
	)
}

func file_inner_processing_grpc_card_proto_message_TimestampToZap(
	label string,
	in *timestamppb.Timestamp,
) zap.Field {
	if in == nil {
		return zap.Reflect(label, in)
	}

	return zap.Dict(
		label,
		zap.Any("Seconds", in.GetSeconds()),
		zap.Any("Nanos", in.GetNanos()),
	)
}

func file_inner_processing_grpc_card_proto_message_TerminalProjectV1ToZap(
	label string,
	in *TerminalProjectV1,
) zap.Field {
	if in == nil {
		return zap.Reflect(label, in)
	}

	return zap.Dict(
		label,
		zap.Any("Id", in.GetId()),
		zap.Any("ProjectId", in.GetProjectId()),
		zap.Any("TransactionTypeId", in.GetTransactionTypeId()),
		zap.Any("TerminalId", in.GetTerminalId()),
	)
}

func file_inner_processing_grpc_card_proto_message_TerminalProjectV1SliceToZap(
	label string,
	in []*TerminalProjectV1,
) zap.Field {
	var res []zap.Field
	for index, val := range in {
		res = append(res, file_inner_processing_grpc_card_proto_message_TerminalProjectV1ToZap(fmt.Sprint(index), val))
	}

	return zap.Dict(label, res...)
}

func file_inner_processing_grpc_card_proto_message_CardTokenV1ToZap(
	label string,
	in *CardTokenV1,
) zap.Field {
	if in == nil {
		return zap.Reflect(label, in)
	}

	return zap.Dict(
		label,
		zap.Any("Id", in.GetId()),
		zap.Any("AcquirerId", in.GetAcquirerId()),
		zap.Any("TerminalId", in.GetTerminalId()),
		zap.Any("CardId", in.GetCardId()),
		zap.Any("Token", "[***hidden***]"),
	)
}

func file_inner_processing_grpc_card_proto_message_CheckClientActivenessRequestV1ToZap(
	label string,
	in *CheckClientActivenessRequestV1,
) zap.Field {
	if in == nil {
		return zap.Reflect(label, in)
	}

	return zap.Dict(
		label,
		zap.Any("ProjectId", in.GetProjectId()),
		zap.Any("ProjectClientId", in.GetProjectClientId()),
	)
}

func file_inner_processing_grpc_card_proto_message_ClientCardToZap(
	label string,
	in *ClientCard,
) zap.Field {
	if in == nil {
		return zap.Reflect(label, in)
	}

	return zap.Dict(
		label,
		zap.Any("Id", in.GetId()),
		zap.Any("IpsId", in.GetIpsId()),
		zap.Any("MaskedPan", in.GetMaskedPan()),
		zap.Any("CountryId", in.GetCountryId()),
		zap.Any("IssuerId", in.GetIssuerId()),
	)
}

func file_inner_processing_grpc_card_proto_message_CreateClientRequestV1ToZap(
	label string,
	in *CreateClientRequestV1,
) zap.Field {
	if in == nil {
		return zap.Reflect(label, in)
	}

	return zap.Dict(
		label,
		zap.Any("ProjectId", in.GetProjectId()),
		zap.Any("ProjectClientId", in.GetProjectClientId()),
		zap.Any("Email", in.GetEmail()),
		zap.Any("Phone", "[***hidden***]"),
		zap.Any("Ip", in.GetIp()),
		zap.Any("Pan", "[***hidden***]"),
		zap.Any("ExpirationYear", in.GetExpirationYear()),
		zap.Any("ExpirationMonth", in.GetExpirationMonth()),
		zap.Any("CardholderName", in.GetCardholderName()),
		zap.Any("SaveAccess", in.GetSaveAccess()),
	)
}

func file_inner_processing_grpc_card_proto_message_CreateClientResponseV1ToZap(
	label string,
	in *CreateClientResponseV1,
) zap.Field {
	if in == nil {
		return zap.Reflect(label, in)
	}

	return zap.Dict(
		label,
		zap.Any("Id", in.GetId()),
		zap.Any("ProjectId", in.GetProjectId()),
		zap.Any("ProjectClientId", in.GetProjectClientId()),
		zap.Any("Ip", in.GetIp()),
		zap.Any("Email", in.GetEmail()),
		zap.Any("Phone", "[***hidden***]"),
		file_inner_processing_grpc_card_proto_message_ClientCardToZap("ClientCard", in.GetClientCard()),
	)
}

func file_inner_processing_grpc_card_proto_message_CreatePayOutClientRequestV1ToZap(
	label string,
	in *CreatePayOutClientRequestV1,
) zap.Field {
	if in == nil {
		return zap.Reflect(label, in)
	}

	return zap.Dict(
		label,
		zap.Any("ProjectId", in.GetProjectId()),
		zap.Any("ProjectClientId", in.GetProjectClientId()),
		zap.Any("Email", in.GetEmail()),
		zap.Any("Phone", "[***hidden***]"),
		zap.Any("Ip", in.GetIp()),
		zap.Any("Pan", "[***hidden***]"),
		zap.Any("SaveAccess", in.GetSaveAccess()),
	)
}

func file_inner_processing_grpc_card_proto_message_CreatePayOutClientResponseV1ToZap(
	label string,
	in *CreatePayOutClientResponseV1,
) zap.Field {
	if in == nil {
		return zap.Reflect(label, in)
	}

	return zap.Dict(
		label,
		zap.Any("Id", in.GetId()),
		zap.Any("ProjectId", in.GetProjectId()),
		zap.Any("ProjectClientId", in.GetProjectClientId()),
		zap.Any("Ip", in.GetIp()),
		zap.Any("Email", in.GetEmail()),
		zap.Any("Phone", "[***hidden***]"),
		file_inner_processing_grpc_card_proto_message_ClientCardToZap("ClientCard", in.GetClientCard()),
	)
}

func file_inner_processing_grpc_card_proto_message_DecryptPayInRequestToZap(
	label string,
	in *DecryptPayInRequest,
) zap.Field {
	if in == nil {
		return zap.Reflect(label, in)
	}

	return zap.Dict(
		label,
		zap.Any("EncryptedCard", in.GetEncryptedCard()),
	)
}

func file_inner_processing_grpc_card_proto_message_DecryptPayInResponseToZap(
	label string,
	in *DecryptPayInResponse,
) zap.Field {
	if in == nil {
		return zap.Reflect(label, in)
	}

	return zap.Dict(
		label,
		zap.Any("Pan", "[***hidden***]"),
		zap.Any("ExpMonth", in.GetExpMonth()),
		zap.Any("ExpYear", in.GetExpYear()),
		zap.Any("Cvc", "[***hidden***]"),
		zap.Any("FullName", in.GetFullName()),
	)
}

func file_inner_processing_grpc_card_proto_message_DecryptPayOutRequestToZap(
	label string,
	in *DecryptPayOutRequest,
) zap.Field {
	if in == nil {
		return zap.Reflect(label, in)
	}

	return zap.Dict(
		label,
		zap.Any("EncryptedCard", in.GetEncryptedCard()),
	)
}

func file_inner_processing_grpc_card_proto_message_DecryptPayOutResponseToZap(
	label string,
	in *DecryptPayOutResponse,
) zap.Field {
	if in == nil {
		return zap.Reflect(label, in)
	}

	return zap.Dict(
		label,
		zap.Any("Pan", "[***hidden***]"),
	)
}

func file_inner_processing_grpc_card_proto_message_GetCardByPanRequestV1ToZap(
	label string,
	in *GetCardByPanRequestV1,
) zap.Field {
	if in == nil {
		return zap.Reflect(label, in)
	}

	return zap.Dict(
		label,
		zap.Any("ProjectId", in.GetProjectId()),
		zap.Any("ProjectClientId", in.GetProjectClientId()),
		zap.Any("Pan", "[***hidden***]"),
	)
}

func file_inner_processing_grpc_card_proto_message_GetCardByPanResponseV1ToZap(
	label string,
	in *GetCardByPanResponseV1,
) zap.Field {
	if in == nil {
		return zap.Reflect(label, in)
	}

	return zap.Dict(
		label,
		zap.Any("CardId", in.GetCardId()),
	)
}

func file_inner_processing_grpc_card_proto_message_GetCardTokensRequestV1ToZap(
	label string,
	in *GetCardTokensRequestV1,
) zap.Field {
	if in == nil {
		return zap.Reflect(label, in)
	}

	return zap.Dict(
		label,
		zap.Any("ProjectId", in.GetProjectId()),
		zap.Any("ProjectClientId", in.GetProjectClientId()),
		zap.Any("CardId", in.GetCardId()),
	)
}

func file_inner_processing_grpc_card_proto_message_GetCardTokensResponseV1ToZap(
	label string,
	in *GetCardTokensResponseV1,
) zap.Field {
	if in == nil {
		return zap.Reflect(label, in)
	}

	return zap.Dict(
		label,
		file_inner_processing_grpc_card_proto_message_ClientCardToZap("Card", in.GetCard()),
		zap.Any("Tokens", "[***hidden***]"),
	)
}

func file_inner_processing_grpc_card_proto_message_GetClientListByProjectClientRequestV1ToZap(
	label string,
	in *GetClientListByProjectClientRequestV1,
) zap.Field {
	if in == nil {
		return zap.Reflect(label, in)
	}

	return zap.Dict(
		label,
		file_inner_processing_grpc_card_proto_message_ProjectAndClientDataSliceToZap("Data", in.GetData()),
	)
}

func file_inner_processing_grpc_card_proto_message_GetClientListByProjectClientResponseV1ToZap(
	label string,
	in *GetClientListByProjectClientResponseV1,
) zap.Field {
	if in == nil {
		return zap.Reflect(label, in)
	}

	return zap.Dict(
		label,
		file_inner_processing_grpc_card_proto_message_GetClientListDataSliceToZap("Data", in.GetData()),
	)
}

func file_inner_processing_grpc_card_proto_message_GetClientListByVerificationRequestV1ToZap(
	label string,
	in *GetClientListByVerificationRequestV1,
) zap.Field {
	if in == nil {
		return zap.Reflect(label, in)
	}

	return zap.Dict(
		label,
		file_inner_processing_grpc_card_proto_message_TimestampToZap("ClientCreatedAtFrom", in.GetClientCreatedAtFrom()),
		file_inner_processing_grpc_card_proto_message_TimestampToZap("ClientCreatedAtTo", in.GetClientCreatedAtTo()),
		zap.Any("VerificationUserId", in.GetVerificationUserId()),
	)
}

func file_inner_processing_grpc_card_proto_message_GetClientListDataToZap(
	label string,
	in *GetClientListData,
) zap.Field {
	if in == nil {
		return zap.Reflect(label, in)
	}

	return zap.Dict(
		label,
		zap.Any("ClientId", in.GetClientId()),
		zap.Any("ProjectId", in.GetProjectId()),
		zap.Any("ProjectClientId", in.GetProjectClientId()),
		zap.Any("VerificationUserId", in.GetVerificationUserId()),
	)
}

func file_inner_processing_grpc_card_proto_message_GetClientListDataSliceToZap(
	label string,
	in []*GetClientListData,
) zap.Field {
	var res []zap.Field
	for index, val := range in {
		res = append(res, file_inner_processing_grpc_card_proto_message_GetClientListDataToZap(fmt.Sprint(index), val))
	}

	return zap.Dict(label, res...)
}

func file_inner_processing_grpc_card_proto_message_GetEncryptedCardRequestV1ToZap(
	label string,
	in *GetEncryptedCardRequestV1,
) zap.Field {
	if in == nil {
		return zap.Reflect(label, in)
	}

	return zap.Dict(
		label,
		zap.Any("CardId", in.GetCardId()),
	)
}

func file_inner_processing_grpc_card_proto_message_GetEncryptedCardResponseV1ToZap(
	label string,
	in *GetEncryptedCardResponseV1,
) zap.Field {
	if in == nil {
		return zap.Reflect(label, in)
	}

	return zap.Dict(
		label,
		zap.Any("CardToken", "[***hidden***]"),
		zap.Any("MaskedPan", in.GetMaskedPan()),
		zap.Any("Ips", in.GetIps()),
		zap.Any("Issuer", in.GetIssuer()),
		zap.Any("Year", in.GetYear()),
		zap.Any("Month", in.GetMonth()),
	)
}

func file_inner_processing_grpc_card_proto_message_GetEncryptedCardResponseV1SliceToZap(
	label string,
	in []*GetEncryptedCardResponseV1,
) zap.Field {
	var res []zap.Field
	for index, val := range in {
		res = append(res, file_inner_processing_grpc_card_proto_message_GetEncryptedCardResponseV1ToZap(fmt.Sprint(index), val))
	}

	return zap.Dict(label, res...)
}

func file_inner_processing_grpc_card_proto_message_GetEncryptedPayOutCardResponseV1ToZap(
	label string,
	in *GetEncryptedPayOutCardResponseV1,
) zap.Field {
	if in == nil {
		return zap.Reflect(label, in)
	}

	return zap.Dict(
		label,
		zap.Any("CardToken", "[***hidden***]"),
		zap.Any("MaskedPan", in.GetMaskedPan()),
		zap.Any("Ips", in.GetIps()),
		zap.Any("Issuer", in.GetIssuer()),
	)
}

func file_inner_processing_grpc_card_proto_message_GetOneClickPayInCardsRequestV1ToZap(
	label string,
	in *GetOneClickPayInCardsRequestV1,
) zap.Field {
	if in == nil {
		return zap.Reflect(label, in)
	}

	return zap.Dict(
		label,
		zap.Any("ProjectId", in.GetProjectId()),
		zap.Any("ProjectClientId", in.GetProjectClientId()),
		zap.Any("TransactionTypeId", in.GetTransactionTypeId()),
		zap.Any("Amount", in.GetAmount()),
		file_inner_processing_grpc_card_proto_message_TerminalProjectV1SliceToZap("ActiveTerminals", in.GetActiveTerminals()),
	)
}

func file_inner_processing_grpc_card_proto_message_GetOneClickPayInCardsResponseV1ToZap(
	label string,
	in *GetOneClickPayInCardsResponseV1,
) zap.Field {
	if in == nil {
		return zap.Reflect(label, in)
	}

	return zap.Dict(
		label,
		file_inner_processing_grpc_card_proto_message_GetEncryptedCardResponseV1SliceToZap("Cards", in.GetCards()),
	)
}

func file_inner_processing_grpc_card_proto_message_GetOneClickPayOutCardsRequestV1ToZap(
	label string,
	in *GetOneClickPayOutCardsRequestV1,
) zap.Field {
	if in == nil {
		return zap.Reflect(label, in)
	}

	return zap.Dict(
		label,
		zap.Any("ProjectId", in.GetProjectId()),
		zap.Any("ProjectClientId", in.GetProjectClientId()),
		zap.Any("Amount", in.GetAmount()),
		file_inner_processing_grpc_card_proto_message_TerminalProjectV1SliceToZap("ActiveTerminals", in.GetActiveTerminals()),
	)
}

func file_inner_processing_grpc_card_proto_message_GetOneClickPayOutCardsResponseV1ToZap(
	label string,
	in *GetOneClickPayOutCardsResponseV1,
) zap.Field {
	if in == nil {
		return zap.Reflect(label, in)
	}

	return zap.Dict(
		label,
		file_inner_processing_grpc_card_proto_string_String("Cards", in.GetCards()),
	)
}

func file_inner_processing_grpc_card_proto_message_GetPanByCardIdRequestV1ToZap(
	label string,
	in *GetPanByCardIdRequestV1,
) zap.Field {
	if in == nil {
		return zap.Reflect(label, in)
	}

	return zap.Dict(
		label,
		zap.Any("CardId", in.GetCardId()),
	)
}

func file_inner_processing_grpc_card_proto_message_GetPanByHashedIdRequestV1ToZap(
	label string,
	in *GetPanByHashedIdRequestV1,
) zap.Field {
	if in == nil {
		return zap.Reflect(label, in)
	}

	return zap.Dict(
		label,
		zap.Any("EncryptedKey", "[***hidden***]"),
	)
}

func file_inner_processing_grpc_card_proto_message_GetPanInfoByProjectIdRequestToZap(
	label string,
	in *GetPanInfoByProjectIdRequest,
) zap.Field {
	if in == nil {
		return zap.Reflect(label, in)
	}

	return zap.Dict(
		label,
		zap.Any("Pan", "[***hidden***]"),
		zap.Any("ProjectId", in.GetProjectId()),
	)
}

func file_inner_processing_grpc_card_proto_message_GetPanInfoByProjectIdResponseToZap(
	label string,
	in *GetPanInfoByProjectIdResponse,
) zap.Field {
	if in == nil {
		return zap.Reflect(label, in)
	}

	return zap.Dict(
		label,
		zap.Any("IpsId", in.GetIpsId()),
		zap.Any("CountryId", in.GetCountryId()),
		zap.Any("IssuerId", in.GetIssuerId()),
		zap.Any("MaskedPan", in.GetMaskedPan()),
	)
}

func file_inner_processing_grpc_card_proto_message_GetPanResponseV1ToZap(
	label string,
	in *GetPanResponseV1,
) zap.Field {
	if in == nil {
		return zap.Reflect(label, in)
	}

	return zap.Dict(
		label,
		zap.Any("MaskedPan", in.GetMaskedPan()),
		zap.Any("IpsId", in.GetIpsId()),
		zap.Any("CountryId", in.GetCountryId()),
		zap.Any("IssuerId", in.GetIssuerId()),
		zap.Any("CardId", in.GetCardId()),
		zap.Any("Pan", "[***hidden***]"),
	)
}

func file_inner_processing_grpc_card_proto_message_ProjectAndClientDataToZap(
	label string,
	in *ProjectAndClientData,
) zap.Field {
	if in == nil {
		return zap.Reflect(label, in)
	}

	return zap.Dict(
		label,
		zap.Any("ProjectId", in.GetProjectId()),
		zap.Any("ProjectClientId", in.GetProjectClientId()),
	)
}

func file_inner_processing_grpc_card_proto_message_ProjectAndClientDataSliceToZap(
	label string,
	in []*ProjectAndClientData,
) zap.Field {
	var res []zap.Field
	for index, val := range in {
		res = append(res, file_inner_processing_grpc_card_proto_message_ProjectAndClientDataToZap(fmt.Sprint(index), val))
	}

	return zap.Dict(label, res...)
}

func file_inner_processing_grpc_card_proto_message_ReEncryptCardRequestToZap(
	label string,
	in *ReEncryptCardRequest,
) zap.Field {
	if in == nil {
		return zap.Reflect(label, in)
	}

	return zap.Dict(
		label,
		zap.Any("Pan", "[***hidden***]"),
		zap.Any("ExpMonth", in.GetExpMonth()),
		zap.Any("ExpYear", in.GetExpYear()),
		zap.Any("Cvc", "[***hidden***]"),
		zap.Any("FullName", in.GetFullName()),
	)
}

func file_inner_processing_grpc_card_proto_message_ReEncryptCardResponseToZap(
	label string,
	in *ReEncryptCardResponse,
) zap.Field {
	if in == nil {
		return zap.Reflect(label, in)
	}

	return zap.Dict(
		label,
		zap.Any("Pan", "[***hidden***]"),
		zap.Any("ExpMonth", in.GetExpMonth()),
		zap.Any("ExpYear", in.GetExpYear()),
		zap.Any("Cvc", "[***hidden***]"),
		zap.Any("FullName", in.GetFullName()),
	)
}

var _ CardServer = (*loggedCardServer)(nil)

func NewLoggedCardServer(srv CardServer) CardServer {
	return &loggedCardServer{srv: srv}
}

type loggedCardServer struct {
	UnimplementedCardServer

	srv CardServer
}

func (s *loggedCardServer) CreateClientV1(
	ctx context.Context,
	request *CreateClientRequestV1,
) (
	response *CreateClientResponseV1,
	err error,
) {
	label := cntx.Begin(ctx, "CardServer_CreateClientV1")
	defer cntx.End(ctx, label)

	defer func() {
		if err != nil {
			ctxzap.AddFields(ctx, zap.Any(label+"error", err))
			return
		}

		ctxzap.AddFields(
			ctx,
			file_inner_processing_grpc_card_proto_message_CreateClientResponseV1ToZap(label+"response", response),
		)
	}()

	ctxzap.AddFields(
		ctx,
		file_inner_processing_grpc_card_proto_message_CreateClientRequestV1ToZap(label+"request", request),
	)

	response, err = s.srv.CreateClientV1(ctx, request)

	return
}

func (s *loggedCardServer) GetCardTokensV1(
	ctx context.Context,
	request *GetCardTokensRequestV1,
) (
	response *GetCardTokensResponseV1,
	err error,
) {
	label := cntx.Begin(ctx, "CardServer_GetCardTokensV1")
	defer cntx.End(ctx, label)

	defer func() {
		if err != nil {
			ctxzap.AddFields(ctx, zap.Any(label+"error", err))
			return
		}

		ctxzap.AddFields(
			ctx,
			file_inner_processing_grpc_card_proto_message_GetCardTokensResponseV1ToZap(label+"response", response),
		)
	}()

	ctxzap.AddFields(
		ctx,
		file_inner_processing_grpc_card_proto_message_GetCardTokensRequestV1ToZap(label+"request", request),
	)

	response, err = s.srv.GetCardTokensV1(ctx, request)

	return
}

func (s *loggedCardServer) GetOneClickPayInCardsV1(
	ctx context.Context,
	request *GetOneClickPayInCardsRequestV1,
) (
	response *GetOneClickPayInCardsResponseV1,
	err error,
) {
	label := cntx.Begin(ctx, "CardServer_GetOneClickPayInCardsV1")
	defer cntx.End(ctx, label)

	defer func() {
		if err != nil {
			ctxzap.AddFields(ctx, zap.Any(label+"error", err))
			return
		}

		ctxzap.AddFields(
			ctx,
			file_inner_processing_grpc_card_proto_message_GetOneClickPayInCardsResponseV1ToZap(label+"response", response),
		)
	}()

	ctxzap.AddFields(
		ctx,
		file_inner_processing_grpc_card_proto_message_GetOneClickPayInCardsRequestV1ToZap(label+"request", request),
	)

	response, err = s.srv.GetOneClickPayInCardsV1(ctx, request)

	return
}

func (s *loggedCardServer) GetPanByCardIdV1(
	ctx context.Context,
	request *GetPanByCardIdRequestV1,
) (
	response *GetPanResponseV1,
	err error,
) {
	label := cntx.Begin(ctx, "CardServer_GetPanByCardIdV1")
	defer cntx.End(ctx, label)

	defer func() {
		if err != nil {
			ctxzap.AddFields(ctx, zap.Any(label+"error", err))
			return
		}

		ctxzap.AddFields(
			ctx,
			file_inner_processing_grpc_card_proto_message_GetPanResponseV1ToZap(label+"response", response),
		)
	}()

	ctxzap.AddFields(
		ctx,
		file_inner_processing_grpc_card_proto_message_GetPanByCardIdRequestV1ToZap(label+"request", request),
	)

	response, err = s.srv.GetPanByCardIdV1(ctx, request)

	return
}

func (s *loggedCardServer) GetPanByHashedIdV1(
	ctx context.Context,
	request *GetPanByHashedIdRequestV1,
) (
	response *GetPanResponseV1,
	err error,
) {
	label := cntx.Begin(ctx, "CardServer_GetPanByHashedIdV1")
	defer cntx.End(ctx, label)

	defer func() {
		if err != nil {
			ctxzap.AddFields(ctx, zap.Any(label+"error", err))
			return
		}

		ctxzap.AddFields(
			ctx,
			file_inner_processing_grpc_card_proto_message_GetPanResponseV1ToZap(label+"response", response),
		)
	}()

	ctxzap.AddFields(
		ctx,
		file_inner_processing_grpc_card_proto_message_GetPanByHashedIdRequestV1ToZap(label+"request", request),
	)

	response, err = s.srv.GetPanByHashedIdV1(ctx, request)

	return
}

func (s *loggedCardServer) GetOneClickPayOutCardsV1(
	ctx context.Context,
	request *GetOneClickPayOutCardsRequestV1,
) (
	response *GetOneClickPayOutCardsResponseV1,
	err error,
) {
	label := cntx.Begin(ctx, "CardServer_GetOneClickPayOutCardsV1")
	defer cntx.End(ctx, label)

	defer func() {
		if err != nil {
			ctxzap.AddFields(ctx, zap.Any(label+"error", err))
			return
		}

		ctxzap.AddFields(
			ctx,
			file_inner_processing_grpc_card_proto_message_GetOneClickPayOutCardsResponseV1ToZap(label+"response", response),
		)
	}()

	ctxzap.AddFields(
		ctx,
		file_inner_processing_grpc_card_proto_message_GetOneClickPayOutCardsRequestV1ToZap(label+"request", request),
	)

	response, err = s.srv.GetOneClickPayOutCardsV1(ctx, request)

	return
}

func (s *loggedCardServer) GetEncryptedCardToken(
	ctx context.Context,
	request *GetEncryptedCardRequestV1,
) (
	response *GetEncryptedCardResponseV1,
	err error,
) {
	label := cntx.Begin(ctx, "CardServer_GetEncryptedCardToken")
	defer cntx.End(ctx, label)

	defer func() {
		if err != nil {
			ctxzap.AddFields(ctx, zap.Any(label+"error", err))
			return
		}

		ctxzap.AddFields(
			ctx,
			file_inner_processing_grpc_card_proto_message_GetEncryptedCardResponseV1ToZap(label+"response", response),
		)
	}()

	ctxzap.AddFields(
		ctx,
		file_inner_processing_grpc_card_proto_message_GetEncryptedCardRequestV1ToZap(label+"request", request),
	)

	response, err = s.srv.GetEncryptedCardToken(ctx, request)

	return
}

func (s *loggedCardServer) GetClientListByVerification(
	ctx context.Context,
	request *GetClientListByVerificationRequestV1,
) (
	response *GetClientListByProjectClientResponseV1,
	err error,
) {
	label := cntx.Begin(ctx, "CardServer_GetClientListByVerification")
	defer cntx.End(ctx, label)

	defer func() {
		if err != nil {
			ctxzap.AddFields(ctx, zap.Any(label+"error", err))
			return
		}

		ctxzap.AddFields(
			ctx,
			file_inner_processing_grpc_card_proto_message_GetClientListByProjectClientResponseV1ToZap(label+"response", response),
		)
	}()

	ctxzap.AddFields(
		ctx,
		file_inner_processing_grpc_card_proto_message_GetClientListByVerificationRequestV1ToZap(label+"request", request),
	)

	response, err = s.srv.GetClientListByVerification(ctx, request)

	return
}

func (s *loggedCardServer) GetClientListByProjectClient(
	ctx context.Context,
	request *GetClientListByProjectClientRequestV1,
) (
	response *GetClientListByProjectClientResponseV1,
	err error,
) {
	label := cntx.Begin(ctx, "CardServer_GetClientListByProjectClient")
	defer cntx.End(ctx, label)

	defer func() {
		if err != nil {
			ctxzap.AddFields(ctx, zap.Any(label+"error", err))
			return
		}

		ctxzap.AddFields(
			ctx,
			file_inner_processing_grpc_card_proto_message_GetClientListByProjectClientResponseV1ToZap(label+"response", response),
		)
	}()

	ctxzap.AddFields(
		ctx,
		file_inner_processing_grpc_card_proto_message_GetClientListByProjectClientRequestV1ToZap(label+"request", request),
	)

	response, err = s.srv.GetClientListByProjectClient(ctx, request)

	return
}

func (s *loggedCardServer) CheckClientActiveness(
	ctx context.Context,
	request *CheckClientActivenessRequestV1,
) (
	response *emptypb.Empty,
	err error,
) {
	label := cntx.Begin(ctx, "CardServer_CheckClientActiveness")
	defer cntx.End(ctx, label)

	defer func() {
		if err != nil {
			ctxzap.AddFields(ctx, zap.Any(label+"error", err))
			return
		}

		ctxzap.AddFields(
			ctx,
			file_inner_processing_grpc_card_proto_message_EmptyToZap(label+"response", response),
		)
	}()

	ctxzap.AddFields(
		ctx,
		file_inner_processing_grpc_card_proto_message_CheckClientActivenessRequestV1ToZap(label+"request", request),
	)

	response, err = s.srv.CheckClientActiveness(ctx, request)

	return
}

func (s *loggedCardServer) GetCardByPan(
	ctx context.Context,
	request *GetCardByPanRequestV1,
) (
	response *GetCardByPanResponseV1,
	err error,
) {
	label := cntx.Begin(ctx, "CardServer_GetCardByPan")
	defer cntx.End(ctx, label)

	defer func() {
		if err != nil {
			ctxzap.AddFields(ctx, zap.Any(label+"error", err))
			return
		}

		ctxzap.AddFields(
			ctx,
			file_inner_processing_grpc_card_proto_message_GetCardByPanResponseV1ToZap(label+"response", response),
		)
	}()

	ctxzap.AddFields(
		ctx,
		file_inner_processing_grpc_card_proto_message_GetCardByPanRequestV1ToZap(label+"request", request),
	)

	response, err = s.srv.GetCardByPan(ctx, request)

	return
}

func (s *loggedCardServer) DecryptPayInCard(
	ctx context.Context,
	request *DecryptPayInRequest,
) (
	response *DecryptPayInResponse,
	err error,
) {
	label := cntx.Begin(ctx, "CardServer_DecryptPayInCard")
	defer cntx.End(ctx, label)

	defer func() {
		if err != nil {
			ctxzap.AddFields(ctx, zap.Any(label+"error", err))
			return
		}

		ctxzap.AddFields(
			ctx,
			file_inner_processing_grpc_card_proto_message_DecryptPayInResponseToZap(label+"response", response),
		)
	}()

	ctxzap.AddFields(
		ctx,
		file_inner_processing_grpc_card_proto_message_DecryptPayInRequestToZap(label+"request", request),
	)

	response, err = s.srv.DecryptPayInCard(ctx, request)

	return
}

func (s *loggedCardServer) DecryptPayOutCard(
	ctx context.Context,
	request *DecryptPayOutRequest,
) (
	response *DecryptPayOutResponse,
	err error,
) {
	label := cntx.Begin(ctx, "CardServer_DecryptPayOutCard")
	defer cntx.End(ctx, label)

	defer func() {
		if err != nil {
			ctxzap.AddFields(ctx, zap.Any(label+"error", err))
			return
		}

		ctxzap.AddFields(
			ctx,
			file_inner_processing_grpc_card_proto_message_DecryptPayOutResponseToZap(label+"response", response),
		)
	}()

	ctxzap.AddFields(
		ctx,
		file_inner_processing_grpc_card_proto_message_DecryptPayOutRequestToZap(label+"request", request),
	)

	response, err = s.srv.DecryptPayOutCard(ctx, request)

	return
}

func (s *loggedCardServer) ReEncryptCard(
	ctx context.Context,
	request *ReEncryptCardRequest,
) (
	response *ReEncryptCardResponse,
	err error,
) {
	label := cntx.Begin(ctx, "CardServer_ReEncryptCard")
	defer cntx.End(ctx, label)

	defer func() {
		if err != nil {
			ctxzap.AddFields(ctx, zap.Any(label+"error", err))
			return
		}

		ctxzap.AddFields(
			ctx,
			file_inner_processing_grpc_card_proto_message_ReEncryptCardResponseToZap(label+"response", response),
		)
	}()

	ctxzap.AddFields(
		ctx,
		file_inner_processing_grpc_card_proto_message_ReEncryptCardRequestToZap(label+"request", request),
	)

	response, err = s.srv.ReEncryptCard(ctx, request)

	return
}

func (s *loggedCardServer) GetPanInfoByProjectId(
	ctx context.Context,
	request *GetPanInfoByProjectIdRequest,
) (
	response *GetPanInfoByProjectIdResponse,
	err error,
) {
	label := cntx.Begin(ctx, "CardServer_GetPanInfoByProjectId")
	defer cntx.End(ctx, label)

	defer func() {
		if err != nil {
			ctxzap.AddFields(ctx, zap.Any(label+"error", err))
			return
		}

		ctxzap.AddFields(
			ctx,
			file_inner_processing_grpc_card_proto_message_GetPanInfoByProjectIdResponseToZap(label+"response", response),
		)
	}()

	ctxzap.AddFields(
		ctx,
		file_inner_processing_grpc_card_proto_message_GetPanInfoByProjectIdRequestToZap(label+"request", request),
	)

	response, err = s.srv.GetPanInfoByProjectId(ctx, request)

	return
}

func (s *loggedCardServer) NewKey(
	ctx context.Context,
	request *emptypb.Empty,
) (
	response *emptypb.Empty,
	err error,
) {
	label := cntx.Begin(ctx, "CardServer_NewKey")
	defer cntx.End(ctx, label)

	defer func() {
		if err != nil {
			ctxzap.AddFields(ctx, zap.Any(label+"error", err))
			return
		}

		ctxzap.AddFields(
			ctx,
			file_inner_processing_grpc_card_proto_message_EmptyToZap(label+"response", response),
		)
	}()

	ctxzap.AddFields(
		ctx,
		file_inner_processing_grpc_card_proto_message_EmptyToZap(label+"request", request),
	)

	response, err = s.srv.NewKey(ctx, request)

	return
}

func (s *loggedCardServer) RotateCardKeys(
	ctx context.Context,
	request *emptypb.Empty,
) (
	response *emptypb.Empty,
	err error,
) {
	label := cntx.Begin(ctx, "CardServer_RotateCardKeys")
	defer cntx.End(ctx, label)

	defer func() {
		if err != nil {
			ctxzap.AddFields(ctx, zap.Any(label+"error", err))
			return
		}

		ctxzap.AddFields(
			ctx,
			file_inner_processing_grpc_card_proto_message_EmptyToZap(label+"response", response),
		)
	}()

	ctxzap.AddFields(
		ctx,
		file_inner_processing_grpc_card_proto_message_EmptyToZap(label+"request", request),
	)

	response, err = s.srv.RotateCardKeys(ctx, request)

	return
}

func (s *loggedCardServer) CheckExpireCards(
	ctx context.Context,
	request *emptypb.Empty,
) (
	response *emptypb.Empty,
	err error,
) {
	label := cntx.Begin(ctx, "CardServer_CheckExpireCards")
	defer cntx.End(ctx, label)

	defer func() {
		if err != nil {
			ctxzap.AddFields(ctx, zap.Any(label+"error", err))
			return
		}

		ctxzap.AddFields(
			ctx,
			file_inner_processing_grpc_card_proto_message_EmptyToZap(label+"response", response),
		)
	}()

	ctxzap.AddFields(
		ctx,
		file_inner_processing_grpc_card_proto_message_EmptyToZap(label+"request", request),
	)

	response, err = s.srv.CheckExpireCards(ctx, request)

	return
}

func (s *loggedCardServer) CreateNewHashKey(
	ctx context.Context,
	request *emptypb.Empty,
) (
	response *emptypb.Empty,
	err error,
) {
	label := cntx.Begin(ctx, "CardServer_CreateNewHashKey")
	defer cntx.End(ctx, label)

	defer func() {
		if err != nil {
			ctxzap.AddFields(ctx, zap.Any(label+"error", err))
			return
		}

		ctxzap.AddFields(
			ctx,
			file_inner_processing_grpc_card_proto_message_EmptyToZap(label+"response", response),
		)
	}()

	ctxzap.AddFields(
		ctx,
		file_inner_processing_grpc_card_proto_message_EmptyToZap(label+"request", request),
	)

	response, err = s.srv.CreateNewHashKey(ctx, request)

	return
}

func (s *loggedCardServer) RotateHashKeys(
	ctx context.Context,
	request *emptypb.Empty,
) (
	response *emptypb.Empty,
	err error,
) {
	label := cntx.Begin(ctx, "CardServer_RotateHashKeys")
	defer cntx.End(ctx, label)

	defer func() {
		if err != nil {
			ctxzap.AddFields(ctx, zap.Any(label+"error", err))
			return
		}

		ctxzap.AddFields(
			ctx,
			file_inner_processing_grpc_card_proto_message_EmptyToZap(label+"response", response),
		)
	}()

	ctxzap.AddFields(
		ctx,
		file_inner_processing_grpc_card_proto_message_EmptyToZap(label+"request", request),
	)

	response, err = s.srv.RotateHashKeys(ctx, request)

	return
}

var _ CardClient = (*loggedCardClient)(nil)

func NewLoggedCardClient(client CardClient) CardClient {
	return &loggedCardClient{client: client}
}

type loggedCardClient struct {
	client CardClient
}

func (s *loggedCardClient) CreateClientV1(
	ctx context.Context,
	request *CreateClientRequestV1,
	opts ...grpc.CallOption,
) (
	response *CreateClientResponseV1,
	err error,
) {
	label := cntx.Begin(ctx, "CardClient_CreateClientV1")
	defer cntx.End(ctx, label)

	defer func() {
		if err != nil {
			ctxzap.AddFields(ctx, zap.Any(label+"error", err))
			return
		}

		ctxzap.AddFields(
			ctx,
			file_inner_processing_grpc_card_proto_message_CreateClientResponseV1ToZap(label+"response", response),
		)
	}()

	ctxzap.AddFields(
		ctx,
		file_inner_processing_grpc_card_proto_message_CreateClientRequestV1ToZap(label+"request", request),
	)

	response, err = s.client.CreateClientV1(ctx, request, opts...)

	return
}

func (s *loggedCardClient) GetCardTokensV1(
	ctx context.Context,
	request *GetCardTokensRequestV1,
	opts ...grpc.CallOption,
) (
	response *GetCardTokensResponseV1,
	err error,
) {
	label := cntx.Begin(ctx, "CardClient_GetCardTokensV1")
	defer cntx.End(ctx, label)

	defer func() {
		if err != nil {
			ctxzap.AddFields(ctx, zap.Any(label+"error", err))
			return
		}

		ctxzap.AddFields(
			ctx,
			file_inner_processing_grpc_card_proto_message_GetCardTokensResponseV1ToZap(label+"response", response),
		)
	}()

	ctxzap.AddFields(
		ctx,
		file_inner_processing_grpc_card_proto_message_GetCardTokensRequestV1ToZap(label+"request", request),
	)

	response, err = s.client.GetCardTokensV1(ctx, request, opts...)

	return
}

func (s *loggedCardClient) GetOneClickPayInCardsV1(
	ctx context.Context,
	request *GetOneClickPayInCardsRequestV1,
	opts ...grpc.CallOption,
) (
	response *GetOneClickPayInCardsResponseV1,
	err error,
) {
	label := cntx.Begin(ctx, "CardClient_GetOneClickPayInCardsV1")
	defer cntx.End(ctx, label)

	defer func() {
		if err != nil {
			ctxzap.AddFields(ctx, zap.Any(label+"error", err))
			return
		}

		ctxzap.AddFields(
			ctx,
			file_inner_processing_grpc_card_proto_message_GetOneClickPayInCardsResponseV1ToZap(label+"response", response),
		)
	}()

	ctxzap.AddFields(
		ctx,
		file_inner_processing_grpc_card_proto_message_GetOneClickPayInCardsRequestV1ToZap(label+"request", request),
	)

	response, err = s.client.GetOneClickPayInCardsV1(ctx, request, opts...)

	return
}

func (s *loggedCardClient) GetPanByCardIdV1(
	ctx context.Context,
	request *GetPanByCardIdRequestV1,
	opts ...grpc.CallOption,
) (
	response *GetPanResponseV1,
	err error,
) {
	label := cntx.Begin(ctx, "CardClient_GetPanByCardIdV1")
	defer cntx.End(ctx, label)

	defer func() {
		if err != nil {
			ctxzap.AddFields(ctx, zap.Any(label+"error", err))
			return
		}

		ctxzap.AddFields(
			ctx,
			file_inner_processing_grpc_card_proto_message_GetPanResponseV1ToZap(label+"response", response),
		)
	}()

	ctxzap.AddFields(
		ctx,
		file_inner_processing_grpc_card_proto_message_GetPanByCardIdRequestV1ToZap(label+"request", request),
	)

	response, err = s.client.GetPanByCardIdV1(ctx, request, opts...)

	return
}

func (s *loggedCardClient) GetPanByHashedIdV1(
	ctx context.Context,
	request *GetPanByHashedIdRequestV1,
	opts ...grpc.CallOption,
) (
	response *GetPanResponseV1,
	err error,
) {
	label := cntx.Begin(ctx, "CardClient_GetPanByHashedIdV1")
	defer cntx.End(ctx, label)

	defer func() {
		if err != nil {
			ctxzap.AddFields(ctx, zap.Any(label+"error", err))
			return
		}

		ctxzap.AddFields(
			ctx,
			file_inner_processing_grpc_card_proto_message_GetPanResponseV1ToZap(label+"response", response),
		)
	}()

	ctxzap.AddFields(
		ctx,
		file_inner_processing_grpc_card_proto_message_GetPanByHashedIdRequestV1ToZap(label+"request", request),
	)

	response, err = s.client.GetPanByHashedIdV1(ctx, request, opts...)

	return
}

func (s *loggedCardClient) GetOneClickPayOutCardsV1(
	ctx context.Context,
	request *GetOneClickPayOutCardsRequestV1,
	opts ...grpc.CallOption,
) (
	response *GetOneClickPayOutCardsResponseV1,
	err error,
) {
	label := cntx.Begin(ctx, "CardClient_GetOneClickPayOutCardsV1")
	defer cntx.End(ctx, label)

	defer func() {
		if err != nil {
			ctxzap.AddFields(ctx, zap.Any(label+"error", err))
			return
		}

		ctxzap.AddFields(
			ctx,
			file_inner_processing_grpc_card_proto_message_GetOneClickPayOutCardsResponseV1ToZap(label+"response", response),
		)
	}()

	ctxzap.AddFields(
		ctx,
		file_inner_processing_grpc_card_proto_message_GetOneClickPayOutCardsRequestV1ToZap(label+"request", request),
	)

	response, err = s.client.GetOneClickPayOutCardsV1(ctx, request, opts...)

	return
}

func (s *loggedCardClient) GetEncryptedCardToken(
	ctx context.Context,
	request *GetEncryptedCardRequestV1,
	opts ...grpc.CallOption,
) (
	response *GetEncryptedCardResponseV1,
	err error,
) {
	label := cntx.Begin(ctx, "CardClient_GetEncryptedCardToken")
	defer cntx.End(ctx, label)

	defer func() {
		if err != nil {
			ctxzap.AddFields(ctx, zap.Any(label+"error", err))
			return
		}

		ctxzap.AddFields(
			ctx,
			file_inner_processing_grpc_card_proto_message_GetEncryptedCardResponseV1ToZap(label+"response", response),
		)
	}()

	ctxzap.AddFields(
		ctx,
		file_inner_processing_grpc_card_proto_message_GetEncryptedCardRequestV1ToZap(label+"request", request),
	)

	response, err = s.client.GetEncryptedCardToken(ctx, request, opts...)

	return
}

func (s *loggedCardClient) GetClientListByVerification(
	ctx context.Context,
	request *GetClientListByVerificationRequestV1,
	opts ...grpc.CallOption,
) (
	response *GetClientListByProjectClientResponseV1,
	err error,
) {
	label := cntx.Begin(ctx, "CardClient_GetClientListByVerification")
	defer cntx.End(ctx, label)

	defer func() {
		if err != nil {
			ctxzap.AddFields(ctx, zap.Any(label+"error", err))
			return
		}

		ctxzap.AddFields(
			ctx,
			file_inner_processing_grpc_card_proto_message_GetClientListByProjectClientResponseV1ToZap(label+"response", response),
		)
	}()

	ctxzap.AddFields(
		ctx,
		file_inner_processing_grpc_card_proto_message_GetClientListByVerificationRequestV1ToZap(label+"request", request),
	)

	response, err = s.client.GetClientListByVerification(ctx, request, opts...)

	return
}

func (s *loggedCardClient) GetClientListByProjectClient(
	ctx context.Context,
	request *GetClientListByProjectClientRequestV1,
	opts ...grpc.CallOption,
) (
	response *GetClientListByProjectClientResponseV1,
	err error,
) {
	label := cntx.Begin(ctx, "CardClient_GetClientListByProjectClient")
	defer cntx.End(ctx, label)

	defer func() {
		if err != nil {
			ctxzap.AddFields(ctx, zap.Any(label+"error", err))
			return
		}

		ctxzap.AddFields(
			ctx,
			file_inner_processing_grpc_card_proto_message_GetClientListByProjectClientResponseV1ToZap(label+"response", response),
		)
	}()

	ctxzap.AddFields(
		ctx,
		file_inner_processing_grpc_card_proto_message_GetClientListByProjectClientRequestV1ToZap(label+"request", request),
	)

	response, err = s.client.GetClientListByProjectClient(ctx, request, opts...)

	return
}

func (s *loggedCardClient) CheckClientActiveness(
	ctx context.Context,
	request *CheckClientActivenessRequestV1,
	opts ...grpc.CallOption,
) (
	response *emptypb.Empty,
	err error,
) {
	label := cntx.Begin(ctx, "CardClient_CheckClientActiveness")
	defer cntx.End(ctx, label)

	defer func() {
		if err != nil {
			ctxzap.AddFields(ctx, zap.Any(label+"error", err))
			return
		}

		ctxzap.AddFields(
			ctx,
			file_inner_processing_grpc_card_proto_message_EmptyToZap(label+"response", response),
		)
	}()

	ctxzap.AddFields(
		ctx,
		file_inner_processing_grpc_card_proto_message_CheckClientActivenessRequestV1ToZap(label+"request", request),
	)

	response, err = s.client.CheckClientActiveness(ctx, request, opts...)

	return
}

func (s *loggedCardClient) GetCardByPan(
	ctx context.Context,
	request *GetCardByPanRequestV1,
	opts ...grpc.CallOption,
) (
	response *GetCardByPanResponseV1,
	err error,
) {
	label := cntx.Begin(ctx, "CardClient_GetCardByPan")
	defer cntx.End(ctx, label)

	defer func() {
		if err != nil {
			ctxzap.AddFields(ctx, zap.Any(label+"error", err))
			return
		}

		ctxzap.AddFields(
			ctx,
			file_inner_processing_grpc_card_proto_message_GetCardByPanResponseV1ToZap(label+"response", response),
		)
	}()

	ctxzap.AddFields(
		ctx,
		file_inner_processing_grpc_card_proto_message_GetCardByPanRequestV1ToZap(label+"request", request),
	)

	response, err = s.client.GetCardByPan(ctx, request, opts...)

	return
}

func (s *loggedCardClient) DecryptPayInCard(
	ctx context.Context,
	request *DecryptPayInRequest,
	opts ...grpc.CallOption,
) (
	response *DecryptPayInResponse,
	err error,
) {
	label := cntx.Begin(ctx, "CardClient_DecryptPayInCard")
	defer cntx.End(ctx, label)

	defer func() {
		if err != nil {
			ctxzap.AddFields(ctx, zap.Any(label+"error", err))
			return
		}

		ctxzap.AddFields(
			ctx,
			file_inner_processing_grpc_card_proto_message_DecryptPayInResponseToZap(label+"response", response),
		)
	}()

	ctxzap.AddFields(
		ctx,
		file_inner_processing_grpc_card_proto_message_DecryptPayInRequestToZap(label+"request", request),
	)

	response, err = s.client.DecryptPayInCard(ctx, request, opts...)

	return
}

func (s *loggedCardClient) DecryptPayOutCard(
	ctx context.Context,
	request *DecryptPayOutRequest,
	opts ...grpc.CallOption,
) (
	response *DecryptPayOutResponse,
	err error,
) {
	label := cntx.Begin(ctx, "CardClient_DecryptPayOutCard")
	defer cntx.End(ctx, label)

	defer func() {
		if err != nil {
			ctxzap.AddFields(ctx, zap.Any(label+"error", err))
			return
		}

		ctxzap.AddFields(
			ctx,
			file_inner_processing_grpc_card_proto_message_DecryptPayOutResponseToZap(label+"response", response),
		)
	}()

	ctxzap.AddFields(
		ctx,
		file_inner_processing_grpc_card_proto_message_DecryptPayOutRequestToZap(label+"request", request),
	)

	response, err = s.client.DecryptPayOutCard(ctx, request, opts...)

	return
}

func (s *loggedCardClient) ReEncryptCard(
	ctx context.Context,
	request *ReEncryptCardRequest,
	opts ...grpc.CallOption,
) (
	response *ReEncryptCardResponse,
	err error,
) {
	label := cntx.Begin(ctx, "CardClient_ReEncryptCard")
	defer cntx.End(ctx, label)

	defer func() {
		if err != nil {
			ctxzap.AddFields(ctx, zap.Any(label+"error", err))
			return
		}

		ctxzap.AddFields(
			ctx,
			file_inner_processing_grpc_card_proto_message_ReEncryptCardResponseToZap(label+"response", response),
		)
	}()

	ctxzap.AddFields(
		ctx,
		file_inner_processing_grpc_card_proto_message_ReEncryptCardRequestToZap(label+"request", request),
	)

	response, err = s.client.ReEncryptCard(ctx, request, opts...)

	return
}

func (s *loggedCardClient) GetPanInfoByProjectId(
	ctx context.Context,
	request *GetPanInfoByProjectIdRequest,
	opts ...grpc.CallOption,
) (
	response *GetPanInfoByProjectIdResponse,
	err error,
) {
	label := cntx.Begin(ctx, "CardClient_GetPanInfoByProjectId")
	defer cntx.End(ctx, label)

	defer func() {
		if err != nil {
			ctxzap.AddFields(ctx, zap.Any(label+"error", err))
			return
		}

		ctxzap.AddFields(
			ctx,
			file_inner_processing_grpc_card_proto_message_GetPanInfoByProjectIdResponseToZap(label+"response", response),
		)
	}()

	ctxzap.AddFields(
		ctx,
		file_inner_processing_grpc_card_proto_message_GetPanInfoByProjectIdRequestToZap(label+"request", request),
	)

	response, err = s.client.GetPanInfoByProjectId(ctx, request, opts...)

	return
}

func (s *loggedCardClient) NewKey(
	ctx context.Context,
	request *emptypb.Empty,
	opts ...grpc.CallOption,
) (
	response *emptypb.Empty,
	err error,
) {
	label := cntx.Begin(ctx, "CardClient_NewKey")
	defer cntx.End(ctx, label)

	defer func() {
		if err != nil {
			ctxzap.AddFields(ctx, zap.Any(label+"error", err))
			return
		}

		ctxzap.AddFields(
			ctx,
			file_inner_processing_grpc_card_proto_message_EmptyToZap(label+"response", response),
		)
	}()

	ctxzap.AddFields(
		ctx,
		file_inner_processing_grpc_card_proto_message_EmptyToZap(label+"request", request),
	)

	response, err = s.client.NewKey(ctx, request, opts...)

	return
}

func (s *loggedCardClient) RotateCardKeys(
	ctx context.Context,
	request *emptypb.Empty,
	opts ...grpc.CallOption,
) (
	response *emptypb.Empty,
	err error,
) {
	label := cntx.Begin(ctx, "CardClient_RotateCardKeys")
	defer cntx.End(ctx, label)

	defer func() {
		if err != nil {
			ctxzap.AddFields(ctx, zap.Any(label+"error", err))
			return
		}

		ctxzap.AddFields(
			ctx,
			file_inner_processing_grpc_card_proto_message_EmptyToZap(label+"response", response),
		)
	}()

	ctxzap.AddFields(
		ctx,
		file_inner_processing_grpc_card_proto_message_EmptyToZap(label+"request", request),
	)

	response, err = s.client.RotateCardKeys(ctx, request, opts...)

	return
}

func (s *loggedCardClient) CheckExpireCards(
	ctx context.Context,
	request *emptypb.Empty,
	opts ...grpc.CallOption,
) (
	response *emptypb.Empty,
	err error,
) {
	label := cntx.Begin(ctx, "CardClient_CheckExpireCards")
	defer cntx.End(ctx, label)

	defer func() {
		if err != nil {
			ctxzap.AddFields(ctx, zap.Any(label+"error", err))
			return
		}

		ctxzap.AddFields(
			ctx,
			file_inner_processing_grpc_card_proto_message_EmptyToZap(label+"response", response),
		)
	}()

	ctxzap.AddFields(
		ctx,
		file_inner_processing_grpc_card_proto_message_EmptyToZap(label+"request", request),
	)

	response, err = s.client.CheckExpireCards(ctx, request, opts...)

	return
}

func (s *loggedCardClient) CreateNewHashKey(
	ctx context.Context,
	request *emptypb.Empty,
	opts ...grpc.CallOption,
) (
	response *emptypb.Empty,
	err error,
) {
	label := cntx.Begin(ctx, "CardClient_CreateNewHashKey")
	defer cntx.End(ctx, label)

	defer func() {
		if err != nil {
			ctxzap.AddFields(ctx, zap.Any(label+"error", err))
			return
		}

		ctxzap.AddFields(
			ctx,
			file_inner_processing_grpc_card_proto_message_EmptyToZap(label+"response", response),
		)
	}()

	ctxzap.AddFields(
		ctx,
		file_inner_processing_grpc_card_proto_message_EmptyToZap(label+"request", request),
	)

	response, err = s.client.CreateNewHashKey(ctx, request, opts...)

	return
}

func (s *loggedCardClient) RotateHashKeys(
	ctx context.Context,
	request *emptypb.Empty,
	opts ...grpc.CallOption,
) (
	response *emptypb.Empty,
	err error,
) {
	label := cntx.Begin(ctx, "CardClient_RotateHashKeys")
	defer cntx.End(ctx, label)

	defer func() {
		if err != nil {
			ctxzap.AddFields(ctx, zap.Any(label+"error", err))
			return
		}

		ctxzap.AddFields(
			ctx,
			file_inner_processing_grpc_card_proto_message_EmptyToZap(label+"response", response),
		)
	}()

	ctxzap.AddFields(
		ctx,
		file_inner_processing_grpc_card_proto_message_EmptyToZap(label+"request", request),
	)

	response, err = s.client.RotateHashKeys(ctx, request, opts...)

	return
}
