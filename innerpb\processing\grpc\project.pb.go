// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.5
// 	protoc        v5.29.3
// source: inner/processing/grpc/project.proto

package grpc

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type ProjectsRequestV1 struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	MerchantId    *uint64                `protobuf:"varint,1,opt,name=merchant_id,json=merchantId" json:"merchant_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ProjectsRequestV1) Reset() {
	*x = ProjectsRequestV1{}
	mi := &file_inner_processing_grpc_project_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ProjectsRequestV1) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ProjectsRequestV1) ProtoMessage() {}

func (x *ProjectsRequestV1) ProtoReflect() protoreflect.Message {
	mi := &file_inner_processing_grpc_project_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ProjectsRequestV1.ProtoReflect.Descriptor instead.
func (*ProjectsRequestV1) Descriptor() ([]byte, []int) {
	return file_inner_processing_grpc_project_proto_rawDescGZIP(), []int{0}
}

func (x *ProjectsRequestV1) GetMerchantId() uint64 {
	if x != nil && x.MerchantId != nil {
		return *x.MerchantId
	}
	return 0
}

type ProjectsResponseV1 struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Projects      []*ProjectData         `protobuf:"bytes,1,rep,name=projects" json:"projects,omitempty"`
	Merchant      *MerchantData          `protobuf:"bytes,2,opt,name=merchant" json:"merchant,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ProjectsResponseV1) Reset() {
	*x = ProjectsResponseV1{}
	mi := &file_inner_processing_grpc_project_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ProjectsResponseV1) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ProjectsResponseV1) ProtoMessage() {}

func (x *ProjectsResponseV1) ProtoReflect() protoreflect.Message {
	mi := &file_inner_processing_grpc_project_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ProjectsResponseV1.ProtoReflect.Descriptor instead.
func (*ProjectsResponseV1) Descriptor() ([]byte, []int) {
	return file_inner_processing_grpc_project_proto_rawDescGZIP(), []int{1}
}

func (x *ProjectsResponseV1) GetProjects() []*ProjectData {
	if x != nil {
		return x.Projects
	}
	return nil
}

func (x *ProjectsResponseV1) GetMerchant() *MerchantData {
	if x != nil {
		return x.Merchant
	}
	return nil
}

type ProjectData struct {
	state              protoimpl.MessageState `protogen:"open.v1"`
	ProjectId          *uint64                `protobuf:"varint,1,opt,name=project_id,json=projectId" json:"project_id,omitempty"`
	Name               *string                `protobuf:"bytes,2,opt,name=name" json:"name,omitempty"`
	IncomeSource       *string                `protobuf:"bytes,3,opt,name=income_source,json=incomeSource" json:"income_source,omitempty"`
	MainActivityType   *string                `protobuf:"bytes,4,opt,name=main_activity_type,json=mainActivityType" json:"main_activity_type,omitempty"`
	FirstActivityType  *string                `protobuf:"bytes,6,opt,name=first_activity_type,json=firstActivityType" json:"first_activity_type,omitempty"`
	SecondActivityType *string                `protobuf:"bytes,7,opt,name=second_activity_type,json=secondActivityType" json:"second_activity_type,omitempty"`
	ThirdActivityType  *string                `protobuf:"bytes,8,opt,name=third_activity_type,json=thirdActivityType" json:"third_activity_type,omitempty"`
	LicenseName        *string                `protobuf:"bytes,9,opt,name=license_name,json=licenseName" json:"license_name,omitempty"`
	LicenseNumber      *string                `protobuf:"bytes,10,opt,name=license_number,json=licenseNumber" json:"license_number,omitempty"`
	IsLicense          *bool                  `protobuf:"varint,11,opt,name=is_license,json=isLicense" json:"is_license,omitempty"`
	MerchantId         *uint64                `protobuf:"varint,12,opt,name=merchant_id,json=merchantId" json:"merchant_id,omitempty"`
	Logo               *string                `protobuf:"bytes,13,opt,name=logo" json:"logo,omitempty"`
	unknownFields      protoimpl.UnknownFields
	sizeCache          protoimpl.SizeCache
}

func (x *ProjectData) Reset() {
	*x = ProjectData{}
	mi := &file_inner_processing_grpc_project_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ProjectData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ProjectData) ProtoMessage() {}

func (x *ProjectData) ProtoReflect() protoreflect.Message {
	mi := &file_inner_processing_grpc_project_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ProjectData.ProtoReflect.Descriptor instead.
func (*ProjectData) Descriptor() ([]byte, []int) {
	return file_inner_processing_grpc_project_proto_rawDescGZIP(), []int{2}
}

func (x *ProjectData) GetProjectId() uint64 {
	if x != nil && x.ProjectId != nil {
		return *x.ProjectId
	}
	return 0
}

func (x *ProjectData) GetName() string {
	if x != nil && x.Name != nil {
		return *x.Name
	}
	return ""
}

func (x *ProjectData) GetIncomeSource() string {
	if x != nil && x.IncomeSource != nil {
		return *x.IncomeSource
	}
	return ""
}

func (x *ProjectData) GetMainActivityType() string {
	if x != nil && x.MainActivityType != nil {
		return *x.MainActivityType
	}
	return ""
}

func (x *ProjectData) GetFirstActivityType() string {
	if x != nil && x.FirstActivityType != nil {
		return *x.FirstActivityType
	}
	return ""
}

func (x *ProjectData) GetSecondActivityType() string {
	if x != nil && x.SecondActivityType != nil {
		return *x.SecondActivityType
	}
	return ""
}

func (x *ProjectData) GetThirdActivityType() string {
	if x != nil && x.ThirdActivityType != nil {
		return *x.ThirdActivityType
	}
	return ""
}

func (x *ProjectData) GetLicenseName() string {
	if x != nil && x.LicenseName != nil {
		return *x.LicenseName
	}
	return ""
}

func (x *ProjectData) GetLicenseNumber() string {
	if x != nil && x.LicenseNumber != nil {
		return *x.LicenseNumber
	}
	return ""
}

func (x *ProjectData) GetIsLicense() bool {
	if x != nil && x.IsLicense != nil {
		return *x.IsLicense
	}
	return false
}

func (x *ProjectData) GetMerchantId() uint64 {
	if x != nil && x.MerchantId != nil {
		return *x.MerchantId
	}
	return 0
}

func (x *ProjectData) GetLogo() string {
	if x != nil && x.Logo != nil {
		return *x.Logo
	}
	return ""
}

type IsSendEmailResponseV1 struct {
	state            protoimpl.MessageState `protogen:"open.v1"`
	SendEmailEnabled *bool                  `protobuf:"varint,1,opt,name=send_email_enabled,json=sendEmailEnabled" json:"send_email_enabled,omitempty"`
	unknownFields    protoimpl.UnknownFields
	sizeCache        protoimpl.SizeCache
}

func (x *IsSendEmailResponseV1) Reset() {
	*x = IsSendEmailResponseV1{}
	mi := &file_inner_processing_grpc_project_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *IsSendEmailResponseV1) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*IsSendEmailResponseV1) ProtoMessage() {}

func (x *IsSendEmailResponseV1) ProtoReflect() protoreflect.Message {
	mi := &file_inner_processing_grpc_project_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use IsSendEmailResponseV1.ProtoReflect.Descriptor instead.
func (*IsSendEmailResponseV1) Descriptor() ([]byte, []int) {
	return file_inner_processing_grpc_project_proto_rawDescGZIP(), []int{3}
}

func (x *IsSendEmailResponseV1) GetSendEmailEnabled() bool {
	if x != nil && x.SendEmailEnabled != nil {
		return *x.SendEmailEnabled
	}
	return false
}

type GetProcessingProjectsByBUIDRequestV1 struct {
	state          protoimpl.MessageState `protogen:"open.v1"`
	BusinessUnitId *uint64                `protobuf:"varint,1,opt,name=business_unit_id,json=businessUnitId" json:"business_unit_id,omitempty"`
	unknownFields  protoimpl.UnknownFields
	sizeCache      protoimpl.SizeCache
}

func (x *GetProcessingProjectsByBUIDRequestV1) Reset() {
	*x = GetProcessingProjectsByBUIDRequestV1{}
	mi := &file_inner_processing_grpc_project_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetProcessingProjectsByBUIDRequestV1) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetProcessingProjectsByBUIDRequestV1) ProtoMessage() {}

func (x *GetProcessingProjectsByBUIDRequestV1) ProtoReflect() protoreflect.Message {
	mi := &file_inner_processing_grpc_project_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetProcessingProjectsByBUIDRequestV1.ProtoReflect.Descriptor instead.
func (*GetProcessingProjectsByBUIDRequestV1) Descriptor() ([]byte, []int) {
	return file_inner_processing_grpc_project_proto_rawDescGZIP(), []int{4}
}

func (x *GetProcessingProjectsByBUIDRequestV1) GetBusinessUnitId() uint64 {
	if x != nil && x.BusinessUnitId != nil {
		return *x.BusinessUnitId
	}
	return 0
}

type GetProcessingProjectsByBUIDResponseV1 struct {
	state          protoimpl.MessageState `protogen:"open.v1"`
	BusinessUnitId *uint64                `protobuf:"varint,1,opt,name=business_unit_id,json=businessUnitId" json:"business_unit_id,omitempty"`
	Projects       []*ProjectBasicData    `protobuf:"bytes,2,rep,name=projects" json:"projects,omitempty"`
	unknownFields  protoimpl.UnknownFields
	sizeCache      protoimpl.SizeCache
}

func (x *GetProcessingProjectsByBUIDResponseV1) Reset() {
	*x = GetProcessingProjectsByBUIDResponseV1{}
	mi := &file_inner_processing_grpc_project_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetProcessingProjectsByBUIDResponseV1) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetProcessingProjectsByBUIDResponseV1) ProtoMessage() {}

func (x *GetProcessingProjectsByBUIDResponseV1) ProtoReflect() protoreflect.Message {
	mi := &file_inner_processing_grpc_project_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetProcessingProjectsByBUIDResponseV1.ProtoReflect.Descriptor instead.
func (*GetProcessingProjectsByBUIDResponseV1) Descriptor() ([]byte, []int) {
	return file_inner_processing_grpc_project_proto_rawDescGZIP(), []int{5}
}

func (x *GetProcessingProjectsByBUIDResponseV1) GetBusinessUnitId() uint64 {
	if x != nil && x.BusinessUnitId != nil {
		return *x.BusinessUnitId
	}
	return 0
}

func (x *GetProcessingProjectsByBUIDResponseV1) GetProjects() []*ProjectBasicData {
	if x != nil {
		return x.Projects
	}
	return nil
}

type ProjectBasicData struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	ProjectId     *string                `protobuf:"bytes,1,opt,name=project_id,json=projectId" json:"project_id,omitempty"`
	ProjectName   *string                `protobuf:"bytes,2,opt,name=project_name,json=projectName" json:"project_name,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ProjectBasicData) Reset() {
	*x = ProjectBasicData{}
	mi := &file_inner_processing_grpc_project_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ProjectBasicData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ProjectBasicData) ProtoMessage() {}

func (x *ProjectBasicData) ProtoReflect() protoreflect.Message {
	mi := &file_inner_processing_grpc_project_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ProjectBasicData.ProtoReflect.Descriptor instead.
func (*ProjectBasicData) Descriptor() ([]byte, []int) {
	return file_inner_processing_grpc_project_proto_rawDescGZIP(), []int{6}
}

func (x *ProjectBasicData) GetProjectId() string {
	if x != nil && x.ProjectId != nil {
		return *x.ProjectId
	}
	return ""
}

func (x *ProjectBasicData) GetProjectName() string {
	if x != nil && x.ProjectName != nil {
		return *x.ProjectName
	}
	return ""
}

var File_inner_processing_grpc_project_proto protoreflect.FileDescriptor

var file_inner_processing_grpc_project_proto_rawDesc = string([]byte{
	0x0a, 0x23, 0x69, 0x6e, 0x6e, 0x65, 0x72, 0x2f, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69,
	0x6e, 0x67, 0x2f, 0x67, 0x72, 0x70, 0x63, 0x2f, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x1b, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e,
	0x67, 0x2e, 0x6d, 0x65, 0x72, 0x63, 0x68, 0x61, 0x6e, 0x74, 0x2e, 0x70, 0x72, 0x6f, 0x6a, 0x65,
	0x63, 0x74, 0x1a, 0x24, 0x69, 0x6e, 0x6e, 0x65, 0x72, 0x2f, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73,
	0x73, 0x69, 0x6e, 0x67, 0x2f, 0x67, 0x72, 0x70, 0x63, 0x2f, 0x6d, 0x65, 0x72, 0x63, 0x68, 0x61,
	0x6e, 0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x34, 0x0a, 0x11, 0x50, 0x72, 0x6f, 0x6a,
	0x65, 0x63, 0x74, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x56, 0x31, 0x12, 0x1f, 0x0a,
	0x0b, 0x6d, 0x65, 0x72, 0x63, 0x68, 0x61, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x04, 0x52, 0x0a, 0x6d, 0x65, 0x72, 0x63, 0x68, 0x61, 0x6e, 0x74, 0x49, 0x64, 0x22, 0xa2,
	0x01, 0x0a, 0x12, 0x50, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x56, 0x31, 0x12, 0x44, 0x0a, 0x08, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74,
	0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x28, 0x2e, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73,
	0x73, 0x69, 0x6e, 0x67, 0x2e, 0x6d, 0x65, 0x72, 0x63, 0x68, 0x61, 0x6e, 0x74, 0x2e, 0x70, 0x72,
	0x6f, 0x6a, 0x65, 0x63, 0x74, 0x2e, 0x50, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x44, 0x61, 0x74,
	0x61, 0x52, 0x08, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x73, 0x12, 0x46, 0x0a, 0x08, 0x6d,
	0x65, 0x72, 0x63, 0x68, 0x61, 0x6e, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2a, 0x2e,
	0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x2e, 0x6d, 0x65, 0x72, 0x63, 0x68,
	0x61, 0x6e, 0x74, 0x2e, 0x6d, 0x65, 0x72, 0x63, 0x68, 0x61, 0x6e, 0x74, 0x2e, 0x4d, 0x65, 0x72,
	0x63, 0x68, 0x61, 0x6e, 0x74, 0x44, 0x61, 0x74, 0x61, 0x52, 0x08, 0x6d, 0x65, 0x72, 0x63, 0x68,
	0x61, 0x6e, 0x74, 0x22, 0xc3, 0x03, 0x0a, 0x0b, 0x50, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x44,
	0x61, 0x74, 0x61, 0x12, 0x1d, 0x0a, 0x0a, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x5f, 0x69,
	0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x09, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74,
	0x49, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x23, 0x0a, 0x0d, 0x69, 0x6e, 0x63, 0x6f, 0x6d, 0x65,
	0x5f, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x69,
	0x6e, 0x63, 0x6f, 0x6d, 0x65, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x12, 0x2c, 0x0a, 0x12, 0x6d,
	0x61, 0x69, 0x6e, 0x5f, 0x61, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x79, 0x5f, 0x74, 0x79, 0x70,
	0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x10, 0x6d, 0x61, 0x69, 0x6e, 0x41, 0x63, 0x74,
	0x69, 0x76, 0x69, 0x74, 0x79, 0x54, 0x79, 0x70, 0x65, 0x12, 0x2e, 0x0a, 0x13, 0x66, 0x69, 0x72,
	0x73, 0x74, 0x5f, 0x61, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x79, 0x5f, 0x74, 0x79, 0x70, 0x65,
	0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x11, 0x66, 0x69, 0x72, 0x73, 0x74, 0x41, 0x63, 0x74,
	0x69, 0x76, 0x69, 0x74, 0x79, 0x54, 0x79, 0x70, 0x65, 0x12, 0x30, 0x0a, 0x14, 0x73, 0x65, 0x63,
	0x6f, 0x6e, 0x64, 0x5f, 0x61, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x79, 0x5f, 0x74, 0x79, 0x70,
	0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x12, 0x73, 0x65, 0x63, 0x6f, 0x6e, 0x64, 0x41,
	0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x79, 0x54, 0x79, 0x70, 0x65, 0x12, 0x2e, 0x0a, 0x13, 0x74,
	0x68, 0x69, 0x72, 0x64, 0x5f, 0x61, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x79, 0x5f, 0x74, 0x79,
	0x70, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x11, 0x74, 0x68, 0x69, 0x72, 0x64, 0x41,
	0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x79, 0x54, 0x79, 0x70, 0x65, 0x12, 0x21, 0x0a, 0x0c, 0x6c,
	0x69, 0x63, 0x65, 0x6e, 0x73, 0x65, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x09, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0b, 0x6c, 0x69, 0x63, 0x65, 0x6e, 0x73, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x25,
	0x0a, 0x0e, 0x6c, 0x69, 0x63, 0x65, 0x6e, 0x73, 0x65, 0x5f, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72,
	0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x6c, 0x69, 0x63, 0x65, 0x6e, 0x73, 0x65, 0x4e,
	0x75, 0x6d, 0x62, 0x65, 0x72, 0x12, 0x1d, 0x0a, 0x0a, 0x69, 0x73, 0x5f, 0x6c, 0x69, 0x63, 0x65,
	0x6e, 0x73, 0x65, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x08, 0x52, 0x09, 0x69, 0x73, 0x4c, 0x69, 0x63,
	0x65, 0x6e, 0x73, 0x65, 0x12, 0x1f, 0x0a, 0x0b, 0x6d, 0x65, 0x72, 0x63, 0x68, 0x61, 0x6e, 0x74,
	0x5f, 0x69, 0x64, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x04, 0x52, 0x0a, 0x6d, 0x65, 0x72, 0x63, 0x68,
	0x61, 0x6e, 0x74, 0x49, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x6c, 0x6f, 0x67, 0x6f, 0x18, 0x0d, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x04, 0x6c, 0x6f, 0x67, 0x6f, 0x22, 0x45, 0x0a, 0x15, 0x49, 0x73, 0x53,
	0x65, 0x6e, 0x64, 0x45, 0x6d, 0x61, 0x69, 0x6c, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x56, 0x31, 0x12, 0x2c, 0x0a, 0x12, 0x73, 0x65, 0x6e, 0x64, 0x5f, 0x65, 0x6d, 0x61, 0x69, 0x6c,
	0x5f, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x52, 0x10,
	0x73, 0x65, 0x6e, 0x64, 0x45, 0x6d, 0x61, 0x69, 0x6c, 0x45, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x64,
	0x22, 0x50, 0x0a, 0x24, 0x47, 0x65, 0x74, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e,
	0x67, 0x50, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x73, 0x42, 0x79, 0x42, 0x55, 0x49, 0x44, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x56, 0x31, 0x12, 0x28, 0x0a, 0x10, 0x62, 0x75, 0x73, 0x69,
	0x6e, 0x65, 0x73, 0x73, 0x5f, 0x75, 0x6e, 0x69, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x04, 0x52, 0x0e, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x55, 0x6e, 0x69, 0x74,
	0x49, 0x64, 0x22, 0x9c, 0x01, 0x0a, 0x25, 0x47, 0x65, 0x74, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73,
	0x73, 0x69, 0x6e, 0x67, 0x50, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x73, 0x42, 0x79, 0x42, 0x55,
	0x49, 0x44, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x56, 0x31, 0x12, 0x28, 0x0a, 0x10,
	0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x75, 0x6e, 0x69, 0x74, 0x5f, 0x69, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x0e, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73,
	0x55, 0x6e, 0x69, 0x74, 0x49, 0x64, 0x12, 0x49, 0x0a, 0x08, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63,
	0x74, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2d, 0x2e, 0x70, 0x72, 0x6f, 0x63, 0x65,
	0x73, 0x73, 0x69, 0x6e, 0x67, 0x2e, 0x6d, 0x65, 0x72, 0x63, 0x68, 0x61, 0x6e, 0x74, 0x2e, 0x70,
	0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x2e, 0x50, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x42, 0x61,
	0x73, 0x69, 0x63, 0x44, 0x61, 0x74, 0x61, 0x52, 0x08, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74,
	0x73, 0x22, 0x54, 0x0a, 0x10, 0x50, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x42, 0x61, 0x73, 0x69,
	0x63, 0x44, 0x61, 0x74, 0x61, 0x12, 0x1d, 0x0a, 0x0a, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74,
	0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x70, 0x72, 0x6f, 0x6a, 0x65,
	0x63, 0x74, 0x49, 0x64, 0x12, 0x21, 0x0a, 0x0c, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x5f,
	0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x70, 0x72, 0x6f, 0x6a,
	0x65, 0x63, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x32, 0xa7, 0x03, 0x0a, 0x07, 0x50, 0x72, 0x6f, 0x6a,
	0x65, 0x63, 0x74, 0x12, 0x7c, 0x0a, 0x17, 0x47, 0x65, 0x74, 0x50, 0x72, 0x6f, 0x6a, 0x65, 0x63,
	0x74, 0x73, 0x42, 0x79, 0x4d, 0x65, 0x72, 0x63, 0x68, 0x61, 0x6e, 0x74, 0x49, 0x44, 0x12, 0x2e,
	0x2e, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x2e, 0x6d, 0x65, 0x72, 0x63,
	0x68, 0x61, 0x6e, 0x74, 0x2e, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x2e, 0x50, 0x72, 0x6f,
	0x6a, 0x65, 0x63, 0x74, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x56, 0x31, 0x1a, 0x2f,
	0x2e, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x2e, 0x6d, 0x65, 0x72, 0x63,
	0x68, 0x61, 0x6e, 0x74, 0x2e, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x2e, 0x50, 0x72, 0x6f,
	0x6a, 0x65, 0x63, 0x74, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x56, 0x31, 0x22,
	0x00, 0x12, 0x73, 0x0a, 0x0b, 0x49, 0x73, 0x53, 0x65, 0x6e, 0x64, 0x45, 0x6d, 0x61, 0x69, 0x6c,
	0x12, 0x2e, 0x2e, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x2e, 0x6d, 0x65,
	0x72, 0x63, 0x68, 0x61, 0x6e, 0x74, 0x2e, 0x6d, 0x65, 0x72, 0x63, 0x68, 0x61, 0x6e, 0x74, 0x2e,
	0x50, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x56, 0x31,
	0x1a, 0x32, 0x2e, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x2e, 0x6d, 0x65,
	0x72, 0x63, 0x68, 0x61, 0x6e, 0x74, 0x2e, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x2e, 0x49,
	0x73, 0x53, 0x65, 0x6e, 0x64, 0x45, 0x6d, 0x61, 0x69, 0x6c, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x56, 0x31, 0x22, 0x00, 0x12, 0xa8, 0x01, 0x0a, 0x1d, 0x47, 0x65, 0x74, 0x50, 0x72,
	0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x50, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x73,
	0x42, 0x79, 0x42, 0x55, 0x49, 0x44, 0x56, 0x31, 0x12, 0x41, 0x2e, 0x70, 0x72, 0x6f, 0x63, 0x65,
	0x73, 0x73, 0x69, 0x6e, 0x67, 0x2e, 0x6d, 0x65, 0x72, 0x63, 0x68, 0x61, 0x6e, 0x74, 0x2e, 0x70,
	0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x2e, 0x47, 0x65, 0x74, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73,
	0x73, 0x69, 0x6e, 0x67, 0x50, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x73, 0x42, 0x79, 0x42, 0x55,
	0x49, 0x44, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x56, 0x31, 0x1a, 0x42, 0x2e, 0x70, 0x72,
	0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x2e, 0x6d, 0x65, 0x72, 0x63, 0x68, 0x61, 0x6e,
	0x74, 0x2e, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x2e, 0x47, 0x65, 0x74, 0x50, 0x72, 0x6f,
	0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x50, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x73, 0x42,
	0x79, 0x42, 0x55, 0x49, 0x44, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x56, 0x31, 0x22,
	0x00, 0x42, 0x2d, 0x5a, 0x2b, 0x67, 0x69, 0x74, 0x2e, 0x6c, 0x6f, 0x63, 0x61, 0x6c, 0x2f, 0x73,
	0x65, 0x6e, 0x73, 0x69, 0x74, 0x69, 0x76, 0x65, 0x2f, 0x69, 0x6e, 0x6e, 0x65, 0x72, 0x70, 0x62,
	0x2f, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x2f, 0x67, 0x72, 0x70, 0x63,
	0x62, 0x08, 0x65, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x70, 0xe8, 0x07,
})

var (
	file_inner_processing_grpc_project_proto_rawDescOnce sync.Once
	file_inner_processing_grpc_project_proto_rawDescData []byte
)

func file_inner_processing_grpc_project_proto_rawDescGZIP() []byte {
	file_inner_processing_grpc_project_proto_rawDescOnce.Do(func() {
		file_inner_processing_grpc_project_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_inner_processing_grpc_project_proto_rawDesc), len(file_inner_processing_grpc_project_proto_rawDesc)))
	})
	return file_inner_processing_grpc_project_proto_rawDescData
}

var file_inner_processing_grpc_project_proto_msgTypes = make([]protoimpl.MessageInfo, 7)
var file_inner_processing_grpc_project_proto_goTypes = []any{
	(*ProjectsRequestV1)(nil),                     // 0: processing.merchant.project.ProjectsRequestV1
	(*ProjectsResponseV1)(nil),                    // 1: processing.merchant.project.ProjectsResponseV1
	(*ProjectData)(nil),                           // 2: processing.merchant.project.ProjectData
	(*IsSendEmailResponseV1)(nil),                 // 3: processing.merchant.project.IsSendEmailResponseV1
	(*GetProcessingProjectsByBUIDRequestV1)(nil),  // 4: processing.merchant.project.GetProcessingProjectsByBUIDRequestV1
	(*GetProcessingProjectsByBUIDResponseV1)(nil), // 5: processing.merchant.project.GetProcessingProjectsByBUIDResponseV1
	(*ProjectBasicData)(nil),                      // 6: processing.merchant.project.ProjectBasicData
	(*MerchantData)(nil),                          // 7: processing.merchant.merchant.MerchantData
	(*ProjectRequestV1)(nil),                      // 8: processing.merchant.merchant.ProjectRequestV1
}
var file_inner_processing_grpc_project_proto_depIdxs = []int32{
	2, // 0: processing.merchant.project.ProjectsResponseV1.projects:type_name -> processing.merchant.project.ProjectData
	7, // 1: processing.merchant.project.ProjectsResponseV1.merchant:type_name -> processing.merchant.merchant.MerchantData
	6, // 2: processing.merchant.project.GetProcessingProjectsByBUIDResponseV1.projects:type_name -> processing.merchant.project.ProjectBasicData
	0, // 3: processing.merchant.project.Project.GetProjectsByMerchantID:input_type -> processing.merchant.project.ProjectsRequestV1
	8, // 4: processing.merchant.project.Project.IsSendEmail:input_type -> processing.merchant.merchant.ProjectRequestV1
	4, // 5: processing.merchant.project.Project.GetProcessingProjectsByBUIDV1:input_type -> processing.merchant.project.GetProcessingProjectsByBUIDRequestV1
	1, // 6: processing.merchant.project.Project.GetProjectsByMerchantID:output_type -> processing.merchant.project.ProjectsResponseV1
	3, // 7: processing.merchant.project.Project.IsSendEmail:output_type -> processing.merchant.project.IsSendEmailResponseV1
	5, // 8: processing.merchant.project.Project.GetProcessingProjectsByBUIDV1:output_type -> processing.merchant.project.GetProcessingProjectsByBUIDResponseV1
	6, // [6:9] is the sub-list for method output_type
	3, // [3:6] is the sub-list for method input_type
	3, // [3:3] is the sub-list for extension type_name
	3, // [3:3] is the sub-list for extension extendee
	0, // [0:3] is the sub-list for field type_name
}

func init() { file_inner_processing_grpc_project_proto_init() }
func file_inner_processing_grpc_project_proto_init() {
	if File_inner_processing_grpc_project_proto != nil {
		return
	}
	file_inner_processing_grpc_merchant_proto_init()
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_inner_processing_grpc_project_proto_rawDesc), len(file_inner_processing_grpc_project_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   7,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_inner_processing_grpc_project_proto_goTypes,
		DependencyIndexes: file_inner_processing_grpc_project_proto_depIdxs,
		MessageInfos:      file_inner_processing_grpc_project_proto_msgTypes,
	}.Build()
	File_inner_processing_grpc_project_proto = out.File
	file_inner_processing_grpc_project_proto_goTypes = nil
	file_inner_processing_grpc_project_proto_depIdxs = nil
}
