package schema

import (
	gorpc "git.local/sensitive/innerpb/processing/grpc"
)

type PanByHashedIdRequest struct {
	EncryptedKey string `json:"encrypted_key"`
}

type PanByCardIdRequest struct {
	CardId uint64 `json:"card_id"`
}

func NewPanByCardIdRequestFromRaw(v1 *gorpc.GetPanByCardIdRequestV1) PanByCardIdRequest {
	return PanByCardIdRequest{
		CardId: v1.GetCardId(),
	}
}

func NewPanByHashedIdRequestFromRaw(v1 *gorpc.GetPanByHashedIdRequestV1) PanByHashedIdRequest {
	return PanByHashedIdRequest{
		EncryptedKey: v1.GetEncryptedKey(),
	}
}

type PanResponse struct {
	Pan       string `json:"pan"`
	IpsID     uint64 `json:"ips_id"`
	CountryID uint64 `json:"country_id"`
	IssuerID  uint64 `json:"issuer_id"`
}
