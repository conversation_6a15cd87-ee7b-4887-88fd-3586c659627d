package service

import (
	"context"
	"git.local/sensitive/innerpb/processing/goerr"
	"git.local/sensitive/pkg/gtransaction"
	"git.local/sensitive/processing/acquirer/model"
	"git.local/sensitive/processing/acquirer/repository/database"
	"git.local/sensitive/processing/acquirer/schema"
	"git.local/sensitive/sdk/dog"
	"go.uber.org/zap"
)

type RulePercentageService struct {
	acquirerRepo       database.Acquirer
	rulePercentageRepo database.RulePercentager
	transaction        gtransaction.Manager
}

func NewRulePercentageService(
	acquirerRepo database.Acquirer,
	rulePercentageRepo database.RulePercentager,
	transaction gtransaction.Manager,
) *RulePercentageService {
	return &RulePercentageService{
		acquirerRepo:       acquirerRepo,
		rulePercentageRepo: rulePercentageRepo,
		transaction:        transaction,
	}
}

// Replace Метод предназначен для создания процентажа балансировки
// Перед созданием новой записи, метод удаляет старые (атомарно)
func (rps RulePercentageService) Replace(
	ctx context.Context,
	request schema.CreateRulePercentageRequest, ruleID uint64) (err error) {
	ctx, span := dog.CreateSpan(ctx, "RulePercentageService_Replace")
	defer span.End()

	ctx = rps.transaction.Begin(ctx)
	defer func() {
		err = rps.transaction.Finish(ctx, err)
	}()

	acquirerIds := request.RetrieveAcquirerIds()

	acquirers, err := rps.acquirerRepo.GetAcquirersById(ctx, acquirerIds)

	if err != nil {
		dog.L().Error(
			"RulePercentageService_Replace_GetAcquirersById",
			zap.Uint64("rule_id", ruleID),
			zap.Error(err),
		)

		return err
	}
	// check if user entered valid/existing acquirer ids
	if len(acquirers) != len(acquirerIds) {
		return goerr.ErrAcquirerNotFound
	}

	if err = rps.rulePercentageRepo.Delete(ctx, ruleID); err != nil {
		dog.L().Error(
			"RulePercentageService_Replace_Delete",
			zap.Uint64("rule_id", ruleID),
			zap.Error(err),
		)

		return err
	}

	balancers := request.ToRulePercentages(ruleID)

	if err = rps.rulePercentageRepo.Create(ctx, balancers); err != nil {
		dog.L().Error(
			"RulePercentageService_Replace_Create",
			zap.Uint64("rule_id", ruleID),
			zap.Error(err),
		)

		return err
	}

	return nil
}

func (rps RulePercentageService) GetAllByRuleID(
	ctx context.Context,
	ruleID uint64,
) (_ model.RulePercentages, err error) {
	ctx, span := dog.CreateSpan(ctx, "RulePercentageService_GetAllByRuleID")
	defer span.End()

	return rps.rulePercentageRepo.GetAllByRuleID(ctx, ruleID)
}
