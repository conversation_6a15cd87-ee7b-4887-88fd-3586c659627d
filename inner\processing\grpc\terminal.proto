edition = "2023";

package processing.acquirer.terminal;

option go_package = "git.local/sensitive/innerpb/processing/grpc";

import "google/protobuf/struct.proto";

service Terminal{
  //GetByTerminalID Запрос на получение данных терминала и его эквайера
  rpc GetByTerminalID(TerminalRequestV1) returns (TerminalResponseV1) {}
  //GetAllActiveTerminals Запрос на получение всех активных терминалов
  rpc FindActiveTerminalsByProject(ActiveTerminalsByProjectRequestV1) returns (ActiveTerminalsByProjectResponseV1) {}
  rpc SearchTerminal(SearchTerminalReqDataV1) returns (SearchTerminalResDataV1) {}
  rpc ExtendedSearchTerminal(ExtendedSearchTerminalReqDataV1) returns (SearchTerminalResDataV1) {}
  rpc GetTerminalsByProjectId (GetTerminalsByProjectIdRequestV1) returns (ActiveTerminalsByProjectResponseV1) {}
  rpc GetTerminalWithJusan(SearchTerminalReqDataV1) returns (GetTerminalWithJusanResponseV1) {}
  rpc GetRuleByActiveTerminals(RuleByActiveTerminalsReqV1) returns (RuleByActiveTerminalsResponseV1) {}
  rpc GetPayInProjectTerminals(GetPayInProjectTerminalsReqV1) returns (GetPayInProjectTerminalsResponseV1){}
}

//запрос на получение данных терминала и его эквайера
message TerminalRequestV1 {
  uint64 terminal_id = 1;
}

message AcquirerData {
  uint64 acquirer_id = 1;
  string acquirer_code = 2;
  string name = 3;
  uint64 bank_id = 4;
  string bank_name = 5;
  string contract = 6;
  string description = 7;
  uint64 country_id = 8;
  bool is_active = 9;
}

//ответ на получение данных терминала и его эквайера
message TerminalResponseV1 {
  uint64 terminal_id = 1;
  uint64 project_id = 2;
  google.protobuf.Struct config = 3;
  AcquirerData acquirer = 4;
  uint32 two_stage_timeout = 5;
  string account_number = 6;
  bool is_transit = 7;
}

// Данные в сервис acquirer для cards
message ActiveTerminalsByProjectRequestV1 {
  uint64 project_id = 1;
  uint64 transaction_type_id = 2;
}

message ActiveTerminalsByProjectResponseV1 {
  repeated ActiveTerminalsByProjectV1 Data = 1;
}

message TerminalProjectV1 {
  uint64 id = 1;
  uint64 project_id = 2;
  uint64 transaction_type_id = 3;
  uint64 terminal_id = 4;
}

// Данные из сервиса acquirer для cards
message ActiveTerminalsByProjectV1 {
  uint64 id = 1;
  uint64 acquirer_id = 2;
  google.protobuf.Struct config = 3;
  int32 status = 4;
  repeated TerminalProjectV1 terminal_projects = 5;
}

message SearchTerminalReqDataV1 {
  uint64 project_id = 1;
  uint64 transaction_type_id = 2;
  double amount = 3;
  uint64 ips_id = 4;
  uint64 issuer_id = 5;
  uint64 country_id = 6;
  uint64 merchant_id = 7;
}

message ExtendedSearchTerminalReqDataV1 {
  SearchTerminalReqDataV1 search_terminal_req = 1;
  repeated uint64 terminal_ids = 2;
}

message SearchTerminalResDataV1 {
  uint64 terminal_id = 1;
  google.protobuf.Struct config = 2;
  AcquirerDataV1 acquirer = 3;
}

message AcquirerDataV1 {
  uint64 acquirer_id = 1;
  string code = 2;
}

message GetTerminalsByProjectIdRequestV1 {
  uint64 project_id = 1;
}

message GetTerminalWithJusanResponseV1 {
  SearchTerminalResDataV1 terminal = 1;
  uint64 jusan_terminal_id = 2;
  google.protobuf.Struct jusan_config = 3;
  AcquirerDataV1 jusan_acquirer = 4;
}

message RuleByActiveTerminalsReqV1 {
  SearchTerminalReqDataV1 search_terminal_req = 1;
  repeated uint64 terminal_ids = 2;
}

message RuleByActiveTerminals {
  uint64 rule_id = 1;
  uint64 project_id = 2;
  uint64 transaction_type_id = 3;
  uint64 ips_id = 4;
  uint64 issuer_id = 5;
  uint64 country_id = 6;
  double amount_from = 7;
  double amount_to = 8;
  uint64 weight = 9;
  bool is_active = 10;
  bool is_base = 11;
}

message RuleByActiveTerminalsResponseV1 {
    RuleByActiveTerminals rule = 1;
    bool isRuleFound = 2;
}

message GetPayInProjectTerminalsReqV1 {
  uint64 project_id = 1;
}

message GetPayInProjectTerminalsResponseV1 {
  repeated uint64 terminal_ids = 1;
}