// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.5
// 	protoc        v5.29.3
// source: inner/processing/events/commission.proto

package events

import (
	_ "git.local/sensitive/mvp/pb"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type CreateCommission struct {
	state             protoimpl.MessageState `protogen:"open.v1"`
	AcquirerId        uint64                 `protobuf:"varint,1,opt,name=acquirer_id,json=acquirerId,proto3" json:"acquirer_id,omitempty"`
	ProjectId         uint64                 `protobuf:"varint,2,opt,name=project_id,json=projectId,proto3" json:"project_id,omitempty"`
	MerchantId        uint64                 `protobuf:"varint,3,opt,name=merchant_id,json=merchantId,proto3" json:"merchant_id,omitempty"`
	TransactionTypeId uint64                 `protobuf:"varint,4,opt,name=transaction_type_id,json=transactionTypeId,proto3" json:"transaction_type_id,omitempty"`
	IpsId             uint64                 `protobuf:"varint,5,opt,name=ips_id,json=ipsId,proto3" json:"ips_id,omitempty"`
	IssuerId          uint64                 `protobuf:"varint,6,opt,name=issuer_id,json=issuerId,proto3" json:"issuer_id,omitempty"`
	CountryId         uint64                 `protobuf:"varint,7,opt,name=country_id,json=countryId,proto3" json:"country_id,omitempty"`
	unknownFields     protoimpl.UnknownFields
	sizeCache         protoimpl.SizeCache
}

func (x *CreateCommission) Reset() {
	*x = CreateCommission{}
	mi := &file_inner_processing_events_commission_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreateCommission) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateCommission) ProtoMessage() {}

func (x *CreateCommission) ProtoReflect() protoreflect.Message {
	mi := &file_inner_processing_events_commission_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateCommission.ProtoReflect.Descriptor instead.
func (*CreateCommission) Descriptor() ([]byte, []int) {
	return file_inner_processing_events_commission_proto_rawDescGZIP(), []int{0}
}

func (x *CreateCommission) GetAcquirerId() uint64 {
	if x != nil {
		return x.AcquirerId
	}
	return 0
}

func (x *CreateCommission) GetProjectId() uint64 {
	if x != nil {
		return x.ProjectId
	}
	return 0
}

func (x *CreateCommission) GetMerchantId() uint64 {
	if x != nil {
		return x.MerchantId
	}
	return 0
}

func (x *CreateCommission) GetTransactionTypeId() uint64 {
	if x != nil {
		return x.TransactionTypeId
	}
	return 0
}

func (x *CreateCommission) GetIpsId() uint64 {
	if x != nil {
		return x.IpsId
	}
	return 0
}

func (x *CreateCommission) GetIssuerId() uint64 {
	if x != nil {
		return x.IssuerId
	}
	return 0
}

func (x *CreateCommission) GetCountryId() uint64 {
	if x != nil {
		return x.CountryId
	}
	return 0
}

type CalculateAndSaveCommission struct {
	state                protoimpl.MessageState `protogen:"open.v1"`
	AcquirerId           uint64                 `protobuf:"varint,1,opt,name=acquirer_id,json=acquirerId,proto3" json:"acquirer_id,omitempty"`
	ProjectId            uint64                 `protobuf:"varint,2,opt,name=project_id,json=projectId,proto3" json:"project_id,omitempty"`
	MerchantId           uint64                 `protobuf:"varint,3,opt,name=merchant_id,json=merchantId,proto3" json:"merchant_id,omitempty"`
	AggregatedTypeId     uint64                 `protobuf:"varint,4,opt,name=aggregated_type_id,json=aggregatedTypeId,proto3" json:"aggregated_type_id,omitempty"`
	IpsId                uint64                 `protobuf:"varint,5,opt,name=ips_id,json=ipsId,proto3" json:"ips_id,omitempty"`
	IssuerId             uint64                 `protobuf:"varint,6,opt,name=issuer_id,json=issuerId,proto3" json:"issuer_id,omitempty"`
	CountryId            uint64                 `protobuf:"varint,7,opt,name=country_id,json=countryId,proto3" json:"country_id,omitempty"`
	TransactionId        uint64                 `protobuf:"varint,8,opt,name=transaction_id,json=transactionId,proto3" json:"transaction_id,omitempty"`
	TransactionAmount    float64                `protobuf:"fixed64,9,opt,name=transaction_amount,json=transactionAmount,proto3" json:"transaction_amount,omitempty"`
	TransactionCreatedAt *timestamppb.Timestamp `protobuf:"bytes,10,opt,name=transaction_created_at,json=transactionCreatedAt,proto3" json:"transaction_created_at,omitempty"`
	unknownFields        protoimpl.UnknownFields
	sizeCache            protoimpl.SizeCache
}

func (x *CalculateAndSaveCommission) Reset() {
	*x = CalculateAndSaveCommission{}
	mi := &file_inner_processing_events_commission_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CalculateAndSaveCommission) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CalculateAndSaveCommission) ProtoMessage() {}

func (x *CalculateAndSaveCommission) ProtoReflect() protoreflect.Message {
	mi := &file_inner_processing_events_commission_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CalculateAndSaveCommission.ProtoReflect.Descriptor instead.
func (*CalculateAndSaveCommission) Descriptor() ([]byte, []int) {
	return file_inner_processing_events_commission_proto_rawDescGZIP(), []int{1}
}

func (x *CalculateAndSaveCommission) GetAcquirerId() uint64 {
	if x != nil {
		return x.AcquirerId
	}
	return 0
}

func (x *CalculateAndSaveCommission) GetProjectId() uint64 {
	if x != nil {
		return x.ProjectId
	}
	return 0
}

func (x *CalculateAndSaveCommission) GetMerchantId() uint64 {
	if x != nil {
		return x.MerchantId
	}
	return 0
}

func (x *CalculateAndSaveCommission) GetAggregatedTypeId() uint64 {
	if x != nil {
		return x.AggregatedTypeId
	}
	return 0
}

func (x *CalculateAndSaveCommission) GetIpsId() uint64 {
	if x != nil {
		return x.IpsId
	}
	return 0
}

func (x *CalculateAndSaveCommission) GetIssuerId() uint64 {
	if x != nil {
		return x.IssuerId
	}
	return 0
}

func (x *CalculateAndSaveCommission) GetCountryId() uint64 {
	if x != nil {
		return x.CountryId
	}
	return 0
}

func (x *CalculateAndSaveCommission) GetTransactionId() uint64 {
	if x != nil {
		return x.TransactionId
	}
	return 0
}

func (x *CalculateAndSaveCommission) GetTransactionAmount() float64 {
	if x != nil {
		return x.TransactionAmount
	}
	return 0
}

func (x *CalculateAndSaveCommission) GetTransactionCreatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.TransactionCreatedAt
	}
	return nil
}

var File_inner_processing_events_commission_proto protoreflect.FileDescriptor

var file_inner_processing_events_commission_proto_rawDesc = string([]byte{
	0x0a, 0x28, 0x69, 0x6e, 0x6e, 0x65, 0x72, 0x2f, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69,
	0x6e, 0x67, 0x2f, 0x65, 0x76, 0x65, 0x6e, 0x74, 0x73, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x69, 0x73,
	0x73, 0x69, 0x6f, 0x6e, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1f, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x74, 0x69, 0x6d, 0x65,
	0x73, 0x74, 0x61, 0x6d, 0x70, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x16, 0x6d, 0x76, 0x70,
	0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f, 0x65, 0x76, 0x65, 0x6e, 0x74, 0x73, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x22, 0xfd, 0x01, 0x0a, 0x10, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x43, 0x6f,
	0x6d, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x1f, 0x0a, 0x0b, 0x61, 0x63, 0x71, 0x75,
	0x69, 0x72, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x0a, 0x61,
	0x63, 0x71, 0x75, 0x69, 0x72, 0x65, 0x72, 0x49, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x70, 0x72, 0x6f,
	0x6a, 0x65, 0x63, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x04, 0x52, 0x09, 0x70,
	0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x49, 0x64, 0x12, 0x1f, 0x0a, 0x0b, 0x6d, 0x65, 0x72, 0x63,
	0x68, 0x61, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x04, 0x52, 0x0a, 0x6d,
	0x65, 0x72, 0x63, 0x68, 0x61, 0x6e, 0x74, 0x49, 0x64, 0x12, 0x2e, 0x0a, 0x13, 0x74, 0x72, 0x61,
	0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x5f, 0x69, 0x64,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x04, 0x52, 0x11, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74,
	0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x49, 0x64, 0x12, 0x15, 0x0a, 0x06, 0x69, 0x70, 0x73,
	0x5f, 0x69, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x04, 0x52, 0x05, 0x69, 0x70, 0x73, 0x49, 0x64,
	0x12, 0x1b, 0x0a, 0x09, 0x69, 0x73, 0x73, 0x75, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x06, 0x20,
	0x01, 0x28, 0x04, 0x52, 0x08, 0x69, 0x73, 0x73, 0x75, 0x65, 0x72, 0x49, 0x64, 0x12, 0x1d, 0x0a,
	0x0a, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x72, 0x79, 0x5f, 0x69, 0x64, 0x18, 0x07, 0x20, 0x01, 0x28,
	0x04, 0x52, 0x09, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x72, 0x79, 0x49, 0x64, 0x3a, 0x05, 0xb8, 0xe9,
	0xe4, 0x03, 0x01, 0x22, 0xad, 0x03, 0x0a, 0x1a, 0x43, 0x61, 0x6c, 0x63, 0x75, 0x6c, 0x61, 0x74,
	0x65, 0x41, 0x6e, 0x64, 0x53, 0x61, 0x76, 0x65, 0x43, 0x6f, 0x6d, 0x6d, 0x69, 0x73, 0x73, 0x69,
	0x6f, 0x6e, 0x12, 0x1f, 0x0a, 0x0b, 0x61, 0x63, 0x71, 0x75, 0x69, 0x72, 0x65, 0x72, 0x5f, 0x69,
	0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x0a, 0x61, 0x63, 0x71, 0x75, 0x69, 0x72, 0x65,
	0x72, 0x49, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x5f, 0x69,
	0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x04, 0x52, 0x09, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74,
	0x49, 0x64, 0x12, 0x1f, 0x0a, 0x0b, 0x6d, 0x65, 0x72, 0x63, 0x68, 0x61, 0x6e, 0x74, 0x5f, 0x69,
	0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x04, 0x52, 0x0a, 0x6d, 0x65, 0x72, 0x63, 0x68, 0x61, 0x6e,
	0x74, 0x49, 0x64, 0x12, 0x2c, 0x0a, 0x12, 0x61, 0x67, 0x67, 0x72, 0x65, 0x67, 0x61, 0x74, 0x65,
	0x64, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x04, 0x52,
	0x10, 0x61, 0x67, 0x67, 0x72, 0x65, 0x67, 0x61, 0x74, 0x65, 0x64, 0x54, 0x79, 0x70, 0x65, 0x49,
	0x64, 0x12, 0x15, 0x0a, 0x06, 0x69, 0x70, 0x73, 0x5f, 0x69, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28,
	0x04, 0x52, 0x05, 0x69, 0x70, 0x73, 0x49, 0x64, 0x12, 0x1b, 0x0a, 0x09, 0x69, 0x73, 0x73, 0x75,
	0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x06, 0x20, 0x01, 0x28, 0x04, 0x52, 0x08, 0x69, 0x73, 0x73,
	0x75, 0x65, 0x72, 0x49, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x72, 0x79,
	0x5f, 0x69, 0x64, 0x18, 0x07, 0x20, 0x01, 0x28, 0x04, 0x52, 0x09, 0x63, 0x6f, 0x75, 0x6e, 0x74,
	0x72, 0x79, 0x49, 0x64, 0x12, 0x25, 0x0a, 0x0e, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74,
	0x69, 0x6f, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x08, 0x20, 0x01, 0x28, 0x04, 0x52, 0x0d, 0x74, 0x72,
	0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x12, 0x2d, 0x0a, 0x12, 0x74,
	0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x61, 0x6d, 0x6f, 0x75, 0x6e,
	0x74, 0x18, 0x09, 0x20, 0x01, 0x28, 0x01, 0x52, 0x11, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63,
	0x74, 0x69, 0x6f, 0x6e, 0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x50, 0x0a, 0x16, 0x74, 0x72,
	0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65,
	0x64, 0x5f, 0x61, 0x74, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d,
	0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x14, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74,
	0x69, 0x6f, 0x6e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x3a, 0x05, 0xb8, 0xe9,
	0xe4, 0x03, 0x01, 0x42, 0x2f, 0x5a, 0x2d, 0x67, 0x69, 0x74, 0x2e, 0x6c, 0x6f, 0x63, 0x61, 0x6c,
	0x2f, 0x73, 0x65, 0x6e, 0x73, 0x69, 0x74, 0x69, 0x76, 0x65, 0x2f, 0x69, 0x6e, 0x6e, 0x65, 0x72,
	0x70, 0x62, 0x2f, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x2f, 0x65, 0x76,
	0x65, 0x6e, 0x74, 0x73, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
})

var (
	file_inner_processing_events_commission_proto_rawDescOnce sync.Once
	file_inner_processing_events_commission_proto_rawDescData []byte
)

func file_inner_processing_events_commission_proto_rawDescGZIP() []byte {
	file_inner_processing_events_commission_proto_rawDescOnce.Do(func() {
		file_inner_processing_events_commission_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_inner_processing_events_commission_proto_rawDesc), len(file_inner_processing_events_commission_proto_rawDesc)))
	})
	return file_inner_processing_events_commission_proto_rawDescData
}

var file_inner_processing_events_commission_proto_msgTypes = make([]protoimpl.MessageInfo, 2)
var file_inner_processing_events_commission_proto_goTypes = []any{
	(*CreateCommission)(nil),           // 0: CreateCommission
	(*CalculateAndSaveCommission)(nil), // 1: CalculateAndSaveCommission
	(*timestamppb.Timestamp)(nil),      // 2: google.protobuf.Timestamp
}
var file_inner_processing_events_commission_proto_depIdxs = []int32{
	2, // 0: CalculateAndSaveCommission.transaction_created_at:type_name -> google.protobuf.Timestamp
	1, // [1:1] is the sub-list for method output_type
	1, // [1:1] is the sub-list for method input_type
	1, // [1:1] is the sub-list for extension type_name
	1, // [1:1] is the sub-list for extension extendee
	0, // [0:1] is the sub-list for field type_name
}

func init() { file_inner_processing_events_commission_proto_init() }
func file_inner_processing_events_commission_proto_init() {
	if File_inner_processing_events_commission_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_inner_processing_events_commission_proto_rawDesc), len(file_inner_processing_events_commission_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   2,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_inner_processing_events_commission_proto_goTypes,
		DependencyIndexes: file_inner_processing_events_commission_proto_depIdxs,
		MessageInfos:      file_inner_processing_events_commission_proto_msgTypes,
	}.Build()
	File_inner_processing_events_commission_proto = out.File
	file_inner_processing_events_commission_proto_goTypes = nil
	file_inner_processing_events_commission_proto_depIdxs = nil
}
