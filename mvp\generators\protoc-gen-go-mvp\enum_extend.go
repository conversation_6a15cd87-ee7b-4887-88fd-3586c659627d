package main

import (
	"strconv"
	"strings"

	"google.golang.org/protobuf/compiler/protogen"
	"google.golang.org/protobuf/proto"

	"git.local/sensitive/mvp/pb"
)

func init() {
	RegisterGenerator(&EnumExtendGenerator{})
}

type EnumExtendGenerator struct{}

func (generator *EnumExtendGenerator) ProtoGenGenerate(gen *protogen.Plugin) {
	for _, file := range gen.Files {
		if !file.Generate {
			continue
		}

		if len(file.Enums) == 0 {
			return
		}

		g := gen.NewGeneratedFile(
			file.GeneratedFilenamePrefix+V2_ENUM_EXTEND_SUFFIX,
			file.GoImportPath,
		)
		HeaderPrint(g, string(file.GoPackageName))

		for _, enum := range file.Enums {
			g.P("func (val ", enum.GoIdent, ") Synonym() ", enum.GoIdent, " {")
			g.P("if _, ok := ", enum.GoIdent, "_name[int32(val)]; ok {")
			g.P("return val")
			g.P("}")
			g.P()
			g.P("return ", enum.GoIdent, "(", minInt32, ")")
			g.P("}")
			g.P()

			g.P("func (val ", enum.GoIdent, ") Int() int {")
			g.P("return int(val.Synonym())")
			g.P("}")
			g.P()

			g.P("func (val ", enum.GoIdent, ") IntPtr() *int {")
			g.P("result := int(val.Synonym())")
			g.P("return &result")
			g.P("}")
			g.P()

			g.P("func (val ", enum.GoIdent, ") Int32() int32 {")
			g.P("return int32(val.Synonym())")
			g.P("}")
			g.P()

			g.P("func (val ", enum.GoIdent, ") Int32Ptr() *int32 {")
			g.P("result := int32(val.Synonym())")
			g.P("return &result")
			g.P("}")
			g.P()

			g.P("func (val ", enum.GoIdent, ") Int64() int64 {")
			g.P("return int64(val.Synonym())")
			g.P("}")
			g.P()

			g.P("func (val ", enum.GoIdent, ") Int64Ptr() *int64 {")
			g.P("result := int64(val.Synonym())")
			g.P("return &result")
			g.P("}")
			g.P()

			g.P("func (val ", enum.GoIdent, ") Uint() uint {")
			g.P("return uint(val.Synonym())")
			g.P("}")
			g.P()

			g.P("func (val ", enum.GoIdent, ") UintPtr() *uint {")
			g.P("result := uint(val.Synonym())")
			g.P("return &result")
			g.P("}")
			g.P()

			g.P("func (val ", enum.GoIdent, ") Uint32() uint32 {")
			g.P("return uint32(val.Synonym())")
			g.P("}")
			g.P()

			g.P("func (val ", enum.GoIdent, ") Uint32Ptr() *uint32 {")
			g.P("result := uint32(val.Synonym())")
			g.P("return &result")
			g.P("}")
			g.P()

			g.P("func (val ", enum.GoIdent, ") Uint64() uint64 {")
			g.P("return uint64(val.Synonym())")
			g.P("}")
			g.P()

			g.P("func (val ", enum.GoIdent, ") Uint64Ptr() *uint64 {")
			g.P("result := uint64(val.Synonym())")
			g.P("return &result")
			g.P("}")
			g.P()

			g.P("func (val ", enum.GoIdent, ") IsKnown() bool {")
			g.P("return val.Synonym() != ", enum.GoIdent, "(", minInt32, ")")
			g.P("}")
			g.P()

			g.P("func ConvertIntTo", enum.GoIdent, "(in int) ", enum.GoIdent, " {")
			g.P("return ", enum.GoIdent, "(in).Synonym()")
			g.P("}")
			g.P()

			g.P("func ConvertUintTo", enum.GoIdent, "(in uint) ", enum.GoIdent, " {")
			g.P("return ", enum.GoIdent, "(in).Synonym()")
			g.P("}")
			g.P()

			g.P("func ConvertInt32To", enum.GoIdent, "(in int32) ", enum.GoIdent, " {")
			g.P("return ", enum.GoIdent, "(in).Synonym()")
			g.P("}")
			g.P()

			g.P("func ConvertUint32To", enum.GoIdent, "(in uint32) ", enum.GoIdent, " {")
			g.P("return ", enum.GoIdent, "(in).Synonym()")
			g.P("}")
			g.P()

			g.P("func ConvertInt64To", enum.GoIdent, "(in int64) ", enum.GoIdent, " {")
			g.P("return ", enum.GoIdent, "(in).Synonym()")
			g.P("}")
			g.P()

			g.P("func ConvertUint64To", enum.GoIdent, "(in uint64) ", enum.GoIdent, " {")
			g.P("return ", enum.GoIdent, "(in).Synonym()")
			g.P("}")
			g.P()

			g.P("var ", enum.GoIdent, "_Lower_value = map[string]", enum.GoIdent, "{")
			for _, value := range enum.Values {
				if fromString, _ := proto.GetExtension(value.Desc.Options(), pb.E_FromString).(string); len(fromString) == 0 {
					g.P("\"", strings.ToLower(string(value.Desc.Name())), "\": ", value.Desc.Number(), ",")
				} else {
					g.P(strconv.Quote(strings.ToLower(fromString)), ": ", value.Desc.Number(), ",")
				}
			}

			g.P("}")
			g.P()

			g.P("func ConvertStringTo", enum.GoIdent, "(in string) ", enum.GoIdent, " {")
			g.P("if result, ok := ", enum.GoIdent, "_value[in]; ok {")
			g.P("return ", enum.GoIdent, "(result)")
			g.P("}")
			g.P()
			g.P("if result, ok := ", enum.GoIdent, "_Lower_value[", toLower, "(in)]; ok {")
			g.P("return ", enum.GoIdent, "(result)")
			g.P("}")
			g.P()
			g.P("return ", enum.GoIdent, "(", minInt32, ")")
			g.P("}")
			g.P()

			g.P("var Slice", enum.GoIdent, "Convert *slice", enum.GoIdent, "Convert")
			g.P()
			g.P("type slice", enum.GoIdent, "Convert struct{}")
			g.P()

			g.P("func (*slice", enum.GoIdent, "Convert) Synonym(in []", enum.GoIdent, ") []", enum.GoIdent, "{")
			g.P("result := make([]", enum.GoIdent, ", len(in))")
			g.P("for i, v := range in {")
			g.P("result[i] = v.Synonym()")
			g.P("}")
			g.P()
			g.P("return result")
			g.P("}")
			g.P()

			g.P("func (*slice", enum.GoIdent, "Convert) Int32(in []", enum.GoIdent, ") []int32 {")
			g.P("result := make([]int32, len(in))")
			g.P("for i, v := range in {")
			g.P("result[i] = v.Int32()")
			g.P("}")
			g.P()
			g.P("return result")
			g.P("}")
			g.P()

			g.P("func (*slice", enum.GoIdent, "Convert) Uint32(in []", enum.GoIdent, ") []uint32 {")
			g.P("result := make([]uint32, len(in))")
			g.P("for i, v := range in {")
			g.P("result[i] = v.Uint32()")
			g.P("}")
			g.P()
			g.P("return result")
			g.P("}")
			g.P()

			g.P("func (*slice", enum.GoIdent, "Convert) Uint64(in []", enum.GoIdent, ") []uint64 {")
			g.P("result := make([]uint64, len(in))")
			g.P("for i, v := range in {")
			g.P("result[i] = v.Uint64()")
			g.P("}")
			g.P()
			g.P("return result")
			g.P("}")
			g.P()

			g.P("func (*slice", enum.GoIdent, "Convert) Int64(in []", enum.GoIdent, ") []int64 {")
			g.P("result := make([]int64, len(in))")
			g.P("for i, v := range in {")
			g.P("result[i] = v.Int64()")
			g.P("}")
			g.P()
			g.P("return result")
			g.P("}")
			g.P()

			g.P("func (*slice", enum.GoIdent, "Convert) Uint(in []", enum.GoIdent, ") []uint {")
			g.P("result := make([]uint, len(in))")
			g.P("for i, v := range in {")
			g.P("result[i] = v.Uint()")
			g.P("}")
			g.P()
			g.P("return result")
			g.P("}")
			g.P()

			g.P("func (*slice", enum.GoIdent, "Convert) Int(in []", enum.GoIdent, ") []int {")
			g.P("result := make([]int, len(in))")
			g.P("for i, v := range in {")
			g.P("result[i] = v.Int()")
			g.P("}")
			g.P()
			g.P("return result")
			g.P("}")
			g.P()

			g.P("func (*slice", enum.GoIdent, "Convert) IsKnown(in []", enum.GoIdent, ") []bool {")
			g.P("result := make([]bool, len(in))")
			g.P("for i, v := range in {")
			g.P("result[i] = v.IsKnown()")
			g.P("}")
			g.P()
			g.P("return result")
			g.P("}")
			g.P()

			g.P("func (*slice", enum.GoIdent, "Convert) ConvertIntTo", enum.GoIdent, "(in []int) []", enum.GoIdent, " {")
			g.P("result := make([]", enum.GoIdent, ", len(in))")
			g.P("for i, v := range in {")
			g.P("result[i] = ConvertIntTo", enum.GoIdent, "(v)")
			g.P("}")
			g.P()
			g.P("return result")
			g.P("}")
			g.P()

			g.P("func (*slice", enum.GoIdent, "Convert) ConvertUintTo", enum.GoIdent, "(in []uint) []", enum.GoIdent, " {")
			g.P("result := make([]", enum.GoIdent, ", len(in))")
			g.P("for i, v := range in {")
			g.P("result[i] = ConvertUintTo", enum.GoIdent, "(v)")
			g.P("}")
			g.P()
			g.P("return result")
			g.P("}")
			g.P()

			g.P("func (*slice", enum.GoIdent, "Convert) ConvertInt32To", enum.GoIdent, "(in []int32) []", enum.GoIdent, " {")
			g.P("result := make([]", enum.GoIdent, ", len(in))")
			g.P("for i, v := range in {")
			g.P("result[i] = ConvertInt32To", enum.GoIdent, "(v)")
			g.P("}")
			g.P()
			g.P("return result")
			g.P("}")
			g.P()

			g.P("func (*slice", enum.GoIdent, "Convert) ConvertUint32To", enum.GoIdent, "(in []uint32) []", enum.GoIdent, " {")
			g.P("result := make([]", enum.GoIdent, ", len(in))")
			g.P("for i, v := range in {")
			g.P("result[i] = ConvertUint32To", enum.GoIdent, "(v)")
			g.P("}")
			g.P()
			g.P("return result")
			g.P("}")
			g.P()

			g.P("func (*slice", enum.GoIdent, "Convert) ConvertInt64To", enum.GoIdent, "(in []int64) []", enum.GoIdent, " {")
			g.P("result := make([]", enum.GoIdent, ", len(in))")
			g.P("for i, v := range in {")
			g.P("result[i] = ConvertInt64To", enum.GoIdent, "(v)")
			g.P("}")
			g.P()
			g.P("return result")
			g.P("}")
			g.P()

			g.P("func (*slice", enum.GoIdent, "Convert) ConvertUint64To", enum.GoIdent, "(in []uint64) []", enum.GoIdent, " {")
			g.P("result := make([]", enum.GoIdent, ", len(in))")
			g.P("for i, v := range in {")
			g.P("result[i] = ConvertUint64To", enum.GoIdent, "(v)")
			g.P("}")
			g.P()
			g.P("return result")
			g.P("}")
			g.P()

			g.P("func (*slice", enum.GoIdent, "Convert) ConvertStringTo", enum.GoIdent, "(in []string) []", enum.GoIdent, " {")
			g.P("result := make([]", enum.GoIdent, ", len(in))")
			g.P("for i, v := range in {")
			g.P("result[i] = ConvertStringTo", enum.GoIdent, "(v)")
			g.P("}")
			g.P()
			g.P("return result")
			g.P("}")
			g.P()

			g.P("func New", enum.GoIdent, "Usage() *", enum.GoIdent, "Usage {")
			g.P("return &", enum.GoIdent, "Usage{")
			g.P("enumMap: map[", enum.GoIdent, "]bool{")
			for _, value := range enum.Values {
				g.P(value.GoIdent, ": false,")
			}
			g.P("},")
			g.P("}")
			g.P("}")
			g.P()

			g.P("func Is", enum.GoIdent, "(target ", enum.GoIdent, ", matches ...", enum.GoIdent, ") bool {")
			g.P("for _, v := range matches {")
			g.P("if target == v {")
			g.P("return true")
			g.P("}")
			g.P("}")
			g.P()
			g.P("return false")
			g.P("}")
			g.P()

			g.P("type ", enum.GoIdent, "Usage struct {")
			g.P("enumMap map[", enum.GoIdent, "]bool")
			g.P("}")
			g.P()

			g.P("func (u *", enum.GoIdent, "Usage) Use(slice ...", enum.GoIdent, ") *", enum.GoIdent, "Usage {")
			g.P("for _, in := range slice {")
			g.P("u.enumMap[in] = true")
			g.P("}")
			g.P()
			g.P("return u")
			g.P("}")
			g.P()

			g.P("func (u *", enum.GoIdent, "Usage) Check() []string {")
			g.P("var result []string")
			g.P("for k, v := range u.enumMap {")
			g.P("if v {")
			g.P("continue")
			g.P("}")
			g.P()
			g.P("result = append(result, k.String())")
			g.P("}")
			g.P()
			g.P("return result")
			g.P("}")
			g.P()

			for _, value := range enum.Values {
				g.P("func (u *", enum.GoIdent, "Usage) ", value.GoIdent, "() ", enum.GoIdent, " {")
				g.P("u.Use(", value.GoIdent, ")")
				g.P("return ", value.GoIdent)
				g.P("}")
				g.P()
			}

		}
	}
}
