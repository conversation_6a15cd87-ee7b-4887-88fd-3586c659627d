// +build amd64
// Code generated by asm2asm, DO NOT EDIT.

package avx2

var _text_value = []byte{
	// .p2align 5, 0x00
	// LCPI0_0
	0x20, // .byte 32
	0x00, //0x00000001 .byte 0
	0x00, //0x00000002 .byte 0
	0x00, //0x00000003 .byte 0
	0x00, //0x00000004 .byte 0
	0x00, //0x00000005 .byte 0
	0x00, //0x00000006 .byte 0
	0x00, //0x00000007 .byte 0
	0x00, //0x00000008 .byte 0
	0x09, //0x00000009 .byte 9
	0x0a, //0x0000000a .byte 10
	0x00, //0x0000000b .byte 0
	0x00, //0x0000000c .byte 0
	0x0d, //0x0000000d .byte 13
	0x00, //0x0000000e .byte 0
	0x00, //0x0000000f .byte 0
	0x20, //0x00000010 .byte 32
	0x00, //0x00000011 .byte 0
	0x00, //0x00000012 .byte 0
	0x00, //0x00000013 .byte 0
	0x00, //0x00000014 .byte 0
	0x00, //0x00000015 .byte 0
	0x00, //0x00000016 .byte 0
	0x00, //0x00000017 .byte 0
	0x00, //0x00000018 .byte 0
	0x09, //0x00000019 .byte 9
	0x0a, //0x0000001a .byte 10
	0x00, //0x0000001b .byte 0
	0x00, //0x0000001c .byte 0
	0x0d, //0x0000001d .byte 13
	0x00, //0x0000001e .byte 0
	0x00, //0x0000001f .byte 0
	//0x00000020 LCPI0_1
	0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, //0x00000020 QUAD $0x2222222222222222; QUAD $0x2222222222222222  // .space 16, '""""""""""""""""'
	0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, //0x00000030 QUAD $0x2222222222222222; QUAD $0x2222222222222222  // .space 16, '""""""""""""""""'
	//0x00000040 LCPI0_2
	0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, //0x00000040 QUAD $0x5c5c5c5c5c5c5c5c; QUAD $0x5c5c5c5c5c5c5c5c  // .space 16, '\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\'
	0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, //0x00000050 QUAD $0x5c5c5c5c5c5c5c5c; QUAD $0x5c5c5c5c5c5c5c5c  // .space 16, '\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\'
	//0x00000060 LCPI0_3
	0x1f, 0x1f, 0x1f, 0x1f, 0x1f, 0x1f, 0x1f, 0x1f, 0x1f, 0x1f, 0x1f, 0x1f, 0x1f, 0x1f, 0x1f, 0x1f, //0x00000060 QUAD $0x1f1f1f1f1f1f1f1f; QUAD $0x1f1f1f1f1f1f1f1f  // .space 16, '\x1f\x1f\x1f\x1f\x1f\x1f\x1f\x1f\x1f\x1f\x1f\x1f\x1f\x1f\x1f\x1f'
	0x1f, 0x1f, 0x1f, 0x1f, 0x1f, 0x1f, 0x1f, 0x1f, 0x1f, 0x1f, 0x1f, 0x1f, 0x1f, 0x1f, 0x1f, 0x1f, //0x00000070 QUAD $0x1f1f1f1f1f1f1f1f; QUAD $0x1f1f1f1f1f1f1f1f  // .space 16, '\x1f\x1f\x1f\x1f\x1f\x1f\x1f\x1f\x1f\x1f\x1f\x1f\x1f\x1f\x1f\x1f'
	//0x00000080 LCPI0_4
	0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, //0x00000080 QUAD $0x2b2b2b2b2b2b2b2b; QUAD $0x2b2b2b2b2b2b2b2b  // .space 16, '++++++++++++++++'
	0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, //0x00000090 QUAD $0x2b2b2b2b2b2b2b2b; QUAD $0x2b2b2b2b2b2b2b2b  // .space 16, '++++++++++++++++'
	//0x000000a0 LCPI0_5
	0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, //0x000000a0 QUAD $0x2d2d2d2d2d2d2d2d; QUAD $0x2d2d2d2d2d2d2d2d  // .space 16, '----------------'
	0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, //0x000000b0 QUAD $0x2d2d2d2d2d2d2d2d; QUAD $0x2d2d2d2d2d2d2d2d  // .space 16, '----------------'
	//0x000000c0 LCPI0_6
	0xdf, 0xdf, 0xdf, 0xdf, 0xdf, 0xdf, 0xdf, 0xdf, 0xdf, 0xdf, 0xdf, 0xdf, 0xdf, 0xdf, 0xdf, 0xdf, //0x000000c0 QUAD $0xdfdfdfdfdfdfdfdf; QUAD $0xdfdfdfdfdfdfdfdf  // .space 16, '\xdf\xdf\xdf\xdf\xdf\xdf\xdf\xdf\xdf\xdf\xdf\xdf\xdf\xdf\xdf\xdf'
	0xdf, 0xdf, 0xdf, 0xdf, 0xdf, 0xdf, 0xdf, 0xdf, 0xdf, 0xdf, 0xdf, 0xdf, 0xdf, 0xdf, 0xdf, 0xdf, //0x000000d0 QUAD $0xdfdfdfdfdfdfdfdf; QUAD $0xdfdfdfdfdfdfdfdf  // .space 16, '\xdf\xdf\xdf\xdf\xdf\xdf\xdf\xdf\xdf\xdf\xdf\xdf\xdf\xdf\xdf\xdf'
	//0x000000e0 LCPI0_7
	0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, //0x000000e0 QUAD $0x2e2e2e2e2e2e2e2e; QUAD $0x2e2e2e2e2e2e2e2e  // .space 16, '................'
	0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, //0x000000f0 QUAD $0x2e2e2e2e2e2e2e2e; QUAD $0x2e2e2e2e2e2e2e2e  // .space 16, '................'
	//0x00000100 LCPI0_8
	0x45, 0x45, 0x45, 0x45, 0x45, 0x45, 0x45, 0x45, 0x45, 0x45, 0x45, 0x45, 0x45, 0x45, 0x45, 0x45, //0x00000100 QUAD $0x4545454545454545; QUAD $0x4545454545454545  // .space 16, 'EEEEEEEEEEEEEEEE'
	0x45, 0x45, 0x45, 0x45, 0x45, 0x45, 0x45, 0x45, 0x45, 0x45, 0x45, 0x45, 0x45, 0x45, 0x45, 0x45, //0x00000110 QUAD $0x4545454545454545; QUAD $0x4545454545454545  // .space 16, 'EEEEEEEEEEEEEEEE'
	//0x00000120 LCPI0_9
	0xd0, 0xd0, 0xd0, 0xd0, 0xd0, 0xd0, 0xd0, 0xd0, 0xd0, 0xd0, 0xd0, 0xd0, 0xd0, 0xd0, 0xd0, 0xd0, //0x00000120 QUAD $0xd0d0d0d0d0d0d0d0; QUAD $0xd0d0d0d0d0d0d0d0  // .space 16, '\xd0\xd0\xd0\xd0\xd0\xd0\xd0\xd0\xd0\xd0\xd0\xd0\xd0\xd0\xd0\xd0'
	0xd0, 0xd0, 0xd0, 0xd0, 0xd0, 0xd0, 0xd0, 0xd0, 0xd0, 0xd0, 0xd0, 0xd0, 0xd0, 0xd0, 0xd0, 0xd0, //0x00000130 QUAD $0xd0d0d0d0d0d0d0d0; QUAD $0xd0d0d0d0d0d0d0d0  // .space 16, '\xd0\xd0\xd0\xd0\xd0\xd0\xd0\xd0\xd0\xd0\xd0\xd0\xd0\xd0\xd0\xd0'
	//0x00000140 LCPI0_10
	0x09, 0x09, 0x09, 0x09, 0x09, 0x09, 0x09, 0x09, 0x09, 0x09, 0x09, 0x09, 0x09, 0x09, 0x09, 0x09, //0x00000140 QUAD $0x0909090909090909; QUAD $0x0909090909090909  // .space 16, '\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t'
	0x09, 0x09, 0x09, 0x09, 0x09, 0x09, 0x09, 0x09, 0x09, 0x09, 0x09, 0x09, 0x09, 0x09, 0x09, 0x09, //0x00000150 QUAD $0x0909090909090909; QUAD $0x0909090909090909  // .space 16, '\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t'
	//0x00000160 LCPI0_22
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00000160 QUAD $0x0000000000000000  // .space 8, '\x00\x00\x00\x00\x00\x00\x00\x00'
	0x01, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00000168 .quad 1
	0x01, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00000170 .quad 1
	0x01, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00000178 .quad 1
	//0x00000180 .p2align 4, 0x00
	//0x00000180 LCPI0_11
	0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, //0x00000180 QUAD $0x2b2b2b2b2b2b2b2b; QUAD $0x2b2b2b2b2b2b2b2b  // .space 16, '++++++++++++++++'
	//0x00000190 LCPI0_12
	0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, //0x00000190 QUAD $0x2d2d2d2d2d2d2d2d; QUAD $0x2d2d2d2d2d2d2d2d  // .space 16, '----------------'
	//0x000001a0 LCPI0_13
	0xd0, 0xd0, 0xd0, 0xd0, 0xd0, 0xd0, 0xd0, 0xd0, 0xd0, 0xd0, 0xd0, 0xd0, 0xd0, 0xd0, 0xd0, 0xd0, //0x000001a0 QUAD $0xd0d0d0d0d0d0d0d0; QUAD $0xd0d0d0d0d0d0d0d0  // .space 16, '\xd0\xd0\xd0\xd0\xd0\xd0\xd0\xd0\xd0\xd0\xd0\xd0\xd0\xd0\xd0\xd0'
	//0x000001b0 LCPI0_14
	0x09, 0x09, 0x09, 0x09, 0x09, 0x09, 0x09, 0x09, 0x09, 0x09, 0x09, 0x09, 0x09, 0x09, 0x09, 0x09, //0x000001b0 QUAD $0x0909090909090909; QUAD $0x0909090909090909  // .space 16, '\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t'
	//0x000001c0 LCPI0_15
	0xdf, 0xdf, 0xdf, 0xdf, 0xdf, 0xdf, 0xdf, 0xdf, 0xdf, 0xdf, 0xdf, 0xdf, 0xdf, 0xdf, 0xdf, 0xdf, //0x000001c0 QUAD $0xdfdfdfdfdfdfdfdf; QUAD $0xdfdfdfdfdfdfdfdf  // .space 16, '\xdf\xdf\xdf\xdf\xdf\xdf\xdf\xdf\xdf\xdf\xdf\xdf\xdf\xdf\xdf\xdf'
	//0x000001d0 LCPI0_16
	0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, //0x000001d0 QUAD $0x2e2e2e2e2e2e2e2e; QUAD $0x2e2e2e2e2e2e2e2e  // .space 16, '................'
	//0x000001e0 LCPI0_17
	0x45, 0x45, 0x45, 0x45, 0x45, 0x45, 0x45, 0x45, 0x45, 0x45, 0x45, 0x45, 0x45, 0x45, 0x45, 0x45, //0x000001e0 QUAD $0x4545454545454545; QUAD $0x4545454545454545  // .space 16, 'EEEEEEEEEEEEEEEE'
	//0x000001f0 LCPI0_18
	0x00, 0x00, 0x30, 0x43, //0x000001f0 .long 1127219200
	0x00, 0x00, 0x30, 0x45, //0x000001f4 .long 1160773632
	0x00, 0x00, 0x00, 0x00, //0x000001f8 .long 0
	0x00, 0x00, 0x00, 0x00, //0x000001fc .long 0
	//0x00000200 LCPI0_19
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x30, 0x43, //0x00000200 .quad 0x4330000000000000
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x30, 0x45, //0x00000208 .quad 0x4530000000000000
	//0x00000210 .p2align 3, 0x00
	//0x00000210 LCPI0_20
	0x00, 0x00, 0x34, 0x26, 0xf5, 0x6b, 0x0c, 0x43, //0x00000210 .quad 0x430c6bf526340000
	//0x00000218 LCPI0_21
	0x00, 0x00, 0x34, 0x26, 0xf5, 0x6b, 0x0c, 0xc3, //0x00000218 .quad 0xc30c6bf526340000
	//0x00000220 LCPI0_23
	0x01, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00000220 .quad 1
	//0x00000228 LCPI0_24
	0x10, 0x27, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00000228 .quad 10000
	//0x00000230 LCPI0_25
	0x0a, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00000230 .quad 10
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x00000238 .p2align 4, 0x90
	//0x00000240 _value
	0x55, //0x00000240 pushq        %rbp
	0x48, 0x89, 0xe5, //0x00000241 movq         %rsp, %rbp
	0x41, 0x57, //0x00000244 pushq        %r15
	0x41, 0x56, //0x00000246 pushq        %r14
	0x41, 0x55, //0x00000248 pushq        %r13
	0x41, 0x54, //0x0000024a pushq        %r12
	0x53, //0x0000024c pushq        %rbx
	0x48, 0x83, 0xec, 0x48, //0x0000024d subq         $72, %rsp
	0x49, 0x89, 0xcf, //0x00000251 movq         %rcx, %r15
	0x49, 0x89, 0xf3, //0x00000254 movq         %rsi, %r11
	0x48, 0x39, 0xf2, //0x00000257 cmpq         %rsi, %rdx
	0x0f, 0x83, 0x27, 0x00, 0x00, 0x00, //0x0000025a jae          LBB0_5
	0x8a, 0x04, 0x17, //0x00000260 movb         (%rdi,%rdx), %al
	0x3c, 0x0d, //0x00000263 cmpb         $13, %al
	0x0f, 0x84, 0x1c, 0x00, 0x00, 0x00, //0x00000265 je           LBB0_5
	0x3c, 0x20, //0x0000026b cmpb         $32, %al
	0x0f, 0x84, 0x14, 0x00, 0x00, 0x00, //0x0000026d je           LBB0_5
	0x8d, 0x48, 0xf5, //0x00000273 leal         $-11(%rax), %ecx
	0x80, 0xf9, 0xfe, //0x00000276 cmpb         $-2, %cl
	0x0f, 0x83, 0x08, 0x00, 0x00, 0x00, //0x00000279 jae          LBB0_5
	0x49, 0x89, 0xd1, //0x0000027f movq         %rdx, %r9
	0xe9, 0x6e, 0x01, 0x00, 0x00, //0x00000282 jmp          LBB0_32
	//0x00000287 LBB0_5
	0x4c, 0x8d, 0x4a, 0x01, //0x00000287 leaq         $1(%rdx), %r9
	0x4d, 0x39, 0xd9, //0x0000028b cmpq         %r11, %r9
	0x0f, 0x83, 0x20, 0x00, 0x00, 0x00, //0x0000028e jae          LBB0_9
	0x42, 0x8a, 0x04, 0x0f, //0x00000294 movb         (%rdi,%r9), %al
	0x3c, 0x0d, //0x00000298 cmpb         $13, %al
	0x0f, 0x84, 0x14, 0x00, 0x00, 0x00, //0x0000029a je           LBB0_9
	0x3c, 0x20, //0x000002a0 cmpb         $32, %al
	0x0f, 0x84, 0x0c, 0x00, 0x00, 0x00, //0x000002a2 je           LBB0_9
	0x8d, 0x48, 0xf5, //0x000002a8 leal         $-11(%rax), %ecx
	0x80, 0xf9, 0xfe, //0x000002ab cmpb         $-2, %cl
	0x0f, 0x82, 0x41, 0x01, 0x00, 0x00, //0x000002ae jb           LBB0_32
	//0x000002b4 LBB0_9
	0x4c, 0x8d, 0x4a, 0x02, //0x000002b4 leaq         $2(%rdx), %r9
	0x4d, 0x39, 0xd9, //0x000002b8 cmpq         %r11, %r9
	0x0f, 0x83, 0x20, 0x00, 0x00, 0x00, //0x000002bb jae          LBB0_13
	0x42, 0x8a, 0x04, 0x0f, //0x000002c1 movb         (%rdi,%r9), %al
	0x3c, 0x0d, //0x000002c5 cmpb         $13, %al
	0x0f, 0x84, 0x14, 0x00, 0x00, 0x00, //0x000002c7 je           LBB0_13
	0x3c, 0x20, //0x000002cd cmpb         $32, %al
	0x0f, 0x84, 0x0c, 0x00, 0x00, 0x00, //0x000002cf je           LBB0_13
	0x8d, 0x48, 0xf5, //0x000002d5 leal         $-11(%rax), %ecx
	0x80, 0xf9, 0xfe, //0x000002d8 cmpb         $-2, %cl
	0x0f, 0x82, 0x14, 0x01, 0x00, 0x00, //0x000002db jb           LBB0_32
	//0x000002e1 LBB0_13
	0x4c, 0x8d, 0x4a, 0x03, //0x000002e1 leaq         $3(%rdx), %r9
	0x4d, 0x39, 0xd9, //0x000002e5 cmpq         %r11, %r9
	0x0f, 0x83, 0x20, 0x00, 0x00, 0x00, //0x000002e8 jae          LBB0_17
	0x42, 0x8a, 0x04, 0x0f, //0x000002ee movb         (%rdi,%r9), %al
	0x3c, 0x0d, //0x000002f2 cmpb         $13, %al
	0x0f, 0x84, 0x14, 0x00, 0x00, 0x00, //0x000002f4 je           LBB0_17
	0x3c, 0x20, //0x000002fa cmpb         $32, %al
	0x0f, 0x84, 0x0c, 0x00, 0x00, 0x00, //0x000002fc je           LBB0_17
	0x8d, 0x48, 0xf5, //0x00000302 leal         $-11(%rax), %ecx
	0x80, 0xf9, 0xfe, //0x00000305 cmpb         $-2, %cl
	0x0f, 0x82, 0xe7, 0x00, 0x00, 0x00, //0x00000308 jb           LBB0_32
	//0x0000030e LBB0_17
	0x4c, 0x8d, 0x4a, 0x04, //0x0000030e leaq         $4(%rdx), %r9
	0x4d, 0x39, 0xd9, //0x00000312 cmpq         %r11, %r9
	0x0f, 0x83, 0xb4, 0x00, 0x00, 0x00, //0x00000315 jae          LBB0_29
	0x4c, 0x89, 0xd8, //0x0000031b movq         %r11, %rax
	0x4c, 0x29, 0xc8, //0x0000031e subq         %r9, %rax
	0x48, 0x83, 0xf8, 0x20, //0x00000321 cmpq         $32, %rax
	0x0f, 0x82, 0x41, 0x15, 0x00, 0x00, //0x00000325 jb           LBB0_325
	0x48, 0xc7, 0xc0, 0xfc, 0xff, 0xff, 0xff, //0x0000032b movq         $-4, %rax
	0x48, 0x29, 0xd0, //0x00000332 subq         %rdx, %rax
	0xc5, 0xfe, 0x6f, 0x05, 0xc3, 0xfc, 0xff, 0xff, //0x00000335 vmovdqu      $-829(%rip), %ymm0  /* LCPI0_0+0(%rip) */
	0x90, 0x90, 0x90, //0x0000033d .p2align 4, 0x90
	//0x00000340 LBB0_20
	0xc4, 0xa1, 0x7e, 0x6f, 0x0c, 0x0f, //0x00000340 vmovdqu      (%rdi,%r9), %ymm1
	0xc4, 0xe2, 0x7d, 0x00, 0xd1, //0x00000346 vpshufb      %ymm1, %ymm0, %ymm2
	0xc5, 0xf5, 0xf8, 0xda, //0x0000034b vpsubb       %ymm2, %ymm1, %ymm3
	0xc4, 0xe2, 0x7d, 0x17, 0xdb, //0x0000034f vptest       %ymm3, %ymm3
	0x0f, 0x85, 0x7d, 0x00, 0x00, 0x00, //0x00000354 jne          LBB0_30
	0x49, 0x83, 0xc1, 0x20, //0x0000035a addq         $32, %r9
	0x49, 0x8d, 0x0c, 0x03, //0x0000035e leaq         (%r11,%rax), %rcx
	0x48, 0x83, 0xc1, 0xe0, //0x00000362 addq         $-32, %rcx
	0x48, 0x83, 0xc0, 0xe0, //0x00000366 addq         $-32, %rax
	0x48, 0x83, 0xf9, 0x1f, //0x0000036a cmpq         $31, %rcx
	0x0f, 0x87, 0xcc, 0xff, 0xff, 0xff, //0x0000036e ja           LBB0_20
	0x49, 0x89, 0xf9, //0x00000374 movq         %rdi, %r9
	0x49, 0x29, 0xc1, //0x00000377 subq         %rax, %r9
	0x4c, 0x01, 0xd8, //0x0000037a addq         %r11, %rax
	0x48, 0x85, 0xc0, //0x0000037d testq        %rax, %rax
	0x0f, 0x84, 0x38, 0x00, 0x00, 0x00, //0x00000380 je           LBB0_28
	//0x00000386 LBB0_23
	0x4d, 0x8d, 0x14, 0x01, //0x00000386 leaq         (%r9,%rax), %r10
	0x31, 0xc9, //0x0000038a xorl         %ecx, %ecx
	0x48, 0xbb, 0x00, 0x26, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, //0x0000038c movabsq      $4294977024, %rbx
	//0x00000396 LBB0_24
	0x41, 0x0f, 0xbe, 0x34, 0x09, //0x00000396 movsbl       (%r9,%rcx), %esi
	0x83, 0xfe, 0x20, //0x0000039b cmpl         $32, %esi
	0x0f, 0x87, 0x46, 0x10, 0x00, 0x00, //0x0000039e ja           LBB0_273
	0x48, 0x0f, 0xa3, 0xf3, //0x000003a4 btq          %rsi, %rbx
	0x0f, 0x83, 0x3c, 0x10, 0x00, 0x00, //0x000003a8 jae          LBB0_273
	0x48, 0x83, 0xc1, 0x01, //0x000003ae addq         $1, %rcx
	0x48, 0x39, 0xc8, //0x000003b2 cmpq         %rcx, %rax
	0x0f, 0x85, 0xdb, 0xff, 0xff, 0xff, //0x000003b5 jne          LBB0_24
	0x4d, 0x89, 0xd1, //0x000003bb movq         %r10, %r9
	//0x000003be LBB0_28
	0x49, 0x29, 0xf9, //0x000003be subq         %rdi, %r9
	0x4d, 0x39, 0xd9, //0x000003c1 cmpq         %r11, %r9
	0x0f, 0x82, 0x27, 0x00, 0x00, 0x00, //0x000003c4 jb           LBB0_31
	0xe9, 0x2a, 0x10, 0x00, 0x00, //0x000003ca jmp          LBB0_274
	//0x000003cf LBB0_29
	0x4c, 0x89, 0xca, //0x000003cf movq         %r9, %rdx
	0xe9, 0x22, 0x10, 0x00, 0x00, //0x000003d2 jmp          LBB0_274
	//0x000003d7 LBB0_30
	0xc5, 0xf5, 0x74, 0xc2, //0x000003d7 vpcmpeqb     %ymm2, %ymm1, %ymm0
	0xc5, 0xfd, 0xd7, 0xc8, //0x000003db vpmovmskb    %ymm0, %ecx
	0xf7, 0xd1, //0x000003df notl         %ecx
	0x44, 0x0f, 0xbc, 0xc9, //0x000003e1 bsfl         %ecx, %r9d
	0x49, 0x29, 0xc1, //0x000003e5 subq         %rax, %r9
	0x4d, 0x39, 0xd9, //0x000003e8 cmpq         %r11, %r9
	0x0f, 0x83, 0x08, 0x10, 0x00, 0x00, //0x000003eb jae          LBB0_274
	//0x000003f1 LBB0_31
	0x42, 0x8a, 0x04, 0x0f, //0x000003f1 movb         (%rdi,%r9), %al
	//0x000003f5 LBB0_32
	0x0f, 0xbe, 0xc8, //0x000003f5 movsbl       %al, %ecx
	0x83, 0xf9, 0x7d, //0x000003f8 cmpl         $125, %ecx
	0x0f, 0x87, 0x2f, 0x07, 0x00, 0x00, //0x000003fb ja           LBB0_132
	0x49, 0x8d, 0x51, 0x01, //0x00000401 leaq         $1(%r9), %rdx
	0x4e, 0x8d, 0x2c, 0x0f, //0x00000405 leaq         (%rdi,%r9), %r13
	0x48, 0x8d, 0x35, 0x58, 0x30, 0x00, 0x00, //0x00000409 leaq         $12376(%rip), %rsi  /* LJTI0_0+0(%rip) */
	0x48, 0x63, 0x0c, 0x8e, //0x00000410 movslq       (%rsi,%rcx,4), %rcx
	0x48, 0x01, 0xf1, //0x00000414 addq         %rsi, %rcx
	0xff, 0xe1, //0x00000417 jmpq         *%rcx
	//0x00000419 LBB0_34
	0x41, 0xf6, 0xc0, 0x02, //0x00000419 testb        $2, %r8b
	0x0f, 0x85, 0x7d, 0x00, 0x00, 0x00, //0x0000041d jne          LBB0_41
	0x4c, 0x89, 0x6d, 0xc0, //0x00000423 movq         %r13, $-64(%rbp)
	0x48, 0x89, 0x7d, 0xc8, //0x00000427 movq         %rdi, $-56(%rbp)
	0x4d, 0x8b, 0x47, 0x20, //0x0000042b movq         $32(%r15), %r8
	0x4d, 0x8b, 0x6f, 0x28, //0x0000042f movq         $40(%r15), %r13
	0x49, 0xc7, 0x07, 0x09, 0x00, 0x00, 0x00, //0x00000433 movq         $9, (%r15)
	0xc5, 0xf9, 0xef, 0xc0, //0x0000043a vpxor        %xmm0, %xmm0, %xmm0
	0xc4, 0xc1, 0x7a, 0x7f, 0x47, 0x08, //0x0000043e vmovdqu      %xmm0, $8(%r15)
	0x4d, 0x89, 0x4f, 0x18, //0x00000444 movq         %r9, $24(%r15)
	0x4d, 0x39, 0xd9, //0x00000448 cmpq         %r11, %r9
	0x0f, 0x83, 0x00, 0x0d, 0x00, 0x00, //0x0000044b jae          LBB0_238
	0x48, 0x8b, 0x45, 0xc0, //0x00000451 movq         $-64(%rbp), %rax
	0x40, 0x8a, 0x30, //0x00000455 movb         (%rax), %sil
	0x41, 0xbe, 0x01, 0x00, 0x00, 0x00, //0x00000458 movl         $1, %r14d
	0x89, 0xf3, //0x0000045e movl         %esi, %ebx
	0x4c, 0x89, 0xc9, //0x00000460 movq         %r9, %rcx
	0x40, 0x80, 0xfe, 0x2d, //0x00000463 cmpb         $45, %sil
	0x0f, 0x85, 0x19, 0x00, 0x00, 0x00, //0x00000467 jne          LBB0_39
	0x4c, 0x39, 0xda, //0x0000046d cmpq         %r11, %rdx
	0x0f, 0x83, 0xdb, 0x0c, 0x00, 0x00, //0x00000470 jae          LBB0_238
	0x48, 0x8b, 0x45, 0xc8, //0x00000476 movq         $-56(%rbp), %rax
	0x8a, 0x1c, 0x10, //0x0000047a movb         (%rax,%rdx), %bl
	0x41, 0xbe, 0xff, 0xff, 0xff, 0xff, //0x0000047d movl         $-1, %r14d
	0x48, 0x89, 0xd1, //0x00000483 movq         %rdx, %rcx
	//0x00000486 LBB0_39
	0x8d, 0x43, 0xc6, //0x00000486 leal         $-58(%rbx), %eax
	0x3c, 0xf5, //0x00000489 cmpb         $-11, %al
	0x0f, 0x87, 0x13, 0x04, 0x00, 0x00, //0x0000048b ja           LBB0_98
	//0x00000491 LBB0_40
	0x49, 0xc7, 0x07, 0xfe, 0xff, 0xff, 0xff, //0x00000491 movq         $-2, (%r15)
	0x49, 0x89, 0xca, //0x00000498 movq         %rcx, %r10
	0xe9, 0x63, 0x0f, 0x00, 0x00, //0x0000049b jmp          LBB0_276
	//0x000004a0 LBB0_41
	0x4d, 0x29, 0xcb, //0x000004a0 subq         %r9, %r11
	0x31, 0xf6, //0x000004a3 xorl         %esi, %esi
	0x3c, 0x2d, //0x000004a5 cmpb         $45, %al
	0x40, 0x0f, 0x94, 0xc6, //0x000004a7 sete         %sil
	0x4e, 0x8d, 0x14, 0x2e, //0x000004ab leaq         (%rsi,%r13), %r10
	0x49, 0x29, 0xf3, //0x000004af subq         %rsi, %r11
	0x0f, 0x84, 0x23, 0x25, 0x00, 0x00, //0x000004b2 je           LBB0_599
	0x4c, 0x89, 0x7d, 0xb8, //0x000004b8 movq         %r15, $-72(%rbp)
	0x41, 0x8a, 0x02, //0x000004bc movb         (%r10), %al
	0x8d, 0x48, 0xc6, //0x000004bf leal         $-58(%rax), %ecx
	0x80, 0xf9, 0xf6, //0x000004c2 cmpb         $-10, %cl
	0x0f, 0x82, 0x03, 0x08, 0x00, 0x00, //0x000004c5 jb           LBB0_162
	0x3c, 0x30, //0x000004cb cmpb         $48, %al
	0x0f, 0x85, 0x36, 0x00, 0x00, 0x00, //0x000004cd jne          LBB0_47
	0xb8, 0x01, 0x00, 0x00, 0x00, //0x000004d3 movl         $1, %eax
	0x49, 0x83, 0xfb, 0x01, //0x000004d8 cmpq         $1, %r11
	0x0f, 0x84, 0xa9, 0x07, 0x00, 0x00, //0x000004dc je           LBB0_158
	0x41, 0x8a, 0x4a, 0x01, //0x000004e2 movb         $1(%r10), %cl
	0x80, 0xc1, 0xd2, //0x000004e6 addb         $-46, %cl
	0x80, 0xf9, 0x37, //0x000004e9 cmpb         $55, %cl
	0x0f, 0x87, 0x99, 0x07, 0x00, 0x00, //0x000004ec ja           LBB0_158
	0x0f, 0xb6, 0xc9, //0x000004f2 movzbl       %cl, %ecx
	0x48, 0xba, 0x01, 0x00, 0x80, 0x00, 0x00, 0x00, 0x80, 0x00, //0x000004f5 movabsq      $36028797027352577, %rdx
	0x48, 0x0f, 0xa3, 0xca, //0x000004ff btq          %rcx, %rdx
	0x0f, 0x83, 0x82, 0x07, 0x00, 0x00, //0x00000503 jae          LBB0_158
	//0x00000509 LBB0_47
	0x49, 0x83, 0xfb, 0x20, //0x00000509 cmpq         $32, %r11
	0x48, 0x89, 0x75, 0xb0, //0x0000050d movq         %rsi, $-80(%rbp)
	0x49, 0xc7, 0xc4, 0xff, 0xff, 0xff, 0xff, //0x00000511 movq         $-1, %r12
	0x0f, 0x82, 0xe6, 0x24, 0x00, 0x00, //0x00000518 jb           LBB0_602
	0x31, 0xc0, //0x0000051e xorl         %eax, %eax
	0xc5, 0xfe, 0x6f, 0x05, 0x58, 0xfb, 0xff, 0xff, //0x00000520 vmovdqu      $-1192(%rip), %ymm0  /* LCPI0_4+0(%rip) */
	0xc5, 0xfe, 0x6f, 0x0d, 0x70, 0xfb, 0xff, 0xff, //0x00000528 vmovdqu      $-1168(%rip), %ymm1  /* LCPI0_5+0(%rip) */
	0xc5, 0xfe, 0x6f, 0x15, 0x88, 0xfb, 0xff, 0xff, //0x00000530 vmovdqu      $-1144(%rip), %ymm2  /* LCPI0_6+0(%rip) */
	0xc5, 0xfe, 0x6f, 0x1d, 0xa0, 0xfb, 0xff, 0xff, //0x00000538 vmovdqu      $-1120(%rip), %ymm3  /* LCPI0_7+0(%rip) */
	0xc5, 0xfe, 0x6f, 0x25, 0xb8, 0xfb, 0xff, 0xff, //0x00000540 vmovdqu      $-1096(%rip), %ymm4  /* LCPI0_8+0(%rip) */
	0xc5, 0xfe, 0x6f, 0x2d, 0xd0, 0xfb, 0xff, 0xff, //0x00000548 vmovdqu      $-1072(%rip), %ymm5  /* LCPI0_9+0(%rip) */
	0xc5, 0xfe, 0x6f, 0x35, 0xe8, 0xfb, 0xff, 0xff, //0x00000550 vmovdqu      $-1048(%rip), %ymm6  /* LCPI0_10+0(%rip) */
	0x49, 0xc7, 0xc7, 0xff, 0xff, 0xff, 0xff, //0x00000558 movq         $-1, %r15
	0x48, 0xc7, 0x45, 0xc8, 0xff, 0xff, 0xff, 0xff, //0x0000055f movq         $-1, $-56(%rbp)
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x00000567 .p2align 4, 0x90
	//0x00000570 LBB0_49
	0xc4, 0xc1, 0x7e, 0x6f, 0x3c, 0x02, //0x00000570 vmovdqu      (%r10,%rax), %ymm7
	0xc5, 0x45, 0x74, 0xc0, //0x00000576 vpcmpeqb     %ymm0, %ymm7, %ymm8
	0xc5, 0x45, 0x74, 0xc9, //0x0000057a vpcmpeqb     %ymm1, %ymm7, %ymm9
	0xc4, 0x41, 0x35, 0xeb, 0xc0, //0x0000057e vpor         %ymm8, %ymm9, %ymm8
	0xc5, 0x45, 0xdb, 0xca, //0x00000583 vpand        %ymm2, %ymm7, %ymm9
	0xc5, 0x35, 0x74, 0xcc, //0x00000587 vpcmpeqb     %ymm4, %ymm9, %ymm9
	0xc5, 0x45, 0x74, 0xd3, //0x0000058b vpcmpeqb     %ymm3, %ymm7, %ymm10
	0xc4, 0xc1, 0x7d, 0xd7, 0xf2, //0x0000058f vpmovmskb    %ymm10, %esi
	0xc4, 0xc1, 0x7d, 0xd7, 0xd1, //0x00000594 vpmovmskb    %ymm9, %edx
	0xc4, 0x41, 0x7d, 0xd7, 0xf0, //0x00000599 vpmovmskb    %ymm8, %r14d
	0xc5, 0xc5, 0xfc, 0xfd, //0x0000059e vpaddb       %ymm5, %ymm7, %ymm7
	0xc5, 0x45, 0xda, 0xde, //0x000005a2 vpminub      %ymm6, %ymm7, %ymm11
	0xc5, 0xa5, 0x74, 0xff, //0x000005a6 vpcmpeqb     %ymm7, %ymm11, %ymm7
	0xc4, 0x41, 0x2d, 0xeb, 0xc9, //0x000005aa vpor         %ymm9, %ymm10, %ymm9
	0xc5, 0xb5, 0xeb, 0xff, //0x000005af vpor         %ymm7, %ymm9, %ymm7
	0xc5, 0xbd, 0xeb, 0xff, //0x000005b3 vpor         %ymm7, %ymm8, %ymm7
	0xc5, 0xfd, 0xd7, 0xcf, //0x000005b7 vpmovmskb    %ymm7, %ecx
	0x48, 0xf7, 0xd1, //0x000005bb notq         %rcx
	0x4c, 0x0f, 0xbc, 0xc1, //0x000005be bsfq         %rcx, %r8
	0x41, 0x83, 0xf8, 0x20, //0x000005c2 cmpl         $32, %r8d
	0x0f, 0x84, 0x16, 0x00, 0x00, 0x00, //0x000005c6 je           LBB0_51
	0xbb, 0xff, 0xff, 0xff, 0xff, //0x000005cc movl         $-1, %ebx
	0x44, 0x89, 0xc1, //0x000005d1 movl         %r8d, %ecx
	0xd3, 0xe3, //0x000005d4 shll         %cl, %ebx
	0xf7, 0xd3, //0x000005d6 notl         %ebx
	0x21, 0xde, //0x000005d8 andl         %ebx, %esi
	0x21, 0xda, //0x000005da andl         %ebx, %edx
	0x44, 0x21, 0xf3, //0x000005dc andl         %r14d, %ebx
	0x41, 0x89, 0xde, //0x000005df movl         %ebx, %r14d
	//0x000005e2 LBB0_51
	0x8d, 0x4e, 0xff, //0x000005e2 leal         $-1(%rsi), %ecx
	0x21, 0xf1, //0x000005e5 andl         %esi, %ecx
	0x0f, 0x85, 0xe7, 0x09, 0x00, 0x00, //0x000005e7 jne          LBB0_209
	0x8d, 0x4a, 0xff, //0x000005ed leal         $-1(%rdx), %ecx
	0x21, 0xd1, //0x000005f0 andl         %edx, %ecx
	0x0f, 0x85, 0xdc, 0x09, 0x00, 0x00, //0x000005f2 jne          LBB0_209
	0x41, 0x8d, 0x4e, 0xff, //0x000005f8 leal         $-1(%r14), %ecx
	0x44, 0x21, 0xf1, //0x000005fc andl         %r14d, %ecx
	0x0f, 0x85, 0xcf, 0x09, 0x00, 0x00, //0x000005ff jne          LBB0_209
	0x85, 0xf6, //0x00000605 testl        %esi, %esi
	0x0f, 0x84, 0x15, 0x00, 0x00, 0x00, //0x00000607 je           LBB0_57
	0x0f, 0xbc, 0xce, //0x0000060d bsfl         %esi, %ecx
	0x48, 0x83, 0x7d, 0xc8, 0xff, //0x00000610 cmpq         $-1, $-56(%rbp)
	0x0f, 0x85, 0x61, 0x0d, 0x00, 0x00, //0x00000615 jne          LBB0_268
	0x48, 0x01, 0xc1, //0x0000061b addq         %rax, %rcx
	0x48, 0x89, 0x4d, 0xc8, //0x0000061e movq         %rcx, $-56(%rbp)
	//0x00000622 LBB0_57
	0x85, 0xd2, //0x00000622 testl        %edx, %edx
	0x0f, 0x84, 0x13, 0x00, 0x00, 0x00, //0x00000624 je           LBB0_60
	0x0f, 0xbc, 0xca, //0x0000062a bsfl         %edx, %ecx
	0x49, 0x83, 0xff, 0xff, //0x0000062d cmpq         $-1, %r15
	0x0f, 0x85, 0x45, 0x0d, 0x00, 0x00, //0x00000631 jne          LBB0_268
	0x48, 0x01, 0xc1, //0x00000637 addq         %rax, %rcx
	0x49, 0x89, 0xcf, //0x0000063a movq         %rcx, %r15
	//0x0000063d LBB0_60
	0x45, 0x85, 0xf6, //0x0000063d testl        %r14d, %r14d
	0x0f, 0x84, 0x14, 0x00, 0x00, 0x00, //0x00000640 je           LBB0_63
	0x41, 0x0f, 0xbc, 0xce, //0x00000646 bsfl         %r14d, %ecx
	0x49, 0x83, 0xfc, 0xff, //0x0000064a cmpq         $-1, %r12
	0x0f, 0x85, 0x28, 0x0d, 0x00, 0x00, //0x0000064e jne          LBB0_268
	0x48, 0x01, 0xc1, //0x00000654 addq         %rax, %rcx
	0x49, 0x89, 0xcc, //0x00000657 movq         %rcx, %r12
	//0x0000065a LBB0_63
	0x41, 0x83, 0xf8, 0x20, //0x0000065a cmpl         $32, %r8d
	0x0f, 0x85, 0x2e, 0x02, 0x00, 0x00, //0x0000065e jne          LBB0_97
	0x49, 0x83, 0xc3, 0xe0, //0x00000664 addq         $-32, %r11
	0x48, 0x83, 0xc0, 0x20, //0x00000668 addq         $32, %rax
	0x49, 0x83, 0xfb, 0x1f, //0x0000066c cmpq         $31, %r11
	0x0f, 0x87, 0xfa, 0xfe, 0xff, 0xff, //0x00000670 ja           LBB0_49
	0xc5, 0xf8, 0x77, //0x00000676 vzeroupper   
	0x4c, 0x01, 0xd0, //0x00000679 addq         %r10, %rax
	0x49, 0x89, 0xc0, //0x0000067c movq         %rax, %r8
	0x4c, 0x89, 0x6d, 0xc0, //0x0000067f movq         %r13, $-64(%rbp)
	0x49, 0x83, 0xfb, 0x10, //0x00000683 cmpq         $16, %r11
	0x4c, 0x89, 0x55, 0xa8, //0x00000687 movq         %r10, $-88(%rbp)
	0x0f, 0x82, 0x5a, 0x01, 0x00, 0x00, //0x0000068b jb           LBB0_84
	//0x00000691 LBB0_66
	0x4d, 0x89, 0xc5, //0x00000691 movq         %r8, %r13
	0x4d, 0x29, 0xd5, //0x00000694 subq         %r10, %r13
	0x31, 0xc0, //0x00000697 xorl         %eax, %eax
	0xc5, 0x7a, 0x6f, 0x05, 0xdf, 0xfa, 0xff, 0xff, //0x00000699 vmovdqu      $-1313(%rip), %xmm8  /* LCPI0_11+0(%rip) */
	0xc5, 0x7a, 0x6f, 0x0d, 0xe7, 0xfa, 0xff, 0xff, //0x000006a1 vmovdqu      $-1305(%rip), %xmm9  /* LCPI0_12+0(%rip) */
	0xc5, 0x7a, 0x6f, 0x15, 0xef, 0xfa, 0xff, 0xff, //0x000006a9 vmovdqu      $-1297(%rip), %xmm10  /* LCPI0_13+0(%rip) */
	0xc5, 0x7a, 0x6f, 0x1d, 0xf7, 0xfa, 0xff, 0xff, //0x000006b1 vmovdqu      $-1289(%rip), %xmm11  /* LCPI0_14+0(%rip) */
	0xc5, 0xfa, 0x6f, 0x25, 0xff, 0xfa, 0xff, 0xff, //0x000006b9 vmovdqu      $-1281(%rip), %xmm4  /* LCPI0_15+0(%rip) */
	0xc5, 0xfa, 0x6f, 0x2d, 0x07, 0xfb, 0xff, 0xff, //0x000006c1 vmovdqu      $-1273(%rip), %xmm5  /* LCPI0_16+0(%rip) */
	0xc5, 0xfa, 0x6f, 0x35, 0x0f, 0xfb, 0xff, 0xff, //0x000006c9 vmovdqu      $-1265(%rip), %xmm6  /* LCPI0_17+0(%rip) */
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x000006d1 .p2align 4, 0x90
	//0x000006e0 LBB0_67
	0xc4, 0xc1, 0x7a, 0x6f, 0x3c, 0x00, //0x000006e0 vmovdqu      (%r8,%rax), %xmm7
	0xc5, 0xb9, 0x74, 0xc7, //0x000006e6 vpcmpeqb     %xmm7, %xmm8, %xmm0
	0xc5, 0xb1, 0x74, 0xcf, //0x000006ea vpcmpeqb     %xmm7, %xmm9, %xmm1
	0xc5, 0xf1, 0xeb, 0xc0, //0x000006ee vpor         %xmm0, %xmm1, %xmm0
	0xc5, 0xa9, 0xfc, 0xcf, //0x000006f2 vpaddb       %xmm7, %xmm10, %xmm1
	0xc5, 0xa1, 0xda, 0xd1, //0x000006f6 vpminub      %xmm1, %xmm11, %xmm2
	0xc5, 0xf1, 0x74, 0xca, //0x000006fa vpcmpeqb     %xmm2, %xmm1, %xmm1
	0xc5, 0xc1, 0xdb, 0xd4, //0x000006fe vpand        %xmm4, %xmm7, %xmm2
	0xc5, 0xe9, 0x74, 0xd6, //0x00000702 vpcmpeqb     %xmm6, %xmm2, %xmm2
	0xc5, 0xc1, 0x74, 0xfd, //0x00000706 vpcmpeqb     %xmm5, %xmm7, %xmm7
	0xc5, 0xe9, 0xeb, 0xdf, //0x0000070a vpor         %xmm7, %xmm2, %xmm3
	0xc5, 0xe1, 0xeb, 0xd8, //0x0000070e vpor         %xmm0, %xmm3, %xmm3
	0xc5, 0xe1, 0xeb, 0xc9, //0x00000712 vpor         %xmm1, %xmm3, %xmm1
	0xc5, 0xf9, 0xd7, 0xf7, //0x00000716 vpmovmskb    %xmm7, %esi
	0xc5, 0x79, 0xd7, 0xf2, //0x0000071a vpmovmskb    %xmm2, %r14d
	0xc5, 0xf9, 0xd7, 0xd8, //0x0000071e vpmovmskb    %xmm0, %ebx
	0xc5, 0xf9, 0xd7, 0xc9, //0x00000722 vpmovmskb    %xmm1, %ecx
	0xf7, 0xd1, //0x00000726 notl         %ecx
	0x0f, 0xbc, 0xc9, //0x00000728 bsfl         %ecx, %ecx
	0x83, 0xf9, 0x10, //0x0000072b cmpl         $16, %ecx
	0x0f, 0x84, 0x12, 0x00, 0x00, 0x00, //0x0000072e je           LBB0_69
	0xba, 0xff, 0xff, 0xff, 0xff, //0x00000734 movl         $-1, %edx
	0xd3, 0xe2, //0x00000739 shll         %cl, %edx
	0xf7, 0xd2, //0x0000073b notl         %edx
	0x21, 0xd6, //0x0000073d andl         %edx, %esi
	0x41, 0x21, 0xd6, //0x0000073f andl         %edx, %r14d
	0x21, 0xda, //0x00000742 andl         %ebx, %edx
	0x89, 0xd3, //0x00000744 movl         %edx, %ebx
	//0x00000746 LBB0_69
	0x44, 0x8d, 0x56, 0xff, //0x00000746 leal         $-1(%rsi), %r10d
	0x41, 0x21, 0xf2, //0x0000074a andl         %esi, %r10d
	0x0f, 0x85, 0x37, 0x0d, 0x00, 0x00, //0x0000074d jne          LBB0_285
	0x41, 0x8d, 0x56, 0xff, //0x00000753 leal         $-1(%r14), %edx
	0x44, 0x21, 0xf2, //0x00000757 andl         %r14d, %edx
	0x0f, 0x85, 0x33, 0x0d, 0x00, 0x00, //0x0000075a jne          LBB0_286
	0x8d, 0x53, 0xff, //0x00000760 leal         $-1(%rbx), %edx
	0x21, 0xda, //0x00000763 andl         %ebx, %edx
	0x4c, 0x8b, 0x55, 0xa8, //0x00000765 movq         $-88(%rbp), %r10
	0x0f, 0x85, 0x44, 0x0d, 0x00, 0x00, //0x00000769 jne          LBB0_288
	0x85, 0xf6, //0x0000076f testl        %esi, %esi
	0x0f, 0x84, 0x18, 0x00, 0x00, 0x00, //0x00000771 je           LBB0_75
	0x0f, 0xbc, 0xf6, //0x00000777 bsfl         %esi, %esi
	0x48, 0x83, 0x7d, 0xc8, 0xff, //0x0000077a cmpq         $-1, $-56(%rbp)
	0x0f, 0x85, 0x9c, 0x0c, 0x00, 0x00, //0x0000077f jne          LBB0_278
	0x4c, 0x01, 0xee, //0x00000785 addq         %r13, %rsi
	0x48, 0x01, 0xc6, //0x00000788 addq         %rax, %rsi
	0x48, 0x89, 0x75, 0xc8, //0x0000078b movq         %rsi, $-56(%rbp)
	//0x0000078f LBB0_75
	0x45, 0x85, 0xf6, //0x0000078f testl        %r14d, %r14d
	0x0f, 0x84, 0x17, 0x00, 0x00, 0x00, //0x00000792 je           LBB0_78
	0x41, 0x0f, 0xbc, 0xf6, //0x00000798 bsfl         %r14d, %esi
	0x49, 0x83, 0xff, 0xff, //0x0000079c cmpq         $-1, %r15
	0x0f, 0x85, 0x7b, 0x0c, 0x00, 0x00, //0x000007a0 jne          LBB0_278
	0x4c, 0x01, 0xee, //0x000007a6 addq         %r13, %rsi
	0x48, 0x01, 0xc6, //0x000007a9 addq         %rax, %rsi
	0x49, 0x89, 0xf7, //0x000007ac movq         %rsi, %r15
	//0x000007af LBB0_78
	0x85, 0xdb, //0x000007af testl        %ebx, %ebx
	0x0f, 0x84, 0x16, 0x00, 0x00, 0x00, //0x000007b1 je           LBB0_81
	0x0f, 0xbc, 0xf3, //0x000007b7 bsfl         %ebx, %esi
	0x49, 0x83, 0xfc, 0xff, //0x000007ba cmpq         $-1, %r12
	0x0f, 0x85, 0x5d, 0x0c, 0x00, 0x00, //0x000007be jne          LBB0_278
	0x4c, 0x01, 0xee, //0x000007c4 addq         %r13, %rsi
	0x48, 0x01, 0xc6, //0x000007c7 addq         %rax, %rsi
	0x49, 0x89, 0xf4, //0x000007ca movq         %rsi, %r12
	//0x000007cd LBB0_81
	0x83, 0xf9, 0x10, //0x000007cd cmpl         $16, %ecx
	0x0f, 0x85, 0x28, 0x04, 0x00, 0x00, //0x000007d0 jne          LBB0_144
	0x49, 0x83, 0xc3, 0xf0, //0x000007d6 addq         $-16, %r11
	0x48, 0x83, 0xc0, 0x10, //0x000007da addq         $16, %rax
	0x49, 0x83, 0xfb, 0x0f, //0x000007de cmpq         $15, %r11
	0x0f, 0x87, 0xf8, 0xfe, 0xff, 0xff, //0x000007e2 ja           LBB0_67
	0x49, 0x01, 0xc0, //0x000007e8 addq         %rax, %r8
	//0x000007eb LBB0_84
	0x4d, 0x85, 0xdb, //0x000007eb testq        %r11, %r11
	0x4c, 0x8b, 0x75, 0xc0, //0x000007ee movq         $-64(%rbp), %r14
	0x4c, 0x8b, 0x6d, 0xc8, //0x000007f2 movq         $-56(%rbp), %r13
	0x0f, 0x84, 0x16, 0x04, 0x00, 0x00, //0x000007f6 je           LBB0_146
	0x4f, 0x8d, 0x14, 0x18, //0x000007fc leaq         (%r8,%r11), %r10
	0x4c, 0x89, 0xc2, //0x00000800 movq         %r8, %rdx
	0x48, 0x2b, 0x55, 0xa8, //0x00000803 subq         $-88(%rbp), %rdx
	0x31, 0xc0, //0x00000807 xorl         %eax, %eax
	0x48, 0x8d, 0x1d, 0x50, 0x2e, 0x00, 0x00, //0x00000809 leaq         $11856(%rip), %rbx  /* LJTI0_1+0(%rip) */
	0xe9, 0x28, 0x00, 0x00, 0x00, //0x00000810 jmp          LBB0_89
	//0x00000815 LBB0_86
	0x49, 0x83, 0xfc, 0xff, //0x00000815 cmpq         $-1, %r12
	0x0f, 0x85, 0xb5, 0x0b, 0x00, 0x00, //0x00000819 jne          LBB0_272
	0x4c, 0x8d, 0x24, 0x02, //0x0000081f leaq         (%rdx,%rax), %r12
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x00000823 .p2align 4, 0x90
	//0x00000830 LBB0_88
	0x48, 0x83, 0xc0, 0x01, //0x00000830 addq         $1, %rax
	0x49, 0x39, 0xc3, //0x00000834 cmpq         %rax, %r11
	0x0f, 0x84, 0x8f, 0x07, 0x00, 0x00, //0x00000837 je           LBB0_208
	//0x0000083d LBB0_89
	0x41, 0x0f, 0xbe, 0x34, 0x00, //0x0000083d movsbl       (%r8,%rax), %esi
	0x8d, 0x4e, 0xd0, //0x00000842 leal         $-48(%rsi), %ecx
	0x83, 0xf9, 0x0a, //0x00000845 cmpl         $10, %ecx
	0x0f, 0x82, 0xe2, 0xff, 0xff, 0xff, //0x00000848 jb           LBB0_88
	0x8d, 0x4e, 0xd5, //0x0000084e leal         $-43(%rsi), %ecx
	0x83, 0xf9, 0x1a, //0x00000851 cmpl         $26, %ecx
	0x0f, 0x87, 0x1c, 0x00, 0x00, 0x00, //0x00000854 ja           LBB0_94
	0x48, 0x63, 0x0c, 0x8b, //0x0000085a movslq       (%rbx,%rcx,4), %rcx
	0x48, 0x01, 0xd9, //0x0000085e addq         %rbx, %rcx
	0xff, 0xe1, //0x00000861 jmpq         *%rcx
	//0x00000863 LBB0_92
	0x49, 0x83, 0xfd, 0xff, //0x00000863 cmpq         $-1, %r13
	0x0f, 0x85, 0x67, 0x0b, 0x00, 0x00, //0x00000867 jne          LBB0_272
	0x4c, 0x8d, 0x2c, 0x02, //0x0000086d leaq         (%rdx,%rax), %r13
	0xe9, 0xba, 0xff, 0xff, 0xff, //0x00000871 jmp          LBB0_88
	//0x00000876 LBB0_94
	0x83, 0xfe, 0x65, //0x00000876 cmpl         $101, %esi
	0x0f, 0x85, 0x90, 0x03, 0x00, 0x00, //0x00000879 jne          LBB0_145
	//0x0000087f LBB0_95
	0x49, 0x83, 0xff, 0xff, //0x0000087f cmpq         $-1, %r15
	0x0f, 0x85, 0x4b, 0x0b, 0x00, 0x00, //0x00000883 jne          LBB0_272
	0x4c, 0x8d, 0x3c, 0x02, //0x00000889 leaq         (%rdx,%rax), %r15
	0xe9, 0x9e, 0xff, 0xff, 0xff, //0x0000088d jmp          LBB0_88
	//0x00000892 LBB0_97
	0x49, 0x01, 0xc0, //0x00000892 addq         %rax, %r8
	0x4d, 0x01, 0xd0, //0x00000895 addq         %r10, %r8
	0xc5, 0xf8, 0x77, //0x00000898 vzeroupper   
	0x4c, 0x8b, 0x6d, 0xc8, //0x0000089b movq         $-56(%rbp), %r13
	0xe9, 0x72, 0x03, 0x00, 0x00, //0x0000089f jmp          LBB0_147
	//0x000008a4 LBB0_98
	0x40, 0x88, 0x75, 0xb0, //0x000008a4 movb         %sil, $-80(%rbp)
	0x80, 0xfb, 0x30, //0x000008a8 cmpb         $48, %bl
	0x0f, 0x85, 0x36, 0x00, 0x00, 0x00, //0x000008ab jne          LBB0_102
	0x4c, 0x8d, 0x51, 0x01, //0x000008b1 leaq         $1(%rcx), %r10
	0x4c, 0x39, 0xd9, //0x000008b5 cmpq         %r11, %rcx
	0x0f, 0x83, 0x45, 0x0b, 0x00, 0x00, //0x000008b8 jae          LBB0_276
	0x48, 0x8b, 0x45, 0xc8, //0x000008be movq         $-56(%rbp), %rax
	0x42, 0x8a, 0x04, 0x10, //0x000008c2 movb         (%rax,%r10), %al
	0x04, 0xd2, //0x000008c6 addb         $-46, %al
	0x3c, 0x37, //0x000008c8 cmpb         $55, %al
	0x0f, 0x87, 0x33, 0x0b, 0x00, 0x00, //0x000008ca ja           LBB0_276
	0x0f, 0xb6, 0xc0, //0x000008d0 movzbl       %al, %eax
	0x48, 0xba, 0x01, 0x00, 0x80, 0x00, 0x00, 0x00, 0x80, 0x00, //0x000008d3 movabsq      $36028797027352577, %rdx
	0x48, 0x0f, 0xa3, 0xc2, //0x000008dd btq          %rax, %rdx
	0x0f, 0x83, 0x1c, 0x0b, 0x00, 0x00, //0x000008e1 jae          LBB0_276
	//0x000008e7 LBB0_102
	0x41, 0xb4, 0x01, //0x000008e7 movb         $1, %r12b
	0x4c, 0x39, 0xd9, //0x000008ea cmpq         %r11, %rcx
	0x0f, 0x83, 0x81, 0x05, 0x00, 0x00, //0x000008ed jae          LBB0_180
	0xbe, 0xd0, 0xff, 0xff, 0xff, //0x000008f3 movl         $4294967248, %esi
	0x48, 0x83, 0xc1, 0x01, //0x000008f8 addq         $1, %rcx
	0x31, 0xd2, //0x000008fc xorl         %edx, %edx
	0x31, 0xc0, //0x000008fe xorl         %eax, %eax
	0x45, 0x31, 0xd2, //0x00000900 xorl         %r10d, %r10d
	//0x00000903 LBB0_104
	0x83, 0xf8, 0x12, //0x00000903 cmpl         $18, %eax
	0x0f, 0x8f, 0x15, 0x00, 0x00, 0x00, //0x00000906 jg           LBB0_106
	0x4b, 0x8d, 0x3c, 0x92, //0x0000090c leaq         (%r10,%r10,4), %rdi
	0x0f, 0xb6, 0xdb, //0x00000910 movzbl       %bl, %ebx
	0x01, 0xf3, //0x00000913 addl         %esi, %ebx
	0x4c, 0x8d, 0x14, 0x7b, //0x00000915 leaq         (%rbx,%rdi,2), %r10
	0x83, 0xc0, 0x01, //0x00000919 addl         $1, %eax
	0xe9, 0x03, 0x00, 0x00, 0x00, //0x0000091c jmp          LBB0_107
	//0x00000921 LBB0_106
	0x83, 0xc2, 0x01, //0x00000921 addl         $1, %edx
	//0x00000924 LBB0_107
	0x49, 0x39, 0xcb, //0x00000924 cmpq         %rcx, %r11
	0x0f, 0x84, 0x74, 0x06, 0x00, 0x00, //0x00000927 je           LBB0_200
	0x48, 0x8b, 0x7d, 0xc8, //0x0000092d movq         $-56(%rbp), %rdi
	0x0f, 0xb6, 0x1c, 0x0f, //0x00000931 movzbl       (%rdi,%rcx), %ebx
	0x8d, 0x7b, 0xd0, //0x00000935 leal         $-48(%rbx), %edi
	0x48, 0x83, 0xc1, 0x01, //0x00000938 addq         $1, %rcx
	0x40, 0x80, 0xff, 0x0a, //0x0000093c cmpb         $10, %dil
	0x0f, 0x82, 0xbd, 0xff, 0xff, 0xff, //0x00000940 jb           LBB0_104
	0x80, 0xfb, 0x2e, //0x00000946 cmpb         $46, %bl
	0x0f, 0x85, 0x8d, 0x06, 0x00, 0x00, //0x00000949 jne          LBB0_210
	0x49, 0xc7, 0x07, 0x08, 0x00, 0x00, 0x00, //0x0000094f movq         $8, (%r15)
	0x4c, 0x39, 0xd9, //0x00000956 cmpq         %r11, %rcx
	0x0f, 0x83, 0xf2, 0x07, 0x00, 0x00, //0x00000959 jae          LBB0_238
	0x48, 0x8b, 0x75, 0xc8, //0x0000095f movq         $-56(%rbp), %rsi
	0x8a, 0x1c, 0x0e, //0x00000963 movb         (%rsi,%rcx), %bl
	0x80, 0xc3, 0xc6, //0x00000966 addb         $-58, %bl
	0x80, 0xfb, 0xf5, //0x00000969 cmpb         $-11, %bl
	0x0f, 0x86, 0x1f, 0xfb, 0xff, 0xff, //0x0000096c jbe          LBB0_40
	0x45, 0x31, 0xe4, //0x00000972 xorl         %r12d, %r12d
	0xe9, 0x66, 0x06, 0x00, 0x00, //0x00000975 jmp          LBB0_211
	//0x0000097a LBB0_113
	0x41, 0xf6, 0xc0, 0x20, //0x0000097a testb        $32, %r8b
	0x0f, 0x85, 0x60, 0x03, 0x00, 0x00, //0x0000097e jne          LBB0_163
	0x4c, 0x39, 0xda, //0x00000984 cmpq         %r11, %rdx
	0x0f, 0x84, 0x6b, 0x27, 0x00, 0x00, //0x00000987 je           LBB0_688
	0x4d, 0x89, 0xfe, //0x0000098d movq         %r15, %r14
	0x4d, 0x89, 0xda, //0x00000990 movq         %r11, %r10
	0x49, 0x29, 0xd2, //0x00000993 subq         %rdx, %r10
	0x49, 0x83, 0xfa, 0x40, //0x00000996 cmpq         $64, %r10
	0x0f, 0x82, 0x64, 0x27, 0x00, 0x00, //0x0000099a jb           LBB0_689
	0x48, 0xc7, 0xc1, 0xfe, 0xff, 0xff, 0xff, //0x000009a0 movq         $-2, %rcx
	0x4c, 0x29, 0xc9, //0x000009a7 subq         %r9, %rcx
	0x49, 0xc7, 0xc0, 0xff, 0xff, 0xff, 0xff, //0x000009aa movq         $-1, %r8
	0x45, 0x31, 0xff, //0x000009b1 xorl         %r15d, %r15d
	0xc5, 0xfe, 0x6f, 0x05, 0x64, 0xf6, 0xff, 0xff, //0x000009b4 vmovdqu      $-2460(%rip), %ymm0  /* LCPI0_1+0(%rip) */
	0xc5, 0xfe, 0x6f, 0x0d, 0x7c, 0xf6, 0xff, 0xff, //0x000009bc vmovdqu      $-2436(%rip), %ymm1  /* LCPI0_2+0(%rip) */
	0x49, 0x89, 0xd4, //0x000009c4 movq         %rdx, %r12
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x000009c7 .p2align 4, 0x90
	//0x000009d0 LBB0_117
	0xc4, 0xa1, 0x7e, 0x6f, 0x14, 0x27, //0x000009d0 vmovdqu      (%rdi,%r12), %ymm2
	0xc4, 0xa1, 0x7e, 0x6f, 0x5c, 0x27, 0x20, //0x000009d6 vmovdqu      $32(%rdi,%r12), %ymm3
	0xc5, 0xed, 0x74, 0xe0, //0x000009dd vpcmpeqb     %ymm0, %ymm2, %ymm4
	0xc5, 0x7d, 0xd7, 0xcc, //0x000009e1 vpmovmskb    %ymm4, %r9d
	0xc5, 0xe5, 0x74, 0xe0, //0x000009e5 vpcmpeqb     %ymm0, %ymm3, %ymm4
	0xc5, 0xfd, 0xd7, 0xdc, //0x000009e9 vpmovmskb    %ymm4, %ebx
	0xc5, 0xed, 0x74, 0xd1, //0x000009ed vpcmpeqb     %ymm1, %ymm2, %ymm2
	0xc5, 0xfd, 0xd7, 0xf2, //0x000009f1 vpmovmskb    %ymm2, %esi
	0xc5, 0xe5, 0x74, 0xd1, //0x000009f5 vpcmpeqb     %ymm1, %ymm3, %ymm2
	0xc5, 0xfd, 0xd7, 0xc2, //0x000009f9 vpmovmskb    %ymm2, %eax
	0x48, 0xc1, 0xe3, 0x20, //0x000009fd shlq         $32, %rbx
	0x49, 0x09, 0xd9, //0x00000a01 orq          %rbx, %r9
	0x48, 0xc1, 0xe0, 0x20, //0x00000a04 shlq         $32, %rax
	0x48, 0x09, 0xc6, //0x00000a08 orq          %rax, %rsi
	0x0f, 0x85, 0x30, 0x00, 0x00, 0x00, //0x00000a0b jne          LBB0_121
	0x4d, 0x85, 0xff, //0x00000a11 testq        %r15, %r15
	0x0f, 0x85, 0x41, 0x00, 0x00, 0x00, //0x00000a14 jne          LBB0_123
	0x45, 0x31, 0xff, //0x00000a1a xorl         %r15d, %r15d
	0x4d, 0x85, 0xc9, //0x00000a1d testq        %r9, %r9
	0x0f, 0x85, 0x8f, 0x00, 0x00, 0x00, //0x00000a20 jne          LBB0_125
	//0x00000a26 LBB0_120
	0x49, 0x83, 0xc2, 0xc0, //0x00000a26 addq         $-64, %r10
	0x48, 0x83, 0xc1, 0xc0, //0x00000a2a addq         $-64, %rcx
	0x49, 0x83, 0xc4, 0x40, //0x00000a2e addq         $64, %r12
	0x49, 0x83, 0xfa, 0x3f, //0x00000a32 cmpq         $63, %r10
	0x0f, 0x87, 0x94, 0xff, 0xff, 0xff, //0x00000a36 ja           LBB0_117
	0xe9, 0x3c, 0x0e, 0x00, 0x00, //0x00000a3c jmp          LBB0_326
	//0x00000a41 LBB0_121
	0x48, 0x89, 0x7d, 0xc8, //0x00000a41 movq         %rdi, $-56(%rbp)
	0x49, 0x83, 0xf8, 0xff, //0x00000a45 cmpq         $-1, %r8
	0x0f, 0x85, 0x10, 0x00, 0x00, 0x00, //0x00000a49 jne          LBB0_124
	0x4c, 0x0f, 0xbc, 0xc6, //0x00000a4f bsfq         %rsi, %r8
	0x4d, 0x01, 0xe0, //0x00000a53 addq         %r12, %r8
	0xe9, 0x04, 0x00, 0x00, 0x00, //0x00000a56 jmp          LBB0_124
	//0x00000a5b LBB0_123
	0x48, 0x89, 0x7d, 0xc8, //0x00000a5b movq         %rdi, $-56(%rbp)
	//0x00000a5f LBB0_124
	0x4c, 0x89, 0xf8, //0x00000a5f movq         %r15, %rax
	0x48, 0xf7, 0xd0, //0x00000a62 notq         %rax
	0x48, 0x21, 0xf0, //0x00000a65 andq         %rsi, %rax
	0x4c, 0x8d, 0x2c, 0x00, //0x00000a68 leaq         (%rax,%rax), %r13
	0x4d, 0x09, 0xfd, //0x00000a6c orq          %r15, %r13
	0x4c, 0x89, 0xef, //0x00000a6f movq         %r13, %rdi
	0x48, 0xf7, 0xd7, //0x00000a72 notq         %rdi
	0x48, 0x21, 0xf7, //0x00000a75 andq         %rsi, %rdi
	0x48, 0xbe, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, //0x00000a78 movabsq      $-6148914691236517206, %rsi
	0x48, 0x21, 0xf7, //0x00000a82 andq         %rsi, %rdi
	0x45, 0x31, 0xff, //0x00000a85 xorl         %r15d, %r15d
	0x48, 0x01, 0xc7, //0x00000a88 addq         %rax, %rdi
	0x41, 0x0f, 0x92, 0xc7, //0x00000a8b setb         %r15b
	0x48, 0x01, 0xff, //0x00000a8f addq         %rdi, %rdi
	0x48, 0xb8, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, //0x00000a92 movabsq      $6148914691236517205, %rax
	0x48, 0x31, 0xc7, //0x00000a9c xorq         %rax, %rdi
	0x4c, 0x21, 0xef, //0x00000a9f andq         %r13, %rdi
	0x48, 0xf7, 0xd7, //0x00000aa2 notq         %rdi
	0x49, 0x21, 0xf9, //0x00000aa5 andq         %rdi, %r9
	0x48, 0x8b, 0x7d, 0xc8, //0x00000aa8 movq         $-56(%rbp), %rdi
	0x4d, 0x85, 0xc9, //0x00000aac testq        %r9, %r9
	0x0f, 0x84, 0x71, 0xff, 0xff, 0xff, //0x00000aaf je           LBB0_120
	//0x00000ab5 LBB0_125
	0x4d, 0x0f, 0xbc, 0xd1, //0x00000ab5 bsfq         %r9, %r10
	0x49, 0x29, 0xca, //0x00000ab9 subq         %rcx, %r10
	//0x00000abc LBB0_126
	0x4d, 0x89, 0xf7, //0x00000abc movq         %r14, %r15
	0xe9, 0xaa, 0x04, 0x00, 0x00, //0x00000abf jmp          LBB0_197
	//0x00000ac4 LBB0_127
	0x31, 0xc0, //0x00000ac4 xorl         %eax, %eax
	0x41, 0xf7, 0xc0, 0x00, 0x00, 0x00, 0x80, //0x00000ac6 testl        $-2147483648, %r8d
	0x0f, 0x94, 0xc0, //0x00000acd sete         %al
	0x48, 0xc7, 0xc1, 0xfe, 0xff, 0xff, 0xff, //0x00000ad0 movq         $-2, %rcx
	0xbe, 0x0b, 0x00, 0x00, 0x00, //0x00000ad7 movl         $11, %esi
	0xe9, 0x02, 0x01, 0x00, 0x00, //0x00000adc jmp          LBB0_142
	//0x00000ae1 LBB0_128
	0x49, 0x8d, 0x4b, 0xfd, //0x00000ae1 leaq         $-3(%r11), %rcx
	0x48, 0xc7, 0xc0, 0xff, 0xff, 0xff, 0xff, //0x00000ae5 movq         $-1, %rax
	0x49, 0x39, 0xc9, //0x00000aec cmpq         %rcx, %r9
	0x0f, 0x83, 0xcc, 0x04, 0x00, 0x00, //0x00000aef jae          LBB0_207
	0x41, 0x8b, 0x4d, 0x00, //0x00000af5 movl         (%r13), %ecx
	0x81, 0xf9, 0x6e, 0x75, 0x6c, 0x6c, //0x00000af9 cmpl         $1819047278, %ecx
	0x0f, 0x85, 0x7b, 0x03, 0x00, 0x00, //0x00000aff jne          LBB0_181
	0x49, 0x83, 0xc1, 0x04, //0x00000b05 addq         $4, %r9
	0xb8, 0x02, 0x00, 0x00, 0x00, //0x00000b09 movl         $2, %eax
	0xe9, 0xab, 0x04, 0x00, 0x00, //0x00000b0e jmp          LBB0_206
	//0x00000b13 LBB0_131
	0x31, 0xc0, //0x00000b13 xorl         %eax, %eax
	0x41, 0xf7, 0xc0, 0x00, 0x00, 0x00, 0x80, //0x00000b15 testl        $-2147483648, %r8d
	0x0f, 0x94, 0xc0, //0x00000b1c sete         %al
	0x48, 0xc7, 0xc1, 0xfe, 0xff, 0xff, 0xff, //0x00000b1f movq         $-2, %rcx
	0xbe, 0x0d, 0x00, 0x00, 0x00, //0x00000b26 movl         $13, %esi
	0xe9, 0xb3, 0x00, 0x00, 0x00, //0x00000b2b jmp          LBB0_142
	//0x00000b30 LBB0_132
	0x49, 0xc7, 0x07, 0xfe, 0xff, 0xff, 0xff, //0x00000b30 movq         $-2, (%r15)
	0x4d, 0x89, 0xca, //0x00000b37 movq         %r9, %r10
	0xe9, 0xc4, 0x08, 0x00, 0x00, //0x00000b3a jmp          LBB0_276
	//0x00000b3f LBB0_133
	0x31, 0xc0, //0x00000b3f xorl         %eax, %eax
	0x41, 0xf7, 0xc0, 0x00, 0x00, 0x00, 0x80, //0x00000b41 testl        $-2147483648, %r8d
	0x0f, 0x94, 0xc0, //0x00000b48 sete         %al
	0x48, 0xc7, 0xc1, 0xfe, 0xff, 0xff, 0xff, //0x00000b4b movq         $-2, %rcx
	0xbe, 0x0a, 0x00, 0x00, 0x00, //0x00000b52 movl         $10, %esi
	0xe9, 0x87, 0x00, 0x00, 0x00, //0x00000b57 jmp          LBB0_142
	//0x00000b5c LBB0_134
	0x49, 0xc7, 0x07, 0x05, 0x00, 0x00, 0x00, //0x00000b5c movq         $5, (%r15)
	0xe9, 0x98, 0x08, 0x00, 0x00, //0x00000b63 jmp          LBB0_275
	//0x00000b68 LBB0_135
	0x49, 0x8d, 0x4b, 0xfc, //0x00000b68 leaq         $-4(%r11), %rcx
	0x48, 0xc7, 0xc0, 0xff, 0xff, 0xff, 0xff, //0x00000b6c movq         $-1, %rax
	0x49, 0x39, 0xc9, //0x00000b73 cmpq         %rcx, %r9
	0x0f, 0x83, 0x45, 0x04, 0x00, 0x00, //0x00000b76 jae          LBB0_207
	0x8b, 0x0c, 0x17, //0x00000b7c movl         (%rdi,%rdx), %ecx
	0x81, 0xf9, 0x61, 0x6c, 0x73, 0x65, //0x00000b7f cmpl         $1702063201, %ecx
	0x0f, 0x85, 0x34, 0x03, 0x00, 0x00, //0x00000b85 jne          LBB0_185
	0x49, 0x83, 0xc1, 0x05, //0x00000b8b addq         $5, %r9
	0xb8, 0x04, 0x00, 0x00, 0x00, //0x00000b8f movl         $4, %eax
	0xe9, 0x25, 0x04, 0x00, 0x00, //0x00000b94 jmp          LBB0_206
	//0x00000b99 LBB0_138
	0x49, 0x8d, 0x4b, 0xfd, //0x00000b99 leaq         $-3(%r11), %rcx
	0x48, 0xc7, 0xc0, 0xff, 0xff, 0xff, 0xff, //0x00000b9d movq         $-1, %rax
	0x49, 0x39, 0xc9, //0x00000ba4 cmpq         %rcx, %r9
	0x0f, 0x83, 0x14, 0x04, 0x00, 0x00, //0x00000ba7 jae          LBB0_207
	0x41, 0x8b, 0x4d, 0x00, //0x00000bad movl         (%r13), %ecx
	0x81, 0xf9, 0x74, 0x72, 0x75, 0x65, //0x00000bb1 cmpl         $1702195828, %ecx
	0x0f, 0x85, 0x41, 0x03, 0x00, 0x00, //0x00000bb7 jne          LBB0_189
	0x49, 0x83, 0xc1, 0x04, //0x00000bbd addq         $4, %r9
	0xb8, 0x03, 0x00, 0x00, 0x00, //0x00000bc1 movl         $3, %eax
	0xe9, 0xf3, 0x03, 0x00, 0x00, //0x00000bc6 jmp          LBB0_206
	//0x00000bcb LBB0_141
	0x31, 0xc0, //0x00000bcb xorl         %eax, %eax
	0x41, 0xf7, 0xc0, 0x00, 0x00, 0x00, 0x80, //0x00000bcd testl        $-2147483648, %r8d
	0x0f, 0x94, 0xc0, //0x00000bd4 sete         %al
	0x48, 0xc7, 0xc1, 0xfe, 0xff, 0xff, 0xff, //0x00000bd7 movq         $-2, %rcx
	0xbe, 0x0c, 0x00, 0x00, 0x00, //0x00000bde movl         $12, %esi
	//0x00000be3 LBB0_142
	0x48, 0x0f, 0x44, 0xf1, //0x00000be3 cmoveq       %rcx, %rsi
	0x49, 0x89, 0x37, //0x00000be7 movq         %rsi, (%r15)
	0x48, 0x29, 0xc2, //0x00000bea subq         %rax, %rdx
	0xe9, 0x0e, 0x08, 0x00, 0x00, //0x00000bed jmp          LBB0_275
	//0x00000bf2 LBB0_143
	0x49, 0xc7, 0x07, 0x06, 0x00, 0x00, 0x00, //0x00000bf2 movq         $6, (%r15)
	0xe9, 0x02, 0x08, 0x00, 0x00, //0x00000bf9 jmp          LBB0_275
	//0x00000bfe LBB0_144
	0x89, 0xc9, //0x00000bfe movl         %ecx, %ecx
	0x49, 0x01, 0xc8, //0x00000c00 addq         %rcx, %r8
	0x49, 0x01, 0xc0, //0x00000c03 addq         %rax, %r8
	0x4c, 0x8b, 0x6d, 0xc8, //0x00000c06 movq         $-56(%rbp), %r13
	0xe9, 0x07, 0x00, 0x00, 0x00, //0x00000c0a jmp          LBB0_147
	//0x00000c0f LBB0_145
	0x49, 0x01, 0xc0, //0x00000c0f addq         %rax, %r8
	//0x00000c12 LBB0_146
	0x4c, 0x8b, 0x55, 0xa8, //0x00000c12 movq         $-88(%rbp), %r10
	//0x00000c16 LBB0_147
	0x48, 0xc7, 0xc0, 0xff, 0xff, 0xff, 0xff, //0x00000c16 movq         $-1, %rax
	0x4d, 0x85, 0xed, //0x00000c1d testq        %r13, %r13
	0x0f, 0x84, 0xa2, 0x00, 0x00, 0x00, //0x00000c20 je           LBB0_161
	0x4d, 0x85, 0xe4, //0x00000c26 testq        %r12, %r12
	0x0f, 0x84, 0x99, 0x00, 0x00, 0x00, //0x00000c29 je           LBB0_161
	0x4d, 0x85, 0xff, //0x00000c2f testq        %r15, %r15
	0x0f, 0x84, 0x90, 0x00, 0x00, 0x00, //0x00000c32 je           LBB0_161
	0x4d, 0x29, 0xd0, //0x00000c38 subq         %r10, %r8
	0x49, 0x8d, 0x40, 0xff, //0x00000c3b leaq         $-1(%r8), %rax
	0x49, 0x39, 0xc5, //0x00000c3f cmpq         %rax, %r13
	0x0f, 0x84, 0x34, 0x00, 0x00, 0x00, //0x00000c42 je           LBB0_156
	0x49, 0x39, 0xc4, //0x00000c48 cmpq         %rax, %r12
	0x0f, 0x84, 0x2b, 0x00, 0x00, 0x00, //0x00000c4b je           LBB0_156
	0x49, 0x39, 0xc7, //0x00000c51 cmpq         %rax, %r15
	0x0f, 0x84, 0x22, 0x00, 0x00, 0x00, //0x00000c54 je           LBB0_156
	0x4d, 0x85, 0xe4, //0x00000c5a testq        %r12, %r12
	0x0f, 0x8e, 0xee, 0x01, 0x00, 0x00, //0x00000c5d jle          LBB0_177
	0x49, 0x8d, 0x44, 0x24, 0xff, //0x00000c63 leaq         $-1(%r12), %rax
	0x49, 0x39, 0xc7, //0x00000c68 cmpq         %rax, %r15
	0x0f, 0x84, 0xe0, 0x01, 0x00, 0x00, //0x00000c6b je           LBB0_177
	0x49, 0xf7, 0xd4, //0x00000c71 notq         %r12
	0x4c, 0x89, 0xe0, //0x00000c74 movq         %r12, %rax
	0xe9, 0x06, 0x00, 0x00, 0x00, //0x00000c77 jmp          LBB0_157
	//0x00000c7c LBB0_156
	0x49, 0xf7, 0xd8, //0x00000c7c negq         %r8
	0x4c, 0x89, 0xc0, //0x00000c7f movq         %r8, %rax
	//0x00000c82 LBB0_157
	0x48, 0x85, 0xc0, //0x00000c82 testq        %rax, %rax
	0x0f, 0x88, 0x3d, 0x00, 0x00, 0x00, //0x00000c85 js           LBB0_161
	//0x00000c8b LBB0_158
	0x49, 0x01, 0xc2, //0x00000c8b addq         %rax, %r10
	0x49, 0x29, 0xfa, //0x00000c8e subq         %rdi, %r10
	0x48, 0xb8, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x80, //0x00000c91 movabsq      $-9223372036854775808, %rax
	0x48, 0x83, 0xc0, 0xfe, //0x00000c9b addq         $-2, %rax
	0x49, 0x39, 0xc1, //0x00000c9f cmpq         %rax, %r9
	0x0f, 0x86, 0x0c, 0x00, 0x00, 0x00, //0x00000ca2 jbe          LBB0_160
	0x4c, 0x8b, 0x7d, 0xb8, //0x00000ca8 movq         $-72(%rbp), %r15
	0x4d, 0x89, 0x0f, //0x00000cac movq         %r9, (%r15)
	0xe9, 0x4f, 0x07, 0x00, 0x00, //0x00000caf jmp          LBB0_276
	//0x00000cb4 LBB0_160
	0x48, 0x8b, 0x45, 0xb8, //0x00000cb4 movq         $-72(%rbp), %rax
	0x48, 0xc7, 0x00, 0x08, 0x00, 0x00, 0x00, //0x00000cb8 movq         $8, (%rax)
	0x4c, 0x89, 0x48, 0x18, //0x00000cbf movq         %r9, $24(%rax)
	0xe9, 0x3b, 0x07, 0x00, 0x00, //0x00000cc3 jmp          LBB0_276
	//0x00000cc8 LBB0_161
	0x48, 0xf7, 0xd0, //0x00000cc8 notq         %rax
	0x49, 0x01, 0xc2, //0x00000ccb addq         %rax, %r10
	//0x00000cce LBB0_162
	0x4c, 0x8b, 0x7d, 0xb8, //0x00000cce movq         $-72(%rbp), %r15
	0x49, 0xc7, 0xc1, 0xfe, 0xff, 0xff, 0xff, //0x00000cd2 movq         $-2, %r9
	0x49, 0x29, 0xfa, //0x00000cd9 subq         %rdi, %r10
	0x4d, 0x89, 0x0f, //0x00000cdc movq         %r9, (%r15)
	0xe9, 0x1f, 0x07, 0x00, 0x00, //0x00000cdf jmp          LBB0_276
	//0x00000ce4 LBB0_163
	0x4c, 0x39, 0xda, //0x00000ce4 cmpq         %r11, %rdx
	0x0f, 0x84, 0x0b, 0x24, 0x00, 0x00, //0x00000ce7 je           LBB0_688
	0x49, 0x89, 0xfe, //0x00000ced movq         %rdi, %r14
	0x4c, 0x89, 0x7d, 0xb8, //0x00000cf0 movq         %r15, $-72(%rbp)
	0x4d, 0x89, 0xda, //0x00000cf4 movq         %r11, %r10
	0x49, 0x29, 0xd2, //0x00000cf7 subq         %rdx, %r10
	0x49, 0x83, 0xfa, 0x40, //0x00000cfa cmpq         $64, %r10
	0x0f, 0x82, 0x1d, 0x24, 0x00, 0x00, //0x00000cfe jb           LBB0_690
	0x49, 0xc7, 0xc5, 0xfe, 0xff, 0xff, 0xff, //0x00000d04 movq         $-2, %r13
	0x4d, 0x29, 0xcd, //0x00000d0b subq         %r9, %r13
	0x49, 0xc7, 0xc0, 0xff, 0xff, 0xff, 0xff, //0x00000d0e movq         $-1, %r8
	0x45, 0x31, 0xff, //0x00000d15 xorl         %r15d, %r15d
	0xc5, 0xfe, 0x6f, 0x05, 0x00, 0xf3, 0xff, 0xff, //0x00000d18 vmovdqu      $-3328(%rip), %ymm0  /* LCPI0_1+0(%rip) */
	0xc5, 0xfe, 0x6f, 0x0d, 0x18, 0xf3, 0xff, 0xff, //0x00000d20 vmovdqu      $-3304(%rip), %ymm1  /* LCPI0_2+0(%rip) */
	0xc5, 0xfe, 0x6f, 0x15, 0x30, 0xf3, 0xff, 0xff, //0x00000d28 vmovdqu      $-3280(%rip), %ymm2  /* LCPI0_3+0(%rip) */
	0x49, 0x89, 0xd4, //0x00000d30 movq         %rdx, %r12
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x00000d33 .p2align 4, 0x90
	//0x00000d40 LBB0_166
	0x4c, 0x89, 0xf0, //0x00000d40 movq         %r14, %rax
	0xc4, 0x81, 0x7e, 0x6f, 0x1c, 0x26, //0x00000d43 vmovdqu      (%r14,%r12), %ymm3
	0xc4, 0x81, 0x7e, 0x6f, 0x64, 0x26, 0x20, //0x00000d49 vmovdqu      $32(%r14,%r12), %ymm4
	0xc5, 0xe5, 0x74, 0xe8, //0x00000d50 vpcmpeqb     %ymm0, %ymm3, %ymm5
	0xc5, 0xfd, 0xd7, 0xf5, //0x00000d54 vpmovmskb    %ymm5, %esi
	0xc5, 0xdd, 0x74, 0xe8, //0x00000d58 vpcmpeqb     %ymm0, %ymm4, %ymm5
	0xc5, 0xfd, 0xd7, 0xfd, //0x00000d5c vpmovmskb    %ymm5, %edi
	0xc5, 0xe5, 0x74, 0xe9, //0x00000d60 vpcmpeqb     %ymm1, %ymm3, %ymm5
	0xc5, 0xfd, 0xd7, 0xdd, //0x00000d64 vpmovmskb    %ymm5, %ebx
	0xc5, 0xdd, 0x74, 0xe9, //0x00000d68 vpcmpeqb     %ymm1, %ymm4, %ymm5
	0xc5, 0xfd, 0xd7, 0xc5, //0x00000d6c vpmovmskb    %ymm5, %eax
	0xc5, 0xdd, 0xda, 0xea, //0x00000d70 vpminub      %ymm2, %ymm4, %ymm5
	0xc5, 0xdd, 0x74, 0xe5, //0x00000d74 vpcmpeqb     %ymm5, %ymm4, %ymm4
	0xc5, 0xfd, 0xd7, 0xcc, //0x00000d78 vpmovmskb    %ymm4, %ecx
	0x48, 0xc1, 0xe7, 0x20, //0x00000d7c shlq         $32, %rdi
	0x48, 0x09, 0xfe, //0x00000d80 orq          %rdi, %rsi
	0x48, 0xc1, 0xe0, 0x20, //0x00000d83 shlq         $32, %rax
	0x48, 0xc1, 0xe1, 0x20, //0x00000d87 shlq         $32, %rcx
	0x48, 0x09, 0xc3, //0x00000d8b orq          %rax, %rbx
	0x0f, 0x85, 0x48, 0x00, 0x00, 0x00, //0x00000d8e jne          LBB0_172
	0x4d, 0x85, 0xff, //0x00000d94 testq        %r15, %r15
	0x0f, 0x85, 0x50, 0x00, 0x00, 0x00, //0x00000d97 jne          LBB0_174
	0x45, 0x31, 0xff, //0x00000d9d xorl         %r15d, %r15d
	//0x00000da0 LBB0_169
	0xc5, 0xe5, 0xda, 0xe2, //0x00000da0 vpminub      %ymm2, %ymm3, %ymm4
	0xc5, 0xe5, 0x74, 0xdc, //0x00000da4 vpcmpeqb     %ymm4, %ymm3, %ymm3
	0xc5, 0xfd, 0xd7, 0xc3, //0x00000da8 vpmovmskb    %ymm3, %eax
	0x48, 0x09, 0xc1, //0x00000dac orq          %rax, %rcx
	0x48, 0x85, 0xf6, //0x00000daf testq        %rsi, %rsi
	0x0f, 0x85, 0x83, 0x00, 0x00, 0x00, //0x00000db2 jne          LBB0_175
	0x48, 0x85, 0xc9, //0x00000db8 testq        %rcx, %rcx
	0x0f, 0x85, 0xd4, 0x25, 0x00, 0x00, //0x00000dbb jne          LBB0_729
	0x49, 0x83, 0xc2, 0xc0, //0x00000dc1 addq         $-64, %r10
	0x49, 0x83, 0xc5, 0xc0, //0x00000dc5 addq         $-64, %r13
	0x49, 0x83, 0xc4, 0x40, //0x00000dc9 addq         $64, %r12
	0x49, 0x83, 0xfa, 0x3f, //0x00000dcd cmpq         $63, %r10
	0x0f, 0x87, 0x69, 0xff, 0xff, 0xff, //0x00000dd1 ja           LBB0_166
	0xe9, 0xfd, 0x0a, 0x00, 0x00, //0x00000dd7 jmp          LBB0_331
	//0x00000ddc LBB0_172
	0x49, 0x83, 0xf8, 0xff, //0x00000ddc cmpq         $-1, %r8
	0x0f, 0x85, 0x07, 0x00, 0x00, 0x00, //0x00000de0 jne          LBB0_174
	0x4c, 0x0f, 0xbc, 0xc3, //0x00000de6 bsfq         %rbx, %r8
	0x4d, 0x01, 0xe0, //0x00000dea addq         %r12, %r8
	//0x00000ded LBB0_174
	0x4c, 0x89, 0xf8, //0x00000ded movq         %r15, %rax
	0x48, 0xf7, 0xd0, //0x00000df0 notq         %rax
	0x48, 0x21, 0xd8, //0x00000df3 andq         %rbx, %rax
	0x4c, 0x8d, 0x0c, 0x00, //0x00000df6 leaq         (%rax,%rax), %r9
	0x4d, 0x09, 0xf9, //0x00000dfa orq          %r15, %r9
	0x4c, 0x89, 0xcf, //0x00000dfd movq         %r9, %rdi
	0x48, 0xf7, 0xd7, //0x00000e00 notq         %rdi
	0x48, 0x21, 0xdf, //0x00000e03 andq         %rbx, %rdi
	0x48, 0xbb, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, //0x00000e06 movabsq      $-6148914691236517206, %rbx
	0x48, 0x21, 0xdf, //0x00000e10 andq         %rbx, %rdi
	0x45, 0x31, 0xff, //0x00000e13 xorl         %r15d, %r15d
	0x48, 0x01, 0xc7, //0x00000e16 addq         %rax, %rdi
	0x41, 0x0f, 0x92, 0xc7, //0x00000e19 setb         %r15b
	0x48, 0x01, 0xff, //0x00000e1d addq         %rdi, %rdi
	0x48, 0xb8, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, //0x00000e20 movabsq      $6148914691236517205, %rax
	0x48, 0x31, 0xc7, //0x00000e2a xorq         %rax, %rdi
	0x4c, 0x21, 0xcf, //0x00000e2d andq         %r9, %rdi
	0x48, 0xf7, 0xd7, //0x00000e30 notq         %rdi
	0x48, 0x21, 0xfe, //0x00000e33 andq         %rdi, %rsi
	0xe9, 0x65, 0xff, 0xff, 0xff, //0x00000e36 jmp          LBB0_169
	//0x00000e3b LBB0_175
	0x4c, 0x0f, 0xbc, 0xd6, //0x00000e3b bsfq         %rsi, %r10
	0x48, 0x85, 0xc9, //0x00000e3f testq        %rcx, %rcx
	0x0f, 0x84, 0x11, 0x01, 0x00, 0x00, //0x00000e42 je           LBB0_194
	0x48, 0x0f, 0xbc, 0xc9, //0x00000e48 bsfq         %rcx, %rcx
	0xe9, 0x0d, 0x01, 0x00, 0x00, //0x00000e4c jmp          LBB0_195
	//0x00000e51 LBB0_177
	0x4c, 0x89, 0xe8, //0x00000e51 movq         %r13, %rax
	0x4c, 0x09, 0xf8, //0x00000e54 orq          %r15, %rax
	0x0f, 0x99, 0xc0, //0x00000e57 setns        %al
	0x0f, 0x88, 0xdd, 0x00, 0x00, 0x00, //0x00000e5a js           LBB0_193
	0x4d, 0x39, 0xfd, //0x00000e60 cmpq         %r15, %r13
	0x0f, 0x8c, 0xd4, 0x00, 0x00, 0x00, //0x00000e63 jl           LBB0_193
	0x49, 0xf7, 0xd5, //0x00000e69 notq         %r13
	0x4c, 0x89, 0xe8, //0x00000e6c movq         %r13, %rax
	0xe9, 0x0e, 0xfe, 0xff, 0xff, //0x00000e6f jmp          LBB0_157
	//0x00000e74 LBB0_180
	0x31, 0xd2, //0x00000e74 xorl         %edx, %edx
	0x31, 0xc0, //0x00000e76 xorl         %eax, %eax
	0x45, 0x31, 0xd2, //0x00000e78 xorl         %r10d, %r10d
	0xe9, 0x60, 0x01, 0x00, 0x00, //0x00000e7b jmp          LBB0_211
	//0x00000e80 LBB0_181
	0x48, 0xc7, 0xc0, 0xfe, 0xff, 0xff, 0xff, //0x00000e80 movq         $-2, %rax
	0x80, 0xf9, 0x6e, //0x00000e87 cmpb         $110, %cl
	0x0f, 0x85, 0x2e, 0x01, 0x00, 0x00, //0x00000e8a jne          LBB0_206
	0x42, 0x80, 0x7c, 0x0f, 0x01, 0x75, //0x00000e90 cmpb         $117, $1(%rdi,%r9)
	0x0f, 0x85, 0x15, 0x01, 0x00, 0x00, //0x00000e96 jne          LBB0_204
	0x42, 0x80, 0x7c, 0x0f, 0x02, 0x6c, //0x00000e9c cmpb         $108, $2(%rdi,%r9)
	0x0f, 0x85, 0x12, 0x01, 0x00, 0x00, //0x00000ea2 jne          LBB0_205
	0x4d, 0x8d, 0x59, 0x03, //0x00000ea8 leaq         $3(%r9), %r11
	0x49, 0x8d, 0x49, 0x04, //0x00000eac leaq         $4(%r9), %rcx
	0x42, 0x80, 0x7c, 0x0f, 0x03, 0x6c, //0x00000eb0 cmpb         $108, $3(%rdi,%r9)
	0x4c, 0x0f, 0x44, 0xd9, //0x00000eb6 cmoveq       %rcx, %r11
	0xe9, 0x02, 0x01, 0x00, 0x00, //0x00000eba jmp          LBB0_207
	//0x00000ebf LBB0_185
	0x48, 0xc7, 0xc0, 0xfe, 0xff, 0xff, 0xff, //0x00000ebf movq         $-2, %rax
	0x80, 0xf9, 0x61, //0x00000ec6 cmpb         $97, %cl
	0x0f, 0x85, 0xda, 0x00, 0x00, 0x00, //0x00000ec9 jne          LBB0_202
	0x42, 0x80, 0x7c, 0x0f, 0x02, 0x6c, //0x00000ecf cmpb         $108, $2(%rdi,%r9)
	0x0f, 0x85, 0xdf, 0x00, 0x00, 0x00, //0x00000ed5 jne          LBB0_205
	0x42, 0x80, 0x7c, 0x0f, 0x03, 0x73, //0x00000edb cmpb         $115, $3(%rdi,%r9)
	0x0f, 0x85, 0x31, 0x05, 0x00, 0x00, //0x00000ee1 jne          LBB0_277
	0x4d, 0x8d, 0x59, 0x04, //0x00000ee7 leaq         $4(%r9), %r11
	0x49, 0x8d, 0x49, 0x05, //0x00000eeb leaq         $5(%r9), %rcx
	0x42, 0x80, 0x7c, 0x0f, 0x04, 0x65, //0x00000eef cmpb         $101, $4(%rdi,%r9)
	0x4c, 0x0f, 0x44, 0xd9, //0x00000ef5 cmoveq       %rcx, %r11
	0xe9, 0xc3, 0x00, 0x00, 0x00, //0x00000ef9 jmp          LBB0_207
	//0x00000efe LBB0_189
	0x48, 0xc7, 0xc0, 0xfe, 0xff, 0xff, 0xff, //0x00000efe movq         $-2, %rax
	0x80, 0xf9, 0x74, //0x00000f05 cmpb         $116, %cl
	0x0f, 0x85, 0xb0, 0x00, 0x00, 0x00, //0x00000f08 jne          LBB0_206
	0x42, 0x80, 0x7c, 0x0f, 0x01, 0x72, //0x00000f0e cmpb         $114, $1(%rdi,%r9)
	0x0f, 0x85, 0x97, 0x00, 0x00, 0x00, //0x00000f14 jne          LBB0_204
	0x42, 0x80, 0x7c, 0x0f, 0x02, 0x75, //0x00000f1a cmpb         $117, $2(%rdi,%r9)
	0x0f, 0x85, 0x94, 0x00, 0x00, 0x00, //0x00000f20 jne          LBB0_205
	0x4d, 0x8d, 0x59, 0x03, //0x00000f26 leaq         $3(%r9), %r11
	0x49, 0x8d, 0x49, 0x04, //0x00000f2a leaq         $4(%r9), %rcx
	0x42, 0x80, 0x7c, 0x0f, 0x03, 0x65, //0x00000f2e cmpb         $101, $3(%rdi,%r9)
	0x4c, 0x0f, 0x44, 0xd9, //0x00000f34 cmoveq       %rcx, %r11
	0xe9, 0x84, 0x00, 0x00, 0x00, //0x00000f38 jmp          LBB0_207
	//0x00000f3d LBB0_193
	0x49, 0x8d, 0x4f, 0xff, //0x00000f3d leaq         $-1(%r15), %rcx
	0x49, 0x39, 0xcd, //0x00000f41 cmpq         %rcx, %r13
	0x49, 0xf7, 0xd7, //0x00000f44 notq         %r15
	0x4d, 0x0f, 0x45, 0xf8, //0x00000f47 cmovneq      %r8, %r15
	0x84, 0xc0, //0x00000f4b testb        %al, %al
	0x4d, 0x0f, 0x44, 0xf8, //0x00000f4d cmoveq       %r8, %r15
	0x4c, 0x89, 0xf8, //0x00000f51 movq         %r15, %rax
	0xe9, 0x29, 0xfd, 0xff, 0xff, //0x00000f54 jmp          LBB0_157
	//0x00000f59 LBB0_194
	0xb9, 0x40, 0x00, 0x00, 0x00, //0x00000f59 movl         $64, %ecx
	//0x00000f5e LBB0_195
	0x4c, 0x8b, 0x7d, 0xb8, //0x00000f5e movq         $-72(%rbp), %r15
	0x4c, 0x39, 0xd1, //0x00000f62 cmpq         %r10, %rcx
	0x0f, 0x82, 0xd6, 0x21, 0x00, 0x00, //0x00000f65 jb           LBB0_691
	0x4d, 0x29, 0xea, //0x00000f6b subq         %r13, %r10
	//0x00000f6e LBB0_197
	0x4d, 0x85, 0xd2, //0x00000f6e testq        %r10, %r10
	0x0f, 0x88, 0x22, 0x00, 0x00, 0x00, //0x00000f71 js           LBB0_199
	0x49, 0x89, 0x57, 0x10, //0x00000f77 movq         %rdx, $16(%r15)
	0x49, 0xc7, 0x07, 0x07, 0x00, 0x00, 0x00, //0x00000f7b movq         $7, (%r15)
	0x4d, 0x39, 0xd0, //0x00000f82 cmpq         %r10, %r8
	0x48, 0xc7, 0xc0, 0xff, 0xff, 0xff, 0xff, //0x00000f85 movq         $-1, %rax
	0x49, 0x0f, 0x4c, 0xc0, //0x00000f8c cmovlq       %r8, %rax
	0x49, 0x89, 0x47, 0x18, //0x00000f90 movq         %rax, $24(%r15)
	0xe9, 0x6a, 0x04, 0x00, 0x00, //0x00000f94 jmp          LBB0_276
	//0x00000f99 LBB0_199
	0x4c, 0x89, 0xd0, //0x00000f99 movq         %r10, %rax
	0xe9, 0x20, 0x00, 0x00, 0x00, //0x00000f9c jmp          LBB0_207
	//0x00000fa1 LBB0_200
	0x4c, 0x89, 0xd9, //0x00000fa1 movq         %r11, %rcx
	0xe9, 0x37, 0x00, 0x00, 0x00, //0x00000fa4 jmp          LBB0_211
	//0x00000fa9 LBB0_202
	0x49, 0x89, 0xd3, //0x00000fa9 movq         %rdx, %r11
	0xe9, 0x10, 0x00, 0x00, 0x00, //0x00000fac jmp          LBB0_207
	//0x00000fb1 LBB0_204
	0x49, 0x83, 0xc1, 0x01, //0x00000fb1 addq         $1, %r9
	0xe9, 0x04, 0x00, 0x00, 0x00, //0x00000fb5 jmp          LBB0_206
	//0x00000fba LBB0_205
	0x49, 0x83, 0xc1, 0x02, //0x00000fba addq         $2, %r9
	//0x00000fbe LBB0_206
	0x4d, 0x89, 0xcb, //0x00000fbe movq         %r9, %r11
	//0x00000fc1 LBB0_207
	0x49, 0x89, 0x07, //0x00000fc1 movq         %rax, (%r15)
	0x4d, 0x89, 0xda, //0x00000fc4 movq         %r11, %r10
	0xe9, 0x37, 0x04, 0x00, 0x00, //0x00000fc7 jmp          LBB0_276
	//0x00000fcc LBB0_208
	0x4d, 0x89, 0xd0, //0x00000fcc movq         %r10, %r8
	0xe9, 0x3e, 0xfc, 0xff, 0xff, //0x00000fcf jmp          LBB0_146
	//0x00000fd4 LBB0_209
	0x0f, 0xbc, 0xc9, //0x00000fd4 bsfl         %ecx, %ecx
	0xe9, 0xa2, 0x03, 0x00, 0x00, //0x00000fd7 jmp          LBB0_269
	//0x00000fdc LBB0_210
	0x48, 0x83, 0xc1, 0xff, //0x00000fdc addq         $-1, %rcx
	//0x00000fe0 LBB0_211
	0x31, 0xf6, //0x00000fe0 xorl         %esi, %esi
	0x85, 0xd2, //0x00000fe2 testl        %edx, %edx
	0x40, 0x0f, 0x9f, 0xc6, //0x00000fe4 setg         %sil
	0x89, 0x75, 0xa4, //0x00000fe8 movl         %esi, $-92(%rbp)
	0x4d, 0x85, 0xd2, //0x00000feb testq        %r10, %r10
	0x4c, 0x89, 0x7d, 0xb8, //0x00000fee movq         %r15, $-72(%rbp)
	0x0f, 0x85, 0x54, 0x00, 0x00, 0x00, //0x00000ff2 jne          LBB0_220
	0x85, 0xd2, //0x00000ff8 testl        %edx, %edx
	0x0f, 0x85, 0x4c, 0x00, 0x00, 0x00, //0x00000ffa jne          LBB0_220
	0x4c, 0x39, 0xd9, //0x00001000 cmpq         %r11, %rcx
	0x0f, 0x83, 0x30, 0x00, 0x00, 0x00, //0x00001003 jae          LBB0_218
	0x41, 0x89, 0xcf, //0x00001009 movl         %ecx, %r15d
	0x45, 0x29, 0xdf, //0x0000100c subl         %r11d, %r15d
	0x31, 0xc0, //0x0000100f xorl         %eax, %eax
	0x31, 0xd2, //0x00001011 xorl         %edx, %edx
	0x48, 0x8b, 0x75, 0xc8, //0x00001013 movq         $-56(%rbp), %rsi
	//0x00001017 LBB0_215
	0x80, 0x3c, 0x0e, 0x30, //0x00001017 cmpb         $48, (%rsi,%rcx)
	0x0f, 0x85, 0x24, 0x00, 0x00, 0x00, //0x0000101b jne          LBB0_219
	0x48, 0x83, 0xc1, 0x01, //0x00001021 addq         $1, %rcx
	0x83, 0xc2, 0xff, //0x00001025 addl         $-1, %edx
	0x49, 0x39, 0xcb, //0x00001028 cmpq         %rcx, %r11
	0x0f, 0x85, 0xe6, 0xff, 0xff, 0xff, //0x0000102b jne          LBB0_215
	0x45, 0x31, 0xd2, //0x00001031 xorl         %r10d, %r10d
	0xe9, 0x3d, 0x01, 0x00, 0x00, //0x00001034 jmp          LBB0_241
	//0x00001039 LBB0_218
	0x31, 0xd2, //0x00001039 xorl         %edx, %edx
	0x31, 0xc0, //0x0000103b xorl         %eax, %eax
	0x45, 0x31, 0xd2, //0x0000103d xorl         %r10d, %r10d
	0xe9, 0x07, 0x00, 0x00, 0x00, //0x00001040 jmp          LBB0_220
	//0x00001045 LBB0_219
	0x45, 0x31, 0xd2, //0x00001045 xorl         %r10d, %r10d
	0x4c, 0x8b, 0x7d, 0xb8, //0x00001048 movq         $-72(%rbp), %r15
	//0x0000104c LBB0_220
	0x4c, 0x39, 0xd9, //0x0000104c cmpq         %r11, %rcx
	0x0f, 0x83, 0x4b, 0x00, 0x00, 0x00, //0x0000104f jae          LBB0_226
	0x83, 0xf8, 0x12, //0x00001055 cmpl         $18, %eax
	0x0f, 0x8f, 0x42, 0x00, 0x00, 0x00, //0x00001058 jg           LBB0_226
	0xbe, 0xd0, 0xff, 0xff, 0xff, //0x0000105e movl         $4294967248, %esi
	//0x00001063 LBB0_223
	0x48, 0x8b, 0x7d, 0xc8, //0x00001063 movq         $-56(%rbp), %rdi
	0x0f, 0xb6, 0x1c, 0x0f, //0x00001067 movzbl       (%rdi,%rcx), %ebx
	0x8d, 0x7b, 0xd0, //0x0000106b leal         $-48(%rbx), %edi
	0x40, 0x80, 0xff, 0x09, //0x0000106e cmpb         $9, %dil
	0x0f, 0x87, 0x28, 0x00, 0x00, 0x00, //0x00001072 ja           LBB0_226
	0x4b, 0x8d, 0x3c, 0x92, //0x00001078 leaq         (%r10,%r10,4), %rdi
	0x01, 0xf3, //0x0000107c addl         %esi, %ebx
	0x4c, 0x8d, 0x14, 0x7b, //0x0000107e leaq         (%rbx,%rdi,2), %r10
	0x83, 0xc2, 0xff, //0x00001082 addl         $-1, %edx
	0x48, 0x83, 0xc1, 0x01, //0x00001085 addq         $1, %rcx
	0x4c, 0x39, 0xd9, //0x00001089 cmpq         %r11, %rcx
	0x0f, 0x83, 0x0e, 0x00, 0x00, 0x00, //0x0000108c jae          LBB0_226
	0x8d, 0x78, 0x01, //0x00001092 leal         $1(%rax), %edi
	0x83, 0xf8, 0x12, //0x00001095 cmpl         $18, %eax
	0x89, 0xf8, //0x00001098 movl         %edi, %eax
	0x0f, 0x8c, 0xc3, 0xff, 0xff, 0xff, //0x0000109a jl           LBB0_223
	//0x000010a0 LBB0_226
	0x4c, 0x39, 0xd9, //0x000010a0 cmpq         %r11, %rcx
	0x0f, 0x83, 0xb7, 0x00, 0x00, 0x00, //0x000010a3 jae          LBB0_239
	0x48, 0x8b, 0x45, 0xc8, //0x000010a9 movq         $-56(%rbp), %rax
	0x8a, 0x04, 0x08, //0x000010ad movb         (%rax,%rcx), %al
	0x8d, 0x70, 0xd0, //0x000010b0 leal         $-48(%rax), %esi
	0x40, 0x80, 0xfe, 0x09, //0x000010b3 cmpb         $9, %sil
	0x0f, 0x87, 0x2e, 0x00, 0x00, 0x00, //0x000010b7 ja           LBB0_232
	0x49, 0x8d, 0x73, 0xff, //0x000010bd leaq         $-1(%r11), %rsi
	//0x000010c1 LBB0_229
	0x48, 0x39, 0xce, //0x000010c1 cmpq         %rcx, %rsi
	0x0f, 0x84, 0xa2, 0x00, 0x00, 0x00, //0x000010c4 je           LBB0_240
	0x48, 0x8b, 0x45, 0xc8, //0x000010ca movq         $-56(%rbp), %rax
	0x0f, 0xb6, 0x44, 0x08, 0x01, //0x000010ce movzbl       $1(%rax,%rcx), %eax
	0x8d, 0x78, 0xd0, //0x000010d3 leal         $-48(%rax), %edi
	0x48, 0x83, 0xc1, 0x01, //0x000010d6 addq         $1, %rcx
	0x40, 0x80, 0xff, 0x09, //0x000010da cmpb         $9, %dil
	0x0f, 0x86, 0xdd, 0xff, 0xff, 0xff, //0x000010de jbe          LBB0_229
	0xc7, 0x45, 0xa4, 0x01, 0x00, 0x00, 0x00, //0x000010e4 movl         $1, $-92(%rbp)
	//0x000010eb LBB0_232
	0x0c, 0x20, //0x000010eb orb          $32, %al
	0x3c, 0x65, //0x000010ed cmpb         $101, %al
	0x0f, 0x85, 0x6b, 0x00, 0x00, 0x00, //0x000010ef jne          LBB0_239
	0x48, 0x8d, 0x71, 0x01, //0x000010f5 leaq         $1(%rcx), %rsi
	0x49, 0xc7, 0x07, 0x08, 0x00, 0x00, 0x00, //0x000010f9 movq         $8, (%r15)
	0x4c, 0x39, 0xde, //0x00001100 cmpq         %r11, %rsi
	0x0f, 0x83, 0x48, 0x00, 0x00, 0x00, //0x00001103 jae          LBB0_238
	0x48, 0x8b, 0x45, 0xc8, //0x00001109 movq         $-56(%rbp), %rax
	0x8a, 0x1c, 0x30, //0x0000110d movb         (%rax,%rsi), %bl
	0x80, 0xfb, 0x2d, //0x00001110 cmpb         $45, %bl
	0x0f, 0x84, 0x0f, 0x00, 0x00, 0x00, //0x00001113 je           LBB0_236
	0x41, 0xbc, 0x01, 0x00, 0x00, 0x00, //0x00001119 movl         $1, %r12d
	0x80, 0xfb, 0x2b, //0x0000111f cmpb         $43, %bl
	0x0f, 0x85, 0x00, 0x03, 0x00, 0x00, //0x00001122 jne          LBB0_279
	//0x00001128 LBB0_236
	0x48, 0x83, 0xc1, 0x02, //0x00001128 addq         $2, %rcx
	0x4c, 0x39, 0xd9, //0x0000112c cmpq         %r11, %rcx
	0x0f, 0x83, 0x1c, 0x00, 0x00, 0x00, //0x0000112f jae          LBB0_238
	0x31, 0xc0, //0x00001135 xorl         %eax, %eax
	0x80, 0xfb, 0x2b, //0x00001137 cmpb         $43, %bl
	0x0f, 0x94, 0xc0, //0x0000113a sete         %al
	0x44, 0x8d, 0x24, 0x00, //0x0000113d leal         (%rax,%rax), %r12d
	0x41, 0x83, 0xc4, 0xff, //0x00001141 addl         $-1, %r12d
	0x48, 0x8b, 0x45, 0xc8, //0x00001145 movq         $-56(%rbp), %rax
	0x8a, 0x1c, 0x08, //0x00001149 movb         (%rax,%rcx), %bl
	0xe9, 0xda, 0x02, 0x00, 0x00, //0x0000114c jmp          LBB0_280
	//0x00001151 LBB0_238
	0x49, 0xc7, 0x07, 0xff, 0xff, 0xff, 0xff, //0x00001151 movq         $-1, (%r15)
	0x4d, 0x89, 0xda, //0x00001158 movq         %r11, %r10
	0xe9, 0xa3, 0x02, 0x00, 0x00, //0x0000115b jmp          LBB0_276
	//0x00001160 LBB0_239
	0x41, 0x89, 0xd7, //0x00001160 movl         %edx, %r15d
	0x48, 0x89, 0x4d, 0xa8, //0x00001163 movq         %rcx, $-88(%rbp)
	0xe9, 0x0e, 0x00, 0x00, 0x00, //0x00001167 jmp          LBB0_242
	//0x0000116c LBB0_240
	0xc7, 0x45, 0xa4, 0x01, 0x00, 0x00, 0x00, //0x0000116c movl         $1, $-92(%rbp)
	0x41, 0x89, 0xd7, //0x00001173 movl         %edx, %r15d
	//0x00001176 LBB0_241
	0x4c, 0x89, 0x5d, 0xa8, //0x00001176 movq         %r11, $-88(%rbp)
	//0x0000117a LBB0_242
	0x45, 0x84, 0xe4, //0x0000117a testb        %r12b, %r12b
	0x0f, 0x84, 0x39, 0x00, 0x00, 0x00, //0x0000117d je           LBB0_247
	0x45, 0x85, 0xff, //0x00001183 testl        %r15d, %r15d
	0x0f, 0x85, 0x25, 0x00, 0x00, 0x00, //0x00001186 jne          LBB0_246
	0x48, 0xb8, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x80, //0x0000118c movabsq      $-9223372036854775808, %rax
	0x49, 0x63, 0xce, //0x00001196 movslq       %r14d, %rcx
	0x4d, 0x85, 0xd2, //0x00001199 testq        %r10, %r10
	0x0f, 0x89, 0xe7, 0x01, 0x00, 0x00, //0x0000119c jns          LBB0_270
	0x4c, 0x89, 0xd2, //0x000011a2 movq         %r10, %rdx
	0x48, 0x21, 0xca, //0x000011a5 andq         %rcx, %rdx
	0x48, 0x39, 0xc2, //0x000011a8 cmpq         %rax, %rdx
	0x0f, 0x84, 0xd8, 0x01, 0x00, 0x00, //0x000011ab je           LBB0_270
	//0x000011b1 LBB0_246
	0x48, 0x8b, 0x45, 0xb8, //0x000011b1 movq         $-72(%rbp), %rax
	0x48, 0xc7, 0x00, 0x08, 0x00, 0x00, 0x00, //0x000011b5 movq         $8, (%rax)
	//0x000011bc LBB0_247
	0x48, 0xba, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x80, //0x000011bc movabsq      $-9223372036854775808, %rdx
	0x48, 0xb8, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x0f, 0x00, //0x000011c6 movabsq      $4503599627370495, %rax
	0x48, 0x8d, 0x48, 0x01, //0x000011d0 leaq         $1(%rax), %rcx
	0x49, 0x39, 0xca, //0x000011d4 cmpq         %rcx, %r10
	0x0f, 0x83, 0xd5, 0x00, 0x00, 0x00, //0x000011d7 jae          LBB0_259
	0xc4, 0xc1, 0xf9, 0x6e, 0xc2, //0x000011dd vmovq        %r10, %xmm0
	0xc5, 0xf9, 0x62, 0x05, 0x06, 0xf0, 0xff, 0xff, //0x000011e2 vpunpckldq   $-4090(%rip), %xmm0, %xmm0  /* LCPI0_18+0(%rip) */
	0xc5, 0xf9, 0x5c, 0x05, 0x0e, 0xf0, 0xff, 0xff, //0x000011ea vsubpd       $-4082(%rip), %xmm0, %xmm0  /* LCPI0_19+0(%rip) */
	0xc4, 0xe3, 0x79, 0x05, 0xc8, 0x01, //0x000011f2 vpermilpd    $1, %xmm0, %xmm1
	0xc5, 0xf3, 0x58, 0xc0, //0x000011f8 vaddsd       %xmm0, %xmm1, %xmm0
	0x41, 0xc1, 0xee, 0x1f, //0x000011fc shrl         $31, %r14d
	0x49, 0xc1, 0xe6, 0x3f, //0x00001200 shlq         $63, %r14
	0xc4, 0xc1, 0xf9, 0x6e, 0xce, //0x00001204 vmovq        %r14, %xmm1
	0xc5, 0xf9, 0x56, 0xc1, //0x00001209 vorpd        %xmm1, %xmm0, %xmm0
	0x4d, 0x85, 0xd2, //0x0000120d testq        %r10, %r10
	0x0f, 0x84, 0x72, 0x00, 0x00, 0x00, //0x00001210 je           LBB0_256
	0x45, 0x85, 0xff, //0x00001216 testl        %r15d, %r15d
	0x49, 0x89, 0xd6, //0x00001219 movq         %rdx, %r14
	0x0f, 0x84, 0x1e, 0x1e, 0x00, 0x00, //0x0000121c je           LBB0_678
	0x41, 0x8d, 0x47, 0xff, //0x00001222 leal         $-1(%r15), %eax
	0x83, 0xf8, 0x24, //0x00001226 cmpl         $36, %eax
	0x0f, 0x87, 0x61, 0x00, 0x00, 0x00, //0x00001229 ja           LBB0_257
	0x48, 0x89, 0x4d, 0x98, //0x0000122f movq         %rcx, $-104(%rbp)
	0x44, 0x89, 0xf8, //0x00001233 movl         %r15d, %eax
	0x41, 0x83, 0xff, 0x17, //0x00001236 cmpl         $23, %r15d
	0x0f, 0x82, 0x15, 0x00, 0x00, 0x00, //0x0000123a jb           LBB0_253
	0x41, 0x8d, 0x47, 0xea, //0x00001240 leal         $-22(%r15), %eax
	0x48, 0x8d, 0x0d, 0x85, 0x24, 0x00, 0x00, //0x00001244 leaq         $9349(%rip), %rcx  /* _P10_TAB+0(%rip) */
	0xc5, 0xfb, 0x59, 0x04, 0xc1, //0x0000124b vmulsd       (%rcx,%rax,8), %xmm0, %xmm0
	0xb8, 0x16, 0x00, 0x00, 0x00, //0x00001250 movl         $22, %eax
	//0x00001255 LBB0_253
	0xc5, 0xf9, 0x2e, 0x05, 0xb3, 0xef, 0xff, 0xff, //0x00001255 vucomisd     $-4173(%rip), %xmm0  /* LCPI0_20+0(%rip) */
	0x0f, 0x87, 0x68, 0x00, 0x00, 0x00, //0x0000125d ja           LBB0_260
	0xc5, 0xfb, 0x10, 0x0d, 0xad, 0xef, 0xff, 0xff, //0x00001263 vmovsd       $-4179(%rip), %xmm1  /* LCPI0_21+0(%rip) */
	0xc5, 0xf9, 0x2e, 0xc8, //0x0000126b vucomisd     %xmm0, %xmm1
	0x0f, 0x87, 0x56, 0x00, 0x00, 0x00, //0x0000126f ja           LBB0_260
	0x89, 0xc0, //0x00001275 movl         %eax, %eax
	0x48, 0x8d, 0x0d, 0x52, 0x24, 0x00, 0x00, //0x00001277 leaq         $9298(%rip), %rcx  /* _P10_TAB+0(%rip) */
	0xc5, 0xfb, 0x59, 0x04, 0xc1, //0x0000127e vmulsd       (%rcx,%rax,8), %xmm0, %xmm0
	0xe9, 0xb8, 0x1d, 0x00, 0x00, //0x00001283 jmp          LBB0_678
	//0x00001288 LBB0_256
	0x49, 0x89, 0xd6, //0x00001288 movq         %rdx, %r14
	0xe9, 0xb0, 0x1d, 0x00, 0x00, //0x0000128b jmp          LBB0_678
	//0x00001290 LBB0_257
	0x41, 0x83, 0xff, 0xea, //0x00001290 cmpl         $-22, %r15d
	0x0f, 0x82, 0x18, 0x00, 0x00, 0x00, //0x00001294 jb           LBB0_259
	0x41, 0xf7, 0xdf, //0x0000129a negl         %r15d
	0x48, 0x8d, 0x05, 0x2c, 0x24, 0x00, 0x00, //0x0000129d leaq         $9260(%rip), %rax  /* _P10_TAB+0(%rip) */
	0xc4, 0xa1, 0x7b, 0x5e, 0x04, 0xf8, //0x000012a4 vdivsd       (%rax,%r15,8), %xmm0, %xmm0
	0x49, 0x89, 0xd6, //0x000012aa movq         %rdx, %r14
	0xe9, 0x8e, 0x1d, 0x00, 0x00, //0x000012ad jmp          LBB0_678
	//0x000012b2 LBB0_259
	0x48, 0x89, 0x4d, 0x98, //0x000012b2 movq         %rcx, $-104(%rbp)
	0x41, 0x8d, 0x87, 0xa4, 0xfe, 0xff, 0xff, //0x000012b6 leal         $-348(%r15), %eax
	0x3d, 0x48, 0xfd, 0xff, 0xff, //0x000012bd cmpl         $-696, %eax
	0x49, 0x89, 0xd6, //0x000012c2 movq         %rdx, %r14
	0x0f, 0x82, 0x7d, 0x02, 0x00, 0x00, //0x000012c5 jb           LBB0_295
	//0x000012cb LBB0_260
	0x4d, 0x85, 0xd2, //0x000012cb testq        %r10, %r10
	0x0f, 0x84, 0x0d, 0x00, 0x00, 0x00, //0x000012ce je           LBB0_262
	0x49, 0x0f, 0xbd, 0xf2, //0x000012d4 bsrq         %r10, %rsi
	0x48, 0x83, 0xf6, 0x3f, //0x000012d8 xorq         $63, %rsi
	0xe9, 0x05, 0x00, 0x00, 0x00, //0x000012dc jmp          LBB0_263
	//0x000012e1 LBB0_262
	0xbe, 0x40, 0x00, 0x00, 0x00, //0x000012e1 movl         $64, %esi
	//0x000012e6 LBB0_263
	0x4c, 0x89, 0xd3, //0x000012e6 movq         %r10, %rbx
	0x89, 0xf1, //0x000012e9 movl         %esi, %ecx
	0x48, 0xd3, 0xe3, //0x000012eb shlq         %cl, %rbx
	0x45, 0x8d, 0xa7, 0x5c, 0x01, 0x00, 0x00, //0x000012ee leal         $348(%r15), %r12d
	0x49, 0xc1, 0xe4, 0x04, //0x000012f5 shlq         $4, %r12
	0x48, 0x8d, 0x05, 0x90, 0x24, 0x00, 0x00, //0x000012f9 leaq         $9360(%rip), %rax  /* _POW10_M128_TAB+0(%rip) */
	0x49, 0x8b, 0x44, 0x04, 0x08, //0x00001300 movq         $8(%r12,%rax), %rax
	0x48, 0x89, 0x45, 0x90, //0x00001305 movq         %rax, $-112(%rbp)
	0x48, 0xf7, 0xe3, //0x00001309 mulq         %rbx
	0x48, 0x89, 0xd7, //0x0000130c movq         %rdx, %rdi
	0x81, 0xe2, 0xff, 0x01, 0x00, 0x00, //0x0000130f andl         $511, %edx
	0x48, 0x81, 0xfa, 0xff, 0x01, 0x00, 0x00, //0x00001315 cmpq         $511, %rdx
	0x0f, 0x85, 0xaa, 0x00, 0x00, 0x00, //0x0000131c jne          LBB0_271
	0x48, 0x89, 0xd9, //0x00001322 movq         %rbx, %rcx
	0x48, 0xf7, 0xd1, //0x00001325 notq         %rcx
	0x48, 0x39, 0xc8, //0x00001328 cmpq         %rcx, %rax
	0x49, 0x89, 0xc3, //0x0000132b movq         %rax, %r11
	0x0f, 0x86, 0x9e, 0x01, 0x00, 0x00, //0x0000132e jbe          LBB0_291
	0x48, 0x89, 0xd8, //0x00001334 movq         %rbx, %rax
	0x48, 0x8d, 0x15, 0x52, 0x24, 0x00, 0x00, //0x00001337 leaq         $9298(%rip), %rdx  /* _POW10_M128_TAB+0(%rip) */
	0x49, 0xf7, 0x24, 0x14, //0x0000133e mulq         (%r12,%rdx)
	0x49, 0x01, 0xd3, //0x00001342 addq         %rdx, %r11
	0x48, 0x83, 0xd7, 0x00, //0x00001345 adcq         $0, %rdi
	0x89, 0xfa, //0x00001349 movl         %edi, %edx
	0x81, 0xe2, 0xff, 0x01, 0x00, 0x00, //0x0000134b andl         $511, %edx
	0x48, 0x81, 0xfa, 0xff, 0x01, 0x00, 0x00, //0x00001351 cmpq         $511, %rdx
	0x0f, 0x85, 0x74, 0x01, 0x00, 0x00, //0x00001358 jne          LBB0_291
	0x4c, 0x89, 0xdb, //0x0000135e movq         %r11, %rbx
	0x49, 0x83, 0xfb, 0xff, //0x00001361 cmpq         $-1, %r11
	0x0f, 0x85, 0x64, 0x01, 0x00, 0x00, //0x00001365 jne          LBB0_290
	0x48, 0x39, 0xc8, //0x0000136b cmpq         %rcx, %rax
	0x49, 0x89, 0xdb, //0x0000136e movq         %rbx, %r11
	0x0f, 0x87, 0xd1, 0x01, 0x00, 0x00, //0x00001371 ja           LBB0_295
	0xe9, 0x56, 0x01, 0x00, 0x00, //0x00001377 jmp          LBB0_291
	//0x0000137c LBB0_268
	0x89, 0xc9, //0x0000137c movl         %ecx, %ecx
	//0x0000137e LBB0_269
	0x48, 0xf7, 0xd0, //0x0000137e notq         %rax
	0x48, 0x29, 0xc8, //0x00001381 subq         %rcx, %rax
	0xe9, 0xf9, 0xf8, 0xff, 0xff, //0x00001384 jmp          LBB0_157
	//0x00001389 LBB0_270
	0xc4, 0xc1, 0xf9, 0x6e, 0xc2, //0x00001389 vmovq        %r10, %xmm0
	0x4c, 0x0f, 0xaf, 0xd1, //0x0000138e imulq        %rcx, %r10
	0x48, 0x8b, 0x55, 0xb8, //0x00001392 movq         $-72(%rbp), %rdx
	0x4c, 0x89, 0x52, 0x10, //0x00001396 movq         %r10, $16(%rdx)
	0xc5, 0xf9, 0x62, 0x05, 0x4e, 0xee, 0xff, 0xff, //0x0000139a vpunpckldq   $-4530(%rip), %xmm0, %xmm0  /* LCPI0_18+0(%rip) */
	0xc5, 0xf9, 0x5c, 0x05, 0x56, 0xee, 0xff, 0xff, //0x000013a2 vsubpd       $-4522(%rip), %xmm0, %xmm0  /* LCPI0_19+0(%rip) */
	0xc4, 0xe3, 0x79, 0x05, 0xc8, 0x01, //0x000013aa vpermilpd    $1, %xmm0, %xmm1
	0xc5, 0xf3, 0x58, 0xc0, //0x000013b0 vaddsd       %xmm0, %xmm1, %xmm0
	0x48, 0x21, 0xc8, //0x000013b4 andq         %rcx, %rax
	0xc4, 0xe1, 0xf9, 0x7e, 0xc1, //0x000013b7 vmovq        %xmm0, %rcx
	0x48, 0x09, 0xc1, //0x000013bc orq          %rax, %rcx
	0x48, 0x89, 0x4a, 0x08, //0x000013bf movq         %rcx, $8(%rdx)
	0x4c, 0x8b, 0x55, 0xa8, //0x000013c3 movq         $-88(%rbp), %r10
	0xe9, 0x37, 0x00, 0x00, 0x00, //0x000013c7 jmp          LBB0_276
	//0x000013cc LBB0_271
	0x49, 0x89, 0xc3, //0x000013cc movq         %rax, %r11
	0xe9, 0xfe, 0x00, 0x00, 0x00, //0x000013cf jmp          LBB0_291
	//0x000013d4 LBB0_272
	0x4c, 0x03, 0x75, 0xb0, //0x000013d4 addq         $-80(%rbp), %r14
	0x4d, 0x29, 0xc6, //0x000013d8 subq         %r8, %r14
	0x48, 0xf7, 0xd0, //0x000013db notq         %rax
	0x4c, 0x01, 0xf0, //0x000013de addq         %r14, %rax
	0x4c, 0x8b, 0x55, 0xa8, //0x000013e1 movq         $-88(%rbp), %r10
	0xe9, 0x98, 0xf8, 0xff, 0xff, //0x000013e5 jmp          LBB0_157
	//0x000013ea LBB0_273
	0x49, 0x29, 0xf9, //0x000013ea subq         %rdi, %r9
	0x49, 0x01, 0xc9, //0x000013ed addq         %rcx, %r9
	0x4d, 0x39, 0xd9, //0x000013f0 cmpq         %r11, %r9
	0x0f, 0x82, 0xf8, 0xef, 0xff, 0xff, //0x000013f3 jb           LBB0_31
	//0x000013f9 LBB0_274
	0x49, 0xc7, 0x07, 0x01, 0x00, 0x00, 0x00, //0x000013f9 movq         $1, (%r15)
	//0x00001400 LBB0_275
	0x49, 0x89, 0xd2, //0x00001400 movq         %rdx, %r10
	//0x00001403 LBB0_276
	0x4c, 0x89, 0xd0, //0x00001403 movq         %r10, %rax
	0x48, 0x83, 0xc4, 0x48, //0x00001406 addq         $72, %rsp
	0x5b, //0x0000140a popq         %rbx
	0x41, 0x5c, //0x0000140b popq         %r12
	0x41, 0x5d, //0x0000140d popq         %r13
	0x41, 0x5e, //0x0000140f popq         %r14
	0x41, 0x5f, //0x00001411 popq         %r15
	0x5d, //0x00001413 popq         %rbp
	0xc5, 0xf8, 0x77, //0x00001414 vzeroupper   
	0xc3, //0x00001417 retq         
	//0x00001418 LBB0_277
	0x49, 0x83, 0xc1, 0x03, //0x00001418 addq         $3, %r9
	0xe9, 0x9d, 0xfb, 0xff, 0xff, //0x0000141c jmp          LBB0_206
	//0x00001421 LBB0_278
	0x89, 0xf1, //0x00001421 movl         %esi, %ecx
	0xe9, 0x8e, 0x00, 0x00, 0x00, //0x00001423 jmp          LBB0_289
	//0x00001428 LBB0_279
	0x48, 0x89, 0xf1, //0x00001428 movq         %rsi, %rcx
	//0x0000142b LBB0_280
	0x8d, 0x73, 0xc6, //0x0000142b leal         $-58(%rbx), %esi
	0x40, 0x80, 0xfe, 0xf6, //0x0000142e cmpb         $-10, %sil
	0x0f, 0x82, 0x59, 0xf0, 0xff, 0xff, //0x00001432 jb           LBB0_40
	0x45, 0x31, 0xff, //0x00001438 xorl         %r15d, %r15d
	0x4c, 0x39, 0xd9, //0x0000143b cmpq         %r11, %rcx
	0x0f, 0x83, 0xf8, 0x04, 0x00, 0x00, //0x0000143e jae          LBB0_337
	0x49, 0x8d, 0x73, 0xff, //0x00001444 leaq         $-1(%r11), %rsi
	0x45, 0x31, 0xff, //0x00001448 xorl         %r15d, %r15d
	//0x0000144b LBB0_283
	0x44, 0x89, 0xff, //0x0000144b movl         %r15d, %edi
	0x0f, 0xb6, 0xdb, //0x0000144e movzbl       %bl, %ebx
	0x41, 0x81, 0xff, 0x10, 0x27, 0x00, 0x00, //0x00001451 cmpl         $10000, %r15d
	0x8d, 0x04, 0xbf, //0x00001458 leal         (%rdi,%rdi,4), %eax
	0x44, 0x8d, 0x7c, 0x43, 0xd0, //0x0000145b leal         $-48(%rbx,%rax,2), %r15d
	0x44, 0x0f, 0x4d, 0xff, //0x00001460 cmovgel      %edi, %r15d
	0x48, 0x39, 0xce, //0x00001464 cmpq         %rcx, %rsi
	0x0f, 0x84, 0xcc, 0x04, 0x00, 0x00, //0x00001467 je           LBB0_336
	0x48, 0x8b, 0x45, 0xc8, //0x0000146d movq         $-56(%rbp), %rax
	0x0f, 0xb6, 0x5c, 0x08, 0x01, //0x00001471 movzbl       $1(%rax,%rcx), %ebx
	0x8d, 0x43, 0xd0, //0x00001476 leal         $-48(%rbx), %eax
	0x48, 0x83, 0xc1, 0x01, //0x00001479 addq         $1, %rcx
	0x3c, 0x0a, //0x0000147d cmpb         $10, %al
	0x0f, 0x82, 0xc6, 0xff, 0xff, 0xff, //0x0000147f jb           LBB0_283
	0xe9, 0xb2, 0x04, 0x00, 0x00, //0x00001485 jmp          LBB0_337
	//0x0000148a LBB0_285
	0x41, 0x0f, 0xbc, 0xca, //0x0000148a bsfl         %r10d, %ecx
	0xe9, 0x03, 0x00, 0x00, 0x00, //0x0000148e jmp          LBB0_287
	//0x00001493 LBB0_286
	0x0f, 0xbc, 0xca, //0x00001493 bsfl         %edx, %ecx
	//0x00001496 LBB0_287
	0x48, 0x8b, 0x55, 0xc0, //0x00001496 movq         $-64(%rbp), %rdx
	0x48, 0x03, 0x55, 0xb0, //0x0000149a addq         $-80(%rbp), %rdx
	0x4c, 0x29, 0xc2, //0x0000149e subq         %r8, %rdx
	0x48, 0x29, 0xca, //0x000014a1 subq         %rcx, %rdx
	0x48, 0xf7, 0xd0, //0x000014a4 notq         %rax
	0x48, 0x01, 0xd0, //0x000014a7 addq         %rdx, %rax
	0x4c, 0x8b, 0x55, 0xa8, //0x000014aa movq         $-88(%rbp), %r10
	0xe9, 0xcf, 0xf7, 0xff, 0xff, //0x000014ae jmp          LBB0_157
	//0x000014b3 LBB0_288
	0x0f, 0xbc, 0xca, //0x000014b3 bsfl         %edx, %ecx
	//0x000014b6 LBB0_289
	0x48, 0x8b, 0x55, 0xc0, //0x000014b6 movq         $-64(%rbp), %rdx
	0x48, 0x03, 0x55, 0xb0, //0x000014ba addq         $-80(%rbp), %rdx
	0x4c, 0x29, 0xc2, //0x000014be subq         %r8, %rdx
	0x48, 0x29, 0xca, //0x000014c1 subq         %rcx, %rdx
	0x48, 0xf7, 0xd0, //0x000014c4 notq         %rax
	0x48, 0x01, 0xd0, //0x000014c7 addq         %rdx, %rax
	0xe9, 0xb3, 0xf7, 0xff, 0xff, //0x000014ca jmp          LBB0_157
	//0x000014cf LBB0_290
	0x49, 0x89, 0xdb, //0x000014cf movq         %rbx, %r11
	//0x000014d2 LBB0_291
	0x48, 0x89, 0xf8, //0x000014d2 movq         %rdi, %rax
	0x48, 0xc1, 0xe8, 0x3f, //0x000014d5 shrq         $63, %rax
	0x8d, 0x48, 0x09, //0x000014d9 leal         $9(%rax), %ecx
	0x48, 0xd3, 0xef, //0x000014dc shrq         %cl, %rdi
	0x4d, 0x85, 0xdb, //0x000014df testq        %r11, %r11
	0x0f, 0x85, 0x17, 0x00, 0x00, 0x00, //0x000014e2 jne          LBB0_294
	0x48, 0x85, 0xd2, //0x000014e8 testq        %rdx, %rdx
	0x0f, 0x85, 0x0e, 0x00, 0x00, 0x00, //0x000014eb jne          LBB0_294
	0x89, 0xf9, //0x000014f1 movl         %edi, %ecx
	0x83, 0xe1, 0x03, //0x000014f3 andl         $3, %ecx
	0x83, 0xf9, 0x01, //0x000014f6 cmpl         $1, %ecx
	0x0f, 0x84, 0x49, 0x00, 0x00, 0x00, //0x000014f9 je           LBB0_295
	//0x000014ff LBB0_294
	0x41, 0x69, 0xcf, 0x6a, 0x52, 0x03, 0x00, //0x000014ff imull        $217706, %r15d, %ecx
	0xc1, 0xf9, 0x10, //0x00001506 sarl         $16, %ecx
	0x81, 0xc1, 0x3f, 0x04, 0x00, 0x00, //0x00001509 addl         $1087, %ecx
	0x4c, 0x63, 0xf9, //0x0000150f movslq       %ecx, %r15
	0x48, 0x89, 0xf1, //0x00001512 movq         %rsi, %rcx
	0x4c, 0x89, 0xfe, //0x00001515 movq         %r15, %rsi
	0x48, 0x29, 0xce, //0x00001518 subq         %rcx, %rsi
	0x89, 0xfa, //0x0000151b movl         %edi, %edx
	0x83, 0xe2, 0x01, //0x0000151d andl         $1, %edx
	0x48, 0x01, 0xfa, //0x00001520 addq         %rdi, %rdx
	0x48, 0x89, 0xd1, //0x00001523 movq         %rdx, %rcx
	0x48, 0xc1, 0xe9, 0x36, //0x00001526 shrq         $54, %rcx
	0x48, 0x01, 0xc6, //0x0000152a addq         %rax, %rsi
	0x48, 0x83, 0xf9, 0x01, //0x0000152d cmpq         $1, %rcx
	0x48, 0x83, 0xde, 0x00, //0x00001531 sbbq         $0, %rsi
	0x48, 0x8d, 0x86, 0x01, 0xf8, 0xff, 0xff, //0x00001535 leaq         $-2047(%rsi), %rax
	0x48, 0x3d, 0x02, 0xf8, 0xff, 0xff, //0x0000153c cmpq         $-2046, %rax
	0x0f, 0x83, 0x48, 0x00, 0x00, 0x00, //0x00001542 jae          LBB0_300
	//0x00001548 LBB0_295
	0x4c, 0x8b, 0x55, 0xa8, //0x00001548 movq         $-88(%rbp), %r10
	0x4d, 0x89, 0xd4, //0x0000154c movq         %r10, %r12
	0x4d, 0x29, 0xcc, //0x0000154f subq         %r9, %r12
	0x4d, 0x85, 0xed, //0x00001552 testq        %r13, %r13
	0x0f, 0x84, 0x0a, 0x04, 0x00, 0x00, //0x00001555 je           LBB0_340
	0x41, 0xc6, 0x00, 0x00, //0x0000155b movb         $0, (%r8)
	0x49, 0x83, 0xfd, 0x01, //0x0000155f cmpq         $1, %r13
	0x0f, 0x84, 0xfc, 0x03, 0x00, 0x00, //0x00001563 je           LBB0_340
	0x49, 0x8d, 0x4d, 0xff, //0x00001569 leaq         $-1(%r13), %rcx
	0xb8, 0x01, 0x00, 0x00, 0x00, //0x0000156d movl         $1, %eax
	0x48, 0x83, 0xf9, 0x10, //0x00001572 cmpq         $16, %rcx
	0x0f, 0x82, 0xd7, 0x03, 0x00, 0x00, //0x00001576 jb           LBB0_339
	0x48, 0x81, 0xf9, 0x80, 0x00, 0x00, 0x00, //0x0000157c cmpq         $128, %rcx
	0x0f, 0x83, 0x85, 0x01, 0x00, 0x00, //0x00001583 jae          LBB0_313
	0x31, 0xd2, //0x00001589 xorl         %edx, %edx
	0xe9, 0xab, 0x02, 0x00, 0x00, //0x0000158b jmp          LBB0_322
	//0x00001590 LBB0_300
	0x48, 0xb8, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x40, 0x00, //0x00001590 movabsq      $18014398509481984, %rax
	0x48, 0x39, 0xc2, //0x0000159a cmpq         %rax, %rdx
	0xb1, 0x02, //0x0000159d movb         $2, %cl
	0x80, 0xd9, 0x00, //0x0000159f sbbb         $0, %cl
	0x48, 0xd3, 0xea, //0x000015a2 shrq         %cl, %rdx
	0x48, 0xc1, 0xe6, 0x34, //0x000015a5 shlq         $52, %rsi
	0x48, 0xb8, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x0f, 0x00, //0x000015a9 movabsq      $4503599627370495, %rax
	0x48, 0x21, 0xc2, //0x000015b3 andq         %rax, %rdx
	0x48, 0x09, 0xf2, //0x000015b6 orq          %rsi, %rdx
	0x48, 0x89, 0xd0, //0x000015b9 movq         %rdx, %rax
	0x4c, 0x09, 0xf0, //0x000015bc orq          %r14, %rax
	0x80, 0x7d, 0xb0, 0x2d, //0x000015bf cmpb         $45, $-80(%rbp)
	0x48, 0x0f, 0x45, 0xc2, //0x000015c3 cmovneq      %rdx, %rax
	0xc4, 0xe1, 0xf9, 0x6e, 0xc0, //0x000015c7 vmovq        %rax, %xmm0
	0x83, 0x7d, 0xa4, 0x00, //0x000015cc cmpl         $0, $-92(%rbp)
	0x0f, 0x84, 0x6a, 0x1a, 0x00, 0x00, //0x000015d0 je           LBB0_678
	0xb9, 0x40, 0x00, 0x00, 0x00, //0x000015d6 movl         $64, %ecx
	0x49, 0xff, 0xc2, //0x000015db incq         %r10
	0x0f, 0x84, 0x08, 0x00, 0x00, 0x00, //0x000015de je           LBB0_303
	0x49, 0x0f, 0xbd, 0xca, //0x000015e4 bsrq         %r10, %rcx
	0x48, 0x83, 0xf1, 0x3f, //0x000015e8 xorq         $63, %rcx
	//0x000015ec LBB0_303
	0x48, 0x89, 0xcf, //0x000015ec movq         %rcx, %rdi
	0x49, 0xd3, 0xe2, //0x000015ef shlq         %cl, %r10
	0x48, 0x8b, 0x45, 0x90, //0x000015f2 movq         $-112(%rbp), %rax
	0x49, 0xf7, 0xe2, //0x000015f6 mulq         %r10
	0x48, 0x89, 0xc6, //0x000015f9 movq         %rax, %rsi
	0x48, 0x89, 0xd3, //0x000015fc movq         %rdx, %rbx
	0x81, 0xe2, 0xff, 0x01, 0x00, 0x00, //0x000015ff andl         $511, %edx
	0x48, 0x81, 0xfa, 0xff, 0x01, 0x00, 0x00, //0x00001605 cmpq         $511, %rdx
	0x0f, 0x85, 0x4c, 0x00, 0x00, 0x00, //0x0000160c jne          LBB0_308
	0x4c, 0x89, 0xd1, //0x00001612 movq         %r10, %rcx
	0x48, 0xf7, 0xd1, //0x00001615 notq         %rcx
	0x48, 0x39, 0xce, //0x00001618 cmpq         %rcx, %rsi
	0x0f, 0x86, 0x3d, 0x00, 0x00, 0x00, //0x0000161b jbe          LBB0_308
	0x4c, 0x89, 0xd0, //0x00001621 movq         %r10, %rax
	0x48, 0x8d, 0x15, 0x65, 0x21, 0x00, 0x00, //0x00001624 leaq         $8549(%rip), %rdx  /* _POW10_M128_TAB+0(%rip) */
	0x49, 0xf7, 0x24, 0x14, //0x0000162b mulq         (%r12,%rdx)
	0x48, 0x01, 0xd6, //0x0000162f addq         %rdx, %rsi
	0x48, 0x83, 0xd3, 0x00, //0x00001632 adcq         $0, %rbx
	0x89, 0xda, //0x00001636 movl         %ebx, %edx
	0x81, 0xe2, 0xff, 0x01, 0x00, 0x00, //0x00001638 andl         $511, %edx
	0x48, 0x81, 0xfa, 0xff, 0x01, 0x00, 0x00, //0x0000163e cmpq         $511, %rdx
	0x0f, 0x85, 0x13, 0x00, 0x00, 0x00, //0x00001645 jne          LBB0_308
	0x48, 0x83, 0xfe, 0xff, //0x0000164b cmpq         $-1, %rsi
	0x0f, 0x85, 0x09, 0x00, 0x00, 0x00, //0x0000164f jne          LBB0_308
	0x48, 0x39, 0xc8, //0x00001655 cmpq         %rcx, %rax
	0x0f, 0x87, 0xea, 0xfe, 0xff, 0xff, //0x00001658 ja           LBB0_295
	//0x0000165e LBB0_308
	0x48, 0x89, 0xd8, //0x0000165e movq         %rbx, %rax
	0x48, 0xc1, 0xe8, 0x3f, //0x00001661 shrq         $63, %rax
	0x8d, 0x48, 0x09, //0x00001665 leal         $9(%rax), %ecx
	0x48, 0xd3, 0xeb, //0x00001668 shrq         %cl, %rbx
	0x48, 0x85, 0xf6, //0x0000166b testq        %rsi, %rsi
	0x0f, 0x85, 0x17, 0x00, 0x00, 0x00, //0x0000166e jne          LBB0_311
	0x48, 0x85, 0xd2, //0x00001674 testq        %rdx, %rdx
	0x0f, 0x85, 0x0e, 0x00, 0x00, 0x00, //0x00001677 jne          LBB0_311
	0x89, 0xd9, //0x0000167d movl         %ebx, %ecx
	0x83, 0xe1, 0x03, //0x0000167f andl         $3, %ecx
	0x83, 0xf9, 0x01, //0x00001682 cmpl         $1, %ecx
	0x0f, 0x84, 0xbd, 0xfe, 0xff, 0xff, //0x00001685 je           LBB0_295
	//0x0000168b LBB0_311
	0x49, 0x29, 0xff, //0x0000168b subq         %rdi, %r15
	0x89, 0xda, //0x0000168e movl         %ebx, %edx
	0x83, 0xe2, 0x01, //0x00001690 andl         $1, %edx
	0x48, 0x01, 0xda, //0x00001693 addq         %rbx, %rdx
	0x49, 0x01, 0xc7, //0x00001696 addq         %rax, %r15
	0x48, 0xb8, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x40, 0x00, //0x00001699 movabsq      $18014398509481984, %rax
	0x48, 0x39, 0xc2, //0x000016a3 cmpq         %rax, %rdx
	0x49, 0x83, 0xdf, 0x00, //0x000016a6 sbbq         $0, %r15
	0x49, 0x8d, 0x87, 0x01, 0xf8, 0xff, 0xff, //0x000016aa leaq         $-2047(%r15), %rax
	0x48, 0x3d, 0x02, 0xf8, 0xff, 0xff, //0x000016b1 cmpq         $-2046, %rax
	0x0f, 0x82, 0x8b, 0xfe, 0xff, 0xff, //0x000016b7 jb           LBB0_295
	0x48, 0xb8, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x40, 0x00, //0x000016bd movabsq      $18014398509481984, %rax
	0x48, 0x39, 0xc2, //0x000016c7 cmpq         %rax, %rdx
	0xb1, 0x02, //0x000016ca movb         $2, %cl
	0x80, 0xd9, 0x00, //0x000016cc sbbb         $0, %cl
	0x48, 0xd3, 0xea, //0x000016cf shrq         %cl, %rdx
	0x49, 0xc1, 0xe7, 0x34, //0x000016d2 shlq         $52, %r15
	0x48, 0xb8, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x0f, 0x00, //0x000016d6 movabsq      $4503599627370495, %rax
	0x48, 0x21, 0xc2, //0x000016e0 andq         %rax, %rdx
	0x4c, 0x09, 0xfa, //0x000016e3 orq          %r15, %rdx
	0x48, 0x89, 0xd0, //0x000016e6 movq         %rdx, %rax
	0x4c, 0x09, 0xf0, //0x000016e9 orq          %r14, %rax
	0x80, 0x7d, 0xb0, 0x2d, //0x000016ec cmpb         $45, $-80(%rbp)
	0x48, 0x0f, 0x45, 0xc2, //0x000016f0 cmovneq      %rdx, %rax
	0xc4, 0xe1, 0xf9, 0x6e, 0xc8, //0x000016f4 vmovq        %rax, %xmm1
	0xc5, 0xf9, 0x2e, 0xc1, //0x000016f9 vucomisd     %xmm1, %xmm0
	0x0f, 0x85, 0x45, 0xfe, 0xff, 0xff, //0x000016fd jne          LBB0_295
	0x0f, 0x8b, 0x37, 0x19, 0x00, 0x00, //0x00001703 jnp          LBB0_678
	0xe9, 0x3a, 0xfe, 0xff, 0xff, //0x00001709 jmp          LBB0_295
	//0x0000170e LBB0_313
	0x48, 0x89, 0xca, //0x0000170e movq         %rcx, %rdx
	0x48, 0x83, 0xe2, 0x80, //0x00001711 andq         $-128, %rdx
	0x48, 0x8d, 0x7a, 0x80, //0x00001715 leaq         $-128(%rdx), %rdi
	0x48, 0x89, 0xfe, //0x00001719 movq         %rdi, %rsi
	0x48, 0xc1, 0xee, 0x07, //0x0000171c shrq         $7, %rsi
	0x48, 0x83, 0xc6, 0x01, //0x00001720 addq         $1, %rsi
	0x89, 0xf0, //0x00001724 movl         %esi, %eax
	0x83, 0xe0, 0x03, //0x00001726 andl         $3, %eax
	0x48, 0x81, 0xff, 0x80, 0x01, 0x00, 0x00, //0x00001729 cmpq         $384, %rdi
	0x0f, 0x83, 0x07, 0x00, 0x00, 0x00, //0x00001730 jae          LBB0_315
	0x31, 0xdb, //0x00001736 xorl         %ebx, %ebx
	0xe9, 0xaf, 0x00, 0x00, 0x00, //0x00001738 jmp          LBB0_317
	//0x0000173d LBB0_315
	0x48, 0x83, 0xe6, 0xfc, //0x0000173d andq         $-4, %rsi
	0x31, 0xdb, //0x00001741 xorl         %ebx, %ebx
	0xc5, 0xf9, 0xef, 0xc0, //0x00001743 vpxor        %xmm0, %xmm0, %xmm0
	//0x00001747 LBB0_316
	0xc4, 0xc1, 0x7e, 0x7f, 0x44, 0x18, 0x01, //0x00001747 vmovdqu      %ymm0, $1(%r8,%rbx)
	0xc4, 0xc1, 0x7e, 0x7f, 0x44, 0x18, 0x21, //0x0000174e vmovdqu      %ymm0, $33(%r8,%rbx)
	0xc4, 0xc1, 0x7e, 0x7f, 0x44, 0x18, 0x41, //0x00001755 vmovdqu      %ymm0, $65(%r8,%rbx)
	0xc4, 0xc1, 0x7e, 0x7f, 0x44, 0x18, 0x61, //0x0000175c vmovdqu      %ymm0, $97(%r8,%rbx)
	0xc4, 0xc1, 0x7e, 0x7f, 0x84, 0x18, 0x81, 0x00, 0x00, 0x00, //0x00001763 vmovdqu      %ymm0, $129(%r8,%rbx)
	0xc4, 0xc1, 0x7e, 0x7f, 0x84, 0x18, 0xa1, 0x00, 0x00, 0x00, //0x0000176d vmovdqu      %ymm0, $161(%r8,%rbx)
	0xc4, 0xc1, 0x7e, 0x7f, 0x84, 0x18, 0xc1, 0x00, 0x00, 0x00, //0x00001777 vmovdqu      %ymm0, $193(%r8,%rbx)
	0xc4, 0xc1, 0x7e, 0x7f, 0x84, 0x18, 0xe1, 0x00, 0x00, 0x00, //0x00001781 vmovdqu      %ymm0, $225(%r8,%rbx)
	0xc4, 0xc1, 0x7e, 0x7f, 0x84, 0x18, 0x01, 0x01, 0x00, 0x00, //0x0000178b vmovdqu      %ymm0, $257(%r8,%rbx)
	0xc4, 0xc1, 0x7e, 0x7f, 0x84, 0x18, 0x21, 0x01, 0x00, 0x00, //0x00001795 vmovdqu      %ymm0, $289(%r8,%rbx)
	0xc4, 0xc1, 0x7e, 0x7f, 0x84, 0x18, 0x41, 0x01, 0x00, 0x00, //0x0000179f vmovdqu      %ymm0, $321(%r8,%rbx)
	0xc4, 0xc1, 0x7e, 0x7f, 0x84, 0x18, 0x61, 0x01, 0x00, 0x00, //0x000017a9 vmovdqu      %ymm0, $353(%r8,%rbx)
	0xc4, 0xc1, 0x7e, 0x7f, 0x84, 0x18, 0x81, 0x01, 0x00, 0x00, //0x000017b3 vmovdqu      %ymm0, $385(%r8,%rbx)
	0xc4, 0xc1, 0x7e, 0x7f, 0x84, 0x18, 0xa1, 0x01, 0x00, 0x00, //0x000017bd vmovdqu      %ymm0, $417(%r8,%rbx)
	0xc4, 0xc1, 0x7e, 0x7f, 0x84, 0x18, 0xc1, 0x01, 0x00, 0x00, //0x000017c7 vmovdqu      %ymm0, $449(%r8,%rbx)
	0xc4, 0xc1, 0x7e, 0x7f, 0x84, 0x18, 0xe1, 0x01, 0x00, 0x00, //0x000017d1 vmovdqu      %ymm0, $481(%r8,%rbx)
	0x48, 0x81, 0xc3, 0x00, 0x02, 0x00, 0x00, //0x000017db addq         $512, %rbx
	0x48, 0x83, 0xc6, 0xfc, //0x000017e2 addq         $-4, %rsi
	0x0f, 0x85, 0x5b, 0xff, 0xff, 0xff, //0x000017e6 jne          LBB0_316
	//0x000017ec LBB0_317
	0x48, 0x85, 0xc0, //0x000017ec testq        %rax, %rax
	0x0f, 0x84, 0x34, 0x00, 0x00, 0x00, //0x000017ef je           LBB0_320
	0xc5, 0xf9, 0xef, 0xc0, //0x000017f5 vpxor        %xmm0, %xmm0, %xmm0
	//0x000017f9 LBB0_319
	0x48, 0x89, 0xde, //0x000017f9 movq         %rbx, %rsi
	0x48, 0x83, 0xce, 0x01, //0x000017fc orq          $1, %rsi
	0xc4, 0xc1, 0x7e, 0x7f, 0x04, 0x30, //0x00001800 vmovdqu      %ymm0, (%r8,%rsi)
	0xc4, 0xc1, 0x7e, 0x7f, 0x44, 0x30, 0x20, //0x00001806 vmovdqu      %ymm0, $32(%r8,%rsi)
	0xc4, 0xc1, 0x7e, 0x7f, 0x44, 0x30, 0x40, //0x0000180d vmovdqu      %ymm0, $64(%r8,%rsi)
	0xc4, 0xc1, 0x7e, 0x7f, 0x44, 0x30, 0x60, //0x00001814 vmovdqu      %ymm0, $96(%r8,%rsi)
	0x48, 0x83, 0xeb, 0x80, //0x0000181b subq         $-128, %rbx
	0x48, 0x83, 0xc0, 0xff, //0x0000181f addq         $-1, %rax
	0x0f, 0x85, 0xd0, 0xff, 0xff, 0xff, //0x00001823 jne          LBB0_319
	//0x00001829 LBB0_320
	0x48, 0x39, 0xd1, //0x00001829 cmpq         %rdx, %rcx
	0x0f, 0x84, 0x33, 0x01, 0x00, 0x00, //0x0000182c je           LBB0_340
	0xf6, 0xc1, 0x70, //0x00001832 testb        $112, %cl
	0x0f, 0x84, 0x11, 0x01, 0x00, 0x00, //0x00001835 je           LBB0_338
	//0x0000183b LBB0_322
	0x48, 0x89, 0xce, //0x0000183b movq         %rcx, %rsi
	0x48, 0x83, 0xe6, 0xf0, //0x0000183e andq         $-16, %rsi
	0x48, 0x8d, 0x46, 0x01, //0x00001842 leaq         $1(%rsi), %rax
	0xc5, 0xf9, 0xef, 0xc0, //0x00001846 vpxor        %xmm0, %xmm0, %xmm0
	//0x0000184a LBB0_323
	0xc4, 0xc1, 0x7a, 0x7f, 0x44, 0x10, 0x01, //0x0000184a vmovdqu      %xmm0, $1(%r8,%rdx)
	0x48, 0x83, 0xc2, 0x10, //0x00001851 addq         $16, %rdx
	0x48, 0x39, 0xd6, //0x00001855 cmpq         %rdx, %rsi
	0x0f, 0x85, 0xec, 0xff, 0xff, 0xff, //0x00001858 jne          LBB0_323
	0x48, 0x39, 0xf1, //0x0000185e cmpq         %rsi, %rcx
	0x0f, 0x85, 0xec, 0x00, 0x00, 0x00, //0x00001861 jne          LBB0_339
	0xe9, 0xf9, 0x00, 0x00, 0x00, //0x00001867 jmp          LBB0_340
	//0x0000186c LBB0_325
	0x49, 0x01, 0xf9, //0x0000186c addq         %rdi, %r9
	0x48, 0x85, 0xc0, //0x0000186f testq        %rax, %rax
	0x0f, 0x85, 0x0e, 0xeb, 0xff, 0xff, //0x00001872 jne          LBB0_23
	0xe9, 0x41, 0xeb, 0xff, 0xff, //0x00001878 jmp          LBB0_28
	//0x0000187d LBB0_326
	0x49, 0x01, 0xfc, //0x0000187d addq         %rdi, %r12
	0x49, 0x83, 0xfa, 0x20, //0x00001880 cmpq         $32, %r10
	0x0f, 0x82, 0x28, 0x19, 0x00, 0x00, //0x00001884 jb           LBB0_697
	//0x0000188a LBB0_327
	0xc4, 0xc1, 0x7e, 0x6f, 0x04, 0x24, //0x0000188a vmovdqu      (%r12), %ymm0
	0xc5, 0xfd, 0x74, 0x0d, 0x88, 0xe7, 0xff, 0xff, //0x00001890 vpcmpeqb     $-6264(%rip), %ymm0, %ymm1  /* LCPI0_1+0(%rip) */
	0xc5, 0xfd, 0xd7, 0xc9, //0x00001898 vpmovmskb    %ymm1, %ecx
	0xc5, 0xfd, 0x74, 0x05, 0x9c, 0xe7, 0xff, 0xff, //0x0000189c vpcmpeqb     $-6244(%rip), %ymm0, %ymm0  /* LCPI0_2+0(%rip) */
	0xc5, 0xfd, 0xd7, 0xf0, //0x000018a4 vpmovmskb    %ymm0, %esi
	0x85, 0xf6, //0x000018a8 testl        %esi, %esi
	0x0f, 0x85, 0x9d, 0x18, 0x00, 0x00, //0x000018aa jne          LBB0_692
	0x4d, 0x85, 0xff, //0x000018b0 testq        %r15, %r15
	0x0f, 0x85, 0xb3, 0x18, 0x00, 0x00, //0x000018b3 jne          LBB0_694
	0x45, 0x31, 0xff, //0x000018b9 xorl         %r15d, %r15d
	0x48, 0x85, 0xc9, //0x000018bc testq        %rcx, %rcx
	0x0f, 0x84, 0xe5, 0x18, 0x00, 0x00, //0x000018bf je           LBB0_696
	//0x000018c5 LBB0_330
	0x48, 0x0f, 0xbc, 0xc1, //0x000018c5 bsfq         %rcx, %rax
	0x49, 0x29, 0xfc, //0x000018c9 subq         %rdi, %r12
	0x4d, 0x8d, 0x14, 0x04, //0x000018cc leaq         (%r12,%rax), %r10
	0x49, 0x83, 0xc2, 0x01, //0x000018d0 addq         $1, %r10
	0xe9, 0xe3, 0xf1, 0xff, 0xff, //0x000018d4 jmp          LBB0_126
	//0x000018d9 LBB0_331
	0x4c, 0x89, 0xf7, //0x000018d9 movq         %r14, %rdi
	0x4d, 0x01, 0xf4, //0x000018dc addq         %r14, %r12
	0x49, 0x83, 0xfa, 0x20, //0x000018df cmpq         $32, %r10
	0x0f, 0x82, 0x2c, 0x1a, 0x00, 0x00, //0x000018e3 jb           LBB0_719
	//0x000018e9 LBB0_332
	0xc4, 0xc1, 0x7e, 0x6f, 0x04, 0x24, //0x000018e9 vmovdqu      (%r12), %ymm0
	0xc5, 0xfd, 0x74, 0x0d, 0x29, 0xe7, 0xff, 0xff, //0x000018ef vpcmpeqb     $-6359(%rip), %ymm0, %ymm1  /* LCPI0_1+0(%rip) */
	0xc5, 0xfd, 0xd7, 0xc1, //0x000018f7 vpmovmskb    %ymm1, %eax
	0xc5, 0xfd, 0x74, 0x0d, 0x3d, 0xe7, 0xff, 0xff, //0x000018fb vpcmpeqb     $-6339(%rip), %ymm0, %ymm1  /* LCPI0_2+0(%rip) */
	0xc5, 0xfd, 0xd7, 0xc9, //0x00001903 vpmovmskb    %ymm1, %ecx
	0xc5, 0xfd, 0xda, 0x0d, 0x51, 0xe7, 0xff, 0xff, //0x00001907 vpminub      $-6319(%rip), %ymm0, %ymm1  /* LCPI0_3+0(%rip) */
	0x85, 0xc9, //0x0000190f testl        %ecx, %ecx
	0x0f, 0x85, 0x5e, 0x19, 0x00, 0x00, //0x00001911 jne          LBB0_710
	0x4d, 0x85, 0xff, //0x00001917 testq        %r15, %r15
	0x0f, 0x85, 0x6c, 0x19, 0x00, 0x00, //0x0000191a jne          LBB0_712
	0x45, 0x31, 0xff, //0x00001920 xorl         %r15d, %r15d
	0xc5, 0xfd, 0x74, 0xc1, //0x00001923 vpcmpeqb     %ymm1, %ymm0, %ymm0
	0x48, 0x85, 0xc0, //0x00001927 testq        %rax, %rax
	0x0f, 0x84, 0x9b, 0x19, 0x00, 0x00, //0x0000192a je           LBB0_713
	//0x00001930 LBB0_335
	0x48, 0x0f, 0xbc, 0xc8, //0x00001930 bsfq         %rax, %rcx
	0xe9, 0x97, 0x19, 0x00, 0x00, //0x00001934 jmp          LBB0_714
	//0x00001939 LBB0_336
	0x4c, 0x89, 0xd9, //0x00001939 movq         %r11, %rcx
	//0x0000193c LBB0_337
	0x45, 0x0f, 0xaf, 0xfc, //0x0000193c imull        %r12d, %r15d
	0x41, 0x01, 0xd7, //0x00001940 addl         %edx, %r15d
	0x48, 0x89, 0x4d, 0xa8, //0x00001943 movq         %rcx, $-88(%rbp)
	0xe9, 0x70, 0xf8, 0xff, 0xff, //0x00001947 jmp          LBB0_247
	//0x0000194c LBB0_338
	0x48, 0x83, 0xca, 0x01, //0x0000194c orq          $1, %rdx
	0x48, 0x89, 0xd0, //0x00001950 movq         %rdx, %rax
	//0x00001953 LBB0_339
	0x41, 0xc6, 0x04, 0x00, 0x00, //0x00001953 movb         $0, (%r8,%rax)
	0x48, 0x83, 0xc0, 0x01, //0x00001958 addq         $1, %rax
	0x49, 0x39, 0xc5, //0x0000195c cmpq         %rax, %r13
	0x0f, 0x85, 0xee, 0xff, 0xff, 0xff, //0x0000195f jne          LBB0_339
	//0x00001965 LBB0_340
	0x48, 0x8b, 0x45, 0xc0, //0x00001965 movq         $-64(%rbp), %rax
	0x8a, 0x10, //0x00001969 movb         (%rax), %dl
	0x31, 0xc9, //0x0000196b xorl         %ecx, %ecx
	0x80, 0xfa, 0x2d, //0x0000196d cmpb         $45, %dl
	0x0f, 0x94, 0xc1, //0x00001970 sete         %cl
	0x31, 0xdb, //0x00001973 xorl         %ebx, %ebx
	0x49, 0x39, 0xcc, //0x00001975 cmpq         %rcx, %r12
	0x4c, 0x89, 0x55, 0xa8, //0x00001978 movq         %r10, $-88(%rbp)
	0x0f, 0x8e, 0xb2, 0x00, 0x00, 0x00, //0x0000197c jle          LBB0_353
	0x88, 0x55, 0xd7, //0x00001982 movb         %dl, $-41(%rbp)
	0xb0, 0x01, //0x00001985 movb         $1, %al
	0x45, 0x31, 0xf6, //0x00001987 xorl         %r14d, %r14d
	0x45, 0x31, 0xd2, //0x0000198a xorl         %r10d, %r10d
	0x45, 0x31, 0xff, //0x0000198d xorl         %r15d, %r15d
	0x31, 0xd2, //0x00001990 xorl         %edx, %edx
	0xe9, 0x24, 0x00, 0x00, 0x00, //0x00001992 jmp          LBB0_344
	//0x00001997 LBB0_342
	0xba, 0x01, 0x00, 0x00, 0x00, //0x00001997 movl         $1, %edx
	0x45, 0x89, 0xfb, //0x0000199c movl         %r15d, %r11d
	0x80, 0xfb, 0x2e, //0x0000199f cmpb         $46, %bl
	0x4c, 0x89, 0xdb, //0x000019a2 movq         %r11, %rbx
	0x0f, 0x85, 0x97, 0x00, 0x00, 0x00, //0x000019a5 jne          LBB0_355
	//0x000019ab LBB0_343
	0x48, 0x83, 0xc1, 0x01, //0x000019ab addq         $1, %rcx
	0x4c, 0x39, 0xe1, //0x000019af cmpq         %r12, %rcx
	0x0f, 0x9c, 0xc0, //0x000019b2 setl         %al
	0x0f, 0x84, 0x80, 0x00, 0x00, 0x00, //0x000019b5 je           LBB0_354
	//0x000019bb LBB0_344
	0x89, 0xd6, //0x000019bb movl         %edx, %esi
	0x89, 0xdf, //0x000019bd movl         %ebx, %edi
	0x48, 0x8b, 0x55, 0xc0, //0x000019bf movq         $-64(%rbp), %rdx
	0x0f, 0xb6, 0x1c, 0x0a, //0x000019c3 movzbl       (%rdx,%rcx), %ebx
	0x8d, 0x53, 0xd0, //0x000019c7 leal         $-48(%rbx), %edx
	0x80, 0xfa, 0x09, //0x000019ca cmpb         $9, %dl
	0x0f, 0x87, 0xc4, 0xff, 0xff, 0xff, //0x000019cd ja           LBB0_342
	0x80, 0xfb, 0x30, //0x000019d3 cmpb         $48, %bl
	0x0f, 0x85, 0x1a, 0x00, 0x00, 0x00, //0x000019d6 jne          LBB0_348
	0x45, 0x85, 0xff, //0x000019dc testl        %r15d, %r15d
	0x0f, 0x84, 0x40, 0x00, 0x00, 0x00, //0x000019df je           LBB0_352
	0x49, 0x63, 0xc6, //0x000019e5 movslq       %r14d, %rax
	0x49, 0x39, 0xc5, //0x000019e8 cmpq         %rax, %r13
	0x0f, 0x87, 0x11, 0x00, 0x00, 0x00, //0x000019eb ja           LBB0_349
	0xe9, 0x14, 0x00, 0x00, 0x00, //0x000019f1 jmp          LBB0_350
	//0x000019f6 LBB0_348
	0x49, 0x63, 0xc7, //0x000019f6 movslq       %r15d, %rax
	0x49, 0x39, 0xc5, //0x000019f9 cmpq         %rax, %r13
	0x0f, 0x86, 0x14, 0x00, 0x00, 0x00, //0x000019fc jbe          LBB0_351
	//0x00001a02 LBB0_349
	0x41, 0x88, 0x1c, 0x00, //0x00001a02 movb         %bl, (%r8,%rax)
	0x41, 0x83, 0xc6, 0x01, //0x00001a06 addl         $1, %r14d
	//0x00001a0a LBB0_350
	0x89, 0xfb, //0x00001a0a movl         %edi, %ebx
	0x45, 0x89, 0xf7, //0x00001a0c movl         %r14d, %r15d
	0x89, 0xf2, //0x00001a0f movl         %esi, %edx
	0xe9, 0x95, 0xff, 0xff, 0xff, //0x00001a11 jmp          LBB0_343
	//0x00001a16 LBB0_351
	0x89, 0xfb, //0x00001a16 movl         %edi, %ebx
	0x41, 0xba, 0x01, 0x00, 0x00, 0x00, //0x00001a18 movl         $1, %r10d
	0x89, 0xf2, //0x00001a1e movl         %esi, %edx
	0xe9, 0x86, 0xff, 0xff, 0xff, //0x00001a20 jmp          LBB0_343
	//0x00001a25 LBB0_352
	0x83, 0xc7, 0xff, //0x00001a25 addl         $-1, %edi
	0x45, 0x31, 0xff, //0x00001a28 xorl         %r15d, %r15d
	0x89, 0xfb, //0x00001a2b movl         %edi, %ebx
	0x89, 0xf2, //0x00001a2d movl         %esi, %edx
	0xe9, 0x77, 0xff, 0xff, 0xff, //0x00001a2f jmp          LBB0_343
	//0x00001a34 LBB0_353
	0x31, 0xf6, //0x00001a34 xorl         %esi, %esi
	0xe9, 0xe3, 0x15, 0x00, 0x00, //0x00001a36 jmp          LBB0_677
	//0x00001a3b LBB0_354
	0x89, 0xdf, //0x00001a3b movl         %ebx, %edi
	0x4c, 0x89, 0xe1, //0x00001a3d movq         %r12, %rcx
	0x89, 0xd6, //0x00001a40 movl         %edx, %esi
	//0x00001a42 LBB0_355
	0x85, 0xf6, //0x00001a42 testl        %esi, %esi
	0x41, 0x0f, 0x44, 0xfe, //0x00001a44 cmovel       %r14d, %edi
	0x48, 0x89, 0xfe, //0x00001a48 movq         %rdi, %rsi
	0xa8, 0x01, //0x00001a4b testb        $1, %al
	0x0f, 0x84, 0x38, 0x00, 0x00, 0x00, //0x00001a4d je           LBB0_360
	0x89, 0xc8, //0x00001a53 movl         %ecx, %eax
	0x48, 0x8b, 0x7d, 0xc0, //0x00001a55 movq         $-64(%rbp), %rdi
	0x8a, 0x04, 0x07, //0x00001a59 movb         (%rdi,%rax), %al
	0x0c, 0x20, //0x00001a5c orb          $32, %al
	0x3c, 0x65, //0x00001a5e cmpb         $101, %al
	0x0f, 0x85, 0x25, 0x00, 0x00, 0x00, //0x00001a60 jne          LBB0_360
	0x89, 0xca, //0x00001a66 movl         %ecx, %edx
	0x8a, 0x5c, 0x17, 0x01, //0x00001a68 movb         $1(%rdi,%rdx), %bl
	0x80, 0xfb, 0x2d, //0x00001a6c cmpb         $45, %bl
	0x0f, 0x84, 0x28, 0x00, 0x00, 0x00, //0x00001a6f je           LBB0_361
	0xb8, 0x01, 0x00, 0x00, 0x00, //0x00001a75 movl         $1, %eax
	0x80, 0xfb, 0x2b, //0x00001a7a cmpb         $43, %bl
	0x0f, 0x85, 0x29, 0x00, 0x00, 0x00, //0x00001a7d jne          LBB0_363
	0x83, 0xc1, 0x02, //0x00001a83 addl         $2, %ecx
	0xe9, 0x1a, 0x00, 0x00, 0x00, //0x00001a86 jmp          LBB0_362
	//0x00001a8b LBB0_360
	0x49, 0xbb, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xf0, 0x7f, //0x00001a8b movabsq      $9218868437227405312, %r11
	0x49, 0x89, 0xf4, //0x00001a95 movq         %rsi, %r12
	0xe9, 0x76, 0x00, 0x00, 0x00, //0x00001a98 jmp          LBB0_371
	//0x00001a9d LBB0_361
	0x83, 0xc1, 0x02, //0x00001a9d addl         $2, %ecx
	0xb8, 0xff, 0xff, 0xff, 0xff, //0x00001aa0 movl         $-1, %eax
	//0x00001aa5 LBB0_362
	0x89, 0xca, //0x00001aa5 movl         %ecx, %edx
	0xe9, 0x04, 0x00, 0x00, 0x00, //0x00001aa7 jmp          LBB0_364
	//0x00001aac LBB0_363
	0x48, 0x83, 0xc2, 0x01, //0x00001aac addq         $1, %rdx
	//0x00001ab0 LBB0_364
	0x48, 0x63, 0xd2, //0x00001ab0 movslq       %edx, %rdx
	0x31, 0xc9, //0x00001ab3 xorl         %ecx, %ecx
	0x49, 0x39, 0xd4, //0x00001ab5 cmpq         %rdx, %r12
	0x49, 0xbb, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xf0, 0x7f, //0x00001ab8 movabsq      $9218868437227405312, %r11
	0x0f, 0x8e, 0x43, 0x00, 0x00, 0x00, //0x00001ac2 jle          LBB0_370
	0x49, 0x01, 0xd1, //0x00001ac8 addq         %rdx, %r9
	0x31, 0xc9, //0x00001acb xorl         %ecx, %ecx
	//0x00001acd LBB0_366
	0x48, 0x8b, 0x55, 0xc8, //0x00001acd movq         $-56(%rbp), %rdx
	0x42, 0x0f, 0xbe, 0x14, 0x0a, //0x00001ad1 movsbl       (%rdx,%r9), %edx
	0x83, 0xfa, 0x30, //0x00001ad6 cmpl         $48, %edx
	0x0f, 0x8c, 0x2c, 0x00, 0x00, 0x00, //0x00001ad9 jl           LBB0_370
	0x80, 0xfa, 0x39, //0x00001adf cmpb         $57, %dl
	0x0f, 0x87, 0x23, 0x00, 0x00, 0x00, //0x00001ae2 ja           LBB0_370
	0x81, 0xf9, 0x0f, 0x27, 0x00, 0x00, //0x00001ae8 cmpl         $9999, %ecx
	0x0f, 0x8f, 0x17, 0x00, 0x00, 0x00, //0x00001aee jg           LBB0_370
	0x8d, 0x0c, 0x89, //0x00001af4 leal         (%rcx,%rcx,4), %ecx
	0x8d, 0x0c, 0x4a, //0x00001af7 leal         (%rdx,%rcx,2), %ecx
	0x83, 0xc1, 0xd0, //0x00001afa addl         $-48, %ecx
	0x49, 0x83, 0xc1, 0x01, //0x00001afd addq         $1, %r9
	0x4c, 0x39, 0x4d, 0xa8, //0x00001b01 cmpq         %r9, $-88(%rbp)
	0x0f, 0x85, 0xc2, 0xff, 0xff, 0xff, //0x00001b05 jne          LBB0_366
	//0x00001b0b LBB0_370
	0x0f, 0xaf, 0xc8, //0x00001b0b imull        %eax, %ecx
	0x01, 0xf1, //0x00001b0e addl         %esi, %ecx
	0x41, 0x89, 0xcc, //0x00001b10 movl         %ecx, %r12d
	//0x00001b13 LBB0_371
	0x45, 0x85, 0xf6, //0x00001b13 testl        %r14d, %r14d
	0x0f, 0x84, 0x17, 0x00, 0x00, 0x00, //0x00001b16 je           LBB0_374
	0x31, 0xf6, //0x00001b1c xorl         %esi, %esi
	0x41, 0x81, 0xfc, 0x36, 0x01, 0x00, 0x00, //0x00001b1e cmpl         $310, %r12d
	0x0f, 0x8e, 0x11, 0x00, 0x00, 0x00, //0x00001b25 jle          LBB0_375
	0x4c, 0x89, 0xdb, //0x00001b2b movq         %r11, %rbx
	0xe9, 0xde, 0x14, 0x00, 0x00, //0x00001b2e jmp          LBB0_676
	//0x00001b33 LBB0_374
	0x31, 0xdb, //0x00001b33 xorl         %ebx, %ebx
	0x31, 0xf6, //0x00001b35 xorl         %esi, %esi
	0xe9, 0xd5, 0x14, 0x00, 0x00, //0x00001b37 jmp          LBB0_676
	//0x00001b3c LBB0_375
	0x41, 0x81, 0xfc, 0xb6, 0xfe, 0xff, 0xff, //0x00001b3c cmpl         $-330, %r12d
	0x0f, 0x8d, 0x07, 0x00, 0x00, 0x00, //0x00001b43 jge          LBB0_377
	0x31, 0xdb, //0x00001b49 xorl         %ebx, %ebx
	0xe9, 0xc1, 0x14, 0x00, 0x00, //0x00001b4b jmp          LBB0_676
	//0x00001b50 LBB0_377
	0x49, 0xb9, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x10, //0x00001b50 movabsq      $1152921504606846976, %r9
	0x45, 0x85, 0xe4, //0x00001b5a testl        %r12d, %r12d
	0x0f, 0x8e, 0xf8, 0x03, 0x00, 0x00, //0x00001b5d jle          LBB0_443
	0x45, 0x31, 0xdb, //0x00001b63 xorl         %r11d, %r11d
	0x41, 0xbf, 0x01, 0x00, 0x00, 0x00, //0x00001b66 movl         $1, %r15d
	0x44, 0x89, 0xf7, //0x00001b6c movl         %r14d, %edi
	0x44, 0x89, 0xf6, //0x00001b6f movl         %r14d, %esi
	0xe9, 0x15, 0x00, 0x00, 0x00, //0x00001b72 jmp          LBB0_381
	//0x00001b77 LBB0_379
	0x31, 0xf6, //0x00001b77 xorl         %esi, %esi
	//0x00001b79 LBB0_380
	0x48, 0x8b, 0x55, 0xc8, //0x00001b79 movq         $-56(%rbp), %rdx
	0x44, 0x01, 0xda, //0x00001b7d addl         %r11d, %edx
	0x41, 0x89, 0xd3, //0x00001b80 movl         %edx, %r11d
	0x45, 0x85, 0xe4, //0x00001b83 testl        %r12d, %r12d
	0x0f, 0x8e, 0xd4, 0x03, 0x00, 0x00, //0x00001b86 jle          LBB0_444
	//0x00001b8c LBB0_381
	0x41, 0x83, 0xfc, 0x08, //0x00001b8c cmpl         $8, %r12d
	0x0f, 0x86, 0x1b, 0x00, 0x00, 0x00, //0x00001b90 jbe          LBB0_384
	0xb9, 0xe5, 0xff, 0xff, 0xff, //0x00001b96 movl         $-27, %ecx
	0xb8, 0x1b, 0x00, 0x00, 0x00, //0x00001b9b movl         $27, %eax
	0x48, 0x89, 0x45, 0xc8, //0x00001ba0 movq         %rax, $-56(%rbp)
	0x85, 0xf6, //0x00001ba4 testl        %esi, %esi
	0x0f, 0x85, 0xc2, 0x01, 0x00, 0x00, //0x00001ba6 jne          LBB0_413
	0xe9, 0xc6, 0xff, 0xff, 0xff, //0x00001bac jmp          LBB0_379
	//0x00001bb1 LBB0_384
	0x44, 0x89, 0xe0, //0x00001bb1 movl         %r12d, %eax
	0x48, 0x8d, 0x0d, 0x65, 0x47, 0x00, 0x00, //0x00001bb4 leaq         $18277(%rip), %rcx  /* _POW_TAB+0(%rip) */
	0x8b, 0x0c, 0x81, //0x00001bbb movl         (%rcx,%rax,4), %ecx
	0x85, 0xf6, //0x00001bbe testl        %esi, %esi
	0x48, 0x89, 0x4d, 0xc8, //0x00001bc0 movq         %rcx, $-56(%rbp)
	0x0f, 0x84, 0xad, 0xff, 0xff, 0xff, //0x00001bc4 je           LBB0_379
	0xf7, 0xd9, //0x00001bca negl         %ecx
	0x83, 0xf9, 0xc3, //0x00001bcc cmpl         $-61, %ecx
	0x0f, 0x87, 0x99, 0x01, 0x00, 0x00, //0x00001bcf ja           LBB0_413
	0x4c, 0x89, 0x5d, 0xc0, //0x00001bd5 movq         %r11, $-64(%rbp)
	0xe9, 0x17, 0x00, 0x00, 0x00, //0x00001bd9 jmp          LBB0_389
	//0x00001bde LBB0_411
	0x45, 0x31, 0xe4, //0x00001bde xorl         %r12d, %r12d
	//0x00001be1 LBB0_387
	0x45, 0x31, 0xf6, //0x00001be1 xorl         %r14d, %r14d
	//0x00001be4 LBB0_388
	0x41, 0x8d, 0x4b, 0x3c, //0x00001be4 leal         $60(%r11), %ecx
	0x44, 0x89, 0xf6, //0x00001be8 movl         %r14d, %esi
	0x41, 0x83, 0xfb, 0x88, //0x00001beb cmpl         $-120, %r11d
	0x0f, 0x8d, 0x72, 0x01, 0x00, 0x00, //0x00001bef jge          LBB0_412
	//0x00001bf5 LBB0_389
	0x41, 0x89, 0xcb, //0x00001bf5 movl         %ecx, %r11d
	0x85, 0xf6, //0x00001bf8 testl        %esi, %esi
	0xb9, 0x00, 0x00, 0x00, 0x00, //0x00001bfa movl         $0, %ecx
	0x0f, 0x4f, 0xce, //0x00001bff cmovgl       %esi, %ecx
	0x31, 0xd2, //0x00001c02 xorl         %edx, %edx
	0x31, 0xc0, //0x00001c04 xorl         %eax, %eax
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x00001c06 .p2align 4, 0x90
	//0x00001c10 LBB0_390
	0x48, 0x39, 0xd1, //0x00001c10 cmpq         %rdx, %rcx
	0x0f, 0x84, 0x25, 0x00, 0x00, 0x00, //0x00001c13 je           LBB0_393
	0x48, 0x8d, 0x04, 0x80, //0x00001c19 leaq         (%rax,%rax,4), %rax
	0x49, 0x0f, 0xbe, 0x1c, 0x10, //0x00001c1d movsbq       (%r8,%rdx), %rbx
	0x48, 0x8d, 0x04, 0x43, //0x00001c22 leaq         (%rbx,%rax,2), %rax
	0x48, 0x83, 0xc0, 0xd0, //0x00001c26 addq         $-48, %rax
	0x48, 0x83, 0xc2, 0x01, //0x00001c2a addq         $1, %rdx
	0x4c, 0x39, 0xc8, //0x00001c2e cmpq         %r9, %rax
	0x0f, 0x82, 0xd9, 0xff, 0xff, 0xff, //0x00001c31 jb           LBB0_390
	0x89, 0xd1, //0x00001c37 movl         %edx, %ecx
	0xe9, 0x1c, 0x00, 0x00, 0x00, //0x00001c39 jmp          LBB0_395
	//0x00001c3e LBB0_393
	0x48, 0x85, 0xc0, //0x00001c3e testq        %rax, %rax
	0x0f, 0x84, 0x9a, 0xff, 0xff, 0xff, //0x00001c41 je           LBB0_387
	//0x00001c47 LBB0_394
	0x48, 0x01, 0xc0, //0x00001c47 addq         %rax, %rax
	0x48, 0x8d, 0x04, 0x80, //0x00001c4a leaq         (%rax,%rax,4), %rax
	0x83, 0xc1, 0x01, //0x00001c4e addl         $1, %ecx
	0x4c, 0x39, 0xc8, //0x00001c51 cmpq         %r9, %rax
	0x0f, 0x82, 0xed, 0xff, 0xff, 0xff, //0x00001c54 jb           LBB0_394
	//0x00001c5a LBB0_395
	0x41, 0x29, 0xcc, //0x00001c5a subl         %ecx, %r12d
	0x31, 0xd2, //0x00001c5d xorl         %edx, %edx
	0x39, 0xf1, //0x00001c5f cmpl         %esi, %ecx
	0x0f, 0x8d, 0x56, 0x00, 0x00, 0x00, //0x00001c61 jge          LBB0_400
	0x48, 0x63, 0xc9, //0x00001c67 movslq       %ecx, %rcx
	0x49, 0x63, 0xf6, //0x00001c6a movslq       %r14d, %rsi
	0x49, 0x8d, 0x14, 0x08, //0x00001c6d leaq         (%r8,%rcx), %rdx
	0x45, 0x31, 0xf6, //0x00001c71 xorl         %r14d, %r14d
	//0x00001c74 LBB0_397
	0x49, 0x8d, 0x59, 0xff, //0x00001c74 leaq         $-1(%r9), %rbx
	0x48, 0x21, 0xc3, //0x00001c78 andq         %rax, %rbx
	0x48, 0xc1, 0xe8, 0x3c, //0x00001c7b shrq         $60, %rax
	0x0c, 0x30, //0x00001c7f orb          $48, %al
	0x43, 0x88, 0x04, 0x30, //0x00001c81 movb         %al, (%r8,%r14)
	0x4a, 0x0f, 0xbe, 0x04, 0x32, //0x00001c85 movsbq       (%rdx,%r14), %rax
	0x4a, 0x8d, 0x3c, 0x31, //0x00001c8a leaq         (%rcx,%r14), %rdi
	0x48, 0x83, 0xc7, 0x01, //0x00001c8e addq         $1, %rdi
	0x49, 0x83, 0xc6, 0x01, //0x00001c92 addq         $1, %r14
	0x48, 0x8d, 0x1c, 0x9b, //0x00001c96 leaq         (%rbx,%rbx,4), %rbx
	0x48, 0x8d, 0x04, 0x58, //0x00001c9a leaq         (%rax,%rbx,2), %rax
	0x48, 0x83, 0xc0, 0xd0, //0x00001c9e addq         $-48, %rax
	0x48, 0x39, 0xf7, //0x00001ca2 cmpq         %rsi, %rdi
	0x0f, 0x8c, 0xc9, 0xff, 0xff, 0xff, //0x00001ca5 jl           LBB0_397
	0x48, 0x85, 0xc0, //0x00001cab testq        %rax, %rax
	0x0f, 0x84, 0x62, 0x00, 0x00, 0x00, //0x00001cae je           LBB0_404
	0x44, 0x89, 0xf2, //0x00001cb4 movl         %r14d, %edx
	0x41, 0xbf, 0x01, 0x00, 0x00, 0x00, //0x00001cb7 movl         $1, %r15d
	//0x00001cbd LBB0_400
	0x41, 0x89, 0xd6, //0x00001cbd movl         %edx, %r14d
	0xe9, 0x18, 0x00, 0x00, 0x00, //0x00001cc0 jmp          LBB0_402
	//0x00001cc5 LBB0_401
	0x4c, 0x39, 0xc8, //0x00001cc5 cmpq         %r9, %rax
	0x45, 0x0f, 0x43, 0xd7, //0x00001cc8 cmovael      %r15d, %r10d
	0x48, 0x8d, 0x04, 0x09, //0x00001ccc leaq         (%rcx,%rcx), %rax
	0x48, 0x8d, 0x04, 0x80, //0x00001cd0 leaq         (%rax,%rax,4), %rax
	0x48, 0x85, 0xc9, //0x00001cd4 testq        %rcx, %rcx
	0x0f, 0x84, 0x3f, 0x00, 0x00, 0x00, //0x00001cd7 je           LBB0_405
	//0x00001cdd LBB0_402
	0x49, 0x8d, 0x49, 0xff, //0x00001cdd leaq         $-1(%r9), %rcx
	0x48, 0x21, 0xc1, //0x00001ce1 andq         %rax, %rcx
	0x49, 0x63, 0xd6, //0x00001ce4 movslq       %r14d, %rdx
	0x49, 0x39, 0xd5, //0x00001ce7 cmpq         %rdx, %r13
	0x0f, 0x86, 0xd5, 0xff, 0xff, 0xff, //0x00001cea jbe          LBB0_401
	0x48, 0xc1, 0xe8, 0x3c, //0x00001cf0 shrq         $60, %rax
	0x0c, 0x30, //0x00001cf4 orb          $48, %al
	0x41, 0x88, 0x04, 0x10, //0x00001cf6 movb         %al, (%r8,%rdx)
	0x83, 0xc2, 0x01, //0x00001cfa addl         $1, %edx
	0x41, 0x89, 0xd6, //0x00001cfd movl         %edx, %r14d
	0x48, 0x8d, 0x04, 0x09, //0x00001d00 leaq         (%rcx,%rcx), %rax
	0x48, 0x8d, 0x04, 0x80, //0x00001d04 leaq         (%rax,%rax,4), %rax
	0x48, 0x85, 0xc9, //0x00001d08 testq        %rcx, %rcx
	0x0f, 0x85, 0xcc, 0xff, 0xff, 0xff, //0x00001d0b jne          LBB0_402
	0xe9, 0x06, 0x00, 0x00, 0x00, //0x00001d11 jmp          LBB0_405
	//0x00001d16 LBB0_404
	0x41, 0xbf, 0x01, 0x00, 0x00, 0x00, //0x00001d16 movl         $1, %r15d
	//0x00001d1c LBB0_405
	0x41, 0x83, 0xc4, 0x01, //0x00001d1c addl         $1, %r12d
	0x45, 0x85, 0xf6, //0x00001d20 testl        %r14d, %r14d
	0x0f, 0x8e, 0x33, 0x00, 0x00, 0x00, //0x00001d23 jle          LBB0_410
	0x44, 0x89, 0xf0, //0x00001d29 movl         %r14d, %eax
	0x42, 0x80, 0x7c, 0x00, 0xff, 0x30, //0x00001d2c cmpb         $48, $-1(%rax,%r8)
	0x0f, 0x85, 0xac, 0xfe, 0xff, 0xff, //0x00001d32 jne          LBB0_388
	//0x00001d38 LBB0_407
	0x48, 0x83, 0xf8, 0x01, //0x00001d38 cmpq         $1, %rax
	0x0f, 0x86, 0x9c, 0xfe, 0xff, 0xff, //0x00001d3c jbe          LBB0_411
	0x8d, 0x48, 0xfe, //0x00001d42 leal         $-2(%rax), %ecx
	0x48, 0x83, 0xc0, 0xff, //0x00001d45 addq         $-1, %rax
	0x41, 0x80, 0x3c, 0x08, 0x30, //0x00001d49 cmpb         $48, (%r8,%rcx)
	0x0f, 0x84, 0xe4, 0xff, 0xff, 0xff, //0x00001d4e je           LBB0_407
	0x41, 0x89, 0xc6, //0x00001d54 movl         %eax, %r14d
	0xe9, 0x88, 0xfe, 0xff, 0xff, //0x00001d57 jmp          LBB0_388
	//0x00001d5c LBB0_410
	0x0f, 0x85, 0x82, 0xfe, 0xff, 0xff, //0x00001d5c jne          LBB0_388
	0xe9, 0x77, 0xfe, 0xff, 0xff, //0x00001d62 jmp          LBB0_411
	//0x00001d67 LBB0_412
	0x44, 0x89, 0xf6, //0x00001d67 movl         %r14d, %esi
	0x4c, 0x8b, 0x5d, 0xc0, //0x00001d6a movq         $-64(%rbp), %r11
	//0x00001d6e LBB0_413
	0xf7, 0xd9, //0x00001d6e negl         %ecx
	0x85, 0xf6, //0x00001d70 testl        %esi, %esi
	0x41, 0xbf, 0x00, 0x00, 0x00, 0x00, //0x00001d72 movl         $0, %r15d
	0x44, 0x0f, 0x4f, 0xfe, //0x00001d78 cmovgl       %esi, %r15d
	0x31, 0xd2, //0x00001d7c xorl         %edx, %edx
	0x31, 0xc0, //0x00001d7e xorl         %eax, %eax
	//0x00001d80 LBB0_414
	0x49, 0x39, 0xd7, //0x00001d80 cmpq         %rdx, %r15
	0x0f, 0x84, 0xaf, 0x00, 0x00, 0x00, //0x00001d83 je           LBB0_423
	0x48, 0x8d, 0x04, 0x80, //0x00001d89 leaq         (%rax,%rax,4), %rax
	0x49, 0x0f, 0xbe, 0x3c, 0x10, //0x00001d8d movsbq       (%r8,%rdx), %rdi
	0x48, 0x8d, 0x04, 0x47, //0x00001d92 leaq         (%rdi,%rax,2), %rax
	0x48, 0x83, 0xc0, 0xd0, //0x00001d96 addq         $-48, %rax
	0x48, 0x83, 0xc2, 0x01, //0x00001d9a addq         $1, %rdx
	0x48, 0x89, 0xc7, //0x00001d9e movq         %rax, %rdi
	0x48, 0xd3, 0xef, //0x00001da1 shrq         %cl, %rdi
	0x48, 0x85, 0xff, //0x00001da4 testq        %rdi, %rdi
	0x0f, 0x84, 0xd3, 0xff, 0xff, 0xff, //0x00001da7 je           LBB0_414
	0x4c, 0x89, 0x65, 0xb0, //0x00001dad movq         %r12, $-80(%rbp)
	0x41, 0x89, 0xd7, //0x00001db1 movl         %edx, %r15d
	//0x00001db4 LBB0_417
	0x49, 0xc7, 0xc4, 0xff, 0xff, 0xff, 0xff, //0x00001db4 movq         $-1, %r12
	0x49, 0xd3, 0xe4, //0x00001dbb shlq         %cl, %r12
	0x49, 0xf7, 0xd4, //0x00001dbe notq         %r12
	0x31, 0xff, //0x00001dc1 xorl         %edi, %edi
	0x41, 0x39, 0xf7, //0x00001dc3 cmpl         %esi, %r15d
	0x0f, 0x8d, 0x4a, 0x00, 0x00, 0x00, //0x00001dc6 jge          LBB0_421
	0x4c, 0x89, 0x5d, 0xc0, //0x00001dcc movq         %r11, $-64(%rbp)
	0x49, 0x63, 0xf7, //0x00001dd0 movslq       %r15d, %rsi
	0x4d, 0x63, 0xf6, //0x00001dd3 movslq       %r14d, %r14
	0x49, 0x8d, 0x14, 0x30, //0x00001dd6 leaq         (%r8,%rsi), %rdx
	0x31, 0xff, //0x00001dda xorl         %edi, %edi
	//0x00001ddc LBB0_419
	0x48, 0x89, 0xc3, //0x00001ddc movq         %rax, %rbx
	0x48, 0xd3, 0xeb, //0x00001ddf shrq         %cl, %rbx
	0x4c, 0x21, 0xe0, //0x00001de2 andq         %r12, %rax
	0x80, 0xc3, 0x30, //0x00001de5 addb         $48, %bl
	0x41, 0x88, 0x1c, 0x38, //0x00001de8 movb         %bl, (%r8,%rdi)
	0x48, 0x0f, 0xbe, 0x1c, 0x3a, //0x00001dec movsbq       (%rdx,%rdi), %rbx
	0x4c, 0x8d, 0x1c, 0x3e, //0x00001df1 leaq         (%rsi,%rdi), %r11
	0x49, 0x83, 0xc3, 0x01, //0x00001df5 addq         $1, %r11
	0x48, 0x83, 0xc7, 0x01, //0x00001df9 addq         $1, %rdi
	0x48, 0x8d, 0x04, 0x80, //0x00001dfd leaq         (%rax,%rax,4), %rax
	0x48, 0x8d, 0x04, 0x43, //0x00001e01 leaq         (%rbx,%rax,2), %rax
	0x48, 0x83, 0xc0, 0xd0, //0x00001e05 addq         $-48, %rax
	0x4d, 0x39, 0xf3, //0x00001e09 cmpq         %r14, %r11
	0x0f, 0x8c, 0xca, 0xff, 0xff, 0xff, //0x00001e0c jl           LBB0_419
	0x4c, 0x8b, 0x5d, 0xc0, //0x00001e12 movq         $-64(%rbp), %r11
	//0x00001e16 LBB0_421
	0x48, 0x8b, 0x5d, 0xb0, //0x00001e16 movq         $-80(%rbp), %rbx
	0x44, 0x29, 0xfb, //0x00001e1a subl         %r15d, %ebx
	0x83, 0xc3, 0x01, //0x00001e1d addl         $1, %ebx
	0x48, 0x85, 0xc0, //0x00001e20 testq        %rax, %rax
	0x0f, 0x85, 0x3f, 0x00, 0x00, 0x00, //0x00001e23 jne          LBB0_426
	0x48, 0x8b, 0x4d, 0xc8, //0x00001e29 movq         $-56(%rbp), %rcx
	0x41, 0xbf, 0x01, 0x00, 0x00, 0x00, //0x00001e2d movl         $1, %r15d
	0xe9, 0x87, 0x00, 0x00, 0x00, //0x00001e33 jmp          LBB0_431
	//0x00001e38 LBB0_423
	0x48, 0x85, 0xc0, //0x00001e38 testq        %rax, %rax
	0x0f, 0x84, 0xd0, 0x00, 0x00, 0x00, //0x00001e3b je           LBB0_438
	0x48, 0x89, 0xc2, //0x00001e41 movq         %rax, %rdx
	0x48, 0xd3, 0xea, //0x00001e44 shrq         %cl, %rdx
	0x48, 0x85, 0xd2, //0x00001e47 testq        %rdx, %rdx
	0x0f, 0x84, 0xd3, 0x00, 0x00, 0x00, //0x00001e4a je           LBB0_439
	0x4c, 0x89, 0xe3, //0x00001e50 movq         %r12, %rbx
	0x49, 0xc7, 0xc4, 0xff, 0xff, 0xff, 0xff, //0x00001e53 movq         $-1, %r12
	0x49, 0xd3, 0xe4, //0x00001e5a shlq         %cl, %r12
	0x49, 0xf7, 0xd4, //0x00001e5d notq         %r12
	0x44, 0x29, 0xfb, //0x00001e60 subl         %r15d, %ebx
	0x83, 0xc3, 0x01, //0x00001e63 addl         $1, %ebx
	0x31, 0xff, //0x00001e66 xorl         %edi, %edi
	//0x00001e68 LBB0_426
	0x41, 0xbf, 0x01, 0x00, 0x00, 0x00, //0x00001e68 movl         $1, %r15d
	0xe9, 0x17, 0x00, 0x00, 0x00, //0x00001e6e jmp          LBB0_428
	//0x00001e73 LBB0_427
	0x48, 0x85, 0xd2, //0x00001e73 testq        %rdx, %rdx
	0x45, 0x0f, 0x45, 0xd7, //0x00001e76 cmovnel      %r15d, %r10d
	0x48, 0x01, 0xc0, //0x00001e7a addq         %rax, %rax
	0x48, 0x8d, 0x04, 0x80, //0x00001e7d leaq         (%rax,%rax,4), %rax
	0x48, 0x85, 0xc0, //0x00001e81 testq        %rax, %rax
	0x0f, 0x84, 0x31, 0x00, 0x00, 0x00, //0x00001e84 je           LBB0_430
	//0x00001e8a LBB0_428
	0x48, 0x89, 0xc2, //0x00001e8a movq         %rax, %rdx
	0x48, 0xd3, 0xea, //0x00001e8d shrq         %cl, %rdx
	0x4c, 0x21, 0xe0, //0x00001e90 andq         %r12, %rax
	0x48, 0x63, 0xf7, //0x00001e93 movslq       %edi, %rsi
	0x49, 0x39, 0xf5, //0x00001e96 cmpq         %rsi, %r13
	0x0f, 0x86, 0xd4, 0xff, 0xff, 0xff, //0x00001e99 jbe          LBB0_427
	0x80, 0xc2, 0x30, //0x00001e9f addb         $48, %dl
	0x41, 0x88, 0x14, 0x30, //0x00001ea2 movb         %dl, (%r8,%rsi)
	0x83, 0xc6, 0x01, //0x00001ea6 addl         $1, %esi
	0x89, 0xf7, //0x00001ea9 movl         %esi, %edi
	0x48, 0x01, 0xc0, //0x00001eab addq         %rax, %rax
	0x48, 0x8d, 0x04, 0x80, //0x00001eae leaq         (%rax,%rax,4), %rax
	0x48, 0x85, 0xc0, //0x00001eb2 testq        %rax, %rax
	0x0f, 0x85, 0xcf, 0xff, 0xff, 0xff, //0x00001eb5 jne          LBB0_428
	//0x00001ebb LBB0_430
	0x48, 0x8b, 0x4d, 0xc8, //0x00001ebb movq         $-56(%rbp), %rcx
	//0x00001ebf LBB0_431
	0x85, 0xff, //0x00001ebf testl        %edi, %edi
	0x49, 0x89, 0xdc, //0x00001ec1 movq         %rbx, %r12
	0x0f, 0x8e, 0x37, 0x00, 0x00, 0x00, //0x00001ec4 jle          LBB0_436
	0x41, 0x89, 0xfe, //0x00001eca movl         %edi, %r14d
	0x43, 0x80, 0x7c, 0x06, 0xff, 0x30, //0x00001ecd cmpb         $48, $-1(%r14,%r8)
	0x0f, 0x85, 0x2e, 0x00, 0x00, 0x00, //0x00001ed3 jne          LBB0_437
	//0x00001ed9 LBB0_433
	0x49, 0x83, 0xfe, 0x01, //0x00001ed9 cmpq         $1, %r14
	0x0f, 0x86, 0x63, 0x00, 0x00, 0x00, //0x00001edd jbe          LBB0_441
	0x41, 0x8d, 0x46, 0xfe, //0x00001ee3 leal         $-2(%r14), %eax
	0x49, 0x83, 0xc6, 0xff, //0x00001ee7 addq         $-1, %r14
	0x41, 0x80, 0x3c, 0x00, 0x30, //0x00001eeb cmpb         $48, (%r8,%rax)
	0x0f, 0x84, 0xe3, 0xff, 0xff, 0xff, //0x00001ef0 je           LBB0_433
	0x44, 0x89, 0xf7, //0x00001ef6 movl         %r14d, %edi
	0x44, 0x89, 0xf6, //0x00001ef9 movl         %r14d, %esi
	0xe9, 0x78, 0xfc, 0xff, 0xff, //0x00001efc jmp          LBB0_380
	//0x00001f01 LBB0_436
	0x0f, 0x84, 0xe5, 0x11, 0x00, 0x00, //0x00001f01 je           LBB0_686
	//0x00001f07 LBB0_437
	0x41, 0x89, 0xfe, //0x00001f07 movl         %edi, %r14d
	0x89, 0xfe, //0x00001f0a movl         %edi, %esi
	0xe9, 0x68, 0xfc, 0xff, 0xff, //0x00001f0c jmp          LBB0_380
	//0x00001f11 LBB0_438
	0x45, 0x31, 0xf6, //0x00001f11 xorl         %r14d, %r14d
	0x31, 0xff, //0x00001f14 xorl         %edi, %edi
	0x31, 0xf6, //0x00001f16 xorl         %esi, %esi
	0x41, 0xbf, 0x01, 0x00, 0x00, 0x00, //0x00001f18 movl         $1, %r15d
	0xe9, 0x56, 0xfc, 0xff, 0xff, //0x00001f1e jmp          LBB0_380
	//0x00001f23 LBB0_439
	0x4c, 0x89, 0x65, 0xb0, //0x00001f23 movq         %r12, $-80(%rbp)
	//0x00001f27 LBB0_440
	0x48, 0x01, 0xc0, //0x00001f27 addq         %rax, %rax
	0x48, 0x8d, 0x04, 0x80, //0x00001f2a leaq         (%rax,%rax,4), %rax
	0x41, 0x83, 0xc7, 0x01, //0x00001f2e addl         $1, %r15d
	0x48, 0x89, 0xc2, //0x00001f32 movq         %rax, %rdx
	0x48, 0xd3, 0xea, //0x00001f35 shrq         %cl, %rdx
	0x48, 0x85, 0xd2, //0x00001f38 testq        %rdx, %rdx
	0x0f, 0x84, 0xe6, 0xff, 0xff, 0xff, //0x00001f3b je           LBB0_440
	0xe9, 0x6e, 0xfe, 0xff, 0xff, //0x00001f41 jmp          LBB0_417
	//0x00001f46 LBB0_441
	0x41, 0x83, 0xc6, 0xff, //0x00001f46 addl         $-1, %r14d
	//0x00001f4a LBB0_442
	0x44, 0x01, 0xd9, //0x00001f4a addl         %r11d, %ecx
	0x45, 0x31, 0xe4, //0x00001f4d xorl         %r12d, %r12d
	0x44, 0x89, 0xf7, //0x00001f50 movl         %r14d, %edi
	0x48, 0x89, 0xca, //0x00001f53 movq         %rcx, %rdx
	0xe9, 0x05, 0x00, 0x00, 0x00, //0x00001f56 jmp          LBB0_444
	//0x00001f5b LBB0_443
	0x44, 0x89, 0xf7, //0x00001f5b movl         %r14d, %edi
	0x31, 0xd2, //0x00001f5e xorl         %edx, %edx
	//0x00001f60 LBB0_444
	0x49, 0x8d, 0x40, 0x01, //0x00001f60 leaq         $1(%r8), %rax
	0x48, 0x89, 0x45, 0x90, //0x00001f64 movq         %rax, $-112(%rbp)
	0x41, 0x89, 0xfb, //0x00001f68 movl         %edi, %r11d
	0xe9, 0x19, 0x00, 0x00, 0x00, //0x00001f6b jmp          LBB0_449
	//0x00001f70 LBB0_445
	0x83, 0xc0, 0xff, //0x00001f70 addl         $-1, %eax
	0x41, 0x89, 0xc6, //0x00001f73 movl         %eax, %r14d
	//0x00001f76 LBB0_446
	0x45, 0x31, 0xe4, //0x00001f76 xorl         %r12d, %r12d
	//0x00001f79 LBB0_447
	0x85, 0xf6, //0x00001f79 testl        %esi, %esi
	0x0f, 0x88, 0x06, 0x02, 0x00, 0x00, //0x00001f7b js           LBB0_475
	//0x00001f81 LBB0_448
	0x44, 0x89, 0xf7, //0x00001f81 movl         %r14d, %edi
	0x45, 0x89, 0xf3, //0x00001f84 movl         %r14d, %r11d
	0x29, 0xf2, //0x00001f87 subl         %esi, %edx
	//0x00001f89 LBB0_449
	0x45, 0x85, 0xe4, //0x00001f89 testl        %r12d, %r12d
	0x0f, 0x88, 0x15, 0x00, 0x00, 0x00, //0x00001f8c js           LBB0_452
	0x0f, 0x85, 0xd9, 0x06, 0x00, 0x00, //0x00001f92 jne          LBB0_547
	0x41, 0x80, 0x38, 0x35, //0x00001f98 cmpb         $53, (%r8)
	0x0f, 0x8c, 0x2e, 0x00, 0x00, 0x00, //0x00001f9c jl           LBB0_455
	0xe9, 0xca, 0x06, 0x00, 0x00, //0x00001fa2 jmp          LBB0_547
	//0x00001fa7 LBB0_452
	0x41, 0x83, 0xfc, 0xf8, //0x00001fa7 cmpl         $-8, %r12d
	0x0f, 0x83, 0x1f, 0x00, 0x00, 0x00, //0x00001fab jae          LBB0_455
	0xbe, 0x1b, 0x00, 0x00, 0x00, //0x00001fb1 movl         $27, %esi
	0x85, 0xff, //0x00001fb6 testl        %edi, %edi
	0x0f, 0x84, 0x41, 0x05, 0x00, 0x00, //0x00001fb8 je           LBB0_525
	0x41, 0xbf, 0x01, 0x00, 0x00, 0x00, //0x00001fbe movl         $1, %r15d
	0x48, 0x89, 0x55, 0xc8, //0x00001fc4 movq         %rdx, $-56(%rbp)
	0x41, 0x89, 0xfb, //0x00001fc8 movl         %edi, %r11d
	0xe9, 0x22, 0x00, 0x00, 0x00, //0x00001fcb jmp          LBB0_457
	//0x00001fd0 LBB0_455
	0x44, 0x89, 0xe0, //0x00001fd0 movl         %r12d, %eax
	0xf7, 0xd8, //0x00001fd3 negl         %eax
	0x48, 0x8d, 0x0d, 0x44, 0x43, 0x00, 0x00, //0x00001fd5 leaq         $17220(%rip), %rcx  /* _POW_TAB+0(%rip) */
	0x8b, 0x34, 0x81, //0x00001fdc movl         (%rcx,%rax,4), %esi
	0x45, 0x85, 0xdb, //0x00001fdf testl        %r11d, %r11d
	0x0f, 0x84, 0x95, 0x01, 0x00, 0x00, //0x00001fe2 je           LBB0_474
	0x41, 0xbf, 0x01, 0x00, 0x00, 0x00, //0x00001fe8 movl         $1, %r15d
	0x48, 0x89, 0x55, 0xc8, //0x00001fee movq         %rdx, $-56(%rbp)
	//0x00001ff2 LBB0_457
	0x89, 0x75, 0xa4, //0x00001ff2 movl         %esi, $-92(%rbp)
	0x89, 0xf1, //0x00001ff5 movl         %esi, %ecx
	0x48, 0x6b, 0xd1, 0x68, //0x00001ff7 imulq        $104, %rcx, %rdx
	0x48, 0x8d, 0x05, 0x4e, 0x43, 0x00, 0x00, //0x00001ffb leaq         $17230(%rip), %rax  /* _LSHIFT_TAB+0(%rip) */
	0x8b, 0x34, 0x02, //0x00002002 movl         (%rdx,%rax), %esi
	0x48, 0x89, 0x75, 0xc0, //0x00002005 movq         %rsi, $-64(%rbp)
	0x49, 0x63, 0xf3, //0x00002009 movslq       %r11d, %rsi
	0x48, 0x8d, 0x3c, 0x02, //0x0000200c leaq         (%rdx,%rax), %rdi
	0x48, 0x83, 0xc7, 0x04, //0x00002010 addq         $4, %rdi
	0x31, 0xdb, //0x00002014 xorl         %ebx, %ebx
	0x4c, 0x89, 0x65, 0xb0, //0x00002016 movq         %r12, $-80(%rbp)
	//0x0000201a LBB0_458
	0x0f, 0xb6, 0x04, 0x1f, //0x0000201a movzbl       (%rdi,%rbx), %eax
	0x84, 0xc0, //0x0000201e testb        %al, %al
	0x0f, 0x84, 0x3a, 0x00, 0x00, 0x00, //0x00002020 je           LBB0_463
	0x41, 0x38, 0x04, 0x18, //0x00002026 cmpb         %al, (%r8,%rbx)
	0x0f, 0x85, 0xed, 0x03, 0x00, 0x00, //0x0000202a jne          LBB0_512
	0x48, 0x83, 0xc3, 0x01, //0x00002030 addq         $1, %rbx
	0x48, 0x39, 0xde, //0x00002034 cmpq         %rbx, %rsi
	0x0f, 0x85, 0xdd, 0xff, 0xff, 0xff, //0x00002037 jne          LBB0_458
	0x44, 0x89, 0xd8, //0x0000203d movl         %r11d, %eax
	0x48, 0x8d, 0x35, 0x09, 0x43, 0x00, 0x00, //0x00002040 leaq         $17161(%rip), %rsi  /* _LSHIFT_TAB+0(%rip) */
	0x48, 0x01, 0xf2, //0x00002047 addq         %rsi, %rdx
	0x80, 0x7c, 0x10, 0x04, 0x00, //0x0000204a cmpb         $0, $4(%rax,%rdx)
	0x0f, 0x84, 0x0b, 0x00, 0x00, 0x00, //0x0000204f je           LBB0_463
	//0x00002055 LBB0_462
	0x48, 0x8b, 0x45, 0xc0, //0x00002055 movq         $-64(%rbp), %rax
	0x83, 0xc0, 0xff, //0x00002059 addl         $-1, %eax
	0x48, 0x89, 0x45, 0xc0, //0x0000205c movq         %rax, $-64(%rbp)
	//0x00002060 LBB0_463
	0x45, 0x85, 0xdb, //0x00002060 testl        %r11d, %r11d
	0x0f, 0x8e, 0x3f, 0x03, 0x00, 0x00, //0x00002063 jle          LBB0_504
	0x48, 0x8b, 0x45, 0xc0, //0x00002069 movq         $-64(%rbp), %rax
	0x44, 0x01, 0xd8, //0x0000206d addl         %r11d, %eax
	0x44, 0x89, 0xdb, //0x00002070 movl         %r11d, %ebx
	0x48, 0x98, //0x00002073 cltq         
	0x49, 0x89, 0xc4, //0x00002075 movq         %rax, %r12
	0x49, 0xc1, 0xe4, 0x20, //0x00002078 shlq         $32, %r12
	0x48, 0x83, 0xc0, 0xff, //0x0000207c addq         $-1, %rax
	0x48, 0x83, 0xc3, 0x01, //0x00002080 addq         $1, %rbx
	0x31, 0xf6, //0x00002084 xorl         %esi, %esi
	0xe9, 0x26, 0x00, 0x00, 0x00, //0x00002086 jmp          LBB0_467
	//0x0000208b LBB0_465
	0x48, 0x85, 0xc0, //0x0000208b testq        %rax, %rax
	0x45, 0x0f, 0x45, 0xd7, //0x0000208e cmovnel      %r15d, %r10d
	//0x00002092 LBB0_466
	0x48, 0xb8, 0x00, 0x00, 0x00, 0x00, 0xff, 0xff, 0xff, 0xff, //0x00002092 movabsq      $-4294967296, %rax
	0x49, 0x01, 0xc4, //0x0000209c addq         %rax, %r12
	0x49, 0x8d, 0x43, 0xff, //0x0000209f leaq         $-1(%r11), %rax
	0x48, 0x83, 0xc3, 0xff, //0x000020a3 addq         $-1, %rbx
	0x48, 0x83, 0xfb, 0x01, //0x000020a7 cmpq         $1, %rbx
	0x0f, 0x86, 0x4e, 0x00, 0x00, 0x00, //0x000020ab jbe          LBB0_469
	//0x000020b1 LBB0_467
	0x49, 0x89, 0xc3, //0x000020b1 movq         %rax, %r11
	0x8d, 0x43, 0xfe, //0x000020b4 leal         $-2(%rbx), %eax
	0x49, 0x0f, 0xbe, 0x3c, 0x00, //0x000020b7 movsbq       (%r8,%rax), %rdi
	0x48, 0x83, 0xc7, 0xd0, //0x000020bc addq         $-48, %rdi
	0x48, 0xd3, 0xe7, //0x000020c0 shlq         %cl, %rdi
	0x48, 0x01, 0xf7, //0x000020c3 addq         %rsi, %rdi
	0x48, 0x89, 0xf8, //0x000020c6 movq         %rdi, %rax
	0x48, 0xba, 0xcd, 0xcc, 0xcc, 0xcc, 0xcc, 0xcc, 0xcc, 0xcc, //0x000020c9 movabsq      $-3689348814741910323, %rdx
	0x48, 0xf7, 0xe2, //0x000020d3 mulq         %rdx
	0x48, 0x89, 0xd6, //0x000020d6 movq         %rdx, %rsi
	0x48, 0xc1, 0xee, 0x03, //0x000020d9 shrq         $3, %rsi
	0x48, 0x8d, 0x04, 0x36, //0x000020dd leaq         (%rsi,%rsi), %rax
	0x48, 0x8d, 0x14, 0x80, //0x000020e1 leaq         (%rax,%rax,4), %rdx
	0x48, 0x89, 0xf8, //0x000020e5 movq         %rdi, %rax
	0x48, 0x29, 0xd0, //0x000020e8 subq         %rdx, %rax
	0x4d, 0x39, 0xeb, //0x000020eb cmpq         %r13, %r11
	0x0f, 0x83, 0x97, 0xff, 0xff, 0xff, //0x000020ee jae          LBB0_465
	0x04, 0x30, //0x000020f4 addb         $48, %al
	0x43, 0x88, 0x04, 0x18, //0x000020f6 movb         %al, (%r8,%r11)
	0xe9, 0x93, 0xff, 0xff, 0xff, //0x000020fa jmp          LBB0_466
	//0x000020ff LBB0_469
	0x48, 0x83, 0xff, 0x0a, //0x000020ff cmpq         $10, %rdi
	0x4c, 0x8b, 0x65, 0xb0, //0x00002103 movq         $-80(%rbp), %r12
	0xbb, 0x01, 0x00, 0x00, 0x00, //0x00002107 movl         $1, %ebx
	0x0f, 0x82, 0x9f, 0x02, 0x00, 0x00, //0x0000210c jb           LBB0_505
	0x49, 0x63, 0xcb, //0x00002112 movslq       %r11d, %rcx
	0x48, 0x83, 0xc1, 0xff, //0x00002115 addq         $-1, %rcx
	0xe9, 0x18, 0x00, 0x00, 0x00, //0x00002119 jmp          LBB0_472
	//0x0000211e LBB0_471
	0x48, 0x85, 0xc0, //0x0000211e testq        %rax, %rax
	0x44, 0x0f, 0x45, 0xd3, //0x00002121 cmovnel      %ebx, %r10d
	0x48, 0x83, 0xc1, 0xff, //0x00002125 addq         $-1, %rcx
	0x48, 0x83, 0xfe, 0x09, //0x00002129 cmpq         $9, %rsi
	0x48, 0x89, 0xd6, //0x0000212d movq         %rdx, %rsi
	0x0f, 0x86, 0x7b, 0x02, 0x00, 0x00, //0x00002130 jbe          LBB0_505
	//0x00002136 LBB0_472
	0x48, 0x89, 0xf0, //0x00002136 movq         %rsi, %rax
	0x48, 0xba, 0xcd, 0xcc, 0xcc, 0xcc, 0xcc, 0xcc, 0xcc, 0xcc, //0x00002139 movabsq      $-3689348814741910323, %rdx
	0x48, 0xf7, 0xe2, //0x00002143 mulq         %rdx
	0x48, 0xc1, 0xea, 0x03, //0x00002146 shrq         $3, %rdx
	0x48, 0x8d, 0x04, 0x12, //0x0000214a leaq         (%rdx,%rdx), %rax
	0x48, 0x8d, 0x3c, 0x80, //0x0000214e leaq         (%rax,%rax,4), %rdi
	0x48, 0x89, 0xf0, //0x00002152 movq         %rsi, %rax
	0x48, 0x29, 0xf8, //0x00002155 subq         %rdi, %rax
	0x4c, 0x39, 0xe9, //0x00002158 cmpq         %r13, %rcx
	0x0f, 0x83, 0xbd, 0xff, 0xff, 0xff, //0x0000215b jae          LBB0_471
	0x04, 0x30, //0x00002161 addb         $48, %al
	0x41, 0x88, 0x04, 0x08, //0x00002163 movb         %al, (%r8,%rcx)
	0x48, 0x83, 0xc1, 0xff, //0x00002167 addq         $-1, %rcx
	0x48, 0x83, 0xfe, 0x09, //0x0000216b cmpq         $9, %rsi
	0x48, 0x89, 0xd6, //0x0000216f movq         %rdx, %rsi
	0x0f, 0x87, 0xbe, 0xff, 0xff, 0xff, //0x00002172 ja           LBB0_472
	0xe9, 0x34, 0x02, 0x00, 0x00, //0x00002178 jmp          LBB0_505
	//0x0000217d LBB0_474
	0x45, 0x31, 0xdb, //0x0000217d xorl         %r11d, %r11d
	0x29, 0xf2, //0x00002180 subl         %esi, %edx
	0xe9, 0x02, 0xfe, 0xff, 0xff, //0x00002182 jmp          LBB0_449
	//0x00002187 LBB0_475
	0x48, 0x89, 0x55, 0xc8, //0x00002187 movq         %rdx, $-56(%rbp)
	0x83, 0xfe, 0xc3, //0x0000218b cmpl         $-61, %esi
	0x0f, 0x87, 0x94, 0x02, 0x00, 0x00, //0x0000218e ja           LBB0_513
	0x41, 0x89, 0xf3, //0x00002194 movl         %esi, %r11d
	0xe9, 0x1a, 0x00, 0x00, 0x00, //0x00002197 jmp          LBB0_479
	//0x0000219c LBB0_503
	0x45, 0x31, 0xe4, //0x0000219c xorl         %r12d, %r12d
	//0x0000219f LBB0_477
	0x45, 0x31, 0xff, //0x0000219f xorl         %r15d, %r15d
	//0x000021a2 LBB0_478
	0x41, 0x8d, 0x4b, 0x3c, //0x000021a2 leal         $60(%r11), %ecx
	0x45, 0x89, 0xfe, //0x000021a6 movl         %r15d, %r14d
	0x41, 0x83, 0xfb, 0x88, //0x000021a9 cmpl         $-120, %r11d
	0x41, 0x89, 0xcb, //0x000021ad movl         %ecx, %r11d
	0x0f, 0x8d, 0x77, 0x02, 0x00, 0x00, //0x000021b0 jge          LBB0_514
	//0x000021b6 LBB0_479
	0x45, 0x85, 0xf6, //0x000021b6 testl        %r14d, %r14d
	0xbe, 0x00, 0x00, 0x00, 0x00, //0x000021b9 movl         $0, %esi
	0x41, 0x0f, 0x4f, 0xf6, //0x000021be cmovgl       %r14d, %esi
	0x31, 0xc0, //0x000021c2 xorl         %eax, %eax
	0x31, 0xc9, //0x000021c4 xorl         %ecx, %ecx
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x000021c6 .p2align 4, 0x90
	//0x000021d0 LBB0_480
	0x48, 0x39, 0xc6, //0x000021d0 cmpq         %rax, %rsi
	0x0f, 0x84, 0x25, 0x00, 0x00, 0x00, //0x000021d3 je           LBB0_483
	0x48, 0x8d, 0x0c, 0x89, //0x000021d9 leaq         (%rcx,%rcx,4), %rcx
	0x49, 0x0f, 0xbe, 0x14, 0x00, //0x000021dd movsbq       (%r8,%rax), %rdx
	0x48, 0x8d, 0x0c, 0x4a, //0x000021e2 leaq         (%rdx,%rcx,2), %rcx
	0x48, 0x83, 0xc1, 0xd0, //0x000021e6 addq         $-48, %rcx
	0x48, 0x83, 0xc0, 0x01, //0x000021ea addq         $1, %rax
	0x4c, 0x39, 0xc9, //0x000021ee cmpq         %r9, %rcx
	0x0f, 0x82, 0xd9, 0xff, 0xff, 0xff, //0x000021f1 jb           LBB0_480
	0x89, 0xc6, //0x000021f7 movl         %eax, %esi
	0xe9, 0x25, 0x00, 0x00, 0x00, //0x000021f9 jmp          LBB0_485
	//0x000021fe LBB0_483
	0x48, 0x85, 0xc9, //0x000021fe testq        %rcx, %rcx
	0x0f, 0x84, 0x98, 0xff, 0xff, 0xff, //0x00002201 je           LBB0_477
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x00002207 .p2align 4, 0x90
	//0x00002210 LBB0_484
	0x48, 0x01, 0xc9, //0x00002210 addq         %rcx, %rcx
	0x48, 0x8d, 0x0c, 0x89, //0x00002213 leaq         (%rcx,%rcx,4), %rcx
	0x83, 0xc6, 0x01, //0x00002217 addl         $1, %esi
	0x4c, 0x39, 0xc9, //0x0000221a cmpq         %r9, %rcx
	0x0f, 0x82, 0xed, 0xff, 0xff, 0xff, //0x0000221d jb           LBB0_484
	//0x00002223 LBB0_485
	0x41, 0x29, 0xf4, //0x00002223 subl         %esi, %r12d
	0x45, 0x31, 0xff, //0x00002226 xorl         %r15d, %r15d
	0x44, 0x39, 0xf6, //0x00002229 cmpl         %r14d, %esi
	0x0f, 0x8d, 0xf6, 0x00, 0x00, 0x00, //0x0000222c jge          LBB0_495
	0x48, 0x63, 0xf6, //0x00002232 movslq       %esi, %rsi
	0x49, 0x63, 0xc6, //0x00002235 movslq       %r14d, %rax
	0x49, 0x89, 0xc7, //0x00002238 movq         %rax, %r15
	0x49, 0x29, 0xf7, //0x0000223b subq         %rsi, %r15
	0x48, 0x89, 0xf7, //0x0000223e movq         %rsi, %rdi
	0x48, 0xf7, 0xd7, //0x00002241 notq         %rdi
	0x48, 0x01, 0xc7, //0x00002244 addq         %rax, %rdi
	0x0f, 0x85, 0x07, 0x00, 0x00, 0x00, //0x00002247 jne          LBB0_488
	0x31, 0xff, //0x0000224d xorl         %edi, %edi
	0xe9, 0x7e, 0x00, 0x00, 0x00, //0x0000224f jmp          LBB0_491
	//0x00002254 LBB0_488
	0x4d, 0x89, 0xfe, //0x00002254 movq         %r15, %r14
	0x49, 0x83, 0xe6, 0xfe, //0x00002257 andq         $-2, %r14
	0x49, 0xf7, 0xde, //0x0000225b negq         %r14
	0x31, 0xff, //0x0000225e xorl         %edi, %edi
	0x48, 0x8b, 0x45, 0x90, //0x00002260 movq         $-112(%rbp), %rax
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x00002264 .p2align 4, 0x90
	//0x00002270 LBB0_489
	0x48, 0x89, 0xcb, //0x00002270 movq         %rcx, %rbx
	0x48, 0xc1, 0xeb, 0x3c, //0x00002273 shrq         $60, %rbx
	0x49, 0x8d, 0x51, 0xff, //0x00002277 leaq         $-1(%r9), %rdx
	0x48, 0x21, 0xd1, //0x0000227b andq         %rdx, %rcx
	0x80, 0xcb, 0x30, //0x0000227e orb          $48, %bl
	0x88, 0x58, 0xff, //0x00002281 movb         %bl, $-1(%rax)
	0x48, 0x8d, 0x0c, 0x89, //0x00002284 leaq         (%rcx,%rcx,4), %rcx
	0x48, 0x0f, 0xbe, 0x5c, 0x30, 0xff, //0x00002288 movsbq       $-1(%rax,%rsi), %rbx
	0x48, 0x8d, 0x0c, 0x4b, //0x0000228e leaq         (%rbx,%rcx,2), %rcx
	0x48, 0x83, 0xc1, 0xd0, //0x00002292 addq         $-48, %rcx
	0x48, 0x89, 0xcb, //0x00002296 movq         %rcx, %rbx
	0x48, 0xc1, 0xeb, 0x3c, //0x00002299 shrq         $60, %rbx
	0x48, 0x21, 0xd1, //0x0000229d andq         %rdx, %rcx
	0x80, 0xcb, 0x30, //0x000022a0 orb          $48, %bl
	0x88, 0x18, //0x000022a3 movb         %bl, (%rax)
	0x48, 0x8d, 0x0c, 0x89, //0x000022a5 leaq         (%rcx,%rcx,4), %rcx
	0x48, 0x0f, 0xbe, 0x14, 0x30, //0x000022a9 movsbq       (%rax,%rsi), %rdx
	0x48, 0x8d, 0x0c, 0x4a, //0x000022ae leaq         (%rdx,%rcx,2), %rcx
	0x48, 0x83, 0xc1, 0xd0, //0x000022b2 addq         $-48, %rcx
	0x48, 0x83, 0xc0, 0x02, //0x000022b6 addq         $2, %rax
	0x48, 0x83, 0xc7, 0xfe, //0x000022ba addq         $-2, %rdi
	0x49, 0x39, 0xfe, //0x000022be cmpq         %rdi, %r14
	0x0f, 0x85, 0xa9, 0xff, 0xff, 0xff, //0x000022c1 jne          LBB0_489
	0x48, 0x29, 0xfe, //0x000022c7 subq         %rdi, %rsi
	0x48, 0xf7, 0xdf, //0x000022ca negq         %rdi
	0xbb, 0x01, 0x00, 0x00, 0x00, //0x000022cd movl         $1, %ebx
	//0x000022d2 LBB0_491
	0x41, 0xf6, 0xc7, 0x01, //0x000022d2 testb        $1, %r15b
	0x0f, 0x84, 0x23, 0x00, 0x00, 0x00, //0x000022d6 je           LBB0_493
	0x49, 0x8d, 0x41, 0xff, //0x000022dc leaq         $-1(%r9), %rax
	0x48, 0x21, 0xc8, //0x000022e0 andq         %rcx, %rax
	0x48, 0xc1, 0xe9, 0x3c, //0x000022e3 shrq         $60, %rcx
	0x80, 0xc9, 0x30, //0x000022e7 orb          $48, %cl
	0x41, 0x88, 0x0c, 0x38, //0x000022ea movb         %cl, (%r8,%rdi)
	0x48, 0x8d, 0x04, 0x80, //0x000022ee leaq         (%rax,%rax,4), %rax
	0x49, 0x0f, 0xbe, 0x0c, 0x30, //0x000022f2 movsbq       (%r8,%rsi), %rcx
	0x48, 0x8d, 0x0c, 0x41, //0x000022f7 leaq         (%rcx,%rax,2), %rcx
	0x48, 0x83, 0xc1, 0xd0, //0x000022fb addq         $-48, %rcx
	//0x000022ff LBB0_493
	0x48, 0x85, 0xc9, //0x000022ff testq        %rcx, %rcx
	0x0f, 0x85, 0x20, 0x00, 0x00, 0x00, //0x00002302 jne          LBB0_495
	0xe9, 0x50, 0x00, 0x00, 0x00, //0x00002308 jmp          LBB0_497
	0x90, 0x90, 0x90, //0x0000230d .p2align 4, 0x90
	//0x00002310 LBB0_494
	0x4c, 0x39, 0xc9, //0x00002310 cmpq         %r9, %rcx
	0x44, 0x0f, 0x43, 0xd3, //0x00002313 cmovael      %ebx, %r10d
	0x48, 0x8d, 0x0c, 0x00, //0x00002317 leaq         (%rax,%rax), %rcx
	0x48, 0x8d, 0x0c, 0x89, //0x0000231b leaq         (%rcx,%rcx,4), %rcx
	0x48, 0x85, 0xc0, //0x0000231f testq        %rax, %rax
	0x0f, 0x84, 0x35, 0x00, 0x00, 0x00, //0x00002322 je           LBB0_497
	//0x00002328 LBB0_495
	0x49, 0x8d, 0x41, 0xff, //0x00002328 leaq         $-1(%r9), %rax
	0x48, 0x21, 0xc8, //0x0000232c andq         %rcx, %rax
	0x49, 0x63, 0xf7, //0x0000232f movslq       %r15d, %rsi
	0x49, 0x39, 0xf5, //0x00002332 cmpq         %rsi, %r13
	0x0f, 0x86, 0xd5, 0xff, 0xff, 0xff, //0x00002335 jbe          LBB0_494
	0x48, 0xc1, 0xe9, 0x3c, //0x0000233b shrq         $60, %rcx
	0x80, 0xc9, 0x30, //0x0000233f orb          $48, %cl
	0x41, 0x88, 0x0c, 0x30, //0x00002342 movb         %cl, (%r8,%rsi)
	0x83, 0xc6, 0x01, //0x00002346 addl         $1, %esi
	0x41, 0x89, 0xf7, //0x00002349 movl         %esi, %r15d
	0x48, 0x8d, 0x0c, 0x00, //0x0000234c leaq         (%rax,%rax), %rcx
	0x48, 0x8d, 0x0c, 0x89, //0x00002350 leaq         (%rcx,%rcx,4), %rcx
	0x48, 0x85, 0xc0, //0x00002354 testq        %rax, %rax
	0x0f, 0x85, 0xcb, 0xff, 0xff, 0xff, //0x00002357 jne          LBB0_495
	//0x0000235d LBB0_497
	0x41, 0x83, 0xc4, 0x01, //0x0000235d addl         $1, %r12d
	0x45, 0x85, 0xff, //0x00002361 testl        %r15d, %r15d
	0x0f, 0x8e, 0x33, 0x00, 0x00, 0x00, //0x00002364 jle          LBB0_502
	0x44, 0x89, 0xf8, //0x0000236a movl         %r15d, %eax
	0x42, 0x80, 0x7c, 0x00, 0xff, 0x30, //0x0000236d cmpb         $48, $-1(%rax,%r8)
	0x0f, 0x85, 0x29, 0xfe, 0xff, 0xff, //0x00002373 jne          LBB0_478
	//0x00002379 LBB0_499
	0x48, 0x83, 0xf8, 0x01, //0x00002379 cmpq         $1, %rax
	0x0f, 0x86, 0x19, 0xfe, 0xff, 0xff, //0x0000237d jbe          LBB0_503
	0x8d, 0x48, 0xfe, //0x00002383 leal         $-2(%rax), %ecx
	0x48, 0x83, 0xc0, 0xff, //0x00002386 addq         $-1, %rax
	0x41, 0x80, 0x3c, 0x08, 0x30, //0x0000238a cmpb         $48, (%r8,%rcx)
	0x0f, 0x84, 0xe4, 0xff, 0xff, 0xff, //0x0000238f je           LBB0_499
	0x41, 0x89, 0xc7, //0x00002395 movl         %eax, %r15d
	0xe9, 0x05, 0xfe, 0xff, 0xff, //0x00002398 jmp          LBB0_478
	//0x0000239d LBB0_502
	0x0f, 0x85, 0xff, 0xfd, 0xff, 0xff, //0x0000239d jne          LBB0_478
	0xe9, 0xf4, 0xfd, 0xff, 0xff, //0x000023a3 jmp          LBB0_503
	//0x000023a8 LBB0_504
	0x4c, 0x8b, 0x65, 0xb0, //0x000023a8 movq         $-80(%rbp), %r12
	0xbb, 0x01, 0x00, 0x00, 0x00, //0x000023ac movl         $1, %ebx
	//0x000023b1 LBB0_505
	0x48, 0x8b, 0x45, 0xc0, //0x000023b1 movq         $-64(%rbp), %rax
	0x41, 0x01, 0xc6, //0x000023b5 addl         %eax, %r14d
	0x4d, 0x63, 0xf6, //0x000023b8 movslq       %r14d, %r14
	0x4d, 0x39, 0xf5, //0x000023bb cmpq         %r14, %r13
	0x45, 0x0f, 0x46, 0xf5, //0x000023be cmovbel      %r13d, %r14d
	0x41, 0x01, 0xc4, //0x000023c2 addl         %eax, %r12d
	0x45, 0x85, 0xf6, //0x000023c5 testl        %r14d, %r14d
	0x0f, 0x8e, 0x3a, 0x00, 0x00, 0x00, //0x000023c8 jle          LBB0_510
	0x44, 0x89, 0xf0, //0x000023ce movl         %r14d, %eax
	0x42, 0x80, 0x7c, 0x00, 0xff, 0x30, //0x000023d1 cmpb         $48, $-1(%rax,%r8)
	0x48, 0x8b, 0x55, 0xc8, //0x000023d7 movq         $-56(%rbp), %rdx
	0x8b, 0x75, 0xa4, //0x000023db movl         $-92(%rbp), %esi
	0x0f, 0x85, 0x95, 0xfb, 0xff, 0xff, //0x000023de jne          LBB0_447
	//0x000023e4 LBB0_507
	0x48, 0x83, 0xf8, 0x01, //0x000023e4 cmpq         $1, %rax
	0x0f, 0x86, 0x82, 0xfb, 0xff, 0xff, //0x000023e8 jbe          LBB0_445
	0x8d, 0x48, 0xfe, //0x000023ee leal         $-2(%rax), %ecx
	0x48, 0x83, 0xc0, 0xff, //0x000023f1 addq         $-1, %rax
	0x41, 0x80, 0x3c, 0x08, 0x30, //0x000023f5 cmpb         $48, (%r8,%rcx)
	0x0f, 0x84, 0xe4, 0xff, 0xff, 0xff, //0x000023fa je           LBB0_507
	0x41, 0x89, 0xc6, //0x00002400 movl         %eax, %r14d
	0xe9, 0x71, 0xfb, 0xff, 0xff, //0x00002403 jmp          LBB0_447
	//0x00002408 LBB0_510
	0x48, 0x8b, 0x55, 0xc8, //0x00002408 movq         $-56(%rbp), %rdx
	0x8b, 0x75, 0xa4, //0x0000240c movl         $-92(%rbp), %esi
	0x0f, 0x85, 0x64, 0xfb, 0xff, 0xff, //0x0000240f jne          LBB0_447
	0x45, 0x31, 0xf6, //0x00002415 xorl         %r14d, %r14d
	0xe9, 0x59, 0xfb, 0xff, 0xff, //0x00002418 jmp          LBB0_446
	//0x0000241d LBB0_512
	0x0f, 0x8c, 0x32, 0xfc, 0xff, 0xff, //0x0000241d jl           LBB0_462
	0xe9, 0x38, 0xfc, 0xff, 0xff, //0x00002423 jmp          LBB0_463
	//0x00002428 LBB0_513
	0x45, 0x89, 0xf7, //0x00002428 movl         %r14d, %r15d
	0x89, 0xf1, //0x0000242b movl         %esi, %ecx
	//0x0000242d LBB0_514
	0xf7, 0xd9, //0x0000242d negl         %ecx
	0x45, 0x85, 0xff, //0x0000242f testl        %r15d, %r15d
	0xbe, 0x00, 0x00, 0x00, 0x00, //0x00002432 movl         $0, %esi
	0x41, 0x0f, 0x4f, 0xf7, //0x00002437 cmovgl       %r15d, %esi
	0x31, 0xff, //0x0000243b xorl         %edi, %edi
	0x31, 0xc0, //0x0000243d xorl         %eax, %eax
	//0x0000243f LBB0_515
	0x48, 0x39, 0xfe, //0x0000243f cmpq         %rdi, %rsi
	0x0f, 0x84, 0x65, 0x00, 0x00, 0x00, //0x00002442 je           LBB0_521
	0x48, 0x8d, 0x04, 0x80, //0x00002448 leaq         (%rax,%rax,4), %rax
	0x49, 0x0f, 0xbe, 0x14, 0x38, //0x0000244c movsbq       (%r8,%rdi), %rdx
	0x48, 0x8d, 0x04, 0x42, //0x00002451 leaq         (%rdx,%rax,2), %rax
	0x48, 0x83, 0xc0, 0xd0, //0x00002455 addq         $-48, %rax
	0x48, 0x83, 0xc7, 0x01, //0x00002459 addq         $1, %rdi
	0x48, 0x89, 0xc2, //0x0000245d movq         %rax, %rdx
	0x48, 0xd3, 0xea, //0x00002460 shrq         %cl, %rdx
	0x48, 0x85, 0xd2, //0x00002463 testq        %rdx, %rdx
	0x0f, 0x84, 0xd3, 0xff, 0xff, 0xff, //0x00002466 je           LBB0_515
	0x89, 0xfe, //0x0000246c movl         %edi, %esi
	//0x0000246e LBB0_518
	0x49, 0xc7, 0xc3, 0xff, 0xff, 0xff, 0xff, //0x0000246e movq         $-1, %r11
	0x49, 0xd3, 0xe3, //0x00002475 shlq         %cl, %r11
	0x49, 0xf7, 0xd3, //0x00002478 notq         %r11
	0x45, 0x31, 0xf6, //0x0000247b xorl         %r14d, %r14d
	0x44, 0x39, 0xfe, //0x0000247e cmpl         %r15d, %esi
	0x0f, 0x8d, 0x39, 0x01, 0x00, 0x00, //0x00002481 jge          LBB0_533
	0x4c, 0x89, 0x65, 0xb0, //0x00002487 movq         %r12, $-80(%rbp)
	0x4c, 0x63, 0xe6, //0x0000248b movslq       %esi, %r12
	0x49, 0x63, 0xd7, //0x0000248e movslq       %r15d, %rdx
	0x49, 0x89, 0xd6, //0x00002491 movq         %rdx, %r14
	0x4d, 0x29, 0xe6, //0x00002494 subq         %r12, %r14
	0x4c, 0x89, 0xe3, //0x00002497 movq         %r12, %rbx
	0x48, 0xf7, 0xd3, //0x0000249a notq         %rbx
	0x48, 0x01, 0xd3, //0x0000249d addq         %rdx, %rbx
	0x0f, 0x85, 0x7b, 0x00, 0x00, 0x00, //0x000024a0 jne          LBB0_527
	0x31, 0xd2, //0x000024a6 xorl         %edx, %edx
	0xe9, 0xdf, 0x00, 0x00, 0x00, //0x000024a8 jmp          LBB0_530
	//0x000024ad LBB0_521
	0x48, 0x85, 0xc0, //0x000024ad testq        %rax, %rax
	0x0f, 0x84, 0x55, 0x00, 0x00, 0x00, //0x000024b0 je           LBB0_526
	0x48, 0x89, 0xc2, //0x000024b6 movq         %rax, %rdx
	0x48, 0xd3, 0xea, //0x000024b9 shrq         %cl, %rdx
	0x48, 0x85, 0xd2, //0x000024bc testq        %rdx, %rdx
	0x0f, 0x84, 0x1c, 0x00, 0x00, 0x00, //0x000024bf je           LBB0_524
	0x49, 0xc7, 0xc3, 0xff, 0xff, 0xff, 0xff, //0x000024c5 movq         $-1, %r11
	0x49, 0xd3, 0xe3, //0x000024cc shlq         %cl, %r11
	0x49, 0xf7, 0xd3, //0x000024cf notq         %r11
	0x41, 0x29, 0xf4, //0x000024d2 subl         %esi, %r12d
	0x41, 0x83, 0xc4, 0x01, //0x000024d5 addl         $1, %r12d
	0x45, 0x31, 0xf6, //0x000024d9 xorl         %r14d, %r14d
	0xe9, 0xeb, 0x00, 0x00, 0x00, //0x000024dc jmp          LBB0_534
	//0x000024e1 LBB0_524
	0x48, 0x01, 0xc0, //0x000024e1 addq         %rax, %rax
	0x48, 0x8d, 0x04, 0x80, //0x000024e4 leaq         (%rax,%rax,4), %rax
	0x83, 0xc6, 0x01, //0x000024e8 addl         $1, %esi
	0x48, 0x89, 0xc2, //0x000024eb movq         %rax, %rdx
	0x48, 0xd3, 0xea, //0x000024ee shrq         %cl, %rdx
	0x48, 0x85, 0xd2, //0x000024f1 testq        %rdx, %rdx
	0x0f, 0x84, 0xe7, 0xff, 0xff, 0xff, //0x000024f4 je           LBB0_524
	0xe9, 0x6f, 0xff, 0xff, 0xff, //0x000024fa jmp          LBB0_518
	//0x000024ff LBB0_525
	0x31, 0xff, //0x000024ff xorl         %edi, %edi
	0x45, 0x31, 0xdb, //0x00002501 xorl         %r11d, %r11d
	0x29, 0xf2, //0x00002504 subl         %esi, %edx
	0xe9, 0x7e, 0xfa, 0xff, 0xff, //0x00002506 jmp          LBB0_449
	//0x0000250b LBB0_526
	0x45, 0x31, 0xf6, //0x0000250b xorl         %r14d, %r14d
	0x31, 0xff, //0x0000250e xorl         %edi, %edi
	0x45, 0x31, 0xdb, //0x00002510 xorl         %r11d, %r11d
	0x48, 0x8b, 0x55, 0xc8, //0x00002513 movq         $-56(%rbp), %rdx
	0x8b, 0x75, 0xa4, //0x00002517 movl         $-92(%rbp), %esi
	0x29, 0xf2, //0x0000251a subl         %esi, %edx
	0xe9, 0x68, 0xfa, 0xff, 0xff, //0x0000251c jmp          LBB0_449
	//0x00002521 LBB0_527
	0x4d, 0x89, 0xf7, //0x00002521 movq         %r14, %r15
	0x49, 0x83, 0xe7, 0xfe, //0x00002524 andq         $-2, %r15
	0x49, 0xf7, 0xdf, //0x00002528 negq         %r15
	0x31, 0xd2, //0x0000252b xorl         %edx, %edx
	0x48, 0x8b, 0x5d, 0x90, //0x0000252d movq         $-112(%rbp), %rbx
	//0x00002531 LBB0_528
	0x48, 0x89, 0xc7, //0x00002531 movq         %rax, %rdi
	0x48, 0xd3, 0xef, //0x00002534 shrq         %cl, %rdi
	0x4c, 0x21, 0xd8, //0x00002537 andq         %r11, %rax
	0x40, 0x80, 0xc7, 0x30, //0x0000253a addb         $48, %dil
	0x40, 0x88, 0x7b, 0xff, //0x0000253e movb         %dil, $-1(%rbx)
	0x48, 0x8d, 0x04, 0x80, //0x00002542 leaq         (%rax,%rax,4), %rax
	0x4a, 0x0f, 0xbe, 0x7c, 0x23, 0xff, //0x00002546 movsbq       $-1(%rbx,%r12), %rdi
	0x48, 0x8d, 0x04, 0x47, //0x0000254c leaq         (%rdi,%rax,2), %rax
	0x48, 0x83, 0xc0, 0xd0, //0x00002550 addq         $-48, %rax
	0x48, 0x89, 0xc7, //0x00002554 movq         %rax, %rdi
	0x48, 0xd3, 0xef, //0x00002557 shrq         %cl, %rdi
	0x4c, 0x21, 0xd8, //0x0000255a andq         %r11, %rax
	0x40, 0x80, 0xc7, 0x30, //0x0000255d addb         $48, %dil
	0x40, 0x88, 0x3b, //0x00002561 movb         %dil, (%rbx)
	0x48, 0x8d, 0x04, 0x80, //0x00002564 leaq         (%rax,%rax,4), %rax
	0x4a, 0x0f, 0xbe, 0x3c, 0x23, //0x00002568 movsbq       (%rbx,%r12), %rdi
	0x48, 0x8d, 0x04, 0x47, //0x0000256d leaq         (%rdi,%rax,2), %rax
	0x48, 0x83, 0xc0, 0xd0, //0x00002571 addq         $-48, %rax
	0x48, 0x83, 0xc3, 0x02, //0x00002575 addq         $2, %rbx
	0x48, 0x83, 0xc2, 0xfe, //0x00002579 addq         $-2, %rdx
	0x49, 0x39, 0xd7, //0x0000257d cmpq         %rdx, %r15
	0x0f, 0x85, 0xab, 0xff, 0xff, 0xff, //0x00002580 jne          LBB0_528
	0x49, 0x29, 0xd4, //0x00002586 subq         %rdx, %r12
	0x48, 0xf7, 0xda, //0x00002589 negq         %rdx
	//0x0000258c LBB0_530
	0x41, 0xf6, 0xc6, 0x01, //0x0000258c testb        $1, %r14b
	0x0f, 0x84, 0x21, 0x00, 0x00, 0x00, //0x00002590 je           LBB0_532
	0x48, 0x89, 0xc3, //0x00002596 movq         %rax, %rbx
	0x48, 0xd3, 0xeb, //0x00002599 shrq         %cl, %rbx
	0x4c, 0x21, 0xd8, //0x0000259c andq         %r11, %rax
	0x80, 0xc3, 0x30, //0x0000259f addb         $48, %bl
	0x41, 0x88, 0x1c, 0x10, //0x000025a2 movb         %bl, (%r8,%rdx)
	0x48, 0x8d, 0x04, 0x80, //0x000025a6 leaq         (%rax,%rax,4), %rax
	0x4b, 0x0f, 0xbe, 0x14, 0x20, //0x000025aa movsbq       (%r8,%r12), %rdx
	0x48, 0x8d, 0x04, 0x42, //0x000025af leaq         (%rdx,%rax,2), %rax
	0x48, 0x83, 0xc0, 0xd0, //0x000025b3 addq         $-48, %rax
	//0x000025b7 LBB0_532
	0x4c, 0x8b, 0x65, 0xb0, //0x000025b7 movq         $-80(%rbp), %r12
	0xbb, 0x01, 0x00, 0x00, 0x00, //0x000025bb movl         $1, %ebx
	//0x000025c0 LBB0_533
	0x41, 0x29, 0xf4, //0x000025c0 subl         %esi, %r12d
	0x41, 0x83, 0xc4, 0x01, //0x000025c3 addl         $1, %r12d
	0xe9, 0x35, 0x00, 0x00, 0x00, //0x000025c7 jmp          LBB0_538
	//0x000025cc LBB0_534
	0x48, 0x89, 0xc2, //0x000025cc movq         %rax, %rdx
	0x48, 0xd3, 0xea, //0x000025cf shrq         %cl, %rdx
	0x4c, 0x21, 0xd8, //0x000025d2 andq         %r11, %rax
	0x49, 0x63, 0xf6, //0x000025d5 movslq       %r14d, %rsi
	0x49, 0x39, 0xf5, //0x000025d8 cmpq         %rsi, %r13
	0x0f, 0x86, 0x12, 0x00, 0x00, 0x00, //0x000025db jbe          LBB0_536
	0x80, 0xc2, 0x30, //0x000025e1 addb         $48, %dl
	0x41, 0x88, 0x14, 0x30, //0x000025e4 movb         %dl, (%r8,%rsi)
	0x83, 0xc6, 0x01, //0x000025e8 addl         $1, %esi
	0x41, 0x89, 0xf6, //0x000025eb movl         %esi, %r14d
	0xe9, 0x07, 0x00, 0x00, 0x00, //0x000025ee jmp          LBB0_537
	//0x000025f3 LBB0_536
	0x48, 0x85, 0xd2, //0x000025f3 testq        %rdx, %rdx
	0x44, 0x0f, 0x45, 0xd3, //0x000025f6 cmovnel      %ebx, %r10d
	//0x000025fa LBB0_537
	0x48, 0x01, 0xc0, //0x000025fa addq         %rax, %rax
	0x48, 0x8d, 0x04, 0x80, //0x000025fd leaq         (%rax,%rax,4), %rax
	//0x00002601 LBB0_538
	0x48, 0x85, 0xc0, //0x00002601 testq        %rax, %rax
	0x0f, 0x85, 0xc2, 0xff, 0xff, 0xff, //0x00002604 jne          LBB0_534
	0x45, 0x85, 0xf6, //0x0000260a testl        %r14d, %r14d
	0x48, 0x8b, 0x55, 0xc8, //0x0000260d movq         $-56(%rbp), %rdx
	0x8b, 0x75, 0xa4, //0x00002611 movl         $-92(%rbp), %esi
	0x0f, 0x8e, 0x38, 0x00, 0x00, 0x00, //0x00002614 jle          LBB0_544
	0x44, 0x89, 0xf7, //0x0000261a movl         %r14d, %edi
	0x42, 0x80, 0x7c, 0x07, 0xff, 0x30, //0x0000261d cmpb         $48, $-1(%rdi,%r8)
	0x0f, 0x85, 0x58, 0xf9, 0xff, 0xff, //0x00002623 jne          LBB0_448
	//0x00002629 LBB0_541
	0x48, 0x83, 0xff, 0x01, //0x00002629 cmpq         $1, %rdi
	0x0f, 0x86, 0x30, 0x00, 0x00, 0x00, //0x0000262d jbe          LBB0_546
	0x8d, 0x47, 0xfe, //0x00002633 leal         $-2(%rdi), %eax
	0x48, 0x83, 0xc7, 0xff, //0x00002636 addq         $-1, %rdi
	0x41, 0x80, 0x3c, 0x00, 0x30, //0x0000263a cmpb         $48, (%r8,%rax)
	0x0f, 0x84, 0xe4, 0xff, 0xff, 0xff, //0x0000263f je           LBB0_541
	0x41, 0x89, 0xfe, //0x00002645 movl         %edi, %r14d
	0x41, 0x89, 0xfb, //0x00002648 movl         %edi, %r11d
	0x29, 0xf2, //0x0000264b subl         %esi, %edx
	0xe9, 0x37, 0xf9, 0xff, 0xff, //0x0000264d jmp          LBB0_449
	//0x00002652 LBB0_544
	0x0f, 0x85, 0x29, 0xf9, 0xff, 0xff, //0x00002652 jne          LBB0_448
	0x45, 0x31, 0xf6, //0x00002658 xorl         %r14d, %r14d
	0x45, 0x31, 0xe4, //0x0000265b xorl         %r12d, %r12d
	0xe9, 0x1e, 0xf9, 0xff, 0xff, //0x0000265e jmp          LBB0_448
	//0x00002663 LBB0_546
	0x83, 0xc7, 0xff, //0x00002663 addl         $-1, %edi
	0x41, 0x89, 0xfe, //0x00002666 movl         %edi, %r14d
	0x45, 0x31, 0xe4, //0x00002669 xorl         %r12d, %r12d
	0xe9, 0x10, 0xf9, 0xff, 0xff, //0x0000266c jmp          LBB0_448
	//0x00002671 LBB0_547
	0x81, 0xfa, 0x02, 0xfc, 0xff, 0xff, //0x00002671 cmpl         $-1022, %edx
	0x0f, 0x8f, 0xac, 0x01, 0x00, 0x00, //0x00002677 jg           LBB0_576
	0xbb, 0x02, 0xfc, 0xff, 0xff, //0x0000267d movl         $-1022, %ebx
	0x45, 0x85, 0xdb, //0x00002682 testl        %r11d, %r11d
	0x0f, 0x84, 0x89, 0x04, 0x00, 0x00, //0x00002685 je           LBB0_619
	0x8d, 0xb2, 0xfd, 0x03, 0x00, 0x00, //0x0000268b leal         $1021(%rdx), %esi
	0x81, 0xfa, 0xc6, 0xfb, 0xff, 0xff, //0x00002691 cmpl         $-1082, %edx
	0x0f, 0x87, 0xa2, 0x01, 0x00, 0x00, //0x00002697 ja           LBB0_578
	0x41, 0xbf, 0x01, 0x00, 0x00, 0x00, //0x0000269d movl         $1, %r15d
	0xe9, 0x1a, 0x00, 0x00, 0x00, //0x000026a3 jmp          LBB0_553
	//0x000026a8 LBB0_575
	0x45, 0x31, 0xe4, //0x000026a8 xorl         %r12d, %r12d
	//0x000026ab LBB0_551
	0x45, 0x31, 0xf6, //0x000026ab xorl         %r14d, %r14d
	//0x000026ae LBB0_552
	0x8d, 0x4e, 0x3c, //0x000026ae leal         $60(%rsi), %ecx
	0x45, 0x89, 0xf3, //0x000026b1 movl         %r14d, %r11d
	0x44, 0x89, 0xf7, //0x000026b4 movl         %r14d, %edi
	0x83, 0xfe, 0x88, //0x000026b7 cmpl         $-120, %esi
	0x89, 0xce, //0x000026ba movl         %ecx, %esi
	0x0f, 0x8d, 0x82, 0x01, 0x00, 0x00, //0x000026bc jge          LBB0_579
	//0x000026c2 LBB0_553
	0x45, 0x85, 0xdb, //0x000026c2 testl        %r11d, %r11d
	0xbb, 0x00, 0x00, 0x00, 0x00, //0x000026c5 movl         $0, %ebx
	0x41, 0x0f, 0x4f, 0xdb, //0x000026ca cmovgl       %r11d, %ebx
	0x31, 0xff, //0x000026ce xorl         %edi, %edi
	0x31, 0xc9, //0x000026d0 xorl         %ecx, %ecx
	//0x000026d2 LBB0_554
	0x48, 0x39, 0xfb, //0x000026d2 cmpq         %rdi, %rbx
	0x0f, 0x84, 0x25, 0x00, 0x00, 0x00, //0x000026d5 je           LBB0_557
	0x48, 0x8d, 0x04, 0x89, //0x000026db leaq         (%rcx,%rcx,4), %rax
	0x49, 0x0f, 0xbe, 0x0c, 0x38, //0x000026df movsbq       (%r8,%rdi), %rcx
	0x48, 0x8d, 0x0c, 0x41, //0x000026e4 leaq         (%rcx,%rax,2), %rcx
	0x48, 0x83, 0xc1, 0xd0, //0x000026e8 addq         $-48, %rcx
	0x48, 0x83, 0xc7, 0x01, //0x000026ec addq         $1, %rdi
	0x4c, 0x39, 0xc9, //0x000026f0 cmpq         %r9, %rcx
	0x0f, 0x82, 0xd9, 0xff, 0xff, 0xff, //0x000026f3 jb           LBB0_554
	0x89, 0xfb, //0x000026f9 movl         %edi, %ebx
	0xe9, 0x1c, 0x00, 0x00, 0x00, //0x000026fb jmp          LBB0_559
	//0x00002700 LBB0_557
	0x48, 0x85, 0xc9, //0x00002700 testq        %rcx, %rcx
	0x0f, 0x84, 0xa2, 0xff, 0xff, 0xff, //0x00002703 je           LBB0_551
	//0x00002709 LBB0_558
	0x48, 0x01, 0xc9, //0x00002709 addq         %rcx, %rcx
	0x48, 0x8d, 0x0c, 0x89, //0x0000270c leaq         (%rcx,%rcx,4), %rcx
	0x83, 0xc3, 0x01, //0x00002710 addl         $1, %ebx
	0x4c, 0x39, 0xc9, //0x00002713 cmpq         %r9, %rcx
	0x0f, 0x82, 0xed, 0xff, 0xff, 0xff, //0x00002716 jb           LBB0_558
	//0x0000271c LBB0_559
	0x41, 0x29, 0xdc, //0x0000271c subl         %ebx, %r12d
	0x31, 0xff, //0x0000271f xorl         %edi, %edi
	0x44, 0x39, 0xdb, //0x00002721 cmpl         %r11d, %ebx
	0x0f, 0x8d, 0x57, 0x00, 0x00, 0x00, //0x00002724 jge          LBB0_564
	0x49, 0x89, 0xf3, //0x0000272a movq         %rsi, %r11
	0x48, 0x63, 0xc3, //0x0000272d movslq       %ebx, %rax
	0x49, 0x63, 0xfe, //0x00002730 movslq       %r14d, %rdi
	0x49, 0x8d, 0x1c, 0x00, //0x00002733 leaq         (%r8,%rax), %rbx
	0x45, 0x31, 0xf6, //0x00002737 xorl         %r14d, %r14d
	//0x0000273a LBB0_561
	0x49, 0x8d, 0x51, 0xff, //0x0000273a leaq         $-1(%r9), %rdx
	0x48, 0x21, 0xca, //0x0000273e andq         %rcx, %rdx
	0x48, 0xc1, 0xe9, 0x3c, //0x00002741 shrq         $60, %rcx
	0x80, 0xc9, 0x30, //0x00002745 orb          $48, %cl
	0x43, 0x88, 0x0c, 0x30, //0x00002748 movb         %cl, (%r8,%r14)
	0x4a, 0x0f, 0xbe, 0x0c, 0x33, //0x0000274c movsbq       (%rbx,%r14), %rcx
	0x4a, 0x8d, 0x34, 0x30, //0x00002751 leaq         (%rax,%r14), %rsi
	0x48, 0x83, 0xc6, 0x01, //0x00002755 addq         $1, %rsi
	0x49, 0x83, 0xc6, 0x01, //0x00002759 addq         $1, %r14
	0x48, 0x8d, 0x14, 0x92, //0x0000275d leaq         (%rdx,%rdx,4), %rdx
	0x48, 0x8d, 0x0c, 0x51, //0x00002761 leaq         (%rcx,%rdx,2), %rcx
	0x48, 0x83, 0xc1, 0xd0, //0x00002765 addq         $-48, %rcx
	0x48, 0x39, 0xfe, //0x00002769 cmpq         %rdi, %rsi
	0x0f, 0x8c, 0xc8, 0xff, 0xff, 0xff, //0x0000276c jl           LBB0_561
	0x48, 0x85, 0xc9, //0x00002772 testq        %rcx, %rcx
	0x0f, 0x84, 0x60, 0x00, 0x00, 0x00, //0x00002775 je           LBB0_568
	0x44, 0x89, 0xf7, //0x0000277b movl         %r14d, %edi
	0x4c, 0x89, 0xde, //0x0000277e movq         %r11, %rsi
	//0x00002781 LBB0_564
	0x41, 0x89, 0xfe, //0x00002781 movl         %edi, %r14d
	0xe9, 0x18, 0x00, 0x00, 0x00, //0x00002784 jmp          LBB0_566
	//0x00002789 LBB0_565
	0x4c, 0x39, 0xc9, //0x00002789 cmpq         %r9, %rcx
	0x45, 0x0f, 0x43, 0xd7, //0x0000278c cmovael      %r15d, %r10d
	0x48, 0x8d, 0x0c, 0x00, //0x00002790 leaq         (%rax,%rax), %rcx
	0x48, 0x8d, 0x0c, 0x89, //0x00002794 leaq         (%rcx,%rcx,4), %rcx
	0x48, 0x85, 0xc0, //0x00002798 testq        %rax, %rax
	0x0f, 0x84, 0x3d, 0x00, 0x00, 0x00, //0x0000279b je           LBB0_569
	//0x000027a1 LBB0_566
	0x49, 0x8d, 0x41, 0xff, //0x000027a1 leaq         $-1(%r9), %rax
	0x48, 0x21, 0xc8, //0x000027a5 andq         %rcx, %rax
	0x49, 0x63, 0xfe, //0x000027a8 movslq       %r14d, %rdi
	0x49, 0x39, 0xfd, //0x000027ab cmpq         %rdi, %r13
	0x0f, 0x86, 0xd5, 0xff, 0xff, 0xff, //0x000027ae jbe          LBB0_565
	0x48, 0xc1, 0xe9, 0x3c, //0x000027b4 shrq         $60, %rcx
	0x80, 0xc9, 0x30, //0x000027b8 orb          $48, %cl
	0x41, 0x88, 0x0c, 0x38, //0x000027bb movb         %cl, (%r8,%rdi)
	0x83, 0xc7, 0x01, //0x000027bf addl         $1, %edi
	0x41, 0x89, 0xfe, //0x000027c2 movl         %edi, %r14d
	0x48, 0x8d, 0x0c, 0x00, //0x000027c5 leaq         (%rax,%rax), %rcx
	0x48, 0x8d, 0x0c, 0x89, //0x000027c9 leaq         (%rcx,%rcx,4), %rcx
	0x48, 0x85, 0xc0, //0x000027cd testq        %rax, %rax
	0x0f, 0x85, 0xcb, 0xff, 0xff, 0xff, //0x000027d0 jne          LBB0_566
	0xe9, 0x03, 0x00, 0x00, 0x00, //0x000027d6 jmp          LBB0_569
	//0x000027db LBB0_568
	0x4c, 0x89, 0xde, //0x000027db movq         %r11, %rsi
	//0x000027de LBB0_569
	0x41, 0x83, 0xc4, 0x01, //0x000027de addl         $1, %r12d
	0x45, 0x85, 0xf6, //0x000027e2 testl        %r14d, %r14d
	0x0f, 0x8e, 0x33, 0x00, 0x00, 0x00, //0x000027e5 jle          LBB0_574
	0x44, 0x89, 0xf0, //0x000027eb movl         %r14d, %eax
	0x42, 0x80, 0x7c, 0x00, 0xff, 0x30, //0x000027ee cmpb         $48, $-1(%rax,%r8)
	0x0f, 0x85, 0xb4, 0xfe, 0xff, 0xff, //0x000027f4 jne          LBB0_552
	//0x000027fa LBB0_571
	0x48, 0x83, 0xf8, 0x01, //0x000027fa cmpq         $1, %rax
	0x0f, 0x86, 0xa4, 0xfe, 0xff, 0xff, //0x000027fe jbe          LBB0_575
	0x8d, 0x48, 0xfe, //0x00002804 leal         $-2(%rax), %ecx
	0x48, 0x83, 0xc0, 0xff, //0x00002807 addq         $-1, %rax
	0x41, 0x80, 0x3c, 0x08, 0x30, //0x0000280b cmpb         $48, (%r8,%rcx)
	0x0f, 0x84, 0xe4, 0xff, 0xff, 0xff, //0x00002810 je           LBB0_571
	0x41, 0x89, 0xc6, //0x00002816 movl         %eax, %r14d
	0xe9, 0x90, 0xfe, 0xff, 0xff, //0x00002819 jmp          LBB0_552
	//0x0000281e LBB0_574
	0x0f, 0x85, 0x8a, 0xfe, 0xff, 0xff, //0x0000281e jne          LBB0_552
	0xe9, 0x7f, 0xfe, 0xff, 0xff, //0x00002824 jmp          LBB0_575
	//0x00002829 LBB0_576
	0x81, 0xfa, 0x00, 0x04, 0x00, 0x00, //0x00002829 cmpl         $1024, %edx
	0x0f, 0x8f, 0xd0, 0x07, 0x00, 0x00, //0x0000282f jg           LBB0_675
	0x83, 0xc2, 0xff, //0x00002835 addl         $-1, %edx
	0x89, 0xd3, //0x00002838 movl         %edx, %ebx
	0xe9, 0x17, 0x02, 0x00, 0x00, //0x0000283a jmp          LBB0_607
	//0x0000283f LBB0_578
	0x44, 0x89, 0xdf, //0x0000283f movl         %r11d, %edi
	0x89, 0xf1, //0x00002842 movl         %esi, %ecx
	//0x00002844 LBB0_579
	0xf7, 0xd9, //0x00002844 negl         %ecx
	0x31, 0xd2, //0x00002846 xorl         %edx, %edx
	0x85, 0xff, //0x00002848 testl        %edi, %edi
	0xbe, 0x00, 0x00, 0x00, 0x00, //0x0000284a movl         $0, %esi
	0x0f, 0x4f, 0xf7, //0x0000284f cmovgl       %edi, %esi
	0x31, 0xc0, //0x00002852 xorl         %eax, %eax
	//0x00002854 LBB0_580
	0x48, 0x39, 0xd6, //0x00002854 cmpq         %rdx, %rsi
	0x0f, 0x84, 0x97, 0x00, 0x00, 0x00, //0x00002857 je           LBB0_587
	0x48, 0x8d, 0x04, 0x80, //0x0000285d leaq         (%rax,%rax,4), %rax
	0x49, 0x0f, 0xbe, 0x1c, 0x10, //0x00002861 movsbq       (%r8,%rdx), %rbx
	0x48, 0x8d, 0x04, 0x43, //0x00002866 leaq         (%rbx,%rax,2), %rax
	0x48, 0x83, 0xc0, 0xd0, //0x0000286a addq         $-48, %rax
	0x48, 0x83, 0xc2, 0x01, //0x0000286e addq         $1, %rdx
	0x48, 0x89, 0xc3, //0x00002872 movq         %rax, %rbx
	0x48, 0xd3, 0xeb, //0x00002875 shrq         %cl, %rbx
	0x48, 0x85, 0xdb, //0x00002878 testq        %rbx, %rbx
	0x0f, 0x84, 0xd3, 0xff, 0xff, 0xff, //0x0000287b je           LBB0_580
	0x89, 0xd6, //0x00002881 movl         %edx, %esi
	//0x00002883 LBB0_583
	0x48, 0xc7, 0xc2, 0xff, 0xff, 0xff, 0xff, //0x00002883 movq         $-1, %rdx
	0x48, 0xd3, 0xe2, //0x0000288a shlq         %cl, %rdx
	0x48, 0xf7, 0xd2, //0x0000288d notq         %rdx
	0x45, 0x31, 0xc9, //0x00002890 xorl         %r9d, %r9d
	0x39, 0xfe, //0x00002893 cmpl         %edi, %esi
	0x0f, 0x8d, 0x44, 0x00, 0x00, 0x00, //0x00002895 jge          LBB0_586
	0x4c, 0x63, 0xfe, //0x0000289b movslq       %esi, %r15
	0x4d, 0x63, 0xde, //0x0000289e movslq       %r14d, %r11
	0x4f, 0x8d, 0x34, 0x38, //0x000028a1 leaq         (%r8,%r15), %r14
	0x45, 0x31, 0xc9, //0x000028a5 xorl         %r9d, %r9d
	//0x000028a8 LBB0_585
	0x48, 0x89, 0xc7, //0x000028a8 movq         %rax, %rdi
	0x48, 0xd3, 0xef, //0x000028ab shrq         %cl, %rdi
	0x48, 0x21, 0xd0, //0x000028ae andq         %rdx, %rax
	0x40, 0x80, 0xc7, 0x30, //0x000028b1 addb         $48, %dil
	0x43, 0x88, 0x3c, 0x08, //0x000028b5 movb         %dil, (%r8,%r9)
	0x4b, 0x0f, 0xbe, 0x3c, 0x0e, //0x000028b9 movsbq       (%r14,%r9), %rdi
	0x4b, 0x8d, 0x1c, 0x0f, //0x000028be leaq         (%r15,%r9), %rbx
	0x48, 0x83, 0xc3, 0x01, //0x000028c2 addq         $1, %rbx
	0x49, 0x83, 0xc1, 0x01, //0x000028c6 addq         $1, %r9
	0x48, 0x8d, 0x04, 0x80, //0x000028ca leaq         (%rax,%rax,4), %rax
	0x48, 0x8d, 0x04, 0x47, //0x000028ce leaq         (%rdi,%rax,2), %rax
	0x48, 0x83, 0xc0, 0xd0, //0x000028d2 addq         $-48, %rax
	0x4c, 0x39, 0xdb, //0x000028d6 cmpq         %r11, %rbx
	0x0f, 0x8c, 0xc9, 0xff, 0xff, 0xff, //0x000028d9 jl           LBB0_585
	//0x000028df LBB0_586
	0x41, 0x29, 0xf4, //0x000028df subl         %esi, %r12d
	0x41, 0x83, 0xc4, 0x01, //0x000028e2 addl         $1, %r12d
	0x48, 0x85, 0xc0, //0x000028e6 testq        %rax, %rax
	0x0f, 0x85, 0x34, 0x00, 0x00, 0x00, //0x000028e9 jne          LBB0_590
	0xe9, 0x83, 0x00, 0x00, 0x00, //0x000028ef jmp          LBB0_594
	//0x000028f4 LBB0_587
	0x48, 0x85, 0xc0, //0x000028f4 testq        %rax, %rax
	0x0f, 0x84, 0x30, 0x01, 0x00, 0x00, //0x000028f7 je           LBB0_604
	0x48, 0x89, 0xc2, //0x000028fd movq         %rax, %rdx
	0x48, 0xd3, 0xea, //0x00002900 shrq         %cl, %rdx
	0x48, 0x85, 0xd2, //0x00002903 testq        %rdx, %rdx
	0x0f, 0x84, 0xb1, 0x00, 0x00, 0x00, //0x00002906 je           LBB0_598
	0x48, 0xc7, 0xc2, 0xff, 0xff, 0xff, 0xff, //0x0000290c movq         $-1, %rdx
	0x48, 0xd3, 0xe2, //0x00002913 shlq         %cl, %rdx
	0x48, 0xf7, 0xd2, //0x00002916 notq         %rdx
	0x41, 0x29, 0xf4, //0x00002919 subl         %esi, %r12d
	0x41, 0x83, 0xc4, 0x01, //0x0000291c addl         $1, %r12d
	0x45, 0x31, 0xc9, //0x00002920 xorl         %r9d, %r9d
	//0x00002923 LBB0_590
	0xbe, 0x01, 0x00, 0x00, 0x00, //0x00002923 movl         $1, %esi
	0xe9, 0x17, 0x00, 0x00, 0x00, //0x00002928 jmp          LBB0_592
	//0x0000292d LBB0_591
	0x48, 0x85, 0xff, //0x0000292d testq        %rdi, %rdi
	0x44, 0x0f, 0x45, 0xd6, //0x00002930 cmovnel      %esi, %r10d
	0x48, 0x01, 0xc0, //0x00002934 addq         %rax, %rax
	0x48, 0x8d, 0x04, 0x80, //0x00002937 leaq         (%rax,%rax,4), %rax
	0x48, 0x85, 0xc0, //0x0000293b testq        %rax, %rax
	0x0f, 0x84, 0x33, 0x00, 0x00, 0x00, //0x0000293e je           LBB0_594
	//0x00002944 LBB0_592
	0x48, 0x89, 0xc7, //0x00002944 movq         %rax, %rdi
	0x48, 0xd3, 0xef, //0x00002947 shrq         %cl, %rdi
	0x48, 0x21, 0xd0, //0x0000294a andq         %rdx, %rax
	0x49, 0x63, 0xd9, //0x0000294d movslq       %r9d, %rbx
	0x49, 0x39, 0xdd, //0x00002950 cmpq         %rbx, %r13
	0x0f, 0x86, 0xd4, 0xff, 0xff, 0xff, //0x00002953 jbe          LBB0_591
	0x40, 0x80, 0xc7, 0x30, //0x00002959 addb         $48, %dil
	0x41, 0x88, 0x3c, 0x18, //0x0000295d movb         %dil, (%r8,%rbx)
	0x83, 0xc3, 0x01, //0x00002961 addl         $1, %ebx
	0x41, 0x89, 0xd9, //0x00002964 movl         %ebx, %r9d
	0x48, 0x01, 0xc0, //0x00002967 addq         %rax, %rax
	0x48, 0x8d, 0x04, 0x80, //0x0000296a leaq         (%rax,%rax,4), %rax
	0x48, 0x85, 0xc0, //0x0000296e testq        %rax, %rax
	0x0f, 0x85, 0xcd, 0xff, 0xff, 0xff, //0x00002971 jne          LBB0_592
	//0x00002977 LBB0_594
	0x45, 0x85, 0xc9, //0x00002977 testl        %r9d, %r9d
	0x0f, 0x8e, 0x6d, 0x00, 0x00, 0x00, //0x0000297a jle          LBB0_600
	0x44, 0x89, 0xc8, //0x00002980 movl         %r9d, %eax
	0xb9, 0x02, 0xfc, 0xff, 0xff, //0x00002983 movl         $-1022, %ecx
	0x48, 0x89, 0x4d, 0xc8, //0x00002988 movq         %rcx, $-56(%rbp)
	0x42, 0x80, 0x7c, 0x00, 0xff, 0x30, //0x0000298c cmpb         $48, $-1(%rax,%r8)
	0x0f, 0x85, 0x64, 0x00, 0x00, 0x00, //0x00002992 jne          LBB0_603
	//0x00002998 LBB0_596
	0x49, 0x89, 0xc6, //0x00002998 movq         %rax, %r14
	0x48, 0x83, 0xf8, 0x01, //0x0000299b cmpq         $1, %rax
	0x0f, 0x86, 0xa2, 0x00, 0x00, 0x00, //0x0000299f jbe          LBB0_605
	0x41, 0x8d, 0x4e, 0xfe, //0x000029a5 leal         $-2(%r14), %ecx
	0x49, 0x8d, 0x46, 0xff, //0x000029a9 leaq         $-1(%r14), %rax
	0x41, 0x80, 0x3c, 0x08, 0x30, //0x000029ad cmpb         $48, (%r8,%rcx)
	0x0f, 0x84, 0xe0, 0xff, 0xff, 0xff, //0x000029b2 je           LBB0_596
	0xe9, 0x8d, 0x00, 0x00, 0x00, //0x000029b8 jmp          LBB0_606
	//0x000029bd LBB0_598
	0x48, 0x01, 0xc0, //0x000029bd addq         %rax, %rax
	0x48, 0x8d, 0x04, 0x80, //0x000029c0 leaq         (%rax,%rax,4), %rax
	0x83, 0xc6, 0x01, //0x000029c4 addl         $1, %esi
	0x48, 0x89, 0xc2, //0x000029c7 movq         %rax, %rdx
	0x48, 0xd3, 0xea, //0x000029ca shrq         %cl, %rdx
	0x48, 0x85, 0xd2, //0x000029cd testq        %rdx, %rdx
	0x0f, 0x84, 0xe7, 0xff, 0xff, 0xff, //0x000029d0 je           LBB0_598
	0xe9, 0xa8, 0xfe, 0xff, 0xff, //0x000029d6 jmp          LBB0_583
	//0x000029db LBB0_599
	0x49, 0xc7, 0xc1, 0xff, 0xff, 0xff, 0xff, //0x000029db movq         $-1, %r9
	0x49, 0x29, 0xfa, //0x000029e2 subq         %rdi, %r10
	0x4d, 0x89, 0x0f, //0x000029e5 movq         %r9, (%r15)
	0xe9, 0x16, 0xea, 0xff, 0xff, //0x000029e8 jmp          LBB0_276
	//0x000029ed LBB0_600
	0xb8, 0x02, 0xfc, 0xff, 0xff, //0x000029ed movl         $-1022, %eax
	0x48, 0x89, 0x45, 0xc8, //0x000029f2 movq         %rax, $-56(%rbp)
	0x0f, 0x84, 0xb9, 0x06, 0x00, 0x00, //0x000029f6 je           LBB0_684
	//0x000029fc LBB0_603
	0x45, 0x89, 0xcb, //0x000029fc movl         %r9d, %r11d
	0xe9, 0x62, 0x00, 0x00, 0x00, //0x000029ff jmp          LBB0_609
	//0x00002a04 LBB0_602
	0x49, 0xc7, 0xc7, 0xff, 0xff, 0xff, 0xff, //0x00002a04 movq         $-1, %r15
	0x48, 0xc7, 0x45, 0xc8, 0xff, 0xff, 0xff, 0xff, //0x00002a0b movq         $-1, $-56(%rbp)
	0x4d, 0x89, 0xd0, //0x00002a13 movq         %r10, %r8
	0x4c, 0x89, 0x6d, 0xc0, //0x00002a16 movq         %r13, $-64(%rbp)
	0x49, 0x83, 0xfb, 0x10, //0x00002a1a cmpq         $16, %r11
	0x4c, 0x89, 0x55, 0xa8, //0x00002a1e movq         %r10, $-88(%rbp)
	0x0f, 0x83, 0x69, 0xdc, 0xff, 0xff, //0x00002a22 jae          LBB0_66
	0xe9, 0xbe, 0xdd, 0xff, 0xff, //0x00002a28 jmp          LBB0_84
	//0x00002a2d LBB0_604
	0x31, 0xc0, //0x00002a2d xorl         %eax, %eax
	0x49, 0xbe, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x80, //0x00002a2f movabsq      $-9223372036854775808, %r14
	0x48, 0x8b, 0x4d, 0x98, //0x00002a39 movq         $-104(%rbp), %rcx
	0xbb, 0x02, 0xfc, 0xff, 0xff, //0x00002a3d movl         $-1022, %ebx
	0xe9, 0x76, 0x02, 0x00, 0x00, //0x00002a42 jmp          LBB0_641
	//0x00002a47 LBB0_605
	0x45, 0x31, 0xe4, //0x00002a47 xorl         %r12d, %r12d
	//0x00002a4a LBB0_606
	0x41, 0x83, 0xc6, 0xff, //0x00002a4a addl         $-1, %r14d
	0xbb, 0x02, 0xfc, 0xff, 0xff, //0x00002a4e movl         $-1022, %ebx
	0x45, 0x89, 0xf3, //0x00002a53 movl         %r14d, %r11d
	//0x00002a56 LBB0_607
	0x45, 0x85, 0xdb, //0x00002a56 testl        %r11d, %r11d
	0x0f, 0x84, 0xb5, 0x00, 0x00, 0x00, //0x00002a59 je           LBB0_619
	0x48, 0x89, 0x5d, 0xc8, //0x00002a5f movq         %rbx, $-56(%rbp)
	0x45, 0x89, 0xf1, //0x00002a63 movl         %r14d, %r9d
	//0x00002a66 LBB0_609
	0x49, 0x63, 0xcb, //0x00002a66 movslq       %r11d, %rcx
	0x48, 0x8d, 0x41, 0xfe, //0x00002a69 leaq         $-2(%rcx), %rax
	0x48, 0x8d, 0x71, 0xff, //0x00002a6d leaq         $-1(%rcx), %rsi
	0x31, 0xff, //0x00002a71 xorl         %edi, %edi
	//0x00002a73 LBB0_610
	0x48, 0x8d, 0x15, 0xd6, 0x38, 0x00, 0x00, //0x00002a73 leaq         $14550(%rip), %rdx  /* _LSHIFT_TAB+0(%rip) */
	0x0f, 0xb6, 0x9c, 0x17, 0x8c, 0x15, 0x00, 0x00, //0x00002a7a movzbl       $5516(%rdi,%rdx), %ebx
	0x41, 0x0f, 0xb6, 0x14, 0x38, //0x00002a82 movzbl       (%r8,%rdi), %edx
	0x38, 0xda, //0x00002a87 cmpb         %bl, %dl
	0x0f, 0x85, 0x9a, 0x00, 0x00, 0x00, //0x00002a89 jne          LBB0_620
	0x48, 0x39, 0xfe, //0x00002a8f cmpq         %rdi, %rsi
	0x0f, 0x84, 0x5a, 0x00, 0x00, 0x00, //0x00002a92 je           LBB0_617
	0x48, 0x8d, 0x15, 0xb1, 0x38, 0x00, 0x00, //0x00002a98 leaq         $14513(%rip), %rdx  /* _LSHIFT_TAB+0(%rip) */
	0x0f, 0xb6, 0x9c, 0x17, 0x8d, 0x15, 0x00, 0x00, //0x00002a9f movzbl       $5517(%rdi,%rdx), %ebx
	0x41, 0x0f, 0xb6, 0x54, 0x38, 0x01, //0x00002aa7 movzbl       $1(%r8,%rdi), %edx
	0x38, 0xda, //0x00002aad cmpb         %bl, %dl
	0x0f, 0x85, 0x74, 0x00, 0x00, 0x00, //0x00002aaf jne          LBB0_620
	0x48, 0x39, 0xf8, //0x00002ab5 cmpq         %rdi, %rax
	0x0f, 0x84, 0x34, 0x00, 0x00, 0x00, //0x00002ab8 je           LBB0_617
	0x48, 0x83, 0xff, 0x24, //0x00002abe cmpq         $36, %rdi
	0x0f, 0x84, 0x42, 0x00, 0x00, 0x00, //0x00002ac2 je           LBB0_618
	0x48, 0x8d, 0x15, 0x81, 0x38, 0x00, 0x00, //0x00002ac8 leaq         $14465(%rip), %rdx  /* _LSHIFT_TAB+0(%rip) */
	0x0f, 0xb6, 0x9c, 0x17, 0x8e, 0x15, 0x00, 0x00, //0x00002acf movzbl       $5518(%rdi,%rdx), %ebx
	0x41, 0x0f, 0xb6, 0x54, 0x38, 0x02, //0x00002ad7 movzbl       $2(%r8,%rdi), %edx
	0x38, 0xda, //0x00002add cmpb         %bl, %dl
	0x0f, 0x85, 0x44, 0x00, 0x00, 0x00, //0x00002adf jne          LBB0_620
	0x48, 0x83, 0xc7, 0x03, //0x00002ae5 addq         $3, %rdi
	0x48, 0x39, 0xf9, //0x00002ae9 cmpq         %rdi, %rcx
	0x0f, 0x85, 0x81, 0xff, 0xff, 0xff, //0x00002aec jne          LBB0_610
	//0x00002af2 LBB0_617
	0x44, 0x89, 0xd8, //0x00002af2 movl         %r11d, %eax
	0x48, 0x8d, 0x0d, 0x54, 0x38, 0x00, 0x00, //0x00002af5 leaq         $14420(%rip), %rcx  /* _LSHIFT_TAB+0(%rip) */
	0x80, 0xbc, 0x08, 0x8c, 0x15, 0x00, 0x00, 0x00, //0x00002afc cmpb         $0, $5516(%rax,%rcx)
	0x0f, 0x85, 0x2c, 0x00, 0x00, 0x00, //0x00002b04 jne          LBB0_621
	//0x00002b0a LBB0_618
	0xbf, 0x10, 0x00, 0x00, 0x00, //0x00002b0a movl         $16, %edi
	0xe9, 0x27, 0x00, 0x00, 0x00, //0x00002b0f jmp          LBB0_622
	//0x00002b14 LBB0_619
	0x31, 0xc0, //0x00002b14 xorl         %eax, %eax
	0x49, 0xbe, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x80, //0x00002b16 movabsq      $-9223372036854775808, %r14
	0x48, 0x8b, 0x4d, 0x98, //0x00002b20 movq         $-104(%rbp), %rcx
	0xe9, 0x94, 0x01, 0x00, 0x00, //0x00002b24 jmp          LBB0_641
	//0x00002b29 LBB0_620
	0xbf, 0x10, 0x00, 0x00, 0x00, //0x00002b29 movl         $16, %edi
	0x38, 0xda, //0x00002b2e cmpb         %bl, %dl
	0x0f, 0x8d, 0x05, 0x00, 0x00, 0x00, //0x00002b30 jge          LBB0_622
	//0x00002b36 LBB0_621
	0xbf, 0x0f, 0x00, 0x00, 0x00, //0x00002b36 movl         $15, %edi
	//0x00002b3b LBB0_622
	0x45, 0x85, 0xdb, //0x00002b3b testl        %r11d, %r11d
	0x0f, 0x8e, 0x18, 0x01, 0x00, 0x00, //0x00002b3e jle          LBB0_634
	0x48, 0x89, 0x7d, 0xc0, //0x00002b44 movq         %rdi, $-64(%rbp)
	0x46, 0x8d, 0x3c, 0x1f, //0x00002b48 leal         (%rdi,%r11), %r15d
	0x44, 0x89, 0xdb, //0x00002b4c movl         %r11d, %ebx
	0x49, 0x63, 0xff, //0x00002b4f movslq       %r15d, %rdi
	0x48, 0x83, 0xc7, 0xff, //0x00002b52 addq         $-1, %rdi
	0x48, 0x83, 0xc3, 0x01, //0x00002b56 addq         $1, %rbx
	0x31, 0xc9, //0x00002b5a xorl         %ecx, %ecx
	0x49, 0xbb, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xfa, //0x00002b5c movabsq      $-432345564227567616, %r11
	0x41, 0xbe, 0x01, 0x00, 0x00, 0x00, //0x00002b66 movl         $1, %r14d
	0xe9, 0x1d, 0x00, 0x00, 0x00, //0x00002b6c jmp          LBB0_626
	//0x00002b71 LBB0_624
	0x48, 0x85, 0xc0, //0x00002b71 testq        %rax, %rax
	0x45, 0x0f, 0x45, 0xd6, //0x00002b74 cmovnel      %r14d, %r10d
	//0x00002b78 LBB0_625
	0x41, 0x83, 0xc7, 0xff, //0x00002b78 addl         $-1, %r15d
	0x48, 0x83, 0xc7, 0xff, //0x00002b7c addq         $-1, %rdi
	0x48, 0x83, 0xc3, 0xff, //0x00002b80 addq         $-1, %rbx
	0x48, 0x83, 0xfb, 0x01, //0x00002b84 cmpq         $1, %rbx
	0x0f, 0x86, 0x4b, 0x00, 0x00, 0x00, //0x00002b88 jbe          LBB0_628
	//0x00002b8e LBB0_626
	0x8d, 0x43, 0xfe, //0x00002b8e leal         $-2(%rbx), %eax
	0x49, 0x0f, 0xbe, 0x34, 0x00, //0x00002b91 movsbq       (%r8,%rax), %rsi
	0x48, 0xc1, 0xe6, 0x35, //0x00002b96 shlq         $53, %rsi
	0x48, 0x01, 0xce, //0x00002b9a addq         %rcx, %rsi
	0x4c, 0x01, 0xde, //0x00002b9d addq         %r11, %rsi
	0x48, 0x89, 0xf0, //0x00002ba0 movq         %rsi, %rax
	0x48, 0xb9, 0xcd, 0xcc, 0xcc, 0xcc, 0xcc, 0xcc, 0xcc, 0xcc, //0x00002ba3 movabsq      $-3689348814741910323, %rcx
	0x48, 0xf7, 0xe1, //0x00002bad mulq         %rcx
	0x48, 0x89, 0xd1, //0x00002bb0 movq         %rdx, %rcx
	0x48, 0xc1, 0xe9, 0x03, //0x00002bb3 shrq         $3, %rcx
	0x48, 0x8d, 0x04, 0x09, //0x00002bb7 leaq         (%rcx,%rcx), %rax
	0x48, 0x8d, 0x14, 0x80, //0x00002bbb leaq         (%rax,%rax,4), %rdx
	0x48, 0x89, 0xf0, //0x00002bbf movq         %rsi, %rax
	0x48, 0x29, 0xd0, //0x00002bc2 subq         %rdx, %rax
	0x4c, 0x39, 0xef, //0x00002bc5 cmpq         %r13, %rdi
	0x0f, 0x83, 0xa3, 0xff, 0xff, 0xff, //0x00002bc8 jae          LBB0_624
	0x04, 0x30, //0x00002bce addb         $48, %al
	0x41, 0x88, 0x04, 0x38, //0x00002bd0 movb         %al, (%r8,%rdi)
	0xe9, 0x9f, 0xff, 0xff, 0xff, //0x00002bd4 jmp          LBB0_625
	//0x00002bd9 LBB0_628
	0x48, 0x83, 0xfe, 0x0a, //0x00002bd9 cmpq         $10, %rsi
	0x0f, 0x83, 0x09, 0x00, 0x00, 0x00, //0x00002bdd jae          LBB0_630
	0x48, 0x8b, 0x7d, 0xc0, //0x00002be3 movq         $-64(%rbp), %rdi
	0xe9, 0x70, 0x00, 0x00, 0x00, //0x00002be7 jmp          LBB0_634
	//0x00002bec LBB0_630
	0x49, 0x63, 0xf7, //0x00002bec movslq       %r15d, %rsi
	0x48, 0x83, 0xc6, 0xff, //0x00002bef addq         $-1, %rsi
	0x41, 0xbb, 0x01, 0x00, 0x00, 0x00, //0x00002bf3 movl         $1, %r11d
	0x48, 0x8b, 0x7d, 0xc0, //0x00002bf9 movq         $-64(%rbp), %rdi
	0xe9, 0x18, 0x00, 0x00, 0x00, //0x00002bfd jmp          LBB0_632
	//0x00002c02 LBB0_631
	0x48, 0x85, 0xc0, //0x00002c02 testq        %rax, %rax
	0x45, 0x0f, 0x45, 0xd3, //0x00002c05 cmovnel      %r11d, %r10d
	0x48, 0x83, 0xc6, 0xff, //0x00002c09 addq         $-1, %rsi
	0x48, 0x83, 0xf9, 0x09, //0x00002c0d cmpq         $9, %rcx
	0x48, 0x89, 0xd1, //0x00002c11 movq         %rdx, %rcx
	0x0f, 0x86, 0x42, 0x00, 0x00, 0x00, //0x00002c14 jbe          LBB0_634
	//0x00002c1a LBB0_632
	0x48, 0x89, 0xc8, //0x00002c1a movq         %rcx, %rax
	0x48, 0xba, 0xcd, 0xcc, 0xcc, 0xcc, 0xcc, 0xcc, 0xcc, 0xcc, //0x00002c1d movabsq      $-3689348814741910323, %rdx
	0x48, 0xf7, 0xe2, //0x00002c27 mulq         %rdx
	0x48, 0xc1, 0xea, 0x03, //0x00002c2a shrq         $3, %rdx
	0x48, 0x8d, 0x04, 0x12, //0x00002c2e leaq         (%rdx,%rdx), %rax
	0x48, 0x8d, 0x1c, 0x80, //0x00002c32 leaq         (%rax,%rax,4), %rbx
	0x48, 0x89, 0xc8, //0x00002c36 movq         %rcx, %rax
	0x48, 0x29, 0xd8, //0x00002c39 subq         %rbx, %rax
	0x4c, 0x39, 0xee, //0x00002c3c cmpq         %r13, %rsi
	0x0f, 0x83, 0xbd, 0xff, 0xff, 0xff, //0x00002c3f jae          LBB0_631
	0x04, 0x30, //0x00002c45 addb         $48, %al
	0x41, 0x88, 0x04, 0x30, //0x00002c47 movb         %al, (%r8,%rsi)
	0x48, 0x83, 0xc6, 0xff, //0x00002c4b addq         $-1, %rsi
	0x48, 0x83, 0xf9, 0x09, //0x00002c4f cmpq         $9, %rcx
	0x48, 0x89, 0xd1, //0x00002c53 movq         %rdx, %rcx
	0x0f, 0x87, 0xbe, 0xff, 0xff, 0xff, //0x00002c56 ja           LBB0_632
	//0x00002c5c LBB0_634
	0x41, 0x01, 0xf9, //0x00002c5c addl         %edi, %r9d
	0x49, 0x63, 0xc1, //0x00002c5f movslq       %r9d, %rax
	0x49, 0x39, 0xc5, //0x00002c62 cmpq         %rax, %r13
	0x41, 0x0f, 0x46, 0xc5, //0x00002c65 cmovbel      %r13d, %eax
	0x41, 0x01, 0xfc, //0x00002c69 addl         %edi, %r12d
	0x85, 0xc0, //0x00002c6c testl        %eax, %eax
	0x0f, 0x8e, 0x31, 0x00, 0x00, 0x00, //0x00002c6e jle          LBB0_639
	0x89, 0xc1, //0x00002c74 movl         %eax, %ecx
	0x42, 0x80, 0x7c, 0x01, 0xff, 0x30, //0x00002c76 cmpb         $48, $-1(%rcx,%r8)
	0x0f, 0x85, 0x29, 0x00, 0x00, 0x00, //0x00002c7c jne          LBB0_640
	//0x00002c82 LBB0_636
	0x48, 0x83, 0xf9, 0x01, //0x00002c82 cmpq         $1, %rcx
	0x0f, 0x86, 0x16, 0x01, 0x00, 0x00, //0x00002c86 jbe          LBB0_654
	0x8d, 0x41, 0xfe, //0x00002c8c leal         $-2(%rcx), %eax
	0x48, 0x83, 0xc1, 0xff, //0x00002c8f addq         $-1, %rcx
	0x41, 0x80, 0x3c, 0x00, 0x30, //0x00002c93 cmpb         $48, (%r8,%rax)
	0x0f, 0x84, 0xe4, 0xff, 0xff, 0xff, //0x00002c98 je           LBB0_636
	0x89, 0xc8, //0x00002c9e movl         %ecx, %eax
	0xe9, 0x06, 0x00, 0x00, 0x00, //0x00002ca0 jmp          LBB0_640
	//0x00002ca5 LBB0_639
	0x0f, 0x84, 0x06, 0x01, 0x00, 0x00, //0x00002ca5 je           LBB0_655
	//0x00002cab LBB0_640
	0x49, 0xbe, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x80, //0x00002cab movabsq      $-9223372036854775808, %r14
	0x48, 0x8b, 0x4d, 0x98, //0x00002cb5 movq         $-104(%rbp), %rcx
	0x48, 0x8b, 0x5d, 0xc8, //0x00002cb9 movq         $-56(%rbp), %rbx
	//0x00002cbd LBB0_641
	0x48, 0xc7, 0xc6, 0xff, 0xff, 0xff, 0xff, //0x00002cbd movq         $-1, %rsi
	0x41, 0x83, 0xfc, 0x14, //0x00002cc4 cmpl         $20, %r12d
	0x0f, 0x8e, 0x08, 0x00, 0x00, 0x00, //0x00002cc8 jle          LBB0_643
	0x8a, 0x55, 0xd7, //0x00002cce movb         $-41(%rbp), %dl
	0xe9, 0xf7, 0x03, 0x00, 0x00, //0x00002cd1 jmp          LBB0_685
	//0x00002cd6 LBB0_643
	0x48, 0x89, 0x5d, 0xc8, //0x00002cd6 movq         %rbx, $-56(%rbp)
	0x45, 0x85, 0xe4, //0x00002cda testl        %r12d, %r12d
	0x48, 0x89, 0x4d, 0x98, //0x00002cdd movq         %rcx, $-104(%rbp)
	0x0f, 0x8e, 0x55, 0x00, 0x00, 0x00, //0x00002ce1 jle          LBB0_649
	0x31, 0xd2, //0x00002ce7 xorl         %edx, %edx
	0x85, 0xc0, //0x00002ce9 testl        %eax, %eax
	0xbe, 0x00, 0x00, 0x00, 0x00, //0x00002ceb movl         $0, %esi
	0x0f, 0x4f, 0xf0, //0x00002cf0 cmovgl       %eax, %esi
	0x4d, 0x89, 0xe6, //0x00002cf3 movq         %r12, %r14
	0x44, 0x89, 0xe3, //0x00002cf6 movl         %r12d, %ebx
	0x4c, 0x8d, 0x4b, 0xff, //0x00002cf9 leaq         $-1(%rbx), %r9
	0x49, 0x39, 0xf1, //0x00002cfd cmpq         %rsi, %r9
	0x4c, 0x0f, 0x43, 0xce, //0x00002d00 cmovaeq      %rsi, %r9
	0x4d, 0x8d, 0x59, 0x01, //0x00002d04 leaq         $1(%r9), %r11
	0x31, 0xff, //0x00002d08 xorl         %edi, %edi
	//0x00002d0a LBB0_645
	0x48, 0x39, 0xd6, //0x00002d0a cmpq         %rdx, %rsi
	0x0f, 0x84, 0x21, 0x00, 0x00, 0x00, //0x00002d0d je           LBB0_648
	0x48, 0x8d, 0x3c, 0xbf, //0x00002d13 leaq         (%rdi,%rdi,4), %rdi
	0x49, 0x0f, 0xbe, 0x0c, 0x10, //0x00002d17 movsbq       (%r8,%rdx), %rcx
	0x48, 0x8d, 0x3c, 0x79, //0x00002d1c leaq         (%rcx,%rdi,2), %rdi
	0x48, 0x83, 0xc7, 0xd0, //0x00002d20 addq         $-48, %rdi
	0x48, 0x83, 0xc2, 0x01, //0x00002d24 addq         $1, %rdx
	0x48, 0x39, 0xd3, //0x00002d28 cmpq         %rdx, %rbx
	0x0f, 0x85, 0xd9, 0xff, 0xff, 0xff, //0x00002d2b jne          LBB0_645
	0x4d, 0x89, 0xd9, //0x00002d31 movq         %r11, %r9
	//0x00002d34 LBB0_648
	0x4d, 0x89, 0xf4, //0x00002d34 movq         %r14, %r12
	0xe9, 0x05, 0x00, 0x00, 0x00, //0x00002d37 jmp          LBB0_650
	//0x00002d3c LBB0_649
	0x45, 0x31, 0xc9, //0x00002d3c xorl         %r9d, %r9d
	0x31, 0xff, //0x00002d3f xorl         %edi, %edi
	//0x00002d41 LBB0_650
	0x44, 0x89, 0xe2, //0x00002d41 movl         %r12d, %edx
	0x44, 0x29, 0xca, //0x00002d44 subl         %r9d, %edx
	0x0f, 0x8e, 0x3b, 0x02, 0x00, 0x00, //0x00002d47 jle          LBB0_665
	0x83, 0xfa, 0x10, //0x00002d4d cmpl         $16, %edx
	0x0f, 0x82, 0x1c, 0x02, 0x00, 0x00, //0x00002d50 jb           LBB0_663
	0x89, 0xd6, //0x00002d56 movl         %edx, %esi
	0xc5, 0xfa, 0x6f, 0x05, 0x00, 0xd4, 0xff, 0xff, //0x00002d58 vmovdqu      $-11264(%rip), %xmm0  /* LCPI0_22+0(%rip) */
	0xc4, 0xe3, 0xf9, 0x22, 0xc7, 0x00, //0x00002d60 vpinsrq      $0, %rdi, %xmm0, %xmm0
	0x83, 0xe6, 0xf0, //0x00002d66 andl         $-16, %esi
	0xc4, 0xe3, 0x7d, 0x02, 0x05, 0xed, 0xd3, 0xff, 0xff, 0xf0, //0x00002d69 vpblendd     $240, $-11283(%rip), %ymm0, %ymm0  /* LCPI0_22+0(%rip) */
	0x8d, 0x4e, 0xf0, //0x00002d73 leal         $-16(%rsi), %ecx
	0x89, 0xcf, //0x00002d76 movl         %ecx, %edi
	0xc1, 0xef, 0x04, //0x00002d78 shrl         $4, %edi
	0x83, 0xc7, 0x01, //0x00002d7b addl         $1, %edi
	0x89, 0xfb, //0x00002d7e movl         %edi, %ebx
	0x83, 0xe3, 0x03, //0x00002d80 andl         $3, %ebx
	0x83, 0xf9, 0x30, //0x00002d83 cmpl         $48, %ecx
	0x0f, 0x83, 0x2c, 0x00, 0x00, 0x00, //0x00002d86 jae          LBB0_656
	0xc4, 0xe2, 0x7d, 0x59, 0x15, 0x8b, 0xd4, 0xff, 0xff, //0x00002d8c vpbroadcastq $-11125(%rip), %ymm2  /* LCPI0_23+0(%rip) */
	0xc5, 0xfd, 0x6f, 0xda, //0x00002d95 vmovdqa      %ymm2, %ymm3
	0xc5, 0xfd, 0x6f, 0xca, //0x00002d99 vmovdqa      %ymm2, %ymm1
	0xe9, 0x94, 0x00, 0x00, 0x00, //0x00002d9d jmp          LBB0_658
	//0x00002da2 LBB0_654
	0x83, 0xc1, 0xff, //0x00002da2 addl         $-1, %ecx
	0x31, 0xff, //0x00002da5 xorl         %edi, %edi
	0x89, 0xc8, //0x00002da7 movl         %ecx, %eax
	0x45, 0x31, 0xe4, //0x00002da9 xorl         %r12d, %r12d
	0xe9, 0xe0, 0x01, 0x00, 0x00, //0x00002dac jmp          LBB0_666
	//0x00002db1 LBB0_655
	0x31, 0xff, //0x00002db1 xorl         %edi, %edi
	0xe9, 0x22, 0x02, 0x00, 0x00, //0x00002db3 jmp          LBB0_672
	//0x00002db8 LBB0_656
	0x83, 0xe7, 0xfc, //0x00002db8 andl         $-4, %edi
	0xc4, 0xe2, 0x7d, 0x59, 0x15, 0x5c, 0xd4, 0xff, 0xff, //0x00002dbb vpbroadcastq $-11172(%rip), %ymm2  /* LCPI0_23+0(%rip) */
	0xc4, 0xe2, 0x7d, 0x59, 0x25, 0x5b, 0xd4, 0xff, 0xff, //0x00002dc4 vpbroadcastq $-11173(%rip), %ymm4  /* LCPI0_24+0(%rip) */
	0xc5, 0xfd, 0x6f, 0xda, //0x00002dcd vmovdqa      %ymm2, %ymm3
	0xc5, 0xfd, 0x6f, 0xca, //0x00002dd1 vmovdqa      %ymm2, %ymm1
	//0x00002dd5 LBB0_657
	0xc5, 0xfd, 0xf4, 0xec, //0x00002dd5 vpmuludq     %ymm4, %ymm0, %ymm5
	0xc5, 0xfd, 0x73, 0xd0, 0x20, //0x00002dd9 vpsrlq       $32, %ymm0, %ymm0
	0xc5, 0xfd, 0xf4, 0xc4, //0x00002dde vpmuludq     %ymm4, %ymm0, %ymm0
	0xc5, 0xfd, 0x73, 0xf0, 0x20, //0x00002de2 vpsllq       $32, %ymm0, %ymm0
	0xc5, 0xd5, 0xd4, 0xc0, //0x00002de7 vpaddq       %ymm0, %ymm5, %ymm0
	0xc5, 0xed, 0xf4, 0xec, //0x00002deb vpmuludq     %ymm4, %ymm2, %ymm5
	0xc5, 0xed, 0x73, 0xd2, 0x20, //0x00002def vpsrlq       $32, %ymm2, %ymm2
	0xc5, 0xed, 0xf4, 0xd4, //0x00002df4 vpmuludq     %ymm4, %ymm2, %ymm2
	0xc5, 0xed, 0x73, 0xf2, 0x20, //0x00002df8 vpsllq       $32, %ymm2, %ymm2
	0xc5, 0xd5, 0xd4, 0xd2, //0x00002dfd vpaddq       %ymm2, %ymm5, %ymm2
	0xc5, 0xe5, 0xf4, 0xec, //0x00002e01 vpmuludq     %ymm4, %ymm3, %ymm5
	0xc5, 0xe5, 0x73, 0xd3, 0x20, //0x00002e05 vpsrlq       $32, %ymm3, %ymm3
	0xc5, 0xe5, 0xf4, 0xdc, //0x00002e0a vpmuludq     %ymm4, %ymm3, %ymm3
	0xc5, 0xe5, 0x73, 0xf3, 0x20, //0x00002e0e vpsllq       $32, %ymm3, %ymm3
	0xc5, 0xd5, 0xd4, 0xdb, //0x00002e13 vpaddq       %ymm3, %ymm5, %ymm3
	0xc5, 0xf5, 0xf4, 0xec, //0x00002e17 vpmuludq     %ymm4, %ymm1, %ymm5
	0xc5, 0xf5, 0x73, 0xd1, 0x20, //0x00002e1b vpsrlq       $32, %ymm1, %ymm1
	0xc5, 0xf5, 0xf4, 0xcc, //0x00002e20 vpmuludq     %ymm4, %ymm1, %ymm1
	0xc5, 0xf5, 0x73, 0xf1, 0x20, //0x00002e24 vpsllq       $32, %ymm1, %ymm1
	0xc5, 0xd5, 0xd4, 0xc9, //0x00002e29 vpaddq       %ymm1, %ymm5, %ymm1
	0x83, 0xc7, 0xfc, //0x00002e2d addl         $-4, %edi
	0x0f, 0x85, 0x9f, 0xff, 0xff, 0xff, //0x00002e30 jne          LBB0_657
	//0x00002e36 LBB0_658
	0x85, 0xdb, //0x00002e36 testl        %ebx, %ebx
	0x0f, 0x84, 0x6a, 0x00, 0x00, 0x00, //0x00002e38 je           LBB0_661
	0xc4, 0xe2, 0x7d, 0x59, 0x25, 0xe9, 0xd3, 0xff, 0xff, //0x00002e3e vpbroadcastq $-11287(%rip), %ymm4  /* LCPI0_25+0(%rip) */
	//0x00002e47 LBB0_660
	0xc5, 0xfd, 0xf4, 0xec, //0x00002e47 vpmuludq     %ymm4, %ymm0, %ymm5
	0xc5, 0xfd, 0x73, 0xd0, 0x20, //0x00002e4b vpsrlq       $32, %ymm0, %ymm0
	0xc5, 0xfd, 0xf4, 0xc4, //0x00002e50 vpmuludq     %ymm4, %ymm0, %ymm0
	0xc5, 0xfd, 0x73, 0xf0, 0x20, //0x00002e54 vpsllq       $32, %ymm0, %ymm0
	0xc5, 0xd5, 0xd4, 0xc0, //0x00002e59 vpaddq       %ymm0, %ymm5, %ymm0
	0xc5, 0xed, 0xf4, 0xec, //0x00002e5d vpmuludq     %ymm4, %ymm2, %ymm5
	0xc5, 0xed, 0x73, 0xd2, 0x20, //0x00002e61 vpsrlq       $32, %ymm2, %ymm2
	0xc5, 0xed, 0xf4, 0xd4, //0x00002e66 vpmuludq     %ymm4, %ymm2, %ymm2
	0xc5, 0xed, 0x73, 0xf2, 0x20, //0x00002e6a vpsllq       $32, %ymm2, %ymm2
	0xc5, 0xd5, 0xd4, 0xd2, //0x00002e6f vpaddq       %ymm2, %ymm5, %ymm2
	0xc5, 0xe5, 0xf4, 0xec, //0x00002e73 vpmuludq     %ymm4, %ymm3, %ymm5
	0xc5, 0xe5, 0x73, 0xd3, 0x20, //0x00002e77 vpsrlq       $32, %ymm3, %ymm3
	0xc5, 0xe5, 0xf4, 0xdc, //0x00002e7c vpmuludq     %ymm4, %ymm3, %ymm3
	0xc5, 0xe5, 0x73, 0xf3, 0x20, //0x00002e80 vpsllq       $32, %ymm3, %ymm3
	0xc5, 0xd5, 0xd4, 0xdb, //0x00002e85 vpaddq       %ymm3, %ymm5, %ymm3
	0xc5, 0xf5, 0xf4, 0xec, //0x00002e89 vpmuludq     %ymm4, %ymm1, %ymm5
	0xc5, 0xf5, 0x73, 0xd1, 0x20, //0x00002e8d vpsrlq       $32, %ymm1, %ymm1
	0xc5, 0xf5, 0xf4, 0xcc, //0x00002e92 vpmuludq     %ymm4, %ymm1, %ymm1
	0xc5, 0xf5, 0x73, 0xf1, 0x20, //0x00002e96 vpsllq       $32, %ymm1, %ymm1
	0xc5, 0xd5, 0xd4, 0xc9, //0x00002e9b vpaddq       %ymm1, %ymm5, %ymm1
	0x83, 0xc3, 0xff, //0x00002e9f addl         $-1, %ebx
	0x0f, 0x85, 0x9f, 0xff, 0xff, 0xff, //0x00002ea2 jne          LBB0_660
	//0x00002ea8 LBB0_661
	0xc5, 0xdd, 0x73, 0xd2, 0x20, //0x00002ea8 vpsrlq       $32, %ymm2, %ymm4
	0xc5, 0xdd, 0xf4, 0xe0, //0x00002ead vpmuludq     %ymm0, %ymm4, %ymm4
	0xc5, 0xd5, 0x73, 0xd0, 0x20, //0x00002eb1 vpsrlq       $32, %ymm0, %ymm5
	0xc5, 0xed, 0xf4, 0xed, //0x00002eb6 vpmuludq     %ymm5, %ymm2, %ymm5
	0xc5, 0xd5, 0xd4, 0xe4, //0x00002eba vpaddq       %ymm4, %ymm5, %ymm4
	0xc5, 0xdd, 0x73, 0xf4, 0x20, //0x00002ebe vpsllq       $32, %ymm4, %ymm4
	0xc5, 0xed, 0xf4, 0xc0, //0x00002ec3 vpmuludq     %ymm0, %ymm2, %ymm0
	0xc5, 0xfd, 0xd4, 0xc4, //0x00002ec7 vpaddq       %ymm4, %ymm0, %ymm0
	0xc5, 0xed, 0x73, 0xd3, 0x20, //0x00002ecb vpsrlq       $32, %ymm3, %ymm2
	0xc5, 0xed, 0xf4, 0xd0, //0x00002ed0 vpmuludq     %ymm0, %ymm2, %ymm2
	0xc5, 0xdd, 0x73, 0xd0, 0x20, //0x00002ed4 vpsrlq       $32, %ymm0, %ymm4
	0xc5, 0xe5, 0xf4, 0xe4, //0x00002ed9 vpmuludq     %ymm4, %ymm3, %ymm4
	0xc5, 0xdd, 0xd4, 0xd2, //0x00002edd vpaddq       %ymm2, %ymm4, %ymm2
	0xc5, 0xed, 0x73, 0xf2, 0x20, //0x00002ee1 vpsllq       $32, %ymm2, %ymm2
	0xc5, 0xe5, 0xf4, 0xc0, //0x00002ee6 vpmuludq     %ymm0, %ymm3, %ymm0
	0xc5, 0xfd, 0xd4, 0xc2, //0x00002eea vpaddq       %ymm2, %ymm0, %ymm0
	0xc5, 0xed, 0x73, 0xd1, 0x20, //0x00002eee vpsrlq       $32, %ymm1, %ymm2
	0xc5, 0xed, 0xf4, 0xd0, //0x00002ef3 vpmuludq     %ymm0, %ymm2, %ymm2
	0xc5, 0xe5, 0x73, 0xd0, 0x20, //0x00002ef7 vpsrlq       $32, %ymm0, %ymm3
	0xc5, 0xf5, 0xf4, 0xdb, //0x00002efc vpmuludq     %ymm3, %ymm1, %ymm3
	0xc5, 0xe5, 0xd4, 0xd2, //0x00002f00 vpaddq       %ymm2, %ymm3, %ymm2
	0xc5, 0xed, 0x73, 0xf2, 0x20, //0x00002f04 vpsllq       $32, %ymm2, %ymm2
	0xc5, 0xf5, 0xf4, 0xc0, //0x00002f09 vpmuludq     %ymm0, %ymm1, %ymm0
	0xc5, 0xfd, 0xd4, 0xc2, //0x00002f0d vpaddq       %ymm2, %ymm0, %ymm0
	0xc4, 0xe3, 0x7d, 0x39, 0xc1, 0x01, //0x00002f11 vextracti128 $1, %ymm0, %xmm1
	0xc5, 0xe9, 0x73, 0xd0, 0x20, //0x00002f17 vpsrlq       $32, %xmm0, %xmm2
	0xc5, 0xe9, 0xf4, 0xd1, //0x00002f1c vpmuludq     %xmm1, %xmm2, %xmm2
	0xc5, 0xe1, 0x73, 0xd1, 0x20, //0x00002f20 vpsrlq       $32, %xmm1, %xmm3
	0xc5, 0xf9, 0xf4, 0xdb, //0x00002f25 vpmuludq     %xmm3, %xmm0, %xmm3
	0xc5, 0xe1, 0xd4, 0xd2, //0x00002f29 vpaddq       %xmm2, %xmm3, %xmm2
	0xc5, 0xe9, 0x73, 0xf2, 0x20, //0x00002f2d vpsllq       $32, %xmm2, %xmm2
	0xc5, 0xf9, 0xf4, 0xc1, //0x00002f32 vpmuludq     %xmm1, %xmm0, %xmm0
	0xc5, 0xf9, 0xd4, 0xc2, //0x00002f36 vpaddq       %xmm2, %xmm0, %xmm0
	0xc5, 0xf9, 0x70, 0xc8, 0xee, //0x00002f3a vpshufd      $238, %xmm0, %xmm1
	0xc5, 0xe9, 0x73, 0xd0, 0x20, //0x00002f3f vpsrlq       $32, %xmm0, %xmm2
	0xc5, 0xe9, 0xf4, 0xd1, //0x00002f44 vpmuludq     %xmm1, %xmm2, %xmm2
	0xc5, 0xf9, 0x70, 0xd8, 0xff, //0x00002f48 vpshufd      $255, %xmm0, %xmm3
	0xc5, 0xf9, 0xf4, 0xdb, //0x00002f4d vpmuludq     %xmm3, %xmm0, %xmm3
	0xc5, 0xe1, 0xd4, 0xd2, //0x00002f51 vpaddq       %xmm2, %xmm3, %xmm2
	0xc5, 0xe9, 0x73, 0xf2, 0x20, //0x00002f55 vpsllq       $32, %xmm2, %xmm2
	0xc5, 0xf9, 0xf4, 0xc1, //0x00002f5a vpmuludq     %xmm1, %xmm0, %xmm0
	0xc5, 0xf9, 0xd4, 0xc2, //0x00002f5e vpaddq       %xmm2, %xmm0, %xmm0
	0xc4, 0xe1, 0xf9, 0x7e, 0xc7, //0x00002f62 vmovq        %xmm0, %rdi
	0x39, 0xf2, //0x00002f67 cmpl         %esi, %edx
	0x0f, 0x84, 0x19, 0x00, 0x00, 0x00, //0x00002f69 je           LBB0_665
	0x41, 0x01, 0xf1, //0x00002f6f addl         %esi, %r9d
	//0x00002f72 LBB0_663
	0x44, 0x89, 0xe1, //0x00002f72 movl         %r12d, %ecx
	0x44, 0x29, 0xc9, //0x00002f75 subl         %r9d, %ecx
	//0x00002f78 LBB0_664
	0x48, 0x01, 0xff, //0x00002f78 addq         %rdi, %rdi
	0x48, 0x8d, 0x3c, 0xbf, //0x00002f7b leaq         (%rdi,%rdi,4), %rdi
	0x83, 0xc1, 0xff, //0x00002f7f addl         $-1, %ecx
	0x0f, 0x85, 0xf0, 0xff, 0xff, 0xff, //0x00002f82 jne          LBB0_664
	//0x00002f88 LBB0_665
	0x45, 0x85, 0xe4, //0x00002f88 testl        %r12d, %r12d
	0x0f, 0x88, 0x49, 0x00, 0x00, 0x00, //0x00002f8b js           LBB0_672
	//0x00002f91 LBB0_666
	0x44, 0x39, 0xe0, //0x00002f91 cmpl         %r12d, %eax
	0x0f, 0x8e, 0x40, 0x00, 0x00, 0x00, //0x00002f94 jle          LBB0_672
	0x44, 0x89, 0xe1, //0x00002f9a movl         %r12d, %ecx
	0x41, 0x8a, 0x0c, 0x08, //0x00002f9d movb         (%r8,%rcx), %cl
	0x80, 0xf9, 0x35, //0x00002fa1 cmpb         $53, %cl
	0x0f, 0x85, 0x00, 0x01, 0x00, 0x00, //0x00002fa4 jne          LBB0_683
	0x41, 0x8d, 0x54, 0x24, 0x01, //0x00002faa leal         $1(%r12), %edx
	0x39, 0xc2, //0x00002faf cmpl         %eax, %edx
	0x0f, 0x85, 0xf3, 0x00, 0x00, 0x00, //0x00002fb1 jne          LBB0_683
	0xb0, 0x01, //0x00002fb7 movb         $1, %al
	0x45, 0x85, 0xd2, //0x00002fb9 testl        %r10d, %r10d
	0x0f, 0x85, 0x1a, 0x00, 0x00, 0x00, //0x00002fbc jne          LBB0_673
	0x45, 0x85, 0xe4, //0x00002fc2 testl        %r12d, %r12d
	0x0f, 0x84, 0x0f, 0x00, 0x00, 0x00, //0x00002fc5 je           LBB0_672
	0x41, 0x83, 0xc4, 0xff, //0x00002fcb addl         $-1, %r12d
	0x43, 0x8a, 0x04, 0x20, //0x00002fcf movb         (%r8,%r12), %al
	0x24, 0x01, //0x00002fd3 andb         $1, %al
	0xe9, 0x02, 0x00, 0x00, 0x00, //0x00002fd5 jmp          LBB0_673
	//0x00002fda LBB0_672
	0x31, 0xc0, //0x00002fda xorl         %eax, %eax
	//0x00002fdc LBB0_673
	0x48, 0x8b, 0x5d, 0xc8, //0x00002fdc movq         $-56(%rbp), %rbx
	0x0f, 0xb6, 0xf0, //0x00002fe0 movzbl       %al, %esi
	0x48, 0x01, 0xfe, //0x00002fe3 addq         %rdi, %rsi
	0x48, 0xb8, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x20, 0x00, //0x00002fe6 movabsq      $9007199254740992, %rax
	0x48, 0x39, 0xc6, //0x00002ff0 cmpq         %rax, %rsi
	0x0f, 0x85, 0x7f, 0x00, 0x00, 0x00, //0x00002ff3 jne          LBB0_681
	0x81, 0xfb, 0xfe, 0x03, 0x00, 0x00, //0x00002ff9 cmpl         $1022, %ebx
	0x0f, 0x8e, 0x89, 0x00, 0x00, 0x00, //0x00002fff jle          LBB0_682
	//0x00003005 LBB0_675
	0x31, 0xf6, //0x00003005 xorl         %esi, %esi
	0x48, 0xbb, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xf0, 0x7f, //0x00003007 movabsq      $9218868437227405312, %rbx
	//0x00003011 LBB0_676
	0x49, 0xbe, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x80, //0x00003011 movabsq      $-9223372036854775808, %r14
	0x8a, 0x55, 0xd7, //0x0000301b movb         $-41(%rbp), %dl
	//0x0000301e LBB0_677
	0x48, 0xb8, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x0f, 0x00, //0x0000301e movabsq      $4503599627370495, %rax
	0x48, 0x21, 0xc6, //0x00003028 andq         %rax, %rsi
	0x48, 0x09, 0xde, //0x0000302b orq          %rbx, %rsi
	0x48, 0x89, 0xf0, //0x0000302e movq         %rsi, %rax
	0x4c, 0x09, 0xf0, //0x00003031 orq          %r14, %rax
	0x80, 0xfa, 0x2d, //0x00003034 cmpb         $45, %dl
	0x48, 0x0f, 0x45, 0xc6, //0x00003037 cmovneq      %rsi, %rax
	0xc4, 0xe1, 0xf9, 0x6e, 0xc0, //0x0000303b vmovq        %rax, %xmm0
	//0x00003040 LBB0_678
	0xc4, 0xe1, 0xf9, 0x7e, 0xc0, //0x00003040 vmovq        %xmm0, %rax
	0x49, 0x83, 0xc6, 0xff, //0x00003045 addq         $-1, %r14
	0x49, 0x21, 0xc6, //0x00003049 andq         %rax, %r14
	0x48, 0xb8, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xf0, 0x7f, //0x0000304c movabsq      $9218868437227405312, %rax
	0x49, 0x39, 0xc6, //0x00003056 cmpq         %rax, %r14
	0x48, 0x8b, 0x45, 0xb8, //0x00003059 movq         $-72(%rbp), %rax
	0x0f, 0x85, 0x07, 0x00, 0x00, 0x00, //0x0000305d jne          LBB0_680
	0x48, 0xc7, 0x00, 0xf8, 0xff, 0xff, 0xff, //0x00003063 movq         $-8, (%rax)
	//0x0000306a LBB0_680
	0xc5, 0xfb, 0x11, 0x40, 0x08, //0x0000306a vmovsd       %xmm0, $8(%rax)
	0x4c, 0x8b, 0x55, 0xa8, //0x0000306f movq         $-88(%rbp), %r10
	0xe9, 0x8b, 0xe3, 0xff, 0xff, //0x00003073 jmp          LBB0_276
	//0x00003078 LBB0_681
	0x49, 0xbe, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x80, //0x00003078 movabsq      $-9223372036854775808, %r14
	0x48, 0x8b, 0x4d, 0x98, //0x00003082 movq         $-104(%rbp), %rcx
	0x8a, 0x55, 0xd7, //0x00003086 movb         $-41(%rbp), %dl
	0xe9, 0x3f, 0x00, 0x00, 0x00, //0x00003089 jmp          LBB0_685
	//0x0000308e LBB0_682
	0x83, 0xc3, 0x01, //0x0000308e addl         $1, %ebx
	0x48, 0x8b, 0x4d, 0x98, //0x00003091 movq         $-104(%rbp), %rcx
	0x48, 0x89, 0xce, //0x00003095 movq         %rcx, %rsi
	0x49, 0xbe, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x80, //0x00003098 movabsq      $-9223372036854775808, %r14
	0x8a, 0x55, 0xd7, //0x000030a2 movb         $-41(%rbp), %dl
	0xe9, 0x23, 0x00, 0x00, 0x00, //0x000030a5 jmp          LBB0_685
	//0x000030aa LBB0_683
	0x80, 0xf9, 0x35, //0x000030aa cmpb         $53, %cl
	0x0f, 0x9d, 0xc0, //0x000030ad setge        %al
	0xe9, 0x27, 0xff, 0xff, 0xff, //0x000030b0 jmp          LBB0_673
	//0x000030b5 LBB0_684
	0x31, 0xf6, //0x000030b5 xorl         %esi, %esi
	0x49, 0xbe, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x80, //0x000030b7 movabsq      $-9223372036854775808, %r14
	0x48, 0x8b, 0x4d, 0x98, //0x000030c1 movq         $-104(%rbp), %rcx
	0x8a, 0x55, 0xd7, //0x000030c5 movb         $-41(%rbp), %dl
	0xbb, 0x02, 0xfc, 0xff, 0xff, //0x000030c8 movl         $-1022, %ebx
	//0x000030cd LBB0_685
	0x48, 0x21, 0xf1, //0x000030cd andq         %rsi, %rcx
	0x81, 0xc3, 0xff, 0x03, 0x00, 0x00, //0x000030d0 addl         $1023, %ebx
	0x81, 0xe3, 0xff, 0x07, 0x00, 0x00, //0x000030d6 andl         $2047, %ebx
	0x48, 0xc1, 0xe3, 0x34, //0x000030dc shlq         $52, %rbx
	0x48, 0x85, 0xc9, //0x000030e0 testq        %rcx, %rcx
	0x48, 0x0f, 0x44, 0xd9, //0x000030e3 cmoveq       %rcx, %rbx
	0xe9, 0x32, 0xff, 0xff, 0xff, //0x000030e7 jmp          LBB0_677
	//0x000030ec LBB0_686
	0x45, 0x31, 0xf6, //0x000030ec xorl         %r14d, %r14d
	0x48, 0x8b, 0x4d, 0xc8, //0x000030ef movq         $-56(%rbp), %rcx
	0xe9, 0x52, 0xee, 0xff, 0xff, //0x000030f3 jmp          LBB0_442
	//0x000030f8 LBB0_688
	0x48, 0xc7, 0xc0, 0xff, 0xff, 0xff, 0xff, //0x000030f8 movq         $-1, %rax
	0xe9, 0xbd, 0xde, 0xff, 0xff, //0x000030ff jmp          LBB0_207
	//0x00003104 LBB0_689
	0x4c, 0x8d, 0x24, 0x17, //0x00003104 leaq         (%rdi,%rdx), %r12
	0x49, 0xc7, 0xc0, 0xff, 0xff, 0xff, 0xff, //0x00003108 movq         $-1, %r8
	0x45, 0x31, 0xff, //0x0000310f xorl         %r15d, %r15d
	0x49, 0x83, 0xfa, 0x20, //0x00003112 cmpq         $32, %r10
	0x0f, 0x83, 0x6e, 0xe7, 0xff, 0xff, //0x00003116 jae          LBB0_327
	0xe9, 0x91, 0x00, 0x00, 0x00, //0x0000311c jmp          LBB0_697
	//0x00003121 LBB0_690
	0x4c, 0x89, 0xf7, //0x00003121 movq         %r14, %rdi
	0x4d, 0x8d, 0x24, 0x16, //0x00003124 leaq         (%r14,%rdx), %r12
	0x49, 0xc7, 0xc0, 0xff, 0xff, 0xff, 0xff, //0x00003128 movq         $-1, %r8
	0x45, 0x31, 0xff, //0x0000312f xorl         %r15d, %r15d
	0x49, 0x83, 0xfa, 0x20, //0x00003132 cmpq         $32, %r10
	0x0f, 0x83, 0xad, 0xe7, 0xff, 0xff, //0x00003136 jae          LBB0_332
	0xe9, 0xd4, 0x01, 0x00, 0x00, //0x0000313c jmp          LBB0_719
	//0x00003141 LBB0_691
	0x48, 0xc7, 0xc0, 0xfe, 0xff, 0xff, 0xff, //0x00003141 movq         $-2, %rax
	0xe9, 0x74, 0xde, 0xff, 0xff, //0x00003148 jmp          LBB0_207
	//0x0000314d LBB0_692
	0x49, 0x89, 0xf9, //0x0000314d movq         %rdi, %r9
	0x49, 0x83, 0xf8, 0xff, //0x00003150 cmpq         $-1, %r8
	0x0f, 0x85, 0x15, 0x00, 0x00, 0x00, //0x00003154 jne          LBB0_695
	0x4c, 0x89, 0xe0, //0x0000315a movq         %r12, %rax
	0x4c, 0x29, 0xc8, //0x0000315d subq         %r9, %rax
	0x4c, 0x0f, 0xbc, 0xc6, //0x00003160 bsfq         %rsi, %r8
	0x49, 0x01, 0xc0, //0x00003164 addq         %rax, %r8
	0xe9, 0x03, 0x00, 0x00, 0x00, //0x00003167 jmp          LBB0_695
	//0x0000316c LBB0_694
	0x49, 0x89, 0xf9, //0x0000316c movq         %rdi, %r9
	//0x0000316f LBB0_695
	0x44, 0x89, 0xf8, //0x0000316f movl         %r15d, %eax
	0xf7, 0xd0, //0x00003172 notl         %eax
	0x21, 0xf0, //0x00003174 andl         %esi, %eax
	0x8d, 0x3c, 0x00, //0x00003176 leal         (%rax,%rax), %edi
	0x41, 0x8d, 0x1c, 0x47, //0x00003179 leal         (%r15,%rax,2), %ebx
	0xf7, 0xd7, //0x0000317d notl         %edi
	0x21, 0xf7, //0x0000317f andl         %esi, %edi
	0x81, 0xe7, 0xaa, 0xaa, 0xaa, 0xaa, //0x00003181 andl         $-1431655766, %edi
	0x45, 0x31, 0xff, //0x00003187 xorl         %r15d, %r15d
	0x01, 0xc7, //0x0000318a addl         %eax, %edi
	0x41, 0x0f, 0x92, 0xc7, //0x0000318c setb         %r15b
	0x01, 0xff, //0x00003190 addl         %edi, %edi
	0x81, 0xf7, 0x55, 0x55, 0x55, 0x55, //0x00003192 xorl         $1431655765, %edi
	0x21, 0xdf, //0x00003198 andl         %ebx, %edi
	0xf7, 0xd7, //0x0000319a notl         %edi
	0x21, 0xf9, //0x0000319c andl         %edi, %ecx
	0x4c, 0x89, 0xcf, //0x0000319e movq         %r9, %rdi
	0x48, 0x85, 0xc9, //0x000031a1 testq        %rcx, %rcx
	0x0f, 0x85, 0x1b, 0xe7, 0xff, 0xff, //0x000031a4 jne          LBB0_330
	//0x000031aa LBB0_696
	0x49, 0x83, 0xc4, 0x20, //0x000031aa addq         $32, %r12
	0x49, 0x83, 0xc2, 0xe0, //0x000031ae addq         $-32, %r10
	//0x000031b2 LBB0_697
	0x4d, 0x85, 0xff, //0x000031b2 testq        %r15, %r15
	0x0f, 0x85, 0xfd, 0x01, 0x00, 0x00, //0x000031b5 jne          LBB0_731
	0x4d, 0x89, 0xc1, //0x000031bb movq         %r8, %r9
	0x4d, 0x85, 0xd2, //0x000031be testq        %r10, %r10
	0x0f, 0x84, 0x88, 0x00, 0x00, 0x00, //0x000031c1 je           LBB0_707
	//0x000031c7 LBB0_699
	0x48, 0x89, 0xfe, //0x000031c7 movq         %rdi, %rsi
	0x48, 0xf7, 0xde, //0x000031ca negq         %rsi
	//0x000031cd LBB0_700
	0x31, 0xdb, //0x000031cd xorl         %ebx, %ebx
	//0x000031cf LBB0_701
	0x41, 0x0f, 0xb6, 0x0c, 0x1c, //0x000031cf movzbl       (%r12,%rbx), %ecx
	0x80, 0xf9, 0x22, //0x000031d4 cmpb         $34, %cl
	0x0f, 0x84, 0x6b, 0x00, 0x00, 0x00, //0x000031d7 je           LBB0_706
	0x80, 0xf9, 0x5c, //0x000031dd cmpb         $92, %cl
	0x0f, 0x84, 0x12, 0x00, 0x00, 0x00, //0x000031e0 je           LBB0_704
	0x48, 0x83, 0xc3, 0x01, //0x000031e6 addq         $1, %rbx
	0x49, 0x39, 0xda, //0x000031ea cmpq         %rbx, %r10
	0x0f, 0x85, 0xdc, 0xff, 0xff, 0xff, //0x000031ed jne          LBB0_701
	0xe9, 0x62, 0x00, 0x00, 0x00, //0x000031f3 jmp          LBB0_708
	//0x000031f8 LBB0_704
	0x49, 0x8d, 0x42, 0xff, //0x000031f8 leaq         $-1(%r10), %rax
	0x48, 0x39, 0xd8, //0x000031fc cmpq         %rbx, %rax
	0x0f, 0x84, 0x51, 0x02, 0x00, 0x00, //0x000031ff je           LBB0_738
	0x4a, 0x8d, 0x04, 0x26, //0x00003205 leaq         (%rsi,%r12), %rax
	0x48, 0x01, 0xd8, //0x00003209 addq         %rbx, %rax
	0x49, 0x83, 0xf9, 0xff, //0x0000320c cmpq         $-1, %r9
	0x4c, 0x0f, 0x44, 0xc0, //0x00003210 cmoveq       %rax, %r8
	0x4c, 0x0f, 0x44, 0xc8, //0x00003214 cmoveq       %rax, %r9
	0x49, 0x01, 0xdc, //0x00003218 addq         %rbx, %r12
	0x49, 0x83, 0xc4, 0x02, //0x0000321b addq         $2, %r12
	0x4c, 0x89, 0xd0, //0x0000321f movq         %r10, %rax
	0x48, 0x29, 0xd8, //0x00003222 subq         %rbx, %rax
	0x48, 0x83, 0xc0, 0xfe, //0x00003225 addq         $-2, %rax
	0x49, 0x83, 0xc2, 0xfe, //0x00003229 addq         $-2, %r10
	0x49, 0x39, 0xda, //0x0000322d cmpq         %rbx, %r10
	0x49, 0x89, 0xc2, //0x00003230 movq         %rax, %r10
	0x4d, 0x89, 0xf7, //0x00003233 movq         %r14, %r15
	0x48, 0xc7, 0xc0, 0xff, 0xff, 0xff, 0xff, //0x00003236 movq         $-1, %rax
	0x0f, 0x85, 0x8a, 0xff, 0xff, 0xff, //0x0000323d jne          LBB0_700
	0xe9, 0x79, 0xdd, 0xff, 0xff, //0x00003243 jmp          LBB0_207
	//0x00003248 LBB0_706
	0x49, 0x01, 0xdc, //0x00003248 addq         %rbx, %r12
	0x49, 0x83, 0xc4, 0x01, //0x0000324b addq         $1, %r12
	//0x0000324f LBB0_707
	0x49, 0x29, 0xfc, //0x0000324f subq         %rdi, %r12
	0x4d, 0x89, 0xe2, //0x00003252 movq         %r12, %r10
	0xe9, 0x62, 0xd8, 0xff, 0xff, //0x00003255 jmp          LBB0_126
	//0x0000325a LBB0_708
	0x48, 0xc7, 0xc0, 0xff, 0xff, 0xff, 0xff, //0x0000325a movq         $-1, %rax
	0x80, 0xf9, 0x22, //0x00003261 cmpb         $34, %cl
	0x4d, 0x89, 0xf7, //0x00003264 movq         %r14, %r15
	0x0f, 0x85, 0x54, 0xdd, 0xff, 0xff, //0x00003267 jne          LBB0_207
	0x4d, 0x01, 0xd4, //0x0000326d addq         %r10, %r12
	0xe9, 0xda, 0xff, 0xff, 0xff, //0x00003270 jmp          LBB0_707
	//0x00003275 LBB0_710
	0x49, 0x83, 0xf8, 0xff, //0x00003275 cmpq         $-1, %r8
	0x0f, 0x85, 0x0d, 0x00, 0x00, 0x00, //0x00003279 jne          LBB0_712
	0x4c, 0x89, 0xe6, //0x0000327f movq         %r12, %rsi
	0x4c, 0x29, 0xf6, //0x00003282 subq         %r14, %rsi
	0x4c, 0x0f, 0xbc, 0xc1, //0x00003285 bsfq         %rcx, %r8
	0x49, 0x01, 0xf0, //0x00003289 addq         %rsi, %r8
	//0x0000328c LBB0_712
	0x44, 0x89, 0xfe, //0x0000328c movl         %r15d, %esi
	0xf7, 0xd6, //0x0000328f notl         %esi
	0x21, 0xce, //0x00003291 andl         %ecx, %esi
	0x8d, 0x3c, 0x36, //0x00003293 leal         (%rsi,%rsi), %edi
	0x41, 0x8d, 0x1c, 0x77, //0x00003296 leal         (%r15,%rsi,2), %ebx
	0xf7, 0xd7, //0x0000329a notl         %edi
	0x21, 0xcf, //0x0000329c andl         %ecx, %edi
	0x81, 0xe7, 0xaa, 0xaa, 0xaa, 0xaa, //0x0000329e andl         $-1431655766, %edi
	0x45, 0x31, 0xff, //0x000032a4 xorl         %r15d, %r15d
	0x01, 0xf7, //0x000032a7 addl         %esi, %edi
	0x41, 0x0f, 0x92, 0xc7, //0x000032a9 setb         %r15b
	0x01, 0xff, //0x000032ad addl         %edi, %edi
	0x81, 0xf7, 0x55, 0x55, 0x55, 0x55, //0x000032af xorl         $1431655765, %edi
	0x21, 0xdf, //0x000032b5 andl         %ebx, %edi
	0xf7, 0xd7, //0x000032b7 notl         %edi
	0x21, 0xf8, //0x000032b9 andl         %edi, %eax
	0x4c, 0x89, 0xf7, //0x000032bb movq         %r14, %rdi
	0xc5, 0xfd, 0x74, 0xc1, //0x000032be vpcmpeqb     %ymm1, %ymm0, %ymm0
	0x48, 0x85, 0xc0, //0x000032c2 testq        %rax, %rax
	0x0f, 0x85, 0x65, 0xe6, 0xff, 0xff, //0x000032c5 jne          LBB0_335
	//0x000032cb LBB0_713
	0xb9, 0x40, 0x00, 0x00, 0x00, //0x000032cb movl         $64, %ecx
	//0x000032d0 LBB0_714
	0xc5, 0xfd, 0xd7, 0xf0, //0x000032d0 vpmovmskb    %ymm0, %esi
	0x48, 0x85, 0xc0, //0x000032d4 testq        %rax, %rax
	0x0f, 0x84, 0x28, 0x00, 0x00, 0x00, //0x000032d7 je           LBB0_717
	0x0f, 0xbc, 0xc6, //0x000032dd bsfl         %esi, %eax
	0xbe, 0x40, 0x00, 0x00, 0x00, //0x000032e0 movl         $64, %esi
	0x0f, 0x45, 0xf0, //0x000032e5 cmovnel      %eax, %esi
	0x48, 0x39, 0xf1, //0x000032e8 cmpq         %rsi, %rcx
	0x0f, 0x87, 0xa4, 0x00, 0x00, 0x00, //0x000032eb ja           LBB0_729
	0x49, 0x29, 0xfc, //0x000032f1 subq         %rdi, %r12
	0x4d, 0x8d, 0x14, 0x0c, //0x000032f4 leaq         (%r12,%rcx), %r10
	0x49, 0x83, 0xc2, 0x01, //0x000032f8 addq         $1, %r10
	0x4c, 0x8b, 0x7d, 0xb8, //0x000032fc movq         $-72(%rbp), %r15
	0xe9, 0x69, 0xdc, 0xff, 0xff, //0x00003300 jmp          LBB0_197
	//0x00003305 LBB0_717
	0x85, 0xf6, //0x00003305 testl        %esi, %esi
	0x0f, 0x85, 0x88, 0x00, 0x00, 0x00, //0x00003307 jne          LBB0_729
	0x49, 0x83, 0xc4, 0x20, //0x0000330d addq         $32, %r12
	0x49, 0x83, 0xc2, 0xe0, //0x00003311 addq         $-32, %r10
	//0x00003315 LBB0_719
	0x4d, 0x85, 0xff, //0x00003315 testq        %r15, %r15
	0x0f, 0x85, 0xd4, 0x00, 0x00, 0x00, //0x00003318 jne          LBB0_733
	0x4c, 0x89, 0xc0, //0x0000331e movq         %r8, %rax
	0x4d, 0x85, 0xd2, //0x00003321 testq        %r10, %r10
	0x0f, 0x84, 0xfd, 0x00, 0x00, 0x00, //0x00003324 je           LBB0_735
	//0x0000332a LBB0_721
	0x41, 0x0f, 0xb6, 0x0c, 0x24, //0x0000332a movzbl       (%r12), %ecx
	0x80, 0xf9, 0x22, //0x0000332f cmpb         $34, %cl
	0x0f, 0x84, 0x6d, 0x00, 0x00, 0x00, //0x00003332 je           LBB0_730
	0x80, 0xf9, 0x5c, //0x00003338 cmpb         $92, %cl
	0x0f, 0x84, 0x26, 0x00, 0x00, 0x00, //0x0000333b je           LBB0_726
	0x80, 0xf9, 0x20, //0x00003341 cmpb         $32, %cl
	0x0f, 0x82, 0x4b, 0x00, 0x00, 0x00, //0x00003344 jb           LBB0_729
	0x48, 0xc7, 0xc1, 0xff, 0xff, 0xff, 0xff, //0x0000334a movq         $-1, %rcx
	0xbe, 0x01, 0x00, 0x00, 0x00, //0x00003351 movl         $1, %esi
	//0x00003356 LBB0_725
	0x49, 0x01, 0xf4, //0x00003356 addq         %rsi, %r12
	0x49, 0x01, 0xca, //0x00003359 addq         %rcx, %r10
	0x0f, 0x85, 0xc8, 0xff, 0xff, 0xff, //0x0000335c jne          LBB0_721
	0xe9, 0xc0, 0x00, 0x00, 0x00, //0x00003362 jmp          LBB0_735
	//0x00003367 LBB0_726
	0x49, 0x83, 0xfa, 0x01, //0x00003367 cmpq         $1, %r10
	0x0f, 0x84, 0xb6, 0x00, 0x00, 0x00, //0x0000336b je           LBB0_735
	0x48, 0xc7, 0xc1, 0xfe, 0xff, 0xff, 0xff, //0x00003371 movq         $-2, %rcx
	0xbe, 0x02, 0x00, 0x00, 0x00, //0x00003378 movl         $2, %esi
	0x48, 0x83, 0xf8, 0xff, //0x0000337d cmpq         $-1, %rax
	0x0f, 0x85, 0xcf, 0xff, 0xff, 0xff, //0x00003381 jne          LBB0_725
	0x4d, 0x89, 0xe0, //0x00003387 movq         %r12, %r8
	0x4d, 0x29, 0xf0, //0x0000338a subq         %r14, %r8
	0x4c, 0x89, 0xc0, //0x0000338d movq         %r8, %rax
	0xe9, 0xc1, 0xff, 0xff, 0xff, //0x00003390 jmp          LBB0_725
	//0x00003395 LBB0_729
	0x48, 0xc7, 0xc0, 0xfe, 0xff, 0xff, 0xff, //0x00003395 movq         $-2, %rax
	0x4c, 0x8b, 0x7d, 0xb8, //0x0000339c movq         $-72(%rbp), %r15
	0xe9, 0x1c, 0xdc, 0xff, 0xff, //0x000033a0 jmp          LBB0_207
	//0x000033a5 LBB0_730
	0x4d, 0x29, 0xf4, //0x000033a5 subq         %r14, %r12
	0x49, 0x83, 0xc4, 0x01, //0x000033a8 addq         $1, %r12
	0x4d, 0x89, 0xe2, //0x000033ac movq         %r12, %r10
	0x4c, 0x8b, 0x7d, 0xb8, //0x000033af movq         $-72(%rbp), %r15
	0xe9, 0xb6, 0xdb, 0xff, 0xff, //0x000033b3 jmp          LBB0_197
	//0x000033b8 LBB0_731
	0x4d, 0x85, 0xd2, //0x000033b8 testq        %r10, %r10
	0x0f, 0x84, 0x76, 0x00, 0x00, 0x00, //0x000033bb je           LBB0_736
	0x49, 0x89, 0xf9, //0x000033c1 movq         %rdi, %r9
	0x49, 0xf7, 0xd1, //0x000033c4 notq         %r9
	0x4d, 0x01, 0xe1, //0x000033c7 addq         %r12, %r9
	0x49, 0x83, 0xf8, 0xff, //0x000033ca cmpq         $-1, %r8
	0x4c, 0x89, 0xc0, //0x000033ce movq         %r8, %rax
	0x49, 0x0f, 0x44, 0xc1, //0x000033d1 cmoveq       %r9, %rax
	0x4d, 0x0f, 0x45, 0xc8, //0x000033d5 cmovneq      %r8, %r9
	0x49, 0x83, 0xc4, 0x01, //0x000033d9 addq         $1, %r12
	0x49, 0x83, 0xc2, 0xff, //0x000033dd addq         $-1, %r10
	0x49, 0x89, 0xc0, //0x000033e1 movq         %rax, %r8
	0x4d, 0x85, 0xd2, //0x000033e4 testq        %r10, %r10
	0x0f, 0x85, 0xda, 0xfd, 0xff, 0xff, //0x000033e7 jne          LBB0_699
	0xe9, 0x5d, 0xfe, 0xff, 0xff, //0x000033ed jmp          LBB0_707
	//0x000033f2 LBB0_733
	0x4d, 0x85, 0xd2, //0x000033f2 testq        %r10, %r10
	0x0f, 0x84, 0x4b, 0x00, 0x00, 0x00, //0x000033f5 je           LBB0_737
	0x4c, 0x89, 0xf0, //0x000033fb movq         %r14, %rax
	0x48, 0xf7, 0xd0, //0x000033fe notq         %rax
	0x4c, 0x01, 0xe0, //0x00003401 addq         %r12, %rax
	0x49, 0x83, 0xf8, 0xff, //0x00003404 cmpq         $-1, %r8
	0x4c, 0x89, 0xc1, //0x00003408 movq         %r8, %rcx
	0x48, 0x0f, 0x44, 0xc8, //0x0000340b cmoveq       %rax, %rcx
	0x49, 0x0f, 0x45, 0xc0, //0x0000340f cmovneq      %r8, %rax
	0x49, 0x83, 0xc4, 0x01, //0x00003413 addq         $1, %r12
	0x49, 0x83, 0xc2, 0xff, //0x00003417 addq         $-1, %r10
	0x49, 0x89, 0xc8, //0x0000341b movq         %rcx, %r8
	0x4d, 0x85, 0xd2, //0x0000341e testq        %r10, %r10
	0x0f, 0x85, 0x03, 0xff, 0xff, 0xff, //0x00003421 jne          LBB0_721
	//0x00003427 LBB0_735
	0x4c, 0x8b, 0x7d, 0xb8, //0x00003427 movq         $-72(%rbp), %r15
	0x48, 0xc7, 0xc0, 0xff, 0xff, 0xff, 0xff, //0x0000342b movq         $-1, %rax
	0xe9, 0x8a, 0xdb, 0xff, 0xff, //0x00003432 jmp          LBB0_207
	//0x00003437 LBB0_736
	0x48, 0xc7, 0xc0, 0xff, 0xff, 0xff, 0xff, //0x00003437 movq         $-1, %rax
	0x4d, 0x89, 0xf7, //0x0000343e movq         %r14, %r15
	0xe9, 0x7b, 0xdb, 0xff, 0xff, //0x00003441 jmp          LBB0_207
	//0x00003446 LBB0_737
	0x48, 0xc7, 0xc0, 0xff, 0xff, 0xff, 0xff, //0x00003446 movq         $-1, %rax
	0x4c, 0x8b, 0x7d, 0xb8, //0x0000344d movq         $-72(%rbp), %r15
	0xe9, 0x6b, 0xdb, 0xff, 0xff, //0x00003451 jmp          LBB0_207
	//0x00003456 LBB0_738
	0x4d, 0x89, 0xf7, //0x00003456 movq         %r14, %r15
	0x48, 0xc7, 0xc0, 0xff, 0xff, 0xff, 0xff, //0x00003459 movq         $-1, %rax
	0xe9, 0x5c, 0xdb, 0xff, 0xff, //0x00003460 jmp          LBB0_207
	0x90, 0x90, 0x90, //0x00003465 .p2align 2, 0x90
	// // .set L0_0_set_274, LBB0_274-LJTI0_0
	// // .set L0_0_set_132, LBB0_132-LJTI0_0
	// // .set L0_0_set_113, LBB0_113-LJTI0_0
	// // .set L0_0_set_127, LBB0_127-LJTI0_0
	// // .set L0_0_set_34, LBB0_34-LJTI0_0
	// // .set L0_0_set_133, LBB0_133-LJTI0_0
	// // .set L0_0_set_134, LBB0_134-LJTI0_0
	// // .set L0_0_set_141, LBB0_141-LJTI0_0
	// // .set L0_0_set_135, LBB0_135-LJTI0_0
	// // .set L0_0_set_128, LBB0_128-LJTI0_0
	// // .set L0_0_set_138, LBB0_138-LJTI0_0
	// // .set L0_0_set_143, LBB0_143-LJTI0_0
	// // .set L0_0_set_131, LBB0_131-LJTI0_0
	//0x00003468 LJTI0_0
	0x91, 0xdf, 0xff, 0xff, //0x00003468 .long L0_0_set_274
	0xc8, 0xd6, 0xff, 0xff, //0x0000346c .long L0_0_set_132
	0xc8, 0xd6, 0xff, 0xff, //0x00003470 .long L0_0_set_132
	0xc8, 0xd6, 0xff, 0xff, //0x00003474 .long L0_0_set_132
	0xc8, 0xd6, 0xff, 0xff, //0x00003478 .long L0_0_set_132
	0xc8, 0xd6, 0xff, 0xff, //0x0000347c .long L0_0_set_132
	0xc8, 0xd6, 0xff, 0xff, //0x00003480 .long L0_0_set_132
	0xc8, 0xd6, 0xff, 0xff, //0x00003484 .long L0_0_set_132
	0xc8, 0xd6, 0xff, 0xff, //0x00003488 .long L0_0_set_132
	0xc8, 0xd6, 0xff, 0xff, //0x0000348c .long L0_0_set_132
	0xc8, 0xd6, 0xff, 0xff, //0x00003490 .long L0_0_set_132
	0xc8, 0xd6, 0xff, 0xff, //0x00003494 .long L0_0_set_132
	0xc8, 0xd6, 0xff, 0xff, //0x00003498 .long L0_0_set_132
	0xc8, 0xd6, 0xff, 0xff, //0x0000349c .long L0_0_set_132
	0xc8, 0xd6, 0xff, 0xff, //0x000034a0 .long L0_0_set_132
	0xc8, 0xd6, 0xff, 0xff, //0x000034a4 .long L0_0_set_132
	0xc8, 0xd6, 0xff, 0xff, //0x000034a8 .long L0_0_set_132
	0xc8, 0xd6, 0xff, 0xff, //0x000034ac .long L0_0_set_132
	0xc8, 0xd6, 0xff, 0xff, //0x000034b0 .long L0_0_set_132
	0xc8, 0xd6, 0xff, 0xff, //0x000034b4 .long L0_0_set_132
	0xc8, 0xd6, 0xff, 0xff, //0x000034b8 .long L0_0_set_132
	0xc8, 0xd6, 0xff, 0xff, //0x000034bc .long L0_0_set_132
	0xc8, 0xd6, 0xff, 0xff, //0x000034c0 .long L0_0_set_132
	0xc8, 0xd6, 0xff, 0xff, //0x000034c4 .long L0_0_set_132
	0xc8, 0xd6, 0xff, 0xff, //0x000034c8 .long L0_0_set_132
	0xc8, 0xd6, 0xff, 0xff, //0x000034cc .long L0_0_set_132
	0xc8, 0xd6, 0xff, 0xff, //0x000034d0 .long L0_0_set_132
	0xc8, 0xd6, 0xff, 0xff, //0x000034d4 .long L0_0_set_132
	0xc8, 0xd6, 0xff, 0xff, //0x000034d8 .long L0_0_set_132
	0xc8, 0xd6, 0xff, 0xff, //0x000034dc .long L0_0_set_132
	0xc8, 0xd6, 0xff, 0xff, //0x000034e0 .long L0_0_set_132
	0xc8, 0xd6, 0xff, 0xff, //0x000034e4 .long L0_0_set_132
	0xc8, 0xd6, 0xff, 0xff, //0x000034e8 .long L0_0_set_132
	0xc8, 0xd6, 0xff, 0xff, //0x000034ec .long L0_0_set_132
	0x12, 0xd5, 0xff, 0xff, //0x000034f0 .long L0_0_set_113
	0xc8, 0xd6, 0xff, 0xff, //0x000034f4 .long L0_0_set_132
	0xc8, 0xd6, 0xff, 0xff, //0x000034f8 .long L0_0_set_132
	0xc8, 0xd6, 0xff, 0xff, //0x000034fc .long L0_0_set_132
	0xc8, 0xd6, 0xff, 0xff, //0x00003500 .long L0_0_set_132
	0xc8, 0xd6, 0xff, 0xff, //0x00003504 .long L0_0_set_132
	0xc8, 0xd6, 0xff, 0xff, //0x00003508 .long L0_0_set_132
	0xc8, 0xd6, 0xff, 0xff, //0x0000350c .long L0_0_set_132
	0xc8, 0xd6, 0xff, 0xff, //0x00003510 .long L0_0_set_132
	0xc8, 0xd6, 0xff, 0xff, //0x00003514 .long L0_0_set_132
	0x5c, 0xd6, 0xff, 0xff, //0x00003518 .long L0_0_set_127
	0xb1, 0xcf, 0xff, 0xff, //0x0000351c .long L0_0_set_34
	0xc8, 0xd6, 0xff, 0xff, //0x00003520 .long L0_0_set_132
	0xc8, 0xd6, 0xff, 0xff, //0x00003524 .long L0_0_set_132
	0xb1, 0xcf, 0xff, 0xff, //0x00003528 .long L0_0_set_34
	0xb1, 0xcf, 0xff, 0xff, //0x0000352c .long L0_0_set_34
	0xb1, 0xcf, 0xff, 0xff, //0x00003530 .long L0_0_set_34
	0xb1, 0xcf, 0xff, 0xff, //0x00003534 .long L0_0_set_34
	0xb1, 0xcf, 0xff, 0xff, //0x00003538 .long L0_0_set_34
	0xb1, 0xcf, 0xff, 0xff, //0x0000353c .long L0_0_set_34
	0xb1, 0xcf, 0xff, 0xff, //0x00003540 .long L0_0_set_34
	0xb1, 0xcf, 0xff, 0xff, //0x00003544 .long L0_0_set_34
	0xb1, 0xcf, 0xff, 0xff, //0x00003548 .long L0_0_set_34
	0xb1, 0xcf, 0xff, 0xff, //0x0000354c .long L0_0_set_34
	0xd7, 0xd6, 0xff, 0xff, //0x00003550 .long L0_0_set_133
	0xc8, 0xd6, 0xff, 0xff, //0x00003554 .long L0_0_set_132
	0xc8, 0xd6, 0xff, 0xff, //0x00003558 .long L0_0_set_132
	0xc8, 0xd6, 0xff, 0xff, //0x0000355c .long L0_0_set_132
	0xc8, 0xd6, 0xff, 0xff, //0x00003560 .long L0_0_set_132
	0xc8, 0xd6, 0xff, 0xff, //0x00003564 .long L0_0_set_132
	0xc8, 0xd6, 0xff, 0xff, //0x00003568 .long L0_0_set_132
	0xc8, 0xd6, 0xff, 0xff, //0x0000356c .long L0_0_set_132
	0xc8, 0xd6, 0xff, 0xff, //0x00003570 .long L0_0_set_132
	0xc8, 0xd6, 0xff, 0xff, //0x00003574 .long L0_0_set_132
	0xc8, 0xd6, 0xff, 0xff, //0x00003578 .long L0_0_set_132
	0xc8, 0xd6, 0xff, 0xff, //0x0000357c .long L0_0_set_132
	0xc8, 0xd6, 0xff, 0xff, //0x00003580 .long L0_0_set_132
	0xc8, 0xd6, 0xff, 0xff, //0x00003584 .long L0_0_set_132
	0xc8, 0xd6, 0xff, 0xff, //0x00003588 .long L0_0_set_132
	0xc8, 0xd6, 0xff, 0xff, //0x0000358c .long L0_0_set_132
	0xc8, 0xd6, 0xff, 0xff, //0x00003590 .long L0_0_set_132
	0xc8, 0xd6, 0xff, 0xff, //0x00003594 .long L0_0_set_132
	0xc8, 0xd6, 0xff, 0xff, //0x00003598 .long L0_0_set_132
	0xc8, 0xd6, 0xff, 0xff, //0x0000359c .long L0_0_set_132
	0xc8, 0xd6, 0xff, 0xff, //0x000035a0 .long L0_0_set_132
	0xc8, 0xd6, 0xff, 0xff, //0x000035a4 .long L0_0_set_132
	0xc8, 0xd6, 0xff, 0xff, //0x000035a8 .long L0_0_set_132
	0xc8, 0xd6, 0xff, 0xff, //0x000035ac .long L0_0_set_132
	0xc8, 0xd6, 0xff, 0xff, //0x000035b0 .long L0_0_set_132
	0xc8, 0xd6, 0xff, 0xff, //0x000035b4 .long L0_0_set_132
	0xc8, 0xd6, 0xff, 0xff, //0x000035b8 .long L0_0_set_132
	0xc8, 0xd6, 0xff, 0xff, //0x000035bc .long L0_0_set_132
	0xc8, 0xd6, 0xff, 0xff, //0x000035c0 .long L0_0_set_132
	0xc8, 0xd6, 0xff, 0xff, //0x000035c4 .long L0_0_set_132
	0xc8, 0xd6, 0xff, 0xff, //0x000035c8 .long L0_0_set_132
	0xc8, 0xd6, 0xff, 0xff, //0x000035cc .long L0_0_set_132
	0xc8, 0xd6, 0xff, 0xff, //0x000035d0 .long L0_0_set_132
	0xf4, 0xd6, 0xff, 0xff, //0x000035d4 .long L0_0_set_134
	0xc8, 0xd6, 0xff, 0xff, //0x000035d8 .long L0_0_set_132
	0x63, 0xd7, 0xff, 0xff, //0x000035dc .long L0_0_set_141
	0xc8, 0xd6, 0xff, 0xff, //0x000035e0 .long L0_0_set_132
	0xc8, 0xd6, 0xff, 0xff, //0x000035e4 .long L0_0_set_132
	0xc8, 0xd6, 0xff, 0xff, //0x000035e8 .long L0_0_set_132
	0xc8, 0xd6, 0xff, 0xff, //0x000035ec .long L0_0_set_132
	0xc8, 0xd6, 0xff, 0xff, //0x000035f0 .long L0_0_set_132
	0xc8, 0xd6, 0xff, 0xff, //0x000035f4 .long L0_0_set_132
	0xc8, 0xd6, 0xff, 0xff, //0x000035f8 .long L0_0_set_132
	0xc8, 0xd6, 0xff, 0xff, //0x000035fc .long L0_0_set_132
	0x00, 0xd7, 0xff, 0xff, //0x00003600 .long L0_0_set_135
	0xc8, 0xd6, 0xff, 0xff, //0x00003604 .long L0_0_set_132
	0xc8, 0xd6, 0xff, 0xff, //0x00003608 .long L0_0_set_132
	0xc8, 0xd6, 0xff, 0xff, //0x0000360c .long L0_0_set_132
	0xc8, 0xd6, 0xff, 0xff, //0x00003610 .long L0_0_set_132
	0xc8, 0xd6, 0xff, 0xff, //0x00003614 .long L0_0_set_132
	0xc8, 0xd6, 0xff, 0xff, //0x00003618 .long L0_0_set_132
	0xc8, 0xd6, 0xff, 0xff, //0x0000361c .long L0_0_set_132
	0x79, 0xd6, 0xff, 0xff, //0x00003620 .long L0_0_set_128
	0xc8, 0xd6, 0xff, 0xff, //0x00003624 .long L0_0_set_132
	0xc8, 0xd6, 0xff, 0xff, //0x00003628 .long L0_0_set_132
	0xc8, 0xd6, 0xff, 0xff, //0x0000362c .long L0_0_set_132
	0xc8, 0xd6, 0xff, 0xff, //0x00003630 .long L0_0_set_132
	0xc8, 0xd6, 0xff, 0xff, //0x00003634 .long L0_0_set_132
	0x31, 0xd7, 0xff, 0xff, //0x00003638 .long L0_0_set_138
	0xc8, 0xd6, 0xff, 0xff, //0x0000363c .long L0_0_set_132
	0xc8, 0xd6, 0xff, 0xff, //0x00003640 .long L0_0_set_132
	0xc8, 0xd6, 0xff, 0xff, //0x00003644 .long L0_0_set_132
	0xc8, 0xd6, 0xff, 0xff, //0x00003648 .long L0_0_set_132
	0xc8, 0xd6, 0xff, 0xff, //0x0000364c .long L0_0_set_132
	0xc8, 0xd6, 0xff, 0xff, //0x00003650 .long L0_0_set_132
	0x8a, 0xd7, 0xff, 0xff, //0x00003654 .long L0_0_set_143
	0xc8, 0xd6, 0xff, 0xff, //0x00003658 .long L0_0_set_132
	0xab, 0xd6, 0xff, 0xff, //0x0000365c .long L0_0_set_131
	// // .set L0_1_set_86, LBB0_86-LJTI0_1
	// // .set L0_1_set_145, LBB0_145-LJTI0_1
	// // .set L0_1_set_92, LBB0_92-LJTI0_1
	// // .set L0_1_set_95, LBB0_95-LJTI0_1
	//0x00003660 LJTI0_1
	0xb5, 0xd1, 0xff, 0xff, //0x00003660 .long L0_1_set_86
	0xaf, 0xd5, 0xff, 0xff, //0x00003664 .long L0_1_set_145
	0xb5, 0xd1, 0xff, 0xff, //0x00003668 .long L0_1_set_86
	0x03, 0xd2, 0xff, 0xff, //0x0000366c .long L0_1_set_92
	0xaf, 0xd5, 0xff, 0xff, //0x00003670 .long L0_1_set_145
	0xaf, 0xd5, 0xff, 0xff, //0x00003674 .long L0_1_set_145
	0xaf, 0xd5, 0xff, 0xff, //0x00003678 .long L0_1_set_145
	0xaf, 0xd5, 0xff, 0xff, //0x0000367c .long L0_1_set_145
	0xaf, 0xd5, 0xff, 0xff, //0x00003680 .long L0_1_set_145
	0xaf, 0xd5, 0xff, 0xff, //0x00003684 .long L0_1_set_145
	0xaf, 0xd5, 0xff, 0xff, //0x00003688 .long L0_1_set_145
	0xaf, 0xd5, 0xff, 0xff, //0x0000368c .long L0_1_set_145
	0xaf, 0xd5, 0xff, 0xff, //0x00003690 .long L0_1_set_145
	0xaf, 0xd5, 0xff, 0xff, //0x00003694 .long L0_1_set_145
	0xaf, 0xd5, 0xff, 0xff, //0x00003698 .long L0_1_set_145
	0xaf, 0xd5, 0xff, 0xff, //0x0000369c .long L0_1_set_145
	0xaf, 0xd5, 0xff, 0xff, //0x000036a0 .long L0_1_set_145
	0xaf, 0xd5, 0xff, 0xff, //0x000036a4 .long L0_1_set_145
	0xaf, 0xd5, 0xff, 0xff, //0x000036a8 .long L0_1_set_145
	0xaf, 0xd5, 0xff, 0xff, //0x000036ac .long L0_1_set_145
	0xaf, 0xd5, 0xff, 0xff, //0x000036b0 .long L0_1_set_145
	0xaf, 0xd5, 0xff, 0xff, //0x000036b4 .long L0_1_set_145
	0xaf, 0xd5, 0xff, 0xff, //0x000036b8 .long L0_1_set_145
	0xaf, 0xd5, 0xff, 0xff, //0x000036bc .long L0_1_set_145
	0xaf, 0xd5, 0xff, 0xff, //0x000036c0 .long L0_1_set_145
	0xaf, 0xd5, 0xff, 0xff, //0x000036c4 .long L0_1_set_145
	0x1f, 0xd2, 0xff, 0xff, //0x000036c8 .long L0_1_set_95
	//0x000036cc .p2align 2, 0x00
	//0x000036cc _MASK_USE_NUMBER
	0x02, 0x00, 0x00, 0x00, //0x000036cc .long 2
	//0x000036d0 .p2align 4, 0x00
	//0x000036d0 _P10_TAB
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xf0, 0x3f, //0x000036d0 .quad 0x3ff0000000000000
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x24, 0x40, //0x000036d8 .quad 0x4024000000000000
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x59, 0x40, //0x000036e0 .quad 0x4059000000000000
	0x00, 0x00, 0x00, 0x00, 0x00, 0x40, 0x8f, 0x40, //0x000036e8 .quad 0x408f400000000000
	0x00, 0x00, 0x00, 0x00, 0x00, 0x88, 0xc3, 0x40, //0x000036f0 .quad 0x40c3880000000000
	0x00, 0x00, 0x00, 0x00, 0x00, 0x6a, 0xf8, 0x40, //0x000036f8 .quad 0x40f86a0000000000
	0x00, 0x00, 0x00, 0x00, 0x80, 0x84, 0x2e, 0x41, //0x00003700 .quad 0x412e848000000000
	0x00, 0x00, 0x00, 0x00, 0xd0, 0x12, 0x63, 0x41, //0x00003708 .quad 0x416312d000000000
	0x00, 0x00, 0x00, 0x00, 0x84, 0xd7, 0x97, 0x41, //0x00003710 .quad 0x4197d78400000000
	0x00, 0x00, 0x00, 0x00, 0x65, 0xcd, 0xcd, 0x41, //0x00003718 .quad 0x41cdcd6500000000
	0x00, 0x00, 0x00, 0x20, 0x5f, 0xa0, 0x02, 0x42, //0x00003720 .quad 0x4202a05f20000000
	0x00, 0x00, 0x00, 0xe8, 0x76, 0x48, 0x37, 0x42, //0x00003728 .quad 0x42374876e8000000
	0x00, 0x00, 0x00, 0xa2, 0x94, 0x1a, 0x6d, 0x42, //0x00003730 .quad 0x426d1a94a2000000
	0x00, 0x00, 0x40, 0xe5, 0x9c, 0x30, 0xa2, 0x42, //0x00003738 .quad 0x42a2309ce5400000
	0x00, 0x00, 0x90, 0x1e, 0xc4, 0xbc, 0xd6, 0x42, //0x00003740 .quad 0x42d6bcc41e900000
	0x00, 0x00, 0x34, 0x26, 0xf5, 0x6b, 0x0c, 0x43, //0x00003748 .quad 0x430c6bf526340000
	0x00, 0x80, 0xe0, 0x37, 0x79, 0xc3, 0x41, 0x43, //0x00003750 .quad 0x4341c37937e08000
	0x00, 0xa0, 0xd8, 0x85, 0x57, 0x34, 0x76, 0x43, //0x00003758 .quad 0x4376345785d8a000
	0x00, 0xc8, 0x4e, 0x67, 0x6d, 0xc1, 0xab, 0x43, //0x00003760 .quad 0x43abc16d674ec800
	0x00, 0x3d, 0x91, 0x60, 0xe4, 0x58, 0xe1, 0x43, //0x00003768 .quad 0x43e158e460913d00
	0x40, 0x8c, 0xb5, 0x78, 0x1d, 0xaf, 0x15, 0x44, //0x00003770 .quad 0x4415af1d78b58c40
	0x50, 0xef, 0xe2, 0xd6, 0xe4, 0x1a, 0x4b, 0x44, //0x00003778 .quad 0x444b1ae4d6e2ef50
	0x92, 0xd5, 0x4d, 0x06, 0xcf, 0xf0, 0x80, 0x44, //0x00003780 .quad 0x4480f0cf064dd592
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00003788 .p2align 4, 0x00
	//0x00003790 _POW10_M128_TAB
	0x53, 0xe4, 0x60, 0xcd, 0x69, 0xc8, 0x32, 0x17, //0x00003790 .quad 1671618768450675795
	0x88, 0x02, 0x1c, 0x08, 0xa0, 0xd5, 0x8f, 0xfa, //0x00003798 .quad -391859759250406776
	0xb4, 0x8e, 0x5c, 0x20, 0x42, 0xbd, 0x7f, 0x0e, //0x000037a0 .quad 1044761730281672372
	0x95, 0x81, 0x11, 0x05, 0x84, 0xe5, 0x99, 0x9c, //0x000037a8 .quad -7162441377172586091
	0x61, 0xb2, 0x73, 0xa8, 0x92, 0xac, 0x1f, 0x52, //0x000037b0 .quad 5917638181279478369
	0xfa, 0xe1, 0x55, 0x06, 0xe5, 0x5e, 0xc0, 0xc3, //0x000037b8 .quad -4341365703038344710
	0xf9, 0x9e, 0x90, 0x52, 0xb7, 0x97, 0xa7, 0xe6, //0x000037c0 .quad -1826324310255427847
	0x78, 0x5a, 0xeb, 0x47, 0x9e, 0x76, 0xb0, 0xf4, //0x000037c8 .quad -815021110370542984
	0x5c, 0x63, 0x9a, 0x93, 0xd2, 0xbe, 0x28, 0x90, //0x000037d0 .quad -8058981721550724260
	0x8b, 0x18, 0xf3, 0xec, 0x22, 0x4a, 0xee, 0x98, //0x000037d8 .quad -7426917221622671221
	0x33, 0xfc, 0x80, 0x38, 0x87, 0xee, 0x32, 0x74, //0x000037e0 .quad 8373016921771146291
	0xae, 0xde, 0x2f, 0xa8, 0xab, 0xdc, 0x29, 0xbf, //0x000037e8 .quad -4671960508600951122
	0x3f, 0x3b, 0xa1, 0x06, 0x29, 0xaa, 0x3f, 0x11, //0x000037f0 .quad 1242899115359157055
	0x5a, 0xd6, 0x3b, 0x92, 0xd6, 0x53, 0xf4, 0xee, //0x000037f8 .quad -1228264617323800998
	0x07, 0xc5, 0x24, 0xa4, 0x59, 0xca, 0xc7, 0x4a, //0x00003800 .quad 5388497965526861063
	0xf8, 0x65, 0x65, 0x1b, 0x66, 0xb4, 0x58, 0x95, //0x00003808 .quad -7685194413468457480
	0x49, 0xf6, 0x2d, 0x0d, 0xf0, 0xbc, 0x79, 0x5d, //0x00003810 .quad 6735622456908576329
	0x76, 0xbf, 0x3e, 0xa2, 0x7f, 0xe1, 0xae, 0xba, //0x00003818 .quad -4994806998408183946
	0xdc, 0x73, 0x79, 0x10, 0x2c, 0x2c, 0xd8, 0xf4, //0x00003820 .quad -803843965719055396
	0x53, 0x6f, 0xce, 0x8a, 0xdf, 0x99, 0x5a, 0xe9, //0x00003828 .quad -1631822729582842029
	0x69, 0xe8, 0x4b, 0x8a, 0x9b, 0x1b, 0x07, 0x79, //0x00003830 .quad 8720969558280366185
	0x94, 0x05, 0xc1, 0xb6, 0x2b, 0xa0, 0xd8, 0x91, //0x00003838 .quad -7937418233630358124
	0x84, 0xe2, 0xde, 0x6c, 0x82, 0xe2, 0x48, 0x97, //0x00003840 .quad -7545532125859093884
	0xf9, 0x46, 0x71, 0xa4, 0x36, 0xc8, 0x4e, 0xb6, //0x00003848 .quad -5310086773610559751
	0x25, 0x9b, 0x16, 0x08, 0x23, 0x1b, 0x1b, 0xfd, //0x00003850 .quad -208543120469091547
	0xb7, 0x98, 0x8d, 0x4d, 0x44, 0x7a, 0xe2, 0xe3, //0x00003858 .quad -2025922448585811785
	0xf7, 0x20, 0x0e, 0xe5, 0xf5, 0xf0, 0x30, 0xfe, //0x00003860 .quad -130339450293182217
	0x72, 0x7f, 0x78, 0xb0, 0x6a, 0x8c, 0x6d, 0x8e, //0x00003868 .quad -8183730558007214222
	0x35, 0xa9, 0x51, 0x5e, 0x33, 0x2d, 0xbd, 0xbd, //0x00003870 .quad -4774610331293865675
	0x4f, 0x9f, 0x96, 0x5c, 0x85, 0xef, 0x08, 0xb2, //0x00003878 .quad -5617977179081629873
	0x82, 0x13, 0xe6, 0x35, 0x80, 0x78, 0x2c, 0xad, //0x00003880 .quad -5968262914117332094
	0x23, 0x47, 0xbc, 0xb3, 0x66, 0x2b, 0x8b, 0xde, //0x00003888 .quad -2410785455424649437
	0x31, 0xcc, 0xaf, 0x21, 0x50, 0xcb, 0x3b, 0x4c, //0x00003890 .quad 5493207715531443249
	0x76, 0xac, 0x55, 0x30, 0x20, 0xfb, 0x16, 0x8b, //0x00003898 .quad -8424269937281487754
	0x3d, 0xbf, 0x1b, 0x2a, 0x24, 0xbe, 0x4a, 0xdf, //0x000038a0 .quad -2356862392440471747
	0x93, 0x17, 0x6b, 0x3c, 0xe8, 0xb9, 0xdc, 0xad, //0x000038a8 .quad -5918651403174471789
	0x0d, 0xaf, 0xa2, 0x34, 0xad, 0x6d, 0x1d, 0xd7, //0x000038b0 .quad -2946077990550589683
	0x78, 0xdd, 0x85, 0x4b, 0x62, 0xe8, 0x53, 0xd9, //0x000038b8 .quad -2786628235540701832
	0x68, 0xad, 0xe5, 0x40, 0x8c, 0x64, 0x72, 0x86, //0x000038c0 .quad -8758827771735200408
	0x6b, 0xaa, 0x33, 0x6f, 0x3d, 0x71, 0xd4, 0x87, //0x000038c8 .quad -8659171674854020501
	0xc2, 0x18, 0x1f, 0x51, 0xaf, 0xfd, 0x0e, 0x68, //0x000038d0 .quad 7498209359040551106
	0x06, 0x95, 0x00, 0xcb, 0x8c, 0x8d, 0xc9, 0xa9, //0x000038d8 .quad -6212278575140137722
	0xf2, 0xde, 0x66, 0x25, 0x1b, 0xbd, 0x12, 0x02, //0x000038e0 .quad 149389661945913074
	0x48, 0xba, 0xc0, 0xfd, 0xef, 0xf0, 0x3b, 0xd4, //0x000038e8 .quad -3153662200497784248
	0x57, 0x4b, 0x60, 0xf7, 0x30, 0xb6, 0x4b, 0x01, //0x000038f0 .quad 93368538716195671
	0x6d, 0x74, 0x98, 0xfe, 0x95, 0x76, 0xa5, 0x84, //0x000038f8 .quad -8888567902952197011
	0x2d, 0x5e, 0x38, 0x35, 0xbd, 0xa3, 0x9e, 0x41, //0x00003900 .quad 4728396691822632493
	0x88, 0x91, 0x3e, 0x7e, 0x3b, 0xd4, 0xce, 0xa5, //0x00003908 .quad -6499023860262858360
	0xb9, 0x75, 0x86, 0x82, 0xac, 0x4c, 0x06, 0x52, //0x00003910 .quad 5910495864778290617
	0xea, 0x35, 0xce, 0x5d, 0x4a, 0x89, 0x42, 0xcf, //0x00003918 .quad -3512093806901185046
	0x93, 0x09, 0x94, 0xd1, 0xeb, 0xef, 0x43, 0x73, //0x00003920 .quad 8305745933913819539
	0xb2, 0xe1, 0xa0, 0x7a, 0xce, 0x95, 0x89, 0x81, //0x00003928 .quad -9112587656954322510
	0xf8, 0x0b, 0xf9, 0xc5, 0xe6, 0xeb, 0x14, 0x10, //0x00003930 .quad 1158810380537498616
	0x1f, 0x1a, 0x49, 0x19, 0x42, 0xfb, 0xeb, 0xa1, //0x00003938 .quad -6779048552765515233
	0xf6, 0x4e, 0x77, 0x77, 0xe0, 0x26, 0x1a, 0xd4, //0x00003940 .quad -3163173042755514634
	0xa6, 0x60, 0x9b, 0x9f, 0x12, 0xfa, 0x66, 0xca, //0x00003948 .quad -3862124672529506138
	0xb4, 0x22, 0x55, 0x95, 0x98, 0xb0, 0x20, 0x89, //0x00003950 .quad -8565652321871781196
	0xd0, 0x38, 0x82, 0x47, 0x97, 0xb8, 0x00, 0xfd, //0x00003958 .quad -215969822234494768
	0xb0, 0x35, 0x55, 0x5d, 0x5f, 0x6e, 0xb4, 0x55, //0x00003960 .quad 6175682344898606512
	0x82, 0x63, 0xb1, 0x8c, 0x5e, 0x73, 0x20, 0x9e, //0x00003968 .quad -7052510166537641086
	0x1d, 0x83, 0xaa, 0x34, 0xf7, 0x89, 0x21, 0xeb, //0x00003970 .quad -1503769105731517667
	0x62, 0xbc, 0xdd, 0x2f, 0x36, 0x90, 0xa8, 0xc5, //0x00003978 .quad -4203951689744663454
	0xe4, 0x23, 0xd5, 0x01, 0x75, 0xec, 0xe9, 0xa5, //0x00003980 .quad -6491397400591784988
	0x7b, 0x2b, 0xd5, 0xbb, 0x43, 0xb4, 0x12, 0xf7, //0x00003988 .quad -643253593753441413
	0x6e, 0x36, 0x25, 0x21, 0xc9, 0x33, 0xb2, 0x47, //0x00003990 .quad 5166248661484910190
	0x2d, 0x3b, 0x65, 0x55, 0xaa, 0xb0, 0x6b, 0x9a, //0x00003998 .quad -7319562523736982739
	0x0a, 0x84, 0x6e, 0x69, 0xbb, 0xc0, 0x9e, 0x99, //0x000039a0 .quad -7377247228426025974
	0xf8, 0x89, 0xbe, 0xea, 0xd4, 0x9c, 0x06, 0xc1, //0x000039a8 .quad -4537767136243840520
	0x0d, 0x25, 0xca, 0x43, 0xea, 0x70, 0x06, 0xc0, //0x000039b0 .quad -4609873017105144563
	0x76, 0x2c, 0x6e, 0x25, 0x0a, 0x44, 0x48, 0xf1, //0x000039b8 .quad -1060522901877412746
	0x28, 0x57, 0x5e, 0x6a, 0x92, 0x06, 0x04, 0x38, //0x000039c0 .quad 4036358391950366504
	0xca, 0xdb, 0x64, 0x57, 0x86, 0x2a, 0xcd, 0x96, //0x000039c8 .quad -7580355841314464822
	0xf2, 0xec, 0xf5, 0x04, 0x37, 0x08, 0x05, 0xc6, //0x000039d0 .quad -4177924046916817678
	0xbc, 0x12, 0x3e, 0xed, 0x27, 0x75, 0x80, 0xbc, //0x000039d8 .quad -4863758783215693124
	0x2e, 0x68, 0x33, 0xc6, 0x44, 0x4a, 0x86, 0xf7, //0x000039e0 .quad -610719040218634194
	0x6b, 0x97, 0x8d, 0xe8, 0x71, 0x92, 0xa0, 0xeb, //0x000039e8 .quad -1468012460592228501
	0x1d, 0x21, 0xe0, 0xfb, 0x6a, 0xee, 0xb3, 0x7a, //0x000039f0 .quad 8841672636718129437
	0xa3, 0x7e, 0x58, 0x31, 0x87, 0x5b, 0x44, 0x93, //0x000039f8 .quad -7835036815511224669
	0x64, 0x29, 0xd8, 0xba, 0x05, 0xea, 0x60, 0x59, //0x00003a00 .quad 6440404777470273892
	0x4c, 0x9e, 0xae, 0xfd, 0x68, 0x72, 0x15, 0xb8, //0x00003a08 .quad -5182110000961642932
	0xbd, 0x33, 0x8e, 0x29, 0x87, 0x24, 0xb9, 0x6f, //0x00003a10 .quad 8050505971837842365
	0xdf, 0x45, 0x1a, 0x3d, 0x03, 0xcf, 0x1a, 0xe6, //0x00003a18 .quad -1865951482774665761
	0x56, 0xe0, 0xf8, 0x79, 0xd4, 0xb6, 0xd3, 0xa5, //0x00003a20 .quad -6497648813669818282
	0xab, 0x6b, 0x30, 0x06, 0x62, 0xc1, 0xd0, 0x8f, //0x00003a28 .quad -8083748704375247957
	0x6c, 0x18, 0x77, 0x98, 0x89, 0xa4, 0x48, 0x8f, //0x00003a30 .quad -8122061017087272852
	0x96, 0x86, 0xbc, 0x87, 0xba, 0xf1, 0xc4, 0xb3, //0x00003a38 .quad -5492999862041672042
	0x87, 0xde, 0x94, 0xfe, 0xab, 0xcd, 0x1a, 0x33, //0x00003a40 .quad 3682481783923072647
	0x3c, 0xa8, 0xab, 0x29, 0x29, 0x2e, 0xb6, 0xe0, //0x00003a48 .quad -2254563809124702148
	0x14, 0x0b, 0x1d, 0x7f, 0x8b, 0xc0, 0xf0, 0x9f, //0x00003a50 .quad -6921820921902855404
	0x25, 0x49, 0x0b, 0xba, 0xd9, 0xdc, 0x71, 0x8c, //0x00003a58 .quad -8326631408344020699
	0xd9, 0x4d, 0xe4, 0x5e, 0xae, 0xf0, 0xec, 0x07, //0x00003a60 .quad 571095884476206553
	0x6f, 0x1b, 0x8e, 0x28, 0x10, 0x54, 0x8e, 0xaf, //0x00003a68 .quad -5796603242002637969
	0x50, 0x61, 0x9d, 0xf6, 0xd9, 0x2c, 0xe8, 0xc9, //0x00003a70 .quad -3897816162832129712
	0x4a, 0xa2, 0xb1, 0x32, 0x14, 0xe9, 0x71, 0xdb, //0x00003a78 .quad -2634068034075909558
	0xd2, 0x5c, 0x22, 0x3a, 0x08, 0x1c, 0x31, 0xbe, //0x00003a80 .quad -4741978110983775022
	0x6e, 0x05, 0xaf, 0x9f, 0xac, 0x31, 0x27, 0x89, //0x00003a88 .quad -8563821548938525330
	0x06, 0xf4, 0xaa, 0x48, 0x0a, 0x63, 0xbd, 0x6d, //0x00003a90 .quad 7907585416552444934
	0xca, 0xc6, 0x9a, 0xc7, 0x17, 0xfe, 0x70, 0xab, //0x00003a98 .quad -6093090917745768758
	0x08, 0xb1, 0xd5, 0xda, 0xcc, 0xbb, 0x2c, 0x09, //0x00003aa0 .quad 661109733835780360
	0x7d, 0x78, 0x81, 0xb9, 0x9d, 0x3d, 0x4d, 0xd6, //0x00003aa8 .quad -3004677628754823043
	0xa5, 0x8e, 0xc5, 0x08, 0x60, 0xf5, 0xbb, 0x25, //0x00003ab0 .quad 2719036592861056677
	0x4e, 0xeb, 0xf0, 0x93, 0x82, 0x46, 0xf0, 0x85, //0x00003ab8 .quad -8795452545612846258
	0x4e, 0xf2, 0xf6, 0x0a, 0xb8, 0xf2, 0x2a, 0xaf, //0x00003ac0 .quad -5824576295778454962
	0x21, 0x26, 0xed, 0x38, 0x23, 0x58, 0x6c, 0xa7, //0x00003ac8 .quad -6382629663588669919
	0xe1, 0xae, 0xb4, 0x0d, 0x66, 0xaf, 0xf5, 0x1a, //0x00003ad0 .quad 1942651667131707105
	0xaa, 0x6f, 0x28, 0x07, 0x2c, 0x6e, 0x47, 0xd1, //0x00003ad8 .quad -3366601061058449494
	0x4d, 0xed, 0x90, 0xc8, 0x9f, 0x8d, 0xd9, 0x50, //0x00003ae0 .quad 5825843310384704845
	0xca, 0x45, 0x79, 0x84, 0xdb, 0xa4, 0xcc, 0x82, //0x00003ae8 .quad -9021654690802612790
	0xa0, 0x28, 0xb5, 0xba, 0x07, 0xf1, 0x0f, 0xe5, //0x00003af0 .quad -1941067898873894752
	0x3c, 0x97, 0x97, 0x65, 0x12, 0xce, 0x7f, 0xa3, //0x00003af8 .quad -6665382345075878084
	0xc8, 0x72, 0x62, 0xa9, 0x49, 0xed, 0x53, 0x1e, //0x00003b00 .quad 2185351144835019464
	0x0c, 0x7d, 0xfd, 0xfe, 0x96, 0xc1, 0x5f, 0xcc, //0x00003b08 .quad -3720041912917459700
	0x7a, 0x0f, 0xbb, 0x13, 0x9c, 0xe8, 0xe8, 0x25, //0x00003b10 .quad 2731688931043774330
	0x4f, 0xdc, 0xbc, 0xbe, 0xfc, 0xb1, 0x77, 0xff, //0x00003b18 .quad -38366372719436721
	0xac, 0xe9, 0x54, 0x8c, 0x61, 0x91, 0xb1, 0x77, //0x00003b20 .quad 8624834609543440812
	0xb1, 0x09, 0x36, 0xf7, 0x3d, 0xcf, 0xaa, 0x9f, //0x00003b28 .quad -6941508010590729807
	0x17, 0x24, 0x6a, 0xef, 0xb9, 0xf5, 0x9d, 0xd5, //0x00003b30 .quad -3054014793352862697
	0x1d, 0x8c, 0x03, 0x75, 0x0d, 0x83, 0x95, 0xc7, //0x00003b38 .quad -4065198994811024355
	0x1d, 0xad, 0x44, 0x6b, 0x28, 0x73, 0x05, 0x4b, //0x00003b40 .quad 5405853545163697437
	0x25, 0x6f, 0x44, 0xd2, 0xd0, 0xe3, 0x7a, 0xf9, //0x00003b48 .quad -469812725086392539
	0x32, 0xec, 0x0a, 0x43, 0xf9, 0x67, 0xe3, 0x4e, //0x00003b50 .quad 5684501474941004850
	0x77, 0xc5, 0x6a, 0x83, 0x62, 0xce, 0xec, 0x9b, //0x00003b58 .quad -7211161980820077193
	0x3f, 0xa7, 0xcd, 0x93, 0xf7, 0x41, 0x9c, 0x22, //0x00003b60 .quad 2493940825248868159
	0xd5, 0x76, 0x45, 0x24, 0xfb, 0x01, 0xe8, 0xc2, //0x00003b68 .quad -4402266457597708587
	0x0f, 0x11, 0xc1, 0x78, 0x75, 0x52, 0x43, 0x6b, //0x00003b70 .quad 7729112049988473103
	0x8a, 0xd4, 0x56, 0xed, 0x79, 0x02, 0xa2, 0xf3, //0x00003b78 .quad -891147053569747830
	0xa9, 0xaa, 0x78, 0x6b, 0x89, 0x13, 0x0a, 0x83, //0x00003b80 .quad -9004363024039368023
	0xd6, 0x44, 0x56, 0x34, 0x8c, 0x41, 0x45, 0x98, //0x00003b88 .quad -7474495936122174250
	0x53, 0xd5, 0x56, 0xc6, 0x6b, 0x98, 0xcc, 0x23, //0x00003b90 .quad 2579604275232953683
	0x0c, 0xd6, 0x6b, 0x41, 0xef, 0x91, 0x56, 0xbe, //0x00003b98 .quad -4731433901725329908
	0xa8, 0x8a, 0xec, 0xb7, 0x86, 0xbe, 0xbf, 0x2c, //0x00003ba0 .quad 3224505344041192104
	0x8f, 0xcb, 0xc6, 0x11, 0x6b, 0x36, 0xec, 0xed, //0x00003ba8 .quad -1302606358729274481
	0xa9, 0xd6, 0xf3, 0x32, 0x14, 0xd7, 0xf7, 0x7b, //0x00003bb0 .quad 8932844867666826921
	0x39, 0x3f, 0x1c, 0xeb, 0x02, 0xa2, 0xb3, 0x94, //0x00003bb8 .quad -7731658001846878407
	0x53, 0xcc, 0xb0, 0x3f, 0xd9, 0xcc, 0xf5, 0xda, //0x00003bc0 .quad -2669001970698630061
	0x07, 0x4f, 0xe3, 0xa5, 0x83, 0x8a, 0xe0, 0xb9, //0x00003bc8 .quad -5052886483881210105
	0x68, 0xff, 0x9c, 0x8f, 0x0f, 0x40, 0xb3, 0xd1, //0x00003bd0 .quad -3336252463373287576
	0xc9, 0x22, 0x5c, 0x8f, 0x24, 0xad, 0x58, 0xe8, //0x00003bd8 .quad -1704422086424124727
	0xa1, 0x1f, 0xc2, 0xb9, 0x09, 0x08, 0x10, 0x23, //0x00003be0 .quad 2526528228819083169
	0xbe, 0x95, 0x99, 0xd9, 0x36, 0x6c, 0x37, 0x91, //0x00003be8 .quad -7982792831656159810
	0x8a, 0xa7, 0x32, 0x28, 0x0c, 0x0a, 0xd4, 0xab, //0x00003bf0 .quad -6065211750830921846
	0x2d, 0xfb, 0xff, 0x8f, 0x44, 0x47, 0x85, 0xb5, //0x00003bf8 .quad -5366805021142811859
	0x6c, 0x51, 0x3f, 0x32, 0x8f, 0x0c, 0xc9, 0x16, //0x00003c00 .quad 1641857348316123500
	0xf9, 0xf9, 0xff, 0xb3, 0x15, 0x99, 0xe6, 0xe2, //0x00003c08 .quad -2096820258001126919
	0xe3, 0x92, 0x67, 0x7f, 0xd9, 0xa7, 0x3d, 0xae, //0x00003c10 .quad -5891368184943504669
	0x3b, 0xfc, 0x7f, 0x90, 0xad, 0x1f, 0xd0, 0x8d, //0x00003c18 .quad -8228041688891786181
	0x9c, 0x77, 0x41, 0xdf, 0xcf, 0x11, 0xcd, 0x99, //0x00003c20 .quad -7364210231179380836
	0x4a, 0xfb, 0x9f, 0xf4, 0x98, 0x27, 0x44, 0xb1, //0x00003c28 .quad -5673366092687344822
	0x83, 0xd5, 0x11, 0xd7, 0x43, 0x56, 0x40, 0x40, //0x00003c30 .quad 4629795266307937667
	0x1d, 0xfa, 0xc7, 0x31, 0x7f, 0x31, 0x95, 0xdd, //0x00003c38 .quad -2480021597431793123
	0x72, 0x25, 0x6b, 0x66, 0xea, 0x35, 0x28, 0x48, //0x00003c40 .quad 5199465050656154994
	0x52, 0xfc, 0x1c, 0x7f, 0xef, 0x3e, 0x7d, 0x8a, //0x00003c48 .quad -8467542526035952558
	0xcf, 0xee, 0x05, 0x00, 0x65, 0x43, 0x32, 0xda, //0x00003c50 .quad -2724040723534582065
	0x66, 0x3b, 0xe4, 0x5e, 0xab, 0x8e, 0x1c, 0xad, //0x00003c58 .quad -5972742139117552794
	0x82, 0x6a, 0x07, 0x40, 0x3e, 0xd4, 0xbe, 0x90, //0x00003c60 .quad -8016736922845615486
	0x40, 0x4a, 0x9d, 0x36, 0x56, 0xb2, 0x63, 0xd8, //0x00003c68 .quad -2854241655469553088
	0x91, 0xa2, 0x04, 0xe8, 0xa6, 0x44, 0x77, 0x5a, //0x00003c70 .quad 6518754469289960081
	0x68, 0x4e, 0x22, 0xe2, 0x75, 0x4f, 0x3e, 0x87, //0x00003c78 .quad -8701430062309552536
	0x36, 0xcb, 0x05, 0xa2, 0xd0, 0x15, 0x15, 0x71, //0x00003c80 .quad 8148443086612450102
	0x02, 0xe2, 0xaa, 0x5a, 0x53, 0xe3, 0x0d, 0xa9, //0x00003c88 .quad -6265101559459552766
	0x03, 0x3e, 0x87, 0xca, 0x44, 0x5b, 0x5a, 0x0d, //0x00003c90 .quad 962181821410786819
	0x83, 0x9a, 0x55, 0x31, 0x28, 0x5c, 0x51, 0xd3, //0x00003c98 .quad -3219690930897053053
	0xc2, 0x86, 0x94, 0xfe, 0x0a, 0x79, 0x58, 0xe8, //0x00003ca0 .quad -1704479370831952190
	0x91, 0x80, 0xd5, 0x1e, 0x99, 0xd9, 0x12, 0x84, //0x00003ca8 .quad -8929835859451740015
	0x72, 0xa8, 0x39, 0xbe, 0x4d, 0x97, 0x6e, 0x62, //0x00003cb0 .quad 7092772823314835570
	0xb6, 0xe0, 0x8a, 0x66, 0xff, 0x8f, 0x17, 0xa5, //0x00003cb8 .quad -6550608805887287114
	0x8f, 0x12, 0xc8, 0x2d, 0x21, 0x3d, 0x0a, 0xfb, //0x00003cc0 .quad -357406007711231345
	0xe3, 0x98, 0x2d, 0x40, 0xff, 0x73, 0x5d, 0xce, //0x00003cc8 .quad -3576574988931720989
	0x99, 0x0b, 0x9d, 0xbc, 0x34, 0x66, 0xe6, 0x7c, //0x00003cd0 .quad 8999993282035256217
	0x8e, 0x7f, 0x1c, 0x88, 0x7f, 0x68, 0xfa, 0x80, //0x00003cd8 .quad -9152888395723407474
	0x80, 0x4e, 0xc4, 0xeb, 0xc1, 0xff, 0x1f, 0x1c, //0x00003ce0 .quad 2026619565689294464
	0x72, 0x9f, 0x23, 0x6a, 0x9f, 0x02, 0x39, 0xa1, //0x00003ce8 .quad -6829424476226871438
	0x20, 0x62, 0xb5, 0x66, 0xb2, 0xff, 0x27, 0xa3, //0x00003cf0 .quad -6690097579743157728
	0x4e, 0x87, 0xac, 0x44, 0x47, 0x43, 0x87, 0xc9, //0x00003cf8 .quad -3925094576856201394
	0xa8, 0xba, 0x62, 0x00, 0x9f, 0xff, 0xf1, 0x4b, //0x00003d00 .quad 5472436080603216552
	0x22, 0xa9, 0xd7, 0x15, 0x19, 0x14, 0xe9, 0xfb, //0x00003d08 .quad -294682202642863838
	0xa9, 0xb4, 0x3d, 0x60, 0xc3, 0x3f, 0x77, 0x6f, //0x00003d10 .quad 8031958568804398249
	0xb5, 0xc9, 0xa6, 0xad, 0x8f, 0xac, 0x71, 0x9d, //0x00003d18 .quad -7101705404292871755
	0xd3, 0x21, 0x4d, 0x38, 0xb4, 0x0f, 0x55, 0xcb, //0x00003d20 .quad -3795109844276665901
	0x22, 0x7c, 0x10, 0x99, 0xb3, 0x17, 0xce, 0xc4, //0x00003d28 .quad -4265445736938701790
	0x48, 0x6a, 0x60, 0x46, 0xa1, 0x53, 0x2a, 0x7e, //0x00003d30 .quad 9091170749936331336
	0x2b, 0x9b, 0x54, 0x7f, 0xa0, 0x9d, 0x01, 0xf6, //0x00003d38 .quad -720121152745989333
	0x6d, 0x42, 0xfc, 0xcb, 0x44, 0x74, 0xda, 0x2e, //0x00003d40 .quad 3376138709496513133
	0xfb, 0xe0, 0x94, 0x4f, 0x84, 0x02, 0xc1, 0x99, //0x00003d48 .quad -7367604748107325189
	0x08, 0x53, 0xfb, 0xfe, 0x55, 0x11, 0x91, 0xfa, //0x00003d50 .quad -391512631556746488
	0x39, 0x19, 0x7a, 0x63, 0x25, 0x43, 0x31, 0xc0, //0x00003d58 .quad -4597819916706768583
	0xca, 0x27, 0xba, 0x7e, 0xab, 0x55, 0x35, 0x79, //0x00003d60 .quad 8733981247408842698
	0x88, 0x9f, 0x58, 0xbc, 0xee, 0x93, 0x3d, 0xf0, //0x00003d68 .quad -1135588877456072824
	0xde, 0x58, 0x34, 0x2f, 0x8b, 0x55, 0xc1, 0x4b, //0x00003d70 .quad 5458738279630526686
	0xb5, 0x63, 0xb7, 0x35, 0x75, 0x7c, 0x26, 0x96, //0x00003d78 .quad -7627272076051127371
	0x16, 0x6f, 0x01, 0xfb, 0xed, 0xaa, 0xb1, 0x9e, //0x00003d80 .quad -7011635205744005354
	0xa2, 0x3c, 0x25, 0x83, 0x92, 0x1b, 0xb0, 0xbb, //0x00003d88 .quad -4922404076636521310
	0xdc, 0xca, 0xc1, 0x79, 0xa9, 0x15, 0x5e, 0x46, //0x00003d90 .quad 5070514048102157020
	0xcb, 0x8b, 0xee, 0x23, 0x77, 0x22, 0x9c, 0xea, //0x00003d98 .quad -1541319077368263733
	0xc9, 0x1e, 0x19, 0xec, 0x89, 0xcd, 0xfa, 0x0b, //0x00003da0 .quad 863228270850154185
	0x5f, 0x17, 0x75, 0x76, 0x8a, 0x95, 0xa1, 0x92, //0x00003da8 .quad -7880853450996246689
	0x7b, 0x66, 0x1f, 0x67, 0xec, 0x80, 0xf9, 0xce, //0x00003db0 .quad -3532650679864695173
	0x36, 0x5d, 0x12, 0x14, 0xed, 0xfa, 0x49, 0xb7, //0x00003db8 .quad -5239380795317920458
	0x1a, 0x40, 0xe7, 0x80, 0x27, 0xe1, 0xb7, 0x82, //0x00003dc0 .quad -9027499368258256870
	0x84, 0xf4, 0x16, 0x59, 0xa8, 0x79, 0x1c, 0xe5, //0x00003dc8 .quad -1937539975720012668
	0x10, 0x88, 0x90, 0xb0, 0xb8, 0xec, 0xb2, 0xd1, //0x00003dd0 .quad -3336344095947716592
	0xd2, 0x58, 0xae, 0x37, 0x09, 0xcc, 0x31, 0x8f, //0x00003dd8 .quad -8128491512466089774
	0x15, 0xaa, 0xb4, 0xdc, 0xe6, 0xa7, 0x1f, 0x86, //0x00003de0 .quad -8782116138362033643
	0x07, 0xef, 0x99, 0x85, 0x0b, 0x3f, 0xfe, 0xb2, //0x00003de8 .quad -5548928372155224313
	0x9a, 0xd4, 0xe1, 0x93, 0xe0, 0x91, 0xa7, 0x67, //0x00003df0 .quad 7469098900757009562
	0xc9, 0x6a, 0x00, 0x67, 0xce, 0xce, 0xbd, 0xdf, //0x00003df8 .quad -2324474446766642487
	0xe0, 0x24, 0x6d, 0x5c, 0x2c, 0xbb, 0xc8, 0xe0, //0x00003e00 .quad -2249342214667950880
	0xbd, 0x42, 0x60, 0x00, 0x41, 0xa1, 0xd6, 0x8b, //0x00003e08 .quad -8370325556870233411
	0x18, 0x6e, 0x88, 0x73, 0xf7, 0xe9, 0xfa, 0x58, //0x00003e10 .quad 6411694268519837208
	0x6d, 0x53, 0x78, 0x40, 0x91, 0x49, 0xcc, 0xae, //0x00003e18 .quad -5851220927660403859
	0x9e, 0x89, 0x6a, 0x50, 0x75, 0xa4, 0x39, 0xaf, //0x00003e20 .quad -5820440219632367202
	0x48, 0x68, 0x96, 0x90, 0xf5, 0x5b, 0x7f, 0xda, //0x00003e28 .quad -2702340141148116920
	0x03, 0x96, 0x42, 0x52, 0xc9, 0x06, 0x84, 0x6d, //0x00003e30 .quad 7891439908798240259
	0x2d, 0x01, 0x5e, 0x7a, 0x79, 0x99, 0x8f, 0x88, //0x00003e38 .quad -8606491615858654931
	0x83, 0x3b, 0xd3, 0xa6, 0x7b, 0x08, 0xe5, 0xc8, //0x00003e40 .quad -3970758169284363389
	0x78, 0x81, 0xf5, 0xd8, 0xd7, 0x7f, 0xb3, 0xaa, //0x00003e48 .quad -6146428501395930760
	0x64, 0x0a, 0x88, 0x90, 0x9a, 0x4a, 0x1e, 0xfb, //0x00003e50 .quad -351761693178066332
	0xd6, 0xe1, 0x32, 0xcf, 0xcd, 0x5f, 0x60, 0xd5, //0x00003e58 .quad -3071349608317525546
	0x7f, 0x06, 0x55, 0x9a, 0xa0, 0xee, 0xf2, 0x5c, //0x00003e60 .quad 6697677969404790399
	0x26, 0xcd, 0x7f, 0xa1, 0xe0, 0x3b, 0x5c, 0x85, //0x00003e68 .quad -8837122532839535322
	0x1e, 0x48, 0xea, 0xc0, 0x48, 0xaa, 0x2f, 0xf4, //0x00003e70 .quad -851274575098787810
	0x6f, 0xc0, 0xdf, 0xc9, 0xd8, 0x4a, 0xb3, 0xa6, //0x00003e78 .quad -6434717147622031249
	0x26, 0xda, 0x24, 0xf1, 0xda, 0x94, 0x3b, 0xf1, //0x00003e80 .quad -1064093218873484762
	0x8b, 0xb0, 0x57, 0xfc, 0x8e, 0x1d, 0x60, 0xd0, //0x00003e88 .quad -3431710416100151157
	0x58, 0x08, 0xb7, 0xd6, 0x08, 0x3d, 0xc5, 0x76, //0x00003e90 .quad 8558313775058847832
	0x57, 0xce, 0xb6, 0x5d, 0x79, 0x12, 0x3c, 0x82, //0x00003e98 .quad -9062348037703676329
	0x6e, 0xca, 0x64, 0x0c, 0x4b, 0x8c, 0x76, 0x54, //0x00003ea0 .quad 6086206200396171886
	0xed, 0x81, 0x24, 0xb5, 0x17, 0x17, 0xcb, 0xa2, //0x00003ea8 .quad -6716249028702207507
	0x09, 0xfd, 0x7d, 0xcf, 0x5d, 0x2f, 0x94, 0xa9, //0x00003eb0 .quad -6227300304786948855
	0x68, 0xa2, 0x6d, 0xa2, 0xdd, 0xdc, 0x7d, 0xcb, //0x00003eb8 .quad -3783625267450371480
	0x4c, 0x7c, 0x5d, 0x43, 0x35, 0x3b, 0xf9, 0xd3, //0x00003ec0 .quad -3172439362556298164
	0x02, 0x0b, 0x09, 0x0b, 0x15, 0x54, 0x5d, 0xfe, //0x00003ec8 .quad -117845565885576446
	0xaf, 0x6d, 0x1a, 0x4a, 0x01, 0xc5, 0x7b, 0xc4, //0x00003ed0 .quad -4288617610811380305
	0xe1, 0xa6, 0xe5, 0x26, 0x8d, 0x54, 0xfa, 0x9e, //0x00003ed8 .quad -6991182506319567135
	0x1b, 0x09, 0xa1, 0x9c, 0x41, 0xb6, 0x9a, 0x35, //0x00003ee0 .quad 3862600023340550427
	0x9a, 0x10, 0x9f, 0x70, 0xb0, 0xe9, 0xb8, 0xc6, //0x00003ee8 .quad -4127292114472071014
	0x62, 0x4b, 0xc9, 0x03, 0xd2, 0x63, 0x01, 0xc3, //0x00003ef0 .quad -4395122007679087774
	0xc0, 0xd4, 0xc6, 0x8c, 0x1c, 0x24, 0x67, 0xf8, //0x00003ef8 .quad -547429124662700864
	0x1d, 0xcf, 0x5d, 0x42, 0x63, 0xde, 0xe0, 0x79, //0x00003f00 .quad 8782263791269039901
	0xf8, 0x44, 0xfc, 0xd7, 0x91, 0x76, 0x40, 0x9b, //0x00003f08 .quad -7259672230555269896
	0xe4, 0x42, 0xf5, 0x12, 0xfc, 0x15, 0x59, 0x98, //0x00003f10 .quad -7468914334623251740
	0x36, 0x56, 0xfb, 0x4d, 0x36, 0x94, 0x10, 0xc2, //0x00003f18 .quad -4462904269766699466
	0x9d, 0x93, 0xb2, 0x17, 0x7b, 0x5b, 0x6f, 0x3e, //0x00003f20 .quad 4498915137003099037
	0xc4, 0x2b, 0x7a, 0xe1, 0x43, 0xb9, 0x94, 0xf2, //0x00003f28 .quad -966944318780986428
	0x42, 0x9c, 0xcf, 0xee, 0x2c, 0x99, 0x05, 0xa7, //0x00003f30 .quad -6411550076227838910
	0x5a, 0x5b, 0xec, 0x6c, 0xca, 0xf3, 0x9c, 0x97, //0x00003f38 .quad -7521869226879198374
	0x53, 0x83, 0x83, 0x2a, 0x78, 0xff, 0xc6, 0x50, //0x00003f40 .quad 5820620459997365075
	0x31, 0x72, 0x27, 0x08, 0xbd, 0x30, 0x84, 0xbd, //0x00003f48 .quad -4790650515171610063
	0x28, 0x64, 0x24, 0x35, 0x56, 0xbf, 0xf8, 0xa4, //0x00003f50 .quad -6559282480285457368
	0xbd, 0x4e, 0x31, 0x4a, 0xec, 0x3c, 0xe5, 0xec, //0x00003f58 .quad -1376627125537124675
	0x99, 0xbe, 0x36, 0xe1, 0x95, 0x77, 0x1b, 0x87, //0x00003f60 .quad -8711237568605798759
	0x36, 0xd1, 0x5e, 0xae, 0x13, 0x46, 0x0f, 0x94, //0x00003f68 .quad -7777920981101784778
	0x3f, 0x6e, 0x84, 0x59, 0x7b, 0x55, 0xe2, 0x28, //0x00003f70 .quad 2946011094524915263
	0x84, 0x85, 0xf6, 0x99, 0x98, 0x17, 0x13, 0xb9, //0x00003f78 .quad -5110715207949843068
	0xcf, 0x89, 0xe5, 0x2f, 0xda, 0xea, 0x1a, 0x33, //0x00003f80 .quad 3682513868156144079
	0xe5, 0x26, 0x74, 0xc0, 0x7e, 0xdd, 0x57, 0xe7, //0x00003f88 .quad -1776707991509915931
	0x21, 0x76, 0xef, 0x5d, 0xc8, 0xd2, 0xf0, 0x3f, //0x00003f90 .quad 4607414176811284001
	0x4f, 0x98, 0x48, 0x38, 0x6f, 0xea, 0x96, 0x90, //0x00003f98 .quad -8027971522334779313
	0xa9, 0x53, 0x6b, 0x75, 0x7a, 0x07, 0xed, 0x0f, //0x00003fa0 .quad 1147581702586717097
	0x63, 0xbe, 0x5a, 0x06, 0x0b, 0xa5, 0xbc, 0xb4, //0x00003fa8 .quad -5423278384491086237
	0x94, 0x28, 0xc6, 0x12, 0x59, 0x49, 0xe8, 0xd3, //0x00003fb0 .quad -3177208890193991532
	0xfb, 0x6d, 0xf1, 0xc7, 0x4d, 0xce, 0xeb, 0xe1, //0x00003fb8 .quad -2167411962186469893
	0x5c, 0xd9, 0xbb, 0xab, 0xd7, 0x2d, 0x71, 0x64, //0x00003fc0 .quad 7237616480483531100
	0xbd, 0xe4, 0xf6, 0x9c, 0xf0, 0x60, 0x33, 0x8d, //0x00003fc8 .quad -8272161504007625539
	0xb3, 0xcf, 0xaa, 0x96, 0x4d, 0x79, 0x8d, 0xbd, //0x00003fd0 .quad -4788037454677749837
	0xec, 0x9d, 0x34, 0xc4, 0x2c, 0x39, 0x80, 0xb0, //0x00003fd8 .quad -5728515861582144020
	0xa0, 0x83, 0x55, 0xfc, 0xa0, 0xd7, 0xf0, 0xec, //0x00003fe0 .quad -1373360799919799392
	0x67, 0xc5, 0x41, 0xf5, 0x77, 0x47, 0xa0, 0xdc, //0x00003fe8 .quad -2548958808550292121
	0x44, 0x72, 0xb5, 0x9d, 0xc4, 0x86, 0x16, 0xf4, //0x00003ff0 .quad -858350499949874620
	0x60, 0x1b, 0x49, 0xf9, 0xaa, 0x2c, 0xe4, 0x89, //0x00003ff8 .quad -8510628282985014432
	0xd5, 0xce, 0x22, 0xc5, 0x75, 0x28, 0x1c, 0x31, //0x00004000 .quad 3538747893490044629
	0x39, 0x62, 0x9b, 0xb7, 0xd5, 0x37, 0x5d, 0xac, //0x00004008 .quad -6026599335303880135
	0x8b, 0x82, 0x6b, 0x36, 0x93, 0x32, 0x63, 0x7d, //0x00004010 .quad 9035120885289943691
	0xc7, 0x3a, 0x82, 0x25, 0xcb, 0x85, 0x74, 0xd7, //0x00004018 .quad -2921563150702462265
	0x97, 0x31, 0x03, 0x02, 0x9c, 0xff, 0x5d, 0xae, //0x00004020 .quad -5882264492762254953
	0xbc, 0x64, 0x71, 0xf7, 0x9e, 0xd3, 0xa8, 0x86, //0x00004028 .quad -8743505996830120772
	0xfc, 0xfd, 0x83, 0x02, 0x83, 0x7f, 0xf5, 0xd9, //0x00004030 .quad -2741144597525430788
	0xeb, 0xbd, 0x4d, 0xb5, 0x86, 0x08, 0x53, 0xa8, //0x00004038 .quad -6317696477610263061
	0x7b, 0xfd, 0x24, 0xc3, 0x63, 0xdf, 0x72, 0xd0, //0x00004040 .quad -3426430746906788485
	0x66, 0x2d, 0xa1, 0x62, 0xa8, 0xca, 0x67, 0xd2, //0x00004048 .quad -3285434578585440922
	0x6d, 0x1e, 0xf7, 0x59, 0x9e, 0xcb, 0x47, 0x42, //0x00004050 .quad 4776009810824339053
	0x60, 0xbc, 0xa4, 0x3d, 0xa9, 0xde, 0x80, 0x83, //0x00004058 .quad -8970925639256982432
	0x08, 0xe6, 0x74, 0xf0, 0x85, 0xbe, 0xd9, 0x52, //0x00004060 .quad 5970012263530423816
	0x78, 0xeb, 0x0d, 0x8d, 0x53, 0x16, 0x61, 0xa4, //0x00004068 .quad -6601971030643840136
	0x8b, 0x1f, 0x92, 0x6c, 0x27, 0x2e, 0x90, 0x67, //0x00004070 .quad 7462515329413029771
	0x56, 0x66, 0x51, 0x70, 0xe8, 0x5b, 0x79, 0xcd, //0x00004078 .quad -3640777769877412266
	0xb6, 0x53, 0xdb, 0xa3, 0xd8, 0x1c, 0xba, 0x00, //0x00004080 .quad 52386062455755702
	0xf6, 0xdf, 0x32, 0x46, 0x71, 0xd9, 0x6b, 0x80, //0x00004088 .quad -9193015133814464522
	0xa4, 0x28, 0xd2, 0xcc, 0x0e, 0xa4, 0xe8, 0x80, //0x00004090 .quad -9157889458785081180
	0xf3, 0x97, 0xbf, 0x97, 0xcd, 0xcf, 0x86, 0xa0, //0x00004098 .quad -6879582898840692749
	0xcd, 0xb2, 0x06, 0x80, 0x12, 0xcd, 0x22, 0x61, //0x000040a0 .quad 6999382250228200141
	0xf0, 0x7d, 0xaf, 0xfd, 0xc0, 0x83, 0xa8, 0xc8, //0x000040a8 .quad -3987792605123478032
	0x81, 0x5f, 0x08, 0x20, 0x57, 0x80, 0x6b, 0x79, //0x000040b0 .quad 8749227812785250177
	0x6c, 0x5d, 0x1b, 0x3d, 0xb1, 0xa4, 0xd2, 0xfa, //0x000040b8 .quad -373054737976959636
	0xb0, 0x3b, 0x05, 0x74, 0x36, 0x30, 0xe3, 0xcb, //0x000040c0 .quad -3755104653863994448
	0x63, 0x1a, 0x31, 0xc6, 0xee, 0xa6, 0xc3, 0x9c, //0x000040c8 .quad -7150688238876681629
	0x9c, 0x8a, 0x06, 0x11, 0x44, 0xfc, 0xdb, 0xbe, //0x000040d0 .quad -4693880817329993060
	0xfc, 0x60, 0xbd, 0x77, 0xaa, 0x90, 0xf4, 0xc3, //0x000040d8 .quad -4326674280168464132
	0x44, 0x2d, 0x48, 0x15, 0x55, 0xfb, 0x92, 0xee, //0x000040e0 .quad -1255665003235103420
	0x3b, 0xb9, 0xac, 0x15, 0xd5, 0xb4, 0xf1, 0xf4, //0x000040e8 .quad -796656831783192261
	0x4a, 0x1c, 0x4d, 0x2d, 0x15, 0xdd, 0x1b, 0x75, //0x000040f0 .quad 8438581409832836170
	0xc5, 0xf3, 0x8b, 0x2d, 0x05, 0x11, 0x17, 0x99, //0x000040f8 .quad -7415439547505577019
	0x5d, 0x63, 0xa0, 0x78, 0x5a, 0xd4, 0x62, 0xd2, //0x00004100 .quad -3286831292991118499
	0xb6, 0xf0, 0xee, 0x78, 0x46, 0xd5, 0x5c, 0xbf, //0x00004108 .quad -4657613415954583370
	0x34, 0x7c, 0xc8, 0x16, 0x71, 0x89, 0xfb, 0x86, //0x00004110 .quad -8720225134666286028
	0xe4, 0xac, 0x2a, 0x17, 0x98, 0x0a, 0x34, 0xef, //0x00004118 .quad -1210330751515841308
	0xa0, 0x4d, 0x3d, 0xae, 0xe6, 0x35, 0x5d, 0xd4, //0x00004120 .quad -3144297699952734816
	0x0e, 0xac, 0x7a, 0x0e, 0x9f, 0x86, 0x80, 0x95, //0x00004128 .quad -7673985747338482674
	0x09, 0xa1, 0xcc, 0x59, 0x60, 0x83, 0x74, 0x89, //0x00004130 .quad -8542058143368306423
	0x12, 0x57, 0x19, 0xd2, 0x46, 0xa8, 0xe0, 0xba, //0x00004138 .quad -4980796165745715438
	0x4b, 0xc9, 0x3f, 0x70, 0x38, 0xa4, 0xd1, 0x2b, //0x00004140 .quad 3157485376071780683
	0xd7, 0xac, 0x9f, 0x86, 0x58, 0xd2, 0x98, 0xe9, //0x00004148 .quad -1614309188754756393
	0xcf, 0xdd, 0x27, 0x46, 0xa3, 0x06, 0x63, 0x7b, //0x00004150 .quad 8890957387685944783
	0x06, 0xcc, 0x23, 0x54, 0x77, 0x83, 0xff, 0x91, //0x00004158 .quad -7926472270612804602
	0x42, 0xd5, 0xb1, 0x17, 0x4c, 0xc8, 0x3b, 0x1a, //0x00004160 .quad 1890324697752655170
	0x08, 0xbf, 0x2c, 0x29, 0x55, 0x64, 0x7f, 0xb6, //0x00004168 .quad -5296404319838617848
	0x93, 0x4a, 0x9e, 0x1d, 0x5f, 0xba, 0xca, 0x20, //0x00004170 .quad 2362905872190818963
	0xca, 0xee, 0x77, 0x73, 0x6a, 0x3d, 0x1f, 0xe4, //0x00004178 .quad -2008819381370884406
	0x9c, 0xee, 0x82, 0x72, 0x7b, 0xb4, 0x7e, 0x54, //0x00004180 .quad 6088502188546649756
	0x3e, 0xf5, 0x2a, 0x88, 0x62, 0x86, 0x93, 0x8e, //0x00004188 .quad -8173041140997884610
	0x43, 0xaa, 0x23, 0x4f, 0x9a, 0x61, 0x9e, 0xe9, //0x00004190 .quad -1612744301171463613
	0x8d, 0xb2, 0x35, 0x2a, 0xfb, 0x67, 0x38, 0xb2, //0x00004198 .quad -5604615407819967859
	0xd4, 0x94, 0xec, 0xe2, 0x00, 0xfa, 0x05, 0x64, //0x000041a0 .quad 7207441660390446292
	0x31, 0x1f, 0xc3, 0xf4, 0xf9, 0x81, 0xc6, 0xde, //0x000041a8 .quad -2394083241347571919
	0x04, 0xdd, 0xd3, 0x8d, 0x40, 0xbc, 0x83, 0xde, //0x000041b0 .quad -2412877989897052924
	0x7e, 0xf3, 0xf9, 0x38, 0x3c, 0x11, 0x3c, 0x8b, //0x000041b8 .quad -8413831053483314306
	0x45, 0xd4, 0x48, 0xb1, 0x50, 0xab, 0x24, 0x96, //0x000041c0 .quad -7627783505798704059
	0x5e, 0x70, 0x38, 0x47, 0x8b, 0x15, 0x0b, 0xae, //0x000041c8 .quad -5905602798426754978
	0x57, 0x09, 0x9b, 0xdd, 0x24, 0xd6, 0xad, 0x3b, //0x000041d0 .quad 4300328673033783639
	0x76, 0x8c, 0x06, 0x19, 0xee, 0xda, 0x8d, 0xd9, //0x000041d8 .quad -2770317479606055818
	0xd6, 0xe5, 0x80, 0x0a, 0xd7, 0xa5, 0x4c, 0xe5, //0x000041e0 .quad -1923980597781273130
	0xc9, 0x17, 0xa4, 0xcf, 0xd4, 0xa8, 0xf8, 0x87, //0x000041e8 .quad -8648977452394866743
	0x4c, 0x1f, 0x21, 0xcd, 0x4c, 0xcf, 0x9f, 0x5e, //0x000041f0 .quad 6818396289628184396
	0xbc, 0x1d, 0x8d, 0x03, 0x0a, 0xd3, 0xf6, 0xa9, //0x000041f8 .quad -6199535797066195524
	0x1f, 0x67, 0x69, 0x00, 0x20, 0xc3, 0x47, 0x76, //0x00004200 .quad 8522995362035230495
	0x2b, 0x65, 0x70, 0x84, 0xcc, 0x87, 0x74, 0xd4, //0x00004208 .quad -3137733727905356501
	0x73, 0xe0, 0x41, 0x00, 0xf4, 0xd9, 0xec, 0x29, //0x00004210 .quad 3021029092058325107
	0x3b, 0x3f, 0xc6, 0xd2, 0xdf, 0xd4, 0xc8, 0x84, //0x00004218 .quad -8878612607581929669
	0x90, 0x58, 0x52, 0x00, 0x71, 0x10, 0x68, 0xf4, //0x00004220 .quad -835399653354481520
	0x09, 0xcf, 0x77, 0xc7, 0x17, 0x0a, 0xfb, 0xa5, //0x00004228 .quad -6486579741050024183
	0xb4, 0xee, 0x66, 0x40, 0x8d, 0x14, 0x82, 0x71, //0x00004230 .quad 8179122470161673908
	0xcc, 0xc2, 0x55, 0xb9, 0x9d, 0xcc, 0x79, 0xcf, //0x00004238 .quad -3496538657885142324
	0x30, 0x55, 0x40, 0x48, 0xd8, 0x4c, 0xf1, 0xc6, //0x00004240 .quad -4111420493003729616
	0xbf, 0x99, 0xd5, 0x93, 0xe2, 0x1f, 0xac, 0x81, //0x00004248 .quad -9102865688819295809
	0x7c, 0x6a, 0x50, 0x5a, 0x0e, 0xa0, 0xad, 0xb8, //0x00004250 .quad -5139275616254662020
	0x2f, 0x00, 0xcb, 0x38, 0xdb, 0x27, 0x17, 0xa2, //0x00004258 .quad -6766896092596731857
	0x1c, 0x85, 0xe4, 0xf0, 0x11, 0x08, 0xd9, 0xa6, //0x00004260 .quad -6424094520318327524
	0x3b, 0xc0, 0xfd, 0x06, 0xd2, 0xf1, 0x9c, 0xca, //0x00004268 .quad -3846934097318526917
	0x63, 0xa6, 0x1d, 0x6d, 0x16, 0x4a, 0x8f, 0x90, //0x00004270 .quad -8030118150397909405
	0x4a, 0x30, 0xbd, 0x88, 0x46, 0x2e, 0x44, 0xfd, //0x00004278 .quad -196981603220770742
	0xfe, 0x87, 0x32, 0x04, 0x4e, 0x8e, 0x59, 0x9a, //0x00004280 .quad -7324666853212387330
	0x2e, 0x3e, 0x76, 0x15, 0xec, 0x9c, 0x4a, 0x9e, //0x00004288 .quad -7040642529654063570
	0xfd, 0x29, 0x3f, 0x85, 0xe1, 0xf1, 0xef, 0x40, //0x00004290 .quad 4679224488766679549
	0xba, 0xcd, 0xd3, 0x1a, 0x27, 0x44, 0xdd, 0xc5, //0x00004298 .quad -4189117143640191558
	0x7c, 0xf4, 0x8e, 0xe6, 0x59, 0xee, 0x2b, 0xd1, //0x000042a0 .quad -3374341425896426372
	0x28, 0xc1, 0x88, 0xe1, 0x30, 0x95, 0x54, 0xf7, //0x000042a8 .quad -624710411122851544
	0xce, 0x58, 0x19, 0x30, 0xf8, 0x74, 0xbb, 0x82, //0x000042b0 .quad -9026492418826348338
	0xb9, 0x78, 0xf5, 0x8c, 0x3e, 0xdd, 0x94, 0x9a, //0x000042b8 .quad -7307973034592864071
	0x01, 0xaf, 0x1f, 0x3c, 0x36, 0x52, 0x6a, 0xe3, //0x000042c0 .quad -2059743486678159615
	0xe7, 0xd6, 0x32, 0x30, 0x8e, 0x14, 0x3a, 0xc1, //0x000042c8 .quad -4523280274813692185
	0xc1, 0x9a, 0x27, 0xcb, 0xc3, 0xe6, 0x44, 0xdc, //0x000042d0 .quad -2574679358347699519
	0xa1, 0x8c, 0x3f, 0xbc, 0xb1, 0x99, 0x88, 0xf1, //0x000042d8 .quad -1042414325089727327
	0xb9, 0xc0, 0xf8, 0x5e, 0x3a, 0x10, 0xab, 0x29, //0x000042e0 .quad 3002511419460075705
	0xe5, 0xb7, 0xa7, 0x15, 0x0f, 0x60, 0xf5, 0x96, //0x000042e8 .quad -7569037980822161435
	0xe7, 0xf0, 0xb6, 0xf6, 0x48, 0xd4, 0x15, 0x74, //0x000042f0 .quad 8364825292752482535
	0xde, 0xa5, 0x11, 0xdb, 0x12, 0xb8, 0xb2, 0xbc, //0x000042f8 .quad -4849611457600313890
	0x21, 0xad, 0x64, 0x34, 0x5b, 0x49, 0x1b, 0x11, //0x00004300 .quad 1232659579085827361
	0x56, 0x0f, 0xd6, 0x91, 0x17, 0x66, 0xdf, 0xeb, //0x00004308 .quad -1450328303573004458
	0x34, 0xec, 0xbe, 0x00, 0xd9, 0x0d, 0xb1, 0xca, //0x00004310 .quad -3841273781498745804
	0x95, 0xc9, 0x25, 0xbb, 0xce, 0x9f, 0x6b, 0x93, //0x00004318 .quad -7823984217374209643
	0x42, 0xa7, 0xee, 0x40, 0x4f, 0x51, 0x5d, 0x3d, //0x00004320 .quad 4421779809981343554
	0xfb, 0x3b, 0xef, 0x69, 0xc2, 0x87, 0x46, 0xb8, //0x00004328 .quad -5168294253290374149
	0x12, 0x51, 0x2a, 0x11, 0xa3, 0xa5, 0xb4, 0x0c, //0x00004330 .quad 915538744049291538
	0xfa, 0x0a, 0x6b, 0x04, 0xb3, 0x29, 0x58, 0xe6, //0x00004338 .quad -1848681798185579782
	0xab, 0x72, 0xba, 0xea, 0x85, 0xe7, 0xf0, 0x47, //0x00004340 .quad 5183897733458195115
	0xdc, 0xe6, 0xc2, 0xe2, 0x0f, 0x1a, 0xf7, 0x8f, //0x00004348 .quad -8072955151507069220
	0x56, 0x0f, 0x69, 0x65, 0x67, 0x21, 0xed, 0x59, //0x00004350 .quad 6479872166822743894
	0x93, 0xa0, 0x73, 0xdb, 0x93, 0xe0, 0xf4, 0xb3, //0x00004358 .quad -5479507920956448621
	0x2c, 0x53, 0xc3, 0x3e, 0xc1, 0x69, 0x68, 0x30, //0x00004360 .quad 3488154190101041964
	0xb8, 0x88, 0x50, 0xd2, 0xb8, 0x18, 0xf2, 0xe0, //0x00004368 .quad -2237698882768172872
	0xfb, 0x13, 0x3a, 0xc7, 0x18, 0x42, 0x41, 0x1e, //0x00004370 .quad 2180096368813151227
	0x73, 0x55, 0x72, 0x83, 0x73, 0x4f, 0x97, 0x8c, //0x00004378 .quad -8316090829371189901
	0xfa, 0x98, 0x08, 0xf9, 0x9e, 0x92, 0xd1, 0xe5, //0x00004380 .quad -1886565557410948870
	0xcf, 0xea, 0x4e, 0x64, 0x50, 0x23, 0xbd, 0xaf, //0x00004388 .quad -5783427518286599473
	0x39, 0xbf, 0x4a, 0xb7, 0x46, 0xf7, 0x45, 0xdf, //0x00004390 .quad -2358206946763686087
	0x83, 0xa5, 0x62, 0x7d, 0x24, 0x6c, 0xac, 0xdb, //0x00004398 .quad -2617598379430861437
	0x83, 0xb7, 0x8e, 0x32, 0x8c, 0xba, 0x8b, 0x6b, //0x000043a0 .quad 7749492695127472003
	0x72, 0xa7, 0x5d, 0xce, 0x96, 0xc3, 0x4b, 0x89, //0x000043a8 .quad -8553528014785370254
	0x64, 0x65, 0x32, 0x3f, 0x2f, 0xa9, 0x6e, 0x06, //0x000043b0 .quad 463493832054564196
	0x4f, 0x11, 0xf5, 0x81, 0x7c, 0xb4, 0x9e, 0xab, //0x000043b8 .quad -6080224000054324913
	0xbd, 0xfe, 0xfe, 0x0e, 0x7b, 0x53, 0x0a, 0xc8, //0x000043c0 .quad -4032318728359182659
	0xa2, 0x55, 0x72, 0xa2, 0x9b, 0x61, 0x86, 0xd6, //0x000043c8 .quad -2988593981640518238
	0x36, 0x5f, 0x5f, 0xe9, 0x2c, 0x74, 0x06, 0xbd, //0x000043d0 .quad -4826042214438183114
	0x85, 0x75, 0x87, 0x45, 0x01, 0xfd, 0x13, 0x86, //0x000043d8 .quad -8785400266166405755
	0x04, 0x37, 0xb7, 0x23, 0x38, 0x11, 0x48, 0x2c, //0x000043e0 .quad 3190819268807046916
	0xe7, 0x52, 0xe9, 0x96, 0x41, 0xfc, 0x98, 0xa7, //0x000043e8 .quad -6370064314280619289
	0xc5, 0x04, 0xa5, 0x2c, 0x86, 0x15, 0x5a, 0xf7, //0x000043f0 .quad -623161932418579259
	0xa0, 0xa7, 0xa3, 0xfc, 0x51, 0x3b, 0x7f, 0xd1, //0x000043f8 .quad -3350894374423386208
	0xfb, 0x22, 0xe7, 0xdb, 0x73, 0x4d, 0x98, 0x9a, //0x00004400 .quad -7307005235402693893
	0xc4, 0x48, 0xe6, 0x3d, 0x13, 0x85, 0xef, 0x82, //0x00004408 .quad -9011838011655698236
	0xba, 0xeb, 0xe0, 0xd2, 0xd0, 0x60, 0x3e, 0xc1, //0x00004410 .quad -4522070525825979462
	0xf5, 0xda, 0x5f, 0x0d, 0x58, 0x66, 0xab, 0xa3, //0x00004418 .quad -6653111496142234891
	0xa8, 0x26, 0x99, 0x07, 0x05, 0xf9, 0x8d, 0x31, //0x00004420 .quad 3570783879572301480
	0xb3, 0xd1, 0xb7, 0x10, 0xee, 0x3f, 0x96, 0xcc, //0x00004428 .quad -3704703351750405709
	0x52, 0x70, 0x7f, 0x49, 0x46, 0x77, 0xf1, 0xfd, //0x00004430 .quad -148206168962011054
	0x1f, 0xc6, 0xe5, 0x94, 0xe9, 0xcf, 0xbb, 0xff, //0x00004438 .quad -19193171260619233
	0x33, 0xa6, 0xef, 0xed, 0x8b, 0xea, 0xb6, 0xfe, //0x00004440 .quad -92628855601256909
	0xd3, 0x9b, 0x0f, 0xfd, 0xf1, 0x61, 0xd5, 0x9f, //0x00004448 .quad -6929524759678968877
	0xc0, 0x8f, 0x6b, 0xe9, 0x2e, 0xa5, 0x64, 0xfe, //0x00004450 .quad -115786069501571136
	0xc8, 0x82, 0x53, 0x7c, 0x6e, 0xba, 0xca, 0xc7, //0x00004458 .quad -4050219931171323192
	0xb0, 0x73, 0xc6, 0xa3, 0x7a, 0xce, 0xfd, 0x3d, //0x00004460 .quad 4466953431550423984
	0x7b, 0x63, 0x68, 0x1b, 0x0a, 0x69, 0xbd, 0xf9, //0x00004468 .quad -451088895536766085
	0x4e, 0x08, 0x5c, 0xa6, 0x0c, 0xa1, 0xbe, 0x06, //0x00004470 .quad 486002885505321038
	0x2d, 0x3e, 0x21, 0x51, 0xa6, 0x61, 0x16, 0x9c, //0x00004478 .quad -7199459587351560659
	0x62, 0x0a, 0xf3, 0xcf, 0x4f, 0x49, 0x6e, 0x48, //0x00004480 .quad 5219189625309039202
	0xb8, 0x8d, 0x69, 0xe5, 0x0f, 0xfa, 0x1b, 0xc3, //0x00004488 .quad -4387638465762062920
	0xfa, 0xcc, 0xef, 0xc3, 0xa3, 0xdb, 0x89, 0x5a, //0x00004490 .quad 6523987031636299002
	0x26, 0xf1, 0xc3, 0xde, 0x93, 0xf8, 0xe2, 0xf3, //0x00004498 .quad -872862063775190746
	0x1c, 0xe0, 0x75, 0x5a, 0x46, 0x29, 0x96, 0xf8, //0x000044a0 .quad -534194123654701028
	0xb7, 0x76, 0x3a, 0x6b, 0x5c, 0xdb, 0x6d, 0x98, //0x000044a8 .quad -7463067817500576073
	0x23, 0x58, 0x13, 0xf1, 0x97, 0xb3, 0xbb, 0xf6, //0x000044b0 .quad -667742654568376285
	0x65, 0x14, 0x09, 0x86, 0x33, 0x52, 0x89, 0xbe, //0x000044b8 .quad -4717148753448332187
	0x2c, 0x2e, 0x58, 0xed, 0x7d, 0xa0, 0x6a, 0x74, //0x000044c0 .quad 8388693718644305452
	0x7f, 0x59, 0x8b, 0x67, 0xc0, 0xa6, 0x2b, 0xee, //0x000044c8 .quad -1284749923383027329
	0xdc, 0x1c, 0x57, 0xb4, 0x4e, 0xa4, 0xc2, 0xa8, //0x000044d0 .quad -6286281471915778852
	0xef, 0x17, 0xb7, 0x40, 0x38, 0x48, 0xdb, 0x94, //0x000044d8 .quad -7720497729755473937
	0x13, 0xe4, 0x6c, 0x61, 0x62, 0x4d, 0xf3, 0x92, //0x000044e0 .quad -7857851839894723565
	0xeb, 0xdd, 0xe4, 0x50, 0x46, 0x1a, 0x12, 0xba, //0x000044e8 .quad -5038936143766954517
	0x17, 0x1d, 0xc8, 0xf9, 0xba, 0x20, 0xb0, 0x77, //0x000044f0 .quad 8624429273841147159
	0x66, 0x15, 0x1e, 0xe5, 0xd7, 0xa0, 0x96, 0xe8, //0x000044f8 .quad -1686984161281305242
	0x2e, 0x12, 0x1d, 0xdc, 0x74, 0x14, 0xce, 0x0a, //0x00004500 .quad 778582277723329070
	0x60, 0xcd, 0x32, 0xef, 0x86, 0x24, 0x5e, 0x91, //0x00004508 .quad -7971894128441897632
	0xba, 0x56, 0x24, 0x13, 0x92, 0x99, 0x81, 0x0d, //0x00004510 .quad 973227847154161338
	0xb8, 0x80, 0xff, 0xaa, 0xa8, 0xad, 0xb5, 0xb5, //0x00004518 .quad -5353181642124984136
	0x69, 0x6c, 0xed, 0x97, 0xf6, 0xff, 0xe1, 0x10, //0x00004520 .quad 1216534808942701673
	0xe6, 0x60, 0xbf, 0xd5, 0x12, 0x19, 0x23, 0xe3, //0x00004528 .quad -2079791034228842266
	0xc1, 0x63, 0xf4, 0x1e, 0xfa, 0x3f, 0x8d, 0xca, //0x00004530 .quad -3851351762838199359
	0x8f, 0x9c, 0x97, 0xc5, 0xab, 0xef, 0xf5, 0x8d, //0x00004538 .quad -8217398424034108273
	0xb2, 0x7c, 0xb1, 0xa6, 0xf8, 0x8f, 0x30, 0xbd, //0x00004540 .quad -4814189703547749198
	0xb3, 0x83, 0xfd, 0xb6, 0x96, 0x6b, 0x73, 0xb1, //0x00004548 .quad -5660062011615247437
	0xde, 0xdb, 0x5d, 0xd0, 0xf6, 0xb3, 0x7c, 0xac, //0x00004550 .quad -6017737129434686498
	0xa0, 0xe4, 0xbc, 0x64, 0x7c, 0x46, 0xd0, 0xdd, //0x00004558 .quad -2463391496091671392
	0x6b, 0xa9, 0x3a, 0x42, 0x7a, 0xf0, 0xcd, 0x6b, //0x00004560 .quad 7768129340171790699
	0xe4, 0x0e, 0xf6, 0xbe, 0x0d, 0x2c, 0xa2, 0x8a, //0x00004568 .quad -8457148712698376476
	0xc6, 0x53, 0xc9, 0xd2, 0x98, 0x6c, 0xc1, 0x86, //0x00004570 .quad -8736582398494813242
	0x9d, 0x92, 0xb3, 0x2e, 0x11, 0xb7, 0x4a, 0xad, //0x00004578 .quad -5959749872445582691
	0xb7, 0xa8, 0x7b, 0x07, 0xbf, 0xc7, 0x71, 0xe8, //0x00004580 .quad -1697355961263740745
	0x44, 0x77, 0x60, 0x7a, 0xd5, 0x64, 0x9d, 0xd8, //0x00004588 .quad -2838001322129590460
	0x72, 0x49, 0xad, 0x64, 0xd7, 0x1c, 0x47, 0x11, //0x00004590 .quad 1244995533423855986
	0x8b, 0x4a, 0x7c, 0x6c, 0x05, 0x5f, 0x62, 0x87, //0x00004598 .quad -8691279853972075893
	0xcf, 0x9b, 0xd8, 0x3d, 0x0d, 0xe4, 0x98, 0xd5, //0x000045a0 .quad -3055441601647567921
	0x2d, 0x5d, 0x9b, 0xc7, 0xc6, 0xf6, 0x3a, 0xa9, //0x000045a8 .quad -6252413799037706963
	0xc3, 0xc2, 0x4e, 0x8d, 0x10, 0x1d, 0xff, 0x4a, //0x000045b0 .quad 5404070034795315907
	0x79, 0x34, 0x82, 0x79, 0x78, 0xb4, 0x89, 0xd3, //0x000045b8 .quad -3203831230369745799
	0xba, 0x39, 0x51, 0x58, 0x2a, 0x72, 0xdf, 0xce, //0x000045c0 .quad -3539985255894009414
	0xcb, 0x60, 0xf1, 0x4b, 0xcb, 0x10, 0x36, 0x84, //0x000045c8 .quad -8919923546622172981
	0x28, 0x88, 0x65, 0xee, 0xb4, 0x4e, 0x97, 0xc2, //0x000045d0 .quad -4424981569867511768
	0xfe, 0xb8, 0xed, 0x1e, 0xfe, 0x94, 0x43, 0xa5, //0x000045d8 .quad -6538218414850328322
	0x32, 0xea, 0xfe, 0x29, 0x62, 0x22, 0x3d, 0x73, //0x000045e0 .quad 8303831092947774002
	0x3e, 0x27, 0xa9, 0xa6, 0x3d, 0x7a, 0x94, 0xce, //0x000045e8 .quad -3561087000135522498
	0x5f, 0x52, 0x3f, 0x5a, 0x7d, 0x35, 0x06, 0x08, //0x000045f0 .quad 578208414664970847
	0x87, 0xb8, 0x29, 0x88, 0x66, 0xcc, 0x1c, 0x81, //0x000045f8 .quad -9143208402725783417
	0xf7, 0x26, 0xcf, 0xb0, 0xdc, 0xc2, 0x07, 0xca, //0x00004600 .quad -3888925500096174345
	0xa8, 0x26, 0x34, 0x2a, 0x80, 0xff, 0x63, 0xa1, //0x00004608 .quad -6817324484979841368
	0xb5, 0xf0, 0x02, 0xdd, 0x93, 0xb3, 0x89, 0xfc, //0x00004610 .quad -249470856692830027
	0x52, 0x30, 0xc1, 0x34, 0x60, 0xff, 0xbc, 0xc9, //0x00004618 .quad -3909969587797413806
	0xe2, 0xac, 0x43, 0xd4, 0x78, 0x20, 0xac, 0xbb, //0x00004620 .quad -4923524589293425438
	0x67, 0x7c, 0xf1, 0x41, 0x38, 0x3f, 0x2c, 0xfc, //0x00004628 .quad -275775966319379353
	0x0d, 0x4c, 0xaa, 0x84, 0x4b, 0x94, 0x4b, 0xd5, //0x00004630 .quad -3077202868308390899
	0xc0, 0xed, 0x36, 0x29, 0x83, 0xa7, 0x9b, 0x9d, //0x00004638 .quad -7089889006590693952
	0x11, 0xdf, 0xd4, 0x65, 0x5e, 0x79, 0x9e, 0x0a, //0x00004640 .quad 765182433041899281
	0x31, 0xa9, 0x84, 0xf3, 0x63, 0x91, 0x02, 0xc5, //0x00004648 .quad -4250675239810979535
	0xd5, 0x16, 0x4a, 0xff, 0xb5, 0x17, 0x46, 0x4d, //0x00004650 .quad 5568164059729762005
	0x7d, 0xd3, 0x65, 0xf0, 0xbc, 0x35, 0x43, 0xf6, //0x00004658 .quad -701658031336336515
	0x45, 0x4e, 0x8e, 0xbf, 0xd1, 0xce, 0x4b, 0x50, //0x00004660 .quad 5785945546544795205
	0x2e, 0xa4, 0x3f, 0x16, 0x96, 0x01, 0xea, 0x99, //0x00004668 .quad -7356065297226292178
	0xd6, 0xe1, 0x71, 0x2f, 0x86, 0xc2, 0x5e, 0xe4, //0x00004670 .quad -1990940103673781802
	0x39, 0x8d, 0xcf, 0x9b, 0xfb, 0x81, 0x64, 0xc0, //0x00004678 .quad -4583395603105477319
	0x4c, 0x5a, 0x4e, 0xbb, 0x27, 0x73, 0x76, 0x5d, //0x00004680 .quad 6734696907262548556
	0x88, 0x70, 0xc3, 0x82, 0x7a, 0xa2, 0x7d, 0xf0, //0x00004688 .quad -1117558485454458744
	0x6f, 0xf8, 0x10, 0xd5, 0xf8, 0x07, 0x6a, 0x3a, //0x00004690 .quad 4209185567039092847
	0x55, 0x26, 0xba, 0x91, 0x8c, 0x85, 0x4e, 0x96, //0x00004698 .quad -7616003081050118571
	0x8b, 0x36, 0x55, 0x0a, 0xf7, 0x89, 0x04, 0x89, //0x000046a0 .quad -8573576096483297653
	0xea, 0xaf, 0x28, 0xb6, 0xef, 0x26, 0xe2, 0xbb, //0x000046a8 .quad -4908317832885260310
	0x2e, 0x84, 0xea, 0xcc, 0x74, 0xac, 0x45, 0x2b, //0x000046b0 .quad 3118087934678041646
	0xe5, 0xdb, 0xb2, 0xa3, 0xab, 0xb0, 0xda, 0xea, //0x000046b8 .quad -1523711272679187483
	0x9d, 0x92, 0x12, 0x00, 0xc9, 0x8b, 0x0b, 0x3b, //0x000046c0 .quad 4254647968387469981
	0x6f, 0xc9, 0x4f, 0x46, 0x6b, 0xae, 0xc8, 0x92, //0x000046c8 .quad -7869848573065574033
	0x44, 0x37, 0x17, 0x40, 0xbb, 0x6e, 0xce, 0x09, //0x000046d0 .quad 706623942056949572
	0xcb, 0xbb, 0xe3, 0x17, 0x06, 0xda, 0x7a, 0xb7, //0x000046d8 .quad -5225624697904579637
	0x15, 0x05, 0x1d, 0x10, 0x6a, 0x0a, 0x42, 0xcc, //0x000046e0 .quad -3728406090856200939
	0xbd, 0xaa, 0xdc, 0x9d, 0x87, 0x90, 0x59, 0xe5, //0x000046e8 .quad -1920344853953336643
	0x2d, 0x23, 0x12, 0x4a, 0x82, 0x46, 0xa9, 0x9f, //0x000046f0 .quad -6941939825212513491
	0xb6, 0xea, 0xa9, 0xc2, 0x54, 0xfa, 0x57, 0x8f, //0x000046f8 .quad -8117744561361917258
	0xf9, 0xab, 0x96, 0xdc, 0x22, 0x98, 0x93, 0x47, //0x00004700 .quad 5157633273766521849
	0x64, 0x65, 0x54, 0xf3, 0xe9, 0xf8, 0x2d, 0xb3, //0x00004708 .quad -5535494683275008668
	0xf7, 0x56, 0xbc, 0x93, 0x2b, 0x7e, 0x78, 0x59, //0x00004710 .quad 6447041592208152311
	0xbd, 0x7e, 0x29, 0x70, 0x24, 0x77, 0xf9, 0xdf, //0x00004718 .quad -2307682335666372931
	0x5a, 0xb6, 0x55, 0x3c, 0xdb, 0x4e, 0xeb, 0x57, //0x00004720 .quad 6335244004343789146
	0x36, 0xef, 0x19, 0xc6, 0x76, 0xea, 0xfb, 0x8b, //0x00004728 .quad -8359830487432564938
	0xf1, 0x23, 0x6b, 0x0b, 0x92, 0x22, 0xe6, 0xed, //0x00004730 .quad -1304317031425039375
	0x03, 0x6b, 0xa0, 0x77, 0x14, 0xe5, 0xfa, 0xae, //0x00004738 .quad -5838102090863318269
	0xed, 0xec, 0x45, 0x8e, 0x36, 0xab, 0x5f, 0xe9, //0x00004740 .quad -1630396289281299219
	0xc4, 0x85, 0x88, 0x95, 0x59, 0x9e, 0xb9, 0xda, //0x00004748 .quad -2685941595151759932
	0x14, 0xb4, 0xeb, 0x18, 0x02, 0xcb, 0xdb, 0x11, //0x00004750 .quad 1286845328412881940
	0x9b, 0x53, 0x75, 0xfd, 0xf7, 0x02, 0xb4, 0x88, //0x00004758 .quad -8596242524610931813
	0x19, 0xa1, 0x26, 0x9f, 0xc2, 0xbd, 0x52, 0xd6, //0x00004760 .quad -3003129357911285479
	0x81, 0xa8, 0xd2, 0xfc, 0xb5, 0x03, 0xe1, 0xaa, //0x00004768 .quad -6133617137336276863
	0x5f, 0x49, 0xf0, 0x46, 0x33, 0x6d, 0xe7, 0x4b, //0x00004770 .quad 5469460339465668959
	0xa2, 0x52, 0x07, 0x7c, 0xa3, 0x44, 0x99, 0xd5, //0x00004778 .quad -3055335403242958174
	0xdb, 0x2d, 0x56, 0x0c, 0x40, 0xa4, 0x70, 0x6f, //0x00004780 .quad 8030098730593431003
	0xa5, 0x93, 0x84, 0x2d, 0xe6, 0xca, 0x7f, 0x85, //0x00004788 .quad -8827113654667930715
	0x52, 0xb9, 0x6b, 0x0f, 0x50, 0xcd, 0x4c, 0xcb, //0x00004790 .quad -3797434642040374958
	0x8e, 0xb8, 0xe5, 0xb8, 0x9f, 0xbd, 0xdf, 0xa6, //0x00004798 .quad -6422206049907525490
	0xa7, 0xa7, 0x46, 0x13, 0xa4, 0x00, 0x20, 0x7e, //0x000047a0 .quad 9088264752731695015
	0xb2, 0x26, 0x1f, 0xa7, 0x07, 0xad, 0x97, 0xd0, //0x000047a8 .quad -3416071543957018958
	0xc8, 0x28, 0x0c, 0x8c, 0x66, 0x00, 0xd4, 0x8e, //0x000047b0 .quad -8154892584824854328
	0x2f, 0x78, 0x73, 0xc8, 0x24, 0xcc, 0x5e, 0x82, //0x000047b8 .quad -9052573742614218705
	0xfa, 0x32, 0x0f, 0x2f, 0x80, 0x00, 0x89, 0x72, //0x000047c0 .quad 8253128342678483706
	0x3b, 0x56, 0x90, 0xfa, 0x2d, 0x7f, 0xf6, 0xa2, //0x000047c8 .quad -6704031159840385477
	0xb9, 0xff, 0xd2, 0x3a, 0xa0, 0x40, 0x2b, 0x4f, //0x000047d0 .quad 5704724409920716729
	0xca, 0x6b, 0x34, 0x79, 0xf9, 0x1e, 0xb4, 0xcb, //0x000047d8 .quad -3768352931373093942
	0xa8, 0xbf, 0x87, 0x49, 0xc8, 0x10, 0xf6, 0xe2, //0x000047e0 .quad -2092466524453879896
	0xbc, 0x86, 0x81, 0xd7, 0xb7, 0x26, 0xa1, 0xfe, //0x000047e8 .quad -98755145788979524
	0xc9, 0xd7, 0xf4, 0x2d, 0x7d, 0xca, 0xd9, 0x0d, //0x000047f0 .quad 998051431430019017
	0x36, 0xf4, 0xb0, 0xe6, 0x32, 0xb8, 0x24, 0x9f, //0x000047f8 .quad -6979250993759194058
	0xbb, 0x0d, 0x72, 0x79, 0x1c, 0x3d, 0x50, 0x91, //0x00004800 .quad -7975807747567252037
	0x43, 0x31, 0x5d, 0xa0, 0x3f, 0xe6, 0xed, 0xc6, //0x00004808 .quad -4112377723771604669
	0x2a, 0x91, 0xce, 0x97, 0x63, 0x4c, 0xa4, 0x75, //0x00004810 .quad 8476984389250486570
	0x94, 0x7d, 0x74, 0x88, 0xcf, 0x5f, 0xa9, 0xf8, //0x00004818 .quad -528786136287117932
	0xba, 0x1a, 0xe1, 0x3e, 0xbe, 0xaf, 0x86, 0xc9, //0x00004820 .quad -3925256793573221702
	0x7c, 0xce, 0x48, 0xb5, 0xe1, 0xdb, 0x69, 0x9b, //0x00004828 .quad -7248020362820530564
	0x68, 0x61, 0x99, 0xce, 0xad, 0x5b, 0xe8, 0xfb, //0x00004830 .quad -294884973539139224
	0x1b, 0x02, 0x9b, 0x22, 0xda, 0x52, 0x44, 0xc2, //0x00004838 .quad -4448339435098275301
	0xc3, 0xb9, 0x3f, 0x42, 0x99, 0x72, 0xe2, 0xfa, //0x00004840 .quad -368606216923924029
	0xa2, 0xc2, 0x41, 0xab, 0x90, 0x67, 0xd5, 0xf2, //0x00004848 .quad -948738275445456222
	0x1a, 0xd4, 0x67, 0xc9, 0x9f, 0x87, 0xcd, 0xdc, //0x00004850 .quad -2536221894791146470
	0xa5, 0x19, 0x09, 0x6b, 0xba, 0x60, 0xc5, 0x97, //0x00004858 .quad -7510490449794491995
	0x20, 0xc9, 0xc1, 0xbb, 0x87, 0xe9, 0x00, 0x54, //0x00004860 .quad 6053094668365842720
	0x0f, 0x60, 0xcb, 0x05, 0xe9, 0xb8, 0xb6, 0xbd, //0x00004868 .quad -4776427043815727089
	0x68, 0x3b, 0xb2, 0xaa, 0xe9, 0x23, 0x01, 0x29, //0x00004870 .quad 2954682317029915496
	0x13, 0x38, 0x3e, 0x47, 0x23, 0x67, 0x24, 0xed, //0x00004878 .quad -1358847786342270957
	0x21, 0x65, 0xaf, 0x0a, 0x72, 0xb6, 0xa0, 0xf9, //0x00004880 .quad -459166561069996767
	0x0b, 0xe3, 0x86, 0x0c, 0x76, 0xc0, 0x36, 0x94, //0x00004888 .quad -7766808894105001205
	0x69, 0x3e, 0x5b, 0x8d, 0x0e, 0xe4, 0x08, 0xf8, //0x00004890 .quad -573958201337495959
	0xce, 0x9b, 0xa8, 0x8f, 0x93, 0x70, 0x44, 0xb9, //0x00004898 .quad -5096825099203863602
	0x04, 0x0e, 0xb2, 0x30, 0x12, 0x1d, 0x0b, 0xb6, //0x000048a0 .quad -5329133770099257852
	0xc2, 0xc2, 0x92, 0x73, 0xb8, 0x8c, 0x95, 0xe7, //0x000048a8 .quad -1759345355577441598
	0xc2, 0x48, 0x6f, 0x5e, 0x2b, 0xf2, 0xc6, 0xb1, //0x000048b0 .quad -5636551615525730110
	0xb9, 0xb9, 0x3b, 0x48, 0xf3, 0x77, 0xbd, 0x90, //0x000048b8 .quad -8017119874876982855
	0xf3, 0x1a, 0x0b, 0x36, 0xb6, 0xae, 0x38, 0x1e, //0x000048c0 .quad 2177682517447613171
	0x28, 0xa8, 0x4a, 0x1a, 0xf0, 0xd5, 0xec, 0xb4, //0x000048c8 .quad -5409713825168840664
	0xb0, 0xe1, 0x8d, 0xc3, 0x63, 0xda, 0xc6, 0x25, //0x000048d0 .quad 2722103146809516464
	0x32, 0x52, 0xdd, 0x20, 0x6c, 0x0b, 0x28, 0xe2, //0x000048d8 .quad -2150456263033662926
	0x0e, 0xad, 0x38, 0x5a, 0x7e, 0x48, 0x9c, 0x57, //0x000048e0 .quad 6313000485183335694
	0x5f, 0x53, 0x8a, 0x94, 0x23, 0x07, 0x59, 0x8d, //0x000048e8 .quad -8261564192037121185
	0x51, 0xd8, 0xc6, 0xf0, 0x9d, 0x5a, 0x83, 0x2d, //0x000048f0 .quad 3279564588051781713
	0x37, 0xe8, 0xac, 0x79, 0xec, 0x48, 0xaf, 0xb0, //0x000048f8 .quad -5715269221619013577
	0x65, 0x8e, 0xf8, 0x6c, 0x45, 0x31, 0xe4, 0xf8, //0x00004900 .quad -512230283362660763
	0x44, 0x22, 0x18, 0x98, 0x27, 0x1b, 0xdb, 0xdc, //0x00004908 .quad -2532400508596379068
	0xff, 0x58, 0x1b, 0x64, 0xcb, 0x9e, 0x8e, 0x1b, //0x00004910 .quad 1985699082112030975
	0x6b, 0x15, 0x0f, 0xbf, 0xf8, 0xf0, 0x08, 0x8a, //0x00004918 .quad -8500279345513818773
	0x3f, 0x2f, 0x22, 0x3d, 0x7e, 0x46, 0x72, 0xe2, //0x00004920 .quad -2129562165787349185
	0xc5, 0xda, 0xd2, 0xee, 0x36, 0x2d, 0x8b, 0xac, //0x00004928 .quad -6013663163464885563
	0x0f, 0xbb, 0x6a, 0xcc, 0x1d, 0xd8, 0x0e, 0x5b, //0x00004930 .quad 6561419329620589327
	0x77, 0x91, 0x87, 0xaa, 0x84, 0xf8, 0xad, 0xd7, //0x00004938 .quad -2905392935903719049
	0xe9, 0xb4, 0xc2, 0x9f, 0x12, 0x47, 0xe9, 0x98, //0x00004940 .quad -7428327965055601431
	0xea, 0xba, 0x94, 0xea, 0x52, 0xbb, 0xcc, 0x86, //0x00004948 .quad -8733399612580906262
	0x24, 0x62, 0xb3, 0x47, 0xd7, 0x98, 0x23, 0x3f, //0x00004950 .quad 4549648098962661924
	0xa5, 0xe9, 0x39, 0xa5, 0x27, 0xea, 0x7f, 0xa8, //0x00004958 .quad -6305063497298744923
	0xad, 0x3a, 0xa0, 0x19, 0x0d, 0x7f, 0xec, 0x8e, //0x00004960 .quad -8147997931578836307
	0x0e, 0x64, 0x88, 0x8e, 0xb1, 0xe4, 0x9f, 0xd2, //0x00004968 .quad -3269643353196043250
	0xac, 0x24, 0x04, 0x30, 0x68, 0xcf, 0x53, 0x19, //0x00004970 .quad 1825030320404309164
	0x89, 0x3e, 0x15, 0xf9, 0xee, 0xee, 0xa3, 0x83, //0x00004978 .quad -8961056123388608887
	0xd7, 0x2d, 0x05, 0x3c, 0x42, 0xc3, 0xa8, 0x5f, //0x00004980 .quad 6892973918932774359
	0x2b, 0x8e, 0x5a, 0xb7, 0xaa, 0xea, 0x8c, 0xa4, //0x00004988 .quad -6589634135808373205
	0x4d, 0x79, 0x06, 0xcb, 0x12, 0xf4, 0x92, 0x37, //0x00004990 .quad 4004531380238580045
	0xb6, 0x31, 0x31, 0x65, 0x55, 0x25, 0xb0, 0xcd, //0x00004998 .quad -3625356651333078602
	0xd0, 0x0b, 0xe4, 0xbe, 0x8b, 0xd8, 0xbb, 0xe2, //0x000049a0 .quad -2108853905778275376
	0x11, 0xbf, 0x3e, 0x5f, 0x55, 0x17, 0x8e, 0x80, //0x000049a8 .quad -9183376934724255983
	0xc4, 0x0e, 0x9d, 0xae, 0xae, 0xce, 0x6a, 0x5b, //0x000049b0 .quad 6587304654631931588
	0xd6, 0x6e, 0x0e, 0xb7, 0x2a, 0x9d, 0xb1, 0xa0, //0x000049b8 .quad -6867535149977932074
	0x75, 0x52, 0x44, 0x5a, 0x5a, 0x82, 0x45, 0xf2, //0x000049c0 .quad -989241218564861323
	0x8b, 0x0a, 0xd2, 0x64, 0x75, 0x04, 0xde, 0xc8, //0x000049c8 .quad -3972732919045027189
	0x12, 0x67, 0xd5, 0xf0, 0xf0, 0xe2, 0xd6, 0xee, //0x000049d0 .quad -1236551523206076654
	0x2e, 0x8d, 0x06, 0xbe, 0x92, 0x85, 0x15, 0xfb, //0x000049d8 .quad -354230130378896082
	0x6b, 0x60, 0x85, 0x96, 0xd6, 0x4d, 0x46, 0x55, //0x000049e0 .quad 6144684325637283947
	0x3d, 0x18, 0xc4, 0xb6, 0x7b, 0x73, 0xed, 0x9c, //0x000049e8 .quad -7138922859127891907
	0x86, 0xb8, 0x26, 0x3c, 0x4c, 0xe1, 0x97, 0xaa, //0x000049f0 .quad -6154202648235558778
	0x4c, 0x1e, 0x75, 0xa4, 0x5a, 0xd0, 0x28, 0xc4, //0x000049f8 .quad -4311967555482476980
	0xa8, 0x66, 0x30, 0x4b, 0x9f, 0xd9, 0x3d, 0xd5, //0x00004a00 .quad -3081067291867060568
	0xdf, 0x65, 0x92, 0x4d, 0x71, 0x04, 0x33, 0xf5, //0x00004a08 .quad -778273425925708321
	0x29, 0x40, 0xfe, 0x8e, 0x03, 0xa8, 0x46, 0xe5, //0x00004a10 .quad -1925667057416912855
	0xab, 0x7f, 0x7b, 0xd0, 0xc6, 0xe2, 0x3f, 0x99, //0x00004a18 .quad -7403949918844649557
	0x33, 0xd0, 0xbd, 0x72, 0x04, 0x52, 0x98, 0xde, //0x00004a20 .quad -2407083821771141069
	0x96, 0x5f, 0x9a, 0x84, 0x78, 0xdb, 0x8f, 0xbf, //0x00004a28 .quad -4643251380128424042
	0x40, 0x44, 0x6d, 0x8f, 0x85, 0x66, 0x3e, 0x96, //0x00004a30 .quad -7620540795641314240
	0x7c, 0xf7, 0xc0, 0xa5, 0x56, 0xd2, 0x73, 0xef, //0x00004a38 .quad -1192378206733142148
	0xa8, 0x4a, 0xa4, 0x79, 0x13, 0x00, 0xe7, 0xdd, //0x00004a40 .quad -2456994988062127448
	0xad, 0x9a, 0x98, 0x27, 0x76, 0x63, 0xa8, 0x95, //0x00004a48 .quad -7662765406849295699
	0x52, 0x5d, 0x0d, 0x58, 0x18, 0xc0, 0x60, 0x55, //0x00004a50 .quad 6152128301777116498
	0x59, 0xc1, 0x7e, 0xb1, 0x53, 0x7c, 0x12, 0xbb, //0x00004a58 .quad -4966770740134231719
	0xa6, 0xb4, 0x10, 0x6e, 0x1e, 0xf0, 0xb8, 0xaa, //0x00004a60 .quad -6144897678060768090
	0xaf, 0x71, 0xde, 0x9d, 0x68, 0x1b, 0xd7, 0xe9, //0x00004a68 .quad -1596777406740401745
	0xe8, 0x70, 0xca, 0x04, 0x13, 0x96, 0xb3, 0xca, //0x00004a70 .quad -3840561048787980056
	0x0d, 0x07, 0xab, 0x62, 0x21, 0x71, 0x26, 0x92, //0x00004a78 .quad -7915514906853832947
	0x22, 0x0d, 0xfd, 0xc5, 0x97, 0x7b, 0x60, 0x3d, //0x00004a80 .quad 4422670725869800738
	0xd1, 0xc8, 0x55, 0xbb, 0x69, 0x0d, 0xb0, 0xb6, //0x00004a88 .quad -5282707615139903279
	0x6a, 0x50, 0x7c, 0xb7, 0x7d, 0x9a, 0xb8, 0x8c, //0x00004a90 .quad -8306719647944912790
	0x05, 0x3b, 0x2b, 0x2a, 0xc4, 0x10, 0x5c, 0xe4, //0x00004a98 .quad -1991698500497491195
	0x42, 0xb2, 0xad, 0x92, 0x8e, 0x60, 0xf3, 0x77, //0x00004aa0 .quad 8643358275316593218
	0xe3, 0x04, 0x5b, 0x9a, 0x7a, 0x8a, 0xb9, 0x8e, //0x00004aa8 .quad -8162340590452013853
	0xd3, 0x1e, 0x59, 0x37, 0xb2, 0x38, 0xf0, 0x55, //0x00004ab0 .quad 6192511825718353619
	0x1c, 0xc6, 0xf1, 0x40, 0x19, 0xed, 0x67, 0xb2, //0x00004ab8 .quad -5591239719637629412
	0x88, 0x66, 0x2f, 0xc5, 0xde, 0x46, 0x6c, 0x6b, //0x00004ac0 .quad 7740639782147942024
	0xa3, 0x37, 0x2e, 0x91, 0x5f, 0xe8, 0x01, 0xdf, //0x00004ac8 .quad -2377363631119648861
	0x15, 0xa0, 0x3d, 0x3b, 0x4b, 0xac, 0x23, 0x23, //0x00004ad0 .quad 2532056854628769813
	0xc6, 0xe2, 0xbc, 0xba, 0x3b, 0x31, 0x61, 0x8b, //0x00004ad8 .quad -8403381297090862394
	0x1a, 0x08, 0x0d, 0x0a, 0x5e, 0x97, 0xec, 0xab, //0x00004ae0 .quad -6058300968568813542
	0x77, 0x1b, 0x6c, 0xa9, 0x8a, 0x7d, 0x39, 0xae, //0x00004ae8 .quad -5892540602936190089
	0x21, 0x4a, 0x90, 0x8c, 0x35, 0xbd, 0xe7, 0x96, //0x00004af0 .quad -7572876210711016927
	0x55, 0x22, 0xc7, 0x53, 0xed, 0xdc, 0xc7, 0xd9, //0x00004af8 .quad -2753989735242849707
	0x54, 0x2e, 0xda, 0x77, 0x41, 0xd6, 0x50, 0x7e, //0x00004b00 .quad 9102010423587778132
	0x75, 0x75, 0x5c, 0x54, 0x14, 0xea, 0x1c, 0x88, //0x00004b08 .quad -8638772612167862923
	0xe9, 0xb9, 0xd0, 0xd5, 0xd1, 0x0b, 0xe5, 0xdd, //0x00004b10 .quad -2457545025797441047
	0xd2, 0x92, 0x73, 0x69, 0x99, 0x24, 0x24, 0xaa, //0x00004b18 .quad -6186779746782440750
	0x64, 0xe8, 0x44, 0x4b, 0xc6, 0x4e, 0x5e, 0x95, //0x00004b20 .quad -7683617300674189212
	0x87, 0x77, 0xd0, 0xc3, 0xbf, 0x2d, 0xad, 0xd4, //0x00004b28 .quad -3121788665050663033
	0x3e, 0x11, 0x0b, 0xef, 0x3b, 0xf1, 0x5a, 0xbd, //0x00004b30 .quad -4802260812921368258
	0xb4, 0x4a, 0x62, 0xda, 0x97, 0x3c, 0xec, 0x84, //0x00004b38 .quad -8868646943297746252
	0x8e, 0xd5, 0xcd, 0xea, 0x8a, 0xad, 0xb1, 0xec, //0x00004b40 .quad -1391139997724322418
	0x61, 0xdd, 0xfa, 0xd0, 0xbd, 0x4b, 0x27, 0xa6, //0x00004b48 .quad -6474122660694794911
	0xf2, 0x4a, 0x81, 0xa5, 0xed, 0x18, 0xde, 0x67, //0x00004b50 .quad 7484447039699372786
	0xba, 0x94, 0x39, 0x45, 0xad, 0x1e, 0xb1, 0xcf, //0x00004b58 .quad -3480967307441105734
	0xd7, 0xce, 0x70, 0x87, 0x94, 0xcf, 0xea, 0x80, //0x00004b60 .quad -9157278655470055721
	0xf4, 0xfc, 0x43, 0x4b, 0x2c, 0xb3, 0xce, 0x81, //0x00004b68 .quad -9093133594791772940
	0x8d, 0x02, 0x4d, 0xa9, 0x79, 0x83, 0x25, 0xa1, //0x00004b70 .quad -6834912300910181747
	0x31, 0xfc, 0x14, 0x5e, 0xf7, 0x5f, 0x42, 0xa2, //0x00004b78 .quad -6754730975062328271
	0x30, 0x43, 0xa0, 0x13, 0x58, 0xe4, 0x6e, 0x09, //0x00004b80 .quad 679731660717048624
	0x3e, 0x3b, 0x9a, 0x35, 0xf5, 0xf7, 0xd2, 0xca, //0x00004b88 .quad -3831727700400522434
	0xfc, 0x53, 0x88, 0x18, 0x6e, 0x9d, 0xca, 0x8b, //0x00004b90 .quad -8373707460958465028
	0x0d, 0xca, 0x00, 0x83, 0xf2, 0xb5, 0x87, 0xfd, //0x00004b98 .quad -177973607073265139
	0x7d, 0x34, 0x55, 0xcf, 0x64, 0xa2, 0x5e, 0x77, //0x00004ba0 .quad 8601490892183123069
	0x48, 0x7e, 0xe0, 0x91, 0xb7, 0xd1, 0x74, 0x9e, //0x00004ba8 .quad -7028762532061872568
	0x9d, 0x81, 0x2a, 0x03, 0xfe, 0x4a, 0x36, 0x95, //0x00004bb0 .quad -7694880458480647779
	0xda, 0x9d, 0x58, 0x76, 0x25, 0x06, 0x12, 0xc6, //0x00004bb8 .quad -4174267146649952806
	0x04, 0x22, 0xf5, 0x83, 0xbd, 0xdd, 0x83, 0x3a, //0x00004bc0 .quad 4216457482181353988
	0x51, 0xc5, 0xee, 0xd3, 0xae, 0x87, 0x96, 0xf7, //0x00004bc8 .quad -606147914885053103
	0x42, 0x35, 0x79, 0x72, 0x96, 0x6a, 0x92, 0xc4, //0x00004bd0 .quad -4282243101277735614
	0x52, 0x3b, 0x75, 0x44, 0xcd, 0x14, 0xbe, 0x9a, //0x00004bd8 .quad -7296371474444240046
	0x93, 0x82, 0x17, 0x0f, 0x3c, 0x05, 0xb7, 0x75, //0x00004be0 .quad 8482254178684994195
	0x27, 0x8a, 0x92, 0x95, 0x00, 0x9a, 0x6d, 0xc1, //0x00004be8 .quad -4508778324627912153
	0x38, 0x63, 0xdd, 0x12, 0x8b, 0xc6, 0x24, 0x53, //0x00004bf0 .quad 5991131704928854840
	0xb1, 0x2c, 0xf7, 0xba, 0x80, 0x00, 0xc9, 0xf1, //0x00004bf8 .quad -1024286887357502287
	0x03, 0x5e, 0xca, 0xeb, 0x16, 0xfc, 0xf6, 0xd3, //0x00004c00 .quad -3173071712060547581
	0xee, 0x7b, 0xda, 0x74, 0x50, 0xa0, 0x1d, 0x97, //0x00004c08 .quad -7557708332239520786
	0x84, 0xf5, 0xbc, 0xa6, 0x1c, 0xbb, 0xf4, 0x88, //0x00004c10 .quad -8578025658503072380
	0xea, 0x1a, 0x11, 0x92, 0x64, 0x08, 0xe5, 0xbc, //0x00004c18 .quad -4835449396872013078
	0xe5, 0x32, 0x6c, 0xd0, 0xe3, 0xe9, 0x31, 0x2b, //0x00004c20 .quad 3112525982153323237
	0xa5, 0x61, 0x95, 0xb6, 0x7d, 0x4a, 0x1e, 0xec, //0x00004c28 .quad -1432625727662628443
	0xcf, 0x9f, 0x43, 0x62, 0x2e, 0x32, 0xff, 0x3a, //0x00004c30 .quad 4251171748059520975
	0x07, 0x5d, 0x1d, 0x92, 0x8e, 0xee, 0x92, 0x93, //0x00004c38 .quad -7812920107430224633
	0xc2, 0x87, 0xd4, 0xfa, 0xb9, 0xfe, 0xbe, 0x09, //0x00004c40 .quad 702278666647013314
	0x49, 0xb4, 0xa4, 0x36, 0x32, 0xaa, 0x77, 0xb8, //0x00004c48 .quad -5154464115860392887
	0xb3, 0xa9, 0x89, 0x79, 0x68, 0xbe, 0x2e, 0x4c, //0x00004c50 .quad 5489534351736154547
	0x5b, 0xe1, 0x4d, 0xc4, 0xbe, 0x94, 0x95, 0xe6, //0x00004c58 .quad -1831394126398103205
	0x10, 0x0a, 0xf6, 0x4b, 0x01, 0x37, 0x9d, 0x0f, //0x00004c60 .quad 1125115960621402640
	0xd9, 0xac, 0xb0, 0x3a, 0xf7, 0x7c, 0x1d, 0x90, //0x00004c68 .quad -8062150356639896359
	0x94, 0x8c, 0xf3, 0x9e, 0xc1, 0x84, 0x84, 0x53, //0x00004c70 .quad 6018080969204141204
	0x0f, 0xd8, 0x5c, 0x09, 0x35, 0xdc, 0x24, 0xb4, //0x00004c78 .quad -5466001927372482545
	0xb9, 0x6f, 0xb0, 0x06, 0xf2, 0xa5, 0x65, 0x28, //0x00004c80 .quad 2910915193077788601
	0x13, 0x0e, 0xb4, 0x4b, 0x42, 0x13, 0x2e, 0xe1, //0x00004c88 .quad -2220816390788215277
	0xd3, 0x45, 0x2e, 0x44, 0xb7, 0x87, 0x3f, 0xf9, //0x00004c90 .quad -486521013540076077
	0xcb, 0x88, 0x50, 0x6f, 0x09, 0xcc, 0xbc, 0x8c, //0x00004c98 .quad -8305539271883716405
	0x48, 0xd7, 0x39, 0x15, 0xa5, 0x69, 0x8f, 0xf7, //0x00004ca0 .quad -608151266925095096
	0xfe, 0xaa, 0x24, 0xcb, 0x0b, 0xff, 0xeb, 0xaf, //0x00004ca8 .quad -5770238071427257602
	0x1b, 0x4d, 0x88, 0x5a, 0x0e, 0x44, 0x73, 0xb5, //0x00004cb0 .quad -5371875102083756773
	0xbe, 0xd5, 0xed, 0xbd, 0xce, 0xfe, 0xe6, 0xdb, //0x00004cb8 .quad -2601111570856684098
	0x30, 0x30, 0x95, 0xf8, 0x88, 0x0a, 0x68, 0x31, //0x00004cc0 .quad 3560107088838733872
	0x97, 0xa5, 0xb4, 0x36, 0x41, 0x5f, 0x70, 0x89, //0x00004cc8 .quad -8543223759426509417
	0x3d, 0x7c, 0xba, 0x36, 0x2b, 0x0d, 0xc2, 0xfd, //0x00004cd0 .quad -161552157378970563
	0xfc, 0xce, 0x61, 0x84, 0x11, 0x77, 0xcc, 0xab, //0x00004cd8 .quad -6067343680855748868
	0x4c, 0x1b, 0x69, 0x04, 0x76, 0x90, 0x32, 0x3d, //0x00004ce0 .quad 4409745821703674700
	0xbc, 0x42, 0x7a, 0xe5, 0xd5, 0x94, 0xbf, 0xd6, //0x00004ce8 .quad -2972493582642298180
	0x0f, 0xb1, 0xc1, 0xc2, 0x49, 0x9a, 0x3f, 0xa6, //0x00004cf0 .quad -6467280898289979121
	0xb5, 0x69, 0x6c, 0xaf, 0x05, 0xbd, 0x37, 0x86, //0x00004cf8 .quad -8775337516792518219
	0x53, 0x1d, 0x72, 0x33, 0xdc, 0x80, 0xcf, 0x0f, //0x00004d00 .quad 1139270913992301907
	0x23, 0x84, 0x47, 0x1b, 0x47, 0xac, 0xc5, 0xa7, //0x00004d08 .quad -6357485877563259869
	0xa8, 0xa4, 0x4e, 0x40, 0x13, 0x61, 0xc3, 0xd3, //0x00004d10 .quad -3187597375937010520
	0x2b, 0x65, 0x19, 0xe2, 0x58, 0x17, 0xb7, 0xd1, //0x00004d18 .quad -3335171328526686933
	0xe9, 0x26, 0x31, 0x08, 0xac, 0x1c, 0x5a, 0x64, //0x00004d20 .quad 7231123676894144233
	0x3b, 0xdf, 0x4f, 0x8d, 0x97, 0x6e, 0x12, 0x83, //0x00004d28 .quad -9002011107970261189
	0xa3, 0x70, 0x3d, 0x0a, 0xd7, 0xa3, 0x70, 0x3d, //0x00004d30 .quad 4427218577690292387
	0x0a, 0xd7, 0xa3, 0x70, 0x3d, 0x0a, 0xd7, 0xa3, //0x00004d38 .quad -6640827866535438582
	0xcc, 0xcc, 0xcc, 0xcc, 0xcc, 0xcc, 0xcc, 0xcc, 0xcc, 0xcc, 0xcc, 0xcc, 0xcc, 0xcc, 0xcc, 0xcc, //0x00004d40 QUAD $0xcccccccccccccccc; QUAD $0xcccccccccccccccc  // .space 16, '\xcc\xcc\xcc\xcc\xcc\xcc\xcc\xcc\xcc\xcc\xcc\xcc\xcc\xcc\xcc\xcc'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00004d50 .quad 0
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x80, //0x00004d58 .quad -9223372036854775808
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00004d60 .quad 0
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xa0, //0x00004d68 .quad -6917529027641081856
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00004d70 .quad 0
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xc8, //0x00004d78 .quad -4035225266123964416
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00004d80 .quad 0
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xfa, //0x00004d88 .quad -432345564227567616
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00004d90 .quad 0
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x40, 0x9c, //0x00004d98 .quad -7187745005283311616
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00004da0 .quad 0
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x50, 0xc3, //0x00004da8 .quad -4372995238176751616
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00004db0 .quad 0
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x24, 0xf4, //0x00004db8 .quad -854558029293551616
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00004dc0 .quad 0
	0x00, 0x00, 0x00, 0x00, 0x00, 0x80, 0x96, 0x98, //0x00004dc8 .quad -7451627795949551616
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00004dd0 .quad 0
	0x00, 0x00, 0x00, 0x00, 0x00, 0x20, 0xbc, 0xbe, //0x00004dd8 .quad -4702848726509551616
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00004de0 .quad 0
	0x00, 0x00, 0x00, 0x00, 0x00, 0x28, 0x6b, 0xee, //0x00004de8 .quad -1266874889709551616
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00004df0 .quad 0
	0x00, 0x00, 0x00, 0x00, 0x00, 0xf9, 0x02, 0x95, //0x00004df8 .quad -7709325833709551616
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00004e00 .quad 0
	0x00, 0x00, 0x00, 0x00, 0x40, 0xb7, 0x43, 0xba, //0x00004e08 .quad -5024971273709551616
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00004e10 .quad 0
	0x00, 0x00, 0x00, 0x00, 0x10, 0xa5, 0xd4, 0xe8, //0x00004e18 .quad -1669528073709551616
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00004e20 .quad 0
	0x00, 0x00, 0x00, 0x00, 0x2a, 0xe7, 0x84, 0x91, //0x00004e28 .quad -7960984073709551616
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00004e30 .quad 0
	0x00, 0x00, 0x00, 0x80, 0xf4, 0x20, 0xe6, 0xb5, //0x00004e38 .quad -5339544073709551616
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00004e40 .quad 0
	0x00, 0x00, 0x00, 0xa0, 0x31, 0xa9, 0x5f, 0xe3, //0x00004e48 .quad -2062744073709551616
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00004e50 .quad 0
	0x00, 0x00, 0x00, 0x04, 0xbf, 0xc9, 0x1b, 0x8e, //0x00004e58 .quad -8206744073709551616
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00004e60 .quad 0
	0x00, 0x00, 0x00, 0xc5, 0x2e, 0xbc, 0xa2, 0xb1, //0x00004e68 .quad -5646744073709551616
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00004e70 .quad 0
	0x00, 0x00, 0x40, 0x76, 0x3a, 0x6b, 0x0b, 0xde, //0x00004e78 .quad -2446744073709551616
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00004e80 .quad 0
	0x00, 0x00, 0xe8, 0x89, 0x04, 0x23, 0xc7, 0x8a, //0x00004e88 .quad -8446744073709551616
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00004e90 .quad 0
	0x00, 0x00, 0x62, 0xac, 0xc5, 0xeb, 0x78, 0xad, //0x00004e98 .quad -5946744073709551616
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00004ea0 .quad 0
	0x00, 0x80, 0x7a, 0x17, 0xb7, 0x26, 0xd7, 0xd8, //0x00004ea8 .quad -2821744073709551616
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00004eb0 .quad 0
	0x00, 0x90, 0xac, 0x6e, 0x32, 0x78, 0x86, 0x87, //0x00004eb8 .quad -8681119073709551616
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00004ec0 .quad 0
	0x00, 0xb4, 0x57, 0x0a, 0x3f, 0x16, 0x68, 0xa9, //0x00004ec8 .quad -6239712823709551616
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00004ed0 .quad 0
	0x00, 0xa1, 0xed, 0xcc, 0xce, 0x1b, 0xc2, 0xd3, //0x00004ed8 .quad -3187955011209551616
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00004ee0 .quad 0
	0xa0, 0x84, 0x14, 0x40, 0x61, 0x51, 0x59, 0x84, //0x00004ee8 .quad -8910000909647051616
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00004ef0 .quad 0
	0xc8, 0xa5, 0x19, 0x90, 0xb9, 0xa5, 0x6f, 0xa5, //0x00004ef8 .quad -6525815118631426616
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00004f00 .quad 0
	0x3a, 0x0f, 0x20, 0xf4, 0x27, 0x8f, 0xcb, 0xce, //0x00004f08 .quad -3545582879861895366
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x40, //0x00004f10 .quad 4611686018427387904
	0x84, 0x09, 0x94, 0xf8, 0x78, 0x39, 0x3f, 0x81, //0x00004f18 .quad -9133518327554766460
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x50, //0x00004f20 .quad 5764607523034234880
	0xe5, 0x0b, 0xb9, 0x36, 0xd7, 0x07, 0x8f, 0xa1, //0x00004f28 .quad -6805211891016070171
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xa4, //0x00004f30 .quad -6629298651489370112
	0xde, 0x4e, 0x67, 0x04, 0xcd, 0xc9, 0xf2, 0xc9, //0x00004f38 .quad -3894828845342699810
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x4d, //0x00004f40 .quad 5548434740920451072
	0x96, 0x22, 0x81, 0x45, 0x40, 0x7c, 0x6f, 0xfc, //0x00004f48 .quad -256850038250986858
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x20, 0xf0, //0x00004f50 .quad -1143914305352105984
	0x9d, 0xb5, 0x70, 0x2b, 0xa8, 0xad, 0xc5, 0x9d, //0x00004f58 .quad -7078060301547948643
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x28, 0x6c, //0x00004f60 .quad 7793479155164643328
	0x05, 0xe3, 0x4c, 0x36, 0x12, 0x19, 0x37, 0xc5, //0x00004f68 .quad -4235889358507547899
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x32, 0xc7, //0x00004f70 .quad -4093209111326359552
	0xc6, 0x1b, 0xe0, 0xc3, 0x56, 0xdf, 0x84, 0xf6, //0x00004f78 .quad -683175679707046970
	0x00, 0x00, 0x00, 0x00, 0x00, 0x40, 0x7f, 0x3c, //0x00004f80 .quad 4359273333062107136
	0x5c, 0x11, 0x6c, 0x3a, 0x96, 0x0b, 0x13, 0x9a, //0x00004f88 .quad -7344513827457986212
	0x00, 0x00, 0x00, 0x00, 0x00, 0x10, 0x9f, 0x4b, //0x00004f90 .quad 5449091666327633920
	0xb3, 0x15, 0x07, 0xc9, 0x7b, 0xce, 0x97, 0xc0, //0x00004f98 .quad -4568956265895094861
	0x00, 0x00, 0x00, 0x00, 0x00, 0xd4, 0x86, 0x1e, //0x00004fa0 .quad 2199678564482154496
	0x20, 0xdb, 0x48, 0xbb, 0x1a, 0xc2, 0xbd, 0xf0, //0x00004fa8 .quad -1099509313941480672
	0x00, 0x00, 0x00, 0x00, 0x80, 0x44, 0x14, 0x13, //0x00004fb0 .quad 1374799102801346560
	0xf4, 0x88, 0x0d, 0xb5, 0x50, 0x99, 0x76, 0x96, //0x00004fb8 .quad -7604722348854507276
	0x00, 0x00, 0x00, 0x00, 0xa0, 0x55, 0xd9, 0x17, //0x00004fc0 .quad 1718498878501683200
	0x31, 0xeb, 0x50, 0xe2, 0xa4, 0x3f, 0x14, 0xbc, //0x00004fc8 .quad -4894216917640746191
	0x00, 0x00, 0x00, 0x00, 0x08, 0xab, 0xcf, 0x5d, //0x00004fd0 .quad 6759809616554491904
	0xfd, 0x25, 0xe5, 0x1a, 0x8e, 0x4f, 0x19, 0xeb, //0x00004fd8 .quad -1506085128623544835
	0x00, 0x00, 0x00, 0x00, 0xe5, 0xca, 0xa1, 0x5a, //0x00004fe0 .quad 6530724019560251392
	0xbe, 0x37, 0xcf, 0xd0, 0xb8, 0xd1, 0xef, 0x92, //0x00004fe8 .quad -7858832233030797378
	0x00, 0x00, 0x00, 0x40, 0x9e, 0x3d, 0x4a, 0xf1, //0x00004ff0 .quad -1059967012404461568
	0xad, 0x05, 0x03, 0x05, 0x27, 0xc6, 0xab, 0xb7, //0x00004ff8 .quad -5211854272861108819
	0x00, 0x00, 0x00, 0xd0, 0x05, 0xcd, 0x9c, 0x6d, //0x00005000 .quad 7898413271349198848
	0x19, 0xc7, 0x43, 0xc6, 0xb0, 0xb7, 0x96, 0xe5, //0x00005008 .quad -1903131822648998119
	0x00, 0x00, 0x00, 0xa2, 0x23, 0x00, 0x82, 0xe4, //0x00005010 .quad -1981020733047832576
	0x6f, 0x5c, 0xea, 0x7b, 0xce, 0x32, 0x7e, 0x8f, //0x00005018 .quad -8106986416796705681
	0x00, 0x00, 0x80, 0x8a, 0x2c, 0x80, 0xa2, 0xdd, //0x00005020 .quad -2476275916309790720
	0x8b, 0xf3, 0xe4, 0x1a, 0x82, 0xbf, 0x5d, 0xb3, //0x00005028 .quad -5522047002568494197
	0x00, 0x00, 0x20, 0xad, 0x37, 0x20, 0x0b, 0xd5, //0x00005030 .quad -3095344895387238400
	0x6e, 0x30, 0x9e, 0xa1, 0x62, 0x2f, 0x35, 0xe0, //0x00005038 .quad -2290872734783229842
	0x00, 0x00, 0x34, 0xcc, 0x22, 0xf4, 0x26, 0x45, //0x00005040 .quad 4982938468024057856
	0x45, 0xde, 0x02, 0xa5, 0x9d, 0x3d, 0x21, 0x8c, //0x00005048 .quad -8349324486880600507
	0x00, 0x00, 0x41, 0x7f, 0x2b, 0xb1, 0x70, 0x96, //0x00005050 .quad -7606384970252091392
	0xd6, 0x95, 0x43, 0x0e, 0x05, 0x8d, 0x29, 0xaf, //0x00005058 .quad -5824969590173362730
	0x00, 0x40, 0x11, 0x5f, 0x76, 0xdd, 0x0c, 0x3c, //0x00005060 .quad 4327076842467049472
	0x4c, 0x7b, 0xd4, 0x51, 0x46, 0xf0, 0xf3, 0xda, //0x00005068 .quad -2669525969289315508
	0x00, 0xc8, 0x6a, 0xfb, 0x69, 0x0a, 0x88, 0xa5, //0x00005070 .quad -6518949010312869888
	0x0f, 0xcd, 0x24, 0xf3, 0x2b, 0x76, 0xd8, 0x88, //0x00005078 .quad -8585982758446904049
	0x00, 0x7a, 0x45, 0x7a, 0x04, 0x0d, 0xea, 0x8e, //0x00005080 .quad -8148686262891087360
	0x53, 0x00, 0xee, 0xef, 0xb6, 0x93, 0x0e, 0xab, //0x00005088 .quad -6120792429631242157
	0x80, 0xd8, 0xd6, 0x98, 0x45, 0x90, 0xa4, 0x72, //0x00005090 .quad 8260886245095692416
	0x68, 0x80, 0xe9, 0xab, 0xa4, 0x38, 0xd2, 0xd5, //0x00005098 .quad -3039304518611664792
	0x50, 0x47, 0x86, 0x7f, 0x2b, 0xda, 0xa6, 0x47, //0x000050a0 .quad 5163053903184807760
	0x41, 0xf0, 0x71, 0xeb, 0x66, 0x63, 0xa3, 0x85, //0x000050a8 .quad -8817094351773372351
	0x24, 0xd9, 0x67, 0x5f, 0xb6, 0x90, 0x90, 0x99, //0x000050b0 .quad -7381240676301154012
	0x51, 0x6c, 0x4e, 0xa6, 0x40, 0x3c, 0x0c, 0xa7, //0x000050b8 .quad -6409681921289327535
	0x6d, 0xcf, 0x41, 0xf7, 0xe3, 0xb4, 0xf4, 0xff, //0x000050c0 .quad -3178808521666707
	0x65, 0x07, 0xe2, 0xcf, 0x50, 0x4b, 0xcf, 0xd0, //0x000050c8 .quad -3400416383184271515
	0xa4, 0x21, 0x89, 0x7a, 0x0e, 0xf1, 0xf8, 0xbf, //0x000050d0 .quad -4613672773753429596
	0x9f, 0x44, 0xed, 0x81, 0x12, 0x8f, 0x81, 0x82, //0x000050d8 .quad -9042789267131251553
	0x0d, 0x6a, 0x2b, 0x19, 0x52, 0x2d, 0xf7, 0xaf, //0x000050e0 .quad -5767090967191786995
	0xc7, 0x95, 0x68, 0x22, 0xd7, 0xf2, 0x21, 0xa3, //0x000050e8 .quad -6691800565486676537
	0x90, 0x44, 0x76, 0x9f, 0xa6, 0xf8, 0xf4, 0x9b, //0x000050f0 .quad -7208863708989733744
	0x39, 0xbb, 0x02, 0xeb, 0x8c, 0x6f, 0xea, 0xcb, //0x000050f8 .quad -3753064688430957767
	0xb4, 0xd5, 0x53, 0x47, 0xd0, 0x36, 0xf2, 0x02, //0x00005100 .quad 212292400617608628
	0x08, 0x6a, 0xc3, 0x25, 0x70, 0x0b, 0xe5, 0xfe, //0x00005108 .quad -79644842111309304
	0x90, 0x65, 0x94, 0x2c, 0x42, 0x62, 0xd7, 0x01, //0x00005110 .quad 132682750386005392
	0x45, 0x22, 0x9a, 0x17, 0x26, 0x27, 0x4f, 0x9f, //0x00005118 .quad -6967307053960650171
	0xf5, 0x7e, 0xb9, 0xb7, 0xd2, 0x3a, 0x4d, 0x42, //0x00005120 .quad 4777539456409894645
	0xd6, 0xaa, 0x80, 0x9d, 0xef, 0xf0, 0x22, 0xc7, //0x00005128 .quad -4097447799023424810
	0xb2, 0xde, 0xa7, 0x65, 0x87, 0x89, 0xe0, 0xd2, //0x00005130 .quad -3251447716342407502
	0x8b, 0xd5, 0xe0, 0x84, 0x2b, 0xad, 0xeb, 0xf8, //0x00005138 .quad -510123730351893109
	0x2f, 0xeb, 0x88, 0x9f, 0xf4, 0x55, 0xcc, 0x63, //0x00005140 .quad 7191217214140771119
	0x77, 0x85, 0x0c, 0x33, 0x3b, 0x4c, 0x93, 0x9b, //0x00005148 .quad -7236356359111015049
	0xfb, 0x25, 0x6b, 0xc7, 0x71, 0x6b, 0xbf, 0x3c, //0x00005150 .quad 4377335499248575995
	0xd5, 0xa6, 0xcf, 0xff, 0x49, 0x1f, 0x78, 0xc2, //0x00005158 .quad -4433759430461380907
	0x7a, 0xef, 0x45, 0x39, 0x4e, 0x46, 0xef, 0x8b, //0x00005160 .quad -8363388681221443718
	0x8a, 0x90, 0xc3, 0x7f, 0x1c, 0x27, 0x16, 0xf3, //0x00005168 .quad -930513269649338230
	0xac, 0xb5, 0xcb, 0xe3, 0xf0, 0x8b, 0x75, 0x97, //0x00005170 .quad -7532960934977096276
	0x56, 0x3a, 0xda, 0xcf, 0x71, 0xd8, 0xed, 0x97, //0x00005178 .quad -7499099821171918250
	0x17, 0xa3, 0xbe, 0x1c, 0xed, 0xee, 0x52, 0x3d, //0x00005180 .quad 4418856886560793367
	0xec, 0xc8, 0xd0, 0x43, 0x8e, 0x4e, 0xe9, 0xbd, //0x00005188 .quad -4762188758037509908
	0xdd, 0x4b, 0xee, 0x63, 0xa8, 0xaa, 0xa7, 0x4c, //0x00005190 .quad 5523571108200991709
	0x27, 0xfb, 0xc4, 0xd4, 0x31, 0xa2, 0x63, 0xed, //0x00005198 .quad -1341049929119499481
	0x6a, 0xef, 0x74, 0x3e, 0xa9, 0xca, 0xe8, 0x8f, //0x000051a0 .quad -8076983103442849942
	0xf8, 0x1c, 0xfb, 0x24, 0x5f, 0x45, 0x5e, 0x94, //0x000051a8 .quad -7755685233340769032
	0x44, 0x2b, 0x12, 0x8e, 0x53, 0xfd, 0xe2, 0xb3, //0x000051b0 .quad -5484542860876174524
	0x36, 0xe4, 0x39, 0xee, 0xb6, 0xd6, 0x75, 0xb9, //0x000051b8 .quad -5082920523248573386
	0x16, 0xb6, 0x96, 0x71, 0xa8, 0xbc, 0xdb, 0x60, //0x000051c0 .quad 6979379479186945558
	0x44, 0x5d, 0xc8, 0xa9, 0x64, 0x4c, 0xd3, 0xe7, //0x000051c8 .quad -1741964635633328828
	0xcd, 0x31, 0xfe, 0x46, 0xe9, 0x55, 0x89, 0xbc, //0x000051d0 .quad -4861259862362934835
	0x4a, 0x3a, 0x1d, 0xea, 0xbe, 0x0f, 0xe4, 0x90, //0x000051d8 .quad -8006256924911912374
	0x41, 0xbe, 0xbd, 0x98, 0x63, 0xab, 0xab, 0x6b, //0x000051e0 .quad 7758483227328495169
	0xdd, 0x88, 0xa4, 0xa4, 0xae, 0x13, 0x1d, 0xb5, //0x000051e8 .quad -5396135137712502563
	0xd1, 0x2d, 0xed, 0x7e, 0x3c, 0x96, 0x96, 0xc6, //0x000051f0 .quad -4136954021121544751
	0x14, 0xab, 0xcd, 0x4d, 0x9a, 0x58, 0x64, 0xe2, //0x000051f8 .quad -2133482903713240300
	0xa2, 0x3c, 0x54, 0xcf, 0xe5, 0x1d, 0x1e, 0xfc, //0x00005200 .quad -279753253987271518
	0xec, 0x8a, 0xa0, 0x70, 0x60, 0xb7, 0x7e, 0x8d, //0x00005208 .quad -8250955842461857044
	0xcb, 0x4b, 0x29, 0x43, 0x5f, 0xa5, 0x25, 0x3b, //0x00005210 .quad 4261994450943298507
	0xa8, 0xad, 0xc8, 0x8c, 0x38, 0x65, 0xde, 0xb0, //0x00005218 .quad -5702008784649933400
	0xbe, 0x9e, 0xf3, 0x13, 0xb7, 0x0e, 0xef, 0x49, //0x00005220 .quad 5327493063679123134
	0x12, 0xd9, 0xfa, 0xaf, 0x86, 0xfe, 0x15, 0xdd, //0x00005228 .quad -2515824962385028846
	0x37, 0x43, 0x78, 0x6c, 0x32, 0x69, 0x35, 0x6e, //0x00005230 .quad 7941369183226839863
	0xab, 0xc7, 0xfc, 0x2d, 0x14, 0xbf, 0x2d, 0x8a, //0x00005238 .quad -8489919629131724885
	0x04, 0x54, 0x96, 0x07, 0x7f, 0xc3, 0xc2, 0x49, //0x00005240 .quad 5315025460606161924
	0x96, 0xf9, 0x7b, 0x39, 0xd9, 0x2e, 0xb9, 0xac, //0x00005248 .quad -6000713517987268202
	0x06, 0xe9, 0x7b, 0xc9, 0x5e, 0x74, 0x33, 0xdc, //0x00005250 .quad -2579590211097073402
	0xfb, 0xf7, 0xda, 0x87, 0x8f, 0x7a, 0xe7, 0xd7, //0x00005258 .quad -2889205879056697349
	0xa3, 0x71, 0xed, 0x3d, 0xbb, 0x28, 0xa0, 0x69, //0x00005260 .quad 7611128154919104931
	0xfd, 0xda, 0xe8, 0xb4, 0x99, 0xac, 0xf0, 0x86, //0x00005268 .quad -8723282702051517699
	0x0c, 0xce, 0x68, 0x0d, 0xea, 0x32, 0x08, 0xc4, //0x00005270 .quad -4321147861633282548
	0xbc, 0x11, 0x23, 0x22, 0xc0, 0xd7, 0xac, 0xa8, //0x00005278 .quad -6292417359137009220
	0x90, 0x01, 0xc3, 0x90, 0xa4, 0x3f, 0x0a, 0xf5, //0x00005280 .quad -789748808614215280
	0x2b, 0xd6, 0xab, 0x2a, 0xb0, 0x0d, 0xd8, 0xd2, //0x00005288 .quad -3253835680493873621
	0xfa, 0xe0, 0x79, 0xda, 0xc6, 0x67, 0x26, 0x79, //0x00005290 .quad 8729779031470891258
	0xdb, 0x65, 0xab, 0x1a, 0x8e, 0x08, 0xc7, 0x83, //0x00005298 .quad -8951176327949752869
	0x38, 0x59, 0x18, 0x91, 0xb8, 0x01, 0x70, 0x57, //0x000052a0 .quad 6300537770911226168
	0x52, 0x3f, 0x56, 0xa1, 0xb1, 0xca, 0xb8, 0xa4, //0x000052a8 .quad -6577284391509803182
	0x86, 0x6f, 0x5e, 0xb5, 0x26, 0x02, 0x4c, 0xed, //0x000052b0 .quad -1347699823215743098
	0x26, 0xcf, 0xab, 0x09, 0x5e, 0xfd, 0xe6, 0xcd, //0x000052b8 .quad -3609919470959866074
	0xb4, 0x05, 0x5b, 0x31, 0x58, 0x81, 0x4f, 0x54, //0x000052c0 .quad 6075216638131242420
	0x78, 0x61, 0x0b, 0xc6, 0x5a, 0x5e, 0xb0, 0x80, //0x000052c8 .quad -9173728696990998152
	0x21, 0xc7, 0xb1, 0x3d, 0xae, 0x61, 0x63, 0x69, //0x000052d0 .quad 7594020797664053025
	0xd6, 0x39, 0x8e, 0x77, 0xf1, 0x75, 0xdc, 0xa0, //0x000052d8 .quad -6855474852811359786
	0xe9, 0x38, 0x1e, 0xcd, 0x19, 0x3a, 0xbc, 0x03, //0x000052e0 .quad 269153960225290473
	0x4c, 0xc8, 0x71, 0xd5, 0x6d, 0x93, 0x13, 0xc9, //0x000052e8 .quad -3957657547586811828
	0x23, 0xc7, 0x65, 0x40, 0xa0, 0x48, 0xab, 0x04, //0x000052f0 .quad 336442450281613091
	0x5f, 0x3a, 0xce, 0x4a, 0x49, 0x78, 0x58, 0xfb, //0x000052f8 .quad -335385916056126881
	0x76, 0x9c, 0x3f, 0x28, 0x64, 0x0d, 0xeb, 0x62, //0x00005300 .quad 7127805559067090038
	0x7b, 0xe4, 0xc0, 0xce, 0x2d, 0x4b, 0x17, 0x9d, //0x00005308 .quad -7127145225176161157
	0x94, 0x83, 0x4f, 0x32, 0xbd, 0xd0, 0xa5, 0x3b, //0x00005310 .quad 4298070930406474644
	0x9a, 0x1d, 0x71, 0x42, 0xf9, 0x1d, 0x5d, 0xc4, //0x00005318 .quad -4297245513042813542
	0x79, 0x64, 0xe3, 0x7e, 0xec, 0x44, 0x8f, 0xca, //0x00005320 .quad -3850783373846682503
	0x00, 0x65, 0x0d, 0x93, 0x77, 0x65, 0x74, 0xf5, //0x00005328 .quad -759870872876129024
	0xcb, 0x1e, 0x4e, 0xcf, 0x13, 0x8b, 0x99, 0x7e, //0x00005330 .quad 9122475437414293195
	0x20, 0x5f, 0xe8, 0xbb, 0x6a, 0xbf, 0x68, 0x99, //0x00005338 .quad -7392448323188662496
	0x7e, 0xa6, 0x21, 0xc3, 0xd8, 0xed, 0x3f, 0x9e, //0x00005340 .quad -7043649776941685122
	0xe8, 0x76, 0xe2, 0x6a, 0x45, 0xef, 0xc2, 0xbf, //0x00005348 .quad -4628874385558440216
	0x1e, 0x10, 0xea, 0xf3, 0x4e, 0xe9, 0xcf, 0xc5, //0x00005350 .quad -4192876202749718498
	0xa2, 0x14, 0x9b, 0xc5, 0x16, 0xab, 0xb3, 0xef, //0x00005358 .quad -1174406963520662366
	0x12, 0x4a, 0x72, 0x58, 0xd1, 0xf1, 0xa1, 0xbb, //0x00005360 .quad -4926390635932268014
	0xe5, 0xec, 0x80, 0x3b, 0xee, 0x4a, 0xd0, 0x95, //0x00005368 .quad -7651533379841495835
	0x97, 0xdc, 0x8e, 0xae, 0x45, 0x6e, 0x8a, 0x2a, //0x00005370 .quad 3065383741939440791
	0x1f, 0x28, 0x61, 0xca, 0xa9, 0x5d, 0x44, 0xbb, //0x00005378 .quad -4952730706374481889
	0xbd, 0x93, 0x32, 0x1a, 0xd7, 0x09, 0x2d, 0xf5, //0x00005380 .quad -779956341003086915
	0x26, 0x72, 0xf9, 0x3c, 0x14, 0x75, 0x15, 0xea, //0x00005388 .quad -1579227364540714458
	0x56, 0x9c, 0x5f, 0x70, 0x26, 0x26, 0x3c, 0x59, //0x00005390 .quad 6430056314514152534
	0x58, 0xe7, 0x1b, 0xa6, 0x2c, 0x69, 0x4d, 0x92, //0x00005398 .quad -7904546130479028392
	0x6c, 0x83, 0x77, 0x0c, 0xb0, 0x2f, 0x8b, 0x6f, //0x000053a0 .quad 8037570393142690668
	0x2e, 0xe1, 0xa2, 0xcf, 0x77, 0xc3, 0xe0, 0xb6, //0x000053a8 .quad -5268996644671397586
	0x47, 0x64, 0x95, 0x0f, 0x9c, 0xfb, 0x6d, 0x0b, //0x000053b0 .quad 823590954573587527
	0x7a, 0x99, 0x8b, 0xc3, 0x55, 0xf4, 0x98, 0xe4, //0x000053b8 .quad -1974559787411859078
	0xac, 0x5e, 0xbd, 0x89, 0x41, 0xbd, 0x24, 0x47, //0x000053c0 .quad 5126430365035880108
	0xec, 0x3f, 0x37, 0x9a, 0xb5, 0x98, 0xdf, 0x8e, //0x000053c8 .quad -8151628894773493780
	0x57, 0xb6, 0x2c, 0xec, 0x91, 0xec, 0xed, 0x58, //0x000053d0 .quad 6408037956294850135
	0xe7, 0x0f, 0xc5, 0x00, 0xe3, 0x7e, 0x97, 0xb2, //0x000053d8 .quad -5577850100039479321
	0xed, 0xe3, 0x37, 0x67, 0xb6, 0x67, 0x29, 0x2f, //0x000053e0 .quad 3398361426941174765
	0xe1, 0x53, 0xf6, 0xc0, 0x9b, 0x5e, 0x3d, 0xdf, //0x000053e8 .quad -2360626606621961247
	0x74, 0xee, 0x82, 0x00, 0xd2, 0xe0, 0x79, 0xbd, //0x000053f0 .quad -4793553135802847628
	0x6c, 0xf4, 0x99, 0x58, 0x21, 0x5b, 0x86, 0x8b, //0x000053f8 .quad -8392920656779807636
	0x11, 0xaa, 0xa3, 0x80, 0x06, 0x59, 0xd8, 0xec, //0x00005400 .quad -1380255401326171631
	0x87, 0x71, 0xc0, 0xae, 0xe9, 0xf1, 0x67, 0xae, //0x00005408 .quad -5879464802547371641
	0x95, 0x94, 0xcc, 0x20, 0x48, 0x6f, 0x0e, 0xe8, //0x00005410 .quad -1725319251657714539
	0xe9, 0x8d, 0x70, 0x1a, 0x64, 0xee, 0x01, 0xda, //0x00005418 .quad -2737644984756826647
	0xdd, 0xdc, 0x7f, 0x14, 0x8d, 0x05, 0x09, 0x31, //0x00005420 .quad 3533361486141316317
	0xb2, 0x58, 0x86, 0x90, 0xfe, 0x34, 0x41, 0x88, //0x00005428 .quad -8628557143114098510
	0x15, 0xd4, 0x9f, 0x59, 0xf0, 0x46, 0x4b, 0xbd, //0x00005430 .quad -4806670179178130411
	0xde, 0xee, 0xa7, 0x34, 0x3e, 0x82, 0x51, 0xaa, //0x00005438 .quad -6174010410465235234
	0x1a, 0xc9, 0x07, 0x70, 0xac, 0x18, 0x9e, 0x6c, //0x00005440 .quad 7826720331309500698
	0x96, 0xea, 0xd1, 0xc1, 0xcd, 0xe2, 0xe5, 0xd4, //0x00005448 .quad -3105826994654156138
	0xb0, 0xdd, 0x04, 0xc6, 0x6b, 0xcf, 0xe2, 0x03, //0x00005450 .quad 280014188641050032
	0x9e, 0x32, 0x23, 0x99, 0xc0, 0xad, 0x0f, 0x85, //0x00005458 .quad -8858670899299929442
	0x1c, 0x15, 0x86, 0xb7, 0x46, 0x83, 0xdb, 0x84, //0x00005460 .quad -8873354301053463268
	0x45, 0xff, 0x6b, 0xbf, 0x30, 0x99, 0x53, 0xa6, //0x00005468 .quad -6461652605697523899
	0x63, 0x9a, 0x67, 0x65, 0x18, 0x64, 0x12, 0xe6, //0x00005470 .quad -1868320839462053277
	0x16, 0xff, 0x46, 0xef, 0x7c, 0x7f, 0xe8, 0xcf, //0x00005478 .quad -3465379738694516970
	0x7e, 0xc0, 0x60, 0x3f, 0x8f, 0x7e, 0xcb, 0x4f, //0x00005480 .quad 5749828502977298558
	0x6e, 0x5f, 0x8c, 0x15, 0xae, 0x4f, 0xf1, 0x81, //0x00005488 .quad -9083391364325154962
	0x9d, 0xf0, 0x38, 0x0f, 0x33, 0x5e, 0xbe, 0xe3, //0x00005490 .quad -2036086408133152611
	0x49, 0x77, 0xef, 0x9a, 0x99, 0xa3, 0x6d, 0xa2, //0x00005498 .quad -6742553186979055799
	0xc5, 0x2c, 0x07, 0xd3, 0xbf, 0xf5, 0xad, 0x5c, //0x000054a0 .quad 6678264026688335045
	0x1c, 0x55, 0xab, 0x01, 0x80, 0x0c, 0x09, 0xcb, //0x000054a8 .quad -3816505465296431844
	0xf6, 0xf7, 0xc8, 0xc7, 0x2f, 0x73, 0xd9, 0x73, //0x000054b0 .quad 8347830033360418806
	0x63, 0x2a, 0x16, 0x02, 0xa0, 0x4f, 0xcb, 0xfd, //0x000054b8 .quad -158945813193151901
	0xfa, 0x9a, 0xdd, 0xdc, 0xfd, 0xe7, 0x67, 0x28, //0x000054c0 .quad 2911550761636567802
	0x7e, 0xda, 0x4d, 0x01, 0xc4, 0x11, 0x9f, 0x9e, //0x000054c8 .quad -7016870160886801794
	0xb8, 0x01, 0x15, 0x54, 0xfd, 0xe1, 0x81, 0xb2, //0x000054d0 .quad -5583933584809066056
	0x1d, 0x51, 0xa1, 0x01, 0x35, 0xd6, 0x46, 0xc6, //0x000054d8 .quad -4159401682681114339
	0x26, 0x42, 0x1a, 0xa9, 0x7c, 0x5a, 0x22, 0x1f, //0x000054e0 .quad 2243455055843443238
	0x65, 0xa5, 0x09, 0x42, 0xc2, 0x8b, 0xd8, 0xf7, //0x000054e8 .quad -587566084924005019
	0x58, 0x69, 0xb0, 0xe9, 0x8d, 0x78, 0x75, 0x33, //0x000054f0 .quad 3708002419115845976
	0x5f, 0x07, 0x46, 0x69, 0x59, 0x57, 0xe7, 0x9a, //0x000054f8 .quad -7284757830718584993
	0xae, 0x83, 0x1c, 0x64, 0xb1, 0xd6, 0x52, 0x00, //0x00005500 .quad 23317005467419566
	0x37, 0x89, 0x97, 0xc3, 0x2f, 0x2d, 0xa1, 0xc1, //0x00005508 .quad -4494261269970843337
	0x9a, 0xa4, 0x23, 0xbd, 0x5d, 0x8c, 0x67, 0xc0, //0x00005510 .quad -4582539761593113446
	0x84, 0x6b, 0x7d, 0xb4, 0x7b, 0x78, 0x09, 0xf2, //0x00005518 .quad -1006140569036166268
	0xe0, 0x46, 0x36, 0x96, 0xba, 0xb7, 0x40, 0xf8, //0x00005520 .quad -558244341782001952
	0x32, 0x63, 0xce, 0x50, 0x4d, 0xeb, 0x45, 0x97, //0x00005528 .quad -7546366883288685774
	0x98, 0xd8, 0xc3, 0x3b, 0xa9, 0xe5, 0x50, 0xb6, //0x00005530 .quad -5309491445654890344
	0xff, 0xfb, 0x01, 0xa5, 0x20, 0x66, 0x17, 0xbd, //0x00005538 .quad -4821272585683469313
	0xbe, 0xce, 0xb4, 0x8a, 0x13, 0x1f, 0xe5, 0xa3, //0x00005540 .quad -6636864307068612930
	0xff, 0x7a, 0x42, 0xce, 0xa8, 0x3f, 0x5d, 0xec, //0x00005548 .quad -1414904713676948737
	0x37, 0x01, 0xb1, 0x36, 0x6c, 0x33, 0x6f, 0xc6, //0x00005550 .quad -4148040191917883081
	0xdf, 0x8c, 0xe9, 0x80, 0xc9, 0x47, 0xba, 0x93, //0x00005558 .quad -7801844473689174817
	0x84, 0x41, 0x5d, 0x44, 0x47, 0x00, 0x0b, 0xb8, //0x00005560 .quad -5185050239897353852
	0x17, 0xf0, 0x23, 0xe1, 0xbb, 0xd9, 0xa8, 0xb8, //0x00005568 .quad -5140619573684080617
	0xe5, 0x91, 0x74, 0x15, 0x59, 0xc0, 0x0d, 0xa6, //0x00005570 .quad -6481312799871692315
	0x1d, 0xec, 0x6c, 0xd9, 0x2a, 0x10, 0xd3, 0xe6, //0x00005578 .quad -1814088448677712867
	0x2f, 0xdb, 0x68, 0xad, 0x37, 0x98, 0xc8, 0x87, //0x00005580 .quad -8662506518347195601
	0x92, 0x13, 0xe4, 0xc7, 0x1a, 0xea, 0x43, 0x90, //0x00005588 .quad -8051334308064652398
	0xfb, 0x11, 0xc3, 0x98, 0x45, 0xbe, 0xba, 0x29, //0x00005590 .quad 3006924907348169211
	0x77, 0x18, 0xdd, 0x79, 0xa1, 0xe4, 0x54, 0xb4, //0x00005598 .quad -5452481866653427593
	0x7a, 0xd6, 0xf3, 0xfe, 0xd6, 0x6d, 0x29, 0xf4, //0x000055a0 .quad -853029884242176390
	0x94, 0x5e, 0x54, 0xd8, 0xc9, 0x1d, 0x6a, 0xe1, //0x000055a8 .quad -2203916314889396588
	0x0c, 0x66, 0x58, 0x5f, 0xa6, 0xe4, 0x99, 0x18, //0x000055b0 .quad 1772699331562333708
	0x1d, 0xbb, 0x34, 0x27, 0x9e, 0x52, 0xe2, 0x8c, //0x000055b8 .quad -8294976724446954723
	0x8f, 0x7f, 0x2e, 0xf7, 0xcf, 0x5d, 0xc0, 0x5e, //0x000055c0 .quad 6827560182880305039
	0xe4, 0xe9, 0x01, 0xb1, 0x45, 0xe7, 0x1a, 0xb0, //0x000055c8 .quad -5757034887131305500
	0x73, 0x1f, 0xfa, 0xf4, 0x43, 0x75, 0x70, 0x76, //0x000055d0 .quad 8534450228600381299
	0x5d, 0x64, 0x42, 0x1d, 0x17, 0xa1, 0x21, 0xdc, //0x000055d8 .quad -2584607590486743971
	0xa8, 0x53, 0x1c, 0x79, 0x4a, 0x49, 0x06, 0x6a, //0x000055e0 .quad 7639874402088932264
	0xba, 0x7e, 0x49, 0x72, 0xae, 0x04, 0x95, 0x89, //0x000055e8 .quad -8532908771695296838
	0x92, 0x68, 0x63, 0x17, 0x9d, 0xdb, 0x87, 0x04, //0x000055f0 .quad 326470965756389522
	0x69, 0xde, 0xdb, 0x0e, 0xda, 0x45, 0xfa, 0xab, //0x000055f8 .quad -6054449946191733143
	0xb6, 0x42, 0x3c, 0x5d, 0x84, 0xd2, 0xa9, 0x45, //0x00005600 .quad 5019774725622874806
	0x03, 0xd6, 0x92, 0x92, 0x50, 0xd7, 0xf8, 0xd6, //0x00005608 .quad -2956376414312278525
	0xb2, 0xa9, 0x45, 0xba, 0x92, 0x23, 0x8a, 0x0b, //0x00005610 .quad 831516194300602802
	0xc2, 0xc5, 0x9b, 0x5b, 0x92, 0x86, 0x5b, 0x86, //0x00005618 .quad -8765264286586255934
	0x1e, 0x14, 0xd7, 0x68, 0x77, 0xac, 0x6c, 0x8e, //0x00005620 .quad -8183976793979022306
	0x32, 0xb7, 0x82, 0xf2, 0x36, 0x68, 0xf2, 0xa7, //0x00005628 .quad -6344894339805432014
	0x26, 0xd9, 0x0c, 0x43, 0x95, 0xd7, 0x07, 0x32, //0x00005630 .quad 3605087062808385830
	0xff, 0x64, 0x23, 0xaf, 0x44, 0x02, 0xef, 0xd1, //0x00005638 .quad -3319431906329402113
	0xb8, 0x07, 0xe8, 0x49, 0xbd, 0xe6, 0x44, 0x7f, //0x00005640 .quad 9170708441896323000
	0x1f, 0x1f, 0x76, 0xed, 0x6a, 0x61, 0x35, 0x83, //0x00005648 .quad -8992173969096958177
	0xa6, 0x09, 0x62, 0x9c, 0x6c, 0x20, 0x16, 0x5f, //0x00005650 .quad 6851699533943015846
	0xe7, 0xa6, 0xd3, 0xa8, 0xc5, 0xb9, 0x02, 0xa4, //0x00005658 .quad -6628531442943809817
	0x0f, 0x8c, 0x7a, 0xc3, 0x87, 0xa8, 0xdb, 0x36, //0x00005660 .quad 3952938399001381903
	0xa1, 0x90, 0x08, 0x13, 0x37, 0x68, 0x03, 0xcd, //0x00005668 .quad -3673978285252374367
	0x89, 0x97, 0x2c, 0xda, 0x54, 0x49, 0x49, 0xc2, //0x00005670 .quad -4446942528265218167
	0x64, 0x5a, 0xe5, 0x6b, 0x22, 0x21, 0x22, 0x80, //0x00005678 .quad -9213765455923815836
	0x6c, 0xbd, 0xb7, 0x10, 0xaa, 0x9b, 0xdb, 0xf2, //0x00005680 .quad -946992141904134804
	0xfd, 0xb0, 0xde, 0x06, 0x6b, 0xa9, 0x2a, 0xa0, //0x00005688 .quad -6905520801477381891
	0xc7, 0xac, 0xe5, 0x94, 0x94, 0x82, 0x92, 0x6f, //0x00005690 .quad 8039631859474607303
	0x3d, 0x5d, 0x96, 0xc8, 0xc5, 0x53, 0x35, 0xc8, //0x00005698 .quad -4020214983419339459
	0xf9, 0x17, 0x1f, 0xba, 0x39, 0x23, 0x77, 0xcb, //0x000056a0 .quad -3785518230938904583
	0x8c, 0xf4, 0xbb, 0x3a, 0xb7, 0xa8, 0x42, 0xfa, //0x000056a8 .quad -413582710846786420
	0xfb, 0x6e, 0x53, 0x14, 0x04, 0x76, 0x2a, 0xff, //0x000056b0 .quad -60105885123121413
	0xd7, 0x78, 0xb5, 0x84, 0x72, 0xa9, 0x69, 0x9c, //0x000056b8 .quad -7176018221920323369
	0xba, 0x4a, 0x68, 0x19, 0x85, 0x13, 0xf5, 0xfe, //0x000056c0 .quad -75132356403901766
	0x0d, 0xd7, 0xe2, 0x25, 0xcf, 0x13, 0x84, 0xc3, //0x000056c8 .quad -4358336758973016307
	0x69, 0x5d, 0xc2, 0x5f, 0x66, 0x58, 0xb2, 0x7e, //0x000056d0 .quad 9129456591349898601
	0xd1, 0x8c, 0x5b, 0xef, 0xc2, 0x18, 0x65, 0xf4, //0x000056d8 .quad -836234930288882479
	0x61, 0x7a, 0xd9, 0xfb, 0x3f, 0x77, 0x2f, 0xef, //0x000056e0 .quad -1211618658047395231
	0x02, 0x38, 0x99, 0xd5, 0x79, 0x2f, 0xbf, 0x98, //0x000056e8 .quad -7440175859071633406
	0xfa, 0xd8, 0xcf, 0xfa, 0x0f, 0x55, 0xfb, 0xaa, //0x000056f0 .quad -6126209340986631942
	0x03, 0x86, 0xff, 0x4a, 0x58, 0xfb, 0xee, 0xbe, //0x000056f8 .quad -4688533805412153853
	0x38, 0xcf, 0x83, 0xf9, 0x53, 0x2a, 0xba, 0x95, //0x00005700 .quad -7657761676233289928
	0x84, 0x67, 0xbf, 0x5d, 0x2e, 0xba, 0xaa, 0xee, //0x00005708 .quad -1248981238337804412
	0x83, 0x61, 0xf2, 0x7b, 0x74, 0x5a, 0x94, 0xdd, //0x00005710 .quad -2480258038432112253
	0xb2, 0xa0, 0x97, 0xfa, 0x5c, 0xb4, 0x2a, 0x95, //0x00005718 .quad -7698142301602209614
	0xe4, 0xf9, 0xee, 0x9a, 0x11, 0x71, 0xf9, 0x94, //0x00005720 .quad -7712008566467528220
	0xdf, 0x88, 0x3d, 0x39, 0x74, 0x61, 0x75, 0xba, //0x00005728 .quad -5010991858575374113
	0x5d, 0xb8, 0xaa, 0x01, 0x56, 0xcd, 0x37, 0x7a, //0x00005730 .quad 8806733365625141341
	0x17, 0xeb, 0x8c, 0x47, 0xd1, 0xb9, 0x12, 0xe9, //0x00005738 .quad -1652053804791829737
	0x3a, 0xb3, 0x0a, 0xc1, 0x55, 0xe0, 0x62, 0xac, //0x00005740 .quad -6025006692552756422
	0xee, 0x12, 0xb8, 0xcc, 0x22, 0xb4, 0xab, 0x91, //0x00005748 .quad -7950062655635975442
	0x09, 0x60, 0x4d, 0x31, 0x6b, 0x98, 0x7b, 0x57, //0x00005750 .quad 6303799689591218185
	0xaa, 0x17, 0xe6, 0x7f, 0x2b, 0xa1, 0x16, 0xb6, //0x00005758 .quad -5325892301117581398
	0x0b, 0xb8, 0xa0, 0xfd, 0x85, 0x7e, 0x5a, 0xed, //0x00005760 .quad -1343622424865753077
	0x94, 0x9d, 0xdf, 0x5f, 0x76, 0x49, 0x9c, 0xe3, //0x00005768 .quad -2045679357969588844
	0x07, 0x73, 0x84, 0xbe, 0x13, 0x8f, 0x58, 0x14, //0x00005770 .quad 1466078993672598279
	0x7d, 0xc2, 0xeb, 0xfb, 0xe9, 0xad, 0x41, 0x8e, //0x00005778 .quad -8196078626372074883
	0xc8, 0x8f, 0x25, 0xae, 0xd8, 0xb2, 0x6e, 0x59, //0x00005780 .quad 6444284760518135752
	0x1c, 0xb3, 0xe6, 0x7a, 0x64, 0x19, 0xd2, 0xb1, //0x00005788 .quad -5633412264537705700
	0xbb, 0xf3, 0xae, 0xd9, 0x8e, 0x5f, 0xca, 0x6f, //0x00005790 .quad 8055355950647669691
	0xe3, 0x5f, 0xa0, 0x99, 0xbd, 0x9f, 0x46, 0xde, //0x00005798 .quad -2430079312244744221
	0x54, 0x58, 0x0d, 0x48, 0xb9, 0x7b, 0xde, 0x25, //0x000057a0 .quad 2728754459941099604
	0xee, 0x3b, 0x04, 0x80, 0xd6, 0x23, 0xec, 0x8a, //0x000057a8 .quad -8436328597794046994
	0x6a, 0xae, 0x10, 0x9a, 0xa7, 0x1a, 0x56, 0xaf, //0x000057b0 .quad -5812428961928401302
	0xe9, 0x4a, 0x05, 0x20, 0xcc, 0x2c, 0xa7, 0xad, //0x000057b8 .quad -5933724728815170839
	0x04, 0xda, 0x94, 0x80, 0x51, 0xa1, 0x2b, 0x1b, //0x000057c0 .quad 1957835834444274180
	0xa4, 0x9d, 0x06, 0x28, 0xff, 0xf7, 0x10, 0xd9, //0x000057c8 .quad -2805469892591575644
	0x42, 0x08, 0x5d, 0xf0, 0xd2, 0x44, 0xfb, 0x90, //0x000057d0 .quad -7999724640327104446
	0x86, 0x22, 0x04, 0x79, 0xff, 0x9a, 0xaa, 0x87, //0x000057d8 .quad -8670947710510816634
	0x53, 0x4a, 0x74, 0xac, 0x07, 0x16, 0x3a, 0x35, //0x000057e0 .quad 3835402254873283155
	0x28, 0x2b, 0x45, 0x57, 0xbf, 0x41, 0x95, 0xa9, //0x000057e8 .quad -6226998619711132888
	0xe8, 0x5c, 0x91, 0x97, 0x89, 0x9b, 0x88, 0x42, //0x000057f0 .quad 4794252818591603944
	0xf2, 0x75, 0x16, 0x2d, 0x2f, 0x92, 0xfa, 0xd3, //0x000057f8 .quad -3172062256211528206
	0x11, 0xda, 0xba, 0xfe, 0x35, 0x61, 0x95, 0x69, //0x00005800 .quad 7608094030047140369
	0xb7, 0x09, 0x2e, 0x7c, 0x5d, 0x9b, 0x7c, 0x84, //0x00005808 .quad -8900067937773286985
	0x95, 0x90, 0x69, 0x7e, 0x83, 0xb9, 0xfa, 0x43, //0x00005810 .quad 4898431519131537557
	0x25, 0x8c, 0x39, 0xdb, 0x34, 0xc2, 0x9b, 0xa5, //0x00005818 .quad -6513398903789220827
	0xbb, 0xf4, 0x03, 0x5e, 0xe4, 0x67, 0xf9, 0x94, //0x00005820 .quad -7712018656367741765
	0x2e, 0xef, 0x07, 0x12, 0xc2, 0xb2, 0x02, 0xcf, //0x00005828 .quad -3530062611309138130
	0xf5, 0x78, 0xc2, 0xba, 0xee, 0xe0, 0x1b, 0x1d, //0x00005830 .quad 2097517367411243253
	0x7d, 0xf5, 0x44, 0x4b, 0xb9, 0xaf, 0x61, 0x81, //0x00005838 .quad -9123818159709293187
	0x32, 0x17, 0x73, 0x69, 0x2a, 0xd9, 0x62, 0x64, //0x00005840 .quad 7233582727691441970
	0xdc, 0x32, 0x16, 0x9e, 0xa7, 0x1b, 0xba, 0xa1, //0x00005848 .quad -6793086681209228580
	0xfe, 0xdc, 0xcf, 0x03, 0x75, 0x8f, 0x7b, 0x7d, //0x00005850 .quad 9041978409614302462
	0x93, 0xbf, 0x9b, 0x85, 0x91, 0xa2, 0x28, 0xca, //0x00005858 .quad -3879672333084147821
	0x3e, 0xd4, 0xc3, 0x44, 0x52, 0x73, 0xda, 0x5c, //0x00005860 .quad 6690786993590490174
	0x78, 0xaf, 0x02, 0xe7, 0x35, 0xcb, 0xb2, 0xfc, //0x00005868 .quad -237904397927796872
	0xa7, 0x64, 0xfa, 0x6a, 0x13, 0x88, 0x08, 0x3a, //0x00005870 .quad 4181741870994056359
	0xab, 0xad, 0x61, 0xb0, 0x01, 0xbf, 0xef, 0x9d, //0x00005878 .quad -7066219276345954901
	0xd0, 0xfd, 0xb8, 0x45, 0x18, 0xaa, 0x8a, 0x08, //0x00005880 .quad 615491320315182544
	0x16, 0x19, 0x7a, 0x1c, 0xc2, 0xae, 0x6b, 0xc5, //0x00005888 .quad -4221088077005055722
	0x45, 0x3d, 0x27, 0x57, 0x9e, 0x54, 0xad, 0x8a, //0x00005890 .quad -8454007886460797627
	0x5b, 0x9f, 0x98, 0xa3, 0x72, 0x9a, 0xc6, 0xf6, //0x00005898 .quad -664674077828931749
	0x4b, 0x86, 0x78, 0xf6, 0xe2, 0x54, 0xac, 0x36, //0x000058a0 .quad 3939617107816777291
	0x99, 0x63, 0x3f, 0xa6, 0x87, 0x20, 0x3c, 0x9a, //0x000058a8 .quad -7332950326284164199
	0xdd, 0xa7, 0x16, 0xb4, 0x1b, 0x6a, 0x57, 0x84, //0x000058b0 .quad -8910536670511192099
	0x7f, 0x3c, 0xcf, 0x8f, 0xa9, 0x28, 0xcb, 0xc0, //0x000058b8 .quad -4554501889427817345
	0xd5, 0x51, 0x1c, 0xa1, 0xa2, 0x44, 0x6d, 0x65, //0x000058c0 .quad 7308573235570561493
	0x9f, 0x0b, 0xc3, 0xf3, 0xd3, 0xf2, 0xfd, 0xf0, //0x000058c8 .quad -1081441343357383777
	0x25, 0xb3, 0xb1, 0xa4, 0xe5, 0x4a, 0x64, 0x9f, //0x000058d0 .quad -6961356773836868827
	0x43, 0xe7, 0x59, 0x78, 0xc4, 0xb7, 0x9e, 0x96, //0x000058d8 .quad -7593429867239446717
	0xee, 0x1f, 0xde, 0x0d, 0x9f, 0x5d, 0x3d, 0x87, //0x000058e0 .quad -8701695967296086034
	0x14, 0x61, 0x70, 0x96, 0xb5, 0x65, 0x46, 0xbc, //0x000058e8 .quad -4880101315621920492
	0xea, 0xa7, 0x55, 0xd1, 0x06, 0xb5, 0x0c, 0xa9, //0x000058f0 .quad -6265433940692719638
	0x59, 0x79, 0x0c, 0xfc, 0x22, 0xff, 0x57, 0xeb, //0x000058f8 .quad -1488440626100012711
	0xf2, 0x88, 0xd5, 0x42, 0x24, 0xf1, 0xa7, 0x09, //0x00005900 .quad 695789805494438130
	0xd8, 0xcb, 0x87, 0xdd, 0x75, 0xff, 0x16, 0x93, //0x00005908 .quad -7847804418953589800
	0x2f, 0xeb, 0x8a, 0x53, 0x6d, 0xed, 0x11, 0x0c, //0x00005910 .quad 869737256868047663
	0xce, 0xbe, 0xe9, 0x54, 0x53, 0xbf, 0xdc, 0xb7, //0x00005918 .quad -5198069505264599346
	0xfa, 0xa5, 0x6d, 0xa8, 0xc8, 0x68, 0x16, 0x8f, //0x00005920 .quad -8136200465769716230
	0x81, 0x2e, 0x24, 0x2a, 0x28, 0xef, 0xd3, 0xe5, //0x00005928 .quad -1885900863153361279
	0xbc, 0x87, 0x44, 0x69, 0x7d, 0x01, 0x6e, 0xf9, //0x00005930 .quad -473439272678684740
	0x10, 0x9d, 0x56, 0x1a, 0x79, 0x75, 0xa4, 0x8f, //0x00005938 .quad -8096217067111932656
	0xac, 0xa9, 0x95, 0xc3, 0xdc, 0x81, 0xc9, 0x37, //0x00005940 .quad 4019886927579031980
	0x55, 0x44, 0xec, 0x60, 0xd7, 0x92, 0x8d, 0xb3, //0x00005948 .quad -5508585315462527915
	0x17, 0x14, 0x7b, 0xf4, 0x53, 0xe2, 0xbb, 0x85, //0x00005950 .quad -8810199395808373737
	0x6a, 0x55, 0x27, 0x39, 0x8d, 0xf7, 0x70, 0xe0, //0x00005958 .quad -2274045625900771990
	0x8e, 0xec, 0xcc, 0x78, 0x74, 0x6d, 0x95, 0x93, //0x00005960 .quad -7812217631593927538
	0x62, 0x95, 0xb8, 0x43, 0xb8, 0x9a, 0x46, 0x8c, //0x00005968 .quad -8338807543829064350
	0xb2, 0x27, 0x00, 0x97, 0xd1, 0xc8, 0x7a, 0x38, //0x00005970 .quad 4069786015789754290
	0xbb, 0xba, 0xa6, 0x54, 0x66, 0x41, 0x58, 0xaf, //0x00005978 .quad -5811823411358942533
	0x9e, 0x31, 0xc0, 0xfc, 0x05, 0x7b, 0x99, 0x06, //0x00005980 .quad 475546501309804958
	0x6a, 0x69, 0xd0, 0xe9, 0xbf, 0x51, 0x2e, 0xdb, //0x00005988 .quad -2653093245771290262
	0x03, 0x1f, 0xf8, 0xbd, 0xe3, 0xec, 0x1f, 0x44, //0x00005990 .quad 4908902581746016003
	0xe2, 0x41, 0x22, 0xf2, 0x17, 0xf3, 0xfc, 0x88, //0x00005998 .quad -8575712306248138270
	0xc3, 0x26, 0x76, 0xad, 0x1c, 0xe8, 0x27, 0xd5, //0x000059a0 .quad -3087243809672255805
	0x5a, 0xd2, 0xaa, 0xee, 0xdd, 0x2f, 0x3c, 0xab, //0x000059a8 .quad -6107954364382784934
	0x74, 0xb0, 0xd3, 0xd8, 0x23, 0xe2, 0x71, 0x8a, //0x000059b0 .quad -8470740780517707660
	0xf1, 0x86, 0x55, 0x6a, 0xd5, 0x3b, 0x0b, 0xd6, //0x000059b8 .quad -3023256937051093263
	0x49, 0x4e, 0x84, 0x67, 0x56, 0x2d, 0x87, 0xf6, //0x000059c0 .quad -682526969396179383
	0x56, 0x74, 0x75, 0x62, 0x65, 0x05, 0xc7, 0x85, //0x000059c8 .quad -8807064613298015146
	0xdb, 0x61, 0x65, 0x01, 0xac, 0xf8, 0x28, 0xb4, //0x000059d0 .quad -5464844730172612133
	0x6c, 0xd1, 0x12, 0xbb, 0xbe, 0xc6, 0x38, 0xa7, //0x000059d8 .quad -6397144748195131028
	0x52, 0xba, 0xbe, 0x01, 0xd7, 0x36, 0x33, 0xe1, //0x000059e0 .quad -2219369894288377262
	0xc7, 0x85, 0xd7, 0x69, 0x6e, 0xf8, 0x06, 0xd1, //0x000059e8 .quad -3384744916816525881
	0x73, 0x34, 0x17, 0x61, 0x46, 0x02, 0xc0, 0xec, //0x000059f0 .quad -1387106183930235789
	0x9c, 0xb3, 0x26, 0x02, 0x45, 0x5b, 0xa4, 0x82, //0x000059f8 .quad -9032994600651410532
	0x90, 0x01, 0x5d, 0xf9, 0xd7, 0x02, 0xf0, 0x27, //0x00005a00 .quad 2877803288514593168
	0x84, 0x60, 0xb0, 0x42, 0x16, 0x72, 0x4d, 0xa3, //0x00005a08 .quad -6679557232386875260
	0xf4, 0x41, 0xb4, 0xf7, 0x8d, 0x03, 0xec, 0x31, //0x00005a10 .quad 3597254110643241460
	0xa5, 0x78, 0x5c, 0xd3, 0x9b, 0xce, 0x20, 0xcc, //0x00005a18 .quad -3737760522056206171
	0x71, 0x52, 0xa1, 0x75, 0x71, 0x04, 0x67, 0x7e, //0x00005a20 .quad 9108253656731439729
	0xce, 0x96, 0x33, 0xc8, 0x42, 0x02, 0x29, 0xff, //0x00005a28 .quad -60514634142869810
	0x86, 0xd3, 0x84, 0xe9, 0xc6, 0x62, 0x00, 0x0f, //0x00005a30 .quad 1080972517029761926
	0x41, 0x3e, 0x20, 0xbd, 0x69, 0xa1, 0x79, 0x9f, //0x00005a38 .quad -6955350673980375487
	0x68, 0x08, 0xe6, 0xa3, 0x78, 0x7b, 0xc0, 0x52, //0x00005a40 .quad 5962901664714590312
	0xd1, 0x4d, 0x68, 0x2c, 0xc4, 0x09, 0x58, 0xc7, //0x00005a48 .quad -4082502324048081455
	0x82, 0x8a, 0xdf, 0xcc, 0x56, 0x9a, 0x70, 0xa7, //0x00005a50 .quad -6381430974388925822
	0x45, 0x61, 0x82, 0x37, 0x35, 0x0c, 0x2e, 0xf9, //0x00005a58 .quad -491441886632713915
	0x91, 0xb6, 0x0b, 0x40, 0x76, 0x60, 0xa6, 0x88, //0x00005a60 .quad -8600080377420466543
	0xcb, 0x7c, 0xb1, 0x42, 0xa1, 0xc7, 0xbc, 0x9b, //0x00005a68 .quad -7224680206786528053
	0x35, 0xa4, 0x0e, 0xd0, 0x93, 0xf8, 0xcf, 0x6a, //0x00005a70 .quad 7696643601933968437
	0xfe, 0xdb, 0x5d, 0x93, 0x89, 0xf9, 0xab, 0xc2, //0x00005a78 .quad -4419164240055772162
	0x43, 0x4d, 0x12, 0xc4, 0xb8, 0xf6, 0x83, 0x05, //0x00005a80 .quad 397432465562684739
	0xfe, 0x52, 0x35, 0xf8, 0xeb, 0xf7, 0x56, 0xf3, //0x00005a88 .quad -912269281642327298
	0x4a, 0x70, 0x8b, 0x7a, 0x33, 0x7a, 0x72, 0xc3, //0x00005a90 .quad -4363290727450709942
	0xde, 0x53, 0x21, 0x7b, 0xf3, 0x5a, 0x16, 0x98, //0x00005a98 .quad -7487697328667536418
	0x5c, 0x4c, 0x2e, 0x59, 0xc0, 0x18, 0x4f, 0x74, //0x00005aa0 .quad 8380944645968776284
	0xd6, 0xa8, 0xe9, 0x59, 0xb0, 0xf1, 0x1b, 0xbe, //0x00005aa8 .quad -4747935642407032618
	0x73, 0xdf, 0x79, 0x6f, 0xf0, 0xde, 0x62, 0x11, //0x00005ab0 .quad 1252808770606194547
	0x0c, 0x13, 0x64, 0x70, 0x1c, 0xee, 0xa2, 0xed, //0x00005ab8 .quad -1323233534581402868
	0xa8, 0x2b, 0xac, 0x45, 0x56, 0xcb, 0xdd, 0x8a, //0x00005ac0 .quad -8440366555225904216
	0xe7, 0x8b, 0x3e, 0xc6, 0xd1, 0xd4, 0x85, 0x94, //0x00005ac8 .quad -7744549986754458649
	0x92, 0x36, 0x17, 0xd7, 0x2b, 0x3e, 0x95, 0x6d, //0x00005ad0 .quad 7896285879677171346
	0xe1, 0x2e, 0xce, 0x37, 0x06, 0x4a, 0xa7, 0xb9, //0x00005ad8 .quad -5069001465015685407
	0x37, 0x04, 0xdd, 0xcc, 0xb6, 0x8d, 0xfa, 0xc8, //0x00005ae0 .quad -3964700705685699529
	0x99, 0xba, 0xc1, 0xc5, 0x87, 0x1c, 0x11, 0xe8, //0x00005ae8 .quad -1724565812842218855
	0xa2, 0x22, 0x0a, 0x40, 0x92, 0x98, 0x9c, 0x1d, //0x00005af0 .quad 2133748077373825698
	0xa0, 0x14, 0x99, 0xdb, 0xd4, 0xb1, 0x0a, 0x91, //0x00005af8 .quad -7995382660667468640
	0x4b, 0xab, 0x0c, 0xd0, 0xb6, 0xbe, 0x03, 0x25, //0x00005b00 .quad 2667185096717282123
	0xc8, 0x59, 0x7f, 0x12, 0x4a, 0x5e, 0x4d, 0xb5, //0x00005b08 .quad -5382542307406947896
	0x1d, 0xd6, 0x0f, 0x84, 0x64, 0xae, 0x44, 0x2e, //0x00005b10 .quad 3333981370896602653
	0x3a, 0x30, 0x1f, 0x97, 0xdc, 0xb5, 0xa0, 0xe2, //0x00005b18 .quad -2116491865831296966
	0xd2, 0xe5, 0x89, 0xd2, 0xfe, 0xec, 0xea, 0x5c, //0x00005b20 .quad 6695424375237764562
	0x24, 0x7e, 0x73, 0xde, 0xa9, 0x71, 0xa4, 0x8d, //0x00005b28 .quad -8240336443785642460
	0x47, 0x5f, 0x2c, 0x87, 0x3e, 0xa8, 0x25, 0x74, //0x00005b30 .quad 8369280469047205703
	0xad, 0x5d, 0x10, 0x56, 0x14, 0x8e, 0x0d, 0xb1, //0x00005b38 .quad -5688734536304665171
	0x19, 0x77, 0xf7, 0x28, 0x4e, 0x12, 0x2f, 0xd1, //0x00005b40 .quad -3373457468973156583
	0x18, 0x75, 0x94, 0x6b, 0x99, 0xf1, 0x50, 0xdd, //0x00005b48 .quad -2499232151953443560
	0x6f, 0xaa, 0x9a, 0xd9, 0x70, 0x6b, 0xbd, 0x82, //0x00005b50 .quad -9025939945749304721
	0x2f, 0xc9, 0x3c, 0xe3, 0xff, 0x96, 0x52, 0x8a, //0x00005b58 .quad -8479549122611984081
	0x0b, 0x55, 0x01, 0x10, 0x4d, 0xc6, 0x6c, 0x63, //0x00005b60 .quad 7164319141522920715
	0x7b, 0xfb, 0x0b, 0xdc, 0xbf, 0x3c, 0xe7, 0xac, //0x00005b68 .quad -5987750384837592197
	0x4e, 0xaa, 0x01, 0x54, 0xe0, 0xf7, 0x47, 0x3c, //0x00005b70 .quad 4343712908476262990
	0x5a, 0xfa, 0x0e, 0xd3, 0xef, 0x0b, 0x21, 0xd8, //0x00005b78 .quad -2873001962619602342
	0x71, 0x0a, 0x81, 0x34, 0xec, 0xfa, 0xac, 0x65, //0x00005b80 .quad 7326506586225052273
	0x78, 0x5c, 0xe9, 0xe3, 0x75, 0xa7, 0x14, 0x87, //0x00005b88 .quad -8713155254278333320
	0x0d, 0x4d, 0xa1, 0x41, 0xa7, 0x39, 0x18, 0x7f, //0x00005b90 .quad 9158133232781315341
	0x96, 0xb3, 0xe3, 0x5c, 0x53, 0xd1, 0xd9, 0xa8, //0x00005b98 .quad -6279758049420528746
	0x50, 0xa0, 0x09, 0x12, 0x11, 0x48, 0xde, 0x1e, //0x00005ba0 .quad 2224294504121868368
	0x7c, 0xa0, 0x1c, 0x34, 0xa8, 0x45, 0x10, 0xd3, //0x00005ba8 .quad -3238011543348273028
	0x32, 0x04, 0x46, 0xab, 0x0a, 0xed, 0x4a, 0x93, //0x00005bb0 .quad -7833187971778608078
	0x4d, 0xe4, 0x91, 0x20, 0x89, 0x2b, 0xea, 0x83, //0x00005bb8 .quad -8941286242233752499
	0x3f, 0x85, 0x17, 0x56, 0x4d, 0xa8, 0x1d, 0xf8, //0x00005bc0 .quad -568112927868484289
	0x60, 0x5d, 0xb6, 0x68, 0x6b, 0xb6, 0xe4, 0xa4, //0x00005bc8 .quad -6564921784364802720
	0x8e, 0x66, 0x9d, 0xab, 0x60, 0x12, 0x25, 0x36, //0x00005bd0 .quad 3901544858591782542
	0xb9, 0xf4, 0xe3, 0x42, 0x06, 0xe4, 0x1d, 0xce, //0x00005bd8 .quad -3594466212028615495
	0x19, 0x60, 0x42, 0x6b, 0x7c, 0x2b, 0xd7, 0xc1, //0x00005be0 .quad -4479063491021217767
	0xf3, 0x78, 0xce, 0xe9, 0x83, 0xae, 0xd2, 0x80, //0x00005be8 .quad -9164070410158966541
	0x1f, 0xf8, 0x12, 0x86, 0x5b, 0xf6, 0x4c, 0xb2, //0x00005bf0 .quad -5598829363776522209
	0x30, 0x17, 0x42, 0xe4, 0x24, 0x5a, 0x07, 0xa1, //0x00005bf8 .quad -6843401994271320272
	0x27, 0xb6, 0x97, 0x67, 0xf2, 0x33, 0xe0, 0xde, //0x00005c00 .quad -2386850686293264857
	0xfc, 0x9c, 0x52, 0x1d, 0xae, 0x30, 0x49, 0xc9, //0x00005c08 .quad -3942566474411762436
	0xb1, 0xa3, 0x7d, 0x01, 0xef, 0x40, 0x98, 0x16, //0x00005c10 .quad 1628122660560806833
	0x3c, 0x44, 0xa7, 0xa4, 0xd9, 0x7c, 0x9b, 0xfb, //0x00005c18 .quad -316522074587315140
	0x4e, 0x86, 0xee, 0x60, 0x95, 0x28, 0x1f, 0x8e, //0x00005c20 .quad -8205795374004271538
	0xa5, 0x8a, 0xe8, 0x06, 0x08, 0x2e, 0x41, 0x9d, //0x00005c28 .quad -7115355324258153819
	0xe2, 0x27, 0x2a, 0xb9, 0xba, 0xf2, 0xa6, 0xf1, //0x00005c30 .quad -1033872180650563614
	0x4e, 0xad, 0xa2, 0x08, 0x8a, 0x79, 0x91, 0xc4, //0x00005c38 .quad -4282508136895304370
	0xdb, 0xb1, 0x74, 0x67, 0x69, 0xaf, 0x10, 0xae, //0x00005c40 .quad -5904026244240592421
	0xa2, 0x58, 0xcb, 0x8a, 0xec, 0xd7, 0xb5, 0xf5, //0x00005c48 .quad -741449152691742558
	0x29, 0xef, 0xa8, 0xe0, 0xa1, 0x6d, 0xca, 0xac, //0x00005c50 .quad -5995859411864064215
	0x65, 0x17, 0xbf, 0xd6, 0xf3, 0xa6, 0x91, 0x99, //0x00005c58 .quad -7380934748073420955
	0xf3, 0x2a, 0xd3, 0x58, 0x0a, 0x09, 0xfd, 0x17, //0x00005c60 .quad 1728547772024695539
	0x3f, 0xdd, 0x6e, 0xcc, 0xb0, 0x10, 0xf6, 0xbf, //0x00005c68 .quad -4614482416664388289
	0xb0, 0xf5, 0x07, 0xef, 0x4c, 0x4b, 0xfc, 0xdd, //0x00005c70 .quad -2451001303396518480
	0x8e, 0x94, 0x8a, 0xff, 0xdc, 0x94, 0xf3, 0xef, //0x00005c78 .quad -1156417002403097458
	0x8e, 0xf9, 0x64, 0x15, 0x10, 0xaf, 0xbd, 0x4a, //0x00005c80 .quad 5385653213018257806
	0xd9, 0x9c, 0xb6, 0x1f, 0x0a, 0x3d, 0xf8, 0x95, //0x00005c88 .quad -7640289654143017767
	0xf1, 0x37, 0xbe, 0x1a, 0xd4, 0x1a, 0x6d, 0x9d, //0x00005c90 .quad -7102991539009341455
	0x0f, 0x44, 0xa4, 0xa7, 0x4c, 0x4c, 0x76, 0xbb, //0x00005c98 .quad -4938676049251384305
	0xed, 0xc5, 0x6d, 0x21, 0x89, 0x61, 0xc8, 0x84, //0x00005ca0 .quad -8878739423761676819
	0x13, 0x55, 0x8d, 0xd1, 0x5f, 0xdf, 0x53, 0xea, //0x00005ca8 .quad -1561659043136842477
	0xb4, 0x9b, 0xe4, 0xb4, 0xf5, 0x3c, 0xfd, 0x32, //0x00005cb0 .quad 3674159897003727796
	0x2c, 0x55, 0xf8, 0xe2, 0x9b, 0x6b, 0x74, 0x92, //0x00005cb8 .quad -7893565929601608404
	0xa1, 0xc2, 0x1d, 0x22, 0x33, 0x8c, 0xbc, 0x3f, //0x00005cc0 .quad 4592699871254659745
	0x77, 0x6a, 0xb6, 0xdb, 0x82, 0x86, 0x11, 0xb7, //0x00005cc8 .quad -5255271393574622601
	0x4a, 0x33, 0xa5, 0xea, 0x3f, 0xaf, 0xab, 0x0f, //0x00005cd0 .quad 1129188820640936778
	0x15, 0x05, 0xa4, 0x92, 0x23, 0xe8, 0xd5, 0xe4, //0x00005cd8 .quad -1957403223540890347
	0x0e, 0x40, 0xa7, 0xf2, 0x87, 0x4d, 0xcb, 0x29, //0x00005ce0 .quad 3011586022114279438
	0x2d, 0x83, 0xa6, 0x3b, 0x16, 0xb1, 0x05, 0x8f, //0x00005ce8 .quad -8140906042354138323
	0x12, 0x10, 0x51, 0xef, 0xe9, 0x20, 0x3e, 0x74, //0x00005cf0 .quad 8376168546070237202
	0xf8, 0x23, 0x90, 0xca, 0x5b, 0x1d, 0xc7, 0xb2, //0x00005cf8 .quad -5564446534515285000
	0x16, 0x54, 0x25, 0x6b, 0x24, 0xa9, 0x4d, 0x91, //0x00005d00 .quad -7976533391121755114
	0xf6, 0x2c, 0x34, 0xbd, 0xb2, 0xe4, 0x78, 0xdf, //0x00005d08 .quad -2343872149716718346
	0x8e, 0x54, 0xf7, 0xc2, 0xb6, 0x89, 0xd0, 0x1a, //0x00005d10 .quad 1932195658189984910
	0x1a, 0x9c, 0x40, 0xb6, 0xef, 0x8e, 0xab, 0x8b, //0x00005d18 .quad -8382449121214030822
	0xb1, 0x29, 0xb5, 0x73, 0x24, 0xac, 0x84, 0xa1, //0x00005d20 .quad -6808127464117294671
	0x20, 0xc3, 0xd0, 0xa3, 0xab, 0x72, 0x96, 0xae, //0x00005d28 .quad -5866375383090150624
	0x1e, 0x74, 0xa2, 0x90, 0x2d, 0xd7, 0xe5, 0xc9, //0x00005d30 .quad -3898473311719230434
	0xe8, 0xf3, 0xc4, 0x8c, 0x56, 0x0f, 0x3c, 0xda, //0x00005d38 .quad -2721283210435300376
	0x92, 0x88, 0x65, 0x7a, 0x7c, 0xa6, 0x2f, 0x7e, //0x00005d40 .quad 9092669226243950738
	0x71, 0x18, 0xfb, 0x17, 0x96, 0x89, 0x65, 0x88, //0x00005d48 .quad -8618331034163144591
	0xb7, 0xea, 0xfe, 0x98, 0x1b, 0x90, 0xbb, 0xdd, //0x00005d50 .quad -2469221522477225289
	0x8d, 0xde, 0xf9, 0x9d, 0xfb, 0xeb, 0x7e, 0xaa, //0x00005d58 .quad -6161227774276542835
	0x65, 0xa5, 0x3e, 0x7f, 0x22, 0x74, 0x2a, 0x55, //0x00005d60 .quad 6136845133758244197
	0x31, 0x56, 0x78, 0x85, 0xfa, 0xa6, 0x1e, 0xd5, //0x00005d68 .quad -3089848699418290639
	0x5f, 0x27, 0x87, 0x8f, 0x95, 0x88, 0x3a, 0xd5, //0x00005d70 .quad -3082000819042179233
	0xde, 0x35, 0x6b, 0x93, 0x5c, 0x28, 0x33, 0x85, //0x00005d78 .quad -8848684464777513506
	0x37, 0xf1, 0x68, 0xf3, 0xba, 0x2a, 0x89, 0x8a, //0x00005d80 .quad -8464187042230111945
	0x56, 0x03, 0x46, 0xb8, 0x73, 0xf2, 0x7f, 0xa6, //0x00005d88 .quad -6449169562544503978
	0x85, 0x2d, 0x43, 0xb0, 0x69, 0x75, 0x2b, 0x2d, //0x00005d90 .quad 3254824252494523781
	0x2c, 0x84, 0x57, 0xa6, 0x10, 0xef, 0x1f, 0xd0, //0x00005d98 .quad -3449775934753242068
	0x73, 0xfc, 0x29, 0x0e, 0x62, 0x29, 0x3b, 0x9c, //0x00005da0 .quad -7189106879045698445
	0x9b, 0xb2, 0xf6, 0x67, 0x6a, 0xf5, 0x13, 0x82, //0x00005da8 .quad -9073638986861858149
	0x8f, 0x7b, 0xb4, 0x91, 0xba, 0xf3, 0x49, 0x83, //0x00005db0 .quad -8986383598807123057
	0x42, 0x5f, 0xf4, 0x01, 0xc5, 0xf2, 0x98, 0xa2, //0x00005db8 .quad -6730362715149934782
	0x73, 0x9a, 0x21, 0x36, 0xa9, 0x70, 0x1c, 0x24, //0x00005dc0 .quad 2602078556773259891
	0x13, 0x77, 0x71, 0x42, 0x76, 0x2f, 0x3f, 0xcb, //0x00005dc8 .quad -3801267375510030573
	0x10, 0x01, 0xaa, 0x83, 0xd3, 0x8c, 0x23, 0xed, //0x00005dd0 .quad -1359087822460813040
	0xd7, 0xd4, 0x0d, 0xd3, 0x53, 0xfb, 0x0e, 0xfe, //0x00005dd8 .quad -139898200960150313
	0xaa, 0x40, 0x4a, 0x32, 0x04, 0x38, 0x36, 0xf4, //0x00005de0 .quad -849429889038008150
	0x06, 0xa5, 0xe8, 0x63, 0x14, 0x5d, 0xc9, 0x9e, //0x00005de8 .quad -7004965403241175802
	0xd5, 0xd0, 0xdc, 0x3e, 0x05, 0xc6, 0x43, 0xb1, //0x00005df0 .quad -5673473379724898091
	0x48, 0xce, 0xe2, 0x7c, 0x59, 0xb4, 0x7b, 0xc6, //0x00005df8 .quad -4144520735624081848
	0x0a, 0x05, 0x94, 0x8e, 0x86, 0xb7, 0x94, 0xdd, //0x00005e00 .quad -2480155706228734710
	0xda, 0x81, 0x1b, 0xdc, 0x6f, 0xa1, 0x1a, 0xf8, //0x00005e08 .quad -568964901102714406
	0x26, 0x83, 0x1c, 0x19, 0xb4, 0xf2, 0x7c, 0xca, //0x00005e10 .quad -3855940325606653146
	0x28, 0x31, 0x91, 0xe9, 0xe5, 0xa4, 0x10, 0x9b, //0x00005e18 .quad -7273132090830278360
	0xf0, 0xa3, 0x63, 0x1f, 0x61, 0x2f, 0x1c, 0xfd, //0x00005e20 .quad -208239388580928528
	0x72, 0x7d, 0xf5, 0x63, 0x1f, 0xce, 0xd4, 0xc1, //0x00005e28 .quad -4479729095110460046
	0xec, 0x8c, 0x3c, 0x67, 0x39, 0x3b, 0x63, 0xbc, //0x00005e30 .quad -4871985254153548564
	0xcf, 0xdc, 0xf2, 0x3c, 0xa7, 0x01, 0x4a, 0xf2, //0x00005e38 .quad -987975350460687153
	0x13, 0xd8, 0x85, 0xe0, 0x03, 0x05, 0xbe, 0xd5, //0x00005e40 .quad -3044990783845967853
	0x01, 0xca, 0x17, 0x86, 0x08, 0x41, 0x6e, 0x97, //0x00005e48 .quad -7535013621679011327
	0x18, 0x4e, 0xa7, 0xd8, 0x44, 0x86, 0x2d, 0x4b, //0x00005e50 .quad 5417133557047315992
	0x82, 0xbc, 0x9d, 0xa7, 0x4a, 0xd1, 0x49, 0xbd, //0x00005e58 .quad -4807081008671376254
	0x9e, 0x21, 0xd1, 0x0e, 0xd6, 0xe7, 0xf8, 0xdd, //0x00005e60 .quad -2451955090545630818
	0xa2, 0x2b, 0x85, 0x51, 0x9d, 0x45, 0x9c, 0xec, //0x00005e68 .quad -1397165242411832414
	0x03, 0xb5, 0x42, 0xc9, 0xe5, 0x90, 0xbb, 0xca, //0x00005e70 .quad -3838314940804713213
	0x45, 0x3b, 0xf3, 0x52, 0x82, 0xab, 0xe1, 0x93, //0x00005e78 .quad -7790757304148477115
	0x43, 0x62, 0x93, 0x3b, 0x1f, 0x75, 0x6a, 0x3d, //0x00005e80 .quad 4425478360848884291
	0x17, 0x0a, 0xb0, 0xe7, 0x62, 0x16, 0xda, 0xb8, //0x00005e88 .quad -5126760611758208489
	0xd4, 0x3a, 0x78, 0x0a, 0x67, 0x12, 0xc5, 0x0c, //0x00005e90 .quad 920161932633717460
	0x9d, 0x0c, 0x9c, 0xa1, 0xfb, 0x9b, 0x10, 0xe7, //0x00005e98 .quad -1796764746270372707
	0xc5, 0x24, 0x8b, 0x66, 0x80, 0x2b, 0xfb, 0x27, //0x00005ea0 .quad 2880944217109767365
	0xe2, 0x87, 0x01, 0x45, 0x7d, 0x61, 0x6a, 0x90, //0x00005ea8 .quad -8040506994060064798
	0xf6, 0xed, 0x2d, 0x80, 0x60, 0xf6, 0xf9, 0xb1, //0x00005eb0 .quad -5622191765467566602
	0xda, 0xe9, 0x41, 0x96, 0xdc, 0xf9, 0x84, 0xb4, //0x00005eb8 .quad -5438947724147693094
	0x73, 0x69, 0x39, 0xa0, 0xf8, 0x73, 0x78, 0x5e, //0x00005ec0 .quad 6807318348447705459
	0x51, 0x64, 0xd2, 0xbb, 0x53, 0x38, 0xa6, 0xe1, //0x00005ec8 .quad -2186998636757228463
	0xe8, 0xe1, 0x23, 0x64, 0x7b, 0x48, 0x0b, 0xdb, //0x00005ed0 .quad -2662955059861265944
	0xb2, 0x7e, 0x63, 0x55, 0x34, 0xe3, 0x07, 0x8d, //0x00005ed8 .quad -8284403175614349646
	0x62, 0xda, 0x2c, 0x3d, 0x9a, 0x1a, 0xce, 0x91, //0x00005ee0 .quad -7940379843253970334
	0x5f, 0x5e, 0xbc, 0x6a, 0x01, 0xdc, 0x49, 0xb0, //0x00005ee8 .quad -5743817951090549153
	0xfb, 0x10, 0x78, 0xcc, 0x40, 0xa1, 0x41, 0x76, //0x00005ef0 .quad 8521269269642088699
	0xf7, 0x75, 0x6b, 0xc5, 0x01, 0x53, 0x5c, 0xdc, //0x00005ef8 .quad -2568086420435798537
	0x9d, 0x0a, 0xcb, 0x7f, 0xc8, 0x04, 0xe9, 0xa9, //0x00005f00 .quad -6203421752542164323
	0xba, 0x29, 0x63, 0x1b, 0xe1, 0xb3, 0xb9, 0x89, //0x00005f08 .quad -8522583040413455942
	0x44, 0xcd, 0xbd, 0x9f, 0xfa, 0x45, 0x63, 0x54, //0x00005f10 .quad 6080780864604458308
	0x29, 0xf4, 0x3b, 0x62, 0xd9, 0x20, 0x28, 0xac, //0x00005f18 .quad -6041542782089432023
	0x95, 0x40, 0xad, 0x47, 0x79, 0x17, 0x7c, 0xa9, //0x00005f20 .quad -6234081974526590827
	0x33, 0xf1, 0xca, 0xba, 0x0f, 0x29, 0x32, 0xd7, //0x00005f28 .quad -2940242459184402125
	0x5d, 0x48, 0xcc, 0xcc, 0xab, 0x8e, 0xed, 0x49, //0x00005f30 .quad 5327070802775656541
	0xc0, 0xd6, 0xbe, 0xd4, 0xa9, 0x59, 0x7f, 0x86, //0x00005f38 .quad -8755180564631333184
	0x74, 0x5a, 0xff, 0xbf, 0x56, 0xf2, 0x68, 0x5c, //0x00005f40 .quad 6658838503469570676
	0x70, 0x8c, 0xee, 0x49, 0x14, 0x30, 0x1f, 0xa8, //0x00005f48 .quad -6332289687361778576
	0x11, 0x31, 0xff, 0x6f, 0xec, 0x2e, 0x83, 0x73, //0x00005f50 .quad 8323548129336963345
	0x8c, 0x2f, 0x6a, 0x5c, 0x19, 0xfc, 0x26, 0xd2, //0x00005f58 .quad -3303676090774835316
	0xab, 0x7e, 0xff, 0xc5, 0x53, 0xfd, 0x31, 0xc8, //0x00005f60 .quad -4021154456019173717
	0xb7, 0x5d, 0xc2, 0xd9, 0x8f, 0x5d, 0x58, 0x83, //0x00005f68 .quad -8982326584375353929
	0x55, 0x5e, 0x7f, 0xb7, 0xa8, 0x7c, 0x3e, 0xba, //0x00005f70 .quad -5026443070023967147
	0x25, 0xf5, 0x32, 0xd0, 0xf3, 0x74, 0x2e, 0xa4, //0x00005f78 .quad -6616222212041804507
	0xeb, 0x35, 0x5f, 0xe5, 0xd2, 0x1b, 0xce, 0x28, //0x00005f80 .quad 2940318199324816875
	0x6f, 0xb2, 0x3f, 0xc4, 0x30, 0x12, 0x3a, 0xcd, //0x00005f88 .quad -3658591746624867729
	0xb3, 0x81, 0x5b, 0xcf, 0x63, 0xd1, 0x80, 0x79, //0x00005f90 .quad 8755227902219092403
	0x85, 0xcf, 0xa7, 0x7a, 0x5e, 0x4b, 0x44, 0x80, //0x00005f98 .quad -9204148869281624187
	0x1f, 0x62, 0x32, 0xc3, 0xbc, 0x05, 0xe1, 0xd7, //0x00005fa0 .quad -2891023177508298209
	0x66, 0xc3, 0x51, 0x19, 0x36, 0x5e, 0x55, 0xa0, //0x00005fa8 .quad -6893500068174642330
	0xa7, 0xfa, 0xfe, 0xf3, 0x2b, 0x47, 0xd9, 0x8d, //0x00005fb0 .quad -8225464990312760665
	0x40, 0x34, 0xa6, 0x9f, 0xc3, 0xb5, 0x6a, 0xc8, //0x00005fb8 .quad -4005189066790915008
	0x51, 0xb9, 0xfe, 0xf0, 0xf6, 0x98, 0x4f, 0xb1, //0x00005fc0 .quad -5670145219463562927
	0x50, 0xc1, 0x8f, 0x87, 0x34, 0x63, 0x85, 0xfa, //0x00005fc8 .quad -394800315061255856
	0xd3, 0x33, 0x9f, 0x56, 0x9a, 0xbf, 0xd1, 0x6e, //0x00005fd0 .quad 7985374283903742931
	0xd2, 0xd8, 0xb9, 0xd4, 0x00, 0x5e, 0x93, 0x9c, //0x00005fd8 .quad -7164279224554366766
	0xc8, 0x00, 0x47, 0xec, 0x80, 0x2f, 0x86, 0x0a, //0x00005fe0 .quad 758345818024902856
	0x07, 0x4f, 0xe8, 0x09, 0x81, 0x35, 0xb8, 0xc3, //0x00005fe8 .quad -4343663012265570553
	0xfa, 0xc0, 0x58, 0x27, 0x61, 0xbb, 0x27, 0xcd, //0x00005ff0 .quad -3663753745896259334
	0xc8, 0x62, 0x62, 0x4c, 0xe1, 0x42, 0xa6, 0xf4, //0x00005ff8 .quad -817892746904575288
	0x9c, 0x78, 0x97, 0xb8, 0x1c, 0xd5, 0x38, 0x80, //0x00006000 .quad -9207375118826243940
	0xbd, 0x7d, 0xbd, 0xcf, 0xcc, 0xe9, 0xe7, 0x98, //0x00006008 .quad -7428711994456441411
	0xc3, 0x56, 0xbd, 0xe6, 0x63, 0x0a, 0x47, 0xe0, //0x00006010 .quad -2285846861678029117
	0x2c, 0xdd, 0xac, 0x03, 0x40, 0xe4, 0x21, 0xbf, //0x00006018 .quad -4674203974643163860
	0x74, 0xac, 0x6c, 0xe0, 0xfc, 0xcc, 0x58, 0x18, //0x00006020 .quad 1754377441329851508
	0x78, 0x14, 0x98, 0x04, 0x50, 0x5d, 0xea, 0xee, //0x00006028 .quad -1231068949876566920
	0xc8, 0xeb, 0x43, 0x0c, 0x1e, 0x80, 0x37, 0x0f, //0x00006030 .quad 1096485900831157192
	0xcb, 0x0c, 0xdf, 0x02, 0x52, 0x7a, 0x52, 0x95, //0x00006038 .quad -7686947121313936181
	0xba, 0xe6, 0x54, 0x8f, 0x25, 0x60, 0x05, 0xd3, //0x00006040 .quad -3241078642388441414
	0xfd, 0xcf, 0x96, 0x83, 0xe6, 0x18, 0xa7, 0xba, //0x00006048 .quad -4996997883215032323
	0x69, 0x20, 0x2a, 0xf3, 0x2e, 0xb8, 0xc6, 0x47, //0x00006050 .quad 5172023733869224041
	0xfd, 0x83, 0x7c, 0x24, 0x20, 0xdf, 0x50, 0xe9, //0x00006058 .quad -1634561335591402499
	0x41, 0x54, 0xfa, 0x57, 0x1d, 0x33, 0xdc, 0x4c, //0x00006060 .quad 5538357842881958977
	0x7e, 0xd2, 0xcd, 0x16, 0x74, 0x8b, 0xd2, 0x91, //0x00006068 .quad -7939129862385708418
	0x52, 0xe9, 0xf8, 0xad, 0xe4, 0x3f, 0x13, 0xe0, //0x00006070 .quad -2300424733252327086
	0x1d, 0x47, 0x81, 0x1c, 0x51, 0x2e, 0x47, 0xb6, //0x00006078 .quad -5312226309554747619
	0xa6, 0x23, 0x77, 0xd9, 0xdd, 0x0f, 0x18, 0x58, //0x00006080 .quad 6347841120289366950
	0xe5, 0x98, 0xa1, 0x63, 0xe5, 0xf9, 0xd8, 0xe3, //0x00006088 .quad -2028596868516046619
	0x48, 0x76, 0xea, 0xa7, 0xea, 0x09, 0x0f, 0x57, //0x00006090 .quad 6273243709394548296
	0x8f, 0xff, 0x44, 0x5e, 0x2f, 0x9c, 0x67, 0x8e, //0x00006098 .quad -8185402070463610993
	0xda, 0x13, 0xe5, 0x51, 0x65, 0xcc, 0xd2, 0x2c, //0x000060a0 .quad 3229868618315797466
	0x73, 0x3f, 0xd6, 0x35, 0x3b, 0x83, 0x01, 0xb2, //0x000060a8 .quad -5620066569652125837
	0xd1, 0x58, 0x5e, 0xa6, 0x7e, 0x7f, 0x07, 0xf8, //0x000060b0 .quad -574350245532641071
	0x4f, 0xcf, 0x4b, 0x03, 0x0a, 0xe4, 0x81, 0xde, //0x000060b8 .quad -2413397193637769393
	0x82, 0xf7, 0xfa, 0x27, 0xaf, 0xaf, 0x04, 0xfb, //0x000060c0 .quad -358968903457900670
	0x91, 0x61, 0x0f, 0x42, 0x86, 0x2e, 0x11, 0x8b, //0x000060c8 .quad -8425902273664687727
	0x63, 0xb5, 0xf9, 0xf1, 0x9a, 0xdb, 0xc5, 0x79, //0x000060d0 .quad 8774660907532399971
	0xf6, 0x39, 0x93, 0xd2, 0x27, 0x7a, 0xd5, 0xad, //0x000060d8 .quad -5920691823653471754
	0xbc, 0x22, 0x78, 0xae, 0x81, 0x52, 0x37, 0x18, //0x000060e0 .quad 1744954097560724156
	0x74, 0x08, 0x38, 0xc7, 0xb1, 0xd8, 0x4a, 0xd9, //0x000060e8 .quad -2789178761139451788
	0xb5, 0x15, 0x0b, 0x0d, 0x91, 0x93, 0x22, 0x8f, //0x000060f0 .quad -8132775725879323211
	0x48, 0x05, 0x83, 0x1c, 0x6f, 0xc7, 0xce, 0x87, //0x000060f8 .quad -8660765753353239224
	0x22, 0xdb, 0x4d, 0x50, 0x75, 0x38, 0xeb, 0xb2, //0x00006100 .quad -5554283638921766110
	0x9a, 0xc6, 0xa3, 0xe3, 0x4a, 0x79, 0xc2, 0xa9, //0x00006108 .quad -6214271173264161126
	0xeb, 0x51, 0x61, 0xa4, 0x92, 0x06, 0xa6, 0x5f, //0x00006110 .quad 6892203506629956075
	0x41, 0xb8, 0x8c, 0x9c, 0x9d, 0x17, 0x33, 0xd4, //0x00006118 .quad -3156152948152813503
	0x33, 0xd3, 0xbc, 0xa6, 0x1b, 0xc4, 0xc7, 0xdb, //0x00006120 .quad -2609901835997359309
	0x28, 0xf3, 0xd7, 0x81, 0xc2, 0xee, 0x9f, 0x84, //0x00006128 .quad -8890124620236590296
	0x00, 0x08, 0x6c, 0x90, 0x22, 0xb5, 0xb9, 0x12, //0x00006130 .quad 1349308723430688768
	0xf3, 0xef, 0x4d, 0x22, 0x73, 0xea, 0xc7, 0xa5, //0x00006138 .quad -6500969756868349965
	0x00, 0x0a, 0x87, 0x34, 0x6b, 0x22, 0x68, 0xd7, //0x00006140 .quad -2925050114139026944
	0xef, 0x6b, 0xe1, 0xea, 0x0f, 0xe5, 0x39, 0xcf, //0x00006148 .quad -3514526177658049553
	0x40, 0x66, 0xd4, 0x00, 0x83, 0x15, 0xa1, 0xe6, //0x00006150 .quad -1828156321336891840
	0x75, 0xe3, 0xcc, 0xf2, 0x29, 0x2f, 0x84, 0x81, //0x00006158 .quad -9114107888677362827
	0xd0, 0x7f, 0x09, 0xc1, 0xe3, 0x5a, 0x49, 0x60, //0x00006160 .quad 6938176635183661008
	0x53, 0x1c, 0x80, 0x6f, 0xf4, 0x3a, 0xe5, 0xa1, //0x00006168 .quad -6780948842419315629
	0xc4, 0xdf, 0x4b, 0xb1, 0x9c, 0xb1, 0x5b, 0x38, //0x00006170 .quad 4061034775552188356
	0x68, 0x23, 0x60, 0x8b, 0xb1, 0x89, 0x5e, 0xca, //0x00006178 .quad -3864500034596756632
	0xb5, 0xd7, 0x9e, 0xdd, 0x03, 0x9e, 0x72, 0x46, //0x00006180 .quad 5076293469440235445
	0x42, 0x2c, 0x38, 0xee, 0x1d, 0x2c, 0xf6, 0xfc, //0x00006188 .quad -218939024818557886
	0xd1, 0x46, 0x83, 0x6a, 0xc2, 0xa2, 0x07, 0x6c, //0x00006190 .quad 7784369436827535057
	0xa9, 0x1b, 0xe3, 0xb4, 0x92, 0xdb, 0x19, 0x9e, //0x00006198 .quad -7054365918152680535
	0x85, 0x18, 0x24, 0x05, 0x73, 0x8b, 0x09, 0xc7, //0x000061a0 .quad -4104596259247744891
	0x93, 0xe2, 0x1b, 0x62, 0x77, 0x52, 0xa0, 0xc5, //0x000061a8 .quad -4206271379263462765
	0xa7, 0x1e, 0x6d, 0xc6, 0x4f, 0xee, 0xcb, 0xb8, //0x000061b0 .quad -5130745324059681113
	0x38, 0xdb, 0xa2, 0x3a, 0x15, 0x67, 0x08, 0xf7, //0x000061b8 .quad -646153205651940552
	0x28, 0x33, 0x04, 0xdc, 0xf1, 0x74, 0x7f, 0x73, //0x000061c0 .quad 8322499218531169064
	0x03, 0xc9, 0xa5, 0x44, 0x6d, 0x40, 0x65, 0x9a, //0x000061c8 .quad -7321374781173544701
	0xf2, 0x3f, 0x05, 0x53, 0x2e, 0x52, 0x5f, 0x50, //0x000061d0 .quad 5791438004736573426
	0x44, 0x3b, 0xcf, 0x95, 0x88, 0x90, 0xfe, 0xc0, //0x000061d8 .quad -4540032458039542972
	0xef, 0x8f, 0xc6, 0xe7, 0xb9, 0x26, 0x77, 0x64, //0x000061e0 .quad 7239297505920716783
	0x15, 0x0a, 0x43, 0xbb, 0xaa, 0x34, 0x3e, 0xf1, //0x000061e8 .quad -1063354554122040811
	0xf5, 0x19, 0xdc, 0x30, 0x34, 0x78, 0xca, 0x5e, //0x000061f0 .quad 6830403950414141941
	0x4d, 0xe6, 0x09, 0xb5, 0xea, 0xe0, 0xc6, 0x96, //0x000061f8 .quad -7582125623967357363
	0x72, 0x20, 0x13, 0x3d, 0x41, 0x16, 0x7d, 0xb6, //0x00006200 .quad -5297053117264486286
	0xe0, 0x5f, 0x4c, 0x62, 0x25, 0x99, 0x78, 0xbc, //0x00006208 .quad -4865971011531808800
	0x8f, 0xe8, 0x57, 0x8c, 0xd1, 0x5b, 0x1c, 0xe4, //0x00006210 .quad -2009630378153219953
	0xd8, 0x77, 0xdf, 0xba, 0x6e, 0xbf, 0x96, 0xeb, //0x00006218 .quad -1470777745987373096
	0x59, 0xf1, 0xb6, 0xf7, 0x62, 0xb9, 0x91, 0x8e, //0x00006220 .quad -8173548013986844327
	0xe7, 0xaa, 0xcb, 0x34, 0xa5, 0x37, 0x3e, 0x93, //0x00006228 .quad -7836765118883190041
	0xb0, 0xad, 0xa4, 0xb5, 0xbb, 0x27, 0x36, 0x72, //0x00006230 .quad 8229809056225996208
	0xa1, 0x95, 0xfe, 0x81, 0x8e, 0xc5, 0x0d, 0xb8, //0x00006238 .quad -5184270380176599647
	0x1c, 0xd9, 0x0d, 0xa3, 0xaa, 0xb1, 0xc3, 0xce, //0x00006240 .quad -3547796734999668452
	0x09, 0x3b, 0x7e, 0x22, 0xf2, 0x36, 0x11, 0xe6, //0x00006248 .quad -1868651956793361655
	0xb1, 0xa7, 0xe8, 0xa5, 0x0a, 0x4f, 0x3a, 0x21, //0x00006250 .quad 2394313059052595121
	0xe6, 0xe4, 0x8e, 0x55, 0x57, 0xc2, 0xca, 0x8f, //0x00006258 .quad -8085436500636932890
	0x9d, 0xd1, 0x62, 0x4f, 0xcd, 0xe2, 0x88, 0xa9, //0x00006260 .quad -6230480713039031907
	0x1f, 0x9e, 0xf2, 0x2a, 0xed, 0x72, 0xbd, 0xb3, //0x00006268 .quad -5495109607368778209
	0x05, 0x86, 0x3b, 0xa3, 0x80, 0x1b, 0xeb, 0x93, //0x00006270 .quad -7788100891298789883
	0xa7, 0x45, 0xaf, 0x75, 0xa8, 0xcf, 0xac, 0xe0, //0x00006278 .quad -2257200990783584857
	0xc3, 0x33, 0x05, 0x66, 0x30, 0xf1, 0x72, 0xbc, //0x00006280 .quad -4867563057061743677
	0x88, 0x8b, 0x8d, 0x49, 0xc9, 0x01, 0x6c, 0x8c, //0x00006288 .quad -8328279646880822392
	0xb4, 0x80, 0x86, 0x7f, 0x7c, 0xad, 0x8f, 0xeb, //0x00006290 .quad -1472767802899791692
	0x6a, 0xee, 0xf0, 0x9b, 0x3b, 0x02, 0x87, 0xaf, //0x00006298 .quad -5798663540173640086
	0xe1, 0x20, 0x68, 0x9f, 0xdb, 0x98, 0x73, 0xa6, //0x000062a0 .quad -6452645772052127519
	0x05, 0x2a, 0xed, 0x82, 0xca, 0xc2, 0x68, 0xdb, //0x000062a8 .quad -2636643406789662203
	0x8c, 0x14, 0xa1, 0x43, 0x89, 0x3f, 0x08, 0x88, //0x000062b0 .quad -8644589625959967604
	0x43, 0x3a, 0xd4, 0x91, 0xbe, 0x79, 0x21, 0x89, //0x000062b8 .quad -8565431156884620733
	0xb0, 0x59, 0x89, 0x94, 0x6b, 0x4f, 0x0a, 0x6a, //0x000062c0 .quad 7641007041259592112
	0xd4, 0x48, 0x49, 0x36, 0x2e, 0xd8, 0x69, 0xab, //0x000062c8 .quad -6095102927678388012
	0x1c, 0xb0, 0xab, 0x79, 0x46, 0xe3, 0x8c, 0x84, //0x000062d0 .quad -8895485272135061476
	0x09, 0x9b, 0xdb, 0xc3, 0x39, 0x4e, 0x44, 0xd6, //0x000062d8 .quad -3007192641170597111
	0x11, 0x4e, 0x0b, 0x0c, 0x0c, 0x0e, 0xd8, 0xf2, //0x000062e0 .quad -947992276657025519
	0xe5, 0x40, 0x69, 0x1a, 0xe4, 0xb0, 0xea, 0x85, //0x000062e8 .quad -8797024428372705051
	0x95, 0x21, 0x0e, 0x0f, 0x8f, 0x11, 0x8e, 0x6f, //0x000062f0 .quad 8038381691033493909
	0x1f, 0x91, 0x03, 0x21, 0x1d, 0x5d, 0x65, 0xa7, //0x000062f8 .quad -6384594517038493409
	0xfb, 0xa9, 0xd1, 0xd2, 0xf2, 0x95, 0x71, 0x4b, //0x00006300 .quad 5436291095364479483
	0x67, 0x75, 0x44, 0x69, 0x64, 0xb4, 0x3e, 0xd1, //0x00006308 .quad -3369057127870728857
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006310 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .space 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	//0x00006320 .p2align 4, 0x00
	//0x00006320 _POW_TAB
	0x01, 0x00, 0x00, 0x00, //0x00006320 .long 1
	0x03, 0x00, 0x00, 0x00, //0x00006324 .long 3
	0x06, 0x00, 0x00, 0x00, //0x00006328 .long 6
	0x09, 0x00, 0x00, 0x00, //0x0000632c .long 9
	0x0d, 0x00, 0x00, 0x00, //0x00006330 .long 13
	0x10, 0x00, 0x00, 0x00, //0x00006334 .long 16
	0x13, 0x00, 0x00, 0x00, //0x00006338 .long 19
	0x17, 0x00, 0x00, 0x00, //0x0000633c .long 23
	0x1a, 0x00, 0x00, 0x00, //0x00006340 .long 26
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006344 .p2align 4, 0x00
	//0x00006350 _LSHIFT_TAB
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006350 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .space 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006360 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .space 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006370 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .space 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006380 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .space 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006390 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .space 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000063a0 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .space 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000063b0 QUAD $0x0000000000000000  // .space 8, '\x00\x00\x00\x00\x00\x00\x00\x00'
	0x01, 0x00, 0x00, 0x00, //0x000063b8 .long 1
	0x35, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000063bc QUAD $0x0000000000000035; QUAD $0x0000000000000000  // .asciz 16, '5\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000063cc QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000063dc QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000063ec QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000063fc QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x0000640c QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, //0x0000641c LONG $0x00000000  // .asciz 4, '\x00\x00\x00\x00'
	0x01, 0x00, 0x00, 0x00, //0x00006420 .long 1
	0x32, 0x35, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006424 QUAD $0x0000000000003532; QUAD $0x0000000000000000  // .asciz 16, '25\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006434 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006444 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006454 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006464 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006474 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, //0x00006484 LONG $0x00000000  // .asciz 4, '\x00\x00\x00\x00'
	0x01, 0x00, 0x00, 0x00, //0x00006488 .long 1
	0x31, 0x32, 0x35, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x0000648c QUAD $0x0000000000353231; QUAD $0x0000000000000000  // .asciz 16, '125\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x0000649c QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000064ac QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000064bc QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000064cc QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000064dc QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, //0x000064ec LONG $0x00000000  // .asciz 4, '\x00\x00\x00\x00'
	0x02, 0x00, 0x00, 0x00, //0x000064f0 .long 2
	0x36, 0x32, 0x35, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000064f4 QUAD $0x0000000000353236; QUAD $0x0000000000000000  // .asciz 16, '625\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006504 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006514 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006524 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006534 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006544 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, //0x00006554 LONG $0x00000000  // .asciz 4, '\x00\x00\x00\x00'
	0x02, 0x00, 0x00, 0x00, //0x00006558 .long 2
	0x33, 0x31, 0x32, 0x35, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x0000655c QUAD $0x0000000035323133; QUAD $0x0000000000000000  // .asciz 16, '3125\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x0000656c QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x0000657c QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x0000658c QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x0000659c QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000065ac QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, //0x000065bc LONG $0x00000000  // .asciz 4, '\x00\x00\x00\x00'
	0x02, 0x00, 0x00, 0x00, //0x000065c0 .long 2
	0x31, 0x35, 0x36, 0x32, 0x35, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000065c4 QUAD $0x0000003532363531; QUAD $0x0000000000000000  // .asciz 16, '15625\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000065d4 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000065e4 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000065f4 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006604 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006614 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, //0x00006624 LONG $0x00000000  // .asciz 4, '\x00\x00\x00\x00'
	0x03, 0x00, 0x00, 0x00, //0x00006628 .long 3
	0x37, 0x38, 0x31, 0x32, 0x35, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x0000662c QUAD $0x0000003532313837; QUAD $0x0000000000000000  // .asciz 16, '78125\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x0000663c QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x0000664c QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x0000665c QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x0000666c QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x0000667c QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, //0x0000668c LONG $0x00000000  // .asciz 4, '\x00\x00\x00\x00'
	0x03, 0x00, 0x00, 0x00, //0x00006690 .long 3
	0x33, 0x39, 0x30, 0x36, 0x32, 0x35, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006694 QUAD $0x0000353236303933; QUAD $0x0000000000000000  // .asciz 16, '390625\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000066a4 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000066b4 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000066c4 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000066d4 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000066e4 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, //0x000066f4 LONG $0x00000000  // .asciz 4, '\x00\x00\x00\x00'
	0x03, 0x00, 0x00, 0x00, //0x000066f8 .long 3
	0x31, 0x39, 0x35, 0x33, 0x31, 0x32, 0x35, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000066fc QUAD $0x0035323133353931; QUAD $0x0000000000000000  // .asciz 16, '1953125\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x0000670c QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x0000671c QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x0000672c QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x0000673c QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x0000674c QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, //0x0000675c LONG $0x00000000  // .asciz 4, '\x00\x00\x00\x00'
	0x04, 0x00, 0x00, 0x00, //0x00006760 .long 4
	0x39, 0x37, 0x36, 0x35, 0x36, 0x32, 0x35, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006764 QUAD $0x0035323635363739; QUAD $0x0000000000000000  // .asciz 16, '9765625\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006774 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006784 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006794 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000067a4 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000067b4 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, //0x000067c4 LONG $0x00000000  // .asciz 4, '\x00\x00\x00\x00'
	0x04, 0x00, 0x00, 0x00, //0x000067c8 .long 4
	0x34, 0x38, 0x38, 0x32, 0x38, 0x31, 0x32, 0x35, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000067cc QUAD $0x3532313832383834; QUAD $0x0000000000000000  // .asciz 16, '48828125\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000067dc QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000067ec QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000067fc QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x0000680c QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x0000681c QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, //0x0000682c LONG $0x00000000  // .asciz 4, '\x00\x00\x00\x00'
	0x04, 0x00, 0x00, 0x00, //0x00006830 .long 4
	0x32, 0x34, 0x34, 0x31, 0x34, 0x30, 0x36, 0x32, 0x35, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006834 QUAD $0x3236303431343432; QUAD $0x0000000000000035  // .asciz 16, '244140625\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006844 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006854 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006864 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006874 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006884 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, //0x00006894 LONG $0x00000000  // .asciz 4, '\x00\x00\x00\x00'
	0x04, 0x00, 0x00, 0x00, //0x00006898 .long 4
	0x31, 0x32, 0x32, 0x30, 0x37, 0x30, 0x33, 0x31, 0x32, 0x35, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x0000689c QUAD $0x3133303730323231; QUAD $0x0000000000003532  // .asciz 16, '1220703125\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000068ac QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000068bc QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000068cc QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000068dc QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000068ec QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, //0x000068fc LONG $0x00000000  // .asciz 4, '\x00\x00\x00\x00'
	0x05, 0x00, 0x00, 0x00, //0x00006900 .long 5
	0x36, 0x31, 0x30, 0x33, 0x35, 0x31, 0x35, 0x36, 0x32, 0x35, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006904 QUAD $0x3635313533303136; QUAD $0x0000000000003532  // .asciz 16, '6103515625\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006914 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006924 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006934 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006944 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006954 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, //0x00006964 LONG $0x00000000  // .asciz 4, '\x00\x00\x00\x00'
	0x05, 0x00, 0x00, 0x00, //0x00006968 .long 5
	0x33, 0x30, 0x35, 0x31, 0x37, 0x35, 0x37, 0x38, 0x31, 0x32, 0x35, 0x00, 0x00, 0x00, 0x00, 0x00, //0x0000696c QUAD $0x3837353731353033; QUAD $0x0000000000353231  // .asciz 16, '30517578125\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x0000697c QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x0000698c QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x0000699c QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000069ac QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000069bc QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, //0x000069cc LONG $0x00000000  // .asciz 4, '\x00\x00\x00\x00'
	0x05, 0x00, 0x00, 0x00, //0x000069d0 .long 5
	0x31, 0x35, 0x32, 0x35, 0x38, 0x37, 0x38, 0x39, 0x30, 0x36, 0x32, 0x35, 0x00, 0x00, 0x00, 0x00, //0x000069d4 QUAD $0x3938373835323531; QUAD $0x0000000035323630  // .asciz 16, '152587890625\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000069e4 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000069f4 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006a04 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006a14 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006a24 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, //0x00006a34 LONG $0x00000000  // .asciz 4, '\x00\x00\x00\x00'
	0x06, 0x00, 0x00, 0x00, //0x00006a38 .long 6
	0x37, 0x36, 0x32, 0x39, 0x33, 0x39, 0x34, 0x35, 0x33, 0x31, 0x32, 0x35, 0x00, 0x00, 0x00, 0x00, //0x00006a3c QUAD $0x3534393339323637; QUAD $0x0000000035323133  // .asciz 16, '762939453125\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006a4c QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006a5c QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006a6c QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006a7c QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006a8c QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, //0x00006a9c LONG $0x00000000  // .asciz 4, '\x00\x00\x00\x00'
	0x06, 0x00, 0x00, 0x00, //0x00006aa0 .long 6
	0x33, 0x38, 0x31, 0x34, 0x36, 0x39, 0x37, 0x32, 0x36, 0x35, 0x36, 0x32, 0x35, 0x00, 0x00, 0x00, //0x00006aa4 QUAD $0x3237393634313833; QUAD $0x0000003532363536  // .asciz 16, '3814697265625\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006ab4 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006ac4 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006ad4 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006ae4 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006af4 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, //0x00006b04 LONG $0x00000000  // .asciz 4, '\x00\x00\x00\x00'
	0x06, 0x00, 0x00, 0x00, //0x00006b08 .long 6
	0x31, 0x39, 0x30, 0x37, 0x33, 0x34, 0x38, 0x36, 0x33, 0x32, 0x38, 0x31, 0x32, 0x35, 0x00, 0x00, //0x00006b0c QUAD $0x3638343337303931; QUAD $0x0000353231383233  // .asciz 16, '19073486328125\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006b1c QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006b2c QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006b3c QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006b4c QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006b5c QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, //0x00006b6c LONG $0x00000000  // .asciz 4, '\x00\x00\x00\x00'
	0x07, 0x00, 0x00, 0x00, //0x00006b70 .long 7
	0x39, 0x35, 0x33, 0x36, 0x37, 0x34, 0x33, 0x31, 0x36, 0x34, 0x30, 0x36, 0x32, 0x35, 0x00, 0x00, //0x00006b74 QUAD $0x3133343736333539; QUAD $0x0000353236303436  // .asciz 16, '95367431640625\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006b84 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006b94 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006ba4 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006bb4 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006bc4 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, //0x00006bd4 LONG $0x00000000  // .asciz 4, '\x00\x00\x00\x00'
	0x07, 0x00, 0x00, 0x00, //0x00006bd8 .long 7
	0x34, 0x37, 0x36, 0x38, 0x33, 0x37, 0x31, 0x35, 0x38, 0x32, 0x30, 0x33, 0x31, 0x32, 0x35, 0x00, //0x00006bdc QUAD $0x3531373338363734; QUAD $0x0035323133303238  // .asciz 16, '476837158203125\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006bec QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006bfc QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006c0c QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006c1c QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006c2c QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, //0x00006c3c LONG $0x00000000  // .asciz 4, '\x00\x00\x00\x00'
	0x07, 0x00, 0x00, 0x00, //0x00006c40 .long 7
	0x32, 0x33, 0x38, 0x34, 0x31, 0x38, 0x35, 0x37, 0x39, 0x31, 0x30, 0x31, 0x35, 0x36, 0x32, 0x35, //0x00006c44 QUAD $0x3735383134383332; QUAD $0x3532363531303139  // .asciz 16, '2384185791015625'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006c54 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006c64 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006c74 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006c84 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006c94 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, //0x00006ca4 LONG $0x00000000  // .asciz 4, '\x00\x00\x00\x00'
	0x07, 0x00, 0x00, 0x00, //0x00006ca8 .long 7
	0x31, 0x31, 0x39, 0x32, 0x30, 0x39, 0x32, 0x38, 0x39, 0x35, 0x35, 0x30, 0x37, 0x38, 0x31, 0x32, //0x00006cac QUAD $0x3832393032393131; QUAD $0x3231383730353539  // .asciz 16, '1192092895507812'
	0x35, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006cbc QUAD $0x0000000000000035; QUAD $0x0000000000000000  // .asciz 16, '5\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006ccc QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006cdc QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006cec QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006cfc QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, //0x00006d0c LONG $0x00000000  // .asciz 4, '\x00\x00\x00\x00'
	0x08, 0x00, 0x00, 0x00, //0x00006d10 .long 8
	0x35, 0x39, 0x36, 0x30, 0x34, 0x36, 0x34, 0x34, 0x37, 0x37, 0x35, 0x33, 0x39, 0x30, 0x36, 0x32, //0x00006d14 QUAD $0x3434363430363935; QUAD $0x3236303933353737  // .asciz 16, '5960464477539062'
	0x35, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006d24 QUAD $0x0000000000000035; QUAD $0x0000000000000000  // .asciz 16, '5\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006d34 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006d44 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006d54 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006d64 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, //0x00006d74 LONG $0x00000000  // .asciz 4, '\x00\x00\x00\x00'
	0x08, 0x00, 0x00, 0x00, //0x00006d78 .long 8
	0x32, 0x39, 0x38, 0x30, 0x32, 0x33, 0x32, 0x32, 0x33, 0x38, 0x37, 0x36, 0x39, 0x35, 0x33, 0x31, //0x00006d7c QUAD $0x3232333230383932; QUAD $0x3133353936373833  // .asciz 16, '2980232238769531'
	0x32, 0x35, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006d8c QUAD $0x0000000000003532; QUAD $0x0000000000000000  // .asciz 16, '25\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006d9c QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006dac QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006dbc QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006dcc QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, //0x00006ddc LONG $0x00000000  // .asciz 4, '\x00\x00\x00\x00'
	0x08, 0x00, 0x00, 0x00, //0x00006de0 .long 8
	0x31, 0x34, 0x39, 0x30, 0x31, 0x31, 0x36, 0x31, 0x31, 0x39, 0x33, 0x38, 0x34, 0x37, 0x36, 0x35, //0x00006de4 QUAD $0x3136313130393431; QUAD $0x3536373438333931  // .asciz 16, '1490116119384765'
	0x36, 0x32, 0x35, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006df4 QUAD $0x0000000000353236; QUAD $0x0000000000000000  // .asciz 16, '625\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006e04 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006e14 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006e24 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006e34 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, //0x00006e44 LONG $0x00000000  // .asciz 4, '\x00\x00\x00\x00'
	0x09, 0x00, 0x00, 0x00, //0x00006e48 .long 9
	0x37, 0x34, 0x35, 0x30, 0x35, 0x38, 0x30, 0x35, 0x39, 0x36, 0x39, 0x32, 0x33, 0x38, 0x32, 0x38, //0x00006e4c QUAD $0x3530383530353437; QUAD $0x3832383332393639  // .asciz 16, '7450580596923828'
	0x31, 0x32, 0x35, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006e5c QUAD $0x0000000000353231; QUAD $0x0000000000000000  // .asciz 16, '125\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006e6c QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006e7c QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006e8c QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006e9c QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, //0x00006eac LONG $0x00000000  // .asciz 4, '\x00\x00\x00\x00'
	0x09, 0x00, 0x00, 0x00, //0x00006eb0 .long 9
	0x33, 0x37, 0x32, 0x35, 0x32, 0x39, 0x30, 0x32, 0x39, 0x38, 0x34, 0x36, 0x31, 0x39, 0x31, 0x34, //0x00006eb4 QUAD $0x3230393235323733; QUAD $0x3431393136343839  // .asciz 16, '3725290298461914'
	0x30, 0x36, 0x32, 0x35, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006ec4 QUAD $0x0000000035323630; QUAD $0x0000000000000000  // .asciz 16, '0625\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006ed4 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006ee4 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006ef4 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006f04 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, //0x00006f14 LONG $0x00000000  // .asciz 4, '\x00\x00\x00\x00'
	0x09, 0x00, 0x00, 0x00, //0x00006f18 .long 9
	0x31, 0x38, 0x36, 0x32, 0x36, 0x34, 0x35, 0x31, 0x34, 0x39, 0x32, 0x33, 0x30, 0x39, 0x35, 0x37, //0x00006f1c QUAD $0x3135343632363831; QUAD $0x3735393033323934  // .asciz 16, '1862645149230957'
	0x30, 0x33, 0x31, 0x32, 0x35, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006f2c QUAD $0x0000003532313330; QUAD $0x0000000000000000  // .asciz 16, '03125\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006f3c QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006f4c QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006f5c QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006f6c QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, //0x00006f7c LONG $0x00000000  // .asciz 4, '\x00\x00\x00\x00'
	0x0a, 0x00, 0x00, 0x00, //0x00006f80 .long 10
	0x39, 0x33, 0x31, 0x33, 0x32, 0x32, 0x35, 0x37, 0x34, 0x36, 0x31, 0x35, 0x34, 0x37, 0x38, 0x35, //0x00006f84 QUAD $0x3735323233313339; QUAD $0x3538373435313634  // .asciz 16, '9313225746154785'
	0x31, 0x35, 0x36, 0x32, 0x35, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006f94 QUAD $0x0000003532363531; QUAD $0x0000000000000000  // .asciz 16, '15625\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006fa4 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006fb4 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006fc4 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006fd4 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, //0x00006fe4 LONG $0x00000000  // .asciz 4, '\x00\x00\x00\x00'
	0x0a, 0x00, 0x00, 0x00, //0x00006fe8 .long 10
	0x34, 0x36, 0x35, 0x36, 0x36, 0x31, 0x32, 0x38, 0x37, 0x33, 0x30, 0x37, 0x37, 0x33, 0x39, 0x32, //0x00006fec QUAD $0x3832313636353634; QUAD $0x3239333737303337  // .asciz 16, '4656612873077392'
	0x35, 0x37, 0x38, 0x31, 0x32, 0x35, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006ffc QUAD $0x0000353231383735; QUAD $0x0000000000000000  // .asciz 16, '578125\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x0000700c QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x0000701c QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x0000702c QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x0000703c QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, //0x0000704c LONG $0x00000000  // .asciz 4, '\x00\x00\x00\x00'
	0x0a, 0x00, 0x00, 0x00, //0x00007050 .long 10
	0x32, 0x33, 0x32, 0x38, 0x33, 0x30, 0x36, 0x34, 0x33, 0x36, 0x35, 0x33, 0x38, 0x36, 0x39, 0x36, //0x00007054 QUAD $0x3436303338323332; QUAD $0x3639363833353633  // .asciz 16, '2328306436538696'
	0x32, 0x38, 0x39, 0x30, 0x36, 0x32, 0x35, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00007064 QUAD $0x0035323630393832; QUAD $0x0000000000000000  // .asciz 16, '2890625\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00007074 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00007084 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00007094 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000070a4 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, //0x000070b4 LONG $0x00000000  // .asciz 4, '\x00\x00\x00\x00'
	0x0a, 0x00, 0x00, 0x00, //0x000070b8 .long 10
	0x31, 0x31, 0x36, 0x34, 0x31, 0x35, 0x33, 0x32, 0x31, 0x38, 0x32, 0x36, 0x39, 0x33, 0x34, 0x38, //0x000070bc QUAD $0x3233353134363131; QUAD $0x3834333936323831  // .asciz 16, '1164153218269348'
	0x31, 0x34, 0x34, 0x35, 0x33, 0x31, 0x32, 0x35, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000070cc QUAD $0x3532313335343431; QUAD $0x0000000000000000  // .asciz 16, '14453125\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000070dc QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000070ec QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000070fc QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x0000710c QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, //0x0000711c LONG $0x00000000  // .asciz 4, '\x00\x00\x00\x00'
	0x0b, 0x00, 0x00, 0x00, //0x00007120 .long 11
	0x35, 0x38, 0x32, 0x30, 0x37, 0x36, 0x36, 0x30, 0x39, 0x31, 0x33, 0x34, 0x36, 0x37, 0x34, 0x30, //0x00007124 QUAD $0x3036363730323835; QUAD $0x3034373634333139  // .asciz 16, '5820766091346740'
	0x37, 0x32, 0x32, 0x36, 0x35, 0x36, 0x32, 0x35, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00007134 QUAD $0x3532363536323237; QUAD $0x0000000000000000  // .asciz 16, '72265625\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00007144 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00007154 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00007164 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00007174 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, //0x00007184 LONG $0x00000000  // .asciz 4, '\x00\x00\x00\x00'
	0x0b, 0x00, 0x00, 0x00, //0x00007188 .long 11
	0x32, 0x39, 0x31, 0x30, 0x33, 0x38, 0x33, 0x30, 0x34, 0x35, 0x36, 0x37, 0x33, 0x33, 0x37, 0x30, //0x0000718c QUAD $0x3033383330313932; QUAD $0x3037333337363534  // .asciz 16, '2910383045673370'
	0x33, 0x36, 0x31, 0x33, 0x32, 0x38, 0x31, 0x32, 0x35, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x0000719c QUAD $0x3231383233313633; QUAD $0x0000000000000035  // .asciz 16, '361328125\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000071ac QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000071bc QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000071cc QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000071dc QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, //0x000071ec LONG $0x00000000  // .asciz 4, '\x00\x00\x00\x00'
	0x0b, 0x00, 0x00, 0x00, //0x000071f0 .long 11
	0x31, 0x34, 0x35, 0x35, 0x31, 0x39, 0x31, 0x35, 0x32, 0x32, 0x38, 0x33, 0x36, 0x36, 0x38, 0x35, //0x000071f4 QUAD $0x3531393135353431; QUAD $0x3538363633383232  // .asciz 16, '1455191522836685'
	0x31, 0x38, 0x30, 0x36, 0x36, 0x34, 0x30, 0x36, 0x32, 0x35, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00007204 QUAD $0x3630343636303831; QUAD $0x0000000000003532  // .asciz 16, '1806640625\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00007214 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00007224 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00007234 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00007244 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, //0x00007254 LONG $0x00000000  // .asciz 4, '\x00\x00\x00\x00'
	0x0c, 0x00, 0x00, 0x00, //0x00007258 .long 12
	0x37, 0x32, 0x37, 0x35, 0x39, 0x35, 0x37, 0x36, 0x31, 0x34, 0x31, 0x38, 0x33, 0x34, 0x32, 0x35, //0x0000725c QUAD $0x3637353935373237; QUAD $0x3532343338313431  // .asciz 16, '7275957614183425'
	0x39, 0x30, 0x33, 0x33, 0x32, 0x30, 0x33, 0x31, 0x32, 0x35, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x0000726c QUAD $0x3133303233333039; QUAD $0x0000000000003532  // .asciz 16, '9033203125\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x0000727c QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x0000728c QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x0000729c QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000072ac QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, //0x000072bc LONG $0x00000000  // .asciz 4, '\x00\x00\x00\x00'
	0x0c, 0x00, 0x00, 0x00, //0x000072c0 .long 12
	0x33, 0x36, 0x33, 0x37, 0x39, 0x37, 0x38, 0x38, 0x30, 0x37, 0x30, 0x39, 0x31, 0x37, 0x31, 0x32, //0x000072c4 QUAD $0x3838373937333633; QUAD $0x3231373139303730  // .asciz 16, '3637978807091712'
	0x39, 0x35, 0x31, 0x36, 0x36, 0x30, 0x31, 0x35, 0x36, 0x32, 0x35, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000072d4 QUAD $0x3531303636313539; QUAD $0x0000000000353236  // .asciz 16, '95166015625\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000072e4 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000072f4 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00007304 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00007314 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, //0x00007324 LONG $0x00000000  // .asciz 4, '\x00\x00\x00\x00'
	0x0c, 0x00, 0x00, 0x00, //0x00007328 .long 12
	0x31, 0x38, 0x31, 0x38, 0x39, 0x38, 0x39, 0x34, 0x30, 0x33, 0x35, 0x34, 0x35, 0x38, 0x35, 0x36, //0x0000732c QUAD $0x3439383938313831; QUAD $0x3635383534353330  // .asciz 16, '1818989403545856'
	0x34, 0x37, 0x35, 0x38, 0x33, 0x30, 0x30, 0x37, 0x38, 0x31, 0x32, 0x35, 0x00, 0x00, 0x00, 0x00, //0x0000733c QUAD $0x3730303338353734; QUAD $0x0000000035323138  // .asciz 16, '475830078125\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x0000734c QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x0000735c QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x0000736c QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x0000737c QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, //0x0000738c LONG $0x00000000  // .asciz 4, '\x00\x00\x00\x00'
	0x0d, 0x00, 0x00, 0x00, //0x00007390 .long 13
	0x39, 0x30, 0x39, 0x34, 0x39, 0x34, 0x37, 0x30, 0x31, 0x37, 0x37, 0x32, 0x39, 0x32, 0x38, 0x32, //0x00007394 QUAD $0x3037343934393039; QUAD $0x3238323932373731  // .asciz 16, '9094947017729282'
	0x33, 0x37, 0x39, 0x31, 0x35, 0x30, 0x33, 0x39, 0x30, 0x36, 0x32, 0x35, 0x00, 0x00, 0x00, 0x00, //0x000073a4 QUAD $0x3933303531393733; QUAD $0x0000000035323630  // .asciz 16, '379150390625\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000073b4 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000073c4 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000073d4 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000073e4 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, //0x000073f4 LONG $0x00000000  // .asciz 4, '\x00\x00\x00\x00'
	0x0d, 0x00, 0x00, 0x00, //0x000073f8 .long 13
	0x34, 0x35, 0x34, 0x37, 0x34, 0x37, 0x33, 0x35, 0x30, 0x38, 0x38, 0x36, 0x34, 0x36, 0x34, 0x31, //0x000073fc QUAD $0x3533373437343534; QUAD $0x3134363436383830  // .asciz 16, '4547473508864641'
	0x31, 0x38, 0x39, 0x35, 0x37, 0x35, 0x31, 0x39, 0x35, 0x33, 0x31, 0x32, 0x35, 0x00, 0x00, 0x00, //0x0000740c QUAD $0x3931353735393831; QUAD $0x0000003532313335  // .asciz 16, '1895751953125\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x0000741c QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x0000742c QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x0000743c QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x0000744c QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, //0x0000745c LONG $0x00000000  // .asciz 4, '\x00\x00\x00\x00'
	0x0d, 0x00, 0x00, 0x00, //0x00007460 .long 13
	0x32, 0x32, 0x37, 0x33, 0x37, 0x33, 0x36, 0x37, 0x35, 0x34, 0x34, 0x33, 0x32, 0x33, 0x32, 0x30, //0x00007464 QUAD $0x3736333733373232; QUAD $0x3032333233343435  // .asciz 16, '2273736754432320'
	0x35, 0x39, 0x34, 0x37, 0x38, 0x37, 0x35, 0x39, 0x37, 0x36, 0x35, 0x36, 0x32, 0x35, 0x00, 0x00, //0x00007474 QUAD $0x3935373837343935; QUAD $0x0000353236353637  // .asciz 16, '59478759765625\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00007484 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00007494 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000074a4 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000074b4 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, //0x000074c4 LONG $0x00000000  // .asciz 4, '\x00\x00\x00\x00'
	0x0d, 0x00, 0x00, 0x00, //0x000074c8 .long 13
	0x31, 0x31, 0x33, 0x36, 0x38, 0x36, 0x38, 0x33, 0x37, 0x37, 0x32, 0x31, 0x36, 0x31, 0x36, 0x30, //0x000074cc QUAD $0x3338363836333131; QUAD $0x3036313631323737  // .asciz 16, '1136868377216160'
	0x32, 0x39, 0x37, 0x33, 0x39, 0x33, 0x37, 0x39, 0x38, 0x38, 0x32, 0x38, 0x31, 0x32, 0x35, 0x00, //0x000074dc QUAD $0x3937333933373932; QUAD $0x0035323138323838  // .asciz 16, '297393798828125\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000074ec QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000074fc QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x0000750c QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x0000751c QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, //0x0000752c LONG $0x00000000  // .asciz 4, '\x00\x00\x00\x00'
	0x0e, 0x00, 0x00, 0x00, //0x00007530 .long 14
	0x35, 0x36, 0x38, 0x34, 0x33, 0x34, 0x31, 0x38, 0x38, 0x36, 0x30, 0x38, 0x30, 0x38, 0x30, 0x31, //0x00007534 QUAD $0x3831343334383635; QUAD $0x3130383038303638  // .asciz 16, '5684341886080801'
	0x34, 0x38, 0x36, 0x39, 0x36, 0x38, 0x39, 0x39, 0x34, 0x31, 0x34, 0x30, 0x36, 0x32, 0x35, 0x00, //0x00007544 QUAD $0x3939383639363834; QUAD $0x0035323630343134  // .asciz 16, '486968994140625\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00007554 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00007564 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00007574 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00007584 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, //0x00007594 LONG $0x00000000  // .asciz 4, '\x00\x00\x00\x00'
	0x0e, 0x00, 0x00, 0x00, //0x00007598 .long 14
	0x32, 0x38, 0x34, 0x32, 0x31, 0x37, 0x30, 0x39, 0x34, 0x33, 0x30, 0x34, 0x30, 0x34, 0x30, 0x30, //0x0000759c QUAD $0x3930373132343832; QUAD $0x3030343034303334  // .asciz 16, '2842170943040400'
	0x37, 0x34, 0x33, 0x34, 0x38, 0x34, 0x34, 0x39, 0x37, 0x30, 0x37, 0x30, 0x33, 0x31, 0x32, 0x35, //0x000075ac QUAD $0x3934343834333437; QUAD $0x3532313330373037  // .asciz 16, '7434844970703125'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000075bc QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000075cc QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000075dc QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000075ec QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, //0x000075fc LONG $0x00000000  // .asciz 4, '\x00\x00\x00\x00'
	0x0e, 0x00, 0x00, 0x00, //0x00007600 .long 14
	0x31, 0x34, 0x32, 0x31, 0x30, 0x38, 0x35, 0x34, 0x37, 0x31, 0x35, 0x32, 0x30, 0x32, 0x30, 0x30, //0x00007604 QUAD $0x3435383031323431; QUAD $0x3030323032353137  // .asciz 16, '1421085471520200'
	0x33, 0x37, 0x31, 0x37, 0x34, 0x32, 0x32, 0x34, 0x38, 0x35, 0x33, 0x35, 0x31, 0x35, 0x36, 0x32, //0x00007614 QUAD $0x3432323437313733; QUAD $0x3236353135333538  // .asciz 16, '3717422485351562'
	0x35, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00007624 QUAD $0x0000000000000035; QUAD $0x0000000000000000  // .asciz 16, '5\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00007634 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00007644 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00007654 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, //0x00007664 LONG $0x00000000  // .asciz 4, '\x00\x00\x00\x00'
	0x0f, 0x00, 0x00, 0x00, //0x00007668 .long 15
	0x37, 0x31, 0x30, 0x35, 0x34, 0x32, 0x37, 0x33, 0x35, 0x37, 0x36, 0x30, 0x31, 0x30, 0x30, 0x31, //0x0000766c QUAD $0x3337323435303137; QUAD $0x3130303130363735  // .asciz 16, '7105427357601001'
	0x38, 0x35, 0x38, 0x37, 0x31, 0x31, 0x32, 0x34, 0x32, 0x36, 0x37, 0x35, 0x37, 0x38, 0x31, 0x32, //0x0000767c QUAD $0x3432313137383538; QUAD $0x3231383735373632  // .asciz 16, '8587112426757812'
	0x35, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x0000768c QUAD $0x0000000000000035; QUAD $0x0000000000000000  // .asciz 16, '5\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x0000769c QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000076ac QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000076bc QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, //0x000076cc LONG $0x00000000  // .asciz 4, '\x00\x00\x00\x00'
	0x0f, 0x00, 0x00, 0x00, //0x000076d0 .long 15
	0x33, 0x35, 0x35, 0x32, 0x37, 0x31, 0x33, 0x36, 0x37, 0x38, 0x38, 0x30, 0x30, 0x35, 0x30, 0x30, //0x000076d4 QUAD $0x3633313732353533; QUAD $0x3030353030383837  // .asciz 16, '3552713678800500'
	0x39, 0x32, 0x39, 0x33, 0x35, 0x35, 0x36, 0x32, 0x31, 0x33, 0x33, 0x37, 0x38, 0x39, 0x30, 0x36, //0x000076e4 QUAD $0x3236353533393239; QUAD $0x3630393837333331  // .asciz 16, '9293556213378906'
	0x32, 0x35, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000076f4 QUAD $0x0000000000003532; QUAD $0x0000000000000000  // .asciz 16, '25\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00007704 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00007714 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00007724 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, //0x00007734 LONG $0x00000000  // .asciz 4, '\x00\x00\x00\x00'
	0x0f, 0x00, 0x00, 0x00, //0x00007738 .long 15
	0x31, 0x37, 0x37, 0x36, 0x33, 0x35, 0x36, 0x38, 0x33, 0x39, 0x34, 0x30, 0x30, 0x32, 0x35, 0x30, //0x0000773c QUAD $0x3836353336373731; QUAD $0x3035323030343933  // .asciz 16, '1776356839400250'
	0x34, 0x36, 0x34, 0x36, 0x37, 0x37, 0x38, 0x31, 0x30, 0x36, 0x36, 0x38, 0x39, 0x34, 0x35, 0x33, //0x0000774c QUAD $0x3138373736343634; QUAD $0x3335343938363630  // .asciz 16, '4646778106689453'
	0x31, 0x32, 0x35, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x0000775c QUAD $0x0000000000353231; QUAD $0x0000000000000000  // .asciz 16, '125\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x0000776c QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x0000777c QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x0000778c QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, //0x0000779c LONG $0x00000000  // .asciz 4, '\x00\x00\x00\x00'
	0x10, 0x00, 0x00, 0x00, //0x000077a0 .long 16
	0x38, 0x38, 0x38, 0x31, 0x37, 0x38, 0x34, 0x31, 0x39, 0x37, 0x30, 0x30, 0x31, 0x32, 0x35, 0x32, //0x000077a4 QUAD $0x3134383731383838; QUAD $0x3235323130303739  // .asciz 16, '8881784197001252'
	0x33, 0x32, 0x33, 0x33, 0x38, 0x39, 0x30, 0x35, 0x33, 0x33, 0x34, 0x34, 0x37, 0x32, 0x36, 0x35, //0x000077b4 QUAD $0x3530393833333233; QUAD $0x3536323734343333  // .asciz 16, '3233890533447265'
	0x36, 0x32, 0x35, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000077c4 QUAD $0x0000000000353236; QUAD $0x0000000000000000  // .asciz 16, '625\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000077d4 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000077e4 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000077f4 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, //0x00007804 LONG $0x00000000  // .asciz 4, '\x00\x00\x00\x00'
	0x10, 0x00, 0x00, 0x00, //0x00007808 .long 16
	0x34, 0x34, 0x34, 0x30, 0x38, 0x39, 0x32, 0x30, 0x39, 0x38, 0x35, 0x30, 0x30, 0x36, 0x32, 0x36, //0x0000780c QUAD $0x3032393830343434; QUAD $0x3632363030353839  // .asciz 16, '4440892098500626'
	0x31, 0x36, 0x31, 0x36, 0x39, 0x34, 0x35, 0x32, 0x36, 0x36, 0x37, 0x32, 0x33, 0x36, 0x33, 0x32, //0x0000781c QUAD $0x3235343936313631; QUAD $0x3233363332373636  // .asciz 16, '1616945266723632'
	0x38, 0x31, 0x32, 0x35, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x0000782c QUAD $0x0000000035323138; QUAD $0x0000000000000000  // .asciz 16, '8125\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x0000783c QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x0000784c QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x0000785c QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, //0x0000786c LONG $0x00000000  // .asciz 4, '\x00\x00\x00\x00'
	0x10, 0x00, 0x00, 0x00, //0x00007870 .long 16
	0x32, 0x32, 0x32, 0x30, 0x34, 0x34, 0x36, 0x30, 0x34, 0x39, 0x32, 0x35, 0x30, 0x33, 0x31, 0x33, //0x00007874 QUAD $0x3036343430323232; QUAD $0x3331333035323934  // .asciz 16, '2220446049250313'
	0x30, 0x38, 0x30, 0x38, 0x34, 0x37, 0x32, 0x36, 0x33, 0x33, 0x33, 0x36, 0x31, 0x38, 0x31, 0x36, //0x00007884 QUAD $0x3632373438303830; QUAD $0x3631383136333333  // .asciz 16, '0808472633361816'
	0x34, 0x30, 0x36, 0x32, 0x35, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00007894 QUAD $0x0000003532363034; QUAD $0x0000000000000000  // .asciz 16, '40625\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000078a4 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000078b4 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000078c4 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, //0x000078d4 LONG $0x00000000  // .asciz 4, '\x00\x00\x00\x00'
	0x10, 0x00, 0x00, 0x00, //0x000078d8 .long 16
	0x31, 0x31, 0x31, 0x30, 0x32, 0x32, 0x33, 0x30, 0x32, 0x34, 0x36, 0x32, 0x35, 0x31, 0x35, 0x36, //0x000078dc QUAD $0x3033323230313131; QUAD $0x3635313532363432  // .asciz 16, '1110223024625156'
	0x35, 0x34, 0x30, 0x34, 0x32, 0x33, 0x36, 0x33, 0x31, 0x36, 0x36, 0x38, 0x30, 0x39, 0x30, 0x38, //0x000078ec QUAD $0x3336333234303435; QUAD $0x3830393038363631  // .asciz 16, '5404236316680908'
	0x32, 0x30, 0x33, 0x31, 0x32, 0x35, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000078fc QUAD $0x0000353231333032; QUAD $0x0000000000000000  // .asciz 16, '203125\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x0000790c QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x0000791c QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x0000792c QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, //0x0000793c LONG $0x00000000  // .asciz 4, '\x00\x00\x00\x00'
	0x11, 0x00, 0x00, 0x00, //0x00007940 .long 17
	0x35, 0x35, 0x35, 0x31, 0x31, 0x31, 0x35, 0x31, 0x32, 0x33, 0x31, 0x32, 0x35, 0x37, 0x38, 0x32, //0x00007944 QUAD $0x3135313131353535; QUAD $0x3238373532313332  // .asciz 16, '5551115123125782'
	0x37, 0x30, 0x32, 0x31, 0x31, 0x38, 0x31, 0x35, 0x38, 0x33, 0x34, 0x30, 0x34, 0x35, 0x34, 0x31, //0x00007954 QUAD $0x3531383131323037; QUAD $0x3134353430343338  // .asciz 16, '7021181583404541'
	0x30, 0x31, 0x35, 0x36, 0x32, 0x35, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00007964 QUAD $0x0000353236353130; QUAD $0x0000000000000000  // .asciz 16, '015625\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00007974 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00007984 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00007994 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, //0x000079a4 LONG $0x00000000  // .asciz 4, '\x00\x00\x00\x00'
	0x11, 0x00, 0x00, 0x00, //0x000079a8 .long 17
	0x32, 0x37, 0x37, 0x35, 0x35, 0x35, 0x37, 0x35, 0x36, 0x31, 0x35, 0x36, 0x32, 0x38, 0x39, 0x31, //0x000079ac QUAD $0x3537353535373732; QUAD $0x3139383236353136  // .asciz 16, '2775557561562891'
	0x33, 0x35, 0x31, 0x30, 0x35, 0x39, 0x30, 0x37, 0x39, 0x31, 0x37, 0x30, 0x32, 0x32, 0x37, 0x30, //0x000079bc QUAD $0x3730393530313533; QUAD $0x3037323230373139  // .asciz 16, '3510590791702270'
	0x35, 0x30, 0x37, 0x38, 0x31, 0x32, 0x35, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000079cc QUAD $0x0035323138373035; QUAD $0x0000000000000000  // .asciz 16, '5078125\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000079dc QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000079ec QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000079fc QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, //0x00007a0c LONG $0x00000000  // .asciz 4, '\x00\x00\x00\x00'
	0x11, 0x00, 0x00, 0x00, //0x00007a10 .long 17
	0x31, 0x33, 0x38, 0x37, 0x37, 0x37, 0x38, 0x37, 0x38, 0x30, 0x37, 0x38, 0x31, 0x34, 0x34, 0x35, //0x00007a14 QUAD $0x3738373737383331; QUAD $0x3534343138373038  // .asciz 16, '1387778780781445'
	0x36, 0x37, 0x35, 0x35, 0x32, 0x39, 0x35, 0x33, 0x39, 0x35, 0x38, 0x35, 0x31, 0x31, 0x33, 0x35, //0x00007a24 QUAD $0x3335393235353736; QUAD $0x3533313135383539  // .asciz 16, '6755295395851135'
	0x32, 0x35, 0x33, 0x39, 0x30, 0x36, 0x32, 0x35, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00007a34 QUAD $0x3532363039333532; QUAD $0x0000000000000000  // .asciz 16, '25390625\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00007a44 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00007a54 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00007a64 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, //0x00007a74 LONG $0x00000000  // .asciz 4, '\x00\x00\x00\x00'
	0x12, 0x00, 0x00, 0x00, //0x00007a78 .long 18
	0x36, 0x39, 0x33, 0x38, 0x38, 0x39, 0x33, 0x39, 0x30, 0x33, 0x39, 0x30, 0x37, 0x32, 0x32, 0x38, //0x00007a7c QUAD $0x3933393838333936; QUAD $0x3832323730393330  // .asciz 16, '6938893903907228'
	0x33, 0x37, 0x37, 0x36, 0x34, 0x37, 0x36, 0x39, 0x37, 0x39, 0x32, 0x35, 0x35, 0x36, 0x37, 0x36, //0x00007a8c QUAD $0x3936373436373733; QUAD $0x3637363535323937  // .asciz 16, '3776476979255676'
	0x32, 0x36, 0x39, 0x35, 0x33, 0x31, 0x32, 0x35, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00007a9c QUAD $0x3532313335393632; QUAD $0x0000000000000000  // .asciz 16, '26953125\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00007aac QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00007abc QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00007acc QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, //0x00007adc LONG $0x00000000  // .asciz 4, '\x00\x00\x00\x00'
	0x12, 0x00, 0x00, 0x00, //0x00007ae0 .long 18
	0x33, 0x34, 0x36, 0x39, 0x34, 0x34, 0x36, 0x39, 0x35, 0x31, 0x39, 0x35, 0x33, 0x36, 0x31, 0x34, //0x00007ae4 QUAD $0x3936343439363433; QUAD $0x3431363335393135  // .asciz 16, '3469446951953614'
	0x31, 0x38, 0x38, 0x38, 0x32, 0x33, 0x38, 0x34, 0x38, 0x39, 0x36, 0x32, 0x37, 0x38, 0x33, 0x38, //0x00007af4 QUAD $0x3438333238383831; QUAD $0x3833383732363938  // .asciz 16, '1888238489627838'
	0x31, 0x33, 0x34, 0x37, 0x36, 0x35, 0x36, 0x32, 0x35, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00007b04 QUAD $0x3236353637343331; QUAD $0x0000000000000035  // .asciz 16, '134765625\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00007b14 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00007b24 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00007b34 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, //0x00007b44 LONG $0x00000000  // .asciz 4, '\x00\x00\x00\x00'
	0x12, 0x00, 0x00, 0x00, //0x00007b48 .long 18
	0x31, 0x37, 0x33, 0x34, 0x37, 0x32, 0x33, 0x34, 0x37, 0x35, 0x39, 0x37, 0x36, 0x38, 0x30, 0x37, //0x00007b4c QUAD $0x3433323734333731; QUAD $0x3730383637393537  // .asciz 16, '1734723475976807'
	0x30, 0x39, 0x34, 0x34, 0x31, 0x31, 0x39, 0x32, 0x34, 0x34, 0x38, 0x31, 0x33, 0x39, 0x31, 0x39, //0x00007b5c QUAD $0x3239313134343930; QUAD $0x3931393331383434  // .asciz 16, '0944119244813919'
	0x30, 0x36, 0x37, 0x33, 0x38, 0x32, 0x38, 0x31, 0x32, 0x35, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00007b6c QUAD $0x3138323833373630; QUAD $0x0000000000003532  // .asciz 16, '0673828125\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00007b7c QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00007b8c QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00007b9c QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, //0x00007bac LONG $0x00000000  // .asciz 4, '\x00\x00\x00\x00'
	0x13, 0x00, 0x00, 0x00, //0x00007bb0 .long 19
	0x38, 0x36, 0x37, 0x33, 0x36, 0x31, 0x37, 0x33, 0x37, 0x39, 0x38, 0x38, 0x34, 0x30, 0x33, 0x35, //0x00007bb4 QUAD $0x3337313633373638; QUAD $0x3533303438383937  // .asciz 16, '8673617379884035'
	0x34, 0x37, 0x32, 0x30, 0x35, 0x39, 0x36, 0x32, 0x32, 0x34, 0x30, 0x36, 0x39, 0x35, 0x39, 0x35, //0x00007bc4 QUAD $0x3236393530323734; QUAD $0x3539353936303432  // .asciz 16, '4720596224069595'
	0x33, 0x33, 0x36, 0x39, 0x31, 0x34, 0x30, 0x36, 0x32, 0x35, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00007bd4 QUAD $0x3630343139363333; QUAD $0x0000000000003532  // .asciz 16, '3369140625\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00007be4 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00007bf4 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00007c04 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, //0x00007c14 LONG $0x00000000  // .asciz 4, '\x00\x00\x00\x00'
}
 
