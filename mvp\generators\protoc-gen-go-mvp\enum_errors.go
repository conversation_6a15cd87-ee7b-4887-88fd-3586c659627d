package main

import (
	"strconv"
	"strings"

	"google.golang.org/protobuf/compiler/protogen"
	"google.golang.org/protobuf/proto"

	"git.local/sensitive/mvp/pb"
)

type EnumErrorsGenerator struct{}

func (generator *EnumErrorsGenerator) ProtoGenGenerate(gen *protogen.Plugin) {
	for _, file := range gen.Files {
		if !file.Generate {
			continue
		}

		if len(file.Enums) == 0 {
			return
		}

		g := gen.NewGeneratedFile(
			file.GeneratedFilenamePrefix+V2_ENUM_ERRORS_SUFFIX,
			file.GoImportPath,
		)
		HeaderPrint(g, string(file.GoPackageName))

		for _, enum := range file.Enums {
			for _, value := range enum.Values {
				extDefault, _ := proto.GetExtension(value.Desc.Options(), pb.E_ErrorDescribe).(*pb.ErrorDescribe)
				if extDefault == nil {
					continue
				}

				name := strings.TrimPrefix(value.GoIdent.GoName, enum.GoIdent.GoName+"_")
				errorTypeVal := strconv.Quote(extDefault.GetErrorType().String())
				messageVal := strconv.Quote(extDefault.GetDescription())
				g.P("var (")
				g.P("status", name, ", _ = ", statusPackageNew, "(")
				g.P(codesPackageEmpty, extDefault.GetCode(), ",")
				g.P(messageVal, ",")
				g.P(").WithDetails(&", structPbPackageStruct, "{")
				g.P("Fields: map[string]*", structPbPackageValue, "{")
				g.P(strconv.Quote("GrpcCode"), ":", structPbPackageNewNumberValue, "(float64(", codesPackageEmpty, extDefault.GetCode(), ")),")
				g.P(strconv.Quote("Message"), ":", structPbPackageNewStringValue, "(", messageVal, "),")
				g.P(strconv.Quote("ErrorType"), ":", structPbPackageNewStringValue, "(", pkgerrorPackageEmpty, extDefault.GetErrorType(), "), // ", errorTypeVal)
				g.P(strconv.Quote("IsExpected"), ":", structPbPackageNewBoolValue, "(true),")
				g.P(strconv.Quote("Code"), ":", structPbPackageNewNumberValue, "(float64(", value.GoIdent, ")), // ", value.Desc.Number())
				g.P("},")
				g.P("})")
				g.P()

				g.P(name, " = ", goErrPackageGoErr, "{")
				g.P("ErrType:", pkgerrorPackageEmpty, extDefault.GetErrorType(), ", // ", errorTypeVal)
				g.P("Status:", "status", name, ",")
				g.P("Code: int(", value.GoIdent, "), // ", value.Desc.Number())
				g.P("Message:", messageVal, ",")
				g.P("GrpcCode:", codesPackageEmpty, extDefault.GetCode(), ",")
				g.P("IsExpected: true,")
				g.P("}")
				g.P(")")
				g.P()
			}

		}
	}
}
