// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.5.1
// - protoc             v5.29.3
// source: inner/processing/grpc/payorder.proto

package grpc

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.64.0 or later.
const _ = grpc.SupportPackageIsVersion9

const (
	Payorder_StartCheckInProcessOrdersWorker_FullMethodName   = "/processing.payorder.payorder.Payorder/StartCheckInProcessOrdersWorker"
	Payorder_StartCreateNewPaymentOrdersWorker_FullMethodName = "/processing.payorder.payorder.Payorder/StartCreateNewPaymentOrdersWorker"
)

// PayorderClient is the client API for Payorder service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type PayorderClient interface {
	StartCheckInProcessOrdersWorker(ctx context.Context, in *emptypb.Empty, opts ...grpc.CallOption) (*emptypb.Empty, error)
	StartCreateNewPaymentOrdersWorker(ctx context.Context, in *emptypb.Empty, opts ...grpc.CallOption) (*emptypb.Empty, error)
}

type payorderClient struct {
	cc grpc.ClientConnInterface
}

func NewPayorderClient(cc grpc.ClientConnInterface) PayorderClient {
	return &payorderClient{cc}
}

func (c *payorderClient) StartCheckInProcessOrdersWorker(ctx context.Context, in *emptypb.Empty, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, Payorder_StartCheckInProcessOrdersWorker_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *payorderClient) StartCreateNewPaymentOrdersWorker(ctx context.Context, in *emptypb.Empty, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, Payorder_StartCreateNewPaymentOrdersWorker_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// PayorderServer is the server API for Payorder service.
// All implementations must embed UnimplementedPayorderServer
// for forward compatibility.
type PayorderServer interface {
	StartCheckInProcessOrdersWorker(context.Context, *emptypb.Empty) (*emptypb.Empty, error)
	StartCreateNewPaymentOrdersWorker(context.Context, *emptypb.Empty) (*emptypb.Empty, error)
	mustEmbedUnimplementedPayorderServer()
}

// UnimplementedPayorderServer must be embedded to have
// forward compatible implementations.
//
// NOTE: this should be embedded by value instead of pointer to avoid a nil
// pointer dereference when methods are called.
type UnimplementedPayorderServer struct{}

func (UnimplementedPayorderServer) StartCheckInProcessOrdersWorker(context.Context, *emptypb.Empty) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method StartCheckInProcessOrdersWorker not implemented")
}
func (UnimplementedPayorderServer) StartCreateNewPaymentOrdersWorker(context.Context, *emptypb.Empty) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method StartCreateNewPaymentOrdersWorker not implemented")
}
func (UnimplementedPayorderServer) mustEmbedUnimplementedPayorderServer() {}
func (UnimplementedPayorderServer) testEmbeddedByValue()                  {}

// UnsafePayorderServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to PayorderServer will
// result in compilation errors.
type UnsafePayorderServer interface {
	mustEmbedUnimplementedPayorderServer()
}

func RegisterPayorderServer(s grpc.ServiceRegistrar, srv PayorderServer) {
	// If the following call pancis, it indicates UnimplementedPayorderServer was
	// embedded by pointer and is nil.  This will cause panics if an
	// unimplemented method is ever invoked, so we test this at initialization
	// time to prevent it from happening at runtime later due to I/O.
	if t, ok := srv.(interface{ testEmbeddedByValue() }); ok {
		t.testEmbeddedByValue()
	}
	s.RegisterService(&Payorder_ServiceDesc, srv)
}

func _Payorder_StartCheckInProcessOrdersWorker_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(emptypb.Empty)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PayorderServer).StartCheckInProcessOrdersWorker(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Payorder_StartCheckInProcessOrdersWorker_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PayorderServer).StartCheckInProcessOrdersWorker(ctx, req.(*emptypb.Empty))
	}
	return interceptor(ctx, in, info, handler)
}

func _Payorder_StartCreateNewPaymentOrdersWorker_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(emptypb.Empty)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PayorderServer).StartCreateNewPaymentOrdersWorker(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Payorder_StartCreateNewPaymentOrdersWorker_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PayorderServer).StartCreateNewPaymentOrdersWorker(ctx, req.(*emptypb.Empty))
	}
	return interceptor(ctx, in, info, handler)
}

// Payorder_ServiceDesc is the grpc.ServiceDesc for Payorder service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var Payorder_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "processing.payorder.payorder.Payorder",
	HandlerType: (*PayorderServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "StartCheckInProcessOrdersWorker",
			Handler:    _Payorder_StartCheckInProcessOrdersWorker_Handler,
		},
		{
			MethodName: "StartCreateNewPaymentOrdersWorker",
			Handler:    _Payorder_StartCreateNewPaymentOrdersWorker_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "inner/processing/grpc/payorder.proto",
}
