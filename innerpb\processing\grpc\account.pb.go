// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.5
// 	protoc        v5.29.3
// source: inner/processing/grpc/account.proto

package grpc

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
	structpb "google.golang.org/protobuf/types/known/structpb"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type DateTime struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Year          *string                `protobuf:"bytes,1,opt,name=year" json:"year,omitempty"`
	Month         *string                `protobuf:"bytes,2,opt,name=month" json:"month,omitempty"`
	Day           *string                `protobuf:"bytes,3,opt,name=day" json:"day,omitempty"`
	Hour          *string                `protobuf:"bytes,4,opt,name=hour" json:"hour,omitempty"`
	Minute        *string                `protobuf:"bytes,5,opt,name=minute" json:"minute,omitempty"`
	Second        *string                `protobuf:"bytes,6,opt,name=second" json:"second,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DateTime) Reset() {
	*x = DateTime{}
	mi := &file_inner_processing_grpc_account_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DateTime) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DateTime) ProtoMessage() {}

func (x *DateTime) ProtoReflect() protoreflect.Message {
	mi := &file_inner_processing_grpc_account_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DateTime.ProtoReflect.Descriptor instead.
func (*DateTime) Descriptor() ([]byte, []int) {
	return file_inner_processing_grpc_account_proto_rawDescGZIP(), []int{0}
}

func (x *DateTime) GetYear() string {
	if x != nil && x.Year != nil {
		return *x.Year
	}
	return ""
}

func (x *DateTime) GetMonth() string {
	if x != nil && x.Month != nil {
		return *x.Month
	}
	return ""
}

func (x *DateTime) GetDay() string {
	if x != nil && x.Day != nil {
		return *x.Day
	}
	return ""
}

func (x *DateTime) GetHour() string {
	if x != nil && x.Hour != nil {
		return *x.Hour
	}
	return ""
}

func (x *DateTime) GetMinute() string {
	if x != nil && x.Minute != nil {
		return *x.Minute
	}
	return ""
}

func (x *DateTime) GetSecond() string {
	if x != nil && x.Second != nil {
		return *x.Second
	}
	return ""
}

type AccountData struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            *uint64                `protobuf:"varint,1,opt,name=id" json:"id,omitempty"`
	Number        *string                `protobuf:"bytes,2,opt,name=number" json:"number,omitempty"`
	CurrencyCode  *string                `protobuf:"bytes,3,opt,name=currency_code,json=currencyCode" json:"currency_code,omitempty"`
	Config        *structpb.Struct       `protobuf:"bytes,4,opt,name=config" json:"config,omitempty"`
	BankCode      *string                `protobuf:"bytes,5,opt,name=bank_code,json=bankCode" json:"bank_code,omitempty"`
	BankId        *uint64                `protobuf:"varint,6,opt,name=bank_id,json=bankId" json:"bank_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *AccountData) Reset() {
	*x = AccountData{}
	mi := &file_inner_processing_grpc_account_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AccountData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AccountData) ProtoMessage() {}

func (x *AccountData) ProtoReflect() protoreflect.Message {
	mi := &file_inner_processing_grpc_account_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AccountData.ProtoReflect.Descriptor instead.
func (*AccountData) Descriptor() ([]byte, []int) {
	return file_inner_processing_grpc_account_proto_rawDescGZIP(), []int{1}
}

func (x *AccountData) GetId() uint64 {
	if x != nil && x.Id != nil {
		return *x.Id
	}
	return 0
}

func (x *AccountData) GetNumber() string {
	if x != nil && x.Number != nil {
		return *x.Number
	}
	return ""
}

func (x *AccountData) GetCurrencyCode() string {
	if x != nil && x.CurrencyCode != nil {
		return *x.CurrencyCode
	}
	return ""
}

func (x *AccountData) GetConfig() *structpb.Struct {
	if x != nil {
		return x.Config
	}
	return nil
}

func (x *AccountData) GetBankCode() string {
	if x != nil && x.BankCode != nil {
		return *x.BankCode
	}
	return ""
}

func (x *AccountData) GetBankId() uint64 {
	if x != nil && x.BankId != nil {
		return *x.BankId
	}
	return 0
}

// requests
type GetAccountByIDRequestV1 struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	AccountId     *uint64                `protobuf:"varint,1,opt,name=account_id,json=accountId" json:"account_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetAccountByIDRequestV1) Reset() {
	*x = GetAccountByIDRequestV1{}
	mi := &file_inner_processing_grpc_account_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetAccountByIDRequestV1) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAccountByIDRequestV1) ProtoMessage() {}

func (x *GetAccountByIDRequestV1) ProtoReflect() protoreflect.Message {
	mi := &file_inner_processing_grpc_account_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAccountByIDRequestV1.ProtoReflect.Descriptor instead.
func (*GetAccountByIDRequestV1) Descriptor() ([]byte, []int) {
	return file_inner_processing_grpc_account_proto_rawDescGZIP(), []int{2}
}

func (x *GetAccountByIDRequestV1) GetAccountId() uint64 {
	if x != nil && x.AccountId != nil {
		return *x.AccountId
	}
	return 0
}

type GetAccountByNumberRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	AccountNumber *string                `protobuf:"bytes,1,opt,name=account_number,json=accountNumber" json:"account_number,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetAccountByNumberRequest) Reset() {
	*x = GetAccountByNumberRequest{}
	mi := &file_inner_processing_grpc_account_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetAccountByNumberRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAccountByNumberRequest) ProtoMessage() {}

func (x *GetAccountByNumberRequest) ProtoReflect() protoreflect.Message {
	mi := &file_inner_processing_grpc_account_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAccountByNumberRequest.ProtoReflect.Descriptor instead.
func (*GetAccountByNumberRequest) Descriptor() ([]byte, []int) {
	return file_inner_processing_grpc_account_proto_rawDescGZIP(), []int{3}
}

func (x *GetAccountByNumberRequest) GetAccountNumber() string {
	if x != nil && x.AccountNumber != nil {
		return *x.AccountNumber
	}
	return ""
}

// responses
type GetAccountByNumberResponse struct {
	state           protoimpl.MessageState `protogen:"open.v1"`
	CreatedAt       *string                `protobuf:"bytes,1,opt,name=created_at,json=createdAt" json:"created_at,omitempty"`
	UpdatedAt       *string                `protobuf:"bytes,2,opt,name=updated_at,json=updatedAt" json:"updated_at,omitempty"`
	AccountId       *uint64                `protobuf:"varint,3,opt,name=account_id,json=accountId" json:"account_id,omitempty"`
	AccountNumber   *string                `protobuf:"bytes,4,opt,name=account_number,json=accountNumber" json:"account_number,omitempty"`
	CurrencyCode    *string                `protobuf:"bytes,5,opt,name=currency_code,json=currencyCode" json:"currency_code,omitempty"`
	EncryptedConfig *string                `protobuf:"bytes,6,opt,name=encrypted_config,json=encryptedConfig" json:"encrypted_config,omitempty"`
	BankCode        *string                `protobuf:"bytes,7,opt,name=bank_code,json=bankCode" json:"bank_code,omitempty"`
	BankId          *uint64                `protobuf:"varint,8,opt,name=bank_id,json=bankId" json:"bank_id,omitempty"`
	unknownFields   protoimpl.UnknownFields
	sizeCache       protoimpl.SizeCache
}

func (x *GetAccountByNumberResponse) Reset() {
	*x = GetAccountByNumberResponse{}
	mi := &file_inner_processing_grpc_account_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetAccountByNumberResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAccountByNumberResponse) ProtoMessage() {}

func (x *GetAccountByNumberResponse) ProtoReflect() protoreflect.Message {
	mi := &file_inner_processing_grpc_account_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAccountByNumberResponse.ProtoReflect.Descriptor instead.
func (*GetAccountByNumberResponse) Descriptor() ([]byte, []int) {
	return file_inner_processing_grpc_account_proto_rawDescGZIP(), []int{4}
}

func (x *GetAccountByNumberResponse) GetCreatedAt() string {
	if x != nil && x.CreatedAt != nil {
		return *x.CreatedAt
	}
	return ""
}

func (x *GetAccountByNumberResponse) GetUpdatedAt() string {
	if x != nil && x.UpdatedAt != nil {
		return *x.UpdatedAt
	}
	return ""
}

func (x *GetAccountByNumberResponse) GetAccountId() uint64 {
	if x != nil && x.AccountId != nil {
		return *x.AccountId
	}
	return 0
}

func (x *GetAccountByNumberResponse) GetAccountNumber() string {
	if x != nil && x.AccountNumber != nil {
		return *x.AccountNumber
	}
	return ""
}

func (x *GetAccountByNumberResponse) GetCurrencyCode() string {
	if x != nil && x.CurrencyCode != nil {
		return *x.CurrencyCode
	}
	return ""
}

func (x *GetAccountByNumberResponse) GetEncryptedConfig() string {
	if x != nil && x.EncryptedConfig != nil {
		return *x.EncryptedConfig
	}
	return ""
}

func (x *GetAccountByNumberResponse) GetBankCode() string {
	if x != nil && x.BankCode != nil {
		return *x.BankCode
	}
	return ""
}

func (x *GetAccountByNumberResponse) GetBankId() uint64 {
	if x != nil && x.BankId != nil {
		return *x.BankId
	}
	return 0
}

type GetTransferByIDRequestV1 struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	TransferId    *uint64                `protobuf:"varint,1,opt,name=transfer_id,json=transferId" json:"transfer_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetTransferByIDRequestV1) Reset() {
	*x = GetTransferByIDRequestV1{}
	mi := &file_inner_processing_grpc_account_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetTransferByIDRequestV1) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetTransferByIDRequestV1) ProtoMessage() {}

func (x *GetTransferByIDRequestV1) ProtoReflect() protoreflect.Message {
	mi := &file_inner_processing_grpc_account_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetTransferByIDRequestV1.ProtoReflect.Descriptor instead.
func (*GetTransferByIDRequestV1) Descriptor() ([]byte, []int) {
	return file_inner_processing_grpc_account_proto_rawDescGZIP(), []int{5}
}

func (x *GetTransferByIDRequestV1) GetTransferId() uint64 {
	if x != nil && x.TransferId != nil {
		return *x.TransferId
	}
	return 0
}

type GetTransferByIDResponseV1 struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	TransferId    *uint64                `protobuf:"varint,1,opt,name=transfer_id,json=transferId" json:"transfer_id,omitempty"`
	AccountNumber *string                `protobuf:"bytes,2,opt,name=account_number,json=accountNumber" json:"account_number,omitempty"`
	ProjectId     *uint64                `protobuf:"varint,3,opt,name=project_id,json=projectId" json:"project_id,omitempty"`
	MerchantId    *uint64                `protobuf:"varint,4,opt,name=merchant_id,json=merchantId" json:"merchant_id,omitempty"`
	StatusCode    *string                `protobuf:"bytes,5,opt,name=status_code,json=statusCode" json:"status_code,omitempty"`
	Amount        *float64               `protobuf:"fixed64,6,opt,name=amount" json:"amount,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetTransferByIDResponseV1) Reset() {
	*x = GetTransferByIDResponseV1{}
	mi := &file_inner_processing_grpc_account_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetTransferByIDResponseV1) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetTransferByIDResponseV1) ProtoMessage() {}

func (x *GetTransferByIDResponseV1) ProtoReflect() protoreflect.Message {
	mi := &file_inner_processing_grpc_account_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetTransferByIDResponseV1.ProtoReflect.Descriptor instead.
func (*GetTransferByIDResponseV1) Descriptor() ([]byte, []int) {
	return file_inner_processing_grpc_account_proto_rawDescGZIP(), []int{6}
}

func (x *GetTransferByIDResponseV1) GetTransferId() uint64 {
	if x != nil && x.TransferId != nil {
		return *x.TransferId
	}
	return 0
}

func (x *GetTransferByIDResponseV1) GetAccountNumber() string {
	if x != nil && x.AccountNumber != nil {
		return *x.AccountNumber
	}
	return ""
}

func (x *GetTransferByIDResponseV1) GetProjectId() uint64 {
	if x != nil && x.ProjectId != nil {
		return *x.ProjectId
	}
	return 0
}

func (x *GetTransferByIDResponseV1) GetMerchantId() uint64 {
	if x != nil && x.MerchantId != nil {
		return *x.MerchantId
	}
	return 0
}

func (x *GetTransferByIDResponseV1) GetStatusCode() string {
	if x != nil && x.StatusCode != nil {
		return *x.StatusCode
	}
	return ""
}

func (x *GetTransferByIDResponseV1) GetAmount() float64 {
	if x != nil && x.Amount != nil {
		return *x.Amount
	}
	return 0
}

type CreateTransferRequest struct {
	state              protoimpl.MessageState `protogen:"open.v1"`
	BalanceOwnerId     *uint64                `protobuf:"varint,1,opt,name=balance_owner_id,json=balanceOwnerId" json:"balance_owner_id,omitempty"`
	BalanceOwnerBin    *string                `protobuf:"bytes,2,opt,name=balance_owner_bin,json=balanceOwnerBin" json:"balance_owner_bin,omitempty"`
	AccountNumber      *string                `protobuf:"bytes,5,opt,name=account_number,json=accountNumber" json:"account_number,omitempty"`
	RecipientAccount   *string                `protobuf:"bytes,6,opt,name=recipient_account,json=recipientAccount" json:"recipient_account,omitempty"`
	PaymentPurposeCode *string                `protobuf:"bytes,7,opt,name=payment_purpose_code,json=paymentPurposeCode" json:"payment_purpose_code,omitempty"`
	BeneficiaryCode    *string                `protobuf:"bytes,8,opt,name=beneficiary_code,json=beneficiaryCode" json:"beneficiary_code,omitempty"`
	Amount             *float64               `protobuf:"fixed64,9,opt,name=amount" json:"amount,omitempty"`
	Description        *string                `protobuf:"bytes,10,opt,name=description" json:"description,omitempty"`
	ForeignId          *string                `protobuf:"bytes,11,opt,name=foreign_id,json=foreignId" json:"foreign_id,omitempty"`
	unknownFields      protoimpl.UnknownFields
	sizeCache          protoimpl.SizeCache
}

func (x *CreateTransferRequest) Reset() {
	*x = CreateTransferRequest{}
	mi := &file_inner_processing_grpc_account_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreateTransferRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateTransferRequest) ProtoMessage() {}

func (x *CreateTransferRequest) ProtoReflect() protoreflect.Message {
	mi := &file_inner_processing_grpc_account_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateTransferRequest.ProtoReflect.Descriptor instead.
func (*CreateTransferRequest) Descriptor() ([]byte, []int) {
	return file_inner_processing_grpc_account_proto_rawDescGZIP(), []int{7}
}

func (x *CreateTransferRequest) GetBalanceOwnerId() uint64 {
	if x != nil && x.BalanceOwnerId != nil {
		return *x.BalanceOwnerId
	}
	return 0
}

func (x *CreateTransferRequest) GetBalanceOwnerBin() string {
	if x != nil && x.BalanceOwnerBin != nil {
		return *x.BalanceOwnerBin
	}
	return ""
}

func (x *CreateTransferRequest) GetAccountNumber() string {
	if x != nil && x.AccountNumber != nil {
		return *x.AccountNumber
	}
	return ""
}

func (x *CreateTransferRequest) GetRecipientAccount() string {
	if x != nil && x.RecipientAccount != nil {
		return *x.RecipientAccount
	}
	return ""
}

func (x *CreateTransferRequest) GetPaymentPurposeCode() string {
	if x != nil && x.PaymentPurposeCode != nil {
		return *x.PaymentPurposeCode
	}
	return ""
}

func (x *CreateTransferRequest) GetBeneficiaryCode() string {
	if x != nil && x.BeneficiaryCode != nil {
		return *x.BeneficiaryCode
	}
	return ""
}

func (x *CreateTransferRequest) GetAmount() float64 {
	if x != nil && x.Amount != nil {
		return *x.Amount
	}
	return 0
}

func (x *CreateTransferRequest) GetDescription() string {
	if x != nil && x.Description != nil {
		return *x.Description
	}
	return ""
}

func (x *CreateTransferRequest) GetForeignId() string {
	if x != nil && x.ForeignId != nil {
		return *x.ForeignId
	}
	return ""
}

type CreateTransferResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            *uint64                `protobuf:"varint,1,opt,name=id" json:"id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CreateTransferResponse) Reset() {
	*x = CreateTransferResponse{}
	mi := &file_inner_processing_grpc_account_proto_msgTypes[8]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreateTransferResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateTransferResponse) ProtoMessage() {}

func (x *CreateTransferResponse) ProtoReflect() protoreflect.Message {
	mi := &file_inner_processing_grpc_account_proto_msgTypes[8]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateTransferResponse.ProtoReflect.Descriptor instead.
func (*CreateTransferResponse) Descriptor() ([]byte, []int) {
	return file_inner_processing_grpc_account_proto_rawDescGZIP(), []int{8}
}

func (x *CreateTransferResponse) GetId() uint64 {
	if x != nil && x.Id != nil {
		return *x.Id
	}
	return 0
}

type ExtractFailOrCanceledTransfersByForeignIDsRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	ForeignIds    []string               `protobuf:"bytes,1,rep,name=foreign_ids,json=foreignIds" json:"foreign_ids,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ExtractFailOrCanceledTransfersByForeignIDsRequest) Reset() {
	*x = ExtractFailOrCanceledTransfersByForeignIDsRequest{}
	mi := &file_inner_processing_grpc_account_proto_msgTypes[9]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ExtractFailOrCanceledTransfersByForeignIDsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ExtractFailOrCanceledTransfersByForeignIDsRequest) ProtoMessage() {}

func (x *ExtractFailOrCanceledTransfersByForeignIDsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_inner_processing_grpc_account_proto_msgTypes[9]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ExtractFailOrCanceledTransfersByForeignIDsRequest.ProtoReflect.Descriptor instead.
func (*ExtractFailOrCanceledTransfersByForeignIDsRequest) Descriptor() ([]byte, []int) {
	return file_inner_processing_grpc_account_proto_rawDescGZIP(), []int{9}
}

func (x *ExtractFailOrCanceledTransfersByForeignIDsRequest) GetForeignIds() []string {
	if x != nil {
		return x.ForeignIds
	}
	return nil
}

type ExtractSuccessTransfersByForeignIDsRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	ForeignIds    []string               `protobuf:"bytes,1,rep,name=foreign_ids,json=foreignIds" json:"foreign_ids,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ExtractSuccessTransfersByForeignIDsRequest) Reset() {
	*x = ExtractSuccessTransfersByForeignIDsRequest{}
	mi := &file_inner_processing_grpc_account_proto_msgTypes[10]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ExtractSuccessTransfersByForeignIDsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ExtractSuccessTransfersByForeignIDsRequest) ProtoMessage() {}

func (x *ExtractSuccessTransfersByForeignIDsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_inner_processing_grpc_account_proto_msgTypes[10]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ExtractSuccessTransfersByForeignIDsRequest.ProtoReflect.Descriptor instead.
func (*ExtractSuccessTransfersByForeignIDsRequest) Descriptor() ([]byte, []int) {
	return file_inner_processing_grpc_account_proto_rawDescGZIP(), []int{10}
}

func (x *ExtractSuccessTransfersByForeignIDsRequest) GetForeignIds() []string {
	if x != nil {
		return x.ForeignIds
	}
	return nil
}

type ExtractTransfersByForeignIDsResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	ForeignIds    []string               `protobuf:"bytes,1,rep,name=foreign_ids,json=foreignIds" json:"foreign_ids,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ExtractTransfersByForeignIDsResponse) Reset() {
	*x = ExtractTransfersByForeignIDsResponse{}
	mi := &file_inner_processing_grpc_account_proto_msgTypes[11]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ExtractTransfersByForeignIDsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ExtractTransfersByForeignIDsResponse) ProtoMessage() {}

func (x *ExtractTransfersByForeignIDsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_inner_processing_grpc_account_proto_msgTypes[11]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ExtractTransfersByForeignIDsResponse.ProtoReflect.Descriptor instead.
func (*ExtractTransfersByForeignIDsResponse) Descriptor() ([]byte, []int) {
	return file_inner_processing_grpc_account_proto_rawDescGZIP(), []int{11}
}

func (x *ExtractTransfersByForeignIDsResponse) GetForeignIds() []string {
	if x != nil {
		return x.ForeignIds
	}
	return nil
}

type GetTransferByForeignIDRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	ForeignId     *string                `protobuf:"bytes,1,opt,name=foreign_id,json=foreignId" json:"foreign_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetTransferByForeignIDRequest) Reset() {
	*x = GetTransferByForeignIDRequest{}
	mi := &file_inner_processing_grpc_account_proto_msgTypes[12]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetTransferByForeignIDRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetTransferByForeignIDRequest) ProtoMessage() {}

func (x *GetTransferByForeignIDRequest) ProtoReflect() protoreflect.Message {
	mi := &file_inner_processing_grpc_account_proto_msgTypes[12]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetTransferByForeignIDRequest.ProtoReflect.Descriptor instead.
func (*GetTransferByForeignIDRequest) Descriptor() ([]byte, []int) {
	return file_inner_processing_grpc_account_proto_rawDescGZIP(), []int{12}
}

func (x *GetTransferByForeignIDRequest) GetForeignId() string {
	if x != nil && x.ForeignId != nil {
		return *x.ForeignId
	}
	return ""
}

type GetTransferResponse struct {
	state              protoimpl.MessageState `protogen:"open.v1"`
	Id                 *uint64                `protobuf:"varint,1,opt,name=id" json:"id,omitempty"`
	AcquirerId         *uint64                `protobuf:"varint,2,opt,name=acquirer_id,json=acquirerId" json:"acquirer_id,omitempty"`
	AccountId          *uint64                `protobuf:"varint,3,opt,name=account_id,json=accountId" json:"account_id,omitempty"`
	ProjectId          *uint64                `protobuf:"varint,4,opt,name=project_id,json=projectId" json:"project_id,omitempty"`
	MerchantId         *uint64                `protobuf:"varint,5,opt,name=merchant_id,json=merchantId" json:"merchant_id,omitempty"`
	Status             *GetStatusResponse     `protobuf:"bytes,6,opt,name=status" json:"status,omitempty"`
	Amount             *float64               `protobuf:"fixed64,7,opt,name=amount" json:"amount,omitempty"`
	MerchantBin        *string                `protobuf:"bytes,8,opt,name=merchant_bin,json=merchantBin" json:"merchant_bin,omitempty"`
	MerchantAccount    *string                `protobuf:"bytes,9,opt,name=merchant_account,json=merchantAccount" json:"merchant_account,omitempty"`
	BeneficiaryCode    *string                `protobuf:"bytes,10,opt,name=beneficiary_code,json=beneficiaryCode" json:"beneficiary_code,omitempty"`
	Description        *string                `protobuf:"bytes,11,opt,name=description" json:"description,omitempty"`
	PaymentPurposeCode *string                `protobuf:"bytes,12,opt,name=payment_purpose_code,json=paymentPurposeCode" json:"payment_purpose_code,omitempty"`
	TransferTypeId     *uint64                `protobuf:"varint,13,opt,name=transfer_type_id,json=transferTypeId" json:"transfer_type_id,omitempty"`
	EntityTypeId       *uint64                `protobuf:"varint,14,opt,name=entity_type_id,json=entityTypeId" json:"entity_type_id,omitempty"`
	CountryCodeId      *uint64                `protobuf:"varint,15,opt,name=country_code_id,json=countryCodeId" json:"country_code_id,omitempty"`
	ForeignId          *string                `protobuf:"bytes,16,opt,name=foreign_id,json=foreignId" json:"foreign_id,omitempty"`
	unknownFields      protoimpl.UnknownFields
	sizeCache          protoimpl.SizeCache
}

func (x *GetTransferResponse) Reset() {
	*x = GetTransferResponse{}
	mi := &file_inner_processing_grpc_account_proto_msgTypes[13]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetTransferResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetTransferResponse) ProtoMessage() {}

func (x *GetTransferResponse) ProtoReflect() protoreflect.Message {
	mi := &file_inner_processing_grpc_account_proto_msgTypes[13]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetTransferResponse.ProtoReflect.Descriptor instead.
func (*GetTransferResponse) Descriptor() ([]byte, []int) {
	return file_inner_processing_grpc_account_proto_rawDescGZIP(), []int{13}
}

func (x *GetTransferResponse) GetId() uint64 {
	if x != nil && x.Id != nil {
		return *x.Id
	}
	return 0
}

func (x *GetTransferResponse) GetAcquirerId() uint64 {
	if x != nil && x.AcquirerId != nil {
		return *x.AcquirerId
	}
	return 0
}

func (x *GetTransferResponse) GetAccountId() uint64 {
	if x != nil && x.AccountId != nil {
		return *x.AccountId
	}
	return 0
}

func (x *GetTransferResponse) GetProjectId() uint64 {
	if x != nil && x.ProjectId != nil {
		return *x.ProjectId
	}
	return 0
}

func (x *GetTransferResponse) GetMerchantId() uint64 {
	if x != nil && x.MerchantId != nil {
		return *x.MerchantId
	}
	return 0
}

func (x *GetTransferResponse) GetStatus() *GetStatusResponse {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *GetTransferResponse) GetAmount() float64 {
	if x != nil && x.Amount != nil {
		return *x.Amount
	}
	return 0
}

func (x *GetTransferResponse) GetMerchantBin() string {
	if x != nil && x.MerchantBin != nil {
		return *x.MerchantBin
	}
	return ""
}

func (x *GetTransferResponse) GetMerchantAccount() string {
	if x != nil && x.MerchantAccount != nil {
		return *x.MerchantAccount
	}
	return ""
}

func (x *GetTransferResponse) GetBeneficiaryCode() string {
	if x != nil && x.BeneficiaryCode != nil {
		return *x.BeneficiaryCode
	}
	return ""
}

func (x *GetTransferResponse) GetDescription() string {
	if x != nil && x.Description != nil {
		return *x.Description
	}
	return ""
}

func (x *GetTransferResponse) GetPaymentPurposeCode() string {
	if x != nil && x.PaymentPurposeCode != nil {
		return *x.PaymentPurposeCode
	}
	return ""
}

func (x *GetTransferResponse) GetTransferTypeId() uint64 {
	if x != nil && x.TransferTypeId != nil {
		return *x.TransferTypeId
	}
	return 0
}

func (x *GetTransferResponse) GetEntityTypeId() uint64 {
	if x != nil && x.EntityTypeId != nil {
		return *x.EntityTypeId
	}
	return 0
}

func (x *GetTransferResponse) GetCountryCodeId() uint64 {
	if x != nil && x.CountryCodeId != nil {
		return *x.CountryCodeId
	}
	return 0
}

func (x *GetTransferResponse) GetForeignId() string {
	if x != nil && x.ForeignId != nil {
		return *x.ForeignId
	}
	return ""
}

type GetStatusResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            *uint64                `protobuf:"varint,1,opt,name=id" json:"id,omitempty"`
	Code          *string                `protobuf:"bytes,2,opt,name=code" json:"code,omitempty"`
	Name          *string                `protobuf:"bytes,3,opt,name=name" json:"name,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetStatusResponse) Reset() {
	*x = GetStatusResponse{}
	mi := &file_inner_processing_grpc_account_proto_msgTypes[14]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetStatusResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetStatusResponse) ProtoMessage() {}

func (x *GetStatusResponse) ProtoReflect() protoreflect.Message {
	mi := &file_inner_processing_grpc_account_proto_msgTypes[14]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetStatusResponse.ProtoReflect.Descriptor instead.
func (*GetStatusResponse) Descriptor() ([]byte, []int) {
	return file_inner_processing_grpc_account_proto_rawDescGZIP(), []int{14}
}

func (x *GetStatusResponse) GetId() uint64 {
	if x != nil && x.Id != nil {
		return *x.Id
	}
	return 0
}

func (x *GetStatusResponse) GetCode() string {
	if x != nil && x.Code != nil {
		return *x.Code
	}
	return ""
}

func (x *GetStatusResponse) GetName() string {
	if x != nil && x.Name != nil {
		return *x.Name
	}
	return ""
}

var File_inner_processing_grpc_account_proto protoreflect.FileDescriptor

var file_inner_processing_grpc_account_proto_rawDesc = string([]byte{
	0x0a, 0x23, 0x69, 0x6e, 0x6e, 0x65, 0x72, 0x2f, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69,
	0x6e, 0x67, 0x2f, 0x67, 0x72, 0x70, 0x63, 0x2f, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x1a, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e,
	0x67, 0x2e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e,
	0x74, 0x1a, 0x1c, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62,
	0x75, 0x66, 0x2f, 0x73, 0x74, 0x72, 0x75, 0x63, 0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a,
	0x1b, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66,
	0x2f, 0x65, 0x6d, 0x70, 0x74, 0x79, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x8a, 0x01, 0x0a,
	0x08, 0x44, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x79, 0x65, 0x61,
	0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x79, 0x65, 0x61, 0x72, 0x12, 0x14, 0x0a,
	0x05, 0x6d, 0x6f, 0x6e, 0x74, 0x68, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x6d, 0x6f,
	0x6e, 0x74, 0x68, 0x12, 0x10, 0x0a, 0x03, 0x64, 0x61, 0x79, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x03, 0x64, 0x61, 0x79, 0x12, 0x12, 0x0a, 0x04, 0x68, 0x6f, 0x75, 0x72, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x04, 0x68, 0x6f, 0x75, 0x72, 0x12, 0x16, 0x0a, 0x06, 0x6d, 0x69, 0x6e,
	0x75, 0x74, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x6d, 0x69, 0x6e, 0x75, 0x74,
	0x65, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x65, 0x63, 0x6f, 0x6e, 0x64, 0x18, 0x06, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x06, 0x73, 0x65, 0x63, 0x6f, 0x6e, 0x64, 0x22, 0xc1, 0x01, 0x0a, 0x0b, 0x41, 0x63,
	0x63, 0x6f, 0x75, 0x6e, 0x74, 0x44, 0x61, 0x74, 0x61, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x02, 0x69, 0x64, 0x12, 0x16, 0x0a, 0x06, 0x6e, 0x75, 0x6d,
	0x62, 0x65, 0x72, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x6e, 0x75, 0x6d, 0x62, 0x65,
	0x72, 0x12, 0x23, 0x0a, 0x0d, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x63, 0x79, 0x5f, 0x63, 0x6f,
	0x64, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e,
	0x63, 0x79, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x2f, 0x0a, 0x06, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x17, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x53, 0x74, 0x72, 0x75, 0x63, 0x74, 0x52,
	0x06, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x1b, 0x0a, 0x09, 0x62, 0x61, 0x6e, 0x6b, 0x5f,
	0x63, 0x6f, 0x64, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x62, 0x61, 0x6e, 0x6b,
	0x43, 0x6f, 0x64, 0x65, 0x12, 0x17, 0x0a, 0x07, 0x62, 0x61, 0x6e, 0x6b, 0x5f, 0x69, 0x64, 0x18,
	0x06, 0x20, 0x01, 0x28, 0x04, 0x52, 0x06, 0x62, 0x61, 0x6e, 0x6b, 0x49, 0x64, 0x22, 0x38, 0x0a,
	0x17, 0x47, 0x65, 0x74, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x42, 0x79, 0x49, 0x44, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x56, 0x31, 0x12, 0x1d, 0x0a, 0x0a, 0x61, 0x63, 0x63, 0x6f,
	0x75, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x09, 0x61, 0x63,
	0x63, 0x6f, 0x75, 0x6e, 0x74, 0x49, 0x64, 0x22, 0x42, 0x0a, 0x19, 0x47, 0x65, 0x74, 0x41, 0x63,
	0x63, 0x6f, 0x75, 0x6e, 0x74, 0x42, 0x79, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x12, 0x25, 0x0a, 0x0e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f,
	0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x61, 0x63,
	0x63, 0x6f, 0x75, 0x6e, 0x74, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x22, 0xa6, 0x02, 0x0a, 0x1a,
	0x47, 0x65, 0x74, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x42, 0x79, 0x4e, 0x75, 0x6d, 0x62,
	0x65, 0x72, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x63, 0x72,
	0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09,
	0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x12, 0x1d, 0x0a, 0x0a, 0x75, 0x70, 0x64,
	0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x75,
	0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x12, 0x1d, 0x0a, 0x0a, 0x61, 0x63, 0x63, 0x6f,
	0x75, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x04, 0x52, 0x09, 0x61, 0x63,
	0x63, 0x6f, 0x75, 0x6e, 0x74, 0x49, 0x64, 0x12, 0x25, 0x0a, 0x0e, 0x61, 0x63, 0x63, 0x6f, 0x75,
	0x6e, 0x74, 0x5f, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0d, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x12, 0x23,
	0x0a, 0x0d, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x63, 0x79, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18,
	0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x63, 0x79, 0x43,
	0x6f, 0x64, 0x65, 0x12, 0x29, 0x0a, 0x10, 0x65, 0x6e, 0x63, 0x72, 0x79, 0x70, 0x74, 0x65, 0x64,
	0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f, 0x65,
	0x6e, 0x63, 0x72, 0x79, 0x70, 0x74, 0x65, 0x64, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x1b,
	0x0a, 0x09, 0x62, 0x61, 0x6e, 0x6b, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x08, 0x62, 0x61, 0x6e, 0x6b, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x17, 0x0a, 0x07, 0x62,
	0x61, 0x6e, 0x6b, 0x5f, 0x69, 0x64, 0x18, 0x08, 0x20, 0x01, 0x28, 0x04, 0x52, 0x06, 0x62, 0x61,
	0x6e, 0x6b, 0x49, 0x64, 0x22, 0x3b, 0x0a, 0x18, 0x47, 0x65, 0x74, 0x54, 0x72, 0x61, 0x6e, 0x73,
	0x66, 0x65, 0x72, 0x42, 0x79, 0x49, 0x44, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x56, 0x31,
	0x12, 0x1f, 0x0a, 0x0b, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x66, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x0a, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x66, 0x65, 0x72, 0x49,
	0x64, 0x22, 0xdc, 0x01, 0x0a, 0x19, 0x47, 0x65, 0x74, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x66, 0x65,
	0x72, 0x42, 0x79, 0x49, 0x44, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x56, 0x31, 0x12,
	0x1f, 0x0a, 0x0b, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x66, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x04, 0x52, 0x0a, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x66, 0x65, 0x72, 0x49, 0x64,
	0x12, 0x25, 0x0a, 0x0e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x6e, 0x75, 0x6d, 0x62,
	0x65, 0x72, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e,
	0x74, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x12, 0x1d, 0x0a, 0x0a, 0x70, 0x72, 0x6f, 0x6a, 0x65,
	0x63, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x04, 0x52, 0x09, 0x70, 0x72, 0x6f,
	0x6a, 0x65, 0x63, 0x74, 0x49, 0x64, 0x12, 0x1f, 0x0a, 0x0b, 0x6d, 0x65, 0x72, 0x63, 0x68, 0x61,
	0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x04, 0x52, 0x0a, 0x6d, 0x65, 0x72,
	0x63, 0x68, 0x61, 0x6e, 0x74, 0x49, 0x64, 0x12, 0x1f, 0x0a, 0x0b, 0x73, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x73, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x61, 0x6d, 0x6f, 0x75,
	0x6e, 0x74, 0x18, 0x06, 0x20, 0x01, 0x28, 0x01, 0x52, 0x06, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74,
	0x22, 0xf7, 0x02, 0x0a, 0x15, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x54, 0x72, 0x61, 0x6e, 0x73,
	0x66, 0x65, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x28, 0x0a, 0x10, 0x62, 0x61,
	0x6c, 0x61, 0x6e, 0x63, 0x65, 0x5f, 0x6f, 0x77, 0x6e, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x04, 0x52, 0x0e, 0x62, 0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65, 0x4f, 0x77, 0x6e,
	0x65, 0x72, 0x49, 0x64, 0x12, 0x2a, 0x0a, 0x11, 0x62, 0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65, 0x5f,
	0x6f, 0x77, 0x6e, 0x65, 0x72, 0x5f, 0x62, 0x69, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0f, 0x62, 0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65, 0x4f, 0x77, 0x6e, 0x65, 0x72, 0x42, 0x69, 0x6e,
	0x12, 0x25, 0x0a, 0x0e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x6e, 0x75, 0x6d, 0x62,
	0x65, 0x72, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e,
	0x74, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x12, 0x2b, 0x0a, 0x11, 0x72, 0x65, 0x63, 0x69, 0x70,
	0x69, 0x65, 0x6e, 0x74, 0x5f, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x06, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x10, 0x72, 0x65, 0x63, 0x69, 0x70, 0x69, 0x65, 0x6e, 0x74, 0x41, 0x63, 0x63,
	0x6f, 0x75, 0x6e, 0x74, 0x12, 0x30, 0x0a, 0x14, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x5f,
	0x70, 0x75, 0x72, 0x70, 0x6f, 0x73, 0x65, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x07, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x12, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x50, 0x75, 0x72, 0x70, 0x6f,
	0x73, 0x65, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x29, 0x0a, 0x10, 0x62, 0x65, 0x6e, 0x65, 0x66, 0x69,
	0x63, 0x69, 0x61, 0x72, 0x79, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0f, 0x62, 0x65, 0x6e, 0x65, 0x66, 0x69, 0x63, 0x69, 0x61, 0x72, 0x79, 0x43, 0x6f, 0x64,
	0x65, 0x12, 0x16, 0x0a, 0x06, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x09, 0x20, 0x01, 0x28,
	0x01, 0x52, 0x06, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x20, 0x0a, 0x0b, 0x64, 0x65, 0x73,
	0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b,
	0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x1d, 0x0a, 0x0a, 0x66,
	0x6f, 0x72, 0x65, 0x69, 0x67, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x09, 0x66, 0x6f, 0x72, 0x65, 0x69, 0x67, 0x6e, 0x49, 0x64, 0x22, 0x28, 0x0a, 0x16, 0x43, 0x72,
	0x65, 0x61, 0x74, 0x65, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x66, 0x65, 0x72, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04,
	0x52, 0x02, 0x69, 0x64, 0x22, 0x54, 0x0a, 0x31, 0x45, 0x78, 0x74, 0x72, 0x61, 0x63, 0x74, 0x46,
	0x61, 0x69, 0x6c, 0x4f, 0x72, 0x43, 0x61, 0x6e, 0x63, 0x65, 0x6c, 0x65, 0x64, 0x54, 0x72, 0x61,
	0x6e, 0x73, 0x66, 0x65, 0x72, 0x73, 0x42, 0x79, 0x46, 0x6f, 0x72, 0x65, 0x69, 0x67, 0x6e, 0x49,
	0x44, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1f, 0x0a, 0x0b, 0x66, 0x6f, 0x72,
	0x65, 0x69, 0x67, 0x6e, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0a,
	0x66, 0x6f, 0x72, 0x65, 0x69, 0x67, 0x6e, 0x49, 0x64, 0x73, 0x22, 0x4d, 0x0a, 0x2a, 0x45, 0x78,
	0x74, 0x72, 0x61, 0x63, 0x74, 0x53, 0x75, 0x63, 0x63, 0x65, 0x73, 0x73, 0x54, 0x72, 0x61, 0x6e,
	0x73, 0x66, 0x65, 0x72, 0x73, 0x42, 0x79, 0x46, 0x6f, 0x72, 0x65, 0x69, 0x67, 0x6e, 0x49, 0x44,
	0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1f, 0x0a, 0x0b, 0x66, 0x6f, 0x72, 0x65,
	0x69, 0x67, 0x6e, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0a, 0x66,
	0x6f, 0x72, 0x65, 0x69, 0x67, 0x6e, 0x49, 0x64, 0x73, 0x22, 0x47, 0x0a, 0x24, 0x45, 0x78, 0x74,
	0x72, 0x61, 0x63, 0x74, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x66, 0x65, 0x72, 0x73, 0x42, 0x79, 0x46,
	0x6f, 0x72, 0x65, 0x69, 0x67, 0x6e, 0x49, 0x44, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x12, 0x1f, 0x0a, 0x0b, 0x66, 0x6f, 0x72, 0x65, 0x69, 0x67, 0x6e, 0x5f, 0x69, 0x64, 0x73,
	0x18, 0x01, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0a, 0x66, 0x6f, 0x72, 0x65, 0x69, 0x67, 0x6e, 0x49,
	0x64, 0x73, 0x22, 0x3e, 0x0a, 0x1d, 0x47, 0x65, 0x74, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x66, 0x65,
	0x72, 0x42, 0x79, 0x46, 0x6f, 0x72, 0x65, 0x69, 0x67, 0x6e, 0x49, 0x44, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x12, 0x1d, 0x0a, 0x0a, 0x66, 0x6f, 0x72, 0x65, 0x69, 0x67, 0x6e, 0x5f, 0x69,
	0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x66, 0x6f, 0x72, 0x65, 0x69, 0x67, 0x6e,
	0x49, 0x64, 0x22, 0xe8, 0x04, 0x0a, 0x13, 0x47, 0x65, 0x74, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x66,
	0x65, 0x72, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x02, 0x69, 0x64, 0x12, 0x1f, 0x0a, 0x0b, 0x61, 0x63,
	0x71, 0x75, 0x69, 0x72, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x04, 0x52,
	0x0a, 0x61, 0x63, 0x71, 0x75, 0x69, 0x72, 0x65, 0x72, 0x49, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x61,
	0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x04, 0x52,
	0x09, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x49, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x70, 0x72,
	0x6f, 0x6a, 0x65, 0x63, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x04, 0x52, 0x09,
	0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x49, 0x64, 0x12, 0x1f, 0x0a, 0x0b, 0x6d, 0x65, 0x72,
	0x63, 0x68, 0x61, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x04, 0x52, 0x0a,
	0x6d, 0x65, 0x72, 0x63, 0x68, 0x61, 0x6e, 0x74, 0x49, 0x64, 0x12, 0x45, 0x0a, 0x06, 0x73, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2d, 0x2e, 0x70, 0x72, 0x6f,
	0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x2e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e,
	0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x47, 0x65, 0x74, 0x53, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x12, 0x16, 0x0a, 0x06, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x07, 0x20, 0x01, 0x28,
	0x01, 0x52, 0x06, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x21, 0x0a, 0x0c, 0x6d, 0x65, 0x72,
	0x63, 0x68, 0x61, 0x6e, 0x74, 0x5f, 0x62, 0x69, 0x6e, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0b, 0x6d, 0x65, 0x72, 0x63, 0x68, 0x61, 0x6e, 0x74, 0x42, 0x69, 0x6e, 0x12, 0x29, 0x0a, 0x10,
	0x6d, 0x65, 0x72, 0x63, 0x68, 0x61, 0x6e, 0x74, 0x5f, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74,
	0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f, 0x6d, 0x65, 0x72, 0x63, 0x68, 0x61, 0x6e, 0x74,
	0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x29, 0x0a, 0x10, 0x62, 0x65, 0x6e, 0x65, 0x66,
	0x69, 0x63, 0x69, 0x61, 0x72, 0x79, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x0a, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0f, 0x62, 0x65, 0x6e, 0x65, 0x66, 0x69, 0x63, 0x69, 0x61, 0x72, 0x79, 0x43, 0x6f,
	0x64, 0x65, 0x12, 0x20, 0x0a, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f,
	0x6e, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70,
	0x74, 0x69, 0x6f, 0x6e, 0x12, 0x30, 0x0a, 0x14, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x5f,
	0x70, 0x75, 0x72, 0x70, 0x6f, 0x73, 0x65, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x0c, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x12, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x50, 0x75, 0x72, 0x70, 0x6f,
	0x73, 0x65, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x28, 0x0a, 0x10, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x66,
	0x65, 0x72, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x04,
	0x52, 0x0e, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x66, 0x65, 0x72, 0x54, 0x79, 0x70, 0x65, 0x49, 0x64,
	0x12, 0x24, 0x0a, 0x0e, 0x65, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x5f,
	0x69, 0x64, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x04, 0x52, 0x0c, 0x65, 0x6e, 0x74, 0x69, 0x74, 0x79,
	0x54, 0x79, 0x70, 0x65, 0x49, 0x64, 0x12, 0x26, 0x0a, 0x0f, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x72,
	0x79, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x04, 0x52,
	0x0d, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x72, 0x79, 0x43, 0x6f, 0x64, 0x65, 0x49, 0x64, 0x12, 0x1d,
	0x0a, 0x0a, 0x66, 0x6f, 0x72, 0x65, 0x69, 0x67, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x10, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x09, 0x66, 0x6f, 0x72, 0x65, 0x69, 0x67, 0x6e, 0x49, 0x64, 0x22, 0x4b, 0x0a,
	0x11, 0x47, 0x65, 0x74, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x02,
	0x69, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x32, 0xc9, 0x0b, 0x0a, 0x07, 0x41,
	0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x82, 0x01, 0x0a, 0x11, 0x47, 0x65, 0x74, 0x54, 0x72,
	0x61, 0x6e, 0x73, 0x66, 0x65, 0x72, 0x42, 0x79, 0x49, 0x44, 0x56, 0x31, 0x12, 0x34, 0x2e, 0x70,
	0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x2e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e,
	0x74, 0x2e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x47, 0x65, 0x74, 0x54, 0x72, 0x61,
	0x6e, 0x73, 0x66, 0x65, 0x72, 0x42, 0x79, 0x49, 0x44, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x56, 0x31, 0x1a, 0x35, 0x2e, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x2e,
	0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e,
	0x47, 0x65, 0x74, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x66, 0x65, 0x72, 0x42, 0x79, 0x49, 0x44, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x56, 0x31, 0x22, 0x00, 0x12, 0x70, 0x0a, 0x0e, 0x47,
	0x65, 0x74, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x42, 0x79, 0x49, 0x44, 0x12, 0x33, 0x2e,
	0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x2e, 0x61, 0x63, 0x63, 0x6f, 0x75,
	0x6e, 0x74, 0x2e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x47, 0x65, 0x74, 0x41, 0x63,
	0x63, 0x6f, 0x75, 0x6e, 0x74, 0x42, 0x79, 0x49, 0x44, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x56, 0x31, 0x1a, 0x27, 0x2e, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x2e,
	0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e,
	0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x44, 0x61, 0x74, 0x61, 0x22, 0x00, 0x12, 0x79, 0x0a,
	0x0e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x66, 0x65, 0x72, 0x12,
	0x31, 0x2e, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x2e, 0x61, 0x63, 0x63,
	0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x43, 0x72, 0x65,
	0x61, 0x74, 0x65, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x66, 0x65, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x1a, 0x32, 0x2e, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x2e,
	0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e,
	0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x66, 0x65, 0x72, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0xbf, 0x01, 0x0a, 0x2a, 0x45, 0x78, 0x74,
	0x72, 0x61, 0x63, 0x74, 0x46, 0x61, 0x69, 0x6c, 0x4f, 0x72, 0x43, 0x61, 0x6e, 0x63, 0x65, 0x6c,
	0x65, 0x64, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x66, 0x65, 0x72, 0x73, 0x42, 0x79, 0x46, 0x6f, 0x72,
	0x65, 0x69, 0x67, 0x6e, 0x49, 0x44, 0x73, 0x12, 0x4d, 0x2e, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73,
	0x73, 0x69, 0x6e, 0x67, 0x2e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x61, 0x63, 0x63,
	0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x45, 0x78, 0x74, 0x72, 0x61, 0x63, 0x74, 0x46, 0x61, 0x69, 0x6c,
	0x4f, 0x72, 0x43, 0x61, 0x6e, 0x63, 0x65, 0x6c, 0x65, 0x64, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x66,
	0x65, 0x72, 0x73, 0x42, 0x79, 0x46, 0x6f, 0x72, 0x65, 0x69, 0x67, 0x6e, 0x49, 0x44, 0x73, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x40, 0x2e, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73,
	0x69, 0x6e, 0x67, 0x2e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x61, 0x63, 0x63, 0x6f,
	0x75, 0x6e, 0x74, 0x2e, 0x45, 0x78, 0x74, 0x72, 0x61, 0x63, 0x74, 0x54, 0x72, 0x61, 0x6e, 0x73,
	0x66, 0x65, 0x72, 0x73, 0x42, 0x79, 0x46, 0x6f, 0x72, 0x65, 0x69, 0x67, 0x6e, 0x49, 0x44, 0x73,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0xb1, 0x01, 0x0a, 0x23, 0x45,
	0x78, 0x74, 0x72, 0x61, 0x63, 0x74, 0x53, 0x75, 0x63, 0x63, 0x65, 0x73, 0x73, 0x54, 0x72, 0x61,
	0x6e, 0x73, 0x66, 0x65, 0x72, 0x73, 0x42, 0x79, 0x46, 0x6f, 0x72, 0x65, 0x69, 0x67, 0x6e, 0x49,
	0x44, 0x73, 0x12, 0x46, 0x2e, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x2e,
	0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e,
	0x45, 0x78, 0x74, 0x72, 0x61, 0x63, 0x74, 0x53, 0x75, 0x63, 0x63, 0x65, 0x73, 0x73, 0x54, 0x72,
	0x61, 0x6e, 0x73, 0x66, 0x65, 0x72, 0x73, 0x42, 0x79, 0x46, 0x6f, 0x72, 0x65, 0x69, 0x67, 0x6e,
	0x49, 0x44, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x40, 0x2e, 0x70, 0x72, 0x6f,
	0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x2e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e,
	0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x45, 0x78, 0x74, 0x72, 0x61, 0x63, 0x74, 0x54,
	0x72, 0x61, 0x6e, 0x73, 0x66, 0x65, 0x72, 0x73, 0x42, 0x79, 0x46, 0x6f, 0x72, 0x65, 0x69, 0x67,
	0x6e, 0x49, 0x44, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x86,
	0x01, 0x0a, 0x16, 0x47, 0x65, 0x74, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x66, 0x65, 0x72, 0x42, 0x79,
	0x46, 0x6f, 0x72, 0x65, 0x69, 0x67, 0x6e, 0x49, 0x44, 0x12, 0x39, 0x2e, 0x70, 0x72, 0x6f, 0x63,
	0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x2e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x61,
	0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x47, 0x65, 0x74, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x66,
	0x65, 0x72, 0x42, 0x79, 0x46, 0x6f, 0x72, 0x65, 0x69, 0x67, 0x6e, 0x49, 0x44, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x1a, 0x2f, 0x2e, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e,
	0x67, 0x2e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e,
	0x74, 0x2e, 0x47, 0x65, 0x74, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x66, 0x65, 0x72, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x63, 0x0a, 0x18, 0x47, 0x65, 0x74, 0x53, 0x75,
	0x63, 0x63, 0x65, 0x73, 0x73, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x66, 0x65, 0x72, 0x53, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x12, 0x16, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x1a, 0x2d, 0x2e, 0x70, 0x72,
	0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x2e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74,
	0x2e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x47, 0x65, 0x74, 0x53, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x85, 0x01, 0x0a,
	0x12, 0x47, 0x65, 0x74, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x42, 0x79, 0x4e, 0x75, 0x6d,
	0x62, 0x65, 0x72, 0x12, 0x35, 0x2e, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67,
	0x2e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74,
	0x2e, 0x47, 0x65, 0x74, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x42, 0x79, 0x4e, 0x75, 0x6d,
	0x62, 0x65, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x36, 0x2e, 0x70, 0x72, 0x6f,
	0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x2e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e,
	0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x47, 0x65, 0x74, 0x41, 0x63, 0x63, 0x6f, 0x75,
	0x6e, 0x74, 0x42, 0x79, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x22, 0x00, 0x12, 0x55, 0x0a, 0x21, 0x53, 0x74, 0x61, 0x72, 0x74, 0x46, 0x69, 0x6e,
	0x61, 0x6c, 0x69, 0x7a, 0x69, 0x6e, 0x67, 0x4f, 0x75, 0x74, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x66,
	0x65, 0x72, 0x73, 0x57, 0x6f, 0x72, 0x6b, 0x65, 0x72, 0x12, 0x16, 0x2e, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x45, 0x6d, 0x70, 0x74,
	0x79, 0x1a, 0x16, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x62, 0x75, 0x66, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x22, 0x00, 0x12, 0x57, 0x0a, 0x23, 0x53,
	0x74, 0x61, 0x72, 0x74, 0x50, 0x61, 0x72, 0x73, 0x69, 0x6e, 0x67, 0x49, 0x6e, 0x63, 0x6f, 0x6d,
	0x69, 0x6e, 0x67, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x66, 0x65, 0x72, 0x73, 0x57, 0x6f, 0x72, 0x6b,
	0x65, 0x72, 0x12, 0x16, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x62, 0x75, 0x66, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x1a, 0x16, 0x2e, 0x67, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x45, 0x6d, 0x70,
	0x74, 0x79, 0x22, 0x00, 0x12, 0x59, 0x0a, 0x25, 0x53, 0x74, 0x61, 0x72, 0x74, 0x50, 0x72, 0x6f,
	0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x53, 0x74,
	0x61, 0x74, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x57, 0x6f, 0x72, 0x6b, 0x65, 0x72, 0x12, 0x16, 0x2e,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e,
	0x45, 0x6d, 0x70, 0x74, 0x79, 0x1a, 0x16, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x22, 0x00, 0x12,
	0x55, 0x0a, 0x21, 0x53, 0x74, 0x61, 0x72, 0x74, 0x46, 0x69, 0x6e, 0x61, 0x6c, 0x69, 0x7a, 0x69,
	0x6e, 0x67, 0x4f, 0x6c, 0x64, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x66, 0x65, 0x72, 0x73, 0x57, 0x6f,
	0x72, 0x6b, 0x65, 0x72, 0x12, 0x16, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x1a, 0x16, 0x2e, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x45,
	0x6d, 0x70, 0x74, 0x79, 0x22, 0x00, 0x42, 0x2d, 0x5a, 0x2b, 0x67, 0x69, 0x74, 0x2e, 0x6c, 0x6f,
	0x63, 0x61, 0x6c, 0x2f, 0x73, 0x65, 0x6e, 0x73, 0x69, 0x74, 0x69, 0x76, 0x65, 0x2f, 0x69, 0x6e,
	0x6e, 0x65, 0x72, 0x70, 0x62, 0x2f, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67,
	0x2f, 0x67, 0x72, 0x70, 0x63, 0x62, 0x08, 0x65, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x70,
	0xe8, 0x07,
})

var (
	file_inner_processing_grpc_account_proto_rawDescOnce sync.Once
	file_inner_processing_grpc_account_proto_rawDescData []byte
)

func file_inner_processing_grpc_account_proto_rawDescGZIP() []byte {
	file_inner_processing_grpc_account_proto_rawDescOnce.Do(func() {
		file_inner_processing_grpc_account_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_inner_processing_grpc_account_proto_rawDesc), len(file_inner_processing_grpc_account_proto_rawDesc)))
	})
	return file_inner_processing_grpc_account_proto_rawDescData
}

var file_inner_processing_grpc_account_proto_msgTypes = make([]protoimpl.MessageInfo, 15)
var file_inner_processing_grpc_account_proto_goTypes = []any{
	(*DateTime)(nil),                                          // 0: processing.account.account.DateTime
	(*AccountData)(nil),                                       // 1: processing.account.account.AccountData
	(*GetAccountByIDRequestV1)(nil),                           // 2: processing.account.account.GetAccountByIDRequestV1
	(*GetAccountByNumberRequest)(nil),                         // 3: processing.account.account.GetAccountByNumberRequest
	(*GetAccountByNumberResponse)(nil),                        // 4: processing.account.account.GetAccountByNumberResponse
	(*GetTransferByIDRequestV1)(nil),                          // 5: processing.account.account.GetTransferByIDRequestV1
	(*GetTransferByIDResponseV1)(nil),                         // 6: processing.account.account.GetTransferByIDResponseV1
	(*CreateTransferRequest)(nil),                             // 7: processing.account.account.CreateTransferRequest
	(*CreateTransferResponse)(nil),                            // 8: processing.account.account.CreateTransferResponse
	(*ExtractFailOrCanceledTransfersByForeignIDsRequest)(nil), // 9: processing.account.account.ExtractFailOrCanceledTransfersByForeignIDsRequest
	(*ExtractSuccessTransfersByForeignIDsRequest)(nil),        // 10: processing.account.account.ExtractSuccessTransfersByForeignIDsRequest
	(*ExtractTransfersByForeignIDsResponse)(nil),              // 11: processing.account.account.ExtractTransfersByForeignIDsResponse
	(*GetTransferByForeignIDRequest)(nil),                     // 12: processing.account.account.GetTransferByForeignIDRequest
	(*GetTransferResponse)(nil),                               // 13: processing.account.account.GetTransferResponse
	(*GetStatusResponse)(nil),                                 // 14: processing.account.account.GetStatusResponse
	(*structpb.Struct)(nil),                                   // 15: google.protobuf.Struct
	(*emptypb.Empty)(nil),                                     // 16: google.protobuf.Empty
}
var file_inner_processing_grpc_account_proto_depIdxs = []int32{
	15, // 0: processing.account.account.AccountData.config:type_name -> google.protobuf.Struct
	14, // 1: processing.account.account.GetTransferResponse.status:type_name -> processing.account.account.GetStatusResponse
	5,  // 2: processing.account.account.Account.GetTransferByIDV1:input_type -> processing.account.account.GetTransferByIDRequestV1
	2,  // 3: processing.account.account.Account.GetAccountByID:input_type -> processing.account.account.GetAccountByIDRequestV1
	7,  // 4: processing.account.account.Account.CreateTransfer:input_type -> processing.account.account.CreateTransferRequest
	9,  // 5: processing.account.account.Account.ExtractFailOrCanceledTransfersByForeignIDs:input_type -> processing.account.account.ExtractFailOrCanceledTransfersByForeignIDsRequest
	10, // 6: processing.account.account.Account.ExtractSuccessTransfersByForeignIDs:input_type -> processing.account.account.ExtractSuccessTransfersByForeignIDsRequest
	12, // 7: processing.account.account.Account.GetTransferByForeignID:input_type -> processing.account.account.GetTransferByForeignIDRequest
	16, // 8: processing.account.account.Account.GetSuccessTransferStatus:input_type -> google.protobuf.Empty
	3,  // 9: processing.account.account.Account.GetAccountByNumber:input_type -> processing.account.account.GetAccountByNumberRequest
	16, // 10: processing.account.account.Account.StartFinalizingOutTransfersWorker:input_type -> google.protobuf.Empty
	16, // 11: processing.account.account.Account.StartParsingIncomingTransfersWorker:input_type -> google.protobuf.Empty
	16, // 12: processing.account.account.Account.StartProcessingAccountStatementWorker:input_type -> google.protobuf.Empty
	16, // 13: processing.account.account.Account.StartFinalizingOldTransfersWorker:input_type -> google.protobuf.Empty
	6,  // 14: processing.account.account.Account.GetTransferByIDV1:output_type -> processing.account.account.GetTransferByIDResponseV1
	1,  // 15: processing.account.account.Account.GetAccountByID:output_type -> processing.account.account.AccountData
	8,  // 16: processing.account.account.Account.CreateTransfer:output_type -> processing.account.account.CreateTransferResponse
	11, // 17: processing.account.account.Account.ExtractFailOrCanceledTransfersByForeignIDs:output_type -> processing.account.account.ExtractTransfersByForeignIDsResponse
	11, // 18: processing.account.account.Account.ExtractSuccessTransfersByForeignIDs:output_type -> processing.account.account.ExtractTransfersByForeignIDsResponse
	13, // 19: processing.account.account.Account.GetTransferByForeignID:output_type -> processing.account.account.GetTransferResponse
	14, // 20: processing.account.account.Account.GetSuccessTransferStatus:output_type -> processing.account.account.GetStatusResponse
	4,  // 21: processing.account.account.Account.GetAccountByNumber:output_type -> processing.account.account.GetAccountByNumberResponse
	16, // 22: processing.account.account.Account.StartFinalizingOutTransfersWorker:output_type -> google.protobuf.Empty
	16, // 23: processing.account.account.Account.StartParsingIncomingTransfersWorker:output_type -> google.protobuf.Empty
	16, // 24: processing.account.account.Account.StartProcessingAccountStatementWorker:output_type -> google.protobuf.Empty
	16, // 25: processing.account.account.Account.StartFinalizingOldTransfersWorker:output_type -> google.protobuf.Empty
	14, // [14:26] is the sub-list for method output_type
	2,  // [2:14] is the sub-list for method input_type
	2,  // [2:2] is the sub-list for extension type_name
	2,  // [2:2] is the sub-list for extension extendee
	0,  // [0:2] is the sub-list for field type_name
}

func init() { file_inner_processing_grpc_account_proto_init() }
func file_inner_processing_grpc_account_proto_init() {
	if File_inner_processing_grpc_account_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_inner_processing_grpc_account_proto_rawDesc), len(file_inner_processing_grpc_account_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   15,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_inner_processing_grpc_account_proto_goTypes,
		DependencyIndexes: file_inner_processing_grpc_account_proto_depIdxs,
		MessageInfos:      file_inner_processing_grpc_account_proto_msgTypes,
	}.Build()
	File_inner_processing_grpc_account_proto = out.File
	file_inner_processing_grpc_account_proto_goTypes = nil
	file_inner_processing_grpc_account_proto_depIdxs = nil
}
