// Code generated by protoc-gen-go-mvp. DO NOT EDIT.
// versions:
// - protoc-gen-go-mvp v2.0.1
// created by mvp authors

package grpc

import (
	context "context"
	grpc "google.golang.org/grpc"
	metadata "google.golang.org/grpc/metadata"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
)

func NewIamBillingServer(
	srv BillingServer,
) BillingServer {
	return &iamBillingServer{
		srv: srv,
	}
}

var _ BillingServer = (*iamBillingServer)(nil)

type iamBillingServer struct {
	UnimplementedBillingServer

	srv BillingServer
}

func (s *iamBillingServer) CheckPayOutBalanceV1(
	ctx context.Context,
	req *CheckPayOutBalanceReqV1,
) (
	*CheckPayOutBalanceResV1,
	error,
) {
	return s.srv.CheckPayOutBalanceV1(ctx, req)
}

func (s *iamBillingServer) BillPayInTransactionV1(
	ctx context.Context,
	req *BillPayInTransactionRequestV1,
) (
	*emptypb.Empty,
	error,
) {
	return s.srv.BillPayInTransactionV1(ctx, req)
}

func (s *iamBillingServer) BillPayOutTransactionV1(
	ctx context.Context,
	req *BillPayOutTransactionRequestV1,
) (
	*emptypb.Empty,
	error,
) {
	return s.srv.BillPayOutTransactionV1(ctx, req)
}

func (s *iamBillingServer) BillRefundTransactionV1(
	ctx context.Context,
	req *BillRefundTransactionRequestV1,
) (
	*emptypb.Empty,
	error,
) {
	return s.srv.BillRefundTransactionV1(ctx, req)
}

func (s *iamBillingServer) CheckOutTransferBalanceV1(
	ctx context.Context,
	req *CheckOutTransferBalanceRequestV1,
) (
	*CheckOutTransferBalanceResponseV1,
	error,
) {
	return s.srv.CheckOutTransferBalanceV1(ctx, req)
}

func (s *iamBillingServer) BillOutTransferV1(
	ctx context.Context,
	req *BillOutTransferRequestV1,
) (
	*emptypb.Empty,
	error,
) {
	return s.srv.BillOutTransferV1(ctx, req)
}

func (s *iamBillingServer) BillInTransferV1(
	ctx context.Context,
	req *BillInTransferRequestV1,
) (
	*emptypb.Empty,
	error,
) {
	return s.srv.BillInTransferV1(ctx, req)
}

func (s *iamBillingServer) GetMerchantByBalanceOwnerIDV1(
	ctx context.Context,
	req *GetMerchantByBalanceOwnerRequestV1,
) (
	*GetMerchantByBalanceOwnerResponseV1,
	error,
) {
	return s.srv.GetMerchantByBalanceOwnerIDV1(ctx, req)
}

func (s *iamBillingServer) SetInTransferV1(
	ctx context.Context,
	req *SetInTransferRequestV1,
) (
	*emptypb.Empty,
	error,
) {
	return s.srv.SetInTransferV1(ctx, req)
}

func (s *iamBillingServer) BillSplitTransferV1(
	ctx context.Context,
	req *BillSplitTransferRequestV1,
) (
	*emptypb.Empty,
	error,
) {
	return s.srv.BillSplitTransferV1(ctx, req)
}

func (s *iamBillingServer) GetBalanceOwnerV1(
	ctx context.Context,
	req *GetBalanceOwnerRequestV1,
) (
	*GetBalanceOwnerResponseV1,
	error,
) {
	return s.srv.GetBalanceOwnerV1(ctx, req)
}

func (s *iamBillingServer) SetBalanceOwnerSplittableV1(
	ctx context.Context,
	req *SetBalanceOwnerSplittableRequestV1,
) (
	*emptypb.Empty,
	error,
) {
	return s.srv.SetBalanceOwnerSplittableV1(ctx, req)
}

func (s *iamBillingServer) GetBalanceOwnerByIDV1(
	ctx context.Context,
	req *GetBalanceOwnerByIDRequestV1,
) (
	*GetBalanceOwnerResponseV1,
	error,
) {
	return s.srv.GetBalanceOwnerByIDV1(ctx, req)
}

func (s *iamBillingServer) GetEntityTypeByIDV1(
	ctx context.Context,
	req *GetEntityTypeByIDRequestV1,
) (
	*GetEntityTypeResponseV1,
	error,
) {
	return s.srv.GetEntityTypeByIDV1(ctx, req)
}

func (s *iamBillingServer) GetCountryCodeByIDV1(
	ctx context.Context,
	req *GetCountryCodeByIDRequestV1,
) (
	*GetCountryCodeResponseV1,
	error,
) {
	return s.srv.GetCountryCodeByIDV1(ctx, req)
}

func (s *iamBillingServer) GetBalanceByIDV1(
	ctx context.Context,
	req *GetBalanceByIDRequestV1,
) (
	*GetBalanceResponseV1,
	error,
) {
	return s.srv.GetBalanceByIDV1(ctx, req)
}

func (s *iamBillingServer) CheckHasBalanceV1(
	ctx context.Context,
	req *CheckHasBalanceRequestV1,
) (
	*emptypb.Empty,
	error,
) {
	return s.srv.CheckHasBalanceV1(ctx, req)
}

func (s *iamBillingServer) CheckBalanceOwnerAccountExistsByBalanceOwnerIdAndAccountIdV1(
	ctx context.Context,
	req *CheckBalanceOwnerAccountExistsByBalanceOwnerIdAndAccountIdRequestV1,
) (
	*emptypb.Empty,
	error,
) {
	return s.srv.CheckBalanceOwnerAccountExistsByBalanceOwnerIdAndAccountIdV1(ctx, req)
}

func (s *iamBillingServer) GetBalanceAccountByNumber(
	ctx context.Context,
	req *GetBalanceAccountByNumberRequest,
) (
	*GetBalanceAccountByNumberResponse,
	error,
) {
	return s.srv.GetBalanceAccountByNumber(ctx, req)
}

func (s *iamBillingServer) GetCurrentBalanceAmountByBalanceOwnerID(
	ctx context.Context,
	req *GetCurrentBalanceAmountByAccountAndOwnerIDRequest,
) (
	*GetCurrentBalanceAmountByAccountAndOwnerIDResponse,
	error,
) {
	return s.srv.GetCurrentBalanceAmountByBalanceOwnerID(ctx, req)
}

func (s *iamBillingServer) CheckBalanceCreditExpireDate(
	ctx context.Context,
	req *emptypb.Empty,
) (
	*emptypb.Empty,
	error,
) {
	return s.srv.CheckBalanceCreditExpireDate(ctx, req)
}

func (s *iamBillingServer) CheckBalanceCreditStartDate(
	ctx context.Context,
	req *emptypb.Empty,
) (
	*emptypb.Empty,
	error,
) {
	return s.srv.CheckBalanceCreditStartDate(ctx, req)
}

func (s *iamBillingServer) RecalculateProvisionalBalances(
	ctx context.Context,
	req *emptypb.Empty,
) (
	*emptypb.Empty,
	error,
) {
	return s.srv.RecalculateProvisionalBalances(ctx, req)
}

func (s *iamBillingServer) RecalculateFinalBalances(
	ctx context.Context,
	req *emptypb.Empty,
) (
	*emptypb.Empty,
	error,
) {
	return s.srv.RecalculateFinalBalances(ctx, req)
}

func (s *iamBillingServer) RecalculateCreditBalances(
	ctx context.Context,
	req *emptypb.Empty,
) (
	*emptypb.Empty,
	error,
) {
	return s.srv.RecalculateCreditBalances(ctx, req)
}

func NewIamBillingClient(
	client BillingClient,
) BillingClient {
	return &iamBillingClient{
		client: client,
	}
}

type iamBillingClient struct {
	client BillingClient
}

func (s *iamBillingClient) CheckPayOutBalanceV1(
	ctx context.Context,
	req *CheckPayOutBalanceReqV1,
	opts ...grpc.CallOption,
) (
	*CheckPayOutBalanceResV1,
	error,
) {
	md, _ := metadata.FromIncomingContext(ctx)
	return s.client.CheckPayOutBalanceV1(metadata.NewOutgoingContext(ctx, md), req)
}

func (s *iamBillingClient) BillPayInTransactionV1(
	ctx context.Context,
	req *BillPayInTransactionRequestV1,
	opts ...grpc.CallOption,
) (
	*emptypb.Empty,
	error,
) {
	md, _ := metadata.FromIncomingContext(ctx)
	return s.client.BillPayInTransactionV1(metadata.NewOutgoingContext(ctx, md), req)
}

func (s *iamBillingClient) BillPayOutTransactionV1(
	ctx context.Context,
	req *BillPayOutTransactionRequestV1,
	opts ...grpc.CallOption,
) (
	*emptypb.Empty,
	error,
) {
	md, _ := metadata.FromIncomingContext(ctx)
	return s.client.BillPayOutTransactionV1(metadata.NewOutgoingContext(ctx, md), req)
}

func (s *iamBillingClient) BillRefundTransactionV1(
	ctx context.Context,
	req *BillRefundTransactionRequestV1,
	opts ...grpc.CallOption,
) (
	*emptypb.Empty,
	error,
) {
	md, _ := metadata.FromIncomingContext(ctx)
	return s.client.BillRefundTransactionV1(metadata.NewOutgoingContext(ctx, md), req)
}

func (s *iamBillingClient) CheckOutTransferBalanceV1(
	ctx context.Context,
	req *CheckOutTransferBalanceRequestV1,
	opts ...grpc.CallOption,
) (
	*CheckOutTransferBalanceResponseV1,
	error,
) {
	md, _ := metadata.FromIncomingContext(ctx)
	return s.client.CheckOutTransferBalanceV1(metadata.NewOutgoingContext(ctx, md), req)
}

func (s *iamBillingClient) BillOutTransferV1(
	ctx context.Context,
	req *BillOutTransferRequestV1,
	opts ...grpc.CallOption,
) (
	*emptypb.Empty,
	error,
) {
	md, _ := metadata.FromIncomingContext(ctx)
	return s.client.BillOutTransferV1(metadata.NewOutgoingContext(ctx, md), req)
}

func (s *iamBillingClient) BillInTransferV1(
	ctx context.Context,
	req *BillInTransferRequestV1,
	opts ...grpc.CallOption,
) (
	*emptypb.Empty,
	error,
) {
	md, _ := metadata.FromIncomingContext(ctx)
	return s.client.BillInTransferV1(metadata.NewOutgoingContext(ctx, md), req)
}

func (s *iamBillingClient) GetMerchantByBalanceOwnerIDV1(
	ctx context.Context,
	req *GetMerchantByBalanceOwnerRequestV1,
	opts ...grpc.CallOption,
) (
	*GetMerchantByBalanceOwnerResponseV1,
	error,
) {
	md, _ := metadata.FromIncomingContext(ctx)
	return s.client.GetMerchantByBalanceOwnerIDV1(metadata.NewOutgoingContext(ctx, md), req)
}

func (s *iamBillingClient) SetInTransferV1(
	ctx context.Context,
	req *SetInTransferRequestV1,
	opts ...grpc.CallOption,
) (
	*emptypb.Empty,
	error,
) {
	md, _ := metadata.FromIncomingContext(ctx)
	return s.client.SetInTransferV1(metadata.NewOutgoingContext(ctx, md), req)
}

func (s *iamBillingClient) BillSplitTransferV1(
	ctx context.Context,
	req *BillSplitTransferRequestV1,
	opts ...grpc.CallOption,
) (
	*emptypb.Empty,
	error,
) {
	md, _ := metadata.FromIncomingContext(ctx)
	return s.client.BillSplitTransferV1(metadata.NewOutgoingContext(ctx, md), req)
}

func (s *iamBillingClient) GetBalanceOwnerV1(
	ctx context.Context,
	req *GetBalanceOwnerRequestV1,
	opts ...grpc.CallOption,
) (
	*GetBalanceOwnerResponseV1,
	error,
) {
	md, _ := metadata.FromIncomingContext(ctx)
	return s.client.GetBalanceOwnerV1(metadata.NewOutgoingContext(ctx, md), req)
}

func (s *iamBillingClient) SetBalanceOwnerSplittableV1(
	ctx context.Context,
	req *SetBalanceOwnerSplittableRequestV1,
	opts ...grpc.CallOption,
) (
	*emptypb.Empty,
	error,
) {
	md, _ := metadata.FromIncomingContext(ctx)
	return s.client.SetBalanceOwnerSplittableV1(metadata.NewOutgoingContext(ctx, md), req)
}

func (s *iamBillingClient) GetBalanceOwnerByIDV1(
	ctx context.Context,
	req *GetBalanceOwnerByIDRequestV1,
	opts ...grpc.CallOption,
) (
	*GetBalanceOwnerResponseV1,
	error,
) {
	md, _ := metadata.FromIncomingContext(ctx)
	return s.client.GetBalanceOwnerByIDV1(metadata.NewOutgoingContext(ctx, md), req)
}

func (s *iamBillingClient) GetEntityTypeByIDV1(
	ctx context.Context,
	req *GetEntityTypeByIDRequestV1,
	opts ...grpc.CallOption,
) (
	*GetEntityTypeResponseV1,
	error,
) {
	md, _ := metadata.FromIncomingContext(ctx)
	return s.client.GetEntityTypeByIDV1(metadata.NewOutgoingContext(ctx, md), req)
}

func (s *iamBillingClient) GetCountryCodeByIDV1(
	ctx context.Context,
	req *GetCountryCodeByIDRequestV1,
	opts ...grpc.CallOption,
) (
	*GetCountryCodeResponseV1,
	error,
) {
	md, _ := metadata.FromIncomingContext(ctx)
	return s.client.GetCountryCodeByIDV1(metadata.NewOutgoingContext(ctx, md), req)
}

func (s *iamBillingClient) GetBalanceByIDV1(
	ctx context.Context,
	req *GetBalanceByIDRequestV1,
	opts ...grpc.CallOption,
) (
	*GetBalanceResponseV1,
	error,
) {
	md, _ := metadata.FromIncomingContext(ctx)
	return s.client.GetBalanceByIDV1(metadata.NewOutgoingContext(ctx, md), req)
}

func (s *iamBillingClient) CheckHasBalanceV1(
	ctx context.Context,
	req *CheckHasBalanceRequestV1,
	opts ...grpc.CallOption,
) (
	*emptypb.Empty,
	error,
) {
	md, _ := metadata.FromIncomingContext(ctx)
	return s.client.CheckHasBalanceV1(metadata.NewOutgoingContext(ctx, md), req)
}

func (s *iamBillingClient) CheckBalanceOwnerAccountExistsByBalanceOwnerIdAndAccountIdV1(
	ctx context.Context,
	req *CheckBalanceOwnerAccountExistsByBalanceOwnerIdAndAccountIdRequestV1,
	opts ...grpc.CallOption,
) (
	*emptypb.Empty,
	error,
) {
	md, _ := metadata.FromIncomingContext(ctx)
	return s.client.CheckBalanceOwnerAccountExistsByBalanceOwnerIdAndAccountIdV1(metadata.NewOutgoingContext(ctx, md), req)
}

func (s *iamBillingClient) GetBalanceAccountByNumber(
	ctx context.Context,
	req *GetBalanceAccountByNumberRequest,
	opts ...grpc.CallOption,
) (
	*GetBalanceAccountByNumberResponse,
	error,
) {
	md, _ := metadata.FromIncomingContext(ctx)
	return s.client.GetBalanceAccountByNumber(metadata.NewOutgoingContext(ctx, md), req)
}

func (s *iamBillingClient) GetCurrentBalanceAmountByBalanceOwnerID(
	ctx context.Context,
	req *GetCurrentBalanceAmountByAccountAndOwnerIDRequest,
	opts ...grpc.CallOption,
) (
	*GetCurrentBalanceAmountByAccountAndOwnerIDResponse,
	error,
) {
	md, _ := metadata.FromIncomingContext(ctx)
	return s.client.GetCurrentBalanceAmountByBalanceOwnerID(metadata.NewOutgoingContext(ctx, md), req)
}

func (s *iamBillingClient) CheckBalanceCreditExpireDate(
	ctx context.Context,
	req *emptypb.Empty,
	opts ...grpc.CallOption,
) (
	*emptypb.Empty,
	error,
) {
	md, _ := metadata.FromIncomingContext(ctx)
	return s.client.CheckBalanceCreditExpireDate(metadata.NewOutgoingContext(ctx, md), req)
}

func (s *iamBillingClient) CheckBalanceCreditStartDate(
	ctx context.Context,
	req *emptypb.Empty,
	opts ...grpc.CallOption,
) (
	*emptypb.Empty,
	error,
) {
	md, _ := metadata.FromIncomingContext(ctx)
	return s.client.CheckBalanceCreditStartDate(metadata.NewOutgoingContext(ctx, md), req)
}

func (s *iamBillingClient) RecalculateProvisionalBalances(
	ctx context.Context,
	req *emptypb.Empty,
	opts ...grpc.CallOption,
) (
	*emptypb.Empty,
	error,
) {
	md, _ := metadata.FromIncomingContext(ctx)
	return s.client.RecalculateProvisionalBalances(metadata.NewOutgoingContext(ctx, md), req)
}

func (s *iamBillingClient) RecalculateFinalBalances(
	ctx context.Context,
	req *emptypb.Empty,
	opts ...grpc.CallOption,
) (
	*emptypb.Empty,
	error,
) {
	md, _ := metadata.FromIncomingContext(ctx)
	return s.client.RecalculateFinalBalances(metadata.NewOutgoingContext(ctx, md), req)
}

func (s *iamBillingClient) RecalculateCreditBalances(
	ctx context.Context,
	req *emptypb.Empty,
	opts ...grpc.CallOption,
) (
	*emptypb.Empty,
	error,
) {
	md, _ := metadata.FromIncomingContext(ctx)
	return s.client.RecalculateCreditBalances(metadata.NewOutgoingContext(ctx, md), req)
}
