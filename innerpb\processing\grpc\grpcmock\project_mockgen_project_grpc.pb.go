// Code generated by MockGen. DO NOT EDIT.
// Source: project_grpc.pb.go

// Package grpcmock is a generated GoMock package.
package grpcmock

import (
	context "context"
	reflect "reflect"

	grpc "git.local/sensitive/innerpb/processing/grpc"
	gomock "github.com/golang/mock/gomock"
	grpc0 "google.golang.org/grpc"
)

// MockProjectClient is a mock of ProjectClient interface.
type MockProjectClient struct {
	ctrl     *gomock.Controller
	recorder *MockProjectClientMockRecorder
}

// MockProjectClientMockRecorder is the mock recorder for MockProjectClient.
type MockProjectClientMockRecorder struct {
	mock *MockProjectClient
}

// NewMockProjectClient creates a new mock instance.
func NewMockProjectClient(ctrl *gomock.Controller) *MockProjectClient {
	mock := &MockProjectClient{ctrl: ctrl}
	mock.recorder = &MockProjectClientMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockProjectClient) EXPECT() *MockProjectClientMockRecorder {
	return m.recorder
}

// GetProcessingProjectsByBUIDV1 mocks base method.
func (m *MockProjectClient) GetProcessingProjectsByBUIDV1(ctx context.Context, in *grpc.GetProcessingProjectsByBUIDRequestV1, opts ...grpc0.CallOption) (*grpc.GetProcessingProjectsByBUIDResponseV1, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetProcessingProjectsByBUIDV1", varargs...)
	ret0, _ := ret[0].(*grpc.GetProcessingProjectsByBUIDResponseV1)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetProcessingProjectsByBUIDV1 indicates an expected call of GetProcessingProjectsByBUIDV1.
func (mr *MockProjectClientMockRecorder) GetProcessingProjectsByBUIDV1(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetProcessingProjectsByBUIDV1", reflect.TypeOf((*MockProjectClient)(nil).GetProcessingProjectsByBUIDV1), varargs...)
}

// GetProjectsByMerchantID mocks base method.
func (m *MockProjectClient) GetProjectsByMerchantID(ctx context.Context, in *grpc.ProjectsRequestV1, opts ...grpc0.CallOption) (*grpc.ProjectsResponseV1, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetProjectsByMerchantID", varargs...)
	ret0, _ := ret[0].(*grpc.ProjectsResponseV1)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetProjectsByMerchantID indicates an expected call of GetProjectsByMerchantID.
func (mr *MockProjectClientMockRecorder) GetProjectsByMerchantID(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetProjectsByMerchantID", reflect.TypeOf((*MockProjectClient)(nil).GetProjectsByMerchantID), varargs...)
}

// IsSendEmail mocks base method.
func (m *MockProjectClient) IsSendEmail(ctx context.Context, in *grpc.ProjectRequestV1, opts ...grpc0.CallOption) (*grpc.IsSendEmailResponseV1, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "IsSendEmail", varargs...)
	ret0, _ := ret[0].(*grpc.IsSendEmailResponseV1)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// IsSendEmail indicates an expected call of IsSendEmail.
func (mr *MockProjectClientMockRecorder) IsSendEmail(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "IsSendEmail", reflect.TypeOf((*MockProjectClient)(nil).IsSendEmail), varargs...)
}

// MockProjectServer is a mock of ProjectServer interface.
type MockProjectServer struct {
	ctrl     *gomock.Controller
	recorder *MockProjectServerMockRecorder
}

// MockProjectServerMockRecorder is the mock recorder for MockProjectServer.
type MockProjectServerMockRecorder struct {
	mock *MockProjectServer
}

// NewMockProjectServer creates a new mock instance.
func NewMockProjectServer(ctrl *gomock.Controller) *MockProjectServer {
	mock := &MockProjectServer{ctrl: ctrl}
	mock.recorder = &MockProjectServerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockProjectServer) EXPECT() *MockProjectServerMockRecorder {
	return m.recorder
}

// GetProcessingProjectsByBUIDV1 mocks base method.
func (m *MockProjectServer) GetProcessingProjectsByBUIDV1(arg0 context.Context, arg1 *grpc.GetProcessingProjectsByBUIDRequestV1) (*grpc.GetProcessingProjectsByBUIDResponseV1, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetProcessingProjectsByBUIDV1", arg0, arg1)
	ret0, _ := ret[0].(*grpc.GetProcessingProjectsByBUIDResponseV1)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetProcessingProjectsByBUIDV1 indicates an expected call of GetProcessingProjectsByBUIDV1.
func (mr *MockProjectServerMockRecorder) GetProcessingProjectsByBUIDV1(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetProcessingProjectsByBUIDV1", reflect.TypeOf((*MockProjectServer)(nil).GetProcessingProjectsByBUIDV1), arg0, arg1)
}

// GetProjectsByMerchantID mocks base method.
func (m *MockProjectServer) GetProjectsByMerchantID(arg0 context.Context, arg1 *grpc.ProjectsRequestV1) (*grpc.ProjectsResponseV1, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetProjectsByMerchantID", arg0, arg1)
	ret0, _ := ret[0].(*grpc.ProjectsResponseV1)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetProjectsByMerchantID indicates an expected call of GetProjectsByMerchantID.
func (mr *MockProjectServerMockRecorder) GetProjectsByMerchantID(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetProjectsByMerchantID", reflect.TypeOf((*MockProjectServer)(nil).GetProjectsByMerchantID), arg0, arg1)
}

// IsSendEmail mocks base method.
func (m *MockProjectServer) IsSendEmail(arg0 context.Context, arg1 *grpc.ProjectRequestV1) (*grpc.IsSendEmailResponseV1, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "IsSendEmail", arg0, arg1)
	ret0, _ := ret[0].(*grpc.IsSendEmailResponseV1)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// IsSendEmail indicates an expected call of IsSendEmail.
func (mr *MockProjectServerMockRecorder) IsSendEmail(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "IsSendEmail", reflect.TypeOf((*MockProjectServer)(nil).IsSendEmail), arg0, arg1)
}

// mustEmbedUnimplementedProjectServer mocks base method.
func (m *MockProjectServer) mustEmbedUnimplementedProjectServer() {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "mustEmbedUnimplementedProjectServer")
}

// mustEmbedUnimplementedProjectServer indicates an expected call of mustEmbedUnimplementedProjectServer.
func (mr *MockProjectServerMockRecorder) mustEmbedUnimplementedProjectServer() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "mustEmbedUnimplementedProjectServer", reflect.TypeOf((*MockProjectServer)(nil).mustEmbedUnimplementedProjectServer))
}

// MockUnsafeProjectServer is a mock of UnsafeProjectServer interface.
type MockUnsafeProjectServer struct {
	ctrl     *gomock.Controller
	recorder *MockUnsafeProjectServerMockRecorder
}

// MockUnsafeProjectServerMockRecorder is the mock recorder for MockUnsafeProjectServer.
type MockUnsafeProjectServerMockRecorder struct {
	mock *MockUnsafeProjectServer
}

// NewMockUnsafeProjectServer creates a new mock instance.
func NewMockUnsafeProjectServer(ctrl *gomock.Controller) *MockUnsafeProjectServer {
	mock := &MockUnsafeProjectServer{ctrl: ctrl}
	mock.recorder = &MockUnsafeProjectServerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockUnsafeProjectServer) EXPECT() *MockUnsafeProjectServerMockRecorder {
	return m.recorder
}

// mustEmbedUnimplementedProjectServer mocks base method.
func (m *MockUnsafeProjectServer) mustEmbedUnimplementedProjectServer() {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "mustEmbedUnimplementedProjectServer")
}

// mustEmbedUnimplementedProjectServer indicates an expected call of mustEmbedUnimplementedProjectServer.
func (mr *MockUnsafeProjectServerMockRecorder) mustEmbedUnimplementedProjectServer() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "mustEmbedUnimplementedProjectServer", reflect.TypeOf((*MockUnsafeProjectServer)(nil).mustEmbedUnimplementedProjectServer))
}
