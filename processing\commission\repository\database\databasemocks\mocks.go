// Code generated by MockGen. DO NOT EDIT.
// Source: database.go
//
// Generated by this command:
//
//	mockgen -package=databasemocks -destination=./databasemocks/mocks.go -source=database.go *
//

// Package databasemocks is a generated GoMock package.
package databasemocks

import (
	context "context"
	reflect "reflect"
	time "time"

	model "git.local/sensitive/processing/commission/model"
	gomock "github.com/golang/mock/gomock"
)

// MockAcquirerOptioner is a mock of AcquirerOptioner interface.
type MockAcquirerOptioner struct {
	ctrl     *gomock.Controller
	recorder *MockAcquirerOptionerMockRecorder
	isgomock struct{}
}

// MockAcquirerOptionerMockRecorder is the mock recorder for MockAcquirerOptioner.
type MockAcquirerOptionerMockRecorder struct {
	mock *MockAcquirerOptioner
}

// NewMockAcquirerOptioner creates a new mock instance.
func NewMockAcquirerOptioner(ctrl *gomock.Controller) *MockAcquirerOptioner {
	mock := &MockAcquirerOptioner{ctrl: ctrl}
	mock.recorder = &MockAcquirerOptionerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockAcquirerOptioner) EXPECT() *MockAcquirerOptionerMockRecorder {
	return m.recorder
}

// Create mocks base method.
func (m *MockAcquirerOptioner) Create(ctx context.Context, acquirerOpt model.AcquirerOption) (model.AcquirerOption, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Create", ctx, acquirerOpt)
	ret0, _ := ret[0].(model.AcquirerOption)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Create indicates an expected call of Create.
func (mr *MockAcquirerOptionerMockRecorder) Create(ctx, acquirerOpt any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Create", reflect.TypeOf((*MockAcquirerOptioner)(nil).Create), ctx, acquirerOpt)
}

// GetByID mocks base method.
func (m *MockAcquirerOptioner) GetByID(ctx context.Context, optionID uint64) (model.AcquirerOption, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetByID", ctx, optionID)
	ret0, _ := ret[0].(model.AcquirerOption)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetByID indicates an expected call of GetByID.
func (mr *MockAcquirerOptionerMockRecorder) GetByID(ctx, optionID any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetByID", reflect.TypeOf((*MockAcquirerOptioner)(nil).GetByID), ctx, optionID)
}

// GetOptions mocks base method.
func (m *MockAcquirerOptioner) GetOptions(ctx context.Context, acquirerId, merchantID, projectID, aggregatedTypeID uint64) (model.CommonOptionFinder[model.AcquirerOption], error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetOptions", ctx, acquirerId, merchantID, projectID, aggregatedTypeID)
	ret0, _ := ret[0].(model.CommonOptionFinder[model.AcquirerOption])
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetOptions indicates an expected call of GetOptions.
func (mr *MockAcquirerOptionerMockRecorder) GetOptions(ctx, acquirerId, merchantID, projectID, aggregatedTypeID any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetOptions", reflect.TypeOf((*MockAcquirerOptioner)(nil).GetOptions), ctx, acquirerId, merchantID, projectID, aggregatedTypeID)
}

// Update mocks base method.
func (m *MockAcquirerOptioner) Update(ctx context.Context, optionID uint64, option model.AcquirerOption) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Update", ctx, optionID, option)
	ret0, _ := ret[0].(error)
	return ret0
}

// Update indicates an expected call of Update.
func (mr *MockAcquirerOptionerMockRecorder) Update(ctx, optionID, option any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Update", reflect.TypeOf((*MockAcquirerOptioner)(nil).Update), ctx, optionID, option)
}

// MockAcquirerCommissioner is a mock of AcquirerCommissioner interface.
type MockAcquirerCommissioner struct {
	ctrl     *gomock.Controller
	recorder *MockAcquirerCommissionerMockRecorder
	isgomock struct{}
}

// MockAcquirerCommissionerMockRecorder is the mock recorder for MockAcquirerCommissioner.
type MockAcquirerCommissionerMockRecorder struct {
	mock *MockAcquirerCommissioner
}

// NewMockAcquirerCommissioner creates a new mock instance.
func NewMockAcquirerCommissioner(ctrl *gomock.Controller) *MockAcquirerCommissioner {
	mock := &MockAcquirerCommissioner{ctrl: ctrl}
	mock.recorder = &MockAcquirerCommissionerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockAcquirerCommissioner) EXPECT() *MockAcquirerCommissionerMockRecorder {
	return m.recorder
}

// Create mocks base method.
func (m *MockAcquirerCommissioner) Create(ctx context.Context, acquirerComm model.AcquirerCommission) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Create", ctx, acquirerComm)
	ret0, _ := ret[0].(error)
	return ret0
}

// Create indicates an expected call of Create.
func (mr *MockAcquirerCommissionerMockRecorder) Create(ctx, acquirerComm any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Create", reflect.TypeOf((*MockAcquirerCommissioner)(nil).Create), ctx, acquirerComm)
}

// GetByID mocks base method.
func (m *MockAcquirerCommissioner) GetByID(ctx context.Context, id uint64) (model.AcquirerCommission, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetByID", ctx, id)
	ret0, _ := ret[0].(model.AcquirerCommission)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetByID indicates an expected call of GetByID.
func (mr *MockAcquirerCommissionerMockRecorder) GetByID(ctx, id any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetByID", reflect.TypeOf((*MockAcquirerCommissioner)(nil).GetByID), ctx, id)
}

// GetCommissionsByOptionID mocks base method.
func (m *MockAcquirerCommissioner) GetCommissionsByOptionID(ctx context.Context, optionID uint64, transactionCreatedAt time.Time) (model.CommissionFinder[model.AcquirerCommission], error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetCommissionsByOptionID", ctx, optionID, transactionCreatedAt)
	ret0, _ := ret[0].(model.CommissionFinder[model.AcquirerCommission])
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetCommissionsByOptionID indicates an expected call of GetCommissionsByOptionID.
func (mr *MockAcquirerCommissionerMockRecorder) GetCommissionsByOptionID(ctx, optionID, transactionCreatedAt any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetCommissionsByOptionID", reflect.TypeOf((*MockAcquirerCommissioner)(nil).GetCommissionsByOptionID), ctx, optionID, transactionCreatedAt)
}

// UpdateExpiracy mocks base method.
func (m *MockAcquirerCommissioner) UpdateExpiracy(ctx context.Context, id uint64, startDate, endDate *time.Time) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateExpiracy", ctx, id, startDate, endDate)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpdateExpiracy indicates an expected call of UpdateExpiracy.
func (mr *MockAcquirerCommissionerMockRecorder) UpdateExpiracy(ctx, id, startDate, endDate any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateExpiracy", reflect.TypeOf((*MockAcquirerCommissioner)(nil).UpdateExpiracy), ctx, id, startDate, endDate)
}

// MockProjectLowerOptioner is a mock of ProjectLowerOptioner interface.
type MockProjectLowerOptioner struct {
	ctrl     *gomock.Controller
	recorder *MockProjectLowerOptionerMockRecorder
	isgomock struct{}
}

// MockProjectLowerOptionerMockRecorder is the mock recorder for MockProjectLowerOptioner.
type MockProjectLowerOptionerMockRecorder struct {
	mock *MockProjectLowerOptioner
}

// NewMockProjectLowerOptioner creates a new mock instance.
func NewMockProjectLowerOptioner(ctrl *gomock.Controller) *MockProjectLowerOptioner {
	mock := &MockProjectLowerOptioner{ctrl: ctrl}
	mock.recorder = &MockProjectLowerOptionerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockProjectLowerOptioner) EXPECT() *MockProjectLowerOptionerMockRecorder {
	return m.recorder
}

// Create mocks base method.
func (m *MockProjectLowerOptioner) Create(ctx context.Context, projectOpt model.ProjectLowerOption) (model.ProjectLowerOption, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Create", ctx, projectOpt)
	ret0, _ := ret[0].(model.ProjectLowerOption)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Create indicates an expected call of Create.
func (mr *MockProjectLowerOptionerMockRecorder) Create(ctx, projectOpt any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Create", reflect.TypeOf((*MockProjectLowerOptioner)(nil).Create), ctx, projectOpt)
}

// GetByID mocks base method.
func (m *MockProjectLowerOptioner) GetByID(ctx context.Context, optionID uint64) (model.ProjectLowerOption, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetByID", ctx, optionID)
	ret0, _ := ret[0].(model.ProjectLowerOption)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetByID indicates an expected call of GetByID.
func (mr *MockProjectLowerOptionerMockRecorder) GetByID(ctx, optionID any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetByID", reflect.TypeOf((*MockProjectLowerOptioner)(nil).GetByID), ctx, optionID)
}

// GetOptions mocks base method.
func (m *MockProjectLowerOptioner) GetOptions(ctx context.Context, acquirerId, merchantID, projectID, aggregatedTypeID, lowerOptionTypeID uint64) (model.CommonOptionFinder[model.ProjectLowerOption], error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetOptions", ctx, acquirerId, merchantID, projectID, aggregatedTypeID, lowerOptionTypeID)
	ret0, _ := ret[0].(model.CommonOptionFinder[model.ProjectLowerOption])
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetOptions indicates an expected call of GetOptions.
func (mr *MockProjectLowerOptionerMockRecorder) GetOptions(ctx, acquirerId, merchantID, projectID, aggregatedTypeID, lowerOptionTypeID any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetOptions", reflect.TypeOf((*MockProjectLowerOptioner)(nil).GetOptions), ctx, acquirerId, merchantID, projectID, aggregatedTypeID, lowerOptionTypeID)
}

// Update mocks base method.
func (m *MockProjectLowerOptioner) Update(ctx context.Context, optionID uint64, option model.ProjectLowerOption) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Update", ctx, optionID, option)
	ret0, _ := ret[0].(error)
	return ret0
}

// Update indicates an expected call of Update.
func (mr *MockProjectLowerOptionerMockRecorder) Update(ctx, optionID, option any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Update", reflect.TypeOf((*MockProjectLowerOptioner)(nil).Update), ctx, optionID, option)
}

// MockProjectLowerCommissioner is a mock of ProjectLowerCommissioner interface.
type MockProjectLowerCommissioner struct {
	ctrl     *gomock.Controller
	recorder *MockProjectLowerCommissionerMockRecorder
	isgomock struct{}
}

// MockProjectLowerCommissionerMockRecorder is the mock recorder for MockProjectLowerCommissioner.
type MockProjectLowerCommissionerMockRecorder struct {
	mock *MockProjectLowerCommissioner
}

// NewMockProjectLowerCommissioner creates a new mock instance.
func NewMockProjectLowerCommissioner(ctrl *gomock.Controller) *MockProjectLowerCommissioner {
	mock := &MockProjectLowerCommissioner{ctrl: ctrl}
	mock.recorder = &MockProjectLowerCommissionerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockProjectLowerCommissioner) EXPECT() *MockProjectLowerCommissionerMockRecorder {
	return m.recorder
}

// Create mocks base method.
func (m *MockProjectLowerCommissioner) Create(ctx context.Context, projectComm model.ProjectLowerCommission) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Create", ctx, projectComm)
	ret0, _ := ret[0].(error)
	return ret0
}

// Create indicates an expected call of Create.
func (mr *MockProjectLowerCommissionerMockRecorder) Create(ctx, projectComm any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Create", reflect.TypeOf((*MockProjectLowerCommissioner)(nil).Create), ctx, projectComm)
}

// GetByID mocks base method.
func (m *MockProjectLowerCommissioner) GetByID(ctx context.Context, id uint64) (model.ProjectLowerCommission, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetByID", ctx, id)
	ret0, _ := ret[0].(model.ProjectLowerCommission)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetByID indicates an expected call of GetByID.
func (mr *MockProjectLowerCommissionerMockRecorder) GetByID(ctx, id any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetByID", reflect.TypeOf((*MockProjectLowerCommissioner)(nil).GetByID), ctx, id)
}

// GetCommissionsByOptionID mocks base method.
func (m *MockProjectLowerCommissioner) GetCommissionsByOptionID(ctx context.Context, optionID uint64, transactionCreatedAt time.Time) (model.CommissionFinder[model.ProjectLowerCommission], error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetCommissionsByOptionID", ctx, optionID, transactionCreatedAt)
	ret0, _ := ret[0].(model.CommissionFinder[model.ProjectLowerCommission])
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetCommissionsByOptionID indicates an expected call of GetCommissionsByOptionID.
func (mr *MockProjectLowerCommissionerMockRecorder) GetCommissionsByOptionID(ctx, optionID, transactionCreatedAt any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetCommissionsByOptionID", reflect.TypeOf((*MockProjectLowerCommissioner)(nil).GetCommissionsByOptionID), ctx, optionID, transactionCreatedAt)
}

// UpdateExpiracy mocks base method.
func (m *MockProjectLowerCommissioner) UpdateExpiracy(ctx context.Context, id uint64, startDate, endDate *time.Time) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateExpiracy", ctx, id, startDate, endDate)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpdateExpiracy indicates an expected call of UpdateExpiracy.
func (mr *MockProjectLowerCommissionerMockRecorder) UpdateExpiracy(ctx, id, startDate, endDate any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateExpiracy", reflect.TypeOf((*MockProjectLowerCommissioner)(nil).UpdateExpiracy), ctx, id, startDate, endDate)
}

// MockProjectUpperOptioner is a mock of ProjectUpperOptioner interface.
type MockProjectUpperOptioner struct {
	ctrl     *gomock.Controller
	recorder *MockProjectUpperOptionerMockRecorder
	isgomock struct{}
}

// MockProjectUpperOptionerMockRecorder is the mock recorder for MockProjectUpperOptioner.
type MockProjectUpperOptionerMockRecorder struct {
	mock *MockProjectUpperOptioner
}

// NewMockProjectUpperOptioner creates a new mock instance.
func NewMockProjectUpperOptioner(ctrl *gomock.Controller) *MockProjectUpperOptioner {
	mock := &MockProjectUpperOptioner{ctrl: ctrl}
	mock.recorder = &MockProjectUpperOptionerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockProjectUpperOptioner) EXPECT() *MockProjectUpperOptionerMockRecorder {
	return m.recorder
}

// Create mocks base method.
func (m *MockProjectUpperOptioner) Create(ctx context.Context, projectOpt model.ProjectUpperOption) (model.ProjectUpperOption, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Create", ctx, projectOpt)
	ret0, _ := ret[0].(model.ProjectUpperOption)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Create indicates an expected call of Create.
func (mr *MockProjectUpperOptionerMockRecorder) Create(ctx, projectOpt any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Create", reflect.TypeOf((*MockProjectUpperOptioner)(nil).Create), ctx, projectOpt)
}

// GetByID mocks base method.
func (m *MockProjectUpperOptioner) GetByID(ctx context.Context, optionID uint64) (model.ProjectUpperOption, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetByID", ctx, optionID)
	ret0, _ := ret[0].(model.ProjectUpperOption)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetByID indicates an expected call of GetByID.
func (mr *MockProjectUpperOptionerMockRecorder) GetByID(ctx, optionID any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetByID", reflect.TypeOf((*MockProjectUpperOptioner)(nil).GetByID), ctx, optionID)
}

// GetOptions mocks base method.
func (m *MockProjectUpperOptioner) GetOptions(ctx context.Context, merchantID, projectID, aggregatedTypeID uint64) (model.UpperOptionFinder, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetOptions", ctx, merchantID, projectID, aggregatedTypeID)
	ret0, _ := ret[0].(model.UpperOptionFinder)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetOptions indicates an expected call of GetOptions.
func (mr *MockProjectUpperOptionerMockRecorder) GetOptions(ctx, merchantID, projectID, aggregatedTypeID any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetOptions", reflect.TypeOf((*MockProjectUpperOptioner)(nil).GetOptions), ctx, merchantID, projectID, aggregatedTypeID)
}

// GetWithCommissionsByID mocks base method.
func (m *MockProjectUpperOptioner) GetWithCommissionsByID(ctx context.Context, id uint64) (model.ProjectUpperOptionWithCommissions, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetWithCommissionsByID", ctx, id)
	ret0, _ := ret[0].(model.ProjectUpperOptionWithCommissions)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetWithCommissionsByID indicates an expected call of GetWithCommissionsByID.
func (mr *MockProjectUpperOptionerMockRecorder) GetWithCommissionsByID(ctx, id any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetWithCommissionsByID", reflect.TypeOf((*MockProjectUpperOptioner)(nil).GetWithCommissionsByID), ctx, id)
}

// Update mocks base method.
func (m *MockProjectUpperOptioner) Update(ctx context.Context, optionID uint64, option model.ProjectUpperOption) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Update", ctx, optionID, option)
	ret0, _ := ret[0].(error)
	return ret0
}

// Update indicates an expected call of Update.
func (mr *MockProjectUpperOptionerMockRecorder) Update(ctx, optionID, option any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Update", reflect.TypeOf((*MockProjectUpperOptioner)(nil).Update), ctx, optionID, option)
}

// MockProjectUpperCommissioner is a mock of ProjectUpperCommissioner interface.
type MockProjectUpperCommissioner struct {
	ctrl     *gomock.Controller
	recorder *MockProjectUpperCommissionerMockRecorder
	isgomock struct{}
}

// MockProjectUpperCommissionerMockRecorder is the mock recorder for MockProjectUpperCommissioner.
type MockProjectUpperCommissionerMockRecorder struct {
	mock *MockProjectUpperCommissioner
}

// NewMockProjectUpperCommissioner creates a new mock instance.
func NewMockProjectUpperCommissioner(ctrl *gomock.Controller) *MockProjectUpperCommissioner {
	mock := &MockProjectUpperCommissioner{ctrl: ctrl}
	mock.recorder = &MockProjectUpperCommissionerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockProjectUpperCommissioner) EXPECT() *MockProjectUpperCommissionerMockRecorder {
	return m.recorder
}

// Create mocks base method.
func (m *MockProjectUpperCommissioner) Create(ctx context.Context, projectComm model.ProjectUpperCommission) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Create", ctx, projectComm)
	ret0, _ := ret[0].(error)
	return ret0
}

// Create indicates an expected call of Create.
func (mr *MockProjectUpperCommissionerMockRecorder) Create(ctx, projectComm any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Create", reflect.TypeOf((*MockProjectUpperCommissioner)(nil).Create), ctx, projectComm)
}

// GetByID mocks base method.
func (m *MockProjectUpperCommissioner) GetByID(ctx context.Context, id uint64) (model.ProjectUpperCommission, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetByID", ctx, id)
	ret0, _ := ret[0].(model.ProjectUpperCommission)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetByID indicates an expected call of GetByID.
func (mr *MockProjectUpperCommissionerMockRecorder) GetByID(ctx, id any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetByID", reflect.TypeOf((*MockProjectUpperCommissioner)(nil).GetByID), ctx, id)
}

// GetCommissionsByOptionID mocks base method.
func (m *MockProjectUpperCommissioner) GetCommissionsByOptionID(ctx context.Context, optionID uint64, transactionCreatedAt time.Time) (model.CommissionFinder[model.ProjectUpperCommission], error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetCommissionsByOptionID", ctx, optionID, transactionCreatedAt)
	ret0, _ := ret[0].(model.CommissionFinder[model.ProjectUpperCommission])
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetCommissionsByOptionID indicates an expected call of GetCommissionsByOptionID.
func (mr *MockProjectUpperCommissionerMockRecorder) GetCommissionsByOptionID(ctx, optionID, transactionCreatedAt any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetCommissionsByOptionID", reflect.TypeOf((*MockProjectUpperCommissioner)(nil).GetCommissionsByOptionID), ctx, optionID, transactionCreatedAt)
}

// UpdateExpiracy mocks base method.
func (m *MockProjectUpperCommissioner) UpdateExpiracy(ctx context.Context, id uint64, startDate, endDate *time.Time) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateExpiracy", ctx, id, startDate, endDate)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpdateExpiracy indicates an expected call of UpdateExpiracy.
func (mr *MockProjectUpperCommissionerMockRecorder) UpdateExpiracy(ctx, id, startDate, endDate any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateExpiracy", reflect.TypeOf((*MockProjectUpperCommissioner)(nil).UpdateExpiracy), ctx, id, startDate, endDate)
}

// MockTransactionCommissioner is a mock of TransactionCommissioner interface.
type MockTransactionCommissioner struct {
	ctrl     *gomock.Controller
	recorder *MockTransactionCommissionerMockRecorder
	isgomock struct{}
}

// MockTransactionCommissionerMockRecorder is the mock recorder for MockTransactionCommissioner.
type MockTransactionCommissionerMockRecorder struct {
	mock *MockTransactionCommissioner
}

// NewMockTransactionCommissioner creates a new mock instance.
func NewMockTransactionCommissioner(ctrl *gomock.Controller) *MockTransactionCommissioner {
	mock := &MockTransactionCommissioner{ctrl: ctrl}
	mock.recorder = &MockTransactionCommissionerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockTransactionCommissioner) EXPECT() *MockTransactionCommissionerMockRecorder {
	return m.recorder
}

// Create mocks base method.
func (m *MockTransactionCommissioner) Create(ctx context.Context, transactionID, upperCommissionID uint64, upperCommissionAmount float64) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Create", ctx, transactionID, upperCommissionID, upperCommissionAmount)
	ret0, _ := ret[0].(error)
	return ret0
}

// Create indicates an expected call of Create.
func (mr *MockTransactionCommissionerMockRecorder) Create(ctx, transactionID, upperCommissionID, upperCommissionAmount any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Create", reflect.TypeOf((*MockTransactionCommissioner)(nil).Create), ctx, transactionID, upperCommissionID, upperCommissionAmount)
}

// GetByTransactionID mocks base method.
func (m *MockTransactionCommissioner) GetByTransactionID(ctx context.Context, transactionID uint64) (model.TransactionCommission, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetByTransactionID", ctx, transactionID)
	ret0, _ := ret[0].(model.TransactionCommission)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetByTransactionID indicates an expected call of GetByTransactionID.
func (mr *MockTransactionCommissionerMockRecorder) GetByTransactionID(ctx, transactionID any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetByTransactionID", reflect.TypeOf((*MockTransactionCommissioner)(nil).GetByTransactionID), ctx, transactionID)
}

// UpdateByTransactionID mocks base method.
func (m *MockTransactionCommissioner) UpdateByTransactionID(ctx context.Context, transactionID uint64, transactionComm model.TransactionCommission) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateByTransactionID", ctx, transactionID, transactionComm)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpdateByTransactionID indicates an expected call of UpdateByTransactionID.
func (mr *MockTransactionCommissionerMockRecorder) UpdateByTransactionID(ctx, transactionID, transactionComm any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateByTransactionID", reflect.TypeOf((*MockTransactionCommissioner)(nil).UpdateByTransactionID), ctx, transactionID, transactionComm)
}

// MockProjectLowerOptionType is a mock of ProjectLowerOptionType interface.
type MockProjectLowerOptionType struct {
	ctrl     *gomock.Controller
	recorder *MockProjectLowerOptionTypeMockRecorder
	isgomock struct{}
}

// MockProjectLowerOptionTypeMockRecorder is the mock recorder for MockProjectLowerOptionType.
type MockProjectLowerOptionTypeMockRecorder struct {
	mock *MockProjectLowerOptionType
}

// NewMockProjectLowerOptionType creates a new mock instance.
func NewMockProjectLowerOptionType(ctrl *gomock.Controller) *MockProjectLowerOptionType {
	mock := &MockProjectLowerOptionType{ctrl: ctrl}
	mock.recorder = &MockProjectLowerOptionTypeMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockProjectLowerOptionType) EXPECT() *MockProjectLowerOptionTypeMockRecorder {
	return m.recorder
}

// GetAll mocks base method.
func (m *MockProjectLowerOptionType) GetAll(ctx context.Context) ([]model.ProjectLowerOptionType, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAll", ctx)
	ret0, _ := ret[0].([]model.ProjectLowerOptionType)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAll indicates an expected call of GetAll.
func (mr *MockProjectLowerOptionTypeMockRecorder) GetAll(ctx any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAll", reflect.TypeOf((*MockProjectLowerOptionType)(nil).GetAll), ctx)
}

// GetByCode mocks base method.
func (m *MockProjectLowerOptionType) GetByCode(ctx context.Context, lowerOptionCode model.ProjectLowerOptionTypeCode) (model.ProjectLowerOptionType, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetByCode", ctx, lowerOptionCode)
	ret0, _ := ret[0].(model.ProjectLowerOptionType)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetByCode indicates an expected call of GetByCode.
func (mr *MockProjectLowerOptionTypeMockRecorder) GetByCode(ctx, lowerOptionCode any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetByCode", reflect.TypeOf((*MockProjectLowerOptionType)(nil).GetByCode), ctx, lowerOptionCode)
}

// GetByID mocks base method.
func (m *MockProjectLowerOptionType) GetByID(ctx context.Context, optionTypeID uint64) (model.ProjectLowerOptionType, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetByID", ctx, optionTypeID)
	ret0, _ := ret[0].(model.ProjectLowerOptionType)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetByID indicates an expected call of GetByID.
func (mr *MockProjectLowerOptionTypeMockRecorder) GetByID(ctx, optionTypeID any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetByID", reflect.TypeOf((*MockProjectLowerOptionType)(nil).GetByID), ctx, optionTypeID)
}
