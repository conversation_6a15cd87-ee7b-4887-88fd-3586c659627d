// Code generated by protoc-gen-go-mvp. DO NOT EDIT.
// versions:
// - protoc-gen-go-mvp v2.0.1
// created by mvp authors

package grpc

import (
	context "context"
	grpc "google.golang.org/grpc"
	metadata "google.golang.org/grpc/metadata"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
)

func NewIamCollectorServer(
	srv CollectorServer,
) CollectorServer {
	return &iamCollectorServer{
		srv: srv,
	}
}

var _ CollectorServer = (*iamCollectorServer)(nil)

type iamCollectorServer struct {
	UnimplementedCollectorServer

	srv CollectorServer
}

func (s *iamCollectorServer) CollectTransaction(
	ctx context.Context,
	req *CollectTransactionRequestV1,
) (
	*CollectorEmptyResponse,
	error,
) {
	return s.srv.CollectTransaction(ctx, req)
}

func (s *iamCollectorServer) GetTransactionStatus(
	ctx context.Context,
	req *emptypb.Empty,
) (
	*emptypb.Empty,
	error,
) {
	return s.srv.GetTransactionStatus(ctx, req)
}

func NewIamCollectorClient(
	client CollectorClient,
) CollectorClient {
	return &iamCollectorClient{
		client: client,
	}
}

type iamCollectorClient struct {
	client CollectorClient
}

func (s *iamCollectorClient) CollectTransaction(
	ctx context.Context,
	req *CollectTransactionRequestV1,
	opts ...grpc.CallOption,
) (
	*CollectorEmptyResponse,
	error,
) {
	md, _ := metadata.FromIncomingContext(ctx)
	return s.client.CollectTransaction(metadata.NewOutgoingContext(ctx, md), req)
}

func (s *iamCollectorClient) GetTransactionStatus(
	ctx context.Context,
	req *emptypb.Empty,
	opts ...grpc.CallOption,
) (
	*emptypb.Empty,
	error,
) {
	md, _ := metadata.FromIncomingContext(ctx)
	return s.client.GetTransactionStatus(metadata.NewOutgoingContext(ctx, md), req)
}
