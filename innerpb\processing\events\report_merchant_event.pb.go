// Code generated by protoc-gen-go-mvp. DO NOT EDIT.
// versions:
// - protoc-gen-go-mvp v2.0.1
// created by mvp authors

package events

import (
	context "context"
	errors "errors"
	fmt "fmt"
	pb "git.local/sensitive/mvp/pb"
	watermill "github.com/ThreeDotsLabs/watermill"
	nats "github.com/ThreeDotsLabs/watermill-nats/v2/pkg/nats"
	message "github.com/ThreeDotsLabs/watermill/message"
	uuid "github.com/google/uuid"
	nats_go "github.com/nats-io/nats.go"
	otel "go.opentelemetry.io/otel"
	attribute "go.opentelemetry.io/otel/attribute"
	propagation "go.opentelemetry.io/otel/propagation"
	zap "go.uber.org/zap"
	protojson "google.golang.org/protobuf/encoding/protojson"
	anypb "google.golang.org/protobuf/types/known/anypb"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	strings "strings"
	time "time"
)

const subjectNameSaveAdditionalDataKeys = "report_merchant.saveadditionaldatakeys"

type eventSaveAdditionalDataKeys struct {
	jsContext         nats_go.JetStreamContext
	natsConn          *nats_go.Conn
	natsMessageRouter *message.Router
}

func newSaveAdditionalDataKeys(
	jsContext nats_go.JetStreamContext,
	conn *nats_go.Conn,
	router *message.Router,
) *eventSaveAdditionalDataKeys {
	return &eventSaveAdditionalDataKeys{
		jsContext:         jsContext,
		natsConn:          conn,
		natsMessageRouter: router,
	}
}

var singleSaveAdditionalDataKeys func(ctx context.Context) (<-chan *SaveAdditionalDataKeys, error)

func NewSaveAdditionalDataKeysSubscriber(
	js nats_go.JetStreamContext,
	conn *nats_go.Conn,
	router *message.Router,
) func(ctx context.Context) (<-chan *SaveAdditionalDataKeys, error) {
	if singleSaveAdditionalDataKeys == nil {
		singleSaveAdditionalDataKeys = newSaveAdditionalDataKeys(js, conn, router).Subscribe
	}

	return singleSaveAdditionalDataKeys
}

func NewSaveAdditionalDataKeysPublisher(
	js nats_go.JetStreamContext,
) func(ctx context.Context, src *SaveAdditionalDataKeys) error {
	return newSaveAdditionalDataKeys(js, nil, nil).Publish
}

func (s *eventSaveAdditionalDataKeys) Publish(ctx context.Context, src *SaveAdditionalDataKeys) error {
	var err error
	eventId := uuid.New().String()

	ctx, span := otel.Tracer("otel_trace").Start(ctx, "SaveAdditionalDataKeys.Publish")
	defer span.End()
	defer func() {
		span.RecordError(err)
		span.SetAttributes(
			attribute.Bool("error", err != nil),
			attribute.String("nats_uuid", eventId),
		)
	}()

	m, err := anypb.New(src)
	if err != nil {
		return err
	}

	data := pb.EventInfo{
		EventId:   &eventId,
		EventTime: timestamppb.Now(),
		Data:      m,
	}
	marshal, err := protojson.Marshal(&data)
	if err != nil {
		return err
	}

	header := make(nats_go.Header)
	otel.GetTextMapPropagator().Inject(ctx, propagation.HeaderCarrier(header))

	_, err = s.jsContext.PublishMsg(&nats_go.Msg{
		Subject: subjectNameSaveAdditionalDataKeys,
		Data:    marshal,
		Header:  header,
	})

	return err
}

func (s *eventSaveAdditionalDataKeys) Subscribe(ctx context.Context) (<-chan *SaveAdditionalDataKeys, error) {
	ch := make(chan *SaveAdditionalDataKeys)

	streamCfg := newSaveAdditionalDataKeysStreamConfig()
	consumerCfg := newSaveAdditionalDataKeysConsumerConfig()
	watermillCfg := newSaveAdditionalDataKeysWatermillConfigs()

	fun := func(msg *message.Message) error {
		data := new(pb.EventInfo)
		resSaveAdditionalDataKeys := new(SaveAdditionalDataKeys)
		if err := protojson.Unmarshal(msg.Payload, data); err != nil {
			return err
		}

		if err := data.GetData().UnmarshalTo(resSaveAdditionalDataKeys); err != nil {
			return err
		}

		ch <- resSaveAdditionalDataKeys

		return nil
	}

	err := s.createConsumerSaveAdditionalDataKeys(streamCfg, consumerCfg)
	if err != nil {
		return nil, err
	}
	subscriber, err := nats.NewSubscriberWithNatsConn(
		s.natsConn,
		*watermillCfg,
		newZapLoggerAdapterSaveAdditionalDataKeys(zap.L()),
	)
	if err != nil {
		return nil, fmt.Errorf("err watermillNats.NewSubscriberWithNatsConn: %w", err)
	}
	go func() {
		select {
		case <-ctx.Done():
			if err := subscriber.Close(); err != nil {
				zap.L().Error("subscriber.Close", zap.Error(err))
			}
		}
	}()

	s.natsMessageRouter.AddNoPublisherHandler(
		consumerCfg.Name,
		subjectNameSaveAdditionalDataKeys,
		subscriber,
		fun,
	)

	return ch, nil
}

func newSaveAdditionalDataKeysStreamConfig() *nats_go.StreamConfig {
	return &nats_go.StreamConfig{
		Name:         strings.ReplaceAll(subjectNameSaveAdditionalDataKeys, ".", "_"),
		Subjects:     []string{subjectNameSaveAdditionalDataKeys, fmt.Sprintf("%s.>", subjectNameSaveAdditionalDataKeys)},
		Retention:    nats_go.LimitsPolicy,
		MaxConsumers: -1,
		Discard:      nats_go.DiscardOld,
		MaxAge:       time.Hour * 24 * 7,
		Storage:      nats_go.FileStorage,
		NoAck:        false,
		AllowRollup:  true,
	}
}

func newSaveAdditionalDataKeysConsumerConfig() *nats_go.ConsumerConfig {
	defaultAckWait := time.Second * 30

	streamNameWithoutDots := strings.ReplaceAll(subjectNameSaveAdditionalDataKeys, ".", "_")

	consumerName := strings.ReplaceAll(fmt.Sprintf("%s_%s",
		"events",
		streamNameWithoutDots), ".", "_")

	return &nats_go.ConsumerConfig{
		Durable:        consumerName,
		Name:           consumerName,
		AckPolicy:      nats_go.AckExplicitPolicy,
		AckWait:        defaultAckWait,
		MaxDeliver:     -1,
		DeliverGroup:   consumerName,
		DeliverSubject: consumerName,
	}
}

func newSaveAdditionalDataKeysWatermillConfigs() *nats.SubscriberSubscriptionConfig {
	defaultAckWait := time.Second * 30

	streamNameWithoutDots := strings.ReplaceAll(subjectNameSaveAdditionalDataKeys, ".", "_")

	consumerName := strings.ReplaceAll(fmt.Sprintf("%s_%s",
		"events",
		streamNameWithoutDots), ".", "_")

	return &nats.SubscriberSubscriptionConfig{
		Unmarshaler:    &eventMarshalerSaveAdditionalDataKeys{},
		AckWaitTimeout: defaultAckWait,
		CloseTimeout:   defaultAckWait,
		SubjectCalculator: func(queueGroupPrefix, topic string) *nats.SubjectDetail {
			return &nats.SubjectDetail{
				Primary:    subjectNameSaveAdditionalDataKeys,
				QueueGroup: consumerName,
			}
		},
		JetStream: nats.JetStreamConfig{
			Disabled:      false,
			AutoProvision: false,
			SubscribeOptions: []nats_go.SubOpt{
				nats_go.ManualAck(),
				nats_go.DeliverAll(),
				nats_go.AckExplicit(),
				nats_go.Bind(streamNameWithoutDots, consumerName),
				nats_go.MaxDeliver(-1),
			},
			TrackMsgId:        true,
			AckAsync:          false,
			DurableCalculator: func(prefix, topic string) string { return consumerName },
		},
	}
}

func (s *eventSaveAdditionalDataKeys) createConsumerSaveAdditionalDataKeys(streamCfg *nats_go.StreamConfig, consumerCfg *nats_go.ConsumerConfig) error {
	_, err := s.jsContext.StreamInfo(streamCfg.Name)
	if err != nil {
		if errors.Is(err, nats_go.ErrStreamNotFound) {
			_, err = s.jsContext.AddStream(streamCfg)
			if err != nil {
				return fmt.Errorf("err jsContext.AddStream: %w", err)
			}
		} else {
			return fmt.Errorf("can't get NATS JetStream Stream: %w", err)
		}
	}

	_, err = s.jsContext.UpdateStream(streamCfg)
	if err != nil {
		return fmt.Errorf("err jsContext.UpdateStream: %w", err)
	}

	_, err = s.jsContext.ConsumerInfo(streamCfg.Name, streamCfg.Name)
	if err != nil {
		if errors.Is(err, nats_go.ErrConsumerNotFound) {
			_, err = s.jsContext.AddConsumer(streamCfg.Name, consumerCfg)
			if err != nil {
				return fmt.Errorf("err jsContext.AddConsumer: %w", err)
			}
		} else {
			return fmt.Errorf("can't get NATS JetStream Consumer %s: %w", consumerCfg.Name, err)
		}
	}

	_, err = s.jsContext.UpdateConsumer(streamCfg.Name, consumerCfg)
	if err != nil {
		return fmt.Errorf("err jsContext.UpdateConsumer: %w", err)
	}

	return nil
}

type eventMarshalerSaveAdditionalDataKeys struct{}

func (*eventMarshalerSaveAdditionalDataKeys) Marshal(topic string, msg *message.Message) (*nats_go.Msg, error) {
	natsMsg := nats_go.NewMsg(topic)
	natsMsg.Data = msg.Payload

	return natsMsg, nil
}
func (*eventMarshalerSaveAdditionalDataKeys) Unmarshal(msg *nats_go.Msg) (*message.Message, error) {
	pbMsg := &pb.EventInfo{}
	err := protojson.Unmarshal(msg.Data, pbMsg)
	if err != nil {
		return nil, err
	}

	wmMsg := message.NewMessage(pbMsg.GetEventId(), msg.Data)
	wmMsg.Metadata = map[string]string{}

	return wmMsg, nil
}

type zapLoggerAdapterSaveAdditionalDataKeys struct {
	logger *zap.Logger
}

func newZapLoggerAdapterSaveAdditionalDataKeys(logger *zap.Logger) *zapLoggerAdapterSaveAdditionalDataKeys {
	return &zapLoggerAdapterSaveAdditionalDataKeys{logger: logger}
}

func (z zapLoggerAdapterSaveAdditionalDataKeys) Error(msg string, err error, fields watermill.LogFields) {
	z.logger.Error(msg, append(z.toZapFields(fields), zap.Error(err))...)
}

func (z zapLoggerAdapterSaveAdditionalDataKeys) Info(msg string, fields watermill.LogFields) {
	z.logger.Info(msg, z.toZapFields(fields)...)
}

func (z zapLoggerAdapterSaveAdditionalDataKeys) Debug(msg string, fields watermill.LogFields) {
	z.logger.Debug(msg, z.toZapFields(fields)...)
}

func (z zapLoggerAdapterSaveAdditionalDataKeys) Trace(msg string, fields watermill.LogFields) {
	z.logger.Debug(msg, z.toZapFields(fields)...)
}

func (z zapLoggerAdapterSaveAdditionalDataKeys) With(fields watermill.LogFields) watermill.LoggerAdapter {
	newLogger := z.logger.With(z.toZapFields(fields)...)
	return &zapLoggerAdapterSaveAdditionalDataKeys{logger: newLogger}
}

func (z zapLoggerAdapterSaveAdditionalDataKeys) toZapFields(fields watermill.LogFields) []zap.Field {
	zapFields := make([]zap.Field, 0, len(fields))
	for k, v := range fields {
		zapFields = append(zapFields, zap.Any(k, v))
	}

	return zapFields
}

const subjectNameCreateMerchantCompany = "report_merchant.createmerchantcompany"

type eventCreateMerchantCompany struct {
	jsContext         nats_go.JetStreamContext
	natsConn          *nats_go.Conn
	natsMessageRouter *message.Router
}

func newCreateMerchantCompany(
	jsContext nats_go.JetStreamContext,
	conn *nats_go.Conn,
	router *message.Router,
) *eventCreateMerchantCompany {
	return &eventCreateMerchantCompany{
		jsContext:         jsContext,
		natsConn:          conn,
		natsMessageRouter: router,
	}
}

var singleCreateMerchantCompany func(ctx context.Context) (<-chan *CreateMerchantCompany, error)

func NewCreateMerchantCompanySubscriber(
	js nats_go.JetStreamContext,
	conn *nats_go.Conn,
	router *message.Router,
) func(ctx context.Context) (<-chan *CreateMerchantCompany, error) {
	if singleCreateMerchantCompany == nil {
		singleCreateMerchantCompany = newCreateMerchantCompany(js, conn, router).Subscribe
	}

	return singleCreateMerchantCompany
}

func NewCreateMerchantCompanyPublisher(
	js nats_go.JetStreamContext,
) func(ctx context.Context, src *CreateMerchantCompany) error {
	return newCreateMerchantCompany(js, nil, nil).Publish
}

func (s *eventCreateMerchantCompany) Publish(ctx context.Context, src *CreateMerchantCompany) error {
	var err error
	eventId := uuid.New().String()

	ctx, span := otel.Tracer("otel_trace").Start(ctx, "CreateMerchantCompany.Publish")
	defer span.End()
	defer func() {
		span.RecordError(err)
		span.SetAttributes(
			attribute.Bool("error", err != nil),
			attribute.String("nats_uuid", eventId),
		)
	}()

	m, err := anypb.New(src)
	if err != nil {
		return err
	}

	data := pb.EventInfo{
		EventId:   &eventId,
		EventTime: timestamppb.Now(),
		Data:      m,
	}
	marshal, err := protojson.Marshal(&data)
	if err != nil {
		return err
	}

	header := make(nats_go.Header)
	otel.GetTextMapPropagator().Inject(ctx, propagation.HeaderCarrier(header))

	_, err = s.jsContext.PublishMsg(&nats_go.Msg{
		Subject: subjectNameCreateMerchantCompany,
		Data:    marshal,
		Header:  header,
	})

	return err
}

func (s *eventCreateMerchantCompany) Subscribe(ctx context.Context) (<-chan *CreateMerchantCompany, error) {
	ch := make(chan *CreateMerchantCompany)

	streamCfg := newCreateMerchantCompanyStreamConfig()
	consumerCfg := newCreateMerchantCompanyConsumerConfig()
	watermillCfg := newCreateMerchantCompanyWatermillConfigs()

	fun := func(msg *message.Message) error {
		data := new(pb.EventInfo)
		resCreateMerchantCompany := new(CreateMerchantCompany)
		if err := protojson.Unmarshal(msg.Payload, data); err != nil {
			return err
		}

		if err := data.GetData().UnmarshalTo(resCreateMerchantCompany); err != nil {
			return err
		}

		ch <- resCreateMerchantCompany

		return nil
	}

	err := s.createConsumerCreateMerchantCompany(streamCfg, consumerCfg)
	if err != nil {
		return nil, err
	}
	subscriber, err := nats.NewSubscriberWithNatsConn(
		s.natsConn,
		*watermillCfg,
		newZapLoggerAdapterCreateMerchantCompany(zap.L()),
	)
	if err != nil {
		return nil, fmt.Errorf("err watermillNats.NewSubscriberWithNatsConn: %w", err)
	}
	go func() {
		select {
		case <-ctx.Done():
			if err := subscriber.Close(); err != nil {
				zap.L().Error("subscriber.Close", zap.Error(err))
			}
		}
	}()

	s.natsMessageRouter.AddNoPublisherHandler(
		consumerCfg.Name,
		subjectNameCreateMerchantCompany,
		subscriber,
		fun,
	)

	return ch, nil
}

func newCreateMerchantCompanyStreamConfig() *nats_go.StreamConfig {
	return &nats_go.StreamConfig{
		Name:         strings.ReplaceAll(subjectNameCreateMerchantCompany, ".", "_"),
		Subjects:     []string{subjectNameCreateMerchantCompany, fmt.Sprintf("%s.>", subjectNameCreateMerchantCompany)},
		Retention:    nats_go.LimitsPolicy,
		MaxConsumers: -1,
		Discard:      nats_go.DiscardOld,
		MaxAge:       time.Hour * 24 * 7,
		Storage:      nats_go.FileStorage,
		NoAck:        false,
		AllowRollup:  true,
	}
}

func newCreateMerchantCompanyConsumerConfig() *nats_go.ConsumerConfig {
	defaultAckWait := time.Second * 30

	streamNameWithoutDots := strings.ReplaceAll(subjectNameCreateMerchantCompany, ".", "_")

	consumerName := strings.ReplaceAll(fmt.Sprintf("%s_%s",
		"events",
		streamNameWithoutDots), ".", "_")

	return &nats_go.ConsumerConfig{
		Durable:        consumerName,
		Name:           consumerName,
		AckPolicy:      nats_go.AckExplicitPolicy,
		AckWait:        defaultAckWait,
		MaxDeliver:     -1,
		DeliverGroup:   consumerName,
		DeliverSubject: consumerName,
	}
}

func newCreateMerchantCompanyWatermillConfigs() *nats.SubscriberSubscriptionConfig {
	defaultAckWait := time.Second * 30

	streamNameWithoutDots := strings.ReplaceAll(subjectNameCreateMerchantCompany, ".", "_")

	consumerName := strings.ReplaceAll(fmt.Sprintf("%s_%s",
		"events",
		streamNameWithoutDots), ".", "_")

	return &nats.SubscriberSubscriptionConfig{
		Unmarshaler:    &eventMarshalerCreateMerchantCompany{},
		AckWaitTimeout: defaultAckWait,
		CloseTimeout:   defaultAckWait,
		SubjectCalculator: func(queueGroupPrefix, topic string) *nats.SubjectDetail {
			return &nats.SubjectDetail{
				Primary:    subjectNameCreateMerchantCompany,
				QueueGroup: consumerName,
			}
		},
		JetStream: nats.JetStreamConfig{
			Disabled:      false,
			AutoProvision: false,
			SubscribeOptions: []nats_go.SubOpt{
				nats_go.ManualAck(),
				nats_go.DeliverAll(),
				nats_go.AckExplicit(),
				nats_go.Bind(streamNameWithoutDots, consumerName),
				nats_go.MaxDeliver(-1),
			},
			TrackMsgId:        true,
			AckAsync:          false,
			DurableCalculator: func(prefix, topic string) string { return consumerName },
		},
	}
}

func (s *eventCreateMerchantCompany) createConsumerCreateMerchantCompany(streamCfg *nats_go.StreamConfig, consumerCfg *nats_go.ConsumerConfig) error {
	_, err := s.jsContext.StreamInfo(streamCfg.Name)
	if err != nil {
		if errors.Is(err, nats_go.ErrStreamNotFound) {
			_, err = s.jsContext.AddStream(streamCfg)
			if err != nil {
				return fmt.Errorf("err jsContext.AddStream: %w", err)
			}
		} else {
			return fmt.Errorf("can't get NATS JetStream Stream: %w", err)
		}
	}

	_, err = s.jsContext.UpdateStream(streamCfg)
	if err != nil {
		return fmt.Errorf("err jsContext.UpdateStream: %w", err)
	}

	_, err = s.jsContext.ConsumerInfo(streamCfg.Name, streamCfg.Name)
	if err != nil {
		if errors.Is(err, nats_go.ErrConsumerNotFound) {
			_, err = s.jsContext.AddConsumer(streamCfg.Name, consumerCfg)
			if err != nil {
				return fmt.Errorf("err jsContext.AddConsumer: %w", err)
			}
		} else {
			return fmt.Errorf("can't get NATS JetStream Consumer %s: %w", consumerCfg.Name, err)
		}
	}

	_, err = s.jsContext.UpdateConsumer(streamCfg.Name, consumerCfg)
	if err != nil {
		return fmt.Errorf("err jsContext.UpdateConsumer: %w", err)
	}

	return nil
}

type eventMarshalerCreateMerchantCompany struct{}

func (*eventMarshalerCreateMerchantCompany) Marshal(topic string, msg *message.Message) (*nats_go.Msg, error) {
	natsMsg := nats_go.NewMsg(topic)
	natsMsg.Data = msg.Payload

	return natsMsg, nil
}
func (*eventMarshalerCreateMerchantCompany) Unmarshal(msg *nats_go.Msg) (*message.Message, error) {
	pbMsg := &pb.EventInfo{}
	err := protojson.Unmarshal(msg.Data, pbMsg)
	if err != nil {
		return nil, err
	}

	wmMsg := message.NewMessage(pbMsg.GetEventId(), msg.Data)
	wmMsg.Metadata = map[string]string{}

	return wmMsg, nil
}

type zapLoggerAdapterCreateMerchantCompany struct {
	logger *zap.Logger
}

func newZapLoggerAdapterCreateMerchantCompany(logger *zap.Logger) *zapLoggerAdapterCreateMerchantCompany {
	return &zapLoggerAdapterCreateMerchantCompany{logger: logger}
}

func (z zapLoggerAdapterCreateMerchantCompany) Error(msg string, err error, fields watermill.LogFields) {
	z.logger.Error(msg, append(z.toZapFields(fields), zap.Error(err))...)
}

func (z zapLoggerAdapterCreateMerchantCompany) Info(msg string, fields watermill.LogFields) {
	z.logger.Info(msg, z.toZapFields(fields)...)
}

func (z zapLoggerAdapterCreateMerchantCompany) Debug(msg string, fields watermill.LogFields) {
	z.logger.Debug(msg, z.toZapFields(fields)...)
}

func (z zapLoggerAdapterCreateMerchantCompany) Trace(msg string, fields watermill.LogFields) {
	z.logger.Debug(msg, z.toZapFields(fields)...)
}

func (z zapLoggerAdapterCreateMerchantCompany) With(fields watermill.LogFields) watermill.LoggerAdapter {
	newLogger := z.logger.With(z.toZapFields(fields)...)
	return &zapLoggerAdapterCreateMerchantCompany{logger: newLogger}
}

func (z zapLoggerAdapterCreateMerchantCompany) toZapFields(fields watermill.LogFields) []zap.Field {
	zapFields := make([]zap.Field, 0, len(fields))
	for k, v := range fields {
		zapFields = append(zapFields, zap.Any(k, v))
	}

	return zapFields
}
