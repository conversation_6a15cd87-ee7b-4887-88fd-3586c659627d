package database

import (
	"context"

	"gorm.io/gorm"

	"git.local/sensitive/innerpb/processing/goerr"
	"git.local/sensitive/processing/acquirer/model"
	"git.local/sensitive/sdk/dog"
)

type TerminalDB struct {
	db *gorm.DB
}

func NewTerminalDB(db *gorm.DB) Terminaler {
	return &TerminalDB{
		db: db,
	}
}

// TODO: посмотреть на фронте где используется
func (t *TerminalDB) UpdateByAcquirerAndStatus(
	ctx context.Context,
	acquirerId uint64,
	searchStatus model.TerminalStatus,
	data *model.Terminal,
) (_ int64, _ int, err error) {
	ctx, span := dog.CreateSpan(ctx, "TerminalDB_UpdateByAcquirerAndStatus")
	defer span.End()

	terminals := make([]*model.Terminal, 0)

	if err = t.db.WithContext(ctx).
		Where("acquirer_id = ?", acquirerId).
		Where("status = ?", searchStatus).
		Find(&terminals).
		Error; err != nil {
		return 0, 0, goerr.ErrDbUnexpected.WithErr(err).WithCtx(ctx)
	}

	request := t.db.WithContext(ctx).
		Where("acquirer_id = ?", acquirerId).
		Where("status = ?", searchStatus)

	if err = request.
		Updates(data).Error; err != nil {
		return 0, 0, goerr.ErrDbUnexpected.WithErr(err).WithCtx(ctx)
	}

	return request.RowsAffected, len(terminals), nil
}

func (t *TerminalDB) GetAllByPaymentType(
	ctx context.Context,
	projectID, transactionTypeID uint64,
) (_ []*model.Terminal, err error) {
	ctx, span := dog.CreateSpan(ctx, "TerminalDB_GetAllByPaymentType")
	defer span.End()

	terminals := make([]*model.Terminal, 0)

	var terminalProjects []model.TerminalProject

	err = t.db.WithContext(ctx).
		Where("project_id = ?", projectID).
		Where("transaction_type_id = ?", transactionTypeID).
		Where("is_active", true).
		Find(&terminalProjects).Error
	if err != nil {
		return nil, goerr.ErrDbUnexpected.WithErr(err).WithCtx(ctx)
	}

	terminalProjectsIds := make([]uint64, len(terminalProjects))
	for i, v := range terminalProjects {
		terminalProjectsIds[i] = v.ID
	}

	err = t.db.WithContext(ctx).
		Table("acquirer.terminals AS t").
		Joins("LEFT JOIN acquirer.terminal_projects AS tp on tp.terminal_id = t.id").
		Where("tp.id IN (?)", terminalProjectsIds).
		Where(`t.status = ?`, model.TerminalOn).
		Preload("Acquirer").
		Find(&terminals).Error
	if err != nil {
		return nil, goerr.ErrDbUnexpected.WithErr(err).WithCtx(ctx)
	}

	return terminals, nil
}

func (t *TerminalDB) GetAllPaymentTypes(
	ctx context.Context,
	projectID uint64,
) (_ []*model.ResponsePaymentType, err error) {
	ctx, span := dog.CreateSpan(ctx, "TerminalDB_GetAllPaymentTypes")
	defer span.End()

	result := make([]*model.ResponsePaymentType, 0)

	request := t.db.WithContext(ctx).
		Model(&model.Terminal{})

	if err = request.
		Table("acquirer.terminals AS t").
		Select(`distinct tp.transaction_type_id, tp.project_id`).
		Joins("LEFT JOIN acquirer.terminal_projects AS tp on tp.terminal_id = t.id").
		Where(`tp.project_id = ?`, projectID).
		Where(`tp.is_active = ?`, true).
		Where(`t.status = ?`, model.TerminalOn).
		Scan(&result).Error; err != nil {
		return nil, goerr.ErrDbUnexpected.WithErr(err).WithCtx(ctx)
	}

	if request.RowsAffected == 0 || len(result) == 0 {
		return nil, goerr.ErrTerminalNotFound
	}

	return result, nil
}

func (t *TerminalDB) FindActiveTerminalsByProject(
	ctx context.Context,
	projectId uint64,
	transactionTypeID uint64,
) (terminals model.Terminals, err error) {
	ctx, span := dog.CreateSpan(ctx, "TerminalDB_FindActiveTerminalsByProject")
	defer span.End()

	var terminalProjects []model.TerminalProject
	err = t.db.WithContext(ctx).
		Where("project_id = ?", projectId).
		Where("transaction_type_id = ?", transactionTypeID).
		Where("is_active", true).
		Find(&terminalProjects).Error

	if err != nil {
		return nil, goerr.ErrDbUnexpected.WithErr(err).WithCtx(ctx)
	}

	terminalProjectIDs := make([]uint64, len(terminalProjects))
	for i, v := range terminalProjects {
		terminalProjectIDs[i] = v.ID
	}

	err = t.db.WithContext(ctx).
		Table("acquirer.terminals AS t").
		Joins("LEFT JOIN acquirer.terminal_projects AS tp on tp.terminal_id = t.id").
		Where("tp.id IN (?)", terminalProjectIDs).
		Where("t.status = ?", model.TerminalOn).
		Preload("Acquirer").
		Find(&terminals).Error
	if err != nil {
		return nil, goerr.ErrDbUnexpected.WithErr(err).WithCtx(ctx)
	}

	return terminals, nil
}

func (t *TerminalDB) FindActiveTerminalsByIDs(
	ctx context.Context,
	ids []uint64,
	projectID uint64,
	transactionTypeID uint64,
) (terminals model.Terminals, err error) {
	ctx, span := dog.CreateSpan(ctx, "TerminalDB_FindActiveTerminalsByIDs")
	defer span.End()

	var terminalProjects []model.TerminalProject
	err = t.db.WithContext(ctx).
		Table("acquirer.terminal_projects").
		Where("terminal_id IN (?) and is_active = ? and project_id = ? and transaction_type_id = ?",
			ids, true, projectID, transactionTypeID).
		Find(&terminalProjects).Error

	if err != nil {
		return nil, goerr.ErrDbUnexpected.WithErr(err).WithCtx(ctx)
	}

	terminalProjectIDs := make([]uint64, len(terminalProjects))
	for i, v := range terminalProjects {
		terminalProjectIDs[i] = v.ID
	}

	err = t.db.WithContext(ctx).
		Table("acquirer.terminals AS t").
		Joins("LEFT JOIN acquirer.terminal_projects AS tp on tp.terminal_id = t.id").
		Where("tp.id IN (?)", terminalProjectIDs).
		Where("t.status = ?", model.TerminalOn).
		Preload("Acquirer").
		Find(&terminals).Error
	if err != nil {
		return nil, goerr.ErrDbUnexpected.WithErr(err).WithCtx(ctx)
	}

	return terminals, nil
}

func (t *TerminalDB) UpdateConfig(ctx context.Context, id uint64, encryptedConfig string) (err error) {
	ctx, span := dog.CreateSpan(ctx, "TerminalDB_UpdateConfig")
	defer span.End()

	if err = t.db.Where("id = ?", id).Model(&model.Terminal{}).
		Update("encrypted_config", encryptedConfig).Error; err != nil {
		return goerr.ErrDbUnexpected.WithErr(err).WithCtx(ctx)
	}

	return nil
}
