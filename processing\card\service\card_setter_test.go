package service

import (
	"context"
	"crypto/aes"
	"errors"
	"testing"

	"git.local/sensitive/innerpb/processing/goerr"
	"git.local/sensitive/innerpb/processing/grpc/grpcmock"
	"git.local/sensitive/pkg/encryptor"
	"git.local/sensitive/sdk/dog"
	"git.local/sensitive/testsdk"

	"github.com/golang/mock/gomock"
	"github.com/stretchr/testify/require"

	goevents "git.local/sensitive/innerpb/processing/events"
	"git.local/sensitive/innerpb/processing/grpc"
	"git.local/sensitive/pkg/middlewares"
	"git.local/sensitive/processing/card/model"
	"git.local/sensitive/processing/card/repository/database/databasemocks"
	"git.local/sensitive/sdk/dog/e2e"
)

func TestModifyCardPan(t *testing.T) {
	type getClientCardsOp struct {
		inputProjectClientID string
		inputProjectID       uint64
		output               model.Cards
		outputErr            error
	}

	type getActualKeyOp struct {
		isCalled  bool
		output    model.Key
		outputErr error
	}

	type modifyCardPanOp struct {
		isCalled     bool
		input        uint64
		inputPayload string
		outputErr    error
	}

	type decryptOp struct {
		input     encryptor.EncryptedCard
		output    encryptor.DecryptedCard
		outputErr error
	}

	tests := []struct {
		name           string
		req            *goevents.ModifyPan
		wantErr        error
		getClientCards getClientCardsOp
		decrypt        decryptOp
		getActualKey   getActualKeyOp
		modifyCardPan  modifyCardPanOp
		appConfig      map[string]any
	}{
		{
			name: "error_getting_client_cards",
			req: &goevents.ModifyPan{
				ProjectClientId: "some client id",
				ProjectId:       1,
			},
			wantErr: errors.New("some error"),
			getClientCards: getClientCardsOp{
				inputProjectClientID: "some client id",
				inputProjectID:       1,
				output:               nil,
				outputErr:            errors.New("some error"),
			},
		},
		{
			name: "decryptor_error",
			req: &goevents.ModifyPan{
				ProjectClientId: "some client id",
				ProjectId:       1,
				Pan:             []byte("some pan"),
			},
			wantErr: errors.New("some error"),
			getClientCards: getClientCardsOp{
				inputProjectClientID: "some client id",
				inputProjectID:       1,
				output:               nil,
				outputErr:            nil,
			},
			decrypt: decryptOp{
				input: encryptor.EncryptedCard{
					Number: []byte("some pan"),
				},
				output:    encryptor.DecryptedCard{},
				outputErr: errors.New("some error"),
			},
		},
		{
			name: "get_actual_key_error",
			req: &goevents.ModifyPan{
				ProjectClientId: "some client id",
				ProjectId:       1,
				Pan:             []byte("some pan"),
			},
			wantErr: errors.New("some error"),
			getClientCards: getClientCardsOp{
				inputProjectClientID: "some client id",
				inputProjectID:       1,
				output: model.Cards{
					{
						Id:          1,
						MaskedPan:   "some pan",
						Pan:         "some pan",
						EncryptName: "some name",
						HashedPan:   "$2a$10$4q4g2vSqVd/9Q3ZpIlngheW01oTebFnMgqU.NydDm.OoGgjRFQKT.",
					},
				},
				outputErr: nil,
			},
			decrypt: decryptOp{
				input: encryptor.EncryptedCard{
					Number: []byte("some pan"),
				},
				output: encryptor.DecryptedCard{
					Number: "some pan",
				},
				outputErr: nil,
			},
			getActualKey: getActualKeyOp{
				isCalled:  true,
				output:    model.Key{},
				outputErr: errors.New("some error"),
			},
		},
		{
			name: "aes_decryption_error",
			req: &goevents.ModifyPan{
				ProjectClientId: "some client id",
				ProjectId:       1,
				Pan:             []byte("some pan"),
			},
			wantErr: aes.KeySizeError(3),
			getClientCards: getClientCardsOp{
				inputProjectClientID: "some client id",
				inputProjectID:       1,
				output: model.Cards{
					{
						Id:          1,
						MaskedPan:   "some pan",
						Pan:         "some pan",
						EncryptName: "some name",
						HashedPan:   "$2a$10$4q4g2vSqVd/9Q3ZpIlngheW01oTebFnMgqU.NydDm.OoGgjRFQKT.",
					},
				},
				outputErr: nil,
			},
			decrypt: decryptOp{
				input: encryptor.EncryptedCard{
					Number: []byte("some pan"),
				},
				output: encryptor.DecryptedCard{
					Number: "some pan",
				},
				outputErr: nil,
			},
			getActualKey: getActualKeyOp{
				isCalled: true,
				output: model.Key{
					Id:  1,
					Key: "AAAAAAAAAAAAAAAAAAAAAD4r1NYivkHsOHdiq9dCiJiz0HJtt7wrVFA8fx/DenGL",
				},
				outputErr: nil,
			},
			appConfig: map[string]any{
				"SYMMETRIC_KEY": badKey,
			},
		},
		{
			name: "modify_card_pan_error",
			req: &goevents.ModifyPan{
				ProjectClientId: "some client id",
				ProjectId:       1,
				Pan:             []byte("some pan"),
			},
			wantErr: errors.New("some error"),
			getClientCards: getClientCardsOp{
				inputProjectClientID: "some client id",
				inputProjectID:       1,
				output: model.Cards{
					{
						Id:          1,
						MaskedPan:   "some pan",
						Pan:         "some pan",
						EncryptName: "some name",
						HashedPan:   "$2a$10$4q4g2vSqVd/9Q3ZpIlngheW01oTebFnMgqU.NydDm.OoGgjRFQKT.",
					},
				},
				outputErr: nil,
			},
			decrypt: decryptOp{
				input: encryptor.EncryptedCard{
					Number: []byte("some pan"),
				},
				output: encryptor.DecryptedCard{
					Number: "some pan",
				},
				outputErr: nil,
			},
			getActualKey: getActualKeyOp{
				isCalled: true,
				output: model.Key{
					Id:  1,
					Key: "AAAAAAAAAAAAAAAAAAAAAD4r1NYivkHsOHdiq9dCiJiz0HJtt7wrVFA8fx/DenGL",
				},
				outputErr: nil,
			},
			modifyCardPan: modifyCardPanOp{
				isCalled:     true,
				input:        1,
				inputPayload: "AAAAAAAAAAAAAAAAAAAAAECsfK75SmtqDbta4UnLlH4=",
				outputErr:    errors.New("some error"),
			},
			appConfig: map[string]any{
				"SYMMETRIC_KEY": normalKey,
			},
		},
		{
			name: "success",
			req: &goevents.ModifyPan{
				ProjectClientId: "some client id",
				ProjectId:       1,
				Pan:             []byte("some pan"),
			},
			wantErr: nil,
			getClientCards: getClientCardsOp{
				inputProjectClientID: "some client id",
				inputProjectID:       1,
				output: model.Cards{
					{
						Id:          1,
						MaskedPan:   "some pan",
						Pan:         "some pan",
						EncryptName: "some name",
						HashedPan:   "$2a$10$4q4g2vSqVd/9Q3ZpIlngheW01oTebFnMgqU.NydDm.OoGgjRFQKT.",
					},
				},
				outputErr: nil,
			},
			decrypt: decryptOp{
				input: encryptor.EncryptedCard{
					Number: []byte("some pan"),
				},
				output: encryptor.DecryptedCard{
					Number: "some pan",
				},
				outputErr: nil,
			},
			getActualKey: getActualKeyOp{
				isCalled: true,
				output: model.Key{
					Id:  1,
					Key: "AAAAAAAAAAAAAAAAAAAAAD4r1NYivkHsOHdiq9dCiJiz0HJtt7wrVFA8fx/DenGL",
				},
				outputErr: nil,
			},
			modifyCardPan: modifyCardPanOp{
				isCalled:     true,
				input:        1,
				inputPayload: "AAAAAAAAAAAAAAAAAAAAAECsfK75SmtqDbta4UnLlH4=",
				outputErr:    nil,
			},
			appConfig: map[string]any{
				"SYMMETRIC_KEY": normalKey,
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctrl := gomock.NewController(t)

			ctx, cancel := context.WithCancel(context.Background())
			e2e.InitAppE2E(t, ctx, cancel, nil, nil, tt.appConfig)

			cardsDBMock := databasemocks.NewMockCarder(ctrl)
			keyDBMock := databasemocks.NewMockKeyer(ctrl)
			cardUpdatorDBMock := databasemocks.NewMockCardUpdator(ctrl)
			decryptorMock := encryptor.NewMockDecryptor(ctrl)

			cardsDBMock.EXPECT().GetClientCards(
				gomock.Any(),
				&middlewares.PaginationInfo{},
				tt.getClientCards.inputProjectClientID,
				tt.getClientCards.inputProjectID,
			).Return(
				tt.getClientCards.output,
				tt.getClientCards.outputErr,
			)

			decryptorMock.EXPECT().DecryptCard(tt.decrypt.input).
				Return(tt.decrypt.output, tt.decrypt.outputErr).AnyTimes()

			if tt.getActualKey.isCalled {
				keyDBMock.EXPECT().GetActualKey(
					gomock.Any(),
				).Return(
					tt.getActualKey.output,
					tt.getActualKey.outputErr,
				)
			}

			if tt.modifyCardPan.isCalled {
				cardUpdatorDBMock.EXPECT().ModifyCardPan(
					gomock.Any(),
					tt.modifyCardPan.input,
					tt.modifyCardPan.inputPayload,
				).Return(
					tt.modifyCardPan.outputErr,
				)
			}

			s := CardSetterService{
				cardsRepo:       cardsDBMock,
				keyRepo:         keyDBMock,
				cardUpdatorRepo: cardUpdatorDBMock,
				decryptor:       decryptorMock,
			}

			err := s.ModifyCardPan(context.Background(), tt.req)
			if tt.wantErr != nil {
				require.Error(t, err)
				require.ErrorContains(t, err, tt.wantErr.Error())
			} else {
				require.NoError(t, err)
			}

		})
	}
}

func TestDeactivateCard(t *testing.T) {
	type getClientByIDOp struct {
		input     uint64
		output    *model.Client
		outputErr error
	}

	type checkProjectOp struct {
		isCalled  bool
		input     *grpc.CheckMerchantProjectRequestV1
		outputErr error
	}

	type getCardByIDOp struct {
		isCalled  bool
		input     uint64
		outputErr error
		output    model.Card
	}

	type deactivateCardByIDOp struct {
		isCalled  bool
		input     uint64
		outputErr error
	}

	tests := []struct {
		name                               string
		reqMerchID, reqClientID, reqCardID uint64
		wantErr                            error
		getClientByID                      getClientByIDOp
		checkProject                       checkProjectOp
		getCardByID                        getCardByIDOp
		deactivateCardByID                 deactivateCardByIDOp
	}{
		{
			name:        "get_client_by_id_error",
			reqCardID:   1,
			reqMerchID:  2,
			reqClientID: 3,
			wantErr:     errors.New("some error"),
			getClientByID: getClientByIDOp{
				input:     3,
				output:    nil,
				outputErr: errors.New("some error"),
			},
		},
		{
			name:        "check_project_error",
			reqCardID:   1,
			reqMerchID:  2,
			reqClientID: 3,
			wantErr:     errors.New("some error"),
			getClientByID: getClientByIDOp{
				input: 3,
				output: &model.Client{
					ProjectId: 10,
				},
				outputErr: nil,
			},
			checkProject: checkProjectOp{
				isCalled: true,
				input: &grpc.CheckMerchantProjectRequestV1{
					MerchantId: testsdk.Ptr(uint64(2)),
					ProjectId:  testsdk.Ptr(uint64(10)),
				},
				outputErr: errors.New("some error"),
			},
		},
		{
			name:        "get_card_by_id_error",
			reqCardID:   1,
			reqMerchID:  2,
			reqClientID: 3,
			wantErr:     errors.New("some error"),
			getClientByID: getClientByIDOp{
				input: 3,
				output: &model.Client{
					ProjectId: 10,
				},
				outputErr: nil,
			},
			checkProject: checkProjectOp{
				isCalled: true,
				input: &grpc.CheckMerchantProjectRequestV1{
					MerchantId: testsdk.Ptr(uint64(2)),
					ProjectId:  testsdk.Ptr(uint64(10)),
				},
				outputErr: nil,
			},
			getCardByID: getCardByIDOp{
				isCalled:  true,
				input:     1,
				outputErr: errors.New("some error"),
				output:    model.Card{},
			},
		},
		{
			name:        "card_is_not_approved",
			reqCardID:   1,
			reqMerchID:  2,
			reqClientID: 3,
			wantErr:     goerr.ErrCardAlreadyDeactivated,
			getClientByID: getClientByIDOp{
				input: 3,
				output: &model.Client{
					ProjectId: 10,
				},
				outputErr: nil,
			},
			checkProject: checkProjectOp{
				isCalled: true,
				input: &grpc.CheckMerchantProjectRequestV1{
					MerchantId: testsdk.Ptr(uint64(2)),
					ProjectId:  testsdk.Ptr(uint64(10)),
				},
				outputErr: nil,
			},
			getCardByID: getCardByIDOp{
				isCalled:  true,
				input:     1,
				outputErr: nil,
				output: model.Card{
					Id:       1,
					Approved: false,
				},
			},
		},
		{
			name:        "success",
			reqCardID:   1,
			reqMerchID:  2,
			reqClientID: 3,
			wantErr:     nil,
			getClientByID: getClientByIDOp{
				input: 3,
				output: &model.Client{
					ProjectId: 10,
				},
				outputErr: nil,
			},
			checkProject: checkProjectOp{
				isCalled: true,
				input: &grpc.CheckMerchantProjectRequestV1{
					MerchantId: testsdk.Ptr(uint64(2)),
					ProjectId:  testsdk.Ptr(uint64(10)),
				},
				outputErr: nil,
			},
			getCardByID: getCardByIDOp{
				isCalled:  true,
				input:     1,
				outputErr: nil,
				output: model.Card{
					Id:       1,
					Approved: true,
				},
			},
			deactivateCardByID: deactivateCardByIDOp{
				isCalled:  true,
				input:     1,
				outputErr: nil,
			},
		},
		{
			name:        "deactivate_client_card_error",
			reqCardID:   1,
			reqMerchID:  2,
			reqClientID: 3,
			wantErr:     errors.New("some error"),
			getClientByID: getClientByIDOp{
				input: 3,
				output: &model.Client{
					ProjectId: 10,
				},
				outputErr: nil,
			},
			checkProject: checkProjectOp{
				isCalled: true,
				input: &grpc.CheckMerchantProjectRequestV1{
					MerchantId: testsdk.Ptr(uint64(2)),
					ProjectId:  testsdk.Ptr(uint64(10)),
				},
				outputErr: nil,
			},
			getCardByID: getCardByIDOp{
				isCalled:  true,
				input:     1,
				outputErr: nil,
				output: model.Card{
					Id:       1,
					Approved: true,
				},
			},
			deactivateCardByID: deactivateCardByIDOp{
				isCalled:  true,
				input:     1,
				outputErr: errors.New("some error"),
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctrl := gomock.NewController(t)

			clientDBMock := databasemocks.NewMockClientor(ctrl)
			cardDBMock := databasemocks.NewMockCarder(ctrl)
			merchantCliMock := grpcmock.NewMockMerchantClient(ctrl)
			cardUpdatorMock := databasemocks.NewMockCardUpdator(ctrl)

			s := CardSetterService{
				clientsRepo:     clientDBMock,
				cardsRepo:       cardDBMock,
				cardUpdatorRepo: cardUpdatorMock,
				merchantClient:  merchantCliMock,
			}

			clientDBMock.EXPECT().GetByID(
				gomock.Any(),
				tt.getClientByID.input,
			).Return(
				tt.getClientByID.output,
				tt.getClientByID.outputErr,
			)

			if tt.checkProject.isCalled {
				merchantCliMock.EXPECT().CheckProject(
					gomock.Any(),
					tt.checkProject.input,
				).Return(
					nil,
					tt.checkProject.outputErr,
				)
			}

			if tt.getCardByID.isCalled {
				cardDBMock.EXPECT().GetByID(
					gomock.Any(),
					tt.getCardByID.input,
				).Return(
					tt.getCardByID.output,
					tt.getCardByID.outputErr,
				)
			}

			if tt.deactivateCardByID.isCalled {
				cardUpdatorMock.EXPECT().DeactivateClientCard(
					gomock.Any(),
					tt.deactivateCardByID.input,
				).Return(
					tt.deactivateCardByID.outputErr,
				)
			}

			err := s.DeactivateCard(context.Background(), tt.reqMerchID, tt.reqClientID, tt.reqCardID)
			if tt.wantErr != nil {
				require.Error(t, err)
				require.ErrorContains(t, err, tt.wantErr.Error())
			} else {
				require.NoError(t, err)
			}
		})
	}
}

func TestCheckIsExpired(t *testing.T) {
	type getAllNotExpiredOp struct {
		outputs []struct {
			lastID    uint64
			output    []model.Card
			outputErr error
		}
	}

	type getKeyByIDOp struct {
		isCalled  bool
		inputID   uint64
		output    model.Key
		outputErr error
	}

	type setExpiredBatchOp struct {
		isCalled  bool
		input     []uint64
		outputErr error
	}

	expiredIDs := []uint64{2}

	validYear, _ := dog.AESEncrypt("29", openKey)
	expiredYear, _ := dog.AESEncrypt("24", openKey)
	month, _ := dog.AESEncrypt("01", openKey)

	tests := []struct {
		name             string
		wantErr          string
		appConfig        map[string]any
		getAllNotExpired getAllNotExpiredOp
		getKeyByID       getKeyByIDOp
		setExpiredBatch  setExpiredBatchOp
	}{
		{
			name:    "error_getAllNotExpired",
			wantErr: "some error",
			appConfig: map[string]any{
				"SYMMETRIC_KEY": symmetricKey,
			},
			getAllNotExpired: getAllNotExpiredOp{
				outputs: []struct {
					lastID    uint64
					output    []model.Card
					outputErr error
				}{
					{
						lastID:    0,
						output:    nil,
						outputErr: errors.New("some error"),
					},
				},
			},
		},
		{
			name:    "success_one_batch_no_expired",
			wantErr: "",
			appConfig: map[string]any{
				"SYMMETRIC_KEY": symmetricKey,
			},
			getAllNotExpired: getAllNotExpiredOp{
				outputs: []struct {
					lastID    uint64
					output    []model.Card
					outputErr error
				}{
					{
						lastID: 0,
						output: []model.Card{
							{
								Id:           1,
								KeyId:        1,
								EncryptYear:  validYear,
								EncryptMonth: month,
							},
						},
						outputErr: nil,
					},
					{
						lastID:    1,
						output:    []model.Card{},
						outputErr: nil,
					},
				},
			},
			getKeyByID: getKeyByIDOp{
				isCalled: true,
				inputID:  1,
				output: model.Key{
					Key: encryptHashKey,
				},
				outputErr: nil,
			},
			setExpiredBatch: setExpiredBatchOp{
				isCalled: false, // потому что не истекает
			},
		},
		{
			name:    "success_one_batch_expired",
			wantErr: "",
			appConfig: map[string]any{
				"SYMMETRIC_KEY": symmetricKey,
			},
			getAllNotExpired: getAllNotExpiredOp{
				outputs: []struct {
					lastID    uint64
					output    []model.Card
					outputErr error
				}{
					{
						lastID: 0,
						output: []model.Card{
							{
								Id:           2,
								KeyId:        1,
								EncryptYear:  expiredYear,
								EncryptMonth: month,
							},
						},
						outputErr: nil,
					},
					{
						lastID:    2,
						output:    []model.Card{},
						outputErr: nil,
					},
				},
			},
			getKeyByID: getKeyByIDOp{
				isCalled: true,
				inputID:  1,
				output: model.Key{
					Key: encryptHashKey,
				},
				outputErr: nil,
			},
			setExpiredBatch: setExpiredBatchOp{
				isCalled:  true,
				input:     expiredIDs,
				outputErr: nil,
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctrl := gomock.NewController(t)
			defer ctrl.Finish()

			ctx := context.Background()

			e2e.InitAppE2E(t, ctx, nil, nil, nil, tt.appConfig)

			cardsRepoMock := databasemocks.NewMockCarder(ctrl)
			keyRepoMock := databasemocks.NewMockKeyer(ctrl)
			cardUpdatorRepoMock := databasemocks.NewMockCardUpdator(ctrl)

			s := CardSetterService{
				cardsRepo:       cardsRepoMock,
				keyRepo:         keyRepoMock,
				cardUpdatorRepo: cardUpdatorRepoMock,
			}

			if len(tt.getAllNotExpired.outputs) > 0 {
				for _, out := range tt.getAllNotExpired.outputs {
					cardsRepoMock.EXPECT().
						GetAllNotExpired(gomock.Any(), out.lastID, batchSize).
						Return(out.output, out.outputErr)
				}
			}

			if tt.getKeyByID.isCalled {
				keyRepoMock.EXPECT().
					GetById(gomock.Any(), tt.getKeyByID.inputID).
					Return(tt.getKeyByID.output, tt.getKeyByID.outputErr)
			}

			if tt.setExpiredBatch.isCalled {
				cardUpdatorRepoMock.EXPECT().
					SetExpiredBatch(gomock.Any(), gomock.Any()).
					Return(tt.setExpiredBatch.outputErr)
			}

			err := s.CheckIsExpired(ctx)

			if tt.wantErr != "" {
				require.Equal(t, tt.wantErr, testsdk.ErrorString(err))
				require.Error(t, err)
			} else {
				require.NoError(t, err)
			}
		})
	}
}
