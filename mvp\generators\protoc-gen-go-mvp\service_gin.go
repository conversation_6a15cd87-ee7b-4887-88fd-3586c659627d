package main

import (
	"fmt"
	"net/http"
	"path/filepath"
	"strconv"
	"strings"

	"google.golang.org/protobuf/compiler/protogen"
	"google.golang.org/protobuf/proto"
	"google.golang.org/protobuf/reflect/protoreflect"

	"git.local/sensitive/mvp/pb"
)

func init() {
	RegisterGenerator(&GinGenerator{})
}

type GinGenerator struct{}

func (generator *GinGenerator) ProtoGenGenerate(gen *protogen.Plugin) {
	generator.Services(gen)
}

func (generator *GinGenerator) Services(gen *protogen.Plugin) {
	for _, file := range gen.Files {
		if !file.Generate {
			continue
		}

		if len(file.Services) == 0 {
			return
		}

		dir := filepath.Dir(file.GeneratedFilenamePrefix)
		mockGenNamePackage := filepath.Base(dir)
		fileName := filepath.Base(file.GeneratedFilenamePrefix + V2GinSuffix)
		mockGenFileName := filepath.Base(file.GeneratedFilenamePrefix + V2GinSuffix)

		g := gen.NewGeneratedFile(
			file.GeneratedFilenamePrefix+V2GinSuffix,
			file.GoImportPath,
		)
		HeaderPrint(g, string(file.GoPackageName))

		for _, service := range file.Services {
			g.P("func RegisterGin", service.GoName, "RouterGroup(")
			g.P("in *", ginRouterGroup, ",")
			g.P("handler func(func (c *", ginContext, ") error) ", ginHandlerFunc, ",")
			g.P(") error {")
			g.P("service, err := NewGin", service.GoName, "Service()")
			g.P("if err != nil {")
			g.P("return err")
			g.P("}")
			g.P()

			relativePath, _ := proto.GetExtension(service.Desc.Options(), pb.E_RelativePath).(string)
			if relativePath == "" {
				relativePath = string(service.Desc.FullName())
			}

			g.P("routerGroup := in.Group(", strconv.Quote(relativePath), ")")
			httpMethods := map[protoreflect.FullName]string{}
			httpPaths := map[protoreflect.FullName]string{}
			for _, method := range service.Methods {
				httpMethod, _ := proto.GetExtension(method.Desc.Options(), pb.E_Method).(pb.GinMethod)

				methodName := "PUT"
				switch httpMethod {
				case pb.GinMethod_GinPatch:
					methodName = "PATCH"
				case pb.GinMethod_GinPost:
					methodName = "POST"
				}

				httpMethods[method.Desc.FullName()] = methodName

				path := string(method.Desc.Name())

				httpPath, _ := proto.GetExtension(method.Desc.Options(), pb.E_MethodPath).(string)
				if httpPath != "" {
					path = httpPath
				}

				httpPaths[method.Desc.FullName()] = path

				g.P("routerGroup.", methodName, "(", strconv.Quote("/"+path), ", handler(service.", method.GoName, "))")
			}
			g.P("return nil")
			g.P("}")
			g.P()

			g.P("func NewGin", service.GoName, "Service (")

			g.P(") (Gin", service.GoName, "Server, error) {")
			g.P("client, err := NewPrepared", service.GoName, "Client()")
			g.P("if err != nil {")
			g.P("return nil, err")
			g.P("}")
			g.P()
			g.P("return &gin", service.GoName, "Server{")
			g.P("client: NewLogged", service.GoName, "Client(")
			g.P("NewIam", service.GoName, "Client(client),")
			g.P("),")
			g.P("}, nil")
			g.P("}")
			g.P()

			g.P("//go:generate mockgen -destination=./", mockGenNamePackage, "mock/", mockGenFileName, " -package=", mockGenNamePackage, "mock -source=", fileName, " Gin", service.GoName, "Server")
			g.P("type Gin", service.GoName, "Server interface {")
			for _, method := range service.Methods {
				g.P(method.GoName, "(c *", ginContext, ") error")
			}
			g.P("}")
			g.P()

			g.P("var _ Gin", service.GoName, "Server = (*gin", service.GoName, "Server)(nil)")
			g.P()

			g.P("type gin", service.GoName, "Server struct{")
			g.P("client ", service.GoName, "Client")
			g.P("}")
			g.P()
			for _, method := range service.Methods {
				g.P("type ", service.GoName, "_", method.GoName, "_Success struct{")
				g.P("// Status is the status of the response. Set true")
				g.P("Status bool `json:\"status\"`")
				g.P("// Message is a human-readable explanation of the status")
				g.P("Message string `json:\"message\"`")
				g.P("// StatusCode is the GoErr status. Set 0")
				g.P("StatusCode int `json:\"status_code\"`")
				g.P("// Result is the result of the request")
				g.P("Result *", method.Output.GoIdent, " `json:\"result\"`")
				g.P("}")
				g.P()
				g.P("type ", service.GoName, "_", method.GoName, "_Failure struct{")
				g.P("// Status is the status of the response. Set false")
				g.P("Status bool `json:\"status\"`")
				g.P("// Message is a human-readable explanation of the status")
				g.P("Message string `json:\"message\"`")
				g.P("// StatusCode is the GoErr status")
				g.P("StatusCode int `json:\"status_code\"`")
				g.P("}")
				g.P()

				g.P("// ", method.GoName)
				if len(method.Comments.Leading) > 0 {
					g.P("// @Summary ", strings.ReplaceAll(string(method.Comments.Leading), "\n", " "))
				} else {
					g.P("// @Summary ", method.GoName)
				}
				g.P("// @Security bearerAuth")
				g.P("// @ID ", service.GoName, "_", method.GoName)
				g.P("// @Accept json")
				g.P("// @Param request body ", method.Input.GoIdent, " true ", strconv.Quote(fmt.Sprint(method.Input.GoIdent.GoName)))
				g.P("// @Success 200 {object} ", service.GoName, "_", method.GoName, "_Success")
				g.P("// @Failure ", http.StatusUnauthorized, " {object} ", service.GoName, "_", method.GoName, "_Failure \"goerrpkg.ErrorTypeAuthorization\"")
				g.P("// @Failure ", http.StatusNotFound, " {object} ", service.GoName, "_", method.GoName, "_Failure \"goerrpkg.ErrorTypeNotFound\"")
				g.P("// @Failure ", http.StatusBadRequest, " {object} ", service.GoName, "_", method.GoName, "_Failure \"goerrpkg.ErrorTypeBadRequest\"")
				g.P("// @Failure ", http.StatusInternalServerError, " {object} ", service.GoName, "_", method.GoName, "_Failure \"goerrpkg.ErrorTypeUnexpectedError\"")
				g.P("// @Failure ", http.StatusBadRequest, " {object} ", service.GoName, "_", method.GoName, "_Failure \"gin.ErrorTypeBind\"")
				g.P("// @Failure ", http.StatusInternalServerError, " {object} ", service.GoName, "_", method.GoName, "_Failure \"Undefined error\"")
				g.P("// @Produce json")
				g.P("// @Router /", relativePath, "/", httpPaths[method.Desc.FullName()], " [", strings.ToLower(httpMethods[method.Desc.FullName()]), "]")
				g.P("// @tags ", service.GoName)

				g.P("func (s *gin", service.GoName, "Server) ", method.GoName, "(c *", ginContext, ") error {")
				g.P("ctx, span := ", dogPackageCreateSpan, "(c.Request.Context(), ", strconv.Quote("Gin"+service.GoName+"Server_"+method.GoName), ")")
				g.P("defer span.End()")
				g.P()
				g.P("var request ", method.Input.GoIdent)
				g.P("if err := c.ShouldBindJSON(&request); err != nil {")
				g.P("return err")
				g.P("}")
				g.P()
				g.P("ctx = ", metadataPackageNewOutgoingContext, "(ctx, ", metadataPackageMD, "(c.Request.Header))")
				g.P()
				g.P("response, err := s.client.", method.GoName, "(ctx, &request)")
				g.P("if err != nil {")
				g.P("return err")
				g.P("}")
				g.P()
				g.P("c.JSON(", netHttpPackageStatusOK, ", &", service.GoName, "_", method.GoName, "_Success{")
				g.P("Status: true,")
				g.P("Message: \"Success\",")
				g.P("StatusCode: 0,")
				g.P("Result: response,")
				g.P("})")
				g.P("return nil")
				g.P("}")
				g.P()
			}
		}
	}
}
