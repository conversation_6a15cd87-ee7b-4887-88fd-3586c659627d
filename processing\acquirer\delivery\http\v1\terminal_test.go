package v1

//func TestHandler_CreateTerminal(t *testing.T) {
//	r := gin.Default()
//	testTable := []struct {
//		name               string
//		request            *events.CreateTerminalRequest
//		method             string
//		url                string
//		expectedStatusCode int
//	}{
//		{
//			name: "aa",
//			request: &events.CreateTerminalRequest{AcquirerID: 1,
//				ProjectID:         5,
//				TransactionTypeID: 6,
//				Status:            1.0,
//			},
//			method:             "POST",
//			url:                "/terminal",
//			expectedStatusCode: http.StatusOK,
//		},
//	}
//	P := Servicemock.NewTerminaler(t)
//
//	P.On("Create", context.Background(), testTable[0].request).
//		Return(&model.Terminal{}, nil)
//
//	h := NewHandler(&service.Services{Terminal: P}, nil)
//	h.Init(r.Group("api"))
//	for _, testCase := range testTable {
//		t.Run(testCase.name, func(t *testing.T) {
//			r := gin.New()
//			r.POST("/terminal",
//				middleware.GinErrorHandle(h.CreateTerminal))
//			if testCase.request != nil {
//				jsonValue, err := json.Marshal(testCase.request)
//				if err != nil {
//					t.Fatalf("Failed to marshal address: %v", err)
//				}
//
//				req, err := http.NewRequest(testCase.method, testCase.url, bytes.NewBuffer(jsonValue))
//				if err != nil {
//					t.Fatalf("Failed to create request: %v", err)
//				}
//				req.Header.Add("Content-Type", "application/json")
//
//				w := httptest.NewRecorder()
//				r.ServeHTTP(w, req)
//				assert.Equal(t, testCase.expectedStatusCode, w.Code)
//			} else {
//				req, err := http.NewRequest(testCase.method, testCase.url, nil)
//				if err != nil {
//					t.Fatalf("Failed to create request: %v", err)
//				}
//
//				w := httptest.NewRecorder()
//				r.ServeHTTP(w, req)
//				assert.Equal(t, testCase.expectedStatusCode, w.Code)
//			}
//		})
//	}
//}

//func TestHandler_UpdateTerminal(t *testing.T) {
//	r := gin.Default()
//	testTable := []struct {
//		name               string
//		request            *events.CreateTerminalRequest
//		method             string
//		url                string
//		expectedStatusCode int
//	}{
//		{
//			name: "aa",
//			request: &events.CreateTerminalRequest{AcquirerID: 1,
//				ProjectID:         5,
//				TransactionTypeID: 6,
//				Status:            model.TerminalStatus(1.0),
//			},
//			method:             "PUT",
//			url:                "/terminal/1",
//			expectedStatusCode: http.StatusOK,
//		},
//	}
//	P := Servicemock.NewTerminaler(t)
//
//	P.On("Update", context.Background(), uint64(1), testTable[0].request).
//		Return(nil)
//
//	h := NewHandler(&service.Services{Terminal: P}, nil)
//	h.Init(r.Group("terminal"))
//	for _, testCase := range testTable {
//		t.Run(testCase.name, func(t *testing.T) {
//			r := gin.New()
//			r.PUT("/terminal/:terminal_id",
//				middleware.GinErrorHandle(h.Update))
//			if testCase.request != nil {
//				jsonValue, err := json.Marshal(testCase.request)
//				if err != nil {
//					t.Fatalf("Failed to marshal address: %v", err)
//				}
//
//				req, err := http.NewRequest(testCase.method, testCase.url, bytes.NewBuffer(jsonValue))
//				if err != nil {
//					t.Fatalf("Failed to create request: %v", err)
//				}
//				req.Header.Add("Content-Type", "application/json")
//
//				w := httptest.NewRecorder()
//				r.ServeHTTP(w, req)
//				assert.Equal(t, testCase.expectedStatusCode, w.Code)
//			} else {
//				req, err := http.NewRequest(testCase.method, testCase.url, nil)
//				if err != nil {
//					t.Fatalf("Failed to create request: %v", err)
//				}
//
//				w := httptest.NewRecorder()
//				r.ServeHTTP(w, req)
//				assert.Equal(t, testCase.expectedStatusCode, w.Code)
//			}
//		})
//	}
//}

//func TestHandler_UpdateTerminalStatus(t *testing.T) {
//	r := gin.Default()
//	testTable := []struct {
//		name               string
//		request            uint64
//		method             string
//		url                string
//		expectedStatusCode int
//	}{
//		{
//			name:               "aa",
//			request:            1,
//			method:             "PUT",
//			url:                "/terminals/1/status",
//			expectedStatusCode: http.StatusOK,
//		},
//	}
//	P := Servicemock.NewTerminaler(t)
//
//	P.On("UpdateStatus", context.Background(), testTable[0].request).
//		Return(nil)
//
//	h := NewHandler(&service.Services{Terminal: P}, nil)
//	h.Init(r.Group("terminals"))
//	for _, testCase := range testTable {
//		t.Run(testCase.name, func(t *testing.T) {
//			r := gin.New()
//			r.PUT("/terminals/:id/status",
//				middleware.GinErrorHandle(h.UpdateTerminalStatus))
//			if testCase.request != 0 {
//				jsonValue, err := json.Marshal(testCase.request)
//				if err != nil {
//					t.Fatalf("Failed to marshal address: %v", err)
//				}
//
//				req, err := http.NewRequest(testCase.method, testCase.url, bytes.NewBuffer(jsonValue))
//				if err != nil {
//					t.Fatalf("Failed to create request: %v", err)
//				}
//				req.Header.Add("Content-Type", "application/json")
//
//				w := httptest.NewRecorder()
//				r.ServeHTTP(w, req)
//				assert.Equal(t, testCase.expectedStatusCode, w.Code)
//			} else {
//				req, err := http.NewRequest(testCase.method, testCase.url, nil)
//				if err != nil {
//					t.Fatalf("Failed to create request: %v", err)
//				}
//
//				w := httptest.NewRecorder()
//				r.ServeHTTP(w, req)
//				assert.Equal(t, testCase.expectedStatusCode, w.Code)
//			}
//		})
//	}
//}
