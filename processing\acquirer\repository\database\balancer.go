package database

import (
	"context"
	"errors"

	"gorm.io/gorm"

	"git.local/sensitive/innerpb/processing/goerr"
	"git.local/sensitive/processing/acquirer/model"
	"git.local/sensitive/sdk/dog"
)

type RuleWeighterDB struct {
	db *gorm.DB
}

func NewRuleWeighterDB(db *gorm.DB) RuleWeighter {
	return &RuleWeighterDB{
		db: db,
	}
}

func (rw *RuleWeighterDB) GetNextRuleByWeight(ctx context.Context, rule *model.Rule) (_ *model.Rule, err error) {
	ctx, span := dog.CreateSpan(ctx, "RuleWeighterDB_GetNextRuleByWeight")
	defer span.End()

	var nextRule model.Rule

	err = rw.db.WithContext(ctx).
		Model(&nextRule).
		Where("project_id = ?", rule.ProjectID).
		Where("transaction_type_id = ?", rule.TransactionTypeID).
		Where("weight > ?", rule.Weight).
		Order("weight").
		First(&nextRule).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, goerr.ErrRuleNotFound.WithCtx(ctx)
		}

		return nil, err
	}

	return &nextRule, nil
}

func (rw *RuleWeighterDB) ChangeRuleWeightByID(ctx context.Context, ruleID uint64, weight uint8) (err error) {
	ctx, span := dog.CreateSpan(ctx, "RuleWeighterDB_ChangeRuleWeightByID")
	defer span.End()

	return rw.db.WithContext(ctx).
		Model(&model.Rule{}).
		Where("id = ?", ruleID).
		Update("weight", weight).
		Error
}

func (rw *RuleWeighterDB) GetPreviousRuleByWeight(ctx context.Context, rule *model.Rule) (_ *model.Rule, err error) {
	ctx, span := dog.CreateSpan(ctx, "RuleWeighterDB_GetPreviousRuleByWeight")
	defer span.End()

	var prevRule model.Rule

	err = rw.db.WithContext(ctx).
		Model(&prevRule).
		Where("project_id = ?", &rule.ProjectID).
		Where("transaction_type_id = ?", &rule.TransactionTypeID).
		Where("weight < ?", &rule.Weight).
		Order("weight desc").
		First(&prevRule).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, goerr.ErrRuleNotFound.WithCtx(ctx)
		}

		return nil, err
	}

	return &prevRule, nil
}
