// Code generated by MockGen. DO NOT EDIT.
// Source: account_grpc.pb.go

// Package grpcmock is a generated GoMock package.
package grpcmock

import (
	context "context"
	reflect "reflect"

	grpc "git.local/sensitive/innerpb/processing/grpc"
	gomock "github.com/golang/mock/gomock"
	grpc0 "google.golang.org/grpc"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
)

// MockAccountClient is a mock of AccountClient interface.
type MockAccountClient struct {
	ctrl     *gomock.Controller
	recorder *MockAccountClientMockRecorder
}

// MockAccountClientMockRecorder is the mock recorder for MockAccountClient.
type MockAccountClientMockRecorder struct {
	mock *MockAccountClient
}

// NewMockAccountClient creates a new mock instance.
func NewMockAccountClient(ctrl *gomock.Controller) *MockAccountClient {
	mock := &MockAccountClient{ctrl: ctrl}
	mock.recorder = &MockAccountClientMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockAccountClient) EXPECT() *MockAccountClientMockRecorder {
	return m.recorder
}

// CreateTransfer mocks base method.
func (m *MockAccountClient) CreateTransfer(ctx context.Context, in *grpc.CreateTransferRequest, opts ...grpc0.CallOption) (*grpc.CreateTransferResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "CreateTransfer", varargs...)
	ret0, _ := ret[0].(*grpc.CreateTransferResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateTransfer indicates an expected call of CreateTransfer.
func (mr *MockAccountClientMockRecorder) CreateTransfer(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateTransfer", reflect.TypeOf((*MockAccountClient)(nil).CreateTransfer), varargs...)
}

// ExtractFailOrCanceledTransfersByForeignIDs mocks base method.
func (m *MockAccountClient) ExtractFailOrCanceledTransfersByForeignIDs(ctx context.Context, in *grpc.ExtractFailOrCanceledTransfersByForeignIDsRequest, opts ...grpc0.CallOption) (*grpc.ExtractTransfersByForeignIDsResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "ExtractFailOrCanceledTransfersByForeignIDs", varargs...)
	ret0, _ := ret[0].(*grpc.ExtractTransfersByForeignIDsResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ExtractFailOrCanceledTransfersByForeignIDs indicates an expected call of ExtractFailOrCanceledTransfersByForeignIDs.
func (mr *MockAccountClientMockRecorder) ExtractFailOrCanceledTransfersByForeignIDs(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ExtractFailOrCanceledTransfersByForeignIDs", reflect.TypeOf((*MockAccountClient)(nil).ExtractFailOrCanceledTransfersByForeignIDs), varargs...)
}

// ExtractSuccessTransfersByForeignIDs mocks base method.
func (m *MockAccountClient) ExtractSuccessTransfersByForeignIDs(ctx context.Context, in *grpc.ExtractSuccessTransfersByForeignIDsRequest, opts ...grpc0.CallOption) (*grpc.ExtractTransfersByForeignIDsResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "ExtractSuccessTransfersByForeignIDs", varargs...)
	ret0, _ := ret[0].(*grpc.ExtractTransfersByForeignIDsResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ExtractSuccessTransfersByForeignIDs indicates an expected call of ExtractSuccessTransfersByForeignIDs.
func (mr *MockAccountClientMockRecorder) ExtractSuccessTransfersByForeignIDs(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ExtractSuccessTransfersByForeignIDs", reflect.TypeOf((*MockAccountClient)(nil).ExtractSuccessTransfersByForeignIDs), varargs...)
}

// GetAccountByID mocks base method.
func (m *MockAccountClient) GetAccountByID(ctx context.Context, in *grpc.GetAccountByIDRequestV1, opts ...grpc0.CallOption) (*grpc.AccountData, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetAccountByID", varargs...)
	ret0, _ := ret[0].(*grpc.AccountData)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAccountByID indicates an expected call of GetAccountByID.
func (mr *MockAccountClientMockRecorder) GetAccountByID(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAccountByID", reflect.TypeOf((*MockAccountClient)(nil).GetAccountByID), varargs...)
}

// GetAccountByNumber mocks base method.
func (m *MockAccountClient) GetAccountByNumber(ctx context.Context, in *grpc.GetAccountByNumberRequest, opts ...grpc0.CallOption) (*grpc.GetAccountByNumberResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetAccountByNumber", varargs...)
	ret0, _ := ret[0].(*grpc.GetAccountByNumberResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAccountByNumber indicates an expected call of GetAccountByNumber.
func (mr *MockAccountClientMockRecorder) GetAccountByNumber(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAccountByNumber", reflect.TypeOf((*MockAccountClient)(nil).GetAccountByNumber), varargs...)
}

// GetSuccessTransferStatus mocks base method.
func (m *MockAccountClient) GetSuccessTransferStatus(ctx context.Context, in *emptypb.Empty, opts ...grpc0.CallOption) (*grpc.GetStatusResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetSuccessTransferStatus", varargs...)
	ret0, _ := ret[0].(*grpc.GetStatusResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetSuccessTransferStatus indicates an expected call of GetSuccessTransferStatus.
func (mr *MockAccountClientMockRecorder) GetSuccessTransferStatus(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetSuccessTransferStatus", reflect.TypeOf((*MockAccountClient)(nil).GetSuccessTransferStatus), varargs...)
}

// GetTransferByForeignID mocks base method.
func (m *MockAccountClient) GetTransferByForeignID(ctx context.Context, in *grpc.GetTransferByForeignIDRequest, opts ...grpc0.CallOption) (*grpc.GetTransferResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetTransferByForeignID", varargs...)
	ret0, _ := ret[0].(*grpc.GetTransferResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetTransferByForeignID indicates an expected call of GetTransferByForeignID.
func (mr *MockAccountClientMockRecorder) GetTransferByForeignID(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetTransferByForeignID", reflect.TypeOf((*MockAccountClient)(nil).GetTransferByForeignID), varargs...)
}

// GetTransferByIDV1 mocks base method.
func (m *MockAccountClient) GetTransferByIDV1(ctx context.Context, in *grpc.GetTransferByIDRequestV1, opts ...grpc0.CallOption) (*grpc.GetTransferByIDResponseV1, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetTransferByIDV1", varargs...)
	ret0, _ := ret[0].(*grpc.GetTransferByIDResponseV1)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetTransferByIDV1 indicates an expected call of GetTransferByIDV1.
func (mr *MockAccountClientMockRecorder) GetTransferByIDV1(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetTransferByIDV1", reflect.TypeOf((*MockAccountClient)(nil).GetTransferByIDV1), varargs...)
}

// StartFinalizingOldTransfersWorker mocks base method.
func (m *MockAccountClient) StartFinalizingOldTransfersWorker(ctx context.Context, in *emptypb.Empty, opts ...grpc0.CallOption) (*emptypb.Empty, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "StartFinalizingOldTransfersWorker", varargs...)
	ret0, _ := ret[0].(*emptypb.Empty)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// StartFinalizingOldTransfersWorker indicates an expected call of StartFinalizingOldTransfersWorker.
func (mr *MockAccountClientMockRecorder) StartFinalizingOldTransfersWorker(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "StartFinalizingOldTransfersWorker", reflect.TypeOf((*MockAccountClient)(nil).StartFinalizingOldTransfersWorker), varargs...)
}

// StartFinalizingOutTransfersWorker mocks base method.
func (m *MockAccountClient) StartFinalizingOutTransfersWorker(ctx context.Context, in *emptypb.Empty, opts ...grpc0.CallOption) (*emptypb.Empty, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "StartFinalizingOutTransfersWorker", varargs...)
	ret0, _ := ret[0].(*emptypb.Empty)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// StartFinalizingOutTransfersWorker indicates an expected call of StartFinalizingOutTransfersWorker.
func (mr *MockAccountClientMockRecorder) StartFinalizingOutTransfersWorker(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "StartFinalizingOutTransfersWorker", reflect.TypeOf((*MockAccountClient)(nil).StartFinalizingOutTransfersWorker), varargs...)
}

// StartParsingIncomingTransfersWorker mocks base method.
func (m *MockAccountClient) StartParsingIncomingTransfersWorker(ctx context.Context, in *emptypb.Empty, opts ...grpc0.CallOption) (*emptypb.Empty, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "StartParsingIncomingTransfersWorker", varargs...)
	ret0, _ := ret[0].(*emptypb.Empty)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// StartParsingIncomingTransfersWorker indicates an expected call of StartParsingIncomingTransfersWorker.
func (mr *MockAccountClientMockRecorder) StartParsingIncomingTransfersWorker(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "StartParsingIncomingTransfersWorker", reflect.TypeOf((*MockAccountClient)(nil).StartParsingIncomingTransfersWorker), varargs...)
}

// StartProcessingAccountStatementWorker mocks base method.
func (m *MockAccountClient) StartProcessingAccountStatementWorker(ctx context.Context, in *emptypb.Empty, opts ...grpc0.CallOption) (*emptypb.Empty, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "StartProcessingAccountStatementWorker", varargs...)
	ret0, _ := ret[0].(*emptypb.Empty)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// StartProcessingAccountStatementWorker indicates an expected call of StartProcessingAccountStatementWorker.
func (mr *MockAccountClientMockRecorder) StartProcessingAccountStatementWorker(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "StartProcessingAccountStatementWorker", reflect.TypeOf((*MockAccountClient)(nil).StartProcessingAccountStatementWorker), varargs...)
}

// MockAccountServer is a mock of AccountServer interface.
type MockAccountServer struct {
	ctrl     *gomock.Controller
	recorder *MockAccountServerMockRecorder
}

// MockAccountServerMockRecorder is the mock recorder for MockAccountServer.
type MockAccountServerMockRecorder struct {
	mock *MockAccountServer
}

// NewMockAccountServer creates a new mock instance.
func NewMockAccountServer(ctrl *gomock.Controller) *MockAccountServer {
	mock := &MockAccountServer{ctrl: ctrl}
	mock.recorder = &MockAccountServerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockAccountServer) EXPECT() *MockAccountServerMockRecorder {
	return m.recorder
}

// CreateTransfer mocks base method.
func (m *MockAccountServer) CreateTransfer(arg0 context.Context, arg1 *grpc.CreateTransferRequest) (*grpc.CreateTransferResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateTransfer", arg0, arg1)
	ret0, _ := ret[0].(*grpc.CreateTransferResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateTransfer indicates an expected call of CreateTransfer.
func (mr *MockAccountServerMockRecorder) CreateTransfer(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateTransfer", reflect.TypeOf((*MockAccountServer)(nil).CreateTransfer), arg0, arg1)
}

// ExtractFailOrCanceledTransfersByForeignIDs mocks base method.
func (m *MockAccountServer) ExtractFailOrCanceledTransfersByForeignIDs(arg0 context.Context, arg1 *grpc.ExtractFailOrCanceledTransfersByForeignIDsRequest) (*grpc.ExtractTransfersByForeignIDsResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ExtractFailOrCanceledTransfersByForeignIDs", arg0, arg1)
	ret0, _ := ret[0].(*grpc.ExtractTransfersByForeignIDsResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ExtractFailOrCanceledTransfersByForeignIDs indicates an expected call of ExtractFailOrCanceledTransfersByForeignIDs.
func (mr *MockAccountServerMockRecorder) ExtractFailOrCanceledTransfersByForeignIDs(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ExtractFailOrCanceledTransfersByForeignIDs", reflect.TypeOf((*MockAccountServer)(nil).ExtractFailOrCanceledTransfersByForeignIDs), arg0, arg1)
}

// ExtractSuccessTransfersByForeignIDs mocks base method.
func (m *MockAccountServer) ExtractSuccessTransfersByForeignIDs(arg0 context.Context, arg1 *grpc.ExtractSuccessTransfersByForeignIDsRequest) (*grpc.ExtractTransfersByForeignIDsResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ExtractSuccessTransfersByForeignIDs", arg0, arg1)
	ret0, _ := ret[0].(*grpc.ExtractTransfersByForeignIDsResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ExtractSuccessTransfersByForeignIDs indicates an expected call of ExtractSuccessTransfersByForeignIDs.
func (mr *MockAccountServerMockRecorder) ExtractSuccessTransfersByForeignIDs(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ExtractSuccessTransfersByForeignIDs", reflect.TypeOf((*MockAccountServer)(nil).ExtractSuccessTransfersByForeignIDs), arg0, arg1)
}

// GetAccountByID mocks base method.
func (m *MockAccountServer) GetAccountByID(arg0 context.Context, arg1 *grpc.GetAccountByIDRequestV1) (*grpc.AccountData, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAccountByID", arg0, arg1)
	ret0, _ := ret[0].(*grpc.AccountData)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAccountByID indicates an expected call of GetAccountByID.
func (mr *MockAccountServerMockRecorder) GetAccountByID(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAccountByID", reflect.TypeOf((*MockAccountServer)(nil).GetAccountByID), arg0, arg1)
}

// GetAccountByNumber mocks base method.
func (m *MockAccountServer) GetAccountByNumber(arg0 context.Context, arg1 *grpc.GetAccountByNumberRequest) (*grpc.GetAccountByNumberResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAccountByNumber", arg0, arg1)
	ret0, _ := ret[0].(*grpc.GetAccountByNumberResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAccountByNumber indicates an expected call of GetAccountByNumber.
func (mr *MockAccountServerMockRecorder) GetAccountByNumber(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAccountByNumber", reflect.TypeOf((*MockAccountServer)(nil).GetAccountByNumber), arg0, arg1)
}

// GetSuccessTransferStatus mocks base method.
func (m *MockAccountServer) GetSuccessTransferStatus(arg0 context.Context, arg1 *emptypb.Empty) (*grpc.GetStatusResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetSuccessTransferStatus", arg0, arg1)
	ret0, _ := ret[0].(*grpc.GetStatusResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetSuccessTransferStatus indicates an expected call of GetSuccessTransferStatus.
func (mr *MockAccountServerMockRecorder) GetSuccessTransferStatus(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetSuccessTransferStatus", reflect.TypeOf((*MockAccountServer)(nil).GetSuccessTransferStatus), arg0, arg1)
}

// GetTransferByForeignID mocks base method.
func (m *MockAccountServer) GetTransferByForeignID(arg0 context.Context, arg1 *grpc.GetTransferByForeignIDRequest) (*grpc.GetTransferResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetTransferByForeignID", arg0, arg1)
	ret0, _ := ret[0].(*grpc.GetTransferResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetTransferByForeignID indicates an expected call of GetTransferByForeignID.
func (mr *MockAccountServerMockRecorder) GetTransferByForeignID(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetTransferByForeignID", reflect.TypeOf((*MockAccountServer)(nil).GetTransferByForeignID), arg0, arg1)
}

// GetTransferByIDV1 mocks base method.
func (m *MockAccountServer) GetTransferByIDV1(arg0 context.Context, arg1 *grpc.GetTransferByIDRequestV1) (*grpc.GetTransferByIDResponseV1, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetTransferByIDV1", arg0, arg1)
	ret0, _ := ret[0].(*grpc.GetTransferByIDResponseV1)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetTransferByIDV1 indicates an expected call of GetTransferByIDV1.
func (mr *MockAccountServerMockRecorder) GetTransferByIDV1(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetTransferByIDV1", reflect.TypeOf((*MockAccountServer)(nil).GetTransferByIDV1), arg0, arg1)
}

// StartFinalizingOldTransfersWorker mocks base method.
func (m *MockAccountServer) StartFinalizingOldTransfersWorker(arg0 context.Context, arg1 *emptypb.Empty) (*emptypb.Empty, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "StartFinalizingOldTransfersWorker", arg0, arg1)
	ret0, _ := ret[0].(*emptypb.Empty)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// StartFinalizingOldTransfersWorker indicates an expected call of StartFinalizingOldTransfersWorker.
func (mr *MockAccountServerMockRecorder) StartFinalizingOldTransfersWorker(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "StartFinalizingOldTransfersWorker", reflect.TypeOf((*MockAccountServer)(nil).StartFinalizingOldTransfersWorker), arg0, arg1)
}

// StartFinalizingOutTransfersWorker mocks base method.
func (m *MockAccountServer) StartFinalizingOutTransfersWorker(arg0 context.Context, arg1 *emptypb.Empty) (*emptypb.Empty, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "StartFinalizingOutTransfersWorker", arg0, arg1)
	ret0, _ := ret[0].(*emptypb.Empty)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// StartFinalizingOutTransfersWorker indicates an expected call of StartFinalizingOutTransfersWorker.
func (mr *MockAccountServerMockRecorder) StartFinalizingOutTransfersWorker(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "StartFinalizingOutTransfersWorker", reflect.TypeOf((*MockAccountServer)(nil).StartFinalizingOutTransfersWorker), arg0, arg1)
}

// StartParsingIncomingTransfersWorker mocks base method.
func (m *MockAccountServer) StartParsingIncomingTransfersWorker(arg0 context.Context, arg1 *emptypb.Empty) (*emptypb.Empty, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "StartParsingIncomingTransfersWorker", arg0, arg1)
	ret0, _ := ret[0].(*emptypb.Empty)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// StartParsingIncomingTransfersWorker indicates an expected call of StartParsingIncomingTransfersWorker.
func (mr *MockAccountServerMockRecorder) StartParsingIncomingTransfersWorker(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "StartParsingIncomingTransfersWorker", reflect.TypeOf((*MockAccountServer)(nil).StartParsingIncomingTransfersWorker), arg0, arg1)
}

// StartProcessingAccountStatementWorker mocks base method.
func (m *MockAccountServer) StartProcessingAccountStatementWorker(arg0 context.Context, arg1 *emptypb.Empty) (*emptypb.Empty, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "StartProcessingAccountStatementWorker", arg0, arg1)
	ret0, _ := ret[0].(*emptypb.Empty)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// StartProcessingAccountStatementWorker indicates an expected call of StartProcessingAccountStatementWorker.
func (mr *MockAccountServerMockRecorder) StartProcessingAccountStatementWorker(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "StartProcessingAccountStatementWorker", reflect.TypeOf((*MockAccountServer)(nil).StartProcessingAccountStatementWorker), arg0, arg1)
}

// mustEmbedUnimplementedAccountServer mocks base method.
func (m *MockAccountServer) mustEmbedUnimplementedAccountServer() {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "mustEmbedUnimplementedAccountServer")
}

// mustEmbedUnimplementedAccountServer indicates an expected call of mustEmbedUnimplementedAccountServer.
func (mr *MockAccountServerMockRecorder) mustEmbedUnimplementedAccountServer() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "mustEmbedUnimplementedAccountServer", reflect.TypeOf((*MockAccountServer)(nil).mustEmbedUnimplementedAccountServer))
}

// MockUnsafeAccountServer is a mock of UnsafeAccountServer interface.
type MockUnsafeAccountServer struct {
	ctrl     *gomock.Controller
	recorder *MockUnsafeAccountServerMockRecorder
}

// MockUnsafeAccountServerMockRecorder is the mock recorder for MockUnsafeAccountServer.
type MockUnsafeAccountServerMockRecorder struct {
	mock *MockUnsafeAccountServer
}

// NewMockUnsafeAccountServer creates a new mock instance.
func NewMockUnsafeAccountServer(ctrl *gomock.Controller) *MockUnsafeAccountServer {
	mock := &MockUnsafeAccountServer{ctrl: ctrl}
	mock.recorder = &MockUnsafeAccountServerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockUnsafeAccountServer) EXPECT() *MockUnsafeAccountServerMockRecorder {
	return m.recorder
}

// mustEmbedUnimplementedAccountServer mocks base method.
func (m *MockUnsafeAccountServer) mustEmbedUnimplementedAccountServer() {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "mustEmbedUnimplementedAccountServer")
}

// mustEmbedUnimplementedAccountServer indicates an expected call of mustEmbedUnimplementedAccountServer.
func (mr *MockUnsafeAccountServerMockRecorder) mustEmbedUnimplementedAccountServer() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "mustEmbedUnimplementedAccountServer", reflect.TypeOf((*MockUnsafeAccountServer)(nil).mustEmbedUnimplementedAccountServer))
}
