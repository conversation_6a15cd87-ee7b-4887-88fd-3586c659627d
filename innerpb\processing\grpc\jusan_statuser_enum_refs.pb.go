// Code generated by protoc-gen-go-mvp. DO NOT EDIT.
// versions:
// - protoc-gen-go-mvp v2.0.1
// created by mvp authors

package grpc

func (x JusanResponseCodeStatuser) Code() string {
	switch x {
	case JusanResponseCodeStatuser_StatuserServiceUnavailable:
		return "11"
	case JusanResponseCodeStatuser_StatuserIncorrectFieldOrder:
		return "12"
	case JusanResponseCodeStatuser_StatuserIncorrectAmount:
		return "13"
	case JusanResponseCodeStatuser_StatuserIncorrectCurrency:
		return "14"
	case JusanResponseCodeStatuser_StatuserNoSuchCard:
		return "15"
	case JusanResponseCodeStatuser_StatuserDbUnavailable:
		return "16"
	case JusanResponseCodeStatuser_StatuserForbiddenForMerchant:
		return "171"
	case JusanResponseCodeStatuser_StatuserForbiddenMerchantByLaw:
		return "172 "
	case JusanResponseCodeStatuser_StatuserRequestAlreadeCompleted:
		return "18"
	case JusanResponseCodeStatuser_StatuserIncorrectCardExpDate:
		return "19"
	case JusanResponseCodeStatuser_StatuserIncorrectFieldTerminal:
		return "20"
	case JusanResponseCodeStatuser_StatuserIncorrectSign:
		return "21"
	case JusanResponseCodeStatuser_StatuserCurrencyNotFound:
		return "22"
	case JusanResponseCodeStatuser_StatuserLimitExceeded:
		return "23"
	case JusanResponseCodeStatuser_StatuserEmptyField:
		return "24"
	case JusanResponseCodeStatuser_StatuserSizeLessSymbol:
		return "25"
	case JusanResponseCodeStatuser_StatuserSizeMoreSymbol:
		return "26"
	case JusanResponseCodeStatuser_StatuserInvalidValue:
		return "27"
	case JusanResponseCodeStatuser_StatuserMPIError3DS:
		return "28"
	case JusanResponseCodeStatuser_StatuserInvalidCardType:
		return "29"
	case JusanResponseCodeStatuser_StatuserPaymentNotFound:
		return "30"
	case JusanResponseCodeStatuser_StatuserClientKeyNotFound:
		return "31"
	case JusanResponseCodeStatuser_StatuserForbiddenTerminal:
		return "32"
	case JusanResponseCodeStatuser_StatuserTokenNotFound:
		return "33"
	case JusanResponseCodeStatuser_StatuserIncorrectBlockAmount:
		return "34"
	case JusanResponseCodeStatuser_StatuserUnknownError:
		return "99"
	case JusanResponseCodeStatuser_StatuserUnavailableService:
		return "41"
	case JusanResponseCodeStatuser_StatuserSumIncorrect:
		return "42"
	case JusanResponseCodeStatuser_StatuserUnavailableDb:
		return "43"
	case JusanResponseCodeStatuser_StatuserIncorrectMerchant:
		return "44"
	case JusanResponseCodeStatuser_StatuserNotFoundMerchant:
		return "17"
	case JusanResponseCodeStatuser_StatuserOrderNotFound:
		return "45"
	case JusanResponseCodeStatuser_StatuserInvalidSign:
		return "46"
	case JusanResponseCodeStatuser_StatuserIncorrectRefundAmount:
		return "47 "
	case JusanResponseCodeStatuser_StatuserIncorrectStatus:
		return "48"
	case JusanResponseCodeStatuser_StatuserIncorrectValue:
		return "50"
	case JusanResponseCodeStatuser_StatuserTerminalStatusIncorrect:
		return "51"
	case JusanResponseCodeStatuser_StatuserTerminalForbidden:
		return "52"
	case JusanResponseCodeStatuser_StatuserDuplicateDescription:
		return "53"
	case JusanResponseCodeStatuser_StatuserRefundHandleError:
		return "F"
	case JusanResponseCodeStatuser_StatuserPaymentError:
		return "E"
	case JusanResponseCodeStatuser_StatuserPaymentExpired:
		return "c"
	default:
		return "0"
	}
}

func (x JusanResponseCodeStatuser) IntegrationError() IntegrationError {
	switch x {
	case JusanResponseCodeStatuser_StatuserServiceUnavailable:
		return IntegrationError_UnavailableAcquirer
	case JusanResponseCodeStatuser_StatuserIncorrectFieldOrder:
		return IntegrationError_TransactionDeclinedByAcquirer
	case JusanResponseCodeStatuser_StatuserIncorrectAmount:
		return IntegrationError_TransactionDeclinedByAcquirer
	case JusanResponseCodeStatuser_StatuserIncorrectCurrency:
		return IntegrationError_TransactionDeclinedByAcquirer
	case JusanResponseCodeStatuser_StatuserNoSuchCard:
		return IntegrationError_InvalidCard
	case JusanResponseCodeStatuser_StatuserDbUnavailable:
		return IntegrationError_UnavailableAcquirer
	case JusanResponseCodeStatuser_StatuserForbiddenForMerchant:
		return IntegrationError_PaymentForbiddenForMerchant
	case JusanResponseCodeStatuser_StatuserForbiddenMerchantByLaw:
		return IntegrationError_PaymentForbiddenForMerchant
	case JusanResponseCodeStatuser_StatuserRequestAlreadeCompleted:
		return IntegrationError_TransactionDeclinedByAcquirer
	case JusanResponseCodeStatuser_StatuserIncorrectCardExpDate:
		return IntegrationError_IncorrectCardExpDate
	case JusanResponseCodeStatuser_StatuserIncorrectFieldTerminal:
		return IntegrationError_TransactionDeclinedByAcquirer
	case JusanResponseCodeStatuser_StatuserIncorrectSign:
		return IntegrationError_TransactionDeclinedByAcquirer
	case JusanResponseCodeStatuser_StatuserCurrencyNotFound:
		return IntegrationError_TransactionDeclinedByAcquirer
	case JusanResponseCodeStatuser_StatuserLimitExceeded:
		return IntegrationError_ExceedsAmountLimit
	case JusanResponseCodeStatuser_StatuserEmptyField:
		return IntegrationError_TransactionDeclinedByAcquirer
	case JusanResponseCodeStatuser_StatuserSizeLessSymbol:
		return IntegrationError_TransactionDeclinedByAcquirer
	case JusanResponseCodeStatuser_StatuserSizeMoreSymbol:
		return IntegrationError_TransactionDeclinedByAcquirer
	case JusanResponseCodeStatuser_StatuserInvalidValue:
		return IntegrationError_TransactionDeclinedByAcquirer
	case JusanResponseCodeStatuser_StatuserMPIError3DS:
		return IntegrationError_ThreeDSAuthFailed
	case JusanResponseCodeStatuser_StatuserInvalidCardType:
		return IntegrationError_InvalidCard
	case JusanResponseCodeStatuser_StatuserPaymentNotFound:
		return IntegrationError_TransactionDeclinedByAcquirer
	case JusanResponseCodeStatuser_StatuserClientKeyNotFound:
		return IntegrationError_TransactionDeclinedByAcquirer
	case JusanResponseCodeStatuser_StatuserForbiddenTerminal:
		return IntegrationError_PaymentForbiddenForMerchant
	case JusanResponseCodeStatuser_StatuserTokenNotFound:
		return IntegrationError_InvalidCard
	case JusanResponseCodeStatuser_StatuserIncorrectBlockAmount:
		return IntegrationError_TransactionDeclinedByAcquirer
	case JusanResponseCodeStatuser_StatuserUnknownError:
		return IntegrationError_TransactionDeclinedByAcquirer
	case JusanResponseCodeStatuser_StatuserUnavailableService:
		return IntegrationError_UnavailableAcquirer
	case JusanResponseCodeStatuser_StatuserSumIncorrect:
		return IntegrationError_TransactionDeclinedByAcquirer
	case JusanResponseCodeStatuser_StatuserUnavailableDb:
		return IntegrationError_UnavailableAcquirer
	case JusanResponseCodeStatuser_StatuserIncorrectMerchant:
		return IntegrationError_TransactionDeclinedByAcquirer
	case JusanResponseCodeStatuser_StatuserNotFoundMerchant:
		return IntegrationError_PaymentForbiddenForMerchant
	case JusanResponseCodeStatuser_StatuserOrderNotFound:
		return IntegrationError_TransactionDeclinedByAcquirer
	case JusanResponseCodeStatuser_StatuserInvalidSign:
		return IntegrationError_TransactionDeclinedByAcquirer
	case JusanResponseCodeStatuser_StatuserIncorrectRefundAmount:
		return IntegrationError_TransactionDeclinedByAcquirer
	case JusanResponseCodeStatuser_StatuserIncorrectStatus:
		return IntegrationError_TransactionDeclinedByAcquirer
	case JusanResponseCodeStatuser_StatuserIncorrectValue:
		return IntegrationError_TransactionDeclinedByAcquirer
	case JusanResponseCodeStatuser_StatuserTerminalStatusIncorrect:
		return IntegrationError_PaymentForbiddenForMerchant
	case JusanResponseCodeStatuser_StatuserTerminalForbidden:
		return IntegrationError_PaymentForbiddenForMerchant
	case JusanResponseCodeStatuser_StatuserDuplicateDescription:
		return IntegrationError_TransactionDeclinedByAcquirer
	case JusanResponseCodeStatuser_StatuserRefundHandleError:
		return IntegrationError_TransactionDeclinedByAcquirer
	case JusanResponseCodeStatuser_StatuserPaymentError:
		return IntegrationError_TransactionDeclinedByAcquirer
	case JusanResponseCodeStatuser_StatuserPaymentExpired:
		return IntegrationError_TransactionDeclinedByAcquirer
	default:
		return IntegrationError_None
	}
}

func (x JusanResponseCodeStatuser) Message() string {
	switch x {
	case JusanResponseCodeStatuser_StatuserServiceUnavailable:
		return "Сервис временно недоступен, попробуйте позже"
	case JusanResponseCodeStatuser_StatuserIncorrectFieldOrder:
		return "Неправильное значение в поле ORDER:"
	case JusanResponseCodeStatuser_StatuserIncorrectAmount:
		return "Неправильная сумма: "
	case JusanResponseCodeStatuser_StatuserIncorrectCurrency:
		return "Неправильная валюта:"
	case JusanResponseCodeStatuser_StatuserNoSuchCard:
		return "No such card"
	case JusanResponseCodeStatuser_StatuserDbUnavailable:
		return "Сервис Db временно недоступен, попробуйте позже"
	case JusanResponseCodeStatuser_StatuserForbiddenForMerchant:
		return "Коммерсанту запрещено выполнение операций"
	case JusanResponseCodeStatuser_StatuserForbiddenMerchantByLaw:
		return "Коммерсанту запрещено проведение операций в соответствии с Законом о ПОД/ФТ"
	case JusanResponseCodeStatuser_StatuserRequestAlreadeCompleted:
		return "Запрос уже выполнялся"
	case JusanResponseCodeStatuser_StatuserIncorrectCardExpDate:
		return "Неправильная дата дейстия карты (MM/ГГ)"
	case JusanResponseCodeStatuser_StatuserIncorrectFieldTerminal:
		return "Неправильное значение в поле TERMINAL:"
	case JusanResponseCodeStatuser_StatuserIncorrectSign:
		return "Неправильная подпись!"
	case JusanResponseCodeStatuser_StatuserCurrencyNotFound:
		return "Не найден курс валюты"
	case JusanResponseCodeStatuser_StatuserLimitExceeded:
		return "Превышен лимит!"
	case JusanResponseCodeStatuser_StatuserEmptyField:
		return "Не указано значение в поле"
	case JusanResponseCodeStatuser_StatuserSizeLessSymbol:
		return "Размер значения в поле менее симоволов"
	case JusanResponseCodeStatuser_StatuserSizeMoreSymbol:
		return "Размер значения в поле больше симоволов"
	case JusanResponseCodeStatuser_StatuserInvalidValue:
		return "Введите валидное значение в поле"
	case JusanResponseCodeStatuser_StatuserMPIError3DS:
		return "Ошибка MPI при выполнении проверки 3DS:"
	case JusanResponseCodeStatuser_StatuserInvalidCardType:
		return "Недопустимый тип карты"
	case JusanResponseCodeStatuser_StatuserPaymentNotFound:
		return "Счет на оплату не найден"
	case JusanResponseCodeStatuser_StatuserClientKeyNotFound:
		return "Не передан ключ указанного клиента"
	case JusanResponseCodeStatuser_StatuserForbiddenTerminal:
		return "Для терминала запрещена токенизация"
	case JusanResponseCodeStatuser_StatuserTokenNotFound:
		return "Для данного клиента в вашей организации не зарегистрирован токен"
	case JusanResponseCodeStatuser_StatuserIncorrectBlockAmount:
		return "Неверная сумма блокирования, заявка отменена!"
	case JusanResponseCodeStatuser_StatuserUnknownError:
		return "Неизвестная ошибка: "
	case JusanResponseCodeStatuser_StatuserUnavailableService:
		return "Сервис временно недоступен, попробуйте позже"
	case JusanResponseCodeStatuser_StatuserSumIncorrect:
		return "Неправильная сумма"
	case JusanResponseCodeStatuser_StatuserUnavailableDb:
		return "Сервис Db временно недоступен, попробуйте позже "
	case JusanResponseCodeStatuser_StatuserIncorrectMerchant:
		return "Неправильное значение в поле MERCHANT"
	case JusanResponseCodeStatuser_StatuserNotFoundMerchant:
		return "Коммерсант не найден"
	case JusanResponseCodeStatuser_StatuserOrderNotFound:
		return "Заявка ORDER не найдена"
	case JusanResponseCodeStatuser_StatuserInvalidSign:
		return "Неправильная подпись!"
	case JusanResponseCodeStatuser_StatuserIncorrectRefundAmount:
		return "Сумма возврта '%s' больше чем сумма заказа"
	case JusanResponseCodeStatuser_StatuserIncorrectStatus:
		return "Текущий статус заказа не позволяет делать возврат/отмену"
	case JusanResponseCodeStatuser_StatuserIncorrectValue:
		return "Неправильное значение"
	case JusanResponseCodeStatuser_StatuserTerminalStatusIncorrect:
		return "Текущий статус терминала не позволяет производить операции"
	case JusanResponseCodeStatuser_StatuserTerminalForbidden:
		return "Операция отмены/возврата через API для терминала запрещена"
	case JusanResponseCodeStatuser_StatuserDuplicateDescription:
		return "Дублирование описания отмены"
	case JusanResponseCodeStatuser_StatuserRefundHandleError:
		return "Ошибка при обработке возврата"
	case JusanResponseCodeStatuser_StatuserPaymentError:
		return "Ошибка при оплате"
	case JusanResponseCodeStatuser_StatuserPaymentExpired:
		return "Счет на оплату устарел"
	default:
		return "default"
	}
}

func (x JusanResponseCodeStatuser) TransactionStatus() EnumTransactionStatus {
	switch x {
	case JusanResponseCodeStatuser_StatuserServiceUnavailable:
		return EnumTransactionStatus_TransactionStatusFailed
	case JusanResponseCodeStatuser_StatuserIncorrectFieldOrder:
		return EnumTransactionStatus_TransactionStatusFailed
	case JusanResponseCodeStatuser_StatuserIncorrectAmount:
		return EnumTransactionStatus_TransactionStatusFailed
	case JusanResponseCodeStatuser_StatuserIncorrectCurrency:
		return EnumTransactionStatus_TransactionStatusFailed
	case JusanResponseCodeStatuser_StatuserNoSuchCard:
		return EnumTransactionStatus_TransactionStatusFailed
	case JusanResponseCodeStatuser_StatuserDbUnavailable:
		return EnumTransactionStatus_TransactionStatusFailed
	case JusanResponseCodeStatuser_StatuserForbiddenForMerchant:
		return EnumTransactionStatus_TransactionStatusFailed
	case JusanResponseCodeStatuser_StatuserForbiddenMerchantByLaw:
		return EnumTransactionStatus_TransactionStatusFailed
	case JusanResponseCodeStatuser_StatuserRequestAlreadeCompleted:
		return EnumTransactionStatus_TransactionStatusFailed
	case JusanResponseCodeStatuser_StatuserIncorrectCardExpDate:
		return EnumTransactionStatus_TransactionStatusFailed
	case JusanResponseCodeStatuser_StatuserIncorrectFieldTerminal:
		return EnumTransactionStatus_TransactionStatusFailed
	case JusanResponseCodeStatuser_StatuserIncorrectSign:
		return EnumTransactionStatus_TransactionStatusFailed
	case JusanResponseCodeStatuser_StatuserCurrencyNotFound:
		return EnumTransactionStatus_TransactionStatusFailed
	case JusanResponseCodeStatuser_StatuserLimitExceeded:
		return EnumTransactionStatus_TransactionStatusFailed
	case JusanResponseCodeStatuser_StatuserEmptyField:
		return EnumTransactionStatus_TransactionStatusFailed
	case JusanResponseCodeStatuser_StatuserSizeLessSymbol:
		return EnumTransactionStatus_TransactionStatusFailed
	case JusanResponseCodeStatuser_StatuserSizeMoreSymbol:
		return EnumTransactionStatus_TransactionStatusFailed
	case JusanResponseCodeStatuser_StatuserInvalidValue:
		return EnumTransactionStatus_TransactionStatusFailed
	case JusanResponseCodeStatuser_StatuserMPIError3DS:
		return EnumTransactionStatus_TransactionStatusFailed
	case JusanResponseCodeStatuser_StatuserInvalidCardType:
		return EnumTransactionStatus_TransactionStatusFailed
	case JusanResponseCodeStatuser_StatuserPaymentNotFound:
		return EnumTransactionStatus_TransactionStatusFailed
	case JusanResponseCodeStatuser_StatuserClientKeyNotFound:
		return EnumTransactionStatus_TransactionStatusFailed
	case JusanResponseCodeStatuser_StatuserForbiddenTerminal:
		return EnumTransactionStatus_TransactionStatusFailed
	case JusanResponseCodeStatuser_StatuserTokenNotFound:
		return EnumTransactionStatus_TransactionStatusFailed
	case JusanResponseCodeStatuser_StatuserIncorrectBlockAmount:
		return EnumTransactionStatus_TransactionStatusFailed
	case JusanResponseCodeStatuser_StatuserUnknownError:
		return EnumTransactionStatus_TransactionStatusHolded
	case JusanResponseCodeStatuser_StatuserUnavailableService:
		return EnumTransactionStatus_TransactionStatusFailed
	case JusanResponseCodeStatuser_StatuserSumIncorrect:
		return EnumTransactionStatus_TransactionStatusFailed
	case JusanResponseCodeStatuser_StatuserUnavailableDb:
		return EnumTransactionStatus_TransactionStatusFailed
	case JusanResponseCodeStatuser_StatuserIncorrectMerchant:
		return EnumTransactionStatus_TransactionStatusFailed
	case JusanResponseCodeStatuser_StatuserNotFoundMerchant:
		return EnumTransactionStatus_TransactionStatusFailed
	case JusanResponseCodeStatuser_StatuserOrderNotFound:
		return EnumTransactionStatus_TransactionStatusFailed
	case JusanResponseCodeStatuser_StatuserInvalidSign:
		return EnumTransactionStatus_TransactionStatusFailed
	case JusanResponseCodeStatuser_StatuserIncorrectRefundAmount:
		return EnumTransactionStatus_TransactionStatusFailed
	case JusanResponseCodeStatuser_StatuserIncorrectStatus:
		return EnumTransactionStatus_TransactionStatusFailed
	case JusanResponseCodeStatuser_StatuserIncorrectValue:
		return EnumTransactionStatus_TransactionStatusFailed
	case JusanResponseCodeStatuser_StatuserTerminalStatusIncorrect:
		return EnumTransactionStatus_TransactionStatusFailed
	case JusanResponseCodeStatuser_StatuserTerminalForbidden:
		return EnumTransactionStatus_TransactionStatusFailed
	case JusanResponseCodeStatuser_StatuserDuplicateDescription:
		return EnumTransactionStatus_TransactionStatusFailed
	case JusanResponseCodeStatuser_StatuserRefundHandleError:
		return EnumTransactionStatus_TransactionStatusFailed
	case JusanResponseCodeStatuser_StatuserPaymentError:
		return EnumTransactionStatus_TransactionStatusFailed
	case JusanResponseCodeStatuser_StatuserPaymentExpired:
		return EnumTransactionStatus_TransactionStatusFailed
	default:
		return EnumTransactionStatus_TransactionStatusError
	}
}

// Created reference to JusanResponseCodeStatuser

//	|	JusanResponseCodeStatuser                                	|	Code  	|	IntegrationError                              	|	Message                                                                                                                                       	|	TransactionStatus                            	|
//	|	JusanResponseCodeStatuser_StatuserServiceUnavailable     	|	"11"  	|	IntegrationError_UnavailableAcquirer          	|	"Сервис временно недоступен, попробуйте позже"                                                                                                	|	EnumTransactionStatus_TransactionStatusFailed	|
//	|	JusanResponseCodeStatuser_StatuserIncorrectFieldOrder    	|	"12"  	|	IntegrationError_TransactionDeclinedByAcquirer	|	"Неправильное значение в поле ORDER:"                                                                                                         	|	EnumTransactionStatus_TransactionStatusFailed	|
//	|	JusanResponseCodeStatuser_StatuserIncorrectAmount        	|	"13"  	|	IntegrationError_TransactionDeclinedByAcquirer	|	"Неправильная сумма: "                                                                                                                        	|	EnumTransactionStatus_TransactionStatusFailed	|
//	|	JusanResponseCodeStatuser_StatuserIncorrectCurrency      	|	"14"  	|	IntegrationError_TransactionDeclinedByAcquirer	|	"Неправильная валюта:"                                                                                                                        	|	EnumTransactionStatus_TransactionStatusFailed	|
//	|	JusanResponseCodeStatuser_StatuserNoSuchCard             	|	"15"  	|	IntegrationError_InvalidCard                  	|	"No such card"                                                                                                                                	|	EnumTransactionStatus_TransactionStatusFailed	|
//	|	JusanResponseCodeStatuser_StatuserDbUnavailable          	|	"16"  	|	IntegrationError_UnavailableAcquirer          	|	"Сервис Db временно недоступен, попробуйте позже"                                                                                             	|	EnumTransactionStatus_TransactionStatusFailed	|
//	|	JusanResponseCodeStatuser_StatuserForbiddenForMerchant   	|	"171" 	|	IntegrationError_PaymentForbiddenForMerchant  	|	"Коммерсанту запрещено выполнение операций"                                                                                                   	|	EnumTransactionStatus_TransactionStatusFailed	|
//	|	JusanResponseCodeStatuser_StatuserForbiddenMerchantByLaw 	|	"172 "	|	IntegrationError_PaymentForbiddenForMerchant  	|	"Коммерсанту запрещено проведение операций в соответствии с Законом о ПОД/ФТ"                                                                 	|	EnumTransactionStatus_TransactionStatusFailed	|
//	|	JusanResponseCodeStatuser_StatuserRequestAlreadeCompleted	|	"18"  	|	IntegrationError_TransactionDeclinedByAcquirer	|	"Запрос уже выполнялся"                                                                                                                       	|	EnumTransactionStatus_TransactionStatusFailed	|
//	|	JusanResponseCodeStatuser_StatuserIncorrectCardExpDate   	|	"19"  	|	IntegrationError_IncorrectCardExpDate         	|	"Неправильная дата дейстия карты (MM/ГГ)"                                                                                                     	|	EnumTransactionStatus_TransactionStatusFailed	|
//	|	JusanResponseCodeStatuser_StatuserIncorrectFieldTerminal 	|	"20"  	|	IntegrationError_TransactionDeclinedByAcquirer	|	"Неправильное значение в поле TERMINAL:"                                                                                                      	|	EnumTransactionStatus_TransactionStatusFailed	|
//	|	JusanResponseCodeStatuser_StatuserIncorrectSign          	|	"21"  	|	IntegrationError_TransactionDeclinedByAcquirer	|	"Неправильная подпись!"                                                                                                                       	|	EnumTransactionStatus_TransactionStatusFailed	|
//	|	JusanResponseCodeStatuser_StatuserCurrencyNotFound       	|	"22"  	|	IntegrationError_TransactionDeclinedByAcquirer	|	"Не найден курс валюты"                                                                                                                       	|	EnumTransactionStatus_TransactionStatusFailed	|
//	|	JusanResponseCodeStatuser_StatuserLimitExceeded          	|	"23"  	|	IntegrationError_ExceedsAmountLimit           	|	"Превышен лимит!"                                                                                                                             	|	EnumTransactionStatus_TransactionStatusFailed	|
//	|	JusanResponseCodeStatuser_StatuserEmptyField             	|	"24"  	|	IntegrationError_TransactionDeclinedByAcquirer	|	"Не указано значение в поле"                                                                                                                  	|	EnumTransactionStatus_TransactionStatusFailed	|
//	|	JusanResponseCodeStatuser_StatuserSizeLessSymbol         	|	"25"  	|	IntegrationError_TransactionDeclinedByAcquirer	|	"Размер значения в поле менее симоволов"                                                                                                      	|	EnumTransactionStatus_TransactionStatusFailed	|
//	|	JusanResponseCodeStatuser_StatuserSizeMoreSymbol         	|	"26"  	|	IntegrationError_TransactionDeclinedByAcquirer	|	"Размер значения в поле больше симоволов"                                                                                                     	|	EnumTransactionStatus_TransactionStatusFailed	|
//	|	JusanResponseCodeStatuser_StatuserInvalidValue           	|	"27"  	|	IntegrationError_TransactionDeclinedByAcquirer	|	"Введите валидное значение в поле"                                                                                                            	|	EnumTransactionStatus_TransactionStatusFailed	|
//	|	JusanResponseCodeStatuser_StatuserMPIError3DS            	|	"28"  	|	IntegrationError_ThreeDSAuthFailed            	|	"Ошибка MPI при выполнении проверки 3DS:"                                                                                                     	|	EnumTransactionStatus_TransactionStatusFailed	|
//	|	JusanResponseCodeStatuser_StatuserInvalidCardType        	|	"29"  	|	IntegrationError_InvalidCard                  	|	"Недопустимый тип карты"                                                                                                                      	|	EnumTransactionStatus_TransactionStatusFailed	|
//	|	JusanResponseCodeStatuser_StatuserPaymentNotFound        	|	"30"  	|	IntegrationError_TransactionDeclinedByAcquirer	|	"Счет на оплату не найден"                                                                                                                    	|	EnumTransactionStatus_TransactionStatusFailed	|
//	|	JusanResponseCodeStatuser_StatuserClientKeyNotFound      	|	"31"  	|	IntegrationError_TransactionDeclinedByAcquirer	|	"Не передан ключ указанного клиента"                                                                                                          	|	EnumTransactionStatus_TransactionStatusFailed	|
//	|	JusanResponseCodeStatuser_StatuserForbiddenTerminal      	|	"32"  	|	IntegrationError_PaymentForbiddenForMerchant  	|	"Для терминала запрещена токенизация"                                                                                                         	|	EnumTransactionStatus_TransactionStatusFailed	|
//	|	JusanResponseCodeStatuser_StatuserTokenNotFound          	|	"33"  	|	IntegrationError_InvalidCard                  	|	"Для данного клиента в вашей организации не зарегистрирован токен"                                                                            	|	EnumTransactionStatus_TransactionStatusFailed	|
//	|	JusanResponseCodeStatuser_StatuserIncorrectBlockAmount   	|	"34"  	|	IntegrationError_TransactionDeclinedByAcquirer	|	"Неверная сумма блокирования, заявка отменена!"                                                                                               	|	EnumTransactionStatus_TransactionStatusFailed	|
//	|	JusanResponseCodeStatuser_StatuserUnknownError           	|	"99"  	|	IntegrationError_TransactionDeclinedByAcquirer	|	"Неизвестная ошибка: "                                                                                                                        	|	EnumTransactionStatus_TransactionStatusHolded	|
//	|	JusanResponseCodeStatuser_StatuserUnavailableService     	|	"41"  	|	IntegrationError_UnavailableAcquirer          	|	"Сервис временно недоступен, попробуйте позже"                                                                                                	|	EnumTransactionStatus_TransactionStatusFailed	|
//	|	JusanResponseCodeStatuser_StatuserSumIncorrect           	|	"42"  	|	IntegrationError_TransactionDeclinedByAcquirer	|	"Неправильная сумма"                                                                                                                          	|	EnumTransactionStatus_TransactionStatusFailed	|
//	|	JusanResponseCodeStatuser_StatuserUnavailableDb          	|	"43"  	|	IntegrationError_UnavailableAcquirer          	|	"Сервис Db временно недоступен, попробуйте позже "                                                                                            	|	EnumTransactionStatus_TransactionStatusFailed	|
//	|	JusanResponseCodeStatuser_StatuserIncorrectMerchant      	|	"44"  	|	IntegrationError_TransactionDeclinedByAcquirer	|	"Неправильное значение в поле MERCHANT"                                                                                                       	|	EnumTransactionStatus_TransactionStatusFailed	|
//	|	JusanResponseCodeStatuser_StatuserNotFoundMerchant       	|	"17"  	|	IntegrationError_PaymentForbiddenForMerchant  	|	"Коммерсант не найден"                                                                                                                        	|	EnumTransactionStatus_TransactionStatusFailed	|
//	|	JusanResponseCodeStatuser_StatuserOrderNotFound          	|	"45"  	|	IntegrationError_TransactionDeclinedByAcquirer	|	"Заявка ORDER не найдена"                                                                                                                     	|	EnumTransactionStatus_TransactionStatusFailed	|
//	|	JusanResponseCodeStatuser_StatuserInvalidSign            	|	"46"  	|	IntegrationError_TransactionDeclinedByAcquirer	|	"Неправильная подпись!"                                                                                                                       	|	EnumTransactionStatus_TransactionStatusFailed	|
//	|	JusanResponseCodeStatuser_StatuserIncorrectRefundAmount  	|	"47 " 	|	IntegrationError_TransactionDeclinedByAcquirer	|	"Сумма возврта '%s' больше чем сумма заказа"                                                                                                  	|	EnumTransactionStatus_TransactionStatusFailed	|
//	|	JusanResponseCodeStatuser_StatuserIncorrectStatus        	|	"48"  	|	IntegrationError_TransactionDeclinedByAcquirer	|	"Текущий статус заказа не позволяет делать возврат/отмену"                                                                                    	|	EnumTransactionStatus_TransactionStatusFailed	|
//	|	JusanResponseCodeStatuser_StatuserIncorrectValue         	|	"50"  	|	IntegrationError_TransactionDeclinedByAcquirer	|	"Неправильное значение"                                                                                                                       	|	EnumTransactionStatus_TransactionStatusFailed	|
//	|	JusanResponseCodeStatuser_StatuserTerminalStatusIncorrect	|	"51"  	|	IntegrationError_PaymentForbiddenForMerchant  	|	"Текущий статус терминала не позволяет производить операции"                                                                                  	|	EnumTransactionStatus_TransactionStatusFailed	|
//	|	JusanResponseCodeStatuser_StatuserTerminalForbidden      	|	"52"  	|	IntegrationError_PaymentForbiddenForMerchant  	|	"Операция отмены/возврата через API для терминала запрещена"                                                                                  	|	EnumTransactionStatus_TransactionStatusFailed	|
//	|	JusanResponseCodeStatuser_StatuserDuplicateDescription   	|	"53"  	|	IntegrationError_TransactionDeclinedByAcquirer	|	"Дублирование описания отмены"                                                                                                                	|	EnumTransactionStatus_TransactionStatusFailed	|
//	|	JusanResponseCodeStatuser_StatuserRefundHandleError      	|	"F"   	|	IntegrationError_TransactionDeclinedByAcquirer	|	"Ошибка при обработке возврата"                                                                                                               	|	EnumTransactionStatus_TransactionStatusFailed	|
//	|	JusanResponseCodeStatuser_StatuserPaymentError           	|	"E"   	|	IntegrationError_TransactionDeclinedByAcquirer	|	"Ошибка при оплате"                                                                                                                           	|	EnumTransactionStatus_TransactionStatusFailed	|
//	|	JusanResponseCodeStatuser_StatuserPaymentExpired         	|	"c"   	|	IntegrationError_TransactionDeclinedByAcquirer	|	"Счет на оплату устарел"                                                                                                                      	|	EnumTransactionStatus_TransactionStatusFailed	|

var SliceJusanResponseCodeStatuserRefs *sliceJusanResponseCodeStatuserRefs

type sliceJusanResponseCodeStatuserRefs struct{}

func (*sliceJusanResponseCodeStatuserRefs) Code(slice ...JusanResponseCodeStatuser) []string {
	var result []string
	for _, val := range slice {
		result = append(result, val.Code())
	}

	return result
}

func (*sliceJusanResponseCodeStatuserRefs) IntegrationError(slice ...JusanResponseCodeStatuser) []IntegrationError {
	var result []IntegrationError
	for _, val := range slice {
		result = append(result, val.IntegrationError())
	}

	return result
}

func (*sliceJusanResponseCodeStatuserRefs) Message(slice ...JusanResponseCodeStatuser) []string {
	var result []string
	for _, val := range slice {
		result = append(result, val.Message())
	}

	return result
}

func (*sliceJusanResponseCodeStatuserRefs) TransactionStatus(slice ...JusanResponseCodeStatuser) []EnumTransactionStatus {
	var result []EnumTransactionStatus
	for _, val := range slice {
		result = append(result, val.TransactionStatus())
	}

	return result
}
