// Code generated by MockGen. DO NOT EDIT.
// Source: transaction_status_grpc.pb.go

// Package grpcmock is a generated GoMock package.
package grpcmock

import (
	context "context"
	reflect "reflect"

	grpc "git.local/sensitive/innerpb/processing/grpc"
	gomock "github.com/golang/mock/gomock"
	grpc0 "google.golang.org/grpc"
)

// MockTransactionStatusClient is a mock of TransactionStatusClient interface.
type MockTransactionStatusClient struct {
	ctrl     *gomock.Controller
	recorder *MockTransactionStatusClientMockRecorder
}

// MockTransactionStatusClientMockRecorder is the mock recorder for MockTransactionStatusClient.
type MockTransactionStatusClientMockRecorder struct {
	mock *MockTransactionStatusClient
}

// NewMockTransactionStatusClient creates a new mock instance.
func NewMockTransactionStatusClient(ctrl *gomock.Controller) *MockTransactionStatusClient {
	mock := &MockTransactionStatusClient{ctrl: ctrl}
	mock.recorder = &MockTransactionStatusClientMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockTransactionStatusClient) EXPECT() *MockTransactionStatusClientMockRecorder {
	return m.recorder
}

// GetBatchTransactionWithStatuses mocks base method.
func (m *MockTransactionStatusClient) GetBatchTransactionWithStatuses(ctx context.Context, in *grpc.BatchTransactionRequestV1, opts ...grpc0.CallOption) (*grpc.BatchTransactionResponseV1, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetBatchTransactionWithStatuses", varargs...)
	ret0, _ := ret[0].(*grpc.BatchTransactionResponseV1)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetBatchTransactionWithStatuses indicates an expected call of GetBatchTransactionWithStatuses.
func (mr *MockTransactionStatusClientMockRecorder) GetBatchTransactionWithStatuses(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetBatchTransactionWithStatuses", reflect.TypeOf((*MockTransactionStatusClient)(nil).GetBatchTransactionWithStatuses), varargs...)
}

// MockTransactionStatusServer is a mock of TransactionStatusServer interface.
type MockTransactionStatusServer struct {
	ctrl     *gomock.Controller
	recorder *MockTransactionStatusServerMockRecorder
}

// MockTransactionStatusServerMockRecorder is the mock recorder for MockTransactionStatusServer.
type MockTransactionStatusServerMockRecorder struct {
	mock *MockTransactionStatusServer
}

// NewMockTransactionStatusServer creates a new mock instance.
func NewMockTransactionStatusServer(ctrl *gomock.Controller) *MockTransactionStatusServer {
	mock := &MockTransactionStatusServer{ctrl: ctrl}
	mock.recorder = &MockTransactionStatusServerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockTransactionStatusServer) EXPECT() *MockTransactionStatusServerMockRecorder {
	return m.recorder
}

// GetBatchTransactionWithStatuses mocks base method.
func (m *MockTransactionStatusServer) GetBatchTransactionWithStatuses(arg0 context.Context, arg1 *grpc.BatchTransactionRequestV1) (*grpc.BatchTransactionResponseV1, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetBatchTransactionWithStatuses", arg0, arg1)
	ret0, _ := ret[0].(*grpc.BatchTransactionResponseV1)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetBatchTransactionWithStatuses indicates an expected call of GetBatchTransactionWithStatuses.
func (mr *MockTransactionStatusServerMockRecorder) GetBatchTransactionWithStatuses(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetBatchTransactionWithStatuses", reflect.TypeOf((*MockTransactionStatusServer)(nil).GetBatchTransactionWithStatuses), arg0, arg1)
}

// mustEmbedUnimplementedTransactionStatusServer mocks base method.
func (m *MockTransactionStatusServer) mustEmbedUnimplementedTransactionStatusServer() {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "mustEmbedUnimplementedTransactionStatusServer")
}

// mustEmbedUnimplementedTransactionStatusServer indicates an expected call of mustEmbedUnimplementedTransactionStatusServer.
func (mr *MockTransactionStatusServerMockRecorder) mustEmbedUnimplementedTransactionStatusServer() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "mustEmbedUnimplementedTransactionStatusServer", reflect.TypeOf((*MockTransactionStatusServer)(nil).mustEmbedUnimplementedTransactionStatusServer))
}

// MockUnsafeTransactionStatusServer is a mock of UnsafeTransactionStatusServer interface.
type MockUnsafeTransactionStatusServer struct {
	ctrl     *gomock.Controller
	recorder *MockUnsafeTransactionStatusServerMockRecorder
}

// MockUnsafeTransactionStatusServerMockRecorder is the mock recorder for MockUnsafeTransactionStatusServer.
type MockUnsafeTransactionStatusServerMockRecorder struct {
	mock *MockUnsafeTransactionStatusServer
}

// NewMockUnsafeTransactionStatusServer creates a new mock instance.
func NewMockUnsafeTransactionStatusServer(ctrl *gomock.Controller) *MockUnsafeTransactionStatusServer {
	mock := &MockUnsafeTransactionStatusServer{ctrl: ctrl}
	mock.recorder = &MockUnsafeTransactionStatusServerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockUnsafeTransactionStatusServer) EXPECT() *MockUnsafeTransactionStatusServerMockRecorder {
	return m.recorder
}

// mustEmbedUnimplementedTransactionStatusServer mocks base method.
func (m *MockUnsafeTransactionStatusServer) mustEmbedUnimplementedTransactionStatusServer() {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "mustEmbedUnimplementedTransactionStatusServer")
}

// mustEmbedUnimplementedTransactionStatusServer indicates an expected call of mustEmbedUnimplementedTransactionStatusServer.
func (mr *MockUnsafeTransactionStatusServerMockRecorder) mustEmbedUnimplementedTransactionStatusServer() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "mustEmbedUnimplementedTransactionStatusServer", reflect.TypeOf((*MockUnsafeTransactionStatusServer)(nil).mustEmbedUnimplementedTransactionStatusServer))
}
