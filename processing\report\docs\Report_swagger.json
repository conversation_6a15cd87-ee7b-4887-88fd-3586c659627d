{"schemes": ["https", "http"], "swagger": "2.0", "info": {"description": "report", "title": "report", "contact": {}}, "host": "prapi.dev-tarlanpayments.kz", "basePath": "/report", "paths": {"/api/v1/acquirer/options/with-commissions": {"get": {"security": [{"bearerAuth": []}], "description": "Запрос на получение списка опций эквайера с актульной комиссией на текущий момент", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["commissions"], "summary": "Получение списка опций эквайера с актуальной комиссией", "parameters": [{"type": "integer", "description": "per_page", "name": "per_page", "in": "query"}, {"type": "integer", "description": "page", "name": "page", "in": "query"}, {"minLength": 1, "type": "string", "name": "acquirer_id", "in": "query"}, {"minLength": 1, "type": "string", "name": "aggregated_type_id", "in": "query"}, {"type": "string", "name": "country_id", "in": "query"}, {"type": "string", "name": "ips_id", "in": "query"}, {"type": "string", "name": "issuer_id", "in": "query"}, {"type": "string", "name": "last_transaction_date_from", "in": "query"}, {"type": "string", "name": "last_transaction_date_to", "in": "query"}, {"minLength": 1, "type": "string", "name": "merchant_id", "in": "query"}, {"minLength": 1, "type": "string", "name": "project_id", "in": "query"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/middlewares.Response-array_model_AcquirerCommission"}, "headers": {"x-page": {"type": "int", "description": "1"}, "x-per-page": {"type": "int", "description": "1"}, "x-total": {"type": "int", "description": "11"}}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/middlewares.Response-middlewares_Empty"}}}}}, "/api/v1/acquirer/options/with-commissions/download/csv": {"get": {"security": [{"bearerAuth": []}], "description": "Запрос на получение списка опций эквайера с актульной комиссией на текущий момент", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["commissions"], "summary": "Выгрузка списка опций эквайера с актуальной комиссией в формате csv", "parameters": [{"type": "integer", "description": "per_page", "name": "per_page", "in": "query"}, {"type": "integer", "description": "page", "name": "page", "in": "query"}, {"minLength": 1, "type": "string", "name": "acquirer_id", "in": "query"}, {"minLength": 1, "type": "string", "name": "aggregated_type_id", "in": "query"}, {"type": "string", "name": "country_id", "in": "query"}, {"type": "string", "name": "ips_id", "in": "query"}, {"type": "string", "name": "issuer_id", "in": "query"}, {"type": "string", "name": "language", "in": "query", "required": true}, {"type": "string", "name": "last_transaction_date_from", "in": "query"}, {"type": "string", "name": "last_transaction_date_to", "in": "query"}, {"minLength": 1, "type": "string", "name": "merchant_id", "in": "query"}, {"minLength": 1, "type": "string", "name": "project_id", "in": "query"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/middlewares.Response-string"}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/middlewares.Response-middlewares_Empty"}}}}}, "/api/v1/acquirer/options/with-commissions/download/xlsx": {"get": {"security": [{"bearerAuth": []}], "description": "Запрос на получение списка опций эквайера с актульной комиссией на текущий момент", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["commissions"], "summary": "Выгрузка списка опций эквайера с актуальной комиссией в формате xlsx", "parameters": [{"type": "integer", "description": "per_page", "name": "per_page", "in": "query"}, {"type": "integer", "description": "page", "name": "page", "in": "query"}, {"minLength": 1, "type": "string", "name": "acquirer_id", "in": "query"}, {"minLength": 1, "type": "string", "name": "aggregated_type_id", "in": "query"}, {"type": "string", "name": "country_id", "in": "query"}, {"type": "string", "name": "ips_id", "in": "query"}, {"type": "string", "name": "issuer_id", "in": "query"}, {"type": "string", "name": "language", "in": "query", "required": true}, {"type": "string", "name": "last_transaction_date_from", "in": "query"}, {"type": "string", "name": "last_transaction_date_to", "in": "query"}, {"minLength": 1, "type": "string", "name": "merchant_id", "in": "query"}, {"minLength": 1, "type": "string", "name": "project_id", "in": "query"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/middlewares.Response-string"}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/middlewares.Response-middlewares_Empty"}}}}}, "/api/v1/amount": {"get": {"security": [{"bearerAuth": []}], "description": "Получение списка суммарной и максимальной транзакции по пользователю за определенный период времени", "produces": ["application/json"], "tags": ["amounts"], "summary": "Получение списка пользователей с пороговыми суммами", "parameters": [{"type": "string", "name": "created_at", "in": "query"}, {"type": "string", "name": "finished_at", "in": "query"}, {"type": "number", "name": "max_amount_lower_bound", "in": "query"}, {"type": "number", "name": "max_amount_upper_bound", "in": "query"}, {"type": "string", "name": "project_client_id", "in": "query"}, {"type": "number", "name": "sum_amount_lower_bound", "in": "query"}, {"type": "number", "name": "sum_amount_upper_bound", "in": "query"}, {"type": "integer", "name": "transaction_status_id", "in": "query"}, {"type": "array", "items": {"type": "integer"}, "collectionFormat": "csv", "name": "transaction_type_ids", "in": "query"}, {"type": "integer", "description": "per_page", "name": "per_page", "in": "query"}, {"type": "integer", "description": "page", "name": "page", "in": "query"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/middlewares.Response-array_schema_UserAmountResponse"}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/middlewares.Response-middlewares_Empty"}}}}}, "/api/v1/billing/balance/histories": {"get": {"security": [{"bearerAuth": []}], "description": "Берет данные из view report.vw_balance_history_details\nДанные отображаются на странице CRM \"выписка транзитного счета\"", "produces": ["application/json"], "tags": ["billing-balance-histories"], "summary": "Получение данных по движению денежных средств на балансах транзитных счетов", "parameters": [{"type": "string", "name": "account_bank_id", "in": "query"}, {"type": "string", "name": "balance_calculated_date_from", "in": "query"}, {"type": "string", "name": "balance_calculated_date_to", "in": "query"}, {"type": "string", "name": "balance_owner_bin", "in": "query"}, {"type": "integer", "name": "balance_owner_earn_type_id", "in": "query"}, {"type": "string", "name": "balance_owner_id", "in": "query"}, {"type": "integer", "name": "balance_owner_status_id", "in": "query"}, {"type": "string", "name": "merchant_id", "in": "query"}, {"type": "integer", "description": "per_page", "name": "per_page", "in": "query"}, {"type": "integer", "description": "page", "name": "page", "in": "query"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/middlewares.Response-delivery_http_v1_getBalanceHistories_response"}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/middlewares.Response-middlewares_Empty"}}}}}, "/api/v1/billing/balance/histories/customize": {"get": {"security": [{"bearerAuth": []}], "description": "Используется для получения полей которые нужно отобразить для пользователя", "produces": ["application/json"], "tags": ["billing-balance-histories"], "summary": "Получение сохранённых полей", "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/middlewares.Response-model_Field"}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/middlewares.Response-middlewares_Empty"}}}}, "put": {"security": [{"bearerAuth": []}], "description": "Используется для обновление полей которые юзер бы хотел видеть у себя", "produces": ["application/json"], "tags": ["billing-balance-histories"], "summary": "Обновление полей пользователя", "parameters": [{"description": "Field Data", "name": "data", "in": "body", "required": true, "schema": {"$ref": "#/definitions/delivery_http_v1.createOrUpdateBalanceHistoriesUserFields.request"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/middlewares.Response-string"}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/middlewares.Response-middlewares_Empty"}}}}}, "/api/v1/billing/balance/histories/download/csv": {"get": {"security": [{"bearerAuth": []}], "description": "Берет данные из view report.vw_balance_history_details\nДанные отображаются на странице CRM \"выписка транзитного счета\"", "produces": ["application/json"], "tags": ["billing-balance-histories"], "summary": "Выгрузка данных по движению денежных средств на балансах транзитных счетов в формате csv", "parameters": [{"type": "string", "name": "account_bank_id", "in": "query"}, {"type": "string", "name": "balance_calculated_date_from", "in": "query"}, {"type": "string", "name": "balance_calculated_date_to", "in": "query"}, {"type": "string", "name": "balance_owner_bin", "in": "query"}, {"type": "integer", "name": "balance_owner_earn_type_id", "in": "query"}, {"type": "string", "name": "balance_owner_id", "in": "query"}, {"type": "integer", "name": "balance_owner_status_id", "in": "query"}, {"type": "string", "name": "language", "in": "query", "required": true}, {"type": "string", "name": "merchant_id", "in": "query"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/middlewares.Response-string"}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/middlewares.Response-middlewares_Empty"}}}}}, "/api/v1/billing/balance/histories/download/xlsx": {"get": {"security": [{"bearerAuth": []}], "description": "Берет данные из view report.vw_balance_history_details\nДанные отображаются на странице CRM \"выписка транзитного счета\"", "produces": ["application/json"], "tags": ["billing-balance-histories"], "summary": "Выгрузка данных по движению денежных средств на балансах транзитных счетов в формате xlsx", "parameters": [{"type": "string", "name": "account_bank_id", "in": "query"}, {"type": "string", "name": "balance_calculated_date_from", "in": "query"}, {"type": "string", "name": "balance_calculated_date_to", "in": "query"}, {"type": "string", "name": "balance_owner_bin", "in": "query"}, {"type": "integer", "name": "balance_owner_earn_type_id", "in": "query"}, {"type": "string", "name": "balance_owner_id", "in": "query"}, {"type": "integer", "name": "balance_owner_status_id", "in": "query"}, {"type": "string", "name": "language", "in": "query", "required": true}, {"type": "string", "name": "merchant_id", "in": "query"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/middlewares.Response-string"}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/middlewares.Response-middlewares_Empty"}}}}}, "/api/v1/billing/client/operations": {"get": {"security": [{"bearerAuth": []}], "description": "Отображение клиентских операций по фильтру на фронте\nДостает данные с view client_operations", "produces": ["application/json"], "tags": ["billing-client-operations"], "summary": "Получение списка клиентских операций из ресурса billing", "parameters": [{"type": "string", "name": "account_bank_id", "in": "query"}, {"type": "string", "name": "account_number", "in": "query"}, {"type": "string", "name": "balance_owner_bin", "in": "query"}, {"type": "string", "name": "balance_owner_id", "in": "query"}, {"type": "string", "name": "balance_type_code", "in": "query"}, {"type": "string", "name": "created_at_from", "in": "query"}, {"type": "string", "name": "created_at_to", "in": "query"}, {"type": "string", "name": "merchant_id", "in": "query"}, {"type": "number", "name": "operation_amount_from", "in": "query"}, {"type": "number", "name": "operation_amount_to", "in": "query"}, {"type": "string", "name": "operation_id", "in": "query"}, {"type": "string", "name": "operation_status_id", "in": "query"}, {"type": "string", "name": "operation_type_id", "in": "query"}, {"type": "string", "name": "transaction_id", "in": "query"}, {"type": "integer", "description": "per_page", "name": "per_page", "in": "query"}, {"type": "integer", "description": "page", "name": "page", "in": "query"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/middlewares.Response-array_schema_ClientOperationResponse"}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/middlewares.Response-middlewares_Empty"}}}}}, "/api/v1/billing/client/operations/customize": {"get": {"security": [{"bearerAuth": []}], "description": "Используется для получения полей которые нужно отобразить для пользователя", "produces": ["application/json"], "tags": ["billing-client-operations"], "summary": "Получение сохранённых полей в клеинетских операциях", "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/middlewares.Response-model_Field"}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/middlewares.Response-middlewares_Empty"}}}}, "put": {"security": [{"bearerAuth": []}], "description": "Используется для обновление полей которые юзер бы хотел видеть у себя", "produces": ["application/json"], "tags": ["billing-client-operations"], "summary": "Обновление полей пользователя по клиентским операциям", "parameters": [{"description": "Field Data", "name": "data", "in": "body", "required": true, "schema": {"$ref": "#/definitions/delivery_http_v1.createOrUpdateUserFields.request"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/middlewares.Response-string"}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/middlewares.Response-middlewares_Empty"}}}}}, "/api/v1/billing/client/operations/download/csv": {"get": {"security": [{"bearerAuth": []}], "description": "Фильтры находятся в теле запроса", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["billing-client-operations"], "summary": "Выгрузка списка клиентских операций по фильтрам в формате csv", "parameters": [{"type": "string", "name": "account_bank_id", "in": "query"}, {"type": "string", "name": "account_number", "in": "query"}, {"type": "string", "name": "balance_owner_bin", "in": "query"}, {"type": "string", "name": "balance_owner_id", "in": "query"}, {"type": "string", "name": "balance_type_code", "in": "query"}, {"type": "string", "name": "create_at_from", "in": "query"}, {"type": "string", "name": "create_at_to", "in": "query"}, {"type": "string", "name": "language", "in": "query", "required": true}, {"type": "string", "name": "merchant_id", "in": "query"}, {"type": "number", "name": "operation_amount_from", "in": "query"}, {"type": "number", "name": "operation_amount_to", "in": "query"}, {"type": "string", "name": "operation_id", "in": "query"}, {"type": "string", "name": "operation_status_id", "in": "query"}, {"type": "string", "name": "operation_type_id", "in": "query"}, {"type": "string", "name": "transaction_id", "in": "query"}, {"type": "integer", "description": "per_page", "name": "per_page", "in": "query"}, {"type": "integer", "description": "page", "name": "page", "in": "query"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/middlewares.Response-string"}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/middlewares.Response-middlewares_Empty"}}}}}, "/api/v1/billing/client/operations/download/xlsx": {"get": {"security": [{"bearerAuth": []}], "description": "Фильтры находятся в теле запроса", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["billing-client-operations"], "summary": "Выгрузка списка клиентских операций по фильтрам в формате xlsx", "parameters": [{"type": "string", "name": "account_bank_id", "in": "query"}, {"type": "string", "name": "account_number", "in": "query"}, {"type": "string", "name": "balance_owner_bin", "in": "query"}, {"type": "string", "name": "balance_owner_id", "in": "query"}, {"type": "string", "name": "balance_type_code", "in": "query"}, {"type": "string", "name": "create_at_from", "in": "query"}, {"type": "string", "name": "create_at_to", "in": "query"}, {"type": "string", "name": "language", "in": "query", "required": true}, {"type": "string", "name": "merchant_id", "in": "query"}, {"type": "number", "name": "operation_amount_from", "in": "query"}, {"type": "number", "name": "operation_amount_to", "in": "query"}, {"type": "string", "name": "operation_id", "in": "query"}, {"type": "string", "name": "operation_status_id", "in": "query"}, {"type": "string", "name": "operation_type_id", "in": "query"}, {"type": "string", "name": "transaction_id", "in": "query"}, {"type": "integer", "description": "per_page", "name": "per_page", "in": "query"}, {"type": "integer", "description": "page", "name": "page", "in": "query"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/middlewares.Response-string"}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/middlewares.Response-middlewares_Empty"}}}}}, "/api/v1/billing/finance/operations": {"get": {"security": [{"bearerAuth": []}], "description": "Используется на фронте на странице финансовые операций.\nБерет данные из view Finance_operations", "produces": ["application/json"], "tags": ["billing-finance-operations"], "summary": "Получение списка финансовых операций из ресурса billing", "parameters": [{"type": "integer", "description": "per_page", "name": "per_page", "in": "query"}, {"type": "integer", "description": "page", "name": "page", "in": "query"}, {"type": "string", "name": "account_bank_id", "in": "query"}, {"type": "string", "name": "account_number", "in": "query"}, {"type": "string", "name": "balance_owner_bin", "in": "query"}, {"type": "string", "name": "balance_owner_id", "in": "query"}, {"type": "string", "name": "created_at_from", "in": "query"}, {"type": "string", "name": "created_at_to", "in": "query"}, {"type": "string", "name": "merchant_id", "in": "query"}, {"type": "number", "name": "operation_amount_from", "in": "query"}, {"type": "number", "name": "operation_amount_to", "in": "query"}, {"type": "integer", "name": "operation_id", "in": "query"}, {"type": "integer", "name": "operation_type_id", "in": "query"}, {"type": "integer", "name": "transfer_id", "in": "query"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/middlewares.Response-array_schema_Transaction"}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/middlewares.Response-middlewares_Empty"}}}}}, "/api/v1/billing/finance/operations/customize": {"get": {"security": [{"bearerAuth": []}], "description": "Используется для получения полей которые нужно отобразить для пользователя", "produces": ["application/json"], "tags": ["billing-finance-operations"], "summary": "Получение сохранённых полей в финансовых операциях", "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/middlewares.Response-model_Field"}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/middlewares.Response-middlewares_Empty"}}}}, "put": {"security": [{"bearerAuth": []}], "description": "Используется для обновление полей которые юзер бы хотел видеть у себя", "produces": ["application/json"], "tags": ["billing-finance-operations"], "summary": "Обновление полей пользователя по финансовым операциям", "parameters": [{"description": "Field Data", "name": "data", "in": "body", "required": true, "schema": {"$ref": "#/definitions/delivery_http_v1.createOrUpdateUserFields.request"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/middlewares.Response-string"}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/middlewares.Response-middlewares_Empty"}}}}}, "/api/v1/billing/finance/operations/download/csv": {"get": {"security": [{"bearerAuth": []}], "description": "Фильтры находятся в теле запроса", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["billing-finance-operations"], "summary": "Выгрузка списка финансовых операций по фильтрам в формате csv", "parameters": [{"type": "string", "name": "account_bank_id", "in": "query"}, {"type": "string", "name": "account_number", "in": "query"}, {"type": "string", "name": "balance_owner_bin", "in": "query"}, {"type": "string", "name": "balance_owner_id", "in": "query"}, {"type": "string", "name": "created_at_from", "in": "query"}, {"type": "string", "name": "created_at_to", "in": "query"}, {"type": "string", "name": "language", "in": "query", "required": true}, {"type": "string", "name": "merchant_id", "in": "query"}, {"type": "number", "name": "operation_amount_from", "in": "query"}, {"type": "number", "name": "operation_amount_to", "in": "query"}, {"type": "integer", "name": "operation_id", "in": "query"}, {"type": "integer", "name": "operation_type_id", "in": "query"}, {"type": "integer", "name": "transfer_id", "in": "query"}, {"type": "integer", "description": "per_page", "name": "per_page", "in": "query"}, {"type": "integer", "description": "page", "name": "page", "in": "query"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/middlewares.Response-string"}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/middlewares.Response-middlewares_Empty"}}}}}, "/api/v1/billing/finance/operations/download/xlsx": {"get": {"security": [{"bearerAuth": []}], "description": "Фильтры находятся в теле запроса", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["billing-finance-operations"], "summary": "Выгрузка списка финансовых операций по фильтрам в формате xlsx", "parameters": [{"type": "string", "name": "account_bank_id", "in": "query"}, {"type": "string", "name": "account_number", "in": "query"}, {"type": "string", "name": "balance_owner_bin", "in": "query"}, {"type": "string", "name": "balance_owner_id", "in": "query"}, {"type": "string", "name": "created_at_from", "in": "query"}, {"type": "string", "name": "created_at_to", "in": "query"}, {"type": "string", "name": "language", "in": "query", "required": true}, {"type": "string", "name": "merchant_id", "in": "query"}, {"type": "number", "name": "operation_amount_from", "in": "query"}, {"type": "number", "name": "operation_amount_to", "in": "query"}, {"type": "integer", "name": "operation_id", "in": "query"}, {"type": "integer", "name": "operation_type_id", "in": "query"}, {"type": "integer", "name": "transfer_id", "in": "query"}, {"type": "integer", "description": "per_page", "name": "per_page", "in": "query"}, {"type": "integer", "description": "page", "name": "page", "in": "query"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/middlewares.Response-string"}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/middlewares.Response-middlewares_Empty"}}}}}, "/api/v1/billing/overdraft/operations": {"get": {"security": [{"bearerAuth": []}], "description": "Отображение списка данных по овердрафту\nДостает список данных по овердрафту", "produces": ["application/json"], "tags": ["billing-overdraft-operations"], "summary": "Получение списка данных по овердрафту", "parameters": [{"type": "string", "name": "account_bank_id", "in": "query"}, {"type": "string", "name": "account_number_id", "in": "query"}, {"type": "string", "name": "balance_owner_bin", "in": "query"}, {"type": "string", "name": "balance_owner_earn_type_id", "in": "query"}, {"type": "string", "name": "balance_owner_id", "in": "query"}, {"type": "string", "name": "balance_owner_status_id", "in": "query"}, {"type": "string", "name": "merchant_id", "in": "query"}, {"type": "integer", "description": "per_page", "name": "per_page", "in": "query"}, {"type": "integer", "description": "page", "name": "page", "in": "query"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/middlewares.Response-array_schema_OverdraftOperationResponse"}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/middlewares.Response-middlewares_Empty"}}}}}, "/api/v1/billing/overdraft/operations/customize": {"get": {"security": [{"bearerAuth": []}], "description": "Используется для получения полей которые нужно отобразить для пользователя", "produces": ["application/json"], "tags": ["billing-overdraft-operations"], "summary": "Получение сохранённых полей в списках овердрафта", "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/middlewares.Response-model_Field"}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/middlewares.Response-middlewares_Empty"}}}}, "put": {"security": [{"bearerAuth": []}], "description": "Используется для обновление полей которые юзер бы хотел видеть у себя", "produces": ["application/json"], "tags": ["billing-overdraft-operations"], "summary": "Обновление полей пользователя по списку овердрафта", "parameters": [{"description": "Field Data", "name": "data", "in": "body", "required": true, "schema": {"$ref": "#/definitions/delivery_http_v1.createOrUpdateUserFields.request"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/middlewares.Response-string"}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/middlewares.Response-middlewares_Empty"}}}}}, "/api/v1/billing/overdraft/operations/download/csv": {"get": {"security": [{"bearerAuth": []}], "description": "Фильтры находятся в теле запроса", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["billing-overdraft-operations"], "summary": "Выгрузка списка данных по овердрафту", "parameters": [{"type": "string", "name": "account_bank_id", "in": "query"}, {"type": "string", "name": "account_number_id", "in": "query"}, {"type": "string", "name": "balance_owner_bin", "in": "query"}, {"type": "string", "name": "balance_owner_earn_type_id", "in": "query"}, {"type": "string", "name": "balance_owner_id", "in": "query"}, {"type": "string", "name": "balance_owner_status_id", "in": "query"}, {"type": "string", "name": "language", "in": "query", "required": true}, {"type": "string", "name": "merchant_id", "in": "query"}, {"type": "integer", "description": "per_page", "name": "per_page", "in": "query"}, {"type": "integer", "description": "page", "name": "page", "in": "query"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/middlewares.Response-string"}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/middlewares.Response-middlewares_Empty"}}}}}, "/api/v1/billing/overdraft/operations/download/xlsx": {"get": {"security": [{"bearerAuth": []}], "description": "Фильтры находятся в теле запроса", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["billing-overdraft-operations"], "summary": "Выгрузка списка данных по овердрафту", "parameters": [{"type": "string", "name": "account_bank_id", "in": "query"}, {"type": "string", "name": "account_number_id", "in": "query"}, {"type": "string", "name": "balance_owner_bin", "in": "query"}, {"type": "string", "name": "balance_owner_earn_type_id", "in": "query"}, {"type": "string", "name": "balance_owner_id", "in": "query"}, {"type": "string", "name": "balance_owner_status_id", "in": "query"}, {"type": "string", "name": "language", "in": "query", "required": true}, {"type": "string", "name": "merchant_id", "in": "query"}, {"type": "integer", "description": "per_page", "name": "per_page", "in": "query"}, {"type": "integer", "description": "page", "name": "page", "in": "query"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/middlewares.Response-string"}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/middlewares.Response-middlewares_Empty"}}}}}, "/api/v1/payment/orders": {"get": {"security": [{"bearerAuth": []}], "description": "Get Payment Orders", "produces": ["application/json"], "tags": ["payment-orders"], "summary": "Get Payment Orders", "parameters": [{"type": "string", "name": "account_bank_id", "in": "query"}, {"type": "string", "name": "account_number", "in": "query"}, {"type": "string", "name": "balance_owner_bin", "in": "query"}, {"type": "string", "name": "balance_owner_id", "in": "query"}, {"type": "string", "name": "payment_order_date_from", "in": "query"}, {"type": "string", "name": "payment_order_date_to", "in": "query"}, {"type": "string", "name": "payment_order_end_date_from", "in": "query"}, {"type": "string", "name": "payment_order_end_date_to", "in": "query"}, {"type": "integer", "name": "payment_order_id", "in": "query"}, {"type": "string", "name": "payment_order_status_id", "in": "query"}, {"type": "integer", "description": "per_page", "name": "per_page", "in": "query"}, {"type": "integer", "description": "page", "name": "page", "in": "query"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/middlewares.Response-array_schema_PaymentOrderResponse"}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/middlewares.Response-middlewares_Empty"}}}}}, "/api/v1/payment/orders/customize": {"get": {"security": [{"bearerAuth": []}], "description": "Используется для получения полей которые нужно отобразить для пользователя", "produces": ["application/json"], "tags": ["payment-orders"], "summary": "Получение сохранённых полей", "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/middlewares.Response-model_Field"}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/middlewares.Response-middlewares_Empty"}}}}, "put": {"security": [{"bearerAuth": []}], "description": "Используется для обновление полей которые юзер бы хотел видеть у себя", "produces": ["application/json"], "tags": ["payment-orders"], "summary": "Обновление полей пользователя", "parameters": [{"description": "Field Data", "name": "data", "in": "body", "required": true, "schema": {"$ref": "#/definitions/delivery_http_v1.createOrUpdatePaymentOrdersCustomization.request"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/middlewares.Response-string"}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/middlewares.Response-middlewares_Empty"}}}}}, "/api/v1/payment/orders/download/csv": {"get": {"security": [{"bearerAuth": []}], "description": "download payment orders report in csv format", "produces": ["application/json"], "tags": ["payment-orders"], "summary": "download payment orders report in csv format", "parameters": [{"type": "string", "name": "account_bank_id", "in": "query"}, {"type": "string", "name": "account_number", "in": "query"}, {"type": "string", "name": "balance_owner_bin", "in": "query"}, {"type": "string", "name": "balance_owner_id", "in": "query"}, {"type": "string", "name": "language", "in": "query"}, {"type": "string", "name": "payment_order_date_from", "in": "query"}, {"type": "string", "name": "payment_order_date_to", "in": "query"}, {"type": "string", "name": "payment_order_end_date_from", "in": "query"}, {"type": "string", "name": "payment_order_end_date_to", "in": "query"}, {"type": "integer", "name": "payment_order_id", "in": "query"}, {"type": "string", "name": "payment_order_status_id", "in": "query"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/middlewares.Response-string"}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/middlewares.Response-middlewares_Empty"}}}}}, "/api/v1/payment/orders/download/xlsx": {"get": {"security": [{"bearerAuth": []}], "description": "download payment orders report in xlsx format", "produces": ["application/json"], "tags": ["payment-orders"], "summary": "download payment orders report in xlsx format", "parameters": [{"type": "string", "name": "account_bank_id", "in": "query"}, {"type": "string", "name": "account_number", "in": "query"}, {"type": "string", "name": "balance_owner_bin", "in": "query"}, {"type": "string", "name": "balance_owner_id", "in": "query"}, {"type": "string", "name": "language", "in": "query"}, {"type": "string", "name": "payment_order_date_from", "in": "query"}, {"type": "string", "name": "payment_order_date_to", "in": "query"}, {"type": "string", "name": "payment_order_end_date_from", "in": "query"}, {"type": "string", "name": "payment_order_end_date_to", "in": "query"}, {"type": "integer", "name": "payment_order_id", "in": "query"}, {"type": "string", "name": "payment_order_status_id", "in": "query"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/middlewares.Response-string"}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/middlewares.Response-middlewares_Empty"}}}}}, "/api/v1/project/lower/options/with-commissions": {"get": {"security": [{"bearerAuth": []}], "description": "Запрос на получение списка нижних опций проекта с актульной комиссией на текущий момент", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["commissions"], "summary": "Получение списка нижних опций проекта с актуальной комиссией", "parameters": [{"type": "integer", "description": "per_page", "name": "per_page", "in": "query"}, {"type": "integer", "description": "page", "name": "page", "in": "query"}, {"minLength": 1, "type": "string", "name": "acquirer_id", "in": "query"}, {"minLength": 1, "type": "string", "name": "aggregated_type_id", "in": "query"}, {"type": "string", "name": "country_id", "in": "query"}, {"type": "string", "name": "ips_id", "in": "query"}, {"type": "string", "name": "issuer_id", "in": "query"}, {"type": "string", "name": "last_transaction_date_from", "in": "query"}, {"type": "string", "name": "last_transaction_date_to", "in": "query"}, {"minLength": 1, "type": "string", "name": "merchant_id", "in": "query"}, {"minLength": 1, "type": "string", "name": "project_id", "in": "query"}, {"type": "string", "name": "project_lower_option_type", "in": "query"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/middlewares.Response-array_model_ProjectLowerCommission"}, "headers": {"x-page": {"type": "int", "description": "1"}, "x-per-page": {"type": "int", "description": "1"}, "x-total": {"type": "int", "description": "11"}}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/middlewares.Response-middlewares_Empty"}}}}}, "/api/v1/project/lower/options/with-commissions/download/csv": {"get": {"security": [{"bearerAuth": []}], "description": "Запрос на получение списка нижних опций проекта с актульной комиссией на текущий момент", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["commissions"], "summary": "Выгрузка списка нижних опций проекта с актуальной комиссией в формате csv", "parameters": [{"type": "integer", "description": "per_page", "name": "per_page", "in": "query"}, {"type": "integer", "description": "page", "name": "page", "in": "query"}, {"minLength": 1, "type": "string", "name": "acquirer_id", "in": "query"}, {"minLength": 1, "type": "string", "name": "aggregated_type_id", "in": "query"}, {"type": "string", "name": "country_id", "in": "query"}, {"type": "string", "name": "ips_id", "in": "query"}, {"type": "string", "name": "issuer_id", "in": "query"}, {"type": "string", "name": "language", "in": "query", "required": true}, {"type": "string", "name": "last_transaction_date_from", "in": "query"}, {"type": "string", "name": "last_transaction_date_to", "in": "query"}, {"minLength": 1, "type": "string", "name": "merchant_id", "in": "query"}, {"minLength": 1, "type": "string", "name": "project_id", "in": "query"}, {"type": "string", "name": "project_lower_option_type", "in": "query"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/middlewares.Response-string"}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/middlewares.Response-middlewares_Empty"}}}}}, "/api/v1/project/lower/options/with-commissions/download/xlsx": {"get": {"security": [{"bearerAuth": []}], "description": "Запрос на получение списка нижних опций проекта с актульной комиссией на текущий момент", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["commissions"], "summary": "Выгрузка списка нижних опций проекта с актуальной комиссией в формате xlsx", "parameters": [{"type": "integer", "description": "per_page", "name": "per_page", "in": "query"}, {"type": "integer", "description": "page", "name": "page", "in": "query"}, {"minLength": 1, "type": "string", "name": "acquirer_id", "in": "query"}, {"minLength": 1, "type": "string", "name": "aggregated_type_id", "in": "query"}, {"type": "string", "name": "country_id", "in": "query"}, {"type": "string", "name": "ips_id", "in": "query"}, {"type": "string", "name": "issuer_id", "in": "query"}, {"type": "string", "name": "language", "in": "query", "required": true}, {"type": "string", "name": "last_transaction_date_from", "in": "query"}, {"type": "string", "name": "last_transaction_date_to", "in": "query"}, {"minLength": 1, "type": "string", "name": "merchant_id", "in": "query"}, {"minLength": 1, "type": "string", "name": "project_id", "in": "query"}, {"type": "string", "name": "project_lower_option_type", "in": "query"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/middlewares.Response-string"}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/middlewares.Response-middlewares_Empty"}}}}}, "/api/v1/project/upper/options/with-commissions": {"get": {"security": [{"bearerAuth": []}], "description": "Запрос на получение списка верхних опций проекта с актульной комиссией на текущий момент", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["commissions"], "summary": "Получение списка верхних опций проекта с актуальной комиссией", "parameters": [{"type": "integer", "description": "per_page", "name": "per_page", "in": "query"}, {"type": "integer", "description": "page", "name": "page", "in": "query"}, {"minLength": 1, "type": "string", "name": "acquirer_id", "in": "query"}, {"minLength": 1, "type": "string", "name": "aggregated_type_id", "in": "query"}, {"type": "string", "name": "last_transaction_date_from", "in": "query"}, {"type": "string", "name": "last_transaction_date_to", "in": "query"}, {"minLength": 1, "type": "string", "name": "merchant_id", "in": "query"}, {"minLength": 1, "type": "string", "name": "project_id", "in": "query"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/middlewares.Response-array_model_ProjectUpperCommission"}, "headers": {"x-page": {"type": "int", "description": "1"}, "x-per-page": {"type": "int", "description": "1"}, "x-total": {"type": "int", "description": "11"}}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/middlewares.Response-middlewares_Empty"}}}}}, "/api/v1/project/upper/options/with-commissions/download/csv": {"get": {"security": [{"bearerAuth": []}], "description": "Запрос на получение списка верхних опций проекта с актульной комиссией на текущий момент", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["commissions"], "summary": "Выгрузка списка верхних опций проекта с актуальной комиссией в формате csv", "parameters": [{"type": "integer", "description": "per_page", "name": "per_page", "in": "query"}, {"type": "integer", "description": "page", "name": "page", "in": "query"}, {"minLength": 1, "type": "string", "name": "acquirer_id", "in": "query"}, {"minLength": 1, "type": "string", "name": "aggregated_type_id", "in": "query"}, {"type": "string", "name": "language", "in": "query", "required": true}, {"type": "string", "name": "last_transaction_date_from", "in": "query"}, {"type": "string", "name": "last_transaction_date_to", "in": "query"}, {"minLength": 1, "type": "string", "name": "merchant_id", "in": "query"}, {"minLength": 1, "type": "string", "name": "project_id", "in": "query"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/middlewares.Response-array_model_ProjectUpperCommission"}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/middlewares.Response-string"}}}}}, "/api/v1/project/upper/options/with-commissions/download/xlsx": {"get": {"security": [{"bearerAuth": []}], "description": "Запрос на получение списка верхних опций проекта с актульной комиссией на текущий момент", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["commissions"], "summary": "Выгрузка списка верхних опций проекта с актуальной комиссией в формате xlsx", "parameters": [{"type": "integer", "description": "per_page", "name": "per_page", "in": "query"}, {"type": "integer", "description": "page", "name": "page", "in": "query"}, {"minLength": 1, "type": "string", "name": "acquirer_id", "in": "query"}, {"minLength": 1, "type": "string", "name": "aggregated_type_id", "in": "query"}, {"type": "string", "name": "language", "in": "query", "required": true}, {"type": "string", "name": "last_transaction_date_from", "in": "query"}, {"type": "string", "name": "last_transaction_date_to", "in": "query"}, {"minLength": 1, "type": "string", "name": "merchant_id", "in": "query"}, {"minLength": 1, "type": "string", "name": "project_id", "in": "query"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/middlewares.Response-array_model_ProjectUpperCommission"}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/middlewares.Response-string"}}}}}, "/api/v1/rules": {"get": {"security": [{"bearerAuth": []}], "description": "Возвращает список с пагинацией и фильтрацией", "produces": ["application/json"], "tags": ["acquirer-rules"], "summary": "Получение списка правил с эквайерами", "parameters": [{"type": "string", "name": "is_base", "in": "query"}, {"type": "string", "name": "project_id", "in": "query"}, {"type": "string", "name": "transaction_type_id", "in": "query"}, {"type": "string", "default": "DESC", "name": "weight_sort_by", "in": "query"}, {"type": "integer", "description": "per_page", "name": "per_page", "in": "query"}, {"type": "integer", "description": "page", "name": "page", "in": "query"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/middlewares.Response-array_schema_RuleResponse"}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/middlewares.Response-middlewares_Empty"}}}}}, "/api/v1/rules/customize": {"get": {"security": [{"bearerAuth": []}], "description": "Используется для получения полей которые нужно отобразить для пользователя", "produces": ["application/json"], "tags": ["acquirer-rules"], "summary": "Получение сохранённых полей по списку правил экваеров", "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/middlewares.Response-model_Field"}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/middlewares.Response-middlewares_Empty"}}}}, "put": {"security": [{"bearerAuth": []}], "description": "Используется для обновление полей которые юзер бы хотел видеть у себя", "produces": ["application/json"], "tags": ["acquirer-rules"], "summary": "Обновление полей пользователя по списку правил экваеров", "parameters": [{"description": "Field Data", "name": "data", "in": "body", "required": true, "schema": {"$ref": "#/definitions/delivery_http_v1.createOrUpdateUserFields.request"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/middlewares.Response-string"}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/middlewares.Response-middlewares_Empty"}}}}}, "/api/v1/rules/{rule_id}/show": {"get": {"security": [{"bearerAuth": []}], "produces": ["application/json"], "tags": ["acquirer-rules"], "summary": "Получение правила по id", "parameters": [{"type": "integer", "description": "Rule ID", "name": "rule_id", "in": "path", "required": true}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/middlewares.Response-schema_RuleResponse"}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/middlewares.Response-middlewares_Empty"}}}}}, "/api/v1/splitting/settings": {"get": {"security": [{"bearerAuth": []}], "description": "Отображение сплиттинга платежа по фильтру на фронте", "produces": ["application/json"], "tags": ["splitting-settings"], "summary": "Получение настроек сплиттинга платежа", "parameters": [{"type": "string", "name": "merchant_id", "in": "query"}, {"type": "string", "name": "payer_project_id", "in": "query"}, {"type": "string", "name": "recipient_project_id", "in": "query"}, {"type": "integer", "description": "per_page", "name": "per_page", "in": "query"}, {"type": "integer", "description": "page", "name": "page", "in": "query"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/middlewares.Response-array_delivery_http_v1_splittingSettingsResponse"}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/middlewares.Response-middlewares_Empty"}}}}}, "/api/v1/splitting/settings/customize": {"get": {"security": [{"bearerAuth": []}], "description": "Используется для получения полей которые нужно отобразить для пользователя", "produces": ["application/json"], "tags": ["splitting-settings"], "summary": "Получение сохранённых полей настроек сплиттинга платежа", "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/middlewares.Response-model_Field"}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/middlewares.Response-middlewares_Empty"}}}}, "put": {"security": [{"bearerAuth": []}], "description": "Используется для обновление полей которые юзер бы хотел видеть у себя", "produces": ["application/json"], "tags": ["splitting-settings"], "summary": "Обновление полей пользователя по настрокам сплиттинга платежа", "parameters": [{"description": "Field Data", "name": "data", "in": "body", "required": true, "schema": {"$ref": "#/definitions/delivery_http_v1.createOrUpdateSplittingSettingCustomization.request"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/middlewares.Response-string"}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/middlewares.Response-middlewares_Empty"}}}}}, "/api/v1/splitting/transfers/{transfer_id}/row-operations": {"get": {"security": [{"bearerAuth": []}], "description": "Используется на фронте на странице детализаций операций.\nБерет данные из view transfer_row_operations", "produces": ["application/json"], "tags": ["splitting-finance-operations"], "summary": "Получение списка финансовых операций из ресурса splitting", "parameters": [{"type": "integer", "description": "TransferID", "name": "transfer_id", "in": "path", "required": true}, {"type": "integer", "description": "per_page", "name": "per_page", "in": "query"}, {"type": "integer", "description": "page", "name": "page", "in": "query"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/middlewares.Response-array_delivery_http_v1_getTransferDetails_response"}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/middlewares.Response-middlewares_Empty"}}}}}, "/api/v1/terminals": {"get": {"security": [{"bearerAuth": []}], "description": "Фильтры находятся в теле запроса", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["terminals"], "summary": "Получение списка терминалов по фильтрам", "parameters": [{"type": "string", "name": "acquirer_name", "in": "query"}, {"type": "string", "name": "acquirer_terminal_name", "in": "query"}, {"type": "string", "name": "project_name", "in": "query"}, {"type": "integer", "name": "status", "in": "query"}, {"type": "integer", "name": "terminal_tarlan_id", "in": "query"}, {"type": "string", "name": "transaction_type_name", "in": "query"}, {"type": "boolean", "name": "transit", "in": "query"}, {"type": "integer", "description": "per_page", "name": "per_page", "in": "query"}, {"type": "integer", "description": "page", "name": "page", "in": "query"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/middlewares.Response-array_model_Terminal"}, "headers": {"x-page": {"type": "int", "description": "1"}, "x-per-page": {"type": "int", "description": "1"}, "x-total": {"type": "int", "description": "11"}}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/middlewares.Response-middlewares_Empty"}}}}}, "/api/v1/transactions": {"get": {"security": [{"bearerAuth": []}], "description": "Фильтры находятся в теле запроса", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["orders"], "summary": "Получение списка транзакций по фильтрам", "parameters": [{"type": "string", "name": "acquirer_ids", "in": "query"}, {"type": "number", "name": "amount", "in": "query"}, {"type": "string", "description": "date,date", "name": "created_at_from", "in": "query"}, {"type": "string", "name": "created_at_to", "in": "query"}, {"type": "string", "name": "finished_at_from", "in": "query"}, {"type": "string", "name": "finished_at_to", "in": "query"}, {"type": "string", "name": "issuer_ids", "in": "query"}, {"type": "string", "name": "last_refund_date_from", "in": "query"}, {"type": "string", "name": "last_refund_date_to", "in": "query"}, {"type": "string", "name": "masked_pan", "in": "query"}, {"type": "integer", "name": "merchant_id", "in": "query"}, {"type": "string", "name": "project_client_id", "in": "query"}, {"type": "string", "name": "project_ids", "in": "query"}, {"type": "string", "name": "project_reference_id", "in": "query"}, {"type": "string", "name": "rrn", "in": "query"}, {"type": "string", "name": "sort_by", "in": "query"}, {"type": "string", "name": "transaction_ids", "in": "query"}, {"type": "string", "name": "transaction_status_ids", "in": "query"}, {"type": "string", "name": "transaction_type_ids", "in": "query"}, {"type": "integer", "description": "per_page", "name": "per_page", "in": "query"}, {"type": "integer", "description": "page", "name": "page", "in": "query"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/middlewares.Response-model_Transactions"}, "headers": {"x-page": {"type": "int", "description": "1"}, "x-per-page": {"type": "int", "description": "1"}, "x-total": {"type": "int", "description": "11"}}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/middlewares.Response-middlewares_Empty"}}}}}, "/api/v1/transactions/aggregated-info": {"get": {"security": [{"bearerAuth": []}], "description": "Сумма и кол-во высчитываются с учетом фильтров", "produces": ["application/json"], "tags": ["orders"], "summary": "Получение суммы и кол-ва транзакций", "parameters": [{"type": "array", "items": {"type": "string"}, "collectionFormat": "csv", "name": "acquirer_ids", "in": "query"}, {"type": "number", "name": "amount", "in": "query"}, {"type": "string", "description": "date,date", "name": "created_at_from", "in": "query"}, {"type": "string", "name": "created_at_to", "in": "query"}, {"type": "string", "name": "finished_at_from", "in": "query"}, {"type": "string", "name": "finished_at_to", "in": "query"}, {"type": "array", "items": {"type": "string"}, "collectionFormat": "csv", "name": "issuer_ids", "in": "query"}, {"type": "string", "name": "last_refund_date_from", "in": "query"}, {"type": "string", "name": "last_refund_date_to", "in": "query"}, {"type": "string", "name": "masked_pan", "in": "query"}, {"type": "integer", "name": "merchant_id", "in": "query"}, {"type": "string", "name": "project_client_id", "in": "query"}, {"type": "array", "items": {"type": "string"}, "collectionFormat": "csv", "name": "project_ids", "in": "query"}, {"type": "string", "name": "project_reference_id", "in": "query"}, {"type": "string", "name": "rrn", "in": "query"}, {"type": "string", "name": "sort_by", "in": "query"}, {"type": "array", "items": {"type": "string"}, "collectionFormat": "csv", "name": "transaction_ids", "in": "query"}, {"type": "array", "items": {"type": "string"}, "collectionFormat": "csv", "name": "transaction_status_ids", "in": "query"}, {"type": "array", "items": {"type": "string"}, "collectionFormat": "csv", "name": "transaction_type_ids", "in": "query"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/middlewares.Response-schema_AggregatedInfoResponse"}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/middlewares.Response-middlewares_Empty"}}}}}, "/api/v1/transactions/download/csv": {"get": {"security": [{"bearerAuth": []}], "description": "Фильтры находятся в теле запроса", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["orders"], "summary": "Выгрузка списка транзакций по фильтрам в формате csv", "parameters": [{"type": "string", "name": "acquirer_ids", "in": "query"}, {"type": "number", "name": "amount", "in": "query"}, {"type": "string", "description": "date,date", "name": "created_at_from", "in": "query"}, {"type": "string", "name": "created_at_to", "in": "query"}, {"type": "string", "name": "finished_at_from", "in": "query"}, {"type": "string", "name": "finished_at_to", "in": "query"}, {"type": "string", "name": "issuer_ids", "in": "query"}, {"type": "string", "name": "language", "in": "query", "required": true}, {"type": "string", "name": "last_refund_date_from", "in": "query"}, {"type": "string", "name": "last_refund_date_to", "in": "query"}, {"type": "string", "name": "masked_pan", "in": "query"}, {"type": "integer", "name": "merchant_id", "in": "query"}, {"type": "string", "name": "project_client_id", "in": "query"}, {"type": "string", "name": "project_ids", "in": "query"}, {"type": "string", "name": "project_reference_id", "in": "query"}, {"type": "string", "name": "rrn", "in": "query"}, {"type": "string", "name": "sort_by", "in": "query"}, {"type": "string", "name": "transaction_ids", "in": "query"}, {"type": "string", "name": "transaction_status_ids", "in": "query"}, {"type": "string", "name": "transaction_type_ids", "in": "query"}, {"type": "integer", "description": "per_page", "name": "per_page", "in": "query"}, {"type": "integer", "description": "page", "name": "page", "in": "query"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/middlewares.Response-string"}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/middlewares.Response-middlewares_Empty"}}}}}, "/api/v1/transactions/download/xlsx": {"get": {"security": [{"bearerAuth": []}], "description": "Фильтры находятся в теле запроса", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["orders"], "summary": "Выгрузка списка транзакций по фильтрам в формате xlsx", "parameters": [{"type": "string", "name": "acquirer_ids", "in": "query"}, {"type": "number", "name": "amount", "in": "query"}, {"type": "string", "description": "date,date", "name": "created_at_from", "in": "query"}, {"type": "string", "name": "created_at_to", "in": "query"}, {"type": "string", "name": "finished_at_from", "in": "query"}, {"type": "string", "name": "finished_at_to", "in": "query"}, {"type": "string", "name": "issuer_ids", "in": "query"}, {"type": "string", "name": "language", "in": "query", "required": true}, {"type": "string", "name": "last_refund_date_from", "in": "query"}, {"type": "string", "name": "last_refund_date_to", "in": "query"}, {"type": "string", "name": "masked_pan", "in": "query"}, {"type": "integer", "name": "merchant_id", "in": "query"}, {"type": "string", "name": "project_client_id", "in": "query"}, {"type": "string", "name": "project_ids", "in": "query"}, {"type": "string", "name": "project_reference_id", "in": "query"}, {"type": "string", "name": "rrn", "in": "query"}, {"type": "string", "name": "sort_by", "in": "query"}, {"type": "string", "name": "transaction_ids", "in": "query"}, {"type": "string", "name": "transaction_status_ids", "in": "query"}, {"type": "string", "name": "transaction_type_ids", "in": "query"}, {"type": "integer", "description": "per_page", "name": "per_page", "in": "query"}, {"type": "integer", "description": "page", "name": "page", "in": "query"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/middlewares.Response-string"}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/middlewares.Response-middlewares_Empty"}}}}}, "/api/v1/transfer/rules": {"get": {"security": [{"bearerAuth": []}], "description": "Отображение трансфер с правилами по фильтру на фронте\nДостает данные с view transfer_rule", "produces": ["application/json"], "tags": ["transfer-rules"], "summary": "Получение списка трансферов по правилу", "parameters": [{"type": "string", "name": "account_bank_id", "in": "query"}, {"type": "string", "name": "account_number", "in": "query"}, {"type": "string", "name": "balance_owner_bin", "in": "query"}, {"type": "string", "name": "balance_owner_id", "in": "query"}, {"type": "boolean", "name": "is_active", "in": "query"}, {"type": "integer", "description": "per_page", "name": "per_page", "in": "query"}, {"type": "integer", "description": "page", "name": "page", "in": "query"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/middlewares.Response-array_schema_TransferRuleResponse"}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/middlewares.Response-middlewares_Empty"}}}}}, "/api/v1/transfers": {"get": {"security": [{"bearerAuth": []}], "description": "Используется на фронте на странице перевод(трансфер).\nБерет данные из view Transfers", "produces": ["application/json"], "tags": ["transfers"], "summary": "Получение списка трансферов", "parameters": [{"type": "integer", "description": "per_page", "name": "per_page", "in": "query"}, {"type": "integer", "description": "page", "name": "page", "in": "query"}, {"type": "string", "name": "account_bank_id", "in": "query"}, {"type": "string", "name": "account_number", "in": "query"}, {"type": "string", "name": "balance_owner_bin", "in": "query"}, {"type": "string", "name": "balance_owner_id", "in": "query"}, {"type": "string", "name": "created_at_from", "in": "query"}, {"type": "string", "name": "created_at_to", "in": "query"}, {"type": "number", "name": "transfer_amount_from", "in": "query"}, {"type": "number", "name": "transfer_amount_to", "in": "query"}, {"type": "integer", "name": "transfer_id", "in": "query"}, {"type": "string", "name": "transfer_status_id", "in": "query"}, {"type": "string", "name": "transfer_type_id", "in": "query"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/middlewares.Response-array_delivery_http_v1_getTransfers_data"}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/middlewares.Response-middlewares_Empty"}}}}}, "/api/v1/transfers/customize": {"get": {"security": [{"bearerAuth": []}], "description": "Используется для получения полей которые нужно отобразить для пользователя", "produces": ["application/json"], "tags": ["transfers"], "summary": "Получение сохранённых полей в трансферах", "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/middlewares.Response-model_Field"}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/middlewares.Response-middlewares_Empty"}}}}, "put": {"security": [{"bearerAuth": []}], "description": "Используется для обновление полей которые юзер бы хотел видеть у себя", "produces": ["application/json"], "tags": ["transfers"], "summary": "Обновление полей пользователя по трансферам", "parameters": [{"description": "Field Data", "name": "data", "in": "body", "required": true, "schema": {"$ref": "#/definitions/delivery_http_v1.updateTransferCustomization.request"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/middlewares.Response-string"}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/middlewares.Response-middlewares_Empty"}}}}}, "/api/v1/transfers/download/csv": {"get": {"security": [{"bearerAuth": []}], "description": "Фильтры находятся в теле запроса", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["transfers"], "summary": "Выгрузка списка трансферов по фильтрам в формате csv", "parameters": [{"type": "string", "name": "account_bank_id", "in": "query"}, {"type": "string", "name": "account_number", "in": "query"}, {"type": "string", "name": "balance_owner_bin", "in": "query"}, {"type": "string", "name": "balance_owner_id", "in": "query"}, {"type": "string", "name": "created_at_from", "in": "query"}, {"type": "string", "name": "created_at_to", "in": "query"}, {"type": "string", "name": "language", "in": "query", "required": true}, {"type": "number", "name": "transfer_amount_from", "in": "query"}, {"type": "number", "name": "transfer_amount_to", "in": "query"}, {"type": "integer", "name": "transfer_id", "in": "query"}, {"type": "string", "name": "transfer_status_id", "in": "query"}, {"type": "string", "name": "transfer_type_id", "in": "query"}, {"type": "integer", "description": "per_page", "name": "per_page", "in": "query"}, {"type": "integer", "description": "page", "name": "page", "in": "query"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/middlewares.Response-string"}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/middlewares.Response-middlewares_Empty"}}}}}, "/api/v1/transfers/download/xlsx": {"get": {"security": [{"bearerAuth": []}], "description": "Фильтры находятся в теле запроса", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["transfers"], "summary": "Выгрузка списка трансферов по фильтрам в формате xlsx", "parameters": [{"type": "string", "name": "account_bank_id", "in": "query"}, {"type": "string", "name": "account_number", "in": "query"}, {"type": "string", "name": "balance_owner_bin", "in": "query"}, {"type": "string", "name": "balance_owner_id", "in": "query"}, {"type": "string", "name": "created_at_from", "in": "query"}, {"type": "string", "name": "created_at_to", "in": "query"}, {"type": "string", "name": "language", "in": "query", "required": true}, {"type": "number", "name": "transfer_amount_from", "in": "query"}, {"type": "number", "name": "transfer_amount_to", "in": "query"}, {"type": "integer", "name": "transfer_id", "in": "query"}, {"type": "string", "name": "transfer_status_id", "in": "query"}, {"type": "string", "name": "transfer_type_id", "in": "query"}, {"type": "integer", "description": "per_page", "name": "per_page", "in": "query"}, {"type": "integer", "description": "page", "name": "page", "in": "query"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/middlewares.Response-string"}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/middlewares.Response-middlewares_Empty"}}}}}, "/api/v1/transfers/rule/customize": {"get": {"security": [{"bearerAuth": []}], "description": "Используется для получения полей которые нужно отобразить для пользователя", "produces": ["application/json"], "tags": ["transfer-rules"], "summary": "Получение сохранённых полей в правилах трансферов", "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/middlewares.Response-model_Field"}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/middlewares.Response-middlewares_Empty"}}}}, "put": {"security": [{"bearerAuth": []}], "description": "Используется для обновление полей которые юзер бы хотел видеть у себя", "produces": ["application/json"], "tags": ["transfer-rules"], "summary": "Обновление полей пользователя по трансферам", "parameters": [{"description": "Field Data", "name": "data", "in": "body", "required": true, "schema": {"$ref": "#/definitions/delivery_http_v1.updateTransferCustomization.request"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/middlewares.Response-string"}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/middlewares.Response-middlewares_Empty"}}}}}}, "definitions": {"datatypes.JSONMap": {"type": "object", "additionalProperties": true}, "delivery_http_v1.createOrUpdateBalanceHistoriesUserFields.request": {"type": "object", "required": ["fields"], "properties": {"fields": {"type": "array", "uniqueItems": true, "items": {"$ref": "#/definitions/model.FieldList"}}}}, "delivery_http_v1.createOrUpdatePaymentOrdersCustomization.request": {"type": "object", "required": ["fields"], "properties": {"fields": {"type": "array", "uniqueItems": true, "items": {"$ref": "#/definitions/model.FieldList"}}}}, "delivery_http_v1.createOrUpdateSplittingSettingCustomization.request": {"type": "object", "required": ["fields"], "properties": {"fields": {"type": "array", "uniqueItems": true, "items": {"$ref": "#/definitions/model.FieldList"}}}}, "delivery_http_v1.createOrUpdateUserFields.request": {"type": "object", "required": ["fields"], "properties": {"fields": {"type": "array", "uniqueItems": true, "items": {"$ref": "#/definitions/model.FieldList"}}}}, "delivery_http_v1.getBalanceHistories.response": {"type": "object", "properties": {"balance_histories": {"type": "array", "items": {"$ref": "#/definitions/delivery_http_v1.historyResponse"}}, "total_info": {"$ref": "#/definitions/delivery_http_v1.totalInfo"}}}, "delivery_http_v1.getTransferDetails.response": {"type": "object", "properties": {"amount": {"type": "number"}, "created_at": {"type": "string"}, "external_project_id": {"type": "string"}, "processor_name": {"type": "string"}, "row_operation_id": {"type": "integer"}}}, "delivery_http_v1.getTransfers.data": {"type": "object", "properties": {"account": {"type": "object", "properties": {"account_bank_name": {"type": "string"}, "account_number": {"type": "string"}}}, "balance_owner": {"type": "object", "properties": {"balance_owner_bin": {"type": "string"}, "balance_owner_id": {"type": "integer"}, "balance_owner_name": {"type": "string"}}}, "transfer": {"type": "object", "properties": {"beneficiary_code": {"type": "string"}, "created_at": {"type": "string"}, "description": {"type": "string"}, "finished_at": {"type": "string"}, "payment_purpose_code": {"type": "string"}, "recipient_account": {"type": "string"}, "transfer_amount": {"type": "number"}, "transfer_id": {"type": "integer"}, "transfer_status_code": {"type": "string"}, "transfer_type_code": {"type": "string"}}}}}, "delivery_http_v1.historyResponse": {"type": "object", "required": ["account", "balance_calculated_date", "balance_main_amount", "balance_minimal_amount", "balance_owner", "merchant"], "properties": {"account": {"type": "object", "required": ["account_bank_name", "account_number"], "properties": {"account_bank_name": {"type": "string"}, "account_number": {"type": "string"}}}, "balance_calculated_date": {"type": "string"}, "balance_main_amount": {"type": "number"}, "balance_minimal_amount": {"type": "number"}, "balance_owner": {"type": "object", "required": ["balance_owner_bin", "balance_owner_earn_type_code", "balance_owner_id", "balance_owner_name", "balance_owner_status_code"], "properties": {"balance_owner_bin": {"type": "string"}, "balance_owner_earn_type_code": {"type": "string"}, "balance_owner_id": {"type": "integer"}, "balance_owner_name": {"type": "string"}, "balance_owner_status_code": {"type": "string"}}}, "merchant": {"type": "object", "required": ["merchant_id", "merchant_name"], "properties": {"merchant_id": {"type": "integer"}, "merchant_name": {"type": "string"}}}}}, "delivery_http_v1.splittingSettingsResponse": {"type": "object", "required": ["merchant", "payer_project", "recipient_project"], "properties": {"merchant": {"type": "object", "required": ["merchant_id", "merchant_name"], "properties": {"merchant_id": {"type": "integer"}, "merchant_name": {"type": "string"}}}, "payer_project": {"type": "object", "required": ["payer_project_id", "payer_project_name", "payer_tax_percentage"], "properties": {"payer_project_id": {"type": "integer"}, "payer_project_name": {"type": "string"}, "payer_tax_percentage": {"type": "number"}}}, "recipient_project": {"type": "object", "required": ["recipient_project_id", "recipient_project_name"], "properties": {"recipient_project_id": {"type": "integer"}, "recipient_project_name": {"type": "string"}}}}}, "delivery_http_v1.totalInfo": {"type": "object", "required": ["total_amount", "total_histories"], "properties": {"total_amount": {"type": "number"}, "total_histories": {"type": "integer"}}}, "delivery_http_v1.updateTransferCustomization.request": {"type": "object", "required": ["fields"], "properties": {"fields": {"type": "array", "uniqueItems": true, "items": {"$ref": "#/definitions/model.FieldList"}}}}, "middlewares.Empty": {"type": "object"}, "middlewares.Response-array_delivery_http_v1_getTransferDetails_response": {"type": "object", "required": ["message", "result", "status"], "properties": {"message": {"type": "string"}, "result": {"type": "array", "items": {"$ref": "#/definitions/delivery_http_v1.getTransferDetails.response"}}, "status": {"type": "boolean"}}}, "middlewares.Response-array_delivery_http_v1_getTransfers_data": {"type": "object", "required": ["message", "result", "status"], "properties": {"message": {"type": "string"}, "result": {"type": "array", "items": {"$ref": "#/definitions/delivery_http_v1.getTransfers.data"}}, "status": {"type": "boolean"}}}, "middlewares.Response-array_delivery_http_v1_splittingSettingsResponse": {"type": "object", "required": ["message", "result", "status"], "properties": {"message": {"type": "string"}, "result": {"type": "array", "items": {"$ref": "#/definitions/delivery_http_v1.splittingSettingsResponse"}}, "status": {"type": "boolean"}}}, "middlewares.Response-array_model_AcquirerCommission": {"type": "object", "required": ["message", "result", "status"], "properties": {"message": {"type": "string"}, "result": {"type": "array", "items": {"$ref": "#/definitions/model.AcquirerCommission"}}, "status": {"type": "boolean"}}}, "middlewares.Response-array_model_ProjectLowerCommission": {"type": "object", "required": ["message", "result", "status"], "properties": {"message": {"type": "string"}, "result": {"type": "array", "items": {"$ref": "#/definitions/model.ProjectLowerCommission"}}, "status": {"type": "boolean"}}}, "middlewares.Response-array_model_ProjectUpperCommission": {"type": "object", "required": ["message", "result", "status"], "properties": {"message": {"type": "string"}, "result": {"type": "array", "items": {"$ref": "#/definitions/model.ProjectUpperCommission"}}, "status": {"type": "boolean"}}}, "middlewares.Response-array_model_Terminal": {"type": "object", "required": ["message", "result", "status"], "properties": {"message": {"type": "string"}, "result": {"type": "array", "items": {"$ref": "#/definitions/model.Terminal"}}, "status": {"type": "boolean"}}}, "middlewares.Response-array_schema_ClientOperationResponse": {"type": "object", "required": ["message", "result", "status"], "properties": {"message": {"type": "string"}, "result": {"type": "array", "items": {"$ref": "#/definitions/schema.ClientOperationResponse"}}, "status": {"type": "boolean"}}}, "middlewares.Response-array_schema_OverdraftOperationResponse": {"type": "object", "required": ["message", "result", "status"], "properties": {"message": {"type": "string"}, "result": {"type": "array", "items": {"$ref": "#/definitions/schema.OverdraftOperationResponse"}}, "status": {"type": "boolean"}}}, "middlewares.Response-array_schema_PaymentOrderResponse": {"type": "object", "required": ["message", "result", "status"], "properties": {"message": {"type": "string"}, "result": {"type": "array", "items": {"$ref": "#/definitions/schema.PaymentOrderResponse"}}, "status": {"type": "boolean"}}}, "middlewares.Response-array_schema_RuleResponse": {"type": "object", "required": ["message", "result", "status"], "properties": {"message": {"type": "string"}, "result": {"type": "array", "items": {"$ref": "#/definitions/schema.RuleResponse"}}, "status": {"type": "boolean"}}}, "middlewares.Response-array_schema_Transaction": {"type": "object", "required": ["message", "result", "status"], "properties": {"message": {"type": "string"}, "result": {"type": "array", "items": {"$ref": "#/definitions/schema.Transaction"}}, "status": {"type": "boolean"}}}, "middlewares.Response-array_schema_TransferRuleResponse": {"type": "object", "required": ["message", "result", "status"], "properties": {"message": {"type": "string"}, "result": {"type": "array", "items": {"$ref": "#/definitions/schema.TransferRuleResponse"}}, "status": {"type": "boolean"}}}, "middlewares.Response-array_schema_UserAmountResponse": {"type": "object", "required": ["message", "result", "status"], "properties": {"message": {"type": "string"}, "result": {"type": "array", "items": {"$ref": "#/definitions/schema.UserAmountResponse"}}, "status": {"type": "boolean"}}}, "middlewares.Response-delivery_http_v1_getBalanceHistories_response": {"type": "object", "required": ["message", "result", "status"], "properties": {"message": {"type": "string"}, "result": {"$ref": "#/definitions/delivery_http_v1.getBalanceHistories.response"}, "status": {"type": "boolean"}}}, "middlewares.Response-middlewares_Empty": {"type": "object", "required": ["message", "result", "status"], "properties": {"message": {"type": "string"}, "result": {"$ref": "#/definitions/middlewares.Empty"}, "status": {"type": "boolean"}}}, "middlewares.Response-model_Field": {"type": "object", "required": ["message", "result", "status"], "properties": {"message": {"type": "string"}, "result": {"$ref": "#/definitions/model.Field"}, "status": {"type": "boolean"}}}, "middlewares.Response-model_Transactions": {"type": "object", "required": ["message", "result", "status"], "properties": {"message": {"type": "string"}, "result": {"type": "array", "items": {"$ref": "#/definitions/model.Transaction"}}, "status": {"type": "boolean"}}}, "middlewares.Response-schema_AggregatedInfoResponse": {"type": "object", "required": ["message", "result", "status"], "properties": {"message": {"type": "string"}, "result": {"$ref": "#/definitions/schema.AggregatedInfoResponse"}, "status": {"type": "boolean"}}}, "middlewares.Response-schema_RuleResponse": {"type": "object", "required": ["message", "result", "status"], "properties": {"message": {"type": "string"}, "result": {"$ref": "#/definitions/schema.RuleResponse"}, "status": {"type": "boolean"}}}, "middlewares.Response-string": {"type": "object", "required": ["message", "result", "status"], "properties": {"message": {"type": "string"}, "result": {"type": "string"}, "status": {"type": "boolean"}}}, "model.AcquirerCommission": {"type": "object", "required": ["acquirer_id", "acquirer_name", "acquirer_option_id", "aggregated_type_id", "aggregated_type_name", "merchant_id", "merchant_name", "project_id", "project_name"], "properties": {"acquirer_id": {"type": "integer"}, "acquirer_name": {"type": "string"}, "acquirer_option_id": {"type": "integer"}, "aggregated_type_id": {"type": "integer"}, "aggregated_type_name": {"type": "string"}, "commission_id": {"type": "integer"}, "commission_percentage": {"type": "number"}, "country_id": {"type": "integer"}, "country_name": {"type": "string"}, "end_date": {"type": "string"}, "fixed_amount": {"type": "number"}, "ips_id": {"type": "integer"}, "ips_name": {"type": "string"}, "issuer_id": {"type": "integer"}, "issuer_name": {"type": "string"}, "last_transaction_date": {"type": "string"}, "merchant_id": {"type": "integer"}, "merchant_name": {"type": "string"}, "min_amount_commission": {"type": "number"}, "project_id": {"type": "integer"}, "project_name": {"type": "string"}, "start_date": {"type": "string"}}}, "model.Field": {"type": "object", "required": ["fields", "id", "source", "user_id"], "properties": {"fields": {"description": "ReportID identity of report.reports these fields will used for", "type": "array", "items": {"$ref": "#/definitions/model.FieldList"}}, "id": {"type": "integer"}, "source": {"description": "Source defines url path from which fields are requested", "type": "string"}, "user_id": {"description": "UserID identity of auth.user these fields will be bound to", "type": "integer"}}}, "model.FieldList": {"type": "object", "required": ["id", "is_required", "is_visible", "sequence"], "properties": {"id": {"description": "ID unique field code ex: transaction_id", "type": "string"}, "is_required": {"description": "IsRequired pointer is used because validator won't pass if value is false", "type": "boolean"}, "is_visible": {"description": "IsVisible pointer is used because validator won't pass if value is false", "type": "boolean"}, "sequence": {"description": "Sequence is used to order this field", "type": "integer"}}}, "model.ProjectLowerCommission": {"type": "object", "required": ["acquirer_name", "aggregated_type_name", "merchant_name", "project_lower_option_type_id", "project_lower_option_type_name", "project_name", "project_option_id"], "properties": {"acquirer_id": {"type": "integer"}, "acquirer_name": {"type": "string"}, "aggregated_type_id": {"type": "integer"}, "aggregated_type_name": {"type": "string"}, "commission_id": {"type": "integer"}, "commission_percentage": {"type": "number"}, "country_id": {"type": "integer"}, "country_name": {"type": "string"}, "end_date": {"type": "string"}, "fixed_amount": {"type": "number"}, "ips_id": {"type": "integer"}, "ips_name": {"type": "string"}, "issuer_id": {"type": "integer"}, "issuer_name": {"type": "string"}, "last_transaction_date": {"type": "string"}, "merchant_id": {"type": "integer"}, "merchant_name": {"type": "string"}, "min_amount_commission": {"type": "number"}, "project_id": {"type": "integer"}, "project_lower_option_type_id": {"type": "integer"}, "project_lower_option_type_name": {"type": "string"}, "project_name": {"type": "string"}, "project_option_id": {"type": "integer"}, "start_date": {"type": "string"}}}, "model.ProjectUpperCommission": {"type": "object", "required": ["acquirer_name", "aggregated_type_name", "amount", "merchant_id", "merchant_name", "project_id", "project_name", "project_option_id", "sign"], "properties": {"acquirer_name": {"type": "string"}, "aggregated_type_id": {"type": "integer"}, "aggregated_type_name": {"type": "string"}, "amount": {"type": "number"}, "commission_id": {"type": "integer"}, "commission_percentage": {"type": "number"}, "end_date": {"type": "string"}, "fixed_amount": {"type": "number"}, "last_transaction_date": {"type": "string"}, "merchant_id": {"type": "integer"}, "merchant_name": {"type": "string"}, "min_amount_commission": {"type": "number"}, "project_id": {"type": "integer"}, "project_name": {"type": "string"}, "project_option_id": {"type": "integer"}, "sign": {"type": "string"}, "start_date": {"type": "string"}}}, "model.Terminal": {"type": "object", "properties": {"acquirer_id": {"type": "integer"}, "acquirer_name": {"type": "string"}, "acquirer_terminal_name": {"type": "string"}, "description": {"type": "string"}, "id": {"type": "integer"}, "project": {"type": "string"}, "status": {"type": "integer"}, "terminal_bank_id": {"type": "integer"}, "transaction_type_name": {"type": "string"}, "transit": {"type": "boolean"}, "transit_account_bank": {"type": "string"}, "transit_account_number": {"type": "string"}, "two_stage_payment_timeout": {"type": "integer"}}}, "model.Transaction": {"type": "object", "properties": {"acquirer": {"type": "string"}, "acquirer_code": {"type": "string"}, "acquirer_commission_amount": {"type": "number"}, "acquirer_id": {"type": "integer"}, "additional_data": {"$ref": "#/definitions/datatypes.JSONMap"}, "amount": {"type": "number"}, "callback_status": {"type": "integer"}, "callback_url": {"type": "string"}, "created_date": {"type": "string"}, "end_date": {"type": "string"}, "issuer": {"type": "string"}, "issuer_id": {"type": "integer"}, "last_refund_date": {"type": "string"}, "lower_commission": {"type": "number"}, "masked_pan": {"type": "string"}, "merchant": {"type": "string"}, "merchant_bin": {"type": "string"}, "merchant_id": {"type": "integer"}, "order_number": {"type": "string"}, "project_client_id": {"type": "string"}, "project_id": {"type": "integer"}, "project_name": {"type": "string"}, "refund_count": {"type": "integer"}, "refund_total_amount": {"type": "number"}, "rrn": {"type": "string"}, "transaction_id": {"type": "integer"}, "transaction_status": {"type": "string"}, "transaction_status_id": {"type": "integer"}, "transaction_type": {"type": "string"}, "transaction_type_id": {"type": "integer"}, "updated_date": {"type": "string"}, "upper_commission": {"type": "number"}}}, "schema.Account": {"type": "object", "required": ["account_bank_name", "account_number"], "properties": {"account_bank_name": {"type": "string"}, "account_number": {"type": "string"}}}, "schema.AggregatedInfoResponse": {"type": "object", "properties": {"total_amount": {"type": "number"}, "total_count": {"type": "integer"}}}, "schema.BalanceOwner": {"type": "object", "required": ["balance_owner_bin", "balance_owner_id", "balance_owner_name"], "properties": {"balance_owner_bin": {"type": "string"}, "balance_owner_id": {"type": "integer"}, "balance_owner_name": {"type": "string"}}}, "schema.BalanceOwnerTr": {"type": "object", "properties": {"balance_country_code": {"type": "string"}, "balance_owner_ID": {"type": "integer"}, "balance_owner_bin": {"type": "string"}, "balance_owner_name": {"type": "string"}, "balance_owner_tag": {"type": "string"}}}, "schema.ClientAccount": {"type": "object", "required": ["account_bank_name", "account_number"], "properties": {"account_bank_name": {"type": "string"}, "account_number": {"type": "string"}}}, "schema.ClientBalanceOwner": {"type": "object", "required": ["balance_owner_bin", "balance_owner_id", "balance_owner_name"], "properties": {"balance_owner_bin": {"type": "string"}, "balance_owner_id": {"type": "integer"}, "balance_owner_name": {"type": "string"}}}, "schema.ClientMerchant": {"type": "object", "required": ["merchant_id", "merchant_name"], "properties": {"merchant_id": {"type": "integer"}, "merchant_name": {"type": "string"}}}, "schema.ClientOperation": {"type": "object", "required": ["operation_amount", "operation_date", "operation_id", "operation_status_code", "operation_type_code"], "properties": {"operation_amount": {"type": "number"}, "operation_date": {"type": "string"}, "operation_id": {"type": "integer"}, "operation_status_code": {"type": "string"}, "operation_type_code": {"type": "string"}}}, "schema.ClientOperationResponse": {"type": "object", "required": ["balance_type_code", "transaction_id"], "properties": {"account": {"$ref": "#/definitions/schema.ClientAccount"}, "balance_owner": {"$ref": "#/definitions/schema.ClientBalanceOwner"}, "balance_type_code": {"type": "string"}, "merchant": {"$ref": "#/definitions/schema.ClientMerchant"}, "operation": {"$ref": "#/definitions/schema.ClientOperation"}, "transaction_id": {"type": "integer"}}}, "schema.Merchant": {"type": "object", "required": ["merchant_id", "merchant_name"], "properties": {"merchant_id": {"type": "integer"}, "merchant_name": {"type": "string"}}}, "schema.Operation": {"type": "object", "required": ["created_at", "finished_at", "operation_amount", "operation_id", "operation_type_code"], "properties": {"created_at": {"type": "string"}, "finished_at": {"type": "string"}, "operation_amount": {"type": "number"}, "operation_id": {"type": "integer"}, "operation_type_code": {"type": "string"}}}, "schema.Overdraft": {"type": "object", "properties": {"overdraft_amount": {"type": "number"}, "overdraft_expired_at": {"type": "string"}, "overdraft_started_at": {"type": "string"}}}, "schema.OverdraftBalanceOwner": {"type": "object", "properties": {"balance_owner_bin": {"type": "string"}, "balance_owner_earn_type_code": {"type": "string"}, "balance_owner_id": {"type": "integer"}, "balance_owner_name": {"type": "string"}, "balance_owner_status_code": {"type": "string"}}}, "schema.OverdraftOperationResponse": {"type": "object", "properties": {"account": {"$ref": "#/definitions/schema.Account"}, "balance_calculated_date": {"type": "string"}, "balance_credit_amount": {"type": "number"}, "balance_owner": {"$ref": "#/definitions/schema.OverdraftBalanceOwner"}, "merchant": {"$ref": "#/definitions/schema.Merchant"}, "overdraft": {"$ref": "#/definitions/schema.Overdraft"}}}, "schema.PaymentOrderAccount": {"type": "object", "properties": {"bank_id": {"type": "integer"}, "bank_name": {"type": "string"}, "id": {"type": "integer"}, "number": {"type": "string"}}}, "schema.PaymentOrderBalanceOwner": {"type": "object", "properties": {"bin": {"type": "string"}, "country": {"type": "string"}, "id": {"type": "integer"}, "name": {"type": "string"}, "tag": {"type": "string"}}}, "schema.PaymentOrderResp": {"type": "object", "properties": {"date": {"type": "string"}, "end_date": {"type": "string"}, "id": {"type": "integer"}, "start_date": {"type": "string"}, "status_id": {"type": "integer"}, "status_name": {"type": "string"}}}, "schema.PaymentOrderResponse": {"type": "object", "properties": {"account": {"$ref": "#/definitions/schema.PaymentOrderAccount"}, "balance_owner": {"$ref": "#/definitions/schema.PaymentOrderBalanceOwner"}, "payment_order": {"$ref": "#/definitions/schema.PaymentOrderResp"}}}, "schema.RuleResponse": {"type": "object", "properties": {"amount_from": {"type": "number"}, "amount_to": {"type": "number"}, "bank_id": {"type": "integer"}, "bank_name": {"type": "string"}, "country_id": {"type": "integer"}, "country_name": {"type": "string"}, "created_at": {"type": "string"}, "id": {"type": "integer"}, "ips_id": {"type": "integer"}, "ips_name": {"type": "string"}, "is_base": {"type": "boolean"}, "project_id": {"type": "integer"}, "project_name": {"type": "string"}, "status": {"type": "boolean"}, "transaction_type_name": {"type": "string"}, "updated_at": {"type": "string"}, "weight": {"type": "integer"}}}, "schema.RuleTr": {"type": "object", "properties": {"created_at": {"type": "string"}, "is_active": {"type": "boolean"}, "rule_id": {"type": "integer"}}}, "schema.Transaction": {"type": "object", "properties": {"account": {"$ref": "#/definitions/schema.Account"}, "balance_owner": {"$ref": "#/definitions/schema.BalanceOwner"}, "merchant": {"$ref": "#/definitions/schema.Merchant"}, "operation": {"$ref": "#/definitions/schema.Operation"}, "transfer": {"$ref": "#/definitions/schema.Transfer"}}}, "schema.Transfer": {"type": "object", "required": ["beneficiary_code", "description", "payment_purpose_code", "recipient_account", "transfer_id"], "properties": {"beneficiary_code": {"type": "string"}, "description": {"type": "string"}, "payment_purpose_code": {"type": "string"}, "recipient_account": {"type": "string"}, "transfer_id": {"type": "integer"}}}, "schema.TransferRuleResponse": {"type": "object", "properties": {"balance_owner": {"$ref": "#/definitions/schema.BalanceOwnerTr"}, "rule": {"$ref": "#/definitions/schema.RuleTr"}, "transfer": {"$ref": "#/definitions/schema.TransferTr"}, "transit_account": {"$ref": "#/definitions/schema.TransitAccountTr"}}}, "schema.TransferTr": {"type": "object", "properties": {"amount": {"type": "number"}, "balance_main_total_withdrawals": {"type": "boolean"}, "beneficiary_code": {"type": "string"}, "description": {"type": "string"}, "frequency": {"type": "string"}, "payment_purpose_code": {"type": "string"}, "recipient_account": {"type": "string"}}}, "schema.TransitAccountTr": {"type": "object", "properties": {"account_bank_name": {"type": "string"}, "account_number": {"type": "string"}}}, "schema.UserAmountResponse": {"type": "object", "properties": {"id": {"type": "integer"}, "merchant_name": {"type": "string"}, "project_client_amount_max": {"type": "number"}, "project_client_amount_sum": {"type": "number"}, "project_client_id": {"type": "string"}, "project_name": {"type": "string"}, "transaction_status_name": {"type": "string"}, "transaction_type_name": {"type": "string"}, "verification_user_id": {"type": "integer"}}}}, "securityDefinitions": {"bearerAuth": {"type": "<PERSON><PERSON><PERSON><PERSON>", "name": "Authorization", "in": "header"}}}