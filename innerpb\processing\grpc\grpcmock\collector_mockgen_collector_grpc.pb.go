// Code generated by MockGen. DO NOT EDIT.
// Source: collector_grpc.pb.go

// Package grpcmock is a generated GoMock package.
package grpcmock

import (
	context "context"
	reflect "reflect"

	grpc "git.local/sensitive/innerpb/processing/grpc"
	gomock "github.com/golang/mock/gomock"
	grpc0 "google.golang.org/grpc"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
)

// MockCollectorClient is a mock of CollectorClient interface.
type MockCollectorClient struct {
	ctrl     *gomock.Controller
	recorder *MockCollectorClientMockRecorder
}

// MockCollectorClientMockRecorder is the mock recorder for MockCollectorClient.
type MockCollectorClientMockRecorder struct {
	mock *MockCollectorClient
}

// NewMockCollectorClient creates a new mock instance.
func NewMockCollectorClient(ctrl *gomock.Controller) *MockCollectorClient {
	mock := &MockCollectorClient{ctrl: ctrl}
	mock.recorder = &MockCollectorClientMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockCollectorClient) EXPECT() *MockCollectorClientMockRecorder {
	return m.recorder
}

// CollectTransaction mocks base method.
func (m *MockCollectorClient) CollectTransaction(ctx context.Context, in *grpc.CollectTransactionRequestV1, opts ...grpc0.CallOption) (*grpc.CollectorEmptyResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "CollectTransaction", varargs...)
	ret0, _ := ret[0].(*grpc.CollectorEmptyResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CollectTransaction indicates an expected call of CollectTransaction.
func (mr *MockCollectorClientMockRecorder) CollectTransaction(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CollectTransaction", reflect.TypeOf((*MockCollectorClient)(nil).CollectTransaction), varargs...)
}

// GetTransactionStatus mocks base method.
func (m *MockCollectorClient) GetTransactionStatus(ctx context.Context, in *emptypb.Empty, opts ...grpc0.CallOption) (*emptypb.Empty, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetTransactionStatus", varargs...)
	ret0, _ := ret[0].(*emptypb.Empty)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetTransactionStatus indicates an expected call of GetTransactionStatus.
func (mr *MockCollectorClientMockRecorder) GetTransactionStatus(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetTransactionStatus", reflect.TypeOf((*MockCollectorClient)(nil).GetTransactionStatus), varargs...)
}

// MockCollectorServer is a mock of CollectorServer interface.
type MockCollectorServer struct {
	ctrl     *gomock.Controller
	recorder *MockCollectorServerMockRecorder
}

// MockCollectorServerMockRecorder is the mock recorder for MockCollectorServer.
type MockCollectorServerMockRecorder struct {
	mock *MockCollectorServer
}

// NewMockCollectorServer creates a new mock instance.
func NewMockCollectorServer(ctrl *gomock.Controller) *MockCollectorServer {
	mock := &MockCollectorServer{ctrl: ctrl}
	mock.recorder = &MockCollectorServerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockCollectorServer) EXPECT() *MockCollectorServerMockRecorder {
	return m.recorder
}

// CollectTransaction mocks base method.
func (m *MockCollectorServer) CollectTransaction(arg0 context.Context, arg1 *grpc.CollectTransactionRequestV1) (*grpc.CollectorEmptyResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CollectTransaction", arg0, arg1)
	ret0, _ := ret[0].(*grpc.CollectorEmptyResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CollectTransaction indicates an expected call of CollectTransaction.
func (mr *MockCollectorServerMockRecorder) CollectTransaction(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CollectTransaction", reflect.TypeOf((*MockCollectorServer)(nil).CollectTransaction), arg0, arg1)
}

// GetTransactionStatus mocks base method.
func (m *MockCollectorServer) GetTransactionStatus(arg0 context.Context, arg1 *emptypb.Empty) (*emptypb.Empty, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetTransactionStatus", arg0, arg1)
	ret0, _ := ret[0].(*emptypb.Empty)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetTransactionStatus indicates an expected call of GetTransactionStatus.
func (mr *MockCollectorServerMockRecorder) GetTransactionStatus(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetTransactionStatus", reflect.TypeOf((*MockCollectorServer)(nil).GetTransactionStatus), arg0, arg1)
}

// mustEmbedUnimplementedCollectorServer mocks base method.
func (m *MockCollectorServer) mustEmbedUnimplementedCollectorServer() {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "mustEmbedUnimplementedCollectorServer")
}

// mustEmbedUnimplementedCollectorServer indicates an expected call of mustEmbedUnimplementedCollectorServer.
func (mr *MockCollectorServerMockRecorder) mustEmbedUnimplementedCollectorServer() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "mustEmbedUnimplementedCollectorServer", reflect.TypeOf((*MockCollectorServer)(nil).mustEmbedUnimplementedCollectorServer))
}

// MockUnsafeCollectorServer is a mock of UnsafeCollectorServer interface.
type MockUnsafeCollectorServer struct {
	ctrl     *gomock.Controller
	recorder *MockUnsafeCollectorServerMockRecorder
}

// MockUnsafeCollectorServerMockRecorder is the mock recorder for MockUnsafeCollectorServer.
type MockUnsafeCollectorServerMockRecorder struct {
	mock *MockUnsafeCollectorServer
}

// NewMockUnsafeCollectorServer creates a new mock instance.
func NewMockUnsafeCollectorServer(ctrl *gomock.Controller) *MockUnsafeCollectorServer {
	mock := &MockUnsafeCollectorServer{ctrl: ctrl}
	mock.recorder = &MockUnsafeCollectorServerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockUnsafeCollectorServer) EXPECT() *MockUnsafeCollectorServerMockRecorder {
	return m.recorder
}

// mustEmbedUnimplementedCollectorServer mocks base method.
func (m *MockUnsafeCollectorServer) mustEmbedUnimplementedCollectorServer() {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "mustEmbedUnimplementedCollectorServer")
}

// mustEmbedUnimplementedCollectorServer indicates an expected call of mustEmbedUnimplementedCollectorServer.
func (mr *MockUnsafeCollectorServerMockRecorder) mustEmbedUnimplementedCollectorServer() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "mustEmbedUnimplementedCollectorServer", reflect.TypeOf((*MockUnsafeCollectorServer)(nil).mustEmbedUnimplementedCollectorServer))
}
