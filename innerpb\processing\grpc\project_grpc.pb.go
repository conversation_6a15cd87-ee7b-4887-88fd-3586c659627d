// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.5.1
// - protoc             v5.29.3
// source: inner/processing/grpc/project.proto

package grpc

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.64.0 or later.
const _ = grpc.SupportPackageIsVersion9

const (
	Project_GetProjectsByMerchantID_FullMethodName       = "/processing.merchant.project.Project/GetProjectsByMerchantID"
	Project_IsSendEmail_FullMethodName                   = "/processing.merchant.project.Project/IsSendEmail"
	Project_GetProcessingProjectsByBUIDV1_FullMethodName = "/processing.merchant.project.Project/GetProcessingProjectsByBUIDV1"
)

// ProjectClient is the client API for Project service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type ProjectClient interface {
	GetProjectsByMerchantID(ctx context.Context, in *ProjectsRequestV1, opts ...grpc.CallOption) (*ProjectsResponseV1, error)
	IsSendEmail(ctx context.Context, in *ProjectRequestV1, opts ...grpc.CallOption) (*IsSendEmailResponseV1, error)
	GetProcessingProjectsByBUIDV1(ctx context.Context, in *GetProcessingProjectsByBUIDRequestV1, opts ...grpc.CallOption) (*GetProcessingProjectsByBUIDResponseV1, error)
}

type projectClient struct {
	cc grpc.ClientConnInterface
}

func NewProjectClient(cc grpc.ClientConnInterface) ProjectClient {
	return &projectClient{cc}
}

func (c *projectClient) GetProjectsByMerchantID(ctx context.Context, in *ProjectsRequestV1, opts ...grpc.CallOption) (*ProjectsResponseV1, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ProjectsResponseV1)
	err := c.cc.Invoke(ctx, Project_GetProjectsByMerchantID_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *projectClient) IsSendEmail(ctx context.Context, in *ProjectRequestV1, opts ...grpc.CallOption) (*IsSendEmailResponseV1, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(IsSendEmailResponseV1)
	err := c.cc.Invoke(ctx, Project_IsSendEmail_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *projectClient) GetProcessingProjectsByBUIDV1(ctx context.Context, in *GetProcessingProjectsByBUIDRequestV1, opts ...grpc.CallOption) (*GetProcessingProjectsByBUIDResponseV1, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetProcessingProjectsByBUIDResponseV1)
	err := c.cc.Invoke(ctx, Project_GetProcessingProjectsByBUIDV1_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// ProjectServer is the server API for Project service.
// All implementations must embed UnimplementedProjectServer
// for forward compatibility.
type ProjectServer interface {
	GetProjectsByMerchantID(context.Context, *ProjectsRequestV1) (*ProjectsResponseV1, error)
	IsSendEmail(context.Context, *ProjectRequestV1) (*IsSendEmailResponseV1, error)
	GetProcessingProjectsByBUIDV1(context.Context, *GetProcessingProjectsByBUIDRequestV1) (*GetProcessingProjectsByBUIDResponseV1, error)
	mustEmbedUnimplementedProjectServer()
}

// UnimplementedProjectServer must be embedded to have
// forward compatible implementations.
//
// NOTE: this should be embedded by value instead of pointer to avoid a nil
// pointer dereference when methods are called.
type UnimplementedProjectServer struct{}

func (UnimplementedProjectServer) GetProjectsByMerchantID(context.Context, *ProjectsRequestV1) (*ProjectsResponseV1, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetProjectsByMerchantID not implemented")
}
func (UnimplementedProjectServer) IsSendEmail(context.Context, *ProjectRequestV1) (*IsSendEmailResponseV1, error) {
	return nil, status.Errorf(codes.Unimplemented, "method IsSendEmail not implemented")
}
func (UnimplementedProjectServer) GetProcessingProjectsByBUIDV1(context.Context, *GetProcessingProjectsByBUIDRequestV1) (*GetProcessingProjectsByBUIDResponseV1, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetProcessingProjectsByBUIDV1 not implemented")
}
func (UnimplementedProjectServer) mustEmbedUnimplementedProjectServer() {}
func (UnimplementedProjectServer) testEmbeddedByValue()                 {}

// UnsafeProjectServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to ProjectServer will
// result in compilation errors.
type UnsafeProjectServer interface {
	mustEmbedUnimplementedProjectServer()
}

func RegisterProjectServer(s grpc.ServiceRegistrar, srv ProjectServer) {
	// If the following call pancis, it indicates UnimplementedProjectServer was
	// embedded by pointer and is nil.  This will cause panics if an
	// unimplemented method is ever invoked, so we test this at initialization
	// time to prevent it from happening at runtime later due to I/O.
	if t, ok := srv.(interface{ testEmbeddedByValue() }); ok {
		t.testEmbeddedByValue()
	}
	s.RegisterService(&Project_ServiceDesc, srv)
}

func _Project_GetProjectsByMerchantID_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ProjectsRequestV1)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ProjectServer).GetProjectsByMerchantID(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Project_GetProjectsByMerchantID_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ProjectServer).GetProjectsByMerchantID(ctx, req.(*ProjectsRequestV1))
	}
	return interceptor(ctx, in, info, handler)
}

func _Project_IsSendEmail_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ProjectRequestV1)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ProjectServer).IsSendEmail(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Project_IsSendEmail_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ProjectServer).IsSendEmail(ctx, req.(*ProjectRequestV1))
	}
	return interceptor(ctx, in, info, handler)
}

func _Project_GetProcessingProjectsByBUIDV1_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetProcessingProjectsByBUIDRequestV1)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ProjectServer).GetProcessingProjectsByBUIDV1(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Project_GetProcessingProjectsByBUIDV1_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ProjectServer).GetProcessingProjectsByBUIDV1(ctx, req.(*GetProcessingProjectsByBUIDRequestV1))
	}
	return interceptor(ctx, in, info, handler)
}

// Project_ServiceDesc is the grpc.ServiceDesc for Project service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var Project_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "processing.merchant.project.Project",
	HandlerType: (*ProjectServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetProjectsByMerchantID",
			Handler:    _Project_GetProjectsByMerchantID_Handler,
		},
		{
			MethodName: "IsSendEmail",
			Handler:    _Project_IsSendEmail_Handler,
		},
		{
			MethodName: "GetProcessingProjectsByBUIDV1",
			Handler:    _Project_GetProcessingProjectsByBUIDV1_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "inner/processing/grpc/project.proto",
}
