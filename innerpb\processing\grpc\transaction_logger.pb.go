// Code generated by protoc-gen-go-mvp. DO NOT EDIT.
// versions:
// - protoc-gen-go-mvp v2.0.1
// created by mvp authors

package grpc

import (
	context "context"
	fmt "fmt"
	cntx "git.local/sensitive/mvp/pkg/cntx"
	ctxzap "github.com/grpc-ecosystem/go-grpc-middleware/logging/zap/ctxzap"
	zap "go.uber.org/zap"
	grpc "google.golang.org/grpc"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
	structpb "google.golang.org/protobuf/types/known/structpb"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
)

func file_inner_processing_grpc_transaction_proto_string_structpb_Value(
	label string,
	in map[string]*structpb.Value,
) zap.Field {
	var res []zap.Field
	for key, val := range in {
		res = append(res, file_inner_processing_grpc_transaction_proto_message_ValueToZap(fmt.Sprint(key), val))
	}

	return zap.Dict(label, res...)
}

func file_inner_processing_grpc_transaction_proto_string_String(
	label string,
	in map[string]string,
) zap.Field {
	var res []zap.Field
	for key, val := range in {
		res = append(res, zap.Any(fmt.Sprint(key), val))
	}

	return zap.Dict(label, res...)
}

func file_inner_processing_grpc_transaction_proto_string_String_1(
	label string,
	in map[string]string,
) zap.Field {
	var res []zap.Field
	for key, val := range in {
		res = append(res, zap.Any(fmt.Sprint(key), val))
	}

	return zap.Dict(label, res...)
}

func file_inner_processing_grpc_transaction_proto_message_EmptyToZap(
	label string,
	in *emptypb.Empty,
) zap.Field {
	if in == nil {
		return zap.Reflect(label, in)
	}

	return zap.Dict(
		label,
	)
}

func file_inner_processing_grpc_transaction_proto_message_ListValueToZap(
	label string,
	in *structpb.ListValue,
) zap.Field {
	if in == nil {
		return zap.Reflect(label, in)
	}

	return zap.Dict(
		label,
		file_inner_processing_grpc_transaction_proto_message_ValueSliceToZap("Values", in.GetValues()),
	)
}

func file_inner_processing_grpc_transaction_proto_enum_NullValueToZap(
	label string,
	in structpb.NullValue,
) zap.Field {
	str, ok := structpb.NullValue_name[int32(in)]
	if !ok {
		str = fmt.Sprintf("unknown structpb.NullValue value %d", in)
	}

	return zap.Dict(
		label,
		zap.Any("value", structpb.NullValue(in)),
		zap.Any("name", str),
	)
}

func file_inner_processing_grpc_transaction_proto_message_StructToZap(
	label string,
	in *structpb.Struct,
) zap.Field {
	if in == nil {
		return zap.Reflect(label, in)
	}

	return zap.Dict(
		label,
		file_inner_processing_grpc_transaction_proto_string_structpb_Value("Fields", in.GetFields()),
	)
}

func file_inner_processing_grpc_transaction_proto_message_TimestampToZap(
	label string,
	in *timestamppb.Timestamp,
) zap.Field {
	if in == nil {
		return zap.Reflect(label, in)
	}

	return zap.Dict(
		label,
		zap.Any("Seconds", in.GetSeconds()),
		zap.Any("Nanos", in.GetNanos()),
	)
}

func file_inner_processing_grpc_transaction_proto_message_ValueToZap(
	label string,
	in *structpb.Value,
) zap.Field {
	if in == nil {
		return zap.Reflect(label, in)
	}

	return zap.Dict(
		label,
		file_inner_processing_grpc_transaction_proto_enum_NullValueToZap("NullValue", in.GetNullValue()),
		zap.Any("NumberValue", in.GetNumberValue()),
		zap.Any("StringValue", in.GetStringValue()),
		zap.Any("BoolValue", in.GetBoolValue()),
		file_inner_processing_grpc_transaction_proto_message_StructToZap("StructValue", in.GetStructValue()),
		file_inner_processing_grpc_transaction_proto_message_ListValueToZap("ListValue", in.GetListValue()),
	)
}

func file_inner_processing_grpc_transaction_proto_message_ValueSliceToZap(
	label string,
	in []*structpb.Value,
) zap.Field {
	var res []zap.Field
	for index, val := range in {
		res = append(res, file_inner_processing_grpc_transaction_proto_message_ValueToZap(fmt.Sprint(index), val))
	}

	return zap.Dict(label, res...)
}

func file_inner_processing_grpc_transaction_proto_message_BillPayoutRequestToZap(
	label string,
	in *BillPayoutRequest,
) zap.Field {
	if in == nil {
		return zap.Reflect(label, in)
	}

	return zap.Dict(
		label,
		zap.Any("TransactionId", in.GetTransactionId()),
	)
}

func file_inner_processing_grpc_transaction_proto_message_CalculateAndUpdateTransactionAmountRequestToZap(
	label string,
	in *CalculateAndUpdateTransactionAmountRequest,
) zap.Field {
	if in == nil {
		return zap.Reflect(label, in)
	}

	return zap.Dict(
		label,
		zap.Any("TransactionId", in.GetTransactionId()),
	)
}

func file_inner_processing_grpc_transaction_proto_message_CheckTransactionHashRequestV1ToZap(
	label string,
	in *CheckTransactionHashRequestV1,
) zap.Field {
	if in == nil {
		return zap.Reflect(label, in)
	}

	return zap.Dict(
		label,
		zap.Any("TransactionId", in.GetTransactionId()),
		zap.Any("Hash", in.GetHash()),
	)
}

func file_inner_processing_grpc_transaction_proto_message_CreateTransactionByPhoneRequestToZap(
	label string,
	in *CreateTransactionByPhoneRequest,
) zap.Field {
	if in == nil {
		return zap.Reflect(label, in)
	}

	return zap.Dict(
		label,
		zap.Any("Amount", in.GetAmount()),
		zap.Any("CallbackUrl", in.GetCallbackUrl()),
		zap.Any("Description", in.GetDescription()),
		zap.Any("ProjectOrderId", in.GetProjectOrderId()),
		zap.Any("MerchantId", in.GetMerchantId()),
		zap.Any("ProjectClientId", in.GetProjectClientId()),
		zap.Any("ProjectId", in.GetProjectId()),
		zap.Any("ProjectReferenceId", in.GetProjectReferenceId()),
		zap.Any("UserPhone", in.GetUserPhone()),
		zap.Any("Email", in.GetEmail()),
		file_inner_processing_grpc_transaction_proto_string_String("AdditionalData", in.GetAdditionalData()),
		zap.Any("VisaAliasToken", in.GetVisaAliasToken()),
		zap.Any("TerminalId", in.GetTerminalId()),
		zap.Any("AcquirerId", in.GetAcquirerId()),
	)
}

func file_inner_processing_grpc_transaction_proto_message_CreateTransactionByPhoneResponseToZap(
	label string,
	in *CreateTransactionByPhoneResponse,
) zap.Field {
	if in == nil {
		return zap.Reflect(label, in)
	}

	return zap.Dict(
		label,
		zap.Any("TransactionId", in.GetTransactionId()),
		zap.Any("ProjectId", in.GetProjectId()),
		zap.Any("MerchantId", in.GetMerchantId()),
		zap.Any("Amount", in.GetAmount()),
		file_inner_processing_grpc_transaction_proto_message_TimestampToZap("CreatedAt", in.GetCreatedAt()),
		zap.Any("TransactionTypeId", in.GetTransactionTypeId()),
	)
}

func file_inner_processing_grpc_transaction_proto_message_GetByIDWithTypeRequestToZap(
	label string,
	in *GetByIDWithTypeRequest,
) zap.Field {
	if in == nil {
		return zap.Reflect(label, in)
	}

	return zap.Dict(
		label,
		zap.Any("TransactionId", in.GetTransactionId()),
		zap.Any("Code", in.GetCode()),
	)
}

func file_inner_processing_grpc_transaction_proto_message_GetByIDWithTypeResponseToZap(
	label string,
	in *GetByIDWithTypeResponse,
) zap.Field {
	if in == nil {
		return zap.Reflect(label, in)
	}

	return zap.Dict(
		label,
		file_inner_processing_grpc_transaction_proto_message_TimestampToZap("CreatedAt", in.GetCreatedAt()),
		file_inner_processing_grpc_transaction_proto_message_TimestampToZap("UpdatedAt", in.GetUpdatedAt()),
		file_inner_processing_grpc_transaction_proto_message_TimestampToZap("FinishedAt", in.GetFinishedAt()),
		zap.Any("Id", in.GetId()),
		zap.Any("AcquirerId", in.GetAcquirerId()),
		zap.Any("TerminalId", in.GetTerminalId()),
		zap.Any("BankReferenceId", in.GetBankReferenceId()),
		zap.Any("CardId", in.GetCardId()),
		zap.Any("ProjectId", in.GetProjectId()),
		zap.Any("MerchantId", in.GetMerchantId()),
		zap.Any("ProjectReferenceId", in.GetProjectReferenceId()),
		zap.Any("ProjectClientId", in.GetProjectClientId()),
		zap.Any("StatusId", in.GetStatusId()),
		zap.Any("TypeId", in.GetTypeId()),
		zap.Any("Amount", in.GetAmount()),
		zap.Any("Try", in.GetTry()),
		zap.Any("Description", in.GetDescription()),
		zap.Any("TypeCode", in.GetTypeCode()),
		zap.Any("StatusCode", in.GetStatusCode()),
		zap.Any("IsFinalStatus", in.GetIsFinalStatus()),
		file_inner_processing_grpc_transaction_proto_message_TransactionInfoToZap("TransactionInfo", in.GetTransactionInfo()),
		file_inner_processing_grpc_transaction_proto_message_TransactionTypeToZap("TransactionType", in.GetTransactionType()),
		file_inner_processing_grpc_transaction_proto_message_TransactionStatusToZap("TransactionStatus", in.GetTransactionStatus()),
		zap.Any("AggregatedTypeId", in.GetAggregatedTypeId()),
		zap.Any("VisaAliasToken", in.GetVisaAliasToken()),
		zap.Any("CInfo", in.GetCInfo()),
	)
}

func file_inner_processing_grpc_transaction_proto_message_GetPayInTransactionsByPeriodAndAcquirerReqV1ToZap(
	label string,
	in *GetPayInTransactionsByPeriodAndAcquirerReqV1,
) zap.Field {
	if in == nil {
		return zap.Reflect(label, in)
	}

	return zap.Dict(
		label,
		zap.Any("AcquirerId", in.GetAcquirerId()),
		file_inner_processing_grpc_transaction_proto_message_TimestampToZap("StartPeriod", in.GetStartPeriod()),
		file_inner_processing_grpc_transaction_proto_message_TimestampToZap("EndPeriod", in.GetEndPeriod()),
	)
}

func file_inner_processing_grpc_transaction_proto_message_GetTransactionByIDRequestV1ToZap(
	label string,
	in *GetTransactionByIDRequestV1,
) zap.Field {
	if in == nil {
		return zap.Reflect(label, in)
	}

	return zap.Dict(
		label,
		zap.Any("Id", in.GetId()),
	)
}

func file_inner_processing_grpc_transaction_proto_message_GetTransactionTypeByIDRequestV1ToZap(
	label string,
	in *GetTransactionTypeByIDRequestV1,
) zap.Field {
	if in == nil {
		return zap.Reflect(label, in)
	}

	return zap.Dict(
		label,
		zap.Any("TypeId", in.GetTypeId()),
	)
}

func file_inner_processing_grpc_transaction_proto_message_GetTransactionTypeByIDResponseV1ToZap(
	label string,
	in *GetTransactionTypeByIDResponseV1,
) zap.Field {
	if in == nil {
		return zap.Reflect(label, in)
	}

	return zap.Dict(
		label,
		zap.Any("Id", in.GetId()),
		zap.Any("Code", in.GetCode()),
		zap.Any("Name", in.GetName()),
	)
}

func file_inner_processing_grpc_transaction_proto_message_GetTransactionsByCallbackStatusReqV1ToZap(
	label string,
	in *GetTransactionsByCallbackStatusReqV1,
) zap.Field {
	if in == nil {
		return zap.Reflect(label, in)
	}

	return zap.Dict(
		label,
		zap.Any("CallbackStatus", in.GetCallbackStatus()),
		file_inner_processing_grpc_transaction_proto_message_TimestampToZap("StartPeriod", in.GetStartPeriod()),
		file_inner_processing_grpc_transaction_proto_message_TimestampToZap("EndPeriod", in.GetEndPeriod()),
	)
}

func file_inner_processing_grpc_transaction_proto_message_GetTransactionsByFinalStatusAndPeriodWithLimitReqV1ToZap(
	label string,
	in *GetTransactionsByFinalStatusAndPeriodWithLimitReqV1,
) zap.Field {
	if in == nil {
		return zap.Reflect(label, in)
	}

	return zap.Dict(
		label,
		zap.Any("IsFinal", in.GetIsFinal()),
		zap.Any("TransactionLimit", in.GetTransactionLimit()),
		file_inner_processing_grpc_transaction_proto_message_TimestampToZap("StartPeriod", in.GetStartPeriod()),
		file_inner_processing_grpc_transaction_proto_message_TimestampToZap("EndPeriod", in.GetEndPeriod()),
	)
}

func file_inner_processing_grpc_transaction_proto_message_GetTransactionsByProjectInfoRequestV1ToZap(
	label string,
	in *GetTransactionsByProjectInfoRequestV1,
) zap.Field {
	if in == nil {
		return zap.Reflect(label, in)
	}

	return zap.Dict(
		label,
		zap.Any("ProjectId", in.GetProjectId()),
		zap.Any("MerchantId", in.GetMerchantId()),
		file_inner_processing_grpc_transaction_proto_message_TimestampToZap("StartPeriod", in.GetStartPeriod()),
		file_inner_processing_grpc_transaction_proto_message_TimestampToZap("EndPeriod", in.GetEndPeriod()),
	)
}

func file_inner_processing_grpc_transaction_proto_message_GetTransactionsByStatusReqV1ToZap(
	label string,
	in *GetTransactionsByStatusReqV1,
) zap.Field {
	if in == nil {
		return zap.Reflect(label, in)
	}

	return zap.Dict(
		label,
		zap.Any("Status", in.GetStatus()),
		file_inner_processing_grpc_transaction_proto_message_TimestampToZap("StartPeriod", in.GetStartPeriod()),
		file_inner_processing_grpc_transaction_proto_message_TimestampToZap("EndPeriod", in.GetEndPeriod()),
	)
}

func file_inner_processing_grpc_transaction_proto_message_GetTransactionsResponseToZap(
	label string,
	in *GetTransactionsResponse,
) zap.Field {
	if in == nil {
		return zap.Reflect(label, in)
	}

	return zap.Dict(
		label,
		file_inner_processing_grpc_transaction_proto_message_TransactionDataV1SliceToZap("Transactions", in.GetTransactions()),
	)
}

func file_inner_processing_grpc_transaction_proto_message_IncreaseTryCountRequestToZap(
	label string,
	in *IncreaseTryCountRequest,
) zap.Field {
	if in == nil {
		return zap.Reflect(label, in)
	}

	return zap.Dict(
		label,
		zap.Any("TransactionId", in.GetTransactionId()),
		zap.Any("Try", in.GetTry()),
	)
}

func file_inner_processing_grpc_transaction_proto_message_MakeAutoChargeRequestV1ToZap(
	label string,
	in *MakeAutoChargeRequestV1,
) zap.Field {
	if in == nil {
		return zap.Reflect(label, in)
	}

	return zap.Dict(
		label,
		zap.Any("TransactionId", in.GetTransactionId()),
	)
}

func file_inner_processing_grpc_transaction_proto_message_SaveAcquirerResponseRequestToZap(
	label string,
	in *SaveAcquirerResponseRequest,
) zap.Field {
	if in == nil {
		return zap.Reflect(label, in)
	}

	return zap.Dict(
		label,
		zap.Any("TransactionId", in.GetTransactionId()),
		file_inner_processing_grpc_transaction_proto_message_TransactionBankResponseToZap("BankResponse", in.GetBankResponse()),
		zap.Any("BankReferenceId", in.GetBankReferenceId()),
		zap.Any("BankOrderId", in.GetBankOrderId()),
	)
}

func file_inner_processing_grpc_transaction_proto_message_SendRequestToZap(
	label string,
	in *SendRequest,
) zap.Field {
	if in == nil {
		return zap.Reflect(label, in)
	}

	return zap.Dict(
		label,
		zap.Any("TransactionId", in.GetTransactionId()),
	)
}

func file_inner_processing_grpc_transaction_proto_message_SetAdditionalDataRequestToZap(
	label string,
	in *SetAdditionalDataRequest,
) zap.Field {
	if in == nil {
		return zap.Reflect(label, in)
	}

	return zap.Dict(
		label,
		file_inner_processing_grpc_transaction_proto_string_String_1("AdditionalData", in.GetAdditionalData()),
		zap.Any("TransactionId", in.GetTransactionId()),
		file_inner_processing_grpc_transaction_proto_message_TimestampToZap("CreatedAt", in.GetCreatedAt()),
		zap.Any("ProjectId", in.GetProjectId()),
		zap.Any("MerchantId", in.GetMerchantId()),
	)
}

func file_inner_processing_grpc_transaction_proto_message_SetRefundWaitingStatusRequestV1ToZap(
	label string,
	in *SetRefundWaitingStatusRequestV1,
) zap.Field {
	if in == nil {
		return zap.Reflect(label, in)
	}

	return zap.Dict(
		label,
		zap.Any("TransactionId", in.GetTransactionId()),
	)
}

func file_inner_processing_grpc_transaction_proto_message_SetRefundWaitingStatusResponseV1ToZap(
	label string,
	in *SetRefundWaitingStatusResponseV1,
) zap.Field {
	if in == nil {
		return zap.Reflect(label, in)
	}

	return zap.Dict(
		label,
		zap.Any("StatusCode", in.GetStatusCode()),
	)
}

func file_inner_processing_grpc_transaction_proto_message_TransactionBankResponseToZap(
	label string,
	in *TransactionBankResponse,
) zap.Field {
	if in == nil {
		return zap.Reflect(label, in)
	}

	return zap.Dict(
		label,
		zap.Any("Code", in.GetCode()),
		zap.Any("Message", in.GetMessage()),
		zap.Any("IntegrationErrorCode", in.GetIntegrationErrorCode()),
		zap.Any("IntegrationErrorMessage", in.GetIntegrationErrorMessage()),
	)
}

func file_inner_processing_grpc_transaction_proto_message_TransactionDataV1ToZap(
	label string,
	in *TransactionDataV1,
) zap.Field {
	if in == nil {
		return zap.Reflect(label, in)
	}

	return zap.Dict(
		label,
		file_inner_processing_grpc_transaction_proto_message_TimestampToZap("CreatedAt", in.GetCreatedAt()),
		file_inner_processing_grpc_transaction_proto_message_TimestampToZap("UpdatedAt", in.GetUpdatedAt()),
		file_inner_processing_grpc_transaction_proto_message_TimestampToZap("FinishedAt", in.GetFinishedAt()),
		zap.Any("Id", in.GetId()),
		zap.Any("AcquirerId", in.GetAcquirerId()),
		zap.Any("TerminalId", in.GetTerminalId()),
		zap.Any("BankReferenceId", in.GetBankReferenceId()),
		zap.Any("CardId", in.GetCardId()),
		zap.Any("ProjectId", in.GetProjectId()),
		zap.Any("MerchantId", in.GetMerchantId()),
		zap.Any("ProjectReferenceId", in.GetProjectReferenceId()),
		zap.Any("ProjectClientId", in.GetProjectClientId()),
		zap.Any("StatusId", in.GetStatusId()),
		zap.Any("TypeId", in.GetTypeId()),
		zap.Any("Amount", in.GetAmount()),
		zap.Any("Try", in.GetTry()),
		zap.Any("Description", in.GetDescription()),
		zap.Any("TypeCode", in.GetTypeCode()),
		zap.Any("StatusCode", in.GetStatusCode()),
		zap.Any("IsFinalStatus", in.GetIsFinalStatus()),
		file_inner_processing_grpc_transaction_proto_message_TransactionInfoToZap("TransactionInfo", in.GetTransactionInfo()),
		zap.Any("AggregatedTypeId", in.GetAggregatedTypeId()),
	)
}

func file_inner_processing_grpc_transaction_proto_message_TransactionDataV1SliceToZap(
	label string,
	in []*TransactionDataV1,
) zap.Field {
	var res []zap.Field
	for index, val := range in {
		res = append(res, file_inner_processing_grpc_transaction_proto_message_TransactionDataV1ToZap(fmt.Sprint(index), val))
	}

	return zap.Dict(label, res...)
}

func file_inner_processing_grpc_transaction_proto_message_TransactionInfoToZap(
	label string,
	in *TransactionInfo,
) zap.Field {
	if in == nil {
		return zap.Reflect(label, in)
	}

	return zap.Dict(
		label,
		file_inner_processing_grpc_transaction_proto_message_TimestampToZap("CreatedAt", in.GetCreatedAt()),
		file_inner_processing_grpc_transaction_proto_message_TimestampToZap("UpdatedAt", in.GetUpdatedAt()),
		zap.Any("TransactionId", in.GetTransactionId()),
		file_inner_processing_grpc_transaction_proto_message_TimestampToZap("TransactionCreatedAt", in.GetTransactionCreatedAt()),
		zap.Any("CallbackUrl", in.GetCallbackUrl()),
		zap.Any("ConfirmUrl", in.GetConfirmUrl()),
		zap.Any("SuccessRedirectUrl", in.GetSuccessRedirectUrl()),
		zap.Any("FailureRedirectUrl", in.GetFailureRedirectUrl()),
		zap.Any("CallbackStatus", in.GetCallbackStatus()),
		zap.Any("UserPhone", in.GetUserPhone()),
		zap.Any("UserEmail", in.GetUserEmail()),
		zap.Any("Shipment", in.GetShipment()),
		zap.Any("BankOrderId", in.GetBankOrderId()),
		file_inner_processing_grpc_transaction_proto_message_StructToZap("JobsMessage", in.GetJobsMessage()),
	)
}

func file_inner_processing_grpc_transaction_proto_message_TransactionStatusToZap(
	label string,
	in *TransactionStatus,
) zap.Field {
	if in == nil {
		return zap.Reflect(label, in)
	}

	return zap.Dict(
		label,
		file_inner_processing_grpc_transaction_proto_message_TimestampToZap("CreatedAt", in.GetCreatedAt()),
		file_inner_processing_grpc_transaction_proto_message_TimestampToZap("UpdatedAt", in.GetUpdatedAt()),
		zap.Any("Id", in.GetId()),
		zap.Any("Code", in.GetCode()),
		zap.Any("Name", in.GetName()),
		zap.Any("IsFinal", in.GetIsFinal()),
	)
}

func file_inner_processing_grpc_transaction_proto_message_TransactionTypeToZap(
	label string,
	in *TransactionType,
) zap.Field {
	if in == nil {
		return zap.Reflect(label, in)
	}

	return zap.Dict(
		label,
		file_inner_processing_grpc_transaction_proto_message_TimestampToZap("CreatedAt", in.GetCreatedAt()),
		file_inner_processing_grpc_transaction_proto_message_TimestampToZap("UpdatedAt", in.GetUpdatedAt()),
		zap.Any("Id", in.GetId()),
		zap.Any("Code", in.GetCode()),
		zap.Any("Name", in.GetName()),
	)
}

func file_inner_processing_grpc_transaction_proto_message_UpdateCallbackStatusRequestV1ToZap(
	label string,
	in *UpdateCallbackStatusRequestV1,
) zap.Field {
	if in == nil {
		return zap.Reflect(label, in)
	}

	return zap.Dict(
		label,
		zap.Any("TransactionId", in.GetTransactionId()),
		zap.Any("CallbackStatus", in.GetCallbackStatus()),
	)
}

func file_inner_processing_grpc_transaction_proto_message_UpdateStatusRequestToZap(
	label string,
	in *UpdateStatusRequest,
) zap.Field {
	if in == nil {
		return zap.Reflect(label, in)
	}

	return zap.Dict(
		label,
		zap.Any("TransactionId", in.GetTransactionId()),
		zap.Any("TransactionStatus", in.GetTransactionStatus()),
		file_inner_processing_grpc_transaction_proto_message_TimestampToZap("FinishedAt", in.GetFinishedAt()),
	)
}

func file_inner_processing_grpc_transaction_proto_message_UpdateTransactionStatusRequestV1ToZap(
	label string,
	in *UpdateTransactionStatusRequestV1,
) zap.Field {
	if in == nil {
		return zap.Reflect(label, in)
	}

	return zap.Dict(
		label,
		zap.Any("TransactionId", in.GetTransactionId()),
		zap.Any("TransactionStatusId", in.GetTransactionStatusId()),
	)
}

var _ TransactionServer = (*loggedTransactionServer)(nil)

func NewLoggedTransactionServer(srv TransactionServer) TransactionServer {
	return &loggedTransactionServer{srv: srv}
}

type loggedTransactionServer struct {
	UnimplementedTransactionServer

	srv TransactionServer
}

func (s *loggedTransactionServer) UpdateTransactionStatus(
	ctx context.Context,
	request *UpdateTransactionStatusRequestV1,
) (
	response *emptypb.Empty,
	err error,
) {
	label := cntx.Begin(ctx, "TransactionServer_UpdateTransactionStatus")
	defer cntx.End(ctx, label)

	defer func() {
		if err != nil {
			ctxzap.AddFields(ctx, zap.Any(label+"error", err))
			return
		}

		ctxzap.AddFields(
			ctx,
			file_inner_processing_grpc_transaction_proto_message_EmptyToZap(label+"response", response),
		)
	}()

	ctxzap.AddFields(
		ctx,
		file_inner_processing_grpc_transaction_proto_message_UpdateTransactionStatusRequestV1ToZap(label+"request", request),
	)

	response, err = s.srv.UpdateTransactionStatus(ctx, request)

	return
}

func (s *loggedTransactionServer) UpdateCallbackStatus(
	ctx context.Context,
	request *UpdateCallbackStatusRequestV1,
) (
	response *emptypb.Empty,
	err error,
) {
	label := cntx.Begin(ctx, "TransactionServer_UpdateCallbackStatus")
	defer cntx.End(ctx, label)

	defer func() {
		if err != nil {
			ctxzap.AddFields(ctx, zap.Any(label+"error", err))
			return
		}

		ctxzap.AddFields(
			ctx,
			file_inner_processing_grpc_transaction_proto_message_EmptyToZap(label+"response", response),
		)
	}()

	ctxzap.AddFields(
		ctx,
		file_inner_processing_grpc_transaction_proto_message_UpdateCallbackStatusRequestV1ToZap(label+"request", request),
	)

	response, err = s.srv.UpdateCallbackStatus(ctx, request)

	return
}

func (s *loggedTransactionServer) GetTransactionsByPeriodAndStatus(
	ctx context.Context,
	request *GetTransactionsByStatusReqV1,
) (
	response *GetTransactionsResponse,
	err error,
) {
	label := cntx.Begin(ctx, "TransactionServer_GetTransactionsByPeriodAndStatus")
	defer cntx.End(ctx, label)

	defer func() {
		if err != nil {
			ctxzap.AddFields(ctx, zap.Any(label+"error", err))
			return
		}

		ctxzap.AddFields(
			ctx,
			file_inner_processing_grpc_transaction_proto_message_GetTransactionsResponseToZap(label+"response", response),
		)
	}()

	ctxzap.AddFields(
		ctx,
		file_inner_processing_grpc_transaction_proto_message_GetTransactionsByStatusReqV1ToZap(label+"request", request),
	)

	response, err = s.srv.GetTransactionsByPeriodAndStatus(ctx, request)

	return
}

func (s *loggedTransactionServer) GetTransactionsByPeriodAndCallbackStatus(
	ctx context.Context,
	request *GetTransactionsByCallbackStatusReqV1,
) (
	response *GetTransactionsResponse,
	err error,
) {
	label := cntx.Begin(ctx, "TransactionServer_GetTransactionsByPeriodAndCallbackStatus")
	defer cntx.End(ctx, label)

	defer func() {
		if err != nil {
			ctxzap.AddFields(ctx, zap.Any(label+"error", err))
			return
		}

		ctxzap.AddFields(
			ctx,
			file_inner_processing_grpc_transaction_proto_message_GetTransactionsResponseToZap(label+"response", response),
		)
	}()

	ctxzap.AddFields(
		ctx,
		file_inner_processing_grpc_transaction_proto_message_GetTransactionsByCallbackStatusReqV1ToZap(label+"request", request),
	)

	response, err = s.srv.GetTransactionsByPeriodAndCallbackStatus(ctx, request)

	return
}

func (s *loggedTransactionServer) GetTransactionsByFinalStatusAndPeriodWithLimit(
	ctx context.Context,
	request *GetTransactionsByFinalStatusAndPeriodWithLimitReqV1,
) (
	response *GetTransactionsResponse,
	err error,
) {
	label := cntx.Begin(ctx, "TransactionServer_GetTransactionsByFinalStatusAndPeriodWithLimit")
	defer cntx.End(ctx, label)

	defer func() {
		if err != nil {
			ctxzap.AddFields(ctx, zap.Any(label+"error", err))
			return
		}

		ctxzap.AddFields(
			ctx,
			file_inner_processing_grpc_transaction_proto_message_GetTransactionsResponseToZap(label+"response", response),
		)
	}()

	ctxzap.AddFields(
		ctx,
		file_inner_processing_grpc_transaction_proto_message_GetTransactionsByFinalStatusAndPeriodWithLimitReqV1ToZap(label+"request", request),
	)

	response, err = s.srv.GetTransactionsByFinalStatusAndPeriodWithLimit(ctx, request)

	return
}

func (s *loggedTransactionServer) GetPayInTransactionsByPeriodAndAcquirer(
	ctx context.Context,
	request *GetPayInTransactionsByPeriodAndAcquirerReqV1,
) (
	response *GetTransactionsResponse,
	err error,
) {
	label := cntx.Begin(ctx, "TransactionServer_GetPayInTransactionsByPeriodAndAcquirer")
	defer cntx.End(ctx, label)

	defer func() {
		if err != nil {
			ctxzap.AddFields(ctx, zap.Any(label+"error", err))
			return
		}

		ctxzap.AddFields(
			ctx,
			file_inner_processing_grpc_transaction_proto_message_GetTransactionsResponseToZap(label+"response", response),
		)
	}()

	ctxzap.AddFields(
		ctx,
		file_inner_processing_grpc_transaction_proto_message_GetPayInTransactionsByPeriodAndAcquirerReqV1ToZap(label+"request", request),
	)

	response, err = s.srv.GetPayInTransactionsByPeriodAndAcquirer(ctx, request)

	return
}

func (s *loggedTransactionServer) GetTransactionTypeByID(
	ctx context.Context,
	request *GetTransactionTypeByIDRequestV1,
) (
	response *GetTransactionTypeByIDResponseV1,
	err error,
) {
	label := cntx.Begin(ctx, "TransactionServer_GetTransactionTypeByID")
	defer cntx.End(ctx, label)

	defer func() {
		if err != nil {
			ctxzap.AddFields(ctx, zap.Any(label+"error", err))
			return
		}

		ctxzap.AddFields(
			ctx,
			file_inner_processing_grpc_transaction_proto_message_GetTransactionTypeByIDResponseV1ToZap(label+"response", response),
		)
	}()

	ctxzap.AddFields(
		ctx,
		file_inner_processing_grpc_transaction_proto_message_GetTransactionTypeByIDRequestV1ToZap(label+"request", request),
	)

	response, err = s.srv.GetTransactionTypeByID(ctx, request)

	return
}

func (s *loggedTransactionServer) GetTransactionByID(
	ctx context.Context,
	request *GetTransactionByIDRequestV1,
) (
	response *TransactionDataV1,
	err error,
) {
	label := cntx.Begin(ctx, "TransactionServer_GetTransactionByID")
	defer cntx.End(ctx, label)

	defer func() {
		if err != nil {
			ctxzap.AddFields(ctx, zap.Any(label+"error", err))
			return
		}

		ctxzap.AddFields(
			ctx,
			file_inner_processing_grpc_transaction_proto_message_TransactionDataV1ToZap(label+"response", response),
		)
	}()

	ctxzap.AddFields(
		ctx,
		file_inner_processing_grpc_transaction_proto_message_GetTransactionByIDRequestV1ToZap(label+"request", request),
	)

	response, err = s.srv.GetTransactionByID(ctx, request)

	return
}

func (s *loggedTransactionServer) MakeAutoCharge(
	ctx context.Context,
	request *MakeAutoChargeRequestV1,
) (
	response *emptypb.Empty,
	err error,
) {
	label := cntx.Begin(ctx, "TransactionServer_MakeAutoCharge")
	defer cntx.End(ctx, label)

	defer func() {
		if err != nil {
			ctxzap.AddFields(ctx, zap.Any(label+"error", err))
			return
		}

		ctxzap.AddFields(
			ctx,
			file_inner_processing_grpc_transaction_proto_message_EmptyToZap(label+"response", response),
		)
	}()

	ctxzap.AddFields(
		ctx,
		file_inner_processing_grpc_transaction_proto_message_MakeAutoChargeRequestV1ToZap(label+"request", request),
	)

	response, err = s.srv.MakeAutoCharge(ctx, request)

	return
}

func (s *loggedTransactionServer) SetRefundWaitingStatus(
	ctx context.Context,
	request *SetRefundWaitingStatusRequestV1,
) (
	response *SetRefundWaitingStatusResponseV1,
	err error,
) {
	label := cntx.Begin(ctx, "TransactionServer_SetRefundWaitingStatus")
	defer cntx.End(ctx, label)

	defer func() {
		if err != nil {
			ctxzap.AddFields(ctx, zap.Any(label+"error", err))
			return
		}

		ctxzap.AddFields(
			ctx,
			file_inner_processing_grpc_transaction_proto_message_SetRefundWaitingStatusResponseV1ToZap(label+"response", response),
		)
	}()

	ctxzap.AddFields(
		ctx,
		file_inner_processing_grpc_transaction_proto_message_SetRefundWaitingStatusRequestV1ToZap(label+"request", request),
	)

	response, err = s.srv.SetRefundWaitingStatus(ctx, request)

	return
}

func (s *loggedTransactionServer) GetTransactionsByProjectInfo(
	ctx context.Context,
	request *GetTransactionsByProjectInfoRequestV1,
) (
	response *GetTransactionsResponse,
	err error,
) {
	label := cntx.Begin(ctx, "TransactionServer_GetTransactionsByProjectInfo")
	defer cntx.End(ctx, label)

	defer func() {
		if err != nil {
			ctxzap.AddFields(ctx, zap.Any(label+"error", err))
			return
		}

		ctxzap.AddFields(
			ctx,
			file_inner_processing_grpc_transaction_proto_message_GetTransactionsResponseToZap(label+"response", response),
		)
	}()

	ctxzap.AddFields(
		ctx,
		file_inner_processing_grpc_transaction_proto_message_GetTransactionsByProjectInfoRequestV1ToZap(label+"request", request),
	)

	response, err = s.srv.GetTransactionsByProjectInfo(ctx, request)

	return
}

func (s *loggedTransactionServer) CheckTransactionHash(
	ctx context.Context,
	request *CheckTransactionHashRequestV1,
) (
	response *emptypb.Empty,
	err error,
) {
	label := cntx.Begin(ctx, "TransactionServer_CheckTransactionHash")
	defer cntx.End(ctx, label)

	defer func() {
		if err != nil {
			ctxzap.AddFields(ctx, zap.Any(label+"error", err))
			return
		}

		ctxzap.AddFields(
			ctx,
			file_inner_processing_grpc_transaction_proto_message_EmptyToZap(label+"response", response),
		)
	}()

	ctxzap.AddFields(
		ctx,
		file_inner_processing_grpc_transaction_proto_message_CheckTransactionHashRequestV1ToZap(label+"request", request),
	)

	response, err = s.srv.CheckTransactionHash(ctx, request)

	return
}

func (s *loggedTransactionServer) CreateTransactionByPhone(
	ctx context.Context,
	request *CreateTransactionByPhoneRequest,
) (
	response *CreateTransactionByPhoneResponse,
	err error,
) {
	label := cntx.Begin(ctx, "TransactionServer_CreateTransactionByPhone")
	defer cntx.End(ctx, label)

	defer func() {
		if err != nil {
			ctxzap.AddFields(ctx, zap.Any(label+"error", err))
			return
		}

		ctxzap.AddFields(
			ctx,
			file_inner_processing_grpc_transaction_proto_message_CreateTransactionByPhoneResponseToZap(label+"response", response),
		)
	}()

	ctxzap.AddFields(
		ctx,
		file_inner_processing_grpc_transaction_proto_message_CreateTransactionByPhoneRequestToZap(label+"request", request),
	)

	response, err = s.srv.CreateTransactionByPhone(ctx, request)

	return
}

func (s *loggedTransactionServer) GetByIDWithType(
	ctx context.Context,
	request *GetByIDWithTypeRequest,
) (
	response *GetByIDWithTypeResponse,
	err error,
) {
	label := cntx.Begin(ctx, "TransactionServer_GetByIDWithType")
	defer cntx.End(ctx, label)

	defer func() {
		if err != nil {
			ctxzap.AddFields(ctx, zap.Any(label+"error", err))
			return
		}

		ctxzap.AddFields(
			ctx,
			file_inner_processing_grpc_transaction_proto_message_GetByIDWithTypeResponseToZap(label+"response", response),
		)
	}()

	ctxzap.AddFields(
		ctx,
		file_inner_processing_grpc_transaction_proto_message_GetByIDWithTypeRequestToZap(label+"request", request),
	)

	response, err = s.srv.GetByIDWithType(ctx, request)

	return
}

func (s *loggedTransactionServer) UpdateStatus(
	ctx context.Context,
	request *UpdateStatusRequest,
) (
	response *emptypb.Empty,
	err error,
) {
	label := cntx.Begin(ctx, "TransactionServer_UpdateStatus")
	defer cntx.End(ctx, label)

	defer func() {
		if err != nil {
			ctxzap.AddFields(ctx, zap.Any(label+"error", err))
			return
		}

		ctxzap.AddFields(
			ctx,
			file_inner_processing_grpc_transaction_proto_message_EmptyToZap(label+"response", response),
		)
	}()

	ctxzap.AddFields(
		ctx,
		file_inner_processing_grpc_transaction_proto_message_UpdateStatusRequestToZap(label+"request", request),
	)

	response, err = s.srv.UpdateStatus(ctx, request)

	return
}

func (s *loggedTransactionServer) IncreaseTryCount(
	ctx context.Context,
	request *IncreaseTryCountRequest,
) (
	response *emptypb.Empty,
	err error,
) {
	label := cntx.Begin(ctx, "TransactionServer_IncreaseTryCount")
	defer cntx.End(ctx, label)

	defer func() {
		if err != nil {
			ctxzap.AddFields(ctx, zap.Any(label+"error", err))
			return
		}

		ctxzap.AddFields(
			ctx,
			file_inner_processing_grpc_transaction_proto_message_EmptyToZap(label+"response", response),
		)
	}()

	ctxzap.AddFields(
		ctx,
		file_inner_processing_grpc_transaction_proto_message_IncreaseTryCountRequestToZap(label+"request", request),
	)

	response, err = s.srv.IncreaseTryCount(ctx, request)

	return
}

func (s *loggedTransactionServer) SaveAcquirerResponse(
	ctx context.Context,
	request *SaveAcquirerResponseRequest,
) (
	response *emptypb.Empty,
	err error,
) {
	label := cntx.Begin(ctx, "TransactionServer_SaveAcquirerResponse")
	defer cntx.End(ctx, label)

	defer func() {
		if err != nil {
			ctxzap.AddFields(ctx, zap.Any(label+"error", err))
			return
		}

		ctxzap.AddFields(
			ctx,
			file_inner_processing_grpc_transaction_proto_message_EmptyToZap(label+"response", response),
		)
	}()

	ctxzap.AddFields(
		ctx,
		file_inner_processing_grpc_transaction_proto_message_SaveAcquirerResponseRequestToZap(label+"request", request),
	)

	response, err = s.srv.SaveAcquirerResponse(ctx, request)

	return
}

func (s *loggedTransactionServer) SendReceipt(
	ctx context.Context,
	request *SendRequest,
) (
	response *emptypb.Empty,
	err error,
) {
	label := cntx.Begin(ctx, "TransactionServer_SendReceipt")
	defer cntx.End(ctx, label)

	defer func() {
		if err != nil {
			ctxzap.AddFields(ctx, zap.Any(label+"error", err))
			return
		}

		ctxzap.AddFields(
			ctx,
			file_inner_processing_grpc_transaction_proto_message_EmptyToZap(label+"response", response),
		)
	}()

	ctxzap.AddFields(
		ctx,
		file_inner_processing_grpc_transaction_proto_message_SendRequestToZap(label+"request", request),
	)

	response, err = s.srv.SendReceipt(ctx, request)

	return
}

func (s *loggedTransactionServer) SetAdditionalData(
	ctx context.Context,
	request *SetAdditionalDataRequest,
) (
	response *emptypb.Empty,
	err error,
) {
	label := cntx.Begin(ctx, "TransactionServer_SetAdditionalData")
	defer cntx.End(ctx, label)

	defer func() {
		if err != nil {
			ctxzap.AddFields(ctx, zap.Any(label+"error", err))
			return
		}

		ctxzap.AddFields(
			ctx,
			file_inner_processing_grpc_transaction_proto_message_EmptyToZap(label+"response", response),
		)
	}()

	ctxzap.AddFields(
		ctx,
		file_inner_processing_grpc_transaction_proto_message_SetAdditionalDataRequestToZap(label+"request", request),
	)

	response, err = s.srv.SetAdditionalData(ctx, request)

	return
}

func (s *loggedTransactionServer) CalculateAndUpdateTransactionAmount(
	ctx context.Context,
	request *CalculateAndUpdateTransactionAmountRequest,
) (
	response *emptypb.Empty,
	err error,
) {
	label := cntx.Begin(ctx, "TransactionServer_CalculateAndUpdateTransactionAmount")
	defer cntx.End(ctx, label)

	defer func() {
		if err != nil {
			ctxzap.AddFields(ctx, zap.Any(label+"error", err))
			return
		}

		ctxzap.AddFields(
			ctx,
			file_inner_processing_grpc_transaction_proto_message_EmptyToZap(label+"response", response),
		)
	}()

	ctxzap.AddFields(
		ctx,
		file_inner_processing_grpc_transaction_proto_message_CalculateAndUpdateTransactionAmountRequestToZap(label+"request", request),
	)

	response, err = s.srv.CalculateAndUpdateTransactionAmount(ctx, request)

	return
}

func (s *loggedTransactionServer) BillPayOut(
	ctx context.Context,
	request *BillPayoutRequest,
) (
	response *emptypb.Empty,
	err error,
) {
	label := cntx.Begin(ctx, "TransactionServer_BillPayOut")
	defer cntx.End(ctx, label)

	defer func() {
		if err != nil {
			ctxzap.AddFields(ctx, zap.Any(label+"error", err))
			return
		}

		ctxzap.AddFields(
			ctx,
			file_inner_processing_grpc_transaction_proto_message_EmptyToZap(label+"response", response),
		)
	}()

	ctxzap.AddFields(
		ctx,
		file_inner_processing_grpc_transaction_proto_message_BillPayoutRequestToZap(label+"request", request),
	)

	response, err = s.srv.BillPayOut(ctx, request)

	return
}

var _ TransactionClient = (*loggedTransactionClient)(nil)

func NewLoggedTransactionClient(client TransactionClient) TransactionClient {
	return &loggedTransactionClient{client: client}
}

type loggedTransactionClient struct {
	client TransactionClient
}

func (s *loggedTransactionClient) UpdateTransactionStatus(
	ctx context.Context,
	request *UpdateTransactionStatusRequestV1,
	opts ...grpc.CallOption,
) (
	response *emptypb.Empty,
	err error,
) {
	label := cntx.Begin(ctx, "TransactionClient_UpdateTransactionStatus")
	defer cntx.End(ctx, label)

	defer func() {
		if err != nil {
			ctxzap.AddFields(ctx, zap.Any(label+"error", err))
			return
		}

		ctxzap.AddFields(
			ctx,
			file_inner_processing_grpc_transaction_proto_message_EmptyToZap(label+"response", response),
		)
	}()

	ctxzap.AddFields(
		ctx,
		file_inner_processing_grpc_transaction_proto_message_UpdateTransactionStatusRequestV1ToZap(label+"request", request),
	)

	response, err = s.client.UpdateTransactionStatus(ctx, request, opts...)

	return
}

func (s *loggedTransactionClient) UpdateCallbackStatus(
	ctx context.Context,
	request *UpdateCallbackStatusRequestV1,
	opts ...grpc.CallOption,
) (
	response *emptypb.Empty,
	err error,
) {
	label := cntx.Begin(ctx, "TransactionClient_UpdateCallbackStatus")
	defer cntx.End(ctx, label)

	defer func() {
		if err != nil {
			ctxzap.AddFields(ctx, zap.Any(label+"error", err))
			return
		}

		ctxzap.AddFields(
			ctx,
			file_inner_processing_grpc_transaction_proto_message_EmptyToZap(label+"response", response),
		)
	}()

	ctxzap.AddFields(
		ctx,
		file_inner_processing_grpc_transaction_proto_message_UpdateCallbackStatusRequestV1ToZap(label+"request", request),
	)

	response, err = s.client.UpdateCallbackStatus(ctx, request, opts...)

	return
}

func (s *loggedTransactionClient) GetTransactionsByPeriodAndStatus(
	ctx context.Context,
	request *GetTransactionsByStatusReqV1,
	opts ...grpc.CallOption,
) (
	response *GetTransactionsResponse,
	err error,
) {
	label := cntx.Begin(ctx, "TransactionClient_GetTransactionsByPeriodAndStatus")
	defer cntx.End(ctx, label)

	defer func() {
		if err != nil {
			ctxzap.AddFields(ctx, zap.Any(label+"error", err))
			return
		}

		ctxzap.AddFields(
			ctx,
			file_inner_processing_grpc_transaction_proto_message_GetTransactionsResponseToZap(label+"response", response),
		)
	}()

	ctxzap.AddFields(
		ctx,
		file_inner_processing_grpc_transaction_proto_message_GetTransactionsByStatusReqV1ToZap(label+"request", request),
	)

	response, err = s.client.GetTransactionsByPeriodAndStatus(ctx, request, opts...)

	return
}

func (s *loggedTransactionClient) GetTransactionsByPeriodAndCallbackStatus(
	ctx context.Context,
	request *GetTransactionsByCallbackStatusReqV1,
	opts ...grpc.CallOption,
) (
	response *GetTransactionsResponse,
	err error,
) {
	label := cntx.Begin(ctx, "TransactionClient_GetTransactionsByPeriodAndCallbackStatus")
	defer cntx.End(ctx, label)

	defer func() {
		if err != nil {
			ctxzap.AddFields(ctx, zap.Any(label+"error", err))
			return
		}

		ctxzap.AddFields(
			ctx,
			file_inner_processing_grpc_transaction_proto_message_GetTransactionsResponseToZap(label+"response", response),
		)
	}()

	ctxzap.AddFields(
		ctx,
		file_inner_processing_grpc_transaction_proto_message_GetTransactionsByCallbackStatusReqV1ToZap(label+"request", request),
	)

	response, err = s.client.GetTransactionsByPeriodAndCallbackStatus(ctx, request, opts...)

	return
}

func (s *loggedTransactionClient) GetTransactionsByFinalStatusAndPeriodWithLimit(
	ctx context.Context,
	request *GetTransactionsByFinalStatusAndPeriodWithLimitReqV1,
	opts ...grpc.CallOption,
) (
	response *GetTransactionsResponse,
	err error,
) {
	label := cntx.Begin(ctx, "TransactionClient_GetTransactionsByFinalStatusAndPeriodWithLimit")
	defer cntx.End(ctx, label)

	defer func() {
		if err != nil {
			ctxzap.AddFields(ctx, zap.Any(label+"error", err))
			return
		}

		ctxzap.AddFields(
			ctx,
			file_inner_processing_grpc_transaction_proto_message_GetTransactionsResponseToZap(label+"response", response),
		)
	}()

	ctxzap.AddFields(
		ctx,
		file_inner_processing_grpc_transaction_proto_message_GetTransactionsByFinalStatusAndPeriodWithLimitReqV1ToZap(label+"request", request),
	)

	response, err = s.client.GetTransactionsByFinalStatusAndPeriodWithLimit(ctx, request, opts...)

	return
}

func (s *loggedTransactionClient) GetPayInTransactionsByPeriodAndAcquirer(
	ctx context.Context,
	request *GetPayInTransactionsByPeriodAndAcquirerReqV1,
	opts ...grpc.CallOption,
) (
	response *GetTransactionsResponse,
	err error,
) {
	label := cntx.Begin(ctx, "TransactionClient_GetPayInTransactionsByPeriodAndAcquirer")
	defer cntx.End(ctx, label)

	defer func() {
		if err != nil {
			ctxzap.AddFields(ctx, zap.Any(label+"error", err))
			return
		}

		ctxzap.AddFields(
			ctx,
			file_inner_processing_grpc_transaction_proto_message_GetTransactionsResponseToZap(label+"response", response),
		)
	}()

	ctxzap.AddFields(
		ctx,
		file_inner_processing_grpc_transaction_proto_message_GetPayInTransactionsByPeriodAndAcquirerReqV1ToZap(label+"request", request),
	)

	response, err = s.client.GetPayInTransactionsByPeriodAndAcquirer(ctx, request, opts...)

	return
}

func (s *loggedTransactionClient) GetTransactionTypeByID(
	ctx context.Context,
	request *GetTransactionTypeByIDRequestV1,
	opts ...grpc.CallOption,
) (
	response *GetTransactionTypeByIDResponseV1,
	err error,
) {
	label := cntx.Begin(ctx, "TransactionClient_GetTransactionTypeByID")
	defer cntx.End(ctx, label)

	defer func() {
		if err != nil {
			ctxzap.AddFields(ctx, zap.Any(label+"error", err))
			return
		}

		ctxzap.AddFields(
			ctx,
			file_inner_processing_grpc_transaction_proto_message_GetTransactionTypeByIDResponseV1ToZap(label+"response", response),
		)
	}()

	ctxzap.AddFields(
		ctx,
		file_inner_processing_grpc_transaction_proto_message_GetTransactionTypeByIDRequestV1ToZap(label+"request", request),
	)

	response, err = s.client.GetTransactionTypeByID(ctx, request, opts...)

	return
}

func (s *loggedTransactionClient) GetTransactionByID(
	ctx context.Context,
	request *GetTransactionByIDRequestV1,
	opts ...grpc.CallOption,
) (
	response *TransactionDataV1,
	err error,
) {
	label := cntx.Begin(ctx, "TransactionClient_GetTransactionByID")
	defer cntx.End(ctx, label)

	defer func() {
		if err != nil {
			ctxzap.AddFields(ctx, zap.Any(label+"error", err))
			return
		}

		ctxzap.AddFields(
			ctx,
			file_inner_processing_grpc_transaction_proto_message_TransactionDataV1ToZap(label+"response", response),
		)
	}()

	ctxzap.AddFields(
		ctx,
		file_inner_processing_grpc_transaction_proto_message_GetTransactionByIDRequestV1ToZap(label+"request", request),
	)

	response, err = s.client.GetTransactionByID(ctx, request, opts...)

	return
}

func (s *loggedTransactionClient) MakeAutoCharge(
	ctx context.Context,
	request *MakeAutoChargeRequestV1,
	opts ...grpc.CallOption,
) (
	response *emptypb.Empty,
	err error,
) {
	label := cntx.Begin(ctx, "TransactionClient_MakeAutoCharge")
	defer cntx.End(ctx, label)

	defer func() {
		if err != nil {
			ctxzap.AddFields(ctx, zap.Any(label+"error", err))
			return
		}

		ctxzap.AddFields(
			ctx,
			file_inner_processing_grpc_transaction_proto_message_EmptyToZap(label+"response", response),
		)
	}()

	ctxzap.AddFields(
		ctx,
		file_inner_processing_grpc_transaction_proto_message_MakeAutoChargeRequestV1ToZap(label+"request", request),
	)

	response, err = s.client.MakeAutoCharge(ctx, request, opts...)

	return
}

func (s *loggedTransactionClient) SetRefundWaitingStatus(
	ctx context.Context,
	request *SetRefundWaitingStatusRequestV1,
	opts ...grpc.CallOption,
) (
	response *SetRefundWaitingStatusResponseV1,
	err error,
) {
	label := cntx.Begin(ctx, "TransactionClient_SetRefundWaitingStatus")
	defer cntx.End(ctx, label)

	defer func() {
		if err != nil {
			ctxzap.AddFields(ctx, zap.Any(label+"error", err))
			return
		}

		ctxzap.AddFields(
			ctx,
			file_inner_processing_grpc_transaction_proto_message_SetRefundWaitingStatusResponseV1ToZap(label+"response", response),
		)
	}()

	ctxzap.AddFields(
		ctx,
		file_inner_processing_grpc_transaction_proto_message_SetRefundWaitingStatusRequestV1ToZap(label+"request", request),
	)

	response, err = s.client.SetRefundWaitingStatus(ctx, request, opts...)

	return
}

func (s *loggedTransactionClient) GetTransactionsByProjectInfo(
	ctx context.Context,
	request *GetTransactionsByProjectInfoRequestV1,
	opts ...grpc.CallOption,
) (
	response *GetTransactionsResponse,
	err error,
) {
	label := cntx.Begin(ctx, "TransactionClient_GetTransactionsByProjectInfo")
	defer cntx.End(ctx, label)

	defer func() {
		if err != nil {
			ctxzap.AddFields(ctx, zap.Any(label+"error", err))
			return
		}

		ctxzap.AddFields(
			ctx,
			file_inner_processing_grpc_transaction_proto_message_GetTransactionsResponseToZap(label+"response", response),
		)
	}()

	ctxzap.AddFields(
		ctx,
		file_inner_processing_grpc_transaction_proto_message_GetTransactionsByProjectInfoRequestV1ToZap(label+"request", request),
	)

	response, err = s.client.GetTransactionsByProjectInfo(ctx, request, opts...)

	return
}

func (s *loggedTransactionClient) CheckTransactionHash(
	ctx context.Context,
	request *CheckTransactionHashRequestV1,
	opts ...grpc.CallOption,
) (
	response *emptypb.Empty,
	err error,
) {
	label := cntx.Begin(ctx, "TransactionClient_CheckTransactionHash")
	defer cntx.End(ctx, label)

	defer func() {
		if err != nil {
			ctxzap.AddFields(ctx, zap.Any(label+"error", err))
			return
		}

		ctxzap.AddFields(
			ctx,
			file_inner_processing_grpc_transaction_proto_message_EmptyToZap(label+"response", response),
		)
	}()

	ctxzap.AddFields(
		ctx,
		file_inner_processing_grpc_transaction_proto_message_CheckTransactionHashRequestV1ToZap(label+"request", request),
	)

	response, err = s.client.CheckTransactionHash(ctx, request, opts...)

	return
}

func (s *loggedTransactionClient) CreateTransactionByPhone(
	ctx context.Context,
	request *CreateTransactionByPhoneRequest,
	opts ...grpc.CallOption,
) (
	response *CreateTransactionByPhoneResponse,
	err error,
) {
	label := cntx.Begin(ctx, "TransactionClient_CreateTransactionByPhone")
	defer cntx.End(ctx, label)

	defer func() {
		if err != nil {
			ctxzap.AddFields(ctx, zap.Any(label+"error", err))
			return
		}

		ctxzap.AddFields(
			ctx,
			file_inner_processing_grpc_transaction_proto_message_CreateTransactionByPhoneResponseToZap(label+"response", response),
		)
	}()

	ctxzap.AddFields(
		ctx,
		file_inner_processing_grpc_transaction_proto_message_CreateTransactionByPhoneRequestToZap(label+"request", request),
	)

	response, err = s.client.CreateTransactionByPhone(ctx, request, opts...)

	return
}

func (s *loggedTransactionClient) GetByIDWithType(
	ctx context.Context,
	request *GetByIDWithTypeRequest,
	opts ...grpc.CallOption,
) (
	response *GetByIDWithTypeResponse,
	err error,
) {
	label := cntx.Begin(ctx, "TransactionClient_GetByIDWithType")
	defer cntx.End(ctx, label)

	defer func() {
		if err != nil {
			ctxzap.AddFields(ctx, zap.Any(label+"error", err))
			return
		}

		ctxzap.AddFields(
			ctx,
			file_inner_processing_grpc_transaction_proto_message_GetByIDWithTypeResponseToZap(label+"response", response),
		)
	}()

	ctxzap.AddFields(
		ctx,
		file_inner_processing_grpc_transaction_proto_message_GetByIDWithTypeRequestToZap(label+"request", request),
	)

	response, err = s.client.GetByIDWithType(ctx, request, opts...)

	return
}

func (s *loggedTransactionClient) UpdateStatus(
	ctx context.Context,
	request *UpdateStatusRequest,
	opts ...grpc.CallOption,
) (
	response *emptypb.Empty,
	err error,
) {
	label := cntx.Begin(ctx, "TransactionClient_UpdateStatus")
	defer cntx.End(ctx, label)

	defer func() {
		if err != nil {
			ctxzap.AddFields(ctx, zap.Any(label+"error", err))
			return
		}

		ctxzap.AddFields(
			ctx,
			file_inner_processing_grpc_transaction_proto_message_EmptyToZap(label+"response", response),
		)
	}()

	ctxzap.AddFields(
		ctx,
		file_inner_processing_grpc_transaction_proto_message_UpdateStatusRequestToZap(label+"request", request),
	)

	response, err = s.client.UpdateStatus(ctx, request, opts...)

	return
}

func (s *loggedTransactionClient) IncreaseTryCount(
	ctx context.Context,
	request *IncreaseTryCountRequest,
	opts ...grpc.CallOption,
) (
	response *emptypb.Empty,
	err error,
) {
	label := cntx.Begin(ctx, "TransactionClient_IncreaseTryCount")
	defer cntx.End(ctx, label)

	defer func() {
		if err != nil {
			ctxzap.AddFields(ctx, zap.Any(label+"error", err))
			return
		}

		ctxzap.AddFields(
			ctx,
			file_inner_processing_grpc_transaction_proto_message_EmptyToZap(label+"response", response),
		)
	}()

	ctxzap.AddFields(
		ctx,
		file_inner_processing_grpc_transaction_proto_message_IncreaseTryCountRequestToZap(label+"request", request),
	)

	response, err = s.client.IncreaseTryCount(ctx, request, opts...)

	return
}

func (s *loggedTransactionClient) SaveAcquirerResponse(
	ctx context.Context,
	request *SaveAcquirerResponseRequest,
	opts ...grpc.CallOption,
) (
	response *emptypb.Empty,
	err error,
) {
	label := cntx.Begin(ctx, "TransactionClient_SaveAcquirerResponse")
	defer cntx.End(ctx, label)

	defer func() {
		if err != nil {
			ctxzap.AddFields(ctx, zap.Any(label+"error", err))
			return
		}

		ctxzap.AddFields(
			ctx,
			file_inner_processing_grpc_transaction_proto_message_EmptyToZap(label+"response", response),
		)
	}()

	ctxzap.AddFields(
		ctx,
		file_inner_processing_grpc_transaction_proto_message_SaveAcquirerResponseRequestToZap(label+"request", request),
	)

	response, err = s.client.SaveAcquirerResponse(ctx, request, opts...)

	return
}

func (s *loggedTransactionClient) SendReceipt(
	ctx context.Context,
	request *SendRequest,
	opts ...grpc.CallOption,
) (
	response *emptypb.Empty,
	err error,
) {
	label := cntx.Begin(ctx, "TransactionClient_SendReceipt")
	defer cntx.End(ctx, label)

	defer func() {
		if err != nil {
			ctxzap.AddFields(ctx, zap.Any(label+"error", err))
			return
		}

		ctxzap.AddFields(
			ctx,
			file_inner_processing_grpc_transaction_proto_message_EmptyToZap(label+"response", response),
		)
	}()

	ctxzap.AddFields(
		ctx,
		file_inner_processing_grpc_transaction_proto_message_SendRequestToZap(label+"request", request),
	)

	response, err = s.client.SendReceipt(ctx, request, opts...)

	return
}

func (s *loggedTransactionClient) SetAdditionalData(
	ctx context.Context,
	request *SetAdditionalDataRequest,
	opts ...grpc.CallOption,
) (
	response *emptypb.Empty,
	err error,
) {
	label := cntx.Begin(ctx, "TransactionClient_SetAdditionalData")
	defer cntx.End(ctx, label)

	defer func() {
		if err != nil {
			ctxzap.AddFields(ctx, zap.Any(label+"error", err))
			return
		}

		ctxzap.AddFields(
			ctx,
			file_inner_processing_grpc_transaction_proto_message_EmptyToZap(label+"response", response),
		)
	}()

	ctxzap.AddFields(
		ctx,
		file_inner_processing_grpc_transaction_proto_message_SetAdditionalDataRequestToZap(label+"request", request),
	)

	response, err = s.client.SetAdditionalData(ctx, request, opts...)

	return
}

func (s *loggedTransactionClient) CalculateAndUpdateTransactionAmount(
	ctx context.Context,
	request *CalculateAndUpdateTransactionAmountRequest,
	opts ...grpc.CallOption,
) (
	response *emptypb.Empty,
	err error,
) {
	label := cntx.Begin(ctx, "TransactionClient_CalculateAndUpdateTransactionAmount")
	defer cntx.End(ctx, label)

	defer func() {
		if err != nil {
			ctxzap.AddFields(ctx, zap.Any(label+"error", err))
			return
		}

		ctxzap.AddFields(
			ctx,
			file_inner_processing_grpc_transaction_proto_message_EmptyToZap(label+"response", response),
		)
	}()

	ctxzap.AddFields(
		ctx,
		file_inner_processing_grpc_transaction_proto_message_CalculateAndUpdateTransactionAmountRequestToZap(label+"request", request),
	)

	response, err = s.client.CalculateAndUpdateTransactionAmount(ctx, request, opts...)

	return
}

func (s *loggedTransactionClient) BillPayOut(
	ctx context.Context,
	request *BillPayoutRequest,
	opts ...grpc.CallOption,
) (
	response *emptypb.Empty,
	err error,
) {
	label := cntx.Begin(ctx, "TransactionClient_BillPayOut")
	defer cntx.End(ctx, label)

	defer func() {
		if err != nil {
			ctxzap.AddFields(ctx, zap.Any(label+"error", err))
			return
		}

		ctxzap.AddFields(
			ctx,
			file_inner_processing_grpc_transaction_proto_message_EmptyToZap(label+"response", response),
		)
	}()

	ctxzap.AddFields(
		ctx,
		file_inner_processing_grpc_transaction_proto_message_BillPayoutRequestToZap(label+"request", request),
	)

	response, err = s.client.BillPayOut(ctx, request, opts...)

	return
}
