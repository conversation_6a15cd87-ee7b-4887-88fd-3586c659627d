// Code generated by MockGen. DO NOT EDIT.
// Source: mock_acquirer.gin.pb.go

// Package grpcmock is a generated GoMock package.
package grpcmock

import (
	reflect "reflect"

	gin "github.com/gin-gonic/gin"
	gomock "github.com/golang/mock/gomock"
)

// MockGinMockAcquirerServer is a mock of GinMockAcquirerServer interface.
type MockGinMockAcquirerServer struct {
	ctrl     *gomock.Controller
	recorder *MockGinMockAcquirerServerMockRecorder
}

// MockGinMockAcquirerServerMockRecorder is the mock recorder for MockGinMockAcquirerServer.
type MockGinMockAcquirerServerMockRecorder struct {
	mock *MockGinMockAcquirerServer
}

// NewMockGinMockAcquirerServer creates a new mock instance.
func NewMockGinMockAcquirerServer(ctrl *gomock.Controller) *MockGinMockAcquirerServer {
	mock := &MockGinMockAcquirerServer{ctrl: ctrl}
	mock.recorder = &MockGinMockAcquirerServerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockGinMockAcquirerServer) EXPECT() *MockGinMockAcquirerServerMockRecorder {
	return m.recorder
}

// ApplePay mocks base method.
func (m *MockGinMockAcquirerServer) ApplePay(c *gin.Context) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ApplePay", c)
	ret0, _ := ret[0].(error)
	return ret0
}

// ApplePay indicates an expected call of ApplePay.
func (mr *MockGinMockAcquirerServerMockRecorder) ApplePay(c interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ApplePay", reflect.TypeOf((*MockGinMockAcquirerServer)(nil).ApplePay), c)
}

// Cancel mocks base method.
func (m *MockGinMockAcquirerServer) Cancel(c *gin.Context) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Cancel", c)
	ret0, _ := ret[0].(error)
	return ret0
}

// Cancel indicates an expected call of Cancel.
func (mr *MockGinMockAcquirerServerMockRecorder) Cancel(c interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Cancel", reflect.TypeOf((*MockGinMockAcquirerServer)(nil).Cancel), c)
}

// Charge mocks base method.
func (m *MockGinMockAcquirerServer) Charge(c *gin.Context) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Charge", c)
	ret0, _ := ret[0].(error)
	return ret0
}

// Charge indicates an expected call of Charge.
func (mr *MockGinMockAcquirerServerMockRecorder) Charge(c interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Charge", reflect.TypeOf((*MockGinMockAcquirerServer)(nil).Charge), c)
}

// GetAcquirerIdentifier mocks base method.
func (m *MockGinMockAcquirerServer) GetAcquirerIdentifier(c *gin.Context) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAcquirerIdentifier", c)
	ret0, _ := ret[0].(error)
	return ret0
}

// GetAcquirerIdentifier indicates an expected call of GetAcquirerIdentifier.
func (mr *MockGinMockAcquirerServerMockRecorder) GetAcquirerIdentifier(c interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAcquirerIdentifier", reflect.TypeOf((*MockGinMockAcquirerServer)(nil).GetAcquirerIdentifier), c)
}

// GetBankTransactionStatus mocks base method.
func (m *MockGinMockAcquirerServer) GetBankTransactionStatus(c *gin.Context) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetBankTransactionStatus", c)
	ret0, _ := ret[0].(error)
	return ret0
}

// GetBankTransactionStatus indicates an expected call of GetBankTransactionStatus.
func (mr *MockGinMockAcquirerServerMockRecorder) GetBankTransactionStatus(c interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetBankTransactionStatus", reflect.TypeOf((*MockGinMockAcquirerServer)(nil).GetBankTransactionStatus), c)
}

// GetBankTransactionStatusUnformated mocks base method.
func (m *MockGinMockAcquirerServer) GetBankTransactionStatusUnformated(c *gin.Context) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetBankTransactionStatusUnformated", c)
	ret0, _ := ret[0].(error)
	return ret0
}

// GetBankTransactionStatusUnformated indicates an expected call of GetBankTransactionStatusUnformated.
func (mr *MockGinMockAcquirerServerMockRecorder) GetBankTransactionStatusUnformated(c interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetBankTransactionStatusUnformated", reflect.TypeOf((*MockGinMockAcquirerServer)(nil).GetBankTransactionStatusUnformated), c)
}

// GooglePay mocks base method.
func (m *MockGinMockAcquirerServer) GooglePay(c *gin.Context) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GooglePay", c)
	ret0, _ := ret[0].(error)
	return ret0
}

// GooglePay indicates an expected call of GooglePay.
func (mr *MockGinMockAcquirerServerMockRecorder) GooglePay(c interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GooglePay", reflect.TypeOf((*MockGinMockAcquirerServer)(nil).GooglePay), c)
}

// MakeToken mocks base method.
func (m *MockGinMockAcquirerServer) MakeToken(c *gin.Context) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "MakeToken", c)
	ret0, _ := ret[0].(error)
	return ret0
}

// MakeToken indicates an expected call of MakeToken.
func (mr *MockGinMockAcquirerServerMockRecorder) MakeToken(c interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "MakeToken", reflect.TypeOf((*MockGinMockAcquirerServer)(nil).MakeToken), c)
}

// OneClickPayIn mocks base method.
func (m *MockGinMockAcquirerServer) OneClickPayIn(c *gin.Context) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "OneClickPayIn", c)
	ret0, _ := ret[0].(error)
	return ret0
}

// OneClickPayIn indicates an expected call of OneClickPayIn.
func (mr *MockGinMockAcquirerServerMockRecorder) OneClickPayIn(c interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "OneClickPayIn", reflect.TypeOf((*MockGinMockAcquirerServer)(nil).OneClickPayIn), c)
}

// PayIn mocks base method.
func (m *MockGinMockAcquirerServer) PayIn(c *gin.Context) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "PayIn", c)
	ret0, _ := ret[0].(error)
	return ret0
}

// PayIn indicates an expected call of PayIn.
func (mr *MockGinMockAcquirerServerMockRecorder) PayIn(c interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "PayIn", reflect.TypeOf((*MockGinMockAcquirerServer)(nil).PayIn), c)
}

// PayOut mocks base method.
func (m *MockGinMockAcquirerServer) PayOut(c *gin.Context) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "PayOut", c)
	ret0, _ := ret[0].(error)
	return ret0
}

// PayOut indicates an expected call of PayOut.
func (mr *MockGinMockAcquirerServerMockRecorder) PayOut(c interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "PayOut", reflect.TypeOf((*MockGinMockAcquirerServer)(nil).PayOut), c)
}

// PayOutByPhone mocks base method.
func (m *MockGinMockAcquirerServer) PayOutByPhone(c *gin.Context) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "PayOutByPhone", c)
	ret0, _ := ret[0].(error)
	return ret0
}

// PayOutByPhone indicates an expected call of PayOutByPhone.
func (mr *MockGinMockAcquirerServerMockRecorder) PayOutByPhone(c interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "PayOutByPhone", reflect.TypeOf((*MockGinMockAcquirerServer)(nil).PayOutByPhone), c)
}

// Refund mocks base method.
func (m *MockGinMockAcquirerServer) Refund(c *gin.Context) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Refund", c)
	ret0, _ := ret[0].(error)
	return ret0
}

// Refund indicates an expected call of Refund.
func (mr *MockGinMockAcquirerServerMockRecorder) Refund(c interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Refund", reflect.TypeOf((*MockGinMockAcquirerServer)(nil).Refund), c)
}

// ResolveVisaAlias mocks base method.
func (m *MockGinMockAcquirerServer) ResolveVisaAlias(c *gin.Context) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ResolveVisaAlias", c)
	ret0, _ := ret[0].(error)
	return ret0
}

// ResolveVisaAlias indicates an expected call of ResolveVisaAlias.
func (mr *MockGinMockAcquirerServerMockRecorder) ResolveVisaAlias(c interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ResolveVisaAlias", reflect.TypeOf((*MockGinMockAcquirerServer)(nil).ResolveVisaAlias), c)
}

// ThreeDSConfirm mocks base method.
func (m *MockGinMockAcquirerServer) ThreeDSConfirm(c *gin.Context) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ThreeDSConfirm", c)
	ret0, _ := ret[0].(error)
	return ret0
}

// ThreeDSConfirm indicates an expected call of ThreeDSConfirm.
func (mr *MockGinMockAcquirerServerMockRecorder) ThreeDSConfirm(c interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ThreeDSConfirm", reflect.TypeOf((*MockGinMockAcquirerServer)(nil).ThreeDSConfirm), c)
}

// ThreeDSResume mocks base method.
func (m *MockGinMockAcquirerServer) ThreeDSResume(c *gin.Context) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ThreeDSResume", c)
	ret0, _ := ret[0].(error)
	return ret0
}

// ThreeDSResume indicates an expected call of ThreeDSResume.
func (mr *MockGinMockAcquirerServerMockRecorder) ThreeDSResume(c interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ThreeDSResume", reflect.TypeOf((*MockGinMockAcquirerServer)(nil).ThreeDSResume), c)
}

// TwoStagePayIn mocks base method.
func (m *MockGinMockAcquirerServer) TwoStagePayIn(c *gin.Context) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "TwoStagePayIn", c)
	ret0, _ := ret[0].(error)
	return ret0
}

// TwoStagePayIn indicates an expected call of TwoStagePayIn.
func (mr *MockGinMockAcquirerServerMockRecorder) TwoStagePayIn(c interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "TwoStagePayIn", reflect.TypeOf((*MockGinMockAcquirerServer)(nil).TwoStagePayIn), c)
}
