edition = "2023";

package processing.magnetiq.magnetiq;

option go_package = "git.local/sensitive/innerpb/processing/grpc";

import "inner/processing/grpc/multiacquiring.proto";
import "inner/processing/grpc/transaction_status.proto";
import "inner/processing/grpc/integration.proto";

import "mvp/proto/refs.proto";
import "mvp/proto/enum.proto";

import "google/protobuf/empty.proto";
import "google/protobuf/descriptor.proto";

service Magnetiq {
  rpc PayIn(processing.multiacquiring.multiacquiring.PayInRequestData) returns (processing.multiacquiring.multiacquiring.PayInResponseData) {}
  rpc OneClickPayIn(processing.multiacquiring.multiacquiring.OneClickPayInRequestData) returns (processing.multiacquiring.multiacquiring.PayInResponseData) {}
  rpc ThreeDSConfirm(processing.multiacquiring.multiacquiring.ThreeDSRequestData) returns (processing.multiacquiring.multiacquiring.ThreeDSResponseData) {}
  rpc ThreeDSResume(processing.multiacquiring.multiacquiring.ThreeDSResumeRequest) returns (processing.multiacquiring.multiacquiring.ThreeDSResumeResponse) {}
  rpc PayOut(processing.multiacquiring.multiacquiring.PayOutRequestData) returns (processing.multiacquiring.multiacquiring.PayOutResponseData) {}
  rpc GetBankTransactionStatus(processing.multiacquiring.multiacquiring.BankTransactionStatusRequest) returns (processing.multiacquiring.multiacquiring.BankTransactionStatusResponse) {}
  rpc GetBankTransactionStatusUnformated(processing.multiacquiring.multiacquiring.BankTransactionStatusUnformatedRequest) returns (processing.multiacquiring.multiacquiring.BankTransactionStatusUnformatedResponse) {}
  rpc Refund(processing.multiacquiring.multiacquiring.RefundRequest) returns (processing.multiacquiring.multiacquiring.RefundResponse) {}
  rpc GooglePay(processing.multiacquiring.multiacquiring.GooglePayRequestData) returns (processing.multiacquiring.multiacquiring.GooglePayResponseData) {}
  rpc ApplePay(processing.multiacquiring.multiacquiring.ApplePayRequestData) returns (processing.multiacquiring.multiacquiring.ApplePayResponseData) {}
  rpc TwoStagePayIn(processing.multiacquiring.multiacquiring.TwoStagePayInRequest) returns (processing.multiacquiring.multiacquiring.TwoStagePayInResponse) {}
  rpc Charge(processing.multiacquiring.multiacquiring.ChargeRequest) returns (processing.multiacquiring.multiacquiring.ChargeResponse) {}
  rpc Cancel(processing.multiacquiring.multiacquiring.CancelRequest) returns (processing.multiacquiring.multiacquiring.CancelResponse) {}
  rpc MakeToken(processing.multiacquiring.multiacquiring.PayInRequestData) returns (processing.multiacquiring.multiacquiring.PayInResponseData) {}
  rpc GetAcquirerIdentifier(google.protobuf.Empty) returns (processing.multiacquiring.multiacquiring.GetAcquirerIdentifierResponse) {}
  rpc ResolveVisaAlias(processing.multiacquiring.multiacquiring.ResolveVisaAliasRequest) returns (processing.multiacquiring.multiacquiring.ResolveVisaAliasResponse) {}
  rpc PayOutByPhone(processing.multiacquiring.multiacquiring.PayOutByPhoneRequestData) returns (processing.multiacquiring.multiacquiring.PayOutResponseByPhoneData) {}
}

message MagnetiqStatusRef {
  string code = 1;
  string name = 2;
  processing.transaction.transaction_status.EnumTransactionStatus transaction_status = 3;
}

extend google.protobuf.EnumValueOptions {
  MagnetiqStatusRef magnetiq_status_value = 300001;
}

extend google.protobuf.EnumOptions {
  MagnetiqStatusRef default_magnetiq_status_value = 300002;
}

enum EnumMagnetiqStatus {
  option(mvp.default_ref) = "default_magnetiq_status_value";
  option(mvp.ref) = "magnetiq_status_value";
  option(default_magnetiq_status_value) = {
    name: "undefined"
    transaction_status: TransactionStatusHolded,
  };
  MagnetiqStatusRequested = 0 [(magnetiq_status_value) = {
    name: "Requested",
    transaction_status: TransactionStatusProcessed
  }, (mvp.from_string) = "1"];
  MagnetiqStatusDeclined = 1 [(magnetiq_status_value) = {
    name: "Declined",
    transaction_status: TransactionStatusFailed
  }, (mvp.from_string) = "2"];
  MagnetiqStatusApproved = 2 [(magnetiq_status_value) = {
    name: "Approved",
    transaction_status: TransactionStatusAuthorized
  }, (mvp.from_string) = "3"];
  MagnetiqStatusUnapproved = 3 [(magnetiq_status_value) = {
    name: "Unapproved",
    transaction_status: TransactionStatusFailed
  }, (mvp.from_string) = "4"];
  MagnetiqStatusCancelled = 4 [(magnetiq_status_value) = {
    name: "Cancelled",
    transaction_status: TransactionStatusCanceled
  }, (mvp.from_string) = "5"];
  MagnetiqStatusDeposited = 5 [(magnetiq_status_value) = {
    name: "Deposited",
    transaction_status: TransactionStatusSuccess
  }, (mvp.from_string) = "6"];
  MagnetiqStatusProcessed= 6 [(magnetiq_status_value) = {
    name: "Processed",
    transaction_status: TransactionStatusSuccess
  }, (mvp.from_string) = "7"];
  MagnetiqStatusReversed= 7 [(magnetiq_status_value) = {
    name: "Reversed",
    transaction_status: TransactionStatusRefund
  }, (mvp.from_string) = "8"];
}

message MagnetiqFaultErrCodeRef {
  string message = 1;
  processing.integration.integration.IntegrationError integration_error = 2;
}

extend google.protobuf.EnumValueOptions {
  MagnetiqFaultErrCodeRef magnetiq_fault_err_code_value = 300003;
}

extend google.protobuf.EnumOptions {
  MagnetiqFaultErrCodeRef default_magnetiq_fault_err_code_value = 300004;
}

enum MagnetiqFaultErrCode {
  option(mvp.default_ref) = "default_magnetiq_fault_err_code_value";
  option(mvp.ref) = "magnetiq_fault_err_code_value";
  option(default_magnetiq_fault_err_code_value) = {
    message: "undefined",
    integration_error: UndefinedError,
  };
  MagnetiqFaultServerErr = 0 [(magnetiq_fault_err_code_value) = {
    message: "Internal server error",
    integration_error: UnavailableAcquirer,
  }, (mvp.from_string) = "100000"];
  MagnetiqFaultLockTimeoutErr = 1 [(magnetiq_fault_err_code_value) = {
    message: "Lock timeout",
    integration_error: UnavailableAcquirer,
  }, (mvp.from_string) = "100001"];
  MagnetiqFaultEntityNotFoundErr = 2 [(magnetiq_fault_err_code_value) = {
    message: "Entity not found",
    integration_error: TransactionDeclinedByAcquirer,
  }, (mvp.from_string) = "200000"];
  MagnetiqFaultBadRequestErr = 3 [(magnetiq_fault_err_code_value) = {
    message: "Bad request",
    integration_error: TransactionDeclinedByAcquirer,
  }, (mvp.from_string) = "300000"];
  MagnetiqFaultIntegrityViolationErr = 4 [(magnetiq_fault_err_code_value) = {
    message: "Integrity violation",
    integration_error: TransactionDeclinedByAcquirer,
  }, (mvp.from_string) = "400000"];
  MagnetiqFaultOrderIdMustBeUniqueErr = 5 [(magnetiq_fault_err_code_value) = {
    message: "Order ID must be unique",
    integration_error: TransactionDeclinedByAcquirer,
  }, (mvp.from_string) = "400011"];
  MagnetiqFaultTokenAlreadyRegisteredErr = 6 [(magnetiq_fault_err_code_value) = {
    message: "Token is already registered",
    integration_error: TransactionDeclinedByAcquirer,
  }, (mvp.from_string) = "400014"];
  MagnetiqFaultTokenNotFoundErr = 7 [(magnetiq_fault_err_code_value) = {
    message: "Token not found",
    integration_error: TransactionDeclinedByAcquirer,
  }, (mvp.from_string) = "200201"];
  MagnetiqFaultDirectoryServiceNotFoundErr = 8 [(magnetiq_fault_err_code_value) = {
    message: "Directory service not found",
    integration_error: TransactionDeclinedByAcquirer,
  }, (mvp.from_string) = "200301"];
  MagnetiqFaultKeyNotFoundErr = 9 [(magnetiq_fault_err_code_value) = {
    message: "Key not found. Either INTERFACE or KEY_INDEX is incorrect.",
    integration_error: TransactionDeclinedByAcquirer,
  }, (mvp.from_string) = "200401"];
  MagnetiqFaultRecurringNotFoundErr = 10 [(magnetiq_fault_err_code_value) = {
    message: "Recurring not found",
    integration_error: TransactionDeclinedByAcquirer,
  }, (mvp.from_string) = "200601"];
  MagnetiqFaultPaymentStateFlowViolationErr = 11 [(magnetiq_fault_err_code_value) = {
    message: "Could not initiate payment, state flow violated",
    integration_error: TransactionDeclinedByAcquirer,
  }, (mvp.from_string) = "505102"];
  MagnetiqFaultAuthNon3dPaymentErr = 12 [(magnetiq_fault_err_code_value) = {
    message: "Could not initialize authentication of non-3D payment",
    integration_error: TransactionDeclinedByAcquirer,
  }, (mvp.from_string) = "605103"];
  MagnetiqFaultAuthenticatePaymentErr = 13 [(magnetiq_fault_err_code_value) = {
    message: "Could not authenticate payment, state flow violated",
    integration_error: TransactionDeclinedByAcquirer,
  }, (mvp.from_string) = "505104"];
  MagnetiqFaultAuthenticateNon3dPaymentErr = 14 [(magnetiq_fault_err_code_value) = {
    message: "Could not authenticate non-3D payment",
    integration_error: TransactionDeclinedByAcquirer,
  }, (mvp.from_string) = "605105"];
  MagnetiqFaultDepositPaymentErr = 15 [(magnetiq_fault_err_code_value) = {
    message: "Could not deposit payment, state flow violated",
    integration_error: TransactionDeclinedByAcquirer,
  }, (mvp.from_string) = "505106"];
  MagnetiqFaultCancelPaymentErr = 16 [(magnetiq_fault_err_code_value) = {
    message: "Could not cancel payment, state flow violated",
    integration_error: TransactionDeclinedByAcquirer,
  }, (mvp.from_string) = "505107"];
  MagnetiqFaultCardNameRequiredErr = 17 [(magnetiq_fault_err_code_value) = {
    message: "Card name is required",
    integration_error: TransactionDeclinedByAcquirer,
  }, (mvp.from_string) = "705109"];
  MagnetiqFaultCardNumberRequiredErr = 18 [(magnetiq_fault_err_code_value) = {
    message: "Card number is required",
    integration_error: TransactionDeclinedByAcquirer,
  }, (mvp.from_string) = "705110"];
  MagnetiqFaultCardExpiryRequiredErr = 19 [(magnetiq_fault_err_code_value) = {
    message: "Card expiry is required",
    integration_error: TransactionDeclinedByAcquirer,
  }, (mvp.from_string) = "705111"];
  MagnetiqFaultInvalidCardExpiryDateErr = 20 [(magnetiq_fault_err_code_value) = {
    message: "Invalid card expiry date",
    integration_error: IncorrectCardExpDate,
  }, (mvp.from_string) = "305112"];
  MagnetiqFaultInvalidCardNumberErr = 21 [(magnetiq_fault_err_code_value) = {
    message: "Invalid card number",
    integration_error: IncorrectCardNumber,
  }, (mvp.from_string) = "305113"];
  MagnetiqFaultCouldNotReversePaymentErr = 22 [(magnetiq_fault_err_code_value) = {
    message: "Could not reverse payment, state flow violated",
    integration_error: TransactionDeclinedByAcquirer,
  }, (mvp.from_string) = "505115"];
  MagnetiqFaultMerchantInterfaceClosedErr = 23 [(magnetiq_fault_err_code_value) = {
    message: "Merchant interface is closed",
    integration_error: TransactionDeclinedByAcquirer,
  }, (mvp.from_string) = "505116"];
  MagnetiqFaultInvalidCurrencyErr = 24 [(magnetiq_fault_err_code_value) = {
    message: "Invalid currency",
    integration_error: TransactionDeclinedByAcquirer,
  }, (mvp.from_string) = "605117"];
  MagnetiqFaultOriginalCreditNotAllowedErr = 25 [(magnetiq_fault_err_code_value) = {
    message: "Original credit operations are not allowed on this interface",
    integration_error: PaymentForbiddenForMerchant,
  }, (mvp.from_string) = "605118"];
  MagnetiqFaultCSCRequiredErr = 26 [(magnetiq_fault_err_code_value) = {
    message: "CSC is required",
    integration_error: TransactionDeclinedByAcquirer,
  }, (mvp.from_string) = "705120"];
  MagnetiqFaultCSCInvalidErr = 27 [(magnetiq_fault_err_code_value) = {
    message: "CSC is invalid",
    integration_error: IncorrectCVVCVC,
  }, (mvp.from_string) = "305122"];
  MagnetiqFaultPaymentModeRequiredErr = 28 [(magnetiq_fault_err_code_value) = {
    message: "Payment.Mode is required",
    integration_error: TransactionDeclinedByAcquirer,
  }, (mvp.from_string) = "710101"];
  MagnetiqFaultPaymentOrderRequiredErr = 29 [(magnetiq_fault_err_code_value) = {
    message: "Payment.Order is required",
    integration_error: TransactionDeclinedByAcquirer,
  }, (mvp.from_string) = "710102"];
  MagnetiqFaultPaymentOrderIDRequiredErr = 30 [(magnetiq_fault_err_code_value) = {
    message: "Payment.Order.ID is required",
    integration_error: TransactionDeclinedByAcquirer,
  }, (mvp.from_string) = "710103"];
  MagnetiqFaultPaymentOrderAmountRequiredErr = 31 [(magnetiq_fault_err_code_value) = {
    message: "Payment.Order.Amount is required",
    integration_error: TransactionDeclinedByAcquirer,
  }, (mvp.from_string) = "710104"];
  MagnetiqFaultPaymentOrderCurrencyRequiredErr = 32 [(magnetiq_fault_err_code_value) = {
    message: "Payment.Order.Currency is required",
    integration_error: TransactionDeclinedByAcquirer,
  }, (mvp.from_string) = "710105"];
  MagnetiqFaultPaymentOrderDescriptionRequiredErr = 33 [(magnetiq_fault_err_code_value) = {
    message: "Payment.Order.Description is required",
    integration_error: TransactionDeclinedByAcquirer,
  }, (mvp.from_string) = "710106"];
  MagnetiqFaultD3DACSParResRequiredErr = 34 [(magnetiq_fault_err_code_value) = {
    message: "D3D.ACS.PaRes is required",
    integration_error: TransactionDeclinedByAcquirer,
  }, (mvp.from_string) = "710109"];
  MagnetiqFaultRecurringExpiredErr = 35 [(magnetiq_fault_err_code_value) = {
    message: "Recurring expired",
    integration_error: TransactionDeclinedByAcquirer,
  }, (mvp.from_string) = "510114"];
  MagnetiqFaultTooFrequentRecurringErr = 36 [(magnetiq_fault_err_code_value) = {
    message: "Too frequent recurring",
    integration_error: ExceedsTransactionFrequencyLimit,
  }, (mvp.from_string) = "510115"];
  MagnetiqFaultRecurringEndDateRequiredErr = 37 [(magnetiq_fault_err_code_value) = {
    message: "Recurring.EndDate required",
    integration_error: TransactionDeclinedByAcquirer,
  }, (mvp.from_string) = "710116"];
  MagnetiqFaultRecurringFrequencyRequiredErr = 38 [(magnetiq_fault_err_code_value) = {
    message: "Recurring.Frequency required",
    integration_error: TransactionDeclinedByAcquirer,
  }, (mvp.from_string) = "710117"];
  MagnetiqFaultRecurringEndDateIncorrectErr = 39 [(magnetiq_fault_err_code_value) = {
    message: "Recurring.EndDate is incorrect",
    integration_error: TransactionDeclinedByAcquirer,
  }, (mvp.from_string) = "610118"];
  MagnetiqFaultRecurringEndDateFutureErr = 40 [(magnetiq_fault_err_code_value) = {
    message: "Recurring.EndDate must be in future",
    integration_error: TransactionDeclinedByAcquirer,
  }, (mvp.from_string) = "510119"];
  MagnetiqFaultRecurringFrequencyGreaterThanZeroErr = 41 [(magnetiq_fault_err_code_value) = {
    message: "Recurring.Frequency must be greater than 0",
    integration_error: TransactionDeclinedByAcquirer,
  }, (mvp.from_string) = "510120"];
  MagnetiqFaultRecurringIDRequiredErr = 42 [(magnetiq_fault_err_code_value) = {
    message: "Recurring.ID is required",
    integration_error: TransactionDeclinedByAcquirer,
  }, (mvp.from_string) = "710121"];
  MagnetiqFaultRemoteAddressRequiredErr = 43 [(magnetiq_fault_err_code_value) = {
    message: "RemoteAddress is required",
    integration_error: TransactionDeclinedByAcquirer,
  }, (mvp.from_string) = "710122"];
  MagnetiqFaultInvalidSignatureErr = 44 [(magnetiq_fault_err_code_value) = {
    message: "Invalid signature",
    integration_error: TransactionDeclinedByAcquirer,
  }, (mvp.from_string) = "110201"];
  MagnetiqFaultRequestRequiredErr = 45 [(magnetiq_fault_err_code_value) = {
    message: "Request is required",
    integration_error: TransactionDeclinedByAcquirer,
  }, (mvp.from_string) = "710203"];
  MagnetiqFaultInterfaceRequiredErr = 46 [(magnetiq_fault_err_code_value) = {
    message: "INTERFACE is required",
    integration_error: TransactionDeclinedByAcquirer,
  }, (mvp.from_string) = "710204"];
  MagnetiqFaultKeyIndexRequiredErr = 47 [(magnetiq_fault_err_code_value) = {
    message: "KEYINDEX is required",
    integration_error: TransactionDeclinedByAcquirer,
  }, (mvp.from_string) = "710205"];
  MagnetiqFaultKeyRequiredErr = 48 [(magnetiq_fault_err_code_value) = {
    message: "KEY is required",
    integration_error: TransactionDeclinedByAcquirer,
  }, (mvp.from_string) = "710206"];
  MagnetiqFaultDataRequiredErr = 49 [(magnetiq_fault_err_code_value) = {
    message: "DATA is required",
    integration_error: TransactionDeclinedByAcquirer,
  }, (mvp.from_string) = "710207"];
  MagnetiqFaultSignatureRequiredErr = 50 [(magnetiq_fault_err_code_value) = {
    message: "SIGNATURE is required",
    integration_error: TransactionDeclinedByAcquirer,
  }, (mvp.from_string) = "710208"];
  MagnetiqFaultXMLDeserializationFailedErr = 51 [(magnetiq_fault_err_code_value) = {
    message: "XML deserialization failed",
    integration_error: TransactionDeclinedByAcquirer,
  }, (mvp.from_string) = "110209"];
  MagnetiqFaultXMLSerializationFailedErr = 52 [(magnetiq_fault_err_code_value) = {
    message: "XML serialization failed",
    integration_error: TransactionDeclinedByAcquirer,
  }, (mvp.from_string) = "110210"];
  MagnetiqFaultDataDecryptionFailedErr = 53 [(magnetiq_fault_err_code_value) = {
    message: "Data decryption failed",
    integration_error: TransactionDeclinedByAcquirer,
  }, (mvp.from_string) = "110301"];
  MagnetiqFaultDataEncryptionFailedErr = 54 [(magnetiq_fault_err_code_value) = {
    message: "Data encryption failed",
    integration_error: TransactionDeclinedByAcquirer,
  }, (mvp.from_string) = "110302"];
  MagnetiqFaultSignatureVerificationFailedErr = 55 [(magnetiq_fault_err_code_value) = {
    message: "Signature verification failed",
    integration_error: TransactionDeclinedByAcquirer,
  }, (mvp.from_string) = "110401"];
  MagnetiqFaultSigningFailedErr = 56 [(magnetiq_fault_err_code_value) = {
    message: "Signing failed",
    integration_error: TransactionDeclinedByAcquirer,
  }, (mvp.from_string) = "110402"];
  MagnetiqFaultPaymentNotFoundErr = 57 [(magnetiq_fault_err_code_value) = {
    message: "Payment not found",
    integration_error: TransactionDeclinedByAcquirer,
  }, (mvp.from_string) = "210501"];
  MagnetiqFaultInvalidPaymentModeAuthenticationErr = 58 [(magnetiq_fault_err_code_value) = {
    message: "Could not authenticate payment, invalid Payment.Mode",
    integration_error: TransactionDeclinedByAcquirer,
  }, (mvp.from_string) = "610602"];
  MagnetiqFaultPaymentRequiredErr = 59 [(magnetiq_fault_err_code_value) = {
    message: "Payment is required",
    integration_error: TransactionDeclinedByAcquirer,
  }, (mvp.from_string) = "710701"];
  MagnetiqFaultInvalidPaymentModeCreationErr = 60 [(magnetiq_fault_err_code_value) = {
    message: "Could not create payment, invalid Payment.Mode",
    integration_error: TransactionDeclinedByAcquirer,
  }, (mvp.from_string) = "610702"];
  MagnetiqFaultTokensDisabledErr = 61 [(magnetiq_fault_err_code_value) = {
    message: "Tokens are disabled",
    integration_error: TransactionDeclinedByAcquirer,
  }, (mvp.from_string) = "111101"];
  MagnetiqFaultInvalidTokenTypeErr = 62 [(magnetiq_fault_err_code_value) = {
    message: "Request holds invalid Token type",
    integration_error: TransactionDeclinedByAcquirer,
  }, (mvp.from_string) = "200203"];
  MagnetiqFaultUnableDecryptTokenErr = 63 [(magnetiq_fault_err_code_value) = {
    message: "System was unable to decrypt the received token",
    integration_error: TransactionDeclinedByAcquirer,
  }, (mvp.from_string) = "200204"];
  MagnetiqFaultIncorrectAmountErr = 64 [(magnetiq_fault_err_code_value) = {
    message: "Order Amount is incorrect",
    integration_error: TransactionDeclinedByAcquirer,
  }, (mvp.from_string) = "200205"];
  MagnetiqFaultIncorrectCurrencyErr = 65 [(magnetiq_fault_err_code_value) = {
    message: "Order currency is incorrect",
    integration_error: TransactionDeclinedByAcquirer,
  }, (mvp.from_string) = "200206"];
  MagnetiqFaultIncorrectCryptogramErr = 66 [(magnetiq_fault_err_code_value) = {
    message: "Cryptogram field is incorrect",
    integration_error: TransactionDeclinedByAcquirer,
  }, (mvp.from_string) = "200207"];
  MagnetiqFaultIncorrectTokenModeErr = 67 [(magnetiq_fault_err_code_value) = {
    message: "Token mode value is incorrect",
    integration_error: TransactionDeclinedByAcquirer,
  }, (mvp.from_string) = "605106"];
}

message MagnetiqActionCodeRef {
  string message = 1;
  processing.integration.integration.IntegrationError integration_error = 2;
}

extend google.protobuf.EnumValueOptions {
  MagnetiqActionCodeRef magnetiq_action_code_value = 300005;
}

extend google.protobuf.EnumOptions {
  MagnetiqActionCodeRef default_magnetiq_action_code_value = 300006;
}

enum EnumMagnetiqActionCode {
  option(mvp.default_ref) = "default_magnetiq_action_code_value";
  option(mvp.ref) = "magnetiq_action_code_value";
  option(default_magnetiq_action_code_value) = {
    message: "undefined"
    integration_error: UndefinedError,
  };
  MagnetiqActionCodeApproved = 0 [(magnetiq_action_code_value) = {
    message: "Approved",
  }, (mvp.from_string) = "000"];
  MagnetiqActionCodeDeclineGeneral = 1 [(magnetiq_action_code_value) = {
    message: "Decline (general, no comments)",
    integration_error: TransactionDeclinedByAcquirer,
  }, (mvp.from_string) = "100"];
  MagnetiqActionCodeDeclineExpiredCard = 2 [(magnetiq_action_code_value) = {
    message: "Decline, expired card",
    integration_error: CardHasExpired,
  }, (mvp.from_string) = "101"];
  MagnetiqActionCodeDeclineSuspectedFraud = 3 [(magnetiq_action_code_value) = {
    message: "Decline, suspected fraud",
    integration_error: SuspiciousClient,
  }, (mvp.from_string) = "102"];
  MagnetiqActionCodeDeclineContactAcquirer = 4 [(magnetiq_action_code_value) = {
    message: "Decline, card acceptor contact acquirer",
    integration_error: TransactionDeclinedByAcquirer,
  }, (mvp.from_string) = "103"];
  MagnetiqActionCodeDeclineRestrictedCard = 5 [(magnetiq_action_code_value) = {
    message: "Decline, restricted card",
    integration_error: InvalidCard,
  }, (mvp.from_string) = "104"];
  MagnetiqActionCodeDeclineCallSecurityDept = 6 [(magnetiq_action_code_value) = {
    message: "Decline, card acceptor call acquirer's security department",
    integration_error: TransactionDeclinedByAcquirer,
  }, (mvp.from_string) = "105"];
  MagnetiqActionCodeDeclineReferToIssuer = 7 [(magnetiq_action_code_value) = {
    message: "Decline, refer to card issuer",
    integration_error: TransactionDeclinedByIssuer,
  }, (mvp.from_string) = "107"];
  MagnetiqActionCodeDeclineIssuerSpecialConditions = 8 [(magnetiq_action_code_value) = {
    message: "Decline, refer to card issuer's special conditions",
    integration_error: TransactionDeclinedByIssuer,
  }, (mvp.from_string) = "108"];
  MagnetiqActionCodeDeclineInvalidMerchant = 9 [(magnetiq_action_code_value) = {
    message: "Decline, invalid merchant",
    integration_error: PaymentForbiddenForMerchant,
  }, (mvp.from_string) = "109"];
  MagnetiqActionCodeDeclineInvalidAmount = 10 [(magnetiq_action_code_value) = {
    message: "Decline, invalid amount",
    integration_error: TransactionDeclinedByAcquirer,
  }, (mvp.from_string) = "110"];
  MagnetiqActionCodeDeclineInvalidCardNumber = 11 [(magnetiq_action_code_value) = {
    message: "Decline, invalid card number",
    integration_error: IncorrectCardNumber,
  }, (mvp.from_string) = "111"];
  MagnetiqActionCodeDeclineUnacceptableFee = 12 [(magnetiq_action_code_value) = {
    message: "Decline, unacceptable fee",
    integration_error: TransactionDeclinedByAcquirer,
  }, (mvp.from_string) = "113"];
  MagnetiqActionCodeDeclineNoAccountType = 13 [(magnetiq_action_code_value) = {
    message: "Decline, no account of type requested",
    integration_error: TransactionDeclinedByAcquirer,
  }, (mvp.from_string) = "114"];
  MagnetiqActionCodeDeclineFunctionNotSupported = 14 [(magnetiq_action_code_value) = {
    message: "Decline, requested function not supported",
    integration_error: TransactionDeclinedByAcquirer,
  }, (mvp.from_string) = "115"];
  MagnetiqActionCodeDeclineInsufficientFunds = 15 [(magnetiq_action_code_value) = {
    message: "Decline, not sufficient funds",
    integration_error: InsufficientFunds,
  }, (mvp.from_string) = "116"];
  MagnetiqActionCodeDeclineNoCardRecord = 16 [(magnetiq_action_code_value) = {
    message: "Decline, no card record",
    integration_error: TransactionDeclinedByAcquirer,
  }, (mvp.from_string) = "118"];
  MagnetiqActionCodeDeclineNotPermittedToCardholder = 17 [(magnetiq_action_code_value) = {
    message: "Decline, transaction not permitted to cardholder",
    integration_error: TransactionDeclinedByAcquirer,
  }, (mvp.from_string) = "119"];
  MagnetiqActionCodeDeclineNotPermittedToTerminal = 18 [(magnetiq_action_code_value) = {
    message: "Decline, transaction not permitted to terminal",
    integration_error: TransactionDeclinedByAcquirer,
  }, (mvp.from_string) = "120"];
  MagnetiqActionCodeDeclineExceedsWithdrawalLimit = 19 [(magnetiq_action_code_value) = {
    message: "Decline, exceeds withdrawal amount limit",
    integration_error: ExceedsAmountLimit,
  }, (mvp.from_string) = "121"];
  MagnetiqActionCodeDeclineSecurityViolation = 20 [(magnetiq_action_code_value) = {
    message: "Decline, security violation",
    integration_error: SuspiciousClient,
  }, (mvp.from_string) = "122"];
  MagnetiqActionCodeDeclineExceedsWithdrawalFrequency = 21 [(magnetiq_action_code_value) = {
    message: "Decline, exceeds withdrawal frequency limit",
    integration_error: ExceedsTransactionFrequencyLimit,
  }, (mvp.from_string) = "123"];
  MagnetiqActionCodeDeclineViolationOfLaw = 22 [(magnetiq_action_code_value) = {
    message: "Decline, violation of law",
    integration_error: SuspiciousClient,
  }, (mvp.from_string) = "124"];
  MagnetiqActionCodeDeclineCardNotEffective = 23 [(magnetiq_action_code_value) = {
    message: "Decline, card not effective",
    integration_error: TransactionDeclinedByAcquirer,
  }, (mvp.from_string) = "125"];
  MagnetiqActionCodeDeclineSuspectedCounterfeitCard = 24 [(magnetiq_action_code_value) = {
    message: "Decline, suspected counterfeit card",
    integration_error: SuspiciousClient,
  }, (mvp.from_string) = "129"];
  MagnetiqActionCodeDeclineAdditionalAuthRequired = 25 [(magnetiq_action_code_value) = {
    message: "Decline, additional customer authentication required",
    integration_error: TransactionDeclinedByAcquirer,
  }, (mvp.from_string) = "160"];
  MagnetiqActionCodeDeclineWrongOnlinePinRetry = 26 [(magnetiq_action_code_value) = {
    message: "Decline, wrong online PIN, retry as single tap",
    integration_error: TransactionDeclinedByAcquirer,
  }, (mvp.from_string) = "161"];
  MagnetiqActionCodeDeclineLifecycleViolation = 27 [(magnetiq_action_code_value) = {
    message: "Decline, Lifecycle violation",
    integration_error: TransactionDeclinedByAcquirer,
  }, (mvp.from_string) = "162"];
  MagnetiqActionCodeDeclinePolicyViolation = 28 [(magnetiq_action_code_value) = {
    message: "Decline, Policy violation",
    integration_error: SuspiciousClient,
  }, (mvp.from_string) = "163"];
  MagnetiqActionCodeDeclineFraudSecurityViolation = 29 [(magnetiq_action_code_value) = {
    message: "Decline, Fraud/Security violation",
    integration_error: SuspiciousClient,
  }, (mvp.from_string) = "164"];
  MagnetiqActionCodeDeclineByCardholderWish = 30 [(magnetiq_action_code_value) = {
    message: "Decline, by cardholder's wish",
    integration_error: UndefinedError,
  }, (mvp.from_string) = "180"];
  MagnetiqActionCodeAdviceAcknowledgedNoLiability = 31 [(magnetiq_action_code_value) = {
    message: "Advice acknowledged, no financial liability accepted",
    integration_error: TransactionDeclinedByAcquirer,
  }, (mvp.from_string) = "900"];
  MagnetiqActionCodeAdviceAcknowledgedLiabilityAccepted = 32 [(magnetiq_action_code_value) = {
    message: "Advice acknowledged, financial liability accepted",
    integration_error: TransactionDeclinedByAcquirer,
  }, (mvp.from_string) = "901"];
  MagnetiqActionCodeDeclineInvalidTransaction = 33 [(magnetiq_action_code_value) = {
    message: "Decline reason message: invalid transaction",
    integration_error: TransactionDeclinedByAcquirer,
  }, (mvp.from_string) = "902"];
  MagnetiqActionCodeStatusReEnterTransaction = 34 [(magnetiq_action_code_value) = {
    message: "Status message: re-enter transaction",
    integration_error: TransactionDeclinedByAcquirer,
  }, (mvp.from_string) = "903"];
  MagnetiqActionCodeDeclineFormatError = 35 [(magnetiq_action_code_value) = {
    message: "Decline reason message: format error",
    integration_error: TransactionDeclinedByAcquirer,
  }, (mvp.from_string) = "904"];
  MagnetiqActionCodeDeclineIssuerOrSwitchInoperative = 36 [(magnetiq_action_code_value) = {
    message: "Decline reason message: card issuer or switch inoperative",
    integration_error: TransactionDeclinedByAcquirer,
  }, (mvp.from_string) = "907"];
  MagnetiqActionCodeDeclineDestinationNotFound = 37 [(magnetiq_action_code_value) = {
    message: "Decline reason message: transaction destination cannot be found for routing",
    integration_error: TransactionDeclinedByAcquirer,
  }, (mvp.from_string) = "908"];
  MagnetiqActionCodeDeclineSystemMalfunction = 38 [(magnetiq_action_code_value) = {
    message: "Decline reason message: system malfunction",
    integration_error: TransactionDeclinedByAcquirer,
  }, (mvp.from_string) = "909"];
  MagnetiqActionCodeDeclineIssuerSignedOff = 39 [(magnetiq_action_code_value) = {
    message: "Decline reason message: card issuer signed off",
    integration_error: TransactionDeclinedByAcquirer,
  }, (mvp.from_string) = "910"];
  MagnetiqActionCodeDeclineIssuerTimedOut = 40 [(magnetiq_action_code_value) = {
    message: "Decline reason message: card issuer timed out",
    integration_error: TransactionDeclinedByAcquirer,
  }, (mvp.from_string) = "911"];
  MagnetiqActionCodeDeclineIssuerUnavailable = 41 [(magnetiq_action_code_value) = {
    message: "Decline reason message: card issuer unavailable",
    integration_error: TransactionDeclinedByAcquirer,
  }, (mvp.from_string) = "912"];
  MagnetiqActionCodeDeclineDuplicateTransmission = 42 [(magnetiq_action_code_value) = {
    message: "Decline reason message: duplicate transmission",
    integration_error: TransactionDeclinedByAcquirer,
  }, (mvp.from_string) = "913"];
  MagnetiqActionCodeDeclineNotTraceableToOriginal = 43 [(magnetiq_action_code_value) = {
    message: "Decline reason message: not able to trace back to original transaction",
    integration_error: TransactionDeclinedByAcquirer,
  }, (mvp.from_string) = "914"];
  MagnetiqActionCodeDeclineNoCommKeysAvailable = 44 [(magnetiq_action_code_value) = {
    message: "Decline reason message: no communication keys available for use",
    integration_error: TransactionDeclinedByAcquirer,
  }, (mvp.from_string) = "918"];
  MagnetiqActionCodeDeclineMessageOutOfSequence = 45 [(magnetiq_action_code_value) = {
    message: "Decline reason message: message number out of sequence",
    integration_error: TransactionDeclinedByAcquirer,
  }, (mvp.from_string) = "922"];
  MagnetiqActionCodeStatusRequestInProgress = 46 [(magnetiq_action_code_value) = {
    message: "Status message: request in progress",
    integration_error: TransactionDeclinedByAcquirer,
  }, (mvp.from_string) = "923"];
  MagnetiqActionCodeDeclineBusinessViolation = 47 [(magnetiq_action_code_value) = {
    message: "Decline reason message: violation of business arrangement",
    integration_error: TransactionDeclinedByAcquirer,
  }, (mvp.from_string) = "950"];
  MagnetiqActionCode3DSecureUnavailable = 48 [(magnetiq_action_code_value) = {
    message: "3D Secure authentication is currently unavailable (Enrollment status = E)",
    integration_error: TransactionDeclinedByAcquirer,
  }, (mvp.from_string) = "181"];
  MagnetiqActionCode3DSecureAuthFailed = 49 [(magnetiq_action_code_value) = {
    message: "3D Secure authentication failed (Authentication status = N)",
    integration_error: ThreeDSAuthFailed,
  }, (mvp.from_string) = "182"];
  MagnetiqActionCodeAgreementNotObtained = 50 [(magnetiq_action_code_value) = {
    message: "Could not obtain agreement for this payment (Currency or card type)",
    integration_error: TransactionDeclinedByAcquirer,
  }, (mvp.from_string) = "940"];
  MagnetiqActionCodeAgreementSuspendedOrClosed = 51 [(magnetiq_action_code_value) = {
    message: "Agreement suspended or closed",
    integration_error: TransactionDeclinedByAcquirer,
  }, (mvp.from_string) = "941"];
  MagnetiqActionCodeOriginalCreditIneligible = 52 [(magnetiq_action_code_value) = {
    message: "Original credit transaction is ineligible for this PAN (at least one deposited payment required)",
    integration_error: TransactionDeclinedByAcquirer,
  }, (mvp.from_string) = "942"];
  MagnetiqActionCode3DSecureEnrollmentVerificationFailed = 53 [(magnetiq_action_code_value) = {
    message: "Card enrollment verification in 3D Secure failed (Incorrect VeRes; DS not available; merchant is not participating in 3D)",
    integration_error: ThreeDSAuthFailed,
  }, (mvp.from_string) = "945"];
  MagnetiqActionCode3DSecurePaResExtractionFailed = 54 [(magnetiq_action_code_value) = {
    message: "3D Secure field extraction from PaRes failed (Incorrect PaRes; Incorrect certificate)",
    integration_error: InvalidThreeDSecureParameters,
  }, (mvp.from_string) = "946"];
  MagnetiqActionCodeSuspectedFraud = 55 [(magnetiq_action_code_value) = {
    message: "Suspected fraud (MAGNETIQ antifraud system blocked this payment. Contact your client-manager for more information)",
    integration_error: SuspiciousClient,
  }, (mvp.from_string) = "948"];
  MagnetiqActionCodeAbandonedTimeout = 56 [(magnetiq_action_code_value) = {
    message: "Abandoned (Timed out)",
    integration_error: UnavailableAcquirer,
  }, (mvp.from_string) = "949"];
  MagnetiqActionCodeLimitsExceeded = 57 [(magnetiq_action_code_value) = {
    message: "MAGNETIQ Limits exceeded (MAGNETIQ antifraud system blocked this payment due to amount limitations in force. Contact your client-manager for more information)",
    integration_error: ExceedsAmountLimit,
  }, (mvp.from_string) = "951"];
}

message MagnetiqModeRef {
  string code = 1;
  string description = 2;
}

extend google.protobuf.EnumValueOptions {
  MagnetiqModeRef magnetiq_mode_value = 300007;
}

extend google.protobuf.EnumOptions {
  MagnetiqModeRef default_magnetiq_mode_value = 300008;
}

enum EnumMagnetiqModeCode {
  option(mvp.default_ref) = "default_magnetiq_mode_value";
  option(mvp.ref) = "magnetiq_mode_value";
  option(default_magnetiq_mode_value) = {
    description: "undefined"
  };
  MagnetiqModeNone3DS = 0 [(magnetiq_mode_value) = {
    code: "4",
    description: "Non-3D Secure",
  }];
  MagnetiqMode3DS = 1 [(magnetiq_mode_value) = {
    code: "6",
    description: "3D Secure",
  }];
  MagnetiqModeOneClickPayIn = 2 [(magnetiq_mode_value) = {
    code: "9"
    description: "Cardholder Not Present",
  }];
}