edition = "2023";

package processing.transaction.transaction_refund;

option go_package = "git.local/sensitive/innerpb/processing/grpc";

import "google/protobuf/timestamp.proto";

service Refund {
  rpc GetByTransactionIDV1(TransactionRequestDataV1) returns (TransactionResponseDataV1) {}
}

message TransactionRequestDataV1 {
  uint64 transaction_id = 1;
}

message TransactionResponseDataV1 {
  repeated TransactionRefundResponseV1 response = 1;
}

message TransactionRefundResponseV1 {
  uint64 transaction_id = 1;
  string reason_name = 2;
  string status = 3;
  double amount = 4;
  string bank_reference_id = 5;
  string bank_message = 6;
  google.protobuf.Timestamp created_at = 7;
}