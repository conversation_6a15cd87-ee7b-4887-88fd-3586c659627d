package test

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"net/url"
	"strconv"
	"testing"

	"github.com/stretchr/testify/assert"

	"git.local/sensitive/local/internal/domain"
)

func TestPrimalPayOut(t *testing.T) {
	t.Skip()

	projectReferenceID := generateSecretKey()

	testData := []struct {
		name         string
		req          *domain.PrimalPayoutRequest
		expectedResp map[string]interface{}
		expectedCode int
	}{
		{
			name: "success_response",
			req: &domain.PrimalPayoutRequest{
				MerchantId:         devMerchantID,
				ProjectId:          devProjectID,
				ProjectClientId:    devProjectClientID,
				Amount:             10.0,
				FailureRedirectUrl: "https://www.youtube.com",
				SuccessRedirectUrl: "https://www.youtube.com",
				CallbackUrl:        devCallbackUrl,
				Description:        "kambartest",
				//ConfirmUrl:         "",
				ProjectReferenceId: projectReferenceID,
				AdditionalData: map[string]string{
					"bonus": "0",
				},
				IsHold:         false,
				ProjectOrderID: "2",
			},
			expectedResp: map[string]interface{}{
				"status":  true,
				"message": "Success",
			},
			expectedCode: http.StatusOK,
		},
		{
			name: "validation_error",
			expectedResp: map[string]interface{}{
				"status":      false,
				"status_code": float64(1021),
				"message":     "request validation error: EOF",
				"result": map[string]interface{}{
					"error": "EOF",
				},
			},
			expectedCode: http.StatusBadRequest,
		},
	}

	for _, tt := range testData {
		t.Run(tt.name, func(t *testing.T) {
			MakePrimalPayOut(t, tt.req, tt.expectedResp, tt.expectedCode)
		})
	}
}

func MakePrimalPayOut(
	t *testing.T, req *domain.PrimalPayoutRequest,
	expectedResp map[string]interface{}, expectedCode int,
) (string, uint64) {
	var body []byte
	var err error

	sign := generateToken(req, "local")

	if req != nil {
		body, err = json.Marshal(req)
		if err != nil {
			t.Fatal(err)
		}
	}

	request, err := http.NewRequest(http.MethodPost, domainURL+PrimalPayOut, bytes.NewBuffer(body))
	if err != nil {
		t.Fatal(err)
	}

	request.Header.Add("Authorization", "Bearer "+sign)
	request.Header.Add("Content-Type", "application/json")

	resp, err := http.DefaultClient.Do(request)
	if err != nil {
		t.Fatal(err)
	}
	defer resp.Body.Close()

	assert.Equal(t, expectedCode, resp.StatusCode, "unexpected HTTP status code")

	bs, err := io.ReadAll(resp.Body)
	if err != nil {
		t.Fatal(err)
	}

	var actualResp map[string]interface{}
	if err := json.Unmarshal(bs, &actualResp); err != nil {
		t.Fatalf("failed to unmarshal response: %s", err)
	}

	for key, expectedValue := range expectedResp {
		if key == "result" {
			expectedResult := expectedValue.(map[string]interface{})
			actualResult := actualResp[key].(map[string]interface{})

			for resultKey, resultValue := range expectedResult {
				assert.Equal(t, resultValue, actualResult[resultKey], "unexpected value in result.%s", resultKey)
			}
		} else {
			assert.Equal(t, expectedValue, actualResp[key], "unexpected value for key %s", key)
		}
	}

	if expectedCode == http.StatusOK {
		assert.Contains(t, actualResp["result"], "https://", "result should contain a valid URL")
	}

	formattedResponse, err := json.MarshalIndent(actualResp, "", "  ")
	if err != nil {
		t.Fatal("Error formatting response: ", err)
	}
	fmt.Println("Formatted Response:", string(formattedResponse))

	link, ok := actualResp["result"].(string) // if it is success case, we will receive url, so we can assert it to string
	if !ok {
		t.Fatal("different object came into result")
	}

	u, err := url.Parse(link)
	if err != nil {
		t.Fatal(err)
	}

	params := u.Query()

	transactionId := params.Get("transaction_id")
	if transactionId == "" {
		t.Fatal("transactionId is empty")
	}

	trID, err := strconv.ParseUint(transactionId, 10, 64)
	if err != nil {
		t.Fatal(err)
	}

	hash := params.Get("hash")
	if hash == "" {
		t.Fatal("hash is empty")
	}

	return hash, trID
}
