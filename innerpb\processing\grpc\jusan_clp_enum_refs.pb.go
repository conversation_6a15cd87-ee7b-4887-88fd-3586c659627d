// Code generated by protoc-gen-go-mvp. DO NOT EDIT.
// versions:
// - protoc-gen-go-mvp v2.0.1
// created by mvp authors

package grpc

func (x JusanResponseCodeClp) Code() string {
	switch x {
	case JusanResponseCodeClp_ClpResponseUnavailable:
		return "11"
	case JusanResponseCodeClp_ClpIncorrectOrder:
		return "12"
	case JusanResponseCodeClp_ClpIncorrectAmount:
		return "13"
	case JusanResponseCodeClp_ClpIncorrectCurrency:
		return "14"
	case JusanResponseCodeClp_ClpMPIUnavailable:
		return "15"
	case JusanResponseCodeClp_ClpDbUnavailable:
		return "16"
	case JusanResponseCodeClp_ClpForbiddenMerchant:
		return "171"
	case JusanResponseCodeClp_ClpMerchantForbidden:
		return "172 "
	case JusanResponseCodeClp_ClpRequestAlreadyCompleted:
		return "18"
	case JusanResponseCodeClp_ClpIncorrectCardExpDate:
		return "19"
	case JusanResponseCodeClp_ClpIncorrectTerminal:
		return "20"
	case JusanResponseCodeClp_ClpInvalidSign:
		return "21"
	case JusanResponseCodeClp_ClpCurrencyNotFound:
		return "22"
	case JusanResponseCodeClp_ClpLimitExceeds:
		return "23"
	case JusanResponseCodeClp_ClpEmptyField:
		return "24"
	case JusanResponseCodeClp_ClpSizeLessSymbols:
		return "25"
	case JusanResponseCodeClp_ClpSizeMoreSymbols:
		return "26"
	case JusanResponseCodeClp_ClpInvalidValue:
		return "27"
	case JusanResponseCodeClp_ClpMPIError3DS:
		return "28"
	case JusanResponseCodeClp_ClpInvalidCardType:
		return "29"
	case JusanResponseCodeClp_ClpPaymentNotFound:
		return "30"
	case JusanResponseCodeClp_ClpEmptyClientKey:
		return "31"
	case JusanResponseCodeClp_ClpForbiddenTerminal:
		return "32"
	case JusanResponseCodeClp_ClpTokenNotFound:
		return "33"
	case JusanResponseCodeClp_ClpIncorrectBlockAmount:
		return "34"
	case JusanResponseCodeClp_ClpUnknownError:
		return "99"
	case JusanResponseCodeClp_ClpUnavailableService:
		return "41"
	case JusanResponseCodeClp_ClpAmountIncorrect:
		return "42"
	case JusanResponseCodeClp_ClpUnavailableDb:
		return "43"
	case JusanResponseCodeClp_ClpIncorrectMerchant:
		return "44"
	case JusanResponseCodeClp_ClpMerchantNotFound:
		return "17"
	case JusanResponseCodeClp_ClpOrderNotFound:
		return "45"
	case JusanResponseCodeClp_ClpSignInvalid:
		return "46"
	case JusanResponseCodeClp_ClpRefundAmountIncorrect:
		return "47"
	case JusanResponseCodeClp_ClpIncorrectStatus:
		return "48"
	case JusanResponseCodeClp_ClpIncorrectValue:
		return "50"
	case JusanResponseCodeClp_ClpTerminalForbidden:
		return "51"
	case JusanResponseCodeClp_ClpForbiddenOperation:
		return "52"
	case JusanResponseCodeClp_ClpDuplicateDescription:
		return "53"
	case JusanResponseCodeClp_ClpRefundHandleError:
		return "F"
	case JusanResponseCodeClp_ClpPaymentError:
		return "E"
	case JusanResponseCodeClp_ClpPaymentExpired:
		return "c"
	case JusanResponseCodeClp_ClpAuthError:
		return "3"
	case JusanResponseCodeClp_ClpIncorrectCardNum:
		return "37"
	case JusanResponseCodeClp_ClpSuspectedFraud:
		return "59"
	case JusanResponseCodeClp_ClpUndefinedError:
		return "1"
	case JusanResponseCodeClp_ClpExceedsLimit:
		return "61"
	case JusanResponseCodeClp_ClpLimitedCard:
		return "62"
	case JusanResponseCodeClp_ClpTransactionDeclined:
		return "05"
	case JusanResponseCodeClp_ClpNotEnough:
		return "2"
	case JusanResponseCodeClp_ClpLimitExceeded:
		return "65"
	case JusanResponseCodeClp_ClpErrorAuth:
		return "-19"
	case JusanResponseCodeClp_ClpCardInactive:
		return "07"
	default:
		return "0"
	}
}

func (x JusanResponseCodeClp) IntegrationError() IntegrationError {
	switch x {
	case JusanResponseCodeClp_ClpResponseUnavailable:
		return IntegrationError_UnavailableAcquirer
	case JusanResponseCodeClp_ClpIncorrectOrder:
		return IntegrationError_TransactionDeclinedByAcquirer
	case JusanResponseCodeClp_ClpIncorrectAmount:
		return IntegrationError_TransactionDeclinedByAcquirer
	case JusanResponseCodeClp_ClpIncorrectCurrency:
		return IntegrationError_TransactionDeclinedByAcquirer
	case JusanResponseCodeClp_ClpMPIUnavailable:
		return IntegrationError_UnavailableAcquirer
	case JusanResponseCodeClp_ClpDbUnavailable:
		return IntegrationError_UnavailableAcquirer
	case JusanResponseCodeClp_ClpForbiddenMerchant:
		return IntegrationError_PaymentForbiddenForMerchant
	case JusanResponseCodeClp_ClpMerchantForbidden:
		return IntegrationError_PaymentForbiddenForMerchant
	case JusanResponseCodeClp_ClpRequestAlreadyCompleted:
		return IntegrationError_TransactionDeclinedByAcquirer
	case JusanResponseCodeClp_ClpIncorrectCardExpDate:
		return IntegrationError_IncorrectCardExpDate
	case JusanResponseCodeClp_ClpIncorrectTerminal:
		return IntegrationError_TransactionDeclinedByAcquirer
	case JusanResponseCodeClp_ClpInvalidSign:
		return IntegrationError_TransactionDeclinedByAcquirer
	case JusanResponseCodeClp_ClpCurrencyNotFound:
		return IntegrationError_TransactionDeclinedByAcquirer
	case JusanResponseCodeClp_ClpLimitExceeds:
		return IntegrationError_ExceedsAmountLimit
	case JusanResponseCodeClp_ClpEmptyField:
		return IntegrationError_TransactionDeclinedByAcquirer
	case JusanResponseCodeClp_ClpSizeLessSymbols:
		return IntegrationError_TransactionDeclinedByAcquirer
	case JusanResponseCodeClp_ClpSizeMoreSymbols:
		return IntegrationError_TransactionDeclinedByAcquirer
	case JusanResponseCodeClp_ClpInvalidValue:
		return IntegrationError_TransactionDeclinedByAcquirer
	case JusanResponseCodeClp_ClpMPIError3DS:
		return IntegrationError_TransactionDeclinedByAcquirer
	case JusanResponseCodeClp_ClpInvalidCardType:
		return IntegrationError_InvalidCard
	case JusanResponseCodeClp_ClpPaymentNotFound:
		return IntegrationError_TransactionDeclinedByAcquirer
	case JusanResponseCodeClp_ClpEmptyClientKey:
		return IntegrationError_TransactionDeclinedByAcquirer
	case JusanResponseCodeClp_ClpForbiddenTerminal:
		return IntegrationError_PaymentForbiddenForMerchant
	case JusanResponseCodeClp_ClpTokenNotFound:
		return IntegrationError_InvalidCard
	case JusanResponseCodeClp_ClpIncorrectBlockAmount:
		return IntegrationError_TransactionDeclinedByAcquirer
	case JusanResponseCodeClp_ClpUnknownError:
		return IntegrationError_TransactionDeclinedByAcquirer
	case JusanResponseCodeClp_ClpUnavailableService:
		return IntegrationError_UnavailableAcquirer
	case JusanResponseCodeClp_ClpAmountIncorrect:
		return IntegrationError_TransactionDeclinedByAcquirer
	case JusanResponseCodeClp_ClpUnavailableDb:
		return IntegrationError_UnavailableAcquirer
	case JusanResponseCodeClp_ClpIncorrectMerchant:
		return IntegrationError_TransactionDeclinedByAcquirer
	case JusanResponseCodeClp_ClpMerchantNotFound:
		return IntegrationError_PaymentForbiddenForMerchant
	case JusanResponseCodeClp_ClpOrderNotFound:
		return IntegrationError_TransactionDeclinedByAcquirer
	case JusanResponseCodeClp_ClpSignInvalid:
		return IntegrationError_TransactionDeclinedByAcquirer
	case JusanResponseCodeClp_ClpRefundAmountIncorrect:
		return IntegrationError_TransactionDeclinedByAcquirer
	case JusanResponseCodeClp_ClpIncorrectStatus:
		return IntegrationError_TransactionDeclinedByAcquirer
	case JusanResponseCodeClp_ClpIncorrectValue:
		return IntegrationError_TransactionDeclinedByAcquirer
	case JusanResponseCodeClp_ClpTerminalForbidden:
		return IntegrationError_PaymentForbiddenForMerchant
	case JusanResponseCodeClp_ClpForbiddenOperation:
		return IntegrationError_PaymentForbiddenForMerchant
	case JusanResponseCodeClp_ClpDuplicateDescription:
		return IntegrationError_TransactionDeclinedByAcquirer
	case JusanResponseCodeClp_ClpRefundHandleError:
		return IntegrationError_TransactionDeclinedByAcquirer
	case JusanResponseCodeClp_ClpPaymentError:
		return IntegrationError_TransactionDeclinedByAcquirer
	case JusanResponseCodeClp_ClpPaymentExpired:
		return IntegrationError_TransactionDeclinedByAcquirer
	case JusanResponseCodeClp_ClpAuthError:
		return IntegrationError_TransactionDeclinedByAcquirer
	case JusanResponseCodeClp_ClpIncorrectCardNum:
		return IntegrationError_IncorrectCardExpDate
	case JusanResponseCodeClp_ClpSuspectedFraud:
		return IntegrationError_SuspiciousClient
	case JusanResponseCodeClp_ClpUndefinedError:
		return IntegrationError_UndefinedError
	case JusanResponseCodeClp_ClpExceedsLimit:
		return IntegrationError_ExceedsAmountLimit
	case JusanResponseCodeClp_ClpLimitedCard:
		return IntegrationError_BlockedCard
	case JusanResponseCodeClp_ClpTransactionDeclined:
		return IntegrationError_TransactionDeclinedByAcquirer
	case JusanResponseCodeClp_ClpNotEnough:
		return IntegrationError_InsufficientFunds
	case JusanResponseCodeClp_ClpLimitExceeded:
		return IntegrationError_ExceedsTransactionFrequencyLimit
	case JusanResponseCodeClp_ClpErrorAuth:
		return IntegrationError_TransactionDeclinedByAcquirer
	case JusanResponseCodeClp_ClpCardInactive:
		return IntegrationError_CardHasExpired
	default:
		return IntegrationError_None
	}
}

func (x JusanResponseCodeClp) Message() string {
	switch x {
	case JusanResponseCodeClp_ClpResponseUnavailable:
		return "Сервис временно недоступен, попробуйте позже"
	case JusanResponseCodeClp_ClpIncorrectOrder:
		return "Неправильное значение в поле ORDER:"
	case JusanResponseCodeClp_ClpIncorrectAmount:
		return "Неправильная сумма: "
	case JusanResponseCodeClp_ClpIncorrectCurrency:
		return "Неправильная валюта:"
	case JusanResponseCodeClp_ClpMPIUnavailable:
		return "Сервис MPI временно недоступен, попробуйте позже"
	case JusanResponseCodeClp_ClpDbUnavailable:
		return "Сервис Db временно недоступен, попробуйте позже"
	case JusanResponseCodeClp_ClpForbiddenMerchant:
		return "Коммерсанту запрещено выполнение операций"
	case JusanResponseCodeClp_ClpMerchantForbidden:
		return "Коммерсанту запрещено проведение операций в соответствии с Законом о ПОД/ФТ"
	case JusanResponseCodeClp_ClpRequestAlreadyCompleted:
		return "Запрос уже выполнялся"
	case JusanResponseCodeClp_ClpIncorrectCardExpDate:
		return "Неправильная дата дейстия карты (MM/ГГ)"
	case JusanResponseCodeClp_ClpIncorrectTerminal:
		return "Неправильное значение в поле TERMINAL:"
	case JusanResponseCodeClp_ClpInvalidSign:
		return "Неправильная подпись!"
	case JusanResponseCodeClp_ClpCurrencyNotFound:
		return "Не найден курс валюты"
	case JusanResponseCodeClp_ClpLimitExceeds:
		return "Превышен лимит!"
	case JusanResponseCodeClp_ClpEmptyField:
		return "Не указано значение в поле"
	case JusanResponseCodeClp_ClpSizeLessSymbols:
		return "Размер значения в поле менее симоволов"
	case JusanResponseCodeClp_ClpSizeMoreSymbols:
		return "Размер значения в поле больше симоволов"
	case JusanResponseCodeClp_ClpInvalidValue:
		return "Введите валидное значение в поле"
	case JusanResponseCodeClp_ClpMPIError3DS:
		return "Ошибка MPI при выполнении проверки 3DS:"
	case JusanResponseCodeClp_ClpInvalidCardType:
		return "Недопустимый тип карты"
	case JusanResponseCodeClp_ClpPaymentNotFound:
		return "Счет на оплату не найден"
	case JusanResponseCodeClp_ClpEmptyClientKey:
		return "Не передан ключ указанного клиента"
	case JusanResponseCodeClp_ClpForbiddenTerminal:
		return "Для терминала запрещена токенизация"
	case JusanResponseCodeClp_ClpTokenNotFound:
		return "Для данного клиента в вашей организации не зарегистрирован токен"
	case JusanResponseCodeClp_ClpIncorrectBlockAmount:
		return "Неверная сумма блокирования, заявка отменена!"
	case JusanResponseCodeClp_ClpUnknownError:
		return "Неизвестная ошибка: "
	case JusanResponseCodeClp_ClpUnavailableService:
		return "Сервис временно недоступен, попробуйте позже"
	case JusanResponseCodeClp_ClpAmountIncorrect:
		return "Неправильная сумма"
	case JusanResponseCodeClp_ClpUnavailableDb:
		return "Сервис Db временно недоступен, попробуйте позже "
	case JusanResponseCodeClp_ClpIncorrectMerchant:
		return "Неправильное значение в поле MERCHANT"
	case JusanResponseCodeClp_ClpMerchantNotFound:
		return "Коммерсант не найден"
	case JusanResponseCodeClp_ClpOrderNotFound:
		return "Заявка ORDER не найдена"
	case JusanResponseCodeClp_ClpSignInvalid:
		return "Неправильная подпись!"
	case JusanResponseCodeClp_ClpRefundAmountIncorrect:
		return "Сумма возврта '%s' больше чем сумма заказа"
	case JusanResponseCodeClp_ClpIncorrectStatus:
		return "Текущий статус заказа не позволяет делать возврат/отмену"
	case JusanResponseCodeClp_ClpIncorrectValue:
		return "Неправильное значение"
	case JusanResponseCodeClp_ClpTerminalForbidden:
		return "Текущий статус терминала не позволяет производить операции"
	case JusanResponseCodeClp_ClpForbiddenOperation:
		return "Операция отмены/возврата через API для терминала запрещена"
	case JusanResponseCodeClp_ClpDuplicateDescription:
		return "Дублирование описания отмены"
	case JusanResponseCodeClp_ClpRefundHandleError:
		return "Ошибка при обработке возврата"
	case JusanResponseCodeClp_ClpPaymentError:
		return "Ошибка при оплате"
	case JusanResponseCodeClp_ClpPaymentExpired:
		return "Счет на оплату устарел"
	case JusanResponseCodeClp_ClpAuthError:
		return "Ошибка авторизации платежа"
	case JusanResponseCodeClp_ClpIncorrectCardNum:
		return "Неправильный номер карты"
	case JusanResponseCodeClp_ClpSuspectedFraud:
		return "Suspected fraud"
	case JusanResponseCodeClp_ClpUndefinedError:
		return "Неизвестная ошибка"
	case JusanResponseCodeClp_ClpExceedsLimit:
		return "Превышает лимит суммы"
	case JusanResponseCodeClp_ClpLimitedCard:
		return "Карта с ограниченным доступом"
	case JusanResponseCodeClp_ClpTransactionDeclined:
		return "Транзакция отклонена банком или МПС"
	case JusanResponseCodeClp_ClpNotEnough:
		return "Не достаточно средств"
	case JusanResponseCodeClp_ClpLimitExceeded:
		return "Превышен лимит"
	case JusanResponseCodeClp_ClpErrorAuth:
		return "Ошибка авторизации"
	case JusanResponseCodeClp_ClpCardInactive:
		return "Ваша карта не активна"
	default:
		return "default"
	}
}

func (x JusanResponseCodeClp) TransactionStatus() EnumTransactionStatus {
	switch x {
	case JusanResponseCodeClp_ClpResponseUnavailable:
		return EnumTransactionStatus_TransactionStatusFailed
	case JusanResponseCodeClp_ClpIncorrectOrder:
		return EnumTransactionStatus_TransactionStatusFailed
	case JusanResponseCodeClp_ClpIncorrectAmount:
		return EnumTransactionStatus_TransactionStatusFailed
	case JusanResponseCodeClp_ClpIncorrectCurrency:
		return EnumTransactionStatus_TransactionStatusFailed
	case JusanResponseCodeClp_ClpMPIUnavailable:
		return EnumTransactionStatus_TransactionStatusFailed
	case JusanResponseCodeClp_ClpDbUnavailable:
		return EnumTransactionStatus_TransactionStatusFailed
	case JusanResponseCodeClp_ClpForbiddenMerchant:
		return EnumTransactionStatus_TransactionStatusFailed
	case JusanResponseCodeClp_ClpMerchantForbidden:
		return EnumTransactionStatus_TransactionStatusFailed
	case JusanResponseCodeClp_ClpRequestAlreadyCompleted:
		return EnumTransactionStatus_TransactionStatusFailed
	case JusanResponseCodeClp_ClpIncorrectCardExpDate:
		return EnumTransactionStatus_TransactionStatusFailed
	case JusanResponseCodeClp_ClpIncorrectTerminal:
		return EnumTransactionStatus_TransactionStatusFailed
	case JusanResponseCodeClp_ClpInvalidSign:
		return EnumTransactionStatus_TransactionStatusFailed
	case JusanResponseCodeClp_ClpCurrencyNotFound:
		return EnumTransactionStatus_TransactionStatusFailed
	case JusanResponseCodeClp_ClpLimitExceeds:
		return EnumTransactionStatus_TransactionStatusFailed
	case JusanResponseCodeClp_ClpEmptyField:
		return EnumTransactionStatus_TransactionStatusFailed
	case JusanResponseCodeClp_ClpSizeLessSymbols:
		return EnumTransactionStatus_TransactionStatusFailed
	case JusanResponseCodeClp_ClpSizeMoreSymbols:
		return EnumTransactionStatus_TransactionStatusFailed
	case JusanResponseCodeClp_ClpInvalidValue:
		return EnumTransactionStatus_TransactionStatusFailed
	case JusanResponseCodeClp_ClpMPIError3DS:
		return EnumTransactionStatus_TransactionStatusFailed
	case JusanResponseCodeClp_ClpInvalidCardType:
		return EnumTransactionStatus_TransactionStatusFailed
	case JusanResponseCodeClp_ClpPaymentNotFound:
		return EnumTransactionStatus_TransactionStatusFailed
	case JusanResponseCodeClp_ClpEmptyClientKey:
		return EnumTransactionStatus_TransactionStatusFailed
	case JusanResponseCodeClp_ClpForbiddenTerminal:
		return EnumTransactionStatus_TransactionStatusFailed
	case JusanResponseCodeClp_ClpTokenNotFound:
		return EnumTransactionStatus_TransactionStatusFailed
	case JusanResponseCodeClp_ClpIncorrectBlockAmount:
		return EnumTransactionStatus_TransactionStatusFailed
	case JusanResponseCodeClp_ClpUnknownError:
		return EnumTransactionStatus_TransactionStatusHolded
	case JusanResponseCodeClp_ClpUnavailableService:
		return EnumTransactionStatus_TransactionStatusFailed
	case JusanResponseCodeClp_ClpAmountIncorrect:
		return EnumTransactionStatus_TransactionStatusFailed
	case JusanResponseCodeClp_ClpUnavailableDb:
		return EnumTransactionStatus_TransactionStatusFailed
	case JusanResponseCodeClp_ClpIncorrectMerchant:
		return EnumTransactionStatus_TransactionStatusFailed
	case JusanResponseCodeClp_ClpMerchantNotFound:
		return EnumTransactionStatus_TransactionStatusFailed
	case JusanResponseCodeClp_ClpOrderNotFound:
		return EnumTransactionStatus_TransactionStatusFailed
	case JusanResponseCodeClp_ClpSignInvalid:
		return EnumTransactionStatus_TransactionStatusFailed
	case JusanResponseCodeClp_ClpRefundAmountIncorrect:
		return EnumTransactionStatus_TransactionStatusFailed
	case JusanResponseCodeClp_ClpIncorrectStatus:
		return EnumTransactionStatus_TransactionStatusFailed
	case JusanResponseCodeClp_ClpIncorrectValue:
		return EnumTransactionStatus_TransactionStatusFailed
	case JusanResponseCodeClp_ClpTerminalForbidden:
		return EnumTransactionStatus_TransactionStatusFailed
	case JusanResponseCodeClp_ClpForbiddenOperation:
		return EnumTransactionStatus_TransactionStatusFailed
	case JusanResponseCodeClp_ClpDuplicateDescription:
		return EnumTransactionStatus_TransactionStatusFailed
	case JusanResponseCodeClp_ClpRefundHandleError:
		return EnumTransactionStatus_TransactionStatusFailed
	case JusanResponseCodeClp_ClpPaymentError:
		return EnumTransactionStatus_TransactionStatusFailed
	case JusanResponseCodeClp_ClpPaymentExpired:
		return EnumTransactionStatus_TransactionStatusFailed
	case JusanResponseCodeClp_ClpAuthError:
		return EnumTransactionStatus_TransactionStatusFailed
	case JusanResponseCodeClp_ClpIncorrectCardNum:
		return EnumTransactionStatus_TransactionStatusFailed
	case JusanResponseCodeClp_ClpSuspectedFraud:
		return EnumTransactionStatus_TransactionStatusFailed
	case JusanResponseCodeClp_ClpUndefinedError:
		return EnumTransactionStatus_TransactionStatusFailed
	case JusanResponseCodeClp_ClpExceedsLimit:
		return EnumTransactionStatus_TransactionStatusFailed
	case JusanResponseCodeClp_ClpLimitedCard:
		return EnumTransactionStatus_TransactionStatusFailed
	case JusanResponseCodeClp_ClpTransactionDeclined:
		return EnumTransactionStatus_TransactionStatusFailed
	case JusanResponseCodeClp_ClpNotEnough:
		return EnumTransactionStatus_TransactionStatusFailed
	case JusanResponseCodeClp_ClpLimitExceeded:
		return EnumTransactionStatus_TransactionStatusFailed
	case JusanResponseCodeClp_ClpErrorAuth:
		return EnumTransactionStatus_TransactionStatusFailed
	case JusanResponseCodeClp_ClpCardInactive:
		return EnumTransactionStatus_TransactionStatusFailed
	default:
		return EnumTransactionStatus_TransactionStatusError
	}
}

// Created reference to JusanResponseCodeClp

//	|	JusanResponseCodeClp                           	|	Code  	|	IntegrationError                                 	|	Message                                                                                                                                       	|	TransactionStatus                            	|
//	|	JusanResponseCodeClp_ClpResponseUnavailable    	|	"11"  	|	IntegrationError_UnavailableAcquirer             	|	"Сервис временно недоступен, попробуйте позже"                                                                                                	|	EnumTransactionStatus_TransactionStatusFailed	|
//	|	JusanResponseCodeClp_ClpIncorrectOrder         	|	"12"  	|	IntegrationError_TransactionDeclinedByAcquirer   	|	"Неправильное значение в поле ORDER:"                                                                                                         	|	EnumTransactionStatus_TransactionStatusFailed	|
//	|	JusanResponseCodeClp_ClpIncorrectAmount        	|	"13"  	|	IntegrationError_TransactionDeclinedByAcquirer   	|	"Неправильная сумма: "                                                                                                                        	|	EnumTransactionStatus_TransactionStatusFailed	|
//	|	JusanResponseCodeClp_ClpIncorrectCurrency      	|	"14"  	|	IntegrationError_TransactionDeclinedByAcquirer   	|	"Неправильная валюта:"                                                                                                                        	|	EnumTransactionStatus_TransactionStatusFailed	|
//	|	JusanResponseCodeClp_ClpMPIUnavailable         	|	"15"  	|	IntegrationError_UnavailableAcquirer             	|	"Сервис MPI временно недоступен, попробуйте позже"                                                                                            	|	EnumTransactionStatus_TransactionStatusFailed	|
//	|	JusanResponseCodeClp_ClpDbUnavailable          	|	"16"  	|	IntegrationError_UnavailableAcquirer             	|	"Сервис Db временно недоступен, попробуйте позже"                                                                                             	|	EnumTransactionStatus_TransactionStatusFailed	|
//	|	JusanResponseCodeClp_ClpForbiddenMerchant      	|	"171" 	|	IntegrationError_PaymentForbiddenForMerchant     	|	"Коммерсанту запрещено выполнение операций"                                                                                                   	|	EnumTransactionStatus_TransactionStatusFailed	|
//	|	JusanResponseCodeClp_ClpMerchantForbidden      	|	"172 "	|	IntegrationError_PaymentForbiddenForMerchant     	|	"Коммерсанту запрещено проведение операций в соответствии с Законом о ПОД/ФТ"                                                                 	|	EnumTransactionStatus_TransactionStatusFailed	|
//	|	JusanResponseCodeClp_ClpRequestAlreadyCompleted	|	"18"  	|	IntegrationError_TransactionDeclinedByAcquirer   	|	"Запрос уже выполнялся"                                                                                                                       	|	EnumTransactionStatus_TransactionStatusFailed	|
//	|	JusanResponseCodeClp_ClpIncorrectCardExpDate   	|	"19"  	|	IntegrationError_IncorrectCardExpDate            	|	"Неправильная дата дейстия карты (MM/ГГ)"                                                                                                     	|	EnumTransactionStatus_TransactionStatusFailed	|
//	|	JusanResponseCodeClp_ClpIncorrectTerminal      	|	"20"  	|	IntegrationError_TransactionDeclinedByAcquirer   	|	"Неправильное значение в поле TERMINAL:"                                                                                                      	|	EnumTransactionStatus_TransactionStatusFailed	|
//	|	JusanResponseCodeClp_ClpInvalidSign            	|	"21"  	|	IntegrationError_TransactionDeclinedByAcquirer   	|	"Неправильная подпись!"                                                                                                                       	|	EnumTransactionStatus_TransactionStatusFailed	|
//	|	JusanResponseCodeClp_ClpCurrencyNotFound       	|	"22"  	|	IntegrationError_TransactionDeclinedByAcquirer   	|	"Не найден курс валюты"                                                                                                                       	|	EnumTransactionStatus_TransactionStatusFailed	|
//	|	JusanResponseCodeClp_ClpLimitExceeds           	|	"23"  	|	IntegrationError_ExceedsAmountLimit              	|	"Превышен лимит!"                                                                                                                             	|	EnumTransactionStatus_TransactionStatusFailed	|
//	|	JusanResponseCodeClp_ClpEmptyField             	|	"24"  	|	IntegrationError_TransactionDeclinedByAcquirer   	|	"Не указано значение в поле"                                                                                                                  	|	EnumTransactionStatus_TransactionStatusFailed	|
//	|	JusanResponseCodeClp_ClpSizeLessSymbols        	|	"25"  	|	IntegrationError_TransactionDeclinedByAcquirer   	|	"Размер значения в поле менее симоволов"                                                                                                      	|	EnumTransactionStatus_TransactionStatusFailed	|
//	|	JusanResponseCodeClp_ClpSizeMoreSymbols        	|	"26"  	|	IntegrationError_TransactionDeclinedByAcquirer   	|	"Размер значения в поле больше симоволов"                                                                                                     	|	EnumTransactionStatus_TransactionStatusFailed	|
//	|	JusanResponseCodeClp_ClpInvalidValue           	|	"27"  	|	IntegrationError_TransactionDeclinedByAcquirer   	|	"Введите валидное значение в поле"                                                                                                            	|	EnumTransactionStatus_TransactionStatusFailed	|
//	|	JusanResponseCodeClp_ClpMPIError3DS            	|	"28"  	|	IntegrationError_TransactionDeclinedByAcquirer   	|	"Ошибка MPI при выполнении проверки 3DS:"                                                                                                     	|	EnumTransactionStatus_TransactionStatusFailed	|
//	|	JusanResponseCodeClp_ClpInvalidCardType        	|	"29"  	|	IntegrationError_InvalidCard                     	|	"Недопустимый тип карты"                                                                                                                      	|	EnumTransactionStatus_TransactionStatusFailed	|
//	|	JusanResponseCodeClp_ClpPaymentNotFound        	|	"30"  	|	IntegrationError_TransactionDeclinedByAcquirer   	|	"Счет на оплату не найден"                                                                                                                    	|	EnumTransactionStatus_TransactionStatusFailed	|
//	|	JusanResponseCodeClp_ClpEmptyClientKey         	|	"31"  	|	IntegrationError_TransactionDeclinedByAcquirer   	|	"Не передан ключ указанного клиента"                                                                                                          	|	EnumTransactionStatus_TransactionStatusFailed	|
//	|	JusanResponseCodeClp_ClpForbiddenTerminal      	|	"32"  	|	IntegrationError_PaymentForbiddenForMerchant     	|	"Для терминала запрещена токенизация"                                                                                                         	|	EnumTransactionStatus_TransactionStatusFailed	|
//	|	JusanResponseCodeClp_ClpTokenNotFound          	|	"33"  	|	IntegrationError_InvalidCard                     	|	"Для данного клиента в вашей организации не зарегистрирован токен"                                                                            	|	EnumTransactionStatus_TransactionStatusFailed	|
//	|	JusanResponseCodeClp_ClpIncorrectBlockAmount   	|	"34"  	|	IntegrationError_TransactionDeclinedByAcquirer   	|	"Неверная сумма блокирования, заявка отменена!"                                                                                               	|	EnumTransactionStatus_TransactionStatusFailed	|
//	|	JusanResponseCodeClp_ClpUnknownError           	|	"99"  	|	IntegrationError_TransactionDeclinedByAcquirer   	|	"Неизвестная ошибка: "                                                                                                                        	|	EnumTransactionStatus_TransactionStatusHolded	|
//	|	JusanResponseCodeClp_ClpUnavailableService     	|	"41"  	|	IntegrationError_UnavailableAcquirer             	|	"Сервис временно недоступен, попробуйте позже"                                                                                                	|	EnumTransactionStatus_TransactionStatusFailed	|
//	|	JusanResponseCodeClp_ClpAmountIncorrect        	|	"42"  	|	IntegrationError_TransactionDeclinedByAcquirer   	|	"Неправильная сумма"                                                                                                                          	|	EnumTransactionStatus_TransactionStatusFailed	|
//	|	JusanResponseCodeClp_ClpUnavailableDb          	|	"43"  	|	IntegrationError_UnavailableAcquirer             	|	"Сервис Db временно недоступен, попробуйте позже "                                                                                            	|	EnumTransactionStatus_TransactionStatusFailed	|
//	|	JusanResponseCodeClp_ClpIncorrectMerchant      	|	"44"  	|	IntegrationError_TransactionDeclinedByAcquirer   	|	"Неправильное значение в поле MERCHANT"                                                                                                       	|	EnumTransactionStatus_TransactionStatusFailed	|
//	|	JusanResponseCodeClp_ClpMerchantNotFound       	|	"17"  	|	IntegrationError_PaymentForbiddenForMerchant     	|	"Коммерсант не найден"                                                                                                                        	|	EnumTransactionStatus_TransactionStatusFailed	|
//	|	JusanResponseCodeClp_ClpOrderNotFound          	|	"45"  	|	IntegrationError_TransactionDeclinedByAcquirer   	|	"Заявка ORDER не найдена"                                                                                                                     	|	EnumTransactionStatus_TransactionStatusFailed	|
//	|	JusanResponseCodeClp_ClpSignInvalid            	|	"46"  	|	IntegrationError_TransactionDeclinedByAcquirer   	|	"Неправильная подпись!"                                                                                                                       	|	EnumTransactionStatus_TransactionStatusFailed	|
//	|	JusanResponseCodeClp_ClpRefundAmountIncorrect  	|	"47"  	|	IntegrationError_TransactionDeclinedByAcquirer   	|	"Сумма возврта '%s' больше чем сумма заказа"                                                                                                  	|	EnumTransactionStatus_TransactionStatusFailed	|
//	|	JusanResponseCodeClp_ClpIncorrectStatus        	|	"48"  	|	IntegrationError_TransactionDeclinedByAcquirer   	|	"Текущий статус заказа не позволяет делать возврат/отмену"                                                                                    	|	EnumTransactionStatus_TransactionStatusFailed	|
//	|	JusanResponseCodeClp_ClpIncorrectValue         	|	"50"  	|	IntegrationError_TransactionDeclinedByAcquirer   	|	"Неправильное значение"                                                                                                                       	|	EnumTransactionStatus_TransactionStatusFailed	|
//	|	JusanResponseCodeClp_ClpTerminalForbidden      	|	"51"  	|	IntegrationError_PaymentForbiddenForMerchant     	|	"Текущий статус терминала не позволяет производить операции"                                                                                  	|	EnumTransactionStatus_TransactionStatusFailed	|
//	|	JusanResponseCodeClp_ClpForbiddenOperation     	|	"52"  	|	IntegrationError_PaymentForbiddenForMerchant     	|	"Операция отмены/возврата через API для терминала запрещена"                                                                                  	|	EnumTransactionStatus_TransactionStatusFailed	|
//	|	JusanResponseCodeClp_ClpDuplicateDescription   	|	"53"  	|	IntegrationError_TransactionDeclinedByAcquirer   	|	"Дублирование описания отмены"                                                                                                                	|	EnumTransactionStatus_TransactionStatusFailed	|
//	|	JusanResponseCodeClp_ClpRefundHandleError      	|	"F"   	|	IntegrationError_TransactionDeclinedByAcquirer   	|	"Ошибка при обработке возврата"                                                                                                               	|	EnumTransactionStatus_TransactionStatusFailed	|
//	|	JusanResponseCodeClp_ClpPaymentError           	|	"E"   	|	IntegrationError_TransactionDeclinedByAcquirer   	|	"Ошибка при оплате"                                                                                                                           	|	EnumTransactionStatus_TransactionStatusFailed	|
//	|	JusanResponseCodeClp_ClpPaymentExpired         	|	"c"   	|	IntegrationError_TransactionDeclinedByAcquirer   	|	"Счет на оплату устарел"                                                                                                                      	|	EnumTransactionStatus_TransactionStatusFailed	|
//	|	JusanResponseCodeClp_ClpAuthError              	|	"3"   	|	IntegrationError_TransactionDeclinedByAcquirer   	|	"Ошибка авторизации платежа"                                                                                                                  	|	EnumTransactionStatus_TransactionStatusFailed	|
//	|	JusanResponseCodeClp_ClpIncorrectCardNum       	|	"37"  	|	IntegrationError_IncorrectCardExpDate            	|	"Неправильный номер карты"                                                                                                                    	|	EnumTransactionStatus_TransactionStatusFailed	|
//	|	JusanResponseCodeClp_ClpSuspectedFraud         	|	"59"  	|	IntegrationError_SuspiciousClient                	|	"Suspected fraud"                                                                                                                             	|	EnumTransactionStatus_TransactionStatusFailed	|
//	|	JusanResponseCodeClp_ClpUndefinedError         	|	"1"   	|	IntegrationError_UndefinedError                  	|	"Неизвестная ошибка"                                                                                                                          	|	EnumTransactionStatus_TransactionStatusFailed	|
//	|	JusanResponseCodeClp_ClpExceedsLimit           	|	"61"  	|	IntegrationError_ExceedsAmountLimit              	|	"Превышает лимит суммы"                                                                                                                       	|	EnumTransactionStatus_TransactionStatusFailed	|
//	|	JusanResponseCodeClp_ClpLimitedCard            	|	"62"  	|	IntegrationError_BlockedCard                     	|	"Карта с ограниченным доступом"                                                                                                               	|	EnumTransactionStatus_TransactionStatusFailed	|
//	|	JusanResponseCodeClp_ClpTransactionDeclined    	|	"05"  	|	IntegrationError_TransactionDeclinedByAcquirer   	|	"Транзакция отклонена банком или МПС"                                                                                                         	|	EnumTransactionStatus_TransactionStatusFailed	|
//	|	JusanResponseCodeClp_ClpNotEnough              	|	"2"   	|	IntegrationError_InsufficientFunds               	|	"Не достаточно средств"                                                                                                                       	|	EnumTransactionStatus_TransactionStatusFailed	|
//	|	JusanResponseCodeClp_ClpLimitExceeded          	|	"65"  	|	IntegrationError_ExceedsTransactionFrequencyLimit	|	"Превышен лимит"                                                                                                                              	|	EnumTransactionStatus_TransactionStatusFailed	|
//	|	JusanResponseCodeClp_ClpErrorAuth              	|	"-19" 	|	IntegrationError_TransactionDeclinedByAcquirer   	|	"Ошибка авторизации"                                                                                                                          	|	EnumTransactionStatus_TransactionStatusFailed	|
//	|	JusanResponseCodeClp_ClpCardInactive           	|	"07"  	|	IntegrationError_CardHasExpired                  	|	"Ваша карта не активна"                                                                                                                       	|	EnumTransactionStatus_TransactionStatusFailed	|

var SliceJusanResponseCodeClpRefs *sliceJusanResponseCodeClpRefs

type sliceJusanResponseCodeClpRefs struct{}

func (*sliceJusanResponseCodeClpRefs) Code(slice ...JusanResponseCodeClp) []string {
	var result []string
	for _, val := range slice {
		result = append(result, val.Code())
	}

	return result
}

func (*sliceJusanResponseCodeClpRefs) IntegrationError(slice ...JusanResponseCodeClp) []IntegrationError {
	var result []IntegrationError
	for _, val := range slice {
		result = append(result, val.IntegrationError())
	}

	return result
}

func (*sliceJusanResponseCodeClpRefs) Message(slice ...JusanResponseCodeClp) []string {
	var result []string
	for _, val := range slice {
		result = append(result, val.Message())
	}

	return result
}

func (*sliceJusanResponseCodeClpRefs) TransactionStatus(slice ...JusanResponseCodeClp) []EnumTransactionStatus {
	var result []EnumTransactionStatus
	for _, val := range slice {
		result = append(result, val.TransactionStatus())
	}

	return result
}
