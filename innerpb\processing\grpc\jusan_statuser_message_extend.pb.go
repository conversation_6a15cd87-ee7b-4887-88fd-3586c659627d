// Code generated by protoc-gen-go-mvp. DO NOT EDIT.
// versions:
// - protoc-gen-go-mvp v2.0.1
// created by mvp authors

package grpc

import (
	protojson "google.golang.org/protobuf/encoding/protojson"
)

func (m *JusanResponseCodeStatuserRef) UnmarshalJSON(in []byte) error {
	val := new(JusanResponseCodeStatuserRef)
	if err := (&protojson.UnmarshalOptions{
		DiscardUnknown: true,
	}).Unmarshal(in, val); err != nil {
		return err
	}

	*m = *val
	return nil
}

func (m *JusanResponseCodeStatuserRef) MarshalJSON() ([]byte, error) {
	return (&protojson.MarshalOptions{
		UseProtoNames: true,
	}).Marshal(m)
}
