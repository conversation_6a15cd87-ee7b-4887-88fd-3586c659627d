package service

import (
	"context"
	"crypto/aes"
	"errors"
	"git.local/sensitive/innerpb/processing/grpc/grpcmock"
	"git.local/sensitive/processing/account/repository/databasemocks"
	"git.local/sensitive/testsdk"
	"github.com/google/uuid"
	empty "google.golang.org/protobuf/types/known/emptypb"
	"strconv"
	"testing"
	"time"

	"github.com/golang/mock/gomock"
	"github.com/stretchr/testify/require"
	"google.golang.org/protobuf/types/known/structpb"

	"git.local/sensitive/innerpb/processing/goerr"
	"git.local/sensitive/innerpb/processing/grpc"
	"git.local/sensitive/processing/account/model"
	"git.local/sensitive/processing/account/schema"
	"git.local/sensitive/sdk/dog/e2e"
)

func TestTransferServiceCreateTransfer(t *testing.T) {
	defaultCfg, err := structpb.NewStruct(map[string]any{
		"my-name-is": "skrillex",
	})
	if err != nil {
		t.Fatal(err)
	}

	testNow := time.Date(2009, time.November, 10, 23, 0, 0, 0, time.UTC)

	type getTransferStatusByCodeOp struct {
		output    *model.TransferStatus
		outputErr error
	}

	type getAccountInfoByNumberOp struct {
		isCalled  bool
		input     string
		output    *model.Account
		outputErr error
	}

	type getMerchantBalanceByBalanceOwnerIDOp struct {
		isCalled  bool
		input     *grpc.GetBalanceOwnerByIDRequestV1
		output    *grpc.GetBalanceOwnerResponseV1
		outputErr error
	}

	type checkHasBalanceOp struct {
		isCalled  bool
		input     *grpc.CheckHasBalanceRequestV1
		output    *empty.Empty
		outputErr error
	}

	type getTransferTypeByCodeOp struct {
		isCalled  bool
		output    *model.TransferType
		outputErr error
	}

	type getEntityTypeByID struct {
		isCalled  bool
		input     *grpc.GetEntityTypeByIDRequestV1
		output    *grpc.GetEntityTypeResponseV1
		outputErr error
	}

	type getCountryByID struct {
		isCalled  bool
		input     *grpc.GetCountryCodeByIDRequestV1
		outputErr error
		output    *grpc.GetCountryCodeResponseV1
	}

	type createTransferOp struct {
		isCalled  bool
		input     model.Transfer
		output    *model.Transfer
		outputErr error
	}

	type makeTransferOp struct {
		isCalled  bool
		input     *grpc.MakeTransferRequest
		output    *grpc.MakeTransferResponse
		outputErr error
	}

	type getFailTransferStatusOp struct {
		isCalled  bool
		input     model.TransferStatusCode
		output    *model.TransferStatus
		outputErr error
	}

	type updateTransferToFailOp struct {
		isCalled   bool
		transferID uint64
		statusID   uint64
		finishedAt *time.Time
		outputErr  error
	}

	type getTransferStatusByIDOp struct {
		isCalled  bool
		input     uint64
		output    *model.TransferStatus
		outputErr error
	}

	type updateTransferStatusOp struct {
		isCalled        bool
		inputTransferID uint64
		inputStatusID   uint64
		inputTime       *time.Time
		outputErr       error
	}

	type updateTransferOp struct {
		isCalled  bool
		input     *schema.CreateTransferResponse
		outputErr error
	}

	tests := []struct {
		name                               string
		req                                schema.CreateTransferRequest
		want                               *schema.CreateTransferResponse
		wantErr                            error
		appConfig                          map[string]any
		getTransferStatusByCode            getTransferStatusByCodeOp
		getAccountInfoByNumber             getAccountInfoByNumberOp
		getMerchantBalanceByBalanceOwnerID getMerchantBalanceByBalanceOwnerIDOp
		checkHasBalanceOp                  checkHasBalanceOp
		getTransferTypeByCode              getTransferTypeByCodeOp
		getEntityTypeByCode                getEntityTypeByID
		getCountryByID                     getCountryByID
		createTransfer                     createTransferOp
		makeTransfer                       makeTransferOp
		getFailTransferStatus              getFailTransferStatusOp
		updateTransferToFail               updateTransferToFailOp
		getTransferStatusByID              getTransferStatusByIDOp
		updateTransferStatus               updateTransferStatusOp
		updateTransfer                     updateTransferOp
	}{
		{
			name: "getting_by_code_error",
			req: schema.CreateTransferRequest{
				TransferAmount:     100000,
				BalanceOwnerID:     1,
				BalanceOwnerBIN:    "some bin",
				AccountNumber:      "some account number",
				RecipientAccount:   "some recipient account",
				PaymentPurposeCode: "some payment purpose code",
				BeneficiaryCode:    "some beneficiary code",
				Description:        "some description",
			},
			want:    nil,
			wantErr: errors.New("some error"),
			getTransferStatusByCode: getTransferStatusByCodeOp{
				output:    nil,
				outputErr: errors.New("some error"),
			},
		},
		{
			name: "getting_by_number_error",
			req: schema.CreateTransferRequest{
				TransferAmount:     100000,
				BalanceOwnerID:     1,
				BalanceOwnerBIN:    "some bin",
				AccountNumber:      "some account number",
				RecipientAccount:   "some recipient account",
				PaymentPurposeCode: "some payment purpose code",
				BeneficiaryCode:    "some beneficiary code",
				Description:        "some description",
			},
			want:    nil,
			wantErr: errors.New("some error"),
			getTransferStatusByCode: getTransferStatusByCodeOp{
				output: &model.TransferStatus{
					ID:   1,
					Name: "new",
					Code: "new",
				},
				outputErr: nil,
			},
			getAccountInfoByNumber: getAccountInfoByNumberOp{
				isCalled:  true,
				input:     "some account number",
				output:    nil,
				outputErr: errors.New("some error"),
			},
		},
		{
			name: "get_merchant_by_balance_owner_id_error",
			req: schema.CreateTransferRequest{
				TransferAmount:     100000,
				BalanceOwnerID:     1,
				BalanceOwnerBIN:    "some bin",
				AccountNumber:      "some account number",
				RecipientAccount:   "some recipient account",
				PaymentPurposeCode: "some payment purpose code",
				BeneficiaryCode:    "some beneficiary code",
				Description:        "some description",
			},
			want:    nil,
			wantErr: errors.New("some error"),
			getTransferStatusByCode: getTransferStatusByCodeOp{
				output: &model.TransferStatus{
					ID:   1,
					Name: "new",
					Code: "new",
				},
				outputErr: nil,
			},
			getAccountInfoByNumber: getAccountInfoByNumberOp{
				isCalled: true,
				input:    "some account number",
				output: &model.Account{
					ID:              23,
					Number:          "some number",
					CurrencyCode:    "kzt",
					BankCode:        "bcc",
					BankID:          25,
					AccountTypeID:   3,
					EncryptedConfig: defaultEncryptedConfig,
				},
				outputErr: nil,
			},
			getMerchantBalanceByBalanceOwnerID: getMerchantBalanceByBalanceOwnerIDOp{
				isCalled: true,
				input: &grpc.GetBalanceOwnerByIDRequestV1{
					BalanceOwnerId: testsdk.Ptr(uint64(1)),
				},
				output:    nil,
				outputErr: errors.New("some error"),
			},
		},
		{
			name: "check_has_balance_error",
			req: schema.CreateTransferRequest{
				TransferAmount:     100000,
				BalanceOwnerID:     1,
				BalanceOwnerBIN:    "some bin",
				AccountNumber:      "some account number",
				RecipientAccount:   "some recipient account",
				PaymentPurposeCode: "some payment purpose code",
				BeneficiaryCode:    "some beneficiary code",
				Description:        "some description",
			},
			want:    nil,
			wantErr: goerr.ErrBalanceNotFound,
			getTransferStatusByCode: getTransferStatusByCodeOp{
				output: &model.TransferStatus{
					ID:   1,
					Name: "new",
					Code: "new",
				},
				outputErr: nil,
			},
			getAccountInfoByNumber: getAccountInfoByNumberOp{
				isCalled: true,
				input:    "some account number",
				output: &model.Account{
					ID:              23,
					Number:          "some number",
					CurrencyCode:    "kzt",
					BankCode:        "bcc",
					BankID:          25,
					AccountTypeID:   3,
					EncryptedConfig: defaultEncryptedConfig,
				},
				outputErr: nil,
			},
			getMerchantBalanceByBalanceOwnerID: getMerchantBalanceByBalanceOwnerIDOp{
				isCalled: true,
				input: &grpc.GetBalanceOwnerByIDRequestV1{
					BalanceOwnerId: testsdk.Ptr(uint64(1)),
				},
				output: &grpc.GetBalanceOwnerResponseV1{
					MerchantId: testsdk.Ptr(uint64(21)),
					ProjectId:  testsdk.Ptr(uint64(10)),
				},
			},
			checkHasBalanceOp: checkHasBalanceOp{
				isCalled: true,
				input: &grpc.CheckHasBalanceRequestV1{
					ProjectId:     testsdk.Ptr(uint64(10)),
					MerchantId:    testsdk.Ptr(uint64(21)),
					AccountNumber: testsdk.Ptr("some number"),
				},
				outputErr: goerr.ErrBalanceNotFound,
			},
		},
		{
			name: "get_transfer_type_error",
			req: schema.CreateTransferRequest{
				TransferAmount:     100000,
				BalanceOwnerID:     1,
				BalanceOwnerBIN:    "some bin",
				AccountNumber:      "some account number",
				RecipientAccount:   "some recipient account",
				PaymentPurposeCode: "some payment purpose code",
				BeneficiaryCode:    "some beneficiary code",
				Description:        "some description",
			},
			want:    nil,
			wantErr: errors.New("some error"),
			getTransferStatusByCode: getTransferStatusByCodeOp{
				output: &model.TransferStatus{
					ID:   1,
					Name: "new",
					Code: "new",
				},
				outputErr: nil,
			},
			getAccountInfoByNumber: getAccountInfoByNumberOp{
				isCalled: true,
				input:    "some account number",
				output: &model.Account{
					ID:              23,
					Number:          "some number",
					CurrencyCode:    "kzt",
					BankCode:        "bcc",
					BankID:          25,
					AccountTypeID:   3,
					EncryptedConfig: defaultEncryptedConfig,
				},
				outputErr: nil,
			},
			getMerchantBalanceByBalanceOwnerID: getMerchantBalanceByBalanceOwnerIDOp{
				isCalled: true,
				input: &grpc.GetBalanceOwnerByIDRequestV1{
					BalanceOwnerId: testsdk.Ptr(uint64(1)),
				},
				output: &grpc.GetBalanceOwnerResponseV1{
					MerchantId: testsdk.Ptr(uint64(21)),
					ProjectId:  testsdk.Ptr(uint64(10)),
				},
				outputErr: nil,
			},
			checkHasBalanceOp: checkHasBalanceOp{
				isCalled: true,
				input: &grpc.CheckHasBalanceRequestV1{
					ProjectId:     testsdk.Ptr(uint64(10)),
					MerchantId:    testsdk.Ptr(uint64(21)),
					AccountNumber: testsdk.Ptr("some number"),
				},
				output: &empty.Empty{},
			},
			getTransferTypeByCode: getTransferTypeByCodeOp{
				isCalled:  true,
				output:    nil,
				outputErr: errors.New("some error"),
			},
		},
		{
			name: "get_entity_type_error",
			req: schema.CreateTransferRequest{
				TransferAmount:     100000,
				BalanceOwnerID:     1,
				BalanceOwnerBIN:    "some bin",
				AccountNumber:      "some account number",
				RecipientAccount:   "some recipient account",
				PaymentPurposeCode: "some payment purpose code",
				BeneficiaryCode:    "some beneficiary code",
				Description:        "some description",
			},
			want:    nil,
			wantErr: errors.New("some error"),
			getTransferStatusByCode: getTransferStatusByCodeOp{
				output: &model.TransferStatus{
					ID:   1,
					Name: "new",
					Code: "new",
				},
				outputErr: nil,
			},
			getAccountInfoByNumber: getAccountInfoByNumberOp{
				isCalled: true,
				input:    "some account number",
				output: &model.Account{
					ID:              23,
					Number:          "some number",
					CurrencyCode:    "kzt",
					BankCode:        "bcc",
					BankID:          25,
					AccountTypeID:   3,
					EncryptedConfig: defaultEncryptedConfig,
				},
				outputErr: nil,
			},
			getMerchantBalanceByBalanceOwnerID: getMerchantBalanceByBalanceOwnerIDOp{
				isCalled: true,
				input: &grpc.GetBalanceOwnerByIDRequestV1{
					BalanceOwnerId: testsdk.Ptr(uint64(1)),
				},
				output: &grpc.GetBalanceOwnerResponseV1{
					MerchantId:   testsdk.Ptr(uint64(21)),
					ProjectId:    testsdk.Ptr(uint64(10)),
					EntityTypeId: testsdk.Ptr(uint64(1)),
				},
				outputErr: nil,
			},
			checkHasBalanceOp: checkHasBalanceOp{
				isCalled: true,
				input: &grpc.CheckHasBalanceRequestV1{
					ProjectId:     testsdk.Ptr(uint64(10)),
					MerchantId:    testsdk.Ptr(uint64(21)),
					AccountNumber: testsdk.Ptr("some number"),
				},
				output: &empty.Empty{},
			},
			getTransferTypeByCode: getTransferTypeByCodeOp{
				isCalled: true,
				output: &model.TransferType{
					ID:   1,
					Code: model.TypeOut,
					Name: "out",
				},
				outputErr: nil,
			},
			getEntityTypeByCode: getEntityTypeByID{
				isCalled: true,
				input: &grpc.GetEntityTypeByIDRequestV1{
					Id: testsdk.Ptr(uint64(1)),
				},
				output:    nil,
				outputErr: errors.New("some error"),
			},
		},
		{
			name: "get_country_code_error",
			req: schema.CreateTransferRequest{
				TransferAmount:     100000,
				BalanceOwnerID:     1,
				BalanceOwnerBIN:    "some bin",
				AccountNumber:      "some account number",
				RecipientAccount:   "some recipient account",
				PaymentPurposeCode: "some payment purpose code",
				BeneficiaryCode:    "some beneficiary code",
				Description:        "some description",
			},
			want:    nil,
			wantErr: errors.New("some error"),
			getTransferStatusByCode: getTransferStatusByCodeOp{
				output: &model.TransferStatus{
					ID:   1,
					Name: "new",
					Code: "new",
				},
				outputErr: nil,
			},
			getAccountInfoByNumber: getAccountInfoByNumberOp{
				isCalled: true,
				input:    "some account number",
				output: &model.Account{
					ID:              23,
					Number:          "some number",
					CurrencyCode:    "kzt",
					BankCode:        "bcc",
					BankID:          25,
					AccountTypeID:   3,
					EncryptedConfig: defaultEncryptedConfig,
				},
				outputErr: nil,
			},
			getMerchantBalanceByBalanceOwnerID: getMerchantBalanceByBalanceOwnerIDOp{
				isCalled: true,
				input: &grpc.GetBalanceOwnerByIDRequestV1{
					BalanceOwnerId: testsdk.Ptr(uint64(1)),
				},
				output: &grpc.GetBalanceOwnerResponseV1{
					MerchantId:    testsdk.Ptr(uint64(21)),
					ProjectId:     testsdk.Ptr(uint64(10)),
					EntityTypeId:  testsdk.Ptr(uint64(1)),
					CountryCodeId: testsdk.Ptr(uint64(2)),
				},
				outputErr: nil,
			},
			checkHasBalanceOp: checkHasBalanceOp{
				isCalled: true,
				input: &grpc.CheckHasBalanceRequestV1{
					ProjectId:     testsdk.Ptr(uint64(10)),
					MerchantId:    testsdk.Ptr(uint64(21)),
					AccountNumber: testsdk.Ptr("some number"),
				},
				output: &empty.Empty{},
			},
			getTransferTypeByCode: getTransferTypeByCodeOp{
				isCalled: true,
				output: &model.TransferType{
					ID:   1,
					Code: model.TypeOut,
					Name: "out",
				},
				outputErr: nil,
			},
			getEntityTypeByCode: getEntityTypeByID{
				isCalled: true,
				input:    &grpc.GetEntityTypeByIDRequestV1{Id: testsdk.Ptr(uint64(1))},
				output: &grpc.GetEntityTypeResponseV1{
					Id:   testsdk.Ptr(uint64(1)),
					Name: testsdk.Ptr("some name"),
					Code: testsdk.Ptr("some code"),
				},
				outputErr: nil,
			},
			getCountryByID: getCountryByID{
				isCalled: true,
				input: &grpc.GetCountryCodeByIDRequestV1{
					Id: testsdk.Ptr(uint64(2)),
				},
				output:    nil,
				outputErr: errors.New("some error"),
			},
		},
		{
			name: "create_transfer_error",
			req: schema.CreateTransferRequest{
				TransferAmount:     100000,
				BalanceOwnerID:     1,
				BalanceOwnerBIN:    "some bin",
				AccountNumber:      "some account number",
				RecipientAccount:   "some recipient account",
				PaymentPurposeCode: "some payment purpose code",
				BeneficiaryCode:    "some beneficiary code",
				Description:        "some description",
				ForeignID:          uuid.MustParse("cd5c60b6-3c82-11f0-b338-00090ffe0001"),
			},
			want:    nil,
			wantErr: errors.New("some error"),
			getTransferStatusByCode: getTransferStatusByCodeOp{
				output: &model.TransferStatus{
					ID:   1,
					Name: "new",
					Code: "new",
				},
				outputErr: nil,
			},
			getAccountInfoByNumber: getAccountInfoByNumberOp{
				isCalled: true,
				input:    "some account number",
				output: &model.Account{
					ID:              23,
					Number:          "some number",
					CurrencyCode:    "kzt",
					BankCode:        "bcc",
					BankID:          25,
					AccountTypeID:   3,
					EncryptedConfig: defaultEncryptedConfig,
				},
				outputErr: nil,
			},
			getMerchantBalanceByBalanceOwnerID: getMerchantBalanceByBalanceOwnerIDOp{
				isCalled: true,
				input: &grpc.GetBalanceOwnerByIDRequestV1{
					BalanceOwnerId: testsdk.Ptr(uint64(1)),
				},
				output: &grpc.GetBalanceOwnerResponseV1{
					MerchantId:    testsdk.Ptr(uint64(21)),
					ProjectId:     testsdk.Ptr(uint64(10)),
					EntityTypeId:  testsdk.Ptr(uint64(1)),
					CountryCodeId: testsdk.Ptr(uint64(2)),
				},
				outputErr: nil,
			},
			checkHasBalanceOp: checkHasBalanceOp{
				isCalled: true,
				input: &grpc.CheckHasBalanceRequestV1{
					ProjectId:     testsdk.Ptr(uint64(10)),
					MerchantId:    testsdk.Ptr(uint64(21)),
					AccountNumber: testsdk.Ptr("some number"),
				},
				output: &empty.Empty{},
			},
			getTransferTypeByCode: getTransferTypeByCodeOp{
				isCalled: true,
				output: &model.TransferType{
					ID:   1,
					Code: model.TypeOut,
					Name: "out",
				},
				outputErr: nil,
			},
			getEntityTypeByCode: getEntityTypeByID{
				isCalled: true,
				input:    &grpc.GetEntityTypeByIDRequestV1{Id: testsdk.Ptr(uint64(1))},
				output: &grpc.GetEntityTypeResponseV1{
					Id:   testsdk.Ptr(uint64(1)),
					Name: testsdk.Ptr("some name"),
					Code: testsdk.Ptr("some code"),
				},
				outputErr: nil,
			},
			getCountryByID: getCountryByID{
				isCalled: true,
				input:    &grpc.GetCountryCodeByIDRequestV1{Id: testsdk.Ptr(uint64(2))},
				output: &grpc.GetCountryCodeResponseV1{
					Id:      testsdk.Ptr(uint64(2)),
					Code:    testsdk.Ptr("kz"),
					EngName: testsdk.Ptr("kz"),
					RuName:  testsdk.Ptr("kz"),
					KazName: testsdk.Ptr("kz"),
				},
				outputErr: nil,
			},
			createTransfer: createTransferOp{
				isCalled: true,
				input: model.Transfer{
					ProjectID:               10,
					MerchantID:              21,
					StatusID:                1,
					Amount:                  100000,
					MerchantBIN:             "some bin",
					MerchantAccount:         "some recipient account",
					MerchantBeneficiaryCode: "some beneficiary code",
					Description:             "some description",
					PaymentPurposeCode:      "some payment purpose code",
					TransferTypeID:          1,
					AcquirerID:              25,
					AccountID:               23,
					EntityTypeID:            1,
					CountryCodeID:           2,
					ForeignID:               uuid.MustParse("cd5c60b6-3c82-11f0-b338-00090ffe0001"),
				},
				output:    nil,
				outputErr: errors.New("some error"),
			},
		},
		{
			name: "unmarshall_error",
			req: schema.CreateTransferRequest{
				TransferAmount:     100000,
				BalanceOwnerID:     1,
				BalanceOwnerBIN:    "some bin",
				AccountNumber:      "some account number",
				RecipientAccount:   "some recipient account",
				PaymentPurposeCode: "some payment purpose code",
				BeneficiaryCode:    "some beneficiary code",
				Description:        "some description",
				ForeignID:          uuid.MustParse("cd5c60b6-3c82-11f0-b338-00090ffe0001"),
			},
			want:    nil,
			wantErr: aes.KeySizeError(3),
			getTransferStatusByCode: getTransferStatusByCodeOp{
				output: &model.TransferStatus{
					ID:   1,
					Name: "new",
					Code: "new",
				},
				outputErr: nil,
			},
			getAccountInfoByNumber: getAccountInfoByNumberOp{
				isCalled: true,
				input:    "some account number",
				output: &model.Account{
					ID:              23,
					Number:          "some number",
					CurrencyCode:    "kzt",
					BankCode:        "bcc",
					BankID:          25,
					AccountTypeID:   3,
					EncryptedConfig: defaultEncryptedConfig,
				},
				outputErr: nil,
			},
			getMerchantBalanceByBalanceOwnerID: getMerchantBalanceByBalanceOwnerIDOp{
				isCalled: true,
				input: &grpc.GetBalanceOwnerByIDRequestV1{
					BalanceOwnerId: testsdk.Ptr(uint64(1)),
				},
				output: &grpc.GetBalanceOwnerResponseV1{
					MerchantId:    testsdk.Ptr(uint64(21)),
					ProjectId:     testsdk.Ptr(uint64(10)),
					EntityTypeId:  testsdk.Ptr(uint64(1)),
					CountryCodeId: testsdk.Ptr(uint64(2)),
				},
				outputErr: nil,
			},
			checkHasBalanceOp: checkHasBalanceOp{
				isCalled: true,
				input: &grpc.CheckHasBalanceRequestV1{
					ProjectId:     testsdk.Ptr(uint64(10)),
					MerchantId:    testsdk.Ptr(uint64(21)),
					AccountNumber: testsdk.Ptr("some number"),
				},
				output: &empty.Empty{},
			},
			getTransferTypeByCode: getTransferTypeByCodeOp{
				isCalled: true,
				output: &model.TransferType{
					ID:   1,
					Code: model.TypeOut,
					Name: "out",
				},
				outputErr: nil,
			},
			getEntityTypeByCode: getEntityTypeByID{
				isCalled: true,
				input:    &grpc.GetEntityTypeByIDRequestV1{Id: testsdk.Ptr(uint64(1))},
				output: &grpc.GetEntityTypeResponseV1{
					Id:   testsdk.Ptr(uint64(1)),
					Name: testsdk.Ptr("some name"),
					Code: testsdk.Ptr("some code"),
				},
				outputErr: nil,
			},
			getCountryByID: getCountryByID{
				isCalled: true,
				input:    &grpc.GetCountryCodeByIDRequestV1{Id: testsdk.Ptr(uint64(2))},
				output: &grpc.GetCountryCodeResponseV1{
					Id:      testsdk.Ptr(uint64(2)),
					Code:    testsdk.Ptr("kz"),
					EngName: testsdk.Ptr("kz"),
					RuName:  testsdk.Ptr("kz"),
					KazName: testsdk.Ptr("kz"),
				},
				outputErr: nil,
			},
			createTransfer: createTransferOp{
				isCalled: true,
				input: model.Transfer{
					ProjectID:               10,
					MerchantID:              21,
					StatusID:                1,
					Amount:                  100000,
					MerchantBIN:             "some bin",
					MerchantAccount:         "some recipient account",
					MerchantBeneficiaryCode: "some beneficiary code",
					Description:             "some description",
					PaymentPurposeCode:      "some payment purpose code",
					TransferTypeID:          1,
					AcquirerID:              25,
					AccountID:               23,
					EntityTypeID:            1,
					CountryCodeID:           2,
					ForeignID:               uuid.MustParse("cd5c60b6-3c82-11f0-b338-00090ffe0001"),
				},
				output: &model.Transfer{
					ID:                      1,
					ProjectID:               10,
					MerchantID:              21,
					StatusID:                1,
					Amount:                  100000,
					MerchantBIN:             "some bin",
					MerchantAccount:         "some recipient account",
					MerchantBeneficiaryCode: "some beneficiary code",
					Description:             "some description",
					PaymentPurposeCode:      "some payment purpose code",
					TransferTypeID:          1,
					AcquirerID:              25,
					AccountID:               23,
					EntityTypeID:            1,
					CountryCodeID:           2,
					ForeignID:               uuid.MustParse("cd5c60b6-3c82-11f0-b338-00090ffe0001"),
				},
				outputErr: nil,
			},
			appConfig: map[string]any{
				"SYMMETRIC_KEY": badKey,
			},
		},
		{
			name: "make transfer error",
			req: schema.CreateTransferRequest{
				TransferAmount:     100000,
				BalanceOwnerID:     1,
				BalanceOwnerBIN:    "some bin",
				AccountNumber:      "some account number",
				RecipientAccount:   "some recipient account",
				PaymentPurposeCode: "some payment purpose code",
				BeneficiaryCode:    "some beneficiary code",
				Description:        "some description",
				ForeignID:          uuid.MustParse("cd5c60b6-3c82-11f0-b338-00090ffe0001"),
			},
			want:    nil,
			wantErr: errors.New("some error"),
			getTransferStatusByCode: getTransferStatusByCodeOp{
				output: &model.TransferStatus{
					ID:   1,
					Name: "new",
					Code: "new",
				},
				outputErr: nil,
			},
			getAccountInfoByNumber: getAccountInfoByNumberOp{
				isCalled: true,
				input:    "some account number",
				output: &model.Account{
					ID:              23,
					Number:          "some number",
					CurrencyCode:    "kzt",
					BankCode:        "bcc",
					BankID:          25,
					AccountTypeID:   3,
					EncryptedConfig: defaultEncryptedConfig,
				},
				outputErr: nil,
			},
			getMerchantBalanceByBalanceOwnerID: getMerchantBalanceByBalanceOwnerIDOp{
				isCalled: true,
				input: &grpc.GetBalanceOwnerByIDRequestV1{
					BalanceOwnerId: testsdk.Ptr(uint64(1)),
				},
				output: &grpc.GetBalanceOwnerResponseV1{
					MerchantId:    testsdk.Ptr(uint64(21)),
					ProjectId:     testsdk.Ptr(uint64(10)),
					EntityTypeId:  testsdk.Ptr(uint64(1)),
					CountryCodeId: testsdk.Ptr(uint64(2)),
				},
				outputErr: nil,
			},
			checkHasBalanceOp: checkHasBalanceOp{
				isCalled: true,
				input: &grpc.CheckHasBalanceRequestV1{
					ProjectId:     testsdk.Ptr(uint64(10)),
					MerchantId:    testsdk.Ptr(uint64(21)),
					AccountNumber: testsdk.Ptr("some number"),
				},
				output: &empty.Empty{},
			},
			getTransferTypeByCode: getTransferTypeByCodeOp{
				isCalled: true,
				output: &model.TransferType{
					ID:   1,
					Code: model.TypeOut,
					Name: "out",
				},
				outputErr: nil,
			},
			getEntityTypeByCode: getEntityTypeByID{
				isCalled: true,
				input:    &grpc.GetEntityTypeByIDRequestV1{Id: testsdk.Ptr(uint64(1))},
				output: &grpc.GetEntityTypeResponseV1{
					Id:   testsdk.Ptr(uint64(1)),
					Name: testsdk.Ptr("some name"),
					Code: testsdk.Ptr("some code"),
				},
				outputErr: nil,
			},
			getCountryByID: getCountryByID{
				isCalled: true,
				input:    &grpc.GetCountryCodeByIDRequestV1{Id: testsdk.Ptr(uint64(2))},
				output: &grpc.GetCountryCodeResponseV1{
					Id:      testsdk.Ptr(uint64(2)),
					Code:    testsdk.Ptr("kz"),
					EngName: testsdk.Ptr("kz"),
					RuName:  testsdk.Ptr("kz"),
					KazName: testsdk.Ptr("kz"),
				},
				outputErr: nil,
			},
			createTransfer: createTransferOp{
				isCalled: true,
				input: model.Transfer{
					ProjectID:               10,
					MerchantID:              21,
					StatusID:                1,
					Amount:                  100000,
					MerchantBIN:             "some bin",
					MerchantAccount:         "some recipient account",
					MerchantBeneficiaryCode: "some beneficiary code",
					Description:             "some description",
					PaymentPurposeCode:      "some payment purpose code",
					TransferTypeID:          1,
					AcquirerID:              25,
					AccountID:               23,
					EntityTypeID:            1,
					CountryCodeID:           2,
					ForeignID:               uuid.MustParse("cd5c60b6-3c82-11f0-b338-00090ffe0001"),
				},
				output: &model.Transfer{
					ID:                      1,
					ProjectID:               10,
					MerchantID:              21,
					StatusID:                1,
					Amount:                  100000,
					MerchantBIN:             "some bin",
					MerchantAccount:         "some recipient account",
					MerchantBeneficiaryCode: "some beneficiary code",
					Description:             "some description",
					PaymentPurposeCode:      "some payment purpose code",
					TransferTypeID:          1,
					AcquirerID:              25,
					AccountID:               23,
					EntityTypeID:            1,
					CountryCodeID:           2,
					ForeignID:               uuid.MustParse("cd5c60b6-3c82-11f0-b338-00090ffe0001"),
				},
				outputErr: nil,
			},
			makeTransfer: makeTransferOp{
				isCalled: true,
				input: &grpc.MakeTransferRequest{
					Account: &grpc.AccountTerminal{
						BankCode: testsdk.Ptr("bcc"),
						Config:   defaultCfg,
					},
					TransferId:          testsdk.Ptr(uint64(1)),
					ExternalReferenceId: testsdk.Ptr(strconv.FormatUint(1, 10)),
					Amount:              testsdk.Ptr(float64(100000)),
					Currency:            testsdk.Ptr("kzt"),
					IsNeedAcceptation:   testsdk.Ptr(int32(1)),
					ServiceParameters: []*grpc.ServiceParameter{
						{
							Code:  testsdk.Ptr("iin"),
							Value: testsdk.Ptr("some bin"),
						},
						{
							Code:  testsdk.Ptr("account"),
							Value: testsdk.Ptr("some recipient account"),
						},
						{
							Code:  testsdk.Ptr("knp"),
							Value: testsdk.Ptr("some payment purpose code"),
						},
						{
							Code:  testsdk.Ptr("code"),
							Value: testsdk.Ptr("some beneficiary code"),
						},
						{
							Code:  testsdk.Ptr("narrative"),
							Value: testsdk.Ptr("some description"),
						},
						{
							Code:  testsdk.Ptr("benIsJur"),
							Value: testsdk.Ptr("some code"),
						},
						{
							Code:  testsdk.Ptr("benCountry"),
							Value: testsdk.Ptr("kz"),
						},
					},
				},
				output:    nil,
				outputErr: errors.New("some error"),
			},
			getFailTransferStatus: getFailTransferStatusOp{
				isCalled: true,
				input:    model.StatusFailed,
				output: &model.TransferStatus{
					ID:   6,
					Code: "failed",
					Name: "Транзакция прошла неуспешно",
				},
			},
			updateTransferToFail: updateTransferToFailOp{
				isCalled:   true,
				transferID: 1,
				statusID:   6,
				finishedAt: testsdk.Ptr(time.Date(2009, time.November, 10, 23, 0, 0, 0, time.UTC)),
			},
			appConfig: map[string]any{
				"SYMMETRIC_KEY": normalKey,
			},
		},
		{
			name: "make transfer error then update status error",
			req: schema.CreateTransferRequest{
				TransferAmount:     100000,
				BalanceOwnerID:     1,
				BalanceOwnerBIN:    "some bin",
				AccountNumber:      "some account number",
				RecipientAccount:   "some recipient account",
				PaymentPurposeCode: "some payment purpose code",
				BeneficiaryCode:    "some beneficiary code",
				Description:        "some description",
				ForeignID:          uuid.MustParse("cd5c60b6-3c82-11f0-b338-00090ffe0001"),
			},
			want:    nil,
			wantErr: errors.New("update status error"),
			getTransferStatusByCode: getTransferStatusByCodeOp{
				output: &model.TransferStatus{
					ID:   1,
					Name: "new",
					Code: "new",
				},
				outputErr: nil,
			},
			getAccountInfoByNumber: getAccountInfoByNumberOp{
				isCalled: true,
				input:    "some account number",
				output: &model.Account{
					ID:              23,
					Number:          "some number",
					CurrencyCode:    "kzt",
					BankCode:        "bcc",
					BankID:          25,
					AccountTypeID:   3,
					EncryptedConfig: defaultEncryptedConfig,
				},
				outputErr: nil,
			},
			getMerchantBalanceByBalanceOwnerID: getMerchantBalanceByBalanceOwnerIDOp{
				isCalled: true,
				input: &grpc.GetBalanceOwnerByIDRequestV1{
					BalanceOwnerId: testsdk.Ptr(uint64(1)),
				},
				output: &grpc.GetBalanceOwnerResponseV1{
					MerchantId:    testsdk.Ptr(uint64(21)),
					ProjectId:     testsdk.Ptr(uint64(10)),
					EntityTypeId:  testsdk.Ptr(uint64(1)),
					CountryCodeId: testsdk.Ptr(uint64(2)),
				},
				outputErr: nil,
			},
			checkHasBalanceOp: checkHasBalanceOp{
				isCalled: true,
				input: &grpc.CheckHasBalanceRequestV1{
					ProjectId:     testsdk.Ptr(uint64(10)),
					MerchantId:    testsdk.Ptr(uint64(21)),
					AccountNumber: testsdk.Ptr("some number"),
				},
				output: &empty.Empty{},
			},
			getTransferTypeByCode: getTransferTypeByCodeOp{
				isCalled: true,
				output: &model.TransferType{
					ID:   1,
					Code: model.TypeOut,
					Name: "out",
				},
				outputErr: nil,
			},
			getEntityTypeByCode: getEntityTypeByID{
				isCalled: true,
				input:    &grpc.GetEntityTypeByIDRequestV1{Id: testsdk.Ptr(uint64(1))},
				output: &grpc.GetEntityTypeResponseV1{
					Id:   testsdk.Ptr(uint64(1)),
					Name: testsdk.Ptr("some name"),
					Code: testsdk.Ptr("some code"),
				},
				outputErr: nil,
			},
			getCountryByID: getCountryByID{
				isCalled: true,
				input:    &grpc.GetCountryCodeByIDRequestV1{Id: testsdk.Ptr(uint64(2))},
				output: &grpc.GetCountryCodeResponseV1{
					Id:      testsdk.Ptr(uint64(2)),
					Code:    testsdk.Ptr("kz"),
					EngName: testsdk.Ptr("kz"),
					RuName:  testsdk.Ptr("kz"),
					KazName: testsdk.Ptr("kz"),
				},
				outputErr: nil,
			},
			createTransfer: createTransferOp{
				isCalled: true,
				input: model.Transfer{
					ProjectID:               10,
					MerchantID:              21,
					StatusID:                1,
					Amount:                  100000,
					MerchantBIN:             "some bin",
					MerchantAccount:         "some recipient account",
					MerchantBeneficiaryCode: "some beneficiary code",
					Description:             "some description",
					PaymentPurposeCode:      "some payment purpose code",
					TransferTypeID:          1,
					AcquirerID:              25,
					AccountID:               23,
					EntityTypeID:            1,
					CountryCodeID:           2,
					ForeignID:               uuid.MustParse("cd5c60b6-3c82-11f0-b338-00090ffe0001"),
				},
				output: &model.Transfer{
					ID:                      1,
					ProjectID:               10,
					MerchantID:              21,
					StatusID:                1,
					Amount:                  100000,
					MerchantBIN:             "some bin",
					MerchantAccount:         "some recipient account",
					MerchantBeneficiaryCode: "some beneficiary code",
					Description:             "some description",
					PaymentPurposeCode:      "some payment purpose code",
					TransferTypeID:          1,
					AcquirerID:              25,
					AccountID:               23,
					EntityTypeID:            1,
					CountryCodeID:           2,
					ForeignID:               uuid.MustParse("cd5c60b6-3c82-11f0-b338-00090ffe0001"),
				},
				outputErr: nil,
			},
			makeTransfer: makeTransferOp{
				isCalled: true,
				input: &grpc.MakeTransferRequest{
					Account: &grpc.AccountTerminal{
						BankCode: testsdk.Ptr("bcc"),
						Config:   defaultCfg,
					},
					TransferId:          testsdk.Ptr(uint64(1)),
					ExternalReferenceId: testsdk.Ptr(strconv.FormatUint(1, 10)),
					Amount:              testsdk.Ptr(float64(100000)),
					Currency:            testsdk.Ptr("kzt"),
					IsNeedAcceptation:   testsdk.Ptr(int32(1)),
					ServiceParameters: []*grpc.ServiceParameter{
						{
							Code:  testsdk.Ptr("iin"),
							Value: testsdk.Ptr("some bin"),
						},
						{
							Code:  testsdk.Ptr("account"),
							Value: testsdk.Ptr("some recipient account"),
						},
						{
							Code:  testsdk.Ptr("knp"),
							Value: testsdk.Ptr("some payment purpose code"),
						},
						{
							Code:  testsdk.Ptr("code"),
							Value: testsdk.Ptr("some beneficiary code"),
						},
						{
							Code:  testsdk.Ptr("narrative"),
							Value: testsdk.Ptr("some description"),
						},
						{
							Code:  testsdk.Ptr("benIsJur"),
							Value: testsdk.Ptr("some code"),
						},
						{
							Code:  testsdk.Ptr("benCountry"),
							Value: testsdk.Ptr("kz"),
						},
					},
				},
				output:    nil,
				outputErr: errors.New("some error"),
			},
			getFailTransferStatus: getFailTransferStatusOp{
				isCalled: true,
				input:    model.StatusFailed,
				output: &model.TransferStatus{
					ID:   6,
					Code: "failed",
					Name: "Транзакция прошла неуспешно",
				},
			},
			updateTransferToFail: updateTransferToFailOp{
				isCalled:   true,
				transferID: 1,
				statusID:   6,
				finishedAt: testsdk.Ptr(time.Date(2009, time.November, 10, 23, 0, 0, 0, time.UTC)),
				outputErr:  errors.New("update status error"),
			},
			appConfig: map[string]any{
				"SYMMETRIC_KEY": normalKey,
			},
		},
		{
			name: "make transfer error then get failed tranfer status error",
			req: schema.CreateTransferRequest{
				TransferAmount:     100000,
				BalanceOwnerID:     1,
				BalanceOwnerBIN:    "some bin",
				AccountNumber:      "some account number",
				RecipientAccount:   "some recipient account",
				PaymentPurposeCode: "some payment purpose code",
				BeneficiaryCode:    "some beneficiary code",
				Description:        "some description",
				ForeignID:          uuid.MustParse("cd5c60b6-3c82-11f0-b338-00090ffe0001"),
			},
			want:    nil,
			wantErr: errors.New("get failed transfer status error"),
			getTransferStatusByCode: getTransferStatusByCodeOp{
				output: &model.TransferStatus{
					ID:   1,
					Name: "new",
					Code: "new",
				},
				outputErr: nil,
			},
			getAccountInfoByNumber: getAccountInfoByNumberOp{
				isCalled: true,
				input:    "some account number",
				output: &model.Account{
					ID:              23,
					Number:          "some number",
					CurrencyCode:    "kzt",
					BankCode:        "bcc",
					BankID:          25,
					AccountTypeID:   3,
					EncryptedConfig: defaultEncryptedConfig,
				},
				outputErr: nil,
			},
			getMerchantBalanceByBalanceOwnerID: getMerchantBalanceByBalanceOwnerIDOp{
				isCalled: true,
				input: &grpc.GetBalanceOwnerByIDRequestV1{
					BalanceOwnerId: testsdk.Ptr(uint64(1)),
				},
				output: &grpc.GetBalanceOwnerResponseV1{
					MerchantId:    testsdk.Ptr(uint64(21)),
					ProjectId:     testsdk.Ptr(uint64(10)),
					EntityTypeId:  testsdk.Ptr(uint64(1)),
					CountryCodeId: testsdk.Ptr(uint64(2)),
				},
				outputErr: nil,
			},
			checkHasBalanceOp: checkHasBalanceOp{
				isCalled: true,
				input: &grpc.CheckHasBalanceRequestV1{
					ProjectId:     testsdk.Ptr(uint64(10)),
					MerchantId:    testsdk.Ptr(uint64(21)),
					AccountNumber: testsdk.Ptr("some number"),
				},
				output: &empty.Empty{},
			},
			getTransferTypeByCode: getTransferTypeByCodeOp{
				isCalled: true,
				output: &model.TransferType{
					ID:   1,
					Code: model.TypeOut,
					Name: "out",
				},
				outputErr: nil,
			},
			getEntityTypeByCode: getEntityTypeByID{
				isCalled: true,
				input:    &grpc.GetEntityTypeByIDRequestV1{Id: testsdk.Ptr(uint64(1))},
				output: &grpc.GetEntityTypeResponseV1{
					Id:   testsdk.Ptr(uint64(1)),
					Name: testsdk.Ptr("some name"),
					Code: testsdk.Ptr("some code"),
				},
				outputErr: nil,
			},
			getCountryByID: getCountryByID{
				isCalled: true,
				input:    &grpc.GetCountryCodeByIDRequestV1{Id: testsdk.Ptr(uint64(2))},
				output: &grpc.GetCountryCodeResponseV1{
					Id:      testsdk.Ptr(uint64(2)),
					Code:    testsdk.Ptr("kz"),
					EngName: testsdk.Ptr("kz"),
					RuName:  testsdk.Ptr("kz"),
					KazName: testsdk.Ptr("kz"),
				},
				outputErr: nil,
			},
			createTransfer: createTransferOp{
				isCalled: true,
				input: model.Transfer{
					ProjectID:               10,
					MerchantID:              21,
					StatusID:                1,
					Amount:                  100000,
					MerchantBIN:             "some bin",
					MerchantAccount:         "some recipient account",
					MerchantBeneficiaryCode: "some beneficiary code",
					Description:             "some description",
					PaymentPurposeCode:      "some payment purpose code",
					TransferTypeID:          1,
					AcquirerID:              25,
					AccountID:               23,
					EntityTypeID:            1,
					CountryCodeID:           2,
					ForeignID:               uuid.MustParse("cd5c60b6-3c82-11f0-b338-00090ffe0001"),
				},
				output: &model.Transfer{
					ID:                      1,
					ProjectID:               10,
					MerchantID:              21,
					StatusID:                1,
					Amount:                  100000,
					MerchantBIN:             "some bin",
					MerchantAccount:         "some recipient account",
					MerchantBeneficiaryCode: "some beneficiary code",
					Description:             "some description",
					PaymentPurposeCode:      "some payment purpose code",
					TransferTypeID:          1,
					AcquirerID:              25,
					AccountID:               23,
					EntityTypeID:            1,
					CountryCodeID:           2,
					ForeignID:               uuid.MustParse("cd5c60b6-3c82-11f0-b338-00090ffe0001"),
				},
				outputErr: nil,
			},
			makeTransfer: makeTransferOp{
				isCalled: true,
				input: &grpc.MakeTransferRequest{
					Account: &grpc.AccountTerminal{
						BankCode: testsdk.Ptr("bcc"),
						Config:   defaultCfg,
					},
					TransferId:          testsdk.Ptr(uint64(1)),
					ExternalReferenceId: testsdk.Ptr(strconv.FormatUint(1, 10)),
					Amount:              testsdk.Ptr(float64(100000)),
					Currency:            testsdk.Ptr("kzt"),
					IsNeedAcceptation:   testsdk.Ptr(int32(1)),
					ServiceParameters: []*grpc.ServiceParameter{
						{
							Code:  testsdk.Ptr("iin"),
							Value: testsdk.Ptr("some bin"),
						},
						{
							Code:  testsdk.Ptr("account"),
							Value: testsdk.Ptr("some recipient account"),
						},
						{
							Code:  testsdk.Ptr("knp"),
							Value: testsdk.Ptr("some payment purpose code"),
						},
						{
							Code:  testsdk.Ptr("code"),
							Value: testsdk.Ptr("some beneficiary code"),
						},
						{
							Code:  testsdk.Ptr("narrative"),
							Value: testsdk.Ptr("some description"),
						},
						{
							Code:  testsdk.Ptr("benIsJur"),
							Value: testsdk.Ptr("some code"),
						},
						{
							Code:  testsdk.Ptr("benCountry"),
							Value: testsdk.Ptr("kz"),
						},
					},
				},
				output:    nil,
				outputErr: errors.New("some error"),
			},
			getFailTransferStatus: getFailTransferStatusOp{
				isCalled: true,
				input:    model.StatusFailed,
				output: &model.TransferStatus{
					ID:   6,
					Code: "failed",
					Name: "Транзакция прошла неуспешно",
				},
				outputErr: errors.New("get failed transfer status error"),
			},
			appConfig: map[string]any{
				"SYMMETRIC_KEY": normalKey,
			},
		},
		{
			name: "get_status_by_id_error",
			req: schema.CreateTransferRequest{
				TransferAmount:     100000,
				BalanceOwnerID:     1,
				BalanceOwnerBIN:    "some bin",
				AccountNumber:      "some account number",
				RecipientAccount:   "some recipient account",
				PaymentPurposeCode: "some payment purpose code",
				BeneficiaryCode:    "some beneficiary code",
				Description:        "some description",
				ForeignID:          uuid.MustParse("cd5c60b6-3c82-11f0-b338-00090ffe0001"),
			},
			want:    nil,
			wantErr: errors.New("some error"),
			getTransferStatusByCode: getTransferStatusByCodeOp{
				output: &model.TransferStatus{
					ID:   1,
					Name: "new",
					Code: "new",
				},
				outputErr: nil,
			},
			getAccountInfoByNumber: getAccountInfoByNumberOp{
				isCalled: true,
				input:    "some account number",
				output: &model.Account{
					ID:              23,
					Number:          "some number",
					CurrencyCode:    "kzt",
					BankCode:        "bcc",
					BankID:          25,
					AccountTypeID:   3,
					EncryptedConfig: defaultEncryptedConfig,
				},
				outputErr: nil,
			},
			getMerchantBalanceByBalanceOwnerID: getMerchantBalanceByBalanceOwnerIDOp{
				isCalled: true,
				input: &grpc.GetBalanceOwnerByIDRequestV1{
					BalanceOwnerId: testsdk.Ptr(uint64(1)),
				},
				output: &grpc.GetBalanceOwnerResponseV1{
					MerchantId:    testsdk.Ptr(uint64(21)),
					ProjectId:     testsdk.Ptr(uint64(10)),
					EntityTypeId:  testsdk.Ptr(uint64(1)),
					CountryCodeId: testsdk.Ptr(uint64(2)),
				},
				outputErr: nil,
			},
			checkHasBalanceOp: checkHasBalanceOp{
				isCalled: true,
				input: &grpc.CheckHasBalanceRequestV1{
					ProjectId:     testsdk.Ptr(uint64(10)),
					MerchantId:    testsdk.Ptr(uint64(21)),
					AccountNumber: testsdk.Ptr("some number"),
				},
				output: &empty.Empty{},
			},
			getTransferTypeByCode: getTransferTypeByCodeOp{
				isCalled: true,
				output: &model.TransferType{
					ID:   1,
					Code: model.TypeOut,
					Name: "out",
				},
				outputErr: nil,
			},
			getEntityTypeByCode: getEntityTypeByID{
				isCalled: true,
				input: &grpc.GetEntityTypeByIDRequestV1{
					Id: testsdk.Ptr(uint64(1)),
				},
				output: &grpc.GetEntityTypeResponseV1{
					Id:   testsdk.Ptr(uint64(1)),
					Name: testsdk.Ptr("some name"),
					Code: testsdk.Ptr("some code"),
				},
				outputErr: nil,
			},
			getCountryByID: getCountryByID{
				isCalled: true,
				input: &grpc.GetCountryCodeByIDRequestV1{
					Id: testsdk.Ptr(uint64(2)),
				},
				output: &grpc.GetCountryCodeResponseV1{
					Id:      testsdk.Ptr(uint64(2)),
					Code:    testsdk.Ptr("kz"),
					EngName: testsdk.Ptr("kz"),
					RuName:  testsdk.Ptr("kz"),
					KazName: testsdk.Ptr("kz"),
				},
				outputErr: nil,
			},
			createTransfer: createTransferOp{
				isCalled: true,
				input: model.Transfer{
					ProjectID:               10,
					MerchantID:              21,
					StatusID:                1,
					Amount:                  100000,
					MerchantBIN:             "some bin",
					MerchantAccount:         "some recipient account",
					MerchantBeneficiaryCode: "some beneficiary code",
					Description:             "some description",
					PaymentPurposeCode:      "some payment purpose code",
					TransferTypeID:          1,
					AcquirerID:              25,
					AccountID:               23,
					EntityTypeID:            1,
					CountryCodeID:           2,
					ForeignID:               uuid.MustParse("cd5c60b6-3c82-11f0-b338-00090ffe0001"),
				},
				output: &model.Transfer{
					ID:                      1,
					ProjectID:               10,
					MerchantID:              21,
					StatusID:                1,
					Amount:                  100000,
					MerchantBIN:             "some bin",
					MerchantAccount:         "some recipient account",
					MerchantBeneficiaryCode: "some beneficiary code",
					Description:             "some description",
					PaymentPurposeCode:      "some payment purpose code",
					TransferTypeID:          1,
					AcquirerID:              25,
					AccountID:               23,
					EntityTypeID:            1,
					CountryCodeID:           2,
					ForeignID:               uuid.MustParse("cd5c60b6-3c82-11f0-b338-00090ffe0001"),
				},
				outputErr: nil,
			},
			makeTransfer: makeTransferOp{
				isCalled: true,
				input: &grpc.MakeTransferRequest{
					Account: &grpc.AccountTerminal{
						BankCode: testsdk.Ptr("bcc"),
						Config:   defaultCfg,
					},
					TransferId:          testsdk.Ptr(uint64(1)),
					ExternalReferenceId: testsdk.Ptr(strconv.FormatUint(1, 10)),
					Amount:              testsdk.Ptr(float64(100000)),
					Currency:            testsdk.Ptr("kzt"),
					IsNeedAcceptation:   testsdk.Ptr(int32(1)),
					ServiceParameters: []*grpc.ServiceParameter{
						{
							Code:  testsdk.Ptr("iin"),
							Value: testsdk.Ptr("some bin"),
						},
						{
							Code:  testsdk.Ptr("account"),
							Value: testsdk.Ptr("some recipient account"),
						},
						{
							Code:  testsdk.Ptr("knp"),
							Value: testsdk.Ptr("some payment purpose code"),
						},
						{
							Code:  testsdk.Ptr("code"),
							Value: testsdk.Ptr("some beneficiary code"),
						},
						{
							Code:  testsdk.Ptr("narrative"),
							Value: testsdk.Ptr("some description"),
						},
						{
							Code:  testsdk.Ptr("benIsJur"),
							Value: testsdk.Ptr("some code"),
						},
						{
							Code:  testsdk.Ptr("benCountry"),
							Value: testsdk.Ptr("kz"),
						},
					},
				},
				output: &grpc.MakeTransferResponse{
					TransferId:          testsdk.Ptr(uint64(1)),
					TransferStatusId:    testsdk.Ptr(uint64(2)),
					TransferStatus:      testsdk.Ptr("success"),
					Rrn:                 testsdk.Ptr("some rrn"),
					ExternalReferenceId: testsdk.Ptr("tarlan123"),
				},
				outputErr: nil,
			},
			getTransferStatusByID: getTransferStatusByIDOp{
				isCalled:  true,
				input:     2,
				output:    nil,
				outputErr: errors.New("some error"),
			},
			appConfig: map[string]any{
				"SYMMETRIC_KEY": normalKey,
			},
		},
		{
			name: "update_status_error",
			req: schema.CreateTransferRequest{
				TransferAmount:     100000,
				BalanceOwnerID:     1,
				BalanceOwnerBIN:    "some bin",
				AccountNumber:      "some account number",
				RecipientAccount:   "some recipient account",
				PaymentPurposeCode: "some payment purpose code",
				BeneficiaryCode:    "some beneficiary code",
				Description:        "some description",
				ForeignID:          uuid.MustParse("cd5c60b6-3c82-11f0-b338-00090ffe0001"),
			},
			want:    nil,
			wantErr: errors.New("some error"),
			getTransferStatusByCode: getTransferStatusByCodeOp{
				output: &model.TransferStatus{
					ID:   1,
					Name: "new",
					Code: "new",
				},
				outputErr: nil,
			},
			getAccountInfoByNumber: getAccountInfoByNumberOp{
				isCalled: true,
				input:    "some account number",
				output: &model.Account{
					ID:              23,
					Number:          "some number",
					CurrencyCode:    "kzt",
					BankCode:        "bcc",
					BankID:          25,
					AccountTypeID:   3,
					EncryptedConfig: defaultEncryptedConfig,
				},
				outputErr: nil,
			},
			getMerchantBalanceByBalanceOwnerID: getMerchantBalanceByBalanceOwnerIDOp{
				isCalled: true,
				input: &grpc.GetBalanceOwnerByIDRequestV1{
					BalanceOwnerId: testsdk.Ptr(uint64(1)),
				},
				output: &grpc.GetBalanceOwnerResponseV1{
					MerchantId:    testsdk.Ptr(uint64(21)),
					ProjectId:     testsdk.Ptr(uint64(10)),
					EntityTypeId:  testsdk.Ptr(uint64(1)),
					CountryCodeId: testsdk.Ptr(uint64(2)),
				},
				outputErr: nil,
			},
			checkHasBalanceOp: checkHasBalanceOp{
				isCalled: true,
				input: &grpc.CheckHasBalanceRequestV1{
					ProjectId:     testsdk.Ptr(uint64(10)),
					MerchantId:    testsdk.Ptr(uint64(21)),
					AccountNumber: testsdk.Ptr("some number"),
				},
				output: &empty.Empty{},
			},
			getTransferTypeByCode: getTransferTypeByCodeOp{
				isCalled: true,
				output: &model.TransferType{
					ID:   1,
					Code: model.TypeOut,
					Name: "out",
				},
				outputErr: nil,
			},
			getEntityTypeByCode: getEntityTypeByID{
				isCalled: true,
				input: &grpc.GetEntityTypeByIDRequestV1{
					Id: testsdk.Ptr(uint64(1)),
				},
				output: &grpc.GetEntityTypeResponseV1{
					Id:   testsdk.Ptr(uint64(1)),
					Name: testsdk.Ptr("some name"),
					Code: testsdk.Ptr("some code"),
				},
				outputErr: nil,
			},
			getCountryByID: getCountryByID{
				isCalled: true,
				input: &grpc.GetCountryCodeByIDRequestV1{
					Id: testsdk.Ptr(uint64(2)),
				},
				output: &grpc.GetCountryCodeResponseV1{
					Id:      testsdk.Ptr(uint64(2)),
					Code:    testsdk.Ptr("kz"),
					EngName: testsdk.Ptr("kz"),
					RuName:  testsdk.Ptr("kz"),
					KazName: testsdk.Ptr("kz"),
				},
				outputErr: nil,
			},
			createTransfer: createTransferOp{
				isCalled: true,
				input: model.Transfer{
					ProjectID:               10,
					MerchantID:              21,
					StatusID:                1,
					Amount:                  100000,
					MerchantBIN:             "some bin",
					MerchantAccount:         "some recipient account",
					MerchantBeneficiaryCode: "some beneficiary code",
					Description:             "some description",
					PaymentPurposeCode:      "some payment purpose code",
					TransferTypeID:          1,
					AcquirerID:              25,
					AccountID:               23,
					EntityTypeID:            1,
					CountryCodeID:           2,
					ForeignID:               uuid.MustParse("cd5c60b6-3c82-11f0-b338-00090ffe0001"),
				},
				output: &model.Transfer{
					ID:                      1,
					ProjectID:               10,
					MerchantID:              21,
					StatusID:                1,
					Amount:                  100000,
					MerchantBIN:             "some bin",
					MerchantAccount:         "some recipient account",
					MerchantBeneficiaryCode: "some beneficiary code",
					Description:             "some description",
					PaymentPurposeCode:      "some payment purpose code",
					TransferTypeID:          1,
					AcquirerID:              25,
					AccountID:               23,
					EntityTypeID:            1,
					CountryCodeID:           2,
					ForeignID:               uuid.MustParse("cd5c60b6-3c82-11f0-b338-00090ffe0001"),
				},
				outputErr: nil,
			},
			makeTransfer: makeTransferOp{
				isCalled: true,
				input: &grpc.MakeTransferRequest{
					Account: &grpc.AccountTerminal{
						BankCode: testsdk.Ptr("bcc"),
						Config:   defaultCfg,
					},
					TransferId:          testsdk.Ptr(uint64(1)),
					ExternalReferenceId: testsdk.Ptr(strconv.FormatUint(1, 10)),
					Amount:              testsdk.Ptr(float64(100000)),
					Currency:            testsdk.Ptr("kzt"),
					IsNeedAcceptation:   testsdk.Ptr(int32(1)),
					ServiceParameters: []*grpc.ServiceParameter{
						{
							Code:  testsdk.Ptr("iin"),
							Value: testsdk.Ptr("some bin"),
						},
						{
							Code:  testsdk.Ptr("account"),
							Value: testsdk.Ptr("some recipient account"),
						},
						{
							Code:  testsdk.Ptr("knp"),
							Value: testsdk.Ptr("some payment purpose code"),
						},
						{
							Code:  testsdk.Ptr("code"),
							Value: testsdk.Ptr("some beneficiary code"),
						},
						{
							Code:  testsdk.Ptr("narrative"),
							Value: testsdk.Ptr("some description"),
						},
						{
							Code:  testsdk.Ptr("benIsJur"),
							Value: testsdk.Ptr("some code"),
						},
						{
							Code:  testsdk.Ptr("benCountry"),
							Value: testsdk.Ptr("kz"),
						},
					},
				},
				output: &grpc.MakeTransferResponse{
					TransferId:          testsdk.Ptr(uint64(1)),
					TransferStatusId:    testsdk.Ptr(uint64(2)),
					TransferStatus:      testsdk.Ptr("success"),
					Rrn:                 testsdk.Ptr("some rrn"),
					ExternalReferenceId: testsdk.Ptr("tarlan123"),
				},
				outputErr: nil,
			},
			getTransferStatusByID: getTransferStatusByIDOp{
				isCalled: true,
				input:    2,
				output: &model.TransferStatus{
					ID:   2,
					Name: "success",
					Code: "success",
				},
				outputErr: nil,
			},
			updateTransferStatus: updateTransferStatusOp{
				isCalled:        true,
				inputTransferID: 1,
				inputStatusID:   2,
				inputTime:       &testNow,
				outputErr:       errors.New("some error"),
			},
			appConfig: map[string]any{
				"SYMMETRIC_KEY": normalKey,
			},
		},
		{
			name: "update_transfer_error",
			req: schema.CreateTransferRequest{
				TransferAmount:     100000,
				BalanceOwnerID:     1,
				BalanceOwnerBIN:    "some bin",
				AccountNumber:      "some account number",
				RecipientAccount:   "some recipient account",
				PaymentPurposeCode: "some payment purpose code",
				BeneficiaryCode:    "some beneficiary code",
				Description:        "some description",
				ForeignID:          uuid.MustParse("cd5c60b6-3c82-11f0-b338-00090ffe0001"),
			},
			want:    nil,
			wantErr: errors.New("some error"),
			getTransferStatusByCode: getTransferStatusByCodeOp{
				output: &model.TransferStatus{
					ID:   1,
					Name: "new",
					Code: "new",
				},
				outputErr: nil,
			},
			getAccountInfoByNumber: getAccountInfoByNumberOp{
				isCalled: true,
				input:    "some account number",
				output: &model.Account{
					ID:              23,
					Number:          "some number",
					CurrencyCode:    "kzt",
					BankCode:        "bcc",
					BankID:          25,
					AccountTypeID:   3,
					EncryptedConfig: defaultEncryptedConfig,
				},
				outputErr: nil,
			},
			getMerchantBalanceByBalanceOwnerID: getMerchantBalanceByBalanceOwnerIDOp{
				isCalled: true,
				input: &grpc.GetBalanceOwnerByIDRequestV1{
					BalanceOwnerId: testsdk.Ptr(uint64(1)),
				},
				output: &grpc.GetBalanceOwnerResponseV1{
					MerchantId:    testsdk.Ptr(uint64(21)),
					ProjectId:     testsdk.Ptr(uint64(10)),
					EntityTypeId:  testsdk.Ptr(uint64(1)),
					CountryCodeId: testsdk.Ptr(uint64(2)),
				},
				outputErr: nil,
			},
			checkHasBalanceOp: checkHasBalanceOp{
				isCalled: true,
				input: &grpc.CheckHasBalanceRequestV1{
					ProjectId:     testsdk.Ptr(uint64(10)),
					MerchantId:    testsdk.Ptr(uint64(21)),
					AccountNumber: testsdk.Ptr("some number"),
				},
				output: &empty.Empty{},
			},
			getTransferTypeByCode: getTransferTypeByCodeOp{
				isCalled: true,
				output: &model.TransferType{
					ID:   1,
					Code: model.TypeOut,
					Name: "out",
				},
				outputErr: nil,
			},
			getEntityTypeByCode: getEntityTypeByID{
				isCalled: true,
				input: &grpc.GetEntityTypeByIDRequestV1{
					Id: testsdk.Ptr(uint64(1)),
				},
				output: &grpc.GetEntityTypeResponseV1{
					Id:   testsdk.Ptr(uint64(1)),
					Name: testsdk.Ptr("some name"),
					Code: testsdk.Ptr("some code"),
				},
				outputErr: nil,
			},
			getCountryByID: getCountryByID{
				isCalled: true,
				input: &grpc.GetCountryCodeByIDRequestV1{
					Id: testsdk.Ptr(uint64(2)),
				},
				output: &grpc.GetCountryCodeResponseV1{
					Id:      testsdk.Ptr(uint64(2)),
					Code:    testsdk.Ptr("kz"),
					EngName: testsdk.Ptr("kz"),
					RuName:  testsdk.Ptr("kz"),
					KazName: testsdk.Ptr("kz"),
				},
				outputErr: nil,
			},
			createTransfer: createTransferOp{
				isCalled: true,
				input: model.Transfer{
					ProjectID:               10,
					MerchantID:              21,
					StatusID:                1,
					Amount:                  100000,
					MerchantBIN:             "some bin",
					MerchantAccount:         "some recipient account",
					MerchantBeneficiaryCode: "some beneficiary code",
					Description:             "some description",
					PaymentPurposeCode:      "some payment purpose code",
					TransferTypeID:          1,
					AcquirerID:              25,
					AccountID:               23,
					EntityTypeID:            1,
					CountryCodeID:           2,
					ForeignID:               uuid.MustParse("cd5c60b6-3c82-11f0-b338-00090ffe0001"),
				},
				output: &model.Transfer{
					ID:                      1,
					ProjectID:               10,
					MerchantID:              21,
					StatusID:                1,
					Amount:                  100000,
					MerchantBIN:             "some bin",
					MerchantAccount:         "some recipient account",
					MerchantBeneficiaryCode: "some beneficiary code",
					Description:             "some description",
					PaymentPurposeCode:      "some payment purpose code",
					TransferTypeID:          1,
					AcquirerID:              25,
					AccountID:               23,
					EntityTypeID:            1,
					CountryCodeID:           2,
					ForeignID:               uuid.MustParse("cd5c60b6-3c82-11f0-b338-00090ffe0001"),
				},
				outputErr: nil,
			},
			makeTransfer: makeTransferOp{
				isCalled: true,
				input: &grpc.MakeTransferRequest{
					Account: &grpc.AccountTerminal{
						BankCode: testsdk.Ptr("bcc"),
						Config:   defaultCfg,
					},
					TransferId:          testsdk.Ptr(uint64(1)),
					ExternalReferenceId: testsdk.Ptr(strconv.FormatUint(1, 10)),
					Amount:              testsdk.Ptr(float64(100000)),
					Currency:            testsdk.Ptr("kzt"),
					IsNeedAcceptation:   testsdk.Ptr(int32(1)),
					ServiceParameters: []*grpc.ServiceParameter{
						{
							Code:  testsdk.Ptr("iin"),
							Value: testsdk.Ptr("some bin"),
						},
						{
							Code:  testsdk.Ptr("account"),
							Value: testsdk.Ptr("some recipient account"),
						},
						{
							Code:  testsdk.Ptr("knp"),
							Value: testsdk.Ptr("some payment purpose code"),
						},
						{
							Code:  testsdk.Ptr("code"),
							Value: testsdk.Ptr("some beneficiary code"),
						},
						{
							Code:  testsdk.Ptr("narrative"),
							Value: testsdk.Ptr("some description"),
						},
						{
							Code:  testsdk.Ptr("benIsJur"),
							Value: testsdk.Ptr("some code"),
						},
						{
							Code:  testsdk.Ptr("benCountry"),
							Value: testsdk.Ptr("kz"),
						},
					},
				},
				output: &grpc.MakeTransferResponse{
					TransferId:          testsdk.Ptr(uint64(1)),
					TransferStatusId:    testsdk.Ptr(uint64(2)),
					TransferStatus:      testsdk.Ptr("success"),
					Rrn:                 testsdk.Ptr("some rrn"),
					ExternalReferenceId: testsdk.Ptr("tarlan123"),
				},
				outputErr: nil,
			},
			getTransferStatusByID: getTransferStatusByIDOp{
				isCalled: true,
				input:    2,
				output: &model.TransferStatus{
					ID:   2,
					Name: "success",
					Code: "success",
				},
				outputErr: nil,
			},
			updateTransferStatus: updateTransferStatusOp{
				isCalled:        true,
				inputTransferID: 1,
				inputStatusID:   2,
				inputTime:       &testNow,
				outputErr:       nil,
			},
			updateTransfer: updateTransferOp{
				isCalled: true,
				input: &schema.CreateTransferResponse{
					TransferID:      1,
					StatusID:        2,
					BankReferenceID: "some rrn",
					BankOrderID:     "tarlan123",
				},
				outputErr: errors.New("some error"),
			},
			appConfig: map[string]any{
				"SYMMETRIC_KEY": normalKey,
			},
		},
		{
			name: "success",
			req: schema.CreateTransferRequest{
				TransferAmount:     100000,
				BalanceOwnerID:     1,
				BalanceOwnerBIN:    "some bin",
				AccountNumber:      "some account number",
				RecipientAccount:   "some recipient account",
				PaymentPurposeCode: "some payment purpose code",
				BeneficiaryCode:    "some beneficiary code",
				Description:        "some description",
				ForeignID:          uuid.MustParse("cd5c60b6-3c82-11f0-b338-00090ffe0001"),
			},
			want: &schema.CreateTransferResponse{
				TransferID:      1,
				StatusID:        2,
				BankReferenceID: "some rrn",
				BankOrderID:     "tarlan123",
			},
			wantErr: nil,
			getTransferStatusByCode: getTransferStatusByCodeOp{
				output: &model.TransferStatus{
					ID:   1,
					Name: "new",
					Code: "new",
				},
				outputErr: nil,
			},
			getAccountInfoByNumber: getAccountInfoByNumberOp{
				isCalled: true,
				input:    "some account number",
				output: &model.Account{
					ID:              23,
					Number:          "some number",
					CurrencyCode:    "kzt",
					BankCode:        "bcc",
					BankID:          25,
					AccountTypeID:   3,
					EncryptedConfig: defaultEncryptedConfig,
				},
				outputErr: nil,
			},
			getMerchantBalanceByBalanceOwnerID: getMerchantBalanceByBalanceOwnerIDOp{
				isCalled: true,
				input: &grpc.GetBalanceOwnerByIDRequestV1{
					BalanceOwnerId: testsdk.Ptr(uint64(1)),
				},
				output: &grpc.GetBalanceOwnerResponseV1{
					MerchantId:    testsdk.Ptr(uint64(21)),
					ProjectId:     testsdk.Ptr(uint64(10)),
					EntityTypeId:  testsdk.Ptr(uint64(1)),
					CountryCodeId: testsdk.Ptr(uint64(2)),
				},
				outputErr: nil,
			},
			checkHasBalanceOp: checkHasBalanceOp{
				isCalled: true,
				input: &grpc.CheckHasBalanceRequestV1{
					ProjectId:     testsdk.Ptr(uint64(10)),
					MerchantId:    testsdk.Ptr(uint64(21)),
					AccountNumber: testsdk.Ptr("some number"),
				},
				output: &empty.Empty{},
			},
			getTransferTypeByCode: getTransferTypeByCodeOp{
				isCalled: true,
				output: &model.TransferType{
					ID:   1,
					Code: model.TypeOut,
					Name: "out",
				},
				outputErr: nil,
			},
			getEntityTypeByCode: getEntityTypeByID{
				isCalled: true,
				input: &grpc.GetEntityTypeByIDRequestV1{
					Id: testsdk.Ptr(uint64(1)),
				},
				output: &grpc.GetEntityTypeResponseV1{
					Id:   testsdk.Ptr(uint64(1)),
					Name: testsdk.Ptr("some name"),
					Code: testsdk.Ptr("some code"),
				},
				outputErr: nil,
			},
			getCountryByID: getCountryByID{
				isCalled: true,
				input: &grpc.GetCountryCodeByIDRequestV1{
					Id: testsdk.Ptr(uint64(2)),
				},
				output: &grpc.GetCountryCodeResponseV1{
					Id:      testsdk.Ptr(uint64(2)),
					Code:    testsdk.Ptr("kz"),
					EngName: testsdk.Ptr("kz"),
					RuName:  testsdk.Ptr("kz"),
					KazName: testsdk.Ptr("kz"),
				},
				outputErr: nil,
			},
			createTransfer: createTransferOp{
				isCalled: true,
				input: model.Transfer{
					ProjectID:               10,
					MerchantID:              21,
					StatusID:                1,
					Amount:                  100000,
					MerchantBIN:             "some bin",
					MerchantAccount:         "some recipient account",
					MerchantBeneficiaryCode: "some beneficiary code",
					Description:             "some description",
					PaymentPurposeCode:      "some payment purpose code",
					TransferTypeID:          1,
					AcquirerID:              25,
					AccountID:               23,
					EntityTypeID:            1,
					CountryCodeID:           2,
					ForeignID:               uuid.MustParse("cd5c60b6-3c82-11f0-b338-00090ffe0001"),
				},
				output: &model.Transfer{
					ID:                      1,
					ProjectID:               10,
					MerchantID:              21,
					StatusID:                1,
					Amount:                  100000,
					MerchantBIN:             "some bin",
					MerchantAccount:         "some recipient account",
					MerchantBeneficiaryCode: "some beneficiary code",
					Description:             "some description",
					PaymentPurposeCode:      "some payment purpose code",
					TransferTypeID:          1,
					AcquirerID:              25,
					AccountID:               23,
					EntityTypeID:            1,
					CountryCodeID:           2,
					ForeignID:               uuid.MustParse("cd5c60b6-3c82-11f0-b338-00090ffe0001"),
				},
				outputErr: nil,
			},
			makeTransfer: makeTransferOp{
				isCalled: true,
				input: &grpc.MakeTransferRequest{
					Account: &grpc.AccountTerminal{
						BankCode: testsdk.Ptr("bcc"),
						Config:   defaultCfg,
					},
					TransferId:          testsdk.Ptr(uint64(1)),
					ExternalReferenceId: testsdk.Ptr(strconv.FormatUint(1, 10)),
					Amount:              testsdk.Ptr(float64(100000)),
					Currency:            testsdk.Ptr("kzt"),
					IsNeedAcceptation:   testsdk.Ptr(int32(1)),
					ServiceParameters: []*grpc.ServiceParameter{
						{
							Code:  testsdk.Ptr("iin"),
							Value: testsdk.Ptr("some bin"),
						},
						{
							Code:  testsdk.Ptr("account"),
							Value: testsdk.Ptr("some recipient account"),
						},
						{
							Code:  testsdk.Ptr("knp"),
							Value: testsdk.Ptr("some payment purpose code"),
						},
						{
							Code:  testsdk.Ptr("code"),
							Value: testsdk.Ptr("some beneficiary code"),
						},
						{
							Code:  testsdk.Ptr("narrative"),
							Value: testsdk.Ptr("some description"),
						},
						{
							Code:  testsdk.Ptr("benIsJur"),
							Value: testsdk.Ptr("some code"),
						},
						{
							Code:  testsdk.Ptr("benCountry"),
							Value: testsdk.Ptr("kz"),
						},
					},
				},
				output: &grpc.MakeTransferResponse{
					TransferId:          testsdk.Ptr(uint64(1)),
					TransferStatusId:    testsdk.Ptr(uint64(2)),
					TransferStatus:      testsdk.Ptr("success"),
					Rrn:                 testsdk.Ptr("some rrn"),
					ExternalReferenceId: testsdk.Ptr("tarlan123"),
				},
				outputErr: nil,
			},
			getTransferStatusByID: getTransferStatusByIDOp{
				isCalled: true,
				input:    2,
				output: &model.TransferStatus{
					ID:   2,
					Name: "success",
					Code: "success",
				},
				outputErr: nil,
			},
			updateTransferStatus: updateTransferStatusOp{
				isCalled:        true,
				inputTransferID: 1,
				inputStatusID:   2,
				inputTime:       &testNow,
				outputErr:       nil,
			},
			updateTransfer: updateTransferOp{
				isCalled: true,
				input: &schema.CreateTransferResponse{
					TransferID:      1,
					StatusID:        2,
					BankReferenceID: "some rrn",
					BankOrderID:     "tarlan123",
				},
				outputErr: nil,
			},
			appConfig: map[string]any{
				"SYMMETRIC_KEY": normalKey,
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctrl := gomock.NewController(t)
			defer ctrl.Finish()

			c := context.Background()
			ctx, cancel := context.WithCancel(c)
			e2e.InitAppE2E(t, ctx, cancel, nil, nil, tt.appConfig)

			transferStatusDBMock := databasemocks.NewMockTransferStatuser(ctrl)
			accountInfoDBMock := databasemocks.NewMockAccountInformer(ctrl)
			transferTypeDBMock := databasemocks.NewMockTransferTyper(ctrl)
			transferManagerDBMock := databasemocks.NewMockTransferManager(ctrl)

			billingCliMock := grpcmock.NewMockBillingClient(ctrl)
			multiaccountingCliMock := grpcmock.NewMockMultiaccountingClient(ctrl)

			s := TransferService{
				transferStatusRepo:    transferStatusDBMock,
				transferManageRepo:    transferManagerDBMock,
				transferTypeRepo:      transferTypeDBMock,
				accountInfoRepo:       accountInfoDBMock,
				billingClient:         billingCliMock,
				multiaccountingClient: multiaccountingCliMock,
				getCurrentTime: func() time.Time {
					return testNow
				},
			}

			transferStatusDBMock.EXPECT().GetByCode(
				gomock.Any(),
				model.StatusNew,
			).Return(
				tt.getTransferStatusByCode.output,
				tt.getTransferStatusByCode.outputErr,
			).Times(1)

			if tt.getAccountInfoByNumber.isCalled {
				accountInfoDBMock.EXPECT().GetByNumber(
					gomock.Any(),
					tt.getAccountInfoByNumber.input,
				).Return(
					tt.getAccountInfoByNumber.output,
					tt.getAccountInfoByNumber.outputErr,
				).Times(1)
			}

			if tt.getMerchantBalanceByBalanceOwnerID.isCalled {
				billingCliMock.EXPECT().GetBalanceOwnerByIDV1(
					gomock.Any(),
					tt.getMerchantBalanceByBalanceOwnerID.input,
				).Return(
					tt.getMerchantBalanceByBalanceOwnerID.output,
					tt.getMerchantBalanceByBalanceOwnerID.outputErr,
				).Times(1)
			}

			if tt.checkHasBalanceOp.isCalled {
				billingCliMock.EXPECT().CheckHasBalanceV1(
					gomock.Any(),
					tt.checkHasBalanceOp.input,
				).Return(
					tt.checkHasBalanceOp.output,
					tt.checkHasBalanceOp.outputErr,
				).Times(1)
			}

			if tt.getTransferTypeByCode.isCalled {
				transferTypeDBMock.EXPECT().GetByCode(
					gomock.Any(),
					model.TypeOut,
				).Return(
					tt.getTransferTypeByCode.output,
					tt.getTransferTypeByCode.outputErr,
				).Times(1)
			}

			if tt.getEntityTypeByCode.isCalled {
				billingCliMock.EXPECT().GetEntityTypeByIDV1(
					gomock.Any(),
					tt.getEntityTypeByCode.input,
				).Return(
					tt.getEntityTypeByCode.output,
					tt.getEntityTypeByCode.outputErr,
				).Times(1)
			}

			if tt.getCountryByID.isCalled {
				billingCliMock.EXPECT().GetCountryCodeByIDV1(
					gomock.Any(),
					tt.getCountryByID.input,
				).Return(
					tt.getCountryByID.output,
					tt.getCountryByID.outputErr,
				).Times(1)
			}

			if tt.createTransfer.isCalled {
				transferManagerDBMock.EXPECT().Create(
					gomock.Any(),
					tt.createTransfer.input,
				).Return(
					tt.createTransfer.output,
					tt.createTransfer.outputErr,
				).Times(1)
			}

			if tt.makeTransfer.isCalled {
				multiaccountingCliMock.EXPECT().MakeTransfer(
					gomock.Any(),
					tt.makeTransfer.input,
				).Return(
					tt.makeTransfer.output,
					tt.makeTransfer.outputErr,
				).Times(1)
			}

			if tt.getFailTransferStatus.isCalled {
				transferStatusDBMock.EXPECT().GetByCode(
					gomock.Any(),
					tt.getFailTransferStatus.input,
				).Return(
					tt.getFailTransferStatus.output,
					tt.getFailTransferStatus.outputErr,
				).Times(1)
			}

			if tt.updateTransferToFail.isCalled {
				transferManagerDBMock.EXPECT().UpdateStatus(
					gomock.Any(),
					tt.updateTransferToFail.transferID,
					tt.updateTransferToFail.statusID,
					tt.updateTransferToFail.finishedAt,
				).Return(
					tt.updateTransferToFail.outputErr,
				).Times(1)
			}

			if tt.getTransferStatusByID.isCalled {
				transferStatusDBMock.EXPECT().GetByID(
					gomock.Any(),
					tt.getTransferStatusByID.input,
				).Return(
					tt.getTransferStatusByID.output,
					tt.getTransferStatusByID.outputErr,
				).Times(1)
			}

			if tt.updateTransferStatus.isCalled {
				transferManagerDBMock.EXPECT().UpdateStatus(
					gomock.Any(),
					tt.updateTransferStatus.inputTransferID,
					tt.updateTransferStatus.inputStatusID,
					tt.updateTransferStatus.inputTime,
				).Return(
					tt.updateTransferStatus.outputErr,
				).Times(1)
			}

			if tt.updateTransfer.isCalled {
				transferManagerDBMock.EXPECT().UpdateTransfer(
					gomock.Any(),
					tt.updateTransfer.input,
				).Return(
					tt.updateTransfer.outputErr,
				).Times(1)
			}

			res, err := s.CreateTransfer(context.Background(), tt.req)
			if tt.wantErr != nil {
				require.Error(t, err)
				require.Equal(t, tt.wantErr, err)
			} else {
				require.NoError(t, err)
			}

			require.Equal(t, tt.want, res)
		})
	}
}

func TestTransferServiceAcceptTransfer(t *testing.T) {
	defaultCfg, err := structpb.NewStruct(map[string]any{
		"my-name-is": "skrillex",
	})
	if err != nil {
		t.Fatal(err)
	}

	testNow := time.Date(2009, time.November, 10, 23, 0, 0, 0, time.UTC)

	type getTransferByIDOp struct {
		isCalled  bool
		input     uint64
		output    *model.Transfer
		outputErr error
	}

	type getTransferStatusByIDOp struct {
		isCalled  bool
		input     uint64
		output    *model.TransferStatus
		outputErr error
	}

	type getAccountByIDOp struct {
		isCalled  bool
		input     uint64
		output    *model.Account
		outputErr error
	}

	type checkoutTransferBalanceOp struct {
		isCalled  bool
		input     *grpc.CheckOutTransferBalanceRequestV1
		output    *grpc.CheckOutTransferBalanceResponseV1
		outputErr error
	}

	type declineTransferOp struct {
		isCalled  bool
		input     *grpc.DeclineTransferRequest
		output    *grpc.DeclineTransferResponse
		outputErr error
	}

	type getTransferStatusByCode struct {
		isCalled  bool
		output    *model.TransferStatus
		outputErr error
	}

	type updateStatusOp struct {
		isCalled        bool
		inputTransferID uint64
		inputStatusID   uint64
		inputTime       *time.Time
		outputErr       error
	}

	type acceptTransferOp struct {
		isCalled  bool
		input     *grpc.AcceptTransferRequest
		output    *grpc.AcceptTransferResponse
		outputErr error
	}

	type billOutTransferOp struct {
		isCalled  bool
		input     *grpc.BillOutTransferRequestV1
		outputErr error
	}

	tests := []struct {
		name                    string
		req                     schema.AcceptTransferRequest
		want                    *schema.AcceptTransferResponse
		wantErr                 error
		getTransferByID         getTransferByIDOp
		getTransferStatus       getTransferStatusByIDOp
		getAccountByID          getAccountByIDOp
		checkoutTransferBalance checkoutTransferBalanceOp
		declineTransfer         declineTransferOp
		getTransferStatusByCode getTransferStatusByCode
		updateStatus            updateStatusOp
		acceptTransfer          acceptTransferOp
		getTransferStatusByID2  getTransferStatusByIDOp
		updateStatus2           updateStatusOp
		billOutTransfer         billOutTransferOp
		appConfig               map[string]any
	}{
		{
			name: "error_getting_transfer_by_id",
			req: schema.AcceptTransferRequest{
				TransfersList: []uint64{1},
			},
			want:    nil,
			wantErr: errors.New("some error"),
			getTransferByID: getTransferByIDOp{
				input:     1,
				output:    nil,
				outputErr: errors.New("some error"),
			},
		},
		{
			name: "error_getting_transfer_status_id",
			req: schema.AcceptTransferRequest{
				TransfersList: []uint64{1},
			},
			want:    nil,
			wantErr: errors.New("some error"),
			getTransferByID: getTransferByIDOp{
				input: 1,
				output: &model.Transfer{
					ID:         1,
					StatusID:   2,
					AccountID:  20,
					ProjectID:  10,
					Amount:     100,
					MerchantID: 81,
				},
				outputErr: nil,
			},
			getTransferStatus: getTransferStatusByIDOp{
				isCalled:  true,
				input:     2,
				output:    nil,
				outputErr: errors.New("some error"),
			},
		},
		{
			name: "transfer_status_is_not_authorized",
			req: schema.AcceptTransferRequest{
				TransfersList: []uint64{1},
			},
			want:    nil,
			wantErr: goerr.ErrStatusValidation,
			getTransferByID: getTransferByIDOp{
				input: 1,
				output: &model.Transfer{
					ID:         1,
					StatusID:   2,
					AccountID:  20,
					ProjectID:  10,
					Amount:     100,
					MerchantID: 81,
				},
				outputErr: nil,
			},
			getTransferStatus: getTransferStatusByIDOp{
				isCalled: true,
				input:    2,
				output: &model.TransferStatus{
					ID:   2,
					Code: "not auth",
					Name: "not auth",
				},
				outputErr: nil,
			},
		},
		{
			name: "get_account_error",
			req: schema.AcceptTransferRequest{
				TransfersList: []uint64{1},
			},
			want:    nil,
			wantErr: errors.New("some error"),
			getTransferByID: getTransferByIDOp{
				input: 1,
				output: &model.Transfer{
					ID:         1,
					StatusID:   2,
					AccountID:  20,
					ProjectID:  10,
					Amount:     100,
					MerchantID: 81,
				},
				outputErr: nil,
			},
			getTransferStatus: getTransferStatusByIDOp{
				isCalled: true,
				input:    2,
				output: &model.TransferStatus{
					ID:   2,
					Code: model.StatusAuthorized,
					Name: "authorized",
				},
				outputErr: nil,
			},
			getAccountByID: getAccountByIDOp{
				isCalled:  true,
				input:     20,
				output:    nil,
				outputErr: errors.New("some error"),
			},
		},
		{
			name: "unmarshalling_error",
			req: schema.AcceptTransferRequest{
				TransfersList: []uint64{1},
			},
			want:    nil,
			wantErr: aes.KeySizeError(3),
			getTransferByID: getTransferByIDOp{
				input: 1,
				output: &model.Transfer{
					ID:         1,
					StatusID:   2,
					AccountID:  20,
					ProjectID:  10,
					Amount:     100,
					MerchantID: 81,
				},
				outputErr: nil,
			},
			getTransferStatus: getTransferStatusByIDOp{
				isCalled: true,
				input:    2,
				output: &model.TransferStatus{
					ID:   2,
					Code: model.StatusAuthorized,
					Name: "authorized",
				},
				outputErr: nil,
			},
			getAccountByID: getAccountByIDOp{
				isCalled: true,
				input:    20,
				output: &model.Account{
					ID:              20,
					Number:          "some number",
					CurrencyCode:    "some currency code",
					BankID:          40,
					BankCode:        "bcc",
					EncryptedConfig: defaultEncryptedConfig,
				},
				outputErr: nil,
			},
			appConfig: map[string]any{
				"SYMMETRIC_KEY": badKey,
			},
		},
		{
			name: "checkout_transfer_balance_error",
			req: schema.AcceptTransferRequest{
				TransfersList: []uint64{1},
			},
			want:    nil,
			wantErr: errors.New("some error"),
			getTransferByID: getTransferByIDOp{
				input: 1,
				output: &model.Transfer{
					ID:         1,
					StatusID:   2,
					AccountID:  20,
					ProjectID:  10,
					Amount:     100,
					MerchantID: 81,
				},
				outputErr: nil,
			},
			getTransferStatus: getTransferStatusByIDOp{
				isCalled: true,
				input:    2,
				output: &model.TransferStatus{
					ID:   2,
					Code: model.StatusAuthorized,
					Name: "authorized",
				},
				outputErr: nil,
			},
			getAccountByID: getAccountByIDOp{
				isCalled: true,
				input:    20,
				output: &model.Account{
					ID:              20,
					Number:          "some number",
					CurrencyCode:    "some currency code",
					BankID:          40,
					BankCode:        "bcc",
					EncryptedConfig: defaultEncryptedConfig,
				},
				outputErr: nil,
			},
			checkoutTransferBalance: checkoutTransferBalanceOp{
				isCalled: true,
				input: &grpc.CheckOutTransferBalanceRequestV1{
					ProjectId:     testsdk.Ptr(uint64(10)),
					AccountNumber: testsdk.Ptr("some number"),
					Amount:        testsdk.Ptr(float64(100)),
					MerchantId:    testsdk.Ptr(uint64(81)),
				},
				outputErr: errors.New("some error"),
				output:    nil,
			},
			appConfig: map[string]any{
				"SYMMETRIC_KEY": normalKey,
			},
		},
		{
			name: "insufficient_balance-error_declining_transfer",
			req: schema.AcceptTransferRequest{
				TransfersList: []uint64{1},
			},
			want:    nil,
			wantErr: errors.New("some error"),
			getTransferByID: getTransferByIDOp{
				input: 1,
				output: &model.Transfer{
					ID:         1,
					StatusID:   2,
					AccountID:  20,
					ProjectID:  10,
					Amount:     100,
					MerchantID: 81,
				},
				outputErr: nil,
			},
			getTransferStatus: getTransferStatusByIDOp{
				isCalled: true,
				input:    2,
				output: &model.TransferStatus{
					ID:   2,
					Code: model.StatusAuthorized,
					Name: "authorized",
				},
				outputErr: nil,
			},
			getAccountByID: getAccountByIDOp{
				isCalled: true,
				input:    20,
				output: &model.Account{
					ID:              20,
					Number:          "some number",
					CurrencyCode:    "some currency code",
					BankID:          40,
					BankCode:        "bcc",
					EncryptedConfig: defaultEncryptedConfig,
				},
				outputErr: nil,
			},
			checkoutTransferBalance: checkoutTransferBalanceOp{
				isCalled: true,
				input: &grpc.CheckOutTransferBalanceRequestV1{
					ProjectId:     testsdk.Ptr(uint64(10)),
					AccountNumber: testsdk.Ptr("some number"),
					Amount:        testsdk.Ptr(float64(100)),
					MerchantId:    testsdk.Ptr(uint64(81)),
				},
				outputErr: nil,
				output: &grpc.CheckOutTransferBalanceResponseV1{
					IsSufficient: testsdk.Ptr(false),
				},
			},
			declineTransfer: declineTransferOp{
				isCalled: true,
				input: &grpc.DeclineTransferRequest{
					Account: &grpc.AccountTerminal{
						BankCode: testsdk.Ptr("bcc"),
						Config:   defaultCfg,
					},
					TransferId:          testsdk.Ptr(uint64(1)),
					ExternalReferenceId: testsdk.Ptr(strconv.FormatUint(1, 10)),
				},
				output:    nil,
				outputErr: errors.New("some error"),
			},
			appConfig: map[string]any{
				"SYMMETRIC_KEY": normalKey,
			},
		},
		{
			name: "insufficient_balance-error_getting_fail_status_code",
			req: schema.AcceptTransferRequest{
				TransfersList: []uint64{1},
			},
			want:    nil,
			wantErr: errors.New("some error"),
			getTransferByID: getTransferByIDOp{
				input: 1,
				output: &model.Transfer{
					ID:         1,
					StatusID:   2,
					AccountID:  20,
					ProjectID:  10,
					Amount:     100,
					MerchantID: 81,
				},
				outputErr: nil,
			},
			getTransferStatus: getTransferStatusByIDOp{
				isCalled: true,
				input:    2,
				output: &model.TransferStatus{
					ID:   2,
					Code: model.StatusAuthorized,
					Name: "authorized",
				},
				outputErr: nil,
			},
			getAccountByID: getAccountByIDOp{
				isCalled: true,
				input:    20,
				output: &model.Account{
					ID:              20,
					Number:          "some number",
					CurrencyCode:    "some currency code",
					BankID:          40,
					BankCode:        "bcc",
					EncryptedConfig: defaultEncryptedConfig,
				},
				outputErr: nil,
			},
			checkoutTransferBalance: checkoutTransferBalanceOp{
				isCalled: true,
				input: &grpc.CheckOutTransferBalanceRequestV1{
					ProjectId:     testsdk.Ptr(uint64(10)),
					AccountNumber: testsdk.Ptr("some number"),
					Amount:        testsdk.Ptr(float64(100)),
					MerchantId:    testsdk.Ptr(uint64(81)),
				},
				outputErr: nil,
				output: &grpc.CheckOutTransferBalanceResponseV1{
					IsSufficient: testsdk.Ptr(false),
				},
			},
			declineTransfer: declineTransferOp{
				isCalled: true,
				input: &grpc.DeclineTransferRequest{
					Account: &grpc.AccountTerminal{
						BankCode: testsdk.Ptr("bcc"),
						Config:   defaultCfg,
					},
					TransferId:          testsdk.Ptr(uint64(1)),
					ExternalReferenceId: testsdk.Ptr(strconv.FormatUint(1, 10)),
				},
				output: &grpc.DeclineTransferResponse{
					TransferId:     testsdk.Ptr(uint64(1)),
					TransferStatus: testsdk.Ptr("some status"),
				},
				outputErr: nil,
			},
			getTransferStatusByCode: getTransferStatusByCode{
				isCalled:  true,
				output:    nil,
				outputErr: errors.New("some error"),
			},
			appConfig: map[string]any{
				"SYMMETRIC_KEY": normalKey,
			},
		},
		{
			name: "insufficient_balance-update_status_error",
			req: schema.AcceptTransferRequest{
				TransfersList: []uint64{1},
			},
			want:    nil,
			wantErr: errors.New("some error"),
			getTransferByID: getTransferByIDOp{
				input: 1,
				output: &model.Transfer{
					ID:         1,
					StatusID:   2,
					AccountID:  20,
					ProjectID:  10,
					Amount:     100,
					MerchantID: 81,
				},
				outputErr: nil,
			},
			getTransferStatus: getTransferStatusByIDOp{
				isCalled: true,
				input:    2,
				output: &model.TransferStatus{
					ID:   2,
					Code: model.StatusAuthorized,
					Name: "authorized",
				},
				outputErr: nil,
			},
			getAccountByID: getAccountByIDOp{
				isCalled: true,
				input:    20,
				output: &model.Account{
					ID:              20,
					Number:          "some number",
					CurrencyCode:    "some currency code",
					BankID:          40,
					BankCode:        "bcc",
					EncryptedConfig: defaultEncryptedConfig,
				},
				outputErr: nil,
			},
			checkoutTransferBalance: checkoutTransferBalanceOp{
				isCalled: true,
				input: &grpc.CheckOutTransferBalanceRequestV1{
					ProjectId:     testsdk.Ptr(uint64(10)),
					AccountNumber: testsdk.Ptr("some number"),
					Amount:        testsdk.Ptr(float64(100)),
					MerchantId:    testsdk.Ptr(uint64(81)),
				},
				outputErr: nil,
				output: &grpc.CheckOutTransferBalanceResponseV1{
					IsSufficient: testsdk.Ptr(false),
				},
			},
			declineTransfer: declineTransferOp{
				isCalled: true,
				input: &grpc.DeclineTransferRequest{
					Account: &grpc.AccountTerminal{
						BankCode: testsdk.Ptr("bcc"),
						Config:   defaultCfg,
					},
					TransferId:          testsdk.Ptr(uint64(1)),
					ExternalReferenceId: testsdk.Ptr(strconv.FormatUint(1, 10)),
				},
				output: &grpc.DeclineTransferResponse{
					TransferId:     testsdk.Ptr(uint64(1)),
					TransferStatus: testsdk.Ptr("some status"),
				},
				outputErr: nil,
			},
			getTransferStatusByCode: getTransferStatusByCode{
				isCalled: true,
				output: &model.TransferStatus{
					ID:   6,
					Code: model.StatusFailed,
					Name: "failed",
				},
				outputErr: nil,
			},
			updateStatus: updateStatusOp{
				isCalled:        true,
				inputTime:       &testNow,
				inputTransferID: 1,
				inputStatusID:   6,
				outputErr:       errors.New("some error"),
			},
			appConfig: map[string]any{
				"SYMMETRIC_KEY": normalKey,
			},
		},
		{
			name: "insufficient_balance-success",
			req: schema.AcceptTransferRequest{
				TransfersList: []uint64{1},
			},
			want: &schema.AcceptTransferResponse{
				Responses: []*schema.TransferResponse{
					{
						TransferID: 1,
						StatusID:   4,
					},
				},
			},
			wantErr: nil,
			getTransferByID: getTransferByIDOp{
				input: 1,
				output: &model.Transfer{
					ID:         1,
					StatusID:   2,
					AccountID:  20,
					ProjectID:  10,
					Amount:     100,
					MerchantID: 81,
				},
				outputErr: nil,
			},
			getTransferStatus: getTransferStatusByIDOp{
				isCalled: true,
				input:    2,
				output: &model.TransferStatus{
					ID:   2,
					Code: model.StatusAuthorized,
					Name: "authorized",
				},
				outputErr: nil,
			},
			getAccountByID: getAccountByIDOp{
				isCalled: true,
				input:    20,
				output: &model.Account{
					ID:              20,
					Number:          "some number",
					CurrencyCode:    "some currency code",
					BankID:          40,
					BankCode:        "bcc",
					EncryptedConfig: defaultEncryptedConfig,
				},
				outputErr: nil,
			},
			checkoutTransferBalance: checkoutTransferBalanceOp{
				isCalled: true,
				input: &grpc.CheckOutTransferBalanceRequestV1{
					ProjectId:     testsdk.Ptr(uint64(10)),
					AccountNumber: testsdk.Ptr("some number"),
					Amount:        testsdk.Ptr(float64(100)),
					MerchantId:    testsdk.Ptr(uint64(81)),
				},
				outputErr: nil,
				output: &grpc.CheckOutTransferBalanceResponseV1{
					IsSufficient: testsdk.Ptr(false),
				},
			},
			declineTransfer: declineTransferOp{
				isCalled: true,
				input: &grpc.DeclineTransferRequest{
					Account: &grpc.AccountTerminal{
						BankCode: testsdk.Ptr("bcc"),
						Config:   defaultCfg,
					},
					TransferId:          testsdk.Ptr(uint64(1)),
					ExternalReferenceId: testsdk.Ptr(strconv.FormatUint(1, 10)),
				},
				output: &grpc.DeclineTransferResponse{
					TransferId:       testsdk.Ptr(uint64(1)),
					TransferStatus:   testsdk.Ptr("some status"),
					TransferStatusId: testsdk.Ptr(uint64(4)),
				},
				outputErr: nil,
			},
			getTransferStatusByCode: getTransferStatusByCode{
				isCalled: true,
				output: &model.TransferStatus{
					ID:   6,
					Code: model.StatusFailed,
					Name: "failed",
				},
				outputErr: nil,
			},
			updateStatus: updateStatusOp{
				isCalled:        true,
				inputTime:       &testNow,
				inputTransferID: 1,
				inputStatusID:   6,
				outputErr:       nil,
			},
			appConfig: map[string]any{
				"SYMMETRIC_KEY": normalKey,
			},
		},
		{
			name: "accept_transfer_error",
			req: schema.AcceptTransferRequest{
				TransfersList: []uint64{1},
			},
			want:    nil,
			wantErr: errors.New("some error"),
			getTransferByID: getTransferByIDOp{
				input: 1,
				output: &model.Transfer{
					ID:         1,
					StatusID:   2,
					AccountID:  20,
					ProjectID:  10,
					Amount:     100,
					MerchantID: 81,
				},
				outputErr: nil,
			},
			getTransferStatus: getTransferStatusByIDOp{
				isCalled: true,
				input:    2,
				output: &model.TransferStatus{
					ID:   2,
					Code: model.StatusAuthorized,
					Name: "authorized",
				},
				outputErr: nil,
			},
			getAccountByID: getAccountByIDOp{
				isCalled: true,
				input:    20,
				output: &model.Account{
					ID:              20,
					Number:          "some number",
					CurrencyCode:    "some currency code",
					BankID:          40,
					BankCode:        "bcc",
					EncryptedConfig: defaultEncryptedConfig,
				},
				outputErr: nil,
			},
			checkoutTransferBalance: checkoutTransferBalanceOp{
				isCalled: true,
				input: &grpc.CheckOutTransferBalanceRequestV1{
					ProjectId:     testsdk.Ptr(uint64(10)),
					AccountNumber: testsdk.Ptr("some number"),
					Amount:        testsdk.Ptr(float64(100)),
					MerchantId:    testsdk.Ptr(uint64(81)),
				},
				outputErr: nil,
				output: &grpc.CheckOutTransferBalanceResponseV1{
					IsSufficient: testsdk.Ptr(true),
				},
			},
			declineTransfer: declineTransferOp{
				isCalled: false,
				input: &grpc.DeclineTransferRequest{
					Account: &grpc.AccountTerminal{
						BankCode: testsdk.Ptr("bcc"),
						Config:   defaultCfg,
					},
					TransferId:          testsdk.Ptr(uint64(1)),
					ExternalReferenceId: testsdk.Ptr(strconv.FormatUint(1, 10)),
				},
				output: &grpc.DeclineTransferResponse{
					TransferId:       testsdk.Ptr(uint64(1)),
					TransferStatus:   testsdk.Ptr("some status"),
					TransferStatusId: testsdk.Ptr(uint64(4)),
				},
				outputErr: nil,
			},
			getTransferStatusByCode: getTransferStatusByCode{
				isCalled: false,
				output: &model.TransferStatus{
					ID:   6,
					Code: model.StatusFailed,
					Name: "failed",
				},
				outputErr: nil,
			},
			updateStatus: updateStatusOp{
				isCalled:        false,
				inputTime:       &testNow,
				inputTransferID: 1,
				inputStatusID:   6,
				outputErr:       nil,
			},
			acceptTransfer: acceptTransferOp{
				isCalled: true,
				input: &grpc.AcceptTransferRequest{
					Account: &grpc.AccountTerminal{
						BankCode: testsdk.Ptr("bcc"),
						Config:   defaultCfg,
					},
					TransferId:          testsdk.Ptr(uint64(1)),
					ExternalReferenceId: testsdk.Ptr(strconv.FormatUint(1, 10)),
				},
				output:    nil,
				outputErr: errors.New("some error"),
			},
			appConfig: map[string]any{
				"SYMMETRIC_KEY": normalKey,
			},
		},
		{
			name: "get_transfer_by_id2_error",
			req: schema.AcceptTransferRequest{
				TransfersList: []uint64{1},
			},
			want:    nil,
			wantErr: errors.New("some error"),
			getTransferByID: getTransferByIDOp{
				input: 1,
				output: &model.Transfer{
					ID:         1,
					StatusID:   2,
					AccountID:  20,
					ProjectID:  10,
					Amount:     100,
					MerchantID: 81,
				},
				outputErr: nil,
			},
			getTransferStatus: getTransferStatusByIDOp{
				isCalled: true,
				input:    2,
				output: &model.TransferStatus{
					ID:   2,
					Code: model.StatusAuthorized,
					Name: "authorized",
				},
				outputErr: nil,
			},
			getAccountByID: getAccountByIDOp{
				isCalled: true,
				input:    20,
				output: &model.Account{
					ID:              20,
					Number:          "some number",
					CurrencyCode:    "some currency code",
					BankID:          40,
					BankCode:        "bcc",
					EncryptedConfig: defaultEncryptedConfig,
				},
				outputErr: nil,
			},
			checkoutTransferBalance: checkoutTransferBalanceOp{
				isCalled: true,
				input: &grpc.CheckOutTransferBalanceRequestV1{
					ProjectId:     testsdk.Ptr(uint64(10)),
					AccountNumber: testsdk.Ptr("some number"),
					Amount:        testsdk.Ptr(float64(100)),
					MerchantId:    testsdk.Ptr(uint64(81)),
				},
				outputErr: nil,
				output: &grpc.CheckOutTransferBalanceResponseV1{
					IsSufficient: testsdk.Ptr(true),
				},
			},
			declineTransfer: declineTransferOp{
				isCalled: false,
				input: &grpc.DeclineTransferRequest{
					Account: &grpc.AccountTerminal{
						BankCode: testsdk.Ptr("bcc"),
						Config:   defaultCfg,
					},
					TransferId:          testsdk.Ptr(uint64(1)),
					ExternalReferenceId: testsdk.Ptr(strconv.FormatUint(1, 10)),
				},
				output: &grpc.DeclineTransferResponse{
					TransferId:       testsdk.Ptr(uint64(1)),
					TransferStatus:   testsdk.Ptr("some status"),
					TransferStatusId: testsdk.Ptr(uint64(4)),
				},
				outputErr: nil,
			},
			getTransferStatusByCode: getTransferStatusByCode{
				isCalled: false,
				output: &model.TransferStatus{
					ID:   6,
					Code: model.StatusFailed,
					Name: "failed",
				},
				outputErr: nil,
			},
			updateStatus: updateStatusOp{
				isCalled:        false,
				inputTime:       &testNow,
				inputTransferID: 1,
				inputStatusID:   6,
				outputErr:       nil,
			},
			acceptTransfer: acceptTransferOp{
				isCalled: true,
				input: &grpc.AcceptTransferRequest{
					Account: &grpc.AccountTerminal{
						BankCode: testsdk.Ptr("bcc"),
						Config:   defaultCfg,
					},
					TransferId:          testsdk.Ptr(uint64(1)),
					ExternalReferenceId: testsdk.Ptr(strconv.FormatUint(1, 10)),
				},
				output: &grpc.AcceptTransferResponse{
					TransferId:       testsdk.Ptr(uint64(1)),
					TransferStatusId: testsdk.Ptr(uint64(5)), // non success
				},
				outputErr: nil,
			},
			getTransferStatusByID2: getTransferStatusByIDOp{
				isCalled:  true,
				input:     5,
				output:    nil,
				outputErr: errors.New("some error"),
			},
			appConfig: map[string]any{
				"SYMMETRIC_KEY": normalKey,
			},
		},
		{
			name: "update_status_error",
			req: schema.AcceptTransferRequest{
				TransfersList: []uint64{1},
			},
			want:    nil,
			wantErr: errors.New("some error"),
			getTransferByID: getTransferByIDOp{
				input: 1,
				output: &model.Transfer{
					ID:         1,
					StatusID:   2,
					AccountID:  20,
					ProjectID:  10,
					Amount:     100,
					MerchantID: 81,
				},
				outputErr: nil,
			},
			getTransferStatus: getTransferStatusByIDOp{
				isCalled: true,
				input:    2,
				output: &model.TransferStatus{
					ID:   2,
					Code: model.StatusAuthorized,
					Name: "authorized",
				},
				outputErr: nil,
			},
			getAccountByID: getAccountByIDOp{
				isCalled: true,
				input:    20,
				output: &model.Account{
					ID:              20,
					Number:          "some number",
					CurrencyCode:    "some currency code",
					BankID:          40,
					BankCode:        "bcc",
					EncryptedConfig: defaultEncryptedConfig,
				},
				outputErr: nil,
			},
			checkoutTransferBalance: checkoutTransferBalanceOp{
				isCalled: true,
				input: &grpc.CheckOutTransferBalanceRequestV1{
					ProjectId:     testsdk.Ptr(uint64(10)),
					AccountNumber: testsdk.Ptr("some number"),
					Amount:        testsdk.Ptr(float64(100)),
					MerchantId:    testsdk.Ptr(uint64(81)),
				},
				outputErr: nil,
				output: &grpc.CheckOutTransferBalanceResponseV1{
					IsSufficient: testsdk.Ptr(true),
				},
			},
			declineTransfer: declineTransferOp{
				isCalled: false,
				input: &grpc.DeclineTransferRequest{
					Account: &grpc.AccountTerminal{
						BankCode: testsdk.Ptr("bcc"),
						Config:   defaultCfg,
					},
					TransferId:          testsdk.Ptr(uint64(1)),
					ExternalReferenceId: testsdk.Ptr(strconv.FormatUint(1, 10)),
				},
				output: &grpc.DeclineTransferResponse{
					TransferId:       testsdk.Ptr(uint64(1)),
					TransferStatus:   testsdk.Ptr("some status"),
					TransferStatusId: testsdk.Ptr(uint64(4)),
				},
				outputErr: nil,
			},
			getTransferStatusByCode: getTransferStatusByCode{
				isCalled: false,
				output: &model.TransferStatus{
					ID:   6,
					Code: model.StatusFailed,
					Name: "failed",
				},
				outputErr: nil,
			},
			updateStatus: updateStatusOp{
				isCalled:        false,
				inputTime:       &testNow,
				inputTransferID: 1,
				inputStatusID:   6,
				outputErr:       nil,
			},
			acceptTransfer: acceptTransferOp{
				isCalled: true,
				input: &grpc.AcceptTransferRequest{
					Account: &grpc.AccountTerminal{
						BankCode: testsdk.Ptr("bcc"),
						Config:   defaultCfg,
					},
					TransferId:          testsdk.Ptr(uint64(1)),
					ExternalReferenceId: testsdk.Ptr(strconv.FormatUint(1, 10)),
				},
				output: &grpc.AcceptTransferResponse{
					TransferId:       testsdk.Ptr(uint64(1)),
					TransferStatusId: testsdk.Ptr(uint64(5)), // non success
				},
				outputErr: nil,
			},
			getTransferStatusByID2: getTransferStatusByIDOp{
				isCalled: true,
				input:    5,
				output: &model.TransferStatus{
					ID:   5,
					Code: model.StatusCanceled,
					Name: "canceled",
				},
				outputErr: nil,
			},
			updateStatus2: updateStatusOp{
				isCalled:        true,
				inputStatusID:   5,
				inputTransferID: 1,
				inputTime:       &testNow,
				outputErr:       errors.New("some error"),
			},
			appConfig: map[string]any{
				"SYMMETRIC_KEY": normalKey,
			},
		},
		{
			name: "non_success_transfer_success",
			req: schema.AcceptTransferRequest{
				TransfersList: []uint64{1},
			},
			want: &schema.AcceptTransferResponse{
				Responses: []*schema.TransferResponse{
					{
						TransferID: 1,
						StatusID:   5,
					},
				},
			},
			wantErr: nil,
			getTransferByID: getTransferByIDOp{
				input: 1,
				output: &model.Transfer{
					ID:         1,
					StatusID:   2,
					AccountID:  20,
					ProjectID:  10,
					Amount:     100,
					MerchantID: 81,
				},
				outputErr: nil,
			},
			getTransferStatus: getTransferStatusByIDOp{
				isCalled: true,
				input:    2,
				output: &model.TransferStatus{
					ID:   2,
					Code: model.StatusAuthorized,
					Name: "authorized",
				},
				outputErr: nil,
			},
			getAccountByID: getAccountByIDOp{
				isCalled: true,
				input:    20,
				output: &model.Account{
					ID:              20,
					Number:          "some number",
					CurrencyCode:    "some currency code",
					BankID:          40,
					BankCode:        "bcc",
					EncryptedConfig: defaultEncryptedConfig,
				},
				outputErr: nil,
			},
			checkoutTransferBalance: checkoutTransferBalanceOp{
				isCalled: true,
				input: &grpc.CheckOutTransferBalanceRequestV1{
					ProjectId:     testsdk.Ptr(uint64(10)),
					AccountNumber: testsdk.Ptr("some number"),
					Amount:        testsdk.Ptr(float64(100)),
					MerchantId:    testsdk.Ptr(uint64(81)),
				},
				outputErr: nil,
				output: &grpc.CheckOutTransferBalanceResponseV1{
					IsSufficient: testsdk.Ptr(true),
				},
			},
			declineTransfer: declineTransferOp{
				isCalled: false,
				input: &grpc.DeclineTransferRequest{
					Account: &grpc.AccountTerminal{
						BankCode: testsdk.Ptr("bcc"),
						Config:   defaultCfg,
					},
					TransferId:          testsdk.Ptr(uint64(1)),
					ExternalReferenceId: testsdk.Ptr(strconv.FormatUint(1, 10)),
				},
				output: &grpc.DeclineTransferResponse{
					TransferId:       testsdk.Ptr(uint64(1)),
					TransferStatus:   testsdk.Ptr("some status"),
					TransferStatusId: testsdk.Ptr(uint64(4)),
				},
				outputErr: nil,
			},
			getTransferStatusByCode: getTransferStatusByCode{
				isCalled: false,
				output: &model.TransferStatus{
					ID:   6,
					Code: model.StatusFailed,
					Name: "failed",
				},
				outputErr: nil,
			},
			updateStatus: updateStatusOp{
				isCalled:        false,
				inputTime:       &testNow,
				inputTransferID: 1,
				inputStatusID:   6,
				outputErr:       nil,
			},
			acceptTransfer: acceptTransferOp{
				isCalled: true,
				input: &grpc.AcceptTransferRequest{
					Account: &grpc.AccountTerminal{
						BankCode: testsdk.Ptr("bcc"),
						Config:   defaultCfg,
					},
					TransferId:          testsdk.Ptr(uint64(1)),
					ExternalReferenceId: testsdk.Ptr(strconv.FormatUint(1, 10)),
				},
				output: &grpc.AcceptTransferResponse{
					TransferId:       testsdk.Ptr(uint64(1)),
					TransferStatusId: testsdk.Ptr(uint64(5)), // non success
				},
				outputErr: nil,
			},
			getTransferStatusByID2: getTransferStatusByIDOp{
				isCalled: true,
				input:    5,
				output: &model.TransferStatus{
					ID:   5,
					Code: model.StatusCanceled,
					Name: "canceled",
				},
				outputErr: nil,
			},
			updateStatus2: updateStatusOp{
				isCalled:        true,
				inputStatusID:   5,
				inputTransferID: 1,
				inputTime:       &testNow,
				outputErr:       nil,
			},
			appConfig: map[string]any{
				"SYMMETRIC_KEY": normalKey,
			},
		},
		{
			name: "billing_error",
			req: schema.AcceptTransferRequest{
				TransfersList: []uint64{1},
			},
			want: &schema.AcceptTransferResponse{
				Responses: []*schema.TransferResponse{
					{
						TransferID: 1,
						StatusID:   9,
					},
				},
			},
			wantErr: nil,
			getTransferByID: getTransferByIDOp{
				input: 1,
				output: &model.Transfer{
					ID:         1,
					StatusID:   2,
					AccountID:  20,
					ProjectID:  10,
					Amount:     100,
					MerchantID: 81,
				},
				outputErr: nil,
			},
			getTransferStatus: getTransferStatusByIDOp{
				isCalled: true,
				input:    2,
				output: &model.TransferStatus{
					ID:   2,
					Code: model.StatusAuthorized,
					Name: "authorized",
				},
				outputErr: nil,
			},
			getAccountByID: getAccountByIDOp{
				isCalled: true,
				input:    20,
				output: &model.Account{
					ID:              20,
					Number:          "some number",
					CurrencyCode:    "some currency code",
					BankID:          40,
					BankCode:        "bcc",
					EncryptedConfig: defaultEncryptedConfig,
				},
				outputErr: nil,
			},
			checkoutTransferBalance: checkoutTransferBalanceOp{
				isCalled: true,
				input: &grpc.CheckOutTransferBalanceRequestV1{
					ProjectId:     testsdk.Ptr(uint64(10)),
					AccountNumber: testsdk.Ptr("some number"),
					Amount:        testsdk.Ptr(float64(100)),
					MerchantId:    testsdk.Ptr(uint64(81)),
				},
				outputErr: nil,
				output: &grpc.CheckOutTransferBalanceResponseV1{
					IsSufficient: testsdk.Ptr(true),
				},
			},
			declineTransfer: declineTransferOp{
				isCalled: false,
				input: &grpc.DeclineTransferRequest{
					Account: &grpc.AccountTerminal{
						BankCode: testsdk.Ptr("bcc"),
						Config:   defaultCfg,
					},
					TransferId:          testsdk.Ptr(uint64(1)),
					ExternalReferenceId: testsdk.Ptr(strconv.FormatUint(1, 10)),
				},
				output: &grpc.DeclineTransferResponse{
					TransferId:       testsdk.Ptr(uint64(1)),
					TransferStatus:   testsdk.Ptr("some status"),
					TransferStatusId: testsdk.Ptr(uint64(4)),
				},
				outputErr: nil,
			},
			getTransferStatusByCode: getTransferStatusByCode{
				isCalled: false,
				output: &model.TransferStatus{
					ID:   6,
					Code: model.StatusFailed,
					Name: "failed",
				},
				outputErr: nil,
			},
			updateStatus: updateStatusOp{
				isCalled:        false,
				inputTime:       &testNow,
				inputTransferID: 1,
				inputStatusID:   6,
				outputErr:       nil,
			},
			acceptTransfer: acceptTransferOp{
				isCalled: true,
				input: &grpc.AcceptTransferRequest{
					Account: &grpc.AccountTerminal{
						BankCode: testsdk.Ptr("bcc"),
						Config:   defaultCfg,
					},
					TransferId:          testsdk.Ptr(uint64(1)),
					ExternalReferenceId: testsdk.Ptr(strconv.FormatUint(1, 10)),
				},
				output: &grpc.AcceptTransferResponse{
					TransferId:       testsdk.Ptr(uint64(1)),
					TransferStatusId: testsdk.Ptr(uint64(9)), // non success
				},
				outputErr: nil,
			},
			getTransferStatusByID2: getTransferStatusByIDOp{
				isCalled: true,
				input:    9,
				output: &model.TransferStatus{
					ID:   9,
					Code: model.StatusSuccess,
					Name: "success",
				},
				outputErr: nil,
			},
			updateStatus2: updateStatusOp{
				isCalled:        true,
				inputStatusID:   9,
				inputTransferID: 1,
				inputTime:       &testNow,
				outputErr:       nil,
			},
			billOutTransfer: billOutTransferOp{
				isCalled: true,
				input: &grpc.BillOutTransferRequestV1{
					ProjectId:     testsdk.Ptr(uint64(10)),
					AccountNumber: testsdk.Ptr("some number"),
					Amount:        testsdk.Ptr(float64(100)),
					MerchantId:    testsdk.Ptr(uint64(81)),
					TransferId:    testsdk.Ptr(uint64(1)),
				},
				outputErr: errors.New("some error"),
			},
			appConfig: map[string]any{
				"SYMMETRIC_KEY": normalKey,
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctrl := gomock.NewController(t)
			defer ctrl.Finish()

			c := context.Background()
			ctx, cancel := context.WithCancel(c)
			e2e.InitAppE2E(t, ctx, cancel, nil, nil, tt.appConfig)

			transferDBMock := databasemocks.NewMockTransferer(ctrl)
			transferStatusDBMock := databasemocks.NewMockTransferStatuser(ctrl)
			transferManagerDBMock := databasemocks.NewMockTransferManager(ctrl)
			accountInfoDBMock := databasemocks.NewMockAccountInformer(ctrl)

			billingCliMock := grpcmock.NewMockBillingClient(ctrl)
			multiaccountingCliMock := grpcmock.NewMockMultiaccountingClient(ctrl)

			transferDBMock.EXPECT().GetByID(
				gomock.Any(),
				tt.getTransferByID.input,
			).Return(
				tt.getTransferByID.output,
				tt.getTransferByID.outputErr,
			).Times(1)

			if tt.getTransferStatus.isCalled {
				transferStatusDBMock.EXPECT().GetByID(
					gomock.Any(),
					tt.getTransferStatus.input,
				).Return(
					tt.getTransferStatus.output,
					tt.getTransferStatus.outputErr,
				).Times(1)
			}

			if tt.getAccountByID.isCalled {
				accountInfoDBMock.EXPECT().GetById(
					gomock.Any(),
					tt.getAccountByID.input,
				).Return(
					tt.getAccountByID.output,
					tt.getAccountByID.outputErr,
				).Times(1)
			}

			if tt.checkoutTransferBalance.isCalled {
				billingCliMock.EXPECT().CheckOutTransferBalanceV1(
					gomock.Any(),
					tt.checkoutTransferBalance.input,
				).Return(
					tt.checkoutTransferBalance.output,
					tt.checkoutTransferBalance.outputErr,
				).Times(1)
			}

			if tt.declineTransfer.isCalled {
				multiaccountingCliMock.EXPECT().DeclineTransfer(
					gomock.Any(),
					tt.declineTransfer.input,
				).Return(
					tt.declineTransfer.output,
					tt.declineTransfer.outputErr,
				).Times(1)
			}

			if tt.getTransferStatusByCode.isCalled {
				transferStatusDBMock.EXPECT().GetByCode(
					gomock.Any(),
					model.StatusFailed,
				).Return(
					tt.getTransferStatusByCode.output,
					tt.getTransferStatusByCode.outputErr,
				).Times(1)
			}

			if tt.updateStatus.isCalled {
				transferManagerDBMock.EXPECT().UpdateStatus(
					gomock.Any(),
					tt.updateStatus.inputTransferID,
					tt.updateStatus.inputStatusID,
					tt.updateStatus.inputTime,
				).Return(
					tt.updateStatus.outputErr,
				).Times(1)
			}

			if tt.acceptTransfer.isCalled {
				multiaccountingCliMock.EXPECT().AcceptTransfer(
					gomock.Any(),
					tt.acceptTransfer.input,
				).Return(
					tt.acceptTransfer.output,
					tt.acceptTransfer.outputErr,
				).Times(1)
			}

			if tt.getTransferStatusByID2.isCalled {
				transferStatusDBMock.EXPECT().GetByID(
					gomock.Any(),
					tt.getTransferStatusByID2.input,
				).Return(
					tt.getTransferStatusByID2.output,
					tt.getTransferStatusByID2.outputErr,
				).Times(1)
			}

			if tt.updateStatus2.isCalled {
				transferManagerDBMock.EXPECT().UpdateStatus(
					gomock.Any(),
					tt.updateStatus2.inputTransferID,
					tt.updateStatus2.inputStatusID,
					tt.updateStatus2.inputTime,
				).Return(
					tt.updateStatus2.outputErr,
				).Times(1)
			}

			if tt.billOutTransfer.isCalled {
				billingCliMock.EXPECT().BillOutTransferV1(
					gomock.Any(),
					tt.billOutTransfer.input,
				).Return(
					nil,
					tt.billOutTransfer.outputErr,
				).Times(1)
			}

			s := TransferService{
				transferStatusRepo:    transferStatusDBMock,
				transferManageRepo:    transferManagerDBMock,
				transferRepo:          transferDBMock,
				accountInfoRepo:       accountInfoDBMock,
				billingClient:         billingCliMock,
				multiaccountingClient: multiaccountingCliMock,
				getCurrentTime: func() time.Time {
					return testNow
				},
			}

			res, err := s.AcceptTransfer(context.Background(), tt.req)

			// TODO: because of goroutine in the calling func, we put time sleep, to wait for goroutine jobs
			// need to investigate better way to test function that creates goroutine inside of them, like that
			time.Sleep(500 * time.Millisecond)
			if tt.wantErr != nil {
				require.Error(t, err)
				require.ErrorAs(t, err, &tt.wantErr)
			} else {
				require.NoError(t, err)
			}

			require.Equal(t, tt.want, res)
		})
	}
}

func TestTransferServiceRedoTransfer(t *testing.T) {
	defaultCfg, err := structpb.NewStruct(map[string]any{
		"my-name-is": "skrillex",
	})
	if err != nil {
		t.Fatal(err)
	}

	testNow := time.Date(2009, time.November, 10, 23, 0, 0, 0, time.UTC)

	type getTransferByIDOp struct {
		input     uint64
		output    *model.Transfer
		outputErr error
	}

	type getTransferStatusByIDOp struct {
		isCalled  bool
		input     uint64
		output    *model.TransferStatus
		outputErr error
	}

	type getByNumberOp struct {
		isCalled  bool
		input     string
		output    *model.Account
		outputErr error
	}

	type redoTransferOp struct {
		isCalled  bool
		input     *grpc.RedoTransferRequest
		output    *grpc.RedoTransferResponse
		outputErr error
	}

	type updateStatusOp struct {
		isCalled                       bool
		inputTransferID, inputStatusID uint64
		inputTime                      *time.Time
		outputErr                      error
	}

	test := []struct {
		name                   string
		req                    schema.RedoTransferRequest
		want                   *schema.TransferResponse
		wantErr                error
		getTransferByID        getTransferByIDOp
		getTransferStatusByID  getTransferStatusByIDOp
		getByNumber            getByNumberOp
		redoTransfer           redoTransferOp
		getTransferStatusByID2 getTransferStatusByIDOp
		updateStatus           updateStatusOp
		appConfig              map[string]any
	}{
		{
			name: "error_getting_transfer_by_id",
			req: schema.RedoTransferRequest{
				TransferID:     1,
				TransitAccount: "some transit account",
				MerchantID:     81,
				ProjectID:      10,
			},
			want:    nil,
			wantErr: errors.New("some error"),
			getTransferByID: getTransferByIDOp{
				input:     1,
				output:    nil,
				outputErr: errors.New("some error"),
			},
		},
		{
			name: "error_getting_status_by_id",
			req: schema.RedoTransferRequest{
				TransferID:     1,
				TransitAccount: "some transit account",
				MerchantID:     81,
				ProjectID:      10,
			},
			want:    nil,
			wantErr: errors.New("some error"),
			getTransferByID: getTransferByIDOp{
				input: 1,
				output: &model.Transfer{
					ID:                      1,
					ProjectID:               10,
					MerchantID:              81,
					StatusID:                1,
					Amount:                  100,
					MerchantBIN:             "some bin",
					MerchantAccount:         "some recipient account",
					MerchantBeneficiaryCode: "some beneficiary code",
					Description:             "some description",
					PaymentPurposeCode:      "some payment purpose code",
					TransferTypeID:          1,
					AcquirerID:              25,
					AccountID:               23,
					EntityTypeID:            40,
					CountryCodeID:           1,
				},
				outputErr: nil,
			},
			getTransferStatusByID: getTransferStatusByIDOp{
				isCalled:  true,
				input:     1,
				output:    nil,
				outputErr: errors.New("some error"),
			},
		},
		{
			name: "transfer_status_is_not_hold",
			req: schema.RedoTransferRequest{
				TransferID:     1,
				TransitAccount: "some transit account",
				MerchantID:     81,
				ProjectID:      10,
			},
			want:    nil,
			wantErr: goerr.ErrStatusValidation,
			getTransferByID: getTransferByIDOp{
				input: 1,
				output: &model.Transfer{
					ID:                      1,
					ProjectID:               10,
					MerchantID:              81,
					StatusID:                1,
					Amount:                  100,
					MerchantBIN:             "some bin",
					MerchantAccount:         "some recipient account",
					MerchantBeneficiaryCode: "some beneficiary code",
					Description:             "some description",
					PaymentPurposeCode:      "some payment purpose code",
					TransferTypeID:          1,
					AcquirerID:              25,
					AccountID:               23,
					EntityTypeID:            40,
					CountryCodeID:           1,
				},
				outputErr: nil,
			},
			getTransferStatusByID: getTransferStatusByIDOp{
				isCalled: true,
				input:    1,
				output: &model.TransferStatus{
					ID:   1,
					Code: model.StatusInProgress,
					Name: "in_progress",
				},
				outputErr: nil,
			},
		},
		{
			name: "error_get_account_by_number",
			req: schema.RedoTransferRequest{
				TransferID:     1,
				TransitAccount: "some transit account",
				MerchantID:     81,
				ProjectID:      10,
			},
			want:    nil,
			wantErr: errors.New("some error"),
			getTransferByID: getTransferByIDOp{
				input: 1,
				output: &model.Transfer{
					ID:                      1,
					ProjectID:               10,
					MerchantID:              81,
					StatusID:                1,
					Amount:                  100,
					MerchantBIN:             "some bin",
					MerchantAccount:         "some recipient account",
					MerchantBeneficiaryCode: "some beneficiary code",
					Description:             "some description",
					PaymentPurposeCode:      "some payment purpose code",
					TransferTypeID:          1,
					AcquirerID:              25,
					AccountID:               23,
					EntityTypeID:            40,
					CountryCodeID:           1,
				},
				outputErr: nil,
			},
			getTransferStatusByID: getTransferStatusByIDOp{
				isCalled: true,
				input:    1,
				output: &model.TransferStatus{
					ID:   1,
					Code: model.StatusHold,
					Name: "hold",
				},
				outputErr: nil,
			},
			getByNumber: getByNumberOp{
				isCalled:  true,
				input:     "some transit account",
				output:    nil,
				outputErr: errors.New("some error"),
			},
		},
		{
			name: "unmarshall_error",
			req: schema.RedoTransferRequest{
				TransferID:     1,
				TransitAccount: "some transit account",
				MerchantID:     81,
				ProjectID:      10,
			},
			want:    nil,
			wantErr: aes.KeySizeError(3),
			getTransferByID: getTransferByIDOp{
				input: 1,
				output: &model.Transfer{
					ID:                      1,
					ProjectID:               10,
					MerchantID:              81,
					StatusID:                1,
					Amount:                  100,
					MerchantBIN:             "some bin",
					MerchantAccount:         "some recipient account",
					MerchantBeneficiaryCode: "some beneficiary code",
					Description:             "some description",
					PaymentPurposeCode:      "some payment purpose code",
					TransferTypeID:          1,
					AcquirerID:              25,
					AccountID:               23,
					EntityTypeID:            40,
					CountryCodeID:           1,
				},
				outputErr: nil,
			},
			getTransferStatusByID: getTransferStatusByIDOp{
				isCalled: true,
				input:    1,
				output: &model.TransferStatus{
					ID:   1,
					Code: model.StatusHold,
					Name: "hold",
				},
				outputErr: nil,
			},
			getByNumber: getByNumberOp{
				isCalled: true,
				input:    "some transit account",
				output: &model.Account{
					ID:              23,
					Number:          "some transit account",
					CurrencyCode:    "kzt",
					BankCode:        "bcc",
					BankID:          25,
					AccountTypeID:   3,
					EncryptedConfig: defaultEncryptedConfig,
				},
				outputErr: nil,
			},
			appConfig: map[string]any{
				"SYMMETRIC_KEY": badKey,
			},
		},
		{
			name: "redo_transfer_error",
			req: schema.RedoTransferRequest{
				TransferID:     1,
				TransitAccount: "some transit account",
				MerchantID:     81,
				ProjectID:      10,
			},
			want:    nil,
			wantErr: errors.New("some error"),
			getTransferByID: getTransferByIDOp{
				input: 1,
				output: &model.Transfer{
					ID:                      1,
					ProjectID:               10,
					MerchantID:              81,
					StatusID:                1,
					Amount:                  100,
					MerchantBIN:             "some bin",
					MerchantAccount:         "some recipient account",
					MerchantBeneficiaryCode: "some beneficiary code",
					Description:             "some description",
					PaymentPurposeCode:      "some payment purpose code",
					TransferTypeID:          1,
					AcquirerID:              25,
					AccountID:               23,
					EntityTypeID:            40,
					CountryCodeID:           1,
				},
				outputErr: nil,
			},
			getTransferStatusByID: getTransferStatusByIDOp{
				isCalled: true,
				input:    1,
				output: &model.TransferStatus{
					ID:   1,
					Code: model.StatusHold,
					Name: "hold",
				},
				outputErr: nil,
			},
			getByNumber: getByNumberOp{
				isCalled: true,
				input:    "some transit account",
				output: &model.Account{
					ID:              23,
					Number:          "some transit account",
					CurrencyCode:    "kzt",
					BankCode:        "bcc",
					BankID:          25,
					AccountTypeID:   3,
					EncryptedConfig: defaultEncryptedConfig,
				},
				outputErr: nil,
			},
			redoTransfer: redoTransferOp{
				isCalled: true,
				input: &grpc.RedoTransferRequest{
					Account: &grpc.AccountTerminal{
						BankCode: testsdk.Ptr("bcc"),
						Config:   defaultCfg,
					},
					TransferId: testsdk.Ptr(uint64(1)),
				},
				output:    nil,
				outputErr: errors.New("some error"),
			},
			appConfig: map[string]any{
				"SYMMETRIC_KEY": normalKey,
			},
		},
		{
			name: "error_getting_transfer_status_2",
			req: schema.RedoTransferRequest{
				TransferID:     1,
				TransitAccount: "some transit account",
				MerchantID:     81,
				ProjectID:      10,
			},
			want:    nil,
			wantErr: errors.New("some error"),
			getTransferByID: getTransferByIDOp{
				input: 1,
				output: &model.Transfer{
					ID:                      1,
					ProjectID:               10,
					MerchantID:              81,
					StatusID:                1,
					Amount:                  100,
					MerchantBIN:             "some bin",
					MerchantAccount:         "some recipient account",
					MerchantBeneficiaryCode: "some beneficiary code",
					Description:             "some description",
					PaymentPurposeCode:      "some payment purpose code",
					TransferTypeID:          1,
					AcquirerID:              25,
					AccountID:               23,
					EntityTypeID:            40,
					CountryCodeID:           1,
				},
				outputErr: nil,
			},
			getTransferStatusByID: getTransferStatusByIDOp{
				isCalled: true,
				input:    1,
				output: &model.TransferStatus{
					ID:   1,
					Code: model.StatusHold,
					Name: "hold",
				},
				outputErr: nil,
			},
			getByNumber: getByNumberOp{
				isCalled: true,
				input:    "some transit account",
				output: &model.Account{
					ID:              23,
					Number:          "some transit account",
					CurrencyCode:    "kzt",
					BankCode:        "bcc",
					BankID:          25,
					AccountTypeID:   3,
					EncryptedConfig: defaultEncryptedConfig,
				},
				outputErr: nil,
			},
			redoTransfer: redoTransferOp{
				isCalled: true,
				input: &grpc.RedoTransferRequest{
					Account: &grpc.AccountTerminal{
						BankCode: testsdk.Ptr("bcc"),
						Config:   defaultCfg,
					},
					TransferId: testsdk.Ptr(uint64(1)),
				},
				output: &grpc.RedoTransferResponse{
					TransferId:       testsdk.Ptr(uint64(1)),
					TransferStatusId: testsdk.Ptr(uint64(6)),
				},
				outputErr: nil,
			},
			getTransferStatusByID2: getTransferStatusByIDOp{
				isCalled:  true,
				input:     6,
				output:    nil,
				outputErr: errors.New("some error"),
			},
			appConfig: map[string]any{
				"SYMMETRIC_KEY": normalKey,
			},
		},
		{
			name: "error_when_updating_status",
			req: schema.RedoTransferRequest{
				TransferID:     1,
				TransitAccount: "some transit account",
				MerchantID:     81,
				ProjectID:      10,
			},
			want:    nil,
			wantErr: errors.New("some error"),
			getTransferByID: getTransferByIDOp{
				input: 1,
				output: &model.Transfer{
					ID:                      1,
					ProjectID:               10,
					MerchantID:              81,
					StatusID:                1,
					Amount:                  100,
					MerchantBIN:             "some bin",
					MerchantAccount:         "some recipient account",
					MerchantBeneficiaryCode: "some beneficiary code",
					Description:             "some description",
					PaymentPurposeCode:      "some payment purpose code",
					TransferTypeID:          1,
					AcquirerID:              25,
					AccountID:               23,
					EntityTypeID:            40,
					CountryCodeID:           1,
				},
				outputErr: nil,
			},
			getTransferStatusByID: getTransferStatusByIDOp{
				isCalled: true,
				input:    1,
				output: &model.TransferStatus{
					ID:   1,
					Code: model.StatusHold,
					Name: "hold",
				},
				outputErr: nil,
			},
			getByNumber: getByNumberOp{
				isCalled: true,
				input:    "some transit account",
				output: &model.Account{
					ID:              23,
					Number:          "some transit account",
					CurrencyCode:    "kzt",
					BankCode:        "bcc",
					BankID:          25,
					AccountTypeID:   3,
					EncryptedConfig: defaultEncryptedConfig,
				},
				outputErr: nil,
			},
			redoTransfer: redoTransferOp{
				isCalled: true,
				input: &grpc.RedoTransferRequest{
					Account: &grpc.AccountTerminal{
						BankCode: testsdk.Ptr("bcc"),
						Config:   defaultCfg,
					},
					TransferId: testsdk.Ptr(uint64(1)),
				},
				output: &grpc.RedoTransferResponse{
					TransferId:       testsdk.Ptr(uint64(1)),
					TransferStatusId: testsdk.Ptr(uint64(6)),
				},
				outputErr: nil,
			},
			getTransferStatusByID2: getTransferStatusByIDOp{
				isCalled: true,
				input:    6,
				output: &model.TransferStatus{
					ID:   6,
					Code: model.StatusSuccess,
					Name: "success",
				},
				outputErr: nil,
			},
			updateStatus: updateStatusOp{
				isCalled:        true,
				inputTransferID: 1,
				inputTime:       &testNow,
				inputStatusID:   6,
				outputErr:       errors.New("some error"),
			},
			appConfig: map[string]any{
				"SYMMETRIC_KEY": normalKey,
			},
		},
		{
			name: "success",
			req: schema.RedoTransferRequest{
				TransferID:     1,
				TransitAccount: "some transit account",
				MerchantID:     81,
				ProjectID:      10,
			},
			want: &schema.TransferResponse{
				TransferID: 1,
				StatusID:   6,
			},
			wantErr: nil,
			getTransferByID: getTransferByIDOp{
				input: 1,
				output: &model.Transfer{
					ID:                      1,
					ProjectID:               10,
					MerchantID:              81,
					StatusID:                1,
					Amount:                  100,
					MerchantBIN:             "some bin",
					MerchantAccount:         "some recipient account",
					MerchantBeneficiaryCode: "some beneficiary code",
					Description:             "some description",
					PaymentPurposeCode:      "some payment purpose code",
					TransferTypeID:          1,
					AcquirerID:              25,
					AccountID:               23,
					EntityTypeID:            40,
					CountryCodeID:           1,
				},
				outputErr: nil,
			},
			getTransferStatusByID: getTransferStatusByIDOp{
				isCalled: true,
				input:    1,
				output: &model.TransferStatus{
					ID:   1,
					Code: model.StatusHold,
					Name: "hold",
				},
				outputErr: nil,
			},
			getByNumber: getByNumberOp{
				isCalled: true,
				input:    "some transit account",
				output: &model.Account{
					ID:              23,
					Number:          "some transit account",
					CurrencyCode:    "kzt",
					BankCode:        "bcc",
					BankID:          25,
					AccountTypeID:   3,
					EncryptedConfig: defaultEncryptedConfig,
				},
				outputErr: nil,
			},
			redoTransfer: redoTransferOp{
				isCalled: true,
				input: &grpc.RedoTransferRequest{
					Account: &grpc.AccountTerminal{
						BankCode: testsdk.Ptr("bcc"),
						Config:   defaultCfg,
					},
					TransferId: testsdk.Ptr(uint64(1)),
				},
				output: &grpc.RedoTransferResponse{
					TransferId:       testsdk.Ptr(uint64(1)),
					TransferStatusId: testsdk.Ptr(uint64(6)),
				},
				outputErr: nil,
			},
			getTransferStatusByID2: getTransferStatusByIDOp{
				isCalled: true,
				input:    6,
				output: &model.TransferStatus{
					ID:   6,
					Code: model.StatusSuccess,
					Name: "success",
				},
				outputErr: nil,
			},
			updateStatus: updateStatusOp{
				isCalled:        true,
				inputTransferID: 1,
				inputTime:       &testNow,
				inputStatusID:   6,
				outputErr:       nil,
			},
			appConfig: map[string]any{
				"SYMMETRIC_KEY": normalKey,
			},
		},
	}

	for _, tt := range test {
		t.Run(tt.name, func(t *testing.T) {
			ctrl := gomock.NewController(t)
			defer ctrl.Finish()

			c := context.Background()
			ctx, cancel := context.WithCancel(c)
			e2e.InitAppE2E(t, ctx, cancel, nil, nil, tt.appConfig)

			transferStatusDBMock := databasemocks.NewMockTransferStatuser(ctrl)
			transferDBMock := databasemocks.NewMockTransferer(ctrl)
			accountInfoDBMock := databasemocks.NewMockAccountInformer(ctrl)
			transferManagerDBMock := databasemocks.NewMockTransferManager(ctrl)

			multiaccountingCliMock := grpcmock.NewMockMultiaccountingClient(ctrl)

			transferDBMock.EXPECT().GetByID(
				gomock.Any(),
				tt.getTransferByID.input,
			).Return(
				tt.getTransferByID.output,
				tt.getTransferByID.outputErr,
			).Times(1)

			if tt.getTransferStatusByID.isCalled {
				transferStatusDBMock.EXPECT().GetByID(
					gomock.Any(),
					tt.getTransferStatusByID.input,
				).Return(
					tt.getTransferStatusByID.output,
					tt.getTransferStatusByID.outputErr,
				).Times(1)
			}

			if tt.getByNumber.isCalled {
				accountInfoDBMock.EXPECT().GetByNumber(
					gomock.Any(),
					tt.getByNumber.input,
				).Return(
					tt.getByNumber.output,
					tt.getByNumber.outputErr,
				).Times(1)
			}

			if tt.redoTransfer.isCalled {
				multiaccountingCliMock.EXPECT().RedoTransfer(
					gomock.Any(),
					tt.redoTransfer.input,
				).Return(
					tt.redoTransfer.output,
					tt.redoTransfer.outputErr,
				).Times(1)
			}

			if tt.getTransferStatusByID2.isCalled {
				transferStatusDBMock.EXPECT().GetByID(
					gomock.Any(),
					tt.getTransferStatusByID2.input,
				).Return(
					tt.getTransferStatusByID2.output,
					tt.getTransferStatusByID2.outputErr,
				).Times(1)
			}

			if tt.updateStatus.isCalled {
				transferManagerDBMock.EXPECT().UpdateStatus(
					gomock.Any(),
					tt.updateStatus.inputTransferID,
					tt.updateStatus.inputStatusID,
					tt.updateStatus.inputTime,
				).Return(
					tt.updateStatus.outputErr,
				).Times(1)
			}

			s := TransferService{
				transferStatusRepo:    transferStatusDBMock,
				transferManageRepo:    transferManagerDBMock,
				transferRepo:          transferDBMock,
				accountInfoRepo:       accountInfoDBMock,
				multiaccountingClient: multiaccountingCliMock,
				getCurrentTime: func() time.Time {
					return testNow
				},
			}

			res, err := s.RedoTransfer(context.Background(), tt.req)
			if tt.wantErr != nil {
				require.Error(t, err)
				require.ErrorAs(t, err, &tt.wantErr)
			} else {
				require.NoError(t, err)
			}

			require.Equal(t, tt.want, res)
		})
	}
}

func TestTransferServiceCancelTransfer(t *testing.T) {
	defaultCfg, err := structpb.NewStruct(map[string]any{
		"my-name-is": "skrillex",
	})
	if err != nil {
		t.Fatal(err)
	}

	testNow := time.Date(2009, time.November, 10, 23, 0, 0, 0, time.UTC)

	type getTransferByIDOp struct {
		input     uint64
		output    *model.Transfer
		outputErr error
	}

	type getTransferStatusByIDOp struct {
		isCalled  bool
		input     uint64
		output    *model.TransferStatus
		outputErr error
	}

	type getAccountByIDOp struct {
		isCalled  bool
		input     uint64
		output    *model.Account
		outputErr error
	}

	type declineTransferOp struct {
		isCalled  bool
		input     *grpc.DeclineTransferRequest
		output    *grpc.DeclineTransferResponse
		outputErr error
	}

	type updateStatusOp struct {
		isCalled                       bool
		inputTransferID, inputStatusID uint64
		inputTime                      *time.Time
		outputErr                      error
	}

	tests := []struct {
		name                   string
		req                    schema.CancelTransferRequest
		want                   *schema.CancelTransferResponse
		wantErr                error
		getTransferByID        getTransferByIDOp
		getTransferStatusByID  getTransferStatusByIDOp
		getAccountByID         getAccountByIDOp
		declineTransfer        declineTransferOp
		getTransferStatusByID2 getTransferStatusByIDOp
		updateStatus           updateStatusOp
		appConfig              map[string]any
	}{
		{
			name: "error_getting_transfer_by_id",
			req: schema.CancelTransferRequest{
				TransfersList: []uint64{1},
			},
			want: &schema.CancelTransferResponse{
				Responses: []*schema.TransferResponse{},
			},
			wantErr: nil,
			getTransferByID: getTransferByIDOp{
				input:     1,
				output:    nil,
				outputErr: errors.New("some error"),
			},
		},
		{
			name: "error_getting_transfer_status_by_id",
			req: schema.CancelTransferRequest{
				TransfersList: []uint64{1},
			},
			want: &schema.CancelTransferResponse{
				Responses: []*schema.TransferResponse{},
			},
			wantErr: nil,
			getTransferByID: getTransferByIDOp{
				input: 1,
				output: &model.Transfer{
					ID:                      1,
					ProjectID:               10,
					MerchantID:              21,
					StatusID:                1,
					Amount:                  100000,
					MerchantBIN:             "some bin",
					MerchantAccount:         "some recipient account",
					MerchantBeneficiaryCode: "some beneficiary code",
					Description:             "some description",
					PaymentPurposeCode:      "some payment purpose code",
					TransferTypeID:          1,
					AcquirerID:              25,
					AccountID:               23,
					EntityTypeID:            40,
					CountryCodeID:           1,
				},
				outputErr: nil,
			},
			getTransferStatusByID: getTransferStatusByIDOp{
				isCalled:  true,
				input:     1,
				output:    nil,
				outputErr: errors.New("some error"),
			},
		},
		{
			name: "status_is_not_authorized",
			req: schema.CancelTransferRequest{
				TransfersList: []uint64{1},
			},
			want: &schema.CancelTransferResponse{
				Responses: []*schema.TransferResponse{},
			},
			wantErr: nil,
			getTransferByID: getTransferByIDOp{
				input: 1,
				output: &model.Transfer{
					ID:                      1,
					ProjectID:               10,
					MerchantID:              21,
					StatusID:                1,
					Amount:                  100000,
					MerchantBIN:             "some bin",
					MerchantAccount:         "some recipient account",
					MerchantBeneficiaryCode: "some beneficiary code",
					Description:             "some description",
					PaymentPurposeCode:      "some payment purpose code",
					TransferTypeID:          1,
					AcquirerID:              25,
					AccountID:               23,
					EntityTypeID:            40,
					CountryCodeID:           1,
				},
				outputErr: nil,
			},
			getTransferStatusByID: getTransferStatusByIDOp{
				isCalled: true,
				input:    1,
				output: &model.TransferStatus{
					ID:   1,
					Name: "hold",
					Code: model.StatusHold,
				},
				outputErr: nil,
			},
		},
		{
			name: "get_account_by_id_error",
			req: schema.CancelTransferRequest{
				TransfersList: []uint64{1},
			},
			want: &schema.CancelTransferResponse{
				Responses: []*schema.TransferResponse{},
			},
			wantErr: nil,
			getTransferByID: getTransferByIDOp{
				input: 1,
				output: &model.Transfer{
					ID:                      1,
					ProjectID:               10,
					MerchantID:              21,
					StatusID:                1,
					Amount:                  100000,
					MerchantBIN:             "some bin",
					MerchantAccount:         "some recipient account",
					MerchantBeneficiaryCode: "some beneficiary code",
					Description:             "some description",
					PaymentPurposeCode:      "some payment purpose code",
					TransferTypeID:          1,
					AcquirerID:              25,
					AccountID:               23,
					EntityTypeID:            40,
					CountryCodeID:           1,
				},
				outputErr: nil,
			},
			getTransferStatusByID: getTransferStatusByIDOp{
				isCalled: true,
				input:    1,
				output: &model.TransferStatus{
					ID:   1,
					Name: "authorize",
					Code: model.StatusAuthorized,
				},
				outputErr: nil,
			},
			getAccountByID: getAccountByIDOp{
				isCalled:  true,
				input:     23,
				output:    nil,
				outputErr: errors.New("some error"),
			},
		},
		{
			name: "unmarshall_error",
			req: schema.CancelTransferRequest{
				TransfersList: []uint64{1},
			},
			want: &schema.CancelTransferResponse{
				Responses: []*schema.TransferResponse{},
			},
			wantErr: nil,
			getTransferByID: getTransferByIDOp{
				input: 1,
				output: &model.Transfer{
					ID:                      1,
					ProjectID:               10,
					MerchantID:              21,
					StatusID:                1,
					Amount:                  100000,
					MerchantBIN:             "some bin",
					MerchantAccount:         "some recipient account",
					MerchantBeneficiaryCode: "some beneficiary code",
					Description:             "some description",
					PaymentPurposeCode:      "some payment purpose code",
					TransferTypeID:          1,
					AcquirerID:              25,
					AccountID:               23,
					EntityTypeID:            40,
					CountryCodeID:           1,
				},
				outputErr: nil,
			},
			getTransferStatusByID: getTransferStatusByIDOp{
				isCalled: true,
				input:    1,
				output: &model.TransferStatus{
					ID:   1,
					Name: "authorized",
					Code: model.StatusAuthorized,
				},
				outputErr: nil,
			},
			getAccountByID: getAccountByIDOp{
				isCalled: true,
				input:    23,
				output: &model.Account{
					ID:              23,
					Number:          "some number",
					CurrencyCode:    "kzt",
					BankCode:        "bcc",
					BankID:          25,
					AccountTypeID:   3,
					EncryptedConfig: defaultEncryptedConfig,
				},
				outputErr: nil,
			},
			appConfig: map[string]any{
				"SYMMETRIC_KEY": badKey,
			},
		},
		{
			name: "decline_transfer_error",
			req: schema.CancelTransferRequest{
				TransfersList: []uint64{1},
			},
			want: &schema.CancelTransferResponse{
				Responses: []*schema.TransferResponse{},
			},
			wantErr: nil,
			getTransferByID: getTransferByIDOp{
				input: 1,
				output: &model.Transfer{
					ID:                      1,
					ProjectID:               10,
					MerchantID:              21,
					StatusID:                1,
					Amount:                  100000,
					MerchantBIN:             "some bin",
					MerchantAccount:         "some recipient account",
					MerchantBeneficiaryCode: "some beneficiary code",
					Description:             "some description",
					PaymentPurposeCode:      "some payment purpose code",
					TransferTypeID:          1,
					AcquirerID:              25,
					AccountID:               23,
					EntityTypeID:            40,
					CountryCodeID:           1,
				},
				outputErr: nil,
			},
			getTransferStatusByID: getTransferStatusByIDOp{
				isCalled: true,
				input:    1,
				output: &model.TransferStatus{
					ID:   1,
					Name: "authorized",
					Code: model.StatusAuthorized,
				},
				outputErr: nil,
			},
			getAccountByID: getAccountByIDOp{
				isCalled: true,
				input:    23,
				output: &model.Account{
					ID:              23,
					Number:          "some number",
					CurrencyCode:    "kzt",
					BankCode:        "bcc",
					BankID:          25,
					AccountTypeID:   3,
					EncryptedConfig: defaultEncryptedConfig,
				},
				outputErr: nil,
			},
			declineTransfer: declineTransferOp{
				isCalled: true,
				input: &grpc.DeclineTransferRequest{
					Account: &grpc.AccountTerminal{
						BankCode: testsdk.Ptr("bcc"),
						Config:   defaultCfg,
					},
					TransferId:          testsdk.Ptr(uint64(1)),
					ExternalReferenceId: testsdk.Ptr(strconv.FormatUint(1, 10)),
				},
				output:    nil,
				outputErr: errors.New("some error"),
			},
			appConfig: map[string]any{
				"SYMMETRIC_KEY": normalKey,
			},
		},
		{
			name: "get_transfer_status_by_id_error",
			req: schema.CancelTransferRequest{
				TransfersList: []uint64{1},
			},
			want: &schema.CancelTransferResponse{
				Responses: []*schema.TransferResponse{},
			},
			wantErr: nil,
			getTransferByID: getTransferByIDOp{
				input: 1,
				output: &model.Transfer{
					ID:                      1,
					ProjectID:               10,
					MerchantID:              21,
					StatusID:                1,
					Amount:                  100000,
					MerchantBIN:             "some bin",
					MerchantAccount:         "some recipient account",
					MerchantBeneficiaryCode: "some beneficiary code",
					Description:             "some description",
					PaymentPurposeCode:      "some payment purpose code",
					TransferTypeID:          1,
					AcquirerID:              25,
					AccountID:               23,
					EntityTypeID:            40,
					CountryCodeID:           1,
				},
				outputErr: nil,
			},
			getTransferStatusByID: getTransferStatusByIDOp{
				isCalled: true,
				input:    1,
				output: &model.TransferStatus{
					ID:   1,
					Name: "authorized",
					Code: model.StatusAuthorized,
				},
				outputErr: nil,
			},
			getAccountByID: getAccountByIDOp{
				isCalled: true,
				input:    23,
				output: &model.Account{
					ID:              23,
					Number:          "some number",
					CurrencyCode:    "kzt",
					BankCode:        "bcc",
					BankID:          25,
					AccountTypeID:   3,
					EncryptedConfig: defaultEncryptedConfig,
				},
				outputErr: nil,
			},
			declineTransfer: declineTransferOp{
				isCalled: true,
				input: &grpc.DeclineTransferRequest{
					Account: &grpc.AccountTerminal{
						BankCode: testsdk.Ptr("bcc"),
						Config:   defaultCfg,
					},
					TransferId:          testsdk.Ptr(uint64(1)),
					ExternalReferenceId: testsdk.Ptr(strconv.FormatUint(1, 10)),
				},
				output: &grpc.DeclineTransferResponse{
					TransferId:       testsdk.Ptr(uint64(1)),
					TransferStatusId: testsdk.Ptr(uint64(3)),
				},
				outputErr: nil,
			},
			getTransferStatusByID2: getTransferStatusByIDOp{
				isCalled:  true,
				input:     3,
				output:    nil,
				outputErr: errors.New("some error"),
			},
			appConfig: map[string]any{
				"SYMMETRIC_KEY": normalKey,
			},
		},
		{
			name: "update_status_error",
			req: schema.CancelTransferRequest{
				TransfersList: []uint64{1},
			},
			want: &schema.CancelTransferResponse{
				Responses: []*schema.TransferResponse{},
			},
			wantErr: nil,
			getTransferByID: getTransferByIDOp{
				input: 1,
				output: &model.Transfer{
					ID:                      1,
					ProjectID:               10,
					MerchantID:              21,
					StatusID:                1,
					Amount:                  100000,
					MerchantBIN:             "some bin",
					MerchantAccount:         "some recipient account",
					MerchantBeneficiaryCode: "some beneficiary code",
					Description:             "some description",
					PaymentPurposeCode:      "some payment purpose code",
					TransferTypeID:          1,
					AcquirerID:              25,
					AccountID:               23,
					EntityTypeID:            40,
					CountryCodeID:           1,
				},
				outputErr: nil,
			},
			getTransferStatusByID: getTransferStatusByIDOp{
				isCalled: true,
				input:    1,
				output: &model.TransferStatus{
					ID:   1,
					Name: "authorized",
					Code: model.StatusAuthorized,
				},
				outputErr: nil,
			},
			getAccountByID: getAccountByIDOp{
				isCalled: true,
				input:    23,
				output: &model.Account{
					ID:              23,
					Number:          "some number",
					CurrencyCode:    "kzt",
					BankCode:        "bcc",
					BankID:          25,
					AccountTypeID:   3,
					EncryptedConfig: defaultEncryptedConfig,
				},
				outputErr: nil,
			},
			declineTransfer: declineTransferOp{
				isCalled: true,
				input: &grpc.DeclineTransferRequest{
					Account: &grpc.AccountTerminal{
						BankCode: testsdk.Ptr("bcc"),
						Config:   defaultCfg,
					},
					TransferId:          testsdk.Ptr(uint64(1)),
					ExternalReferenceId: testsdk.Ptr(strconv.FormatUint(1, 10)),
				},
				output: &grpc.DeclineTransferResponse{
					TransferId:       testsdk.Ptr(uint64(1)),
					TransferStatusId: testsdk.Ptr(uint64(3)),
				},
				outputErr: nil,
			},
			getTransferStatusByID2: getTransferStatusByIDOp{
				isCalled: true,
				input:    3,
				output: &model.TransferStatus{
					ID:   3,
					Name: "success",
					Code: model.StatusSuccess,
				},
				outputErr: nil,
			},
			updateStatus: updateStatusOp{
				isCalled:        true,
				inputTime:       &testNow,
				inputTransferID: 1,
				inputStatusID:   3,
				outputErr:       errors.New("some error"),
			},
			appConfig: map[string]any{
				"SYMMETRIC_KEY": normalKey,
			},
		},
		{
			name: "success",
			req: schema.CancelTransferRequest{
				TransfersList: []uint64{1},
			},
			want: &schema.CancelTransferResponse{
				Responses: []*schema.TransferResponse{
					{
						TransferID: 1,
						StatusID:   3,
					},
				},
			},
			wantErr: nil,
			getTransferByID: getTransferByIDOp{
				input: 1,
				output: &model.Transfer{
					ID:                      1,
					ProjectID:               10,
					MerchantID:              21,
					StatusID:                1,
					Amount:                  100000,
					MerchantBIN:             "some bin",
					MerchantAccount:         "some recipient account",
					MerchantBeneficiaryCode: "some beneficiary code",
					Description:             "some description",
					PaymentPurposeCode:      "some payment purpose code",
					TransferTypeID:          1,
					AcquirerID:              25,
					AccountID:               23,
					EntityTypeID:            40,
					CountryCodeID:           1,
				},
				outputErr: nil,
			},
			getTransferStatusByID: getTransferStatusByIDOp{
				isCalled: true,
				input:    1,
				output: &model.TransferStatus{
					ID:   1,
					Name: "authorized",
					Code: model.StatusAuthorized,
				},
				outputErr: nil,
			},
			getAccountByID: getAccountByIDOp{
				isCalled: true,
				input:    23,
				output: &model.Account{
					ID:              23,
					Number:          "some number",
					CurrencyCode:    "kzt",
					BankCode:        "bcc",
					BankID:          25,
					AccountTypeID:   3,
					EncryptedConfig: defaultEncryptedConfig,
				},
				outputErr: nil,
			},
			declineTransfer: declineTransferOp{
				isCalled: true,
				input: &grpc.DeclineTransferRequest{
					Account: &grpc.AccountTerminal{
						BankCode: testsdk.Ptr("bcc"),
						Config:   defaultCfg,
					},
					TransferId:          testsdk.Ptr(uint64(1)),
					ExternalReferenceId: testsdk.Ptr(strconv.FormatUint(1, 10)),
				},
				output: &grpc.DeclineTransferResponse{
					TransferId:       testsdk.Ptr(uint64(1)),
					TransferStatusId: testsdk.Ptr(uint64(3)),
				},
				outputErr: nil,
			},
			getTransferStatusByID2: getTransferStatusByIDOp{
				isCalled: true,
				input:    3,
				output: &model.TransferStatus{
					ID:   3,
					Name: "success",
					Code: model.StatusSuccess,
				},
				outputErr: nil,
			},
			updateStatus: updateStatusOp{
				isCalled:        true,
				inputTime:       &testNow,
				inputTransferID: 1,
				inputStatusID:   3,
				outputErr:       nil,
			},
			appConfig: map[string]any{
				"SYMMETRIC_KEY": normalKey,
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctrl := gomock.NewController(t)
			defer ctrl.Finish()

			c := context.Background()
			ctx, cancel := context.WithCancel(c)
			e2e.InitAppE2E(t, ctx, cancel, nil, nil, tt.appConfig)

			transferStatusDBMock := databasemocks.NewMockTransferStatuser(ctrl)
			transferDBMock := databasemocks.NewMockTransferer(ctrl)
			transferManagerDBMock := databasemocks.NewMockTransferManager(ctrl)
			accountInfoDBMock := databasemocks.NewMockAccountInformer(ctrl)

			multiaccountinCliMock := grpcmock.NewMockMultiaccountingClient(ctrl)

			s := TransferService{
				transferStatusRepo:    transferStatusDBMock,
				transferManageRepo:    transferManagerDBMock,
				transferRepo:          transferDBMock,
				accountInfoRepo:       accountInfoDBMock,
				multiaccountingClient: multiaccountinCliMock,
				getCurrentTime: func() time.Time {
					return testNow
				},
			}

			transferDBMock.EXPECT().GetByID(
				gomock.Any(),
				tt.getTransferByID.input,
			).Return(
				tt.getTransferByID.output,
				tt.getTransferByID.outputErr,
			).Times(1)

			if tt.getAccountByID.isCalled {
				accountInfoDBMock.EXPECT().GetById(
					gomock.Any(),
					tt.getAccountByID.input,
				).Return(
					tt.getAccountByID.output,
					tt.getAccountByID.outputErr,
				).Times(1)
			}

			if tt.getTransferStatusByID.isCalled {
				transferStatusDBMock.EXPECT().GetByID(
					gomock.Any(),
					tt.getTransferStatusByID.input,
				).Return(
					tt.getTransferStatusByID.output,
					tt.getTransferStatusByID.outputErr,
				).Times(1)
			}

			if tt.declineTransfer.isCalled {
				multiaccountinCliMock.EXPECT().DeclineTransfer(
					gomock.Any(),
					tt.declineTransfer.input,
				).Return(
					tt.declineTransfer.output,
					tt.declineTransfer.outputErr,
				).Times(1)
			}

			if tt.getTransferStatusByID2.isCalled {
				transferStatusDBMock.EXPECT().GetByID(
					gomock.Any(),
					tt.getTransferStatusByID2.input,
				).Return(
					tt.getTransferStatusByID2.output,
					tt.getTransferStatusByID2.outputErr,
				).Times(1)
			}

			if tt.updateStatus.isCalled {
				transferManagerDBMock.EXPECT().UpdateStatus(
					gomock.Any(),
					tt.updateStatus.inputTransferID,
					tt.updateStatus.inputStatusID,
					tt.updateStatus.inputTime,
				).Return(
					tt.updateStatus.outputErr,
				).Times(1)
			}

			res, err := s.CancelTransfer(context.Background(), tt.req)
			if tt.wantErr != nil {
				require.Error(t, err)
				require.ErrorAs(t, err, &tt.wantErr)
			} else {
				require.NoError(t, err)
			}

			require.Equal(t, tt.want, res)
		})
	}
}

func TestTransferServiceFinalizeTransfer(t *testing.T) {
	defaultCfg, err := structpb.NewStruct(map[string]any{
		"my-name-is": "skrillex",
	})
	if err != nil {
		t.Fatal(err)
	}

	testNow := time.Date(2009, time.November, 10, 23, 0, 0, 0, time.UTC)

	type getTransferStatusAsCodeMapOp struct {
		output    map[model.TransferStatusCode]uint64
		outputErr error
	}

	type getByFiltersOp struct {
		isCalled  bool
		input     schema.TransferFilters
		output    []model.Transfer
		outputErr error
	}

	type getAccountByIDOp struct {
		isCalled  bool
		input     uint64
		output    *model.Account
		outputErr error
	}

	type getTransferDetailsOp struct {
		isCalled  bool
		input     *grpc.GetTransferDetailsRequest
		output    *grpc.GetTransferDetailsResponse
		outputErr error
	}

	type getByIDOp struct {
		isCalled  bool
		inputID   uint64
		output    *model.TransferStatus
		outputErr error
	}

	type updateStatusOp struct {
		isCalled      bool
		inputID       uint64
		inputStatusID uint64
		inputTime     *time.Time
		outputErr     error
	}

	type billOutTransferOp struct {
		isCalled  bool
		input     *grpc.BillOutTransferRequestV1
		outputErr error
	}

	tests := []struct {
		name                       string
		wantErr                    error
		getTransferStatusAsCodeMap getTransferStatusAsCodeMapOp
		getByFilters               getByFiltersOp
		getByFilters2              getByFiltersOp
		getByFilters3              getByFiltersOp
		getAccountByID             getAccountByIDOp
		getTransferDetails         getTransferDetailsOp
		getByID                    getByIDOp
		updateStatus               updateStatusOp
		billOutTransfer            billOutTransferOp
		appConfig                  map[string]any
	}{
		{
			name:    "error_getting_statuses",
			wantErr: errors.New("some error"),
			getTransferStatusAsCodeMap: getTransferStatusAsCodeMapOp{
				output:    nil,
				outputErr: errors.New("some error"),
			},
		},
		{
			name:    "get_by_filters_error",
			wantErr: errors.New("some error"),
			getTransferStatusAsCodeMap: getTransferStatusAsCodeMapOp{
				output: map[model.TransferStatusCode]uint64{
					model.StatusInProgress: 1,
					model.StatusHold:       2,
					model.StatusInRedo:     3,
				},
				outputErr: nil,
			},
			getByFilters: getByFiltersOp{
				isCalled: true,
				input: schema.TransferFilters{
					StatusID: "1",
				},
				output:    nil,
				outputErr: errors.New("some error"),
			},
		},
		{
			name:    "get_account_by_id_error",
			wantErr: errors.New("some error"),
			getTransferStatusAsCodeMap: getTransferStatusAsCodeMapOp{
				output: map[model.TransferStatusCode]uint64{
					model.StatusInProgress: 1,
					model.StatusHold:       2,
					model.StatusInRedo:     3,
				},
				outputErr: nil,
			},
			getByFilters: getByFiltersOp{
				isCalled: true,
				input: schema.TransferFilters{
					StatusID: "1",
				},
				output: []model.Transfer{
					{
						ID:                      1,
						ProjectID:               10,
						MerchantID:              21,
						StatusID:                1,
						Amount:                  100000,
						MerchantBIN:             "some bin",
						MerchantAccount:         "some recipient account",
						MerchantBeneficiaryCode: "some beneficiary code",
						Description:             "some description",
						PaymentPurposeCode:      "some payment purpose code",
						TransferTypeID:          1,
						AcquirerID:              25,
						AccountID:               23,
						EntityTypeID:            40,
						CountryCodeID:           1,
					},
				},
				outputErr: nil,
			},
			getByFilters2: getByFiltersOp{
				isCalled: true,
				input: schema.TransferFilters{
					StatusID: "2",
				},
				output:    []model.Transfer{},
				outputErr: nil,
			},
			getByFilters3: getByFiltersOp{
				isCalled: true,
				input: schema.TransferFilters{
					StatusID: "3",
				},
				output:    []model.Transfer{},
				outputErr: nil,
			},
			getAccountByID: getAccountByIDOp{
				isCalled:  true,
				input:     23,
				output:    nil,
				outputErr: errors.New("some error"),
			},
		},
		{
			name:    "unmarshal_error",
			wantErr: aes.KeySizeError(3),
			getTransferStatusAsCodeMap: getTransferStatusAsCodeMapOp{
				output: map[model.TransferStatusCode]uint64{
					model.StatusInProgress: 1,
					model.StatusHold:       2,
					model.StatusInRedo:     3,
				},
				outputErr: nil,
			},
			getByFilters: getByFiltersOp{
				isCalled: true,
				input: schema.TransferFilters{
					StatusID: "1",
				},
				output: []model.Transfer{
					{
						ID:                      1,
						ProjectID:               10,
						MerchantID:              21,
						StatusID:                1,
						Amount:                  100000,
						MerchantBIN:             "some bin",
						MerchantAccount:         "some recipient account",
						MerchantBeneficiaryCode: "some beneficiary code",
						Description:             "some description",
						PaymentPurposeCode:      "some payment purpose code",
						TransferTypeID:          1,
						AcquirerID:              25,
						AccountID:               23,
						EntityTypeID:            40,
						CountryCodeID:           1,
					},
				},
				outputErr: nil,
			},
			getByFilters2: getByFiltersOp{
				isCalled: true,
				input: schema.TransferFilters{
					StatusID: "2",
				},
				output:    []model.Transfer{},
				outputErr: nil,
			},
			getByFilters3: getByFiltersOp{
				isCalled: true,
				input: schema.TransferFilters{
					StatusID: "3",
				},
				output:    []model.Transfer{},
				outputErr: nil,
			},
			getAccountByID: getAccountByIDOp{
				isCalled: true,
				input:    23,
				output: &model.Account{
					ID:              23,
					Number:          "some number",
					CurrencyCode:    "kzt",
					BankCode:        "bcc",
					BankID:          25,
					AccountTypeID:   3,
					EncryptedConfig: defaultEncryptedConfig,
				},
				outputErr: nil,
			},
			appConfig: map[string]any{
				"SYMMETRIC_KEY": badKey,
			},
		},
		{
			name:    "get_transfer_details_error",
			wantErr: errors.New("some error"),
			getTransferStatusAsCodeMap: getTransferStatusAsCodeMapOp{
				output: map[model.TransferStatusCode]uint64{
					model.StatusInProgress: 1,
					model.StatusHold:       2,
					model.StatusInRedo:     3,
				},
				outputErr: nil,
			},
			getByFilters: getByFiltersOp{
				isCalled: true,
				input: schema.TransferFilters{
					StatusID: "1",
				},
				output: []model.Transfer{
					{
						ID:                      1,
						ProjectID:               10,
						MerchantID:              21,
						StatusID:                1,
						Amount:                  100000,
						MerchantBIN:             "some bin",
						MerchantAccount:         "some recipient account",
						MerchantBeneficiaryCode: "some beneficiary code",
						Description:             "some description",
						PaymentPurposeCode:      "some payment purpose code",
						TransferTypeID:          1,
						AcquirerID:              25,
						AccountID:               23,
						EntityTypeID:            40,
						CountryCodeID:           1,
					},
				},
				outputErr: nil,
			},
			getByFilters2: getByFiltersOp{
				isCalled: true,
				input: schema.TransferFilters{
					StatusID: "2",
				},
				output:    []model.Transfer{},
				outputErr: nil,
			},
			getByFilters3: getByFiltersOp{
				isCalled: true,
				input: schema.TransferFilters{
					StatusID: "3",
				},
				output:    []model.Transfer{},
				outputErr: nil,
			},
			getAccountByID: getAccountByIDOp{
				isCalled: true,
				input:    23,
				output: &model.Account{
					ID:              23,
					Number:          "some number",
					CurrencyCode:    "kzt",
					BankCode:        "bcc",
					BankID:          25,
					AccountTypeID:   3,
					EncryptedConfig: defaultEncryptedConfig,
				},
				outputErr: nil,
			},
			getTransferDetails: getTransferDetailsOp{
				isCalled: true,
				input: &grpc.GetTransferDetailsRequest{
					Account: &grpc.AccountTerminal{
						BankCode: testsdk.Ptr("bcc"),
						Config:   defaultCfg,
					},
					TransferId:          testsdk.Ptr(uint64(1)),
					ExternalReferenceId: testsdk.Ptr(strconv.FormatUint(1, 10)),
				},
				output:    nil,
				outputErr: errors.New("some error"),
			},
			appConfig: map[string]any{
				"SYMMETRIC_KEY": normalKey,
			},
		},
		{
			name:    "status_is_not_final",
			wantErr: nil,
			getTransferStatusAsCodeMap: getTransferStatusAsCodeMapOp{
				output: map[model.TransferStatusCode]uint64{
					model.StatusInProgress: 1,
					model.StatusHold:       2,
					model.StatusInRedo:     3,
				},
				outputErr: nil,
			},
			getByFilters: getByFiltersOp{
				isCalled: true,
				input: schema.TransferFilters{
					StatusID: "1",
				},
				output: []model.Transfer{
					{
						ID:                      1,
						ProjectID:               10,
						MerchantID:              21,
						StatusID:                1,
						Amount:                  100000,
						MerchantBIN:             "some bin",
						MerchantAccount:         "some recipient account",
						MerchantBeneficiaryCode: "some beneficiary code",
						Description:             "some description",
						PaymentPurposeCode:      "some payment purpose code",
						TransferTypeID:          1,
						AcquirerID:              25,
						AccountID:               23,
						EntityTypeID:            40,
						CountryCodeID:           1,
					},
				},
				outputErr: nil,
			},
			getByFilters2: getByFiltersOp{
				isCalled: true,
				input: schema.TransferFilters{
					StatusID: "2",
				},
				output:    []model.Transfer{},
				outputErr: nil,
			},
			getByFilters3: getByFiltersOp{
				isCalled: true,
				input: schema.TransferFilters{
					StatusID: "3",
				},
				output:    []model.Transfer{},
				outputErr: nil,
			},
			getAccountByID: getAccountByIDOp{
				isCalled: true,
				input:    23,
				output: &model.Account{
					ID:              23,
					Number:          "some number",
					CurrencyCode:    "kzt",
					BankCode:        "bcc",
					BankID:          25,
					AccountTypeID:   3,
					EncryptedConfig: defaultEncryptedConfig,
				},
				outputErr: nil,
			},
			getTransferDetails: getTransferDetailsOp{
				isCalled: true,
				input: &grpc.GetTransferDetailsRequest{
					Account: &grpc.AccountTerminal{
						BankCode: testsdk.Ptr("bcc"),
						Config:   defaultCfg,
					},
					TransferId:          testsdk.Ptr(uint64(1)),
					ExternalReferenceId: testsdk.Ptr(strconv.FormatUint(1, 10)),
				},
				output: &grpc.GetTransferDetailsResponse{
					TransferStatusId: testsdk.Ptr(uint64(1)),
				},
				outputErr: nil,
			},
			appConfig: map[string]any{
				"SYMMETRIC_KEY": normalKey,
			},
		},
		{
			name:    "get_by_id_error",
			wantErr: errors.New("some error"),
			getTransferStatusAsCodeMap: getTransferStatusAsCodeMapOp{
				output: map[model.TransferStatusCode]uint64{
					model.StatusInProgress: 1,
					model.StatusHold:       2,
					model.StatusInRedo:     3,
				},
				outputErr: nil,
			},
			getByFilters: getByFiltersOp{
				isCalled: true,
				input: schema.TransferFilters{
					StatusID: "1",
				},
				output: []model.Transfer{
					{
						ID:                      1,
						ProjectID:               10,
						MerchantID:              21,
						StatusID:                1,
						Amount:                  100000,
						MerchantBIN:             "some bin",
						MerchantAccount:         "some recipient account",
						MerchantBeneficiaryCode: "some beneficiary code",
						Description:             "some description",
						PaymentPurposeCode:      "some payment purpose code",
						TransferTypeID:          1,
						AcquirerID:              25,
						AccountID:               23,
						EntityTypeID:            40,
						CountryCodeID:           1,
					},
				},
				outputErr: nil,
			},
			getByFilters2: getByFiltersOp{
				isCalled: true,
				input: schema.TransferFilters{
					StatusID: "2",
				},
				output:    []model.Transfer{},
				outputErr: nil,
			},
			getByFilters3: getByFiltersOp{
				isCalled: true,
				input: schema.TransferFilters{
					StatusID: "3",
				},
				output:    []model.Transfer{},
				outputErr: nil,
			},
			getAccountByID: getAccountByIDOp{
				isCalled: true,
				input:    23,
				output: &model.Account{
					ID:              23,
					Number:          "some number",
					CurrencyCode:    "kzt",
					BankCode:        "bcc",
					BankID:          25,
					AccountTypeID:   3,
					EncryptedConfig: defaultEncryptedConfig,
				},
				outputErr: nil,
			},
			getTransferDetails: getTransferDetailsOp{
				isCalled: true,
				input: &grpc.GetTransferDetailsRequest{
					Account: &grpc.AccountTerminal{
						BankCode: testsdk.Ptr("bcc"),
						Config:   defaultCfg,
					},
					TransferId:          testsdk.Ptr(uint64(1)),
					ExternalReferenceId: testsdk.Ptr(strconv.FormatUint(1, 10)),
				},
				output: &grpc.GetTransferDetailsResponse{
					TransferStatusId: testsdk.Ptr(uint64(4)),
				},
				outputErr: nil,
			},
			getByID: getByIDOp{
				isCalled:  true,
				inputID:   4,
				output:    nil,
				outputErr: errors.New("some error"),
			},
			appConfig: map[string]any{
				"SYMMETRIC_KEY": normalKey,
			},
		},
		{
			name:    "update_status_error",
			wantErr: errors.New("some error"),
			getTransferStatusAsCodeMap: getTransferStatusAsCodeMapOp{
				output: map[model.TransferStatusCode]uint64{
					model.StatusInProgress: 1,
					model.StatusHold:       2,
					model.StatusInRedo:     3,
				},
				outputErr: nil,
			},
			getByFilters: getByFiltersOp{
				isCalled: true,
				input: schema.TransferFilters{
					StatusID: "1",
				},
				output: []model.Transfer{
					{
						ID:                      1,
						ProjectID:               10,
						MerchantID:              21,
						StatusID:                1,
						Amount:                  100000,
						MerchantBIN:             "some bin",
						MerchantAccount:         "some recipient account",
						MerchantBeneficiaryCode: "some beneficiary code",
						Description:             "some description",
						PaymentPurposeCode:      "some payment purpose code",
						TransferTypeID:          1,
						AcquirerID:              25,
						AccountID:               23,
						EntityTypeID:            40,
						CountryCodeID:           1,
					},
				},
				outputErr: nil,
			},
			getByFilters2: getByFiltersOp{
				isCalled: true,
				input: schema.TransferFilters{
					StatusID: "2",
				},
				output:    []model.Transfer{},
				outputErr: nil,
			},
			getByFilters3: getByFiltersOp{
				isCalled: true,
				input: schema.TransferFilters{
					StatusID: "3",
				},
				output:    []model.Transfer{},
				outputErr: nil,
			},
			getAccountByID: getAccountByIDOp{
				isCalled: true,
				input:    23,
				output: &model.Account{
					ID:              23,
					Number:          "some number",
					CurrencyCode:    "kzt",
					BankCode:        "bcc",
					BankID:          25,
					AccountTypeID:   3,
					EncryptedConfig: defaultEncryptedConfig,
				},
				outputErr: nil,
			},
			getTransferDetails: getTransferDetailsOp{
				isCalled: true,
				input: &grpc.GetTransferDetailsRequest{
					Account: &grpc.AccountTerminal{
						BankCode: testsdk.Ptr("bcc"),
						Config:   defaultCfg,
					},
					TransferId:          testsdk.Ptr(uint64(1)),
					ExternalReferenceId: testsdk.Ptr(strconv.FormatUint(1, 10)),
				},
				output: &grpc.GetTransferDetailsResponse{
					TransferStatusId: testsdk.Ptr(uint64(4)),
				},
				outputErr: nil,
			},
			getByID: getByIDOp{
				isCalled: true,
				inputID:  4,
				output: &model.TransferStatus{
					ID:   4,
					Code: model.StatusSuccess,
					Name: "success",
				},
				outputErr: nil,
			},
			updateStatus: updateStatusOp{
				isCalled:      true,
				inputID:       1,
				inputStatusID: 4,
				inputTime:     &testNow,
				outputErr:     errors.New("some error"),
			},
			appConfig: map[string]any{
				"SYMMETRIC_KEY": normalKey,
			},
		},
		{
			name:    "bill_out_error",
			wantErr: nil,
			getTransferStatusAsCodeMap: getTransferStatusAsCodeMapOp{
				output: map[model.TransferStatusCode]uint64{
					model.StatusInProgress: 1,
					model.StatusHold:       2,
					model.StatusInRedo:     3,
					model.StatusSuccess:    4,
				},
				outputErr: nil,
			},
			getByFilters: getByFiltersOp{
				isCalled: true,
				input: schema.TransferFilters{
					StatusID: "1",
				},
				output: []model.Transfer{
					{
						ID:                      1,
						ProjectID:               10,
						MerchantID:              21,
						StatusID:                1,
						Amount:                  100000,
						MerchantBIN:             "some bin",
						MerchantAccount:         "some recipient account",
						MerchantBeneficiaryCode: "some beneficiary code",
						Description:             "some description",
						PaymentPurposeCode:      "some payment purpose code",
						TransferTypeID:          1,
						AcquirerID:              25,
						AccountID:               23,
						EntityTypeID:            40,
						CountryCodeID:           1,
					},
				},
				outputErr: nil,
			},
			getByFilters2: getByFiltersOp{
				isCalled: true,
				input: schema.TransferFilters{
					StatusID: "2",
				},
				output:    []model.Transfer{},
				outputErr: nil,
			},
			getByFilters3: getByFiltersOp{
				isCalled: true,
				input: schema.TransferFilters{
					StatusID: "3",
				},
				output:    []model.Transfer{},
				outputErr: nil,
			},
			getAccountByID: getAccountByIDOp{
				isCalled: true,
				input:    23,
				output: &model.Account{
					ID:              23,
					Number:          "some number",
					CurrencyCode:    "kzt",
					BankCode:        "bcc",
					BankID:          25,
					AccountTypeID:   3,
					EncryptedConfig: defaultEncryptedConfig,
				},
				outputErr: nil,
			},
			getTransferDetails: getTransferDetailsOp{
				isCalled: true,
				input: &grpc.GetTransferDetailsRequest{
					Account: &grpc.AccountTerminal{
						BankCode: testsdk.Ptr("bcc"),
						Config:   defaultCfg,
					},
					TransferId:          testsdk.Ptr(uint64(1)),
					ExternalReferenceId: testsdk.Ptr(strconv.FormatUint(1, 10)),
				},
				output: &grpc.GetTransferDetailsResponse{
					TransferStatusId: testsdk.Ptr(uint64(4)),
				},
				outputErr: nil,
			},
			getByID: getByIDOp{
				isCalled: true,
				inputID:  4,
				output: &model.TransferStatus{
					ID:   4,
					Code: model.StatusSuccess,
					Name: "success",
				},
				outputErr: nil,
			},
			updateStatus: updateStatusOp{
				isCalled:      true,
				inputID:       1,
				inputStatusID: 4,
				inputTime:     &testNow,
				outputErr:     nil,
			},
			billOutTransfer: billOutTransferOp{
				isCalled: true,
				input: &grpc.BillOutTransferRequestV1{
					ProjectId:     testsdk.Ptr(uint64(10)),
					Amount:        testsdk.Ptr(float64(100000)),
					MerchantId:    testsdk.Ptr(uint64(21)),
					TransferId:    testsdk.Ptr(uint64(1)),
					AccountNumber: testsdk.Ptr("some number"),
				},
				outputErr: errors.New("some error"),
			},
			appConfig: map[string]any{
				"SYMMETRIC_KEY": normalKey,
			},
		},
		{
			name:    "success",
			wantErr: nil,
			getTransferStatusAsCodeMap: getTransferStatusAsCodeMapOp{
				output: map[model.TransferStatusCode]uint64{
					model.StatusInProgress: 1,
					model.StatusHold:       2,
					model.StatusInRedo:     3,
					model.StatusSuccess:    4,
				},
				outputErr: nil,
			},
			getByFilters: getByFiltersOp{
				isCalled: true,
				input: schema.TransferFilters{
					StatusID: "1",
				},
				output: []model.Transfer{
					{
						ID:                      1,
						ProjectID:               10,
						MerchantID:              21,
						StatusID:                1,
						Amount:                  100000,
						MerchantBIN:             "some bin",
						MerchantAccount:         "some recipient account",
						MerchantBeneficiaryCode: "some beneficiary code",
						Description:             "some description",
						PaymentPurposeCode:      "some payment purpose code",
						TransferTypeID:          1,
						AcquirerID:              25,
						AccountID:               23,
						EntityTypeID:            40,
						CountryCodeID:           1,
					},
				},
				outputErr: nil,
			},
			getByFilters2: getByFiltersOp{
				isCalled: true,
				input: schema.TransferFilters{
					StatusID: "2",
				},
				output:    []model.Transfer{},
				outputErr: nil,
			},
			getByFilters3: getByFiltersOp{
				isCalled: true,
				input: schema.TransferFilters{
					StatusID: "3",
				},
				output:    []model.Transfer{},
				outputErr: nil,
			},
			getAccountByID: getAccountByIDOp{
				isCalled: true,
				input:    23,
				output: &model.Account{
					ID:              23,
					Number:          "some number",
					CurrencyCode:    "kzt",
					BankCode:        "bcc",
					BankID:          25,
					AccountTypeID:   3,
					EncryptedConfig: defaultEncryptedConfig,
				},
				outputErr: nil,
			},
			getTransferDetails: getTransferDetailsOp{
				isCalled: true,
				input: &grpc.GetTransferDetailsRequest{
					Account: &grpc.AccountTerminal{
						BankCode: testsdk.Ptr("bcc"),
						Config:   defaultCfg,
					},
					TransferId:          testsdk.Ptr(uint64(1)),
					ExternalReferenceId: testsdk.Ptr(strconv.FormatUint(1, 10)),
				},
				output: &grpc.GetTransferDetailsResponse{
					TransferStatusId: testsdk.Ptr(uint64(4)),
				},
				outputErr: nil,
			},
			getByID: getByIDOp{
				isCalled: true,
				inputID:  4,
				output: &model.TransferStatus{
					ID:   4,
					Code: model.StatusSuccess,
					Name: "success",
				},
				outputErr: nil,
			},
			updateStatus: updateStatusOp{
				isCalled:      true,
				inputID:       1,
				inputStatusID: 4,
				inputTime:     &testNow,
				outputErr:     nil,
			},
			billOutTransfer: billOutTransferOp{
				isCalled: true,
				input: &grpc.BillOutTransferRequestV1{
					ProjectId:     testsdk.Ptr(uint64(10)),
					Amount:        testsdk.Ptr(float64(100000)),
					MerchantId:    testsdk.Ptr(uint64(21)),
					TransferId:    testsdk.Ptr(uint64(1)),
					AccountNumber: testsdk.Ptr("some number"),
				},
				outputErr: nil,
			},
			appConfig: map[string]any{
				"SYMMETRIC_KEY": normalKey,
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctrl := gomock.NewController(t)
			defer ctrl.Finish()

			c := context.Background()
			ctx, cancel := context.WithCancel(c)
			e2e.InitAppE2E(t, ctx, cancel, nil, nil, tt.appConfig)

			transferStatusDBMock := databasemocks.NewMockTransferStatuser(ctrl)
			transferDBMock := databasemocks.NewMockTransferer(ctrl)
			accountInfoDBMock := databasemocks.NewMockAccountInformer(ctrl)
			multiaccountingCliMock := grpcmock.NewMockMultiaccountingClient(ctrl)
			transferManagerDBMock := databasemocks.NewMockTransferManager(ctrl)
			billingCliMock := grpcmock.NewMockBillingClient(ctrl)

			transferStatusDBMock.EXPECT().AsCodeMap(
				gomock.Any(),
			).Return(
				tt.getTransferStatusAsCodeMap.output,
				tt.getTransferStatusAsCodeMap.outputErr,
			).Times(1)

			if tt.getByFilters.isCalled {
				transferDBMock.EXPECT().GetByFilters(
					gomock.Any(),
					tt.getByFilters.input,
				).Return(
					tt.getByFilters.output,
					tt.getByFilters.outputErr,
				).Times(1)
			}

			if tt.getByFilters2.isCalled {
				transferDBMock.EXPECT().GetByFilters(
					gomock.Any(),
					tt.getByFilters2.input,
				).Return(
					tt.getByFilters2.output,
					tt.getByFilters2.outputErr,
				).Times(1)
			}

			if tt.getByFilters3.isCalled {
				transferDBMock.EXPECT().GetByFilters(
					gomock.Any(),
					tt.getByFilters3.input,
				).Return(
					tt.getByFilters3.output,
					tt.getByFilters3.outputErr,
				).Times(1)
			}

			if tt.getAccountByID.isCalled {
				accountInfoDBMock.EXPECT().GetById(
					gomock.Any(),
					tt.getAccountByID.input,
				).Return(
					tt.getAccountByID.output,
					tt.getAccountByID.outputErr,
				).Times(1)
			}

			if tt.getTransferDetails.isCalled {
				multiaccountingCliMock.EXPECT().GetTransferDetails(
					gomock.Any(),
					tt.getTransferDetails.input,
				).Return(
					tt.getTransferDetails.output,
					tt.getTransferDetails.outputErr,
				).Times(1)
			}

			if tt.getByID.isCalled {
				transferStatusDBMock.EXPECT().GetByID(
					gomock.Any(),
					tt.getByID.inputID,
				).Return(
					tt.getByID.output,
					tt.getByID.outputErr,
				).Times(1)
			}

			if tt.updateStatus.isCalled {
				transferManagerDBMock.EXPECT().UpdateStatus(
					gomock.Any(),
					tt.updateStatus.inputID,
					tt.updateStatus.inputStatusID,
					tt.updateStatus.inputTime,
				).Return(
					tt.updateStatus.outputErr,
				).Times(1)
			}

			if tt.billOutTransfer.isCalled {
				billingCliMock.EXPECT().BillOutTransferV1(
					gomock.Any(),
					tt.billOutTransfer.input,
				).Return(
					nil,
					tt.billOutTransfer.outputErr,
				).Times(1)
			}

			s := TransferService{
				transferStatusRepo:    transferStatusDBMock,
				transferManageRepo:    transferManagerDBMock,
				transferRepo:          transferDBMock,
				accountInfoRepo:       accountInfoDBMock,
				multiaccountingClient: multiaccountingCliMock,
				billingClient:         billingCliMock,
				getCurrentTime: func() time.Time {
					return testNow
				},
			}

			err := s.FinalizeTransfer(context.Background())
			time.Sleep(500 * time.Millisecond)
			if tt.wantErr != nil {
				require.Error(t, err)
				require.Equal(t, tt.wantErr, err)
			} else {
				require.NoError(t, err)
			}
		})
	}
}
