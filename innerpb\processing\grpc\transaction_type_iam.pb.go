// Code generated by protoc-gen-go-mvp. DO NOT EDIT.
// versions:
// - protoc-gen-go-mvp v2.0.1
// created by mvp authors

package grpc

import (
	context "context"
	grpc "google.golang.org/grpc"
	metadata "google.golang.org/grpc/metadata"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
)

func NewIamTransactionTypeServer(
	srv TransactionTypeServer,
) TransactionTypeServer {
	return &iamTransactionTypeServer{
		srv: srv,
	}
}

var _ TransactionTypeServer = (*iamTransactionTypeServer)(nil)

type iamTransactionTypeServer struct {
	UnimplementedTransactionTypeServer

	srv TransactionTypeServer
}

func (s *iamTransactionTypeServer) GetAll(
	ctx context.Context,
	req *emptypb.Empty,
) (
	*TransactionTypeResponseV1,
	error,
) {
	return s.srv.GetAll(ctx, req)
}

func (s *iamTransactionTypeServer) GetTransactionPayOutTypes(
	ctx context.Context,
	req *emptypb.Empty,
) (
	*TransactionTypeResponseV1,
	error,
) {
	return s.srv.GetTransactionPayOutTypes(ctx, req)
}

func (s *iamTransactionTypeServer) GetTransactionPayInTypes(
	ctx context.Context,
	req *emptypb.Empty,
) (
	*TransactionTypeResponseV1,
	error,
) {
	return s.srv.GetTransactionPayInTypes(ctx, req)
}

func (s *iamTransactionTypeServer) GetAggregatedTransactionType(
	ctx context.Context,
	req *GetAggregatedTransactionTypeRequestV1,
) (
	*GetAggregatedTransactionTypeResponseV1,
	error,
) {
	return s.srv.GetAggregatedTransactionType(ctx, req)
}

func (s *iamTransactionTypeServer) GetAggregatedTypeByID(
	ctx context.Context,
	req *GetAggregatedTypeByIDRequestV1,
) (
	*GetAggregatedTypeByIDResponseV1,
	error,
) {
	return s.srv.GetAggregatedTypeByID(ctx, req)
}

func (s *iamTransactionTypeServer) GetAggregatedTransactionTypeByTypeID(
	ctx context.Context,
	req *GetAggregatedTransactionTypeByTypeIDRequest,
) (
	*GetAggregatedTransactionTypeByTypeIDResponse,
	error,
) {
	return s.srv.GetAggregatedTransactionTypeByTypeID(ctx, req)
}

func NewIamTransactionTypeClient(
	client TransactionTypeClient,
) TransactionTypeClient {
	return &iamTransactionTypeClient{
		client: client,
	}
}

type iamTransactionTypeClient struct {
	client TransactionTypeClient
}

func (s *iamTransactionTypeClient) GetAll(
	ctx context.Context,
	req *emptypb.Empty,
	opts ...grpc.CallOption,
) (
	*TransactionTypeResponseV1,
	error,
) {
	md, _ := metadata.FromIncomingContext(ctx)
	return s.client.GetAll(metadata.NewOutgoingContext(ctx, md), req)
}

func (s *iamTransactionTypeClient) GetTransactionPayOutTypes(
	ctx context.Context,
	req *emptypb.Empty,
	opts ...grpc.CallOption,
) (
	*TransactionTypeResponseV1,
	error,
) {
	md, _ := metadata.FromIncomingContext(ctx)
	return s.client.GetTransactionPayOutTypes(metadata.NewOutgoingContext(ctx, md), req)
}

func (s *iamTransactionTypeClient) GetTransactionPayInTypes(
	ctx context.Context,
	req *emptypb.Empty,
	opts ...grpc.CallOption,
) (
	*TransactionTypeResponseV1,
	error,
) {
	md, _ := metadata.FromIncomingContext(ctx)
	return s.client.GetTransactionPayInTypes(metadata.NewOutgoingContext(ctx, md), req)
}

func (s *iamTransactionTypeClient) GetAggregatedTransactionType(
	ctx context.Context,
	req *GetAggregatedTransactionTypeRequestV1,
	opts ...grpc.CallOption,
) (
	*GetAggregatedTransactionTypeResponseV1,
	error,
) {
	md, _ := metadata.FromIncomingContext(ctx)
	return s.client.GetAggregatedTransactionType(metadata.NewOutgoingContext(ctx, md), req)
}

func (s *iamTransactionTypeClient) GetAggregatedTypeByID(
	ctx context.Context,
	req *GetAggregatedTypeByIDRequestV1,
	opts ...grpc.CallOption,
) (
	*GetAggregatedTypeByIDResponseV1,
	error,
) {
	md, _ := metadata.FromIncomingContext(ctx)
	return s.client.GetAggregatedTypeByID(metadata.NewOutgoingContext(ctx, md), req)
}

func (s *iamTransactionTypeClient) GetAggregatedTransactionTypeByTypeID(
	ctx context.Context,
	req *GetAggregatedTransactionTypeByTypeIDRequest,
	opts ...grpc.CallOption,
) (
	*GetAggregatedTransactionTypeByTypeIDResponse,
	error,
) {
	md, _ := metadata.FromIncomingContext(ctx)
	return s.client.GetAggregatedTransactionTypeByTypeID(metadata.NewOutgoingContext(ctx, md), req)
}
