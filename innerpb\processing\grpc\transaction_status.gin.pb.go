// Code generated by protoc-gen-go-mvp. DO NOT EDIT.
// versions:
// - protoc-gen-go-mvp v2.0.1
// created by mvp authors

package grpc

import (
	dog "git.local/sensitive/sdk/dog"
	gin "github.com/gin-gonic/gin"
	metadata "google.golang.org/grpc/metadata"
	http "net/http"
)

func RegisterGinTransactionStatusRouterGroup(
	in *gin.RouterGroup,
	handler func(func(c *gin.Context) error) gin.HandlerFunc,
) error {
	service, err := NewGinTransactionStatusService()
	if err != nil {
		return err
	}

	routerGroup := in.Group("processing.transaction.transaction_status.TransactionStatus")
	routerGroup.PUT("/GetBatchTransactionWithStatuses", handler(service.GetBatchTransactionWithStatuses))
	return nil
}

func NewGinTransactionStatusService() (GinTransactionStatusServer, error) {
	client, err := NewPreparedTransactionStatusClient()
	if err != nil {
		return nil, err
	}

	return &ginTransactionStatusServer{
		client: NewLoggedTransactionStatusClient(
			NewIamTransactionStatusClient(client),
		),
	}, nil
}

//go:generate mockgen -destination=./grpcmock/transaction_status.gin.pb.go -package=grpcmock -source=transaction_status.gin.pb.go GinTransactionStatusServer
type GinTransactionStatusServer interface {
	GetBatchTransactionWithStatuses(c *gin.Context) error
}

var _ GinTransactionStatusServer = (*ginTransactionStatusServer)(nil)

type ginTransactionStatusServer struct {
	client TransactionStatusClient
}

type TransactionStatus_GetBatchTransactionWithStatuses_Success struct {
	// Status is the status of the response. Set true
	Status bool `json:"status"`
	// Message is a human-readable explanation of the status
	Message string `json:"message"`
	// StatusCode is the GoErr status. Set 0
	StatusCode int `json:"status_code"`
	// Result is the result of the request
	Result *BatchTransactionResponseV1 `json:"result"`
}

type TransactionStatus_GetBatchTransactionWithStatuses_Failure struct {
	// Status is the status of the response. Set false
	Status bool `json:"status"`
	// Message is a human-readable explanation of the status
	Message string `json:"message"`
	// StatusCode is the GoErr status
	StatusCode int `json:"status_code"`
}

// GetBatchTransactionWithStatuses
// @Summary GetBatchTransactionWithStatuses
// @Security bearerAuth
// @ID TransactionStatus_GetBatchTransactionWithStatuses
// @Accept json
// @Param request body BatchTransactionRequestV1 true "BatchTransactionRequestV1"
// @Success 200 {object} TransactionStatus_GetBatchTransactionWithStatuses_Success
// @Failure 401 {object} TransactionStatus_GetBatchTransactionWithStatuses_Failure "goerrpkg.ErrorTypeAuthorization"
// @Failure 404 {object} TransactionStatus_GetBatchTransactionWithStatuses_Failure "goerrpkg.ErrorTypeNotFound"
// @Failure 400 {object} TransactionStatus_GetBatchTransactionWithStatuses_Failure "goerrpkg.ErrorTypeBadRequest"
// @Failure 500 {object} TransactionStatus_GetBatchTransactionWithStatuses_Failure "goerrpkg.ErrorTypeUnexpectedError"
// @Failure 400 {object} TransactionStatus_GetBatchTransactionWithStatuses_Failure "gin.ErrorTypeBind"
// @Failure 500 {object} TransactionStatus_GetBatchTransactionWithStatuses_Failure "Undefined error"
// @Produce json
// @Router /processing.transaction.transaction_status.TransactionStatus/GetBatchTransactionWithStatuses [put]
// @tags TransactionStatus
func (s *ginTransactionStatusServer) GetBatchTransactionWithStatuses(c *gin.Context) error {
	ctx, span := dog.CreateSpan(c.Request.Context(), "GinTransactionStatusServer_GetBatchTransactionWithStatuses")
	defer span.End()

	var request BatchTransactionRequestV1
	if err := c.ShouldBindJSON(&request); err != nil {
		return err
	}

	ctx = metadata.NewOutgoingContext(ctx, metadata.MD(c.Request.Header))

	response, err := s.client.GetBatchTransactionWithStatuses(ctx, &request)
	if err != nil {
		return err
	}

	c.JSON(http.StatusOK, &TransactionStatus_GetBatchTransactionWithStatuses_Success{
		Status:     true,
		Message:    "Success",
		StatusCode: 0,
		Result:     response,
	})
	return nil
}
