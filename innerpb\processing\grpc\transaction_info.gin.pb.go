// Code generated by protoc-gen-go-mvp. DO NOT EDIT.
// versions:
// - protoc-gen-go-mvp v2.0.1
// created by mvp authors

package grpc

import (
	dog "git.local/sensitive/sdk/dog"
	gin "github.com/gin-gonic/gin"
	metadata "google.golang.org/grpc/metadata"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
	http "net/http"
)

func RegisterGinTransactionInfRouterGroup(
	in *gin.RouterGroup,
	handler func(func(c *gin.Context) error) gin.HandlerFunc,
) error {
	service, err := NewGinTransactionInfService()
	if err != nil {
		return err
	}

	routerGroup := in.Group("processing.transaction.transaction_info.TransactionInf")
	routerGroup.PUT("/UpdateJobsMessage", handler(service.UpdateJobsMessage))
	routerGroup.PUT("/UpdateBankResponseMessage", handler(service.UpdateBankResponseMessage))
	routerGroup.PUT("/GetTransactionsWithEmptyBankReferenceID", handler(service.GetTransactionsWithEmptyBankReferenceID))
	routerGroup.PUT("/UpdateBankReferenceID", handler(service.UpdateBankReferenceID))
	return nil
}

func NewGinTransactionInfService() (GinTransactionInfServer, error) {
	client, err := NewPreparedTransactionInfClient()
	if err != nil {
		return nil, err
	}

	return &ginTransactionInfServer{
		client: NewLoggedTransactionInfClient(
			NewIamTransactionInfClient(client),
		),
	}, nil
}

//go:generate mockgen -destination=./grpcmock/transaction_info.gin.pb.go -package=grpcmock -source=transaction_info.gin.pb.go GinTransactionInfServer
type GinTransactionInfServer interface {
	UpdateJobsMessage(c *gin.Context) error
	UpdateBankResponseMessage(c *gin.Context) error
	GetTransactionsWithEmptyBankReferenceID(c *gin.Context) error
	UpdateBankReferenceID(c *gin.Context) error
}

var _ GinTransactionInfServer = (*ginTransactionInfServer)(nil)

type ginTransactionInfServer struct {
	client TransactionInfClient
}

type TransactionInf_UpdateJobsMessage_Success struct {
	// Status is the status of the response. Set true
	Status bool `json:"status"`
	// Message is a human-readable explanation of the status
	Message string `json:"message"`
	// StatusCode is the GoErr status. Set 0
	StatusCode int `json:"status_code"`
	// Result is the result of the request
	Result *emptypb.Empty `json:"result"`
}

type TransactionInf_UpdateJobsMessage_Failure struct {
	// Status is the status of the response. Set false
	Status bool `json:"status"`
	// Message is a human-readable explanation of the status
	Message string `json:"message"`
	// StatusCode is the GoErr status
	StatusCode int `json:"status_code"`
}

// UpdateJobsMessage
// @Summary UpdateJobsMessage
// @Security bearerAuth
// @ID TransactionInf_UpdateJobsMessage
// @Accept json
// @Param request body UpdateJobsMessageRequestV1 true "UpdateJobsMessageRequestV1"
// @Success 200 {object} TransactionInf_UpdateJobsMessage_Success
// @Failure 401 {object} TransactionInf_UpdateJobsMessage_Failure "goerrpkg.ErrorTypeAuthorization"
// @Failure 404 {object} TransactionInf_UpdateJobsMessage_Failure "goerrpkg.ErrorTypeNotFound"
// @Failure 400 {object} TransactionInf_UpdateJobsMessage_Failure "goerrpkg.ErrorTypeBadRequest"
// @Failure 500 {object} TransactionInf_UpdateJobsMessage_Failure "goerrpkg.ErrorTypeUnexpectedError"
// @Failure 400 {object} TransactionInf_UpdateJobsMessage_Failure "gin.ErrorTypeBind"
// @Failure 500 {object} TransactionInf_UpdateJobsMessage_Failure "Undefined error"
// @Produce json
// @Router /processing.transaction.transaction_info.TransactionInf/UpdateJobsMessage [put]
// @tags TransactionInf
func (s *ginTransactionInfServer) UpdateJobsMessage(c *gin.Context) error {
	ctx, span := dog.CreateSpan(c.Request.Context(), "GinTransactionInfServer_UpdateJobsMessage")
	defer span.End()

	var request UpdateJobsMessageRequestV1
	if err := c.ShouldBindJSON(&request); err != nil {
		return err
	}

	ctx = metadata.NewOutgoingContext(ctx, metadata.MD(c.Request.Header))

	response, err := s.client.UpdateJobsMessage(ctx, &request)
	if err != nil {
		return err
	}

	c.JSON(http.StatusOK, &TransactionInf_UpdateJobsMessage_Success{
		Status:     true,
		Message:    "Success",
		StatusCode: 0,
		Result:     response,
	})
	return nil
}

type TransactionInf_UpdateBankResponseMessage_Success struct {
	// Status is the status of the response. Set true
	Status bool `json:"status"`
	// Message is a human-readable explanation of the status
	Message string `json:"message"`
	// StatusCode is the GoErr status. Set 0
	StatusCode int `json:"status_code"`
	// Result is the result of the request
	Result *emptypb.Empty `json:"result"`
}

type TransactionInf_UpdateBankResponseMessage_Failure struct {
	// Status is the status of the response. Set false
	Status bool `json:"status"`
	// Message is a human-readable explanation of the status
	Message string `json:"message"`
	// StatusCode is the GoErr status
	StatusCode int `json:"status_code"`
}

// UpdateBankResponseMessage
// @Summary UpdateBankResponseMessage
// @Security bearerAuth
// @ID TransactionInf_UpdateBankResponseMessage
// @Accept json
// @Param request body UpdateBankResponseMessageRequestV1 true "UpdateBankResponseMessageRequestV1"
// @Success 200 {object} TransactionInf_UpdateBankResponseMessage_Success
// @Failure 401 {object} TransactionInf_UpdateBankResponseMessage_Failure "goerrpkg.ErrorTypeAuthorization"
// @Failure 404 {object} TransactionInf_UpdateBankResponseMessage_Failure "goerrpkg.ErrorTypeNotFound"
// @Failure 400 {object} TransactionInf_UpdateBankResponseMessage_Failure "goerrpkg.ErrorTypeBadRequest"
// @Failure 500 {object} TransactionInf_UpdateBankResponseMessage_Failure "goerrpkg.ErrorTypeUnexpectedError"
// @Failure 400 {object} TransactionInf_UpdateBankResponseMessage_Failure "gin.ErrorTypeBind"
// @Failure 500 {object} TransactionInf_UpdateBankResponseMessage_Failure "Undefined error"
// @Produce json
// @Router /processing.transaction.transaction_info.TransactionInf/UpdateBankResponseMessage [put]
// @tags TransactionInf
func (s *ginTransactionInfServer) UpdateBankResponseMessage(c *gin.Context) error {
	ctx, span := dog.CreateSpan(c.Request.Context(), "GinTransactionInfServer_UpdateBankResponseMessage")
	defer span.End()

	var request UpdateBankResponseMessageRequestV1
	if err := c.ShouldBindJSON(&request); err != nil {
		return err
	}

	ctx = metadata.NewOutgoingContext(ctx, metadata.MD(c.Request.Header))

	response, err := s.client.UpdateBankResponseMessage(ctx, &request)
	if err != nil {
		return err
	}

	c.JSON(http.StatusOK, &TransactionInf_UpdateBankResponseMessage_Success{
		Status:     true,
		Message:    "Success",
		StatusCode: 0,
		Result:     response,
	})
	return nil
}

type TransactionInf_GetTransactionsWithEmptyBankReferenceID_Success struct {
	// Status is the status of the response. Set true
	Status bool `json:"status"`
	// Message is a human-readable explanation of the status
	Message string `json:"message"`
	// StatusCode is the GoErr status. Set 0
	StatusCode int `json:"status_code"`
	// Result is the result of the request
	Result *GetTransactionsWithEmptyBankReferenceIDResponseV1 `json:"result"`
}

type TransactionInf_GetTransactionsWithEmptyBankReferenceID_Failure struct {
	// Status is the status of the response. Set false
	Status bool `json:"status"`
	// Message is a human-readable explanation of the status
	Message string `json:"message"`
	// StatusCode is the GoErr status
	StatusCode int `json:"status_code"`
}

// GetTransactionsWithEmptyBankReferenceID
// @Summary GetTransactionsWithEmptyBankReferenceID
// @Security bearerAuth
// @ID TransactionInf_GetTransactionsWithEmptyBankReferenceID
// @Accept json
// @Param request body emptypb.Empty true "Empty"
// @Success 200 {object} TransactionInf_GetTransactionsWithEmptyBankReferenceID_Success
// @Failure 401 {object} TransactionInf_GetTransactionsWithEmptyBankReferenceID_Failure "goerrpkg.ErrorTypeAuthorization"
// @Failure 404 {object} TransactionInf_GetTransactionsWithEmptyBankReferenceID_Failure "goerrpkg.ErrorTypeNotFound"
// @Failure 400 {object} TransactionInf_GetTransactionsWithEmptyBankReferenceID_Failure "goerrpkg.ErrorTypeBadRequest"
// @Failure 500 {object} TransactionInf_GetTransactionsWithEmptyBankReferenceID_Failure "goerrpkg.ErrorTypeUnexpectedError"
// @Failure 400 {object} TransactionInf_GetTransactionsWithEmptyBankReferenceID_Failure "gin.ErrorTypeBind"
// @Failure 500 {object} TransactionInf_GetTransactionsWithEmptyBankReferenceID_Failure "Undefined error"
// @Produce json
// @Router /processing.transaction.transaction_info.TransactionInf/GetTransactionsWithEmptyBankReferenceID [put]
// @tags TransactionInf
func (s *ginTransactionInfServer) GetTransactionsWithEmptyBankReferenceID(c *gin.Context) error {
	ctx, span := dog.CreateSpan(c.Request.Context(), "GinTransactionInfServer_GetTransactionsWithEmptyBankReferenceID")
	defer span.End()

	var request emptypb.Empty
	if err := c.ShouldBindJSON(&request); err != nil {
		return err
	}

	ctx = metadata.NewOutgoingContext(ctx, metadata.MD(c.Request.Header))

	response, err := s.client.GetTransactionsWithEmptyBankReferenceID(ctx, &request)
	if err != nil {
		return err
	}

	c.JSON(http.StatusOK, &TransactionInf_GetTransactionsWithEmptyBankReferenceID_Success{
		Status:     true,
		Message:    "Success",
		StatusCode: 0,
		Result:     response,
	})
	return nil
}

type TransactionInf_UpdateBankReferenceID_Success struct {
	// Status is the status of the response. Set true
	Status bool `json:"status"`
	// Message is a human-readable explanation of the status
	Message string `json:"message"`
	// StatusCode is the GoErr status. Set 0
	StatusCode int `json:"status_code"`
	// Result is the result of the request
	Result *emptypb.Empty `json:"result"`
}

type TransactionInf_UpdateBankReferenceID_Failure struct {
	// Status is the status of the response. Set false
	Status bool `json:"status"`
	// Message is a human-readable explanation of the status
	Message string `json:"message"`
	// StatusCode is the GoErr status
	StatusCode int `json:"status_code"`
}

// UpdateBankReferenceID
// @Summary UpdateBankReferenceID
// @Security bearerAuth
// @ID TransactionInf_UpdateBankReferenceID
// @Accept json
// @Param request body UpdateBankReferenceIDRequestV1 true "UpdateBankReferenceIDRequestV1"
// @Success 200 {object} TransactionInf_UpdateBankReferenceID_Success
// @Failure 401 {object} TransactionInf_UpdateBankReferenceID_Failure "goerrpkg.ErrorTypeAuthorization"
// @Failure 404 {object} TransactionInf_UpdateBankReferenceID_Failure "goerrpkg.ErrorTypeNotFound"
// @Failure 400 {object} TransactionInf_UpdateBankReferenceID_Failure "goerrpkg.ErrorTypeBadRequest"
// @Failure 500 {object} TransactionInf_UpdateBankReferenceID_Failure "goerrpkg.ErrorTypeUnexpectedError"
// @Failure 400 {object} TransactionInf_UpdateBankReferenceID_Failure "gin.ErrorTypeBind"
// @Failure 500 {object} TransactionInf_UpdateBankReferenceID_Failure "Undefined error"
// @Produce json
// @Router /processing.transaction.transaction_info.TransactionInf/UpdateBankReferenceID [put]
// @tags TransactionInf
func (s *ginTransactionInfServer) UpdateBankReferenceID(c *gin.Context) error {
	ctx, span := dog.CreateSpan(c.Request.Context(), "GinTransactionInfServer_UpdateBankReferenceID")
	defer span.End()

	var request UpdateBankReferenceIDRequestV1
	if err := c.ShouldBindJSON(&request); err != nil {
		return err
	}

	ctx = metadata.NewOutgoingContext(ctx, metadata.MD(c.Request.Header))

	response, err := s.client.UpdateBankReferenceID(ctx, &request)
	if err != nil {
		return err
	}

	c.JSON(http.StatusOK, &TransactionInf_UpdateBankReferenceID_Success{
		Status:     true,
		Message:    "Success",
		StatusCode: 0,
		Result:     response,
	})
	return nil
}
