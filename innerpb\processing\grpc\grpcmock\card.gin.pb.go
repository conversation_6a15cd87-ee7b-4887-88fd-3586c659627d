// Code generated by MockGen. DO NOT EDIT.
// Source: card.gin.pb.go

// Package grpcmock is a generated GoMock package.
package grpcmock

import (
	reflect "reflect"

	gin "github.com/gin-gonic/gin"
	gomock "github.com/golang/mock/gomock"
)

// MockGinCardServer is a mock of GinCardServer interface.
type MockGinCardServer struct {
	ctrl     *gomock.Controller
	recorder *MockGinCardServerMockRecorder
}

// MockGinCardServerMockRecorder is the mock recorder for MockGinCardServer.
type MockGinCardServerMockRecorder struct {
	mock *MockGinCardServer
}

// NewMockGinCardServer creates a new mock instance.
func NewMockGinCardServer(ctrl *gomock.Controller) *MockGinCardServer {
	mock := &MockGinCardServer{ctrl: ctrl}
	mock.recorder = &MockGinCardServerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockGinCardServer) EXPECT() *MockGinCardServerMockRecorder {
	return m.recorder
}

// CheckClientActiveness mocks base method.
func (m *MockGinCardServer) CheckClientActiveness(c *gin.Context) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CheckClientActiveness", c)
	ret0, _ := ret[0].(error)
	return ret0
}

// CheckClientActiveness indicates an expected call of CheckClientActiveness.
func (mr *MockGinCardServerMockRecorder) CheckClientActiveness(c interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CheckClientActiveness", reflect.TypeOf((*MockGinCardServer)(nil).CheckClientActiveness), c)
}

// CheckExpireCards mocks base method.
func (m *MockGinCardServer) CheckExpireCards(c *gin.Context) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CheckExpireCards", c)
	ret0, _ := ret[0].(error)
	return ret0
}

// CheckExpireCards indicates an expected call of CheckExpireCards.
func (mr *MockGinCardServerMockRecorder) CheckExpireCards(c interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CheckExpireCards", reflect.TypeOf((*MockGinCardServer)(nil).CheckExpireCards), c)
}

// CreateClientV1 mocks base method.
func (m *MockGinCardServer) CreateClientV1(c *gin.Context) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateClientV1", c)
	ret0, _ := ret[0].(error)
	return ret0
}

// CreateClientV1 indicates an expected call of CreateClientV1.
func (mr *MockGinCardServerMockRecorder) CreateClientV1(c interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateClientV1", reflect.TypeOf((*MockGinCardServer)(nil).CreateClientV1), c)
}

// CreateNewHashKey mocks base method.
func (m *MockGinCardServer) CreateNewHashKey(c *gin.Context) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateNewHashKey", c)
	ret0, _ := ret[0].(error)
	return ret0
}

// CreateNewHashKey indicates an expected call of CreateNewHashKey.
func (mr *MockGinCardServerMockRecorder) CreateNewHashKey(c interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateNewHashKey", reflect.TypeOf((*MockGinCardServer)(nil).CreateNewHashKey), c)
}

// DecryptPayInCard mocks base method.
func (m *MockGinCardServer) DecryptPayInCard(c *gin.Context) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DecryptPayInCard", c)
	ret0, _ := ret[0].(error)
	return ret0
}

// DecryptPayInCard indicates an expected call of DecryptPayInCard.
func (mr *MockGinCardServerMockRecorder) DecryptPayInCard(c interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DecryptPayInCard", reflect.TypeOf((*MockGinCardServer)(nil).DecryptPayInCard), c)
}

// DecryptPayOutCard mocks base method.
func (m *MockGinCardServer) DecryptPayOutCard(c *gin.Context) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DecryptPayOutCard", c)
	ret0, _ := ret[0].(error)
	return ret0
}

// DecryptPayOutCard indicates an expected call of DecryptPayOutCard.
func (mr *MockGinCardServerMockRecorder) DecryptPayOutCard(c interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DecryptPayOutCard", reflect.TypeOf((*MockGinCardServer)(nil).DecryptPayOutCard), c)
}

// GetCardByPan mocks base method.
func (m *MockGinCardServer) GetCardByPan(c *gin.Context) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetCardByPan", c)
	ret0, _ := ret[0].(error)
	return ret0
}

// GetCardByPan indicates an expected call of GetCardByPan.
func (mr *MockGinCardServerMockRecorder) GetCardByPan(c interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetCardByPan", reflect.TypeOf((*MockGinCardServer)(nil).GetCardByPan), c)
}

// GetCardTokensV1 mocks base method.
func (m *MockGinCardServer) GetCardTokensV1(c *gin.Context) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetCardTokensV1", c)
	ret0, _ := ret[0].(error)
	return ret0
}

// GetCardTokensV1 indicates an expected call of GetCardTokensV1.
func (mr *MockGinCardServerMockRecorder) GetCardTokensV1(c interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetCardTokensV1", reflect.TypeOf((*MockGinCardServer)(nil).GetCardTokensV1), c)
}

// GetClientListByProjectClient mocks base method.
func (m *MockGinCardServer) GetClientListByProjectClient(c *gin.Context) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetClientListByProjectClient", c)
	ret0, _ := ret[0].(error)
	return ret0
}

// GetClientListByProjectClient indicates an expected call of GetClientListByProjectClient.
func (mr *MockGinCardServerMockRecorder) GetClientListByProjectClient(c interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetClientListByProjectClient", reflect.TypeOf((*MockGinCardServer)(nil).GetClientListByProjectClient), c)
}

// GetClientListByVerification mocks base method.
func (m *MockGinCardServer) GetClientListByVerification(c *gin.Context) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetClientListByVerification", c)
	ret0, _ := ret[0].(error)
	return ret0
}

// GetClientListByVerification indicates an expected call of GetClientListByVerification.
func (mr *MockGinCardServerMockRecorder) GetClientListByVerification(c interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetClientListByVerification", reflect.TypeOf((*MockGinCardServer)(nil).GetClientListByVerification), c)
}

// GetEncryptedCardToken mocks base method.
func (m *MockGinCardServer) GetEncryptedCardToken(c *gin.Context) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetEncryptedCardToken", c)
	ret0, _ := ret[0].(error)
	return ret0
}

// GetEncryptedCardToken indicates an expected call of GetEncryptedCardToken.
func (mr *MockGinCardServerMockRecorder) GetEncryptedCardToken(c interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetEncryptedCardToken", reflect.TypeOf((*MockGinCardServer)(nil).GetEncryptedCardToken), c)
}

// GetOneClickPayInCardsV1 mocks base method.
func (m *MockGinCardServer) GetOneClickPayInCardsV1(c *gin.Context) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetOneClickPayInCardsV1", c)
	ret0, _ := ret[0].(error)
	return ret0
}

// GetOneClickPayInCardsV1 indicates an expected call of GetOneClickPayInCardsV1.
func (mr *MockGinCardServerMockRecorder) GetOneClickPayInCardsV1(c interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetOneClickPayInCardsV1", reflect.TypeOf((*MockGinCardServer)(nil).GetOneClickPayInCardsV1), c)
}

// GetOneClickPayOutCardsV1 mocks base method.
func (m *MockGinCardServer) GetOneClickPayOutCardsV1(c *gin.Context) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetOneClickPayOutCardsV1", c)
	ret0, _ := ret[0].(error)
	return ret0
}

// GetOneClickPayOutCardsV1 indicates an expected call of GetOneClickPayOutCardsV1.
func (mr *MockGinCardServerMockRecorder) GetOneClickPayOutCardsV1(c interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetOneClickPayOutCardsV1", reflect.TypeOf((*MockGinCardServer)(nil).GetOneClickPayOutCardsV1), c)
}

// GetPanByCardIdV1 mocks base method.
func (m *MockGinCardServer) GetPanByCardIdV1(c *gin.Context) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetPanByCardIdV1", c)
	ret0, _ := ret[0].(error)
	return ret0
}

// GetPanByCardIdV1 indicates an expected call of GetPanByCardIdV1.
func (mr *MockGinCardServerMockRecorder) GetPanByCardIdV1(c interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetPanByCardIdV1", reflect.TypeOf((*MockGinCardServer)(nil).GetPanByCardIdV1), c)
}

// GetPanByHashedIdV1 mocks base method.
func (m *MockGinCardServer) GetPanByHashedIdV1(c *gin.Context) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetPanByHashedIdV1", c)
	ret0, _ := ret[0].(error)
	return ret0
}

// GetPanByHashedIdV1 indicates an expected call of GetPanByHashedIdV1.
func (mr *MockGinCardServerMockRecorder) GetPanByHashedIdV1(c interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetPanByHashedIdV1", reflect.TypeOf((*MockGinCardServer)(nil).GetPanByHashedIdV1), c)
}

// GetPanInfoByProjectId mocks base method.
func (m *MockGinCardServer) GetPanInfoByProjectId(c *gin.Context) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetPanInfoByProjectId", c)
	ret0, _ := ret[0].(error)
	return ret0
}

// GetPanInfoByProjectId indicates an expected call of GetPanInfoByProjectId.
func (mr *MockGinCardServerMockRecorder) GetPanInfoByProjectId(c interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetPanInfoByProjectId", reflect.TypeOf((*MockGinCardServer)(nil).GetPanInfoByProjectId), c)
}

// NewKey mocks base method.
func (m *MockGinCardServer) NewKey(c *gin.Context) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "NewKey", c)
	ret0, _ := ret[0].(error)
	return ret0
}

// NewKey indicates an expected call of NewKey.
func (mr *MockGinCardServerMockRecorder) NewKey(c interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "NewKey", reflect.TypeOf((*MockGinCardServer)(nil).NewKey), c)
}

// ReEncryptCard mocks base method.
func (m *MockGinCardServer) ReEncryptCard(c *gin.Context) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ReEncryptCard", c)
	ret0, _ := ret[0].(error)
	return ret0
}

// ReEncryptCard indicates an expected call of ReEncryptCard.
func (mr *MockGinCardServerMockRecorder) ReEncryptCard(c interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ReEncryptCard", reflect.TypeOf((*MockGinCardServer)(nil).ReEncryptCard), c)
}

// RotateCardKeys mocks base method.
func (m *MockGinCardServer) RotateCardKeys(c *gin.Context) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "RotateCardKeys", c)
	ret0, _ := ret[0].(error)
	return ret0
}

// RotateCardKeys indicates an expected call of RotateCardKeys.
func (mr *MockGinCardServerMockRecorder) RotateCardKeys(c interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "RotateCardKeys", reflect.TypeOf((*MockGinCardServer)(nil).RotateCardKeys), c)
}

// RotateHashKeys mocks base method.
func (m *MockGinCardServer) RotateHashKeys(c *gin.Context) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "RotateHashKeys", c)
	ret0, _ := ret[0].(error)
	return ret0
}

// RotateHashKeys indicates an expected call of RotateHashKeys.
func (mr *MockGinCardServerMockRecorder) RotateHashKeys(c interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "RotateHashKeys", reflect.TypeOf((*MockGinCardServer)(nil).RotateHashKeys), c)
}
