edition = "2023";

package processing.multiacquiring.multiacquiring_emission;

option go_package = "git.local/sensitive/innerpb/processing/grpc";

import "google/protobuf/empty.proto";

service MultiacquiringEmission {
  rpc GetEmission(google.protobuf.Empty) returns (EmissionResponse) {}
  rpc ConfirmEmission(EmoneyRequest) returns (EmoneyResponse) {}
}

message Value {
  uint64 id = 1;
  string dscr = 2;
  string clibin = 3;
  string iban = 4;
  string agent_name = 5;
  string agent = 6;
  double amount = 7;
}

message EmissionResponse{
  string code = 1;
  string message = 2;
  repeated Value Value = 3;
}

message EmoneyResponse{
  string code = 1;
  string message = 2;
}

message EmoneyRequest{
  string id = 1;
}