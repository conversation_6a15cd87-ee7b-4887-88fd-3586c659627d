package domain

type PrimalPayoutRequest struct {
	Amount             float64           `json:"amount"`
	CallbackUrl        string            `json:"callback_url"`
	ConfirmUrl         string            `json:"confirm_url,omitempty"`
	Description        string            `json:"description"`
	FailureRedirectUrl string            `json:"failure_redirect_url"`
	MerchantId         int               `json:"merchant_id" validate:"required"`
	ProjectClientId    string            `json:"project_client_id"`
	ProjectId          int               `json:"project_id"`
	ProjectReferenceId string            `json:"project_reference_id"`
	Shipment           string            `json:"shipment,omitempty"`
	SuccessRedirectUrl string            `json:"success_redirect_url"`
	AdditionalData     map[string]string `json:"additional_data"`
	IsHold             bool              `json:"is_hold"`
	ProjectOrderID     string            `json:"project_order_id,omitempty"`
}

type MakePayOutRequest struct {
	TransactionID   uint64 `json:"transaction_id" validate:"required"`
	TransactionHash string `json:"transaction_hash" validate:"required"`
	UserEmail       string `json:"user_email,omitempty" validate:"omitempty,email"`
	UserPhone       string `json:"user_phone"`
	EncryptedPan    string `json:"encrypted_pan" validate:"required"`
}

type MakePayOutResponse struct {
	TransactionID         uint64 `json:"transaction_id" validate:"required"`
	TransactionStatusCode string `json:"transaction_status_code" validate:"required"`
	AcquirerCode          string `json:"acquirer_code" validate:"required"`
}
