// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.5
// 	protoc        v5.29.3
// source: inner/processing/grpc/commission.proto

package grpc

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type CalculatePrimalAmountRequestV1 struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	TransactionID *uint64                `protobuf:"varint,1,opt,name=transactionID" json:"transactionID,omitempty"`
	Amount        *float64               `protobuf:"fixed64,2,opt,name=amount" json:"amount,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CalculatePrimalAmountRequestV1) Reset() {
	*x = CalculatePrimalAmountRequestV1{}
	mi := &file_inner_processing_grpc_commission_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CalculatePrimalAmountRequestV1) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CalculatePrimalAmountRequestV1) ProtoMessage() {}

func (x *CalculatePrimalAmountRequestV1) ProtoReflect() protoreflect.Message {
	mi := &file_inner_processing_grpc_commission_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CalculatePrimalAmountRequestV1.ProtoReflect.Descriptor instead.
func (*CalculatePrimalAmountRequestV1) Descriptor() ([]byte, []int) {
	return file_inner_processing_grpc_commission_proto_rawDescGZIP(), []int{0}
}

func (x *CalculatePrimalAmountRequestV1) GetTransactionID() uint64 {
	if x != nil && x.TransactionID != nil {
		return *x.TransactionID
	}
	return 0
}

func (x *CalculatePrimalAmountRequestV1) GetAmount() float64 {
	if x != nil && x.Amount != nil {
		return *x.Amount
	}
	return 0
}

type TransactionCommissionResponseV1 struct {
	state                        protoimpl.MessageState `protogen:"open.v1"`
	Id                           *uint64                `protobuf:"varint,1,opt,name=id" json:"id,omitempty"`
	AcquirerCommissionId         *uint64                `protobuf:"varint,2,opt,name=acquirer_commission_id,json=acquirerCommissionId" json:"acquirer_commission_id,omitempty"`
	ProjectLowerCommissionId     *uint64                `protobuf:"varint,3,opt,name=project_lower_commission_id,json=projectLowerCommissionId" json:"project_lower_commission_id,omitempty"`
	ProjectUpperCommissionId     *uint64                `protobuf:"varint,4,opt,name=project_upper_commission_id,json=projectUpperCommissionId" json:"project_upper_commission_id,omitempty"`
	AcquirerCommissionAmount     *float64               `protobuf:"fixed64,5,opt,name=acquirer_commission_amount,json=acquirerCommissionAmount" json:"acquirer_commission_amount,omitempty"`
	ProjectLowerCommissionAmount *float64               `protobuf:"fixed64,6,opt,name=project_lower_commission_amount,json=projectLowerCommissionAmount" json:"project_lower_commission_amount,omitempty"`
	ProjectUpperCommissionAmount *float64               `protobuf:"fixed64,7,opt,name=project_upper_commission_amount,json=projectUpperCommissionAmount" json:"project_upper_commission_amount,omitempty"`
	unknownFields                protoimpl.UnknownFields
	sizeCache                    protoimpl.SizeCache
}

func (x *TransactionCommissionResponseV1) Reset() {
	*x = TransactionCommissionResponseV1{}
	mi := &file_inner_processing_grpc_commission_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *TransactionCommissionResponseV1) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TransactionCommissionResponseV1) ProtoMessage() {}

func (x *TransactionCommissionResponseV1) ProtoReflect() protoreflect.Message {
	mi := &file_inner_processing_grpc_commission_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TransactionCommissionResponseV1.ProtoReflect.Descriptor instead.
func (*TransactionCommissionResponseV1) Descriptor() ([]byte, []int) {
	return file_inner_processing_grpc_commission_proto_rawDescGZIP(), []int{1}
}

func (x *TransactionCommissionResponseV1) GetId() uint64 {
	if x != nil && x.Id != nil {
		return *x.Id
	}
	return 0
}

func (x *TransactionCommissionResponseV1) GetAcquirerCommissionId() uint64 {
	if x != nil && x.AcquirerCommissionId != nil {
		return *x.AcquirerCommissionId
	}
	return 0
}

func (x *TransactionCommissionResponseV1) GetProjectLowerCommissionId() uint64 {
	if x != nil && x.ProjectLowerCommissionId != nil {
		return *x.ProjectLowerCommissionId
	}
	return 0
}

func (x *TransactionCommissionResponseV1) GetProjectUpperCommissionId() uint64 {
	if x != nil && x.ProjectUpperCommissionId != nil {
		return *x.ProjectUpperCommissionId
	}
	return 0
}

func (x *TransactionCommissionResponseV1) GetAcquirerCommissionAmount() float64 {
	if x != nil && x.AcquirerCommissionAmount != nil {
		return *x.AcquirerCommissionAmount
	}
	return 0
}

func (x *TransactionCommissionResponseV1) GetProjectLowerCommissionAmount() float64 {
	if x != nil && x.ProjectLowerCommissionAmount != nil {
		return *x.ProjectLowerCommissionAmount
	}
	return 0
}

func (x *TransactionCommissionResponseV1) GetProjectUpperCommissionAmount() float64 {
	if x != nil && x.ProjectUpperCommissionAmount != nil {
		return *x.ProjectUpperCommissionAmount
	}
	return 0
}

type CalculateAndSaveUpperCommissionResponseV1 struct {
	state                 protoimpl.MessageState `protogen:"open.v1"`
	CommissionId          *uint64                `protobuf:"varint,1,opt,name=commission_id,json=commissionId" json:"commission_id,omitempty"`
	TotalCommissionAmount *float64               `protobuf:"fixed64,2,opt,name=total_commission_amount,json=totalCommissionAmount" json:"total_commission_amount,omitempty"`
	unknownFields         protoimpl.UnknownFields
	sizeCache             protoimpl.SizeCache
}

func (x *CalculateAndSaveUpperCommissionResponseV1) Reset() {
	*x = CalculateAndSaveUpperCommissionResponseV1{}
	mi := &file_inner_processing_grpc_commission_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CalculateAndSaveUpperCommissionResponseV1) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CalculateAndSaveUpperCommissionResponseV1) ProtoMessage() {}

func (x *CalculateAndSaveUpperCommissionResponseV1) ProtoReflect() protoreflect.Message {
	mi := &file_inner_processing_grpc_commission_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CalculateAndSaveUpperCommissionResponseV1.ProtoReflect.Descriptor instead.
func (*CalculateAndSaveUpperCommissionResponseV1) Descriptor() ([]byte, []int) {
	return file_inner_processing_grpc_commission_proto_rawDescGZIP(), []int{2}
}

func (x *CalculateAndSaveUpperCommissionResponseV1) GetCommissionId() uint64 {
	if x != nil && x.CommissionId != nil {
		return *x.CommissionId
	}
	return 0
}

func (x *CalculateAndSaveUpperCommissionResponseV1) GetTotalCommissionAmount() float64 {
	if x != nil && x.TotalCommissionAmount != nil {
		return *x.TotalCommissionAmount
	}
	return 0
}

type CalculatePrimalAmountResponseV1 struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	CommissionId  *uint64                `protobuf:"varint,1,opt,name=commission_id,json=commissionId" json:"commission_id,omitempty"`
	PrimalAmount  *float64               `protobuf:"fixed64,2,opt,name=primal_amount,json=primalAmount" json:"primal_amount,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CalculatePrimalAmountResponseV1) Reset() {
	*x = CalculatePrimalAmountResponseV1{}
	mi := &file_inner_processing_grpc_commission_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CalculatePrimalAmountResponseV1) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CalculatePrimalAmountResponseV1) ProtoMessage() {}

func (x *CalculatePrimalAmountResponseV1) ProtoReflect() protoreflect.Message {
	mi := &file_inner_processing_grpc_commission_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CalculatePrimalAmountResponseV1.ProtoReflect.Descriptor instead.
func (*CalculatePrimalAmountResponseV1) Descriptor() ([]byte, []int) {
	return file_inner_processing_grpc_commission_proto_rawDescGZIP(), []int{3}
}

func (x *CalculatePrimalAmountResponseV1) GetCommissionId() uint64 {
	if x != nil && x.CommissionId != nil {
		return *x.CommissionId
	}
	return 0
}

func (x *CalculatePrimalAmountResponseV1) GetPrimalAmount() float64 {
	if x != nil && x.PrimalAmount != nil {
		return *x.PrimalAmount
	}
	return 0
}

type CalculateAndSaveUpperCommissionRequestV1 struct {
	state                protoimpl.MessageState `protogen:"open.v1"`
	ProjectId            *uint64                `protobuf:"varint,1,opt,name=project_id,json=projectId" json:"project_id,omitempty"`
	MerchantId           *uint64                `protobuf:"varint,2,opt,name=merchant_id,json=merchantId" json:"merchant_id,omitempty"`
	AggregatedTypeId     *uint64                `protobuf:"varint,3,opt,name=aggregated_type_id,json=aggregatedTypeId" json:"aggregated_type_id,omitempty"`
	TransactionId        *uint64                `protobuf:"varint,4,opt,name=transaction_id,json=transactionId" json:"transaction_id,omitempty"`
	TransactionAmount    *float64               `protobuf:"fixed64,5,opt,name=transaction_amount,json=transactionAmount" json:"transaction_amount,omitempty"`
	TransactionCreatedAt *timestamppb.Timestamp `protobuf:"bytes,6,opt,name=transaction_created_at,json=transactionCreatedAt" json:"transaction_created_at,omitempty"`
	unknownFields        protoimpl.UnknownFields
	sizeCache            protoimpl.SizeCache
}

func (x *CalculateAndSaveUpperCommissionRequestV1) Reset() {
	*x = CalculateAndSaveUpperCommissionRequestV1{}
	mi := &file_inner_processing_grpc_commission_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CalculateAndSaveUpperCommissionRequestV1) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CalculateAndSaveUpperCommissionRequestV1) ProtoMessage() {}

func (x *CalculateAndSaveUpperCommissionRequestV1) ProtoReflect() protoreflect.Message {
	mi := &file_inner_processing_grpc_commission_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CalculateAndSaveUpperCommissionRequestV1.ProtoReflect.Descriptor instead.
func (*CalculateAndSaveUpperCommissionRequestV1) Descriptor() ([]byte, []int) {
	return file_inner_processing_grpc_commission_proto_rawDescGZIP(), []int{4}
}

func (x *CalculateAndSaveUpperCommissionRequestV1) GetProjectId() uint64 {
	if x != nil && x.ProjectId != nil {
		return *x.ProjectId
	}
	return 0
}

func (x *CalculateAndSaveUpperCommissionRequestV1) GetMerchantId() uint64 {
	if x != nil && x.MerchantId != nil {
		return *x.MerchantId
	}
	return 0
}

func (x *CalculateAndSaveUpperCommissionRequestV1) GetAggregatedTypeId() uint64 {
	if x != nil && x.AggregatedTypeId != nil {
		return *x.AggregatedTypeId
	}
	return 0
}

func (x *CalculateAndSaveUpperCommissionRequestV1) GetTransactionId() uint64 {
	if x != nil && x.TransactionId != nil {
		return *x.TransactionId
	}
	return 0
}

func (x *CalculateAndSaveUpperCommissionRequestV1) GetTransactionAmount() float64 {
	if x != nil && x.TransactionAmount != nil {
		return *x.TransactionAmount
	}
	return 0
}

func (x *CalculateAndSaveUpperCommissionRequestV1) GetTransactionCreatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.TransactionCreatedAt
	}
	return nil
}

type UpdateCommissionForCreditBalanceRequestV1 struct {
	state                protoimpl.MessageState `protogen:"open.v1"`
	TransactionId        *uint64                `protobuf:"varint,1,opt,name=transaction_id,json=transactionId" json:"transaction_id,omitempty"`
	Amount               *float64               `protobuf:"fixed64,2,opt,name=amount" json:"amount,omitempty"`
	ProjectId            *uint64                `protobuf:"varint,3,opt,name=project_id,json=projectId" json:"project_id,omitempty"`
	TransactionCreatedAt *timestamppb.Timestamp `protobuf:"bytes,4,opt,name=transaction_created_at,json=transactionCreatedAt" json:"transaction_created_at,omitempty"`
	unknownFields        protoimpl.UnknownFields
	sizeCache            protoimpl.SizeCache
}

func (x *UpdateCommissionForCreditBalanceRequestV1) Reset() {
	*x = UpdateCommissionForCreditBalanceRequestV1{}
	mi := &file_inner_processing_grpc_commission_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdateCommissionForCreditBalanceRequestV1) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateCommissionForCreditBalanceRequestV1) ProtoMessage() {}

func (x *UpdateCommissionForCreditBalanceRequestV1) ProtoReflect() protoreflect.Message {
	mi := &file_inner_processing_grpc_commission_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateCommissionForCreditBalanceRequestV1.ProtoReflect.Descriptor instead.
func (*UpdateCommissionForCreditBalanceRequestV1) Descriptor() ([]byte, []int) {
	return file_inner_processing_grpc_commission_proto_rawDescGZIP(), []int{5}
}

func (x *UpdateCommissionForCreditBalanceRequestV1) GetTransactionId() uint64 {
	if x != nil && x.TransactionId != nil {
		return *x.TransactionId
	}
	return 0
}

func (x *UpdateCommissionForCreditBalanceRequestV1) GetAmount() float64 {
	if x != nil && x.Amount != nil {
		return *x.Amount
	}
	return 0
}

func (x *UpdateCommissionForCreditBalanceRequestV1) GetProjectId() uint64 {
	if x != nil && x.ProjectId != nil {
		return *x.ProjectId
	}
	return 0
}

func (x *UpdateCommissionForCreditBalanceRequestV1) GetTransactionCreatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.TransactionCreatedAt
	}
	return nil
}

type UpdateCommissionForCreditBalanceResponseV1 struct {
	state              protoimpl.MessageState `protogen:"open.v1"`
	TarlanCommission   *float64               `protobuf:"fixed64,1,opt,name=tarlan_commission,json=tarlanCommission" json:"tarlan_commission,omitempty"`
	AcquirerCommission *float64               `protobuf:"fixed64,2,opt,name=acquirer_commission,json=acquirerCommission" json:"acquirer_commission,omitempty"`
	unknownFields      protoimpl.UnknownFields
	sizeCache          protoimpl.SizeCache
}

func (x *UpdateCommissionForCreditBalanceResponseV1) Reset() {
	*x = UpdateCommissionForCreditBalanceResponseV1{}
	mi := &file_inner_processing_grpc_commission_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdateCommissionForCreditBalanceResponseV1) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateCommissionForCreditBalanceResponseV1) ProtoMessage() {}

func (x *UpdateCommissionForCreditBalanceResponseV1) ProtoReflect() protoreflect.Message {
	mi := &file_inner_processing_grpc_commission_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateCommissionForCreditBalanceResponseV1.ProtoReflect.Descriptor instead.
func (*UpdateCommissionForCreditBalanceResponseV1) Descriptor() ([]byte, []int) {
	return file_inner_processing_grpc_commission_proto_rawDescGZIP(), []int{6}
}

func (x *UpdateCommissionForCreditBalanceResponseV1) GetTarlanCommission() float64 {
	if x != nil && x.TarlanCommission != nil {
		return *x.TarlanCommission
	}
	return 0
}

func (x *UpdateCommissionForCreditBalanceResponseV1) GetAcquirerCommission() float64 {
	if x != nil && x.AcquirerCommission != nil {
		return *x.AcquirerCommission
	}
	return 0
}

type GetCommissionForMainBalanceRequestV1 struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	TransactionId *uint64                `protobuf:"varint,1,opt,name=transaction_id,json=transactionId" json:"transaction_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetCommissionForMainBalanceRequestV1) Reset() {
	*x = GetCommissionForMainBalanceRequestV1{}
	mi := &file_inner_processing_grpc_commission_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetCommissionForMainBalanceRequestV1) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetCommissionForMainBalanceRequestV1) ProtoMessage() {}

func (x *GetCommissionForMainBalanceRequestV1) ProtoReflect() protoreflect.Message {
	mi := &file_inner_processing_grpc_commission_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetCommissionForMainBalanceRequestV1.ProtoReflect.Descriptor instead.
func (*GetCommissionForMainBalanceRequestV1) Descriptor() ([]byte, []int) {
	return file_inner_processing_grpc_commission_proto_rawDescGZIP(), []int{7}
}

func (x *GetCommissionForMainBalanceRequestV1) GetTransactionId() uint64 {
	if x != nil && x.TransactionId != nil {
		return *x.TransactionId
	}
	return 0
}

type GetCommissionForMainBalanceResponseV1 struct {
	state              protoimpl.MessageState `protogen:"open.v1"`
	TarlanCommission   *float64               `protobuf:"fixed64,1,opt,name=tarlan_commission,json=tarlanCommission" json:"tarlan_commission,omitempty"`
	AcquirerCommission *float64               `protobuf:"fixed64,2,opt,name=acquirer_commission,json=acquirerCommission" json:"acquirer_commission,omitempty"`
	unknownFields      protoimpl.UnknownFields
	sizeCache          protoimpl.SizeCache
}

func (x *GetCommissionForMainBalanceResponseV1) Reset() {
	*x = GetCommissionForMainBalanceResponseV1{}
	mi := &file_inner_processing_grpc_commission_proto_msgTypes[8]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetCommissionForMainBalanceResponseV1) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetCommissionForMainBalanceResponseV1) ProtoMessage() {}

func (x *GetCommissionForMainBalanceResponseV1) ProtoReflect() protoreflect.Message {
	mi := &file_inner_processing_grpc_commission_proto_msgTypes[8]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetCommissionForMainBalanceResponseV1.ProtoReflect.Descriptor instead.
func (*GetCommissionForMainBalanceResponseV1) Descriptor() ([]byte, []int) {
	return file_inner_processing_grpc_commission_proto_rawDescGZIP(), []int{8}
}

func (x *GetCommissionForMainBalanceResponseV1) GetTarlanCommission() float64 {
	if x != nil && x.TarlanCommission != nil {
		return *x.TarlanCommission
	}
	return 0
}

func (x *GetCommissionForMainBalanceResponseV1) GetAcquirerCommission() float64 {
	if x != nil && x.AcquirerCommission != nil {
		return *x.AcquirerCommission
	}
	return 0
}

type FiscalizeUpperCommissionRequestV1 struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	TransactionId *uint64                `protobuf:"varint,1,opt,name=transaction_id,json=transactionId" json:"transaction_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *FiscalizeUpperCommissionRequestV1) Reset() {
	*x = FiscalizeUpperCommissionRequestV1{}
	mi := &file_inner_processing_grpc_commission_proto_msgTypes[9]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *FiscalizeUpperCommissionRequestV1) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FiscalizeUpperCommissionRequestV1) ProtoMessage() {}

func (x *FiscalizeUpperCommissionRequestV1) ProtoReflect() protoreflect.Message {
	mi := &file_inner_processing_grpc_commission_proto_msgTypes[9]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FiscalizeUpperCommissionRequestV1.ProtoReflect.Descriptor instead.
func (*FiscalizeUpperCommissionRequestV1) Descriptor() ([]byte, []int) {
	return file_inner_processing_grpc_commission_proto_rawDescGZIP(), []int{9}
}

func (x *FiscalizeUpperCommissionRequestV1) GetTransactionId() uint64 {
	if x != nil && x.TransactionId != nil {
		return *x.TransactionId
	}
	return 0
}

type GetCommissionByTransactionIDRequestV1 struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	TransactionId *uint64                `protobuf:"varint,1,opt,name=transaction_id,json=transactionId" json:"transaction_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetCommissionByTransactionIDRequestV1) Reset() {
	*x = GetCommissionByTransactionIDRequestV1{}
	mi := &file_inner_processing_grpc_commission_proto_msgTypes[10]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetCommissionByTransactionIDRequestV1) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetCommissionByTransactionIDRequestV1) ProtoMessage() {}

func (x *GetCommissionByTransactionIDRequestV1) ProtoReflect() protoreflect.Message {
	mi := &file_inner_processing_grpc_commission_proto_msgTypes[10]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetCommissionByTransactionIDRequestV1.ProtoReflect.Descriptor instead.
func (*GetCommissionByTransactionIDRequestV1) Descriptor() ([]byte, []int) {
	return file_inner_processing_grpc_commission_proto_rawDescGZIP(), []int{10}
}

func (x *GetCommissionByTransactionIDRequestV1) GetTransactionId() uint64 {
	if x != nil && x.TransactionId != nil {
		return *x.TransactionId
	}
	return 0
}

type GetCommissionByTransactionIDResponseV1 struct {
	state                        protoimpl.MessageState `protogen:"open.v1"`
	ProjectLowerCommissionAmount *float64               `protobuf:"fixed64,1,opt,name=project_lower_commission_amount,json=projectLowerCommissionAmount" json:"project_lower_commission_amount,omitempty"`
	ProjectUpperCommissionAmount *float64               `protobuf:"fixed64,2,opt,name=project_upper_commission_amount,json=projectUpperCommissionAmount" json:"project_upper_commission_amount,omitempty"`
	unknownFields                protoimpl.UnknownFields
	sizeCache                    protoimpl.SizeCache
}

func (x *GetCommissionByTransactionIDResponseV1) Reset() {
	*x = GetCommissionByTransactionIDResponseV1{}
	mi := &file_inner_processing_grpc_commission_proto_msgTypes[11]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetCommissionByTransactionIDResponseV1) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetCommissionByTransactionIDResponseV1) ProtoMessage() {}

func (x *GetCommissionByTransactionIDResponseV1) ProtoReflect() protoreflect.Message {
	mi := &file_inner_processing_grpc_commission_proto_msgTypes[11]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetCommissionByTransactionIDResponseV1.ProtoReflect.Descriptor instead.
func (*GetCommissionByTransactionIDResponseV1) Descriptor() ([]byte, []int) {
	return file_inner_processing_grpc_commission_proto_rawDescGZIP(), []int{11}
}

func (x *GetCommissionByTransactionIDResponseV1) GetProjectLowerCommissionAmount() float64 {
	if x != nil && x.ProjectLowerCommissionAmount != nil {
		return *x.ProjectLowerCommissionAmount
	}
	return 0
}

func (x *GetCommissionByTransactionIDResponseV1) GetProjectUpperCommissionAmount() float64 {
	if x != nil && x.ProjectUpperCommissionAmount != nil {
		return *x.ProjectUpperCommissionAmount
	}
	return 0
}

type CommissionV1 struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	TaxProjectId  *uint64                `protobuf:"varint,1,opt,name=tax_project_id,json=taxProjectId" json:"tax_project_id,omitempty"`
	Amount        *float64               `protobuf:"fixed64,2,opt,name=amount" json:"amount,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CommissionV1) Reset() {
	*x = CommissionV1{}
	mi := &file_inner_processing_grpc_commission_proto_msgTypes[12]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CommissionV1) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CommissionV1) ProtoMessage() {}

func (x *CommissionV1) ProtoReflect() protoreflect.Message {
	mi := &file_inner_processing_grpc_commission_proto_msgTypes[12]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CommissionV1.ProtoReflect.Descriptor instead.
func (*CommissionV1) Descriptor() ([]byte, []int) {
	return file_inner_processing_grpc_commission_proto_rawDescGZIP(), []int{12}
}

func (x *CommissionV1) GetTaxProjectId() uint64 {
	if x != nil && x.TaxProjectId != nil {
		return *x.TaxProjectId
	}
	return 0
}

func (x *CommissionV1) GetAmount() float64 {
	if x != nil && x.Amount != nil {
		return *x.Amount
	}
	return 0
}

var File_inner_processing_grpc_commission_proto protoreflect.FileDescriptor

var file_inner_processing_grpc_commission_proto_rawDesc = string([]byte{
	0x0a, 0x26, 0x69, 0x6e, 0x6e, 0x65, 0x72, 0x2f, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69,
	0x6e, 0x67, 0x2f, 0x67, 0x72, 0x70, 0x63, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x69, 0x73, 0x73, 0x69,
	0x6f, 0x6e, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x20, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73,
	0x73, 0x69, 0x6e, 0x67, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x2e,
	0x63, 0x6f, 0x6d, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x1a, 0x1f, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x74, 0x69, 0x6d, 0x65,
	0x73, 0x74, 0x61, 0x6d, 0x70, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1b, 0x67, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x65, 0x6d, 0x70,
	0x74, 0x79, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x5e, 0x0a, 0x1e, 0x43, 0x61, 0x6c, 0x63,
	0x75, 0x6c, 0x61, 0x74, 0x65, 0x50, 0x72, 0x69, 0x6d, 0x61, 0x6c, 0x41, 0x6d, 0x6f, 0x75, 0x6e,
	0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x56, 0x31, 0x12, 0x24, 0x0a, 0x0d, 0x74, 0x72,
	0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x44, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x04, 0x52, 0x0d, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x44,
	0x12, 0x16, 0x0a, 0x06, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x01,
	0x52, 0x06, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x22, 0xb1, 0x03, 0x0a, 0x1f, 0x54, 0x72, 0x61,
	0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x43, 0x6f, 0x6d, 0x6d, 0x69, 0x73, 0x73, 0x69,
	0x6f, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x56, 0x31, 0x12, 0x0e, 0x0a, 0x02,
	0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x02, 0x69, 0x64, 0x12, 0x34, 0x0a, 0x16,
	0x61, 0x63, 0x71, 0x75, 0x69, 0x72, 0x65, 0x72, 0x5f, 0x63, 0x6f, 0x6d, 0x6d, 0x69, 0x73, 0x73,
	0x69, 0x6f, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x04, 0x52, 0x14, 0x61, 0x63,
	0x71, 0x75, 0x69, 0x72, 0x65, 0x72, 0x43, 0x6f, 0x6d, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e,
	0x49, 0x64, 0x12, 0x3d, 0x0a, 0x1b, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x5f, 0x6c, 0x6f,
	0x77, 0x65, 0x72, 0x5f, 0x63, 0x6f, 0x6d, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x5f, 0x69,
	0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x04, 0x52, 0x18, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74,
	0x4c, 0x6f, 0x77, 0x65, 0x72, 0x43, 0x6f, 0x6d, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x49,
	0x64, 0x12, 0x3d, 0x0a, 0x1b, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x5f, 0x75, 0x70, 0x70,
	0x65, 0x72, 0x5f, 0x63, 0x6f, 0x6d, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x5f, 0x69, 0x64,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x04, 0x52, 0x18, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x55,
	0x70, 0x70, 0x65, 0x72, 0x43, 0x6f, 0x6d, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x49, 0x64,
	0x12, 0x3c, 0x0a, 0x1a, 0x61, 0x63, 0x71, 0x75, 0x69, 0x72, 0x65, 0x72, 0x5f, 0x63, 0x6f, 0x6d,
	0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x5f, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x05,
	0x20, 0x01, 0x28, 0x01, 0x52, 0x18, 0x61, 0x63, 0x71, 0x75, 0x69, 0x72, 0x65, 0x72, 0x43, 0x6f,
	0x6d, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x45,
	0x0a, 0x1f, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x5f, 0x6c, 0x6f, 0x77, 0x65, 0x72, 0x5f,
	0x63, 0x6f, 0x6d, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x5f, 0x61, 0x6d, 0x6f, 0x75, 0x6e,
	0x74, 0x18, 0x06, 0x20, 0x01, 0x28, 0x01, 0x52, 0x1c, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74,
	0x4c, 0x6f, 0x77, 0x65, 0x72, 0x43, 0x6f, 0x6d, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x41,
	0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x45, 0x0a, 0x1f, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74,
	0x5f, 0x75, 0x70, 0x70, 0x65, 0x72, 0x5f, 0x63, 0x6f, 0x6d, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f,
	0x6e, 0x5f, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x07, 0x20, 0x01, 0x28, 0x01, 0x52, 0x1c,
	0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x55, 0x70, 0x70, 0x65, 0x72, 0x43, 0x6f, 0x6d, 0x6d,
	0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x22, 0x88, 0x01, 0x0a,
	0x29, 0x43, 0x61, 0x6c, 0x63, 0x75, 0x6c, 0x61, 0x74, 0x65, 0x41, 0x6e, 0x64, 0x53, 0x61, 0x76,
	0x65, 0x55, 0x70, 0x70, 0x65, 0x72, 0x43, 0x6f, 0x6d, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x56, 0x31, 0x12, 0x23, 0x0a, 0x0d, 0x63, 0x6f,
	0x6d, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x04, 0x52, 0x0c, 0x63, 0x6f, 0x6d, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x12,
	0x36, 0x0a, 0x17, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x5f, 0x63, 0x6f, 0x6d, 0x6d, 0x69, 0x73, 0x73,
	0x69, 0x6f, 0x6e, 0x5f, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x01,
	0x52, 0x15, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x43, 0x6f, 0x6d, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f,
	0x6e, 0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x22, 0x6b, 0x0a, 0x1f, 0x43, 0x61, 0x6c, 0x63, 0x75,
	0x6c, 0x61, 0x74, 0x65, 0x50, 0x72, 0x69, 0x6d, 0x61, 0x6c, 0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x56, 0x31, 0x12, 0x23, 0x0a, 0x0d, 0x63, 0x6f,
	0x6d, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x04, 0x52, 0x0c, 0x63, 0x6f, 0x6d, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x12,
	0x23, 0x0a, 0x0d, 0x70, 0x72, 0x69, 0x6d, 0x61, 0x6c, 0x5f, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x01, 0x52, 0x0c, 0x70, 0x72, 0x69, 0x6d, 0x61, 0x6c, 0x41, 0x6d,
	0x6f, 0x75, 0x6e, 0x74, 0x22, 0xc0, 0x02, 0x0a, 0x28, 0x43, 0x61, 0x6c, 0x63, 0x75, 0x6c, 0x61,
	0x74, 0x65, 0x41, 0x6e, 0x64, 0x53, 0x61, 0x76, 0x65, 0x55, 0x70, 0x70, 0x65, 0x72, 0x43, 0x6f,
	0x6d, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x56,
	0x31, 0x12, 0x1d, 0x0a, 0x0a, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x5f, 0x69, 0x64, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x09, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x49, 0x64,
	0x12, 0x1f, 0x0a, 0x0b, 0x6d, 0x65, 0x72, 0x63, 0x68, 0x61, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x04, 0x52, 0x0a, 0x6d, 0x65, 0x72, 0x63, 0x68, 0x61, 0x6e, 0x74, 0x49,
	0x64, 0x12, 0x2c, 0x0a, 0x12, 0x61, 0x67, 0x67, 0x72, 0x65, 0x67, 0x61, 0x74, 0x65, 0x64, 0x5f,
	0x74, 0x79, 0x70, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x04, 0x52, 0x10, 0x61,
	0x67, 0x67, 0x72, 0x65, 0x67, 0x61, 0x74, 0x65, 0x64, 0x54, 0x79, 0x70, 0x65, 0x49, 0x64, 0x12,
	0x25, 0x0a, 0x0e, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x69,
	0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x04, 0x52, 0x0d, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63,
	0x74, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x12, 0x2d, 0x0a, 0x12, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61,
	0x63, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x05, 0x20, 0x01,
	0x28, 0x01, 0x52, 0x11, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x41,
	0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x50, 0x0a, 0x16, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63,
	0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18,
	0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d,
	0x70, 0x52, 0x14, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x43, 0x72,
	0x65, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x22, 0xdb, 0x01, 0x0a, 0x29, 0x55, 0x70, 0x64, 0x61,
	0x74, 0x65, 0x43, 0x6f, 0x6d, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x46, 0x6f, 0x72, 0x43,
	0x72, 0x65, 0x64, 0x69, 0x74, 0x42, 0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x56, 0x31, 0x12, 0x25, 0x0a, 0x0e, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63,
	0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x0d, 0x74,
	0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x12, 0x16, 0x0a, 0x06,
	0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x01, 0x52, 0x06, 0x61, 0x6d,
	0x6f, 0x75, 0x6e, 0x74, 0x12, 0x1d, 0x0a, 0x0a, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x5f,
	0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x04, 0x52, 0x09, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63,
	0x74, 0x49, 0x64, 0x12, 0x50, 0x0a, 0x16, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69,
	0x6f, 0x6e, 0x5f, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52,
	0x14, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x43, 0x72, 0x65, 0x61,
	0x74, 0x65, 0x64, 0x41, 0x74, 0x22, 0x8a, 0x01, 0x0a, 0x2a, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65,
	0x43, 0x6f, 0x6d, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x46, 0x6f, 0x72, 0x43, 0x72, 0x65,
	0x64, 0x69, 0x74, 0x42, 0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x56, 0x31, 0x12, 0x2b, 0x0a, 0x11, 0x74, 0x61, 0x72, 0x6c, 0x61, 0x6e, 0x5f, 0x63,
	0x6f, 0x6d, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x01, 0x52,
	0x10, 0x74, 0x61, 0x72, 0x6c, 0x61, 0x6e, 0x43, 0x6f, 0x6d, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f,
	0x6e, 0x12, 0x2f, 0x0a, 0x13, 0x61, 0x63, 0x71, 0x75, 0x69, 0x72, 0x65, 0x72, 0x5f, 0x63, 0x6f,
	0x6d, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x01, 0x52, 0x12,
	0x61, 0x63, 0x71, 0x75, 0x69, 0x72, 0x65, 0x72, 0x43, 0x6f, 0x6d, 0x6d, 0x69, 0x73, 0x73, 0x69,
	0x6f, 0x6e, 0x22, 0x4d, 0x0a, 0x24, 0x47, 0x65, 0x74, 0x43, 0x6f, 0x6d, 0x6d, 0x69, 0x73, 0x73,
	0x69, 0x6f, 0x6e, 0x46, 0x6f, 0x72, 0x4d, 0x61, 0x69, 0x6e, 0x42, 0x61, 0x6c, 0x61, 0x6e, 0x63,
	0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x56, 0x31, 0x12, 0x25, 0x0a, 0x0e, 0x74, 0x72,
	0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x04, 0x52, 0x0d, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x49,
	0x64, 0x22, 0x85, 0x01, 0x0a, 0x25, 0x47, 0x65, 0x74, 0x43, 0x6f, 0x6d, 0x6d, 0x69, 0x73, 0x73,
	0x69, 0x6f, 0x6e, 0x46, 0x6f, 0x72, 0x4d, 0x61, 0x69, 0x6e, 0x42, 0x61, 0x6c, 0x61, 0x6e, 0x63,
	0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x56, 0x31, 0x12, 0x2b, 0x0a, 0x11, 0x74,
	0x61, 0x72, 0x6c, 0x61, 0x6e, 0x5f, 0x63, 0x6f, 0x6d, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x01, 0x52, 0x10, 0x74, 0x61, 0x72, 0x6c, 0x61, 0x6e, 0x43, 0x6f,
	0x6d, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x2f, 0x0a, 0x13, 0x61, 0x63, 0x71, 0x75,
	0x69, 0x72, 0x65, 0x72, 0x5f, 0x63, 0x6f, 0x6d, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x01, 0x52, 0x12, 0x61, 0x63, 0x71, 0x75, 0x69, 0x72, 0x65, 0x72, 0x43,
	0x6f, 0x6d, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x22, 0x4a, 0x0a, 0x21, 0x46, 0x69, 0x73,
	0x63, 0x61, 0x6c, 0x69, 0x7a, 0x65, 0x55, 0x70, 0x70, 0x65, 0x72, 0x43, 0x6f, 0x6d, 0x6d, 0x69,
	0x73, 0x73, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x56, 0x31, 0x12, 0x25,
	0x0a, 0x0e, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x69, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x0d, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74,
	0x69, 0x6f, 0x6e, 0x49, 0x64, 0x22, 0x4e, 0x0a, 0x25, 0x47, 0x65, 0x74, 0x43, 0x6f, 0x6d, 0x6d,
	0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x42, 0x79, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74,
	0x69, 0x6f, 0x6e, 0x49, 0x44, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x56, 0x31, 0x12, 0x25,
	0x0a, 0x0e, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x69, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x0d, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74,
	0x69, 0x6f, 0x6e, 0x49, 0x64, 0x22, 0xb6, 0x01, 0x0a, 0x26, 0x47, 0x65, 0x74, 0x43, 0x6f, 0x6d,
	0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x42, 0x79, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63,
	0x74, 0x69, 0x6f, 0x6e, 0x49, 0x44, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x56, 0x31,
	0x12, 0x45, 0x0a, 0x1f, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x5f, 0x6c, 0x6f, 0x77, 0x65,
	0x72, 0x5f, 0x63, 0x6f, 0x6d, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x5f, 0x61, 0x6d, 0x6f,
	0x75, 0x6e, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x01, 0x52, 0x1c, 0x70, 0x72, 0x6f, 0x6a, 0x65,
	0x63, 0x74, 0x4c, 0x6f, 0x77, 0x65, 0x72, 0x43, 0x6f, 0x6d, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f,
	0x6e, 0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x45, 0x0a, 0x1f, 0x70, 0x72, 0x6f, 0x6a, 0x65,
	0x63, 0x74, 0x5f, 0x75, 0x70, 0x70, 0x65, 0x72, 0x5f, 0x63, 0x6f, 0x6d, 0x6d, 0x69, 0x73, 0x73,
	0x69, 0x6f, 0x6e, 0x5f, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x01,
	0x52, 0x1c, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x55, 0x70, 0x70, 0x65, 0x72, 0x43, 0x6f,
	0x6d, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x22, 0x4c,
	0x0a, 0x0c, 0x43, 0x6f, 0x6d, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x56, 0x31, 0x12, 0x24,
	0x0a, 0x0e, 0x74, 0x61, 0x78, 0x5f, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x5f, 0x69, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x0c, 0x74, 0x61, 0x78, 0x50, 0x72, 0x6f, 0x6a, 0x65,
	0x63, 0x74, 0x49, 0x64, 0x12, 0x16, 0x0a, 0x06, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x01, 0x52, 0x06, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x32, 0xbe, 0x09, 0x0a,
	0x0a, 0x43, 0x6f, 0x6d, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0xbc, 0x01, 0x0a, 0x1f,
	0x43, 0x61, 0x6c, 0x63, 0x75, 0x6c, 0x61, 0x74, 0x65, 0x41, 0x6e, 0x64, 0x53, 0x61, 0x76, 0x65,
	0x55, 0x70, 0x70, 0x65, 0x72, 0x43, 0x6f, 0x6d, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x12,
	0x4a, 0x2e, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x2e, 0x63, 0x6f, 0x6d,
	0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x69, 0x73, 0x73, 0x69,
	0x6f, 0x6e, 0x2e, 0x43, 0x61, 0x6c, 0x63, 0x75, 0x6c, 0x61, 0x74, 0x65, 0x41, 0x6e, 0x64, 0x53,
	0x61, 0x76, 0x65, 0x55, 0x70, 0x70, 0x65, 0x72, 0x43, 0x6f, 0x6d, 0x6d, 0x69, 0x73, 0x73, 0x69,
	0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x56, 0x31, 0x1a, 0x4b, 0x2e, 0x70, 0x72,
	0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x69, 0x73, 0x73,
	0x69, 0x6f, 0x6e, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x2e, 0x43,
	0x61, 0x6c, 0x63, 0x75, 0x6c, 0x61, 0x74, 0x65, 0x41, 0x6e, 0x64, 0x53, 0x61, 0x76, 0x65, 0x55,
	0x70, 0x70, 0x65, 0x72, 0x43, 0x6f, 0x6d, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x56, 0x31, 0x22, 0x00, 0x12, 0xb0, 0x01, 0x0a, 0x1b, 0x47,
	0x65, 0x74, 0x43, 0x6f, 0x6d, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x46, 0x6f, 0x72, 0x4d,
	0x61, 0x69, 0x6e, 0x42, 0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65, 0x12, 0x46, 0x2e, 0x70, 0x72, 0x6f,
	0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x69, 0x73, 0x73, 0x69,
	0x6f, 0x6e, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x2e, 0x47, 0x65,
	0x74, 0x43, 0x6f, 0x6d, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x46, 0x6f, 0x72, 0x4d, 0x61,
	0x69, 0x6e, 0x42, 0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x56, 0x31, 0x1a, 0x47, 0x2e, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x2e,
	0x63, 0x6f, 0x6d, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x69,
	0x73, 0x73, 0x69, 0x6f, 0x6e, 0x2e, 0x47, 0x65, 0x74, 0x43, 0x6f, 0x6d, 0x6d, 0x69, 0x73, 0x73,
	0x69, 0x6f, 0x6e, 0x46, 0x6f, 0x72, 0x4d, 0x61, 0x69, 0x6e, 0x42, 0x61, 0x6c, 0x61, 0x6e, 0x63,
	0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x56, 0x31, 0x22, 0x00, 0x12, 0xbf, 0x01,
	0x0a, 0x20, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x43, 0x6f, 0x6d, 0x6d, 0x69, 0x73, 0x73, 0x69,
	0x6f, 0x6e, 0x46, 0x6f, 0x72, 0x43, 0x72, 0x65, 0x64, 0x69, 0x74, 0x42, 0x61, 0x6c, 0x61, 0x6e,
	0x63, 0x65, 0x12, 0x4b, 0x2e, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x2e,
	0x63, 0x6f, 0x6d, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x69,
	0x73, 0x73, 0x69, 0x6f, 0x6e, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x43, 0x6f, 0x6d, 0x6d,
	0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x46, 0x6f, 0x72, 0x43, 0x72, 0x65, 0x64, 0x69, 0x74, 0x42,
	0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x56, 0x31, 0x1a,
	0x4c, 0x2e, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x2e, 0x63, 0x6f, 0x6d,
	0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x69, 0x73, 0x73, 0x69,
	0x6f, 0x6e, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x43, 0x6f, 0x6d, 0x6d, 0x69, 0x73, 0x73,
	0x69, 0x6f, 0x6e, 0x46, 0x6f, 0x72, 0x43, 0x72, 0x65, 0x64, 0x69, 0x74, 0x42, 0x61, 0x6c, 0x61,
	0x6e, 0x63, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x56, 0x31, 0x22, 0x00, 0x12,
	0xa3, 0x01, 0x0a, 0x1a, 0x43, 0x61, 0x6c, 0x63, 0x75, 0x6c, 0x61, 0x74, 0x65, 0x50, 0x61, 0x79,
	0x49, 0x6e, 0x50, 0x72, 0x69, 0x6d, 0x61, 0x6c, 0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x40,
	0x2e, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x2e, 0x63, 0x6f, 0x6d, 0x6d,
	0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f,
	0x6e, 0x2e, 0x43, 0x61, 0x6c, 0x63, 0x75, 0x6c, 0x61, 0x74, 0x65, 0x50, 0x72, 0x69, 0x6d, 0x61,
	0x6c, 0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x56, 0x31,
	0x1a, 0x41, 0x2e, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x2e, 0x63, 0x6f,
	0x6d, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x69, 0x73, 0x73,
	0x69, 0x6f, 0x6e, 0x2e, 0x43, 0x61, 0x6c, 0x63, 0x75, 0x6c, 0x61, 0x74, 0x65, 0x50, 0x72, 0x69,
	0x6d, 0x61, 0x6c, 0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x56, 0x31, 0x22, 0x00, 0x12, 0xa4, 0x01, 0x0a, 0x1b, 0x43, 0x61, 0x6c, 0x63, 0x75, 0x6c,
	0x61, 0x74, 0x65, 0x50, 0x61, 0x79, 0x4f, 0x75, 0x74, 0x50, 0x72, 0x69, 0x6d, 0x61, 0x6c, 0x41,
	0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x40, 0x2e, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69,
	0x6e, 0x67, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x2e, 0x63, 0x6f,
	0x6d, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x2e, 0x43, 0x61, 0x6c, 0x63, 0x75, 0x6c, 0x61,
	0x74, 0x65, 0x50, 0x72, 0x69, 0x6d, 0x61, 0x6c, 0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x56, 0x31, 0x1a, 0x41, 0x2e, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73,
	0x73, 0x69, 0x6e, 0x67, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x2e,
	0x63, 0x6f, 0x6d, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x2e, 0x43, 0x61, 0x6c, 0x63, 0x75,
	0x6c, 0x61, 0x74, 0x65, 0x50, 0x72, 0x69, 0x6d, 0x61, 0x6c, 0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x56, 0x31, 0x22, 0x00, 0x12, 0x79, 0x0a, 0x18,
	0x46, 0x69, 0x73, 0x63, 0x61, 0x6c, 0x69, 0x7a, 0x65, 0x55, 0x70, 0x70, 0x65, 0x72, 0x43, 0x6f,
	0x6d, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x43, 0x2e, 0x70, 0x72, 0x6f, 0x63, 0x65,
	0x73, 0x73, 0x69, 0x6e, 0x67, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e,
	0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x2e, 0x46, 0x69, 0x73, 0x63,
	0x61, 0x6c, 0x69, 0x7a, 0x65, 0x55, 0x70, 0x70, 0x65, 0x72, 0x43, 0x6f, 0x6d, 0x6d, 0x69, 0x73,
	0x73, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x56, 0x31, 0x1a, 0x16, 0x2e,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e,
	0x45, 0x6d, 0x70, 0x74, 0x79, 0x22, 0x00, 0x12, 0xb3, 0x01, 0x0a, 0x1c, 0x47, 0x65, 0x74, 0x43,
	0x6f, 0x6d, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x42, 0x79, 0x54, 0x72, 0x61, 0x6e, 0x73,
	0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x44, 0x12, 0x47, 0x2e, 0x70, 0x72, 0x6f, 0x63, 0x65,
	0x73, 0x73, 0x69, 0x6e, 0x67, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e,
	0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x2e, 0x47, 0x65, 0x74, 0x43,
	0x6f, 0x6d, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x42, 0x79, 0x54, 0x72, 0x61, 0x6e, 0x73,
	0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x44, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x56,
	0x31, 0x1a, 0x48, 0x2e, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x2e, 0x63,
	0x6f, 0x6d, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x69, 0x73,
	0x73, 0x69, 0x6f, 0x6e, 0x2e, 0x47, 0x65, 0x74, 0x43, 0x6f, 0x6d, 0x6d, 0x69, 0x73, 0x73, 0x69,
	0x6f, 0x6e, 0x42, 0x79, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x49,
	0x44, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x56, 0x31, 0x22, 0x00, 0x42, 0x2d, 0x5a,
	0x2b, 0x67, 0x69, 0x74, 0x2e, 0x6c, 0x6f, 0x63, 0x61, 0x6c, 0x2f, 0x73, 0x65, 0x6e, 0x73, 0x69,
	0x74, 0x69, 0x76, 0x65, 0x2f, 0x69, 0x6e, 0x6e, 0x65, 0x72, 0x70, 0x62, 0x2f, 0x70, 0x72, 0x6f,
	0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x2f, 0x67, 0x72, 0x70, 0x63, 0x62, 0x08, 0x65, 0x64,
	0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x70, 0xe8, 0x07,
})

var (
	file_inner_processing_grpc_commission_proto_rawDescOnce sync.Once
	file_inner_processing_grpc_commission_proto_rawDescData []byte
)

func file_inner_processing_grpc_commission_proto_rawDescGZIP() []byte {
	file_inner_processing_grpc_commission_proto_rawDescOnce.Do(func() {
		file_inner_processing_grpc_commission_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_inner_processing_grpc_commission_proto_rawDesc), len(file_inner_processing_grpc_commission_proto_rawDesc)))
	})
	return file_inner_processing_grpc_commission_proto_rawDescData
}

var file_inner_processing_grpc_commission_proto_msgTypes = make([]protoimpl.MessageInfo, 13)
var file_inner_processing_grpc_commission_proto_goTypes = []any{
	(*CalculatePrimalAmountRequestV1)(nil),             // 0: processing.commission.commission.CalculatePrimalAmountRequestV1
	(*TransactionCommissionResponseV1)(nil),            // 1: processing.commission.commission.TransactionCommissionResponseV1
	(*CalculateAndSaveUpperCommissionResponseV1)(nil),  // 2: processing.commission.commission.CalculateAndSaveUpperCommissionResponseV1
	(*CalculatePrimalAmountResponseV1)(nil),            // 3: processing.commission.commission.CalculatePrimalAmountResponseV1
	(*CalculateAndSaveUpperCommissionRequestV1)(nil),   // 4: processing.commission.commission.CalculateAndSaveUpperCommissionRequestV1
	(*UpdateCommissionForCreditBalanceRequestV1)(nil),  // 5: processing.commission.commission.UpdateCommissionForCreditBalanceRequestV1
	(*UpdateCommissionForCreditBalanceResponseV1)(nil), // 6: processing.commission.commission.UpdateCommissionForCreditBalanceResponseV1
	(*GetCommissionForMainBalanceRequestV1)(nil),       // 7: processing.commission.commission.GetCommissionForMainBalanceRequestV1
	(*GetCommissionForMainBalanceResponseV1)(nil),      // 8: processing.commission.commission.GetCommissionForMainBalanceResponseV1
	(*FiscalizeUpperCommissionRequestV1)(nil),          // 9: processing.commission.commission.FiscalizeUpperCommissionRequestV1
	(*GetCommissionByTransactionIDRequestV1)(nil),      // 10: processing.commission.commission.GetCommissionByTransactionIDRequestV1
	(*GetCommissionByTransactionIDResponseV1)(nil),     // 11: processing.commission.commission.GetCommissionByTransactionIDResponseV1
	(*CommissionV1)(nil),                               // 12: processing.commission.commission.CommissionV1
	(*timestamppb.Timestamp)(nil),                      // 13: google.protobuf.Timestamp
	(*emptypb.Empty)(nil),                              // 14: google.protobuf.Empty
}
var file_inner_processing_grpc_commission_proto_depIdxs = []int32{
	13, // 0: processing.commission.commission.CalculateAndSaveUpperCommissionRequestV1.transaction_created_at:type_name -> google.protobuf.Timestamp
	13, // 1: processing.commission.commission.UpdateCommissionForCreditBalanceRequestV1.transaction_created_at:type_name -> google.protobuf.Timestamp
	4,  // 2: processing.commission.commission.Commission.CalculateAndSaveUpperCommission:input_type -> processing.commission.commission.CalculateAndSaveUpperCommissionRequestV1
	7,  // 3: processing.commission.commission.Commission.GetCommissionForMainBalance:input_type -> processing.commission.commission.GetCommissionForMainBalanceRequestV1
	5,  // 4: processing.commission.commission.Commission.UpdateCommissionForCreditBalance:input_type -> processing.commission.commission.UpdateCommissionForCreditBalanceRequestV1
	0,  // 5: processing.commission.commission.Commission.CalculatePayInPrimalAmount:input_type -> processing.commission.commission.CalculatePrimalAmountRequestV1
	0,  // 6: processing.commission.commission.Commission.CalculatePayOutPrimalAmount:input_type -> processing.commission.commission.CalculatePrimalAmountRequestV1
	9,  // 7: processing.commission.commission.Commission.FiscalizeUpperCommission:input_type -> processing.commission.commission.FiscalizeUpperCommissionRequestV1
	10, // 8: processing.commission.commission.Commission.GetCommissionByTransactionID:input_type -> processing.commission.commission.GetCommissionByTransactionIDRequestV1
	2,  // 9: processing.commission.commission.Commission.CalculateAndSaveUpperCommission:output_type -> processing.commission.commission.CalculateAndSaveUpperCommissionResponseV1
	8,  // 10: processing.commission.commission.Commission.GetCommissionForMainBalance:output_type -> processing.commission.commission.GetCommissionForMainBalanceResponseV1
	6,  // 11: processing.commission.commission.Commission.UpdateCommissionForCreditBalance:output_type -> processing.commission.commission.UpdateCommissionForCreditBalanceResponseV1
	3,  // 12: processing.commission.commission.Commission.CalculatePayInPrimalAmount:output_type -> processing.commission.commission.CalculatePrimalAmountResponseV1
	3,  // 13: processing.commission.commission.Commission.CalculatePayOutPrimalAmount:output_type -> processing.commission.commission.CalculatePrimalAmountResponseV1
	14, // 14: processing.commission.commission.Commission.FiscalizeUpperCommission:output_type -> google.protobuf.Empty
	11, // 15: processing.commission.commission.Commission.GetCommissionByTransactionID:output_type -> processing.commission.commission.GetCommissionByTransactionIDResponseV1
	9,  // [9:16] is the sub-list for method output_type
	2,  // [2:9] is the sub-list for method input_type
	2,  // [2:2] is the sub-list for extension type_name
	2,  // [2:2] is the sub-list for extension extendee
	0,  // [0:2] is the sub-list for field type_name
}

func init() { file_inner_processing_grpc_commission_proto_init() }
func file_inner_processing_grpc_commission_proto_init() {
	if File_inner_processing_grpc_commission_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_inner_processing_grpc_commission_proto_rawDesc), len(file_inner_processing_grpc_commission_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   13,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_inner_processing_grpc_commission_proto_goTypes,
		DependencyIndexes: file_inner_processing_grpc_commission_proto_depIdxs,
		MessageInfos:      file_inner_processing_grpc_commission_proto_msgTypes,
	}.Build()
	File_inner_processing_grpc_commission_proto = out.File
	file_inner_processing_grpc_commission_proto_goTypes = nil
	file_inner_processing_grpc_commission_proto_depIdxs = nil
}
