package database

import (
	"context"
	"errors"

	"gorm.io/gorm"

	"git.local/sensitive/innerpb/processing/goerr"
	"git.local/sensitive/processing/commission/model"
	"git.local/sensitive/sdk/dog"
)

type TransactionCommissionDB struct {
	db *gorm.DB
}

func NewTransactionCommissionDB(db *gorm.DB) TransactionCommissioner {
	return TransactionCommissionDB{
		db: db,
	}
}

func (t TransactionCommissionDB) Create(
	ctx context.Context,
	transactionID, upperCommissionID uint64,
	upperCommissionAmount float64,
) (err error) {
	ctx, span := dog.CreateSpan(ctx, "TransactionCommissionDB_Create")
	defer span.End()

	var transactionCommission = model.TransactionCommission{
		TransactionID:            transactionID,
		ProjectUpperCommissionID: upperCommissionID,
		UpperCommissionAmount:    upperCommissionAmount,
	}

	if err = t.db.WithContext(ctx).Create(&transactionCommission).Error; err != nil {
		return goerr.ErrDbUnexpected.WithErr(err).WithCtx(ctx)
	}

	return nil
}

func (t TransactionCommissionDB) GetByTransactionID(
	ctx context.Context,
	transactionID uint64,
) (_ model.TransactionCommission, err error) {
	ctx, span := dog.CreateSpan(ctx, "TransactionCommissionDB_GetByTransactionID")
	defer span.End()

	transactionCommission := new(model.TransactionCommission)

	if err = t.db.WithContext(ctx).Where("transaction_id = ?", transactionID).
		First(transactionCommission).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return model.TransactionCommission{}, goerr.ErrTransactionNotFound.WithErr(err).WithCtx(ctx)
		}

		return model.TransactionCommission{}, goerr.ErrDbUnexpected.WithErr(err).WithCtx(ctx)
	}

	return *transactionCommission, nil
}

func (t TransactionCommissionDB) UpdateByTransactionID(
	ctx context.Context,
	transactionID uint64,
	transactionComm model.TransactionCommission,
) (err error) {
	ctx, span := dog.CreateSpan(ctx, "TransactionCommissionDB_UpdateByTransactionID")
	defer span.End()

	if err = t.db.WithContext(ctx).
		Model(&model.TransactionCommission{}).
		Where(`transaction_id = ?`, transactionID).
		Updates(&transactionComm).Error; err != nil {
		return goerr.ErrDbUnexpected.WithErr(err).WithCtx(ctx)
	}

	return nil
}
