// Code generated by MockGen. DO NOT EDIT.
// Source: transaction_info.gin.pb.go

// Package grpcmock is a generated GoMock package.
package grpcmock

import (
	reflect "reflect"

	gin "github.com/gin-gonic/gin"
	gomock "github.com/golang/mock/gomock"
)

// MockGinTransactionInfServer is a mock of GinTransactionInfServer interface.
type MockGinTransactionInfServer struct {
	ctrl     *gomock.Controller
	recorder *MockGinTransactionInfServerMockRecorder
}

// MockGinTransactionInfServerMockRecorder is the mock recorder for MockGinTransactionInfServer.
type MockGinTransactionInfServerMockRecorder struct {
	mock *MockGinTransactionInfServer
}

// NewMockGinTransactionInfServer creates a new mock instance.
func NewMockGinTransactionInfServer(ctrl *gomock.Controller) *MockGinTransactionInfServer {
	mock := &MockGinTransactionInfServer{ctrl: ctrl}
	mock.recorder = &MockGinTransactionInfServerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockGinTransactionInfServer) EXPECT() *MockGinTransactionInfServerMockRecorder {
	return m.recorder
}

// GetTransactionsWithEmptyBankReferenceID mocks base method.
func (m *MockGinTransactionInfServer) GetTransactionsWithEmptyBankReferenceID(c *gin.Context) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetTransactionsWithEmptyBankReferenceID", c)
	ret0, _ := ret[0].(error)
	return ret0
}

// GetTransactionsWithEmptyBankReferenceID indicates an expected call of GetTransactionsWithEmptyBankReferenceID.
func (mr *MockGinTransactionInfServerMockRecorder) GetTransactionsWithEmptyBankReferenceID(c interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetTransactionsWithEmptyBankReferenceID", reflect.TypeOf((*MockGinTransactionInfServer)(nil).GetTransactionsWithEmptyBankReferenceID), c)
}

// UpdateBankReferenceID mocks base method.
func (m *MockGinTransactionInfServer) UpdateBankReferenceID(c *gin.Context) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateBankReferenceID", c)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpdateBankReferenceID indicates an expected call of UpdateBankReferenceID.
func (mr *MockGinTransactionInfServerMockRecorder) UpdateBankReferenceID(c interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateBankReferenceID", reflect.TypeOf((*MockGinTransactionInfServer)(nil).UpdateBankReferenceID), c)
}

// UpdateBankResponseMessage mocks base method.
func (m *MockGinTransactionInfServer) UpdateBankResponseMessage(c *gin.Context) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateBankResponseMessage", c)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpdateBankResponseMessage indicates an expected call of UpdateBankResponseMessage.
func (mr *MockGinTransactionInfServerMockRecorder) UpdateBankResponseMessage(c interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateBankResponseMessage", reflect.TypeOf((*MockGinTransactionInfServer)(nil).UpdateBankResponseMessage), c)
}

// UpdateJobsMessage mocks base method.
func (m *MockGinTransactionInfServer) UpdateJobsMessage(c *gin.Context) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateJobsMessage", c)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpdateJobsMessage indicates an expected call of UpdateJobsMessage.
func (mr *MockGinTransactionInfServerMockRecorder) UpdateJobsMessage(c interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateJobsMessage", reflect.TypeOf((*MockGinTransactionInfServer)(nil).UpdateJobsMessage), c)
}
