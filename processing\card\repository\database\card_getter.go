package database

import (
	"context"
	"git.local/sensitive/innerpb/processing/goerr"
	"git.local/sensitive/pkg/middlewares"
	"git.local/sensitive/processing/card/model"
	"git.local/sensitive/sdk/dog"
	"gorm.io/gorm"
)

type CardGetterDB struct {
	db *gorm.DB
}

func NewCardGetterDB(db *gorm.DB) CardGetter {
	return &CardGetterDB{
		db: db,
	}
}

func (c CardGetterDB) GetActiveCardsByClient(ctx context.Context, id uint64) (cards model.Cards, err error) {
	ctx, span := dog.CreateSpan(ctx, "CardGetterDB_GetActiveCardsByClient")
	defer span.End()

	if err = c.db.WithContext(ctx).
		Where("client_id = ?", id).
		Where("approved = ?", true).
		Preload("Tokens").
		Find(&cards).
		Error; err != nil {
		return nil, goerr.ErrDbUnexpected.WithErr(err).WithCtx(ctx)
	}

	return cards, nil
}

func (c CardGetterDB) GetByClient(
	ctx context.Context,
	id uint64,
	pagination *middlewares.PaginationInfo,
) (cards []model.Card, err error) {
	query := c.db.WithContext(ctx).
		Model(&model.Card{}).
		Where("client_id = ?", id).
		Where("save_access = ?", true)

	if pagination.Pagination {
		query = query.Offset((pagination.Page - 1) * pagination.PerPage).Limit(pagination.PerPage)
	}

	if err = query.Find(&cards).Error; err != nil {
		return nil, goerr.ErrDbUnexpected.WithErr(err).WithCtx(ctx)
	}

	return cards, nil
}
