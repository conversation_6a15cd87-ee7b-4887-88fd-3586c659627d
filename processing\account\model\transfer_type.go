package model

type TransferTypeCode string

const (
	TypeIn  TransferTypeCode = "in"
	TypeOut TransferTypeCode = "out"
)

type TransferType struct {
	TimestampMixin
	ID   uint64           `gorm:"primaryKey;column:id" json:"id"`
	Code TransferTypeCode `gorm:"column:code" json:"code"`
	Name string           `gorm:"column:name" json:"name"`
}

func (tt TransferType) TableName() string {
	return "account.transfer_types"
}
