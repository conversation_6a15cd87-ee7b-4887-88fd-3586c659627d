// Code generated by protoc-gen-go-mvp. DO NOT EDIT.
// versions:
// - protoc-gen-go-mvp v2.0.1
// created by mvp authors

package grpc

import (
	context "context"
	grpc "google.golang.org/grpc"
	metadata "google.golang.org/grpc/metadata"
)

func NewIamNotificationServer(
	srv NotificationServer,
) NotificationServer {
	return &iamNotificationServer{
		srv: srv,
	}
}

var _ NotificationServer = (*iamNotificationServer)(nil)

type iamNotificationServer struct {
	UnimplementedNotificationServer

	srv NotificationServer
}

func (s *iamNotificationServer) GetLastMailByUserEmail(
	ctx context.Context,
	req *GetMailReqDataV1,
) (
	*GetMailResDataV1,
	error,
) {
	return s.srv.GetLastMailByUserEmail(ctx, req)
}

func (s *iamNotificationServer) GetLastSMSByUserPhone(
	ctx context.Context,
	req *GetSMSReqDataV1,
) (
	*GetSMSResDataV1,
	error,
) {
	return s.srv.GetLastSMSByUserPhone(ctx, req)
}

func NewIamNotificationClient(
	client NotificationClient,
) NotificationClient {
	return &iamNotificationClient{
		client: client,
	}
}

type iamNotificationClient struct {
	client NotificationClient
}

func (s *iamNotificationClient) GetLastMailByUserEmail(
	ctx context.Context,
	req *GetMailReqDataV1,
	opts ...grpc.CallOption,
) (
	*GetMailResDataV1,
	error,
) {
	md, _ := metadata.FromIncomingContext(ctx)
	return s.client.GetLastMailByUserEmail(metadata.NewOutgoingContext(ctx, md), req)
}

func (s *iamNotificationClient) GetLastSMSByUserPhone(
	ctx context.Context,
	req *GetSMSReqDataV1,
	opts ...grpc.CallOption,
) (
	*GetSMSResDataV1,
	error,
) {
	md, _ := metadata.FromIncomingContext(ctx)
	return s.client.GetLastSMSByUserPhone(metadata.NewOutgoingContext(ctx, md), req)
}
