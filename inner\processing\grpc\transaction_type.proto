edition = "2023";

package processing.transaction.transaction_type;

option go_package = "git.local/sensitive/innerpb/processing/grpc";

import "google/protobuf/empty.proto";
import "google/protobuf/descriptor.proto";
import "mvp/proto/refs.proto";

service TransactionType {
  rpc GetAll(google.protobuf.Empty) returns (TransactionTypeResponseV1) {}
  rpc GetTransactionPayOutTypes(google.protobuf.Empty) returns (TransactionTypeResponseV1) {}
  rpc GetTransactionPayInTypes(google.protobuf.Empty) returns (TransactionTypeResponseV1) {}
  rpc GetAggregatedTransactionType(GetAggregatedTransactionTypeRequestV1) returns (GetAggregatedTransactionTypeResponseV1) {}
  rpc GetAggregatedTypeByID(GetAggregatedTypeByIDRequestV1) returns (GetAggregatedTypeByIDResponseV1) {}
  rpc GetAggregatedTransactionTypeByTypeID(GetAggregatedTransactionTypeByTypeIDRequest) returns (GetAggregatedTransactionTypeByTypeIDResponse) {}
}

message TransactionTypeRef {
  string name = 2;
  string code = 3;
}

extend google.protobuf.EnumValueOptions {
  TransactionTypeRef transaction_type_value = 200002;
}

extend google.protobuf.EnumOptions {
  TransactionTypeRef default_transaction_type_value = 200003;
}

enum EnumTransactionType {
  option(mvp.default_ref) = "default_transaction_type_value";
  option(mvp.ref) = "transaction_type_value";
  option(default_transaction_type_value) = {
    code: "Unknown",
    name: "Unknown",
  };
  Unknown = 0 [(transaction_type_value) = {
    code: "Unknown",
    name: "Unknown",
  }];
  TransactionTypePayIn = 1 [(transaction_type_value) = {
    code: "in",
    name: "Приём",
  }];
  TransactionTypePayOut = 2 [(transaction_type_value) = {
    code: "out",
    name: "Вывод",
  }];
  TransactionTypeOneClickPayIn = 3 [(transaction_type_value) = {
    code: "one_click_pay_in",
    name: "Рекуррентный приём",
  }];
  TransactionTypeOneClickPayOut = 4 [(transaction_type_value) = {
    code: "one_click_pay_out",
    name: "Рекуррентный вывод",
  }];
  TransactionTypeApplePay = 5 [(transaction_type_value) = {
    code: "apple_pay",
    name: "Apple Pay",
  }];
  TransactionTypeCardLink = 6 [(transaction_type_value) = {
    code: "card_link",
    name: "Привязка карты",
  }];
  TransactionTypeRefund = 7 [(transaction_type_value) = {
    code: "refund",
    name: "Возврат",
  }];
  TransactionTypeGooglePay = 8 [(transaction_type_value) = {
    code: "google_pay",
    name: "Гугл Пэй",
  }];
  TransactionTypeTwoStagePayIn = 9 [(transaction_type_value) = {
    code: "two_stage_pay_in",
    name: "Двустадийный прием",
  }];
}

message TransactionTypeResponseV1 {
  repeated TransactionTypesResponseV1 data = 1;
}

message TransactionTypesResponseV1 {
  uint64 id = 1;
  string name = 2;
  string code = 3;
}

message GetAggregatedTransactionTypeRequestV1 {
  uint64 transaction_type_id = 1;
  uint64 aggregated_type_id = 2;
}

message GetAggregatedTransactionTypeResponseV1 {
  uint64 transaction_type_id = 1;
  uint64 aggregated_type_id = 2;
  string aggregated_type_code = 3;
}

message GetAggregatedTypeByIDRequestV1 {
  uint64 aggregated_type_id = 1;
}

message GetAggregatedTypeByIDResponseV1 {
  uint64 id = 1;
  string name = 2;
  string code = 3;
  string description = 4;
}

message GetAggregatedTransactionTypeByTypeIDRequest {
  uint64 transaction_type_id = 1;
}

message GetAggregatedTransactionTypeByTypeIDResponse {
  uint64 aggregated_type_id = 1;
  uint64 transaction_type_id = 2;
}