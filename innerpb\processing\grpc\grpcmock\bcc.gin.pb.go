// Code generated by MockGen. DO NOT EDIT.
// Source: bcc.gin.pb.go

// Package grpcmock is a generated GoMock package.
package grpcmock

import (
	reflect "reflect"

	gin "github.com/gin-gonic/gin"
	gomock "github.com/golang/mock/gomock"
)

// MockGinBCCServer is a mock of GinBCCServer interface.
type MockGinBCCServer struct {
	ctrl     *gomock.Controller
	recorder *MockGinBCCServerMockRecorder
}

// MockGinBCCServerMockRecorder is the mock recorder for MockGinBCCServer.
type MockGinBCCServerMockRecorder struct {
	mock *MockGinBCCServer
}

// NewMockGinBCCServer creates a new mock instance.
func NewMockGinBCCServer(ctrl *gomock.Controller) *MockGinBCCServer {
	mock := &MockGinBCCServer{ctrl: ctrl}
	mock.recorder = &MockGinBCCServerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockGinBCCServer) EXPECT() *MockGinBCCServerMockRecorder {
	return m.recorder
}

// ApplePay mocks base method.
func (m *MockGinBCCServer) ApplePay(c *gin.Context) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ApplePay", c)
	ret0, _ := ret[0].(error)
	return ret0
}

// ApplePay indicates an expected call of ApplePay.
func (mr *MockGinBCCServerMockRecorder) ApplePay(c interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ApplePay", reflect.TypeOf((*MockGinBCCServer)(nil).ApplePay), c)
}

// Cancel mocks base method.
func (m *MockGinBCCServer) Cancel(c *gin.Context) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Cancel", c)
	ret0, _ := ret[0].(error)
	return ret0
}

// Cancel indicates an expected call of Cancel.
func (mr *MockGinBCCServerMockRecorder) Cancel(c interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Cancel", reflect.TypeOf((*MockGinBCCServer)(nil).Cancel), c)
}

// Charge mocks base method.
func (m *MockGinBCCServer) Charge(c *gin.Context) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Charge", c)
	ret0, _ := ret[0].(error)
	return ret0
}

// Charge indicates an expected call of Charge.
func (mr *MockGinBCCServerMockRecorder) Charge(c interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Charge", reflect.TypeOf((*MockGinBCCServer)(nil).Charge), c)
}

// GetAcquirerIdentifier mocks base method.
func (m *MockGinBCCServer) GetAcquirerIdentifier(c *gin.Context) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAcquirerIdentifier", c)
	ret0, _ := ret[0].(error)
	return ret0
}

// GetAcquirerIdentifier indicates an expected call of GetAcquirerIdentifier.
func (mr *MockGinBCCServerMockRecorder) GetAcquirerIdentifier(c interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAcquirerIdentifier", reflect.TypeOf((*MockGinBCCServer)(nil).GetAcquirerIdentifier), c)
}

// GetBankTransactionStatus mocks base method.
func (m *MockGinBCCServer) GetBankTransactionStatus(c *gin.Context) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetBankTransactionStatus", c)
	ret0, _ := ret[0].(error)
	return ret0
}

// GetBankTransactionStatus indicates an expected call of GetBankTransactionStatus.
func (mr *MockGinBCCServerMockRecorder) GetBankTransactionStatus(c interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetBankTransactionStatus", reflect.TypeOf((*MockGinBCCServer)(nil).GetBankTransactionStatus), c)
}

// GetBankTransactionStatusUnformated mocks base method.
func (m *MockGinBCCServer) GetBankTransactionStatusUnformated(c *gin.Context) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetBankTransactionStatusUnformated", c)
	ret0, _ := ret[0].(error)
	return ret0
}

// GetBankTransactionStatusUnformated indicates an expected call of GetBankTransactionStatusUnformated.
func (mr *MockGinBCCServerMockRecorder) GetBankTransactionStatusUnformated(c interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetBankTransactionStatusUnformated", reflect.TypeOf((*MockGinBCCServer)(nil).GetBankTransactionStatusUnformated), c)
}

// GooglePay mocks base method.
func (m *MockGinBCCServer) GooglePay(c *gin.Context) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GooglePay", c)
	ret0, _ := ret[0].(error)
	return ret0
}

// GooglePay indicates an expected call of GooglePay.
func (mr *MockGinBCCServerMockRecorder) GooglePay(c interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GooglePay", reflect.TypeOf((*MockGinBCCServer)(nil).GooglePay), c)
}

// MakeToken mocks base method.
func (m *MockGinBCCServer) MakeToken(c *gin.Context) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "MakeToken", c)
	ret0, _ := ret[0].(error)
	return ret0
}

// MakeToken indicates an expected call of MakeToken.
func (mr *MockGinBCCServerMockRecorder) MakeToken(c interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "MakeToken", reflect.TypeOf((*MockGinBCCServer)(nil).MakeToken), c)
}

// OneClickPayIn mocks base method.
func (m *MockGinBCCServer) OneClickPayIn(c *gin.Context) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "OneClickPayIn", c)
	ret0, _ := ret[0].(error)
	return ret0
}

// OneClickPayIn indicates an expected call of OneClickPayIn.
func (mr *MockGinBCCServerMockRecorder) OneClickPayIn(c interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "OneClickPayIn", reflect.TypeOf((*MockGinBCCServer)(nil).OneClickPayIn), c)
}

// PayIn mocks base method.
func (m *MockGinBCCServer) PayIn(c *gin.Context) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "PayIn", c)
	ret0, _ := ret[0].(error)
	return ret0
}

// PayIn indicates an expected call of PayIn.
func (mr *MockGinBCCServerMockRecorder) PayIn(c interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "PayIn", reflect.TypeOf((*MockGinBCCServer)(nil).PayIn), c)
}

// PayOut mocks base method.
func (m *MockGinBCCServer) PayOut(c *gin.Context) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "PayOut", c)
	ret0, _ := ret[0].(error)
	return ret0
}

// PayOut indicates an expected call of PayOut.
func (mr *MockGinBCCServerMockRecorder) PayOut(c interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "PayOut", reflect.TypeOf((*MockGinBCCServer)(nil).PayOut), c)
}

// PayOutByPhone mocks base method.
func (m *MockGinBCCServer) PayOutByPhone(c *gin.Context) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "PayOutByPhone", c)
	ret0, _ := ret[0].(error)
	return ret0
}

// PayOutByPhone indicates an expected call of PayOutByPhone.
func (mr *MockGinBCCServerMockRecorder) PayOutByPhone(c interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "PayOutByPhone", reflect.TypeOf((*MockGinBCCServer)(nil).PayOutByPhone), c)
}

// Refund mocks base method.
func (m *MockGinBCCServer) Refund(c *gin.Context) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Refund", c)
	ret0, _ := ret[0].(error)
	return ret0
}

// Refund indicates an expected call of Refund.
func (mr *MockGinBCCServerMockRecorder) Refund(c interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Refund", reflect.TypeOf((*MockGinBCCServer)(nil).Refund), c)
}

// ResolveVisaAlias mocks base method.
func (m *MockGinBCCServer) ResolveVisaAlias(c *gin.Context) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ResolveVisaAlias", c)
	ret0, _ := ret[0].(error)
	return ret0
}

// ResolveVisaAlias indicates an expected call of ResolveVisaAlias.
func (mr *MockGinBCCServerMockRecorder) ResolveVisaAlias(c interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ResolveVisaAlias", reflect.TypeOf((*MockGinBCCServer)(nil).ResolveVisaAlias), c)
}

// ThreeDSConfirm mocks base method.
func (m *MockGinBCCServer) ThreeDSConfirm(c *gin.Context) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ThreeDSConfirm", c)
	ret0, _ := ret[0].(error)
	return ret0
}

// ThreeDSConfirm indicates an expected call of ThreeDSConfirm.
func (mr *MockGinBCCServerMockRecorder) ThreeDSConfirm(c interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ThreeDSConfirm", reflect.TypeOf((*MockGinBCCServer)(nil).ThreeDSConfirm), c)
}

// ThreeDSResume mocks base method.
func (m *MockGinBCCServer) ThreeDSResume(c *gin.Context) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ThreeDSResume", c)
	ret0, _ := ret[0].(error)
	return ret0
}

// ThreeDSResume indicates an expected call of ThreeDSResume.
func (mr *MockGinBCCServerMockRecorder) ThreeDSResume(c interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ThreeDSResume", reflect.TypeOf((*MockGinBCCServer)(nil).ThreeDSResume), c)
}

// TwoStagePayIn mocks base method.
func (m *MockGinBCCServer) TwoStagePayIn(c *gin.Context) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "TwoStagePayIn", c)
	ret0, _ := ret[0].(error)
	return ret0
}

// TwoStagePayIn indicates an expected call of TwoStagePayIn.
func (mr *MockGinBCCServerMockRecorder) TwoStagePayIn(c interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "TwoStagePayIn", reflect.TypeOf((*MockGinBCCServer)(nil).TwoStagePayIn), c)
}
