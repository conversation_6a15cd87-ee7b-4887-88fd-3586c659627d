// Code generated by protoc-gen-go-mvp. DO NOT EDIT.
// versions:
// - protoc-gen-go-mvp v2.0.1
// created by mvp authors

package grpc

import (
	dog "git.local/sensitive/sdk/dog"
	gin "github.com/gin-gonic/gin"
	metadata "google.golang.org/grpc/metadata"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
	http "net/http"
)

func RegisterGinTransactionCallbackRouterGroup(
	in *gin.RouterGroup,
	handler func(func(c *gin.Context) error) gin.HandlerFunc,
) error {
	service, err := NewGinTransactionCallbackService()
	if err != nil {
		return err
	}

	routerGroup := in.Group("processing.transaction.transaction_callback.TransactionCallback")
	routerGroup.PUT("/SendCallback", handler(service.SendCallback))
	return nil
}

func NewGinTransactionCallbackService() (GinTransactionCallbackServer, error) {
	client, err := NewPreparedTransactionCallbackClient()
	if err != nil {
		return nil, err
	}

	return &ginTransactionCallbackServer{
		client: NewLoggedTransactionCallbackClient(
			NewIamTransactionCallbackClient(client),
		),
	}, nil
}

//go:generate mockgen -destination=./grpcmock/transaction_callback.gin.pb.go -package=grpcmock -source=transaction_callback.gin.pb.go GinTransactionCallbackServer
type GinTransactionCallbackServer interface {
	SendCallback(c *gin.Context) error
}

var _ GinTransactionCallbackServer = (*ginTransactionCallbackServer)(nil)

type ginTransactionCallbackServer struct {
	client TransactionCallbackClient
}

type TransactionCallback_SendCallback_Success struct {
	// Status is the status of the response. Set true
	Status bool `json:"status"`
	// Message is a human-readable explanation of the status
	Message string `json:"message"`
	// StatusCode is the GoErr status. Set 0
	StatusCode int `json:"status_code"`
	// Result is the result of the request
	Result *emptypb.Empty `json:"result"`
}

type TransactionCallback_SendCallback_Failure struct {
	// Status is the status of the response. Set false
	Status bool `json:"status"`
	// Message is a human-readable explanation of the status
	Message string `json:"message"`
	// StatusCode is the GoErr status
	StatusCode int `json:"status_code"`
}

// SendCallback
// @Summary SendCallback
// @Security bearerAuth
// @ID TransactionCallback_SendCallback
// @Accept json
// @Param request body TransactionSendCallbackRequestV1 true "TransactionSendCallbackRequestV1"
// @Success 200 {object} TransactionCallback_SendCallback_Success
// @Failure 401 {object} TransactionCallback_SendCallback_Failure "goerrpkg.ErrorTypeAuthorization"
// @Failure 404 {object} TransactionCallback_SendCallback_Failure "goerrpkg.ErrorTypeNotFound"
// @Failure 400 {object} TransactionCallback_SendCallback_Failure "goerrpkg.ErrorTypeBadRequest"
// @Failure 500 {object} TransactionCallback_SendCallback_Failure "goerrpkg.ErrorTypeUnexpectedError"
// @Failure 400 {object} TransactionCallback_SendCallback_Failure "gin.ErrorTypeBind"
// @Failure 500 {object} TransactionCallback_SendCallback_Failure "Undefined error"
// @Produce json
// @Router /processing.transaction.transaction_callback.TransactionCallback/SendCallback [put]
// @tags TransactionCallback
func (s *ginTransactionCallbackServer) SendCallback(c *gin.Context) error {
	ctx, span := dog.CreateSpan(c.Request.Context(), "GinTransactionCallbackServer_SendCallback")
	defer span.End()

	var request TransactionSendCallbackRequestV1
	if err := c.ShouldBindJSON(&request); err != nil {
		return err
	}

	ctx = metadata.NewOutgoingContext(ctx, metadata.MD(c.Request.Header))

	response, err := s.client.SendCallback(ctx, &request)
	if err != nil {
		return err
	}

	c.JSON(http.StatusOK, &TransactionCallback_SendCallback_Success{
		Status:     true,
		Message:    "Success",
		StatusCode: 0,
		Result:     response,
	})
	return nil
}
