// Code generated by protoc-gen-go-mvp. DO NOT EDIT.
// versions:
// - protoc-gen-go-mvp v2.0.1
// created by mvp authors

package grpc

import (
	math "math"
	strings "strings"
)

func (val EnumMagnetiqStatus) Synonym() EnumMagnetiqStatus {
	if _, ok := EnumMagnetiqStatus_name[int32(val)]; ok {
		return val
	}

	return EnumMagnetiqStatus(math.MinInt32)
}

func (val EnumMagnetiqStatus) Int() int {
	return int(val.Synonym())
}

func (val EnumMagnetiqStatus) IntPtr() *int {
	result := int(val.Synonym())
	return &result
}

func (val EnumMagnetiqStatus) Int32() int32 {
	return int32(val.Synonym())
}

func (val EnumMagnetiqStatus) Int32Ptr() *int32 {
	result := int32(val.Synonym())
	return &result
}

func (val EnumMagnetiqStatus) Int64() int64 {
	return int64(val.Synonym())
}

func (val EnumMagnetiqStatus) Int64Ptr() *int64 {
	result := int64(val.Synonym())
	return &result
}

func (val EnumMagnetiqStatus) Uint() uint {
	return uint(val.Synonym())
}

func (val EnumMagnetiqStatus) UintPtr() *uint {
	result := uint(val.Synonym())
	return &result
}

func (val EnumMagnetiqStatus) Uint32() uint32 {
	return uint32(val.Synonym())
}

func (val EnumMagnetiqStatus) Uint32Ptr() *uint32 {
	result := uint32(val.Synonym())
	return &result
}

func (val EnumMagnetiqStatus) Uint64() uint64 {
	return uint64(val.Synonym())
}

func (val EnumMagnetiqStatus) Uint64Ptr() *uint64 {
	result := uint64(val.Synonym())
	return &result
}

func (val EnumMagnetiqStatus) IsKnown() bool {
	return val.Synonym() != EnumMagnetiqStatus(math.MinInt32)
}

func ConvertIntToEnumMagnetiqStatus(in int) EnumMagnetiqStatus {
	return EnumMagnetiqStatus(in).Synonym()
}

func ConvertUintToEnumMagnetiqStatus(in uint) EnumMagnetiqStatus {
	return EnumMagnetiqStatus(in).Synonym()
}

func ConvertInt32ToEnumMagnetiqStatus(in int32) EnumMagnetiqStatus {
	return EnumMagnetiqStatus(in).Synonym()
}

func ConvertUint32ToEnumMagnetiqStatus(in uint32) EnumMagnetiqStatus {
	return EnumMagnetiqStatus(in).Synonym()
}

func ConvertInt64ToEnumMagnetiqStatus(in int64) EnumMagnetiqStatus {
	return EnumMagnetiqStatus(in).Synonym()
}

func ConvertUint64ToEnumMagnetiqStatus(in uint64) EnumMagnetiqStatus {
	return EnumMagnetiqStatus(in).Synonym()
}

var EnumMagnetiqStatus_Lower_value = map[string]EnumMagnetiqStatus{
	"1": 0,
	"2": 1,
	"3": 2,
	"4": 3,
	"5": 4,
	"6": 5,
	"7": 6,
	"8": 7,
}

func ConvertStringToEnumMagnetiqStatus(in string) EnumMagnetiqStatus {
	if result, ok := EnumMagnetiqStatus_value[in]; ok {
		return EnumMagnetiqStatus(result)
	}

	if result, ok := EnumMagnetiqStatus_Lower_value[strings.ToLower(in)]; ok {
		return EnumMagnetiqStatus(result)
	}

	return EnumMagnetiqStatus(math.MinInt32)
}

var SliceEnumMagnetiqStatusConvert *sliceEnumMagnetiqStatusConvert

type sliceEnumMagnetiqStatusConvert struct{}

func (*sliceEnumMagnetiqStatusConvert) Synonym(in []EnumMagnetiqStatus) []EnumMagnetiqStatus {
	result := make([]EnumMagnetiqStatus, len(in))
	for i, v := range in {
		result[i] = v.Synonym()
	}

	return result
}

func (*sliceEnumMagnetiqStatusConvert) Int32(in []EnumMagnetiqStatus) []int32 {
	result := make([]int32, len(in))
	for i, v := range in {
		result[i] = v.Int32()
	}

	return result
}

func (*sliceEnumMagnetiqStatusConvert) Uint32(in []EnumMagnetiqStatus) []uint32 {
	result := make([]uint32, len(in))
	for i, v := range in {
		result[i] = v.Uint32()
	}

	return result
}

func (*sliceEnumMagnetiqStatusConvert) Uint64(in []EnumMagnetiqStatus) []uint64 {
	result := make([]uint64, len(in))
	for i, v := range in {
		result[i] = v.Uint64()
	}

	return result
}

func (*sliceEnumMagnetiqStatusConvert) Int64(in []EnumMagnetiqStatus) []int64 {
	result := make([]int64, len(in))
	for i, v := range in {
		result[i] = v.Int64()
	}

	return result
}

func (*sliceEnumMagnetiqStatusConvert) Uint(in []EnumMagnetiqStatus) []uint {
	result := make([]uint, len(in))
	for i, v := range in {
		result[i] = v.Uint()
	}

	return result
}

func (*sliceEnumMagnetiqStatusConvert) Int(in []EnumMagnetiqStatus) []int {
	result := make([]int, len(in))
	for i, v := range in {
		result[i] = v.Int()
	}

	return result
}

func (*sliceEnumMagnetiqStatusConvert) IsKnown(in []EnumMagnetiqStatus) []bool {
	result := make([]bool, len(in))
	for i, v := range in {
		result[i] = v.IsKnown()
	}

	return result
}

func (*sliceEnumMagnetiqStatusConvert) ConvertIntToEnumMagnetiqStatus(in []int) []EnumMagnetiqStatus {
	result := make([]EnumMagnetiqStatus, len(in))
	for i, v := range in {
		result[i] = ConvertIntToEnumMagnetiqStatus(v)
	}

	return result
}

func (*sliceEnumMagnetiqStatusConvert) ConvertUintToEnumMagnetiqStatus(in []uint) []EnumMagnetiqStatus {
	result := make([]EnumMagnetiqStatus, len(in))
	for i, v := range in {
		result[i] = ConvertUintToEnumMagnetiqStatus(v)
	}

	return result
}

func (*sliceEnumMagnetiqStatusConvert) ConvertInt32ToEnumMagnetiqStatus(in []int32) []EnumMagnetiqStatus {
	result := make([]EnumMagnetiqStatus, len(in))
	for i, v := range in {
		result[i] = ConvertInt32ToEnumMagnetiqStatus(v)
	}

	return result
}

func (*sliceEnumMagnetiqStatusConvert) ConvertUint32ToEnumMagnetiqStatus(in []uint32) []EnumMagnetiqStatus {
	result := make([]EnumMagnetiqStatus, len(in))
	for i, v := range in {
		result[i] = ConvertUint32ToEnumMagnetiqStatus(v)
	}

	return result
}

func (*sliceEnumMagnetiqStatusConvert) ConvertInt64ToEnumMagnetiqStatus(in []int64) []EnumMagnetiqStatus {
	result := make([]EnumMagnetiqStatus, len(in))
	for i, v := range in {
		result[i] = ConvertInt64ToEnumMagnetiqStatus(v)
	}

	return result
}

func (*sliceEnumMagnetiqStatusConvert) ConvertUint64ToEnumMagnetiqStatus(in []uint64) []EnumMagnetiqStatus {
	result := make([]EnumMagnetiqStatus, len(in))
	for i, v := range in {
		result[i] = ConvertUint64ToEnumMagnetiqStatus(v)
	}

	return result
}

func (*sliceEnumMagnetiqStatusConvert) ConvertStringToEnumMagnetiqStatus(in []string) []EnumMagnetiqStatus {
	result := make([]EnumMagnetiqStatus, len(in))
	for i, v := range in {
		result[i] = ConvertStringToEnumMagnetiqStatus(v)
	}

	return result
}

func NewEnumMagnetiqStatusUsage() *EnumMagnetiqStatusUsage {
	return &EnumMagnetiqStatusUsage{
		enumMap: map[EnumMagnetiqStatus]bool{
			EnumMagnetiqStatus_MagnetiqStatusRequested:  false,
			EnumMagnetiqStatus_MagnetiqStatusDeclined:   false,
			EnumMagnetiqStatus_MagnetiqStatusApproved:   false,
			EnumMagnetiqStatus_MagnetiqStatusUnapproved: false,
			EnumMagnetiqStatus_MagnetiqStatusCancelled:  false,
			EnumMagnetiqStatus_MagnetiqStatusDeposited:  false,
			EnumMagnetiqStatus_MagnetiqStatusProcessed:  false,
			EnumMagnetiqStatus_MagnetiqStatusReversed:   false,
		},
	}
}

func IsEnumMagnetiqStatus(target EnumMagnetiqStatus, matches ...EnumMagnetiqStatus) bool {
	for _, v := range matches {
		if target == v {
			return true
		}
	}

	return false
}

type EnumMagnetiqStatusUsage struct {
	enumMap map[EnumMagnetiqStatus]bool
}

func (u *EnumMagnetiqStatusUsage) Use(slice ...EnumMagnetiqStatus) *EnumMagnetiqStatusUsage {
	for _, in := range slice {
		u.enumMap[in] = true
	}

	return u
}

func (u *EnumMagnetiqStatusUsage) Check() []string {
	var result []string
	for k, v := range u.enumMap {
		if v {
			continue
		}

		result = append(result, k.String())
	}

	return result
}

func (u *EnumMagnetiqStatusUsage) EnumMagnetiqStatus_MagnetiqStatusRequested() EnumMagnetiqStatus {
	u.Use(EnumMagnetiqStatus_MagnetiqStatusRequested)
	return EnumMagnetiqStatus_MagnetiqStatusRequested
}

func (u *EnumMagnetiqStatusUsage) EnumMagnetiqStatus_MagnetiqStatusDeclined() EnumMagnetiqStatus {
	u.Use(EnumMagnetiqStatus_MagnetiqStatusDeclined)
	return EnumMagnetiqStatus_MagnetiqStatusDeclined
}

func (u *EnumMagnetiqStatusUsage) EnumMagnetiqStatus_MagnetiqStatusApproved() EnumMagnetiqStatus {
	u.Use(EnumMagnetiqStatus_MagnetiqStatusApproved)
	return EnumMagnetiqStatus_MagnetiqStatusApproved
}

func (u *EnumMagnetiqStatusUsage) EnumMagnetiqStatus_MagnetiqStatusUnapproved() EnumMagnetiqStatus {
	u.Use(EnumMagnetiqStatus_MagnetiqStatusUnapproved)
	return EnumMagnetiqStatus_MagnetiqStatusUnapproved
}

func (u *EnumMagnetiqStatusUsage) EnumMagnetiqStatus_MagnetiqStatusCancelled() EnumMagnetiqStatus {
	u.Use(EnumMagnetiqStatus_MagnetiqStatusCancelled)
	return EnumMagnetiqStatus_MagnetiqStatusCancelled
}

func (u *EnumMagnetiqStatusUsage) EnumMagnetiqStatus_MagnetiqStatusDeposited() EnumMagnetiqStatus {
	u.Use(EnumMagnetiqStatus_MagnetiqStatusDeposited)
	return EnumMagnetiqStatus_MagnetiqStatusDeposited
}

func (u *EnumMagnetiqStatusUsage) EnumMagnetiqStatus_MagnetiqStatusProcessed() EnumMagnetiqStatus {
	u.Use(EnumMagnetiqStatus_MagnetiqStatusProcessed)
	return EnumMagnetiqStatus_MagnetiqStatusProcessed
}

func (u *EnumMagnetiqStatusUsage) EnumMagnetiqStatus_MagnetiqStatusReversed() EnumMagnetiqStatus {
	u.Use(EnumMagnetiqStatus_MagnetiqStatusReversed)
	return EnumMagnetiqStatus_MagnetiqStatusReversed
}

func (val MagnetiqFaultErrCode) Synonym() MagnetiqFaultErrCode {
	if _, ok := MagnetiqFaultErrCode_name[int32(val)]; ok {
		return val
	}

	return MagnetiqFaultErrCode(math.MinInt32)
}

func (val MagnetiqFaultErrCode) Int() int {
	return int(val.Synonym())
}

func (val MagnetiqFaultErrCode) IntPtr() *int {
	result := int(val.Synonym())
	return &result
}

func (val MagnetiqFaultErrCode) Int32() int32 {
	return int32(val.Synonym())
}

func (val MagnetiqFaultErrCode) Int32Ptr() *int32 {
	result := int32(val.Synonym())
	return &result
}

func (val MagnetiqFaultErrCode) Int64() int64 {
	return int64(val.Synonym())
}

func (val MagnetiqFaultErrCode) Int64Ptr() *int64 {
	result := int64(val.Synonym())
	return &result
}

func (val MagnetiqFaultErrCode) Uint() uint {
	return uint(val.Synonym())
}

func (val MagnetiqFaultErrCode) UintPtr() *uint {
	result := uint(val.Synonym())
	return &result
}

func (val MagnetiqFaultErrCode) Uint32() uint32 {
	return uint32(val.Synonym())
}

func (val MagnetiqFaultErrCode) Uint32Ptr() *uint32 {
	result := uint32(val.Synonym())
	return &result
}

func (val MagnetiqFaultErrCode) Uint64() uint64 {
	return uint64(val.Synonym())
}

func (val MagnetiqFaultErrCode) Uint64Ptr() *uint64 {
	result := uint64(val.Synonym())
	return &result
}

func (val MagnetiqFaultErrCode) IsKnown() bool {
	return val.Synonym() != MagnetiqFaultErrCode(math.MinInt32)
}

func ConvertIntToMagnetiqFaultErrCode(in int) MagnetiqFaultErrCode {
	return MagnetiqFaultErrCode(in).Synonym()
}

func ConvertUintToMagnetiqFaultErrCode(in uint) MagnetiqFaultErrCode {
	return MagnetiqFaultErrCode(in).Synonym()
}

func ConvertInt32ToMagnetiqFaultErrCode(in int32) MagnetiqFaultErrCode {
	return MagnetiqFaultErrCode(in).Synonym()
}

func ConvertUint32ToMagnetiqFaultErrCode(in uint32) MagnetiqFaultErrCode {
	return MagnetiqFaultErrCode(in).Synonym()
}

func ConvertInt64ToMagnetiqFaultErrCode(in int64) MagnetiqFaultErrCode {
	return MagnetiqFaultErrCode(in).Synonym()
}

func ConvertUint64ToMagnetiqFaultErrCode(in uint64) MagnetiqFaultErrCode {
	return MagnetiqFaultErrCode(in).Synonym()
}

var MagnetiqFaultErrCode_Lower_value = map[string]MagnetiqFaultErrCode{
	"100000": 0,
	"100001": 1,
	"200000": 2,
	"300000": 3,
	"400000": 4,
	"400011": 5,
	"400014": 6,
	"200201": 7,
	"200301": 8,
	"200401": 9,
	"200601": 10,
	"505102": 11,
	"605103": 12,
	"505104": 13,
	"605105": 14,
	"505106": 15,
	"505107": 16,
	"705109": 17,
	"705110": 18,
	"705111": 19,
	"305112": 20,
	"305113": 21,
	"505115": 22,
	"505116": 23,
	"605117": 24,
	"605118": 25,
	"705120": 26,
	"305122": 27,
	"710101": 28,
	"710102": 29,
	"710103": 30,
	"710104": 31,
	"710105": 32,
	"710106": 33,
	"710109": 34,
	"510114": 35,
	"510115": 36,
	"710116": 37,
	"710117": 38,
	"610118": 39,
	"510119": 40,
	"510120": 41,
	"710121": 42,
	"710122": 43,
	"110201": 44,
	"710203": 45,
	"710204": 46,
	"710205": 47,
	"710206": 48,
	"710207": 49,
	"710208": 50,
	"110209": 51,
	"110210": 52,
	"110301": 53,
	"110302": 54,
	"110401": 55,
	"110402": 56,
	"210501": 57,
	"610602": 58,
	"710701": 59,
	"610702": 60,
	"111101": 61,
	"200203": 62,
	"200204": 63,
	"200205": 64,
	"200206": 65,
	"200207": 66,
	"605106": 67,
}

func ConvertStringToMagnetiqFaultErrCode(in string) MagnetiqFaultErrCode {
	if result, ok := MagnetiqFaultErrCode_value[in]; ok {
		return MagnetiqFaultErrCode(result)
	}

	if result, ok := MagnetiqFaultErrCode_Lower_value[strings.ToLower(in)]; ok {
		return MagnetiqFaultErrCode(result)
	}

	return MagnetiqFaultErrCode(math.MinInt32)
}

var SliceMagnetiqFaultErrCodeConvert *sliceMagnetiqFaultErrCodeConvert

type sliceMagnetiqFaultErrCodeConvert struct{}

func (*sliceMagnetiqFaultErrCodeConvert) Synonym(in []MagnetiqFaultErrCode) []MagnetiqFaultErrCode {
	result := make([]MagnetiqFaultErrCode, len(in))
	for i, v := range in {
		result[i] = v.Synonym()
	}

	return result
}

func (*sliceMagnetiqFaultErrCodeConvert) Int32(in []MagnetiqFaultErrCode) []int32 {
	result := make([]int32, len(in))
	for i, v := range in {
		result[i] = v.Int32()
	}

	return result
}

func (*sliceMagnetiqFaultErrCodeConvert) Uint32(in []MagnetiqFaultErrCode) []uint32 {
	result := make([]uint32, len(in))
	for i, v := range in {
		result[i] = v.Uint32()
	}

	return result
}

func (*sliceMagnetiqFaultErrCodeConvert) Uint64(in []MagnetiqFaultErrCode) []uint64 {
	result := make([]uint64, len(in))
	for i, v := range in {
		result[i] = v.Uint64()
	}

	return result
}

func (*sliceMagnetiqFaultErrCodeConvert) Int64(in []MagnetiqFaultErrCode) []int64 {
	result := make([]int64, len(in))
	for i, v := range in {
		result[i] = v.Int64()
	}

	return result
}

func (*sliceMagnetiqFaultErrCodeConvert) Uint(in []MagnetiqFaultErrCode) []uint {
	result := make([]uint, len(in))
	for i, v := range in {
		result[i] = v.Uint()
	}

	return result
}

func (*sliceMagnetiqFaultErrCodeConvert) Int(in []MagnetiqFaultErrCode) []int {
	result := make([]int, len(in))
	for i, v := range in {
		result[i] = v.Int()
	}

	return result
}

func (*sliceMagnetiqFaultErrCodeConvert) IsKnown(in []MagnetiqFaultErrCode) []bool {
	result := make([]bool, len(in))
	for i, v := range in {
		result[i] = v.IsKnown()
	}

	return result
}

func (*sliceMagnetiqFaultErrCodeConvert) ConvertIntToMagnetiqFaultErrCode(in []int) []MagnetiqFaultErrCode {
	result := make([]MagnetiqFaultErrCode, len(in))
	for i, v := range in {
		result[i] = ConvertIntToMagnetiqFaultErrCode(v)
	}

	return result
}

func (*sliceMagnetiqFaultErrCodeConvert) ConvertUintToMagnetiqFaultErrCode(in []uint) []MagnetiqFaultErrCode {
	result := make([]MagnetiqFaultErrCode, len(in))
	for i, v := range in {
		result[i] = ConvertUintToMagnetiqFaultErrCode(v)
	}

	return result
}

func (*sliceMagnetiqFaultErrCodeConvert) ConvertInt32ToMagnetiqFaultErrCode(in []int32) []MagnetiqFaultErrCode {
	result := make([]MagnetiqFaultErrCode, len(in))
	for i, v := range in {
		result[i] = ConvertInt32ToMagnetiqFaultErrCode(v)
	}

	return result
}

func (*sliceMagnetiqFaultErrCodeConvert) ConvertUint32ToMagnetiqFaultErrCode(in []uint32) []MagnetiqFaultErrCode {
	result := make([]MagnetiqFaultErrCode, len(in))
	for i, v := range in {
		result[i] = ConvertUint32ToMagnetiqFaultErrCode(v)
	}

	return result
}

func (*sliceMagnetiqFaultErrCodeConvert) ConvertInt64ToMagnetiqFaultErrCode(in []int64) []MagnetiqFaultErrCode {
	result := make([]MagnetiqFaultErrCode, len(in))
	for i, v := range in {
		result[i] = ConvertInt64ToMagnetiqFaultErrCode(v)
	}

	return result
}

func (*sliceMagnetiqFaultErrCodeConvert) ConvertUint64ToMagnetiqFaultErrCode(in []uint64) []MagnetiqFaultErrCode {
	result := make([]MagnetiqFaultErrCode, len(in))
	for i, v := range in {
		result[i] = ConvertUint64ToMagnetiqFaultErrCode(v)
	}

	return result
}

func (*sliceMagnetiqFaultErrCodeConvert) ConvertStringToMagnetiqFaultErrCode(in []string) []MagnetiqFaultErrCode {
	result := make([]MagnetiqFaultErrCode, len(in))
	for i, v := range in {
		result[i] = ConvertStringToMagnetiqFaultErrCode(v)
	}

	return result
}

func NewMagnetiqFaultErrCodeUsage() *MagnetiqFaultErrCodeUsage {
	return &MagnetiqFaultErrCodeUsage{
		enumMap: map[MagnetiqFaultErrCode]bool{
			MagnetiqFaultErrCode_MagnetiqFaultServerErr:                            false,
			MagnetiqFaultErrCode_MagnetiqFaultLockTimeoutErr:                       false,
			MagnetiqFaultErrCode_MagnetiqFaultEntityNotFoundErr:                    false,
			MagnetiqFaultErrCode_MagnetiqFaultBadRequestErr:                        false,
			MagnetiqFaultErrCode_MagnetiqFaultIntegrityViolationErr:                false,
			MagnetiqFaultErrCode_MagnetiqFaultOrderIdMustBeUniqueErr:               false,
			MagnetiqFaultErrCode_MagnetiqFaultTokenAlreadyRegisteredErr:            false,
			MagnetiqFaultErrCode_MagnetiqFaultTokenNotFoundErr:                     false,
			MagnetiqFaultErrCode_MagnetiqFaultDirectoryServiceNotFoundErr:          false,
			MagnetiqFaultErrCode_MagnetiqFaultKeyNotFoundErr:                       false,
			MagnetiqFaultErrCode_MagnetiqFaultRecurringNotFoundErr:                 false,
			MagnetiqFaultErrCode_MagnetiqFaultPaymentStateFlowViolationErr:         false,
			MagnetiqFaultErrCode_MagnetiqFaultAuthNon3dPaymentErr:                  false,
			MagnetiqFaultErrCode_MagnetiqFaultAuthenticatePaymentErr:               false,
			MagnetiqFaultErrCode_MagnetiqFaultAuthenticateNon3dPaymentErr:          false,
			MagnetiqFaultErrCode_MagnetiqFaultDepositPaymentErr:                    false,
			MagnetiqFaultErrCode_MagnetiqFaultCancelPaymentErr:                     false,
			MagnetiqFaultErrCode_MagnetiqFaultCardNameRequiredErr:                  false,
			MagnetiqFaultErrCode_MagnetiqFaultCardNumberRequiredErr:                false,
			MagnetiqFaultErrCode_MagnetiqFaultCardExpiryRequiredErr:                false,
			MagnetiqFaultErrCode_MagnetiqFaultInvalidCardExpiryDateErr:             false,
			MagnetiqFaultErrCode_MagnetiqFaultInvalidCardNumberErr:                 false,
			MagnetiqFaultErrCode_MagnetiqFaultCouldNotReversePaymentErr:            false,
			MagnetiqFaultErrCode_MagnetiqFaultMerchantInterfaceClosedErr:           false,
			MagnetiqFaultErrCode_MagnetiqFaultInvalidCurrencyErr:                   false,
			MagnetiqFaultErrCode_MagnetiqFaultOriginalCreditNotAllowedErr:          false,
			MagnetiqFaultErrCode_MagnetiqFaultCSCRequiredErr:                       false,
			MagnetiqFaultErrCode_MagnetiqFaultCSCInvalidErr:                        false,
			MagnetiqFaultErrCode_MagnetiqFaultPaymentModeRequiredErr:               false,
			MagnetiqFaultErrCode_MagnetiqFaultPaymentOrderRequiredErr:              false,
			MagnetiqFaultErrCode_MagnetiqFaultPaymentOrderIDRequiredErr:            false,
			MagnetiqFaultErrCode_MagnetiqFaultPaymentOrderAmountRequiredErr:        false,
			MagnetiqFaultErrCode_MagnetiqFaultPaymentOrderCurrencyRequiredErr:      false,
			MagnetiqFaultErrCode_MagnetiqFaultPaymentOrderDescriptionRequiredErr:   false,
			MagnetiqFaultErrCode_MagnetiqFaultD3DACSParResRequiredErr:              false,
			MagnetiqFaultErrCode_MagnetiqFaultRecurringExpiredErr:                  false,
			MagnetiqFaultErrCode_MagnetiqFaultTooFrequentRecurringErr:              false,
			MagnetiqFaultErrCode_MagnetiqFaultRecurringEndDateRequiredErr:          false,
			MagnetiqFaultErrCode_MagnetiqFaultRecurringFrequencyRequiredErr:        false,
			MagnetiqFaultErrCode_MagnetiqFaultRecurringEndDateIncorrectErr:         false,
			MagnetiqFaultErrCode_MagnetiqFaultRecurringEndDateFutureErr:            false,
			MagnetiqFaultErrCode_MagnetiqFaultRecurringFrequencyGreaterThanZeroErr: false,
			MagnetiqFaultErrCode_MagnetiqFaultRecurringIDRequiredErr:               false,
			MagnetiqFaultErrCode_MagnetiqFaultRemoteAddressRequiredErr:             false,
			MagnetiqFaultErrCode_MagnetiqFaultInvalidSignatureErr:                  false,
			MagnetiqFaultErrCode_MagnetiqFaultRequestRequiredErr:                   false,
			MagnetiqFaultErrCode_MagnetiqFaultInterfaceRequiredErr:                 false,
			MagnetiqFaultErrCode_MagnetiqFaultKeyIndexRequiredErr:                  false,
			MagnetiqFaultErrCode_MagnetiqFaultKeyRequiredErr:                       false,
			MagnetiqFaultErrCode_MagnetiqFaultDataRequiredErr:                      false,
			MagnetiqFaultErrCode_MagnetiqFaultSignatureRequiredErr:                 false,
			MagnetiqFaultErrCode_MagnetiqFaultXMLDeserializationFailedErr:          false,
			MagnetiqFaultErrCode_MagnetiqFaultXMLSerializationFailedErr:            false,
			MagnetiqFaultErrCode_MagnetiqFaultDataDecryptionFailedErr:              false,
			MagnetiqFaultErrCode_MagnetiqFaultDataEncryptionFailedErr:              false,
			MagnetiqFaultErrCode_MagnetiqFaultSignatureVerificationFailedErr:       false,
			MagnetiqFaultErrCode_MagnetiqFaultSigningFailedErr:                     false,
			MagnetiqFaultErrCode_MagnetiqFaultPaymentNotFoundErr:                   false,
			MagnetiqFaultErrCode_MagnetiqFaultInvalidPaymentModeAuthenticationErr:  false,
			MagnetiqFaultErrCode_MagnetiqFaultPaymentRequiredErr:                   false,
			MagnetiqFaultErrCode_MagnetiqFaultInvalidPaymentModeCreationErr:        false,
			MagnetiqFaultErrCode_MagnetiqFaultTokensDisabledErr:                    false,
			MagnetiqFaultErrCode_MagnetiqFaultInvalidTokenTypeErr:                  false,
			MagnetiqFaultErrCode_MagnetiqFaultUnableDecryptTokenErr:                false,
			MagnetiqFaultErrCode_MagnetiqFaultIncorrectAmountErr:                   false,
			MagnetiqFaultErrCode_MagnetiqFaultIncorrectCurrencyErr:                 false,
			MagnetiqFaultErrCode_MagnetiqFaultIncorrectCryptogramErr:               false,
			MagnetiqFaultErrCode_MagnetiqFaultIncorrectTokenModeErr:                false,
		},
	}
}

func IsMagnetiqFaultErrCode(target MagnetiqFaultErrCode, matches ...MagnetiqFaultErrCode) bool {
	for _, v := range matches {
		if target == v {
			return true
		}
	}

	return false
}

type MagnetiqFaultErrCodeUsage struct {
	enumMap map[MagnetiqFaultErrCode]bool
}

func (u *MagnetiqFaultErrCodeUsage) Use(slice ...MagnetiqFaultErrCode) *MagnetiqFaultErrCodeUsage {
	for _, in := range slice {
		u.enumMap[in] = true
	}

	return u
}

func (u *MagnetiqFaultErrCodeUsage) Check() []string {
	var result []string
	for k, v := range u.enumMap {
		if v {
			continue
		}

		result = append(result, k.String())
	}

	return result
}

func (u *MagnetiqFaultErrCodeUsage) MagnetiqFaultErrCode_MagnetiqFaultServerErr() MagnetiqFaultErrCode {
	u.Use(MagnetiqFaultErrCode_MagnetiqFaultServerErr)
	return MagnetiqFaultErrCode_MagnetiqFaultServerErr
}

func (u *MagnetiqFaultErrCodeUsage) MagnetiqFaultErrCode_MagnetiqFaultLockTimeoutErr() MagnetiqFaultErrCode {
	u.Use(MagnetiqFaultErrCode_MagnetiqFaultLockTimeoutErr)
	return MagnetiqFaultErrCode_MagnetiqFaultLockTimeoutErr
}

func (u *MagnetiqFaultErrCodeUsage) MagnetiqFaultErrCode_MagnetiqFaultEntityNotFoundErr() MagnetiqFaultErrCode {
	u.Use(MagnetiqFaultErrCode_MagnetiqFaultEntityNotFoundErr)
	return MagnetiqFaultErrCode_MagnetiqFaultEntityNotFoundErr
}

func (u *MagnetiqFaultErrCodeUsage) MagnetiqFaultErrCode_MagnetiqFaultBadRequestErr() MagnetiqFaultErrCode {
	u.Use(MagnetiqFaultErrCode_MagnetiqFaultBadRequestErr)
	return MagnetiqFaultErrCode_MagnetiqFaultBadRequestErr
}

func (u *MagnetiqFaultErrCodeUsage) MagnetiqFaultErrCode_MagnetiqFaultIntegrityViolationErr() MagnetiqFaultErrCode {
	u.Use(MagnetiqFaultErrCode_MagnetiqFaultIntegrityViolationErr)
	return MagnetiqFaultErrCode_MagnetiqFaultIntegrityViolationErr
}

func (u *MagnetiqFaultErrCodeUsage) MagnetiqFaultErrCode_MagnetiqFaultOrderIdMustBeUniqueErr() MagnetiqFaultErrCode {
	u.Use(MagnetiqFaultErrCode_MagnetiqFaultOrderIdMustBeUniqueErr)
	return MagnetiqFaultErrCode_MagnetiqFaultOrderIdMustBeUniqueErr
}

func (u *MagnetiqFaultErrCodeUsage) MagnetiqFaultErrCode_MagnetiqFaultTokenAlreadyRegisteredErr() MagnetiqFaultErrCode {
	u.Use(MagnetiqFaultErrCode_MagnetiqFaultTokenAlreadyRegisteredErr)
	return MagnetiqFaultErrCode_MagnetiqFaultTokenAlreadyRegisteredErr
}

func (u *MagnetiqFaultErrCodeUsage) MagnetiqFaultErrCode_MagnetiqFaultTokenNotFoundErr() MagnetiqFaultErrCode {
	u.Use(MagnetiqFaultErrCode_MagnetiqFaultTokenNotFoundErr)
	return MagnetiqFaultErrCode_MagnetiqFaultTokenNotFoundErr
}

func (u *MagnetiqFaultErrCodeUsage) MagnetiqFaultErrCode_MagnetiqFaultDirectoryServiceNotFoundErr() MagnetiqFaultErrCode {
	u.Use(MagnetiqFaultErrCode_MagnetiqFaultDirectoryServiceNotFoundErr)
	return MagnetiqFaultErrCode_MagnetiqFaultDirectoryServiceNotFoundErr
}

func (u *MagnetiqFaultErrCodeUsage) MagnetiqFaultErrCode_MagnetiqFaultKeyNotFoundErr() MagnetiqFaultErrCode {
	u.Use(MagnetiqFaultErrCode_MagnetiqFaultKeyNotFoundErr)
	return MagnetiqFaultErrCode_MagnetiqFaultKeyNotFoundErr
}

func (u *MagnetiqFaultErrCodeUsage) MagnetiqFaultErrCode_MagnetiqFaultRecurringNotFoundErr() MagnetiqFaultErrCode {
	u.Use(MagnetiqFaultErrCode_MagnetiqFaultRecurringNotFoundErr)
	return MagnetiqFaultErrCode_MagnetiqFaultRecurringNotFoundErr
}

func (u *MagnetiqFaultErrCodeUsage) MagnetiqFaultErrCode_MagnetiqFaultPaymentStateFlowViolationErr() MagnetiqFaultErrCode {
	u.Use(MagnetiqFaultErrCode_MagnetiqFaultPaymentStateFlowViolationErr)
	return MagnetiqFaultErrCode_MagnetiqFaultPaymentStateFlowViolationErr
}

func (u *MagnetiqFaultErrCodeUsage) MagnetiqFaultErrCode_MagnetiqFaultAuthNon3dPaymentErr() MagnetiqFaultErrCode {
	u.Use(MagnetiqFaultErrCode_MagnetiqFaultAuthNon3dPaymentErr)
	return MagnetiqFaultErrCode_MagnetiqFaultAuthNon3dPaymentErr
}

func (u *MagnetiqFaultErrCodeUsage) MagnetiqFaultErrCode_MagnetiqFaultAuthenticatePaymentErr() MagnetiqFaultErrCode {
	u.Use(MagnetiqFaultErrCode_MagnetiqFaultAuthenticatePaymentErr)
	return MagnetiqFaultErrCode_MagnetiqFaultAuthenticatePaymentErr
}

func (u *MagnetiqFaultErrCodeUsage) MagnetiqFaultErrCode_MagnetiqFaultAuthenticateNon3dPaymentErr() MagnetiqFaultErrCode {
	u.Use(MagnetiqFaultErrCode_MagnetiqFaultAuthenticateNon3dPaymentErr)
	return MagnetiqFaultErrCode_MagnetiqFaultAuthenticateNon3dPaymentErr
}

func (u *MagnetiqFaultErrCodeUsage) MagnetiqFaultErrCode_MagnetiqFaultDepositPaymentErr() MagnetiqFaultErrCode {
	u.Use(MagnetiqFaultErrCode_MagnetiqFaultDepositPaymentErr)
	return MagnetiqFaultErrCode_MagnetiqFaultDepositPaymentErr
}

func (u *MagnetiqFaultErrCodeUsage) MagnetiqFaultErrCode_MagnetiqFaultCancelPaymentErr() MagnetiqFaultErrCode {
	u.Use(MagnetiqFaultErrCode_MagnetiqFaultCancelPaymentErr)
	return MagnetiqFaultErrCode_MagnetiqFaultCancelPaymentErr
}

func (u *MagnetiqFaultErrCodeUsage) MagnetiqFaultErrCode_MagnetiqFaultCardNameRequiredErr() MagnetiqFaultErrCode {
	u.Use(MagnetiqFaultErrCode_MagnetiqFaultCardNameRequiredErr)
	return MagnetiqFaultErrCode_MagnetiqFaultCardNameRequiredErr
}

func (u *MagnetiqFaultErrCodeUsage) MagnetiqFaultErrCode_MagnetiqFaultCardNumberRequiredErr() MagnetiqFaultErrCode {
	u.Use(MagnetiqFaultErrCode_MagnetiqFaultCardNumberRequiredErr)
	return MagnetiqFaultErrCode_MagnetiqFaultCardNumberRequiredErr
}

func (u *MagnetiqFaultErrCodeUsage) MagnetiqFaultErrCode_MagnetiqFaultCardExpiryRequiredErr() MagnetiqFaultErrCode {
	u.Use(MagnetiqFaultErrCode_MagnetiqFaultCardExpiryRequiredErr)
	return MagnetiqFaultErrCode_MagnetiqFaultCardExpiryRequiredErr
}

func (u *MagnetiqFaultErrCodeUsage) MagnetiqFaultErrCode_MagnetiqFaultInvalidCardExpiryDateErr() MagnetiqFaultErrCode {
	u.Use(MagnetiqFaultErrCode_MagnetiqFaultInvalidCardExpiryDateErr)
	return MagnetiqFaultErrCode_MagnetiqFaultInvalidCardExpiryDateErr
}

func (u *MagnetiqFaultErrCodeUsage) MagnetiqFaultErrCode_MagnetiqFaultInvalidCardNumberErr() MagnetiqFaultErrCode {
	u.Use(MagnetiqFaultErrCode_MagnetiqFaultInvalidCardNumberErr)
	return MagnetiqFaultErrCode_MagnetiqFaultInvalidCardNumberErr
}

func (u *MagnetiqFaultErrCodeUsage) MagnetiqFaultErrCode_MagnetiqFaultCouldNotReversePaymentErr() MagnetiqFaultErrCode {
	u.Use(MagnetiqFaultErrCode_MagnetiqFaultCouldNotReversePaymentErr)
	return MagnetiqFaultErrCode_MagnetiqFaultCouldNotReversePaymentErr
}

func (u *MagnetiqFaultErrCodeUsage) MagnetiqFaultErrCode_MagnetiqFaultMerchantInterfaceClosedErr() MagnetiqFaultErrCode {
	u.Use(MagnetiqFaultErrCode_MagnetiqFaultMerchantInterfaceClosedErr)
	return MagnetiqFaultErrCode_MagnetiqFaultMerchantInterfaceClosedErr
}

func (u *MagnetiqFaultErrCodeUsage) MagnetiqFaultErrCode_MagnetiqFaultInvalidCurrencyErr() MagnetiqFaultErrCode {
	u.Use(MagnetiqFaultErrCode_MagnetiqFaultInvalidCurrencyErr)
	return MagnetiqFaultErrCode_MagnetiqFaultInvalidCurrencyErr
}

func (u *MagnetiqFaultErrCodeUsage) MagnetiqFaultErrCode_MagnetiqFaultOriginalCreditNotAllowedErr() MagnetiqFaultErrCode {
	u.Use(MagnetiqFaultErrCode_MagnetiqFaultOriginalCreditNotAllowedErr)
	return MagnetiqFaultErrCode_MagnetiqFaultOriginalCreditNotAllowedErr
}

func (u *MagnetiqFaultErrCodeUsage) MagnetiqFaultErrCode_MagnetiqFaultCSCRequiredErr() MagnetiqFaultErrCode {
	u.Use(MagnetiqFaultErrCode_MagnetiqFaultCSCRequiredErr)
	return MagnetiqFaultErrCode_MagnetiqFaultCSCRequiredErr
}

func (u *MagnetiqFaultErrCodeUsage) MagnetiqFaultErrCode_MagnetiqFaultCSCInvalidErr() MagnetiqFaultErrCode {
	u.Use(MagnetiqFaultErrCode_MagnetiqFaultCSCInvalidErr)
	return MagnetiqFaultErrCode_MagnetiqFaultCSCInvalidErr
}

func (u *MagnetiqFaultErrCodeUsage) MagnetiqFaultErrCode_MagnetiqFaultPaymentModeRequiredErr() MagnetiqFaultErrCode {
	u.Use(MagnetiqFaultErrCode_MagnetiqFaultPaymentModeRequiredErr)
	return MagnetiqFaultErrCode_MagnetiqFaultPaymentModeRequiredErr
}

func (u *MagnetiqFaultErrCodeUsage) MagnetiqFaultErrCode_MagnetiqFaultPaymentOrderRequiredErr() MagnetiqFaultErrCode {
	u.Use(MagnetiqFaultErrCode_MagnetiqFaultPaymentOrderRequiredErr)
	return MagnetiqFaultErrCode_MagnetiqFaultPaymentOrderRequiredErr
}

func (u *MagnetiqFaultErrCodeUsage) MagnetiqFaultErrCode_MagnetiqFaultPaymentOrderIDRequiredErr() MagnetiqFaultErrCode {
	u.Use(MagnetiqFaultErrCode_MagnetiqFaultPaymentOrderIDRequiredErr)
	return MagnetiqFaultErrCode_MagnetiqFaultPaymentOrderIDRequiredErr
}

func (u *MagnetiqFaultErrCodeUsage) MagnetiqFaultErrCode_MagnetiqFaultPaymentOrderAmountRequiredErr() MagnetiqFaultErrCode {
	u.Use(MagnetiqFaultErrCode_MagnetiqFaultPaymentOrderAmountRequiredErr)
	return MagnetiqFaultErrCode_MagnetiqFaultPaymentOrderAmountRequiredErr
}

func (u *MagnetiqFaultErrCodeUsage) MagnetiqFaultErrCode_MagnetiqFaultPaymentOrderCurrencyRequiredErr() MagnetiqFaultErrCode {
	u.Use(MagnetiqFaultErrCode_MagnetiqFaultPaymentOrderCurrencyRequiredErr)
	return MagnetiqFaultErrCode_MagnetiqFaultPaymentOrderCurrencyRequiredErr
}

func (u *MagnetiqFaultErrCodeUsage) MagnetiqFaultErrCode_MagnetiqFaultPaymentOrderDescriptionRequiredErr() MagnetiqFaultErrCode {
	u.Use(MagnetiqFaultErrCode_MagnetiqFaultPaymentOrderDescriptionRequiredErr)
	return MagnetiqFaultErrCode_MagnetiqFaultPaymentOrderDescriptionRequiredErr
}

func (u *MagnetiqFaultErrCodeUsage) MagnetiqFaultErrCode_MagnetiqFaultD3DACSParResRequiredErr() MagnetiqFaultErrCode {
	u.Use(MagnetiqFaultErrCode_MagnetiqFaultD3DACSParResRequiredErr)
	return MagnetiqFaultErrCode_MagnetiqFaultD3DACSParResRequiredErr
}

func (u *MagnetiqFaultErrCodeUsage) MagnetiqFaultErrCode_MagnetiqFaultRecurringExpiredErr() MagnetiqFaultErrCode {
	u.Use(MagnetiqFaultErrCode_MagnetiqFaultRecurringExpiredErr)
	return MagnetiqFaultErrCode_MagnetiqFaultRecurringExpiredErr
}

func (u *MagnetiqFaultErrCodeUsage) MagnetiqFaultErrCode_MagnetiqFaultTooFrequentRecurringErr() MagnetiqFaultErrCode {
	u.Use(MagnetiqFaultErrCode_MagnetiqFaultTooFrequentRecurringErr)
	return MagnetiqFaultErrCode_MagnetiqFaultTooFrequentRecurringErr
}

func (u *MagnetiqFaultErrCodeUsage) MagnetiqFaultErrCode_MagnetiqFaultRecurringEndDateRequiredErr() MagnetiqFaultErrCode {
	u.Use(MagnetiqFaultErrCode_MagnetiqFaultRecurringEndDateRequiredErr)
	return MagnetiqFaultErrCode_MagnetiqFaultRecurringEndDateRequiredErr
}

func (u *MagnetiqFaultErrCodeUsage) MagnetiqFaultErrCode_MagnetiqFaultRecurringFrequencyRequiredErr() MagnetiqFaultErrCode {
	u.Use(MagnetiqFaultErrCode_MagnetiqFaultRecurringFrequencyRequiredErr)
	return MagnetiqFaultErrCode_MagnetiqFaultRecurringFrequencyRequiredErr
}

func (u *MagnetiqFaultErrCodeUsage) MagnetiqFaultErrCode_MagnetiqFaultRecurringEndDateIncorrectErr() MagnetiqFaultErrCode {
	u.Use(MagnetiqFaultErrCode_MagnetiqFaultRecurringEndDateIncorrectErr)
	return MagnetiqFaultErrCode_MagnetiqFaultRecurringEndDateIncorrectErr
}

func (u *MagnetiqFaultErrCodeUsage) MagnetiqFaultErrCode_MagnetiqFaultRecurringEndDateFutureErr() MagnetiqFaultErrCode {
	u.Use(MagnetiqFaultErrCode_MagnetiqFaultRecurringEndDateFutureErr)
	return MagnetiqFaultErrCode_MagnetiqFaultRecurringEndDateFutureErr
}

func (u *MagnetiqFaultErrCodeUsage) MagnetiqFaultErrCode_MagnetiqFaultRecurringFrequencyGreaterThanZeroErr() MagnetiqFaultErrCode {
	u.Use(MagnetiqFaultErrCode_MagnetiqFaultRecurringFrequencyGreaterThanZeroErr)
	return MagnetiqFaultErrCode_MagnetiqFaultRecurringFrequencyGreaterThanZeroErr
}

func (u *MagnetiqFaultErrCodeUsage) MagnetiqFaultErrCode_MagnetiqFaultRecurringIDRequiredErr() MagnetiqFaultErrCode {
	u.Use(MagnetiqFaultErrCode_MagnetiqFaultRecurringIDRequiredErr)
	return MagnetiqFaultErrCode_MagnetiqFaultRecurringIDRequiredErr
}

func (u *MagnetiqFaultErrCodeUsage) MagnetiqFaultErrCode_MagnetiqFaultRemoteAddressRequiredErr() MagnetiqFaultErrCode {
	u.Use(MagnetiqFaultErrCode_MagnetiqFaultRemoteAddressRequiredErr)
	return MagnetiqFaultErrCode_MagnetiqFaultRemoteAddressRequiredErr
}

func (u *MagnetiqFaultErrCodeUsage) MagnetiqFaultErrCode_MagnetiqFaultInvalidSignatureErr() MagnetiqFaultErrCode {
	u.Use(MagnetiqFaultErrCode_MagnetiqFaultInvalidSignatureErr)
	return MagnetiqFaultErrCode_MagnetiqFaultInvalidSignatureErr
}

func (u *MagnetiqFaultErrCodeUsage) MagnetiqFaultErrCode_MagnetiqFaultRequestRequiredErr() MagnetiqFaultErrCode {
	u.Use(MagnetiqFaultErrCode_MagnetiqFaultRequestRequiredErr)
	return MagnetiqFaultErrCode_MagnetiqFaultRequestRequiredErr
}

func (u *MagnetiqFaultErrCodeUsage) MagnetiqFaultErrCode_MagnetiqFaultInterfaceRequiredErr() MagnetiqFaultErrCode {
	u.Use(MagnetiqFaultErrCode_MagnetiqFaultInterfaceRequiredErr)
	return MagnetiqFaultErrCode_MagnetiqFaultInterfaceRequiredErr
}

func (u *MagnetiqFaultErrCodeUsage) MagnetiqFaultErrCode_MagnetiqFaultKeyIndexRequiredErr() MagnetiqFaultErrCode {
	u.Use(MagnetiqFaultErrCode_MagnetiqFaultKeyIndexRequiredErr)
	return MagnetiqFaultErrCode_MagnetiqFaultKeyIndexRequiredErr
}

func (u *MagnetiqFaultErrCodeUsage) MagnetiqFaultErrCode_MagnetiqFaultKeyRequiredErr() MagnetiqFaultErrCode {
	u.Use(MagnetiqFaultErrCode_MagnetiqFaultKeyRequiredErr)
	return MagnetiqFaultErrCode_MagnetiqFaultKeyRequiredErr
}

func (u *MagnetiqFaultErrCodeUsage) MagnetiqFaultErrCode_MagnetiqFaultDataRequiredErr() MagnetiqFaultErrCode {
	u.Use(MagnetiqFaultErrCode_MagnetiqFaultDataRequiredErr)
	return MagnetiqFaultErrCode_MagnetiqFaultDataRequiredErr
}

func (u *MagnetiqFaultErrCodeUsage) MagnetiqFaultErrCode_MagnetiqFaultSignatureRequiredErr() MagnetiqFaultErrCode {
	u.Use(MagnetiqFaultErrCode_MagnetiqFaultSignatureRequiredErr)
	return MagnetiqFaultErrCode_MagnetiqFaultSignatureRequiredErr
}

func (u *MagnetiqFaultErrCodeUsage) MagnetiqFaultErrCode_MagnetiqFaultXMLDeserializationFailedErr() MagnetiqFaultErrCode {
	u.Use(MagnetiqFaultErrCode_MagnetiqFaultXMLDeserializationFailedErr)
	return MagnetiqFaultErrCode_MagnetiqFaultXMLDeserializationFailedErr
}

func (u *MagnetiqFaultErrCodeUsage) MagnetiqFaultErrCode_MagnetiqFaultXMLSerializationFailedErr() MagnetiqFaultErrCode {
	u.Use(MagnetiqFaultErrCode_MagnetiqFaultXMLSerializationFailedErr)
	return MagnetiqFaultErrCode_MagnetiqFaultXMLSerializationFailedErr
}

func (u *MagnetiqFaultErrCodeUsage) MagnetiqFaultErrCode_MagnetiqFaultDataDecryptionFailedErr() MagnetiqFaultErrCode {
	u.Use(MagnetiqFaultErrCode_MagnetiqFaultDataDecryptionFailedErr)
	return MagnetiqFaultErrCode_MagnetiqFaultDataDecryptionFailedErr
}

func (u *MagnetiqFaultErrCodeUsage) MagnetiqFaultErrCode_MagnetiqFaultDataEncryptionFailedErr() MagnetiqFaultErrCode {
	u.Use(MagnetiqFaultErrCode_MagnetiqFaultDataEncryptionFailedErr)
	return MagnetiqFaultErrCode_MagnetiqFaultDataEncryptionFailedErr
}

func (u *MagnetiqFaultErrCodeUsage) MagnetiqFaultErrCode_MagnetiqFaultSignatureVerificationFailedErr() MagnetiqFaultErrCode {
	u.Use(MagnetiqFaultErrCode_MagnetiqFaultSignatureVerificationFailedErr)
	return MagnetiqFaultErrCode_MagnetiqFaultSignatureVerificationFailedErr
}

func (u *MagnetiqFaultErrCodeUsage) MagnetiqFaultErrCode_MagnetiqFaultSigningFailedErr() MagnetiqFaultErrCode {
	u.Use(MagnetiqFaultErrCode_MagnetiqFaultSigningFailedErr)
	return MagnetiqFaultErrCode_MagnetiqFaultSigningFailedErr
}

func (u *MagnetiqFaultErrCodeUsage) MagnetiqFaultErrCode_MagnetiqFaultPaymentNotFoundErr() MagnetiqFaultErrCode {
	u.Use(MagnetiqFaultErrCode_MagnetiqFaultPaymentNotFoundErr)
	return MagnetiqFaultErrCode_MagnetiqFaultPaymentNotFoundErr
}

func (u *MagnetiqFaultErrCodeUsage) MagnetiqFaultErrCode_MagnetiqFaultInvalidPaymentModeAuthenticationErr() MagnetiqFaultErrCode {
	u.Use(MagnetiqFaultErrCode_MagnetiqFaultInvalidPaymentModeAuthenticationErr)
	return MagnetiqFaultErrCode_MagnetiqFaultInvalidPaymentModeAuthenticationErr
}

func (u *MagnetiqFaultErrCodeUsage) MagnetiqFaultErrCode_MagnetiqFaultPaymentRequiredErr() MagnetiqFaultErrCode {
	u.Use(MagnetiqFaultErrCode_MagnetiqFaultPaymentRequiredErr)
	return MagnetiqFaultErrCode_MagnetiqFaultPaymentRequiredErr
}

func (u *MagnetiqFaultErrCodeUsage) MagnetiqFaultErrCode_MagnetiqFaultInvalidPaymentModeCreationErr() MagnetiqFaultErrCode {
	u.Use(MagnetiqFaultErrCode_MagnetiqFaultInvalidPaymentModeCreationErr)
	return MagnetiqFaultErrCode_MagnetiqFaultInvalidPaymentModeCreationErr
}

func (u *MagnetiqFaultErrCodeUsage) MagnetiqFaultErrCode_MagnetiqFaultTokensDisabledErr() MagnetiqFaultErrCode {
	u.Use(MagnetiqFaultErrCode_MagnetiqFaultTokensDisabledErr)
	return MagnetiqFaultErrCode_MagnetiqFaultTokensDisabledErr
}

func (u *MagnetiqFaultErrCodeUsage) MagnetiqFaultErrCode_MagnetiqFaultInvalidTokenTypeErr() MagnetiqFaultErrCode {
	u.Use(MagnetiqFaultErrCode_MagnetiqFaultInvalidTokenTypeErr)
	return MagnetiqFaultErrCode_MagnetiqFaultInvalidTokenTypeErr
}

func (u *MagnetiqFaultErrCodeUsage) MagnetiqFaultErrCode_MagnetiqFaultUnableDecryptTokenErr() MagnetiqFaultErrCode {
	u.Use(MagnetiqFaultErrCode_MagnetiqFaultUnableDecryptTokenErr)
	return MagnetiqFaultErrCode_MagnetiqFaultUnableDecryptTokenErr
}

func (u *MagnetiqFaultErrCodeUsage) MagnetiqFaultErrCode_MagnetiqFaultIncorrectAmountErr() MagnetiqFaultErrCode {
	u.Use(MagnetiqFaultErrCode_MagnetiqFaultIncorrectAmountErr)
	return MagnetiqFaultErrCode_MagnetiqFaultIncorrectAmountErr
}

func (u *MagnetiqFaultErrCodeUsage) MagnetiqFaultErrCode_MagnetiqFaultIncorrectCurrencyErr() MagnetiqFaultErrCode {
	u.Use(MagnetiqFaultErrCode_MagnetiqFaultIncorrectCurrencyErr)
	return MagnetiqFaultErrCode_MagnetiqFaultIncorrectCurrencyErr
}

func (u *MagnetiqFaultErrCodeUsage) MagnetiqFaultErrCode_MagnetiqFaultIncorrectCryptogramErr() MagnetiqFaultErrCode {
	u.Use(MagnetiqFaultErrCode_MagnetiqFaultIncorrectCryptogramErr)
	return MagnetiqFaultErrCode_MagnetiqFaultIncorrectCryptogramErr
}

func (u *MagnetiqFaultErrCodeUsage) MagnetiqFaultErrCode_MagnetiqFaultIncorrectTokenModeErr() MagnetiqFaultErrCode {
	u.Use(MagnetiqFaultErrCode_MagnetiqFaultIncorrectTokenModeErr)
	return MagnetiqFaultErrCode_MagnetiqFaultIncorrectTokenModeErr
}

func (val EnumMagnetiqActionCode) Synonym() EnumMagnetiqActionCode {
	if _, ok := EnumMagnetiqActionCode_name[int32(val)]; ok {
		return val
	}

	return EnumMagnetiqActionCode(math.MinInt32)
}

func (val EnumMagnetiqActionCode) Int() int {
	return int(val.Synonym())
}

func (val EnumMagnetiqActionCode) IntPtr() *int {
	result := int(val.Synonym())
	return &result
}

func (val EnumMagnetiqActionCode) Int32() int32 {
	return int32(val.Synonym())
}

func (val EnumMagnetiqActionCode) Int32Ptr() *int32 {
	result := int32(val.Synonym())
	return &result
}

func (val EnumMagnetiqActionCode) Int64() int64 {
	return int64(val.Synonym())
}

func (val EnumMagnetiqActionCode) Int64Ptr() *int64 {
	result := int64(val.Synonym())
	return &result
}

func (val EnumMagnetiqActionCode) Uint() uint {
	return uint(val.Synonym())
}

func (val EnumMagnetiqActionCode) UintPtr() *uint {
	result := uint(val.Synonym())
	return &result
}

func (val EnumMagnetiqActionCode) Uint32() uint32 {
	return uint32(val.Synonym())
}

func (val EnumMagnetiqActionCode) Uint32Ptr() *uint32 {
	result := uint32(val.Synonym())
	return &result
}

func (val EnumMagnetiqActionCode) Uint64() uint64 {
	return uint64(val.Synonym())
}

func (val EnumMagnetiqActionCode) Uint64Ptr() *uint64 {
	result := uint64(val.Synonym())
	return &result
}

func (val EnumMagnetiqActionCode) IsKnown() bool {
	return val.Synonym() != EnumMagnetiqActionCode(math.MinInt32)
}

func ConvertIntToEnumMagnetiqActionCode(in int) EnumMagnetiqActionCode {
	return EnumMagnetiqActionCode(in).Synonym()
}

func ConvertUintToEnumMagnetiqActionCode(in uint) EnumMagnetiqActionCode {
	return EnumMagnetiqActionCode(in).Synonym()
}

func ConvertInt32ToEnumMagnetiqActionCode(in int32) EnumMagnetiqActionCode {
	return EnumMagnetiqActionCode(in).Synonym()
}

func ConvertUint32ToEnumMagnetiqActionCode(in uint32) EnumMagnetiqActionCode {
	return EnumMagnetiqActionCode(in).Synonym()
}

func ConvertInt64ToEnumMagnetiqActionCode(in int64) EnumMagnetiqActionCode {
	return EnumMagnetiqActionCode(in).Synonym()
}

func ConvertUint64ToEnumMagnetiqActionCode(in uint64) EnumMagnetiqActionCode {
	return EnumMagnetiqActionCode(in).Synonym()
}

var EnumMagnetiqActionCode_Lower_value = map[string]EnumMagnetiqActionCode{
	"000": 0,
	"100": 1,
	"101": 2,
	"102": 3,
	"103": 4,
	"104": 5,
	"105": 6,
	"107": 7,
	"108": 8,
	"109": 9,
	"110": 10,
	"111": 11,
	"113": 12,
	"114": 13,
	"115": 14,
	"116": 15,
	"118": 16,
	"119": 17,
	"120": 18,
	"121": 19,
	"122": 20,
	"123": 21,
	"124": 22,
	"125": 23,
	"129": 24,
	"160": 25,
	"161": 26,
	"162": 27,
	"163": 28,
	"164": 29,
	"180": 30,
	"900": 31,
	"901": 32,
	"902": 33,
	"903": 34,
	"904": 35,
	"907": 36,
	"908": 37,
	"909": 38,
	"910": 39,
	"911": 40,
	"912": 41,
	"913": 42,
	"914": 43,
	"918": 44,
	"922": 45,
	"923": 46,
	"950": 47,
	"181": 48,
	"182": 49,
	"940": 50,
	"941": 51,
	"942": 52,
	"945": 53,
	"946": 54,
	"948": 55,
	"949": 56,
	"951": 57,
}

func ConvertStringToEnumMagnetiqActionCode(in string) EnumMagnetiqActionCode {
	if result, ok := EnumMagnetiqActionCode_value[in]; ok {
		return EnumMagnetiqActionCode(result)
	}

	if result, ok := EnumMagnetiqActionCode_Lower_value[strings.ToLower(in)]; ok {
		return EnumMagnetiqActionCode(result)
	}

	return EnumMagnetiqActionCode(math.MinInt32)
}

var SliceEnumMagnetiqActionCodeConvert *sliceEnumMagnetiqActionCodeConvert

type sliceEnumMagnetiqActionCodeConvert struct{}

func (*sliceEnumMagnetiqActionCodeConvert) Synonym(in []EnumMagnetiqActionCode) []EnumMagnetiqActionCode {
	result := make([]EnumMagnetiqActionCode, len(in))
	for i, v := range in {
		result[i] = v.Synonym()
	}

	return result
}

func (*sliceEnumMagnetiqActionCodeConvert) Int32(in []EnumMagnetiqActionCode) []int32 {
	result := make([]int32, len(in))
	for i, v := range in {
		result[i] = v.Int32()
	}

	return result
}

func (*sliceEnumMagnetiqActionCodeConvert) Uint32(in []EnumMagnetiqActionCode) []uint32 {
	result := make([]uint32, len(in))
	for i, v := range in {
		result[i] = v.Uint32()
	}

	return result
}

func (*sliceEnumMagnetiqActionCodeConvert) Uint64(in []EnumMagnetiqActionCode) []uint64 {
	result := make([]uint64, len(in))
	for i, v := range in {
		result[i] = v.Uint64()
	}

	return result
}

func (*sliceEnumMagnetiqActionCodeConvert) Int64(in []EnumMagnetiqActionCode) []int64 {
	result := make([]int64, len(in))
	for i, v := range in {
		result[i] = v.Int64()
	}

	return result
}

func (*sliceEnumMagnetiqActionCodeConvert) Uint(in []EnumMagnetiqActionCode) []uint {
	result := make([]uint, len(in))
	for i, v := range in {
		result[i] = v.Uint()
	}

	return result
}

func (*sliceEnumMagnetiqActionCodeConvert) Int(in []EnumMagnetiqActionCode) []int {
	result := make([]int, len(in))
	for i, v := range in {
		result[i] = v.Int()
	}

	return result
}

func (*sliceEnumMagnetiqActionCodeConvert) IsKnown(in []EnumMagnetiqActionCode) []bool {
	result := make([]bool, len(in))
	for i, v := range in {
		result[i] = v.IsKnown()
	}

	return result
}

func (*sliceEnumMagnetiqActionCodeConvert) ConvertIntToEnumMagnetiqActionCode(in []int) []EnumMagnetiqActionCode {
	result := make([]EnumMagnetiqActionCode, len(in))
	for i, v := range in {
		result[i] = ConvertIntToEnumMagnetiqActionCode(v)
	}

	return result
}

func (*sliceEnumMagnetiqActionCodeConvert) ConvertUintToEnumMagnetiqActionCode(in []uint) []EnumMagnetiqActionCode {
	result := make([]EnumMagnetiqActionCode, len(in))
	for i, v := range in {
		result[i] = ConvertUintToEnumMagnetiqActionCode(v)
	}

	return result
}

func (*sliceEnumMagnetiqActionCodeConvert) ConvertInt32ToEnumMagnetiqActionCode(in []int32) []EnumMagnetiqActionCode {
	result := make([]EnumMagnetiqActionCode, len(in))
	for i, v := range in {
		result[i] = ConvertInt32ToEnumMagnetiqActionCode(v)
	}

	return result
}

func (*sliceEnumMagnetiqActionCodeConvert) ConvertUint32ToEnumMagnetiqActionCode(in []uint32) []EnumMagnetiqActionCode {
	result := make([]EnumMagnetiqActionCode, len(in))
	for i, v := range in {
		result[i] = ConvertUint32ToEnumMagnetiqActionCode(v)
	}

	return result
}

func (*sliceEnumMagnetiqActionCodeConvert) ConvertInt64ToEnumMagnetiqActionCode(in []int64) []EnumMagnetiqActionCode {
	result := make([]EnumMagnetiqActionCode, len(in))
	for i, v := range in {
		result[i] = ConvertInt64ToEnumMagnetiqActionCode(v)
	}

	return result
}

func (*sliceEnumMagnetiqActionCodeConvert) ConvertUint64ToEnumMagnetiqActionCode(in []uint64) []EnumMagnetiqActionCode {
	result := make([]EnumMagnetiqActionCode, len(in))
	for i, v := range in {
		result[i] = ConvertUint64ToEnumMagnetiqActionCode(v)
	}

	return result
}

func (*sliceEnumMagnetiqActionCodeConvert) ConvertStringToEnumMagnetiqActionCode(in []string) []EnumMagnetiqActionCode {
	result := make([]EnumMagnetiqActionCode, len(in))
	for i, v := range in {
		result[i] = ConvertStringToEnumMagnetiqActionCode(v)
	}

	return result
}

func NewEnumMagnetiqActionCodeUsage() *EnumMagnetiqActionCodeUsage {
	return &EnumMagnetiqActionCodeUsage{
		enumMap: map[EnumMagnetiqActionCode]bool{
			EnumMagnetiqActionCode_MagnetiqActionCodeApproved:                             false,
			EnumMagnetiqActionCode_MagnetiqActionCodeDeclineGeneral:                       false,
			EnumMagnetiqActionCode_MagnetiqActionCodeDeclineExpiredCard:                   false,
			EnumMagnetiqActionCode_MagnetiqActionCodeDeclineSuspectedFraud:                false,
			EnumMagnetiqActionCode_MagnetiqActionCodeDeclineContactAcquirer:               false,
			EnumMagnetiqActionCode_MagnetiqActionCodeDeclineRestrictedCard:                false,
			EnumMagnetiqActionCode_MagnetiqActionCodeDeclineCallSecurityDept:              false,
			EnumMagnetiqActionCode_MagnetiqActionCodeDeclineReferToIssuer:                 false,
			EnumMagnetiqActionCode_MagnetiqActionCodeDeclineIssuerSpecialConditions:       false,
			EnumMagnetiqActionCode_MagnetiqActionCodeDeclineInvalidMerchant:               false,
			EnumMagnetiqActionCode_MagnetiqActionCodeDeclineInvalidAmount:                 false,
			EnumMagnetiqActionCode_MagnetiqActionCodeDeclineInvalidCardNumber:             false,
			EnumMagnetiqActionCode_MagnetiqActionCodeDeclineUnacceptableFee:               false,
			EnumMagnetiqActionCode_MagnetiqActionCodeDeclineNoAccountType:                 false,
			EnumMagnetiqActionCode_MagnetiqActionCodeDeclineFunctionNotSupported:          false,
			EnumMagnetiqActionCode_MagnetiqActionCodeDeclineInsufficientFunds:             false,
			EnumMagnetiqActionCode_MagnetiqActionCodeDeclineNoCardRecord:                  false,
			EnumMagnetiqActionCode_MagnetiqActionCodeDeclineNotPermittedToCardholder:      false,
			EnumMagnetiqActionCode_MagnetiqActionCodeDeclineNotPermittedToTerminal:        false,
			EnumMagnetiqActionCode_MagnetiqActionCodeDeclineExceedsWithdrawalLimit:        false,
			EnumMagnetiqActionCode_MagnetiqActionCodeDeclineSecurityViolation:             false,
			EnumMagnetiqActionCode_MagnetiqActionCodeDeclineExceedsWithdrawalFrequency:    false,
			EnumMagnetiqActionCode_MagnetiqActionCodeDeclineViolationOfLaw:                false,
			EnumMagnetiqActionCode_MagnetiqActionCodeDeclineCardNotEffective:              false,
			EnumMagnetiqActionCode_MagnetiqActionCodeDeclineSuspectedCounterfeitCard:      false,
			EnumMagnetiqActionCode_MagnetiqActionCodeDeclineAdditionalAuthRequired:        false,
			EnumMagnetiqActionCode_MagnetiqActionCodeDeclineWrongOnlinePinRetry:           false,
			EnumMagnetiqActionCode_MagnetiqActionCodeDeclineLifecycleViolation:            false,
			EnumMagnetiqActionCode_MagnetiqActionCodeDeclinePolicyViolation:               false,
			EnumMagnetiqActionCode_MagnetiqActionCodeDeclineFraudSecurityViolation:        false,
			EnumMagnetiqActionCode_MagnetiqActionCodeDeclineByCardholderWish:              false,
			EnumMagnetiqActionCode_MagnetiqActionCodeAdviceAcknowledgedNoLiability:        false,
			EnumMagnetiqActionCode_MagnetiqActionCodeAdviceAcknowledgedLiabilityAccepted:  false,
			EnumMagnetiqActionCode_MagnetiqActionCodeDeclineInvalidTransaction:            false,
			EnumMagnetiqActionCode_MagnetiqActionCodeStatusReEnterTransaction:             false,
			EnumMagnetiqActionCode_MagnetiqActionCodeDeclineFormatError:                   false,
			EnumMagnetiqActionCode_MagnetiqActionCodeDeclineIssuerOrSwitchInoperative:     false,
			EnumMagnetiqActionCode_MagnetiqActionCodeDeclineDestinationNotFound:           false,
			EnumMagnetiqActionCode_MagnetiqActionCodeDeclineSystemMalfunction:             false,
			EnumMagnetiqActionCode_MagnetiqActionCodeDeclineIssuerSignedOff:               false,
			EnumMagnetiqActionCode_MagnetiqActionCodeDeclineIssuerTimedOut:                false,
			EnumMagnetiqActionCode_MagnetiqActionCodeDeclineIssuerUnavailable:             false,
			EnumMagnetiqActionCode_MagnetiqActionCodeDeclineDuplicateTransmission:         false,
			EnumMagnetiqActionCode_MagnetiqActionCodeDeclineNotTraceableToOriginal:        false,
			EnumMagnetiqActionCode_MagnetiqActionCodeDeclineNoCommKeysAvailable:           false,
			EnumMagnetiqActionCode_MagnetiqActionCodeDeclineMessageOutOfSequence:          false,
			EnumMagnetiqActionCode_MagnetiqActionCodeStatusRequestInProgress:              false,
			EnumMagnetiqActionCode_MagnetiqActionCodeDeclineBusinessViolation:             false,
			EnumMagnetiqActionCode_MagnetiqActionCode3DSecureUnavailable:                  false,
			EnumMagnetiqActionCode_MagnetiqActionCode3DSecureAuthFailed:                   false,
			EnumMagnetiqActionCode_MagnetiqActionCodeAgreementNotObtained:                 false,
			EnumMagnetiqActionCode_MagnetiqActionCodeAgreementSuspendedOrClosed:           false,
			EnumMagnetiqActionCode_MagnetiqActionCodeOriginalCreditIneligible:             false,
			EnumMagnetiqActionCode_MagnetiqActionCode3DSecureEnrollmentVerificationFailed: false,
			EnumMagnetiqActionCode_MagnetiqActionCode3DSecurePaResExtractionFailed:        false,
			EnumMagnetiqActionCode_MagnetiqActionCodeSuspectedFraud:                       false,
			EnumMagnetiqActionCode_MagnetiqActionCodeAbandonedTimeout:                     false,
			EnumMagnetiqActionCode_MagnetiqActionCodeLimitsExceeded:                       false,
		},
	}
}

func IsEnumMagnetiqActionCode(target EnumMagnetiqActionCode, matches ...EnumMagnetiqActionCode) bool {
	for _, v := range matches {
		if target == v {
			return true
		}
	}

	return false
}

type EnumMagnetiqActionCodeUsage struct {
	enumMap map[EnumMagnetiqActionCode]bool
}

func (u *EnumMagnetiqActionCodeUsage) Use(slice ...EnumMagnetiqActionCode) *EnumMagnetiqActionCodeUsage {
	for _, in := range slice {
		u.enumMap[in] = true
	}

	return u
}

func (u *EnumMagnetiqActionCodeUsage) Check() []string {
	var result []string
	for k, v := range u.enumMap {
		if v {
			continue
		}

		result = append(result, k.String())
	}

	return result
}

func (u *EnumMagnetiqActionCodeUsage) EnumMagnetiqActionCode_MagnetiqActionCodeApproved() EnumMagnetiqActionCode {
	u.Use(EnumMagnetiqActionCode_MagnetiqActionCodeApproved)
	return EnumMagnetiqActionCode_MagnetiqActionCodeApproved
}

func (u *EnumMagnetiqActionCodeUsage) EnumMagnetiqActionCode_MagnetiqActionCodeDeclineGeneral() EnumMagnetiqActionCode {
	u.Use(EnumMagnetiqActionCode_MagnetiqActionCodeDeclineGeneral)
	return EnumMagnetiqActionCode_MagnetiqActionCodeDeclineGeneral
}

func (u *EnumMagnetiqActionCodeUsage) EnumMagnetiqActionCode_MagnetiqActionCodeDeclineExpiredCard() EnumMagnetiqActionCode {
	u.Use(EnumMagnetiqActionCode_MagnetiqActionCodeDeclineExpiredCard)
	return EnumMagnetiqActionCode_MagnetiqActionCodeDeclineExpiredCard
}

func (u *EnumMagnetiqActionCodeUsage) EnumMagnetiqActionCode_MagnetiqActionCodeDeclineSuspectedFraud() EnumMagnetiqActionCode {
	u.Use(EnumMagnetiqActionCode_MagnetiqActionCodeDeclineSuspectedFraud)
	return EnumMagnetiqActionCode_MagnetiqActionCodeDeclineSuspectedFraud
}

func (u *EnumMagnetiqActionCodeUsage) EnumMagnetiqActionCode_MagnetiqActionCodeDeclineContactAcquirer() EnumMagnetiqActionCode {
	u.Use(EnumMagnetiqActionCode_MagnetiqActionCodeDeclineContactAcquirer)
	return EnumMagnetiqActionCode_MagnetiqActionCodeDeclineContactAcquirer
}

func (u *EnumMagnetiqActionCodeUsage) EnumMagnetiqActionCode_MagnetiqActionCodeDeclineRestrictedCard() EnumMagnetiqActionCode {
	u.Use(EnumMagnetiqActionCode_MagnetiqActionCodeDeclineRestrictedCard)
	return EnumMagnetiqActionCode_MagnetiqActionCodeDeclineRestrictedCard
}

func (u *EnumMagnetiqActionCodeUsage) EnumMagnetiqActionCode_MagnetiqActionCodeDeclineCallSecurityDept() EnumMagnetiqActionCode {
	u.Use(EnumMagnetiqActionCode_MagnetiqActionCodeDeclineCallSecurityDept)
	return EnumMagnetiqActionCode_MagnetiqActionCodeDeclineCallSecurityDept
}

func (u *EnumMagnetiqActionCodeUsage) EnumMagnetiqActionCode_MagnetiqActionCodeDeclineReferToIssuer() EnumMagnetiqActionCode {
	u.Use(EnumMagnetiqActionCode_MagnetiqActionCodeDeclineReferToIssuer)
	return EnumMagnetiqActionCode_MagnetiqActionCodeDeclineReferToIssuer
}

func (u *EnumMagnetiqActionCodeUsage) EnumMagnetiqActionCode_MagnetiqActionCodeDeclineIssuerSpecialConditions() EnumMagnetiqActionCode {
	u.Use(EnumMagnetiqActionCode_MagnetiqActionCodeDeclineIssuerSpecialConditions)
	return EnumMagnetiqActionCode_MagnetiqActionCodeDeclineIssuerSpecialConditions
}

func (u *EnumMagnetiqActionCodeUsage) EnumMagnetiqActionCode_MagnetiqActionCodeDeclineInvalidMerchant() EnumMagnetiqActionCode {
	u.Use(EnumMagnetiqActionCode_MagnetiqActionCodeDeclineInvalidMerchant)
	return EnumMagnetiqActionCode_MagnetiqActionCodeDeclineInvalidMerchant
}

func (u *EnumMagnetiqActionCodeUsage) EnumMagnetiqActionCode_MagnetiqActionCodeDeclineInvalidAmount() EnumMagnetiqActionCode {
	u.Use(EnumMagnetiqActionCode_MagnetiqActionCodeDeclineInvalidAmount)
	return EnumMagnetiqActionCode_MagnetiqActionCodeDeclineInvalidAmount
}

func (u *EnumMagnetiqActionCodeUsage) EnumMagnetiqActionCode_MagnetiqActionCodeDeclineInvalidCardNumber() EnumMagnetiqActionCode {
	u.Use(EnumMagnetiqActionCode_MagnetiqActionCodeDeclineInvalidCardNumber)
	return EnumMagnetiqActionCode_MagnetiqActionCodeDeclineInvalidCardNumber
}

func (u *EnumMagnetiqActionCodeUsage) EnumMagnetiqActionCode_MagnetiqActionCodeDeclineUnacceptableFee() EnumMagnetiqActionCode {
	u.Use(EnumMagnetiqActionCode_MagnetiqActionCodeDeclineUnacceptableFee)
	return EnumMagnetiqActionCode_MagnetiqActionCodeDeclineUnacceptableFee
}

func (u *EnumMagnetiqActionCodeUsage) EnumMagnetiqActionCode_MagnetiqActionCodeDeclineNoAccountType() EnumMagnetiqActionCode {
	u.Use(EnumMagnetiqActionCode_MagnetiqActionCodeDeclineNoAccountType)
	return EnumMagnetiqActionCode_MagnetiqActionCodeDeclineNoAccountType
}

func (u *EnumMagnetiqActionCodeUsage) EnumMagnetiqActionCode_MagnetiqActionCodeDeclineFunctionNotSupported() EnumMagnetiqActionCode {
	u.Use(EnumMagnetiqActionCode_MagnetiqActionCodeDeclineFunctionNotSupported)
	return EnumMagnetiqActionCode_MagnetiqActionCodeDeclineFunctionNotSupported
}

func (u *EnumMagnetiqActionCodeUsage) EnumMagnetiqActionCode_MagnetiqActionCodeDeclineInsufficientFunds() EnumMagnetiqActionCode {
	u.Use(EnumMagnetiqActionCode_MagnetiqActionCodeDeclineInsufficientFunds)
	return EnumMagnetiqActionCode_MagnetiqActionCodeDeclineInsufficientFunds
}

func (u *EnumMagnetiqActionCodeUsage) EnumMagnetiqActionCode_MagnetiqActionCodeDeclineNoCardRecord() EnumMagnetiqActionCode {
	u.Use(EnumMagnetiqActionCode_MagnetiqActionCodeDeclineNoCardRecord)
	return EnumMagnetiqActionCode_MagnetiqActionCodeDeclineNoCardRecord
}

func (u *EnumMagnetiqActionCodeUsage) EnumMagnetiqActionCode_MagnetiqActionCodeDeclineNotPermittedToCardholder() EnumMagnetiqActionCode {
	u.Use(EnumMagnetiqActionCode_MagnetiqActionCodeDeclineNotPermittedToCardholder)
	return EnumMagnetiqActionCode_MagnetiqActionCodeDeclineNotPermittedToCardholder
}

func (u *EnumMagnetiqActionCodeUsage) EnumMagnetiqActionCode_MagnetiqActionCodeDeclineNotPermittedToTerminal() EnumMagnetiqActionCode {
	u.Use(EnumMagnetiqActionCode_MagnetiqActionCodeDeclineNotPermittedToTerminal)
	return EnumMagnetiqActionCode_MagnetiqActionCodeDeclineNotPermittedToTerminal
}

func (u *EnumMagnetiqActionCodeUsage) EnumMagnetiqActionCode_MagnetiqActionCodeDeclineExceedsWithdrawalLimit() EnumMagnetiqActionCode {
	u.Use(EnumMagnetiqActionCode_MagnetiqActionCodeDeclineExceedsWithdrawalLimit)
	return EnumMagnetiqActionCode_MagnetiqActionCodeDeclineExceedsWithdrawalLimit
}

func (u *EnumMagnetiqActionCodeUsage) EnumMagnetiqActionCode_MagnetiqActionCodeDeclineSecurityViolation() EnumMagnetiqActionCode {
	u.Use(EnumMagnetiqActionCode_MagnetiqActionCodeDeclineSecurityViolation)
	return EnumMagnetiqActionCode_MagnetiqActionCodeDeclineSecurityViolation
}

func (u *EnumMagnetiqActionCodeUsage) EnumMagnetiqActionCode_MagnetiqActionCodeDeclineExceedsWithdrawalFrequency() EnumMagnetiqActionCode {
	u.Use(EnumMagnetiqActionCode_MagnetiqActionCodeDeclineExceedsWithdrawalFrequency)
	return EnumMagnetiqActionCode_MagnetiqActionCodeDeclineExceedsWithdrawalFrequency
}

func (u *EnumMagnetiqActionCodeUsage) EnumMagnetiqActionCode_MagnetiqActionCodeDeclineViolationOfLaw() EnumMagnetiqActionCode {
	u.Use(EnumMagnetiqActionCode_MagnetiqActionCodeDeclineViolationOfLaw)
	return EnumMagnetiqActionCode_MagnetiqActionCodeDeclineViolationOfLaw
}

func (u *EnumMagnetiqActionCodeUsage) EnumMagnetiqActionCode_MagnetiqActionCodeDeclineCardNotEffective() EnumMagnetiqActionCode {
	u.Use(EnumMagnetiqActionCode_MagnetiqActionCodeDeclineCardNotEffective)
	return EnumMagnetiqActionCode_MagnetiqActionCodeDeclineCardNotEffective
}

func (u *EnumMagnetiqActionCodeUsage) EnumMagnetiqActionCode_MagnetiqActionCodeDeclineSuspectedCounterfeitCard() EnumMagnetiqActionCode {
	u.Use(EnumMagnetiqActionCode_MagnetiqActionCodeDeclineSuspectedCounterfeitCard)
	return EnumMagnetiqActionCode_MagnetiqActionCodeDeclineSuspectedCounterfeitCard
}

func (u *EnumMagnetiqActionCodeUsage) EnumMagnetiqActionCode_MagnetiqActionCodeDeclineAdditionalAuthRequired() EnumMagnetiqActionCode {
	u.Use(EnumMagnetiqActionCode_MagnetiqActionCodeDeclineAdditionalAuthRequired)
	return EnumMagnetiqActionCode_MagnetiqActionCodeDeclineAdditionalAuthRequired
}

func (u *EnumMagnetiqActionCodeUsage) EnumMagnetiqActionCode_MagnetiqActionCodeDeclineWrongOnlinePinRetry() EnumMagnetiqActionCode {
	u.Use(EnumMagnetiqActionCode_MagnetiqActionCodeDeclineWrongOnlinePinRetry)
	return EnumMagnetiqActionCode_MagnetiqActionCodeDeclineWrongOnlinePinRetry
}

func (u *EnumMagnetiqActionCodeUsage) EnumMagnetiqActionCode_MagnetiqActionCodeDeclineLifecycleViolation() EnumMagnetiqActionCode {
	u.Use(EnumMagnetiqActionCode_MagnetiqActionCodeDeclineLifecycleViolation)
	return EnumMagnetiqActionCode_MagnetiqActionCodeDeclineLifecycleViolation
}

func (u *EnumMagnetiqActionCodeUsage) EnumMagnetiqActionCode_MagnetiqActionCodeDeclinePolicyViolation() EnumMagnetiqActionCode {
	u.Use(EnumMagnetiqActionCode_MagnetiqActionCodeDeclinePolicyViolation)
	return EnumMagnetiqActionCode_MagnetiqActionCodeDeclinePolicyViolation
}

func (u *EnumMagnetiqActionCodeUsage) EnumMagnetiqActionCode_MagnetiqActionCodeDeclineFraudSecurityViolation() EnumMagnetiqActionCode {
	u.Use(EnumMagnetiqActionCode_MagnetiqActionCodeDeclineFraudSecurityViolation)
	return EnumMagnetiqActionCode_MagnetiqActionCodeDeclineFraudSecurityViolation
}

func (u *EnumMagnetiqActionCodeUsage) EnumMagnetiqActionCode_MagnetiqActionCodeDeclineByCardholderWish() EnumMagnetiqActionCode {
	u.Use(EnumMagnetiqActionCode_MagnetiqActionCodeDeclineByCardholderWish)
	return EnumMagnetiqActionCode_MagnetiqActionCodeDeclineByCardholderWish
}

func (u *EnumMagnetiqActionCodeUsage) EnumMagnetiqActionCode_MagnetiqActionCodeAdviceAcknowledgedNoLiability() EnumMagnetiqActionCode {
	u.Use(EnumMagnetiqActionCode_MagnetiqActionCodeAdviceAcknowledgedNoLiability)
	return EnumMagnetiqActionCode_MagnetiqActionCodeAdviceAcknowledgedNoLiability
}

func (u *EnumMagnetiqActionCodeUsage) EnumMagnetiqActionCode_MagnetiqActionCodeAdviceAcknowledgedLiabilityAccepted() EnumMagnetiqActionCode {
	u.Use(EnumMagnetiqActionCode_MagnetiqActionCodeAdviceAcknowledgedLiabilityAccepted)
	return EnumMagnetiqActionCode_MagnetiqActionCodeAdviceAcknowledgedLiabilityAccepted
}

func (u *EnumMagnetiqActionCodeUsage) EnumMagnetiqActionCode_MagnetiqActionCodeDeclineInvalidTransaction() EnumMagnetiqActionCode {
	u.Use(EnumMagnetiqActionCode_MagnetiqActionCodeDeclineInvalidTransaction)
	return EnumMagnetiqActionCode_MagnetiqActionCodeDeclineInvalidTransaction
}

func (u *EnumMagnetiqActionCodeUsage) EnumMagnetiqActionCode_MagnetiqActionCodeStatusReEnterTransaction() EnumMagnetiqActionCode {
	u.Use(EnumMagnetiqActionCode_MagnetiqActionCodeStatusReEnterTransaction)
	return EnumMagnetiqActionCode_MagnetiqActionCodeStatusReEnterTransaction
}

func (u *EnumMagnetiqActionCodeUsage) EnumMagnetiqActionCode_MagnetiqActionCodeDeclineFormatError() EnumMagnetiqActionCode {
	u.Use(EnumMagnetiqActionCode_MagnetiqActionCodeDeclineFormatError)
	return EnumMagnetiqActionCode_MagnetiqActionCodeDeclineFormatError
}

func (u *EnumMagnetiqActionCodeUsage) EnumMagnetiqActionCode_MagnetiqActionCodeDeclineIssuerOrSwitchInoperative() EnumMagnetiqActionCode {
	u.Use(EnumMagnetiqActionCode_MagnetiqActionCodeDeclineIssuerOrSwitchInoperative)
	return EnumMagnetiqActionCode_MagnetiqActionCodeDeclineIssuerOrSwitchInoperative
}

func (u *EnumMagnetiqActionCodeUsage) EnumMagnetiqActionCode_MagnetiqActionCodeDeclineDestinationNotFound() EnumMagnetiqActionCode {
	u.Use(EnumMagnetiqActionCode_MagnetiqActionCodeDeclineDestinationNotFound)
	return EnumMagnetiqActionCode_MagnetiqActionCodeDeclineDestinationNotFound
}

func (u *EnumMagnetiqActionCodeUsage) EnumMagnetiqActionCode_MagnetiqActionCodeDeclineSystemMalfunction() EnumMagnetiqActionCode {
	u.Use(EnumMagnetiqActionCode_MagnetiqActionCodeDeclineSystemMalfunction)
	return EnumMagnetiqActionCode_MagnetiqActionCodeDeclineSystemMalfunction
}

func (u *EnumMagnetiqActionCodeUsage) EnumMagnetiqActionCode_MagnetiqActionCodeDeclineIssuerSignedOff() EnumMagnetiqActionCode {
	u.Use(EnumMagnetiqActionCode_MagnetiqActionCodeDeclineIssuerSignedOff)
	return EnumMagnetiqActionCode_MagnetiqActionCodeDeclineIssuerSignedOff
}

func (u *EnumMagnetiqActionCodeUsage) EnumMagnetiqActionCode_MagnetiqActionCodeDeclineIssuerTimedOut() EnumMagnetiqActionCode {
	u.Use(EnumMagnetiqActionCode_MagnetiqActionCodeDeclineIssuerTimedOut)
	return EnumMagnetiqActionCode_MagnetiqActionCodeDeclineIssuerTimedOut
}

func (u *EnumMagnetiqActionCodeUsage) EnumMagnetiqActionCode_MagnetiqActionCodeDeclineIssuerUnavailable() EnumMagnetiqActionCode {
	u.Use(EnumMagnetiqActionCode_MagnetiqActionCodeDeclineIssuerUnavailable)
	return EnumMagnetiqActionCode_MagnetiqActionCodeDeclineIssuerUnavailable
}

func (u *EnumMagnetiqActionCodeUsage) EnumMagnetiqActionCode_MagnetiqActionCodeDeclineDuplicateTransmission() EnumMagnetiqActionCode {
	u.Use(EnumMagnetiqActionCode_MagnetiqActionCodeDeclineDuplicateTransmission)
	return EnumMagnetiqActionCode_MagnetiqActionCodeDeclineDuplicateTransmission
}

func (u *EnumMagnetiqActionCodeUsage) EnumMagnetiqActionCode_MagnetiqActionCodeDeclineNotTraceableToOriginal() EnumMagnetiqActionCode {
	u.Use(EnumMagnetiqActionCode_MagnetiqActionCodeDeclineNotTraceableToOriginal)
	return EnumMagnetiqActionCode_MagnetiqActionCodeDeclineNotTraceableToOriginal
}

func (u *EnumMagnetiqActionCodeUsage) EnumMagnetiqActionCode_MagnetiqActionCodeDeclineNoCommKeysAvailable() EnumMagnetiqActionCode {
	u.Use(EnumMagnetiqActionCode_MagnetiqActionCodeDeclineNoCommKeysAvailable)
	return EnumMagnetiqActionCode_MagnetiqActionCodeDeclineNoCommKeysAvailable
}

func (u *EnumMagnetiqActionCodeUsage) EnumMagnetiqActionCode_MagnetiqActionCodeDeclineMessageOutOfSequence() EnumMagnetiqActionCode {
	u.Use(EnumMagnetiqActionCode_MagnetiqActionCodeDeclineMessageOutOfSequence)
	return EnumMagnetiqActionCode_MagnetiqActionCodeDeclineMessageOutOfSequence
}

func (u *EnumMagnetiqActionCodeUsage) EnumMagnetiqActionCode_MagnetiqActionCodeStatusRequestInProgress() EnumMagnetiqActionCode {
	u.Use(EnumMagnetiqActionCode_MagnetiqActionCodeStatusRequestInProgress)
	return EnumMagnetiqActionCode_MagnetiqActionCodeStatusRequestInProgress
}

func (u *EnumMagnetiqActionCodeUsage) EnumMagnetiqActionCode_MagnetiqActionCodeDeclineBusinessViolation() EnumMagnetiqActionCode {
	u.Use(EnumMagnetiqActionCode_MagnetiqActionCodeDeclineBusinessViolation)
	return EnumMagnetiqActionCode_MagnetiqActionCodeDeclineBusinessViolation
}

func (u *EnumMagnetiqActionCodeUsage) EnumMagnetiqActionCode_MagnetiqActionCode3DSecureUnavailable() EnumMagnetiqActionCode {
	u.Use(EnumMagnetiqActionCode_MagnetiqActionCode3DSecureUnavailable)
	return EnumMagnetiqActionCode_MagnetiqActionCode3DSecureUnavailable
}

func (u *EnumMagnetiqActionCodeUsage) EnumMagnetiqActionCode_MagnetiqActionCode3DSecureAuthFailed() EnumMagnetiqActionCode {
	u.Use(EnumMagnetiqActionCode_MagnetiqActionCode3DSecureAuthFailed)
	return EnumMagnetiqActionCode_MagnetiqActionCode3DSecureAuthFailed
}

func (u *EnumMagnetiqActionCodeUsage) EnumMagnetiqActionCode_MagnetiqActionCodeAgreementNotObtained() EnumMagnetiqActionCode {
	u.Use(EnumMagnetiqActionCode_MagnetiqActionCodeAgreementNotObtained)
	return EnumMagnetiqActionCode_MagnetiqActionCodeAgreementNotObtained
}

func (u *EnumMagnetiqActionCodeUsage) EnumMagnetiqActionCode_MagnetiqActionCodeAgreementSuspendedOrClosed() EnumMagnetiqActionCode {
	u.Use(EnumMagnetiqActionCode_MagnetiqActionCodeAgreementSuspendedOrClosed)
	return EnumMagnetiqActionCode_MagnetiqActionCodeAgreementSuspendedOrClosed
}

func (u *EnumMagnetiqActionCodeUsage) EnumMagnetiqActionCode_MagnetiqActionCodeOriginalCreditIneligible() EnumMagnetiqActionCode {
	u.Use(EnumMagnetiqActionCode_MagnetiqActionCodeOriginalCreditIneligible)
	return EnumMagnetiqActionCode_MagnetiqActionCodeOriginalCreditIneligible
}

func (u *EnumMagnetiqActionCodeUsage) EnumMagnetiqActionCode_MagnetiqActionCode3DSecureEnrollmentVerificationFailed() EnumMagnetiqActionCode {
	u.Use(EnumMagnetiqActionCode_MagnetiqActionCode3DSecureEnrollmentVerificationFailed)
	return EnumMagnetiqActionCode_MagnetiqActionCode3DSecureEnrollmentVerificationFailed
}

func (u *EnumMagnetiqActionCodeUsage) EnumMagnetiqActionCode_MagnetiqActionCode3DSecurePaResExtractionFailed() EnumMagnetiqActionCode {
	u.Use(EnumMagnetiqActionCode_MagnetiqActionCode3DSecurePaResExtractionFailed)
	return EnumMagnetiqActionCode_MagnetiqActionCode3DSecurePaResExtractionFailed
}

func (u *EnumMagnetiqActionCodeUsage) EnumMagnetiqActionCode_MagnetiqActionCodeSuspectedFraud() EnumMagnetiqActionCode {
	u.Use(EnumMagnetiqActionCode_MagnetiqActionCodeSuspectedFraud)
	return EnumMagnetiqActionCode_MagnetiqActionCodeSuspectedFraud
}

func (u *EnumMagnetiqActionCodeUsage) EnumMagnetiqActionCode_MagnetiqActionCodeAbandonedTimeout() EnumMagnetiqActionCode {
	u.Use(EnumMagnetiqActionCode_MagnetiqActionCodeAbandonedTimeout)
	return EnumMagnetiqActionCode_MagnetiqActionCodeAbandonedTimeout
}

func (u *EnumMagnetiqActionCodeUsage) EnumMagnetiqActionCode_MagnetiqActionCodeLimitsExceeded() EnumMagnetiqActionCode {
	u.Use(EnumMagnetiqActionCode_MagnetiqActionCodeLimitsExceeded)
	return EnumMagnetiqActionCode_MagnetiqActionCodeLimitsExceeded
}

func (val EnumMagnetiqModeCode) Synonym() EnumMagnetiqModeCode {
	if _, ok := EnumMagnetiqModeCode_name[int32(val)]; ok {
		return val
	}

	return EnumMagnetiqModeCode(math.MinInt32)
}

func (val EnumMagnetiqModeCode) Int() int {
	return int(val.Synonym())
}

func (val EnumMagnetiqModeCode) IntPtr() *int {
	result := int(val.Synonym())
	return &result
}

func (val EnumMagnetiqModeCode) Int32() int32 {
	return int32(val.Synonym())
}

func (val EnumMagnetiqModeCode) Int32Ptr() *int32 {
	result := int32(val.Synonym())
	return &result
}

func (val EnumMagnetiqModeCode) Int64() int64 {
	return int64(val.Synonym())
}

func (val EnumMagnetiqModeCode) Int64Ptr() *int64 {
	result := int64(val.Synonym())
	return &result
}

func (val EnumMagnetiqModeCode) Uint() uint {
	return uint(val.Synonym())
}

func (val EnumMagnetiqModeCode) UintPtr() *uint {
	result := uint(val.Synonym())
	return &result
}

func (val EnumMagnetiqModeCode) Uint32() uint32 {
	return uint32(val.Synonym())
}

func (val EnumMagnetiqModeCode) Uint32Ptr() *uint32 {
	result := uint32(val.Synonym())
	return &result
}

func (val EnumMagnetiqModeCode) Uint64() uint64 {
	return uint64(val.Synonym())
}

func (val EnumMagnetiqModeCode) Uint64Ptr() *uint64 {
	result := uint64(val.Synonym())
	return &result
}

func (val EnumMagnetiqModeCode) IsKnown() bool {
	return val.Synonym() != EnumMagnetiqModeCode(math.MinInt32)
}

func ConvertIntToEnumMagnetiqModeCode(in int) EnumMagnetiqModeCode {
	return EnumMagnetiqModeCode(in).Synonym()
}

func ConvertUintToEnumMagnetiqModeCode(in uint) EnumMagnetiqModeCode {
	return EnumMagnetiqModeCode(in).Synonym()
}

func ConvertInt32ToEnumMagnetiqModeCode(in int32) EnumMagnetiqModeCode {
	return EnumMagnetiqModeCode(in).Synonym()
}

func ConvertUint32ToEnumMagnetiqModeCode(in uint32) EnumMagnetiqModeCode {
	return EnumMagnetiqModeCode(in).Synonym()
}

func ConvertInt64ToEnumMagnetiqModeCode(in int64) EnumMagnetiqModeCode {
	return EnumMagnetiqModeCode(in).Synonym()
}

func ConvertUint64ToEnumMagnetiqModeCode(in uint64) EnumMagnetiqModeCode {
	return EnumMagnetiqModeCode(in).Synonym()
}

var EnumMagnetiqModeCode_Lower_value = map[string]EnumMagnetiqModeCode{
	"magnetiqmodenone3ds":       0,
	"magnetiqmode3ds":           1,
	"magnetiqmodeoneclickpayin": 2,
}

func ConvertStringToEnumMagnetiqModeCode(in string) EnumMagnetiqModeCode {
	if result, ok := EnumMagnetiqModeCode_value[in]; ok {
		return EnumMagnetiqModeCode(result)
	}

	if result, ok := EnumMagnetiqModeCode_Lower_value[strings.ToLower(in)]; ok {
		return EnumMagnetiqModeCode(result)
	}

	return EnumMagnetiqModeCode(math.MinInt32)
}

var SliceEnumMagnetiqModeCodeConvert *sliceEnumMagnetiqModeCodeConvert

type sliceEnumMagnetiqModeCodeConvert struct{}

func (*sliceEnumMagnetiqModeCodeConvert) Synonym(in []EnumMagnetiqModeCode) []EnumMagnetiqModeCode {
	result := make([]EnumMagnetiqModeCode, len(in))
	for i, v := range in {
		result[i] = v.Synonym()
	}

	return result
}

func (*sliceEnumMagnetiqModeCodeConvert) Int32(in []EnumMagnetiqModeCode) []int32 {
	result := make([]int32, len(in))
	for i, v := range in {
		result[i] = v.Int32()
	}

	return result
}

func (*sliceEnumMagnetiqModeCodeConvert) Uint32(in []EnumMagnetiqModeCode) []uint32 {
	result := make([]uint32, len(in))
	for i, v := range in {
		result[i] = v.Uint32()
	}

	return result
}

func (*sliceEnumMagnetiqModeCodeConvert) Uint64(in []EnumMagnetiqModeCode) []uint64 {
	result := make([]uint64, len(in))
	for i, v := range in {
		result[i] = v.Uint64()
	}

	return result
}

func (*sliceEnumMagnetiqModeCodeConvert) Int64(in []EnumMagnetiqModeCode) []int64 {
	result := make([]int64, len(in))
	for i, v := range in {
		result[i] = v.Int64()
	}

	return result
}

func (*sliceEnumMagnetiqModeCodeConvert) Uint(in []EnumMagnetiqModeCode) []uint {
	result := make([]uint, len(in))
	for i, v := range in {
		result[i] = v.Uint()
	}

	return result
}

func (*sliceEnumMagnetiqModeCodeConvert) Int(in []EnumMagnetiqModeCode) []int {
	result := make([]int, len(in))
	for i, v := range in {
		result[i] = v.Int()
	}

	return result
}

func (*sliceEnumMagnetiqModeCodeConvert) IsKnown(in []EnumMagnetiqModeCode) []bool {
	result := make([]bool, len(in))
	for i, v := range in {
		result[i] = v.IsKnown()
	}

	return result
}

func (*sliceEnumMagnetiqModeCodeConvert) ConvertIntToEnumMagnetiqModeCode(in []int) []EnumMagnetiqModeCode {
	result := make([]EnumMagnetiqModeCode, len(in))
	for i, v := range in {
		result[i] = ConvertIntToEnumMagnetiqModeCode(v)
	}

	return result
}

func (*sliceEnumMagnetiqModeCodeConvert) ConvertUintToEnumMagnetiqModeCode(in []uint) []EnumMagnetiqModeCode {
	result := make([]EnumMagnetiqModeCode, len(in))
	for i, v := range in {
		result[i] = ConvertUintToEnumMagnetiqModeCode(v)
	}

	return result
}

func (*sliceEnumMagnetiqModeCodeConvert) ConvertInt32ToEnumMagnetiqModeCode(in []int32) []EnumMagnetiqModeCode {
	result := make([]EnumMagnetiqModeCode, len(in))
	for i, v := range in {
		result[i] = ConvertInt32ToEnumMagnetiqModeCode(v)
	}

	return result
}

func (*sliceEnumMagnetiqModeCodeConvert) ConvertUint32ToEnumMagnetiqModeCode(in []uint32) []EnumMagnetiqModeCode {
	result := make([]EnumMagnetiqModeCode, len(in))
	for i, v := range in {
		result[i] = ConvertUint32ToEnumMagnetiqModeCode(v)
	}

	return result
}

func (*sliceEnumMagnetiqModeCodeConvert) ConvertInt64ToEnumMagnetiqModeCode(in []int64) []EnumMagnetiqModeCode {
	result := make([]EnumMagnetiqModeCode, len(in))
	for i, v := range in {
		result[i] = ConvertInt64ToEnumMagnetiqModeCode(v)
	}

	return result
}

func (*sliceEnumMagnetiqModeCodeConvert) ConvertUint64ToEnumMagnetiqModeCode(in []uint64) []EnumMagnetiqModeCode {
	result := make([]EnumMagnetiqModeCode, len(in))
	for i, v := range in {
		result[i] = ConvertUint64ToEnumMagnetiqModeCode(v)
	}

	return result
}

func (*sliceEnumMagnetiqModeCodeConvert) ConvertStringToEnumMagnetiqModeCode(in []string) []EnumMagnetiqModeCode {
	result := make([]EnumMagnetiqModeCode, len(in))
	for i, v := range in {
		result[i] = ConvertStringToEnumMagnetiqModeCode(v)
	}

	return result
}

func NewEnumMagnetiqModeCodeUsage() *EnumMagnetiqModeCodeUsage {
	return &EnumMagnetiqModeCodeUsage{
		enumMap: map[EnumMagnetiqModeCode]bool{
			EnumMagnetiqModeCode_MagnetiqModeNone3DS:       false,
			EnumMagnetiqModeCode_MagnetiqMode3DS:           false,
			EnumMagnetiqModeCode_MagnetiqModeOneClickPayIn: false,
		},
	}
}

func IsEnumMagnetiqModeCode(target EnumMagnetiqModeCode, matches ...EnumMagnetiqModeCode) bool {
	for _, v := range matches {
		if target == v {
			return true
		}
	}

	return false
}

type EnumMagnetiqModeCodeUsage struct {
	enumMap map[EnumMagnetiqModeCode]bool
}

func (u *EnumMagnetiqModeCodeUsage) Use(slice ...EnumMagnetiqModeCode) *EnumMagnetiqModeCodeUsage {
	for _, in := range slice {
		u.enumMap[in] = true
	}

	return u
}

func (u *EnumMagnetiqModeCodeUsage) Check() []string {
	var result []string
	for k, v := range u.enumMap {
		if v {
			continue
		}

		result = append(result, k.String())
	}

	return result
}

func (u *EnumMagnetiqModeCodeUsage) EnumMagnetiqModeCode_MagnetiqModeNone3DS() EnumMagnetiqModeCode {
	u.Use(EnumMagnetiqModeCode_MagnetiqModeNone3DS)
	return EnumMagnetiqModeCode_MagnetiqModeNone3DS
}

func (u *EnumMagnetiqModeCodeUsage) EnumMagnetiqModeCode_MagnetiqMode3DS() EnumMagnetiqModeCode {
	u.Use(EnumMagnetiqModeCode_MagnetiqMode3DS)
	return EnumMagnetiqModeCode_MagnetiqMode3DS
}

func (u *EnumMagnetiqModeCodeUsage) EnumMagnetiqModeCode_MagnetiqModeOneClickPayIn() EnumMagnetiqModeCode {
	u.Use(EnumMagnetiqModeCode_MagnetiqModeOneClickPayIn)
	return EnumMagnetiqModeCode_MagnetiqModeOneClickPayIn
}
