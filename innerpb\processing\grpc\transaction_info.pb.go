// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.5
// 	protoc        v5.29.3
// source: inner/processing/grpc/transaction_info.proto

package grpc

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
	structpb "google.golang.org/protobuf/types/known/structpb"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type UpdateJobsMessageRequestV1 struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	TransactionId *uint64                `protobuf:"varint,1,opt,name=transaction_id,json=transactionId" json:"transaction_id,omitempty"`
	JobsMessage   *structpb.Struct       `protobuf:"bytes,2,opt,name=jobs_message,json=jobsMessage" json:"jobs_message,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UpdateJobsMessageRequestV1) Reset() {
	*x = UpdateJobsMessageRequestV1{}
	mi := &file_inner_processing_grpc_transaction_info_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdateJobsMessageRequestV1) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateJobsMessageRequestV1) ProtoMessage() {}

func (x *UpdateJobsMessageRequestV1) ProtoReflect() protoreflect.Message {
	mi := &file_inner_processing_grpc_transaction_info_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateJobsMessageRequestV1.ProtoReflect.Descriptor instead.
func (*UpdateJobsMessageRequestV1) Descriptor() ([]byte, []int) {
	return file_inner_processing_grpc_transaction_info_proto_rawDescGZIP(), []int{0}
}

func (x *UpdateJobsMessageRequestV1) GetTransactionId() uint64 {
	if x != nil && x.TransactionId != nil {
		return *x.TransactionId
	}
	return 0
}

func (x *UpdateJobsMessageRequestV1) GetJobsMessage() *structpb.Struct {
	if x != nil {
		return x.JobsMessage
	}
	return nil
}

type UpdateBankResponseMessageRequestV1 struct {
	state                   protoimpl.MessageState `protogen:"open.v1"`
	TransactionId           *uint64                `protobuf:"varint,1,opt,name=transaction_id,json=transactionId" json:"transaction_id,omitempty"`
	BankCode                *string                `protobuf:"bytes,2,opt,name=bank_code,json=bankCode" json:"bank_code,omitempty"`
	BankMessage             *string                `protobuf:"bytes,3,opt,name=bank_message,json=bankMessage" json:"bank_message,omitempty"`
	IntegrationErrorCode    *string                `protobuf:"bytes,4,opt,name=integration_error_code,json=integrationErrorCode" json:"integration_error_code,omitempty"`
	IntegrationErrorMessage *string                `protobuf:"bytes,5,opt,name=integration_error_message,json=integrationErrorMessage" json:"integration_error_message,omitempty"`
	unknownFields           protoimpl.UnknownFields
	sizeCache               protoimpl.SizeCache
}

func (x *UpdateBankResponseMessageRequestV1) Reset() {
	*x = UpdateBankResponseMessageRequestV1{}
	mi := &file_inner_processing_grpc_transaction_info_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdateBankResponseMessageRequestV1) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateBankResponseMessageRequestV1) ProtoMessage() {}

func (x *UpdateBankResponseMessageRequestV1) ProtoReflect() protoreflect.Message {
	mi := &file_inner_processing_grpc_transaction_info_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateBankResponseMessageRequestV1.ProtoReflect.Descriptor instead.
func (*UpdateBankResponseMessageRequestV1) Descriptor() ([]byte, []int) {
	return file_inner_processing_grpc_transaction_info_proto_rawDescGZIP(), []int{1}
}

func (x *UpdateBankResponseMessageRequestV1) GetTransactionId() uint64 {
	if x != nil && x.TransactionId != nil {
		return *x.TransactionId
	}
	return 0
}

func (x *UpdateBankResponseMessageRequestV1) GetBankCode() string {
	if x != nil && x.BankCode != nil {
		return *x.BankCode
	}
	return ""
}

func (x *UpdateBankResponseMessageRequestV1) GetBankMessage() string {
	if x != nil && x.BankMessage != nil {
		return *x.BankMessage
	}
	return ""
}

func (x *UpdateBankResponseMessageRequestV1) GetIntegrationErrorCode() string {
	if x != nil && x.IntegrationErrorCode != nil {
		return *x.IntegrationErrorCode
	}
	return ""
}

func (x *UpdateBankResponseMessageRequestV1) GetIntegrationErrorMessage() string {
	if x != nil && x.IntegrationErrorMessage != nil {
		return *x.IntegrationErrorMessage
	}
	return ""
}

type GetTransactionsWithEmptyBankReferenceIDResponseV1 struct {
	state          protoimpl.MessageState `protogen:"open.v1"`
	TransactionIds []*TransactionDataV1   `protobuf:"bytes,1,rep,name=transaction_ids,json=transactionIds" json:"transaction_ids,omitempty"`
	unknownFields  protoimpl.UnknownFields
	sizeCache      protoimpl.SizeCache
}

func (x *GetTransactionsWithEmptyBankReferenceIDResponseV1) Reset() {
	*x = GetTransactionsWithEmptyBankReferenceIDResponseV1{}
	mi := &file_inner_processing_grpc_transaction_info_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetTransactionsWithEmptyBankReferenceIDResponseV1) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetTransactionsWithEmptyBankReferenceIDResponseV1) ProtoMessage() {}

func (x *GetTransactionsWithEmptyBankReferenceIDResponseV1) ProtoReflect() protoreflect.Message {
	mi := &file_inner_processing_grpc_transaction_info_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetTransactionsWithEmptyBankReferenceIDResponseV1.ProtoReflect.Descriptor instead.
func (*GetTransactionsWithEmptyBankReferenceIDResponseV1) Descriptor() ([]byte, []int) {
	return file_inner_processing_grpc_transaction_info_proto_rawDescGZIP(), []int{2}
}

func (x *GetTransactionsWithEmptyBankReferenceIDResponseV1) GetTransactionIds() []*TransactionDataV1 {
	if x != nil {
		return x.TransactionIds
	}
	return nil
}

type UpdateBankReferenceIDRequestV1 struct {
	state           protoimpl.MessageState `protogen:"open.v1"`
	TransactionId   *uint64                `protobuf:"varint,1,opt,name=transaction_id,json=transactionId" json:"transaction_id,omitempty"`
	BankReferenceId *string                `protobuf:"bytes,2,opt,name=bank_reference_id,json=bankReferenceId" json:"bank_reference_id,omitempty"`
	unknownFields   protoimpl.UnknownFields
	sizeCache       protoimpl.SizeCache
}

func (x *UpdateBankReferenceIDRequestV1) Reset() {
	*x = UpdateBankReferenceIDRequestV1{}
	mi := &file_inner_processing_grpc_transaction_info_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdateBankReferenceIDRequestV1) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateBankReferenceIDRequestV1) ProtoMessage() {}

func (x *UpdateBankReferenceIDRequestV1) ProtoReflect() protoreflect.Message {
	mi := &file_inner_processing_grpc_transaction_info_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateBankReferenceIDRequestV1.ProtoReflect.Descriptor instead.
func (*UpdateBankReferenceIDRequestV1) Descriptor() ([]byte, []int) {
	return file_inner_processing_grpc_transaction_info_proto_rawDescGZIP(), []int{3}
}

func (x *UpdateBankReferenceIDRequestV1) GetTransactionId() uint64 {
	if x != nil && x.TransactionId != nil {
		return *x.TransactionId
	}
	return 0
}

func (x *UpdateBankReferenceIDRequestV1) GetBankReferenceId() string {
	if x != nil && x.BankReferenceId != nil {
		return *x.BankReferenceId
	}
	return ""
}

var File_inner_processing_grpc_transaction_info_proto protoreflect.FileDescriptor

var file_inner_processing_grpc_transaction_info_proto_rawDesc = string([]byte{
	0x0a, 0x2c, 0x69, 0x6e, 0x6e, 0x65, 0x72, 0x2f, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69,
	0x6e, 0x67, 0x2f, 0x67, 0x72, 0x70, 0x63, 0x2f, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74,
	0x69, 0x6f, 0x6e, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x27,
	0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x2e, 0x74, 0x72, 0x61, 0x6e, 0x73,
	0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69,
	0x6f, 0x6e, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x1a, 0x1c, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x73, 0x74, 0x72, 0x75, 0x63, 0x74, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1b, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x65, 0x6d, 0x70, 0x74, 0x79, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x1a, 0x27, 0x69, 0x6e, 0x6e, 0x65, 0x72, 0x2f, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73,
	0x73, 0x69, 0x6e, 0x67, 0x2f, 0x67, 0x72, 0x70, 0x63, 0x2f, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61,
	0x63, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x7f, 0x0a, 0x1a, 0x55,
	0x70, 0x64, 0x61, 0x74, 0x65, 0x4a, 0x6f, 0x62, 0x73, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x56, 0x31, 0x12, 0x25, 0x0a, 0x0e, 0x74, 0x72, 0x61,
	0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x04, 0x52, 0x0d, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x64,
	0x12, 0x3a, 0x0a, 0x0c, 0x6a, 0x6f, 0x62, 0x73, 0x5f, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x17, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x53, 0x74, 0x72, 0x75, 0x63, 0x74, 0x52,
	0x0b, 0x6a, 0x6f, 0x62, 0x73, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x22, 0xfd, 0x01, 0x0a,
	0x22, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x42, 0x61, 0x6e, 0x6b, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x56, 0x31, 0x12, 0x25, 0x0a, 0x0e, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69,
	0x6f, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x0d, 0x74, 0x72, 0x61,
	0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x12, 0x1b, 0x0a, 0x09, 0x62, 0x61,
	0x6e, 0x6b, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x62,
	0x61, 0x6e, 0x6b, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x21, 0x0a, 0x0c, 0x62, 0x61, 0x6e, 0x6b, 0x5f,
	0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x62,
	0x61, 0x6e, 0x6b, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x34, 0x0a, 0x16, 0x69, 0x6e,
	0x74, 0x65, 0x67, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x5f,
	0x63, 0x6f, 0x64, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x14, 0x69, 0x6e, 0x74, 0x65,
	0x67, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x43, 0x6f, 0x64, 0x65,
	0x12, 0x3a, 0x0a, 0x19, 0x69, 0x6e, 0x74, 0x65, 0x67, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f,
	0x65, 0x72, 0x72, 0x6f, 0x72, 0x5f, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x18, 0x05, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x17, 0x69, 0x6e, 0x74, 0x65, 0x67, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x45, 0x72, 0x72, 0x6f, 0x72, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x22, 0x93, 0x01, 0x0a,
	0x31, 0x47, 0x65, 0x74, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x73,
	0x57, 0x69, 0x74, 0x68, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x42, 0x61, 0x6e, 0x6b, 0x52, 0x65, 0x66,
	0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x49, 0x44, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x56, 0x31, 0x12, 0x5e, 0x0a, 0x0f, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f,
	0x6e, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x35, 0x2e, 0x70, 0x72,
	0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x2e, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63,
	0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e,
	0x2e, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x44, 0x61, 0x74, 0x61,
	0x56, 0x31, 0x52, 0x0e, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x49,
	0x64, 0x73, 0x22, 0x73, 0x0a, 0x1e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x42, 0x61, 0x6e, 0x6b,
	0x52, 0x65, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x49, 0x44, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x56, 0x31, 0x12, 0x25, 0x0a, 0x0e, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74,
	0x69, 0x6f, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x0d, 0x74, 0x72,
	0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x12, 0x2a, 0x0a, 0x11, 0x62,
	0x61, 0x6e, 0x6b, 0x5f, 0x72, 0x65, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x5f, 0x69, 0x64,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f, 0x62, 0x61, 0x6e, 0x6b, 0x52, 0x65, 0x66, 0x65,
	0x72, 0x65, 0x6e, 0x63, 0x65, 0x49, 0x64, 0x32, 0xa7, 0x04, 0x0a, 0x0e, 0x54, 0x72, 0x61, 0x6e,
	0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x6e, 0x66, 0x12, 0x72, 0x0a, 0x11, 0x55, 0x70,
	0x64, 0x61, 0x74, 0x65, 0x4a, 0x6f, 0x62, 0x73, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12,
	0x43, 0x2e, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x2e, 0x74, 0x72, 0x61,
	0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63,
	0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65,
	0x4a, 0x6f, 0x62, 0x73, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x56, 0x31, 0x1a, 0x16, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x22, 0x00, 0x12, 0x82,
	0x01, 0x0a, 0x19, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x42, 0x61, 0x6e, 0x6b, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x4b, 0x2e, 0x70,
	0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x2e, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61,
	0x63, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f,
	0x6e, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x42, 0x61, 0x6e,
	0x6b, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x56, 0x31, 0x1a, 0x16, 0x2e, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x45, 0x6d, 0x70, 0x74,
	0x79, 0x22, 0x00, 0x12, 0x9f, 0x01, 0x0a, 0x27, 0x47, 0x65, 0x74, 0x54, 0x72, 0x61, 0x6e, 0x73,
	0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x57, 0x69, 0x74, 0x68, 0x45, 0x6d, 0x70, 0x74, 0x79,
	0x42, 0x61, 0x6e, 0x6b, 0x52, 0x65, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x49, 0x44, 0x12,
	0x16, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75,
	0x66, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x1a, 0x5a, 0x2e, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73,
	0x73, 0x69, 0x6e, 0x67, 0x2e, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e,
	0x2e, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x69, 0x6e, 0x66,
	0x6f, 0x2e, 0x47, 0x65, 0x74, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e,
	0x73, 0x57, 0x69, 0x74, 0x68, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x42, 0x61, 0x6e, 0x6b, 0x52, 0x65,
	0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x49, 0x44, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x56, 0x31, 0x22, 0x00, 0x12, 0x7a, 0x0a, 0x15, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x42,
	0x61, 0x6e, 0x6b, 0x52, 0x65, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x49, 0x44, 0x12, 0x47,
	0x2e, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x2e, 0x74, 0x72, 0x61, 0x6e,
	0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74,
	0x69, 0x6f, 0x6e, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x42,
	0x61, 0x6e, 0x6b, 0x52, 0x65, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x49, 0x44, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x56, 0x31, 0x1a, 0x16, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x22,
	0x00, 0x42, 0x2d, 0x5a, 0x2b, 0x67, 0x69, 0x74, 0x2e, 0x6c, 0x6f, 0x63, 0x61, 0x6c, 0x2f, 0x73,
	0x65, 0x6e, 0x73, 0x69, 0x74, 0x69, 0x76, 0x65, 0x2f, 0x69, 0x6e, 0x6e, 0x65, 0x72, 0x70, 0x62,
	0x2f, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x2f, 0x67, 0x72, 0x70, 0x63,
	0x62, 0x08, 0x65, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x70, 0xe8, 0x07,
})

var (
	file_inner_processing_grpc_transaction_info_proto_rawDescOnce sync.Once
	file_inner_processing_grpc_transaction_info_proto_rawDescData []byte
)

func file_inner_processing_grpc_transaction_info_proto_rawDescGZIP() []byte {
	file_inner_processing_grpc_transaction_info_proto_rawDescOnce.Do(func() {
		file_inner_processing_grpc_transaction_info_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_inner_processing_grpc_transaction_info_proto_rawDesc), len(file_inner_processing_grpc_transaction_info_proto_rawDesc)))
	})
	return file_inner_processing_grpc_transaction_info_proto_rawDescData
}

var file_inner_processing_grpc_transaction_info_proto_msgTypes = make([]protoimpl.MessageInfo, 4)
var file_inner_processing_grpc_transaction_info_proto_goTypes = []any{
	(*UpdateJobsMessageRequestV1)(nil),                        // 0: processing.transaction.transaction_info.UpdateJobsMessageRequestV1
	(*UpdateBankResponseMessageRequestV1)(nil),                // 1: processing.transaction.transaction_info.UpdateBankResponseMessageRequestV1
	(*GetTransactionsWithEmptyBankReferenceIDResponseV1)(nil), // 2: processing.transaction.transaction_info.GetTransactionsWithEmptyBankReferenceIDResponseV1
	(*UpdateBankReferenceIDRequestV1)(nil),                    // 3: processing.transaction.transaction_info.UpdateBankReferenceIDRequestV1
	(*structpb.Struct)(nil),                                   // 4: google.protobuf.Struct
	(*TransactionDataV1)(nil),                                 // 5: processing.transaction.transaction.TransactionDataV1
	(*emptypb.Empty)(nil),                                     // 6: google.protobuf.Empty
}
var file_inner_processing_grpc_transaction_info_proto_depIdxs = []int32{
	4, // 0: processing.transaction.transaction_info.UpdateJobsMessageRequestV1.jobs_message:type_name -> google.protobuf.Struct
	5, // 1: processing.transaction.transaction_info.GetTransactionsWithEmptyBankReferenceIDResponseV1.transaction_ids:type_name -> processing.transaction.transaction.TransactionDataV1
	0, // 2: processing.transaction.transaction_info.TransactionInf.UpdateJobsMessage:input_type -> processing.transaction.transaction_info.UpdateJobsMessageRequestV1
	1, // 3: processing.transaction.transaction_info.TransactionInf.UpdateBankResponseMessage:input_type -> processing.transaction.transaction_info.UpdateBankResponseMessageRequestV1
	6, // 4: processing.transaction.transaction_info.TransactionInf.GetTransactionsWithEmptyBankReferenceID:input_type -> google.protobuf.Empty
	3, // 5: processing.transaction.transaction_info.TransactionInf.UpdateBankReferenceID:input_type -> processing.transaction.transaction_info.UpdateBankReferenceIDRequestV1
	6, // 6: processing.transaction.transaction_info.TransactionInf.UpdateJobsMessage:output_type -> google.protobuf.Empty
	6, // 7: processing.transaction.transaction_info.TransactionInf.UpdateBankResponseMessage:output_type -> google.protobuf.Empty
	2, // 8: processing.transaction.transaction_info.TransactionInf.GetTransactionsWithEmptyBankReferenceID:output_type -> processing.transaction.transaction_info.GetTransactionsWithEmptyBankReferenceIDResponseV1
	6, // 9: processing.transaction.transaction_info.TransactionInf.UpdateBankReferenceID:output_type -> google.protobuf.Empty
	6, // [6:10] is the sub-list for method output_type
	2, // [2:6] is the sub-list for method input_type
	2, // [2:2] is the sub-list for extension type_name
	2, // [2:2] is the sub-list for extension extendee
	0, // [0:2] is the sub-list for field type_name
}

func init() { file_inner_processing_grpc_transaction_info_proto_init() }
func file_inner_processing_grpc_transaction_info_proto_init() {
	if File_inner_processing_grpc_transaction_info_proto != nil {
		return
	}
	file_inner_processing_grpc_transaction_proto_init()
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_inner_processing_grpc_transaction_info_proto_rawDesc), len(file_inner_processing_grpc_transaction_info_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   4,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_inner_processing_grpc_transaction_info_proto_goTypes,
		DependencyIndexes: file_inner_processing_grpc_transaction_info_proto_depIdxs,
		MessageInfos:      file_inner_processing_grpc_transaction_info_proto_msgTypes,
	}.Build()
	File_inner_processing_grpc_transaction_info_proto = out.File
	file_inner_processing_grpc_transaction_info_proto_goTypes = nil
	file_inner_processing_grpc_transaction_info_proto_depIdxs = nil
}
