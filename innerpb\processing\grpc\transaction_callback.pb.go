// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.5
// 	protoc        v5.29.3
// source: inner/processing/grpc/transaction_callback.proto

package grpc

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type TransactionSendCallbackRequestV1 struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	TransactionId *uint64                `protobuf:"varint,1,opt,name=transaction_id,json=transactionId" json:"transaction_id,omitempty"`
	AcquireCode   *string                `protobuf:"bytes,2,opt,name=acquireCode" json:"acquireCode,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *TransactionSendCallbackRequestV1) Reset() {
	*x = TransactionSendCallbackRequestV1{}
	mi := &file_inner_processing_grpc_transaction_callback_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *TransactionSendCallbackRequestV1) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TransactionSendCallbackRequestV1) ProtoMessage() {}

func (x *TransactionSendCallbackRequestV1) ProtoReflect() protoreflect.Message {
	mi := &file_inner_processing_grpc_transaction_callback_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TransactionSendCallbackRequestV1.ProtoReflect.Descriptor instead.
func (*TransactionSendCallbackRequestV1) Descriptor() ([]byte, []int) {
	return file_inner_processing_grpc_transaction_callback_proto_rawDescGZIP(), []int{0}
}

func (x *TransactionSendCallbackRequestV1) GetTransactionId() uint64 {
	if x != nil && x.TransactionId != nil {
		return *x.TransactionId
	}
	return 0
}

func (x *TransactionSendCallbackRequestV1) GetAcquireCode() string {
	if x != nil && x.AcquireCode != nil {
		return *x.AcquireCode
	}
	return ""
}

var File_inner_processing_grpc_transaction_callback_proto protoreflect.FileDescriptor

var file_inner_processing_grpc_transaction_callback_proto_rawDesc = string([]byte{
	0x0a, 0x30, 0x69, 0x6e, 0x6e, 0x65, 0x72, 0x2f, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69,
	0x6e, 0x67, 0x2f, 0x67, 0x72, 0x70, 0x63, 0x2f, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74,
	0x69, 0x6f, 0x6e, 0x5f, 0x63, 0x61, 0x6c, 0x6c, 0x62, 0x61, 0x63, 0x6b, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x12, 0x2b, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x2e, 0x74,
	0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x74, 0x72, 0x61, 0x6e, 0x73,
	0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x63, 0x61, 0x6c, 0x6c, 0x62, 0x61, 0x63, 0x6b, 0x1a,
	0x1b, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66,
	0x2f, 0x65, 0x6d, 0x70, 0x74, 0x79, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x6b, 0x0a, 0x20,
	0x54, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x65, 0x6e, 0x64, 0x43,
	0x61, 0x6c, 0x6c, 0x62, 0x61, 0x63, 0x6b, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x56, 0x31,
	0x12, 0x25, 0x0a, 0x0e, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x5f,
	0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x0d, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61,
	0x63, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x12, 0x20, 0x0a, 0x0b, 0x61, 0x63, 0x71, 0x75, 0x69,
	0x72, 0x65, 0x43, 0x6f, 0x64, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x61, 0x63,
	0x71, 0x75, 0x69, 0x72, 0x65, 0x43, 0x6f, 0x64, 0x65, 0x32, 0x8e, 0x01, 0x0a, 0x13, 0x54, 0x72,
	0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x43, 0x61, 0x6c, 0x6c, 0x62, 0x61, 0x63,
	0x6b, 0x12, 0x77, 0x0a, 0x0c, 0x53, 0x65, 0x6e, 0x64, 0x43, 0x61, 0x6c, 0x6c, 0x62, 0x61, 0x63,
	0x6b, 0x12, 0x4d, 0x2e, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x2e, 0x74,
	0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x74, 0x72, 0x61, 0x6e, 0x73,
	0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x63, 0x61, 0x6c, 0x6c, 0x62, 0x61, 0x63, 0x6b, 0x2e,
	0x54, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x65, 0x6e, 0x64, 0x43,
	0x61, 0x6c, 0x6c, 0x62, 0x61, 0x63, 0x6b, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x56, 0x31,
	0x1a, 0x16, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62,
	0x75, 0x66, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x22, 0x00, 0x42, 0x2d, 0x5a, 0x2b, 0x67, 0x69,
	0x74, 0x2e, 0x6c, 0x6f, 0x63, 0x61, 0x6c, 0x2f, 0x73, 0x65, 0x6e, 0x73, 0x69, 0x74, 0x69, 0x76,
	0x65, 0x2f, 0x69, 0x6e, 0x6e, 0x65, 0x72, 0x70, 0x62, 0x2f, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73,
	0x73, 0x69, 0x6e, 0x67, 0x2f, 0x67, 0x72, 0x70, 0x63, 0x62, 0x08, 0x65, 0x64, 0x69, 0x74, 0x69,
	0x6f, 0x6e, 0x73, 0x70, 0xe8, 0x07,
})

var (
	file_inner_processing_grpc_transaction_callback_proto_rawDescOnce sync.Once
	file_inner_processing_grpc_transaction_callback_proto_rawDescData []byte
)

func file_inner_processing_grpc_transaction_callback_proto_rawDescGZIP() []byte {
	file_inner_processing_grpc_transaction_callback_proto_rawDescOnce.Do(func() {
		file_inner_processing_grpc_transaction_callback_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_inner_processing_grpc_transaction_callback_proto_rawDesc), len(file_inner_processing_grpc_transaction_callback_proto_rawDesc)))
	})
	return file_inner_processing_grpc_transaction_callback_proto_rawDescData
}

var file_inner_processing_grpc_transaction_callback_proto_msgTypes = make([]protoimpl.MessageInfo, 1)
var file_inner_processing_grpc_transaction_callback_proto_goTypes = []any{
	(*TransactionSendCallbackRequestV1)(nil), // 0: processing.transaction.transaction_callback.TransactionSendCallbackRequestV1
	(*emptypb.Empty)(nil),                    // 1: google.protobuf.Empty
}
var file_inner_processing_grpc_transaction_callback_proto_depIdxs = []int32{
	0, // 0: processing.transaction.transaction_callback.TransactionCallback.SendCallback:input_type -> processing.transaction.transaction_callback.TransactionSendCallbackRequestV1
	1, // 1: processing.transaction.transaction_callback.TransactionCallback.SendCallback:output_type -> google.protobuf.Empty
	1, // [1:2] is the sub-list for method output_type
	0, // [0:1] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_inner_processing_grpc_transaction_callback_proto_init() }
func file_inner_processing_grpc_transaction_callback_proto_init() {
	if File_inner_processing_grpc_transaction_callback_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_inner_processing_grpc_transaction_callback_proto_rawDesc), len(file_inner_processing_grpc_transaction_callback_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   1,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_inner_processing_grpc_transaction_callback_proto_goTypes,
		DependencyIndexes: file_inner_processing_grpc_transaction_callback_proto_depIdxs,
		MessageInfos:      file_inner_processing_grpc_transaction_callback_proto_msgTypes,
	}.Build()
	File_inner_processing_grpc_transaction_callback_proto = out.File
	file_inner_processing_grpc_transaction_callback_proto_goTypes = nil
	file_inner_processing_grpc_transaction_callback_proto_depIdxs = nil
}
