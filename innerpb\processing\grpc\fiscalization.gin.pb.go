// Code generated by protoc-gen-go-mvp. DO NOT EDIT.
// versions:
// - protoc-gen-go-mvp v2.0.1
// created by mvp authors

package grpc

import (
	dog "git.local/sensitive/sdk/dog"
	gin "github.com/gin-gonic/gin"
	metadata "google.golang.org/grpc/metadata"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
	http "net/http"
)

func RegisterGinFiscalizationRouterGroup(
	in *gin.RouterGroup,
	handler func(func(c *gin.Context) error) gin.HandlerFunc,
) error {
	service, err := NewGinFiscalizationService()
	if err != nil {
		return err
	}

	routerGroup := in.Group("processing.fiscalization.fiscalization.Fiscalization")
	routerGroup.PUT("/MakeFiscalizationV1", handler(service.MakeFiscalizationV1))
	routerGroup.PUT("/GetFiscalInfoByTransactionIDV1", handler(service.GetFiscalInfoByTransactionIDV1))
	routerGroup.PUT("/ManageShifts", handler(service.ManageShifts))
	routerGroup.PUT("/FinalizeFiscalizations", handler(service.FinalizeFiscalizations))
	return nil
}

func NewGinFiscalizationService() (GinFiscalizationServer, error) {
	client, err := NewPreparedFiscalizationClient()
	if err != nil {
		return nil, err
	}

	return &ginFiscalizationServer{
		client: NewLoggedFiscalizationClient(
			NewIamFiscalizationClient(client),
		),
	}, nil
}

//go:generate mockgen -destination=./grpcmock/fiscalization.gin.pb.go -package=grpcmock -source=fiscalization.gin.pb.go GinFiscalizationServer
type GinFiscalizationServer interface {
	MakeFiscalizationV1(c *gin.Context) error
	GetFiscalInfoByTransactionIDV1(c *gin.Context) error
	ManageShifts(c *gin.Context) error
	FinalizeFiscalizations(c *gin.Context) error
}

var _ GinFiscalizationServer = (*ginFiscalizationServer)(nil)

type ginFiscalizationServer struct {
	client FiscalizationClient
}

type Fiscalization_MakeFiscalizationV1_Success struct {
	// Status is the status of the response. Set true
	Status bool `json:"status"`
	// Message is a human-readable explanation of the status
	Message string `json:"message"`
	// StatusCode is the GoErr status. Set 0
	StatusCode int `json:"status_code"`
	// Result is the result of the request
	Result *emptypb.Empty `json:"result"`
}

type Fiscalization_MakeFiscalizationV1_Failure struct {
	// Status is the status of the response. Set false
	Status bool `json:"status"`
	// Message is a human-readable explanation of the status
	Message string `json:"message"`
	// StatusCode is the GoErr status
	StatusCode int `json:"status_code"`
}

// MakeFiscalizationV1
// @Summary MakeFiscalizationV1
// @Security bearerAuth
// @ID Fiscalization_MakeFiscalizationV1
// @Accept json
// @Param request body MakeFiscalizationRequestV1 true "MakeFiscalizationRequestV1"
// @Success 200 {object} Fiscalization_MakeFiscalizationV1_Success
// @Failure 401 {object} Fiscalization_MakeFiscalizationV1_Failure "goerrpkg.ErrorTypeAuthorization"
// @Failure 404 {object} Fiscalization_MakeFiscalizationV1_Failure "goerrpkg.ErrorTypeNotFound"
// @Failure 400 {object} Fiscalization_MakeFiscalizationV1_Failure "goerrpkg.ErrorTypeBadRequest"
// @Failure 500 {object} Fiscalization_MakeFiscalizationV1_Failure "goerrpkg.ErrorTypeUnexpectedError"
// @Failure 400 {object} Fiscalization_MakeFiscalizationV1_Failure "gin.ErrorTypeBind"
// @Failure 500 {object} Fiscalization_MakeFiscalizationV1_Failure "Undefined error"
// @Produce json
// @Router /processing.fiscalization.fiscalization.Fiscalization/MakeFiscalizationV1 [put]
// @tags Fiscalization
func (s *ginFiscalizationServer) MakeFiscalizationV1(c *gin.Context) error {
	ctx, span := dog.CreateSpan(c.Request.Context(), "GinFiscalizationServer_MakeFiscalizationV1")
	defer span.End()

	var request MakeFiscalizationRequestV1
	if err := c.ShouldBindJSON(&request); err != nil {
		return err
	}

	ctx = metadata.NewOutgoingContext(ctx, metadata.MD(c.Request.Header))

	response, err := s.client.MakeFiscalizationV1(ctx, &request)
	if err != nil {
		return err
	}

	c.JSON(http.StatusOK, &Fiscalization_MakeFiscalizationV1_Success{
		Status:     true,
		Message:    "Success",
		StatusCode: 0,
		Result:     response,
	})
	return nil
}

type Fiscalization_GetFiscalInfoByTransactionIDV1_Success struct {
	// Status is the status of the response. Set true
	Status bool `json:"status"`
	// Message is a human-readable explanation of the status
	Message string `json:"message"`
	// StatusCode is the GoErr status. Set 0
	StatusCode int `json:"status_code"`
	// Result is the result of the request
	Result *GetFiscalInfoByTransactionIDResponseV1 `json:"result"`
}

type Fiscalization_GetFiscalInfoByTransactionIDV1_Failure struct {
	// Status is the status of the response. Set false
	Status bool `json:"status"`
	// Message is a human-readable explanation of the status
	Message string `json:"message"`
	// StatusCode is the GoErr status
	StatusCode int `json:"status_code"`
}

// GetFiscalInfoByTransactionIDV1
// @Summary GetFiscalInfoByTransactionIDV1
// @Security bearerAuth
// @ID Fiscalization_GetFiscalInfoByTransactionIDV1
// @Accept json
// @Param request body GetFiscalInfoByTransactionIDRequestV1 true "GetFiscalInfoByTransactionIDRequestV1"
// @Success 200 {object} Fiscalization_GetFiscalInfoByTransactionIDV1_Success
// @Failure 401 {object} Fiscalization_GetFiscalInfoByTransactionIDV1_Failure "goerrpkg.ErrorTypeAuthorization"
// @Failure 404 {object} Fiscalization_GetFiscalInfoByTransactionIDV1_Failure "goerrpkg.ErrorTypeNotFound"
// @Failure 400 {object} Fiscalization_GetFiscalInfoByTransactionIDV1_Failure "goerrpkg.ErrorTypeBadRequest"
// @Failure 500 {object} Fiscalization_GetFiscalInfoByTransactionIDV1_Failure "goerrpkg.ErrorTypeUnexpectedError"
// @Failure 400 {object} Fiscalization_GetFiscalInfoByTransactionIDV1_Failure "gin.ErrorTypeBind"
// @Failure 500 {object} Fiscalization_GetFiscalInfoByTransactionIDV1_Failure "Undefined error"
// @Produce json
// @Router /processing.fiscalization.fiscalization.Fiscalization/GetFiscalInfoByTransactionIDV1 [put]
// @tags Fiscalization
func (s *ginFiscalizationServer) GetFiscalInfoByTransactionIDV1(c *gin.Context) error {
	ctx, span := dog.CreateSpan(c.Request.Context(), "GinFiscalizationServer_GetFiscalInfoByTransactionIDV1")
	defer span.End()

	var request GetFiscalInfoByTransactionIDRequestV1
	if err := c.ShouldBindJSON(&request); err != nil {
		return err
	}

	ctx = metadata.NewOutgoingContext(ctx, metadata.MD(c.Request.Header))

	response, err := s.client.GetFiscalInfoByTransactionIDV1(ctx, &request)
	if err != nil {
		return err
	}

	c.JSON(http.StatusOK, &Fiscalization_GetFiscalInfoByTransactionIDV1_Success{
		Status:     true,
		Message:    "Success",
		StatusCode: 0,
		Result:     response,
	})
	return nil
}

type Fiscalization_ManageShifts_Success struct {
	// Status is the status of the response. Set true
	Status bool `json:"status"`
	// Message is a human-readable explanation of the status
	Message string `json:"message"`
	// StatusCode is the GoErr status. Set 0
	StatusCode int `json:"status_code"`
	// Result is the result of the request
	Result *emptypb.Empty `json:"result"`
}

type Fiscalization_ManageShifts_Failure struct {
	// Status is the status of the response. Set false
	Status bool `json:"status"`
	// Message is a human-readable explanation of the status
	Message string `json:"message"`
	// StatusCode is the GoErr status
	StatusCode int `json:"status_code"`
}

// ManageShifts
// @Summary  jobs
// @Security bearerAuth
// @ID Fiscalization_ManageShifts
// @Accept json
// @Param request body emptypb.Empty true "Empty"
// @Success 200 {object} Fiscalization_ManageShifts_Success
// @Failure 401 {object} Fiscalization_ManageShifts_Failure "goerrpkg.ErrorTypeAuthorization"
// @Failure 404 {object} Fiscalization_ManageShifts_Failure "goerrpkg.ErrorTypeNotFound"
// @Failure 400 {object} Fiscalization_ManageShifts_Failure "goerrpkg.ErrorTypeBadRequest"
// @Failure 500 {object} Fiscalization_ManageShifts_Failure "goerrpkg.ErrorTypeUnexpectedError"
// @Failure 400 {object} Fiscalization_ManageShifts_Failure "gin.ErrorTypeBind"
// @Failure 500 {object} Fiscalization_ManageShifts_Failure "Undefined error"
// @Produce json
// @Router /processing.fiscalization.fiscalization.Fiscalization/ManageShifts [put]
// @tags Fiscalization
func (s *ginFiscalizationServer) ManageShifts(c *gin.Context) error {
	ctx, span := dog.CreateSpan(c.Request.Context(), "GinFiscalizationServer_ManageShifts")
	defer span.End()

	var request emptypb.Empty
	if err := c.ShouldBindJSON(&request); err != nil {
		return err
	}

	ctx = metadata.NewOutgoingContext(ctx, metadata.MD(c.Request.Header))

	response, err := s.client.ManageShifts(ctx, &request)
	if err != nil {
		return err
	}

	c.JSON(http.StatusOK, &Fiscalization_ManageShifts_Success{
		Status:     true,
		Message:    "Success",
		StatusCode: 0,
		Result:     response,
	})
	return nil
}

type Fiscalization_FinalizeFiscalizations_Success struct {
	// Status is the status of the response. Set true
	Status bool `json:"status"`
	// Message is a human-readable explanation of the status
	Message string `json:"message"`
	// StatusCode is the GoErr status. Set 0
	StatusCode int `json:"status_code"`
	// Result is the result of the request
	Result *emptypb.Empty `json:"result"`
}

type Fiscalization_FinalizeFiscalizations_Failure struct {
	// Status is the status of the response. Set false
	Status bool `json:"status"`
	// Message is a human-readable explanation of the status
	Message string `json:"message"`
	// StatusCode is the GoErr status
	StatusCode int `json:"status_code"`
}

// FinalizeFiscalizations
// @Summary FinalizeFiscalizations
// @Security bearerAuth
// @ID Fiscalization_FinalizeFiscalizations
// @Accept json
// @Param request body emptypb.Empty true "Empty"
// @Success 200 {object} Fiscalization_FinalizeFiscalizations_Success
// @Failure 401 {object} Fiscalization_FinalizeFiscalizations_Failure "goerrpkg.ErrorTypeAuthorization"
// @Failure 404 {object} Fiscalization_FinalizeFiscalizations_Failure "goerrpkg.ErrorTypeNotFound"
// @Failure 400 {object} Fiscalization_FinalizeFiscalizations_Failure "goerrpkg.ErrorTypeBadRequest"
// @Failure 500 {object} Fiscalization_FinalizeFiscalizations_Failure "goerrpkg.ErrorTypeUnexpectedError"
// @Failure 400 {object} Fiscalization_FinalizeFiscalizations_Failure "gin.ErrorTypeBind"
// @Failure 500 {object} Fiscalization_FinalizeFiscalizations_Failure "Undefined error"
// @Produce json
// @Router /processing.fiscalization.fiscalization.Fiscalization/FinalizeFiscalizations [put]
// @tags Fiscalization
func (s *ginFiscalizationServer) FinalizeFiscalizations(c *gin.Context) error {
	ctx, span := dog.CreateSpan(c.Request.Context(), "GinFiscalizationServer_FinalizeFiscalizations")
	defer span.End()

	var request emptypb.Empty
	if err := c.ShouldBindJSON(&request); err != nil {
		return err
	}

	ctx = metadata.NewOutgoingContext(ctx, metadata.MD(c.Request.Header))

	response, err := s.client.FinalizeFiscalizations(ctx, &request)
	if err != nil {
		return err
	}

	c.JSON(http.StatusOK, &Fiscalization_FinalizeFiscalizations_Success{
		Status:     true,
		Message:    "Success",
		StatusCode: 0,
		Result:     response,
	})
	return nil
}
