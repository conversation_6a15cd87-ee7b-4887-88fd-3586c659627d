// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.5.1
// - protoc             v5.29.3
// source: inner/processing/grpc/multiacquiring_balance.proto

package grpc

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.64.0 or later.
const _ = grpc.SupportPackageIsVersion9

const (
	MultiacquiringBalance_CheckBalance_FullMethodName = "/processing.multiacquiring.multiacquiring_balance.MultiacquiringBalance/CheckBalance"
)

// MultiacquiringBalanceClient is the client API for MultiacquiringBalance service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type MultiacquiringBalanceClient interface {
	CheckBalance(ctx context.Context, in *CheckBalanceRequest, opts ...grpc.CallOption) (*CheckBalanceResponse, error)
}

type multiacquiringBalanceClient struct {
	cc grpc.ClientConnInterface
}

func NewMultiacquiringBalanceClient(cc grpc.ClientConnInterface) MultiacquiringBalanceClient {
	return &multiacquiringBalanceClient{cc}
}

func (c *multiacquiringBalanceClient) CheckBalance(ctx context.Context, in *CheckBalanceRequest, opts ...grpc.CallOption) (*CheckBalanceResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(CheckBalanceResponse)
	err := c.cc.Invoke(ctx, MultiacquiringBalance_CheckBalance_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// MultiacquiringBalanceServer is the server API for MultiacquiringBalance service.
// All implementations must embed UnimplementedMultiacquiringBalanceServer
// for forward compatibility.
type MultiacquiringBalanceServer interface {
	CheckBalance(context.Context, *CheckBalanceRequest) (*CheckBalanceResponse, error)
	mustEmbedUnimplementedMultiacquiringBalanceServer()
}

// UnimplementedMultiacquiringBalanceServer must be embedded to have
// forward compatible implementations.
//
// NOTE: this should be embedded by value instead of pointer to avoid a nil
// pointer dereference when methods are called.
type UnimplementedMultiacquiringBalanceServer struct{}

func (UnimplementedMultiacquiringBalanceServer) CheckBalance(context.Context, *CheckBalanceRequest) (*CheckBalanceResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CheckBalance not implemented")
}
func (UnimplementedMultiacquiringBalanceServer) mustEmbedUnimplementedMultiacquiringBalanceServer() {}
func (UnimplementedMultiacquiringBalanceServer) testEmbeddedByValue()                               {}

// UnsafeMultiacquiringBalanceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to MultiacquiringBalanceServer will
// result in compilation errors.
type UnsafeMultiacquiringBalanceServer interface {
	mustEmbedUnimplementedMultiacquiringBalanceServer()
}

func RegisterMultiacquiringBalanceServer(s grpc.ServiceRegistrar, srv MultiacquiringBalanceServer) {
	// If the following call pancis, it indicates UnimplementedMultiacquiringBalanceServer was
	// embedded by pointer and is nil.  This will cause panics if an
	// unimplemented method is ever invoked, so we test this at initialization
	// time to prevent it from happening at runtime later due to I/O.
	if t, ok := srv.(interface{ testEmbeddedByValue() }); ok {
		t.testEmbeddedByValue()
	}
	s.RegisterService(&MultiacquiringBalance_ServiceDesc, srv)
}

func _MultiacquiringBalance_CheckBalance_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CheckBalanceRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MultiacquiringBalanceServer).CheckBalance(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: MultiacquiringBalance_CheckBalance_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MultiacquiringBalanceServer).CheckBalance(ctx, req.(*CheckBalanceRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// MultiacquiringBalance_ServiceDesc is the grpc.ServiceDesc for MultiacquiringBalance service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var MultiacquiringBalance_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "processing.multiacquiring.multiacquiring_balance.MultiacquiringBalance",
	HandlerType: (*MultiacquiringBalanceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "CheckBalance",
			Handler:    _MultiacquiringBalance_CheckBalance_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "inner/processing/grpc/multiacquiring_balance.proto",
}
