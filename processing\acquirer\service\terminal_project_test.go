package service

import (
	"context"
	"errors"
	"git.local/sensitive/innerpb/processing/grpc/grpcmock"
	"testing"
	"time"

	"github.com/golang/mock/gomock"
	"github.com/stretchr/testify/require"

	"git.local/sensitive/innerpb/processing/goerr"
	"git.local/sensitive/innerpb/processing/grpc"
	"git.local/sensitive/processing/acquirer/model"
	"git.local/sensitive/processing/acquirer/repository/database/databasemocks"
	"git.local/sensitive/processing/acquirer/schema"
	"git.local/sensitive/sdk/dog/e2e"
	"git.local/sensitive/testsdk"
)

func TestGetPayinProjectTerminals(t *testing.T) {
	now := time.Now()

	type getByProjectIDOp struct {
		input     uint64
		output    []*model.TerminalProject
		outputErr error
	}

	type getTransactionPayInTypesOp struct {
		isCalled  bool
		output    *grpc.TransactionTypeResponseV1
		outputErr error
	}

	tests := []struct {
		name                     string
		req                      uint64
		want                     []*schema.GetTerminalProjectPayIn
		wantErr                  error
		getByProjectID           getByProjectIDOp
		getTransactionPayInTypes getTransactionPayInTypesOp
	}{
		{
			name:    "error when getting project by id",
			req:     22,
			want:    nil,
			wantErr: goerr.ErrDbUnexpected,
			getByProjectID: getByProjectIDOp{
				input:     22,
				output:    nil,
				outputErr: goerr.ErrDbUnexpected,
			},
		},
		{
			name:    "error when getting payin types",
			req:     22,
			want:    nil,
			wantErr: goerr.ErrDbUnexpected,
			getByProjectID: getByProjectIDOp{
				input: 22,
				output: []*model.TerminalProject{
					{
						ID:                1,
						ProjectID:         22,
						TransactionTypeID: uint64(grpc.EnumTransactionType_TransactionTypeOneClickPayIn.Int()),
						IsActive:          true,
						TerminalID:        22,
						TimestampMixin: model.TimestampMixin{
							CreatedAt: now,
							UpdatedAt: now,
						},
					},
					{
						ID:                2,
						ProjectID:         22,
						TransactionTypeID: uint64(grpc.EnumTransactionType_TransactionTypePayOut.Int()),
						IsActive:          true,
						TerminalID:        22,
						TimestampMixin: model.TimestampMixin{
							CreatedAt: now,
							UpdatedAt: now,
						},
					},
				},
				outputErr: nil,
			},
			getTransactionPayInTypes: getTransactionPayInTypesOp{
				isCalled:  true,
				output:    nil,
				outputErr: goerr.ErrDbUnexpected,
			},
		},
		{
			name:    "nil payin types",
			req:     22,
			want:    nil,
			wantErr: goerr.ErrTransactionTypeNotFound,
			getByProjectID: getByProjectIDOp{
				input: 22,
				output: []*model.TerminalProject{
					{
						ID:                1,
						ProjectID:         22,
						TransactionTypeID: uint64(grpc.EnumTransactionType_TransactionTypeOneClickPayIn.Int()),
						IsActive:          true,
						TerminalID:        22,
						TimestampMixin: model.TimestampMixin{
							CreatedAt: now,
							UpdatedAt: now,
						},
					},
					{
						ID:                2,
						ProjectID:         22,
						TransactionTypeID: uint64(grpc.EnumTransactionType_TransactionTypePayOut.Int()),
						IsActive:          true,
						TerminalID:        22,
						TimestampMixin: model.TimestampMixin{
							CreatedAt: now,
							UpdatedAt: now,
						},
					},
				},
				outputErr: nil,
			},
			getTransactionPayInTypes: getTransactionPayInTypesOp{
				isCalled: true,
				output: &grpc.TransactionTypeResponseV1{
					Data: nil,
				},
				outputErr: nil,
			},
		},
		{
			name:    "no terminals",
			req:     22,
			want:    nil,
			wantErr: goerr.ErrProjectTerminalNotFound,
			getByProjectID: getByProjectIDOp{
				input: 22,
				output: []*model.TerminalProject{
					{
						ID:                1,
						ProjectID:         22,
						TransactionTypeID: uint64(grpc.EnumTransactionType_TransactionTypePayOut.Int()),
						IsActive:          true,
						TerminalID:        22,
						TimestampMixin: model.TimestampMixin{
							CreatedAt: now,
							UpdatedAt: now,
						},
					},
					{
						ID:                2,
						ProjectID:         22,
						TransactionTypeID: uint64(grpc.EnumTransactionType_TransactionTypePayOut.Int()),
						IsActive:          true,
						TerminalID:        22,
						TimestampMixin: model.TimestampMixin{
							CreatedAt: now,
							UpdatedAt: now,
						},
					},
				},
				outputErr: nil,
			},
			getTransactionPayInTypes: getTransactionPayInTypesOp{
				isCalled: true,
				output: &grpc.TransactionTypeResponseV1{
					Data: []*grpc.TransactionTypesResponseV1{
						{
							Id:   testsdk.Ptr(uint64(1)),
							Name: testsdk.Ptr("Приём"),
							Code: testsdk.Ptr("in"),
						},
					},
				},
				outputErr: nil,
			},
		},
		{
			name: "success",
			req:  22,
			want: []*schema.GetTerminalProjectPayIn{
				{
					ID:                1,
					ProjectID:         22,
					TransactionTypeID: uint64(grpc.EnumTransactionType_TransactionTypePayIn.Int()),
					TerminalID:        22,
					IsActive:          true,
					Name:              "Приём",
					Code:              "in",
					CreatedAt:         now,
					UpdatedAt:         now,
				},
			},
			wantErr: nil,
			getByProjectID: getByProjectIDOp{
				input: 22,
				output: []*model.TerminalProject{
					{
						ID:                1,
						ProjectID:         22,
						TransactionTypeID: uint64(grpc.EnumTransactionType_TransactionTypePayIn.Int()),
						IsActive:          true,
						TerminalID:        22,
						TimestampMixin: model.TimestampMixin{
							CreatedAt: now,
							UpdatedAt: now,
						},
					},
					{
						ID:                2,
						ProjectID:         22,
						TransactionTypeID: uint64(grpc.EnumTransactionType_TransactionTypePayOut.Int()),
						IsActive:          true,
						TerminalID:        22,
						TimestampMixin: model.TimestampMixin{
							CreatedAt: now,
							UpdatedAt: now,
						},
					},
				},
				outputErr: nil,
			},
			getTransactionPayInTypes: getTransactionPayInTypesOp{
				isCalled: true,
				output: &grpc.TransactionTypeResponseV1{
					Data: []*grpc.TransactionTypesResponseV1{
						{
							Id:   testsdk.Ptr(uint64(1)),
							Name: testsdk.Ptr("Приём"),
							Code: testsdk.Ptr("in"),
						},
					},
				},
				outputErr: nil,
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctrl := gomock.NewController(t)
			defer ctrl.Finish()

			terminalProjectDB := databasemocks.NewMockTerminalProjecter(ctrl)
			transactionTypeCliMock := grpcmock.NewMockTransactionTypeClient(ctrl)

			terminalProjectDB.EXPECT().GetByProjectID(
				gomock.Any(),
				tt.getByProjectID.input,
			).Return(tt.getByProjectID.output, tt.getByProjectID.outputErr).Times(1)

			if tt.getTransactionPayInTypes.isCalled {
				transactionTypeCliMock.EXPECT().GetTransactionPayInTypes(
					gomock.Any(),
					gomock.Any(),
				).Return(
					tt.getTransactionPayInTypes.output,
					tt.getTransactionPayInTypes.outputErr,
				).Times(1)
			}

			s := NewTerminalProjectService(terminalProjectDB, transactionTypeCliMock)

			resp, err := s.GetPayInProjectTerminals(context.Background(), tt.req)
			if tt.wantErr != nil {
				require.Error(t, err)
				require.ErrorIs(t, tt.wantErr, err)
			} else {
				require.NoError(t, err)
			}

			require.Equal(t, tt.want, resp)
		})
	}
}

func TestTerminalProjectUpdateStatus(t *testing.T) {
	type updateStatusOp struct {
		inputID   uint64
		inputReq  schema.UpdateTerminalProjectStatusRequest
		outputErr error
	}

	tests := []struct {
		name         string
		reqID        uint64
		req          schema.UpdateTerminalProjectStatusRequest
		updateStatus updateStatusOp
		wantErr      error
	}{
		{
			name:  "error when updating status",
			reqID: 33,
			req: schema.UpdateTerminalProjectStatusRequest{
				IsActive: true,
			},
			updateStatus: updateStatusOp{
				inputID: 33,
				inputReq: schema.UpdateTerminalProjectStatusRequest{
					IsActive: true,
				},
				outputErr: errors.New("some error"),
			},
			wantErr: errors.New("some error"),
		},
		{
			name:  "success",
			reqID: 33,
			req: schema.UpdateTerminalProjectStatusRequest{
				IsActive: true,
			},
			updateStatus: updateStatusOp{
				inputID: 33,
				inputReq: schema.UpdateTerminalProjectStatusRequest{
					IsActive: true,
				},
				outputErr: nil,
			},
			wantErr: nil,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctrl := gomock.NewController(t)
			defer ctrl.Finish()

			terminalProjectDB := databasemocks.NewMockTerminalProjecter(ctrl)

			terminalProjectDB.EXPECT().UpdateStatus(
				gomock.Any(),
				tt.updateStatus.inputID,
				tt.updateStatus.inputReq,
			).Return(tt.updateStatus.outputErr).Times(1)

			s := NewTerminalProjectService(terminalProjectDB, nil)

			err := s.UpdateStatus(context.Background(), tt.reqID, tt.req)
			if tt.wantErr != nil {
				require.Error(t, err)
				require.Equal(t, tt.wantErr, err)
			} else {
				require.NoError(t, err)
			}
		})
	}
}

func TestTerminalProjectUpdate(t *testing.T) {
	type updateOp struct {
		inputID   uint64
		inputReq  schema.UpdateTerminalProjectRequest
		outputErr error
	}

	tests := []struct {
		name    string
		reqID   uint64
		req     schema.UpdateTerminalProjectRequest
		wantErr error
		update  updateOp
	}{
		{
			name:  "error when updating",
			reqID: 33,
			req: schema.UpdateTerminalProjectRequest{
				IsActive:          true,
				ProjectID:         21,
				TransactionTypeID: uint64(grpc.EnumTransactionType_TransactionTypePayOut.Int()),
			},
			wantErr: errors.New("some error"),
			update: updateOp{
				inputID: 33,
				inputReq: schema.UpdateTerminalProjectRequest{
					IsActive:          true,
					ProjectID:         21,
					TransactionTypeID: uint64(grpc.EnumTransactionType_TransactionTypePayOut.Int()),
				},
				outputErr: errors.New("some error"),
			},
		},
		{
			name:  "success",
			reqID: 33,
			req: schema.UpdateTerminalProjectRequest{
				IsActive:          true,
				ProjectID:         21,
				TransactionTypeID: uint64(grpc.EnumTransactionType_TransactionTypePayOut.Int()),
			},
			wantErr: nil,
			update: updateOp{
				inputID: 33,
				inputReq: schema.UpdateTerminalProjectRequest{
					IsActive:          true,
					ProjectID:         21,
					TransactionTypeID: uint64(grpc.EnumTransactionType_TransactionTypePayOut.Int()),
				},
				outputErr: nil,
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctrl := gomock.NewController(t)
			defer ctrl.Finish()

			terminalProjectDB := databasemocks.NewMockTerminalProjecter(ctrl)

			terminalProjectDB.EXPECT().Update(
				gomock.Any(),
				tt.update.inputID,
				tt.update.inputReq,
			).Return(tt.update.outputErr).Times(1)

			s := NewTerminalProjectService(terminalProjectDB, nil)

			err := s.Update(context.Background(), tt.reqID, tt.req)
			if tt.wantErr != nil {
				require.Error(t, err)
				require.Equal(t, tt.wantErr, err)
			} else {
				require.NoError(t, err)
			}
		})
	}
}

func TestTerminalProjectCreate(t *testing.T) {
	type createOp struct {
		input     *model.TerminalProject
		outputErr error
	}

	tests := []struct {
		name    string
		req     schema.TerminalProjectRequest
		wantErr error
		create  createOp
	}{
		{
			name: "error when creating",
			req: schema.TerminalProjectRequest{
				ProjectID:         22,
				TransactionTypeID: uint64(grpc.EnumTransactionType_TransactionTypePayOut.Int()),
				IsActive:          true,
				TerminalID:        98,
			},
			wantErr: errors.New("some error"),
			create: createOp{
				input: &model.TerminalProject{
					ProjectID:         22,
					TransactionTypeID: uint64(grpc.EnumTransactionType_TransactionTypePayOut.Int()),
					IsActive:          true,
					TerminalID:        98,
				},
				outputErr: errors.New("some error"),
			},
		},
		{
			name: "success",
			req: schema.TerminalProjectRequest{
				ProjectID:         22,
				TransactionTypeID: uint64(grpc.EnumTransactionType_TransactionTypePayOut.Int()),
				IsActive:          true,
				TerminalID:        98,
			},
			wantErr: nil,
			create: createOp{
				input: &model.TerminalProject{
					ProjectID:         22,
					TransactionTypeID: uint64(grpc.EnumTransactionType_TransactionTypePayOut.Int()),
					IsActive:          true,
					TerminalID:        98,
				},
				outputErr: nil,
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctrl := gomock.NewController(t)
			defer ctrl.Finish()

			c := context.Background()
			ctx, cancel := context.WithCancel(c)
			e2e.InitAppE2E(t, ctx, cancel, nil, nil, nil)

			terminalProjectDB := databasemocks.NewMockTerminalProjecter(ctrl)

			terminalProjectDB.EXPECT().Create(
				gomock.Any(),
				tt.create.input,
			).Return(tt.create.outputErr).Times(1)
			s := NewTerminalProjectService(terminalProjectDB, nil)

			err := s.Create(context.Background(), tt.req)
			if tt.wantErr != nil {
				require.Error(t, err)
				require.Equal(t, tt.wantErr, err)
			} else {
				require.NoError(t, err)
			}
		})
	}
}
