edition = "2023";

package processing.transaction.transaction_info;

option go_package = "git.local/sensitive/innerpb/processing/grpc";

import "google/protobuf/struct.proto";
import "google/protobuf/empty.proto";
import "inner/processing/grpc/transaction.proto";

service TransactionInf{
  rpc UpdateJobsMessage(UpdateJobsMessageRequestV1) returns (google.protobuf.Empty) {}
  rpc UpdateBankResponseMessage(UpdateBankResponseMessageRequestV1) returns (google.protobuf.Empty) {}
  rpc GetTransactionsWithEmptyBankReferenceID(google.protobuf.Empty) returns (GetTransactionsWithEmptyBankReferenceIDResponseV1) {}
  rpc UpdateBankReferenceID(UpdateBankReferenceIDRequestV1) returns (google.protobuf.Empty) {}
}

message UpdateJobsMessageRequestV1 {
  uint64 transaction_id = 1;
  google.protobuf.Struct jobs_message = 2;
}

message UpdateBankResponseMessageRequestV1 {
  uint64 transaction_id = 1;
  string bank_code = 2;
  string bank_message = 3;
  string integration_error_code = 4;
  string integration_error_message = 5;
}

message GetTransactionsWithEmptyBankReferenceIDResponseV1 {
  repeated transaction.TransactionDataV1 transaction_ids = 1;
}

message UpdateBankReferenceIDRequestV1 {
  uint64 transaction_id = 1;
  string bank_reference_id = 2;
}