// Code generated by MockGen. DO NOT EDIT.
// Source: multiacquiring.gin.pb.go

// Package grpcmock is a generated GoMock package.
package grpcmock

import (
	reflect "reflect"

	gin "github.com/gin-gonic/gin"
	gomock "github.com/golang/mock/gomock"
)

// MockGinMultiAcquiringServer is a mock of GinMultiAcquiringServer interface.
type MockGinMultiAcquiringServer struct {
	ctrl     *gomock.Controller
	recorder *MockGinMultiAcquiringServerMockRecorder
}

// MockGinMultiAcquiringServerMockRecorder is the mock recorder for MockGinMultiAcquiringServer.
type MockGinMultiAcquiringServerMockRecorder struct {
	mock *MockGinMultiAcquiringServer
}

// NewMockGinMultiAcquiringServer creates a new mock instance.
func NewMockGinMultiAcquiringServer(ctrl *gomock.Controller) *MockGinMultiAcquiringServer {
	mock := &MockGinMultiAcquiringServer{ctrl: ctrl}
	mock.recorder = &MockGinMultiAcquiringServerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockGinMultiAcquiringServer) EXPECT() *MockGinMultiAcquiringServerMockRecorder {
	return m.recorder
}

// ApplePay mocks base method.
func (m *MockGinMultiAcquiringServer) ApplePay(c *gin.Context) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ApplePay", c)
	ret0, _ := ret[0].(error)
	return ret0
}

// ApplePay indicates an expected call of ApplePay.
func (mr *MockGinMultiAcquiringServerMockRecorder) ApplePay(c interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ApplePay", reflect.TypeOf((*MockGinMultiAcquiringServer)(nil).ApplePay), c)
}

// Cancel mocks base method.
func (m *MockGinMultiAcquiringServer) Cancel(c *gin.Context) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Cancel", c)
	ret0, _ := ret[0].(error)
	return ret0
}

// Cancel indicates an expected call of Cancel.
func (mr *MockGinMultiAcquiringServerMockRecorder) Cancel(c interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Cancel", reflect.TypeOf((*MockGinMultiAcquiringServer)(nil).Cancel), c)
}

// Charge mocks base method.
func (m *MockGinMultiAcquiringServer) Charge(c *gin.Context) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Charge", c)
	ret0, _ := ret[0].(error)
	return ret0
}

// Charge indicates an expected call of Charge.
func (mr *MockGinMultiAcquiringServerMockRecorder) Charge(c interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Charge", reflect.TypeOf((*MockGinMultiAcquiringServer)(nil).Charge), c)
}

// GetAcquirerIdentifier mocks base method.
func (m *MockGinMultiAcquiringServer) GetAcquirerIdentifier(c *gin.Context) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAcquirerIdentifier", c)
	ret0, _ := ret[0].(error)
	return ret0
}

// GetAcquirerIdentifier indicates an expected call of GetAcquirerIdentifier.
func (mr *MockGinMultiAcquiringServerMockRecorder) GetAcquirerIdentifier(c interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAcquirerIdentifier", reflect.TypeOf((*MockGinMultiAcquiringServer)(nil).GetAcquirerIdentifier), c)
}

// GetBankTransactionStatus mocks base method.
func (m *MockGinMultiAcquiringServer) GetBankTransactionStatus(c *gin.Context) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetBankTransactionStatus", c)
	ret0, _ := ret[0].(error)
	return ret0
}

// GetBankTransactionStatus indicates an expected call of GetBankTransactionStatus.
func (mr *MockGinMultiAcquiringServerMockRecorder) GetBankTransactionStatus(c interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetBankTransactionStatus", reflect.TypeOf((*MockGinMultiAcquiringServer)(nil).GetBankTransactionStatus), c)
}

// GetBankTransactionStatusUnformated mocks base method.
func (m *MockGinMultiAcquiringServer) GetBankTransactionStatusUnformated(c *gin.Context) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetBankTransactionStatusUnformated", c)
	ret0, _ := ret[0].(error)
	return ret0
}

// GetBankTransactionStatusUnformated indicates an expected call of GetBankTransactionStatusUnformated.
func (mr *MockGinMultiAcquiringServerMockRecorder) GetBankTransactionStatusUnformated(c interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetBankTransactionStatusUnformated", reflect.TypeOf((*MockGinMultiAcquiringServer)(nil).GetBankTransactionStatusUnformated), c)
}

// GooglePay mocks base method.
func (m *MockGinMultiAcquiringServer) GooglePay(c *gin.Context) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GooglePay", c)
	ret0, _ := ret[0].(error)
	return ret0
}

// GooglePay indicates an expected call of GooglePay.
func (mr *MockGinMultiAcquiringServerMockRecorder) GooglePay(c interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GooglePay", reflect.TypeOf((*MockGinMultiAcquiringServer)(nil).GooglePay), c)
}

// MakeToken mocks base method.
func (m *MockGinMultiAcquiringServer) MakeToken(c *gin.Context) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "MakeToken", c)
	ret0, _ := ret[0].(error)
	return ret0
}

// MakeToken indicates an expected call of MakeToken.
func (mr *MockGinMultiAcquiringServerMockRecorder) MakeToken(c interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "MakeToken", reflect.TypeOf((*MockGinMultiAcquiringServer)(nil).MakeToken), c)
}

// OneClickPayIn mocks base method.
func (m *MockGinMultiAcquiringServer) OneClickPayIn(c *gin.Context) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "OneClickPayIn", c)
	ret0, _ := ret[0].(error)
	return ret0
}

// OneClickPayIn indicates an expected call of OneClickPayIn.
func (mr *MockGinMultiAcquiringServerMockRecorder) OneClickPayIn(c interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "OneClickPayIn", reflect.TypeOf((*MockGinMultiAcquiringServer)(nil).OneClickPayIn), c)
}

// PayIn mocks base method.
func (m *MockGinMultiAcquiringServer) PayIn(c *gin.Context) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "PayIn", c)
	ret0, _ := ret[0].(error)
	return ret0
}

// PayIn indicates an expected call of PayIn.
func (mr *MockGinMultiAcquiringServerMockRecorder) PayIn(c interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "PayIn", reflect.TypeOf((*MockGinMultiAcquiringServer)(nil).PayIn), c)
}

// PayOut mocks base method.
func (m *MockGinMultiAcquiringServer) PayOut(c *gin.Context) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "PayOut", c)
	ret0, _ := ret[0].(error)
	return ret0
}

// PayOut indicates an expected call of PayOut.
func (mr *MockGinMultiAcquiringServerMockRecorder) PayOut(c interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "PayOut", reflect.TypeOf((*MockGinMultiAcquiringServer)(nil).PayOut), c)
}

// PayOutByPhone mocks base method.
func (m *MockGinMultiAcquiringServer) PayOutByPhone(c *gin.Context) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "PayOutByPhone", c)
	ret0, _ := ret[0].(error)
	return ret0
}

// PayOutByPhone indicates an expected call of PayOutByPhone.
func (mr *MockGinMultiAcquiringServerMockRecorder) PayOutByPhone(c interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "PayOutByPhone", reflect.TypeOf((*MockGinMultiAcquiringServer)(nil).PayOutByPhone), c)
}

// Refund mocks base method.
func (m *MockGinMultiAcquiringServer) Refund(c *gin.Context) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Refund", c)
	ret0, _ := ret[0].(error)
	return ret0
}

// Refund indicates an expected call of Refund.
func (mr *MockGinMultiAcquiringServerMockRecorder) Refund(c interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Refund", reflect.TypeOf((*MockGinMultiAcquiringServer)(nil).Refund), c)
}

// ResolveVisaAlias mocks base method.
func (m *MockGinMultiAcquiringServer) ResolveVisaAlias(c *gin.Context) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ResolveVisaAlias", c)
	ret0, _ := ret[0].(error)
	return ret0
}

// ResolveVisaAlias indicates an expected call of ResolveVisaAlias.
func (mr *MockGinMultiAcquiringServerMockRecorder) ResolveVisaAlias(c interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ResolveVisaAlias", reflect.TypeOf((*MockGinMultiAcquiringServer)(nil).ResolveVisaAlias), c)
}

// ThreeDSConfirm mocks base method.
func (m *MockGinMultiAcquiringServer) ThreeDSConfirm(c *gin.Context) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ThreeDSConfirm", c)
	ret0, _ := ret[0].(error)
	return ret0
}

// ThreeDSConfirm indicates an expected call of ThreeDSConfirm.
func (mr *MockGinMultiAcquiringServerMockRecorder) ThreeDSConfirm(c interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ThreeDSConfirm", reflect.TypeOf((*MockGinMultiAcquiringServer)(nil).ThreeDSConfirm), c)
}

// ThreeDSResume mocks base method.
func (m *MockGinMultiAcquiringServer) ThreeDSResume(c *gin.Context) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ThreeDSResume", c)
	ret0, _ := ret[0].(error)
	return ret0
}

// ThreeDSResume indicates an expected call of ThreeDSResume.
func (mr *MockGinMultiAcquiringServerMockRecorder) ThreeDSResume(c interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ThreeDSResume", reflect.TypeOf((*MockGinMultiAcquiringServer)(nil).ThreeDSResume), c)
}

// TwoStagePayIn mocks base method.
func (m *MockGinMultiAcquiringServer) TwoStagePayIn(c *gin.Context) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "TwoStagePayIn", c)
	ret0, _ := ret[0].(error)
	return ret0
}

// TwoStagePayIn indicates an expected call of TwoStagePayIn.
func (mr *MockGinMultiAcquiringServerMockRecorder) TwoStagePayIn(c interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "TwoStagePayIn", reflect.TypeOf((*MockGinMultiAcquiringServer)(nil).TwoStagePayIn), c)
}
