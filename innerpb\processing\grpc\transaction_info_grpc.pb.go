// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.5.1
// - protoc             v5.29.3
// source: inner/processing/grpc/transaction_info.proto

package grpc

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.64.0 or later.
const _ = grpc.SupportPackageIsVersion9

const (
	TransactionInf_UpdateJobsMessage_FullMethodName                       = "/processing.transaction.transaction_info.TransactionInf/UpdateJobsMessage"
	TransactionInf_UpdateBankResponseMessage_FullMethodName               = "/processing.transaction.transaction_info.TransactionInf/UpdateBankResponseMessage"
	TransactionInf_GetTransactionsWithEmptyBankReferenceID_FullMethodName = "/processing.transaction.transaction_info.TransactionInf/GetTransactionsWithEmptyBankReferenceID"
	TransactionInf_UpdateBankReferenceID_FullMethodName                   = "/processing.transaction.transaction_info.TransactionInf/UpdateBankReferenceID"
)

// TransactionInfClient is the client API for TransactionInf service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type TransactionInfClient interface {
	UpdateJobsMessage(ctx context.Context, in *UpdateJobsMessageRequestV1, opts ...grpc.CallOption) (*emptypb.Empty, error)
	UpdateBankResponseMessage(ctx context.Context, in *UpdateBankResponseMessageRequestV1, opts ...grpc.CallOption) (*emptypb.Empty, error)
	GetTransactionsWithEmptyBankReferenceID(ctx context.Context, in *emptypb.Empty, opts ...grpc.CallOption) (*GetTransactionsWithEmptyBankReferenceIDResponseV1, error)
	UpdateBankReferenceID(ctx context.Context, in *UpdateBankReferenceIDRequestV1, opts ...grpc.CallOption) (*emptypb.Empty, error)
}

type transactionInfClient struct {
	cc grpc.ClientConnInterface
}

func NewTransactionInfClient(cc grpc.ClientConnInterface) TransactionInfClient {
	return &transactionInfClient{cc}
}

func (c *transactionInfClient) UpdateJobsMessage(ctx context.Context, in *UpdateJobsMessageRequestV1, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, TransactionInf_UpdateJobsMessage_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *transactionInfClient) UpdateBankResponseMessage(ctx context.Context, in *UpdateBankResponseMessageRequestV1, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, TransactionInf_UpdateBankResponseMessage_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *transactionInfClient) GetTransactionsWithEmptyBankReferenceID(ctx context.Context, in *emptypb.Empty, opts ...grpc.CallOption) (*GetTransactionsWithEmptyBankReferenceIDResponseV1, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetTransactionsWithEmptyBankReferenceIDResponseV1)
	err := c.cc.Invoke(ctx, TransactionInf_GetTransactionsWithEmptyBankReferenceID_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *transactionInfClient) UpdateBankReferenceID(ctx context.Context, in *UpdateBankReferenceIDRequestV1, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, TransactionInf_UpdateBankReferenceID_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// TransactionInfServer is the server API for TransactionInf service.
// All implementations must embed UnimplementedTransactionInfServer
// for forward compatibility.
type TransactionInfServer interface {
	UpdateJobsMessage(context.Context, *UpdateJobsMessageRequestV1) (*emptypb.Empty, error)
	UpdateBankResponseMessage(context.Context, *UpdateBankResponseMessageRequestV1) (*emptypb.Empty, error)
	GetTransactionsWithEmptyBankReferenceID(context.Context, *emptypb.Empty) (*GetTransactionsWithEmptyBankReferenceIDResponseV1, error)
	UpdateBankReferenceID(context.Context, *UpdateBankReferenceIDRequestV1) (*emptypb.Empty, error)
	mustEmbedUnimplementedTransactionInfServer()
}

// UnimplementedTransactionInfServer must be embedded to have
// forward compatible implementations.
//
// NOTE: this should be embedded by value instead of pointer to avoid a nil
// pointer dereference when methods are called.
type UnimplementedTransactionInfServer struct{}

func (UnimplementedTransactionInfServer) UpdateJobsMessage(context.Context, *UpdateJobsMessageRequestV1) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateJobsMessage not implemented")
}
func (UnimplementedTransactionInfServer) UpdateBankResponseMessage(context.Context, *UpdateBankResponseMessageRequestV1) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateBankResponseMessage not implemented")
}
func (UnimplementedTransactionInfServer) GetTransactionsWithEmptyBankReferenceID(context.Context, *emptypb.Empty) (*GetTransactionsWithEmptyBankReferenceIDResponseV1, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetTransactionsWithEmptyBankReferenceID not implemented")
}
func (UnimplementedTransactionInfServer) UpdateBankReferenceID(context.Context, *UpdateBankReferenceIDRequestV1) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateBankReferenceID not implemented")
}
func (UnimplementedTransactionInfServer) mustEmbedUnimplementedTransactionInfServer() {}
func (UnimplementedTransactionInfServer) testEmbeddedByValue()                        {}

// UnsafeTransactionInfServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to TransactionInfServer will
// result in compilation errors.
type UnsafeTransactionInfServer interface {
	mustEmbedUnimplementedTransactionInfServer()
}

func RegisterTransactionInfServer(s grpc.ServiceRegistrar, srv TransactionInfServer) {
	// If the following call pancis, it indicates UnimplementedTransactionInfServer was
	// embedded by pointer and is nil.  This will cause panics if an
	// unimplemented method is ever invoked, so we test this at initialization
	// time to prevent it from happening at runtime later due to I/O.
	if t, ok := srv.(interface{ testEmbeddedByValue() }); ok {
		t.testEmbeddedByValue()
	}
	s.RegisterService(&TransactionInf_ServiceDesc, srv)
}

func _TransactionInf_UpdateJobsMessage_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateJobsMessageRequestV1)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TransactionInfServer).UpdateJobsMessage(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: TransactionInf_UpdateJobsMessage_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TransactionInfServer).UpdateJobsMessage(ctx, req.(*UpdateJobsMessageRequestV1))
	}
	return interceptor(ctx, in, info, handler)
}

func _TransactionInf_UpdateBankResponseMessage_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateBankResponseMessageRequestV1)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TransactionInfServer).UpdateBankResponseMessage(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: TransactionInf_UpdateBankResponseMessage_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TransactionInfServer).UpdateBankResponseMessage(ctx, req.(*UpdateBankResponseMessageRequestV1))
	}
	return interceptor(ctx, in, info, handler)
}

func _TransactionInf_GetTransactionsWithEmptyBankReferenceID_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(emptypb.Empty)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TransactionInfServer).GetTransactionsWithEmptyBankReferenceID(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: TransactionInf_GetTransactionsWithEmptyBankReferenceID_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TransactionInfServer).GetTransactionsWithEmptyBankReferenceID(ctx, req.(*emptypb.Empty))
	}
	return interceptor(ctx, in, info, handler)
}

func _TransactionInf_UpdateBankReferenceID_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateBankReferenceIDRequestV1)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TransactionInfServer).UpdateBankReferenceID(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: TransactionInf_UpdateBankReferenceID_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TransactionInfServer).UpdateBankReferenceID(ctx, req.(*UpdateBankReferenceIDRequestV1))
	}
	return interceptor(ctx, in, info, handler)
}

// TransactionInf_ServiceDesc is the grpc.ServiceDesc for TransactionInf service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var TransactionInf_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "processing.transaction.transaction_info.TransactionInf",
	HandlerType: (*TransactionInfServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "UpdateJobsMessage",
			Handler:    _TransactionInf_UpdateJobsMessage_Handler,
		},
		{
			MethodName: "UpdateBankResponseMessage",
			Handler:    _TransactionInf_UpdateBankResponseMessage_Handler,
		},
		{
			MethodName: "GetTransactionsWithEmptyBankReferenceID",
			Handler:    _TransactionInf_GetTransactionsWithEmptyBankReferenceID_Handler,
		},
		{
			MethodName: "UpdateBankReferenceID",
			Handler:    _TransactionInf_UpdateBankReferenceID_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "inner/processing/grpc/transaction_info.proto",
}
