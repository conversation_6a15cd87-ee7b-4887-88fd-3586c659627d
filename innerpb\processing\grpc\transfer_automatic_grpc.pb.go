// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.5.1
// - protoc             v5.29.3
// source: inner/processing/grpc/transfer_automatic.proto

package grpc

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.64.0 or later.
const _ = grpc.SupportPackageIsVersion9

const (
	TransferAutomatic_StartCreateTransferByRulesWorker_FullMethodName = "/processing.transfer_automatic.transfer_automatic.TransferAutomatic/StartCreateTransferByRulesWorker"
)

// TransferAutomaticClient is the client API for TransferAutomatic service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type TransferAutomaticClient interface {
	StartCreateTransferByRulesWorker(ctx context.Context, in *CreateTransferByRulesRequest, opts ...grpc.CallOption) (*emptypb.Empty, error)
}

type transferAutomaticClient struct {
	cc grpc.ClientConnInterface
}

func NewTransferAutomaticClient(cc grpc.ClientConnInterface) TransferAutomaticClient {
	return &transferAutomaticClient{cc}
}

func (c *transferAutomaticClient) StartCreateTransferByRulesWorker(ctx context.Context, in *CreateTransferByRulesRequest, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, TransferAutomatic_StartCreateTransferByRulesWorker_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// TransferAutomaticServer is the server API for TransferAutomatic service.
// All implementations must embed UnimplementedTransferAutomaticServer
// for forward compatibility.
type TransferAutomaticServer interface {
	StartCreateTransferByRulesWorker(context.Context, *CreateTransferByRulesRequest) (*emptypb.Empty, error)
	mustEmbedUnimplementedTransferAutomaticServer()
}

// UnimplementedTransferAutomaticServer must be embedded to have
// forward compatible implementations.
//
// NOTE: this should be embedded by value instead of pointer to avoid a nil
// pointer dereference when methods are called.
type UnimplementedTransferAutomaticServer struct{}

func (UnimplementedTransferAutomaticServer) StartCreateTransferByRulesWorker(context.Context, *CreateTransferByRulesRequest) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method StartCreateTransferByRulesWorker not implemented")
}
func (UnimplementedTransferAutomaticServer) mustEmbedUnimplementedTransferAutomaticServer() {}
func (UnimplementedTransferAutomaticServer) testEmbeddedByValue()                           {}

// UnsafeTransferAutomaticServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to TransferAutomaticServer will
// result in compilation errors.
type UnsafeTransferAutomaticServer interface {
	mustEmbedUnimplementedTransferAutomaticServer()
}

func RegisterTransferAutomaticServer(s grpc.ServiceRegistrar, srv TransferAutomaticServer) {
	// If the following call pancis, it indicates UnimplementedTransferAutomaticServer was
	// embedded by pointer and is nil.  This will cause panics if an
	// unimplemented method is ever invoked, so we test this at initialization
	// time to prevent it from happening at runtime later due to I/O.
	if t, ok := srv.(interface{ testEmbeddedByValue() }); ok {
		t.testEmbeddedByValue()
	}
	s.RegisterService(&TransferAutomatic_ServiceDesc, srv)
}

func _TransferAutomatic_StartCreateTransferByRulesWorker_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateTransferByRulesRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TransferAutomaticServer).StartCreateTransferByRulesWorker(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: TransferAutomatic_StartCreateTransferByRulesWorker_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TransferAutomaticServer).StartCreateTransferByRulesWorker(ctx, req.(*CreateTransferByRulesRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// TransferAutomatic_ServiceDesc is the grpc.ServiceDesc for TransferAutomatic service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var TransferAutomatic_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "processing.transfer_automatic.transfer_automatic.TransferAutomatic",
	HandlerType: (*TransferAutomaticServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "StartCreateTransferByRulesWorker",
			Handler:    _TransferAutomatic_StartCreateTransferByRulesWorker_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "inner/processing/grpc/transfer_automatic.proto",
}
