package service

import (
	"context"
	"errors"
	"testing"
	"time"

	"github.com/golang/mock/gomock"
	"github.com/stretchr/testify/require"
	"google.golang.org/protobuf/types/known/emptypb"
	"google.golang.org/protobuf/types/known/structpb"

	"git.local/sensitive/innerpb/processing/goerr"
	gorpc "git.local/sensitive/innerpb/processing/grpc"
	"git.local/sensitive/innerpb/processing/grpc/grpcmock"
	"git.local/sensitive/processing/account/model"
	"git.local/sensitive/processing/account/repository/databasemocks"
	"git.local/sensitive/sdk/dog/e2e"
	"git.local/sensitive/testsdk"
)

func TestOldTransfersFinalizerService_FinalizeOldTransfers(t *testing.T) {
	nowFn := time.Date(2023, 1, 1, 12, 0, 0, 0, time.UTC)
	defaultCfg, err := structpb.NewStruct(map[string]any{
		"my-name-is": "skrillex",
	})
	if err != nil {
		t.Fatal(err)
	}

	type transferStatusMockExpect struct {
		wantRes map[model.TransferStatusCode]uint64
		wantErr error
	}

	type transferMockExpect struct {
		createdDateFrom time.Time
		createdDateTo   time.Time
		statusID        uint64
		wantRes         []model.Transfer
		wantErr         error
		expect          bool
	}

	type accountInfoMockExpect struct {
		id      uint64
		wanRes  *model.Account
		wantErr error
		expect  bool
	}

	type multiAccountingClientMockExpect struct {
		req     *gorpc.GetTransferDetailsRequest
		wantRes *gorpc.GetTransferDetailsResponse
		wantErr error
		expect  bool
	}

	type transferManagerMockExpect struct {
		transferID uint64
		statusID   uint64
		finishedAt *time.Time
		wantErr    error
		expect     bool
	}

	type in struct {
		req     *gorpc.BillInTransferRequestV1
		wantRes *emptypb.Empty
		wantErr error
		expect  bool
	}

	type out struct {
		req     *gorpc.BillOutTransferRequestV1
		wantRes *emptypb.Empty
		wantErr error
		expect  bool
	}

	type billingClientMockExpect struct {
		in
		out
	}

	type transferStatusMockExpectSecond struct {
		statusID uint64
		wantRes  *model.TransferStatus
		wantErr  error
		expect   bool
	}

	tests := []struct {
		name                            string
		wantErr                         error
		transferStatusMockExpect        transferStatusMockExpect
		transferMockExpect              transferMockExpect
		accountInfoMockExpect           accountInfoMockExpect
		transferStatusMockExpectSecond  transferStatusMockExpectSecond
		multiAccountingClientMockExpect multiAccountingClientMockExpect
		transferManagerMockExpectFirst  transferManagerMockExpect
		transferManagerMockExpectSecond transferManagerMockExpect
		billingClientMockExpect         billingClientMockExpect
		appConfig                       map[string]any
	}{
		{
			name:    "Success_FinalizeOldTransfers_BillIn",
			wantErr: nil,
			transferStatusMockExpect: transferStatusMockExpect{
				wantRes: map[model.TransferStatusCode]uint64{
					model.StatusAuthorized: 3,
					model.StatusSuccess:    4,
				},
				wantErr: nil,
			},
			transferMockExpect: transferMockExpect{
				createdDateFrom: nowFn.Add(-30 * time.Hour * 24),
				createdDateTo:   nowFn.Add(-7 * time.Hour * 24),
				statusID:        3,
				wantRes: []model.Transfer{
					{
						AccountID:  1,
						ID:         1,
						FinishedAt: nil,
						TransferType: model.TransferType{
							Code: model.TypeIn,
						},
						Amount:     100,
						MerchantID: 1,
						ProjectID:  1,
					},
				},

				wantErr: nil,
				expect:  true,
			},
			accountInfoMockExpect: accountInfoMockExpect{
				id: 1,
				wanRes: &model.Account{
					ID:              1,
					Number:          "some",
					CurrencyCode:    "some",
					BankCode:        "some",
					AccountTypeID:   1,
					EncryptedConfig: defaultEncryptedConfig,
					BankID:          1,
				},
				wantErr: nil,
				expect:  true,
			},
			multiAccountingClientMockExpect: multiAccountingClientMockExpect{
				req: &gorpc.GetTransferDetailsRequest{
					TransferId: testsdk.Ptr(uint64(1)),
					Account: &gorpc.AccountTerminal{
						BankCode: testsdk.Ptr("some"),
						Config:   defaultCfg,
					},
					ExternalReferenceId: testsdk.Ptr("1"),
				},
				wantRes: &gorpc.GetTransferDetailsResponse{
					TransferId:       testsdk.Ptr(uint64(1)),
					TransferStatusId: testsdk.Ptr(uint64(4)),
				},
				wantErr: nil,
				expect:  true,
			},
			billingClientMockExpect: billingClientMockExpect{
				in: in{
					req: &gorpc.BillInTransferRequestV1{
						ProjectId:     testsdk.Ptr(uint64(1)),
						AccountNumber: testsdk.Ptr("some"),
						Amount:        testsdk.Ptr(float64(100)),
						MerchantId:    testsdk.Ptr(uint64(1)),
						TransferId:    testsdk.Ptr(uint64(1)),
					},
					wantRes: &emptypb.Empty{},
					wantErr: nil,
					expect:  true,
				},
				out: out{
					req: &gorpc.BillOutTransferRequestV1{
						ProjectId:     testsdk.Ptr(uint64(1)),
						AccountNumber: testsdk.Ptr("some"),
						Amount:        testsdk.Ptr(float64(100)),
						MerchantId:    testsdk.Ptr(uint64(1)),
						TransferId:    testsdk.Ptr(uint64(1)),
					},
					wantRes: &emptypb.Empty{},
					wantErr: nil,
					expect:  false,
				},
			},
			transferStatusMockExpectSecond: transferStatusMockExpectSecond{
				statusID: 4,
				wantRes: &model.TransferStatus{
					ID:   4,
					Code: "success",
				},
				expect: true,
			},
			transferManagerMockExpectFirst: transferManagerMockExpect{
				transferID: 1,
				statusID:   4,
				finishedAt: &nowFn,
				wantErr:    nil,
				expect:     true,
			},
			transferManagerMockExpectSecond: transferManagerMockExpect{
				transferID: 1,
				statusID:   1,
				finishedAt: nil,
				wantErr:    nil,
				expect:     false,
			},
			appConfig: map[string]any{
				"SYMMETRIC_KEY": normalKey,
			},
		},
		{
			name:    "Success_FinalizeOldTransfers_BillOut",
			wantErr: nil,
			transferStatusMockExpect: transferStatusMockExpect{
				wantRes: map[model.TransferStatusCode]uint64{
					model.StatusAuthorized: 3,
					model.StatusSuccess:    4,
				},
				wantErr: nil,
			},
			transferMockExpect: transferMockExpect{
				createdDateFrom: nowFn.Add(-30 * time.Hour * 24),
				createdDateTo:   nowFn.Add(-7 * time.Hour * 24),
				statusID:        3,
				wantRes: []model.Transfer{
					{
						AccountID:  1,
						ID:         1,
						FinishedAt: nil,
						TransferType: model.TransferType{
							Code: model.TypeOut,
						},
						Amount:     100,
						MerchantID: 1,
						ProjectID:  1,
					},
				},

				wantErr: nil,
				expect:  true,
			},
			accountInfoMockExpect: accountInfoMockExpect{
				id: 1,
				wanRes: &model.Account{
					ID:              1,
					Number:          "some",
					CurrencyCode:    "some",
					BankCode:        "some",
					AccountTypeID:   1,
					EncryptedConfig: defaultEncryptedConfig,
					BankID:          1,
				},
				wantErr: nil,
				expect:  true,
			},
			multiAccountingClientMockExpect: multiAccountingClientMockExpect{
				req: &gorpc.GetTransferDetailsRequest{
					TransferId: testsdk.Ptr(uint64(1)),
					Account: &gorpc.AccountTerminal{
						BankCode: testsdk.Ptr("some"),
						Config:   defaultCfg,
					},
					ExternalReferenceId: testsdk.Ptr("1"),
				},
				wantRes: &gorpc.GetTransferDetailsResponse{
					TransferId:       testsdk.Ptr(uint64(1)),
					TransferStatusId: testsdk.Ptr(uint64(4)),
				},
				wantErr: nil,
				expect:  true,
			},
			billingClientMockExpect: billingClientMockExpect{
				in: in{
					req: &gorpc.BillInTransferRequestV1{
						ProjectId:     testsdk.Ptr(uint64(1)),
						AccountNumber: testsdk.Ptr("some"),
						Amount:        testsdk.Ptr(float64(100)),
						MerchantId:    testsdk.Ptr(uint64(1)),
						TransferId:    testsdk.Ptr(uint64(1)),
					},
					wantRes: &emptypb.Empty{},
					wantErr: nil,
					expect:  false,
				},
				out: out{
					req: &gorpc.BillOutTransferRequestV1{
						ProjectId:     testsdk.Ptr(uint64(1)),
						AccountNumber: testsdk.Ptr("some"),
						Amount:        testsdk.Ptr(float64(100)),
						MerchantId:    testsdk.Ptr(uint64(1)),
						TransferId:    testsdk.Ptr(uint64(1)),
					},
					wantRes: &emptypb.Empty{},
					wantErr: nil,
					expect:  true,
				},
			},
			transferStatusMockExpectSecond: transferStatusMockExpectSecond{
				statusID: 4,
				wantRes: &model.TransferStatus{
					ID:   4,
					Code: "success",
				},
				expect: true,
			},
			transferManagerMockExpectFirst: transferManagerMockExpect{
				transferID: 1,
				statusID:   4,
				finishedAt: &nowFn,
				wantErr:    nil,
				expect:     true,
			},
			transferManagerMockExpectSecond: transferManagerMockExpect{
				transferID: 1,
				statusID:   1,
				finishedAt: nil,
				wantErr:    nil,
				expect:     false,
			},
			appConfig: map[string]any{
				"SYMMETRIC_KEY": normalKey,
			},
		},
		{
			name:    "Failed_error_AsCodeMap",
			wantErr: goerr.ErrDbUnexpected,
			transferStatusMockExpect: transferStatusMockExpect{
				wantRes: nil,
				wantErr: goerr.ErrDbUnexpected,
			},
			transferMockExpect: transferMockExpect{
				createdDateFrom: nowFn.Add(-30 * time.Hour * 24),
				createdDateTo:   nowFn.Add(-7 * time.Hour * 24),
				statusID:        3,
				wantRes: []model.Transfer{
					{
						AccountID:  1,
						ID:         1,
						FinishedAt: nil,
						TransferType: model.TransferType{
							Code: model.TypeIn,
						},
						Amount:     100,
						MerchantID: 1,
						ProjectID:  1,
					},
				},

				wantErr: nil,
				expect:  false,
			},
			accountInfoMockExpect: accountInfoMockExpect{
				id: 1,
				wanRes: &model.Account{
					ID:              1,
					Number:          "some",
					CurrencyCode:    "some",
					BankCode:        "some",
					AccountTypeID:   1,
					EncryptedConfig: defaultEncryptedConfig,
					BankID:          1,
				},
				wantErr: nil,
				expect:  false,
			},
			multiAccountingClientMockExpect: multiAccountingClientMockExpect{
				req: &gorpc.GetTransferDetailsRequest{
					TransferId: testsdk.Ptr(uint64(1)),
					Account: &gorpc.AccountTerminal{
						BankCode: testsdk.Ptr("some"),
						Config:   defaultCfg,
					},
					ExternalReferenceId: testsdk.Ptr("1"),
				},
				wantRes: &gorpc.GetTransferDetailsResponse{
					TransferId:       testsdk.Ptr(uint64(1)),
					TransferStatusId: testsdk.Ptr(uint64(4)),
				},
				wantErr: nil,
				expect:  false,
			},
			billingClientMockExpect: billingClientMockExpect{
				in: in{
					req: &gorpc.BillInTransferRequestV1{
						ProjectId:     testsdk.Ptr(uint64(1)),
						AccountNumber: testsdk.Ptr("some"),
						Amount:        testsdk.Ptr(float64(100)),
						MerchantId:    testsdk.Ptr(uint64(1)),
						TransferId:    testsdk.Ptr(uint64(1)),
					},
					wantRes: &emptypb.Empty{},
					wantErr: nil,
					expect:  false,
				},
				out: out{
					req: &gorpc.BillOutTransferRequestV1{
						ProjectId:     testsdk.Ptr(uint64(1)),
						AccountNumber: testsdk.Ptr("some"),
						Amount:        testsdk.Ptr(float64(100)),
						MerchantId:    testsdk.Ptr(uint64(1)),
						TransferId:    testsdk.Ptr(uint64(1)),
					},
					wantRes: &emptypb.Empty{},
					wantErr: nil,
					expect:  false,
				},
			},
			transferManagerMockExpectFirst: transferManagerMockExpect{
				transferID: 1,
				statusID:   4,
				finishedAt: &nowFn,
				wantErr:    nil,
				expect:     false,
			},
			transferManagerMockExpectSecond: transferManagerMockExpect{
				transferID: 1,
				statusID:   1,
				finishedAt: nil,
				wantErr:    nil,
				expect:     false,
			},
			appConfig: map[string]any{
				"SYMMETRIC_KEY": normalKey,
			},
		},
		{
			name:    "Failed_error_GetByPeriod",
			wantErr: goerr.ErrDbUnexpected,
			transferStatusMockExpect: transferStatusMockExpect{
				wantRes: map[model.TransferStatusCode]uint64{
					model.StatusAuthorized: 3,
					model.StatusSuccess:    4,
				},
				wantErr: nil,
			},
			transferMockExpect: transferMockExpect{
				createdDateFrom: nowFn.Add(-30 * time.Hour * 24),
				createdDateTo:   nowFn.Add(-7 * time.Hour * 24),
				statusID:        3,
				wantRes:         nil,
				wantErr:         goerr.ErrDbUnexpected,
				expect:          true,
			},
			accountInfoMockExpect: accountInfoMockExpect{
				id: 1,
				wanRes: &model.Account{
					ID:              1,
					Number:          "some",
					CurrencyCode:    "some",
					BankCode:        "some",
					AccountTypeID:   1,
					EncryptedConfig: defaultEncryptedConfig,
					BankID:          1,
				},
				wantErr: nil,
				expect:  false,
			},
			multiAccountingClientMockExpect: multiAccountingClientMockExpect{
				req: &gorpc.GetTransferDetailsRequest{
					TransferId: testsdk.Ptr(uint64(1)),
					Account: &gorpc.AccountTerminal{
						BankCode: testsdk.Ptr("some"),
						Config:   defaultCfg,
					},
					ExternalReferenceId: testsdk.Ptr("1"),
				},
				wantRes: &gorpc.GetTransferDetailsResponse{
					TransferId:       testsdk.Ptr(uint64(1)),
					TransferStatusId: testsdk.Ptr(uint64(4)),
				},
				wantErr: nil,
				expect:  false,
			},
			billingClientMockExpect: billingClientMockExpect{
				in: in{
					req: &gorpc.BillInTransferRequestV1{
						ProjectId:     testsdk.Ptr(uint64(1)),
						AccountNumber: testsdk.Ptr("some"),
						Amount:        testsdk.Ptr(float64(100)),
						MerchantId:    testsdk.Ptr(uint64(1)),
						TransferId:    testsdk.Ptr(uint64(1)),
					},
					wantRes: &emptypb.Empty{},
					wantErr: nil,
					expect:  false,
				},
				out: out{
					req: &gorpc.BillOutTransferRequestV1{
						ProjectId:     testsdk.Ptr(uint64(1)),
						AccountNumber: testsdk.Ptr("some"),
						Amount:        testsdk.Ptr(float64(100)),
						MerchantId:    testsdk.Ptr(uint64(1)),
						TransferId:    testsdk.Ptr(uint64(1)),
					},
					wantRes: &emptypb.Empty{},
					wantErr: nil,
					expect:  false,
				},
			},
			transferManagerMockExpectFirst: transferManagerMockExpect{
				transferID: 1,
				statusID:   4,
				finishedAt: nil,
				wantErr:    nil,
				expect:     false,
			},
			transferManagerMockExpectSecond: transferManagerMockExpect{
				transferID: 1,
				statusID:   1,
				finishedAt: nil,
				wantErr:    nil,
				expect:     false,
			},
			appConfig: map[string]any{
				"SYMMETRIC_KEY": normalKey,
			},
		},
		{
			name:    "Failed_error_GetById",
			wantErr: goerr.ErrDbUnexpected,
			transferStatusMockExpect: transferStatusMockExpect{
				wantRes: map[model.TransferStatusCode]uint64{
					model.StatusAuthorized: 3,
					model.StatusSuccess:    4,
				},
				wantErr: nil,
			},
			transferMockExpect: transferMockExpect{
				createdDateFrom: nowFn.Add(-30 * time.Hour * 24),
				createdDateTo:   nowFn.Add(-7 * time.Hour * 24),
				statusID:        3,
				wantRes: []model.Transfer{
					{
						AccountID:  1,
						ID:         1,
						FinishedAt: nil,
						TransferType: model.TransferType{
							Code: model.TypeIn,
						},
						Amount:     100,
						MerchantID: 1,
						ProjectID:  1,
					},
				},

				wantErr: nil,
				expect:  true,
			},
			accountInfoMockExpect: accountInfoMockExpect{
				id:      1,
				wanRes:  nil,
				wantErr: goerr.ErrDbUnexpected,
				expect:  true,
			},
			multiAccountingClientMockExpect: multiAccountingClientMockExpect{
				req: &gorpc.GetTransferDetailsRequest{
					TransferId: testsdk.Ptr(uint64(1)),
					Account: &gorpc.AccountTerminal{
						BankCode: testsdk.Ptr("some"),
						Config:   defaultCfg,
					},
					ExternalReferenceId: testsdk.Ptr("1"),
				},
				wantRes: &gorpc.GetTransferDetailsResponse{
					TransferId:       testsdk.Ptr(uint64(1)),
					TransferStatusId: testsdk.Ptr(uint64(4)),
				},
				wantErr: nil,
				expect:  false,
			},
			billingClientMockExpect: billingClientMockExpect{
				in: in{
					req: &gorpc.BillInTransferRequestV1{
						ProjectId:     testsdk.Ptr(uint64(1)),
						AccountNumber: testsdk.Ptr("some"),
						Amount:        testsdk.Ptr(float64(100)),
						MerchantId:    testsdk.Ptr(uint64(1)),
						TransferId:    testsdk.Ptr(uint64(1)),
					},
					wantRes: &emptypb.Empty{},
					wantErr: nil,
					expect:  false,
				},
				out: out{
					req: &gorpc.BillOutTransferRequestV1{
						ProjectId:     testsdk.Ptr(uint64(1)),
						AccountNumber: testsdk.Ptr("some"),
						Amount:        testsdk.Ptr(float64(100)),
						MerchantId:    testsdk.Ptr(uint64(1)),
						TransferId:    testsdk.Ptr(uint64(1)),
					},
					wantRes: &emptypb.Empty{},
					wantErr: nil,
					expect:  false,
				},
			},
			transferManagerMockExpectFirst: transferManagerMockExpect{
				transferID: 1,
				statusID:   4,
				finishedAt: nil,
				wantErr:    nil,
				expect:     false,
			},
			transferManagerMockExpectSecond: transferManagerMockExpect{
				transferID: 1,
				statusID:   1,
				finishedAt: nil,
				wantErr:    nil,
				expect:     false,
			},
			appConfig: map[string]any{
				"SYMMETRIC_KEY": normalKey,
			},
		},
		{
			name:    "Failed_error_multiAccountingClient",
			wantErr: errors.New("some error"),
			transferStatusMockExpect: transferStatusMockExpect{
				wantRes: map[model.TransferStatusCode]uint64{
					model.StatusAuthorized: 3,
					model.StatusSuccess:    4,
				},
				wantErr: nil,
			},
			transferMockExpect: transferMockExpect{
				createdDateFrom: nowFn.Add(-30 * time.Hour * 24),
				createdDateTo:   nowFn.Add(-7 * time.Hour * 24),
				statusID:        3,
				wantRes: []model.Transfer{
					{
						AccountID:  1,
						ID:         1,
						FinishedAt: nil,
						TransferType: model.TransferType{
							Code: model.TypeIn,
						},
						Amount:     100,
						MerchantID: 1,
						ProjectID:  1,
					},
				},

				wantErr: nil,
				expect:  true,
			},
			accountInfoMockExpect: accountInfoMockExpect{
				id: 1,
				wanRes: &model.Account{
					ID:              1,
					Number:          "some",
					CurrencyCode:    "some",
					BankCode:        "some",
					AccountTypeID:   1,
					EncryptedConfig: defaultEncryptedConfig,
					BankID:          1,
				},
				wantErr: nil,
				expect:  true,
			},
			multiAccountingClientMockExpect: multiAccountingClientMockExpect{
				req: &gorpc.GetTransferDetailsRequest{
					TransferId: testsdk.Ptr(uint64(1)),
					Account: &gorpc.AccountTerminal{
						BankCode: testsdk.Ptr("some"),
						Config:   defaultCfg,
					},
					ExternalReferenceId: testsdk.Ptr("1"),
				},
				wantRes: nil,
				wantErr: errors.New("some error"),
				expect:  true,
			},
			billingClientMockExpect: billingClientMockExpect{
				in: in{
					req: &gorpc.BillInTransferRequestV1{
						ProjectId:     testsdk.Ptr(uint64(1)),
						AccountNumber: testsdk.Ptr("some"),
						Amount:        testsdk.Ptr(float64(100)),
						MerchantId:    testsdk.Ptr(uint64(1)),
						TransferId:    testsdk.Ptr(uint64(1)),
					},
					wantRes: &emptypb.Empty{},
					wantErr: nil,
					expect:  false,
				},
				out: out{
					req: &gorpc.BillOutTransferRequestV1{
						ProjectId:     testsdk.Ptr(uint64(1)),
						AccountNumber: testsdk.Ptr("some"),
						Amount:        testsdk.Ptr(float64(100)),
						MerchantId:    testsdk.Ptr(uint64(1)),
						TransferId:    testsdk.Ptr(uint64(1)),
					},
					wantRes: &emptypb.Empty{},
					wantErr: nil,
					expect:  false,
				},
			},
			transferManagerMockExpectFirst: transferManagerMockExpect{
				transferID: 1,
				statusID:   4,
				finishedAt: nil,
				wantErr:    nil,
				expect:     false,
			},
			transferManagerMockExpectSecond: transferManagerMockExpect{
				transferID: 1,
				statusID:   1,
				finishedAt: nil,
				wantErr:    nil,
				expect:     false,
			},
			appConfig: map[string]any{
				"SYMMETRIC_KEY": normalKey,
			},
		},
		{
			name:    "Failed_error_BillInTransferV1",
			wantErr: nil,
			transferStatusMockExpect: transferStatusMockExpect{
				wantRes: map[model.TransferStatusCode]uint64{
					model.StatusAuthorized: 3,
					model.StatusSuccess:    4,
				},
				wantErr: nil,
			},
			transferMockExpect: transferMockExpect{
				createdDateFrom: nowFn.Add(-30 * time.Hour * 24),
				createdDateTo:   nowFn.Add(-7 * time.Hour * 24),
				statusID:        3,
				wantRes: []model.Transfer{
					{
						AccountID:  1,
						ID:         1,
						FinishedAt: nil,
						TransferType: model.TransferType{
							Code: model.TypeIn,
						},
						Amount:     100,
						MerchantID: 1,
						ProjectID:  1,
					},
				},

				wantErr: nil,
				expect:  true,
			},
			accountInfoMockExpect: accountInfoMockExpect{
				id: 1,
				wanRes: &model.Account{
					ID:              1,
					Number:          "some",
					CurrencyCode:    "some",
					BankCode:        "some",
					AccountTypeID:   1,
					EncryptedConfig: defaultEncryptedConfig,
					BankID:          1,
				},
				wantErr: nil,
				expect:  true,
			},
			multiAccountingClientMockExpect: multiAccountingClientMockExpect{
				req: &gorpc.GetTransferDetailsRequest{
					TransferId: testsdk.Ptr(uint64(1)),
					Account: &gorpc.AccountTerminal{
						BankCode: testsdk.Ptr("some"),
						Config:   defaultCfg,
					},
					ExternalReferenceId: testsdk.Ptr("1"),
				},
				wantRes: &gorpc.GetTransferDetailsResponse{
					TransferId:       testsdk.Ptr(uint64(1)),
					TransferStatusId: testsdk.Ptr(uint64(4)),
				},
				wantErr: nil,
				expect:  true,
			},
			billingClientMockExpect: billingClientMockExpect{
				in: in{
					req: &gorpc.BillInTransferRequestV1{
						ProjectId:     testsdk.Ptr(uint64(1)),
						AccountNumber: testsdk.Ptr("some"),
						Amount:        testsdk.Ptr(float64(100)),
						MerchantId:    testsdk.Ptr(uint64(1)),
						TransferId:    testsdk.Ptr(uint64(1)),
					},
					wantRes: &emptypb.Empty{},
					wantErr: errors.New("some error"),
					expect:  true,
				},
				out: out{
					req: &gorpc.BillOutTransferRequestV1{
						ProjectId:     testsdk.Ptr(uint64(1)),
						AccountNumber: testsdk.Ptr("some"),
						Amount:        testsdk.Ptr(float64(100)),
						MerchantId:    testsdk.Ptr(uint64(1)),
						TransferId:    testsdk.Ptr(uint64(1)),
					},
					wantRes: &emptypb.Empty{},
					wantErr: nil,
					expect:  false,
				},
			},
			transferStatusMockExpectSecond: transferStatusMockExpectSecond{
				statusID: 4,
				wantRes: &model.TransferStatus{
					ID:   4,
					Code: "success",
				},
				expect: true,
			},
			transferManagerMockExpectFirst: transferManagerMockExpect{
				transferID: 1,
				statusID:   4,
				finishedAt: &nowFn,
				wantErr:    nil,
				expect:     true,
			},
			transferManagerMockExpectSecond: transferManagerMockExpect{
				transferID: 1,
				statusID:   3,
				finishedAt: nil,
				wantErr:    nil,
				expect:     true,
			},
			appConfig: map[string]any{
				"SYMMETRIC_KEY": normalKey,
			},
		},
		{
			name:    "Failed_error_BillOutTransferV1",
			wantErr: nil,
			transferStatusMockExpect: transferStatusMockExpect{
				wantRes: map[model.TransferStatusCode]uint64{
					model.StatusAuthorized: 3,
					model.StatusSuccess:    4,
				},
				wantErr: nil,
			},
			transferMockExpect: transferMockExpect{
				createdDateFrom: nowFn.Add(-30 * time.Hour * 24),
				createdDateTo:   nowFn.Add(-7 * time.Hour * 24),
				statusID:        3,
				wantRes: []model.Transfer{
					{
						AccountID:  1,
						ID:         1,
						FinishedAt: nil,
						TransferType: model.TransferType{
							Code: model.TypeOut,
						},
						Amount:     100,
						MerchantID: 1,
						ProjectID:  1,
					},
				},

				wantErr: nil,
				expect:  true,
			},
			accountInfoMockExpect: accountInfoMockExpect{
				id: 1,
				wanRes: &model.Account{
					ID:              1,
					Number:          "some",
					CurrencyCode:    "some",
					BankCode:        "some",
					AccountTypeID:   1,
					EncryptedConfig: defaultEncryptedConfig,
					BankID:          1,
				},
				wantErr: nil,
				expect:  true,
			},
			multiAccountingClientMockExpect: multiAccountingClientMockExpect{
				req: &gorpc.GetTransferDetailsRequest{
					TransferId: testsdk.Ptr(uint64(1)),
					Account: &gorpc.AccountTerminal{
						BankCode: testsdk.Ptr("some"),
						Config:   defaultCfg,
					},
					ExternalReferenceId: testsdk.Ptr("1"),
				},
				wantRes: &gorpc.GetTransferDetailsResponse{
					TransferId:       testsdk.Ptr(uint64(1)),
					TransferStatusId: testsdk.Ptr(uint64(4)),
				},
				wantErr: nil,
				expect:  true,
			},
			billingClientMockExpect: billingClientMockExpect{
				in: in{
					req: &gorpc.BillInTransferRequestV1{
						ProjectId:     testsdk.Ptr(uint64(1)),
						AccountNumber: testsdk.Ptr("some"),
						Amount:        testsdk.Ptr(float64(100)),
						MerchantId:    testsdk.Ptr(uint64(1)),
						TransferId:    testsdk.Ptr(uint64(1)),
					},
					wantRes: &emptypb.Empty{},
					wantErr: nil,
					expect:  false,
				},
				out: out{
					req: &gorpc.BillOutTransferRequestV1{
						ProjectId:     testsdk.Ptr(uint64(1)),
						AccountNumber: testsdk.Ptr("some"),
						Amount:        testsdk.Ptr(float64(100)),
						MerchantId:    testsdk.Ptr(uint64(1)),
						TransferId:    testsdk.Ptr(uint64(1)),
					},
					wantRes: &emptypb.Empty{},
					wantErr: errors.New("some error"),
					expect:  true,
				},
			},
			transferStatusMockExpectSecond: transferStatusMockExpectSecond{
				statusID: 4,
				wantRes: &model.TransferStatus{
					ID:   4,
					Code: "success",
				},
				expect: true,
			},
			transferManagerMockExpectFirst: transferManagerMockExpect{
				transferID: 1,
				statusID:   4,
				finishedAt: &nowFn,
				wantErr:    nil,
				expect:     true,
			},
			transferManagerMockExpectSecond: transferManagerMockExpect{
				transferID: 1,
				statusID:   3,
				finishedAt: nil,
				wantErr:    nil,
				expect:     true,
			},
			appConfig: map[string]any{
				"SYMMETRIC_KEY": normalKey,
			},
		},
		{
			name:    "Failed_error_secondUpdateStatus",
			wantErr: goerr.ErrDbUnexpected,
			transferStatusMockExpect: transferStatusMockExpect{
				wantRes: map[model.TransferStatusCode]uint64{
					model.StatusAuthorized: 3,
					model.StatusSuccess:    4,
				},
				wantErr: nil,
			},
			transferMockExpect: transferMockExpect{
				createdDateFrom: nowFn.Add(-30 * time.Hour * 24),
				createdDateTo:   nowFn.Add(-7 * time.Hour * 24),
				statusID:        3,
				wantRes: []model.Transfer{
					{
						AccountID:  1,
						ID:         1,
						FinishedAt: nil,
						TransferType: model.TransferType{
							Code: model.TypeOut,
						},
						Amount:     100,
						MerchantID: 1,
						ProjectID:  1,
					},
				},

				wantErr: nil,
				expect:  true,
			},
			accountInfoMockExpect: accountInfoMockExpect{
				id: 1,
				wanRes: &model.Account{
					ID:              1,
					Number:          "some",
					CurrencyCode:    "some",
					BankCode:        "some",
					AccountTypeID:   1,
					EncryptedConfig: defaultEncryptedConfig,
					BankID:          1,
				},
				wantErr: nil,
				expect:  true,
			},
			multiAccountingClientMockExpect: multiAccountingClientMockExpect{
				req: &gorpc.GetTransferDetailsRequest{
					TransferId: testsdk.Ptr(uint64(1)),
					Account: &gorpc.AccountTerminal{
						BankCode: testsdk.Ptr("some"),
						Config:   defaultCfg,
					},
					ExternalReferenceId: testsdk.Ptr("1"),
				},
				wantRes: &gorpc.GetTransferDetailsResponse{
					TransferId:       testsdk.Ptr(uint64(1)),
					TransferStatusId: testsdk.Ptr(uint64(4)),
				},
				wantErr: nil,
				expect:  true,
			},
			billingClientMockExpect: billingClientMockExpect{
				in: in{
					req: &gorpc.BillInTransferRequestV1{
						ProjectId:     testsdk.Ptr(uint64(1)),
						AccountNumber: testsdk.Ptr("some"),
						Amount:        testsdk.Ptr(float64(100)),
						MerchantId:    testsdk.Ptr(uint64(1)),
						TransferId:    testsdk.Ptr(uint64(1)),
					},
					wantRes: &emptypb.Empty{},
					wantErr: nil,
					expect:  false,
				},
				out: out{
					req: &gorpc.BillOutTransferRequestV1{
						ProjectId:     testsdk.Ptr(uint64(1)),
						AccountNumber: testsdk.Ptr("some"),
						Amount:        testsdk.Ptr(float64(100)),
						MerchantId:    testsdk.Ptr(uint64(1)),
						TransferId:    testsdk.Ptr(uint64(1)),
					},
					wantRes: &emptypb.Empty{},
					wantErr: errors.New("some error"),
					expect:  true,
				},
			},
			transferStatusMockExpectSecond: transferStatusMockExpectSecond{
				statusID: 4,
				wantRes: &model.TransferStatus{
					ID:   4,
					Code: "success",
				},
				expect: true,
			},
			transferManagerMockExpectFirst: transferManagerMockExpect{
				transferID: 1,
				statusID:   4,
				finishedAt: &nowFn,
				wantErr:    nil,
				expect:     true,
			},
			transferManagerMockExpectSecond: transferManagerMockExpect{
				transferID: 1,
				statusID:   3,
				finishedAt: nil,
				wantErr:    goerr.ErrDbUnexpected,
				expect:     true,
			},
			appConfig: map[string]any{
				"SYMMETRIC_KEY": normalKey,
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctrl := gomock.NewController(t)
			defer ctrl.Finish()
			t.Helper()

			c := context.Background()
			ctx, cancel := context.WithCancel(c)

			e2e.InitAppE2E(t, ctx, cancel, nil, nil, tt.appConfig)

			transferStatusMock := databasemocks.NewMockTransferStatuser(ctrl)
			transferMock := databasemocks.NewMockTransferer(ctrl)
			transferManageMock := databasemocks.NewMockTransferManager(ctrl)
			accountInfoMock := databasemocks.NewMockAccountInformer(ctrl)
			billingClientMock := grpcmock.NewMockBillingClient(ctrl)
			multiAccountingClientMock := grpcmock.NewMockMultiaccountingClient(ctrl)

			service := OldTransfersFinalizerService{
				transferStatusDB:      transferStatusMock,
				transferDB:            transferMock,
				transferManageDB:      transferManageMock,
				multiAccountingClient: multiAccountingClientMock,
				billingClient:         billingClientMock,
				accountInfoDB:         accountInfoMock,
				nowFn: func() time.Time {
					return nowFn
				},
				workerCreatedAtToHours:   "-168h",
				workerCreatedAtFromHours: "-720h",
			}

			transferStatusMock.EXPECT().AsCodeMap(
				gomock.Any(),
			).Return(
				tt.transferStatusMockExpect.wantRes,
				tt.transferStatusMockExpect.wantErr,
			)

			if tt.transferMockExpect.expect {
				transferMock.EXPECT().GetByPeriod(
					gomock.Any(),
					tt.transferMockExpect.createdDateFrom,
					tt.transferMockExpect.createdDateTo,
					tt.transferMockExpect.statusID,
				).Return(
					tt.transferMockExpect.wantRes,
					tt.transferMockExpect.wantErr,
				)
			}

			if tt.accountInfoMockExpect.expect {
				accountInfoMock.EXPECT().GetById(
					gomock.Any(),
					tt.accountInfoMockExpect.id,
				).Return(
					tt.accountInfoMockExpect.wanRes,
					tt.accountInfoMockExpect.wantErr,
				)
			}

			if tt.multiAccountingClientMockExpect.expect {
				multiAccountingClientMock.EXPECT().GetTransferDetails(
					gomock.Any(),
					tt.multiAccountingClientMockExpect.req,
				).Return(
					tt.multiAccountingClientMockExpect.wantRes,
					tt.multiAccountingClientMockExpect.wantErr,
				)
			}

			if tt.transferStatusMockExpectSecond.expect {
				transferStatusMock.EXPECT().GetByID(
					gomock.Any(),
					tt.transferStatusMockExpectSecond.statusID,
				).Return(
					tt.transferStatusMockExpectSecond.wantRes,
					tt.transferManagerMockExpectFirst.wantErr,
				)
			}

			if tt.transferManagerMockExpectFirst.expect {
				transferManageMock.EXPECT().UpdateStatus(
					gomock.Any(),
					tt.transferManagerMockExpectFirst.transferID,
					tt.transferManagerMockExpectFirst.statusID,
					tt.transferManagerMockExpectFirst.finishedAt,
				).Return(
					tt.transferManagerMockExpectFirst.wantErr,
				)
			}

			if tt.billingClientMockExpect.in.expect {
				billingClientMock.EXPECT().BillInTransferV1(
					gomock.Any(),
					tt.billingClientMockExpect.in.req,
				).Return(
					tt.billingClientMockExpect.in.wantRes,
					tt.billingClientMockExpect.in.wantErr,
				)
			}

			if tt.billingClientMockExpect.out.expect {
				billingClientMock.EXPECT().BillOutTransferV1(
					gomock.Any(),
					tt.billingClientMockExpect.out.req,
				).Return(
					tt.billingClientMockExpect.out.wantRes,
					tt.billingClientMockExpect.out.wantErr,
				)
			}

			if tt.transferManagerMockExpectSecond.expect {
				transferManageMock.EXPECT().UpdateStatus(
					gomock.Any(),
					tt.transferManagerMockExpectSecond.transferID,
					tt.transferManagerMockExpectSecond.statusID,
					tt.transferManagerMockExpectSecond.finishedAt,
				).Return(
					tt.transferManagerMockExpectSecond.wantErr,
				)
			}

			err := service.FinalizeOldTransfers(
				testsdk.TimeoutContext(t),
			)

			require.Equal(t, tt.wantErr, err)
		})
	}
}
