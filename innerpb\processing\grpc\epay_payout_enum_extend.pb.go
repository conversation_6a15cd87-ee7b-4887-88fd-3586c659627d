// Code generated by protoc-gen-go-mvp. DO NOT EDIT.
// versions:
// - protoc-gen-go-mvp v2.0.1
// created by mvp authors

package grpc

import (
	math "math"
	strings "strings"
)

func (val EnumEpayPayOutCode) Synonym() EnumEpayPayOutCode {
	if _, ok := EnumEpayPayOutCode_name[int32(val)]; ok {
		return val
	}

	return EnumEpayPayOutCode(math.MinInt32)
}

func (val EnumEpayPayOutCode) Int() int {
	return int(val.Synonym())
}

func (val EnumEpayPayOutCode) IntPtr() *int {
	result := int(val.Synonym())
	return &result
}

func (val EnumEpayPayOutCode) Int32() int32 {
	return int32(val.Synonym())
}

func (val EnumEpayPayOutCode) Int32Ptr() *int32 {
	result := int32(val.Synonym())
	return &result
}

func (val EnumEpayPayOutCode) Int64() int64 {
	return int64(val.Synonym())
}

func (val EnumEpayPayOutCode) Int64Ptr() *int64 {
	result := int64(val.Synonym())
	return &result
}

func (val EnumEpayPayOutCode) Uint() uint {
	return uint(val.Synonym())
}

func (val EnumEpayPayOutCode) UintPtr() *uint {
	result := uint(val.Synonym())
	return &result
}

func (val EnumEpayPayOutCode) Uint32() uint32 {
	return uint32(val.Synonym())
}

func (val EnumEpayPayOutCode) Uint32Ptr() *uint32 {
	result := uint32(val.Synonym())
	return &result
}

func (val EnumEpayPayOutCode) Uint64() uint64 {
	return uint64(val.Synonym())
}

func (val EnumEpayPayOutCode) Uint64Ptr() *uint64 {
	result := uint64(val.Synonym())
	return &result
}

func (val EnumEpayPayOutCode) IsKnown() bool {
	return val.Synonym() != EnumEpayPayOutCode(math.MinInt32)
}

func ConvertIntToEnumEpayPayOutCode(in int) EnumEpayPayOutCode {
	return EnumEpayPayOutCode(in).Synonym()
}

func ConvertUintToEnumEpayPayOutCode(in uint) EnumEpayPayOutCode {
	return EnumEpayPayOutCode(in).Synonym()
}

func ConvertInt32ToEnumEpayPayOutCode(in int32) EnumEpayPayOutCode {
	return EnumEpayPayOutCode(in).Synonym()
}

func ConvertUint32ToEnumEpayPayOutCode(in uint32) EnumEpayPayOutCode {
	return EnumEpayPayOutCode(in).Synonym()
}

func ConvertInt64ToEnumEpayPayOutCode(in int64) EnumEpayPayOutCode {
	return EnumEpayPayOutCode(in).Synonym()
}

func ConvertUint64ToEnumEpayPayOutCode(in uint64) EnumEpayPayOutCode {
	return EnumEpayPayOutCode(in).Synonym()
}

var EnumEpayPayOutCode_Lower_value = map[string]EnumEpayPayOutCode{
	"empty_response_code":              0,
	"clientauthenticationcode_33":      -33,
	"systemerrorpleasetrycode_50":      -50,
	"errorincvc2orcvc2descode_18":      -18,
	"invalidretrievalrefecode_15":      -15,
	"theoperationfailedplcode_454":     454,
	"threedsecurecheckfailedcode_455":  455,
	"accessdeniedcode_456":             456,
	"errorincardexpiratiocode_457":     457,
	"serverisnotrespondincode_458":     458,
	"serverisnotrespondincode_459":     459,
	"noorinvalidresponcercode_460":     460,
	"badcgirequestcode_461":            461,
	"callyourbankcode_462":             462,
	"callyourbankcode_463":             463,
	"invalidmerchantcode_464":          464,
	"yourcardisrestrictedcode_465":     465,
	"notpermittedtoclientcode_466":     466,
	"yourcardisdisabledcode_467":       467,
	"additionalidentificacode_468":     468,
	"invalidtransactioncode_469":       469,
	"invalidamountcode_470":            470,
	"nosuchcardcode_471":               471,
	"nosuchcardcode_472":               472,
	"authenticationfailedcode_473":     473,
	"invalidresponsecode_475":          475,
	"noactiontakencode_476":            476,
	"formaterrorcode_477":              477,
	"expiredcardcode_478":              478,
	"restrictedcardcode_479":           479,
	"callyourbankcode_480":             480,
	"lostcardcode_481":                 481,
	"lostcardcode_482":                 482,
	"stolencardcode_483":               483,
	"notsufficientfundscode_484":       484,
	"expiredcardcode_485":              485,
	"notpermittedtoclientcode_486":     486,
	"notpermittedtomerchacode_487":     487,
	"exceedsamountlimitcode_488":       488,
	"restrictedcardcode_489":           489,
	"invalidcontractcardcode_490":      490,
	"exceedsfrequencylimicode_491":     491,
	"pintriesexceededcode_492":         492,
	"timeoutatissuersystecode_493":     493,
	"issuerunavailablecode_494":        494,
	"cannotbecompletedviocode_495":     495,
	"threedsecuresecurecodeabcode_496": 496,
	"serverisnotrespondincode_497":     497,
	"errorincurrencyfieldcode_499":     499,
	"threedsecurecheckfailedcode_500":  500,
	"cardcheckfailedcode_501":          501,
	"threedsecuresecurecodeabcode_502": 502,
	"threedsecuresecurecodeabcode_503": 503,
	"transactiondeclinedccode_523":     523,
	"yourcardisrestrictedcode_524":     524,
	"yourcardisrestrictedcode_525":     525,
	"systemerrorpleasetrycode_526":     526,
	"transactiondeclinedccode_527":     527,
	"thedailylimitofincomcode_528":     528,
	"transactiondeclinedccode_531":     531,
	"unabletoverifypincalcode_532":     532,
	"authenticationfailedcode_19":      -19,
	"errorincardexpiratiocode_9":       -9,
	"serverisnotrespondincode_8":       -8,
	"serverisnotrespondincode_4":       -4,
	"errorbindactioncreatcode_1":       1,
	"errorcreatingrecordicode_2":       2,
	"errorbindactionupdatcode_3":       3,
	"errorwhileupdatingrecode_4":       4,
	"errorwhiledeletingthcode_5":       5,
	"errorgetbynameactioncode_6":       6,
	"errorgetbyidactiongecode_7":       7,
	"errorbindactioncreatcode_8":       8,
	"errorcreatingrecordicode_9":       9,
	"redisactioncreateerrcode_10":      10,
	"errorbindactionupdatcode_11":      11,
	"errorwhileupdatingrecode_12":      12,
	"redisactionupdateerrcode_13":      13,
	"errorwhiledeletingthcode_14":      14,
	"redisactiondeleteerrcode_15":      15,
	"errorgetbycodeinpostcode_16":      16,
	"codeactiongeterrorbycode_17":      17,
	"idapplicationidapplicode_18":      18,
	"codecode_19":                      19,
	"idapplicationidapplicode_20":      20,
	"idapplicationapplicacode_21":      21,
	"code_22":                          22,
	"successfullycode_23":              23,
	"test111code_24":                   24,
	"invoiceidcode_25":                 25,
	"bindjsonbindjsoncode_27":          27,
	"transactionalreadyprcode_28":      28,
	"transactionalreadyprcode_29":      29,
	"errorgetshopsbymerchcode_54":      54,
	"idcode_55":                        55,
	"errorgetshopbyidcode_56":          56,
	"errorbindcode_57":                 57,
	"errorbindcode_58":                 58,
	"errorcreateshopcode_59":           59,
	"errorgetmerchantbyidcode_60":      60,
	"errorgetstatusbynamecode_61":      61,
	"errorcreatedefaultcocode_64":      64,
	"errorcreatemerchantcode_65":       65,
	"errorverificationsercode_71":      71,
	"errorcheckmerchantcode_72":        72,
	"errorsendverificatiocode_73":      73,
	"errorverificationsercode_74":      74,
	"errorfindmerchantbyecode_75":      75,
	"errorupdatemerchantcode_76":       76,
	"errorregistrationusecode_77":      77,
	"rrncode_83":                       83,
	"errorgetcardtypebyrrcode_84":      84,
	"errorchecktransactiocode_85":      85,
	"unauthorizedcode_86":              86,
	"tokenisnotvalidcode_87":           87,
	"errorchecktokencode_88":           88,
	"errornoscopescode_89":             89,
	"errornoscopescode_90":             90,
	"errorchecktokencode_91":           91,
	"cardnumber14code_92":              92,
	"errorgetcardcorporatcode_94":      94,
	"errorgetcountrybybincode_95":      95,
	"invaliddatacode_96":               96,
	"errorchecktokencode_97":           97,
	"errornoscopescode_98":             98,
	"contractcode_99":                  99,
	"invaliddateformatplecode_111":     111,
	"contractcode_112":                 112,
	"extcsvxlsxcode_113":               113,
	"errorgetstatementbydcode_114":     114,
	"errorgetstatementbyocode_115":     115,
	"errorgetstatementaircode_116":     116,
	"errorgetstatementbytcode_117":     117,
	"errorchecktokencode_121":          121,
	"code_122":                         122,
	"errorbindcode_123":                123,
	"code_124":                         124,
	"code_125":                         125,
	"code_126":                         126,
	"code_127":                         127,
	"erroremptymerchantidcode_128":     128,
	"errorunknownmerchantcode_129":     129,
	"servererrorcode_130":              130,
	"errorchecktokencode_131":          131,
	"errorinvalidscopecode_132":        132,
	"errorbindcode_133":                133,
	"errorrequestpostformcode_134":     134,
	"errorbodyisemptycode_135":         135,
	"errorrequestisnotsuccode_136":     136,
	"unkwonerrorcode_137":              137,
	"secretincorrecterrorcode_138":     138,
	"code_139":                         139,
	"secretincorrectcode_140":          140,
	"errorgetclientfromrecode_141":     141,
	"errorparseclientcode_142":         142,
	"checkscopescode_143":              143,
	"useridpasswordcode_144":           144,
	"userstatuschangepasscode_145":     145,
	"usernotfoundcode_146":             146,
	"statuscode_147":                   147,
	"code_148":                         148,
	"userstatusinvalidcode_149":        149,
	"statusisnotdefinedcode_150":       150,
	"passwordincorrectcode_151":        151,
	"granttypecode_152":                152,
	"refreshtokencode_153":             153,
	"code_154":                         154,
	"scopecode_155":                    155,
	"refreshtokencode_156":             156,
	"generateaccesserrorcode_157":      157,
	"generateaccesserrorcode_158":      158,
	"extensionfieldadderrcode_159":     159,
	"extensionfieldadderrcode_160":     160,
	"code_161":                         161,
	"code_162":                         162,
	"getuserbyloginerrorccode_163":     163,
	"code_164":                         164,
	"getstatusbynameerrorcode_165":     165,
	"hashpassworderrorcode_166":        166,
	"saveusererrorcode_167":            167,
	"updateusererrorcode_169":          169,
	"updateusererrorcode_170":          170,
	"getuserbyloginerrorccode_171":     171,
	"code_172":                         172,
	"code_173":                         173,
	"actionservicegetdebicode_178":     178,
	"actionservicegettrancode_179":     179,
	"referenceepaycode_180":            180,
	"rediscode_181":                    181,
	"code_182":                         182,
	"code_185":                         185,
	"errorunmarshalcode_186":           186,
	"startdateandenddatescode_193":     193,
	"epaycode_194":                     194,
	"noauthorizecode_197":              197,
	"badtokencode_198":                 198,
	"parseerrorcode_199":               199,
	"paymenterrorcode_200":             200,
	"code_216":                         216,
	"errorbindjsoncannotucode_224":     224,
	"likecode_225":                     225,
	"code_226":                         226,
	"betweencode_227":                  227,
	"code_228":                         228,
	"code_229":                         229,
	"code_230":                         230,
	"code_231":                         231,
	"code_232":                         232,
	"errorchecktokencode_233":          233,
	"errornoscopescode_234":            234,
	"fromdateyyyymmddcode_235":         235,
	"todateyyyymmddcode_236":           236,
	"errorchecktokencode_237":          237,
	"errornoscopescode_238":            238,
	"cannotunmarshalintoscode_239":     239,
	"code_240":                         240,
	"code_241":                         241,
	"code_242":                         242,
	"idcode_243":                       243,
	"merchantidcode_244":               244,
	"startdateandenddatescode_245":     245,
	"code_246":                         246,
	"notemailcode_247":                 247,
	"jsoncode_248":                     248,
	"code_249":                         249,
	"msgvaluecode_250":                 250,
	"code_251":                         251,
	"code_252":                         252,
	"code_263":                         263,
	"code_264":                         264,
	"code_265":                         265,
	"secretincorrecterrorcode_266":     266,
	"scopenotinchangepasscode_267":     267,
	"unsupportedscopecode_268":         268,
	"blockcode_269":                    269,
	"blockcode_270":                    270,
	"usernotsavecode_271":              271,
	"usernotsavecode_272":              272,
	"failedtogetstatuscode_273":        273,
	"useralreadyexistcode_274":         274,
	"useralreadyexistcode_275":         275,
	"incorrectdatacode_276":            276,
	"incorrectdatacode_277":            277,
	"notemailcode_278":                 278,
	"notemailcode_279":                 279,
	"code_280":                         280,
	"transactioncreateerrcode_281":     281,
	"code_282":                         282,
	"code_283":                         283,
	"testmecode_285":                   285,
	"statementcode_286":                286,
	"errorchecktokencode_287":          287,
	"errornoscopescode_288":            288,
	"tofromyyyymmddcode_289":           289,
	"code_290":                         290,
	"fromdateandtodateshocode_291":     291,
	"errorwhilecreatingrecode_292":     292,
	"code_293":                         293,
	"requiredclientcredencode_294":     294,
	"ididcode_295":                     295,
	"errorupdatestaffcode_296":         296,
	"staffcode_297":                    297,
	"code_298":                         298,
	"staffcode_299":                    299,
	"binderrorcode_300":                300,
	"updatestaffcode_301":              301,
	"multipartformcode_302":            302,
	"expireatyyyymmddcode_303":         303,
	"expireatyyyymmddcode_304":         304,
	"expireatcode_305":                 305,
	"expireatcode_306":                 306,
	"code_307":                         307,
	"code_308":                         308,
	"code_309":                         309,
	"commiterrorcode_310":              310,
	"bindjsonerrorcode_311":            311,
	"code_312":                         312,
	"searchtransactionerrcode_318":     318,
	"jsoncode_319":                     319,
	"code_320":                         320,
	"code_321":                         321,
	"code_322":                         322,
	"fromdateyyyymmddcode_323":         323,
	"todateyyyymmddcode_324":           324,
	"epaycode_325":                     325,
	"errorgetmerchantinfocode_326":     326,
	"code_327":                         327,
	"code_328":                         328,
	"invoiceidcode_329":                329,
	"terminalcode_330":                 330,
	"currencycode_331":                 331,
	"amountcode_332":                   332,
	"code_333":                         333,
	"shopidcode_334":                   334,
	"merchantidcode_335":               335,
	"shopidcode_336":                   336,
	"errorgetmerchantinfocode_337":     337,
	"code_338":                         338,
	"code_339":                         339,
	"code_340":                         340,
	"contractcode_342":                 342,
	"getshopbycontractcode_343":        343,
	"code_344":                         344,
	"marshalerrorcode_345":             345,
	"code_346":                         346,
	"code_347":                         347,
	"code_348":                         348,
	"code_349":                         349,
	"code_350":                         350,
	"code_351":                         351,
	"code_352":                         352,
	"topcode_353":                      353,
	"code_354":                         354,
	"emailcode_355":                    355,
	"code_356":                         356,
	"code_357":                         357,
	"code_358":                         358,
	"code_359":                         359,
	"code_360":                         360,
	"code_361":                         361,
	"code_362":                         362,
	"parseerrorcode_363":               363,
	"parseerrorcode_364":               364,
	"code_365":                         365,
	"parseerrorcode_366":               366,
	"parseerrorcode_367":               367,
	"code_368":                         368,
	"parseerrorcode_369":               369,
	"parseerrorcode_370":               370,
	"code_371":                         371,
	"code_372":                         372,
	"noemailcode_373":                  373,
	"code_374":                         374,
	"code_375":                         375,
	"code_376":                         376,
	"incorrectformatdatecode_377":      377,
	"getfilefiledoesnotexcode_378":     378,
	"code_379":                         379,
	"code_380":                         380,
	"code_381":                         381,
	"code_382":                         382,
	"code_383":                         383,
	"egatewaycode_384":                 384,
	"egatewaycode_385":                 385,
	"egatewaycode_386":                 386,
	"egatewaycode_387":                 387,
	"egatewaycode_388":                 388,
	"userdeletecode_389":               389,
	"parseerrorcode_390":               390,
	"callbacksendmessageecode_391":     391,
	"code_392":                         392,
	"code_393":                         393,
	"code_394":                         394,
	"code_395":                         395,
	"code_396":                         396,
	"mvisacode_397":                    397,
	"threedsecurecode_398":             398,
	"mvisaverificationcode_399":        399,
	"code_400":                         400,
	"code_401":                         401,
	"code_402":                         402,
	"carddataisnotrequirecode_403":     403,
	"code_404":                         404,
	"code_405":                         405,
	"code_406":                         406,
	"code_407":                         407,
	"code_408":                         408,
	"code_409":                         409,
	"mvisacode_410":                    410,
	"noauthorizecode_411":              411,
	"badtokencode_412":                 412,
	"code_413":                         413,
	"timeouttransactioncode_504":       504,
	"exceededattemptscode_505":         505,
	"code_506":                         506,
	"code_507":                         507,
	"code_508":                         508,
	"code_509":                         509,
	"rabbitcode_510":                   510,
	"code_512":                         512,
	"postlinkcode_513":                 513,
	"code_514":                         514,
	"code_515":                         515,
	"code_516":                         516,
	"therewasnoattempttopcode_517":     517,
	"ipcode_518":                       518,
	"invoiceidcode_520":                520,
	"terminalidcode_511":               511,
	"bincode_522":                      522,
	"egwcode_546":                      546,
	"egwhttpcode200code_547":           547,
	"code_559":                         559,
	"egwcode_560":                      560,
	"egwhttpcode200code_561":           561,
	"mdcode_529":                       529,
	"egwcode_530":                      530,
	"code_570":                         570,
	"egwcode_571":                      571,
	"egwhttpcode200code_533":           533,
	"code_534":                         534,
	"code_535":                         535,
	"merchantcode_536":                 536,
	"merchantisactivefalscode_537":     537,
	"code_538":                         538,
	"merchantcode_539":                 539,
	"scopenotfoundchecksccode_540":     540,
	"errorparameternamecode_541":       541,
	"unauthorizedchecktokcode_542":     542,
	"errorinvalidscopecode_543":        543,
	"errorbindcode_544":                544,
	"unkwonerrorcode_548":              548,
	"transactionalreadycacode_549":     549,
	"requesttoxlsserverfacode_550":     550,
	"requesttoxlsserverrecode_551":     551,
	"invalidbonusamountcode_552":       552,
	"invalidinputdatacode_553":         553,
	"terminalconfiguratiocode_554":     554,
	"unabletogetgetrequescode_555":     555,
	"requesttoapixlsfailecode_556":     556,
	"requesttoapixlsreturcode_557":     557,
	"gobonusconvertiontofcode_558":     558,
	"code_563":                         563,
	"terminalidcode_564":               564,
	"requesttogetcardinfocode_565":     565,
	"requesttogetcardinfocode_566":     566,
	"cardnotfoundcode_567":             567,
	"cardownernotfoundcode_568":        568,
	"code_569":                         569,
	"code_572":                         572,
	"requesttoapixlsfailecode_573":     573,
	"requesttoapixlsreturcode_574":     574,
	"unauthorizedchecktokcode_576":     576,
	"cannotcancelxlstranscode_578":     578,
	"cannotsendtransactiocode_579":     579,
	"cannotsendcancelxlstcode_580":     580,
	"cannotcreaterabbitincode_581":     581,
	"changepaymentcode_582":            582,
	"code_583":                         583,
	"xlscode_584":                      584,
	"gocode_585":                       585,
	"code_586":                         586,
	"code_587":                         587,
	"code_588":                         588,
	"code_589":                         589,
	"code_590":                         590,
	"code_591":                         591,
	"code_592":                         592,
	"code_593":                         593,
	"code_594":                         594,
	"code_595":                         595,
	"epay1documentcode_596":            596,
	"crtificateidcode_597":             597,
	"code_598":                         598,
	"code_599":                         599,
	"code_600":                         600,
	"code_601":                         601,
	"code_602":                         602,
	"code_603":                         603,
	"code_604":                         604,
	"code_605":                         605,
	"code_606":                         606,
	"code_607":                         607,
	"code_608":                         608,
	"code_609":                         609,
	"code_610":                         610,
	"code_611":                         611,
	"code_612":                         612,
	"code_613":                         613,
	"code_614":                         614,
	"code_615":                         615,
	"code_616":                         616,
	"invoiceidcode_617":                617,
	"terminalcode_618":                 618,
	"terminalcode_619":                 619,
	"currencycode_620":                 620,
	"amountcode_621":                   621,
	"amountcode_622":                   622,
	"amountcode_623":                   623,
	"code_624":                         624,
	"code_625":                         625,
	"code_626":                         626,
	"code_627":                         627,
	"code_628":                         628,
	"code_629":                         629,
	"code_630":                         630,
	"code_631":                         631,
	"code_632":                         632,
	"code_633":                         633,
	"code_634":                         634,
	"egatewaycode_635":                 635,
	"egatewaycode_636":                 636,
	"egatewaycode_637":                 637,
	"code_638":                         638,
	"code_639":                         639,
	"code_640":                         640,
	"code_641":                         641,
	"code_642":                         642,
	"callbacksendmessageecode_643":     643,
	"code_644":                         644,
	"code_645":                         645,
	"code_646":                         646,
	"code_647":                         647,
	"code_648":                         648,
	"code_649":                         649,
	"code_650":                         650,
	"code_651":                         651,
	"code_652":                         652,
	"code_653":                         653,
	"code_654":                         654,
	"code_655":                         655,
	"code_656":                         656,
	"code_657":                         657,
	"code_658":                         658,
	"code_659":                         659,
	"code_660":                         660,
	"code_661":                         661,
	"code_662":                         662,
	"code_663":                         663,
	"code_664":                         664,
	"code_665":                         665,
	"code_666":                         666,
	"code_667":                         667,
	"code_668":                         668,
	"code_669":                         669,
	"code_670":                         670,
	"code_671":                         671,
	"code_672":                         672,
	"code_673":                         673,
	"egwhttpcode200code_674":           674,
	"code_675":                         675,
	"code_676":                         676,
	"code_677":                         677,
	"code_678":                         678,
	"code_679":                         679,
	"requesttogetcardinfocode_680":     680,
	"requesttogetcardinfocode_681":     681,
	"xlscode_683":                      683,
	"xlscode_684":                      684,
	"code_685":                         685,
	"terminalconfiguratiocode_686":     686,
	"requesttoxlsserverfacode_687":     687,
	"requesttoxlsserverfacode_688":     688,
	"code_689":                         689,
	"code_690":                         690,
	"code_691":                         691,
	"jsoncode_692":                     692,
	"code_693":                         693,
	"code_694":                         694,
	"statementcode_695":                695,
	"statementcode_696":                696,
	"statementcode_697":                697,
	"statementcode_698":                698,
	"statementcode_699":                699,
	"tofromyyyymmddcode_700":           700,
	"tofromyyyymmddcode_701":           701,
	"tofromyyyymmddcode_702":           702,
	"code_703":                         703,
	"code_704":                         704,
	"code_705":                         705,
	"code_706":                         706,
	"code_707":                         707,
	"staffcode_708":                    708,
	"staffcode_709":                    709,
	"staffcode_710":                    710,
	"code_711":                         711,
	"code_712":                         712,
	"code_713":                         713,
	"code_714":                         714,
	"code_715":                         715,
	"code_716":                         716,
	"code_717":                         717,
	"code_718":                         718,
	"code_719":                         719,
	"errorinservicingtheccode_720":     720,
	"errorinservicingtheccode_721":     721,
	"errorinservicingtheccode_722":     722,
	"errorinservicingtheccode_723":     723,
	"errorinservicingtheccode_724":     724,
	"errorinservicingtheccode_725":     725,
	"errorinservicingtheccode_726":     726,
	"errorinservicingtheccode_727":     727,
	"noauthorizecode_728":              728,
	"badtokencode_729":                 729,
	"code_730":                         730,
	"invalidrequestinputcode_731":      731,
	"code_732":                         732,
	"code_733":                         733,
	"code_734":                         734,
	"code_735":                         735,
	"code_736":                         736,
	"idcode_737":                       737,
	"code_738":                         738,
	"code_739":                         739,
	"code_740":                         740,
	"code_741":                         741,
	"code_742":                         742,
	"code_743":                         743,
	"code_744":                         744,
	"code_745":                         745,
	"code_746":                         746,
	"code_747":                         747,
	"code_748":                         748,
	"code_749":                         749,
	"code_750":                         750,
	"code_751":                         751,
	"code_752":                         752,
	"code_753":                         753,
	"apimakepaymentscorercode_754":     754,
	"code_756":                         756,
	"code_757":                         757,
	"merchantcode_758":                 758,
	"code_759":                         759,
	"code_760":                         760,
	"code_761":                         761,
	"code_762":                         762,
	"code_765":                         765,
	"egatewaycode_766":                 766,
	"code_767":                         767,
	"code_768":                         768,
	"code_769":                         769,
	"code_770":                         770,
	"code_771":                         771,
	"code_772":                         772,
	"code_773":                         773,
	"code_774":                         774,
	"code_775":                         775,
	"invoiceidcode_776":                776,
	"terminalcode_777":                 777,
	"currencycode_778":                 778,
	"amountcode_779":                   779,
	"amountcode_780":                   780,
	"corepaymentcode_781":              781,
	"code_782":                         782,
	"code_783":                         783,
	"code_784":                         784,
	"idcode_785":                       785,
	"terminalidcode_786":               786,
	"code_787":                         787,
	"code_789":                         789,
	"code_790":                         790,
	"code_791":                         791,
	"code_792":                         792,
	"code_793":                         793,
	"code_795":                         795,
	"corerecurrentcode_796":            796,
	"code_797":                         797,
	"code_798":                         798,
	"code_799":                         799,
	"code_800":                         800,
	"code_801":                         801,
	"code_802":                         802,
	"code_803":                         803,
	"code_804":                         804,
	"code_805":                         805,
	"publicidcode_806":                 806,
	"code_807":                         807,
	"code_808":                         808,
	"code_809":                         809,
	"code_810":                         810,
	"code_811":                         811,
	"code_812":                         812,
	"code_813":                         813,
	"code_814":                         814,
	"code_815":                         815,
	"code_816":                         816,
	"code_817":                         817,
	"code_818":                         818,
	"code_819":                         819,
	"code_820":                         820,
	"code_821":                         821,
	"code_822":                         822,
	"code_823":                         823,
	"code_824":                         824,
	"code_825":                         825,
	"code_826":                         826,
	"code_827":                         827,
	"code_828":                         828,
	"code_829":                         829,
	"code_830":                         830,
	"code_831":                         831,
	"code_832":                         832,
	"code_833":                         833,
	"code_834":                         834,
	"code_835":                         835,
	"code_836":                         836,
	"code_837":                         837,
	"code_838":                         838,
	"code_839":                         839,
	"code_840":                         840,
	"code_841":                         841,
	"code_842":                         842,
	"code_843":                         843,
	"code_844":                         844,
	"code_845":                         845,
	"publicidcode_846":                 846,
	"merchantapicode_847":              847,
	"code_848":                         848,
	"code_849":                         849,
	"code_850":                         850,
	"staffinviterequestcode_851":       851,
	"publicidcode_852":                 852,
	"code_853":                         853,
	"code_854":                         854,
	"publicidcode_855":                 855,
	"publicidcode_856":                 856,
	"code_857":                         857,
	"publicidcode_858":                 858,
	"code_859":                         859,
	"code_860":                         860,
	"code_861":                         861,
	"code_862":                         862,
	"code_863":                         863,
	"code_864":                         864,
	"code_865":                         865,
	"oauthcode_866":                    866,
	"code_867":                         867,
	"code_868":                         868,
	"code_869":                         869,
	"code_870":                         870,
	"code_871":                         871,
	"code_872":                         872,
	"oauthcode_873":                    873,
	"code_874":                         874,
	"oauthcode_875":                    875,
	"code_876":                         876,
	"mdcode_877":                       877,
	"code_878":                         878,
	"code_879":                         879,
	"code_880":                         880,
	"code_881":                         881,
	"egwcode_882":                      882,
	"rediscode_883":                    883,
	"rediscode_884":                    884,
	"rediscode_885":                    885,
	"rediscode_886":                    886,
	"code_887":                         887,
	"aclapicode_888":                   888,
	"code_889":                         889,
	"merchantapicode_890":              890,
	"code_891":                         891,
	"code_892":                         892,
	"merchantapicode_893":              893,
	"code_894":                         894,
	"corepaymentcode_895":              895,
	"confirmcode_896":                  896,
	"code_897":                         897,
	"publicidcode_898":                 898,
	"code_899":                         899,
	"code_900":                         900,
	"code_901":                         901,
	"publicidcode_902":                 902,
	"code_903":                         903,
	"code_904":                         904,
	"code_905":                         905,
	"code_906":                         906,
	"code_907":                         907,
	"code_908":                         908,
	"merchantapicode_909":              909,
	"merchantapicode_910":              910,
	"code_911":                         911,
	"code_912":                         912,
	"code_913":                         913,
	"code_914":                         914,
	"code_915":                         915,
	"code_916":                         916,
	"code_917":                         917,
	"code_918":                         918,
	"code_919":                         919,
	"code_920":                         920,
	"code_921":                         921,
	"code_922":                         922,
	"code_923":                         923,
	"code_924":                         924,
	"code_925":                         925,
	"code_926":                         926,
	"code_927":                         927,
	"code_928":                         928,
	"publicidcode_929":                 929,
	"code_930":                         930,
	"code_931":                         931,
	"scopescode_932":                   932,
	"code_933":                         933,
	"code_934":                         934,
	"code_935":                         935,
	"code_937":                         937,
	"code_938":                         938,
	"code_939":                         939,
	"code_940":                         940,
	"code_941":                         941,
	"code_942":                         942,
	"code_943":                         943,
	"code_944":                         944,
	"publicidcode_945":                 945,
	"code_946":                         946,
	"code_947":                         947,
	"code_948":                         948,
	"code_949":                         949,
	"code_950":                         950,
	"publicidcode_951":                 951,
	"code_952":                         952,
	"code_953":                         953,
	"code_954":                         954,
	"code_955":                         955,
	"code_956":                         956,
	"code_957":                         957,
	"code_958":                         958,
	"code_959":                         959,
	"code_960":                         960,
	"code_961":                         961,
	"code_962":                         962,
	"authorizedrequestjsocode_963":     963,
	"clientsecretcode_964":             964,
	"clientidclientsecretcode_965":     965,
	"merchantidclientsecrcode_966":     966,
	"clientsecretclientidcode_967":     967,
	"emailclientidclientscode_968":     968,
	"code_969":                         969,
	"code_970":                         970,
	"code_971":                         971,
	"code_972":                         972,
	"idcode_973":                       973,
	"code_974":                         974,
	"code_975":                         975,
	"userclientidclientsecode_976":     976,
	"authorizedrequestjsocode_977":     977,
	"jsonmarshalsendemailcode_978":     978,
	"code_979":                         979,
	"formfilecode_980":                 980,
	"code_981":                         981,
	"code_982":                         982,
	"code_983":                         983,
	"code_984":                         984,
	"code_985":                         985,
	"code_986":                         986,
	"code_987":                         987,
	"code_988":                         988,
	"code_989":                         989,
	"code_990":                         990,
	"code_991":                         991,
	"code_992":                         992,
	"servicecode_993":                  993,
	"shouldbindjsonserviccode_994":     994,
	"authorizedrequestjsocode_995":     995,
	"authorizedrequestjsocode_996":     996,
	"useruserusernameservcode_997":     997,
	"merchantservicecode_999":          999,
	"namewebservicecode_1000":          1000,
	"contacttypenameservicode_1001":    1001,
	"publicidcode_1002":                1002,
	"code_1003":                        1003,
	"code_1004":                        1004,
	"code_1005":                        1005,
	"oauthcode_1006":                   1006,
	"code_1007":                        1007,
	"code_1008":                        1008,
	"code_1009":                        1009,
	"idservicecode_1010":               1010,
	"rsaservicecode_1011":              1011,
	"servicecode_1012":                 1012,
	"codebase64stringbasecode_1013":    1013,
	"xmlunmarshalservicecode_1014":     1014,
	"codebase64stringbasecode_1015":    1015,
	"xmlmarshalservicecode_1016":       1016,
	"oauthcode_1017":                   1017,
	"servicecode_1018":                 1018,
	"shopidcode_1019":                  1019,
	"jsonservicecode_1020":             1020,
	"authservicecode_1021":             1021,
	"deviceidauthservicecode_1022":     1022,
	"servicecode_1023":                 1023,
	"idservicecode_1024":               1024,
	"bindxmlwebservicecode_1025":       1025,
	"authorizedrequestjsocode_1026":    1026,
	"bindclientidpostlinkcode_1027":    1027,
	"clientidpostlinkinfocode_1028":    1028,
	"merchantidpostlinkincode_1029":    1029,
	"postlinkrabbitserviccode_1030":    1030,
	"terminalidpostlinkincode_1031":    1031,
	"cbindpostlinkinfoposcode_1032":    1032,
	"jsonmarshalpostlinkicode_1033":    1033,
	"clientidclientidservcode_1034":    1034,
	"shopmerchantidclientcode_1035":    1035,
	"shopmerchantidclientcode_1036":    1036,
	"bindinvoicewebserviccode_1037":    1037,
	"expireperiodinvoicescode_1038":    1038,
	"postlinkrabbitserviccode_1039":    1039,
	"servicecode_1040":                 1040,
	"jsonservicecode_1041":             1041,
	"egwcode_1042":                     1042,
	"emailclientsecretsercode_1043":    1043,
	"emailwebservicecode_1044":         1044,
	"authorizedrequestjsocode_1045":    1045,
	"jsonmarshalmerchantocode_1046":    1046,
	"jsonmarshalmerchantocode_1047":    1047,
	"bindjsonmerchantwebscode_1048":    1048,
	"clientidservicecode_1049":         1049,
	"clientscopesclientidcode_1050":    1050,
	"userloginservicecode_1051":        1051,
	"clientclientscopesuscode_1052":    1052,
	"bindjsoninvoicecode_1053":         1053,
	"invoicecode_1054":                 1054,
	"xmlway4code_1055":                 1055,
	"invoicecode_1056":                 1056,
	"code_1057":                        1057,
	"code_1058":                        1058,
	"code_1059":                        1059,
	"coreinvoicecode_1060":             1060,
	"bindjsoninvoiceinfocode_1061":     1061,
	"code_1062":                        1062,
	"code_1063":                        1063,
	"scannererrcode_1064":              1064,
	"bindcode_1065":                    1065,
	"shopidcode_1066":                  1066,
	"invoicelinkcode_1067":             1067,
	"marshalinvoicelinkcode_1068":      1068,
	"invoicelinkcode_1069":             1069,
	"invoicelinkcode_1070":             1070,
	"invoicelinkcode_1071":             1071,
	"publicidinvoicecode_1072":         1072,
	"paramsinvoicecode_1073":           1073,
	"invoicelinkcode_1074":             1074,
	"bindcode_1075":                    1075,
	"strconvatoiexpirepercode_1076":    1076,
	"invoicelinkcode_1077":             1077,
	"emailcode_1078":                   1078,
	"smscode_1079":                     1079,
	"invoicelinkcode_1080":             1080,
	"bindcode_1081":                    1081,
	"publicidcode_1082":                1082,
	"nameinvoicelinkcode_1083":         1083,
	"invoicelinkcode_1084":             1084,
	"searchparamsinvoicelcode_1085":    1085,
	"incorrectsearchmethocode_1086":    1086,
	"code_1087":                        1087,
	"invoicelinkidcode_1088":           1088,
	"invoicelinkcode_1089":             1089,
	"code_1090":                        1090,
	"invoicelinkcode_1091":             1091,
	"publicidcode_1092":                1092,
	"code_1093":                        1093,
	"code_1096":                        1096,
	"homebankcode_1097":                1097,
	"emailcode_1098":                   1098,
	"code_1099":                        1099,
	"code_1100":                        1100,
	"coreinvoicecode_1101":             1101,
	"authorizedrequestjsocode_1103":    1103,
	"authorizedrequestjsocode_1104":    1104,
	"idservicecode_1105":               1105,
	"clientidservicecode_1106":         1106,
	"terminalxlsmidtermincode_1107":    1107,
	"code_1130":                        1130,
	"code_1132":                        1132,
	"namecode_1133":                    1133,
	"postlinkcorecallbackcode_1144":    1144,
	"code_1145":                        1145,
	"cannotsendpostlinkcode_1146":      1146,
	"cannotsendpostlinkcode_1147":      1147,
	"bindjsoncode_1148":                1148,
	"base64code_1149":                  1149,
	"unmarshalcode_1150":               1150,
	"httpcoremigrationcode_1151":       1151,
	"code_1152":                        1152,
	"unmarshalouathcode_1153":          1153,
	"epay1code_1154":                   1154,
	"unmarshalepay1code_1155":          1155,
	"cannotsendpostlinkcode_1156":      1156,
	"code_1157":                        1157,
	"userservicecode_1158":             1158,
	"userservicecode_1159":             1159,
	"userservicecode_1160":             1160,
	"userservicecode_1161":             1161,
	"userservicecode_1162":             1162,
	"merchantcompanynamescode_1163":    1163,
	"merchantservicecode_1164":         1164,
	"merchantmerchantcode_1165":        1165,
	"userloginuserclientcode_1166":     1166,
	"merchantnameusercliecode_1167":    1167,
	"authorizedrequestjsocode_1168":    1168,
	"merchantnamecode_1169":            1169,
	"emailmerchantcode_1170":           1170,
	"shopmerchantcode_1171":            1171,
	"terminalmerchantcode_1172":        1172,
	"userusercode_1173":                1173,
	"userclientidcode_1174":            1174,
	"userclientusercode_1175":          1175,
	"terminalterminalidcode_1176":      1176,
	"currencycurrencynamecode_1177":    1177,
	"terminalidcode_1178":              1178,
	"terminalterminalidtecode_1179":    1179,
	"terminalidcode_1180":              1180,
	"authorizedrequestjsocode_1182":    1182,
	"code_1184":                        1184,
	"terminalcode_1185":                1185,
	"terminalcode_1186":                1186,
	"code_1187":                        1187,
	"merchantcontacttypencode_1188":    1188,
	"senderrorreportcontacode_1189":    1189,
	"code_1190":                        1190,
	"idcode_1191":                      1191,
	"excelcode_1192":                   1192,
	"currencycurrencyidcode_1193":      1193,
	"cardtypecardtypeidcode_1194":      1194,
	"emailcode_1195":                   1195,
	"jsonmarshalemailcode_1196":        1196,
	"authorizedrequestjsocode_1197":    1197,
	"merchantcontacttypeecode_1198":    1198,
	"merchantcontactemailcode_1199":    1199,
	"excelfileemailcode_1200":          1200,
	"code_1201":                        1201,
	"userlogincode_1202":               1202,
	"rowsexcelfilereportcode_1203":     1203,
	"userconnectionsusericode_1205":    1205,
	"kafkacode_1206":                   1206,
	"code_1207":                        1207,
	"code_1208":                        1208,
	"code_1209":                        1209,
	"code_1210":                        1210,
	"authorizedrequestmulcode_1211":    1211,
	"formatdataheaderwritcode_1212":    1212,
	"writerreadermultiparcode_1213":    1213,
	"apicdncode_1214":                  1214,
	"logourlshopidcode_1215":           1215,
	"bindjsonshopshopidcode_1216":      1216,
	"shopiddbshopidcode_1217":          1217,
	"jsonsamsungpaycode_1219":          1219,
	"requestjsonsamsungpacode_1220":    1220,
	"shopidshopidcode_1221":            1221,
	"terminalshopidcode_1222":          1222,
	"authorizedrequestjsocode_1223":    1223,
	"shopidshopidcode_1224":            1224,
	"bindjsoninoutterminacode_1225":    1225,
	"ididcode_1226":                    1226,
	"authorizedrequestjsocode_1227":    1227,
	"terminalterminalidcode_1228":      1228,
	"terminalidterminalidcode_1229":    1229,
	"terminalterminalidcode_1230":      1230,
	"jsonmarshalterminaltcode_1231":    1231,
	"pancode_1232":                     1232,
	"samsungpaycallbackrecode_1233":    1233,
	"staffstaffcode_1234":              1234,
	"bindcreatemvisatranscode_1235":    1235,
	"requestpostformxmlcrcode_1236":    1236,
	"requestpostformxmlcrcode_1237":    1237,
	"requestpostformxmlcrcode_1238":    1238,
	"requestpostformxmlcrcode_1239":    1239,
	"bindget3dsecurecode_1240":         1240,
	"requestpostformxmlgecode_1241":    1241,
	"requestpostformxmlgecode_1242":    1242,
	"requestpostformxmlgecode_1243":    1243,
	"requestpostformxmlgecode_1244":    1244,
	"bindmanagetranscode_1245":         1245,
	"requestpostformxmlmacode_1246":    1246,
	"requestpostformxmlmacode_1247":    1247,
	"requestpostformxmlmacode_1248":    1248,
	"requestpostformxmlmacode_1249":    1249,
	"bindcreatetranscode_1250":         1250,
	"requestpostformxmlcrcode_1251":    1251,
	"requestpostformxmlcrcode_1252":    1252,
	"requestpostformxmlcrcode_1253":    1253,
	"requestpostformxmlcrcode_1254":    1254,
	"bindrepaymentcode_1255":           1255,
	"requestpostformxmlrecode_1256":    1256,
	"requestpostformxmlrecode_1257":    1257,
	"requestpostformxmlrecode_1258":    1258,
	"requestpostformxmlrecode_1259":    1259,
	"bindgettokencode_1260":            1260,
	"requestpostformxmlgecode_1261":    1261,
	"requestpostformxmlgecode_1262":    1262,
	"requestpostformxmlgecode_1263":    1263,
	"requestpostformxmlgecode_1264":    1264,
	"bindtransactioncode_1265":         1265,
	"requestpostformxmltrcode_1266":    1266,
	"cardsystemcommunicatcode_1267":    1267,
	"requestpostformxmltrcode_1268":    1268,
	"requestpostformxmltrcode_1269":    1269,
	"requestpostformxmltrcode_1270":    1270,
	"bindtransactionconficode_1271":    1271,
	"requestpostformxmltrcode_1272":    1272,
	"requestpostformxmltrcode_1273":    1273,
	"requestpostformxmltrcode_1274":    1274,
	"requestpostformxmltrcode_1275":    1275,
	"invoicelinksgetinvoicode_1276":    1276,
	"publiciduseridcode_1277":          1277,
	"paramscode_1278":                  1278,
	"postlinkcorecallbackcode_1279":    1279,
	"rabbitpostlinkcode_1280":          1280,
	"publicidtokeninvoicecode_1281":    1281,
	"slowpostlinkcorecallcode_1282":    1282,
	"getopenwayidhalykboncode_1283":    1283,
	"cardtypenilcode_1284":             1284,
	"cardtypecode_1285":                1285,
	"postlinkcorecallbackcode_1286":    1286,
	"postlinkcode_1287":                1287,
	"postlinkinvoiceidcode_1288":       1288,
	"callbackcorecallbackcode_1289":    1289,
	"invoicelinkinvoiceidcode_1290":    1290,
	"invoicelinkinvoiceidcode_1291":    1291,
	"invoicelinkinvoicelicode_1293":    1293,
	"errormessageinvoicelcode_1294":    1294,
	"transactionstatusautcode_1295":    1295,
	"filepathfileidcode_1296":          1296,
	"bindchargecode_1297":              1297,
	"code_1298":                        1298,
	"informationstatementcode_1299":    1299,
	"refidcode_1301":                   1301,
	"emailcode_1302":                   1302,
	"statementhistoryomitcode_1303":    1303,
	"stafflistcode_1304":               1304,
	"getcode_1305":                     1305,
	"authorizedrequestjsocode_1306":    1306,
	"code_1307":                        1307,
	"coreinvoiceinvoicelicode_1308":    1308,
	"coreinvoiceinvoicelicode_1309":    1309,
	"invoicelinkcode_1310":             1310,
	"invoicelinkinvoiceidcode_1311":    1311,
	"coreinvoiceinvoiceincode_1312":    1312,
	"kafkacode_1315":                   1315,
	"homebankacode_1317":               1317,
	"code_1318":                        1318,
	"useracode_1319":                   1319,
	"staffupdatepasswordcode_1320":     1320,
	"updatestaffrolecode_1321":         1321,
	"code_1322":                        1322,
	"commitcode_1323":                  1323,
	"code_1324":                        1324,
	"code_1325":                        1325,
	"code_1326":                        1326,
	"float64feeamountstricode_1327":    1327,
	"float64settlamountstcode_1328":    1328,
	"float64tramountstrincode_1329":    1329,
	"code_1330":                        1330,
	"code_1331":                        1331,
	"parsemediatypecode_1332":          1332,
	"createfilecode_1333":              1333,
	"emailcode_1334":                   1334,
	"hbcode_1335":                      1335,
	"code_1336":                        1336,
	"code_1337":                        1337,
	"activecode_1338":                  1338,
	"chargedcode_1339":                 1339,
	"merchantterminaltermcode_1340":    1340,
	"postingdatestringordcode_1341":    1341,
	"hbcode_1342":                      1342,
	"ftpcode_1343":                     1343,
	"jobexecutiongocroncode_1344":      1344,
	"float64feeamountstricode_1346":    1346,
	"float64settlamountstcode_1347":    1347,
	"float64tramountstrincode_1348":    1348,
	"merchantpublicidcode_1361":        1361,
	"merchantinfopublicidcode_1366":    1366,
	"publicidcode_1367":                1367,
	"code_1368":                        1368,
	"code_1369":                        1369,
	"code_1370":                        1370,
	"authorizedrequestjsocode_1371":    1371,
	"coregetcardcode_1372":             1372,
	"code_1373":                        1373,
	"oauthcode_1374":                   1374,
	"requestclientcode_1375":           1375,
	"code_1376":                        1376,
	"code_1378":                        1378,
	"scopecode_1379":                   1379,
	"scopecode_1380":                   1380,
	"clientscopescode_1381":            1381,
	"code_1382":                        1382,
	"code_1383":                        1383,
	"code_1384":                        1384,
	"code_1385":                        1385,
	"code_1386":                        1386,
	"homebankcode_1387":                1387,
	"hmcode_1388":                      1388,
	"requestclientupdatecode_1389":     1389,
	"clientidcode_1390":                1390,
	"getclientclientcode_1391":         1391,
	"getclientscopecode_1392":          1392,
	"updatescopecode_1393":             1393,
	"scopescode_1394":                  1394,
	"clientcode_1395":                  1395,
	"scopecode_1396":                   1396,
	"clientscopescode_1397":            1397,
	"readererrorcode_1398":             1398,
	"terminaldbmerchantidcode_1400":    1400,
	"clientclientiddbtildcode_1403":    1403,
	"clientclientiddbcode_1405":        1405,
	"clientiptildacode_1406":           1406,
	"bindmultipartformdatcode_1407":    1407,
	"tildatildacode_1408":              1408,
	"tildacode_1409":                   1409,
	"float64amounttildacode_1410":      1410,
	"authorizedrequestjsocode_1411":    1411,
	"sourcecode_1412":                  1412,
	"callbackcorecallbackcode_1413":    1413,
	"postlinktildacode_1414":           1414,
	"code_1415":                        1415,
	"code_1416":                        1416,
	"authorizedrequestjsocode_1418":    1418,
	"corestatementcode_1419":           1419,
	"code_1421":                        1421,
	"code_1422":                        1422,
	"merchantcompanynamecode_1423":     1423,
	"clienttypecode_1424":              1424,
	"clienttypenametildacode_1425":     1425,
	"clienttypetildacliencode_1426":    1426,
	"bindjsonbindjsoncode_1427":        1427,
	"parsingerrorcode_1428":            1428,
	"code_1429":                        1429,
	"code_1430":                        1430,
	"code_1431":                        1431,
	"likecode_1432":                    1432,
	"betweencode_1433":                 1433,
	"code_1434":                        1434,
	"code_1436":                        1436,
	"code_1437":                        1437,
	"code_1438":                        1438,
	"countcode_1439":                   1439,
	"code_1440":                        1440,
	"code_1441":                        1441,
	"referenceepaycode_1442":           1442,
	"code_1443":                        1443,
	"code_1444":                        1444,
	"code_1445":                        1445,
	"hmhmbcode_1446":                   1446,
	"jsonjsoncode_1447":                1447,
	"urlbodycode_1448":                 1448,
	"code_1449":                        1449,
	"getusescopebyclientcode_1451":     1451,
	"code_1452":                        1452,
	"jsonunmarshalcode_1453":           1453,
	"code_1454":                        1454,
	"code_1455":                        1455,
	"websocketcode_1457":               1457,
	"websocketcode_1458":               1458,
	"websocketcode_1459":               1459,
	"websocketcode_1460":               1460,
	"websocketcode_1461":               1461,
	"websocketcode_1462":               1462,
	"code_1463":                        1463,
	"code_1464":                        1464,
	"code_1465":                        1465,
	"code_1466":                        1466,
	"code_1467":                        1467,
	"updatemerchantcode_1468":          1468,
	"updatemerchantshopcode_1469":      1469,
	"updatemerchanttermincode_1470":    1470,
	"code_1471":                        1471,
	"code_1472":                        1472,
	"emailcode_1473":                   1473,
	"nameservicecode_1475":             1475,
	"idparamscode_1476":                1476,
	"merchantpublicidcode_1477":        1477,
	"terminalmerchantidcode_1478":      1478,
	"shopidshopwebservicecode_1479":    1479,
	"code_1480":                        1480,
	"code_1481":                        1481,
	"idparamscode_1482":                1482,
	"jsonjsoncode_1483":                1483,
	"jsonjsonmarshaljsoncode_1484":     1484,
	"idparamscode_1486":                1486,
	"code_1487":                        1487,
	"code_1488":                        1488,
	"code_1489":                        1489,
	"code_1490":                        1490,
	"paymentrequestcode_1491":          1491,
	"code_1492":                        1492,
	"jsonunmarshalcode_1493":           1493,
	"jsonmarshalcode_1494":             1494,
	"code_1495":                        1495,
	"jsonjsoncode_1496":                1496,
	"code_1497":                        1497,
	"code_1498":                        1498,
	"authorizedrequestjsocode_1499":    1499,
	"authorizedrequestjsocode_1500":    1500,
	"authorizedrequestjsocode_1502":    1502,
	"authorizedrequestjsocode_1503":    1503,
	"authorizedrequestjsocode_1504":    1504,
	"terminaluuidcode_1505":            1505,
	"bindgrafanacode_1506":             1506,
	"httpgrafanacode_1507":             1507,
	"unauthorizedcode_1508":            1508,
	"badtokencode_1509":                1509,
	"jsonunmarshalcode_1510":           1510,
	"jsonmarshalcode_1511":             1511,
	"requestjsongeoipcode_1512":        1512,
	"code_1513":                        1513,
	"jsonunmarshalcode_1514":           1514,
	"jsonmarshalcode_1515":             1515,
	"code_1516":                        1516,
	"qrtransactioninfocode_1517":       1517,
	"cardtypeidcode_1518":              1518,
	"currencyidcode_1519":              1519,
	"statusidcode_1520":                1520,
	"code_1521":                        1521,
	"code_1522":                        1522,
	"code_1523":                        1523,
	"code_1524":                        1524,
	"shopidmerchantidcliecode_1525":    1525,
	"code_1526":                        1526,
	"qrcode_1527":                      1527,
	"sourcelistidcode_1528":            1528,
	"authorizedrequestjsocode_1529":    1529,
	"xlsidcode_1530":                   1530,
	"jsonjsoncode_1531":                1531,
	"redispartialtransactcode_1532":    1532,
	"code_1533":                        1533,
	"code_1534":                        1534,
	"code_1535":                        1535,
	"code_1536":                        1536,
	"code_1537":                        1537,
	"redisinvoiceidcode_1538":          1538,
	"epay1code_1539":                   1539,
	"epay1code_1540":                   1540,
	"epay1code_1541":                   1541,
	"epay1code_1542":                   1542,
	"transactiontypeidcode_1543":       1543,
	"clienttypeidcode_1544":            1544,
	"jsonmarshalucscode_1545":          1545,
	"code_1546":                        1546,
	"scopecode_1547":                   1547,
	"redisqrstatuscode_1548":           1548,
	"authorizedrequestjsocode_1549":    1549,
	"code_1550":                        1550,
	"redisrealipcode_1551":             1551,
	"requestjsoncoreqrqrscode_1552":    1552,
	"kafkareadmessagecode_1553":        1553,
	"jsonunmarshalkafkamecode_1554":    1554,
	"jsonjsonmarshaljsoncode_1555":     1555,
	"kafkacode_1556":                   1556,
	"setoffsetkafkacode_1557":          1557,
	"code_1558":                        1558,
	"code_1559":                        1559,
	"code_1560":                        1560,
	"code_1561":                        1561,
	"httpcode_1562":                    1562,
	"httpcode_1563":                    1563,
	"httpcode_1564":                    1564,
	"httpcode_1565":                    1565,
	"authcode_1566":                    1566,
	"egatewaycode_1567":                1567,
	"egatewaycode_1568":                1568,
	"code_1569":                        1569,
	"jsoncode_1570":                    1570,
	"code_1571":                        1571,
	"code_1572":                        1572,
	"code_1578":                        1578,
	"code_1579":                        1579,
	"readallfromrequestbocode_1604":    1604,
	"parsefloatrefundcode_1605":        1605,
	"amountbodyurlrefundcode_1606":     1606,
	"code_1607":                        1607,
	"chargecode_1608":                  1608,
	"cancelcode_1609":                  1609,
	"refundcode_1610":                  1610,
	"httpcode_1611":                    1611,
	"code_1612":                        1612,
	"code_1614":                        1614,
	"terminalidcode_1615":              1615,
	"code_1616":                        1616,
	"jsonunmarshalbincode_1617":        1617,
	"jsonmarshalcode_1618":             1618,
	"code_1619":                        1619,
	"jsonapiosuvoxcode_1620":           1620,
	"apiosuvoxcode_1621":               1621,
	"apiosuvoxhttpcode200code_1622":    1622,
	"code_1623":                        1623,
	"sourcelistcode_1624":              1624,
	"code_1636":                        1636,
	"code_1637":                        1637,
	"osuvoxcode_1638":                  1638,
	"invalidcardidcode_1639":           1639,
	"osuvoxcode_1640":                  1640,
	"code_1641":                        1641,
	"osuvoxcode_1642":                  1642,
	"singlemessageschemecode_1650":     1650,
	"code_1651":                        1651,
	"otpcode_1652":                     1652,
	"otpcode_1653":                     1653,
	"otpcodecode_1654":                 1654,
	"otpcodecode_1655":                 1655,
	"redisterminaluuidinvcode_1656":    1656,
	"invalidterminalidcode_1658":       1658,
	"invalidamoutcode_1659":            1659,
	"code_1660":                        1660,
	"code_1661":                        1661,
	"authorizedrequestjsocode_1663":    1663,
	"bindgettokenbycardcode_1664":      1664,
	"requestpostformxmlgecode_1665":    1665,
	"requestpostformxmlgecode_1666":    1666,
	"requestpostformxmlgecode_1667":    1667,
	"requestpostformxmlgecode_1668":    1668,
	"codebase64stringbasecode_1670":    1670,
	"authorizedrequestjsocode_1671":    1671,
	"cardcode_1672":                    1672,
	"homebankpayosuvoxcarcode_1673":    1673,
	"osuvoxcode_1675":                  1675,
	"osuvoxcode_1677":                  1677,
	"osuvoxcode_1679":                  1679,
	"osuvoxcode_1680":                  1680,
	"osuvoxcode_1681":                  1681,
	"code_1682":                        1682,
	"osuvoxcode_1683":                  1683,
	"osuvoxcode_1685":                  1685,
	"transactioncode_1690":             1690,
	"couldnotgetphonenumbcode_1692":    1692,
	"code_1693":                        1693,
	"code_1694":                        1694,
	"osuvoxcode_1695":                  1695,
	"xmlbodycode_1699":                 1699,
	"xmlmarhsalswitchpaymcode_1700":    1700,
	"terminalidcode_1703":              1703,
	"terminalxlsmidtermincode_1704":    1704,
	"ibancode_1705":                    1705,
	"ibancode_1706":                    1706,
	"terminalidcode_1707":              1707,
	"ibancode_1708":                    1708,
	"code_1709":                        1709,
	"code_1710":                        1710,
	"code_1711":                        1711,
	"code_1712":                        1712,
	"invoiceidcode_1713":               1713,
	"code_1714":                        1714,
	"authcode_1715":                    1715,
	"paymenttypecode_1716":             1716,
	"osuvoxcode_1717":                  1717,
	"paymenttypecode_1719":             1719,
	"paymentsystemcode_1720":           1720,
	"code_1723":                        1723,
	"code_1724":                        1724,
	"code_1759":                        1759,
	"code_1760":                        1760,
	"code_1761":                        1761,
	"code_1762":                        1762,
	"shopidcode_1763":                  1763,
	"code_1764":                        1764,
	"orderidcode_1765":                 1765,
	"coremerchantcode_1771":            1771,
	"postlinkcorecallbackcode_1780":    1780,
	"cardtypenilcode_1781":             1781,
	"switchpaymentcode_1786":           1786,
	"code_1787":                        1787,
	"httpgetmerchantcerticode_1788":    1788,
	"httpgetmerchantcreatcode_1789":    1789,
	"enabledcode_1849":                 1849,
	"shopcode_1850":                    1850,
	"code_1855":                        1855,
	"terminalidcode_1856":              1856,
	"shopidcode_1857":                  1857,
	"idcode_1858":                      1858,
	"shopidcode_1859":                  1859,
	"tokenexpireinsecondscode_1860":    1860,
	"apiepay1code_1899":                1899,
	"requestcode_1900":                 1900,
	"code_1901":                        1901,
	"code_1902":                        1902,
	"jsoncode_1903":                    1903,
	"idservicecode_1944":               1944,
	"updatemerchantcontaccode_1945":    1945,
	"contacttypenameservicode_1946":    1946,
	"code_1954":                        1954,
	"code_1955":                        1955,
	"code_1956":                        1956,
	"code_1961":                        1961,
	"code_1962":                        1962,
	"code_1964":                        1964,
	"terminalshopnamecode_1965":        1965,
	"code_1970":                        1970,
	"invoiceidcode_1971":               1971,
	"amount0code_1975":                 1975,
	"code_1976":                        1976,
	"code_1977":                        1977,
	"tokenexpiredtryagaincode_1979":    1979,
	"mpancode_1980":                    1980,
	"code_2008":                        2008,
	"recordnotfoundcode_2009":          2009,
	"code_2015":                        2015,
	"code_2016":                        2016,
	"redismaxreqperseccode_2017":       2017,
	"code_2018":                        2018,
	"googlepaycode_2028":               2028,
	"googlepaycode_2030":               2030,
	"accountidcode_2031":               2031,
	"callbackcode_2032":                2032,
	"code_2033":                        2033,
	"code_2046":                        2046,
	"jsoncode_2048":                    2048,
	"code_2049":                        2049,
	"code_2050":                        2050,
	"code_2051":                        2051,
	"xlsurlcode_2052":                  2052,
	"code_2053":                        2053,
	"jsonupdateprofilestacode_2057":    2057,
	"jsoncreatestaffproficode_2058":    2058,
	"code_2059":                        2059,
	"code_2060":                        2060,
	"invalidamountamountmcode_2061":    2061,
	"corebusinessreportcode_2067":      2067,
	"code_2069":                        2069,
	"emailcode_2070":                   2070,
	"ginbindcode_2074":                 2074,
	"xlstidxlsmidcode_2075":            2075,
	"code_2076":                        2076,
	"mpgscode_2085":                    2085,
	"mpgscode_2086":                    2086,
	"mpgscode_2087":                    2087,
	"mpgscode_2088":                    2088,
	"mpgscode_2089":                    2089,
	"mpgscode_2090":                    2090,
	"panbincode_2091":                  2091,
	"binorpanrestrictedcode_2092":      2092,
	"bindcode_2122":                    2122,
	"code_2123":                        2123,
	"code_2124":                        2124,
	"merchantcode_2125":                2125,
	"merchantcode_2126":                2126,
	"code_2127":                        2127,
	"threecode_2128":                   2128,
	"code_2129":                        2129,
	"openwayidcode_2130":               2130,
	"usdcode_2131":                     2131,
	"code_2132":                        2132,
	"code_2133":                        2133,
	"code_2134":                        2134,
	"code_2135":                        2135,
	"xlscode_2136":                     2136,
	"code_2137":                        2137,
	"postlinkcorecallbackcode_2138":    2138,
	"code_2139":                        2139,
	"cryptogramopenwayidcode_2146":     2146,
	"ipcode_2147":                      2147,
	"code_2148":                        2148,
	"formdatasftpproxycode_2152":       2152,
	"httpnewrequestsftpprcode_2153":    2153,
	"redispipelinecode_2154":           2154,
	"transactionamountshocode_2156":    2156,
	"code_2157":                        2157,
	"urlpagesizecode_2158":             2158,
	"code_2159":                        2159,
	"urlpagesizecode_2191":             2191,
	"pagesizecode_2193":                2193,
	"pagesizecode_2194":                2194,
	"code_2200":                        2200,
	"code_2203":                        2203,
	"code_2204":                        2204,
	"code_2205":                        2205,
	"unmarshalcode_2206":               2206,
	"code_2207":                        2207,
	"code_2210":                        2210,
	"publicidonboardingcode_2211":      2211,
	"aclservicecode_2212":              2212,
	"staffcode_2213":                   2213,
	"openwaycardidcode_2214":           2214,
	"code_2218":                        2218,
	"code_2237":                        2237,
	"postlinkacode_2238":               2238,
	"postlinkacode_2239":               2239,
	"postlinkacode_2240":               2240,
	"postlinkacode_2241":               2241,
	"postlinkcode_2242":                2242,
	"code_2243":                        2243,
	"code_2244":                        2244,
	"code_2245":                        2245,
	"code_2249":                        2249,
	"p2pcode_2250":                     2250,
	"code_2257":                        2257,
	"jsonjsonmarshaljsoncode_2268":     2268,
	"code_2271":                        2271,
	"code_2301":                        2301,
	"code_2305":                        2305,
	"qrbyqrcode_2322":                  2322,
	"code_2323":                        2323,
	"code_2326":                        2326,
	"cardidcode_2339":                  2339,
	"code_2349":                        2349,
	"code_2350":                        2350,
	"code_2351":                        2351,
	"code_2352":                        2352,
	"code_2353":                        2353,
	"code_2354":                        2354,
	"clientidcode_2355":                2355,
	"code_2356":                        2356,
	"resultcodestatusidcode_2360":      2360,
	"paymentsystemcode_2362":           2362,
	"kafkacode_2365":                   2365,
	"shopinfocode_2366":                2366,
	"code_2367":                        2367,
	"code_2433":                        2433,
	"uzgwcode_2435":                    2435,
	"code_2624":                        2624,
	"checktransactionstatcode_2660":    2660,
	"corepaymentcode_2678":             2678,
	"corepaymentcode_2679":             2679,
	"otpcodeapiuzgatewaycode_2704":     2704,
	"codeapiuzgatewaycode_2705":        2705,
	"incorrectcurrencycode_101":        101,
	"donotreattemptrestricode_2872":    2872,
}

func ConvertStringToEnumEpayPayOutCode(in string) EnumEpayPayOutCode {
	if result, ok := EnumEpayPayOutCode_value[in]; ok {
		return EnumEpayPayOutCode(result)
	}

	if result, ok := EnumEpayPayOutCode_Lower_value[strings.ToLower(in)]; ok {
		return EnumEpayPayOutCode(result)
	}

	return EnumEpayPayOutCode(math.MinInt32)
}

var SliceEnumEpayPayOutCodeConvert *sliceEnumEpayPayOutCodeConvert

type sliceEnumEpayPayOutCodeConvert struct{}

func (*sliceEnumEpayPayOutCodeConvert) Synonym(in []EnumEpayPayOutCode) []EnumEpayPayOutCode {
	result := make([]EnumEpayPayOutCode, len(in))
	for i, v := range in {
		result[i] = v.Synonym()
	}

	return result
}

func (*sliceEnumEpayPayOutCodeConvert) Int32(in []EnumEpayPayOutCode) []int32 {
	result := make([]int32, len(in))
	for i, v := range in {
		result[i] = v.Int32()
	}

	return result
}

func (*sliceEnumEpayPayOutCodeConvert) Uint32(in []EnumEpayPayOutCode) []uint32 {
	result := make([]uint32, len(in))
	for i, v := range in {
		result[i] = v.Uint32()
	}

	return result
}

func (*sliceEnumEpayPayOutCodeConvert) Uint64(in []EnumEpayPayOutCode) []uint64 {
	result := make([]uint64, len(in))
	for i, v := range in {
		result[i] = v.Uint64()
	}

	return result
}

func (*sliceEnumEpayPayOutCodeConvert) Int64(in []EnumEpayPayOutCode) []int64 {
	result := make([]int64, len(in))
	for i, v := range in {
		result[i] = v.Int64()
	}

	return result
}

func (*sliceEnumEpayPayOutCodeConvert) Uint(in []EnumEpayPayOutCode) []uint {
	result := make([]uint, len(in))
	for i, v := range in {
		result[i] = v.Uint()
	}

	return result
}

func (*sliceEnumEpayPayOutCodeConvert) Int(in []EnumEpayPayOutCode) []int {
	result := make([]int, len(in))
	for i, v := range in {
		result[i] = v.Int()
	}

	return result
}

func (*sliceEnumEpayPayOutCodeConvert) IsKnown(in []EnumEpayPayOutCode) []bool {
	result := make([]bool, len(in))
	for i, v := range in {
		result[i] = v.IsKnown()
	}

	return result
}

func (*sliceEnumEpayPayOutCodeConvert) ConvertIntToEnumEpayPayOutCode(in []int) []EnumEpayPayOutCode {
	result := make([]EnumEpayPayOutCode, len(in))
	for i, v := range in {
		result[i] = ConvertIntToEnumEpayPayOutCode(v)
	}

	return result
}

func (*sliceEnumEpayPayOutCodeConvert) ConvertUintToEnumEpayPayOutCode(in []uint) []EnumEpayPayOutCode {
	result := make([]EnumEpayPayOutCode, len(in))
	for i, v := range in {
		result[i] = ConvertUintToEnumEpayPayOutCode(v)
	}

	return result
}

func (*sliceEnumEpayPayOutCodeConvert) ConvertInt32ToEnumEpayPayOutCode(in []int32) []EnumEpayPayOutCode {
	result := make([]EnumEpayPayOutCode, len(in))
	for i, v := range in {
		result[i] = ConvertInt32ToEnumEpayPayOutCode(v)
	}

	return result
}

func (*sliceEnumEpayPayOutCodeConvert) ConvertUint32ToEnumEpayPayOutCode(in []uint32) []EnumEpayPayOutCode {
	result := make([]EnumEpayPayOutCode, len(in))
	for i, v := range in {
		result[i] = ConvertUint32ToEnumEpayPayOutCode(v)
	}

	return result
}

func (*sliceEnumEpayPayOutCodeConvert) ConvertInt64ToEnumEpayPayOutCode(in []int64) []EnumEpayPayOutCode {
	result := make([]EnumEpayPayOutCode, len(in))
	for i, v := range in {
		result[i] = ConvertInt64ToEnumEpayPayOutCode(v)
	}

	return result
}

func (*sliceEnumEpayPayOutCodeConvert) ConvertUint64ToEnumEpayPayOutCode(in []uint64) []EnumEpayPayOutCode {
	result := make([]EnumEpayPayOutCode, len(in))
	for i, v := range in {
		result[i] = ConvertUint64ToEnumEpayPayOutCode(v)
	}

	return result
}

func (*sliceEnumEpayPayOutCodeConvert) ConvertStringToEnumEpayPayOutCode(in []string) []EnumEpayPayOutCode {
	result := make([]EnumEpayPayOutCode, len(in))
	for i, v := range in {
		result[i] = ConvertStringToEnumEpayPayOutCode(v)
	}

	return result
}

func NewEnumEpayPayOutCodeUsage() *EnumEpayPayOutCodeUsage {
	return &EnumEpayPayOutCodeUsage{
		enumMap: map[EnumEpayPayOutCode]bool{
			EnumEpayPayOutCode_Empty_Response_Code:              false,
			EnumEpayPayOutCode_ClientauthenticationCode_33:      false,
			EnumEpayPayOutCode_SystemerrorpleasetryCode_50:      false,
			EnumEpayPayOutCode_ErrorinCVC2orCVC2DesCode_18:      false,
			EnumEpayPayOutCode_InvalidRetrievalrefeCode_15:      false,
			EnumEpayPayOutCode_TheoperationfailedplCode_454:     false,
			EnumEpayPayOutCode_ThreeDSecurecheckfailedCode_455:  false,
			EnumEpayPayOutCode_AccessdeniedCode_456:             false,
			EnumEpayPayOutCode_ErrorincardexpiratioCode_457:     false,
			EnumEpayPayOutCode_ServerisnotrespondinCode_458:     false,
			EnumEpayPayOutCode_ServerisnotrespondinCode_459:     false,
			EnumEpayPayOutCode_NoorinvalidresponcerCode_460:     false,
			EnumEpayPayOutCode_BadCGIrequestCode_461:            false,
			EnumEpayPayOutCode_CallyourbankCode_462:             false,
			EnumEpayPayOutCode_CallyourbankCode_463:             false,
			EnumEpayPayOutCode_InvalidmerchantCode_464:          false,
			EnumEpayPayOutCode_YourcardisrestrictedCode_465:     false,
			EnumEpayPayOutCode_NotpermittedtoclientCode_466:     false,
			EnumEpayPayOutCode_YourcardisdisabledCode_467:       false,
			EnumEpayPayOutCode_AdditionalidentificaCode_468:     false,
			EnumEpayPayOutCode_InvalidtransactionCode_469:       false,
			EnumEpayPayOutCode_InvalidamountCode_470:            false,
			EnumEpayPayOutCode_NosuchcardCode_471:               false,
			EnumEpayPayOutCode_NosuchcardCode_472:               false,
			EnumEpayPayOutCode_AuthenticationfailedCode_473:     false,
			EnumEpayPayOutCode_InvalidresponseCode_475:          false,
			EnumEpayPayOutCode_NoactiontakenCode_476:            false,
			EnumEpayPayOutCode_FormaterrorCode_477:              false,
			EnumEpayPayOutCode_ExpiredcardCode_478:              false,
			EnumEpayPayOutCode_RestrictedcardCode_479:           false,
			EnumEpayPayOutCode_CallyourbankCode_480:             false,
			EnumEpayPayOutCode_LostcardCode_481:                 false,
			EnumEpayPayOutCode_LostcardCode_482:                 false,
			EnumEpayPayOutCode_StolencardCode_483:               false,
			EnumEpayPayOutCode_NotsufficientfundsCode_484:       false,
			EnumEpayPayOutCode_ExpiredcardCode_485:              false,
			EnumEpayPayOutCode_NotpermittedtoclientCode_486:     false,
			EnumEpayPayOutCode_NotpermittedtomerchaCode_487:     false,
			EnumEpayPayOutCode_ExceedsamountlimitCode_488:       false,
			EnumEpayPayOutCode_RestrictedcardCode_489:           false,
			EnumEpayPayOutCode_InvalidContractcardCode_490:      false,
			EnumEpayPayOutCode_ExceedsfrequencylimiCode_491:     false,
			EnumEpayPayOutCode_PINtriesexceededCode_492:         false,
			EnumEpayPayOutCode_TimeoutatissuersysteCode_493:     false,
			EnumEpayPayOutCode_IssuerunavailableCode_494:        false,
			EnumEpayPayOutCode_CannotbecompletedvioCode_495:     false,
			EnumEpayPayOutCode_ThreeDSecureSecureCodeabCode_496: false,
			EnumEpayPayOutCode_ServerisnotrespondinCode_497:     false,
			EnumEpayPayOutCode_ErrorincurrencyfieldCode_499:     false,
			EnumEpayPayOutCode_ThreeDSecurecheckfailedCode_500:  false,
			EnumEpayPayOutCode_CardcheckfailedCode_501:          false,
			EnumEpayPayOutCode_ThreeDSecureSecureCodeabCode_502: false,
			EnumEpayPayOutCode_ThreeDSecureSecureCodeabCode_503: false,
			EnumEpayPayOutCode_TransactiondeclinedCCode_523:     false,
			EnumEpayPayOutCode_YourcardisrestrictedCode_524:     false,
			EnumEpayPayOutCode_YourcardisrestrictedCode_525:     false,
			EnumEpayPayOutCode_SystemerrorPleasetryCode_526:     false,
			EnumEpayPayOutCode_TransactiondeclinedCCode_527:     false,
			EnumEpayPayOutCode_ThedailylimitofincomCode_528:     false,
			EnumEpayPayOutCode_TransactiondeclinedCCode_531:     false,
			EnumEpayPayOutCode_UnabletoverifyPINcalCode_532:     false,
			EnumEpayPayOutCode_AuthenticationfailedCode_19:      false,
			EnumEpayPayOutCode_ErrorincardexpiratioCode_9:       false,
			EnumEpayPayOutCode_ServerisnotrespondinCode_8:       false,
			EnumEpayPayOutCode_ServerisnotrespondinCode_4:       false,
			EnumEpayPayOutCode_ErrorBindActionCreatCode_1:       false,
			EnumEpayPayOutCode_ErrorcreatingrecordiCode_2:       false,
			EnumEpayPayOutCode_ErrorBindActionUpdatCode_3:       false,
			EnumEpayPayOutCode_ErrorwhileupdatingreCode_4:       false,
			EnumEpayPayOutCode_ErrorwhiledeletingthCode_5:       false,
			EnumEpayPayOutCode_ErrorGetByNameActionCode_6:       false,
			EnumEpayPayOutCode_ErrorGetByIDActionGeCode_7:       false,
			EnumEpayPayOutCode_ErrorBindActionCreatCode_8:       false,
			EnumEpayPayOutCode_ErrorcreatingrecordiCode_9:       false,
			EnumEpayPayOutCode_RedisActionCreateErrCode_10:      false,
			EnumEpayPayOutCode_ErrorBindActionUpdatCode_11:      false,
			EnumEpayPayOutCode_ErrorwhileupdatingreCode_12:      false,
			EnumEpayPayOutCode_RedisActionUpdateErrCode_13:      false,
			EnumEpayPayOutCode_ErrorwhiledeletingthCode_14:      false,
			EnumEpayPayOutCode_RedisActionDeleteErrCode_15:      false,
			EnumEpayPayOutCode_ErrorGetByCodeinPostCode_16:      false,
			EnumEpayPayOutCode_CodeActionGetErrorByCode_17:      false,
			EnumEpayPayOutCode_IDApplicationIDAppliCode_18:      false,
			EnumEpayPayOutCode_CodeCode_19:                      false,
			EnumEpayPayOutCode_IDApplicationIDAppliCode_20:      false,
			EnumEpayPayOutCode_IDApplicationApplicaCode_21:      false,
			EnumEpayPayOutCode_Code_22:                          false,
			EnumEpayPayOutCode_SuccessfullyCode_23:              false,
			EnumEpayPayOutCode_Test111Code_24:                   false,
			EnumEpayPayOutCode_InvoiceidCode_25:                 false,
			EnumEpayPayOutCode_BindJSONBindJSONCode_27:          false,
			EnumEpayPayOutCode_TransactionalreadyprCode_28:      false,
			EnumEpayPayOutCode_TransactionalreadyprCode_29:      false,
			EnumEpayPayOutCode_ErrorGetShopsByMerchCode_54:      false,
			EnumEpayPayOutCode_IdCode_55:                        false,
			EnumEpayPayOutCode_ErrorGetShopByIDCode_56:          false,
			EnumEpayPayOutCode_ErrorBindCode_57:                 false,
			EnumEpayPayOutCode_ErrorBindCode_58:                 false,
			EnumEpayPayOutCode_ErrorCreateShopCode_59:           false,
			EnumEpayPayOutCode_ErrorGetMerchantByIDCode_60:      false,
			EnumEpayPayOutCode_ErrorGetStatusByNameCode_61:      false,
			EnumEpayPayOutCode_ErrorCreateDefaultCoCode_64:      false,
			EnumEpayPayOutCode_ErrorCreateMerchantCode_65:       false,
			EnumEpayPayOutCode_ErrorVerificationSerCode_71:      false,
			EnumEpayPayOutCode_ErrorCheckMerchantCode_72:        false,
			EnumEpayPayOutCode_ErrorSendVerificatioCode_73:      false,
			EnumEpayPayOutCode_ErrorVerificationSerCode_74:      false,
			EnumEpayPayOutCode_ErrorFindMerchantByECode_75:      false,
			EnumEpayPayOutCode_ErrorUpdateMerchantCode_76:       false,
			EnumEpayPayOutCode_ErrorRegistrationUseCode_77:      false,
			EnumEpayPayOutCode_RrnCode_83:                       false,
			EnumEpayPayOutCode_ErrorGetCardTypebyRRCode_84:      false,
			EnumEpayPayOutCode_ErrorCheckTransactioCode_85:      false,
			EnumEpayPayOutCode_UnauthorizedCode_86:              false,
			EnumEpayPayOutCode_TokenisnotvalidCode_87:           false,
			EnumEpayPayOutCode_ErrorCheckTokenCode_88:           false,
			EnumEpayPayOutCode_ErrorNoScopesCode_89:             false,
			EnumEpayPayOutCode_ErrorNoScopesCode_90:             false,
			EnumEpayPayOutCode_ErrorCheckTokenCode_91:           false,
			EnumEpayPayOutCode_Cardnumber14Code_92:              false,
			EnumEpayPayOutCode_ErrorGetCardCorporatCode_94:      false,
			EnumEpayPayOutCode_ErrorGetCountryByBINCode_95:      false,
			EnumEpayPayOutCode_InvaliddataCode_96:               false,
			EnumEpayPayOutCode_ErrorCheckTokenCode_97:           false,
			EnumEpayPayOutCode_ErrorNoScopesCode_98:             false,
			EnumEpayPayOutCode_ContractCode_99:                  false,
			EnumEpayPayOutCode_InvalidDateformatPleCode_111:     false,
			EnumEpayPayOutCode_ContractCode_112:                 false,
			EnumEpayPayOutCode_ExtcsvxlsxCode_113:               false,
			EnumEpayPayOutCode_ErrorGetStatementByDCode_114:     false,
			EnumEpayPayOutCode_ErrorGetStatementByOCode_115:     false,
			EnumEpayPayOutCode_ErrorGetStatementAirCode_116:     false,
			EnumEpayPayOutCode_ErrorGetStatementByTCode_117:     false,
			EnumEpayPayOutCode_ErrorCheckTokenCode_121:          false,
			EnumEpayPayOutCode_Code_122:                         false,
			EnumEpayPayOutCode_ErrorBindCode_123:                false,
			EnumEpayPayOutCode_Code_124:                         false,
			EnumEpayPayOutCode_Code_125:                         false,
			EnumEpayPayOutCode_Code_126:                         false,
			EnumEpayPayOutCode_Code_127:                         false,
			EnumEpayPayOutCode_ErrorEmptyMerchantIDCode_128:     false,
			EnumEpayPayOutCode_ErrorUnknownMerchantCode_129:     false,
			EnumEpayPayOutCode_ServerErrorCode_130:              false,
			EnumEpayPayOutCode_ErrorCheckTokenCode_131:          false,
			EnumEpayPayOutCode_ErrorInvalidScopeCode_132:        false,
			EnumEpayPayOutCode_ErrorBindCode_133:                false,
			EnumEpayPayOutCode_ErrorRequestPostFormCode_134:     false,
			EnumEpayPayOutCode_ErrorBodyIsEmptyCode_135:         false,
			EnumEpayPayOutCode_ErrorRequestIsNotSucCode_136:     false,
			EnumEpayPayOutCode_UnkwonErrorCode_137:              false,
			EnumEpayPayOutCode_SecretincorrecterrorCode_138:     false,
			EnumEpayPayOutCode_Code_139:                         false,
			EnumEpayPayOutCode_SecretincorrectCode_140:          false,
			EnumEpayPayOutCode_ErrorgetclientfromreCode_141:     false,
			EnumEpayPayOutCode_ErrorparseclientCode_142:         false,
			EnumEpayPayOutCode_CheckscopesCode_143:              false,
			EnumEpayPayOutCode_UseridpasswordCode_144:           false,
			EnumEpayPayOutCode_UserstatusCHANGEPASSCode_145:     false,
			EnumEpayPayOutCode_UsernotfoundCode_146:             false,
			EnumEpayPayOutCode_StatusCode_147:                   false,
			EnumEpayPayOutCode_Code_148:                         false,
			EnumEpayPayOutCode_UserstatusinvalidCode_149:        false,
			EnumEpayPayOutCode_StatusisnotdefinedCode_150:       false,
			EnumEpayPayOutCode_PASSWORDINCORRECTCode_151:        false,
			EnumEpayPayOutCode_GranttypeCode_152:                false,
			EnumEpayPayOutCode_RefreshTokenCode_153:             false,
			EnumEpayPayOutCode_Code_154:                         false,
			EnumEpayPayOutCode_ScopeCode_155:                    false,
			EnumEpayPayOutCode_RefreshtokenCode_156:             false,
			EnumEpayPayOutCode_GENERATEACCESSERRORCode_157:      false,
			EnumEpayPayOutCode_GENERATEACCESSERRORCode_158:      false,
			EnumEpayPayOutCode_EXTENSIONFIELDADDERRCode_159:     false,
			EnumEpayPayOutCode_EXTENSIONFIELDADDERRCode_160:     false,
			EnumEpayPayOutCode_Code_161:                         false,
			EnumEpayPayOutCode_Code_162:                         false,
			EnumEpayPayOutCode_GetUserByLoginerrorcCode_163:     false,
			EnumEpayPayOutCode_Code_164:                         false,
			EnumEpayPayOutCode_GetStatusByNameerrorCode_165:     false,
			EnumEpayPayOutCode_HashPassworderrorCode_166:        false,
			EnumEpayPayOutCode_SaveUsererrorCode_167:            false,
			EnumEpayPayOutCode_UpdateUsererrorCode_169:          false,
			EnumEpayPayOutCode_UpdateUsererrorCode_170:          false,
			EnumEpayPayOutCode_GetUserByLoginerrorcCode_171:     false,
			EnumEpayPayOutCode_Code_172:                         false,
			EnumEpayPayOutCode_Code_173:                         false,
			EnumEpayPayOutCode_ActionServiceGetDebiCode_178:     false,
			EnumEpayPayOutCode_ActionServiceGetTranCode_179:     false,
			EnumEpayPayOutCode_ReferenceEPAYCode_180:            false,
			EnumEpayPayOutCode_RedisCode_181:                    false,
			EnumEpayPayOutCode_Code_182:                         false,
			EnumEpayPayOutCode_Code_185:                         false,
			EnumEpayPayOutCode_ErrorUnmarshalCode_186:           false,
			EnumEpayPayOutCode_StartDateandEndDatesCode_193:     false,
			EnumEpayPayOutCode_EPayCode_194:                     false,
			EnumEpayPayOutCode_NoAuthorizeCode_197:              false,
			EnumEpayPayOutCode_BadtokenCode_198:                 false,
			EnumEpayPayOutCode_ParseerrorCode_199:               false,
			EnumEpayPayOutCode_PaymenterrorCode_200:             false,
			EnumEpayPayOutCode_Code_216:                         false,
			EnumEpayPayOutCode_ErrorBindJSONCannotuCode_224:     false,
			EnumEpayPayOutCode_LIKECode_225:                     false,
			EnumEpayPayOutCode_Code_226:                         false,
			EnumEpayPayOutCode_BETWEENCode_227:                  false,
			EnumEpayPayOutCode_Code_228:                         false,
			EnumEpayPayOutCode_Code_229:                         false,
			EnumEpayPayOutCode_Code_230:                         false,
			EnumEpayPayOutCode_Code_231:                         false,
			EnumEpayPayOutCode_Code_232:                         false,
			EnumEpayPayOutCode_ErrorCheckTokenCode_233:          false,
			EnumEpayPayOutCode_ErrorNoScopesCode_234:            false,
			EnumEpayPayOutCode_FromdateYYYYMMDDCode_235:         false,
			EnumEpayPayOutCode_TodateYYYYMMDDCode_236:           false,
			EnumEpayPayOutCode_ErrorCheckTokenCode_237:          false,
			EnumEpayPayOutCode_ErrorNoScopesCode_238:            false,
			EnumEpayPayOutCode_CannotunmarshalintosCode_239:     false,
			EnumEpayPayOutCode_Code_240:                         false,
			EnumEpayPayOutCode_Code_241:                         false,
			EnumEpayPayOutCode_Code_242:                         false,
			EnumEpayPayOutCode_IDCode_243:                       false,
			EnumEpayPayOutCode_MerchantIDCode_244:               false,
			EnumEpayPayOutCode_StartDateandEndDatesCode_245:     false,
			EnumEpayPayOutCode_Code_246:                         false,
			EnumEpayPayOutCode_NotemailCode_247:                 false,
			EnumEpayPayOutCode_JsonCode_248:                     false,
			EnumEpayPayOutCode_Code_249:                         false,
			EnumEpayPayOutCode_MsgValueCode_250:                 false,
			EnumEpayPayOutCode_Code_251:                         false,
			EnumEpayPayOutCode_Code_252:                         false,
			EnumEpayPayOutCode_Code_263:                         false,
			EnumEpayPayOutCode_Code_264:                         false,
			EnumEpayPayOutCode_Code_265:                         false,
			EnumEpayPayOutCode_SecretincorrecterrorCode_266:     false,
			EnumEpayPayOutCode_ScopenotinchangepassCode_267:     false,
			EnumEpayPayOutCode_UnsupportedscopeCode_268:         false,
			EnumEpayPayOutCode_BLOCKCode_269:                    false,
			EnumEpayPayOutCode_BLOCKCode_270:                    false,
			EnumEpayPayOutCode_UsernotsaveCode_271:              false,
			EnumEpayPayOutCode_UsernotsaveCode_272:              false,
			EnumEpayPayOutCode_FailedtogetstatusCode_273:        false,
			EnumEpayPayOutCode_UseralreadyexistCode_274:         false,
			EnumEpayPayOutCode_UseralreadyexistCode_275:         false,
			EnumEpayPayOutCode_IncorrectdataCode_276:            false,
			EnumEpayPayOutCode_IncorrectdataCode_277:            false,
			EnumEpayPayOutCode_NotemailCode_278:                 false,
			EnumEpayPayOutCode_NotemailCode_279:                 false,
			EnumEpayPayOutCode_Code_280:                         false,
			EnumEpayPayOutCode_TransactioncreateerrCode_281:     false,
			EnumEpayPayOutCode_Code_282:                         false,
			EnumEpayPayOutCode_Code_283:                         false,
			EnumEpayPayOutCode_TestMECode_285:                   false,
			EnumEpayPayOutCode_StatementCode_286:                false,
			EnumEpayPayOutCode_ErrorCheckTokenCode_287:          false,
			EnumEpayPayOutCode_ErrorNoScopesCode_288:            false,
			EnumEpayPayOutCode_TofromYYYYMMDDCode_289:           false,
			EnumEpayPayOutCode_Code_290:                         false,
			EnumEpayPayOutCode_FromDateandToDateshoCode_291:     false,
			EnumEpayPayOutCode_ERRORwhilecreatingreCode_292:     false,
			EnumEpayPayOutCode_Code_293:                         false,
			EnumEpayPayOutCode_RequiredclientcredenCode_294:     false,
			EnumEpayPayOutCode_IDIDCode_295:                     false,
			EnumEpayPayOutCode_ErrorUpdateStaffCode_296:         false,
			EnumEpayPayOutCode_StaffCode_297:                    false,
			EnumEpayPayOutCode_Code_298:                         false,
			EnumEpayPayOutCode_StaffCode_299:                    false,
			EnumEpayPayOutCode_BindErrorCode_300:                false,
			EnumEpayPayOutCode_UpdateStaffCode_301:              false,
			EnumEpayPayOutCode_MultipartFormCode_302:            false,
			EnumEpayPayOutCode_ExpireAtYYYYMMDDCode_303:         false,
			EnumEpayPayOutCode_ExpireAtYYYYMMDDCode_304:         false,
			EnumEpayPayOutCode_ExpireAtCode_305:                 false,
			EnumEpayPayOutCode_ExpireAtCode_306:                 false,
			EnumEpayPayOutCode_Code_307:                         false,
			EnumEpayPayOutCode_Code_308:                         false,
			EnumEpayPayOutCode_Code_309:                         false,
			EnumEpayPayOutCode_CommitErrorCode_310:              false,
			EnumEpayPayOutCode_BindJSONErrorCode_311:            false,
			EnumEpayPayOutCode_Code_312:                         false,
			EnumEpayPayOutCode_SearchtransactionerrCode_318:     false,
			EnumEpayPayOutCode_JsonCode_319:                     false,
			EnumEpayPayOutCode_Code_320:                         false,
			EnumEpayPayOutCode_Code_321:                         false,
			EnumEpayPayOutCode_Code_322:                         false,
			EnumEpayPayOutCode_FromdateYYYYMMDDCode_323:         false,
			EnumEpayPayOutCode_TodateYYYYMMDDCode_324:           false,
			EnumEpayPayOutCode_EPayCode_325:                     false,
			EnumEpayPayOutCode_ErrorgetMerchantinfoCode_326:     false,
			EnumEpayPayOutCode_Code_327:                         false,
			EnumEpayPayOutCode_Code_328:                         false,
			EnumEpayPayOutCode_InvoiceIDCode_329:                false,
			EnumEpayPayOutCode_TerminalCode_330:                 false,
			EnumEpayPayOutCode_CurrencyCode_331:                 false,
			EnumEpayPayOutCode_AmountCode_332:                   false,
			EnumEpayPayOutCode_Code_333:                         false,
			EnumEpayPayOutCode_ShopIDCode_334:                   false,
			EnumEpayPayOutCode_MerchantIDCode_335:               false,
			EnumEpayPayOutCode_ShopIDCode_336:                   false,
			EnumEpayPayOutCode_ErrorgetMerchantinfoCode_337:     false,
			EnumEpayPayOutCode_Code_338:                         false,
			EnumEpayPayOutCode_Code_339:                         false,
			EnumEpayPayOutCode_Code_340:                         false,
			EnumEpayPayOutCode_ContractCode_342:                 false,
			EnumEpayPayOutCode_GetShopByContractCode_343:        false,
			EnumEpayPayOutCode_Code_344:                         false,
			EnumEpayPayOutCode_MarshalerrorCode_345:             false,
			EnumEpayPayOutCode_Code_346:                         false,
			EnumEpayPayOutCode_Code_347:                         false,
			EnumEpayPayOutCode_Code_348:                         false,
			EnumEpayPayOutCode_Code_349:                         false,
			EnumEpayPayOutCode_Code_350:                         false,
			EnumEpayPayOutCode_Code_351:                         false,
			EnumEpayPayOutCode_Code_352:                         false,
			EnumEpayPayOutCode_TopCode_353:                      false,
			EnumEpayPayOutCode_Code_354:                         false,
			EnumEpayPayOutCode_EmailCode_355:                    false,
			EnumEpayPayOutCode_Code_356:                         false,
			EnumEpayPayOutCode_Code_357:                         false,
			EnumEpayPayOutCode_Code_358:                         false,
			EnumEpayPayOutCode_Code_359:                         false,
			EnumEpayPayOutCode_Code_360:                         false,
			EnumEpayPayOutCode_Code_361:                         false,
			EnumEpayPayOutCode_Code_362:                         false,
			EnumEpayPayOutCode_ParseerrorCode_363:               false,
			EnumEpayPayOutCode_ParseerrorCode_364:               false,
			EnumEpayPayOutCode_Code_365:                         false,
			EnumEpayPayOutCode_ParseerrorCode_366:               false,
			EnumEpayPayOutCode_ParseerrorCode_367:               false,
			EnumEpayPayOutCode_Code_368:                         false,
			EnumEpayPayOutCode_ParseerrorCode_369:               false,
			EnumEpayPayOutCode_ParseerrorCode_370:               false,
			EnumEpayPayOutCode_Code_371:                         false,
			EnumEpayPayOutCode_Code_372:                         false,
			EnumEpayPayOutCode_NoemailCode_373:                  false,
			EnumEpayPayOutCode_Code_374:                         false,
			EnumEpayPayOutCode_Code_375:                         false,
			EnumEpayPayOutCode_Code_376:                         false,
			EnumEpayPayOutCode_IncorrectformatdateCode_377:      false,
			EnumEpayPayOutCode_GetFileFiledoesnotexCode_378:     false,
			EnumEpayPayOutCode_Code_379:                         false,
			EnumEpayPayOutCode_Code_380:                         false,
			EnumEpayPayOutCode_Code_381:                         false,
			EnumEpayPayOutCode_Code_382:                         false,
			EnumEpayPayOutCode_Code_383:                         false,
			EnumEpayPayOutCode_EgatewayCode_384:                 false,
			EnumEpayPayOutCode_EgatewayCode_385:                 false,
			EnumEpayPayOutCode_EgatewayCode_386:                 false,
			EnumEpayPayOutCode_EgatewayCode_387:                 false,
			EnumEpayPayOutCode_EgatewayCode_388:                 false,
			EnumEpayPayOutCode_UserdeleteCode_389:               false,
			EnumEpayPayOutCode_ParseerrorCode_390:               false,
			EnumEpayPayOutCode_CallbacksendmessageeCode_391:     false,
			EnumEpayPayOutCode_Code_392:                         false,
			EnumEpayPayOutCode_Code_393:                         false,
			EnumEpayPayOutCode_Code_394:                         false,
			EnumEpayPayOutCode_Code_395:                         false,
			EnumEpayPayOutCode_Code_396:                         false,
			EnumEpayPayOutCode_MVisaCode_397:                    false,
			EnumEpayPayOutCode_ThreeDSecureCode_398:             false,
			EnumEpayPayOutCode_MVisaverificationCode_399:        false,
			EnumEpayPayOutCode_Code_400:                         false,
			EnumEpayPayOutCode_Code_401:                         false,
			EnumEpayPayOutCode_Code_402:                         false,
			EnumEpayPayOutCode_CarddataisnotrequireCode_403:     false,
			EnumEpayPayOutCode_Code_404:                         false,
			EnumEpayPayOutCode_Code_405:                         false,
			EnumEpayPayOutCode_Code_406:                         false,
			EnumEpayPayOutCode_Code_407:                         false,
			EnumEpayPayOutCode_Code_408:                         false,
			EnumEpayPayOutCode_Code_409:                         false,
			EnumEpayPayOutCode_MVISACode_410:                    false,
			EnumEpayPayOutCode_NoAuthorizeCode_411:              false,
			EnumEpayPayOutCode_BadtokenCode_412:                 false,
			EnumEpayPayOutCode_Code_413:                         false,
			EnumEpayPayOutCode_TimeouttransactionCode_504:       false,
			EnumEpayPayOutCode_ExceededattemptsCode_505:         false,
			EnumEpayPayOutCode_Code_506:                         false,
			EnumEpayPayOutCode_Code_507:                         false,
			EnumEpayPayOutCode_Code_508:                         false,
			EnumEpayPayOutCode_Code_509:                         false,
			EnumEpayPayOutCode_RabbitCode_510:                   false,
			EnumEpayPayOutCode_Code_512:                         false,
			EnumEpayPayOutCode_PostlinkCode_513:                 false,
			EnumEpayPayOutCode_Code_514:                         false,
			EnumEpayPayOutCode_Code_515:                         false,
			EnumEpayPayOutCode_Code_516:                         false,
			EnumEpayPayOutCode_TherewasnoattempttopCode_517:     false,
			EnumEpayPayOutCode_IpCode_518:                       false,
			EnumEpayPayOutCode_InvoiceIDCode_520:                false,
			EnumEpayPayOutCode_TerminalIDCode_511:               false,
			EnumEpayPayOutCode_BinCode_522:                      false,
			EnumEpayPayOutCode_EGWCode_546:                      false,
			EnumEpayPayOutCode_EGWHTTPCODE200Code_547:           false,
			EnumEpayPayOutCode_Code_559:                         false,
			EnumEpayPayOutCode_EGWCode_560:                      false,
			EnumEpayPayOutCode_EGWHTTPCODE200Code_561:           false,
			EnumEpayPayOutCode_MDCode_529:                       false,
			EnumEpayPayOutCode_EGWCode_530:                      false,
			EnumEpayPayOutCode_Code_570:                         false,
			EnumEpayPayOutCode_EGWCode_571:                      false,
			EnumEpayPayOutCode_EGWHTTPCODE200Code_533:           false,
			EnumEpayPayOutCode_Code_534:                         false,
			EnumEpayPayOutCode_Code_535:                         false,
			EnumEpayPayOutCode_MerchantCode_536:                 false,
			EnumEpayPayOutCode_MerchantIsActivefalsCode_537:     false,
			EnumEpayPayOutCode_Code_538:                         false,
			EnumEpayPayOutCode_MerchantCode_539:                 false,
			EnumEpayPayOutCode_ScopenotfoundcheckscCode_540:     false,
			EnumEpayPayOutCode_ErrorparameternameCode_541:       false,
			EnumEpayPayOutCode_UnauthorizedChecktokCode_542:     false,
			EnumEpayPayOutCode_ErrorInvalidScopeCode_543:        false,
			EnumEpayPayOutCode_ErrorBindCode_544:                false,
			EnumEpayPayOutCode_UnkwonErrorCode_548:              false,
			EnumEpayPayOutCode_TransactionalreadycaCode_549:     false,
			EnumEpayPayOutCode_RequesttoXLSServerfaCode_550:     false,
			EnumEpayPayOutCode_RequesttoXLSServerreCode_551:     false,
			EnumEpayPayOutCode_InvalidbonusamountCode_552:       false,
			EnumEpayPayOutCode_InvalidinputdataCode_553:         false,
			EnumEpayPayOutCode_TerminalconfiguratioCode_554:     false,
			EnumEpayPayOutCode_UnabletogetGETrequesCode_555:     false,
			EnumEpayPayOutCode_RequesttoapixlsfaileCode_556:     false,
			EnumEpayPayOutCode_RequesttoapixlsreturCode_557:     false,
			EnumEpayPayOutCode_GobonusconvertiontofCode_558:     false,
			EnumEpayPayOutCode_Code_563:                         false,
			EnumEpayPayOutCode_TerminalIDCode_564:               false,
			EnumEpayPayOutCode_RequesttogetcardinfoCode_565:     false,
			EnumEpayPayOutCode_RequesttogetcardinfoCode_566:     false,
			EnumEpayPayOutCode_CardnotfoundCode_567:             false,
			EnumEpayPayOutCode_CardownernotfoundCode_568:        false,
			EnumEpayPayOutCode_Code_569:                         false,
			EnumEpayPayOutCode_Code_572:                         false,
			EnumEpayPayOutCode_RequesttoapixlsfaileCode_573:     false,
			EnumEpayPayOutCode_RequesttoapixlsreturCode_574:     false,
			EnumEpayPayOutCode_UnauthorizedChecktokCode_576:     false,
			EnumEpayPayOutCode_CannotcancelxlstransCode_578:     false,
			EnumEpayPayOutCode_CannotsendtransactioCode_579:     false,
			EnumEpayPayOutCode_CannotsendcancelxlstCode_580:     false,
			EnumEpayPayOutCode_CannotcreaterabbitinCode_581:     false,
			EnumEpayPayOutCode_ChangePaymentCode_582:            false,
			EnumEpayPayOutCode_Code_583:                         false,
			EnumEpayPayOutCode_XlsCode_584:                      false,
			EnumEpayPayOutCode_GoCode_585:                       false,
			EnumEpayPayOutCode_Code_586:                         false,
			EnumEpayPayOutCode_Code_587:                         false,
			EnumEpayPayOutCode_Code_588:                         false,
			EnumEpayPayOutCode_Code_589:                         false,
			EnumEpayPayOutCode_Code_590:                         false,
			EnumEpayPayOutCode_Code_591:                         false,
			EnumEpayPayOutCode_Code_592:                         false,
			EnumEpayPayOutCode_Code_593:                         false,
			EnumEpayPayOutCode_Code_594:                         false,
			EnumEpayPayOutCode_Code_595:                         false,
			EnumEpayPayOutCode_Epay1documentCode_596:            false,
			EnumEpayPayOutCode_CrtificateIDCode_597:             false,
			EnumEpayPayOutCode_Code_598:                         false,
			EnumEpayPayOutCode_Code_599:                         false,
			EnumEpayPayOutCode_Code_600:                         false,
			EnumEpayPayOutCode_Code_601:                         false,
			EnumEpayPayOutCode_Code_602:                         false,
			EnumEpayPayOutCode_Code_603:                         false,
			EnumEpayPayOutCode_Code_604:                         false,
			EnumEpayPayOutCode_Code_605:                         false,
			EnumEpayPayOutCode_Code_606:                         false,
			EnumEpayPayOutCode_Code_607:                         false,
			EnumEpayPayOutCode_Code_608:                         false,
			EnumEpayPayOutCode_Code_609:                         false,
			EnumEpayPayOutCode_Code_610:                         false,
			EnumEpayPayOutCode_Code_611:                         false,
			EnumEpayPayOutCode_Code_612:                         false,
			EnumEpayPayOutCode_Code_613:                         false,
			EnumEpayPayOutCode_Code_614:                         false,
			EnumEpayPayOutCode_Code_615:                         false,
			EnumEpayPayOutCode_Code_616:                         false,
			EnumEpayPayOutCode_InvoiceIDCode_617:                false,
			EnumEpayPayOutCode_TerminalCode_618:                 false,
			EnumEpayPayOutCode_TerminalCode_619:                 false,
			EnumEpayPayOutCode_CurrencyCode_620:                 false,
			EnumEpayPayOutCode_AmountCode_621:                   false,
			EnumEpayPayOutCode_AmountCode_622:                   false,
			EnumEpayPayOutCode_AmountCode_623:                   false,
			EnumEpayPayOutCode_Code_624:                         false,
			EnumEpayPayOutCode_Code_625:                         false,
			EnumEpayPayOutCode_Code_626:                         false,
			EnumEpayPayOutCode_Code_627:                         false,
			EnumEpayPayOutCode_Code_628:                         false,
			EnumEpayPayOutCode_Code_629:                         false,
			EnumEpayPayOutCode_Code_630:                         false,
			EnumEpayPayOutCode_Code_631:                         false,
			EnumEpayPayOutCode_Code_632:                         false,
			EnumEpayPayOutCode_Code_633:                         false,
			EnumEpayPayOutCode_Code_634:                         false,
			EnumEpayPayOutCode_EgatewayCode_635:                 false,
			EnumEpayPayOutCode_EgatewayCode_636:                 false,
			EnumEpayPayOutCode_EgatewayCode_637:                 false,
			EnumEpayPayOutCode_Code_638:                         false,
			EnumEpayPayOutCode_Code_639:                         false,
			EnumEpayPayOutCode_Code_640:                         false,
			EnumEpayPayOutCode_Code_641:                         false,
			EnumEpayPayOutCode_Code_642:                         false,
			EnumEpayPayOutCode_CallbacksendmessageeCode_643:     false,
			EnumEpayPayOutCode_Code_644:                         false,
			EnumEpayPayOutCode_Code_645:                         false,
			EnumEpayPayOutCode_Code_646:                         false,
			EnumEpayPayOutCode_Code_647:                         false,
			EnumEpayPayOutCode_Code_648:                         false,
			EnumEpayPayOutCode_Code_649:                         false,
			EnumEpayPayOutCode_Code_650:                         false,
			EnumEpayPayOutCode_Code_651:                         false,
			EnumEpayPayOutCode_Code_652:                         false,
			EnumEpayPayOutCode_Code_653:                         false,
			EnumEpayPayOutCode_Code_654:                         false,
			EnumEpayPayOutCode_Code_655:                         false,
			EnumEpayPayOutCode_Code_656:                         false,
			EnumEpayPayOutCode_Code_657:                         false,
			EnumEpayPayOutCode_Code_658:                         false,
			EnumEpayPayOutCode_Code_659:                         false,
			EnumEpayPayOutCode_Code_660:                         false,
			EnumEpayPayOutCode_Code_661:                         false,
			EnumEpayPayOutCode_Code_662:                         false,
			EnumEpayPayOutCode_Code_663:                         false,
			EnumEpayPayOutCode_Code_664:                         false,
			EnumEpayPayOutCode_Code_665:                         false,
			EnumEpayPayOutCode_Code_666:                         false,
			EnumEpayPayOutCode_Code_667:                         false,
			EnumEpayPayOutCode_Code_668:                         false,
			EnumEpayPayOutCode_Code_669:                         false,
			EnumEpayPayOutCode_Code_670:                         false,
			EnumEpayPayOutCode_Code_671:                         false,
			EnumEpayPayOutCode_Code_672:                         false,
			EnumEpayPayOutCode_Code_673:                         false,
			EnumEpayPayOutCode_EGWHTTPCODE200Code_674:           false,
			EnumEpayPayOutCode_Code_675:                         false,
			EnumEpayPayOutCode_Code_676:                         false,
			EnumEpayPayOutCode_Code_677:                         false,
			EnumEpayPayOutCode_Code_678:                         false,
			EnumEpayPayOutCode_Code_679:                         false,
			EnumEpayPayOutCode_RequesttogetcardinfoCode_680:     false,
			EnumEpayPayOutCode_RequesttogetcardinfoCode_681:     false,
			EnumEpayPayOutCode_XlsCode_683:                      false,
			EnumEpayPayOutCode_XlsCode_684:                      false,
			EnumEpayPayOutCode_Code_685:                         false,
			EnumEpayPayOutCode_TerminalconfiguratioCode_686:     false,
			EnumEpayPayOutCode_RequesttoXLSServerfaCode_687:     false,
			EnumEpayPayOutCode_RequesttoXLSServerfaCode_688:     false,
			EnumEpayPayOutCode_Code_689:                         false,
			EnumEpayPayOutCode_Code_690:                         false,
			EnumEpayPayOutCode_Code_691:                         false,
			EnumEpayPayOutCode_JsonCode_692:                     false,
			EnumEpayPayOutCode_Code_693:                         false,
			EnumEpayPayOutCode_Code_694:                         false,
			EnumEpayPayOutCode_StatementCode_695:                false,
			EnumEpayPayOutCode_StatementCode_696:                false,
			EnumEpayPayOutCode_StatementCode_697:                false,
			EnumEpayPayOutCode_StatementCode_698:                false,
			EnumEpayPayOutCode_StatementCode_699:                false,
			EnumEpayPayOutCode_TofromYYYYMMDDCode_700:           false,
			EnumEpayPayOutCode_TofromYYYYMMDDCode_701:           false,
			EnumEpayPayOutCode_TofromYYYYMMDDCode_702:           false,
			EnumEpayPayOutCode_Code_703:                         false,
			EnumEpayPayOutCode_Code_704:                         false,
			EnumEpayPayOutCode_Code_705:                         false,
			EnumEpayPayOutCode_Code_706:                         false,
			EnumEpayPayOutCode_Code_707:                         false,
			EnumEpayPayOutCode_StaffCode_708:                    false,
			EnumEpayPayOutCode_StaffCode_709:                    false,
			EnumEpayPayOutCode_StaffCode_710:                    false,
			EnumEpayPayOutCode_Code_711:                         false,
			EnumEpayPayOutCode_Code_712:                         false,
			EnumEpayPayOutCode_Code_713:                         false,
			EnumEpayPayOutCode_Code_714:                         false,
			EnumEpayPayOutCode_Code_715:                         false,
			EnumEpayPayOutCode_Code_716:                         false,
			EnumEpayPayOutCode_Code_717:                         false,
			EnumEpayPayOutCode_Code_718:                         false,
			EnumEpayPayOutCode_Code_719:                         false,
			EnumEpayPayOutCode_ErrorinservicingthecCode_720:     false,
			EnumEpayPayOutCode_ErrorinservicingthecCode_721:     false,
			EnumEpayPayOutCode_ErrorinservicingthecCode_722:     false,
			EnumEpayPayOutCode_ErrorinservicingthecCode_723:     false,
			EnumEpayPayOutCode_ErrorinservicingthecCode_724:     false,
			EnumEpayPayOutCode_ErrorinservicingthecCode_725:     false,
			EnumEpayPayOutCode_ErrorinservicingthecCode_726:     false,
			EnumEpayPayOutCode_ErrorinservicingthecCode_727:     false,
			EnumEpayPayOutCode_NoAuthorizeCode_728:              false,
			EnumEpayPayOutCode_BadtokenCode_729:                 false,
			EnumEpayPayOutCode_Code_730:                         false,
			EnumEpayPayOutCode_InvalidrequestinputCode_731:      false,
			EnumEpayPayOutCode_Code_732:                         false,
			EnumEpayPayOutCode_Code_733:                         false,
			EnumEpayPayOutCode_Code_734:                         false,
			EnumEpayPayOutCode_Code_735:                         false,
			EnumEpayPayOutCode_Code_736:                         false,
			EnumEpayPayOutCode_IdCode_737:                       false,
			EnumEpayPayOutCode_Code_738:                         false,
			EnumEpayPayOutCode_Code_739:                         false,
			EnumEpayPayOutCode_Code_740:                         false,
			EnumEpayPayOutCode_Code_741:                         false,
			EnumEpayPayOutCode_Code_742:                         false,
			EnumEpayPayOutCode_Code_743:                         false,
			EnumEpayPayOutCode_Code_744:                         false,
			EnumEpayPayOutCode_Code_745:                         false,
			EnumEpayPayOutCode_Code_746:                         false,
			EnumEpayPayOutCode_Code_747:                         false,
			EnumEpayPayOutCode_Code_748:                         false,
			EnumEpayPayOutCode_Code_749:                         false,
			EnumEpayPayOutCode_Code_750:                         false,
			EnumEpayPayOutCode_Code_751:                         false,
			EnumEpayPayOutCode_Code_752:                         false,
			EnumEpayPayOutCode_Code_753:                         false,
			EnumEpayPayOutCode_ApimakePaymentscorerCode_754:     false,
			EnumEpayPayOutCode_Code_756:                         false,
			EnumEpayPayOutCode_Code_757:                         false,
			EnumEpayPayOutCode_MerchantCode_758:                 false,
			EnumEpayPayOutCode_Code_759:                         false,
			EnumEpayPayOutCode_Code_760:                         false,
			EnumEpayPayOutCode_Code_761:                         false,
			EnumEpayPayOutCode_Code_762:                         false,
			EnumEpayPayOutCode_Code_765:                         false,
			EnumEpayPayOutCode_EgatewayCode_766:                 false,
			EnumEpayPayOutCode_Code_767:                         false,
			EnumEpayPayOutCode_Code_768:                         false,
			EnumEpayPayOutCode_Code_769:                         false,
			EnumEpayPayOutCode_Code_770:                         false,
			EnumEpayPayOutCode_Code_771:                         false,
			EnumEpayPayOutCode_Code_772:                         false,
			EnumEpayPayOutCode_Code_773:                         false,
			EnumEpayPayOutCode_Code_774:                         false,
			EnumEpayPayOutCode_Code_775:                         false,
			EnumEpayPayOutCode_InvoiceIDCode_776:                false,
			EnumEpayPayOutCode_TerminalCode_777:                 false,
			EnumEpayPayOutCode_CurrencyCode_778:                 false,
			EnumEpayPayOutCode_AmountCode_779:                   false,
			EnumEpayPayOutCode_AmountCode_780:                   false,
			EnumEpayPayOutCode_CorepaymentCode_781:              false,
			EnumEpayPayOutCode_Code_782:                         false,
			EnumEpayPayOutCode_Code_783:                         false,
			EnumEpayPayOutCode_Code_784:                         false,
			EnumEpayPayOutCode_IdCode_785:                       false,
			EnumEpayPayOutCode_TerminalIdCode_786:               false,
			EnumEpayPayOutCode_Code_787:                         false,
			EnumEpayPayOutCode_Code_789:                         false,
			EnumEpayPayOutCode_Code_790:                         false,
			EnumEpayPayOutCode_Code_791:                         false,
			EnumEpayPayOutCode_Code_792:                         false,
			EnumEpayPayOutCode_Code_793:                         false,
			EnumEpayPayOutCode_Code_795:                         false,
			EnumEpayPayOutCode_CorerecurrentCode_796:            false,
			EnumEpayPayOutCode_Code_797:                         false,
			EnumEpayPayOutCode_Code_798:                         false,
			EnumEpayPayOutCode_Code_799:                         false,
			EnumEpayPayOutCode_Code_800:                         false,
			EnumEpayPayOutCode_Code_801:                         false,
			EnumEpayPayOutCode_Code_802:                         false,
			EnumEpayPayOutCode_Code_803:                         false,
			EnumEpayPayOutCode_Code_804:                         false,
			EnumEpayPayOutCode_Code_805:                         false,
			EnumEpayPayOutCode_PublicIDCode_806:                 false,
			EnumEpayPayOutCode_Code_807:                         false,
			EnumEpayPayOutCode_Code_808:                         false,
			EnumEpayPayOutCode_Code_809:                         false,
			EnumEpayPayOutCode_Code_810:                         false,
			EnumEpayPayOutCode_Code_811:                         false,
			EnumEpayPayOutCode_Code_812:                         false,
			EnumEpayPayOutCode_Code_813:                         false,
			EnumEpayPayOutCode_Code_814:                         false,
			EnumEpayPayOutCode_Code_815:                         false,
			EnumEpayPayOutCode_Code_816:                         false,
			EnumEpayPayOutCode_Code_817:                         false,
			EnumEpayPayOutCode_Code_818:                         false,
			EnumEpayPayOutCode_Code_819:                         false,
			EnumEpayPayOutCode_Code_820:                         false,
			EnumEpayPayOutCode_Code_821:                         false,
			EnumEpayPayOutCode_Code_822:                         false,
			EnumEpayPayOutCode_Code_823:                         false,
			EnumEpayPayOutCode_Code_824:                         false,
			EnumEpayPayOutCode_Code_825:                         false,
			EnumEpayPayOutCode_Code_826:                         false,
			EnumEpayPayOutCode_Code_827:                         false,
			EnumEpayPayOutCode_Code_828:                         false,
			EnumEpayPayOutCode_Code_829:                         false,
			EnumEpayPayOutCode_Code_830:                         false,
			EnumEpayPayOutCode_Code_831:                         false,
			EnumEpayPayOutCode_Code_832:                         false,
			EnumEpayPayOutCode_Code_833:                         false,
			EnumEpayPayOutCode_Code_834:                         false,
			EnumEpayPayOutCode_Code_835:                         false,
			EnumEpayPayOutCode_Code_836:                         false,
			EnumEpayPayOutCode_Code_837:                         false,
			EnumEpayPayOutCode_Code_838:                         false,
			EnumEpayPayOutCode_Code_839:                         false,
			EnumEpayPayOutCode_Code_840:                         false,
			EnumEpayPayOutCode_Code_841:                         false,
			EnumEpayPayOutCode_Code_842:                         false,
			EnumEpayPayOutCode_Code_843:                         false,
			EnumEpayPayOutCode_Code_844:                         false,
			EnumEpayPayOutCode_Code_845:                         false,
			EnumEpayPayOutCode_PublicIdCode_846:                 false,
			EnumEpayPayOutCode_MerchantAPICode_847:              false,
			EnumEpayPayOutCode_Code_848:                         false,
			EnumEpayPayOutCode_Code_849:                         false,
			EnumEpayPayOutCode_Code_850:                         false,
			EnumEpayPayOutCode_StaffInviteRequestCode_851:       false,
			EnumEpayPayOutCode_PublicIdCode_852:                 false,
			EnumEpayPayOutCode_Code_853:                         false,
			EnumEpayPayOutCode_Code_854:                         false,
			EnumEpayPayOutCode_PublicIdCode_855:                 false,
			EnumEpayPayOutCode_PublicIdCode_856:                 false,
			EnumEpayPayOutCode_Code_857:                         false,
			EnumEpayPayOutCode_PublicIdCode_858:                 false,
			EnumEpayPayOutCode_Code_859:                         false,
			EnumEpayPayOutCode_Code_860:                         false,
			EnumEpayPayOutCode_Code_861:                         false,
			EnumEpayPayOutCode_Code_862:                         false,
			EnumEpayPayOutCode_Code_863:                         false,
			EnumEpayPayOutCode_Code_864:                         false,
			EnumEpayPayOutCode_Code_865:                         false,
			EnumEpayPayOutCode_OauthCode_866:                    false,
			EnumEpayPayOutCode_Code_867:                         false,
			EnumEpayPayOutCode_Code_868:                         false,
			EnumEpayPayOutCode_Code_869:                         false,
			EnumEpayPayOutCode_Code_870:                         false,
			EnumEpayPayOutCode_Code_871:                         false,
			EnumEpayPayOutCode_Code_872:                         false,
			EnumEpayPayOutCode_OauthCode_873:                    false,
			EnumEpayPayOutCode_Code_874:                         false,
			EnumEpayPayOutCode_OauthCode_875:                    false,
			EnumEpayPayOutCode_Code_876:                         false,
			EnumEpayPayOutCode_MDCode_877:                       false,
			EnumEpayPayOutCode_Code_878:                         false,
			EnumEpayPayOutCode_Code_879:                         false,
			EnumEpayPayOutCode_Code_880:                         false,
			EnumEpayPayOutCode_Code_881:                         false,
			EnumEpayPayOutCode_EGWCode_882:                      false,
			EnumEpayPayOutCode_RedisCode_883:                    false,
			EnumEpayPayOutCode_RedisCode_884:                    false,
			EnumEpayPayOutCode_RedisCode_885:                    false,
			EnumEpayPayOutCode_RedisCode_886:                    false,
			EnumEpayPayOutCode_Code_887:                         false,
			EnumEpayPayOutCode_AclapiCode_888:                   false,
			EnumEpayPayOutCode_Code_889:                         false,
			EnumEpayPayOutCode_MerchantapiCode_890:              false,
			EnumEpayPayOutCode_Code_891:                         false,
			EnumEpayPayOutCode_Code_892:                         false,
			EnumEpayPayOutCode_MerchantapiCode_893:              false,
			EnumEpayPayOutCode_Code_894:                         false,
			EnumEpayPayOutCode_CorepaymentCode_895:              false,
			EnumEpayPayOutCode_ConfirmCode_896:                  false,
			EnumEpayPayOutCode_Code_897:                         false,
			EnumEpayPayOutCode_PublicIDCode_898:                 false,
			EnumEpayPayOutCode_Code_899:                         false,
			EnumEpayPayOutCode_Code_900:                         false,
			EnumEpayPayOutCode_Code_901:                         false,
			EnumEpayPayOutCode_PublicIDCode_902:                 false,
			EnumEpayPayOutCode_Code_903:                         false,
			EnumEpayPayOutCode_Code_904:                         false,
			EnumEpayPayOutCode_Code_905:                         false,
			EnumEpayPayOutCode_Code_906:                         false,
			EnumEpayPayOutCode_Code_907:                         false,
			EnumEpayPayOutCode_Code_908:                         false,
			EnumEpayPayOutCode_MerchantapiCode_909:              false,
			EnumEpayPayOutCode_MerchantapiCode_910:              false,
			EnumEpayPayOutCode_Code_911:                         false,
			EnumEpayPayOutCode_Code_912:                         false,
			EnumEpayPayOutCode_Code_913:                         false,
			EnumEpayPayOutCode_Code_914:                         false,
			EnumEpayPayOutCode_Code_915:                         false,
			EnumEpayPayOutCode_Code_916:                         false,
			EnumEpayPayOutCode_Code_917:                         false,
			EnumEpayPayOutCode_Code_918:                         false,
			EnumEpayPayOutCode_Code_919:                         false,
			EnumEpayPayOutCode_Code_920:                         false,
			EnumEpayPayOutCode_Code_921:                         false,
			EnumEpayPayOutCode_Code_922:                         false,
			EnumEpayPayOutCode_Code_923:                         false,
			EnumEpayPayOutCode_Code_924:                         false,
			EnumEpayPayOutCode_Code_925:                         false,
			EnumEpayPayOutCode_Code_926:                         false,
			EnumEpayPayOutCode_Code_927:                         false,
			EnumEpayPayOutCode_Code_928:                         false,
			EnumEpayPayOutCode_PublicIDCode_929:                 false,
			EnumEpayPayOutCode_Code_930:                         false,
			EnumEpayPayOutCode_Code_931:                         false,
			EnumEpayPayOutCode_ScopesCode_932:                   false,
			EnumEpayPayOutCode_Code_933:                         false,
			EnumEpayPayOutCode_Code_934:                         false,
			EnumEpayPayOutCode_Code_935:                         false,
			EnumEpayPayOutCode_Code_937:                         false,
			EnumEpayPayOutCode_Code_938:                         false,
			EnumEpayPayOutCode_Code_939:                         false,
			EnumEpayPayOutCode_Code_940:                         false,
			EnumEpayPayOutCode_Code_941:                         false,
			EnumEpayPayOutCode_Code_942:                         false,
			EnumEpayPayOutCode_Code_943:                         false,
			EnumEpayPayOutCode_Code_944:                         false,
			EnumEpayPayOutCode_PublicIDCode_945:                 false,
			EnumEpayPayOutCode_Code_946:                         false,
			EnumEpayPayOutCode_Code_947:                         false,
			EnumEpayPayOutCode_Code_948:                         false,
			EnumEpayPayOutCode_Code_949:                         false,
			EnumEpayPayOutCode_Code_950:                         false,
			EnumEpayPayOutCode_PublicIdCode_951:                 false,
			EnumEpayPayOutCode_Code_952:                         false,
			EnumEpayPayOutCode_Code_953:                         false,
			EnumEpayPayOutCode_Code_954:                         false,
			EnumEpayPayOutCode_Code_955:                         false,
			EnumEpayPayOutCode_Code_956:                         false,
			EnumEpayPayOutCode_Code_957:                         false,
			EnumEpayPayOutCode_Code_958:                         false,
			EnumEpayPayOutCode_Code_959:                         false,
			EnumEpayPayOutCode_Code_960:                         false,
			EnumEpayPayOutCode_Code_961:                         false,
			EnumEpayPayOutCode_Code_962:                         false,
			EnumEpayPayOutCode_AuthorizedRequestJSOCode_963:     false,
			EnumEpayPayOutCode_ClientsecretCode_964:             false,
			EnumEpayPayOutCode_ClientidclientsecretCode_965:     false,
			EnumEpayPayOutCode_MerchantIDclientsecrCode_966:     false,
			EnumEpayPayOutCode_ClientsecretclientIDCode_967:     false,
			EnumEpayPayOutCode_EmailclientIDclientsCode_968:     false,
			EnumEpayPayOutCode_Code_969:                         false,
			EnumEpayPayOutCode_Code_970:                         false,
			EnumEpayPayOutCode_Code_971:                         false,
			EnumEpayPayOutCode_Code_972:                         false,
			EnumEpayPayOutCode_IDCode_973:                       false,
			EnumEpayPayOutCode_Code_974:                         false,
			EnumEpayPayOutCode_Code_975:                         false,
			EnumEpayPayOutCode_UserclientIDclientseCode_976:     false,
			EnumEpayPayOutCode_AuthorizedRequestJSOCode_977:     false,
			EnumEpayPayOutCode_JsonMarshalsendEmailCode_978:     false,
			EnumEpayPayOutCode_Code_979:                         false,
			EnumEpayPayOutCode_FormFileCode_980:                 false,
			EnumEpayPayOutCode_Code_981:                         false,
			EnumEpayPayOutCode_Code_982:                         false,
			EnumEpayPayOutCode_Code_983:                         false,
			EnumEpayPayOutCode_Code_984:                         false,
			EnumEpayPayOutCode_Code_985:                         false,
			EnumEpayPayOutCode_Code_986:                         false,
			EnumEpayPayOutCode_Code_987:                         false,
			EnumEpayPayOutCode_Code_988:                         false,
			EnumEpayPayOutCode_Code_989:                         false,
			EnumEpayPayOutCode_Code_990:                         false,
			EnumEpayPayOutCode_Code_991:                         false,
			EnumEpayPayOutCode_Code_992:                         false,
			EnumEpayPayOutCode_ServiceCode_993:                  false,
			EnumEpayPayOutCode_ShouldBindJSONservicCode_994:     false,
			EnumEpayPayOutCode_AuthorizedRequestJSOCode_995:     false,
			EnumEpayPayOutCode_AuthorizedRequestJSOCode_996:     false,
			EnumEpayPayOutCode_UseruserusernameservCode_997:     false,
			EnumEpayPayOutCode_MerchantserviceCode_999:          false,
			EnumEpayPayOutCode_NamewebserviceCode_1000:          false,
			EnumEpayPayOutCode_ContactTypenameserviCode_1001:    false,
			EnumEpayPayOutCode_PublicIdCode_1002:                false,
			EnumEpayPayOutCode_Code_1003:                        false,
			EnumEpayPayOutCode_Code_1004:                        false,
			EnumEpayPayOutCode_Code_1005:                        false,
			EnumEpayPayOutCode_OauthCode_1006:                   false,
			EnumEpayPayOutCode_Code_1007:                        false,
			EnumEpayPayOutCode_Code_1008:                        false,
			EnumEpayPayOutCode_Code_1009:                        false,
			EnumEpayPayOutCode_IdserviceCode_1010:               false,
			EnumEpayPayOutCode_RSAserviceCode_1011:              false,
			EnumEpayPayOutCode_ServiceCode_1012:                 false,
			EnumEpayPayOutCode_Codebase64stringbaseCode_1013:    false,
			EnumEpayPayOutCode_XmlUnmarshalserviceCode_1014:     false,
			EnumEpayPayOutCode_Codebase64stringbaseCode_1015:    false,
			EnumEpayPayOutCode_XmlMarshalserviceCode_1016:       false,
			EnumEpayPayOutCode_OAUTHCode_1017:                   false,
			EnumEpayPayOutCode_ServiceCode_1018:                 false,
			EnumEpayPayOutCode_ShopidCode_1019:                  false,
			EnumEpayPayOutCode_JsonserviceCode_1020:             false,
			EnumEpayPayOutCode_AuthserviceCode_1021:             false,
			EnumEpayPayOutCode_DeviceIDauthserviceCode_1022:     false,
			EnumEpayPayOutCode_ServiceCode_1023:                 false,
			EnumEpayPayOutCode_IdserviceCode_1024:               false,
			EnumEpayPayOutCode_BindXMLwebserviceCode_1025:       false,
			EnumEpayPayOutCode_AuthorizedRequestJSOCode_1026:    false,
			EnumEpayPayOutCode_BindclientIDpostLinkCode_1027:    false,
			EnumEpayPayOutCode_ClientIDpostLinkInfoCode_1028:    false,
			EnumEpayPayOutCode_MerchantIDpostLinkInCode_1029:    false,
			EnumEpayPayOutCode_PostlinkrabbitservicCode_1030:    false,
			EnumEpayPayOutCode_TerminalidpostLinkInCode_1031:    false,
			EnumEpayPayOutCode_CBindpostLinkInfoposCode_1032:    false,
			EnumEpayPayOutCode_JsonMarshalpostlinkICode_1033:    false,
			EnumEpayPayOutCode_ClientIDclientIDservCode_1034:    false,
			EnumEpayPayOutCode_ShopmerchantIDclientCode_1035:    false,
			EnumEpayPayOutCode_ShopMerchantIDclientCode_1036:    false,
			EnumEpayPayOutCode_BindinvoicewebservicCode_1037:    false,
			EnumEpayPayOutCode_ExpirePeriodinvoicesCode_1038:    false,
			EnumEpayPayOutCode_PostlinkRabbitservicCode_1039:    false,
			EnumEpayPayOutCode_ServiceCode_1040:                 false,
			EnumEpayPayOutCode_JsonserviceCode_1041:             false,
			EnumEpayPayOutCode_EgwCode_1042:                     false,
			EnumEpayPayOutCode_EmailclientsecretserCode_1043:    false,
			EnumEpayPayOutCode_EmailwebserviceCode_1044:         false,
			EnumEpayPayOutCode_AuthorizedRequestJSOCode_1045:    false,
			EnumEpayPayOutCode_JsonMarshalmerchantoCode_1046:    false,
			EnumEpayPayOutCode_JsonMarshalmerchantoCode_1047:    false,
			EnumEpayPayOutCode_BindJSONmerchantwebsCode_1048:    false,
			EnumEpayPayOutCode_ClientidserviceCode_1049:         false,
			EnumEpayPayOutCode_ClientScopesclientIDCode_1050:    false,
			EnumEpayPayOutCode_UserloginserviceCode_1051:        false,
			EnumEpayPayOutCode_ClientclientscopesusCode_1052:    false,
			EnumEpayPayOutCode_BindJSONinvoiceCode_1053:         false,
			EnumEpayPayOutCode_InvoiceCode_1054:                 false,
			EnumEpayPayOutCode_XMLway4Code_1055:                 false,
			EnumEpayPayOutCode_InvoiceCode_1056:                 false,
			EnumEpayPayOutCode_Code_1057:                        false,
			EnumEpayPayOutCode_Code_1058:                        false,
			EnumEpayPayOutCode_Code_1059:                        false,
			EnumEpayPayOutCode_CoreinvoiceCode_1060:             false,
			EnumEpayPayOutCode_BindJSONinvoiceInfoCode_1061:     false,
			EnumEpayPayOutCode_Code_1062:                        false,
			EnumEpayPayOutCode_Code_1063:                        false,
			EnumEpayPayOutCode_ScannererrCode_1064:              false,
			EnumEpayPayOutCode_BindCode_1065:                    false,
			EnumEpayPayOutCode_ShopIdCode_1066:                  false,
			EnumEpayPayOutCode_InvoiceLinkCode_1067:             false,
			EnumEpayPayOutCode_MarshalinvoicelinkCode_1068:      false,
			EnumEpayPayOutCode_InvoiceLinkCode_1069:             false,
			EnumEpayPayOutCode_InvoiceLinkCode_1070:             false,
			EnumEpayPayOutCode_InvoiceLinkCode_1071:             false,
			EnumEpayPayOutCode_PublicIDinvoiceCode_1072:         false,
			EnumEpayPayOutCode_ParamsinvoiceCode_1073:           false,
			EnumEpayPayOutCode_InvoiceLinkCode_1074:             false,
			EnumEpayPayOutCode_BindCode_1075:                    false,
			EnumEpayPayOutCode_StrconvAtoiExpirePerCode_1076:    false,
			EnumEpayPayOutCode_InvoiceLinkCode_1077:             false,
			EnumEpayPayOutCode_EmailCode_1078:                   false,
			EnumEpayPayOutCode_SmsCode_1079:                     false,
			EnumEpayPayOutCode_InvoiceLinkCode_1080:             false,
			EnumEpayPayOutCode_BindCode_1081:                    false,
			EnumEpayPayOutCode_PublicidCode_1082:                false,
			EnumEpayPayOutCode_NameinvoicelinkCode_1083:         false,
			EnumEpayPayOutCode_InvoiceLinkCode_1084:             false,
			EnumEpayPayOutCode_SearchparamsinvoicelCode_1085:    false,
			EnumEpayPayOutCode_IncorrectsearchmethoCode_1086:    false,
			EnumEpayPayOutCode_Code_1087:                        false,
			EnumEpayPayOutCode_InvoiceLinkidCode_1088:           false,
			EnumEpayPayOutCode_InvoiceLinkCode_1089:             false,
			EnumEpayPayOutCode_Code_1090:                        false,
			EnumEpayPayOutCode_InvoiceLinkCode_1091:             false,
			EnumEpayPayOutCode_PublicIDCode_1092:                false,
			EnumEpayPayOutCode_Code_1093:                        false,
			EnumEpayPayOutCode_Code_1096:                        false,
			EnumEpayPayOutCode_HomeBankCode_1097:                false,
			EnumEpayPayOutCode_EmailCode_1098:                   false,
			EnumEpayPayOutCode_Code_1099:                        false,
			EnumEpayPayOutCode_Code_1100:                        false,
			EnumEpayPayOutCode_CoreinvoiceCode_1101:             false,
			EnumEpayPayOutCode_AuthorizedRequestJSOCode_1103:    false,
			EnumEpayPayOutCode_AuthorizedRequestJSOCode_1104:    false,
			EnumEpayPayOutCode_IdserviceCode_1105:               false,
			EnumEpayPayOutCode_ClientIDserviceCode_1106:         false,
			EnumEpayPayOutCode_TerminalXLSMIDterminCode_1107:    false,
			EnumEpayPayOutCode_Code_1130:                        false,
			EnumEpayPayOutCode_Code_1132:                        false,
			EnumEpayPayOutCode_NameCode_1133:                    false,
			EnumEpayPayOutCode_PostlinkcorecallbackCode_1144:    false,
			EnumEpayPayOutCode_Code_1145:                        false,
			EnumEpayPayOutCode_CannotsendpostlinkCode_1146:      false,
			EnumEpayPayOutCode_CannotsendpostlinkCode_1147:      false,
			EnumEpayPayOutCode_BindJsonCode_1148:                false,
			EnumEpayPayOutCode_Base64Code_1149:                  false,
			EnumEpayPayOutCode_UnmarshalCode_1150:               false,
			EnumEpayPayOutCode_HttpcoremigrationCode_1151:       false,
			EnumEpayPayOutCode_Code_1152:                        false,
			EnumEpayPayOutCode_UnmarshalouathCode_1153:          false,
			EnumEpayPayOutCode_EPAY1Code_1154:                   false,
			EnumEpayPayOutCode_UnmarshalEPAY1Code_1155:          false,
			EnumEpayPayOutCode_CannotsendpostlinkCode_1156:      false,
			EnumEpayPayOutCode_Code_1157:                        false,
			EnumEpayPayOutCode_UserserviceCode_1158:             false,
			EnumEpayPayOutCode_UserserviceCode_1159:             false,
			EnumEpayPayOutCode_UserserviceCode_1160:             false,
			EnumEpayPayOutCode_UserserviceCode_1161:             false,
			EnumEpayPayOutCode_UserserviceCode_1162:             false,
			EnumEpayPayOutCode_MerchantcompanynamesCode_1163:    false,
			EnumEpayPayOutCode_MerchantserviceCode_1164:         false,
			EnumEpayPayOutCode_MerchantmerchantCode_1165:        false,
			EnumEpayPayOutCode_UserloginuserclientCode_1166:     false,
			EnumEpayPayOutCode_MerchantnameuserclieCode_1167:    false,
			EnumEpayPayOutCode_AuthorizedRequestJSOCode_1168:    false,
			EnumEpayPayOutCode_MerchantnameCode_1169:            false,
			EnumEpayPayOutCode_EmailmerchantCode_1170:           false,
			EnumEpayPayOutCode_ShopmerchantCode_1171:            false,
			EnumEpayPayOutCode_TerminalmerchantCode_1172:        false,
			EnumEpayPayOutCode_UseruserCode_1173:                false,
			EnumEpayPayOutCode_UserclientIDCode_1174:            false,
			EnumEpayPayOutCode_UserclientuserCode_1175:          false,
			EnumEpayPayOutCode_TerminalterminalIDCode_1176:      false,
			EnumEpayPayOutCode_CurrencycurrencyNameCode_1177:    false,
			EnumEpayPayOutCode_TerminalIDCode_1178:              false,
			EnumEpayPayOutCode_TerminalterminalIDteCode_1179:    false,
			EnumEpayPayOutCode_TerminalIDCode_1180:              false,
			EnumEpayPayOutCode_AuthorizedRequestJSOCode_1182:    false,
			EnumEpayPayOutCode_Code_1184:                        false,
			EnumEpayPayOutCode_TerminalCode_1185:                false,
			EnumEpayPayOutCode_TerminalCode_1186:                false,
			EnumEpayPayOutCode_Code_1187:                        false,
			EnumEpayPayOutCode_MerchantContactTypenCode_1188:    false,
			EnumEpayPayOutCode_SenderrorreportcontaCode_1189:    false,
			EnumEpayPayOutCode_Code_1190:                        false,
			EnumEpayPayOutCode_IdCode_1191:                      false,
			EnumEpayPayOutCode_ExcelCode_1192:                   false,
			EnumEpayPayOutCode_CurrencycurrencyIDCode_1193:      false,
			EnumEpayPayOutCode_CardTypeCardTypeIDCode_1194:      false,
			EnumEpayPayOutCode_EmailCode_1195:                   false,
			EnumEpayPayOutCode_JsonMarshalemailCode_1196:        false,
			EnumEpayPayOutCode_AuthorizedRequestJSOCode_1197:    false,
			EnumEpayPayOutCode_MerchantContactTypeeCode_1198:    false,
			EnumEpayPayOutCode_MerchantContactemailCode_1199:    false,
			EnumEpayPayOutCode_ExcelfileemailCode_1200:          false,
			EnumEpayPayOutCode_Code_1201:                        false,
			EnumEpayPayOutCode_UserloginCode_1202:               false,
			EnumEpayPayOutCode_RowsexcelfilereportCode_1203:     false,
			EnumEpayPayOutCode_UserConnectionsuserICode_1205:    false,
			EnumEpayPayOutCode_KafkaCode_1206:                   false,
			EnumEpayPayOutCode_Code_1207:                        false,
			EnumEpayPayOutCode_Code_1208:                        false,
			EnumEpayPayOutCode_Code_1209:                        false,
			EnumEpayPayOutCode_Code_1210:                        false,
			EnumEpayPayOutCode_AuthorizedRequestMulCode_1211:    false,
			EnumEpayPayOutCode_FormatdataheaderwritCode_1212:    false,
			EnumEpayPayOutCode_WriterreadermultiparCode_1213:    false,
			EnumEpayPayOutCode_ApicdnCode_1214:                  false,
			EnumEpayPayOutCode_LogourlshopIDCode_1215:           false,
			EnumEpayPayOutCode_BindJSONshopshopIDCode_1216:      false,
			EnumEpayPayOutCode_ShopIDdbshopIDCode_1217:          false,
			EnumEpayPayOutCode_JSONSamsungPayCode_1219:          false,
			EnumEpayPayOutCode_RequestJSONSamsungPaCode_1220:    false,
			EnumEpayPayOutCode_ShopIDshopIDCode_1221:            false,
			EnumEpayPayOutCode_TerminalshopIDCode_1222:          false,
			EnumEpayPayOutCode_AuthorizedRequestJSOCode_1223:    false,
			EnumEpayPayOutCode_ShopIDshopIDCode_1224:            false,
			EnumEpayPayOutCode_BindJSONinOutTerminaCode_1225:    false,
			EnumEpayPayOutCode_IdidCode_1226:                    false,
			EnumEpayPayOutCode_AuthorizedRequestJSOCode_1227:    false,
			EnumEpayPayOutCode_TerminalterminalidCode_1228:      false,
			EnumEpayPayOutCode_TerminalidterminalidCode_1229:    false,
			EnumEpayPayOutCode_TerminalterminalidCode_1230:      false,
			EnumEpayPayOutCode_JsonMarshalterminaltCode_1231:    false,
			EnumEpayPayOutCode_PANCode_1232:                     false,
			EnumEpayPayOutCode_SamsungPaycallbackreCode_1233:    false,
			EnumEpayPayOutCode_StaffstaffCode_1234:              false,
			EnumEpayPayOutCode_BindCreateMVisaTransCode_1235:    false,
			EnumEpayPayOutCode_RequestPostFormXMLCrCode_1236:    false,
			EnumEpayPayOutCode_RequestPostFormXMLCrCode_1237:    false,
			EnumEpayPayOutCode_RequestPostFormXMLCrCode_1238:    false,
			EnumEpayPayOutCode_RequestPostFormXMLCrCode_1239:    false,
			EnumEpayPayOutCode_BindGet3DSecureCode_1240:         false,
			EnumEpayPayOutCode_RequestPostFormXMLGeCode_1241:    false,
			EnumEpayPayOutCode_RequestPostFormXMLGeCode_1242:    false,
			EnumEpayPayOutCode_RequestPostFormXMLGeCode_1243:    false,
			EnumEpayPayOutCode_RequestPostFormXMLGeCode_1244:    false,
			EnumEpayPayOutCode_BindManageTransCode_1245:         false,
			EnumEpayPayOutCode_RequestPostFormXMLMaCode_1246:    false,
			EnumEpayPayOutCode_RequestPostFormXMLMaCode_1247:    false,
			EnumEpayPayOutCode_RequestPostFormXMLMaCode_1248:    false,
			EnumEpayPayOutCode_RequestPostFormXMLMaCode_1249:    false,
			EnumEpayPayOutCode_BindCreateTransCode_1250:         false,
			EnumEpayPayOutCode_RequestPostFormXMLCrCode_1251:    false,
			EnumEpayPayOutCode_RequestPostFormXMLCrCode_1252:    false,
			EnumEpayPayOutCode_RequestPostFormXMLCrCode_1253:    false,
			EnumEpayPayOutCode_RequestPostFormXMLCrCode_1254:    false,
			EnumEpayPayOutCode_BindRePaymentCode_1255:           false,
			EnumEpayPayOutCode_RequestPostFormXMLReCode_1256:    false,
			EnumEpayPayOutCode_RequestPostFormXMLReCode_1257:    false,
			EnumEpayPayOutCode_RequestPostFormXMLReCode_1258:    false,
			EnumEpayPayOutCode_RequestPostFormXMLReCode_1259:    false,
			EnumEpayPayOutCode_BindGetTokenCode_1260:            false,
			EnumEpayPayOutCode_RequestPostFormXMLGeCode_1261:    false,
			EnumEpayPayOutCode_RequestPostFormXMLGeCode_1262:    false,
			EnumEpayPayOutCode_RequestPostFormXMLGeCode_1263:    false,
			EnumEpayPayOutCode_RequestPostFormXMLGeCode_1264:    false,
			EnumEpayPayOutCode_BindTransactionCode_1265:         false,
			EnumEpayPayOutCode_RequestPostFormXMLTrCode_1266:    false,
			EnumEpayPayOutCode_CardsystemcommunicatCode_1267:    false,
			EnumEpayPayOutCode_RequestPostFormXMLTrCode_1268:    false,
			EnumEpayPayOutCode_RequestPostFormXMLTrCode_1269:    false,
			EnumEpayPayOutCode_RequestPostFormXMLTrCode_1270:    false,
			EnumEpayPayOutCode_BindTransactionConfiCode_1271:    false,
			EnumEpayPayOutCode_RequestPostFormXMLTrCode_1272:    false,
			EnumEpayPayOutCode_RequestPostFormXMLTrCode_1273:    false,
			EnumEpayPayOutCode_RequestPostFormXMLTrCode_1274:    false,
			EnumEpayPayOutCode_RequestPostFormXMLTrCode_1275:    false,
			EnumEpayPayOutCode_InvoiceLinksGetInvoiCode_1276:    false,
			EnumEpayPayOutCode_PublicIDuserIDCode_1277:          false,
			EnumEpayPayOutCode_ParamsCode_1278:                  false,
			EnumEpayPayOutCode_PostlinkcorecallbackCode_1279:    false,
			EnumEpayPayOutCode_RabbitpostlinkCode_1280:          false,
			EnumEpayPayOutCode_PublicIDtokeninvoiceCode_1281:    false,
			EnumEpayPayOutCode_SlowpostlinkcorecallCode_1282:    false,
			EnumEpayPayOutCode_GetOpenWayIDHalykBonCode_1283:    false,
			EnumEpayPayOutCode_CardTypenilCode_1284:             false,
			EnumEpayPayOutCode_CardtypeCode_1285:                false,
			EnumEpayPayOutCode_PostlinkcorecallbackCode_1286:    false,
			EnumEpayPayOutCode_PostlinkCode_1287:                false,
			EnumEpayPayOutCode_PostlinkinvoiceIDCode_1288:       false,
			EnumEpayPayOutCode_CallbackcorecallbackCode_1289:    false,
			EnumEpayPayOutCode_InvoiceLinkInvoiceIDCode_1290:    false,
			EnumEpayPayOutCode_InvoiceLinkInvoiceIDCode_1291:    false,
			EnumEpayPayOutCode_InvoiceLinkinvoiceLiCode_1293:    false,
			EnumEpayPayOutCode_ErrorMessageinvoiceLCode_1294:    false,
			EnumEpayPayOutCode_TransactionStatusAUTCode_1295:    false,
			EnumEpayPayOutCode_FilePathFileIDCode_1296:          false,
			EnumEpayPayOutCode_BindChargeCode_1297:              false,
			EnumEpayPayOutCode_Code_1298:                        false,
			EnumEpayPayOutCode_InformationStatementCode_1299:    false,
			EnumEpayPayOutCode_RefIDCode_1301:                   false,
			EnumEpayPayOutCode_EmailCode_1302:                   false,
			EnumEpayPayOutCode_StatementHistoryOmitCode_1303:    false,
			EnumEpayPayOutCode_StaffListCode_1304:               false,
			EnumEpayPayOutCode_GETCode_1305:                     false,
			EnumEpayPayOutCode_AuthorizedRequestJSOCode_1306:    false,
			EnumEpayPayOutCode_Code_1307:                        false,
			EnumEpayPayOutCode_CoreinvoiceinvoiceLiCode_1308:    false,
			EnumEpayPayOutCode_CoreinvoiceinvoiceLiCode_1309:    false,
			EnumEpayPayOutCode_InvoiceLinkCode_1310:             false,
			EnumEpayPayOutCode_InvoiceLinkinvoiceIDCode_1311:    false,
			EnumEpayPayOutCode_CoreinvoiceinvoiceInCode_1312:    false,
			EnumEpayPayOutCode_KafkaCode_1315:                   false,
			EnumEpayPayOutCode_HomebankaCode_1317:               false,
			EnumEpayPayOutCode_Code_1318:                        false,
			EnumEpayPayOutCode_UseraCode_1319:                   false,
			EnumEpayPayOutCode_StaffupdatepasswordCode_1320:     false,
			EnumEpayPayOutCode_UpdateStaffRoleCode_1321:         false,
			EnumEpayPayOutCode_Code_1322:                        false,
			EnumEpayPayOutCode_COMMITCode_1323:                  false,
			EnumEpayPayOutCode_Code_1324:                        false,
			EnumEpayPayOutCode_Code_1325:                        false,
			EnumEpayPayOutCode_Code_1326:                        false,
			EnumEpayPayOutCode_Float64FeeAmountstriCode_1327:    false,
			EnumEpayPayOutCode_Float64SettlAmountstCode_1328:    false,
			EnumEpayPayOutCode_Float64TrAmountstrinCode_1329:    false,
			EnumEpayPayOutCode_Code_1330:                        false,
			EnumEpayPayOutCode_Code_1331:                        false,
			EnumEpayPayOutCode_ParseMediaTypeCode_1332:          false,
			EnumEpayPayOutCode_CreateFileCode_1333:              false,
			EnumEpayPayOutCode_EmailCode_1334:                   false,
			EnumEpayPayOutCode_HBCode_1335:                      false,
			EnumEpayPayOutCode_Code_1336:                        false,
			EnumEpayPayOutCode_Code_1337:                        false,
			EnumEpayPayOutCode_ACTIVECode_1338:                  false,
			EnumEpayPayOutCode_CHARGEDCode_1339:                 false,
			EnumEpayPayOutCode_MerchantterminaltermCode_1340:    false,
			EnumEpayPayOutCode_PostingDatestringOrdCode_1341:    false,
			EnumEpayPayOutCode_HBCode_1342:                      false,
			EnumEpayPayOutCode_FTPCode_1343:                     false,
			EnumEpayPayOutCode_JobExecutiongocronCode_1344:      false,
			EnumEpayPayOutCode_Float64FeeAmountstriCode_1346:    false,
			EnumEpayPayOutCode_Float64SettlAmountstCode_1347:    false,
			EnumEpayPayOutCode_Float64TrAmountstrinCode_1348:    false,
			EnumEpayPayOutCode_MerchantPublicIDCode_1361:        false,
			EnumEpayPayOutCode_MerchantInfoPublicIDCode_1366:    false,
			EnumEpayPayOutCode_PublicIDCode_1367:                false,
			EnumEpayPayOutCode_Code_1368:                        false,
			EnumEpayPayOutCode_Code_1369:                        false,
			EnumEpayPayOutCode_Code_1370:                        false,
			EnumEpayPayOutCode_AuthorizedRequestJSOCode_1371:    false,
			EnumEpayPayOutCode_CoreGetCardCode_1372:             false,
			EnumEpayPayOutCode_Code_1373:                        false,
			EnumEpayPayOutCode_OauthCode_1374:                   false,
			EnumEpayPayOutCode_RequestClientCode_1375:           false,
			EnumEpayPayOutCode_Code_1376:                        false,
			EnumEpayPayOutCode_Code_1378:                        false,
			EnumEpayPayOutCode_ScopeCode_1379:                   false,
			EnumEpayPayOutCode_ScopeCode_1380:                   false,
			EnumEpayPayOutCode_ClientScopesCode_1381:            false,
			EnumEpayPayOutCode_Code_1382:                        false,
			EnumEpayPayOutCode_Code_1383:                        false,
			EnumEpayPayOutCode_Code_1384:                        false,
			EnumEpayPayOutCode_Code_1385:                        false,
			EnumEpayPayOutCode_Code_1386:                        false,
			EnumEpayPayOutCode_HomeBankCode_1387:                false,
			EnumEpayPayOutCode_HMCode_1388:                      false,
			EnumEpayPayOutCode_RequestClientUpdateCode_1389:     false,
			EnumEpayPayOutCode_ClientIDCode_1390:                false,
			EnumEpayPayOutCode_GetClientclientCode_1391:         false,
			EnumEpayPayOutCode_GetClientScopeCode_1392:          false,
			EnumEpayPayOutCode_UpdateScopeCode_1393:             false,
			EnumEpayPayOutCode_ScopesCode_1394:                  false,
			EnumEpayPayOutCode_ClientCode_1395:                  false,
			EnumEpayPayOutCode_ScopeCode_1396:                   false,
			EnumEpayPayOutCode_ClientScopesCode_1397:            false,
			EnumEpayPayOutCode_ReadererrorCode_1398:             false,
			EnumEpayPayOutCode_TerminaldbmerchantIDCode_1400:    false,
			EnumEpayPayOutCode_ClientclientIDdbTildCode_1403:    false,
			EnumEpayPayOutCode_ClientclientIDdbCode_1405:        false,
			EnumEpayPayOutCode_ClientIPTildaCode_1406:           false,
			EnumEpayPayOutCode_BindmultipartformdatCode_1407:    false,
			EnumEpayPayOutCode_TildaTildaCode_1408:              false,
			EnumEpayPayOutCode_TildaCode_1409:                   false,
			EnumEpayPayOutCode_Float64amountTildaCode_1410:      false,
			EnumEpayPayOutCode_AuthorizedRequestJSOCode_1411:    false,
			EnumEpayPayOutCode_SourceCode_1412:                  false,
			EnumEpayPayOutCode_CallbackcorecallbackCode_1413:    false,
			EnumEpayPayOutCode_PostlinkTildaCode_1414:           false,
			EnumEpayPayOutCode_Code_1415:                        false,
			EnumEpayPayOutCode_Code_1416:                        false,
			EnumEpayPayOutCode_AuthorizedRequestJSOCode_1418:    false,
			EnumEpayPayOutCode_CoreStatementCode_1419:           false,
			EnumEpayPayOutCode_Code_1421:                        false,
			EnumEpayPayOutCode_Code_1422:                        false,
			EnumEpayPayOutCode_MerchantcompanynameCode_1423:     false,
			EnumEpayPayOutCode_ClientTypeCode_1424:              false,
			EnumEpayPayOutCode_ClientTypenameTildaCode_1425:     false,
			EnumEpayPayOutCode_ClientTypeTildaClienCode_1426:    false,
			EnumEpayPayOutCode_BindJSONBindJSONCode_1427:        false,
			EnumEpayPayOutCode_ParsingerrorCode_1428:            false,
			EnumEpayPayOutCode_Code_1429:                        false,
			EnumEpayPayOutCode_Code_1430:                        false,
			EnumEpayPayOutCode_Code_1431:                        false,
			EnumEpayPayOutCode_LIKECode_1432:                    false,
			EnumEpayPayOutCode_BETWEENCode_1433:                 false,
			EnumEpayPayOutCode_Code_1434:                        false,
			EnumEpayPayOutCode_Code_1436:                        false,
			EnumEpayPayOutCode_Code_1437:                        false,
			EnumEpayPayOutCode_Code_1438:                        false,
			EnumEpayPayOutCode_CountCode_1439:                   false,
			EnumEpayPayOutCode_Code_1440:                        false,
			EnumEpayPayOutCode_Code_1441:                        false,
			EnumEpayPayOutCode_ReferenceEPAYCode_1442:           false,
			EnumEpayPayOutCode_Code_1443:                        false,
			EnumEpayPayOutCode_Code_1444:                        false,
			EnumEpayPayOutCode_Code_1445:                        false,
			EnumEpayPayOutCode_HMHMBCode_1446:                   false,
			EnumEpayPayOutCode_JSONJSONCode_1447:                false,
			EnumEpayPayOutCode_UrlbodyCode_1448:                 false,
			EnumEpayPayOutCode_Code_1449:                        false,
			EnumEpayPayOutCode_GetUseScopeByClientCode_1451:     false,
			EnumEpayPayOutCode_Code_1452:                        false,
			EnumEpayPayOutCode_JSONUnmarshalCode_1453:           false,
			EnumEpayPayOutCode_Code_1454:                        false,
			EnumEpayPayOutCode_Code_1455:                        false,
			EnumEpayPayOutCode_WebsocketCode_1457:               false,
			EnumEpayPayOutCode_WebsocketCode_1458:               false,
			EnumEpayPayOutCode_WebsocketCode_1459:               false,
			EnumEpayPayOutCode_WebsocketCode_1460:               false,
			EnumEpayPayOutCode_WebsocketCode_1461:               false,
			EnumEpayPayOutCode_WebsocketCode_1462:               false,
			EnumEpayPayOutCode_Code_1463:                        false,
			EnumEpayPayOutCode_Code_1464:                        false,
			EnumEpayPayOutCode_Code_1465:                        false,
			EnumEpayPayOutCode_Code_1466:                        false,
			EnumEpayPayOutCode_Code_1467:                        false,
			EnumEpayPayOutCode_UpdateMerchantCode_1468:          false,
			EnumEpayPayOutCode_UpdateMerchantShopCode_1469:      false,
			EnumEpayPayOutCode_UpdateMerchantTerminCode_1470:    false,
			EnumEpayPayOutCode_Code_1471:                        false,
			EnumEpayPayOutCode_Code_1472:                        false,
			EnumEpayPayOutCode_EmailCode_1473:                   false,
			EnumEpayPayOutCode_NameserviceCode_1475:             false,
			EnumEpayPayOutCode_IdparamsCode_1476:                false,
			EnumEpayPayOutCode_MerchantpublicIDCode_1477:        false,
			EnumEpayPayOutCode_TerminalmerchantIDCode_1478:      false,
			EnumEpayPayOutCode_ShopidshopwebserviceCode_1479:    false,
			EnumEpayPayOutCode_Code_1480:                        false,
			EnumEpayPayOutCode_Code_1481:                        false,
			EnumEpayPayOutCode_IdparamsCode_1482:                false,
			EnumEpayPayOutCode_JSONJSONCode_1483:                false,
			EnumEpayPayOutCode_JSONjsonMarshalJSONCode_1484:     false,
			EnumEpayPayOutCode_IdparamsCode_1486:                false,
			EnumEpayPayOutCode_Code_1487:                        false,
			EnumEpayPayOutCode_Code_1488:                        false,
			EnumEpayPayOutCode_Code_1489:                        false,
			EnumEpayPayOutCode_Code_1490:                        false,
			EnumEpayPayOutCode_PaymentRequestCode_1491:          false,
			EnumEpayPayOutCode_Code_1492:                        false,
			EnumEpayPayOutCode_JsonUnmarshalCode_1493:           false,
			EnumEpayPayOutCode_JsonMarshalCode_1494:             false,
			EnumEpayPayOutCode_Code_1495:                        false,
			EnumEpayPayOutCode_JSONJSONCode_1496:                false,
			EnumEpayPayOutCode_Code_1497:                        false,
			EnumEpayPayOutCode_Code_1498:                        false,
			EnumEpayPayOutCode_AuthorizedRequestJSOCode_1499:    false,
			EnumEpayPayOutCode_AuthorizedRequestJSOCode_1500:    false,
			EnumEpayPayOutCode_AuthorizedRequestJSOCode_1502:    false,
			EnumEpayPayOutCode_AuthorizedRequestJSOCode_1503:    false,
			EnumEpayPayOutCode_AuthorizedRequestJSOCode_1504:    false,
			EnumEpayPayOutCode_TerminaluuidCode_1505:            false,
			EnumEpayPayOutCode_BindgrafanaCode_1506:             false,
			EnumEpayPayOutCode_HttpgrafanaCode_1507:             false,
			EnumEpayPayOutCode_UnauthorizedCode_1508:            false,
			EnumEpayPayOutCode_BadtokenCode_1509:                false,
			EnumEpayPayOutCode_JsonUnmarshalCode_1510:           false,
			EnumEpayPayOutCode_JsonMarshalCode_1511:             false,
			EnumEpayPayOutCode_RequestJSONgeoIPCode_1512:        false,
			EnumEpayPayOutCode_Code_1513:                        false,
			EnumEpayPayOutCode_JsonUnmarshalCode_1514:           false,
			EnumEpayPayOutCode_JsonMarshalCode_1515:             false,
			EnumEpayPayOutCode_Code_1516:                        false,
			EnumEpayPayOutCode_QrTransactionInfoCode_1517:       false,
			EnumEpayPayOutCode_CardTypeIDCode_1518:              false,
			EnumEpayPayOutCode_CurrencyIDCode_1519:              false,
			EnumEpayPayOutCode_StatusIDCode_1520:                false,
			EnumEpayPayOutCode_Code_1521:                        false,
			EnumEpayPayOutCode_Code_1522:                        false,
			EnumEpayPayOutCode_Code_1523:                        false,
			EnumEpayPayOutCode_Code_1524:                        false,
			EnumEpayPayOutCode_ShopIDmerchantIDclieCode_1525:    false,
			EnumEpayPayOutCode_Code_1526:                        false,
			EnumEpayPayOutCode_QRCode_1527:                      false,
			EnumEpayPayOutCode_SourceListIDCode_1528:            false,
			EnumEpayPayOutCode_AuthorizedRequestJSOCode_1529:    false,
			EnumEpayPayOutCode_XLSIDCode_1530:                   false,
			EnumEpayPayOutCode_JSONJSONCode_1531:                false,
			EnumEpayPayOutCode_RedispartialTransactCode_1532:    false,
			EnumEpayPayOutCode_Code_1533:                        false,
			EnumEpayPayOutCode_Code_1534:                        false,
			EnumEpayPayOutCode_Code_1535:                        false,
			EnumEpayPayOutCode_Code_1536:                        false,
			EnumEpayPayOutCode_Code_1537:                        false,
			EnumEpayPayOutCode_RedisinvoiceIdCode_1538:          false,
			EnumEpayPayOutCode_Epay1Code_1539:                   false,
			EnumEpayPayOutCode_Epay1Code_1540:                   false,
			EnumEpayPayOutCode_Epay1Code_1541:                   false,
			EnumEpayPayOutCode_Epay1Code_1542:                   false,
			EnumEpayPayOutCode_TransactionTypeIDCode_1543:       false,
			EnumEpayPayOutCode_ClientTypeIDCode_1544:            false,
			EnumEpayPayOutCode_JsonMarshalucsCode_1545:          false,
			EnumEpayPayOutCode_Code_1546:                        false,
			EnumEpayPayOutCode_ScopeCode_1547:                   false,
			EnumEpayPayOutCode_RedisqrstatusCode_1548:           false,
			EnumEpayPayOutCode_AuthorizedRequestJSOCode_1549:    false,
			EnumEpayPayOutCode_Code_1550:                        false,
			EnumEpayPayOutCode_RedisrealIPCode_1551:             false,
			EnumEpayPayOutCode_RequestJSONcoreqrQRSCode_1552:    false,
			EnumEpayPayOutCode_KafkaReadMessageCode_1553:        false,
			EnumEpayPayOutCode_JsonUnmarshalkafkameCode_1554:    false,
			EnumEpayPayOutCode_JSONjsonMarshalJSONCode_1555:     false,
			EnumEpayPayOutCode_KafkaCode_1556:                   false,
			EnumEpayPayOutCode_SetOffsetkafkaCode_1557:          false,
			EnumEpayPayOutCode_Code_1558:                        false,
			EnumEpayPayOutCode_Code_1559:                        false,
			EnumEpayPayOutCode_Code_1560:                        false,
			EnumEpayPayOutCode_Code_1561:                        false,
			EnumEpayPayOutCode_HTTPCode_1562:                    false,
			EnumEpayPayOutCode_HTTPCode_1563:                    false,
			EnumEpayPayOutCode_HTTPCode_1564:                    false,
			EnumEpayPayOutCode_HTTPCode_1565:                    false,
			EnumEpayPayOutCode_AUTHCode_1566:                    false,
			EnumEpayPayOutCode_EGateWayCode_1567:                false,
			EnumEpayPayOutCode_EGateWayCode_1568:                false,
			EnumEpayPayOutCode_Code_1569:                        false,
			EnumEpayPayOutCode_JsonCode_1570:                    false,
			EnumEpayPayOutCode_Code_1571:                        false,
			EnumEpayPayOutCode_Code_1572:                        false,
			EnumEpayPayOutCode_Code_1578:                        false,
			EnumEpayPayOutCode_Code_1579:                        false,
			EnumEpayPayOutCode_ReadAllfromRequestBoCode_1604:    false,
			EnumEpayPayOutCode_ParseFloatrefundCode_1605:        false,
			EnumEpayPayOutCode_AmountbodyurlrefundCode_1606:     false,
			EnumEpayPayOutCode_Code_1607:                        false,
			EnumEpayPayOutCode_ChargeCode_1608:                  false,
			EnumEpayPayOutCode_CancelCode_1609:                  false,
			EnumEpayPayOutCode_RefundCode_1610:                  false,
			EnumEpayPayOutCode_HttpCode_1611:                    false,
			EnumEpayPayOutCode_Code_1612:                        false,
			EnumEpayPayOutCode_Code_1614:                        false,
			EnumEpayPayOutCode_TerminalIDCode_1615:              false,
			EnumEpayPayOutCode_Code_1616:                        false,
			EnumEpayPayOutCode_JsonUnmarshalbinCode_1617:        false,
			EnumEpayPayOutCode_JsonMarshalCode_1618:             false,
			EnumEpayPayOutCode_Code_1619:                        false,
			EnumEpayPayOutCode_JSONapiosuvoxCode_1620:           false,
			EnumEpayPayOutCode_ApiosuvoxCode_1621:               false,
			EnumEpayPayOutCode_ApiosuvoxHTTPCODE200Code_1622:    false,
			EnumEpayPayOutCode_Code_1623:                        false,
			EnumEpayPayOutCode_SourceListCode_1624:              false,
			EnumEpayPayOutCode_Code_1636:                        false,
			EnumEpayPayOutCode_Code_1637:                        false,
			EnumEpayPayOutCode_OsuvoxCode_1638:                  false,
			EnumEpayPayOutCode_InvalidCardIDCode_1639:           false,
			EnumEpayPayOutCode_OsuvoxCode_1640:                  false,
			EnumEpayPayOutCode_Code_1641:                        false,
			EnumEpayPayOutCode_OsuvoxCode_1642:                  false,
			EnumEpayPayOutCode_SingleMessageSchemeCode_1650:     false,
			EnumEpayPayOutCode_Code_1651:                        false,
			EnumEpayPayOutCode_OTPCode_1652:                     false,
			EnumEpayPayOutCode_OTPCode_1653:                     false,
			EnumEpayPayOutCode_OTPcodeCode_1654:                 false,
			EnumEpayPayOutCode_OTPcodeCode_1655:                 false,
			EnumEpayPayOutCode_RedisterminaluuidinvCode_1656:    false,
			EnumEpayPayOutCode_InvalidterminalIdCode_1658:       false,
			EnumEpayPayOutCode_InvalidAmoutCode_1659:            false,
			EnumEpayPayOutCode_Code_1660:                        false,
			EnumEpayPayOutCode_Code_1661:                        false,
			EnumEpayPayOutCode_AuthorizedRequestJSOCode_1663:    false,
			EnumEpayPayOutCode_BindGetTokenByCardCode_1664:      false,
			EnumEpayPayOutCode_RequestPostFormXMLGeCode_1665:    false,
			EnumEpayPayOutCode_RequestPostFormXMLGeCode_1666:    false,
			EnumEpayPayOutCode_RequestPostFormXMLGeCode_1667:    false,
			EnumEpayPayOutCode_RequestPostFormXMLGeCode_1668:    false,
			EnumEpayPayOutCode_Codebase64stringbaseCode_1670:    false,
			EnumEpayPayOutCode_AuthorizedRequestJSOCode_1671:    false,
			EnumEpayPayOutCode_CardCode_1672:                    false,
			EnumEpayPayOutCode_HomebankPayOsuvoxCarCode_1673:    false,
			EnumEpayPayOutCode_OsuvoxCode_1675:                  false,
			EnumEpayPayOutCode_OsuvoxCode_1677:                  false,
			EnumEpayPayOutCode_OsuvoxCode_1679:                  false,
			EnumEpayPayOutCode_OsuvoxCode_1680:                  false,
			EnumEpayPayOutCode_OsuvoxCode_1681:                  false,
			EnumEpayPayOutCode_Code_1682:                        false,
			EnumEpayPayOutCode_OsuvoxCode_1683:                  false,
			EnumEpayPayOutCode_OsuvoxCode_1685:                  false,
			EnumEpayPayOutCode_TransactionCode_1690:             false,
			EnumEpayPayOutCode_CouldnotgetphoneNumbCode_1692:    false,
			EnumEpayPayOutCode_Code_1693:                        false,
			EnumEpayPayOutCode_Code_1694:                        false,
			EnumEpayPayOutCode_OsuvoxCode_1695:                  false,
			EnumEpayPayOutCode_XMLbodyCode_1699:                 false,
			EnumEpayPayOutCode_XMLMarhsalswitchpaymCode_1700:    false,
			EnumEpayPayOutCode_TerminalIdCode_1703:              false,
			EnumEpayPayOutCode_TerminalXLSMIDterminCode_1704:    false,
			EnumEpayPayOutCode_IbanCode_1705:                    false,
			EnumEpayPayOutCode_IbanCode_1706:                    false,
			EnumEpayPayOutCode_TerminalIdCode_1707:              false,
			EnumEpayPayOutCode_IbanCode_1708:                    false,
			EnumEpayPayOutCode_Code_1709:                        false,
			EnumEpayPayOutCode_Code_1710:                        false,
			EnumEpayPayOutCode_Code_1711:                        false,
			EnumEpayPayOutCode_Code_1712:                        false,
			EnumEpayPayOutCode_InvoiceIdCode_1713:               false,
			EnumEpayPayOutCode_Code_1714:                        false,
			EnumEpayPayOutCode_AUTHCode_1715:                    false,
			EnumEpayPayOutCode_PaymentTypeCode_1716:             false,
			EnumEpayPayOutCode_OsuvoxCode_1717:                  false,
			EnumEpayPayOutCode_PaymentTypeCode_1719:             false,
			EnumEpayPayOutCode_PaymentSystemCode_1720:           false,
			EnumEpayPayOutCode_Code_1723:                        false,
			EnumEpayPayOutCode_Code_1724:                        false,
			EnumEpayPayOutCode_Code_1759:                        false,
			EnumEpayPayOutCode_Code_1760:                        false,
			EnumEpayPayOutCode_Code_1761:                        false,
			EnumEpayPayOutCode_Code_1762:                        false,
			EnumEpayPayOutCode_ShopIdCode_1763:                  false,
			EnumEpayPayOutCode_Code_1764:                        false,
			EnumEpayPayOutCode_OrderIdCode_1765:                 false,
			EnumEpayPayOutCode_CoreMerchantCode_1771:            false,
			EnumEpayPayOutCode_PostlinkcorecallbackCode_1780:    false,
			EnumEpayPayOutCode_CardTypenilCode_1781:             false,
			EnumEpayPayOutCode_SwitchpaymentCode_1786:           false,
			EnumEpayPayOutCode_Code_1787:                        false,
			EnumEpayPayOutCode_HttpGetMerchantCertiCode_1788:    false,
			EnumEpayPayOutCode_HttpGetMerchantCreatCode_1789:    false,
			EnumEpayPayOutCode_EnabledCode_1849:                 false,
			EnumEpayPayOutCode_ShopCode_1850:                    false,
			EnumEpayPayOutCode_Code_1855:                        false,
			EnumEpayPayOutCode_TerminalIDCode_1856:              false,
			EnumEpayPayOutCode_ShopIDCode_1857:                  false,
			EnumEpayPayOutCode_IDCode_1858:                      false,
			EnumEpayPayOutCode_ShopidCode_1859:                  false,
			EnumEpayPayOutCode_TokenExpireInSecondsCode_1860:    false,
			EnumEpayPayOutCode_Apiepay1Code_1899:                false,
			EnumEpayPayOutCode_RequestCode_1900:                 false,
			EnumEpayPayOutCode_Code_1901:                        false,
			EnumEpayPayOutCode_Code_1902:                        false,
			EnumEpayPayOutCode_JSONCode_1903:                    false,
			EnumEpayPayOutCode_IdserviceCode_1944:               false,
			EnumEpayPayOutCode_UpdateMerchantContacCode_1945:    false,
			EnumEpayPayOutCode_ContactTypenameserviCode_1946:    false,
			EnumEpayPayOutCode_Code_1954:                        false,
			EnumEpayPayOutCode_Code_1955:                        false,
			EnumEpayPayOutCode_Code_1956:                        false,
			EnumEpayPayOutCode_Code_1961:                        false,
			EnumEpayPayOutCode_Code_1962:                        false,
			EnumEpayPayOutCode_Code_1964:                        false,
			EnumEpayPayOutCode_TerminalshopNameCode_1965:        false,
			EnumEpayPayOutCode_Code_1970:                        false,
			EnumEpayPayOutCode_InvoiceIDCode_1971:               false,
			EnumEpayPayOutCode_Amount0Code_1975:                 false,
			EnumEpayPayOutCode_Code_1976:                        false,
			EnumEpayPayOutCode_Code_1977:                        false,
			EnumEpayPayOutCode_TokenexpiredtryagainCode_1979:    false,
			EnumEpayPayOutCode_MPANCode_1980:                    false,
			EnumEpayPayOutCode_Code_2008:                        false,
			EnumEpayPayOutCode_RecordnotfoundCode_2009:          false,
			EnumEpayPayOutCode_Code_2015:                        false,
			EnumEpayPayOutCode_Code_2016:                        false,
			EnumEpayPayOutCode_RedismaxReqPerSecCode_2017:       false,
			EnumEpayPayOutCode_Code_2018:                        false,
			EnumEpayPayOutCode_GooglePayCode_2028:               false,
			EnumEpayPayOutCode_GooglePayCode_2030:               false,
			EnumEpayPayOutCode_AccountIDCode_2031:               false,
			EnumEpayPayOutCode_CallbackCode_2032:                false,
			EnumEpayPayOutCode_Code_2033:                        false,
			EnumEpayPayOutCode_Code_2046:                        false,
			EnumEpayPayOutCode_JSONCode_2048:                    false,
			EnumEpayPayOutCode_Code_2049:                        false,
			EnumEpayPayOutCode_Code_2050:                        false,
			EnumEpayPayOutCode_Code_2051:                        false,
			EnumEpayPayOutCode_XLSURLCode_2052:                  false,
			EnumEpayPayOutCode_Code_2053:                        false,
			EnumEpayPayOutCode_JSONUpdateProfileStaCode_2057:    false,
			EnumEpayPayOutCode_JSONCreateStaffProfiCode_2058:    false,
			EnumEpayPayOutCode_Code_2059:                        false,
			EnumEpayPayOutCode_Code_2060:                        false,
			EnumEpayPayOutCode_InvalidamountamountmCode_2061:    false,
			EnumEpayPayOutCode_CorebusinessreportCode_2067:      false,
			EnumEpayPayOutCode_Code_2069:                        false,
			EnumEpayPayOutCode_EmailCode_2070:                   false,
			EnumEpayPayOutCode_GinBindCode_2074:                 false,
			EnumEpayPayOutCode_XLSTIDXLSMIDCode_2075:            false,
			EnumEpayPayOutCode_Code_2076:                        false,
			EnumEpayPayOutCode_MPGSCode_2085:                    false,
			EnumEpayPayOutCode_MPGSCode_2086:                    false,
			EnumEpayPayOutCode_MPGSCode_2087:                    false,
			EnumEpayPayOutCode_MPGSCode_2088:                    false,
			EnumEpayPayOutCode_MPGSCode_2089:                    false,
			EnumEpayPayOutCode_MPGSCode_2090:                    false,
			EnumEpayPayOutCode_PANBINCode_2091:                  false,
			EnumEpayPayOutCode_BINorPANrestrictedCode_2092:      false,
			EnumEpayPayOutCode_BindCode_2122:                    false,
			EnumEpayPayOutCode_Code_2123:                        false,
			EnumEpayPayOutCode_Code_2124:                        false,
			EnumEpayPayOutCode_MerchantCode_2125:                false,
			EnumEpayPayOutCode_MerchantCode_2126:                false,
			EnumEpayPayOutCode_Code_2127:                        false,
			EnumEpayPayOutCode_ThreeCode_2128:                   false,
			EnumEpayPayOutCode_Code_2129:                        false,
			EnumEpayPayOutCode_OpenwayIDCode_2130:               false,
			EnumEpayPayOutCode_USDCode_2131:                     false,
			EnumEpayPayOutCode_Code_2132:                        false,
			EnumEpayPayOutCode_Code_2133:                        false,
			EnumEpayPayOutCode_Code_2134:                        false,
			EnumEpayPayOutCode_Code_2135:                        false,
			EnumEpayPayOutCode_XlsCode_2136:                     false,
			EnumEpayPayOutCode_Code_2137:                        false,
			EnumEpayPayOutCode_PostlinkcorecallbackCode_2138:    false,
			EnumEpayPayOutCode_Code_2139:                        false,
			EnumEpayPayOutCode_CryptogramopenwayIDCode_2146:     false,
			EnumEpayPayOutCode_IPCode_2147:                      false,
			EnumEpayPayOutCode_Code_2148:                        false,
			EnumEpayPayOutCode_FormdatasftpproxyCode_2152:       false,
			EnumEpayPayOutCode_HttpNewRequestsftpprCode_2153:    false,
			EnumEpayPayOutCode_RedispipelineCode_2154:           false,
			EnumEpayPayOutCode_TransactionamountshoCode_2156:    false,
			EnumEpayPayOutCode_Code_2157:                        false,
			EnumEpayPayOutCode_URLpagesizeCode_2158:             false,
			EnumEpayPayOutCode_Code_2159:                        false,
			EnumEpayPayOutCode_URLpagesizeCode_2191:             false,
			EnumEpayPayOutCode_PagesizeCode_2193:                false,
			EnumEpayPayOutCode_PagesizeCode_2194:                false,
			EnumEpayPayOutCode_Code_2200:                        false,
			EnumEpayPayOutCode_Code_2203:                        false,
			EnumEpayPayOutCode_Code_2204:                        false,
			EnumEpayPayOutCode_Code_2205:                        false,
			EnumEpayPayOutCode_UnmarshalCode_2206:               false,
			EnumEpayPayOutCode_Code_2207:                        false,
			EnumEpayPayOutCode_Code_2210:                        false,
			EnumEpayPayOutCode_PublicIDonboardingCode_2211:      false,
			EnumEpayPayOutCode_AclserviceCode_2212:              false,
			EnumEpayPayOutCode_StaffCode_2213:                   false,
			EnumEpayPayOutCode_OpenWayCardIdCode_2214:           false,
			EnumEpayPayOutCode_Code_2218:                        false,
			EnumEpayPayOutCode_Code_2237:                        false,
			EnumEpayPayOutCode_PostlinkaCode_2238:               false,
			EnumEpayPayOutCode_PostlinkaCode_2239:               false,
			EnumEpayPayOutCode_PostlinkaCode_2240:               false,
			EnumEpayPayOutCode_PostlinkaCode_2241:               false,
			EnumEpayPayOutCode_PostlinkCode_2242:                false,
			EnumEpayPayOutCode_Code_2243:                        false,
			EnumEpayPayOutCode_Code_2244:                        false,
			EnumEpayPayOutCode_Code_2245:                        false,
			EnumEpayPayOutCode_Code_2249:                        false,
			EnumEpayPayOutCode_P2PCode_2250:                     false,
			EnumEpayPayOutCode_Code_2257:                        false,
			EnumEpayPayOutCode_JSONjsonMarshalJSONCode_2268:     false,
			EnumEpayPayOutCode_Code_2271:                        false,
			EnumEpayPayOutCode_Code_2301:                        false,
			EnumEpayPayOutCode_Code_2305:                        false,
			EnumEpayPayOutCode_QrbyqrCode_2322:                  false,
			EnumEpayPayOutCode_Code_2323:                        false,
			EnumEpayPayOutCode_Code_2326:                        false,
			EnumEpayPayOutCode_CardIDCode_2339:                  false,
			EnumEpayPayOutCode_Code_2349:                        false,
			EnumEpayPayOutCode_Code_2350:                        false,
			EnumEpayPayOutCode_Code_2351:                        false,
			EnumEpayPayOutCode_Code_2352:                        false,
			EnumEpayPayOutCode_Code_2353:                        false,
			EnumEpayPayOutCode_Code_2354:                        false,
			EnumEpayPayOutCode_ClientIDCode_2355:                false,
			EnumEpayPayOutCode_Code_2356:                        false,
			EnumEpayPayOutCode_ResultCodestatusIDCode_2360:      false,
			EnumEpayPayOutCode_PaymentsystemCode_2362:           false,
			EnumEpayPayOutCode_KafkaCode_2365:                   false,
			EnumEpayPayOutCode_ShopInfoCode_2366:                false,
			EnumEpayPayOutCode_Code_2367:                        false,
			EnumEpayPayOutCode_Code_2433:                        false,
			EnumEpayPayOutCode_UZGWCode_2435:                    false,
			EnumEpayPayOutCode_Code_2624:                        false,
			EnumEpayPayOutCode_ChecktransactionstatCode_2660:    false,
			EnumEpayPayOutCode_CorePaymentCode_2678:             false,
			EnumEpayPayOutCode_CorePaymentCode_2679:             false,
			EnumEpayPayOutCode_OtpcodeapiuzgatewayCode_2704:     false,
			EnumEpayPayOutCode_CodeapiuzgatewayCode_2705:        false,
			EnumEpayPayOutCode_IncorrectcurrencyCode_101:        false,
			EnumEpayPayOutCode_DonotreattemptrestriCode_2872:    false,
		},
	}
}

func IsEnumEpayPayOutCode(target EnumEpayPayOutCode, matches ...EnumEpayPayOutCode) bool {
	for _, v := range matches {
		if target == v {
			return true
		}
	}

	return false
}

type EnumEpayPayOutCodeUsage struct {
	enumMap map[EnumEpayPayOutCode]bool
}

func (u *EnumEpayPayOutCodeUsage) Use(slice ...EnumEpayPayOutCode) *EnumEpayPayOutCodeUsage {
	for _, in := range slice {
		u.enumMap[in] = true
	}

	return u
}

func (u *EnumEpayPayOutCodeUsage) Check() []string {
	var result []string
	for k, v := range u.enumMap {
		if v {
			continue
		}

		result = append(result, k.String())
	}

	return result
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Empty_Response_Code() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Empty_Response_Code)
	return EnumEpayPayOutCode_Empty_Response_Code
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_ClientauthenticationCode_33() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_ClientauthenticationCode_33)
	return EnumEpayPayOutCode_ClientauthenticationCode_33
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_SystemerrorpleasetryCode_50() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_SystemerrorpleasetryCode_50)
	return EnumEpayPayOutCode_SystemerrorpleasetryCode_50
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_ErrorinCVC2orCVC2DesCode_18() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_ErrorinCVC2orCVC2DesCode_18)
	return EnumEpayPayOutCode_ErrorinCVC2orCVC2DesCode_18
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_InvalidRetrievalrefeCode_15() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_InvalidRetrievalrefeCode_15)
	return EnumEpayPayOutCode_InvalidRetrievalrefeCode_15
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_TheoperationfailedplCode_454() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_TheoperationfailedplCode_454)
	return EnumEpayPayOutCode_TheoperationfailedplCode_454
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_ThreeDSecurecheckfailedCode_455() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_ThreeDSecurecheckfailedCode_455)
	return EnumEpayPayOutCode_ThreeDSecurecheckfailedCode_455
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_AccessdeniedCode_456() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_AccessdeniedCode_456)
	return EnumEpayPayOutCode_AccessdeniedCode_456
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_ErrorincardexpiratioCode_457() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_ErrorincardexpiratioCode_457)
	return EnumEpayPayOutCode_ErrorincardexpiratioCode_457
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_ServerisnotrespondinCode_458() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_ServerisnotrespondinCode_458)
	return EnumEpayPayOutCode_ServerisnotrespondinCode_458
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_ServerisnotrespondinCode_459() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_ServerisnotrespondinCode_459)
	return EnumEpayPayOutCode_ServerisnotrespondinCode_459
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_NoorinvalidresponcerCode_460() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_NoorinvalidresponcerCode_460)
	return EnumEpayPayOutCode_NoorinvalidresponcerCode_460
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_BadCGIrequestCode_461() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_BadCGIrequestCode_461)
	return EnumEpayPayOutCode_BadCGIrequestCode_461
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_CallyourbankCode_462() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_CallyourbankCode_462)
	return EnumEpayPayOutCode_CallyourbankCode_462
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_CallyourbankCode_463() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_CallyourbankCode_463)
	return EnumEpayPayOutCode_CallyourbankCode_463
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_InvalidmerchantCode_464() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_InvalidmerchantCode_464)
	return EnumEpayPayOutCode_InvalidmerchantCode_464
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_YourcardisrestrictedCode_465() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_YourcardisrestrictedCode_465)
	return EnumEpayPayOutCode_YourcardisrestrictedCode_465
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_NotpermittedtoclientCode_466() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_NotpermittedtoclientCode_466)
	return EnumEpayPayOutCode_NotpermittedtoclientCode_466
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_YourcardisdisabledCode_467() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_YourcardisdisabledCode_467)
	return EnumEpayPayOutCode_YourcardisdisabledCode_467
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_AdditionalidentificaCode_468() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_AdditionalidentificaCode_468)
	return EnumEpayPayOutCode_AdditionalidentificaCode_468
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_InvalidtransactionCode_469() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_InvalidtransactionCode_469)
	return EnumEpayPayOutCode_InvalidtransactionCode_469
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_InvalidamountCode_470() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_InvalidamountCode_470)
	return EnumEpayPayOutCode_InvalidamountCode_470
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_NosuchcardCode_471() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_NosuchcardCode_471)
	return EnumEpayPayOutCode_NosuchcardCode_471
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_NosuchcardCode_472() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_NosuchcardCode_472)
	return EnumEpayPayOutCode_NosuchcardCode_472
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_AuthenticationfailedCode_473() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_AuthenticationfailedCode_473)
	return EnumEpayPayOutCode_AuthenticationfailedCode_473
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_InvalidresponseCode_475() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_InvalidresponseCode_475)
	return EnumEpayPayOutCode_InvalidresponseCode_475
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_NoactiontakenCode_476() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_NoactiontakenCode_476)
	return EnumEpayPayOutCode_NoactiontakenCode_476
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_FormaterrorCode_477() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_FormaterrorCode_477)
	return EnumEpayPayOutCode_FormaterrorCode_477
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_ExpiredcardCode_478() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_ExpiredcardCode_478)
	return EnumEpayPayOutCode_ExpiredcardCode_478
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_RestrictedcardCode_479() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_RestrictedcardCode_479)
	return EnumEpayPayOutCode_RestrictedcardCode_479
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_CallyourbankCode_480() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_CallyourbankCode_480)
	return EnumEpayPayOutCode_CallyourbankCode_480
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_LostcardCode_481() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_LostcardCode_481)
	return EnumEpayPayOutCode_LostcardCode_481
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_LostcardCode_482() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_LostcardCode_482)
	return EnumEpayPayOutCode_LostcardCode_482
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_StolencardCode_483() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_StolencardCode_483)
	return EnumEpayPayOutCode_StolencardCode_483
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_NotsufficientfundsCode_484() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_NotsufficientfundsCode_484)
	return EnumEpayPayOutCode_NotsufficientfundsCode_484
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_ExpiredcardCode_485() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_ExpiredcardCode_485)
	return EnumEpayPayOutCode_ExpiredcardCode_485
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_NotpermittedtoclientCode_486() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_NotpermittedtoclientCode_486)
	return EnumEpayPayOutCode_NotpermittedtoclientCode_486
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_NotpermittedtomerchaCode_487() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_NotpermittedtomerchaCode_487)
	return EnumEpayPayOutCode_NotpermittedtomerchaCode_487
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_ExceedsamountlimitCode_488() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_ExceedsamountlimitCode_488)
	return EnumEpayPayOutCode_ExceedsamountlimitCode_488
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_RestrictedcardCode_489() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_RestrictedcardCode_489)
	return EnumEpayPayOutCode_RestrictedcardCode_489
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_InvalidContractcardCode_490() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_InvalidContractcardCode_490)
	return EnumEpayPayOutCode_InvalidContractcardCode_490
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_ExceedsfrequencylimiCode_491() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_ExceedsfrequencylimiCode_491)
	return EnumEpayPayOutCode_ExceedsfrequencylimiCode_491
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_PINtriesexceededCode_492() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_PINtriesexceededCode_492)
	return EnumEpayPayOutCode_PINtriesexceededCode_492
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_TimeoutatissuersysteCode_493() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_TimeoutatissuersysteCode_493)
	return EnumEpayPayOutCode_TimeoutatissuersysteCode_493
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_IssuerunavailableCode_494() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_IssuerunavailableCode_494)
	return EnumEpayPayOutCode_IssuerunavailableCode_494
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_CannotbecompletedvioCode_495() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_CannotbecompletedvioCode_495)
	return EnumEpayPayOutCode_CannotbecompletedvioCode_495
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_ThreeDSecureSecureCodeabCode_496() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_ThreeDSecureSecureCodeabCode_496)
	return EnumEpayPayOutCode_ThreeDSecureSecureCodeabCode_496
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_ServerisnotrespondinCode_497() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_ServerisnotrespondinCode_497)
	return EnumEpayPayOutCode_ServerisnotrespondinCode_497
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_ErrorincurrencyfieldCode_499() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_ErrorincurrencyfieldCode_499)
	return EnumEpayPayOutCode_ErrorincurrencyfieldCode_499
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_ThreeDSecurecheckfailedCode_500() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_ThreeDSecurecheckfailedCode_500)
	return EnumEpayPayOutCode_ThreeDSecurecheckfailedCode_500
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_CardcheckfailedCode_501() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_CardcheckfailedCode_501)
	return EnumEpayPayOutCode_CardcheckfailedCode_501
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_ThreeDSecureSecureCodeabCode_502() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_ThreeDSecureSecureCodeabCode_502)
	return EnumEpayPayOutCode_ThreeDSecureSecureCodeabCode_502
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_ThreeDSecureSecureCodeabCode_503() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_ThreeDSecureSecureCodeabCode_503)
	return EnumEpayPayOutCode_ThreeDSecureSecureCodeabCode_503
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_TransactiondeclinedCCode_523() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_TransactiondeclinedCCode_523)
	return EnumEpayPayOutCode_TransactiondeclinedCCode_523
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_YourcardisrestrictedCode_524() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_YourcardisrestrictedCode_524)
	return EnumEpayPayOutCode_YourcardisrestrictedCode_524
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_YourcardisrestrictedCode_525() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_YourcardisrestrictedCode_525)
	return EnumEpayPayOutCode_YourcardisrestrictedCode_525
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_SystemerrorPleasetryCode_526() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_SystemerrorPleasetryCode_526)
	return EnumEpayPayOutCode_SystemerrorPleasetryCode_526
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_TransactiondeclinedCCode_527() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_TransactiondeclinedCCode_527)
	return EnumEpayPayOutCode_TransactiondeclinedCCode_527
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_ThedailylimitofincomCode_528() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_ThedailylimitofincomCode_528)
	return EnumEpayPayOutCode_ThedailylimitofincomCode_528
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_TransactiondeclinedCCode_531() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_TransactiondeclinedCCode_531)
	return EnumEpayPayOutCode_TransactiondeclinedCCode_531
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_UnabletoverifyPINcalCode_532() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_UnabletoverifyPINcalCode_532)
	return EnumEpayPayOutCode_UnabletoverifyPINcalCode_532
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_AuthenticationfailedCode_19() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_AuthenticationfailedCode_19)
	return EnumEpayPayOutCode_AuthenticationfailedCode_19
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_ErrorincardexpiratioCode_9() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_ErrorincardexpiratioCode_9)
	return EnumEpayPayOutCode_ErrorincardexpiratioCode_9
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_ServerisnotrespondinCode_8() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_ServerisnotrespondinCode_8)
	return EnumEpayPayOutCode_ServerisnotrespondinCode_8
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_ServerisnotrespondinCode_4() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_ServerisnotrespondinCode_4)
	return EnumEpayPayOutCode_ServerisnotrespondinCode_4
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_ErrorBindActionCreatCode_1() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_ErrorBindActionCreatCode_1)
	return EnumEpayPayOutCode_ErrorBindActionCreatCode_1
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_ErrorcreatingrecordiCode_2() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_ErrorcreatingrecordiCode_2)
	return EnumEpayPayOutCode_ErrorcreatingrecordiCode_2
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_ErrorBindActionUpdatCode_3() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_ErrorBindActionUpdatCode_3)
	return EnumEpayPayOutCode_ErrorBindActionUpdatCode_3
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_ErrorwhileupdatingreCode_4() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_ErrorwhileupdatingreCode_4)
	return EnumEpayPayOutCode_ErrorwhileupdatingreCode_4
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_ErrorwhiledeletingthCode_5() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_ErrorwhiledeletingthCode_5)
	return EnumEpayPayOutCode_ErrorwhiledeletingthCode_5
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_ErrorGetByNameActionCode_6() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_ErrorGetByNameActionCode_6)
	return EnumEpayPayOutCode_ErrorGetByNameActionCode_6
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_ErrorGetByIDActionGeCode_7() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_ErrorGetByIDActionGeCode_7)
	return EnumEpayPayOutCode_ErrorGetByIDActionGeCode_7
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_ErrorBindActionCreatCode_8() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_ErrorBindActionCreatCode_8)
	return EnumEpayPayOutCode_ErrorBindActionCreatCode_8
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_ErrorcreatingrecordiCode_9() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_ErrorcreatingrecordiCode_9)
	return EnumEpayPayOutCode_ErrorcreatingrecordiCode_9
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_RedisActionCreateErrCode_10() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_RedisActionCreateErrCode_10)
	return EnumEpayPayOutCode_RedisActionCreateErrCode_10
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_ErrorBindActionUpdatCode_11() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_ErrorBindActionUpdatCode_11)
	return EnumEpayPayOutCode_ErrorBindActionUpdatCode_11
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_ErrorwhileupdatingreCode_12() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_ErrorwhileupdatingreCode_12)
	return EnumEpayPayOutCode_ErrorwhileupdatingreCode_12
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_RedisActionUpdateErrCode_13() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_RedisActionUpdateErrCode_13)
	return EnumEpayPayOutCode_RedisActionUpdateErrCode_13
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_ErrorwhiledeletingthCode_14() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_ErrorwhiledeletingthCode_14)
	return EnumEpayPayOutCode_ErrorwhiledeletingthCode_14
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_RedisActionDeleteErrCode_15() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_RedisActionDeleteErrCode_15)
	return EnumEpayPayOutCode_RedisActionDeleteErrCode_15
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_ErrorGetByCodeinPostCode_16() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_ErrorGetByCodeinPostCode_16)
	return EnumEpayPayOutCode_ErrorGetByCodeinPostCode_16
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_CodeActionGetErrorByCode_17() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_CodeActionGetErrorByCode_17)
	return EnumEpayPayOutCode_CodeActionGetErrorByCode_17
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_IDApplicationIDAppliCode_18() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_IDApplicationIDAppliCode_18)
	return EnumEpayPayOutCode_IDApplicationIDAppliCode_18
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_CodeCode_19() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_CodeCode_19)
	return EnumEpayPayOutCode_CodeCode_19
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_IDApplicationIDAppliCode_20() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_IDApplicationIDAppliCode_20)
	return EnumEpayPayOutCode_IDApplicationIDAppliCode_20
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_IDApplicationApplicaCode_21() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_IDApplicationApplicaCode_21)
	return EnumEpayPayOutCode_IDApplicationApplicaCode_21
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_22() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_22)
	return EnumEpayPayOutCode_Code_22
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_SuccessfullyCode_23() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_SuccessfullyCode_23)
	return EnumEpayPayOutCode_SuccessfullyCode_23
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Test111Code_24() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Test111Code_24)
	return EnumEpayPayOutCode_Test111Code_24
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_InvoiceidCode_25() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_InvoiceidCode_25)
	return EnumEpayPayOutCode_InvoiceidCode_25
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_BindJSONBindJSONCode_27() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_BindJSONBindJSONCode_27)
	return EnumEpayPayOutCode_BindJSONBindJSONCode_27
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_TransactionalreadyprCode_28() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_TransactionalreadyprCode_28)
	return EnumEpayPayOutCode_TransactionalreadyprCode_28
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_TransactionalreadyprCode_29() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_TransactionalreadyprCode_29)
	return EnumEpayPayOutCode_TransactionalreadyprCode_29
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_ErrorGetShopsByMerchCode_54() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_ErrorGetShopsByMerchCode_54)
	return EnumEpayPayOutCode_ErrorGetShopsByMerchCode_54
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_IdCode_55() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_IdCode_55)
	return EnumEpayPayOutCode_IdCode_55
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_ErrorGetShopByIDCode_56() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_ErrorGetShopByIDCode_56)
	return EnumEpayPayOutCode_ErrorGetShopByIDCode_56
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_ErrorBindCode_57() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_ErrorBindCode_57)
	return EnumEpayPayOutCode_ErrorBindCode_57
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_ErrorBindCode_58() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_ErrorBindCode_58)
	return EnumEpayPayOutCode_ErrorBindCode_58
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_ErrorCreateShopCode_59() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_ErrorCreateShopCode_59)
	return EnumEpayPayOutCode_ErrorCreateShopCode_59
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_ErrorGetMerchantByIDCode_60() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_ErrorGetMerchantByIDCode_60)
	return EnumEpayPayOutCode_ErrorGetMerchantByIDCode_60
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_ErrorGetStatusByNameCode_61() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_ErrorGetStatusByNameCode_61)
	return EnumEpayPayOutCode_ErrorGetStatusByNameCode_61
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_ErrorCreateDefaultCoCode_64() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_ErrorCreateDefaultCoCode_64)
	return EnumEpayPayOutCode_ErrorCreateDefaultCoCode_64
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_ErrorCreateMerchantCode_65() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_ErrorCreateMerchantCode_65)
	return EnumEpayPayOutCode_ErrorCreateMerchantCode_65
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_ErrorVerificationSerCode_71() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_ErrorVerificationSerCode_71)
	return EnumEpayPayOutCode_ErrorVerificationSerCode_71
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_ErrorCheckMerchantCode_72() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_ErrorCheckMerchantCode_72)
	return EnumEpayPayOutCode_ErrorCheckMerchantCode_72
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_ErrorSendVerificatioCode_73() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_ErrorSendVerificatioCode_73)
	return EnumEpayPayOutCode_ErrorSendVerificatioCode_73
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_ErrorVerificationSerCode_74() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_ErrorVerificationSerCode_74)
	return EnumEpayPayOutCode_ErrorVerificationSerCode_74
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_ErrorFindMerchantByECode_75() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_ErrorFindMerchantByECode_75)
	return EnumEpayPayOutCode_ErrorFindMerchantByECode_75
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_ErrorUpdateMerchantCode_76() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_ErrorUpdateMerchantCode_76)
	return EnumEpayPayOutCode_ErrorUpdateMerchantCode_76
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_ErrorRegistrationUseCode_77() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_ErrorRegistrationUseCode_77)
	return EnumEpayPayOutCode_ErrorRegistrationUseCode_77
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_RrnCode_83() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_RrnCode_83)
	return EnumEpayPayOutCode_RrnCode_83
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_ErrorGetCardTypebyRRCode_84() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_ErrorGetCardTypebyRRCode_84)
	return EnumEpayPayOutCode_ErrorGetCardTypebyRRCode_84
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_ErrorCheckTransactioCode_85() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_ErrorCheckTransactioCode_85)
	return EnumEpayPayOutCode_ErrorCheckTransactioCode_85
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_UnauthorizedCode_86() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_UnauthorizedCode_86)
	return EnumEpayPayOutCode_UnauthorizedCode_86
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_TokenisnotvalidCode_87() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_TokenisnotvalidCode_87)
	return EnumEpayPayOutCode_TokenisnotvalidCode_87
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_ErrorCheckTokenCode_88() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_ErrorCheckTokenCode_88)
	return EnumEpayPayOutCode_ErrorCheckTokenCode_88
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_ErrorNoScopesCode_89() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_ErrorNoScopesCode_89)
	return EnumEpayPayOutCode_ErrorNoScopesCode_89
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_ErrorNoScopesCode_90() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_ErrorNoScopesCode_90)
	return EnumEpayPayOutCode_ErrorNoScopesCode_90
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_ErrorCheckTokenCode_91() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_ErrorCheckTokenCode_91)
	return EnumEpayPayOutCode_ErrorCheckTokenCode_91
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Cardnumber14Code_92() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Cardnumber14Code_92)
	return EnumEpayPayOutCode_Cardnumber14Code_92
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_ErrorGetCardCorporatCode_94() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_ErrorGetCardCorporatCode_94)
	return EnumEpayPayOutCode_ErrorGetCardCorporatCode_94
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_ErrorGetCountryByBINCode_95() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_ErrorGetCountryByBINCode_95)
	return EnumEpayPayOutCode_ErrorGetCountryByBINCode_95
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_InvaliddataCode_96() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_InvaliddataCode_96)
	return EnumEpayPayOutCode_InvaliddataCode_96
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_ErrorCheckTokenCode_97() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_ErrorCheckTokenCode_97)
	return EnumEpayPayOutCode_ErrorCheckTokenCode_97
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_ErrorNoScopesCode_98() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_ErrorNoScopesCode_98)
	return EnumEpayPayOutCode_ErrorNoScopesCode_98
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_ContractCode_99() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_ContractCode_99)
	return EnumEpayPayOutCode_ContractCode_99
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_InvalidDateformatPleCode_111() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_InvalidDateformatPleCode_111)
	return EnumEpayPayOutCode_InvalidDateformatPleCode_111
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_ContractCode_112() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_ContractCode_112)
	return EnumEpayPayOutCode_ContractCode_112
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_ExtcsvxlsxCode_113() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_ExtcsvxlsxCode_113)
	return EnumEpayPayOutCode_ExtcsvxlsxCode_113
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_ErrorGetStatementByDCode_114() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_ErrorGetStatementByDCode_114)
	return EnumEpayPayOutCode_ErrorGetStatementByDCode_114
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_ErrorGetStatementByOCode_115() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_ErrorGetStatementByOCode_115)
	return EnumEpayPayOutCode_ErrorGetStatementByOCode_115
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_ErrorGetStatementAirCode_116() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_ErrorGetStatementAirCode_116)
	return EnumEpayPayOutCode_ErrorGetStatementAirCode_116
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_ErrorGetStatementByTCode_117() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_ErrorGetStatementByTCode_117)
	return EnumEpayPayOutCode_ErrorGetStatementByTCode_117
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_ErrorCheckTokenCode_121() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_ErrorCheckTokenCode_121)
	return EnumEpayPayOutCode_ErrorCheckTokenCode_121
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_122() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_122)
	return EnumEpayPayOutCode_Code_122
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_ErrorBindCode_123() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_ErrorBindCode_123)
	return EnumEpayPayOutCode_ErrorBindCode_123
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_124() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_124)
	return EnumEpayPayOutCode_Code_124
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_125() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_125)
	return EnumEpayPayOutCode_Code_125
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_126() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_126)
	return EnumEpayPayOutCode_Code_126
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_127() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_127)
	return EnumEpayPayOutCode_Code_127
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_ErrorEmptyMerchantIDCode_128() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_ErrorEmptyMerchantIDCode_128)
	return EnumEpayPayOutCode_ErrorEmptyMerchantIDCode_128
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_ErrorUnknownMerchantCode_129() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_ErrorUnknownMerchantCode_129)
	return EnumEpayPayOutCode_ErrorUnknownMerchantCode_129
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_ServerErrorCode_130() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_ServerErrorCode_130)
	return EnumEpayPayOutCode_ServerErrorCode_130
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_ErrorCheckTokenCode_131() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_ErrorCheckTokenCode_131)
	return EnumEpayPayOutCode_ErrorCheckTokenCode_131
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_ErrorInvalidScopeCode_132() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_ErrorInvalidScopeCode_132)
	return EnumEpayPayOutCode_ErrorInvalidScopeCode_132
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_ErrorBindCode_133() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_ErrorBindCode_133)
	return EnumEpayPayOutCode_ErrorBindCode_133
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_ErrorRequestPostFormCode_134() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_ErrorRequestPostFormCode_134)
	return EnumEpayPayOutCode_ErrorRequestPostFormCode_134
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_ErrorBodyIsEmptyCode_135() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_ErrorBodyIsEmptyCode_135)
	return EnumEpayPayOutCode_ErrorBodyIsEmptyCode_135
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_ErrorRequestIsNotSucCode_136() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_ErrorRequestIsNotSucCode_136)
	return EnumEpayPayOutCode_ErrorRequestIsNotSucCode_136
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_UnkwonErrorCode_137() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_UnkwonErrorCode_137)
	return EnumEpayPayOutCode_UnkwonErrorCode_137
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_SecretincorrecterrorCode_138() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_SecretincorrecterrorCode_138)
	return EnumEpayPayOutCode_SecretincorrecterrorCode_138
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_139() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_139)
	return EnumEpayPayOutCode_Code_139
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_SecretincorrectCode_140() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_SecretincorrectCode_140)
	return EnumEpayPayOutCode_SecretincorrectCode_140
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_ErrorgetclientfromreCode_141() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_ErrorgetclientfromreCode_141)
	return EnumEpayPayOutCode_ErrorgetclientfromreCode_141
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_ErrorparseclientCode_142() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_ErrorparseclientCode_142)
	return EnumEpayPayOutCode_ErrorparseclientCode_142
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_CheckscopesCode_143() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_CheckscopesCode_143)
	return EnumEpayPayOutCode_CheckscopesCode_143
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_UseridpasswordCode_144() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_UseridpasswordCode_144)
	return EnumEpayPayOutCode_UseridpasswordCode_144
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_UserstatusCHANGEPASSCode_145() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_UserstatusCHANGEPASSCode_145)
	return EnumEpayPayOutCode_UserstatusCHANGEPASSCode_145
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_UsernotfoundCode_146() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_UsernotfoundCode_146)
	return EnumEpayPayOutCode_UsernotfoundCode_146
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_StatusCode_147() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_StatusCode_147)
	return EnumEpayPayOutCode_StatusCode_147
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_148() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_148)
	return EnumEpayPayOutCode_Code_148
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_UserstatusinvalidCode_149() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_UserstatusinvalidCode_149)
	return EnumEpayPayOutCode_UserstatusinvalidCode_149
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_StatusisnotdefinedCode_150() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_StatusisnotdefinedCode_150)
	return EnumEpayPayOutCode_StatusisnotdefinedCode_150
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_PASSWORDINCORRECTCode_151() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_PASSWORDINCORRECTCode_151)
	return EnumEpayPayOutCode_PASSWORDINCORRECTCode_151
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_GranttypeCode_152() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_GranttypeCode_152)
	return EnumEpayPayOutCode_GranttypeCode_152
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_RefreshTokenCode_153() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_RefreshTokenCode_153)
	return EnumEpayPayOutCode_RefreshTokenCode_153
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_154() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_154)
	return EnumEpayPayOutCode_Code_154
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_ScopeCode_155() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_ScopeCode_155)
	return EnumEpayPayOutCode_ScopeCode_155
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_RefreshtokenCode_156() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_RefreshtokenCode_156)
	return EnumEpayPayOutCode_RefreshtokenCode_156
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_GENERATEACCESSERRORCode_157() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_GENERATEACCESSERRORCode_157)
	return EnumEpayPayOutCode_GENERATEACCESSERRORCode_157
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_GENERATEACCESSERRORCode_158() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_GENERATEACCESSERRORCode_158)
	return EnumEpayPayOutCode_GENERATEACCESSERRORCode_158
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_EXTENSIONFIELDADDERRCode_159() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_EXTENSIONFIELDADDERRCode_159)
	return EnumEpayPayOutCode_EXTENSIONFIELDADDERRCode_159
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_EXTENSIONFIELDADDERRCode_160() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_EXTENSIONFIELDADDERRCode_160)
	return EnumEpayPayOutCode_EXTENSIONFIELDADDERRCode_160
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_161() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_161)
	return EnumEpayPayOutCode_Code_161
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_162() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_162)
	return EnumEpayPayOutCode_Code_162
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_GetUserByLoginerrorcCode_163() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_GetUserByLoginerrorcCode_163)
	return EnumEpayPayOutCode_GetUserByLoginerrorcCode_163
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_164() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_164)
	return EnumEpayPayOutCode_Code_164
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_GetStatusByNameerrorCode_165() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_GetStatusByNameerrorCode_165)
	return EnumEpayPayOutCode_GetStatusByNameerrorCode_165
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_HashPassworderrorCode_166() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_HashPassworderrorCode_166)
	return EnumEpayPayOutCode_HashPassworderrorCode_166
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_SaveUsererrorCode_167() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_SaveUsererrorCode_167)
	return EnumEpayPayOutCode_SaveUsererrorCode_167
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_UpdateUsererrorCode_169() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_UpdateUsererrorCode_169)
	return EnumEpayPayOutCode_UpdateUsererrorCode_169
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_UpdateUsererrorCode_170() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_UpdateUsererrorCode_170)
	return EnumEpayPayOutCode_UpdateUsererrorCode_170
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_GetUserByLoginerrorcCode_171() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_GetUserByLoginerrorcCode_171)
	return EnumEpayPayOutCode_GetUserByLoginerrorcCode_171
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_172() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_172)
	return EnumEpayPayOutCode_Code_172
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_173() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_173)
	return EnumEpayPayOutCode_Code_173
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_ActionServiceGetDebiCode_178() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_ActionServiceGetDebiCode_178)
	return EnumEpayPayOutCode_ActionServiceGetDebiCode_178
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_ActionServiceGetTranCode_179() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_ActionServiceGetTranCode_179)
	return EnumEpayPayOutCode_ActionServiceGetTranCode_179
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_ReferenceEPAYCode_180() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_ReferenceEPAYCode_180)
	return EnumEpayPayOutCode_ReferenceEPAYCode_180
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_RedisCode_181() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_RedisCode_181)
	return EnumEpayPayOutCode_RedisCode_181
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_182() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_182)
	return EnumEpayPayOutCode_Code_182
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_185() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_185)
	return EnumEpayPayOutCode_Code_185
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_ErrorUnmarshalCode_186() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_ErrorUnmarshalCode_186)
	return EnumEpayPayOutCode_ErrorUnmarshalCode_186
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_StartDateandEndDatesCode_193() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_StartDateandEndDatesCode_193)
	return EnumEpayPayOutCode_StartDateandEndDatesCode_193
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_EPayCode_194() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_EPayCode_194)
	return EnumEpayPayOutCode_EPayCode_194
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_NoAuthorizeCode_197() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_NoAuthorizeCode_197)
	return EnumEpayPayOutCode_NoAuthorizeCode_197
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_BadtokenCode_198() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_BadtokenCode_198)
	return EnumEpayPayOutCode_BadtokenCode_198
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_ParseerrorCode_199() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_ParseerrorCode_199)
	return EnumEpayPayOutCode_ParseerrorCode_199
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_PaymenterrorCode_200() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_PaymenterrorCode_200)
	return EnumEpayPayOutCode_PaymenterrorCode_200
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_216() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_216)
	return EnumEpayPayOutCode_Code_216
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_ErrorBindJSONCannotuCode_224() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_ErrorBindJSONCannotuCode_224)
	return EnumEpayPayOutCode_ErrorBindJSONCannotuCode_224
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_LIKECode_225() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_LIKECode_225)
	return EnumEpayPayOutCode_LIKECode_225
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_226() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_226)
	return EnumEpayPayOutCode_Code_226
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_BETWEENCode_227() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_BETWEENCode_227)
	return EnumEpayPayOutCode_BETWEENCode_227
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_228() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_228)
	return EnumEpayPayOutCode_Code_228
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_229() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_229)
	return EnumEpayPayOutCode_Code_229
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_230() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_230)
	return EnumEpayPayOutCode_Code_230
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_231() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_231)
	return EnumEpayPayOutCode_Code_231
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_232() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_232)
	return EnumEpayPayOutCode_Code_232
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_ErrorCheckTokenCode_233() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_ErrorCheckTokenCode_233)
	return EnumEpayPayOutCode_ErrorCheckTokenCode_233
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_ErrorNoScopesCode_234() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_ErrorNoScopesCode_234)
	return EnumEpayPayOutCode_ErrorNoScopesCode_234
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_FromdateYYYYMMDDCode_235() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_FromdateYYYYMMDDCode_235)
	return EnumEpayPayOutCode_FromdateYYYYMMDDCode_235
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_TodateYYYYMMDDCode_236() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_TodateYYYYMMDDCode_236)
	return EnumEpayPayOutCode_TodateYYYYMMDDCode_236
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_ErrorCheckTokenCode_237() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_ErrorCheckTokenCode_237)
	return EnumEpayPayOutCode_ErrorCheckTokenCode_237
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_ErrorNoScopesCode_238() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_ErrorNoScopesCode_238)
	return EnumEpayPayOutCode_ErrorNoScopesCode_238
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_CannotunmarshalintosCode_239() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_CannotunmarshalintosCode_239)
	return EnumEpayPayOutCode_CannotunmarshalintosCode_239
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_240() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_240)
	return EnumEpayPayOutCode_Code_240
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_241() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_241)
	return EnumEpayPayOutCode_Code_241
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_242() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_242)
	return EnumEpayPayOutCode_Code_242
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_IDCode_243() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_IDCode_243)
	return EnumEpayPayOutCode_IDCode_243
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_MerchantIDCode_244() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_MerchantIDCode_244)
	return EnumEpayPayOutCode_MerchantIDCode_244
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_StartDateandEndDatesCode_245() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_StartDateandEndDatesCode_245)
	return EnumEpayPayOutCode_StartDateandEndDatesCode_245
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_246() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_246)
	return EnumEpayPayOutCode_Code_246
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_NotemailCode_247() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_NotemailCode_247)
	return EnumEpayPayOutCode_NotemailCode_247
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_JsonCode_248() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_JsonCode_248)
	return EnumEpayPayOutCode_JsonCode_248
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_249() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_249)
	return EnumEpayPayOutCode_Code_249
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_MsgValueCode_250() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_MsgValueCode_250)
	return EnumEpayPayOutCode_MsgValueCode_250
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_251() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_251)
	return EnumEpayPayOutCode_Code_251
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_252() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_252)
	return EnumEpayPayOutCode_Code_252
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_263() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_263)
	return EnumEpayPayOutCode_Code_263
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_264() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_264)
	return EnumEpayPayOutCode_Code_264
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_265() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_265)
	return EnumEpayPayOutCode_Code_265
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_SecretincorrecterrorCode_266() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_SecretincorrecterrorCode_266)
	return EnumEpayPayOutCode_SecretincorrecterrorCode_266
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_ScopenotinchangepassCode_267() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_ScopenotinchangepassCode_267)
	return EnumEpayPayOutCode_ScopenotinchangepassCode_267
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_UnsupportedscopeCode_268() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_UnsupportedscopeCode_268)
	return EnumEpayPayOutCode_UnsupportedscopeCode_268
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_BLOCKCode_269() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_BLOCKCode_269)
	return EnumEpayPayOutCode_BLOCKCode_269
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_BLOCKCode_270() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_BLOCKCode_270)
	return EnumEpayPayOutCode_BLOCKCode_270
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_UsernotsaveCode_271() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_UsernotsaveCode_271)
	return EnumEpayPayOutCode_UsernotsaveCode_271
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_UsernotsaveCode_272() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_UsernotsaveCode_272)
	return EnumEpayPayOutCode_UsernotsaveCode_272
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_FailedtogetstatusCode_273() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_FailedtogetstatusCode_273)
	return EnumEpayPayOutCode_FailedtogetstatusCode_273
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_UseralreadyexistCode_274() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_UseralreadyexistCode_274)
	return EnumEpayPayOutCode_UseralreadyexistCode_274
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_UseralreadyexistCode_275() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_UseralreadyexistCode_275)
	return EnumEpayPayOutCode_UseralreadyexistCode_275
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_IncorrectdataCode_276() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_IncorrectdataCode_276)
	return EnumEpayPayOutCode_IncorrectdataCode_276
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_IncorrectdataCode_277() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_IncorrectdataCode_277)
	return EnumEpayPayOutCode_IncorrectdataCode_277
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_NotemailCode_278() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_NotemailCode_278)
	return EnumEpayPayOutCode_NotemailCode_278
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_NotemailCode_279() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_NotemailCode_279)
	return EnumEpayPayOutCode_NotemailCode_279
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_280() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_280)
	return EnumEpayPayOutCode_Code_280
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_TransactioncreateerrCode_281() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_TransactioncreateerrCode_281)
	return EnumEpayPayOutCode_TransactioncreateerrCode_281
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_282() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_282)
	return EnumEpayPayOutCode_Code_282
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_283() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_283)
	return EnumEpayPayOutCode_Code_283
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_TestMECode_285() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_TestMECode_285)
	return EnumEpayPayOutCode_TestMECode_285
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_StatementCode_286() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_StatementCode_286)
	return EnumEpayPayOutCode_StatementCode_286
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_ErrorCheckTokenCode_287() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_ErrorCheckTokenCode_287)
	return EnumEpayPayOutCode_ErrorCheckTokenCode_287
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_ErrorNoScopesCode_288() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_ErrorNoScopesCode_288)
	return EnumEpayPayOutCode_ErrorNoScopesCode_288
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_TofromYYYYMMDDCode_289() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_TofromYYYYMMDDCode_289)
	return EnumEpayPayOutCode_TofromYYYYMMDDCode_289
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_290() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_290)
	return EnumEpayPayOutCode_Code_290
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_FromDateandToDateshoCode_291() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_FromDateandToDateshoCode_291)
	return EnumEpayPayOutCode_FromDateandToDateshoCode_291
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_ERRORwhilecreatingreCode_292() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_ERRORwhilecreatingreCode_292)
	return EnumEpayPayOutCode_ERRORwhilecreatingreCode_292
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_293() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_293)
	return EnumEpayPayOutCode_Code_293
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_RequiredclientcredenCode_294() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_RequiredclientcredenCode_294)
	return EnumEpayPayOutCode_RequiredclientcredenCode_294
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_IDIDCode_295() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_IDIDCode_295)
	return EnumEpayPayOutCode_IDIDCode_295
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_ErrorUpdateStaffCode_296() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_ErrorUpdateStaffCode_296)
	return EnumEpayPayOutCode_ErrorUpdateStaffCode_296
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_StaffCode_297() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_StaffCode_297)
	return EnumEpayPayOutCode_StaffCode_297
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_298() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_298)
	return EnumEpayPayOutCode_Code_298
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_StaffCode_299() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_StaffCode_299)
	return EnumEpayPayOutCode_StaffCode_299
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_BindErrorCode_300() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_BindErrorCode_300)
	return EnumEpayPayOutCode_BindErrorCode_300
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_UpdateStaffCode_301() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_UpdateStaffCode_301)
	return EnumEpayPayOutCode_UpdateStaffCode_301
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_MultipartFormCode_302() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_MultipartFormCode_302)
	return EnumEpayPayOutCode_MultipartFormCode_302
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_ExpireAtYYYYMMDDCode_303() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_ExpireAtYYYYMMDDCode_303)
	return EnumEpayPayOutCode_ExpireAtYYYYMMDDCode_303
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_ExpireAtYYYYMMDDCode_304() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_ExpireAtYYYYMMDDCode_304)
	return EnumEpayPayOutCode_ExpireAtYYYYMMDDCode_304
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_ExpireAtCode_305() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_ExpireAtCode_305)
	return EnumEpayPayOutCode_ExpireAtCode_305
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_ExpireAtCode_306() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_ExpireAtCode_306)
	return EnumEpayPayOutCode_ExpireAtCode_306
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_307() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_307)
	return EnumEpayPayOutCode_Code_307
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_308() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_308)
	return EnumEpayPayOutCode_Code_308
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_309() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_309)
	return EnumEpayPayOutCode_Code_309
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_CommitErrorCode_310() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_CommitErrorCode_310)
	return EnumEpayPayOutCode_CommitErrorCode_310
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_BindJSONErrorCode_311() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_BindJSONErrorCode_311)
	return EnumEpayPayOutCode_BindJSONErrorCode_311
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_312() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_312)
	return EnumEpayPayOutCode_Code_312
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_SearchtransactionerrCode_318() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_SearchtransactionerrCode_318)
	return EnumEpayPayOutCode_SearchtransactionerrCode_318
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_JsonCode_319() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_JsonCode_319)
	return EnumEpayPayOutCode_JsonCode_319
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_320() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_320)
	return EnumEpayPayOutCode_Code_320
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_321() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_321)
	return EnumEpayPayOutCode_Code_321
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_322() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_322)
	return EnumEpayPayOutCode_Code_322
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_FromdateYYYYMMDDCode_323() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_FromdateYYYYMMDDCode_323)
	return EnumEpayPayOutCode_FromdateYYYYMMDDCode_323
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_TodateYYYYMMDDCode_324() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_TodateYYYYMMDDCode_324)
	return EnumEpayPayOutCode_TodateYYYYMMDDCode_324
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_EPayCode_325() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_EPayCode_325)
	return EnumEpayPayOutCode_EPayCode_325
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_ErrorgetMerchantinfoCode_326() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_ErrorgetMerchantinfoCode_326)
	return EnumEpayPayOutCode_ErrorgetMerchantinfoCode_326
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_327() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_327)
	return EnumEpayPayOutCode_Code_327
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_328() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_328)
	return EnumEpayPayOutCode_Code_328
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_InvoiceIDCode_329() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_InvoiceIDCode_329)
	return EnumEpayPayOutCode_InvoiceIDCode_329
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_TerminalCode_330() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_TerminalCode_330)
	return EnumEpayPayOutCode_TerminalCode_330
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_CurrencyCode_331() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_CurrencyCode_331)
	return EnumEpayPayOutCode_CurrencyCode_331
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_AmountCode_332() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_AmountCode_332)
	return EnumEpayPayOutCode_AmountCode_332
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_333() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_333)
	return EnumEpayPayOutCode_Code_333
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_ShopIDCode_334() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_ShopIDCode_334)
	return EnumEpayPayOutCode_ShopIDCode_334
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_MerchantIDCode_335() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_MerchantIDCode_335)
	return EnumEpayPayOutCode_MerchantIDCode_335
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_ShopIDCode_336() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_ShopIDCode_336)
	return EnumEpayPayOutCode_ShopIDCode_336
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_ErrorgetMerchantinfoCode_337() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_ErrorgetMerchantinfoCode_337)
	return EnumEpayPayOutCode_ErrorgetMerchantinfoCode_337
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_338() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_338)
	return EnumEpayPayOutCode_Code_338
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_339() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_339)
	return EnumEpayPayOutCode_Code_339
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_340() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_340)
	return EnumEpayPayOutCode_Code_340
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_ContractCode_342() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_ContractCode_342)
	return EnumEpayPayOutCode_ContractCode_342
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_GetShopByContractCode_343() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_GetShopByContractCode_343)
	return EnumEpayPayOutCode_GetShopByContractCode_343
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_344() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_344)
	return EnumEpayPayOutCode_Code_344
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_MarshalerrorCode_345() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_MarshalerrorCode_345)
	return EnumEpayPayOutCode_MarshalerrorCode_345
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_346() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_346)
	return EnumEpayPayOutCode_Code_346
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_347() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_347)
	return EnumEpayPayOutCode_Code_347
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_348() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_348)
	return EnumEpayPayOutCode_Code_348
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_349() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_349)
	return EnumEpayPayOutCode_Code_349
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_350() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_350)
	return EnumEpayPayOutCode_Code_350
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_351() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_351)
	return EnumEpayPayOutCode_Code_351
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_352() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_352)
	return EnumEpayPayOutCode_Code_352
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_TopCode_353() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_TopCode_353)
	return EnumEpayPayOutCode_TopCode_353
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_354() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_354)
	return EnumEpayPayOutCode_Code_354
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_EmailCode_355() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_EmailCode_355)
	return EnumEpayPayOutCode_EmailCode_355
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_356() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_356)
	return EnumEpayPayOutCode_Code_356
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_357() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_357)
	return EnumEpayPayOutCode_Code_357
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_358() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_358)
	return EnumEpayPayOutCode_Code_358
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_359() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_359)
	return EnumEpayPayOutCode_Code_359
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_360() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_360)
	return EnumEpayPayOutCode_Code_360
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_361() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_361)
	return EnumEpayPayOutCode_Code_361
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_362() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_362)
	return EnumEpayPayOutCode_Code_362
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_ParseerrorCode_363() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_ParseerrorCode_363)
	return EnumEpayPayOutCode_ParseerrorCode_363
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_ParseerrorCode_364() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_ParseerrorCode_364)
	return EnumEpayPayOutCode_ParseerrorCode_364
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_365() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_365)
	return EnumEpayPayOutCode_Code_365
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_ParseerrorCode_366() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_ParseerrorCode_366)
	return EnumEpayPayOutCode_ParseerrorCode_366
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_ParseerrorCode_367() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_ParseerrorCode_367)
	return EnumEpayPayOutCode_ParseerrorCode_367
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_368() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_368)
	return EnumEpayPayOutCode_Code_368
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_ParseerrorCode_369() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_ParseerrorCode_369)
	return EnumEpayPayOutCode_ParseerrorCode_369
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_ParseerrorCode_370() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_ParseerrorCode_370)
	return EnumEpayPayOutCode_ParseerrorCode_370
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_371() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_371)
	return EnumEpayPayOutCode_Code_371
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_372() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_372)
	return EnumEpayPayOutCode_Code_372
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_NoemailCode_373() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_NoemailCode_373)
	return EnumEpayPayOutCode_NoemailCode_373
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_374() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_374)
	return EnumEpayPayOutCode_Code_374
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_375() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_375)
	return EnumEpayPayOutCode_Code_375
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_376() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_376)
	return EnumEpayPayOutCode_Code_376
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_IncorrectformatdateCode_377() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_IncorrectformatdateCode_377)
	return EnumEpayPayOutCode_IncorrectformatdateCode_377
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_GetFileFiledoesnotexCode_378() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_GetFileFiledoesnotexCode_378)
	return EnumEpayPayOutCode_GetFileFiledoesnotexCode_378
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_379() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_379)
	return EnumEpayPayOutCode_Code_379
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_380() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_380)
	return EnumEpayPayOutCode_Code_380
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_381() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_381)
	return EnumEpayPayOutCode_Code_381
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_382() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_382)
	return EnumEpayPayOutCode_Code_382
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_383() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_383)
	return EnumEpayPayOutCode_Code_383
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_EgatewayCode_384() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_EgatewayCode_384)
	return EnumEpayPayOutCode_EgatewayCode_384
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_EgatewayCode_385() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_EgatewayCode_385)
	return EnumEpayPayOutCode_EgatewayCode_385
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_EgatewayCode_386() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_EgatewayCode_386)
	return EnumEpayPayOutCode_EgatewayCode_386
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_EgatewayCode_387() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_EgatewayCode_387)
	return EnumEpayPayOutCode_EgatewayCode_387
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_EgatewayCode_388() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_EgatewayCode_388)
	return EnumEpayPayOutCode_EgatewayCode_388
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_UserdeleteCode_389() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_UserdeleteCode_389)
	return EnumEpayPayOutCode_UserdeleteCode_389
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_ParseerrorCode_390() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_ParseerrorCode_390)
	return EnumEpayPayOutCode_ParseerrorCode_390
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_CallbacksendmessageeCode_391() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_CallbacksendmessageeCode_391)
	return EnumEpayPayOutCode_CallbacksendmessageeCode_391
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_392() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_392)
	return EnumEpayPayOutCode_Code_392
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_393() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_393)
	return EnumEpayPayOutCode_Code_393
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_394() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_394)
	return EnumEpayPayOutCode_Code_394
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_395() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_395)
	return EnumEpayPayOutCode_Code_395
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_396() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_396)
	return EnumEpayPayOutCode_Code_396
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_MVisaCode_397() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_MVisaCode_397)
	return EnumEpayPayOutCode_MVisaCode_397
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_ThreeDSecureCode_398() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_ThreeDSecureCode_398)
	return EnumEpayPayOutCode_ThreeDSecureCode_398
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_MVisaverificationCode_399() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_MVisaverificationCode_399)
	return EnumEpayPayOutCode_MVisaverificationCode_399
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_400() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_400)
	return EnumEpayPayOutCode_Code_400
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_401() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_401)
	return EnumEpayPayOutCode_Code_401
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_402() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_402)
	return EnumEpayPayOutCode_Code_402
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_CarddataisnotrequireCode_403() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_CarddataisnotrequireCode_403)
	return EnumEpayPayOutCode_CarddataisnotrequireCode_403
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_404() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_404)
	return EnumEpayPayOutCode_Code_404
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_405() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_405)
	return EnumEpayPayOutCode_Code_405
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_406() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_406)
	return EnumEpayPayOutCode_Code_406
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_407() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_407)
	return EnumEpayPayOutCode_Code_407
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_408() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_408)
	return EnumEpayPayOutCode_Code_408
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_409() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_409)
	return EnumEpayPayOutCode_Code_409
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_MVISACode_410() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_MVISACode_410)
	return EnumEpayPayOutCode_MVISACode_410
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_NoAuthorizeCode_411() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_NoAuthorizeCode_411)
	return EnumEpayPayOutCode_NoAuthorizeCode_411
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_BadtokenCode_412() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_BadtokenCode_412)
	return EnumEpayPayOutCode_BadtokenCode_412
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_413() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_413)
	return EnumEpayPayOutCode_Code_413
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_TimeouttransactionCode_504() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_TimeouttransactionCode_504)
	return EnumEpayPayOutCode_TimeouttransactionCode_504
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_ExceededattemptsCode_505() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_ExceededattemptsCode_505)
	return EnumEpayPayOutCode_ExceededattemptsCode_505
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_506() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_506)
	return EnumEpayPayOutCode_Code_506
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_507() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_507)
	return EnumEpayPayOutCode_Code_507
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_508() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_508)
	return EnumEpayPayOutCode_Code_508
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_509() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_509)
	return EnumEpayPayOutCode_Code_509
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_RabbitCode_510() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_RabbitCode_510)
	return EnumEpayPayOutCode_RabbitCode_510
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_512() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_512)
	return EnumEpayPayOutCode_Code_512
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_PostlinkCode_513() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_PostlinkCode_513)
	return EnumEpayPayOutCode_PostlinkCode_513
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_514() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_514)
	return EnumEpayPayOutCode_Code_514
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_515() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_515)
	return EnumEpayPayOutCode_Code_515
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_516() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_516)
	return EnumEpayPayOutCode_Code_516
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_TherewasnoattempttopCode_517() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_TherewasnoattempttopCode_517)
	return EnumEpayPayOutCode_TherewasnoattempttopCode_517
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_IpCode_518() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_IpCode_518)
	return EnumEpayPayOutCode_IpCode_518
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_InvoiceIDCode_520() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_InvoiceIDCode_520)
	return EnumEpayPayOutCode_InvoiceIDCode_520
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_TerminalIDCode_511() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_TerminalIDCode_511)
	return EnumEpayPayOutCode_TerminalIDCode_511
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_BinCode_522() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_BinCode_522)
	return EnumEpayPayOutCode_BinCode_522
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_EGWCode_546() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_EGWCode_546)
	return EnumEpayPayOutCode_EGWCode_546
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_EGWHTTPCODE200Code_547() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_EGWHTTPCODE200Code_547)
	return EnumEpayPayOutCode_EGWHTTPCODE200Code_547
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_559() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_559)
	return EnumEpayPayOutCode_Code_559
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_EGWCode_560() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_EGWCode_560)
	return EnumEpayPayOutCode_EGWCode_560
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_EGWHTTPCODE200Code_561() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_EGWHTTPCODE200Code_561)
	return EnumEpayPayOutCode_EGWHTTPCODE200Code_561
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_MDCode_529() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_MDCode_529)
	return EnumEpayPayOutCode_MDCode_529
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_EGWCode_530() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_EGWCode_530)
	return EnumEpayPayOutCode_EGWCode_530
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_570() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_570)
	return EnumEpayPayOutCode_Code_570
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_EGWCode_571() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_EGWCode_571)
	return EnumEpayPayOutCode_EGWCode_571
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_EGWHTTPCODE200Code_533() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_EGWHTTPCODE200Code_533)
	return EnumEpayPayOutCode_EGWHTTPCODE200Code_533
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_534() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_534)
	return EnumEpayPayOutCode_Code_534
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_535() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_535)
	return EnumEpayPayOutCode_Code_535
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_MerchantCode_536() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_MerchantCode_536)
	return EnumEpayPayOutCode_MerchantCode_536
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_MerchantIsActivefalsCode_537() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_MerchantIsActivefalsCode_537)
	return EnumEpayPayOutCode_MerchantIsActivefalsCode_537
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_538() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_538)
	return EnumEpayPayOutCode_Code_538
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_MerchantCode_539() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_MerchantCode_539)
	return EnumEpayPayOutCode_MerchantCode_539
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_ScopenotfoundcheckscCode_540() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_ScopenotfoundcheckscCode_540)
	return EnumEpayPayOutCode_ScopenotfoundcheckscCode_540
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_ErrorparameternameCode_541() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_ErrorparameternameCode_541)
	return EnumEpayPayOutCode_ErrorparameternameCode_541
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_UnauthorizedChecktokCode_542() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_UnauthorizedChecktokCode_542)
	return EnumEpayPayOutCode_UnauthorizedChecktokCode_542
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_ErrorInvalidScopeCode_543() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_ErrorInvalidScopeCode_543)
	return EnumEpayPayOutCode_ErrorInvalidScopeCode_543
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_ErrorBindCode_544() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_ErrorBindCode_544)
	return EnumEpayPayOutCode_ErrorBindCode_544
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_UnkwonErrorCode_548() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_UnkwonErrorCode_548)
	return EnumEpayPayOutCode_UnkwonErrorCode_548
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_TransactionalreadycaCode_549() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_TransactionalreadycaCode_549)
	return EnumEpayPayOutCode_TransactionalreadycaCode_549
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_RequesttoXLSServerfaCode_550() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_RequesttoXLSServerfaCode_550)
	return EnumEpayPayOutCode_RequesttoXLSServerfaCode_550
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_RequesttoXLSServerreCode_551() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_RequesttoXLSServerreCode_551)
	return EnumEpayPayOutCode_RequesttoXLSServerreCode_551
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_InvalidbonusamountCode_552() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_InvalidbonusamountCode_552)
	return EnumEpayPayOutCode_InvalidbonusamountCode_552
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_InvalidinputdataCode_553() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_InvalidinputdataCode_553)
	return EnumEpayPayOutCode_InvalidinputdataCode_553
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_TerminalconfiguratioCode_554() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_TerminalconfiguratioCode_554)
	return EnumEpayPayOutCode_TerminalconfiguratioCode_554
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_UnabletogetGETrequesCode_555() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_UnabletogetGETrequesCode_555)
	return EnumEpayPayOutCode_UnabletogetGETrequesCode_555
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_RequesttoapixlsfaileCode_556() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_RequesttoapixlsfaileCode_556)
	return EnumEpayPayOutCode_RequesttoapixlsfaileCode_556
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_RequesttoapixlsreturCode_557() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_RequesttoapixlsreturCode_557)
	return EnumEpayPayOutCode_RequesttoapixlsreturCode_557
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_GobonusconvertiontofCode_558() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_GobonusconvertiontofCode_558)
	return EnumEpayPayOutCode_GobonusconvertiontofCode_558
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_563() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_563)
	return EnumEpayPayOutCode_Code_563
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_TerminalIDCode_564() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_TerminalIDCode_564)
	return EnumEpayPayOutCode_TerminalIDCode_564
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_RequesttogetcardinfoCode_565() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_RequesttogetcardinfoCode_565)
	return EnumEpayPayOutCode_RequesttogetcardinfoCode_565
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_RequesttogetcardinfoCode_566() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_RequesttogetcardinfoCode_566)
	return EnumEpayPayOutCode_RequesttogetcardinfoCode_566
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_CardnotfoundCode_567() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_CardnotfoundCode_567)
	return EnumEpayPayOutCode_CardnotfoundCode_567
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_CardownernotfoundCode_568() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_CardownernotfoundCode_568)
	return EnumEpayPayOutCode_CardownernotfoundCode_568
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_569() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_569)
	return EnumEpayPayOutCode_Code_569
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_572() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_572)
	return EnumEpayPayOutCode_Code_572
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_RequesttoapixlsfaileCode_573() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_RequesttoapixlsfaileCode_573)
	return EnumEpayPayOutCode_RequesttoapixlsfaileCode_573
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_RequesttoapixlsreturCode_574() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_RequesttoapixlsreturCode_574)
	return EnumEpayPayOutCode_RequesttoapixlsreturCode_574
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_UnauthorizedChecktokCode_576() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_UnauthorizedChecktokCode_576)
	return EnumEpayPayOutCode_UnauthorizedChecktokCode_576
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_CannotcancelxlstransCode_578() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_CannotcancelxlstransCode_578)
	return EnumEpayPayOutCode_CannotcancelxlstransCode_578
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_CannotsendtransactioCode_579() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_CannotsendtransactioCode_579)
	return EnumEpayPayOutCode_CannotsendtransactioCode_579
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_CannotsendcancelxlstCode_580() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_CannotsendcancelxlstCode_580)
	return EnumEpayPayOutCode_CannotsendcancelxlstCode_580
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_CannotcreaterabbitinCode_581() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_CannotcreaterabbitinCode_581)
	return EnumEpayPayOutCode_CannotcreaterabbitinCode_581
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_ChangePaymentCode_582() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_ChangePaymentCode_582)
	return EnumEpayPayOutCode_ChangePaymentCode_582
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_583() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_583)
	return EnumEpayPayOutCode_Code_583
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_XlsCode_584() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_XlsCode_584)
	return EnumEpayPayOutCode_XlsCode_584
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_GoCode_585() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_GoCode_585)
	return EnumEpayPayOutCode_GoCode_585
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_586() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_586)
	return EnumEpayPayOutCode_Code_586
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_587() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_587)
	return EnumEpayPayOutCode_Code_587
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_588() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_588)
	return EnumEpayPayOutCode_Code_588
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_589() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_589)
	return EnumEpayPayOutCode_Code_589
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_590() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_590)
	return EnumEpayPayOutCode_Code_590
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_591() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_591)
	return EnumEpayPayOutCode_Code_591
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_592() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_592)
	return EnumEpayPayOutCode_Code_592
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_593() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_593)
	return EnumEpayPayOutCode_Code_593
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_594() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_594)
	return EnumEpayPayOutCode_Code_594
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_595() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_595)
	return EnumEpayPayOutCode_Code_595
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Epay1documentCode_596() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Epay1documentCode_596)
	return EnumEpayPayOutCode_Epay1documentCode_596
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_CrtificateIDCode_597() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_CrtificateIDCode_597)
	return EnumEpayPayOutCode_CrtificateIDCode_597
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_598() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_598)
	return EnumEpayPayOutCode_Code_598
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_599() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_599)
	return EnumEpayPayOutCode_Code_599
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_600() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_600)
	return EnumEpayPayOutCode_Code_600
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_601() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_601)
	return EnumEpayPayOutCode_Code_601
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_602() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_602)
	return EnumEpayPayOutCode_Code_602
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_603() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_603)
	return EnumEpayPayOutCode_Code_603
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_604() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_604)
	return EnumEpayPayOutCode_Code_604
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_605() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_605)
	return EnumEpayPayOutCode_Code_605
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_606() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_606)
	return EnumEpayPayOutCode_Code_606
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_607() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_607)
	return EnumEpayPayOutCode_Code_607
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_608() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_608)
	return EnumEpayPayOutCode_Code_608
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_609() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_609)
	return EnumEpayPayOutCode_Code_609
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_610() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_610)
	return EnumEpayPayOutCode_Code_610
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_611() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_611)
	return EnumEpayPayOutCode_Code_611
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_612() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_612)
	return EnumEpayPayOutCode_Code_612
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_613() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_613)
	return EnumEpayPayOutCode_Code_613
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_614() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_614)
	return EnumEpayPayOutCode_Code_614
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_615() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_615)
	return EnumEpayPayOutCode_Code_615
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_616() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_616)
	return EnumEpayPayOutCode_Code_616
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_InvoiceIDCode_617() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_InvoiceIDCode_617)
	return EnumEpayPayOutCode_InvoiceIDCode_617
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_TerminalCode_618() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_TerminalCode_618)
	return EnumEpayPayOutCode_TerminalCode_618
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_TerminalCode_619() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_TerminalCode_619)
	return EnumEpayPayOutCode_TerminalCode_619
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_CurrencyCode_620() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_CurrencyCode_620)
	return EnumEpayPayOutCode_CurrencyCode_620
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_AmountCode_621() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_AmountCode_621)
	return EnumEpayPayOutCode_AmountCode_621
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_AmountCode_622() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_AmountCode_622)
	return EnumEpayPayOutCode_AmountCode_622
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_AmountCode_623() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_AmountCode_623)
	return EnumEpayPayOutCode_AmountCode_623
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_624() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_624)
	return EnumEpayPayOutCode_Code_624
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_625() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_625)
	return EnumEpayPayOutCode_Code_625
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_626() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_626)
	return EnumEpayPayOutCode_Code_626
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_627() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_627)
	return EnumEpayPayOutCode_Code_627
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_628() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_628)
	return EnumEpayPayOutCode_Code_628
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_629() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_629)
	return EnumEpayPayOutCode_Code_629
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_630() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_630)
	return EnumEpayPayOutCode_Code_630
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_631() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_631)
	return EnumEpayPayOutCode_Code_631
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_632() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_632)
	return EnumEpayPayOutCode_Code_632
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_633() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_633)
	return EnumEpayPayOutCode_Code_633
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_634() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_634)
	return EnumEpayPayOutCode_Code_634
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_EgatewayCode_635() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_EgatewayCode_635)
	return EnumEpayPayOutCode_EgatewayCode_635
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_EgatewayCode_636() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_EgatewayCode_636)
	return EnumEpayPayOutCode_EgatewayCode_636
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_EgatewayCode_637() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_EgatewayCode_637)
	return EnumEpayPayOutCode_EgatewayCode_637
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_638() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_638)
	return EnumEpayPayOutCode_Code_638
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_639() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_639)
	return EnumEpayPayOutCode_Code_639
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_640() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_640)
	return EnumEpayPayOutCode_Code_640
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_641() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_641)
	return EnumEpayPayOutCode_Code_641
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_642() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_642)
	return EnumEpayPayOutCode_Code_642
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_CallbacksendmessageeCode_643() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_CallbacksendmessageeCode_643)
	return EnumEpayPayOutCode_CallbacksendmessageeCode_643
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_644() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_644)
	return EnumEpayPayOutCode_Code_644
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_645() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_645)
	return EnumEpayPayOutCode_Code_645
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_646() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_646)
	return EnumEpayPayOutCode_Code_646
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_647() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_647)
	return EnumEpayPayOutCode_Code_647
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_648() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_648)
	return EnumEpayPayOutCode_Code_648
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_649() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_649)
	return EnumEpayPayOutCode_Code_649
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_650() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_650)
	return EnumEpayPayOutCode_Code_650
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_651() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_651)
	return EnumEpayPayOutCode_Code_651
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_652() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_652)
	return EnumEpayPayOutCode_Code_652
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_653() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_653)
	return EnumEpayPayOutCode_Code_653
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_654() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_654)
	return EnumEpayPayOutCode_Code_654
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_655() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_655)
	return EnumEpayPayOutCode_Code_655
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_656() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_656)
	return EnumEpayPayOutCode_Code_656
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_657() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_657)
	return EnumEpayPayOutCode_Code_657
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_658() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_658)
	return EnumEpayPayOutCode_Code_658
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_659() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_659)
	return EnumEpayPayOutCode_Code_659
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_660() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_660)
	return EnumEpayPayOutCode_Code_660
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_661() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_661)
	return EnumEpayPayOutCode_Code_661
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_662() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_662)
	return EnumEpayPayOutCode_Code_662
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_663() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_663)
	return EnumEpayPayOutCode_Code_663
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_664() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_664)
	return EnumEpayPayOutCode_Code_664
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_665() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_665)
	return EnumEpayPayOutCode_Code_665
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_666() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_666)
	return EnumEpayPayOutCode_Code_666
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_667() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_667)
	return EnumEpayPayOutCode_Code_667
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_668() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_668)
	return EnumEpayPayOutCode_Code_668
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_669() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_669)
	return EnumEpayPayOutCode_Code_669
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_670() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_670)
	return EnumEpayPayOutCode_Code_670
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_671() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_671)
	return EnumEpayPayOutCode_Code_671
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_672() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_672)
	return EnumEpayPayOutCode_Code_672
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_673() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_673)
	return EnumEpayPayOutCode_Code_673
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_EGWHTTPCODE200Code_674() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_EGWHTTPCODE200Code_674)
	return EnumEpayPayOutCode_EGWHTTPCODE200Code_674
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_675() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_675)
	return EnumEpayPayOutCode_Code_675
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_676() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_676)
	return EnumEpayPayOutCode_Code_676
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_677() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_677)
	return EnumEpayPayOutCode_Code_677
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_678() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_678)
	return EnumEpayPayOutCode_Code_678
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_679() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_679)
	return EnumEpayPayOutCode_Code_679
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_RequesttogetcardinfoCode_680() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_RequesttogetcardinfoCode_680)
	return EnumEpayPayOutCode_RequesttogetcardinfoCode_680
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_RequesttogetcardinfoCode_681() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_RequesttogetcardinfoCode_681)
	return EnumEpayPayOutCode_RequesttogetcardinfoCode_681
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_XlsCode_683() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_XlsCode_683)
	return EnumEpayPayOutCode_XlsCode_683
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_XlsCode_684() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_XlsCode_684)
	return EnumEpayPayOutCode_XlsCode_684
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_685() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_685)
	return EnumEpayPayOutCode_Code_685
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_TerminalconfiguratioCode_686() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_TerminalconfiguratioCode_686)
	return EnumEpayPayOutCode_TerminalconfiguratioCode_686
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_RequesttoXLSServerfaCode_687() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_RequesttoXLSServerfaCode_687)
	return EnumEpayPayOutCode_RequesttoXLSServerfaCode_687
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_RequesttoXLSServerfaCode_688() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_RequesttoXLSServerfaCode_688)
	return EnumEpayPayOutCode_RequesttoXLSServerfaCode_688
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_689() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_689)
	return EnumEpayPayOutCode_Code_689
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_690() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_690)
	return EnumEpayPayOutCode_Code_690
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_691() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_691)
	return EnumEpayPayOutCode_Code_691
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_JsonCode_692() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_JsonCode_692)
	return EnumEpayPayOutCode_JsonCode_692
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_693() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_693)
	return EnumEpayPayOutCode_Code_693
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_694() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_694)
	return EnumEpayPayOutCode_Code_694
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_StatementCode_695() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_StatementCode_695)
	return EnumEpayPayOutCode_StatementCode_695
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_StatementCode_696() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_StatementCode_696)
	return EnumEpayPayOutCode_StatementCode_696
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_StatementCode_697() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_StatementCode_697)
	return EnumEpayPayOutCode_StatementCode_697
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_StatementCode_698() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_StatementCode_698)
	return EnumEpayPayOutCode_StatementCode_698
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_StatementCode_699() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_StatementCode_699)
	return EnumEpayPayOutCode_StatementCode_699
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_TofromYYYYMMDDCode_700() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_TofromYYYYMMDDCode_700)
	return EnumEpayPayOutCode_TofromYYYYMMDDCode_700
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_TofromYYYYMMDDCode_701() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_TofromYYYYMMDDCode_701)
	return EnumEpayPayOutCode_TofromYYYYMMDDCode_701
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_TofromYYYYMMDDCode_702() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_TofromYYYYMMDDCode_702)
	return EnumEpayPayOutCode_TofromYYYYMMDDCode_702
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_703() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_703)
	return EnumEpayPayOutCode_Code_703
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_704() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_704)
	return EnumEpayPayOutCode_Code_704
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_705() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_705)
	return EnumEpayPayOutCode_Code_705
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_706() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_706)
	return EnumEpayPayOutCode_Code_706
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_707() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_707)
	return EnumEpayPayOutCode_Code_707
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_StaffCode_708() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_StaffCode_708)
	return EnumEpayPayOutCode_StaffCode_708
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_StaffCode_709() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_StaffCode_709)
	return EnumEpayPayOutCode_StaffCode_709
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_StaffCode_710() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_StaffCode_710)
	return EnumEpayPayOutCode_StaffCode_710
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_711() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_711)
	return EnumEpayPayOutCode_Code_711
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_712() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_712)
	return EnumEpayPayOutCode_Code_712
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_713() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_713)
	return EnumEpayPayOutCode_Code_713
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_714() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_714)
	return EnumEpayPayOutCode_Code_714
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_715() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_715)
	return EnumEpayPayOutCode_Code_715
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_716() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_716)
	return EnumEpayPayOutCode_Code_716
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_717() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_717)
	return EnumEpayPayOutCode_Code_717
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_718() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_718)
	return EnumEpayPayOutCode_Code_718
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_719() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_719)
	return EnumEpayPayOutCode_Code_719
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_ErrorinservicingthecCode_720() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_ErrorinservicingthecCode_720)
	return EnumEpayPayOutCode_ErrorinservicingthecCode_720
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_ErrorinservicingthecCode_721() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_ErrorinservicingthecCode_721)
	return EnumEpayPayOutCode_ErrorinservicingthecCode_721
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_ErrorinservicingthecCode_722() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_ErrorinservicingthecCode_722)
	return EnumEpayPayOutCode_ErrorinservicingthecCode_722
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_ErrorinservicingthecCode_723() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_ErrorinservicingthecCode_723)
	return EnumEpayPayOutCode_ErrorinservicingthecCode_723
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_ErrorinservicingthecCode_724() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_ErrorinservicingthecCode_724)
	return EnumEpayPayOutCode_ErrorinservicingthecCode_724
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_ErrorinservicingthecCode_725() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_ErrorinservicingthecCode_725)
	return EnumEpayPayOutCode_ErrorinservicingthecCode_725
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_ErrorinservicingthecCode_726() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_ErrorinservicingthecCode_726)
	return EnumEpayPayOutCode_ErrorinservicingthecCode_726
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_ErrorinservicingthecCode_727() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_ErrorinservicingthecCode_727)
	return EnumEpayPayOutCode_ErrorinservicingthecCode_727
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_NoAuthorizeCode_728() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_NoAuthorizeCode_728)
	return EnumEpayPayOutCode_NoAuthorizeCode_728
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_BadtokenCode_729() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_BadtokenCode_729)
	return EnumEpayPayOutCode_BadtokenCode_729
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_730() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_730)
	return EnumEpayPayOutCode_Code_730
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_InvalidrequestinputCode_731() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_InvalidrequestinputCode_731)
	return EnumEpayPayOutCode_InvalidrequestinputCode_731
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_732() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_732)
	return EnumEpayPayOutCode_Code_732
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_733() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_733)
	return EnumEpayPayOutCode_Code_733
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_734() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_734)
	return EnumEpayPayOutCode_Code_734
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_735() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_735)
	return EnumEpayPayOutCode_Code_735
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_736() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_736)
	return EnumEpayPayOutCode_Code_736
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_IdCode_737() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_IdCode_737)
	return EnumEpayPayOutCode_IdCode_737
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_738() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_738)
	return EnumEpayPayOutCode_Code_738
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_739() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_739)
	return EnumEpayPayOutCode_Code_739
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_740() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_740)
	return EnumEpayPayOutCode_Code_740
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_741() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_741)
	return EnumEpayPayOutCode_Code_741
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_742() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_742)
	return EnumEpayPayOutCode_Code_742
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_743() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_743)
	return EnumEpayPayOutCode_Code_743
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_744() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_744)
	return EnumEpayPayOutCode_Code_744
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_745() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_745)
	return EnumEpayPayOutCode_Code_745
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_746() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_746)
	return EnumEpayPayOutCode_Code_746
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_747() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_747)
	return EnumEpayPayOutCode_Code_747
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_748() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_748)
	return EnumEpayPayOutCode_Code_748
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_749() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_749)
	return EnumEpayPayOutCode_Code_749
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_750() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_750)
	return EnumEpayPayOutCode_Code_750
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_751() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_751)
	return EnumEpayPayOutCode_Code_751
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_752() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_752)
	return EnumEpayPayOutCode_Code_752
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_753() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_753)
	return EnumEpayPayOutCode_Code_753
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_ApimakePaymentscorerCode_754() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_ApimakePaymentscorerCode_754)
	return EnumEpayPayOutCode_ApimakePaymentscorerCode_754
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_756() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_756)
	return EnumEpayPayOutCode_Code_756
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_757() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_757)
	return EnumEpayPayOutCode_Code_757
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_MerchantCode_758() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_MerchantCode_758)
	return EnumEpayPayOutCode_MerchantCode_758
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_759() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_759)
	return EnumEpayPayOutCode_Code_759
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_760() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_760)
	return EnumEpayPayOutCode_Code_760
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_761() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_761)
	return EnumEpayPayOutCode_Code_761
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_762() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_762)
	return EnumEpayPayOutCode_Code_762
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_765() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_765)
	return EnumEpayPayOutCode_Code_765
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_EgatewayCode_766() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_EgatewayCode_766)
	return EnumEpayPayOutCode_EgatewayCode_766
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_767() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_767)
	return EnumEpayPayOutCode_Code_767
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_768() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_768)
	return EnumEpayPayOutCode_Code_768
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_769() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_769)
	return EnumEpayPayOutCode_Code_769
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_770() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_770)
	return EnumEpayPayOutCode_Code_770
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_771() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_771)
	return EnumEpayPayOutCode_Code_771
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_772() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_772)
	return EnumEpayPayOutCode_Code_772
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_773() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_773)
	return EnumEpayPayOutCode_Code_773
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_774() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_774)
	return EnumEpayPayOutCode_Code_774
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_775() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_775)
	return EnumEpayPayOutCode_Code_775
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_InvoiceIDCode_776() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_InvoiceIDCode_776)
	return EnumEpayPayOutCode_InvoiceIDCode_776
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_TerminalCode_777() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_TerminalCode_777)
	return EnumEpayPayOutCode_TerminalCode_777
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_CurrencyCode_778() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_CurrencyCode_778)
	return EnumEpayPayOutCode_CurrencyCode_778
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_AmountCode_779() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_AmountCode_779)
	return EnumEpayPayOutCode_AmountCode_779
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_AmountCode_780() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_AmountCode_780)
	return EnumEpayPayOutCode_AmountCode_780
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_CorepaymentCode_781() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_CorepaymentCode_781)
	return EnumEpayPayOutCode_CorepaymentCode_781
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_782() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_782)
	return EnumEpayPayOutCode_Code_782
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_783() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_783)
	return EnumEpayPayOutCode_Code_783
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_784() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_784)
	return EnumEpayPayOutCode_Code_784
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_IdCode_785() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_IdCode_785)
	return EnumEpayPayOutCode_IdCode_785
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_TerminalIdCode_786() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_TerminalIdCode_786)
	return EnumEpayPayOutCode_TerminalIdCode_786
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_787() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_787)
	return EnumEpayPayOutCode_Code_787
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_789() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_789)
	return EnumEpayPayOutCode_Code_789
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_790() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_790)
	return EnumEpayPayOutCode_Code_790
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_791() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_791)
	return EnumEpayPayOutCode_Code_791
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_792() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_792)
	return EnumEpayPayOutCode_Code_792
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_793() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_793)
	return EnumEpayPayOutCode_Code_793
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_795() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_795)
	return EnumEpayPayOutCode_Code_795
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_CorerecurrentCode_796() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_CorerecurrentCode_796)
	return EnumEpayPayOutCode_CorerecurrentCode_796
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_797() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_797)
	return EnumEpayPayOutCode_Code_797
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_798() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_798)
	return EnumEpayPayOutCode_Code_798
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_799() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_799)
	return EnumEpayPayOutCode_Code_799
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_800() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_800)
	return EnumEpayPayOutCode_Code_800
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_801() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_801)
	return EnumEpayPayOutCode_Code_801
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_802() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_802)
	return EnumEpayPayOutCode_Code_802
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_803() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_803)
	return EnumEpayPayOutCode_Code_803
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_804() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_804)
	return EnumEpayPayOutCode_Code_804
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_805() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_805)
	return EnumEpayPayOutCode_Code_805
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_PublicIDCode_806() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_PublicIDCode_806)
	return EnumEpayPayOutCode_PublicIDCode_806
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_807() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_807)
	return EnumEpayPayOutCode_Code_807
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_808() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_808)
	return EnumEpayPayOutCode_Code_808
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_809() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_809)
	return EnumEpayPayOutCode_Code_809
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_810() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_810)
	return EnumEpayPayOutCode_Code_810
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_811() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_811)
	return EnumEpayPayOutCode_Code_811
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_812() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_812)
	return EnumEpayPayOutCode_Code_812
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_813() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_813)
	return EnumEpayPayOutCode_Code_813
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_814() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_814)
	return EnumEpayPayOutCode_Code_814
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_815() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_815)
	return EnumEpayPayOutCode_Code_815
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_816() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_816)
	return EnumEpayPayOutCode_Code_816
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_817() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_817)
	return EnumEpayPayOutCode_Code_817
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_818() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_818)
	return EnumEpayPayOutCode_Code_818
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_819() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_819)
	return EnumEpayPayOutCode_Code_819
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_820() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_820)
	return EnumEpayPayOutCode_Code_820
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_821() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_821)
	return EnumEpayPayOutCode_Code_821
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_822() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_822)
	return EnumEpayPayOutCode_Code_822
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_823() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_823)
	return EnumEpayPayOutCode_Code_823
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_824() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_824)
	return EnumEpayPayOutCode_Code_824
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_825() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_825)
	return EnumEpayPayOutCode_Code_825
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_826() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_826)
	return EnumEpayPayOutCode_Code_826
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_827() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_827)
	return EnumEpayPayOutCode_Code_827
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_828() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_828)
	return EnumEpayPayOutCode_Code_828
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_829() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_829)
	return EnumEpayPayOutCode_Code_829
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_830() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_830)
	return EnumEpayPayOutCode_Code_830
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_831() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_831)
	return EnumEpayPayOutCode_Code_831
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_832() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_832)
	return EnumEpayPayOutCode_Code_832
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_833() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_833)
	return EnumEpayPayOutCode_Code_833
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_834() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_834)
	return EnumEpayPayOutCode_Code_834
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_835() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_835)
	return EnumEpayPayOutCode_Code_835
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_836() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_836)
	return EnumEpayPayOutCode_Code_836
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_837() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_837)
	return EnumEpayPayOutCode_Code_837
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_838() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_838)
	return EnumEpayPayOutCode_Code_838
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_839() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_839)
	return EnumEpayPayOutCode_Code_839
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_840() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_840)
	return EnumEpayPayOutCode_Code_840
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_841() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_841)
	return EnumEpayPayOutCode_Code_841
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_842() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_842)
	return EnumEpayPayOutCode_Code_842
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_843() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_843)
	return EnumEpayPayOutCode_Code_843
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_844() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_844)
	return EnumEpayPayOutCode_Code_844
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_845() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_845)
	return EnumEpayPayOutCode_Code_845
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_PublicIdCode_846() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_PublicIdCode_846)
	return EnumEpayPayOutCode_PublicIdCode_846
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_MerchantAPICode_847() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_MerchantAPICode_847)
	return EnumEpayPayOutCode_MerchantAPICode_847
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_848() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_848)
	return EnumEpayPayOutCode_Code_848
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_849() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_849)
	return EnumEpayPayOutCode_Code_849
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_850() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_850)
	return EnumEpayPayOutCode_Code_850
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_StaffInviteRequestCode_851() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_StaffInviteRequestCode_851)
	return EnumEpayPayOutCode_StaffInviteRequestCode_851
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_PublicIdCode_852() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_PublicIdCode_852)
	return EnumEpayPayOutCode_PublicIdCode_852
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_853() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_853)
	return EnumEpayPayOutCode_Code_853
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_854() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_854)
	return EnumEpayPayOutCode_Code_854
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_PublicIdCode_855() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_PublicIdCode_855)
	return EnumEpayPayOutCode_PublicIdCode_855
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_PublicIdCode_856() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_PublicIdCode_856)
	return EnumEpayPayOutCode_PublicIdCode_856
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_857() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_857)
	return EnumEpayPayOutCode_Code_857
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_PublicIdCode_858() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_PublicIdCode_858)
	return EnumEpayPayOutCode_PublicIdCode_858
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_859() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_859)
	return EnumEpayPayOutCode_Code_859
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_860() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_860)
	return EnumEpayPayOutCode_Code_860
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_861() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_861)
	return EnumEpayPayOutCode_Code_861
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_862() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_862)
	return EnumEpayPayOutCode_Code_862
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_863() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_863)
	return EnumEpayPayOutCode_Code_863
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_864() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_864)
	return EnumEpayPayOutCode_Code_864
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_865() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_865)
	return EnumEpayPayOutCode_Code_865
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_OauthCode_866() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_OauthCode_866)
	return EnumEpayPayOutCode_OauthCode_866
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_867() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_867)
	return EnumEpayPayOutCode_Code_867
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_868() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_868)
	return EnumEpayPayOutCode_Code_868
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_869() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_869)
	return EnumEpayPayOutCode_Code_869
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_870() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_870)
	return EnumEpayPayOutCode_Code_870
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_871() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_871)
	return EnumEpayPayOutCode_Code_871
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_872() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_872)
	return EnumEpayPayOutCode_Code_872
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_OauthCode_873() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_OauthCode_873)
	return EnumEpayPayOutCode_OauthCode_873
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_874() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_874)
	return EnumEpayPayOutCode_Code_874
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_OauthCode_875() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_OauthCode_875)
	return EnumEpayPayOutCode_OauthCode_875
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_876() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_876)
	return EnumEpayPayOutCode_Code_876
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_MDCode_877() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_MDCode_877)
	return EnumEpayPayOutCode_MDCode_877
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_878() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_878)
	return EnumEpayPayOutCode_Code_878
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_879() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_879)
	return EnumEpayPayOutCode_Code_879
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_880() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_880)
	return EnumEpayPayOutCode_Code_880
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_881() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_881)
	return EnumEpayPayOutCode_Code_881
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_EGWCode_882() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_EGWCode_882)
	return EnumEpayPayOutCode_EGWCode_882
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_RedisCode_883() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_RedisCode_883)
	return EnumEpayPayOutCode_RedisCode_883
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_RedisCode_884() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_RedisCode_884)
	return EnumEpayPayOutCode_RedisCode_884
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_RedisCode_885() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_RedisCode_885)
	return EnumEpayPayOutCode_RedisCode_885
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_RedisCode_886() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_RedisCode_886)
	return EnumEpayPayOutCode_RedisCode_886
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_887() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_887)
	return EnumEpayPayOutCode_Code_887
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_AclapiCode_888() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_AclapiCode_888)
	return EnumEpayPayOutCode_AclapiCode_888
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_889() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_889)
	return EnumEpayPayOutCode_Code_889
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_MerchantapiCode_890() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_MerchantapiCode_890)
	return EnumEpayPayOutCode_MerchantapiCode_890
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_891() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_891)
	return EnumEpayPayOutCode_Code_891
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_892() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_892)
	return EnumEpayPayOutCode_Code_892
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_MerchantapiCode_893() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_MerchantapiCode_893)
	return EnumEpayPayOutCode_MerchantapiCode_893
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_894() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_894)
	return EnumEpayPayOutCode_Code_894
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_CorepaymentCode_895() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_CorepaymentCode_895)
	return EnumEpayPayOutCode_CorepaymentCode_895
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_ConfirmCode_896() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_ConfirmCode_896)
	return EnumEpayPayOutCode_ConfirmCode_896
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_897() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_897)
	return EnumEpayPayOutCode_Code_897
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_PublicIDCode_898() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_PublicIDCode_898)
	return EnumEpayPayOutCode_PublicIDCode_898
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_899() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_899)
	return EnumEpayPayOutCode_Code_899
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_900() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_900)
	return EnumEpayPayOutCode_Code_900
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_901() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_901)
	return EnumEpayPayOutCode_Code_901
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_PublicIDCode_902() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_PublicIDCode_902)
	return EnumEpayPayOutCode_PublicIDCode_902
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_903() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_903)
	return EnumEpayPayOutCode_Code_903
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_904() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_904)
	return EnumEpayPayOutCode_Code_904
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_905() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_905)
	return EnumEpayPayOutCode_Code_905
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_906() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_906)
	return EnumEpayPayOutCode_Code_906
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_907() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_907)
	return EnumEpayPayOutCode_Code_907
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_908() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_908)
	return EnumEpayPayOutCode_Code_908
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_MerchantapiCode_909() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_MerchantapiCode_909)
	return EnumEpayPayOutCode_MerchantapiCode_909
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_MerchantapiCode_910() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_MerchantapiCode_910)
	return EnumEpayPayOutCode_MerchantapiCode_910
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_911() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_911)
	return EnumEpayPayOutCode_Code_911
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_912() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_912)
	return EnumEpayPayOutCode_Code_912
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_913() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_913)
	return EnumEpayPayOutCode_Code_913
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_914() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_914)
	return EnumEpayPayOutCode_Code_914
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_915() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_915)
	return EnumEpayPayOutCode_Code_915
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_916() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_916)
	return EnumEpayPayOutCode_Code_916
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_917() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_917)
	return EnumEpayPayOutCode_Code_917
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_918() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_918)
	return EnumEpayPayOutCode_Code_918
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_919() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_919)
	return EnumEpayPayOutCode_Code_919
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_920() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_920)
	return EnumEpayPayOutCode_Code_920
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_921() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_921)
	return EnumEpayPayOutCode_Code_921
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_922() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_922)
	return EnumEpayPayOutCode_Code_922
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_923() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_923)
	return EnumEpayPayOutCode_Code_923
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_924() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_924)
	return EnumEpayPayOutCode_Code_924
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_925() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_925)
	return EnumEpayPayOutCode_Code_925
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_926() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_926)
	return EnumEpayPayOutCode_Code_926
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_927() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_927)
	return EnumEpayPayOutCode_Code_927
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_928() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_928)
	return EnumEpayPayOutCode_Code_928
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_PublicIDCode_929() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_PublicIDCode_929)
	return EnumEpayPayOutCode_PublicIDCode_929
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_930() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_930)
	return EnumEpayPayOutCode_Code_930
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_931() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_931)
	return EnumEpayPayOutCode_Code_931
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_ScopesCode_932() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_ScopesCode_932)
	return EnumEpayPayOutCode_ScopesCode_932
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_933() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_933)
	return EnumEpayPayOutCode_Code_933
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_934() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_934)
	return EnumEpayPayOutCode_Code_934
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_935() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_935)
	return EnumEpayPayOutCode_Code_935
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_937() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_937)
	return EnumEpayPayOutCode_Code_937
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_938() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_938)
	return EnumEpayPayOutCode_Code_938
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_939() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_939)
	return EnumEpayPayOutCode_Code_939
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_940() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_940)
	return EnumEpayPayOutCode_Code_940
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_941() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_941)
	return EnumEpayPayOutCode_Code_941
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_942() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_942)
	return EnumEpayPayOutCode_Code_942
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_943() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_943)
	return EnumEpayPayOutCode_Code_943
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_944() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_944)
	return EnumEpayPayOutCode_Code_944
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_PublicIDCode_945() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_PublicIDCode_945)
	return EnumEpayPayOutCode_PublicIDCode_945
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_946() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_946)
	return EnumEpayPayOutCode_Code_946
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_947() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_947)
	return EnumEpayPayOutCode_Code_947
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_948() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_948)
	return EnumEpayPayOutCode_Code_948
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_949() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_949)
	return EnumEpayPayOutCode_Code_949
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_950() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_950)
	return EnumEpayPayOutCode_Code_950
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_PublicIdCode_951() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_PublicIdCode_951)
	return EnumEpayPayOutCode_PublicIdCode_951
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_952() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_952)
	return EnumEpayPayOutCode_Code_952
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_953() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_953)
	return EnumEpayPayOutCode_Code_953
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_954() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_954)
	return EnumEpayPayOutCode_Code_954
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_955() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_955)
	return EnumEpayPayOutCode_Code_955
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_956() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_956)
	return EnumEpayPayOutCode_Code_956
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_957() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_957)
	return EnumEpayPayOutCode_Code_957
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_958() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_958)
	return EnumEpayPayOutCode_Code_958
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_959() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_959)
	return EnumEpayPayOutCode_Code_959
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_960() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_960)
	return EnumEpayPayOutCode_Code_960
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_961() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_961)
	return EnumEpayPayOutCode_Code_961
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_962() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_962)
	return EnumEpayPayOutCode_Code_962
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_AuthorizedRequestJSOCode_963() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_AuthorizedRequestJSOCode_963)
	return EnumEpayPayOutCode_AuthorizedRequestJSOCode_963
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_ClientsecretCode_964() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_ClientsecretCode_964)
	return EnumEpayPayOutCode_ClientsecretCode_964
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_ClientidclientsecretCode_965() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_ClientidclientsecretCode_965)
	return EnumEpayPayOutCode_ClientidclientsecretCode_965
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_MerchantIDclientsecrCode_966() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_MerchantIDclientsecrCode_966)
	return EnumEpayPayOutCode_MerchantIDclientsecrCode_966
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_ClientsecretclientIDCode_967() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_ClientsecretclientIDCode_967)
	return EnumEpayPayOutCode_ClientsecretclientIDCode_967
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_EmailclientIDclientsCode_968() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_EmailclientIDclientsCode_968)
	return EnumEpayPayOutCode_EmailclientIDclientsCode_968
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_969() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_969)
	return EnumEpayPayOutCode_Code_969
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_970() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_970)
	return EnumEpayPayOutCode_Code_970
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_971() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_971)
	return EnumEpayPayOutCode_Code_971
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_972() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_972)
	return EnumEpayPayOutCode_Code_972
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_IDCode_973() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_IDCode_973)
	return EnumEpayPayOutCode_IDCode_973
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_974() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_974)
	return EnumEpayPayOutCode_Code_974
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_975() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_975)
	return EnumEpayPayOutCode_Code_975
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_UserclientIDclientseCode_976() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_UserclientIDclientseCode_976)
	return EnumEpayPayOutCode_UserclientIDclientseCode_976
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_AuthorizedRequestJSOCode_977() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_AuthorizedRequestJSOCode_977)
	return EnumEpayPayOutCode_AuthorizedRequestJSOCode_977
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_JsonMarshalsendEmailCode_978() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_JsonMarshalsendEmailCode_978)
	return EnumEpayPayOutCode_JsonMarshalsendEmailCode_978
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_979() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_979)
	return EnumEpayPayOutCode_Code_979
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_FormFileCode_980() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_FormFileCode_980)
	return EnumEpayPayOutCode_FormFileCode_980
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_981() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_981)
	return EnumEpayPayOutCode_Code_981
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_982() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_982)
	return EnumEpayPayOutCode_Code_982
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_983() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_983)
	return EnumEpayPayOutCode_Code_983
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_984() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_984)
	return EnumEpayPayOutCode_Code_984
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_985() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_985)
	return EnumEpayPayOutCode_Code_985
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_986() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_986)
	return EnumEpayPayOutCode_Code_986
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_987() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_987)
	return EnumEpayPayOutCode_Code_987
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_988() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_988)
	return EnumEpayPayOutCode_Code_988
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_989() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_989)
	return EnumEpayPayOutCode_Code_989
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_990() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_990)
	return EnumEpayPayOutCode_Code_990
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_991() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_991)
	return EnumEpayPayOutCode_Code_991
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_992() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_992)
	return EnumEpayPayOutCode_Code_992
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_ServiceCode_993() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_ServiceCode_993)
	return EnumEpayPayOutCode_ServiceCode_993
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_ShouldBindJSONservicCode_994() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_ShouldBindJSONservicCode_994)
	return EnumEpayPayOutCode_ShouldBindJSONservicCode_994
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_AuthorizedRequestJSOCode_995() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_AuthorizedRequestJSOCode_995)
	return EnumEpayPayOutCode_AuthorizedRequestJSOCode_995
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_AuthorizedRequestJSOCode_996() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_AuthorizedRequestJSOCode_996)
	return EnumEpayPayOutCode_AuthorizedRequestJSOCode_996
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_UseruserusernameservCode_997() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_UseruserusernameservCode_997)
	return EnumEpayPayOutCode_UseruserusernameservCode_997
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_MerchantserviceCode_999() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_MerchantserviceCode_999)
	return EnumEpayPayOutCode_MerchantserviceCode_999
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_NamewebserviceCode_1000() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_NamewebserviceCode_1000)
	return EnumEpayPayOutCode_NamewebserviceCode_1000
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_ContactTypenameserviCode_1001() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_ContactTypenameserviCode_1001)
	return EnumEpayPayOutCode_ContactTypenameserviCode_1001
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_PublicIdCode_1002() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_PublicIdCode_1002)
	return EnumEpayPayOutCode_PublicIdCode_1002
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_1003() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_1003)
	return EnumEpayPayOutCode_Code_1003
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_1004() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_1004)
	return EnumEpayPayOutCode_Code_1004
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_1005() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_1005)
	return EnumEpayPayOutCode_Code_1005
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_OauthCode_1006() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_OauthCode_1006)
	return EnumEpayPayOutCode_OauthCode_1006
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_1007() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_1007)
	return EnumEpayPayOutCode_Code_1007
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_1008() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_1008)
	return EnumEpayPayOutCode_Code_1008
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_1009() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_1009)
	return EnumEpayPayOutCode_Code_1009
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_IdserviceCode_1010() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_IdserviceCode_1010)
	return EnumEpayPayOutCode_IdserviceCode_1010
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_RSAserviceCode_1011() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_RSAserviceCode_1011)
	return EnumEpayPayOutCode_RSAserviceCode_1011
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_ServiceCode_1012() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_ServiceCode_1012)
	return EnumEpayPayOutCode_ServiceCode_1012
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Codebase64stringbaseCode_1013() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Codebase64stringbaseCode_1013)
	return EnumEpayPayOutCode_Codebase64stringbaseCode_1013
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_XmlUnmarshalserviceCode_1014() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_XmlUnmarshalserviceCode_1014)
	return EnumEpayPayOutCode_XmlUnmarshalserviceCode_1014
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Codebase64stringbaseCode_1015() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Codebase64stringbaseCode_1015)
	return EnumEpayPayOutCode_Codebase64stringbaseCode_1015
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_XmlMarshalserviceCode_1016() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_XmlMarshalserviceCode_1016)
	return EnumEpayPayOutCode_XmlMarshalserviceCode_1016
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_OAUTHCode_1017() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_OAUTHCode_1017)
	return EnumEpayPayOutCode_OAUTHCode_1017
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_ServiceCode_1018() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_ServiceCode_1018)
	return EnumEpayPayOutCode_ServiceCode_1018
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_ShopidCode_1019() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_ShopidCode_1019)
	return EnumEpayPayOutCode_ShopidCode_1019
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_JsonserviceCode_1020() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_JsonserviceCode_1020)
	return EnumEpayPayOutCode_JsonserviceCode_1020
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_AuthserviceCode_1021() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_AuthserviceCode_1021)
	return EnumEpayPayOutCode_AuthserviceCode_1021
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_DeviceIDauthserviceCode_1022() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_DeviceIDauthserviceCode_1022)
	return EnumEpayPayOutCode_DeviceIDauthserviceCode_1022
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_ServiceCode_1023() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_ServiceCode_1023)
	return EnumEpayPayOutCode_ServiceCode_1023
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_IdserviceCode_1024() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_IdserviceCode_1024)
	return EnumEpayPayOutCode_IdserviceCode_1024
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_BindXMLwebserviceCode_1025() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_BindXMLwebserviceCode_1025)
	return EnumEpayPayOutCode_BindXMLwebserviceCode_1025
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_AuthorizedRequestJSOCode_1026() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_AuthorizedRequestJSOCode_1026)
	return EnumEpayPayOutCode_AuthorizedRequestJSOCode_1026
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_BindclientIDpostLinkCode_1027() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_BindclientIDpostLinkCode_1027)
	return EnumEpayPayOutCode_BindclientIDpostLinkCode_1027
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_ClientIDpostLinkInfoCode_1028() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_ClientIDpostLinkInfoCode_1028)
	return EnumEpayPayOutCode_ClientIDpostLinkInfoCode_1028
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_MerchantIDpostLinkInCode_1029() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_MerchantIDpostLinkInCode_1029)
	return EnumEpayPayOutCode_MerchantIDpostLinkInCode_1029
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_PostlinkrabbitservicCode_1030() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_PostlinkrabbitservicCode_1030)
	return EnumEpayPayOutCode_PostlinkrabbitservicCode_1030
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_TerminalidpostLinkInCode_1031() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_TerminalidpostLinkInCode_1031)
	return EnumEpayPayOutCode_TerminalidpostLinkInCode_1031
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_CBindpostLinkInfoposCode_1032() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_CBindpostLinkInfoposCode_1032)
	return EnumEpayPayOutCode_CBindpostLinkInfoposCode_1032
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_JsonMarshalpostlinkICode_1033() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_JsonMarshalpostlinkICode_1033)
	return EnumEpayPayOutCode_JsonMarshalpostlinkICode_1033
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_ClientIDclientIDservCode_1034() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_ClientIDclientIDservCode_1034)
	return EnumEpayPayOutCode_ClientIDclientIDservCode_1034
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_ShopmerchantIDclientCode_1035() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_ShopmerchantIDclientCode_1035)
	return EnumEpayPayOutCode_ShopmerchantIDclientCode_1035
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_ShopMerchantIDclientCode_1036() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_ShopMerchantIDclientCode_1036)
	return EnumEpayPayOutCode_ShopMerchantIDclientCode_1036
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_BindinvoicewebservicCode_1037() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_BindinvoicewebservicCode_1037)
	return EnumEpayPayOutCode_BindinvoicewebservicCode_1037
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_ExpirePeriodinvoicesCode_1038() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_ExpirePeriodinvoicesCode_1038)
	return EnumEpayPayOutCode_ExpirePeriodinvoicesCode_1038
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_PostlinkRabbitservicCode_1039() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_PostlinkRabbitservicCode_1039)
	return EnumEpayPayOutCode_PostlinkRabbitservicCode_1039
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_ServiceCode_1040() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_ServiceCode_1040)
	return EnumEpayPayOutCode_ServiceCode_1040
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_JsonserviceCode_1041() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_JsonserviceCode_1041)
	return EnumEpayPayOutCode_JsonserviceCode_1041
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_EgwCode_1042() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_EgwCode_1042)
	return EnumEpayPayOutCode_EgwCode_1042
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_EmailclientsecretserCode_1043() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_EmailclientsecretserCode_1043)
	return EnumEpayPayOutCode_EmailclientsecretserCode_1043
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_EmailwebserviceCode_1044() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_EmailwebserviceCode_1044)
	return EnumEpayPayOutCode_EmailwebserviceCode_1044
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_AuthorizedRequestJSOCode_1045() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_AuthorizedRequestJSOCode_1045)
	return EnumEpayPayOutCode_AuthorizedRequestJSOCode_1045
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_JsonMarshalmerchantoCode_1046() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_JsonMarshalmerchantoCode_1046)
	return EnumEpayPayOutCode_JsonMarshalmerchantoCode_1046
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_JsonMarshalmerchantoCode_1047() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_JsonMarshalmerchantoCode_1047)
	return EnumEpayPayOutCode_JsonMarshalmerchantoCode_1047
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_BindJSONmerchantwebsCode_1048() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_BindJSONmerchantwebsCode_1048)
	return EnumEpayPayOutCode_BindJSONmerchantwebsCode_1048
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_ClientidserviceCode_1049() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_ClientidserviceCode_1049)
	return EnumEpayPayOutCode_ClientidserviceCode_1049
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_ClientScopesclientIDCode_1050() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_ClientScopesclientIDCode_1050)
	return EnumEpayPayOutCode_ClientScopesclientIDCode_1050
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_UserloginserviceCode_1051() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_UserloginserviceCode_1051)
	return EnumEpayPayOutCode_UserloginserviceCode_1051
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_ClientclientscopesusCode_1052() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_ClientclientscopesusCode_1052)
	return EnumEpayPayOutCode_ClientclientscopesusCode_1052
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_BindJSONinvoiceCode_1053() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_BindJSONinvoiceCode_1053)
	return EnumEpayPayOutCode_BindJSONinvoiceCode_1053
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_InvoiceCode_1054() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_InvoiceCode_1054)
	return EnumEpayPayOutCode_InvoiceCode_1054
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_XMLway4Code_1055() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_XMLway4Code_1055)
	return EnumEpayPayOutCode_XMLway4Code_1055
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_InvoiceCode_1056() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_InvoiceCode_1056)
	return EnumEpayPayOutCode_InvoiceCode_1056
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_1057() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_1057)
	return EnumEpayPayOutCode_Code_1057
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_1058() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_1058)
	return EnumEpayPayOutCode_Code_1058
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_1059() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_1059)
	return EnumEpayPayOutCode_Code_1059
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_CoreinvoiceCode_1060() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_CoreinvoiceCode_1060)
	return EnumEpayPayOutCode_CoreinvoiceCode_1060
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_BindJSONinvoiceInfoCode_1061() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_BindJSONinvoiceInfoCode_1061)
	return EnumEpayPayOutCode_BindJSONinvoiceInfoCode_1061
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_1062() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_1062)
	return EnumEpayPayOutCode_Code_1062
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_1063() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_1063)
	return EnumEpayPayOutCode_Code_1063
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_ScannererrCode_1064() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_ScannererrCode_1064)
	return EnumEpayPayOutCode_ScannererrCode_1064
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_BindCode_1065() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_BindCode_1065)
	return EnumEpayPayOutCode_BindCode_1065
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_ShopIdCode_1066() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_ShopIdCode_1066)
	return EnumEpayPayOutCode_ShopIdCode_1066
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_InvoiceLinkCode_1067() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_InvoiceLinkCode_1067)
	return EnumEpayPayOutCode_InvoiceLinkCode_1067
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_MarshalinvoicelinkCode_1068() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_MarshalinvoicelinkCode_1068)
	return EnumEpayPayOutCode_MarshalinvoicelinkCode_1068
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_InvoiceLinkCode_1069() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_InvoiceLinkCode_1069)
	return EnumEpayPayOutCode_InvoiceLinkCode_1069
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_InvoiceLinkCode_1070() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_InvoiceLinkCode_1070)
	return EnumEpayPayOutCode_InvoiceLinkCode_1070
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_InvoiceLinkCode_1071() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_InvoiceLinkCode_1071)
	return EnumEpayPayOutCode_InvoiceLinkCode_1071
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_PublicIDinvoiceCode_1072() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_PublicIDinvoiceCode_1072)
	return EnumEpayPayOutCode_PublicIDinvoiceCode_1072
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_ParamsinvoiceCode_1073() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_ParamsinvoiceCode_1073)
	return EnumEpayPayOutCode_ParamsinvoiceCode_1073
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_InvoiceLinkCode_1074() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_InvoiceLinkCode_1074)
	return EnumEpayPayOutCode_InvoiceLinkCode_1074
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_BindCode_1075() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_BindCode_1075)
	return EnumEpayPayOutCode_BindCode_1075
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_StrconvAtoiExpirePerCode_1076() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_StrconvAtoiExpirePerCode_1076)
	return EnumEpayPayOutCode_StrconvAtoiExpirePerCode_1076
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_InvoiceLinkCode_1077() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_InvoiceLinkCode_1077)
	return EnumEpayPayOutCode_InvoiceLinkCode_1077
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_EmailCode_1078() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_EmailCode_1078)
	return EnumEpayPayOutCode_EmailCode_1078
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_SmsCode_1079() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_SmsCode_1079)
	return EnumEpayPayOutCode_SmsCode_1079
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_InvoiceLinkCode_1080() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_InvoiceLinkCode_1080)
	return EnumEpayPayOutCode_InvoiceLinkCode_1080
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_BindCode_1081() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_BindCode_1081)
	return EnumEpayPayOutCode_BindCode_1081
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_PublicidCode_1082() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_PublicidCode_1082)
	return EnumEpayPayOutCode_PublicidCode_1082
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_NameinvoicelinkCode_1083() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_NameinvoicelinkCode_1083)
	return EnumEpayPayOutCode_NameinvoicelinkCode_1083
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_InvoiceLinkCode_1084() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_InvoiceLinkCode_1084)
	return EnumEpayPayOutCode_InvoiceLinkCode_1084
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_SearchparamsinvoicelCode_1085() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_SearchparamsinvoicelCode_1085)
	return EnumEpayPayOutCode_SearchparamsinvoicelCode_1085
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_IncorrectsearchmethoCode_1086() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_IncorrectsearchmethoCode_1086)
	return EnumEpayPayOutCode_IncorrectsearchmethoCode_1086
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_1087() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_1087)
	return EnumEpayPayOutCode_Code_1087
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_InvoiceLinkidCode_1088() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_InvoiceLinkidCode_1088)
	return EnumEpayPayOutCode_InvoiceLinkidCode_1088
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_InvoiceLinkCode_1089() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_InvoiceLinkCode_1089)
	return EnumEpayPayOutCode_InvoiceLinkCode_1089
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_1090() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_1090)
	return EnumEpayPayOutCode_Code_1090
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_InvoiceLinkCode_1091() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_InvoiceLinkCode_1091)
	return EnumEpayPayOutCode_InvoiceLinkCode_1091
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_PublicIDCode_1092() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_PublicIDCode_1092)
	return EnumEpayPayOutCode_PublicIDCode_1092
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_1093() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_1093)
	return EnumEpayPayOutCode_Code_1093
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_1096() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_1096)
	return EnumEpayPayOutCode_Code_1096
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_HomeBankCode_1097() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_HomeBankCode_1097)
	return EnumEpayPayOutCode_HomeBankCode_1097
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_EmailCode_1098() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_EmailCode_1098)
	return EnumEpayPayOutCode_EmailCode_1098
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_1099() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_1099)
	return EnumEpayPayOutCode_Code_1099
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_1100() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_1100)
	return EnumEpayPayOutCode_Code_1100
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_CoreinvoiceCode_1101() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_CoreinvoiceCode_1101)
	return EnumEpayPayOutCode_CoreinvoiceCode_1101
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_AuthorizedRequestJSOCode_1103() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_AuthorizedRequestJSOCode_1103)
	return EnumEpayPayOutCode_AuthorizedRequestJSOCode_1103
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_AuthorizedRequestJSOCode_1104() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_AuthorizedRequestJSOCode_1104)
	return EnumEpayPayOutCode_AuthorizedRequestJSOCode_1104
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_IdserviceCode_1105() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_IdserviceCode_1105)
	return EnumEpayPayOutCode_IdserviceCode_1105
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_ClientIDserviceCode_1106() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_ClientIDserviceCode_1106)
	return EnumEpayPayOutCode_ClientIDserviceCode_1106
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_TerminalXLSMIDterminCode_1107() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_TerminalXLSMIDterminCode_1107)
	return EnumEpayPayOutCode_TerminalXLSMIDterminCode_1107
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_1130() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_1130)
	return EnumEpayPayOutCode_Code_1130
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_1132() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_1132)
	return EnumEpayPayOutCode_Code_1132
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_NameCode_1133() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_NameCode_1133)
	return EnumEpayPayOutCode_NameCode_1133
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_PostlinkcorecallbackCode_1144() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_PostlinkcorecallbackCode_1144)
	return EnumEpayPayOutCode_PostlinkcorecallbackCode_1144
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_1145() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_1145)
	return EnumEpayPayOutCode_Code_1145
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_CannotsendpostlinkCode_1146() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_CannotsendpostlinkCode_1146)
	return EnumEpayPayOutCode_CannotsendpostlinkCode_1146
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_CannotsendpostlinkCode_1147() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_CannotsendpostlinkCode_1147)
	return EnumEpayPayOutCode_CannotsendpostlinkCode_1147
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_BindJsonCode_1148() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_BindJsonCode_1148)
	return EnumEpayPayOutCode_BindJsonCode_1148
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Base64Code_1149() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Base64Code_1149)
	return EnumEpayPayOutCode_Base64Code_1149
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_UnmarshalCode_1150() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_UnmarshalCode_1150)
	return EnumEpayPayOutCode_UnmarshalCode_1150
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_HttpcoremigrationCode_1151() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_HttpcoremigrationCode_1151)
	return EnumEpayPayOutCode_HttpcoremigrationCode_1151
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_1152() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_1152)
	return EnumEpayPayOutCode_Code_1152
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_UnmarshalouathCode_1153() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_UnmarshalouathCode_1153)
	return EnumEpayPayOutCode_UnmarshalouathCode_1153
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_EPAY1Code_1154() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_EPAY1Code_1154)
	return EnumEpayPayOutCode_EPAY1Code_1154
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_UnmarshalEPAY1Code_1155() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_UnmarshalEPAY1Code_1155)
	return EnumEpayPayOutCode_UnmarshalEPAY1Code_1155
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_CannotsendpostlinkCode_1156() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_CannotsendpostlinkCode_1156)
	return EnumEpayPayOutCode_CannotsendpostlinkCode_1156
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_1157() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_1157)
	return EnumEpayPayOutCode_Code_1157
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_UserserviceCode_1158() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_UserserviceCode_1158)
	return EnumEpayPayOutCode_UserserviceCode_1158
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_UserserviceCode_1159() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_UserserviceCode_1159)
	return EnumEpayPayOutCode_UserserviceCode_1159
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_UserserviceCode_1160() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_UserserviceCode_1160)
	return EnumEpayPayOutCode_UserserviceCode_1160
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_UserserviceCode_1161() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_UserserviceCode_1161)
	return EnumEpayPayOutCode_UserserviceCode_1161
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_UserserviceCode_1162() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_UserserviceCode_1162)
	return EnumEpayPayOutCode_UserserviceCode_1162
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_MerchantcompanynamesCode_1163() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_MerchantcompanynamesCode_1163)
	return EnumEpayPayOutCode_MerchantcompanynamesCode_1163
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_MerchantserviceCode_1164() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_MerchantserviceCode_1164)
	return EnumEpayPayOutCode_MerchantserviceCode_1164
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_MerchantmerchantCode_1165() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_MerchantmerchantCode_1165)
	return EnumEpayPayOutCode_MerchantmerchantCode_1165
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_UserloginuserclientCode_1166() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_UserloginuserclientCode_1166)
	return EnumEpayPayOutCode_UserloginuserclientCode_1166
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_MerchantnameuserclieCode_1167() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_MerchantnameuserclieCode_1167)
	return EnumEpayPayOutCode_MerchantnameuserclieCode_1167
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_AuthorizedRequestJSOCode_1168() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_AuthorizedRequestJSOCode_1168)
	return EnumEpayPayOutCode_AuthorizedRequestJSOCode_1168
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_MerchantnameCode_1169() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_MerchantnameCode_1169)
	return EnumEpayPayOutCode_MerchantnameCode_1169
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_EmailmerchantCode_1170() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_EmailmerchantCode_1170)
	return EnumEpayPayOutCode_EmailmerchantCode_1170
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_ShopmerchantCode_1171() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_ShopmerchantCode_1171)
	return EnumEpayPayOutCode_ShopmerchantCode_1171
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_TerminalmerchantCode_1172() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_TerminalmerchantCode_1172)
	return EnumEpayPayOutCode_TerminalmerchantCode_1172
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_UseruserCode_1173() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_UseruserCode_1173)
	return EnumEpayPayOutCode_UseruserCode_1173
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_UserclientIDCode_1174() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_UserclientIDCode_1174)
	return EnumEpayPayOutCode_UserclientIDCode_1174
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_UserclientuserCode_1175() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_UserclientuserCode_1175)
	return EnumEpayPayOutCode_UserclientuserCode_1175
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_TerminalterminalIDCode_1176() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_TerminalterminalIDCode_1176)
	return EnumEpayPayOutCode_TerminalterminalIDCode_1176
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_CurrencycurrencyNameCode_1177() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_CurrencycurrencyNameCode_1177)
	return EnumEpayPayOutCode_CurrencycurrencyNameCode_1177
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_TerminalIDCode_1178() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_TerminalIDCode_1178)
	return EnumEpayPayOutCode_TerminalIDCode_1178
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_TerminalterminalIDteCode_1179() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_TerminalterminalIDteCode_1179)
	return EnumEpayPayOutCode_TerminalterminalIDteCode_1179
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_TerminalIDCode_1180() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_TerminalIDCode_1180)
	return EnumEpayPayOutCode_TerminalIDCode_1180
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_AuthorizedRequestJSOCode_1182() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_AuthorizedRequestJSOCode_1182)
	return EnumEpayPayOutCode_AuthorizedRequestJSOCode_1182
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_1184() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_1184)
	return EnumEpayPayOutCode_Code_1184
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_TerminalCode_1185() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_TerminalCode_1185)
	return EnumEpayPayOutCode_TerminalCode_1185
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_TerminalCode_1186() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_TerminalCode_1186)
	return EnumEpayPayOutCode_TerminalCode_1186
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_1187() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_1187)
	return EnumEpayPayOutCode_Code_1187
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_MerchantContactTypenCode_1188() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_MerchantContactTypenCode_1188)
	return EnumEpayPayOutCode_MerchantContactTypenCode_1188
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_SenderrorreportcontaCode_1189() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_SenderrorreportcontaCode_1189)
	return EnumEpayPayOutCode_SenderrorreportcontaCode_1189
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_1190() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_1190)
	return EnumEpayPayOutCode_Code_1190
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_IdCode_1191() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_IdCode_1191)
	return EnumEpayPayOutCode_IdCode_1191
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_ExcelCode_1192() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_ExcelCode_1192)
	return EnumEpayPayOutCode_ExcelCode_1192
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_CurrencycurrencyIDCode_1193() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_CurrencycurrencyIDCode_1193)
	return EnumEpayPayOutCode_CurrencycurrencyIDCode_1193
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_CardTypeCardTypeIDCode_1194() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_CardTypeCardTypeIDCode_1194)
	return EnumEpayPayOutCode_CardTypeCardTypeIDCode_1194
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_EmailCode_1195() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_EmailCode_1195)
	return EnumEpayPayOutCode_EmailCode_1195
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_JsonMarshalemailCode_1196() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_JsonMarshalemailCode_1196)
	return EnumEpayPayOutCode_JsonMarshalemailCode_1196
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_AuthorizedRequestJSOCode_1197() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_AuthorizedRequestJSOCode_1197)
	return EnumEpayPayOutCode_AuthorizedRequestJSOCode_1197
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_MerchantContactTypeeCode_1198() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_MerchantContactTypeeCode_1198)
	return EnumEpayPayOutCode_MerchantContactTypeeCode_1198
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_MerchantContactemailCode_1199() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_MerchantContactemailCode_1199)
	return EnumEpayPayOutCode_MerchantContactemailCode_1199
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_ExcelfileemailCode_1200() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_ExcelfileemailCode_1200)
	return EnumEpayPayOutCode_ExcelfileemailCode_1200
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_1201() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_1201)
	return EnumEpayPayOutCode_Code_1201
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_UserloginCode_1202() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_UserloginCode_1202)
	return EnumEpayPayOutCode_UserloginCode_1202
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_RowsexcelfilereportCode_1203() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_RowsexcelfilereportCode_1203)
	return EnumEpayPayOutCode_RowsexcelfilereportCode_1203
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_UserConnectionsuserICode_1205() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_UserConnectionsuserICode_1205)
	return EnumEpayPayOutCode_UserConnectionsuserICode_1205
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_KafkaCode_1206() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_KafkaCode_1206)
	return EnumEpayPayOutCode_KafkaCode_1206
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_1207() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_1207)
	return EnumEpayPayOutCode_Code_1207
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_1208() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_1208)
	return EnumEpayPayOutCode_Code_1208
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_1209() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_1209)
	return EnumEpayPayOutCode_Code_1209
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_1210() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_1210)
	return EnumEpayPayOutCode_Code_1210
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_AuthorizedRequestMulCode_1211() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_AuthorizedRequestMulCode_1211)
	return EnumEpayPayOutCode_AuthorizedRequestMulCode_1211
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_FormatdataheaderwritCode_1212() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_FormatdataheaderwritCode_1212)
	return EnumEpayPayOutCode_FormatdataheaderwritCode_1212
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_WriterreadermultiparCode_1213() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_WriterreadermultiparCode_1213)
	return EnumEpayPayOutCode_WriterreadermultiparCode_1213
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_ApicdnCode_1214() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_ApicdnCode_1214)
	return EnumEpayPayOutCode_ApicdnCode_1214
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_LogourlshopIDCode_1215() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_LogourlshopIDCode_1215)
	return EnumEpayPayOutCode_LogourlshopIDCode_1215
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_BindJSONshopshopIDCode_1216() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_BindJSONshopshopIDCode_1216)
	return EnumEpayPayOutCode_BindJSONshopshopIDCode_1216
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_ShopIDdbshopIDCode_1217() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_ShopIDdbshopIDCode_1217)
	return EnumEpayPayOutCode_ShopIDdbshopIDCode_1217
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_JSONSamsungPayCode_1219() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_JSONSamsungPayCode_1219)
	return EnumEpayPayOutCode_JSONSamsungPayCode_1219
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_RequestJSONSamsungPaCode_1220() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_RequestJSONSamsungPaCode_1220)
	return EnumEpayPayOutCode_RequestJSONSamsungPaCode_1220
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_ShopIDshopIDCode_1221() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_ShopIDshopIDCode_1221)
	return EnumEpayPayOutCode_ShopIDshopIDCode_1221
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_TerminalshopIDCode_1222() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_TerminalshopIDCode_1222)
	return EnumEpayPayOutCode_TerminalshopIDCode_1222
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_AuthorizedRequestJSOCode_1223() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_AuthorizedRequestJSOCode_1223)
	return EnumEpayPayOutCode_AuthorizedRequestJSOCode_1223
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_ShopIDshopIDCode_1224() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_ShopIDshopIDCode_1224)
	return EnumEpayPayOutCode_ShopIDshopIDCode_1224
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_BindJSONinOutTerminaCode_1225() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_BindJSONinOutTerminaCode_1225)
	return EnumEpayPayOutCode_BindJSONinOutTerminaCode_1225
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_IdidCode_1226() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_IdidCode_1226)
	return EnumEpayPayOutCode_IdidCode_1226
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_AuthorizedRequestJSOCode_1227() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_AuthorizedRequestJSOCode_1227)
	return EnumEpayPayOutCode_AuthorizedRequestJSOCode_1227
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_TerminalterminalidCode_1228() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_TerminalterminalidCode_1228)
	return EnumEpayPayOutCode_TerminalterminalidCode_1228
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_TerminalidterminalidCode_1229() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_TerminalidterminalidCode_1229)
	return EnumEpayPayOutCode_TerminalidterminalidCode_1229
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_TerminalterminalidCode_1230() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_TerminalterminalidCode_1230)
	return EnumEpayPayOutCode_TerminalterminalidCode_1230
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_JsonMarshalterminaltCode_1231() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_JsonMarshalterminaltCode_1231)
	return EnumEpayPayOutCode_JsonMarshalterminaltCode_1231
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_PANCode_1232() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_PANCode_1232)
	return EnumEpayPayOutCode_PANCode_1232
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_SamsungPaycallbackreCode_1233() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_SamsungPaycallbackreCode_1233)
	return EnumEpayPayOutCode_SamsungPaycallbackreCode_1233
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_StaffstaffCode_1234() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_StaffstaffCode_1234)
	return EnumEpayPayOutCode_StaffstaffCode_1234
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_BindCreateMVisaTransCode_1235() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_BindCreateMVisaTransCode_1235)
	return EnumEpayPayOutCode_BindCreateMVisaTransCode_1235
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_RequestPostFormXMLCrCode_1236() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_RequestPostFormXMLCrCode_1236)
	return EnumEpayPayOutCode_RequestPostFormXMLCrCode_1236
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_RequestPostFormXMLCrCode_1237() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_RequestPostFormXMLCrCode_1237)
	return EnumEpayPayOutCode_RequestPostFormXMLCrCode_1237
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_RequestPostFormXMLCrCode_1238() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_RequestPostFormXMLCrCode_1238)
	return EnumEpayPayOutCode_RequestPostFormXMLCrCode_1238
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_RequestPostFormXMLCrCode_1239() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_RequestPostFormXMLCrCode_1239)
	return EnumEpayPayOutCode_RequestPostFormXMLCrCode_1239
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_BindGet3DSecureCode_1240() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_BindGet3DSecureCode_1240)
	return EnumEpayPayOutCode_BindGet3DSecureCode_1240
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_RequestPostFormXMLGeCode_1241() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_RequestPostFormXMLGeCode_1241)
	return EnumEpayPayOutCode_RequestPostFormXMLGeCode_1241
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_RequestPostFormXMLGeCode_1242() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_RequestPostFormXMLGeCode_1242)
	return EnumEpayPayOutCode_RequestPostFormXMLGeCode_1242
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_RequestPostFormXMLGeCode_1243() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_RequestPostFormXMLGeCode_1243)
	return EnumEpayPayOutCode_RequestPostFormXMLGeCode_1243
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_RequestPostFormXMLGeCode_1244() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_RequestPostFormXMLGeCode_1244)
	return EnumEpayPayOutCode_RequestPostFormXMLGeCode_1244
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_BindManageTransCode_1245() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_BindManageTransCode_1245)
	return EnumEpayPayOutCode_BindManageTransCode_1245
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_RequestPostFormXMLMaCode_1246() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_RequestPostFormXMLMaCode_1246)
	return EnumEpayPayOutCode_RequestPostFormXMLMaCode_1246
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_RequestPostFormXMLMaCode_1247() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_RequestPostFormXMLMaCode_1247)
	return EnumEpayPayOutCode_RequestPostFormXMLMaCode_1247
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_RequestPostFormXMLMaCode_1248() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_RequestPostFormXMLMaCode_1248)
	return EnumEpayPayOutCode_RequestPostFormXMLMaCode_1248
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_RequestPostFormXMLMaCode_1249() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_RequestPostFormXMLMaCode_1249)
	return EnumEpayPayOutCode_RequestPostFormXMLMaCode_1249
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_BindCreateTransCode_1250() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_BindCreateTransCode_1250)
	return EnumEpayPayOutCode_BindCreateTransCode_1250
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_RequestPostFormXMLCrCode_1251() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_RequestPostFormXMLCrCode_1251)
	return EnumEpayPayOutCode_RequestPostFormXMLCrCode_1251
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_RequestPostFormXMLCrCode_1252() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_RequestPostFormXMLCrCode_1252)
	return EnumEpayPayOutCode_RequestPostFormXMLCrCode_1252
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_RequestPostFormXMLCrCode_1253() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_RequestPostFormXMLCrCode_1253)
	return EnumEpayPayOutCode_RequestPostFormXMLCrCode_1253
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_RequestPostFormXMLCrCode_1254() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_RequestPostFormXMLCrCode_1254)
	return EnumEpayPayOutCode_RequestPostFormXMLCrCode_1254
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_BindRePaymentCode_1255() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_BindRePaymentCode_1255)
	return EnumEpayPayOutCode_BindRePaymentCode_1255
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_RequestPostFormXMLReCode_1256() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_RequestPostFormXMLReCode_1256)
	return EnumEpayPayOutCode_RequestPostFormXMLReCode_1256
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_RequestPostFormXMLReCode_1257() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_RequestPostFormXMLReCode_1257)
	return EnumEpayPayOutCode_RequestPostFormXMLReCode_1257
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_RequestPostFormXMLReCode_1258() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_RequestPostFormXMLReCode_1258)
	return EnumEpayPayOutCode_RequestPostFormXMLReCode_1258
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_RequestPostFormXMLReCode_1259() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_RequestPostFormXMLReCode_1259)
	return EnumEpayPayOutCode_RequestPostFormXMLReCode_1259
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_BindGetTokenCode_1260() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_BindGetTokenCode_1260)
	return EnumEpayPayOutCode_BindGetTokenCode_1260
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_RequestPostFormXMLGeCode_1261() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_RequestPostFormXMLGeCode_1261)
	return EnumEpayPayOutCode_RequestPostFormXMLGeCode_1261
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_RequestPostFormXMLGeCode_1262() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_RequestPostFormXMLGeCode_1262)
	return EnumEpayPayOutCode_RequestPostFormXMLGeCode_1262
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_RequestPostFormXMLGeCode_1263() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_RequestPostFormXMLGeCode_1263)
	return EnumEpayPayOutCode_RequestPostFormXMLGeCode_1263
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_RequestPostFormXMLGeCode_1264() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_RequestPostFormXMLGeCode_1264)
	return EnumEpayPayOutCode_RequestPostFormXMLGeCode_1264
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_BindTransactionCode_1265() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_BindTransactionCode_1265)
	return EnumEpayPayOutCode_BindTransactionCode_1265
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_RequestPostFormXMLTrCode_1266() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_RequestPostFormXMLTrCode_1266)
	return EnumEpayPayOutCode_RequestPostFormXMLTrCode_1266
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_CardsystemcommunicatCode_1267() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_CardsystemcommunicatCode_1267)
	return EnumEpayPayOutCode_CardsystemcommunicatCode_1267
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_RequestPostFormXMLTrCode_1268() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_RequestPostFormXMLTrCode_1268)
	return EnumEpayPayOutCode_RequestPostFormXMLTrCode_1268
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_RequestPostFormXMLTrCode_1269() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_RequestPostFormXMLTrCode_1269)
	return EnumEpayPayOutCode_RequestPostFormXMLTrCode_1269
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_RequestPostFormXMLTrCode_1270() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_RequestPostFormXMLTrCode_1270)
	return EnumEpayPayOutCode_RequestPostFormXMLTrCode_1270
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_BindTransactionConfiCode_1271() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_BindTransactionConfiCode_1271)
	return EnumEpayPayOutCode_BindTransactionConfiCode_1271
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_RequestPostFormXMLTrCode_1272() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_RequestPostFormXMLTrCode_1272)
	return EnumEpayPayOutCode_RequestPostFormXMLTrCode_1272
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_RequestPostFormXMLTrCode_1273() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_RequestPostFormXMLTrCode_1273)
	return EnumEpayPayOutCode_RequestPostFormXMLTrCode_1273
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_RequestPostFormXMLTrCode_1274() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_RequestPostFormXMLTrCode_1274)
	return EnumEpayPayOutCode_RequestPostFormXMLTrCode_1274
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_RequestPostFormXMLTrCode_1275() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_RequestPostFormXMLTrCode_1275)
	return EnumEpayPayOutCode_RequestPostFormXMLTrCode_1275
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_InvoiceLinksGetInvoiCode_1276() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_InvoiceLinksGetInvoiCode_1276)
	return EnumEpayPayOutCode_InvoiceLinksGetInvoiCode_1276
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_PublicIDuserIDCode_1277() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_PublicIDuserIDCode_1277)
	return EnumEpayPayOutCode_PublicIDuserIDCode_1277
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_ParamsCode_1278() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_ParamsCode_1278)
	return EnumEpayPayOutCode_ParamsCode_1278
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_PostlinkcorecallbackCode_1279() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_PostlinkcorecallbackCode_1279)
	return EnumEpayPayOutCode_PostlinkcorecallbackCode_1279
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_RabbitpostlinkCode_1280() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_RabbitpostlinkCode_1280)
	return EnumEpayPayOutCode_RabbitpostlinkCode_1280
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_PublicIDtokeninvoiceCode_1281() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_PublicIDtokeninvoiceCode_1281)
	return EnumEpayPayOutCode_PublicIDtokeninvoiceCode_1281
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_SlowpostlinkcorecallCode_1282() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_SlowpostlinkcorecallCode_1282)
	return EnumEpayPayOutCode_SlowpostlinkcorecallCode_1282
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_GetOpenWayIDHalykBonCode_1283() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_GetOpenWayIDHalykBonCode_1283)
	return EnumEpayPayOutCode_GetOpenWayIDHalykBonCode_1283
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_CardTypenilCode_1284() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_CardTypenilCode_1284)
	return EnumEpayPayOutCode_CardTypenilCode_1284
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_CardtypeCode_1285() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_CardtypeCode_1285)
	return EnumEpayPayOutCode_CardtypeCode_1285
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_PostlinkcorecallbackCode_1286() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_PostlinkcorecallbackCode_1286)
	return EnumEpayPayOutCode_PostlinkcorecallbackCode_1286
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_PostlinkCode_1287() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_PostlinkCode_1287)
	return EnumEpayPayOutCode_PostlinkCode_1287
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_PostlinkinvoiceIDCode_1288() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_PostlinkinvoiceIDCode_1288)
	return EnumEpayPayOutCode_PostlinkinvoiceIDCode_1288
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_CallbackcorecallbackCode_1289() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_CallbackcorecallbackCode_1289)
	return EnumEpayPayOutCode_CallbackcorecallbackCode_1289
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_InvoiceLinkInvoiceIDCode_1290() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_InvoiceLinkInvoiceIDCode_1290)
	return EnumEpayPayOutCode_InvoiceLinkInvoiceIDCode_1290
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_InvoiceLinkInvoiceIDCode_1291() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_InvoiceLinkInvoiceIDCode_1291)
	return EnumEpayPayOutCode_InvoiceLinkInvoiceIDCode_1291
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_InvoiceLinkinvoiceLiCode_1293() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_InvoiceLinkinvoiceLiCode_1293)
	return EnumEpayPayOutCode_InvoiceLinkinvoiceLiCode_1293
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_ErrorMessageinvoiceLCode_1294() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_ErrorMessageinvoiceLCode_1294)
	return EnumEpayPayOutCode_ErrorMessageinvoiceLCode_1294
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_TransactionStatusAUTCode_1295() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_TransactionStatusAUTCode_1295)
	return EnumEpayPayOutCode_TransactionStatusAUTCode_1295
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_FilePathFileIDCode_1296() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_FilePathFileIDCode_1296)
	return EnumEpayPayOutCode_FilePathFileIDCode_1296
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_BindChargeCode_1297() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_BindChargeCode_1297)
	return EnumEpayPayOutCode_BindChargeCode_1297
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_1298() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_1298)
	return EnumEpayPayOutCode_Code_1298
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_InformationStatementCode_1299() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_InformationStatementCode_1299)
	return EnumEpayPayOutCode_InformationStatementCode_1299
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_RefIDCode_1301() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_RefIDCode_1301)
	return EnumEpayPayOutCode_RefIDCode_1301
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_EmailCode_1302() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_EmailCode_1302)
	return EnumEpayPayOutCode_EmailCode_1302
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_StatementHistoryOmitCode_1303() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_StatementHistoryOmitCode_1303)
	return EnumEpayPayOutCode_StatementHistoryOmitCode_1303
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_StaffListCode_1304() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_StaffListCode_1304)
	return EnumEpayPayOutCode_StaffListCode_1304
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_GETCode_1305() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_GETCode_1305)
	return EnumEpayPayOutCode_GETCode_1305
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_AuthorizedRequestJSOCode_1306() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_AuthorizedRequestJSOCode_1306)
	return EnumEpayPayOutCode_AuthorizedRequestJSOCode_1306
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_1307() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_1307)
	return EnumEpayPayOutCode_Code_1307
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_CoreinvoiceinvoiceLiCode_1308() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_CoreinvoiceinvoiceLiCode_1308)
	return EnumEpayPayOutCode_CoreinvoiceinvoiceLiCode_1308
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_CoreinvoiceinvoiceLiCode_1309() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_CoreinvoiceinvoiceLiCode_1309)
	return EnumEpayPayOutCode_CoreinvoiceinvoiceLiCode_1309
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_InvoiceLinkCode_1310() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_InvoiceLinkCode_1310)
	return EnumEpayPayOutCode_InvoiceLinkCode_1310
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_InvoiceLinkinvoiceIDCode_1311() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_InvoiceLinkinvoiceIDCode_1311)
	return EnumEpayPayOutCode_InvoiceLinkinvoiceIDCode_1311
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_CoreinvoiceinvoiceInCode_1312() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_CoreinvoiceinvoiceInCode_1312)
	return EnumEpayPayOutCode_CoreinvoiceinvoiceInCode_1312
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_KafkaCode_1315() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_KafkaCode_1315)
	return EnumEpayPayOutCode_KafkaCode_1315
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_HomebankaCode_1317() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_HomebankaCode_1317)
	return EnumEpayPayOutCode_HomebankaCode_1317
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_1318() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_1318)
	return EnumEpayPayOutCode_Code_1318
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_UseraCode_1319() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_UseraCode_1319)
	return EnumEpayPayOutCode_UseraCode_1319
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_StaffupdatepasswordCode_1320() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_StaffupdatepasswordCode_1320)
	return EnumEpayPayOutCode_StaffupdatepasswordCode_1320
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_UpdateStaffRoleCode_1321() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_UpdateStaffRoleCode_1321)
	return EnumEpayPayOutCode_UpdateStaffRoleCode_1321
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_1322() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_1322)
	return EnumEpayPayOutCode_Code_1322
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_COMMITCode_1323() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_COMMITCode_1323)
	return EnumEpayPayOutCode_COMMITCode_1323
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_1324() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_1324)
	return EnumEpayPayOutCode_Code_1324
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_1325() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_1325)
	return EnumEpayPayOutCode_Code_1325
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_1326() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_1326)
	return EnumEpayPayOutCode_Code_1326
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Float64FeeAmountstriCode_1327() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Float64FeeAmountstriCode_1327)
	return EnumEpayPayOutCode_Float64FeeAmountstriCode_1327
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Float64SettlAmountstCode_1328() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Float64SettlAmountstCode_1328)
	return EnumEpayPayOutCode_Float64SettlAmountstCode_1328
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Float64TrAmountstrinCode_1329() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Float64TrAmountstrinCode_1329)
	return EnumEpayPayOutCode_Float64TrAmountstrinCode_1329
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_1330() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_1330)
	return EnumEpayPayOutCode_Code_1330
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_1331() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_1331)
	return EnumEpayPayOutCode_Code_1331
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_ParseMediaTypeCode_1332() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_ParseMediaTypeCode_1332)
	return EnumEpayPayOutCode_ParseMediaTypeCode_1332
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_CreateFileCode_1333() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_CreateFileCode_1333)
	return EnumEpayPayOutCode_CreateFileCode_1333
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_EmailCode_1334() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_EmailCode_1334)
	return EnumEpayPayOutCode_EmailCode_1334
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_HBCode_1335() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_HBCode_1335)
	return EnumEpayPayOutCode_HBCode_1335
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_1336() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_1336)
	return EnumEpayPayOutCode_Code_1336
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_1337() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_1337)
	return EnumEpayPayOutCode_Code_1337
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_ACTIVECode_1338() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_ACTIVECode_1338)
	return EnumEpayPayOutCode_ACTIVECode_1338
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_CHARGEDCode_1339() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_CHARGEDCode_1339)
	return EnumEpayPayOutCode_CHARGEDCode_1339
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_MerchantterminaltermCode_1340() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_MerchantterminaltermCode_1340)
	return EnumEpayPayOutCode_MerchantterminaltermCode_1340
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_PostingDatestringOrdCode_1341() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_PostingDatestringOrdCode_1341)
	return EnumEpayPayOutCode_PostingDatestringOrdCode_1341
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_HBCode_1342() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_HBCode_1342)
	return EnumEpayPayOutCode_HBCode_1342
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_FTPCode_1343() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_FTPCode_1343)
	return EnumEpayPayOutCode_FTPCode_1343
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_JobExecutiongocronCode_1344() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_JobExecutiongocronCode_1344)
	return EnumEpayPayOutCode_JobExecutiongocronCode_1344
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Float64FeeAmountstriCode_1346() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Float64FeeAmountstriCode_1346)
	return EnumEpayPayOutCode_Float64FeeAmountstriCode_1346
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Float64SettlAmountstCode_1347() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Float64SettlAmountstCode_1347)
	return EnumEpayPayOutCode_Float64SettlAmountstCode_1347
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Float64TrAmountstrinCode_1348() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Float64TrAmountstrinCode_1348)
	return EnumEpayPayOutCode_Float64TrAmountstrinCode_1348
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_MerchantPublicIDCode_1361() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_MerchantPublicIDCode_1361)
	return EnumEpayPayOutCode_MerchantPublicIDCode_1361
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_MerchantInfoPublicIDCode_1366() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_MerchantInfoPublicIDCode_1366)
	return EnumEpayPayOutCode_MerchantInfoPublicIDCode_1366
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_PublicIDCode_1367() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_PublicIDCode_1367)
	return EnumEpayPayOutCode_PublicIDCode_1367
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_1368() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_1368)
	return EnumEpayPayOutCode_Code_1368
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_1369() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_1369)
	return EnumEpayPayOutCode_Code_1369
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_1370() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_1370)
	return EnumEpayPayOutCode_Code_1370
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_AuthorizedRequestJSOCode_1371() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_AuthorizedRequestJSOCode_1371)
	return EnumEpayPayOutCode_AuthorizedRequestJSOCode_1371
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_CoreGetCardCode_1372() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_CoreGetCardCode_1372)
	return EnumEpayPayOutCode_CoreGetCardCode_1372
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_1373() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_1373)
	return EnumEpayPayOutCode_Code_1373
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_OauthCode_1374() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_OauthCode_1374)
	return EnumEpayPayOutCode_OauthCode_1374
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_RequestClientCode_1375() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_RequestClientCode_1375)
	return EnumEpayPayOutCode_RequestClientCode_1375
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_1376() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_1376)
	return EnumEpayPayOutCode_Code_1376
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_1378() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_1378)
	return EnumEpayPayOutCode_Code_1378
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_ScopeCode_1379() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_ScopeCode_1379)
	return EnumEpayPayOutCode_ScopeCode_1379
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_ScopeCode_1380() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_ScopeCode_1380)
	return EnumEpayPayOutCode_ScopeCode_1380
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_ClientScopesCode_1381() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_ClientScopesCode_1381)
	return EnumEpayPayOutCode_ClientScopesCode_1381
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_1382() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_1382)
	return EnumEpayPayOutCode_Code_1382
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_1383() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_1383)
	return EnumEpayPayOutCode_Code_1383
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_1384() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_1384)
	return EnumEpayPayOutCode_Code_1384
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_1385() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_1385)
	return EnumEpayPayOutCode_Code_1385
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_1386() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_1386)
	return EnumEpayPayOutCode_Code_1386
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_HomeBankCode_1387() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_HomeBankCode_1387)
	return EnumEpayPayOutCode_HomeBankCode_1387
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_HMCode_1388() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_HMCode_1388)
	return EnumEpayPayOutCode_HMCode_1388
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_RequestClientUpdateCode_1389() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_RequestClientUpdateCode_1389)
	return EnumEpayPayOutCode_RequestClientUpdateCode_1389
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_ClientIDCode_1390() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_ClientIDCode_1390)
	return EnumEpayPayOutCode_ClientIDCode_1390
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_GetClientclientCode_1391() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_GetClientclientCode_1391)
	return EnumEpayPayOutCode_GetClientclientCode_1391
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_GetClientScopeCode_1392() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_GetClientScopeCode_1392)
	return EnumEpayPayOutCode_GetClientScopeCode_1392
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_UpdateScopeCode_1393() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_UpdateScopeCode_1393)
	return EnumEpayPayOutCode_UpdateScopeCode_1393
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_ScopesCode_1394() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_ScopesCode_1394)
	return EnumEpayPayOutCode_ScopesCode_1394
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_ClientCode_1395() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_ClientCode_1395)
	return EnumEpayPayOutCode_ClientCode_1395
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_ScopeCode_1396() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_ScopeCode_1396)
	return EnumEpayPayOutCode_ScopeCode_1396
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_ClientScopesCode_1397() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_ClientScopesCode_1397)
	return EnumEpayPayOutCode_ClientScopesCode_1397
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_ReadererrorCode_1398() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_ReadererrorCode_1398)
	return EnumEpayPayOutCode_ReadererrorCode_1398
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_TerminaldbmerchantIDCode_1400() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_TerminaldbmerchantIDCode_1400)
	return EnumEpayPayOutCode_TerminaldbmerchantIDCode_1400
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_ClientclientIDdbTildCode_1403() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_ClientclientIDdbTildCode_1403)
	return EnumEpayPayOutCode_ClientclientIDdbTildCode_1403
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_ClientclientIDdbCode_1405() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_ClientclientIDdbCode_1405)
	return EnumEpayPayOutCode_ClientclientIDdbCode_1405
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_ClientIPTildaCode_1406() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_ClientIPTildaCode_1406)
	return EnumEpayPayOutCode_ClientIPTildaCode_1406
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_BindmultipartformdatCode_1407() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_BindmultipartformdatCode_1407)
	return EnumEpayPayOutCode_BindmultipartformdatCode_1407
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_TildaTildaCode_1408() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_TildaTildaCode_1408)
	return EnumEpayPayOutCode_TildaTildaCode_1408
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_TildaCode_1409() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_TildaCode_1409)
	return EnumEpayPayOutCode_TildaCode_1409
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Float64amountTildaCode_1410() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Float64amountTildaCode_1410)
	return EnumEpayPayOutCode_Float64amountTildaCode_1410
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_AuthorizedRequestJSOCode_1411() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_AuthorizedRequestJSOCode_1411)
	return EnumEpayPayOutCode_AuthorizedRequestJSOCode_1411
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_SourceCode_1412() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_SourceCode_1412)
	return EnumEpayPayOutCode_SourceCode_1412
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_CallbackcorecallbackCode_1413() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_CallbackcorecallbackCode_1413)
	return EnumEpayPayOutCode_CallbackcorecallbackCode_1413
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_PostlinkTildaCode_1414() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_PostlinkTildaCode_1414)
	return EnumEpayPayOutCode_PostlinkTildaCode_1414
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_1415() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_1415)
	return EnumEpayPayOutCode_Code_1415
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_1416() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_1416)
	return EnumEpayPayOutCode_Code_1416
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_AuthorizedRequestJSOCode_1418() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_AuthorizedRequestJSOCode_1418)
	return EnumEpayPayOutCode_AuthorizedRequestJSOCode_1418
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_CoreStatementCode_1419() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_CoreStatementCode_1419)
	return EnumEpayPayOutCode_CoreStatementCode_1419
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_1421() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_1421)
	return EnumEpayPayOutCode_Code_1421
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_1422() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_1422)
	return EnumEpayPayOutCode_Code_1422
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_MerchantcompanynameCode_1423() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_MerchantcompanynameCode_1423)
	return EnumEpayPayOutCode_MerchantcompanynameCode_1423
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_ClientTypeCode_1424() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_ClientTypeCode_1424)
	return EnumEpayPayOutCode_ClientTypeCode_1424
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_ClientTypenameTildaCode_1425() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_ClientTypenameTildaCode_1425)
	return EnumEpayPayOutCode_ClientTypenameTildaCode_1425
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_ClientTypeTildaClienCode_1426() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_ClientTypeTildaClienCode_1426)
	return EnumEpayPayOutCode_ClientTypeTildaClienCode_1426
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_BindJSONBindJSONCode_1427() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_BindJSONBindJSONCode_1427)
	return EnumEpayPayOutCode_BindJSONBindJSONCode_1427
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_ParsingerrorCode_1428() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_ParsingerrorCode_1428)
	return EnumEpayPayOutCode_ParsingerrorCode_1428
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_1429() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_1429)
	return EnumEpayPayOutCode_Code_1429
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_1430() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_1430)
	return EnumEpayPayOutCode_Code_1430
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_1431() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_1431)
	return EnumEpayPayOutCode_Code_1431
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_LIKECode_1432() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_LIKECode_1432)
	return EnumEpayPayOutCode_LIKECode_1432
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_BETWEENCode_1433() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_BETWEENCode_1433)
	return EnumEpayPayOutCode_BETWEENCode_1433
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_1434() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_1434)
	return EnumEpayPayOutCode_Code_1434
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_1436() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_1436)
	return EnumEpayPayOutCode_Code_1436
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_1437() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_1437)
	return EnumEpayPayOutCode_Code_1437
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_1438() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_1438)
	return EnumEpayPayOutCode_Code_1438
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_CountCode_1439() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_CountCode_1439)
	return EnumEpayPayOutCode_CountCode_1439
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_1440() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_1440)
	return EnumEpayPayOutCode_Code_1440
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_1441() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_1441)
	return EnumEpayPayOutCode_Code_1441
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_ReferenceEPAYCode_1442() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_ReferenceEPAYCode_1442)
	return EnumEpayPayOutCode_ReferenceEPAYCode_1442
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_1443() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_1443)
	return EnumEpayPayOutCode_Code_1443
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_1444() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_1444)
	return EnumEpayPayOutCode_Code_1444
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_1445() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_1445)
	return EnumEpayPayOutCode_Code_1445
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_HMHMBCode_1446() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_HMHMBCode_1446)
	return EnumEpayPayOutCode_HMHMBCode_1446
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_JSONJSONCode_1447() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_JSONJSONCode_1447)
	return EnumEpayPayOutCode_JSONJSONCode_1447
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_UrlbodyCode_1448() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_UrlbodyCode_1448)
	return EnumEpayPayOutCode_UrlbodyCode_1448
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_1449() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_1449)
	return EnumEpayPayOutCode_Code_1449
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_GetUseScopeByClientCode_1451() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_GetUseScopeByClientCode_1451)
	return EnumEpayPayOutCode_GetUseScopeByClientCode_1451
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_1452() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_1452)
	return EnumEpayPayOutCode_Code_1452
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_JSONUnmarshalCode_1453() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_JSONUnmarshalCode_1453)
	return EnumEpayPayOutCode_JSONUnmarshalCode_1453
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_1454() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_1454)
	return EnumEpayPayOutCode_Code_1454
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_1455() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_1455)
	return EnumEpayPayOutCode_Code_1455
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_WebsocketCode_1457() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_WebsocketCode_1457)
	return EnumEpayPayOutCode_WebsocketCode_1457
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_WebsocketCode_1458() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_WebsocketCode_1458)
	return EnumEpayPayOutCode_WebsocketCode_1458
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_WebsocketCode_1459() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_WebsocketCode_1459)
	return EnumEpayPayOutCode_WebsocketCode_1459
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_WebsocketCode_1460() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_WebsocketCode_1460)
	return EnumEpayPayOutCode_WebsocketCode_1460
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_WebsocketCode_1461() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_WebsocketCode_1461)
	return EnumEpayPayOutCode_WebsocketCode_1461
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_WebsocketCode_1462() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_WebsocketCode_1462)
	return EnumEpayPayOutCode_WebsocketCode_1462
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_1463() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_1463)
	return EnumEpayPayOutCode_Code_1463
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_1464() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_1464)
	return EnumEpayPayOutCode_Code_1464
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_1465() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_1465)
	return EnumEpayPayOutCode_Code_1465
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_1466() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_1466)
	return EnumEpayPayOutCode_Code_1466
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_1467() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_1467)
	return EnumEpayPayOutCode_Code_1467
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_UpdateMerchantCode_1468() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_UpdateMerchantCode_1468)
	return EnumEpayPayOutCode_UpdateMerchantCode_1468
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_UpdateMerchantShopCode_1469() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_UpdateMerchantShopCode_1469)
	return EnumEpayPayOutCode_UpdateMerchantShopCode_1469
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_UpdateMerchantTerminCode_1470() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_UpdateMerchantTerminCode_1470)
	return EnumEpayPayOutCode_UpdateMerchantTerminCode_1470
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_1471() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_1471)
	return EnumEpayPayOutCode_Code_1471
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_1472() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_1472)
	return EnumEpayPayOutCode_Code_1472
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_EmailCode_1473() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_EmailCode_1473)
	return EnumEpayPayOutCode_EmailCode_1473
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_NameserviceCode_1475() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_NameserviceCode_1475)
	return EnumEpayPayOutCode_NameserviceCode_1475
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_IdparamsCode_1476() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_IdparamsCode_1476)
	return EnumEpayPayOutCode_IdparamsCode_1476
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_MerchantpublicIDCode_1477() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_MerchantpublicIDCode_1477)
	return EnumEpayPayOutCode_MerchantpublicIDCode_1477
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_TerminalmerchantIDCode_1478() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_TerminalmerchantIDCode_1478)
	return EnumEpayPayOutCode_TerminalmerchantIDCode_1478
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_ShopidshopwebserviceCode_1479() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_ShopidshopwebserviceCode_1479)
	return EnumEpayPayOutCode_ShopidshopwebserviceCode_1479
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_1480() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_1480)
	return EnumEpayPayOutCode_Code_1480
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_1481() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_1481)
	return EnumEpayPayOutCode_Code_1481
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_IdparamsCode_1482() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_IdparamsCode_1482)
	return EnumEpayPayOutCode_IdparamsCode_1482
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_JSONJSONCode_1483() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_JSONJSONCode_1483)
	return EnumEpayPayOutCode_JSONJSONCode_1483
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_JSONjsonMarshalJSONCode_1484() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_JSONjsonMarshalJSONCode_1484)
	return EnumEpayPayOutCode_JSONjsonMarshalJSONCode_1484
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_IdparamsCode_1486() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_IdparamsCode_1486)
	return EnumEpayPayOutCode_IdparamsCode_1486
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_1487() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_1487)
	return EnumEpayPayOutCode_Code_1487
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_1488() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_1488)
	return EnumEpayPayOutCode_Code_1488
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_1489() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_1489)
	return EnumEpayPayOutCode_Code_1489
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_1490() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_1490)
	return EnumEpayPayOutCode_Code_1490
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_PaymentRequestCode_1491() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_PaymentRequestCode_1491)
	return EnumEpayPayOutCode_PaymentRequestCode_1491
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_1492() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_1492)
	return EnumEpayPayOutCode_Code_1492
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_JsonUnmarshalCode_1493() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_JsonUnmarshalCode_1493)
	return EnumEpayPayOutCode_JsonUnmarshalCode_1493
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_JsonMarshalCode_1494() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_JsonMarshalCode_1494)
	return EnumEpayPayOutCode_JsonMarshalCode_1494
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_1495() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_1495)
	return EnumEpayPayOutCode_Code_1495
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_JSONJSONCode_1496() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_JSONJSONCode_1496)
	return EnumEpayPayOutCode_JSONJSONCode_1496
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_1497() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_1497)
	return EnumEpayPayOutCode_Code_1497
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_1498() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_1498)
	return EnumEpayPayOutCode_Code_1498
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_AuthorizedRequestJSOCode_1499() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_AuthorizedRequestJSOCode_1499)
	return EnumEpayPayOutCode_AuthorizedRequestJSOCode_1499
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_AuthorizedRequestJSOCode_1500() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_AuthorizedRequestJSOCode_1500)
	return EnumEpayPayOutCode_AuthorizedRequestJSOCode_1500
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_AuthorizedRequestJSOCode_1502() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_AuthorizedRequestJSOCode_1502)
	return EnumEpayPayOutCode_AuthorizedRequestJSOCode_1502
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_AuthorizedRequestJSOCode_1503() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_AuthorizedRequestJSOCode_1503)
	return EnumEpayPayOutCode_AuthorizedRequestJSOCode_1503
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_AuthorizedRequestJSOCode_1504() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_AuthorizedRequestJSOCode_1504)
	return EnumEpayPayOutCode_AuthorizedRequestJSOCode_1504
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_TerminaluuidCode_1505() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_TerminaluuidCode_1505)
	return EnumEpayPayOutCode_TerminaluuidCode_1505
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_BindgrafanaCode_1506() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_BindgrafanaCode_1506)
	return EnumEpayPayOutCode_BindgrafanaCode_1506
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_HttpgrafanaCode_1507() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_HttpgrafanaCode_1507)
	return EnumEpayPayOutCode_HttpgrafanaCode_1507
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_UnauthorizedCode_1508() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_UnauthorizedCode_1508)
	return EnumEpayPayOutCode_UnauthorizedCode_1508
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_BadtokenCode_1509() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_BadtokenCode_1509)
	return EnumEpayPayOutCode_BadtokenCode_1509
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_JsonUnmarshalCode_1510() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_JsonUnmarshalCode_1510)
	return EnumEpayPayOutCode_JsonUnmarshalCode_1510
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_JsonMarshalCode_1511() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_JsonMarshalCode_1511)
	return EnumEpayPayOutCode_JsonMarshalCode_1511
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_RequestJSONgeoIPCode_1512() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_RequestJSONgeoIPCode_1512)
	return EnumEpayPayOutCode_RequestJSONgeoIPCode_1512
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_1513() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_1513)
	return EnumEpayPayOutCode_Code_1513
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_JsonUnmarshalCode_1514() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_JsonUnmarshalCode_1514)
	return EnumEpayPayOutCode_JsonUnmarshalCode_1514
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_JsonMarshalCode_1515() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_JsonMarshalCode_1515)
	return EnumEpayPayOutCode_JsonMarshalCode_1515
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_1516() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_1516)
	return EnumEpayPayOutCode_Code_1516
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_QrTransactionInfoCode_1517() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_QrTransactionInfoCode_1517)
	return EnumEpayPayOutCode_QrTransactionInfoCode_1517
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_CardTypeIDCode_1518() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_CardTypeIDCode_1518)
	return EnumEpayPayOutCode_CardTypeIDCode_1518
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_CurrencyIDCode_1519() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_CurrencyIDCode_1519)
	return EnumEpayPayOutCode_CurrencyIDCode_1519
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_StatusIDCode_1520() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_StatusIDCode_1520)
	return EnumEpayPayOutCode_StatusIDCode_1520
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_1521() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_1521)
	return EnumEpayPayOutCode_Code_1521
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_1522() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_1522)
	return EnumEpayPayOutCode_Code_1522
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_1523() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_1523)
	return EnumEpayPayOutCode_Code_1523
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_1524() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_1524)
	return EnumEpayPayOutCode_Code_1524
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_ShopIDmerchantIDclieCode_1525() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_ShopIDmerchantIDclieCode_1525)
	return EnumEpayPayOutCode_ShopIDmerchantIDclieCode_1525
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_1526() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_1526)
	return EnumEpayPayOutCode_Code_1526
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_QRCode_1527() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_QRCode_1527)
	return EnumEpayPayOutCode_QRCode_1527
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_SourceListIDCode_1528() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_SourceListIDCode_1528)
	return EnumEpayPayOutCode_SourceListIDCode_1528
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_AuthorizedRequestJSOCode_1529() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_AuthorizedRequestJSOCode_1529)
	return EnumEpayPayOutCode_AuthorizedRequestJSOCode_1529
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_XLSIDCode_1530() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_XLSIDCode_1530)
	return EnumEpayPayOutCode_XLSIDCode_1530
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_JSONJSONCode_1531() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_JSONJSONCode_1531)
	return EnumEpayPayOutCode_JSONJSONCode_1531
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_RedispartialTransactCode_1532() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_RedispartialTransactCode_1532)
	return EnumEpayPayOutCode_RedispartialTransactCode_1532
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_1533() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_1533)
	return EnumEpayPayOutCode_Code_1533
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_1534() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_1534)
	return EnumEpayPayOutCode_Code_1534
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_1535() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_1535)
	return EnumEpayPayOutCode_Code_1535
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_1536() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_1536)
	return EnumEpayPayOutCode_Code_1536
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_1537() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_1537)
	return EnumEpayPayOutCode_Code_1537
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_RedisinvoiceIdCode_1538() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_RedisinvoiceIdCode_1538)
	return EnumEpayPayOutCode_RedisinvoiceIdCode_1538
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Epay1Code_1539() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Epay1Code_1539)
	return EnumEpayPayOutCode_Epay1Code_1539
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Epay1Code_1540() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Epay1Code_1540)
	return EnumEpayPayOutCode_Epay1Code_1540
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Epay1Code_1541() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Epay1Code_1541)
	return EnumEpayPayOutCode_Epay1Code_1541
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Epay1Code_1542() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Epay1Code_1542)
	return EnumEpayPayOutCode_Epay1Code_1542
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_TransactionTypeIDCode_1543() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_TransactionTypeIDCode_1543)
	return EnumEpayPayOutCode_TransactionTypeIDCode_1543
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_ClientTypeIDCode_1544() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_ClientTypeIDCode_1544)
	return EnumEpayPayOutCode_ClientTypeIDCode_1544
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_JsonMarshalucsCode_1545() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_JsonMarshalucsCode_1545)
	return EnumEpayPayOutCode_JsonMarshalucsCode_1545
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_1546() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_1546)
	return EnumEpayPayOutCode_Code_1546
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_ScopeCode_1547() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_ScopeCode_1547)
	return EnumEpayPayOutCode_ScopeCode_1547
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_RedisqrstatusCode_1548() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_RedisqrstatusCode_1548)
	return EnumEpayPayOutCode_RedisqrstatusCode_1548
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_AuthorizedRequestJSOCode_1549() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_AuthorizedRequestJSOCode_1549)
	return EnumEpayPayOutCode_AuthorizedRequestJSOCode_1549
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_1550() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_1550)
	return EnumEpayPayOutCode_Code_1550
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_RedisrealIPCode_1551() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_RedisrealIPCode_1551)
	return EnumEpayPayOutCode_RedisrealIPCode_1551
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_RequestJSONcoreqrQRSCode_1552() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_RequestJSONcoreqrQRSCode_1552)
	return EnumEpayPayOutCode_RequestJSONcoreqrQRSCode_1552
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_KafkaReadMessageCode_1553() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_KafkaReadMessageCode_1553)
	return EnumEpayPayOutCode_KafkaReadMessageCode_1553
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_JsonUnmarshalkafkameCode_1554() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_JsonUnmarshalkafkameCode_1554)
	return EnumEpayPayOutCode_JsonUnmarshalkafkameCode_1554
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_JSONjsonMarshalJSONCode_1555() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_JSONjsonMarshalJSONCode_1555)
	return EnumEpayPayOutCode_JSONjsonMarshalJSONCode_1555
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_KafkaCode_1556() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_KafkaCode_1556)
	return EnumEpayPayOutCode_KafkaCode_1556
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_SetOffsetkafkaCode_1557() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_SetOffsetkafkaCode_1557)
	return EnumEpayPayOutCode_SetOffsetkafkaCode_1557
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_1558() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_1558)
	return EnumEpayPayOutCode_Code_1558
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_1559() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_1559)
	return EnumEpayPayOutCode_Code_1559
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_1560() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_1560)
	return EnumEpayPayOutCode_Code_1560
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_1561() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_1561)
	return EnumEpayPayOutCode_Code_1561
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_HTTPCode_1562() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_HTTPCode_1562)
	return EnumEpayPayOutCode_HTTPCode_1562
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_HTTPCode_1563() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_HTTPCode_1563)
	return EnumEpayPayOutCode_HTTPCode_1563
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_HTTPCode_1564() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_HTTPCode_1564)
	return EnumEpayPayOutCode_HTTPCode_1564
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_HTTPCode_1565() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_HTTPCode_1565)
	return EnumEpayPayOutCode_HTTPCode_1565
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_AUTHCode_1566() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_AUTHCode_1566)
	return EnumEpayPayOutCode_AUTHCode_1566
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_EGateWayCode_1567() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_EGateWayCode_1567)
	return EnumEpayPayOutCode_EGateWayCode_1567
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_EGateWayCode_1568() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_EGateWayCode_1568)
	return EnumEpayPayOutCode_EGateWayCode_1568
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_1569() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_1569)
	return EnumEpayPayOutCode_Code_1569
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_JsonCode_1570() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_JsonCode_1570)
	return EnumEpayPayOutCode_JsonCode_1570
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_1571() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_1571)
	return EnumEpayPayOutCode_Code_1571
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_1572() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_1572)
	return EnumEpayPayOutCode_Code_1572
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_1578() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_1578)
	return EnumEpayPayOutCode_Code_1578
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_1579() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_1579)
	return EnumEpayPayOutCode_Code_1579
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_ReadAllfromRequestBoCode_1604() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_ReadAllfromRequestBoCode_1604)
	return EnumEpayPayOutCode_ReadAllfromRequestBoCode_1604
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_ParseFloatrefundCode_1605() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_ParseFloatrefundCode_1605)
	return EnumEpayPayOutCode_ParseFloatrefundCode_1605
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_AmountbodyurlrefundCode_1606() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_AmountbodyurlrefundCode_1606)
	return EnumEpayPayOutCode_AmountbodyurlrefundCode_1606
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_1607() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_1607)
	return EnumEpayPayOutCode_Code_1607
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_ChargeCode_1608() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_ChargeCode_1608)
	return EnumEpayPayOutCode_ChargeCode_1608
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_CancelCode_1609() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_CancelCode_1609)
	return EnumEpayPayOutCode_CancelCode_1609
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_RefundCode_1610() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_RefundCode_1610)
	return EnumEpayPayOutCode_RefundCode_1610
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_HttpCode_1611() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_HttpCode_1611)
	return EnumEpayPayOutCode_HttpCode_1611
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_1612() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_1612)
	return EnumEpayPayOutCode_Code_1612
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_1614() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_1614)
	return EnumEpayPayOutCode_Code_1614
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_TerminalIDCode_1615() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_TerminalIDCode_1615)
	return EnumEpayPayOutCode_TerminalIDCode_1615
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_1616() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_1616)
	return EnumEpayPayOutCode_Code_1616
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_JsonUnmarshalbinCode_1617() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_JsonUnmarshalbinCode_1617)
	return EnumEpayPayOutCode_JsonUnmarshalbinCode_1617
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_JsonMarshalCode_1618() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_JsonMarshalCode_1618)
	return EnumEpayPayOutCode_JsonMarshalCode_1618
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_1619() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_1619)
	return EnumEpayPayOutCode_Code_1619
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_JSONapiosuvoxCode_1620() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_JSONapiosuvoxCode_1620)
	return EnumEpayPayOutCode_JSONapiosuvoxCode_1620
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_ApiosuvoxCode_1621() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_ApiosuvoxCode_1621)
	return EnumEpayPayOutCode_ApiosuvoxCode_1621
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_ApiosuvoxHTTPCODE200Code_1622() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_ApiosuvoxHTTPCODE200Code_1622)
	return EnumEpayPayOutCode_ApiosuvoxHTTPCODE200Code_1622
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_1623() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_1623)
	return EnumEpayPayOutCode_Code_1623
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_SourceListCode_1624() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_SourceListCode_1624)
	return EnumEpayPayOutCode_SourceListCode_1624
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_1636() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_1636)
	return EnumEpayPayOutCode_Code_1636
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_1637() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_1637)
	return EnumEpayPayOutCode_Code_1637
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_OsuvoxCode_1638() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_OsuvoxCode_1638)
	return EnumEpayPayOutCode_OsuvoxCode_1638
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_InvalidCardIDCode_1639() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_InvalidCardIDCode_1639)
	return EnumEpayPayOutCode_InvalidCardIDCode_1639
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_OsuvoxCode_1640() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_OsuvoxCode_1640)
	return EnumEpayPayOutCode_OsuvoxCode_1640
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_1641() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_1641)
	return EnumEpayPayOutCode_Code_1641
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_OsuvoxCode_1642() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_OsuvoxCode_1642)
	return EnumEpayPayOutCode_OsuvoxCode_1642
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_SingleMessageSchemeCode_1650() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_SingleMessageSchemeCode_1650)
	return EnumEpayPayOutCode_SingleMessageSchemeCode_1650
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_1651() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_1651)
	return EnumEpayPayOutCode_Code_1651
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_OTPCode_1652() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_OTPCode_1652)
	return EnumEpayPayOutCode_OTPCode_1652
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_OTPCode_1653() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_OTPCode_1653)
	return EnumEpayPayOutCode_OTPCode_1653
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_OTPcodeCode_1654() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_OTPcodeCode_1654)
	return EnumEpayPayOutCode_OTPcodeCode_1654
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_OTPcodeCode_1655() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_OTPcodeCode_1655)
	return EnumEpayPayOutCode_OTPcodeCode_1655
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_RedisterminaluuidinvCode_1656() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_RedisterminaluuidinvCode_1656)
	return EnumEpayPayOutCode_RedisterminaluuidinvCode_1656
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_InvalidterminalIdCode_1658() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_InvalidterminalIdCode_1658)
	return EnumEpayPayOutCode_InvalidterminalIdCode_1658
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_InvalidAmoutCode_1659() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_InvalidAmoutCode_1659)
	return EnumEpayPayOutCode_InvalidAmoutCode_1659
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_1660() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_1660)
	return EnumEpayPayOutCode_Code_1660
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_1661() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_1661)
	return EnumEpayPayOutCode_Code_1661
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_AuthorizedRequestJSOCode_1663() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_AuthorizedRequestJSOCode_1663)
	return EnumEpayPayOutCode_AuthorizedRequestJSOCode_1663
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_BindGetTokenByCardCode_1664() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_BindGetTokenByCardCode_1664)
	return EnumEpayPayOutCode_BindGetTokenByCardCode_1664
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_RequestPostFormXMLGeCode_1665() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_RequestPostFormXMLGeCode_1665)
	return EnumEpayPayOutCode_RequestPostFormXMLGeCode_1665
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_RequestPostFormXMLGeCode_1666() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_RequestPostFormXMLGeCode_1666)
	return EnumEpayPayOutCode_RequestPostFormXMLGeCode_1666
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_RequestPostFormXMLGeCode_1667() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_RequestPostFormXMLGeCode_1667)
	return EnumEpayPayOutCode_RequestPostFormXMLGeCode_1667
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_RequestPostFormXMLGeCode_1668() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_RequestPostFormXMLGeCode_1668)
	return EnumEpayPayOutCode_RequestPostFormXMLGeCode_1668
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Codebase64stringbaseCode_1670() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Codebase64stringbaseCode_1670)
	return EnumEpayPayOutCode_Codebase64stringbaseCode_1670
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_AuthorizedRequestJSOCode_1671() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_AuthorizedRequestJSOCode_1671)
	return EnumEpayPayOutCode_AuthorizedRequestJSOCode_1671
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_CardCode_1672() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_CardCode_1672)
	return EnumEpayPayOutCode_CardCode_1672
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_HomebankPayOsuvoxCarCode_1673() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_HomebankPayOsuvoxCarCode_1673)
	return EnumEpayPayOutCode_HomebankPayOsuvoxCarCode_1673
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_OsuvoxCode_1675() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_OsuvoxCode_1675)
	return EnumEpayPayOutCode_OsuvoxCode_1675
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_OsuvoxCode_1677() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_OsuvoxCode_1677)
	return EnumEpayPayOutCode_OsuvoxCode_1677
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_OsuvoxCode_1679() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_OsuvoxCode_1679)
	return EnumEpayPayOutCode_OsuvoxCode_1679
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_OsuvoxCode_1680() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_OsuvoxCode_1680)
	return EnumEpayPayOutCode_OsuvoxCode_1680
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_OsuvoxCode_1681() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_OsuvoxCode_1681)
	return EnumEpayPayOutCode_OsuvoxCode_1681
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_1682() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_1682)
	return EnumEpayPayOutCode_Code_1682
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_OsuvoxCode_1683() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_OsuvoxCode_1683)
	return EnumEpayPayOutCode_OsuvoxCode_1683
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_OsuvoxCode_1685() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_OsuvoxCode_1685)
	return EnumEpayPayOutCode_OsuvoxCode_1685
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_TransactionCode_1690() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_TransactionCode_1690)
	return EnumEpayPayOutCode_TransactionCode_1690
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_CouldnotgetphoneNumbCode_1692() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_CouldnotgetphoneNumbCode_1692)
	return EnumEpayPayOutCode_CouldnotgetphoneNumbCode_1692
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_1693() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_1693)
	return EnumEpayPayOutCode_Code_1693
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_1694() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_1694)
	return EnumEpayPayOutCode_Code_1694
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_OsuvoxCode_1695() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_OsuvoxCode_1695)
	return EnumEpayPayOutCode_OsuvoxCode_1695
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_XMLbodyCode_1699() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_XMLbodyCode_1699)
	return EnumEpayPayOutCode_XMLbodyCode_1699
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_XMLMarhsalswitchpaymCode_1700() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_XMLMarhsalswitchpaymCode_1700)
	return EnumEpayPayOutCode_XMLMarhsalswitchpaymCode_1700
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_TerminalIdCode_1703() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_TerminalIdCode_1703)
	return EnumEpayPayOutCode_TerminalIdCode_1703
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_TerminalXLSMIDterminCode_1704() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_TerminalXLSMIDterminCode_1704)
	return EnumEpayPayOutCode_TerminalXLSMIDterminCode_1704
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_IbanCode_1705() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_IbanCode_1705)
	return EnumEpayPayOutCode_IbanCode_1705
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_IbanCode_1706() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_IbanCode_1706)
	return EnumEpayPayOutCode_IbanCode_1706
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_TerminalIdCode_1707() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_TerminalIdCode_1707)
	return EnumEpayPayOutCode_TerminalIdCode_1707
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_IbanCode_1708() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_IbanCode_1708)
	return EnumEpayPayOutCode_IbanCode_1708
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_1709() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_1709)
	return EnumEpayPayOutCode_Code_1709
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_1710() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_1710)
	return EnumEpayPayOutCode_Code_1710
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_1711() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_1711)
	return EnumEpayPayOutCode_Code_1711
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_1712() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_1712)
	return EnumEpayPayOutCode_Code_1712
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_InvoiceIdCode_1713() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_InvoiceIdCode_1713)
	return EnumEpayPayOutCode_InvoiceIdCode_1713
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_1714() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_1714)
	return EnumEpayPayOutCode_Code_1714
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_AUTHCode_1715() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_AUTHCode_1715)
	return EnumEpayPayOutCode_AUTHCode_1715
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_PaymentTypeCode_1716() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_PaymentTypeCode_1716)
	return EnumEpayPayOutCode_PaymentTypeCode_1716
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_OsuvoxCode_1717() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_OsuvoxCode_1717)
	return EnumEpayPayOutCode_OsuvoxCode_1717
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_PaymentTypeCode_1719() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_PaymentTypeCode_1719)
	return EnumEpayPayOutCode_PaymentTypeCode_1719
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_PaymentSystemCode_1720() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_PaymentSystemCode_1720)
	return EnumEpayPayOutCode_PaymentSystemCode_1720
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_1723() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_1723)
	return EnumEpayPayOutCode_Code_1723
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_1724() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_1724)
	return EnumEpayPayOutCode_Code_1724
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_1759() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_1759)
	return EnumEpayPayOutCode_Code_1759
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_1760() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_1760)
	return EnumEpayPayOutCode_Code_1760
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_1761() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_1761)
	return EnumEpayPayOutCode_Code_1761
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_1762() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_1762)
	return EnumEpayPayOutCode_Code_1762
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_ShopIdCode_1763() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_ShopIdCode_1763)
	return EnumEpayPayOutCode_ShopIdCode_1763
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_1764() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_1764)
	return EnumEpayPayOutCode_Code_1764
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_OrderIdCode_1765() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_OrderIdCode_1765)
	return EnumEpayPayOutCode_OrderIdCode_1765
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_CoreMerchantCode_1771() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_CoreMerchantCode_1771)
	return EnumEpayPayOutCode_CoreMerchantCode_1771
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_PostlinkcorecallbackCode_1780() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_PostlinkcorecallbackCode_1780)
	return EnumEpayPayOutCode_PostlinkcorecallbackCode_1780
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_CardTypenilCode_1781() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_CardTypenilCode_1781)
	return EnumEpayPayOutCode_CardTypenilCode_1781
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_SwitchpaymentCode_1786() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_SwitchpaymentCode_1786)
	return EnumEpayPayOutCode_SwitchpaymentCode_1786
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_1787() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_1787)
	return EnumEpayPayOutCode_Code_1787
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_HttpGetMerchantCertiCode_1788() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_HttpGetMerchantCertiCode_1788)
	return EnumEpayPayOutCode_HttpGetMerchantCertiCode_1788
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_HttpGetMerchantCreatCode_1789() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_HttpGetMerchantCreatCode_1789)
	return EnumEpayPayOutCode_HttpGetMerchantCreatCode_1789
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_EnabledCode_1849() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_EnabledCode_1849)
	return EnumEpayPayOutCode_EnabledCode_1849
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_ShopCode_1850() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_ShopCode_1850)
	return EnumEpayPayOutCode_ShopCode_1850
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_1855() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_1855)
	return EnumEpayPayOutCode_Code_1855
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_TerminalIDCode_1856() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_TerminalIDCode_1856)
	return EnumEpayPayOutCode_TerminalIDCode_1856
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_ShopIDCode_1857() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_ShopIDCode_1857)
	return EnumEpayPayOutCode_ShopIDCode_1857
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_IDCode_1858() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_IDCode_1858)
	return EnumEpayPayOutCode_IDCode_1858
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_ShopidCode_1859() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_ShopidCode_1859)
	return EnumEpayPayOutCode_ShopidCode_1859
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_TokenExpireInSecondsCode_1860() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_TokenExpireInSecondsCode_1860)
	return EnumEpayPayOutCode_TokenExpireInSecondsCode_1860
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Apiepay1Code_1899() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Apiepay1Code_1899)
	return EnumEpayPayOutCode_Apiepay1Code_1899
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_RequestCode_1900() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_RequestCode_1900)
	return EnumEpayPayOutCode_RequestCode_1900
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_1901() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_1901)
	return EnumEpayPayOutCode_Code_1901
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_1902() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_1902)
	return EnumEpayPayOutCode_Code_1902
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_JSONCode_1903() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_JSONCode_1903)
	return EnumEpayPayOutCode_JSONCode_1903
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_IdserviceCode_1944() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_IdserviceCode_1944)
	return EnumEpayPayOutCode_IdserviceCode_1944
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_UpdateMerchantContacCode_1945() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_UpdateMerchantContacCode_1945)
	return EnumEpayPayOutCode_UpdateMerchantContacCode_1945
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_ContactTypenameserviCode_1946() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_ContactTypenameserviCode_1946)
	return EnumEpayPayOutCode_ContactTypenameserviCode_1946
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_1954() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_1954)
	return EnumEpayPayOutCode_Code_1954
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_1955() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_1955)
	return EnumEpayPayOutCode_Code_1955
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_1956() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_1956)
	return EnumEpayPayOutCode_Code_1956
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_1961() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_1961)
	return EnumEpayPayOutCode_Code_1961
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_1962() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_1962)
	return EnumEpayPayOutCode_Code_1962
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_1964() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_1964)
	return EnumEpayPayOutCode_Code_1964
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_TerminalshopNameCode_1965() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_TerminalshopNameCode_1965)
	return EnumEpayPayOutCode_TerminalshopNameCode_1965
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_1970() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_1970)
	return EnumEpayPayOutCode_Code_1970
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_InvoiceIDCode_1971() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_InvoiceIDCode_1971)
	return EnumEpayPayOutCode_InvoiceIDCode_1971
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Amount0Code_1975() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Amount0Code_1975)
	return EnumEpayPayOutCode_Amount0Code_1975
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_1976() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_1976)
	return EnumEpayPayOutCode_Code_1976
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_1977() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_1977)
	return EnumEpayPayOutCode_Code_1977
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_TokenexpiredtryagainCode_1979() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_TokenexpiredtryagainCode_1979)
	return EnumEpayPayOutCode_TokenexpiredtryagainCode_1979
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_MPANCode_1980() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_MPANCode_1980)
	return EnumEpayPayOutCode_MPANCode_1980
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_2008() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_2008)
	return EnumEpayPayOutCode_Code_2008
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_RecordnotfoundCode_2009() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_RecordnotfoundCode_2009)
	return EnumEpayPayOutCode_RecordnotfoundCode_2009
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_2015() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_2015)
	return EnumEpayPayOutCode_Code_2015
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_2016() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_2016)
	return EnumEpayPayOutCode_Code_2016
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_RedismaxReqPerSecCode_2017() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_RedismaxReqPerSecCode_2017)
	return EnumEpayPayOutCode_RedismaxReqPerSecCode_2017
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_2018() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_2018)
	return EnumEpayPayOutCode_Code_2018
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_GooglePayCode_2028() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_GooglePayCode_2028)
	return EnumEpayPayOutCode_GooglePayCode_2028
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_GooglePayCode_2030() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_GooglePayCode_2030)
	return EnumEpayPayOutCode_GooglePayCode_2030
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_AccountIDCode_2031() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_AccountIDCode_2031)
	return EnumEpayPayOutCode_AccountIDCode_2031
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_CallbackCode_2032() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_CallbackCode_2032)
	return EnumEpayPayOutCode_CallbackCode_2032
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_2033() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_2033)
	return EnumEpayPayOutCode_Code_2033
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_2046() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_2046)
	return EnumEpayPayOutCode_Code_2046
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_JSONCode_2048() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_JSONCode_2048)
	return EnumEpayPayOutCode_JSONCode_2048
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_2049() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_2049)
	return EnumEpayPayOutCode_Code_2049
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_2050() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_2050)
	return EnumEpayPayOutCode_Code_2050
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_2051() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_2051)
	return EnumEpayPayOutCode_Code_2051
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_XLSURLCode_2052() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_XLSURLCode_2052)
	return EnumEpayPayOutCode_XLSURLCode_2052
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_2053() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_2053)
	return EnumEpayPayOutCode_Code_2053
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_JSONUpdateProfileStaCode_2057() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_JSONUpdateProfileStaCode_2057)
	return EnumEpayPayOutCode_JSONUpdateProfileStaCode_2057
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_JSONCreateStaffProfiCode_2058() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_JSONCreateStaffProfiCode_2058)
	return EnumEpayPayOutCode_JSONCreateStaffProfiCode_2058
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_2059() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_2059)
	return EnumEpayPayOutCode_Code_2059
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_2060() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_2060)
	return EnumEpayPayOutCode_Code_2060
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_InvalidamountamountmCode_2061() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_InvalidamountamountmCode_2061)
	return EnumEpayPayOutCode_InvalidamountamountmCode_2061
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_CorebusinessreportCode_2067() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_CorebusinessreportCode_2067)
	return EnumEpayPayOutCode_CorebusinessreportCode_2067
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_2069() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_2069)
	return EnumEpayPayOutCode_Code_2069
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_EmailCode_2070() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_EmailCode_2070)
	return EnumEpayPayOutCode_EmailCode_2070
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_GinBindCode_2074() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_GinBindCode_2074)
	return EnumEpayPayOutCode_GinBindCode_2074
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_XLSTIDXLSMIDCode_2075() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_XLSTIDXLSMIDCode_2075)
	return EnumEpayPayOutCode_XLSTIDXLSMIDCode_2075
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_2076() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_2076)
	return EnumEpayPayOutCode_Code_2076
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_MPGSCode_2085() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_MPGSCode_2085)
	return EnumEpayPayOutCode_MPGSCode_2085
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_MPGSCode_2086() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_MPGSCode_2086)
	return EnumEpayPayOutCode_MPGSCode_2086
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_MPGSCode_2087() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_MPGSCode_2087)
	return EnumEpayPayOutCode_MPGSCode_2087
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_MPGSCode_2088() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_MPGSCode_2088)
	return EnumEpayPayOutCode_MPGSCode_2088
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_MPGSCode_2089() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_MPGSCode_2089)
	return EnumEpayPayOutCode_MPGSCode_2089
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_MPGSCode_2090() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_MPGSCode_2090)
	return EnumEpayPayOutCode_MPGSCode_2090
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_PANBINCode_2091() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_PANBINCode_2091)
	return EnumEpayPayOutCode_PANBINCode_2091
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_BINorPANrestrictedCode_2092() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_BINorPANrestrictedCode_2092)
	return EnumEpayPayOutCode_BINorPANrestrictedCode_2092
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_BindCode_2122() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_BindCode_2122)
	return EnumEpayPayOutCode_BindCode_2122
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_2123() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_2123)
	return EnumEpayPayOutCode_Code_2123
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_2124() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_2124)
	return EnumEpayPayOutCode_Code_2124
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_MerchantCode_2125() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_MerchantCode_2125)
	return EnumEpayPayOutCode_MerchantCode_2125
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_MerchantCode_2126() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_MerchantCode_2126)
	return EnumEpayPayOutCode_MerchantCode_2126
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_2127() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_2127)
	return EnumEpayPayOutCode_Code_2127
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_ThreeCode_2128() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_ThreeCode_2128)
	return EnumEpayPayOutCode_ThreeCode_2128
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_2129() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_2129)
	return EnumEpayPayOutCode_Code_2129
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_OpenwayIDCode_2130() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_OpenwayIDCode_2130)
	return EnumEpayPayOutCode_OpenwayIDCode_2130
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_USDCode_2131() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_USDCode_2131)
	return EnumEpayPayOutCode_USDCode_2131
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_2132() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_2132)
	return EnumEpayPayOutCode_Code_2132
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_2133() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_2133)
	return EnumEpayPayOutCode_Code_2133
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_2134() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_2134)
	return EnumEpayPayOutCode_Code_2134
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_2135() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_2135)
	return EnumEpayPayOutCode_Code_2135
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_XlsCode_2136() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_XlsCode_2136)
	return EnumEpayPayOutCode_XlsCode_2136
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_2137() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_2137)
	return EnumEpayPayOutCode_Code_2137
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_PostlinkcorecallbackCode_2138() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_PostlinkcorecallbackCode_2138)
	return EnumEpayPayOutCode_PostlinkcorecallbackCode_2138
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_2139() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_2139)
	return EnumEpayPayOutCode_Code_2139
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_CryptogramopenwayIDCode_2146() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_CryptogramopenwayIDCode_2146)
	return EnumEpayPayOutCode_CryptogramopenwayIDCode_2146
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_IPCode_2147() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_IPCode_2147)
	return EnumEpayPayOutCode_IPCode_2147
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_2148() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_2148)
	return EnumEpayPayOutCode_Code_2148
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_FormdatasftpproxyCode_2152() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_FormdatasftpproxyCode_2152)
	return EnumEpayPayOutCode_FormdatasftpproxyCode_2152
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_HttpNewRequestsftpprCode_2153() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_HttpNewRequestsftpprCode_2153)
	return EnumEpayPayOutCode_HttpNewRequestsftpprCode_2153
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_RedispipelineCode_2154() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_RedispipelineCode_2154)
	return EnumEpayPayOutCode_RedispipelineCode_2154
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_TransactionamountshoCode_2156() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_TransactionamountshoCode_2156)
	return EnumEpayPayOutCode_TransactionamountshoCode_2156
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_2157() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_2157)
	return EnumEpayPayOutCode_Code_2157
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_URLpagesizeCode_2158() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_URLpagesizeCode_2158)
	return EnumEpayPayOutCode_URLpagesizeCode_2158
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_2159() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_2159)
	return EnumEpayPayOutCode_Code_2159
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_URLpagesizeCode_2191() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_URLpagesizeCode_2191)
	return EnumEpayPayOutCode_URLpagesizeCode_2191
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_PagesizeCode_2193() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_PagesizeCode_2193)
	return EnumEpayPayOutCode_PagesizeCode_2193
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_PagesizeCode_2194() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_PagesizeCode_2194)
	return EnumEpayPayOutCode_PagesizeCode_2194
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_2200() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_2200)
	return EnumEpayPayOutCode_Code_2200
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_2203() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_2203)
	return EnumEpayPayOutCode_Code_2203
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_2204() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_2204)
	return EnumEpayPayOutCode_Code_2204
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_2205() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_2205)
	return EnumEpayPayOutCode_Code_2205
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_UnmarshalCode_2206() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_UnmarshalCode_2206)
	return EnumEpayPayOutCode_UnmarshalCode_2206
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_2207() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_2207)
	return EnumEpayPayOutCode_Code_2207
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_2210() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_2210)
	return EnumEpayPayOutCode_Code_2210
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_PublicIDonboardingCode_2211() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_PublicIDonboardingCode_2211)
	return EnumEpayPayOutCode_PublicIDonboardingCode_2211
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_AclserviceCode_2212() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_AclserviceCode_2212)
	return EnumEpayPayOutCode_AclserviceCode_2212
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_StaffCode_2213() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_StaffCode_2213)
	return EnumEpayPayOutCode_StaffCode_2213
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_OpenWayCardIdCode_2214() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_OpenWayCardIdCode_2214)
	return EnumEpayPayOutCode_OpenWayCardIdCode_2214
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_2218() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_2218)
	return EnumEpayPayOutCode_Code_2218
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_2237() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_2237)
	return EnumEpayPayOutCode_Code_2237
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_PostlinkaCode_2238() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_PostlinkaCode_2238)
	return EnumEpayPayOutCode_PostlinkaCode_2238
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_PostlinkaCode_2239() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_PostlinkaCode_2239)
	return EnumEpayPayOutCode_PostlinkaCode_2239
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_PostlinkaCode_2240() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_PostlinkaCode_2240)
	return EnumEpayPayOutCode_PostlinkaCode_2240
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_PostlinkaCode_2241() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_PostlinkaCode_2241)
	return EnumEpayPayOutCode_PostlinkaCode_2241
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_PostlinkCode_2242() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_PostlinkCode_2242)
	return EnumEpayPayOutCode_PostlinkCode_2242
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_2243() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_2243)
	return EnumEpayPayOutCode_Code_2243
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_2244() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_2244)
	return EnumEpayPayOutCode_Code_2244
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_2245() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_2245)
	return EnumEpayPayOutCode_Code_2245
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_2249() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_2249)
	return EnumEpayPayOutCode_Code_2249
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_P2PCode_2250() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_P2PCode_2250)
	return EnumEpayPayOutCode_P2PCode_2250
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_2257() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_2257)
	return EnumEpayPayOutCode_Code_2257
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_JSONjsonMarshalJSONCode_2268() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_JSONjsonMarshalJSONCode_2268)
	return EnumEpayPayOutCode_JSONjsonMarshalJSONCode_2268
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_2271() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_2271)
	return EnumEpayPayOutCode_Code_2271
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_2301() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_2301)
	return EnumEpayPayOutCode_Code_2301
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_2305() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_2305)
	return EnumEpayPayOutCode_Code_2305
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_QrbyqrCode_2322() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_QrbyqrCode_2322)
	return EnumEpayPayOutCode_QrbyqrCode_2322
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_2323() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_2323)
	return EnumEpayPayOutCode_Code_2323
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_2326() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_2326)
	return EnumEpayPayOutCode_Code_2326
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_CardIDCode_2339() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_CardIDCode_2339)
	return EnumEpayPayOutCode_CardIDCode_2339
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_2349() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_2349)
	return EnumEpayPayOutCode_Code_2349
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_2350() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_2350)
	return EnumEpayPayOutCode_Code_2350
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_2351() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_2351)
	return EnumEpayPayOutCode_Code_2351
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_2352() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_2352)
	return EnumEpayPayOutCode_Code_2352
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_2353() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_2353)
	return EnumEpayPayOutCode_Code_2353
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_2354() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_2354)
	return EnumEpayPayOutCode_Code_2354
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_ClientIDCode_2355() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_ClientIDCode_2355)
	return EnumEpayPayOutCode_ClientIDCode_2355
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_2356() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_2356)
	return EnumEpayPayOutCode_Code_2356
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_ResultCodestatusIDCode_2360() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_ResultCodestatusIDCode_2360)
	return EnumEpayPayOutCode_ResultCodestatusIDCode_2360
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_PaymentsystemCode_2362() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_PaymentsystemCode_2362)
	return EnumEpayPayOutCode_PaymentsystemCode_2362
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_KafkaCode_2365() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_KafkaCode_2365)
	return EnumEpayPayOutCode_KafkaCode_2365
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_ShopInfoCode_2366() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_ShopInfoCode_2366)
	return EnumEpayPayOutCode_ShopInfoCode_2366
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_2367() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_2367)
	return EnumEpayPayOutCode_Code_2367
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_2433() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_2433)
	return EnumEpayPayOutCode_Code_2433
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_UZGWCode_2435() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_UZGWCode_2435)
	return EnumEpayPayOutCode_UZGWCode_2435
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_Code_2624() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_Code_2624)
	return EnumEpayPayOutCode_Code_2624
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_ChecktransactionstatCode_2660() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_ChecktransactionstatCode_2660)
	return EnumEpayPayOutCode_ChecktransactionstatCode_2660
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_CorePaymentCode_2678() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_CorePaymentCode_2678)
	return EnumEpayPayOutCode_CorePaymentCode_2678
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_CorePaymentCode_2679() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_CorePaymentCode_2679)
	return EnumEpayPayOutCode_CorePaymentCode_2679
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_OtpcodeapiuzgatewayCode_2704() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_OtpcodeapiuzgatewayCode_2704)
	return EnumEpayPayOutCode_OtpcodeapiuzgatewayCode_2704
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_CodeapiuzgatewayCode_2705() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_CodeapiuzgatewayCode_2705)
	return EnumEpayPayOutCode_CodeapiuzgatewayCode_2705
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_IncorrectcurrencyCode_101() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_IncorrectcurrencyCode_101)
	return EnumEpayPayOutCode_IncorrectcurrencyCode_101
}

func (u *EnumEpayPayOutCodeUsage) EnumEpayPayOutCode_DonotreattemptrestriCode_2872() EnumEpayPayOutCode {
	u.Use(EnumEpayPayOutCode_DonotreattemptrestriCode_2872)
	return EnumEpayPayOutCode_DonotreattemptrestriCode_2872
}
