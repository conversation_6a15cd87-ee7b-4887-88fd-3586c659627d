edition = "2023";

package processing.jusan_payout.jusan_payout;

option go_package = "git.local/sensitive/innerpb/processing/grpc";

import "inner/processing/grpc/transaction_status.proto";
import "inner/processing/grpc/integration.proto";

import "mvp/proto/refs.proto";
import "mvp/proto/enum.proto";

import "google/protobuf/descriptor.proto";

message JusanResponseCodePayoutRef {
  string code = 1;
  string message = 2;
  transaction.transaction_status.EnumTransactionStatus transaction_status = 3;
  integration.integration.IntegrationError integration_error = 4;
}

extend google.protobuf.EnumValueOptions {
  JusanResponseCodePayoutRef jusan_response_code_payout_value = 100117;
}

extend google.protobuf.EnumOptions {
  JusanResponseCodePayoutRef default_jusan_response_code_payout_value = 100118;
}

enum JusanResponseCodePayout {
  option(mvp.default_ref) = "default_jusan_response_code_payout_value";
  option(mvp.ref) = "jusan_response_code_payout_value";
  option(default_jusan_response_code_payout_value) = {
    code: "0"
    message: "default"
    transaction_status: TransactionStatusError
    integration_error: None
  };

  PayoutUnhandledError = 0 [(jusan_response_code_payout_value) = {
    code: "-1"
    transaction_status: TransactionStatusHolded
    integration_error: TransactionDeclinedByAcquirer
    message: "Необработанная ошибка"
  }, (mvp.from_string) = "-1"];

  PayoutValidationError = 1 [(jusan_response_code_payout_value) = {
    code: "-2"
    transaction_status: TransactionStatusFailed
    integration_error: TransactionDeclinedByAcquirer
    message: "Ошибка валидации"
  }, (mvp.from_string) = "-2"];

  PayoutIncorrectLoginPassword = 2 [(jusan_response_code_payout_value) = {
    code: "-3"
    transaction_status: TransactionStatusFailed
    integration_error: TransactionDeclinedByAcquirer
    message: "Некорректный логин или пароль"
  }, (mvp.from_string) = "-3"];

  PayoutBlocked = 3 [(jusan_response_code_payout_value) = {
    code: "-4"
    transaction_status: TransactionStatusFailed
    integration_error: TransactionDeclinedByAcquirer
    message: "Учетная запись заблокирована"
  }, (mvp.from_string) = "-4"];

  PayoutRetry = 4 [(jusan_response_code_payout_value) = {
    code: "-5"
    transaction_status: TransactionStatusRetry
    integration_error: TransactionDeclinedByAcquirer
    message: "Повторите запрос"
  }, (mvp.from_string) = "-5"];

  PayoutEmptyPartnerCorrelationId = 5 [(jusan_response_code_payout_value) = {
    code: "-100"
    transaction_status: TransactionStatusFailed
    integration_error: TransactionDeclinedByAcquirer
    message: "Пустой заголовок PartnerCorrelationId"
  }, (mvp.from_string) = "-100"];

  PayoutEmptyPartnerLogin = 6 [(jusan_response_code_payout_value) = {
    code: "-101"
    transaction_status: TransactionStatusFailed
    integration_error: TransactionDeclinedByAcquirer
    message: "Пустой заголовок PartnerLogin"
  }, (mvp.from_string) = "-101"];

  PayoutEmptyAuthorization = 7 [(jusan_response_code_payout_value) = {
    code: "-102"
    transaction_status: TransactionStatusFailed
    integration_error: TransactionDeclinedByAcquirer
    message: "Некорректное значение заголовка Autorization"
  }, (mvp.from_string) = "-102"];

  PayoutEmptyData = 8 [(jusan_response_code_payout_value) = {
    code: "-103"
    transaction_status: TransactionStatusFailed
    integration_error: TransactionDeclinedByAcquirer
    message: "Пустые зашифрованные данные"
  }, (mvp.from_string) = "-103"];

  PayoutEmptyAuth = 9 [(jusan_response_code_payout_value) = {
    code: "-104"
    transaction_status: TransactionStatusFailed
    integration_error: TransactionDeclinedByAcquirer
    message: "Пустой заголовок Authorization"
  }, (mvp.from_string) = "-104"];

  PayoutIncorrectAuth = 10 [(jusan_response_code_payout_value) = {
    code: "-105"
    transaction_status: TransactionStatusFailed
    integration_error: TransactionDeclinedByAcquirer
    message: "Некорректный заголовок Authorization"
  }, (mvp.from_string) = "-105"];

  PayoutIncorrectCurrency = 11 [(jusan_response_code_payout_value) = {
    code: "-106"
    transaction_status: TransactionStatusFailed
    integration_error: TransactionDeclinedByAcquirer
    message: "Некорректно указана валюта"
  }, (mvp.from_string) = "-106"];

  PayoutEDNotFound = 12 [(jusan_response_code_payout_value) = {
    code: "-107"
    transaction_status: TransactionStatusFailed
    integration_error: TransactionDeclinedByAcquirer
    message: "Транзакция ED не найдена"
  }, (mvp.from_string) = "-107"];

  PayoutEDNotEnabled = 13 [(jusan_response_code_payout_value) = {
    code: "-108"
    transaction_status: TransactionStatusFailed
    integration_error: TransactionDeclinedByAcquirer
    message: "Возможность зачисления с ЭД отключена"
  }, (mvp.from_string) = "-108"];

  PayoutInsufficientFunds = 14 [(jusan_response_code_payout_value) = {
    code: "-10000"
    transaction_status: TransactionStatusFailed
    integration_error: InsufficientFunds
    message: "Сумма остатка на счете не позволяет провести данный платеж!"
  }, (mvp.from_string) = "-10000"];

  PayoutIncorrectCard = 15 [(jusan_response_code_payout_value) = {
    code: "-109"
    transaction_status: TransactionStatusFailed
    integration_error: IncorrectCardNumber
    message: "Неверно указан номер карты"
  }, (mvp.from_string) = "-109"];

  PayoutAccountBlocked = 16 [(jusan_response_code_payout_value) = {
    code: "-110"
    transaction_status: TransactionStatusFailed
    integration_error: TransactionDeclinedByAcquirer
    message: "Учетная запись партнера заблокирована"
  }, (mvp.from_string) = "-110"];

  PayoutIncorrectSession = 17 [(jusan_response_code_payout_value) = {
    code: "-111"
    transaction_status: TransactionStatusFailed
    integration_error: TransactionDeclinedByAcquirer
    message: "Неверно указана сессия"
  }, (mvp.from_string) = "-111"];

  PayoutPaymentNotFound = 18 [(jusan_response_code_payout_value) = {
    code: "-112"
    transaction_status: TransactionStatusFailed
    integration_error: TransactionDeclinedByAcquirer
    message: "Информация о платеже не найдена"
  }, (mvp.from_string) = "-112"];

  PayoutIncorrectAmount = 19 [(jusan_response_code_payout_value) = {
    code: "-113"
    transaction_status: TransactionStatusFailed
    integration_error: TransactionDeclinedByAcquirer
    message: "Суммы операции и гашения отличаются"
  }, (mvp.from_string) = "-113"];

  PayoutIssuerDeclined = 20 [(jusan_response_code_payout_value) = {
    code: "W05"
    transaction_status: TransactionStatusFailed
    integration_error: TransactionDeclinedByIssuer
    message: "Отказ Эмитента, причина не определена"
  }, (mvp.from_string) = "W05"];

  PayoutCardDisabled = 21 [(jusan_response_code_payout_value) = {
    code: "W07"
    transaction_status: TransactionStatusFailed
    integration_error: BlockedCard
    message: "Карта отключена"
  }, (mvp.from_string) = "W07"];

  PayoutTransactionNotActive = 22 [(jusan_response_code_payout_value) = {
    code: "W12"
    transaction_status: TransactionStatusFailed
    integration_error: TransactionDeclinedByAcquirer
    message: "Недействительная транзакция"
  }, (mvp.from_string) = "W12"];

  PayoutCardNumIncorrect = 23 [(jusan_response_code_payout_value) = {
    code: "W14"
    transaction_status: TransactionStatusFailed
    integration_error: IncorrectCardNumber
    message: "Недействительный номер карточки"
  }, (mvp.from_string) = "W14"];

  PayoutClientDisabled = 24 [(jusan_response_code_payout_value) = {
    code: "W17"
    transaction_status: TransactionStatusFailed
    integration_error: SuspiciousClient
    message: "Клиент исключен"
  }, (mvp.from_string) = "W17"];

  PayoutSuspiciousClient = 25 [(jusan_response_code_payout_value) = {
    code: "W18"
    transaction_status: TransactionStatusFailed
    integration_error: SuspiciousClient
    message: "Подозрительный клиент"
  }, (mvp.from_string) = "W18"];

  PayoutRetryTransaction = 26 [(jusan_response_code_payout_value) = {
    code: "W19"
    transaction_status: TransactionStatusFailed
    integration_error: TransactionDeclinedByAcquirer
    message: "Повторная транзакция"
  }, (mvp.from_string) = "W19"];

  PayoutCardExpired = 27 [(jusan_response_code_payout_value) = {
    code: "W33"
    transaction_status: TransactionStatusFailed
    integration_error: CardHasExpired
    message: "Карточка просрочена"
  }, (mvp.from_string) = "W33"];

  PayoutCardLimited = 28 [(jusan_response_code_payout_value) = {
    code: "W36"
    transaction_status: TransactionStatusFailed
    integration_error: InvalidCard
    message: "Карточка ограничена в использовании"
  }, (mvp.from_string) = "W36"];

  PayoutStolenCard = 29 [(jusan_response_code_payout_value) = {
    code: "W43"
    transaction_status: TransactionStatusFailed
    integration_error: StolenCard
    message: "Украденная карточка"
  }, (mvp.from_string) = "W43"];

  PayoutExpCard = 30 [(jusan_response_code_payout_value) = {
    code: "W54"
    transaction_status: TransactionStatusFailed
    integration_error: CardHasExpired
    message: "Карточка просрочена"
  }, (mvp.from_string) = "W54"];

  PayoutForbiddenForClient = 31 [(jusan_response_code_payout_value) = {
    code: "W57"
    transaction_status: TransactionStatusFailed
    integration_error: SuspiciousClient
    message: "Транзакция запрещена для клиента"
  }, (mvp.from_string) = "W57"];

  PayoutForbiddenTerminal = 32 [(jusan_response_code_payout_value) = {
    code: "W58"
    transaction_status: TransactionStatusFailed
    integration_error: PaymentForbiddenForMerchant
    message: "Транзакция запрещена для терминала"
  }, (mvp.from_string) = "W58"];

  PayoutClientSuspicious = 33 [(jusan_response_code_payout_value) = {
    code: "W59"
    transaction_status: TransactionStatusFailed
    integration_error: SuspiciousClient
    message: "Подозрение в мошенничестве"
  }, (mvp.from_string) = "W59"];

  PayoutLimitExceeded = 34 [(jusan_response_code_payout_value) = {
    code: "W61"
    transaction_status: TransactionStatusFailed
    integration_error: TransactionDeclinedByAcquirer
    message: "Превышает лимит суммы карты получателя"
  }, (mvp.from_string) = "W61"];

  PayoutLimitedCard = 35 [(jusan_response_code_payout_value) = {
    code: "W62"
    transaction_status: TransactionStatusFailed
    integration_error: InvalidCard
    message: "Карточка с ограниченным использованием"
  }, (mvp.from_string) = "W62"];

  PayoutSecurityBreached = 36 [(jusan_response_code_payout_value) = {
    code: "W63"
    transaction_status: TransactionStatusFailed
    integration_error: TransactionDeclinedByAcquirer
    message: "Нарушена безопасность"
  }, (mvp.from_string) = "W63"];

  PayoutUnavailableIssuer = 37 [(jusan_response_code_payout_value) = {
    code: "W91"
    transaction_status: TransactionStatusFailed
    integration_error: UnavailableIssuer
    message: "Недействующий эмитент"
  }, (mvp.from_string) = "W91"];

  PayoutTransactionNotFinished = 38 [(jusan_response_code_payout_value) = {
    code: "W93"
    transaction_status: TransactionStatusFailed
    integration_error: TransactionDeclinedByAcquirer
    message: "Транзакция не завершена. Нарушение закона"
  }, (mvp.from_string) = "W93"];

  PayoutSystemMalfuction = 39 [(jusan_response_code_payout_value) = {
    code: "W96"
    transaction_status: TransactionStatusHolded
    integration_error: UnavailableAcquirer
    message: "Неисправность в системе"
  }, (mvp.from_string) = "W96"];
}
