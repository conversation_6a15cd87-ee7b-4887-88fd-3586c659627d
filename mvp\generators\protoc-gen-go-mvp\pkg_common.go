package main

import (
	"google.golang.org/protobuf/compiler/protogen"
	"google.golang.org/protobuf/types/descriptorpb"
	"google.golang.org/protobuf/types/pluginpb"
)

func AddEditionsSupport(gen *protogen.Plugin) {
	gen.SupportedEditionsMinimum = descriptorpb.Edition_EDITION_PROTO2
	gen.SupportedEditionsMaximum = descriptorpb.Edition_EDITION_MAX
	gen.SupportedFeatures = uint64(pluginpb.CodeGeneratorResponse_FEATURE_PROTO3_OPTIONAL | pluginpb.CodeGeneratorResponse_FEATURE_NONE | pluginpb.CodeGeneratorResponse_FEATURE_SUPPORTS_EDITIONS)
}

func HeaderPrint(g *protogen.GeneratedFile, packageName string) {
	g.P("// Code generated by protoc-gen-go-mvp. DO NOT EDIT.")
	g.P("// versions:")
	g.P("// - protoc-gen-go-mvp v2.0.1")
	g.P("// created by mvp authors")
	g.P()
	g.P("package ", packageName)
	g.P()
}
