package repository

import (
	"context"
	"errors"
	"git.local/sensitive/innerpb/processing/goerr"
	"git.local/sensitive/processing/account/model"
	"git.local/sensitive/sdk/dog"
	"gorm.io/gorm"
)

type TransferTypeDB struct {
	db *gorm.DB
}

func NewTransferTypeDB(db *gorm.DB) TransferTyper {
	return &TransferTypeDB{
		db: db,
	}
}

func (tt *TransferTypeDB) GetByID(ctx context.Context, id uint64) (_ *model.TransferType, err error) {
	ctx, span := dog.CreateSpan(ctx, "TransferTypeDB_GetByID")
	defer span.End()

	transferType := new(model.TransferType)

	if err = tt.db.WithContext(ctx).
		Where("id = ?", id).
		First(transferType).
		Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, goerr.ErrDbUnexpected.WithCtx(ctx)
		}

		return nil, goerr.ErrDbUnexpected.WithErr(err).WithCtx(ctx)
	}

	return transferType, nil
}

func (tt *TransferTypeDB) GetByCode(
	ctx context.Context,
	code model.TransferTypeCode,
) (_ *model.TransferType, err error) {
	ctx, span := dog.CreateSpan(ctx, "TransferTypeDB_GetByCode")
	defer span.End()

	transferTypes := new(model.TransferType)

	if err = tt.db.WithContext(ctx).
		Where("code = ?", code).
		First(transferTypes).
		Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, goerr.ErrTransferTypeNotFound.WithErr(err).WithCtx(ctx)
		}

		return nil, goerr.ErrDbUnexpected.WithErr(err).WithCtx(ctx)
	}

	return transferTypes, nil
}

func (tt *TransferTypeDB) GetAll(
	ctx context.Context,
) (_ []model.TransferType, err error) {
	ctx, span := dog.CreateSpan(ctx, "TransferTypeDB_GetAll")
	defer span.End()

	var types []model.TransferType

	if err = tt.db.WithContext(ctx).
		Find(&types).
		Error; err != nil {
		return nil, goerr.ErrDbUnexpected.WithErr(err).WithCtx(ctx)

	}

	return types, nil
}
