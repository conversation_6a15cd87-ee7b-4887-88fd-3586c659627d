edition = "2023";

package processing.multiaccounting.multiaccounting;

option go_package = "git.local/sensitive/innerpb/processing/grpc";

import "google/protobuf/struct.proto";
import "google/protobuf/empty.proto";
import "mvp/proto/logger.proto";

service Multiaccounting {
  rpc MakeMerchantCheck(MakeMerchantCheckRequest) returns (MakeMerchantCheckResponse) {}
  rpc MakeTransfer(MakeTransferRequest) returns (MakeTransferResponse) {}
  rpc AcceptTransfer(AcceptTransferRequest) returns (AcceptTransferResponse) {}
  rpc DeclineTransfer(DeclineTransferRequest) returns (DeclineTransferResponse) {}
  rpc RedoTransfer(RedoTransferRequest) returns (RedoTransferResponse) {}
  rpc GetTransferDetails(GetTransferDetailsRequest) returns (GetTransferDetailsResponse) {}
  rpc GetTransfersList(GetTransfersListRequest) returns (GetTransfersListResponse) {}
  rpc GetAccountBalance(GetAccountBalanceRequest) returns (GetAccountBalanceResponse) {}
  rpc GetAccountStatement(GetAccountStatementRequest) returns (GetAccountStatementResponse) {}
  rpc GetAccountIdentifier(google.protobuf.Empty) returns (GetAccountIdentifierResponse) {}
}

message AccountTerminal {
  uint64 id = 1;
  string bank_code = 2;
  google.protobuf.Struct config = 3 [(mvp.FieldLoggerLevel) = Hidden];
}

// -- transit accounts
message ServiceParametersList {
  repeated ServiceParameter service_params_list = 1;
}

message GetAccountIdentifierResponse{
  string account_identifier = 1;
}

message ServiceParameter {
  string code = 1;
  string value = 2;
  string parameter_type = 3;
}

message TransitAccountTransfer{
  int32 id = 1;
  int32 service_id = 2;
  double amount = 3;
  string currency = 4;
  string external_reference_id = 5;
  string transfer_status = 6;
  string transfer_status_name = 7;
  string creation_date = 8;
  string finishing_date = 9;
  repeated ServiceParameter service_parameters = 10;
  string auth_code = 11;
  string card = 12;
  string terminal = 13;
}

message TransitAccountOperation {
  string external_reference_id = 1;
  string date = 2;
  string operation_type = 3;
  string merchant_account = 4;
  string merchant_bank = 5;
  string merchant_bin = 6;
  string merchant_name = 7;
  double amount = 8;
  string currency = 9;
  string payment_purpose_code = 10;
  string description = 11;
  uint64 transfer_type_id = 12;
}

// requests

message MakeTransferRequest {
  uint64 transfer_id = 1;
  AccountTerminal account = 2;
  string external_reference_id = 3;
  double amount = 4;
  string currency = 5;
  int32 is_need_acceptation = 6;
  repeated ServiceParameter service_parameters = 7;
}

message MakeMerchantCheckRequest {
 AccountTerminal account = 1;
  string merchant_bin = 2;
  string merchant_bank_account = 3;
}

message AcceptTransferRequest {
  uint64 transfer_id = 1;
  AccountTerminal account = 2;
  string external_reference_id = 3;
}

message DeclineTransferRequest {
  uint64 transfer_id = 1;
  AccountTerminal account = 2;
  string external_reference_id = 3;
}

message RedoTransferRequest {
  uint64 transfer_id = 1;
  AccountTerminal account = 2;
  string external_reference_id = 3;
}

message GetTransferDetailsRequest {
  uint64 transfer_id = 1;
  AccountTerminal account = 2;
  string external_reference_id = 3;
}

message GetTransfersListRequest {
  AccountTerminal account = 1;
  string date_from = 2;
  string date_to = 3;
}

message GetAccountBalanceRequest {
  AccountTerminal account = 1;
}

message GetAccountStatementRequest {
  AccountTerminal account = 1;
  string date = 2;
  int32 page = 3;
}

// responses
message MakeTransferResponse {
  uint64 transfer_id = 1;
  uint64 transfer_status_id = 2;
  string status = 3;
  int32 id = 4;
  int32 service_id = 5;
  double amount = 6;
  string currency = 7;
  string external_reference_id = 8;
  string transfer_status = 9;
  string transfer_status_name = 10;
  string creation_date = 11;
  string finishing_date = 12;
  repeated ServiceParameter service_parameters = 13;
  string rrn = 14;
  string auth_code = 15;
  string card = 16;
  string terminal = 17;
}

message MakeMerchantCheckResponse {
  string status = 1;
  repeated ServiceParametersList response_data = 2;
}

message AcceptTransferResponse {
  uint64 transfer_id = 1;
  uint64 transfer_status_id = 2;
  string status = 3;
  int32 id = 4;
  int32 service_id = 5;
  double amount = 6;
  string currency = 7;
  string external_reference_id = 8;
  string transfer_status = 9;
  string transfer_status_name = 10;
  string creation_date = 11;
  string finishing_date = 12;
  repeated ServiceParameter service_parameters = 13;
  string auth_code = 14;
  string card = 15;
  string terminal = 16;
}

message DeclineTransferResponse {
  uint64 transfer_id = 1;
  uint64 transfer_status_id = 2;
  string status = 3;
  int32 id = 4;
  int32 service_id = 5;
  double amount = 6;
  string currency = 7;
  string external_reference_id = 8;
  string transfer_status = 9;
  string transfer_status_name = 10;
  string creation_date = 11;
  string finishing_date = 12;
  repeated ServiceParameter service_parameters = 13;
  string auth_code = 14;
  string card = 15;
  string terminal = 16;
}

message RedoTransferResponse {
  uint64 transfer_id = 1;
  uint64 transfer_status_id = 2;
  string status = 3;
  int32 id = 4;
  int32 service_id = 5;
  double amount = 6;
  string currency = 7;
  string external_reference_id = 8;
  string transfer_status = 9;
  string transfer_status_name = 10;
  string creation_date = 11;
  string finishing_date = 12;
  repeated ServiceParameter service_parameters = 13;
  string auth_code = 14;
  string card = 15;
  string terminal = 16;
}

message GetTransferDetailsResponse {
  uint64 transfer_id = 1;
  uint64 transfer_status_id = 2;
  string status = 3;
  int32 id = 4;
  int32 service_id = 5;
  double amount = 6;
  string currency = 7;
  string external_reference_id = 8;
  string transfer_status = 9;
  string transfer_status_name = 10;
  string creation_date = 11;
  string finishing_date = 12;
  repeated ServiceParameter service_parameters = 13;
  string auth_code = 14;
  string card = 15;
  string terminal = 16;
}

message GetTransfersListResponse {
  string status = 1;
  repeated TransitAccountTransfer data = 2;
}

message GetAccountBalanceResponse {
  string status = 1;
  double balance = 2;
  double processed = 3;
  double threshold = 4;
}

message GetAccountStatementResponse {
  string status = 1;
  double in_balance = 2;
  double out_balance = 3;
  int32 page = 4;
  int32 pages_count = 5;
  repeated TransitAccountOperation operations = 6;
}

