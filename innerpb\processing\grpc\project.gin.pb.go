// Code generated by protoc-gen-go-mvp. DO NOT EDIT.
// versions:
// - protoc-gen-go-mvp v2.0.1
// created by mvp authors

package grpc

import (
	dog "git.local/sensitive/sdk/dog"
	gin "github.com/gin-gonic/gin"
	metadata "google.golang.org/grpc/metadata"
	http "net/http"
)

func RegisterGinProjectRouterGroup(
	in *gin.RouterGroup,
	handler func(func(c *gin.Context) error) gin.HandlerFunc,
) error {
	service, err := NewGinProjectService()
	if err != nil {
		return err
	}

	routerGroup := in.Group("processing.merchant.project.Project")
	routerGroup.PUT("/GetProjectsByMerchantID", handler(service.GetProjectsByMerchantID))
	routerGroup.PUT("/IsSendEmail", handler(service.IsSendEmail))
	routerGroup.PUT("/GetProcessingProjectsByBUIDV1", handler(service.GetProcessingProjectsByBUIDV1))
	return nil
}

func NewGinProjectService() (GinProjectServer, error) {
	client, err := NewPreparedProjectClient()
	if err != nil {
		return nil, err
	}

	return &ginProjectServer{
		client: NewLoggedProjectClient(
			NewIamProjectClient(client),
		),
	}, nil
}

//go:generate mockgen -destination=./grpcmock/project.gin.pb.go -package=grpcmock -source=project.gin.pb.go GinProjectServer
type GinProjectServer interface {
	GetProjectsByMerchantID(c *gin.Context) error
	IsSendEmail(c *gin.Context) error
	GetProcessingProjectsByBUIDV1(c *gin.Context) error
}

var _ GinProjectServer = (*ginProjectServer)(nil)

type ginProjectServer struct {
	client ProjectClient
}

type Project_GetProjectsByMerchantID_Success struct {
	// Status is the status of the response. Set true
	Status bool `json:"status"`
	// Message is a human-readable explanation of the status
	Message string `json:"message"`
	// StatusCode is the GoErr status. Set 0
	StatusCode int `json:"status_code"`
	// Result is the result of the request
	Result *ProjectsResponseV1 `json:"result"`
}

type Project_GetProjectsByMerchantID_Failure struct {
	// Status is the status of the response. Set false
	Status bool `json:"status"`
	// Message is a human-readable explanation of the status
	Message string `json:"message"`
	// StatusCode is the GoErr status
	StatusCode int `json:"status_code"`
}

// GetProjectsByMerchantID
// @Summary GetProjectsByMerchantID
// @Security bearerAuth
// @ID Project_GetProjectsByMerchantID
// @Accept json
// @Param request body ProjectsRequestV1 true "ProjectsRequestV1"
// @Success 200 {object} Project_GetProjectsByMerchantID_Success
// @Failure 401 {object} Project_GetProjectsByMerchantID_Failure "goerrpkg.ErrorTypeAuthorization"
// @Failure 404 {object} Project_GetProjectsByMerchantID_Failure "goerrpkg.ErrorTypeNotFound"
// @Failure 400 {object} Project_GetProjectsByMerchantID_Failure "goerrpkg.ErrorTypeBadRequest"
// @Failure 500 {object} Project_GetProjectsByMerchantID_Failure "goerrpkg.ErrorTypeUnexpectedError"
// @Failure 400 {object} Project_GetProjectsByMerchantID_Failure "gin.ErrorTypeBind"
// @Failure 500 {object} Project_GetProjectsByMerchantID_Failure "Undefined error"
// @Produce json
// @Router /processing.merchant.project.Project/GetProjectsByMerchantID [put]
// @tags Project
func (s *ginProjectServer) GetProjectsByMerchantID(c *gin.Context) error {
	ctx, span := dog.CreateSpan(c.Request.Context(), "GinProjectServer_GetProjectsByMerchantID")
	defer span.End()

	var request ProjectsRequestV1
	if err := c.ShouldBindJSON(&request); err != nil {
		return err
	}

	ctx = metadata.NewOutgoingContext(ctx, metadata.MD(c.Request.Header))

	response, err := s.client.GetProjectsByMerchantID(ctx, &request)
	if err != nil {
		return err
	}

	c.JSON(http.StatusOK, &Project_GetProjectsByMerchantID_Success{
		Status:     true,
		Message:    "Success",
		StatusCode: 0,
		Result:     response,
	})
	return nil
}

type Project_IsSendEmail_Success struct {
	// Status is the status of the response. Set true
	Status bool `json:"status"`
	// Message is a human-readable explanation of the status
	Message string `json:"message"`
	// StatusCode is the GoErr status. Set 0
	StatusCode int `json:"status_code"`
	// Result is the result of the request
	Result *IsSendEmailResponseV1 `json:"result"`
}

type Project_IsSendEmail_Failure struct {
	// Status is the status of the response. Set false
	Status bool `json:"status"`
	// Message is a human-readable explanation of the status
	Message string `json:"message"`
	// StatusCode is the GoErr status
	StatusCode int `json:"status_code"`
}

// IsSendEmail
// @Summary IsSendEmail
// @Security bearerAuth
// @ID Project_IsSendEmail
// @Accept json
// @Param request body ProjectRequestV1 true "ProjectRequestV1"
// @Success 200 {object} Project_IsSendEmail_Success
// @Failure 401 {object} Project_IsSendEmail_Failure "goerrpkg.ErrorTypeAuthorization"
// @Failure 404 {object} Project_IsSendEmail_Failure "goerrpkg.ErrorTypeNotFound"
// @Failure 400 {object} Project_IsSendEmail_Failure "goerrpkg.ErrorTypeBadRequest"
// @Failure 500 {object} Project_IsSendEmail_Failure "goerrpkg.ErrorTypeUnexpectedError"
// @Failure 400 {object} Project_IsSendEmail_Failure "gin.ErrorTypeBind"
// @Failure 500 {object} Project_IsSendEmail_Failure "Undefined error"
// @Produce json
// @Router /processing.merchant.project.Project/IsSendEmail [put]
// @tags Project
func (s *ginProjectServer) IsSendEmail(c *gin.Context) error {
	ctx, span := dog.CreateSpan(c.Request.Context(), "GinProjectServer_IsSendEmail")
	defer span.End()

	var request ProjectRequestV1
	if err := c.ShouldBindJSON(&request); err != nil {
		return err
	}

	ctx = metadata.NewOutgoingContext(ctx, metadata.MD(c.Request.Header))

	response, err := s.client.IsSendEmail(ctx, &request)
	if err != nil {
		return err
	}

	c.JSON(http.StatusOK, &Project_IsSendEmail_Success{
		Status:     true,
		Message:    "Success",
		StatusCode: 0,
		Result:     response,
	})
	return nil
}

type Project_GetProcessingProjectsByBUIDV1_Success struct {
	// Status is the status of the response. Set true
	Status bool `json:"status"`
	// Message is a human-readable explanation of the status
	Message string `json:"message"`
	// StatusCode is the GoErr status. Set 0
	StatusCode int `json:"status_code"`
	// Result is the result of the request
	Result *GetProcessingProjectsByBUIDResponseV1 `json:"result"`
}

type Project_GetProcessingProjectsByBUIDV1_Failure struct {
	// Status is the status of the response. Set false
	Status bool `json:"status"`
	// Message is a human-readable explanation of the status
	Message string `json:"message"`
	// StatusCode is the GoErr status
	StatusCode int `json:"status_code"`
}

// GetProcessingProjectsByBUIDV1
// @Summary GetProcessingProjectsByBUIDV1
// @Security bearerAuth
// @ID Project_GetProcessingProjectsByBUIDV1
// @Accept json
// @Param request body GetProcessingProjectsByBUIDRequestV1 true "GetProcessingProjectsByBUIDRequestV1"
// @Success 200 {object} Project_GetProcessingProjectsByBUIDV1_Success
// @Failure 401 {object} Project_GetProcessingProjectsByBUIDV1_Failure "goerrpkg.ErrorTypeAuthorization"
// @Failure 404 {object} Project_GetProcessingProjectsByBUIDV1_Failure "goerrpkg.ErrorTypeNotFound"
// @Failure 400 {object} Project_GetProcessingProjectsByBUIDV1_Failure "goerrpkg.ErrorTypeBadRequest"
// @Failure 500 {object} Project_GetProcessingProjectsByBUIDV1_Failure "goerrpkg.ErrorTypeUnexpectedError"
// @Failure 400 {object} Project_GetProcessingProjectsByBUIDV1_Failure "gin.ErrorTypeBind"
// @Failure 500 {object} Project_GetProcessingProjectsByBUIDV1_Failure "Undefined error"
// @Produce json
// @Router /processing.merchant.project.Project/GetProcessingProjectsByBUIDV1 [put]
// @tags Project
func (s *ginProjectServer) GetProcessingProjectsByBUIDV1(c *gin.Context) error {
	ctx, span := dog.CreateSpan(c.Request.Context(), "GinProjectServer_GetProcessingProjectsByBUIDV1")
	defer span.End()

	var request GetProcessingProjectsByBUIDRequestV1
	if err := c.ShouldBindJSON(&request); err != nil {
		return err
	}

	ctx = metadata.NewOutgoingContext(ctx, metadata.MD(c.Request.Header))

	response, err := s.client.GetProcessingProjectsByBUIDV1(ctx, &request)
	if err != nil {
		return err
	}

	c.JSON(http.StatusOK, &Project_GetProcessingProjectsByBUIDV1_Success{
		Status:     true,
		Message:    "Success",
		StatusCode: 0,
		Result:     response,
	})
	return nil
}
