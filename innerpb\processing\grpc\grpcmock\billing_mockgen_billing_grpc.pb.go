// Code generated by MockGen. DO NOT EDIT.
// Source: billing_grpc.pb.go

// Package grpcmock is a generated GoMock package.
package grpcmock

import (
	context "context"
	reflect "reflect"

	grpc "git.local/sensitive/innerpb/processing/grpc"
	gomock "github.com/golang/mock/gomock"
	grpc0 "google.golang.org/grpc"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
)

// MockBillingClient is a mock of BillingClient interface.
type MockBillingClient struct {
	ctrl     *gomock.Controller
	recorder *MockBillingClientMockRecorder
}

// MockBillingClientMockRecorder is the mock recorder for MockBillingClient.
type MockBillingClientMockRecorder struct {
	mock *MockBillingClient
}

// NewMockBillingClient creates a new mock instance.
func NewMockBillingClient(ctrl *gomock.Controller) *MockBillingClient {
	mock := &MockBillingClient{ctrl: ctrl}
	mock.recorder = &MockBillingClientMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockBillingClient) EXPECT() *MockBillingClientMockRecorder {
	return m.recorder
}

// BillInTransferV1 mocks base method.
func (m *MockBillingClient) BillInTransferV1(ctx context.Context, in *grpc.BillInTransferRequestV1, opts ...grpc0.CallOption) (*emptypb.Empty, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "BillInTransferV1", varargs...)
	ret0, _ := ret[0].(*emptypb.Empty)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BillInTransferV1 indicates an expected call of BillInTransferV1.
func (mr *MockBillingClientMockRecorder) BillInTransferV1(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BillInTransferV1", reflect.TypeOf((*MockBillingClient)(nil).BillInTransferV1), varargs...)
}

// BillOutTransferV1 mocks base method.
func (m *MockBillingClient) BillOutTransferV1(ctx context.Context, in *grpc.BillOutTransferRequestV1, opts ...grpc0.CallOption) (*emptypb.Empty, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "BillOutTransferV1", varargs...)
	ret0, _ := ret[0].(*emptypb.Empty)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BillOutTransferV1 indicates an expected call of BillOutTransferV1.
func (mr *MockBillingClientMockRecorder) BillOutTransferV1(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BillOutTransferV1", reflect.TypeOf((*MockBillingClient)(nil).BillOutTransferV1), varargs...)
}

// BillPayInTransactionV1 mocks base method.
func (m *MockBillingClient) BillPayInTransactionV1(ctx context.Context, in *grpc.BillPayInTransactionRequestV1, opts ...grpc0.CallOption) (*emptypb.Empty, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "BillPayInTransactionV1", varargs...)
	ret0, _ := ret[0].(*emptypb.Empty)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BillPayInTransactionV1 indicates an expected call of BillPayInTransactionV1.
func (mr *MockBillingClientMockRecorder) BillPayInTransactionV1(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BillPayInTransactionV1", reflect.TypeOf((*MockBillingClient)(nil).BillPayInTransactionV1), varargs...)
}

// BillPayOutTransactionV1 mocks base method.
func (m *MockBillingClient) BillPayOutTransactionV1(ctx context.Context, in *grpc.BillPayOutTransactionRequestV1, opts ...grpc0.CallOption) (*emptypb.Empty, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "BillPayOutTransactionV1", varargs...)
	ret0, _ := ret[0].(*emptypb.Empty)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BillPayOutTransactionV1 indicates an expected call of BillPayOutTransactionV1.
func (mr *MockBillingClientMockRecorder) BillPayOutTransactionV1(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BillPayOutTransactionV1", reflect.TypeOf((*MockBillingClient)(nil).BillPayOutTransactionV1), varargs...)
}

// BillRefundTransactionV1 mocks base method.
func (m *MockBillingClient) BillRefundTransactionV1(ctx context.Context, in *grpc.BillRefundTransactionRequestV1, opts ...grpc0.CallOption) (*emptypb.Empty, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "BillRefundTransactionV1", varargs...)
	ret0, _ := ret[0].(*emptypb.Empty)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BillRefundTransactionV1 indicates an expected call of BillRefundTransactionV1.
func (mr *MockBillingClientMockRecorder) BillRefundTransactionV1(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BillRefundTransactionV1", reflect.TypeOf((*MockBillingClient)(nil).BillRefundTransactionV1), varargs...)
}

// BillSplitTransferV1 mocks base method.
func (m *MockBillingClient) BillSplitTransferV1(ctx context.Context, in *grpc.BillSplitTransferRequestV1, opts ...grpc0.CallOption) (*emptypb.Empty, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "BillSplitTransferV1", varargs...)
	ret0, _ := ret[0].(*emptypb.Empty)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BillSplitTransferV1 indicates an expected call of BillSplitTransferV1.
func (mr *MockBillingClientMockRecorder) BillSplitTransferV1(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BillSplitTransferV1", reflect.TypeOf((*MockBillingClient)(nil).BillSplitTransferV1), varargs...)
}

// CheckBalanceCreditExpireDate mocks base method.
func (m *MockBillingClient) CheckBalanceCreditExpireDate(ctx context.Context, in *emptypb.Empty, opts ...grpc0.CallOption) (*emptypb.Empty, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "CheckBalanceCreditExpireDate", varargs...)
	ret0, _ := ret[0].(*emptypb.Empty)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CheckBalanceCreditExpireDate indicates an expected call of CheckBalanceCreditExpireDate.
func (mr *MockBillingClientMockRecorder) CheckBalanceCreditExpireDate(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CheckBalanceCreditExpireDate", reflect.TypeOf((*MockBillingClient)(nil).CheckBalanceCreditExpireDate), varargs...)
}

// CheckBalanceCreditStartDate mocks base method.
func (m *MockBillingClient) CheckBalanceCreditStartDate(ctx context.Context, in *emptypb.Empty, opts ...grpc0.CallOption) (*emptypb.Empty, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "CheckBalanceCreditStartDate", varargs...)
	ret0, _ := ret[0].(*emptypb.Empty)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CheckBalanceCreditStartDate indicates an expected call of CheckBalanceCreditStartDate.
func (mr *MockBillingClientMockRecorder) CheckBalanceCreditStartDate(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CheckBalanceCreditStartDate", reflect.TypeOf((*MockBillingClient)(nil).CheckBalanceCreditStartDate), varargs...)
}

// CheckBalanceOwnerAccountExistsByBalanceOwnerIdAndAccountIdV1 mocks base method.
func (m *MockBillingClient) CheckBalanceOwnerAccountExistsByBalanceOwnerIdAndAccountIdV1(ctx context.Context, in *grpc.CheckBalanceOwnerAccountExistsByBalanceOwnerIdAndAccountIdRequestV1, opts ...grpc0.CallOption) (*emptypb.Empty, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "CheckBalanceOwnerAccountExistsByBalanceOwnerIdAndAccountIdV1", varargs...)
	ret0, _ := ret[0].(*emptypb.Empty)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CheckBalanceOwnerAccountExistsByBalanceOwnerIdAndAccountIdV1 indicates an expected call of CheckBalanceOwnerAccountExistsByBalanceOwnerIdAndAccountIdV1.
func (mr *MockBillingClientMockRecorder) CheckBalanceOwnerAccountExistsByBalanceOwnerIdAndAccountIdV1(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CheckBalanceOwnerAccountExistsByBalanceOwnerIdAndAccountIdV1", reflect.TypeOf((*MockBillingClient)(nil).CheckBalanceOwnerAccountExistsByBalanceOwnerIdAndAccountIdV1), varargs...)
}

// CheckHasBalanceV1 mocks base method.
func (m *MockBillingClient) CheckHasBalanceV1(ctx context.Context, in *grpc.CheckHasBalanceRequestV1, opts ...grpc0.CallOption) (*emptypb.Empty, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "CheckHasBalanceV1", varargs...)
	ret0, _ := ret[0].(*emptypb.Empty)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CheckHasBalanceV1 indicates an expected call of CheckHasBalanceV1.
func (mr *MockBillingClientMockRecorder) CheckHasBalanceV1(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CheckHasBalanceV1", reflect.TypeOf((*MockBillingClient)(nil).CheckHasBalanceV1), varargs...)
}

// CheckOutTransferBalanceV1 mocks base method.
func (m *MockBillingClient) CheckOutTransferBalanceV1(ctx context.Context, in *grpc.CheckOutTransferBalanceRequestV1, opts ...grpc0.CallOption) (*grpc.CheckOutTransferBalanceResponseV1, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "CheckOutTransferBalanceV1", varargs...)
	ret0, _ := ret[0].(*grpc.CheckOutTransferBalanceResponseV1)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CheckOutTransferBalanceV1 indicates an expected call of CheckOutTransferBalanceV1.
func (mr *MockBillingClientMockRecorder) CheckOutTransferBalanceV1(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CheckOutTransferBalanceV1", reflect.TypeOf((*MockBillingClient)(nil).CheckOutTransferBalanceV1), varargs...)
}

// CheckPayOutBalanceV1 mocks base method.
func (m *MockBillingClient) CheckPayOutBalanceV1(ctx context.Context, in *grpc.CheckPayOutBalanceReqV1, opts ...grpc0.CallOption) (*grpc.CheckPayOutBalanceResV1, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "CheckPayOutBalanceV1", varargs...)
	ret0, _ := ret[0].(*grpc.CheckPayOutBalanceResV1)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CheckPayOutBalanceV1 indicates an expected call of CheckPayOutBalanceV1.
func (mr *MockBillingClientMockRecorder) CheckPayOutBalanceV1(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CheckPayOutBalanceV1", reflect.TypeOf((*MockBillingClient)(nil).CheckPayOutBalanceV1), varargs...)
}

// GetBalanceAccountByNumber mocks base method.
func (m *MockBillingClient) GetBalanceAccountByNumber(ctx context.Context, in *grpc.GetBalanceAccountByNumberRequest, opts ...grpc0.CallOption) (*grpc.GetBalanceAccountByNumberResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetBalanceAccountByNumber", varargs...)
	ret0, _ := ret[0].(*grpc.GetBalanceAccountByNumberResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetBalanceAccountByNumber indicates an expected call of GetBalanceAccountByNumber.
func (mr *MockBillingClientMockRecorder) GetBalanceAccountByNumber(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetBalanceAccountByNumber", reflect.TypeOf((*MockBillingClient)(nil).GetBalanceAccountByNumber), varargs...)
}

// GetBalanceByIDV1 mocks base method.
func (m *MockBillingClient) GetBalanceByIDV1(ctx context.Context, in *grpc.GetBalanceByIDRequestV1, opts ...grpc0.CallOption) (*grpc.GetBalanceResponseV1, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetBalanceByIDV1", varargs...)
	ret0, _ := ret[0].(*grpc.GetBalanceResponseV1)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetBalanceByIDV1 indicates an expected call of GetBalanceByIDV1.
func (mr *MockBillingClientMockRecorder) GetBalanceByIDV1(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetBalanceByIDV1", reflect.TypeOf((*MockBillingClient)(nil).GetBalanceByIDV1), varargs...)
}

// GetBalanceOwnerByIDV1 mocks base method.
func (m *MockBillingClient) GetBalanceOwnerByIDV1(ctx context.Context, in *grpc.GetBalanceOwnerByIDRequestV1, opts ...grpc0.CallOption) (*grpc.GetBalanceOwnerResponseV1, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetBalanceOwnerByIDV1", varargs...)
	ret0, _ := ret[0].(*grpc.GetBalanceOwnerResponseV1)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetBalanceOwnerByIDV1 indicates an expected call of GetBalanceOwnerByIDV1.
func (mr *MockBillingClientMockRecorder) GetBalanceOwnerByIDV1(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetBalanceOwnerByIDV1", reflect.TypeOf((*MockBillingClient)(nil).GetBalanceOwnerByIDV1), varargs...)
}

// GetBalanceOwnerV1 mocks base method.
func (m *MockBillingClient) GetBalanceOwnerV1(ctx context.Context, in *grpc.GetBalanceOwnerRequestV1, opts ...grpc0.CallOption) (*grpc.GetBalanceOwnerResponseV1, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetBalanceOwnerV1", varargs...)
	ret0, _ := ret[0].(*grpc.GetBalanceOwnerResponseV1)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetBalanceOwnerV1 indicates an expected call of GetBalanceOwnerV1.
func (mr *MockBillingClientMockRecorder) GetBalanceOwnerV1(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetBalanceOwnerV1", reflect.TypeOf((*MockBillingClient)(nil).GetBalanceOwnerV1), varargs...)
}

// GetCountryCodeByIDV1 mocks base method.
func (m *MockBillingClient) GetCountryCodeByIDV1(ctx context.Context, in *grpc.GetCountryCodeByIDRequestV1, opts ...grpc0.CallOption) (*grpc.GetCountryCodeResponseV1, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetCountryCodeByIDV1", varargs...)
	ret0, _ := ret[0].(*grpc.GetCountryCodeResponseV1)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetCountryCodeByIDV1 indicates an expected call of GetCountryCodeByIDV1.
func (mr *MockBillingClientMockRecorder) GetCountryCodeByIDV1(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetCountryCodeByIDV1", reflect.TypeOf((*MockBillingClient)(nil).GetCountryCodeByIDV1), varargs...)
}

// GetCurrentBalanceAmountByBalanceOwnerID mocks base method.
func (m *MockBillingClient) GetCurrentBalanceAmountByBalanceOwnerID(ctx context.Context, in *grpc.GetCurrentBalanceAmountByAccountAndOwnerIDRequest, opts ...grpc0.CallOption) (*grpc.GetCurrentBalanceAmountByAccountAndOwnerIDResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetCurrentBalanceAmountByBalanceOwnerID", varargs...)
	ret0, _ := ret[0].(*grpc.GetCurrentBalanceAmountByAccountAndOwnerIDResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetCurrentBalanceAmountByBalanceOwnerID indicates an expected call of GetCurrentBalanceAmountByBalanceOwnerID.
func (mr *MockBillingClientMockRecorder) GetCurrentBalanceAmountByBalanceOwnerID(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetCurrentBalanceAmountByBalanceOwnerID", reflect.TypeOf((*MockBillingClient)(nil).GetCurrentBalanceAmountByBalanceOwnerID), varargs...)
}

// GetEntityTypeByIDV1 mocks base method.
func (m *MockBillingClient) GetEntityTypeByIDV1(ctx context.Context, in *grpc.GetEntityTypeByIDRequestV1, opts ...grpc0.CallOption) (*grpc.GetEntityTypeResponseV1, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetEntityTypeByIDV1", varargs...)
	ret0, _ := ret[0].(*grpc.GetEntityTypeResponseV1)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetEntityTypeByIDV1 indicates an expected call of GetEntityTypeByIDV1.
func (mr *MockBillingClientMockRecorder) GetEntityTypeByIDV1(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetEntityTypeByIDV1", reflect.TypeOf((*MockBillingClient)(nil).GetEntityTypeByIDV1), varargs...)
}

// GetMerchantByBalanceOwnerIDV1 mocks base method.
func (m *MockBillingClient) GetMerchantByBalanceOwnerIDV1(ctx context.Context, in *grpc.GetMerchantByBalanceOwnerRequestV1, opts ...grpc0.CallOption) (*grpc.GetMerchantByBalanceOwnerResponseV1, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetMerchantByBalanceOwnerIDV1", varargs...)
	ret0, _ := ret[0].(*grpc.GetMerchantByBalanceOwnerResponseV1)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetMerchantByBalanceOwnerIDV1 indicates an expected call of GetMerchantByBalanceOwnerIDV1.
func (mr *MockBillingClientMockRecorder) GetMerchantByBalanceOwnerIDV1(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetMerchantByBalanceOwnerIDV1", reflect.TypeOf((*MockBillingClient)(nil).GetMerchantByBalanceOwnerIDV1), varargs...)
}

// RecalculateCreditBalances mocks base method.
func (m *MockBillingClient) RecalculateCreditBalances(ctx context.Context, in *emptypb.Empty, opts ...grpc0.CallOption) (*emptypb.Empty, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "RecalculateCreditBalances", varargs...)
	ret0, _ := ret[0].(*emptypb.Empty)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// RecalculateCreditBalances indicates an expected call of RecalculateCreditBalances.
func (mr *MockBillingClientMockRecorder) RecalculateCreditBalances(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "RecalculateCreditBalances", reflect.TypeOf((*MockBillingClient)(nil).RecalculateCreditBalances), varargs...)
}

// RecalculateFinalBalances mocks base method.
func (m *MockBillingClient) RecalculateFinalBalances(ctx context.Context, in *emptypb.Empty, opts ...grpc0.CallOption) (*emptypb.Empty, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "RecalculateFinalBalances", varargs...)
	ret0, _ := ret[0].(*emptypb.Empty)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// RecalculateFinalBalances indicates an expected call of RecalculateFinalBalances.
func (mr *MockBillingClientMockRecorder) RecalculateFinalBalances(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "RecalculateFinalBalances", reflect.TypeOf((*MockBillingClient)(nil).RecalculateFinalBalances), varargs...)
}

// RecalculateProvisionalBalances mocks base method.
func (m *MockBillingClient) RecalculateProvisionalBalances(ctx context.Context, in *emptypb.Empty, opts ...grpc0.CallOption) (*emptypb.Empty, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "RecalculateProvisionalBalances", varargs...)
	ret0, _ := ret[0].(*emptypb.Empty)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// RecalculateProvisionalBalances indicates an expected call of RecalculateProvisionalBalances.
func (mr *MockBillingClientMockRecorder) RecalculateProvisionalBalances(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "RecalculateProvisionalBalances", reflect.TypeOf((*MockBillingClient)(nil).RecalculateProvisionalBalances), varargs...)
}

// SetBalanceOwnerSplittableV1 mocks base method.
func (m *MockBillingClient) SetBalanceOwnerSplittableV1(ctx context.Context, in *grpc.SetBalanceOwnerSplittableRequestV1, opts ...grpc0.CallOption) (*emptypb.Empty, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "SetBalanceOwnerSplittableV1", varargs...)
	ret0, _ := ret[0].(*emptypb.Empty)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SetBalanceOwnerSplittableV1 indicates an expected call of SetBalanceOwnerSplittableV1.
func (mr *MockBillingClientMockRecorder) SetBalanceOwnerSplittableV1(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetBalanceOwnerSplittableV1", reflect.TypeOf((*MockBillingClient)(nil).SetBalanceOwnerSplittableV1), varargs...)
}

// SetInTransferV1 mocks base method.
func (m *MockBillingClient) SetInTransferV1(ctx context.Context, in *grpc.SetInTransferRequestV1, opts ...grpc0.CallOption) (*emptypb.Empty, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "SetInTransferV1", varargs...)
	ret0, _ := ret[0].(*emptypb.Empty)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SetInTransferV1 indicates an expected call of SetInTransferV1.
func (mr *MockBillingClientMockRecorder) SetInTransferV1(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetInTransferV1", reflect.TypeOf((*MockBillingClient)(nil).SetInTransferV1), varargs...)
}

// MockBillingServer is a mock of BillingServer interface.
type MockBillingServer struct {
	ctrl     *gomock.Controller
	recorder *MockBillingServerMockRecorder
}

// MockBillingServerMockRecorder is the mock recorder for MockBillingServer.
type MockBillingServerMockRecorder struct {
	mock *MockBillingServer
}

// NewMockBillingServer creates a new mock instance.
func NewMockBillingServer(ctrl *gomock.Controller) *MockBillingServer {
	mock := &MockBillingServer{ctrl: ctrl}
	mock.recorder = &MockBillingServerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockBillingServer) EXPECT() *MockBillingServerMockRecorder {
	return m.recorder
}

// BillInTransferV1 mocks base method.
func (m *MockBillingServer) BillInTransferV1(arg0 context.Context, arg1 *grpc.BillInTransferRequestV1) (*emptypb.Empty, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BillInTransferV1", arg0, arg1)
	ret0, _ := ret[0].(*emptypb.Empty)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BillInTransferV1 indicates an expected call of BillInTransferV1.
func (mr *MockBillingServerMockRecorder) BillInTransferV1(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BillInTransferV1", reflect.TypeOf((*MockBillingServer)(nil).BillInTransferV1), arg0, arg1)
}

// BillOutTransferV1 mocks base method.
func (m *MockBillingServer) BillOutTransferV1(arg0 context.Context, arg1 *grpc.BillOutTransferRequestV1) (*emptypb.Empty, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BillOutTransferV1", arg0, arg1)
	ret0, _ := ret[0].(*emptypb.Empty)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BillOutTransferV1 indicates an expected call of BillOutTransferV1.
func (mr *MockBillingServerMockRecorder) BillOutTransferV1(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BillOutTransferV1", reflect.TypeOf((*MockBillingServer)(nil).BillOutTransferV1), arg0, arg1)
}

// BillPayInTransactionV1 mocks base method.
func (m *MockBillingServer) BillPayInTransactionV1(arg0 context.Context, arg1 *grpc.BillPayInTransactionRequestV1) (*emptypb.Empty, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BillPayInTransactionV1", arg0, arg1)
	ret0, _ := ret[0].(*emptypb.Empty)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BillPayInTransactionV1 indicates an expected call of BillPayInTransactionV1.
func (mr *MockBillingServerMockRecorder) BillPayInTransactionV1(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BillPayInTransactionV1", reflect.TypeOf((*MockBillingServer)(nil).BillPayInTransactionV1), arg0, arg1)
}

// BillPayOutTransactionV1 mocks base method.
func (m *MockBillingServer) BillPayOutTransactionV1(arg0 context.Context, arg1 *grpc.BillPayOutTransactionRequestV1) (*emptypb.Empty, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BillPayOutTransactionV1", arg0, arg1)
	ret0, _ := ret[0].(*emptypb.Empty)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BillPayOutTransactionV1 indicates an expected call of BillPayOutTransactionV1.
func (mr *MockBillingServerMockRecorder) BillPayOutTransactionV1(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BillPayOutTransactionV1", reflect.TypeOf((*MockBillingServer)(nil).BillPayOutTransactionV1), arg0, arg1)
}

// BillRefundTransactionV1 mocks base method.
func (m *MockBillingServer) BillRefundTransactionV1(arg0 context.Context, arg1 *grpc.BillRefundTransactionRequestV1) (*emptypb.Empty, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BillRefundTransactionV1", arg0, arg1)
	ret0, _ := ret[0].(*emptypb.Empty)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BillRefundTransactionV1 indicates an expected call of BillRefundTransactionV1.
func (mr *MockBillingServerMockRecorder) BillRefundTransactionV1(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BillRefundTransactionV1", reflect.TypeOf((*MockBillingServer)(nil).BillRefundTransactionV1), arg0, arg1)
}

// BillSplitTransferV1 mocks base method.
func (m *MockBillingServer) BillSplitTransferV1(arg0 context.Context, arg1 *grpc.BillSplitTransferRequestV1) (*emptypb.Empty, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BillSplitTransferV1", arg0, arg1)
	ret0, _ := ret[0].(*emptypb.Empty)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BillSplitTransferV1 indicates an expected call of BillSplitTransferV1.
func (mr *MockBillingServerMockRecorder) BillSplitTransferV1(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BillSplitTransferV1", reflect.TypeOf((*MockBillingServer)(nil).BillSplitTransferV1), arg0, arg1)
}

// CheckBalanceCreditExpireDate mocks base method.
func (m *MockBillingServer) CheckBalanceCreditExpireDate(arg0 context.Context, arg1 *emptypb.Empty) (*emptypb.Empty, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CheckBalanceCreditExpireDate", arg0, arg1)
	ret0, _ := ret[0].(*emptypb.Empty)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CheckBalanceCreditExpireDate indicates an expected call of CheckBalanceCreditExpireDate.
func (mr *MockBillingServerMockRecorder) CheckBalanceCreditExpireDate(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CheckBalanceCreditExpireDate", reflect.TypeOf((*MockBillingServer)(nil).CheckBalanceCreditExpireDate), arg0, arg1)
}

// CheckBalanceCreditStartDate mocks base method.
func (m *MockBillingServer) CheckBalanceCreditStartDate(arg0 context.Context, arg1 *emptypb.Empty) (*emptypb.Empty, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CheckBalanceCreditStartDate", arg0, arg1)
	ret0, _ := ret[0].(*emptypb.Empty)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CheckBalanceCreditStartDate indicates an expected call of CheckBalanceCreditStartDate.
func (mr *MockBillingServerMockRecorder) CheckBalanceCreditStartDate(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CheckBalanceCreditStartDate", reflect.TypeOf((*MockBillingServer)(nil).CheckBalanceCreditStartDate), arg0, arg1)
}

// CheckBalanceOwnerAccountExistsByBalanceOwnerIdAndAccountIdV1 mocks base method.
func (m *MockBillingServer) CheckBalanceOwnerAccountExistsByBalanceOwnerIdAndAccountIdV1(arg0 context.Context, arg1 *grpc.CheckBalanceOwnerAccountExistsByBalanceOwnerIdAndAccountIdRequestV1) (*emptypb.Empty, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CheckBalanceOwnerAccountExistsByBalanceOwnerIdAndAccountIdV1", arg0, arg1)
	ret0, _ := ret[0].(*emptypb.Empty)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CheckBalanceOwnerAccountExistsByBalanceOwnerIdAndAccountIdV1 indicates an expected call of CheckBalanceOwnerAccountExistsByBalanceOwnerIdAndAccountIdV1.
func (mr *MockBillingServerMockRecorder) CheckBalanceOwnerAccountExistsByBalanceOwnerIdAndAccountIdV1(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CheckBalanceOwnerAccountExistsByBalanceOwnerIdAndAccountIdV1", reflect.TypeOf((*MockBillingServer)(nil).CheckBalanceOwnerAccountExistsByBalanceOwnerIdAndAccountIdV1), arg0, arg1)
}

// CheckHasBalanceV1 mocks base method.
func (m *MockBillingServer) CheckHasBalanceV1(arg0 context.Context, arg1 *grpc.CheckHasBalanceRequestV1) (*emptypb.Empty, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CheckHasBalanceV1", arg0, arg1)
	ret0, _ := ret[0].(*emptypb.Empty)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CheckHasBalanceV1 indicates an expected call of CheckHasBalanceV1.
func (mr *MockBillingServerMockRecorder) CheckHasBalanceV1(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CheckHasBalanceV1", reflect.TypeOf((*MockBillingServer)(nil).CheckHasBalanceV1), arg0, arg1)
}

// CheckOutTransferBalanceV1 mocks base method.
func (m *MockBillingServer) CheckOutTransferBalanceV1(arg0 context.Context, arg1 *grpc.CheckOutTransferBalanceRequestV1) (*grpc.CheckOutTransferBalanceResponseV1, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CheckOutTransferBalanceV1", arg0, arg1)
	ret0, _ := ret[0].(*grpc.CheckOutTransferBalanceResponseV1)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CheckOutTransferBalanceV1 indicates an expected call of CheckOutTransferBalanceV1.
func (mr *MockBillingServerMockRecorder) CheckOutTransferBalanceV1(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CheckOutTransferBalanceV1", reflect.TypeOf((*MockBillingServer)(nil).CheckOutTransferBalanceV1), arg0, arg1)
}

// CheckPayOutBalanceV1 mocks base method.
func (m *MockBillingServer) CheckPayOutBalanceV1(arg0 context.Context, arg1 *grpc.CheckPayOutBalanceReqV1) (*grpc.CheckPayOutBalanceResV1, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CheckPayOutBalanceV1", arg0, arg1)
	ret0, _ := ret[0].(*grpc.CheckPayOutBalanceResV1)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CheckPayOutBalanceV1 indicates an expected call of CheckPayOutBalanceV1.
func (mr *MockBillingServerMockRecorder) CheckPayOutBalanceV1(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CheckPayOutBalanceV1", reflect.TypeOf((*MockBillingServer)(nil).CheckPayOutBalanceV1), arg0, arg1)
}

// GetBalanceAccountByNumber mocks base method.
func (m *MockBillingServer) GetBalanceAccountByNumber(arg0 context.Context, arg1 *grpc.GetBalanceAccountByNumberRequest) (*grpc.GetBalanceAccountByNumberResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetBalanceAccountByNumber", arg0, arg1)
	ret0, _ := ret[0].(*grpc.GetBalanceAccountByNumberResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetBalanceAccountByNumber indicates an expected call of GetBalanceAccountByNumber.
func (mr *MockBillingServerMockRecorder) GetBalanceAccountByNumber(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetBalanceAccountByNumber", reflect.TypeOf((*MockBillingServer)(nil).GetBalanceAccountByNumber), arg0, arg1)
}

// GetBalanceByIDV1 mocks base method.
func (m *MockBillingServer) GetBalanceByIDV1(arg0 context.Context, arg1 *grpc.GetBalanceByIDRequestV1) (*grpc.GetBalanceResponseV1, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetBalanceByIDV1", arg0, arg1)
	ret0, _ := ret[0].(*grpc.GetBalanceResponseV1)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetBalanceByIDV1 indicates an expected call of GetBalanceByIDV1.
func (mr *MockBillingServerMockRecorder) GetBalanceByIDV1(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetBalanceByIDV1", reflect.TypeOf((*MockBillingServer)(nil).GetBalanceByIDV1), arg0, arg1)
}

// GetBalanceOwnerByIDV1 mocks base method.
func (m *MockBillingServer) GetBalanceOwnerByIDV1(arg0 context.Context, arg1 *grpc.GetBalanceOwnerByIDRequestV1) (*grpc.GetBalanceOwnerResponseV1, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetBalanceOwnerByIDV1", arg0, arg1)
	ret0, _ := ret[0].(*grpc.GetBalanceOwnerResponseV1)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetBalanceOwnerByIDV1 indicates an expected call of GetBalanceOwnerByIDV1.
func (mr *MockBillingServerMockRecorder) GetBalanceOwnerByIDV1(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetBalanceOwnerByIDV1", reflect.TypeOf((*MockBillingServer)(nil).GetBalanceOwnerByIDV1), arg0, arg1)
}

// GetBalanceOwnerV1 mocks base method.
func (m *MockBillingServer) GetBalanceOwnerV1(arg0 context.Context, arg1 *grpc.GetBalanceOwnerRequestV1) (*grpc.GetBalanceOwnerResponseV1, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetBalanceOwnerV1", arg0, arg1)
	ret0, _ := ret[0].(*grpc.GetBalanceOwnerResponseV1)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetBalanceOwnerV1 indicates an expected call of GetBalanceOwnerV1.
func (mr *MockBillingServerMockRecorder) GetBalanceOwnerV1(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetBalanceOwnerV1", reflect.TypeOf((*MockBillingServer)(nil).GetBalanceOwnerV1), arg0, arg1)
}

// GetCountryCodeByIDV1 mocks base method.
func (m *MockBillingServer) GetCountryCodeByIDV1(arg0 context.Context, arg1 *grpc.GetCountryCodeByIDRequestV1) (*grpc.GetCountryCodeResponseV1, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetCountryCodeByIDV1", arg0, arg1)
	ret0, _ := ret[0].(*grpc.GetCountryCodeResponseV1)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetCountryCodeByIDV1 indicates an expected call of GetCountryCodeByIDV1.
func (mr *MockBillingServerMockRecorder) GetCountryCodeByIDV1(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetCountryCodeByIDV1", reflect.TypeOf((*MockBillingServer)(nil).GetCountryCodeByIDV1), arg0, arg1)
}

// GetCurrentBalanceAmountByBalanceOwnerID mocks base method.
func (m *MockBillingServer) GetCurrentBalanceAmountByBalanceOwnerID(arg0 context.Context, arg1 *grpc.GetCurrentBalanceAmountByAccountAndOwnerIDRequest) (*grpc.GetCurrentBalanceAmountByAccountAndOwnerIDResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetCurrentBalanceAmountByBalanceOwnerID", arg0, arg1)
	ret0, _ := ret[0].(*grpc.GetCurrentBalanceAmountByAccountAndOwnerIDResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetCurrentBalanceAmountByBalanceOwnerID indicates an expected call of GetCurrentBalanceAmountByBalanceOwnerID.
func (mr *MockBillingServerMockRecorder) GetCurrentBalanceAmountByBalanceOwnerID(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetCurrentBalanceAmountByBalanceOwnerID", reflect.TypeOf((*MockBillingServer)(nil).GetCurrentBalanceAmountByBalanceOwnerID), arg0, arg1)
}

// GetEntityTypeByIDV1 mocks base method.
func (m *MockBillingServer) GetEntityTypeByIDV1(arg0 context.Context, arg1 *grpc.GetEntityTypeByIDRequestV1) (*grpc.GetEntityTypeResponseV1, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetEntityTypeByIDV1", arg0, arg1)
	ret0, _ := ret[0].(*grpc.GetEntityTypeResponseV1)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetEntityTypeByIDV1 indicates an expected call of GetEntityTypeByIDV1.
func (mr *MockBillingServerMockRecorder) GetEntityTypeByIDV1(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetEntityTypeByIDV1", reflect.TypeOf((*MockBillingServer)(nil).GetEntityTypeByIDV1), arg0, arg1)
}

// GetMerchantByBalanceOwnerIDV1 mocks base method.
func (m *MockBillingServer) GetMerchantByBalanceOwnerIDV1(arg0 context.Context, arg1 *grpc.GetMerchantByBalanceOwnerRequestV1) (*grpc.GetMerchantByBalanceOwnerResponseV1, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetMerchantByBalanceOwnerIDV1", arg0, arg1)
	ret0, _ := ret[0].(*grpc.GetMerchantByBalanceOwnerResponseV1)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetMerchantByBalanceOwnerIDV1 indicates an expected call of GetMerchantByBalanceOwnerIDV1.
func (mr *MockBillingServerMockRecorder) GetMerchantByBalanceOwnerIDV1(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetMerchantByBalanceOwnerIDV1", reflect.TypeOf((*MockBillingServer)(nil).GetMerchantByBalanceOwnerIDV1), arg0, arg1)
}

// RecalculateCreditBalances mocks base method.
func (m *MockBillingServer) RecalculateCreditBalances(arg0 context.Context, arg1 *emptypb.Empty) (*emptypb.Empty, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "RecalculateCreditBalances", arg0, arg1)
	ret0, _ := ret[0].(*emptypb.Empty)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// RecalculateCreditBalances indicates an expected call of RecalculateCreditBalances.
func (mr *MockBillingServerMockRecorder) RecalculateCreditBalances(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "RecalculateCreditBalances", reflect.TypeOf((*MockBillingServer)(nil).RecalculateCreditBalances), arg0, arg1)
}

// RecalculateFinalBalances mocks base method.
func (m *MockBillingServer) RecalculateFinalBalances(arg0 context.Context, arg1 *emptypb.Empty) (*emptypb.Empty, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "RecalculateFinalBalances", arg0, arg1)
	ret0, _ := ret[0].(*emptypb.Empty)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// RecalculateFinalBalances indicates an expected call of RecalculateFinalBalances.
func (mr *MockBillingServerMockRecorder) RecalculateFinalBalances(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "RecalculateFinalBalances", reflect.TypeOf((*MockBillingServer)(nil).RecalculateFinalBalances), arg0, arg1)
}

// RecalculateProvisionalBalances mocks base method.
func (m *MockBillingServer) RecalculateProvisionalBalances(arg0 context.Context, arg1 *emptypb.Empty) (*emptypb.Empty, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "RecalculateProvisionalBalances", arg0, arg1)
	ret0, _ := ret[0].(*emptypb.Empty)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// RecalculateProvisionalBalances indicates an expected call of RecalculateProvisionalBalances.
func (mr *MockBillingServerMockRecorder) RecalculateProvisionalBalances(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "RecalculateProvisionalBalances", reflect.TypeOf((*MockBillingServer)(nil).RecalculateProvisionalBalances), arg0, arg1)
}

// SetBalanceOwnerSplittableV1 mocks base method.
func (m *MockBillingServer) SetBalanceOwnerSplittableV1(arg0 context.Context, arg1 *grpc.SetBalanceOwnerSplittableRequestV1) (*emptypb.Empty, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SetBalanceOwnerSplittableV1", arg0, arg1)
	ret0, _ := ret[0].(*emptypb.Empty)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SetBalanceOwnerSplittableV1 indicates an expected call of SetBalanceOwnerSplittableV1.
func (mr *MockBillingServerMockRecorder) SetBalanceOwnerSplittableV1(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetBalanceOwnerSplittableV1", reflect.TypeOf((*MockBillingServer)(nil).SetBalanceOwnerSplittableV1), arg0, arg1)
}

// SetInTransferV1 mocks base method.
func (m *MockBillingServer) SetInTransferV1(arg0 context.Context, arg1 *grpc.SetInTransferRequestV1) (*emptypb.Empty, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SetInTransferV1", arg0, arg1)
	ret0, _ := ret[0].(*emptypb.Empty)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SetInTransferV1 indicates an expected call of SetInTransferV1.
func (mr *MockBillingServerMockRecorder) SetInTransferV1(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetInTransferV1", reflect.TypeOf((*MockBillingServer)(nil).SetInTransferV1), arg0, arg1)
}

// mustEmbedUnimplementedBillingServer mocks base method.
func (m *MockBillingServer) mustEmbedUnimplementedBillingServer() {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "mustEmbedUnimplementedBillingServer")
}

// mustEmbedUnimplementedBillingServer indicates an expected call of mustEmbedUnimplementedBillingServer.
func (mr *MockBillingServerMockRecorder) mustEmbedUnimplementedBillingServer() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "mustEmbedUnimplementedBillingServer", reflect.TypeOf((*MockBillingServer)(nil).mustEmbedUnimplementedBillingServer))
}

// MockUnsafeBillingServer is a mock of UnsafeBillingServer interface.
type MockUnsafeBillingServer struct {
	ctrl     *gomock.Controller
	recorder *MockUnsafeBillingServerMockRecorder
}

// MockUnsafeBillingServerMockRecorder is the mock recorder for MockUnsafeBillingServer.
type MockUnsafeBillingServerMockRecorder struct {
	mock *MockUnsafeBillingServer
}

// NewMockUnsafeBillingServer creates a new mock instance.
func NewMockUnsafeBillingServer(ctrl *gomock.Controller) *MockUnsafeBillingServer {
	mock := &MockUnsafeBillingServer{ctrl: ctrl}
	mock.recorder = &MockUnsafeBillingServerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockUnsafeBillingServer) EXPECT() *MockUnsafeBillingServerMockRecorder {
	return m.recorder
}

// mustEmbedUnimplementedBillingServer mocks base method.
func (m *MockUnsafeBillingServer) mustEmbedUnimplementedBillingServer() {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "mustEmbedUnimplementedBillingServer")
}

// mustEmbedUnimplementedBillingServer indicates an expected call of mustEmbedUnimplementedBillingServer.
func (mr *MockUnsafeBillingServerMockRecorder) mustEmbedUnimplementedBillingServer() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "mustEmbedUnimplementedBillingServer", reflect.TypeOf((*MockUnsafeBillingServer)(nil).mustEmbedUnimplementedBillingServer))
}
