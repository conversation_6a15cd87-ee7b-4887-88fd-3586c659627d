// Code generated by protoc-gen-go-mvp. DO NOT EDIT.
// versions:
// - protoc-gen-go-mvp v2.0.1
// created by mvp authors

package rest

import (
	fmt "fmt"
	zap "go.uber.org/zap"
)

func file_inner_processing_rest_alatau_city_payin_proto_message_AlatauCityCancelPaymentRequestToGo(
	label string,
	in *AlatauCityCancelPaymentRequest,
) any {
	if in == nil {
		return map[string]any{label: in}
	}

	return map[string]any{
		label: map[string]any{
			"Body": file_inner_processing_rest_alatau_city_payin_proto_message_AlatauCityCancelPaymentRequest_CancelPaymentRequestToGo("Body", in.GetBody()),
		},
	}
}

func file_inner_processing_rest_alatau_city_payin_proto_message_AlatauCityCancelPaymentRequestToZap(
	label string,
	in *AlatauCityCancelPaymentRequest,
) zap.Field {
	if in == nil {
		return zap.Reflect(label, in)
	}

	return zap.Dict(
		label,
		file_inner_processing_rest_alatau_city_payin_proto_message_AlatauCityCancelPaymentRequest_CancelPaymentRequestToZap("Body", in.GetBody()),
	)
}

func file_inner_processing_rest_alatau_city_payin_proto_message_AlatauCityCancelPaymentRequest_CancelPaymentRequestToGo(
	label string,
	in *AlatauCityCancelPaymentRequest_CancelPaymentRequest,
) any {
	if in == nil {
		return map[string]any{label: in}
	}

	return map[string]any{
		label: map[string]any{
			"ORDER":      in.GetORDER(),
			"MERCHANT":   in.GetMERCHANT(),
			"P_SIGN":     in.GetP_SIGN(),
			"REV_AMOUNT": in.GetREV_AMOUNT(),
			"REV_DESC":   in.GetREV_DESC(),
		},
	}
}

func file_inner_processing_rest_alatau_city_payin_proto_message_AlatauCityCancelPaymentRequest_CancelPaymentRequestToZap(
	label string,
	in *AlatauCityCancelPaymentRequest_CancelPaymentRequest,
) zap.Field {
	if in == nil {
		return zap.Reflect(label, in)
	}

	return zap.Dict(
		label,
		zap.Any("ORDER", in.GetORDER()),
		zap.Any("MERCHANT", in.GetMERCHANT()),
		zap.Any("P_SIGN", in.GetP_SIGN()),
		zap.Any("REV_AMOUNT", in.GetREV_AMOUNT()),
		zap.Any("REV_DESC", in.GetREV_DESC()),
	)
}

func file_inner_processing_rest_alatau_city_payin_proto_message_AlatauCityCancelPaymentResponseToGo(
	label string,
	in *AlatauCityCancelPaymentResponse,
) any {
	if in == nil {
		return map[string]any{label: in}
	}

	return map[string]any{
		label: map[string]any{
			"CancelResponse": file_inner_processing_rest_alatau_city_payin_proto_message_AlatauCityCancelPaymentResponse_CancelPaymentResponseToGo("CancelResponse", in.GetCancelResponse()),
		},
	}
}

func file_inner_processing_rest_alatau_city_payin_proto_message_AlatauCityCancelPaymentResponseToZap(
	label string,
	in *AlatauCityCancelPaymentResponse,
) zap.Field {
	if in == nil {
		return zap.Reflect(label, in)
	}

	return zap.Dict(
		label,
		file_inner_processing_rest_alatau_city_payin_proto_message_AlatauCityCancelPaymentResponse_CancelPaymentResponseToZap("CancelResponse", in.GetCancelResponse()),
	)
}

func file_inner_processing_rest_alatau_city_payin_proto_message_AlatauCityCancelPaymentResponse_CancelPaymentResponseToGo(
	label string,
	in *AlatauCityCancelPaymentResponse_CancelPaymentResponse,
) any {
	if in == nil {
		return map[string]any{label: in}
	}

	return map[string]any{
		label: map[string]any{
			"Result": file_inner_processing_rest_alatau_city_payin_proto_message_AlatauCityCancelPaymentResponse_ResultToGo("Result", in.GetResult()),
		},
	}
}

func file_inner_processing_rest_alatau_city_payin_proto_message_AlatauCityCancelPaymentResponse_CancelPaymentResponseToZap(
	label string,
	in *AlatauCityCancelPaymentResponse_CancelPaymentResponse,
) zap.Field {
	if in == nil {
		return zap.Reflect(label, in)
	}

	return zap.Dict(
		label,
		file_inner_processing_rest_alatau_city_payin_proto_message_AlatauCityCancelPaymentResponse_ResultToZap("Result", in.GetResult()),
	)
}

func file_inner_processing_rest_alatau_city_payin_proto_message_AlatauCityCancelPaymentResponse_OperationToGo(
	label string,
	in *AlatauCityCancelPaymentResponse_Operation,
) any {
	if in == nil {
		return map[string]any{label: in}
	}

	return map[string]any{
		label: map[string]any{
			"Status":     in.GetStatus(),
			"ResultDesc": in.GetResultDesc(),
			"Result":     in.GetResult(),
			"Rc":         in.GetRc(),
			"Ecode":      in.GetEcode(),
			"Edesc":      in.GetEdesc(),
			"Amount":     in.GetAmount(),
			"Rrn":        in.GetRrn(),
			"RevDesc":    in.GetRevDesc(),
			"RevDate":    in.GetRevDate(),
		},
	}
}

func file_inner_processing_rest_alatau_city_payin_proto_message_AlatauCityCancelPaymentResponse_OperationToZap(
	label string,
	in *AlatauCityCancelPaymentResponse_Operation,
) zap.Field {
	if in == nil {
		return zap.Reflect(label, in)
	}

	return zap.Dict(
		label,
		zap.Any("Status", in.GetStatus()),
		zap.Any("ResultDesc", in.GetResultDesc()),
		zap.Any("Result", in.GetResult()),
		zap.Any("Rc", in.GetRc()),
		zap.Any("Ecode", in.GetEcode()),
		zap.Any("Edesc", in.GetEdesc()),
		zap.Any("Amount", in.GetAmount()),
		zap.Any("Rrn", in.GetRrn()),
		zap.Any("RevDesc", in.GetRevDesc()),
		zap.Any("RevDate", in.GetRevDate()),
	)
}

func file_inner_processing_rest_alatau_city_payin_proto_message_AlatauCityCancelPaymentResponse_ResultToGo(
	label string,
	in *AlatauCityCancelPaymentResponse_Result,
) any {
	if in == nil {
		return map[string]any{label: in}
	}

	return map[string]any{
		label: map[string]any{
			"Code":        in.GetCode(),
			"Description": in.GetDescription(),
			"Operation":   file_inner_processing_rest_alatau_city_payin_proto_message_AlatauCityCancelPaymentResponse_OperationToGo("Operation", in.GetOperation()),
		},
	}
}

func file_inner_processing_rest_alatau_city_payin_proto_message_AlatauCityCancelPaymentResponse_ResultToZap(
	label string,
	in *AlatauCityCancelPaymentResponse_Result,
) zap.Field {
	if in == nil {
		return zap.Reflect(label, in)
	}

	return zap.Dict(
		label,
		zap.Any("Code", in.GetCode()),
		zap.Any("Description", in.GetDescription()),
		file_inner_processing_rest_alatau_city_payin_proto_message_AlatauCityCancelPaymentResponse_OperationToZap("Operation", in.GetOperation()),
	)
}

func file_inner_processing_rest_alatau_city_payin_proto_message_AlatauCityChargeRequestToGo(
	label string,
	in *AlatauCityChargeRequest,
) any {
	if in == nil {
		return map[string]any{label: in}
	}

	return map[string]any{
		label: map[string]any{
			"Body": file_inner_processing_rest_alatau_city_payin_proto_message_AlatauCityChargeRequest_ChargeRequestToGo("Body", in.GetBody()),
		},
	}
}

func file_inner_processing_rest_alatau_city_payin_proto_message_AlatauCityChargeRequestToZap(
	label string,
	in *AlatauCityChargeRequest,
) zap.Field {
	if in == nil {
		return zap.Reflect(label, in)
	}

	return zap.Dict(
		label,
		file_inner_processing_rest_alatau_city_payin_proto_message_AlatauCityChargeRequest_ChargeRequestToZap("Body", in.GetBody()),
	)
}

func file_inner_processing_rest_alatau_city_payin_proto_message_AlatauCityChargeRequest_ChargeRequestToGo(
	label string,
	in *AlatauCityChargeRequest_ChargeRequest,
) any {
	if in == nil {
		return map[string]any{label: in}
	}

	return map[string]any{
		label: map[string]any{
			"APPROVE":  in.GetAPPROVE(),
			"ORDER":    in.GetORDER(),
			"AMOUNT":   in.GetAMOUNT(),
			"MERCHANT": in.GetMERCHANT(),
			"P_SIGN":   in.GetP_SIGN(),
		},
	}
}

func file_inner_processing_rest_alatau_city_payin_proto_message_AlatauCityChargeRequest_ChargeRequestToZap(
	label string,
	in *AlatauCityChargeRequest_ChargeRequest,
) zap.Field {
	if in == nil {
		return zap.Reflect(label, in)
	}

	return zap.Dict(
		label,
		zap.Any("APPROVE", in.GetAPPROVE()),
		zap.Any("ORDER", in.GetORDER()),
		zap.Any("AMOUNT", in.GetAMOUNT()),
		zap.Any("MERCHANT", in.GetMERCHANT()),
		zap.Any("P_SIGN", in.GetP_SIGN()),
	)
}

func file_inner_processing_rest_alatau_city_payin_proto_message_AlatauCityChargeResponseToGo(
	label string,
	in *AlatauCityChargeResponse,
) any {
	if in == nil {
		return map[string]any{label: in}
	}

	return map[string]any{
		label: map[string]any{
			"ChargeResponse": file_inner_processing_rest_alatau_city_payin_proto_message_AlatauCityChargeResponse_ChargeResponseToGo("ChargeResponse", in.GetChargeResponse()),
		},
	}
}

func file_inner_processing_rest_alatau_city_payin_proto_message_AlatauCityChargeResponseToZap(
	label string,
	in *AlatauCityChargeResponse,
) zap.Field {
	if in == nil {
		return zap.Reflect(label, in)
	}

	return zap.Dict(
		label,
		file_inner_processing_rest_alatau_city_payin_proto_message_AlatauCityChargeResponse_ChargeResponseToZap("ChargeResponse", in.GetChargeResponse()),
	)
}

func file_inner_processing_rest_alatau_city_payin_proto_message_AlatauCityChargeResponse_ChargeResponseToGo(
	label string,
	in *AlatauCityChargeResponse_ChargeResponse,
) any {
	if in == nil {
		return map[string]any{label: in}
	}

	return map[string]any{
		label: map[string]any{
			"Result": file_inner_processing_rest_alatau_city_payin_proto_message_AlatauCityChargeResponse_ResultToGo("Result", in.GetResult()),
		},
	}
}

func file_inner_processing_rest_alatau_city_payin_proto_message_AlatauCityChargeResponse_ChargeResponseToZap(
	label string,
	in *AlatauCityChargeResponse_ChargeResponse,
) zap.Field {
	if in == nil {
		return zap.Reflect(label, in)
	}

	return zap.Dict(
		label,
		file_inner_processing_rest_alatau_city_payin_proto_message_AlatauCityChargeResponse_ResultToZap("Result", in.GetResult()),
	)
}

func file_inner_processing_rest_alatau_city_payin_proto_message_AlatauCityChargeResponse_ResultToGo(
	label string,
	in *AlatauCityChargeResponse_Result,
) any {
	if in == nil {
		return map[string]any{label: in}
	}

	return map[string]any{
		label: map[string]any{
			"Code":        in.GetCode(),
			"Description": in.GetDescription(),
		},
	}
}

func file_inner_processing_rest_alatau_city_payin_proto_message_AlatauCityChargeResponse_ResultToZap(
	label string,
	in *AlatauCityChargeResponse_Result,
) zap.Field {
	if in == nil {
		return zap.Reflect(label, in)
	}

	return zap.Dict(
		label,
		zap.Any("Code", in.GetCode()),
		zap.Any("Description", in.GetDescription()),
	)
}

func file_inner_processing_rest_alatau_city_payin_proto_message_AlatauCityConfirmThreeDSRequestToGo(
	label string,
	in *AlatauCityConfirmThreeDSRequest,
) any {
	if in == nil {
		return map[string]any{label: in}
	}

	return map[string]any{
		label: map[string]any{
			"Body": file_inner_processing_rest_alatau_city_payin_proto_message_AlatauCityConfirmThreeDSRequest_ConfirmThreeDSRequestToGo("Body", in.GetBody()),
		},
	}
}

func file_inner_processing_rest_alatau_city_payin_proto_message_AlatauCityConfirmThreeDSRequestToZap(
	label string,
	in *AlatauCityConfirmThreeDSRequest,
) zap.Field {
	if in == nil {
		return zap.Reflect(label, in)
	}

	return zap.Dict(
		label,
		file_inner_processing_rest_alatau_city_payin_proto_message_AlatauCityConfirmThreeDSRequest_ConfirmThreeDSRequestToZap("Body", in.GetBody()),
	)
}

func file_inner_processing_rest_alatau_city_payin_proto_message_AlatauCityConfirmThreeDSRequest_ConfirmThreeDSRequestToGo(
	label string,
	in *AlatauCityConfirmThreeDSRequest_ConfirmThreeDSRequest,
) any {
	if in == nil {
		return map[string]any{label: in}
	}

	return map[string]any{
		label: map[string]any{
			"Cres":               in.GetCres(),
			"ThreeDSSessionData": in.GetThreeDSSessionData(),
		},
	}
}

func file_inner_processing_rest_alatau_city_payin_proto_message_AlatauCityConfirmThreeDSRequest_ConfirmThreeDSRequestToZap(
	label string,
	in *AlatauCityConfirmThreeDSRequest_ConfirmThreeDSRequest,
) zap.Field {
	if in == nil {
		return zap.Reflect(label, in)
	}

	return zap.Dict(
		label,
		zap.Any("Cres", in.GetCres()),
		zap.Any("ThreeDSSessionData", in.GetThreeDSSessionData()),
	)
}

func file_inner_processing_rest_alatau_city_payin_proto_message_AlatauCityConfirmThreeDSResponseToGo(
	label string,
	in *AlatauCityConfirmThreeDSResponse,
) any {
	if in == nil {
		return map[string]any{label: in}
	}

	return map[string]any{
		label: map[string]any{
			"Response": in.GetResponse(),
		},
	}
}

func file_inner_processing_rest_alatau_city_payin_proto_message_AlatauCityConfirmThreeDSResponseToZap(
	label string,
	in *AlatauCityConfirmThreeDSResponse,
) zap.Field {
	if in == nil {
		return zap.Reflect(label, in)
	}

	return zap.Dict(
		label,
		zap.Any("Response", in.GetResponse()),
	)
}

func file_inner_processing_rest_alatau_city_payin_proto_message_AlatauCityGetStatusPayInRequestToGo(
	label string,
	in *AlatauCityGetStatusPayInRequest,
) any {
	if in == nil {
		return map[string]any{label: in}
	}

	return map[string]any{
		label: map[string]any{
			"Body": file_inner_processing_rest_alatau_city_payin_proto_message_AlatauCityGetStatusPayInRequest_GetStatusRequestToGo("Body", in.GetBody()),
		},
	}
}

func file_inner_processing_rest_alatau_city_payin_proto_message_AlatauCityGetStatusPayInRequestToZap(
	label string,
	in *AlatauCityGetStatusPayInRequest,
) zap.Field {
	if in == nil {
		return zap.Reflect(label, in)
	}

	return zap.Dict(
		label,
		file_inner_processing_rest_alatau_city_payin_proto_message_AlatauCityGetStatusPayInRequest_GetStatusRequestToZap("Body", in.GetBody()),
	)
}

func file_inner_processing_rest_alatau_city_payin_proto_message_AlatauCityGetStatusPayInRequest_GetStatusRequestToGo(
	label string,
	in *AlatauCityGetStatusPayInRequest_GetStatusRequest,
) any {
	if in == nil {
		return map[string]any{label: in}
	}

	return map[string]any{
		label: map[string]any{
			"ORDER":     in.GetORDER(),
			"MERCHANT":  in.GetMERCHANT(),
			"GETSTATUS": in.GetGETSTATUS(),
			"P_SIGN":    in.GetP_SIGN(),
		},
	}
}

func file_inner_processing_rest_alatau_city_payin_proto_message_AlatauCityGetStatusPayInRequest_GetStatusRequestToZap(
	label string,
	in *AlatauCityGetStatusPayInRequest_GetStatusRequest,
) zap.Field {
	if in == nil {
		return zap.Reflect(label, in)
	}

	return zap.Dict(
		label,
		zap.Any("ORDER", in.GetORDER()),
		zap.Any("MERCHANT", in.GetMERCHANT()),
		zap.Any("GETSTATUS", in.GetGETSTATUS()),
		zap.Any("P_SIGN", in.GetP_SIGN()),
	)
}

func file_inner_processing_rest_alatau_city_payin_proto_message_AlatauCityGetStatusPayInResponseToGo(
	label string,
	in *AlatauCityGetStatusPayInResponse,
) any {
	if in == nil {
		return map[string]any{label: in}
	}

	return map[string]any{
		label: map[string]any{
			"StatusResponse": file_inner_processing_rest_alatau_city_payin_proto_message_AlatauCityGetStatusPayInResponse_StatusResponseToGo("StatusResponse", in.GetStatusResponse()),
		},
	}
}

func file_inner_processing_rest_alatau_city_payin_proto_message_AlatauCityGetStatusPayInResponseToZap(
	label string,
	in *AlatauCityGetStatusPayInResponse,
) zap.Field {
	if in == nil {
		return zap.Reflect(label, in)
	}

	return zap.Dict(
		label,
		file_inner_processing_rest_alatau_city_payin_proto_message_AlatauCityGetStatusPayInResponse_StatusResponseToZap("StatusResponse", in.GetStatusResponse()),
	)
}

func file_inner_processing_rest_alatau_city_payin_proto_message_AlatauCityGetStatusPayInResponse_OperationToGo(
	label string,
	in *AlatauCityGetStatusPayInResponse_Operation,
) any {
	if in == nil {
		return map[string]any{label: in}
	}

	return map[string]any{
		label: map[string]any{
			"Status":       in.GetStatus(),
			"StatusDesc":   in.GetStatusDesc(),
			"Amount":       in.GetAmount(),
			"Currency":     in.GetCurrency(),
			"Description":  in.GetDescription(),
			"DescOrder":    in.GetDescOrder(),
			"Email":        in.GetEmail(),
			"Lang":         in.GetLang(),
			"MpiOrder":     in.GetMpiOrder(),
			"Terminal":     in.GetTerminal(),
			"Phone":        in.GetPhone(),
			"CardMasked":   "[***hidden***]",
			"CardName":     in.GetCardName(),
			"CardExpdt":    in.GetCardExpdt(),
			"CardToken":    in.GetCardToken(),
			"CreateDate":   in.GetCreateDate(),
			"Result":       in.GetResult(),
			"ResultDesc":   in.GetResultDesc(),
			"Rc":           in.GetRc(),
			"Rrn":          in.GetRrn(),
			"AuthCode":     in.GetAuthCode(),
			"InvId":        in.GetInvId(),
			"IntExpDate":   in.GetIntExpDate(),
			"RevMaxAmount": in.GetRevMaxAmount(),
			"RecurFreq":    in.GetRecurFreq(),
			"RecurRef":     in.GetRecurRef(),
			"RecurIntRef":  in.GetRecurIntRef(),
			"ClientId":     in.GetClientId(),
			"CardToMasked": in.GetCardToMasked(),
			"CardToToken":  in.GetCardToToken(),
			"Refunds":      file_inner_processing_rest_alatau_city_payin_proto_message_AlatauCityGetStatusPayInResponse_Operation_RefundsToGo("Refunds", in.GetRefunds()),
		},
	}
}

func file_inner_processing_rest_alatau_city_payin_proto_message_AlatauCityGetStatusPayInResponse_OperationToZap(
	label string,
	in *AlatauCityGetStatusPayInResponse_Operation,
) zap.Field {
	if in == nil {
		return zap.Reflect(label, in)
	}

	return zap.Dict(
		label,
		zap.Any("Status", in.GetStatus()),
		zap.Any("StatusDesc", in.GetStatusDesc()),
		zap.Any("Amount", in.GetAmount()),
		zap.Any("Currency", in.GetCurrency()),
		zap.Any("Description", in.GetDescription()),
		zap.Any("DescOrder", in.GetDescOrder()),
		zap.Any("Email", in.GetEmail()),
		zap.Any("Lang", in.GetLang()),
		zap.Any("MpiOrder", in.GetMpiOrder()),
		zap.Any("Terminal", in.GetTerminal()),
		zap.Any("Phone", in.GetPhone()),
		zap.Any("CardMasked", "[***hidden***]"),
		zap.Any("CardName", in.GetCardName()),
		zap.Any("CardExpdt", in.GetCardExpdt()),
		zap.Any("CardToken", in.GetCardToken()),
		zap.Any("CreateDate", in.GetCreateDate()),
		zap.Any("Result", in.GetResult()),
		zap.Any("ResultDesc", in.GetResultDesc()),
		zap.Any("Rc", in.GetRc()),
		zap.Any("Rrn", in.GetRrn()),
		zap.Any("AuthCode", in.GetAuthCode()),
		zap.Any("InvId", in.GetInvId()),
		zap.Any("IntExpDate", in.GetIntExpDate()),
		zap.Any("RevMaxAmount", in.GetRevMaxAmount()),
		zap.Any("RecurFreq", in.GetRecurFreq()),
		zap.Any("RecurRef", in.GetRecurRef()),
		zap.Any("RecurIntRef", in.GetRecurIntRef()),
		zap.Any("ClientId", in.GetClientId()),
		zap.Any("CardToMasked", in.GetCardToMasked()),
		zap.Any("CardToToken", in.GetCardToToken()),
		file_inner_processing_rest_alatau_city_payin_proto_message_AlatauCityGetStatusPayInResponse_Operation_RefundsToZap("Refunds", in.GetRefunds()),
	)
}

func file_inner_processing_rest_alatau_city_payin_proto_message_AlatauCityGetStatusPayInResponse_Operation_RecToGo(
	label string,
	in *AlatauCityGetStatusPayInResponse_Operation_Rec,
) any {
	if in == nil {
		return map[string]any{label: in}
	}

	return map[string]any{
		label: map[string]any{
			"Status":         in.GetStatus(),
			"StatusDesc":     in.GetStatusDesc(),
			"RevRc":          in.GetRevRc(),
			"RevAmount":      in.GetRevAmount(),
			"RevDescription": in.GetRevDescription(),
			"RevError":       in.GetRevError(),
			"RevDate":        in.GetRevDate(),
		},
	}
}

func file_inner_processing_rest_alatau_city_payin_proto_message_AlatauCityGetStatusPayInResponse_Operation_RecToZap(
	label string,
	in *AlatauCityGetStatusPayInResponse_Operation_Rec,
) zap.Field {
	if in == nil {
		return zap.Reflect(label, in)
	}

	return zap.Dict(
		label,
		zap.Any("Status", in.GetStatus()),
		zap.Any("StatusDesc", in.GetStatusDesc()),
		zap.Any("RevRc", in.GetRevRc()),
		zap.Any("RevAmount", in.GetRevAmount()),
		zap.Any("RevDescription", in.GetRevDescription()),
		zap.Any("RevError", in.GetRevError()),
		zap.Any("RevDate", in.GetRevDate()),
	)
}

func file_inner_processing_rest_alatau_city_payin_proto_message_AlatauCityGetStatusPayInResponse_Operation_RecSliceToGo(
	label string,
	in []*AlatauCityGetStatusPayInResponse_Operation_Rec,
) any {
	var res []any
	for index, val := range in {
		res = append(res, file_inner_processing_rest_alatau_city_payin_proto_message_AlatauCityGetStatusPayInResponse_Operation_RecToGo(fmt.Sprint(index), val))
	}

	return res
}

func file_inner_processing_rest_alatau_city_payin_proto_message_AlatauCityGetStatusPayInResponse_Operation_RecSliceToZap(
	label string,
	in []*AlatauCityGetStatusPayInResponse_Operation_Rec,
) zap.Field {
	var res []zap.Field
	for index, val := range in {
		res = append(res, file_inner_processing_rest_alatau_city_payin_proto_message_AlatauCityGetStatusPayInResponse_Operation_RecToZap(fmt.Sprint(index), val))
	}

	return zap.Dict(label, res...)
}

func file_inner_processing_rest_alatau_city_payin_proto_message_AlatauCityGetStatusPayInResponse_Operation_RefundsToGo(
	label string,
	in *AlatauCityGetStatusPayInResponse_Operation_Refunds,
) any {
	if in == nil {
		return map[string]any{label: in}
	}

	return map[string]any{
		label: map[string]any{
			"Rec": file_inner_processing_rest_alatau_city_payin_proto_message_AlatauCityGetStatusPayInResponse_Operation_RecSliceToGo("Rec", in.GetRec()),
		},
	}
}

func file_inner_processing_rest_alatau_city_payin_proto_message_AlatauCityGetStatusPayInResponse_Operation_RefundsToZap(
	label string,
	in *AlatauCityGetStatusPayInResponse_Operation_Refunds,
) zap.Field {
	if in == nil {
		return zap.Reflect(label, in)
	}

	return zap.Dict(
		label,
		file_inner_processing_rest_alatau_city_payin_proto_message_AlatauCityGetStatusPayInResponse_Operation_RecSliceToZap("Rec", in.GetRec()),
	)
}

func file_inner_processing_rest_alatau_city_payin_proto_message_AlatauCityGetStatusPayInResponse_ResultToGo(
	label string,
	in *AlatauCityGetStatusPayInResponse_Result,
) any {
	if in == nil {
		return map[string]any{label: in}
	}

	return map[string]any{
		label: map[string]any{
			"Code":        in.GetCode(),
			"Description": in.GetDescription(),
			"Operation":   file_inner_processing_rest_alatau_city_payin_proto_message_AlatauCityGetStatusPayInResponse_OperationToGo("Operation", in.GetOperation()),
		},
	}
}

func file_inner_processing_rest_alatau_city_payin_proto_message_AlatauCityGetStatusPayInResponse_ResultToZap(
	label string,
	in *AlatauCityGetStatusPayInResponse_Result,
) zap.Field {
	if in == nil {
		return zap.Reflect(label, in)
	}

	return zap.Dict(
		label,
		zap.Any("Code", in.GetCode()),
		zap.Any("Description", in.GetDescription()),
		file_inner_processing_rest_alatau_city_payin_proto_message_AlatauCityGetStatusPayInResponse_OperationToZap("Operation", in.GetOperation()),
	)
}

func file_inner_processing_rest_alatau_city_payin_proto_message_AlatauCityGetStatusPayInResponse_StatusResponseToGo(
	label string,
	in *AlatauCityGetStatusPayInResponse_StatusResponse,
) any {
	if in == nil {
		return map[string]any{label: in}
	}

	return map[string]any{
		label: map[string]any{
			"Result": file_inner_processing_rest_alatau_city_payin_proto_message_AlatauCityGetStatusPayInResponse_ResultToGo("Result", in.GetResult()),
		},
	}
}

func file_inner_processing_rest_alatau_city_payin_proto_message_AlatauCityGetStatusPayInResponse_StatusResponseToZap(
	label string,
	in *AlatauCityGetStatusPayInResponse_StatusResponse,
) zap.Field {
	if in == nil {
		return zap.Reflect(label, in)
	}

	return zap.Dict(
		label,
		file_inner_processing_rest_alatau_city_payin_proto_message_AlatauCityGetStatusPayInResponse_ResultToZap("Result", in.GetResult()),
	)
}

func file_inner_processing_rest_alatau_city_payin_proto_message_AlatauCityPayInRequestToGo(
	label string,
	in *AlatauCityPayInRequest,
) any {
	if in == nil {
		return map[string]any{label: in}
	}

	return map[string]any{
		label: map[string]any{
			"AMOUNT":                   in.GetAMOUNT(),
			"BACKREF":                  in.GetBACKREF(),
			"CLIENT_ID":                in.GetCLIENT_ID(),
			"CURRENCY":                 in.GetCURRENCY(),
			"DESC":                     in.GetDESC(),
			"DESC_ORDER":               in.GetDESC_ORDER(),
			"EMAIL":                    in.GetEMAIL(),
			"EXT_MPI_ECI":              in.GetEXT_MPI_ECI(),
			"INT_REF":                  in.GetINT_REF(),
			"LANGUAGE":                 in.GetLANGUAGE(),
			"MERCHANT":                 in.GetMERCHANT(),
			"MERCH_PAYTO_TOKEN_ID":     in.GetMERCH_PAYTO_TOKEN_ID(),
			"MERCH_RN_ID":              in.GetMERCH_RN_ID(),
			"MERCH_TOKEN_ID":           in.GetMERCH_TOKEN_ID(),
			"MERCH_TRAN_STATE":         in.GetMERCH_TRAN_STATE(),
			"MK_TOKEN":                 in.GetMK_TOKEN(),
			"M_INFO":                   in.GetM_INFO(),
			"NAME":                     in.GetNAME(),
			"NONCE":                    in.GetNONCE(),
			"ORDER":                    in.GetORDER(),
			"PAYMENT_TO":               in.GetPAYMENT_TO(),
			"P_SIGN":                   in.GetP_SIGN(),
			"RECUR_EXP":                in.GetRECUR_EXP(),
			"RECUR_FREQ":               in.GetRECUR_FREQ(),
			"RECUR_REF":                in.GetRECUR_REF(),
			"TAVV":                     in.GetTAVV(),
			"TERMINAL":                 in.GetTERMINAL(),
			"Ucaf_Authentication_Data": in.GetUcaf_Authentication_Data(),
			"Ucaf_Flag":                in.GetUcaf_Flag(),
			"WTYPE":                    in.GetWTYPE(),
			"CrdCvc":                   "[***hidden***]",
			"CrdExp":                   "[***hidden***]",
			"CrdPan":                   "[***hidden***]",
			"REF":                      in.GetREF(),
			"MERCH_3D_TERM_URL":        in.GetMERCH_3D_TERM_URL(),
			"MERCH_SCA":                in.GetMERCH_SCA(),
		},
	}
}

func file_inner_processing_rest_alatau_city_payin_proto_message_AlatauCityPayInRequestToZap(
	label string,
	in *AlatauCityPayInRequest,
) zap.Field {
	if in == nil {
		return zap.Reflect(label, in)
	}

	return zap.Dict(
		label,
		zap.Any("AMOUNT", in.GetAMOUNT()),
		zap.Any("BACKREF", in.GetBACKREF()),
		zap.Any("CLIENT_ID", in.GetCLIENT_ID()),
		zap.Any("CURRENCY", in.GetCURRENCY()),
		zap.Any("DESC", in.GetDESC()),
		zap.Any("DESC_ORDER", in.GetDESC_ORDER()),
		zap.Any("EMAIL", in.GetEMAIL()),
		zap.Any("EXT_MPI_ECI", in.GetEXT_MPI_ECI()),
		zap.Any("INT_REF", in.GetINT_REF()),
		zap.Any("LANGUAGE", in.GetLANGUAGE()),
		zap.Any("MERCHANT", in.GetMERCHANT()),
		zap.Any("MERCH_PAYTO_TOKEN_ID", in.GetMERCH_PAYTO_TOKEN_ID()),
		zap.Any("MERCH_RN_ID", in.GetMERCH_RN_ID()),
		zap.Any("MERCH_TOKEN_ID", in.GetMERCH_TOKEN_ID()),
		zap.Any("MERCH_TRAN_STATE", in.GetMERCH_TRAN_STATE()),
		zap.Any("MK_TOKEN", in.GetMK_TOKEN()),
		zap.Any("M_INFO", in.GetM_INFO()),
		zap.Any("NAME", in.GetNAME()),
		zap.Any("NONCE", in.GetNONCE()),
		zap.Any("ORDER", in.GetORDER()),
		zap.Any("PAYMENT_TO", in.GetPAYMENT_TO()),
		zap.Any("P_SIGN", in.GetP_SIGN()),
		zap.Any("RECUR_EXP", in.GetRECUR_EXP()),
		zap.Any("RECUR_FREQ", in.GetRECUR_FREQ()),
		zap.Any("RECUR_REF", in.GetRECUR_REF()),
		zap.Any("TAVV", in.GetTAVV()),
		zap.Any("TERMINAL", in.GetTERMINAL()),
		zap.Any("Ucaf_Authentication_Data", in.GetUcaf_Authentication_Data()),
		zap.Any("Ucaf_Flag", in.GetUcaf_Flag()),
		zap.Any("WTYPE", in.GetWTYPE()),
		zap.Any("CrdCvc", "[***hidden***]"),
		zap.Any("CrdExp", "[***hidden***]"),
		zap.Any("CrdPan", "[***hidden***]"),
		zap.Any("REF", in.GetREF()),
		zap.Any("MERCH_3D_TERM_URL", in.GetMERCH_3D_TERM_URL()),
		zap.Any("MERCH_SCA", in.GetMERCH_SCA()),
	)
}

func file_inner_processing_rest_alatau_city_payin_proto_message_AlatauCityProcessNoAcceptPayRequestToGo(
	label string,
	in *AlatauCityProcessNoAcceptPayRequest,
) any {
	if in == nil {
		return map[string]any{label: in}
	}

	return map[string]any{
		label: map[string]any{
			"Body": file_inner_processing_rest_alatau_city_payin_proto_message_AlatauCityPayInRequestToGo("Body", in.GetBody()),
		},
	}
}

func file_inner_processing_rest_alatau_city_payin_proto_message_AlatauCityProcessNoAcceptPayRequestToZap(
	label string,
	in *AlatauCityProcessNoAcceptPayRequest,
) zap.Field {
	if in == nil {
		return zap.Reflect(label, in)
	}

	return zap.Dict(
		label,
		file_inner_processing_rest_alatau_city_payin_proto_message_AlatauCityPayInRequestToZap("Body", in.GetBody()),
	)
}

func file_inner_processing_rest_alatau_city_payin_proto_message_AlatauCityProcessNoAcceptPayResponseToGo(
	label string,
	in *AlatauCityProcessNoAcceptPayResponse,
) any {
	if in == nil {
		return map[string]any{label: in}
	}

	return map[string]any{
		label: map[string]any{
			"Response": in.GetResponse(),
		},
	}
}

func file_inner_processing_rest_alatau_city_payin_proto_message_AlatauCityProcessNoAcceptPayResponseToZap(
	label string,
	in *AlatauCityProcessNoAcceptPayResponse,
) zap.Field {
	if in == nil {
		return zap.Reflect(label, in)
	}

	return zap.Dict(
		label,
		zap.Any("Response", in.GetResponse()),
	)
}

func file_inner_processing_rest_alatau_city_payin_proto_message_AlatauCityProcessPayInRequestToGo(
	label string,
	in *AlatauCityProcessPayInRequest,
) any {
	if in == nil {
		return map[string]any{label: in}
	}

	return map[string]any{
		label: map[string]any{
			"Body": file_inner_processing_rest_alatau_city_payin_proto_message_AlatauCityPayInRequestToGo("Body", in.GetBody()),
		},
	}
}

func file_inner_processing_rest_alatau_city_payin_proto_message_AlatauCityProcessPayInRequestToZap(
	label string,
	in *AlatauCityProcessPayInRequest,
) zap.Field {
	if in == nil {
		return zap.Reflect(label, in)
	}

	return zap.Dict(
		label,
		file_inner_processing_rest_alatau_city_payin_proto_message_AlatauCityPayInRequestToZap("Body", in.GetBody()),
	)
}

func file_inner_processing_rest_alatau_city_payin_proto_message_AlatauCityProcessPayInResponseToGo(
	label string,
	in *AlatauCityProcessPayInResponse,
) any {
	if in == nil {
		return map[string]any{label: in}
	}

	return map[string]any{
		label: map[string]any{
			"Response": in.GetResponse(),
		},
	}
}

func file_inner_processing_rest_alatau_city_payin_proto_message_AlatauCityProcessPayInResponseToZap(
	label string,
	in *AlatauCityProcessPayInResponse,
) zap.Field {
	if in == nil {
		return zap.Reflect(label, in)
	}

	return zap.Dict(
		label,
		zap.Any("Response", in.GetResponse()),
	)
}

func file_inner_processing_rest_alatau_city_payin_proto_message_AlatauCityRegisterTokenRequestToGo(
	label string,
	in *AlatauCityRegisterTokenRequest,
) any {
	if in == nil {
		return map[string]any{label: in}
	}

	return map[string]any{
		label: map[string]any{
			"Body": file_inner_processing_rest_alatau_city_payin_proto_message_AlatauCityRegisterTokenRequest_RegisterTokenRequestToGo("Body", in.GetBody()),
		},
	}
}

func file_inner_processing_rest_alatau_city_payin_proto_message_AlatauCityRegisterTokenRequestToZap(
	label string,
	in *AlatauCityRegisterTokenRequest,
) zap.Field {
	if in == nil {
		return zap.Reflect(label, in)
	}

	return zap.Dict(
		label,
		file_inner_processing_rest_alatau_city_payin_proto_message_AlatauCityRegisterTokenRequest_RegisterTokenRequestToZap("Body", in.GetBody()),
	)
}

func file_inner_processing_rest_alatau_city_payin_proto_message_AlatauCityRegisterTokenRequest_RegisterTokenRequestToGo(
	label string,
	in *AlatauCityRegisterTokenRequest_RegisterTokenRequest,
) any {
	if in == nil {
		return map[string]any{label: in}
	}

	return map[string]any{
		label: map[string]any{
			"ORDER":     in.GetORDER(),
			"MERCHANT":  in.GetMERCHANT(),
			"TERMINAL":  in.GetTERMINAL(),
			"CLIENT_ID": in.GetCLIENT_ID(),
			"CrdPan":    "[***hidden***]",
			"CrdExp":    "[***hidden***]",
			"CrdCvc":    "[***hidden***]",
			"NAME":      in.GetNAME(),
			"P_SIGN":    in.GetP_SIGN(),
			"TOKEN_CMD": in.GetTOKEN_CMD(),
		},
	}
}

func file_inner_processing_rest_alatau_city_payin_proto_message_AlatauCityRegisterTokenRequest_RegisterTokenRequestToZap(
	label string,
	in *AlatauCityRegisterTokenRequest_RegisterTokenRequest,
) zap.Field {
	if in == nil {
		return zap.Reflect(label, in)
	}

	return zap.Dict(
		label,
		zap.Any("ORDER", in.GetORDER()),
		zap.Any("MERCHANT", in.GetMERCHANT()),
		zap.Any("TERMINAL", in.GetTERMINAL()),
		zap.Any("CLIENT_ID", in.GetCLIENT_ID()),
		zap.Any("CrdPan", "[***hidden***]"),
		zap.Any("CrdExp", "[***hidden***]"),
		zap.Any("CrdCvc", "[***hidden***]"),
		zap.Any("NAME", in.GetNAME()),
		zap.Any("P_SIGN", in.GetP_SIGN()),
		zap.Any("TOKEN_CMD", in.GetTOKEN_CMD()),
	)
}

func file_inner_processing_rest_alatau_city_payin_proto_message_AlatauCityRegisterTokenResponseToGo(
	label string,
	in *AlatauCityRegisterTokenResponse,
) any {
	if in == nil {
		return map[string]any{label: in}
	}

	return map[string]any{
		label: map[string]any{
			"TokenResponse": file_inner_processing_rest_alatau_city_payin_proto_message_AlatauCityRegisterTokenResponse_RegisterTokenResponseToGo("TokenResponse", in.GetTokenResponse()),
		},
	}
}

func file_inner_processing_rest_alatau_city_payin_proto_message_AlatauCityRegisterTokenResponseToZap(
	label string,
	in *AlatauCityRegisterTokenResponse,
) zap.Field {
	if in == nil {
		return zap.Reflect(label, in)
	}

	return zap.Dict(
		label,
		file_inner_processing_rest_alatau_city_payin_proto_message_AlatauCityRegisterTokenResponse_RegisterTokenResponseToZap("TokenResponse", in.GetTokenResponse()),
	)
}

func file_inner_processing_rest_alatau_city_payin_proto_message_AlatauCityRegisterTokenResponse_RegisterTokenResponseToGo(
	label string,
	in *AlatauCityRegisterTokenResponse_RegisterTokenResponse,
) any {
	if in == nil {
		return map[string]any{label: in}
	}

	return map[string]any{
		label: map[string]any{
			"Result": file_inner_processing_rest_alatau_city_payin_proto_message_AlatauCityRegisterTokenResponse_ResultToGo("Result", in.GetResult()),
		},
	}
}

func file_inner_processing_rest_alatau_city_payin_proto_message_AlatauCityRegisterTokenResponse_RegisterTokenResponseToZap(
	label string,
	in *AlatauCityRegisterTokenResponse_RegisterTokenResponse,
) zap.Field {
	if in == nil {
		return zap.Reflect(label, in)
	}

	return zap.Dict(
		label,
		file_inner_processing_rest_alatau_city_payin_proto_message_AlatauCityRegisterTokenResponse_ResultToZap("Result", in.GetResult()),
	)
}

func file_inner_processing_rest_alatau_city_payin_proto_message_AlatauCityRegisterTokenResponse_ResultToGo(
	label string,
	in *AlatauCityRegisterTokenResponse_Result,
) any {
	if in == nil {
		return map[string]any{label: in}
	}

	return map[string]any{
		label: map[string]any{
			"Code":        in.GetCode(),
			"Description": in.GetDescription(),
			"Token":       in.GetToken(),
		},
	}
}

func file_inner_processing_rest_alatau_city_payin_proto_message_AlatauCityRegisterTokenResponse_ResultToZap(
	label string,
	in *AlatauCityRegisterTokenResponse_Result,
) zap.Field {
	if in == nil {
		return zap.Reflect(label, in)
	}

	return zap.Dict(
		label,
		zap.Any("Code", in.GetCode()),
		zap.Any("Description", in.GetDescription()),
		zap.Any("Token", in.GetToken()),
	)
}

func file_inner_processing_rest_alatau_city_payin_proto_message_AlatauCityResumeThreeDSRequestToGo(
	label string,
	in *AlatauCityResumeThreeDSRequest,
) any {
	if in == nil {
		return map[string]any{label: in}
	}

	return map[string]any{
		label: map[string]any{
			"Body": file_inner_processing_rest_alatau_city_payin_proto_message_AlatauCityResumeThreeDSRequest_ResumeThreeDSRequestToGo("Body", in.GetBody()),
		},
	}
}

func file_inner_processing_rest_alatau_city_payin_proto_message_AlatauCityResumeThreeDSRequestToZap(
	label string,
	in *AlatauCityResumeThreeDSRequest,
) zap.Field {
	if in == nil {
		return zap.Reflect(label, in)
	}

	return zap.Dict(
		label,
		file_inner_processing_rest_alatau_city_payin_proto_message_AlatauCityResumeThreeDSRequest_ResumeThreeDSRequestToZap("Body", in.GetBody()),
	)
}

func file_inner_processing_rest_alatau_city_payin_proto_message_AlatauCityResumeThreeDSRequest_ResumeThreeDSRequestToGo(
	label string,
	in *AlatauCityResumeThreeDSRequest_ResumeThreeDSRequest,
) any {
	if in == nil {
		return map[string]any{label: in}
	}

	return map[string]any{
		label: map[string]any{
			"ThreeDSMethodData":  in.GetThreeDSMethodData(),
			"ThreeDSMethodState": in.GetThreeDSMethodState(),
		},
	}
}

func file_inner_processing_rest_alatau_city_payin_proto_message_AlatauCityResumeThreeDSRequest_ResumeThreeDSRequestToZap(
	label string,
	in *AlatauCityResumeThreeDSRequest_ResumeThreeDSRequest,
) zap.Field {
	if in == nil {
		return zap.Reflect(label, in)
	}

	return zap.Dict(
		label,
		zap.Any("ThreeDSMethodData", in.GetThreeDSMethodData()),
		zap.Any("ThreeDSMethodState", in.GetThreeDSMethodState()),
	)
}

func file_inner_processing_rest_alatau_city_payin_proto_message_AlatauCityResumeThreeDSResponseToGo(
	label string,
	in *AlatauCityResumeThreeDSResponse,
) any {
	if in == nil {
		return map[string]any{label: in}
	}

	return map[string]any{
		label: map[string]any{
			"Response": in.GetResponse(),
		},
	}
}

func file_inner_processing_rest_alatau_city_payin_proto_message_AlatauCityResumeThreeDSResponseToZap(
	label string,
	in *AlatauCityResumeThreeDSResponse,
) zap.Field {
	if in == nil {
		return zap.Reflect(label, in)
	}

	return zap.Dict(
		label,
		zap.Any("Response", in.GetResponse()),
	)
}

func file_inner_processing_rest_alatau_city_payin_proto_message_AlatauCitySendFormRequestToGo(
	label string,
	in *AlatauCitySendFormRequest,
) any {
	if in == nil {
		return map[string]any{label: in}
	}

	return map[string]any{
		label: map[string]any{
			"Body": file_inner_processing_rest_alatau_city_payin_proto_message_AlatauCitySendFormRequest_SendFormRequestToGo("Body", in.GetBody()),
		},
	}
}

func file_inner_processing_rest_alatau_city_payin_proto_message_AlatauCitySendFormRequestToZap(
	label string,
	in *AlatauCitySendFormRequest,
) zap.Field {
	if in == nil {
		return zap.Reflect(label, in)
	}

	return zap.Dict(
		label,
		file_inner_processing_rest_alatau_city_payin_proto_message_AlatauCitySendFormRequest_SendFormRequestToZap("Body", in.GetBody()),
	)
}

func file_inner_processing_rest_alatau_city_payin_proto_message_AlatauCitySendFormRequest_SendFormRequestToGo(
	label string,
	in *AlatauCitySendFormRequest_SendFormRequest,
) any {
	if in == nil {
		return map[string]any{label: in}
	}

	return map[string]any{
		label: map[string]any{
			"ThreeDSMethodData": in.GetThreeDSMethodData(),
		},
	}
}

func file_inner_processing_rest_alatau_city_payin_proto_message_AlatauCitySendFormRequest_SendFormRequestToZap(
	label string,
	in *AlatauCitySendFormRequest_SendFormRequest,
) zap.Field {
	if in == nil {
		return zap.Reflect(label, in)
	}

	return zap.Dict(
		label,
		zap.Any("ThreeDSMethodData", in.GetThreeDSMethodData()),
	)
}

func file_inner_processing_rest_alatau_city_payin_proto_message_AlatauCitySendFormResponseToGo(
	label string,
	in *AlatauCitySendFormResponse,
) any {
	if in == nil {
		return map[string]any{label: in}
	}

	return map[string]any{
		label: map[string]any{
			"Response": in.GetResponse(),
		},
	}
}

func file_inner_processing_rest_alatau_city_payin_proto_message_AlatauCitySendFormResponseToZap(
	label string,
	in *AlatauCitySendFormResponse,
) zap.Field {
	if in == nil {
		return zap.Reflect(label, in)
	}

	return zap.Dict(
		label,
		zap.Any("Response", in.GetResponse()),
	)
}
