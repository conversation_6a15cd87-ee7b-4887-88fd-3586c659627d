// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.5
// 	protoc        v5.29.3
// source: inner/processing/rest/alatau_city_payin.proto

package rest

import (
	_ "git.local/sensitive/mvp/pb"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type AlatauCityProcessPayInRequest struct {
	state         protoimpl.MessageState  `protogen:"open.v1"`
	Body          *AlatauCityPayInRequest `protobuf:"bytes,1,opt,name=body" json:"body,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *AlatauCityProcessPayInRequest) Reset() {
	*x = AlatauCityProcessPayInRequest{}
	mi := &file_inner_processing_rest_alatau_city_payin_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AlatauCityProcessPayInRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AlatauCityProcessPayInRequest) ProtoMessage() {}

func (x *AlatauCityProcessPayInRequest) ProtoReflect() protoreflect.Message {
	mi := &file_inner_processing_rest_alatau_city_payin_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AlatauCityProcessPayInRequest.ProtoReflect.Descriptor instead.
func (*AlatauCityProcessPayInRequest) Descriptor() ([]byte, []int) {
	return file_inner_processing_rest_alatau_city_payin_proto_rawDescGZIP(), []int{0}
}

func (x *AlatauCityProcessPayInRequest) GetBody() *AlatauCityPayInRequest {
	if x != nil {
		return x.Body
	}
	return nil
}

type AlatauCityPayInRequest struct {
	state                    protoimpl.MessageState `protogen:"open.v1"`
	AMOUNT                   *string                `protobuf:"bytes,1,opt,name=AMOUNT" json:"AMOUNT,omitempty"`
	BACKREF                  *string                `protobuf:"bytes,2,opt,name=BACKREF" json:"BACKREF,omitempty"`
	CLIENT_ID                *string                `protobuf:"bytes,3,opt,name=CLIENT_ID,json=CLIENTID" json:"CLIENT_ID,omitempty"`
	CURRENCY                 *string                `protobuf:"bytes,4,opt,name=CURRENCY" json:"CURRENCY,omitempty"`
	DESC                     *string                `protobuf:"bytes,5,opt,name=DESC" json:"DESC,omitempty"`
	DESC_ORDER               *string                `protobuf:"bytes,6,opt,name=DESC_ORDER,json=DESCORDER" json:"DESC_ORDER,omitempty"`
	EMAIL                    *string                `protobuf:"bytes,7,opt,name=EMAIL" json:"EMAIL,omitempty"`
	EXT_MPI_ECI              *string                `protobuf:"bytes,8,opt,name=EXT_MPI_ECI,json=EXTMPIECI" json:"EXT_MPI_ECI,omitempty"`
	INT_REF                  *string                `protobuf:"bytes,9,opt,name=INT_REF,json=INTREF" json:"INT_REF,omitempty"`
	LANGUAGE                 *string                `protobuf:"bytes,10,opt,name=LANGUAGE" json:"LANGUAGE,omitempty"`
	MERCHANT                 *string                `protobuf:"bytes,11,opt,name=MERCHANT" json:"MERCHANT,omitempty"`
	MERCH_PAYTO_TOKEN_ID     *string                `protobuf:"bytes,12,opt,name=MERCH_PAYTO_TOKEN_ID,json=MERCHPAYTOTOKENID" json:"MERCH_PAYTO_TOKEN_ID,omitempty"`
	MERCH_RN_ID              *string                `protobuf:"bytes,13,opt,name=MERCH_RN_ID,json=MERCHRNID" json:"MERCH_RN_ID,omitempty"`
	MERCH_TOKEN_ID           *string                `protobuf:"bytes,14,opt,name=MERCH_TOKEN_ID,json=MERCHTOKENID" json:"MERCH_TOKEN_ID,omitempty"`
	MERCH_TRAN_STATE         *string                `protobuf:"bytes,15,opt,name=MERCH_TRAN_STATE,json=MERCHTRANSTATE" json:"MERCH_TRAN_STATE,omitempty"`
	MK_TOKEN                 *string                `protobuf:"bytes,16,opt,name=MK_TOKEN,json=MKTOKEN" json:"MK_TOKEN,omitempty"`
	M_INFO                   *string                `protobuf:"bytes,17,opt,name=M_INFO,json=MINFO" json:"M_INFO,omitempty"`
	NAME                     *string                `protobuf:"bytes,18,opt,name=NAME" json:"NAME,omitempty"`
	NONCE                    *string                `protobuf:"bytes,19,opt,name=NONCE" json:"NONCE,omitempty"`
	ORDER                    *string                `protobuf:"bytes,20,opt,name=ORDER" json:"ORDER,omitempty"`
	PAYMENT_TO               *string                `protobuf:"bytes,21,opt,name=PAYMENT_TO,json=PAYMENTTO" json:"PAYMENT_TO,omitempty"`
	P_SIGN                   *string                `protobuf:"bytes,22,opt,name=P_SIGN,json=PSIGN" json:"P_SIGN,omitempty"`
	RECUR_EXP                *string                `protobuf:"bytes,23,opt,name=RECUR_EXP,json=RECUREXP" json:"RECUR_EXP,omitempty"`
	RECUR_FREQ               *string                `protobuf:"bytes,24,opt,name=RECUR_FREQ,json=RECURFREQ" json:"RECUR_FREQ,omitempty"`
	RECUR_REF                *string                `protobuf:"bytes,25,opt,name=RECUR_REF,json=RECURREF" json:"RECUR_REF,omitempty"`
	TAVV                     *string                `protobuf:"bytes,26,opt,name=TAVV" json:"TAVV,omitempty"`
	TERMINAL                 *string                `protobuf:"bytes,27,opt,name=TERMINAL" json:"TERMINAL,omitempty"`
	Ucaf_Authentication_Data *string                `protobuf:"bytes,28,opt,name=Ucaf_Authentication_Data,json=UcafAuthenticationData" json:"Ucaf_Authentication_Data,omitempty"`
	Ucaf_Flag                *string                `protobuf:"bytes,29,opt,name=Ucaf_Flag,json=UcafFlag" json:"Ucaf_Flag,omitempty"`
	WTYPE                    *string                `protobuf:"bytes,30,opt,name=WTYPE" json:"WTYPE,omitempty"`
	CrdCvc                   *string                `protobuf:"bytes,31,opt,name=crd_cvc,json=crdCvc" json:"crd_cvc,omitempty"`
	CrdExp                   *string                `protobuf:"bytes,32,opt,name=crd_exp,json=crdExp" json:"crd_exp,omitempty"`
	CrdPan                   *string                `protobuf:"bytes,33,opt,name=crd_pan,json=crdPan" json:"crd_pan,omitempty"`
	REF                      *string                `protobuf:"bytes,34,opt,name=REF" json:"REF,omitempty"`
	MERCH_3D_TERM_URL        *string                `protobuf:"bytes,35,opt,name=MERCH_3D_TERM_URL,json=MERCH3DTERMURL" json:"MERCH_3D_TERM_URL,omitempty"`
	MERCH_SCA                *string                `protobuf:"bytes,36,opt,name=MERCH_SCA,json=MERCHSCA" json:"MERCH_SCA,omitempty"`
	unknownFields            protoimpl.UnknownFields
	sizeCache                protoimpl.SizeCache
}

func (x *AlatauCityPayInRequest) Reset() {
	*x = AlatauCityPayInRequest{}
	mi := &file_inner_processing_rest_alatau_city_payin_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AlatauCityPayInRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AlatauCityPayInRequest) ProtoMessage() {}

func (x *AlatauCityPayInRequest) ProtoReflect() protoreflect.Message {
	mi := &file_inner_processing_rest_alatau_city_payin_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AlatauCityPayInRequest.ProtoReflect.Descriptor instead.
func (*AlatauCityPayInRequest) Descriptor() ([]byte, []int) {
	return file_inner_processing_rest_alatau_city_payin_proto_rawDescGZIP(), []int{1}
}

func (x *AlatauCityPayInRequest) GetAMOUNT() string {
	if x != nil && x.AMOUNT != nil {
		return *x.AMOUNT
	}
	return ""
}

func (x *AlatauCityPayInRequest) GetBACKREF() string {
	if x != nil && x.BACKREF != nil {
		return *x.BACKREF
	}
	return ""
}

func (x *AlatauCityPayInRequest) GetCLIENT_ID() string {
	if x != nil && x.CLIENT_ID != nil {
		return *x.CLIENT_ID
	}
	return ""
}

func (x *AlatauCityPayInRequest) GetCURRENCY() string {
	if x != nil && x.CURRENCY != nil {
		return *x.CURRENCY
	}
	return ""
}

func (x *AlatauCityPayInRequest) GetDESC() string {
	if x != nil && x.DESC != nil {
		return *x.DESC
	}
	return ""
}

func (x *AlatauCityPayInRequest) GetDESC_ORDER() string {
	if x != nil && x.DESC_ORDER != nil {
		return *x.DESC_ORDER
	}
	return ""
}

func (x *AlatauCityPayInRequest) GetEMAIL() string {
	if x != nil && x.EMAIL != nil {
		return *x.EMAIL
	}
	return ""
}

func (x *AlatauCityPayInRequest) GetEXT_MPI_ECI() string {
	if x != nil && x.EXT_MPI_ECI != nil {
		return *x.EXT_MPI_ECI
	}
	return ""
}

func (x *AlatauCityPayInRequest) GetINT_REF() string {
	if x != nil && x.INT_REF != nil {
		return *x.INT_REF
	}
	return ""
}

func (x *AlatauCityPayInRequest) GetLANGUAGE() string {
	if x != nil && x.LANGUAGE != nil {
		return *x.LANGUAGE
	}
	return ""
}

func (x *AlatauCityPayInRequest) GetMERCHANT() string {
	if x != nil && x.MERCHANT != nil {
		return *x.MERCHANT
	}
	return ""
}

func (x *AlatauCityPayInRequest) GetMERCH_PAYTO_TOKEN_ID() string {
	if x != nil && x.MERCH_PAYTO_TOKEN_ID != nil {
		return *x.MERCH_PAYTO_TOKEN_ID
	}
	return ""
}

func (x *AlatauCityPayInRequest) GetMERCH_RN_ID() string {
	if x != nil && x.MERCH_RN_ID != nil {
		return *x.MERCH_RN_ID
	}
	return ""
}

func (x *AlatauCityPayInRequest) GetMERCH_TOKEN_ID() string {
	if x != nil && x.MERCH_TOKEN_ID != nil {
		return *x.MERCH_TOKEN_ID
	}
	return ""
}

func (x *AlatauCityPayInRequest) GetMERCH_TRAN_STATE() string {
	if x != nil && x.MERCH_TRAN_STATE != nil {
		return *x.MERCH_TRAN_STATE
	}
	return ""
}

func (x *AlatauCityPayInRequest) GetMK_TOKEN() string {
	if x != nil && x.MK_TOKEN != nil {
		return *x.MK_TOKEN
	}
	return ""
}

func (x *AlatauCityPayInRequest) GetM_INFO() string {
	if x != nil && x.M_INFO != nil {
		return *x.M_INFO
	}
	return ""
}

func (x *AlatauCityPayInRequest) GetNAME() string {
	if x != nil && x.NAME != nil {
		return *x.NAME
	}
	return ""
}

func (x *AlatauCityPayInRequest) GetNONCE() string {
	if x != nil && x.NONCE != nil {
		return *x.NONCE
	}
	return ""
}

func (x *AlatauCityPayInRequest) GetORDER() string {
	if x != nil && x.ORDER != nil {
		return *x.ORDER
	}
	return ""
}

func (x *AlatauCityPayInRequest) GetPAYMENT_TO() string {
	if x != nil && x.PAYMENT_TO != nil {
		return *x.PAYMENT_TO
	}
	return ""
}

func (x *AlatauCityPayInRequest) GetP_SIGN() string {
	if x != nil && x.P_SIGN != nil {
		return *x.P_SIGN
	}
	return ""
}

func (x *AlatauCityPayInRequest) GetRECUR_EXP() string {
	if x != nil && x.RECUR_EXP != nil {
		return *x.RECUR_EXP
	}
	return ""
}

func (x *AlatauCityPayInRequest) GetRECUR_FREQ() string {
	if x != nil && x.RECUR_FREQ != nil {
		return *x.RECUR_FREQ
	}
	return ""
}

func (x *AlatauCityPayInRequest) GetRECUR_REF() string {
	if x != nil && x.RECUR_REF != nil {
		return *x.RECUR_REF
	}
	return ""
}

func (x *AlatauCityPayInRequest) GetTAVV() string {
	if x != nil && x.TAVV != nil {
		return *x.TAVV
	}
	return ""
}

func (x *AlatauCityPayInRequest) GetTERMINAL() string {
	if x != nil && x.TERMINAL != nil {
		return *x.TERMINAL
	}
	return ""
}

func (x *AlatauCityPayInRequest) GetUcaf_Authentication_Data() string {
	if x != nil && x.Ucaf_Authentication_Data != nil {
		return *x.Ucaf_Authentication_Data
	}
	return ""
}

func (x *AlatauCityPayInRequest) GetUcaf_Flag() string {
	if x != nil && x.Ucaf_Flag != nil {
		return *x.Ucaf_Flag
	}
	return ""
}

func (x *AlatauCityPayInRequest) GetWTYPE() string {
	if x != nil && x.WTYPE != nil {
		return *x.WTYPE
	}
	return ""
}

func (x *AlatauCityPayInRequest) GetCrdCvc() string {
	if x != nil && x.CrdCvc != nil {
		return *x.CrdCvc
	}
	return ""
}

func (x *AlatauCityPayInRequest) GetCrdExp() string {
	if x != nil && x.CrdExp != nil {
		return *x.CrdExp
	}
	return ""
}

func (x *AlatauCityPayInRequest) GetCrdPan() string {
	if x != nil && x.CrdPan != nil {
		return *x.CrdPan
	}
	return ""
}

func (x *AlatauCityPayInRequest) GetREF() string {
	if x != nil && x.REF != nil {
		return *x.REF
	}
	return ""
}

func (x *AlatauCityPayInRequest) GetMERCH_3D_TERM_URL() string {
	if x != nil && x.MERCH_3D_TERM_URL != nil {
		return *x.MERCH_3D_TERM_URL
	}
	return ""
}

func (x *AlatauCityPayInRequest) GetMERCH_SCA() string {
	if x != nil && x.MERCH_SCA != nil {
		return *x.MERCH_SCA
	}
	return ""
}

type AlatauCityProcessPayInResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Response      *string                `protobuf:"bytes,1,opt,name=response" json:"response,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *AlatauCityProcessPayInResponse) Reset() {
	*x = AlatauCityProcessPayInResponse{}
	mi := &file_inner_processing_rest_alatau_city_payin_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AlatauCityProcessPayInResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AlatauCityProcessPayInResponse) ProtoMessage() {}

func (x *AlatauCityProcessPayInResponse) ProtoReflect() protoreflect.Message {
	mi := &file_inner_processing_rest_alatau_city_payin_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AlatauCityProcessPayInResponse.ProtoReflect.Descriptor instead.
func (*AlatauCityProcessPayInResponse) Descriptor() ([]byte, []int) {
	return file_inner_processing_rest_alatau_city_payin_proto_rawDescGZIP(), []int{2}
}

func (x *AlatauCityProcessPayInResponse) GetResponse() string {
	if x != nil && x.Response != nil {
		return *x.Response
	}
	return ""
}

type AlatauCityGetStatusPayInRequest struct {
	state         protoimpl.MessageState                            `protogen:"open.v1"`
	Body          *AlatauCityGetStatusPayInRequest_GetStatusRequest `protobuf:"bytes,1,opt,name=body" json:"body,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *AlatauCityGetStatusPayInRequest) Reset() {
	*x = AlatauCityGetStatusPayInRequest{}
	mi := &file_inner_processing_rest_alatau_city_payin_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AlatauCityGetStatusPayInRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AlatauCityGetStatusPayInRequest) ProtoMessage() {}

func (x *AlatauCityGetStatusPayInRequest) ProtoReflect() protoreflect.Message {
	mi := &file_inner_processing_rest_alatau_city_payin_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AlatauCityGetStatusPayInRequest.ProtoReflect.Descriptor instead.
func (*AlatauCityGetStatusPayInRequest) Descriptor() ([]byte, []int) {
	return file_inner_processing_rest_alatau_city_payin_proto_rawDescGZIP(), []int{3}
}

func (x *AlatauCityGetStatusPayInRequest) GetBody() *AlatauCityGetStatusPayInRequest_GetStatusRequest {
	if x != nil {
		return x.Body
	}
	return nil
}

type AlatauCityGetStatusPayInResponse struct {
	state          protoimpl.MessageState                           `protogen:"open.v1"`
	StatusResponse *AlatauCityGetStatusPayInResponse_StatusResponse `protobuf:"bytes,1,opt,name=status_response,json=statusResponse" json:"status_response,omitempty"`
	unknownFields  protoimpl.UnknownFields
	sizeCache      protoimpl.SizeCache
}

func (x *AlatauCityGetStatusPayInResponse) Reset() {
	*x = AlatauCityGetStatusPayInResponse{}
	mi := &file_inner_processing_rest_alatau_city_payin_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AlatauCityGetStatusPayInResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AlatauCityGetStatusPayInResponse) ProtoMessage() {}

func (x *AlatauCityGetStatusPayInResponse) ProtoReflect() protoreflect.Message {
	mi := &file_inner_processing_rest_alatau_city_payin_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AlatauCityGetStatusPayInResponse.ProtoReflect.Descriptor instead.
func (*AlatauCityGetStatusPayInResponse) Descriptor() ([]byte, []int) {
	return file_inner_processing_rest_alatau_city_payin_proto_rawDescGZIP(), []int{4}
}

func (x *AlatauCityGetStatusPayInResponse) GetStatusResponse() *AlatauCityGetStatusPayInResponse_StatusResponse {
	if x != nil {
		return x.StatusResponse
	}
	return nil
}

type AlatauCityProcessNoAcceptPayRequest struct {
	state         protoimpl.MessageState  `protogen:"open.v1"`
	Body          *AlatauCityPayInRequest `protobuf:"bytes,1,opt,name=body" json:"body,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *AlatauCityProcessNoAcceptPayRequest) Reset() {
	*x = AlatauCityProcessNoAcceptPayRequest{}
	mi := &file_inner_processing_rest_alatau_city_payin_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AlatauCityProcessNoAcceptPayRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AlatauCityProcessNoAcceptPayRequest) ProtoMessage() {}

func (x *AlatauCityProcessNoAcceptPayRequest) ProtoReflect() protoreflect.Message {
	mi := &file_inner_processing_rest_alatau_city_payin_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AlatauCityProcessNoAcceptPayRequest.ProtoReflect.Descriptor instead.
func (*AlatauCityProcessNoAcceptPayRequest) Descriptor() ([]byte, []int) {
	return file_inner_processing_rest_alatau_city_payin_proto_rawDescGZIP(), []int{5}
}

func (x *AlatauCityProcessNoAcceptPayRequest) GetBody() *AlatauCityPayInRequest {
	if x != nil {
		return x.Body
	}
	return nil
}

type AlatauCityProcessNoAcceptPayResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Response      *string                `protobuf:"bytes,1,opt,name=response" json:"response,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *AlatauCityProcessNoAcceptPayResponse) Reset() {
	*x = AlatauCityProcessNoAcceptPayResponse{}
	mi := &file_inner_processing_rest_alatau_city_payin_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AlatauCityProcessNoAcceptPayResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AlatauCityProcessNoAcceptPayResponse) ProtoMessage() {}

func (x *AlatauCityProcessNoAcceptPayResponse) ProtoReflect() protoreflect.Message {
	mi := &file_inner_processing_rest_alatau_city_payin_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AlatauCityProcessNoAcceptPayResponse.ProtoReflect.Descriptor instead.
func (*AlatauCityProcessNoAcceptPayResponse) Descriptor() ([]byte, []int) {
	return file_inner_processing_rest_alatau_city_payin_proto_rawDescGZIP(), []int{6}
}

func (x *AlatauCityProcessNoAcceptPayResponse) GetResponse() string {
	if x != nil && x.Response != nil {
		return *x.Response
	}
	return ""
}

type AlatauCityConfirmThreeDSRequest struct {
	state         protoimpl.MessageState                                 `protogen:"open.v1"`
	Body          *AlatauCityConfirmThreeDSRequest_ConfirmThreeDSRequest `protobuf:"bytes,1,opt,name=body" json:"body,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *AlatauCityConfirmThreeDSRequest) Reset() {
	*x = AlatauCityConfirmThreeDSRequest{}
	mi := &file_inner_processing_rest_alatau_city_payin_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AlatauCityConfirmThreeDSRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AlatauCityConfirmThreeDSRequest) ProtoMessage() {}

func (x *AlatauCityConfirmThreeDSRequest) ProtoReflect() protoreflect.Message {
	mi := &file_inner_processing_rest_alatau_city_payin_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AlatauCityConfirmThreeDSRequest.ProtoReflect.Descriptor instead.
func (*AlatauCityConfirmThreeDSRequest) Descriptor() ([]byte, []int) {
	return file_inner_processing_rest_alatau_city_payin_proto_rawDescGZIP(), []int{7}
}

func (x *AlatauCityConfirmThreeDSRequest) GetBody() *AlatauCityConfirmThreeDSRequest_ConfirmThreeDSRequest {
	if x != nil {
		return x.Body
	}
	return nil
}

type AlatauCityConfirmThreeDSResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Response      *string                `protobuf:"bytes,1,opt,name=response" json:"response,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *AlatauCityConfirmThreeDSResponse) Reset() {
	*x = AlatauCityConfirmThreeDSResponse{}
	mi := &file_inner_processing_rest_alatau_city_payin_proto_msgTypes[8]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AlatauCityConfirmThreeDSResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AlatauCityConfirmThreeDSResponse) ProtoMessage() {}

func (x *AlatauCityConfirmThreeDSResponse) ProtoReflect() protoreflect.Message {
	mi := &file_inner_processing_rest_alatau_city_payin_proto_msgTypes[8]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AlatauCityConfirmThreeDSResponse.ProtoReflect.Descriptor instead.
func (*AlatauCityConfirmThreeDSResponse) Descriptor() ([]byte, []int) {
	return file_inner_processing_rest_alatau_city_payin_proto_rawDescGZIP(), []int{8}
}

func (x *AlatauCityConfirmThreeDSResponse) GetResponse() string {
	if x != nil && x.Response != nil {
		return *x.Response
	}
	return ""
}

type AlatauCityResumeThreeDSRequest struct {
	state         protoimpl.MessageState                               `protogen:"open.v1"`
	Body          *AlatauCityResumeThreeDSRequest_ResumeThreeDSRequest `protobuf:"bytes,1,opt,name=body" json:"body,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *AlatauCityResumeThreeDSRequest) Reset() {
	*x = AlatauCityResumeThreeDSRequest{}
	mi := &file_inner_processing_rest_alatau_city_payin_proto_msgTypes[9]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AlatauCityResumeThreeDSRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AlatauCityResumeThreeDSRequest) ProtoMessage() {}

func (x *AlatauCityResumeThreeDSRequest) ProtoReflect() protoreflect.Message {
	mi := &file_inner_processing_rest_alatau_city_payin_proto_msgTypes[9]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AlatauCityResumeThreeDSRequest.ProtoReflect.Descriptor instead.
func (*AlatauCityResumeThreeDSRequest) Descriptor() ([]byte, []int) {
	return file_inner_processing_rest_alatau_city_payin_proto_rawDescGZIP(), []int{9}
}

func (x *AlatauCityResumeThreeDSRequest) GetBody() *AlatauCityResumeThreeDSRequest_ResumeThreeDSRequest {
	if x != nil {
		return x.Body
	}
	return nil
}

type AlatauCityResumeThreeDSResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Response      *string                `protobuf:"bytes,1,opt,name=response" json:"response,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *AlatauCityResumeThreeDSResponse) Reset() {
	*x = AlatauCityResumeThreeDSResponse{}
	mi := &file_inner_processing_rest_alatau_city_payin_proto_msgTypes[10]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AlatauCityResumeThreeDSResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AlatauCityResumeThreeDSResponse) ProtoMessage() {}

func (x *AlatauCityResumeThreeDSResponse) ProtoReflect() protoreflect.Message {
	mi := &file_inner_processing_rest_alatau_city_payin_proto_msgTypes[10]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AlatauCityResumeThreeDSResponse.ProtoReflect.Descriptor instead.
func (*AlatauCityResumeThreeDSResponse) Descriptor() ([]byte, []int) {
	return file_inner_processing_rest_alatau_city_payin_proto_rawDescGZIP(), []int{10}
}

func (x *AlatauCityResumeThreeDSResponse) GetResponse() string {
	if x != nil && x.Response != nil {
		return *x.Response
	}
	return ""
}

type AlatauCitySendFormRequest struct {
	state         protoimpl.MessageState                     `protogen:"open.v1"`
	Body          *AlatauCitySendFormRequest_SendFormRequest `protobuf:"bytes,1,opt,name=body" json:"body,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *AlatauCitySendFormRequest) Reset() {
	*x = AlatauCitySendFormRequest{}
	mi := &file_inner_processing_rest_alatau_city_payin_proto_msgTypes[11]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AlatauCitySendFormRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AlatauCitySendFormRequest) ProtoMessage() {}

func (x *AlatauCitySendFormRequest) ProtoReflect() protoreflect.Message {
	mi := &file_inner_processing_rest_alatau_city_payin_proto_msgTypes[11]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AlatauCitySendFormRequest.ProtoReflect.Descriptor instead.
func (*AlatauCitySendFormRequest) Descriptor() ([]byte, []int) {
	return file_inner_processing_rest_alatau_city_payin_proto_rawDescGZIP(), []int{11}
}

func (x *AlatauCitySendFormRequest) GetBody() *AlatauCitySendFormRequest_SendFormRequest {
	if x != nil {
		return x.Body
	}
	return nil
}

type AlatauCitySendFormResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Response      *string                `protobuf:"bytes,1,opt,name=response" json:"response,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *AlatauCitySendFormResponse) Reset() {
	*x = AlatauCitySendFormResponse{}
	mi := &file_inner_processing_rest_alatau_city_payin_proto_msgTypes[12]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AlatauCitySendFormResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AlatauCitySendFormResponse) ProtoMessage() {}

func (x *AlatauCitySendFormResponse) ProtoReflect() protoreflect.Message {
	mi := &file_inner_processing_rest_alatau_city_payin_proto_msgTypes[12]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AlatauCitySendFormResponse.ProtoReflect.Descriptor instead.
func (*AlatauCitySendFormResponse) Descriptor() ([]byte, []int) {
	return file_inner_processing_rest_alatau_city_payin_proto_rawDescGZIP(), []int{12}
}

func (x *AlatauCitySendFormResponse) GetResponse() string {
	if x != nil && x.Response != nil {
		return *x.Response
	}
	return ""
}

type AlatauCityCancelPaymentRequest struct {
	state         protoimpl.MessageState                               `protogen:"open.v1"`
	Body          *AlatauCityCancelPaymentRequest_CancelPaymentRequest `protobuf:"bytes,1,opt,name=body" json:"body,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *AlatauCityCancelPaymentRequest) Reset() {
	*x = AlatauCityCancelPaymentRequest{}
	mi := &file_inner_processing_rest_alatau_city_payin_proto_msgTypes[13]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AlatauCityCancelPaymentRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AlatauCityCancelPaymentRequest) ProtoMessage() {}

func (x *AlatauCityCancelPaymentRequest) ProtoReflect() protoreflect.Message {
	mi := &file_inner_processing_rest_alatau_city_payin_proto_msgTypes[13]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AlatauCityCancelPaymentRequest.ProtoReflect.Descriptor instead.
func (*AlatauCityCancelPaymentRequest) Descriptor() ([]byte, []int) {
	return file_inner_processing_rest_alatau_city_payin_proto_rawDescGZIP(), []int{13}
}

func (x *AlatauCityCancelPaymentRequest) GetBody() *AlatauCityCancelPaymentRequest_CancelPaymentRequest {
	if x != nil {
		return x.Body
	}
	return nil
}

type AlatauCityCancelPaymentResponse struct {
	state          protoimpl.MessageState                                 `protogen:"open.v1"`
	CancelResponse *AlatauCityCancelPaymentResponse_CancelPaymentResponse `protobuf:"bytes,1,opt,name=cancel_response,json=cancelResponse" json:"cancel_response,omitempty"`
	unknownFields  protoimpl.UnknownFields
	sizeCache      protoimpl.SizeCache
}

func (x *AlatauCityCancelPaymentResponse) Reset() {
	*x = AlatauCityCancelPaymentResponse{}
	mi := &file_inner_processing_rest_alatau_city_payin_proto_msgTypes[14]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AlatauCityCancelPaymentResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AlatauCityCancelPaymentResponse) ProtoMessage() {}

func (x *AlatauCityCancelPaymentResponse) ProtoReflect() protoreflect.Message {
	mi := &file_inner_processing_rest_alatau_city_payin_proto_msgTypes[14]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AlatauCityCancelPaymentResponse.ProtoReflect.Descriptor instead.
func (*AlatauCityCancelPaymentResponse) Descriptor() ([]byte, []int) {
	return file_inner_processing_rest_alatau_city_payin_proto_rawDescGZIP(), []int{14}
}

func (x *AlatauCityCancelPaymentResponse) GetCancelResponse() *AlatauCityCancelPaymentResponse_CancelPaymentResponse {
	if x != nil {
		return x.CancelResponse
	}
	return nil
}

type AlatauCityRegisterTokenRequest struct {
	state         protoimpl.MessageState                               `protogen:"open.v1"`
	Body          *AlatauCityRegisterTokenRequest_RegisterTokenRequest `protobuf:"bytes,1,opt,name=body" json:"body,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *AlatauCityRegisterTokenRequest) Reset() {
	*x = AlatauCityRegisterTokenRequest{}
	mi := &file_inner_processing_rest_alatau_city_payin_proto_msgTypes[15]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AlatauCityRegisterTokenRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AlatauCityRegisterTokenRequest) ProtoMessage() {}

func (x *AlatauCityRegisterTokenRequest) ProtoReflect() protoreflect.Message {
	mi := &file_inner_processing_rest_alatau_city_payin_proto_msgTypes[15]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AlatauCityRegisterTokenRequest.ProtoReflect.Descriptor instead.
func (*AlatauCityRegisterTokenRequest) Descriptor() ([]byte, []int) {
	return file_inner_processing_rest_alatau_city_payin_proto_rawDescGZIP(), []int{15}
}

func (x *AlatauCityRegisterTokenRequest) GetBody() *AlatauCityRegisterTokenRequest_RegisterTokenRequest {
	if x != nil {
		return x.Body
	}
	return nil
}

type AlatauCityRegisterTokenResponse struct {
	state         protoimpl.MessageState                                 `protogen:"open.v1"`
	TokenResponse *AlatauCityRegisterTokenResponse_RegisterTokenResponse `protobuf:"bytes,1,opt,name=token_response,json=tokenResponse" json:"token_response,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *AlatauCityRegisterTokenResponse) Reset() {
	*x = AlatauCityRegisterTokenResponse{}
	mi := &file_inner_processing_rest_alatau_city_payin_proto_msgTypes[16]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AlatauCityRegisterTokenResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AlatauCityRegisterTokenResponse) ProtoMessage() {}

func (x *AlatauCityRegisterTokenResponse) ProtoReflect() protoreflect.Message {
	mi := &file_inner_processing_rest_alatau_city_payin_proto_msgTypes[16]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AlatauCityRegisterTokenResponse.ProtoReflect.Descriptor instead.
func (*AlatauCityRegisterTokenResponse) Descriptor() ([]byte, []int) {
	return file_inner_processing_rest_alatau_city_payin_proto_rawDescGZIP(), []int{16}
}

func (x *AlatauCityRegisterTokenResponse) GetTokenResponse() *AlatauCityRegisterTokenResponse_RegisterTokenResponse {
	if x != nil {
		return x.TokenResponse
	}
	return nil
}

type AlatauCityChargeRequest struct {
	state         protoimpl.MessageState                 `protogen:"open.v1"`
	Body          *AlatauCityChargeRequest_ChargeRequest `protobuf:"bytes,1,opt,name=body" json:"body,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *AlatauCityChargeRequest) Reset() {
	*x = AlatauCityChargeRequest{}
	mi := &file_inner_processing_rest_alatau_city_payin_proto_msgTypes[17]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AlatauCityChargeRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AlatauCityChargeRequest) ProtoMessage() {}

func (x *AlatauCityChargeRequest) ProtoReflect() protoreflect.Message {
	mi := &file_inner_processing_rest_alatau_city_payin_proto_msgTypes[17]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AlatauCityChargeRequest.ProtoReflect.Descriptor instead.
func (*AlatauCityChargeRequest) Descriptor() ([]byte, []int) {
	return file_inner_processing_rest_alatau_city_payin_proto_rawDescGZIP(), []int{17}
}

func (x *AlatauCityChargeRequest) GetBody() *AlatauCityChargeRequest_ChargeRequest {
	if x != nil {
		return x.Body
	}
	return nil
}

type AlatauCityChargeResponse struct {
	state          protoimpl.MessageState                   `protogen:"open.v1"`
	ChargeResponse *AlatauCityChargeResponse_ChargeResponse `protobuf:"bytes,1,opt,name=charge_response,json=chargeResponse" json:"charge_response,omitempty"`
	unknownFields  protoimpl.UnknownFields
	sizeCache      protoimpl.SizeCache
}

func (x *AlatauCityChargeResponse) Reset() {
	*x = AlatauCityChargeResponse{}
	mi := &file_inner_processing_rest_alatau_city_payin_proto_msgTypes[18]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AlatauCityChargeResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AlatauCityChargeResponse) ProtoMessage() {}

func (x *AlatauCityChargeResponse) ProtoReflect() protoreflect.Message {
	mi := &file_inner_processing_rest_alatau_city_payin_proto_msgTypes[18]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AlatauCityChargeResponse.ProtoReflect.Descriptor instead.
func (*AlatauCityChargeResponse) Descriptor() ([]byte, []int) {
	return file_inner_processing_rest_alatau_city_payin_proto_rawDescGZIP(), []int{18}
}

func (x *AlatauCityChargeResponse) GetChargeResponse() *AlatauCityChargeResponse_ChargeResponse {
	if x != nil {
		return x.ChargeResponse
	}
	return nil
}

type AlatauCityGetStatusPayInRequest_GetStatusRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	ORDER         *string                `protobuf:"bytes,1,opt,name=ORDER" json:"ORDER,omitempty"`
	MERCHANT      *string                `protobuf:"bytes,2,opt,name=MERCHANT" json:"MERCHANT,omitempty"`
	GETSTATUS     *string                `protobuf:"bytes,3,opt,name=GETSTATUS" json:"GETSTATUS,omitempty"`
	P_SIGN        *string                `protobuf:"bytes,4,opt,name=P_SIGN,json=PSIGN" json:"P_SIGN,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *AlatauCityGetStatusPayInRequest_GetStatusRequest) Reset() {
	*x = AlatauCityGetStatusPayInRequest_GetStatusRequest{}
	mi := &file_inner_processing_rest_alatau_city_payin_proto_msgTypes[19]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AlatauCityGetStatusPayInRequest_GetStatusRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AlatauCityGetStatusPayInRequest_GetStatusRequest) ProtoMessage() {}

func (x *AlatauCityGetStatusPayInRequest_GetStatusRequest) ProtoReflect() protoreflect.Message {
	mi := &file_inner_processing_rest_alatau_city_payin_proto_msgTypes[19]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AlatauCityGetStatusPayInRequest_GetStatusRequest.ProtoReflect.Descriptor instead.
func (*AlatauCityGetStatusPayInRequest_GetStatusRequest) Descriptor() ([]byte, []int) {
	return file_inner_processing_rest_alatau_city_payin_proto_rawDescGZIP(), []int{3, 0}
}

func (x *AlatauCityGetStatusPayInRequest_GetStatusRequest) GetORDER() string {
	if x != nil && x.ORDER != nil {
		return *x.ORDER
	}
	return ""
}

func (x *AlatauCityGetStatusPayInRequest_GetStatusRequest) GetMERCHANT() string {
	if x != nil && x.MERCHANT != nil {
		return *x.MERCHANT
	}
	return ""
}

func (x *AlatauCityGetStatusPayInRequest_GetStatusRequest) GetGETSTATUS() string {
	if x != nil && x.GETSTATUS != nil {
		return *x.GETSTATUS
	}
	return ""
}

func (x *AlatauCityGetStatusPayInRequest_GetStatusRequest) GetP_SIGN() string {
	if x != nil && x.P_SIGN != nil {
		return *x.P_SIGN
	}
	return ""
}

type AlatauCityGetStatusPayInResponse_StatusResponse struct {
	state         protoimpl.MessageState                   `protogen:"open.v1"`
	Result        *AlatauCityGetStatusPayInResponse_Result `protobuf:"bytes,1,opt,name=result" json:"result,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *AlatauCityGetStatusPayInResponse_StatusResponse) Reset() {
	*x = AlatauCityGetStatusPayInResponse_StatusResponse{}
	mi := &file_inner_processing_rest_alatau_city_payin_proto_msgTypes[20]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AlatauCityGetStatusPayInResponse_StatusResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AlatauCityGetStatusPayInResponse_StatusResponse) ProtoMessage() {}

func (x *AlatauCityGetStatusPayInResponse_StatusResponse) ProtoReflect() protoreflect.Message {
	mi := &file_inner_processing_rest_alatau_city_payin_proto_msgTypes[20]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AlatauCityGetStatusPayInResponse_StatusResponse.ProtoReflect.Descriptor instead.
func (*AlatauCityGetStatusPayInResponse_StatusResponse) Descriptor() ([]byte, []int) {
	return file_inner_processing_rest_alatau_city_payin_proto_rawDescGZIP(), []int{4, 0}
}

func (x *AlatauCityGetStatusPayInResponse_StatusResponse) GetResult() *AlatauCityGetStatusPayInResponse_Result {
	if x != nil {
		return x.Result
	}
	return nil
}

type AlatauCityGetStatusPayInResponse_Result struct {
	state         protoimpl.MessageState                      `protogen:"open.v1"`
	Code          *string                                     `protobuf:"bytes,1,opt,name=code" json:"code,omitempty"`
	Description   *string                                     `protobuf:"bytes,2,opt,name=description" json:"description,omitempty"`
	Operation     *AlatauCityGetStatusPayInResponse_Operation `protobuf:"bytes,3,opt,name=operation" json:"operation,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *AlatauCityGetStatusPayInResponse_Result) Reset() {
	*x = AlatauCityGetStatusPayInResponse_Result{}
	mi := &file_inner_processing_rest_alatau_city_payin_proto_msgTypes[21]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AlatauCityGetStatusPayInResponse_Result) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AlatauCityGetStatusPayInResponse_Result) ProtoMessage() {}

func (x *AlatauCityGetStatusPayInResponse_Result) ProtoReflect() protoreflect.Message {
	mi := &file_inner_processing_rest_alatau_city_payin_proto_msgTypes[21]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AlatauCityGetStatusPayInResponse_Result.ProtoReflect.Descriptor instead.
func (*AlatauCityGetStatusPayInResponse_Result) Descriptor() ([]byte, []int) {
	return file_inner_processing_rest_alatau_city_payin_proto_rawDescGZIP(), []int{4, 1}
}

func (x *AlatauCityGetStatusPayInResponse_Result) GetCode() string {
	if x != nil && x.Code != nil {
		return *x.Code
	}
	return ""
}

func (x *AlatauCityGetStatusPayInResponse_Result) GetDescription() string {
	if x != nil && x.Description != nil {
		return *x.Description
	}
	return ""
}

func (x *AlatauCityGetStatusPayInResponse_Result) GetOperation() *AlatauCityGetStatusPayInResponse_Operation {
	if x != nil {
		return x.Operation
	}
	return nil
}

type AlatauCityGetStatusPayInResponse_Operation struct {
	state         protoimpl.MessageState                              `protogen:"open.v1"`
	Status        *string                                             `protobuf:"bytes,1,opt,name=status" json:"status,omitempty"`
	StatusDesc    *string                                             `protobuf:"bytes,2,opt,name=status_desc,json=statusDesc" json:"status_desc,omitempty"`
	Amount        *string                                             `protobuf:"bytes,3,opt,name=amount" json:"amount,omitempty"`
	Currency      *string                                             `protobuf:"bytes,4,opt,name=currency" json:"currency,omitempty"`
	Description   *string                                             `protobuf:"bytes,5,opt,name=description" json:"description,omitempty"`
	DescOrder     *string                                             `protobuf:"bytes,6,opt,name=desc_order,json=descOrder" json:"desc_order,omitempty"`
	Email         *string                                             `protobuf:"bytes,7,opt,name=email" json:"email,omitempty"`
	Lang          *string                                             `protobuf:"bytes,8,opt,name=lang" json:"lang,omitempty"`
	MpiOrder      *string                                             `protobuf:"bytes,9,opt,name=mpi_order,json=mpiOrder" json:"mpi_order,omitempty"`
	Terminal      *string                                             `protobuf:"bytes,10,opt,name=terminal" json:"terminal,omitempty"`
	Phone         *string                                             `protobuf:"bytes,11,opt,name=phone" json:"phone,omitempty"`
	CardMasked    *string                                             `protobuf:"bytes,12,opt,name=card_masked,json=cardMasked" json:"card_masked,omitempty"`
	CardName      *string                                             `protobuf:"bytes,13,opt,name=card_name,json=cardName" json:"card_name,omitempty"`
	CardExpdt     *string                                             `protobuf:"bytes,14,opt,name=card_expdt,json=cardExpdt" json:"card_expdt,omitempty"`
	CardToken     *string                                             `protobuf:"bytes,15,opt,name=card_token,json=cardToken" json:"card_token,omitempty"`
	CreateDate    *string                                             `protobuf:"bytes,16,opt,name=create_date,json=createDate" json:"create_date,omitempty"`
	Result        *string                                             `protobuf:"bytes,17,opt,name=result" json:"result,omitempty"`
	ResultDesc    *string                                             `protobuf:"bytes,18,opt,name=result_desc,json=resultDesc" json:"result_desc,omitempty"`
	Rc            *string                                             `protobuf:"bytes,19,opt,name=rc" json:"rc,omitempty"`
	Rrn           *string                                             `protobuf:"bytes,20,opt,name=rrn" json:"rrn,omitempty"`
	AuthCode      *string                                             `protobuf:"bytes,21,opt,name=auth_code,json=authCode" json:"auth_code,omitempty"`
	InvId         *string                                             `protobuf:"bytes,22,opt,name=inv_id,json=invId" json:"inv_id,omitempty"`
	IntExpDate    *string                                             `protobuf:"bytes,23,opt,name=int_exp_date,json=intExpDate" json:"int_exp_date,omitempty"`
	RevMaxAmount  *string                                             `protobuf:"bytes,24,opt,name=rev_max_amount,json=revMaxAmount" json:"rev_max_amount,omitempty"`
	RecurFreq     *string                                             `protobuf:"bytes,25,opt,name=recur_freq,json=recurFreq" json:"recur_freq,omitempty"`
	RecurRef      *string                                             `protobuf:"bytes,26,opt,name=recur_ref,json=recurRef" json:"recur_ref,omitempty"`
	RecurIntRef   *string                                             `protobuf:"bytes,27,opt,name=recur_int_ref,json=recurIntRef" json:"recur_int_ref,omitempty"`
	ClientId      *string                                             `protobuf:"bytes,28,opt,name=client_id,json=clientId" json:"client_id,omitempty"`
	CardToMasked  *string                                             `protobuf:"bytes,29,opt,name=card_to_masked,json=cardToMasked" json:"card_to_masked,omitempty"`
	CardToToken   *string                                             `protobuf:"bytes,30,opt,name=card_to_token,json=cardToToken" json:"card_to_token,omitempty"`
	Refunds       *AlatauCityGetStatusPayInResponse_Operation_Refunds `protobuf:"bytes,31,opt,name=refunds" json:"refunds,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *AlatauCityGetStatusPayInResponse_Operation) Reset() {
	*x = AlatauCityGetStatusPayInResponse_Operation{}
	mi := &file_inner_processing_rest_alatau_city_payin_proto_msgTypes[22]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AlatauCityGetStatusPayInResponse_Operation) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AlatauCityGetStatusPayInResponse_Operation) ProtoMessage() {}

func (x *AlatauCityGetStatusPayInResponse_Operation) ProtoReflect() protoreflect.Message {
	mi := &file_inner_processing_rest_alatau_city_payin_proto_msgTypes[22]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AlatauCityGetStatusPayInResponse_Operation.ProtoReflect.Descriptor instead.
func (*AlatauCityGetStatusPayInResponse_Operation) Descriptor() ([]byte, []int) {
	return file_inner_processing_rest_alatau_city_payin_proto_rawDescGZIP(), []int{4, 2}
}

func (x *AlatauCityGetStatusPayInResponse_Operation) GetStatus() string {
	if x != nil && x.Status != nil {
		return *x.Status
	}
	return ""
}

func (x *AlatauCityGetStatusPayInResponse_Operation) GetStatusDesc() string {
	if x != nil && x.StatusDesc != nil {
		return *x.StatusDesc
	}
	return ""
}

func (x *AlatauCityGetStatusPayInResponse_Operation) GetAmount() string {
	if x != nil && x.Amount != nil {
		return *x.Amount
	}
	return ""
}

func (x *AlatauCityGetStatusPayInResponse_Operation) GetCurrency() string {
	if x != nil && x.Currency != nil {
		return *x.Currency
	}
	return ""
}

func (x *AlatauCityGetStatusPayInResponse_Operation) GetDescription() string {
	if x != nil && x.Description != nil {
		return *x.Description
	}
	return ""
}

func (x *AlatauCityGetStatusPayInResponse_Operation) GetDescOrder() string {
	if x != nil && x.DescOrder != nil {
		return *x.DescOrder
	}
	return ""
}

func (x *AlatauCityGetStatusPayInResponse_Operation) GetEmail() string {
	if x != nil && x.Email != nil {
		return *x.Email
	}
	return ""
}

func (x *AlatauCityGetStatusPayInResponse_Operation) GetLang() string {
	if x != nil && x.Lang != nil {
		return *x.Lang
	}
	return ""
}

func (x *AlatauCityGetStatusPayInResponse_Operation) GetMpiOrder() string {
	if x != nil && x.MpiOrder != nil {
		return *x.MpiOrder
	}
	return ""
}

func (x *AlatauCityGetStatusPayInResponse_Operation) GetTerminal() string {
	if x != nil && x.Terminal != nil {
		return *x.Terminal
	}
	return ""
}

func (x *AlatauCityGetStatusPayInResponse_Operation) GetPhone() string {
	if x != nil && x.Phone != nil {
		return *x.Phone
	}
	return ""
}

func (x *AlatauCityGetStatusPayInResponse_Operation) GetCardMasked() string {
	if x != nil && x.CardMasked != nil {
		return *x.CardMasked
	}
	return ""
}

func (x *AlatauCityGetStatusPayInResponse_Operation) GetCardName() string {
	if x != nil && x.CardName != nil {
		return *x.CardName
	}
	return ""
}

func (x *AlatauCityGetStatusPayInResponse_Operation) GetCardExpdt() string {
	if x != nil && x.CardExpdt != nil {
		return *x.CardExpdt
	}
	return ""
}

func (x *AlatauCityGetStatusPayInResponse_Operation) GetCardToken() string {
	if x != nil && x.CardToken != nil {
		return *x.CardToken
	}
	return ""
}

func (x *AlatauCityGetStatusPayInResponse_Operation) GetCreateDate() string {
	if x != nil && x.CreateDate != nil {
		return *x.CreateDate
	}
	return ""
}

func (x *AlatauCityGetStatusPayInResponse_Operation) GetResult() string {
	if x != nil && x.Result != nil {
		return *x.Result
	}
	return ""
}

func (x *AlatauCityGetStatusPayInResponse_Operation) GetResultDesc() string {
	if x != nil && x.ResultDesc != nil {
		return *x.ResultDesc
	}
	return ""
}

func (x *AlatauCityGetStatusPayInResponse_Operation) GetRc() string {
	if x != nil && x.Rc != nil {
		return *x.Rc
	}
	return ""
}

func (x *AlatauCityGetStatusPayInResponse_Operation) GetRrn() string {
	if x != nil && x.Rrn != nil {
		return *x.Rrn
	}
	return ""
}

func (x *AlatauCityGetStatusPayInResponse_Operation) GetAuthCode() string {
	if x != nil && x.AuthCode != nil {
		return *x.AuthCode
	}
	return ""
}

func (x *AlatauCityGetStatusPayInResponse_Operation) GetInvId() string {
	if x != nil && x.InvId != nil {
		return *x.InvId
	}
	return ""
}

func (x *AlatauCityGetStatusPayInResponse_Operation) GetIntExpDate() string {
	if x != nil && x.IntExpDate != nil {
		return *x.IntExpDate
	}
	return ""
}

func (x *AlatauCityGetStatusPayInResponse_Operation) GetRevMaxAmount() string {
	if x != nil && x.RevMaxAmount != nil {
		return *x.RevMaxAmount
	}
	return ""
}

func (x *AlatauCityGetStatusPayInResponse_Operation) GetRecurFreq() string {
	if x != nil && x.RecurFreq != nil {
		return *x.RecurFreq
	}
	return ""
}

func (x *AlatauCityGetStatusPayInResponse_Operation) GetRecurRef() string {
	if x != nil && x.RecurRef != nil {
		return *x.RecurRef
	}
	return ""
}

func (x *AlatauCityGetStatusPayInResponse_Operation) GetRecurIntRef() string {
	if x != nil && x.RecurIntRef != nil {
		return *x.RecurIntRef
	}
	return ""
}

func (x *AlatauCityGetStatusPayInResponse_Operation) GetClientId() string {
	if x != nil && x.ClientId != nil {
		return *x.ClientId
	}
	return ""
}

func (x *AlatauCityGetStatusPayInResponse_Operation) GetCardToMasked() string {
	if x != nil && x.CardToMasked != nil {
		return *x.CardToMasked
	}
	return ""
}

func (x *AlatauCityGetStatusPayInResponse_Operation) GetCardToToken() string {
	if x != nil && x.CardToToken != nil {
		return *x.CardToToken
	}
	return ""
}

func (x *AlatauCityGetStatusPayInResponse_Operation) GetRefunds() *AlatauCityGetStatusPayInResponse_Operation_Refunds {
	if x != nil {
		return x.Refunds
	}
	return nil
}

type AlatauCityGetStatusPayInResponse_Operation_Refunds struct {
	state         protoimpl.MessageState                            `protogen:"open.v1"`
	Rec           []*AlatauCityGetStatusPayInResponse_Operation_Rec `protobuf:"bytes,1,rep,name=rec" json:"rec,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *AlatauCityGetStatusPayInResponse_Operation_Refunds) Reset() {
	*x = AlatauCityGetStatusPayInResponse_Operation_Refunds{}
	mi := &file_inner_processing_rest_alatau_city_payin_proto_msgTypes[23]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AlatauCityGetStatusPayInResponse_Operation_Refunds) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AlatauCityGetStatusPayInResponse_Operation_Refunds) ProtoMessage() {}

func (x *AlatauCityGetStatusPayInResponse_Operation_Refunds) ProtoReflect() protoreflect.Message {
	mi := &file_inner_processing_rest_alatau_city_payin_proto_msgTypes[23]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AlatauCityGetStatusPayInResponse_Operation_Refunds.ProtoReflect.Descriptor instead.
func (*AlatauCityGetStatusPayInResponse_Operation_Refunds) Descriptor() ([]byte, []int) {
	return file_inner_processing_rest_alatau_city_payin_proto_rawDescGZIP(), []int{4, 2, 0}
}

func (x *AlatauCityGetStatusPayInResponse_Operation_Refunds) GetRec() []*AlatauCityGetStatusPayInResponse_Operation_Rec {
	if x != nil {
		return x.Rec
	}
	return nil
}

type AlatauCityGetStatusPayInResponse_Operation_Rec struct {
	state          protoimpl.MessageState `protogen:"open.v1"`
	Status         *string                `protobuf:"bytes,1,opt,name=status" json:"status,omitempty"`
	StatusDesc     *string                `protobuf:"bytes,2,opt,name=status_desc,json=statusDesc" json:"status_desc,omitempty"`
	RevRc          *string                `protobuf:"bytes,3,opt,name=rev_rc,json=revRc" json:"rev_rc,omitempty"`
	RevAmount      *string                `protobuf:"bytes,4,opt,name=rev_amount,json=revAmount" json:"rev_amount,omitempty"`
	RevDescription *string                `protobuf:"bytes,5,opt,name=rev_description,json=revDescription" json:"rev_description,omitempty"`
	RevError       *string                `protobuf:"bytes,6,opt,name=rev_error,json=revError" json:"rev_error,omitempty"`
	RevDate        *string                `protobuf:"bytes,7,opt,name=rev_date,json=revDate" json:"rev_date,omitempty"`
	unknownFields  protoimpl.UnknownFields
	sizeCache      protoimpl.SizeCache
}

func (x *AlatauCityGetStatusPayInResponse_Operation_Rec) Reset() {
	*x = AlatauCityGetStatusPayInResponse_Operation_Rec{}
	mi := &file_inner_processing_rest_alatau_city_payin_proto_msgTypes[24]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AlatauCityGetStatusPayInResponse_Operation_Rec) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AlatauCityGetStatusPayInResponse_Operation_Rec) ProtoMessage() {}

func (x *AlatauCityGetStatusPayInResponse_Operation_Rec) ProtoReflect() protoreflect.Message {
	mi := &file_inner_processing_rest_alatau_city_payin_proto_msgTypes[24]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AlatauCityGetStatusPayInResponse_Operation_Rec.ProtoReflect.Descriptor instead.
func (*AlatauCityGetStatusPayInResponse_Operation_Rec) Descriptor() ([]byte, []int) {
	return file_inner_processing_rest_alatau_city_payin_proto_rawDescGZIP(), []int{4, 2, 1}
}

func (x *AlatauCityGetStatusPayInResponse_Operation_Rec) GetStatus() string {
	if x != nil && x.Status != nil {
		return *x.Status
	}
	return ""
}

func (x *AlatauCityGetStatusPayInResponse_Operation_Rec) GetStatusDesc() string {
	if x != nil && x.StatusDesc != nil {
		return *x.StatusDesc
	}
	return ""
}

func (x *AlatauCityGetStatusPayInResponse_Operation_Rec) GetRevRc() string {
	if x != nil && x.RevRc != nil {
		return *x.RevRc
	}
	return ""
}

func (x *AlatauCityGetStatusPayInResponse_Operation_Rec) GetRevAmount() string {
	if x != nil && x.RevAmount != nil {
		return *x.RevAmount
	}
	return ""
}

func (x *AlatauCityGetStatusPayInResponse_Operation_Rec) GetRevDescription() string {
	if x != nil && x.RevDescription != nil {
		return *x.RevDescription
	}
	return ""
}

func (x *AlatauCityGetStatusPayInResponse_Operation_Rec) GetRevError() string {
	if x != nil && x.RevError != nil {
		return *x.RevError
	}
	return ""
}

func (x *AlatauCityGetStatusPayInResponse_Operation_Rec) GetRevDate() string {
	if x != nil && x.RevDate != nil {
		return *x.RevDate
	}
	return ""
}

type AlatauCityConfirmThreeDSRequest_ConfirmThreeDSRequest struct {
	state              protoimpl.MessageState `protogen:"open.v1"`
	Cres               *string                `protobuf:"bytes,1,opt,name=cres" json:"cres,omitempty"`
	ThreeDSSessionData *string                `protobuf:"bytes,2,opt,name=threeDSSessionData" json:"threeDSSessionData,omitempty"`
	unknownFields      protoimpl.UnknownFields
	sizeCache          protoimpl.SizeCache
}

func (x *AlatauCityConfirmThreeDSRequest_ConfirmThreeDSRequest) Reset() {
	*x = AlatauCityConfirmThreeDSRequest_ConfirmThreeDSRequest{}
	mi := &file_inner_processing_rest_alatau_city_payin_proto_msgTypes[25]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AlatauCityConfirmThreeDSRequest_ConfirmThreeDSRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AlatauCityConfirmThreeDSRequest_ConfirmThreeDSRequest) ProtoMessage() {}

func (x *AlatauCityConfirmThreeDSRequest_ConfirmThreeDSRequest) ProtoReflect() protoreflect.Message {
	mi := &file_inner_processing_rest_alatau_city_payin_proto_msgTypes[25]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AlatauCityConfirmThreeDSRequest_ConfirmThreeDSRequest.ProtoReflect.Descriptor instead.
func (*AlatauCityConfirmThreeDSRequest_ConfirmThreeDSRequest) Descriptor() ([]byte, []int) {
	return file_inner_processing_rest_alatau_city_payin_proto_rawDescGZIP(), []int{7, 0}
}

func (x *AlatauCityConfirmThreeDSRequest_ConfirmThreeDSRequest) GetCres() string {
	if x != nil && x.Cres != nil {
		return *x.Cres
	}
	return ""
}

func (x *AlatauCityConfirmThreeDSRequest_ConfirmThreeDSRequest) GetThreeDSSessionData() string {
	if x != nil && x.ThreeDSSessionData != nil {
		return *x.ThreeDSSessionData
	}
	return ""
}

type AlatauCityResumeThreeDSRequest_ResumeThreeDSRequest struct {
	state              protoimpl.MessageState `protogen:"open.v1"`
	ThreeDSMethodData  *string                `protobuf:"bytes,1,opt,name=threeDSMethodData" json:"threeDSMethodData,omitempty"`
	ThreeDSMethodState *string                `protobuf:"bytes,2,opt,name=threeDSMethodState" json:"threeDSMethodState,omitempty"`
	unknownFields      protoimpl.UnknownFields
	sizeCache          protoimpl.SizeCache
}

func (x *AlatauCityResumeThreeDSRequest_ResumeThreeDSRequest) Reset() {
	*x = AlatauCityResumeThreeDSRequest_ResumeThreeDSRequest{}
	mi := &file_inner_processing_rest_alatau_city_payin_proto_msgTypes[26]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AlatauCityResumeThreeDSRequest_ResumeThreeDSRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AlatauCityResumeThreeDSRequest_ResumeThreeDSRequest) ProtoMessage() {}

func (x *AlatauCityResumeThreeDSRequest_ResumeThreeDSRequest) ProtoReflect() protoreflect.Message {
	mi := &file_inner_processing_rest_alatau_city_payin_proto_msgTypes[26]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AlatauCityResumeThreeDSRequest_ResumeThreeDSRequest.ProtoReflect.Descriptor instead.
func (*AlatauCityResumeThreeDSRequest_ResumeThreeDSRequest) Descriptor() ([]byte, []int) {
	return file_inner_processing_rest_alatau_city_payin_proto_rawDescGZIP(), []int{9, 0}
}

func (x *AlatauCityResumeThreeDSRequest_ResumeThreeDSRequest) GetThreeDSMethodData() string {
	if x != nil && x.ThreeDSMethodData != nil {
		return *x.ThreeDSMethodData
	}
	return ""
}

func (x *AlatauCityResumeThreeDSRequest_ResumeThreeDSRequest) GetThreeDSMethodState() string {
	if x != nil && x.ThreeDSMethodState != nil {
		return *x.ThreeDSMethodState
	}
	return ""
}

type AlatauCitySendFormRequest_SendFormRequest struct {
	state             protoimpl.MessageState `protogen:"open.v1"`
	ThreeDSMethodData *string                `protobuf:"bytes,1,opt,name=threeDSMethodData" json:"threeDSMethodData,omitempty"`
	unknownFields     protoimpl.UnknownFields
	sizeCache         protoimpl.SizeCache
}

func (x *AlatauCitySendFormRequest_SendFormRequest) Reset() {
	*x = AlatauCitySendFormRequest_SendFormRequest{}
	mi := &file_inner_processing_rest_alatau_city_payin_proto_msgTypes[27]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AlatauCitySendFormRequest_SendFormRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AlatauCitySendFormRequest_SendFormRequest) ProtoMessage() {}

func (x *AlatauCitySendFormRequest_SendFormRequest) ProtoReflect() protoreflect.Message {
	mi := &file_inner_processing_rest_alatau_city_payin_proto_msgTypes[27]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AlatauCitySendFormRequest_SendFormRequest.ProtoReflect.Descriptor instead.
func (*AlatauCitySendFormRequest_SendFormRequest) Descriptor() ([]byte, []int) {
	return file_inner_processing_rest_alatau_city_payin_proto_rawDescGZIP(), []int{11, 0}
}

func (x *AlatauCitySendFormRequest_SendFormRequest) GetThreeDSMethodData() string {
	if x != nil && x.ThreeDSMethodData != nil {
		return *x.ThreeDSMethodData
	}
	return ""
}

type AlatauCityCancelPaymentRequest_CancelPaymentRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	ORDER         *string                `protobuf:"bytes,1,opt,name=ORDER" json:"ORDER,omitempty"`
	MERCHANT      *string                `protobuf:"bytes,2,opt,name=MERCHANT" json:"MERCHANT,omitempty"`
	P_SIGN        *string                `protobuf:"bytes,3,opt,name=P_SIGN,json=PSIGN" json:"P_SIGN,omitempty"`
	REV_AMOUNT    *string                `protobuf:"bytes,4,opt,name=REV_AMOUNT,json=REVAMOUNT" json:"REV_AMOUNT,omitempty"`
	REV_DESC      *string                `protobuf:"bytes,5,opt,name=REV_DESC,json=REVDESC" json:"REV_DESC,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *AlatauCityCancelPaymentRequest_CancelPaymentRequest) Reset() {
	*x = AlatauCityCancelPaymentRequest_CancelPaymentRequest{}
	mi := &file_inner_processing_rest_alatau_city_payin_proto_msgTypes[28]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AlatauCityCancelPaymentRequest_CancelPaymentRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AlatauCityCancelPaymentRequest_CancelPaymentRequest) ProtoMessage() {}

func (x *AlatauCityCancelPaymentRequest_CancelPaymentRequest) ProtoReflect() protoreflect.Message {
	mi := &file_inner_processing_rest_alatau_city_payin_proto_msgTypes[28]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AlatauCityCancelPaymentRequest_CancelPaymentRequest.ProtoReflect.Descriptor instead.
func (*AlatauCityCancelPaymentRequest_CancelPaymentRequest) Descriptor() ([]byte, []int) {
	return file_inner_processing_rest_alatau_city_payin_proto_rawDescGZIP(), []int{13, 0}
}

func (x *AlatauCityCancelPaymentRequest_CancelPaymentRequest) GetORDER() string {
	if x != nil && x.ORDER != nil {
		return *x.ORDER
	}
	return ""
}

func (x *AlatauCityCancelPaymentRequest_CancelPaymentRequest) GetMERCHANT() string {
	if x != nil && x.MERCHANT != nil {
		return *x.MERCHANT
	}
	return ""
}

func (x *AlatauCityCancelPaymentRequest_CancelPaymentRequest) GetP_SIGN() string {
	if x != nil && x.P_SIGN != nil {
		return *x.P_SIGN
	}
	return ""
}

func (x *AlatauCityCancelPaymentRequest_CancelPaymentRequest) GetREV_AMOUNT() string {
	if x != nil && x.REV_AMOUNT != nil {
		return *x.REV_AMOUNT
	}
	return ""
}

func (x *AlatauCityCancelPaymentRequest_CancelPaymentRequest) GetREV_DESC() string {
	if x != nil && x.REV_DESC != nil {
		return *x.REV_DESC
	}
	return ""
}

type AlatauCityCancelPaymentResponse_Result struct {
	state         protoimpl.MessageState                     `protogen:"open.v1"`
	Code          *string                                    `protobuf:"bytes,1,opt,name=code" json:"code,omitempty"`
	Description   *string                                    `protobuf:"bytes,2,opt,name=description" json:"description,omitempty"`
	Operation     *AlatauCityCancelPaymentResponse_Operation `protobuf:"bytes,3,opt,name=operation" json:"operation,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *AlatauCityCancelPaymentResponse_Result) Reset() {
	*x = AlatauCityCancelPaymentResponse_Result{}
	mi := &file_inner_processing_rest_alatau_city_payin_proto_msgTypes[29]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AlatauCityCancelPaymentResponse_Result) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AlatauCityCancelPaymentResponse_Result) ProtoMessage() {}

func (x *AlatauCityCancelPaymentResponse_Result) ProtoReflect() protoreflect.Message {
	mi := &file_inner_processing_rest_alatau_city_payin_proto_msgTypes[29]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AlatauCityCancelPaymentResponse_Result.ProtoReflect.Descriptor instead.
func (*AlatauCityCancelPaymentResponse_Result) Descriptor() ([]byte, []int) {
	return file_inner_processing_rest_alatau_city_payin_proto_rawDescGZIP(), []int{14, 0}
}

func (x *AlatauCityCancelPaymentResponse_Result) GetCode() string {
	if x != nil && x.Code != nil {
		return *x.Code
	}
	return ""
}

func (x *AlatauCityCancelPaymentResponse_Result) GetDescription() string {
	if x != nil && x.Description != nil {
		return *x.Description
	}
	return ""
}

func (x *AlatauCityCancelPaymentResponse_Result) GetOperation() *AlatauCityCancelPaymentResponse_Operation {
	if x != nil {
		return x.Operation
	}
	return nil
}

type AlatauCityCancelPaymentResponse_CancelPaymentResponse struct {
	state         protoimpl.MessageState                  `protogen:"open.v1"`
	Result        *AlatauCityCancelPaymentResponse_Result `protobuf:"bytes,1,opt,name=result" json:"result,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *AlatauCityCancelPaymentResponse_CancelPaymentResponse) Reset() {
	*x = AlatauCityCancelPaymentResponse_CancelPaymentResponse{}
	mi := &file_inner_processing_rest_alatau_city_payin_proto_msgTypes[30]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AlatauCityCancelPaymentResponse_CancelPaymentResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AlatauCityCancelPaymentResponse_CancelPaymentResponse) ProtoMessage() {}

func (x *AlatauCityCancelPaymentResponse_CancelPaymentResponse) ProtoReflect() protoreflect.Message {
	mi := &file_inner_processing_rest_alatau_city_payin_proto_msgTypes[30]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AlatauCityCancelPaymentResponse_CancelPaymentResponse.ProtoReflect.Descriptor instead.
func (*AlatauCityCancelPaymentResponse_CancelPaymentResponse) Descriptor() ([]byte, []int) {
	return file_inner_processing_rest_alatau_city_payin_proto_rawDescGZIP(), []int{14, 1}
}

func (x *AlatauCityCancelPaymentResponse_CancelPaymentResponse) GetResult() *AlatauCityCancelPaymentResponse_Result {
	if x != nil {
		return x.Result
	}
	return nil
}

type AlatauCityCancelPaymentResponse_Operation struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Status        *string                `protobuf:"bytes,1,opt,name=status" json:"status,omitempty"`
	ResultDesc    *string                `protobuf:"bytes,2,opt,name=result_desc,json=resultDesc" json:"result_desc,omitempty"`
	Result        *string                `protobuf:"bytes,3,opt,name=result" json:"result,omitempty"`
	Rc            *string                `protobuf:"bytes,4,opt,name=rc" json:"rc,omitempty"`
	Ecode         *string                `protobuf:"bytes,5,opt,name=ecode" json:"ecode,omitempty"`
	Edesc         *string                `protobuf:"bytes,6,opt,name=edesc" json:"edesc,omitempty"`
	Amount        *string                `protobuf:"bytes,7,opt,name=amount" json:"amount,omitempty"`
	Rrn           *string                `protobuf:"bytes,8,opt,name=rrn" json:"rrn,omitempty"`
	RevDesc       *string                `protobuf:"bytes,9,opt,name=rev_desc,json=revDesc" json:"rev_desc,omitempty"`
	RevDate       *string                `protobuf:"bytes,10,opt,name=rev_date,json=revDate" json:"rev_date,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *AlatauCityCancelPaymentResponse_Operation) Reset() {
	*x = AlatauCityCancelPaymentResponse_Operation{}
	mi := &file_inner_processing_rest_alatau_city_payin_proto_msgTypes[31]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AlatauCityCancelPaymentResponse_Operation) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AlatauCityCancelPaymentResponse_Operation) ProtoMessage() {}

func (x *AlatauCityCancelPaymentResponse_Operation) ProtoReflect() protoreflect.Message {
	mi := &file_inner_processing_rest_alatau_city_payin_proto_msgTypes[31]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AlatauCityCancelPaymentResponse_Operation.ProtoReflect.Descriptor instead.
func (*AlatauCityCancelPaymentResponse_Operation) Descriptor() ([]byte, []int) {
	return file_inner_processing_rest_alatau_city_payin_proto_rawDescGZIP(), []int{14, 2}
}

func (x *AlatauCityCancelPaymentResponse_Operation) GetStatus() string {
	if x != nil && x.Status != nil {
		return *x.Status
	}
	return ""
}

func (x *AlatauCityCancelPaymentResponse_Operation) GetResultDesc() string {
	if x != nil && x.ResultDesc != nil {
		return *x.ResultDesc
	}
	return ""
}

func (x *AlatauCityCancelPaymentResponse_Operation) GetResult() string {
	if x != nil && x.Result != nil {
		return *x.Result
	}
	return ""
}

func (x *AlatauCityCancelPaymentResponse_Operation) GetRc() string {
	if x != nil && x.Rc != nil {
		return *x.Rc
	}
	return ""
}

func (x *AlatauCityCancelPaymentResponse_Operation) GetEcode() string {
	if x != nil && x.Ecode != nil {
		return *x.Ecode
	}
	return ""
}

func (x *AlatauCityCancelPaymentResponse_Operation) GetEdesc() string {
	if x != nil && x.Edesc != nil {
		return *x.Edesc
	}
	return ""
}

func (x *AlatauCityCancelPaymentResponse_Operation) GetAmount() string {
	if x != nil && x.Amount != nil {
		return *x.Amount
	}
	return ""
}

func (x *AlatauCityCancelPaymentResponse_Operation) GetRrn() string {
	if x != nil && x.Rrn != nil {
		return *x.Rrn
	}
	return ""
}

func (x *AlatauCityCancelPaymentResponse_Operation) GetRevDesc() string {
	if x != nil && x.RevDesc != nil {
		return *x.RevDesc
	}
	return ""
}

func (x *AlatauCityCancelPaymentResponse_Operation) GetRevDate() string {
	if x != nil && x.RevDate != nil {
		return *x.RevDate
	}
	return ""
}

type AlatauCityRegisterTokenRequest_RegisterTokenRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	ORDER         *string                `protobuf:"bytes,1,opt,name=ORDER" json:"ORDER,omitempty"`
	MERCHANT      *string                `protobuf:"bytes,2,opt,name=MERCHANT" json:"MERCHANT,omitempty"`
	TERMINAL      *string                `protobuf:"bytes,3,opt,name=TERMINAL" json:"TERMINAL,omitempty"`
	CLIENT_ID     *string                `protobuf:"bytes,4,opt,name=CLIENT_ID,json=CLIENTID" json:"CLIENT_ID,omitempty"`
	CrdPan        *string                `protobuf:"bytes,5,opt,name=crd_pan,json=crdPan" json:"crd_pan,omitempty"`
	CrdExp        *string                `protobuf:"bytes,6,opt,name=crd_exp,json=crdExp" json:"crd_exp,omitempty"`
	CrdCvc        *string                `protobuf:"bytes,7,opt,name=crd_cvc,json=crdCvc" json:"crd_cvc,omitempty"`
	NAME          *string                `protobuf:"bytes,8,opt,name=NAME" json:"NAME,omitempty"`
	P_SIGN        *string                `protobuf:"bytes,9,opt,name=P_SIGN,json=PSIGN" json:"P_SIGN,omitempty"`
	TOKEN_CMD     *string                `protobuf:"bytes,10,opt,name=TOKEN_CMD,json=TOKENCMD" json:"TOKEN_CMD,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *AlatauCityRegisterTokenRequest_RegisterTokenRequest) Reset() {
	*x = AlatauCityRegisterTokenRequest_RegisterTokenRequest{}
	mi := &file_inner_processing_rest_alatau_city_payin_proto_msgTypes[32]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AlatauCityRegisterTokenRequest_RegisterTokenRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AlatauCityRegisterTokenRequest_RegisterTokenRequest) ProtoMessage() {}

func (x *AlatauCityRegisterTokenRequest_RegisterTokenRequest) ProtoReflect() protoreflect.Message {
	mi := &file_inner_processing_rest_alatau_city_payin_proto_msgTypes[32]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AlatauCityRegisterTokenRequest_RegisterTokenRequest.ProtoReflect.Descriptor instead.
func (*AlatauCityRegisterTokenRequest_RegisterTokenRequest) Descriptor() ([]byte, []int) {
	return file_inner_processing_rest_alatau_city_payin_proto_rawDescGZIP(), []int{15, 0}
}

func (x *AlatauCityRegisterTokenRequest_RegisterTokenRequest) GetORDER() string {
	if x != nil && x.ORDER != nil {
		return *x.ORDER
	}
	return ""
}

func (x *AlatauCityRegisterTokenRequest_RegisterTokenRequest) GetMERCHANT() string {
	if x != nil && x.MERCHANT != nil {
		return *x.MERCHANT
	}
	return ""
}

func (x *AlatauCityRegisterTokenRequest_RegisterTokenRequest) GetTERMINAL() string {
	if x != nil && x.TERMINAL != nil {
		return *x.TERMINAL
	}
	return ""
}

func (x *AlatauCityRegisterTokenRequest_RegisterTokenRequest) GetCLIENT_ID() string {
	if x != nil && x.CLIENT_ID != nil {
		return *x.CLIENT_ID
	}
	return ""
}

func (x *AlatauCityRegisterTokenRequest_RegisterTokenRequest) GetCrdPan() string {
	if x != nil && x.CrdPan != nil {
		return *x.CrdPan
	}
	return ""
}

func (x *AlatauCityRegisterTokenRequest_RegisterTokenRequest) GetCrdExp() string {
	if x != nil && x.CrdExp != nil {
		return *x.CrdExp
	}
	return ""
}

func (x *AlatauCityRegisterTokenRequest_RegisterTokenRequest) GetCrdCvc() string {
	if x != nil && x.CrdCvc != nil {
		return *x.CrdCvc
	}
	return ""
}

func (x *AlatauCityRegisterTokenRequest_RegisterTokenRequest) GetNAME() string {
	if x != nil && x.NAME != nil {
		return *x.NAME
	}
	return ""
}

func (x *AlatauCityRegisterTokenRequest_RegisterTokenRequest) GetP_SIGN() string {
	if x != nil && x.P_SIGN != nil {
		return *x.P_SIGN
	}
	return ""
}

func (x *AlatauCityRegisterTokenRequest_RegisterTokenRequest) GetTOKEN_CMD() string {
	if x != nil && x.TOKEN_CMD != nil {
		return *x.TOKEN_CMD
	}
	return ""
}

type AlatauCityRegisterTokenResponse_RegisterTokenResponse struct {
	state         protoimpl.MessageState                  `protogen:"open.v1"`
	Result        *AlatauCityRegisterTokenResponse_Result `protobuf:"bytes,1,opt,name=result" json:"result,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *AlatauCityRegisterTokenResponse_RegisterTokenResponse) Reset() {
	*x = AlatauCityRegisterTokenResponse_RegisterTokenResponse{}
	mi := &file_inner_processing_rest_alatau_city_payin_proto_msgTypes[33]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AlatauCityRegisterTokenResponse_RegisterTokenResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AlatauCityRegisterTokenResponse_RegisterTokenResponse) ProtoMessage() {}

func (x *AlatauCityRegisterTokenResponse_RegisterTokenResponse) ProtoReflect() protoreflect.Message {
	mi := &file_inner_processing_rest_alatau_city_payin_proto_msgTypes[33]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AlatauCityRegisterTokenResponse_RegisterTokenResponse.ProtoReflect.Descriptor instead.
func (*AlatauCityRegisterTokenResponse_RegisterTokenResponse) Descriptor() ([]byte, []int) {
	return file_inner_processing_rest_alatau_city_payin_proto_rawDescGZIP(), []int{16, 0}
}

func (x *AlatauCityRegisterTokenResponse_RegisterTokenResponse) GetResult() *AlatauCityRegisterTokenResponse_Result {
	if x != nil {
		return x.Result
	}
	return nil
}

type AlatauCityRegisterTokenResponse_Result struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Code          *string                `protobuf:"bytes,1,opt,name=code" json:"code,omitempty"`
	Description   *string                `protobuf:"bytes,2,opt,name=description" json:"description,omitempty"`
	Token         *string                `protobuf:"bytes,3,opt,name=token" json:"token,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *AlatauCityRegisterTokenResponse_Result) Reset() {
	*x = AlatauCityRegisterTokenResponse_Result{}
	mi := &file_inner_processing_rest_alatau_city_payin_proto_msgTypes[34]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AlatauCityRegisterTokenResponse_Result) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AlatauCityRegisterTokenResponse_Result) ProtoMessage() {}

func (x *AlatauCityRegisterTokenResponse_Result) ProtoReflect() protoreflect.Message {
	mi := &file_inner_processing_rest_alatau_city_payin_proto_msgTypes[34]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AlatauCityRegisterTokenResponse_Result.ProtoReflect.Descriptor instead.
func (*AlatauCityRegisterTokenResponse_Result) Descriptor() ([]byte, []int) {
	return file_inner_processing_rest_alatau_city_payin_proto_rawDescGZIP(), []int{16, 1}
}

func (x *AlatauCityRegisterTokenResponse_Result) GetCode() string {
	if x != nil && x.Code != nil {
		return *x.Code
	}
	return ""
}

func (x *AlatauCityRegisterTokenResponse_Result) GetDescription() string {
	if x != nil && x.Description != nil {
		return *x.Description
	}
	return ""
}

func (x *AlatauCityRegisterTokenResponse_Result) GetToken() string {
	if x != nil && x.Token != nil {
		return *x.Token
	}
	return ""
}

type AlatauCityChargeRequest_ChargeRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	APPROVE       *string                `protobuf:"bytes,1,opt,name=APPROVE" json:"APPROVE,omitempty"`
	ORDER         *string                `protobuf:"bytes,2,opt,name=ORDER" json:"ORDER,omitempty"`
	AMOUNT        *string                `protobuf:"bytes,3,opt,name=AMOUNT" json:"AMOUNT,omitempty"`
	MERCHANT      *string                `protobuf:"bytes,4,opt,name=MERCHANT" json:"MERCHANT,omitempty"`
	P_SIGN        *string                `protobuf:"bytes,5,opt,name=P_SIGN,json=PSIGN" json:"P_SIGN,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *AlatauCityChargeRequest_ChargeRequest) Reset() {
	*x = AlatauCityChargeRequest_ChargeRequest{}
	mi := &file_inner_processing_rest_alatau_city_payin_proto_msgTypes[35]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AlatauCityChargeRequest_ChargeRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AlatauCityChargeRequest_ChargeRequest) ProtoMessage() {}

func (x *AlatauCityChargeRequest_ChargeRequest) ProtoReflect() protoreflect.Message {
	mi := &file_inner_processing_rest_alatau_city_payin_proto_msgTypes[35]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AlatauCityChargeRequest_ChargeRequest.ProtoReflect.Descriptor instead.
func (*AlatauCityChargeRequest_ChargeRequest) Descriptor() ([]byte, []int) {
	return file_inner_processing_rest_alatau_city_payin_proto_rawDescGZIP(), []int{17, 0}
}

func (x *AlatauCityChargeRequest_ChargeRequest) GetAPPROVE() string {
	if x != nil && x.APPROVE != nil {
		return *x.APPROVE
	}
	return ""
}

func (x *AlatauCityChargeRequest_ChargeRequest) GetORDER() string {
	if x != nil && x.ORDER != nil {
		return *x.ORDER
	}
	return ""
}

func (x *AlatauCityChargeRequest_ChargeRequest) GetAMOUNT() string {
	if x != nil && x.AMOUNT != nil {
		return *x.AMOUNT
	}
	return ""
}

func (x *AlatauCityChargeRequest_ChargeRequest) GetMERCHANT() string {
	if x != nil && x.MERCHANT != nil {
		return *x.MERCHANT
	}
	return ""
}

func (x *AlatauCityChargeRequest_ChargeRequest) GetP_SIGN() string {
	if x != nil && x.P_SIGN != nil {
		return *x.P_SIGN
	}
	return ""
}

type AlatauCityChargeResponse_ChargeResponse struct {
	state         protoimpl.MessageState           `protogen:"open.v1"`
	Result        *AlatauCityChargeResponse_Result `protobuf:"bytes,1,opt,name=result" json:"result,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *AlatauCityChargeResponse_ChargeResponse) Reset() {
	*x = AlatauCityChargeResponse_ChargeResponse{}
	mi := &file_inner_processing_rest_alatau_city_payin_proto_msgTypes[36]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AlatauCityChargeResponse_ChargeResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AlatauCityChargeResponse_ChargeResponse) ProtoMessage() {}

func (x *AlatauCityChargeResponse_ChargeResponse) ProtoReflect() protoreflect.Message {
	mi := &file_inner_processing_rest_alatau_city_payin_proto_msgTypes[36]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AlatauCityChargeResponse_ChargeResponse.ProtoReflect.Descriptor instead.
func (*AlatauCityChargeResponse_ChargeResponse) Descriptor() ([]byte, []int) {
	return file_inner_processing_rest_alatau_city_payin_proto_rawDescGZIP(), []int{18, 0}
}

func (x *AlatauCityChargeResponse_ChargeResponse) GetResult() *AlatauCityChargeResponse_Result {
	if x != nil {
		return x.Result
	}
	return nil
}

type AlatauCityChargeResponse_Result struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Code          *string                `protobuf:"bytes,1,opt,name=code" json:"code,omitempty"`
	Description   *string                `protobuf:"bytes,2,opt,name=description" json:"description,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *AlatauCityChargeResponse_Result) Reset() {
	*x = AlatauCityChargeResponse_Result{}
	mi := &file_inner_processing_rest_alatau_city_payin_proto_msgTypes[37]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AlatauCityChargeResponse_Result) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AlatauCityChargeResponse_Result) ProtoMessage() {}

func (x *AlatauCityChargeResponse_Result) ProtoReflect() protoreflect.Message {
	mi := &file_inner_processing_rest_alatau_city_payin_proto_msgTypes[37]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AlatauCityChargeResponse_Result.ProtoReflect.Descriptor instead.
func (*AlatauCityChargeResponse_Result) Descriptor() ([]byte, []int) {
	return file_inner_processing_rest_alatau_city_payin_proto_rawDescGZIP(), []int{18, 1}
}

func (x *AlatauCityChargeResponse_Result) GetCode() string {
	if x != nil && x.Code != nil {
		return *x.Code
	}
	return ""
}

func (x *AlatauCityChargeResponse_Result) GetDescription() string {
	if x != nil && x.Description != nil {
		return *x.Description
	}
	return ""
}

var File_inner_processing_rest_alatau_city_payin_proto protoreflect.FileDescriptor

var file_inner_processing_rest_alatau_city_payin_proto_rawDesc = string([]byte{
	0x0a, 0x2d, 0x69, 0x6e, 0x6e, 0x65, 0x72, 0x2f, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69,
	0x6e, 0x67, 0x2f, 0x72, 0x65, 0x73, 0x74, 0x2f, 0x61, 0x6c, 0x61, 0x74, 0x61, 0x75, 0x5f, 0x63,
	0x69, 0x74, 0x79, 0x5f, 0x70, 0x61, 0x79, 0x69, 0x6e, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12,
	0x10, 0x61, 0x6c, 0x61, 0x74, 0x61, 0x75, 0x5f, 0x63, 0x69, 0x74, 0x79, 0x5f, 0x62, 0x61, 0x6e,
	0x6b, 0x1a, 0x14, 0x6d, 0x76, 0x70, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f, 0x72, 0x65, 0x73,
	0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x16, 0x6d, 0x76, 0x70, 0x2f, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x2f, 0x6c, 0x6f, 0x67, 0x67, 0x65, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22,
	0x5d, 0x0a, 0x1d, 0x41, 0x6c, 0x61, 0x74, 0x61, 0x75, 0x43, 0x69, 0x74, 0x79, 0x50, 0x72, 0x6f,
	0x63, 0x65, 0x73, 0x73, 0x50, 0x61, 0x79, 0x49, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x12, 0x3c, 0x0a, 0x04, 0x62, 0x6f, 0x64, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x28,
	0x2e, 0x61, 0x6c, 0x61, 0x74, 0x61, 0x75, 0x5f, 0x63, 0x69, 0x74, 0x79, 0x5f, 0x62, 0x61, 0x6e,
	0x6b, 0x2e, 0x41, 0x6c, 0x61, 0x74, 0x61, 0x75, 0x43, 0x69, 0x74, 0x79, 0x50, 0x61, 0x79, 0x49,
	0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x52, 0x04, 0x62, 0x6f, 0x64, 0x79, 0x22, 0xbc,
	0x08, 0x0a, 0x16, 0x41, 0x6c, 0x61, 0x74, 0x61, 0x75, 0x43, 0x69, 0x74, 0x79, 0x50, 0x61, 0x79,
	0x49, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x16, 0x0a, 0x06, 0x41, 0x4d, 0x4f,
	0x55, 0x4e, 0x54, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x41, 0x4d, 0x4f, 0x55, 0x4e,
	0x54, 0x12, 0x18, 0x0a, 0x07, 0x42, 0x41, 0x43, 0x4b, 0x52, 0x45, 0x46, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x07, 0x42, 0x41, 0x43, 0x4b, 0x52, 0x45, 0x46, 0x12, 0x1b, 0x0a, 0x09, 0x43,
	0x4c, 0x49, 0x45, 0x4e, 0x54, 0x5f, 0x49, 0x44, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08,
	0x43, 0x4c, 0x49, 0x45, 0x4e, 0x54, 0x49, 0x44, 0x12, 0x1a, 0x0a, 0x08, 0x43, 0x55, 0x52, 0x52,
	0x45, 0x4e, 0x43, 0x59, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x43, 0x55, 0x52, 0x52,
	0x45, 0x4e, 0x43, 0x59, 0x12, 0x12, 0x0a, 0x04, 0x44, 0x45, 0x53, 0x43, 0x18, 0x05, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x04, 0x44, 0x45, 0x53, 0x43, 0x12, 0x1d, 0x0a, 0x0a, 0x44, 0x45, 0x53, 0x43,
	0x5f, 0x4f, 0x52, 0x44, 0x45, 0x52, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x44, 0x45,
	0x53, 0x43, 0x4f, 0x52, 0x44, 0x45, 0x52, 0x12, 0x14, 0x0a, 0x05, 0x45, 0x4d, 0x41, 0x49, 0x4c,
	0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x45, 0x4d, 0x41, 0x49, 0x4c, 0x12, 0x1e, 0x0a,
	0x0b, 0x45, 0x58, 0x54, 0x5f, 0x4d, 0x50, 0x49, 0x5f, 0x45, 0x43, 0x49, 0x18, 0x08, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x09, 0x45, 0x58, 0x54, 0x4d, 0x50, 0x49, 0x45, 0x43, 0x49, 0x12, 0x17, 0x0a,
	0x07, 0x49, 0x4e, 0x54, 0x5f, 0x52, 0x45, 0x46, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06,
	0x49, 0x4e, 0x54, 0x52, 0x45, 0x46, 0x12, 0x1a, 0x0a, 0x08, 0x4c, 0x41, 0x4e, 0x47, 0x55, 0x41,
	0x47, 0x45, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x4c, 0x41, 0x4e, 0x47, 0x55, 0x41,
	0x47, 0x45, 0x12, 0x1a, 0x0a, 0x08, 0x4d, 0x45, 0x52, 0x43, 0x48, 0x41, 0x4e, 0x54, 0x18, 0x0b,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x4d, 0x45, 0x52, 0x43, 0x48, 0x41, 0x4e, 0x54, 0x12, 0x2f,
	0x0a, 0x14, 0x4d, 0x45, 0x52, 0x43, 0x48, 0x5f, 0x50, 0x41, 0x59, 0x54, 0x4f, 0x5f, 0x54, 0x4f,
	0x4b, 0x45, 0x4e, 0x5f, 0x49, 0x44, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x09, 0x52, 0x11, 0x4d, 0x45,
	0x52, 0x43, 0x48, 0x50, 0x41, 0x59, 0x54, 0x4f, 0x54, 0x4f, 0x4b, 0x45, 0x4e, 0x49, 0x44, 0x12,
	0x1e, 0x0a, 0x0b, 0x4d, 0x45, 0x52, 0x43, 0x48, 0x5f, 0x52, 0x4e, 0x5f, 0x49, 0x44, 0x18, 0x0d,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x4d, 0x45, 0x52, 0x43, 0x48, 0x52, 0x4e, 0x49, 0x44, 0x12,
	0x24, 0x0a, 0x0e, 0x4d, 0x45, 0x52, 0x43, 0x48, 0x5f, 0x54, 0x4f, 0x4b, 0x45, 0x4e, 0x5f, 0x49,
	0x44, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x4d, 0x45, 0x52, 0x43, 0x48, 0x54, 0x4f,
	0x4b, 0x45, 0x4e, 0x49, 0x44, 0x12, 0x28, 0x0a, 0x10, 0x4d, 0x45, 0x52, 0x43, 0x48, 0x5f, 0x54,
	0x52, 0x41, 0x4e, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x45, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0e, 0x4d, 0x45, 0x52, 0x43, 0x48, 0x54, 0x52, 0x41, 0x4e, 0x53, 0x54, 0x41, 0x54, 0x45, 0x12,
	0x19, 0x0a, 0x08, 0x4d, 0x4b, 0x5f, 0x54, 0x4f, 0x4b, 0x45, 0x4e, 0x18, 0x10, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x07, 0x4d, 0x4b, 0x54, 0x4f, 0x4b, 0x45, 0x4e, 0x12, 0x15, 0x0a, 0x06, 0x4d, 0x5f,
	0x49, 0x4e, 0x46, 0x4f, 0x18, 0x11, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x4d, 0x49, 0x4e, 0x46,
	0x4f, 0x12, 0x12, 0x0a, 0x04, 0x4e, 0x41, 0x4d, 0x45, 0x18, 0x12, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x04, 0x4e, 0x41, 0x4d, 0x45, 0x12, 0x14, 0x0a, 0x05, 0x4e, 0x4f, 0x4e, 0x43, 0x45, 0x18, 0x13,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x4e, 0x4f, 0x4e, 0x43, 0x45, 0x12, 0x14, 0x0a, 0x05, 0x4f,
	0x52, 0x44, 0x45, 0x52, 0x18, 0x14, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x4f, 0x52, 0x44, 0x45,
	0x52, 0x12, 0x1d, 0x0a, 0x0a, 0x50, 0x41, 0x59, 0x4d, 0x45, 0x4e, 0x54, 0x5f, 0x54, 0x4f, 0x18,
	0x15, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x50, 0x41, 0x59, 0x4d, 0x45, 0x4e, 0x54, 0x54, 0x4f,
	0x12, 0x15, 0x0a, 0x06, 0x50, 0x5f, 0x53, 0x49, 0x47, 0x4e, 0x18, 0x16, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x05, 0x50, 0x53, 0x49, 0x47, 0x4e, 0x12, 0x1b, 0x0a, 0x09, 0x52, 0x45, 0x43, 0x55, 0x52,
	0x5f, 0x45, 0x58, 0x50, 0x18, 0x17, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x52, 0x45, 0x43, 0x55,
	0x52, 0x45, 0x58, 0x50, 0x12, 0x1d, 0x0a, 0x0a, 0x52, 0x45, 0x43, 0x55, 0x52, 0x5f, 0x46, 0x52,
	0x45, 0x51, 0x18, 0x18, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x52, 0x45, 0x43, 0x55, 0x52, 0x46,
	0x52, 0x45, 0x51, 0x12, 0x1b, 0x0a, 0x09, 0x52, 0x45, 0x43, 0x55, 0x52, 0x5f, 0x52, 0x45, 0x46,
	0x18, 0x19, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x52, 0x45, 0x43, 0x55, 0x52, 0x52, 0x45, 0x46,
	0x12, 0x12, 0x0a, 0x04, 0x54, 0x41, 0x56, 0x56, 0x18, 0x1a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04,
	0x54, 0x41, 0x56, 0x56, 0x12, 0x1a, 0x0a, 0x08, 0x54, 0x45, 0x52, 0x4d, 0x49, 0x4e, 0x41, 0x4c,
	0x18, 0x1b, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x54, 0x45, 0x52, 0x4d, 0x49, 0x4e, 0x41, 0x4c,
	0x12, 0x38, 0x0a, 0x18, 0x55, 0x63, 0x61, 0x66, 0x5f, 0x41, 0x75, 0x74, 0x68, 0x65, 0x6e, 0x74,
	0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x44, 0x61, 0x74, 0x61, 0x18, 0x1c, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x16, 0x55, 0x63, 0x61, 0x66, 0x41, 0x75, 0x74, 0x68, 0x65, 0x6e, 0x74, 0x69,
	0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x44, 0x61, 0x74, 0x61, 0x12, 0x1b, 0x0a, 0x09, 0x55, 0x63,
	0x61, 0x66, 0x5f, 0x46, 0x6c, 0x61, 0x67, 0x18, 0x1d, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x55,
	0x63, 0x61, 0x66, 0x46, 0x6c, 0x61, 0x67, 0x12, 0x14, 0x0a, 0x05, 0x57, 0x54, 0x59, 0x50, 0x45,
	0x18, 0x1e, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x57, 0x54, 0x59, 0x50, 0x45, 0x12, 0x20, 0x0a,
	0x07, 0x63, 0x72, 0x64, 0x5f, 0x63, 0x76, 0x63, 0x18, 0x1f, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07,
	0x9a, 0xfc, 0xeb, 0x8e, 0x02, 0x01, 0x64, 0x52, 0x06, 0x63, 0x72, 0x64, 0x43, 0x76, 0x63, 0x12,
	0x20, 0x0a, 0x07, 0x63, 0x72, 0x64, 0x5f, 0x65, 0x78, 0x70, 0x18, 0x20, 0x20, 0x01, 0x28, 0x09,
	0x42, 0x07, 0x9a, 0xfc, 0xeb, 0x8e, 0x02, 0x01, 0x64, 0x52, 0x06, 0x63, 0x72, 0x64, 0x45, 0x78,
	0x70, 0x12, 0x20, 0x0a, 0x07, 0x63, 0x72, 0x64, 0x5f, 0x70, 0x61, 0x6e, 0x18, 0x21, 0x20, 0x01,
	0x28, 0x09, 0x42, 0x07, 0x9a, 0xfc, 0xeb, 0x8e, 0x02, 0x01, 0x64, 0x52, 0x06, 0x63, 0x72, 0x64,
	0x50, 0x61, 0x6e, 0x12, 0x10, 0x0a, 0x03, 0x52, 0x45, 0x46, 0x18, 0x22, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x03, 0x52, 0x45, 0x46, 0x12, 0x29, 0x0a, 0x11, 0x4d, 0x45, 0x52, 0x43, 0x48, 0x5f, 0x33,
	0x44, 0x5f, 0x54, 0x45, 0x52, 0x4d, 0x5f, 0x55, 0x52, 0x4c, 0x18, 0x23, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0e, 0x4d, 0x45, 0x52, 0x43, 0x48, 0x33, 0x44, 0x54, 0x45, 0x52, 0x4d, 0x55, 0x52, 0x4c,
	0x12, 0x1b, 0x0a, 0x09, 0x4d, 0x45, 0x52, 0x43, 0x48, 0x5f, 0x53, 0x43, 0x41, 0x18, 0x24, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x08, 0x4d, 0x45, 0x52, 0x43, 0x48, 0x53, 0x43, 0x41, 0x22, 0x3c, 0x0a,
	0x1e, 0x41, 0x6c, 0x61, 0x74, 0x61, 0x75, 0x43, 0x69, 0x74, 0x79, 0x50, 0x72, 0x6f, 0x63, 0x65,
	0x73, 0x73, 0x50, 0x61, 0x79, 0x49, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12,
	0x1a, 0x0a, 0x08, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x08, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0xf4, 0x01, 0x0a, 0x1f,
	0x41, 0x6c, 0x61, 0x74, 0x61, 0x75, 0x43, 0x69, 0x74, 0x79, 0x47, 0x65, 0x74, 0x53, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x50, 0x61, 0x79, 0x49, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12,
	0x56, 0x0a, 0x04, 0x62, 0x6f, 0x64, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x42, 0x2e,
	0x61, 0x6c, 0x61, 0x74, 0x61, 0x75, 0x5f, 0x63, 0x69, 0x74, 0x79, 0x5f, 0x62, 0x61, 0x6e, 0x6b,
	0x2e, 0x41, 0x6c, 0x61, 0x74, 0x61, 0x75, 0x43, 0x69, 0x74, 0x79, 0x47, 0x65, 0x74, 0x53, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x50, 0x61, 0x79, 0x49, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x2e, 0x47, 0x65, 0x74, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x52, 0x04, 0x62, 0x6f, 0x64, 0x79, 0x1a, 0x79, 0x0a, 0x10, 0x47, 0x65, 0x74, 0x53, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x14, 0x0a, 0x05, 0x4f,
	0x52, 0x44, 0x45, 0x52, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x4f, 0x52, 0x44, 0x45,
	0x52, 0x12, 0x1a, 0x0a, 0x08, 0x4d, 0x45, 0x52, 0x43, 0x48, 0x41, 0x4e, 0x54, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x08, 0x4d, 0x45, 0x52, 0x43, 0x48, 0x41, 0x4e, 0x54, 0x12, 0x1c, 0x0a,
	0x09, 0x47, 0x45, 0x54, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x09, 0x47, 0x45, 0x54, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x12, 0x15, 0x0a, 0x06, 0x50,
	0x5f, 0x53, 0x49, 0x47, 0x4e, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x50, 0x53, 0x49,
	0x47, 0x4e, 0x22, 0xa0, 0x0d, 0x0a, 0x20, 0x41, 0x6c, 0x61, 0x74, 0x61, 0x75, 0x43, 0x69, 0x74,
	0x79, 0x47, 0x65, 0x74, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x50, 0x61, 0x79, 0x49, 0x6e, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x6a, 0x0a, 0x0f, 0x73, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x5f, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x41, 0x2e, 0x61, 0x6c, 0x61, 0x74, 0x61, 0x75, 0x5f, 0x63, 0x69, 0x74, 0x79, 0x5f, 0x62,
	0x61, 0x6e, 0x6b, 0x2e, 0x41, 0x6c, 0x61, 0x74, 0x61, 0x75, 0x43, 0x69, 0x74, 0x79, 0x47, 0x65,
	0x74, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x50, 0x61, 0x79, 0x49, 0x6e, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x52, 0x0e, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x1a, 0x63, 0x0a, 0x0e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x51, 0x0a, 0x06, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x39, 0x2e, 0x61, 0x6c, 0x61, 0x74, 0x61, 0x75, 0x5f, 0x63,
	0x69, 0x74, 0x79, 0x5f, 0x62, 0x61, 0x6e, 0x6b, 0x2e, 0x41, 0x6c, 0x61, 0x74, 0x61, 0x75, 0x43,
	0x69, 0x74, 0x79, 0x47, 0x65, 0x74, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x50, 0x61, 0x79, 0x49,
	0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74,
	0x52, 0x06, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x1a, 0x9a, 0x01, 0x0a, 0x06, 0x52, 0x65, 0x73,
	0x75, 0x6c, 0x74, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x12, 0x20, 0x0a, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72,
	0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x64, 0x65,
	0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x5a, 0x0a, 0x09, 0x6f, 0x70, 0x65,
	0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x3c, 0x2e, 0x61,
	0x6c, 0x61, 0x74, 0x61, 0x75, 0x5f, 0x63, 0x69, 0x74, 0x79, 0x5f, 0x62, 0x61, 0x6e, 0x6b, 0x2e,
	0x41, 0x6c, 0x61, 0x74, 0x61, 0x75, 0x43, 0x69, 0x74, 0x79, 0x47, 0x65, 0x74, 0x53, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x50, 0x61, 0x79, 0x49, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x2e, 0x4f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x09, 0x6f, 0x70, 0x65, 0x72,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x1a, 0x8d, 0x0a, 0x0a, 0x09, 0x4f, 0x70, 0x65, 0x72, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x1f, 0x0a, 0x0b, 0x73,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x5f, 0x64, 0x65, 0x73, 0x63, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0a, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x44, 0x65, 0x73, 0x63, 0x12, 0x16, 0x0a, 0x06,
	0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x61, 0x6d,
	0x6f, 0x75, 0x6e, 0x74, 0x12, 0x1a, 0x0a, 0x08, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x63, 0x79,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x63, 0x79,
	0x12, 0x20, 0x0a, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18,
	0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69,
	0x6f, 0x6e, 0x12, 0x1d, 0x0a, 0x0a, 0x64, 0x65, 0x73, 0x63, 0x5f, 0x6f, 0x72, 0x64, 0x65, 0x72,
	0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x64, 0x65, 0x73, 0x63, 0x4f, 0x72, 0x64, 0x65,
	0x72, 0x12, 0x14, 0x0a, 0x05, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x05, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x12, 0x12, 0x0a, 0x04, 0x6c, 0x61, 0x6e, 0x67, 0x18,
	0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6c, 0x61, 0x6e, 0x67, 0x12, 0x1b, 0x0a, 0x09, 0x6d,
	0x70, 0x69, 0x5f, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08,
	0x6d, 0x70, 0x69, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x12, 0x1a, 0x0a, 0x08, 0x74, 0x65, 0x72, 0x6d,
	0x69, 0x6e, 0x61, 0x6c, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x74, 0x65, 0x72, 0x6d,
	0x69, 0x6e, 0x61, 0x6c, 0x12, 0x14, 0x0a, 0x05, 0x70, 0x68, 0x6f, 0x6e, 0x65, 0x18, 0x0b, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x05, 0x70, 0x68, 0x6f, 0x6e, 0x65, 0x12, 0x28, 0x0a, 0x0b, 0x63, 0x61,
	0x72, 0x64, 0x5f, 0x6d, 0x61, 0x73, 0x6b, 0x65, 0x64, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x09, 0x42,
	0x07, 0x9a, 0xfc, 0xeb, 0x8e, 0x02, 0x01, 0x64, 0x52, 0x0a, 0x63, 0x61, 0x72, 0x64, 0x4d, 0x61,
	0x73, 0x6b, 0x65, 0x64, 0x12, 0x1b, 0x0a, 0x09, 0x63, 0x61, 0x72, 0x64, 0x5f, 0x6e, 0x61, 0x6d,
	0x65, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x63, 0x61, 0x72, 0x64, 0x4e, 0x61, 0x6d,
	0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x63, 0x61, 0x72, 0x64, 0x5f, 0x65, 0x78, 0x70, 0x64, 0x74, 0x18,
	0x0e, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x63, 0x61, 0x72, 0x64, 0x45, 0x78, 0x70, 0x64, 0x74,
	0x12, 0x1d, 0x0a, 0x0a, 0x63, 0x61, 0x72, 0x64, 0x5f, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x18, 0x0f,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x63, 0x61, 0x72, 0x64, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x12,
	0x1f, 0x0a, 0x0b, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x18, 0x10,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x44, 0x61, 0x74, 0x65,
	0x12, 0x16, 0x0a, 0x06, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x18, 0x11, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x06, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x1f, 0x0a, 0x0b, 0x72, 0x65, 0x73, 0x75,
	0x6c, 0x74, 0x5f, 0x64, 0x65, 0x73, 0x63, 0x18, 0x12, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x72,
	0x65, 0x73, 0x75, 0x6c, 0x74, 0x44, 0x65, 0x73, 0x63, 0x12, 0x0e, 0x0a, 0x02, 0x72, 0x63, 0x18,
	0x13, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x72, 0x63, 0x12, 0x10, 0x0a, 0x03, 0x72, 0x72, 0x6e,
	0x18, 0x14, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x72, 0x72, 0x6e, 0x12, 0x1b, 0x0a, 0x09, 0x61,
	0x75, 0x74, 0x68, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x15, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08,
	0x61, 0x75, 0x74, 0x68, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x15, 0x0a, 0x06, 0x69, 0x6e, 0x76, 0x5f,
	0x69, 0x64, 0x18, 0x16, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x69, 0x6e, 0x76, 0x49, 0x64, 0x12,
	0x20, 0x0a, 0x0c, 0x69, 0x6e, 0x74, 0x5f, 0x65, 0x78, 0x70, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x18,
	0x17, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x69, 0x6e, 0x74, 0x45, 0x78, 0x70, 0x44, 0x61, 0x74,
	0x65, 0x12, 0x24, 0x0a, 0x0e, 0x72, 0x65, 0x76, 0x5f, 0x6d, 0x61, 0x78, 0x5f, 0x61, 0x6d, 0x6f,
	0x75, 0x6e, 0x74, 0x18, 0x18, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x72, 0x65, 0x76, 0x4d, 0x61,
	0x78, 0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x1d, 0x0a, 0x0a, 0x72, 0x65, 0x63, 0x75, 0x72,
	0x5f, 0x66, 0x72, 0x65, 0x71, 0x18, 0x19, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x72, 0x65, 0x63,
	0x75, 0x72, 0x46, 0x72, 0x65, 0x71, 0x12, 0x1b, 0x0a, 0x09, 0x72, 0x65, 0x63, 0x75, 0x72, 0x5f,
	0x72, 0x65, 0x66, 0x18, 0x1a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x72, 0x65, 0x63, 0x75, 0x72,
	0x52, 0x65, 0x66, 0x12, 0x22, 0x0a, 0x0d, 0x72, 0x65, 0x63, 0x75, 0x72, 0x5f, 0x69, 0x6e, 0x74,
	0x5f, 0x72, 0x65, 0x66, 0x18, 0x1b, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x72, 0x65, 0x63, 0x75,
	0x72, 0x49, 0x6e, 0x74, 0x52, 0x65, 0x66, 0x12, 0x1b, 0x0a, 0x09, 0x63, 0x6c, 0x69, 0x65, 0x6e,
	0x74, 0x5f, 0x69, 0x64, 0x18, 0x1c, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x63, 0x6c, 0x69, 0x65,
	0x6e, 0x74, 0x49, 0x64, 0x12, 0x24, 0x0a, 0x0e, 0x63, 0x61, 0x72, 0x64, 0x5f, 0x74, 0x6f, 0x5f,
	0x6d, 0x61, 0x73, 0x6b, 0x65, 0x64, 0x18, 0x1d, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x63, 0x61,
	0x72, 0x64, 0x54, 0x6f, 0x4d, 0x61, 0x73, 0x6b, 0x65, 0x64, 0x12, 0x22, 0x0a, 0x0d, 0x63, 0x61,
	0x72, 0x64, 0x5f, 0x74, 0x6f, 0x5f, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x18, 0x1e, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0b, 0x63, 0x61, 0x72, 0x64, 0x54, 0x6f, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x12, 0x5e,
	0x0a, 0x07, 0x72, 0x65, 0x66, 0x75, 0x6e, 0x64, 0x73, 0x18, 0x1f, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x44, 0x2e, 0x61, 0x6c, 0x61, 0x74, 0x61, 0x75, 0x5f, 0x63, 0x69, 0x74, 0x79, 0x5f, 0x62, 0x61,
	0x6e, 0x6b, 0x2e, 0x41, 0x6c, 0x61, 0x74, 0x61, 0x75, 0x43, 0x69, 0x74, 0x79, 0x47, 0x65, 0x74,
	0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x50, 0x61, 0x79, 0x49, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x2e, 0x4f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x52, 0x65,
	0x66, 0x75, 0x6e, 0x64, 0x73, 0x52, 0x07, 0x72, 0x65, 0x66, 0x75, 0x6e, 0x64, 0x73, 0x1a, 0x5d,
	0x0a, 0x07, 0x52, 0x65, 0x66, 0x75, 0x6e, 0x64, 0x73, 0x12, 0x52, 0x0a, 0x03, 0x72, 0x65, 0x63,
	0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x40, 0x2e, 0x61, 0x6c, 0x61, 0x74, 0x61, 0x75, 0x5f,
	0x63, 0x69, 0x74, 0x79, 0x5f, 0x62, 0x61, 0x6e, 0x6b, 0x2e, 0x41, 0x6c, 0x61, 0x74, 0x61, 0x75,
	0x43, 0x69, 0x74, 0x79, 0x47, 0x65, 0x74, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x50, 0x61, 0x79,
	0x49, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x4f, 0x70, 0x65, 0x72, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x52, 0x65, 0x63, 0x52, 0x03, 0x72, 0x65, 0x63, 0x1a, 0xd5, 0x01,
	0x0a, 0x03, 0x52, 0x65, 0x63, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x1f, 0x0a,
	0x0b, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x5f, 0x64, 0x65, 0x73, 0x63, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0a, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x44, 0x65, 0x73, 0x63, 0x12, 0x15,
	0x0a, 0x06, 0x72, 0x65, 0x76, 0x5f, 0x72, 0x63, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05,
	0x72, 0x65, 0x76, 0x52, 0x63, 0x12, 0x1d, 0x0a, 0x0a, 0x72, 0x65, 0x76, 0x5f, 0x61, 0x6d, 0x6f,
	0x75, 0x6e, 0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x72, 0x65, 0x76, 0x41, 0x6d,
	0x6f, 0x75, 0x6e, 0x74, 0x12, 0x27, 0x0a, 0x0f, 0x72, 0x65, 0x76, 0x5f, 0x64, 0x65, 0x73, 0x63,
	0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x72,
	0x65, 0x76, 0x44, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x1b, 0x0a,
	0x09, 0x72, 0x65, 0x76, 0x5f, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x08, 0x72, 0x65, 0x76, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x12, 0x19, 0x0a, 0x08, 0x72, 0x65,
	0x76, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x72, 0x65,
	0x76, 0x44, 0x61, 0x74, 0x65, 0x22, 0x63, 0x0a, 0x23, 0x41, 0x6c, 0x61, 0x74, 0x61, 0x75, 0x43,
	0x69, 0x74, 0x79, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x4e, 0x6f, 0x41, 0x63, 0x63, 0x65,
	0x70, 0x74, 0x50, 0x61, 0x79, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x3c, 0x0a, 0x04,
	0x62, 0x6f, 0x64, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x28, 0x2e, 0x61, 0x6c, 0x61,
	0x74, 0x61, 0x75, 0x5f, 0x63, 0x69, 0x74, 0x79, 0x5f, 0x62, 0x61, 0x6e, 0x6b, 0x2e, 0x41, 0x6c,
	0x61, 0x74, 0x61, 0x75, 0x43, 0x69, 0x74, 0x79, 0x50, 0x61, 0x79, 0x49, 0x6e, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x52, 0x04, 0x62, 0x6f, 0x64, 0x79, 0x22, 0x42, 0x0a, 0x24, 0x41, 0x6c,
	0x61, 0x74, 0x61, 0x75, 0x43, 0x69, 0x74, 0x79, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x4e,
	0x6f, 0x41, 0x63, 0x63, 0x65, 0x70, 0x74, 0x50, 0x61, 0x79, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0xdb,
	0x01, 0x0a, 0x1f, 0x41, 0x6c, 0x61, 0x74, 0x61, 0x75, 0x43, 0x69, 0x74, 0x79, 0x43, 0x6f, 0x6e,
	0x66, 0x69, 0x72, 0x6d, 0x54, 0x68, 0x72, 0x65, 0x65, 0x44, 0x53, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x12, 0x5b, 0x0a, 0x04, 0x62, 0x6f, 0x64, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x47, 0x2e, 0x61, 0x6c, 0x61, 0x74, 0x61, 0x75, 0x5f, 0x63, 0x69, 0x74, 0x79, 0x5f, 0x62,
	0x61, 0x6e, 0x6b, 0x2e, 0x41, 0x6c, 0x61, 0x74, 0x61, 0x75, 0x43, 0x69, 0x74, 0x79, 0x43, 0x6f,
	0x6e, 0x66, 0x69, 0x72, 0x6d, 0x54, 0x68, 0x72, 0x65, 0x65, 0x44, 0x53, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x2e, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x72, 0x6d, 0x54, 0x68, 0x72, 0x65, 0x65,
	0x44, 0x53, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x52, 0x04, 0x62, 0x6f, 0x64, 0x79, 0x1a,
	0x5b, 0x0a, 0x15, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x72, 0x6d, 0x54, 0x68, 0x72, 0x65, 0x65, 0x44,
	0x53, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x72, 0x65, 0x73,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x63, 0x72, 0x65, 0x73, 0x12, 0x2e, 0x0a, 0x12,
	0x74, 0x68, 0x72, 0x65, 0x65, 0x44, 0x53, 0x53, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x44, 0x61,
	0x74, 0x61, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x12, 0x74, 0x68, 0x72, 0x65, 0x65, 0x44,
	0x53, 0x53, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x44, 0x61, 0x74, 0x61, 0x22, 0x3e, 0x0a, 0x20,
	0x41, 0x6c, 0x61, 0x74, 0x61, 0x75, 0x43, 0x69, 0x74, 0x79, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x72,
	0x6d, 0x54, 0x68, 0x72, 0x65, 0x65, 0x44, 0x53, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x12, 0x1a, 0x0a, 0x08, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x08, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0xf1, 0x01, 0x0a,
	0x1e, 0x41, 0x6c, 0x61, 0x74, 0x61, 0x75, 0x43, 0x69, 0x74, 0x79, 0x52, 0x65, 0x73, 0x75, 0x6d,
	0x65, 0x54, 0x68, 0x72, 0x65, 0x65, 0x44, 0x53, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12,
	0x59, 0x0a, 0x04, 0x62, 0x6f, 0x64, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x45, 0x2e,
	0x61, 0x6c, 0x61, 0x74, 0x61, 0x75, 0x5f, 0x63, 0x69, 0x74, 0x79, 0x5f, 0x62, 0x61, 0x6e, 0x6b,
	0x2e, 0x41, 0x6c, 0x61, 0x74, 0x61, 0x75, 0x43, 0x69, 0x74, 0x79, 0x52, 0x65, 0x73, 0x75, 0x6d,
	0x65, 0x54, 0x68, 0x72, 0x65, 0x65, 0x44, 0x53, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e,
	0x52, 0x65, 0x73, 0x75, 0x6d, 0x65, 0x54, 0x68, 0x72, 0x65, 0x65, 0x44, 0x53, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x52, 0x04, 0x62, 0x6f, 0x64, 0x79, 0x1a, 0x74, 0x0a, 0x14, 0x52, 0x65,
	0x73, 0x75, 0x6d, 0x65, 0x54, 0x68, 0x72, 0x65, 0x65, 0x44, 0x53, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x12, 0x2c, 0x0a, 0x11, 0x74, 0x68, 0x72, 0x65, 0x65, 0x44, 0x53, 0x4d, 0x65, 0x74,
	0x68, 0x6f, 0x64, 0x44, 0x61, 0x74, 0x61, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x11, 0x74,
	0x68, 0x72, 0x65, 0x65, 0x44, 0x53, 0x4d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x44, 0x61, 0x74, 0x61,
	0x12, 0x2e, 0x0a, 0x12, 0x74, 0x68, 0x72, 0x65, 0x65, 0x44, 0x53, 0x4d, 0x65, 0x74, 0x68, 0x6f,
	0x64, 0x53, 0x74, 0x61, 0x74, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x12, 0x74, 0x68,
	0x72, 0x65, 0x65, 0x44, 0x53, 0x4d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x53, 0x74, 0x61, 0x74, 0x65,
	0x22, 0x3d, 0x0a, 0x1f, 0x41, 0x6c, 0x61, 0x74, 0x61, 0x75, 0x43, 0x69, 0x74, 0x79, 0x52, 0x65,
	0x73, 0x75, 0x6d, 0x65, 0x54, 0x68, 0x72, 0x65, 0x65, 0x44, 0x53, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22,
	0xad, 0x01, 0x0a, 0x19, 0x41, 0x6c, 0x61, 0x74, 0x61, 0x75, 0x43, 0x69, 0x74, 0x79, 0x53, 0x65,
	0x6e, 0x64, 0x46, 0x6f, 0x72, 0x6d, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x4f, 0x0a,
	0x04, 0x62, 0x6f, 0x64, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x3b, 0x2e, 0x61, 0x6c,
	0x61, 0x74, 0x61, 0x75, 0x5f, 0x63, 0x69, 0x74, 0x79, 0x5f, 0x62, 0x61, 0x6e, 0x6b, 0x2e, 0x41,
	0x6c, 0x61, 0x74, 0x61, 0x75, 0x43, 0x69, 0x74, 0x79, 0x53, 0x65, 0x6e, 0x64, 0x46, 0x6f, 0x72,
	0x6d, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x53, 0x65, 0x6e, 0x64, 0x46, 0x6f, 0x72,
	0x6d, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x52, 0x04, 0x62, 0x6f, 0x64, 0x79, 0x1a, 0x3f,
	0x0a, 0x0f, 0x53, 0x65, 0x6e, 0x64, 0x46, 0x6f, 0x72, 0x6d, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x12, 0x2c, 0x0a, 0x11, 0x74, 0x68, 0x72, 0x65, 0x65, 0x44, 0x53, 0x4d, 0x65, 0x74, 0x68,
	0x6f, 0x64, 0x44, 0x61, 0x74, 0x61, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x11, 0x74, 0x68,
	0x72, 0x65, 0x65, 0x44, 0x53, 0x4d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x44, 0x61, 0x74, 0x61, 0x22,
	0x38, 0x0a, 0x1a, 0x41, 0x6c, 0x61, 0x74, 0x61, 0x75, 0x43, 0x69, 0x74, 0x79, 0x53, 0x65, 0x6e,
	0x64, 0x46, 0x6f, 0x72, 0x6d, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x1a, 0x0a,
	0x08, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x08, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x97, 0x02, 0x0a, 0x1e, 0x41, 0x6c,
	0x61, 0x74, 0x61, 0x75, 0x43, 0x69, 0x74, 0x79, 0x43, 0x61, 0x6e, 0x63, 0x65, 0x6c, 0x50, 0x61,
	0x79, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x59, 0x0a, 0x04,
	0x62, 0x6f, 0x64, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x45, 0x2e, 0x61, 0x6c, 0x61,
	0x74, 0x61, 0x75, 0x5f, 0x63, 0x69, 0x74, 0x79, 0x5f, 0x62, 0x61, 0x6e, 0x6b, 0x2e, 0x41, 0x6c,
	0x61, 0x74, 0x61, 0x75, 0x43, 0x69, 0x74, 0x79, 0x43, 0x61, 0x6e, 0x63, 0x65, 0x6c, 0x50, 0x61,
	0x79, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x43, 0x61, 0x6e,
	0x63, 0x65, 0x6c, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x52, 0x04, 0x62, 0x6f, 0x64, 0x79, 0x1a, 0x99, 0x01, 0x0a, 0x14, 0x43, 0x61, 0x6e, 0x63,
	0x65, 0x6c, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x12, 0x14, 0x0a, 0x05, 0x4f, 0x52, 0x44, 0x45, 0x52, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x05, 0x4f, 0x52, 0x44, 0x45, 0x52, 0x12, 0x1a, 0x0a, 0x08, 0x4d, 0x45, 0x52, 0x43, 0x48, 0x41,
	0x4e, 0x54, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x4d, 0x45, 0x52, 0x43, 0x48, 0x41,
	0x4e, 0x54, 0x12, 0x15, 0x0a, 0x06, 0x50, 0x5f, 0x53, 0x49, 0x47, 0x4e, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x05, 0x50, 0x53, 0x49, 0x47, 0x4e, 0x12, 0x1d, 0x0a, 0x0a, 0x52, 0x45, 0x56,
	0x5f, 0x41, 0x4d, 0x4f, 0x55, 0x4e, 0x54, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x52,
	0x45, 0x56, 0x41, 0x4d, 0x4f, 0x55, 0x4e, 0x54, 0x12, 0x19, 0x0a, 0x08, 0x52, 0x45, 0x56, 0x5f,
	0x44, 0x45, 0x53, 0x43, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x52, 0x45, 0x56, 0x44,
	0x45, 0x53, 0x43, 0x22, 0x95, 0x05, 0x0a, 0x1f, 0x41, 0x6c, 0x61, 0x74, 0x61, 0x75, 0x43, 0x69,
	0x74, 0x79, 0x43, 0x61, 0x6e, 0x63, 0x65, 0x6c, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x70, 0x0a, 0x0f, 0x63, 0x61, 0x6e, 0x63, 0x65,
	0x6c, 0x5f, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x47, 0x2e, 0x61, 0x6c, 0x61, 0x74, 0x61, 0x75, 0x5f, 0x63, 0x69, 0x74, 0x79, 0x5f, 0x62,
	0x61, 0x6e, 0x6b, 0x2e, 0x41, 0x6c, 0x61, 0x74, 0x61, 0x75, 0x43, 0x69, 0x74, 0x79, 0x43, 0x61,
	0x6e, 0x63, 0x65, 0x6c, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x2e, 0x43, 0x61, 0x6e, 0x63, 0x65, 0x6c, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e,
	0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x52, 0x0e, 0x63, 0x61, 0x6e, 0x63, 0x65,
	0x6c, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x1a, 0x99, 0x01, 0x0a, 0x06, 0x52, 0x65,
	0x73, 0x75, 0x6c, 0x74, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x12, 0x20, 0x0a, 0x0b, 0x64, 0x65, 0x73, 0x63,
	0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x64,
	0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x59, 0x0a, 0x09, 0x6f, 0x70,
	0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x3b, 0x2e,
	0x61, 0x6c, 0x61, 0x74, 0x61, 0x75, 0x5f, 0x63, 0x69, 0x74, 0x79, 0x5f, 0x62, 0x61, 0x6e, 0x6b,
	0x2e, 0x41, 0x6c, 0x61, 0x74, 0x61, 0x75, 0x43, 0x69, 0x74, 0x79, 0x43, 0x61, 0x6e, 0x63, 0x65,
	0x6c, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x2e, 0x4f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x09, 0x6f, 0x70, 0x65, 0x72,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x1a, 0x69, 0x0a, 0x15, 0x43, 0x61, 0x6e, 0x63, 0x65, 0x6c, 0x50,
	0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x50,
	0x0a, 0x06, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x38,
	0x2e, 0x61, 0x6c, 0x61, 0x74, 0x61, 0x75, 0x5f, 0x63, 0x69, 0x74, 0x79, 0x5f, 0x62, 0x61, 0x6e,
	0x6b, 0x2e, 0x41, 0x6c, 0x61, 0x74, 0x61, 0x75, 0x43, 0x69, 0x74, 0x79, 0x43, 0x61, 0x6e, 0x63,
	0x65, 0x6c, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x2e, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x52, 0x06, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74,
	0x1a, 0xf8, 0x01, 0x0a, 0x09, 0x4f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x16,
	0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06,
	0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x1f, 0x0a, 0x0b, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74,
	0x5f, 0x64, 0x65, 0x73, 0x63, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x72, 0x65, 0x73,
	0x75, 0x6c, 0x74, 0x44, 0x65, 0x73, 0x63, 0x12, 0x16, 0x0a, 0x06, 0x72, 0x65, 0x73, 0x75, 0x6c,
	0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12,
	0x0e, 0x0a, 0x02, 0x72, 0x63, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x72, 0x63, 0x12,
	0x14, 0x0a, 0x05, 0x65, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05,
	0x65, 0x63, 0x6f, 0x64, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x65, 0x64, 0x65, 0x73, 0x63, 0x18, 0x06,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x65, 0x64, 0x65, 0x73, 0x63, 0x12, 0x16, 0x0a, 0x06, 0x61,
	0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x61, 0x6d, 0x6f,
	0x75, 0x6e, 0x74, 0x12, 0x10, 0x0a, 0x03, 0x72, 0x72, 0x6e, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x03, 0x72, 0x72, 0x6e, 0x12, 0x19, 0x0a, 0x08, 0x72, 0x65, 0x76, 0x5f, 0x64, 0x65, 0x73,
	0x63, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x72, 0x65, 0x76, 0x44, 0x65, 0x73, 0x63,
	0x12, 0x19, 0x0a, 0x08, 0x72, 0x65, 0x76, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x18, 0x0a, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x07, 0x72, 0x65, 0x76, 0x44, 0x61, 0x74, 0x65, 0x22, 0xad, 0x03, 0x0a, 0x1e,
	0x41, 0x6c, 0x61, 0x74, 0x61, 0x75, 0x43, 0x69, 0x74, 0x79, 0x52, 0x65, 0x67, 0x69, 0x73, 0x74,
	0x65, 0x72, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x59,
	0x0a, 0x04, 0x62, 0x6f, 0x64, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x45, 0x2e, 0x61,
	0x6c, 0x61, 0x74, 0x61, 0x75, 0x5f, 0x63, 0x69, 0x74, 0x79, 0x5f, 0x62, 0x61, 0x6e, 0x6b, 0x2e,
	0x41, 0x6c, 0x61, 0x74, 0x61, 0x75, 0x43, 0x69, 0x74, 0x79, 0x52, 0x65, 0x67, 0x69, 0x73, 0x74,
	0x65, 0x72, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x52,
	0x65, 0x67, 0x69, 0x73, 0x74, 0x65, 0x72, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x52, 0x04, 0x62, 0x6f, 0x64, 0x79, 0x1a, 0xaf, 0x02, 0x0a, 0x14, 0x52, 0x65,
	0x67, 0x69, 0x73, 0x74, 0x65, 0x72, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x12, 0x14, 0x0a, 0x05, 0x4f, 0x52, 0x44, 0x45, 0x52, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x05, 0x4f, 0x52, 0x44, 0x45, 0x52, 0x12, 0x1a, 0x0a, 0x08, 0x4d, 0x45, 0x52, 0x43,
	0x48, 0x41, 0x4e, 0x54, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x4d, 0x45, 0x52, 0x43,
	0x48, 0x41, 0x4e, 0x54, 0x12, 0x1a, 0x0a, 0x08, 0x54, 0x45, 0x52, 0x4d, 0x49, 0x4e, 0x41, 0x4c,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x54, 0x45, 0x52, 0x4d, 0x49, 0x4e, 0x41, 0x4c,
	0x12, 0x1b, 0x0a, 0x09, 0x43, 0x4c, 0x49, 0x45, 0x4e, 0x54, 0x5f, 0x49, 0x44, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x08, 0x43, 0x4c, 0x49, 0x45, 0x4e, 0x54, 0x49, 0x44, 0x12, 0x20, 0x0a,
	0x07, 0x63, 0x72, 0x64, 0x5f, 0x70, 0x61, 0x6e, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07,
	0x9a, 0xfc, 0xeb, 0x8e, 0x02, 0x01, 0x64, 0x52, 0x06, 0x63, 0x72, 0x64, 0x50, 0x61, 0x6e, 0x12,
	0x20, 0x0a, 0x07, 0x63, 0x72, 0x64, 0x5f, 0x65, 0x78, 0x70, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09,
	0x42, 0x07, 0x9a, 0xfc, 0xeb, 0x8e, 0x02, 0x01, 0x64, 0x52, 0x06, 0x63, 0x72, 0x64, 0x45, 0x78,
	0x70, 0x12, 0x20, 0x0a, 0x07, 0x63, 0x72, 0x64, 0x5f, 0x63, 0x76, 0x63, 0x18, 0x07, 0x20, 0x01,
	0x28, 0x09, 0x42, 0x07, 0x9a, 0xfc, 0xeb, 0x8e, 0x02, 0x01, 0x64, 0x52, 0x06, 0x63, 0x72, 0x64,
	0x43, 0x76, 0x63, 0x12, 0x12, 0x0a, 0x04, 0x4e, 0x41, 0x4d, 0x45, 0x18, 0x08, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x04, 0x4e, 0x41, 0x4d, 0x45, 0x12, 0x15, 0x0a, 0x06, 0x50, 0x5f, 0x53, 0x49, 0x47,
	0x4e, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x50, 0x53, 0x49, 0x47, 0x4e, 0x12, 0x1b,
	0x0a, 0x09, 0x54, 0x4f, 0x4b, 0x45, 0x4e, 0x5f, 0x43, 0x4d, 0x44, 0x18, 0x0a, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x08, 0x54, 0x4f, 0x4b, 0x45, 0x4e, 0x43, 0x4d, 0x44, 0x22, 0xd2, 0x02, 0x0a, 0x1f,
	0x41, 0x6c, 0x61, 0x74, 0x61, 0x75, 0x43, 0x69, 0x74, 0x79, 0x52, 0x65, 0x67, 0x69, 0x73, 0x74,
	0x65, 0x72, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12,
	0x6e, 0x0a, 0x0e, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x5f, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x47, 0x2e, 0x61, 0x6c, 0x61, 0x74, 0x61, 0x75,
	0x5f, 0x63, 0x69, 0x74, 0x79, 0x5f, 0x62, 0x61, 0x6e, 0x6b, 0x2e, 0x41, 0x6c, 0x61, 0x74, 0x61,
	0x75, 0x43, 0x69, 0x74, 0x79, 0x52, 0x65, 0x67, 0x69, 0x73, 0x74, 0x65, 0x72, 0x54, 0x6f, 0x6b,
	0x65, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x52, 0x65, 0x67, 0x69, 0x73,
	0x74, 0x65, 0x72, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x52, 0x0d, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x1a,
	0x69, 0x0a, 0x15, 0x52, 0x65, 0x67, 0x69, 0x73, 0x74, 0x65, 0x72, 0x54, 0x6f, 0x6b, 0x65, 0x6e,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x50, 0x0a, 0x06, 0x72, 0x65, 0x73, 0x75,
	0x6c, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x38, 0x2e, 0x61, 0x6c, 0x61, 0x74, 0x61,
	0x75, 0x5f, 0x63, 0x69, 0x74, 0x79, 0x5f, 0x62, 0x61, 0x6e, 0x6b, 0x2e, 0x41, 0x6c, 0x61, 0x74,
	0x61, 0x75, 0x43, 0x69, 0x74, 0x79, 0x52, 0x65, 0x67, 0x69, 0x73, 0x74, 0x65, 0x72, 0x54, 0x6f,
	0x6b, 0x65, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x52, 0x65, 0x73, 0x75,
	0x6c, 0x74, 0x52, 0x06, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x1a, 0x54, 0x0a, 0x06, 0x52, 0x65,
	0x73, 0x75, 0x6c, 0x74, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x12, 0x20, 0x0a, 0x0b, 0x64, 0x65, 0x73, 0x63,
	0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x64,
	0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x14, 0x0a, 0x05, 0x74, 0x6f,
	0x6b, 0x65, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x74, 0x6f, 0x6b, 0x65, 0x6e,
	0x22, 0xf3, 0x01, 0x0a, 0x17, 0x41, 0x6c, 0x61, 0x74, 0x61, 0x75, 0x43, 0x69, 0x74, 0x79, 0x43,
	0x68, 0x61, 0x72, 0x67, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x4b, 0x0a, 0x04,
	0x62, 0x6f, 0x64, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x37, 0x2e, 0x61, 0x6c, 0x61,
	0x74, 0x61, 0x75, 0x5f, 0x63, 0x69, 0x74, 0x79, 0x5f, 0x62, 0x61, 0x6e, 0x6b, 0x2e, 0x41, 0x6c,
	0x61, 0x74, 0x61, 0x75, 0x43, 0x69, 0x74, 0x79, 0x43, 0x68, 0x61, 0x72, 0x67, 0x65, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x43, 0x68, 0x61, 0x72, 0x67, 0x65, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x52, 0x04, 0x62, 0x6f, 0x64, 0x79, 0x1a, 0x8a, 0x01, 0x0a, 0x0d, 0x43, 0x68,
	0x61, 0x72, 0x67, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x18, 0x0a, 0x07, 0x41,
	0x50, 0x50, 0x52, 0x4f, 0x56, 0x45, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x41, 0x50,
	0x50, 0x52, 0x4f, 0x56, 0x45, 0x12, 0x14, 0x0a, 0x05, 0x4f, 0x52, 0x44, 0x45, 0x52, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x4f, 0x52, 0x44, 0x45, 0x52, 0x12, 0x16, 0x0a, 0x06, 0x41,
	0x4d, 0x4f, 0x55, 0x4e, 0x54, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x41, 0x4d, 0x4f,
	0x55, 0x4e, 0x54, 0x12, 0x1a, 0x0a, 0x08, 0x4d, 0x45, 0x52, 0x43, 0x48, 0x41, 0x4e, 0x54, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x4d, 0x45, 0x52, 0x43, 0x48, 0x41, 0x4e, 0x54, 0x12,
	0x15, 0x0a, 0x06, 0x50, 0x5f, 0x53, 0x49, 0x47, 0x4e, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x05, 0x50, 0x53, 0x49, 0x47, 0x4e, 0x22, 0x9b, 0x02, 0x0a, 0x18, 0x41, 0x6c, 0x61, 0x74, 0x61,
	0x75, 0x43, 0x69, 0x74, 0x79, 0x43, 0x68, 0x61, 0x72, 0x67, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x12, 0x62, 0x0a, 0x0f, 0x63, 0x68, 0x61, 0x72, 0x67, 0x65, 0x5f, 0x72, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x39, 0x2e, 0x61,
	0x6c, 0x61, 0x74, 0x61, 0x75, 0x5f, 0x63, 0x69, 0x74, 0x79, 0x5f, 0x62, 0x61, 0x6e, 0x6b, 0x2e,
	0x41, 0x6c, 0x61, 0x74, 0x61, 0x75, 0x43, 0x69, 0x74, 0x79, 0x43, 0x68, 0x61, 0x72, 0x67, 0x65,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x43, 0x68, 0x61, 0x72, 0x67, 0x65, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x52, 0x0e, 0x63, 0x68, 0x61, 0x72, 0x67, 0x65, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x1a, 0x5b, 0x0a, 0x0e, 0x43, 0x68, 0x61, 0x72, 0x67,
	0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x49, 0x0a, 0x06, 0x72, 0x65, 0x73,
	0x75, 0x6c, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x31, 0x2e, 0x61, 0x6c, 0x61, 0x74,
	0x61, 0x75, 0x5f, 0x63, 0x69, 0x74, 0x79, 0x5f, 0x62, 0x61, 0x6e, 0x6b, 0x2e, 0x41, 0x6c, 0x61,
	0x74, 0x61, 0x75, 0x43, 0x69, 0x74, 0x79, 0x43, 0x68, 0x61, 0x72, 0x67, 0x65, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x52, 0x06, 0x72, 0x65,
	0x73, 0x75, 0x6c, 0x74, 0x1a, 0x3e, 0x0a, 0x06, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x12,
	0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x63, 0x6f,
	0x64, 0x65, 0x12, 0x20, 0x0a, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f,
	0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70,
	0x74, 0x69, 0x6f, 0x6e, 0x32, 0xa3, 0x0c, 0x0a, 0x13, 0x41, 0x6c, 0x61, 0x74, 0x61, 0x75, 0x43,
	0x69, 0x74, 0x79, 0x42, 0x61, 0x6e, 0x6b, 0x50, 0x61, 0x79, 0x49, 0x6e, 0x12, 0x8c, 0x01, 0x0a,
	0x0c, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x50, 0x61, 0x79, 0x49, 0x6e, 0x12, 0x2f, 0x2e,
	0x61, 0x6c, 0x61, 0x74, 0x61, 0x75, 0x5f, 0x63, 0x69, 0x74, 0x79, 0x5f, 0x62, 0x61, 0x6e, 0x6b,
	0x2e, 0x41, 0x6c, 0x61, 0x74, 0x61, 0x75, 0x43, 0x69, 0x74, 0x79, 0x50, 0x72, 0x6f, 0x63, 0x65,
	0x73, 0x73, 0x50, 0x61, 0x79, 0x49, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x30,
	0x2e, 0x61, 0x6c, 0x61, 0x74, 0x61, 0x75, 0x5f, 0x63, 0x69, 0x74, 0x79, 0x5f, 0x62, 0x61, 0x6e,
	0x6b, 0x2e, 0x41, 0x6c, 0x61, 0x74, 0x61, 0x75, 0x43, 0x69, 0x74, 0x79, 0x50, 0x72, 0x6f, 0x63,
	0x65, 0x73, 0x73, 0x50, 0x61, 0x79, 0x49, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x22, 0x19, 0xf2, 0x8e, 0xec, 0x8e, 0x02, 0x13, 0x08, 0x02, 0x10, 0x00, 0x18, 0x05, 0x2a, 0x09,
	0x2f, 0x65, 0x63, 0x6f, 0x6d, 0x2f, 0x61, 0x70, 0x69, 0x40, 0x3c, 0x12, 0x9e, 0x01, 0x0a, 0x12,
	0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x4e, 0x6f, 0x41, 0x63, 0x63, 0x65, 0x70, 0x74, 0x50,
	0x61, 0x79, 0x12, 0x35, 0x2e, 0x61, 0x6c, 0x61, 0x74, 0x61, 0x75, 0x5f, 0x63, 0x69, 0x74, 0x79,
	0x5f, 0x62, 0x61, 0x6e, 0x6b, 0x2e, 0x41, 0x6c, 0x61, 0x74, 0x61, 0x75, 0x43, 0x69, 0x74, 0x79,
	0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x4e, 0x6f, 0x41, 0x63, 0x63, 0x65, 0x70, 0x74, 0x50,
	0x61, 0x79, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x36, 0x2e, 0x61, 0x6c, 0x61, 0x74,
	0x61, 0x75, 0x5f, 0x63, 0x69, 0x74, 0x79, 0x5f, 0x62, 0x61, 0x6e, 0x6b, 0x2e, 0x41, 0x6c, 0x61,
	0x74, 0x61, 0x75, 0x43, 0x69, 0x74, 0x79, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x4e, 0x6f,
	0x41, 0x63, 0x63, 0x65, 0x70, 0x74, 0x50, 0x61, 0x79, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x22, 0x19, 0xf2, 0x8e, 0xec, 0x8e, 0x02, 0x13, 0x08, 0x02, 0x10, 0x00, 0x18, 0x05, 0x2a,
	0x09, 0x2f, 0x65, 0x63, 0x6f, 0x6d, 0x2f, 0x61, 0x70, 0x69, 0x40, 0x3c, 0x12, 0x8f, 0x01, 0x0a,
	0x09, 0x47, 0x65, 0x74, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x31, 0x2e, 0x61, 0x6c, 0x61,
	0x74, 0x61, 0x75, 0x5f, 0x63, 0x69, 0x74, 0x79, 0x5f, 0x62, 0x61, 0x6e, 0x6b, 0x2e, 0x41, 0x6c,
	0x61, 0x74, 0x61, 0x75, 0x43, 0x69, 0x74, 0x79, 0x47, 0x65, 0x74, 0x53, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x50, 0x61, 0x79, 0x49, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x32, 0x2e,
	0x61, 0x6c, 0x61, 0x74, 0x61, 0x75, 0x5f, 0x63, 0x69, 0x74, 0x79, 0x5f, 0x62, 0x61, 0x6e, 0x6b,
	0x2e, 0x41, 0x6c, 0x61, 0x74, 0x61, 0x75, 0x43, 0x69, 0x74, 0x79, 0x47, 0x65, 0x74, 0x53, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x50, 0x61, 0x79, 0x49, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x22, 0x1b, 0xf2, 0x8e, 0xec, 0x8e, 0x02, 0x15, 0x08, 0x02, 0x10, 0x00, 0x18, 0x05, 0x20,
	0x03, 0x2a, 0x09, 0x2f, 0x65, 0x63, 0x6f, 0x6d, 0x2f, 0x61, 0x70, 0x69, 0x40, 0x3c, 0x12, 0x8b,
	0x01, 0x0a, 0x0e, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x72, 0x6d, 0x54, 0x68, 0x72, 0x65, 0x65, 0x44,
	0x53, 0x12, 0x31, 0x2e, 0x61, 0x6c, 0x61, 0x74, 0x61, 0x75, 0x5f, 0x63, 0x69, 0x74, 0x79, 0x5f,
	0x62, 0x61, 0x6e, 0x6b, 0x2e, 0x41, 0x6c, 0x61, 0x74, 0x61, 0x75, 0x43, 0x69, 0x74, 0x79, 0x43,
	0x6f, 0x6e, 0x66, 0x69, 0x72, 0x6d, 0x54, 0x68, 0x72, 0x65, 0x65, 0x44, 0x53, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x1a, 0x32, 0x2e, 0x61, 0x6c, 0x61, 0x74, 0x61, 0x75, 0x5f, 0x63, 0x69,
	0x74, 0x79, 0x5f, 0x62, 0x61, 0x6e, 0x6b, 0x2e, 0x41, 0x6c, 0x61, 0x74, 0x61, 0x75, 0x43, 0x69,
	0x74, 0x79, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x72, 0x6d, 0x54, 0x68, 0x72, 0x65, 0x65, 0x44, 0x53,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x12, 0xf2, 0x8e, 0xec, 0x8e, 0x02, 0x0c,
	0x08, 0x02, 0x10, 0x00, 0x18, 0x05, 0x20, 0x00, 0x2a, 0x00, 0x40, 0x3c, 0x12, 0x88, 0x01, 0x0a,
	0x0d, 0x52, 0x65, 0x73, 0x75, 0x6d, 0x65, 0x54, 0x68, 0x72, 0x65, 0x65, 0x44, 0x53, 0x12, 0x30,
	0x2e, 0x61, 0x6c, 0x61, 0x74, 0x61, 0x75, 0x5f, 0x63, 0x69, 0x74, 0x79, 0x5f, 0x62, 0x61, 0x6e,
	0x6b, 0x2e, 0x41, 0x6c, 0x61, 0x74, 0x61, 0x75, 0x43, 0x69, 0x74, 0x79, 0x52, 0x65, 0x73, 0x75,
	0x6d, 0x65, 0x54, 0x68, 0x72, 0x65, 0x65, 0x44, 0x53, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x1a, 0x31, 0x2e, 0x61, 0x6c, 0x61, 0x74, 0x61, 0x75, 0x5f, 0x63, 0x69, 0x74, 0x79, 0x5f, 0x62,
	0x61, 0x6e, 0x6b, 0x2e, 0x41, 0x6c, 0x61, 0x74, 0x61, 0x75, 0x43, 0x69, 0x74, 0x79, 0x52, 0x65,
	0x73, 0x75, 0x6d, 0x65, 0x54, 0x68, 0x72, 0x65, 0x65, 0x44, 0x53, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x22, 0x12, 0xf2, 0x8e, 0xec, 0x8e, 0x02, 0x0c, 0x08, 0x02, 0x10, 0x00, 0x18,
	0x05, 0x20, 0x00, 0x2a, 0x00, 0x40, 0x3c, 0x12, 0x79, 0x0a, 0x08, 0x53, 0x65, 0x6e, 0x64, 0x46,
	0x6f, 0x72, 0x6d, 0x12, 0x2b, 0x2e, 0x61, 0x6c, 0x61, 0x74, 0x61, 0x75, 0x5f, 0x63, 0x69, 0x74,
	0x79, 0x5f, 0x62, 0x61, 0x6e, 0x6b, 0x2e, 0x41, 0x6c, 0x61, 0x74, 0x61, 0x75, 0x43, 0x69, 0x74,
	0x79, 0x53, 0x65, 0x6e, 0x64, 0x46, 0x6f, 0x72, 0x6d, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x1a, 0x2c, 0x2e, 0x61, 0x6c, 0x61, 0x74, 0x61, 0x75, 0x5f, 0x63, 0x69, 0x74, 0x79, 0x5f, 0x62,
	0x61, 0x6e, 0x6b, 0x2e, 0x41, 0x6c, 0x61, 0x74, 0x61, 0x75, 0x43, 0x69, 0x74, 0x79, 0x53, 0x65,
	0x6e, 0x64, 0x46, 0x6f, 0x72, 0x6d, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x12,
	0xf2, 0x8e, 0xec, 0x8e, 0x02, 0x0c, 0x08, 0x02, 0x10, 0x00, 0x18, 0x05, 0x20, 0x00, 0x2a, 0x00,
	0x40, 0x3c, 0x12, 0x91, 0x01, 0x0a, 0x0d, 0x43, 0x61, 0x6e, 0x63, 0x65, 0x6c, 0x50, 0x61, 0x79,
	0x6d, 0x65, 0x6e, 0x74, 0x12, 0x30, 0x2e, 0x61, 0x6c, 0x61, 0x74, 0x61, 0x75, 0x5f, 0x63, 0x69,
	0x74, 0x79, 0x5f, 0x62, 0x61, 0x6e, 0x6b, 0x2e, 0x41, 0x6c, 0x61, 0x74, 0x61, 0x75, 0x43, 0x69,
	0x74, 0x79, 0x43, 0x61, 0x6e, 0x63, 0x65, 0x6c, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x31, 0x2e, 0x61, 0x6c, 0x61, 0x74, 0x61, 0x75, 0x5f,
	0x63, 0x69, 0x74, 0x79, 0x5f, 0x62, 0x61, 0x6e, 0x6b, 0x2e, 0x41, 0x6c, 0x61, 0x74, 0x61, 0x75,
	0x43, 0x69, 0x74, 0x79, 0x43, 0x61, 0x6e, 0x63, 0x65, 0x6c, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e,
	0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x1b, 0xf2, 0x8e, 0xec, 0x8e, 0x02,
	0x15, 0x08, 0x02, 0x10, 0x00, 0x18, 0x05, 0x20, 0x03, 0x2a, 0x09, 0x2f, 0x65, 0x63, 0x6f, 0x6d,
	0x2f, 0x61, 0x70, 0x69, 0x40, 0x3c, 0x12, 0x91, 0x01, 0x0a, 0x0d, 0x52, 0x65, 0x67, 0x69, 0x73,
	0x74, 0x65, 0x72, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x12, 0x30, 0x2e, 0x61, 0x6c, 0x61, 0x74, 0x61,
	0x75, 0x5f, 0x63, 0x69, 0x74, 0x79, 0x5f, 0x62, 0x61, 0x6e, 0x6b, 0x2e, 0x41, 0x6c, 0x61, 0x74,
	0x61, 0x75, 0x43, 0x69, 0x74, 0x79, 0x52, 0x65, 0x67, 0x69, 0x73, 0x74, 0x65, 0x72, 0x54, 0x6f,
	0x6b, 0x65, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x31, 0x2e, 0x61, 0x6c, 0x61,
	0x74, 0x61, 0x75, 0x5f, 0x63, 0x69, 0x74, 0x79, 0x5f, 0x62, 0x61, 0x6e, 0x6b, 0x2e, 0x41, 0x6c,
	0x61, 0x74, 0x61, 0x75, 0x43, 0x69, 0x74, 0x79, 0x52, 0x65, 0x67, 0x69, 0x73, 0x74, 0x65, 0x72,
	0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x1b, 0xf2,
	0x8e, 0xec, 0x8e, 0x02, 0x15, 0x08, 0x02, 0x10, 0x00, 0x18, 0x05, 0x20, 0x03, 0x2a, 0x09, 0x2f,
	0x65, 0x63, 0x6f, 0x6d, 0x2f, 0x61, 0x70, 0x69, 0x40, 0x3c, 0x12, 0x7c, 0x0a, 0x06, 0x43, 0x68,
	0x61, 0x72, 0x67, 0x65, 0x12, 0x29, 0x2e, 0x61, 0x6c, 0x61, 0x74, 0x61, 0x75, 0x5f, 0x63, 0x69,
	0x74, 0x79, 0x5f, 0x62, 0x61, 0x6e, 0x6b, 0x2e, 0x41, 0x6c, 0x61, 0x74, 0x61, 0x75, 0x43, 0x69,
	0x74, 0x79, 0x43, 0x68, 0x61, 0x72, 0x67, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a,
	0x2a, 0x2e, 0x61, 0x6c, 0x61, 0x74, 0x61, 0x75, 0x5f, 0x63, 0x69, 0x74, 0x79, 0x5f, 0x62, 0x61,
	0x6e, 0x6b, 0x2e, 0x41, 0x6c, 0x61, 0x74, 0x61, 0x75, 0x43, 0x69, 0x74, 0x79, 0x43, 0x68, 0x61,
	0x72, 0x67, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x1b, 0xf2, 0x8e, 0xec,
	0x8e, 0x02, 0x15, 0x08, 0x02, 0x10, 0x00, 0x18, 0x05, 0x20, 0x03, 0x2a, 0x09, 0x2f, 0x65, 0x63,
	0x6f, 0x6d, 0x2f, 0x61, 0x70, 0x69, 0x40, 0x3c, 0x1a, 0x8f, 0x02, 0xea, 0x8e, 0xec, 0x8e, 0x02,
	0x88, 0x02, 0x0a, 0x2c, 0x0a, 0x05, 0x6c, 0x6f, 0x63, 0x61, 0x6c, 0x12, 0x23, 0x68, 0x74, 0x74,
	0x70, 0x73, 0x3a, 0x2f, 0x2f, 0x6a, 0x70, 0x61, 0x79, 0x2d, 0x74, 0x65, 0x73, 0x74, 0x2e, 0x61,
	0x6c, 0x61, 0x74, 0x61, 0x75, 0x63, 0x69, 0x74, 0x79, 0x62, 0x61, 0x6e, 0x6b, 0x2e, 0x6b, 0x7a,
	0x0a, 0x2a, 0x0a, 0x03, 0x64, 0x65, 0x76, 0x12, 0x23, 0x68, 0x74, 0x74, 0x70, 0x73, 0x3a, 0x2f,
	0x2f, 0x6a, 0x70, 0x61, 0x79, 0x2d, 0x74, 0x65, 0x73, 0x74, 0x2e, 0x61, 0x6c, 0x61, 0x74, 0x61,
	0x75, 0x63, 0x69, 0x74, 0x79, 0x62, 0x61, 0x6e, 0x6b, 0x2e, 0x6b, 0x7a, 0x0a, 0x26, 0x0a, 0x04,
	0x74, 0x65, 0x73, 0x74, 0x12, 0x1e, 0x68, 0x74, 0x74, 0x70, 0x73, 0x3a, 0x2f, 0x2f, 0x6a, 0x70,
	0x61, 0x79, 0x2e, 0x61, 0x6c, 0x61, 0x74, 0x61, 0x75, 0x63, 0x69, 0x74, 0x79, 0x62, 0x61, 0x6e,
	0x6b, 0x2e, 0x6b, 0x7a, 0x0a, 0x27, 0x0a, 0x05, 0x73, 0x74, 0x61, 0x67, 0x65, 0x12, 0x1e, 0x68,
	0x74, 0x74, 0x70, 0x73, 0x3a, 0x2f, 0x2f, 0x6a, 0x70, 0x61, 0x79, 0x2e, 0x61, 0x6c, 0x61, 0x74,
	0x61, 0x75, 0x63, 0x69, 0x74, 0x79, 0x62, 0x61, 0x6e, 0x6b, 0x2e, 0x6b, 0x7a, 0x0a, 0x2b, 0x0a,
	0x04, 0x70, 0x72, 0x6f, 0x64, 0x12, 0x23, 0x68, 0x74, 0x74, 0x70, 0x73, 0x3a, 0x2f, 0x2f, 0x6a,
	0x70, 0x61, 0x79, 0x2d, 0x74, 0x65, 0x73, 0x74, 0x2e, 0x61, 0x6c, 0x61, 0x74, 0x61, 0x75, 0x63,
	0x69, 0x74, 0x79, 0x62, 0x61, 0x6e, 0x6b, 0x2e, 0x6b, 0x7a, 0x0a, 0x2e, 0x0a, 0x07, 0x73, 0x61,
	0x6e, 0x64, 0x62, 0x6f, 0x78, 0x12, 0x23, 0x68, 0x74, 0x74, 0x70, 0x73, 0x3a, 0x2f, 0x2f, 0x6a,
	0x70, 0x61, 0x79, 0x2d, 0x74, 0x65, 0x73, 0x74, 0x2e, 0x61, 0x6c, 0x61, 0x74, 0x61, 0x75, 0x63,
	0x69, 0x74, 0x79, 0x62, 0x61, 0x6e, 0x6b, 0x2e, 0x6b, 0x7a, 0x42, 0x2d, 0x5a, 0x2b, 0x67, 0x69,
	0x74, 0x2e, 0x6c, 0x6f, 0x63, 0x61, 0x6c, 0x2f, 0x73, 0x65, 0x6e, 0x73, 0x69, 0x74, 0x69, 0x76,
	0x65, 0x2f, 0x69, 0x6e, 0x6e, 0x65, 0x72, 0x70, 0x62, 0x2f, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73,
	0x73, 0x69, 0x6e, 0x67, 0x2f, 0x72, 0x65, 0x73, 0x74, 0x62, 0x08, 0x65, 0x64, 0x69, 0x74, 0x69,
	0x6f, 0x6e, 0x73, 0x70, 0xe8, 0x07,
})

var (
	file_inner_processing_rest_alatau_city_payin_proto_rawDescOnce sync.Once
	file_inner_processing_rest_alatau_city_payin_proto_rawDescData []byte
)

func file_inner_processing_rest_alatau_city_payin_proto_rawDescGZIP() []byte {
	file_inner_processing_rest_alatau_city_payin_proto_rawDescOnce.Do(func() {
		file_inner_processing_rest_alatau_city_payin_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_inner_processing_rest_alatau_city_payin_proto_rawDesc), len(file_inner_processing_rest_alatau_city_payin_proto_rawDesc)))
	})
	return file_inner_processing_rest_alatau_city_payin_proto_rawDescData
}

var file_inner_processing_rest_alatau_city_payin_proto_msgTypes = make([]protoimpl.MessageInfo, 38)
var file_inner_processing_rest_alatau_city_payin_proto_goTypes = []any{
	(*AlatauCityProcessPayInRequest)(nil),                         // 0: alatau_city_bank.AlatauCityProcessPayInRequest
	(*AlatauCityPayInRequest)(nil),                                // 1: alatau_city_bank.AlatauCityPayInRequest
	(*AlatauCityProcessPayInResponse)(nil),                        // 2: alatau_city_bank.AlatauCityProcessPayInResponse
	(*AlatauCityGetStatusPayInRequest)(nil),                       // 3: alatau_city_bank.AlatauCityGetStatusPayInRequest
	(*AlatauCityGetStatusPayInResponse)(nil),                      // 4: alatau_city_bank.AlatauCityGetStatusPayInResponse
	(*AlatauCityProcessNoAcceptPayRequest)(nil),                   // 5: alatau_city_bank.AlatauCityProcessNoAcceptPayRequest
	(*AlatauCityProcessNoAcceptPayResponse)(nil),                  // 6: alatau_city_bank.AlatauCityProcessNoAcceptPayResponse
	(*AlatauCityConfirmThreeDSRequest)(nil),                       // 7: alatau_city_bank.AlatauCityConfirmThreeDSRequest
	(*AlatauCityConfirmThreeDSResponse)(nil),                      // 8: alatau_city_bank.AlatauCityConfirmThreeDSResponse
	(*AlatauCityResumeThreeDSRequest)(nil),                        // 9: alatau_city_bank.AlatauCityResumeThreeDSRequest
	(*AlatauCityResumeThreeDSResponse)(nil),                       // 10: alatau_city_bank.AlatauCityResumeThreeDSResponse
	(*AlatauCitySendFormRequest)(nil),                             // 11: alatau_city_bank.AlatauCitySendFormRequest
	(*AlatauCitySendFormResponse)(nil),                            // 12: alatau_city_bank.AlatauCitySendFormResponse
	(*AlatauCityCancelPaymentRequest)(nil),                        // 13: alatau_city_bank.AlatauCityCancelPaymentRequest
	(*AlatauCityCancelPaymentResponse)(nil),                       // 14: alatau_city_bank.AlatauCityCancelPaymentResponse
	(*AlatauCityRegisterTokenRequest)(nil),                        // 15: alatau_city_bank.AlatauCityRegisterTokenRequest
	(*AlatauCityRegisterTokenResponse)(nil),                       // 16: alatau_city_bank.AlatauCityRegisterTokenResponse
	(*AlatauCityChargeRequest)(nil),                               // 17: alatau_city_bank.AlatauCityChargeRequest
	(*AlatauCityChargeResponse)(nil),                              // 18: alatau_city_bank.AlatauCityChargeResponse
	(*AlatauCityGetStatusPayInRequest_GetStatusRequest)(nil),      // 19: alatau_city_bank.AlatauCityGetStatusPayInRequest.GetStatusRequest
	(*AlatauCityGetStatusPayInResponse_StatusResponse)(nil),       // 20: alatau_city_bank.AlatauCityGetStatusPayInResponse.StatusResponse
	(*AlatauCityGetStatusPayInResponse_Result)(nil),               // 21: alatau_city_bank.AlatauCityGetStatusPayInResponse.Result
	(*AlatauCityGetStatusPayInResponse_Operation)(nil),            // 22: alatau_city_bank.AlatauCityGetStatusPayInResponse.Operation
	(*AlatauCityGetStatusPayInResponse_Operation_Refunds)(nil),    // 23: alatau_city_bank.AlatauCityGetStatusPayInResponse.Operation.Refunds
	(*AlatauCityGetStatusPayInResponse_Operation_Rec)(nil),        // 24: alatau_city_bank.AlatauCityGetStatusPayInResponse.Operation.Rec
	(*AlatauCityConfirmThreeDSRequest_ConfirmThreeDSRequest)(nil), // 25: alatau_city_bank.AlatauCityConfirmThreeDSRequest.ConfirmThreeDSRequest
	(*AlatauCityResumeThreeDSRequest_ResumeThreeDSRequest)(nil),   // 26: alatau_city_bank.AlatauCityResumeThreeDSRequest.ResumeThreeDSRequest
	(*AlatauCitySendFormRequest_SendFormRequest)(nil),             // 27: alatau_city_bank.AlatauCitySendFormRequest.SendFormRequest
	(*AlatauCityCancelPaymentRequest_CancelPaymentRequest)(nil),   // 28: alatau_city_bank.AlatauCityCancelPaymentRequest.CancelPaymentRequest
	(*AlatauCityCancelPaymentResponse_Result)(nil),                // 29: alatau_city_bank.AlatauCityCancelPaymentResponse.Result
	(*AlatauCityCancelPaymentResponse_CancelPaymentResponse)(nil), // 30: alatau_city_bank.AlatauCityCancelPaymentResponse.CancelPaymentResponse
	(*AlatauCityCancelPaymentResponse_Operation)(nil),             // 31: alatau_city_bank.AlatauCityCancelPaymentResponse.Operation
	(*AlatauCityRegisterTokenRequest_RegisterTokenRequest)(nil),   // 32: alatau_city_bank.AlatauCityRegisterTokenRequest.RegisterTokenRequest
	(*AlatauCityRegisterTokenResponse_RegisterTokenResponse)(nil), // 33: alatau_city_bank.AlatauCityRegisterTokenResponse.RegisterTokenResponse
	(*AlatauCityRegisterTokenResponse_Result)(nil),                // 34: alatau_city_bank.AlatauCityRegisterTokenResponse.Result
	(*AlatauCityChargeRequest_ChargeRequest)(nil),                 // 35: alatau_city_bank.AlatauCityChargeRequest.ChargeRequest
	(*AlatauCityChargeResponse_ChargeResponse)(nil),               // 36: alatau_city_bank.AlatauCityChargeResponse.ChargeResponse
	(*AlatauCityChargeResponse_Result)(nil),                       // 37: alatau_city_bank.AlatauCityChargeResponse.Result
}
var file_inner_processing_rest_alatau_city_payin_proto_depIdxs = []int32{
	1,  // 0: alatau_city_bank.AlatauCityProcessPayInRequest.body:type_name -> alatau_city_bank.AlatauCityPayInRequest
	19, // 1: alatau_city_bank.AlatauCityGetStatusPayInRequest.body:type_name -> alatau_city_bank.AlatauCityGetStatusPayInRequest.GetStatusRequest
	20, // 2: alatau_city_bank.AlatauCityGetStatusPayInResponse.status_response:type_name -> alatau_city_bank.AlatauCityGetStatusPayInResponse.StatusResponse
	1,  // 3: alatau_city_bank.AlatauCityProcessNoAcceptPayRequest.body:type_name -> alatau_city_bank.AlatauCityPayInRequest
	25, // 4: alatau_city_bank.AlatauCityConfirmThreeDSRequest.body:type_name -> alatau_city_bank.AlatauCityConfirmThreeDSRequest.ConfirmThreeDSRequest
	26, // 5: alatau_city_bank.AlatauCityResumeThreeDSRequest.body:type_name -> alatau_city_bank.AlatauCityResumeThreeDSRequest.ResumeThreeDSRequest
	27, // 6: alatau_city_bank.AlatauCitySendFormRequest.body:type_name -> alatau_city_bank.AlatauCitySendFormRequest.SendFormRequest
	28, // 7: alatau_city_bank.AlatauCityCancelPaymentRequest.body:type_name -> alatau_city_bank.AlatauCityCancelPaymentRequest.CancelPaymentRequest
	30, // 8: alatau_city_bank.AlatauCityCancelPaymentResponse.cancel_response:type_name -> alatau_city_bank.AlatauCityCancelPaymentResponse.CancelPaymentResponse
	32, // 9: alatau_city_bank.AlatauCityRegisterTokenRequest.body:type_name -> alatau_city_bank.AlatauCityRegisterTokenRequest.RegisterTokenRequest
	33, // 10: alatau_city_bank.AlatauCityRegisterTokenResponse.token_response:type_name -> alatau_city_bank.AlatauCityRegisterTokenResponse.RegisterTokenResponse
	35, // 11: alatau_city_bank.AlatauCityChargeRequest.body:type_name -> alatau_city_bank.AlatauCityChargeRequest.ChargeRequest
	36, // 12: alatau_city_bank.AlatauCityChargeResponse.charge_response:type_name -> alatau_city_bank.AlatauCityChargeResponse.ChargeResponse
	21, // 13: alatau_city_bank.AlatauCityGetStatusPayInResponse.StatusResponse.result:type_name -> alatau_city_bank.AlatauCityGetStatusPayInResponse.Result
	22, // 14: alatau_city_bank.AlatauCityGetStatusPayInResponse.Result.operation:type_name -> alatau_city_bank.AlatauCityGetStatusPayInResponse.Operation
	23, // 15: alatau_city_bank.AlatauCityGetStatusPayInResponse.Operation.refunds:type_name -> alatau_city_bank.AlatauCityGetStatusPayInResponse.Operation.Refunds
	24, // 16: alatau_city_bank.AlatauCityGetStatusPayInResponse.Operation.Refunds.rec:type_name -> alatau_city_bank.AlatauCityGetStatusPayInResponse.Operation.Rec
	31, // 17: alatau_city_bank.AlatauCityCancelPaymentResponse.Result.operation:type_name -> alatau_city_bank.AlatauCityCancelPaymentResponse.Operation
	29, // 18: alatau_city_bank.AlatauCityCancelPaymentResponse.CancelPaymentResponse.result:type_name -> alatau_city_bank.AlatauCityCancelPaymentResponse.Result
	34, // 19: alatau_city_bank.AlatauCityRegisterTokenResponse.RegisterTokenResponse.result:type_name -> alatau_city_bank.AlatauCityRegisterTokenResponse.Result
	37, // 20: alatau_city_bank.AlatauCityChargeResponse.ChargeResponse.result:type_name -> alatau_city_bank.AlatauCityChargeResponse.Result
	0,  // 21: alatau_city_bank.AlatauCityBankPayIn.ProcessPayIn:input_type -> alatau_city_bank.AlatauCityProcessPayInRequest
	5,  // 22: alatau_city_bank.AlatauCityBankPayIn.ProcessNoAcceptPay:input_type -> alatau_city_bank.AlatauCityProcessNoAcceptPayRequest
	3,  // 23: alatau_city_bank.AlatauCityBankPayIn.GetStatus:input_type -> alatau_city_bank.AlatauCityGetStatusPayInRequest
	7,  // 24: alatau_city_bank.AlatauCityBankPayIn.ConfirmThreeDS:input_type -> alatau_city_bank.AlatauCityConfirmThreeDSRequest
	9,  // 25: alatau_city_bank.AlatauCityBankPayIn.ResumeThreeDS:input_type -> alatau_city_bank.AlatauCityResumeThreeDSRequest
	11, // 26: alatau_city_bank.AlatauCityBankPayIn.SendForm:input_type -> alatau_city_bank.AlatauCitySendFormRequest
	13, // 27: alatau_city_bank.AlatauCityBankPayIn.CancelPayment:input_type -> alatau_city_bank.AlatauCityCancelPaymentRequest
	15, // 28: alatau_city_bank.AlatauCityBankPayIn.RegisterToken:input_type -> alatau_city_bank.AlatauCityRegisterTokenRequest
	17, // 29: alatau_city_bank.AlatauCityBankPayIn.Charge:input_type -> alatau_city_bank.AlatauCityChargeRequest
	2,  // 30: alatau_city_bank.AlatauCityBankPayIn.ProcessPayIn:output_type -> alatau_city_bank.AlatauCityProcessPayInResponse
	6,  // 31: alatau_city_bank.AlatauCityBankPayIn.ProcessNoAcceptPay:output_type -> alatau_city_bank.AlatauCityProcessNoAcceptPayResponse
	4,  // 32: alatau_city_bank.AlatauCityBankPayIn.GetStatus:output_type -> alatau_city_bank.AlatauCityGetStatusPayInResponse
	8,  // 33: alatau_city_bank.AlatauCityBankPayIn.ConfirmThreeDS:output_type -> alatau_city_bank.AlatauCityConfirmThreeDSResponse
	10, // 34: alatau_city_bank.AlatauCityBankPayIn.ResumeThreeDS:output_type -> alatau_city_bank.AlatauCityResumeThreeDSResponse
	12, // 35: alatau_city_bank.AlatauCityBankPayIn.SendForm:output_type -> alatau_city_bank.AlatauCitySendFormResponse
	14, // 36: alatau_city_bank.AlatauCityBankPayIn.CancelPayment:output_type -> alatau_city_bank.AlatauCityCancelPaymentResponse
	16, // 37: alatau_city_bank.AlatauCityBankPayIn.RegisterToken:output_type -> alatau_city_bank.AlatauCityRegisterTokenResponse
	18, // 38: alatau_city_bank.AlatauCityBankPayIn.Charge:output_type -> alatau_city_bank.AlatauCityChargeResponse
	30, // [30:39] is the sub-list for method output_type
	21, // [21:30] is the sub-list for method input_type
	21, // [21:21] is the sub-list for extension type_name
	21, // [21:21] is the sub-list for extension extendee
	0,  // [0:21] is the sub-list for field type_name
}

func init() { file_inner_processing_rest_alatau_city_payin_proto_init() }
func file_inner_processing_rest_alatau_city_payin_proto_init() {
	if File_inner_processing_rest_alatau_city_payin_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_inner_processing_rest_alatau_city_payin_proto_rawDesc), len(file_inner_processing_rest_alatau_city_payin_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   38,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_inner_processing_rest_alatau_city_payin_proto_goTypes,
		DependencyIndexes: file_inner_processing_rest_alatau_city_payin_proto_depIdxs,
		MessageInfos:      file_inner_processing_rest_alatau_city_payin_proto_msgTypes,
	}.Build()
	File_inner_processing_rest_alatau_city_payin_proto = out.File
	file_inner_processing_rest_alatau_city_payin_proto_goTypes = nil
	file_inner_processing_rest_alatau_city_payin_proto_depIdxs = nil
}
