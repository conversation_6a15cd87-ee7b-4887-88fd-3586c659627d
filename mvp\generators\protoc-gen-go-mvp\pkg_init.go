package main

import (
	"google.golang.org/protobuf/compiler/protogen"
)

func printStand(g *protogen.GeneratedFile, stand, funcName string) {
	g.P("// Stand")

	g.P("switch ", stand, " {")
	g.P("case \"", STAND_LOCAL, "\",",
		"\"", STAND_DEVELOPMENT, "\",",
		"\"", STAND_STAGE, "\",",
		"\"", STAND_SANDBOX, "\",",
		"\"", STAND_PRODUCTION, "\",",
		"\"", STAND_TEST, "\":")
	g.P("default:")
	g.P("panic(", fmtErrorf, "(\"unexpected ENVIRONMENT in .env: %s\", stand))")
	g.P("}")
	g.P()
}

func printVault(g *protogen.GeneratedFile, funcName string) {
	g.P("// Vault")

	g.P("user := ", osGetEnv, "(\"VAULT_USER\")")
	g.P("password := ", osGetEnv, "(\"VAULT_PASSWORD\")")

	g.P("vaultConfig := ", vaultDefaultConfig, "()")
	g.P("vaultConfig.Address = \"http://vault-ui.vault.svc.cluster.local:8200\"")
	g.P("for _, o := range opts { o(vaultConfig) }")
	g.P("vaultClient, err := ", vaultNewClient, "(vaultConfig)")
	g.P("if err != nil { panic(err) }")
	g.P()

	g.P("if stand == \"", STAND_LOCAL, "\" {")
	g.P("secret, err := vaultClient.Logical().Write(fmt.Sprintf(\"auth/userpass/login/%s\", user), map[string]any{")
	g.P("\"password\": password,")
	g.P("})")

	g.P("if err != nil { ")
	g.P("secret, err = vaultClient.Logical().Write(fmt.Sprintf(\"auth/ldap/login/%s\",user), map[string]any{")
	g.P("\"password\": password,")
	g.P("})")
	g.P("if err != nil { panic(err) }")
	g.P("}")

	g.P("if secret.Auth == nil {")
	g.P("panic(fmt.Errorf(\"nil auth\"))")
	g.P("}")

	g.P("vaultClient.SetToken(secret.Auth.ClientToken)")
	g.P("serviceSecret, err := vaultClient.KVv2(namespace).Get(", contextBackgroundIdent, "(), microservice)")
	g.P("if err != nil { panic(err) }")
	g.P("pass = serviceSecret.Data")

	g.P("} else {")
	g.P("k8sAuth, err := ", vaultNewKubernetesAuth, "(")
	g.P("namespace,")
	g.P(vaultWithServiceAccountTokenPath, "(\"/var/run/secrets/kubernetes.io/serviceaccount/token\"),")
	g.P(")")
	g.P("if err != nil { panic(err) }")

	g.P("_, err = vaultClient.Auth().Login(", contextBackgroundIdent, "(), k8sAuth)")
	g.P("if err != nil { panic(err) }")

	g.P("serviceSecret, err := vaultClient.KVv2(namespace).Get(", contextBackgroundIdent, "(), microservice)")
	g.P("if err != nil { panic(err) }")
	g.P("pass = serviceSecret.Data")

	g.P("}")
}

func printLogger(g *protogen.GeneratedFile) {
	g.P("// Logger")

	g.P("zapConfig := ", zapNewProductionConfig, "()")

	g.P("parseLevel, err := ", zapcoreParseLevel, "(level)")
	g.P("if err != nil { panic(err) }")

	g.P("zapConfig.Level = zap.NewAtomicLevelAt(parseLevel)")
	g.P("zapConfig.DisableStacktrace = true")
	g.P("zapConfig.EncoderConfig.TimeKey = \"@timestamp\"")
	g.P("zapConfig.EncoderConfig.EncodeTime = ", zapcoreRFC3339TimeEncoder)
	g.P("zapConfig.EncoderConfig.CallerKey = \"file\"")

	g.P("log, err := zapConfig.Build(", zapAddCallerSkip, "(1))")
	g.P("if err != nil { panic(err) }")

	g.P("log = log.Named(appName)")

	g.P(zapReplaceGlobals, "(log)")
	g.P()
}

func printMongo(g *protogen.GeneratedFile) {
	g.P("// Mongo")

	g.P("dsn := sc.", MONGO_KEY)
	g.P("mongoOpts := ", mongoOptionsClient, "().ApplyURI(dsn)")
	g.P("mongoOpts.Monitor = ", mongoOtelNewMonitor, "()")

	g.P("connectCtx, cancel := ", contextWithTimeoutIdent, "(", contextBackgroundIdent, "(), 10 * ", timePackageSecond, ")")
	g.P("defer cancel()")

	g.P("mongoClient, err := ", mongoConnect, "(connectCtx, mongoOpts)")
	g.P("if err != nil { panic(\"mongo: \" + err.Error()) }")

	g.P("err = mongoClient.Ping(", contextBackgroundIdent, "(), nil)")
	g.P("if err != nil { panic(\"mongo: \" + err.Error()) }")
	g.P()
}

func printRedis(g *protogen.GeneratedFile) {
	g.P("// Redis")

	g.P("redisAddr := sc.", REDIS_DSN_KEY)
	g.P("redisPassword := sc.", REDIS_PASSWORD_KEY)

	g.P("var network string")
	g.P("if ", stringsHasPrefix, "(redisAddr, \"/\") {")
	g.P("} else {")
	g.P("network = \"tcp\"")
	g.P("}")

	g.P("redisClient := ", redisNewClient, "(&", redisOptions, "{")
	g.P("Addr: redisAddr,")
	g.P("Password: redisPassword,")
	g.P("Network: network,")
	g.P("Dialer: func(ctx ", contextContext, ", network, addr string) (", netConn, ", error) {")
	g.P("return ", netDial, "(network, addr)")
	g.P("},")
	g.P("})")

	g.P("_, err = redisClient.Ping(", contextBackgroundIdent, "()).Result()")
	g.P("if err != nil { panic(err) }")

	g.P("if err = ", redisOtelInstrumentTracing, "(redisClient); err != nil { panic (err) }")

	g.P()
}

func printNotifier(g *protogen.GeneratedFile) {
	g.P("// Notifier")

	g.P("ctx, cancel := context.WithCancel(context.TODO())")
	g.P()
	g.P("go func() {")
	g.P("defer cancel()")

	g.P("quit := make(chan ", osSignal, ", 1)")
	g.P(signalNotify, "(quit, ", syscallSigterm, ", ", syscallSigint, ")")

	g.P("select {")
	g.P("case <-ctx.Done():")
	g.P("case <-quit: ")
	g.P("}")

	g.P("}()")
	g.P()
}

func printJaegerTraceProvider(g *protogen.GeneratedFile, funcName string) {
	g.P("// Jaeger")

	g.P("url := sc.", JAEGER_DSN)

	g.P("if stand == \"", STAND_LOCAL, "\" {")
	g.P("url = \"http://host.docker.internal:14268/api/traces\"")
	g.P("}")

	g.P("exp, err := ", jaegerNew, "(", jaegerWithCollectorEndpoint, "(", jaegerWithEndpoint, "(url)))")
	g.P("if err != nil { panic(err) }")

	g.P("tp := ", sdktraceNewTracerProvider, "(")
	g.P(sdktraceWithBatcher, "(exp),")
	g.P(sdktraceWithResource, "(", resourceNewWithAttributes, "(")
	g.P(semconvSchemaURL, ",")
	g.P(semconvServiceNameKey, ".String(appName),")
	g.P(semconvDeploymentEnvironmentKey, ".String(stand),")
	g.P(")),")
	g.P(sdktraceWithSpanProcessor, "(", sentryotelNewSentrySpanProcessor, "()),")
	g.P(")")

	g.P(otelSetTracerProvider, "(tp)")
	g.P(otelSetTextMapPropagator, "(")
	g.P(propagationNewCompositeTextMapPropagator, "(")
	g.P(sentryotelNewSentryPropagator, "(),")
	g.P("&", propagationTraceContext, "{},")
	g.P("&", propagationBaggage, "{},")
	g.P("),")
	g.P(")")
	g.P()
}

func printSentry(g *protogen.GeneratedFile) {
	g.P("// Sentry")

	g.P("dsnSentry := sc.", SENTRY_DSN)
	g.P("if dsnSentry == \"\" {")
	g.P("panic(", fmtErrorf, "(\"sentry dsn is empty\"))")
	g.P("}")

	g.P("err = ", sentryInit, "(", sentryClientOptions, "{")
	g.P("Dsn:              dsnSentry,")
	g.P("AttachStacktrace: true,")
	g.P("ServerName:       microservice,")
	g.P("Environment:      stand,")
	g.P("EnableTracing:    true,")
	g.P("TracesSampleRate: 1.0,")
	g.P("})")

	g.P("if err != nil { panic(err) }")
	g.P()
}

func printHealthCheckStore(g *protogen.GeneratedFile, funcName string) {
	g.P("// HealthCheckStore")

	g.P("healthCheckStore := newHealthCheckStore", funcName, "()")
	g.P()

	g.P("go func(s *healthCheckStore", funcName, ", ctx ", contextContext, ") {")
	g.P("timer := ", timeNewTimer, "(s.timeOut)")
	g.P("defer timer.Stop()")
	g.P("for {")
	g.P("select {")
	g.P("case <-ctx.Done():")
	g.P("return")
	g.P("case <-timer.C:")
	g.P("}")
	g.P("func() {")
	g.P("var errorSlice []error")
	g.P("if s == nil { return }")
	g.P("s.mu.RLock()")
	g.P("checks := make([]check", funcName, ", len(s.checks))")
	g.P("copy(checks, s.checks)")
	g.P("s.mu.RUnlock()")
	g.P("for _, check := range checks {")
	g.P("errorSlice = append(errorSlice, check(ctx))")
	g.P("}")
	g.P("s.mu.Lock()")
	g.P("s.err = ", errorsPackageJoin, "(errorSlice...)")
	g.P("s.mu.Unlock()")
	g.P("}()")
	g.P("timer.Reset(s.timeOut)")
	g.P("}")
	g.P("}(healthCheckStore, ctx)")
	g.P()

	g.P("func(s *healthCheckStore", funcName, ") {")
	g.P("if s == nil { return }")
	g.P("s.mu.Lock()")
	g.P("defer s.mu.Unlock()")
	g.P("s.checks = append(s.checks, func(_ context.Context) error {")
	g.P("select {")
	g.P("case <-ctx.Done():")
	g.P("return ctx.Err()")
	g.P("default:")
	g.P("return nil")
	g.P("}")
	g.P("})")
	g.P("}(healthCheckStore)")
	g.P()
}

func printGin(g *protogen.GeneratedFile, funcName string) {
	g.P("// GIN ")

	g.P("ignoreEndpoint := func(endpoint string) bool {")
	g.P("for _, item := range [4]string{")
	g.P("\"", PrometheusEndPoint, "\",")
	g.P("\"", PingEndPoint, "\",")
	g.P("\"", LivenessEndPoint, "\",")
	g.P("\"", ReadinessEndPoint, "\",")
	g.P("} {")
	g.P("if item == endpoint {")
	g.P("return true")
	g.P("}")
	g.P("}")
	g.P("return false")
	g.P("}")
	g.P("ginEngine := ", ginNew, "()")
	g.P("ginEngine.ContextWithFallback = true")
	g.P("ginEngine.Use(")
	g.P("sentrygin.New(sentrygin.Options{")
	g.P("Repanic:         true,")
	g.P("WaitForDelivery: true,")
	g.P("Timeout:         5 * time.Second,")
	g.P("}),")
	g.P("otelgin.Middleware(microservice, otelgin.WithFilter(")
	g.P("func(g *", httpPackageRequest, ") bool {")
	g.P("return !ignoreEndpoint(g.URL.Path)")
	g.P("},")
	g.P(")),")
	g.P("func(c *", ginContext, ") {")
	g.P("var requestBody []byte")
	g.P("if c.Request.Body != nil {")
	g.P("requestBody, _ = ", ioPackageReadAll, "(c.Request.Body)")
	g.P("c.Request.Body = ", ioPackageNopCloser, "(", bytesPackageNewBuffer, "(requestBody))")
	g.P("}")
	g.P("defer func() {")
	g.P("if err := recover(); err != nil {")
	g.P("httpRequest, _ := ", httputilPackageDumpRequest, "(c.Request, true)")
	g.P()
	g.P(ctxZapError, "(c.Request.Context(), \"[recovery from panic]\",")
	g.P(zapString, "(\"error\", ", fmtSprintf, "(\"%v\", err)),")
	g.P(zapString, "(\"stack_trace\", string(", runtimeDebugStack, "())),")
	g.P(zapString, "(\"request\", string(httpRequest)),")
	g.P(zapString, "(\"request_body\", string(requestBody)),")
	g.P(zapTime, "(\"time\", time.Now()),")
	g.P(")")
	g.P("c.JSON(", httpPackageStatusInternalServerError, ", gin.H{")
	g.P("\"status\":      false,")
	g.P("\"status_code\": ", httpPackageStatusInternalServerError, ",")
	g.P("\"message\":     \"Internal Server Error\",")
	g.P("\"result\":      struct{}{},")
	g.P("})")
	g.P("c.Abort()")
	g.P("}")
	g.P("}()")
	g.P("c.Next()")
	g.P("},")
	g.P("func(c *", ginContext, ") {")
	g.P("ginCtx := ", contextWithValue, "(c.Request.Context(),\"", UserIPCTX, "\", c.Request.Header.Get(\"X-Forwarded-For\"))")
	g.P("c.Request = c.Request.WithContext(ginCtx)")
	g.P("c.Next()")
	g.P("},")
	g.P(")")
	g.P("ginEngine.Use(func(c *", ginContext, ") {")
	g.P("c.Request = c.Request.WithContext(")
	g.P(ctxZapToContext, "(")
	g.P(cntxNewCounter, "(c.Request.Context()),")
	g.P("zap.L().Named(c.Request.URL.String()),")
	g.P("),")
	g.P("	)")
	g.P("span := ", traceSpanFromContext, "(c.Request.Context())")
	g.P("traceId := span.SpanContext().TraceID().String()")
	g.P("", ctxZapAddFields, "(c.Request.Context(), ", zapString, "(\"", TraceIdCTX, "\", traceId))")
	g.P("crw := &customResponseWriter", funcName, "{")
	g.P("ResponseWriter: c.Writer,")
	g.P("Body:           ", bytesPackageNewBufferString, "(\"\"),")
	g.P("}")
	g.P("c.Writer = crw")
	g.P("c.Next()")
	g.P("status := c.Writer.Status()")
	g.P("if status < ", httpPackageStatusBadRequest, " {")
	g.P("if ignoreEndpoint(c.Request.URL.Path) {")
	g.P("return")
	g.P("}")
	g.P(ctxZapDebug, "(c.Request.Context(), \"correct_ended\",")
	g.P(zapString, "(\"response_body\", crw.Body.String()),")
	g.P(")")
	g.P("} else {")
	g.P("var reqBody []byte")
	g.P("if c.Request.Body != nil {")
	g.P("reqBody, _ = ", ioPackageReadAll, "(c.Request.Body)")
	g.P("c.Request.Body = ", ioPackageNopCloser, "(", bytesPackageNewBuffer, "(reqBody))")
	g.P("}")
	g.P(ctxZapError, "(c.Request.Context(), \"wrong_ended\",")
	g.P(zapString, "(\"response_body\", crw.Body.String()),")
	g.P(zapString, "(\"request_body\", string(reqBody)),")
	g.P(")")
	g.P("}")
	g.P("})")
	g.P("httpMetrics := ", promHttpHandlerFor, "(registry, ", promHttpHandlerOpts, "{})")
	g.P("ginEngine.GET(\"", PrometheusEndPoint, "\", func(c *", ginContext, ") {")
	g.P("httpMetrics.ServeHTTP(c.Writer, c.Request)")
	g.P("})")
	g.P("ginEngine.GET(\"", PingEndPoint, "\", func(c *", ginContext, ") {")
	g.P(ctxZapDebug, "(c, \"healthCheck_ping\")")
	g.P("c.JSON(", httpPackageStatusOK, ", map[string]string{\"message\": \"pong\"})")
	g.P("})")
	g.P("ginEngine.GET(\"", LivenessEndPoint, "\", func(c *", ginContext, ") {")
	g.P("if err := healthCheckStore.Status(); err != nil {")
	g.P(ctxZapError, "(c, \"healthCheck_liveness\", zap.Error(err))")
	g.P("c.JSON(", netHttpPackageStatusServiceUnavailable, ", map[string]string{\"message\": err.Error()})")
	g.P("} else {")
	g.P("c.JSON(", httpPackageStatusOK, ", map[string]string{\"message\": \"ok\"})")
	g.P("}")
	g.P("})")
	g.P("ginEngine.GET(\"", ReadinessEndPoint, "\", func(c *", ginContext, ") {")
	g.P("if err := healthCheckStore.Status(); err != nil {")
	g.P(ctxZapError, "(c, \"healthCheck_readiness\", zap.Error(err))")
	g.P("c.JSON(", netHttpPackageStatusServiceUnavailable, ", map[string]string{\"message\": err.Error()})")
	g.P("} else {")
	g.P("c.JSON(", httpPackageStatusOK, ", map[string]string{\"message\": \"ok\"})")
	g.P("}")
	g.P("})")

	g.P()
}

func printPrometheus(g *protogen.GeneratedFile) {
	g.P("// Prometheus")

	g.P("registry := ", prometheusNewRegistry, "()")
	g.P("registry.MustRegister(", prometheusNewGoCollector, "())")
	g.P("registry.MustRegister(", prometheusNewProcessCollector, "(", prometheusProcessCollectorOpts, "{}))")
	g.P("registry.MustRegister(", prometheusNewBuildInfoCollector, "())")

	g.P()
}

func printNatsJSClient(g *protogen.GeneratedFile, funcName string) {
	g.P("// NatsJSClient")

	g.P("natsConn, err := ", natsPkg.Ident("Connect"), "(sc.NATS_DSN, ", natsPkg.Ident("DrainTimeout"), "(10))")
	g.P("if err != nil {")
	g.P("panic(", fmtErrorf, "(\"error connecting to NATS: %w\", err))")
	g.P("}")

	g.P("jetStreamContext, err := natsConn.JetStream()")
	g.P("if err != nil {")
	g.P("panic(", fmtErrorf, "(\"error connect to JetStream: %w\", err))")
	g.P("}")

	g.P("middlewareLogger := newZapLoggerAdapter", funcName, "(log)")
	g.P("natsMessageRouter, err := ", watermillMessageNewRouter, "(", watermillMessageRouterConfig, "{}, middlewareLogger)")
	g.P("if err != nil {")
	g.P("panic(", fmtErrorf, "(\"error of create routing: %w\", err))")
	g.P("}")

	g.P("natsMessageRouter.AddMiddleware(")
	g.P(watermillMiddlewareRecoverer, ",")

	// Sentry middleware
	g.P("func(h ", watermillMessageHandlerFunc, ") ", watermillMessageHandlerFunc, " {")
	g.P("return func(msg *", watermillMessageMessage, ") ([]*", watermillMessageMessage, ", error) {")
	g.P("messages, err := h(msg)")
	g.P("if err == nil {")
	g.P("return messages, nil")
	g.P("}")
	g.P("msgCtx := msg.Context()")
	g.P("hub := ", sentryGetHubFromContext, "(msgCtx)")
	g.P("if hub == nil {")
	g.P("hub = ", sentryCurrentHub, "().Clone()")
	g.P("msgCtx = ", sentrySetHubOnContext, "(msgCtx, hub)")
	g.P("}")
	g.P("msg.SetContext(msgCtx)")
	g.P("defer func() {")
	g.P("if rcv := recover(); rcv != nil {")
	g.P("recoveredCtx := msg.Context()")
	g.P("if recoveredCtx != nil { ctx = recoveredCtx }")
	g.P("eventID := hub.RecoverWithContext(ctx, rcv)")
	g.P("if eventID != nil { hub.Flush(time.Second) }")
	g.P("panic(rcv)")
	g.P("}")
	g.P("}()")
	g.P("var eventMap map[string]any")
	g.P("extras := make(map[string]interface{})")
	g.P("if eventMap != nil {")
	g.P("extras[\"raw\"] = string(msg.Payload)")
	g.P("extras[\"event_uuid\"] = msg.UUID")
	g.P("}")
	g.P("return messages, err")
	g.P("}")
	g.P("},")

	// OTEL middleware
	g.P("func(h ", watermillMessageHandlerFunc, ") ", watermillMessageHandlerFunc, " {")
	g.P("return func(msg *", watermillMessageMessage, ") ([]*", watermillMessageMessage, ", error) {")
	g.P("parentCtx  := ", otelGetTextMapPropagator, "().Extract(msg.Context(), ", propagationMapCarrier, "(msg.Metadata))")
	g.P("handlerNameFromCtx := ", watermillMessageHandlerNameFromCtx, "(ctx)")
	g.P("tracedCtx, span := ", otelTracer, "(\"consumer\").Start(")
	g.P("parentCtx ,")
	g.P("handlerNameFromCtx,")
	g.P(traceWithSpanKind, "(", traceSpanKindConsumer, "),")
	g.P(")")
	g.P("defer span.End()")
	g.P("span.SetAttributes(")
	g.P(semconvMessagingDestinationKindTopic, ",")
	g.P(semconvMessagingDestinationKey, ".String(", watermillMessageSubscribeTopicFromCtx, "(tracedCtx)),")
	g.P(semconvMessagingOperationReceive, ",")
	g.P(")")
	g.P("msg.SetContext(tracedCtx)")
	g.P("messages, err := h(msg)")
	g.P("span.SetAttributes(")
	g.P(otelAttributeBool, "(\"error\", err != nil),")
	g.P(otelAttributeString, "(\"nats_uuid\", msg.UUID),")
	g.P(")")
	g.P("span.RecordError(err)")
	g.P("return messages, err")
	g.P("}")
	g.P("},")

	g.P(")")
	g.P()
}

func printGrpc(g *protogen.GeneratedFile, funcName string) {
	g.P("// GRPC")

	g.P("grpcMetrics := ", grpcpromPkgNewServerMetrics, "(")
	g.P(grpcpromPkgWithServerHandlingTimeHistogram, "(")
	g.P(grpcpromPkgWithHistogramBuckets, "([]float64{0.001, 0.01, 0.1, 0.3, 0.6, 1, 3, 6, 9, 20, 30, 60, 90, 120}),")
	g.P("),")
	g.P(")")
	g.P("registry.MustRegister(grpcMetrics)")

	g.P("spanProm := func(ctx ", contextContext, ") ", prometheusLabels, " {")
	g.P("if span := ", traceSpanContextFromContext, "(ctx); span.IsSampled() {")
	g.P("return ", prometheusLabels, "{\"", TraceIdCTX, "\": span.TraceID().String()}")
	g.P("}")
	g.P("return nil")
	g.P("}")

	g.P("unaryLogInterceptor := func(ctx ", contextContext, ", req any, info *", grpcPackageUnaryServerInfo,
		", handler ", grpcPackageUnaryHandler, ") (resp any, err error) {")
	g.P("ctx = ", ctxZapToContext, "(", cntxNewCounter, "(ctx), zap.L().Named(info.FullMethod))")
	g.P("span := ", traceSpanFromContext, "(ctx)")
	g.P(ctxZapAddFields, "(ctx, ", zapString, "(\"", TraceIdCTX, "\", span.SpanContext().TraceID().String()))")

	g.P("resp, err = handler(ctx, req)")

	g.P("if err != nil {")
	g.P(ctxZapError, "(ctx, \"error_ended\", ", zapError, "(err))")
	g.P("} else {")
	g.P(ctxZapDebug, "(ctx, \"correct_ended\")")
	g.P("}")

	g.P("return resp, err")
	g.P("}")

	// Опции сервера
	g.P("options := []", grpcPackageServerOption, "{")
	g.P(grpcPackageStatsHandler, "(", otelgrpcPackageNewServerHandler, "()),")
	g.P(grpcPackageChainUnaryInterceptor, "(")
	g.P(goErrPackageUnaryServerInterceptor, "(),")
	g.P("grpcMetrics.UnaryServerInterceptor(", grpcpromPkgWithExemplarFromContext, "(spanProm)),")
	g.P("),")
	g.P(grpcPackageUnaryInterceptor, "(", grpcMiddlewarePkgChainUnaryServer, "(")
	g.P(grpcTagsPkgUnaryServerInterceptor, "(),")
	g.P("unaryLogInterceptor,")
	g.P(grpcMiddlewareSentryPackageUnaryServerInterceptor, "(),")
	g.P(")),")
	g.P(grpcPackageStreamInterceptor, "(", grpcMiddlewarePkgChainStreamServer, "(")
	g.P(grpcTagsPkgStreamServerInterceptor, "(),")
	g.P(grpcMiddlewareSentryPackageStreamServerInterceptor, "(),")
	g.P(")),")
	g.P("}")
	g.P()
	g.P("grpcServer := ", grpcPackageNewServer, "(options...)")

	g.P()
}

func printWorkerRegistry(g *protogen.GeneratedFile) {
	g.P("// WorkerRegistry")

	g.P("cobra.RegisteredWorkers = make(map[string]cobra.Worker)")
	g.P()
}

func printGracefulShutdown(g *protogen.GeneratedFile) {
	g.P("// GracefulShutdown")

	g.P("go func() {")
	g.P("select {")
	g.P("case <-ctx.Done():")
	g.P("grpcServer.GracefulStop()")
	g.P("}")
	g.P("}()")

	g.P()
}

func printPostgresClient(g *protogen.GeneratedFile, funcName string) {
	g.P("// PostgresClient")

	g.P("if len(postgresWriteConnections) == 0 {")
	g.P("panic(\"postgresWriteConnections database connections is empty\")")
	g.P("}")
	g.P()
	g.P("gormDB, err := ", gormOpen, "(")
	g.P(gormPostgresNew, "(", gormPostgresConfig, "{")
	g.P("DSN:                  postgresWriteConnections[0],")
	g.P("PreferSimpleProtocol: true,")
	g.P("}),")
	g.P("&gorm.Config{},")
	g.P(")")
	g.P("if err != nil {")
	g.P("panic(err)")
	g.P("}")
	g.P()
	g.P("if len(sc.DATABASE_WRITE_DSN) > 1 || len(sc.DATABASE_READ_DSN) > 0 {")
	g.P("zap.L().Debug(\"using multiple replica-set\")")
	g.P()
	g.P("sc.DATABASE_WRITE_DSN = sc.DATABASE_WRITE_DSN[1:]")
	g.P("sourceConnections := make([]gorm.Dialector, len(sc.DATABASE_WRITE_DSN))")
	g.P("replicaConnections := make([]gorm.Dialector, len(sc.DATABASE_READ_DSN))")
	g.P()
	g.P("for i, sourceDSN := range sc.DATABASE_WRITE_DSN {")
	g.P("sourceConnections[i] = ", gormPostgresOpen, "(sourceDSN)")
	g.P("}")
	g.P()
	g.P("for i, replicaDSN := range sc.DATABASE_READ_DSN {")
	g.P("replicaConnections[i] = ", gormPostgresOpen, "(replicaDSN)")
	g.P("}")
	g.P()
	g.P("err = gormDB.Use(", gormDBResolverRegister, "(")
	g.P(gormDBResolverConfig, "{")
	g.P("Sources:  sourceConnections,")
	g.P("Replicas: replicaConnections,")
	g.P("Policy:   ", gormDBResolverRandomPolicy, "{},")
	g.P("},")
	g.P("))")
	g.P()
	g.P("if err != nil {")
	g.P("panic(err)")
	g.P("}")
	g.P("}")
	g.P()
	g.P("connection, err := gormDB.DB()")
	g.P("if err != nil {")
	g.P("panic(err)")
	g.P("}")
	g.P()
	g.P("connection.SetConnMaxLifetime(time.Hour)")
	g.P("connection.SetMaxIdleConns(", ConnectionsNum, ")")
	g.P("connection.SetMaxOpenConns(", ConnectionsNum, ")")
	g.P()
	g.P("if err = gormDB.Use(", gormTracingNewPlugin, "()); err != nil {")
	g.P("panic(err)")
	g.P("}")
	g.P()
}

func printZapLoggerAdapter(g *protogen.GeneratedFile, funcName string) {
	g.P("type zapLoggerAdapter", funcName, " struct {")
	g.P("	logger *", zapLogger)
	g.P("}")
	g.P()
	g.P("func newZapLoggerAdapter", funcName, "(logger *", zapLogger, ") *zapLoggerAdapter", funcName, " {")
	g.P("	return &zapLoggerAdapter", funcName, "{logger: logger}")
	g.P("}")
	g.P()
	g.P("func (z *zapLoggerAdapter", funcName, ") Error(msg string, err error, fields ", watermillLogFields, ") {")
	g.P("	z.logger.Error(msg, append(z.toZapFields(fields), ", zapError, "(err))...)")
	g.P("}")
	g.P()
	g.P("func (z *zapLoggerAdapter", funcName, ") Info(msg string, fields ", watermillLogFields, ") {")
	g.P("	z.logger.Info(msg, z.toZapFields(fields)...)")
	g.P("}")
	g.P()
	g.P("func (z *zapLoggerAdapter", funcName, ") Debug(msg string, fields ", watermillLogFields, ") {")
	g.P("	z.logger.Debug(msg, z.toZapFields(fields)...)")
	g.P("}")
	g.P()
	g.P("func (z *zapLoggerAdapter", funcName, ") Trace(msg string, fields ", watermillLogFields, ") {")
	g.P("	z.logger.Debug(msg, z.toZapFields(fields)...) // Trace maps to Debug")
	g.P("}")
	g.P()
	g.P("func (z *zapLoggerAdapter", funcName, ") With(fields ", watermillLogFields, ") ", watermillLoggerAdapter, " {")
	g.P("	newLogger := z.logger.With(z.toZapFields(fields)...)")
	g.P("	return &zapLoggerAdapter", funcName, "{logger: newLogger}")
	g.P("}")
	g.P()
	g.P("func (z *zapLoggerAdapter", funcName, ") toZapFields(fields ", watermillLogFields, ") []", zapField, " {")
	g.P("	zapFields := make([]", zapField, ", 0, len(fields))")
	g.P("	for k, v := range fields {")
	g.P("	zapFields = append(zapFields, ", zapAny, "(k, v))")
	g.P("}")
	g.P("	return zapFields")
	g.P("}")
	g.P()
}

func printCustomResponseWriter(g *protogen.GeneratedFile, funcName string) {
	g.P("type customResponseWriter", funcName, " struct {")
	g.P(ginResponseWriter)
	g.P("Body *", bytesPackageBuffer)
	g.P("}")
	g.P()
	g.P("func (w *customResponseWriter", funcName, ") Write(b []byte) (int, error) {")
	g.P("w.Body.Write(b)")
	g.P("return w.ResponseWriter.Write(b)")
	g.P("}")
	g.P()
}

func printHealthCheckStoreFunc(g *protogen.GeneratedFile, funcName string) {

	g.P("type check", funcName, " func(ctx ", contextContext, ") error")
	g.P()
	g.P("type healthCheckStore", funcName, " struct {")
	g.P("timeOut ", timeDuration)
	g.P("checks  []check", funcName)
	g.P("err     error")
	g.P("mu      *", syncRWMutex)
	g.P("}")
	g.P()
	g.P("func newHealthCheckStore", funcName, "() *healthCheckStore", funcName, " {")
	g.P("return &healthCheckStore", funcName, "{")
	g.P("timeOut: 10 * ", timePackageSecond, ",")
	g.P("checks:  []check", funcName, "{},")
	g.P("err:     nil,")
	g.P("mu:      &", syncRWMutex, "{},")
	g.P("}")
	g.P("}")
	g.P()
	g.P("type HealthCheckStoreInterface", funcName, " interface {")
	g.P("RegisterCheck(check check", funcName, ")")
	g.P("Status() error")
	g.P("}")
	g.P()
	g.P("func (s *healthCheckStore", funcName, ") Status() error {")
	g.P("s.mu.RLock()")
	g.P("defer s.mu.RUnlock()")
	g.P("return s.err")
	g.P("}")
	g.P()
	g.P("func (s *healthCheckStore", funcName, ") RegisterCheck(check check", funcName, ") {")
	g.P("if s == nil || s.checks == nil {")
	g.P("return")
	g.P("}")
	g.P("s.checks = append(s.checks, check)")
	g.P("}")
	g.P()

	g.P("func registerDBHealthCheck", funcName, "(gormDB *gorm.DB, healthCheck *healthCheckStore", funcName, ") {")
	g.P("db, err := gormDB.DB()")
	g.P("if err != nil {")
	g.P("panic(err)")
	g.P("}")
	g.P("healthCheck.RegisterCheck(func(ctx context.Context) error {")
	g.P("if err = db.PingContext(ctx); err != nil {")
	g.P("return fmt.Errorf(\"database is not responding: %w\", err)")
	g.P("}")
	g.P("return nil")
	g.P("})")
	g.P("}")
	g.P()
}

func printSensitiveConfig(g *protogen.GeneratedFile, funcName string) {

	g.P("type sensitiveConfig", funcName, " struct {")
	g.P(MONGO_KEY, " string `json:\"", MONGO_KEY, "\"`")
	g.P(POSTGRES_READ_KEY, " []string `json:\"", POSTGRES_READ_KEY, "\"`")
	g.P(POSTGRES_WRITE_KEY, " []string `json:\"", POSTGRES_WRITE_KEY, "\"`")
	g.P(REDIS_PASSWORD_KEY, " string `json:\"", REDIS_PASSWORD_KEY, "\"`")
	g.P(REDIS_DSN_KEY, " string `json:\"", REDIS_DSN_KEY, "\"`")
	g.P(LOG_LEVEL_KEY, " string `json:\"", LOG_LEVEL_KEY, "\"`")
	g.P(JAEGER_DSN, " string `json:\"", JAEGER_DSN, "\"`")
	g.P(SENTRY_DSN, " string `json:\"", SENTRY_DSN, "\"`")
	g.P(NATS_DSN, " string `json:\"", NATS_DSN, "\"`")
	g.P("}")
	g.P()
	g.P("func (c sensitiveConfig", funcName, ") Check() {")
	g.P("if c.", MONGO_KEY, " == \"\" {")
	g.P("panic(\"empty ", MONGO_KEY, "\")")
	g.P("}")
	g.P("if len(c.", POSTGRES_READ_KEY, ") == 0 {")
	g.P("panic(\"empty ", POSTGRES_READ_KEY, "\")")
	g.P("}")
	g.P("if len(c.", POSTGRES_WRITE_KEY, ") == 0 {")
	g.P("panic(\"empty ", POSTGRES_WRITE_KEY, "\")")
	g.P("}")
	g.P("if c.", REDIS_PASSWORD_KEY, " == \"\" {")
	g.P("panic(\"empty ", REDIS_PASSWORD_KEY, "\")")
	g.P("}")
	g.P("if c.", REDIS_DSN_KEY, " == \"\" {")
	g.P("panic(\"empty ", REDIS_DSN_KEY, "\")")
	g.P("}")
	g.P("if c.", JAEGER_DSN, " == \"\" {")
	g.P("panic(\"empty ", JAEGER_DSN, "\")")
	g.P("}")
	g.P("if c.", SENTRY_DSN, " == \"\" {")
	g.P("panic(\"empty ", SENTRY_DSN, "\")")
	g.P("}")
	g.P("if c.", NATS_DSN, " == \"\" {")
	g.P("panic(\"empty ", NATS_DSN, "\")")
	g.P("}")
	g.P("}")
	g.P()
}

func printSwagger(g *protogen.GeneratedFile, funcName string) {
	g.P("")

	g.P("func initOpenAPI", funcName, "(docs *", swagPkgSpec, ", config config", funcName, ", stand, microservice string) error {")
	g.P("docs.Host = config.DOMAIN")

	g.P("if stand == \"", STAND_LOCAL, "\" {")
	g.P("docs.Host = \"localhost:3000\"")
	g.P("}")

	g.P("docData := make(map[string]interface{})")
	g.P("_ = ", jsonPackageUnmarshal, "([]byte(docs.ReadDoc()), &docData)")

	g.P("body, err := ", jsonPackageMarshal, "(map[string]interface{}{")
	g.P("\"service_name\": microservice,")
	g.P("\"doc\":          docData,")
	g.P("})")
	g.P("if err != nil {")
	g.P("return err")
	g.P("}")

	g.P("req, err := ", httpPackageNewRequestWithContext, "(")
	g.P("context.Background(),")
	g.P(httpPackageMethodPost, ",")
	g.P("\"", openapiEndpoint, "\",")
	g.P(bytesPackageNewBuffer, "(body),")
	g.P(")")
	g.P("if err != nil {")
	g.P("return err")
	g.P("}")

	g.P("client := &", httpPackageClient, "{Timeout: ", clientTimeout, "}")
	g.P("resp, err := client.Do(req)")
	g.P("if err != nil {")
	g.P("return err")
	g.P("}")
	g.P("defer resp.Body.Close()")

	g.P("return nil")
	g.P("}")

}
