// Code generated by MockGen. DO NOT EDIT.
// Source: transaction_status.gin.pb.go

// Package grpcmock is a generated GoMock package.
package grpcmock

import (
	reflect "reflect"

	gin "github.com/gin-gonic/gin"
	gomock "github.com/golang/mock/gomock"
)

// MockGinTransactionStatusServer is a mock of GinTransactionStatusServer interface.
type MockGinTransactionStatusServer struct {
	ctrl     *gomock.Controller
	recorder *MockGinTransactionStatusServerMockRecorder
}

// MockGinTransactionStatusServerMockRecorder is the mock recorder for MockGinTransactionStatusServer.
type MockGinTransactionStatusServerMockRecorder struct {
	mock *MockGinTransactionStatusServer
}

// NewMockGinTransactionStatusServer creates a new mock instance.
func NewMockGinTransactionStatusServer(ctrl *gomock.Controller) *MockGinTransactionStatusServer {
	mock := &MockGinTransactionStatusServer{ctrl: ctrl}
	mock.recorder = &MockGinTransactionStatusServerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockGinTransactionStatusServer) EXPECT() *MockGinTransactionStatusServerMockRecorder {
	return m.recorder
}

// GetBatchTransactionWithStatuses mocks base method.
func (m *MockGinTransactionStatusServer) GetBatchTransactionWithStatuses(c *gin.Context) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetBatchTransactionWithStatuses", c)
	ret0, _ := ret[0].(error)
	return ret0
}

// GetBatchTransactionWithStatuses indicates an expected call of GetBatchTransactionWithStatuses.
func (mr *MockGinTransactionStatusServerMockRecorder) GetBatchTransactionWithStatuses(c interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetBatchTransactionWithStatuses", reflect.TypeOf((*MockGinTransactionStatusServer)(nil).GetBatchTransactionWithStatuses), c)
}
