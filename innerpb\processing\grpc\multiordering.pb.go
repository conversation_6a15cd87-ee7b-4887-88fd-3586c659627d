// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.5
// 	protoc        v5.29.3
// source: inner/processing/grpc/multiordering.proto

package grpc

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type GetOrderingIdentifierRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetOrderingIdentifierRequest) Reset() {
	*x = GetOrderingIdentifierRequest{}
	mi := &file_inner_processing_grpc_multiordering_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetOrderingIdentifierRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetOrderingIdentifierRequest) ProtoMessage() {}

func (x *GetOrderingIdentifierRequest) ProtoReflect() protoreflect.Message {
	mi := &file_inner_processing_grpc_multiordering_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetOrderingIdentifierRequest.ProtoReflect.Descriptor instead.
func (*GetOrderingIdentifierRequest) Descriptor() ([]byte, []int) {
	return file_inner_processing_grpc_multiordering_proto_rawDescGZIP(), []int{0}
}

type GetOrderingIdentifierResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Identifier    *string                `protobuf:"bytes,1,opt,name=identifier" json:"identifier,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetOrderingIdentifierResponse) Reset() {
	*x = GetOrderingIdentifierResponse{}
	mi := &file_inner_processing_grpc_multiordering_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetOrderingIdentifierResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetOrderingIdentifierResponse) ProtoMessage() {}

func (x *GetOrderingIdentifierResponse) ProtoReflect() protoreflect.Message {
	mi := &file_inner_processing_grpc_multiordering_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetOrderingIdentifierResponse.ProtoReflect.Descriptor instead.
func (*GetOrderingIdentifierResponse) Descriptor() ([]byte, []int) {
	return file_inner_processing_grpc_multiordering_proto_rawDescGZIP(), []int{1}
}

func (x *GetOrderingIdentifierResponse) GetIdentifier() string {
	if x != nil && x.Identifier != nil {
		return *x.Identifier
	}
	return ""
}

type GetPaymentOrderRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Date          *timestamppb.Timestamp `protobuf:"bytes,1,opt,name=date" json:"date,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetPaymentOrderRequest) Reset() {
	*x = GetPaymentOrderRequest{}
	mi := &file_inner_processing_grpc_multiordering_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetPaymentOrderRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetPaymentOrderRequest) ProtoMessage() {}

func (x *GetPaymentOrderRequest) ProtoReflect() protoreflect.Message {
	mi := &file_inner_processing_grpc_multiordering_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetPaymentOrderRequest.ProtoReflect.Descriptor instead.
func (*GetPaymentOrderRequest) Descriptor() ([]byte, []int) {
	return file_inner_processing_grpc_multiordering_proto_rawDescGZIP(), []int{2}
}

func (x *GetPaymentOrderRequest) GetDate() *timestamppb.Timestamp {
	if x != nil {
		return x.Date
	}
	return nil
}

type GetPaymentOrderResponse struct {
	state         protoimpl.MessageState                     `protogen:"open.v1"`
	ExternalId    *string                                    `protobuf:"bytes,1,opt,name=external_id,json=externalId" json:"external_id,omitempty"`
	Submerchants  []*GetPaymentOrderResponse_SubmerchantData `protobuf:"bytes,2,rep,name=submerchants" json:"submerchants,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetPaymentOrderResponse) Reset() {
	*x = GetPaymentOrderResponse{}
	mi := &file_inner_processing_grpc_multiordering_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetPaymentOrderResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetPaymentOrderResponse) ProtoMessage() {}

func (x *GetPaymentOrderResponse) ProtoReflect() protoreflect.Message {
	mi := &file_inner_processing_grpc_multiordering_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetPaymentOrderResponse.ProtoReflect.Descriptor instead.
func (*GetPaymentOrderResponse) Descriptor() ([]byte, []int) {
	return file_inner_processing_grpc_multiordering_proto_rawDescGZIP(), []int{3}
}

func (x *GetPaymentOrderResponse) GetExternalId() string {
	if x != nil && x.ExternalId != nil {
		return *x.ExternalId
	}
	return ""
}

func (x *GetPaymentOrderResponse) GetSubmerchants() []*GetPaymentOrderResponse_SubmerchantData {
	if x != nil {
		return x.Submerchants
	}
	return nil
}

type GetPaymentOrderResponse_SubmerchantData struct {
	state              protoimpl.MessageState `protogen:"open.v1"`
	Bin                *string                `protobuf:"bytes,1,opt,name=bin" json:"bin,omitempty"`
	Name               *string                `protobuf:"bytes,2,opt,name=name" json:"name,omitempty"`
	RecipientAccount   *string                `protobuf:"bytes,3,opt,name=recipient_account,json=recipientAccount" json:"recipient_account,omitempty"`
	PaymentPurposeCode *string                `protobuf:"bytes,4,opt,name=payment_purpose_code,json=paymentPurposeCode" json:"payment_purpose_code,omitempty"`
	BeneficiaryCode    *string                `protobuf:"bytes,5,opt,name=beneficiary_code,json=beneficiaryCode" json:"beneficiary_code,omitempty"`
	Description        *string                `protobuf:"bytes,6,opt,name=description" json:"description,omitempty"`
	Amount             *float64               `protobuf:"fixed64,7,opt,name=amount" json:"amount,omitempty"`
	unknownFields      protoimpl.UnknownFields
	sizeCache          protoimpl.SizeCache
}

func (x *GetPaymentOrderResponse_SubmerchantData) Reset() {
	*x = GetPaymentOrderResponse_SubmerchantData{}
	mi := &file_inner_processing_grpc_multiordering_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetPaymentOrderResponse_SubmerchantData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetPaymentOrderResponse_SubmerchantData) ProtoMessage() {}

func (x *GetPaymentOrderResponse_SubmerchantData) ProtoReflect() protoreflect.Message {
	mi := &file_inner_processing_grpc_multiordering_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetPaymentOrderResponse_SubmerchantData.ProtoReflect.Descriptor instead.
func (*GetPaymentOrderResponse_SubmerchantData) Descriptor() ([]byte, []int) {
	return file_inner_processing_grpc_multiordering_proto_rawDescGZIP(), []int{3, 0}
}

func (x *GetPaymentOrderResponse_SubmerchantData) GetBin() string {
	if x != nil && x.Bin != nil {
		return *x.Bin
	}
	return ""
}

func (x *GetPaymentOrderResponse_SubmerchantData) GetName() string {
	if x != nil && x.Name != nil {
		return *x.Name
	}
	return ""
}

func (x *GetPaymentOrderResponse_SubmerchantData) GetRecipientAccount() string {
	if x != nil && x.RecipientAccount != nil {
		return *x.RecipientAccount
	}
	return ""
}

func (x *GetPaymentOrderResponse_SubmerchantData) GetPaymentPurposeCode() string {
	if x != nil && x.PaymentPurposeCode != nil {
		return *x.PaymentPurposeCode
	}
	return ""
}

func (x *GetPaymentOrderResponse_SubmerchantData) GetBeneficiaryCode() string {
	if x != nil && x.BeneficiaryCode != nil {
		return *x.BeneficiaryCode
	}
	return ""
}

func (x *GetPaymentOrderResponse_SubmerchantData) GetDescription() string {
	if x != nil && x.Description != nil {
		return *x.Description
	}
	return ""
}

func (x *GetPaymentOrderResponse_SubmerchantData) GetAmount() float64 {
	if x != nil && x.Amount != nil {
		return *x.Amount
	}
	return 0
}

var File_inner_processing_grpc_multiordering_proto protoreflect.FileDescriptor

var file_inner_processing_grpc_multiordering_proto_rawDesc = string([]byte{
	0x0a, 0x29, 0x69, 0x6e, 0x6e, 0x65, 0x72, 0x2f, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69,
	0x6e, 0x67, 0x2f, 0x67, 0x72, 0x70, 0x63, 0x2f, 0x6d, 0x75, 0x6c, 0x74, 0x69, 0x6f, 0x72, 0x64,
	0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x26, 0x70, 0x72, 0x6f,
	0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x2e, 0x6d, 0x75, 0x6c, 0x74, 0x69, 0x6f, 0x72, 0x64,
	0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x6d, 0x75, 0x6c, 0x74, 0x69, 0x6f, 0x72, 0x64, 0x65, 0x72,
	0x69, 0x6e, 0x67, 0x1a, 0x1f, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x62, 0x75, 0x66, 0x2f, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x22, 0x1e, 0x0a, 0x1c, 0x47, 0x65, 0x74, 0x4f, 0x72, 0x64, 0x65, 0x72,
	0x69, 0x6e, 0x67, 0x49, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x66, 0x69, 0x65, 0x72, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x22, 0x3f, 0x0a, 0x1d, 0x47, 0x65, 0x74, 0x4f, 0x72, 0x64, 0x65, 0x72,
	0x69, 0x6e, 0x67, 0x49, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x66, 0x69, 0x65, 0x72, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x1e, 0x0a, 0x0a, 0x69, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x66,
	0x69, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x69, 0x64, 0x65, 0x6e, 0x74,
	0x69, 0x66, 0x69, 0x65, 0x72, 0x22, 0x48, 0x0a, 0x16, 0x47, 0x65, 0x74, 0x50, 0x61, 0x79, 0x6d,
	0x65, 0x6e, 0x74, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12,
	0x2e, 0x0a, 0x04, 0x64, 0x61, 0x74, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e,
	0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x04, 0x64, 0x61, 0x74, 0x65, 0x22,
	0xad, 0x03, 0x0a, 0x17, 0x47, 0x65, 0x74, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x4f, 0x72,
	0x64, 0x65, 0x72, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x1f, 0x0a, 0x0b, 0x65,
	0x78, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0a, 0x65, 0x78, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x49, 0x64, 0x12, 0x73, 0x0a, 0x0c,
	0x73, 0x75, 0x62, 0x6d, 0x65, 0x72, 0x63, 0x68, 0x61, 0x6e, 0x74, 0x73, 0x18, 0x02, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x4f, 0x2e, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x2e,
	0x6d, 0x75, 0x6c, 0x74, 0x69, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x6d, 0x75,
	0x6c, 0x74, 0x69, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x47, 0x65, 0x74, 0x50,
	0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x2e, 0x53, 0x75, 0x62, 0x6d, 0x65, 0x72, 0x63, 0x68, 0x61, 0x6e, 0x74, 0x44,
	0x61, 0x74, 0x61, 0x52, 0x0c, 0x73, 0x75, 0x62, 0x6d, 0x65, 0x72, 0x63, 0x68, 0x61, 0x6e, 0x74,
	0x73, 0x1a, 0xfb, 0x01, 0x0a, 0x0f, 0x53, 0x75, 0x62, 0x6d, 0x65, 0x72, 0x63, 0x68, 0x61, 0x6e,
	0x74, 0x44, 0x61, 0x74, 0x61, 0x12, 0x10, 0x0a, 0x03, 0x62, 0x69, 0x6e, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x03, 0x62, 0x69, 0x6e, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x2b, 0x0a, 0x11, 0x72,
	0x65, 0x63, 0x69, 0x70, 0x69, 0x65, 0x6e, 0x74, 0x5f, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x10, 0x72, 0x65, 0x63, 0x69, 0x70, 0x69, 0x65, 0x6e,
	0x74, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x30, 0x0a, 0x14, 0x70, 0x61, 0x79, 0x6d,
	0x65, 0x6e, 0x74, 0x5f, 0x70, 0x75, 0x72, 0x70, 0x6f, 0x73, 0x65, 0x5f, 0x63, 0x6f, 0x64, 0x65,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x12, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x50,
	0x75, 0x72, 0x70, 0x6f, 0x73, 0x65, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x29, 0x0a, 0x10, 0x62, 0x65,
	0x6e, 0x65, 0x66, 0x69, 0x63, 0x69, 0x61, 0x72, 0x79, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x05,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0f, 0x62, 0x65, 0x6e, 0x65, 0x66, 0x69, 0x63, 0x69, 0x61, 0x72,
	0x79, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x20, 0x0a, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70,
	0x74, 0x69, 0x6f, 0x6e, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x64, 0x65, 0x73, 0x63,
	0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x16, 0x0a, 0x06, 0x61, 0x6d, 0x6f, 0x75, 0x6e,
	0x74, 0x18, 0x07, 0x20, 0x01, 0x28, 0x01, 0x52, 0x06, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x32,
	0xcf, 0x02, 0x0a, 0x0d, 0x4d, 0x75, 0x6c, 0x74, 0x69, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x69, 0x6e,
	0x67, 0x12, 0x94, 0x01, 0x0a, 0x0f, 0x47, 0x65, 0x74, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74,
	0x4f, 0x72, 0x64, 0x65, 0x72, 0x12, 0x3e, 0x2e, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69,
	0x6e, 0x67, 0x2e, 0x6d, 0x75, 0x6c, 0x74, 0x69, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x69, 0x6e, 0x67,
	0x2e, 0x6d, 0x75, 0x6c, 0x74, 0x69, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x47,
	0x65, 0x74, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x3f, 0x2e, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69,
	0x6e, 0x67, 0x2e, 0x6d, 0x75, 0x6c, 0x74, 0x69, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x69, 0x6e, 0x67,
	0x2e, 0x6d, 0x75, 0x6c, 0x74, 0x69, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x47,
	0x65, 0x74, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0xa6, 0x01, 0x0a, 0x15, 0x47, 0x65, 0x74,
	0x4f, 0x72, 0x64, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x49, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x66, 0x69,
	0x65, 0x72, 0x12, 0x44, 0x2e, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x2e,
	0x6d, 0x75, 0x6c, 0x74, 0x69, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x6d, 0x75,
	0x6c, 0x74, 0x69, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x47, 0x65, 0x74, 0x4f,
	0x72, 0x64, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x49, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x66, 0x69, 0x65,
	0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x45, 0x2e, 0x70, 0x72, 0x6f, 0x63, 0x65,
	0x73, 0x73, 0x69, 0x6e, 0x67, 0x2e, 0x6d, 0x75, 0x6c, 0x74, 0x69, 0x6f, 0x72, 0x64, 0x65, 0x72,
	0x69, 0x6e, 0x67, 0x2e, 0x6d, 0x75, 0x6c, 0x74, 0x69, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x69, 0x6e,
	0x67, 0x2e, 0x47, 0x65, 0x74, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x49, 0x64, 0x65,
	0x6e, 0x74, 0x69, 0x66, 0x69, 0x65, 0x72, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22,
	0x00, 0x42, 0x2d, 0x5a, 0x2b, 0x67, 0x69, 0x74, 0x2e, 0x6c, 0x6f, 0x63, 0x61, 0x6c, 0x2f, 0x73,
	0x65, 0x6e, 0x73, 0x69, 0x74, 0x69, 0x76, 0x65, 0x2f, 0x69, 0x6e, 0x6e, 0x65, 0x72, 0x70, 0x62,
	0x2f, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x2f, 0x67, 0x72, 0x70, 0x63,
	0x62, 0x08, 0x65, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x70, 0xe8, 0x07,
})

var (
	file_inner_processing_grpc_multiordering_proto_rawDescOnce sync.Once
	file_inner_processing_grpc_multiordering_proto_rawDescData []byte
)

func file_inner_processing_grpc_multiordering_proto_rawDescGZIP() []byte {
	file_inner_processing_grpc_multiordering_proto_rawDescOnce.Do(func() {
		file_inner_processing_grpc_multiordering_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_inner_processing_grpc_multiordering_proto_rawDesc), len(file_inner_processing_grpc_multiordering_proto_rawDesc)))
	})
	return file_inner_processing_grpc_multiordering_proto_rawDescData
}

var file_inner_processing_grpc_multiordering_proto_msgTypes = make([]protoimpl.MessageInfo, 5)
var file_inner_processing_grpc_multiordering_proto_goTypes = []any{
	(*GetOrderingIdentifierRequest)(nil),            // 0: processing.multiordering.multiordering.GetOrderingIdentifierRequest
	(*GetOrderingIdentifierResponse)(nil),           // 1: processing.multiordering.multiordering.GetOrderingIdentifierResponse
	(*GetPaymentOrderRequest)(nil),                  // 2: processing.multiordering.multiordering.GetPaymentOrderRequest
	(*GetPaymentOrderResponse)(nil),                 // 3: processing.multiordering.multiordering.GetPaymentOrderResponse
	(*GetPaymentOrderResponse_SubmerchantData)(nil), // 4: processing.multiordering.multiordering.GetPaymentOrderResponse.SubmerchantData
	(*timestamppb.Timestamp)(nil),                   // 5: google.protobuf.Timestamp
}
var file_inner_processing_grpc_multiordering_proto_depIdxs = []int32{
	5, // 0: processing.multiordering.multiordering.GetPaymentOrderRequest.date:type_name -> google.protobuf.Timestamp
	4, // 1: processing.multiordering.multiordering.GetPaymentOrderResponse.submerchants:type_name -> processing.multiordering.multiordering.GetPaymentOrderResponse.SubmerchantData
	2, // 2: processing.multiordering.multiordering.Multiordering.GetPaymentOrder:input_type -> processing.multiordering.multiordering.GetPaymentOrderRequest
	0, // 3: processing.multiordering.multiordering.Multiordering.GetOrderingIdentifier:input_type -> processing.multiordering.multiordering.GetOrderingIdentifierRequest
	3, // 4: processing.multiordering.multiordering.Multiordering.GetPaymentOrder:output_type -> processing.multiordering.multiordering.GetPaymentOrderResponse
	1, // 5: processing.multiordering.multiordering.Multiordering.GetOrderingIdentifier:output_type -> processing.multiordering.multiordering.GetOrderingIdentifierResponse
	4, // [4:6] is the sub-list for method output_type
	2, // [2:4] is the sub-list for method input_type
	2, // [2:2] is the sub-list for extension type_name
	2, // [2:2] is the sub-list for extension extendee
	0, // [0:2] is the sub-list for field type_name
}

func init() { file_inner_processing_grpc_multiordering_proto_init() }
func file_inner_processing_grpc_multiordering_proto_init() {
	if File_inner_processing_grpc_multiordering_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_inner_processing_grpc_multiordering_proto_rawDesc), len(file_inner_processing_grpc_multiordering_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   5,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_inner_processing_grpc_multiordering_proto_goTypes,
		DependencyIndexes: file_inner_processing_grpc_multiordering_proto_depIdxs,
		MessageInfos:      file_inner_processing_grpc_multiordering_proto_msgTypes,
	}.Build()
	File_inner_processing_grpc_multiordering_proto = out.File
	file_inner_processing_grpc_multiordering_proto_goTypes = nil
	file_inner_processing_grpc_multiordering_proto_depIdxs = nil
}
