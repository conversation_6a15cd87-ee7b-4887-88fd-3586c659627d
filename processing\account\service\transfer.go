package service

import (
	"context"
	"github.com/google/uuid"
	"strconv"
	"time"

	"git.local/sensitive/innerpb/processing/goerr"
	gorpc "git.local/sensitive/innerpb/processing/grpc"
	"git.local/sensitive/processing/account/model"
	"git.local/sensitive/processing/account/repository"
	"git.local/sensitive/processing/account/schema"
	"git.local/sensitive/sdk/dog"
)

type TransferService struct {
	transferStatusRepo    repository.TransferStatuser
	transferManageRepo    repository.TransferManager
	transferTypeRepo      repository.TransferTyper
	transferRepo          repository.Transferer
	accountInfoRepo       repository.AccountInformer
	billingClient         gorpc.BillingClient
	multiaccountingClient gorpc.MultiaccountingClient
	getCurrentTime        func() time.Time
}

func NewTransferService(
	transferStatusRepo repository.TransferStatuser,
	transferManageRepo repository.TransferManager,
	transferTypeRepo repository.TransferTyper,
	transferRepo repository.Transferer,
	accountInfoRepo repository.AccountInformer,
	billingClient gorpc.BillingClient,
	multiaccountingClient gorpc.MultiaccountingClient,
) Transferer {
	return &TransferService{
		transferStatusRepo:    transferStatusRepo,
		transferManageRepo:    transferManageRepo,
		transferTypeRepo:      transferTypeRepo,
		transferRepo:          transferRepo,
		accountInfoRepo:       accountInfoRepo,
		billingClient:         billingClient,
		multiaccountingClient: multiaccountingClient,
		getCurrentTime:        time.Now,
	}
}

func (ts *TransferService) CreateTransfer(
	ctx context.Context,
	request schema.CreateTransferRequest,
) (_ *schema.CreateTransferResponse, err error) {
	ctx, span := dog.CreateSpan(ctx, "TransferService_CreateTransfer")
	defer span.End()

	transferStatusNew, err := ts.transferStatusRepo.GetByCode(ctx, model.StatusNew)
	if err != nil {
		return nil, err
	}

	account, err := ts.accountInfoRepo.GetByNumber(ctx, request.AccountNumber)
	if err != nil {
		return nil, err
	}

	balanceOwnerInfo, err := ts.billingClient.GetBalanceOwnerByIDV1(
		ctx, &gorpc.GetBalanceOwnerByIDRequestV1{
			BalanceOwnerId: &request.BalanceOwnerID,
		})
	if err != nil {
		return nil, err
	}

	_, err = ts.billingClient.CheckHasBalanceV1(ctx, &gorpc.CheckHasBalanceRequestV1{
		ProjectId:     balanceOwnerInfo.ProjectId,
		MerchantId:    balanceOwnerInfo.MerchantId,
		AccountNumber: &account.Number,
	})
	if err != nil {
		return nil, err
	}

	transferType, entityType, countryCode, err := ts.fetchAdditionalInfo(ctx, balanceOwnerInfo)
	if err != nil {
		return nil, err
	}

	transfer, err := schema.NewTransferFromCreateRequest(&request, transferStatusNew, transferType, balanceOwnerInfo, account, entityType)
	if err != nil {
		return nil, err
	}

	newTransfer, err := ts.transferManageRepo.Create(ctx, transfer)
	if err != nil {
		return nil, err
	}

	convertedAccountInfo, err := schema.AccountInfoConverter(account)
	if err != nil {
		return nil, err
	}

	rawRequest := schema.NewRawMakeTransferRequest(
		newTransfer.ID, account.CurrencyCode, request, convertedAccountInfo, entityType, countryCode)

	rawResponse, err := ts.multiaccountingClient.MakeTransfer(ctx, rawRequest)
	if err != nil {
		dog.SendError(ctx, err, dog.WithExt(map[string]interface{}{
			"request": rawRequest,
		}))

		transferStatus, statusErr := ts.transferStatusRepo.GetByCode(ctx, model.StatusFailed)
		if statusErr != nil {
			return nil, statusErr
		}

		now := ts.getCurrentTime()
		finishedAt := &now

		statusErr = ts.transferManageRepo.UpdateStatus(ctx, newTransfer.ID, transferStatus.ID, finishedAt)
		if statusErr != nil {
			return nil, statusErr
		}

		return nil, err
	}

	response := schema.NewFromRawMakeTransferResponse(rawResponse)

	transferStatus, err := ts.transferStatusRepo.GetByID(ctx, response.StatusID)
	if err != nil {
		return nil, err
	}

	var finishedAt *time.Time
	if transferStatus.Code.IsFinal() {
		now := ts.getCurrentTime()
		finishedAt = &now
	}

	if err = ts.transferManageRepo.UpdateStatus(ctx, response.TransferID, response.StatusID, finishedAt); err != nil {
		return nil, err
	}

	if err = ts.transferManageRepo.UpdateTransfer(ctx, response); err != nil {
		return nil, err
	}

	return response, nil
}

func (ts *TransferService) fetchAdditionalInfo(
	ctx context.Context,
	balanceOwner *gorpc.GetBalanceOwnerResponseV1,
) (transferType *model.TransferType, entityType *gorpc.GetEntityTypeResponseV1, countryCode *gorpc.GetCountryCodeResponseV1, err error) {
	transferType, err = ts.transferTypeRepo.GetByCode(ctx, model.TypeOut)
	if err != nil {
		return nil, nil, nil, err
	}

	entityType, err = ts.billingClient.GetEntityTypeByIDV1(ctx, &gorpc.GetEntityTypeByIDRequestV1{
		Id: balanceOwner.EntityTypeId,
	})
	if err != nil {
		return nil, nil, nil, err
	}

	countryCode, err = ts.billingClient.GetCountryCodeByIDV1(ctx, &gorpc.GetCountryCodeByIDRequestV1{
		Id: balanceOwner.CountryCodeId,
	})
	if err != nil {
		return nil, nil, nil, err
	}

	return transferType, entityType, countryCode, nil
}

func (ts *TransferService) AcceptTransfer(
	ctx context.Context,
	request schema.AcceptTransferRequest,
) (_ *schema.AcceptTransferResponse, err error) {
	ctx, span := dog.CreateSpan(ctx, "TransferService_AcceptOrder")
	defer span.End()

	responses := make([]*schema.TransferResponse, 0)

	for _, orderID := range request.TransfersList {
		response, orderErr := ts.ProcessAcceptTransfer(ctx, orderID)
		if orderErr != nil {
			return nil, orderErr
		}

		responses = append(responses, response)
	}

	return &schema.AcceptTransferResponse{
		Responses: responses,
	}, nil
}

func (ts *TransferService) ProcessAcceptTransfer(
	ctx context.Context,
	transferID uint64,
) (*schema.TransferResponse, error) {
	transfer, err := ts.transferRepo.GetByID(ctx, transferID)
	if err != nil {
		return nil, err
	}

	transferStatus, err := ts.transferStatusRepo.GetByID(ctx, transfer.StatusID)
	if err != nil {
		return nil, err
	}

	if transferStatus.Code != model.StatusAuthorized {
		return nil, goerr.ErrStatusValidation.WithCtx(ctx)
	}

	account, err := ts.accountInfoRepo.GetById(ctx, transfer.AccountID)
	if err != nil {
		return nil, err
	}

	convertedAccountInfo, err := schema.AccountInfoConverter(account)
	if err != nil {
		return nil, err
	}

	rawRequest := schema.NewRawAcceptTransferRequest(transferID, convertedAccountInfo)

	sufficient, err := ts.billingClient.CheckOutTransferBalanceV1(ctx, &gorpc.CheckOutTransferBalanceRequestV1{
		ProjectId:     &transfer.ProjectID,
		AccountNumber: &account.Number,
		Amount:        &transfer.Amount,
		MerchantId:    &transfer.MerchantID,
	})
	if err != nil {
		return nil, err
	}

	if !sufficient.GetIsSufficient() {
		declineRawRequest := schema.NewRawDeclineTransferRequest(transferID, convertedAccountInfo)

		rawResponse, declineTransferErr := ts.multiaccountingClient.DeclineTransfer(ctx, declineRawRequest)
		if declineTransferErr != nil {
			return nil, declineTransferErr
		}

		failStatusCode, stCodeErr := ts.transferStatusRepo.GetByCode(ctx, model.StatusFailed)
		if stCodeErr != nil {
			return nil, stCodeErr
		}

		now := ts.getCurrentTime()
		if stCodeErr = ts.transferManageRepo.UpdateStatus(ctx, transferID, failStatusCode.ID, &now); stCodeErr != nil {
			return nil, stCodeErr
		}

		return schema.NewFromRawDeclineTransferResponse(rawResponse), nil
	}

	rawResponse, err := ts.multiaccountingClient.AcceptTransfer(ctx, rawRequest)
	if err != nil {
		return nil, err
	}

	response := schema.NewFromRawAcceptTransferResponse(rawResponse)

	if err = ts.handleTransferStatus(ctx, transferID, response, transfer, account); err != nil {
		return nil, err
	}

	return response, nil
}

func (ts *TransferService) handleTransferStatus(
	ctx context.Context,
	transferID uint64,
	response *schema.TransferResponse,
	transfer *model.Transfer,
	account *model.Account,
) error {
	transferStatus, err := ts.transferStatusRepo.GetByID(ctx, response.StatusID)
	if err != nil {
		return err
	}

	var finishedAt *time.Time
	if transferStatus.Code.IsFinal() {
		now := ts.getCurrentTime()
		finishedAt = &now
	}

	if err := ts.transferManageRepo.UpdateStatus(ctx, response.TransferID, response.StatusID, finishedAt); err != nil {
		return err
	}

	if transferStatus.Code == model.StatusSuccess {
		go func() {
			if _, err = ts.billingClient.BillOutTransferV1(
				context.Background(),
				&gorpc.BillOutTransferRequestV1{
					ProjectId:     &transfer.ProjectID,
					AccountNumber: &account.Number,
					Amount:        &transfer.Amount,
					MerchantId:    &transfer.MerchantID,
					TransferId:    &transferID,
				}); err != nil {
				dog.SendError(ctx, err, nil)
			}
		}()
	}

	return nil
}

func (ts *TransferService) CancelTransfer(
	ctx context.Context,
	request schema.CancelTransferRequest,
) (_ *schema.CancelTransferResponse, err error) {
	ctx, span := dog.CreateSpan(ctx, "TransferService_CancelOrder")
	defer span.End()

	responses := make([]*schema.TransferResponse, 0)

	for _, orderID := range request.TransfersList {
		response, orderErr := ts.ProcessCancelTransfer(ctx, orderID)
		if orderErr != nil {
			dog.SendError(ctx, orderErr)
			continue
		}

		responses = append(responses, response)
	}

	return &schema.CancelTransferResponse{
		Responses: responses,
	}, nil
}

func (ts *TransferService) ProcessCancelTransfer(
	ctx context.Context,
	transferID uint64,
) (*schema.TransferResponse, error) {
	transfer, err := ts.transferRepo.GetByID(ctx, transferID)
	if err != nil {
		return nil, err
	}

	transferStatus, err := ts.transferStatusRepo.GetByID(ctx, transfer.StatusID)
	if err != nil {
		return nil, err
	}

	if transferStatus.Code != model.StatusAuthorized {
		return nil, goerr.ErrStatusValidation.WithCtx(ctx)
	}

	account, err := ts.accountInfoRepo.GetById(ctx, transfer.AccountID)
	if err != nil {
		return nil, err
	}

	convertedAccountInfo, err := schema.AccountInfoConverter(account)
	if err != nil {
		return nil, err
	}

	rawRequest := schema.NewRawDeclineTransferRequest(transferID, convertedAccountInfo)

	rawResponse, err := ts.multiaccountingClient.DeclineTransfer(ctx, rawRequest)
	if err != nil {
		return nil, err
	}

	response := schema.NewFromRawDeclineTransferResponse(rawResponse)

	transferStatus, err = ts.transferStatusRepo.GetByID(ctx, response.StatusID)
	if err != nil {
		return nil, err
	}

	var finishedAt *time.Time
	if transferStatus.Code.IsFinal() {
		now := ts.getCurrentTime()
		finishedAt = &now
	}

	err = ts.transferManageRepo.UpdateStatus(ctx, response.TransferID, response.StatusID, finishedAt)
	if err != nil {
		return nil, err
	}

	return response, nil
}

func (ts *TransferService) RedoTransfer(
	ctx context.Context,
	request schema.RedoTransferRequest,
) (_ *schema.TransferResponse, err error) {
	ctx, span := dog.CreateSpan(ctx, "TransferService_RedoOrder")
	defer span.End()

	transfer, err := ts.transferRepo.GetByID(ctx, request.TransferID)
	if err != nil {
		return nil, err
	}

	transferStatus, err := ts.transferStatusRepo.GetByID(ctx, transfer.StatusID)
	if err != nil {
		return nil, err
	}

	if transferStatus.Code != model.StatusHold {
		return nil, goerr.ErrStatusValidation.WithCtx(ctx)
	}

	account, err := ts.accountInfoRepo.GetByNumber(ctx, request.TransitAccount)
	if err != nil {
		return nil, err
	}

	convertedAccountInfo, err := schema.AccountInfoConverter(account)
	if err != nil {
		return nil, err
	}

	rawRequest := schema.NewRawRedoTransferRequest(request.TransferID, convertedAccountInfo)

	rawResponse, err := ts.multiaccountingClient.RedoTransfer(ctx, rawRequest)
	if err != nil {
		return nil, err
	}

	response := schema.NewFromRawRedoTransferResponse(rawResponse)

	transferStatus, err = ts.transferStatusRepo.GetByID(ctx, response.StatusID)
	if err != nil {
		return nil, err
	}

	var finishedAt *time.Time
	if transferStatus.Code.IsFinal() {
		now := ts.getCurrentTime()
		finishedAt = &now
	}

	err = ts.transferManageRepo.UpdateStatus(ctx, response.TransferID, response.StatusID, finishedAt)
	if err != nil {
		return nil, err
	}

	return response, nil
}

func (ts *TransferService) FinalizeTransfer(ctx context.Context) error {
	transferStatusMap, err := ts.transferStatusRepo.AsCodeMap(ctx)
	if err != nil {
		return err
	}

	nonFinalizedStatusIDs := ts.getNonFinalizedStatusIDs(transferStatusMap)

	nonFinalizedTransfers := make([]model.Transfer, 0)

	for _, statusID := range nonFinalizedStatusIDs {
		transfers, transferErr := ts.transferRepo.GetByFilters(ctx, schema.TransferFilters{
			StatusID: statusID,
		})
		if transferErr != nil {
			return transferErr
		}

		nonFinalizedTransfers = append(nonFinalizedTransfers, transfers...)
	}

	for _, transfer := range nonFinalizedTransfers {
		accountInfo, accountInfoErr := ts.accountInfoRepo.GetById(ctx, transfer.AccountID)
		if accountInfoErr != nil {
			return accountInfoErr
		}

		convertedAccountInfo, convertAccountErr := schema.AccountInfoConverter(accountInfo)
		if convertAccountErr != nil {
			return convertAccountErr
		}

		rawRequest := schema.NewRawGetTransferStatusRequest(&transfer, convertedAccountInfo)

		transferStatus, transferStatusErr := ts.multiaccountingClient.GetTransferDetails(ctx, rawRequest)
		if transferStatusErr != nil {
			return transferStatusErr
		}

		if ts.isNotFinalStatus(transferStatus.GetTransferStatusId(), transferStatusMap) {
			continue
		}

		trStatus, err := ts.transferStatusRepo.GetByID(ctx, transferStatus.GetTransferStatusId())
		if err != nil {
			return err
		}

		var finishedAt *time.Time
		if trStatus.Code.IsFinal() {
			now := ts.getCurrentTime()
			finishedAt = &now
		}

		if err = ts.transferManageRepo.UpdateStatus(ctx, transfer.ID, transferStatus.GetTransferStatusId(), finishedAt); err != nil {
			return err
		}

		if transferStatus.GetTransferStatusId() == transferStatusMap[model.StatusSuccess] {
			go func() {
				if _, err = ts.billingClient.BillOutTransferV1(
					context.Background(),
					&gorpc.BillOutTransferRequestV1{
						ProjectId:     &transfer.ProjectID,
						AccountNumber: &accountInfo.Number,
						Amount:        &transfer.Amount,
						MerchantId:    &transfer.MerchantID,
						TransferId:    &transfer.ID,
					}); err != nil {
					dog.SendError(ctx, err, nil)
				}
			}()
		}
	}

	return nil
}

func (ts *TransferService) ExtractSuccessTransfersByForeignIDs(ctx context.Context, foreignIDs []uuid.UUID) ([]model.Transfer, error) {
	ctx, span := dog.CreateSpan(ctx, "TransferService_ExtractSuccessTransfersByForeignIDs")
	defer span.End()

	return ts.transferRepo.ExtractSuccessTransfersByForeignIDs(ctx, foreignIDs)
}

func (ts *TransferService) ExtractFailOrCanceledTransfersByForeignIDs(ctx context.Context, foreignIDs []uuid.UUID) ([]model.Transfer, error) {
	ctx, span := dog.CreateSpan(ctx, "TransferService_ExtractFailOrCanceledTransfersByForeignIDs")
	defer span.End()

	return ts.transferRepo.ExtractFailOrCanceledTransfersByForeignIDs(ctx, foreignIDs)
}

func (ts *TransferService) getNonFinalizedStatusIDs(transferStatusMap map[model.TransferStatusCode]uint64) []string {
	return []string{
		strconv.FormatUint(transferStatusMap[model.StatusInProgress], 10),
		strconv.FormatUint(transferStatusMap[model.StatusHold], 10),
		strconv.FormatUint(transferStatusMap[model.StatusInRedo], 10),
	}
}

func (ts *TransferService) GetByForeignID(ctx context.Context, foreignID uuid.UUID) (*model.Transfer, error) {
	ctx, span := dog.CreateSpan(ctx, "TransferService_GetByForeignID")
	defer span.End()

	return ts.transferRepo.GetByForeignID(ctx, foreignID)
}

func (ts *TransferService) isNotFinalStatus(
	transferStatusID uint64,
	transferStatusMap map[model.TransferStatusCode]uint64,
) bool {
	return transferStatusID == transferStatusMap[model.StatusInProgress] ||
		transferStatusID == transferStatusMap[model.StatusHold] ||
		transferStatusID == transferStatusMap[model.StatusInRedo]
}
