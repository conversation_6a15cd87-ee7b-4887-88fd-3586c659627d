// Code generated by MockGen. DO NOT EDIT.
// Source: report_merchant_worker.gin.pb.go

// Package grpcmock is a generated GoMock package.
package grpcmock

import (
	reflect "reflect"

	gin "github.com/gin-gonic/gin"
	gomock "github.com/golang/mock/gomock"
)

// MockGinReportMerchantWorkerServer is a mock of GinReportMerchantWorkerServer interface.
type MockGinReportMerchantWorkerServer struct {
	ctrl     *gomock.Controller
	recorder *MockGinReportMerchantWorkerServerMockRecorder
}

// MockGinReportMerchantWorkerServerMockRecorder is the mock recorder for MockGinReportMerchantWorkerServer.
type MockGinReportMerchantWorkerServerMockRecorder struct {
	mock *MockGinReportMerchantWorkerServer
}

// NewMockGinReportMerchantWorkerServer creates a new mock instance.
func NewMockGinReportMerchantWorkerServer(ctrl *gomock.Controller) *MockGinReportMerchantWorkerServer {
	mock := &MockGinReportMerchantWorkerServer{ctrl: ctrl}
	mock.recorder = &MockGinReportMerchantWorkerServerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockGinReportMerchantWorkerServer) EXPECT() *MockGinReportMerchantWorkerServerMockRecorder {
	return m.recorder
}

// ProcessReportScheduleByPeriodType mocks base method.
func (m *MockGinReportMerchantWorkerServer) ProcessReportScheduleByPeriodType(c *gin.Context) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ProcessReportScheduleByPeriodType", c)
	ret0, _ := ret[0].(error)
	return ret0
}

// ProcessReportScheduleByPeriodType indicates an expected call of ProcessReportScheduleByPeriodType.
func (mr *MockGinReportMerchantWorkerServerMockRecorder) ProcessReportScheduleByPeriodType(c interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ProcessReportScheduleByPeriodType", reflect.TypeOf((*MockGinReportMerchantWorkerServer)(nil).ProcessReportScheduleByPeriodType), c)
}
