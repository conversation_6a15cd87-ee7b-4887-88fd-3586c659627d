// Code generated by protoc-gen-go-mvp. DO NOT EDIT.
// versions:
// - protoc-gen-go-mvp v2.0.1
// created by mvp authors

package grpc

import (
	dog "git.local/sensitive/sdk/dog"
	gin "github.com/gin-gonic/gin"
	metadata "google.golang.org/grpc/metadata"
	http "net/http"
)

func RegisterGinProjectTransactionsRouterGroup(
	in *gin.RouterGroup,
	handler func(func(c *gin.Context) error) gin.HandlerFunc,
) error {
	service, err := NewGinProjectTransactionsService()
	if err != nil {
		return err
	}

	routerGroup := in.Group("processing.merchant.project_transactions.ProjectTransactions")
	routerGroup.PUT("/CheckAmountLimit", handler(service.CheckAmountLimit))
	routerGroup.PUT("/CheckAttemptsWithinTimeout", handler(service.CheckAttemptsWithinTimeout))
	routerGroup.PUT("/GetTransactionLimit", handler(service.GetTransactionLimit))
	return nil
}

func NewGinProjectTransactionsService() (GinProjectTransactionsServer, error) {
	client, err := NewPreparedProjectTransactionsClient()
	if err != nil {
		return nil, err
	}

	return &ginProjectTransactionsServer{
		client: NewLoggedProjectTransactionsClient(
			NewIamProjectTransactionsClient(client),
		),
	}, nil
}

//go:generate mockgen -destination=./grpcmock/project_transactions.gin.pb.go -package=grpcmock -source=project_transactions.gin.pb.go GinProjectTransactionsServer
type GinProjectTransactionsServer interface {
	CheckAmountLimit(c *gin.Context) error
	CheckAttemptsWithinTimeout(c *gin.Context) error
	GetTransactionLimit(c *gin.Context) error
}

var _ GinProjectTransactionsServer = (*ginProjectTransactionsServer)(nil)

type ginProjectTransactionsServer struct {
	client ProjectTransactionsClient
}

type ProjectTransactions_CheckAmountLimit_Success struct {
	// Status is the status of the response. Set true
	Status bool `json:"status"`
	// Message is a human-readable explanation of the status
	Message string `json:"message"`
	// StatusCode is the GoErr status. Set 0
	StatusCode int `json:"status_code"`
	// Result is the result of the request
	Result *CheckAmountLimitResponseV1 `json:"result"`
}

type ProjectTransactions_CheckAmountLimit_Failure struct {
	// Status is the status of the response. Set false
	Status bool `json:"status"`
	// Message is a human-readable explanation of the status
	Message string `json:"message"`
	// StatusCode is the GoErr status
	StatusCode int `json:"status_code"`
}

// CheckAmountLimit
// @Summary CheckAmountLimit
// @Security bearerAuth
// @ID ProjectTransactions_CheckAmountLimit
// @Accept json
// @Param request body CheckAmountLimitRequestV1 true "CheckAmountLimitRequestV1"
// @Success 200 {object} ProjectTransactions_CheckAmountLimit_Success
// @Failure 401 {object} ProjectTransactions_CheckAmountLimit_Failure "goerrpkg.ErrorTypeAuthorization"
// @Failure 404 {object} ProjectTransactions_CheckAmountLimit_Failure "goerrpkg.ErrorTypeNotFound"
// @Failure 400 {object} ProjectTransactions_CheckAmountLimit_Failure "goerrpkg.ErrorTypeBadRequest"
// @Failure 500 {object} ProjectTransactions_CheckAmountLimit_Failure "goerrpkg.ErrorTypeUnexpectedError"
// @Failure 400 {object} ProjectTransactions_CheckAmountLimit_Failure "gin.ErrorTypeBind"
// @Failure 500 {object} ProjectTransactions_CheckAmountLimit_Failure "Undefined error"
// @Produce json
// @Router /processing.merchant.project_transactions.ProjectTransactions/CheckAmountLimit [put]
// @tags ProjectTransactions
func (s *ginProjectTransactionsServer) CheckAmountLimit(c *gin.Context) error {
	ctx, span := dog.CreateSpan(c.Request.Context(), "GinProjectTransactionsServer_CheckAmountLimit")
	defer span.End()

	var request CheckAmountLimitRequestV1
	if err := c.ShouldBindJSON(&request); err != nil {
		return err
	}

	ctx = metadata.NewOutgoingContext(ctx, metadata.MD(c.Request.Header))

	response, err := s.client.CheckAmountLimit(ctx, &request)
	if err != nil {
		return err
	}

	c.JSON(http.StatusOK, &ProjectTransactions_CheckAmountLimit_Success{
		Status:     true,
		Message:    "Success",
		StatusCode: 0,
		Result:     response,
	})
	return nil
}

type ProjectTransactions_CheckAttemptsWithinTimeout_Success struct {
	// Status is the status of the response. Set true
	Status bool `json:"status"`
	// Message is a human-readable explanation of the status
	Message string `json:"message"`
	// StatusCode is the GoErr status. Set 0
	StatusCode int `json:"status_code"`
	// Result is the result of the request
	Result *CheckAttemptsWithinTimeoutResponseV1 `json:"result"`
}

type ProjectTransactions_CheckAttemptsWithinTimeout_Failure struct {
	// Status is the status of the response. Set false
	Status bool `json:"status"`
	// Message is a human-readable explanation of the status
	Message string `json:"message"`
	// StatusCode is the GoErr status
	StatusCode int `json:"status_code"`
}

// CheckAttemptsWithinTimeout
// @Summary CheckAttemptsWithinTimeout
// @Security bearerAuth
// @ID ProjectTransactions_CheckAttemptsWithinTimeout
// @Accept json
// @Param request body CheckAttemptsWithinTimeoutRequestV1 true "CheckAttemptsWithinTimeoutRequestV1"
// @Success 200 {object} ProjectTransactions_CheckAttemptsWithinTimeout_Success
// @Failure 401 {object} ProjectTransactions_CheckAttemptsWithinTimeout_Failure "goerrpkg.ErrorTypeAuthorization"
// @Failure 404 {object} ProjectTransactions_CheckAttemptsWithinTimeout_Failure "goerrpkg.ErrorTypeNotFound"
// @Failure 400 {object} ProjectTransactions_CheckAttemptsWithinTimeout_Failure "goerrpkg.ErrorTypeBadRequest"
// @Failure 500 {object} ProjectTransactions_CheckAttemptsWithinTimeout_Failure "goerrpkg.ErrorTypeUnexpectedError"
// @Failure 400 {object} ProjectTransactions_CheckAttemptsWithinTimeout_Failure "gin.ErrorTypeBind"
// @Failure 500 {object} ProjectTransactions_CheckAttemptsWithinTimeout_Failure "Undefined error"
// @Produce json
// @Router /processing.merchant.project_transactions.ProjectTransactions/CheckAttemptsWithinTimeout [put]
// @tags ProjectTransactions
func (s *ginProjectTransactionsServer) CheckAttemptsWithinTimeout(c *gin.Context) error {
	ctx, span := dog.CreateSpan(c.Request.Context(), "GinProjectTransactionsServer_CheckAttemptsWithinTimeout")
	defer span.End()

	var request CheckAttemptsWithinTimeoutRequestV1
	if err := c.ShouldBindJSON(&request); err != nil {
		return err
	}

	ctx = metadata.NewOutgoingContext(ctx, metadata.MD(c.Request.Header))

	response, err := s.client.CheckAttemptsWithinTimeout(ctx, &request)
	if err != nil {
		return err
	}

	c.JSON(http.StatusOK, &ProjectTransactions_CheckAttemptsWithinTimeout_Success{
		Status:     true,
		Message:    "Success",
		StatusCode: 0,
		Result:     response,
	})
	return nil
}

type ProjectTransactions_GetTransactionLimit_Success struct {
	// Status is the status of the response. Set true
	Status bool `json:"status"`
	// Message is a human-readable explanation of the status
	Message string `json:"message"`
	// StatusCode is the GoErr status. Set 0
	StatusCode int `json:"status_code"`
	// Result is the result of the request
	Result *GetTransactionLimitResponseV1 `json:"result"`
}

type ProjectTransactions_GetTransactionLimit_Failure struct {
	// Status is the status of the response. Set false
	Status bool `json:"status"`
	// Message is a human-readable explanation of the status
	Message string `json:"message"`
	// StatusCode is the GoErr status
	StatusCode int `json:"status_code"`
}

// GetTransactionLimit
// @Summary GetTransactionLimit
// @Security bearerAuth
// @ID ProjectTransactions_GetTransactionLimit
// @Accept json
// @Param request body GetTransactionLimitRequestV1 true "GetTransactionLimitRequestV1"
// @Success 200 {object} ProjectTransactions_GetTransactionLimit_Success
// @Failure 401 {object} ProjectTransactions_GetTransactionLimit_Failure "goerrpkg.ErrorTypeAuthorization"
// @Failure 404 {object} ProjectTransactions_GetTransactionLimit_Failure "goerrpkg.ErrorTypeNotFound"
// @Failure 400 {object} ProjectTransactions_GetTransactionLimit_Failure "goerrpkg.ErrorTypeBadRequest"
// @Failure 500 {object} ProjectTransactions_GetTransactionLimit_Failure "goerrpkg.ErrorTypeUnexpectedError"
// @Failure 400 {object} ProjectTransactions_GetTransactionLimit_Failure "gin.ErrorTypeBind"
// @Failure 500 {object} ProjectTransactions_GetTransactionLimit_Failure "Undefined error"
// @Produce json
// @Router /processing.merchant.project_transactions.ProjectTransactions/GetTransactionLimit [put]
// @tags ProjectTransactions
func (s *ginProjectTransactionsServer) GetTransactionLimit(c *gin.Context) error {
	ctx, span := dog.CreateSpan(c.Request.Context(), "GinProjectTransactionsServer_GetTransactionLimit")
	defer span.End()

	var request GetTransactionLimitRequestV1
	if err := c.ShouldBindJSON(&request); err != nil {
		return err
	}

	ctx = metadata.NewOutgoingContext(ctx, metadata.MD(c.Request.Header))

	response, err := s.client.GetTransactionLimit(ctx, &request)
	if err != nil {
		return err
	}

	c.JSON(http.StatusOK, &ProjectTransactions_GetTransactionLimit_Success{
		Status:     true,
		Message:    "Success",
		StatusCode: 0,
		Result:     response,
	})
	return nil
}
