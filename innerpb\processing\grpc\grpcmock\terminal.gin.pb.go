// Code generated by MockGen. DO NOT EDIT.
// Source: terminal.gin.pb.go

// Package grpcmock is a generated GoMock package.
package grpcmock

import (
	reflect "reflect"

	gin "github.com/gin-gonic/gin"
	gomock "github.com/golang/mock/gomock"
)

// MockGinTerminalServer is a mock of GinTerminalServer interface.
type MockGinTerminalServer struct {
	ctrl     *gomock.Controller
	recorder *MockGinTerminalServerMockRecorder
}

// MockGinTerminalServerMockRecorder is the mock recorder for MockGinTerminalServer.
type MockGinTerminalServerMockRecorder struct {
	mock *MockGinTerminalServer
}

// NewMockGinTerminalServer creates a new mock instance.
func NewMockGinTerminalServer(ctrl *gomock.Controller) *MockGinTerminalServer {
	mock := &MockGinTerminalServer{ctrl: ctrl}
	mock.recorder = &MockGinTerminalServerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockGinTerminalServer) EXPECT() *MockGinTerminalServerMockRecorder {
	return m.recorder
}

// ExtendedSearchTerminal mocks base method.
func (m *MockGinTerminalServer) ExtendedSearchTerminal(c *gin.Context) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ExtendedSearchTerminal", c)
	ret0, _ := ret[0].(error)
	return ret0
}

// ExtendedSearchTerminal indicates an expected call of ExtendedSearchTerminal.
func (mr *MockGinTerminalServerMockRecorder) ExtendedSearchTerminal(c interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ExtendedSearchTerminal", reflect.TypeOf((*MockGinTerminalServer)(nil).ExtendedSearchTerminal), c)
}

// FindActiveTerminalsByProject mocks base method.
func (m *MockGinTerminalServer) FindActiveTerminalsByProject(c *gin.Context) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "FindActiveTerminalsByProject", c)
	ret0, _ := ret[0].(error)
	return ret0
}

// FindActiveTerminalsByProject indicates an expected call of FindActiveTerminalsByProject.
func (mr *MockGinTerminalServerMockRecorder) FindActiveTerminalsByProject(c interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "FindActiveTerminalsByProject", reflect.TypeOf((*MockGinTerminalServer)(nil).FindActiveTerminalsByProject), c)
}

// GetByTerminalID mocks base method.
func (m *MockGinTerminalServer) GetByTerminalID(c *gin.Context) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetByTerminalID", c)
	ret0, _ := ret[0].(error)
	return ret0
}

// GetByTerminalID indicates an expected call of GetByTerminalID.
func (mr *MockGinTerminalServerMockRecorder) GetByTerminalID(c interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetByTerminalID", reflect.TypeOf((*MockGinTerminalServer)(nil).GetByTerminalID), c)
}

// GetPayInProjectTerminals mocks base method.
func (m *MockGinTerminalServer) GetPayInProjectTerminals(c *gin.Context) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetPayInProjectTerminals", c)
	ret0, _ := ret[0].(error)
	return ret0
}

// GetPayInProjectTerminals indicates an expected call of GetPayInProjectTerminals.
func (mr *MockGinTerminalServerMockRecorder) GetPayInProjectTerminals(c interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetPayInProjectTerminals", reflect.TypeOf((*MockGinTerminalServer)(nil).GetPayInProjectTerminals), c)
}

// GetRuleByActiveTerminals mocks base method.
func (m *MockGinTerminalServer) GetRuleByActiveTerminals(c *gin.Context) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetRuleByActiveTerminals", c)
	ret0, _ := ret[0].(error)
	return ret0
}

// GetRuleByActiveTerminals indicates an expected call of GetRuleByActiveTerminals.
func (mr *MockGinTerminalServerMockRecorder) GetRuleByActiveTerminals(c interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetRuleByActiveTerminals", reflect.TypeOf((*MockGinTerminalServer)(nil).GetRuleByActiveTerminals), c)
}

// GetTerminalWithJusan mocks base method.
func (m *MockGinTerminalServer) GetTerminalWithJusan(c *gin.Context) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetTerminalWithJusan", c)
	ret0, _ := ret[0].(error)
	return ret0
}

// GetTerminalWithJusan indicates an expected call of GetTerminalWithJusan.
func (mr *MockGinTerminalServerMockRecorder) GetTerminalWithJusan(c interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetTerminalWithJusan", reflect.TypeOf((*MockGinTerminalServer)(nil).GetTerminalWithJusan), c)
}

// GetTerminalsByProjectId mocks base method.
func (m *MockGinTerminalServer) GetTerminalsByProjectId(c *gin.Context) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetTerminalsByProjectId", c)
	ret0, _ := ret[0].(error)
	return ret0
}

// GetTerminalsByProjectId indicates an expected call of GetTerminalsByProjectId.
func (mr *MockGinTerminalServerMockRecorder) GetTerminalsByProjectId(c interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetTerminalsByProjectId", reflect.TypeOf((*MockGinTerminalServer)(nil).GetTerminalsByProjectId), c)
}

// SearchTerminal mocks base method.
func (m *MockGinTerminalServer) SearchTerminal(c *gin.Context) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SearchTerminal", c)
	ret0, _ := ret[0].(error)
	return ret0
}

// SearchTerminal indicates an expected call of SearchTerminal.
func (mr *MockGinTerminalServerMockRecorder) SearchTerminal(c interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SearchTerminal", reflect.TypeOf((*MockGinTerminalServer)(nil).SearchTerminal), c)
}
