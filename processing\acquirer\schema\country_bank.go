package schema

import (
	"github.com/go-playground/validator/v10"

	"git.local/sensitive/innerpb/processing/goerr"
	"git.local/sensitive/processing/acquirer/model"
)

type CountryBank struct {
	CountryID  uint64              `json:"country_id" validate:"required"`
	BankID     uint64              `json:"bank_id" validate:"required"`
	validation *validator.Validate `json:"-"`
}

func (cb *CountryBank) Validate() error {
	if cb == nil {
		return goerr.ErrParseErrorBody
	}

	cb.validation = new(validator.Validate)
	cb.validation = validator.New()

	if err := cb.validation.Struct(cb); err != nil {
		return err
	}

	return nil
}

func (cb *CountryBank) ToModel() *model.CountryBank {
	if cb == nil {
		return nil
	}

	return &model.CountryBank{
		CountryID: cb.CountryID,
		BankID:    cb.BankID,
	}
}
