// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.5
// 	protoc        v5.29.3
// source: proto/gin.proto

package pb

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	descriptorpb "google.golang.org/protobuf/types/descriptorpb"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type GinMethod int32

const (
	GinMethod_GinPut   GinMethod = 0
	GinMethod_GinPost  GinMethod = 1
	GinMethod_GinPatch GinMethod = 2
)

// Enum value maps for GinMethod.
var (
	GinMethod_name = map[int32]string{
		0: "GinPut",
		1: "GinPost",
		2: "GinPatch",
	}
	GinMethod_value = map[string]int32{
		"GinPut":   0,
		"GinPost":  1,
		"GinPatch": 2,
	}
)

func (x GinMethod) Enum() *GinMethod {
	p := new(GinMethod)
	*p = x
	return p
}

func (x GinMethod) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (GinMethod) Descriptor() protoreflect.EnumDescriptor {
	return file_proto_gin_proto_enumTypes[0].Descriptor()
}

func (GinMethod) Type() protoreflect.EnumType {
	return &file_proto_gin_proto_enumTypes[0]
}

func (x GinMethod) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use GinMethod.Descriptor instead.
func (GinMethod) EnumDescriptor() ([]byte, []int) {
	return file_proto_gin_proto_rawDescGZIP(), []int{0}
}

var file_proto_gin_proto_extTypes = []protoimpl.ExtensionInfo{
	{
		ExtendedType:  (*descriptorpb.ServiceOptions)(nil),
		ExtensionType: (*string)(nil),
		Field:         71000701,
		Name:          "mvp.relative_path",
		Tag:           "bytes,71000701,opt,name=relative_path",
		Filename:      "proto/gin.proto",
	},
	{
		ExtendedType:  (*descriptorpb.MethodOptions)(nil),
		ExtensionType: (*GinMethod)(nil),
		Field:         71000702,
		Name:          "mvp.method",
		Tag:           "varint,71000702,opt,name=method,enum=mvp.GinMethod",
		Filename:      "proto/gin.proto",
	},
	{
		ExtendedType:  (*descriptorpb.MethodOptions)(nil),
		ExtensionType: (*string)(nil),
		Field:         71000703,
		Name:          "mvp.method_path",
		Tag:           "bytes,71000703,opt,name=method_path",
		Filename:      "proto/gin.proto",
	},
}

// Extension fields to descriptorpb.ServiceOptions.
var (
	// По умолчанию название полное сервиса
	//
	// optional string relative_path = 71000701;
	E_RelativePath = &file_proto_gin_proto_extTypes[0]
)

// Extension fields to descriptorpb.MethodOptions.
var (
	// optional mvp.GinMethod method = 71000702;
	E_Method = &file_proto_gin_proto_extTypes[1]
	// optional string method_path = 71000703;
	E_MethodPath = &file_proto_gin_proto_extTypes[2]
)

var File_proto_gin_proto protoreflect.FileDescriptor

var file_proto_gin_proto_rawDesc = string([]byte{
	0x0a, 0x0f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f, 0x67, 0x69, 0x6e, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x12, 0x03, 0x6d, 0x76, 0x70, 0x1a, 0x20, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74,
	0x6f, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2a, 0x32, 0x0a, 0x09, 0x47, 0x69, 0x6e, 0x4d,
	0x65, 0x74, 0x68, 0x6f, 0x64, 0x12, 0x0a, 0x0a, 0x06, 0x47, 0x69, 0x6e, 0x50, 0x75, 0x74, 0x10,
	0x00, 0x12, 0x0b, 0x0a, 0x07, 0x47, 0x69, 0x6e, 0x50, 0x6f, 0x73, 0x74, 0x10, 0x01, 0x12, 0x0c,
	0x0a, 0x08, 0x47, 0x69, 0x6e, 0x50, 0x61, 0x74, 0x63, 0x68, 0x10, 0x02, 0x3a, 0x47, 0x0a, 0x0d,
	0x72, 0x65, 0x6c, 0x61, 0x74, 0x69, 0x76, 0x65, 0x5f, 0x70, 0x61, 0x74, 0x68, 0x12, 0x1f, 0x2e,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e,
	0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0xfd,
	0xc4, 0xed, 0x21, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x72, 0x65, 0x6c, 0x61, 0x74, 0x69, 0x76,
	0x65, 0x50, 0x61, 0x74, 0x68, 0x3a, 0x49, 0x0a, 0x06, 0x6d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x12,
	0x1e, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75,
	0x66, 0x2e, 0x4d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18,
	0xfe, 0xc4, 0xed, 0x21, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x0e, 0x2e, 0x6d, 0x76, 0x70, 0x2e, 0x47,
	0x69, 0x6e, 0x4d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x52, 0x06, 0x6d, 0x65, 0x74, 0x68, 0x6f, 0x64,
	0x3a, 0x42, 0x0a, 0x0b, 0x6d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x5f, 0x70, 0x61, 0x74, 0x68, 0x12,
	0x1e, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75,
	0x66, 0x2e, 0x4d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18,
	0xff, 0xc4, 0xed, 0x21, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x6d, 0x65, 0x74, 0x68, 0x6f, 0x64,
	0x50, 0x61, 0x74, 0x68, 0x42, 0x1c, 0x5a, 0x1a, 0x67, 0x69, 0x74, 0x2e, 0x6c, 0x6f, 0x63, 0x61,
	0x6c, 0x2f, 0x73, 0x65, 0x6e, 0x73, 0x69, 0x74, 0x69, 0x76, 0x65, 0x2f, 0x6d, 0x76, 0x70, 0x2f,
	0x70, 0x62, 0x62, 0x08, 0x65, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x70, 0xe8, 0x07,
})

var (
	file_proto_gin_proto_rawDescOnce sync.Once
	file_proto_gin_proto_rawDescData []byte
)

func file_proto_gin_proto_rawDescGZIP() []byte {
	file_proto_gin_proto_rawDescOnce.Do(func() {
		file_proto_gin_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_proto_gin_proto_rawDesc), len(file_proto_gin_proto_rawDesc)))
	})
	return file_proto_gin_proto_rawDescData
}

var file_proto_gin_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_proto_gin_proto_goTypes = []any{
	(GinMethod)(0),                      // 0: mvp.GinMethod
	(*descriptorpb.ServiceOptions)(nil), // 1: google.protobuf.ServiceOptions
	(*descriptorpb.MethodOptions)(nil),  // 2: google.protobuf.MethodOptions
}
var file_proto_gin_proto_depIdxs = []int32{
	1, // 0: mvp.relative_path:extendee -> google.protobuf.ServiceOptions
	2, // 1: mvp.method:extendee -> google.protobuf.MethodOptions
	2, // 2: mvp.method_path:extendee -> google.protobuf.MethodOptions
	0, // 3: mvp.method:type_name -> mvp.GinMethod
	4, // [4:4] is the sub-list for method output_type
	4, // [4:4] is the sub-list for method input_type
	3, // [3:4] is the sub-list for extension type_name
	0, // [0:3] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_proto_gin_proto_init() }
func file_proto_gin_proto_init() {
	if File_proto_gin_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_proto_gin_proto_rawDesc), len(file_proto_gin_proto_rawDesc)),
			NumEnums:      1,
			NumMessages:   0,
			NumExtensions: 3,
			NumServices:   0,
		},
		GoTypes:           file_proto_gin_proto_goTypes,
		DependencyIndexes: file_proto_gin_proto_depIdxs,
		EnumInfos:         file_proto_gin_proto_enumTypes,
		ExtensionInfos:    file_proto_gin_proto_extTypes,
	}.Build()
	File_proto_gin_proto = out.File
	file_proto_gin_proto_goTypes = nil
	file_proto_gin_proto_depIdxs = nil
}
