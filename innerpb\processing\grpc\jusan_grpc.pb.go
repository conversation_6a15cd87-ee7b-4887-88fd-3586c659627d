// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.5.1
// - protoc             v5.29.3
// source: inner/processing/grpc/jusan.proto

package grpc

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.64.0 or later.
const _ = grpc.SupportPackageIsVersion9

const (
	Jusan_PayIn_FullMethodName                              = "/processing.jusan.jusan.Jusan/PayIn"
	Jusan_OneClickPayIn_FullMethodName                      = "/processing.jusan.jusan.Jusan/OneClickPayIn"
	Jusan_ThreeDSConfirm_FullMethodName                     = "/processing.jusan.jusan.Jusan/ThreeDSConfirm"
	Jusan_ThreeDSResume_FullMethodName                      = "/processing.jusan.jusan.Jusan/ThreeDSResume"
	Jusan_PayOut_FullMethodName                             = "/processing.jusan.jusan.Jusan/PayOut"
	Jusan_GetBankTransactionStatus_FullMethodName           = "/processing.jusan.jusan.Jusan/GetBankTransactionStatus"
	Jusan_GetBankTransactionStatusUnformated_FullMethodName = "/processing.jusan.jusan.Jusan/GetBankTransactionStatusUnformated"
	Jusan_Refund_FullMethodName                             = "/processing.jusan.jusan.Jusan/Refund"
	Jusan_GooglePay_FullMethodName                          = "/processing.jusan.jusan.Jusan/GooglePay"
	Jusan_ApplePay_FullMethodName                           = "/processing.jusan.jusan.Jusan/ApplePay"
	Jusan_TwoStagePayIn_FullMethodName                      = "/processing.jusan.jusan.Jusan/TwoStagePayIn"
	Jusan_Charge_FullMethodName                             = "/processing.jusan.jusan.Jusan/Charge"
	Jusan_Cancel_FullMethodName                             = "/processing.jusan.jusan.Jusan/Cancel"
	Jusan_MakeToken_FullMethodName                          = "/processing.jusan.jusan.Jusan/MakeToken"
	Jusan_GetEmission_FullMethodName                        = "/processing.jusan.jusan.Jusan/GetEmission"
	Jusan_ConfirmEmission_FullMethodName                    = "/processing.jusan.jusan.Jusan/ConfirmEmission"
	Jusan_GetAcquirerIdentifier_FullMethodName              = "/processing.jusan.jusan.Jusan/GetAcquirerIdentifier"
	Jusan_CheckBalance_FullMethodName                       = "/processing.jusan.jusan.Jusan/CheckBalance"
	Jusan_ResolveVisaAlias_FullMethodName                   = "/processing.jusan.jusan.Jusan/ResolveVisaAlias"
	Jusan_PayOutByPhone_FullMethodName                      = "/processing.jusan.jusan.Jusan/PayOutByPhone"
)

// JusanClient is the client API for Jusan service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type JusanClient interface {
	PayIn(ctx context.Context, in *PayInRequestData, opts ...grpc.CallOption) (*PayInResponseData, error)
	OneClickPayIn(ctx context.Context, in *OneClickPayInRequestData, opts ...grpc.CallOption) (*PayInResponseData, error)
	ThreeDSConfirm(ctx context.Context, in *ThreeDSRequestData, opts ...grpc.CallOption) (*ThreeDSResponseData, error)
	ThreeDSResume(ctx context.Context, in *ThreeDSResumeRequest, opts ...grpc.CallOption) (*ThreeDSResumeResponse, error)
	PayOut(ctx context.Context, in *PayOutRequestData, opts ...grpc.CallOption) (*PayOutResponseData, error)
	GetBankTransactionStatus(ctx context.Context, in *BankTransactionStatusRequest, opts ...grpc.CallOption) (*BankTransactionStatusResponse, error)
	GetBankTransactionStatusUnformated(ctx context.Context, in *BankTransactionStatusUnformatedRequest, opts ...grpc.CallOption) (*BankTransactionStatusUnformatedResponse, error)
	Refund(ctx context.Context, in *RefundRequest, opts ...grpc.CallOption) (*RefundResponse, error)
	GooglePay(ctx context.Context, in *GooglePayRequestData, opts ...grpc.CallOption) (*GooglePayResponseData, error)
	ApplePay(ctx context.Context, in *ApplePayRequestData, opts ...grpc.CallOption) (*ApplePayResponseData, error)
	TwoStagePayIn(ctx context.Context, in *TwoStagePayInRequest, opts ...grpc.CallOption) (*TwoStagePayInResponse, error)
	Charge(ctx context.Context, in *ChargeRequest, opts ...grpc.CallOption) (*ChargeResponse, error)
	Cancel(ctx context.Context, in *CancelRequest, opts ...grpc.CallOption) (*CancelResponse, error)
	MakeToken(ctx context.Context, in *PayInRequestData, opts ...grpc.CallOption) (*PayInResponseData, error)
	GetEmission(ctx context.Context, in *emptypb.Empty, opts ...grpc.CallOption) (*EmissionResponse, error)
	ConfirmEmission(ctx context.Context, in *EmoneyRequest, opts ...grpc.CallOption) (*EmoneyResponse, error)
	GetAcquirerIdentifier(ctx context.Context, in *emptypb.Empty, opts ...grpc.CallOption) (*GetAcquirerIdentifierResponse, error)
	CheckBalance(ctx context.Context, in *CheckBalanceRequest, opts ...grpc.CallOption) (*CheckBalanceResponse, error)
	ResolveVisaAlias(ctx context.Context, in *ResolveVisaAliasRequest, opts ...grpc.CallOption) (*ResolveVisaAliasResponse, error)
	PayOutByPhone(ctx context.Context, in *PayOutByPhoneRequestData, opts ...grpc.CallOption) (*PayOutResponseByPhoneData, error)
}

type jusanClient struct {
	cc grpc.ClientConnInterface
}

func NewJusanClient(cc grpc.ClientConnInterface) JusanClient {
	return &jusanClient{cc}
}

func (c *jusanClient) PayIn(ctx context.Context, in *PayInRequestData, opts ...grpc.CallOption) (*PayInResponseData, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(PayInResponseData)
	err := c.cc.Invoke(ctx, Jusan_PayIn_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *jusanClient) OneClickPayIn(ctx context.Context, in *OneClickPayInRequestData, opts ...grpc.CallOption) (*PayInResponseData, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(PayInResponseData)
	err := c.cc.Invoke(ctx, Jusan_OneClickPayIn_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *jusanClient) ThreeDSConfirm(ctx context.Context, in *ThreeDSRequestData, opts ...grpc.CallOption) (*ThreeDSResponseData, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ThreeDSResponseData)
	err := c.cc.Invoke(ctx, Jusan_ThreeDSConfirm_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *jusanClient) ThreeDSResume(ctx context.Context, in *ThreeDSResumeRequest, opts ...grpc.CallOption) (*ThreeDSResumeResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ThreeDSResumeResponse)
	err := c.cc.Invoke(ctx, Jusan_ThreeDSResume_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *jusanClient) PayOut(ctx context.Context, in *PayOutRequestData, opts ...grpc.CallOption) (*PayOutResponseData, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(PayOutResponseData)
	err := c.cc.Invoke(ctx, Jusan_PayOut_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *jusanClient) GetBankTransactionStatus(ctx context.Context, in *BankTransactionStatusRequest, opts ...grpc.CallOption) (*BankTransactionStatusResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(BankTransactionStatusResponse)
	err := c.cc.Invoke(ctx, Jusan_GetBankTransactionStatus_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *jusanClient) GetBankTransactionStatusUnformated(ctx context.Context, in *BankTransactionStatusUnformatedRequest, opts ...grpc.CallOption) (*BankTransactionStatusUnformatedResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(BankTransactionStatusUnformatedResponse)
	err := c.cc.Invoke(ctx, Jusan_GetBankTransactionStatusUnformated_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *jusanClient) Refund(ctx context.Context, in *RefundRequest, opts ...grpc.CallOption) (*RefundResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(RefundResponse)
	err := c.cc.Invoke(ctx, Jusan_Refund_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *jusanClient) GooglePay(ctx context.Context, in *GooglePayRequestData, opts ...grpc.CallOption) (*GooglePayResponseData, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GooglePayResponseData)
	err := c.cc.Invoke(ctx, Jusan_GooglePay_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *jusanClient) ApplePay(ctx context.Context, in *ApplePayRequestData, opts ...grpc.CallOption) (*ApplePayResponseData, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ApplePayResponseData)
	err := c.cc.Invoke(ctx, Jusan_ApplePay_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *jusanClient) TwoStagePayIn(ctx context.Context, in *TwoStagePayInRequest, opts ...grpc.CallOption) (*TwoStagePayInResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(TwoStagePayInResponse)
	err := c.cc.Invoke(ctx, Jusan_TwoStagePayIn_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *jusanClient) Charge(ctx context.Context, in *ChargeRequest, opts ...grpc.CallOption) (*ChargeResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ChargeResponse)
	err := c.cc.Invoke(ctx, Jusan_Charge_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *jusanClient) Cancel(ctx context.Context, in *CancelRequest, opts ...grpc.CallOption) (*CancelResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(CancelResponse)
	err := c.cc.Invoke(ctx, Jusan_Cancel_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *jusanClient) MakeToken(ctx context.Context, in *PayInRequestData, opts ...grpc.CallOption) (*PayInResponseData, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(PayInResponseData)
	err := c.cc.Invoke(ctx, Jusan_MakeToken_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *jusanClient) GetEmission(ctx context.Context, in *emptypb.Empty, opts ...grpc.CallOption) (*EmissionResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(EmissionResponse)
	err := c.cc.Invoke(ctx, Jusan_GetEmission_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *jusanClient) ConfirmEmission(ctx context.Context, in *EmoneyRequest, opts ...grpc.CallOption) (*EmoneyResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(EmoneyResponse)
	err := c.cc.Invoke(ctx, Jusan_ConfirmEmission_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *jusanClient) GetAcquirerIdentifier(ctx context.Context, in *emptypb.Empty, opts ...grpc.CallOption) (*GetAcquirerIdentifierResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetAcquirerIdentifierResponse)
	err := c.cc.Invoke(ctx, Jusan_GetAcquirerIdentifier_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *jusanClient) CheckBalance(ctx context.Context, in *CheckBalanceRequest, opts ...grpc.CallOption) (*CheckBalanceResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(CheckBalanceResponse)
	err := c.cc.Invoke(ctx, Jusan_CheckBalance_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *jusanClient) ResolveVisaAlias(ctx context.Context, in *ResolveVisaAliasRequest, opts ...grpc.CallOption) (*ResolveVisaAliasResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ResolveVisaAliasResponse)
	err := c.cc.Invoke(ctx, Jusan_ResolveVisaAlias_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *jusanClient) PayOutByPhone(ctx context.Context, in *PayOutByPhoneRequestData, opts ...grpc.CallOption) (*PayOutResponseByPhoneData, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(PayOutResponseByPhoneData)
	err := c.cc.Invoke(ctx, Jusan_PayOutByPhone_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// JusanServer is the server API for Jusan service.
// All implementations must embed UnimplementedJusanServer
// for forward compatibility.
type JusanServer interface {
	PayIn(context.Context, *PayInRequestData) (*PayInResponseData, error)
	OneClickPayIn(context.Context, *OneClickPayInRequestData) (*PayInResponseData, error)
	ThreeDSConfirm(context.Context, *ThreeDSRequestData) (*ThreeDSResponseData, error)
	ThreeDSResume(context.Context, *ThreeDSResumeRequest) (*ThreeDSResumeResponse, error)
	PayOut(context.Context, *PayOutRequestData) (*PayOutResponseData, error)
	GetBankTransactionStatus(context.Context, *BankTransactionStatusRequest) (*BankTransactionStatusResponse, error)
	GetBankTransactionStatusUnformated(context.Context, *BankTransactionStatusUnformatedRequest) (*BankTransactionStatusUnformatedResponse, error)
	Refund(context.Context, *RefundRequest) (*RefundResponse, error)
	GooglePay(context.Context, *GooglePayRequestData) (*GooglePayResponseData, error)
	ApplePay(context.Context, *ApplePayRequestData) (*ApplePayResponseData, error)
	TwoStagePayIn(context.Context, *TwoStagePayInRequest) (*TwoStagePayInResponse, error)
	Charge(context.Context, *ChargeRequest) (*ChargeResponse, error)
	Cancel(context.Context, *CancelRequest) (*CancelResponse, error)
	MakeToken(context.Context, *PayInRequestData) (*PayInResponseData, error)
	GetEmission(context.Context, *emptypb.Empty) (*EmissionResponse, error)
	ConfirmEmission(context.Context, *EmoneyRequest) (*EmoneyResponse, error)
	GetAcquirerIdentifier(context.Context, *emptypb.Empty) (*GetAcquirerIdentifierResponse, error)
	CheckBalance(context.Context, *CheckBalanceRequest) (*CheckBalanceResponse, error)
	ResolveVisaAlias(context.Context, *ResolveVisaAliasRequest) (*ResolveVisaAliasResponse, error)
	PayOutByPhone(context.Context, *PayOutByPhoneRequestData) (*PayOutResponseByPhoneData, error)
	mustEmbedUnimplementedJusanServer()
}

// UnimplementedJusanServer must be embedded to have
// forward compatible implementations.
//
// NOTE: this should be embedded by value instead of pointer to avoid a nil
// pointer dereference when methods are called.
type UnimplementedJusanServer struct{}

func (UnimplementedJusanServer) PayIn(context.Context, *PayInRequestData) (*PayInResponseData, error) {
	return nil, status.Errorf(codes.Unimplemented, "method PayIn not implemented")
}
func (UnimplementedJusanServer) OneClickPayIn(context.Context, *OneClickPayInRequestData) (*PayInResponseData, error) {
	return nil, status.Errorf(codes.Unimplemented, "method OneClickPayIn not implemented")
}
func (UnimplementedJusanServer) ThreeDSConfirm(context.Context, *ThreeDSRequestData) (*ThreeDSResponseData, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ThreeDSConfirm not implemented")
}
func (UnimplementedJusanServer) ThreeDSResume(context.Context, *ThreeDSResumeRequest) (*ThreeDSResumeResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ThreeDSResume not implemented")
}
func (UnimplementedJusanServer) PayOut(context.Context, *PayOutRequestData) (*PayOutResponseData, error) {
	return nil, status.Errorf(codes.Unimplemented, "method PayOut not implemented")
}
func (UnimplementedJusanServer) GetBankTransactionStatus(context.Context, *BankTransactionStatusRequest) (*BankTransactionStatusResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetBankTransactionStatus not implemented")
}
func (UnimplementedJusanServer) GetBankTransactionStatusUnformated(context.Context, *BankTransactionStatusUnformatedRequest) (*BankTransactionStatusUnformatedResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetBankTransactionStatusUnformated not implemented")
}
func (UnimplementedJusanServer) Refund(context.Context, *RefundRequest) (*RefundResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Refund not implemented")
}
func (UnimplementedJusanServer) GooglePay(context.Context, *GooglePayRequestData) (*GooglePayResponseData, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GooglePay not implemented")
}
func (UnimplementedJusanServer) ApplePay(context.Context, *ApplePayRequestData) (*ApplePayResponseData, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ApplePay not implemented")
}
func (UnimplementedJusanServer) TwoStagePayIn(context.Context, *TwoStagePayInRequest) (*TwoStagePayInResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method TwoStagePayIn not implemented")
}
func (UnimplementedJusanServer) Charge(context.Context, *ChargeRequest) (*ChargeResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Charge not implemented")
}
func (UnimplementedJusanServer) Cancel(context.Context, *CancelRequest) (*CancelResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Cancel not implemented")
}
func (UnimplementedJusanServer) MakeToken(context.Context, *PayInRequestData) (*PayInResponseData, error) {
	return nil, status.Errorf(codes.Unimplemented, "method MakeToken not implemented")
}
func (UnimplementedJusanServer) GetEmission(context.Context, *emptypb.Empty) (*EmissionResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetEmission not implemented")
}
func (UnimplementedJusanServer) ConfirmEmission(context.Context, *EmoneyRequest) (*EmoneyResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ConfirmEmission not implemented")
}
func (UnimplementedJusanServer) GetAcquirerIdentifier(context.Context, *emptypb.Empty) (*GetAcquirerIdentifierResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetAcquirerIdentifier not implemented")
}
func (UnimplementedJusanServer) CheckBalance(context.Context, *CheckBalanceRequest) (*CheckBalanceResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CheckBalance not implemented")
}
func (UnimplementedJusanServer) ResolveVisaAlias(context.Context, *ResolveVisaAliasRequest) (*ResolveVisaAliasResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ResolveVisaAlias not implemented")
}
func (UnimplementedJusanServer) PayOutByPhone(context.Context, *PayOutByPhoneRequestData) (*PayOutResponseByPhoneData, error) {
	return nil, status.Errorf(codes.Unimplemented, "method PayOutByPhone not implemented")
}
func (UnimplementedJusanServer) mustEmbedUnimplementedJusanServer() {}
func (UnimplementedJusanServer) testEmbeddedByValue()               {}

// UnsafeJusanServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to JusanServer will
// result in compilation errors.
type UnsafeJusanServer interface {
	mustEmbedUnimplementedJusanServer()
}

func RegisterJusanServer(s grpc.ServiceRegistrar, srv JusanServer) {
	// If the following call pancis, it indicates UnimplementedJusanServer was
	// embedded by pointer and is nil.  This will cause panics if an
	// unimplemented method is ever invoked, so we test this at initialization
	// time to prevent it from happening at runtime later due to I/O.
	if t, ok := srv.(interface{ testEmbeddedByValue() }); ok {
		t.testEmbeddedByValue()
	}
	s.RegisterService(&Jusan_ServiceDesc, srv)
}

func _Jusan_PayIn_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(PayInRequestData)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(JusanServer).PayIn(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Jusan_PayIn_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(JusanServer).PayIn(ctx, req.(*PayInRequestData))
	}
	return interceptor(ctx, in, info, handler)
}

func _Jusan_OneClickPayIn_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(OneClickPayInRequestData)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(JusanServer).OneClickPayIn(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Jusan_OneClickPayIn_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(JusanServer).OneClickPayIn(ctx, req.(*OneClickPayInRequestData))
	}
	return interceptor(ctx, in, info, handler)
}

func _Jusan_ThreeDSConfirm_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ThreeDSRequestData)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(JusanServer).ThreeDSConfirm(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Jusan_ThreeDSConfirm_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(JusanServer).ThreeDSConfirm(ctx, req.(*ThreeDSRequestData))
	}
	return interceptor(ctx, in, info, handler)
}

func _Jusan_ThreeDSResume_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ThreeDSResumeRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(JusanServer).ThreeDSResume(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Jusan_ThreeDSResume_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(JusanServer).ThreeDSResume(ctx, req.(*ThreeDSResumeRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Jusan_PayOut_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(PayOutRequestData)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(JusanServer).PayOut(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Jusan_PayOut_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(JusanServer).PayOut(ctx, req.(*PayOutRequestData))
	}
	return interceptor(ctx, in, info, handler)
}

func _Jusan_GetBankTransactionStatus_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BankTransactionStatusRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(JusanServer).GetBankTransactionStatus(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Jusan_GetBankTransactionStatus_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(JusanServer).GetBankTransactionStatus(ctx, req.(*BankTransactionStatusRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Jusan_GetBankTransactionStatusUnformated_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BankTransactionStatusUnformatedRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(JusanServer).GetBankTransactionStatusUnformated(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Jusan_GetBankTransactionStatusUnformated_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(JusanServer).GetBankTransactionStatusUnformated(ctx, req.(*BankTransactionStatusUnformatedRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Jusan_Refund_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RefundRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(JusanServer).Refund(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Jusan_Refund_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(JusanServer).Refund(ctx, req.(*RefundRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Jusan_GooglePay_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GooglePayRequestData)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(JusanServer).GooglePay(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Jusan_GooglePay_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(JusanServer).GooglePay(ctx, req.(*GooglePayRequestData))
	}
	return interceptor(ctx, in, info, handler)
}

func _Jusan_ApplePay_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ApplePayRequestData)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(JusanServer).ApplePay(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Jusan_ApplePay_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(JusanServer).ApplePay(ctx, req.(*ApplePayRequestData))
	}
	return interceptor(ctx, in, info, handler)
}

func _Jusan_TwoStagePayIn_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(TwoStagePayInRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(JusanServer).TwoStagePayIn(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Jusan_TwoStagePayIn_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(JusanServer).TwoStagePayIn(ctx, req.(*TwoStagePayInRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Jusan_Charge_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ChargeRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(JusanServer).Charge(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Jusan_Charge_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(JusanServer).Charge(ctx, req.(*ChargeRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Jusan_Cancel_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CancelRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(JusanServer).Cancel(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Jusan_Cancel_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(JusanServer).Cancel(ctx, req.(*CancelRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Jusan_MakeToken_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(PayInRequestData)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(JusanServer).MakeToken(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Jusan_MakeToken_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(JusanServer).MakeToken(ctx, req.(*PayInRequestData))
	}
	return interceptor(ctx, in, info, handler)
}

func _Jusan_GetEmission_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(emptypb.Empty)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(JusanServer).GetEmission(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Jusan_GetEmission_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(JusanServer).GetEmission(ctx, req.(*emptypb.Empty))
	}
	return interceptor(ctx, in, info, handler)
}

func _Jusan_ConfirmEmission_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(EmoneyRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(JusanServer).ConfirmEmission(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Jusan_ConfirmEmission_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(JusanServer).ConfirmEmission(ctx, req.(*EmoneyRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Jusan_GetAcquirerIdentifier_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(emptypb.Empty)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(JusanServer).GetAcquirerIdentifier(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Jusan_GetAcquirerIdentifier_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(JusanServer).GetAcquirerIdentifier(ctx, req.(*emptypb.Empty))
	}
	return interceptor(ctx, in, info, handler)
}

func _Jusan_CheckBalance_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CheckBalanceRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(JusanServer).CheckBalance(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Jusan_CheckBalance_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(JusanServer).CheckBalance(ctx, req.(*CheckBalanceRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Jusan_ResolveVisaAlias_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ResolveVisaAliasRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(JusanServer).ResolveVisaAlias(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Jusan_ResolveVisaAlias_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(JusanServer).ResolveVisaAlias(ctx, req.(*ResolveVisaAliasRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Jusan_PayOutByPhone_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(PayOutByPhoneRequestData)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(JusanServer).PayOutByPhone(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Jusan_PayOutByPhone_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(JusanServer).PayOutByPhone(ctx, req.(*PayOutByPhoneRequestData))
	}
	return interceptor(ctx, in, info, handler)
}

// Jusan_ServiceDesc is the grpc.ServiceDesc for Jusan service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var Jusan_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "processing.jusan.jusan.Jusan",
	HandlerType: (*JusanServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "PayIn",
			Handler:    _Jusan_PayIn_Handler,
		},
		{
			MethodName: "OneClickPayIn",
			Handler:    _Jusan_OneClickPayIn_Handler,
		},
		{
			MethodName: "ThreeDSConfirm",
			Handler:    _Jusan_ThreeDSConfirm_Handler,
		},
		{
			MethodName: "ThreeDSResume",
			Handler:    _Jusan_ThreeDSResume_Handler,
		},
		{
			MethodName: "PayOut",
			Handler:    _Jusan_PayOut_Handler,
		},
		{
			MethodName: "GetBankTransactionStatus",
			Handler:    _Jusan_GetBankTransactionStatus_Handler,
		},
		{
			MethodName: "GetBankTransactionStatusUnformated",
			Handler:    _Jusan_GetBankTransactionStatusUnformated_Handler,
		},
		{
			MethodName: "Refund",
			Handler:    _Jusan_Refund_Handler,
		},
		{
			MethodName: "GooglePay",
			Handler:    _Jusan_GooglePay_Handler,
		},
		{
			MethodName: "ApplePay",
			Handler:    _Jusan_ApplePay_Handler,
		},
		{
			MethodName: "TwoStagePayIn",
			Handler:    _Jusan_TwoStagePayIn_Handler,
		},
		{
			MethodName: "Charge",
			Handler:    _Jusan_Charge_Handler,
		},
		{
			MethodName: "Cancel",
			Handler:    _Jusan_Cancel_Handler,
		},
		{
			MethodName: "MakeToken",
			Handler:    _Jusan_MakeToken_Handler,
		},
		{
			MethodName: "GetEmission",
			Handler:    _Jusan_GetEmission_Handler,
		},
		{
			MethodName: "ConfirmEmission",
			Handler:    _Jusan_ConfirmEmission_Handler,
		},
		{
			MethodName: "GetAcquirerIdentifier",
			Handler:    _Jusan_GetAcquirerIdentifier_Handler,
		},
		{
			MethodName: "CheckBalance",
			Handler:    _Jusan_CheckBalance_Handler,
		},
		{
			MethodName: "ResolveVisaAlias",
			Handler:    _Jusan_ResolveVisaAlias_Handler,
		},
		{
			MethodName: "PayOutByPhone",
			Handler:    _Jusan_PayOutByPhone_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "inner/processing/grpc/jusan.proto",
}
