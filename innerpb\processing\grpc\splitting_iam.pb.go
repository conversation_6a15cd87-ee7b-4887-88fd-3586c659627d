// Code generated by protoc-gen-go-mvp. DO NOT EDIT.
// versions:
// - protoc-gen-go-mvp v2.0.1
// created by mvp authors

package grpc

import (
	context "context"
	grpc "google.golang.org/grpc"
	metadata "google.golang.org/grpc/metadata"
)

func NewIamSplittingServer(
	srv SplittingServer,
) SplittingServer {
	return &iamSplittingServer{
		srv: srv,
	}
}

var _ SplittingServer = (*iamSplittingServer)(nil)

type iamSplittingServer struct {
	UnimplementedSplittingServer

	srv SplittingServer
}

func (s *iamSplittingServer) CalculatePaymentSplitTax(
	ctx context.Context,
	req *CalculatePaymentSplitTaxRequest,
) (
	*CalculatePaymentSplitTaxResponse,
	error,
) {
	return s.srv.CalculatePaymentSplitTax(ctx, req)
}

func NewIamSplittingClient(
	client SplittingClient,
) SplittingClient {
	return &iamSplittingClient{
		client: client,
	}
}

type iamSplittingClient struct {
	client SplittingClient
}

func (s *iamSplittingClient) CalculatePaymentSplitTax(
	ctx context.Context,
	req *CalculatePaymentSplitTaxRequest,
	opts ...grpc.CallOption,
) (
	*CalculatePaymentSplitTaxResponse,
	error,
) {
	md, _ := metadata.FromIncomingContext(ctx)
	return s.client.CalculatePaymentSplitTax(metadata.NewOutgoingContext(ctx, md), req)
}
