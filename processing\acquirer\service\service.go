package service

import (
	"context"

	gorpc "git.local/sensitive/innerpb/processing/grpc"
	"git.local/sensitive/pkg/gtransaction"
	"git.local/sensitive/pkg/middlewares"
	"git.local/sensitive/processing/acquirer/model"
	"git.local/sensitive/processing/acquirer/repository"
	"git.local/sensitive/processing/acquirer/schema"
)

type Banker interface {
	Create(ctx context.Context, request *schema.Bank) error
	GetAll(
		ctx context.Context,
		pagination *middlewares.PaginationInfo,
		filter schema.BankFilter,
	) ([]*model.Bank, error)
	Update(ctx context.Context, id uint64, request *schema.Bank) error
	Delete(ctx context.Context, id uint64) error
	GetByID(ctx context.Context, id uint64) (*model.Bank, error)
	GetBanksByName(ctx context.Context, name string, pagination *middlewares.PaginationInfo) ([]*model.Bank, error)
}

type BankBiner interface {
	Create(ctx context.Context, request schema.BankBin) error
	GetAll(ctx context.Context, pagination *middlewares.PaginationInfo) ([]*model.BankBin, error)
	Update(ctx context.Context, id uint64, request *schema.BankBin) error
	Delete(ctx context.Context, id uint64) error
	GetByPan(ctx context.Context, eightDigitBin string, sixDigitBin string, fiveDigitBin string) (*model.BankBin, error)
}

type Ipser interface {
	Create(ctx context.Context, request *schema.Ips) error
	GetAll(ctx context.Context, pagination *middlewares.PaginationInfo) ([]*model.Ips, error)
	Update(ctx context.Context, id uint64, request *schema.Ips) error
	Delete(ctx context.Context, id uint64) error
	GetByID(ctx context.Context, id uint64) (model.Ips, error)
}

type Countrier interface {
	Create(ctx context.Context, request *schema.Country) error
	GetAll(ctx context.Context, pagination *middlewares.PaginationInfo) ([]*model.Country, error)
	GetCountriesByName(
		ctx context.Context, name string, pagination *middlewares.PaginationInfo) ([]*model.CountryBasic, error)
	Update(ctx context.Context, id uint64, request *schema.Country) error
	Delete(ctx context.Context, id uint64) error
	GetByID(ctx context.Context, id uint64) (model.Country, error)
}

type Rule interface {
	Create(ctx context.Context, request schema.CreateRuleRequest) (*model.Rule, error)
	CreateBase(ctx context.Context, request schema.CreateBaseRuleRequest) (*model.Rule, error)
	GetList(
		ctx context.Context,
		pagination *middlewares.PaginationInfo,
		request schema.RuleListRequest,
	) (rules []*model.Rule, err error)
}

type Balancer interface {
	Search(ctx context.Context, request schema.RuleSearchRequest, excludedAcquirers ...string) (*model.Terminal, error)
	ExtendedSearch(ctx context.Context, request schema.ExtendedRuleSearchRequest) (*model.Terminal, error)
	GetRuleByActiveTerminals(ctx context.Context, request *schema.RuleByActiveTerminalsRequest) (*model.Rule, error)
	RuleUp(ctx context.Context, ruleID uint64) error
	RuleDown(ctx context.Context, ruleID uint64) error
}

type RuleActivator interface {
	Activate(ctx context.Context, ruleID uint64) error
	Deactivate(ctx context.Context, ruleID uint64) error
}

type RulePercentage interface {
	Replace(ctx context.Context, request schema.CreateRulePercentageRequest, ruleID uint64) error
	GetAllByRuleID(ctx context.Context, ruleID uint64) (model.RulePercentages, error)
}

type Acquirer interface {
	//Отключить все терминалы (статус - 3), смотрит где терминал включен (статус - 1)
	DeactivateTerminals(ctx context.Context, acquirerId uint64) (result schema.UpdateTerminalsResponse, err error)
	//Включить все терминалы (статус - 1), смотрит где терминал отключен (статус - 3)
	ActivateTerminals(ctx context.Context, acquirerId uint64) (result schema.UpdateTerminalsResponse, err error)
	GetByCode(ctx context.Context, acquirerCode string) (*model.Acquirer, error)
}

type AcquirerBasicer interface {
	Create(ctx context.Context, request *schema.Acquirer) error
	Update(ctx context.Context, id uint64, request *schema.Acquirer) error
	GetAllByFilter(
		ctx context.Context,
		filter schema.AcquirerFilter,
		pagination *middlewares.PaginationInfo,
	) ([]schema.AcquirerTerminal, error)
	GetByID(ctx context.Context, acquirerID uint64) (*model.Acquirer, error)
	GetAllActive(ctx context.Context, pagination *middlewares.PaginationInfo) ([]*model.Acquirer, error)
}

type Terminaler interface {
	Create(ctx context.Context, terminal *schema.CreateTerminalRequest) (*schema.TerminalResponse, error)
	GetAll(ctx context.Context, projectID uint64) ([]model.Terminal, error)
	GetAllInfoByID(ctx context.Context, id uint64) (*model.Terminal, error)
	//Обновление статус терминала (включаем/выключаем)
	UpdateStatus(ctx context.Context, id uint64) error
	GetByFilters(
		ctx context.Context,
		filters schema.FiltersTerminalRequest,
		pagination *middlewares.PaginationInfo,
	) ([]*schema.TerminalResponse, error)
}

type TerminalActiver interface {
	FindActiveTerminalsByProject(
		ctx context.Context,
		projectID uint64,
		paymentType uint64,
	) (model.Terminals, error)
	FindActiveTerminalsByIDs(
		ctx context.Context,
		request *schema.ActiveTerminalsByIDsReq,
	) ([]*model.Terminal, error)
}

type BasicTerminaler interface {
	GetByParams(ctx context.Context, projectID, acquirerID, transactionTypeID uint64) (model.Terminal, error)
	GetTerminalsByIDs(ctx context.Context, terminalIDs []uint64) ([]model.Terminal, error)
}

type TerminalConfiger interface {
	UpdateConfig(ctx context.Context, id uint64, config map[string]any) error
	GetConfig(ctx context.Context, id uint64) (schema.DecryptedConfig, error)
}

type TerminalTwoStager interface {
	UpdateTimeout(ctx context.Context, id uint64, timeout uint32) error
}

type TerminalBalancer interface {
	GetTerminalsBalanceByProjectID(ctx context.Context, projectID uint64) ([]schema.GetBalanceResponse, error)
}

type Projector interface {
	GetAcquirers(ctx context.Context, projectID uint64) ([]*model.Acquirer, error)
	GetTerminals(ctx context.Context, projectID uint64) ([]*schema.TerminalResponse, error)
	GetAcquirersByPaymentType(
		ctx context.Context,
		projectID, transactionTypeID uint64,
	) ([]*model.Acquirer, error)
	GetPaymentTypes(
		ctx context.Context,
		projectID uint64,
	) ([]*schema.ResponsePaymentType, error)
}

type Issuer interface {
	GetAll(
		ctx context.Context,
		pagination *middlewares.PaginationInfo,
	) ([]*model.BankBins, error)
	GetByID(ctx context.Context, id uint64) (model.Bank, error)
}

type CountryBanker interface {
	Create(ctx context.Context, request *schema.CountryBank) error
	Delete(ctx context.Context, countryId uint64, currencyId uint64) error
	GetCountriesByBank(
		ctx context.Context,
		bankId uint64,
	) ([]*model.Country, error)
	GetBanksByCountryID(
		ctx context.Context,
		countryID uint64,
	) (_ []*model.Bank, err error)
}

type TerminalProjecter interface {
	Create(ctx context.Context, request schema.TerminalProjectRequest) error
	Update(ctx context.Context, id uint64, request schema.UpdateTerminalProjectRequest) error
	UpdateStatus(ctx context.Context, id uint64, request schema.UpdateTerminalProjectStatusRequest) error
	GetPayInProjectTerminals(ctx context.Context, id uint64) ([]*schema.GetTerminalProjectPayIn, error)
}

type Services struct {
	Bank             Banker
	Ips              Ipser
	Country          Countrier
	BankBin          BankBiner
	RulePercentage   RulePercentage
	Rule             Rule
	Balancer         Balancer
	RuleActivator    RuleActivator
	Acquirer         Acquirer
	AcquirerBasic    AcquirerBasicer
	Terminal         Terminaler
	TerminalActive   TerminalActiver
	TerminalProject  TerminalProjecter
	Project          Projector
	Issuer           Issuer
	CountryBank      CountryBanker
	TerminalConfiger TerminalConfiger
	TerminalTwoStage TerminalTwoStager
	TerminalBalancer TerminalBalancer
	BasicTerminaler  BasicTerminaler
}

func NewServices(
	transactionManager gtransaction.Manager,
	repos *repository.Repositories,
	transactionTypeClient gorpc.TransactionTypeClient,
	billingClient gorpc.BillingClient,
	multiacquiringBalanceClient gorpc.MultiacquiringBalanceClient,
) *Services {
	return &Services{
		Bank:           NewBankService(repos.BankDB),
		BankBin:        NewBankBinService(repos.BankBinDB),
		Ips:            NewIpsService(repos.IpsDB),
		Country:        NewCountryService(repos.CountryDB),
		RulePercentage: NewRulePercentageService(repos.AcquirerDB, repos.RulePercentageDB, transactionManager),
		Rule:           NewRuleService(repos.RuleDB, repos.BankDB, repos.RulePercentageDB, transactionManager),
		Balancer: NewBalancerService(
			repos.TerminalDB,
			repos.RuleDB,
			repos.RuleWeighterDB,
			repos.BasicAcquirerDB,
			transactionManager,
			billingClient,
			transactionTypeClient,
		),
		RuleActivator:  NewRuleActivatorService(repos.RuleActivatorDB, repos.RuleDB),
		Acquirer:       NewAcquirerService(repos.TerminalDB, repos.AcquirerDB, repos.BasicAcquirerDB),
		AcquirerBasic:  NewAcquirerBasicService(repos.TerminalBasicDB, repos.BasicAcquirerDB, transactionManager),
		Terminal:       NewTerminalService(repos.TerminalBasicDB, repos.TerminalDB),
		TerminalActive: NewTerminalActiveService(repos.TerminalDB),
		Project: NewProjectService(
			repos.TerminalBasicDB,
			repos.AcquirerDB,
			repos.TerminalDB,
			transactionTypeClient,
		),
		Issuer:           NewIssuerService(repos.IssuerDB),
		CountryBank:      NewCountryBankService(repos.CountryBankDB),
		TerminalConfiger: NewTerminalConfigService(repos.TerminalDB, repos.TerminalBasicDB),
		TerminalTwoStage: NewTerminalTwoStageService(repos.TerminalTwoStageDB),
		TerminalBalancer: NewTerminalBalanceService(
			repos.TerminalBalanceDB,
			multiacquiringBalanceClient,
			transactionTypeClient,
		),
		TerminalProject: NewTerminalProjectService(repos.TerminalProjectDB, transactionTypeClient),
		BasicTerminaler: NewBasicTerminalService(repos.TerminalBalanceDB),
	}
}
