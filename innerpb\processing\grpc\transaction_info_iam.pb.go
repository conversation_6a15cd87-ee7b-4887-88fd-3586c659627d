// Code generated by protoc-gen-go-mvp. DO NOT EDIT.
// versions:
// - protoc-gen-go-mvp v2.0.1
// created by mvp authors

package grpc

import (
	context "context"
	grpc "google.golang.org/grpc"
	metadata "google.golang.org/grpc/metadata"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
)

func NewIamTransactionInfServer(
	srv TransactionInfServer,
) TransactionInfServer {
	return &iamTransactionInfServer{
		srv: srv,
	}
}

var _ TransactionInfServer = (*iamTransactionInfServer)(nil)

type iamTransactionInfServer struct {
	UnimplementedTransactionInfServer

	srv TransactionInfServer
}

func (s *iamTransactionInfServer) UpdateJobsMessage(
	ctx context.Context,
	req *UpdateJobsMessageRequestV1,
) (
	*emptypb.Empty,
	error,
) {
	return s.srv.UpdateJobsMessage(ctx, req)
}

func (s *iamTransactionInfServer) UpdateBankResponseMessage(
	ctx context.Context,
	req *UpdateBankResponseMessageRequestV1,
) (
	*emptypb.Empty,
	error,
) {
	return s.srv.UpdateBankResponseMessage(ctx, req)
}

func (s *iamTransactionInfServer) GetTransactionsWithEmptyBankReferenceID(
	ctx context.Context,
	req *emptypb.Empty,
) (
	*GetTransactionsWithEmptyBankReferenceIDResponseV1,
	error,
) {
	return s.srv.GetTransactionsWithEmptyBankReferenceID(ctx, req)
}

func (s *iamTransactionInfServer) UpdateBankReferenceID(
	ctx context.Context,
	req *UpdateBankReferenceIDRequestV1,
) (
	*emptypb.Empty,
	error,
) {
	return s.srv.UpdateBankReferenceID(ctx, req)
}

func NewIamTransactionInfClient(
	client TransactionInfClient,
) TransactionInfClient {
	return &iamTransactionInfClient{
		client: client,
	}
}

type iamTransactionInfClient struct {
	client TransactionInfClient
}

func (s *iamTransactionInfClient) UpdateJobsMessage(
	ctx context.Context,
	req *UpdateJobsMessageRequestV1,
	opts ...grpc.CallOption,
) (
	*emptypb.Empty,
	error,
) {
	md, _ := metadata.FromIncomingContext(ctx)
	return s.client.UpdateJobsMessage(metadata.NewOutgoingContext(ctx, md), req)
}

func (s *iamTransactionInfClient) UpdateBankResponseMessage(
	ctx context.Context,
	req *UpdateBankResponseMessageRequestV1,
	opts ...grpc.CallOption,
) (
	*emptypb.Empty,
	error,
) {
	md, _ := metadata.FromIncomingContext(ctx)
	return s.client.UpdateBankResponseMessage(metadata.NewOutgoingContext(ctx, md), req)
}

func (s *iamTransactionInfClient) GetTransactionsWithEmptyBankReferenceID(
	ctx context.Context,
	req *emptypb.Empty,
	opts ...grpc.CallOption,
) (
	*GetTransactionsWithEmptyBankReferenceIDResponseV1,
	error,
) {
	md, _ := metadata.FromIncomingContext(ctx)
	return s.client.GetTransactionsWithEmptyBankReferenceID(metadata.NewOutgoingContext(ctx, md), req)
}

func (s *iamTransactionInfClient) UpdateBankReferenceID(
	ctx context.Context,
	req *UpdateBankReferenceIDRequestV1,
	opts ...grpc.CallOption,
) (
	*emptypb.Empty,
	error,
) {
	md, _ := metadata.FromIncomingContext(ctx)
	return s.client.UpdateBankReferenceID(metadata.NewOutgoingContext(ctx, md), req)
}
