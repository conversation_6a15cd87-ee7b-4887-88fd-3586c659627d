package service

import (
	"context"
	"time"

	gorpc "git.local/sensitive/innerpb/processing/grpc"
	"git.local/sensitive/processing/account/model"
	"git.local/sensitive/processing/account/repository"
	"git.local/sensitive/processing/account/schema"
	"git.local/sensitive/sdk/dog"
)

type OldTransfersFinalizerService struct {
	transferStatusDB repository.TransferStatuser
	transferDB       repository.Transferer
	accountInfoDB    repository.AccountInformer
	transferManageDB repository.TransferManager
	workerCreatedAtFromHours,
	workerCreatedAtToHours string
	multiAccountingClient gorpc.MultiaccountingClient
	billingClient         gorpc.BillingClient
	nowFn                 func() time.Time
}

func NewOldTransfersFinalizerService(
	transferStatusDB repository.TransferStatuser,
	transferDB repository.Transferer,
	accountInfoDB repository.AccountInformer,
	transferManageDB repository.TransferManager,
	multiAccountingClient gorpc.MultiaccountingClient,
	billingClient gorpc.BillingClient,
	workerCreatedAtFromHours,
	workerCreatedAtToHours string,
) *OldTransfersFinalizerService {
	return &OldTransfersFinalizerService{
		transferStatusDB:         transferStatusDB,
		transferDB:               transferDB,
		accountInfoDB:            accountInfoDB,
		transferManageDB:         transferManageDB,
		workerCreatedAtFromHours: workerCreatedAtFromHours,
		workerCreatedAtToHours:   workerCreatedAtToHours,
		multiAccountingClient:    multiAccountingClient,
		billingClient:            billingClient,
		nowFn:                    time.Now,
	}
}

func (o *OldTransfersFinalizerService) FinalizeOldTransfers(
	ctx context.Context,
) error {
	ctx, span := dog.CreateSpan(ctx, "TransferInfoService_GetTransferByPeriod")
	defer span.End()

	parseCreatedAtFrom, err := time.ParseDuration(o.workerCreatedAtFromHours)
	if err != nil {
		return err
	}
	parseCreatedAtTO, err := time.ParseDuration(o.workerCreatedAtToHours)
	if err != nil {
		return err
	}

	currentTime := o.nowFn()
	createdDateFrom := currentTime.Add(parseCreatedAtFrom)
	createdDateTo := currentTime.Add(parseCreatedAtTO)

	statusMap, err := o.transferStatusDB.AsCodeMap(ctx)
	if err != nil {
		return err
	}
	statusID := statusMap[model.StatusAuthorized]

	transfers, err := o.transferDB.GetByPeriod(ctx, createdDateFrom, createdDateTo, statusID)
	if err != nil {
		return err
	}

	for _, transfer := range transfers {
		account, err := o.accountInfoDB.GetById(ctx, transfer.AccountID)
		if err != nil {
			return err
		}

		convertedAccountInfo, err := schema.AccountInfoConverter(account)
		if err != nil {
			return err
		}

		rawRequest := schema.NewRawGetTransferStatusRequest(&transfer, convertedAccountInfo)

		transferDetail, err := o.multiAccountingClient.GetTransferDetails(ctx, &gorpc.GetTransferDetailsRequest{
			TransferId: rawRequest.TransferId,
			Account: &gorpc.AccountTerminal{
				Id:       rawRequest.GetAccount().Id,
				BankCode: rawRequest.GetAccount().BankCode,
				Config:   rawRequest.GetAccount().GetConfig(),
			},
			ExternalReferenceId: rawRequest.ExternalReferenceId,
		})
		if err != nil {
			return err
		}

		detailStatus := transferDetail.GetTransferStatusId()

		if detailStatus != statusMap[model.StatusAuthorized] {
			status, err := o.transferStatusDB.GetByID(ctx, detailStatus)
			if err != nil {
				return err
			}

			var finishedAt *time.Time
			if status.Code.IsFinal() {
				now := o.nowFn()
				finishedAt = &now
			}

			err = o.transferManageDB.UpdateStatus(ctx, transfer.ID, detailStatus, finishedAt)
			if err != nil {
				return err
			}
		}

		if detailStatus == statusMap[model.StatusSuccess] {
			switch transfer.TransferType.Code {
			case model.TypeIn:
				_, err = o.billingClient.BillInTransferV1(ctx, &gorpc.BillInTransferRequestV1{
					ProjectId:     &transfer.ProjectID,
					AccountNumber: &account.Number,
					Amount:        &transfer.Amount,
					MerchantId:    &transfer.MerchantID,
					TransferId:    &transfer.ID,
				})

			case model.TypeOut:
				_, err = o.billingClient.BillOutTransferV1(ctx, &gorpc.BillOutTransferRequestV1{
					ProjectId:     &transfer.ProjectID,
					AccountNumber: &account.Number,
					Amount:        &transfer.Amount,
					MerchantId:    &transfer.MerchantID,
					TransferId:    &transfer.ID,
				})
			}

			if err != nil {
				err = o.transferManageDB.UpdateStatus(ctx,
					transfer.ID,
					statusMap[model.StatusAuthorized],
					nil,
				)
				if err != nil {
					return err
				}
			}
		}
	}

	return nil
}
