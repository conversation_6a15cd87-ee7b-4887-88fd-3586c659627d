edition = "2023";

package processing.jusan_tokenize.jusan_tokenize;

option go_package = "git.local/sensitive/innerpb/processing/grpc";

import "inner/processing/grpc/transaction_status.proto";
import "inner/processing/grpc/integration.proto";
import "mvp/proto/refs.proto";
import "mvp/proto/enum.proto";
import "google/protobuf/descriptor.proto";

message JusanResponseCodeTokenizeRef {
  string code = 1;
  string description = 2;
  transaction.transaction_status.EnumTransactionStatus transaction_status = 3;
  integration.integration.IntegrationError integration_error = 4;
}

extend google.protobuf.EnumValueOptions {
  JusanResponseCodeTokenizeRef jusan_response_code_tokenize_value = 100113;
}

extend google.protobuf.EnumOptions {
  JusanResponseCodeTokenizeRef default_jusan_response_code_tokenize_value = 100114;
}

enum JusanResponseCodeTokenize {
  option(mvp.default_ref) = "default_jusan_response_code_tokenize_value";
  option(mvp.ref) = "jusan_response_code_tokenize_value";
  option(default_jusan_response_code_tokenize_value) = {
    code: "0"
    description: "default"
    transaction_status: TransactionStatusError
    integration_error: None
  };

  TokenizeServiceUnavailable = 0 [(jusan_response_code_tokenize_value) = {
    code: "11"
    description: "Сервис временно недоступен, попробуйте позже"
    transaction_status: TransactionStatusFailed
    integration_error: UnavailableAcquirer
  }, (mvp.from_string) = "11"];

  TokenizeIncorrectFieldOrder = 1 [(jusan_response_code_tokenize_value) = {
    code: "12"
    description: "Неправильное значение в поле ORDER:"
    transaction_status: TransactionStatusFailed
    integration_error: TransactionDeclinedByAcquirer
  }, (mvp.from_string) = "12"];

  TokenizeIncorrectAmount = 2 [(jusan_response_code_tokenize_value) = {
    code: "13"
    description: "Неправильная сумма: "
    transaction_status: TransactionStatusFailed
    integration_error: TransactionDeclinedByAcquirer
  }, (mvp.from_string) = "13"];

  TokenizeIncorrectCurrency = 3 [(jusan_response_code_tokenize_value) = {
    code: "14"
    description: "Неправильная валюта:"
    transaction_status: TransactionStatusFailed
    integration_error: TransactionDeclinedByAcquirer
  }, (mvp.from_string) = "14"];

  TokenizeUnavailableMPI = 4 [(jusan_response_code_tokenize_value) = {
    code: "15"
    description: "Сервис MPI временно недоступен, попробуйте позже"
    transaction_status: TransactionStatusFailed
    integration_error: UnavailableAcquirer
  }, (mvp.from_string) = "15"];

  TokenizeUnavailableDb = 5 [(jusan_response_code_tokenize_value) = {
    code: "16"
    description: "Сервис Db временно недоступен, попробуйте позже"
    transaction_status: TransactionStatusFailed
    integration_error: UnavailableAcquirer
  }, (mvp.from_string) = "16"];

  TokenizeOperationForbidden = 6 [(jusan_response_code_tokenize_value) = {
    code: "171"
    description: "Коммерсанту запрещено выполнение операций"
    transaction_status: TransactionStatusFailed
    integration_error: PaymentForbiddenForMerchant
  }, (mvp.from_string) = "171"];

  TokenizeOperationForbiddenByLaw = 7 [(jusan_response_code_tokenize_value) = {
    code: "172 "
    description: "Коммерсанту запрещено проведение операций в соответствии с Законом о ПОД/ФТ"
    transaction_status: TransactionStatusFailed
    integration_error: PaymentForbiddenForMerchant
  }, (mvp.from_string) = "172 "];

  TokenizeRequestAlreadyCompleted = 8 [(jusan_response_code_tokenize_value) = {
    code: "18"
    description: "Запрос уже выполнялся"
    transaction_status: TransactionStatusFailed
    integration_error: TransactionDeclinedByAcquirer
  }, (mvp.from_string) = "18"];

  TokenizeIncorrectCardExpDate = 9 [(jusan_response_code_tokenize_value) = {
    code: "19"
    description: "Неправильная дата дейстия карты (MM/ГГ)"
    transaction_status: TransactionStatusFailed
    integration_error: IncorrectCardExpDate
  }, (mvp.from_string) = "19"];

  TokenizeIncorrectFieldTerminal = 10 [(jusan_response_code_tokenize_value) = {
    code: "20"
    description: "Неправильное значение в поле TERMINAL:"
    transaction_status: TransactionStatusFailed
    integration_error: TransactionDeclinedByAcquirer
  }, (mvp.from_string) = "20"];

  TokenizeIncorrectSign = 11 [(jusan_response_code_tokenize_value) = {
    code: "21"
    description: "Неправильная подпись!"
    transaction_status: TransactionStatusFailed
    integration_error: TransactionDeclinedByAcquirer
  }, (mvp.from_string) = "21"];

  TokenizeCurrencyNotFound = 12 [(jusan_response_code_tokenize_value) = {
    code: "22"
    description: "Не найден курс валюты"
    transaction_status: TransactionStatusFailed
    integration_error: TransactionDeclinedByAcquirer
  }, (mvp.from_string) = "22"];

  TokenizeLimitExceeded = 13 [(jusan_response_code_tokenize_value) = {
    code: "23"
    description: "Превышен лимит!"
    transaction_status: TransactionStatusFailed
    integration_error: ExceedsAmountLimit
  }, (mvp.from_string) = "23"];

  TokenizeEmptyField = 14 [(jusan_response_code_tokenize_value) = {
    code: "24"
    description: "Не указано значение в поле"
    transaction_status: TransactionStatusFailed
    integration_error: TransactionDeclinedByAcquirer
  }, (mvp.from_string) = "24"];

  TokenizeFieldSizeLessSymbols = 15 [(jusan_response_code_tokenize_value) = {
    code: "25"
    description: "Размер значения в поле менее симоволов"
    transaction_status: TransactionStatusFailed
    integration_error: TransactionDeclinedByAcquirer
  }, (mvp.from_string) = "25"];

  TokenizeFieldSizeMoreSymbols = 16 [(jusan_response_code_tokenize_value) = {
    code: "26"
    description: "Размер значения в поле больше симоволов"
    transaction_status: TransactionStatusFailed
    integration_error: TransactionDeclinedByAcquirer
  }, (mvp.from_string) = "26"];

  TokenizeInvalidField = 17 [(jusan_response_code_tokenize_value) = {
    code: "27"
    description: "Введите валидное значение в поле"
    transaction_status: TransactionStatusFailed
    integration_error: TransactionDeclinedByAcquirer
  }, (mvp.from_string) = "27"];

  TokenizeMPIError3DS = 18 [(jusan_response_code_tokenize_value) = {
    code: "28"
    description: "Ошибка MPI при выполнении проверки 3DS:"
    transaction_status: TransactionStatusFailed
    integration_error: ThreeDSAuthFailed
  }, (mvp.from_string) = "28"];

  TokenizeUnacceptableCardType = 19 [(jusan_response_code_tokenize_value) = {
    code: "29"
    description: "Недопустимый тип карты"
    transaction_status: TransactionStatusFailed
    integration_error: InvalidCard
  }, (mvp.from_string) = "29"];

  TokenizePaymentNotFound = 20 [(jusan_response_code_tokenize_value) = {
    code: "30"
    description: "Счет на оплату не найден"
    transaction_status: TransactionStatusFailed
    integration_error: TransactionDeclinedByAcquirer
  }, (mvp.from_string) = "30"];

  TokenizeClientKeyNotFound = 21 [(jusan_response_code_tokenize_value) = {
    code: "31"
    description: "Не передан ключ указанного клиента"
    transaction_status: TransactionStatusFailed
    integration_error: TransactionDeclinedByAcquirer
  }, (mvp.from_string) = "31"];

  TokenizeForbidden = 22 [(jusan_response_code_tokenize_value) = {
    code: "32"
    description: "Для терминала запрещена токенизация"
    transaction_status: TransactionStatusFailed
    integration_error: PaymentForbiddenForMerchant
  }, (mvp.from_string) = "32"];

  TokenizeTokenNotFound = 23 [(jusan_response_code_tokenize_value) = {
    code: "33"
    description: "Для данного клиента в вашей организации не зарегистрирован токен"
    transaction_status: TransactionStatusFailed
    integration_error: InvalidCard
  }, (mvp.from_string) = "33"];

  TokenizeIncorrectBlockAmount = 24 [(jusan_response_code_tokenize_value) = {
    code: "34"
    description: "Неверная сумма блокирования, заявка отменена!"
    transaction_status: TransactionStatusFailed
    integration_error: TransactionDeclinedByAcquirer
  }, (mvp.from_string) = "34"];

  TokenizeUnknownError = 25 [(jusan_response_code_tokenize_value) = {
    code: "99"
    description: "Неизвестная ошибка: "
    transaction_status: TransactionStatusHolded
    integration_error: TransactionDeclinedByAcquirer
  }, (mvp.from_string) = "99"];

  TokenizeServiceUnavailableTryLater = 26 [(jusan_response_code_tokenize_value) = {
    code: "41"
    description: "Сервис временно недоступен, попробуйте позже"
    transaction_status: TransactionStatusFailed
    integration_error: UnavailableAcquirer
  }, (mvp.from_string) = "41"];

  TokenizeInvalidAmount = 27 [(jusan_response_code_tokenize_value) = {
    code: "42"
    description: "Неправильная сумма"
    transaction_status: TransactionStatusFailed
    integration_error: TransactionDeclinedByAcquirer
  }, (mvp.from_string) = "42"];

  TokenizeServiceDbUnavailable = 28 [(jusan_response_code_tokenize_value) = {
    code: "43"
    description: "Сервис Db временно недоступен, попробуйте позже "
    transaction_status: TransactionStatusFailed
    integration_error: UnavailableAcquirer
  }, (mvp.from_string) = "43"];

  TokenizeIncorrectFieldMerchant = 29 [(jusan_response_code_tokenize_value) = {
    code: "44"
    description: "Неправильное значение в поле MERCHANT"
    transaction_status: TransactionStatusFailed
    integration_error: TransactionDeclinedByAcquirer
  }, (mvp.from_string) = "44"];

  TokenizeMerchantNotFound = 30 [(jusan_response_code_tokenize_value) = {
    code: "17"
    description: "Коммерсант не найден"
    transaction_status: TransactionStatusFailed
    integration_error: PaymentForbiddenForMerchant
  }, (mvp.from_string) = "17"];

  TokenizeOrderRequestNotFound = 31 [(jusan_response_code_tokenize_value) = {
    code: "45"
    description: "Заявка ORDER не найдена"
    transaction_status: TransactionStatusFailed
    integration_error: TransactionDeclinedByAcquirer
  }, (mvp.from_string) = "45"];

  TokenizeInvalidSign = 32 [(jusan_response_code_tokenize_value) = {
    code: "46"
    description: "Неправильная подпись!"
    transaction_status: TransactionStatusFailed
    integration_error: TransactionDeclinedByAcquirer
  }, (mvp.from_string) = "46"];

  TokenizeIncorrectRefundSum = 33 [(jusan_response_code_tokenize_value) = {
    code: "47 "
    description: "Сумма возврта '%s' больше чем сумма заказа"
    transaction_status: TransactionStatusFailed
    integration_error: TransactionDeclinedByAcquirer
  }, (mvp.from_string) = "47 "];

  TokenizeIncorrectStatus = 34 [(jusan_response_code_tokenize_value) = {
    code: "48"
    description: "Текущий статус заказа не позволяет делать возврат/отмену"
    transaction_status: TransactionStatusFailed
    integration_error: TransactionDeclinedByAcquirer
  }, (mvp.from_string) = "48"];

  TokenizeIncorrectValue = 35 [(jusan_response_code_tokenize_value) = {
    code: "50"
    description: "Неправильное значение"
    transaction_status: TransactionStatusFailed
    integration_error: TransactionDeclinedByAcquirer
  }, (mvp.from_string) = "50"];

  TokenizeIncorrectTerminalStatus = 36 [(jusan_response_code_tokenize_value) = {
    code: "51"
    description: "Текущий статус терминала не позволяет производить операции"
    transaction_status: TransactionStatusFailed
    integration_error: PaymentForbiddenForMerchant
  }, (mvp.from_string) = "51"];

  TokenizeForbiddenOperation = 37 [(jusan_response_code_tokenize_value) = {
    code: "52"
    description: "Операция отмены/возврата через API для терминала запрещена"
    transaction_status: TransactionStatusFailed
    integration_error: PaymentForbiddenForMerchant
  }, (mvp.from_string) = "52"];

  TokenizeDuplicateDescription = 38 [(jusan_response_code_tokenize_value) = {
    code: "53"
    description: "Дублирование описания отмены"
    transaction_status: TransactionStatusFailed
    integration_error: TransactionDeclinedByAcquirer
  }, (mvp.from_string) = "53"];

  TokenizeRefundError = 39 [(jusan_response_code_tokenize_value) = {
    code: "F"
    description: "Ошибка при обработке возврата"
    transaction_status: TransactionStatusFailed
    integration_error: TransactionDeclinedByAcquirer
  }, (mvp.from_string) = "F"];

  TokenizePayError = 40 [(jusan_response_code_tokenize_value) = {
    code: "E"
    description: "Ошибка при оплате"
    transaction_status: TransactionStatusFailed
    integration_error: TransactionDeclinedByAcquirer
  }, (mvp.from_string) = "E"];

  TokenizePaymentExpired = 41 [(jusan_response_code_tokenize_value) = {
    code: "c"
    description: "Счет на оплату устарел"
    transaction_status: TransactionStatusFailed
    integration_error: TransactionDeclinedByAcquirer
  }, (mvp.from_string) = "c"];

  TokenizeTransactionHandleError = 43 [(jusan_response_code_tokenize_value) = {
    code: "411"
    description: "Ошибка при обработке транзакции"
    integration_error: TransactionDeclinedByAcquirer
    transaction_status: TransactionStatusFailed
  }, (mvp.from_string) = "411"];
}
