// Code generated by protoc-gen-go-mvp. DO NOT EDIT.
// versions:
// - protoc-gen-go-mvp v2.0.1
// created by mvp authors

package grpc

import (
	context "context"
	cntx "git.local/sensitive/mvp/pkg/cntx"
	ctxzap "github.com/grpc-ecosystem/go-grpc-middleware/logging/zap/ctxzap"
	zap "go.uber.org/zap"
	grpc "google.golang.org/grpc"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
)

func file_inner_processing_grpc_compliance_proto_message_EmptyToZap(
	label string,
	in *emptypb.Empty,
) zap.Field {
	if in == nil {
		return zap.Reflect(label, in)
	}

	return zap.Dict(
		label,
	)
}

func file_inner_processing_grpc_compliance_proto_message_TestRequestV1ToZap(
	label string,
	in *TestRequestV1,
) zap.Field {
	if in == nil {
		return zap.Reflect(label, in)
	}

	return zap.Dict(
		label,
		zap.Any("Name", in.GetName()),
	)
}

var _ ComplianceServer = (*loggedComplianceServer)(nil)

func NewLoggedComplianceServer(srv ComplianceServer) ComplianceServer {
	return &loggedComplianceServer{srv: srv}
}

type loggedComplianceServer struct {
	UnimplementedComplianceServer

	srv ComplianceServer
}

func (s *loggedComplianceServer) UpdateSanctionFinanciersList(
	ctx context.Context,
	request *emptypb.Empty,
) (
	response *emptypb.Empty,
	err error,
) {
	label := cntx.Begin(ctx, "ComplianceServer_UpdateSanctionFinanciersList")
	defer cntx.End(ctx, label)

	defer func() {
		if err != nil {
			ctxzap.AddFields(ctx, zap.Any(label+"error", err))
			return
		}

		ctxzap.AddFields(
			ctx,
			file_inner_processing_grpc_compliance_proto_message_EmptyToZap(label+"response", response),
		)
	}()

	ctxzap.AddFields(
		ctx,
		file_inner_processing_grpc_compliance_proto_message_EmptyToZap(label+"request", request),
	)

	response, err = s.srv.UpdateSanctionFinanciersList(ctx, request)

	return
}

func (s *loggedComplianceServer) UpdateSanctionInvolvedList(
	ctx context.Context,
	request *emptypb.Empty,
) (
	response *emptypb.Empty,
	err error,
) {
	label := cntx.Begin(ctx, "ComplianceServer_UpdateSanctionInvolvedList")
	defer cntx.End(ctx, label)

	defer func() {
		if err != nil {
			ctxzap.AddFields(ctx, zap.Any(label+"error", err))
			return
		}

		ctxzap.AddFields(
			ctx,
			file_inner_processing_grpc_compliance_proto_message_EmptyToZap(label+"response", response),
		)
	}()

	ctxzap.AddFields(
		ctx,
		file_inner_processing_grpc_compliance_proto_message_EmptyToZap(label+"request", request),
	)

	response, err = s.srv.UpdateSanctionInvolvedList(ctx, request)

	return
}

func (s *loggedComplianceServer) UpdateSanctionUNSCList(
	ctx context.Context,
	request *emptypb.Empty,
) (
	response *emptypb.Empty,
	err error,
) {
	label := cntx.Begin(ctx, "ComplianceServer_UpdateSanctionUNSCList")
	defer cntx.End(ctx, label)

	defer func() {
		if err != nil {
			ctxzap.AddFields(ctx, zap.Any(label+"error", err))
			return
		}

		ctxzap.AddFields(
			ctx,
			file_inner_processing_grpc_compliance_proto_message_EmptyToZap(label+"response", response),
		)
	}()

	ctxzap.AddFields(
		ctx,
		file_inner_processing_grpc_compliance_proto_message_EmptyToZap(label+"request", request),
	)

	response, err = s.srv.UpdateSanctionUNSCList(ctx, request)

	return
}

func (s *loggedComplianceServer) UpdateSanctionWMDList(
	ctx context.Context,
	request *emptypb.Empty,
) (
	response *emptypb.Empty,
	err error,
) {
	label := cntx.Begin(ctx, "ComplianceServer_UpdateSanctionWMDList")
	defer cntx.End(ctx, label)

	defer func() {
		if err != nil {
			ctxzap.AddFields(ctx, zap.Any(label+"error", err))
			return
		}

		ctxzap.AddFields(
			ctx,
			file_inner_processing_grpc_compliance_proto_message_EmptyToZap(label+"response", response),
		)
	}()

	ctxzap.AddFields(
		ctx,
		file_inner_processing_grpc_compliance_proto_message_EmptyToZap(label+"request", request),
	)

	response, err = s.srv.UpdateSanctionWMDList(ctx, request)

	return
}

var _ ComplianceClient = (*loggedComplianceClient)(nil)

func NewLoggedComplianceClient(client ComplianceClient) ComplianceClient {
	return &loggedComplianceClient{client: client}
}

type loggedComplianceClient struct {
	client ComplianceClient
}

func (s *loggedComplianceClient) UpdateSanctionFinanciersList(
	ctx context.Context,
	request *emptypb.Empty,
	opts ...grpc.CallOption,
) (
	response *emptypb.Empty,
	err error,
) {
	label := cntx.Begin(ctx, "ComplianceClient_UpdateSanctionFinanciersList")
	defer cntx.End(ctx, label)

	defer func() {
		if err != nil {
			ctxzap.AddFields(ctx, zap.Any(label+"error", err))
			return
		}

		ctxzap.AddFields(
			ctx,
			file_inner_processing_grpc_compliance_proto_message_EmptyToZap(label+"response", response),
		)
	}()

	ctxzap.AddFields(
		ctx,
		file_inner_processing_grpc_compliance_proto_message_EmptyToZap(label+"request", request),
	)

	response, err = s.client.UpdateSanctionFinanciersList(ctx, request, opts...)

	return
}

func (s *loggedComplianceClient) UpdateSanctionInvolvedList(
	ctx context.Context,
	request *emptypb.Empty,
	opts ...grpc.CallOption,
) (
	response *emptypb.Empty,
	err error,
) {
	label := cntx.Begin(ctx, "ComplianceClient_UpdateSanctionInvolvedList")
	defer cntx.End(ctx, label)

	defer func() {
		if err != nil {
			ctxzap.AddFields(ctx, zap.Any(label+"error", err))
			return
		}

		ctxzap.AddFields(
			ctx,
			file_inner_processing_grpc_compliance_proto_message_EmptyToZap(label+"response", response),
		)
	}()

	ctxzap.AddFields(
		ctx,
		file_inner_processing_grpc_compliance_proto_message_EmptyToZap(label+"request", request),
	)

	response, err = s.client.UpdateSanctionInvolvedList(ctx, request, opts...)

	return
}

func (s *loggedComplianceClient) UpdateSanctionUNSCList(
	ctx context.Context,
	request *emptypb.Empty,
	opts ...grpc.CallOption,
) (
	response *emptypb.Empty,
	err error,
) {
	label := cntx.Begin(ctx, "ComplianceClient_UpdateSanctionUNSCList")
	defer cntx.End(ctx, label)

	defer func() {
		if err != nil {
			ctxzap.AddFields(ctx, zap.Any(label+"error", err))
			return
		}

		ctxzap.AddFields(
			ctx,
			file_inner_processing_grpc_compliance_proto_message_EmptyToZap(label+"response", response),
		)
	}()

	ctxzap.AddFields(
		ctx,
		file_inner_processing_grpc_compliance_proto_message_EmptyToZap(label+"request", request),
	)

	response, err = s.client.UpdateSanctionUNSCList(ctx, request, opts...)

	return
}

func (s *loggedComplianceClient) UpdateSanctionWMDList(
	ctx context.Context,
	request *emptypb.Empty,
	opts ...grpc.CallOption,
) (
	response *emptypb.Empty,
	err error,
) {
	label := cntx.Begin(ctx, "ComplianceClient_UpdateSanctionWMDList")
	defer cntx.End(ctx, label)

	defer func() {
		if err != nil {
			ctxzap.AddFields(ctx, zap.Any(label+"error", err))
			return
		}

		ctxzap.AddFields(
			ctx,
			file_inner_processing_grpc_compliance_proto_message_EmptyToZap(label+"response", response),
		)
	}()

	ctxzap.AddFields(
		ctx,
		file_inner_processing_grpc_compliance_proto_message_EmptyToZap(label+"request", request),
	)

	response, err = s.client.UpdateSanctionWMDList(ctx, request, opts...)

	return
}
