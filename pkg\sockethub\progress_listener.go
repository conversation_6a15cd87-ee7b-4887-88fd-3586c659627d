package sockethub

import (
	"context"
	"git.local/sensitive/sdk/dog"
	"sync/atomic"
)

const (
	FullProgressVal            uint32 = 100
	DbJobProgressVal           uint32 = 40
	UserFieldsFetchProgressVal uint32 = 10
	UserFieldsApplyProgressVal uint32 = 10
	FormFileProgressVal        uint32 = 20
)

type ProgressListener struct {
	clientID        uint64
	progressCh      chan int
	linkCh          chan string
	socketHub       *Hub
	currentProgress *atomic.Uint32
	ctx             context.Context
}

func NewProgressListener(
	ctx context.Context,
	clientID uint64,
	progressCh chan int,
	linkCh chan string,
	socketHub *Hub,
) *ProgressListener {
	currentProgress := new(atomic.Uint32)
	pl := &ProgressListener{
		ctx:             ctx,
		clientID:        clientID,
		progressCh:      progressCh,
		linkCh:          linkCh,
		socketHub:       socketHub,
		currentProgress: currentProgress,
	}

	go pl.start()

	return pl
}

func (pl *ProgressListener) start() {
	for {
		select {
		case <-pl.ctx.Done():
			dog.L().Info("ProgressListener Done")

			return
		case val := <-pl.progressCh:
			pl.socketHub.broadcast <- &WsProgressResponse{
				ClientID:    pl.clientID,
				Progress:    uint(val),
				IsConnected: true,
			}

			pl.currentProgress.Add(uint32(val))
		case val := <-pl.linkCh:
			pl.socketHub.broadcast <- &WsProgressResponse{
				ClientID:    pl.clientID,
				Progress:    uint(FullProgressVal),
				Link:        val,
				IsConnected: true,
			}
		}

	}
}

func (pl *ProgressListener) SendProgress(val uint32) {

	pl.progressCh <- int(val)

}
