// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.5
// 	protoc        v5.29.3
// source: inner/processing/grpc/billing.proto

package grpc

import (
	_ "git.local/sensitive/mvp/pb"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	descriptorpb "google.golang.org/protobuf/types/descriptorpb"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type BillingOperationBalanceType int32

const (
	BillingOperationBalanceType_BillingOperationBalanceTypeUnknown BillingOperationBalanceType = 0
	BillingOperationBalanceType_BillingOperationBalanceTypeMain    BillingOperationBalanceType = 1
	BillingOperationBalanceType_BillingOperationBalanceTypeCredit  BillingOperationBalanceType = 2
)

// Enum value maps for BillingOperationBalanceType.
var (
	BillingOperationBalanceType_name = map[int32]string{
		0: "BillingOperationBalanceTypeUnknown",
		1: "BillingOperationBalanceTypeMain",
		2: "BillingOperationBalanceTypeCredit",
	}
	BillingOperationBalanceType_value = map[string]int32{
		"BillingOperationBalanceTypeUnknown": 0,
		"BillingOperationBalanceTypeMain":    1,
		"BillingOperationBalanceTypeCredit":  2,
	}
)

func (x BillingOperationBalanceType) Enum() *BillingOperationBalanceType {
	p := new(BillingOperationBalanceType)
	*p = x
	return p
}

func (x BillingOperationBalanceType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (BillingOperationBalanceType) Descriptor() protoreflect.EnumDescriptor {
	return file_inner_processing_grpc_billing_proto_enumTypes[0].Descriptor()
}

func (BillingOperationBalanceType) Type() protoreflect.EnumType {
	return &file_inner_processing_grpc_billing_proto_enumTypes[0]
}

func (x BillingOperationBalanceType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use BillingOperationBalanceType.Descriptor instead.
func (BillingOperationBalanceType) EnumDescriptor() ([]byte, []int) {
	return file_inner_processing_grpc_billing_proto_rawDescGZIP(), []int{0}
}

type BillingOperationType int32

const (
	BillingOperationType_BillingOperationTypeUnknown                     BillingOperationType = 0
	BillingOperationType_BillingOperationTypePayIn                       BillingOperationType = 1
	BillingOperationType_BillingOperationTypePayOut                      BillingOperationType = 2
	BillingOperationType_BillingOperationTypeRefund                      BillingOperationType = 3
	BillingOperationType_BillingOperationTypeInTransfer                  BillingOperationType = 4
	BillingOperationType_BillingOperationTypeOutTransfer                 BillingOperationType = 5
	BillingOperationType_BillingOperationTypePayInCommissionOutAcquirer  BillingOperationType = 6
	BillingOperationType_BillingOperationTypePayInCommissionInSystem     BillingOperationType = 7
	BillingOperationType_BillingOperationTypePayInCommissionOutSystem    BillingOperationType = 8
	BillingOperationType_BillingOperationTypePayInCommissionInMerchant   BillingOperationType = 9
	BillingOperationType_BillingOperationTypePayInCommissionOutMerchant  BillingOperationType = 10
	BillingOperationType_BillingOperationTypeInternalInTransfer          BillingOperationType = 11
	BillingOperationType_BillingOperationTypeInternalOutTransfer         BillingOperationType = 12
	BillingOperationType_BillingOperationTypeSplitOutTransfer            BillingOperationType = 13
	BillingOperationType_BillingOperationTypeSplitInTransfer             BillingOperationType = 14
	BillingOperationType_BillingOperationTypePayOutCommissionOutAcquirer BillingOperationType = 15
	BillingOperationType_BillingOperationTypePayOutCommissionInSystem    BillingOperationType = 16
	BillingOperationType_BillingOperationTypePayOutCommissionOutSystem   BillingOperationType = 17
	BillingOperationType_BillingOperationTypePayOutCommissionInMerchant  BillingOperationType = 18
	BillingOperationType_BillingOperationTypePayOutCommissionOutMerchant BillingOperationType = 19
	BillingOperationType_BillingOperationTypeSplitCommissionInSystem     BillingOperationType = 20
	BillingOperationType_BillingOperationTypeSplitCommissionOutSystem    BillingOperationType = 21
	BillingOperationType_BillingOperationTypeSplitCommissionInMerchant   BillingOperationType = 22
	BillingOperationType_BillingOperationTypeSplitCommissionOutMerchant  BillingOperationType = 23
)

// Enum value maps for BillingOperationType.
var (
	BillingOperationType_name = map[int32]string{
		0:  "BillingOperationTypeUnknown",
		1:  "BillingOperationTypePayIn",
		2:  "BillingOperationTypePayOut",
		3:  "BillingOperationTypeRefund",
		4:  "BillingOperationTypeInTransfer",
		5:  "BillingOperationTypeOutTransfer",
		6:  "BillingOperationTypePayInCommissionOutAcquirer",
		7:  "BillingOperationTypePayInCommissionInSystem",
		8:  "BillingOperationTypePayInCommissionOutSystem",
		9:  "BillingOperationTypePayInCommissionInMerchant",
		10: "BillingOperationTypePayInCommissionOutMerchant",
		11: "BillingOperationTypeInternalInTransfer",
		12: "BillingOperationTypeInternalOutTransfer",
		13: "BillingOperationTypeSplitOutTransfer",
		14: "BillingOperationTypeSplitInTransfer",
		15: "BillingOperationTypePayOutCommissionOutAcquirer",
		16: "BillingOperationTypePayOutCommissionInSystem",
		17: "BillingOperationTypePayOutCommissionOutSystem",
		18: "BillingOperationTypePayOutCommissionInMerchant",
		19: "BillingOperationTypePayOutCommissionOutMerchant",
		20: "BillingOperationTypeSplitCommissionInSystem",
		21: "BillingOperationTypeSplitCommissionOutSystem",
		22: "BillingOperationTypeSplitCommissionInMerchant",
		23: "BillingOperationTypeSplitCommissionOutMerchant",
	}
	BillingOperationType_value = map[string]int32{
		"BillingOperationTypeUnknown":                     0,
		"BillingOperationTypePayIn":                       1,
		"BillingOperationTypePayOut":                      2,
		"BillingOperationTypeRefund":                      3,
		"BillingOperationTypeInTransfer":                  4,
		"BillingOperationTypeOutTransfer":                 5,
		"BillingOperationTypePayInCommissionOutAcquirer":  6,
		"BillingOperationTypePayInCommissionInSystem":     7,
		"BillingOperationTypePayInCommissionOutSystem":    8,
		"BillingOperationTypePayInCommissionInMerchant":   9,
		"BillingOperationTypePayInCommissionOutMerchant":  10,
		"BillingOperationTypeInternalInTransfer":          11,
		"BillingOperationTypeInternalOutTransfer":         12,
		"BillingOperationTypeSplitOutTransfer":            13,
		"BillingOperationTypeSplitInTransfer":             14,
		"BillingOperationTypePayOutCommissionOutAcquirer": 15,
		"BillingOperationTypePayOutCommissionInSystem":    16,
		"BillingOperationTypePayOutCommissionOutSystem":   17,
		"BillingOperationTypePayOutCommissionInMerchant":  18,
		"BillingOperationTypePayOutCommissionOutMerchant": 19,
		"BillingOperationTypeSplitCommissionInSystem":     20,
		"BillingOperationTypeSplitCommissionOutSystem":    21,
		"BillingOperationTypeSplitCommissionInMerchant":   22,
		"BillingOperationTypeSplitCommissionOutMerchant":  23,
	}
)

func (x BillingOperationType) Enum() *BillingOperationType {
	p := new(BillingOperationType)
	*p = x
	return p
}

func (x BillingOperationType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (BillingOperationType) Descriptor() protoreflect.EnumDescriptor {
	return file_inner_processing_grpc_billing_proto_enumTypes[1].Descriptor()
}

func (BillingOperationType) Type() protoreflect.EnumType {
	return &file_inner_processing_grpc_billing_proto_enumTypes[1]
}

func (x BillingOperationType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use BillingOperationType.Descriptor instead.
func (BillingOperationType) EnumDescriptor() ([]byte, []int) {
	return file_inner_processing_grpc_billing_proto_rawDescGZIP(), []int{1}
}

type BillingOperationStatus int32

const (
	BillingOperationStatus_BillingOperationStatusUnknown    BillingOperationStatus = 0
	BillingOperationStatus_BillingOperationStatusFinalized  BillingOperationStatus = 1
	BillingOperationStatus_BillingOperationStatusInProgress BillingOperationStatus = 2
)

// Enum value maps for BillingOperationStatus.
var (
	BillingOperationStatus_name = map[int32]string{
		0: "BillingOperationStatusUnknown",
		1: "BillingOperationStatusFinalized",
		2: "BillingOperationStatusInProgress",
	}
	BillingOperationStatus_value = map[string]int32{
		"BillingOperationStatusUnknown":    0,
		"BillingOperationStatusFinalized":  1,
		"BillingOperationStatusInProgress": 2,
	}
)

func (x BillingOperationStatus) Enum() *BillingOperationStatus {
	p := new(BillingOperationStatus)
	*p = x
	return p
}

func (x BillingOperationStatus) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (BillingOperationStatus) Descriptor() protoreflect.EnumDescriptor {
	return file_inner_processing_grpc_billing_proto_enumTypes[2].Descriptor()
}

func (BillingOperationStatus) Type() protoreflect.EnumType {
	return &file_inner_processing_grpc_billing_proto_enumTypes[2]
}

func (x BillingOperationStatus) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use BillingOperationStatus.Descriptor instead.
func (BillingOperationStatus) EnumDescriptor() ([]byte, []int) {
	return file_inner_processing_grpc_billing_proto_rawDescGZIP(), []int{2}
}

type BillingOperationTypeGroup int32

const (
	BillingOperationTypeGroup_BillingOperationTypeGroupUnknown BillingOperationTypeGroup = 0
	BillingOperationTypeGroup_BillingOperationTypeGroupIn      BillingOperationTypeGroup = 1
	BillingOperationTypeGroup_BillingOperationTypeGroupOut     BillingOperationTypeGroup = 2
	BillingOperationTypeGroup_BillingOperationTypeGroupRefund  BillingOperationTypeGroup = 3
	BillingOperationTypeGroup_BillingOperationTypeGroupPayIn   BillingOperationTypeGroup = 4
)

// Enum value maps for BillingOperationTypeGroup.
var (
	BillingOperationTypeGroup_name = map[int32]string{
		0: "BillingOperationTypeGroupUnknown",
		1: "BillingOperationTypeGroupIn",
		2: "BillingOperationTypeGroupOut",
		3: "BillingOperationTypeGroupRefund",
		4: "BillingOperationTypeGroupPayIn",
	}
	BillingOperationTypeGroup_value = map[string]int32{
		"BillingOperationTypeGroupUnknown": 0,
		"BillingOperationTypeGroupIn":      1,
		"BillingOperationTypeGroupOut":     2,
		"BillingOperationTypeGroupRefund":  3,
		"BillingOperationTypeGroupPayIn":   4,
	}
)

func (x BillingOperationTypeGroup) Enum() *BillingOperationTypeGroup {
	p := new(BillingOperationTypeGroup)
	*p = x
	return p
}

func (x BillingOperationTypeGroup) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (BillingOperationTypeGroup) Descriptor() protoreflect.EnumDescriptor {
	return file_inner_processing_grpc_billing_proto_enumTypes[3].Descriptor()
}

func (BillingOperationTypeGroup) Type() protoreflect.EnumType {
	return &file_inner_processing_grpc_billing_proto_enumTypes[3]
}

func (x BillingOperationTypeGroup) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use BillingOperationTypeGroup.Descriptor instead.
func (BillingOperationTypeGroup) EnumDescriptor() ([]byte, []int) {
	return file_inner_processing_grpc_billing_proto_rawDescGZIP(), []int{3}
}

type BillingEarnType int32

const (
	BillingEarnType_BillingEarnTypeUnknown    BillingEarnType = 0
	BillingEarnType_BillingEarnTypeCommission BillingEarnType = 1
	BillingEarnType_BillingEarnTypeFee        BillingEarnType = 2
)

// Enum value maps for BillingEarnType.
var (
	BillingEarnType_name = map[int32]string{
		0: "BillingEarnTypeUnknown",
		1: "BillingEarnTypeCommission",
		2: "BillingEarnTypeFee",
	}
	BillingEarnType_value = map[string]int32{
		"BillingEarnTypeUnknown":    0,
		"BillingEarnTypeCommission": 1,
		"BillingEarnTypeFee":        2,
	}
)

func (x BillingEarnType) Enum() *BillingEarnType {
	p := new(BillingEarnType)
	*p = x
	return p
}

func (x BillingEarnType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (BillingEarnType) Descriptor() protoreflect.EnumDescriptor {
	return file_inner_processing_grpc_billing_proto_enumTypes[4].Descriptor()
}

func (BillingEarnType) Type() protoreflect.EnumType {
	return &file_inner_processing_grpc_billing_proto_enumTypes[4]
}

func (x BillingEarnType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use BillingEarnType.Descriptor instead.
func (BillingEarnType) EnumDescriptor() ([]byte, []int) {
	return file_inner_processing_grpc_billing_proto_rawDescGZIP(), []int{4}
}

type BillingBalanceOwnerType int32

const (
	BillingBalanceOwnerType_BillingBalanceOwnerTypeUnknown  BillingBalanceOwnerType = 0
	BillingBalanceOwnerType_BillingBalanceOwnerTypeSystem   BillingBalanceOwnerType = 1
	BillingBalanceOwnerType_BillingBalanceOwnerTypeMerchant BillingBalanceOwnerType = 2
	BillingBalanceOwnerType_BillingBalanceOwnerTypeProject  BillingBalanceOwnerType = 3
)

// Enum value maps for BillingBalanceOwnerType.
var (
	BillingBalanceOwnerType_name = map[int32]string{
		0: "BillingBalanceOwnerTypeUnknown",
		1: "BillingBalanceOwnerTypeSystem",
		2: "BillingBalanceOwnerTypeMerchant",
		3: "BillingBalanceOwnerTypeProject",
	}
	BillingBalanceOwnerType_value = map[string]int32{
		"BillingBalanceOwnerTypeUnknown":  0,
		"BillingBalanceOwnerTypeSystem":   1,
		"BillingBalanceOwnerTypeMerchant": 2,
		"BillingBalanceOwnerTypeProject":  3,
	}
)

func (x BillingBalanceOwnerType) Enum() *BillingBalanceOwnerType {
	p := new(BillingBalanceOwnerType)
	*p = x
	return p
}

func (x BillingBalanceOwnerType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (BillingBalanceOwnerType) Descriptor() protoreflect.EnumDescriptor {
	return file_inner_processing_grpc_billing_proto_enumTypes[5].Descriptor()
}

func (BillingBalanceOwnerType) Type() protoreflect.EnumType {
	return &file_inner_processing_grpc_billing_proto_enumTypes[5]
}

func (x BillingBalanceOwnerType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use BillingBalanceOwnerType.Descriptor instead.
func (BillingBalanceOwnerType) EnumDescriptor() ([]byte, []int) {
	return file_inner_processing_grpc_billing_proto_rawDescGZIP(), []int{5}
}

type BillingBalanceOwnerStatus int32

const (
	BillingBalanceOwnerStatus_BillingBalanceOwnerStatusUnknown  BillingBalanceOwnerStatus = 0
	BillingBalanceOwnerStatus_BillingBalanceOwnerStatusActive   BillingBalanceOwnerStatus = 1
	BillingBalanceOwnerStatus_BillingBalanceOwnerStatusInactive BillingBalanceOwnerStatus = 2
)

// Enum value maps for BillingBalanceOwnerStatus.
var (
	BillingBalanceOwnerStatus_name = map[int32]string{
		0: "BillingBalanceOwnerStatusUnknown",
		1: "BillingBalanceOwnerStatusActive",
		2: "BillingBalanceOwnerStatusInactive",
	}
	BillingBalanceOwnerStatus_value = map[string]int32{
		"BillingBalanceOwnerStatusUnknown":  0,
		"BillingBalanceOwnerStatusActive":   1,
		"BillingBalanceOwnerStatusInactive": 2,
	}
)

func (x BillingBalanceOwnerStatus) Enum() *BillingBalanceOwnerStatus {
	p := new(BillingBalanceOwnerStatus)
	*p = x
	return p
}

func (x BillingBalanceOwnerStatus) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (BillingBalanceOwnerStatus) Descriptor() protoreflect.EnumDescriptor {
	return file_inner_processing_grpc_billing_proto_enumTypes[6].Descriptor()
}

func (BillingBalanceOwnerStatus) Type() protoreflect.EnumType {
	return &file_inner_processing_grpc_billing_proto_enumTypes[6]
}

func (x BillingBalanceOwnerStatus) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use BillingBalanceOwnerStatus.Descriptor instead.
func (BillingBalanceOwnerStatus) EnumDescriptor() ([]byte, []int) {
	return file_inner_processing_grpc_billing_proto_rawDescGZIP(), []int{6}
}

type BillingBalanceCreditStatus int32

const (
	BillingBalanceCreditStatus_BillingBalanceCreditStatusUnknown  BillingBalanceCreditStatus = 0
	BillingBalanceCreditStatus_BillingBalanceCreditStatusActive   BillingBalanceCreditStatus = 1
	BillingBalanceCreditStatus_BillingBalanceCreditStatusInactive BillingBalanceCreditStatus = 2
	BillingBalanceCreditStatus_BillingBalanceCreditStatusIdle     BillingBalanceCreditStatus = 3
)

// Enum value maps for BillingBalanceCreditStatus.
var (
	BillingBalanceCreditStatus_name = map[int32]string{
		0: "BillingBalanceCreditStatusUnknown",
		1: "BillingBalanceCreditStatusActive",
		2: "BillingBalanceCreditStatusInactive",
		3: "BillingBalanceCreditStatusIdle",
	}
	BillingBalanceCreditStatus_value = map[string]int32{
		"BillingBalanceCreditStatusUnknown":  0,
		"BillingBalanceCreditStatusActive":   1,
		"BillingBalanceCreditStatusInactive": 2,
		"BillingBalanceCreditStatusIdle":     3,
	}
)

func (x BillingBalanceCreditStatus) Enum() *BillingBalanceCreditStatus {
	p := new(BillingBalanceCreditStatus)
	*p = x
	return p
}

func (x BillingBalanceCreditStatus) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (BillingBalanceCreditStatus) Descriptor() protoreflect.EnumDescriptor {
	return file_inner_processing_grpc_billing_proto_enumTypes[7].Descriptor()
}

func (BillingBalanceCreditStatus) Type() protoreflect.EnumType {
	return &file_inner_processing_grpc_billing_proto_enumTypes[7]
}

func (x BillingBalanceCreditStatus) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use BillingBalanceCreditStatus.Descriptor instead.
func (BillingBalanceCreditStatus) EnumDescriptor() ([]byte, []int) {
	return file_inner_processing_grpc_billing_proto_rawDescGZIP(), []int{7}
}

type BillingBalanceAccountStatus int32

const (
	BillingBalanceAccountStatus_BillingBalanceAccountStatusUnknown  BillingBalanceAccountStatus = 0
	BillingBalanceAccountStatus_BillingBalanceAccountStatusActive   BillingBalanceAccountStatus = 1
	BillingBalanceAccountStatus_BillingBalanceAccountStatusInactive BillingBalanceAccountStatus = 2
)

// Enum value maps for BillingBalanceAccountStatus.
var (
	BillingBalanceAccountStatus_name = map[int32]string{
		0: "BillingBalanceAccountStatusUnknown",
		1: "BillingBalanceAccountStatusActive",
		2: "BillingBalanceAccountStatusInactive",
	}
	BillingBalanceAccountStatus_value = map[string]int32{
		"BillingBalanceAccountStatusUnknown":  0,
		"BillingBalanceAccountStatusActive":   1,
		"BillingBalanceAccountStatusInactive": 2,
	}
)

func (x BillingBalanceAccountStatus) Enum() *BillingBalanceAccountStatus {
	p := new(BillingBalanceAccountStatus)
	*p = x
	return p
}

func (x BillingBalanceAccountStatus) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (BillingBalanceAccountStatus) Descriptor() protoreflect.EnumDescriptor {
	return file_inner_processing_grpc_billing_proto_enumTypes[8].Descriptor()
}

func (BillingBalanceAccountStatus) Type() protoreflect.EnumType {
	return &file_inner_processing_grpc_billing_proto_enumTypes[8]
}

func (x BillingBalanceAccountStatus) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use BillingBalanceAccountStatus.Descriptor instead.
func (BillingBalanceAccountStatus) EnumDescriptor() ([]byte, []int) {
	return file_inner_processing_grpc_billing_proto_rawDescGZIP(), []int{8}
}

type BillingOperationMode int32

const (
	BillingOperationMode_BillingOperationModeUnknown     BillingOperationMode = 0
	BillingOperationMode_BillingOperationModeRealtime    BillingOperationMode = 1
	BillingOperationMode_BillingOperationModeBankProcess BillingOperationMode = 2
)

// Enum value maps for BillingOperationMode.
var (
	BillingOperationMode_name = map[int32]string{
		0: "BillingOperationModeUnknown",
		1: "BillingOperationModeRealtime",
		2: "BillingOperationModeBankProcess",
	}
	BillingOperationMode_value = map[string]int32{
		"BillingOperationModeUnknown":     0,
		"BillingOperationModeRealtime":    1,
		"BillingOperationModeBankProcess": 2,
	}
)

func (x BillingOperationMode) Enum() *BillingOperationMode {
	p := new(BillingOperationMode)
	*p = x
	return p
}

func (x BillingOperationMode) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (BillingOperationMode) Descriptor() protoreflect.EnumDescriptor {
	return file_inner_processing_grpc_billing_proto_enumTypes[9].Descriptor()
}

func (BillingOperationMode) Type() protoreflect.EnumType {
	return &file_inner_processing_grpc_billing_proto_enumTypes[9]
}

func (x BillingOperationMode) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use BillingOperationMode.Descriptor instead.
func (BillingOperationMode) EnumDescriptor() ([]byte, []int) {
	return file_inner_processing_grpc_billing_proto_rawDescGZIP(), []int{9}
}

type BillingOperationTypeGroupRelation int32

const (
	BillingOperationTypeGroupRelation_BillingOperationTypeGroupRelationUnknown              BillingOperationTypeGroupRelation = 0
	BillingOperationTypeGroupRelation_BillingOperationTypeGroupRelationPayIn                BillingOperationTypeGroupRelation = 1
	BillingOperationTypeGroupRelation_BillingOperationTypeGroupRelationPayOut               BillingOperationTypeGroupRelation = 2
	BillingOperationTypeGroupRelation_BillingOperationTypeGroupRelationRefund               BillingOperationTypeGroupRelation = 3
	BillingOperationTypeGroupRelation_BillingOperationTypeGroupRelationInTransfer           BillingOperationTypeGroupRelation = 4
	BillingOperationTypeGroupRelation_BillingOperationTypeGroupRelationOutTransfer          BillingOperationTypeGroupRelation = 5
	BillingOperationTypeGroupRelation_BillingOperationTypeGroupRelationPayInCmsOutAcquirer  BillingOperationTypeGroupRelation = 6
	BillingOperationTypeGroupRelation_BillingOperationTypeGroupRelationPayInCmsInSystem     BillingOperationTypeGroupRelation = 7
	BillingOperationTypeGroupRelation_BillingOperationTypeGroupRelationPayInCmsOutSystem    BillingOperationTypeGroupRelation = 8
	BillingOperationTypeGroupRelation_BillingOperationTypeGroupRelationPayInCmsInMerchant   BillingOperationTypeGroupRelation = 9
	BillingOperationTypeGroupRelation_BillingOperationTypeGroupRelationPayInCmsOutMerchant  BillingOperationTypeGroupRelation = 10
	BillingOperationTypeGroupRelation_BillingOperationTypeGroupRelationInternalInTransfer   BillingOperationTypeGroupRelation = 11
	BillingOperationTypeGroupRelation_BillingOperationTypeGroupRelationInternalOutTransfer  BillingOperationTypeGroupRelation = 12
	BillingOperationTypeGroupRelation_BillingOperationTypeGroupRelationSplitOutTransfer     BillingOperationTypeGroupRelation = 13
	BillingOperationTypeGroupRelation_BillingOperationTypeGroupRelationSplitInTransfer      BillingOperationTypeGroupRelation = 14
	BillingOperationTypeGroupRelation_BillingOperationTypeGroupRelationPayOutCmsOutAcquirer BillingOperationTypeGroupRelation = 15
	BillingOperationTypeGroupRelation_BillingOperationTypeGroupRelationPayOutCmsInSystem    BillingOperationTypeGroupRelation = 16
	BillingOperationTypeGroupRelation_BillingOperationTypeGroupRelationPayOutCmsOutSystem   BillingOperationTypeGroupRelation = 17
	BillingOperationTypeGroupRelation_BillingOperationTypeGroupRelationPayOutCmsInMerchant  BillingOperationTypeGroupRelation = 18
	BillingOperationTypeGroupRelation_BillingOperationTypeGroupRelationPayOutCmsOutMerchant BillingOperationTypeGroupRelation = 19
	BillingOperationTypeGroupRelation_BillingOperationTypeGroupRelationSplitCmsInSystem     BillingOperationTypeGroupRelation = 20
	BillingOperationTypeGroupRelation_BillingOperationTypeGroupRelationSplitCmsOutSystem    BillingOperationTypeGroupRelation = 21
	BillingOperationTypeGroupRelation_BillingOperationTypeGroupRelationSplitCmsInMerchant   BillingOperationTypeGroupRelation = 22
	BillingOperationTypeGroupRelation_BillingOperationTypeGroupRelationSplitCmsOutMerchant  BillingOperationTypeGroupRelation = 23
)

// Enum value maps for BillingOperationTypeGroupRelation.
var (
	BillingOperationTypeGroupRelation_name = map[int32]string{
		0:  "BillingOperationTypeGroupRelationUnknown",
		1:  "BillingOperationTypeGroupRelationPayIn",
		2:  "BillingOperationTypeGroupRelationPayOut",
		3:  "BillingOperationTypeGroupRelationRefund",
		4:  "BillingOperationTypeGroupRelationInTransfer",
		5:  "BillingOperationTypeGroupRelationOutTransfer",
		6:  "BillingOperationTypeGroupRelationPayInCmsOutAcquirer",
		7:  "BillingOperationTypeGroupRelationPayInCmsInSystem",
		8:  "BillingOperationTypeGroupRelationPayInCmsOutSystem",
		9:  "BillingOperationTypeGroupRelationPayInCmsInMerchant",
		10: "BillingOperationTypeGroupRelationPayInCmsOutMerchant",
		11: "BillingOperationTypeGroupRelationInternalInTransfer",
		12: "BillingOperationTypeGroupRelationInternalOutTransfer",
		13: "BillingOperationTypeGroupRelationSplitOutTransfer",
		14: "BillingOperationTypeGroupRelationSplitInTransfer",
		15: "BillingOperationTypeGroupRelationPayOutCmsOutAcquirer",
		16: "BillingOperationTypeGroupRelationPayOutCmsInSystem",
		17: "BillingOperationTypeGroupRelationPayOutCmsOutSystem",
		18: "BillingOperationTypeGroupRelationPayOutCmsInMerchant",
		19: "BillingOperationTypeGroupRelationPayOutCmsOutMerchant",
		20: "BillingOperationTypeGroupRelationSplitCmsInSystem",
		21: "BillingOperationTypeGroupRelationSplitCmsOutSystem",
		22: "BillingOperationTypeGroupRelationSplitCmsInMerchant",
		23: "BillingOperationTypeGroupRelationSplitCmsOutMerchant",
	}
	BillingOperationTypeGroupRelation_value = map[string]int32{
		"BillingOperationTypeGroupRelationUnknown":              0,
		"BillingOperationTypeGroupRelationPayIn":                1,
		"BillingOperationTypeGroupRelationPayOut":               2,
		"BillingOperationTypeGroupRelationRefund":               3,
		"BillingOperationTypeGroupRelationInTransfer":           4,
		"BillingOperationTypeGroupRelationOutTransfer":          5,
		"BillingOperationTypeGroupRelationPayInCmsOutAcquirer":  6,
		"BillingOperationTypeGroupRelationPayInCmsInSystem":     7,
		"BillingOperationTypeGroupRelationPayInCmsOutSystem":    8,
		"BillingOperationTypeGroupRelationPayInCmsInMerchant":   9,
		"BillingOperationTypeGroupRelationPayInCmsOutMerchant":  10,
		"BillingOperationTypeGroupRelationInternalInTransfer":   11,
		"BillingOperationTypeGroupRelationInternalOutTransfer":  12,
		"BillingOperationTypeGroupRelationSplitOutTransfer":     13,
		"BillingOperationTypeGroupRelationSplitInTransfer":      14,
		"BillingOperationTypeGroupRelationPayOutCmsOutAcquirer": 15,
		"BillingOperationTypeGroupRelationPayOutCmsInSystem":    16,
		"BillingOperationTypeGroupRelationPayOutCmsOutSystem":   17,
		"BillingOperationTypeGroupRelationPayOutCmsInMerchant":  18,
		"BillingOperationTypeGroupRelationPayOutCmsOutMerchant": 19,
		"BillingOperationTypeGroupRelationSplitCmsInSystem":     20,
		"BillingOperationTypeGroupRelationSplitCmsOutSystem":    21,
		"BillingOperationTypeGroupRelationSplitCmsInMerchant":   22,
		"BillingOperationTypeGroupRelationSplitCmsOutMerchant":  23,
	}
)

func (x BillingOperationTypeGroupRelation) Enum() *BillingOperationTypeGroupRelation {
	p := new(BillingOperationTypeGroupRelation)
	*p = x
	return p
}

func (x BillingOperationTypeGroupRelation) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (BillingOperationTypeGroupRelation) Descriptor() protoreflect.EnumDescriptor {
	return file_inner_processing_grpc_billing_proto_enumTypes[10].Descriptor()
}

func (BillingOperationTypeGroupRelation) Type() protoreflect.EnumType {
	return &file_inner_processing_grpc_billing_proto_enumTypes[10]
}

func (x BillingOperationTypeGroupRelation) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use BillingOperationTypeGroupRelation.Descriptor instead.
func (BillingOperationTypeGroupRelation) EnumDescriptor() ([]byte, []int) {
	return file_inner_processing_grpc_billing_proto_rawDescGZIP(), []int{10}
}

type CheckPayOutBalanceReqV1 struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	MerchantId    *uint64                `protobuf:"varint,1,opt,name=merchant_id,json=merchantId" json:"merchant_id,omitempty"`
	ProjectId     *uint64                `protobuf:"varint,2,opt,name=project_id,json=projectId" json:"project_id,omitempty"`
	AccountNumber *string                `protobuf:"bytes,3,opt,name=account_number,json=accountNumber" json:"account_number,omitempty"`
	Amount        *float64               `protobuf:"fixed64,4,opt,name=amount" json:"amount,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CheckPayOutBalanceReqV1) Reset() {
	*x = CheckPayOutBalanceReqV1{}
	mi := &file_inner_processing_grpc_billing_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CheckPayOutBalanceReqV1) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CheckPayOutBalanceReqV1) ProtoMessage() {}

func (x *CheckPayOutBalanceReqV1) ProtoReflect() protoreflect.Message {
	mi := &file_inner_processing_grpc_billing_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CheckPayOutBalanceReqV1.ProtoReflect.Descriptor instead.
func (*CheckPayOutBalanceReqV1) Descriptor() ([]byte, []int) {
	return file_inner_processing_grpc_billing_proto_rawDescGZIP(), []int{0}
}

func (x *CheckPayOutBalanceReqV1) GetMerchantId() uint64 {
	if x != nil && x.MerchantId != nil {
		return *x.MerchantId
	}
	return 0
}

func (x *CheckPayOutBalanceReqV1) GetProjectId() uint64 {
	if x != nil && x.ProjectId != nil {
		return *x.ProjectId
	}
	return 0
}

func (x *CheckPayOutBalanceReqV1) GetAccountNumber() string {
	if x != nil && x.AccountNumber != nil {
		return *x.AccountNumber
	}
	return ""
}

func (x *CheckPayOutBalanceReqV1) GetAmount() float64 {
	if x != nil && x.Amount != nil {
		return *x.Amount
	}
	return 0
}

type CheckPayOutBalanceResV1 struct {
	state                protoimpl.MessageState `protogen:"open.v1"`
	HasSufficientBalance *bool                  `protobuf:"varint,1,opt,name=has_sufficient_balance,json=hasSufficientBalance" json:"has_sufficient_balance,omitempty"`
	unknownFields        protoimpl.UnknownFields
	sizeCache            protoimpl.SizeCache
}

func (x *CheckPayOutBalanceResV1) Reset() {
	*x = CheckPayOutBalanceResV1{}
	mi := &file_inner_processing_grpc_billing_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CheckPayOutBalanceResV1) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CheckPayOutBalanceResV1) ProtoMessage() {}

func (x *CheckPayOutBalanceResV1) ProtoReflect() protoreflect.Message {
	mi := &file_inner_processing_grpc_billing_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CheckPayOutBalanceResV1.ProtoReflect.Descriptor instead.
func (*CheckPayOutBalanceResV1) Descriptor() ([]byte, []int) {
	return file_inner_processing_grpc_billing_proto_rawDescGZIP(), []int{1}
}

func (x *CheckPayOutBalanceResV1) GetHasSufficientBalance() bool {
	if x != nil && x.HasSufficientBalance != nil {
		return *x.HasSufficientBalance
	}
	return false
}

type BillPayInTransactionRequestV1 struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	MerchantId    *uint64                `protobuf:"varint,1,opt,name=merchant_id,json=merchantId" json:"merchant_id,omitempty"`
	ProjectId     *uint64                `protobuf:"varint,2,opt,name=project_id,json=projectId" json:"project_id,omitempty"`
	TransactionId *uint64                `protobuf:"varint,3,opt,name=transaction_id,json=transactionId" json:"transaction_id,omitempty"`
	Amount        *float64               `protobuf:"fixed64,4,opt,name=amount" json:"amount,omitempty"`
	TerminalId    *uint64                `protobuf:"varint,5,opt,name=terminal_id,json=terminalId" json:"terminal_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *BillPayInTransactionRequestV1) Reset() {
	*x = BillPayInTransactionRequestV1{}
	mi := &file_inner_processing_grpc_billing_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BillPayInTransactionRequestV1) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BillPayInTransactionRequestV1) ProtoMessage() {}

func (x *BillPayInTransactionRequestV1) ProtoReflect() protoreflect.Message {
	mi := &file_inner_processing_grpc_billing_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BillPayInTransactionRequestV1.ProtoReflect.Descriptor instead.
func (*BillPayInTransactionRequestV1) Descriptor() ([]byte, []int) {
	return file_inner_processing_grpc_billing_proto_rawDescGZIP(), []int{2}
}

func (x *BillPayInTransactionRequestV1) GetMerchantId() uint64 {
	if x != nil && x.MerchantId != nil {
		return *x.MerchantId
	}
	return 0
}

func (x *BillPayInTransactionRequestV1) GetProjectId() uint64 {
	if x != nil && x.ProjectId != nil {
		return *x.ProjectId
	}
	return 0
}

func (x *BillPayInTransactionRequestV1) GetTransactionId() uint64 {
	if x != nil && x.TransactionId != nil {
		return *x.TransactionId
	}
	return 0
}

func (x *BillPayInTransactionRequestV1) GetAmount() float64 {
	if x != nil && x.Amount != nil {
		return *x.Amount
	}
	return 0
}

func (x *BillPayInTransactionRequestV1) GetTerminalId() uint64 {
	if x != nil && x.TerminalId != nil {
		return *x.TerminalId
	}
	return 0
}

type BillPayOutTransactionRequestV1 struct {
	state                protoimpl.MessageState `protogen:"open.v1"`
	MerchantId           *uint64                `protobuf:"varint,1,opt,name=merchant_id,json=merchantId" json:"merchant_id,omitempty"`
	ProjectId            *uint64                `protobuf:"varint,2,opt,name=project_id,json=projectId" json:"project_id,omitempty"`
	TransactionId        *uint64                `protobuf:"varint,3,opt,name=transaction_id,json=transactionId" json:"transaction_id,omitempty"`
	Amount               *float64               `protobuf:"fixed64,4,opt,name=amount" json:"amount,omitempty"`
	TerminalId           *uint64                `protobuf:"varint,5,opt,name=terminal_id,json=terminalId" json:"terminal_id,omitempty"`
	TransactionCreatedAt *timestamppb.Timestamp `protobuf:"bytes,6,opt,name=transaction_created_at,json=transactionCreatedAt" json:"transaction_created_at,omitempty"`
	unknownFields        protoimpl.UnknownFields
	sizeCache            protoimpl.SizeCache
}

func (x *BillPayOutTransactionRequestV1) Reset() {
	*x = BillPayOutTransactionRequestV1{}
	mi := &file_inner_processing_grpc_billing_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BillPayOutTransactionRequestV1) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BillPayOutTransactionRequestV1) ProtoMessage() {}

func (x *BillPayOutTransactionRequestV1) ProtoReflect() protoreflect.Message {
	mi := &file_inner_processing_grpc_billing_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BillPayOutTransactionRequestV1.ProtoReflect.Descriptor instead.
func (*BillPayOutTransactionRequestV1) Descriptor() ([]byte, []int) {
	return file_inner_processing_grpc_billing_proto_rawDescGZIP(), []int{3}
}

func (x *BillPayOutTransactionRequestV1) GetMerchantId() uint64 {
	if x != nil && x.MerchantId != nil {
		return *x.MerchantId
	}
	return 0
}

func (x *BillPayOutTransactionRequestV1) GetProjectId() uint64 {
	if x != nil && x.ProjectId != nil {
		return *x.ProjectId
	}
	return 0
}

func (x *BillPayOutTransactionRequestV1) GetTransactionId() uint64 {
	if x != nil && x.TransactionId != nil {
		return *x.TransactionId
	}
	return 0
}

func (x *BillPayOutTransactionRequestV1) GetAmount() float64 {
	if x != nil && x.Amount != nil {
		return *x.Amount
	}
	return 0
}

func (x *BillPayOutTransactionRequestV1) GetTerminalId() uint64 {
	if x != nil && x.TerminalId != nil {
		return *x.TerminalId
	}
	return 0
}

func (x *BillPayOutTransactionRequestV1) GetTransactionCreatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.TransactionCreatedAt
	}
	return nil
}

type GetCurrentBalanceAmountByAccountAndOwnerIDRequest struct {
	state          protoimpl.MessageState `protogen:"open.v1"`
	BalanceOwnerId *uint64                `protobuf:"varint,1,opt,name=balance_owner_id,json=balanceOwnerId" json:"balance_owner_id,omitempty"`
	AccountId      *uint64                `protobuf:"varint,2,opt,name=account_id,json=accountId" json:"account_id,omitempty"`
	unknownFields  protoimpl.UnknownFields
	sizeCache      protoimpl.SizeCache
}

func (x *GetCurrentBalanceAmountByAccountAndOwnerIDRequest) Reset() {
	*x = GetCurrentBalanceAmountByAccountAndOwnerIDRequest{}
	mi := &file_inner_processing_grpc_billing_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetCurrentBalanceAmountByAccountAndOwnerIDRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetCurrentBalanceAmountByAccountAndOwnerIDRequest) ProtoMessage() {}

func (x *GetCurrentBalanceAmountByAccountAndOwnerIDRequest) ProtoReflect() protoreflect.Message {
	mi := &file_inner_processing_grpc_billing_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetCurrentBalanceAmountByAccountAndOwnerIDRequest.ProtoReflect.Descriptor instead.
func (*GetCurrentBalanceAmountByAccountAndOwnerIDRequest) Descriptor() ([]byte, []int) {
	return file_inner_processing_grpc_billing_proto_rawDescGZIP(), []int{4}
}

func (x *GetCurrentBalanceAmountByAccountAndOwnerIDRequest) GetBalanceOwnerId() uint64 {
	if x != nil && x.BalanceOwnerId != nil {
		return *x.BalanceOwnerId
	}
	return 0
}

func (x *GetCurrentBalanceAmountByAccountAndOwnerIDRequest) GetAccountId() uint64 {
	if x != nil && x.AccountId != nil {
		return *x.AccountId
	}
	return 0
}

type GetCurrentBalanceAmountByAccountAndOwnerIDResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Amount        *float64               `protobuf:"fixed64,1,opt,name=amount" json:"amount,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetCurrentBalanceAmountByAccountAndOwnerIDResponse) Reset() {
	*x = GetCurrentBalanceAmountByAccountAndOwnerIDResponse{}
	mi := &file_inner_processing_grpc_billing_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetCurrentBalanceAmountByAccountAndOwnerIDResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetCurrentBalanceAmountByAccountAndOwnerIDResponse) ProtoMessage() {}

func (x *GetCurrentBalanceAmountByAccountAndOwnerIDResponse) ProtoReflect() protoreflect.Message {
	mi := &file_inner_processing_grpc_billing_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetCurrentBalanceAmountByAccountAndOwnerIDResponse.ProtoReflect.Descriptor instead.
func (*GetCurrentBalanceAmountByAccountAndOwnerIDResponse) Descriptor() ([]byte, []int) {
	return file_inner_processing_grpc_billing_proto_rawDescGZIP(), []int{5}
}

func (x *GetCurrentBalanceAmountByAccountAndOwnerIDResponse) GetAmount() float64 {
	if x != nil && x.Amount != nil {
		return *x.Amount
	}
	return 0
}

type BillRefundTransactionRequestV1 struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	MerchantId    *uint64                `protobuf:"varint,1,opt,name=merchant_id,json=merchantId" json:"merchant_id,omitempty"`
	ProjectId     *uint64                `protobuf:"varint,2,opt,name=project_id,json=projectId" json:"project_id,omitempty"`
	RefundId      *uint64                `protobuf:"varint,3,opt,name=refund_id,json=refundId" json:"refund_id,omitempty"`
	Amount        *float64               `protobuf:"fixed64,4,opt,name=amount" json:"amount,omitempty"`
	TerminalId    *uint64                `protobuf:"varint,5,opt,name=terminal_id,json=terminalId" json:"terminal_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *BillRefundTransactionRequestV1) Reset() {
	*x = BillRefundTransactionRequestV1{}
	mi := &file_inner_processing_grpc_billing_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BillRefundTransactionRequestV1) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BillRefundTransactionRequestV1) ProtoMessage() {}

func (x *BillRefundTransactionRequestV1) ProtoReflect() protoreflect.Message {
	mi := &file_inner_processing_grpc_billing_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BillRefundTransactionRequestV1.ProtoReflect.Descriptor instead.
func (*BillRefundTransactionRequestV1) Descriptor() ([]byte, []int) {
	return file_inner_processing_grpc_billing_proto_rawDescGZIP(), []int{6}
}

func (x *BillRefundTransactionRequestV1) GetMerchantId() uint64 {
	if x != nil && x.MerchantId != nil {
		return *x.MerchantId
	}
	return 0
}

func (x *BillRefundTransactionRequestV1) GetProjectId() uint64 {
	if x != nil && x.ProjectId != nil {
		return *x.ProjectId
	}
	return 0
}

func (x *BillRefundTransactionRequestV1) GetRefundId() uint64 {
	if x != nil && x.RefundId != nil {
		return *x.RefundId
	}
	return 0
}

func (x *BillRefundTransactionRequestV1) GetAmount() float64 {
	if x != nil && x.Amount != nil {
		return *x.Amount
	}
	return 0
}

func (x *BillRefundTransactionRequestV1) GetTerminalId() uint64 {
	if x != nil && x.TerminalId != nil {
		return *x.TerminalId
	}
	return 0
}

type CheckBalanceOwnerAccountExistsByBalanceOwnerIdAndAccountIdRequestV1 struct {
	state          protoimpl.MessageState `protogen:"open.v1"`
	BalanceOwnerId *uint64                `protobuf:"varint,1,opt,name=balance_owner_id,json=balanceOwnerId" json:"balance_owner_id,omitempty"`
	AccountId      *uint64                `protobuf:"varint,2,opt,name=account_id,json=accountId" json:"account_id,omitempty"`
	unknownFields  protoimpl.UnknownFields
	sizeCache      protoimpl.SizeCache
}

func (x *CheckBalanceOwnerAccountExistsByBalanceOwnerIdAndAccountIdRequestV1) Reset() {
	*x = CheckBalanceOwnerAccountExistsByBalanceOwnerIdAndAccountIdRequestV1{}
	mi := &file_inner_processing_grpc_billing_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CheckBalanceOwnerAccountExistsByBalanceOwnerIdAndAccountIdRequestV1) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CheckBalanceOwnerAccountExistsByBalanceOwnerIdAndAccountIdRequestV1) ProtoMessage() {}

func (x *CheckBalanceOwnerAccountExistsByBalanceOwnerIdAndAccountIdRequestV1) ProtoReflect() protoreflect.Message {
	mi := &file_inner_processing_grpc_billing_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CheckBalanceOwnerAccountExistsByBalanceOwnerIdAndAccountIdRequestV1.ProtoReflect.Descriptor instead.
func (*CheckBalanceOwnerAccountExistsByBalanceOwnerIdAndAccountIdRequestV1) Descriptor() ([]byte, []int) {
	return file_inner_processing_grpc_billing_proto_rawDescGZIP(), []int{7}
}

func (x *CheckBalanceOwnerAccountExistsByBalanceOwnerIdAndAccountIdRequestV1) GetBalanceOwnerId() uint64 {
	if x != nil && x.BalanceOwnerId != nil {
		return *x.BalanceOwnerId
	}
	return 0
}

func (x *CheckBalanceOwnerAccountExistsByBalanceOwnerIdAndAccountIdRequestV1) GetAccountId() uint64 {
	if x != nil && x.AccountId != nil {
		return *x.AccountId
	}
	return 0
}

type GetBalanceAccountByNumberRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	AccountNumber *string                `protobuf:"bytes,1,opt,name=account_number,json=accountNumber" json:"account_number,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetBalanceAccountByNumberRequest) Reset() {
	*x = GetBalanceAccountByNumberRequest{}
	mi := &file_inner_processing_grpc_billing_proto_msgTypes[8]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetBalanceAccountByNumberRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetBalanceAccountByNumberRequest) ProtoMessage() {}

func (x *GetBalanceAccountByNumberRequest) ProtoReflect() protoreflect.Message {
	mi := &file_inner_processing_grpc_billing_proto_msgTypes[8]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetBalanceAccountByNumberRequest.ProtoReflect.Descriptor instead.
func (*GetBalanceAccountByNumberRequest) Descriptor() ([]byte, []int) {
	return file_inner_processing_grpc_billing_proto_rawDescGZIP(), []int{8}
}

func (x *GetBalanceAccountByNumberRequest) GetAccountNumber() string {
	if x != nil && x.AccountNumber != nil {
		return *x.AccountNumber
	}
	return ""
}

type GetBalanceAccountByNumberResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	AccountId     *uint64                `protobuf:"varint,1,opt,name=account_id,json=accountId" json:"account_id,omitempty"`
	AccountNumber *string                `protobuf:"bytes,2,opt,name=account_number,json=accountNumber" json:"account_number,omitempty"`
	StatusId      *uint64                `protobuf:"varint,3,opt,name=status_id,json=statusId" json:"status_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetBalanceAccountByNumberResponse) Reset() {
	*x = GetBalanceAccountByNumberResponse{}
	mi := &file_inner_processing_grpc_billing_proto_msgTypes[9]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetBalanceAccountByNumberResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetBalanceAccountByNumberResponse) ProtoMessage() {}

func (x *GetBalanceAccountByNumberResponse) ProtoReflect() protoreflect.Message {
	mi := &file_inner_processing_grpc_billing_proto_msgTypes[9]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetBalanceAccountByNumberResponse.ProtoReflect.Descriptor instead.
func (*GetBalanceAccountByNumberResponse) Descriptor() ([]byte, []int) {
	return file_inner_processing_grpc_billing_proto_rawDescGZIP(), []int{9}
}

func (x *GetBalanceAccountByNumberResponse) GetAccountId() uint64 {
	if x != nil && x.AccountId != nil {
		return *x.AccountId
	}
	return 0
}

func (x *GetBalanceAccountByNumberResponse) GetAccountNumber() string {
	if x != nil && x.AccountNumber != nil {
		return *x.AccountNumber
	}
	return ""
}

func (x *GetBalanceAccountByNumberResponse) GetStatusId() uint64 {
	if x != nil && x.StatusId != nil {
		return *x.StatusId
	}
	return 0
}

type CheckOutTransferBalanceRequestV1 struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	ProjectId     *uint64                `protobuf:"varint,1,opt,name=project_id,json=projectId" json:"project_id,omitempty"`
	AccountNumber *string                `protobuf:"bytes,2,opt,name=account_number,json=accountNumber" json:"account_number,omitempty"`
	Amount        *float64               `protobuf:"fixed64,3,opt,name=amount" json:"amount,omitempty"`
	MerchantId    *uint64                `protobuf:"varint,4,opt,name=merchant_id,json=merchantId" json:"merchant_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CheckOutTransferBalanceRequestV1) Reset() {
	*x = CheckOutTransferBalanceRequestV1{}
	mi := &file_inner_processing_grpc_billing_proto_msgTypes[10]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CheckOutTransferBalanceRequestV1) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CheckOutTransferBalanceRequestV1) ProtoMessage() {}

func (x *CheckOutTransferBalanceRequestV1) ProtoReflect() protoreflect.Message {
	mi := &file_inner_processing_grpc_billing_proto_msgTypes[10]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CheckOutTransferBalanceRequestV1.ProtoReflect.Descriptor instead.
func (*CheckOutTransferBalanceRequestV1) Descriptor() ([]byte, []int) {
	return file_inner_processing_grpc_billing_proto_rawDescGZIP(), []int{10}
}

func (x *CheckOutTransferBalanceRequestV1) GetProjectId() uint64 {
	if x != nil && x.ProjectId != nil {
		return *x.ProjectId
	}
	return 0
}

func (x *CheckOutTransferBalanceRequestV1) GetAccountNumber() string {
	if x != nil && x.AccountNumber != nil {
		return *x.AccountNumber
	}
	return ""
}

func (x *CheckOutTransferBalanceRequestV1) GetAmount() float64 {
	if x != nil && x.Amount != nil {
		return *x.Amount
	}
	return 0
}

func (x *CheckOutTransferBalanceRequestV1) GetMerchantId() uint64 {
	if x != nil && x.MerchantId != nil {
		return *x.MerchantId
	}
	return 0
}

type SetBalanceOwnerSplittableRequestV1 struct {
	state          protoimpl.MessageState `protogen:"open.v1"`
	BalanceOwnerId *uint64                `protobuf:"varint,1,opt,name=balance_owner_id,json=balanceOwnerId" json:"balance_owner_id,omitempty"`
	unknownFields  protoimpl.UnknownFields
	sizeCache      protoimpl.SizeCache
}

func (x *SetBalanceOwnerSplittableRequestV1) Reset() {
	*x = SetBalanceOwnerSplittableRequestV1{}
	mi := &file_inner_processing_grpc_billing_proto_msgTypes[11]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SetBalanceOwnerSplittableRequestV1) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SetBalanceOwnerSplittableRequestV1) ProtoMessage() {}

func (x *SetBalanceOwnerSplittableRequestV1) ProtoReflect() protoreflect.Message {
	mi := &file_inner_processing_grpc_billing_proto_msgTypes[11]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SetBalanceOwnerSplittableRequestV1.ProtoReflect.Descriptor instead.
func (*SetBalanceOwnerSplittableRequestV1) Descriptor() ([]byte, []int) {
	return file_inner_processing_grpc_billing_proto_rawDescGZIP(), []int{11}
}

func (x *SetBalanceOwnerSplittableRequestV1) GetBalanceOwnerId() uint64 {
	if x != nil && x.BalanceOwnerId != nil {
		return *x.BalanceOwnerId
	}
	return 0
}

type GetBalanceOwnerRequestV1 struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	ProjectId     *uint64                `protobuf:"varint,1,opt,name=project_id,json=projectId" json:"project_id,omitempty"`
	MerchantId    *uint64                `protobuf:"varint,2,opt,name=merchant_id,json=merchantId" json:"merchant_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetBalanceOwnerRequestV1) Reset() {
	*x = GetBalanceOwnerRequestV1{}
	mi := &file_inner_processing_grpc_billing_proto_msgTypes[12]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetBalanceOwnerRequestV1) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetBalanceOwnerRequestV1) ProtoMessage() {}

func (x *GetBalanceOwnerRequestV1) ProtoReflect() protoreflect.Message {
	mi := &file_inner_processing_grpc_billing_proto_msgTypes[12]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetBalanceOwnerRequestV1.ProtoReflect.Descriptor instead.
func (*GetBalanceOwnerRequestV1) Descriptor() ([]byte, []int) {
	return file_inner_processing_grpc_billing_proto_rawDescGZIP(), []int{12}
}

func (x *GetBalanceOwnerRequestV1) GetProjectId() uint64 {
	if x != nil && x.ProjectId != nil {
		return *x.ProjectId
	}
	return 0
}

func (x *GetBalanceOwnerRequestV1) GetMerchantId() uint64 {
	if x != nil && x.MerchantId != nil {
		return *x.MerchantId
	}
	return 0
}

type CheckOutTransferBalanceResponseV1 struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	IsSufficient  *bool                  `protobuf:"varint,1,opt,name=is_sufficient,json=isSufficient" json:"is_sufficient,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CheckOutTransferBalanceResponseV1) Reset() {
	*x = CheckOutTransferBalanceResponseV1{}
	mi := &file_inner_processing_grpc_billing_proto_msgTypes[13]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CheckOutTransferBalanceResponseV1) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CheckOutTransferBalanceResponseV1) ProtoMessage() {}

func (x *CheckOutTransferBalanceResponseV1) ProtoReflect() protoreflect.Message {
	mi := &file_inner_processing_grpc_billing_proto_msgTypes[13]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CheckOutTransferBalanceResponseV1.ProtoReflect.Descriptor instead.
func (*CheckOutTransferBalanceResponseV1) Descriptor() ([]byte, []int) {
	return file_inner_processing_grpc_billing_proto_rawDescGZIP(), []int{13}
}

func (x *CheckOutTransferBalanceResponseV1) GetIsSufficient() bool {
	if x != nil && x.IsSufficient != nil {
		return *x.IsSufficient
	}
	return false
}

type BillOutTransferRequestV1 struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	ProjectId     *uint64                `protobuf:"varint,1,opt,name=project_id,json=projectId" json:"project_id,omitempty"`
	AccountNumber *string                `protobuf:"bytes,2,opt,name=account_number,json=accountNumber" json:"account_number,omitempty"`
	Amount        *float64               `protobuf:"fixed64,3,opt,name=amount" json:"amount,omitempty"`
	MerchantId    *uint64                `protobuf:"varint,4,opt,name=merchant_id,json=merchantId" json:"merchant_id,omitempty"`
	TransferId    *uint64                `protobuf:"varint,5,opt,name=transfer_id,json=transferId" json:"transfer_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *BillOutTransferRequestV1) Reset() {
	*x = BillOutTransferRequestV1{}
	mi := &file_inner_processing_grpc_billing_proto_msgTypes[14]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BillOutTransferRequestV1) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BillOutTransferRequestV1) ProtoMessage() {}

func (x *BillOutTransferRequestV1) ProtoReflect() protoreflect.Message {
	mi := &file_inner_processing_grpc_billing_proto_msgTypes[14]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BillOutTransferRequestV1.ProtoReflect.Descriptor instead.
func (*BillOutTransferRequestV1) Descriptor() ([]byte, []int) {
	return file_inner_processing_grpc_billing_proto_rawDescGZIP(), []int{14}
}

func (x *BillOutTransferRequestV1) GetProjectId() uint64 {
	if x != nil && x.ProjectId != nil {
		return *x.ProjectId
	}
	return 0
}

func (x *BillOutTransferRequestV1) GetAccountNumber() string {
	if x != nil && x.AccountNumber != nil {
		return *x.AccountNumber
	}
	return ""
}

func (x *BillOutTransferRequestV1) GetAmount() float64 {
	if x != nil && x.Amount != nil {
		return *x.Amount
	}
	return 0
}

func (x *BillOutTransferRequestV1) GetMerchantId() uint64 {
	if x != nil && x.MerchantId != nil {
		return *x.MerchantId
	}
	return 0
}

func (x *BillOutTransferRequestV1) GetTransferId() uint64 {
	if x != nil && x.TransferId != nil {
		return *x.TransferId
	}
	return 0
}

type BillInTransferRequestV1 struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	ProjectId     *uint64                `protobuf:"varint,1,opt,name=project_id,json=projectId" json:"project_id,omitempty"`
	AccountNumber *string                `protobuf:"bytes,2,opt,name=account_number,json=accountNumber" json:"account_number,omitempty"`
	Amount        *float64               `protobuf:"fixed64,3,opt,name=amount" json:"amount,omitempty"`
	MerchantId    *uint64                `protobuf:"varint,4,opt,name=merchant_id,json=merchantId" json:"merchant_id,omitempty"`
	TransferId    *uint64                `protobuf:"varint,5,opt,name=transfer_id,json=transferId" json:"transfer_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *BillInTransferRequestV1) Reset() {
	*x = BillInTransferRequestV1{}
	mi := &file_inner_processing_grpc_billing_proto_msgTypes[15]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BillInTransferRequestV1) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BillInTransferRequestV1) ProtoMessage() {}

func (x *BillInTransferRequestV1) ProtoReflect() protoreflect.Message {
	mi := &file_inner_processing_grpc_billing_proto_msgTypes[15]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BillInTransferRequestV1.ProtoReflect.Descriptor instead.
func (*BillInTransferRequestV1) Descriptor() ([]byte, []int) {
	return file_inner_processing_grpc_billing_proto_rawDescGZIP(), []int{15}
}

func (x *BillInTransferRequestV1) GetProjectId() uint64 {
	if x != nil && x.ProjectId != nil {
		return *x.ProjectId
	}
	return 0
}

func (x *BillInTransferRequestV1) GetAccountNumber() string {
	if x != nil && x.AccountNumber != nil {
		return *x.AccountNumber
	}
	return ""
}

func (x *BillInTransferRequestV1) GetAmount() float64 {
	if x != nil && x.Amount != nil {
		return *x.Amount
	}
	return 0
}

func (x *BillInTransferRequestV1) GetMerchantId() uint64 {
	if x != nil && x.MerchantId != nil {
		return *x.MerchantId
	}
	return 0
}

func (x *BillInTransferRequestV1) GetTransferId() uint64 {
	if x != nil && x.TransferId != nil {
		return *x.TransferId
	}
	return 0
}

type GetMerchantByBalanceOwnerRequestV1 struct {
	state          protoimpl.MessageState `protogen:"open.v1"`
	BalanceOwnerId *uint64                `protobuf:"varint,1,opt,name=balance_owner_id,json=balanceOwnerId" json:"balance_owner_id,omitempty"`
	unknownFields  protoimpl.UnknownFields
	sizeCache      protoimpl.SizeCache
}

func (x *GetMerchantByBalanceOwnerRequestV1) Reset() {
	*x = GetMerchantByBalanceOwnerRequestV1{}
	mi := &file_inner_processing_grpc_billing_proto_msgTypes[16]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetMerchantByBalanceOwnerRequestV1) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetMerchantByBalanceOwnerRequestV1) ProtoMessage() {}

func (x *GetMerchantByBalanceOwnerRequestV1) ProtoReflect() protoreflect.Message {
	mi := &file_inner_processing_grpc_billing_proto_msgTypes[16]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetMerchantByBalanceOwnerRequestV1.ProtoReflect.Descriptor instead.
func (*GetMerchantByBalanceOwnerRequestV1) Descriptor() ([]byte, []int) {
	return file_inner_processing_grpc_billing_proto_rawDescGZIP(), []int{16}
}

func (x *GetMerchantByBalanceOwnerRequestV1) GetBalanceOwnerId() uint64 {
	if x != nil && x.BalanceOwnerId != nil {
		return *x.BalanceOwnerId
	}
	return 0
}

type GetMerchantByBalanceOwnerResponseV1 struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	MerchantId    *uint64                `protobuf:"varint,1,opt,name=merchant_id,json=merchantId" json:"merchant_id,omitempty"`
	ProjectId     *uint64                `protobuf:"varint,2,opt,name=project_id,json=projectId" json:"project_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetMerchantByBalanceOwnerResponseV1) Reset() {
	*x = GetMerchantByBalanceOwnerResponseV1{}
	mi := &file_inner_processing_grpc_billing_proto_msgTypes[17]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetMerchantByBalanceOwnerResponseV1) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetMerchantByBalanceOwnerResponseV1) ProtoMessage() {}

func (x *GetMerchantByBalanceOwnerResponseV1) ProtoReflect() protoreflect.Message {
	mi := &file_inner_processing_grpc_billing_proto_msgTypes[17]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetMerchantByBalanceOwnerResponseV1.ProtoReflect.Descriptor instead.
func (*GetMerchantByBalanceOwnerResponseV1) Descriptor() ([]byte, []int) {
	return file_inner_processing_grpc_billing_proto_rawDescGZIP(), []int{17}
}

func (x *GetMerchantByBalanceOwnerResponseV1) GetMerchantId() uint64 {
	if x != nil && x.MerchantId != nil {
		return *x.MerchantId
	}
	return 0
}

func (x *GetMerchantByBalanceOwnerResponseV1) GetProjectId() uint64 {
	if x != nil && x.ProjectId != nil {
		return *x.ProjectId
	}
	return 0
}

type GetBalanceOwnerResponseV1 struct {
	state          protoimpl.MessageState `protogen:"open.v1"`
	Id             *uint64                `protobuf:"varint,3,opt,name=id" json:"id,omitempty"`
	Name           *string                `protobuf:"bytes,4,opt,name=name" json:"name,omitempty"`
	StatusId       *uint64                `protobuf:"varint,5,opt,name=status_id,json=statusId" json:"status_id,omitempty"`
	HasCreditFirst *bool                  `protobuf:"varint,6,opt,name=has_credit_first,json=hasCreditFirst" json:"has_credit_first,omitempty"`
	MerchantId     *uint64                `protobuf:"varint,7,opt,name=merchant_id,json=merchantId" json:"merchant_id,omitempty"`
	ProjectId      *uint64                `protobuf:"varint,8,opt,name=project_id,json=projectId" json:"project_id,omitempty"`
	Bin            *string                `protobuf:"bytes,9,opt,name=bin" json:"bin,omitempty"`
	EarnTypeId     *uint64                `protobuf:"varint,10,opt,name=earn_type_id,json=earnTypeId" json:"earn_type_id,omitempty"`
	IsSplittable   *bool                  `protobuf:"varint,11,opt,name=is_splittable,json=isSplittable" json:"is_splittable,omitempty"`
	EntityTypeId   *uint64                `protobuf:"varint,12,opt,name=entity_type_id,json=entityTypeId" json:"entity_type_id,omitempty"`
	CountryCodeId  *uint64                `protobuf:"varint,13,opt,name=country_code_id,json=countryCodeId" json:"country_code_id,omitempty"`
	unknownFields  protoimpl.UnknownFields
	sizeCache      protoimpl.SizeCache
}

func (x *GetBalanceOwnerResponseV1) Reset() {
	*x = GetBalanceOwnerResponseV1{}
	mi := &file_inner_processing_grpc_billing_proto_msgTypes[18]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetBalanceOwnerResponseV1) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetBalanceOwnerResponseV1) ProtoMessage() {}

func (x *GetBalanceOwnerResponseV1) ProtoReflect() protoreflect.Message {
	mi := &file_inner_processing_grpc_billing_proto_msgTypes[18]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetBalanceOwnerResponseV1.ProtoReflect.Descriptor instead.
func (*GetBalanceOwnerResponseV1) Descriptor() ([]byte, []int) {
	return file_inner_processing_grpc_billing_proto_rawDescGZIP(), []int{18}
}

func (x *GetBalanceOwnerResponseV1) GetId() uint64 {
	if x != nil && x.Id != nil {
		return *x.Id
	}
	return 0
}

func (x *GetBalanceOwnerResponseV1) GetName() string {
	if x != nil && x.Name != nil {
		return *x.Name
	}
	return ""
}

func (x *GetBalanceOwnerResponseV1) GetStatusId() uint64 {
	if x != nil && x.StatusId != nil {
		return *x.StatusId
	}
	return 0
}

func (x *GetBalanceOwnerResponseV1) GetHasCreditFirst() bool {
	if x != nil && x.HasCreditFirst != nil {
		return *x.HasCreditFirst
	}
	return false
}

func (x *GetBalanceOwnerResponseV1) GetMerchantId() uint64 {
	if x != nil && x.MerchantId != nil {
		return *x.MerchantId
	}
	return 0
}

func (x *GetBalanceOwnerResponseV1) GetProjectId() uint64 {
	if x != nil && x.ProjectId != nil {
		return *x.ProjectId
	}
	return 0
}

func (x *GetBalanceOwnerResponseV1) GetBin() string {
	if x != nil && x.Bin != nil {
		return *x.Bin
	}
	return ""
}

func (x *GetBalanceOwnerResponseV1) GetEarnTypeId() uint64 {
	if x != nil && x.EarnTypeId != nil {
		return *x.EarnTypeId
	}
	return 0
}

func (x *GetBalanceOwnerResponseV1) GetIsSplittable() bool {
	if x != nil && x.IsSplittable != nil {
		return *x.IsSplittable
	}
	return false
}

func (x *GetBalanceOwnerResponseV1) GetEntityTypeId() uint64 {
	if x != nil && x.EntityTypeId != nil {
		return *x.EntityTypeId
	}
	return 0
}

func (x *GetBalanceOwnerResponseV1) GetCountryCodeId() uint64 {
	if x != nil && x.CountryCodeId != nil {
		return *x.CountryCodeId
	}
	return 0
}

type SetInTransferRequestV1 struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	TransferId    *uint64                `protobuf:"varint,1,opt,name=transfer_id,json=transferId" json:"transfer_id,omitempty"`
	MerchantId    *uint64                `protobuf:"varint,2,opt,name=merchant_id,json=merchantId" json:"merchant_id,omitempty"`
	ProjectId     *uint64                `protobuf:"varint,3,opt,name=project_id,json=projectId" json:"project_id,omitempty"`
	AccountNumber *string                `protobuf:"bytes,4,opt,name=account_number,json=accountNumber" json:"account_number,omitempty"`
	Amount        *float64               `protobuf:"fixed64,5,opt,name=amount" json:"amount,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SetInTransferRequestV1) Reset() {
	*x = SetInTransferRequestV1{}
	mi := &file_inner_processing_grpc_billing_proto_msgTypes[19]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SetInTransferRequestV1) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SetInTransferRequestV1) ProtoMessage() {}

func (x *SetInTransferRequestV1) ProtoReflect() protoreflect.Message {
	mi := &file_inner_processing_grpc_billing_proto_msgTypes[19]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SetInTransferRequestV1.ProtoReflect.Descriptor instead.
func (*SetInTransferRequestV1) Descriptor() ([]byte, []int) {
	return file_inner_processing_grpc_billing_proto_rawDescGZIP(), []int{19}
}

func (x *SetInTransferRequestV1) GetTransferId() uint64 {
	if x != nil && x.TransferId != nil {
		return *x.TransferId
	}
	return 0
}

func (x *SetInTransferRequestV1) GetMerchantId() uint64 {
	if x != nil && x.MerchantId != nil {
		return *x.MerchantId
	}
	return 0
}

func (x *SetInTransferRequestV1) GetProjectId() uint64 {
	if x != nil && x.ProjectId != nil {
		return *x.ProjectId
	}
	return 0
}

func (x *SetInTransferRequestV1) GetAccountNumber() string {
	if x != nil && x.AccountNumber != nil {
		return *x.AccountNumber
	}
	return ""
}

func (x *SetInTransferRequestV1) GetAmount() float64 {
	if x != nil && x.Amount != nil {
		return *x.Amount
	}
	return 0
}

type SplitTransferOperationV1 struct {
	state            protoimpl.MessageState `protogen:"open.v1"`
	TransferId       *uint64                `protobuf:"varint,1,opt,name=transfer_id,json=transferId" json:"transfer_id,omitempty"`
	SplitOperationId *uint64                `protobuf:"varint,2,opt,name=split_operation_id,json=splitOperationId" json:"split_operation_id,omitempty"`
	MerchantId       *uint64                `protobuf:"varint,3,opt,name=merchant_id,json=merchantId" json:"merchant_id,omitempty"`
	ProjectId        *uint64                `protobuf:"varint,4,opt,name=project_id,json=projectId" json:"project_id,omitempty"`
	AccountNumber    *string                `protobuf:"bytes,5,opt,name=account_number,json=accountNumber" json:"account_number,omitempty"`
	Amount           *float64               `protobuf:"fixed64,6,opt,name=amount" json:"amount,omitempty"`
	SystemTax        *float64               `protobuf:"fixed64,7,opt,name=system_tax,json=systemTax" json:"system_tax,omitempty"`
	CommissionList   []*CommissionV1        `protobuf:"bytes,8,rep,name=commission_list,json=commissionList" json:"commission_list,omitempty"`
	unknownFields    protoimpl.UnknownFields
	sizeCache        protoimpl.SizeCache
}

func (x *SplitTransferOperationV1) Reset() {
	*x = SplitTransferOperationV1{}
	mi := &file_inner_processing_grpc_billing_proto_msgTypes[20]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SplitTransferOperationV1) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SplitTransferOperationV1) ProtoMessage() {}

func (x *SplitTransferOperationV1) ProtoReflect() protoreflect.Message {
	mi := &file_inner_processing_grpc_billing_proto_msgTypes[20]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SplitTransferOperationV1.ProtoReflect.Descriptor instead.
func (*SplitTransferOperationV1) Descriptor() ([]byte, []int) {
	return file_inner_processing_grpc_billing_proto_rawDescGZIP(), []int{20}
}

func (x *SplitTransferOperationV1) GetTransferId() uint64 {
	if x != nil && x.TransferId != nil {
		return *x.TransferId
	}
	return 0
}

func (x *SplitTransferOperationV1) GetSplitOperationId() uint64 {
	if x != nil && x.SplitOperationId != nil {
		return *x.SplitOperationId
	}
	return 0
}

func (x *SplitTransferOperationV1) GetMerchantId() uint64 {
	if x != nil && x.MerchantId != nil {
		return *x.MerchantId
	}
	return 0
}

func (x *SplitTransferOperationV1) GetProjectId() uint64 {
	if x != nil && x.ProjectId != nil {
		return *x.ProjectId
	}
	return 0
}

func (x *SplitTransferOperationV1) GetAccountNumber() string {
	if x != nil && x.AccountNumber != nil {
		return *x.AccountNumber
	}
	return ""
}

func (x *SplitTransferOperationV1) GetAmount() float64 {
	if x != nil && x.Amount != nil {
		return *x.Amount
	}
	return 0
}

func (x *SplitTransferOperationV1) GetSystemTax() float64 {
	if x != nil && x.SystemTax != nil {
		return *x.SystemTax
	}
	return 0
}

func (x *SplitTransferOperationV1) GetCommissionList() []*CommissionV1 {
	if x != nil {
		return x.CommissionList
	}
	return nil
}

type BillSplitTransferRequestV1 struct {
	state         protoimpl.MessageState      `protogen:"open.v1"`
	Operations    []*SplitTransferOperationV1 `protobuf:"bytes,1,rep,name=operations" json:"operations,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *BillSplitTransferRequestV1) Reset() {
	*x = BillSplitTransferRequestV1{}
	mi := &file_inner_processing_grpc_billing_proto_msgTypes[21]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BillSplitTransferRequestV1) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BillSplitTransferRequestV1) ProtoMessage() {}

func (x *BillSplitTransferRequestV1) ProtoReflect() protoreflect.Message {
	mi := &file_inner_processing_grpc_billing_proto_msgTypes[21]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BillSplitTransferRequestV1.ProtoReflect.Descriptor instead.
func (*BillSplitTransferRequestV1) Descriptor() ([]byte, []int) {
	return file_inner_processing_grpc_billing_proto_rawDescGZIP(), []int{21}
}

func (x *BillSplitTransferRequestV1) GetOperations() []*SplitTransferOperationV1 {
	if x != nil {
		return x.Operations
	}
	return nil
}

type GetBalanceOwnerByIDRequestV1 struct {
	state          protoimpl.MessageState `protogen:"open.v1"`
	BalanceOwnerId *uint64                `protobuf:"varint,1,opt,name=balance_owner_id,json=balanceOwnerId" json:"balance_owner_id,omitempty"`
	unknownFields  protoimpl.UnknownFields
	sizeCache      protoimpl.SizeCache
}

func (x *GetBalanceOwnerByIDRequestV1) Reset() {
	*x = GetBalanceOwnerByIDRequestV1{}
	mi := &file_inner_processing_grpc_billing_proto_msgTypes[22]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetBalanceOwnerByIDRequestV1) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetBalanceOwnerByIDRequestV1) ProtoMessage() {}

func (x *GetBalanceOwnerByIDRequestV1) ProtoReflect() protoreflect.Message {
	mi := &file_inner_processing_grpc_billing_proto_msgTypes[22]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetBalanceOwnerByIDRequestV1.ProtoReflect.Descriptor instead.
func (*GetBalanceOwnerByIDRequestV1) Descriptor() ([]byte, []int) {
	return file_inner_processing_grpc_billing_proto_rawDescGZIP(), []int{22}
}

func (x *GetBalanceOwnerByIDRequestV1) GetBalanceOwnerId() uint64 {
	if x != nil && x.BalanceOwnerId != nil {
		return *x.BalanceOwnerId
	}
	return 0
}

type GetEntityTypeByIDRequestV1 struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            *uint64                `protobuf:"varint,1,opt,name=id" json:"id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetEntityTypeByIDRequestV1) Reset() {
	*x = GetEntityTypeByIDRequestV1{}
	mi := &file_inner_processing_grpc_billing_proto_msgTypes[23]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetEntityTypeByIDRequestV1) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetEntityTypeByIDRequestV1) ProtoMessage() {}

func (x *GetEntityTypeByIDRequestV1) ProtoReflect() protoreflect.Message {
	mi := &file_inner_processing_grpc_billing_proto_msgTypes[23]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetEntityTypeByIDRequestV1.ProtoReflect.Descriptor instead.
func (*GetEntityTypeByIDRequestV1) Descriptor() ([]byte, []int) {
	return file_inner_processing_grpc_billing_proto_rawDescGZIP(), []int{23}
}

func (x *GetEntityTypeByIDRequestV1) GetId() uint64 {
	if x != nil && x.Id != nil {
		return *x.Id
	}
	return 0
}

type GetEntityTypeResponseV1 struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            *uint64                `protobuf:"varint,1,opt,name=id" json:"id,omitempty"`
	Code          *string                `protobuf:"bytes,2,opt,name=code" json:"code,omitempty"`
	Name          *string                `protobuf:"bytes,3,opt,name=name" json:"name,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetEntityTypeResponseV1) Reset() {
	*x = GetEntityTypeResponseV1{}
	mi := &file_inner_processing_grpc_billing_proto_msgTypes[24]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetEntityTypeResponseV1) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetEntityTypeResponseV1) ProtoMessage() {}

func (x *GetEntityTypeResponseV1) ProtoReflect() protoreflect.Message {
	mi := &file_inner_processing_grpc_billing_proto_msgTypes[24]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetEntityTypeResponseV1.ProtoReflect.Descriptor instead.
func (*GetEntityTypeResponseV1) Descriptor() ([]byte, []int) {
	return file_inner_processing_grpc_billing_proto_rawDescGZIP(), []int{24}
}

func (x *GetEntityTypeResponseV1) GetId() uint64 {
	if x != nil && x.Id != nil {
		return *x.Id
	}
	return 0
}

func (x *GetEntityTypeResponseV1) GetCode() string {
	if x != nil && x.Code != nil {
		return *x.Code
	}
	return ""
}

func (x *GetEntityTypeResponseV1) GetName() string {
	if x != nil && x.Name != nil {
		return *x.Name
	}
	return ""
}

type GetCountryCodeByIDRequestV1 struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            *uint64                `protobuf:"varint,1,opt,name=id" json:"id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetCountryCodeByIDRequestV1) Reset() {
	*x = GetCountryCodeByIDRequestV1{}
	mi := &file_inner_processing_grpc_billing_proto_msgTypes[25]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetCountryCodeByIDRequestV1) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetCountryCodeByIDRequestV1) ProtoMessage() {}

func (x *GetCountryCodeByIDRequestV1) ProtoReflect() protoreflect.Message {
	mi := &file_inner_processing_grpc_billing_proto_msgTypes[25]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetCountryCodeByIDRequestV1.ProtoReflect.Descriptor instead.
func (*GetCountryCodeByIDRequestV1) Descriptor() ([]byte, []int) {
	return file_inner_processing_grpc_billing_proto_rawDescGZIP(), []int{25}
}

func (x *GetCountryCodeByIDRequestV1) GetId() uint64 {
	if x != nil && x.Id != nil {
		return *x.Id
	}
	return 0
}

type GetCountryCodeResponseV1 struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            *uint64                `protobuf:"varint,1,opt,name=id" json:"id,omitempty"`
	Code          *string                `protobuf:"bytes,2,opt,name=code" json:"code,omitempty"`
	KazName       *string                `protobuf:"bytes,3,opt,name=kaz_name,json=kazName" json:"kaz_name,omitempty"`
	EngName       *string                `protobuf:"bytes,4,opt,name=eng_name,json=engName" json:"eng_name,omitempty"`
	RuName        *string                `protobuf:"bytes,5,opt,name=ru_name,json=ruName" json:"ru_name,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetCountryCodeResponseV1) Reset() {
	*x = GetCountryCodeResponseV1{}
	mi := &file_inner_processing_grpc_billing_proto_msgTypes[26]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetCountryCodeResponseV1) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetCountryCodeResponseV1) ProtoMessage() {}

func (x *GetCountryCodeResponseV1) ProtoReflect() protoreflect.Message {
	mi := &file_inner_processing_grpc_billing_proto_msgTypes[26]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetCountryCodeResponseV1.ProtoReflect.Descriptor instead.
func (*GetCountryCodeResponseV1) Descriptor() ([]byte, []int) {
	return file_inner_processing_grpc_billing_proto_rawDescGZIP(), []int{26}
}

func (x *GetCountryCodeResponseV1) GetId() uint64 {
	if x != nil && x.Id != nil {
		return *x.Id
	}
	return 0
}

func (x *GetCountryCodeResponseV1) GetCode() string {
	if x != nil && x.Code != nil {
		return *x.Code
	}
	return ""
}

func (x *GetCountryCodeResponseV1) GetKazName() string {
	if x != nil && x.KazName != nil {
		return *x.KazName
	}
	return ""
}

func (x *GetCountryCodeResponseV1) GetEngName() string {
	if x != nil && x.EngName != nil {
		return *x.EngName
	}
	return ""
}

func (x *GetCountryCodeResponseV1) GetRuName() string {
	if x != nil && x.RuName != nil {
		return *x.RuName
	}
	return ""
}

type GetBalanceByIDRequestV1 struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            *uint64                `protobuf:"varint,1,opt,name=id" json:"id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetBalanceByIDRequestV1) Reset() {
	*x = GetBalanceByIDRequestV1{}
	mi := &file_inner_processing_grpc_billing_proto_msgTypes[27]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetBalanceByIDRequestV1) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetBalanceByIDRequestV1) ProtoMessage() {}

func (x *GetBalanceByIDRequestV1) ProtoReflect() protoreflect.Message {
	mi := &file_inner_processing_grpc_billing_proto_msgTypes[27]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetBalanceByIDRequestV1.ProtoReflect.Descriptor instead.
func (*GetBalanceByIDRequestV1) Descriptor() ([]byte, []int) {
	return file_inner_processing_grpc_billing_proto_rawDescGZIP(), []int{27}
}

func (x *GetBalanceByIDRequestV1) GetId() uint64 {
	if x != nil && x.Id != nil {
		return *x.Id
	}
	return 0
}

type GetBalanceResponseV1 struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            *uint64                `protobuf:"varint,1,opt,name=id" json:"id,omitempty"`
	OwnerId       *uint64                `protobuf:"varint,2,opt,name=owner_id,json=ownerId" json:"owner_id,omitempty"`
	AccountId     *uint64                `protobuf:"varint,3,opt,name=account_id,json=accountId" json:"account_id,omitempty"`
	Amount        *float64               `protobuf:"fixed64,4,opt,name=amount" json:"amount,omitempty"`
	LastCalcTime  *timestamppb.Timestamp `protobuf:"bytes,5,opt,name=last_calc_time,json=lastCalcTime" json:"last_calc_time,omitempty"`
	HoldAmount    *float64               `protobuf:"fixed64,6,opt,name=hold_amount,json=holdAmount" json:"hold_amount,omitempty"`
	TypeId        *uint64                `protobuf:"varint,7,opt,name=type_id,json=typeId" json:"type_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetBalanceResponseV1) Reset() {
	*x = GetBalanceResponseV1{}
	mi := &file_inner_processing_grpc_billing_proto_msgTypes[28]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetBalanceResponseV1) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetBalanceResponseV1) ProtoMessage() {}

func (x *GetBalanceResponseV1) ProtoReflect() protoreflect.Message {
	mi := &file_inner_processing_grpc_billing_proto_msgTypes[28]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetBalanceResponseV1.ProtoReflect.Descriptor instead.
func (*GetBalanceResponseV1) Descriptor() ([]byte, []int) {
	return file_inner_processing_grpc_billing_proto_rawDescGZIP(), []int{28}
}

func (x *GetBalanceResponseV1) GetId() uint64 {
	if x != nil && x.Id != nil {
		return *x.Id
	}
	return 0
}

func (x *GetBalanceResponseV1) GetOwnerId() uint64 {
	if x != nil && x.OwnerId != nil {
		return *x.OwnerId
	}
	return 0
}

func (x *GetBalanceResponseV1) GetAccountId() uint64 {
	if x != nil && x.AccountId != nil {
		return *x.AccountId
	}
	return 0
}

func (x *GetBalanceResponseV1) GetAmount() float64 {
	if x != nil && x.Amount != nil {
		return *x.Amount
	}
	return 0
}

func (x *GetBalanceResponseV1) GetLastCalcTime() *timestamppb.Timestamp {
	if x != nil {
		return x.LastCalcTime
	}
	return nil
}

func (x *GetBalanceResponseV1) GetHoldAmount() float64 {
	if x != nil && x.HoldAmount != nil {
		return *x.HoldAmount
	}
	return 0
}

func (x *GetBalanceResponseV1) GetTypeId() uint64 {
	if x != nil && x.TypeId != nil {
		return *x.TypeId
	}
	return 0
}

type CheckHasBalanceRequestV1 struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	ProjectId     *uint64                `protobuf:"varint,1,opt,name=project_id,json=projectId" json:"project_id,omitempty"`
	MerchantId    *uint64                `protobuf:"varint,2,opt,name=merchant_id,json=merchantId" json:"merchant_id,omitempty"`
	AccountNumber *string                `protobuf:"bytes,3,opt,name=account_number,json=accountNumber" json:"account_number,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CheckHasBalanceRequestV1) Reset() {
	*x = CheckHasBalanceRequestV1{}
	mi := &file_inner_processing_grpc_billing_proto_msgTypes[29]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CheckHasBalanceRequestV1) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CheckHasBalanceRequestV1) ProtoMessage() {}

func (x *CheckHasBalanceRequestV1) ProtoReflect() protoreflect.Message {
	mi := &file_inner_processing_grpc_billing_proto_msgTypes[29]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CheckHasBalanceRequestV1.ProtoReflect.Descriptor instead.
func (*CheckHasBalanceRequestV1) Descriptor() ([]byte, []int) {
	return file_inner_processing_grpc_billing_proto_rawDescGZIP(), []int{29}
}

func (x *CheckHasBalanceRequestV1) GetProjectId() uint64 {
	if x != nil && x.ProjectId != nil {
		return *x.ProjectId
	}
	return 0
}

func (x *CheckHasBalanceRequestV1) GetMerchantId() uint64 {
	if x != nil && x.MerchantId != nil {
		return *x.MerchantId
	}
	return 0
}

func (x *CheckHasBalanceRequestV1) GetAccountNumber() string {
	if x != nil && x.AccountNumber != nil {
		return *x.AccountNumber
	}
	return ""
}

type BillingOperationBalanceTypeRef struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Code          *string                `protobuf:"bytes,1,opt,name=code" json:"code,omitempty"`
	Name          *string                `protobuf:"bytes,2,opt,name=name" json:"name,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *BillingOperationBalanceTypeRef) Reset() {
	*x = BillingOperationBalanceTypeRef{}
	mi := &file_inner_processing_grpc_billing_proto_msgTypes[30]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BillingOperationBalanceTypeRef) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BillingOperationBalanceTypeRef) ProtoMessage() {}

func (x *BillingOperationBalanceTypeRef) ProtoReflect() protoreflect.Message {
	mi := &file_inner_processing_grpc_billing_proto_msgTypes[30]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BillingOperationBalanceTypeRef.ProtoReflect.Descriptor instead.
func (*BillingOperationBalanceTypeRef) Descriptor() ([]byte, []int) {
	return file_inner_processing_grpc_billing_proto_rawDescGZIP(), []int{30}
}

func (x *BillingOperationBalanceTypeRef) GetCode() string {
	if x != nil && x.Code != nil {
		return *x.Code
	}
	return ""
}

func (x *BillingOperationBalanceTypeRef) GetName() string {
	if x != nil && x.Name != nil {
		return *x.Name
	}
	return ""
}

type BillingOperationTypeRef struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Code          *string                `protobuf:"bytes,1,opt,name=code" json:"code,omitempty"`
	Name          *string                `protobuf:"bytes,2,opt,name=name" json:"name,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *BillingOperationTypeRef) Reset() {
	*x = BillingOperationTypeRef{}
	mi := &file_inner_processing_grpc_billing_proto_msgTypes[31]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BillingOperationTypeRef) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BillingOperationTypeRef) ProtoMessage() {}

func (x *BillingOperationTypeRef) ProtoReflect() protoreflect.Message {
	mi := &file_inner_processing_grpc_billing_proto_msgTypes[31]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BillingOperationTypeRef.ProtoReflect.Descriptor instead.
func (*BillingOperationTypeRef) Descriptor() ([]byte, []int) {
	return file_inner_processing_grpc_billing_proto_rawDescGZIP(), []int{31}
}

func (x *BillingOperationTypeRef) GetCode() string {
	if x != nil && x.Code != nil {
		return *x.Code
	}
	return ""
}

func (x *BillingOperationTypeRef) GetName() string {
	if x != nil && x.Name != nil {
		return *x.Name
	}
	return ""
}

type BillingOperationStatusRef struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Code          *string                `protobuf:"bytes,1,opt,name=code" json:"code,omitempty"`
	Name          *string                `protobuf:"bytes,2,opt,name=name" json:"name,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *BillingOperationStatusRef) Reset() {
	*x = BillingOperationStatusRef{}
	mi := &file_inner_processing_grpc_billing_proto_msgTypes[32]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BillingOperationStatusRef) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BillingOperationStatusRef) ProtoMessage() {}

func (x *BillingOperationStatusRef) ProtoReflect() protoreflect.Message {
	mi := &file_inner_processing_grpc_billing_proto_msgTypes[32]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BillingOperationStatusRef.ProtoReflect.Descriptor instead.
func (*BillingOperationStatusRef) Descriptor() ([]byte, []int) {
	return file_inner_processing_grpc_billing_proto_rawDescGZIP(), []int{32}
}

func (x *BillingOperationStatusRef) GetCode() string {
	if x != nil && x.Code != nil {
		return *x.Code
	}
	return ""
}

func (x *BillingOperationStatusRef) GetName() string {
	if x != nil && x.Name != nil {
		return *x.Name
	}
	return ""
}

type BillingOperationTypeGroupRef struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Code          *string                `protobuf:"bytes,1,opt,name=code" json:"code,omitempty"`
	Name          *string                `protobuf:"bytes,2,opt,name=name" json:"name,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *BillingOperationTypeGroupRef) Reset() {
	*x = BillingOperationTypeGroupRef{}
	mi := &file_inner_processing_grpc_billing_proto_msgTypes[33]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BillingOperationTypeGroupRef) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BillingOperationTypeGroupRef) ProtoMessage() {}

func (x *BillingOperationTypeGroupRef) ProtoReflect() protoreflect.Message {
	mi := &file_inner_processing_grpc_billing_proto_msgTypes[33]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BillingOperationTypeGroupRef.ProtoReflect.Descriptor instead.
func (*BillingOperationTypeGroupRef) Descriptor() ([]byte, []int) {
	return file_inner_processing_grpc_billing_proto_rawDescGZIP(), []int{33}
}

func (x *BillingOperationTypeGroupRef) GetCode() string {
	if x != nil && x.Code != nil {
		return *x.Code
	}
	return ""
}

func (x *BillingOperationTypeGroupRef) GetName() string {
	if x != nil && x.Name != nil {
		return *x.Name
	}
	return ""
}

type BillingEarnTypeRef struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Code          *string                `protobuf:"bytes,1,opt,name=code" json:"code,omitempty"`
	Name          *string                `protobuf:"bytes,2,opt,name=name" json:"name,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *BillingEarnTypeRef) Reset() {
	*x = BillingEarnTypeRef{}
	mi := &file_inner_processing_grpc_billing_proto_msgTypes[34]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BillingEarnTypeRef) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BillingEarnTypeRef) ProtoMessage() {}

func (x *BillingEarnTypeRef) ProtoReflect() protoreflect.Message {
	mi := &file_inner_processing_grpc_billing_proto_msgTypes[34]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BillingEarnTypeRef.ProtoReflect.Descriptor instead.
func (*BillingEarnTypeRef) Descriptor() ([]byte, []int) {
	return file_inner_processing_grpc_billing_proto_rawDescGZIP(), []int{34}
}

func (x *BillingEarnTypeRef) GetCode() string {
	if x != nil && x.Code != nil {
		return *x.Code
	}
	return ""
}

func (x *BillingEarnTypeRef) GetName() string {
	if x != nil && x.Name != nil {
		return *x.Name
	}
	return ""
}

type BillingBalanceOwnerTypeRef struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Code          *string                `protobuf:"bytes,1,opt,name=code" json:"code,omitempty"`
	Name          *string                `protobuf:"bytes,2,opt,name=name" json:"name,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *BillingBalanceOwnerTypeRef) Reset() {
	*x = BillingBalanceOwnerTypeRef{}
	mi := &file_inner_processing_grpc_billing_proto_msgTypes[35]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BillingBalanceOwnerTypeRef) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BillingBalanceOwnerTypeRef) ProtoMessage() {}

func (x *BillingBalanceOwnerTypeRef) ProtoReflect() protoreflect.Message {
	mi := &file_inner_processing_grpc_billing_proto_msgTypes[35]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BillingBalanceOwnerTypeRef.ProtoReflect.Descriptor instead.
func (*BillingBalanceOwnerTypeRef) Descriptor() ([]byte, []int) {
	return file_inner_processing_grpc_billing_proto_rawDescGZIP(), []int{35}
}

func (x *BillingBalanceOwnerTypeRef) GetCode() string {
	if x != nil && x.Code != nil {
		return *x.Code
	}
	return ""
}

func (x *BillingBalanceOwnerTypeRef) GetName() string {
	if x != nil && x.Name != nil {
		return *x.Name
	}
	return ""
}

type BillingBalanceOwnerStatusRef struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Code          *string                `protobuf:"bytes,1,opt,name=code" json:"code,omitempty"`
	Name          *string                `protobuf:"bytes,2,opt,name=name" json:"name,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *BillingBalanceOwnerStatusRef) Reset() {
	*x = BillingBalanceOwnerStatusRef{}
	mi := &file_inner_processing_grpc_billing_proto_msgTypes[36]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BillingBalanceOwnerStatusRef) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BillingBalanceOwnerStatusRef) ProtoMessage() {}

func (x *BillingBalanceOwnerStatusRef) ProtoReflect() protoreflect.Message {
	mi := &file_inner_processing_grpc_billing_proto_msgTypes[36]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BillingBalanceOwnerStatusRef.ProtoReflect.Descriptor instead.
func (*BillingBalanceOwnerStatusRef) Descriptor() ([]byte, []int) {
	return file_inner_processing_grpc_billing_proto_rawDescGZIP(), []int{36}
}

func (x *BillingBalanceOwnerStatusRef) GetCode() string {
	if x != nil && x.Code != nil {
		return *x.Code
	}
	return ""
}

func (x *BillingBalanceOwnerStatusRef) GetName() string {
	if x != nil && x.Name != nil {
		return *x.Name
	}
	return ""
}

type BillingBalanceCreditStatusRef struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Code          *string                `protobuf:"bytes,1,opt,name=code" json:"code,omitempty"`
	Name          *string                `protobuf:"bytes,2,opt,name=name" json:"name,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *BillingBalanceCreditStatusRef) Reset() {
	*x = BillingBalanceCreditStatusRef{}
	mi := &file_inner_processing_grpc_billing_proto_msgTypes[37]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BillingBalanceCreditStatusRef) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BillingBalanceCreditStatusRef) ProtoMessage() {}

func (x *BillingBalanceCreditStatusRef) ProtoReflect() protoreflect.Message {
	mi := &file_inner_processing_grpc_billing_proto_msgTypes[37]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BillingBalanceCreditStatusRef.ProtoReflect.Descriptor instead.
func (*BillingBalanceCreditStatusRef) Descriptor() ([]byte, []int) {
	return file_inner_processing_grpc_billing_proto_rawDescGZIP(), []int{37}
}

func (x *BillingBalanceCreditStatusRef) GetCode() string {
	if x != nil && x.Code != nil {
		return *x.Code
	}
	return ""
}

func (x *BillingBalanceCreditStatusRef) GetName() string {
	if x != nil && x.Name != nil {
		return *x.Name
	}
	return ""
}

type BillingBalanceAccountStatusRef struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Code          *string                `protobuf:"bytes,1,opt,name=code" json:"code,omitempty"`
	Name          *string                `protobuf:"bytes,2,opt,name=name" json:"name,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *BillingBalanceAccountStatusRef) Reset() {
	*x = BillingBalanceAccountStatusRef{}
	mi := &file_inner_processing_grpc_billing_proto_msgTypes[38]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BillingBalanceAccountStatusRef) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BillingBalanceAccountStatusRef) ProtoMessage() {}

func (x *BillingBalanceAccountStatusRef) ProtoReflect() protoreflect.Message {
	mi := &file_inner_processing_grpc_billing_proto_msgTypes[38]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BillingBalanceAccountStatusRef.ProtoReflect.Descriptor instead.
func (*BillingBalanceAccountStatusRef) Descriptor() ([]byte, []int) {
	return file_inner_processing_grpc_billing_proto_rawDescGZIP(), []int{38}
}

func (x *BillingBalanceAccountStatusRef) GetCode() string {
	if x != nil && x.Code != nil {
		return *x.Code
	}
	return ""
}

func (x *BillingBalanceAccountStatusRef) GetName() string {
	if x != nil && x.Name != nil {
		return *x.Name
	}
	return ""
}

type BillingOperationModeRef struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Code          *string                `protobuf:"bytes,1,opt,name=code" json:"code,omitempty"`
	Name          *string                `protobuf:"bytes,2,opt,name=name" json:"name,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *BillingOperationModeRef) Reset() {
	*x = BillingOperationModeRef{}
	mi := &file_inner_processing_grpc_billing_proto_msgTypes[39]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BillingOperationModeRef) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BillingOperationModeRef) ProtoMessage() {}

func (x *BillingOperationModeRef) ProtoReflect() protoreflect.Message {
	mi := &file_inner_processing_grpc_billing_proto_msgTypes[39]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BillingOperationModeRef.ProtoReflect.Descriptor instead.
func (*BillingOperationModeRef) Descriptor() ([]byte, []int) {
	return file_inner_processing_grpc_billing_proto_rawDescGZIP(), []int{39}
}

func (x *BillingOperationModeRef) GetCode() string {
	if x != nil && x.Code != nil {
		return *x.Code
	}
	return ""
}

func (x *BillingOperationModeRef) GetName() string {
	if x != nil && x.Name != nil {
		return *x.Name
	}
	return ""
}

type BillingOperationTypeGroupRelationRef struct {
	state              protoimpl.MessageState     `protogen:"open.v1"`
	OperationType      *BillingOperationType      `protobuf:"varint,1,opt,name=operation_type,json=operationType,enum=processing.billing.billing.BillingOperationType" json:"operation_type,omitempty"`
	OperationTypeGroup *BillingOperationTypeGroup `protobuf:"varint,2,opt,name=operation_type_group,json=operationTypeGroup,enum=processing.billing.billing.BillingOperationTypeGroup" json:"operation_type_group,omitempty"`
	OperationMode      *BillingOperationMode      `protobuf:"varint,3,opt,name=operation_mode,json=operationMode,enum=processing.billing.billing.BillingOperationMode" json:"operation_mode,omitempty"`
	unknownFields      protoimpl.UnknownFields
	sizeCache          protoimpl.SizeCache
}

func (x *BillingOperationTypeGroupRelationRef) Reset() {
	*x = BillingOperationTypeGroupRelationRef{}
	mi := &file_inner_processing_grpc_billing_proto_msgTypes[40]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BillingOperationTypeGroupRelationRef) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BillingOperationTypeGroupRelationRef) ProtoMessage() {}

func (x *BillingOperationTypeGroupRelationRef) ProtoReflect() protoreflect.Message {
	mi := &file_inner_processing_grpc_billing_proto_msgTypes[40]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BillingOperationTypeGroupRelationRef.ProtoReflect.Descriptor instead.
func (*BillingOperationTypeGroupRelationRef) Descriptor() ([]byte, []int) {
	return file_inner_processing_grpc_billing_proto_rawDescGZIP(), []int{40}
}

func (x *BillingOperationTypeGroupRelationRef) GetOperationType() BillingOperationType {
	if x != nil && x.OperationType != nil {
		return *x.OperationType
	}
	return BillingOperationType_BillingOperationTypeUnknown
}

func (x *BillingOperationTypeGroupRelationRef) GetOperationTypeGroup() BillingOperationTypeGroup {
	if x != nil && x.OperationTypeGroup != nil {
		return *x.OperationTypeGroup
	}
	return BillingOperationTypeGroup_BillingOperationTypeGroupUnknown
}

func (x *BillingOperationTypeGroupRelationRef) GetOperationMode() BillingOperationMode {
	if x != nil && x.OperationMode != nil {
		return *x.OperationMode
	}
	return BillingOperationMode_BillingOperationModeUnknown
}

var file_inner_processing_grpc_billing_proto_extTypes = []protoimpl.ExtensionInfo{
	{
		ExtendedType:  (*descriptorpb.EnumValueOptions)(nil),
		ExtensionType: (*BillingOperationBalanceTypeRef)(nil),
		Field:         100100,
		Name:          "processing.billing.billing.operation_balance_type_value",
		Tag:           "bytes,100100,opt,name=operation_balance_type_value",
		Filename:      "inner/processing/grpc/billing.proto",
	},
	{
		ExtendedType:  (*descriptorpb.EnumOptions)(nil),
		ExtensionType: (*BillingOperationBalanceTypeRef)(nil),
		Field:         100101,
		Name:          "processing.billing.billing.default_operation_balance_type_value",
		Tag:           "bytes,100101,opt,name=default_operation_balance_type_value",
		Filename:      "inner/processing/grpc/billing.proto",
	},
	{
		ExtendedType:  (*descriptorpb.EnumValueOptions)(nil),
		ExtensionType: (*BillingOperationTypeRef)(nil),
		Field:         100102,
		Name:          "processing.billing.billing.operation_type_value",
		Tag:           "bytes,100102,opt,name=operation_type_value",
		Filename:      "inner/processing/grpc/billing.proto",
	},
	{
		ExtendedType:  (*descriptorpb.EnumOptions)(nil),
		ExtensionType: (*BillingOperationTypeRef)(nil),
		Field:         100103,
		Name:          "processing.billing.billing.default_operation_type_value",
		Tag:           "bytes,100103,opt,name=default_operation_type_value",
		Filename:      "inner/processing/grpc/billing.proto",
	},
	{
		ExtendedType:  (*descriptorpb.EnumValueOptions)(nil),
		ExtensionType: (*BillingOperationStatusRef)(nil),
		Field:         100104,
		Name:          "processing.billing.billing.operation_status_value",
		Tag:           "bytes,100104,opt,name=operation_status_value",
		Filename:      "inner/processing/grpc/billing.proto",
	},
	{
		ExtendedType:  (*descriptorpb.EnumOptions)(nil),
		ExtensionType: (*BillingOperationStatusRef)(nil),
		Field:         100105,
		Name:          "processing.billing.billing.default_operation_status_value",
		Tag:           "bytes,100105,opt,name=default_operation_status_value",
		Filename:      "inner/processing/grpc/billing.proto",
	},
	{
		ExtendedType:  (*descriptorpb.EnumValueOptions)(nil),
		ExtensionType: (*BillingOperationTypeGroupRef)(nil),
		Field:         100106,
		Name:          "processing.billing.billing.operation_type_group_value",
		Tag:           "bytes,100106,opt,name=operation_type_group_value",
		Filename:      "inner/processing/grpc/billing.proto",
	},
	{
		ExtendedType:  (*descriptorpb.EnumOptions)(nil),
		ExtensionType: (*BillingOperationTypeGroupRef)(nil),
		Field:         100107,
		Name:          "processing.billing.billing.default_operation_type_group_value",
		Tag:           "bytes,100107,opt,name=default_operation_type_group_value",
		Filename:      "inner/processing/grpc/billing.proto",
	},
	{
		ExtendedType:  (*descriptorpb.EnumValueOptions)(nil),
		ExtensionType: (*BillingEarnTypeRef)(nil),
		Field:         100108,
		Name:          "processing.billing.billing.earn_type_value",
		Tag:           "bytes,100108,opt,name=earn_type_value",
		Filename:      "inner/processing/grpc/billing.proto",
	},
	{
		ExtendedType:  (*descriptorpb.EnumOptions)(nil),
		ExtensionType: (*BillingEarnTypeRef)(nil),
		Field:         100109,
		Name:          "processing.billing.billing.default_earn_type_value",
		Tag:           "bytes,100109,opt,name=default_earn_type_value",
		Filename:      "inner/processing/grpc/billing.proto",
	},
	{
		ExtendedType:  (*descriptorpb.EnumValueOptions)(nil),
		ExtensionType: (*BillingBalanceOwnerTypeRef)(nil),
		Field:         1200110,
		Name:          "processing.billing.billing.balance_owner_type_value",
		Tag:           "bytes,1200110,opt,name=balance_owner_type_value",
		Filename:      "inner/processing/grpc/billing.proto",
	},
	{
		ExtendedType:  (*descriptorpb.EnumOptions)(nil),
		ExtensionType: (*BillingBalanceOwnerTypeRef)(nil),
		Field:         1200111,
		Name:          "processing.billing.billing.default_balance_owner_type_value",
		Tag:           "bytes,1200111,opt,name=default_balance_owner_type_value",
		Filename:      "inner/processing/grpc/billing.proto",
	},
	{
		ExtendedType:  (*descriptorpb.EnumValueOptions)(nil),
		ExtensionType: (*BillingBalanceOwnerStatusRef)(nil),
		Field:         100112,
		Name:          "processing.billing.billing.balance_owner_status_value",
		Tag:           "bytes,100112,opt,name=balance_owner_status_value",
		Filename:      "inner/processing/grpc/billing.proto",
	},
	{
		ExtendedType:  (*descriptorpb.EnumOptions)(nil),
		ExtensionType: (*BillingBalanceOwnerStatusRef)(nil),
		Field:         100113,
		Name:          "processing.billing.billing.default_balance_owner_status_value",
		Tag:           "bytes,100113,opt,name=default_balance_owner_status_value",
		Filename:      "inner/processing/grpc/billing.proto",
	},
	{
		ExtendedType:  (*descriptorpb.EnumValueOptions)(nil),
		ExtensionType: (*BillingBalanceCreditStatusRef)(nil),
		Field:         100114,
		Name:          "processing.billing.billing.balance_credit_status_value",
		Tag:           "bytes,100114,opt,name=balance_credit_status_value",
		Filename:      "inner/processing/grpc/billing.proto",
	},
	{
		ExtendedType:  (*descriptorpb.EnumOptions)(nil),
		ExtensionType: (*BillingBalanceCreditStatusRef)(nil),
		Field:         100115,
		Name:          "processing.billing.billing.default_balance_credit_status_value",
		Tag:           "bytes,100115,opt,name=default_balance_credit_status_value",
		Filename:      "inner/processing/grpc/billing.proto",
	},
	{
		ExtendedType:  (*descriptorpb.EnumValueOptions)(nil),
		ExtensionType: (*BillingBalanceAccountStatusRef)(nil),
		Field:         100116,
		Name:          "processing.billing.billing.balance_account_status_value",
		Tag:           "bytes,100116,opt,name=balance_account_status_value",
		Filename:      "inner/processing/grpc/billing.proto",
	},
	{
		ExtendedType:  (*descriptorpb.EnumOptions)(nil),
		ExtensionType: (*BillingBalanceAccountStatusRef)(nil),
		Field:         100117,
		Name:          "processing.billing.billing.default_balance_account_status_value",
		Tag:           "bytes,100117,opt,name=default_balance_account_status_value",
		Filename:      "inner/processing/grpc/billing.proto",
	},
	{
		ExtendedType:  (*descriptorpb.EnumValueOptions)(nil),
		ExtensionType: (*BillingOperationModeRef)(nil),
		Field:         100118,
		Name:          "processing.billing.billing.operation_mode_value",
		Tag:           "bytes,100118,opt,name=operation_mode_value",
		Filename:      "inner/processing/grpc/billing.proto",
	},
	{
		ExtendedType:  (*descriptorpb.EnumOptions)(nil),
		ExtensionType: (*BillingOperationModeRef)(nil),
		Field:         100119,
		Name:          "processing.billing.billing.default_operation_mode_value",
		Tag:           "bytes,100119,opt,name=default_operation_mode_value",
		Filename:      "inner/processing/grpc/billing.proto",
	},
	{
		ExtendedType:  (*descriptorpb.EnumValueOptions)(nil),
		ExtensionType: (*BillingOperationTypeGroupRelationRef)(nil),
		Field:         100120,
		Name:          "processing.billing.billing.group_relation_value",
		Tag:           "bytes,100120,opt,name=group_relation_value",
		Filename:      "inner/processing/grpc/billing.proto",
	},
	{
		ExtendedType:  (*descriptorpb.EnumOptions)(nil),
		ExtensionType: (*BillingOperationTypeGroupRelationRef)(nil),
		Field:         100121,
		Name:          "processing.billing.billing.default_group_relation_value",
		Tag:           "bytes,100121,opt,name=default_group_relation_value",
		Filename:      "inner/processing/grpc/billing.proto",
	},
}

// Extension fields to descriptorpb.EnumValueOptions.
var (
	// optional processing.billing.billing.BillingOperationBalanceTypeRef operation_balance_type_value = 100100;
	E_OperationBalanceTypeValue = &file_inner_processing_grpc_billing_proto_extTypes[0]
	// optional processing.billing.billing.BillingOperationTypeRef operation_type_value = 100102;
	E_OperationTypeValue = &file_inner_processing_grpc_billing_proto_extTypes[2]
	// optional processing.billing.billing.BillingOperationStatusRef operation_status_value = 100104;
	E_OperationStatusValue = &file_inner_processing_grpc_billing_proto_extTypes[4]
	// optional processing.billing.billing.BillingOperationTypeGroupRef operation_type_group_value = 100106;
	E_OperationTypeGroupValue = &file_inner_processing_grpc_billing_proto_extTypes[6]
	// optional processing.billing.billing.BillingEarnTypeRef earn_type_value = 100108;
	E_EarnTypeValue = &file_inner_processing_grpc_billing_proto_extTypes[8]
	// optional processing.billing.billing.BillingBalanceOwnerTypeRef balance_owner_type_value = 1200110;
	E_BalanceOwnerTypeValue = &file_inner_processing_grpc_billing_proto_extTypes[10]
	// optional processing.billing.billing.BillingBalanceOwnerStatusRef balance_owner_status_value = 100112;
	E_BalanceOwnerStatusValue = &file_inner_processing_grpc_billing_proto_extTypes[12]
	// optional processing.billing.billing.BillingBalanceCreditStatusRef balance_credit_status_value = 100114;
	E_BalanceCreditStatusValue = &file_inner_processing_grpc_billing_proto_extTypes[14]
	// optional processing.billing.billing.BillingBalanceAccountStatusRef balance_account_status_value = 100116;
	E_BalanceAccountStatusValue = &file_inner_processing_grpc_billing_proto_extTypes[16]
	// optional processing.billing.billing.BillingOperationModeRef operation_mode_value = 100118;
	E_OperationModeValue = &file_inner_processing_grpc_billing_proto_extTypes[18]
	// optional processing.billing.billing.BillingOperationTypeGroupRelationRef group_relation_value = 100120;
	E_GroupRelationValue = &file_inner_processing_grpc_billing_proto_extTypes[20]
)

// Extension fields to descriptorpb.EnumOptions.
var (
	// optional processing.billing.billing.BillingOperationBalanceTypeRef default_operation_balance_type_value = 100101;
	E_DefaultOperationBalanceTypeValue = &file_inner_processing_grpc_billing_proto_extTypes[1]
	// optional processing.billing.billing.BillingOperationTypeRef default_operation_type_value = 100103;
	E_DefaultOperationTypeValue = &file_inner_processing_grpc_billing_proto_extTypes[3]
	// optional processing.billing.billing.BillingOperationStatusRef default_operation_status_value = 100105;
	E_DefaultOperationStatusValue = &file_inner_processing_grpc_billing_proto_extTypes[5]
	// optional processing.billing.billing.BillingOperationTypeGroupRef default_operation_type_group_value = 100107;
	E_DefaultOperationTypeGroupValue = &file_inner_processing_grpc_billing_proto_extTypes[7]
	// optional processing.billing.billing.BillingEarnTypeRef default_earn_type_value = 100109;
	E_DefaultEarnTypeValue = &file_inner_processing_grpc_billing_proto_extTypes[9]
	// optional processing.billing.billing.BillingBalanceOwnerTypeRef default_balance_owner_type_value = 1200111;
	E_DefaultBalanceOwnerTypeValue = &file_inner_processing_grpc_billing_proto_extTypes[11]
	// optional processing.billing.billing.BillingBalanceOwnerStatusRef default_balance_owner_status_value = 100113;
	E_DefaultBalanceOwnerStatusValue = &file_inner_processing_grpc_billing_proto_extTypes[13]
	// optional processing.billing.billing.BillingBalanceCreditStatusRef default_balance_credit_status_value = 100115;
	E_DefaultBalanceCreditStatusValue = &file_inner_processing_grpc_billing_proto_extTypes[15]
	// optional processing.billing.billing.BillingBalanceAccountStatusRef default_balance_account_status_value = 100117;
	E_DefaultBalanceAccountStatusValue = &file_inner_processing_grpc_billing_proto_extTypes[17]
	// optional processing.billing.billing.BillingOperationModeRef default_operation_mode_value = 100119;
	E_DefaultOperationModeValue = &file_inner_processing_grpc_billing_proto_extTypes[19]
	// optional processing.billing.billing.BillingOperationTypeGroupRelationRef default_group_relation_value = 100121;
	E_DefaultGroupRelationValue = &file_inner_processing_grpc_billing_proto_extTypes[21]
)

var File_inner_processing_grpc_billing_proto protoreflect.FileDescriptor

var file_inner_processing_grpc_billing_proto_rawDesc = string([]byte{
	0x0a, 0x23, 0x69, 0x6e, 0x6e, 0x65, 0x72, 0x2f, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69,
	0x6e, 0x67, 0x2f, 0x67, 0x72, 0x70, 0x63, 0x2f, 0x62, 0x69, 0x6c, 0x6c, 0x69, 0x6e, 0x67, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x1a, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e,
	0x67, 0x2e, 0x62, 0x69, 0x6c, 0x6c, 0x69, 0x6e, 0x67, 0x2e, 0x62, 0x69, 0x6c, 0x6c, 0x69, 0x6e,
	0x67, 0x1a, 0x1f, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62,
	0x75, 0x66, 0x2f, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x1a, 0x1b, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x62, 0x75, 0x66, 0x2f, 0x65, 0x6d, 0x70, 0x74, 0x79, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a,
	0x26, 0x69, 0x6e, 0x6e, 0x65, 0x72, 0x2f, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e,
	0x67, 0x2f, 0x67, 0x72, 0x70, 0x63, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f,
	0x6e, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x14, 0x6d, 0x76, 0x70, 0x2f, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x2f, 0x72, 0x65, 0x66, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x20, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x64,
	0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x6f, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22,
	0x98, 0x01, 0x0a, 0x17, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x50, 0x61, 0x79, 0x4f, 0x75, 0x74, 0x42,
	0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65, 0x52, 0x65, 0x71, 0x56, 0x31, 0x12, 0x1f, 0x0a, 0x0b, 0x6d,
	0x65, 0x72, 0x63, 0x68, 0x61, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04,
	0x52, 0x0a, 0x6d, 0x65, 0x72, 0x63, 0x68, 0x61, 0x6e, 0x74, 0x49, 0x64, 0x12, 0x1d, 0x0a, 0x0a,
	0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x04,
	0x52, 0x09, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x49, 0x64, 0x12, 0x25, 0x0a, 0x0e, 0x61,
	0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0d, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x4e, 0x75, 0x6d, 0x62,
	0x65, 0x72, 0x12, 0x16, 0x0a, 0x06, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x01, 0x52, 0x06, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x22, 0x4f, 0x0a, 0x17, 0x43, 0x68,
	0x65, 0x63, 0x6b, 0x50, 0x61, 0x79, 0x4f, 0x75, 0x74, 0x42, 0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65,
	0x52, 0x65, 0x73, 0x56, 0x31, 0x12, 0x34, 0x0a, 0x16, 0x68, 0x61, 0x73, 0x5f, 0x73, 0x75, 0x66,
	0x66, 0x69, 0x63, 0x69, 0x65, 0x6e, 0x74, 0x5f, 0x62, 0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x08, 0x52, 0x14, 0x68, 0x61, 0x73, 0x53, 0x75, 0x66, 0x66, 0x69, 0x63,
	0x69, 0x65, 0x6e, 0x74, 0x42, 0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65, 0x22, 0xbf, 0x01, 0x0a, 0x1d,
	0x42, 0x69, 0x6c, 0x6c, 0x50, 0x61, 0x79, 0x49, 0x6e, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63,
	0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x56, 0x31, 0x12, 0x1f, 0x0a,
	0x0b, 0x6d, 0x65, 0x72, 0x63, 0x68, 0x61, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x04, 0x52, 0x0a, 0x6d, 0x65, 0x72, 0x63, 0x68, 0x61, 0x6e, 0x74, 0x49, 0x64, 0x12, 0x1d,
	0x0a, 0x0a, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x04, 0x52, 0x09, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x49, 0x64, 0x12, 0x25, 0x0a,
	0x0e, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x69, 0x64, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x04, 0x52, 0x0d, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69,
	0x6f, 0x6e, 0x49, 0x64, 0x12, 0x16, 0x0a, 0x06, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x01, 0x52, 0x06, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x1f, 0x0a, 0x0b,
	0x74, 0x65, 0x72, 0x6d, 0x69, 0x6e, 0x61, 0x6c, 0x5f, 0x69, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28,
	0x04, 0x52, 0x0a, 0x74, 0x65, 0x72, 0x6d, 0x69, 0x6e, 0x61, 0x6c, 0x49, 0x64, 0x22, 0x92, 0x02,
	0x0a, 0x1e, 0x42, 0x69, 0x6c, 0x6c, 0x50, 0x61, 0x79, 0x4f, 0x75, 0x74, 0x54, 0x72, 0x61, 0x6e,
	0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x56, 0x31,
	0x12, 0x1f, 0x0a, 0x0b, 0x6d, 0x65, 0x72, 0x63, 0x68, 0x61, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x0a, 0x6d, 0x65, 0x72, 0x63, 0x68, 0x61, 0x6e, 0x74, 0x49,
	0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x5f, 0x69, 0x64, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x04, 0x52, 0x09, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x49, 0x64,
	0x12, 0x25, 0x0a, 0x0e, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x5f,
	0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x04, 0x52, 0x0d, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61,
	0x63, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x12, 0x16, 0x0a, 0x06, 0x61, 0x6d, 0x6f, 0x75, 0x6e,
	0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x01, 0x52, 0x06, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x12,
	0x1f, 0x0a, 0x0b, 0x74, 0x65, 0x72, 0x6d, 0x69, 0x6e, 0x61, 0x6c, 0x5f, 0x69, 0x64, 0x18, 0x05,
	0x20, 0x01, 0x28, 0x04, 0x52, 0x0a, 0x74, 0x65, 0x72, 0x6d, 0x69, 0x6e, 0x61, 0x6c, 0x49, 0x64,
	0x12, 0x50, 0x0a, 0x16, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x5f,
	0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62,
	0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x14, 0x74, 0x72,
	0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64,
	0x41, 0x74, 0x22, 0x7c, 0x0a, 0x31, 0x47, 0x65, 0x74, 0x43, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x74,
	0x42, 0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65, 0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x42, 0x79, 0x41,
	0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x41, 0x6e, 0x64, 0x4f, 0x77, 0x6e, 0x65, 0x72, 0x49, 0x44,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x28, 0x0a, 0x10, 0x62, 0x61, 0x6c, 0x61, 0x6e,
	0x63, 0x65, 0x5f, 0x6f, 0x77, 0x6e, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x04, 0x52, 0x0e, 0x62, 0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65, 0x4f, 0x77, 0x6e, 0x65, 0x72, 0x49,
	0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x04, 0x52, 0x09, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x49, 0x64,
	0x22, 0x4c, 0x0a, 0x32, 0x47, 0x65, 0x74, 0x43, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x74, 0x42, 0x61,
	0x6c, 0x61, 0x6e, 0x63, 0x65, 0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x42, 0x79, 0x41, 0x63, 0x63,
	0x6f, 0x75, 0x6e, 0x74, 0x41, 0x6e, 0x64, 0x4f, 0x77, 0x6e, 0x65, 0x72, 0x49, 0x44, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x01, 0x52, 0x06, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x22, 0xb6,
	0x01, 0x0a, 0x1e, 0x42, 0x69, 0x6c, 0x6c, 0x52, 0x65, 0x66, 0x75, 0x6e, 0x64, 0x54, 0x72, 0x61,
	0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x56,
	0x31, 0x12, 0x1f, 0x0a, 0x0b, 0x6d, 0x65, 0x72, 0x63, 0x68, 0x61, 0x6e, 0x74, 0x5f, 0x69, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x0a, 0x6d, 0x65, 0x72, 0x63, 0x68, 0x61, 0x6e, 0x74,
	0x49, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x5f, 0x69, 0x64,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x04, 0x52, 0x09, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x49,
	0x64, 0x12, 0x1b, 0x0a, 0x09, 0x72, 0x65, 0x66, 0x75, 0x6e, 0x64, 0x5f, 0x69, 0x64, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x04, 0x52, 0x08, 0x72, 0x65, 0x66, 0x75, 0x6e, 0x64, 0x49, 0x64, 0x12, 0x16,
	0x0a, 0x06, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x01, 0x52, 0x06,
	0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x1f, 0x0a, 0x0b, 0x74, 0x65, 0x72, 0x6d, 0x69, 0x6e,
	0x61, 0x6c, 0x5f, 0x69, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x04, 0x52, 0x0a, 0x74, 0x65, 0x72,
	0x6d, 0x69, 0x6e, 0x61, 0x6c, 0x49, 0x64, 0x22, 0x8e, 0x01, 0x0a, 0x43, 0x43, 0x68, 0x65, 0x63,
	0x6b, 0x42, 0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65, 0x4f, 0x77, 0x6e, 0x65, 0x72, 0x41, 0x63, 0x63,
	0x6f, 0x75, 0x6e, 0x74, 0x45, 0x78, 0x69, 0x73, 0x74, 0x73, 0x42, 0x79, 0x42, 0x61, 0x6c, 0x61,
	0x6e, 0x63, 0x65, 0x4f, 0x77, 0x6e, 0x65, 0x72, 0x49, 0x64, 0x41, 0x6e, 0x64, 0x41, 0x63, 0x63,
	0x6f, 0x75, 0x6e, 0x74, 0x49, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x56, 0x31, 0x12,
	0x28, 0x0a, 0x10, 0x62, 0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65, 0x5f, 0x6f, 0x77, 0x6e, 0x65, 0x72,
	0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x0e, 0x62, 0x61, 0x6c, 0x61, 0x6e,
	0x63, 0x65, 0x4f, 0x77, 0x6e, 0x65, 0x72, 0x49, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x61, 0x63, 0x63,
	0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x04, 0x52, 0x09, 0x61,
	0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x49, 0x64, 0x22, 0x49, 0x0a, 0x20, 0x47, 0x65, 0x74, 0x42,
	0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x42, 0x79, 0x4e,
	0x75, 0x6d, 0x62, 0x65, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x25, 0x0a, 0x0e,
	0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x4e, 0x75, 0x6d,
	0x62, 0x65, 0x72, 0x22, 0x86, 0x01, 0x0a, 0x21, 0x47, 0x65, 0x74, 0x42, 0x61, 0x6c, 0x61, 0x6e,
	0x63, 0x65, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x42, 0x79, 0x4e, 0x75, 0x6d, 0x62, 0x65,
	0x72, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x61, 0x63, 0x63,
	0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x09, 0x61,
	0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x49, 0x64, 0x12, 0x25, 0x0a, 0x0e, 0x61, 0x63, 0x63, 0x6f,
	0x75, 0x6e, 0x74, 0x5f, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0d, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x12,
	0x1b, 0x0a, 0x09, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x04, 0x52, 0x08, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x49, 0x64, 0x22, 0xa1, 0x01, 0x0a,
	0x20, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x4f, 0x75, 0x74, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x66, 0x65,
	0x72, 0x42, 0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x56,
	0x31, 0x12, 0x1d, 0x0a, 0x0a, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x5f, 0x69, 0x64, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x09, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x49, 0x64,
	0x12, 0x25, 0x0a, 0x0e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x6e, 0x75, 0x6d, 0x62,
	0x65, 0x72, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e,
	0x74, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x12, 0x16, 0x0a, 0x06, 0x61, 0x6d, 0x6f, 0x75, 0x6e,
	0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x01, 0x52, 0x06, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x12,
	0x1f, 0x0a, 0x0b, 0x6d, 0x65, 0x72, 0x63, 0x68, 0x61, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x04, 0x52, 0x0a, 0x6d, 0x65, 0x72, 0x63, 0x68, 0x61, 0x6e, 0x74, 0x49, 0x64,
	0x22, 0x4e, 0x0a, 0x22, 0x53, 0x65, 0x74, 0x42, 0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65, 0x4f, 0x77,
	0x6e, 0x65, 0x72, 0x53, 0x70, 0x6c, 0x69, 0x74, 0x74, 0x61, 0x62, 0x6c, 0x65, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x56, 0x31, 0x12, 0x28, 0x0a, 0x10, 0x62, 0x61, 0x6c, 0x61, 0x6e, 0x63,
	0x65, 0x5f, 0x6f, 0x77, 0x6e, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04,
	0x52, 0x0e, 0x62, 0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65, 0x4f, 0x77, 0x6e, 0x65, 0x72, 0x49, 0x64,
	0x22, 0x5a, 0x0a, 0x18, 0x47, 0x65, 0x74, 0x42, 0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65, 0x4f, 0x77,
	0x6e, 0x65, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x56, 0x31, 0x12, 0x1d, 0x0a, 0x0a,
	0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04,
	0x52, 0x09, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x49, 0x64, 0x12, 0x1f, 0x0a, 0x0b, 0x6d,
	0x65, 0x72, 0x63, 0x68, 0x61, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x04,
	0x52, 0x0a, 0x6d, 0x65, 0x72, 0x63, 0x68, 0x61, 0x6e, 0x74, 0x49, 0x64, 0x22, 0x48, 0x0a, 0x21,
	0x43, 0x68, 0x65, 0x63, 0x6b, 0x4f, 0x75, 0x74, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x66, 0x65, 0x72,
	0x42, 0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x56,
	0x31, 0x12, 0x23, 0x0a, 0x0d, 0x69, 0x73, 0x5f, 0x73, 0x75, 0x66, 0x66, 0x69, 0x63, 0x69, 0x65,
	0x6e, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0c, 0x69, 0x73, 0x53, 0x75, 0x66, 0x66,
	0x69, 0x63, 0x69, 0x65, 0x6e, 0x74, 0x22, 0xba, 0x01, 0x0a, 0x18, 0x42, 0x69, 0x6c, 0x6c, 0x4f,
	0x75, 0x74, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x66, 0x65, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x56, 0x31, 0x12, 0x1d, 0x0a, 0x0a, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x5f, 0x69,
	0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x09, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74,
	0x49, 0x64, 0x12, 0x25, 0x0a, 0x0e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x6e, 0x75,
	0x6d, 0x62, 0x65, 0x72, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x61, 0x63, 0x63, 0x6f,
	0x75, 0x6e, 0x74, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x12, 0x16, 0x0a, 0x06, 0x61, 0x6d, 0x6f,
	0x75, 0x6e, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x01, 0x52, 0x06, 0x61, 0x6d, 0x6f, 0x75, 0x6e,
	0x74, 0x12, 0x1f, 0x0a, 0x0b, 0x6d, 0x65, 0x72, 0x63, 0x68, 0x61, 0x6e, 0x74, 0x5f, 0x69, 0x64,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x04, 0x52, 0x0a, 0x6d, 0x65, 0x72, 0x63, 0x68, 0x61, 0x6e, 0x74,
	0x49, 0x64, 0x12, 0x1f, 0x0a, 0x0b, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x66, 0x65, 0x72, 0x5f, 0x69,
	0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x04, 0x52, 0x0a, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x66, 0x65,
	0x72, 0x49, 0x64, 0x22, 0xb9, 0x01, 0x0a, 0x17, 0x42, 0x69, 0x6c, 0x6c, 0x49, 0x6e, 0x54, 0x72,
	0x61, 0x6e, 0x73, 0x66, 0x65, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x56, 0x31, 0x12,
	0x1d, 0x0a, 0x0a, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x04, 0x52, 0x09, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x49, 0x64, 0x12, 0x25,
	0x0a, 0x0e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x4e,
	0x75, 0x6d, 0x62, 0x65, 0x72, 0x12, 0x16, 0x0a, 0x06, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x01, 0x52, 0x06, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x1f, 0x0a,
	0x0b, 0x6d, 0x65, 0x72, 0x63, 0x68, 0x61, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x04, 0x52, 0x0a, 0x6d, 0x65, 0x72, 0x63, 0x68, 0x61, 0x6e, 0x74, 0x49, 0x64, 0x12, 0x1f,
	0x0a, 0x0b, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x66, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x05, 0x20,
	0x01, 0x28, 0x04, 0x52, 0x0a, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x66, 0x65, 0x72, 0x49, 0x64, 0x22,
	0x4e, 0x0a, 0x22, 0x47, 0x65, 0x74, 0x4d, 0x65, 0x72, 0x63, 0x68, 0x61, 0x6e, 0x74, 0x42, 0x79,
	0x42, 0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65, 0x4f, 0x77, 0x6e, 0x65, 0x72, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x56, 0x31, 0x12, 0x28, 0x0a, 0x10, 0x62, 0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65,
	0x5f, 0x6f, 0x77, 0x6e, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52,
	0x0e, 0x62, 0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65, 0x4f, 0x77, 0x6e, 0x65, 0x72, 0x49, 0x64, 0x22,
	0x65, 0x0a, 0x23, 0x47, 0x65, 0x74, 0x4d, 0x65, 0x72, 0x63, 0x68, 0x61, 0x6e, 0x74, 0x42, 0x79,
	0x42, 0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65, 0x4f, 0x77, 0x6e, 0x65, 0x72, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x56, 0x31, 0x12, 0x1f, 0x0a, 0x0b, 0x6d, 0x65, 0x72, 0x63, 0x68, 0x61,
	0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x0a, 0x6d, 0x65, 0x72,
	0x63, 0x68, 0x61, 0x6e, 0x74, 0x49, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x70, 0x72, 0x6f, 0x6a, 0x65,
	0x63, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x04, 0x52, 0x09, 0x70, 0x72, 0x6f,
	0x6a, 0x65, 0x63, 0x74, 0x49, 0x64, 0x22, 0xed, 0x02, 0x0a, 0x19, 0x47, 0x65, 0x74, 0x42, 0x61,
	0x6c, 0x61, 0x6e, 0x63, 0x65, 0x4f, 0x77, 0x6e, 0x65, 0x72, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x56, 0x31, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x04,
	0x52, 0x02, 0x69, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x1b, 0x0a, 0x09, 0x73, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x5f, 0x69, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x04, 0x52, 0x08, 0x73, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x49, 0x64, 0x12, 0x28, 0x0a, 0x10, 0x68, 0x61, 0x73, 0x5f, 0x63, 0x72, 0x65,
	0x64, 0x69, 0x74, 0x5f, 0x66, 0x69, 0x72, 0x73, 0x74, 0x18, 0x06, 0x20, 0x01, 0x28, 0x08, 0x52,
	0x0e, 0x68, 0x61, 0x73, 0x43, 0x72, 0x65, 0x64, 0x69, 0x74, 0x46, 0x69, 0x72, 0x73, 0x74, 0x12,
	0x1f, 0x0a, 0x0b, 0x6d, 0x65, 0x72, 0x63, 0x68, 0x61, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x07,
	0x20, 0x01, 0x28, 0x04, 0x52, 0x0a, 0x6d, 0x65, 0x72, 0x63, 0x68, 0x61, 0x6e, 0x74, 0x49, 0x64,
	0x12, 0x1d, 0x0a, 0x0a, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x08,
	0x20, 0x01, 0x28, 0x04, 0x52, 0x09, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x49, 0x64, 0x12,
	0x10, 0x0a, 0x03, 0x62, 0x69, 0x6e, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x62, 0x69,
	0x6e, 0x12, 0x20, 0x0a, 0x0c, 0x65, 0x61, 0x72, 0x6e, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x5f, 0x69,
	0x64, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x04, 0x52, 0x0a, 0x65, 0x61, 0x72, 0x6e, 0x54, 0x79, 0x70,
	0x65, 0x49, 0x64, 0x12, 0x23, 0x0a, 0x0d, 0x69, 0x73, 0x5f, 0x73, 0x70, 0x6c, 0x69, 0x74, 0x74,
	0x61, 0x62, 0x6c, 0x65, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0c, 0x69, 0x73, 0x53, 0x70,
	0x6c, 0x69, 0x74, 0x74, 0x61, 0x62, 0x6c, 0x65, 0x12, 0x24, 0x0a, 0x0e, 0x65, 0x6e, 0x74, 0x69,
	0x74, 0x79, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x04,
	0x52, 0x0c, 0x65, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x54, 0x79, 0x70, 0x65, 0x49, 0x64, 0x12, 0x26,
	0x0a, 0x0f, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x72, 0x79, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x5f, 0x69,
	0x64, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x04, 0x52, 0x0d, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x72, 0x79,
	0x43, 0x6f, 0x64, 0x65, 0x49, 0x64, 0x22, 0xb8, 0x01, 0x0a, 0x16, 0x53, 0x65, 0x74, 0x49, 0x6e,
	0x54, 0x72, 0x61, 0x6e, 0x73, 0x66, 0x65, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x56,
	0x31, 0x12, 0x1f, 0x0a, 0x0b, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x66, 0x65, 0x72, 0x5f, 0x69, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x0a, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x66, 0x65, 0x72,
	0x49, 0x64, 0x12, 0x1f, 0x0a, 0x0b, 0x6d, 0x65, 0x72, 0x63, 0x68, 0x61, 0x6e, 0x74, 0x5f, 0x69,
	0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x04, 0x52, 0x0a, 0x6d, 0x65, 0x72, 0x63, 0x68, 0x61, 0x6e,
	0x74, 0x49, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x5f, 0x69,
	0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x04, 0x52, 0x09, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74,
	0x49, 0x64, 0x12, 0x25, 0x0a, 0x0e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x6e, 0x75,
	0x6d, 0x62, 0x65, 0x72, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x61, 0x63, 0x63, 0x6f,
	0x75, 0x6e, 0x74, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x12, 0x16, 0x0a, 0x06, 0x61, 0x6d, 0x6f,
	0x75, 0x6e, 0x74, 0x18, 0x05, 0x20, 0x01, 0x28, 0x01, 0x52, 0x06, 0x61, 0x6d, 0x6f, 0x75, 0x6e,
	0x74, 0x22, 0xe0, 0x02, 0x0a, 0x18, 0x53, 0x70, 0x6c, 0x69, 0x74, 0x54, 0x72, 0x61, 0x6e, 0x73,
	0x66, 0x65, 0x72, 0x4f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x56, 0x31, 0x12, 0x1f,
	0x0a, 0x0b, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x66, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x04, 0x52, 0x0a, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x66, 0x65, 0x72, 0x49, 0x64, 0x12,
	0x2c, 0x0a, 0x12, 0x73, 0x70, 0x6c, 0x69, 0x74, 0x5f, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x04, 0x52, 0x10, 0x73, 0x70, 0x6c,
	0x69, 0x74, 0x4f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x12, 0x1f, 0x0a,
	0x0b, 0x6d, 0x65, 0x72, 0x63, 0x68, 0x61, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x04, 0x52, 0x0a, 0x6d, 0x65, 0x72, 0x63, 0x68, 0x61, 0x6e, 0x74, 0x49, 0x64, 0x12, 0x1d,
	0x0a, 0x0a, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x04, 0x52, 0x09, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x49, 0x64, 0x12, 0x25, 0x0a,
	0x0e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x18,
	0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x4e, 0x75,
	0x6d, 0x62, 0x65, 0x72, 0x12, 0x16, 0x0a, 0x06, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x06,
	0x20, 0x01, 0x28, 0x01, 0x52, 0x06, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x1d, 0x0a, 0x0a,
	0x73, 0x79, 0x73, 0x74, 0x65, 0x6d, 0x5f, 0x74, 0x61, 0x78, 0x18, 0x07, 0x20, 0x01, 0x28, 0x01,
	0x52, 0x09, 0x73, 0x79, 0x73, 0x74, 0x65, 0x6d, 0x54, 0x61, 0x78, 0x12, 0x57, 0x0a, 0x0f, 0x63,
	0x6f, 0x6d, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x5f, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x08,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x2e, 0x2e, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e,
	0x67, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x2e, 0x63, 0x6f, 0x6d,
	0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x2e, 0x43, 0x6f, 0x6d, 0x6d, 0x69, 0x73, 0x73, 0x69,
	0x6f, 0x6e, 0x56, 0x31, 0x52, 0x0e, 0x63, 0x6f, 0x6d, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e,
	0x4c, 0x69, 0x73, 0x74, 0x22, 0x72, 0x0a, 0x1a, 0x42, 0x69, 0x6c, 0x6c, 0x53, 0x70, 0x6c, 0x69,
	0x74, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x66, 0x65, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x56, 0x31, 0x12, 0x54, 0x0a, 0x0a, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73,
	0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x34, 0x2e, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73,
	0x69, 0x6e, 0x67, 0x2e, 0x62, 0x69, 0x6c, 0x6c, 0x69, 0x6e, 0x67, 0x2e, 0x62, 0x69, 0x6c, 0x6c,
	0x69, 0x6e, 0x67, 0x2e, 0x53, 0x70, 0x6c, 0x69, 0x74, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x66, 0x65,
	0x72, 0x4f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x56, 0x31, 0x52, 0x0a, 0x6f, 0x70,
	0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x22, 0x48, 0x0a, 0x1c, 0x47, 0x65, 0x74, 0x42,
	0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65, 0x4f, 0x77, 0x6e, 0x65, 0x72, 0x42, 0x79, 0x49, 0x44, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x56, 0x31, 0x12, 0x28, 0x0a, 0x10, 0x62, 0x61, 0x6c, 0x61,
	0x6e, 0x63, 0x65, 0x5f, 0x6f, 0x77, 0x6e, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x04, 0x52, 0x0e, 0x62, 0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65, 0x4f, 0x77, 0x6e, 0x65, 0x72,
	0x49, 0x64, 0x22, 0x2c, 0x0a, 0x1a, 0x47, 0x65, 0x74, 0x45, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x54,
	0x79, 0x70, 0x65, 0x42, 0x79, 0x49, 0x44, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x56, 0x31,
	0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x02, 0x69, 0x64,
	0x22, 0x51, 0x0a, 0x17, 0x47, 0x65, 0x74, 0x45, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x54, 0x79, 0x70,
	0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x56, 0x31, 0x12, 0x0e, 0x0a, 0x02, 0x69,
	0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x02, 0x69, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x63,
	0x6f, 0x64, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x12,
	0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e,
	0x61, 0x6d, 0x65, 0x22, 0x2d, 0x0a, 0x1b, 0x47, 0x65, 0x74, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x72,
	0x79, 0x43, 0x6f, 0x64, 0x65, 0x42, 0x79, 0x49, 0x44, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x56, 0x31, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x02,
	0x69, 0x64, 0x22, 0x8d, 0x01, 0x0a, 0x18, 0x47, 0x65, 0x74, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x72,
	0x79, 0x43, 0x6f, 0x64, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x56, 0x31, 0x12,
	0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x02, 0x69, 0x64, 0x12,
	0x12, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x63,
	0x6f, 0x64, 0x65, 0x12, 0x19, 0x0a, 0x08, 0x6b, 0x61, 0x7a, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6b, 0x61, 0x7a, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x19,
	0x0a, 0x08, 0x65, 0x6e, 0x67, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x07, 0x65, 0x6e, 0x67, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x17, 0x0a, 0x07, 0x72, 0x75, 0x5f,
	0x6e, 0x61, 0x6d, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x72, 0x75, 0x4e, 0x61,
	0x6d, 0x65, 0x22, 0x29, 0x0a, 0x17, 0x47, 0x65, 0x74, 0x42, 0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65,
	0x42, 0x79, 0x49, 0x44, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x56, 0x31, 0x12, 0x0e, 0x0a,
	0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x02, 0x69, 0x64, 0x22, 0xf4, 0x01,
	0x0a, 0x14, 0x47, 0x65, 0x74, 0x42, 0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x56, 0x31, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x04, 0x52, 0x02, 0x69, 0x64, 0x12, 0x19, 0x0a, 0x08, 0x6f, 0x77, 0x6e, 0x65, 0x72, 0x5f,
	0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x04, 0x52, 0x07, 0x6f, 0x77, 0x6e, 0x65, 0x72, 0x49,
	0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x04, 0x52, 0x09, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x49, 0x64,
	0x12, 0x16, 0x0a, 0x06, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x01,
	0x52, 0x06, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x40, 0x0a, 0x0e, 0x6c, 0x61, 0x73, 0x74,
	0x5f, 0x63, 0x61, 0x6c, 0x63, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62,
	0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x0c, 0x6c, 0x61,
	0x73, 0x74, 0x43, 0x61, 0x6c, 0x63, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x1f, 0x0a, 0x0b, 0x68, 0x6f,
	0x6c, 0x64, 0x5f, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x06, 0x20, 0x01, 0x28, 0x01, 0x52,
	0x0a, 0x68, 0x6f, 0x6c, 0x64, 0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x17, 0x0a, 0x07, 0x74,
	0x79, 0x70, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x07, 0x20, 0x01, 0x28, 0x04, 0x52, 0x06, 0x74, 0x79,
	0x70, 0x65, 0x49, 0x64, 0x22, 0x81, 0x01, 0x0a, 0x18, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x48, 0x61,
	0x73, 0x42, 0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x56,
	0x31, 0x12, 0x1d, 0x0a, 0x0a, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x5f, 0x69, 0x64, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x09, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x49, 0x64,
	0x12, 0x1f, 0x0a, 0x0b, 0x6d, 0x65, 0x72, 0x63, 0x68, 0x61, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x04, 0x52, 0x0a, 0x6d, 0x65, 0x72, 0x63, 0x68, 0x61, 0x6e, 0x74, 0x49,
	0x64, 0x12, 0x25, 0x0a, 0x0e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x6e, 0x75, 0x6d,
	0x62, 0x65, 0x72, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x61, 0x63, 0x63, 0x6f, 0x75,
	0x6e, 0x74, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x22, 0x48, 0x0a, 0x1e, 0x42, 0x69, 0x6c, 0x6c,
	0x69, 0x6e, 0x67, 0x4f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x42, 0x61, 0x6c, 0x61,
	0x6e, 0x63, 0x65, 0x54, 0x79, 0x70, 0x65, 0x52, 0x65, 0x66, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x6f,
	0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x12, 0x12,
	0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61,
	0x6d, 0x65, 0x22, 0x41, 0x0a, 0x17, 0x42, 0x69, 0x6c, 0x6c, 0x69, 0x6e, 0x67, 0x4f, 0x70, 0x65,
	0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x52, 0x65, 0x66, 0x12, 0x12, 0x0a,
	0x04, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x63, 0x6f, 0x64,
	0x65, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x04, 0x6e, 0x61, 0x6d, 0x65, 0x22, 0x43, 0x0a, 0x19, 0x42, 0x69, 0x6c, 0x6c, 0x69, 0x6e, 0x67,
	0x4f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52,
	0x65, 0x66, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x22, 0x46, 0x0a, 0x1c, 0x42, 0x69,
	0x6c, 0x6c, 0x69, 0x6e, 0x67, 0x4f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x79,
	0x70, 0x65, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x52, 0x65, 0x66, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x6f,
	0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x12, 0x12,
	0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61,
	0x6d, 0x65, 0x22, 0x3c, 0x0a, 0x12, 0x42, 0x69, 0x6c, 0x6c, 0x69, 0x6e, 0x67, 0x45, 0x61, 0x72,
	0x6e, 0x54, 0x79, 0x70, 0x65, 0x52, 0x65, 0x66, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x12, 0x12, 0x0a, 0x04,
	0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65,
	0x22, 0x44, 0x0a, 0x1a, 0x42, 0x69, 0x6c, 0x6c, 0x69, 0x6e, 0x67, 0x42, 0x61, 0x6c, 0x61, 0x6e,
	0x63, 0x65, 0x4f, 0x77, 0x6e, 0x65, 0x72, 0x54, 0x79, 0x70, 0x65, 0x52, 0x65, 0x66, 0x12, 0x12,
	0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x63, 0x6f,
	0x64, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x22, 0x46, 0x0a, 0x1c, 0x42, 0x69, 0x6c, 0x6c, 0x69, 0x6e,
	0x67, 0x42, 0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65, 0x4f, 0x77, 0x6e, 0x65, 0x72, 0x53, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x52, 0x65, 0x66, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61,
	0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x22, 0x47,
	0x0a, 0x1d, 0x42, 0x69, 0x6c, 0x6c, 0x69, 0x6e, 0x67, 0x42, 0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65,
	0x43, 0x72, 0x65, 0x64, 0x69, 0x74, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65, 0x66, 0x12,
	0x12, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x63,
	0x6f, 0x64, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x22, 0x48, 0x0a, 0x1e, 0x42, 0x69, 0x6c, 0x6c, 0x69,
	0x6e, 0x67, 0x42, 0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74,
	0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65, 0x66, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x6f, 0x64,
	0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x12, 0x12, 0x0a,
	0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d,
	0x65, 0x22, 0x41, 0x0a, 0x17, 0x42, 0x69, 0x6c, 0x6c, 0x69, 0x6e, 0x67, 0x4f, 0x70, 0x65, 0x72,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x4d, 0x6f, 0x64, 0x65, 0x52, 0x65, 0x66, 0x12, 0x12, 0x0a, 0x04,
	0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65,
	0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04,
	0x6e, 0x61, 0x6d, 0x65, 0x22, 0xc1, 0x02, 0x0a, 0x24, 0x42, 0x69, 0x6c, 0x6c, 0x69, 0x6e, 0x67,
	0x4f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x47, 0x72, 0x6f,
	0x75, 0x70, 0x52, 0x65, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x66, 0x12, 0x57, 0x0a,
	0x0e, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x30, 0x2e, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69,
	0x6e, 0x67, 0x2e, 0x62, 0x69, 0x6c, 0x6c, 0x69, 0x6e, 0x67, 0x2e, 0x62, 0x69, 0x6c, 0x6c, 0x69,
	0x6e, 0x67, 0x2e, 0x42, 0x69, 0x6c, 0x6c, 0x69, 0x6e, 0x67, 0x4f, 0x70, 0x65, 0x72, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0d, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x12, 0x67, 0x0a, 0x14, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x5f, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x0e, 0x32, 0x35, 0x2e, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e,
	0x67, 0x2e, 0x62, 0x69, 0x6c, 0x6c, 0x69, 0x6e, 0x67, 0x2e, 0x62, 0x69, 0x6c, 0x6c, 0x69, 0x6e,
	0x67, 0x2e, 0x42, 0x69, 0x6c, 0x6c, 0x69, 0x6e, 0x67, 0x4f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x52, 0x12, 0x6f, 0x70, 0x65,
	0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x12,
	0x57, 0x0a, 0x0e, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x6d, 0x6f, 0x64,
	0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x30, 0x2e, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73,
	0x73, 0x69, 0x6e, 0x67, 0x2e, 0x62, 0x69, 0x6c, 0x6c, 0x69, 0x6e, 0x67, 0x2e, 0x62, 0x69, 0x6c,
	0x6c, 0x69, 0x6e, 0x67, 0x2e, 0x42, 0x69, 0x6c, 0x6c, 0x69, 0x6e, 0x67, 0x4f, 0x70, 0x65, 0x72,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x4d, 0x6f, 0x64, 0x65, 0x52, 0x0d, 0x6f, 0x70, 0x65, 0x72, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x4d, 0x6f, 0x64, 0x65, 0x2a, 0x95, 0x03, 0x0a, 0x1b, 0x42, 0x69, 0x6c,
	0x6c, 0x69, 0x6e, 0x67, 0x4f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x42, 0x61, 0x6c,
	0x61, 0x6e, 0x63, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x55, 0x0a, 0x22, 0x42, 0x69, 0x6c, 0x6c,
	0x69, 0x6e, 0x67, 0x4f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x42, 0x61, 0x6c, 0x61,
	0x6e, 0x63, 0x65, 0x54, 0x79, 0x70, 0x65, 0x55, 0x6e, 0x6b, 0x6e, 0x6f, 0x77, 0x6e, 0x10, 0x00,
	0x1a, 0x2d, 0xa2, 0xf0, 0x30, 0x29, 0x0a, 0x07, 0x75, 0x6e, 0x6b, 0x6e, 0x6f, 0x77, 0x6e, 0x12,
	0x1e, 0x75, 0x6e, 0x6b, 0x6e, 0x6f, 0x77, 0x6e, 0x20, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x20, 0x62, 0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65, 0x20, 0x74, 0x79, 0x70, 0x65, 0x12,
	0x4e, 0x0a, 0x1f, 0x42, 0x69, 0x6c, 0x6c, 0x69, 0x6e, 0x67, 0x4f, 0x70, 0x65, 0x72, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x42, 0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65, 0x54, 0x79, 0x70, 0x65, 0x4d, 0x61,
	0x69, 0x6e, 0x10, 0x01, 0x1a, 0x29, 0xa2, 0xf0, 0x30, 0x25, 0x0a, 0x04, 0x6d, 0x61, 0x69, 0x6e,
	0x12, 0x1d, 0xd0, 0x9e, 0xd1, 0x81, 0xd0, 0xbd, 0xd0, 0xbe, 0xd0, 0xb2, 0xd0, 0xbd, 0xd0, 0xbe,
	0xd0, 0xb9, 0x20, 0xd0, 0xb1, 0xd0, 0xb0, 0xd0, 0xbb, 0xd0, 0xb0, 0xd0, 0xbd, 0xd1, 0x81, 0x12,
	0x54, 0x0a, 0x21, 0x42, 0x69, 0x6c, 0x6c, 0x69, 0x6e, 0x67, 0x4f, 0x70, 0x65, 0x72, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x42, 0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65, 0x54, 0x79, 0x70, 0x65, 0x43, 0x72,
	0x65, 0x64, 0x69, 0x74, 0x10, 0x02, 0x1a, 0x2d, 0xa2, 0xf0, 0x30, 0x29, 0x0a, 0x06, 0x63, 0x72,
	0x65, 0x64, 0x69, 0x74, 0x12, 0x1f, 0xd0, 0x9a, 0xd1, 0x80, 0xd0, 0xb5, 0xd0, 0xb4, 0xd0, 0xb8,
	0xd1, 0x82, 0xd0, 0xbd, 0xd1, 0x8b, 0xd0, 0xb9, 0x20, 0xd0, 0xb1, 0xd0, 0xb0, 0xd0, 0xbb, 0xd0,
	0xb0, 0xd0, 0xbd, 0xd1, 0x81, 0x1a, 0x79, 0xaa, 0xf0, 0x30, 0x29, 0x0a, 0x07, 0x75, 0x6e, 0x6b,
	0x6e, 0x6f, 0x77, 0x6e, 0x12, 0x1e, 0x75, 0x6e, 0x6b, 0x6e, 0x6f, 0x77, 0x6e, 0x20, 0x6f, 0x70,
	0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x20, 0x62, 0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65, 0x20,
	0x74, 0x79, 0x70, 0x65, 0xaa, 0x82, 0xec, 0x8e, 0x02, 0x24, 0x64, 0x65, 0x66, 0x61, 0x75, 0x6c,
	0x74, 0x5f, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x62, 0x61, 0x6c, 0x61,
	0x6e, 0x63, 0x65, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x5f, 0x76, 0x61, 0x6c, 0x75, 0x65, 0xb2, 0x82,
	0xec, 0x8e, 0x02, 0x1c, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x62, 0x61,
	0x6c, 0x61, 0x6e, 0x63, 0x65, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x5f, 0x76, 0x61, 0x6c, 0x75, 0x65,
	0x2a, 0xf5, 0x17, 0x0a, 0x14, 0x42, 0x69, 0x6c, 0x6c, 0x69, 0x6e, 0x67, 0x4f, 0x70, 0x65, 0x72,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x12, 0x46, 0x0a, 0x1b, 0x42, 0x69, 0x6c,
	0x6c, 0x69, 0x6e, 0x67, 0x4f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70,
	0x65, 0x55, 0x6e, 0x6b, 0x6e, 0x6f, 0x77, 0x6e, 0x10, 0x00, 0x1a, 0x25, 0xb2, 0xf0, 0x30, 0x21,
	0x0a, 0x07, 0x75, 0x6e, 0x6b, 0x6e, 0x6f, 0x77, 0x6e, 0x12, 0x16, 0x75, 0x6e, 0x6b, 0x6e, 0x6f,
	0x77, 0x6e, 0x20, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x20, 0x74, 0x79, 0x70,
	0x65, 0x12, 0x37, 0x0a, 0x19, 0x42, 0x69, 0x6c, 0x6c, 0x69, 0x6e, 0x67, 0x4f, 0x70, 0x65, 0x72,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x50, 0x61, 0x79, 0x49, 0x6e, 0x10, 0x01,
	0x1a, 0x18, 0xb2, 0xf0, 0x30, 0x14, 0x0a, 0x06, 0x70, 0x61, 0x79, 0x5f, 0x69, 0x6e, 0x12, 0x0a,
	0xd0, 0x9f, 0xd1, 0x80, 0xd0, 0xb8, 0xd0, 0xb5, 0xd0, 0xbc, 0x12, 0x39, 0x0a, 0x1a, 0x42, 0x69,
	0x6c, 0x6c, 0x69, 0x6e, 0x67, 0x4f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x79,
	0x70, 0x65, 0x50, 0x61, 0x79, 0x4f, 0x75, 0x74, 0x10, 0x02, 0x1a, 0x19, 0xb2, 0xf0, 0x30, 0x15,
	0x0a, 0x07, 0x70, 0x61, 0x79, 0x5f, 0x6f, 0x75, 0x74, 0x12, 0x0a, 0xd0, 0x92, 0xd1, 0x8b, 0xd0,
	0xb2, 0xd0, 0xbe, 0xd0, 0xb4, 0x12, 0x3c, 0x0a, 0x1a, 0x42, 0x69, 0x6c, 0x6c, 0x69, 0x6e, 0x67,
	0x4f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x52, 0x65, 0x66,
	0x75, 0x6e, 0x64, 0x10, 0x03, 0x1a, 0x1c, 0xb2, 0xf0, 0x30, 0x18, 0x0a, 0x06, 0x72, 0x65, 0x66,
	0x75, 0x6e, 0x64, 0x12, 0x0e, 0xd0, 0x92, 0xd0, 0xbe, 0xd0, 0xb7, 0xd0, 0xb2, 0xd1, 0x80, 0xd0,
	0xb0, 0xd1, 0x82, 0x12, 0x5a, 0x0a, 0x1e, 0x42, 0x69, 0x6c, 0x6c, 0x69, 0x6e, 0x67, 0x4f, 0x70,
	0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x49, 0x6e, 0x54, 0x72, 0x61,
	0x6e, 0x73, 0x66, 0x65, 0x72, 0x10, 0x04, 0x1a, 0x36, 0xb2, 0xf0, 0x30, 0x32, 0x0a, 0x0b, 0x69,
	0x6e, 0x5f, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x66, 0x65, 0x72, 0x12, 0x23, 0xd0, 0xa4, 0xd0, 0xb8,
	0xd0, 0xbd, 0x2e, 0x20, 0xd0, 0xbe, 0xd0, 0xbf, 0xd0, 0xb5, 0xd1, 0x80, 0xd0, 0xb0, 0xd1, 0x86,
	0xd0, 0xb8, 0xd1, 0x8f, 0x20, 0xd0, 0xbf, 0xd1, 0x80, 0xd0, 0xb8, 0xd0, 0xb5, 0xd0, 0xbc, 0x12,
	0x5c, 0x0a, 0x1f, 0x42, 0x69, 0x6c, 0x6c, 0x69, 0x6e, 0x67, 0x4f, 0x70, 0x65, 0x72, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x4f, 0x75, 0x74, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x66,
	0x65, 0x72, 0x10, 0x05, 0x1a, 0x37, 0xb2, 0xf0, 0x30, 0x33, 0x0a, 0x0c, 0x6f, 0x75, 0x74, 0x5f,
	0x74, 0x72, 0x61, 0x6e, 0x73, 0x66, 0x65, 0x72, 0x12, 0x23, 0xd0, 0xa4, 0xd0, 0xb8, 0xd0, 0xbd,
	0x2e, 0x20, 0xd0, 0xbe, 0xd0, 0xbf, 0xd0, 0xb5, 0xd1, 0x80, 0xd0, 0xb0, 0xd1, 0x86, 0xd0, 0xb8,
	0xd1, 0x8f, 0x20, 0xd0, 0xb2, 0xd1, 0x8b, 0xd0, 0xb2, 0xd0, 0xbe, 0xd0, 0xb4, 0x12, 0x8f, 0x01,
	0x0a, 0x2e, 0x42, 0x69, 0x6c, 0x6c, 0x69, 0x6e, 0x67, 0x4f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x50, 0x61, 0x79, 0x49, 0x6e, 0x43, 0x6f, 0x6d, 0x6d, 0x69,
	0x73, 0x73, 0x69, 0x6f, 0x6e, 0x4f, 0x75, 0x74, 0x41, 0x63, 0x71, 0x75, 0x69, 0x72, 0x65, 0x72,
	0x10, 0x06, 0x1a, 0x5b, 0xb2, 0xf0, 0x30, 0x57, 0x0a, 0x12, 0x70, 0x61, 0x79, 0x5f, 0x69, 0x6e,
	0x5f, 0x63, 0x6d, 0x73, 0x5f, 0x6f, 0x75, 0x74, 0x5f, 0x61, 0x63, 0x72, 0x12, 0x41, 0xd0, 0xa3,
	0xd0, 0xb4, 0xd0, 0xb5, 0xd1, 0x80, 0xd0, 0xb6, 0xd0, 0xb0, 0xd0, 0xbd, 0xd0, 0xb8, 0xd0, 0xb5,
	0x20, 0xd0, 0xba, 0xd0, 0xbe, 0xd0, 0xbc, 0xd0, 0xb8, 0xd1, 0x81, 0xd1, 0x81, 0xd0, 0xb8, 0xd0,
	0xb8, 0x20, 0xd1, 0x8d, 0xd0, 0xba, 0xd0, 0xb2, 0xd0, 0xb0, 0xd0, 0xb9, 0xd0, 0xb5, 0xd1, 0x80,
	0xd0, 0xb0, 0x20, 0x28, 0xd0, 0xbf, 0xd1, 0x80, 0xd0, 0xb8, 0xd0, 0xb5, 0xd0, 0xbc, 0x29, 0x12,
	0x89, 0x01, 0x0a, 0x2b, 0x42, 0x69, 0x6c, 0x6c, 0x69, 0x6e, 0x67, 0x4f, 0x70, 0x65, 0x72, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x50, 0x61, 0x79, 0x49, 0x6e, 0x43, 0x6f, 0x6d,
	0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x49, 0x6e, 0x53, 0x79, 0x73, 0x74, 0x65, 0x6d, 0x10,
	0x07, 0x1a, 0x58, 0xb2, 0xf0, 0x30, 0x54, 0x0a, 0x11, 0x70, 0x61, 0x79, 0x5f, 0x69, 0x6e, 0x5f,
	0x63, 0x6d, 0x73, 0x5f, 0x69, 0x6e, 0x5f, 0x73, 0x79, 0x73, 0x12, 0x3f, 0xd0, 0x9f, 0xd0, 0xbe,
	0xd0, 0xbf, 0xd0, 0xbe, 0xd0, 0xbb, 0xd0, 0xbd, 0xd0, 0xb5, 0xd0, 0xbd, 0xd0, 0xb8, 0xd0, 0xb5,
	0x20, 0xd0, 0xba, 0xd0, 0xbe, 0xd0, 0xbc, 0xd0, 0xb8, 0xd1, 0x81, 0xd1, 0x81, 0xd0, 0xb8, 0xd0,
	0xb8, 0x20, 0xd0, 0xa2, 0xd0, 0xb0, 0xd1, 0x80, 0xd0, 0xbb, 0xd0, 0xb0, 0xd0, 0xbd, 0x20, 0x28,
	0xd0, 0xbf, 0xd1, 0x80, 0xd0, 0xb8, 0xd0, 0xb5, 0xd0, 0xbc, 0x29, 0x12, 0x89, 0x01, 0x0a, 0x2c,
	0x42, 0x69, 0x6c, 0x6c, 0x69, 0x6e, 0x67, 0x4f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x54, 0x79, 0x70, 0x65, 0x50, 0x61, 0x79, 0x49, 0x6e, 0x43, 0x6f, 0x6d, 0x6d, 0x69, 0x73, 0x73,
	0x69, 0x6f, 0x6e, 0x4f, 0x75, 0x74, 0x53, 0x79, 0x73, 0x74, 0x65, 0x6d, 0x10, 0x08, 0x1a, 0x57,
	0xb2, 0xf0, 0x30, 0x53, 0x0a, 0x12, 0x70, 0x61, 0x79, 0x5f, 0x69, 0x6e, 0x5f, 0x63, 0x6d, 0x73,
	0x5f, 0x6f, 0x75, 0x74, 0x5f, 0x73, 0x79, 0x73, 0x12, 0x3d, 0xd0, 0xa3, 0xd0, 0xb4, 0xd0, 0xb5,
	0xd1, 0x80, 0xd0, 0xb6, 0xd0, 0xb0, 0xd0, 0xbd, 0xd0, 0xb8, 0xd0, 0xb5, 0x20, 0xd0, 0xba, 0xd0,
	0xbe, 0xd0, 0xbc, 0xd0, 0xb8, 0xd1, 0x81, 0xd1, 0x81, 0xd0, 0xb8, 0xd0, 0xb8, 0x20, 0xd0, 0xa2,
	0xd0, 0xb0, 0xd1, 0x80, 0xd0, 0xbb, 0xd0, 0xb0, 0xd0, 0xbd, 0x20, 0x28, 0xd0, 0xbf, 0xd1, 0x80,
	0xd0, 0xb8, 0xd0, 0xb5, 0xd0, 0xbc, 0x29, 0x12, 0x8f, 0x01, 0x0a, 0x2d, 0x42, 0x69, 0x6c, 0x6c,
	0x69, 0x6e, 0x67, 0x4f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65,
	0x50, 0x61, 0x79, 0x49, 0x6e, 0x43, 0x6f, 0x6d, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x49,
	0x6e, 0x4d, 0x65, 0x72, 0x63, 0x68, 0x61, 0x6e, 0x74, 0x10, 0x09, 0x1a, 0x5c, 0xb2, 0xf0, 0x30,
	0x58, 0x0a, 0x11, 0x70, 0x61, 0x79, 0x5f, 0x69, 0x6e, 0x5f, 0x63, 0x6d, 0x73, 0x5f, 0x69, 0x6e,
	0x5f, 0x6d, 0x72, 0x63, 0x12, 0x43, 0xd0, 0x9f, 0xd0, 0xbe, 0xd0, 0xbf, 0xd0, 0xbe, 0xd0, 0xbb,
	0xd0, 0xbd, 0xd0, 0xb5, 0xd0, 0xbd, 0xd0, 0xb8, 0xd0, 0xb5, 0x20, 0xd0, 0xba, 0xd0, 0xbe, 0xd0,
	0xbc, 0xd0, 0xb8, 0xd1, 0x81, 0xd1, 0x81, 0xd0, 0xb8, 0xd0, 0xb8, 0x20, 0xd0, 0xbc, 0xd0, 0xb5,
	0xd1, 0x80, 0xd1, 0x87, 0xd0, 0xb0, 0xd0, 0xbd, 0xd1, 0x82, 0xd0, 0xb0, 0x20, 0x28, 0xd0, 0xbf,
	0xd1, 0x80, 0xd0, 0xb8, 0xd0, 0xb5, 0xd0, 0xbc, 0x29, 0x12, 0x8f, 0x01, 0x0a, 0x2e, 0x42, 0x69,
	0x6c, 0x6c, 0x69, 0x6e, 0x67, 0x4f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x79,
	0x70, 0x65, 0x50, 0x61, 0x79, 0x49, 0x6e, 0x43, 0x6f, 0x6d, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f,
	0x6e, 0x4f, 0x75, 0x74, 0x4d, 0x65, 0x72, 0x63, 0x68, 0x61, 0x6e, 0x74, 0x10, 0x0a, 0x1a, 0x5b,
	0xb2, 0xf0, 0x30, 0x57, 0x0a, 0x12, 0x70, 0x61, 0x79, 0x5f, 0x69, 0x6e, 0x5f, 0x63, 0x6d, 0x73,
	0x5f, 0x6f, 0x75, 0x74, 0x5f, 0x6d, 0x72, 0x63, 0x12, 0x41, 0xd0, 0xa3, 0xd0, 0xb4, 0xd0, 0xb5,
	0xd1, 0x80, 0xd0, 0xb6, 0xd0, 0xb0, 0xd0, 0xbd, 0xd0, 0xb8, 0xd0, 0xb5, 0x20, 0xd0, 0xba, 0xd0,
	0xbe, 0xd0, 0xbc, 0xd0, 0xb8, 0xd1, 0x81, 0xd1, 0x81, 0xd0, 0xb8, 0xd0, 0xb8, 0x20, 0xd0, 0xbc,
	0xd0, 0xb5, 0xd1, 0x80, 0xd1, 0x87, 0xd0, 0xb0, 0xd0, 0xbd, 0xd1, 0x82, 0xd0, 0xb0, 0x20, 0x28,
	0xd0, 0xbf, 0xd1, 0x80, 0xd0, 0xb8, 0xd0, 0xb5, 0xd0, 0xbc, 0x29, 0x12, 0x67, 0x0a, 0x26, 0x42,
	0x69, 0x6c, 0x6c, 0x69, 0x6e, 0x67, 0x4f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x54,
	0x79, 0x70, 0x65, 0x49, 0x6e, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x49, 0x6e, 0x54, 0x72, 0x61,
	0x6e, 0x73, 0x66, 0x65, 0x72, 0x10, 0x0b, 0x1a, 0x3b, 0xb2, 0xf0, 0x30, 0x37, 0x0a, 0x14, 0x69,
	0x6e, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x5f, 0x69, 0x6e, 0x5f, 0x74, 0x72, 0x61, 0x6e, 0x73,
	0x66, 0x65, 0x72, 0x12, 0x1f, 0xd0, 0x92, 0xd0, 0xbd, 0xd1, 0x83, 0xd1, 0x82, 0xd1, 0x80, 0xd0,
	0xb5, 0xd0, 0xbd, 0xd0, 0xbd, 0xd0, 0xb8, 0xd0, 0xb9, 0x20, 0xd0, 0xbf, 0xd1, 0x80, 0xd0, 0xb8,
	0xd0, 0xb5, 0xd0, 0xbc, 0x12, 0x69, 0x0a, 0x27, 0x42, 0x69, 0x6c, 0x6c, 0x69, 0x6e, 0x67, 0x4f,
	0x70, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x49, 0x6e, 0x74, 0x65,
	0x72, 0x6e, 0x61, 0x6c, 0x4f, 0x75, 0x74, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x66, 0x65, 0x72, 0x10,
	0x0c, 0x1a, 0x3c, 0xb2, 0xf0, 0x30, 0x38, 0x0a, 0x15, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x6e, 0x61,
	0x6c, 0x5f, 0x6f, 0x75, 0x74, 0x5f, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x66, 0x65, 0x72, 0x12, 0x1f,
	0xd0, 0x92, 0xd0, 0xbd, 0xd1, 0x83, 0xd1, 0x82, 0xd1, 0x80, 0xd0, 0xb5, 0xd0, 0xbd, 0xd0, 0xbd,
	0xd0, 0xb8, 0xd0, 0xb9, 0x20, 0xd0, 0xb2, 0xd1, 0x8b, 0xd0, 0xb2, 0xd0, 0xbe, 0xd0, 0xb4, 0x12,
	0x71, 0x0a, 0x24, 0x42, 0x69, 0x6c, 0x6c, 0x69, 0x6e, 0x67, 0x4f, 0x70, 0x65, 0x72, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x53, 0x70, 0x6c, 0x69, 0x74, 0x4f, 0x75, 0x74, 0x54,
	0x72, 0x61, 0x6e, 0x73, 0x66, 0x65, 0x72, 0x10, 0x0d, 0x1a, 0x47, 0xb2, 0xf0, 0x30, 0x43, 0x0a,
	0x12, 0x73, 0x70, 0x6c, 0x69, 0x74, 0x5f, 0x6f, 0x75, 0x74, 0x5f, 0x74, 0x72, 0x61, 0x6e, 0x73,
	0x66, 0x65, 0x72, 0x12, 0x2d, 0xd0, 0xa3, 0xd0, 0xb4, 0xd0, 0xb5, 0xd1, 0x80, 0xd0, 0xb6, 0xd0,
	0xb0, 0xd0, 0xbd, 0xd0, 0xb8, 0xd0, 0xb5, 0x20, 0xd1, 0x81, 0xd0, 0xbf, 0xd0, 0xbb, 0xd0, 0xb8,
	0xd1, 0x82, 0x2e, 0x20, 0xd1, 0x81, 0xd1, 0x80, 0xd0, 0xb5, 0xd0, 0xb4, 0xd1, 0x81, 0xd1, 0x82,
	0xd0, 0xb2, 0x12, 0x71, 0x0a, 0x23, 0x42, 0x69, 0x6c, 0x6c, 0x69, 0x6e, 0x67, 0x4f, 0x70, 0x65,
	0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x53, 0x70, 0x6c, 0x69, 0x74, 0x49,
	0x6e, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x66, 0x65, 0x72, 0x10, 0x0e, 0x1a, 0x48, 0xb2, 0xf0, 0x30,
	0x44, 0x0a, 0x11, 0x73, 0x70, 0x6c, 0x69, 0x74, 0x5f, 0x69, 0x6e, 0x5f, 0x74, 0x72, 0x61, 0x6e,
	0x73, 0x66, 0x65, 0x72, 0x12, 0x2f, 0xd0, 0x9f, 0xd0, 0xbe, 0xd0, 0xbf, 0xd0, 0xbe, 0xd0, 0xbb,
	0xd0, 0xbd, 0xd0, 0xb5, 0xd0, 0xbd, 0xd0, 0xb8, 0xd0, 0xb5, 0x20, 0xd1, 0x81, 0xd0, 0xbf, 0xd0,
	0xbb, 0xd0, 0xb8, 0xd1, 0x82, 0x2e, 0x20, 0xd1, 0x81, 0xd1, 0x80, 0xd0, 0xb5, 0xd0, 0xb4, 0xd1,
	0x81, 0xd1, 0x82, 0xd0, 0xb2, 0x12, 0x91, 0x01, 0x0a, 0x2f, 0x42, 0x69, 0x6c, 0x6c, 0x69, 0x6e,
	0x67, 0x4f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x50, 0x61,
	0x79, 0x4f, 0x75, 0x74, 0x43, 0x6f, 0x6d, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x4f, 0x75,
	0x74, 0x41, 0x63, 0x71, 0x75, 0x69, 0x72, 0x65, 0x72, 0x10, 0x0f, 0x1a, 0x5c, 0xb2, 0xf0, 0x30,
	0x58, 0x0a, 0x13, 0x70, 0x61, 0x79, 0x5f, 0x6f, 0x75, 0x74, 0x5f, 0x63, 0x6d, 0x73, 0x5f, 0x6f,
	0x75, 0x74, 0x5f, 0x61, 0x63, 0x72, 0x12, 0x41, 0xd0, 0xa3, 0xd0, 0xb4, 0xd0, 0xb5, 0xd1, 0x80,
	0xd0, 0xb6, 0xd0, 0xb0, 0xd0, 0xbd, 0xd0, 0xb8, 0xd0, 0xb5, 0x20, 0xd0, 0xba, 0xd0, 0xbe, 0xd0,
	0xbc, 0xd0, 0xb8, 0xd1, 0x81, 0xd1, 0x81, 0xd0, 0xb8, 0xd0, 0xb8, 0x20, 0xd1, 0x8d, 0xd0, 0xba,
	0xd0, 0xb2, 0xd0, 0xb0, 0xd0, 0xb9, 0xd0, 0xb5, 0xd1, 0x80, 0xd0, 0xb0, 0x20, 0x28, 0xd0, 0xb2,
	0xd1, 0x8b, 0xd0, 0xb2, 0xd0, 0xbe, 0xd0, 0xb4, 0x29, 0x12, 0x8b, 0x01, 0x0a, 0x2c, 0x42, 0x69,
	0x6c, 0x6c, 0x69, 0x6e, 0x67, 0x4f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x79,
	0x70, 0x65, 0x50, 0x61, 0x79, 0x4f, 0x75, 0x74, 0x43, 0x6f, 0x6d, 0x6d, 0x69, 0x73, 0x73, 0x69,
	0x6f, 0x6e, 0x49, 0x6e, 0x53, 0x79, 0x73, 0x74, 0x65, 0x6d, 0x10, 0x10, 0x1a, 0x59, 0xb2, 0xf0,
	0x30, 0x55, 0x0a, 0x12, 0x70, 0x61, 0x79, 0x5f, 0x6f, 0x75, 0x74, 0x5f, 0x63, 0x6d, 0x73, 0x5f,
	0x69, 0x6e, 0x5f, 0x73, 0x79, 0x73, 0x12, 0x3f, 0xd0, 0x9f, 0xd0, 0xbe, 0xd0, 0xbf, 0xd0, 0xbe,
	0xd0, 0xbb, 0xd0, 0xbd, 0xd0, 0xb5, 0xd0, 0xbd, 0xd0, 0xb8, 0xd0, 0xb5, 0x20, 0xd0, 0xba, 0xd0,
	0xbe, 0xd0, 0xbc, 0xd0, 0xb8, 0xd1, 0x81, 0xd1, 0x81, 0xd0, 0xb8, 0xd0, 0xb8, 0x20, 0xd0, 0xa2,
	0xd0, 0xb0, 0xd1, 0x80, 0xd0, 0xbb, 0xd0, 0xb0, 0xd0, 0xbd, 0x20, 0x28, 0xd0, 0xb2, 0xd1, 0x8b,
	0xd0, 0xb2, 0xd0, 0xbe, 0xd0, 0xb4, 0x29, 0x12, 0x8b, 0x01, 0x0a, 0x2d, 0x42, 0x69, 0x6c, 0x6c,
	0x69, 0x6e, 0x67, 0x4f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65,
	0x50, 0x61, 0x79, 0x4f, 0x75, 0x74, 0x43, 0x6f, 0x6d, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e,
	0x4f, 0x75, 0x74, 0x53, 0x79, 0x73, 0x74, 0x65, 0x6d, 0x10, 0x11, 0x1a, 0x58, 0xb2, 0xf0, 0x30,
	0x54, 0x0a, 0x13, 0x70, 0x61, 0x79, 0x5f, 0x6f, 0x75, 0x74, 0x5f, 0x63, 0x6d, 0x73, 0x5f, 0x6f,
	0x75, 0x74, 0x5f, 0x73, 0x79, 0x73, 0x12, 0x3d, 0xd0, 0xa3, 0xd0, 0xb4, 0xd0, 0xb5, 0xd1, 0x80,
	0xd0, 0xb6, 0xd0, 0xb0, 0xd0, 0xbd, 0xd0, 0xb8, 0xd0, 0xb5, 0x20, 0xd0, 0xba, 0xd0, 0xbe, 0xd0,
	0xbc, 0xd0, 0xb8, 0xd1, 0x81, 0xd1, 0x81, 0xd0, 0xb8, 0xd0, 0xb8, 0x20, 0xd0, 0xa2, 0xd0, 0xb0,
	0xd1, 0x80, 0xd0, 0xbb, 0xd0, 0xb0, 0xd0, 0xbd, 0x20, 0x28, 0xd0, 0xb2, 0xd1, 0x8b, 0xd0, 0xb2,
	0xd0, 0xbe, 0xd0, 0xb4, 0x29, 0x12, 0x91, 0x01, 0x0a, 0x2e, 0x42, 0x69, 0x6c, 0x6c, 0x69, 0x6e,
	0x67, 0x4f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x50, 0x61,
	0x79, 0x4f, 0x75, 0x74, 0x43, 0x6f, 0x6d, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x49, 0x6e,
	0x4d, 0x65, 0x72, 0x63, 0x68, 0x61, 0x6e, 0x74, 0x10, 0x12, 0x1a, 0x5d, 0xb2, 0xf0, 0x30, 0x59,
	0x0a, 0x12, 0x70, 0x61, 0x79, 0x5f, 0x6f, 0x75, 0x74, 0x5f, 0x63, 0x6d, 0x73, 0x5f, 0x69, 0x6e,
	0x5f, 0x6d, 0x72, 0x63, 0x12, 0x43, 0xd0, 0x9f, 0xd0, 0xbe, 0xd0, 0xbf, 0xd0, 0xbe, 0xd0, 0xbb,
	0xd0, 0xbd, 0xd0, 0xb5, 0xd0, 0xbd, 0xd0, 0xb8, 0xd0, 0xb5, 0x20, 0xd0, 0xba, 0xd0, 0xbe, 0xd0,
	0xbc, 0xd0, 0xb8, 0xd1, 0x81, 0xd1, 0x81, 0xd0, 0xb8, 0xd0, 0xb8, 0x20, 0xd0, 0xbc, 0xd0, 0xb5,
	0xd1, 0x80, 0xd1, 0x87, 0xd0, 0xb0, 0xd0, 0xbd, 0xd1, 0x82, 0xd0, 0xb0, 0x20, 0x28, 0xd0, 0xb2,
	0xd1, 0x8b, 0xd0, 0xb2, 0xd0, 0xbe, 0xd0, 0xb4, 0x29, 0x12, 0x91, 0x01, 0x0a, 0x2f, 0x42, 0x69,
	0x6c, 0x6c, 0x69, 0x6e, 0x67, 0x4f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x79,
	0x70, 0x65, 0x50, 0x61, 0x79, 0x4f, 0x75, 0x74, 0x43, 0x6f, 0x6d, 0x6d, 0x69, 0x73, 0x73, 0x69,
	0x6f, 0x6e, 0x4f, 0x75, 0x74, 0x4d, 0x65, 0x72, 0x63, 0x68, 0x61, 0x6e, 0x74, 0x10, 0x13, 0x1a,
	0x5c, 0xb2, 0xf0, 0x30, 0x58, 0x0a, 0x13, 0x70, 0x61, 0x79, 0x5f, 0x6f, 0x75, 0x74, 0x5f, 0x63,
	0x6d, 0x73, 0x5f, 0x6f, 0x75, 0x74, 0x5f, 0x6d, 0x72, 0x63, 0x12, 0x41, 0xd0, 0xa3, 0xd0, 0xb4,
	0xd0, 0xb5, 0xd1, 0x80, 0xd0, 0xb6, 0xd0, 0xb0, 0xd0, 0xbd, 0xd0, 0xb8, 0xd0, 0xb5, 0x20, 0xd0,
	0xba, 0xd0, 0xbe, 0xd0, 0xbc, 0xd0, 0xb8, 0xd1, 0x81, 0xd1, 0x81, 0xd0, 0xb8, 0xd0, 0xb8, 0x20,
	0xd0, 0xbc, 0xd0, 0xb5, 0xd1, 0x80, 0xd1, 0x87, 0xd0, 0xb0, 0xd0, 0xbd, 0xd1, 0x82, 0xd0, 0xb0,
	0x20, 0x28, 0xd0, 0xb2, 0xd1, 0x8b, 0xd0, 0xb2, 0xd0, 0xbe, 0xd0, 0xb4, 0x29, 0x12, 0x94, 0x01,
	0x0a, 0x2b, 0x42, 0x69, 0x6c, 0x6c, 0x69, 0x6e, 0x67, 0x4f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x53, 0x70, 0x6c, 0x69, 0x74, 0x43, 0x6f, 0x6d, 0x6d, 0x69,
	0x73, 0x73, 0x69, 0x6f, 0x6e, 0x49, 0x6e, 0x53, 0x79, 0x73, 0x74, 0x65, 0x6d, 0x10, 0x14, 0x1a,
	0x63, 0xb2, 0xf0, 0x30, 0x5f, 0x0a, 0x10, 0x73, 0x70, 0x6c, 0x69, 0x74, 0x5f, 0x63, 0x6d, 0x73,
	0x5f, 0x69, 0x6e, 0x5f, 0x73, 0x79, 0x73, 0x12, 0x4b, 0xd0, 0x9f, 0xd0, 0xbe, 0xd0, 0xbf, 0xd0,
	0xbe, 0xd0, 0xbb, 0xd0, 0xbd, 0xd0, 0xb5, 0xd0, 0xbd, 0xd0, 0xb8, 0xd0, 0xb5, 0x20, 0xd0, 0xba,
	0xd0, 0xbe, 0xd0, 0xbc, 0xd0, 0xb8, 0xd1, 0x81, 0xd1, 0x81, 0xd0, 0xb8, 0xd0, 0xb8, 0x20, 0xd0,
	0xa2, 0xd0, 0xb0, 0xd1, 0x80, 0xd0, 0xbb, 0xd0, 0xb0, 0xd0, 0xbd, 0x20, 0x28, 0xd1, 0x81, 0xd0,
	0xbf, 0xd0, 0xbb, 0xd0, 0xb8, 0xd1, 0x82, 0xd0, 0xbe, 0xd0, 0xb2, 0xd0, 0xb0, 0xd0, 0xbd, 0xd0,
	0xb8, 0xd0, 0xb5, 0x29, 0x12, 0x94, 0x01, 0x0a, 0x2c, 0x42, 0x69, 0x6c, 0x6c, 0x69, 0x6e, 0x67,
	0x4f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x53, 0x70, 0x6c,
	0x69, 0x74, 0x43, 0x6f, 0x6d, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x4f, 0x75, 0x74, 0x53,
	0x79, 0x73, 0x74, 0x65, 0x6d, 0x10, 0x15, 0x1a, 0x62, 0xb2, 0xf0, 0x30, 0x5e, 0x0a, 0x11, 0x73,
	0x70, 0x6c, 0x69, 0x74, 0x5f, 0x63, 0x6d, 0x73, 0x5f, 0x6f, 0x75, 0x74, 0x5f, 0x73, 0x79, 0x73,
	0x12, 0x49, 0xd0, 0xa3, 0xd0, 0xb4, 0xd0, 0xb5, 0xd1, 0x80, 0xd0, 0xb6, 0xd0, 0xb0, 0xd0, 0xbd,
	0xd0, 0xb8, 0xd0, 0xb5, 0x20, 0xd0, 0xba, 0xd0, 0xbe, 0xd0, 0xbc, 0xd0, 0xb8, 0xd1, 0x81, 0xd1,
	0x81, 0xd0, 0xb8, 0xd0, 0xb8, 0x20, 0xd0, 0xa2, 0xd0, 0xb0, 0xd1, 0x80, 0xd0, 0xbb, 0xd0, 0xb0,
	0xd0, 0xbd, 0x20, 0x28, 0xd1, 0x81, 0xd0, 0xbf, 0xd0, 0xbb, 0xd0, 0xb8, 0xd1, 0x82, 0xd0, 0xbe,
	0xd0, 0xb2, 0xd0, 0xb0, 0xd0, 0xbd, 0xd0, 0xb8, 0xd0, 0xb5, 0x29, 0x12, 0x9a, 0x01, 0x0a, 0x2d,
	0x42, 0x69, 0x6c, 0x6c, 0x69, 0x6e, 0x67, 0x4f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x54, 0x79, 0x70, 0x65, 0x53, 0x70, 0x6c, 0x69, 0x74, 0x43, 0x6f, 0x6d, 0x6d, 0x69, 0x73, 0x73,
	0x69, 0x6f, 0x6e, 0x49, 0x6e, 0x4d, 0x65, 0x72, 0x63, 0x68, 0x61, 0x6e, 0x74, 0x10, 0x16, 0x1a,
	0x67, 0xb2, 0xf0, 0x30, 0x63, 0x0a, 0x10, 0x73, 0x70, 0x6c, 0x69, 0x74, 0x5f, 0x63, 0x6d, 0x73,
	0x5f, 0x69, 0x6e, 0x5f, 0x6d, 0x72, 0x63, 0x12, 0x4f, 0xd0, 0x9f, 0xd0, 0xbe, 0xd0, 0xbf, 0xd0,
	0xbe, 0xd0, 0xbb, 0xd0, 0xbd, 0xd0, 0xb5, 0xd0, 0xbd, 0xd0, 0xb8, 0xd0, 0xb5, 0x20, 0xd0, 0xba,
	0xd0, 0xbe, 0xd0, 0xbc, 0xd0, 0xb8, 0xd1, 0x81, 0xd1, 0x81, 0xd0, 0xb8, 0xd0, 0xb8, 0x20, 0xd0,
	0xbc, 0xd0, 0xb5, 0xd1, 0x80, 0xd1, 0x87, 0xd0, 0xb0, 0xd0, 0xbd, 0xd1, 0x82, 0xd0, 0xb0, 0x20,
	0x28, 0xd1, 0x81, 0xd0, 0xbf, 0xd0, 0xbb, 0xd0, 0xb8, 0xd1, 0x82, 0xd0, 0xbe, 0xd0, 0xb2, 0xd0,
	0xb0, 0xd0, 0xbd, 0xd0, 0xb8, 0xd0, 0xb5, 0x29, 0x12, 0x9a, 0x01, 0x0a, 0x2e, 0x42, 0x69, 0x6c,
	0x6c, 0x69, 0x6e, 0x67, 0x4f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70,
	0x65, 0x53, 0x70, 0x6c, 0x69, 0x74, 0x43, 0x6f, 0x6d, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e,
	0x4f, 0x75, 0x74, 0x4d, 0x65, 0x72, 0x63, 0x68, 0x61, 0x6e, 0x74, 0x10, 0x17, 0x1a, 0x66, 0xb2,
	0xf0, 0x30, 0x62, 0x0a, 0x11, 0x73, 0x70, 0x6c, 0x69, 0x74, 0x5f, 0x63, 0x6d, 0x73, 0x5f, 0x6f,
	0x75, 0x74, 0x5f, 0x6d, 0x72, 0x63, 0x12, 0x4d, 0xd0, 0xa3, 0xd0, 0xb4, 0xd0, 0xb5, 0xd1, 0x80,
	0xd0, 0xb6, 0xd0, 0xb0, 0xd0, 0xbd, 0xd0, 0xb8, 0xd0, 0xb5, 0x20, 0xd0, 0xba, 0xd0, 0xbe, 0xd0,
	0xbc, 0xd0, 0xb8, 0xd1, 0x81, 0xd1, 0x81, 0xd0, 0xb8, 0xd0, 0xb8, 0x20, 0xd0, 0xbc, 0xd0, 0xb5,
	0xd1, 0x80, 0xd1, 0x87, 0xd0, 0xb0, 0xd0, 0xbd, 0xd1, 0x82, 0xd0, 0xb0, 0x20, 0x28, 0xd1, 0x81,
	0xd0, 0xbf, 0xd0, 0xbb, 0xd0, 0xb8, 0xd1, 0x82, 0xd0, 0xbe, 0xd0, 0xb2, 0xd0, 0xb0, 0xd0, 0xbd,
	0xd0, 0xb8, 0xd0, 0xb5, 0x29, 0x1a, 0x61, 0xba, 0xf0, 0x30, 0x21, 0x0a, 0x07, 0x75, 0x6e, 0x6b,
	0x6e, 0x6f, 0x77, 0x6e, 0x12, 0x16, 0x75, 0x6e, 0x6b, 0x6e, 0x6f, 0x77, 0x6e, 0x20, 0x6f, 0x70,
	0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x20, 0x74, 0x79, 0x70, 0x65, 0xaa, 0x82, 0xec, 0x8e,
	0x02, 0x1c, 0x64, 0x65, 0x66, 0x61, 0x75, 0x6c, 0x74, 0x5f, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x5f, 0x76, 0x61, 0x6c, 0x75, 0x65, 0xb2, 0x82,
	0xec, 0x8e, 0x02, 0x14, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x74, 0x79,
	0x70, 0x65, 0x5f, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x2a, 0x91, 0x03, 0x0a, 0x16, 0x42, 0x69, 0x6c,
	0x6c, 0x69, 0x6e, 0x67, 0x4f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x12, 0x4a, 0x0a, 0x1d, 0x42, 0x69, 0x6c, 0x6c, 0x69, 0x6e, 0x67, 0x4f, 0x70,
	0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x55, 0x6e, 0x6b,
	0x6e, 0x6f, 0x77, 0x6e, 0x10, 0x00, 0x1a, 0x27, 0xc2, 0xf0, 0x30, 0x23, 0x0a, 0x07, 0x75, 0x6e,
	0x6b, 0x6e, 0x6f, 0x77, 0x6e, 0x12, 0x18, 0x75, 0x6e, 0x6b, 0x6e, 0x6f, 0x77, 0x6e, 0x20, 0x6f,
	0x70, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x20, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12,
	0x63, 0x0a, 0x1f, 0x42, 0x69, 0x6c, 0x6c, 0x69, 0x6e, 0x67, 0x4f, 0x70, 0x65, 0x72, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x46, 0x69, 0x6e, 0x61, 0x6c, 0x69, 0x7a,
	0x65, 0x64, 0x10, 0x01, 0x1a, 0x3e, 0xc2, 0xf0, 0x30, 0x3a, 0x0a, 0x09, 0x66, 0x69, 0x6e, 0x61,
	0x6c, 0x69, 0x7a, 0x65, 0x64, 0x12, 0x2d, 0xd0, 0x9e, 0xd0, 0xbf, 0xd0, 0xb5, 0xd1, 0x80, 0xd0,
	0xb0, 0xd1, 0x86, 0xd0, 0xb8, 0xd1, 0x8f, 0x20, 0xd1, 0x84, 0xd0, 0xb8, 0xd0, 0xbd, 0xd0, 0xb0,
	0xd0, 0xbb, 0xd0, 0xb8, 0xd0, 0xb7, 0xd0, 0xb8, 0xd1, 0x80, 0xd0, 0xbe, 0xd0, 0xb2, 0xd0, 0xb0,
	0xd0, 0xbd, 0xd0, 0xb0, 0x12, 0x5d, 0x0a, 0x20, 0x42, 0x69, 0x6c, 0x6c, 0x69, 0x6e, 0x67, 0x4f,
	0x70, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x49, 0x6e,
	0x50, 0x72, 0x6f, 0x67, 0x72, 0x65, 0x73, 0x73, 0x10, 0x02, 0x1a, 0x37, 0xc2, 0xf0, 0x30, 0x33,
	0x0a, 0x0b, 0x69, 0x6e, 0x5f, 0x70, 0x72, 0x6f, 0x67, 0x72, 0x65, 0x73, 0x73, 0x12, 0x24, 0xd0,
	0x9e, 0xd0, 0xbf, 0xd0, 0xb5, 0xd1, 0x80, 0xd0, 0xb0, 0xd1, 0x86, 0xd0, 0xb8, 0xd1, 0x8f, 0x20,
	0xd0, 0xb2, 0x20, 0xd0, 0xbf, 0xd1, 0x80, 0xd0, 0xbe, 0xd1, 0x86, 0xd0, 0xb5, 0xd1, 0x81, 0xd1,
	0x81, 0xd0, 0xb5, 0x1a, 0x67, 0xca, 0xf0, 0x30, 0x23, 0x0a, 0x07, 0x75, 0x6e, 0x6b, 0x6e, 0x6f,
	0x77, 0x6e, 0x12, 0x18, 0x75, 0x6e, 0x6b, 0x6e, 0x6f, 0x77, 0x6e, 0x20, 0x6f, 0x70, 0x65, 0x72,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x20, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0xaa, 0x82, 0xec, 0x8e,
	0x02, 0x1e, 0x64, 0x65, 0x66, 0x61, 0x75, 0x6c, 0x74, 0x5f, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x5f, 0x76, 0x61, 0x6c, 0x75, 0x65,
	0xb2, 0x82, 0xec, 0x8e, 0x02, 0x16, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f,
	0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x5f, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x2a, 0xb9, 0x03, 0x0a,
	0x19, 0x42, 0x69, 0x6c, 0x6c, 0x69, 0x6e, 0x67, 0x4f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x54, 0x79, 0x70, 0x65, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x12, 0x51, 0x0a, 0x20, 0x42, 0x69,
	0x6c, 0x6c, 0x69, 0x6e, 0x67, 0x4f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x79,
	0x70, 0x65, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x55, 0x6e, 0x6b, 0x6e, 0x6f, 0x77, 0x6e, 0x10, 0x00,
	0x1a, 0x2b, 0xd2, 0xf0, 0x30, 0x27, 0x0a, 0x07, 0x75, 0x6e, 0x6b, 0x6e, 0x6f, 0x77, 0x6e, 0x12,
	0x1c, 0x75, 0x6e, 0x6b, 0x6e, 0x6f, 0x77, 0x6e, 0x20, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x20, 0x74, 0x79, 0x70, 0x65, 0x20, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x12, 0x2d, 0x0a,
	0x1b, 0x42, 0x69, 0x6c, 0x6c, 0x69, 0x6e, 0x67, 0x4f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x54, 0x79, 0x70, 0x65, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x49, 0x6e, 0x10, 0x01, 0x1a, 0x0c,
	0xd2, 0xf0, 0x30, 0x08, 0x0a, 0x02, 0x69, 0x6e, 0x12, 0x02, 0x69, 0x6e, 0x12, 0x30, 0x0a, 0x1c,
	0x42, 0x69, 0x6c, 0x6c, 0x69, 0x6e, 0x67, 0x4f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x54, 0x79, 0x70, 0x65, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x4f, 0x75, 0x74, 0x10, 0x02, 0x1a, 0x0e,
	0xd2, 0xf0, 0x30, 0x0a, 0x0a, 0x03, 0x6f, 0x75, 0x74, 0x12, 0x03, 0x6f, 0x75, 0x74, 0x12, 0x39,
	0x0a, 0x1f, 0x42, 0x69, 0x6c, 0x6c, 0x69, 0x6e, 0x67, 0x4f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x52, 0x65, 0x66, 0x75, 0x6e,
	0x64, 0x10, 0x03, 0x1a, 0x14, 0xd2, 0xf0, 0x30, 0x10, 0x0a, 0x06, 0x72, 0x65, 0x66, 0x75, 0x6e,
	0x64, 0x12, 0x06, 0x72, 0x65, 0x66, 0x75, 0x6e, 0x64, 0x12, 0x38, 0x0a, 0x1e, 0x42, 0x69, 0x6c,
	0x6c, 0x69, 0x6e, 0x67, 0x4f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70,
	0x65, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x50, 0x61, 0x79, 0x49, 0x6e, 0x10, 0x04, 0x1a, 0x14, 0xd2,
	0xf0, 0x30, 0x10, 0x0a, 0x06, 0x70, 0x61, 0x79, 0x5f, 0x69, 0x6e, 0x12, 0x06, 0x70, 0x61, 0x79,
	0x5f, 0x69, 0x6e, 0x1a, 0x73, 0xda, 0xf0, 0x30, 0x27, 0x0a, 0x07, 0x75, 0x6e, 0x6b, 0x6e, 0x6f,
	0x77, 0x6e, 0x12, 0x1c, 0x75, 0x6e, 0x6b, 0x6e, 0x6f, 0x77, 0x6e, 0x20, 0x6f, 0x70, 0x65, 0x72,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x20, 0x74, 0x79, 0x70, 0x65, 0x20, 0x67, 0x72, 0x6f, 0x75, 0x70,
	0xaa, 0x82, 0xec, 0x8e, 0x02, 0x22, 0x64, 0x65, 0x66, 0x61, 0x75, 0x6c, 0x74, 0x5f, 0x6f, 0x70,
	0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x5f, 0x67, 0x72, 0x6f,
	0x75, 0x70, 0x5f, 0x76, 0x61, 0x6c, 0x75, 0x65, 0xb2, 0x82, 0xec, 0x8e, 0x02, 0x1a, 0x6f, 0x70,
	0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x5f, 0x67, 0x72, 0x6f,
	0x75, 0x70, 0x5f, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x2a, 0xdd, 0x02, 0x0a, 0x0f, 0x42, 0x69, 0x6c,
	0x6c, 0x69, 0x6e, 0x67, 0x45, 0x61, 0x72, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x12, 0x3c, 0x0a, 0x16,
	0x42, 0x69, 0x6c, 0x6c, 0x69, 0x6e, 0x67, 0x45, 0x61, 0x72, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x55,
	0x6e, 0x6b, 0x6e, 0x6f, 0x77, 0x6e, 0x10, 0x00, 0x1a, 0x20, 0xe2, 0xf0, 0x30, 0x1c, 0x0a, 0x07,
	0x75, 0x6e, 0x6b, 0x6e, 0x6f, 0x77, 0x6e, 0x12, 0x11, 0x75, 0x6e, 0x6b, 0x6e, 0x6f, 0x77, 0x6e,
	0x20, 0x65, 0x61, 0x72, 0x6e, 0x20, 0x74, 0x79, 0x70, 0x65, 0x12, 0x63, 0x0a, 0x19, 0x42, 0x69,
	0x6c, 0x6c, 0x69, 0x6e, 0x67, 0x45, 0x61, 0x72, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x43, 0x6f, 0x6d,
	0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x10, 0x01, 0x1a, 0x44, 0xe2, 0xf0, 0x30, 0x40, 0x0a,
	0x0a, 0x63, 0x6f, 0x6d, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x32, 0xd0, 0x9a, 0xd0,
	0xbe, 0xd0, 0xbc, 0xd0, 0xb8, 0xd1, 0x81, 0xd1, 0x81, 0xd0, 0xb8, 0xd0, 0xbe, 0xd0, 0xbd, 0xd0,
	0xbd, 0xd0, 0xb0, 0xd1, 0x8f, 0x20, 0xd0, 0xbc, 0xd0, 0xbe, 0xd0, 0xb4, 0xd0, 0xb5, 0xd0, 0xbb,
	0xd1, 0x8c, 0x20, 0xd0, 0xb4, 0xd0, 0xbe, 0xd1, 0x85, 0xd0, 0xbe, 0xd0, 0xb4, 0xd0, 0xb0, 0x12,
	0x53, 0x0a, 0x12, 0x42, 0x69, 0x6c, 0x6c, 0x69, 0x6e, 0x67, 0x45, 0x61, 0x72, 0x6e, 0x54, 0x79,
	0x70, 0x65, 0x46, 0x65, 0x65, 0x10, 0x02, 0x1a, 0x3b, 0xe2, 0xf0, 0x30, 0x37, 0x0a, 0x03, 0x66,
	0x65, 0x65, 0x12, 0x30, 0xd0, 0x9f, 0xd0, 0xbe, 0xd0, 0xb4, 0xd0, 0xbf, 0xd0, 0xb8, 0xd1, 0x81,
	0xd0, 0xbe, 0xd1, 0x87, 0xd0, 0xbd, 0xd0, 0xb0, 0xd1, 0x8f, 0x20, 0xd0, 0xbc, 0xd0, 0xbe, 0xd0,
	0xb4, 0xd0, 0xb5, 0xd0, 0xbb, 0xd1, 0x8c, 0x20, 0xd0, 0xb4, 0xd0, 0xbe, 0xd1, 0x85, 0xd0, 0xbe,
	0xd0, 0xb4, 0xd0, 0xb0, 0x1a, 0x52, 0xea, 0xf0, 0x30, 0x1c, 0x0a, 0x07, 0x75, 0x6e, 0x6b, 0x6e,
	0x6f, 0x77, 0x6e, 0x12, 0x11, 0x75, 0x6e, 0x6b, 0x6e, 0x6f, 0x77, 0x6e, 0x20, 0x65, 0x61, 0x72,
	0x6e, 0x20, 0x74, 0x79, 0x70, 0x65, 0xaa, 0x82, 0xec, 0x8e, 0x02, 0x17, 0x64, 0x65, 0x66, 0x61,
	0x75, 0x6c, 0x74, 0x5f, 0x65, 0x61, 0x72, 0x6e, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x5f, 0x76, 0x61,
	0x6c, 0x75, 0x65, 0xb2, 0x82, 0xec, 0x8e, 0x02, 0x0f, 0x65, 0x61, 0x72, 0x6e, 0x5f, 0x74, 0x79,
	0x70, 0x65, 0x5f, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x2a, 0xa3, 0x03, 0x0a, 0x17, 0x42, 0x69, 0x6c,
	0x6c, 0x69, 0x6e, 0x67, 0x42, 0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65, 0x4f, 0x77, 0x6e, 0x65, 0x72,
	0x54, 0x79, 0x70, 0x65, 0x12, 0x4e, 0x0a, 0x1e, 0x42, 0x69, 0x6c, 0x6c, 0x69, 0x6e, 0x67, 0x42,
	0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65, 0x4f, 0x77, 0x6e, 0x65, 0x72, 0x54, 0x79, 0x70, 0x65, 0x55,
	0x6e, 0x6b, 0x6e, 0x6f, 0x77, 0x6e, 0x10, 0x00, 0x1a, 0x2a, 0xf2, 0xfe, 0xc9, 0x04, 0x25, 0x0a,
	0x07, 0x75, 0x6e, 0x6b, 0x6e, 0x6f, 0x77, 0x6e, 0x12, 0x1a, 0x75, 0x6e, 0x6b, 0x6e, 0x6f, 0x77,
	0x6e, 0x20, 0x62, 0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65, 0x20, 0x6f, 0x77, 0x6e, 0x65, 0x72, 0x20,
	0x74, 0x79, 0x70, 0x65, 0x12, 0x40, 0x0a, 0x1d, 0x42, 0x69, 0x6c, 0x6c, 0x69, 0x6e, 0x67, 0x42,
	0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65, 0x4f, 0x77, 0x6e, 0x65, 0x72, 0x54, 0x79, 0x70, 0x65, 0x53,
	0x79, 0x73, 0x74, 0x65, 0x6d, 0x10, 0x01, 0x1a, 0x1d, 0xf2, 0xfe, 0xc9, 0x04, 0x18, 0x0a, 0x06,
	0x73, 0x79, 0x73, 0x74, 0x65, 0x6d, 0x12, 0x0e, 0xd0, 0xa1, 0xd0, 0xb8, 0xd1, 0x81, 0xd1, 0x82,
	0xd0, 0xb5, 0xd0, 0xbc, 0xd0, 0xb0, 0x12, 0x44, 0x0a, 0x1f, 0x42, 0x69, 0x6c, 0x6c, 0x69, 0x6e,
	0x67, 0x42, 0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65, 0x4f, 0x77, 0x6e, 0x65, 0x72, 0x54, 0x79, 0x70,
	0x65, 0x4d, 0x65, 0x72, 0x63, 0x68, 0x61, 0x6e, 0x74, 0x10, 0x02, 0x1a, 0x1f, 0xf2, 0xfe, 0xc9,
	0x04, 0x1a, 0x0a, 0x08, 0x6d, 0x65, 0x72, 0x63, 0x68, 0x61, 0x6e, 0x74, 0x12, 0x0e, 0xd0, 0x9c,
	0xd0, 0xb5, 0xd1, 0x80, 0xd1, 0x87, 0xd0, 0xb0, 0xd0, 0xbd, 0xd1, 0x82, 0x12, 0x40, 0x0a, 0x1e,
	0x42, 0x69, 0x6c, 0x6c, 0x69, 0x6e, 0x67, 0x42, 0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65, 0x4f, 0x77,
	0x6e, 0x65, 0x72, 0x54, 0x79, 0x70, 0x65, 0x50, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x10, 0x03,
	0x1a, 0x1c, 0xf2, 0xfe, 0xc9, 0x04, 0x17, 0x0a, 0x07, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74,
	0x12, 0x0c, 0xd0, 0x9f, 0xd1, 0x80, 0xd0, 0xbe, 0xd0, 0xb5, 0xd0, 0xba, 0xd1, 0x82, 0x1a, 0x6e,
	0xfa, 0xfe, 0xc9, 0x04, 0x25, 0x0a, 0x07, 0x75, 0x6e, 0x6b, 0x6e, 0x6f, 0x77, 0x6e, 0x12, 0x1a,
	0x75, 0x6e, 0x6b, 0x6e, 0x6f, 0x77, 0x6e, 0x20, 0x62, 0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65, 0x20,
	0x6f, 0x77, 0x6e, 0x65, 0x72, 0x20, 0x74, 0x79, 0x70, 0x65, 0xaa, 0x82, 0xec, 0x8e, 0x02, 0x20,
	0x64, 0x65, 0x66, 0x61, 0x75, 0x6c, 0x74, 0x5f, 0x62, 0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65, 0x5f,
	0x6f, 0x77, 0x6e, 0x65, 0x72, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x5f, 0x76, 0x61, 0x6c, 0x75, 0x65,
	0xb2, 0x82, 0xec, 0x8e, 0x02, 0x18, 0x62, 0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65, 0x5f, 0x6f, 0x77,
	0x6e, 0x65, 0x72, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x5f, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x2a, 0xf5,
	0x02, 0x0a, 0x19, 0x42, 0x69, 0x6c, 0x6c, 0x69, 0x6e, 0x67, 0x42, 0x61, 0x6c, 0x61, 0x6e, 0x63,
	0x65, 0x4f, 0x77, 0x6e, 0x65, 0x72, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x51, 0x0a, 0x20,
	0x42, 0x69, 0x6c, 0x6c, 0x69, 0x6e, 0x67, 0x42, 0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65, 0x4f, 0x77,
	0x6e, 0x65, 0x72, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x55, 0x6e, 0x6b, 0x6e, 0x6f, 0x77, 0x6e,
	0x10, 0x00, 0x1a, 0x2b, 0x82, 0xf1, 0x30, 0x27, 0x0a, 0x07, 0x75, 0x6e, 0x6b, 0x6e, 0x6f, 0x77,
	0x6e, 0x12, 0x1c, 0x75, 0x6e, 0x6b, 0x6e, 0x6f, 0x77, 0x6e, 0x20, 0x62, 0x61, 0x6c, 0x61, 0x6e,
	0x63, 0x65, 0x20, 0x6f, 0x77, 0x6e, 0x65, 0x72, 0x20, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12,
	0x43, 0x0a, 0x1f, 0x42, 0x69, 0x6c, 0x6c, 0x69, 0x6e, 0x67, 0x42, 0x61, 0x6c, 0x61, 0x6e, 0x63,
	0x65, 0x4f, 0x77, 0x6e, 0x65, 0x72, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x41, 0x63, 0x74, 0x69,
	0x76, 0x65, 0x10, 0x01, 0x1a, 0x1e, 0x82, 0xf1, 0x30, 0x1a, 0x0a, 0x06, 0x61, 0x63, 0x74, 0x69,
	0x76, 0x65, 0x12, 0x10, 0xd0, 0x90, 0xd0, 0xba, 0xd1, 0x82, 0xd0, 0xb8, 0xd0, 0xb2, 0xd0, 0xbd,
	0xd1, 0x8b, 0xd0, 0xb9, 0x12, 0x4b, 0x0a, 0x21, 0x42, 0x69, 0x6c, 0x6c, 0x69, 0x6e, 0x67, 0x42,
	0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65, 0x4f, 0x77, 0x6e, 0x65, 0x72, 0x53, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x49, 0x6e, 0x61, 0x63, 0x74, 0x69, 0x76, 0x65, 0x10, 0x02, 0x1a, 0x24, 0x82, 0xf1, 0x30,
	0x20, 0x0a, 0x08, 0x69, 0x6e, 0x61, 0x63, 0x74, 0x69, 0x76, 0x65, 0x12, 0x14, 0xd0, 0x9d, 0xd0,
	0xb5, 0xd0, 0xb0, 0xd0, 0xba, 0xd1, 0x82, 0xd0, 0xb8, 0xd0, 0xb2, 0xd0, 0xbd, 0xd1, 0x8b, 0xd0,
	0xb9, 0x1a, 0x73, 0x8a, 0xf1, 0x30, 0x27, 0x0a, 0x07, 0x75, 0x6e, 0x6b, 0x6e, 0x6f, 0x77, 0x6e,
	0x12, 0x1c, 0x75, 0x6e, 0x6b, 0x6e, 0x6f, 0x77, 0x6e, 0x20, 0x62, 0x61, 0x6c, 0x61, 0x6e, 0x63,
	0x65, 0x20, 0x6f, 0x77, 0x6e, 0x65, 0x72, 0x20, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0xaa, 0x82,
	0xec, 0x8e, 0x02, 0x22, 0x64, 0x65, 0x66, 0x61, 0x75, 0x6c, 0x74, 0x5f, 0x62, 0x61, 0x6c, 0x61,
	0x6e, 0x63, 0x65, 0x5f, 0x6f, 0x77, 0x6e, 0x65, 0x72, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x5f, 0x76, 0x61, 0x6c, 0x75, 0x65, 0xb2, 0x82, 0xec, 0x8e, 0x02, 0x1a, 0x62, 0x61, 0x6c, 0x61,
	0x6e, 0x63, 0x65, 0x5f, 0x6f, 0x77, 0x6e, 0x65, 0x72, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x5f, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x2a, 0xd5, 0x03, 0x0a, 0x1a, 0x42, 0x69, 0x6c, 0x6c, 0x69,
	0x6e, 0x67, 0x42, 0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65, 0x43, 0x72, 0x65, 0x64, 0x69, 0x74, 0x53,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x53, 0x0a, 0x21, 0x42, 0x69, 0x6c, 0x6c, 0x69, 0x6e, 0x67,
	0x42, 0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65, 0x43, 0x72, 0x65, 0x64, 0x69, 0x74, 0x53, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x55, 0x6e, 0x6b, 0x6e, 0x6f, 0x77, 0x6e, 0x10, 0x00, 0x1a, 0x2c, 0x92, 0xf1,
	0x30, 0x28, 0x0a, 0x07, 0x75, 0x6e, 0x6b, 0x6e, 0x6f, 0x77, 0x6e, 0x12, 0x1d, 0x75, 0x6e, 0x6b,
	0x6e, 0x6f, 0x77, 0x6e, 0x20, 0x62, 0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65, 0x20, 0x63, 0x72, 0x65,
	0x64, 0x69, 0x74, 0x20, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x44, 0x0a, 0x20, 0x42, 0x69,
	0x6c, 0x6c, 0x69, 0x6e, 0x67, 0x42, 0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65, 0x43, 0x72, 0x65, 0x64,
	0x69, 0x74, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x41, 0x63, 0x74, 0x69, 0x76, 0x65, 0x10, 0x01,
	0x1a, 0x1e, 0x92, 0xf1, 0x30, 0x1a, 0x0a, 0x06, 0x61, 0x63, 0x74, 0x69, 0x76, 0x65, 0x12, 0x10,
	0xd0, 0x90, 0xd0, 0xba, 0xd1, 0x82, 0xd0, 0xb8, 0xd0, 0xb2, 0xd0, 0xbd, 0xd1, 0x8b, 0xd0, 0xb9,
	0x12, 0x4c, 0x0a, 0x22, 0x42, 0x69, 0x6c, 0x6c, 0x69, 0x6e, 0x67, 0x42, 0x61, 0x6c, 0x61, 0x6e,
	0x63, 0x65, 0x43, 0x72, 0x65, 0x64, 0x69, 0x74, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x49, 0x6e,
	0x61, 0x63, 0x74, 0x69, 0x76, 0x65, 0x10, 0x02, 0x1a, 0x24, 0x92, 0xf1, 0x30, 0x20, 0x0a, 0x08,
	0x69, 0x6e, 0x61, 0x63, 0x74, 0x69, 0x76, 0x65, 0x12, 0x14, 0xd0, 0x9d, 0xd0, 0xb5, 0xd0, 0xb0,
	0xd0, 0xba, 0xd1, 0x82, 0xd0, 0xb8, 0xd0, 0xb2, 0xd0, 0xbd, 0xd1, 0x8b, 0xd0, 0xb9, 0x12, 0x56,
	0x0a, 0x1e, 0x42, 0x69, 0x6c, 0x6c, 0x69, 0x6e, 0x67, 0x42, 0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65,
	0x43, 0x72, 0x65, 0x64, 0x69, 0x74, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x49, 0x64, 0x6c, 0x65,
	0x10, 0x03, 0x1a, 0x32, 0x92, 0xf1, 0x30, 0x2e, 0x0a, 0x04, 0x69, 0x64, 0x6c, 0x65, 0x12, 0x26,
	0xd0, 0x92, 0x20, 0xd0, 0xbe, 0xd0, 0xb6, 0xd0, 0xb8, 0xd0, 0xb4, 0xd0, 0xb0, 0xd0, 0xbd, 0xd0,
	0xb8, 0xd0, 0xb8, 0x20, 0xd0, 0xb0, 0xd0, 0xba, 0xd1, 0x82, 0xd0, 0xb8, 0xd0, 0xb2, 0xd0, 0xb0,
	0xd1, 0x86, 0xd0, 0xb8, 0xd0, 0xb8, 0x1a, 0x76, 0x9a, 0xf1, 0x30, 0x28, 0x0a, 0x07, 0x75, 0x6e,
	0x6b, 0x6e, 0x6f, 0x77, 0x6e, 0x12, 0x1d, 0x75, 0x6e, 0x6b, 0x6e, 0x6f, 0x77, 0x6e, 0x20, 0x62,
	0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65, 0x20, 0x63, 0x72, 0x65, 0x64, 0x69, 0x74, 0x20, 0x73, 0x74,
	0x61, 0x74, 0x75, 0x73, 0xaa, 0x82, 0xec, 0x8e, 0x02, 0x23, 0x64, 0x65, 0x66, 0x61, 0x75, 0x6c,
	0x74, 0x5f, 0x62, 0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65, 0x5f, 0x63, 0x72, 0x65, 0x64, 0x69, 0x74,
	0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x5f, 0x76, 0x61, 0x6c, 0x75, 0x65, 0xb2, 0x82, 0xec,
	0x8e, 0x02, 0x1b, 0x62, 0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65, 0x5f, 0x63, 0x72, 0x65, 0x64, 0x69,
	0x74, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x5f, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x2a, 0x85,
	0x03, 0x0a, 0x1b, 0x42, 0x69, 0x6c, 0x6c, 0x69, 0x6e, 0x67, 0x42, 0x61, 0x6c, 0x61, 0x6e, 0x63,
	0x65, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x55,
	0x0a, 0x22, 0x42, 0x69, 0x6c, 0x6c, 0x69, 0x6e, 0x67, 0x42, 0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65,
	0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x55, 0x6e, 0x6b,
	0x6e, 0x6f, 0x77, 0x6e, 0x10, 0x00, 0x1a, 0x2d, 0xa2, 0xf1, 0x30, 0x29, 0x0a, 0x07, 0x75, 0x6e,
	0x6b, 0x6e, 0x6f, 0x77, 0x6e, 0x12, 0x1e, 0x75, 0x6e, 0x6b, 0x6e, 0x6f, 0x77, 0x6e, 0x20, 0x62,
	0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65, 0x20, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x20, 0x73,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x45, 0x0a, 0x21, 0x42, 0x69, 0x6c, 0x6c, 0x69, 0x6e, 0x67,
	0x42, 0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x53, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x41, 0x63, 0x74, 0x69, 0x76, 0x65, 0x10, 0x01, 0x1a, 0x1e, 0xa2, 0xf1,
	0x30, 0x1a, 0x0a, 0x06, 0x61, 0x63, 0x74, 0x69, 0x76, 0x65, 0x12, 0x10, 0xd0, 0x90, 0xd0, 0xba,
	0xd1, 0x82, 0xd0, 0xb8, 0xd0, 0xb2, 0xd0, 0xbd, 0xd1, 0x8b, 0xd0, 0xb9, 0x12, 0x4d, 0x0a, 0x23,
	0x42, 0x69, 0x6c, 0x6c, 0x69, 0x6e, 0x67, 0x42, 0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65, 0x41, 0x63,
	0x63, 0x6f, 0x75, 0x6e, 0x74, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x49, 0x6e, 0x61, 0x63, 0x74,
	0x69, 0x76, 0x65, 0x10, 0x02, 0x1a, 0x24, 0xa2, 0xf1, 0x30, 0x20, 0x0a, 0x08, 0x69, 0x6e, 0x61,
	0x63, 0x74, 0x69, 0x76, 0x65, 0x12, 0x14, 0xd0, 0x9d, 0xd0, 0xb5, 0xd0, 0xb0, 0xd0, 0xba, 0xd1,
	0x82, 0xd0, 0xb8, 0xd0, 0xb2, 0xd0, 0xbd, 0xd1, 0x8b, 0xd0, 0xb9, 0x1a, 0x79, 0xaa, 0xf1, 0x30,
	0x29, 0x0a, 0x07, 0x75, 0x6e, 0x6b, 0x6e, 0x6f, 0x77, 0x6e, 0x12, 0x1e, 0x75, 0x6e, 0x6b, 0x6e,
	0x6f, 0x77, 0x6e, 0x20, 0x62, 0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65, 0x20, 0x61, 0x63, 0x63, 0x6f,
	0x75, 0x6e, 0x74, 0x20, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0xaa, 0x82, 0xec, 0x8e, 0x02, 0x24,
	0x64, 0x65, 0x66, 0x61, 0x75, 0x6c, 0x74, 0x5f, 0x62, 0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65, 0x5f,
	0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x5f, 0x76,
	0x61, 0x6c, 0x75, 0x65, 0xb2, 0x82, 0xec, 0x8e, 0x02, 0x1c, 0x62, 0x61, 0x6c, 0x61, 0x6e, 0x63,
	0x65, 0x5f, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x5f, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x2a, 0xa0, 0x03, 0x0a, 0x14, 0x42, 0x69, 0x6c, 0x6c, 0x69,
	0x6e, 0x67, 0x4f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x4d, 0x6f, 0x64, 0x65, 0x12,
	0x46, 0x0a, 0x1b, 0x42, 0x69, 0x6c, 0x6c, 0x69, 0x6e, 0x67, 0x4f, 0x70, 0x65, 0x72, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x4d, 0x6f, 0x64, 0x65, 0x55, 0x6e, 0x6b, 0x6e, 0x6f, 0x77, 0x6e, 0x10, 0x00,
	0x1a, 0x25, 0xb2, 0xf1, 0x30, 0x21, 0x0a, 0x07, 0x75, 0x6e, 0x6b, 0x6e, 0x6f, 0x77, 0x6e, 0x12,
	0x16, 0x75, 0x6e, 0x6b, 0x6e, 0x6f, 0x77, 0x6e, 0x20, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x20, 0x6d, 0x6f, 0x64, 0x65, 0x12, 0x6e, 0x0a, 0x1c, 0x42, 0x69, 0x6c, 0x6c, 0x69,
	0x6e, 0x67, 0x4f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x4d, 0x6f, 0x64, 0x65, 0x52,
	0x65, 0x61, 0x6c, 0x74, 0x69, 0x6d, 0x65, 0x10, 0x01, 0x1a, 0x4c, 0xb2, 0xf1, 0x30, 0x48, 0x0a,
	0x08, 0x72, 0x65, 0x61, 0x6c, 0x74, 0x69, 0x6d, 0x65, 0x12, 0x3c, 0xd0, 0x9e, 0xd0, 0xbf, 0xd0,
	0xb5, 0xd1, 0x80, 0xd0, 0xb0, 0xd1, 0x86, 0xd0, 0xb8, 0xd1, 0x8f, 0x20, 0xd0, 0xbc, 0xd0, 0xb3,
	0xd0, 0xbd, 0xd0, 0xbe, 0xd0, 0xb2, 0xd0, 0xb5, 0xd0, 0xbd, 0xd0, 0xbd, 0xd0, 0xbe, 0x20, 0xd0,
	0xbf, 0xd0, 0xbe, 0xd0, 0xb4, 0xd1, 0x82, 0xd0, 0xb2, 0xd0, 0xb5, 0xd1, 0x80, 0xd0, 0xb6, 0xd0,
	0xb4, 0xd0, 0xb5, 0xd0, 0xbd, 0xd0, 0xb0, 0x12, 0x6d, 0x0a, 0x1f, 0x42, 0x69, 0x6c, 0x6c, 0x69,
	0x6e, 0x67, 0x4f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x4d, 0x6f, 0x64, 0x65, 0x42,
	0x61, 0x6e, 0x6b, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x10, 0x02, 0x1a, 0x48, 0xb2, 0xf1,
	0x30, 0x44, 0x0a, 0x0c, 0x62, 0x61, 0x6e, 0x6b, 0x5f, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73,
	0x12, 0x34, 0xd0, 0x9e, 0xd0, 0xbf, 0xd0, 0xb5, 0xd1, 0x80, 0xd0, 0xb0, 0xd1, 0x86, 0xd0, 0xb8,
	0xd1, 0x8f, 0x20, 0xd0, 0xb2, 0x20, 0xd0, 0xbe, 0xd0, 0xb1, 0xd1, 0x80, 0xd0, 0xb0, 0xd0, 0xb1,
	0xd0, 0xbe, 0xd1, 0x82, 0xd0, 0xba, 0xd0, 0xb5, 0x20, 0xd1, 0x83, 0x20, 0xd0, 0xb1, 0xd0, 0xb0,
	0xd0, 0xbd, 0xd0, 0xba, 0xd0, 0xb0, 0x1a, 0x61, 0xba, 0xf1, 0x30, 0x21, 0x0a, 0x07, 0x75, 0x6e,
	0x6b, 0x6e, 0x6f, 0x77, 0x6e, 0x12, 0x16, 0x75, 0x6e, 0x6b, 0x6e, 0x6f, 0x77, 0x6e, 0x20, 0x6f,
	0x70, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x20, 0x6d, 0x6f, 0x64, 0x65, 0xaa, 0x82, 0xec,
	0x8e, 0x02, 0x1c, 0x64, 0x65, 0x66, 0x61, 0x75, 0x6c, 0x74, 0x5f, 0x6f, 0x70, 0x65, 0x72, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x5f, 0x76, 0x61, 0x6c, 0x75, 0x65, 0xb2,
	0x82, 0xec, 0x8e, 0x02, 0x14, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x6d,
	0x6f, 0x64, 0x65, 0x5f, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x2a, 0x9d, 0x0d, 0x0a, 0x21, 0x42, 0x69,
	0x6c, 0x6c, 0x69, 0x6e, 0x67, 0x4f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x79,
	0x70, 0x65, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x52, 0x65, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12,
	0x38, 0x0a, 0x28, 0x42, 0x69, 0x6c, 0x6c, 0x69, 0x6e, 0x67, 0x4f, 0x70, 0x65, 0x72, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x52, 0x65, 0x6c, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x55, 0x6e, 0x6b, 0x6e, 0x6f, 0x77, 0x6e, 0x10, 0x00, 0x1a, 0x0a, 0xc2,
	0xf1, 0x30, 0x06, 0x08, 0x01, 0x10, 0x00, 0x18, 0x00, 0x12, 0x36, 0x0a, 0x26, 0x42, 0x69, 0x6c,
	0x6c, 0x69, 0x6e, 0x67, 0x4f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70,
	0x65, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x52, 0x65, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x50, 0x61,
	0x79, 0x49, 0x6e, 0x10, 0x01, 0x1a, 0x0a, 0xc2, 0xf1, 0x30, 0x06, 0x08, 0x01, 0x10, 0x04, 0x18,
	0x02, 0x12, 0x37, 0x0a, 0x27, 0x42, 0x69, 0x6c, 0x6c, 0x69, 0x6e, 0x67, 0x4f, 0x70, 0x65, 0x72,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x52, 0x65,
	0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x50, 0x61, 0x79, 0x4f, 0x75, 0x74, 0x10, 0x02, 0x1a, 0x0a,
	0xc2, 0xf1, 0x30, 0x06, 0x08, 0x02, 0x10, 0x02, 0x18, 0x01, 0x12, 0x37, 0x0a, 0x27, 0x42, 0x69,
	0x6c, 0x6c, 0x69, 0x6e, 0x67, 0x4f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x79,
	0x70, 0x65, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x52, 0x65, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52,
	0x65, 0x66, 0x75, 0x6e, 0x64, 0x10, 0x03, 0x1a, 0x0a, 0xc2, 0xf1, 0x30, 0x06, 0x08, 0x03, 0x10,
	0x03, 0x18, 0x02, 0x12, 0x3b, 0x0a, 0x2b, 0x42, 0x69, 0x6c, 0x6c, 0x69, 0x6e, 0x67, 0x4f, 0x70,
	0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x47, 0x72, 0x6f, 0x75, 0x70,
	0x52, 0x65, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x6e, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x66,
	0x65, 0x72, 0x10, 0x04, 0x1a, 0x0a, 0xc2, 0xf1, 0x30, 0x06, 0x08, 0x04, 0x10, 0x01, 0x18, 0x01,
	0x12, 0x3c, 0x0a, 0x2c, 0x42, 0x69, 0x6c, 0x6c, 0x69, 0x6e, 0x67, 0x4f, 0x70, 0x65, 0x72, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x52, 0x65, 0x6c,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x4f, 0x75, 0x74, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x66, 0x65, 0x72,
	0x10, 0x05, 0x1a, 0x0a, 0xc2, 0xf1, 0x30, 0x06, 0x08, 0x05, 0x10, 0x02, 0x18, 0x01, 0x12, 0x44,
	0x0a, 0x34, 0x42, 0x69, 0x6c, 0x6c, 0x69, 0x6e, 0x67, 0x4f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x52, 0x65, 0x6c, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x50, 0x61, 0x79, 0x49, 0x6e, 0x43, 0x6d, 0x73, 0x4f, 0x75, 0x74, 0x41, 0x63,
	0x71, 0x75, 0x69, 0x72, 0x65, 0x72, 0x10, 0x06, 0x1a, 0x0a, 0xc2, 0xf1, 0x30, 0x06, 0x08, 0x06,
	0x10, 0x02, 0x18, 0x02, 0x12, 0x41, 0x0a, 0x31, 0x42, 0x69, 0x6c, 0x6c, 0x69, 0x6e, 0x67, 0x4f,
	0x70, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x47, 0x72, 0x6f, 0x75,
	0x70, 0x52, 0x65, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x50, 0x61, 0x79, 0x49, 0x6e, 0x43, 0x6d,
	0x73, 0x49, 0x6e, 0x53, 0x79, 0x73, 0x74, 0x65, 0x6d, 0x10, 0x07, 0x1a, 0x0a, 0xc2, 0xf1, 0x30,
	0x06, 0x08, 0x07, 0x10, 0x01, 0x18, 0x02, 0x12, 0x42, 0x0a, 0x32, 0x42, 0x69, 0x6c, 0x6c, 0x69,
	0x6e, 0x67, 0x4f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x47,
	0x72, 0x6f, 0x75, 0x70, 0x52, 0x65, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x50, 0x61, 0x79, 0x49,
	0x6e, 0x43, 0x6d, 0x73, 0x4f, 0x75, 0x74, 0x53, 0x79, 0x73, 0x74, 0x65, 0x6d, 0x10, 0x08, 0x1a,
	0x0a, 0xc2, 0xf1, 0x30, 0x06, 0x08, 0x08, 0x10, 0x02, 0x18, 0x02, 0x12, 0x43, 0x0a, 0x33, 0x42,
	0x69, 0x6c, 0x6c, 0x69, 0x6e, 0x67, 0x4f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x54,
	0x79, 0x70, 0x65, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x52, 0x65, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x50, 0x61, 0x79, 0x49, 0x6e, 0x43, 0x6d, 0x73, 0x49, 0x6e, 0x4d, 0x65, 0x72, 0x63, 0x68, 0x61,
	0x6e, 0x74, 0x10, 0x09, 0x1a, 0x0a, 0xc2, 0xf1, 0x30, 0x06, 0x08, 0x09, 0x10, 0x01, 0x18, 0x02,
	0x12, 0x44, 0x0a, 0x34, 0x42, 0x69, 0x6c, 0x6c, 0x69, 0x6e, 0x67, 0x4f, 0x70, 0x65, 0x72, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x52, 0x65, 0x6c,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x50, 0x61, 0x79, 0x49, 0x6e, 0x43, 0x6d, 0x73, 0x4f, 0x75, 0x74,
	0x4d, 0x65, 0x72, 0x63, 0x68, 0x61, 0x6e, 0x74, 0x10, 0x0a, 0x1a, 0x0a, 0xc2, 0xf1, 0x30, 0x06,
	0x08, 0x0a, 0x10, 0x02, 0x18, 0x02, 0x12, 0x43, 0x0a, 0x33, 0x42, 0x69, 0x6c, 0x6c, 0x69, 0x6e,
	0x67, 0x4f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x47, 0x72,
	0x6f, 0x75, 0x70, 0x52, 0x65, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x6e, 0x74, 0x65, 0x72,
	0x6e, 0x61, 0x6c, 0x49, 0x6e, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x66, 0x65, 0x72, 0x10, 0x0b, 0x1a,
	0x0a, 0xc2, 0xf1, 0x30, 0x06, 0x08, 0x0b, 0x10, 0x01, 0x18, 0x01, 0x12, 0x44, 0x0a, 0x34, 0x42,
	0x69, 0x6c, 0x6c, 0x69, 0x6e, 0x67, 0x4f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x54,
	0x79, 0x70, 0x65, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x52, 0x65, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x49, 0x6e, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x4f, 0x75, 0x74, 0x54, 0x72, 0x61, 0x6e, 0x73,
	0x66, 0x65, 0x72, 0x10, 0x0c, 0x1a, 0x0a, 0xc2, 0xf1, 0x30, 0x06, 0x08, 0x0c, 0x10, 0x02, 0x18,
	0x01, 0x12, 0x41, 0x0a, 0x31, 0x42, 0x69, 0x6c, 0x6c, 0x69, 0x6e, 0x67, 0x4f, 0x70, 0x65, 0x72,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x52, 0x65,
	0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x70, 0x6c, 0x69, 0x74, 0x4f, 0x75, 0x74, 0x54, 0x72,
	0x61, 0x6e, 0x73, 0x66, 0x65, 0x72, 0x10, 0x0d, 0x1a, 0x0a, 0xc2, 0xf1, 0x30, 0x06, 0x08, 0x0d,
	0x10, 0x02, 0x18, 0x01, 0x12, 0x40, 0x0a, 0x30, 0x42, 0x69, 0x6c, 0x6c, 0x69, 0x6e, 0x67, 0x4f,
	0x70, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x47, 0x72, 0x6f, 0x75,
	0x70, 0x52, 0x65, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x70, 0x6c, 0x69, 0x74, 0x49, 0x6e,
	0x54, 0x72, 0x61, 0x6e, 0x73, 0x66, 0x65, 0x72, 0x10, 0x0e, 0x1a, 0x0a, 0xc2, 0xf1, 0x30, 0x06,
	0x08, 0x0e, 0x10, 0x01, 0x18, 0x01, 0x12, 0x45, 0x0a, 0x35, 0x42, 0x69, 0x6c, 0x6c, 0x69, 0x6e,
	0x67, 0x4f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x47, 0x72,
	0x6f, 0x75, 0x70, 0x52, 0x65, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x50, 0x61, 0x79, 0x4f, 0x75,
	0x74, 0x43, 0x6d, 0x73, 0x4f, 0x75, 0x74, 0x41, 0x63, 0x71, 0x75, 0x69, 0x72, 0x65, 0x72, 0x10,
	0x0f, 0x1a, 0x0a, 0xc2, 0xf1, 0x30, 0x06, 0x08, 0x0f, 0x10, 0x02, 0x18, 0x01, 0x12, 0x42, 0x0a,
	0x32, 0x42, 0x69, 0x6c, 0x6c, 0x69, 0x6e, 0x67, 0x4f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x54, 0x79, 0x70, 0x65, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x52, 0x65, 0x6c, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x50, 0x61, 0x79, 0x4f, 0x75, 0x74, 0x43, 0x6d, 0x73, 0x49, 0x6e, 0x53, 0x79, 0x73,
	0x74, 0x65, 0x6d, 0x10, 0x10, 0x1a, 0x0a, 0xc2, 0xf1, 0x30, 0x06, 0x08, 0x10, 0x10, 0x01, 0x18,
	0x01, 0x12, 0x43, 0x0a, 0x33, 0x42, 0x69, 0x6c, 0x6c, 0x69, 0x6e, 0x67, 0x4f, 0x70, 0x65, 0x72,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x52, 0x65,
	0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x50, 0x61, 0x79, 0x4f, 0x75, 0x74, 0x43, 0x6d, 0x73, 0x4f,
	0x75, 0x74, 0x53, 0x79, 0x73, 0x74, 0x65, 0x6d, 0x10, 0x11, 0x1a, 0x0a, 0xc2, 0xf1, 0x30, 0x06,
	0x08, 0x11, 0x10, 0x02, 0x18, 0x01, 0x12, 0x44, 0x0a, 0x34, 0x42, 0x69, 0x6c, 0x6c, 0x69, 0x6e,
	0x67, 0x4f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x47, 0x72,
	0x6f, 0x75, 0x70, 0x52, 0x65, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x50, 0x61, 0x79, 0x4f, 0x75,
	0x74, 0x43, 0x6d, 0x73, 0x49, 0x6e, 0x4d, 0x65, 0x72, 0x63, 0x68, 0x61, 0x6e, 0x74, 0x10, 0x12,
	0x1a, 0x0a, 0xc2, 0xf1, 0x30, 0x06, 0x08, 0x12, 0x10, 0x01, 0x18, 0x01, 0x12, 0x45, 0x0a, 0x35,
	0x42, 0x69, 0x6c, 0x6c, 0x69, 0x6e, 0x67, 0x4f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x54, 0x79, 0x70, 0x65, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x52, 0x65, 0x6c, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x50, 0x61, 0x79, 0x4f, 0x75, 0x74, 0x43, 0x6d, 0x73, 0x4f, 0x75, 0x74, 0x4d, 0x65, 0x72,
	0x63, 0x68, 0x61, 0x6e, 0x74, 0x10, 0x13, 0x1a, 0x0a, 0xc2, 0xf1, 0x30, 0x06, 0x08, 0x13, 0x10,
	0x02, 0x18, 0x01, 0x12, 0x41, 0x0a, 0x31, 0x42, 0x69, 0x6c, 0x6c, 0x69, 0x6e, 0x67, 0x4f, 0x70,
	0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x47, 0x72, 0x6f, 0x75, 0x70,
	0x52, 0x65, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x70, 0x6c, 0x69, 0x74, 0x43, 0x6d, 0x73,
	0x49, 0x6e, 0x53, 0x79, 0x73, 0x74, 0x65, 0x6d, 0x10, 0x14, 0x1a, 0x0a, 0xc2, 0xf1, 0x30, 0x06,
	0x08, 0x14, 0x10, 0x01, 0x18, 0x01, 0x12, 0x42, 0x0a, 0x32, 0x42, 0x69, 0x6c, 0x6c, 0x69, 0x6e,
	0x67, 0x4f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x47, 0x72,
	0x6f, 0x75, 0x70, 0x52, 0x65, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x70, 0x6c, 0x69, 0x74,
	0x43, 0x6d, 0x73, 0x4f, 0x75, 0x74, 0x53, 0x79, 0x73, 0x74, 0x65, 0x6d, 0x10, 0x15, 0x1a, 0x0a,
	0xc2, 0xf1, 0x30, 0x06, 0x08, 0x15, 0x10, 0x02, 0x18, 0x01, 0x12, 0x43, 0x0a, 0x33, 0x42, 0x69,
	0x6c, 0x6c, 0x69, 0x6e, 0x67, 0x4f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x79,
	0x70, 0x65, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x52, 0x65, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x53,
	0x70, 0x6c, 0x69, 0x74, 0x43, 0x6d, 0x73, 0x49, 0x6e, 0x4d, 0x65, 0x72, 0x63, 0x68, 0x61, 0x6e,
	0x74, 0x10, 0x16, 0x1a, 0x0a, 0xc2, 0xf1, 0x30, 0x06, 0x08, 0x16, 0x10, 0x01, 0x18, 0x01, 0x12,
	0x44, 0x0a, 0x34, 0x42, 0x69, 0x6c, 0x6c, 0x69, 0x6e, 0x67, 0x4f, 0x70, 0x65, 0x72, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x52, 0x65, 0x6c, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x53, 0x70, 0x6c, 0x69, 0x74, 0x43, 0x6d, 0x73, 0x4f, 0x75, 0x74, 0x4d,
	0x65, 0x72, 0x63, 0x68, 0x61, 0x6e, 0x74, 0x10, 0x17, 0x1a, 0x0a, 0xc2, 0xf1, 0x30, 0x06, 0x08,
	0x17, 0x10, 0x02, 0x18, 0x01, 0x1a, 0x42, 0xca, 0xf1, 0x30, 0x02, 0x08, 0x00, 0xaa, 0x82, 0xec,
	0x8e, 0x02, 0x1c, 0x64, 0x65, 0x66, 0x61, 0x75, 0x6c, 0x74, 0x5f, 0x67, 0x72, 0x6f, 0x75, 0x70,
	0x5f, 0x72, 0x65, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x76, 0x61, 0x6c, 0x75, 0x65, 0xb2,
	0x82, 0xec, 0x8e, 0x02, 0x14, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x5f, 0x72, 0x65, 0x6c, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x5f, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x32, 0xe9, 0x17, 0x0a, 0x07, 0x42, 0x69,
	0x6c, 0x6c, 0x69, 0x6e, 0x67, 0x12, 0x82, 0x01, 0x0a, 0x14, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x50,
	0x61, 0x79, 0x4f, 0x75, 0x74, 0x42, 0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65, 0x56, 0x31, 0x12, 0x33,
	0x2e, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x2e, 0x62, 0x69, 0x6c, 0x6c,
	0x69, 0x6e, 0x67, 0x2e, 0x62, 0x69, 0x6c, 0x6c, 0x69, 0x6e, 0x67, 0x2e, 0x43, 0x68, 0x65, 0x63,
	0x6b, 0x50, 0x61, 0x79, 0x4f, 0x75, 0x74, 0x42, 0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65, 0x52, 0x65,
	0x71, 0x56, 0x31, 0x1a, 0x33, 0x2e, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67,
	0x2e, 0x62, 0x69, 0x6c, 0x6c, 0x69, 0x6e, 0x67, 0x2e, 0x62, 0x69, 0x6c, 0x6c, 0x69, 0x6e, 0x67,
	0x2e, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x50, 0x61, 0x79, 0x4f, 0x75, 0x74, 0x42, 0x61, 0x6c, 0x61,
	0x6e, 0x63, 0x65, 0x52, 0x65, 0x73, 0x56, 0x31, 0x22, 0x00, 0x12, 0x6d, 0x0a, 0x16, 0x42, 0x69,
	0x6c, 0x6c, 0x50, 0x61, 0x79, 0x49, 0x6e, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69,
	0x6f, 0x6e, 0x56, 0x31, 0x12, 0x39, 0x2e, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e,
	0x67, 0x2e, 0x62, 0x69, 0x6c, 0x6c, 0x69, 0x6e, 0x67, 0x2e, 0x62, 0x69, 0x6c, 0x6c, 0x69, 0x6e,
	0x67, 0x2e, 0x42, 0x69, 0x6c, 0x6c, 0x50, 0x61, 0x79, 0x49, 0x6e, 0x54, 0x72, 0x61, 0x6e, 0x73,
	0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x56, 0x31, 0x1a,
	0x16, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75,
	0x66, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x22, 0x00, 0x12, 0x6f, 0x0a, 0x17, 0x42, 0x69, 0x6c,
	0x6c, 0x50, 0x61, 0x79, 0x4f, 0x75, 0x74, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69,
	0x6f, 0x6e, 0x56, 0x31, 0x12, 0x3a, 0x2e, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e,
	0x67, 0x2e, 0x62, 0x69, 0x6c, 0x6c, 0x69, 0x6e, 0x67, 0x2e, 0x62, 0x69, 0x6c, 0x6c, 0x69, 0x6e,
	0x67, 0x2e, 0x42, 0x69, 0x6c, 0x6c, 0x50, 0x61, 0x79, 0x4f, 0x75, 0x74, 0x54, 0x72, 0x61, 0x6e,
	0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x56, 0x31,
	0x1a, 0x16, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62,
	0x75, 0x66, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x22, 0x00, 0x12, 0x6f, 0x0a, 0x17, 0x42, 0x69,
	0x6c, 0x6c, 0x52, 0x65, 0x66, 0x75, 0x6e, 0x64, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74,
	0x69, 0x6f, 0x6e, 0x56, 0x31, 0x12, 0x3a, 0x2e, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69,
	0x6e, 0x67, 0x2e, 0x62, 0x69, 0x6c, 0x6c, 0x69, 0x6e, 0x67, 0x2e, 0x62, 0x69, 0x6c, 0x6c, 0x69,
	0x6e, 0x67, 0x2e, 0x42, 0x69, 0x6c, 0x6c, 0x52, 0x65, 0x66, 0x75, 0x6e, 0x64, 0x54, 0x72, 0x61,
	0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x56,
	0x31, 0x1a, 0x16, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x62, 0x75, 0x66, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x22, 0x00, 0x12, 0x9a, 0x01, 0x0a, 0x19,
	0x43, 0x68, 0x65, 0x63, 0x6b, 0x4f, 0x75, 0x74, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x66, 0x65, 0x72,
	0x42, 0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65, 0x56, 0x31, 0x12, 0x3c, 0x2e, 0x70, 0x72, 0x6f, 0x63,
	0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x2e, 0x62, 0x69, 0x6c, 0x6c, 0x69, 0x6e, 0x67, 0x2e, 0x62,
	0x69, 0x6c, 0x6c, 0x69, 0x6e, 0x67, 0x2e, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x4f, 0x75, 0x74, 0x54,
	0x72, 0x61, 0x6e, 0x73, 0x66, 0x65, 0x72, 0x42, 0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x56, 0x31, 0x1a, 0x3d, 0x2e, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73,
	0x73, 0x69, 0x6e, 0x67, 0x2e, 0x62, 0x69, 0x6c, 0x6c, 0x69, 0x6e, 0x67, 0x2e, 0x62, 0x69, 0x6c,
	0x6c, 0x69, 0x6e, 0x67, 0x2e, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x4f, 0x75, 0x74, 0x54, 0x72, 0x61,
	0x6e, 0x73, 0x66, 0x65, 0x72, 0x42, 0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x56, 0x31, 0x22, 0x00, 0x12, 0x63, 0x0a, 0x11, 0x42, 0x69, 0x6c, 0x6c,
	0x4f, 0x75, 0x74, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x66, 0x65, 0x72, 0x56, 0x31, 0x12, 0x34, 0x2e,
	0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x2e, 0x62, 0x69, 0x6c, 0x6c, 0x69,
	0x6e, 0x67, 0x2e, 0x62, 0x69, 0x6c, 0x6c, 0x69, 0x6e, 0x67, 0x2e, 0x42, 0x69, 0x6c, 0x6c, 0x4f,
	0x75, 0x74, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x66, 0x65, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x56, 0x31, 0x1a, 0x16, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x22, 0x00, 0x12, 0x61, 0x0a,
	0x10, 0x42, 0x69, 0x6c, 0x6c, 0x49, 0x6e, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x66, 0x65, 0x72, 0x56,
	0x31, 0x12, 0x33, 0x2e, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x2e, 0x62,
	0x69, 0x6c, 0x6c, 0x69, 0x6e, 0x67, 0x2e, 0x62, 0x69, 0x6c, 0x6c, 0x69, 0x6e, 0x67, 0x2e, 0x42,
	0x69, 0x6c, 0x6c, 0x49, 0x6e, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x66, 0x65, 0x72, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x56, 0x31, 0x1a, 0x16, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x22, 0x00,
	0x12, 0xa2, 0x01, 0x0a, 0x1d, 0x47, 0x65, 0x74, 0x4d, 0x65, 0x72, 0x63, 0x68, 0x61, 0x6e, 0x74,
	0x42, 0x79, 0x42, 0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65, 0x4f, 0x77, 0x6e, 0x65, 0x72, 0x49, 0x44,
	0x56, 0x31, 0x12, 0x3e, 0x2e, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x2e,
	0x62, 0x69, 0x6c, 0x6c, 0x69, 0x6e, 0x67, 0x2e, 0x62, 0x69, 0x6c, 0x6c, 0x69, 0x6e, 0x67, 0x2e,
	0x47, 0x65, 0x74, 0x4d, 0x65, 0x72, 0x63, 0x68, 0x61, 0x6e, 0x74, 0x42, 0x79, 0x42, 0x61, 0x6c,
	0x61, 0x6e, 0x63, 0x65, 0x4f, 0x77, 0x6e, 0x65, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x56, 0x31, 0x1a, 0x3f, 0x2e, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x2e,
	0x62, 0x69, 0x6c, 0x6c, 0x69, 0x6e, 0x67, 0x2e, 0x62, 0x69, 0x6c, 0x6c, 0x69, 0x6e, 0x67, 0x2e,
	0x47, 0x65, 0x74, 0x4d, 0x65, 0x72, 0x63, 0x68, 0x61, 0x6e, 0x74, 0x42, 0x79, 0x42, 0x61, 0x6c,
	0x61, 0x6e, 0x63, 0x65, 0x4f, 0x77, 0x6e, 0x65, 0x72, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x56, 0x31, 0x22, 0x00, 0x12, 0x5f, 0x0a, 0x0f, 0x53, 0x65, 0x74, 0x49, 0x6e, 0x54, 0x72,
	0x61, 0x6e, 0x73, 0x66, 0x65, 0x72, 0x56, 0x31, 0x12, 0x32, 0x2e, 0x70, 0x72, 0x6f, 0x63, 0x65,
	0x73, 0x73, 0x69, 0x6e, 0x67, 0x2e, 0x62, 0x69, 0x6c, 0x6c, 0x69, 0x6e, 0x67, 0x2e, 0x62, 0x69,
	0x6c, 0x6c, 0x69, 0x6e, 0x67, 0x2e, 0x53, 0x65, 0x74, 0x49, 0x6e, 0x54, 0x72, 0x61, 0x6e, 0x73,
	0x66, 0x65, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x56, 0x31, 0x1a, 0x16, 0x2e, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x45,
	0x6d, 0x70, 0x74, 0x79, 0x22, 0x00, 0x12, 0x67, 0x0a, 0x13, 0x42, 0x69, 0x6c, 0x6c, 0x53, 0x70,
	0x6c, 0x69, 0x74, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x66, 0x65, 0x72, 0x56, 0x31, 0x12, 0x36, 0x2e,
	0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x2e, 0x62, 0x69, 0x6c, 0x6c, 0x69,
	0x6e, 0x67, 0x2e, 0x62, 0x69, 0x6c, 0x6c, 0x69, 0x6e, 0x67, 0x2e, 0x42, 0x69, 0x6c, 0x6c, 0x53,
	0x70, 0x6c, 0x69, 0x74, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x66, 0x65, 0x72, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x56, 0x31, 0x1a, 0x16, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x22, 0x00, 0x12,
	0x82, 0x01, 0x0a, 0x11, 0x47, 0x65, 0x74, 0x42, 0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65, 0x4f, 0x77,
	0x6e, 0x65, 0x72, 0x56, 0x31, 0x12, 0x34, 0x2e, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69,
	0x6e, 0x67, 0x2e, 0x62, 0x69, 0x6c, 0x6c, 0x69, 0x6e, 0x67, 0x2e, 0x62, 0x69, 0x6c, 0x6c, 0x69,
	0x6e, 0x67, 0x2e, 0x47, 0x65, 0x74, 0x42, 0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65, 0x4f, 0x77, 0x6e,
	0x65, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x56, 0x31, 0x1a, 0x35, 0x2e, 0x70, 0x72,
	0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x2e, 0x62, 0x69, 0x6c, 0x6c, 0x69, 0x6e, 0x67,
	0x2e, 0x62, 0x69, 0x6c, 0x6c, 0x69, 0x6e, 0x67, 0x2e, 0x47, 0x65, 0x74, 0x42, 0x61, 0x6c, 0x61,
	0x6e, 0x63, 0x65, 0x4f, 0x77, 0x6e, 0x65, 0x72, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x56, 0x31, 0x22, 0x00, 0x12, 0x75, 0x0a, 0x1b, 0x53, 0x65, 0x74, 0x42, 0x61, 0x6c, 0x61, 0x6e,
	0x63, 0x65, 0x4f, 0x77, 0x6e, 0x65, 0x72, 0x53, 0x70, 0x6c, 0x69, 0x74, 0x74, 0x61, 0x62, 0x6c,
	0x65, 0x56, 0x31, 0x12, 0x3e, 0x2e, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67,
	0x2e, 0x62, 0x69, 0x6c, 0x6c, 0x69, 0x6e, 0x67, 0x2e, 0x62, 0x69, 0x6c, 0x6c, 0x69, 0x6e, 0x67,
	0x2e, 0x53, 0x65, 0x74, 0x42, 0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65, 0x4f, 0x77, 0x6e, 0x65, 0x72,
	0x53, 0x70, 0x6c, 0x69, 0x74, 0x74, 0x61, 0x62, 0x6c, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x56, 0x31, 0x1a, 0x16, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x12, 0x8a, 0x01, 0x0a, 0x15,
	0x47, 0x65, 0x74, 0x42, 0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65, 0x4f, 0x77, 0x6e, 0x65, 0x72, 0x42,
	0x79, 0x49, 0x44, 0x56, 0x31, 0x12, 0x38, 0x2e, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69,
	0x6e, 0x67, 0x2e, 0x62, 0x69, 0x6c, 0x6c, 0x69, 0x6e, 0x67, 0x2e, 0x62, 0x69, 0x6c, 0x6c, 0x69,
	0x6e, 0x67, 0x2e, 0x47, 0x65, 0x74, 0x42, 0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65, 0x4f, 0x77, 0x6e,
	0x65, 0x72, 0x42, 0x79, 0x49, 0x44, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x56, 0x31, 0x1a,
	0x35, 0x2e, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x2e, 0x62, 0x69, 0x6c,
	0x6c, 0x69, 0x6e, 0x67, 0x2e, 0x62, 0x69, 0x6c, 0x6c, 0x69, 0x6e, 0x67, 0x2e, 0x47, 0x65, 0x74,
	0x42, 0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65, 0x4f, 0x77, 0x6e, 0x65, 0x72, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x56, 0x31, 0x22, 0x00, 0x12, 0x84, 0x01, 0x0a, 0x13, 0x47, 0x65, 0x74,
	0x45, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x54, 0x79, 0x70, 0x65, 0x42, 0x79, 0x49, 0x44, 0x56, 0x31,
	0x12, 0x36, 0x2e, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x2e, 0x62, 0x69,
	0x6c, 0x6c, 0x69, 0x6e, 0x67, 0x2e, 0x62, 0x69, 0x6c, 0x6c, 0x69, 0x6e, 0x67, 0x2e, 0x47, 0x65,
	0x74, 0x45, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x54, 0x79, 0x70, 0x65, 0x42, 0x79, 0x49, 0x44, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x56, 0x31, 0x1a, 0x33, 0x2e, 0x70, 0x72, 0x6f, 0x63, 0x65,
	0x73, 0x73, 0x69, 0x6e, 0x67, 0x2e, 0x62, 0x69, 0x6c, 0x6c, 0x69, 0x6e, 0x67, 0x2e, 0x62, 0x69,
	0x6c, 0x6c, 0x69, 0x6e, 0x67, 0x2e, 0x47, 0x65, 0x74, 0x45, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x54,
	0x79, 0x70, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x56, 0x31, 0x22, 0x00, 0x12,
	0x87, 0x01, 0x0a, 0x14, 0x47, 0x65, 0x74, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x72, 0x79, 0x43, 0x6f,
	0x64, 0x65, 0x42, 0x79, 0x49, 0x44, 0x56, 0x31, 0x12, 0x37, 0x2e, 0x70, 0x72, 0x6f, 0x63, 0x65,
	0x73, 0x73, 0x69, 0x6e, 0x67, 0x2e, 0x62, 0x69, 0x6c, 0x6c, 0x69, 0x6e, 0x67, 0x2e, 0x62, 0x69,
	0x6c, 0x6c, 0x69, 0x6e, 0x67, 0x2e, 0x47, 0x65, 0x74, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x72, 0x79,
	0x43, 0x6f, 0x64, 0x65, 0x42, 0x79, 0x49, 0x44, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x56,
	0x31, 0x1a, 0x34, 0x2e, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x2e, 0x62,
	0x69, 0x6c, 0x6c, 0x69, 0x6e, 0x67, 0x2e, 0x62, 0x69, 0x6c, 0x6c, 0x69, 0x6e, 0x67, 0x2e, 0x47,
	0x65, 0x74, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x72, 0x79, 0x43, 0x6f, 0x64, 0x65, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x56, 0x31, 0x22, 0x00, 0x12, 0x7b, 0x0a, 0x10, 0x47, 0x65, 0x74,
	0x42, 0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65, 0x42, 0x79, 0x49, 0x44, 0x56, 0x31, 0x12, 0x33, 0x2e,
	0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x2e, 0x62, 0x69, 0x6c, 0x6c, 0x69,
	0x6e, 0x67, 0x2e, 0x62, 0x69, 0x6c, 0x6c, 0x69, 0x6e, 0x67, 0x2e, 0x47, 0x65, 0x74, 0x42, 0x61,
	0x6c, 0x61, 0x6e, 0x63, 0x65, 0x42, 0x79, 0x49, 0x44, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x56, 0x31, 0x1a, 0x30, 0x2e, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x2e,
	0x62, 0x69, 0x6c, 0x6c, 0x69, 0x6e, 0x67, 0x2e, 0x62, 0x69, 0x6c, 0x6c, 0x69, 0x6e, 0x67, 0x2e,
	0x47, 0x65, 0x74, 0x42, 0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x56, 0x31, 0x22, 0x00, 0x12, 0x63, 0x0a, 0x11, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x48,
	0x61, 0x73, 0x42, 0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65, 0x56, 0x31, 0x12, 0x34, 0x2e, 0x70, 0x72,
	0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x2e, 0x62, 0x69, 0x6c, 0x6c, 0x69, 0x6e, 0x67,
	0x2e, 0x62, 0x69, 0x6c, 0x6c, 0x69, 0x6e, 0x67, 0x2e, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x48, 0x61,
	0x73, 0x42, 0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x56,
	0x31, 0x1a, 0x16, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x62, 0x75, 0x66, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x22, 0x00, 0x12, 0xb9, 0x01, 0x0a, 0x3c,
	0x43, 0x68, 0x65, 0x63, 0x6b, 0x42, 0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65, 0x4f, 0x77, 0x6e, 0x65,
	0x72, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x45, 0x78, 0x69, 0x73, 0x74, 0x73, 0x42, 0x79,
	0x42, 0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65, 0x4f, 0x77, 0x6e, 0x65, 0x72, 0x49, 0x64, 0x41, 0x6e,
	0x64, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x49, 0x64, 0x56, 0x31, 0x12, 0x5f, 0x2e, 0x70,
	0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x2e, 0x62, 0x69, 0x6c, 0x6c, 0x69, 0x6e,
	0x67, 0x2e, 0x62, 0x69, 0x6c, 0x6c, 0x69, 0x6e, 0x67, 0x2e, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x42,
	0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65, 0x4f, 0x77, 0x6e, 0x65, 0x72, 0x41, 0x63, 0x63, 0x6f, 0x75,
	0x6e, 0x74, 0x45, 0x78, 0x69, 0x73, 0x74, 0x73, 0x42, 0x79, 0x42, 0x61, 0x6c, 0x61, 0x6e, 0x63,
	0x65, 0x4f, 0x77, 0x6e, 0x65, 0x72, 0x49, 0x64, 0x41, 0x6e, 0x64, 0x41, 0x63, 0x63, 0x6f, 0x75,
	0x6e, 0x74, 0x49, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x56, 0x31, 0x1a, 0x16, 0x2e,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e,
	0x45, 0x6d, 0x70, 0x74, 0x79, 0x22, 0x00, 0x12, 0x9a, 0x01, 0x0a, 0x19, 0x47, 0x65, 0x74, 0x42,
	0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x42, 0x79, 0x4e,
	0x75, 0x6d, 0x62, 0x65, 0x72, 0x12, 0x3c, 0x2e, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69,
	0x6e, 0x67, 0x2e, 0x62, 0x69, 0x6c, 0x6c, 0x69, 0x6e, 0x67, 0x2e, 0x62, 0x69, 0x6c, 0x6c, 0x69,
	0x6e, 0x67, 0x2e, 0x47, 0x65, 0x74, 0x42, 0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65, 0x41, 0x63, 0x63,
	0x6f, 0x75, 0x6e, 0x74, 0x42, 0x79, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x1a, 0x3d, 0x2e, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67,
	0x2e, 0x62, 0x69, 0x6c, 0x6c, 0x69, 0x6e, 0x67, 0x2e, 0x62, 0x69, 0x6c, 0x6c, 0x69, 0x6e, 0x67,
	0x2e, 0x47, 0x65, 0x74, 0x42, 0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65, 0x41, 0x63, 0x63, 0x6f, 0x75,
	0x6e, 0x74, 0x42, 0x79, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x22, 0x00, 0x12, 0xca, 0x01, 0x0a, 0x27, 0x47, 0x65, 0x74, 0x43, 0x75, 0x72, 0x72,
	0x65, 0x6e, 0x74, 0x42, 0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65, 0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74,
	0x42, 0x79, 0x42, 0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65, 0x4f, 0x77, 0x6e, 0x65, 0x72, 0x49, 0x44,
	0x12, 0x4d, 0x2e, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x2e, 0x62, 0x69,
	0x6c, 0x6c, 0x69, 0x6e, 0x67, 0x2e, 0x62, 0x69, 0x6c, 0x6c, 0x69, 0x6e, 0x67, 0x2e, 0x47, 0x65,
	0x74, 0x43, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x74, 0x42, 0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65, 0x41,
	0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x42, 0x79, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x41, 0x6e,
	0x64, 0x4f, 0x77, 0x6e, 0x65, 0x72, 0x49, 0x44, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a,
	0x4e, 0x2e, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x2e, 0x62, 0x69, 0x6c,
	0x6c, 0x69, 0x6e, 0x67, 0x2e, 0x62, 0x69, 0x6c, 0x6c, 0x69, 0x6e, 0x67, 0x2e, 0x47, 0x65, 0x74,
	0x43, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x74, 0x42, 0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65, 0x41, 0x6d,
	0x6f, 0x75, 0x6e, 0x74, 0x42, 0x79, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x41, 0x6e, 0x64,
	0x4f, 0x77, 0x6e, 0x65, 0x72, 0x49, 0x44, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22,
	0x00, 0x12, 0x50, 0x0a, 0x1c, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x42, 0x61, 0x6c, 0x61, 0x6e, 0x63,
	0x65, 0x43, 0x72, 0x65, 0x64, 0x69, 0x74, 0x45, 0x78, 0x70, 0x69, 0x72, 0x65, 0x44, 0x61, 0x74,
	0x65, 0x12, 0x16, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x62, 0x75, 0x66, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x1a, 0x16, 0x2e, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x45, 0x6d, 0x70, 0x74,
	0x79, 0x22, 0x00, 0x12, 0x4f, 0x0a, 0x1b, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x42, 0x61, 0x6c, 0x61,
	0x6e, 0x63, 0x65, 0x43, 0x72, 0x65, 0x64, 0x69, 0x74, 0x53, 0x74, 0x61, 0x72, 0x74, 0x44, 0x61,
	0x74, 0x65, 0x12, 0x16, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x62, 0x75, 0x66, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x1a, 0x16, 0x2e, 0x67, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x45, 0x6d, 0x70,
	0x74, 0x79, 0x22, 0x00, 0x12, 0x52, 0x0a, 0x1e, 0x52, 0x65, 0x63, 0x61, 0x6c, 0x63, 0x75, 0x6c,
	0x61, 0x74, 0x65, 0x50, 0x72, 0x6f, 0x76, 0x69, 0x73, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x42, 0x61,
	0x6c, 0x61, 0x6e, 0x63, 0x65, 0x73, 0x12, 0x16, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x1a, 0x16,
	0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66,
	0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x22, 0x00, 0x12, 0x4c, 0x0a, 0x18, 0x52, 0x65, 0x63, 0x61,
	0x6c, 0x63, 0x75, 0x6c, 0x61, 0x74, 0x65, 0x46, 0x69, 0x6e, 0x61, 0x6c, 0x42, 0x61, 0x6c, 0x61,
	0x6e, 0x63, 0x65, 0x73, 0x12, 0x16, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x1a, 0x16, 0x2e, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x45,
	0x6d, 0x70, 0x74, 0x79, 0x22, 0x00, 0x12, 0x4d, 0x0a, 0x19, 0x52, 0x65, 0x63, 0x61, 0x6c, 0x63,
	0x75, 0x6c, 0x61, 0x74, 0x65, 0x43, 0x72, 0x65, 0x64, 0x69, 0x74, 0x42, 0x61, 0x6c, 0x61, 0x6e,
	0x63, 0x65, 0x73, 0x12, 0x16, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x1a, 0x16, 0x2e, 0x67, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x45, 0x6d,
	0x70, 0x74, 0x79, 0x22, 0x00, 0x3a, 0xa0, 0x01, 0x0a, 0x1c, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x5f, 0x62, 0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65, 0x5f, 0x74, 0x79, 0x70, 0x65,
	0x5f, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x12, 0x21, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x45, 0x6e, 0x75, 0x6d, 0x56, 0x61, 0x6c,
	0x75, 0x65, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x84, 0x8e, 0x06, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x3a, 0x2e, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x2e, 0x62,
	0x69, 0x6c, 0x6c, 0x69, 0x6e, 0x67, 0x2e, 0x62, 0x69, 0x6c, 0x6c, 0x69, 0x6e, 0x67, 0x2e, 0x42,
	0x69, 0x6c, 0x6c, 0x69, 0x6e, 0x67, 0x4f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x42,
	0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65, 0x54, 0x79, 0x70, 0x65, 0x52, 0x65, 0x66, 0x52, 0x19, 0x6f,
	0x70, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x42, 0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65, 0x54,
	0x79, 0x70, 0x65, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0xaa, 0x01, 0x0a, 0x24, 0x64, 0x65, 0x66,
	0x61, 0x75, 0x6c, 0x74, 0x5f, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x62,
	0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x5f, 0x76, 0x61, 0x6c, 0x75,
	0x65, 0x12, 0x1c, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x62, 0x75, 0x66, 0x2e, 0x45, 0x6e, 0x75, 0x6d, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18,
	0x85, 0x8e, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x3a, 0x2e, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73,
	0x73, 0x69, 0x6e, 0x67, 0x2e, 0x62, 0x69, 0x6c, 0x6c, 0x69, 0x6e, 0x67, 0x2e, 0x62, 0x69, 0x6c,
	0x6c, 0x69, 0x6e, 0x67, 0x2e, 0x42, 0x69, 0x6c, 0x6c, 0x69, 0x6e, 0x67, 0x4f, 0x70, 0x65, 0x72,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x42, 0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65, 0x54, 0x79, 0x70, 0x65,
	0x52, 0x65, 0x66, 0x52, 0x20, 0x64, 0x65, 0x66, 0x61, 0x75, 0x6c, 0x74, 0x4f, 0x70, 0x65, 0x72,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x42, 0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65, 0x54, 0x79, 0x70, 0x65,
	0x56, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x8a, 0x01, 0x0a, 0x14, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x5f, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x12, 0x21,
	0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66,
	0x2e, 0x45, 0x6e, 0x75, 0x6d, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e,
	0x73, 0x18, 0x86, 0x8e, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x33, 0x2e, 0x70, 0x72, 0x6f, 0x63,
	0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x2e, 0x62, 0x69, 0x6c, 0x6c, 0x69, 0x6e, 0x67, 0x2e, 0x62,
	0x69, 0x6c, 0x6c, 0x69, 0x6e, 0x67, 0x2e, 0x42, 0x69, 0x6c, 0x6c, 0x69, 0x6e, 0x67, 0x4f, 0x70,
	0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x52, 0x65, 0x66, 0x52, 0x12,
	0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x56, 0x61, 0x6c,
	0x75, 0x65, 0x3a, 0x94, 0x01, 0x0a, 0x1c, 0x64, 0x65, 0x66, 0x61, 0x75, 0x6c, 0x74, 0x5f, 0x6f,
	0x70, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x5f, 0x76, 0x61,
	0x6c, 0x75, 0x65, 0x12, 0x1c, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x45, 0x6e, 0x75, 0x6d, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e,
	0x73, 0x18, 0x87, 0x8e, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x33, 0x2e, 0x70, 0x72, 0x6f, 0x63,
	0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x2e, 0x62, 0x69, 0x6c, 0x6c, 0x69, 0x6e, 0x67, 0x2e, 0x62,
	0x69, 0x6c, 0x6c, 0x69, 0x6e, 0x67, 0x2e, 0x42, 0x69, 0x6c, 0x6c, 0x69, 0x6e, 0x67, 0x4f, 0x70,
	0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x52, 0x65, 0x66, 0x52, 0x19,
	0x64, 0x65, 0x66, 0x61, 0x75, 0x6c, 0x74, 0x4f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x54, 0x79, 0x70, 0x65, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x90, 0x01, 0x0a, 0x16, 0x6f, 0x70,
	0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x5f, 0x76,
	0x61, 0x6c, 0x75, 0x65, 0x12, 0x21, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x45, 0x6e, 0x75, 0x6d, 0x56, 0x61, 0x6c, 0x75, 0x65,
	0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x88, 0x8e, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x35, 0x2e, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x2e, 0x62, 0x69, 0x6c,
	0x6c, 0x69, 0x6e, 0x67, 0x2e, 0x62, 0x69, 0x6c, 0x6c, 0x69, 0x6e, 0x67, 0x2e, 0x42, 0x69, 0x6c,
	0x6c, 0x69, 0x6e, 0x67, 0x4f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x52, 0x65, 0x66, 0x52, 0x14, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x9a, 0x01, 0x0a,
	0x1e, 0x64, 0x65, 0x66, 0x61, 0x75, 0x6c, 0x74, 0x5f, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x5f, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x12,
	0x1c, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75,
	0x66, 0x2e, 0x45, 0x6e, 0x75, 0x6d, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x89, 0x8e,
	0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x35, 0x2e, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69,
	0x6e, 0x67, 0x2e, 0x62, 0x69, 0x6c, 0x6c, 0x69, 0x6e, 0x67, 0x2e, 0x62, 0x69, 0x6c, 0x6c, 0x69,
	0x6e, 0x67, 0x2e, 0x42, 0x69, 0x6c, 0x6c, 0x69, 0x6e, 0x67, 0x4f, 0x70, 0x65, 0x72, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65, 0x66, 0x52, 0x1b, 0x64, 0x65,
	0x66, 0x61, 0x75, 0x6c, 0x74, 0x4f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x9a, 0x01, 0x0a, 0x1a, 0x6f, 0x70,
	0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x5f, 0x67, 0x72, 0x6f,
	0x75, 0x70, 0x5f, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x12, 0x21, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x45, 0x6e, 0x75, 0x6d, 0x56,
	0x61, 0x6c, 0x75, 0x65, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x8a, 0x8e, 0x06, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x38, 0x2e, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67,
	0x2e, 0x62, 0x69, 0x6c, 0x6c, 0x69, 0x6e, 0x67, 0x2e, 0x62, 0x69, 0x6c, 0x6c, 0x69, 0x6e, 0x67,
	0x2e, 0x42, 0x69, 0x6c, 0x6c, 0x69, 0x6e, 0x67, 0x4f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x54, 0x79, 0x70, 0x65, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x52, 0x65, 0x66, 0x52, 0x17, 0x6f,
	0x70, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x47, 0x72, 0x6f, 0x75,
	0x70, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0xa4, 0x01, 0x0a, 0x22, 0x64, 0x65, 0x66, 0x61, 0x75,
	0x6c, 0x74, 0x5f, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x74, 0x79, 0x70,
	0x65, 0x5f, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x5f, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x12, 0x1c, 0x2e,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e,
	0x45, 0x6e, 0x75, 0x6d, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x8b, 0x8e, 0x06, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x38, 0x2e, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67,
	0x2e, 0x62, 0x69, 0x6c, 0x6c, 0x69, 0x6e, 0x67, 0x2e, 0x62, 0x69, 0x6c, 0x6c, 0x69, 0x6e, 0x67,
	0x2e, 0x42, 0x69, 0x6c, 0x6c, 0x69, 0x6e, 0x67, 0x4f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x54, 0x79, 0x70, 0x65, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x52, 0x65, 0x66, 0x52, 0x1e, 0x64,
	0x65, 0x66, 0x61, 0x75, 0x6c, 0x74, 0x4f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x54,
	0x79, 0x70, 0x65, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x7b, 0x0a,
	0x0f, 0x65, 0x61, 0x72, 0x6e, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x5f, 0x76, 0x61, 0x6c, 0x75, 0x65,
	0x12, 0x21, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62,
	0x75, 0x66, 0x2e, 0x45, 0x6e, 0x75, 0x6d, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x4f, 0x70, 0x74, 0x69,
	0x6f, 0x6e, 0x73, 0x18, 0x8c, 0x8e, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2e, 0x2e, 0x70, 0x72,
	0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x2e, 0x62, 0x69, 0x6c, 0x6c, 0x69, 0x6e, 0x67,
	0x2e, 0x62, 0x69, 0x6c, 0x6c, 0x69, 0x6e, 0x67, 0x2e, 0x42, 0x69, 0x6c, 0x6c, 0x69, 0x6e, 0x67,
	0x45, 0x61, 0x72, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x52, 0x65, 0x66, 0x52, 0x0d, 0x65, 0x61, 0x72,
	0x6e, 0x54, 0x79, 0x70, 0x65, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x85, 0x01, 0x0a, 0x17, 0x64,
	0x65, 0x66, 0x61, 0x75, 0x6c, 0x74, 0x5f, 0x65, 0x61, 0x72, 0x6e, 0x5f, 0x74, 0x79, 0x70, 0x65,
	0x5f, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x12, 0x1c, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x45, 0x6e, 0x75, 0x6d, 0x4f, 0x70, 0x74,
	0x69, 0x6f, 0x6e, 0x73, 0x18, 0x8d, 0x8e, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2e, 0x2e, 0x70,
	0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x2e, 0x62, 0x69, 0x6c, 0x6c, 0x69, 0x6e,
	0x67, 0x2e, 0x62, 0x69, 0x6c, 0x6c, 0x69, 0x6e, 0x67, 0x2e, 0x42, 0x69, 0x6c, 0x6c, 0x69, 0x6e,
	0x67, 0x45, 0x61, 0x72, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x52, 0x65, 0x66, 0x52, 0x14, 0x64, 0x65,
	0x66, 0x61, 0x75, 0x6c, 0x74, 0x45, 0x61, 0x72, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x56, 0x61, 0x6c,
	0x75, 0x65, 0x3a, 0x94, 0x01, 0x0a, 0x18, 0x62, 0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65, 0x5f, 0x6f,
	0x77, 0x6e, 0x65, 0x72, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x5f, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x12,
	0x21, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75,
	0x66, 0x2e, 0x45, 0x6e, 0x75, 0x6d, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x4f, 0x70, 0x74, 0x69, 0x6f,
	0x6e, 0x73, 0x18, 0xee, 0x9f, 0x49, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x36, 0x2e, 0x70, 0x72, 0x6f,
	0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x2e, 0x62, 0x69, 0x6c, 0x6c, 0x69, 0x6e, 0x67, 0x2e,
	0x62, 0x69, 0x6c, 0x6c, 0x69, 0x6e, 0x67, 0x2e, 0x42, 0x69, 0x6c, 0x6c, 0x69, 0x6e, 0x67, 0x42,
	0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65, 0x4f, 0x77, 0x6e, 0x65, 0x72, 0x54, 0x79, 0x70, 0x65, 0x52,
	0x65, 0x66, 0x52, 0x15, 0x62, 0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65, 0x4f, 0x77, 0x6e, 0x65, 0x72,
	0x54, 0x79, 0x70, 0x65, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x9e, 0x01, 0x0a, 0x20, 0x64, 0x65,
	0x66, 0x61, 0x75, 0x6c, 0x74, 0x5f, 0x62, 0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65, 0x5f, 0x6f, 0x77,
	0x6e, 0x65, 0x72, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x5f, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x12, 0x1c,
	0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66,
	0x2e, 0x45, 0x6e, 0x75, 0x6d, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0xef, 0x9f, 0x49,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x36, 0x2e, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e,
	0x67, 0x2e, 0x62, 0x69, 0x6c, 0x6c, 0x69, 0x6e, 0x67, 0x2e, 0x62, 0x69, 0x6c, 0x6c, 0x69, 0x6e,
	0x67, 0x2e, 0x42, 0x69, 0x6c, 0x6c, 0x69, 0x6e, 0x67, 0x42, 0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65,
	0x4f, 0x77, 0x6e, 0x65, 0x72, 0x54, 0x79, 0x70, 0x65, 0x52, 0x65, 0x66, 0x52, 0x1c, 0x64, 0x65,
	0x66, 0x61, 0x75, 0x6c, 0x74, 0x42, 0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65, 0x4f, 0x77, 0x6e, 0x65,
	0x72, 0x54, 0x79, 0x70, 0x65, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x9a, 0x01, 0x0a, 0x1a, 0x62,
	0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65, 0x5f, 0x6f, 0x77, 0x6e, 0x65, 0x72, 0x5f, 0x73, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x5f, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x12, 0x21, 0x2e, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x45, 0x6e, 0x75, 0x6d,
	0x56, 0x61, 0x6c, 0x75, 0x65, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x90, 0x8e, 0x06,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x38, 0x2e, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e,
	0x67, 0x2e, 0x62, 0x69, 0x6c, 0x6c, 0x69, 0x6e, 0x67, 0x2e, 0x62, 0x69, 0x6c, 0x6c, 0x69, 0x6e,
	0x67, 0x2e, 0x42, 0x69, 0x6c, 0x6c, 0x69, 0x6e, 0x67, 0x42, 0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65,
	0x4f, 0x77, 0x6e, 0x65, 0x72, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65, 0x66, 0x52, 0x17,
	0x62, 0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65, 0x4f, 0x77, 0x6e, 0x65, 0x72, 0x53, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0xa4, 0x01, 0x0a, 0x22, 0x64, 0x65, 0x66, 0x61,
	0x75, 0x6c, 0x74, 0x5f, 0x62, 0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65, 0x5f, 0x6f, 0x77, 0x6e, 0x65,
	0x72, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x5f, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x12, 0x1c,
	0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66,
	0x2e, 0x45, 0x6e, 0x75, 0x6d, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x91, 0x8e, 0x06,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x38, 0x2e, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e,
	0x67, 0x2e, 0x62, 0x69, 0x6c, 0x6c, 0x69, 0x6e, 0x67, 0x2e, 0x62, 0x69, 0x6c, 0x6c, 0x69, 0x6e,
	0x67, 0x2e, 0x42, 0x69, 0x6c, 0x6c, 0x69, 0x6e, 0x67, 0x42, 0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65,
	0x4f, 0x77, 0x6e, 0x65, 0x72, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65, 0x66, 0x52, 0x1e,
	0x64, 0x65, 0x66, 0x61, 0x75, 0x6c, 0x74, 0x42, 0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65, 0x4f, 0x77,
	0x6e, 0x65, 0x72, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x9d,
	0x01, 0x0a, 0x1b, 0x62, 0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65, 0x5f, 0x63, 0x72, 0x65, 0x64, 0x69,
	0x74, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x5f, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x12, 0x21,
	0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66,
	0x2e, 0x45, 0x6e, 0x75, 0x6d, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e,
	0x73, 0x18, 0x92, 0x8e, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x39, 0x2e, 0x70, 0x72, 0x6f, 0x63,
	0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x2e, 0x62, 0x69, 0x6c, 0x6c, 0x69, 0x6e, 0x67, 0x2e, 0x62,
	0x69, 0x6c, 0x6c, 0x69, 0x6e, 0x67, 0x2e, 0x42, 0x69, 0x6c, 0x6c, 0x69, 0x6e, 0x67, 0x42, 0x61,
	0x6c, 0x61, 0x6e, 0x63, 0x65, 0x43, 0x72, 0x65, 0x64, 0x69, 0x74, 0x53, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x52, 0x65, 0x66, 0x52, 0x18, 0x62, 0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65, 0x43, 0x72, 0x65,
	0x64, 0x69, 0x74, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0xa7,
	0x01, 0x0a, 0x23, 0x64, 0x65, 0x66, 0x61, 0x75, 0x6c, 0x74, 0x5f, 0x62, 0x61, 0x6c, 0x61, 0x6e,
	0x63, 0x65, 0x5f, 0x63, 0x72, 0x65, 0x64, 0x69, 0x74, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x5f, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x12, 0x1c, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x45, 0x6e, 0x75, 0x6d, 0x4f, 0x70, 0x74,
	0x69, 0x6f, 0x6e, 0x73, 0x18, 0x93, 0x8e, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x39, 0x2e, 0x70,
	0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x2e, 0x62, 0x69, 0x6c, 0x6c, 0x69, 0x6e,
	0x67, 0x2e, 0x62, 0x69, 0x6c, 0x6c, 0x69, 0x6e, 0x67, 0x2e, 0x42, 0x69, 0x6c, 0x6c, 0x69, 0x6e,
	0x67, 0x42, 0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65, 0x43, 0x72, 0x65, 0x64, 0x69, 0x74, 0x53, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x52, 0x65, 0x66, 0x52, 0x1f, 0x64, 0x65, 0x66, 0x61, 0x75, 0x6c, 0x74,
	0x42, 0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65, 0x43, 0x72, 0x65, 0x64, 0x69, 0x74, 0x53, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0xa0, 0x01, 0x0a, 0x1c, 0x62, 0x61, 0x6c,
	0x61, 0x6e, 0x63, 0x65, 0x5f, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x73, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x5f, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x12, 0x21, 0x2e, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x45, 0x6e, 0x75, 0x6d,
	0x56, 0x61, 0x6c, 0x75, 0x65, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x94, 0x8e, 0x06,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x3a, 0x2e, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e,
	0x67, 0x2e, 0x62, 0x69, 0x6c, 0x6c, 0x69, 0x6e, 0x67, 0x2e, 0x62, 0x69, 0x6c, 0x6c, 0x69, 0x6e,
	0x67, 0x2e, 0x42, 0x69, 0x6c, 0x6c, 0x69, 0x6e, 0x67, 0x42, 0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65,
	0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65, 0x66,
	0x52, 0x19, 0x62, 0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74,
	0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0xaa, 0x01, 0x0a, 0x24,
	0x64, 0x65, 0x66, 0x61, 0x75, 0x6c, 0x74, 0x5f, 0x62, 0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65, 0x5f,
	0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x5f, 0x76,
	0x61, 0x6c, 0x75, 0x65, 0x12, 0x1c, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x45, 0x6e, 0x75, 0x6d, 0x4f, 0x70, 0x74, 0x69, 0x6f,
	0x6e, 0x73, 0x18, 0x95, 0x8e, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x3a, 0x2e, 0x70, 0x72, 0x6f,
	0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x2e, 0x62, 0x69, 0x6c, 0x6c, 0x69, 0x6e, 0x67, 0x2e,
	0x62, 0x69, 0x6c, 0x6c, 0x69, 0x6e, 0x67, 0x2e, 0x42, 0x69, 0x6c, 0x6c, 0x69, 0x6e, 0x67, 0x42,
	0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x53, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x52, 0x65, 0x66, 0x52, 0x20, 0x64, 0x65, 0x66, 0x61, 0x75, 0x6c, 0x74, 0x42,
	0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x53, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x8a, 0x01, 0x0a, 0x14, 0x6f, 0x70, 0x65,
	0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x5f, 0x76, 0x61, 0x6c, 0x75,
	0x65, 0x12, 0x21, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x62, 0x75, 0x66, 0x2e, 0x45, 0x6e, 0x75, 0x6d, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x4f, 0x70, 0x74,
	0x69, 0x6f, 0x6e, 0x73, 0x18, 0x96, 0x8e, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x33, 0x2e, 0x70,
	0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x2e, 0x62, 0x69, 0x6c, 0x6c, 0x69, 0x6e,
	0x67, 0x2e, 0x62, 0x69, 0x6c, 0x6c, 0x69, 0x6e, 0x67, 0x2e, 0x42, 0x69, 0x6c, 0x6c, 0x69, 0x6e,
	0x67, 0x4f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x4d, 0x6f, 0x64, 0x65, 0x52, 0x65,
	0x66, 0x52, 0x12, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x4d, 0x6f, 0x64, 0x65,
	0x56, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x94, 0x01, 0x0a, 0x1c, 0x64, 0x65, 0x66, 0x61, 0x75, 0x6c,
	0x74, 0x5f, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x6d, 0x6f, 0x64, 0x65,
	0x5f, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x12, 0x1c, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x45, 0x6e, 0x75, 0x6d, 0x4f, 0x70, 0x74,
	0x69, 0x6f, 0x6e, 0x73, 0x18, 0x97, 0x8e, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x33, 0x2e, 0x70,
	0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x2e, 0x62, 0x69, 0x6c, 0x6c, 0x69, 0x6e,
	0x67, 0x2e, 0x62, 0x69, 0x6c, 0x6c, 0x69, 0x6e, 0x67, 0x2e, 0x42, 0x69, 0x6c, 0x6c, 0x69, 0x6e,
	0x67, 0x4f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x4d, 0x6f, 0x64, 0x65, 0x52, 0x65,
	0x66, 0x52, 0x19, 0x64, 0x65, 0x66, 0x61, 0x75, 0x6c, 0x74, 0x4f, 0x70, 0x65, 0x72, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x4d, 0x6f, 0x64, 0x65, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x97, 0x01, 0x0a,
	0x14, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x5f, 0x72, 0x65, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f,
	0x76, 0x61, 0x6c, 0x75, 0x65, 0x12, 0x21, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x45, 0x6e, 0x75, 0x6d, 0x56, 0x61, 0x6c, 0x75,
	0x65, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x98, 0x8e, 0x06, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x40, 0x2e, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x2e, 0x62, 0x69,
	0x6c, 0x6c, 0x69, 0x6e, 0x67, 0x2e, 0x62, 0x69, 0x6c, 0x6c, 0x69, 0x6e, 0x67, 0x2e, 0x42, 0x69,
	0x6c, 0x6c, 0x69, 0x6e, 0x67, 0x4f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x79,
	0x70, 0x65, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x52, 0x65, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52,
	0x65, 0x66, 0x52, 0x12, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x52, 0x65, 0x6c, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0xa1, 0x01, 0x0a, 0x1c, 0x64, 0x65, 0x66, 0x61, 0x75,
	0x6c, 0x74, 0x5f, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x5f, 0x72, 0x65, 0x6c, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x5f, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x12, 0x1c, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x45, 0x6e, 0x75, 0x6d, 0x4f, 0x70,
	0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x99, 0x8e, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x40, 0x2e,
	0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x2e, 0x62, 0x69, 0x6c, 0x6c, 0x69,
	0x6e, 0x67, 0x2e, 0x62, 0x69, 0x6c, 0x6c, 0x69, 0x6e, 0x67, 0x2e, 0x42, 0x69, 0x6c, 0x6c, 0x69,
	0x6e, 0x67, 0x4f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x47,
	0x72, 0x6f, 0x75, 0x70, 0x52, 0x65, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x66, 0x52,
	0x19, 0x64, 0x65, 0x66, 0x61, 0x75, 0x6c, 0x74, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x52, 0x65, 0x6c,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x42, 0x2d, 0x5a, 0x2b, 0x67, 0x69,
	0x74, 0x2e, 0x6c, 0x6f, 0x63, 0x61, 0x6c, 0x2f, 0x73, 0x65, 0x6e, 0x73, 0x69, 0x74, 0x69, 0x76,
	0x65, 0x2f, 0x69, 0x6e, 0x6e, 0x65, 0x72, 0x70, 0x62, 0x2f, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73,
	0x73, 0x69, 0x6e, 0x67, 0x2f, 0x67, 0x72, 0x70, 0x63, 0x62, 0x08, 0x65, 0x64, 0x69, 0x74, 0x69,
	0x6f, 0x6e, 0x73, 0x70, 0xe8, 0x07,
})

var (
	file_inner_processing_grpc_billing_proto_rawDescOnce sync.Once
	file_inner_processing_grpc_billing_proto_rawDescData []byte
)

func file_inner_processing_grpc_billing_proto_rawDescGZIP() []byte {
	file_inner_processing_grpc_billing_proto_rawDescOnce.Do(func() {
		file_inner_processing_grpc_billing_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_inner_processing_grpc_billing_proto_rawDesc), len(file_inner_processing_grpc_billing_proto_rawDesc)))
	})
	return file_inner_processing_grpc_billing_proto_rawDescData
}

var file_inner_processing_grpc_billing_proto_enumTypes = make([]protoimpl.EnumInfo, 11)
var file_inner_processing_grpc_billing_proto_msgTypes = make([]protoimpl.MessageInfo, 41)
var file_inner_processing_grpc_billing_proto_goTypes = []any{
	(BillingOperationBalanceType)(0),                                            // 0: processing.billing.billing.BillingOperationBalanceType
	(BillingOperationType)(0),                                                   // 1: processing.billing.billing.BillingOperationType
	(BillingOperationStatus)(0),                                                 // 2: processing.billing.billing.BillingOperationStatus
	(BillingOperationTypeGroup)(0),                                              // 3: processing.billing.billing.BillingOperationTypeGroup
	(BillingEarnType)(0),                                                        // 4: processing.billing.billing.BillingEarnType
	(BillingBalanceOwnerType)(0),                                                // 5: processing.billing.billing.BillingBalanceOwnerType
	(BillingBalanceOwnerStatus)(0),                                              // 6: processing.billing.billing.BillingBalanceOwnerStatus
	(BillingBalanceCreditStatus)(0),                                             // 7: processing.billing.billing.BillingBalanceCreditStatus
	(BillingBalanceAccountStatus)(0),                                            // 8: processing.billing.billing.BillingBalanceAccountStatus
	(BillingOperationMode)(0),                                                   // 9: processing.billing.billing.BillingOperationMode
	(BillingOperationTypeGroupRelation)(0),                                      // 10: processing.billing.billing.BillingOperationTypeGroupRelation
	(*CheckPayOutBalanceReqV1)(nil),                                             // 11: processing.billing.billing.CheckPayOutBalanceReqV1
	(*CheckPayOutBalanceResV1)(nil),                                             // 12: processing.billing.billing.CheckPayOutBalanceResV1
	(*BillPayInTransactionRequestV1)(nil),                                       // 13: processing.billing.billing.BillPayInTransactionRequestV1
	(*BillPayOutTransactionRequestV1)(nil),                                      // 14: processing.billing.billing.BillPayOutTransactionRequestV1
	(*GetCurrentBalanceAmountByAccountAndOwnerIDRequest)(nil),                   // 15: processing.billing.billing.GetCurrentBalanceAmountByAccountAndOwnerIDRequest
	(*GetCurrentBalanceAmountByAccountAndOwnerIDResponse)(nil),                  // 16: processing.billing.billing.GetCurrentBalanceAmountByAccountAndOwnerIDResponse
	(*BillRefundTransactionRequestV1)(nil),                                      // 17: processing.billing.billing.BillRefundTransactionRequestV1
	(*CheckBalanceOwnerAccountExistsByBalanceOwnerIdAndAccountIdRequestV1)(nil), // 18: processing.billing.billing.CheckBalanceOwnerAccountExistsByBalanceOwnerIdAndAccountIdRequestV1
	(*GetBalanceAccountByNumberRequest)(nil),                                    // 19: processing.billing.billing.GetBalanceAccountByNumberRequest
	(*GetBalanceAccountByNumberResponse)(nil),                                   // 20: processing.billing.billing.GetBalanceAccountByNumberResponse
	(*CheckOutTransferBalanceRequestV1)(nil),                                    // 21: processing.billing.billing.CheckOutTransferBalanceRequestV1
	(*SetBalanceOwnerSplittableRequestV1)(nil),                                  // 22: processing.billing.billing.SetBalanceOwnerSplittableRequestV1
	(*GetBalanceOwnerRequestV1)(nil),                                            // 23: processing.billing.billing.GetBalanceOwnerRequestV1
	(*CheckOutTransferBalanceResponseV1)(nil),                                   // 24: processing.billing.billing.CheckOutTransferBalanceResponseV1
	(*BillOutTransferRequestV1)(nil),                                            // 25: processing.billing.billing.BillOutTransferRequestV1
	(*BillInTransferRequestV1)(nil),                                             // 26: processing.billing.billing.BillInTransferRequestV1
	(*GetMerchantByBalanceOwnerRequestV1)(nil),                                  // 27: processing.billing.billing.GetMerchantByBalanceOwnerRequestV1
	(*GetMerchantByBalanceOwnerResponseV1)(nil),                                 // 28: processing.billing.billing.GetMerchantByBalanceOwnerResponseV1
	(*GetBalanceOwnerResponseV1)(nil),                                           // 29: processing.billing.billing.GetBalanceOwnerResponseV1
	(*SetInTransferRequestV1)(nil),                                              // 30: processing.billing.billing.SetInTransferRequestV1
	(*SplitTransferOperationV1)(nil),                                            // 31: processing.billing.billing.SplitTransferOperationV1
	(*BillSplitTransferRequestV1)(nil),                                          // 32: processing.billing.billing.BillSplitTransferRequestV1
	(*GetBalanceOwnerByIDRequestV1)(nil),                                        // 33: processing.billing.billing.GetBalanceOwnerByIDRequestV1
	(*GetEntityTypeByIDRequestV1)(nil),                                          // 34: processing.billing.billing.GetEntityTypeByIDRequestV1
	(*GetEntityTypeResponseV1)(nil),                                             // 35: processing.billing.billing.GetEntityTypeResponseV1
	(*GetCountryCodeByIDRequestV1)(nil),                                         // 36: processing.billing.billing.GetCountryCodeByIDRequestV1
	(*GetCountryCodeResponseV1)(nil),                                            // 37: processing.billing.billing.GetCountryCodeResponseV1
	(*GetBalanceByIDRequestV1)(nil),                                             // 38: processing.billing.billing.GetBalanceByIDRequestV1
	(*GetBalanceResponseV1)(nil),                                                // 39: processing.billing.billing.GetBalanceResponseV1
	(*CheckHasBalanceRequestV1)(nil),                                            // 40: processing.billing.billing.CheckHasBalanceRequestV1
	(*BillingOperationBalanceTypeRef)(nil),                                      // 41: processing.billing.billing.BillingOperationBalanceTypeRef
	(*BillingOperationTypeRef)(nil),                                             // 42: processing.billing.billing.BillingOperationTypeRef
	(*BillingOperationStatusRef)(nil),                                           // 43: processing.billing.billing.BillingOperationStatusRef
	(*BillingOperationTypeGroupRef)(nil),                                        // 44: processing.billing.billing.BillingOperationTypeGroupRef
	(*BillingEarnTypeRef)(nil),                                                  // 45: processing.billing.billing.BillingEarnTypeRef
	(*BillingBalanceOwnerTypeRef)(nil),                                          // 46: processing.billing.billing.BillingBalanceOwnerTypeRef
	(*BillingBalanceOwnerStatusRef)(nil),                                        // 47: processing.billing.billing.BillingBalanceOwnerStatusRef
	(*BillingBalanceCreditStatusRef)(nil),                                       // 48: processing.billing.billing.BillingBalanceCreditStatusRef
	(*BillingBalanceAccountStatusRef)(nil),                                      // 49: processing.billing.billing.BillingBalanceAccountStatusRef
	(*BillingOperationModeRef)(nil),                                             // 50: processing.billing.billing.BillingOperationModeRef
	(*BillingOperationTypeGroupRelationRef)(nil),                                // 51: processing.billing.billing.BillingOperationTypeGroupRelationRef
	(*timestamppb.Timestamp)(nil),                                               // 52: google.protobuf.Timestamp
	(*CommissionV1)(nil),                                                        // 53: processing.commission.commission.CommissionV1
	(*descriptorpb.EnumValueOptions)(nil),                                       // 54: google.protobuf.EnumValueOptions
	(*descriptorpb.EnumOptions)(nil),                                            // 55: google.protobuf.EnumOptions
	(*emptypb.Empty)(nil),                                                       // 56: google.protobuf.Empty
}
var file_inner_processing_grpc_billing_proto_depIdxs = []int32{
	52, // 0: processing.billing.billing.BillPayOutTransactionRequestV1.transaction_created_at:type_name -> google.protobuf.Timestamp
	53, // 1: processing.billing.billing.SplitTransferOperationV1.commission_list:type_name -> processing.commission.commission.CommissionV1
	31, // 2: processing.billing.billing.BillSplitTransferRequestV1.operations:type_name -> processing.billing.billing.SplitTransferOperationV1
	52, // 3: processing.billing.billing.GetBalanceResponseV1.last_calc_time:type_name -> google.protobuf.Timestamp
	1,  // 4: processing.billing.billing.BillingOperationTypeGroupRelationRef.operation_type:type_name -> processing.billing.billing.BillingOperationType
	3,  // 5: processing.billing.billing.BillingOperationTypeGroupRelationRef.operation_type_group:type_name -> processing.billing.billing.BillingOperationTypeGroup
	9,  // 6: processing.billing.billing.BillingOperationTypeGroupRelationRef.operation_mode:type_name -> processing.billing.billing.BillingOperationMode
	54, // 7: processing.billing.billing.operation_balance_type_value:extendee -> google.protobuf.EnumValueOptions
	55, // 8: processing.billing.billing.default_operation_balance_type_value:extendee -> google.protobuf.EnumOptions
	54, // 9: processing.billing.billing.operation_type_value:extendee -> google.protobuf.EnumValueOptions
	55, // 10: processing.billing.billing.default_operation_type_value:extendee -> google.protobuf.EnumOptions
	54, // 11: processing.billing.billing.operation_status_value:extendee -> google.protobuf.EnumValueOptions
	55, // 12: processing.billing.billing.default_operation_status_value:extendee -> google.protobuf.EnumOptions
	54, // 13: processing.billing.billing.operation_type_group_value:extendee -> google.protobuf.EnumValueOptions
	55, // 14: processing.billing.billing.default_operation_type_group_value:extendee -> google.protobuf.EnumOptions
	54, // 15: processing.billing.billing.earn_type_value:extendee -> google.protobuf.EnumValueOptions
	55, // 16: processing.billing.billing.default_earn_type_value:extendee -> google.protobuf.EnumOptions
	54, // 17: processing.billing.billing.balance_owner_type_value:extendee -> google.protobuf.EnumValueOptions
	55, // 18: processing.billing.billing.default_balance_owner_type_value:extendee -> google.protobuf.EnumOptions
	54, // 19: processing.billing.billing.balance_owner_status_value:extendee -> google.protobuf.EnumValueOptions
	55, // 20: processing.billing.billing.default_balance_owner_status_value:extendee -> google.protobuf.EnumOptions
	54, // 21: processing.billing.billing.balance_credit_status_value:extendee -> google.protobuf.EnumValueOptions
	55, // 22: processing.billing.billing.default_balance_credit_status_value:extendee -> google.protobuf.EnumOptions
	54, // 23: processing.billing.billing.balance_account_status_value:extendee -> google.protobuf.EnumValueOptions
	55, // 24: processing.billing.billing.default_balance_account_status_value:extendee -> google.protobuf.EnumOptions
	54, // 25: processing.billing.billing.operation_mode_value:extendee -> google.protobuf.EnumValueOptions
	55, // 26: processing.billing.billing.default_operation_mode_value:extendee -> google.protobuf.EnumOptions
	54, // 27: processing.billing.billing.group_relation_value:extendee -> google.protobuf.EnumValueOptions
	55, // 28: processing.billing.billing.default_group_relation_value:extendee -> google.protobuf.EnumOptions
	41, // 29: processing.billing.billing.operation_balance_type_value:type_name -> processing.billing.billing.BillingOperationBalanceTypeRef
	41, // 30: processing.billing.billing.default_operation_balance_type_value:type_name -> processing.billing.billing.BillingOperationBalanceTypeRef
	42, // 31: processing.billing.billing.operation_type_value:type_name -> processing.billing.billing.BillingOperationTypeRef
	42, // 32: processing.billing.billing.default_operation_type_value:type_name -> processing.billing.billing.BillingOperationTypeRef
	43, // 33: processing.billing.billing.operation_status_value:type_name -> processing.billing.billing.BillingOperationStatusRef
	43, // 34: processing.billing.billing.default_operation_status_value:type_name -> processing.billing.billing.BillingOperationStatusRef
	44, // 35: processing.billing.billing.operation_type_group_value:type_name -> processing.billing.billing.BillingOperationTypeGroupRef
	44, // 36: processing.billing.billing.default_operation_type_group_value:type_name -> processing.billing.billing.BillingOperationTypeGroupRef
	45, // 37: processing.billing.billing.earn_type_value:type_name -> processing.billing.billing.BillingEarnTypeRef
	45, // 38: processing.billing.billing.default_earn_type_value:type_name -> processing.billing.billing.BillingEarnTypeRef
	46, // 39: processing.billing.billing.balance_owner_type_value:type_name -> processing.billing.billing.BillingBalanceOwnerTypeRef
	46, // 40: processing.billing.billing.default_balance_owner_type_value:type_name -> processing.billing.billing.BillingBalanceOwnerTypeRef
	47, // 41: processing.billing.billing.balance_owner_status_value:type_name -> processing.billing.billing.BillingBalanceOwnerStatusRef
	47, // 42: processing.billing.billing.default_balance_owner_status_value:type_name -> processing.billing.billing.BillingBalanceOwnerStatusRef
	48, // 43: processing.billing.billing.balance_credit_status_value:type_name -> processing.billing.billing.BillingBalanceCreditStatusRef
	48, // 44: processing.billing.billing.default_balance_credit_status_value:type_name -> processing.billing.billing.BillingBalanceCreditStatusRef
	49, // 45: processing.billing.billing.balance_account_status_value:type_name -> processing.billing.billing.BillingBalanceAccountStatusRef
	49, // 46: processing.billing.billing.default_balance_account_status_value:type_name -> processing.billing.billing.BillingBalanceAccountStatusRef
	50, // 47: processing.billing.billing.operation_mode_value:type_name -> processing.billing.billing.BillingOperationModeRef
	50, // 48: processing.billing.billing.default_operation_mode_value:type_name -> processing.billing.billing.BillingOperationModeRef
	51, // 49: processing.billing.billing.group_relation_value:type_name -> processing.billing.billing.BillingOperationTypeGroupRelationRef
	51, // 50: processing.billing.billing.default_group_relation_value:type_name -> processing.billing.billing.BillingOperationTypeGroupRelationRef
	11, // 51: processing.billing.billing.Billing.CheckPayOutBalanceV1:input_type -> processing.billing.billing.CheckPayOutBalanceReqV1
	13, // 52: processing.billing.billing.Billing.BillPayInTransactionV1:input_type -> processing.billing.billing.BillPayInTransactionRequestV1
	14, // 53: processing.billing.billing.Billing.BillPayOutTransactionV1:input_type -> processing.billing.billing.BillPayOutTransactionRequestV1
	17, // 54: processing.billing.billing.Billing.BillRefundTransactionV1:input_type -> processing.billing.billing.BillRefundTransactionRequestV1
	21, // 55: processing.billing.billing.Billing.CheckOutTransferBalanceV1:input_type -> processing.billing.billing.CheckOutTransferBalanceRequestV1
	25, // 56: processing.billing.billing.Billing.BillOutTransferV1:input_type -> processing.billing.billing.BillOutTransferRequestV1
	26, // 57: processing.billing.billing.Billing.BillInTransferV1:input_type -> processing.billing.billing.BillInTransferRequestV1
	27, // 58: processing.billing.billing.Billing.GetMerchantByBalanceOwnerIDV1:input_type -> processing.billing.billing.GetMerchantByBalanceOwnerRequestV1
	30, // 59: processing.billing.billing.Billing.SetInTransferV1:input_type -> processing.billing.billing.SetInTransferRequestV1
	32, // 60: processing.billing.billing.Billing.BillSplitTransferV1:input_type -> processing.billing.billing.BillSplitTransferRequestV1
	23, // 61: processing.billing.billing.Billing.GetBalanceOwnerV1:input_type -> processing.billing.billing.GetBalanceOwnerRequestV1
	22, // 62: processing.billing.billing.Billing.SetBalanceOwnerSplittableV1:input_type -> processing.billing.billing.SetBalanceOwnerSplittableRequestV1
	33, // 63: processing.billing.billing.Billing.GetBalanceOwnerByIDV1:input_type -> processing.billing.billing.GetBalanceOwnerByIDRequestV1
	34, // 64: processing.billing.billing.Billing.GetEntityTypeByIDV1:input_type -> processing.billing.billing.GetEntityTypeByIDRequestV1
	36, // 65: processing.billing.billing.Billing.GetCountryCodeByIDV1:input_type -> processing.billing.billing.GetCountryCodeByIDRequestV1
	38, // 66: processing.billing.billing.Billing.GetBalanceByIDV1:input_type -> processing.billing.billing.GetBalanceByIDRequestV1
	40, // 67: processing.billing.billing.Billing.CheckHasBalanceV1:input_type -> processing.billing.billing.CheckHasBalanceRequestV1
	18, // 68: processing.billing.billing.Billing.CheckBalanceOwnerAccountExistsByBalanceOwnerIdAndAccountIdV1:input_type -> processing.billing.billing.CheckBalanceOwnerAccountExistsByBalanceOwnerIdAndAccountIdRequestV1
	19, // 69: processing.billing.billing.Billing.GetBalanceAccountByNumber:input_type -> processing.billing.billing.GetBalanceAccountByNumberRequest
	15, // 70: processing.billing.billing.Billing.GetCurrentBalanceAmountByBalanceOwnerID:input_type -> processing.billing.billing.GetCurrentBalanceAmountByAccountAndOwnerIDRequest
	56, // 71: processing.billing.billing.Billing.CheckBalanceCreditExpireDate:input_type -> google.protobuf.Empty
	56, // 72: processing.billing.billing.Billing.CheckBalanceCreditStartDate:input_type -> google.protobuf.Empty
	56, // 73: processing.billing.billing.Billing.RecalculateProvisionalBalances:input_type -> google.protobuf.Empty
	56, // 74: processing.billing.billing.Billing.RecalculateFinalBalances:input_type -> google.protobuf.Empty
	56, // 75: processing.billing.billing.Billing.RecalculateCreditBalances:input_type -> google.protobuf.Empty
	12, // 76: processing.billing.billing.Billing.CheckPayOutBalanceV1:output_type -> processing.billing.billing.CheckPayOutBalanceResV1
	56, // 77: processing.billing.billing.Billing.BillPayInTransactionV1:output_type -> google.protobuf.Empty
	56, // 78: processing.billing.billing.Billing.BillPayOutTransactionV1:output_type -> google.protobuf.Empty
	56, // 79: processing.billing.billing.Billing.BillRefundTransactionV1:output_type -> google.protobuf.Empty
	24, // 80: processing.billing.billing.Billing.CheckOutTransferBalanceV1:output_type -> processing.billing.billing.CheckOutTransferBalanceResponseV1
	56, // 81: processing.billing.billing.Billing.BillOutTransferV1:output_type -> google.protobuf.Empty
	56, // 82: processing.billing.billing.Billing.BillInTransferV1:output_type -> google.protobuf.Empty
	28, // 83: processing.billing.billing.Billing.GetMerchantByBalanceOwnerIDV1:output_type -> processing.billing.billing.GetMerchantByBalanceOwnerResponseV1
	56, // 84: processing.billing.billing.Billing.SetInTransferV1:output_type -> google.protobuf.Empty
	56, // 85: processing.billing.billing.Billing.BillSplitTransferV1:output_type -> google.protobuf.Empty
	29, // 86: processing.billing.billing.Billing.GetBalanceOwnerV1:output_type -> processing.billing.billing.GetBalanceOwnerResponseV1
	56, // 87: processing.billing.billing.Billing.SetBalanceOwnerSplittableV1:output_type -> google.protobuf.Empty
	29, // 88: processing.billing.billing.Billing.GetBalanceOwnerByIDV1:output_type -> processing.billing.billing.GetBalanceOwnerResponseV1
	35, // 89: processing.billing.billing.Billing.GetEntityTypeByIDV1:output_type -> processing.billing.billing.GetEntityTypeResponseV1
	37, // 90: processing.billing.billing.Billing.GetCountryCodeByIDV1:output_type -> processing.billing.billing.GetCountryCodeResponseV1
	39, // 91: processing.billing.billing.Billing.GetBalanceByIDV1:output_type -> processing.billing.billing.GetBalanceResponseV1
	56, // 92: processing.billing.billing.Billing.CheckHasBalanceV1:output_type -> google.protobuf.Empty
	56, // 93: processing.billing.billing.Billing.CheckBalanceOwnerAccountExistsByBalanceOwnerIdAndAccountIdV1:output_type -> google.protobuf.Empty
	20, // 94: processing.billing.billing.Billing.GetBalanceAccountByNumber:output_type -> processing.billing.billing.GetBalanceAccountByNumberResponse
	16, // 95: processing.billing.billing.Billing.GetCurrentBalanceAmountByBalanceOwnerID:output_type -> processing.billing.billing.GetCurrentBalanceAmountByAccountAndOwnerIDResponse
	56, // 96: processing.billing.billing.Billing.CheckBalanceCreditExpireDate:output_type -> google.protobuf.Empty
	56, // 97: processing.billing.billing.Billing.CheckBalanceCreditStartDate:output_type -> google.protobuf.Empty
	56, // 98: processing.billing.billing.Billing.RecalculateProvisionalBalances:output_type -> google.protobuf.Empty
	56, // 99: processing.billing.billing.Billing.RecalculateFinalBalances:output_type -> google.protobuf.Empty
	56, // 100: processing.billing.billing.Billing.RecalculateCreditBalances:output_type -> google.protobuf.Empty
	76, // [76:101] is the sub-list for method output_type
	51, // [51:76] is the sub-list for method input_type
	29, // [29:51] is the sub-list for extension type_name
	7,  // [7:29] is the sub-list for extension extendee
	0,  // [0:7] is the sub-list for field type_name
}

func init() { file_inner_processing_grpc_billing_proto_init() }
func file_inner_processing_grpc_billing_proto_init() {
	if File_inner_processing_grpc_billing_proto != nil {
		return
	}
	file_inner_processing_grpc_commission_proto_init()
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_inner_processing_grpc_billing_proto_rawDesc), len(file_inner_processing_grpc_billing_proto_rawDesc)),
			NumEnums:      11,
			NumMessages:   41,
			NumExtensions: 22,
			NumServices:   1,
		},
		GoTypes:           file_inner_processing_grpc_billing_proto_goTypes,
		DependencyIndexes: file_inner_processing_grpc_billing_proto_depIdxs,
		EnumInfos:         file_inner_processing_grpc_billing_proto_enumTypes,
		MessageInfos:      file_inner_processing_grpc_billing_proto_msgTypes,
		ExtensionInfos:    file_inner_processing_grpc_billing_proto_extTypes,
	}.Build()
	File_inner_processing_grpc_billing_proto = out.File
	file_inner_processing_grpc_billing_proto_goTypes = nil
	file_inner_processing_grpc_billing_proto_depIdxs = nil
}
