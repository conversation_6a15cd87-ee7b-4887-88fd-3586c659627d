// Code generated by MockGen. DO NOT EDIT.
// Source: jusan.gin.pb.go

// Package grpcmock is a generated GoMock package.
package grpcmock

import (
	reflect "reflect"

	gin "github.com/gin-gonic/gin"
	gomock "github.com/golang/mock/gomock"
)

// MockGinJusanServer is a mock of GinJusanServer interface.
type MockGinJusanServer struct {
	ctrl     *gomock.Controller
	recorder *MockGinJusanServerMockRecorder
}

// MockGinJusanServerMockRecorder is the mock recorder for MockGinJusanServer.
type MockGinJusanServerMockRecorder struct {
	mock *MockGinJusanServer
}

// NewMockGinJusanServer creates a new mock instance.
func NewMockGinJusanServer(ctrl *gomock.Controller) *MockGinJusanServer {
	mock := &MockGinJusanServer{ctrl: ctrl}
	mock.recorder = &MockGinJusanServerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockGinJusanServer) EXPECT() *MockGinJusanServerMockRecorder {
	return m.recorder
}

// ApplePay mocks base method.
func (m *MockGinJusanServer) ApplePay(c *gin.Context) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ApplePay", c)
	ret0, _ := ret[0].(error)
	return ret0
}

// ApplePay indicates an expected call of ApplePay.
func (mr *MockGinJusanServerMockRecorder) ApplePay(c interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ApplePay", reflect.TypeOf((*MockGinJusanServer)(nil).ApplePay), c)
}

// Cancel mocks base method.
func (m *MockGinJusanServer) Cancel(c *gin.Context) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Cancel", c)
	ret0, _ := ret[0].(error)
	return ret0
}

// Cancel indicates an expected call of Cancel.
func (mr *MockGinJusanServerMockRecorder) Cancel(c interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Cancel", reflect.TypeOf((*MockGinJusanServer)(nil).Cancel), c)
}

// Charge mocks base method.
func (m *MockGinJusanServer) Charge(c *gin.Context) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Charge", c)
	ret0, _ := ret[0].(error)
	return ret0
}

// Charge indicates an expected call of Charge.
func (mr *MockGinJusanServerMockRecorder) Charge(c interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Charge", reflect.TypeOf((*MockGinJusanServer)(nil).Charge), c)
}

// CheckBalance mocks base method.
func (m *MockGinJusanServer) CheckBalance(c *gin.Context) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CheckBalance", c)
	ret0, _ := ret[0].(error)
	return ret0
}

// CheckBalance indicates an expected call of CheckBalance.
func (mr *MockGinJusanServerMockRecorder) CheckBalance(c interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CheckBalance", reflect.TypeOf((*MockGinJusanServer)(nil).CheckBalance), c)
}

// ConfirmEmission mocks base method.
func (m *MockGinJusanServer) ConfirmEmission(c *gin.Context) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ConfirmEmission", c)
	ret0, _ := ret[0].(error)
	return ret0
}

// ConfirmEmission indicates an expected call of ConfirmEmission.
func (mr *MockGinJusanServerMockRecorder) ConfirmEmission(c interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ConfirmEmission", reflect.TypeOf((*MockGinJusanServer)(nil).ConfirmEmission), c)
}

// GetAcquirerIdentifier mocks base method.
func (m *MockGinJusanServer) GetAcquirerIdentifier(c *gin.Context) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAcquirerIdentifier", c)
	ret0, _ := ret[0].(error)
	return ret0
}

// GetAcquirerIdentifier indicates an expected call of GetAcquirerIdentifier.
func (mr *MockGinJusanServerMockRecorder) GetAcquirerIdentifier(c interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAcquirerIdentifier", reflect.TypeOf((*MockGinJusanServer)(nil).GetAcquirerIdentifier), c)
}

// GetBankTransactionStatus mocks base method.
func (m *MockGinJusanServer) GetBankTransactionStatus(c *gin.Context) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetBankTransactionStatus", c)
	ret0, _ := ret[0].(error)
	return ret0
}

// GetBankTransactionStatus indicates an expected call of GetBankTransactionStatus.
func (mr *MockGinJusanServerMockRecorder) GetBankTransactionStatus(c interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetBankTransactionStatus", reflect.TypeOf((*MockGinJusanServer)(nil).GetBankTransactionStatus), c)
}

// GetBankTransactionStatusUnformated mocks base method.
func (m *MockGinJusanServer) GetBankTransactionStatusUnformated(c *gin.Context) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetBankTransactionStatusUnformated", c)
	ret0, _ := ret[0].(error)
	return ret0
}

// GetBankTransactionStatusUnformated indicates an expected call of GetBankTransactionStatusUnformated.
func (mr *MockGinJusanServerMockRecorder) GetBankTransactionStatusUnformated(c interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetBankTransactionStatusUnformated", reflect.TypeOf((*MockGinJusanServer)(nil).GetBankTransactionStatusUnformated), c)
}

// GetEmission mocks base method.
func (m *MockGinJusanServer) GetEmission(c *gin.Context) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetEmission", c)
	ret0, _ := ret[0].(error)
	return ret0
}

// GetEmission indicates an expected call of GetEmission.
func (mr *MockGinJusanServerMockRecorder) GetEmission(c interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetEmission", reflect.TypeOf((*MockGinJusanServer)(nil).GetEmission), c)
}

// GooglePay mocks base method.
func (m *MockGinJusanServer) GooglePay(c *gin.Context) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GooglePay", c)
	ret0, _ := ret[0].(error)
	return ret0
}

// GooglePay indicates an expected call of GooglePay.
func (mr *MockGinJusanServerMockRecorder) GooglePay(c interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GooglePay", reflect.TypeOf((*MockGinJusanServer)(nil).GooglePay), c)
}

// MakeToken mocks base method.
func (m *MockGinJusanServer) MakeToken(c *gin.Context) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "MakeToken", c)
	ret0, _ := ret[0].(error)
	return ret0
}

// MakeToken indicates an expected call of MakeToken.
func (mr *MockGinJusanServerMockRecorder) MakeToken(c interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "MakeToken", reflect.TypeOf((*MockGinJusanServer)(nil).MakeToken), c)
}

// OneClickPayIn mocks base method.
func (m *MockGinJusanServer) OneClickPayIn(c *gin.Context) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "OneClickPayIn", c)
	ret0, _ := ret[0].(error)
	return ret0
}

// OneClickPayIn indicates an expected call of OneClickPayIn.
func (mr *MockGinJusanServerMockRecorder) OneClickPayIn(c interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "OneClickPayIn", reflect.TypeOf((*MockGinJusanServer)(nil).OneClickPayIn), c)
}

// PayIn mocks base method.
func (m *MockGinJusanServer) PayIn(c *gin.Context) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "PayIn", c)
	ret0, _ := ret[0].(error)
	return ret0
}

// PayIn indicates an expected call of PayIn.
func (mr *MockGinJusanServerMockRecorder) PayIn(c interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "PayIn", reflect.TypeOf((*MockGinJusanServer)(nil).PayIn), c)
}

// PayOut mocks base method.
func (m *MockGinJusanServer) PayOut(c *gin.Context) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "PayOut", c)
	ret0, _ := ret[0].(error)
	return ret0
}

// PayOut indicates an expected call of PayOut.
func (mr *MockGinJusanServerMockRecorder) PayOut(c interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "PayOut", reflect.TypeOf((*MockGinJusanServer)(nil).PayOut), c)
}

// PayOutByPhone mocks base method.
func (m *MockGinJusanServer) PayOutByPhone(c *gin.Context) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "PayOutByPhone", c)
	ret0, _ := ret[0].(error)
	return ret0
}

// PayOutByPhone indicates an expected call of PayOutByPhone.
func (mr *MockGinJusanServerMockRecorder) PayOutByPhone(c interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "PayOutByPhone", reflect.TypeOf((*MockGinJusanServer)(nil).PayOutByPhone), c)
}

// Refund mocks base method.
func (m *MockGinJusanServer) Refund(c *gin.Context) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Refund", c)
	ret0, _ := ret[0].(error)
	return ret0
}

// Refund indicates an expected call of Refund.
func (mr *MockGinJusanServerMockRecorder) Refund(c interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Refund", reflect.TypeOf((*MockGinJusanServer)(nil).Refund), c)
}

// ResolveVisaAlias mocks base method.
func (m *MockGinJusanServer) ResolveVisaAlias(c *gin.Context) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ResolveVisaAlias", c)
	ret0, _ := ret[0].(error)
	return ret0
}

// ResolveVisaAlias indicates an expected call of ResolveVisaAlias.
func (mr *MockGinJusanServerMockRecorder) ResolveVisaAlias(c interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ResolveVisaAlias", reflect.TypeOf((*MockGinJusanServer)(nil).ResolveVisaAlias), c)
}

// ThreeDSConfirm mocks base method.
func (m *MockGinJusanServer) ThreeDSConfirm(c *gin.Context) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ThreeDSConfirm", c)
	ret0, _ := ret[0].(error)
	return ret0
}

// ThreeDSConfirm indicates an expected call of ThreeDSConfirm.
func (mr *MockGinJusanServerMockRecorder) ThreeDSConfirm(c interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ThreeDSConfirm", reflect.TypeOf((*MockGinJusanServer)(nil).ThreeDSConfirm), c)
}

// ThreeDSResume mocks base method.
func (m *MockGinJusanServer) ThreeDSResume(c *gin.Context) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ThreeDSResume", c)
	ret0, _ := ret[0].(error)
	return ret0
}

// ThreeDSResume indicates an expected call of ThreeDSResume.
func (mr *MockGinJusanServerMockRecorder) ThreeDSResume(c interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ThreeDSResume", reflect.TypeOf((*MockGinJusanServer)(nil).ThreeDSResume), c)
}

// TwoStagePayIn mocks base method.
func (m *MockGinJusanServer) TwoStagePayIn(c *gin.Context) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "TwoStagePayIn", c)
	ret0, _ := ret[0].(error)
	return ret0
}

// TwoStagePayIn indicates an expected call of TwoStagePayIn.
func (mr *MockGinJusanServerMockRecorder) TwoStagePayIn(c interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "TwoStagePayIn", reflect.TypeOf((*MockGinJusanServer)(nil).TwoStagePayIn), c)
}
