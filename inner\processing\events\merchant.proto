syntax = "proto3";

option go_package = "git.local/sensitive/innerpb/processing/events";

import "mvp/proto/events.proto";

message ActivateMerchant {
  option (mvp.events.is_event) = true;
  uint64 id = 1;
}

message CreateProcessingMerchant {
  uint64 merchant_id = 1;
  uint64 project_id = 2;
}

message CreateSetting {
  string producer = 1;
  uint64 merchant_id = 2;
  uint64 project_id = 3;
}

message DeactivateMerchant {
  uint64 id = 1;
}




