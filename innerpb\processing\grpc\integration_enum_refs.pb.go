// Code generated by protoc-gen-go-mvp. DO NOT EDIT.
// versions:
// - protoc-gen-go-mvp v2.0.1
// created by mvp authors

package grpc

func (x IntegrationError) Message() string {
	switch x {
	case IntegrationError_None:
		return "None"
	case IntegrationError_UndefinedError:
		return "undefined error"
	case IntegrationError_ThreeDSAuthFailed:
		return "3DS authentication failed"
	case IntegrationError_InvalidCard:
		return "invalid card"
	case IntegrationError_ExceedsAmountLimit:
		return "exceeds amount limit"
	case IntegrationError_ExceedsTransactionFrequencyLimit:
		return "exceeds transaction frequency limit"
	case IntegrationError_TransactionDeclinedByIssuer:
		return "transaction declined by an issuer"
	case IntegrationError_TransactionDeclinedByAcquirer:
		return "transaction declined by an acquirer"
	case IntegrationError_UnavailableIssuer:
		return "unavailable issuer"
	case IntegrationError_UnavailableAcquirer:
		return "unavailable acquirer"
	case IntegrationError_PaymentForbiddenForMerchant:
		return "payment is forbidden for the merchant"
	case IntegrationError_StolenCard:
		return "stolen card"
	case IntegrationError_BlockedCard:
		return "blocked card"
	case IntegrationError_NonExistentCard:
		return "non existent card"
	case IntegrationError_LostCard:
		return "lost card"
	case IntegrationError_CardHasExpired:
		return "card has expired"
	case IntegrationError_IncorrectCVVCVC:
		return "incorrect CVV/CVC"
	case IntegrationError_IncorrectCardNumber:
		return "incorrect card number"
	case IntegrationError_IncorrectCardExpDate:
		return "incorrect card expiration date"
	case IntegrationError_InsufficientFunds:
		return "insufficient funds"
	case IntegrationError_SuspiciousClient:
		return "suspicious client"
	case IntegrationError_UserDidNotPay:
		return "user did not pay"
	case IntegrationError_InvalidThreeDSecureParameters:
		return "invalid threeD secure parameters"
	default:
		return "None"
	}
}

// Created reference to IntegrationError

//	|	IntegrationError                                 	|	Message                                	|
//	|	IntegrationError_None                            	|	"None"                                 	|
//	|	IntegrationError_IncorrectCardExpDate            	|	"incorrect card expiration date"       	|
//	|	IntegrationError_InvalidCard                     	|	"invalid card"                         	|
//	|	IntegrationError_ExceedsAmountLimit              	|	"exceeds amount limit"                 	|
//	|	IntegrationError_TransactionDeclinedByIssuer     	|	"transaction declined by an issuer"    	|
//	|	IntegrationError_UnavailableIssuer               	|	"unavailable issuer"                   	|
//	|	IntegrationError_IncorrectCardNumber             	|	"incorrect card number"                	|
//	|	IntegrationError_PaymentForbiddenForMerchant     	|	"payment is forbidden for the merchant"	|
//	|	IntegrationError_TransactionDeclinedByAcquirer   	|	"transaction declined by an acquirer"  	|
//	|	IntegrationError_UnavailableAcquirer             	|	"unavailable acquirer"                 	|
//	|	IntegrationError_IncorrectCVVCVC                 	|	"incorrect CVV/CVC"                    	|
//	|	IntegrationError_ThreeDSAuthFailed               	|	"3DS authentication failed"            	|
//	|	IntegrationError_ExceedsTransactionFrequencyLimit	|	"exceeds transaction frequency limit"  	|
//	|	IntegrationError_LostCard                        	|	"lost card"                            	|
//	|	IntegrationError_SuspiciousClient                	|	"suspicious client"                    	|
//	|	IntegrationError_NonExistentCard                 	|	"non existent card"                    	|
//	|	IntegrationError_CardHasExpired                  	|	"card has expired"                     	|
//	|	IntegrationError_InsufficientFunds               	|	"insufficient funds"                   	|
//	|	IntegrationError_UndefinedError                  	|	"undefined error"                      	|
//	|	IntegrationError_StolenCard                      	|	"stolen card"                          	|
//	|	IntegrationError_BlockedCard                     	|	"blocked card"                         	|
//	|	IntegrationError_UserDidNotPay                   	|	"user did not pay"                     	|
//	|	IntegrationError_InvalidThreeDSecureParameters   	|	"invalid threeD secure parameters"     	|

var SliceIntegrationErrorRefs *sliceIntegrationErrorRefs

type sliceIntegrationErrorRefs struct{}

func (*sliceIntegrationErrorRefs) Message(slice ...IntegrationError) []string {
	var result []string
	for _, val := range slice {
		result = append(result, val.Message())
	}

	return result
}
