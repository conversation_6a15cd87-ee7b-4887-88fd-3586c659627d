// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.5
// 	protoc        v5.29.3
// source: inner/processing/grpc/transaction_refund.proto

package grpc

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type TransactionRequestDataV1 struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	TransactionId *uint64                `protobuf:"varint,1,opt,name=transaction_id,json=transactionId" json:"transaction_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *TransactionRequestDataV1) Reset() {
	*x = TransactionRequestDataV1{}
	mi := &file_inner_processing_grpc_transaction_refund_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *TransactionRequestDataV1) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TransactionRequestDataV1) ProtoMessage() {}

func (x *TransactionRequestDataV1) ProtoReflect() protoreflect.Message {
	mi := &file_inner_processing_grpc_transaction_refund_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TransactionRequestDataV1.ProtoReflect.Descriptor instead.
func (*TransactionRequestDataV1) Descriptor() ([]byte, []int) {
	return file_inner_processing_grpc_transaction_refund_proto_rawDescGZIP(), []int{0}
}

func (x *TransactionRequestDataV1) GetTransactionId() uint64 {
	if x != nil && x.TransactionId != nil {
		return *x.TransactionId
	}
	return 0
}

type TransactionResponseDataV1 struct {
	state         protoimpl.MessageState         `protogen:"open.v1"`
	Response      []*TransactionRefundResponseV1 `protobuf:"bytes,1,rep,name=response" json:"response,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *TransactionResponseDataV1) Reset() {
	*x = TransactionResponseDataV1{}
	mi := &file_inner_processing_grpc_transaction_refund_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *TransactionResponseDataV1) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TransactionResponseDataV1) ProtoMessage() {}

func (x *TransactionResponseDataV1) ProtoReflect() protoreflect.Message {
	mi := &file_inner_processing_grpc_transaction_refund_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TransactionResponseDataV1.ProtoReflect.Descriptor instead.
func (*TransactionResponseDataV1) Descriptor() ([]byte, []int) {
	return file_inner_processing_grpc_transaction_refund_proto_rawDescGZIP(), []int{1}
}

func (x *TransactionResponseDataV1) GetResponse() []*TransactionRefundResponseV1 {
	if x != nil {
		return x.Response
	}
	return nil
}

type TransactionRefundResponseV1 struct {
	state           protoimpl.MessageState `protogen:"open.v1"`
	TransactionId   *uint64                `protobuf:"varint,1,opt,name=transaction_id,json=transactionId" json:"transaction_id,omitempty"`
	ReasonName      *string                `protobuf:"bytes,2,opt,name=reason_name,json=reasonName" json:"reason_name,omitempty"`
	Status          *string                `protobuf:"bytes,3,opt,name=status" json:"status,omitempty"`
	Amount          *float64               `protobuf:"fixed64,4,opt,name=amount" json:"amount,omitempty"`
	BankReferenceId *string                `protobuf:"bytes,5,opt,name=bank_reference_id,json=bankReferenceId" json:"bank_reference_id,omitempty"`
	BankMessage     *string                `protobuf:"bytes,6,opt,name=bank_message,json=bankMessage" json:"bank_message,omitempty"`
	CreatedAt       *timestamppb.Timestamp `protobuf:"bytes,7,opt,name=created_at,json=createdAt" json:"created_at,omitempty"`
	unknownFields   protoimpl.UnknownFields
	sizeCache       protoimpl.SizeCache
}

func (x *TransactionRefundResponseV1) Reset() {
	*x = TransactionRefundResponseV1{}
	mi := &file_inner_processing_grpc_transaction_refund_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *TransactionRefundResponseV1) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TransactionRefundResponseV1) ProtoMessage() {}

func (x *TransactionRefundResponseV1) ProtoReflect() protoreflect.Message {
	mi := &file_inner_processing_grpc_transaction_refund_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TransactionRefundResponseV1.ProtoReflect.Descriptor instead.
func (*TransactionRefundResponseV1) Descriptor() ([]byte, []int) {
	return file_inner_processing_grpc_transaction_refund_proto_rawDescGZIP(), []int{2}
}

func (x *TransactionRefundResponseV1) GetTransactionId() uint64 {
	if x != nil && x.TransactionId != nil {
		return *x.TransactionId
	}
	return 0
}

func (x *TransactionRefundResponseV1) GetReasonName() string {
	if x != nil && x.ReasonName != nil {
		return *x.ReasonName
	}
	return ""
}

func (x *TransactionRefundResponseV1) GetStatus() string {
	if x != nil && x.Status != nil {
		return *x.Status
	}
	return ""
}

func (x *TransactionRefundResponseV1) GetAmount() float64 {
	if x != nil && x.Amount != nil {
		return *x.Amount
	}
	return 0
}

func (x *TransactionRefundResponseV1) GetBankReferenceId() string {
	if x != nil && x.BankReferenceId != nil {
		return *x.BankReferenceId
	}
	return ""
}

func (x *TransactionRefundResponseV1) GetBankMessage() string {
	if x != nil && x.BankMessage != nil {
		return *x.BankMessage
	}
	return ""
}

func (x *TransactionRefundResponseV1) GetCreatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.CreatedAt
	}
	return nil
}

var File_inner_processing_grpc_transaction_refund_proto protoreflect.FileDescriptor

var file_inner_processing_grpc_transaction_refund_proto_rawDesc = string([]byte{
	0x0a, 0x2e, 0x69, 0x6e, 0x6e, 0x65, 0x72, 0x2f, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69,
	0x6e, 0x67, 0x2f, 0x67, 0x72, 0x70, 0x63, 0x2f, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74,
	0x69, 0x6f, 0x6e, 0x5f, 0x72, 0x65, 0x66, 0x75, 0x6e, 0x64, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x12, 0x29, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x2e, 0x74, 0x72, 0x61,
	0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63,
	0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x72, 0x65, 0x66, 0x75, 0x6e, 0x64, 0x1a, 0x1f, 0x67, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x74, 0x69, 0x6d,
	0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x41, 0x0a, 0x18,
	0x54, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x44, 0x61, 0x74, 0x61, 0x56, 0x31, 0x12, 0x25, 0x0a, 0x0e, 0x74, 0x72, 0x61, 0x6e,
	0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04,
	0x52, 0x0d, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x22,
	0x7f, 0x0a, 0x19, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x44, 0x61, 0x74, 0x61, 0x56, 0x31, 0x12, 0x62, 0x0a, 0x08,
	0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x46,
	0x2e, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x2e, 0x74, 0x72, 0x61, 0x6e,
	0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74,
	0x69, 0x6f, 0x6e, 0x5f, 0x72, 0x65, 0x66, 0x75, 0x6e, 0x64, 0x2e, 0x54, 0x72, 0x61, 0x6e, 0x73,
	0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x66, 0x75, 0x6e, 0x64, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x56, 0x31, 0x52, 0x08, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x22, 0x9f, 0x02, 0x0a, 0x1b, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e,
	0x52, 0x65, 0x66, 0x75, 0x6e, 0x64, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x56, 0x31,
	0x12, 0x25, 0x0a, 0x0e, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x5f,
	0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x0d, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61,
	0x63, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x12, 0x1f, 0x0a, 0x0b, 0x72, 0x65, 0x61, 0x73, 0x6f,
	0x6e, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x72, 0x65,
	0x61, 0x73, 0x6f, 0x6e, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x12, 0x16, 0x0a, 0x06, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x01,
	0x52, 0x06, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x2a, 0x0a, 0x11, 0x62, 0x61, 0x6e, 0x6b,
	0x5f, 0x72, 0x65, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x05, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0f, 0x62, 0x61, 0x6e, 0x6b, 0x52, 0x65, 0x66, 0x65, 0x72, 0x65, 0x6e,
	0x63, 0x65, 0x49, 0x64, 0x12, 0x21, 0x0a, 0x0c, 0x62, 0x61, 0x6e, 0x6b, 0x5f, 0x6d, 0x65, 0x73,
	0x73, 0x61, 0x67, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x62, 0x61, 0x6e, 0x6b,
	0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x39, 0x0a, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74,
	0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69,
	0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x09, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64,
	0x41, 0x74, 0x32, 0xae, 0x01, 0x0a, 0x06, 0x52, 0x65, 0x66, 0x75, 0x6e, 0x64, 0x12, 0xa3, 0x01,
	0x0a, 0x14, 0x47, 0x65, 0x74, 0x42, 0x79, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69,
	0x6f, 0x6e, 0x49, 0x44, 0x56, 0x31, 0x12, 0x43, 0x2e, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73,
	0x69, 0x6e, 0x67, 0x2e, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x2e,
	0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x72, 0x65, 0x66, 0x75,
	0x6e, 0x64, 0x2e, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x44, 0x61, 0x74, 0x61, 0x56, 0x31, 0x1a, 0x44, 0x2e, 0x70, 0x72,
	0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x2e, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63,
	0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e,
	0x5f, 0x72, 0x65, 0x66, 0x75, 0x6e, 0x64, 0x2e, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74,
	0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x44, 0x61, 0x74, 0x61, 0x56,
	0x31, 0x22, 0x00, 0x42, 0x2d, 0x5a, 0x2b, 0x67, 0x69, 0x74, 0x2e, 0x6c, 0x6f, 0x63, 0x61, 0x6c,
	0x2f, 0x73, 0x65, 0x6e, 0x73, 0x69, 0x74, 0x69, 0x76, 0x65, 0x2f, 0x69, 0x6e, 0x6e, 0x65, 0x72,
	0x70, 0x62, 0x2f, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x2f, 0x67, 0x72,
	0x70, 0x63, 0x62, 0x08, 0x65, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x70, 0xe8, 0x07,
})

var (
	file_inner_processing_grpc_transaction_refund_proto_rawDescOnce sync.Once
	file_inner_processing_grpc_transaction_refund_proto_rawDescData []byte
)

func file_inner_processing_grpc_transaction_refund_proto_rawDescGZIP() []byte {
	file_inner_processing_grpc_transaction_refund_proto_rawDescOnce.Do(func() {
		file_inner_processing_grpc_transaction_refund_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_inner_processing_grpc_transaction_refund_proto_rawDesc), len(file_inner_processing_grpc_transaction_refund_proto_rawDesc)))
	})
	return file_inner_processing_grpc_transaction_refund_proto_rawDescData
}

var file_inner_processing_grpc_transaction_refund_proto_msgTypes = make([]protoimpl.MessageInfo, 3)
var file_inner_processing_grpc_transaction_refund_proto_goTypes = []any{
	(*TransactionRequestDataV1)(nil),    // 0: processing.transaction.transaction_refund.TransactionRequestDataV1
	(*TransactionResponseDataV1)(nil),   // 1: processing.transaction.transaction_refund.TransactionResponseDataV1
	(*TransactionRefundResponseV1)(nil), // 2: processing.transaction.transaction_refund.TransactionRefundResponseV1
	(*timestamppb.Timestamp)(nil),       // 3: google.protobuf.Timestamp
}
var file_inner_processing_grpc_transaction_refund_proto_depIdxs = []int32{
	2, // 0: processing.transaction.transaction_refund.TransactionResponseDataV1.response:type_name -> processing.transaction.transaction_refund.TransactionRefundResponseV1
	3, // 1: processing.transaction.transaction_refund.TransactionRefundResponseV1.created_at:type_name -> google.protobuf.Timestamp
	0, // 2: processing.transaction.transaction_refund.Refund.GetByTransactionIDV1:input_type -> processing.transaction.transaction_refund.TransactionRequestDataV1
	1, // 3: processing.transaction.transaction_refund.Refund.GetByTransactionIDV1:output_type -> processing.transaction.transaction_refund.TransactionResponseDataV1
	3, // [3:4] is the sub-list for method output_type
	2, // [2:3] is the sub-list for method input_type
	2, // [2:2] is the sub-list for extension type_name
	2, // [2:2] is the sub-list for extension extendee
	0, // [0:2] is the sub-list for field type_name
}

func init() { file_inner_processing_grpc_transaction_refund_proto_init() }
func file_inner_processing_grpc_transaction_refund_proto_init() {
	if File_inner_processing_grpc_transaction_refund_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_inner_processing_grpc_transaction_refund_proto_rawDesc), len(file_inner_processing_grpc_transaction_refund_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   3,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_inner_processing_grpc_transaction_refund_proto_goTypes,
		DependencyIndexes: file_inner_processing_grpc_transaction_refund_proto_depIdxs,
		MessageInfos:      file_inner_processing_grpc_transaction_refund_proto_msgTypes,
	}.Build()
	File_inner_processing_grpc_transaction_refund_proto = out.File
	file_inner_processing_grpc_transaction_refund_proto_goTypes = nil
	file_inner_processing_grpc_transaction_refund_proto_depIdxs = nil
}
