edition = "2023";

package eosi;

option go_package = "git.local/sensitive/innerpb/processing/rest";

import "mvp/proto/rest.proto";

service eosi {
  option(mvp.rest_service_options) = {
    hosts :[
      {
        stand: "local",
        base_uri: "https://eosi.kz/test-payment-api"
      },
      {
        stand: "dev",
        base_uri: "https://eosi.kz/test-payment-api"
      },
      {
        stand: "test",
        base_uri: "https://eosi.kz/test-payment-api"
      },
      {
        stand: "stage",
        base_uri: "https://eosi.kz/test-payment-api"
      },
      {
        stand: "prod",
        base_uri: "https://eosi.kz/test-payment-api"
      },
      {
        stand: "sandbox",
        base_uri: "https://eosi.kz/test-payment-api"
      }
    ]
  };

  rpc GetPaymentOrder(EmptyEosi) returns (GetPaymentOrderResponseEosi) {
    option(mvp.rest_method_options) = {
      method: Get,
      authorization: None,
      path: "/${bankCode}/payment-orders/${date}",
      max_request_timeout: 60,
      request_marshal: JSON,
      response_unmarshal: JSON
    };
  }
}

message EmptyEosi {}

message GetPaymentOrderResponseBodyEosi {
  int64 code = 1;
  string message = 2;
  repeated Result result = 3;

  message Result {
    string date = 1;
    string bic = 2;
    string iban = 3;
    string idn = 4;
    string kbe = 5;
    string knp = 6;
    string assign = 7;
    string name = 8;
    double amount = 9;
  }
}

message GetPaymentOrderResponseEosi {
  GetPaymentOrderResponseBodyEosi body = 1;
}
