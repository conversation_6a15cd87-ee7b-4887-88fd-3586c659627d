// Code generated by protoc-gen-go-mvp. DO NOT EDIT.
// versions:
// - protoc-gen-go-mvp v2.0.1
// created by mvp authors

package rest

import (
	bytes "bytes"
	context "context"
	cntx "git.local/sensitive/mvp/pkg/cntx"
	wraps "git.local/sensitive/mvp/pkg/convert/wraps"
	doc "git.local/sensitive/mvp/pkg/doc"
	marshaller "git.local/sensitive/mvp/pkg/marshaller"
	uuid "github.com/google/uuid"
	ctxzap "github.com/grpc-ecosystem/go-grpc-middleware/logging/zap/ctxzap"
	zap "go.uber.org/zap"
	io "io"
	http "net/http"
	url "net/url"
	time "time"
)

var restAlatauCityBankPayInBaseURL = map[string]string{
	"local":   "https://jpay-test.alataucitybank.kz",
	"dev":     "https://jpay-test.alataucitybank.kz",
	"test":    "https://jpay.alataucitybank.kz",
	"stage":   "https://jpay.alataucitybank.kz",
	"prod":    "https://jpay-test.alataucitybank.kz",
	"sandbox": "https://jpay-test.alataucitybank.kz",
}

type RestAlatauCityBankPayInServerConfig struct {
	DocFunc doc.Func

	HooksProcessPayIn       *File_inner_processing_rest_alatau_city_payin_proto_AlatauCityBankPayIn_ProcessPayIn_Hooks
	HooksProcessNoAcceptPay *File_inner_processing_rest_alatau_city_payin_proto_AlatauCityBankPayIn_ProcessNoAcceptPay_Hooks
	HooksGetStatus          *File_inner_processing_rest_alatau_city_payin_proto_AlatauCityBankPayIn_GetStatus_Hooks
	HooksConfirmThreeDS     *File_inner_processing_rest_alatau_city_payin_proto_AlatauCityBankPayIn_ConfirmThreeDS_Hooks
	HooksResumeThreeDS      *File_inner_processing_rest_alatau_city_payin_proto_AlatauCityBankPayIn_ResumeThreeDS_Hooks
	HooksSendForm           *File_inner_processing_rest_alatau_city_payin_proto_AlatauCityBankPayIn_SendForm_Hooks
	HooksCancelPayment      *File_inner_processing_rest_alatau_city_payin_proto_AlatauCityBankPayIn_CancelPayment_Hooks
	HooksRegisterToken      *File_inner_processing_rest_alatau_city_payin_proto_AlatauCityBankPayIn_RegisterToken_Hooks
	HooksCharge             *File_inner_processing_rest_alatau_city_payin_proto_AlatauCityBankPayIn_Charge_Hooks
}

func (config *RestAlatauCityBankPayInServerConfig) Build(stand string) *restAlatauCityBankPayInServer {
	if config == nil {
		config = &RestAlatauCityBankPayInServerConfig{}
	}

	if config.DocFunc == nil {
		config.DocFunc = func(ctx context.Context, doc *doc.Struct) {}
	}

	return &restAlatauCityBankPayInServer{
		baseUrl: restAlatauCityBankPayInBaseURL[stand],
		docFunc: config.DocFunc,

		hooksProcessPayIn:       PrepareFile_inner_processing_rest_alatau_city_payin_proto_AlatauCityBankPayIn_ProcessPayIn_Hooks(config.HooksProcessPayIn),
		hooksProcessNoAcceptPay: PrepareFile_inner_processing_rest_alatau_city_payin_proto_AlatauCityBankPayIn_ProcessNoAcceptPay_Hooks(config.HooksProcessNoAcceptPay),
		hooksGetStatus:          PrepareFile_inner_processing_rest_alatau_city_payin_proto_AlatauCityBankPayIn_GetStatus_Hooks(config.HooksGetStatus),
		hooksConfirmThreeDS:     PrepareFile_inner_processing_rest_alatau_city_payin_proto_AlatauCityBankPayIn_ConfirmThreeDS_Hooks(config.HooksConfirmThreeDS),
		hooksResumeThreeDS:      PrepareFile_inner_processing_rest_alatau_city_payin_proto_AlatauCityBankPayIn_ResumeThreeDS_Hooks(config.HooksResumeThreeDS),
		hooksSendForm:           PrepareFile_inner_processing_rest_alatau_city_payin_proto_AlatauCityBankPayIn_SendForm_Hooks(config.HooksSendForm),
		hooksCancelPayment:      PrepareFile_inner_processing_rest_alatau_city_payin_proto_AlatauCityBankPayIn_CancelPayment_Hooks(config.HooksCancelPayment),
		hooksRegisterToken:      PrepareFile_inner_processing_rest_alatau_city_payin_proto_AlatauCityBankPayIn_RegisterToken_Hooks(config.HooksRegisterToken),
		hooksCharge:             PrepareFile_inner_processing_rest_alatau_city_payin_proto_AlatauCityBankPayIn_Charge_Hooks(config.HooksCharge),
	}
}

type restAlatauCityBankPayInServer struct {
	baseUrl string
	docFunc doc.Func

	hooksProcessPayIn       *File_inner_processing_rest_alatau_city_payin_proto_AlatauCityBankPayIn_ProcessPayIn_Hooks
	hooksProcessNoAcceptPay *File_inner_processing_rest_alatau_city_payin_proto_AlatauCityBankPayIn_ProcessNoAcceptPay_Hooks
	hooksGetStatus          *File_inner_processing_rest_alatau_city_payin_proto_AlatauCityBankPayIn_GetStatus_Hooks
	hooksConfirmThreeDS     *File_inner_processing_rest_alatau_city_payin_proto_AlatauCityBankPayIn_ConfirmThreeDS_Hooks
	hooksResumeThreeDS      *File_inner_processing_rest_alatau_city_payin_proto_AlatauCityBankPayIn_ResumeThreeDS_Hooks
	hooksSendForm           *File_inner_processing_rest_alatau_city_payin_proto_AlatauCityBankPayIn_SendForm_Hooks
	hooksCancelPayment      *File_inner_processing_rest_alatau_city_payin_proto_AlatauCityBankPayIn_CancelPayment_Hooks
	hooksRegisterToken      *File_inner_processing_rest_alatau_city_payin_proto_AlatauCityBankPayIn_RegisterToken_Hooks
	hooksCharge             *File_inner_processing_rest_alatau_city_payin_proto_AlatauCityBankPayIn_Charge_Hooks
}

func NewRestAlatauCityBankPayInServer(
	stand string,
	config *RestAlatauCityBankPayInServerConfig,
) RestAlatauCityBankPayIn {
	return config.Build(stand)
}

//go:generate mockgen -destination=./restmock/alatau_city_payin_rest.pb.go -package=restmock -source=alatau_city_payin_rest.pb.go RestAlatauCityBankPayIn
type RestAlatauCityBankPayIn interface {
	ProcessPayIn(
		ctx context.Context,
		request *AlatauCityProcessPayInRequest,
	) (
		response *AlatauCityBankPayIn_ProcessPayIn_Response,
		err error,
	)
	ProcessNoAcceptPay(
		ctx context.Context,
		request *AlatauCityProcessNoAcceptPayRequest,
	) (
		response *AlatauCityBankPayIn_ProcessNoAcceptPay_Response,
		err error,
	)
	GetStatus(
		ctx context.Context,
		request *AlatauCityGetStatusPayInRequest,
	) (
		response *AlatauCityBankPayIn_GetStatus_Response,
		err error,
	)
	ConfirmThreeDS(
		ctx context.Context,
		request *AlatauCityConfirmThreeDSRequest,
	) (
		response *AlatauCityBankPayIn_ConfirmThreeDS_Response,
		err error,
	)
	ResumeThreeDS(
		ctx context.Context,
		request *AlatauCityResumeThreeDSRequest,
	) (
		response *AlatauCityBankPayIn_ResumeThreeDS_Response,
		err error,
	)
	SendForm(
		ctx context.Context,
		request *AlatauCitySendFormRequest,
	) (
		response *AlatauCityBankPayIn_SendForm_Response,
		err error,
	)
	CancelPayment(
		ctx context.Context,
		request *AlatauCityCancelPaymentRequest,
	) (
		response *AlatauCityBankPayIn_CancelPayment_Response,
		err error,
	)
	RegisterToken(
		ctx context.Context,
		request *AlatauCityRegisterTokenRequest,
	) (
		response *AlatauCityBankPayIn_RegisterToken_Response,
		err error,
	)
	Charge(
		ctx context.Context,
		request *AlatauCityChargeRequest,
	) (
		response *AlatauCityBankPayIn_Charge_Response,
		err error,
	)
}

type File_inner_processing_rest_alatau_city_payin_proto_AlatauCityBankPayIn_ProcessPayIn int32

const (
	File_inner_processing_rest_alatau_city_payin_proto_AlatauCityBankPayIn_ProcessPayIn_OutOfScope File_inner_processing_rest_alatau_city_payin_proto_AlatauCityBankPayIn_ProcessPayIn = iota
	File_inner_processing_rest_alatau_city_payin_proto_AlatauCityBankPayIn_ProcessPayIn_Response
)

type File_inner_processing_rest_alatau_city_payin_proto_AlatauCityBankPayIn_ProcessPayIn_Hooks struct {
	ValidateArguments func(
		ctx context.Context,
		request *AlatauCityProcessPayInRequest,
		response *AlatauCityBankPayIn_ProcessPayIn_Response,
		docData map[string]any,
		hooksData map[string]any,
	) error

	MutatePath func(
		ctx context.Context,
		request *AlatauCityProcessPayInRequest,
		response *AlatauCityBankPayIn_ProcessPayIn_Response,
		calculatedPath string,
		docData map[string]any,
		hooksData map[string]any,
	) (mutatePath string, err error)

	CustomMarshal func(
		ctx context.Context,
		request *AlatauCityProcessPayInRequest,
		response *AlatauCityBankPayIn_ProcessPayIn_Response,
		path string,
		docData map[string]any,
		hooksData map[string]any,
	) (body []byte, contentType string, err error)

	MutateBody func(
		ctx context.Context,
		request *AlatauCityProcessPayInRequest,
		response *AlatauCityBankPayIn_ProcessPayIn_Response,
		body []byte,
		docData map[string]any,
		hooksData map[string]any,
	) (mutatedBody []byte, err error)

	BeforeHttpRequest func(
		ctx context.Context,
		request *AlatauCityProcessPayInRequest,
		response *AlatauCityBankPayIn_ProcessPayIn_Response,
		httpRequest *http.Request,
		docData map[string]any,
		hooksData map[string]any,
	) error

	AfterHttpResponse func(
		ctx context.Context,
		request *AlatauCityProcessPayInRequest,
		httpRequest *http.Request,
		httpResponse *http.Response,
		response *AlatauCityBankPayIn_ProcessPayIn_Response,
		docData map[string]any,
		hooksData map[string]any,
	) (done bool, err error)

	CustomUnmarshal func(
		ctx context.Context,
		request *AlatauCityProcessPayInRequest,
		response *AlatauCityBankPayIn_ProcessPayIn_Response,
		rspBody []byte,
		docData map[string]any,
		hooksData map[string]any,
	) error
}

func NewFile_inner_processing_rest_alatau_city_payin_proto_AlatauCityBankPayIn_ProcessPayIn_Hooks() *File_inner_processing_rest_alatau_city_payin_proto_AlatauCityBankPayIn_ProcessPayIn_Hooks {
	return &File_inner_processing_rest_alatau_city_payin_proto_AlatauCityBankPayIn_ProcessPayIn_Hooks{
		ValidateArguments: func(
			ctx context.Context,
			request *AlatauCityProcessPayInRequest,
			response *AlatauCityBankPayIn_ProcessPayIn_Response,
			docData map[string]any,
			hooksData map[string]any,
		) error {
			return nil
		},
		MutatePath: func(
			ctx context.Context,
			request *AlatauCityProcessPayInRequest,
			response *AlatauCityBankPayIn_ProcessPayIn_Response,
			calculatedPath string,
			docData map[string]any,
			hooksData map[string]any,
		) (mutatePath string, err error) {
			return calculatedPath, nil
		},
		MutateBody: func(
			ctx context.Context,
			request *AlatauCityProcessPayInRequest,
			response *AlatauCityBankPayIn_ProcessPayIn_Response,
			body []byte,
			docData map[string]any,
			hooksData map[string]any,
		) (mutatedBody []byte, err error) {
			return body, nil
		},
		BeforeHttpRequest: func(
			ctx context.Context,
			request *AlatauCityProcessPayInRequest,
			response *AlatauCityBankPayIn_ProcessPayIn_Response,
			httpRequest *http.Request,
			docData map[string]any,
			hooksData map[string]any,
		) error {
			return nil
		},
		AfterHttpResponse: func(
			ctx context.Context,
			request *AlatauCityProcessPayInRequest,
			httpRequest *http.Request,
			httpResponse *http.Response,
			response *AlatauCityBankPayIn_ProcessPayIn_Response,
			docData map[string]any,
			hooksData map[string]any,
		) (done bool, err error) {
			return false, nil
		},
	}
}

func PrepareFile_inner_processing_rest_alatau_city_payin_proto_AlatauCityBankPayIn_ProcessPayIn_Hooks(
	in *File_inner_processing_rest_alatau_city_payin_proto_AlatauCityBankPayIn_ProcessPayIn_Hooks,
) *File_inner_processing_rest_alatau_city_payin_proto_AlatauCityBankPayIn_ProcessPayIn_Hooks {
	defaultHook := NewFile_inner_processing_rest_alatau_city_payin_proto_AlatauCityBankPayIn_ProcessPayIn_Hooks()
	if in == nil {
		return defaultHook
	}

	if in.ValidateArguments == nil {
		in.ValidateArguments = defaultHook.ValidateArguments
	}

	if in.MutatePath == nil {
		in.MutatePath = defaultHook.MutatePath
	}
	if in.MutateBody == nil {
		in.MutateBody = defaultHook.MutateBody
	}

	if in.BeforeHttpRequest == nil {
		in.BeforeHttpRequest = defaultHook.BeforeHttpRequest
	}

	if in.AfterHttpResponse == nil {
		in.AfterHttpResponse = defaultHook.AfterHttpResponse
	}

	return in
}

type AlatauCityBankPayIn_ProcessPayIn_Response struct {
	Request        *AlatauCityProcessPayInRequest
	Response       *AlatauCityProcessPayInResponse
	ResponseOutput File_inner_processing_rest_alatau_city_payin_proto_AlatauCityBankPayIn_ProcessPayIn
	Error          error
	Doc            *doc.Struct
}

func (s *restAlatauCityBankPayInServer) ProcessPayIn(
	ctx context.Context,
	request *AlatauCityProcessPayInRequest,
) (
	response *AlatauCityBankPayIn_ProcessPayIn_Response,
	err error,
) {
	label := cntx.Begin(ctx, "AlatauCityBankPayInServer_ProcessPayIn")
	defer cntx.End(ctx, label)

	response = &AlatauCityBankPayIn_ProcessPayIn_Response{
		Request:        request,
		Response:       &AlatauCityProcessPayInResponse{},
		ResponseOutput: File_inner_processing_rest_alatau_city_payin_proto_AlatauCityBankPayIn_ProcessPayIn_OutOfScope,
		Error:          nil,
		Doc: &doc.Struct{
			UUID:          uuid.New(),
			CreatedAt:     time.Now(),
			CreatedAtNano: time.Now().UnixNano(),
			FuncName:      "AlatauCityBankPayInServer_ProcessPayIn",
			Label:         label,
			Logs:          make(map[string]any),
			Arguments: map[string]any{
				"clear_path": "/ecom/api",
			},
			Request:         file_inner_processing_rest_alatau_city_payin_proto_message_AlatauCityProcessPayInRequestToGo(label, request),
			RequestMethod:   "Post",
			RequestHeaders:  make(map[string][]string),
			ResponseHeaders: make(map[string][]string),
			HooksData:       make(map[string]any),
		},
	}
	defer func() { s.docFunc(ctx, response.Doc) }()

	defer func() {
		if err != nil {
			ctxzap.AddFields(ctx, zap.Any(label+"error", err))
			response.Error = err
			response.Doc.Error = err

			return
		}

		ctxzap.AddFields(
			ctx,
			file_inner_processing_rest_alatau_city_payin_proto_message_AlatauCityProcessPayInResponseToZap(label+"response.Response", response.Response),
			zap.Any(label+"response.ResponseOutput", response.ResponseOutput),
		)

		response.Doc.Response = file_inner_processing_rest_alatau_city_payin_proto_message_AlatauCityProcessPayInResponseToGo(label, response.Response)
		response.Doc.ResponseOutput = int32(response.ResponseOutput)
	}()

	ctxzap.AddFields(
		ctx,
		file_inner_processing_rest_alatau_city_payin_proto_message_AlatauCityProcessPayInRequestToZap(label+"response.Request", response.Request),
		zap.Any(label+"clear_path", "/ecom/api"),
	)

	if err = s.hooksProcessPayIn.ValidateArguments(
		ctx,
		request,
		response,
		response.Doc.Logs,
		response.Doc.HooksData,
	); err != nil {
		return
	}

	path := s.baseUrl + "/ecom/api"

	response.Doc.RequestURI = path

	if path, err = s.hooksProcessPayIn.MutatePath(
		ctx,
		request,
		response,
		path,
		response.Doc.Logs,
		response.Doc.HooksData,
	); err != nil {
		return
	}

	response.Doc.MutatedRequestURI = path

	var contentType string

	var requestBody []byte
	if s.hooksProcessPayIn.CustomMarshal != nil {
		if requestBody, contentType, err = s.hooksProcessPayIn.CustomMarshal(
			ctx,
			request,
			response,
			path,
			response.Doc.Logs,
			response.Doc.HooksData,
		); err != nil {
			return
		}
	} else {
		requestForm := url.Values{}
		if request.GetBody().AMOUNT != nil {
			requestForm.Set("AMOUNT", request.GetBody().GetAMOUNT())
		}

		if request.GetBody().BACKREF != nil {
			requestForm.Set("BACKREF", request.GetBody().GetBACKREF())
		}

		if request.GetBody().CLIENT_ID != nil {
			requestForm.Set("CLIENT_ID", request.GetBody().GetCLIENT_ID())
		}

		if request.GetBody().CURRENCY != nil {
			requestForm.Set("CURRENCY", request.GetBody().GetCURRENCY())
		}

		if request.GetBody().DESC != nil {
			requestForm.Set("DESC", request.GetBody().GetDESC())
		}

		if request.GetBody().DESC_ORDER != nil {
			requestForm.Set("DESC_ORDER", request.GetBody().GetDESC_ORDER())
		}

		if request.GetBody().EMAIL != nil {
			requestForm.Set("EMAIL", request.GetBody().GetEMAIL())
		}

		if request.GetBody().EXT_MPI_ECI != nil {
			requestForm.Set("EXT_MPI_ECI", request.GetBody().GetEXT_MPI_ECI())
		}

		if request.GetBody().INT_REF != nil {
			requestForm.Set("INT_REF", request.GetBody().GetINT_REF())
		}

		if request.GetBody().LANGUAGE != nil {
			requestForm.Set("LANGUAGE", request.GetBody().GetLANGUAGE())
		}

		if request.GetBody().MERCHANT != nil {
			requestForm.Set("MERCHANT", request.GetBody().GetMERCHANT())
		}

		if request.GetBody().MERCH_PAYTO_TOKEN_ID != nil {
			requestForm.Set("MERCH_PAYTO_TOKEN_ID", request.GetBody().GetMERCH_PAYTO_TOKEN_ID())
		}

		if request.GetBody().MERCH_RN_ID != nil {
			requestForm.Set("MERCH_RN_ID", request.GetBody().GetMERCH_RN_ID())
		}

		if request.GetBody().MERCH_TOKEN_ID != nil {
			requestForm.Set("MERCH_TOKEN_ID", request.GetBody().GetMERCH_TOKEN_ID())
		}

		if request.GetBody().MERCH_TRAN_STATE != nil {
			requestForm.Set("MERCH_TRAN_STATE", request.GetBody().GetMERCH_TRAN_STATE())
		}

		if request.GetBody().MK_TOKEN != nil {
			requestForm.Set("MK_TOKEN", request.GetBody().GetMK_TOKEN())
		}

		if request.GetBody().M_INFO != nil {
			requestForm.Set("M_INFO", request.GetBody().GetM_INFO())
		}

		if request.GetBody().NAME != nil {
			requestForm.Set("NAME", request.GetBody().GetNAME())
		}

		if request.GetBody().NONCE != nil {
			requestForm.Set("NONCE", request.GetBody().GetNONCE())
		}

		if request.GetBody().ORDER != nil {
			requestForm.Set("ORDER", request.GetBody().GetORDER())
		}

		if request.GetBody().PAYMENT_TO != nil {
			requestForm.Set("PAYMENT_TO", request.GetBody().GetPAYMENT_TO())
		}

		if request.GetBody().P_SIGN != nil {
			requestForm.Set("P_SIGN", request.GetBody().GetP_SIGN())
		}

		if request.GetBody().RECUR_EXP != nil {
			requestForm.Set("RECUR_EXP", request.GetBody().GetRECUR_EXP())
		}

		if request.GetBody().RECUR_FREQ != nil {
			requestForm.Set("RECUR_FREQ", request.GetBody().GetRECUR_FREQ())
		}

		if request.GetBody().RECUR_REF != nil {
			requestForm.Set("RECUR_REF", request.GetBody().GetRECUR_REF())
		}

		if request.GetBody().TAVV != nil {
			requestForm.Set("TAVV", request.GetBody().GetTAVV())
		}

		if request.GetBody().TERMINAL != nil {
			requestForm.Set("TERMINAL", request.GetBody().GetTERMINAL())
		}

		if request.GetBody().Ucaf_Authentication_Data != nil {
			requestForm.Set("Ucaf_Authentication_Data", request.GetBody().GetUcaf_Authentication_Data())
		}

		if request.GetBody().Ucaf_Flag != nil {
			requestForm.Set("Ucaf_Flag", request.GetBody().GetUcaf_Flag())
		}

		if request.GetBody().WTYPE != nil {
			requestForm.Set("WTYPE", request.GetBody().GetWTYPE())
		}

		if request.GetBody().CrdCvc != nil {
			requestForm.Set("crd_cvc", request.GetBody().GetCrdCvc())
		}

		if request.GetBody().CrdExp != nil {
			requestForm.Set("crd_exp", request.GetBody().GetCrdExp())
		}

		if request.GetBody().CrdPan != nil {
			requestForm.Set("crd_pan", request.GetBody().GetCrdPan())
		}

		if request.GetBody().REF != nil {
			requestForm.Set("REF", request.GetBody().GetREF())
		}

		if request.GetBody().MERCH_3D_TERM_URL != nil {
			requestForm.Set("MERCH_3D_TERM_URL", request.GetBody().GetMERCH_3D_TERM_URL())
		}

		if request.GetBody().MERCH_SCA != nil {
			requestForm.Set("MERCH_SCA", request.GetBody().GetMERCH_SCA())
		}

		contentType = "application/x-www-form-urlencoded"
		requestBody = []byte(requestForm.Encode())
	}

	if requestBody, err = s.hooksProcessPayIn.MutateBody(
		ctx,
		request,
		response,
		requestBody,
		response.Doc.Logs,
		response.Doc.HooksData,
	); err != nil {
		return
	}

	response.Doc.RequestBody = requestBody

	requestReader := bytes.NewReader(requestBody)

	var httpRequest *http.Request
	if httpRequest, err = http.NewRequestWithContext(
		ctx,
		http.MethodPost,
		path,
		requestReader,
	); err != nil {
		return
	}

	httpRequest.Header.Set("Content-Type", contentType)

	if err = s.hooksProcessPayIn.BeforeHttpRequest(
		ctx,
		request,
		response,
		httpRequest,
		response.Doc.Logs,
		response.Doc.HooksData,
	); err != nil {
		return
	}

	response.Doc.RequestHeaders = httpRequest.Header

	var httpResponse *http.Response
	if httpResponse, err = http.DefaultClient.Do(httpRequest); err != nil {
		response.Doc.ResponseError = err.Error()
		return
	}
	defer httpResponse.Body.Close()

	response.Doc.ResponseStatus = httpResponse.Status
	response.Doc.ResponseHeaders = httpResponse.Header

	var done bool
	if done, err = s.hooksProcessPayIn.AfterHttpResponse(
		ctx,
		request,
		httpRequest,
		httpResponse,
		response,
		response.Doc.Logs,
		response.Doc.HooksData,
	); done || err != nil {
		return
	}

	var rspBody []byte
	if rspBody, err = io.ReadAll(httpResponse.Body); err != nil {
		return
	}

	response.Doc.ResponseBody = rspBody

	if s.hooksProcessPayIn.CustomUnmarshal != nil {
		err = s.hooksProcessPayIn.CustomUnmarshal(
			ctx,
			request,
			response,
			rspBody,
			response.Doc.Logs,
			response.Doc.HooksData,
		)
		return
	}

	if response.Response.Response, err = wraps.ParseStringPtr[string](string(rspBody)); err == nil {
		response.ResponseOutput = File_inner_processing_rest_alatau_city_payin_proto_AlatauCityBankPayIn_ProcessPayIn_Response
		return
	}
	return
}

type File_inner_processing_rest_alatau_city_payin_proto_AlatauCityBankPayIn_ProcessNoAcceptPay int32

const (
	File_inner_processing_rest_alatau_city_payin_proto_AlatauCityBankPayIn_ProcessNoAcceptPay_OutOfScope File_inner_processing_rest_alatau_city_payin_proto_AlatauCityBankPayIn_ProcessNoAcceptPay = iota
	File_inner_processing_rest_alatau_city_payin_proto_AlatauCityBankPayIn_ProcessNoAcceptPay_Response
)

type File_inner_processing_rest_alatau_city_payin_proto_AlatauCityBankPayIn_ProcessNoAcceptPay_Hooks struct {
	ValidateArguments func(
		ctx context.Context,
		request *AlatauCityProcessNoAcceptPayRequest,
		response *AlatauCityBankPayIn_ProcessNoAcceptPay_Response,
		docData map[string]any,
		hooksData map[string]any,
	) error

	MutatePath func(
		ctx context.Context,
		request *AlatauCityProcessNoAcceptPayRequest,
		response *AlatauCityBankPayIn_ProcessNoAcceptPay_Response,
		calculatedPath string,
		docData map[string]any,
		hooksData map[string]any,
	) (mutatePath string, err error)

	CustomMarshal func(
		ctx context.Context,
		request *AlatauCityProcessNoAcceptPayRequest,
		response *AlatauCityBankPayIn_ProcessNoAcceptPay_Response,
		path string,
		docData map[string]any,
		hooksData map[string]any,
	) (body []byte, contentType string, err error)

	MutateBody func(
		ctx context.Context,
		request *AlatauCityProcessNoAcceptPayRequest,
		response *AlatauCityBankPayIn_ProcessNoAcceptPay_Response,
		body []byte,
		docData map[string]any,
		hooksData map[string]any,
	) (mutatedBody []byte, err error)

	BeforeHttpRequest func(
		ctx context.Context,
		request *AlatauCityProcessNoAcceptPayRequest,
		response *AlatauCityBankPayIn_ProcessNoAcceptPay_Response,
		httpRequest *http.Request,
		docData map[string]any,
		hooksData map[string]any,
	) error

	AfterHttpResponse func(
		ctx context.Context,
		request *AlatauCityProcessNoAcceptPayRequest,
		httpRequest *http.Request,
		httpResponse *http.Response,
		response *AlatauCityBankPayIn_ProcessNoAcceptPay_Response,
		docData map[string]any,
		hooksData map[string]any,
	) (done bool, err error)

	CustomUnmarshal func(
		ctx context.Context,
		request *AlatauCityProcessNoAcceptPayRequest,
		response *AlatauCityBankPayIn_ProcessNoAcceptPay_Response,
		rspBody []byte,
		docData map[string]any,
		hooksData map[string]any,
	) error
}

func NewFile_inner_processing_rest_alatau_city_payin_proto_AlatauCityBankPayIn_ProcessNoAcceptPay_Hooks() *File_inner_processing_rest_alatau_city_payin_proto_AlatauCityBankPayIn_ProcessNoAcceptPay_Hooks {
	return &File_inner_processing_rest_alatau_city_payin_proto_AlatauCityBankPayIn_ProcessNoAcceptPay_Hooks{
		ValidateArguments: func(
			ctx context.Context,
			request *AlatauCityProcessNoAcceptPayRequest,
			response *AlatauCityBankPayIn_ProcessNoAcceptPay_Response,
			docData map[string]any,
			hooksData map[string]any,
		) error {
			return nil
		},
		MutatePath: func(
			ctx context.Context,
			request *AlatauCityProcessNoAcceptPayRequest,
			response *AlatauCityBankPayIn_ProcessNoAcceptPay_Response,
			calculatedPath string,
			docData map[string]any,
			hooksData map[string]any,
		) (mutatePath string, err error) {
			return calculatedPath, nil
		},
		MutateBody: func(
			ctx context.Context,
			request *AlatauCityProcessNoAcceptPayRequest,
			response *AlatauCityBankPayIn_ProcessNoAcceptPay_Response,
			body []byte,
			docData map[string]any,
			hooksData map[string]any,
		) (mutatedBody []byte, err error) {
			return body, nil
		},
		BeforeHttpRequest: func(
			ctx context.Context,
			request *AlatauCityProcessNoAcceptPayRequest,
			response *AlatauCityBankPayIn_ProcessNoAcceptPay_Response,
			httpRequest *http.Request,
			docData map[string]any,
			hooksData map[string]any,
		) error {
			return nil
		},
		AfterHttpResponse: func(
			ctx context.Context,
			request *AlatauCityProcessNoAcceptPayRequest,
			httpRequest *http.Request,
			httpResponse *http.Response,
			response *AlatauCityBankPayIn_ProcessNoAcceptPay_Response,
			docData map[string]any,
			hooksData map[string]any,
		) (done bool, err error) {
			return false, nil
		},
	}
}

func PrepareFile_inner_processing_rest_alatau_city_payin_proto_AlatauCityBankPayIn_ProcessNoAcceptPay_Hooks(
	in *File_inner_processing_rest_alatau_city_payin_proto_AlatauCityBankPayIn_ProcessNoAcceptPay_Hooks,
) *File_inner_processing_rest_alatau_city_payin_proto_AlatauCityBankPayIn_ProcessNoAcceptPay_Hooks {
	defaultHook := NewFile_inner_processing_rest_alatau_city_payin_proto_AlatauCityBankPayIn_ProcessNoAcceptPay_Hooks()
	if in == nil {
		return defaultHook
	}

	if in.ValidateArguments == nil {
		in.ValidateArguments = defaultHook.ValidateArguments
	}

	if in.MutatePath == nil {
		in.MutatePath = defaultHook.MutatePath
	}
	if in.MutateBody == nil {
		in.MutateBody = defaultHook.MutateBody
	}

	if in.BeforeHttpRequest == nil {
		in.BeforeHttpRequest = defaultHook.BeforeHttpRequest
	}

	if in.AfterHttpResponse == nil {
		in.AfterHttpResponse = defaultHook.AfterHttpResponse
	}

	return in
}

type AlatauCityBankPayIn_ProcessNoAcceptPay_Response struct {
	Request        *AlatauCityProcessNoAcceptPayRequest
	Response       *AlatauCityProcessNoAcceptPayResponse
	ResponseOutput File_inner_processing_rest_alatau_city_payin_proto_AlatauCityBankPayIn_ProcessNoAcceptPay
	Error          error
	Doc            *doc.Struct
}

func (s *restAlatauCityBankPayInServer) ProcessNoAcceptPay(
	ctx context.Context,
	request *AlatauCityProcessNoAcceptPayRequest,
) (
	response *AlatauCityBankPayIn_ProcessNoAcceptPay_Response,
	err error,
) {
	label := cntx.Begin(ctx, "AlatauCityBankPayInServer_ProcessNoAcceptPay")
	defer cntx.End(ctx, label)

	response = &AlatauCityBankPayIn_ProcessNoAcceptPay_Response{
		Request:        request,
		Response:       &AlatauCityProcessNoAcceptPayResponse{},
		ResponseOutput: File_inner_processing_rest_alatau_city_payin_proto_AlatauCityBankPayIn_ProcessNoAcceptPay_OutOfScope,
		Error:          nil,
		Doc: &doc.Struct{
			UUID:          uuid.New(),
			CreatedAt:     time.Now(),
			CreatedAtNano: time.Now().UnixNano(),
			FuncName:      "AlatauCityBankPayInServer_ProcessNoAcceptPay",
			Label:         label,
			Logs:          make(map[string]any),
			Arguments: map[string]any{
				"clear_path": "/ecom/api",
			},
			Request:         file_inner_processing_rest_alatau_city_payin_proto_message_AlatauCityProcessNoAcceptPayRequestToGo(label, request),
			RequestMethod:   "Post",
			RequestHeaders:  make(map[string][]string),
			ResponseHeaders: make(map[string][]string),
			HooksData:       make(map[string]any),
		},
	}
	defer func() { s.docFunc(ctx, response.Doc) }()

	defer func() {
		if err != nil {
			ctxzap.AddFields(ctx, zap.Any(label+"error", err))
			response.Error = err
			response.Doc.Error = err

			return
		}

		ctxzap.AddFields(
			ctx,
			file_inner_processing_rest_alatau_city_payin_proto_message_AlatauCityProcessNoAcceptPayResponseToZap(label+"response.Response", response.Response),
			zap.Any(label+"response.ResponseOutput", response.ResponseOutput),
		)

		response.Doc.Response = file_inner_processing_rest_alatau_city_payin_proto_message_AlatauCityProcessNoAcceptPayResponseToGo(label, response.Response)
		response.Doc.ResponseOutput = int32(response.ResponseOutput)
	}()

	ctxzap.AddFields(
		ctx,
		file_inner_processing_rest_alatau_city_payin_proto_message_AlatauCityProcessNoAcceptPayRequestToZap(label+"response.Request", response.Request),
		zap.Any(label+"clear_path", "/ecom/api"),
	)

	if err = s.hooksProcessNoAcceptPay.ValidateArguments(
		ctx,
		request,
		response,
		response.Doc.Logs,
		response.Doc.HooksData,
	); err != nil {
		return
	}

	path := s.baseUrl + "/ecom/api"

	response.Doc.RequestURI = path

	if path, err = s.hooksProcessNoAcceptPay.MutatePath(
		ctx,
		request,
		response,
		path,
		response.Doc.Logs,
		response.Doc.HooksData,
	); err != nil {
		return
	}

	response.Doc.MutatedRequestURI = path

	var contentType string

	var requestBody []byte
	if s.hooksProcessNoAcceptPay.CustomMarshal != nil {
		if requestBody, contentType, err = s.hooksProcessNoAcceptPay.CustomMarshal(
			ctx,
			request,
			response,
			path,
			response.Doc.Logs,
			response.Doc.HooksData,
		); err != nil {
			return
		}
	} else {
		requestForm := url.Values{}
		if request.GetBody().AMOUNT != nil {
			requestForm.Set("AMOUNT", request.GetBody().GetAMOUNT())
		}

		if request.GetBody().BACKREF != nil {
			requestForm.Set("BACKREF", request.GetBody().GetBACKREF())
		}

		if request.GetBody().CLIENT_ID != nil {
			requestForm.Set("CLIENT_ID", request.GetBody().GetCLIENT_ID())
		}

		if request.GetBody().CURRENCY != nil {
			requestForm.Set("CURRENCY", request.GetBody().GetCURRENCY())
		}

		if request.GetBody().DESC != nil {
			requestForm.Set("DESC", request.GetBody().GetDESC())
		}

		if request.GetBody().DESC_ORDER != nil {
			requestForm.Set("DESC_ORDER", request.GetBody().GetDESC_ORDER())
		}

		if request.GetBody().EMAIL != nil {
			requestForm.Set("EMAIL", request.GetBody().GetEMAIL())
		}

		if request.GetBody().EXT_MPI_ECI != nil {
			requestForm.Set("EXT_MPI_ECI", request.GetBody().GetEXT_MPI_ECI())
		}

		if request.GetBody().INT_REF != nil {
			requestForm.Set("INT_REF", request.GetBody().GetINT_REF())
		}

		if request.GetBody().LANGUAGE != nil {
			requestForm.Set("LANGUAGE", request.GetBody().GetLANGUAGE())
		}

		if request.GetBody().MERCHANT != nil {
			requestForm.Set("MERCHANT", request.GetBody().GetMERCHANT())
		}

		if request.GetBody().MERCH_PAYTO_TOKEN_ID != nil {
			requestForm.Set("MERCH_PAYTO_TOKEN_ID", request.GetBody().GetMERCH_PAYTO_TOKEN_ID())
		}

		if request.GetBody().MERCH_RN_ID != nil {
			requestForm.Set("MERCH_RN_ID", request.GetBody().GetMERCH_RN_ID())
		}

		if request.GetBody().MERCH_TOKEN_ID != nil {
			requestForm.Set("MERCH_TOKEN_ID", request.GetBody().GetMERCH_TOKEN_ID())
		}

		if request.GetBody().MERCH_TRAN_STATE != nil {
			requestForm.Set("MERCH_TRAN_STATE", request.GetBody().GetMERCH_TRAN_STATE())
		}

		if request.GetBody().MK_TOKEN != nil {
			requestForm.Set("MK_TOKEN", request.GetBody().GetMK_TOKEN())
		}

		if request.GetBody().M_INFO != nil {
			requestForm.Set("M_INFO", request.GetBody().GetM_INFO())
		}

		if request.GetBody().NAME != nil {
			requestForm.Set("NAME", request.GetBody().GetNAME())
		}

		if request.GetBody().NONCE != nil {
			requestForm.Set("NONCE", request.GetBody().GetNONCE())
		}

		if request.GetBody().ORDER != nil {
			requestForm.Set("ORDER", request.GetBody().GetORDER())
		}

		if request.GetBody().PAYMENT_TO != nil {
			requestForm.Set("PAYMENT_TO", request.GetBody().GetPAYMENT_TO())
		}

		if request.GetBody().P_SIGN != nil {
			requestForm.Set("P_SIGN", request.GetBody().GetP_SIGN())
		}

		if request.GetBody().RECUR_EXP != nil {
			requestForm.Set("RECUR_EXP", request.GetBody().GetRECUR_EXP())
		}

		if request.GetBody().RECUR_FREQ != nil {
			requestForm.Set("RECUR_FREQ", request.GetBody().GetRECUR_FREQ())
		}

		if request.GetBody().RECUR_REF != nil {
			requestForm.Set("RECUR_REF", request.GetBody().GetRECUR_REF())
		}

		if request.GetBody().TAVV != nil {
			requestForm.Set("TAVV", request.GetBody().GetTAVV())
		}

		if request.GetBody().TERMINAL != nil {
			requestForm.Set("TERMINAL", request.GetBody().GetTERMINAL())
		}

		if request.GetBody().Ucaf_Authentication_Data != nil {
			requestForm.Set("Ucaf_Authentication_Data", request.GetBody().GetUcaf_Authentication_Data())
		}

		if request.GetBody().Ucaf_Flag != nil {
			requestForm.Set("Ucaf_Flag", request.GetBody().GetUcaf_Flag())
		}

		if request.GetBody().WTYPE != nil {
			requestForm.Set("WTYPE", request.GetBody().GetWTYPE())
		}

		if request.GetBody().CrdCvc != nil {
			requestForm.Set("crd_cvc", request.GetBody().GetCrdCvc())
		}

		if request.GetBody().CrdExp != nil {
			requestForm.Set("crd_exp", request.GetBody().GetCrdExp())
		}

		if request.GetBody().CrdPan != nil {
			requestForm.Set("crd_pan", request.GetBody().GetCrdPan())
		}

		if request.GetBody().REF != nil {
			requestForm.Set("REF", request.GetBody().GetREF())
		}

		if request.GetBody().MERCH_3D_TERM_URL != nil {
			requestForm.Set("MERCH_3D_TERM_URL", request.GetBody().GetMERCH_3D_TERM_URL())
		}

		if request.GetBody().MERCH_SCA != nil {
			requestForm.Set("MERCH_SCA", request.GetBody().GetMERCH_SCA())
		}

		contentType = "application/x-www-form-urlencoded"
		requestBody = []byte(requestForm.Encode())
	}

	if requestBody, err = s.hooksProcessNoAcceptPay.MutateBody(
		ctx,
		request,
		response,
		requestBody,
		response.Doc.Logs,
		response.Doc.HooksData,
	); err != nil {
		return
	}

	response.Doc.RequestBody = requestBody

	requestReader := bytes.NewReader(requestBody)

	var httpRequest *http.Request
	if httpRequest, err = http.NewRequestWithContext(
		ctx,
		http.MethodPost,
		path,
		requestReader,
	); err != nil {
		return
	}

	httpRequest.Header.Set("Content-Type", contentType)

	if err = s.hooksProcessNoAcceptPay.BeforeHttpRequest(
		ctx,
		request,
		response,
		httpRequest,
		response.Doc.Logs,
		response.Doc.HooksData,
	); err != nil {
		return
	}

	response.Doc.RequestHeaders = httpRequest.Header

	var httpResponse *http.Response
	if httpResponse, err = http.DefaultClient.Do(httpRequest); err != nil {
		response.Doc.ResponseError = err.Error()
		return
	}
	defer httpResponse.Body.Close()

	response.Doc.ResponseStatus = httpResponse.Status
	response.Doc.ResponseHeaders = httpResponse.Header

	var done bool
	if done, err = s.hooksProcessNoAcceptPay.AfterHttpResponse(
		ctx,
		request,
		httpRequest,
		httpResponse,
		response,
		response.Doc.Logs,
		response.Doc.HooksData,
	); done || err != nil {
		return
	}

	var rspBody []byte
	if rspBody, err = io.ReadAll(httpResponse.Body); err != nil {
		return
	}

	response.Doc.ResponseBody = rspBody

	if s.hooksProcessNoAcceptPay.CustomUnmarshal != nil {
		err = s.hooksProcessNoAcceptPay.CustomUnmarshal(
			ctx,
			request,
			response,
			rspBody,
			response.Doc.Logs,
			response.Doc.HooksData,
		)
		return
	}

	if response.Response.Response, err = wraps.ParseStringPtr[string](string(rspBody)); err == nil {
		response.ResponseOutput = File_inner_processing_rest_alatau_city_payin_proto_AlatauCityBankPayIn_ProcessNoAcceptPay_Response
		return
	}
	return
}

type File_inner_processing_rest_alatau_city_payin_proto_AlatauCityBankPayIn_GetStatus int32

const (
	File_inner_processing_rest_alatau_city_payin_proto_AlatauCityBankPayIn_GetStatus_OutOfScope File_inner_processing_rest_alatau_city_payin_proto_AlatauCityBankPayIn_GetStatus = iota
	File_inner_processing_rest_alatau_city_payin_proto_AlatauCityBankPayIn_GetStatus_StatusResponse
)

type File_inner_processing_rest_alatau_city_payin_proto_AlatauCityBankPayIn_GetStatus_Hooks struct {
	ValidateArguments func(
		ctx context.Context,
		request *AlatauCityGetStatusPayInRequest,
		response *AlatauCityBankPayIn_GetStatus_Response,
		docData map[string]any,
		hooksData map[string]any,
	) error

	MutatePath func(
		ctx context.Context,
		request *AlatauCityGetStatusPayInRequest,
		response *AlatauCityBankPayIn_GetStatus_Response,
		calculatedPath string,
		docData map[string]any,
		hooksData map[string]any,
	) (mutatePath string, err error)

	CustomMarshal func(
		ctx context.Context,
		request *AlatauCityGetStatusPayInRequest,
		response *AlatauCityBankPayIn_GetStatus_Response,
		path string,
		docData map[string]any,
		hooksData map[string]any,
	) (body []byte, contentType string, err error)

	MutateBody func(
		ctx context.Context,
		request *AlatauCityGetStatusPayInRequest,
		response *AlatauCityBankPayIn_GetStatus_Response,
		body []byte,
		docData map[string]any,
		hooksData map[string]any,
	) (mutatedBody []byte, err error)

	BeforeHttpRequest func(
		ctx context.Context,
		request *AlatauCityGetStatusPayInRequest,
		response *AlatauCityBankPayIn_GetStatus_Response,
		httpRequest *http.Request,
		docData map[string]any,
		hooksData map[string]any,
	) error

	AfterHttpResponse func(
		ctx context.Context,
		request *AlatauCityGetStatusPayInRequest,
		httpRequest *http.Request,
		httpResponse *http.Response,
		response *AlatauCityBankPayIn_GetStatus_Response,
		docData map[string]any,
		hooksData map[string]any,
	) (done bool, err error)

	CustomUnmarshal func(
		ctx context.Context,
		request *AlatauCityGetStatusPayInRequest,
		response *AlatauCityBankPayIn_GetStatus_Response,
		rspBody []byte,
		docData map[string]any,
		hooksData map[string]any,
	) error
}

func NewFile_inner_processing_rest_alatau_city_payin_proto_AlatauCityBankPayIn_GetStatus_Hooks() *File_inner_processing_rest_alatau_city_payin_proto_AlatauCityBankPayIn_GetStatus_Hooks {
	return &File_inner_processing_rest_alatau_city_payin_proto_AlatauCityBankPayIn_GetStatus_Hooks{
		ValidateArguments: func(
			ctx context.Context,
			request *AlatauCityGetStatusPayInRequest,
			response *AlatauCityBankPayIn_GetStatus_Response,
			docData map[string]any,
			hooksData map[string]any,
		) error {
			return nil
		},
		MutatePath: func(
			ctx context.Context,
			request *AlatauCityGetStatusPayInRequest,
			response *AlatauCityBankPayIn_GetStatus_Response,
			calculatedPath string,
			docData map[string]any,
			hooksData map[string]any,
		) (mutatePath string, err error) {
			return calculatedPath, nil
		},
		MutateBody: func(
			ctx context.Context,
			request *AlatauCityGetStatusPayInRequest,
			response *AlatauCityBankPayIn_GetStatus_Response,
			body []byte,
			docData map[string]any,
			hooksData map[string]any,
		) (mutatedBody []byte, err error) {
			return body, nil
		},
		BeforeHttpRequest: func(
			ctx context.Context,
			request *AlatauCityGetStatusPayInRequest,
			response *AlatauCityBankPayIn_GetStatus_Response,
			httpRequest *http.Request,
			docData map[string]any,
			hooksData map[string]any,
		) error {
			return nil
		},
		AfterHttpResponse: func(
			ctx context.Context,
			request *AlatauCityGetStatusPayInRequest,
			httpRequest *http.Request,
			httpResponse *http.Response,
			response *AlatauCityBankPayIn_GetStatus_Response,
			docData map[string]any,
			hooksData map[string]any,
		) (done bool, err error) {
			return false, nil
		},
	}
}

func PrepareFile_inner_processing_rest_alatau_city_payin_proto_AlatauCityBankPayIn_GetStatus_Hooks(
	in *File_inner_processing_rest_alatau_city_payin_proto_AlatauCityBankPayIn_GetStatus_Hooks,
) *File_inner_processing_rest_alatau_city_payin_proto_AlatauCityBankPayIn_GetStatus_Hooks {
	defaultHook := NewFile_inner_processing_rest_alatau_city_payin_proto_AlatauCityBankPayIn_GetStatus_Hooks()
	if in == nil {
		return defaultHook
	}

	if in.ValidateArguments == nil {
		in.ValidateArguments = defaultHook.ValidateArguments
	}

	if in.MutatePath == nil {
		in.MutatePath = defaultHook.MutatePath
	}
	if in.MutateBody == nil {
		in.MutateBody = defaultHook.MutateBody
	}

	if in.BeforeHttpRequest == nil {
		in.BeforeHttpRequest = defaultHook.BeforeHttpRequest
	}

	if in.AfterHttpResponse == nil {
		in.AfterHttpResponse = defaultHook.AfterHttpResponse
	}

	return in
}

type AlatauCityBankPayIn_GetStatus_Response struct {
	Request        *AlatauCityGetStatusPayInRequest
	Response       *AlatauCityGetStatusPayInResponse
	ResponseOutput File_inner_processing_rest_alatau_city_payin_proto_AlatauCityBankPayIn_GetStatus
	Error          error
	Doc            *doc.Struct
}

func (s *restAlatauCityBankPayInServer) GetStatus(
	ctx context.Context,
	request *AlatauCityGetStatusPayInRequest,
) (
	response *AlatauCityBankPayIn_GetStatus_Response,
	err error,
) {
	label := cntx.Begin(ctx, "AlatauCityBankPayInServer_GetStatus")
	defer cntx.End(ctx, label)

	response = &AlatauCityBankPayIn_GetStatus_Response{
		Request:        request,
		Response:       &AlatauCityGetStatusPayInResponse{},
		ResponseOutput: File_inner_processing_rest_alatau_city_payin_proto_AlatauCityBankPayIn_GetStatus_OutOfScope,
		Error:          nil,
		Doc: &doc.Struct{
			UUID:          uuid.New(),
			CreatedAt:     time.Now(),
			CreatedAtNano: time.Now().UnixNano(),
			FuncName:      "AlatauCityBankPayInServer_GetStatus",
			Label:         label,
			Logs:          make(map[string]any),
			Arguments: map[string]any{
				"clear_path": "/ecom/api",
			},
			Request:         file_inner_processing_rest_alatau_city_payin_proto_message_AlatauCityGetStatusPayInRequestToGo(label, request),
			RequestMethod:   "Post",
			RequestHeaders:  make(map[string][]string),
			ResponseHeaders: make(map[string][]string),
			HooksData:       make(map[string]any),
		},
	}
	defer func() { s.docFunc(ctx, response.Doc) }()

	defer func() {
		if err != nil {
			ctxzap.AddFields(ctx, zap.Any(label+"error", err))
			response.Error = err
			response.Doc.Error = err

			return
		}

		ctxzap.AddFields(
			ctx,
			file_inner_processing_rest_alatau_city_payin_proto_message_AlatauCityGetStatusPayInResponseToZap(label+"response.Response", response.Response),
			zap.Any(label+"response.ResponseOutput", response.ResponseOutput),
		)

		response.Doc.Response = file_inner_processing_rest_alatau_city_payin_proto_message_AlatauCityGetStatusPayInResponseToGo(label, response.Response)
		response.Doc.ResponseOutput = int32(response.ResponseOutput)
	}()

	ctxzap.AddFields(
		ctx,
		file_inner_processing_rest_alatau_city_payin_proto_message_AlatauCityGetStatusPayInRequestToZap(label+"response.Request", response.Request),
		zap.Any(label+"clear_path", "/ecom/api"),
	)

	if err = s.hooksGetStatus.ValidateArguments(
		ctx,
		request,
		response,
		response.Doc.Logs,
		response.Doc.HooksData,
	); err != nil {
		return
	}

	path := s.baseUrl + "/ecom/api"

	response.Doc.RequestURI = path

	if path, err = s.hooksGetStatus.MutatePath(
		ctx,
		request,
		response,
		path,
		response.Doc.Logs,
		response.Doc.HooksData,
	); err != nil {
		return
	}

	response.Doc.MutatedRequestURI = path

	var contentType string

	var requestBody []byte
	if s.hooksGetStatus.CustomMarshal != nil {
		if requestBody, contentType, err = s.hooksGetStatus.CustomMarshal(
			ctx,
			request,
			response,
			path,
			response.Doc.Logs,
			response.Doc.HooksData,
		); err != nil {
			return
		}
	} else {
		requestForm := url.Values{}
		if request.GetBody().ORDER != nil {
			requestForm.Set("ORDER", request.GetBody().GetORDER())
		}

		if request.GetBody().MERCHANT != nil {
			requestForm.Set("MERCHANT", request.GetBody().GetMERCHANT())
		}

		if request.GetBody().GETSTATUS != nil {
			requestForm.Set("GETSTATUS", request.GetBody().GetGETSTATUS())
		}

		if request.GetBody().P_SIGN != nil {
			requestForm.Set("P_SIGN", request.GetBody().GetP_SIGN())
		}

		contentType = "application/x-www-form-urlencoded"
		requestBody = []byte(requestForm.Encode())
	}

	if requestBody, err = s.hooksGetStatus.MutateBody(
		ctx,
		request,
		response,
		requestBody,
		response.Doc.Logs,
		response.Doc.HooksData,
	); err != nil {
		return
	}

	response.Doc.RequestBody = requestBody

	requestReader := bytes.NewReader(requestBody)

	var httpRequest *http.Request
	if httpRequest, err = http.NewRequestWithContext(
		ctx,
		http.MethodPost,
		path,
		requestReader,
	); err != nil {
		return
	}

	httpRequest.Header.Set("Content-Type", contentType)

	if err = s.hooksGetStatus.BeforeHttpRequest(
		ctx,
		request,
		response,
		httpRequest,
		response.Doc.Logs,
		response.Doc.HooksData,
	); err != nil {
		return
	}

	response.Doc.RequestHeaders = httpRequest.Header

	var httpResponse *http.Response
	if httpResponse, err = http.DefaultClient.Do(httpRequest); err != nil {
		response.Doc.ResponseError = err.Error()
		return
	}
	defer httpResponse.Body.Close()

	response.Doc.ResponseStatus = httpResponse.Status
	response.Doc.ResponseHeaders = httpResponse.Header

	var done bool
	if done, err = s.hooksGetStatus.AfterHttpResponse(
		ctx,
		request,
		httpRequest,
		httpResponse,
		response,
		response.Doc.Logs,
		response.Doc.HooksData,
	); done || err != nil {
		return
	}

	var rspBody []byte
	if rspBody, err = io.ReadAll(httpResponse.Body); err != nil {
		return
	}

	response.Doc.ResponseBody = rspBody

	if s.hooksGetStatus.CustomUnmarshal != nil {
		err = s.hooksGetStatus.CustomUnmarshal(
			ctx,
			request,
			response,
			rspBody,
			response.Doc.Logs,
			response.Doc.HooksData,
		)
		return
	}

	if response.Response.StatusResponse, err = marshaller.XMLUnmarshalPtr[AlatauCityGetStatusPayInResponse_StatusResponse](rspBody); err == nil {
		response.ResponseOutput = File_inner_processing_rest_alatau_city_payin_proto_AlatauCityBankPayIn_GetStatus_StatusResponse
		return
	}
	return
}

type File_inner_processing_rest_alatau_city_payin_proto_AlatauCityBankPayIn_ConfirmThreeDS int32

const (
	File_inner_processing_rest_alatau_city_payin_proto_AlatauCityBankPayIn_ConfirmThreeDS_OutOfScope File_inner_processing_rest_alatau_city_payin_proto_AlatauCityBankPayIn_ConfirmThreeDS = iota
	File_inner_processing_rest_alatau_city_payin_proto_AlatauCityBankPayIn_ConfirmThreeDS_Response
)

type File_inner_processing_rest_alatau_city_payin_proto_AlatauCityBankPayIn_ConfirmThreeDS_Hooks struct {
	ValidateArguments func(
		ctx context.Context,
		request *AlatauCityConfirmThreeDSRequest,
		response *AlatauCityBankPayIn_ConfirmThreeDS_Response,
		docData map[string]any,
		hooksData map[string]any,
	) error

	MutatePath func(
		ctx context.Context,
		request *AlatauCityConfirmThreeDSRequest,
		response *AlatauCityBankPayIn_ConfirmThreeDS_Response,
		calculatedPath string,
		docData map[string]any,
		hooksData map[string]any,
	) (mutatePath string, err error)

	CustomMarshal func(
		ctx context.Context,
		request *AlatauCityConfirmThreeDSRequest,
		response *AlatauCityBankPayIn_ConfirmThreeDS_Response,
		path string,
		docData map[string]any,
		hooksData map[string]any,
	) (body []byte, contentType string, err error)

	MutateBody func(
		ctx context.Context,
		request *AlatauCityConfirmThreeDSRequest,
		response *AlatauCityBankPayIn_ConfirmThreeDS_Response,
		body []byte,
		docData map[string]any,
		hooksData map[string]any,
	) (mutatedBody []byte, err error)

	BeforeHttpRequest func(
		ctx context.Context,
		request *AlatauCityConfirmThreeDSRequest,
		response *AlatauCityBankPayIn_ConfirmThreeDS_Response,
		httpRequest *http.Request,
		docData map[string]any,
		hooksData map[string]any,
	) error

	AfterHttpResponse func(
		ctx context.Context,
		request *AlatauCityConfirmThreeDSRequest,
		httpRequest *http.Request,
		httpResponse *http.Response,
		response *AlatauCityBankPayIn_ConfirmThreeDS_Response,
		docData map[string]any,
		hooksData map[string]any,
	) (done bool, err error)

	CustomUnmarshal func(
		ctx context.Context,
		request *AlatauCityConfirmThreeDSRequest,
		response *AlatauCityBankPayIn_ConfirmThreeDS_Response,
		rspBody []byte,
		docData map[string]any,
		hooksData map[string]any,
	) error
}

func NewFile_inner_processing_rest_alatau_city_payin_proto_AlatauCityBankPayIn_ConfirmThreeDS_Hooks() *File_inner_processing_rest_alatau_city_payin_proto_AlatauCityBankPayIn_ConfirmThreeDS_Hooks {
	return &File_inner_processing_rest_alatau_city_payin_proto_AlatauCityBankPayIn_ConfirmThreeDS_Hooks{
		ValidateArguments: func(
			ctx context.Context,
			request *AlatauCityConfirmThreeDSRequest,
			response *AlatauCityBankPayIn_ConfirmThreeDS_Response,
			docData map[string]any,
			hooksData map[string]any,
		) error {
			return nil
		},
		MutatePath: func(
			ctx context.Context,
			request *AlatauCityConfirmThreeDSRequest,
			response *AlatauCityBankPayIn_ConfirmThreeDS_Response,
			calculatedPath string,
			docData map[string]any,
			hooksData map[string]any,
		) (mutatePath string, err error) {
			return calculatedPath, nil
		},
		MutateBody: func(
			ctx context.Context,
			request *AlatauCityConfirmThreeDSRequest,
			response *AlatauCityBankPayIn_ConfirmThreeDS_Response,
			body []byte,
			docData map[string]any,
			hooksData map[string]any,
		) (mutatedBody []byte, err error) {
			return body, nil
		},
		BeforeHttpRequest: func(
			ctx context.Context,
			request *AlatauCityConfirmThreeDSRequest,
			response *AlatauCityBankPayIn_ConfirmThreeDS_Response,
			httpRequest *http.Request,
			docData map[string]any,
			hooksData map[string]any,
		) error {
			return nil
		},
		AfterHttpResponse: func(
			ctx context.Context,
			request *AlatauCityConfirmThreeDSRequest,
			httpRequest *http.Request,
			httpResponse *http.Response,
			response *AlatauCityBankPayIn_ConfirmThreeDS_Response,
			docData map[string]any,
			hooksData map[string]any,
		) (done bool, err error) {
			return false, nil
		},
	}
}

func PrepareFile_inner_processing_rest_alatau_city_payin_proto_AlatauCityBankPayIn_ConfirmThreeDS_Hooks(
	in *File_inner_processing_rest_alatau_city_payin_proto_AlatauCityBankPayIn_ConfirmThreeDS_Hooks,
) *File_inner_processing_rest_alatau_city_payin_proto_AlatauCityBankPayIn_ConfirmThreeDS_Hooks {
	defaultHook := NewFile_inner_processing_rest_alatau_city_payin_proto_AlatauCityBankPayIn_ConfirmThreeDS_Hooks()
	if in == nil {
		return defaultHook
	}

	if in.ValidateArguments == nil {
		in.ValidateArguments = defaultHook.ValidateArguments
	}

	if in.MutatePath == nil {
		in.MutatePath = defaultHook.MutatePath
	}
	if in.MutateBody == nil {
		in.MutateBody = defaultHook.MutateBody
	}

	if in.BeforeHttpRequest == nil {
		in.BeforeHttpRequest = defaultHook.BeforeHttpRequest
	}

	if in.AfterHttpResponse == nil {
		in.AfterHttpResponse = defaultHook.AfterHttpResponse
	}

	return in
}

type AlatauCityBankPayIn_ConfirmThreeDS_Response struct {
	Request        *AlatauCityConfirmThreeDSRequest
	Response       *AlatauCityConfirmThreeDSResponse
	ResponseOutput File_inner_processing_rest_alatau_city_payin_proto_AlatauCityBankPayIn_ConfirmThreeDS
	Error          error
	Doc            *doc.Struct
}

func (s *restAlatauCityBankPayInServer) ConfirmThreeDS(
	ctx context.Context,
	request *AlatauCityConfirmThreeDSRequest,
) (
	response *AlatauCityBankPayIn_ConfirmThreeDS_Response,
	err error,
) {
	label := cntx.Begin(ctx, "AlatauCityBankPayInServer_ConfirmThreeDS")
	defer cntx.End(ctx, label)

	response = &AlatauCityBankPayIn_ConfirmThreeDS_Response{
		Request:        request,
		Response:       &AlatauCityConfirmThreeDSResponse{},
		ResponseOutput: File_inner_processing_rest_alatau_city_payin_proto_AlatauCityBankPayIn_ConfirmThreeDS_OutOfScope,
		Error:          nil,
		Doc: &doc.Struct{
			UUID:            uuid.New(),
			CreatedAt:       time.Now(),
			CreatedAtNano:   time.Now().UnixNano(),
			FuncName:        "AlatauCityBankPayInServer_ConfirmThreeDS",
			Label:           label,
			Logs:            make(map[string]any),
			Arguments:       map[string]any{},
			Request:         file_inner_processing_rest_alatau_city_payin_proto_message_AlatauCityConfirmThreeDSRequestToGo(label, request),
			RequestMethod:   "Post",
			RequestHeaders:  make(map[string][]string),
			ResponseHeaders: make(map[string][]string),
			HooksData:       make(map[string]any),
		},
	}
	defer func() { s.docFunc(ctx, response.Doc) }()

	defer func() {
		if err != nil {
			ctxzap.AddFields(ctx, zap.Any(label+"error", err))
			response.Error = err
			response.Doc.Error = err

			return
		}

		ctxzap.AddFields(
			ctx,
			file_inner_processing_rest_alatau_city_payin_proto_message_AlatauCityConfirmThreeDSResponseToZap(label+"response.Response", response.Response),
			zap.Any(label+"response.ResponseOutput", response.ResponseOutput),
		)

		response.Doc.Response = file_inner_processing_rest_alatau_city_payin_proto_message_AlatauCityConfirmThreeDSResponseToGo(label, response.Response)
		response.Doc.ResponseOutput = int32(response.ResponseOutput)
	}()

	ctxzap.AddFields(
		ctx,
		file_inner_processing_rest_alatau_city_payin_proto_message_AlatauCityConfirmThreeDSRequestToZap(label+"response.Request", response.Request),
	)

	if err = s.hooksConfirmThreeDS.ValidateArguments(
		ctx,
		request,
		response,
		response.Doc.Logs,
		response.Doc.HooksData,
	); err != nil {
		return
	}

	path := s.baseUrl

	response.Doc.RequestURI = path

	if path, err = s.hooksConfirmThreeDS.MutatePath(
		ctx,
		request,
		response,
		path,
		response.Doc.Logs,
		response.Doc.HooksData,
	); err != nil {
		return
	}

	response.Doc.MutatedRequestURI = path

	var contentType string

	var requestBody []byte
	if s.hooksConfirmThreeDS.CustomMarshal != nil {
		if requestBody, contentType, err = s.hooksConfirmThreeDS.CustomMarshal(
			ctx,
			request,
			response,
			path,
			response.Doc.Logs,
			response.Doc.HooksData,
		); err != nil {
			return
		}
	} else {
		requestForm := url.Values{}
		if request.GetBody().Cres != nil {
			requestForm.Set("cres", request.GetBody().GetCres())
		}

		if request.GetBody().ThreeDSSessionData != nil {
			requestForm.Set("threeDSSessionData", request.GetBody().GetThreeDSSessionData())
		}

		contentType = "application/x-www-form-urlencoded"
		requestBody = []byte(requestForm.Encode())
	}

	if requestBody, err = s.hooksConfirmThreeDS.MutateBody(
		ctx,
		request,
		response,
		requestBody,
		response.Doc.Logs,
		response.Doc.HooksData,
	); err != nil {
		return
	}

	response.Doc.RequestBody = requestBody

	requestReader := bytes.NewReader(requestBody)

	var httpRequest *http.Request
	if httpRequest, err = http.NewRequestWithContext(
		ctx,
		http.MethodPost,
		path,
		requestReader,
	); err != nil {
		return
	}

	httpRequest.Header.Set("Content-Type", contentType)

	if err = s.hooksConfirmThreeDS.BeforeHttpRequest(
		ctx,
		request,
		response,
		httpRequest,
		response.Doc.Logs,
		response.Doc.HooksData,
	); err != nil {
		return
	}

	response.Doc.RequestHeaders = httpRequest.Header

	var httpResponse *http.Response
	if httpResponse, err = http.DefaultClient.Do(httpRequest); err != nil {
		response.Doc.ResponseError = err.Error()
		return
	}
	defer httpResponse.Body.Close()

	response.Doc.ResponseStatus = httpResponse.Status
	response.Doc.ResponseHeaders = httpResponse.Header

	var done bool
	if done, err = s.hooksConfirmThreeDS.AfterHttpResponse(
		ctx,
		request,
		httpRequest,
		httpResponse,
		response,
		response.Doc.Logs,
		response.Doc.HooksData,
	); done || err != nil {
		return
	}

	var rspBody []byte
	if rspBody, err = io.ReadAll(httpResponse.Body); err != nil {
		return
	}

	response.Doc.ResponseBody = rspBody

	if s.hooksConfirmThreeDS.CustomUnmarshal != nil {
		err = s.hooksConfirmThreeDS.CustomUnmarshal(
			ctx,
			request,
			response,
			rspBody,
			response.Doc.Logs,
			response.Doc.HooksData,
		)
		return
	}

	if response.Response.Response, err = wraps.ParseStringPtr[string](string(rspBody)); err == nil {
		response.ResponseOutput = File_inner_processing_rest_alatau_city_payin_proto_AlatauCityBankPayIn_ConfirmThreeDS_Response
		return
	}
	return
}

type File_inner_processing_rest_alatau_city_payin_proto_AlatauCityBankPayIn_ResumeThreeDS int32

const (
	File_inner_processing_rest_alatau_city_payin_proto_AlatauCityBankPayIn_ResumeThreeDS_OutOfScope File_inner_processing_rest_alatau_city_payin_proto_AlatauCityBankPayIn_ResumeThreeDS = iota
	File_inner_processing_rest_alatau_city_payin_proto_AlatauCityBankPayIn_ResumeThreeDS_Response
)

type File_inner_processing_rest_alatau_city_payin_proto_AlatauCityBankPayIn_ResumeThreeDS_Hooks struct {
	ValidateArguments func(
		ctx context.Context,
		request *AlatauCityResumeThreeDSRequest,
		response *AlatauCityBankPayIn_ResumeThreeDS_Response,
		docData map[string]any,
		hooksData map[string]any,
	) error

	MutatePath func(
		ctx context.Context,
		request *AlatauCityResumeThreeDSRequest,
		response *AlatauCityBankPayIn_ResumeThreeDS_Response,
		calculatedPath string,
		docData map[string]any,
		hooksData map[string]any,
	) (mutatePath string, err error)

	CustomMarshal func(
		ctx context.Context,
		request *AlatauCityResumeThreeDSRequest,
		response *AlatauCityBankPayIn_ResumeThreeDS_Response,
		path string,
		docData map[string]any,
		hooksData map[string]any,
	) (body []byte, contentType string, err error)

	MutateBody func(
		ctx context.Context,
		request *AlatauCityResumeThreeDSRequest,
		response *AlatauCityBankPayIn_ResumeThreeDS_Response,
		body []byte,
		docData map[string]any,
		hooksData map[string]any,
	) (mutatedBody []byte, err error)

	BeforeHttpRequest func(
		ctx context.Context,
		request *AlatauCityResumeThreeDSRequest,
		response *AlatauCityBankPayIn_ResumeThreeDS_Response,
		httpRequest *http.Request,
		docData map[string]any,
		hooksData map[string]any,
	) error

	AfterHttpResponse func(
		ctx context.Context,
		request *AlatauCityResumeThreeDSRequest,
		httpRequest *http.Request,
		httpResponse *http.Response,
		response *AlatauCityBankPayIn_ResumeThreeDS_Response,
		docData map[string]any,
		hooksData map[string]any,
	) (done bool, err error)

	CustomUnmarshal func(
		ctx context.Context,
		request *AlatauCityResumeThreeDSRequest,
		response *AlatauCityBankPayIn_ResumeThreeDS_Response,
		rspBody []byte,
		docData map[string]any,
		hooksData map[string]any,
	) error
}

func NewFile_inner_processing_rest_alatau_city_payin_proto_AlatauCityBankPayIn_ResumeThreeDS_Hooks() *File_inner_processing_rest_alatau_city_payin_proto_AlatauCityBankPayIn_ResumeThreeDS_Hooks {
	return &File_inner_processing_rest_alatau_city_payin_proto_AlatauCityBankPayIn_ResumeThreeDS_Hooks{
		ValidateArguments: func(
			ctx context.Context,
			request *AlatauCityResumeThreeDSRequest,
			response *AlatauCityBankPayIn_ResumeThreeDS_Response,
			docData map[string]any,
			hooksData map[string]any,
		) error {
			return nil
		},
		MutatePath: func(
			ctx context.Context,
			request *AlatauCityResumeThreeDSRequest,
			response *AlatauCityBankPayIn_ResumeThreeDS_Response,
			calculatedPath string,
			docData map[string]any,
			hooksData map[string]any,
		) (mutatePath string, err error) {
			return calculatedPath, nil
		},
		MutateBody: func(
			ctx context.Context,
			request *AlatauCityResumeThreeDSRequest,
			response *AlatauCityBankPayIn_ResumeThreeDS_Response,
			body []byte,
			docData map[string]any,
			hooksData map[string]any,
		) (mutatedBody []byte, err error) {
			return body, nil
		},
		BeforeHttpRequest: func(
			ctx context.Context,
			request *AlatauCityResumeThreeDSRequest,
			response *AlatauCityBankPayIn_ResumeThreeDS_Response,
			httpRequest *http.Request,
			docData map[string]any,
			hooksData map[string]any,
		) error {
			return nil
		},
		AfterHttpResponse: func(
			ctx context.Context,
			request *AlatauCityResumeThreeDSRequest,
			httpRequest *http.Request,
			httpResponse *http.Response,
			response *AlatauCityBankPayIn_ResumeThreeDS_Response,
			docData map[string]any,
			hooksData map[string]any,
		) (done bool, err error) {
			return false, nil
		},
	}
}

func PrepareFile_inner_processing_rest_alatau_city_payin_proto_AlatauCityBankPayIn_ResumeThreeDS_Hooks(
	in *File_inner_processing_rest_alatau_city_payin_proto_AlatauCityBankPayIn_ResumeThreeDS_Hooks,
) *File_inner_processing_rest_alatau_city_payin_proto_AlatauCityBankPayIn_ResumeThreeDS_Hooks {
	defaultHook := NewFile_inner_processing_rest_alatau_city_payin_proto_AlatauCityBankPayIn_ResumeThreeDS_Hooks()
	if in == nil {
		return defaultHook
	}

	if in.ValidateArguments == nil {
		in.ValidateArguments = defaultHook.ValidateArguments
	}

	if in.MutatePath == nil {
		in.MutatePath = defaultHook.MutatePath
	}
	if in.MutateBody == nil {
		in.MutateBody = defaultHook.MutateBody
	}

	if in.BeforeHttpRequest == nil {
		in.BeforeHttpRequest = defaultHook.BeforeHttpRequest
	}

	if in.AfterHttpResponse == nil {
		in.AfterHttpResponse = defaultHook.AfterHttpResponse
	}

	return in
}

type AlatauCityBankPayIn_ResumeThreeDS_Response struct {
	Request        *AlatauCityResumeThreeDSRequest
	Response       *AlatauCityResumeThreeDSResponse
	ResponseOutput File_inner_processing_rest_alatau_city_payin_proto_AlatauCityBankPayIn_ResumeThreeDS
	Error          error
	Doc            *doc.Struct
}

func (s *restAlatauCityBankPayInServer) ResumeThreeDS(
	ctx context.Context,
	request *AlatauCityResumeThreeDSRequest,
) (
	response *AlatauCityBankPayIn_ResumeThreeDS_Response,
	err error,
) {
	label := cntx.Begin(ctx, "AlatauCityBankPayInServer_ResumeThreeDS")
	defer cntx.End(ctx, label)

	response = &AlatauCityBankPayIn_ResumeThreeDS_Response{
		Request:        request,
		Response:       &AlatauCityResumeThreeDSResponse{},
		ResponseOutput: File_inner_processing_rest_alatau_city_payin_proto_AlatauCityBankPayIn_ResumeThreeDS_OutOfScope,
		Error:          nil,
		Doc: &doc.Struct{
			UUID:            uuid.New(),
			CreatedAt:       time.Now(),
			CreatedAtNano:   time.Now().UnixNano(),
			FuncName:        "AlatauCityBankPayInServer_ResumeThreeDS",
			Label:           label,
			Logs:            make(map[string]any),
			Arguments:       map[string]any{},
			Request:         file_inner_processing_rest_alatau_city_payin_proto_message_AlatauCityResumeThreeDSRequestToGo(label, request),
			RequestMethod:   "Post",
			RequestHeaders:  make(map[string][]string),
			ResponseHeaders: make(map[string][]string),
			HooksData:       make(map[string]any),
		},
	}
	defer func() { s.docFunc(ctx, response.Doc) }()

	defer func() {
		if err != nil {
			ctxzap.AddFields(ctx, zap.Any(label+"error", err))
			response.Error = err
			response.Doc.Error = err

			return
		}

		ctxzap.AddFields(
			ctx,
			file_inner_processing_rest_alatau_city_payin_proto_message_AlatauCityResumeThreeDSResponseToZap(label+"response.Response", response.Response),
			zap.Any(label+"response.ResponseOutput", response.ResponseOutput),
		)

		response.Doc.Response = file_inner_processing_rest_alatau_city_payin_proto_message_AlatauCityResumeThreeDSResponseToGo(label, response.Response)
		response.Doc.ResponseOutput = int32(response.ResponseOutput)
	}()

	ctxzap.AddFields(
		ctx,
		file_inner_processing_rest_alatau_city_payin_proto_message_AlatauCityResumeThreeDSRequestToZap(label+"response.Request", response.Request),
	)

	if err = s.hooksResumeThreeDS.ValidateArguments(
		ctx,
		request,
		response,
		response.Doc.Logs,
		response.Doc.HooksData,
	); err != nil {
		return
	}

	path := s.baseUrl

	response.Doc.RequestURI = path

	if path, err = s.hooksResumeThreeDS.MutatePath(
		ctx,
		request,
		response,
		path,
		response.Doc.Logs,
		response.Doc.HooksData,
	); err != nil {
		return
	}

	response.Doc.MutatedRequestURI = path

	var contentType string

	var requestBody []byte
	if s.hooksResumeThreeDS.CustomMarshal != nil {
		if requestBody, contentType, err = s.hooksResumeThreeDS.CustomMarshal(
			ctx,
			request,
			response,
			path,
			response.Doc.Logs,
			response.Doc.HooksData,
		); err != nil {
			return
		}
	} else {
		requestForm := url.Values{}
		if request.GetBody().ThreeDSMethodData != nil {
			requestForm.Set("threeDSMethodData", request.GetBody().GetThreeDSMethodData())
		}

		if request.GetBody().ThreeDSMethodState != nil {
			requestForm.Set("threeDSMethodState", request.GetBody().GetThreeDSMethodState())
		}

		contentType = "application/x-www-form-urlencoded"
		requestBody = []byte(requestForm.Encode())
	}

	if requestBody, err = s.hooksResumeThreeDS.MutateBody(
		ctx,
		request,
		response,
		requestBody,
		response.Doc.Logs,
		response.Doc.HooksData,
	); err != nil {
		return
	}

	response.Doc.RequestBody = requestBody

	requestReader := bytes.NewReader(requestBody)

	var httpRequest *http.Request
	if httpRequest, err = http.NewRequestWithContext(
		ctx,
		http.MethodPost,
		path,
		requestReader,
	); err != nil {
		return
	}

	httpRequest.Header.Set("Content-Type", contentType)

	if err = s.hooksResumeThreeDS.BeforeHttpRequest(
		ctx,
		request,
		response,
		httpRequest,
		response.Doc.Logs,
		response.Doc.HooksData,
	); err != nil {
		return
	}

	response.Doc.RequestHeaders = httpRequest.Header

	var httpResponse *http.Response
	if httpResponse, err = http.DefaultClient.Do(httpRequest); err != nil {
		response.Doc.ResponseError = err.Error()
		return
	}
	defer httpResponse.Body.Close()

	response.Doc.ResponseStatus = httpResponse.Status
	response.Doc.ResponseHeaders = httpResponse.Header

	var done bool
	if done, err = s.hooksResumeThreeDS.AfterHttpResponse(
		ctx,
		request,
		httpRequest,
		httpResponse,
		response,
		response.Doc.Logs,
		response.Doc.HooksData,
	); done || err != nil {
		return
	}

	var rspBody []byte
	if rspBody, err = io.ReadAll(httpResponse.Body); err != nil {
		return
	}

	response.Doc.ResponseBody = rspBody

	if s.hooksResumeThreeDS.CustomUnmarshal != nil {
		err = s.hooksResumeThreeDS.CustomUnmarshal(
			ctx,
			request,
			response,
			rspBody,
			response.Doc.Logs,
			response.Doc.HooksData,
		)
		return
	}

	if response.Response.Response, err = wraps.ParseStringPtr[string](string(rspBody)); err == nil {
		response.ResponseOutput = File_inner_processing_rest_alatau_city_payin_proto_AlatauCityBankPayIn_ResumeThreeDS_Response
		return
	}
	return
}

type File_inner_processing_rest_alatau_city_payin_proto_AlatauCityBankPayIn_SendForm int32

const (
	File_inner_processing_rest_alatau_city_payin_proto_AlatauCityBankPayIn_SendForm_OutOfScope File_inner_processing_rest_alatau_city_payin_proto_AlatauCityBankPayIn_SendForm = iota
	File_inner_processing_rest_alatau_city_payin_proto_AlatauCityBankPayIn_SendForm_Response
)

type File_inner_processing_rest_alatau_city_payin_proto_AlatauCityBankPayIn_SendForm_Hooks struct {
	ValidateArguments func(
		ctx context.Context,
		request *AlatauCitySendFormRequest,
		response *AlatauCityBankPayIn_SendForm_Response,
		docData map[string]any,
		hooksData map[string]any,
	) error

	MutatePath func(
		ctx context.Context,
		request *AlatauCitySendFormRequest,
		response *AlatauCityBankPayIn_SendForm_Response,
		calculatedPath string,
		docData map[string]any,
		hooksData map[string]any,
	) (mutatePath string, err error)

	CustomMarshal func(
		ctx context.Context,
		request *AlatauCitySendFormRequest,
		response *AlatauCityBankPayIn_SendForm_Response,
		path string,
		docData map[string]any,
		hooksData map[string]any,
	) (body []byte, contentType string, err error)

	MutateBody func(
		ctx context.Context,
		request *AlatauCitySendFormRequest,
		response *AlatauCityBankPayIn_SendForm_Response,
		body []byte,
		docData map[string]any,
		hooksData map[string]any,
	) (mutatedBody []byte, err error)

	BeforeHttpRequest func(
		ctx context.Context,
		request *AlatauCitySendFormRequest,
		response *AlatauCityBankPayIn_SendForm_Response,
		httpRequest *http.Request,
		docData map[string]any,
		hooksData map[string]any,
	) error

	AfterHttpResponse func(
		ctx context.Context,
		request *AlatauCitySendFormRequest,
		httpRequest *http.Request,
		httpResponse *http.Response,
		response *AlatauCityBankPayIn_SendForm_Response,
		docData map[string]any,
		hooksData map[string]any,
	) (done bool, err error)

	CustomUnmarshal func(
		ctx context.Context,
		request *AlatauCitySendFormRequest,
		response *AlatauCityBankPayIn_SendForm_Response,
		rspBody []byte,
		docData map[string]any,
		hooksData map[string]any,
	) error
}

func NewFile_inner_processing_rest_alatau_city_payin_proto_AlatauCityBankPayIn_SendForm_Hooks() *File_inner_processing_rest_alatau_city_payin_proto_AlatauCityBankPayIn_SendForm_Hooks {
	return &File_inner_processing_rest_alatau_city_payin_proto_AlatauCityBankPayIn_SendForm_Hooks{
		ValidateArguments: func(
			ctx context.Context,
			request *AlatauCitySendFormRequest,
			response *AlatauCityBankPayIn_SendForm_Response,
			docData map[string]any,
			hooksData map[string]any,
		) error {
			return nil
		},
		MutatePath: func(
			ctx context.Context,
			request *AlatauCitySendFormRequest,
			response *AlatauCityBankPayIn_SendForm_Response,
			calculatedPath string,
			docData map[string]any,
			hooksData map[string]any,
		) (mutatePath string, err error) {
			return calculatedPath, nil
		},
		MutateBody: func(
			ctx context.Context,
			request *AlatauCitySendFormRequest,
			response *AlatauCityBankPayIn_SendForm_Response,
			body []byte,
			docData map[string]any,
			hooksData map[string]any,
		) (mutatedBody []byte, err error) {
			return body, nil
		},
		BeforeHttpRequest: func(
			ctx context.Context,
			request *AlatauCitySendFormRequest,
			response *AlatauCityBankPayIn_SendForm_Response,
			httpRequest *http.Request,
			docData map[string]any,
			hooksData map[string]any,
		) error {
			return nil
		},
		AfterHttpResponse: func(
			ctx context.Context,
			request *AlatauCitySendFormRequest,
			httpRequest *http.Request,
			httpResponse *http.Response,
			response *AlatauCityBankPayIn_SendForm_Response,
			docData map[string]any,
			hooksData map[string]any,
		) (done bool, err error) {
			return false, nil
		},
	}
}

func PrepareFile_inner_processing_rest_alatau_city_payin_proto_AlatauCityBankPayIn_SendForm_Hooks(
	in *File_inner_processing_rest_alatau_city_payin_proto_AlatauCityBankPayIn_SendForm_Hooks,
) *File_inner_processing_rest_alatau_city_payin_proto_AlatauCityBankPayIn_SendForm_Hooks {
	defaultHook := NewFile_inner_processing_rest_alatau_city_payin_proto_AlatauCityBankPayIn_SendForm_Hooks()
	if in == nil {
		return defaultHook
	}

	if in.ValidateArguments == nil {
		in.ValidateArguments = defaultHook.ValidateArguments
	}

	if in.MutatePath == nil {
		in.MutatePath = defaultHook.MutatePath
	}
	if in.MutateBody == nil {
		in.MutateBody = defaultHook.MutateBody
	}

	if in.BeforeHttpRequest == nil {
		in.BeforeHttpRequest = defaultHook.BeforeHttpRequest
	}

	if in.AfterHttpResponse == nil {
		in.AfterHttpResponse = defaultHook.AfterHttpResponse
	}

	return in
}

type AlatauCityBankPayIn_SendForm_Response struct {
	Request        *AlatauCitySendFormRequest
	Response       *AlatauCitySendFormResponse
	ResponseOutput File_inner_processing_rest_alatau_city_payin_proto_AlatauCityBankPayIn_SendForm
	Error          error
	Doc            *doc.Struct
}

func (s *restAlatauCityBankPayInServer) SendForm(
	ctx context.Context,
	request *AlatauCitySendFormRequest,
) (
	response *AlatauCityBankPayIn_SendForm_Response,
	err error,
) {
	label := cntx.Begin(ctx, "AlatauCityBankPayInServer_SendForm")
	defer cntx.End(ctx, label)

	response = &AlatauCityBankPayIn_SendForm_Response{
		Request:        request,
		Response:       &AlatauCitySendFormResponse{},
		ResponseOutput: File_inner_processing_rest_alatau_city_payin_proto_AlatauCityBankPayIn_SendForm_OutOfScope,
		Error:          nil,
		Doc: &doc.Struct{
			UUID:            uuid.New(),
			CreatedAt:       time.Now(),
			CreatedAtNano:   time.Now().UnixNano(),
			FuncName:        "AlatauCityBankPayInServer_SendForm",
			Label:           label,
			Logs:            make(map[string]any),
			Arguments:       map[string]any{},
			Request:         file_inner_processing_rest_alatau_city_payin_proto_message_AlatauCitySendFormRequestToGo(label, request),
			RequestMethod:   "Post",
			RequestHeaders:  make(map[string][]string),
			ResponseHeaders: make(map[string][]string),
			HooksData:       make(map[string]any),
		},
	}
	defer func() { s.docFunc(ctx, response.Doc) }()

	defer func() {
		if err != nil {
			ctxzap.AddFields(ctx, zap.Any(label+"error", err))
			response.Error = err
			response.Doc.Error = err

			return
		}

		ctxzap.AddFields(
			ctx,
			file_inner_processing_rest_alatau_city_payin_proto_message_AlatauCitySendFormResponseToZap(label+"response.Response", response.Response),
			zap.Any(label+"response.ResponseOutput", response.ResponseOutput),
		)

		response.Doc.Response = file_inner_processing_rest_alatau_city_payin_proto_message_AlatauCitySendFormResponseToGo(label, response.Response)
		response.Doc.ResponseOutput = int32(response.ResponseOutput)
	}()

	ctxzap.AddFields(
		ctx,
		file_inner_processing_rest_alatau_city_payin_proto_message_AlatauCitySendFormRequestToZap(label+"response.Request", response.Request),
	)

	if err = s.hooksSendForm.ValidateArguments(
		ctx,
		request,
		response,
		response.Doc.Logs,
		response.Doc.HooksData,
	); err != nil {
		return
	}

	path := s.baseUrl

	response.Doc.RequestURI = path

	if path, err = s.hooksSendForm.MutatePath(
		ctx,
		request,
		response,
		path,
		response.Doc.Logs,
		response.Doc.HooksData,
	); err != nil {
		return
	}

	response.Doc.MutatedRequestURI = path

	var contentType string

	var requestBody []byte
	if s.hooksSendForm.CustomMarshal != nil {
		if requestBody, contentType, err = s.hooksSendForm.CustomMarshal(
			ctx,
			request,
			response,
			path,
			response.Doc.Logs,
			response.Doc.HooksData,
		); err != nil {
			return
		}
	} else {
		requestForm := url.Values{}
		if request.GetBody().ThreeDSMethodData != nil {
			requestForm.Set("threeDSMethodData", request.GetBody().GetThreeDSMethodData())
		}

		contentType = "application/x-www-form-urlencoded"
		requestBody = []byte(requestForm.Encode())
	}

	if requestBody, err = s.hooksSendForm.MutateBody(
		ctx,
		request,
		response,
		requestBody,
		response.Doc.Logs,
		response.Doc.HooksData,
	); err != nil {
		return
	}

	response.Doc.RequestBody = requestBody

	requestReader := bytes.NewReader(requestBody)

	var httpRequest *http.Request
	if httpRequest, err = http.NewRequestWithContext(
		ctx,
		http.MethodPost,
		path,
		requestReader,
	); err != nil {
		return
	}

	httpRequest.Header.Set("Content-Type", contentType)

	if err = s.hooksSendForm.BeforeHttpRequest(
		ctx,
		request,
		response,
		httpRequest,
		response.Doc.Logs,
		response.Doc.HooksData,
	); err != nil {
		return
	}

	response.Doc.RequestHeaders = httpRequest.Header

	var httpResponse *http.Response
	if httpResponse, err = http.DefaultClient.Do(httpRequest); err != nil {
		response.Doc.ResponseError = err.Error()
		return
	}
	defer httpResponse.Body.Close()

	response.Doc.ResponseStatus = httpResponse.Status
	response.Doc.ResponseHeaders = httpResponse.Header

	var done bool
	if done, err = s.hooksSendForm.AfterHttpResponse(
		ctx,
		request,
		httpRequest,
		httpResponse,
		response,
		response.Doc.Logs,
		response.Doc.HooksData,
	); done || err != nil {
		return
	}

	var rspBody []byte
	if rspBody, err = io.ReadAll(httpResponse.Body); err != nil {
		return
	}

	response.Doc.ResponseBody = rspBody

	if s.hooksSendForm.CustomUnmarshal != nil {
		err = s.hooksSendForm.CustomUnmarshal(
			ctx,
			request,
			response,
			rspBody,
			response.Doc.Logs,
			response.Doc.HooksData,
		)
		return
	}

	if response.Response.Response, err = wraps.ParseStringPtr[string](string(rspBody)); err == nil {
		response.ResponseOutput = File_inner_processing_rest_alatau_city_payin_proto_AlatauCityBankPayIn_SendForm_Response
		return
	}
	return
}

type File_inner_processing_rest_alatau_city_payin_proto_AlatauCityBankPayIn_CancelPayment int32

const (
	File_inner_processing_rest_alatau_city_payin_proto_AlatauCityBankPayIn_CancelPayment_OutOfScope File_inner_processing_rest_alatau_city_payin_proto_AlatauCityBankPayIn_CancelPayment = iota
	File_inner_processing_rest_alatau_city_payin_proto_AlatauCityBankPayIn_CancelPayment_CancelResponse
)

type File_inner_processing_rest_alatau_city_payin_proto_AlatauCityBankPayIn_CancelPayment_Hooks struct {
	ValidateArguments func(
		ctx context.Context,
		request *AlatauCityCancelPaymentRequest,
		response *AlatauCityBankPayIn_CancelPayment_Response,
		docData map[string]any,
		hooksData map[string]any,
	) error

	MutatePath func(
		ctx context.Context,
		request *AlatauCityCancelPaymentRequest,
		response *AlatauCityBankPayIn_CancelPayment_Response,
		calculatedPath string,
		docData map[string]any,
		hooksData map[string]any,
	) (mutatePath string, err error)

	CustomMarshal func(
		ctx context.Context,
		request *AlatauCityCancelPaymentRequest,
		response *AlatauCityBankPayIn_CancelPayment_Response,
		path string,
		docData map[string]any,
		hooksData map[string]any,
	) (body []byte, contentType string, err error)

	MutateBody func(
		ctx context.Context,
		request *AlatauCityCancelPaymentRequest,
		response *AlatauCityBankPayIn_CancelPayment_Response,
		body []byte,
		docData map[string]any,
		hooksData map[string]any,
	) (mutatedBody []byte, err error)

	BeforeHttpRequest func(
		ctx context.Context,
		request *AlatauCityCancelPaymentRequest,
		response *AlatauCityBankPayIn_CancelPayment_Response,
		httpRequest *http.Request,
		docData map[string]any,
		hooksData map[string]any,
	) error

	AfterHttpResponse func(
		ctx context.Context,
		request *AlatauCityCancelPaymentRequest,
		httpRequest *http.Request,
		httpResponse *http.Response,
		response *AlatauCityBankPayIn_CancelPayment_Response,
		docData map[string]any,
		hooksData map[string]any,
	) (done bool, err error)

	CustomUnmarshal func(
		ctx context.Context,
		request *AlatauCityCancelPaymentRequest,
		response *AlatauCityBankPayIn_CancelPayment_Response,
		rspBody []byte,
		docData map[string]any,
		hooksData map[string]any,
	) error
}

func NewFile_inner_processing_rest_alatau_city_payin_proto_AlatauCityBankPayIn_CancelPayment_Hooks() *File_inner_processing_rest_alatau_city_payin_proto_AlatauCityBankPayIn_CancelPayment_Hooks {
	return &File_inner_processing_rest_alatau_city_payin_proto_AlatauCityBankPayIn_CancelPayment_Hooks{
		ValidateArguments: func(
			ctx context.Context,
			request *AlatauCityCancelPaymentRequest,
			response *AlatauCityBankPayIn_CancelPayment_Response,
			docData map[string]any,
			hooksData map[string]any,
		) error {
			return nil
		},
		MutatePath: func(
			ctx context.Context,
			request *AlatauCityCancelPaymentRequest,
			response *AlatauCityBankPayIn_CancelPayment_Response,
			calculatedPath string,
			docData map[string]any,
			hooksData map[string]any,
		) (mutatePath string, err error) {
			return calculatedPath, nil
		},
		MutateBody: func(
			ctx context.Context,
			request *AlatauCityCancelPaymentRequest,
			response *AlatauCityBankPayIn_CancelPayment_Response,
			body []byte,
			docData map[string]any,
			hooksData map[string]any,
		) (mutatedBody []byte, err error) {
			return body, nil
		},
		BeforeHttpRequest: func(
			ctx context.Context,
			request *AlatauCityCancelPaymentRequest,
			response *AlatauCityBankPayIn_CancelPayment_Response,
			httpRequest *http.Request,
			docData map[string]any,
			hooksData map[string]any,
		) error {
			return nil
		},
		AfterHttpResponse: func(
			ctx context.Context,
			request *AlatauCityCancelPaymentRequest,
			httpRequest *http.Request,
			httpResponse *http.Response,
			response *AlatauCityBankPayIn_CancelPayment_Response,
			docData map[string]any,
			hooksData map[string]any,
		) (done bool, err error) {
			return false, nil
		},
	}
}

func PrepareFile_inner_processing_rest_alatau_city_payin_proto_AlatauCityBankPayIn_CancelPayment_Hooks(
	in *File_inner_processing_rest_alatau_city_payin_proto_AlatauCityBankPayIn_CancelPayment_Hooks,
) *File_inner_processing_rest_alatau_city_payin_proto_AlatauCityBankPayIn_CancelPayment_Hooks {
	defaultHook := NewFile_inner_processing_rest_alatau_city_payin_proto_AlatauCityBankPayIn_CancelPayment_Hooks()
	if in == nil {
		return defaultHook
	}

	if in.ValidateArguments == nil {
		in.ValidateArguments = defaultHook.ValidateArguments
	}

	if in.MutatePath == nil {
		in.MutatePath = defaultHook.MutatePath
	}
	if in.MutateBody == nil {
		in.MutateBody = defaultHook.MutateBody
	}

	if in.BeforeHttpRequest == nil {
		in.BeforeHttpRequest = defaultHook.BeforeHttpRequest
	}

	if in.AfterHttpResponse == nil {
		in.AfterHttpResponse = defaultHook.AfterHttpResponse
	}

	return in
}

type AlatauCityBankPayIn_CancelPayment_Response struct {
	Request        *AlatauCityCancelPaymentRequest
	Response       *AlatauCityCancelPaymentResponse
	ResponseOutput File_inner_processing_rest_alatau_city_payin_proto_AlatauCityBankPayIn_CancelPayment
	Error          error
	Doc            *doc.Struct
}

func (s *restAlatauCityBankPayInServer) CancelPayment(
	ctx context.Context,
	request *AlatauCityCancelPaymentRequest,
) (
	response *AlatauCityBankPayIn_CancelPayment_Response,
	err error,
) {
	label := cntx.Begin(ctx, "AlatauCityBankPayInServer_CancelPayment")
	defer cntx.End(ctx, label)

	response = &AlatauCityBankPayIn_CancelPayment_Response{
		Request:        request,
		Response:       &AlatauCityCancelPaymentResponse{},
		ResponseOutput: File_inner_processing_rest_alatau_city_payin_proto_AlatauCityBankPayIn_CancelPayment_OutOfScope,
		Error:          nil,
		Doc: &doc.Struct{
			UUID:          uuid.New(),
			CreatedAt:     time.Now(),
			CreatedAtNano: time.Now().UnixNano(),
			FuncName:      "AlatauCityBankPayInServer_CancelPayment",
			Label:         label,
			Logs:          make(map[string]any),
			Arguments: map[string]any{
				"clear_path": "/ecom/api",
			},
			Request:         file_inner_processing_rest_alatau_city_payin_proto_message_AlatauCityCancelPaymentRequestToGo(label, request),
			RequestMethod:   "Post",
			RequestHeaders:  make(map[string][]string),
			ResponseHeaders: make(map[string][]string),
			HooksData:       make(map[string]any),
		},
	}
	defer func() { s.docFunc(ctx, response.Doc) }()

	defer func() {
		if err != nil {
			ctxzap.AddFields(ctx, zap.Any(label+"error", err))
			response.Error = err
			response.Doc.Error = err

			return
		}

		ctxzap.AddFields(
			ctx,
			file_inner_processing_rest_alatau_city_payin_proto_message_AlatauCityCancelPaymentResponseToZap(label+"response.Response", response.Response),
			zap.Any(label+"response.ResponseOutput", response.ResponseOutput),
		)

		response.Doc.Response = file_inner_processing_rest_alatau_city_payin_proto_message_AlatauCityCancelPaymentResponseToGo(label, response.Response)
		response.Doc.ResponseOutput = int32(response.ResponseOutput)
	}()

	ctxzap.AddFields(
		ctx,
		file_inner_processing_rest_alatau_city_payin_proto_message_AlatauCityCancelPaymentRequestToZap(label+"response.Request", response.Request),
		zap.Any(label+"clear_path", "/ecom/api"),
	)

	if err = s.hooksCancelPayment.ValidateArguments(
		ctx,
		request,
		response,
		response.Doc.Logs,
		response.Doc.HooksData,
	); err != nil {
		return
	}

	path := s.baseUrl + "/ecom/api"

	response.Doc.RequestURI = path

	if path, err = s.hooksCancelPayment.MutatePath(
		ctx,
		request,
		response,
		path,
		response.Doc.Logs,
		response.Doc.HooksData,
	); err != nil {
		return
	}

	response.Doc.MutatedRequestURI = path

	var contentType string

	var requestBody []byte
	if s.hooksCancelPayment.CustomMarshal != nil {
		if requestBody, contentType, err = s.hooksCancelPayment.CustomMarshal(
			ctx,
			request,
			response,
			path,
			response.Doc.Logs,
			response.Doc.HooksData,
		); err != nil {
			return
		}
	} else {
		requestForm := url.Values{}
		if request.GetBody().ORDER != nil {
			requestForm.Set("ORDER", request.GetBody().GetORDER())
		}

		if request.GetBody().MERCHANT != nil {
			requestForm.Set("MERCHANT", request.GetBody().GetMERCHANT())
		}

		if request.GetBody().P_SIGN != nil {
			requestForm.Set("P_SIGN", request.GetBody().GetP_SIGN())
		}

		if request.GetBody().REV_AMOUNT != nil {
			requestForm.Set("REV_AMOUNT", request.GetBody().GetREV_AMOUNT())
		}

		if request.GetBody().REV_DESC != nil {
			requestForm.Set("REV_DESC", request.GetBody().GetREV_DESC())
		}

		contentType = "application/x-www-form-urlencoded"
		requestBody = []byte(requestForm.Encode())
	}

	if requestBody, err = s.hooksCancelPayment.MutateBody(
		ctx,
		request,
		response,
		requestBody,
		response.Doc.Logs,
		response.Doc.HooksData,
	); err != nil {
		return
	}

	response.Doc.RequestBody = requestBody

	requestReader := bytes.NewReader(requestBody)

	var httpRequest *http.Request
	if httpRequest, err = http.NewRequestWithContext(
		ctx,
		http.MethodPost,
		path,
		requestReader,
	); err != nil {
		return
	}

	httpRequest.Header.Set("Content-Type", contentType)

	if err = s.hooksCancelPayment.BeforeHttpRequest(
		ctx,
		request,
		response,
		httpRequest,
		response.Doc.Logs,
		response.Doc.HooksData,
	); err != nil {
		return
	}

	response.Doc.RequestHeaders = httpRequest.Header

	var httpResponse *http.Response
	if httpResponse, err = http.DefaultClient.Do(httpRequest); err != nil {
		response.Doc.ResponseError = err.Error()
		return
	}
	defer httpResponse.Body.Close()

	response.Doc.ResponseStatus = httpResponse.Status
	response.Doc.ResponseHeaders = httpResponse.Header

	var done bool
	if done, err = s.hooksCancelPayment.AfterHttpResponse(
		ctx,
		request,
		httpRequest,
		httpResponse,
		response,
		response.Doc.Logs,
		response.Doc.HooksData,
	); done || err != nil {
		return
	}

	var rspBody []byte
	if rspBody, err = io.ReadAll(httpResponse.Body); err != nil {
		return
	}

	response.Doc.ResponseBody = rspBody

	if s.hooksCancelPayment.CustomUnmarshal != nil {
		err = s.hooksCancelPayment.CustomUnmarshal(
			ctx,
			request,
			response,
			rspBody,
			response.Doc.Logs,
			response.Doc.HooksData,
		)
		return
	}

	if response.Response.CancelResponse, err = marshaller.XMLUnmarshalPtr[AlatauCityCancelPaymentResponse_CancelPaymentResponse](rspBody); err == nil {
		response.ResponseOutput = File_inner_processing_rest_alatau_city_payin_proto_AlatauCityBankPayIn_CancelPayment_CancelResponse
		return
	}
	return
}

type File_inner_processing_rest_alatau_city_payin_proto_AlatauCityBankPayIn_RegisterToken int32

const (
	File_inner_processing_rest_alatau_city_payin_proto_AlatauCityBankPayIn_RegisterToken_OutOfScope File_inner_processing_rest_alatau_city_payin_proto_AlatauCityBankPayIn_RegisterToken = iota
	File_inner_processing_rest_alatau_city_payin_proto_AlatauCityBankPayIn_RegisterToken_TokenResponse
)

type File_inner_processing_rest_alatau_city_payin_proto_AlatauCityBankPayIn_RegisterToken_Hooks struct {
	ValidateArguments func(
		ctx context.Context,
		request *AlatauCityRegisterTokenRequest,
		response *AlatauCityBankPayIn_RegisterToken_Response,
		docData map[string]any,
		hooksData map[string]any,
	) error

	MutatePath func(
		ctx context.Context,
		request *AlatauCityRegisterTokenRequest,
		response *AlatauCityBankPayIn_RegisterToken_Response,
		calculatedPath string,
		docData map[string]any,
		hooksData map[string]any,
	) (mutatePath string, err error)

	CustomMarshal func(
		ctx context.Context,
		request *AlatauCityRegisterTokenRequest,
		response *AlatauCityBankPayIn_RegisterToken_Response,
		path string,
		docData map[string]any,
		hooksData map[string]any,
	) (body []byte, contentType string, err error)

	MutateBody func(
		ctx context.Context,
		request *AlatauCityRegisterTokenRequest,
		response *AlatauCityBankPayIn_RegisterToken_Response,
		body []byte,
		docData map[string]any,
		hooksData map[string]any,
	) (mutatedBody []byte, err error)

	BeforeHttpRequest func(
		ctx context.Context,
		request *AlatauCityRegisterTokenRequest,
		response *AlatauCityBankPayIn_RegisterToken_Response,
		httpRequest *http.Request,
		docData map[string]any,
		hooksData map[string]any,
	) error

	AfterHttpResponse func(
		ctx context.Context,
		request *AlatauCityRegisterTokenRequest,
		httpRequest *http.Request,
		httpResponse *http.Response,
		response *AlatauCityBankPayIn_RegisterToken_Response,
		docData map[string]any,
		hooksData map[string]any,
	) (done bool, err error)

	CustomUnmarshal func(
		ctx context.Context,
		request *AlatauCityRegisterTokenRequest,
		response *AlatauCityBankPayIn_RegisterToken_Response,
		rspBody []byte,
		docData map[string]any,
		hooksData map[string]any,
	) error
}

func NewFile_inner_processing_rest_alatau_city_payin_proto_AlatauCityBankPayIn_RegisterToken_Hooks() *File_inner_processing_rest_alatau_city_payin_proto_AlatauCityBankPayIn_RegisterToken_Hooks {
	return &File_inner_processing_rest_alatau_city_payin_proto_AlatauCityBankPayIn_RegisterToken_Hooks{
		ValidateArguments: func(
			ctx context.Context,
			request *AlatauCityRegisterTokenRequest,
			response *AlatauCityBankPayIn_RegisterToken_Response,
			docData map[string]any,
			hooksData map[string]any,
		) error {
			return nil
		},
		MutatePath: func(
			ctx context.Context,
			request *AlatauCityRegisterTokenRequest,
			response *AlatauCityBankPayIn_RegisterToken_Response,
			calculatedPath string,
			docData map[string]any,
			hooksData map[string]any,
		) (mutatePath string, err error) {
			return calculatedPath, nil
		},
		MutateBody: func(
			ctx context.Context,
			request *AlatauCityRegisterTokenRequest,
			response *AlatauCityBankPayIn_RegisterToken_Response,
			body []byte,
			docData map[string]any,
			hooksData map[string]any,
		) (mutatedBody []byte, err error) {
			return body, nil
		},
		BeforeHttpRequest: func(
			ctx context.Context,
			request *AlatauCityRegisterTokenRequest,
			response *AlatauCityBankPayIn_RegisterToken_Response,
			httpRequest *http.Request,
			docData map[string]any,
			hooksData map[string]any,
		) error {
			return nil
		},
		AfterHttpResponse: func(
			ctx context.Context,
			request *AlatauCityRegisterTokenRequest,
			httpRequest *http.Request,
			httpResponse *http.Response,
			response *AlatauCityBankPayIn_RegisterToken_Response,
			docData map[string]any,
			hooksData map[string]any,
		) (done bool, err error) {
			return false, nil
		},
	}
}

func PrepareFile_inner_processing_rest_alatau_city_payin_proto_AlatauCityBankPayIn_RegisterToken_Hooks(
	in *File_inner_processing_rest_alatau_city_payin_proto_AlatauCityBankPayIn_RegisterToken_Hooks,
) *File_inner_processing_rest_alatau_city_payin_proto_AlatauCityBankPayIn_RegisterToken_Hooks {
	defaultHook := NewFile_inner_processing_rest_alatau_city_payin_proto_AlatauCityBankPayIn_RegisterToken_Hooks()
	if in == nil {
		return defaultHook
	}

	if in.ValidateArguments == nil {
		in.ValidateArguments = defaultHook.ValidateArguments
	}

	if in.MutatePath == nil {
		in.MutatePath = defaultHook.MutatePath
	}
	if in.MutateBody == nil {
		in.MutateBody = defaultHook.MutateBody
	}

	if in.BeforeHttpRequest == nil {
		in.BeforeHttpRequest = defaultHook.BeforeHttpRequest
	}

	if in.AfterHttpResponse == nil {
		in.AfterHttpResponse = defaultHook.AfterHttpResponse
	}

	return in
}

type AlatauCityBankPayIn_RegisterToken_Response struct {
	Request        *AlatauCityRegisterTokenRequest
	Response       *AlatauCityRegisterTokenResponse
	ResponseOutput File_inner_processing_rest_alatau_city_payin_proto_AlatauCityBankPayIn_RegisterToken
	Error          error
	Doc            *doc.Struct
}

func (s *restAlatauCityBankPayInServer) RegisterToken(
	ctx context.Context,
	request *AlatauCityRegisterTokenRequest,
) (
	response *AlatauCityBankPayIn_RegisterToken_Response,
	err error,
) {
	label := cntx.Begin(ctx, "AlatauCityBankPayInServer_RegisterToken")
	defer cntx.End(ctx, label)

	response = &AlatauCityBankPayIn_RegisterToken_Response{
		Request:        request,
		Response:       &AlatauCityRegisterTokenResponse{},
		ResponseOutput: File_inner_processing_rest_alatau_city_payin_proto_AlatauCityBankPayIn_RegisterToken_OutOfScope,
		Error:          nil,
		Doc: &doc.Struct{
			UUID:          uuid.New(),
			CreatedAt:     time.Now(),
			CreatedAtNano: time.Now().UnixNano(),
			FuncName:      "AlatauCityBankPayInServer_RegisterToken",
			Label:         label,
			Logs:          make(map[string]any),
			Arguments: map[string]any{
				"clear_path": "/ecom/api",
			},
			Request:         file_inner_processing_rest_alatau_city_payin_proto_message_AlatauCityRegisterTokenRequestToGo(label, request),
			RequestMethod:   "Post",
			RequestHeaders:  make(map[string][]string),
			ResponseHeaders: make(map[string][]string),
			HooksData:       make(map[string]any),
		},
	}
	defer func() { s.docFunc(ctx, response.Doc) }()

	defer func() {
		if err != nil {
			ctxzap.AddFields(ctx, zap.Any(label+"error", err))
			response.Error = err
			response.Doc.Error = err

			return
		}

		ctxzap.AddFields(
			ctx,
			file_inner_processing_rest_alatau_city_payin_proto_message_AlatauCityRegisterTokenResponseToZap(label+"response.Response", response.Response),
			zap.Any(label+"response.ResponseOutput", response.ResponseOutput),
		)

		response.Doc.Response = file_inner_processing_rest_alatau_city_payin_proto_message_AlatauCityRegisterTokenResponseToGo(label, response.Response)
		response.Doc.ResponseOutput = int32(response.ResponseOutput)
	}()

	ctxzap.AddFields(
		ctx,
		file_inner_processing_rest_alatau_city_payin_proto_message_AlatauCityRegisterTokenRequestToZap(label+"response.Request", response.Request),
		zap.Any(label+"clear_path", "/ecom/api"),
	)

	if err = s.hooksRegisterToken.ValidateArguments(
		ctx,
		request,
		response,
		response.Doc.Logs,
		response.Doc.HooksData,
	); err != nil {
		return
	}

	path := s.baseUrl + "/ecom/api"

	response.Doc.RequestURI = path

	if path, err = s.hooksRegisterToken.MutatePath(
		ctx,
		request,
		response,
		path,
		response.Doc.Logs,
		response.Doc.HooksData,
	); err != nil {
		return
	}

	response.Doc.MutatedRequestURI = path

	var contentType string

	var requestBody []byte
	if s.hooksRegisterToken.CustomMarshal != nil {
		if requestBody, contentType, err = s.hooksRegisterToken.CustomMarshal(
			ctx,
			request,
			response,
			path,
			response.Doc.Logs,
			response.Doc.HooksData,
		); err != nil {
			return
		}
	} else {
		requestForm := url.Values{}
		if request.GetBody().ORDER != nil {
			requestForm.Set("ORDER", request.GetBody().GetORDER())
		}

		if request.GetBody().MERCHANT != nil {
			requestForm.Set("MERCHANT", request.GetBody().GetMERCHANT())
		}

		if request.GetBody().TERMINAL != nil {
			requestForm.Set("TERMINAL", request.GetBody().GetTERMINAL())
		}

		if request.GetBody().CLIENT_ID != nil {
			requestForm.Set("CLIENT_ID", request.GetBody().GetCLIENT_ID())
		}

		if request.GetBody().CrdPan != nil {
			requestForm.Set("crd_pan", request.GetBody().GetCrdPan())
		}

		if request.GetBody().CrdExp != nil {
			requestForm.Set("crd_exp", request.GetBody().GetCrdExp())
		}

		if request.GetBody().CrdCvc != nil {
			requestForm.Set("crd_cvc", request.GetBody().GetCrdCvc())
		}

		if request.GetBody().NAME != nil {
			requestForm.Set("NAME", request.GetBody().GetNAME())
		}

		if request.GetBody().P_SIGN != nil {
			requestForm.Set("P_SIGN", request.GetBody().GetP_SIGN())
		}

		if request.GetBody().TOKEN_CMD != nil {
			requestForm.Set("TOKEN_CMD", request.GetBody().GetTOKEN_CMD())
		}

		contentType = "application/x-www-form-urlencoded"
		requestBody = []byte(requestForm.Encode())
	}

	if requestBody, err = s.hooksRegisterToken.MutateBody(
		ctx,
		request,
		response,
		requestBody,
		response.Doc.Logs,
		response.Doc.HooksData,
	); err != nil {
		return
	}

	response.Doc.RequestBody = requestBody

	requestReader := bytes.NewReader(requestBody)

	var httpRequest *http.Request
	if httpRequest, err = http.NewRequestWithContext(
		ctx,
		http.MethodPost,
		path,
		requestReader,
	); err != nil {
		return
	}

	httpRequest.Header.Set("Content-Type", contentType)

	if err = s.hooksRegisterToken.BeforeHttpRequest(
		ctx,
		request,
		response,
		httpRequest,
		response.Doc.Logs,
		response.Doc.HooksData,
	); err != nil {
		return
	}

	response.Doc.RequestHeaders = httpRequest.Header

	var httpResponse *http.Response
	if httpResponse, err = http.DefaultClient.Do(httpRequest); err != nil {
		response.Doc.ResponseError = err.Error()
		return
	}
	defer httpResponse.Body.Close()

	response.Doc.ResponseStatus = httpResponse.Status
	response.Doc.ResponseHeaders = httpResponse.Header

	var done bool
	if done, err = s.hooksRegisterToken.AfterHttpResponse(
		ctx,
		request,
		httpRequest,
		httpResponse,
		response,
		response.Doc.Logs,
		response.Doc.HooksData,
	); done || err != nil {
		return
	}

	var rspBody []byte
	if rspBody, err = io.ReadAll(httpResponse.Body); err != nil {
		return
	}

	response.Doc.ResponseBody = rspBody

	if s.hooksRegisterToken.CustomUnmarshal != nil {
		err = s.hooksRegisterToken.CustomUnmarshal(
			ctx,
			request,
			response,
			rspBody,
			response.Doc.Logs,
			response.Doc.HooksData,
		)
		return
	}

	if response.Response.TokenResponse, err = marshaller.XMLUnmarshalPtr[AlatauCityRegisterTokenResponse_RegisterTokenResponse](rspBody); err == nil {
		response.ResponseOutput = File_inner_processing_rest_alatau_city_payin_proto_AlatauCityBankPayIn_RegisterToken_TokenResponse
		return
	}
	return
}

type File_inner_processing_rest_alatau_city_payin_proto_AlatauCityBankPayIn_Charge int32

const (
	File_inner_processing_rest_alatau_city_payin_proto_AlatauCityBankPayIn_Charge_OutOfScope File_inner_processing_rest_alatau_city_payin_proto_AlatauCityBankPayIn_Charge = iota
	File_inner_processing_rest_alatau_city_payin_proto_AlatauCityBankPayIn_Charge_ChargeResponse
)

type File_inner_processing_rest_alatau_city_payin_proto_AlatauCityBankPayIn_Charge_Hooks struct {
	ValidateArguments func(
		ctx context.Context,
		request *AlatauCityChargeRequest,
		response *AlatauCityBankPayIn_Charge_Response,
		docData map[string]any,
		hooksData map[string]any,
	) error

	MutatePath func(
		ctx context.Context,
		request *AlatauCityChargeRequest,
		response *AlatauCityBankPayIn_Charge_Response,
		calculatedPath string,
		docData map[string]any,
		hooksData map[string]any,
	) (mutatePath string, err error)

	CustomMarshal func(
		ctx context.Context,
		request *AlatauCityChargeRequest,
		response *AlatauCityBankPayIn_Charge_Response,
		path string,
		docData map[string]any,
		hooksData map[string]any,
	) (body []byte, contentType string, err error)

	MutateBody func(
		ctx context.Context,
		request *AlatauCityChargeRequest,
		response *AlatauCityBankPayIn_Charge_Response,
		body []byte,
		docData map[string]any,
		hooksData map[string]any,
	) (mutatedBody []byte, err error)

	BeforeHttpRequest func(
		ctx context.Context,
		request *AlatauCityChargeRequest,
		response *AlatauCityBankPayIn_Charge_Response,
		httpRequest *http.Request,
		docData map[string]any,
		hooksData map[string]any,
	) error

	AfterHttpResponse func(
		ctx context.Context,
		request *AlatauCityChargeRequest,
		httpRequest *http.Request,
		httpResponse *http.Response,
		response *AlatauCityBankPayIn_Charge_Response,
		docData map[string]any,
		hooksData map[string]any,
	) (done bool, err error)

	CustomUnmarshal func(
		ctx context.Context,
		request *AlatauCityChargeRequest,
		response *AlatauCityBankPayIn_Charge_Response,
		rspBody []byte,
		docData map[string]any,
		hooksData map[string]any,
	) error
}

func NewFile_inner_processing_rest_alatau_city_payin_proto_AlatauCityBankPayIn_Charge_Hooks() *File_inner_processing_rest_alatau_city_payin_proto_AlatauCityBankPayIn_Charge_Hooks {
	return &File_inner_processing_rest_alatau_city_payin_proto_AlatauCityBankPayIn_Charge_Hooks{
		ValidateArguments: func(
			ctx context.Context,
			request *AlatauCityChargeRequest,
			response *AlatauCityBankPayIn_Charge_Response,
			docData map[string]any,
			hooksData map[string]any,
		) error {
			return nil
		},
		MutatePath: func(
			ctx context.Context,
			request *AlatauCityChargeRequest,
			response *AlatauCityBankPayIn_Charge_Response,
			calculatedPath string,
			docData map[string]any,
			hooksData map[string]any,
		) (mutatePath string, err error) {
			return calculatedPath, nil
		},
		MutateBody: func(
			ctx context.Context,
			request *AlatauCityChargeRequest,
			response *AlatauCityBankPayIn_Charge_Response,
			body []byte,
			docData map[string]any,
			hooksData map[string]any,
		) (mutatedBody []byte, err error) {
			return body, nil
		},
		BeforeHttpRequest: func(
			ctx context.Context,
			request *AlatauCityChargeRequest,
			response *AlatauCityBankPayIn_Charge_Response,
			httpRequest *http.Request,
			docData map[string]any,
			hooksData map[string]any,
		) error {
			return nil
		},
		AfterHttpResponse: func(
			ctx context.Context,
			request *AlatauCityChargeRequest,
			httpRequest *http.Request,
			httpResponse *http.Response,
			response *AlatauCityBankPayIn_Charge_Response,
			docData map[string]any,
			hooksData map[string]any,
		) (done bool, err error) {
			return false, nil
		},
	}
}

func PrepareFile_inner_processing_rest_alatau_city_payin_proto_AlatauCityBankPayIn_Charge_Hooks(
	in *File_inner_processing_rest_alatau_city_payin_proto_AlatauCityBankPayIn_Charge_Hooks,
) *File_inner_processing_rest_alatau_city_payin_proto_AlatauCityBankPayIn_Charge_Hooks {
	defaultHook := NewFile_inner_processing_rest_alatau_city_payin_proto_AlatauCityBankPayIn_Charge_Hooks()
	if in == nil {
		return defaultHook
	}

	if in.ValidateArguments == nil {
		in.ValidateArguments = defaultHook.ValidateArguments
	}

	if in.MutatePath == nil {
		in.MutatePath = defaultHook.MutatePath
	}
	if in.MutateBody == nil {
		in.MutateBody = defaultHook.MutateBody
	}

	if in.BeforeHttpRequest == nil {
		in.BeforeHttpRequest = defaultHook.BeforeHttpRequest
	}

	if in.AfterHttpResponse == nil {
		in.AfterHttpResponse = defaultHook.AfterHttpResponse
	}

	return in
}

type AlatauCityBankPayIn_Charge_Response struct {
	Request        *AlatauCityChargeRequest
	Response       *AlatauCityChargeResponse
	ResponseOutput File_inner_processing_rest_alatau_city_payin_proto_AlatauCityBankPayIn_Charge
	Error          error
	Doc            *doc.Struct
}

func (s *restAlatauCityBankPayInServer) Charge(
	ctx context.Context,
	request *AlatauCityChargeRequest,
) (
	response *AlatauCityBankPayIn_Charge_Response,
	err error,
) {
	label := cntx.Begin(ctx, "AlatauCityBankPayInServer_Charge")
	defer cntx.End(ctx, label)

	response = &AlatauCityBankPayIn_Charge_Response{
		Request:        request,
		Response:       &AlatauCityChargeResponse{},
		ResponseOutput: File_inner_processing_rest_alatau_city_payin_proto_AlatauCityBankPayIn_Charge_OutOfScope,
		Error:          nil,
		Doc: &doc.Struct{
			UUID:          uuid.New(),
			CreatedAt:     time.Now(),
			CreatedAtNano: time.Now().UnixNano(),
			FuncName:      "AlatauCityBankPayInServer_Charge",
			Label:         label,
			Logs:          make(map[string]any),
			Arguments: map[string]any{
				"clear_path": "/ecom/api",
			},
			Request:         file_inner_processing_rest_alatau_city_payin_proto_message_AlatauCityChargeRequestToGo(label, request),
			RequestMethod:   "Post",
			RequestHeaders:  make(map[string][]string),
			ResponseHeaders: make(map[string][]string),
			HooksData:       make(map[string]any),
		},
	}
	defer func() { s.docFunc(ctx, response.Doc) }()

	defer func() {
		if err != nil {
			ctxzap.AddFields(ctx, zap.Any(label+"error", err))
			response.Error = err
			response.Doc.Error = err

			return
		}

		ctxzap.AddFields(
			ctx,
			file_inner_processing_rest_alatau_city_payin_proto_message_AlatauCityChargeResponseToZap(label+"response.Response", response.Response),
			zap.Any(label+"response.ResponseOutput", response.ResponseOutput),
		)

		response.Doc.Response = file_inner_processing_rest_alatau_city_payin_proto_message_AlatauCityChargeResponseToGo(label, response.Response)
		response.Doc.ResponseOutput = int32(response.ResponseOutput)
	}()

	ctxzap.AddFields(
		ctx,
		file_inner_processing_rest_alatau_city_payin_proto_message_AlatauCityChargeRequestToZap(label+"response.Request", response.Request),
		zap.Any(label+"clear_path", "/ecom/api"),
	)

	if err = s.hooksCharge.ValidateArguments(
		ctx,
		request,
		response,
		response.Doc.Logs,
		response.Doc.HooksData,
	); err != nil {
		return
	}

	path := s.baseUrl + "/ecom/api"

	response.Doc.RequestURI = path

	if path, err = s.hooksCharge.MutatePath(
		ctx,
		request,
		response,
		path,
		response.Doc.Logs,
		response.Doc.HooksData,
	); err != nil {
		return
	}

	response.Doc.MutatedRequestURI = path

	var contentType string

	var requestBody []byte
	if s.hooksCharge.CustomMarshal != nil {
		if requestBody, contentType, err = s.hooksCharge.CustomMarshal(
			ctx,
			request,
			response,
			path,
			response.Doc.Logs,
			response.Doc.HooksData,
		); err != nil {
			return
		}
	} else {
		requestForm := url.Values{}
		if request.GetBody().APPROVE != nil {
			requestForm.Set("APPROVE", request.GetBody().GetAPPROVE())
		}

		if request.GetBody().ORDER != nil {
			requestForm.Set("ORDER", request.GetBody().GetORDER())
		}

		if request.GetBody().AMOUNT != nil {
			requestForm.Set("AMOUNT", request.GetBody().GetAMOUNT())
		}

		if request.GetBody().MERCHANT != nil {
			requestForm.Set("MERCHANT", request.GetBody().GetMERCHANT())
		}

		if request.GetBody().P_SIGN != nil {
			requestForm.Set("P_SIGN", request.GetBody().GetP_SIGN())
		}

		contentType = "application/x-www-form-urlencoded"
		requestBody = []byte(requestForm.Encode())
	}

	if requestBody, err = s.hooksCharge.MutateBody(
		ctx,
		request,
		response,
		requestBody,
		response.Doc.Logs,
		response.Doc.HooksData,
	); err != nil {
		return
	}

	response.Doc.RequestBody = requestBody

	requestReader := bytes.NewReader(requestBody)

	var httpRequest *http.Request
	if httpRequest, err = http.NewRequestWithContext(
		ctx,
		http.MethodPost,
		path,
		requestReader,
	); err != nil {
		return
	}

	httpRequest.Header.Set("Content-Type", contentType)

	if err = s.hooksCharge.BeforeHttpRequest(
		ctx,
		request,
		response,
		httpRequest,
		response.Doc.Logs,
		response.Doc.HooksData,
	); err != nil {
		return
	}

	response.Doc.RequestHeaders = httpRequest.Header

	var httpResponse *http.Response
	if httpResponse, err = http.DefaultClient.Do(httpRequest); err != nil {
		response.Doc.ResponseError = err.Error()
		return
	}
	defer httpResponse.Body.Close()

	response.Doc.ResponseStatus = httpResponse.Status
	response.Doc.ResponseHeaders = httpResponse.Header

	var done bool
	if done, err = s.hooksCharge.AfterHttpResponse(
		ctx,
		request,
		httpRequest,
		httpResponse,
		response,
		response.Doc.Logs,
		response.Doc.HooksData,
	); done || err != nil {
		return
	}

	var rspBody []byte
	if rspBody, err = io.ReadAll(httpResponse.Body); err != nil {
		return
	}

	response.Doc.ResponseBody = rspBody

	if s.hooksCharge.CustomUnmarshal != nil {
		err = s.hooksCharge.CustomUnmarshal(
			ctx,
			request,
			response,
			rspBody,
			response.Doc.Logs,
			response.Doc.HooksData,
		)
		return
	}

	if response.Response.ChargeResponse, err = marshaller.XMLUnmarshalPtr[AlatauCityChargeResponse_ChargeResponse](rspBody); err == nil {
		response.ResponseOutput = File_inner_processing_rest_alatau_city_payin_proto_AlatauCityBankPayIn_Charge_ChargeResponse
		return
	}
	return
}
