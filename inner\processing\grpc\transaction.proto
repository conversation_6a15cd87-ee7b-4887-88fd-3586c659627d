edition = "2023";

package processing.transaction.transaction;

option go_package = "git.local/sensitive/innerpb/processing/grpc";

import "google/protobuf/timestamp.proto";
import "google/protobuf/struct.proto";
import "google/protobuf/empty.proto";

service Transaction {
  rpc UpdateTransactionStatus(UpdateTransactionStatusRequestV1) returns (google.protobuf.Empty) {}
  rpc UpdateCallbackStatus(UpdateCallbackStatusRequestV1) returns (google.protobuf.Empty) {}
  rpc GetTransactionsByPeriodAndStatus(GetTransactionsByStatusReqV1) returns (GetTransactionsResponse) {}
  rpc GetTransactionsByPeriodAndCallbackStatus(GetTransactionsByCallbackStatusReqV1) returns (GetTransactionsResponse) {}
  rpc GetTransactionsByFinalStatusAndPeriodWithLimit(GetTransactionsByFinalStatusAndPeriodWithLimitReqV1) returns (GetTransactionsResponse) {}
  rpc GetPayInTransactionsByPeriodAndAcquirer(GetPayInTransactionsByPeriodAndAcquirerReqV1) returns (GetTransactionsResponse) {}
  rpc GetTransactionTypeByID(GetTransactionTypeByIDRequestV1) returns (GetTransactionTypeByIDResponseV1) {}
  rpc GetTransactionByID(GetTransactionByIDRequestV1) returns (TransactionDataV1) {}
  rpc MakeAutoCharge(MakeAutoChargeRequestV1) returns (google.protobuf.Empty) {}
  rpc SetRefundWaitingStatus(SetRefundWaitingStatusRequestV1) returns (SetRefundWaitingStatusResponseV1) {}
  rpc GetTransactionsByProjectInfo(GetTransactionsByProjectInfoRequestV1) returns (GetTransactionsResponse) {}
  rpc CheckTransactionHash(CheckTransactionHashRequestV1) returns (google.protobuf.Empty) {}
  rpc CreateTransactionByPhone(CreateTransactionByPhoneRequest) returns (CreateTransactionByPhoneResponse) {}
  rpc GetByIDWithType(GetByIDWithTypeRequest) returns (GetByIDWithTypeResponse) {}
  rpc UpdateStatus(UpdateStatusRequest) returns (google.protobuf.Empty) {}
  rpc IncreaseTryCount(IncreaseTryCountRequest) returns (google.protobuf.Empty) {}
  rpc SaveAcquirerResponse(SaveAcquirerResponseRequest) returns (google.protobuf.Empty) {}
  rpc SendReceipt(SendRequest) returns (google.protobuf.Empty) {}
  rpc SetAdditionalData(SetAdditionalDataRequest) returns (google.protobuf.Empty) {}
  rpc CalculateAndUpdateTransactionAmount(CalculateAndUpdateTransactionAmountRequest) returns (google.protobuf.Empty) {}
  rpc BillPayOut(BillPayoutRequest) returns (google.protobuf.Empty) {}
}

message SetRefundWaitingStatusRequestV1 {
  uint64 transaction_id = 1;
}

message SetRefundWaitingStatusResponseV1 {
  string status_code = 1;
}

message GetPayInTransactionsByPeriodAndAcquirerReqV1 {
  uint64 acquirer_id = 1;
  google.protobuf.Timestamp start_period = 2;
  google.protobuf.Timestamp end_period = 3;
}

message GetTransactionsByStatusReqV1 {
  string status = 1;
  google.protobuf.Timestamp start_period = 2;
  google.protobuf.Timestamp end_period = 3;
}

message GetTransactionsByCallbackStatusReqV1 {
  uint32 callback_status = 1;
  google.protobuf.Timestamp start_period = 2;
  google.protobuf.Timestamp end_period = 3;
}

message GetTransactionsByFinalStatusAndPeriodWithLimitReqV1 {
  bool is_final = 1;
  uint32 transactionLimit = 2;
  google.protobuf.Timestamp start_period = 3;
  google.protobuf.Timestamp end_period = 4;
}

message GetTransactionByIDRequestV1 {
  uint64 id = 1;
}

message TransactionDataV1 {
  google.protobuf.Timestamp created_at = 1;
  google.protobuf.Timestamp updated_at = 2;
  google.protobuf.Timestamp finished_at = 3;
  uint64 id = 4;
  uint64 acquirer_id = 5;
  uint64 terminal_id = 6;
  string bank_reference_id = 7;
  uint64 card_id = 8;
  uint64 project_id = 9;
  uint64 merchant_id = 10;
  string project_reference_id = 11;
  string project_client_id = 12;
  uint64 status_id = 13;
  uint64 type_id = 14;
  double amount = 15;
  uint64 try = 16;
  string description = 17;
  string type_code = 18;
  string status_code = 19;
  bool is_final_status = 20;
  TransactionInfo transaction_info = 21;
  uint64 aggregated_type_id = 22;
}

message TransactionInfo {
  google.protobuf.Timestamp created_at = 1;
  google.protobuf.Timestamp updated_at = 2;
  uint64 transaction_id = 3;
  google.protobuf.Timestamp transaction_created_at = 4;
  string callback_url = 5;
  string confirm_url = 6;
  string success_redirect_url = 7;
  string failure_redirect_url = 8;
  uint32 callback_status = 9;
  string user_phone = 10;
  string user_email = 11;
  string shipment = 12;
  string bank_order_id = 13;
  google.protobuf.Struct jobs_message = 14;
}

message GetTransactionsResponse {
  repeated TransactionDataV1 transactions = 1;
}

message UpdateTransactionStatusRequestV1{
  uint64 transaction_id = 1;
  uint64 transaction_status_id = 2;
}

message UpdateCallbackStatusRequestV1{
  uint64 transaction_id = 1;
  uint32 callback_status = 2;
}

message GetTransactionTypeByIDRequestV1 {
  uint64 type_id = 1;
}

message GetTransactionTypeByIDResponseV1 {
  uint64 id = 1;
  string code = 2;
  string name = 3;
}

message MakeAutoChargeRequestV1 {
  uint64 transaction_id = 1;
}

message GetTransactionsByProjectInfoRequestV1 {
  uint64 project_id = 1;
  uint64 merchant_id = 2;
  google.protobuf.Timestamp start_period = 3;
  google.protobuf.Timestamp end_period = 4;
}

message CheckTransactionHashRequestV1 {
  uint64 transaction_id = 1;
  string hash = 2;
}

message CreateTransactionByPhoneRequest {
  double amount = 1;
  string callback_url = 2;
  string description = 3;
  string project_order_id = 4;
  uint64 merchant_id = 5;
  string project_client_id = 6;
  uint64 project_id = 7;
  string project_reference_id = 8;
  string user_phone = 9;
  string email = 10;
  map<string, string> additional_data = 11;
  string visa_alias_token = 12;
  uint64 terminal_id = 13;
  uint64 acquirer_id = 14;
}

message CreateTransactionByPhoneResponse {
  uint64 transaction_id = 1;
  uint64 project_id = 2;
  uint64 merchant_id = 3;
  double amount = 4;
  google.protobuf.Timestamp created_at = 5;
  uint64 transaction_type_id = 6;
}

message GetByIDWithTypeRequest {
  uint64 transaction_id = 1;
  string code = 2;
}

message GetByIDWithTypeResponse {
  google.protobuf.Timestamp created_at = 1;
  google.protobuf.Timestamp updated_at = 2;
  google.protobuf.Timestamp finished_at = 3;
  uint64 id = 4;
  uint64 acquirer_id = 5;
  uint64 terminal_id = 6;
  string bank_reference_id = 7;
  uint64 card_id = 8;
  uint64 project_id = 9;
  uint64 merchant_id = 10;
  string project_reference_id = 11;
  string project_client_id = 12;
  uint64 status_id = 13;
  uint64 type_id = 14;
  double amount = 15;
  uint64 try = 16;
  string description = 17;
  string type_code = 18;
  string status_code = 19;
  bool is_final_status = 20;
  TransactionInfo transaction_info = 21;
  TransactionType transaction_type = 22;
  TransactionStatus transaction_status = 23;
  uint64 aggregated_type_id = 24;
  string visa_alias_token = 25;
  string c_info = 26;
}

message TransactionStatus {
  google.protobuf.Timestamp created_at = 1;
  google.protobuf.Timestamp updated_at = 2;
  uint64 id = 3;
  string code = 4;
  string name = 5;
  bool is_final = 6;
}

message TransactionType {
  google.protobuf.Timestamp created_at = 1;
  google.protobuf.Timestamp updated_at = 2;
  uint64 id = 3;
  string code = 4;
  string name = 5;
}

message UpdateStatusRequest {
  uint64 transaction_id = 1;
  uint64 transaction_status = 2;
  google.protobuf.Timestamp finished_at = 3;
}

message IncreaseTryCountRequest {
  uint64 transaction_id = 1;
  uint64 try = 2;
}

message SaveAcquirerResponseRequest {
  uint64 transaction_id = 1;
  TransactionBankResponse bank_response = 2;
  string bank_reference_id = 3;
  string bank_order_id = 4;
}

message TransactionBankResponse {
  string code = 1;
  string message = 2;
  string integrationErrorCode = 3;
  string integrationErrorMessage = 4;
}

message SendRequest {
  uint64 transaction_id = 1;
}

message SetAdditionalDataRequest {
  map<string, string> additional_data = 1;
  uint64 transaction_id = 2;
  google.protobuf.Timestamp created_at = 3;
  uint64 project_id = 4;
  uint64 merchant_id = 5;
}

message CalculateAndUpdateTransactionAmountRequest {
  uint64 transaction_id = 1;
}

message BillPayoutRequest {
  uint64 transaction_id = 1;
}

