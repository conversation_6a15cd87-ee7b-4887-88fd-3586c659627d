// Code generated by protoc-gen-go-mvp. DO NOT EDIT.
// versions:
// - protoc-gen-go-mvp v2.0.1
// created by mvp authors

package grpc

import (
	context "context"
	fmt "fmt"
	cntx "git.local/sensitive/mvp/pkg/cntx"
	ctxzap "github.com/grpc-ecosystem/go-grpc-middleware/logging/zap/ctxzap"
	zap "go.uber.org/zap"
	grpc "google.golang.org/grpc"
)

func file_inner_processing_grpc_transaction_status_proto_message_BatchTransactionRequestV1ToZap(
	label string,
	in *BatchTransactionRequestV1,
) zap.Field {
	if in == nil {
		return zap.Reflect(label, in)
	}

	return zap.Dict(
		label,
		zap.Any("TransactionIds", in.GetTransactionIds()),
	)
}

func file_inner_processing_grpc_transaction_status_proto_message_BatchTransactionResponseV1ToZap(
	label string,
	in *BatchTransactionResponseV1,
) zap.Field {
	if in == nil {
		return zap.Reflect(label, in)
	}

	return zap.Dict(
		label,
		file_inner_processing_grpc_transaction_status_proto_message_TransactionWithStatusResponseV1SliceToZap("BatchResponse", in.GetBatchResponse()),
	)
}

func file_inner_processing_grpc_transaction_status_proto_enum_EnumTransactionStatusToZap(
	label string,
	in EnumTransactionStatus,
) zap.Field {
	str, ok := EnumTransactionStatus_name[int32(in)]
	if !ok {
		str = fmt.Sprintf("unknown EnumTransactionStatus value %d", in)
	}

	return zap.Dict(
		label,
		zap.Any("value", EnumTransactionStatus(in)),
		zap.Any("name", str),
	)
}

func file_inner_processing_grpc_transaction_status_proto_message_TransactionStatusRefToZap(
	label string,
	in *TransactionStatusRef,
) zap.Field {
	if in == nil {
		return zap.Reflect(label, in)
	}

	return zap.Dict(
		label,
		zap.Any("Name", in.GetName()),
		zap.Any("Code", in.GetCode()),
		zap.Any("IsFinal", in.GetIsFinal()),
	)
}

func file_inner_processing_grpc_transaction_status_proto_message_TransactionWithStatusResponseV1ToZap(
	label string,
	in *TransactionWithStatusResponseV1,
) zap.Field {
	if in == nil {
		return zap.Reflect(label, in)
	}

	return zap.Dict(
		label,
		zap.Any("TransactionId", in.GetTransactionId()),
		zap.Any("StatusCode", in.GetStatusCode()),
		zap.Any("IsFinalStatus", in.GetIsFinalStatus()),
	)
}

func file_inner_processing_grpc_transaction_status_proto_message_TransactionWithStatusResponseV1SliceToZap(
	label string,
	in []*TransactionWithStatusResponseV1,
) zap.Field {
	var res []zap.Field
	for index, val := range in {
		res = append(res, file_inner_processing_grpc_transaction_status_proto_message_TransactionWithStatusResponseV1ToZap(fmt.Sprint(index), val))
	}

	return zap.Dict(label, res...)
}

var _ TransactionStatusServer = (*loggedTransactionStatusServer)(nil)

func NewLoggedTransactionStatusServer(srv TransactionStatusServer) TransactionStatusServer {
	return &loggedTransactionStatusServer{srv: srv}
}

type loggedTransactionStatusServer struct {
	UnimplementedTransactionStatusServer

	srv TransactionStatusServer
}

func (s *loggedTransactionStatusServer) GetBatchTransactionWithStatuses(
	ctx context.Context,
	request *BatchTransactionRequestV1,
) (
	response *BatchTransactionResponseV1,
	err error,
) {
	label := cntx.Begin(ctx, "TransactionStatusServer_GetBatchTransactionWithStatuses")
	defer cntx.End(ctx, label)

	defer func() {
		if err != nil {
			ctxzap.AddFields(ctx, zap.Any(label+"error", err))
			return
		}

		ctxzap.AddFields(
			ctx,
			file_inner_processing_grpc_transaction_status_proto_message_BatchTransactionResponseV1ToZap(label+"response", response),
		)
	}()

	ctxzap.AddFields(
		ctx,
		file_inner_processing_grpc_transaction_status_proto_message_BatchTransactionRequestV1ToZap(label+"request", request),
	)

	response, err = s.srv.GetBatchTransactionWithStatuses(ctx, request)

	return
}

var _ TransactionStatusClient = (*loggedTransactionStatusClient)(nil)

func NewLoggedTransactionStatusClient(client TransactionStatusClient) TransactionStatusClient {
	return &loggedTransactionStatusClient{client: client}
}

type loggedTransactionStatusClient struct {
	client TransactionStatusClient
}

func (s *loggedTransactionStatusClient) GetBatchTransactionWithStatuses(
	ctx context.Context,
	request *BatchTransactionRequestV1,
	opts ...grpc.CallOption,
) (
	response *BatchTransactionResponseV1,
	err error,
) {
	label := cntx.Begin(ctx, "TransactionStatusClient_GetBatchTransactionWithStatuses")
	defer cntx.End(ctx, label)

	defer func() {
		if err != nil {
			ctxzap.AddFields(ctx, zap.Any(label+"error", err))
			return
		}

		ctxzap.AddFields(
			ctx,
			file_inner_processing_grpc_transaction_status_proto_message_BatchTransactionResponseV1ToZap(label+"response", response),
		)
	}()

	ctxzap.AddFields(
		ctx,
		file_inner_processing_grpc_transaction_status_proto_message_BatchTransactionRequestV1ToZap(label+"request", request),
	)

	response, err = s.client.GetBatchTransactionWithStatuses(ctx, request, opts...)

	return
}
