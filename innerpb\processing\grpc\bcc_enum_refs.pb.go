// Code generated by protoc-gen-go-mvp. DO NOT EDIT.
// versions:
// - protoc-gen-go-mvp v2.0.1
// created by mvp authors

package grpc

func (x ResponseCodePayIn) Description() string {
	switch x {
	case ResponseCodePayIn_ResponseCodePayInSuccess:
		return "Транзакция успешно завершена."
	case ResponseCodePayIn_ResponseCodePayInErrorProcessing:
		return "Ошибка обработки транзакции."
	case ResponseCodePayIn_ResponseCodePayIn3DSWaiting:
		return "Проверка держателя карты по OTP-коду."
	case ResponseCodePayIn_ResponseCodePayInRequestCardForm:
		return "Запрос формы ввода данных карты пользователя."
	case ResponseCodePayIn_ResponseCodePayInVeResU:
		return "Сообщение VeRes или PaRes со статусом 'U'."
	case ResponseCodePayIn_ResponseCodePayInVeResA:
		return "Сообщение VeRes или PaRes со статусом 'A'."
	case ResponseCodePayIn_ResponseCodePayInRequestFingerprint:
		return "Запрос параметров fingerprint в рамках 3D-Secure."
	case ResponseCodePayIn_ResponseCodePayIn3DSResponse:
		return "Ответ 3D-Secure со стороны эмитента."
	case ResponseCodePayIn_ResponseCodePayInMastercardInstallment:
		return "Автоматическая полная оплата в рамках Mastercard installment."
	case ResponseCodePayIn_ResponseCodePayInSCA:
		return "Требования Strong Customer Authentication."
	case ResponseCodePayIn_ResponseCodePayInRepeatAuthError:
		return "Повтор транзакции с ошибкой аутентификации."
	case ResponseCodePayIn_ResponseCodePayInRepeatDeclined:
		return "Повтор транзакции, которая была отклонена."
	case ResponseCodePayIn_ResponseCodePayInDeclined:
		return "Транзакция отклонена."
	case ResponseCodePayIn_ResponseCodePayInInfoMessage:
		return "Информационное сообщение."
	case ResponseCodePayIn_ResponseCodePayInCheckCardBySum:
		return "Проверка держателя карты путем генерации случайной суммы."
	case ResponseCodePayIn_ResponseCodePayInUPIRequest:
		return "Запрос UPI."
	case ResponseCodePayIn_ResponseCodePayInRepeatTransaction:
		return "Обнаружена повторная транзакция."
	case ResponseCodePayIn_ResponseCodePayInNoResponse:
		return "Повтор транзакции, завершившейся без ответа."
	case ResponseCodePayIn_ResponseCodePayInInstallmentAutoCancel:
		return "Автоматическая отмена транзакции Mastercard installment."
	case ResponseCodePayIn_ResponseCodePayInMerchantSumCheck:
		return "Проверка держателя карты по запросу торговцем генерации случайной суммы."
	case ResponseCodePayIn_ResponseCodePayInInstallment:
		return "Транзакция Mastercard installment."
	case ResponseCodePayIn_ResponseCodePayInInstallmentCancel:
		return "Отмена пользователем транзакции Mastercard installment."
	case ResponseCodePayIn_ResponseCodePayInUserConfirmation:
		return "Запрос подтверждения пользователя."
	default:
		return "default"
	}
}

func (x ResponseCodePayIn) TransactionStatus() EnumTransactionStatus {
	switch x {
	case ResponseCodePayIn_ResponseCodePayInSuccess:
		return EnumTransactionStatus_TransactionStatusSuccess
	case ResponseCodePayIn_ResponseCodePayInErrorProcessing:
		return EnumTransactionStatus_TransactionStatusError
	case ResponseCodePayIn_ResponseCodePayIn3DSWaiting:
		return EnumTransactionStatus_TransactionStatusFingerPrint
	case ResponseCodePayIn_ResponseCodePayInRequestCardForm:
		return EnumTransactionStatus_TransactionStatusThreeDSWaiting
	case ResponseCodePayIn_ResponseCodePayInVeResU:
		return EnumTransactionStatus_TransactionStatusFailed
	case ResponseCodePayIn_ResponseCodePayInVeResA:
		return EnumTransactionStatus_TransactionStatusThreeDSReceived
	case ResponseCodePayIn_ResponseCodePayInRequestFingerprint:
		return EnumTransactionStatus_TransactionStatusFingerPrint
	case ResponseCodePayIn_ResponseCodePayIn3DSResponse:
		return EnumTransactionStatus_TransactionStatusThreeDSReceived
	case ResponseCodePayIn_ResponseCodePayInMastercardInstallment:
		return EnumTransactionStatus_TransactionStatusRefundWaiting
	case ResponseCodePayIn_ResponseCodePayInSCA:
		return EnumTransactionStatus_TransactionStatusThreeDSReceived
	case ResponseCodePayIn_ResponseCodePayInRepeatAuthError:
		return EnumTransactionStatus_TransactionStatusCanceled
	case ResponseCodePayIn_ResponseCodePayInRepeatDeclined:
		return EnumTransactionStatus_TransactionStatusRefund
	case ResponseCodePayIn_ResponseCodePayInDeclined:
		return EnumTransactionStatus_TransactionStatusFailed
	case ResponseCodePayIn_ResponseCodePayInInfoMessage:
		return EnumTransactionStatus_TransactionStatusProcessed
	case ResponseCodePayIn_ResponseCodePayInCheckCardBySum:
		return EnumTransactionStatus_TransactionStatusFingerPrint
	case ResponseCodePayIn_ResponseCodePayInUPIRequest:
		return EnumTransactionStatus_TransactionStatusFailed
	case ResponseCodePayIn_ResponseCodePayInRepeatTransaction:
		return EnumTransactionStatus_TransactionStatusNew
	case ResponseCodePayIn_ResponseCodePayInNoResponse:
		return EnumTransactionStatus_TransactionStatusHolded
	case ResponseCodePayIn_ResponseCodePayInInstallmentAutoCancel:
		return EnumTransactionStatus_TransactionStatusCanceled
	case ResponseCodePayIn_ResponseCodePayInMerchantSumCheck:
		return EnumTransactionStatus_TransactionStatusFingerPrint
	case ResponseCodePayIn_ResponseCodePayInInstallment:
		return EnumTransactionStatus_TransactionStatusAuthorized
	case ResponseCodePayIn_ResponseCodePayInInstallmentCancel:
		return EnumTransactionStatus_TransactionStatusCanceled
	case ResponseCodePayIn_ResponseCodePayInUserConfirmation:
		return EnumTransactionStatus_TransactionStatusFingerPrint
	default:
		return EnumTransactionStatus_TransactionStatusHolded
	}
}

// Created reference to ResponseCodePayIn

//	|	ResponseCodePayIn                                       	|	Description                                                                                                                              	|	TransactionStatus                                     	|
//	|	ResponseCodePayIn_ResponseCodePayInSuccess              	|	"Транзакция успешно завершена."                                                                                                          	|	EnumTransactionStatus_TransactionStatusSuccess        	|
//	|	ResponseCodePayIn_ResponseCodePayInErrorProcessing      	|	"Ошибка обработки транзакции."                                                                                                           	|	EnumTransactionStatus_TransactionStatusError          	|
//	|	ResponseCodePayIn_ResponseCodePayIn3DSWaiting           	|	"Проверка держателя карты по OTP-коду."                                                                                                  	|	EnumTransactionStatus_TransactionStatusFingerPrint    	|
//	|	ResponseCodePayIn_ResponseCodePayInRequestCardForm      	|	"Запрос формы ввода данных карты пользователя."                                                                                          	|	EnumTransactionStatus_TransactionStatusThreeDSWaiting 	|
//	|	ResponseCodePayIn_ResponseCodePayInVeResU               	|	"Сообщение VeRes или PaRes со статусом 'U'."                                                                                             	|	EnumTransactionStatus_TransactionStatusFailed         	|
//	|	ResponseCodePayIn_ResponseCodePayInVeResA               	|	"Сообщение VeRes или PaRes со статусом 'A'."                                                                                             	|	EnumTransactionStatus_TransactionStatusThreeDSReceived	|
//	|	ResponseCodePayIn_ResponseCodePayInRequestFingerprint   	|	"Запрос параметров fingerprint в рамках 3D-Secure."                                                                                      	|	EnumTransactionStatus_TransactionStatusFingerPrint    	|
//	|	ResponseCodePayIn_ResponseCodePayIn3DSResponse          	|	"Ответ 3D-Secure со стороны эмитента."                                                                                                   	|	EnumTransactionStatus_TransactionStatusThreeDSReceived	|
//	|	ResponseCodePayIn_ResponseCodePayInMastercardInstallment	|	"Автоматическая полная оплата в рамках Mastercard installment."                                                                          	|	EnumTransactionStatus_TransactionStatusRefundWaiting  	|
//	|	ResponseCodePayIn_ResponseCodePayInSCA                  	|	"Требования Strong Customer Authentication."                                                                                             	|	EnumTransactionStatus_TransactionStatusThreeDSReceived	|
//	|	ResponseCodePayIn_ResponseCodePayInRepeatAuthError      	|	"Повтор транзакции с ошибкой аутентификации."                                                                                            	|	EnumTransactionStatus_TransactionStatusCanceled       	|
//	|	ResponseCodePayIn_ResponseCodePayInRepeatDeclined       	|	"Повтор транзакции, которая была отклонена."                                                                                             	|	EnumTransactionStatus_TransactionStatusRefund         	|
//	|	ResponseCodePayIn_ResponseCodePayInDeclined             	|	"Транзакция отклонена."                                                                                                                  	|	EnumTransactionStatus_TransactionStatusFailed         	|
//	|	ResponseCodePayIn_ResponseCodePayInInfoMessage          	|	"Информационное сообщение."                                                                                                              	|	EnumTransactionStatus_TransactionStatusProcessed      	|
//	|	ResponseCodePayIn_ResponseCodePayInCheckCardBySum       	|	"Проверка держателя карты путем генерации случайной суммы."                                                                              	|	EnumTransactionStatus_TransactionStatusFingerPrint    	|
//	|	ResponseCodePayIn_ResponseCodePayInUPIRequest           	|	"Запрос UPI."                                                                                                                            	|	EnumTransactionStatus_TransactionStatusFailed         	|
//	|	ResponseCodePayIn_ResponseCodePayInRepeatTransaction    	|	"Обнаружена повторная транзакция."                                                                                                       	|	EnumTransactionStatus_TransactionStatusNew            	|
//	|	ResponseCodePayIn_ResponseCodePayInNoResponse           	|	"Повтор транзакции, завершившейся без ответа."                                                                                           	|	EnumTransactionStatus_TransactionStatusHolded         	|
//	|	ResponseCodePayIn_ResponseCodePayInInstallmentAutoCancel	|	"Автоматическая отмена транзакции Mastercard installment."                                                                               	|	EnumTransactionStatus_TransactionStatusCanceled       	|
//	|	ResponseCodePayIn_ResponseCodePayInMerchantSumCheck     	|	"Проверка держателя карты по запросу торговцем генерации случайной суммы."                                                               	|	EnumTransactionStatus_TransactionStatusFingerPrint    	|
//	|	ResponseCodePayIn_ResponseCodePayInInstallment          	|	"Транзакция Mastercard installment."                                                                                                     	|	EnumTransactionStatus_TransactionStatusAuthorized     	|
//	|	ResponseCodePayIn_ResponseCodePayInInstallmentCancel    	|	"Отмена пользователем транзакции Mastercard installment."                                                                                	|	EnumTransactionStatus_TransactionStatusCanceled       	|
//	|	ResponseCodePayIn_ResponseCodePayInUserConfirmation     	|	"Запрос подтверждения пользователя."                                                                                                     	|	EnumTransactionStatus_TransactionStatusFingerPrint    	|

var SliceResponseCodePayInRefs *sliceResponseCodePayInRefs

type sliceResponseCodePayInRefs struct{}

func (*sliceResponseCodePayInRefs) Description(slice ...ResponseCodePayIn) []string {
	var result []string
	for _, val := range slice {
		result = append(result, val.Description())
	}

	return result
}

func (*sliceResponseCodePayInRefs) TransactionStatus(slice ...ResponseCodePayIn) []EnumTransactionStatus {
	var result []EnumTransactionStatus
	for _, val := range slice {
		result = append(result, val.TransactionStatus())
	}

	return result
}

func (x ResponseCodePayOut) TransactionStatus() EnumTransactionStatus {
	switch x {
	case ResponseCodePayOut_ResponseCodePayOutBadRequest:
		return EnumTransactionStatus_TransactionStatusFailed
	case ResponseCodePayOut_ResponseCodePayOutUnauthorized:
		return EnumTransactionStatus_TransactionStatusFailed
	case ResponseCodePayOut_ResponseCodePayOutTooManyRequests:
		return EnumTransactionStatus_TransactionStatusFailed
	case ResponseCodePayOut_ResponseCodePayOutInternalServerError:
		return EnumTransactionStatus_TransactionStatusFailed
	case ResponseCodePayOut_ResponseCodePayOutNotImplemented:
		return EnumTransactionStatus_TransactionStatusFailed
	case ResponseCodePayOut_ResponseCodePayOutServiceUnavailable:
		return EnumTransactionStatus_TransactionStatusFailed
	case ResponseCodePayOut_ResponseCodePayOutDeclined:
		return EnumTransactionStatus_TransactionStatusFailed
	case ResponseCodePayOut_ResponseCodePayOutRejected:
		return EnumTransactionStatus_TransactionStatusFailed
	case ResponseCodePayOut_ResponseCodePayOutTeapot:
		return EnumTransactionStatus_TransactionStatusFailed
	case ResponseCodePayOut_ResponseCodePayOutActive:
		return EnumTransactionStatus_TransactionStatusHolded
	case ResponseCodePayOut_ResponseCodePayOutWaiting:
		return EnumTransactionStatus_TransactionStatusSuccess
	case ResponseCodePayOut_ResponseCodePayOutLoaded:
		return EnumTransactionStatus_TransactionStatusSuccess
	case ResponseCodePayOut_ResponseCodePayOutSuspended:
		return EnumTransactionStatus_TransactionStatusHolded
	case ResponseCodePayOut_ResponseCodePayOutRetry:
		return EnumTransactionStatus_TransactionStatusHolded
	case ResponseCodePayOut_ResponseCodePayOutRejectOut:
		return EnumTransactionStatus_TransactionStatusFailed
	case ResponseCodePayOut_ResponseCodePayOutRejectSent:
		return EnumTransactionStatus_TransactionStatusFailed
	case ResponseCodePayOut_ResponseCodePayOutCompleted:
		return EnumTransactionStatus_TransactionStatusSuccess
	default:
		return EnumTransactionStatus_TransactionStatusHolded
	}
}

// Created reference to ResponseCodePayOut

//	|	ResponseCodePayOut                                      	|	TransactionStatus                             	|
//	|	ResponseCodePayOut_ResponseCodePayOutBadRequest         	|	EnumTransactionStatus_TransactionStatusFailed 	|
//	|	ResponseCodePayOut_ResponseCodePayOutUnauthorized       	|	EnumTransactionStatus_TransactionStatusFailed 	|
//	|	ResponseCodePayOut_ResponseCodePayOutTooManyRequests    	|	EnumTransactionStatus_TransactionStatusFailed 	|
//	|	ResponseCodePayOut_ResponseCodePayOutInternalServerError	|	EnumTransactionStatus_TransactionStatusFailed 	|
//	|	ResponseCodePayOut_ResponseCodePayOutNotImplemented     	|	EnumTransactionStatus_TransactionStatusFailed 	|
//	|	ResponseCodePayOut_ResponseCodePayOutServiceUnavailable 	|	EnumTransactionStatus_TransactionStatusFailed 	|
//	|	ResponseCodePayOut_ResponseCodePayOutDeclined           	|	EnumTransactionStatus_TransactionStatusFailed 	|
//	|	ResponseCodePayOut_ResponseCodePayOutRejected           	|	EnumTransactionStatus_TransactionStatusFailed 	|
//	|	ResponseCodePayOut_ResponseCodePayOutTeapot             	|	EnumTransactionStatus_TransactionStatusFailed 	|
//	|	ResponseCodePayOut_ResponseCodePayOutActive             	|	EnumTransactionStatus_TransactionStatusHolded 	|
//	|	ResponseCodePayOut_ResponseCodePayOutWaiting            	|	EnumTransactionStatus_TransactionStatusSuccess	|
//	|	ResponseCodePayOut_ResponseCodePayOutLoaded             	|	EnumTransactionStatus_TransactionStatusSuccess	|
//	|	ResponseCodePayOut_ResponseCodePayOutSuspended          	|	EnumTransactionStatus_TransactionStatusHolded 	|
//	|	ResponseCodePayOut_ResponseCodePayOutRetry              	|	EnumTransactionStatus_TransactionStatusHolded 	|
//	|	ResponseCodePayOut_ResponseCodePayOutRejectOut          	|	EnumTransactionStatus_TransactionStatusFailed 	|
//	|	ResponseCodePayOut_ResponseCodePayOutRejectSent         	|	EnumTransactionStatus_TransactionStatusFailed 	|
//	|	ResponseCodePayOut_ResponseCodePayOutCompleted          	|	EnumTransactionStatus_TransactionStatusSuccess	|

var SliceResponseCodePayOutRefs *sliceResponseCodePayOutRefs

type sliceResponseCodePayOutRefs struct{}

func (*sliceResponseCodePayOutRefs) TransactionStatus(slice ...ResponseCodePayOut) []EnumTransactionStatus {
	var result []EnumTransactionStatus
	for _, val := range slice {
		result = append(result, val.TransactionStatus())
	}

	return result
}

func (x IntegrationErrorMap) Description() string {
	switch x {
	case IntegrationErrorMap_IntegrationErrorMapNone:
		return ""
	case IntegrationErrorMap_IntegrationErrorMapInvalidTransaction:
		return "The bank has declined the transaction because of an invalid format or field. This indicates the card details were incorrect. Check card data entered and try again."
	case IntegrationErrorMap_IntegrationErrorMapInvalidMerchant:
		return "This error indicates that either your merchant facility is non-functional or the details entered into eWAY are incorrect."
	case IntegrationErrorMap_IntegrationErrorMapCardNotExist:
		return "The card issuing bank has declined the transaction as the credit card number is incorrectly entered, or does not exist. Check card details and try processing again."
	case IntegrationErrorMap_IntegrationErrorMapNotSupportedByReceiver:
		return ""
	case IntegrationErrorMap_IntegrationErrorMapFileUpdateNotSuccess:
		return ""
	case IntegrationErrorMap_IntegrationErrorMapNotSufficient:
		return "The customer’s card issuer has declined the transaction as the credit card does not have sufficient funds."
	case IntegrationErrorMap_IntegrationErrorMapNotPermitted:
		return "The customer’s card issuer has declined the transaction as this credit card cannot be used for this type of transaction."
	case IntegrationErrorMap_IntegrationErrorMapInvalidDate:
		return ""
	case IntegrationErrorMap_IntegrationErrorMapReconcileError:
		return ""
	case IntegrationErrorMap_IntegrationErrorMapForwardToIssuer:
		return ""
	case IntegrationErrorMap_IntegrationErrorMapInvalidAmount:
		return "The customer’s card issuer has declined the transaction because of an invalid format or field. Check the transaction information and try processing the transaction again."
	case IntegrationErrorMap_IntegrationErrorMapReenterTransaction:
		return "The transaction has not been processed and the customer should attempt to process the transaction again. No further information is provided from the bank as to the reason why this was not processed."
	case IntegrationErrorMap_IntegrationErrorMapNoAction:
		return "The customer’s card issuer has indicated there is a problem with the credit card number. The customer should use an alternate credit card, or contact their bank."
	case IntegrationErrorMap_IntegrationErrorMapFormatError:
		return "The customer’s card issuer does not recognise the transaction details being entered. This is due to a format error. The customer should check the transaction information and try processing the transaction again."
	case IntegrationErrorMap_IntegrationErrorMapIssuerSignOff:
		return "The customer’s card issuer has declined the transaction as it does not allow transactions originating through mail/telephone, fax, email or Internet orders."
	case IntegrationErrorMap_IntegrationErrorMapCompletedPartially:
		return ""
	case IntegrationErrorMap_IntegrationErrorMapNoCardRecord:
		return "The customer’s card issuer has declined the transaction as the credit card number does not exist."
	case IntegrationErrorMap_IntegrationErrorMapTransactionNotPermitted:
		return "The customer’s card issuer has declined the transaction as this credit card cannot be used for this type of transaction. This may be associated with a test credit card number."
	case IntegrationErrorMap_IntegrationErrorMapAmountIncorrect:
		return "The customer’s card issuer has declined the transaction due to the amount attempting to be processed."
	case IntegrationErrorMap_IntegrationErrorMapAlreadyReversed:
		return ""
	case IntegrationErrorMap_IntegrationErrorMapNoRoutingPath:
		return "The customer’s card issuer cannot be found for routing. This response code is often returned when the customer is using a test credit card number."
	case IntegrationErrorMap_IntegrationErrorMapSystemMalfunction:
		return "The customer’s card issuer was not able to process the transaction. The customer should attempt to process this transaction again."
	case IntegrationErrorMap_IntegrationErrorMapSoftDecline:
		return ""
	case IntegrationErrorMap_IntegrationErrorMapReferToIssuer:
		return "The customer’s card issuer has indicated there is a problem with the credentials used in the transaction. The customer should use an alternate credit card, or contact their bank."
	case IntegrationErrorMap_IntegrationErrorMapReferToIssuerSpecial:
		return "The customer’s card issuer has indicated there is a problem with the card number. The customer should use an alternate credit card, or contact their bank."
	case IntegrationErrorMap_IntegrationErrorMapError:
		return "The customer’s card issuer has declined the transaction as there is a problem with the card number. The customer should contact their card issuer and/or use an alternate card."
	case IntegrationErrorMap_IntegrationErrorMapApprovedPartialAmount:
		return "The transaction was successful."
	case IntegrationErrorMap_IntegrationErrorMapNoSuchIssuer:
		return "The customer’s card issuer does not exist. Check the card information and try processing the transaction again."
	case IntegrationErrorMap_IntegrationErrorMapCustomerCancellation:
		return ""
	case IntegrationErrorMap_IntegrationErrorMapCustomerDispute:
		return ""
	case IntegrationErrorMap_IntegrationErrorMapNoInvestmentAccount:
		return "The customer’s card issuer has declined the transaction as the account type selected is not valid for this credit card number."
	case IntegrationErrorMap_IntegrationErrorMapCardAcceptorCallAcquirer:
		return "The customer should use an alternative credit card. Neither eWAY nor the bank can provide more details."
	case IntegrationErrorMap_IntegrationErrorMapUnableToVerifyPIN:
		return ""
	case IntegrationErrorMap_IntegrationErrorMapAuthenticationFailure:
		return ""
	case IntegrationErrorMap_IntegrationErrorMapIssuerSwitchInoperative:
		return "The customer’s card issuer is unable to be contacted to authorise the transaction. The customer should attempt to process this transaction again."
	case IntegrationErrorMap_IntegrationErrorMapInvalidResponse:
		return ""
	case IntegrationErrorMap_IntegrationErrorMapCardAcceptorContactAcquirer:
		return "The customer’s card issuer has declined the transaction and requested that your customer’s card be retained as the card was reported as lost or stolen."
	case IntegrationErrorMap_IntegrationErrorMapFunctionNotSupported:
		return "The customer’s card issuer has declined the transaction as it does not allow this type of transaction."
	case IntegrationErrorMap_IntegrationErrorMapNoCheckingAccount:
		return "The customer’s card issuer has declined the transaction as the credit card number is associated to a cheque account that does not exist."
	case IntegrationErrorMap_IntegrationErrorMapPreviousMessageNotFound:
		return ""
	case IntegrationErrorMap_IntegrationErrorMapCryptographicErrorInPIN:
		return ""
	case IntegrationErrorMap_IntegrationErrorMapPINValidationNotPossible:
		return ""
	case IntegrationErrorMap_IntegrationErrorMapCutoffInProcess:
		return "The customer’s card issuer is temporarily not able to process this customer’s credit card."
	case IntegrationErrorMap_IntegrationErrorMapSurchargeNotSupported:
		return ""
	case IntegrationErrorMap_IntegrationErrorMapForceSTIP:
		return ""
	case IntegrationErrorMap_IntegrationErrorMapExpiredCard:
		return "The customer’s card issuer has declined the transaction as Card has expired or the date is incorrect. Check the expiry date in the transaction and try processing the transaction again."
	case IntegrationErrorMap_IntegrationErrorMapSuspectedFraud:
		return "The customer’s card issuer has declined the transaction as there is a suspected fraud on this credit card number."
	case IntegrationErrorMap_IntegrationErrorMapNoCreditAccount:
		return "The customer’s card issuer has declined the transaction as the Credit Card number used is not a credit account."
	case IntegrationErrorMap_IntegrationErrorMapNoUniversalAccount:
		return "The customer’s card issuer has declined the transaction as the account type selected is not valid for this credit card number."
	case IntegrationErrorMap_IntegrationErrorMapExpiredCardAlternate:
		return "The customer’s card is expired. Contact your customer and confirm that the correct dates were entered and that there were no mistakes (e.g. 05/21 rather than 05/20)."
	case IntegrationErrorMap_IntegrationErrorMapSuspectedFraudAlternate:
		return "The customer’s card issuer has declined this transaction as the credit card appears to be fraudulent. While you could contact this customer yourself, it's very possible that this transaction is fraudulent."
	case IntegrationErrorMap_IntegrationErrorMapCardAcceptorWriteToAcquirer:
		return "The customer’s card issuer has declined the transaction. The customer should contact their bank and retry the transaction."
	case IntegrationErrorMap_IntegrationErrorMapSecurityViolation:
		return "The customer’s card issuer has declined the transaction. The customer should use an alternate credit card, and contact their bank."
	case IntegrationErrorMap_IntegrationErrorMapCryptographicFailure:
		return ""
	case IntegrationErrorMap_IntegrationErrorMapViolationOfLaw:
		return "The customer’s card issuer has declined the transaction and request the customer to contact their bank."
	case IntegrationErrorMap_IntegrationErrorMapFraud:
		return ""
	case IntegrationErrorMap_IntegrationErrorMapCashRequestExceedsLimit:
		return ""
	case IntegrationErrorMap_IntegrationErrorMapIneligibleForResubmission:
		return ""
	case IntegrationErrorMap_IntegrationErrorMapStopPaymentOrder:
		return ""
	case IntegrationErrorMap_IntegrationErrorMapHonorWithIdentification:
		return "Transaction processed successfully - identification NOT required. This code is returned by some banks in place of 00 response."
	case IntegrationErrorMap_IntegrationErrorMapUnacceptableTransactionFee:
		return "An unspecified bank error has occurred. No further information is available from eWAY or the bank. The customer should attempt to process the transaction again."
	case IntegrationErrorMap_IntegrationErrorMapDuplicateFileUpdateRecord:
		return ""
	case IntegrationErrorMap_IntegrationErrorMapActivityCountExceeded:
		return "The customer’s card issuer has declined the transaction as the customer has exceeded the withdrawal frequency limit."
	case IntegrationErrorMap_IntegrationErrorMapCardPickUpAtATM:
		return "The customer’s card issuer has declined the transaction as the card is suspected to be a counterfeit. The customer’s card issuer has requested that your customer’s credit card be retained by you."
	case IntegrationErrorMap_IntegrationErrorMapDataMismatchOriginalMessage:
		return ""
	case IntegrationErrorMap_IntegrationErrorMapInvalidAuthorizationLifeCycle:
		return ""
	case IntegrationErrorMap_IntegrationErrorMapDuplicateTransmission:
		return "The customer’s card issuer has declined the transaction as this transaction appears to be a duplicate transmission."
	case IntegrationErrorMap_IntegrationErrorMapDeclineForCVV2Failure:
		return ""
	case IntegrationErrorMap_IntegrationErrorMapCardAuthenticationFailed:
		return ""
	case IntegrationErrorMap_IntegrationErrorMapPickUpCardSpecial:
		return ""
	case IntegrationErrorMap_IntegrationErrorMapRequestInProgress:
		return "The card issuer has indicated there is a problem with the card number. The customer should contact their bank and/or use an alternate credit card."
	case IntegrationErrorMap_IntegrationErrorMapApprovedUpdateTrack3:
		return ""
	case IntegrationErrorMap_IntegrationErrorMapSuspectedMalfunction:
		return "The customer’s card issuer could not be contacted during the transaction. The customer should check the card information and try processing the transaction again."
	case IntegrationErrorMap_IntegrationErrorMapRestrictedCard:
		return "The customer’s card issuer has declined the transaction and requested that your customer’s card be retained."
	case IntegrationErrorMap_IntegrationErrorMapPickUpCardStolenCard:
		return "The customer’s card has been reported as stolen. While you could contact this customer yourself, it's very possible that this transaction is fraudulent."
	case IntegrationErrorMap_IntegrationErrorMapExceedsWithdrawalLimit:
		return "The customer’s card issuer has declined the transaction as it will exceed the customer’s card limit."
	case IntegrationErrorMap_IntegrationErrorMapRestrictedCardAlternate:
		return "The customer’s card issuer has declined the transaction as the credit card has some restrictions."
	case IntegrationErrorMap_IntegrationErrorMapResponseReceivedTooLate:
		return ""
	case IntegrationErrorMap_IntegrationErrorMapTooManyWrongPINTries:
		return "The customer’s card issuer has declined the transaction as the customer has entered the incorrect PIN more than three times."
	case IntegrationErrorMap_IntegrationErrorMapIncorrectCVV:
		return "The customer’s card issuer has declined the transaction as the CVV is incorrect. The customer should check the CVV details (the 3 numbers on the back for Visa/MC, or 4 numbers on the front for AMEX) and try again."
	case IntegrationErrorMap_IntegrationErrorMapNoReasonToDecline:
		return ""
	case IntegrationErrorMap_IntegrationErrorMapDeclinedByGateway:
		return ""
	case IntegrationErrorMap_IntegrationErrorMapTransactionAmountExceedsPreAuth:
		return ""
	case IntegrationErrorMap_IntegrationErrorMapApproved:
		return "Transaction has been processed successfully. No further action is required from you as the merchant."
	case IntegrationErrorMap_IntegrationErrorMapPickUpCard:
		return "The customer’s card issuer has declined the transaction and requested that the card be retained as the card may have been reported as lost or stolen."
	case IntegrationErrorMap_IntegrationErrorMapDoNotHonor:
		return "The '05 Do Not Honour' error is a generic bank response code that has several possible causes. However, it generally indicates a card error rather than an error with your merchant facility. The '05' error indicates your bank declining the customer's card for an unspecified reason."
	case IntegrationErrorMap_IntegrationErrorMapUnableToLocateRecord:
		return "The customer’s card issuer does not recognise the credit card details. The customer should check the card information and try processing the transaction again."
	case IntegrationErrorMap_IntegrationErrorMapFileUpdateFieldEditError:
		return ""
	case IntegrationErrorMap_IntegrationErrorMapFileTemporarilyUnavailable:
		return ""
	case IntegrationErrorMap_IntegrationErrorMapCardAcceptor:
		return "The customer’s card issuer has declined the transaction and requested that your customer’s card be retained."
	case IntegrationErrorMap_IntegrationErrorMapAllowablePINTriesExceeded:
		return "The customer’s card issuer has declined the transaction as the customer has entered the incorrect PIN three times."
	case IntegrationErrorMap_IntegrationErrorMapPickUpCardLostCard:
		return "The customer’s card issuer has declined the transaction as the card has been reported lost."
	case IntegrationErrorMap_IntegrationErrorMapNoSavingsAccount:
		return "The customer’s card issuer has declined the transaction as the credit card number is associated to a savings account that does not exist."
	case IntegrationErrorMap_IntegrationErrorMapIncorrectPIN:
		return "The customer’s card issuer has declined the transaction as the customer has entered an incorrect PIN. The customer should re-enter their PIN."
	case IntegrationErrorMap_IntegrationErrorMapCashServiceNotAvailable:
		return ""
	case IntegrationErrorMap_IntegrationErrorMapForwardIssuer:
		return ""
	case IntegrationErrorMap_IntegrationErrorMapCGICheckFailed:
		return "Запрос не прошел CGI-проверку"
	case IntegrationErrorMap_IntegrationErrorMapInvalidAmountField:
		return "Ошибка в поле 'Amount' запроса"
	case IntegrationErrorMap_IntegrationErrorMapInvalidCurrencyField:
		return "Ошибка в поле 'Currency' запроса"
	case IntegrationErrorMap_IntegrationErrorMapHostNotResponding:
		return "Хост эквайера (TS) не отвечает либо неверный формат файла шаблона ответа модуля eGateway"
	case IntegrationErrorMap_IntegrationErrorMapNoConnectionWithPINPad:
		return "Нет соединения с PIN-клавиатурой интернет-терминала"
	case IntegrationErrorMap_IntegrationErrorMapConnectionErrorDuringProcessing:
		return "Ошибка соединения с хостом эквайера (TS) во время обработки транзакции"
	case IntegrationErrorMap_IntegrationErrorMapMissingMandatoryField:
		return "В запросе не заполнено обязательное поле"
	case IntegrationErrorMap_IntegrationErrorMapNoConnectionWithHost:
		return "Нет соединения с хостом эквайера (TS)"
	case IntegrationErrorMap_IntegrationErrorMapInvalidMerchantIDField:
		return "Ошибка в поле 'Merchant ID' запроса"
	case IntegrationErrorMap_IntegrationErrorMapInvalidRRNField:
		return "Ошибка в поле 'RRN' запроса"
	case IntegrationErrorMap_IntegrationErrorMapAccessDenied:
		return "Терминалу отказано в доступе к модулю e-Gateway"
	case IntegrationErrorMap_IntegrationErrorMapTerminalBusy:
		return "На терминале выполняется другая транзакция"
	case IntegrationErrorMap_IntegrationErrorMapIPAddressMismatch:
		return "IP-адрес источника транзакции не соответствует ожидаемому"
	case IntegrationErrorMap_IntegrationErrorMapAuthenticationError:
		return "Ошибка в запросе на аутентификационную информацию либо аутентификация неуспешна"
	case IntegrationErrorMap_IntegrationErrorMapEGatewaySetupError:
		return "Ошибка настройки модуля e-Gateway"
	case IntegrationErrorMap_IntegrationErrorMapIncorrectHostResponse:
		return "Некорректный ответ хоста эквайера (TS), например, отсутствуют обязательные поля"
	case IntegrationErrorMap_IntegrationErrorMapInvalidCardNumberField:
		return "Ошибка в поле 'Card number' запроса"
	case IntegrationErrorMap_IntegrationErrorMapInvalidCardExpirationDateField:
		return "Ошибка в поле 'Card expiration date' запроса"
	case IntegrationErrorMap_IntegrationErrorMapInvalidCVC2Field:
		return "Ошибка в поле 'CVC2' или 'CVC2 Description' запроса"
	case IntegrationErrorMap_IntegrationErrorMapTimeExceeded:
		return "Превышен допустимый временной интервал между запросом и временем модуля e-Gateway"
	default:
		return "No error"
	}
}

func (x IntegrationErrorMap) IntegrationErrorId() IntegrationError {
	switch x {
	case IntegrationErrorMap_IntegrationErrorMapNone:
		return IntegrationError_None
	case IntegrationErrorMap_IntegrationErrorMapInvalidTransaction:
		return IntegrationError_InvalidCard
	case IntegrationErrorMap_IntegrationErrorMapInvalidMerchant:
		return IntegrationError_PaymentForbiddenForMerchant
	case IntegrationErrorMap_IntegrationErrorMapCardNotExist:
		return IntegrationError_NonExistentCard
	case IntegrationErrorMap_IntegrationErrorMapNotSupportedByReceiver:
		return IntegrationError_UndefinedError
	case IntegrationErrorMap_IntegrationErrorMapFileUpdateNotSuccess:
		return IntegrationError_UndefinedError
	case IntegrationErrorMap_IntegrationErrorMapNotSufficient:
		return IntegrationError_InsufficientFunds
	case IntegrationErrorMap_IntegrationErrorMapNotPermitted:
		return IntegrationError_TransactionDeclinedByAcquirer
	case IntegrationErrorMap_IntegrationErrorMapInvalidDate:
		return IntegrationError_UndefinedError
	case IntegrationErrorMap_IntegrationErrorMapReconcileError:
		return IntegrationError_UndefinedError
	case IntegrationErrorMap_IntegrationErrorMapForwardToIssuer:
		return IntegrationError_UndefinedError
	case IntegrationErrorMap_IntegrationErrorMapInvalidAmount:
		return IntegrationError_TransactionDeclinedByIssuer
	case IntegrationErrorMap_IntegrationErrorMapReenterTransaction:
		return IntegrationError_UndefinedError
	case IntegrationErrorMap_IntegrationErrorMapNoAction:
		return IntegrationError_TransactionDeclinedByIssuer
	case IntegrationErrorMap_IntegrationErrorMapFormatError:
		return IntegrationError_TransactionDeclinedByIssuer
	case IntegrationErrorMap_IntegrationErrorMapIssuerSignOff:
		return IntegrationError_TransactionDeclinedByIssuer
	case IntegrationErrorMap_IntegrationErrorMapCompletedPartially:
		return IntegrationError_None
	case IntegrationErrorMap_IntegrationErrorMapNoCardRecord:
		return IntegrationError_IncorrectCardNumber
	case IntegrationErrorMap_IntegrationErrorMapTransactionNotPermitted:
		return IntegrationError_InvalidCard
	case IntegrationErrorMap_IntegrationErrorMapAmountIncorrect:
		return IntegrationError_TransactionDeclinedByIssuer
	case IntegrationErrorMap_IntegrationErrorMapAlreadyReversed:
		return IntegrationError_UndefinedError
	case IntegrationErrorMap_IntegrationErrorMapNoRoutingPath:
		return IntegrationError_TransactionDeclinedByIssuer
	case IntegrationErrorMap_IntegrationErrorMapSystemMalfunction:
		return IntegrationError_TransactionDeclinedByIssuer
	case IntegrationErrorMap_IntegrationErrorMapSoftDecline:
		return IntegrationError_UndefinedError
	case IntegrationErrorMap_IntegrationErrorMapReferToIssuer:
		return IntegrationError_InvalidCard
	case IntegrationErrorMap_IntegrationErrorMapReferToIssuerSpecial:
		return IntegrationError_InvalidCard
	case IntegrationErrorMap_IntegrationErrorMapError:
		return IntegrationError_UndefinedError
	case IntegrationErrorMap_IntegrationErrorMapApprovedPartialAmount:
		return IntegrationError_None
	case IntegrationErrorMap_IntegrationErrorMapNoSuchIssuer:
		return IntegrationError_UnavailableIssuer
	case IntegrationErrorMap_IntegrationErrorMapCustomerCancellation:
		return IntegrationError_UndefinedError
	case IntegrationErrorMap_IntegrationErrorMapCustomerDispute:
		return IntegrationError_UndefinedError
	case IntegrationErrorMap_IntegrationErrorMapNoInvestmentAccount:
		return IntegrationError_InvalidCard
	case IntegrationErrorMap_IntegrationErrorMapCardAcceptorCallAcquirer:
		return IntegrationError_InvalidCard
	case IntegrationErrorMap_IntegrationErrorMapUnableToVerifyPIN:
		return IntegrationError_IncorrectCVVCVC
	case IntegrationErrorMap_IntegrationErrorMapAuthenticationFailure:
		return IntegrationError_UndefinedError
	case IntegrationErrorMap_IntegrationErrorMapIssuerSwitchInoperative:
		return IntegrationError_TransactionDeclinedByIssuer
	case IntegrationErrorMap_IntegrationErrorMapInvalidResponse:
		return IntegrationError_UndefinedError
	case IntegrationErrorMap_IntegrationErrorMapCardAcceptorContactAcquirer:
		return IntegrationError_LostCard
	case IntegrationErrorMap_IntegrationErrorMapFunctionNotSupported:
		return IntegrationError_InvalidCard
	case IntegrationErrorMap_IntegrationErrorMapNoCheckingAccount:
		return IntegrationError_InvalidCard
	case IntegrationErrorMap_IntegrationErrorMapPreviousMessageNotFound:
		return IntegrationError_UndefinedError
	case IntegrationErrorMap_IntegrationErrorMapCryptographicErrorInPIN:
		return IntegrationError_IncorrectCVVCVC
	case IntegrationErrorMap_IntegrationErrorMapPINValidationNotPossible:
		return IntegrationError_UndefinedError
	case IntegrationErrorMap_IntegrationErrorMapCutoffInProcess:
		return IntegrationError_TransactionDeclinedByIssuer
	case IntegrationErrorMap_IntegrationErrorMapSurchargeNotSupported:
		return IntegrationError_UndefinedError
	case IntegrationErrorMap_IntegrationErrorMapForceSTIP:
		return IntegrationError_UndefinedError
	case IntegrationErrorMap_IntegrationErrorMapExpiredCard:
		return IntegrationError_CardHasExpired
	case IntegrationErrorMap_IntegrationErrorMapSuspectedFraud:
		return IntegrationError_SuspiciousClient
	case IntegrationErrorMap_IntegrationErrorMapNoCreditAccount:
		return IntegrationError_InvalidCard
	case IntegrationErrorMap_IntegrationErrorMapNoUniversalAccount:
		return IntegrationError_InvalidCard
	case IntegrationErrorMap_IntegrationErrorMapExpiredCardAlternate:
		return IntegrationError_CardHasExpired
	case IntegrationErrorMap_IntegrationErrorMapSuspectedFraudAlternate:
		return IntegrationError_SuspiciousClient
	case IntegrationErrorMap_IntegrationErrorMapCardAcceptorWriteToAcquirer:
		return IntegrationError_UndefinedError
	case IntegrationErrorMap_IntegrationErrorMapSecurityViolation:
		return IntegrationError_TransactionDeclinedByIssuer
	case IntegrationErrorMap_IntegrationErrorMapCryptographicFailure:
		return IntegrationError_UndefinedError
	case IntegrationErrorMap_IntegrationErrorMapViolationOfLaw:
		return IntegrationError_TransactionDeclinedByIssuer
	case IntegrationErrorMap_IntegrationErrorMapFraud:
		return IntegrationError_SuspiciousClient
	case IntegrationErrorMap_IntegrationErrorMapCashRequestExceedsLimit:
		return IntegrationError_UndefinedError
	case IntegrationErrorMap_IntegrationErrorMapIneligibleForResubmission:
		return IntegrationError_UndefinedError
	case IntegrationErrorMap_IntegrationErrorMapStopPaymentOrder:
		return IntegrationError_UndefinedError
	case IntegrationErrorMap_IntegrationErrorMapHonorWithIdentification:
		return IntegrationError_None
	case IntegrationErrorMap_IntegrationErrorMapUnacceptableTransactionFee:
		return IntegrationError_UndefinedError
	case IntegrationErrorMap_IntegrationErrorMapDuplicateFileUpdateRecord:
		return IntegrationError_UndefinedError
	case IntegrationErrorMap_IntegrationErrorMapActivityCountExceeded:
		return IntegrationError_ExceedsTransactionFrequencyLimit
	case IntegrationErrorMap_IntegrationErrorMapCardPickUpAtATM:
		return IntegrationError_LostCard
	case IntegrationErrorMap_IntegrationErrorMapDataMismatchOriginalMessage:
		return IntegrationError_UndefinedError
	case IntegrationErrorMap_IntegrationErrorMapInvalidAuthorizationLifeCycle:
		return IntegrationError_UndefinedError
	case IntegrationErrorMap_IntegrationErrorMapDuplicateTransmission:
		return IntegrationError_TransactionDeclinedByIssuer
	case IntegrationErrorMap_IntegrationErrorMapDeclineForCVV2Failure:
		return IntegrationError_IncorrectCVVCVC
	case IntegrationErrorMap_IntegrationErrorMapCardAuthenticationFailed:
		return IntegrationError_InvalidCard
	case IntegrationErrorMap_IntegrationErrorMapPickUpCardSpecial:
		return IntegrationError_LostCard
	case IntegrationErrorMap_IntegrationErrorMapRequestInProgress:
		return IntegrationError_TransactionDeclinedByIssuer
	case IntegrationErrorMap_IntegrationErrorMapApprovedUpdateTrack3:
		return IntegrationError_None
	case IntegrationErrorMap_IntegrationErrorMapSuspectedMalfunction:
		return IntegrationError_TransactionDeclinedByIssuer
	case IntegrationErrorMap_IntegrationErrorMapRestrictedCard:
		return IntegrationError_TransactionDeclinedByIssuer
	case IntegrationErrorMap_IntegrationErrorMapPickUpCardStolenCard:
		return IntegrationError_StolenCard
	case IntegrationErrorMap_IntegrationErrorMapExceedsWithdrawalLimit:
		return IntegrationError_ExceedsAmountLimit
	case IntegrationErrorMap_IntegrationErrorMapRestrictedCardAlternate:
		return IntegrationError_InvalidCard
	case IntegrationErrorMap_IntegrationErrorMapResponseReceivedTooLate:
		return IntegrationError_UndefinedError
	case IntegrationErrorMap_IntegrationErrorMapTooManyWrongPINTries:
		return IntegrationError_BlockedCard
	case IntegrationErrorMap_IntegrationErrorMapIncorrectCVV:
		return IntegrationError_IncorrectCVVCVC
	case IntegrationErrorMap_IntegrationErrorMapNoReasonToDecline:
		return IntegrationError_UndefinedError
	case IntegrationErrorMap_IntegrationErrorMapDeclinedByGateway:
		return IntegrationError_UndefinedError
	case IntegrationErrorMap_IntegrationErrorMapTransactionAmountExceedsPreAuth:
		return IntegrationError_ExceedsAmountLimit
	case IntegrationErrorMap_IntegrationErrorMapApproved:
		return IntegrationError_None
	case IntegrationErrorMap_IntegrationErrorMapPickUpCard:
		return IntegrationError_LostCard
	case IntegrationErrorMap_IntegrationErrorMapDoNotHonor:
		return IntegrationError_InvalidCard
	case IntegrationErrorMap_IntegrationErrorMapUnableToLocateRecord:
		return IntegrationError_TransactionDeclinedByIssuer
	case IntegrationErrorMap_IntegrationErrorMapFileUpdateFieldEditError:
		return IntegrationError_UndefinedError
	case IntegrationErrorMap_IntegrationErrorMapFileTemporarilyUnavailable:
		return IntegrationError_UndefinedError
	case IntegrationErrorMap_IntegrationErrorMapCardAcceptor:
		return IntegrationError_InvalidCard
	case IntegrationErrorMap_IntegrationErrorMapAllowablePINTriesExceeded:
		return IntegrationError_BlockedCard
	case IntegrationErrorMap_IntegrationErrorMapPickUpCardLostCard:
		return IntegrationError_LostCard
	case IntegrationErrorMap_IntegrationErrorMapNoSavingsAccount:
		return IntegrationError_InvalidCard
	case IntegrationErrorMap_IntegrationErrorMapIncorrectPIN:
		return IntegrationError_IncorrectCVVCVC
	case IntegrationErrorMap_IntegrationErrorMapCashServiceNotAvailable:
		return IntegrationError_UndefinedError
	case IntegrationErrorMap_IntegrationErrorMapForwardIssuer:
		return IntegrationError_UndefinedError
	case IntegrationErrorMap_IntegrationErrorMapCGICheckFailed:
		return IntegrationError_ThreeDSAuthFailed
	case IntegrationErrorMap_IntegrationErrorMapInvalidAmountField:
		return IntegrationError_ExceedsAmountLimit
	case IntegrationErrorMap_IntegrationErrorMapInvalidCurrencyField:
		return IntegrationError_UndefinedError
	case IntegrationErrorMap_IntegrationErrorMapHostNotResponding:
		return IntegrationError_UnavailableIssuer
	case IntegrationErrorMap_IntegrationErrorMapNoConnectionWithPINPad:
		return IntegrationError_UndefinedError
	case IntegrationErrorMap_IntegrationErrorMapConnectionErrorDuringProcessing:
		return IntegrationError_UnavailableAcquirer
	case IntegrationErrorMap_IntegrationErrorMapMissingMandatoryField:
		return IntegrationError_IncorrectCardNumber
	case IntegrationErrorMap_IntegrationErrorMapNoConnectionWithHost:
		return IntegrationError_UnavailableAcquirer
	case IntegrationErrorMap_IntegrationErrorMapInvalidMerchantIDField:
		return IntegrationError_NonExistentCard
	case IntegrationErrorMap_IntegrationErrorMapInvalidRRNField:
		return IntegrationError_UndefinedError
	case IntegrationErrorMap_IntegrationErrorMapAccessDenied:
		return IntegrationError_BlockedCard
	case IntegrationErrorMap_IntegrationErrorMapTerminalBusy:
		return IntegrationError_ExceedsTransactionFrequencyLimit
	case IntegrationErrorMap_IntegrationErrorMapIPAddressMismatch:
		return IntegrationError_SuspiciousClient
	case IntegrationErrorMap_IntegrationErrorMapAuthenticationError:
		return IntegrationError_ThreeDSAuthFailed
	case IntegrationErrorMap_IntegrationErrorMapEGatewaySetupError:
		return IntegrationError_UndefinedError
	case IntegrationErrorMap_IntegrationErrorMapIncorrectHostResponse:
		return IntegrationError_TransactionDeclinedByAcquirer
	case IntegrationErrorMap_IntegrationErrorMapInvalidCardNumberField:
		return IntegrationError_IncorrectCardNumber
	case IntegrationErrorMap_IntegrationErrorMapInvalidCardExpirationDateField:
		return IntegrationError_IncorrectCardExpDate
	case IntegrationErrorMap_IntegrationErrorMapInvalidCVC2Field:
		return IntegrationError_IncorrectCVVCVC
	case IntegrationErrorMap_IntegrationErrorMapTimeExceeded:
		return IntegrationError_UndefinedError
	default:
		return IntegrationError_None
	}
}

func (x IntegrationErrorMap) IsoMessage() string {
	switch x {
	case IntegrationErrorMap_IntegrationErrorMapNone:
		return "VIP Approval"
	case IntegrationErrorMap_IntegrationErrorMapInvalidTransaction:
		return "Invalid transaction"
	case IntegrationErrorMap_IntegrationErrorMapInvalidMerchant:
		return "Invalid merchant"
	case IntegrationErrorMap_IntegrationErrorMapCardNotExist:
		return "Card number does not exist"
	case IntegrationErrorMap_IntegrationErrorMapNotSupportedByReceiver:
		return "File update not supported by receiverr"
	case IntegrationErrorMap_IntegrationErrorMapFileUpdateNotSuccess:
		return "File update not successful"
	case IntegrationErrorMap_IntegrationErrorMapNotSufficient:
		return "Not sufficient funds"
	case IntegrationErrorMap_IntegrationErrorMapNotPermitted:
		return "Transaction not permitted to card"
	case IntegrationErrorMap_IntegrationErrorMapInvalidDate:
		return "Invalid date"
	case IntegrationErrorMap_IntegrationErrorMapReconcileError:
		return "Reconcile error"
	case IntegrationErrorMap_IntegrationErrorMapForwardToIssuer:
		return "Forward to issuer"
	case IntegrationErrorMap_IntegrationErrorMapInvalidAmount:
		return "Invalid amount"
	case IntegrationErrorMap_IntegrationErrorMapReenterTransaction:
		return "Re-enter transaction"
	case IntegrationErrorMap_IntegrationErrorMapNoAction:
		return "No action taken (no match)"
	case IntegrationErrorMap_IntegrationErrorMapFormatError:
		return "Format error"
	case IntegrationErrorMap_IntegrationErrorMapIssuerSignOff:
		return "Issuer sign-off"
	case IntegrationErrorMap_IntegrationErrorMapCompletedPartially:
		return "Completed partially"
	case IntegrationErrorMap_IntegrationErrorMapNoCardRecord:
		return "No card record"
	case IntegrationErrorMap_IntegrationErrorMapTransactionNotPermitted:
		return "Transaction not permitted to card"
	case IntegrationErrorMap_IntegrationErrorMapAmountIncorrect:
		return "Original amount incorrect"
	case IntegrationErrorMap_IntegrationErrorMapAlreadyReversed:
		return "Already reversed (by Switch)"
	case IntegrationErrorMap_IntegrationErrorMapNoRoutingPath:
		return "No routing path"
	case IntegrationErrorMap_IntegrationErrorMapSystemMalfunction:
		return "System malfunction"
	case IntegrationErrorMap_IntegrationErrorMapSoftDecline:
		return "Soft-Decline"
	case IntegrationErrorMap_IntegrationErrorMapReferToIssuer:
		return "Refer to issuer"
	case IntegrationErrorMap_IntegrationErrorMapReferToIssuerSpecial:
		return "Refer to issuer (special)"
	case IntegrationErrorMap_IntegrationErrorMapError:
		return "Error"
	case IntegrationErrorMap_IntegrationErrorMapApprovedPartialAmount:
		return "Approved for partial amount"
	case IntegrationErrorMap_IntegrationErrorMapNoSuchIssuer:
		return "No such issuer"
	case IntegrationErrorMap_IntegrationErrorMapCustomerCancellation:
		return "Customer cancellation"
	case IntegrationErrorMap_IntegrationErrorMapCustomerDispute:
		return "Customer dispute"
	case IntegrationErrorMap_IntegrationErrorMapNoInvestmentAccount:
		return "No investment account"
	case IntegrationErrorMap_IntegrationErrorMapCardAcceptorCallAcquirer:
		return "Card acceptor call acquirer"
	case IntegrationErrorMap_IntegrationErrorMapUnableToVerifyPIN:
		return "Unable to verify PIN"
	case IntegrationErrorMap_IntegrationErrorMapAuthenticationFailure:
		return "Authentication failure"
	case IntegrationErrorMap_IntegrationErrorMapIssuerSwitchInoperative:
		return "Issuer or switch inoperative"
	case IntegrationErrorMap_IntegrationErrorMapInvalidResponse:
		return "Invalid response"
	case IntegrationErrorMap_IntegrationErrorMapCardAcceptorContactAcquirer:
		return "Card acceptor contact acquirer"
	case IntegrationErrorMap_IntegrationErrorMapFunctionNotSupported:
		return "Function not supported"
	case IntegrationErrorMap_IntegrationErrorMapNoCheckingAccount:
		return "No checking account"
	case IntegrationErrorMap_IntegrationErrorMapPreviousMessageNotFound:
		return "Previous message not found"
	case IntegrationErrorMap_IntegrationErrorMapCryptographicErrorInPIN:
		return "Cryptographic error in PIN"
	case IntegrationErrorMap_IntegrationErrorMapPINValidationNotPossible:
		return "PIN validation not possible"
	case IntegrationErrorMap_IntegrationErrorMapCutoffInProcess:
		return "Cutoff is in process"
	case IntegrationErrorMap_IntegrationErrorMapSurchargeNotSupported:
		return "Surcharge amount not supported by deb"
	case IntegrationErrorMap_IntegrationErrorMapForceSTIP:
		return "Force STIP"
	case IntegrationErrorMap_IntegrationErrorMapExpiredCard:
		return "Expired card"
	case IntegrationErrorMap_IntegrationErrorMapSuspectedFraud:
		return "Suspected fraud"
	case IntegrationErrorMap_IntegrationErrorMapNoCreditAccount:
		return "No credit account"
	case IntegrationErrorMap_IntegrationErrorMapNoUniversalAccount:
		return "No universal account"
	case IntegrationErrorMap_IntegrationErrorMapExpiredCardAlternate:
		return "Expired card"
	case IntegrationErrorMap_IntegrationErrorMapSuspectedFraudAlternate:
		return "Suspected fraud"
	case IntegrationErrorMap_IntegrationErrorMapCardAcceptorWriteToAcquirer:
		return "Card acceptor contact acquirer"
	case IntegrationErrorMap_IntegrationErrorMapSecurityViolation:
		return "Security violation"
	case IntegrationErrorMap_IntegrationErrorMapCryptographicFailure:
		return "Cryptographic failure"
	case IntegrationErrorMap_IntegrationErrorMapViolationOfLaw:
		return "Violation of law"
	case IntegrationErrorMap_IntegrationErrorMapFraud:
		return "Fraud"
	case IntegrationErrorMap_IntegrationErrorMapCashRequestExceedsLimit:
		return "Cash request exceeds issuer or approv"
	case IntegrationErrorMap_IntegrationErrorMapIneligibleForResubmission:
		return "Ineligible for resubmission"
	case IntegrationErrorMap_IntegrationErrorMapStopPaymentOrder:
		return "Stop Payment Order"
	case IntegrationErrorMap_IntegrationErrorMapHonorWithIdentification:
		return "Honor with identification"
	case IntegrationErrorMap_IntegrationErrorMapUnacceptableTransactionFee:
		return "Unacceptable transaction fee"
	case IntegrationErrorMap_IntegrationErrorMapDuplicateFileUpdateRecord:
		return "Duplicate file update record"
	case IntegrationErrorMap_IntegrationErrorMapActivityCountExceeded:
		return "Activity count exceeded"
	case IntegrationErrorMap_IntegrationErrorMapCardPickUpAtATM:
		return "Card pick up at ATM"
	case IntegrationErrorMap_IntegrationErrorMapDataMismatchOriginalMessage:
		return "Data does not match original message"
	case IntegrationErrorMap_IntegrationErrorMapInvalidAuthorizationLifeCycle:
		return "Invalid authorization life cycle"
	case IntegrationErrorMap_IntegrationErrorMapDuplicateTransmission:
		return "Duplicate transmission"
	case IntegrationErrorMap_IntegrationErrorMapDeclineForCVV2Failure:
		return "Decline for CVV2 failure"
	case IntegrationErrorMap_IntegrationErrorMapCardAuthenticationFailed:
		return "Card Authentication failed"
	case IntegrationErrorMap_IntegrationErrorMapPickUpCardSpecial:
		return "Pick up card (special)"
	case IntegrationErrorMap_IntegrationErrorMapRequestInProgress:
		return "Request in progress"
	case IntegrationErrorMap_IntegrationErrorMapApprovedUpdateTrack3:
		return "Approved update track 3"
	case IntegrationErrorMap_IntegrationErrorMapSuspectedMalfunction:
		return "Suspected malfunction"
	case IntegrationErrorMap_IntegrationErrorMapRestrictedCard:
		return "Restricted card"
	case IntegrationErrorMap_IntegrationErrorMapPickUpCardStolenCard:
		return "Pick up card (stolen card)"
	case IntegrationErrorMap_IntegrationErrorMapExceedsWithdrawalLimit:
		return "Exceeds withdrawal limit"
	case IntegrationErrorMap_IntegrationErrorMapRestrictedCardAlternate:
		return "Restricted card"
	case IntegrationErrorMap_IntegrationErrorMapResponseReceivedTooLate:
		return "Response received too late"
	case IntegrationErrorMap_IntegrationErrorMapTooManyWrongPINTries:
		return "Too many wrong PIN tries"
	case IntegrationErrorMap_IntegrationErrorMapIncorrectCVV:
		return "Incorrect CVV"
	case IntegrationErrorMap_IntegrationErrorMapNoReasonToDecline:
		return "No reason to decline"
	case IntegrationErrorMap_IntegrationErrorMapDeclinedByGateway:
		return "Declined by gateway"
	case IntegrationErrorMap_IntegrationErrorMapTransactionAmountExceedsPreAuth:
		return "Transaction amount exceeds preauthori"
	case IntegrationErrorMap_IntegrationErrorMapApproved:
		return "Approved"
	case IntegrationErrorMap_IntegrationErrorMapPickUpCard:
		return "Pick up card"
	case IntegrationErrorMap_IntegrationErrorMapDoNotHonor:
		return "Do not honor"
	case IntegrationErrorMap_IntegrationErrorMapUnableToLocateRecord:
		return "Unable to locate record"
	case IntegrationErrorMap_IntegrationErrorMapFileUpdateFieldEditError:
		return "File update field edit error"
	case IntegrationErrorMap_IntegrationErrorMapFileTemporarilyUnavailable:
		return "File temporarily unavailable"
	case IntegrationErrorMap_IntegrationErrorMapCardAcceptor:
		return "Card acceptor call acquirer"
	case IntegrationErrorMap_IntegrationErrorMapAllowablePINTriesExceeded:
		return "Allowable PIN tries exceeded"
	case IntegrationErrorMap_IntegrationErrorMapPickUpCardLostCard:
		return "Pick up card (lost card)"
	case IntegrationErrorMap_IntegrationErrorMapNoSavingsAccount:
		return "No savings account"
	case IntegrationErrorMap_IntegrationErrorMapIncorrectPIN:
		return "Incorrect PIN"
	case IntegrationErrorMap_IntegrationErrorMapCashServiceNotAvailable:
		return "Cash service not available"
	case IntegrationErrorMap_IntegrationErrorMapForwardIssuer:
		return "Forward to issuer"
	case IntegrationErrorMap_IntegrationErrorMapCGICheckFailed:
		return "CGI check failed"
	case IntegrationErrorMap_IntegrationErrorMapInvalidAmountField:
		return "Invalid 'Amount' field"
	case IntegrationErrorMap_IntegrationErrorMapInvalidCurrencyField:
		return "Invalid 'Currency' field"
	case IntegrationErrorMap_IntegrationErrorMapHostNotResponding:
		return "Host not responding or invalid template format"
	case IntegrationErrorMap_IntegrationErrorMapNoConnectionWithPINPad:
		return "No connection with PIN-pad"
	case IntegrationErrorMap_IntegrationErrorMapConnectionErrorDuringProcessing:
		return "Connection error during transaction processing"
	case IntegrationErrorMap_IntegrationErrorMapMissingMandatoryField:
		return "Missing mandatory field"
	case IntegrationErrorMap_IntegrationErrorMapNoConnectionWithHost:
		return "No connection with the host"
	case IntegrationErrorMap_IntegrationErrorMapInvalidMerchantIDField:
		return "Invalid 'Merchant ID' field"
	case IntegrationErrorMap_IntegrationErrorMapInvalidRRNField:
		return "Invalid 'RRN' field"
	case IntegrationErrorMap_IntegrationErrorMapAccessDenied:
		return "Access denied"
	case IntegrationErrorMap_IntegrationErrorMapTerminalBusy:
		return "Terminal busy"
	case IntegrationErrorMap_IntegrationErrorMapIPAddressMismatch:
		return "IP address mismatch"
	case IntegrationErrorMap_IntegrationErrorMapAuthenticationError:
		return "Authentication error"
	case IntegrationErrorMap_IntegrationErrorMapEGatewaySetupError:
		return "E-Gateway module setup error"
	case IntegrationErrorMap_IntegrationErrorMapIncorrectHostResponse:
		return "Incorrect host response"
	case IntegrationErrorMap_IntegrationErrorMapInvalidCardNumberField:
		return "Invalid 'Card number' field"
	case IntegrationErrorMap_IntegrationErrorMapInvalidCardExpirationDateField:
		return "Invalid 'Card expiration date' field"
	case IntegrationErrorMap_IntegrationErrorMapInvalidCVC2Field:
		return "Invalid 'CVC2' field"
	case IntegrationErrorMap_IntegrationErrorMapTimeExceeded:
		return "Time exceeded"
	default:
		return "None"
	}
}

// Created reference to IntegrationErrorMap

//	|	IntegrationErrorMap                                                   	|	Description                                                                                                                                                                                                                                                                                	|	IntegrationErrorId                               	|	IsoMessage                                      	|
//	|	IntegrationErrorMap_IntegrationErrorMapNone                           	|	""                                                                                                                                                                                                                                                                                         	|	IntegrationError_None                            	|	"VIP Approval"                                  	|
//	|	IntegrationErrorMap_IntegrationErrorMapInvalidTransaction             	|	"The bank has declined the transaction because of an invalid format or field. This indicates the card details were incorrect. Check card data entered and try again."                                                                                                                      	|	IntegrationError_InvalidCard                     	|	"Invalid transaction"                           	|
//	|	IntegrationErrorMap_IntegrationErrorMapInvalidMerchant                	|	"This error indicates that either your merchant facility is non-functional or the details entered into eWAY are incorrect."                                                                                                                                                                	|	IntegrationError_PaymentForbiddenForMerchant     	|	"Invalid merchant"                              	|
//	|	IntegrationErrorMap_IntegrationErrorMapCardNotExist                   	|	"The card issuing bank has declined the transaction as the credit card number is incorrectly entered, or does not exist. Check card details and try processing again."                                                                                                                     	|	IntegrationError_NonExistentCard                 	|	"Card number does not exist"                    	|
//	|	IntegrationErrorMap_IntegrationErrorMapNotSupportedByReceiver         	|	""                                                                                                                                                                                                                                                                                         	|	IntegrationError_UndefinedError                  	|	"File update not supported by receiverr"        	|
//	|	IntegrationErrorMap_IntegrationErrorMapFileUpdateNotSuccess           	|	""                                                                                                                                                                                                                                                                                         	|	IntegrationError_UndefinedError                  	|	"File update not successful"                    	|
//	|	IntegrationErrorMap_IntegrationErrorMapNotSufficient                  	|	"The customer’s card issuer has declined the transaction as the credit card does not have sufficient funds."                                                                                                                                                                               	|	IntegrationError_InsufficientFunds               	|	"Not sufficient funds"                          	|
//	|	IntegrationErrorMap_IntegrationErrorMapNotPermitted                   	|	"The customer’s card issuer has declined the transaction as this credit card cannot be used for this type of transaction."                                                                                                                                                                 	|	IntegrationError_TransactionDeclinedByAcquirer   	|	"Transaction not permitted to card"             	|
//	|	IntegrationErrorMap_IntegrationErrorMapInvalidDate                    	|	""                                                                                                                                                                                                                                                                                         	|	IntegrationError_UndefinedError                  	|	"Invalid date"                                  	|
//	|	IntegrationErrorMap_IntegrationErrorMapReconcileError                 	|	""                                                                                                                                                                                                                                                                                         	|	IntegrationError_UndefinedError                  	|	"Reconcile error"                               	|
//	|	IntegrationErrorMap_IntegrationErrorMapForwardToIssuer                	|	""                                                                                                                                                                                                                                                                                         	|	IntegrationError_UndefinedError                  	|	"Forward to issuer"                             	|
//	|	IntegrationErrorMap_IntegrationErrorMapInvalidAmount                  	|	"The customer’s card issuer has declined the transaction because of an invalid format or field. Check the transaction information and try processing the transaction again."                                                                                                               	|	IntegrationError_TransactionDeclinedByIssuer     	|	"Invalid amount"                                	|
//	|	IntegrationErrorMap_IntegrationErrorMapReenterTransaction             	|	"The transaction has not been processed and the customer should attempt to process the transaction again. No further information is provided from the bank as to the reason why this was not processed."                                                                                   	|	IntegrationError_UndefinedError                  	|	"Re-enter transaction"                          	|
//	|	IntegrationErrorMap_IntegrationErrorMapNoAction                       	|	"The customer’s card issuer has indicated there is a problem with the credit card number. The customer should use an alternate credit card, or contact their bank."                                                                                                                        	|	IntegrationError_TransactionDeclinedByIssuer     	|	"No action taken (no match)"                    	|
//	|	IntegrationErrorMap_IntegrationErrorMapFormatError                    	|	"The customer’s card issuer does not recognise the transaction details being entered. This is due to a format error. The customer should check the transaction information and try processing the transaction again."                                                                      	|	IntegrationError_TransactionDeclinedByIssuer     	|	"Format error"                                  	|
//	|	IntegrationErrorMap_IntegrationErrorMapIssuerSignOff                  	|	"The customer’s card issuer has declined the transaction as it does not allow transactions originating through mail/telephone, fax, email or Internet orders."                                                                                                                             	|	IntegrationError_TransactionDeclinedByIssuer     	|	"Issuer sign-off"                               	|
//	|	IntegrationErrorMap_IntegrationErrorMapCompletedPartially             	|	""                                                                                                                                                                                                                                                                                         	|	IntegrationError_None                            	|	"Completed partially"                           	|
//	|	IntegrationErrorMap_IntegrationErrorMapNoCardRecord                   	|	"The customer’s card issuer has declined the transaction as the credit card number does not exist."                                                                                                                                                                                        	|	IntegrationError_IncorrectCardNumber             	|	"No card record"                                	|
//	|	IntegrationErrorMap_IntegrationErrorMapTransactionNotPermitted        	|	"The customer’s card issuer has declined the transaction as this credit card cannot be used for this type of transaction. This may be associated with a test credit card number."                                                                                                          	|	IntegrationError_InvalidCard                     	|	"Transaction not permitted to card"             	|
//	|	IntegrationErrorMap_IntegrationErrorMapAmountIncorrect                	|	"The customer’s card issuer has declined the transaction due to the amount attempting to be processed."                                                                                                                                                                                    	|	IntegrationError_TransactionDeclinedByIssuer     	|	"Original amount incorrect"                     	|
//	|	IntegrationErrorMap_IntegrationErrorMapAlreadyReversed                	|	""                                                                                                                                                                                                                                                                                         	|	IntegrationError_UndefinedError                  	|	"Already reversed (by Switch)"                  	|
//	|	IntegrationErrorMap_IntegrationErrorMapNoRoutingPath                  	|	"The customer’s card issuer cannot be found for routing. This response code is often returned when the customer is using a test credit card number."                                                                                                                                       	|	IntegrationError_TransactionDeclinedByIssuer     	|	"No routing path"                               	|
//	|	IntegrationErrorMap_IntegrationErrorMapSystemMalfunction              	|	"The customer’s card issuer was not able to process the transaction. The customer should attempt to process this transaction again."                                                                                                                                                       	|	IntegrationError_TransactionDeclinedByIssuer     	|	"System malfunction"                            	|
//	|	IntegrationErrorMap_IntegrationErrorMapSoftDecline                    	|	""                                                                                                                                                                                                                                                                                         	|	IntegrationError_UndefinedError                  	|	"Soft-Decline"                                  	|
//	|	IntegrationErrorMap_IntegrationErrorMapReferToIssuer                  	|	"The customer’s card issuer has indicated there is a problem with the credentials used in the transaction. The customer should use an alternate credit card, or contact their bank."                                                                                                       	|	IntegrationError_InvalidCard                     	|	"Refer to issuer"                               	|
//	|	IntegrationErrorMap_IntegrationErrorMapReferToIssuerSpecial           	|	"The customer’s card issuer has indicated there is a problem with the card number. The customer should use an alternate credit card, or contact their bank."                                                                                                                               	|	IntegrationError_InvalidCard                     	|	"Refer to issuer (special)"                     	|
//	|	IntegrationErrorMap_IntegrationErrorMapError                          	|	"The customer’s card issuer has declined the transaction as there is a problem with the card number. The customer should contact their card issuer and/or use an alternate card."                                                                                                          	|	IntegrationError_UndefinedError                  	|	"Error"                                         	|
//	|	IntegrationErrorMap_IntegrationErrorMapApprovedPartialAmount          	|	"The transaction was successful."                                                                                                                                                                                                                                                          	|	IntegrationError_None                            	|	"Approved for partial amount"                   	|
//	|	IntegrationErrorMap_IntegrationErrorMapNoSuchIssuer                   	|	"The customer’s card issuer does not exist. Check the card information and try processing the transaction again."                                                                                                                                                                          	|	IntegrationError_UnavailableIssuer               	|	"No such issuer"                                	|
//	|	IntegrationErrorMap_IntegrationErrorMapCustomerCancellation           	|	""                                                                                                                                                                                                                                                                                         	|	IntegrationError_UndefinedError                  	|	"Customer cancellation"                         	|
//	|	IntegrationErrorMap_IntegrationErrorMapCustomerDispute                	|	""                                                                                                                                                                                                                                                                                         	|	IntegrationError_UndefinedError                  	|	"Customer dispute"                              	|
//	|	IntegrationErrorMap_IntegrationErrorMapNoInvestmentAccount            	|	"The customer’s card issuer has declined the transaction as the account type selected is not valid for this credit card number."                                                                                                                                                           	|	IntegrationError_InvalidCard                     	|	"No investment account"                         	|
//	|	IntegrationErrorMap_IntegrationErrorMapCardAcceptorCallAcquirer       	|	"The customer should use an alternative credit card. Neither eWAY nor the bank can provide more details."                                                                                                                                                                                  	|	IntegrationError_InvalidCard                     	|	"Card acceptor call acquirer"                   	|
//	|	IntegrationErrorMap_IntegrationErrorMapUnableToVerifyPIN              	|	""                                                                                                                                                                                                                                                                                         	|	IntegrationError_IncorrectCVVCVC                 	|	"Unable to verify PIN"                          	|
//	|	IntegrationErrorMap_IntegrationErrorMapAuthenticationFailure          	|	""                                                                                                                                                                                                                                                                                         	|	IntegrationError_UndefinedError                  	|	"Authentication failure"                        	|
//	|	IntegrationErrorMap_IntegrationErrorMapIssuerSwitchInoperative        	|	"The customer’s card issuer is unable to be contacted to authorise the transaction. The customer should attempt to process this transaction again."                                                                                                                                        	|	IntegrationError_TransactionDeclinedByIssuer     	|	"Issuer or switch inoperative"                  	|
//	|	IntegrationErrorMap_IntegrationErrorMapInvalidResponse                	|	""                                                                                                                                                                                                                                                                                         	|	IntegrationError_UndefinedError                  	|	"Invalid response"                              	|
//	|	IntegrationErrorMap_IntegrationErrorMapCardAcceptorContactAcquirer    	|	"The customer’s card issuer has declined the transaction and requested that your customer’s card be retained as the card was reported as lost or stolen."                                                                                                                                  	|	IntegrationError_LostCard                        	|	"Card acceptor contact acquirer"                	|
//	|	IntegrationErrorMap_IntegrationErrorMapFunctionNotSupported           	|	"The customer’s card issuer has declined the transaction as it does not allow this type of transaction."                                                                                                                                                                                   	|	IntegrationError_InvalidCard                     	|	"Function not supported"                        	|
//	|	IntegrationErrorMap_IntegrationErrorMapNoCheckingAccount              	|	"The customer’s card issuer has declined the transaction as the credit card number is associated to a cheque account that does not exist."                                                                                                                                                 	|	IntegrationError_InvalidCard                     	|	"No checking account"                           	|
//	|	IntegrationErrorMap_IntegrationErrorMapPreviousMessageNotFound        	|	""                                                                                                                                                                                                                                                                                         	|	IntegrationError_UndefinedError                  	|	"Previous message not found"                    	|
//	|	IntegrationErrorMap_IntegrationErrorMapCryptographicErrorInPIN        	|	""                                                                                                                                                                                                                                                                                         	|	IntegrationError_IncorrectCVVCVC                 	|	"Cryptographic error in PIN"                    	|
//	|	IntegrationErrorMap_IntegrationErrorMapPINValidationNotPossible       	|	""                                                                                                                                                                                                                                                                                         	|	IntegrationError_UndefinedError                  	|	"PIN validation not possible"                   	|
//	|	IntegrationErrorMap_IntegrationErrorMapCutoffInProcess                	|	"The customer’s card issuer is temporarily not able to process this customer’s credit card."                                                                                                                                                                                               	|	IntegrationError_TransactionDeclinedByIssuer     	|	"Cutoff is in process"                          	|
//	|	IntegrationErrorMap_IntegrationErrorMapSurchargeNotSupported          	|	""                                                                                                                                                                                                                                                                                         	|	IntegrationError_UndefinedError                  	|	"Surcharge amount not supported by deb"         	|
//	|	IntegrationErrorMap_IntegrationErrorMapForceSTIP                      	|	""                                                                                                                                                                                                                                                                                         	|	IntegrationError_UndefinedError                  	|	"Force STIP"                                    	|
//	|	IntegrationErrorMap_IntegrationErrorMapExpiredCard                    	|	"The customer’s card issuer has declined the transaction as Card has expired or the date is incorrect. Check the expiry date in the transaction and try processing the transaction again."                                                                                                 	|	IntegrationError_CardHasExpired                  	|	"Expired card"                                  	|
//	|	IntegrationErrorMap_IntegrationErrorMapSuspectedFraud                 	|	"The customer’s card issuer has declined the transaction as there is a suspected fraud on this credit card number."                                                                                                                                                                        	|	IntegrationError_SuspiciousClient                	|	"Suspected fraud"                               	|
//	|	IntegrationErrorMap_IntegrationErrorMapNoCreditAccount                	|	"The customer’s card issuer has declined the transaction as the Credit Card number used is not a credit account."                                                                                                                                                                          	|	IntegrationError_InvalidCard                     	|	"No credit account"                             	|
//	|	IntegrationErrorMap_IntegrationErrorMapNoUniversalAccount             	|	"The customer’s card issuer has declined the transaction as the account type selected is not valid for this credit card number."                                                                                                                                                           	|	IntegrationError_InvalidCard                     	|	"No universal account"                          	|
//	|	IntegrationErrorMap_IntegrationErrorMapExpiredCardAlternate           	|	"The customer’s card is expired. Contact your customer and confirm that the correct dates were entered and that there were no mistakes (e.g. 05/21 rather than 05/20)."                                                                                                                    	|	IntegrationError_CardHasExpired                  	|	"Expired card"                                  	|
//	|	IntegrationErrorMap_IntegrationErrorMapSuspectedFraudAlternate        	|	"The customer’s card issuer has declined this transaction as the credit card appears to be fraudulent. While you could contact this customer yourself, it's very possible that this transaction is fraudulent."                                                                            	|	IntegrationError_SuspiciousClient                	|	"Suspected fraud"                               	|
//	|	IntegrationErrorMap_IntegrationErrorMapCardAcceptorWriteToAcquirer    	|	"The customer’s card issuer has declined the transaction. The customer should contact their bank and retry the transaction."                                                                                                                                                               	|	IntegrationError_UndefinedError                  	|	"Card acceptor contact acquirer"                	|
//	|	IntegrationErrorMap_IntegrationErrorMapSecurityViolation              	|	"The customer’s card issuer has declined the transaction. The customer should use an alternate credit card, and contact their bank."                                                                                                                                                       	|	IntegrationError_TransactionDeclinedByIssuer     	|	"Security violation"                            	|
//	|	IntegrationErrorMap_IntegrationErrorMapCryptographicFailure           	|	""                                                                                                                                                                                                                                                                                         	|	IntegrationError_UndefinedError                  	|	"Cryptographic failure"                         	|
//	|	IntegrationErrorMap_IntegrationErrorMapViolationOfLaw                 	|	"The customer’s card issuer has declined the transaction and request the customer to contact their bank."                                                                                                                                                                                  	|	IntegrationError_TransactionDeclinedByIssuer     	|	"Violation of law"                              	|
//	|	IntegrationErrorMap_IntegrationErrorMapFraud                          	|	""                                                                                                                                                                                                                                                                                         	|	IntegrationError_SuspiciousClient                	|	"Fraud"                                         	|
//	|	IntegrationErrorMap_IntegrationErrorMapCashRequestExceedsLimit        	|	""                                                                                                                                                                                                                                                                                         	|	IntegrationError_UndefinedError                  	|	"Cash request exceeds issuer or approv"         	|
//	|	IntegrationErrorMap_IntegrationErrorMapIneligibleForResubmission      	|	""                                                                                                                                                                                                                                                                                         	|	IntegrationError_UndefinedError                  	|	"Ineligible for resubmission"                   	|
//	|	IntegrationErrorMap_IntegrationErrorMapStopPaymentOrder               	|	""                                                                                                                                                                                                                                                                                         	|	IntegrationError_UndefinedError                  	|	"Stop Payment Order"                            	|
//	|	IntegrationErrorMap_IntegrationErrorMapHonorWithIdentification        	|	"Transaction processed successfully - identification NOT required. This code is returned by some banks in place of 00 response."                                                                                                                                                           	|	IntegrationError_None                            	|	"Honor with identification"                     	|
//	|	IntegrationErrorMap_IntegrationErrorMapUnacceptableTransactionFee     	|	"An unspecified bank error has occurred. No further information is available from eWAY or the bank. The customer should attempt to process the transaction again."                                                                                                                         	|	IntegrationError_UndefinedError                  	|	"Unacceptable transaction fee"                  	|
//	|	IntegrationErrorMap_IntegrationErrorMapDuplicateFileUpdateRecord      	|	""                                                                                                                                                                                                                                                                                         	|	IntegrationError_UndefinedError                  	|	"Duplicate file update record"                  	|
//	|	IntegrationErrorMap_IntegrationErrorMapActivityCountExceeded          	|	"The customer’s card issuer has declined the transaction as the customer has exceeded the withdrawal frequency limit."                                                                                                                                                                     	|	IntegrationError_ExceedsTransactionFrequencyLimit	|	"Activity count exceeded"                       	|
//	|	IntegrationErrorMap_IntegrationErrorMapCardPickUpAtATM                	|	"The customer’s card issuer has declined the transaction as the card is suspected to be a counterfeit. The customer’s card issuer has requested that your customer’s credit card be retained by you."                                                                                      	|	IntegrationError_LostCard                        	|	"Card pick up at ATM"                           	|
//	|	IntegrationErrorMap_IntegrationErrorMapDataMismatchOriginalMessage    	|	""                                                                                                                                                                                                                                                                                         	|	IntegrationError_UndefinedError                  	|	"Data does not match original message"          	|
//	|	IntegrationErrorMap_IntegrationErrorMapInvalidAuthorizationLifeCycle  	|	""                                                                                                                                                                                                                                                                                         	|	IntegrationError_UndefinedError                  	|	"Invalid authorization life cycle"              	|
//	|	IntegrationErrorMap_IntegrationErrorMapDuplicateTransmission          	|	"The customer’s card issuer has declined the transaction as this transaction appears to be a duplicate transmission."                                                                                                                                                                      	|	IntegrationError_TransactionDeclinedByIssuer     	|	"Duplicate transmission"                        	|
//	|	IntegrationErrorMap_IntegrationErrorMapDeclineForCVV2Failure          	|	""                                                                                                                                                                                                                                                                                         	|	IntegrationError_IncorrectCVVCVC                 	|	"Decline for CVV2 failure"                      	|
//	|	IntegrationErrorMap_IntegrationErrorMapCardAuthenticationFailed       	|	""                                                                                                                                                                                                                                                                                         	|	IntegrationError_InvalidCard                     	|	"Card Authentication failed"                    	|
//	|	IntegrationErrorMap_IntegrationErrorMapPickUpCardSpecial              	|	""                                                                                                                                                                                                                                                                                         	|	IntegrationError_LostCard                        	|	"Pick up card (special)"                        	|
//	|	IntegrationErrorMap_IntegrationErrorMapRequestInProgress              	|	"The card issuer has indicated there is a problem with the card number. The customer should contact their bank and/or use an alternate credit card."                                                                                                                                       	|	IntegrationError_TransactionDeclinedByIssuer     	|	"Request in progress"                           	|
//	|	IntegrationErrorMap_IntegrationErrorMapApprovedUpdateTrack3           	|	""                                                                                                                                                                                                                                                                                         	|	IntegrationError_None                            	|	"Approved update track 3"                       	|
//	|	IntegrationErrorMap_IntegrationErrorMapSuspectedMalfunction           	|	"The customer’s card issuer could not be contacted during the transaction. The customer should check the card information and try processing the transaction again."                                                                                                                       	|	IntegrationError_TransactionDeclinedByIssuer     	|	"Suspected malfunction"                         	|
//	|	IntegrationErrorMap_IntegrationErrorMapRestrictedCard                 	|	"The customer’s card issuer has declined the transaction and requested that your customer’s card be retained."                                                                                                                                                                             	|	IntegrationError_TransactionDeclinedByIssuer     	|	"Restricted card"                               	|
//	|	IntegrationErrorMap_IntegrationErrorMapPickUpCardStolenCard           	|	"The customer’s card has been reported as stolen. While you could contact this customer yourself, it's very possible that this transaction is fraudulent."                                                                                                                                 	|	IntegrationError_StolenCard                      	|	"Pick up card (stolen card)"                    	|
//	|	IntegrationErrorMap_IntegrationErrorMapExceedsWithdrawalLimit         	|	"The customer’s card issuer has declined the transaction as it will exceed the customer’s card limit."                                                                                                                                                                                     	|	IntegrationError_ExceedsAmountLimit              	|	"Exceeds withdrawal limit"                      	|
//	|	IntegrationErrorMap_IntegrationErrorMapRestrictedCardAlternate        	|	"The customer’s card issuer has declined the transaction as the credit card has some restrictions."                                                                                                                                                                                        	|	IntegrationError_InvalidCard                     	|	"Restricted card"                               	|
//	|	IntegrationErrorMap_IntegrationErrorMapResponseReceivedTooLate        	|	""                                                                                                                                                                                                                                                                                         	|	IntegrationError_UndefinedError                  	|	"Response received too late"                    	|
//	|	IntegrationErrorMap_IntegrationErrorMapTooManyWrongPINTries           	|	"The customer’s card issuer has declined the transaction as the customer has entered the incorrect PIN more than three times."                                                                                                                                                             	|	IntegrationError_BlockedCard                     	|	"Too many wrong PIN tries"                      	|
//	|	IntegrationErrorMap_IntegrationErrorMapIncorrectCVV                   	|	"The customer’s card issuer has declined the transaction as the CVV is incorrect. The customer should check the CVV details (the 3 numbers on the back for Visa/MC, or 4 numbers on the front for AMEX) and try again."                                                                    	|	IntegrationError_IncorrectCVVCVC                 	|	"Incorrect CVV"                                 	|
//	|	IntegrationErrorMap_IntegrationErrorMapNoReasonToDecline              	|	""                                                                                                                                                                                                                                                                                         	|	IntegrationError_UndefinedError                  	|	"No reason to decline"                          	|
//	|	IntegrationErrorMap_IntegrationErrorMapDeclinedByGateway              	|	""                                                                                                                                                                                                                                                                                         	|	IntegrationError_UndefinedError                  	|	"Declined by gateway"                           	|
//	|	IntegrationErrorMap_IntegrationErrorMapTransactionAmountExceedsPreAuth	|	""                                                                                                                                                                                                                                                                                         	|	IntegrationError_ExceedsAmountLimit              	|	"Transaction amount exceeds preauthori"         	|
//	|	IntegrationErrorMap_IntegrationErrorMapApproved                       	|	"Transaction has been processed successfully. No further action is required from you as the merchant."                                                                                                                                                                                     	|	IntegrationError_None                            	|	"Approved"                                      	|
//	|	IntegrationErrorMap_IntegrationErrorMapPickUpCard                     	|	"The customer’s card issuer has declined the transaction and requested that the card be retained as the card may have been reported as lost or stolen."                                                                                                                                    	|	IntegrationError_LostCard                        	|	"Pick up card"                                  	|
//	|	IntegrationErrorMap_IntegrationErrorMapDoNotHonor                     	|	"The '05 Do Not Honour' error is a generic bank response code that has several possible causes. However, it generally indicates a card error rather than an error with your merchant facility. The '05' error indicates your bank declining the customer's card for an unspecified reason."	|	IntegrationError_InvalidCard                     	|	"Do not honor"                                  	|
//	|	IntegrationErrorMap_IntegrationErrorMapUnableToLocateRecord           	|	"The customer’s card issuer does not recognise the credit card details. The customer should check the card information and try processing the transaction again."                                                                                                                          	|	IntegrationError_TransactionDeclinedByIssuer     	|	"Unable to locate record"                       	|
//	|	IntegrationErrorMap_IntegrationErrorMapFileUpdateFieldEditError       	|	""                                                                                                                                                                                                                                                                                         	|	IntegrationError_UndefinedError                  	|	"File update field edit error"                  	|
//	|	IntegrationErrorMap_IntegrationErrorMapFileTemporarilyUnavailable     	|	""                                                                                                                                                                                                                                                                                         	|	IntegrationError_UndefinedError                  	|	"File temporarily unavailable"                  	|
//	|	IntegrationErrorMap_IntegrationErrorMapCardAcceptor                   	|	"The customer’s card issuer has declined the transaction and requested that your customer’s card be retained."                                                                                                                                                                             	|	IntegrationError_InvalidCard                     	|	"Card acceptor call acquirer"                   	|
//	|	IntegrationErrorMap_IntegrationErrorMapAllowablePINTriesExceeded      	|	"The customer’s card issuer has declined the transaction as the customer has entered the incorrect PIN three times."                                                                                                                                                                       	|	IntegrationError_BlockedCard                     	|	"Allowable PIN tries exceeded"                  	|
//	|	IntegrationErrorMap_IntegrationErrorMapPickUpCardLostCard             	|	"The customer’s card issuer has declined the transaction as the card has been reported lost."                                                                                                                                                                                              	|	IntegrationError_LostCard                        	|	"Pick up card (lost card)"                      	|
//	|	IntegrationErrorMap_IntegrationErrorMapNoSavingsAccount               	|	"The customer’s card issuer has declined the transaction as the credit card number is associated to a savings account that does not exist."                                                                                                                                                	|	IntegrationError_InvalidCard                     	|	"No savings account"                            	|
//	|	IntegrationErrorMap_IntegrationErrorMapIncorrectPIN                   	|	"The customer’s card issuer has declined the transaction as the customer has entered an incorrect PIN. The customer should re-enter their PIN."                                                                                                                                            	|	IntegrationError_IncorrectCVVCVC                 	|	"Incorrect PIN"                                 	|
//	|	IntegrationErrorMap_IntegrationErrorMapCashServiceNotAvailable        	|	""                                                                                                                                                                                                                                                                                         	|	IntegrationError_UndefinedError                  	|	"Cash service not available"                    	|
//	|	IntegrationErrorMap_IntegrationErrorMapForwardIssuer                  	|	""                                                                                                                                                                                                                                                                                         	|	IntegrationError_UndefinedError                  	|	"Forward to issuer"                             	|
//	|	IntegrationErrorMap_IntegrationErrorMapCGICheckFailed                 	|	"Запрос не прошел CGI-проверку"                                                                                                                                                                                                                                                            	|	IntegrationError_ThreeDSAuthFailed               	|	"CGI check failed"                              	|
//	|	IntegrationErrorMap_IntegrationErrorMapInvalidAmountField             	|	"Ошибка в поле 'Amount' запроса"                                                                                                                                                                                                                                                           	|	IntegrationError_ExceedsAmountLimit              	|	"Invalid 'Amount' field"                        	|
//	|	IntegrationErrorMap_IntegrationErrorMapInvalidCurrencyField           	|	"Ошибка в поле 'Currency' запроса"                                                                                                                                                                                                                                                         	|	IntegrationError_UndefinedError                  	|	"Invalid 'Currency' field"                      	|
//	|	IntegrationErrorMap_IntegrationErrorMapHostNotResponding              	|	"Хост эквайера (TS) не отвечает либо неверный формат файла шаблона ответа модуля eGateway"                                                                                                                                                                                                 	|	IntegrationError_UnavailableIssuer               	|	"Host not responding or invalid template format"	|
//	|	IntegrationErrorMap_IntegrationErrorMapNoConnectionWithPINPad         	|	"Нет соединения с PIN-клавиатурой интернет-терминала"                                                                                                                                                                                                                                      	|	IntegrationError_UndefinedError                  	|	"No connection with PIN-pad"                    	|
//	|	IntegrationErrorMap_IntegrationErrorMapConnectionErrorDuringProcessing	|	"Ошибка соединения с хостом эквайера (TS) во время обработки транзакции"                                                                                                                                                                                                                   	|	IntegrationError_UnavailableAcquirer             	|	"Connection error during transaction processing"	|
//	|	IntegrationErrorMap_IntegrationErrorMapMissingMandatoryField          	|	"В запросе не заполнено обязательное поле"                                                                                                                                                                                                                                                 	|	IntegrationError_IncorrectCardNumber             	|	"Missing mandatory field"                       	|
//	|	IntegrationErrorMap_IntegrationErrorMapNoConnectionWithHost           	|	"Нет соединения с хостом эквайера (TS)"                                                                                                                                                                                                                                                    	|	IntegrationError_UnavailableAcquirer             	|	"No connection with the host"                   	|
//	|	IntegrationErrorMap_IntegrationErrorMapInvalidMerchantIDField         	|	"Ошибка в поле 'Merchant ID' запроса"                                                                                                                                                                                                                                                      	|	IntegrationError_NonExistentCard                 	|	"Invalid 'Merchant ID' field"                   	|
//	|	IntegrationErrorMap_IntegrationErrorMapInvalidRRNField                	|	"Ошибка в поле 'RRN' запроса"                                                                                                                                                                                                                                                              	|	IntegrationError_UndefinedError                  	|	"Invalid 'RRN' field"                           	|
//	|	IntegrationErrorMap_IntegrationErrorMapAccessDenied                   	|	"Терминалу отказано в доступе к модулю e-Gateway"                                                                                                                                                                                                                                          	|	IntegrationError_BlockedCard                     	|	"Access denied"                                 	|
//	|	IntegrationErrorMap_IntegrationErrorMapTerminalBusy                   	|	"На терминале выполняется другая транзакция"                                                                                                                                                                                                                                               	|	IntegrationError_ExceedsTransactionFrequencyLimit	|	"Terminal busy"                                 	|
//	|	IntegrationErrorMap_IntegrationErrorMapIPAddressMismatch              	|	"IP-адрес источника транзакции не соответствует ожидаемому"                                                                                                                                                                                                                                	|	IntegrationError_SuspiciousClient                	|	"IP address mismatch"                           	|
//	|	IntegrationErrorMap_IntegrationErrorMapAuthenticationError            	|	"Ошибка в запросе на аутентификационную информацию либо аутентификация неуспешна"                                                                                                                                                                                                          	|	IntegrationError_ThreeDSAuthFailed               	|	"Authentication error"                          	|
//	|	IntegrationErrorMap_IntegrationErrorMapEGatewaySetupError             	|	"Ошибка настройки модуля e-Gateway"                                                                                                                                                                                                                                                        	|	IntegrationError_UndefinedError                  	|	"E-Gateway module setup error"                  	|
//	|	IntegrationErrorMap_IntegrationErrorMapIncorrectHostResponse          	|	"Некорректный ответ хоста эквайера (TS), например, отсутствуют обязательные поля"                                                                                                                                                                                                          	|	IntegrationError_TransactionDeclinedByAcquirer   	|	"Incorrect host response"                       	|
//	|	IntegrationErrorMap_IntegrationErrorMapInvalidCardNumberField         	|	"Ошибка в поле 'Card number' запроса"                                                                                                                                                                                                                                                      	|	IntegrationError_IncorrectCardNumber             	|	"Invalid 'Card number' field"                   	|
//	|	IntegrationErrorMap_IntegrationErrorMapInvalidCardExpirationDateField 	|	"Ошибка в поле 'Card expiration date' запроса"                                                                                                                                                                                                                                             	|	IntegrationError_IncorrectCardExpDate            	|	"Invalid 'Card expiration date' field"          	|
//	|	IntegrationErrorMap_IntegrationErrorMapInvalidCVC2Field               	|	"Ошибка в поле 'CVC2' или 'CVC2 Description' запроса"                                                                                                                                                                                                                                      	|	IntegrationError_IncorrectCVVCVC                 	|	"Invalid 'CVC2' field"                          	|
//	|	IntegrationErrorMap_IntegrationErrorMapTimeExceeded                   	|	"Превышен допустимый временной интервал между запросом и временем модуля e-Gateway"                                                                                                                                                                                                        	|	IntegrationError_UndefinedError                  	|	"Time exceeded"                                 	|

var SliceIntegrationErrorMapRefs *sliceIntegrationErrorMapRefs

type sliceIntegrationErrorMapRefs struct{}

func (*sliceIntegrationErrorMapRefs) Description(slice ...IntegrationErrorMap) []string {
	var result []string
	for _, val := range slice {
		result = append(result, val.Description())
	}

	return result
}

func (*sliceIntegrationErrorMapRefs) IntegrationErrorId(slice ...IntegrationErrorMap) []IntegrationError {
	var result []IntegrationError
	for _, val := range slice {
		result = append(result, val.IntegrationErrorId())
	}

	return result
}

func (*sliceIntegrationErrorMapRefs) IsoMessage(slice ...IntegrationErrorMap) []string {
	var result []string
	for _, val := range slice {
		result = append(result, val.IsoMessage())
	}

	return result
}
