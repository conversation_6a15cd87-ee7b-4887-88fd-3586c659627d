package service

import (
	"context"

	goevents "git.local/sensitive/innerpb/processing/events"
	gorpc "git.local/sensitive/innerpb/processing/grpc"
	"git.local/sensitive/pkg/encryptor"
	"git.local/sensitive/pkg/middlewares"
	"git.local/sensitive/processing/card/model"
	"git.local/sensitive/processing/card/repository"
	"git.local/sensitive/processing/card/schema"
	"git.local/sensitive/processing/card/schema/convertor"
)

type Services struct {
	CardGetter                CardGetter
	CardSetter                CardSetter
	CardEncryptManager        CardEncryptManager
	KeyRotator                KeyRotator
	Client                    Clientor
	Token                     Tokener
	Authorizer                Authorizer
	OneClickCard              OneClickCarder
	ClientVerificationManager ClientVerificationManager
	ProjectMaskFormat         ProjectMaskFormatter
	HashKeyRotator            HashKeyRotator
}

func NewServices(
	repo *repository.Repositories,
	privateKey string,
	terminalCard gorpc.TerminalCardsClient,
	terminalClient gorpc.TerminalClient,
	merchantClient gorpc.MerchantClient,
	acquirerClient gorpc.AcquirerClient,
	symmetricKey string,
	hashKeySecret string,
	encryptor encryptor.Encryptor,
	decryptor encryptor.Decryptor,
) *Services {
	cardGetterService := NewCardGetterService(
		repo.CardsDB,
		repo.ClientsDB,
		repo.KeyDB,
		repo.ProjectMaskFormat,
		acquirerClient,
		decryptor,
	)
	clientService := NewClientService(
		repo.CardsDB,
		repo.ClientsDB,
		repo.CardGetterDB,
		repo.ProjectMaskFormat,
		repo.KeyDB,
		repo.CardUpdatorDB,
		repo.CardValidityDB,
		merchantClient,
		terminalCard,
		decryptor,
		repo.HashKey,
		hashKeySecret,
	)
	tokenService := NewTokenService(
		repo.CardTokenerDB,
		repo.TokenUpdatorDB,
		repo.CardsDB,
		repo.CardUpdatorDB,
		repo.ClientsDB,
	)
	authService := NewAuthService(
		repo,
		merchantClient,
	)
	cardValidatorService := NewKeyRotationService(
		repo.KeyDB,
		repo.BulkCardUpdaterDB,
	)
	oneClickCardService := NewOneClickCardService(
		repo.ClientsDB,
		repo.CardGetterDB,
		repo.ProjectMaskFormat,
		repo.KeyDB,
		terminalClient,
		acquirerClient,
	)
	clientVerificationService := NewClientVerificationManagerService(
		repo.ClientVerificationManager,
		merchantClient,
	)
	cardSetterService := NewCardSetterService(
		repo.ClientsDB,
		repo.CardUpdatorDB,
		repo.CardsDB,
		repo.KeyDB,
		decryptor,
		merchantClient,
		symmetricKey,
	)
	cardEncryptManagerService := NewCardEncryptManagerService(
		repo, privateKey, encryptor,
	)
	projectMaskFormatService := NewProjectMaskFormatService(
		repo.ProjectMaskFormat,
		terminalCard,
		decryptor,
	)
	hashKeyRotationService := NewHashKeyRotationService(
		repo.KeyDB,
		repo.HashKey,
		repo.BulkCardUpdaterDB,
		hashKeySecret,
		symmetricKey,
	)

	return &Services{
		CardGetter:                cardGetterService,
		KeyRotator:                cardValidatorService,
		Client:                    clientService,
		Token:                     tokenService,
		OneClickCard:              oneClickCardService,
		Authorizer:                authService,
		ClientVerificationManager: clientVerificationService,
		CardSetter:                cardSetterService,
		CardEncryptManager:        cardEncryptManagerService,
		ProjectMaskFormat:         projectMaskFormatService,
		HashKeyRotator:            hashKeyRotationService,
	}
}

//go:generate go run github.com/vektra/mockery/v2 --name=Carder
type CardGetter interface {
	GetCardToken(ctx context.Context, cardID uint64) (schema.EncryptedCardToken, error)
	GetClientCards(
		ctx context.Context,
		pagination *middlewares.PaginationInfo,
		projectClientId string,
		projectId uint64,
	) (card []*model.Card, err error)
	GetCardTokens(ctx context.Context, request *schema.CardTokenRequest) (*model.Card, error)
	GetByEncryptedId(ctx context.Context, request schema.PanByHashedIdRequest) (model.Card, error)
	GetById(ctx context.Context, request schema.PanByCardIdRequest) (_ model.Card, err error)
	GetCardByPan(ctx context.Context, projectID uint64, projectClientID string, pan []byte) (*model.Card, error)
}

// CardSetter describes the methods that modifies card information that was migrated/built incorrectly.
type CardSetter interface {
	ModifyCardPan(ctx context.Context, panInfo *goevents.ModifyPan) error
	DeactivateCard(ctx context.Context, merchantID uint64, clientID uint64, cardID uint64) error
	// CheckIsExpired - метод проходится по всем картам, у которых is_expired равен false,
	// чтобы найти просроченные карты и обновить у них параметр is_expired
	CheckIsExpired(ctx context.Context) error
}

type KeyRotator interface {
	CreateNewKey(ctx context.Context) (err error)
	RotateKeyInCards(ctx context.Context) (err error)
}

type OneClickCarder interface {
	GetOneClickPayInCards(
		ctx context.Context,
		v1 *gorpc.GetOneClickPayInCardsRequestV1,
	) ([]*schema.EncryptedCardToken, error)
	//GetOneClickPayOutCards получение всех карт пользователя для рекуррентного вывода
	//(карта с save_access=true, approved=true)
	GetOneClickPayOutCards(
		ctx context.Context,
		v1 *gorpc.GetOneClickPayOutCardsRequestV1,
	) (map[string]string, error)
}

type Clientor interface {
	CreateClient(ctx context.Context, clientCard *schema.CreateClientRequest) (*model.Card, error)
	GetProjectClients(
		ctx context.Context,
		pagination *middlewares.PaginationInfo,
		projectId uint64,
	) (client []*model.Client, err error)
	GetClients(
		ctx context.Context,
		merchantID uint64,
		pagination *middlewares.PaginationInfo,
		request schema.ClientRequest,
	) ([]schema.ClientResponse, error)
	GetClientsByFilter(
		ctx context.Context,
		request schema.ClientRequest,
	) ([]model.Client, error)
	GetCardPans(
		ctx context.Context,
		clientID uint64,
		merchantID uint64,
		pagination *middlewares.PaginationInfo,
	) ([]schema.MaskedPanInfo, error)
	GetByProjectClientInfo(
		ctx context.Context,
		request []schema.ClientRequest,
	) ([]model.Client, error)
}

type Tokener interface {
	Create(ctx context.Context, tokenReq schema.TokenRequest) error
	Update(ctx context.Context, tokenId uint64, tokenRequest *goevents.SaveToken) error
	GetByCardAndAcquirer(ctx context.Context, cardId uint64, acquirerId uint64) (model.Token, error)
	SaveToken(ctx context.Context, tokenRequest *goevents.SaveToken) error
	DeactivateTokenByEncryptedCardId(ctx context.Context, encryptedCardID string, projectID uint64) (err error)
}

type Authorizer interface {
	ProjectAuth(ctx context.Context, request convertor.Base64Convertor, merchantID, projectID uint64, sign string) error
	CheckProjectAuthV2(ctx context.Context, request schema.DeactivateTokenRequest, sign string) error
}

type ClientVerificationManager interface {
	BindVerificationUserIDToClient(
		ctx context.Context,
		projectID uint64,
		projectClientID string,
		verificationUserID uint64,
	) error
	UnbindVerificationUserIDFromClient(
		ctx context.Context,
		projectID uint64,
		projectClientID string,
	) error
	GetClientByVerificationID(
		ctx context.Context,
		verificationID uint64,
	) (_ []schema.GetClientByVerificationResponse, err error)
	BlockClient(
		ctx context.Context,
		projectID uint64,
		projectClientID string,
	) error
	GetClientActiveness(
		ctx context.Context,
		projectID uint64,
		projectClientID string,
	) (bool, error)
}

type CardEncryptManager interface {
	DecryptPayInCard(ctx context.Context, encryptedCard []byte) (encryptor.EncryptedCard, error)
	DecryptPayOutCard(ctx context.Context, encryptedCard []byte) (encryptor.EncryptedCard, error)
}

type ProjectMaskFormatter interface {
	// GetByProjectID - функция ищет формат маски карты проекта, если не находит,
	// то возвращает дефолтный формат маскирования (1234-12XXXXXX-1234)
	GetByProjectID(ctx context.Context, projectID uint64) (*model.ProjectMaskFormat, error)
	GetPanInfoByProjectId(
		ctx context.Context,
		v1 *gorpc.GetPanInfoByProjectIdRequest,
	) (*gorpc.GetPanInfoByProjectIdResponse, error)
	Create(ctx context.Context, request *model.ProjectMaskFormat) error
	UpdateByProjectId(
		ctx context.Context,
		projectID uint64,
		placeholderSign string,
		withSeparator *bool,
	) error
}

type HashKeyRotator interface {
	CreateNewHashKey(ctx context.Context) (err error)
	RotateHashKeyInCards(ctx context.Context) (err error)
}
