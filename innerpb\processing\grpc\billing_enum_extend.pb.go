// Code generated by protoc-gen-go-mvp. DO NOT EDIT.
// versions:
// - protoc-gen-go-mvp v2.0.1
// created by mvp authors

package grpc

import (
	math "math"
	strings "strings"
)

func (val BillingOperationBalanceType) Synonym() BillingOperationBalanceType {
	if _, ok := BillingOperationBalanceType_name[int32(val)]; ok {
		return val
	}

	return BillingOperationBalanceType(math.MinInt32)
}

func (val BillingOperationBalanceType) Int() int {
	return int(val.Synonym())
}

func (val BillingOperationBalanceType) IntPtr() *int {
	result := int(val.Synonym())
	return &result
}

func (val BillingOperationBalanceType) Int32() int32 {
	return int32(val.Synonym())
}

func (val BillingOperationBalanceType) Int32Ptr() *int32 {
	result := int32(val.Synonym())
	return &result
}

func (val BillingOperationBalanceType) Int64() int64 {
	return int64(val.Synonym())
}

func (val BillingOperationBalanceType) Int64Ptr() *int64 {
	result := int64(val.Synonym())
	return &result
}

func (val BillingOperationBalanceType) Uint() uint {
	return uint(val.Synonym())
}

func (val BillingOperationBalanceType) UintPtr() *uint {
	result := uint(val.Synonym())
	return &result
}

func (val BillingOperationBalanceType) Uint32() uint32 {
	return uint32(val.Synonym())
}

func (val BillingOperationBalanceType) Uint32Ptr() *uint32 {
	result := uint32(val.Synonym())
	return &result
}

func (val BillingOperationBalanceType) Uint64() uint64 {
	return uint64(val.Synonym())
}

func (val BillingOperationBalanceType) Uint64Ptr() *uint64 {
	result := uint64(val.Synonym())
	return &result
}

func (val BillingOperationBalanceType) IsKnown() bool {
	return val.Synonym() != BillingOperationBalanceType(math.MinInt32)
}

func ConvertIntToBillingOperationBalanceType(in int) BillingOperationBalanceType {
	return BillingOperationBalanceType(in).Synonym()
}

func ConvertUintToBillingOperationBalanceType(in uint) BillingOperationBalanceType {
	return BillingOperationBalanceType(in).Synonym()
}

func ConvertInt32ToBillingOperationBalanceType(in int32) BillingOperationBalanceType {
	return BillingOperationBalanceType(in).Synonym()
}

func ConvertUint32ToBillingOperationBalanceType(in uint32) BillingOperationBalanceType {
	return BillingOperationBalanceType(in).Synonym()
}

func ConvertInt64ToBillingOperationBalanceType(in int64) BillingOperationBalanceType {
	return BillingOperationBalanceType(in).Synonym()
}

func ConvertUint64ToBillingOperationBalanceType(in uint64) BillingOperationBalanceType {
	return BillingOperationBalanceType(in).Synonym()
}

var BillingOperationBalanceType_Lower_value = map[string]BillingOperationBalanceType{
	"billingoperationbalancetypeunknown": 0,
	"billingoperationbalancetypemain":    1,
	"billingoperationbalancetypecredit":  2,
}

func ConvertStringToBillingOperationBalanceType(in string) BillingOperationBalanceType {
	if result, ok := BillingOperationBalanceType_value[in]; ok {
		return BillingOperationBalanceType(result)
	}

	if result, ok := BillingOperationBalanceType_Lower_value[strings.ToLower(in)]; ok {
		return BillingOperationBalanceType(result)
	}

	return BillingOperationBalanceType(math.MinInt32)
}

var SliceBillingOperationBalanceTypeConvert *sliceBillingOperationBalanceTypeConvert

type sliceBillingOperationBalanceTypeConvert struct{}

func (*sliceBillingOperationBalanceTypeConvert) Synonym(in []BillingOperationBalanceType) []BillingOperationBalanceType {
	result := make([]BillingOperationBalanceType, len(in))
	for i, v := range in {
		result[i] = v.Synonym()
	}

	return result
}

func (*sliceBillingOperationBalanceTypeConvert) Int32(in []BillingOperationBalanceType) []int32 {
	result := make([]int32, len(in))
	for i, v := range in {
		result[i] = v.Int32()
	}

	return result
}

func (*sliceBillingOperationBalanceTypeConvert) Uint32(in []BillingOperationBalanceType) []uint32 {
	result := make([]uint32, len(in))
	for i, v := range in {
		result[i] = v.Uint32()
	}

	return result
}

func (*sliceBillingOperationBalanceTypeConvert) Uint64(in []BillingOperationBalanceType) []uint64 {
	result := make([]uint64, len(in))
	for i, v := range in {
		result[i] = v.Uint64()
	}

	return result
}

func (*sliceBillingOperationBalanceTypeConvert) Int64(in []BillingOperationBalanceType) []int64 {
	result := make([]int64, len(in))
	for i, v := range in {
		result[i] = v.Int64()
	}

	return result
}

func (*sliceBillingOperationBalanceTypeConvert) Uint(in []BillingOperationBalanceType) []uint {
	result := make([]uint, len(in))
	for i, v := range in {
		result[i] = v.Uint()
	}

	return result
}

func (*sliceBillingOperationBalanceTypeConvert) Int(in []BillingOperationBalanceType) []int {
	result := make([]int, len(in))
	for i, v := range in {
		result[i] = v.Int()
	}

	return result
}

func (*sliceBillingOperationBalanceTypeConvert) IsKnown(in []BillingOperationBalanceType) []bool {
	result := make([]bool, len(in))
	for i, v := range in {
		result[i] = v.IsKnown()
	}

	return result
}

func (*sliceBillingOperationBalanceTypeConvert) ConvertIntToBillingOperationBalanceType(in []int) []BillingOperationBalanceType {
	result := make([]BillingOperationBalanceType, len(in))
	for i, v := range in {
		result[i] = ConvertIntToBillingOperationBalanceType(v)
	}

	return result
}

func (*sliceBillingOperationBalanceTypeConvert) ConvertUintToBillingOperationBalanceType(in []uint) []BillingOperationBalanceType {
	result := make([]BillingOperationBalanceType, len(in))
	for i, v := range in {
		result[i] = ConvertUintToBillingOperationBalanceType(v)
	}

	return result
}

func (*sliceBillingOperationBalanceTypeConvert) ConvertInt32ToBillingOperationBalanceType(in []int32) []BillingOperationBalanceType {
	result := make([]BillingOperationBalanceType, len(in))
	for i, v := range in {
		result[i] = ConvertInt32ToBillingOperationBalanceType(v)
	}

	return result
}

func (*sliceBillingOperationBalanceTypeConvert) ConvertUint32ToBillingOperationBalanceType(in []uint32) []BillingOperationBalanceType {
	result := make([]BillingOperationBalanceType, len(in))
	for i, v := range in {
		result[i] = ConvertUint32ToBillingOperationBalanceType(v)
	}

	return result
}

func (*sliceBillingOperationBalanceTypeConvert) ConvertInt64ToBillingOperationBalanceType(in []int64) []BillingOperationBalanceType {
	result := make([]BillingOperationBalanceType, len(in))
	for i, v := range in {
		result[i] = ConvertInt64ToBillingOperationBalanceType(v)
	}

	return result
}

func (*sliceBillingOperationBalanceTypeConvert) ConvertUint64ToBillingOperationBalanceType(in []uint64) []BillingOperationBalanceType {
	result := make([]BillingOperationBalanceType, len(in))
	for i, v := range in {
		result[i] = ConvertUint64ToBillingOperationBalanceType(v)
	}

	return result
}

func (*sliceBillingOperationBalanceTypeConvert) ConvertStringToBillingOperationBalanceType(in []string) []BillingOperationBalanceType {
	result := make([]BillingOperationBalanceType, len(in))
	for i, v := range in {
		result[i] = ConvertStringToBillingOperationBalanceType(v)
	}

	return result
}

func NewBillingOperationBalanceTypeUsage() *BillingOperationBalanceTypeUsage {
	return &BillingOperationBalanceTypeUsage{
		enumMap: map[BillingOperationBalanceType]bool{
			BillingOperationBalanceType_BillingOperationBalanceTypeUnknown: false,
			BillingOperationBalanceType_BillingOperationBalanceTypeMain:    false,
			BillingOperationBalanceType_BillingOperationBalanceTypeCredit:  false,
		},
	}
}

func IsBillingOperationBalanceType(target BillingOperationBalanceType, matches ...BillingOperationBalanceType) bool {
	for _, v := range matches {
		if target == v {
			return true
		}
	}

	return false
}

type BillingOperationBalanceTypeUsage struct {
	enumMap map[BillingOperationBalanceType]bool
}

func (u *BillingOperationBalanceTypeUsage) Use(slice ...BillingOperationBalanceType) *BillingOperationBalanceTypeUsage {
	for _, in := range slice {
		u.enumMap[in] = true
	}

	return u
}

func (u *BillingOperationBalanceTypeUsage) Check() []string {
	var result []string
	for k, v := range u.enumMap {
		if v {
			continue
		}

		result = append(result, k.String())
	}

	return result
}

func (u *BillingOperationBalanceTypeUsage) BillingOperationBalanceType_BillingOperationBalanceTypeUnknown() BillingOperationBalanceType {
	u.Use(BillingOperationBalanceType_BillingOperationBalanceTypeUnknown)
	return BillingOperationBalanceType_BillingOperationBalanceTypeUnknown
}

func (u *BillingOperationBalanceTypeUsage) BillingOperationBalanceType_BillingOperationBalanceTypeMain() BillingOperationBalanceType {
	u.Use(BillingOperationBalanceType_BillingOperationBalanceTypeMain)
	return BillingOperationBalanceType_BillingOperationBalanceTypeMain
}

func (u *BillingOperationBalanceTypeUsage) BillingOperationBalanceType_BillingOperationBalanceTypeCredit() BillingOperationBalanceType {
	u.Use(BillingOperationBalanceType_BillingOperationBalanceTypeCredit)
	return BillingOperationBalanceType_BillingOperationBalanceTypeCredit
}

func (val BillingOperationType) Synonym() BillingOperationType {
	if _, ok := BillingOperationType_name[int32(val)]; ok {
		return val
	}

	return BillingOperationType(math.MinInt32)
}

func (val BillingOperationType) Int() int {
	return int(val.Synonym())
}

func (val BillingOperationType) IntPtr() *int {
	result := int(val.Synonym())
	return &result
}

func (val BillingOperationType) Int32() int32 {
	return int32(val.Synonym())
}

func (val BillingOperationType) Int32Ptr() *int32 {
	result := int32(val.Synonym())
	return &result
}

func (val BillingOperationType) Int64() int64 {
	return int64(val.Synonym())
}

func (val BillingOperationType) Int64Ptr() *int64 {
	result := int64(val.Synonym())
	return &result
}

func (val BillingOperationType) Uint() uint {
	return uint(val.Synonym())
}

func (val BillingOperationType) UintPtr() *uint {
	result := uint(val.Synonym())
	return &result
}

func (val BillingOperationType) Uint32() uint32 {
	return uint32(val.Synonym())
}

func (val BillingOperationType) Uint32Ptr() *uint32 {
	result := uint32(val.Synonym())
	return &result
}

func (val BillingOperationType) Uint64() uint64 {
	return uint64(val.Synonym())
}

func (val BillingOperationType) Uint64Ptr() *uint64 {
	result := uint64(val.Synonym())
	return &result
}

func (val BillingOperationType) IsKnown() bool {
	return val.Synonym() != BillingOperationType(math.MinInt32)
}

func ConvertIntToBillingOperationType(in int) BillingOperationType {
	return BillingOperationType(in).Synonym()
}

func ConvertUintToBillingOperationType(in uint) BillingOperationType {
	return BillingOperationType(in).Synonym()
}

func ConvertInt32ToBillingOperationType(in int32) BillingOperationType {
	return BillingOperationType(in).Synonym()
}

func ConvertUint32ToBillingOperationType(in uint32) BillingOperationType {
	return BillingOperationType(in).Synonym()
}

func ConvertInt64ToBillingOperationType(in int64) BillingOperationType {
	return BillingOperationType(in).Synonym()
}

func ConvertUint64ToBillingOperationType(in uint64) BillingOperationType {
	return BillingOperationType(in).Synonym()
}

var BillingOperationType_Lower_value = map[string]BillingOperationType{
	"billingoperationtypeunknown":                     0,
	"billingoperationtypepayin":                       1,
	"billingoperationtypepayout":                      2,
	"billingoperationtyperefund":                      3,
	"billingoperationtypeintransfer":                  4,
	"billingoperationtypeouttransfer":                 5,
	"billingoperationtypepayincommissionoutacquirer":  6,
	"billingoperationtypepayincommissioninsystem":     7,
	"billingoperationtypepayincommissionoutsystem":    8,
	"billingoperationtypepayincommissioninmerchant":   9,
	"billingoperationtypepayincommissionoutmerchant":  10,
	"billingoperationtypeinternalintransfer":          11,
	"billingoperationtypeinternalouttransfer":         12,
	"billingoperationtypesplitouttransfer":            13,
	"billingoperationtypesplitintransfer":             14,
	"billingoperationtypepayoutcommissionoutacquirer": 15,
	"billingoperationtypepayoutcommissioninsystem":    16,
	"billingoperationtypepayoutcommissionoutsystem":   17,
	"billingoperationtypepayoutcommissioninmerchant":  18,
	"billingoperationtypepayoutcommissionoutmerchant": 19,
	"billingoperationtypesplitcommissioninsystem":     20,
	"billingoperationtypesplitcommissionoutsystem":    21,
	"billingoperationtypesplitcommissioninmerchant":   22,
	"billingoperationtypesplitcommissionoutmerchant":  23,
}

func ConvertStringToBillingOperationType(in string) BillingOperationType {
	if result, ok := BillingOperationType_value[in]; ok {
		return BillingOperationType(result)
	}

	if result, ok := BillingOperationType_Lower_value[strings.ToLower(in)]; ok {
		return BillingOperationType(result)
	}

	return BillingOperationType(math.MinInt32)
}

var SliceBillingOperationTypeConvert *sliceBillingOperationTypeConvert

type sliceBillingOperationTypeConvert struct{}

func (*sliceBillingOperationTypeConvert) Synonym(in []BillingOperationType) []BillingOperationType {
	result := make([]BillingOperationType, len(in))
	for i, v := range in {
		result[i] = v.Synonym()
	}

	return result
}

func (*sliceBillingOperationTypeConvert) Int32(in []BillingOperationType) []int32 {
	result := make([]int32, len(in))
	for i, v := range in {
		result[i] = v.Int32()
	}

	return result
}

func (*sliceBillingOperationTypeConvert) Uint32(in []BillingOperationType) []uint32 {
	result := make([]uint32, len(in))
	for i, v := range in {
		result[i] = v.Uint32()
	}

	return result
}

func (*sliceBillingOperationTypeConvert) Uint64(in []BillingOperationType) []uint64 {
	result := make([]uint64, len(in))
	for i, v := range in {
		result[i] = v.Uint64()
	}

	return result
}

func (*sliceBillingOperationTypeConvert) Int64(in []BillingOperationType) []int64 {
	result := make([]int64, len(in))
	for i, v := range in {
		result[i] = v.Int64()
	}

	return result
}

func (*sliceBillingOperationTypeConvert) Uint(in []BillingOperationType) []uint {
	result := make([]uint, len(in))
	for i, v := range in {
		result[i] = v.Uint()
	}

	return result
}

func (*sliceBillingOperationTypeConvert) Int(in []BillingOperationType) []int {
	result := make([]int, len(in))
	for i, v := range in {
		result[i] = v.Int()
	}

	return result
}

func (*sliceBillingOperationTypeConvert) IsKnown(in []BillingOperationType) []bool {
	result := make([]bool, len(in))
	for i, v := range in {
		result[i] = v.IsKnown()
	}

	return result
}

func (*sliceBillingOperationTypeConvert) ConvertIntToBillingOperationType(in []int) []BillingOperationType {
	result := make([]BillingOperationType, len(in))
	for i, v := range in {
		result[i] = ConvertIntToBillingOperationType(v)
	}

	return result
}

func (*sliceBillingOperationTypeConvert) ConvertUintToBillingOperationType(in []uint) []BillingOperationType {
	result := make([]BillingOperationType, len(in))
	for i, v := range in {
		result[i] = ConvertUintToBillingOperationType(v)
	}

	return result
}

func (*sliceBillingOperationTypeConvert) ConvertInt32ToBillingOperationType(in []int32) []BillingOperationType {
	result := make([]BillingOperationType, len(in))
	for i, v := range in {
		result[i] = ConvertInt32ToBillingOperationType(v)
	}

	return result
}

func (*sliceBillingOperationTypeConvert) ConvertUint32ToBillingOperationType(in []uint32) []BillingOperationType {
	result := make([]BillingOperationType, len(in))
	for i, v := range in {
		result[i] = ConvertUint32ToBillingOperationType(v)
	}

	return result
}

func (*sliceBillingOperationTypeConvert) ConvertInt64ToBillingOperationType(in []int64) []BillingOperationType {
	result := make([]BillingOperationType, len(in))
	for i, v := range in {
		result[i] = ConvertInt64ToBillingOperationType(v)
	}

	return result
}

func (*sliceBillingOperationTypeConvert) ConvertUint64ToBillingOperationType(in []uint64) []BillingOperationType {
	result := make([]BillingOperationType, len(in))
	for i, v := range in {
		result[i] = ConvertUint64ToBillingOperationType(v)
	}

	return result
}

func (*sliceBillingOperationTypeConvert) ConvertStringToBillingOperationType(in []string) []BillingOperationType {
	result := make([]BillingOperationType, len(in))
	for i, v := range in {
		result[i] = ConvertStringToBillingOperationType(v)
	}

	return result
}

func NewBillingOperationTypeUsage() *BillingOperationTypeUsage {
	return &BillingOperationTypeUsage{
		enumMap: map[BillingOperationType]bool{
			BillingOperationType_BillingOperationTypeUnknown:                     false,
			BillingOperationType_BillingOperationTypePayIn:                       false,
			BillingOperationType_BillingOperationTypePayOut:                      false,
			BillingOperationType_BillingOperationTypeRefund:                      false,
			BillingOperationType_BillingOperationTypeInTransfer:                  false,
			BillingOperationType_BillingOperationTypeOutTransfer:                 false,
			BillingOperationType_BillingOperationTypePayInCommissionOutAcquirer:  false,
			BillingOperationType_BillingOperationTypePayInCommissionInSystem:     false,
			BillingOperationType_BillingOperationTypePayInCommissionOutSystem:    false,
			BillingOperationType_BillingOperationTypePayInCommissionInMerchant:   false,
			BillingOperationType_BillingOperationTypePayInCommissionOutMerchant:  false,
			BillingOperationType_BillingOperationTypeInternalInTransfer:          false,
			BillingOperationType_BillingOperationTypeInternalOutTransfer:         false,
			BillingOperationType_BillingOperationTypeSplitOutTransfer:            false,
			BillingOperationType_BillingOperationTypeSplitInTransfer:             false,
			BillingOperationType_BillingOperationTypePayOutCommissionOutAcquirer: false,
			BillingOperationType_BillingOperationTypePayOutCommissionInSystem:    false,
			BillingOperationType_BillingOperationTypePayOutCommissionOutSystem:   false,
			BillingOperationType_BillingOperationTypePayOutCommissionInMerchant:  false,
			BillingOperationType_BillingOperationTypePayOutCommissionOutMerchant: false,
			BillingOperationType_BillingOperationTypeSplitCommissionInSystem:     false,
			BillingOperationType_BillingOperationTypeSplitCommissionOutSystem:    false,
			BillingOperationType_BillingOperationTypeSplitCommissionInMerchant:   false,
			BillingOperationType_BillingOperationTypeSplitCommissionOutMerchant:  false,
		},
	}
}

func IsBillingOperationType(target BillingOperationType, matches ...BillingOperationType) bool {
	for _, v := range matches {
		if target == v {
			return true
		}
	}

	return false
}

type BillingOperationTypeUsage struct {
	enumMap map[BillingOperationType]bool
}

func (u *BillingOperationTypeUsage) Use(slice ...BillingOperationType) *BillingOperationTypeUsage {
	for _, in := range slice {
		u.enumMap[in] = true
	}

	return u
}

func (u *BillingOperationTypeUsage) Check() []string {
	var result []string
	for k, v := range u.enumMap {
		if v {
			continue
		}

		result = append(result, k.String())
	}

	return result
}

func (u *BillingOperationTypeUsage) BillingOperationType_BillingOperationTypeUnknown() BillingOperationType {
	u.Use(BillingOperationType_BillingOperationTypeUnknown)
	return BillingOperationType_BillingOperationTypeUnknown
}

func (u *BillingOperationTypeUsage) BillingOperationType_BillingOperationTypePayIn() BillingOperationType {
	u.Use(BillingOperationType_BillingOperationTypePayIn)
	return BillingOperationType_BillingOperationTypePayIn
}

func (u *BillingOperationTypeUsage) BillingOperationType_BillingOperationTypePayOut() BillingOperationType {
	u.Use(BillingOperationType_BillingOperationTypePayOut)
	return BillingOperationType_BillingOperationTypePayOut
}

func (u *BillingOperationTypeUsage) BillingOperationType_BillingOperationTypeRefund() BillingOperationType {
	u.Use(BillingOperationType_BillingOperationTypeRefund)
	return BillingOperationType_BillingOperationTypeRefund
}

func (u *BillingOperationTypeUsage) BillingOperationType_BillingOperationTypeInTransfer() BillingOperationType {
	u.Use(BillingOperationType_BillingOperationTypeInTransfer)
	return BillingOperationType_BillingOperationTypeInTransfer
}

func (u *BillingOperationTypeUsage) BillingOperationType_BillingOperationTypeOutTransfer() BillingOperationType {
	u.Use(BillingOperationType_BillingOperationTypeOutTransfer)
	return BillingOperationType_BillingOperationTypeOutTransfer
}

func (u *BillingOperationTypeUsage) BillingOperationType_BillingOperationTypePayInCommissionOutAcquirer() BillingOperationType {
	u.Use(BillingOperationType_BillingOperationTypePayInCommissionOutAcquirer)
	return BillingOperationType_BillingOperationTypePayInCommissionOutAcquirer
}

func (u *BillingOperationTypeUsage) BillingOperationType_BillingOperationTypePayInCommissionInSystem() BillingOperationType {
	u.Use(BillingOperationType_BillingOperationTypePayInCommissionInSystem)
	return BillingOperationType_BillingOperationTypePayInCommissionInSystem
}

func (u *BillingOperationTypeUsage) BillingOperationType_BillingOperationTypePayInCommissionOutSystem() BillingOperationType {
	u.Use(BillingOperationType_BillingOperationTypePayInCommissionOutSystem)
	return BillingOperationType_BillingOperationTypePayInCommissionOutSystem
}

func (u *BillingOperationTypeUsage) BillingOperationType_BillingOperationTypePayInCommissionInMerchant() BillingOperationType {
	u.Use(BillingOperationType_BillingOperationTypePayInCommissionInMerchant)
	return BillingOperationType_BillingOperationTypePayInCommissionInMerchant
}

func (u *BillingOperationTypeUsage) BillingOperationType_BillingOperationTypePayInCommissionOutMerchant() BillingOperationType {
	u.Use(BillingOperationType_BillingOperationTypePayInCommissionOutMerchant)
	return BillingOperationType_BillingOperationTypePayInCommissionOutMerchant
}

func (u *BillingOperationTypeUsage) BillingOperationType_BillingOperationTypeInternalInTransfer() BillingOperationType {
	u.Use(BillingOperationType_BillingOperationTypeInternalInTransfer)
	return BillingOperationType_BillingOperationTypeInternalInTransfer
}

func (u *BillingOperationTypeUsage) BillingOperationType_BillingOperationTypeInternalOutTransfer() BillingOperationType {
	u.Use(BillingOperationType_BillingOperationTypeInternalOutTransfer)
	return BillingOperationType_BillingOperationTypeInternalOutTransfer
}

func (u *BillingOperationTypeUsage) BillingOperationType_BillingOperationTypeSplitOutTransfer() BillingOperationType {
	u.Use(BillingOperationType_BillingOperationTypeSplitOutTransfer)
	return BillingOperationType_BillingOperationTypeSplitOutTransfer
}

func (u *BillingOperationTypeUsage) BillingOperationType_BillingOperationTypeSplitInTransfer() BillingOperationType {
	u.Use(BillingOperationType_BillingOperationTypeSplitInTransfer)
	return BillingOperationType_BillingOperationTypeSplitInTransfer
}

func (u *BillingOperationTypeUsage) BillingOperationType_BillingOperationTypePayOutCommissionOutAcquirer() BillingOperationType {
	u.Use(BillingOperationType_BillingOperationTypePayOutCommissionOutAcquirer)
	return BillingOperationType_BillingOperationTypePayOutCommissionOutAcquirer
}

func (u *BillingOperationTypeUsage) BillingOperationType_BillingOperationTypePayOutCommissionInSystem() BillingOperationType {
	u.Use(BillingOperationType_BillingOperationTypePayOutCommissionInSystem)
	return BillingOperationType_BillingOperationTypePayOutCommissionInSystem
}

func (u *BillingOperationTypeUsage) BillingOperationType_BillingOperationTypePayOutCommissionOutSystem() BillingOperationType {
	u.Use(BillingOperationType_BillingOperationTypePayOutCommissionOutSystem)
	return BillingOperationType_BillingOperationTypePayOutCommissionOutSystem
}

func (u *BillingOperationTypeUsage) BillingOperationType_BillingOperationTypePayOutCommissionInMerchant() BillingOperationType {
	u.Use(BillingOperationType_BillingOperationTypePayOutCommissionInMerchant)
	return BillingOperationType_BillingOperationTypePayOutCommissionInMerchant
}

func (u *BillingOperationTypeUsage) BillingOperationType_BillingOperationTypePayOutCommissionOutMerchant() BillingOperationType {
	u.Use(BillingOperationType_BillingOperationTypePayOutCommissionOutMerchant)
	return BillingOperationType_BillingOperationTypePayOutCommissionOutMerchant
}

func (u *BillingOperationTypeUsage) BillingOperationType_BillingOperationTypeSplitCommissionInSystem() BillingOperationType {
	u.Use(BillingOperationType_BillingOperationTypeSplitCommissionInSystem)
	return BillingOperationType_BillingOperationTypeSplitCommissionInSystem
}

func (u *BillingOperationTypeUsage) BillingOperationType_BillingOperationTypeSplitCommissionOutSystem() BillingOperationType {
	u.Use(BillingOperationType_BillingOperationTypeSplitCommissionOutSystem)
	return BillingOperationType_BillingOperationTypeSplitCommissionOutSystem
}

func (u *BillingOperationTypeUsage) BillingOperationType_BillingOperationTypeSplitCommissionInMerchant() BillingOperationType {
	u.Use(BillingOperationType_BillingOperationTypeSplitCommissionInMerchant)
	return BillingOperationType_BillingOperationTypeSplitCommissionInMerchant
}

func (u *BillingOperationTypeUsage) BillingOperationType_BillingOperationTypeSplitCommissionOutMerchant() BillingOperationType {
	u.Use(BillingOperationType_BillingOperationTypeSplitCommissionOutMerchant)
	return BillingOperationType_BillingOperationTypeSplitCommissionOutMerchant
}

func (val BillingOperationStatus) Synonym() BillingOperationStatus {
	if _, ok := BillingOperationStatus_name[int32(val)]; ok {
		return val
	}

	return BillingOperationStatus(math.MinInt32)
}

func (val BillingOperationStatus) Int() int {
	return int(val.Synonym())
}

func (val BillingOperationStatus) IntPtr() *int {
	result := int(val.Synonym())
	return &result
}

func (val BillingOperationStatus) Int32() int32 {
	return int32(val.Synonym())
}

func (val BillingOperationStatus) Int32Ptr() *int32 {
	result := int32(val.Synonym())
	return &result
}

func (val BillingOperationStatus) Int64() int64 {
	return int64(val.Synonym())
}

func (val BillingOperationStatus) Int64Ptr() *int64 {
	result := int64(val.Synonym())
	return &result
}

func (val BillingOperationStatus) Uint() uint {
	return uint(val.Synonym())
}

func (val BillingOperationStatus) UintPtr() *uint {
	result := uint(val.Synonym())
	return &result
}

func (val BillingOperationStatus) Uint32() uint32 {
	return uint32(val.Synonym())
}

func (val BillingOperationStatus) Uint32Ptr() *uint32 {
	result := uint32(val.Synonym())
	return &result
}

func (val BillingOperationStatus) Uint64() uint64 {
	return uint64(val.Synonym())
}

func (val BillingOperationStatus) Uint64Ptr() *uint64 {
	result := uint64(val.Synonym())
	return &result
}

func (val BillingOperationStatus) IsKnown() bool {
	return val.Synonym() != BillingOperationStatus(math.MinInt32)
}

func ConvertIntToBillingOperationStatus(in int) BillingOperationStatus {
	return BillingOperationStatus(in).Synonym()
}

func ConvertUintToBillingOperationStatus(in uint) BillingOperationStatus {
	return BillingOperationStatus(in).Synonym()
}

func ConvertInt32ToBillingOperationStatus(in int32) BillingOperationStatus {
	return BillingOperationStatus(in).Synonym()
}

func ConvertUint32ToBillingOperationStatus(in uint32) BillingOperationStatus {
	return BillingOperationStatus(in).Synonym()
}

func ConvertInt64ToBillingOperationStatus(in int64) BillingOperationStatus {
	return BillingOperationStatus(in).Synonym()
}

func ConvertUint64ToBillingOperationStatus(in uint64) BillingOperationStatus {
	return BillingOperationStatus(in).Synonym()
}

var BillingOperationStatus_Lower_value = map[string]BillingOperationStatus{
	"billingoperationstatusunknown":    0,
	"billingoperationstatusfinalized":  1,
	"billingoperationstatusinprogress": 2,
}

func ConvertStringToBillingOperationStatus(in string) BillingOperationStatus {
	if result, ok := BillingOperationStatus_value[in]; ok {
		return BillingOperationStatus(result)
	}

	if result, ok := BillingOperationStatus_Lower_value[strings.ToLower(in)]; ok {
		return BillingOperationStatus(result)
	}

	return BillingOperationStatus(math.MinInt32)
}

var SliceBillingOperationStatusConvert *sliceBillingOperationStatusConvert

type sliceBillingOperationStatusConvert struct{}

func (*sliceBillingOperationStatusConvert) Synonym(in []BillingOperationStatus) []BillingOperationStatus {
	result := make([]BillingOperationStatus, len(in))
	for i, v := range in {
		result[i] = v.Synonym()
	}

	return result
}

func (*sliceBillingOperationStatusConvert) Int32(in []BillingOperationStatus) []int32 {
	result := make([]int32, len(in))
	for i, v := range in {
		result[i] = v.Int32()
	}

	return result
}

func (*sliceBillingOperationStatusConvert) Uint32(in []BillingOperationStatus) []uint32 {
	result := make([]uint32, len(in))
	for i, v := range in {
		result[i] = v.Uint32()
	}

	return result
}

func (*sliceBillingOperationStatusConvert) Uint64(in []BillingOperationStatus) []uint64 {
	result := make([]uint64, len(in))
	for i, v := range in {
		result[i] = v.Uint64()
	}

	return result
}

func (*sliceBillingOperationStatusConvert) Int64(in []BillingOperationStatus) []int64 {
	result := make([]int64, len(in))
	for i, v := range in {
		result[i] = v.Int64()
	}

	return result
}

func (*sliceBillingOperationStatusConvert) Uint(in []BillingOperationStatus) []uint {
	result := make([]uint, len(in))
	for i, v := range in {
		result[i] = v.Uint()
	}

	return result
}

func (*sliceBillingOperationStatusConvert) Int(in []BillingOperationStatus) []int {
	result := make([]int, len(in))
	for i, v := range in {
		result[i] = v.Int()
	}

	return result
}

func (*sliceBillingOperationStatusConvert) IsKnown(in []BillingOperationStatus) []bool {
	result := make([]bool, len(in))
	for i, v := range in {
		result[i] = v.IsKnown()
	}

	return result
}

func (*sliceBillingOperationStatusConvert) ConvertIntToBillingOperationStatus(in []int) []BillingOperationStatus {
	result := make([]BillingOperationStatus, len(in))
	for i, v := range in {
		result[i] = ConvertIntToBillingOperationStatus(v)
	}

	return result
}

func (*sliceBillingOperationStatusConvert) ConvertUintToBillingOperationStatus(in []uint) []BillingOperationStatus {
	result := make([]BillingOperationStatus, len(in))
	for i, v := range in {
		result[i] = ConvertUintToBillingOperationStatus(v)
	}

	return result
}

func (*sliceBillingOperationStatusConvert) ConvertInt32ToBillingOperationStatus(in []int32) []BillingOperationStatus {
	result := make([]BillingOperationStatus, len(in))
	for i, v := range in {
		result[i] = ConvertInt32ToBillingOperationStatus(v)
	}

	return result
}

func (*sliceBillingOperationStatusConvert) ConvertUint32ToBillingOperationStatus(in []uint32) []BillingOperationStatus {
	result := make([]BillingOperationStatus, len(in))
	for i, v := range in {
		result[i] = ConvertUint32ToBillingOperationStatus(v)
	}

	return result
}

func (*sliceBillingOperationStatusConvert) ConvertInt64ToBillingOperationStatus(in []int64) []BillingOperationStatus {
	result := make([]BillingOperationStatus, len(in))
	for i, v := range in {
		result[i] = ConvertInt64ToBillingOperationStatus(v)
	}

	return result
}

func (*sliceBillingOperationStatusConvert) ConvertUint64ToBillingOperationStatus(in []uint64) []BillingOperationStatus {
	result := make([]BillingOperationStatus, len(in))
	for i, v := range in {
		result[i] = ConvertUint64ToBillingOperationStatus(v)
	}

	return result
}

func (*sliceBillingOperationStatusConvert) ConvertStringToBillingOperationStatus(in []string) []BillingOperationStatus {
	result := make([]BillingOperationStatus, len(in))
	for i, v := range in {
		result[i] = ConvertStringToBillingOperationStatus(v)
	}

	return result
}

func NewBillingOperationStatusUsage() *BillingOperationStatusUsage {
	return &BillingOperationStatusUsage{
		enumMap: map[BillingOperationStatus]bool{
			BillingOperationStatus_BillingOperationStatusUnknown:    false,
			BillingOperationStatus_BillingOperationStatusFinalized:  false,
			BillingOperationStatus_BillingOperationStatusInProgress: false,
		},
	}
}

func IsBillingOperationStatus(target BillingOperationStatus, matches ...BillingOperationStatus) bool {
	for _, v := range matches {
		if target == v {
			return true
		}
	}

	return false
}

type BillingOperationStatusUsage struct {
	enumMap map[BillingOperationStatus]bool
}

func (u *BillingOperationStatusUsage) Use(slice ...BillingOperationStatus) *BillingOperationStatusUsage {
	for _, in := range slice {
		u.enumMap[in] = true
	}

	return u
}

func (u *BillingOperationStatusUsage) Check() []string {
	var result []string
	for k, v := range u.enumMap {
		if v {
			continue
		}

		result = append(result, k.String())
	}

	return result
}

func (u *BillingOperationStatusUsage) BillingOperationStatus_BillingOperationStatusUnknown() BillingOperationStatus {
	u.Use(BillingOperationStatus_BillingOperationStatusUnknown)
	return BillingOperationStatus_BillingOperationStatusUnknown
}

func (u *BillingOperationStatusUsage) BillingOperationStatus_BillingOperationStatusFinalized() BillingOperationStatus {
	u.Use(BillingOperationStatus_BillingOperationStatusFinalized)
	return BillingOperationStatus_BillingOperationStatusFinalized
}

func (u *BillingOperationStatusUsage) BillingOperationStatus_BillingOperationStatusInProgress() BillingOperationStatus {
	u.Use(BillingOperationStatus_BillingOperationStatusInProgress)
	return BillingOperationStatus_BillingOperationStatusInProgress
}

func (val BillingOperationTypeGroup) Synonym() BillingOperationTypeGroup {
	if _, ok := BillingOperationTypeGroup_name[int32(val)]; ok {
		return val
	}

	return BillingOperationTypeGroup(math.MinInt32)
}

func (val BillingOperationTypeGroup) Int() int {
	return int(val.Synonym())
}

func (val BillingOperationTypeGroup) IntPtr() *int {
	result := int(val.Synonym())
	return &result
}

func (val BillingOperationTypeGroup) Int32() int32 {
	return int32(val.Synonym())
}

func (val BillingOperationTypeGroup) Int32Ptr() *int32 {
	result := int32(val.Synonym())
	return &result
}

func (val BillingOperationTypeGroup) Int64() int64 {
	return int64(val.Synonym())
}

func (val BillingOperationTypeGroup) Int64Ptr() *int64 {
	result := int64(val.Synonym())
	return &result
}

func (val BillingOperationTypeGroup) Uint() uint {
	return uint(val.Synonym())
}

func (val BillingOperationTypeGroup) UintPtr() *uint {
	result := uint(val.Synonym())
	return &result
}

func (val BillingOperationTypeGroup) Uint32() uint32 {
	return uint32(val.Synonym())
}

func (val BillingOperationTypeGroup) Uint32Ptr() *uint32 {
	result := uint32(val.Synonym())
	return &result
}

func (val BillingOperationTypeGroup) Uint64() uint64 {
	return uint64(val.Synonym())
}

func (val BillingOperationTypeGroup) Uint64Ptr() *uint64 {
	result := uint64(val.Synonym())
	return &result
}

func (val BillingOperationTypeGroup) IsKnown() bool {
	return val.Synonym() != BillingOperationTypeGroup(math.MinInt32)
}

func ConvertIntToBillingOperationTypeGroup(in int) BillingOperationTypeGroup {
	return BillingOperationTypeGroup(in).Synonym()
}

func ConvertUintToBillingOperationTypeGroup(in uint) BillingOperationTypeGroup {
	return BillingOperationTypeGroup(in).Synonym()
}

func ConvertInt32ToBillingOperationTypeGroup(in int32) BillingOperationTypeGroup {
	return BillingOperationTypeGroup(in).Synonym()
}

func ConvertUint32ToBillingOperationTypeGroup(in uint32) BillingOperationTypeGroup {
	return BillingOperationTypeGroup(in).Synonym()
}

func ConvertInt64ToBillingOperationTypeGroup(in int64) BillingOperationTypeGroup {
	return BillingOperationTypeGroup(in).Synonym()
}

func ConvertUint64ToBillingOperationTypeGroup(in uint64) BillingOperationTypeGroup {
	return BillingOperationTypeGroup(in).Synonym()
}

var BillingOperationTypeGroup_Lower_value = map[string]BillingOperationTypeGroup{
	"billingoperationtypegroupunknown": 0,
	"billingoperationtypegroupin":      1,
	"billingoperationtypegroupout":     2,
	"billingoperationtypegrouprefund":  3,
	"billingoperationtypegrouppayin":   4,
}

func ConvertStringToBillingOperationTypeGroup(in string) BillingOperationTypeGroup {
	if result, ok := BillingOperationTypeGroup_value[in]; ok {
		return BillingOperationTypeGroup(result)
	}

	if result, ok := BillingOperationTypeGroup_Lower_value[strings.ToLower(in)]; ok {
		return BillingOperationTypeGroup(result)
	}

	return BillingOperationTypeGroup(math.MinInt32)
}

var SliceBillingOperationTypeGroupConvert *sliceBillingOperationTypeGroupConvert

type sliceBillingOperationTypeGroupConvert struct{}

func (*sliceBillingOperationTypeGroupConvert) Synonym(in []BillingOperationTypeGroup) []BillingOperationTypeGroup {
	result := make([]BillingOperationTypeGroup, len(in))
	for i, v := range in {
		result[i] = v.Synonym()
	}

	return result
}

func (*sliceBillingOperationTypeGroupConvert) Int32(in []BillingOperationTypeGroup) []int32 {
	result := make([]int32, len(in))
	for i, v := range in {
		result[i] = v.Int32()
	}

	return result
}

func (*sliceBillingOperationTypeGroupConvert) Uint32(in []BillingOperationTypeGroup) []uint32 {
	result := make([]uint32, len(in))
	for i, v := range in {
		result[i] = v.Uint32()
	}

	return result
}

func (*sliceBillingOperationTypeGroupConvert) Uint64(in []BillingOperationTypeGroup) []uint64 {
	result := make([]uint64, len(in))
	for i, v := range in {
		result[i] = v.Uint64()
	}

	return result
}

func (*sliceBillingOperationTypeGroupConvert) Int64(in []BillingOperationTypeGroup) []int64 {
	result := make([]int64, len(in))
	for i, v := range in {
		result[i] = v.Int64()
	}

	return result
}

func (*sliceBillingOperationTypeGroupConvert) Uint(in []BillingOperationTypeGroup) []uint {
	result := make([]uint, len(in))
	for i, v := range in {
		result[i] = v.Uint()
	}

	return result
}

func (*sliceBillingOperationTypeGroupConvert) Int(in []BillingOperationTypeGroup) []int {
	result := make([]int, len(in))
	for i, v := range in {
		result[i] = v.Int()
	}

	return result
}

func (*sliceBillingOperationTypeGroupConvert) IsKnown(in []BillingOperationTypeGroup) []bool {
	result := make([]bool, len(in))
	for i, v := range in {
		result[i] = v.IsKnown()
	}

	return result
}

func (*sliceBillingOperationTypeGroupConvert) ConvertIntToBillingOperationTypeGroup(in []int) []BillingOperationTypeGroup {
	result := make([]BillingOperationTypeGroup, len(in))
	for i, v := range in {
		result[i] = ConvertIntToBillingOperationTypeGroup(v)
	}

	return result
}

func (*sliceBillingOperationTypeGroupConvert) ConvertUintToBillingOperationTypeGroup(in []uint) []BillingOperationTypeGroup {
	result := make([]BillingOperationTypeGroup, len(in))
	for i, v := range in {
		result[i] = ConvertUintToBillingOperationTypeGroup(v)
	}

	return result
}

func (*sliceBillingOperationTypeGroupConvert) ConvertInt32ToBillingOperationTypeGroup(in []int32) []BillingOperationTypeGroup {
	result := make([]BillingOperationTypeGroup, len(in))
	for i, v := range in {
		result[i] = ConvertInt32ToBillingOperationTypeGroup(v)
	}

	return result
}

func (*sliceBillingOperationTypeGroupConvert) ConvertUint32ToBillingOperationTypeGroup(in []uint32) []BillingOperationTypeGroup {
	result := make([]BillingOperationTypeGroup, len(in))
	for i, v := range in {
		result[i] = ConvertUint32ToBillingOperationTypeGroup(v)
	}

	return result
}

func (*sliceBillingOperationTypeGroupConvert) ConvertInt64ToBillingOperationTypeGroup(in []int64) []BillingOperationTypeGroup {
	result := make([]BillingOperationTypeGroup, len(in))
	for i, v := range in {
		result[i] = ConvertInt64ToBillingOperationTypeGroup(v)
	}

	return result
}

func (*sliceBillingOperationTypeGroupConvert) ConvertUint64ToBillingOperationTypeGroup(in []uint64) []BillingOperationTypeGroup {
	result := make([]BillingOperationTypeGroup, len(in))
	for i, v := range in {
		result[i] = ConvertUint64ToBillingOperationTypeGroup(v)
	}

	return result
}

func (*sliceBillingOperationTypeGroupConvert) ConvertStringToBillingOperationTypeGroup(in []string) []BillingOperationTypeGroup {
	result := make([]BillingOperationTypeGroup, len(in))
	for i, v := range in {
		result[i] = ConvertStringToBillingOperationTypeGroup(v)
	}

	return result
}

func NewBillingOperationTypeGroupUsage() *BillingOperationTypeGroupUsage {
	return &BillingOperationTypeGroupUsage{
		enumMap: map[BillingOperationTypeGroup]bool{
			BillingOperationTypeGroup_BillingOperationTypeGroupUnknown: false,
			BillingOperationTypeGroup_BillingOperationTypeGroupIn:      false,
			BillingOperationTypeGroup_BillingOperationTypeGroupOut:     false,
			BillingOperationTypeGroup_BillingOperationTypeGroupRefund:  false,
			BillingOperationTypeGroup_BillingOperationTypeGroupPayIn:   false,
		},
	}
}

func IsBillingOperationTypeGroup(target BillingOperationTypeGroup, matches ...BillingOperationTypeGroup) bool {
	for _, v := range matches {
		if target == v {
			return true
		}
	}

	return false
}

type BillingOperationTypeGroupUsage struct {
	enumMap map[BillingOperationTypeGroup]bool
}

func (u *BillingOperationTypeGroupUsage) Use(slice ...BillingOperationTypeGroup) *BillingOperationTypeGroupUsage {
	for _, in := range slice {
		u.enumMap[in] = true
	}

	return u
}

func (u *BillingOperationTypeGroupUsage) Check() []string {
	var result []string
	for k, v := range u.enumMap {
		if v {
			continue
		}

		result = append(result, k.String())
	}

	return result
}

func (u *BillingOperationTypeGroupUsage) BillingOperationTypeGroup_BillingOperationTypeGroupUnknown() BillingOperationTypeGroup {
	u.Use(BillingOperationTypeGroup_BillingOperationTypeGroupUnknown)
	return BillingOperationTypeGroup_BillingOperationTypeGroupUnknown
}

func (u *BillingOperationTypeGroupUsage) BillingOperationTypeGroup_BillingOperationTypeGroupIn() BillingOperationTypeGroup {
	u.Use(BillingOperationTypeGroup_BillingOperationTypeGroupIn)
	return BillingOperationTypeGroup_BillingOperationTypeGroupIn
}

func (u *BillingOperationTypeGroupUsage) BillingOperationTypeGroup_BillingOperationTypeGroupOut() BillingOperationTypeGroup {
	u.Use(BillingOperationTypeGroup_BillingOperationTypeGroupOut)
	return BillingOperationTypeGroup_BillingOperationTypeGroupOut
}

func (u *BillingOperationTypeGroupUsage) BillingOperationTypeGroup_BillingOperationTypeGroupRefund() BillingOperationTypeGroup {
	u.Use(BillingOperationTypeGroup_BillingOperationTypeGroupRefund)
	return BillingOperationTypeGroup_BillingOperationTypeGroupRefund
}

func (u *BillingOperationTypeGroupUsage) BillingOperationTypeGroup_BillingOperationTypeGroupPayIn() BillingOperationTypeGroup {
	u.Use(BillingOperationTypeGroup_BillingOperationTypeGroupPayIn)
	return BillingOperationTypeGroup_BillingOperationTypeGroupPayIn
}

func (val BillingEarnType) Synonym() BillingEarnType {
	if _, ok := BillingEarnType_name[int32(val)]; ok {
		return val
	}

	return BillingEarnType(math.MinInt32)
}

func (val BillingEarnType) Int() int {
	return int(val.Synonym())
}

func (val BillingEarnType) IntPtr() *int {
	result := int(val.Synonym())
	return &result
}

func (val BillingEarnType) Int32() int32 {
	return int32(val.Synonym())
}

func (val BillingEarnType) Int32Ptr() *int32 {
	result := int32(val.Synonym())
	return &result
}

func (val BillingEarnType) Int64() int64 {
	return int64(val.Synonym())
}

func (val BillingEarnType) Int64Ptr() *int64 {
	result := int64(val.Synonym())
	return &result
}

func (val BillingEarnType) Uint() uint {
	return uint(val.Synonym())
}

func (val BillingEarnType) UintPtr() *uint {
	result := uint(val.Synonym())
	return &result
}

func (val BillingEarnType) Uint32() uint32 {
	return uint32(val.Synonym())
}

func (val BillingEarnType) Uint32Ptr() *uint32 {
	result := uint32(val.Synonym())
	return &result
}

func (val BillingEarnType) Uint64() uint64 {
	return uint64(val.Synonym())
}

func (val BillingEarnType) Uint64Ptr() *uint64 {
	result := uint64(val.Synonym())
	return &result
}

func (val BillingEarnType) IsKnown() bool {
	return val.Synonym() != BillingEarnType(math.MinInt32)
}

func ConvertIntToBillingEarnType(in int) BillingEarnType {
	return BillingEarnType(in).Synonym()
}

func ConvertUintToBillingEarnType(in uint) BillingEarnType {
	return BillingEarnType(in).Synonym()
}

func ConvertInt32ToBillingEarnType(in int32) BillingEarnType {
	return BillingEarnType(in).Synonym()
}

func ConvertUint32ToBillingEarnType(in uint32) BillingEarnType {
	return BillingEarnType(in).Synonym()
}

func ConvertInt64ToBillingEarnType(in int64) BillingEarnType {
	return BillingEarnType(in).Synonym()
}

func ConvertUint64ToBillingEarnType(in uint64) BillingEarnType {
	return BillingEarnType(in).Synonym()
}

var BillingEarnType_Lower_value = map[string]BillingEarnType{
	"billingearntypeunknown":    0,
	"billingearntypecommission": 1,
	"billingearntypefee":        2,
}

func ConvertStringToBillingEarnType(in string) BillingEarnType {
	if result, ok := BillingEarnType_value[in]; ok {
		return BillingEarnType(result)
	}

	if result, ok := BillingEarnType_Lower_value[strings.ToLower(in)]; ok {
		return BillingEarnType(result)
	}

	return BillingEarnType(math.MinInt32)
}

var SliceBillingEarnTypeConvert *sliceBillingEarnTypeConvert

type sliceBillingEarnTypeConvert struct{}

func (*sliceBillingEarnTypeConvert) Synonym(in []BillingEarnType) []BillingEarnType {
	result := make([]BillingEarnType, len(in))
	for i, v := range in {
		result[i] = v.Synonym()
	}

	return result
}

func (*sliceBillingEarnTypeConvert) Int32(in []BillingEarnType) []int32 {
	result := make([]int32, len(in))
	for i, v := range in {
		result[i] = v.Int32()
	}

	return result
}

func (*sliceBillingEarnTypeConvert) Uint32(in []BillingEarnType) []uint32 {
	result := make([]uint32, len(in))
	for i, v := range in {
		result[i] = v.Uint32()
	}

	return result
}

func (*sliceBillingEarnTypeConvert) Uint64(in []BillingEarnType) []uint64 {
	result := make([]uint64, len(in))
	for i, v := range in {
		result[i] = v.Uint64()
	}

	return result
}

func (*sliceBillingEarnTypeConvert) Int64(in []BillingEarnType) []int64 {
	result := make([]int64, len(in))
	for i, v := range in {
		result[i] = v.Int64()
	}

	return result
}

func (*sliceBillingEarnTypeConvert) Uint(in []BillingEarnType) []uint {
	result := make([]uint, len(in))
	for i, v := range in {
		result[i] = v.Uint()
	}

	return result
}

func (*sliceBillingEarnTypeConvert) Int(in []BillingEarnType) []int {
	result := make([]int, len(in))
	for i, v := range in {
		result[i] = v.Int()
	}

	return result
}

func (*sliceBillingEarnTypeConvert) IsKnown(in []BillingEarnType) []bool {
	result := make([]bool, len(in))
	for i, v := range in {
		result[i] = v.IsKnown()
	}

	return result
}

func (*sliceBillingEarnTypeConvert) ConvertIntToBillingEarnType(in []int) []BillingEarnType {
	result := make([]BillingEarnType, len(in))
	for i, v := range in {
		result[i] = ConvertIntToBillingEarnType(v)
	}

	return result
}

func (*sliceBillingEarnTypeConvert) ConvertUintToBillingEarnType(in []uint) []BillingEarnType {
	result := make([]BillingEarnType, len(in))
	for i, v := range in {
		result[i] = ConvertUintToBillingEarnType(v)
	}

	return result
}

func (*sliceBillingEarnTypeConvert) ConvertInt32ToBillingEarnType(in []int32) []BillingEarnType {
	result := make([]BillingEarnType, len(in))
	for i, v := range in {
		result[i] = ConvertInt32ToBillingEarnType(v)
	}

	return result
}

func (*sliceBillingEarnTypeConvert) ConvertUint32ToBillingEarnType(in []uint32) []BillingEarnType {
	result := make([]BillingEarnType, len(in))
	for i, v := range in {
		result[i] = ConvertUint32ToBillingEarnType(v)
	}

	return result
}

func (*sliceBillingEarnTypeConvert) ConvertInt64ToBillingEarnType(in []int64) []BillingEarnType {
	result := make([]BillingEarnType, len(in))
	for i, v := range in {
		result[i] = ConvertInt64ToBillingEarnType(v)
	}

	return result
}

func (*sliceBillingEarnTypeConvert) ConvertUint64ToBillingEarnType(in []uint64) []BillingEarnType {
	result := make([]BillingEarnType, len(in))
	for i, v := range in {
		result[i] = ConvertUint64ToBillingEarnType(v)
	}

	return result
}

func (*sliceBillingEarnTypeConvert) ConvertStringToBillingEarnType(in []string) []BillingEarnType {
	result := make([]BillingEarnType, len(in))
	for i, v := range in {
		result[i] = ConvertStringToBillingEarnType(v)
	}

	return result
}

func NewBillingEarnTypeUsage() *BillingEarnTypeUsage {
	return &BillingEarnTypeUsage{
		enumMap: map[BillingEarnType]bool{
			BillingEarnType_BillingEarnTypeUnknown:    false,
			BillingEarnType_BillingEarnTypeCommission: false,
			BillingEarnType_BillingEarnTypeFee:        false,
		},
	}
}

func IsBillingEarnType(target BillingEarnType, matches ...BillingEarnType) bool {
	for _, v := range matches {
		if target == v {
			return true
		}
	}

	return false
}

type BillingEarnTypeUsage struct {
	enumMap map[BillingEarnType]bool
}

func (u *BillingEarnTypeUsage) Use(slice ...BillingEarnType) *BillingEarnTypeUsage {
	for _, in := range slice {
		u.enumMap[in] = true
	}

	return u
}

func (u *BillingEarnTypeUsage) Check() []string {
	var result []string
	for k, v := range u.enumMap {
		if v {
			continue
		}

		result = append(result, k.String())
	}

	return result
}

func (u *BillingEarnTypeUsage) BillingEarnType_BillingEarnTypeUnknown() BillingEarnType {
	u.Use(BillingEarnType_BillingEarnTypeUnknown)
	return BillingEarnType_BillingEarnTypeUnknown
}

func (u *BillingEarnTypeUsage) BillingEarnType_BillingEarnTypeCommission() BillingEarnType {
	u.Use(BillingEarnType_BillingEarnTypeCommission)
	return BillingEarnType_BillingEarnTypeCommission
}

func (u *BillingEarnTypeUsage) BillingEarnType_BillingEarnTypeFee() BillingEarnType {
	u.Use(BillingEarnType_BillingEarnTypeFee)
	return BillingEarnType_BillingEarnTypeFee
}

func (val BillingBalanceOwnerType) Synonym() BillingBalanceOwnerType {
	if _, ok := BillingBalanceOwnerType_name[int32(val)]; ok {
		return val
	}

	return BillingBalanceOwnerType(math.MinInt32)
}

func (val BillingBalanceOwnerType) Int() int {
	return int(val.Synonym())
}

func (val BillingBalanceOwnerType) IntPtr() *int {
	result := int(val.Synonym())
	return &result
}

func (val BillingBalanceOwnerType) Int32() int32 {
	return int32(val.Synonym())
}

func (val BillingBalanceOwnerType) Int32Ptr() *int32 {
	result := int32(val.Synonym())
	return &result
}

func (val BillingBalanceOwnerType) Int64() int64 {
	return int64(val.Synonym())
}

func (val BillingBalanceOwnerType) Int64Ptr() *int64 {
	result := int64(val.Synonym())
	return &result
}

func (val BillingBalanceOwnerType) Uint() uint {
	return uint(val.Synonym())
}

func (val BillingBalanceOwnerType) UintPtr() *uint {
	result := uint(val.Synonym())
	return &result
}

func (val BillingBalanceOwnerType) Uint32() uint32 {
	return uint32(val.Synonym())
}

func (val BillingBalanceOwnerType) Uint32Ptr() *uint32 {
	result := uint32(val.Synonym())
	return &result
}

func (val BillingBalanceOwnerType) Uint64() uint64 {
	return uint64(val.Synonym())
}

func (val BillingBalanceOwnerType) Uint64Ptr() *uint64 {
	result := uint64(val.Synonym())
	return &result
}

func (val BillingBalanceOwnerType) IsKnown() bool {
	return val.Synonym() != BillingBalanceOwnerType(math.MinInt32)
}

func ConvertIntToBillingBalanceOwnerType(in int) BillingBalanceOwnerType {
	return BillingBalanceOwnerType(in).Synonym()
}

func ConvertUintToBillingBalanceOwnerType(in uint) BillingBalanceOwnerType {
	return BillingBalanceOwnerType(in).Synonym()
}

func ConvertInt32ToBillingBalanceOwnerType(in int32) BillingBalanceOwnerType {
	return BillingBalanceOwnerType(in).Synonym()
}

func ConvertUint32ToBillingBalanceOwnerType(in uint32) BillingBalanceOwnerType {
	return BillingBalanceOwnerType(in).Synonym()
}

func ConvertInt64ToBillingBalanceOwnerType(in int64) BillingBalanceOwnerType {
	return BillingBalanceOwnerType(in).Synonym()
}

func ConvertUint64ToBillingBalanceOwnerType(in uint64) BillingBalanceOwnerType {
	return BillingBalanceOwnerType(in).Synonym()
}

var BillingBalanceOwnerType_Lower_value = map[string]BillingBalanceOwnerType{
	"billingbalanceownertypeunknown":  0,
	"billingbalanceownertypesystem":   1,
	"billingbalanceownertypemerchant": 2,
	"billingbalanceownertypeproject":  3,
}

func ConvertStringToBillingBalanceOwnerType(in string) BillingBalanceOwnerType {
	if result, ok := BillingBalanceOwnerType_value[in]; ok {
		return BillingBalanceOwnerType(result)
	}

	if result, ok := BillingBalanceOwnerType_Lower_value[strings.ToLower(in)]; ok {
		return BillingBalanceOwnerType(result)
	}

	return BillingBalanceOwnerType(math.MinInt32)
}

var SliceBillingBalanceOwnerTypeConvert *sliceBillingBalanceOwnerTypeConvert

type sliceBillingBalanceOwnerTypeConvert struct{}

func (*sliceBillingBalanceOwnerTypeConvert) Synonym(in []BillingBalanceOwnerType) []BillingBalanceOwnerType {
	result := make([]BillingBalanceOwnerType, len(in))
	for i, v := range in {
		result[i] = v.Synonym()
	}

	return result
}

func (*sliceBillingBalanceOwnerTypeConvert) Int32(in []BillingBalanceOwnerType) []int32 {
	result := make([]int32, len(in))
	for i, v := range in {
		result[i] = v.Int32()
	}

	return result
}

func (*sliceBillingBalanceOwnerTypeConvert) Uint32(in []BillingBalanceOwnerType) []uint32 {
	result := make([]uint32, len(in))
	for i, v := range in {
		result[i] = v.Uint32()
	}

	return result
}

func (*sliceBillingBalanceOwnerTypeConvert) Uint64(in []BillingBalanceOwnerType) []uint64 {
	result := make([]uint64, len(in))
	for i, v := range in {
		result[i] = v.Uint64()
	}

	return result
}

func (*sliceBillingBalanceOwnerTypeConvert) Int64(in []BillingBalanceOwnerType) []int64 {
	result := make([]int64, len(in))
	for i, v := range in {
		result[i] = v.Int64()
	}

	return result
}

func (*sliceBillingBalanceOwnerTypeConvert) Uint(in []BillingBalanceOwnerType) []uint {
	result := make([]uint, len(in))
	for i, v := range in {
		result[i] = v.Uint()
	}

	return result
}

func (*sliceBillingBalanceOwnerTypeConvert) Int(in []BillingBalanceOwnerType) []int {
	result := make([]int, len(in))
	for i, v := range in {
		result[i] = v.Int()
	}

	return result
}

func (*sliceBillingBalanceOwnerTypeConvert) IsKnown(in []BillingBalanceOwnerType) []bool {
	result := make([]bool, len(in))
	for i, v := range in {
		result[i] = v.IsKnown()
	}

	return result
}

func (*sliceBillingBalanceOwnerTypeConvert) ConvertIntToBillingBalanceOwnerType(in []int) []BillingBalanceOwnerType {
	result := make([]BillingBalanceOwnerType, len(in))
	for i, v := range in {
		result[i] = ConvertIntToBillingBalanceOwnerType(v)
	}

	return result
}

func (*sliceBillingBalanceOwnerTypeConvert) ConvertUintToBillingBalanceOwnerType(in []uint) []BillingBalanceOwnerType {
	result := make([]BillingBalanceOwnerType, len(in))
	for i, v := range in {
		result[i] = ConvertUintToBillingBalanceOwnerType(v)
	}

	return result
}

func (*sliceBillingBalanceOwnerTypeConvert) ConvertInt32ToBillingBalanceOwnerType(in []int32) []BillingBalanceOwnerType {
	result := make([]BillingBalanceOwnerType, len(in))
	for i, v := range in {
		result[i] = ConvertInt32ToBillingBalanceOwnerType(v)
	}

	return result
}

func (*sliceBillingBalanceOwnerTypeConvert) ConvertUint32ToBillingBalanceOwnerType(in []uint32) []BillingBalanceOwnerType {
	result := make([]BillingBalanceOwnerType, len(in))
	for i, v := range in {
		result[i] = ConvertUint32ToBillingBalanceOwnerType(v)
	}

	return result
}

func (*sliceBillingBalanceOwnerTypeConvert) ConvertInt64ToBillingBalanceOwnerType(in []int64) []BillingBalanceOwnerType {
	result := make([]BillingBalanceOwnerType, len(in))
	for i, v := range in {
		result[i] = ConvertInt64ToBillingBalanceOwnerType(v)
	}

	return result
}

func (*sliceBillingBalanceOwnerTypeConvert) ConvertUint64ToBillingBalanceOwnerType(in []uint64) []BillingBalanceOwnerType {
	result := make([]BillingBalanceOwnerType, len(in))
	for i, v := range in {
		result[i] = ConvertUint64ToBillingBalanceOwnerType(v)
	}

	return result
}

func (*sliceBillingBalanceOwnerTypeConvert) ConvertStringToBillingBalanceOwnerType(in []string) []BillingBalanceOwnerType {
	result := make([]BillingBalanceOwnerType, len(in))
	for i, v := range in {
		result[i] = ConvertStringToBillingBalanceOwnerType(v)
	}

	return result
}

func NewBillingBalanceOwnerTypeUsage() *BillingBalanceOwnerTypeUsage {
	return &BillingBalanceOwnerTypeUsage{
		enumMap: map[BillingBalanceOwnerType]bool{
			BillingBalanceOwnerType_BillingBalanceOwnerTypeUnknown:  false,
			BillingBalanceOwnerType_BillingBalanceOwnerTypeSystem:   false,
			BillingBalanceOwnerType_BillingBalanceOwnerTypeMerchant: false,
			BillingBalanceOwnerType_BillingBalanceOwnerTypeProject:  false,
		},
	}
}

func IsBillingBalanceOwnerType(target BillingBalanceOwnerType, matches ...BillingBalanceOwnerType) bool {
	for _, v := range matches {
		if target == v {
			return true
		}
	}

	return false
}

type BillingBalanceOwnerTypeUsage struct {
	enumMap map[BillingBalanceOwnerType]bool
}

func (u *BillingBalanceOwnerTypeUsage) Use(slice ...BillingBalanceOwnerType) *BillingBalanceOwnerTypeUsage {
	for _, in := range slice {
		u.enumMap[in] = true
	}

	return u
}

func (u *BillingBalanceOwnerTypeUsage) Check() []string {
	var result []string
	for k, v := range u.enumMap {
		if v {
			continue
		}

		result = append(result, k.String())
	}

	return result
}

func (u *BillingBalanceOwnerTypeUsage) BillingBalanceOwnerType_BillingBalanceOwnerTypeUnknown() BillingBalanceOwnerType {
	u.Use(BillingBalanceOwnerType_BillingBalanceOwnerTypeUnknown)
	return BillingBalanceOwnerType_BillingBalanceOwnerTypeUnknown
}

func (u *BillingBalanceOwnerTypeUsage) BillingBalanceOwnerType_BillingBalanceOwnerTypeSystem() BillingBalanceOwnerType {
	u.Use(BillingBalanceOwnerType_BillingBalanceOwnerTypeSystem)
	return BillingBalanceOwnerType_BillingBalanceOwnerTypeSystem
}

func (u *BillingBalanceOwnerTypeUsage) BillingBalanceOwnerType_BillingBalanceOwnerTypeMerchant() BillingBalanceOwnerType {
	u.Use(BillingBalanceOwnerType_BillingBalanceOwnerTypeMerchant)
	return BillingBalanceOwnerType_BillingBalanceOwnerTypeMerchant
}

func (u *BillingBalanceOwnerTypeUsage) BillingBalanceOwnerType_BillingBalanceOwnerTypeProject() BillingBalanceOwnerType {
	u.Use(BillingBalanceOwnerType_BillingBalanceOwnerTypeProject)
	return BillingBalanceOwnerType_BillingBalanceOwnerTypeProject
}

func (val BillingBalanceOwnerStatus) Synonym() BillingBalanceOwnerStatus {
	if _, ok := BillingBalanceOwnerStatus_name[int32(val)]; ok {
		return val
	}

	return BillingBalanceOwnerStatus(math.MinInt32)
}

func (val BillingBalanceOwnerStatus) Int() int {
	return int(val.Synonym())
}

func (val BillingBalanceOwnerStatus) IntPtr() *int {
	result := int(val.Synonym())
	return &result
}

func (val BillingBalanceOwnerStatus) Int32() int32 {
	return int32(val.Synonym())
}

func (val BillingBalanceOwnerStatus) Int32Ptr() *int32 {
	result := int32(val.Synonym())
	return &result
}

func (val BillingBalanceOwnerStatus) Int64() int64 {
	return int64(val.Synonym())
}

func (val BillingBalanceOwnerStatus) Int64Ptr() *int64 {
	result := int64(val.Synonym())
	return &result
}

func (val BillingBalanceOwnerStatus) Uint() uint {
	return uint(val.Synonym())
}

func (val BillingBalanceOwnerStatus) UintPtr() *uint {
	result := uint(val.Synonym())
	return &result
}

func (val BillingBalanceOwnerStatus) Uint32() uint32 {
	return uint32(val.Synonym())
}

func (val BillingBalanceOwnerStatus) Uint32Ptr() *uint32 {
	result := uint32(val.Synonym())
	return &result
}

func (val BillingBalanceOwnerStatus) Uint64() uint64 {
	return uint64(val.Synonym())
}

func (val BillingBalanceOwnerStatus) Uint64Ptr() *uint64 {
	result := uint64(val.Synonym())
	return &result
}

func (val BillingBalanceOwnerStatus) IsKnown() bool {
	return val.Synonym() != BillingBalanceOwnerStatus(math.MinInt32)
}

func ConvertIntToBillingBalanceOwnerStatus(in int) BillingBalanceOwnerStatus {
	return BillingBalanceOwnerStatus(in).Synonym()
}

func ConvertUintToBillingBalanceOwnerStatus(in uint) BillingBalanceOwnerStatus {
	return BillingBalanceOwnerStatus(in).Synonym()
}

func ConvertInt32ToBillingBalanceOwnerStatus(in int32) BillingBalanceOwnerStatus {
	return BillingBalanceOwnerStatus(in).Synonym()
}

func ConvertUint32ToBillingBalanceOwnerStatus(in uint32) BillingBalanceOwnerStatus {
	return BillingBalanceOwnerStatus(in).Synonym()
}

func ConvertInt64ToBillingBalanceOwnerStatus(in int64) BillingBalanceOwnerStatus {
	return BillingBalanceOwnerStatus(in).Synonym()
}

func ConvertUint64ToBillingBalanceOwnerStatus(in uint64) BillingBalanceOwnerStatus {
	return BillingBalanceOwnerStatus(in).Synonym()
}

var BillingBalanceOwnerStatus_Lower_value = map[string]BillingBalanceOwnerStatus{
	"billingbalanceownerstatusunknown":  0,
	"billingbalanceownerstatusactive":   1,
	"billingbalanceownerstatusinactive": 2,
}

func ConvertStringToBillingBalanceOwnerStatus(in string) BillingBalanceOwnerStatus {
	if result, ok := BillingBalanceOwnerStatus_value[in]; ok {
		return BillingBalanceOwnerStatus(result)
	}

	if result, ok := BillingBalanceOwnerStatus_Lower_value[strings.ToLower(in)]; ok {
		return BillingBalanceOwnerStatus(result)
	}

	return BillingBalanceOwnerStatus(math.MinInt32)
}

var SliceBillingBalanceOwnerStatusConvert *sliceBillingBalanceOwnerStatusConvert

type sliceBillingBalanceOwnerStatusConvert struct{}

func (*sliceBillingBalanceOwnerStatusConvert) Synonym(in []BillingBalanceOwnerStatus) []BillingBalanceOwnerStatus {
	result := make([]BillingBalanceOwnerStatus, len(in))
	for i, v := range in {
		result[i] = v.Synonym()
	}

	return result
}

func (*sliceBillingBalanceOwnerStatusConvert) Int32(in []BillingBalanceOwnerStatus) []int32 {
	result := make([]int32, len(in))
	for i, v := range in {
		result[i] = v.Int32()
	}

	return result
}

func (*sliceBillingBalanceOwnerStatusConvert) Uint32(in []BillingBalanceOwnerStatus) []uint32 {
	result := make([]uint32, len(in))
	for i, v := range in {
		result[i] = v.Uint32()
	}

	return result
}

func (*sliceBillingBalanceOwnerStatusConvert) Uint64(in []BillingBalanceOwnerStatus) []uint64 {
	result := make([]uint64, len(in))
	for i, v := range in {
		result[i] = v.Uint64()
	}

	return result
}

func (*sliceBillingBalanceOwnerStatusConvert) Int64(in []BillingBalanceOwnerStatus) []int64 {
	result := make([]int64, len(in))
	for i, v := range in {
		result[i] = v.Int64()
	}

	return result
}

func (*sliceBillingBalanceOwnerStatusConvert) Uint(in []BillingBalanceOwnerStatus) []uint {
	result := make([]uint, len(in))
	for i, v := range in {
		result[i] = v.Uint()
	}

	return result
}

func (*sliceBillingBalanceOwnerStatusConvert) Int(in []BillingBalanceOwnerStatus) []int {
	result := make([]int, len(in))
	for i, v := range in {
		result[i] = v.Int()
	}

	return result
}

func (*sliceBillingBalanceOwnerStatusConvert) IsKnown(in []BillingBalanceOwnerStatus) []bool {
	result := make([]bool, len(in))
	for i, v := range in {
		result[i] = v.IsKnown()
	}

	return result
}

func (*sliceBillingBalanceOwnerStatusConvert) ConvertIntToBillingBalanceOwnerStatus(in []int) []BillingBalanceOwnerStatus {
	result := make([]BillingBalanceOwnerStatus, len(in))
	for i, v := range in {
		result[i] = ConvertIntToBillingBalanceOwnerStatus(v)
	}

	return result
}

func (*sliceBillingBalanceOwnerStatusConvert) ConvertUintToBillingBalanceOwnerStatus(in []uint) []BillingBalanceOwnerStatus {
	result := make([]BillingBalanceOwnerStatus, len(in))
	for i, v := range in {
		result[i] = ConvertUintToBillingBalanceOwnerStatus(v)
	}

	return result
}

func (*sliceBillingBalanceOwnerStatusConvert) ConvertInt32ToBillingBalanceOwnerStatus(in []int32) []BillingBalanceOwnerStatus {
	result := make([]BillingBalanceOwnerStatus, len(in))
	for i, v := range in {
		result[i] = ConvertInt32ToBillingBalanceOwnerStatus(v)
	}

	return result
}

func (*sliceBillingBalanceOwnerStatusConvert) ConvertUint32ToBillingBalanceOwnerStatus(in []uint32) []BillingBalanceOwnerStatus {
	result := make([]BillingBalanceOwnerStatus, len(in))
	for i, v := range in {
		result[i] = ConvertUint32ToBillingBalanceOwnerStatus(v)
	}

	return result
}

func (*sliceBillingBalanceOwnerStatusConvert) ConvertInt64ToBillingBalanceOwnerStatus(in []int64) []BillingBalanceOwnerStatus {
	result := make([]BillingBalanceOwnerStatus, len(in))
	for i, v := range in {
		result[i] = ConvertInt64ToBillingBalanceOwnerStatus(v)
	}

	return result
}

func (*sliceBillingBalanceOwnerStatusConvert) ConvertUint64ToBillingBalanceOwnerStatus(in []uint64) []BillingBalanceOwnerStatus {
	result := make([]BillingBalanceOwnerStatus, len(in))
	for i, v := range in {
		result[i] = ConvertUint64ToBillingBalanceOwnerStatus(v)
	}

	return result
}

func (*sliceBillingBalanceOwnerStatusConvert) ConvertStringToBillingBalanceOwnerStatus(in []string) []BillingBalanceOwnerStatus {
	result := make([]BillingBalanceOwnerStatus, len(in))
	for i, v := range in {
		result[i] = ConvertStringToBillingBalanceOwnerStatus(v)
	}

	return result
}

func NewBillingBalanceOwnerStatusUsage() *BillingBalanceOwnerStatusUsage {
	return &BillingBalanceOwnerStatusUsage{
		enumMap: map[BillingBalanceOwnerStatus]bool{
			BillingBalanceOwnerStatus_BillingBalanceOwnerStatusUnknown:  false,
			BillingBalanceOwnerStatus_BillingBalanceOwnerStatusActive:   false,
			BillingBalanceOwnerStatus_BillingBalanceOwnerStatusInactive: false,
		},
	}
}

func IsBillingBalanceOwnerStatus(target BillingBalanceOwnerStatus, matches ...BillingBalanceOwnerStatus) bool {
	for _, v := range matches {
		if target == v {
			return true
		}
	}

	return false
}

type BillingBalanceOwnerStatusUsage struct {
	enumMap map[BillingBalanceOwnerStatus]bool
}

func (u *BillingBalanceOwnerStatusUsage) Use(slice ...BillingBalanceOwnerStatus) *BillingBalanceOwnerStatusUsage {
	for _, in := range slice {
		u.enumMap[in] = true
	}

	return u
}

func (u *BillingBalanceOwnerStatusUsage) Check() []string {
	var result []string
	for k, v := range u.enumMap {
		if v {
			continue
		}

		result = append(result, k.String())
	}

	return result
}

func (u *BillingBalanceOwnerStatusUsage) BillingBalanceOwnerStatus_BillingBalanceOwnerStatusUnknown() BillingBalanceOwnerStatus {
	u.Use(BillingBalanceOwnerStatus_BillingBalanceOwnerStatusUnknown)
	return BillingBalanceOwnerStatus_BillingBalanceOwnerStatusUnknown
}

func (u *BillingBalanceOwnerStatusUsage) BillingBalanceOwnerStatus_BillingBalanceOwnerStatusActive() BillingBalanceOwnerStatus {
	u.Use(BillingBalanceOwnerStatus_BillingBalanceOwnerStatusActive)
	return BillingBalanceOwnerStatus_BillingBalanceOwnerStatusActive
}

func (u *BillingBalanceOwnerStatusUsage) BillingBalanceOwnerStatus_BillingBalanceOwnerStatusInactive() BillingBalanceOwnerStatus {
	u.Use(BillingBalanceOwnerStatus_BillingBalanceOwnerStatusInactive)
	return BillingBalanceOwnerStatus_BillingBalanceOwnerStatusInactive
}

func (val BillingBalanceCreditStatus) Synonym() BillingBalanceCreditStatus {
	if _, ok := BillingBalanceCreditStatus_name[int32(val)]; ok {
		return val
	}

	return BillingBalanceCreditStatus(math.MinInt32)
}

func (val BillingBalanceCreditStatus) Int() int {
	return int(val.Synonym())
}

func (val BillingBalanceCreditStatus) IntPtr() *int {
	result := int(val.Synonym())
	return &result
}

func (val BillingBalanceCreditStatus) Int32() int32 {
	return int32(val.Synonym())
}

func (val BillingBalanceCreditStatus) Int32Ptr() *int32 {
	result := int32(val.Synonym())
	return &result
}

func (val BillingBalanceCreditStatus) Int64() int64 {
	return int64(val.Synonym())
}

func (val BillingBalanceCreditStatus) Int64Ptr() *int64 {
	result := int64(val.Synonym())
	return &result
}

func (val BillingBalanceCreditStatus) Uint() uint {
	return uint(val.Synonym())
}

func (val BillingBalanceCreditStatus) UintPtr() *uint {
	result := uint(val.Synonym())
	return &result
}

func (val BillingBalanceCreditStatus) Uint32() uint32 {
	return uint32(val.Synonym())
}

func (val BillingBalanceCreditStatus) Uint32Ptr() *uint32 {
	result := uint32(val.Synonym())
	return &result
}

func (val BillingBalanceCreditStatus) Uint64() uint64 {
	return uint64(val.Synonym())
}

func (val BillingBalanceCreditStatus) Uint64Ptr() *uint64 {
	result := uint64(val.Synonym())
	return &result
}

func (val BillingBalanceCreditStatus) IsKnown() bool {
	return val.Synonym() != BillingBalanceCreditStatus(math.MinInt32)
}

func ConvertIntToBillingBalanceCreditStatus(in int) BillingBalanceCreditStatus {
	return BillingBalanceCreditStatus(in).Synonym()
}

func ConvertUintToBillingBalanceCreditStatus(in uint) BillingBalanceCreditStatus {
	return BillingBalanceCreditStatus(in).Synonym()
}

func ConvertInt32ToBillingBalanceCreditStatus(in int32) BillingBalanceCreditStatus {
	return BillingBalanceCreditStatus(in).Synonym()
}

func ConvertUint32ToBillingBalanceCreditStatus(in uint32) BillingBalanceCreditStatus {
	return BillingBalanceCreditStatus(in).Synonym()
}

func ConvertInt64ToBillingBalanceCreditStatus(in int64) BillingBalanceCreditStatus {
	return BillingBalanceCreditStatus(in).Synonym()
}

func ConvertUint64ToBillingBalanceCreditStatus(in uint64) BillingBalanceCreditStatus {
	return BillingBalanceCreditStatus(in).Synonym()
}

var BillingBalanceCreditStatus_Lower_value = map[string]BillingBalanceCreditStatus{
	"billingbalancecreditstatusunknown":  0,
	"billingbalancecreditstatusactive":   1,
	"billingbalancecreditstatusinactive": 2,
	"billingbalancecreditstatusidle":     3,
}

func ConvertStringToBillingBalanceCreditStatus(in string) BillingBalanceCreditStatus {
	if result, ok := BillingBalanceCreditStatus_value[in]; ok {
		return BillingBalanceCreditStatus(result)
	}

	if result, ok := BillingBalanceCreditStatus_Lower_value[strings.ToLower(in)]; ok {
		return BillingBalanceCreditStatus(result)
	}

	return BillingBalanceCreditStatus(math.MinInt32)
}

var SliceBillingBalanceCreditStatusConvert *sliceBillingBalanceCreditStatusConvert

type sliceBillingBalanceCreditStatusConvert struct{}

func (*sliceBillingBalanceCreditStatusConvert) Synonym(in []BillingBalanceCreditStatus) []BillingBalanceCreditStatus {
	result := make([]BillingBalanceCreditStatus, len(in))
	for i, v := range in {
		result[i] = v.Synonym()
	}

	return result
}

func (*sliceBillingBalanceCreditStatusConvert) Int32(in []BillingBalanceCreditStatus) []int32 {
	result := make([]int32, len(in))
	for i, v := range in {
		result[i] = v.Int32()
	}

	return result
}

func (*sliceBillingBalanceCreditStatusConvert) Uint32(in []BillingBalanceCreditStatus) []uint32 {
	result := make([]uint32, len(in))
	for i, v := range in {
		result[i] = v.Uint32()
	}

	return result
}

func (*sliceBillingBalanceCreditStatusConvert) Uint64(in []BillingBalanceCreditStatus) []uint64 {
	result := make([]uint64, len(in))
	for i, v := range in {
		result[i] = v.Uint64()
	}

	return result
}

func (*sliceBillingBalanceCreditStatusConvert) Int64(in []BillingBalanceCreditStatus) []int64 {
	result := make([]int64, len(in))
	for i, v := range in {
		result[i] = v.Int64()
	}

	return result
}

func (*sliceBillingBalanceCreditStatusConvert) Uint(in []BillingBalanceCreditStatus) []uint {
	result := make([]uint, len(in))
	for i, v := range in {
		result[i] = v.Uint()
	}

	return result
}

func (*sliceBillingBalanceCreditStatusConvert) Int(in []BillingBalanceCreditStatus) []int {
	result := make([]int, len(in))
	for i, v := range in {
		result[i] = v.Int()
	}

	return result
}

func (*sliceBillingBalanceCreditStatusConvert) IsKnown(in []BillingBalanceCreditStatus) []bool {
	result := make([]bool, len(in))
	for i, v := range in {
		result[i] = v.IsKnown()
	}

	return result
}

func (*sliceBillingBalanceCreditStatusConvert) ConvertIntToBillingBalanceCreditStatus(in []int) []BillingBalanceCreditStatus {
	result := make([]BillingBalanceCreditStatus, len(in))
	for i, v := range in {
		result[i] = ConvertIntToBillingBalanceCreditStatus(v)
	}

	return result
}

func (*sliceBillingBalanceCreditStatusConvert) ConvertUintToBillingBalanceCreditStatus(in []uint) []BillingBalanceCreditStatus {
	result := make([]BillingBalanceCreditStatus, len(in))
	for i, v := range in {
		result[i] = ConvertUintToBillingBalanceCreditStatus(v)
	}

	return result
}

func (*sliceBillingBalanceCreditStatusConvert) ConvertInt32ToBillingBalanceCreditStatus(in []int32) []BillingBalanceCreditStatus {
	result := make([]BillingBalanceCreditStatus, len(in))
	for i, v := range in {
		result[i] = ConvertInt32ToBillingBalanceCreditStatus(v)
	}

	return result
}

func (*sliceBillingBalanceCreditStatusConvert) ConvertUint32ToBillingBalanceCreditStatus(in []uint32) []BillingBalanceCreditStatus {
	result := make([]BillingBalanceCreditStatus, len(in))
	for i, v := range in {
		result[i] = ConvertUint32ToBillingBalanceCreditStatus(v)
	}

	return result
}

func (*sliceBillingBalanceCreditStatusConvert) ConvertInt64ToBillingBalanceCreditStatus(in []int64) []BillingBalanceCreditStatus {
	result := make([]BillingBalanceCreditStatus, len(in))
	for i, v := range in {
		result[i] = ConvertInt64ToBillingBalanceCreditStatus(v)
	}

	return result
}

func (*sliceBillingBalanceCreditStatusConvert) ConvertUint64ToBillingBalanceCreditStatus(in []uint64) []BillingBalanceCreditStatus {
	result := make([]BillingBalanceCreditStatus, len(in))
	for i, v := range in {
		result[i] = ConvertUint64ToBillingBalanceCreditStatus(v)
	}

	return result
}

func (*sliceBillingBalanceCreditStatusConvert) ConvertStringToBillingBalanceCreditStatus(in []string) []BillingBalanceCreditStatus {
	result := make([]BillingBalanceCreditStatus, len(in))
	for i, v := range in {
		result[i] = ConvertStringToBillingBalanceCreditStatus(v)
	}

	return result
}

func NewBillingBalanceCreditStatusUsage() *BillingBalanceCreditStatusUsage {
	return &BillingBalanceCreditStatusUsage{
		enumMap: map[BillingBalanceCreditStatus]bool{
			BillingBalanceCreditStatus_BillingBalanceCreditStatusUnknown:  false,
			BillingBalanceCreditStatus_BillingBalanceCreditStatusActive:   false,
			BillingBalanceCreditStatus_BillingBalanceCreditStatusInactive: false,
			BillingBalanceCreditStatus_BillingBalanceCreditStatusIdle:     false,
		},
	}
}

func IsBillingBalanceCreditStatus(target BillingBalanceCreditStatus, matches ...BillingBalanceCreditStatus) bool {
	for _, v := range matches {
		if target == v {
			return true
		}
	}

	return false
}

type BillingBalanceCreditStatusUsage struct {
	enumMap map[BillingBalanceCreditStatus]bool
}

func (u *BillingBalanceCreditStatusUsage) Use(slice ...BillingBalanceCreditStatus) *BillingBalanceCreditStatusUsage {
	for _, in := range slice {
		u.enumMap[in] = true
	}

	return u
}

func (u *BillingBalanceCreditStatusUsage) Check() []string {
	var result []string
	for k, v := range u.enumMap {
		if v {
			continue
		}

		result = append(result, k.String())
	}

	return result
}

func (u *BillingBalanceCreditStatusUsage) BillingBalanceCreditStatus_BillingBalanceCreditStatusUnknown() BillingBalanceCreditStatus {
	u.Use(BillingBalanceCreditStatus_BillingBalanceCreditStatusUnknown)
	return BillingBalanceCreditStatus_BillingBalanceCreditStatusUnknown
}

func (u *BillingBalanceCreditStatusUsage) BillingBalanceCreditStatus_BillingBalanceCreditStatusActive() BillingBalanceCreditStatus {
	u.Use(BillingBalanceCreditStatus_BillingBalanceCreditStatusActive)
	return BillingBalanceCreditStatus_BillingBalanceCreditStatusActive
}

func (u *BillingBalanceCreditStatusUsage) BillingBalanceCreditStatus_BillingBalanceCreditStatusInactive() BillingBalanceCreditStatus {
	u.Use(BillingBalanceCreditStatus_BillingBalanceCreditStatusInactive)
	return BillingBalanceCreditStatus_BillingBalanceCreditStatusInactive
}

func (u *BillingBalanceCreditStatusUsage) BillingBalanceCreditStatus_BillingBalanceCreditStatusIdle() BillingBalanceCreditStatus {
	u.Use(BillingBalanceCreditStatus_BillingBalanceCreditStatusIdle)
	return BillingBalanceCreditStatus_BillingBalanceCreditStatusIdle
}

func (val BillingBalanceAccountStatus) Synonym() BillingBalanceAccountStatus {
	if _, ok := BillingBalanceAccountStatus_name[int32(val)]; ok {
		return val
	}

	return BillingBalanceAccountStatus(math.MinInt32)
}

func (val BillingBalanceAccountStatus) Int() int {
	return int(val.Synonym())
}

func (val BillingBalanceAccountStatus) IntPtr() *int {
	result := int(val.Synonym())
	return &result
}

func (val BillingBalanceAccountStatus) Int32() int32 {
	return int32(val.Synonym())
}

func (val BillingBalanceAccountStatus) Int32Ptr() *int32 {
	result := int32(val.Synonym())
	return &result
}

func (val BillingBalanceAccountStatus) Int64() int64 {
	return int64(val.Synonym())
}

func (val BillingBalanceAccountStatus) Int64Ptr() *int64 {
	result := int64(val.Synonym())
	return &result
}

func (val BillingBalanceAccountStatus) Uint() uint {
	return uint(val.Synonym())
}

func (val BillingBalanceAccountStatus) UintPtr() *uint {
	result := uint(val.Synonym())
	return &result
}

func (val BillingBalanceAccountStatus) Uint32() uint32 {
	return uint32(val.Synonym())
}

func (val BillingBalanceAccountStatus) Uint32Ptr() *uint32 {
	result := uint32(val.Synonym())
	return &result
}

func (val BillingBalanceAccountStatus) Uint64() uint64 {
	return uint64(val.Synonym())
}

func (val BillingBalanceAccountStatus) Uint64Ptr() *uint64 {
	result := uint64(val.Synonym())
	return &result
}

func (val BillingBalanceAccountStatus) IsKnown() bool {
	return val.Synonym() != BillingBalanceAccountStatus(math.MinInt32)
}

func ConvertIntToBillingBalanceAccountStatus(in int) BillingBalanceAccountStatus {
	return BillingBalanceAccountStatus(in).Synonym()
}

func ConvertUintToBillingBalanceAccountStatus(in uint) BillingBalanceAccountStatus {
	return BillingBalanceAccountStatus(in).Synonym()
}

func ConvertInt32ToBillingBalanceAccountStatus(in int32) BillingBalanceAccountStatus {
	return BillingBalanceAccountStatus(in).Synonym()
}

func ConvertUint32ToBillingBalanceAccountStatus(in uint32) BillingBalanceAccountStatus {
	return BillingBalanceAccountStatus(in).Synonym()
}

func ConvertInt64ToBillingBalanceAccountStatus(in int64) BillingBalanceAccountStatus {
	return BillingBalanceAccountStatus(in).Synonym()
}

func ConvertUint64ToBillingBalanceAccountStatus(in uint64) BillingBalanceAccountStatus {
	return BillingBalanceAccountStatus(in).Synonym()
}

var BillingBalanceAccountStatus_Lower_value = map[string]BillingBalanceAccountStatus{
	"billingbalanceaccountstatusunknown":  0,
	"billingbalanceaccountstatusactive":   1,
	"billingbalanceaccountstatusinactive": 2,
}

func ConvertStringToBillingBalanceAccountStatus(in string) BillingBalanceAccountStatus {
	if result, ok := BillingBalanceAccountStatus_value[in]; ok {
		return BillingBalanceAccountStatus(result)
	}

	if result, ok := BillingBalanceAccountStatus_Lower_value[strings.ToLower(in)]; ok {
		return BillingBalanceAccountStatus(result)
	}

	return BillingBalanceAccountStatus(math.MinInt32)
}

var SliceBillingBalanceAccountStatusConvert *sliceBillingBalanceAccountStatusConvert

type sliceBillingBalanceAccountStatusConvert struct{}

func (*sliceBillingBalanceAccountStatusConvert) Synonym(in []BillingBalanceAccountStatus) []BillingBalanceAccountStatus {
	result := make([]BillingBalanceAccountStatus, len(in))
	for i, v := range in {
		result[i] = v.Synonym()
	}

	return result
}

func (*sliceBillingBalanceAccountStatusConvert) Int32(in []BillingBalanceAccountStatus) []int32 {
	result := make([]int32, len(in))
	for i, v := range in {
		result[i] = v.Int32()
	}

	return result
}

func (*sliceBillingBalanceAccountStatusConvert) Uint32(in []BillingBalanceAccountStatus) []uint32 {
	result := make([]uint32, len(in))
	for i, v := range in {
		result[i] = v.Uint32()
	}

	return result
}

func (*sliceBillingBalanceAccountStatusConvert) Uint64(in []BillingBalanceAccountStatus) []uint64 {
	result := make([]uint64, len(in))
	for i, v := range in {
		result[i] = v.Uint64()
	}

	return result
}

func (*sliceBillingBalanceAccountStatusConvert) Int64(in []BillingBalanceAccountStatus) []int64 {
	result := make([]int64, len(in))
	for i, v := range in {
		result[i] = v.Int64()
	}

	return result
}

func (*sliceBillingBalanceAccountStatusConvert) Uint(in []BillingBalanceAccountStatus) []uint {
	result := make([]uint, len(in))
	for i, v := range in {
		result[i] = v.Uint()
	}

	return result
}

func (*sliceBillingBalanceAccountStatusConvert) Int(in []BillingBalanceAccountStatus) []int {
	result := make([]int, len(in))
	for i, v := range in {
		result[i] = v.Int()
	}

	return result
}

func (*sliceBillingBalanceAccountStatusConvert) IsKnown(in []BillingBalanceAccountStatus) []bool {
	result := make([]bool, len(in))
	for i, v := range in {
		result[i] = v.IsKnown()
	}

	return result
}

func (*sliceBillingBalanceAccountStatusConvert) ConvertIntToBillingBalanceAccountStatus(in []int) []BillingBalanceAccountStatus {
	result := make([]BillingBalanceAccountStatus, len(in))
	for i, v := range in {
		result[i] = ConvertIntToBillingBalanceAccountStatus(v)
	}

	return result
}

func (*sliceBillingBalanceAccountStatusConvert) ConvertUintToBillingBalanceAccountStatus(in []uint) []BillingBalanceAccountStatus {
	result := make([]BillingBalanceAccountStatus, len(in))
	for i, v := range in {
		result[i] = ConvertUintToBillingBalanceAccountStatus(v)
	}

	return result
}

func (*sliceBillingBalanceAccountStatusConvert) ConvertInt32ToBillingBalanceAccountStatus(in []int32) []BillingBalanceAccountStatus {
	result := make([]BillingBalanceAccountStatus, len(in))
	for i, v := range in {
		result[i] = ConvertInt32ToBillingBalanceAccountStatus(v)
	}

	return result
}

func (*sliceBillingBalanceAccountStatusConvert) ConvertUint32ToBillingBalanceAccountStatus(in []uint32) []BillingBalanceAccountStatus {
	result := make([]BillingBalanceAccountStatus, len(in))
	for i, v := range in {
		result[i] = ConvertUint32ToBillingBalanceAccountStatus(v)
	}

	return result
}

func (*sliceBillingBalanceAccountStatusConvert) ConvertInt64ToBillingBalanceAccountStatus(in []int64) []BillingBalanceAccountStatus {
	result := make([]BillingBalanceAccountStatus, len(in))
	for i, v := range in {
		result[i] = ConvertInt64ToBillingBalanceAccountStatus(v)
	}

	return result
}

func (*sliceBillingBalanceAccountStatusConvert) ConvertUint64ToBillingBalanceAccountStatus(in []uint64) []BillingBalanceAccountStatus {
	result := make([]BillingBalanceAccountStatus, len(in))
	for i, v := range in {
		result[i] = ConvertUint64ToBillingBalanceAccountStatus(v)
	}

	return result
}

func (*sliceBillingBalanceAccountStatusConvert) ConvertStringToBillingBalanceAccountStatus(in []string) []BillingBalanceAccountStatus {
	result := make([]BillingBalanceAccountStatus, len(in))
	for i, v := range in {
		result[i] = ConvertStringToBillingBalanceAccountStatus(v)
	}

	return result
}

func NewBillingBalanceAccountStatusUsage() *BillingBalanceAccountStatusUsage {
	return &BillingBalanceAccountStatusUsage{
		enumMap: map[BillingBalanceAccountStatus]bool{
			BillingBalanceAccountStatus_BillingBalanceAccountStatusUnknown:  false,
			BillingBalanceAccountStatus_BillingBalanceAccountStatusActive:   false,
			BillingBalanceAccountStatus_BillingBalanceAccountStatusInactive: false,
		},
	}
}

func IsBillingBalanceAccountStatus(target BillingBalanceAccountStatus, matches ...BillingBalanceAccountStatus) bool {
	for _, v := range matches {
		if target == v {
			return true
		}
	}

	return false
}

type BillingBalanceAccountStatusUsage struct {
	enumMap map[BillingBalanceAccountStatus]bool
}

func (u *BillingBalanceAccountStatusUsage) Use(slice ...BillingBalanceAccountStatus) *BillingBalanceAccountStatusUsage {
	for _, in := range slice {
		u.enumMap[in] = true
	}

	return u
}

func (u *BillingBalanceAccountStatusUsage) Check() []string {
	var result []string
	for k, v := range u.enumMap {
		if v {
			continue
		}

		result = append(result, k.String())
	}

	return result
}

func (u *BillingBalanceAccountStatusUsage) BillingBalanceAccountStatus_BillingBalanceAccountStatusUnknown() BillingBalanceAccountStatus {
	u.Use(BillingBalanceAccountStatus_BillingBalanceAccountStatusUnknown)
	return BillingBalanceAccountStatus_BillingBalanceAccountStatusUnknown
}

func (u *BillingBalanceAccountStatusUsage) BillingBalanceAccountStatus_BillingBalanceAccountStatusActive() BillingBalanceAccountStatus {
	u.Use(BillingBalanceAccountStatus_BillingBalanceAccountStatusActive)
	return BillingBalanceAccountStatus_BillingBalanceAccountStatusActive
}

func (u *BillingBalanceAccountStatusUsage) BillingBalanceAccountStatus_BillingBalanceAccountStatusInactive() BillingBalanceAccountStatus {
	u.Use(BillingBalanceAccountStatus_BillingBalanceAccountStatusInactive)
	return BillingBalanceAccountStatus_BillingBalanceAccountStatusInactive
}

func (val BillingOperationMode) Synonym() BillingOperationMode {
	if _, ok := BillingOperationMode_name[int32(val)]; ok {
		return val
	}

	return BillingOperationMode(math.MinInt32)
}

func (val BillingOperationMode) Int() int {
	return int(val.Synonym())
}

func (val BillingOperationMode) IntPtr() *int {
	result := int(val.Synonym())
	return &result
}

func (val BillingOperationMode) Int32() int32 {
	return int32(val.Synonym())
}

func (val BillingOperationMode) Int32Ptr() *int32 {
	result := int32(val.Synonym())
	return &result
}

func (val BillingOperationMode) Int64() int64 {
	return int64(val.Synonym())
}

func (val BillingOperationMode) Int64Ptr() *int64 {
	result := int64(val.Synonym())
	return &result
}

func (val BillingOperationMode) Uint() uint {
	return uint(val.Synonym())
}

func (val BillingOperationMode) UintPtr() *uint {
	result := uint(val.Synonym())
	return &result
}

func (val BillingOperationMode) Uint32() uint32 {
	return uint32(val.Synonym())
}

func (val BillingOperationMode) Uint32Ptr() *uint32 {
	result := uint32(val.Synonym())
	return &result
}

func (val BillingOperationMode) Uint64() uint64 {
	return uint64(val.Synonym())
}

func (val BillingOperationMode) Uint64Ptr() *uint64 {
	result := uint64(val.Synonym())
	return &result
}

func (val BillingOperationMode) IsKnown() bool {
	return val.Synonym() != BillingOperationMode(math.MinInt32)
}

func ConvertIntToBillingOperationMode(in int) BillingOperationMode {
	return BillingOperationMode(in).Synonym()
}

func ConvertUintToBillingOperationMode(in uint) BillingOperationMode {
	return BillingOperationMode(in).Synonym()
}

func ConvertInt32ToBillingOperationMode(in int32) BillingOperationMode {
	return BillingOperationMode(in).Synonym()
}

func ConvertUint32ToBillingOperationMode(in uint32) BillingOperationMode {
	return BillingOperationMode(in).Synonym()
}

func ConvertInt64ToBillingOperationMode(in int64) BillingOperationMode {
	return BillingOperationMode(in).Synonym()
}

func ConvertUint64ToBillingOperationMode(in uint64) BillingOperationMode {
	return BillingOperationMode(in).Synonym()
}

var BillingOperationMode_Lower_value = map[string]BillingOperationMode{
	"billingoperationmodeunknown":     0,
	"billingoperationmoderealtime":    1,
	"billingoperationmodebankprocess": 2,
}

func ConvertStringToBillingOperationMode(in string) BillingOperationMode {
	if result, ok := BillingOperationMode_value[in]; ok {
		return BillingOperationMode(result)
	}

	if result, ok := BillingOperationMode_Lower_value[strings.ToLower(in)]; ok {
		return BillingOperationMode(result)
	}

	return BillingOperationMode(math.MinInt32)
}

var SliceBillingOperationModeConvert *sliceBillingOperationModeConvert

type sliceBillingOperationModeConvert struct{}

func (*sliceBillingOperationModeConvert) Synonym(in []BillingOperationMode) []BillingOperationMode {
	result := make([]BillingOperationMode, len(in))
	for i, v := range in {
		result[i] = v.Synonym()
	}

	return result
}

func (*sliceBillingOperationModeConvert) Int32(in []BillingOperationMode) []int32 {
	result := make([]int32, len(in))
	for i, v := range in {
		result[i] = v.Int32()
	}

	return result
}

func (*sliceBillingOperationModeConvert) Uint32(in []BillingOperationMode) []uint32 {
	result := make([]uint32, len(in))
	for i, v := range in {
		result[i] = v.Uint32()
	}

	return result
}

func (*sliceBillingOperationModeConvert) Uint64(in []BillingOperationMode) []uint64 {
	result := make([]uint64, len(in))
	for i, v := range in {
		result[i] = v.Uint64()
	}

	return result
}

func (*sliceBillingOperationModeConvert) Int64(in []BillingOperationMode) []int64 {
	result := make([]int64, len(in))
	for i, v := range in {
		result[i] = v.Int64()
	}

	return result
}

func (*sliceBillingOperationModeConvert) Uint(in []BillingOperationMode) []uint {
	result := make([]uint, len(in))
	for i, v := range in {
		result[i] = v.Uint()
	}

	return result
}

func (*sliceBillingOperationModeConvert) Int(in []BillingOperationMode) []int {
	result := make([]int, len(in))
	for i, v := range in {
		result[i] = v.Int()
	}

	return result
}

func (*sliceBillingOperationModeConvert) IsKnown(in []BillingOperationMode) []bool {
	result := make([]bool, len(in))
	for i, v := range in {
		result[i] = v.IsKnown()
	}

	return result
}

func (*sliceBillingOperationModeConvert) ConvertIntToBillingOperationMode(in []int) []BillingOperationMode {
	result := make([]BillingOperationMode, len(in))
	for i, v := range in {
		result[i] = ConvertIntToBillingOperationMode(v)
	}

	return result
}

func (*sliceBillingOperationModeConvert) ConvertUintToBillingOperationMode(in []uint) []BillingOperationMode {
	result := make([]BillingOperationMode, len(in))
	for i, v := range in {
		result[i] = ConvertUintToBillingOperationMode(v)
	}

	return result
}

func (*sliceBillingOperationModeConvert) ConvertInt32ToBillingOperationMode(in []int32) []BillingOperationMode {
	result := make([]BillingOperationMode, len(in))
	for i, v := range in {
		result[i] = ConvertInt32ToBillingOperationMode(v)
	}

	return result
}

func (*sliceBillingOperationModeConvert) ConvertUint32ToBillingOperationMode(in []uint32) []BillingOperationMode {
	result := make([]BillingOperationMode, len(in))
	for i, v := range in {
		result[i] = ConvertUint32ToBillingOperationMode(v)
	}

	return result
}

func (*sliceBillingOperationModeConvert) ConvertInt64ToBillingOperationMode(in []int64) []BillingOperationMode {
	result := make([]BillingOperationMode, len(in))
	for i, v := range in {
		result[i] = ConvertInt64ToBillingOperationMode(v)
	}

	return result
}

func (*sliceBillingOperationModeConvert) ConvertUint64ToBillingOperationMode(in []uint64) []BillingOperationMode {
	result := make([]BillingOperationMode, len(in))
	for i, v := range in {
		result[i] = ConvertUint64ToBillingOperationMode(v)
	}

	return result
}

func (*sliceBillingOperationModeConvert) ConvertStringToBillingOperationMode(in []string) []BillingOperationMode {
	result := make([]BillingOperationMode, len(in))
	for i, v := range in {
		result[i] = ConvertStringToBillingOperationMode(v)
	}

	return result
}

func NewBillingOperationModeUsage() *BillingOperationModeUsage {
	return &BillingOperationModeUsage{
		enumMap: map[BillingOperationMode]bool{
			BillingOperationMode_BillingOperationModeUnknown:     false,
			BillingOperationMode_BillingOperationModeRealtime:    false,
			BillingOperationMode_BillingOperationModeBankProcess: false,
		},
	}
}

func IsBillingOperationMode(target BillingOperationMode, matches ...BillingOperationMode) bool {
	for _, v := range matches {
		if target == v {
			return true
		}
	}

	return false
}

type BillingOperationModeUsage struct {
	enumMap map[BillingOperationMode]bool
}

func (u *BillingOperationModeUsage) Use(slice ...BillingOperationMode) *BillingOperationModeUsage {
	for _, in := range slice {
		u.enumMap[in] = true
	}

	return u
}

func (u *BillingOperationModeUsage) Check() []string {
	var result []string
	for k, v := range u.enumMap {
		if v {
			continue
		}

		result = append(result, k.String())
	}

	return result
}

func (u *BillingOperationModeUsage) BillingOperationMode_BillingOperationModeUnknown() BillingOperationMode {
	u.Use(BillingOperationMode_BillingOperationModeUnknown)
	return BillingOperationMode_BillingOperationModeUnknown
}

func (u *BillingOperationModeUsage) BillingOperationMode_BillingOperationModeRealtime() BillingOperationMode {
	u.Use(BillingOperationMode_BillingOperationModeRealtime)
	return BillingOperationMode_BillingOperationModeRealtime
}

func (u *BillingOperationModeUsage) BillingOperationMode_BillingOperationModeBankProcess() BillingOperationMode {
	u.Use(BillingOperationMode_BillingOperationModeBankProcess)
	return BillingOperationMode_BillingOperationModeBankProcess
}

func (val BillingOperationTypeGroupRelation) Synonym() BillingOperationTypeGroupRelation {
	if _, ok := BillingOperationTypeGroupRelation_name[int32(val)]; ok {
		return val
	}

	return BillingOperationTypeGroupRelation(math.MinInt32)
}

func (val BillingOperationTypeGroupRelation) Int() int {
	return int(val.Synonym())
}

func (val BillingOperationTypeGroupRelation) IntPtr() *int {
	result := int(val.Synonym())
	return &result
}

func (val BillingOperationTypeGroupRelation) Int32() int32 {
	return int32(val.Synonym())
}

func (val BillingOperationTypeGroupRelation) Int32Ptr() *int32 {
	result := int32(val.Synonym())
	return &result
}

func (val BillingOperationTypeGroupRelation) Int64() int64 {
	return int64(val.Synonym())
}

func (val BillingOperationTypeGroupRelation) Int64Ptr() *int64 {
	result := int64(val.Synonym())
	return &result
}

func (val BillingOperationTypeGroupRelation) Uint() uint {
	return uint(val.Synonym())
}

func (val BillingOperationTypeGroupRelation) UintPtr() *uint {
	result := uint(val.Synonym())
	return &result
}

func (val BillingOperationTypeGroupRelation) Uint32() uint32 {
	return uint32(val.Synonym())
}

func (val BillingOperationTypeGroupRelation) Uint32Ptr() *uint32 {
	result := uint32(val.Synonym())
	return &result
}

func (val BillingOperationTypeGroupRelation) Uint64() uint64 {
	return uint64(val.Synonym())
}

func (val BillingOperationTypeGroupRelation) Uint64Ptr() *uint64 {
	result := uint64(val.Synonym())
	return &result
}

func (val BillingOperationTypeGroupRelation) IsKnown() bool {
	return val.Synonym() != BillingOperationTypeGroupRelation(math.MinInt32)
}

func ConvertIntToBillingOperationTypeGroupRelation(in int) BillingOperationTypeGroupRelation {
	return BillingOperationTypeGroupRelation(in).Synonym()
}

func ConvertUintToBillingOperationTypeGroupRelation(in uint) BillingOperationTypeGroupRelation {
	return BillingOperationTypeGroupRelation(in).Synonym()
}

func ConvertInt32ToBillingOperationTypeGroupRelation(in int32) BillingOperationTypeGroupRelation {
	return BillingOperationTypeGroupRelation(in).Synonym()
}

func ConvertUint32ToBillingOperationTypeGroupRelation(in uint32) BillingOperationTypeGroupRelation {
	return BillingOperationTypeGroupRelation(in).Synonym()
}

func ConvertInt64ToBillingOperationTypeGroupRelation(in int64) BillingOperationTypeGroupRelation {
	return BillingOperationTypeGroupRelation(in).Synonym()
}

func ConvertUint64ToBillingOperationTypeGroupRelation(in uint64) BillingOperationTypeGroupRelation {
	return BillingOperationTypeGroupRelation(in).Synonym()
}

var BillingOperationTypeGroupRelation_Lower_value = map[string]BillingOperationTypeGroupRelation{
	"billingoperationtypegrouprelationunknown":              0,
	"billingoperationtypegrouprelationpayin":                1,
	"billingoperationtypegrouprelationpayout":               2,
	"billingoperationtypegrouprelationrefund":               3,
	"billingoperationtypegrouprelationintransfer":           4,
	"billingoperationtypegrouprelationouttransfer":          5,
	"billingoperationtypegrouprelationpayincmsoutacquirer":  6,
	"billingoperationtypegrouprelationpayincmsinsystem":     7,
	"billingoperationtypegrouprelationpayincmsoutsystem":    8,
	"billingoperationtypegrouprelationpayincmsinmerchant":   9,
	"billingoperationtypegrouprelationpayincmsoutmerchant":  10,
	"billingoperationtypegrouprelationinternalintransfer":   11,
	"billingoperationtypegrouprelationinternalouttransfer":  12,
	"billingoperationtypegrouprelationsplitouttransfer":     13,
	"billingoperationtypegrouprelationsplitintransfer":      14,
	"billingoperationtypegrouprelationpayoutcmsoutacquirer": 15,
	"billingoperationtypegrouprelationpayoutcmsinsystem":    16,
	"billingoperationtypegrouprelationpayoutcmsoutsystem":   17,
	"billingoperationtypegrouprelationpayoutcmsinmerchant":  18,
	"billingoperationtypegrouprelationpayoutcmsoutmerchant": 19,
	"billingoperationtypegrouprelationsplitcmsinsystem":     20,
	"billingoperationtypegrouprelationsplitcmsoutsystem":    21,
	"billingoperationtypegrouprelationsplitcmsinmerchant":   22,
	"billingoperationtypegrouprelationsplitcmsoutmerchant":  23,
}

func ConvertStringToBillingOperationTypeGroupRelation(in string) BillingOperationTypeGroupRelation {
	if result, ok := BillingOperationTypeGroupRelation_value[in]; ok {
		return BillingOperationTypeGroupRelation(result)
	}

	if result, ok := BillingOperationTypeGroupRelation_Lower_value[strings.ToLower(in)]; ok {
		return BillingOperationTypeGroupRelation(result)
	}

	return BillingOperationTypeGroupRelation(math.MinInt32)
}

var SliceBillingOperationTypeGroupRelationConvert *sliceBillingOperationTypeGroupRelationConvert

type sliceBillingOperationTypeGroupRelationConvert struct{}

func (*sliceBillingOperationTypeGroupRelationConvert) Synonym(in []BillingOperationTypeGroupRelation) []BillingOperationTypeGroupRelation {
	result := make([]BillingOperationTypeGroupRelation, len(in))
	for i, v := range in {
		result[i] = v.Synonym()
	}

	return result
}

func (*sliceBillingOperationTypeGroupRelationConvert) Int32(in []BillingOperationTypeGroupRelation) []int32 {
	result := make([]int32, len(in))
	for i, v := range in {
		result[i] = v.Int32()
	}

	return result
}

func (*sliceBillingOperationTypeGroupRelationConvert) Uint32(in []BillingOperationTypeGroupRelation) []uint32 {
	result := make([]uint32, len(in))
	for i, v := range in {
		result[i] = v.Uint32()
	}

	return result
}

func (*sliceBillingOperationTypeGroupRelationConvert) Uint64(in []BillingOperationTypeGroupRelation) []uint64 {
	result := make([]uint64, len(in))
	for i, v := range in {
		result[i] = v.Uint64()
	}

	return result
}

func (*sliceBillingOperationTypeGroupRelationConvert) Int64(in []BillingOperationTypeGroupRelation) []int64 {
	result := make([]int64, len(in))
	for i, v := range in {
		result[i] = v.Int64()
	}

	return result
}

func (*sliceBillingOperationTypeGroupRelationConvert) Uint(in []BillingOperationTypeGroupRelation) []uint {
	result := make([]uint, len(in))
	for i, v := range in {
		result[i] = v.Uint()
	}

	return result
}

func (*sliceBillingOperationTypeGroupRelationConvert) Int(in []BillingOperationTypeGroupRelation) []int {
	result := make([]int, len(in))
	for i, v := range in {
		result[i] = v.Int()
	}

	return result
}

func (*sliceBillingOperationTypeGroupRelationConvert) IsKnown(in []BillingOperationTypeGroupRelation) []bool {
	result := make([]bool, len(in))
	for i, v := range in {
		result[i] = v.IsKnown()
	}

	return result
}

func (*sliceBillingOperationTypeGroupRelationConvert) ConvertIntToBillingOperationTypeGroupRelation(in []int) []BillingOperationTypeGroupRelation {
	result := make([]BillingOperationTypeGroupRelation, len(in))
	for i, v := range in {
		result[i] = ConvertIntToBillingOperationTypeGroupRelation(v)
	}

	return result
}

func (*sliceBillingOperationTypeGroupRelationConvert) ConvertUintToBillingOperationTypeGroupRelation(in []uint) []BillingOperationTypeGroupRelation {
	result := make([]BillingOperationTypeGroupRelation, len(in))
	for i, v := range in {
		result[i] = ConvertUintToBillingOperationTypeGroupRelation(v)
	}

	return result
}

func (*sliceBillingOperationTypeGroupRelationConvert) ConvertInt32ToBillingOperationTypeGroupRelation(in []int32) []BillingOperationTypeGroupRelation {
	result := make([]BillingOperationTypeGroupRelation, len(in))
	for i, v := range in {
		result[i] = ConvertInt32ToBillingOperationTypeGroupRelation(v)
	}

	return result
}

func (*sliceBillingOperationTypeGroupRelationConvert) ConvertUint32ToBillingOperationTypeGroupRelation(in []uint32) []BillingOperationTypeGroupRelation {
	result := make([]BillingOperationTypeGroupRelation, len(in))
	for i, v := range in {
		result[i] = ConvertUint32ToBillingOperationTypeGroupRelation(v)
	}

	return result
}

func (*sliceBillingOperationTypeGroupRelationConvert) ConvertInt64ToBillingOperationTypeGroupRelation(in []int64) []BillingOperationTypeGroupRelation {
	result := make([]BillingOperationTypeGroupRelation, len(in))
	for i, v := range in {
		result[i] = ConvertInt64ToBillingOperationTypeGroupRelation(v)
	}

	return result
}

func (*sliceBillingOperationTypeGroupRelationConvert) ConvertUint64ToBillingOperationTypeGroupRelation(in []uint64) []BillingOperationTypeGroupRelation {
	result := make([]BillingOperationTypeGroupRelation, len(in))
	for i, v := range in {
		result[i] = ConvertUint64ToBillingOperationTypeGroupRelation(v)
	}

	return result
}

func (*sliceBillingOperationTypeGroupRelationConvert) ConvertStringToBillingOperationTypeGroupRelation(in []string) []BillingOperationTypeGroupRelation {
	result := make([]BillingOperationTypeGroupRelation, len(in))
	for i, v := range in {
		result[i] = ConvertStringToBillingOperationTypeGroupRelation(v)
	}

	return result
}

func NewBillingOperationTypeGroupRelationUsage() *BillingOperationTypeGroupRelationUsage {
	return &BillingOperationTypeGroupRelationUsage{
		enumMap: map[BillingOperationTypeGroupRelation]bool{
			BillingOperationTypeGroupRelation_BillingOperationTypeGroupRelationUnknown:              false,
			BillingOperationTypeGroupRelation_BillingOperationTypeGroupRelationPayIn:                false,
			BillingOperationTypeGroupRelation_BillingOperationTypeGroupRelationPayOut:               false,
			BillingOperationTypeGroupRelation_BillingOperationTypeGroupRelationRefund:               false,
			BillingOperationTypeGroupRelation_BillingOperationTypeGroupRelationInTransfer:           false,
			BillingOperationTypeGroupRelation_BillingOperationTypeGroupRelationOutTransfer:          false,
			BillingOperationTypeGroupRelation_BillingOperationTypeGroupRelationPayInCmsOutAcquirer:  false,
			BillingOperationTypeGroupRelation_BillingOperationTypeGroupRelationPayInCmsInSystem:     false,
			BillingOperationTypeGroupRelation_BillingOperationTypeGroupRelationPayInCmsOutSystem:    false,
			BillingOperationTypeGroupRelation_BillingOperationTypeGroupRelationPayInCmsInMerchant:   false,
			BillingOperationTypeGroupRelation_BillingOperationTypeGroupRelationPayInCmsOutMerchant:  false,
			BillingOperationTypeGroupRelation_BillingOperationTypeGroupRelationInternalInTransfer:   false,
			BillingOperationTypeGroupRelation_BillingOperationTypeGroupRelationInternalOutTransfer:  false,
			BillingOperationTypeGroupRelation_BillingOperationTypeGroupRelationSplitOutTransfer:     false,
			BillingOperationTypeGroupRelation_BillingOperationTypeGroupRelationSplitInTransfer:      false,
			BillingOperationTypeGroupRelation_BillingOperationTypeGroupRelationPayOutCmsOutAcquirer: false,
			BillingOperationTypeGroupRelation_BillingOperationTypeGroupRelationPayOutCmsInSystem:    false,
			BillingOperationTypeGroupRelation_BillingOperationTypeGroupRelationPayOutCmsOutSystem:   false,
			BillingOperationTypeGroupRelation_BillingOperationTypeGroupRelationPayOutCmsInMerchant:  false,
			BillingOperationTypeGroupRelation_BillingOperationTypeGroupRelationPayOutCmsOutMerchant: false,
			BillingOperationTypeGroupRelation_BillingOperationTypeGroupRelationSplitCmsInSystem:     false,
			BillingOperationTypeGroupRelation_BillingOperationTypeGroupRelationSplitCmsOutSystem:    false,
			BillingOperationTypeGroupRelation_BillingOperationTypeGroupRelationSplitCmsInMerchant:   false,
			BillingOperationTypeGroupRelation_BillingOperationTypeGroupRelationSplitCmsOutMerchant:  false,
		},
	}
}

func IsBillingOperationTypeGroupRelation(target BillingOperationTypeGroupRelation, matches ...BillingOperationTypeGroupRelation) bool {
	for _, v := range matches {
		if target == v {
			return true
		}
	}

	return false
}

type BillingOperationTypeGroupRelationUsage struct {
	enumMap map[BillingOperationTypeGroupRelation]bool
}

func (u *BillingOperationTypeGroupRelationUsage) Use(slice ...BillingOperationTypeGroupRelation) *BillingOperationTypeGroupRelationUsage {
	for _, in := range slice {
		u.enumMap[in] = true
	}

	return u
}

func (u *BillingOperationTypeGroupRelationUsage) Check() []string {
	var result []string
	for k, v := range u.enumMap {
		if v {
			continue
		}

		result = append(result, k.String())
	}

	return result
}

func (u *BillingOperationTypeGroupRelationUsage) BillingOperationTypeGroupRelation_BillingOperationTypeGroupRelationUnknown() BillingOperationTypeGroupRelation {
	u.Use(BillingOperationTypeGroupRelation_BillingOperationTypeGroupRelationUnknown)
	return BillingOperationTypeGroupRelation_BillingOperationTypeGroupRelationUnknown
}

func (u *BillingOperationTypeGroupRelationUsage) BillingOperationTypeGroupRelation_BillingOperationTypeGroupRelationPayIn() BillingOperationTypeGroupRelation {
	u.Use(BillingOperationTypeGroupRelation_BillingOperationTypeGroupRelationPayIn)
	return BillingOperationTypeGroupRelation_BillingOperationTypeGroupRelationPayIn
}

func (u *BillingOperationTypeGroupRelationUsage) BillingOperationTypeGroupRelation_BillingOperationTypeGroupRelationPayOut() BillingOperationTypeGroupRelation {
	u.Use(BillingOperationTypeGroupRelation_BillingOperationTypeGroupRelationPayOut)
	return BillingOperationTypeGroupRelation_BillingOperationTypeGroupRelationPayOut
}

func (u *BillingOperationTypeGroupRelationUsage) BillingOperationTypeGroupRelation_BillingOperationTypeGroupRelationRefund() BillingOperationTypeGroupRelation {
	u.Use(BillingOperationTypeGroupRelation_BillingOperationTypeGroupRelationRefund)
	return BillingOperationTypeGroupRelation_BillingOperationTypeGroupRelationRefund
}

func (u *BillingOperationTypeGroupRelationUsage) BillingOperationTypeGroupRelation_BillingOperationTypeGroupRelationInTransfer() BillingOperationTypeGroupRelation {
	u.Use(BillingOperationTypeGroupRelation_BillingOperationTypeGroupRelationInTransfer)
	return BillingOperationTypeGroupRelation_BillingOperationTypeGroupRelationInTransfer
}

func (u *BillingOperationTypeGroupRelationUsage) BillingOperationTypeGroupRelation_BillingOperationTypeGroupRelationOutTransfer() BillingOperationTypeGroupRelation {
	u.Use(BillingOperationTypeGroupRelation_BillingOperationTypeGroupRelationOutTransfer)
	return BillingOperationTypeGroupRelation_BillingOperationTypeGroupRelationOutTransfer
}

func (u *BillingOperationTypeGroupRelationUsage) BillingOperationTypeGroupRelation_BillingOperationTypeGroupRelationPayInCmsOutAcquirer() BillingOperationTypeGroupRelation {
	u.Use(BillingOperationTypeGroupRelation_BillingOperationTypeGroupRelationPayInCmsOutAcquirer)
	return BillingOperationTypeGroupRelation_BillingOperationTypeGroupRelationPayInCmsOutAcquirer
}

func (u *BillingOperationTypeGroupRelationUsage) BillingOperationTypeGroupRelation_BillingOperationTypeGroupRelationPayInCmsInSystem() BillingOperationTypeGroupRelation {
	u.Use(BillingOperationTypeGroupRelation_BillingOperationTypeGroupRelationPayInCmsInSystem)
	return BillingOperationTypeGroupRelation_BillingOperationTypeGroupRelationPayInCmsInSystem
}

func (u *BillingOperationTypeGroupRelationUsage) BillingOperationTypeGroupRelation_BillingOperationTypeGroupRelationPayInCmsOutSystem() BillingOperationTypeGroupRelation {
	u.Use(BillingOperationTypeGroupRelation_BillingOperationTypeGroupRelationPayInCmsOutSystem)
	return BillingOperationTypeGroupRelation_BillingOperationTypeGroupRelationPayInCmsOutSystem
}

func (u *BillingOperationTypeGroupRelationUsage) BillingOperationTypeGroupRelation_BillingOperationTypeGroupRelationPayInCmsInMerchant() BillingOperationTypeGroupRelation {
	u.Use(BillingOperationTypeGroupRelation_BillingOperationTypeGroupRelationPayInCmsInMerchant)
	return BillingOperationTypeGroupRelation_BillingOperationTypeGroupRelationPayInCmsInMerchant
}

func (u *BillingOperationTypeGroupRelationUsage) BillingOperationTypeGroupRelation_BillingOperationTypeGroupRelationPayInCmsOutMerchant() BillingOperationTypeGroupRelation {
	u.Use(BillingOperationTypeGroupRelation_BillingOperationTypeGroupRelationPayInCmsOutMerchant)
	return BillingOperationTypeGroupRelation_BillingOperationTypeGroupRelationPayInCmsOutMerchant
}

func (u *BillingOperationTypeGroupRelationUsage) BillingOperationTypeGroupRelation_BillingOperationTypeGroupRelationInternalInTransfer() BillingOperationTypeGroupRelation {
	u.Use(BillingOperationTypeGroupRelation_BillingOperationTypeGroupRelationInternalInTransfer)
	return BillingOperationTypeGroupRelation_BillingOperationTypeGroupRelationInternalInTransfer
}

func (u *BillingOperationTypeGroupRelationUsage) BillingOperationTypeGroupRelation_BillingOperationTypeGroupRelationInternalOutTransfer() BillingOperationTypeGroupRelation {
	u.Use(BillingOperationTypeGroupRelation_BillingOperationTypeGroupRelationInternalOutTransfer)
	return BillingOperationTypeGroupRelation_BillingOperationTypeGroupRelationInternalOutTransfer
}

func (u *BillingOperationTypeGroupRelationUsage) BillingOperationTypeGroupRelation_BillingOperationTypeGroupRelationSplitOutTransfer() BillingOperationTypeGroupRelation {
	u.Use(BillingOperationTypeGroupRelation_BillingOperationTypeGroupRelationSplitOutTransfer)
	return BillingOperationTypeGroupRelation_BillingOperationTypeGroupRelationSplitOutTransfer
}

func (u *BillingOperationTypeGroupRelationUsage) BillingOperationTypeGroupRelation_BillingOperationTypeGroupRelationSplitInTransfer() BillingOperationTypeGroupRelation {
	u.Use(BillingOperationTypeGroupRelation_BillingOperationTypeGroupRelationSplitInTransfer)
	return BillingOperationTypeGroupRelation_BillingOperationTypeGroupRelationSplitInTransfer
}

func (u *BillingOperationTypeGroupRelationUsage) BillingOperationTypeGroupRelation_BillingOperationTypeGroupRelationPayOutCmsOutAcquirer() BillingOperationTypeGroupRelation {
	u.Use(BillingOperationTypeGroupRelation_BillingOperationTypeGroupRelationPayOutCmsOutAcquirer)
	return BillingOperationTypeGroupRelation_BillingOperationTypeGroupRelationPayOutCmsOutAcquirer
}

func (u *BillingOperationTypeGroupRelationUsage) BillingOperationTypeGroupRelation_BillingOperationTypeGroupRelationPayOutCmsInSystem() BillingOperationTypeGroupRelation {
	u.Use(BillingOperationTypeGroupRelation_BillingOperationTypeGroupRelationPayOutCmsInSystem)
	return BillingOperationTypeGroupRelation_BillingOperationTypeGroupRelationPayOutCmsInSystem
}

func (u *BillingOperationTypeGroupRelationUsage) BillingOperationTypeGroupRelation_BillingOperationTypeGroupRelationPayOutCmsOutSystem() BillingOperationTypeGroupRelation {
	u.Use(BillingOperationTypeGroupRelation_BillingOperationTypeGroupRelationPayOutCmsOutSystem)
	return BillingOperationTypeGroupRelation_BillingOperationTypeGroupRelationPayOutCmsOutSystem
}

func (u *BillingOperationTypeGroupRelationUsage) BillingOperationTypeGroupRelation_BillingOperationTypeGroupRelationPayOutCmsInMerchant() BillingOperationTypeGroupRelation {
	u.Use(BillingOperationTypeGroupRelation_BillingOperationTypeGroupRelationPayOutCmsInMerchant)
	return BillingOperationTypeGroupRelation_BillingOperationTypeGroupRelationPayOutCmsInMerchant
}

func (u *BillingOperationTypeGroupRelationUsage) BillingOperationTypeGroupRelation_BillingOperationTypeGroupRelationPayOutCmsOutMerchant() BillingOperationTypeGroupRelation {
	u.Use(BillingOperationTypeGroupRelation_BillingOperationTypeGroupRelationPayOutCmsOutMerchant)
	return BillingOperationTypeGroupRelation_BillingOperationTypeGroupRelationPayOutCmsOutMerchant
}

func (u *BillingOperationTypeGroupRelationUsage) BillingOperationTypeGroupRelation_BillingOperationTypeGroupRelationSplitCmsInSystem() BillingOperationTypeGroupRelation {
	u.Use(BillingOperationTypeGroupRelation_BillingOperationTypeGroupRelationSplitCmsInSystem)
	return BillingOperationTypeGroupRelation_BillingOperationTypeGroupRelationSplitCmsInSystem
}

func (u *BillingOperationTypeGroupRelationUsage) BillingOperationTypeGroupRelation_BillingOperationTypeGroupRelationSplitCmsOutSystem() BillingOperationTypeGroupRelation {
	u.Use(BillingOperationTypeGroupRelation_BillingOperationTypeGroupRelationSplitCmsOutSystem)
	return BillingOperationTypeGroupRelation_BillingOperationTypeGroupRelationSplitCmsOutSystem
}

func (u *BillingOperationTypeGroupRelationUsage) BillingOperationTypeGroupRelation_BillingOperationTypeGroupRelationSplitCmsInMerchant() BillingOperationTypeGroupRelation {
	u.Use(BillingOperationTypeGroupRelation_BillingOperationTypeGroupRelationSplitCmsInMerchant)
	return BillingOperationTypeGroupRelation_BillingOperationTypeGroupRelationSplitCmsInMerchant
}

func (u *BillingOperationTypeGroupRelationUsage) BillingOperationTypeGroupRelation_BillingOperationTypeGroupRelationSplitCmsOutMerchant() BillingOperationTypeGroupRelation {
	u.Use(BillingOperationTypeGroupRelation_BillingOperationTypeGroupRelationSplitCmsOutMerchant)
	return BillingOperationTypeGroupRelation_BillingOperationTypeGroupRelationSplitCmsOutMerchant
}
