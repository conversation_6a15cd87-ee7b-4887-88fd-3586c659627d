// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.5
// 	protoc        v5.29.3
// source: inner/processing/events/report_merchant.proto

package events

import (
	_ "git.local/sensitive/mvp/pb"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type SaveAdditionalDataKeys struct {
	state          protoimpl.MessageState `protogen:"open.v1"`
	ProjectId      uint64                 `protobuf:"varint,1,opt,name=project_id,json=projectId,proto3" json:"project_id,omitempty"`
	MerchantId     uint64                 `protobuf:"varint,2,opt,name=merchant_id,json=merchantId,proto3" json:"merchant_id,omitempty"`
	AdditionalData map[string]string      `protobuf:"bytes,3,rep,name=additional_data,json=additionalData,proto3" json:"additional_data,omitempty" protobuf_key:"bytes,1,opt,name=key" protobuf_val:"bytes,2,opt,name=value"`
	unknownFields  protoimpl.UnknownFields
	sizeCache      protoimpl.SizeCache
}

func (x *SaveAdditionalDataKeys) Reset() {
	*x = SaveAdditionalDataKeys{}
	mi := &file_inner_processing_events_report_merchant_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SaveAdditionalDataKeys) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SaveAdditionalDataKeys) ProtoMessage() {}

func (x *SaveAdditionalDataKeys) ProtoReflect() protoreflect.Message {
	mi := &file_inner_processing_events_report_merchant_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SaveAdditionalDataKeys.ProtoReflect.Descriptor instead.
func (*SaveAdditionalDataKeys) Descriptor() ([]byte, []int) {
	return file_inner_processing_events_report_merchant_proto_rawDescGZIP(), []int{0}
}

func (x *SaveAdditionalDataKeys) GetProjectId() uint64 {
	if x != nil {
		return x.ProjectId
	}
	return 0
}

func (x *SaveAdditionalDataKeys) GetMerchantId() uint64 {
	if x != nil {
		return x.MerchantId
	}
	return 0
}

func (x *SaveAdditionalDataKeys) GetAdditionalData() map[string]string {
	if x != nil {
		return x.AdditionalData
	}
	return nil
}

type CreateMerchantCompany struct {
	state               protoimpl.MessageState `protogen:"open.v1"`
	MerchantId          uint64                 `protobuf:"varint,1,opt,name=merchant_id,json=merchantId,proto3" json:"merchant_id,omitempty"`
	MerchantName        string                 `protobuf:"bytes,2,opt,name=merchant_name,json=merchantName,proto3" json:"merchant_name,omitempty"`
	MerchantCompanyName string                 `protobuf:"bytes,3,opt,name=merchant_company_name,json=merchantCompanyName,proto3" json:"merchant_company_name,omitempty"`
	MerchantEmail       string                 `protobuf:"bytes,4,opt,name=merchant_email,json=merchantEmail,proto3" json:"merchant_email,omitempty"`
	MerchantSiteUrl     string                 `protobuf:"bytes,5,opt,name=merchant_siteUrl,json=merchantSiteUrl,proto3" json:"merchant_siteUrl,omitempty"`
	MerchantDescription string                 `protobuf:"bytes,6,opt,name=merchant_description,json=merchantDescription,proto3" json:"merchant_description,omitempty"`
	MerchantContacts    string                 `protobuf:"bytes,7,opt,name=merchant_contacts,json=merchantContacts,proto3" json:"merchant_contacts,omitempty"`
	unknownFields       protoimpl.UnknownFields
	sizeCache           protoimpl.SizeCache
}

func (x *CreateMerchantCompany) Reset() {
	*x = CreateMerchantCompany{}
	mi := &file_inner_processing_events_report_merchant_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreateMerchantCompany) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateMerchantCompany) ProtoMessage() {}

func (x *CreateMerchantCompany) ProtoReflect() protoreflect.Message {
	mi := &file_inner_processing_events_report_merchant_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateMerchantCompany.ProtoReflect.Descriptor instead.
func (*CreateMerchantCompany) Descriptor() ([]byte, []int) {
	return file_inner_processing_events_report_merchant_proto_rawDescGZIP(), []int{1}
}

func (x *CreateMerchantCompany) GetMerchantId() uint64 {
	if x != nil {
		return x.MerchantId
	}
	return 0
}

func (x *CreateMerchantCompany) GetMerchantName() string {
	if x != nil {
		return x.MerchantName
	}
	return ""
}

func (x *CreateMerchantCompany) GetMerchantCompanyName() string {
	if x != nil {
		return x.MerchantCompanyName
	}
	return ""
}

func (x *CreateMerchantCompany) GetMerchantEmail() string {
	if x != nil {
		return x.MerchantEmail
	}
	return ""
}

func (x *CreateMerchantCompany) GetMerchantSiteUrl() string {
	if x != nil {
		return x.MerchantSiteUrl
	}
	return ""
}

func (x *CreateMerchantCompany) GetMerchantDescription() string {
	if x != nil {
		return x.MerchantDescription
	}
	return ""
}

func (x *CreateMerchantCompany) GetMerchantContacts() string {
	if x != nil {
		return x.MerchantContacts
	}
	return ""
}

var File_inner_processing_events_report_merchant_proto protoreflect.FileDescriptor

var file_inner_processing_events_report_merchant_proto_rawDesc = string([]byte{
	0x0a, 0x2d, 0x69, 0x6e, 0x6e, 0x65, 0x72, 0x2f, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69,
	0x6e, 0x67, 0x2f, 0x65, 0x76, 0x65, 0x6e, 0x74, 0x73, 0x2f, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74,
	0x5f, 0x6d, 0x65, 0x72, 0x63, 0x68, 0x61, 0x6e, 0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a,
	0x16, 0x6d, 0x76, 0x70, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f, 0x65, 0x76, 0x65, 0x6e, 0x74,
	0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0xf8, 0x01, 0x0a, 0x16, 0x53, 0x61, 0x76, 0x65,
	0x41, 0x64, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x44, 0x61, 0x74, 0x61, 0x4b, 0x65,
	0x79, 0x73, 0x12, 0x1d, 0x0a, 0x0a, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x5f, 0x69, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x09, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x49,
	0x64, 0x12, 0x1f, 0x0a, 0x0b, 0x6d, 0x65, 0x72, 0x63, 0x68, 0x61, 0x6e, 0x74, 0x5f, 0x69, 0x64,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x04, 0x52, 0x0a, 0x6d, 0x65, 0x72, 0x63, 0x68, 0x61, 0x6e, 0x74,
	0x49, 0x64, 0x12, 0x54, 0x0a, 0x0f, 0x61, 0x64, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c,
	0x5f, 0x64, 0x61, 0x74, 0x61, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2b, 0x2e, 0x53, 0x61,
	0x76, 0x65, 0x41, 0x64, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x44, 0x61, 0x74, 0x61,
	0x4b, 0x65, 0x79, 0x73, 0x2e, 0x41, 0x64, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x44,
	0x61, 0x74, 0x61, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x0e, 0x61, 0x64, 0x64, 0x69, 0x74, 0x69,
	0x6f, 0x6e, 0x61, 0x6c, 0x44, 0x61, 0x74, 0x61, 0x1a, 0x41, 0x0a, 0x13, 0x41, 0x64, 0x64, 0x69,
	0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x44, 0x61, 0x74, 0x61, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12,
	0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65,
	0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x3a, 0x05, 0xb8, 0xe9, 0xe4,
	0x03, 0x01, 0x22, 0xca, 0x02, 0x0a, 0x15, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x4d, 0x65, 0x72,
	0x63, 0x68, 0x61, 0x6e, 0x74, 0x43, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x12, 0x1f, 0x0a, 0x0b,
	0x6d, 0x65, 0x72, 0x63, 0x68, 0x61, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x04, 0x52, 0x0a, 0x6d, 0x65, 0x72, 0x63, 0x68, 0x61, 0x6e, 0x74, 0x49, 0x64, 0x12, 0x23, 0x0a,
	0x0d, 0x6d, 0x65, 0x72, 0x63, 0x68, 0x61, 0x6e, 0x74, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x6d, 0x65, 0x72, 0x63, 0x68, 0x61, 0x6e, 0x74, 0x4e, 0x61,
	0x6d, 0x65, 0x12, 0x32, 0x0a, 0x15, 0x6d, 0x65, 0x72, 0x63, 0x68, 0x61, 0x6e, 0x74, 0x5f, 0x63,
	0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x13, 0x6d, 0x65, 0x72, 0x63, 0x68, 0x61, 0x6e, 0x74, 0x43, 0x6f, 0x6d, 0x70, 0x61,
	0x6e, 0x79, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x25, 0x0a, 0x0e, 0x6d, 0x65, 0x72, 0x63, 0x68, 0x61,
	0x6e, 0x74, 0x5f, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d,
	0x6d, 0x65, 0x72, 0x63, 0x68, 0x61, 0x6e, 0x74, 0x45, 0x6d, 0x61, 0x69, 0x6c, 0x12, 0x29, 0x0a,
	0x10, 0x6d, 0x65, 0x72, 0x63, 0x68, 0x61, 0x6e, 0x74, 0x5f, 0x73, 0x69, 0x74, 0x65, 0x55, 0x72,
	0x6c, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f, 0x6d, 0x65, 0x72, 0x63, 0x68, 0x61, 0x6e,
	0x74, 0x53, 0x69, 0x74, 0x65, 0x55, 0x72, 0x6c, 0x12, 0x31, 0x0a, 0x14, 0x6d, 0x65, 0x72, 0x63,
	0x68, 0x61, 0x6e, 0x74, 0x5f, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e,
	0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x13, 0x6d, 0x65, 0x72, 0x63, 0x68, 0x61, 0x6e, 0x74,
	0x44, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x2b, 0x0a, 0x11, 0x6d,
	0x65, 0x72, 0x63, 0x68, 0x61, 0x6e, 0x74, 0x5f, 0x63, 0x6f, 0x6e, 0x74, 0x61, 0x63, 0x74, 0x73,
	0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x10, 0x6d, 0x65, 0x72, 0x63, 0x68, 0x61, 0x6e, 0x74,
	0x43, 0x6f, 0x6e, 0x74, 0x61, 0x63, 0x74, 0x73, 0x3a, 0x05, 0xb8, 0xe9, 0xe4, 0x03, 0x01, 0x42,
	0x2f, 0x5a, 0x2d, 0x67, 0x69, 0x74, 0x2e, 0x6c, 0x6f, 0x63, 0x61, 0x6c, 0x2f, 0x73, 0x65, 0x6e,
	0x73, 0x69, 0x74, 0x69, 0x76, 0x65, 0x2f, 0x69, 0x6e, 0x6e, 0x65, 0x72, 0x70, 0x62, 0x2f, 0x70,
	0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x2f, 0x65, 0x76, 0x65, 0x6e, 0x74, 0x73,
	0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
})

var (
	file_inner_processing_events_report_merchant_proto_rawDescOnce sync.Once
	file_inner_processing_events_report_merchant_proto_rawDescData []byte
)

func file_inner_processing_events_report_merchant_proto_rawDescGZIP() []byte {
	file_inner_processing_events_report_merchant_proto_rawDescOnce.Do(func() {
		file_inner_processing_events_report_merchant_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_inner_processing_events_report_merchant_proto_rawDesc), len(file_inner_processing_events_report_merchant_proto_rawDesc)))
	})
	return file_inner_processing_events_report_merchant_proto_rawDescData
}

var file_inner_processing_events_report_merchant_proto_msgTypes = make([]protoimpl.MessageInfo, 3)
var file_inner_processing_events_report_merchant_proto_goTypes = []any{
	(*SaveAdditionalDataKeys)(nil), // 0: SaveAdditionalDataKeys
	(*CreateMerchantCompany)(nil),  // 1: CreateMerchantCompany
	nil,                            // 2: SaveAdditionalDataKeys.AdditionalDataEntry
}
var file_inner_processing_events_report_merchant_proto_depIdxs = []int32{
	2, // 0: SaveAdditionalDataKeys.additional_data:type_name -> SaveAdditionalDataKeys.AdditionalDataEntry
	1, // [1:1] is the sub-list for method output_type
	1, // [1:1] is the sub-list for method input_type
	1, // [1:1] is the sub-list for extension type_name
	1, // [1:1] is the sub-list for extension extendee
	0, // [0:1] is the sub-list for field type_name
}

func init() { file_inner_processing_events_report_merchant_proto_init() }
func file_inner_processing_events_report_merchant_proto_init() {
	if File_inner_processing_events_report_merchant_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_inner_processing_events_report_merchant_proto_rawDesc), len(file_inner_processing_events_report_merchant_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   3,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_inner_processing_events_report_merchant_proto_goTypes,
		DependencyIndexes: file_inner_processing_events_report_merchant_proto_depIdxs,
		MessageInfos:      file_inner_processing_events_report_merchant_proto_msgTypes,
	}.Build()
	File_inner_processing_events_report_merchant_proto = out.File
	file_inner_processing_events_report_merchant_proto_goTypes = nil
	file_inner_processing_events_report_merchant_proto_depIdxs = nil
}
