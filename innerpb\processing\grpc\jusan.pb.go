// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.5
// 	protoc        v5.29.3
// source: inner/processing/grpc/jusan.proto

package grpc

import (
	_ "git.local/sensitive/mvp/pb"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	descriptorpb "google.golang.org/protobuf/types/descriptorpb"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type JusanResponseStatus int32

const (
	JusanResponseStatus_Stage1Status1 JusanResponseStatus = 0
	JusanResponseStatus_Stage2Status1 JusanResponseStatus = 1
	JusanResponseStatus_Stage3Status1 JusanResponseStatus = 2
	JusanResponseStatus_Stage1Status0 JusanResponseStatus = 3
	JusanResponseStatus_Stage2Status0 JusanResponseStatus = 4
	JusanResponseStatus_Stage1Status2 JusanResponseStatus = 5
	JusanResponseStatus_Stage2Status2 JusanResponseStatus = 6
	JusanResponseStatus_Stage3Status2 JusanResponseStatus = 7
	JusanResponseStatus_Stage4Status0 JusanResponseStatus = 8
	JusanResponseStatus_Stage4Status1 JusanResponseStatus = 9
	JusanResponseStatus_Stage4Status2 JusanResponseStatus = 10
)

// Enum value maps for JusanResponseStatus.
var (
	JusanResponseStatus_name = map[int32]string{
		0:  "Stage1Status1",
		1:  "Stage2Status1",
		2:  "Stage3Status1",
		3:  "Stage1Status0",
		4:  "Stage2Status0",
		5:  "Stage1Status2",
		6:  "Stage2Status2",
		7:  "Stage3Status2",
		8:  "Stage4Status0",
		9:  "Stage4Status1",
		10: "Stage4Status2",
	}
	JusanResponseStatus_value = map[string]int32{
		"Stage1Status1": 0,
		"Stage2Status1": 1,
		"Stage3Status1": 2,
		"Stage1Status0": 3,
		"Stage2Status0": 4,
		"Stage1Status2": 5,
		"Stage2Status2": 6,
		"Stage3Status2": 7,
		"Stage4Status0": 8,
		"Stage4Status1": 9,
		"Stage4Status2": 10,
	}
)

func (x JusanResponseStatus) Enum() *JusanResponseStatus {
	p := new(JusanResponseStatus)
	*p = x
	return p
}

func (x JusanResponseStatus) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (JusanResponseStatus) Descriptor() protoreflect.EnumDescriptor {
	return file_inner_processing_grpc_jusan_proto_enumTypes[0].Descriptor()
}

func (JusanResponseStatus) Type() protoreflect.EnumType {
	return &file_inner_processing_grpc_jusan_proto_enumTypes[0]
}

func (x JusanResponseStatus) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use JusanResponseStatus.Descriptor instead.
func (JusanResponseStatus) EnumDescriptor() ([]byte, []int) {
	return file_inner_processing_grpc_jusan_proto_rawDescGZIP(), []int{0}
}

type JusanResponseStatusRef struct {
	state             protoimpl.MessageState `protogen:"open.v1"`
	Stage             *string                `protobuf:"bytes,1,opt,name=stage" json:"stage,omitempty"`
	Status            *string                `protobuf:"bytes,2,opt,name=status" json:"status,omitempty"`
	Description       *string                `protobuf:"bytes,3,opt,name=description" json:"description,omitempty"`
	TransactionStatus *EnumTransactionStatus `protobuf:"varint,4,opt,name=transaction_status,json=transactionStatus,enum=processing.transaction.transaction_status.EnumTransactionStatus" json:"transaction_status,omitempty"`
	unknownFields     protoimpl.UnknownFields
	sizeCache         protoimpl.SizeCache
}

func (x *JusanResponseStatusRef) Reset() {
	*x = JusanResponseStatusRef{}
	mi := &file_inner_processing_grpc_jusan_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *JusanResponseStatusRef) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*JusanResponseStatusRef) ProtoMessage() {}

func (x *JusanResponseStatusRef) ProtoReflect() protoreflect.Message {
	mi := &file_inner_processing_grpc_jusan_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use JusanResponseStatusRef.ProtoReflect.Descriptor instead.
func (*JusanResponseStatusRef) Descriptor() ([]byte, []int) {
	return file_inner_processing_grpc_jusan_proto_rawDescGZIP(), []int{0}
}

func (x *JusanResponseStatusRef) GetStage() string {
	if x != nil && x.Stage != nil {
		return *x.Stage
	}
	return ""
}

func (x *JusanResponseStatusRef) GetStatus() string {
	if x != nil && x.Status != nil {
		return *x.Status
	}
	return ""
}

func (x *JusanResponseStatusRef) GetDescription() string {
	if x != nil && x.Description != nil {
		return *x.Description
	}
	return ""
}

func (x *JusanResponseStatusRef) GetTransactionStatus() EnumTransactionStatus {
	if x != nil && x.TransactionStatus != nil {
		return *x.TransactionStatus
	}
	return EnumTransactionStatus_Unknown
}

var file_inner_processing_grpc_jusan_proto_extTypes = []protoimpl.ExtensionInfo{
	{
		ExtendedType:  (*descriptorpb.EnumValueOptions)(nil),
		ExtensionType: (*JusanResponseStatusRef)(nil),
		Field:         100110,
		Name:          "processing.jusan.jusan.jusan_response_status_value",
		Tag:           "bytes,100110,opt,name=jusan_response_status_value",
		Filename:      "inner/processing/grpc/jusan.proto",
	},
	{
		ExtendedType:  (*descriptorpb.EnumOptions)(nil),
		ExtensionType: (*JusanResponseStatusRef)(nil),
		Field:         100111,
		Name:          "processing.jusan.jusan.default_jusan_response_status_value",
		Tag:           "bytes,100111,opt,name=default_jusan_response_status_value",
		Filename:      "inner/processing/grpc/jusan.proto",
	},
}

// Extension fields to descriptorpb.EnumValueOptions.
var (
	// optional processing.jusan.jusan.JusanResponseStatusRef jusan_response_status_value = 100110;
	E_JusanResponseStatusValue = &file_inner_processing_grpc_jusan_proto_extTypes[0]
)

// Extension fields to descriptorpb.EnumOptions.
var (
	// optional processing.jusan.jusan.JusanResponseStatusRef default_jusan_response_status_value = 100111;
	E_DefaultJusanResponseStatusValue = &file_inner_processing_grpc_jusan_proto_extTypes[1]
)

var File_inner_processing_grpc_jusan_proto protoreflect.FileDescriptor

var file_inner_processing_grpc_jusan_proto_rawDesc = string([]byte{
	0x0a, 0x21, 0x69, 0x6e, 0x6e, 0x65, 0x72, 0x2f, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69,
	0x6e, 0x67, 0x2f, 0x67, 0x72, 0x70, 0x63, 0x2f, 0x6a, 0x75, 0x73, 0x61, 0x6e, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x12, 0x16, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x2e,
	0x6a, 0x75, 0x73, 0x61, 0x6e, 0x2e, 0x6a, 0x75, 0x73, 0x61, 0x6e, 0x1a, 0x2a, 0x69, 0x6e, 0x6e,
	0x65, 0x72, 0x2f, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x2f, 0x67, 0x72,
	0x70, 0x63, 0x2f, 0x6d, 0x75, 0x6c, 0x74, 0x69, 0x61, 0x63, 0x71, 0x75, 0x69, 0x72, 0x69, 0x6e,
	0x67, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x33, 0x69, 0x6e, 0x6e, 0x65, 0x72, 0x2f, 0x70,
	0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x2f, 0x67, 0x72, 0x70, 0x63, 0x2f, 0x6d,
	0x75, 0x6c, 0x74, 0x69, 0x61, 0x63, 0x71, 0x75, 0x69, 0x72, 0x69, 0x6e, 0x67, 0x5f, 0x65, 0x6d,
	0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x32, 0x69, 0x6e,
	0x6e, 0x65, 0x72, 0x2f, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x2f, 0x67,
	0x72, 0x70, 0x63, 0x2f, 0x6d, 0x75, 0x6c, 0x74, 0x69, 0x61, 0x63, 0x71, 0x75, 0x69, 0x72, 0x69,
	0x6e, 0x67, 0x5f, 0x62, 0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x1a, 0x2e, 0x69, 0x6e, 0x6e, 0x65, 0x72, 0x2f, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69,
	0x6e, 0x67, 0x2f, 0x67, 0x72, 0x70, 0x63, 0x2f, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74,
	0x69, 0x6f, 0x6e, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x1a, 0x1b, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75,
	0x66, 0x2f, 0x65, 0x6d, 0x70, 0x74, 0x79, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x14, 0x6d,
	0x76, 0x70, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f, 0x72, 0x65, 0x66, 0x73, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x1a, 0x14, 0x6d, 0x76, 0x70, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f, 0x65,
	0x6e, 0x75, 0x6d, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x20, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x64, 0x65, 0x73, 0x63, 0x72,
	0x69, 0x70, 0x74, 0x6f, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0xd9, 0x01, 0x0a, 0x16,
	0x4a, 0x75, 0x73, 0x61, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x53, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x52, 0x65, 0x66, 0x12, 0x14, 0x0a, 0x05, 0x73, 0x74, 0x61, 0x67, 0x65, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x73, 0x74, 0x61, 0x67, 0x65, 0x12, 0x16, 0x0a, 0x06,
	0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x73, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x12, 0x20, 0x0a, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74,
	0x69, 0x6f, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72,
	0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x6f, 0x0a, 0x12, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61,
	0x63, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x0e, 0x32, 0x40, 0x2e, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x2e,
	0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x74, 0x72, 0x61, 0x6e,
	0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x2e, 0x45,
	0x6e, 0x75, 0x6d, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x52, 0x11, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f,
	0x6e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x2a, 0x81, 0x06, 0x0a, 0x13, 0x4a, 0x75, 0x73, 0x61,
	0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12,
	0x36, 0x0a, 0x0d, 0x53, 0x74, 0x61, 0x67, 0x65, 0x31, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x31,
	0x10, 0x00, 0x1a, 0x23, 0xf2, 0xf0, 0x30, 0x16, 0x0a, 0x01, 0x31, 0x12, 0x01, 0x31, 0x1a, 0x0c,
	0xd0, 0x9e, 0xd1, 0x88, 0xd0, 0xb8, 0xd0, 0xb1, 0xd0, 0xba, 0xd0, 0xb0, 0x20, 0x05, 0xca, 0xa1,
	0xec, 0x8e, 0x02, 0x03, 0x31, 0x3a, 0x31, 0x12, 0x36, 0x0a, 0x0d, 0x53, 0x74, 0x61, 0x67, 0x65,
	0x32, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x31, 0x10, 0x01, 0x1a, 0x23, 0xf2, 0xf0, 0x30, 0x16,
	0x0a, 0x01, 0x32, 0x12, 0x01, 0x31, 0x1a, 0x0c, 0xd0, 0x9e, 0xd1, 0x88, 0xd0, 0xb8, 0xd0, 0xb1,
	0xd0, 0xba, 0xd0, 0xb0, 0x20, 0x05, 0xca, 0xa1, 0xec, 0x8e, 0x02, 0x03, 0x32, 0x3a, 0x31, 0x12,
	0x36, 0x0a, 0x0d, 0x53, 0x74, 0x61, 0x67, 0x65, 0x33, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x31,
	0x10, 0x02, 0x1a, 0x23, 0xf2, 0xf0, 0x30, 0x16, 0x0a, 0x01, 0x33, 0x12, 0x01, 0x31, 0x1a, 0x0c,
	0xd0, 0x9e, 0xd1, 0x88, 0xd0, 0xb8, 0xd0, 0xb1, 0xd0, 0xba, 0xd0, 0xb0, 0x20, 0x05, 0xca, 0xa1,
	0xec, 0x8e, 0x02, 0x03, 0x33, 0x3a, 0x31, 0x12, 0x3d, 0x0a, 0x0d, 0x53, 0x74, 0x61, 0x67, 0x65,
	0x31, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x30, 0x10, 0x03, 0x1a, 0x2a, 0xf2, 0xf0, 0x30, 0x1d,
	0x0a, 0x01, 0x31, 0x12, 0x01, 0x30, 0x1a, 0x13, 0xd0, 0x92, 0x20, 0xd0, 0xbf, 0xd1, 0x80, 0xd0,
	0xbe, 0xd1, 0x86, 0xd0, 0xb5, 0xd1, 0x81, 0xd1, 0x81, 0xd0, 0xb5, 0x20, 0x04, 0xca, 0xa1, 0xec,
	0x8e, 0x02, 0x03, 0x31, 0x3a, 0x30, 0x12, 0x3d, 0x0a, 0x0d, 0x53, 0x74, 0x61, 0x67, 0x65, 0x32,
	0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x30, 0x10, 0x04, 0x1a, 0x2a, 0xf2, 0xf0, 0x30, 0x1d, 0x0a,
	0x01, 0x32, 0x12, 0x01, 0x30, 0x1a, 0x13, 0xd0, 0x92, 0x20, 0xd0, 0xbf, 0xd1, 0x80, 0xd0, 0xbe,
	0xd1, 0x86, 0xd0, 0xb5, 0xd1, 0x81, 0xd1, 0x81, 0xd0, 0xb5, 0x20, 0x04, 0xca, 0xa1, 0xec, 0x8e,
	0x02, 0x03, 0x32, 0x3a, 0x30, 0x12, 0x3d, 0x0a, 0x0d, 0x53, 0x74, 0x61, 0x67, 0x65, 0x31, 0x53,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x32, 0x10, 0x05, 0x1a, 0x2a, 0xf2, 0xf0, 0x30, 0x1d, 0x0a, 0x01,
	0x31, 0x12, 0x01, 0x32, 0x1a, 0x13, 0xd0, 0x92, 0x20, 0xd0, 0xbf, 0xd1, 0x80, 0xd0, 0xbe, 0xd1,
	0x86, 0xd0, 0xb5, 0xd1, 0x81, 0xd1, 0x81, 0xd0, 0xb5, 0x20, 0x04, 0xca, 0xa1, 0xec, 0x8e, 0x02,
	0x03, 0x31, 0x3a, 0x32, 0x12, 0x3d, 0x0a, 0x0d, 0x53, 0x74, 0x61, 0x67, 0x65, 0x32, 0x53, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x32, 0x10, 0x06, 0x1a, 0x2a, 0xf2, 0xf0, 0x30, 0x1d, 0x0a, 0x01, 0x32,
	0x12, 0x01, 0x32, 0x1a, 0x13, 0xd0, 0x92, 0x20, 0xd0, 0xbf, 0xd1, 0x80, 0xd0, 0xbe, 0xd1, 0x86,
	0xd0, 0xb5, 0xd1, 0x81, 0xd1, 0x81, 0xd0, 0xb5, 0x20, 0x04, 0xca, 0xa1, 0xec, 0x8e, 0x02, 0x03,
	0x32, 0x3a, 0x32, 0x12, 0x3d, 0x0a, 0x0d, 0x53, 0x74, 0x61, 0x67, 0x65, 0x33, 0x53, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x32, 0x10, 0x07, 0x1a, 0x2a, 0xf2, 0xf0, 0x30, 0x1d, 0x0a, 0x01, 0x33, 0x12,
	0x01, 0x32, 0x1a, 0x13, 0xd0, 0x92, 0x20, 0xd0, 0xbf, 0xd1, 0x80, 0xd0, 0xbe, 0xd1, 0x86, 0xd0,
	0xb5, 0xd1, 0x81, 0xd1, 0x81, 0xd0, 0xb5, 0x20, 0x04, 0xca, 0xa1, 0xec, 0x8e, 0x02, 0x03, 0x33,
	0x3a, 0x32, 0x12, 0x38, 0x0a, 0x0d, 0x53, 0x74, 0x61, 0x67, 0x65, 0x34, 0x53, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x30, 0x10, 0x08, 0x1a, 0x25, 0xf2, 0xf0, 0x30, 0x18, 0x0a, 0x01, 0x34, 0x12, 0x01,
	0x30, 0x1a, 0x0e, 0xd0, 0xa3, 0xd1, 0x81, 0xd0, 0xbf, 0xd0, 0xb5, 0xd1, 0x88, 0xd0, 0xbd, 0xd0,
	0xbe, 0x20, 0x09, 0xca, 0xa1, 0xec, 0x8e, 0x02, 0x03, 0x34, 0x3a, 0x30, 0x12, 0x38, 0x0a, 0x0d,
	0x53, 0x74, 0x61, 0x67, 0x65, 0x34, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x31, 0x10, 0x09, 0x1a,
	0x25, 0xf2, 0xf0, 0x30, 0x18, 0x0a, 0x01, 0x34, 0x12, 0x01, 0x31, 0x1a, 0x0e, 0xd0, 0xa3, 0xd1,
	0x81, 0xd0, 0xbf, 0xd0, 0xb5, 0xd1, 0x88, 0xd0, 0xbd, 0xd0, 0xbe, 0x20, 0x09, 0xca, 0xa1, 0xec,
	0x8e, 0x02, 0x03, 0x34, 0x3a, 0x31, 0x12, 0x38, 0x0a, 0x0d, 0x53, 0x74, 0x61, 0x67, 0x65, 0x34,
	0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x32, 0x10, 0x0a, 0x1a, 0x25, 0xf2, 0xf0, 0x30, 0x18, 0x0a,
	0x01, 0x34, 0x12, 0x01, 0x32, 0x1a, 0x0e, 0xd0, 0xa3, 0xd1, 0x81, 0xd0, 0xbf, 0xd0, 0xb5, 0xd1,
	0x88, 0xd0, 0xbd, 0xd0, 0xbe, 0x20, 0x09, 0xca, 0xa1, 0xec, 0x8e, 0x02, 0x03, 0x34, 0x3a, 0x32,
	0x1a, 0x59, 0xfa, 0xf0, 0x30, 0x0b, 0x0a, 0x01, 0x30, 0x12, 0x01, 0x30, 0x1a, 0x01, 0x30, 0x20,
	0x0f, 0xaa, 0x82, 0xec, 0x8e, 0x02, 0x23, 0x64, 0x65, 0x66, 0x61, 0x75, 0x6c, 0x74, 0x5f, 0x6a,
	0x75, 0x73, 0x61, 0x6e, 0x5f, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x5f, 0x73, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x5f, 0x76, 0x61, 0x6c, 0x75, 0x65, 0xb2, 0x82, 0xec, 0x8e, 0x02, 0x1b,
	0x6a, 0x75, 0x73, 0x61, 0x6e, 0x5f, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x5f, 0x73,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x5f, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x32, 0xea, 0x16, 0x0a, 0x05,
	0x4a, 0x75, 0x73, 0x61, 0x6e, 0x12, 0x82, 0x01, 0x0a, 0x05, 0x50, 0x61, 0x79, 0x49, 0x6e, 0x12,
	0x3a, 0x2e, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x2e, 0x6d, 0x75, 0x6c,
	0x74, 0x69, 0x61, 0x63, 0x71, 0x75, 0x69, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x6d, 0x75, 0x6c, 0x74,
	0x69, 0x61, 0x63, 0x71, 0x75, 0x69, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x50, 0x61, 0x79, 0x49, 0x6e,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x44, 0x61, 0x74, 0x61, 0x1a, 0x3b, 0x2e, 0x70, 0x72,
	0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x2e, 0x6d, 0x75, 0x6c, 0x74, 0x69, 0x61, 0x63,
	0x71, 0x75, 0x69, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x6d, 0x75, 0x6c, 0x74, 0x69, 0x61, 0x63, 0x71,
	0x75, 0x69, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x50, 0x61, 0x79, 0x49, 0x6e, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x44, 0x61, 0x74, 0x61, 0x22, 0x00, 0x12, 0x92, 0x01, 0x0a, 0x0d, 0x4f,
	0x6e, 0x65, 0x43, 0x6c, 0x69, 0x63, 0x6b, 0x50, 0x61, 0x79, 0x49, 0x6e, 0x12, 0x42, 0x2e, 0x70,
	0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x2e, 0x6d, 0x75, 0x6c, 0x74, 0x69, 0x61,
	0x63, 0x71, 0x75, 0x69, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x6d, 0x75, 0x6c, 0x74, 0x69, 0x61, 0x63,
	0x71, 0x75, 0x69, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x4f, 0x6e, 0x65, 0x43, 0x6c, 0x69, 0x63, 0x6b,
	0x50, 0x61, 0x79, 0x49, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x44, 0x61, 0x74, 0x61,
	0x1a, 0x3b, 0x2e, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x2e, 0x6d, 0x75,
	0x6c, 0x74, 0x69, 0x61, 0x63, 0x71, 0x75, 0x69, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x6d, 0x75, 0x6c,
	0x74, 0x69, 0x61, 0x63, 0x71, 0x75, 0x69, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x50, 0x61, 0x79, 0x49,
	0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x44, 0x61, 0x74, 0x61, 0x22, 0x00, 0x12,
	0x8f, 0x01, 0x0a, 0x0e, 0x54, 0x68, 0x72, 0x65, 0x65, 0x44, 0x53, 0x43, 0x6f, 0x6e, 0x66, 0x69,
	0x72, 0x6d, 0x12, 0x3c, 0x2e, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x2e,
	0x6d, 0x75, 0x6c, 0x74, 0x69, 0x61, 0x63, 0x71, 0x75, 0x69, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x6d,
	0x75, 0x6c, 0x74, 0x69, 0x61, 0x63, 0x71, 0x75, 0x69, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x54, 0x68,
	0x72, 0x65, 0x65, 0x44, 0x53, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x44, 0x61, 0x74, 0x61,
	0x1a, 0x3d, 0x2e, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x2e, 0x6d, 0x75,
	0x6c, 0x74, 0x69, 0x61, 0x63, 0x71, 0x75, 0x69, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x6d, 0x75, 0x6c,
	0x74, 0x69, 0x61, 0x63, 0x71, 0x75, 0x69, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x54, 0x68, 0x72, 0x65,
	0x65, 0x44, 0x53, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x44, 0x61, 0x74, 0x61, 0x22,
	0x00, 0x12, 0x92, 0x01, 0x0a, 0x0d, 0x54, 0x68, 0x72, 0x65, 0x65, 0x44, 0x53, 0x52, 0x65, 0x73,
	0x75, 0x6d, 0x65, 0x12, 0x3e, 0x2e, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67,
	0x2e, 0x6d, 0x75, 0x6c, 0x74, 0x69, 0x61, 0x63, 0x71, 0x75, 0x69, 0x72, 0x69, 0x6e, 0x67, 0x2e,
	0x6d, 0x75, 0x6c, 0x74, 0x69, 0x61, 0x63, 0x71, 0x75, 0x69, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x54,
	0x68, 0x72, 0x65, 0x65, 0x44, 0x53, 0x52, 0x65, 0x73, 0x75, 0x6d, 0x65, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x1a, 0x3f, 0x2e, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67,
	0x2e, 0x6d, 0x75, 0x6c, 0x74, 0x69, 0x61, 0x63, 0x71, 0x75, 0x69, 0x72, 0x69, 0x6e, 0x67, 0x2e,
	0x6d, 0x75, 0x6c, 0x74, 0x69, 0x61, 0x63, 0x71, 0x75, 0x69, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x54,
	0x68, 0x72, 0x65, 0x65, 0x44, 0x53, 0x52, 0x65, 0x73, 0x75, 0x6d, 0x65, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x85, 0x01, 0x0a, 0x06, 0x50, 0x61, 0x79, 0x4f, 0x75,
	0x74, 0x12, 0x3b, 0x2e, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x2e, 0x6d,
	0x75, 0x6c, 0x74, 0x69, 0x61, 0x63, 0x71, 0x75, 0x69, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x6d, 0x75,
	0x6c, 0x74, 0x69, 0x61, 0x63, 0x71, 0x75, 0x69, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x50, 0x61, 0x79,
	0x4f, 0x75, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x44, 0x61, 0x74, 0x61, 0x1a, 0x3c,
	0x2e, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x2e, 0x6d, 0x75, 0x6c, 0x74,
	0x69, 0x61, 0x63, 0x71, 0x75, 0x69, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x6d, 0x75, 0x6c, 0x74, 0x69,
	0x61, 0x63, 0x71, 0x75, 0x69, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x50, 0x61, 0x79, 0x4f, 0x75, 0x74,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x44, 0x61, 0x74, 0x61, 0x22, 0x00, 0x12, 0xad,
	0x01, 0x0a, 0x18, 0x47, 0x65, 0x74, 0x42, 0x61, 0x6e, 0x6b, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x61,
	0x63, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x46, 0x2e, 0x70, 0x72,
	0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x2e, 0x6d, 0x75, 0x6c, 0x74, 0x69, 0x61, 0x63,
	0x71, 0x75, 0x69, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x6d, 0x75, 0x6c, 0x74, 0x69, 0x61, 0x63, 0x71,
	0x75, 0x69, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x42, 0x61, 0x6e, 0x6b, 0x54, 0x72, 0x61, 0x6e, 0x73,
	0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x1a, 0x47, 0x2e, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67,
	0x2e, 0x6d, 0x75, 0x6c, 0x74, 0x69, 0x61, 0x63, 0x71, 0x75, 0x69, 0x72, 0x69, 0x6e, 0x67, 0x2e,
	0x6d, 0x75, 0x6c, 0x74, 0x69, 0x61, 0x63, 0x71, 0x75, 0x69, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x42,
	0x61, 0x6e, 0x6b, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0xcb,
	0x01, 0x0a, 0x22, 0x47, 0x65, 0x74, 0x42, 0x61, 0x6e, 0x6b, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x61,
	0x63, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x55, 0x6e, 0x66, 0x6f, 0x72,
	0x6d, 0x61, 0x74, 0x65, 0x64, 0x12, 0x50, 0x2e, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69,
	0x6e, 0x67, 0x2e, 0x6d, 0x75, 0x6c, 0x74, 0x69, 0x61, 0x63, 0x71, 0x75, 0x69, 0x72, 0x69, 0x6e,
	0x67, 0x2e, 0x6d, 0x75, 0x6c, 0x74, 0x69, 0x61, 0x63, 0x71, 0x75, 0x69, 0x72, 0x69, 0x6e, 0x67,
	0x2e, 0x42, 0x61, 0x6e, 0x6b, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e,
	0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x55, 0x6e, 0x66, 0x6f, 0x72, 0x6d, 0x61, 0x74, 0x65, 0x64,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x51, 0x2e, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73,
	0x73, 0x69, 0x6e, 0x67, 0x2e, 0x6d, 0x75, 0x6c, 0x74, 0x69, 0x61, 0x63, 0x71, 0x75, 0x69, 0x72,
	0x69, 0x6e, 0x67, 0x2e, 0x6d, 0x75, 0x6c, 0x74, 0x69, 0x61, 0x63, 0x71, 0x75, 0x69, 0x72, 0x69,
	0x6e, 0x67, 0x2e, 0x42, 0x61, 0x6e, 0x6b, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69,
	0x6f, 0x6e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x55, 0x6e, 0x66, 0x6f, 0x72, 0x6d, 0x61, 0x74,
	0x65, 0x64, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x7d, 0x0a, 0x06,
	0x52, 0x65, 0x66, 0x75, 0x6e, 0x64, 0x12, 0x37, 0x2e, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73,
	0x69, 0x6e, 0x67, 0x2e, 0x6d, 0x75, 0x6c, 0x74, 0x69, 0x61, 0x63, 0x71, 0x75, 0x69, 0x72, 0x69,
	0x6e, 0x67, 0x2e, 0x6d, 0x75, 0x6c, 0x74, 0x69, 0x61, 0x63, 0x71, 0x75, 0x69, 0x72, 0x69, 0x6e,
	0x67, 0x2e, 0x52, 0x65, 0x66, 0x75, 0x6e, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a,
	0x38, 0x2e, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x2e, 0x6d, 0x75, 0x6c,
	0x74, 0x69, 0x61, 0x63, 0x71, 0x75, 0x69, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x6d, 0x75, 0x6c, 0x74,
	0x69, 0x61, 0x63, 0x71, 0x75, 0x69, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x52, 0x65, 0x66, 0x75, 0x6e,
	0x64, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x8e, 0x01, 0x0a, 0x09,
	0x47, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x50, 0x61, 0x79, 0x12, 0x3e, 0x2e, 0x70, 0x72, 0x6f, 0x63,
	0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x2e, 0x6d, 0x75, 0x6c, 0x74, 0x69, 0x61, 0x63, 0x71, 0x75,
	0x69, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x6d, 0x75, 0x6c, 0x74, 0x69, 0x61, 0x63, 0x71, 0x75, 0x69,
	0x72, 0x69, 0x6e, 0x67, 0x2e, 0x47, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x50, 0x61, 0x79, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x44, 0x61, 0x74, 0x61, 0x1a, 0x3f, 0x2e, 0x70, 0x72, 0x6f, 0x63,
	0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x2e, 0x6d, 0x75, 0x6c, 0x74, 0x69, 0x61, 0x63, 0x71, 0x75,
	0x69, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x6d, 0x75, 0x6c, 0x74, 0x69, 0x61, 0x63, 0x71, 0x75, 0x69,
	0x72, 0x69, 0x6e, 0x67, 0x2e, 0x47, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x50, 0x61, 0x79, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x44, 0x61, 0x74, 0x61, 0x22, 0x00, 0x12, 0x8b, 0x01, 0x0a,
	0x08, 0x41, 0x70, 0x70, 0x6c, 0x65, 0x50, 0x61, 0x79, 0x12, 0x3d, 0x2e, 0x70, 0x72, 0x6f, 0x63,
	0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x2e, 0x6d, 0x75, 0x6c, 0x74, 0x69, 0x61, 0x63, 0x71, 0x75,
	0x69, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x6d, 0x75, 0x6c, 0x74, 0x69, 0x61, 0x63, 0x71, 0x75, 0x69,
	0x72, 0x69, 0x6e, 0x67, 0x2e, 0x41, 0x70, 0x70, 0x6c, 0x65, 0x50, 0x61, 0x79, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x44, 0x61, 0x74, 0x61, 0x1a, 0x3e, 0x2e, 0x70, 0x72, 0x6f, 0x63, 0x65,
	0x73, 0x73, 0x69, 0x6e, 0x67, 0x2e, 0x6d, 0x75, 0x6c, 0x74, 0x69, 0x61, 0x63, 0x71, 0x75, 0x69,
	0x72, 0x69, 0x6e, 0x67, 0x2e, 0x6d, 0x75, 0x6c, 0x74, 0x69, 0x61, 0x63, 0x71, 0x75, 0x69, 0x72,
	0x69, 0x6e, 0x67, 0x2e, 0x41, 0x70, 0x70, 0x6c, 0x65, 0x50, 0x61, 0x79, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x44, 0x61, 0x74, 0x61, 0x22, 0x00, 0x12, 0x92, 0x01, 0x0a, 0x0d, 0x54,
	0x77, 0x6f, 0x53, 0x74, 0x61, 0x67, 0x65, 0x50, 0x61, 0x79, 0x49, 0x6e, 0x12, 0x3e, 0x2e, 0x70,
	0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x2e, 0x6d, 0x75, 0x6c, 0x74, 0x69, 0x61,
	0x63, 0x71, 0x75, 0x69, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x6d, 0x75, 0x6c, 0x74, 0x69, 0x61, 0x63,
	0x71, 0x75, 0x69, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x54, 0x77, 0x6f, 0x53, 0x74, 0x61, 0x67, 0x65,
	0x50, 0x61, 0x79, 0x49, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x3f, 0x2e, 0x70,
	0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x2e, 0x6d, 0x75, 0x6c, 0x74, 0x69, 0x61,
	0x63, 0x71, 0x75, 0x69, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x6d, 0x75, 0x6c, 0x74, 0x69, 0x61, 0x63,
	0x71, 0x75, 0x69, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x54, 0x77, 0x6f, 0x53, 0x74, 0x61, 0x67, 0x65,
	0x50, 0x61, 0x79, 0x49, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12,
	0x7d, 0x0a, 0x06, 0x43, 0x68, 0x61, 0x72, 0x67, 0x65, 0x12, 0x37, 0x2e, 0x70, 0x72, 0x6f, 0x63,
	0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x2e, 0x6d, 0x75, 0x6c, 0x74, 0x69, 0x61, 0x63, 0x71, 0x75,
	0x69, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x6d, 0x75, 0x6c, 0x74, 0x69, 0x61, 0x63, 0x71, 0x75, 0x69,
	0x72, 0x69, 0x6e, 0x67, 0x2e, 0x43, 0x68, 0x61, 0x72, 0x67, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x1a, 0x38, 0x2e, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x2e,
	0x6d, 0x75, 0x6c, 0x74, 0x69, 0x61, 0x63, 0x71, 0x75, 0x69, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x6d,
	0x75, 0x6c, 0x74, 0x69, 0x61, 0x63, 0x71, 0x75, 0x69, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x43, 0x68,
	0x61, 0x72, 0x67, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x7d,
	0x0a, 0x06, 0x43, 0x61, 0x6e, 0x63, 0x65, 0x6c, 0x12, 0x37, 0x2e, 0x70, 0x72, 0x6f, 0x63, 0x65,
	0x73, 0x73, 0x69, 0x6e, 0x67, 0x2e, 0x6d, 0x75, 0x6c, 0x74, 0x69, 0x61, 0x63, 0x71, 0x75, 0x69,
	0x72, 0x69, 0x6e, 0x67, 0x2e, 0x6d, 0x75, 0x6c, 0x74, 0x69, 0x61, 0x63, 0x71, 0x75, 0x69, 0x72,
	0x69, 0x6e, 0x67, 0x2e, 0x43, 0x61, 0x6e, 0x63, 0x65, 0x6c, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x1a, 0x38, 0x2e, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x2e, 0x6d,
	0x75, 0x6c, 0x74, 0x69, 0x61, 0x63, 0x71, 0x75, 0x69, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x6d, 0x75,
	0x6c, 0x74, 0x69, 0x61, 0x63, 0x71, 0x75, 0x69, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x43, 0x61, 0x6e,
	0x63, 0x65, 0x6c, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x86, 0x01,
	0x0a, 0x09, 0x4d, 0x61, 0x6b, 0x65, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x12, 0x3a, 0x2e, 0x70, 0x72,
	0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x2e, 0x6d, 0x75, 0x6c, 0x74, 0x69, 0x61, 0x63,
	0x71, 0x75, 0x69, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x6d, 0x75, 0x6c, 0x74, 0x69, 0x61, 0x63, 0x71,
	0x75, 0x69, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x50, 0x61, 0x79, 0x49, 0x6e, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x44, 0x61, 0x74, 0x61, 0x1a, 0x3b, 0x2e, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73,
	0x73, 0x69, 0x6e, 0x67, 0x2e, 0x6d, 0x75, 0x6c, 0x74, 0x69, 0x61, 0x63, 0x71, 0x75, 0x69, 0x72,
	0x69, 0x6e, 0x67, 0x2e, 0x6d, 0x75, 0x6c, 0x74, 0x69, 0x61, 0x63, 0x71, 0x75, 0x69, 0x72, 0x69,
	0x6e, 0x67, 0x2e, 0x50, 0x61, 0x79, 0x49, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x44, 0x61, 0x74, 0x61, 0x22, 0x00, 0x12, 0x6c, 0x0a, 0x0b, 0x47, 0x65, 0x74, 0x45, 0x6d, 0x69,
	0x73, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x16, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x1a, 0x43, 0x2e,
	0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x2e, 0x6d, 0x75, 0x6c, 0x74, 0x69,
	0x61, 0x63, 0x71, 0x75, 0x69, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x6d, 0x75, 0x6c, 0x74, 0x69, 0x61,
	0x63, 0x71, 0x75, 0x69, 0x72, 0x69, 0x6e, 0x67, 0x5f, 0x65, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f,
	0x6e, 0x2e, 0x45, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x22, 0x00, 0x12, 0x98, 0x01, 0x0a, 0x0f, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x72, 0x6d,
	0x45, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x40, 0x2e, 0x70, 0x72, 0x6f, 0x63, 0x65,
	0x73, 0x73, 0x69, 0x6e, 0x67, 0x2e, 0x6d, 0x75, 0x6c, 0x74, 0x69, 0x61, 0x63, 0x71, 0x75, 0x69,
	0x72, 0x69, 0x6e, 0x67, 0x2e, 0x6d, 0x75, 0x6c, 0x74, 0x69, 0x61, 0x63, 0x71, 0x75, 0x69, 0x72,
	0x69, 0x6e, 0x67, 0x5f, 0x65, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x2e, 0x45, 0x6d, 0x6f,
	0x6e, 0x65, 0x79, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x41, 0x2e, 0x70, 0x72, 0x6f,
	0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x2e, 0x6d, 0x75, 0x6c, 0x74, 0x69, 0x61, 0x63, 0x71,
	0x75, 0x69, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x6d, 0x75, 0x6c, 0x74, 0x69, 0x61, 0x63, 0x71, 0x75,
	0x69, 0x72, 0x69, 0x6e, 0x67, 0x5f, 0x65, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x2e, 0x45,
	0x6d, 0x6f, 0x6e, 0x65, 0x79, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12,
	0x7a, 0x0a, 0x15, 0x47, 0x65, 0x74, 0x41, 0x63, 0x71, 0x75, 0x69, 0x72, 0x65, 0x72, 0x49, 0x64,
	0x65, 0x6e, 0x74, 0x69, 0x66, 0x69, 0x65, 0x72, 0x12, 0x16, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79,
	0x1a, 0x47, 0x2e, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x2e, 0x6d, 0x75,
	0x6c, 0x74, 0x69, 0x61, 0x63, 0x71, 0x75, 0x69, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x6d, 0x75, 0x6c,
	0x74, 0x69, 0x61, 0x63, 0x71, 0x75, 0x69, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x47, 0x65, 0x74, 0x41,
	0x63, 0x71, 0x75, 0x69, 0x72, 0x65, 0x72, 0x49, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x66, 0x69, 0x65,
	0x72, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x9f, 0x01, 0x0a, 0x0c,
	0x43, 0x68, 0x65, 0x63, 0x6b, 0x42, 0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65, 0x12, 0x45, 0x2e, 0x70,
	0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x2e, 0x6d, 0x75, 0x6c, 0x74, 0x69, 0x61,
	0x63, 0x71, 0x75, 0x69, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x6d, 0x75, 0x6c, 0x74, 0x69, 0x61, 0x63,
	0x71, 0x75, 0x69, 0x72, 0x69, 0x6e, 0x67, 0x5f, 0x62, 0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65, 0x2e,
	0x43, 0x68, 0x65, 0x63, 0x6b, 0x42, 0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x1a, 0x46, 0x2e, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67,
	0x2e, 0x6d, 0x75, 0x6c, 0x74, 0x69, 0x61, 0x63, 0x71, 0x75, 0x69, 0x72, 0x69, 0x6e, 0x67, 0x2e,
	0x6d, 0x75, 0x6c, 0x74, 0x69, 0x61, 0x63, 0x71, 0x75, 0x69, 0x72, 0x69, 0x6e, 0x67, 0x5f, 0x62,
	0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65, 0x2e, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x42, 0x61, 0x6c, 0x61,
	0x6e, 0x63, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x9b, 0x01,
	0x0a, 0x10, 0x52, 0x65, 0x73, 0x6f, 0x6c, 0x76, 0x65, 0x56, 0x69, 0x73, 0x61, 0x41, 0x6c, 0x69,
	0x61, 0x73, 0x12, 0x41, 0x2e, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x2e,
	0x6d, 0x75, 0x6c, 0x74, 0x69, 0x61, 0x63, 0x71, 0x75, 0x69, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x6d,
	0x75, 0x6c, 0x74, 0x69, 0x61, 0x63, 0x71, 0x75, 0x69, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x52, 0x65,
	0x73, 0x6f, 0x6c, 0x76, 0x65, 0x56, 0x69, 0x73, 0x61, 0x41, 0x6c, 0x69, 0x61, 0x73, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x42, 0x2e, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69,
	0x6e, 0x67, 0x2e, 0x6d, 0x75, 0x6c, 0x74, 0x69, 0x61, 0x63, 0x71, 0x75, 0x69, 0x72, 0x69, 0x6e,
	0x67, 0x2e, 0x6d, 0x75, 0x6c, 0x74, 0x69, 0x61, 0x63, 0x71, 0x75, 0x69, 0x72, 0x69, 0x6e, 0x67,
	0x2e, 0x52, 0x65, 0x73, 0x6f, 0x6c, 0x76, 0x65, 0x56, 0x69, 0x73, 0x61, 0x41, 0x6c, 0x69, 0x61,
	0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x9a, 0x01, 0x0a, 0x0d,
	0x50, 0x61, 0x79, 0x4f, 0x75, 0x74, 0x42, 0x79, 0x50, 0x68, 0x6f, 0x6e, 0x65, 0x12, 0x42, 0x2e,
	0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x2e, 0x6d, 0x75, 0x6c, 0x74, 0x69,
	0x61, 0x63, 0x71, 0x75, 0x69, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x6d, 0x75, 0x6c, 0x74, 0x69, 0x61,
	0x63, 0x71, 0x75, 0x69, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x50, 0x61, 0x79, 0x4f, 0x75, 0x74, 0x42,
	0x79, 0x50, 0x68, 0x6f, 0x6e, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x44, 0x61, 0x74,
	0x61, 0x1a, 0x43, 0x2e, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x2e, 0x6d,
	0x75, 0x6c, 0x74, 0x69, 0x61, 0x63, 0x71, 0x75, 0x69, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x6d, 0x75,
	0x6c, 0x74, 0x69, 0x61, 0x63, 0x71, 0x75, 0x69, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x50, 0x61, 0x79,
	0x4f, 0x75, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x42, 0x79, 0x50, 0x68, 0x6f,
	0x6e, 0x65, 0x44, 0x61, 0x74, 0x61, 0x22, 0x00, 0x3a, 0x92, 0x01, 0x0a, 0x1b, 0x6a, 0x75, 0x73,
	0x61, 0x6e, 0x5f, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x5f, 0x73, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x5f, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x12, 0x21, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x45, 0x6e, 0x75, 0x6d, 0x56,
	0x61, 0x6c, 0x75, 0x65, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x8e, 0x8e, 0x06, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x2e, 0x2e, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67,
	0x2e, 0x6a, 0x75, 0x73, 0x61, 0x6e, 0x2e, 0x6a, 0x75, 0x73, 0x61, 0x6e, 0x2e, 0x4a, 0x75, 0x73,
	0x61, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x52, 0x65, 0x66, 0x52, 0x18, 0x6a, 0x75, 0x73, 0x61, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x9c, 0x01,
	0x0a, 0x23, 0x64, 0x65, 0x66, 0x61, 0x75, 0x6c, 0x74, 0x5f, 0x6a, 0x75, 0x73, 0x61, 0x6e, 0x5f,
	0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x5f,
	0x76, 0x61, 0x6c, 0x75, 0x65, 0x12, 0x1c, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x45, 0x6e, 0x75, 0x6d, 0x4f, 0x70, 0x74, 0x69,
	0x6f, 0x6e, 0x73, 0x18, 0x8f, 0x8e, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2e, 0x2e, 0x70, 0x72,
	0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x2e, 0x6a, 0x75, 0x73, 0x61, 0x6e, 0x2e, 0x6a,
	0x75, 0x73, 0x61, 0x6e, 0x2e, 0x4a, 0x75, 0x73, 0x61, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65, 0x66, 0x52, 0x1f, 0x64, 0x65, 0x66,
	0x61, 0x75, 0x6c, 0x74, 0x4a, 0x75, 0x73, 0x61, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x42, 0x2d, 0x5a, 0x2b,
	0x67, 0x69, 0x74, 0x2e, 0x6c, 0x6f, 0x63, 0x61, 0x6c, 0x2f, 0x73, 0x65, 0x6e, 0x73, 0x69, 0x74,
	0x69, 0x76, 0x65, 0x2f, 0x69, 0x6e, 0x6e, 0x65, 0x72, 0x70, 0x62, 0x2f, 0x70, 0x72, 0x6f, 0x63,
	0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x2f, 0x67, 0x72, 0x70, 0x63, 0x62, 0x08, 0x65, 0x64, 0x69,
	0x74, 0x69, 0x6f, 0x6e, 0x73, 0x70, 0xe8, 0x07,
})

var (
	file_inner_processing_grpc_jusan_proto_rawDescOnce sync.Once
	file_inner_processing_grpc_jusan_proto_rawDescData []byte
)

func file_inner_processing_grpc_jusan_proto_rawDescGZIP() []byte {
	file_inner_processing_grpc_jusan_proto_rawDescOnce.Do(func() {
		file_inner_processing_grpc_jusan_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_inner_processing_grpc_jusan_proto_rawDesc), len(file_inner_processing_grpc_jusan_proto_rawDesc)))
	})
	return file_inner_processing_grpc_jusan_proto_rawDescData
}

var file_inner_processing_grpc_jusan_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_inner_processing_grpc_jusan_proto_msgTypes = make([]protoimpl.MessageInfo, 1)
var file_inner_processing_grpc_jusan_proto_goTypes = []any{
	(JusanResponseStatus)(0),                        // 0: processing.jusan.jusan.JusanResponseStatus
	(*JusanResponseStatusRef)(nil),                  // 1: processing.jusan.jusan.JusanResponseStatusRef
	(EnumTransactionStatus)(0),                      // 2: processing.transaction.transaction_status.EnumTransactionStatus
	(*descriptorpb.EnumValueOptions)(nil),           // 3: google.protobuf.EnumValueOptions
	(*descriptorpb.EnumOptions)(nil),                // 4: google.protobuf.EnumOptions
	(*PayInRequestData)(nil),                        // 5: processing.multiacquiring.multiacquiring.PayInRequestData
	(*OneClickPayInRequestData)(nil),                // 6: processing.multiacquiring.multiacquiring.OneClickPayInRequestData
	(*ThreeDSRequestData)(nil),                      // 7: processing.multiacquiring.multiacquiring.ThreeDSRequestData
	(*ThreeDSResumeRequest)(nil),                    // 8: processing.multiacquiring.multiacquiring.ThreeDSResumeRequest
	(*PayOutRequestData)(nil),                       // 9: processing.multiacquiring.multiacquiring.PayOutRequestData
	(*BankTransactionStatusRequest)(nil),            // 10: processing.multiacquiring.multiacquiring.BankTransactionStatusRequest
	(*BankTransactionStatusUnformatedRequest)(nil),  // 11: processing.multiacquiring.multiacquiring.BankTransactionStatusUnformatedRequest
	(*RefundRequest)(nil),                           // 12: processing.multiacquiring.multiacquiring.RefundRequest
	(*GooglePayRequestData)(nil),                    // 13: processing.multiacquiring.multiacquiring.GooglePayRequestData
	(*ApplePayRequestData)(nil),                     // 14: processing.multiacquiring.multiacquiring.ApplePayRequestData
	(*TwoStagePayInRequest)(nil),                    // 15: processing.multiacquiring.multiacquiring.TwoStagePayInRequest
	(*ChargeRequest)(nil),                           // 16: processing.multiacquiring.multiacquiring.ChargeRequest
	(*CancelRequest)(nil),                           // 17: processing.multiacquiring.multiacquiring.CancelRequest
	(*emptypb.Empty)(nil),                           // 18: google.protobuf.Empty
	(*EmoneyRequest)(nil),                           // 19: processing.multiacquiring.multiacquiring_emission.EmoneyRequest
	(*CheckBalanceRequest)(nil),                     // 20: processing.multiacquiring.multiacquiring_balance.CheckBalanceRequest
	(*ResolveVisaAliasRequest)(nil),                 // 21: processing.multiacquiring.multiacquiring.ResolveVisaAliasRequest
	(*PayOutByPhoneRequestData)(nil),                // 22: processing.multiacquiring.multiacquiring.PayOutByPhoneRequestData
	(*PayInResponseData)(nil),                       // 23: processing.multiacquiring.multiacquiring.PayInResponseData
	(*ThreeDSResponseData)(nil),                     // 24: processing.multiacquiring.multiacquiring.ThreeDSResponseData
	(*ThreeDSResumeResponse)(nil),                   // 25: processing.multiacquiring.multiacquiring.ThreeDSResumeResponse
	(*PayOutResponseData)(nil),                      // 26: processing.multiacquiring.multiacquiring.PayOutResponseData
	(*BankTransactionStatusResponse)(nil),           // 27: processing.multiacquiring.multiacquiring.BankTransactionStatusResponse
	(*BankTransactionStatusUnformatedResponse)(nil), // 28: processing.multiacquiring.multiacquiring.BankTransactionStatusUnformatedResponse
	(*RefundResponse)(nil),                          // 29: processing.multiacquiring.multiacquiring.RefundResponse
	(*GooglePayResponseData)(nil),                   // 30: processing.multiacquiring.multiacquiring.GooglePayResponseData
	(*ApplePayResponseData)(nil),                    // 31: processing.multiacquiring.multiacquiring.ApplePayResponseData
	(*TwoStagePayInResponse)(nil),                   // 32: processing.multiacquiring.multiacquiring.TwoStagePayInResponse
	(*ChargeResponse)(nil),                          // 33: processing.multiacquiring.multiacquiring.ChargeResponse
	(*CancelResponse)(nil),                          // 34: processing.multiacquiring.multiacquiring.CancelResponse
	(*EmissionResponse)(nil),                        // 35: processing.multiacquiring.multiacquiring_emission.EmissionResponse
	(*EmoneyResponse)(nil),                          // 36: processing.multiacquiring.multiacquiring_emission.EmoneyResponse
	(*GetAcquirerIdentifierResponse)(nil),           // 37: processing.multiacquiring.multiacquiring.GetAcquirerIdentifierResponse
	(*CheckBalanceResponse)(nil),                    // 38: processing.multiacquiring.multiacquiring_balance.CheckBalanceResponse
	(*ResolveVisaAliasResponse)(nil),                // 39: processing.multiacquiring.multiacquiring.ResolveVisaAliasResponse
	(*PayOutResponseByPhoneData)(nil),               // 40: processing.multiacquiring.multiacquiring.PayOutResponseByPhoneData
}
var file_inner_processing_grpc_jusan_proto_depIdxs = []int32{
	2,  // 0: processing.jusan.jusan.JusanResponseStatusRef.transaction_status:type_name -> processing.transaction.transaction_status.EnumTransactionStatus
	3,  // 1: processing.jusan.jusan.jusan_response_status_value:extendee -> google.protobuf.EnumValueOptions
	4,  // 2: processing.jusan.jusan.default_jusan_response_status_value:extendee -> google.protobuf.EnumOptions
	1,  // 3: processing.jusan.jusan.jusan_response_status_value:type_name -> processing.jusan.jusan.JusanResponseStatusRef
	1,  // 4: processing.jusan.jusan.default_jusan_response_status_value:type_name -> processing.jusan.jusan.JusanResponseStatusRef
	5,  // 5: processing.jusan.jusan.Jusan.PayIn:input_type -> processing.multiacquiring.multiacquiring.PayInRequestData
	6,  // 6: processing.jusan.jusan.Jusan.OneClickPayIn:input_type -> processing.multiacquiring.multiacquiring.OneClickPayInRequestData
	7,  // 7: processing.jusan.jusan.Jusan.ThreeDSConfirm:input_type -> processing.multiacquiring.multiacquiring.ThreeDSRequestData
	8,  // 8: processing.jusan.jusan.Jusan.ThreeDSResume:input_type -> processing.multiacquiring.multiacquiring.ThreeDSResumeRequest
	9,  // 9: processing.jusan.jusan.Jusan.PayOut:input_type -> processing.multiacquiring.multiacquiring.PayOutRequestData
	10, // 10: processing.jusan.jusan.Jusan.GetBankTransactionStatus:input_type -> processing.multiacquiring.multiacquiring.BankTransactionStatusRequest
	11, // 11: processing.jusan.jusan.Jusan.GetBankTransactionStatusUnformated:input_type -> processing.multiacquiring.multiacquiring.BankTransactionStatusUnformatedRequest
	12, // 12: processing.jusan.jusan.Jusan.Refund:input_type -> processing.multiacquiring.multiacquiring.RefundRequest
	13, // 13: processing.jusan.jusan.Jusan.GooglePay:input_type -> processing.multiacquiring.multiacquiring.GooglePayRequestData
	14, // 14: processing.jusan.jusan.Jusan.ApplePay:input_type -> processing.multiacquiring.multiacquiring.ApplePayRequestData
	15, // 15: processing.jusan.jusan.Jusan.TwoStagePayIn:input_type -> processing.multiacquiring.multiacquiring.TwoStagePayInRequest
	16, // 16: processing.jusan.jusan.Jusan.Charge:input_type -> processing.multiacquiring.multiacquiring.ChargeRequest
	17, // 17: processing.jusan.jusan.Jusan.Cancel:input_type -> processing.multiacquiring.multiacquiring.CancelRequest
	5,  // 18: processing.jusan.jusan.Jusan.MakeToken:input_type -> processing.multiacquiring.multiacquiring.PayInRequestData
	18, // 19: processing.jusan.jusan.Jusan.GetEmission:input_type -> google.protobuf.Empty
	19, // 20: processing.jusan.jusan.Jusan.ConfirmEmission:input_type -> processing.multiacquiring.multiacquiring_emission.EmoneyRequest
	18, // 21: processing.jusan.jusan.Jusan.GetAcquirerIdentifier:input_type -> google.protobuf.Empty
	20, // 22: processing.jusan.jusan.Jusan.CheckBalance:input_type -> processing.multiacquiring.multiacquiring_balance.CheckBalanceRequest
	21, // 23: processing.jusan.jusan.Jusan.ResolveVisaAlias:input_type -> processing.multiacquiring.multiacquiring.ResolveVisaAliasRequest
	22, // 24: processing.jusan.jusan.Jusan.PayOutByPhone:input_type -> processing.multiacquiring.multiacquiring.PayOutByPhoneRequestData
	23, // 25: processing.jusan.jusan.Jusan.PayIn:output_type -> processing.multiacquiring.multiacquiring.PayInResponseData
	23, // 26: processing.jusan.jusan.Jusan.OneClickPayIn:output_type -> processing.multiacquiring.multiacquiring.PayInResponseData
	24, // 27: processing.jusan.jusan.Jusan.ThreeDSConfirm:output_type -> processing.multiacquiring.multiacquiring.ThreeDSResponseData
	25, // 28: processing.jusan.jusan.Jusan.ThreeDSResume:output_type -> processing.multiacquiring.multiacquiring.ThreeDSResumeResponse
	26, // 29: processing.jusan.jusan.Jusan.PayOut:output_type -> processing.multiacquiring.multiacquiring.PayOutResponseData
	27, // 30: processing.jusan.jusan.Jusan.GetBankTransactionStatus:output_type -> processing.multiacquiring.multiacquiring.BankTransactionStatusResponse
	28, // 31: processing.jusan.jusan.Jusan.GetBankTransactionStatusUnformated:output_type -> processing.multiacquiring.multiacquiring.BankTransactionStatusUnformatedResponse
	29, // 32: processing.jusan.jusan.Jusan.Refund:output_type -> processing.multiacquiring.multiacquiring.RefundResponse
	30, // 33: processing.jusan.jusan.Jusan.GooglePay:output_type -> processing.multiacquiring.multiacquiring.GooglePayResponseData
	31, // 34: processing.jusan.jusan.Jusan.ApplePay:output_type -> processing.multiacquiring.multiacquiring.ApplePayResponseData
	32, // 35: processing.jusan.jusan.Jusan.TwoStagePayIn:output_type -> processing.multiacquiring.multiacquiring.TwoStagePayInResponse
	33, // 36: processing.jusan.jusan.Jusan.Charge:output_type -> processing.multiacquiring.multiacquiring.ChargeResponse
	34, // 37: processing.jusan.jusan.Jusan.Cancel:output_type -> processing.multiacquiring.multiacquiring.CancelResponse
	23, // 38: processing.jusan.jusan.Jusan.MakeToken:output_type -> processing.multiacquiring.multiacquiring.PayInResponseData
	35, // 39: processing.jusan.jusan.Jusan.GetEmission:output_type -> processing.multiacquiring.multiacquiring_emission.EmissionResponse
	36, // 40: processing.jusan.jusan.Jusan.ConfirmEmission:output_type -> processing.multiacquiring.multiacquiring_emission.EmoneyResponse
	37, // 41: processing.jusan.jusan.Jusan.GetAcquirerIdentifier:output_type -> processing.multiacquiring.multiacquiring.GetAcquirerIdentifierResponse
	38, // 42: processing.jusan.jusan.Jusan.CheckBalance:output_type -> processing.multiacquiring.multiacquiring_balance.CheckBalanceResponse
	39, // 43: processing.jusan.jusan.Jusan.ResolveVisaAlias:output_type -> processing.multiacquiring.multiacquiring.ResolveVisaAliasResponse
	40, // 44: processing.jusan.jusan.Jusan.PayOutByPhone:output_type -> processing.multiacquiring.multiacquiring.PayOutResponseByPhoneData
	25, // [25:45] is the sub-list for method output_type
	5,  // [5:25] is the sub-list for method input_type
	3,  // [3:5] is the sub-list for extension type_name
	1,  // [1:3] is the sub-list for extension extendee
	0,  // [0:1] is the sub-list for field type_name
}

func init() { file_inner_processing_grpc_jusan_proto_init() }
func file_inner_processing_grpc_jusan_proto_init() {
	if File_inner_processing_grpc_jusan_proto != nil {
		return
	}
	file_inner_processing_grpc_multiacquiring_proto_init()
	file_inner_processing_grpc_multiacquiring_emission_proto_init()
	file_inner_processing_grpc_multiacquiring_balance_proto_init()
	file_inner_processing_grpc_transaction_status_proto_init()
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_inner_processing_grpc_jusan_proto_rawDesc), len(file_inner_processing_grpc_jusan_proto_rawDesc)),
			NumEnums:      1,
			NumMessages:   1,
			NumExtensions: 2,
			NumServices:   1,
		},
		GoTypes:           file_inner_processing_grpc_jusan_proto_goTypes,
		DependencyIndexes: file_inner_processing_grpc_jusan_proto_depIdxs,
		EnumInfos:         file_inner_processing_grpc_jusan_proto_enumTypes,
		MessageInfos:      file_inner_processing_grpc_jusan_proto_msgTypes,
		ExtensionInfos:    file_inner_processing_grpc_jusan_proto_extTypes,
	}.Build()
	File_inner_processing_grpc_jusan_proto = out.File
	file_inner_processing_grpc_jusan_proto_goTypes = nil
	file_inner_processing_grpc_jusan_proto_depIdxs = nil
}
