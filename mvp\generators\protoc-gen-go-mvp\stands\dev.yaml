agws.binder.binder.Binder: agws-deployment-binder:4040
agws.commission.commission.Commission: agws-deployment-commission:4040
agws.transaction.transaction.TransactionService: agws-deployment-transaction:4040
agws.session.session.SessionService: agws-deployment-session:4040
agws.billing.billing.Billing: agws-deployment-billing:4040
agws.showcase.showcase.Showcase: agws-deployment-showcase:4040
agws.fcredit.fcredit.FCredit: agws-deployment-fcredit:4040
agws.showcase.showcase_worker.ShowcaseWorker: agws-deployment-showcase:4040
agws.expert_plus.expert_plus.ExpertPlus: agws-deployment-expert-plus:4040
agws.finalization.finalization.Finalization: agws-deployment-finalization:4040
agws.gourmet.gourmet.Gourmet: agws-deployment-gourmet:4040
agws.id_collect.id_collect.IdCollect: agws-deployment-id-collect:4040
agws.integration.integration.Integration: agws-deployment-integration:4040
agws.provider.provider.Provider: agws-deployment-service-provider:4040
agws.fiscalization.fiscalization.Fiscalization: agws-deployment-fiscalization:4040
agws.fiscalization.fiscalization_worker.FiscalizationWorker: agws-deployment-fiscalization:4040
agws.sxodim.sxodim.Sxodim: agws-deployment-sxodim:4040
agws.xpost.xpost.Xpost: agws-deployment-xpost:4040
agws.betsson.betsson.Betsson: agws-deployment-betsson:4040
agws.mock_provider.mock_provider.MockProvider: agws-deployment-mock-provider:4040
agws.kaspi.kaspi.Kaspi: agws-deployment-kaspi:4040
agws.parqour.parqour.Parqour: agws-deployment-parqour:4040
agws.paydala.paydala.Paydala: agws-deployment-paydala:4040
agws.astanapark.astanapark.Astanapark: agws-deployment-astanapark:4040
agws.kassa24.kassa24.Kassa24: agws-deployment-kassa24:4040
agws.kassa24.kassa24_finance.Kassa24Finance: agws-deployment-kassa24-finance:4040
agws.kassa24.kassa24_utilities.Kassa24Utilities: agws-deployment-kassa24-utilities:4040
agws.kassa24.kassa24_dynamic.Kassa24Dynamic: agws-deployment-kassa24-dynamic:4040
agws.simply.simply.Simply: agws-deployment-simply:4040
agws.qiwi.qiwi.Qiwi: agws-deployment-qiwi:4040
agws.quick_money.quick_money.QuickMoney: agws-deployment-quick-money:4040
agws.quick_money_prolongation.quick_money_prolongation.QuickMoneyProlongation: agws-deployment-quick-money-prolongation:4040
agws.rps.rps.RPS: agws-deployment-rps:4040
agws.rps.rps_worker.RpsWorker: agws-deployment-rps:4040
agws.rps.rps_phone.RPSPhone: agws-deployment-account:4040
agws.rps.rps_iin_cashout.RPSIinCashout: agws-deployment-account:4040
agws.rps.rps_wallet.RPSWallet: agws-deployment-account:4040
agws.rps.rps_account.RPSAccount: agws-deployment-rps-account:4040
agws.woopay.woopay.Woopay: agws-deployment-woopay:4040
agws.woopay.woopay_atm.WoopayAtm: agws-deployment-woopay-atm:4040
agws.woopay.woopay_payin_mobile.WoopayPayinMobile: agws-deployment-woopay-payin-mobile:4040
agws.woopay.woopay_payout_mobile.WoopayPayoutMobile: agws-deployment-woopay-payout-mobile:4040
agws.woopay.woopay_wallet.WoopayWallet: agws-deployment-woopay-wallet:4040
agws.tengebai.tengebai.Tengebai: agws-deployment-tengebai:4040
agws.xbet.xbet.XBet: agws-deployment-xbet:4040
agws.pay_gate.pay_gate.PayGate: agws-deployment-paygate:4040
agws.ccloan.ccloan.CCLoan: agws-deployment-ccloan:4040
agws.ccloan.ccloan_prolongation.CCLoanProlongation: agws-deployment-ccloan-prolongation:4040
agws.zaimer.zaimer.Zaimer: agws-deployment-zaimer:4040
agws.eosi.eosi.Eosi: agws-deployment-eosi:4040
agws.creditbar.creditbar.Creditbar: agws-deployment-creditbar:4040
bus.anti_fraud.collector.Collector: bus-deployment-collector:4040
bus.faq.faq.Faq: bus-deployment-faq:4040
bus.krakend.krakend.Krakend: merchant-deployment-onboarding:4040
bus.notification.notification.Notification: bus-deployment-notification:4040
merchant.onboarding.onboarding.Onboarding: merchant-deployment-onboarding:4040
merchant.send_email.send_email.SendEmail: merchant-deployment-onboarding:4040
merchant.view_crafter.view_crafter.ViewCrafter: merchant-deployment-view-crafter:4040
processing.account.account.Account: processing-deployment-account:4040
processing.acquirer.acquirer.Acquirer: processing-deployment-acquirer:4040
processing.acquirer.terminal.Terminal: processing-deployment-acquirer:4040
processing.acquirer.terminal_cards.TerminalCards: processing-deployment-acquirer:4040
processing.alatau_city_bank.alatau_city_bank.AlatauCityBank: processing-deployment-alatau-city-bank:4040
processing.bcc.bcc.BCC: processing-deployment-bcc:4040
processing.bcc_account.bcc_account.BCCAccount: processing-deployment-bcc-account:4040
processing.billing.billing.Billing: processing-deployment-billing:4040
processing.card.card.Card: processing-deployment-card:4040
processing.commission.commission.Commission: processing-deployment-commission:4040
processing.compliance.compliance.Test: Processing_Test_addr:4000
processing.epay.epay.Epay: processing-deployment-epay:4040
processing.jusan.jusan.Jusan: processing-deployment-jusan:4040
processing.kalkan.kalkan.Kalkan: processing-deployment-kalkan:4040
processing.merchant.merchant.Merchant: processing-deployment-merchant:4040
processing.merchant.project.Project: processing-deployment-merchant:4040
processing.merchant.project_transactions.ProjectTransactions: processing-deployment-merchant:4040
processing.multi_acquiring.multi_acquiring.MultiAcquiring: processing-deployment-multiacquiring:4040
processing.smart_pay.smart_pay.SmartPay: processing-deployment-smartpay:4040
processing.splitting.splitting.Splitting: processing-deployment-splitting:4040
processing.transaction.transaction.Transaction: processing-deployment-transaction:4040
processing.transaction.transaction_callback.TransactionCallback: processing-deployment-transaction:4040
processing.transaction.transaction_info.TransactionInf: processing-deployment-transaction:4040
processing.transaction.transaction_refund.Refund: processing-deployment-refund:4040
processing.transaction.transaction_status.TransactionStatus: processing-deployment-transaction:4040
processing.transaction.transaction_type.TransactionType: processing-deployment-transaction:4040
processing.anti_fraud.collector.Collector: processing-deployment-antifraud:4040
processing.compliance.compliance.Compliance: processing-deployment-compliance:4040
processing.report_merchant.report_merchant_worker.ReportMerchantWorker: processing-deployment-report-merchant:4040
processing.eosi.eosi.Eosi: processing-deployment-eosi:4040
processing.fiscalization.fiscalization.Fiscalization: processing-deployment-fiscalization:4040
processing.magnetiq.magnetiq.Magnetiq: processing-deployment-magnetiq:4040
processing.mock_acquirer.mock_acquirer.MockAcquirer: processing-deployment-mock-acquirer:4040
processing.multiaccounting.multiaccounting.Multiaccounting: processing-deployment-multiaccounting:4040
processing.multiacquiring.multiacquiring.MultiAcquiring: processing-deployment-multiacquiring:4040
processing.multiacquiring.multiacquiring_balance.MultiacquiringBalance: processing-deployment-multiacquiring:4040
processing.multiacquiring.multiacquiring_emission.MultiacquiringEmission: processing-deployment-multiacquiring:4040
processing.multiordering.multiordering.Multiordering: processing-deployment-multiordering:4040
processing.notification.notification.Notification: processing-deployment-notification:4040
processing.payorder.payorder.Payorder: processing-deployment-payorder:4040
processing.view_crafter.view_crafter.ViewCrafter: processing-deployment-view-crafter:4040
processing.transfer_automatic.transfer_automatic.TransferAutomatic: processing-deployment-transfer-automatic:4040
processing.phone.phone.Phone: processing-deployment-phone:4040