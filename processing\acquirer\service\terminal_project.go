package service

import (
	"context"
	"git.local/sensitive/innerpb/processing/goerr"
	gorpc "git.local/sensitive/innerpb/processing/grpc"
	"git.local/sensitive/processing/acquirer/repository/database"
	"git.local/sensitive/processing/acquirer/schema"
	"git.local/sensitive/sdk/dog"
	"go.uber.org/zap"
)

type TerminalProjectService struct {
	terminalProjectRepo   database.TerminalProjecter
	transactionTypeClient gorpc.TransactionTypeClient
}

func NewTerminalProjectService(
	terminalProjectRepo database.TerminalProjecter,
	transactionTypeClient gorpc.TransactionTypeClient,
) *TerminalProjectService {
	return &TerminalProjectService{
		terminalProjectRepo:   terminalProjectRepo,
		transactionTypeClient: transactionTypeClient,
	}
}

func (t *TerminalProjectService) UpdateStatus(
	ctx context.Context,
	id uint64,
	request schema.UpdateTerminalProjectStatusRequest,
) (err error) {
	ctx, span := dog.CreateSpan(ctx, "TerminalProjectService_UpdateStatus")
	defer span.End()

	return t.terminalProjectRepo.UpdateStatus(ctx, id, request)
}

func (t *TerminalProjectService) Update(
	ctx context.Context,
	id uint64, request schema.UpdateTerminalProjectRequest,
) (err error) {
	ctx, span := dog.CreateSpan(ctx, "TerminalProjectService_Update")
	defer span.End()

	return t.terminalProjectRepo.Update(ctx, id, request)
}

func (t *TerminalProjectService) Create(ctx context.Context, request schema.TerminalProjectRequest) (err error) {
	ctx, span := dog.CreateSpan(ctx, "TerminalProjectService_Create")
	defer span.End()

	err = t.terminalProjectRepo.Create(ctx, request.ToModel())
	if err != nil {
		dog.L().Error("TerminalProjectService_Create", zap.Any("request", request), zap.Uint64("terminal_id", request.TerminalID),
			zap.Error(err))
		return err
	}

	return nil
}

func (t *TerminalProjectService) GetPayInProjectTerminals(
	ctx context.Context,
	id uint64,
) ([]*schema.GetTerminalProjectPayIn, error) {
	ctx, span := dog.CreateSpan(ctx, "TerminalProjectService_GetPayInProjectTerminals")
	defer span.End()

	terminalProjects, err := t.terminalProjectRepo.GetByProjectID(ctx, id)
	if err != nil {
		return nil, err
	}

	resp, err := t.transactionTypeClient.GetTransactionPayInTypes(ctx, nil)
	if err != nil {
		return nil, err
	}

	transactionTypes := resp.GetData()
	if transactionTypes == nil {
		return nil, goerr.ErrTransactionTypeNotFound
	}

	typeMap := make(map[uint64]*gorpc.TransactionTypesResponseV1)
	for _, trType := range transactionTypes {
		typeMap[trType.GetId()] = trType
	}

	var (
		terminals         = make([]*schema.GetTerminalProjectPayIn, 0)
		uniqueTerminalIDs = make(map[uint64]struct{})
	)

	for _, terminalProject := range terminalProjects {
		if trType, ok := typeMap[terminalProject.TransactionTypeID]; ok {
			if _, exists := uniqueTerminalIDs[terminalProject.TerminalID]; !exists {
				terminals = append(terminals, &schema.GetTerminalProjectPayIn{
					ID:                terminalProject.ID,
					ProjectID:         terminalProject.ProjectID,
					TransactionTypeID: terminalProject.TransactionTypeID,
					TerminalID:        terminalProject.TerminalID,
					IsActive:          terminalProject.IsActive,
					Name:              trType.GetName(),
					Code:              trType.GetCode(),
					CreatedAt:         terminalProject.TimestampMixin.CreatedAt,
					UpdatedAt:         terminalProject.TimestampMixin.UpdatedAt,
				})
				uniqueTerminalIDs[terminalProject.TerminalID] = struct{}{}
			}
		}
	}

	if len(terminals) == 0 {
		return nil, goerr.ErrProjectTerminalNotFound.WithCtx(ctx)
	}

	return terminals, nil
}
