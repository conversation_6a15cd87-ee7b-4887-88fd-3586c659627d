edition = "2023";

package processing.report_merchant.report_merchant_worker;

option go_package = "git.local/sensitive/innerpb/processing/grpc";

import "google/protobuf/empty.proto";

service ReportMerchantWorker {
  rpc ProcessReportScheduleByPeriodType(ProcessReportScheduleByPeriodTypeRequest) returns (google.protobuf.Empty) {}
}

message ProcessReportScheduleByPeriodTypeRequest {
  ReportSchedulePeriodType report_schedule_period_type = 1;
}

enum ReportSchedulePeriodType {
  ReportSchedulePeriodTypeUnknown = 0;
  ReportSchedulePeriodTypeDaily = 1;
  ReportSchedulePeriodTypeWeekly = 2;
  ReportSchedulePeriodTypeMonthly = 3;
}