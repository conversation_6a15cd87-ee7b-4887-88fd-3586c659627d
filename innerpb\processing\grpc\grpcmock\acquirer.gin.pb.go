// Code generated by MockGen. DO NOT EDIT.
// Source: acquirer.gin.pb.go

// Package grpcmock is a generated GoMock package.
package grpcmock

import (
	reflect "reflect"

	gin "github.com/gin-gonic/gin"
	gomock "github.com/golang/mock/gomock"
)

// MockGinAcquirerServer is a mock of GinAcquirerServer interface.
type MockGinAcquirerServer struct {
	ctrl     *gomock.Controller
	recorder *MockGinAcquirerServerMockRecorder
}

// MockGinAcquirerServerMockRecorder is the mock recorder for MockGinAcquirerServer.
type MockGinAcquirerServerMockRecorder struct {
	mock *MockGinAcquirerServer
}

// NewMockGinAcquirerServer creates a new mock instance.
func NewMockGinAcquirerServer(ctrl *gomock.Controller) *MockGinAcquirerServer {
	mock := &MockGinAcquirerServer{ctrl: ctrl}
	mock.recorder = &MockGinAcquirerServerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockGinAcquirerServer) EXPECT() *MockGinAcquirerServerMockRecorder {
	return m.recorder
}

// GetAcquirerByID mocks base method.
func (m *MockGinAcquirerServer) GetAcquirerByID(c *gin.Context) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAcquirerByID", c)
	ret0, _ := ret[0].(error)
	return ret0
}

// GetAcquirerByID indicates an expected call of GetAcquirerByID.
func (mr *MockGinAcquirerServerMockRecorder) GetAcquirerByID(c interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAcquirerByID", reflect.TypeOf((*MockGinAcquirerServer)(nil).GetAcquirerByID), c)
}

// GetAcquirers mocks base method.
func (m *MockGinAcquirerServer) GetAcquirers(c *gin.Context) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAcquirers", c)
	ret0, _ := ret[0].(error)
	return ret0
}

// GetAcquirers indicates an expected call of GetAcquirers.
func (mr *MockGinAcquirerServerMockRecorder) GetAcquirers(c interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAcquirers", reflect.TypeOf((*MockGinAcquirerServer)(nil).GetAcquirers), c)
}

// GetBankByID mocks base method.
func (m *MockGinAcquirerServer) GetBankByID(c *gin.Context) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetBankByID", c)
	ret0, _ := ret[0].(error)
	return ret0
}

// GetBankByID indicates an expected call of GetBankByID.
func (mr *MockGinAcquirerServerMockRecorder) GetBankByID(c interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetBankByID", reflect.TypeOf((*MockGinAcquirerServer)(nil).GetBankByID), c)
}

// GetCountryByID mocks base method.
func (m *MockGinAcquirerServer) GetCountryByID(c *gin.Context) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetCountryByID", c)
	ret0, _ := ret[0].(error)
	return ret0
}

// GetCountryByID indicates an expected call of GetCountryByID.
func (mr *MockGinAcquirerServerMockRecorder) GetCountryByID(c interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetCountryByID", reflect.TypeOf((*MockGinAcquirerServer)(nil).GetCountryByID), c)
}

// GetIpsByID mocks base method.
func (m *MockGinAcquirerServer) GetIpsByID(c *gin.Context) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetIpsByID", c)
	ret0, _ := ret[0].(error)
	return ret0
}

// GetIpsByID indicates an expected call of GetIpsByID.
func (mr *MockGinAcquirerServerMockRecorder) GetIpsByID(c interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetIpsByID", reflect.TypeOf((*MockGinAcquirerServer)(nil).GetIpsByID), c)
}

// GetIssuerByID mocks base method.
func (m *MockGinAcquirerServer) GetIssuerByID(c *gin.Context) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetIssuerByID", c)
	ret0, _ := ret[0].(error)
	return ret0
}

// GetIssuerByID indicates an expected call of GetIssuerByID.
func (mr *MockGinAcquirerServerMockRecorder) GetIssuerByID(c interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetIssuerByID", reflect.TypeOf((*MockGinAcquirerServer)(nil).GetIssuerByID), c)
}
