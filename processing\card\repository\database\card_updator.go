package database

import (
	"errors"
	"time"

	"golang.org/x/net/context"
	"gorm.io/gorm"

	"git.local/sensitive/innerpb/processing/goerr"
	"git.local/sensitive/processing/card/model"
	"git.local/sensitive/sdk/dog"
)

type CardUpdatorDB struct {
	db *gorm.DB
}

func NewCardUpdatorDB(
	db *gorm.DB,
) CardUpdator {
	return &CardUpdatorDB{
		db: db,
	}
}

func (p *CardUpdatorDB) SetExpiredBatch(ctx context.Context, cardIDs []uint64) (err error) {
	ctx, span := dog.CreateSpan(ctx, "PayInCardUpdatorDB_SetExpiredBatch")
	defer span.End()

	if len(cardIDs) == 0 {
		return nil
	}

	err = p.db.
		WithContext(ctx).
		Model(&model.Card{}).
		Where("id IN ?", cardIDs).
		Update("is_expired", true).
		Error

	if err != nil {
		return goerr.ErrDbUnexpected.WithErr(err).WithCtx(ctx)
	}

	return nil
}

func (p *CardUpdatorDB) DeactivateClientCard(ctx context.Context, cardID uint64) (err error) {
	ctx, span := dog.CreateSpan(ctx, "PayInCardUpdatorDB_DeactivateClientCard")
	defer span.End()

	if err = p.db.WithContext(ctx).
		Model(&model.Card{}).
		Where("id = ?", cardID).
		Update("approved", false).Error; err != nil {
		return err
	}

	return nil
}

func (p *CardUpdatorDB) UpdateApproved(ctx context.Context, cardId uint64, approvedState bool) (err error) {
	ctx, span := dog.CreateSpan(ctx, "CardUpdatorDB_UpdateApproved")
	defer span.End()

	if err = p.db.WithContext(ctx).
		Model(&model.Card{}).
		Where("id = ?", cardId).
		Update("approved", approvedState).
		First(&model.Card{}).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return goerr.ErrCardNotFound.WithErr(err).WithCtx(ctx)
		}

		return goerr.ErrDbUnexpected.WithErr(err).WithCtx(ctx)
	}

	return nil
}

func (p *CardUpdatorDB) UpdateSaveAccess(ctx context.Context, cardId uint64, saveAccess bool) (err error) {
	ctx, span := dog.CreateSpan(ctx, "PayInCardUpdatorDB_UpdateSaveAccess")
	defer span.End()

	if err = p.db.WithContext(ctx).
		Model(&model.Card{}).
		Where("id = ?", cardId).
		Updates(map[string]interface{}{
			"save_access": saveAccess,
		}).
		First(&model.Card{}).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return goerr.ErrCardNotFound
		}

		return goerr.ErrDbUnexpected.WithErr(err).WithCtx(ctx)
	}

	return nil
}

func (p *CardUpdatorDB) ModifyCardPan(ctx context.Context, cardID uint64, encryptedPan string) error {
	ctx, span := dog.CreateSpan(ctx, "PayInCardUpdatorDB_ModifyCardPan")
	defer span.End()

	if err := p.db.WithContext(ctx).
		Model(&model.Card{}).
		Where("id = ?", cardID).
		Updates(map[string]interface{}{
			"encrypt_pan":     encryptedPan,
			"is_pan_modified": true,
			"pan_modified_at": time.Now(),
		}).Error; err != nil {
		return goerr.ErrDbUnexpected.WithErr(err).WithCtx(ctx)
	}

	return nil
}
