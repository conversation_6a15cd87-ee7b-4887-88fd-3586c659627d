package service

import (
	"context"
	"git.local/sensitive/processing/acquirer/repository/database"

	"go.uber.org/zap"
	"google.golang.org/protobuf/types/known/structpb"

	gorpc "git.local/sensitive/innerpb/processing/grpc"
	"git.local/sensitive/processing/acquirer/model"
	"git.local/sensitive/processing/acquirer/schema"
	"git.local/sensitive/sdk/dog"
)

type TerminalBalanceService struct {
	terminalBalanceRepo         database.TerminalBalancer
	multiacquiringBalanceClient gorpc.MultiacquiringBalanceClient
	transactionTypeClient       gorpc.TransactionTypeClient
}

func NewTerminalBalanceService(
	terminalBalanceRepo database.TerminalBalancer,
	multiacquiringBalanceClient gorpc.MultiacquiringBalanceClient,
	transactionTypeClient gorpc.TransactionTypeClient,
) TerminalBalancer {
	return &TerminalBalanceService{
		terminalBalanceRepo:         terminalBalanceRepo,
		transactionTypeClient:       transactionTypeClient,
		multiacquiringBalanceClient: multiacquiringBalanceClient,
	}
}

func (t TerminalBalanceService) GetTerminalsBalanceByProjectID(
	ctx context.Context,
	projectID uint64,
) (_ []schema.GetBalanceResponse, err error) {
	ctx, span := dog.CreateSpan(ctx, "TerminalBalanceService_GetTerminalsBalanceByProjectID")
	defer span.End()

	terminals, err := t.getPayOutTerminals(ctx, projectID)
	if err != nil {
		return nil, err
	}

	if terminals == nil {
		return make([]schema.GetBalanceResponse, 0), nil
	}

	response := make([]schema.GetBalanceResponse, 0)

	for i := range terminals {
		terminals[i].EncryptedConfig, err = dog.AESDecrypt(terminals[i].EncryptedConfig, dog.ConfigString("SYMMETRIC_KEY"))
		if err != nil {
			dog.L().Error("TerminalBalanceService_GetTerminalsBalanceByProjectID", zap.Error(err))

			return nil, err
		}

		var configStruct *structpb.Struct

		configStruct, err = terminals[i].ToStruct()
		if err != nil {
			dog.L().Error("TerminalBalanceService_GetTerminalsBalanceByProjectID", zap.Error(err))
			return nil, err
		}

		balance, err := t.multiacquiringBalanceClient.CheckBalance(ctx, &gorpc.CheckBalanceRequest{
			TerminalId:   &terminals[i].ID,
			AcquirerCode: &terminals[i].Acquirer.Code,
			Config:       configStruct,
		})
		if err != nil {
			dog.L().Error("TerminalBalanceService_GetTerminalsBalanceByProjectID", zap.Error(err))
			return nil, err
		}

		response = append(response, schema.GetBalanceResponse{
			TerminalID:   balance.GetTerminalId(),
			AcquirerName: balance.GetAcquirerCode(),
			Amount:       balance.GetAmount(),
			Message:      balance.GetMessage(),
		})
	}

	return response, nil
}

func (t TerminalBalanceService) getPayOutTerminals(
	ctx context.Context,
	projectID uint64,
) (_ model.Terminals, err error) {
	ctx, span := dog.CreateSpan(ctx, "TerminalBalanceService_getPayOutTerminals")
	defer span.End()

	var (
		terminals    model.Terminals
		tmpTerminals model.Terminals
	)

	payOutTransactionTypes, err := t.transactionTypeClient.GetTransactionPayOutTypes(ctx, nil)
	if err != nil {
		return nil, err
	}

	for i := range payOutTransactionTypes.Data {
		tmpTerminals, err = t.terminalBalanceRepo.GetTerminals(
			ctx,
			projectID,
			payOutTransactionTypes.GetData()[i].GetId(),
		)
		if err != nil {
			return nil, err
		}

		terminals = append(terminals, tmpTerminals...)
	}

	return terminals, nil
}
