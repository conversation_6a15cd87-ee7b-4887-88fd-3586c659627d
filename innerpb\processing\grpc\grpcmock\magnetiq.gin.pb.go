// Code generated by MockGen. DO NOT EDIT.
// Source: magnetiq.gin.pb.go

// Package grpcmock is a generated GoMock package.
package grpcmock

import (
	reflect "reflect"

	gin "github.com/gin-gonic/gin"
	gomock "github.com/golang/mock/gomock"
)

// MockGinMagnetiqServer is a mock of GinMagnetiqServer interface.
type MockGinMagnetiqServer struct {
	ctrl     *gomock.Controller
	recorder *MockGinMagnetiqServerMockRecorder
}

// MockGinMagnetiqServerMockRecorder is the mock recorder for MockGinMagnetiqServer.
type MockGinMagnetiqServerMockRecorder struct {
	mock *MockGinMagnetiqServer
}

// NewMockGinMagnetiqServer creates a new mock instance.
func NewMockGinMagnetiqServer(ctrl *gomock.Controller) *MockGinMagnetiqServer {
	mock := &MockGinMagnetiqServer{ctrl: ctrl}
	mock.recorder = &MockGinMagnetiqServerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockGinMagnetiqServer) EXPECT() *MockGinMagnetiqServerMockRecorder {
	return m.recorder
}

// ApplePay mocks base method.
func (m *MockGinMagnetiqServer) ApplePay(c *gin.Context) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ApplePay", c)
	ret0, _ := ret[0].(error)
	return ret0
}

// ApplePay indicates an expected call of ApplePay.
func (mr *MockGinMagnetiqServerMockRecorder) ApplePay(c interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ApplePay", reflect.TypeOf((*MockGinMagnetiqServer)(nil).ApplePay), c)
}

// Cancel mocks base method.
func (m *MockGinMagnetiqServer) Cancel(c *gin.Context) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Cancel", c)
	ret0, _ := ret[0].(error)
	return ret0
}

// Cancel indicates an expected call of Cancel.
func (mr *MockGinMagnetiqServerMockRecorder) Cancel(c interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Cancel", reflect.TypeOf((*MockGinMagnetiqServer)(nil).Cancel), c)
}

// Charge mocks base method.
func (m *MockGinMagnetiqServer) Charge(c *gin.Context) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Charge", c)
	ret0, _ := ret[0].(error)
	return ret0
}

// Charge indicates an expected call of Charge.
func (mr *MockGinMagnetiqServerMockRecorder) Charge(c interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Charge", reflect.TypeOf((*MockGinMagnetiqServer)(nil).Charge), c)
}

// GetAcquirerIdentifier mocks base method.
func (m *MockGinMagnetiqServer) GetAcquirerIdentifier(c *gin.Context) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAcquirerIdentifier", c)
	ret0, _ := ret[0].(error)
	return ret0
}

// GetAcquirerIdentifier indicates an expected call of GetAcquirerIdentifier.
func (mr *MockGinMagnetiqServerMockRecorder) GetAcquirerIdentifier(c interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAcquirerIdentifier", reflect.TypeOf((*MockGinMagnetiqServer)(nil).GetAcquirerIdentifier), c)
}

// GetBankTransactionStatus mocks base method.
func (m *MockGinMagnetiqServer) GetBankTransactionStatus(c *gin.Context) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetBankTransactionStatus", c)
	ret0, _ := ret[0].(error)
	return ret0
}

// GetBankTransactionStatus indicates an expected call of GetBankTransactionStatus.
func (mr *MockGinMagnetiqServerMockRecorder) GetBankTransactionStatus(c interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetBankTransactionStatus", reflect.TypeOf((*MockGinMagnetiqServer)(nil).GetBankTransactionStatus), c)
}

// GetBankTransactionStatusUnformated mocks base method.
func (m *MockGinMagnetiqServer) GetBankTransactionStatusUnformated(c *gin.Context) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetBankTransactionStatusUnformated", c)
	ret0, _ := ret[0].(error)
	return ret0
}

// GetBankTransactionStatusUnformated indicates an expected call of GetBankTransactionStatusUnformated.
func (mr *MockGinMagnetiqServerMockRecorder) GetBankTransactionStatusUnformated(c interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetBankTransactionStatusUnformated", reflect.TypeOf((*MockGinMagnetiqServer)(nil).GetBankTransactionStatusUnformated), c)
}

// GooglePay mocks base method.
func (m *MockGinMagnetiqServer) GooglePay(c *gin.Context) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GooglePay", c)
	ret0, _ := ret[0].(error)
	return ret0
}

// GooglePay indicates an expected call of GooglePay.
func (mr *MockGinMagnetiqServerMockRecorder) GooglePay(c interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GooglePay", reflect.TypeOf((*MockGinMagnetiqServer)(nil).GooglePay), c)
}

// MakeToken mocks base method.
func (m *MockGinMagnetiqServer) MakeToken(c *gin.Context) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "MakeToken", c)
	ret0, _ := ret[0].(error)
	return ret0
}

// MakeToken indicates an expected call of MakeToken.
func (mr *MockGinMagnetiqServerMockRecorder) MakeToken(c interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "MakeToken", reflect.TypeOf((*MockGinMagnetiqServer)(nil).MakeToken), c)
}

// OneClickPayIn mocks base method.
func (m *MockGinMagnetiqServer) OneClickPayIn(c *gin.Context) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "OneClickPayIn", c)
	ret0, _ := ret[0].(error)
	return ret0
}

// OneClickPayIn indicates an expected call of OneClickPayIn.
func (mr *MockGinMagnetiqServerMockRecorder) OneClickPayIn(c interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "OneClickPayIn", reflect.TypeOf((*MockGinMagnetiqServer)(nil).OneClickPayIn), c)
}

// PayIn mocks base method.
func (m *MockGinMagnetiqServer) PayIn(c *gin.Context) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "PayIn", c)
	ret0, _ := ret[0].(error)
	return ret0
}

// PayIn indicates an expected call of PayIn.
func (mr *MockGinMagnetiqServerMockRecorder) PayIn(c interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "PayIn", reflect.TypeOf((*MockGinMagnetiqServer)(nil).PayIn), c)
}

// PayOut mocks base method.
func (m *MockGinMagnetiqServer) PayOut(c *gin.Context) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "PayOut", c)
	ret0, _ := ret[0].(error)
	return ret0
}

// PayOut indicates an expected call of PayOut.
func (mr *MockGinMagnetiqServerMockRecorder) PayOut(c interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "PayOut", reflect.TypeOf((*MockGinMagnetiqServer)(nil).PayOut), c)
}

// PayOutByPhone mocks base method.
func (m *MockGinMagnetiqServer) PayOutByPhone(c *gin.Context) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "PayOutByPhone", c)
	ret0, _ := ret[0].(error)
	return ret0
}

// PayOutByPhone indicates an expected call of PayOutByPhone.
func (mr *MockGinMagnetiqServerMockRecorder) PayOutByPhone(c interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "PayOutByPhone", reflect.TypeOf((*MockGinMagnetiqServer)(nil).PayOutByPhone), c)
}

// Refund mocks base method.
func (m *MockGinMagnetiqServer) Refund(c *gin.Context) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Refund", c)
	ret0, _ := ret[0].(error)
	return ret0
}

// Refund indicates an expected call of Refund.
func (mr *MockGinMagnetiqServerMockRecorder) Refund(c interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Refund", reflect.TypeOf((*MockGinMagnetiqServer)(nil).Refund), c)
}

// ResolveVisaAlias mocks base method.
func (m *MockGinMagnetiqServer) ResolveVisaAlias(c *gin.Context) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ResolveVisaAlias", c)
	ret0, _ := ret[0].(error)
	return ret0
}

// ResolveVisaAlias indicates an expected call of ResolveVisaAlias.
func (mr *MockGinMagnetiqServerMockRecorder) ResolveVisaAlias(c interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ResolveVisaAlias", reflect.TypeOf((*MockGinMagnetiqServer)(nil).ResolveVisaAlias), c)
}

// ThreeDSConfirm mocks base method.
func (m *MockGinMagnetiqServer) ThreeDSConfirm(c *gin.Context) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ThreeDSConfirm", c)
	ret0, _ := ret[0].(error)
	return ret0
}

// ThreeDSConfirm indicates an expected call of ThreeDSConfirm.
func (mr *MockGinMagnetiqServerMockRecorder) ThreeDSConfirm(c interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ThreeDSConfirm", reflect.TypeOf((*MockGinMagnetiqServer)(nil).ThreeDSConfirm), c)
}

// ThreeDSResume mocks base method.
func (m *MockGinMagnetiqServer) ThreeDSResume(c *gin.Context) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ThreeDSResume", c)
	ret0, _ := ret[0].(error)
	return ret0
}

// ThreeDSResume indicates an expected call of ThreeDSResume.
func (mr *MockGinMagnetiqServerMockRecorder) ThreeDSResume(c interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ThreeDSResume", reflect.TypeOf((*MockGinMagnetiqServer)(nil).ThreeDSResume), c)
}

// TwoStagePayIn mocks base method.
func (m *MockGinMagnetiqServer) TwoStagePayIn(c *gin.Context) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "TwoStagePayIn", c)
	ret0, _ := ret[0].(error)
	return ret0
}

// TwoStagePayIn indicates an expected call of TwoStagePayIn.
func (mr *MockGinMagnetiqServerMockRecorder) TwoStagePayIn(c interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "TwoStagePayIn", reflect.TypeOf((*MockGinMagnetiqServer)(nil).TwoStagePayIn), c)
}
