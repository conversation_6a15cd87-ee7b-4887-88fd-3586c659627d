edition = "2023";

package processing.merchant.project_transactions;

option go_package = "git.local/sensitive/innerpb/processing/grpc";

import "google/protobuf/timestamp.proto";

service ProjectTransactions {
  rpc CheckAmountLimit(CheckAmountLimitRequestV1) returns (CheckAmountLimitResponseV1) {}
  rpc CheckAttemptsWithinTimeout(CheckAttemptsWithinTimeoutRequestV1) returns (CheckAttemptsWithinTimeoutResponseV1) {}
  rpc GetTransactionLimit(GetTransactionLimitRequestV1) returns (GetTransactionLimitResponseV1) {}
}

message CheckAmountLimitRequestV1 {
  uint64 project_id = 1;
  uint64 transaction_type_id = 2;
  double transaction_amount = 3;
}

message CheckAmountLimitResponseV1 {
  bool verified_amount = 1;
}

message CheckAttemptsWithinTimeoutRequestV1 {
  uint64 project_id = 1;
  uint64 transaction_type_id = 2;
  uint64 try = 3;
  uint64 transaction_life_time = 4;
}

message CheckAttemptsWithinTimeoutResponseV1 {
  bool verified_timeout = 1;
  bool verified_try = 2;
}

message GetTransactionLimitRequestV1 {
  uint64 project_id = 1;
  uint64 transaction_type_id = 2;
}

message GetTransactionLimitResponseV1 {
  google.protobuf.Timestamp created_at = 1;
  google.protobuf.Timestamp updated_at = 2;
  uint64 id = 3;
  uint64 project_id = 4;
  uint64 type_id = 5;
  uint64 timeout = 6;
  uint64 try = 7;
  double amount = 8;
}
