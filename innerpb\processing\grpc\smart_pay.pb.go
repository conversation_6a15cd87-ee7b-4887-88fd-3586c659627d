// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.5
// 	protoc        v5.29.3
// source: inner/processing/grpc/smart_pay.proto

package grpc

import (
	_ "git.local/sensitive/mvp/pb"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type ApplePaySessionRequestV1 struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	ValidationURL *string                `protobuf:"bytes,1,opt,name=validationURL" json:"validationURL,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ApplePaySessionRequestV1) Reset() {
	*x = ApplePaySessionRequestV1{}
	mi := &file_inner_processing_grpc_smart_pay_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ApplePaySessionRequestV1) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ApplePaySessionRequestV1) ProtoMessage() {}

func (x *ApplePaySessionRequestV1) ProtoReflect() protoreflect.Message {
	mi := &file_inner_processing_grpc_smart_pay_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ApplePaySessionRequestV1.ProtoReflect.Descriptor instead.
func (*ApplePaySessionRequestV1) Descriptor() ([]byte, []int) {
	return file_inner_processing_grpc_smart_pay_proto_rawDescGZIP(), []int{0}
}

func (x *ApplePaySessionRequestV1) GetValidationURL() string {
	if x != nil && x.ValidationURL != nil {
		return *x.ValidationURL
	}
	return ""
}

type ApplePaySessionResponseV1 struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Response      *string                `protobuf:"bytes,1,opt,name=response" json:"response,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ApplePaySessionResponseV1) Reset() {
	*x = ApplePaySessionResponseV1{}
	mi := &file_inner_processing_grpc_smart_pay_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ApplePaySessionResponseV1) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ApplePaySessionResponseV1) ProtoMessage() {}

func (x *ApplePaySessionResponseV1) ProtoReflect() protoreflect.Message {
	mi := &file_inner_processing_grpc_smart_pay_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ApplePaySessionResponseV1.ProtoReflect.Descriptor instead.
func (*ApplePaySessionResponseV1) Descriptor() ([]byte, []int) {
	return file_inner_processing_grpc_smart_pay_proto_rawDescGZIP(), []int{1}
}

func (x *ApplePaySessionResponseV1) GetResponse() string {
	if x != nil && x.Response != nil {
		return *x.Response
	}
	return ""
}

type DecodeTokenRequestV1 struct {
	state                 protoimpl.MessageState `protogen:"open.v1"`
	TransactionIdentifier *string                `protobuf:"bytes,1,opt,name=transaction_identifier,json=transactionIdentifier" json:"transaction_identifier,omitempty"`
	Method                *PaymentMethod         `protobuf:"bytes,2,opt,name=method" json:"method,omitempty"`
	Data                  *PaymentData           `protobuf:"bytes,3,opt,name=data" json:"data,omitempty"`
	unknownFields         protoimpl.UnknownFields
	sizeCache             protoimpl.SizeCache
}

func (x *DecodeTokenRequestV1) Reset() {
	*x = DecodeTokenRequestV1{}
	mi := &file_inner_processing_grpc_smart_pay_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DecodeTokenRequestV1) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DecodeTokenRequestV1) ProtoMessage() {}

func (x *DecodeTokenRequestV1) ProtoReflect() protoreflect.Message {
	mi := &file_inner_processing_grpc_smart_pay_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DecodeTokenRequestV1.ProtoReflect.Descriptor instead.
func (*DecodeTokenRequestV1) Descriptor() ([]byte, []int) {
	return file_inner_processing_grpc_smart_pay_proto_rawDescGZIP(), []int{2}
}

func (x *DecodeTokenRequestV1) GetTransactionIdentifier() string {
	if x != nil && x.TransactionIdentifier != nil {
		return *x.TransactionIdentifier
	}
	return ""
}

func (x *DecodeTokenRequestV1) GetMethod() *PaymentMethod {
	if x != nil {
		return x.Method
	}
	return nil
}

func (x *DecodeTokenRequestV1) GetData() *PaymentData {
	if x != nil {
		return x.Data
	}
	return nil
}

type PaymentMethod struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Type          *string                `protobuf:"bytes,1,opt,name=type" json:"type,omitempty"`
	Network       *string                `protobuf:"bytes,2,opt,name=network" json:"network,omitempty"`
	DisplayName   *string                `protobuf:"bytes,3,opt,name=display_name,json=displayName" json:"display_name,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *PaymentMethod) Reset() {
	*x = PaymentMethod{}
	mi := &file_inner_processing_grpc_smart_pay_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PaymentMethod) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PaymentMethod) ProtoMessage() {}

func (x *PaymentMethod) ProtoReflect() protoreflect.Message {
	mi := &file_inner_processing_grpc_smart_pay_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PaymentMethod.ProtoReflect.Descriptor instead.
func (*PaymentMethod) Descriptor() ([]byte, []int) {
	return file_inner_processing_grpc_smart_pay_proto_rawDescGZIP(), []int{3}
}

func (x *PaymentMethod) GetType() string {
	if x != nil && x.Type != nil {
		return *x.Type
	}
	return ""
}

func (x *PaymentMethod) GetNetwork() string {
	if x != nil && x.Network != nil {
		return *x.Network
	}
	return ""
}

func (x *PaymentMethod) GetDisplayName() string {
	if x != nil && x.DisplayName != nil {
		return *x.DisplayName
	}
	return ""
}

type PaymentData struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Version       *string                `protobuf:"bytes,1,opt,name=version" json:"version,omitempty"`
	Signature     []byte                 `protobuf:"bytes,2,opt,name=signature" json:"signature,omitempty"`
	Header        *Header                `protobuf:"bytes,3,opt,name=header" json:"header,omitempty"`
	Data          []byte                 `protobuf:"bytes,4,opt,name=data" json:"data,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *PaymentData) Reset() {
	*x = PaymentData{}
	mi := &file_inner_processing_grpc_smart_pay_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PaymentData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PaymentData) ProtoMessage() {}

func (x *PaymentData) ProtoReflect() protoreflect.Message {
	mi := &file_inner_processing_grpc_smart_pay_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PaymentData.ProtoReflect.Descriptor instead.
func (*PaymentData) Descriptor() ([]byte, []int) {
	return file_inner_processing_grpc_smart_pay_proto_rawDescGZIP(), []int{4}
}

func (x *PaymentData) GetVersion() string {
	if x != nil && x.Version != nil {
		return *x.Version
	}
	return ""
}

func (x *PaymentData) GetSignature() []byte {
	if x != nil {
		return x.Signature
	}
	return nil
}

func (x *PaymentData) GetHeader() *Header {
	if x != nil {
		return x.Header
	}
	return nil
}

func (x *PaymentData) GetData() []byte {
	if x != nil {
		return x.Data
	}
	return nil
}

type Header struct {
	state              protoimpl.MessageState `protogen:"open.v1"`
	ApplicationName    *string                `protobuf:"bytes,1,opt,name=application_name,json=applicationName" json:"application_name,omitempty"`
	EphemeralPublicKey []byte                 `protobuf:"bytes,2,opt,name=ephemeral_public_key,json=ephemeralPublicKey" json:"ephemeral_public_key,omitempty"`
	WrappedKey         []byte                 `protobuf:"bytes,3,opt,name=wrapped_key,json=wrappedKey" json:"wrapped_key,omitempty"`
	PublicKeyHash      []byte                 `protobuf:"bytes,4,opt,name=public_key_hash,json=publicKeyHash" json:"public_key_hash,omitempty"`
	TransactionId      *string                `protobuf:"bytes,5,opt,name=transaction_id,json=transactionId" json:"transaction_id,omitempty"`
	unknownFields      protoimpl.UnknownFields
	sizeCache          protoimpl.SizeCache
}

func (x *Header) Reset() {
	*x = Header{}
	mi := &file_inner_processing_grpc_smart_pay_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Header) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Header) ProtoMessage() {}

func (x *Header) ProtoReflect() protoreflect.Message {
	mi := &file_inner_processing_grpc_smart_pay_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Header.ProtoReflect.Descriptor instead.
func (*Header) Descriptor() ([]byte, []int) {
	return file_inner_processing_grpc_smart_pay_proto_rawDescGZIP(), []int{5}
}

func (x *Header) GetApplicationName() string {
	if x != nil && x.ApplicationName != nil {
		return *x.ApplicationName
	}
	return ""
}

func (x *Header) GetEphemeralPublicKey() []byte {
	if x != nil {
		return x.EphemeralPublicKey
	}
	return nil
}

func (x *Header) GetWrappedKey() []byte {
	if x != nil {
		return x.WrappedKey
	}
	return nil
}

func (x *Header) GetPublicKeyHash() []byte {
	if x != nil {
		return x.PublicKeyHash
	}
	return nil
}

func (x *Header) GetTransactionId() string {
	if x != nil && x.TransactionId != nil {
		return *x.TransactionId
	}
	return ""
}

type DecodeTokenResponseV1 struct {
	state                           protoimpl.MessageState `protogen:"open.v1"`
	ApplicationPrimaryAccountNumber []byte                 `protobuf:"bytes,1,opt,name=application_primary_account_number,json=applicationPrimaryAccountNumber" json:"application_primary_account_number,omitempty"`
	ApplicationExpirationMonth      []byte                 `protobuf:"bytes,2,opt,name=application_expiration_month,json=applicationExpirationMonth" json:"application_expiration_month,omitempty"`
	ApplicationExpirationYear       []byte                 `protobuf:"bytes,3,opt,name=application_expiration_year,json=applicationExpirationYear" json:"application_expiration_year,omitempty"`
	CurrencyCode                    *string                `protobuf:"bytes,4,opt,name=currency_code,json=currencyCode" json:"currency_code,omitempty"`
	TransactionAmount               *float64               `protobuf:"fixed64,5,opt,name=transaction_amount,json=transactionAmount" json:"transaction_amount,omitempty"`
	CardholderName                  []byte                 `protobuf:"bytes,6,opt,name=cardholder_name,json=cardholderName" json:"cardholder_name,omitempty"`
	DeviceManufacturerIdentifier    *string                `protobuf:"bytes,7,opt,name=device_manufacturer_identifier,json=deviceManufacturerIdentifier" json:"device_manufacturer_identifier,omitempty"`
	PaymentDataType                 *string                `protobuf:"bytes,8,opt,name=payment_data_type,json=paymentDataType" json:"payment_data_type,omitempty"`
	PaymentData                     *PaymentDataResponse   `protobuf:"bytes,9,opt,name=payment_data,json=paymentData" json:"payment_data,omitempty"`
	Version                         *string                `protobuf:"bytes,10,opt,name=version" json:"version,omitempty"`
	unknownFields                   protoimpl.UnknownFields
	sizeCache                       protoimpl.SizeCache
}

func (x *DecodeTokenResponseV1) Reset() {
	*x = DecodeTokenResponseV1{}
	mi := &file_inner_processing_grpc_smart_pay_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DecodeTokenResponseV1) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DecodeTokenResponseV1) ProtoMessage() {}

func (x *DecodeTokenResponseV1) ProtoReflect() protoreflect.Message {
	mi := &file_inner_processing_grpc_smart_pay_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DecodeTokenResponseV1.ProtoReflect.Descriptor instead.
func (*DecodeTokenResponseV1) Descriptor() ([]byte, []int) {
	return file_inner_processing_grpc_smart_pay_proto_rawDescGZIP(), []int{6}
}

func (x *DecodeTokenResponseV1) GetApplicationPrimaryAccountNumber() []byte {
	if x != nil {
		return x.ApplicationPrimaryAccountNumber
	}
	return nil
}

func (x *DecodeTokenResponseV1) GetApplicationExpirationMonth() []byte {
	if x != nil {
		return x.ApplicationExpirationMonth
	}
	return nil
}

func (x *DecodeTokenResponseV1) GetApplicationExpirationYear() []byte {
	if x != nil {
		return x.ApplicationExpirationYear
	}
	return nil
}

func (x *DecodeTokenResponseV1) GetCurrencyCode() string {
	if x != nil && x.CurrencyCode != nil {
		return *x.CurrencyCode
	}
	return ""
}

func (x *DecodeTokenResponseV1) GetTransactionAmount() float64 {
	if x != nil && x.TransactionAmount != nil {
		return *x.TransactionAmount
	}
	return 0
}

func (x *DecodeTokenResponseV1) GetCardholderName() []byte {
	if x != nil {
		return x.CardholderName
	}
	return nil
}

func (x *DecodeTokenResponseV1) GetDeviceManufacturerIdentifier() string {
	if x != nil && x.DeviceManufacturerIdentifier != nil {
		return *x.DeviceManufacturerIdentifier
	}
	return ""
}

func (x *DecodeTokenResponseV1) GetPaymentDataType() string {
	if x != nil && x.PaymentDataType != nil {
		return *x.PaymentDataType
	}
	return ""
}

func (x *DecodeTokenResponseV1) GetPaymentData() *PaymentDataResponse {
	if x != nil {
		return x.PaymentData
	}
	return nil
}

func (x *DecodeTokenResponseV1) GetVersion() string {
	if x != nil && x.Version != nil {
		return *x.Version
	}
	return ""
}

type PaymentDataResponse struct {
	state                   protoimpl.MessageState `protogen:"open.v1"`
	OnlinePaymentCryptogram []byte                 `protobuf:"bytes,1,opt,name=online_payment_cryptogram,json=onlinePaymentCryptogram" json:"online_payment_cryptogram,omitempty"`
	EciIndicator            *string                `protobuf:"bytes,2,opt,name=eci_indicator,json=eciIndicator" json:"eci_indicator,omitempty"`
	EmvData                 []byte                 `protobuf:"bytes,3,opt,name=emv_data,json=emvData" json:"emv_data,omitempty"`
	EncryptedPinData        *string                `protobuf:"bytes,4,opt,name=encrypted_pin_data,json=encryptedPinData" json:"encrypted_pin_data,omitempty"`
	unknownFields           protoimpl.UnknownFields
	sizeCache               protoimpl.SizeCache
}

func (x *PaymentDataResponse) Reset() {
	*x = PaymentDataResponse{}
	mi := &file_inner_processing_grpc_smart_pay_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PaymentDataResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PaymentDataResponse) ProtoMessage() {}

func (x *PaymentDataResponse) ProtoReflect() protoreflect.Message {
	mi := &file_inner_processing_grpc_smart_pay_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PaymentDataResponse.ProtoReflect.Descriptor instead.
func (*PaymentDataResponse) Descriptor() ([]byte, []int) {
	return file_inner_processing_grpc_smart_pay_proto_rawDescGZIP(), []int{7}
}

func (x *PaymentDataResponse) GetOnlinePaymentCryptogram() []byte {
	if x != nil {
		return x.OnlinePaymentCryptogram
	}
	return nil
}

func (x *PaymentDataResponse) GetEciIndicator() string {
	if x != nil && x.EciIndicator != nil {
		return *x.EciIndicator
	}
	return ""
}

func (x *PaymentDataResponse) GetEmvData() []byte {
	if x != nil {
		return x.EmvData
	}
	return nil
}

func (x *PaymentDataResponse) GetEncryptedPinData() string {
	if x != nil && x.EncryptedPinData != nil {
		return *x.EncryptedPinData
	}
	return ""
}

type DecryptGPayTokenRequestV1 struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Token         *string                `protobuf:"bytes,1,opt,name=token" json:"token,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DecryptGPayTokenRequestV1) Reset() {
	*x = DecryptGPayTokenRequestV1{}
	mi := &file_inner_processing_grpc_smart_pay_proto_msgTypes[8]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DecryptGPayTokenRequestV1) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DecryptGPayTokenRequestV1) ProtoMessage() {}

func (x *DecryptGPayTokenRequestV1) ProtoReflect() protoreflect.Message {
	mi := &file_inner_processing_grpc_smart_pay_proto_msgTypes[8]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DecryptGPayTokenRequestV1.ProtoReflect.Descriptor instead.
func (*DecryptGPayTokenRequestV1) Descriptor() ([]byte, []int) {
	return file_inner_processing_grpc_smart_pay_proto_rawDescGZIP(), []int{8}
}

func (x *DecryptGPayTokenRequestV1) GetToken() string {
	if x != nil && x.Token != nil {
		return *x.Token
	}
	return ""
}

type DecryptGPayTokenResponseV1 struct {
	state                protoimpl.MessageState `protogen:"open.v1"`
	GatewayMerchantId    *string                `protobuf:"bytes,1,opt,name=gateway_merchant_id,json=gatewayMerchantId" json:"gateway_merchant_id,omitempty"`
	MessageExpiration    *string                `protobuf:"bytes,2,opt,name=message_expiration,json=messageExpiration" json:"message_expiration,omitempty"`
	MessageId            *string                `protobuf:"bytes,3,opt,name=message_id,json=messageId" json:"message_id,omitempty"`
	PaymentMethod        *string                `protobuf:"bytes,4,opt,name=payment_method,json=paymentMethod" json:"payment_method,omitempty"`
	PaymentMethodDetails *PaymentMethodDetails  `protobuf:"bytes,5,opt,name=payment_method_details,json=paymentMethodDetails" json:"payment_method_details,omitempty"`
	unknownFields        protoimpl.UnknownFields
	sizeCache            protoimpl.SizeCache
}

func (x *DecryptGPayTokenResponseV1) Reset() {
	*x = DecryptGPayTokenResponseV1{}
	mi := &file_inner_processing_grpc_smart_pay_proto_msgTypes[9]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DecryptGPayTokenResponseV1) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DecryptGPayTokenResponseV1) ProtoMessage() {}

func (x *DecryptGPayTokenResponseV1) ProtoReflect() protoreflect.Message {
	mi := &file_inner_processing_grpc_smart_pay_proto_msgTypes[9]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DecryptGPayTokenResponseV1.ProtoReflect.Descriptor instead.
func (*DecryptGPayTokenResponseV1) Descriptor() ([]byte, []int) {
	return file_inner_processing_grpc_smart_pay_proto_rawDescGZIP(), []int{9}
}

func (x *DecryptGPayTokenResponseV1) GetGatewayMerchantId() string {
	if x != nil && x.GatewayMerchantId != nil {
		return *x.GatewayMerchantId
	}
	return ""
}

func (x *DecryptGPayTokenResponseV1) GetMessageExpiration() string {
	if x != nil && x.MessageExpiration != nil {
		return *x.MessageExpiration
	}
	return ""
}

func (x *DecryptGPayTokenResponseV1) GetMessageId() string {
	if x != nil && x.MessageId != nil {
		return *x.MessageId
	}
	return ""
}

func (x *DecryptGPayTokenResponseV1) GetPaymentMethod() string {
	if x != nil && x.PaymentMethod != nil {
		return *x.PaymentMethod
	}
	return ""
}

func (x *DecryptGPayTokenResponseV1) GetPaymentMethodDetails() *PaymentMethodDetails {
	if x != nil {
		return x.PaymentMethodDetails
	}
	return nil
}

type PaymentMethodDetails struct {
	state            protoimpl.MessageState `protogen:"open.v1"`
	ExpirationYear   []byte                 `protobuf:"bytes,1,opt,name=expiration_year,json=expirationYear" json:"expiration_year,omitempty"`
	ExpirationMonth  []byte                 `protobuf:"bytes,2,opt,name=expiration_month,json=expirationMonth" json:"expiration_month,omitempty"`
	Pan              []byte                 `protobuf:"bytes,3,opt,name=pan" json:"pan,omitempty"`
	AuthMethod       *string                `protobuf:"bytes,4,opt,name=auth_method,json=authMethod" json:"auth_method,omitempty"`
	EciIndicator     *string                `protobuf:"bytes,5,opt,name=eci_indicator,json=eciIndicator" json:"eci_indicator,omitempty"`
	Cryptogram       *string                `protobuf:"bytes,6,opt,name=cryptogram" json:"cryptogram,omitempty"`
	AssuranceDetails *AssuranceDetails      `protobuf:"bytes,7,opt,name=assurance_details,json=assuranceDetails" json:"assurance_details,omitempty"`
	unknownFields    protoimpl.UnknownFields
	sizeCache        protoimpl.SizeCache
}

func (x *PaymentMethodDetails) Reset() {
	*x = PaymentMethodDetails{}
	mi := &file_inner_processing_grpc_smart_pay_proto_msgTypes[10]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PaymentMethodDetails) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PaymentMethodDetails) ProtoMessage() {}

func (x *PaymentMethodDetails) ProtoReflect() protoreflect.Message {
	mi := &file_inner_processing_grpc_smart_pay_proto_msgTypes[10]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PaymentMethodDetails.ProtoReflect.Descriptor instead.
func (*PaymentMethodDetails) Descriptor() ([]byte, []int) {
	return file_inner_processing_grpc_smart_pay_proto_rawDescGZIP(), []int{10}
}

func (x *PaymentMethodDetails) GetExpirationYear() []byte {
	if x != nil {
		return x.ExpirationYear
	}
	return nil
}

func (x *PaymentMethodDetails) GetExpirationMonth() []byte {
	if x != nil {
		return x.ExpirationMonth
	}
	return nil
}

func (x *PaymentMethodDetails) GetPan() []byte {
	if x != nil {
		return x.Pan
	}
	return nil
}

func (x *PaymentMethodDetails) GetAuthMethod() string {
	if x != nil && x.AuthMethod != nil {
		return *x.AuthMethod
	}
	return ""
}

func (x *PaymentMethodDetails) GetEciIndicator() string {
	if x != nil && x.EciIndicator != nil {
		return *x.EciIndicator
	}
	return ""
}

func (x *PaymentMethodDetails) GetCryptogram() string {
	if x != nil && x.Cryptogram != nil {
		return *x.Cryptogram
	}
	return ""
}

func (x *PaymentMethodDetails) GetAssuranceDetails() *AssuranceDetails {
	if x != nil {
		return x.AssuranceDetails
	}
	return nil
}

type AssuranceDetails struct {
	state                   protoimpl.MessageState `protogen:"open.v1"`
	AccountVerified         *bool                  `protobuf:"varint,1,opt,name=account_verified,json=accountVerified" json:"account_verified,omitempty"`
	CardHolderAuthenticated *bool                  `protobuf:"varint,2,opt,name=card_holder_authenticated,json=cardHolderAuthenticated" json:"card_holder_authenticated,omitempty"`
	unknownFields           protoimpl.UnknownFields
	sizeCache               protoimpl.SizeCache
}

func (x *AssuranceDetails) Reset() {
	*x = AssuranceDetails{}
	mi := &file_inner_processing_grpc_smart_pay_proto_msgTypes[11]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AssuranceDetails) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AssuranceDetails) ProtoMessage() {}

func (x *AssuranceDetails) ProtoReflect() protoreflect.Message {
	mi := &file_inner_processing_grpc_smart_pay_proto_msgTypes[11]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AssuranceDetails.ProtoReflect.Descriptor instead.
func (*AssuranceDetails) Descriptor() ([]byte, []int) {
	return file_inner_processing_grpc_smart_pay_proto_rawDescGZIP(), []int{11}
}

func (x *AssuranceDetails) GetAccountVerified() bool {
	if x != nil && x.AccountVerified != nil {
		return *x.AccountVerified
	}
	return false
}

func (x *AssuranceDetails) GetCardHolderAuthenticated() bool {
	if x != nil && x.CardHolderAuthenticated != nil {
		return *x.CardHolderAuthenticated
	}
	return false
}

type GetGPayCredentialsRequestV1 struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	ProjectId     *uint64                `protobuf:"varint,1,opt,name=project_id,json=projectId" json:"project_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetGPayCredentialsRequestV1) Reset() {
	*x = GetGPayCredentialsRequestV1{}
	mi := &file_inner_processing_grpc_smart_pay_proto_msgTypes[12]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetGPayCredentialsRequestV1) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetGPayCredentialsRequestV1) ProtoMessage() {}

func (x *GetGPayCredentialsRequestV1) ProtoReflect() protoreflect.Message {
	mi := &file_inner_processing_grpc_smart_pay_proto_msgTypes[12]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetGPayCredentialsRequestV1.ProtoReflect.Descriptor instead.
func (*GetGPayCredentialsRequestV1) Descriptor() ([]byte, []int) {
	return file_inner_processing_grpc_smart_pay_proto_rawDescGZIP(), []int{12}
}

func (x *GetGPayCredentialsRequestV1) GetProjectId() uint64 {
	if x != nil && x.ProjectId != nil {
		return *x.ProjectId
	}
	return 0
}

type GetGPayCredentialsResponseV1 struct {
	state                    protoimpl.MessageState `protogen:"open.v1"`
	GooglePayGatewayMerchant *string                `protobuf:"bytes,1,opt,name=google_pay_gateway_merchant,json=googlePayGatewayMerchant" json:"google_pay_gateway_merchant,omitempty"`
	GooglePayMerchantId      *string                `protobuf:"bytes,2,opt,name=google_pay_merchant_id,json=googlePayMerchantId" json:"google_pay_merchant_id,omitempty"`
	unknownFields            protoimpl.UnknownFields
	sizeCache                protoimpl.SizeCache
}

func (x *GetGPayCredentialsResponseV1) Reset() {
	*x = GetGPayCredentialsResponseV1{}
	mi := &file_inner_processing_grpc_smart_pay_proto_msgTypes[13]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetGPayCredentialsResponseV1) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetGPayCredentialsResponseV1) ProtoMessage() {}

func (x *GetGPayCredentialsResponseV1) ProtoReflect() protoreflect.Message {
	mi := &file_inner_processing_grpc_smart_pay_proto_msgTypes[13]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetGPayCredentialsResponseV1.ProtoReflect.Descriptor instead.
func (*GetGPayCredentialsResponseV1) Descriptor() ([]byte, []int) {
	return file_inner_processing_grpc_smart_pay_proto_rawDescGZIP(), []int{13}
}

func (x *GetGPayCredentialsResponseV1) GetGooglePayGatewayMerchant() string {
	if x != nil && x.GooglePayGatewayMerchant != nil {
		return *x.GooglePayGatewayMerchant
	}
	return ""
}

func (x *GetGPayCredentialsResponseV1) GetGooglePayMerchantId() string {
	if x != nil && x.GooglePayMerchantId != nil {
		return *x.GooglePayMerchantId
	}
	return ""
}

var File_inner_processing_grpc_smart_pay_proto protoreflect.FileDescriptor

var file_inner_processing_grpc_smart_pay_proto_rawDesc = string([]byte{
	0x0a, 0x25, 0x69, 0x6e, 0x6e, 0x65, 0x72, 0x2f, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69,
	0x6e, 0x67, 0x2f, 0x67, 0x72, 0x70, 0x63, 0x2f, 0x73, 0x6d, 0x61, 0x72, 0x74, 0x5f, 0x70, 0x61,
	0x79, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x1e, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73,
	0x69, 0x6e, 0x67, 0x2e, 0x73, 0x6d, 0x61, 0x72, 0x74, 0x5f, 0x70, 0x61, 0x79, 0x2e, 0x73, 0x6d,
	0x61, 0x72, 0x74, 0x5f, 0x70, 0x61, 0x79, 0x1a, 0x16, 0x6d, 0x76, 0x70, 0x2f, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x2f, 0x6c, 0x6f, 0x67, 0x67, 0x65, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22,
	0x40, 0x0a, 0x18, 0x41, 0x70, 0x70, 0x6c, 0x65, 0x50, 0x61, 0x79, 0x53, 0x65, 0x73, 0x73, 0x69,
	0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x56, 0x31, 0x12, 0x24, 0x0a, 0x0d, 0x76,
	0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x55, 0x52, 0x4c, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0d, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x55, 0x52,
	0x4c, 0x22, 0x37, 0x0a, 0x19, 0x41, 0x70, 0x70, 0x6c, 0x65, 0x50, 0x61, 0x79, 0x53, 0x65, 0x73,
	0x73, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x56, 0x31, 0x12, 0x1a,
	0x0a, 0x08, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x08, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0xd5, 0x01, 0x0a, 0x14, 0x44,
	0x65, 0x63, 0x6f, 0x64, 0x65, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x56, 0x31, 0x12, 0x35, 0x0a, 0x16, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69,
	0x6f, 0x6e, 0x5f, 0x69, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x66, 0x69, 0x65, 0x72, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x15, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e,
	0x49, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x66, 0x69, 0x65, 0x72, 0x12, 0x45, 0x0a, 0x06, 0x6d, 0x65,
	0x74, 0x68, 0x6f, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2d, 0x2e, 0x70, 0x72, 0x6f,
	0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x2e, 0x73, 0x6d, 0x61, 0x72, 0x74, 0x5f, 0x70, 0x61,
	0x79, 0x2e, 0x73, 0x6d, 0x61, 0x72, 0x74, 0x5f, 0x70, 0x61, 0x79, 0x2e, 0x50, 0x61, 0x79, 0x6d,
	0x65, 0x6e, 0x74, 0x4d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x52, 0x06, 0x6d, 0x65, 0x74, 0x68, 0x6f,
	0x64, 0x12, 0x3f, 0x0a, 0x04, 0x64, 0x61, 0x74, 0x61, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x2b, 0x2e, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x2e, 0x73, 0x6d, 0x61,
	0x72, 0x74, 0x5f, 0x70, 0x61, 0x79, 0x2e, 0x73, 0x6d, 0x61, 0x72, 0x74, 0x5f, 0x70, 0x61, 0x79,
	0x2e, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x44, 0x61, 0x74, 0x61, 0x52, 0x04, 0x64, 0x61,
	0x74, 0x61, 0x22, 0x60, 0x0a, 0x0d, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x4d, 0x65, 0x74,
	0x68, 0x6f, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x6e, 0x65, 0x74, 0x77, 0x6f,
	0x72, 0x6b, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72,
	0x6b, 0x12, 0x21, 0x0a, 0x0c, 0x64, 0x69, 0x73, 0x70, 0x6c, 0x61, 0x79, 0x5f, 0x6e, 0x61, 0x6d,
	0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x64, 0x69, 0x73, 0x70, 0x6c, 0x61, 0x79,
	0x4e, 0x61, 0x6d, 0x65, 0x22, 0x99, 0x01, 0x0a, 0x0b, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74,
	0x44, 0x61, 0x74, 0x61, 0x12, 0x18, 0x0a, 0x07, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x1c,
	0x0a, 0x09, 0x73, 0x69, 0x67, 0x6e, 0x61, 0x74, 0x75, 0x72, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x0c, 0x52, 0x09, 0x73, 0x69, 0x67, 0x6e, 0x61, 0x74, 0x75, 0x72, 0x65, 0x12, 0x3e, 0x0a, 0x06,
	0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x26, 0x2e, 0x70,
	0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x2e, 0x73, 0x6d, 0x61, 0x72, 0x74, 0x5f,
	0x70, 0x61, 0x79, 0x2e, 0x73, 0x6d, 0x61, 0x72, 0x74, 0x5f, 0x70, 0x61, 0x79, 0x2e, 0x48, 0x65,
	0x61, 0x64, 0x65, 0x72, 0x52, 0x06, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x12, 0x12, 0x0a, 0x04,
	0x64, 0x61, 0x74, 0x61, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x04, 0x64, 0x61, 0x74, 0x61,
	0x22, 0xd5, 0x01, 0x0a, 0x06, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x12, 0x29, 0x0a, 0x10, 0x61,
	0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x30, 0x0a, 0x14, 0x65, 0x70, 0x68, 0x65, 0x6d, 0x65,
	0x72, 0x61, 0x6c, 0x5f, 0x70, 0x75, 0x62, 0x6c, 0x69, 0x63, 0x5f, 0x6b, 0x65, 0x79, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x0c, 0x52, 0x12, 0x65, 0x70, 0x68, 0x65, 0x6d, 0x65, 0x72, 0x61, 0x6c, 0x50,
	0x75, 0x62, 0x6c, 0x69, 0x63, 0x4b, 0x65, 0x79, 0x12, 0x1f, 0x0a, 0x0b, 0x77, 0x72, 0x61, 0x70,
	0x70, 0x65, 0x64, 0x5f, 0x6b, 0x65, 0x79, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x0a, 0x77,
	0x72, 0x61, 0x70, 0x70, 0x65, 0x64, 0x4b, 0x65, 0x79, 0x12, 0x26, 0x0a, 0x0f, 0x70, 0x75, 0x62,
	0x6c, 0x69, 0x63, 0x5f, 0x6b, 0x65, 0x79, 0x5f, 0x68, 0x61, 0x73, 0x68, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x0c, 0x52, 0x0d, 0x70, 0x75, 0x62, 0x6c, 0x69, 0x63, 0x4b, 0x65, 0x79, 0x48, 0x61, 0x73,
	0x68, 0x12, 0x25, 0x0a, 0x0e, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e,
	0x5f, 0x69, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x74, 0x72, 0x61, 0x6e, 0x73,
	0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x22, 0xe2, 0x04, 0x0a, 0x15, 0x44, 0x65, 0x63,
	0x6f, 0x64, 0x65, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x56, 0x31, 0x12, 0x54, 0x0a, 0x22, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x5f, 0x70, 0x72, 0x69, 0x6d, 0x61, 0x72, 0x79, 0x5f, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e,
	0x74, 0x5f, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0c, 0x42, 0x07,
	0x9a, 0xfc, 0xeb, 0x8e, 0x02, 0x01, 0x64, 0x52, 0x1f, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x50, 0x72, 0x69, 0x6d, 0x61, 0x72, 0x79, 0x41, 0x63, 0x63, 0x6f, 0x75,
	0x6e, 0x74, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x12, 0x49, 0x0a, 0x1c, 0x61, 0x70, 0x70, 0x6c,
	0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x65, 0x78, 0x70, 0x69, 0x72, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x5f, 0x6d, 0x6f, 0x6e, 0x74, 0x68, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0c, 0x42, 0x07,
	0x9a, 0xfc, 0xeb, 0x8e, 0x02, 0x01, 0x64, 0x52, 0x1a, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x45, 0x78, 0x70, 0x69, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x4d, 0x6f,
	0x6e, 0x74, 0x68, 0x12, 0x47, 0x0a, 0x1b, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x5f, 0x65, 0x78, 0x70, 0x69, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x79, 0x65,
	0x61, 0x72, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0c, 0x42, 0x07, 0x9a, 0xfc, 0xeb, 0x8e, 0x02, 0x01,
	0x64, 0x52, 0x19, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x45, 0x78,
	0x70, 0x69, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x59, 0x65, 0x61, 0x72, 0x12, 0x23, 0x0a, 0x0d,
	0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x63, 0x79, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0c, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x63, 0x79, 0x43, 0x6f, 0x64,
	0x65, 0x12, 0x2d, 0x0a, 0x12, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e,
	0x5f, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x05, 0x20, 0x01, 0x28, 0x01, 0x52, 0x11, 0x74,
	0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74,
	0x12, 0x27, 0x0a, 0x0f, 0x63, 0x61, 0x72, 0x64, 0x68, 0x6f, 0x6c, 0x64, 0x65, 0x72, 0x5f, 0x6e,
	0x61, 0x6d, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x0e, 0x63, 0x61, 0x72, 0x64, 0x68,
	0x6f, 0x6c, 0x64, 0x65, 0x72, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x44, 0x0a, 0x1e, 0x64, 0x65, 0x76,
	0x69, 0x63, 0x65, 0x5f, 0x6d, 0x61, 0x6e, 0x75, 0x66, 0x61, 0x63, 0x74, 0x75, 0x72, 0x65, 0x72,
	0x5f, 0x69, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x66, 0x69, 0x65, 0x72, 0x18, 0x07, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x1c, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x4d, 0x61, 0x6e, 0x75, 0x66, 0x61, 0x63,
	0x74, 0x75, 0x72, 0x65, 0x72, 0x49, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x66, 0x69, 0x65, 0x72, 0x12,
	0x2a, 0x0a, 0x11, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x64, 0x61, 0x74, 0x61, 0x5f,
	0x74, 0x79, 0x70, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f, 0x70, 0x61, 0x79, 0x6d,
	0x65, 0x6e, 0x74, 0x44, 0x61, 0x74, 0x61, 0x54, 0x79, 0x70, 0x65, 0x12, 0x56, 0x0a, 0x0c, 0x70,
	0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x64, 0x61, 0x74, 0x61, 0x18, 0x09, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x33, 0x2e, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x2e, 0x73,
	0x6d, 0x61, 0x72, 0x74, 0x5f, 0x70, 0x61, 0x79, 0x2e, 0x73, 0x6d, 0x61, 0x72, 0x74, 0x5f, 0x70,
	0x61, 0x79, 0x2e, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x44, 0x61, 0x74, 0x61, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x52, 0x0b, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x44,
	0x61, 0x74, 0x61, 0x12, 0x18, 0x0a, 0x07, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x0a,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x22, 0xbf, 0x01,
	0x0a, 0x13, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x44, 0x61, 0x74, 0x61, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x3a, 0x0a, 0x19, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f,
	0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x63, 0x72, 0x79, 0x70, 0x74, 0x6f, 0x67, 0x72,
	0x61, 0x6d, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x17, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65,
	0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x43, 0x72, 0x79, 0x70, 0x74, 0x6f, 0x67, 0x72, 0x61,
	0x6d, 0x12, 0x23, 0x0a, 0x0d, 0x65, 0x63, 0x69, 0x5f, 0x69, 0x6e, 0x64, 0x69, 0x63, 0x61, 0x74,
	0x6f, 0x72, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x65, 0x63, 0x69, 0x49, 0x6e, 0x64,
	0x69, 0x63, 0x61, 0x74, 0x6f, 0x72, 0x12, 0x19, 0x0a, 0x08, 0x65, 0x6d, 0x76, 0x5f, 0x64, 0x61,
	0x74, 0x61, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x07, 0x65, 0x6d, 0x76, 0x44, 0x61, 0x74,
	0x61, 0x12, 0x2c, 0x0a, 0x12, 0x65, 0x6e, 0x63, 0x72, 0x79, 0x70, 0x74, 0x65, 0x64, 0x5f, 0x70,
	0x69, 0x6e, 0x5f, 0x64, 0x61, 0x74, 0x61, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x10, 0x65,
	0x6e, 0x63, 0x72, 0x79, 0x70, 0x74, 0x65, 0x64, 0x50, 0x69, 0x6e, 0x44, 0x61, 0x74, 0x61, 0x22,
	0x31, 0x0a, 0x19, 0x44, 0x65, 0x63, 0x72, 0x79, 0x70, 0x74, 0x47, 0x50, 0x61, 0x79, 0x54, 0x6f,
	0x6b, 0x65, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x56, 0x31, 0x12, 0x14, 0x0a, 0x05,
	0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x74, 0x6f, 0x6b,
	0x65, 0x6e, 0x22, 0xad, 0x02, 0x0a, 0x1a, 0x44, 0x65, 0x63, 0x72, 0x79, 0x70, 0x74, 0x47, 0x50,
	0x61, 0x79, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x56,
	0x31, 0x12, 0x2e, 0x0a, 0x13, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x5f, 0x6d, 0x65, 0x72,
	0x63, 0x68, 0x61, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x11,
	0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x4d, 0x65, 0x72, 0x63, 0x68, 0x61, 0x6e, 0x74, 0x49,
	0x64, 0x12, 0x2d, 0x0a, 0x12, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x5f, 0x65, 0x78, 0x70,
	0x69, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x11, 0x6d,
	0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x45, 0x78, 0x70, 0x69, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x12, 0x1d, 0x0a, 0x0a, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x49, 0x64, 0x12,
	0x25, 0x0a, 0x0e, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x6d, 0x65, 0x74, 0x68, 0x6f,
	0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74,
	0x4d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x12, 0x6a, 0x0a, 0x16, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e,
	0x74, 0x5f, 0x6d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x5f, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73,
	0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x34, 0x2e, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73,
	0x69, 0x6e, 0x67, 0x2e, 0x73, 0x6d, 0x61, 0x72, 0x74, 0x5f, 0x70, 0x61, 0x79, 0x2e, 0x73, 0x6d,
	0x61, 0x72, 0x74, 0x5f, 0x70, 0x61, 0x79, 0x2e, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x4d,
	0x65, 0x74, 0x68, 0x6f, 0x64, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x52, 0x14, 0x70, 0x61,
	0x79, 0x6d, 0x65, 0x6e, 0x74, 0x4d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x44, 0x65, 0x74, 0x61, 0x69,
	0x6c, 0x73, 0x22, 0xca, 0x02, 0x0a, 0x14, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x4d, 0x65,
	0x74, 0x68, 0x6f, 0x64, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x12, 0x27, 0x0a, 0x0f, 0x65,
	0x78, 0x70, 0x69, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x79, 0x65, 0x61, 0x72, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x0c, 0x52, 0x0e, 0x65, 0x78, 0x70, 0x69, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x59, 0x65, 0x61, 0x72, 0x12, 0x29, 0x0a, 0x10, 0x65, 0x78, 0x70, 0x69, 0x72, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x5f, 0x6d, 0x6f, 0x6e, 0x74, 0x68, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x0f,
	0x65, 0x78, 0x70, 0x69, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x4d, 0x6f, 0x6e, 0x74, 0x68, 0x12,
	0x19, 0x0a, 0x03, 0x70, 0x61, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0c, 0x42, 0x07, 0x9a, 0xfc,
	0xeb, 0x8e, 0x02, 0x01, 0x64, 0x52, 0x03, 0x70, 0x61, 0x6e, 0x12, 0x1f, 0x0a, 0x0b, 0x61, 0x75,
	0x74, 0x68, 0x5f, 0x6d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0a, 0x61, 0x75, 0x74, 0x68, 0x4d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x12, 0x23, 0x0a, 0x0d, 0x65,
	0x63, 0x69, 0x5f, 0x69, 0x6e, 0x64, 0x69, 0x63, 0x61, 0x74, 0x6f, 0x72, 0x18, 0x05, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0c, 0x65, 0x63, 0x69, 0x49, 0x6e, 0x64, 0x69, 0x63, 0x61, 0x74, 0x6f, 0x72,
	0x12, 0x1e, 0x0a, 0x0a, 0x63, 0x72, 0x79, 0x70, 0x74, 0x6f, 0x67, 0x72, 0x61, 0x6d, 0x18, 0x06,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x63, 0x72, 0x79, 0x70, 0x74, 0x6f, 0x67, 0x72, 0x61, 0x6d,
	0x12, 0x5d, 0x0a, 0x11, 0x61, 0x73, 0x73, 0x75, 0x72, 0x61, 0x6e, 0x63, 0x65, 0x5f, 0x64, 0x65,
	0x74, 0x61, 0x69, 0x6c, 0x73, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x30, 0x2e, 0x70, 0x72,
	0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x2e, 0x73, 0x6d, 0x61, 0x72, 0x74, 0x5f, 0x70,
	0x61, 0x79, 0x2e, 0x73, 0x6d, 0x61, 0x72, 0x74, 0x5f, 0x70, 0x61, 0x79, 0x2e, 0x41, 0x73, 0x73,
	0x75, 0x72, 0x61, 0x6e, 0x63, 0x65, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x52, 0x10, 0x61,
	0x73, 0x73, 0x75, 0x72, 0x61, 0x6e, 0x63, 0x65, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x22,
	0x79, 0x0a, 0x10, 0x41, 0x73, 0x73, 0x75, 0x72, 0x61, 0x6e, 0x63, 0x65, 0x44, 0x65, 0x74, 0x61,
	0x69, 0x6c, 0x73, 0x12, 0x29, 0x0a, 0x10, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x76,
	0x65, 0x72, 0x69, 0x66, 0x69, 0x65, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0f, 0x61,
	0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x56, 0x65, 0x72, 0x69, 0x66, 0x69, 0x65, 0x64, 0x12, 0x3a,
	0x0a, 0x19, 0x63, 0x61, 0x72, 0x64, 0x5f, 0x68, 0x6f, 0x6c, 0x64, 0x65, 0x72, 0x5f, 0x61, 0x75,
	0x74, 0x68, 0x65, 0x6e, 0x74, 0x69, 0x63, 0x61, 0x74, 0x65, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x08, 0x52, 0x17, 0x63, 0x61, 0x72, 0x64, 0x48, 0x6f, 0x6c, 0x64, 0x65, 0x72, 0x41, 0x75, 0x74,
	0x68, 0x65, 0x6e, 0x74, 0x69, 0x63, 0x61, 0x74, 0x65, 0x64, 0x22, 0x3c, 0x0a, 0x1b, 0x47, 0x65,
	0x74, 0x47, 0x50, 0x61, 0x79, 0x43, 0x72, 0x65, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x61, 0x6c, 0x73,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x56, 0x31, 0x12, 0x1d, 0x0a, 0x0a, 0x70, 0x72, 0x6f,
	0x6a, 0x65, 0x63, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x09, 0x70,
	0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x49, 0x64, 0x22, 0x92, 0x01, 0x0a, 0x1c, 0x47, 0x65, 0x74,
	0x47, 0x50, 0x61, 0x79, 0x43, 0x72, 0x65, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x61, 0x6c, 0x73, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x56, 0x31, 0x12, 0x3d, 0x0a, 0x1b, 0x67, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x5f, 0x70, 0x61, 0x79, 0x5f, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x5f,
	0x6d, 0x65, 0x72, 0x63, 0x68, 0x61, 0x6e, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x18,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x50, 0x61, 0x79, 0x47, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79,
	0x4d, 0x65, 0x72, 0x63, 0x68, 0x61, 0x6e, 0x74, 0x12, 0x33, 0x0a, 0x16, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x5f, 0x70, 0x61, 0x79, 0x5f, 0x6d, 0x65, 0x72, 0x63, 0x68, 0x61, 0x6e, 0x74, 0x5f,
	0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x13, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x50, 0x61, 0x79, 0x4d, 0x65, 0x72, 0x63, 0x68, 0x61, 0x6e, 0x74, 0x49, 0x64, 0x32, 0xb5, 0x04,
	0x0a, 0x08, 0x53, 0x6d, 0x61, 0x72, 0x74, 0x50, 0x61, 0x79, 0x12, 0x88, 0x01, 0x0a, 0x0f, 0x41,
	0x70, 0x70, 0x6c, 0x65, 0x50, 0x61, 0x79, 0x53, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x38,
	0x2e, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x2e, 0x73, 0x6d, 0x61, 0x72,
	0x74, 0x5f, 0x70, 0x61, 0x79, 0x2e, 0x73, 0x6d, 0x61, 0x72, 0x74, 0x5f, 0x70, 0x61, 0x79, 0x2e,
	0x41, 0x70, 0x70, 0x6c, 0x65, 0x50, 0x61, 0x79, 0x53, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x56, 0x31, 0x1a, 0x39, 0x2e, 0x70, 0x72, 0x6f, 0x63, 0x65,
	0x73, 0x73, 0x69, 0x6e, 0x67, 0x2e, 0x73, 0x6d, 0x61, 0x72, 0x74, 0x5f, 0x70, 0x61, 0x79, 0x2e,
	0x73, 0x6d, 0x61, 0x72, 0x74, 0x5f, 0x70, 0x61, 0x79, 0x2e, 0x41, 0x70, 0x70, 0x6c, 0x65, 0x50,
	0x61, 0x79, 0x53, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x56, 0x31, 0x22, 0x00, 0x12, 0x7c, 0x0a, 0x0b, 0x44, 0x65, 0x63, 0x6f, 0x64, 0x65, 0x54,
	0x6f, 0x6b, 0x65, 0x6e, 0x12, 0x34, 0x2e, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e,
	0x67, 0x2e, 0x73, 0x6d, 0x61, 0x72, 0x74, 0x5f, 0x70, 0x61, 0x79, 0x2e, 0x73, 0x6d, 0x61, 0x72,
	0x74, 0x5f, 0x70, 0x61, 0x79, 0x2e, 0x44, 0x65, 0x63, 0x6f, 0x64, 0x65, 0x54, 0x6f, 0x6b, 0x65,
	0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x56, 0x31, 0x1a, 0x35, 0x2e, 0x70, 0x72, 0x6f,
	0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x2e, 0x73, 0x6d, 0x61, 0x72, 0x74, 0x5f, 0x70, 0x61,
	0x79, 0x2e, 0x73, 0x6d, 0x61, 0x72, 0x74, 0x5f, 0x70, 0x61, 0x79, 0x2e, 0x44, 0x65, 0x63, 0x6f,
	0x64, 0x65, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x56,
	0x31, 0x22, 0x00, 0x12, 0x8b, 0x01, 0x0a, 0x10, 0x44, 0x65, 0x63, 0x72, 0x79, 0x70, 0x74, 0x47,
	0x50, 0x61, 0x79, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x12, 0x39, 0x2e, 0x70, 0x72, 0x6f, 0x63, 0x65,
	0x73, 0x73, 0x69, 0x6e, 0x67, 0x2e, 0x73, 0x6d, 0x61, 0x72, 0x74, 0x5f, 0x70, 0x61, 0x79, 0x2e,
	0x73, 0x6d, 0x61, 0x72, 0x74, 0x5f, 0x70, 0x61, 0x79, 0x2e, 0x44, 0x65, 0x63, 0x72, 0x79, 0x70,
	0x74, 0x47, 0x50, 0x61, 0x79, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x56, 0x31, 0x1a, 0x3a, 0x2e, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67,
	0x2e, 0x73, 0x6d, 0x61, 0x72, 0x74, 0x5f, 0x70, 0x61, 0x79, 0x2e, 0x73, 0x6d, 0x61, 0x72, 0x74,
	0x5f, 0x70, 0x61, 0x79, 0x2e, 0x44, 0x65, 0x63, 0x72, 0x79, 0x70, 0x74, 0x47, 0x50, 0x61, 0x79,
	0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x56, 0x31, 0x22,
	0x00, 0x12, 0x91, 0x01, 0x0a, 0x12, 0x47, 0x65, 0x74, 0x47, 0x50, 0x61, 0x79, 0x43, 0x72, 0x65,
	0x64, 0x65, 0x6e, 0x74, 0x69, 0x61, 0x6c, 0x73, 0x12, 0x3b, 0x2e, 0x70, 0x72, 0x6f, 0x63, 0x65,
	0x73, 0x73, 0x69, 0x6e, 0x67, 0x2e, 0x73, 0x6d, 0x61, 0x72, 0x74, 0x5f, 0x70, 0x61, 0x79, 0x2e,
	0x73, 0x6d, 0x61, 0x72, 0x74, 0x5f, 0x70, 0x61, 0x79, 0x2e, 0x47, 0x65, 0x74, 0x47, 0x50, 0x61,
	0x79, 0x43, 0x72, 0x65, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x61, 0x6c, 0x73, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x56, 0x31, 0x1a, 0x3c, 0x2e, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69,
	0x6e, 0x67, 0x2e, 0x73, 0x6d, 0x61, 0x72, 0x74, 0x5f, 0x70, 0x61, 0x79, 0x2e, 0x73, 0x6d, 0x61,
	0x72, 0x74, 0x5f, 0x70, 0x61, 0x79, 0x2e, 0x47, 0x65, 0x74, 0x47, 0x50, 0x61, 0x79, 0x43, 0x72,
	0x65, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x61, 0x6c, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x56, 0x31, 0x22, 0x00, 0x42, 0x2d, 0x5a, 0x2b, 0x67, 0x69, 0x74, 0x2e, 0x6c, 0x6f, 0x63,
	0x61, 0x6c, 0x2f, 0x73, 0x65, 0x6e, 0x73, 0x69, 0x74, 0x69, 0x76, 0x65, 0x2f, 0x69, 0x6e, 0x6e,
	0x65, 0x72, 0x70, 0x62, 0x2f, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x2f,
	0x67, 0x72, 0x70, 0x63, 0x62, 0x08, 0x65, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x70, 0xe8,
	0x07,
})

var (
	file_inner_processing_grpc_smart_pay_proto_rawDescOnce sync.Once
	file_inner_processing_grpc_smart_pay_proto_rawDescData []byte
)

func file_inner_processing_grpc_smart_pay_proto_rawDescGZIP() []byte {
	file_inner_processing_grpc_smart_pay_proto_rawDescOnce.Do(func() {
		file_inner_processing_grpc_smart_pay_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_inner_processing_grpc_smart_pay_proto_rawDesc), len(file_inner_processing_grpc_smart_pay_proto_rawDesc)))
	})
	return file_inner_processing_grpc_smart_pay_proto_rawDescData
}

var file_inner_processing_grpc_smart_pay_proto_msgTypes = make([]protoimpl.MessageInfo, 14)
var file_inner_processing_grpc_smart_pay_proto_goTypes = []any{
	(*ApplePaySessionRequestV1)(nil),     // 0: processing.smart_pay.smart_pay.ApplePaySessionRequestV1
	(*ApplePaySessionResponseV1)(nil),    // 1: processing.smart_pay.smart_pay.ApplePaySessionResponseV1
	(*DecodeTokenRequestV1)(nil),         // 2: processing.smart_pay.smart_pay.DecodeTokenRequestV1
	(*PaymentMethod)(nil),                // 3: processing.smart_pay.smart_pay.PaymentMethod
	(*PaymentData)(nil),                  // 4: processing.smart_pay.smart_pay.PaymentData
	(*Header)(nil),                       // 5: processing.smart_pay.smart_pay.Header
	(*DecodeTokenResponseV1)(nil),        // 6: processing.smart_pay.smart_pay.DecodeTokenResponseV1
	(*PaymentDataResponse)(nil),          // 7: processing.smart_pay.smart_pay.PaymentDataResponse
	(*DecryptGPayTokenRequestV1)(nil),    // 8: processing.smart_pay.smart_pay.DecryptGPayTokenRequestV1
	(*DecryptGPayTokenResponseV1)(nil),   // 9: processing.smart_pay.smart_pay.DecryptGPayTokenResponseV1
	(*PaymentMethodDetails)(nil),         // 10: processing.smart_pay.smart_pay.PaymentMethodDetails
	(*AssuranceDetails)(nil),             // 11: processing.smart_pay.smart_pay.AssuranceDetails
	(*GetGPayCredentialsRequestV1)(nil),  // 12: processing.smart_pay.smart_pay.GetGPayCredentialsRequestV1
	(*GetGPayCredentialsResponseV1)(nil), // 13: processing.smart_pay.smart_pay.GetGPayCredentialsResponseV1
}
var file_inner_processing_grpc_smart_pay_proto_depIdxs = []int32{
	3,  // 0: processing.smart_pay.smart_pay.DecodeTokenRequestV1.method:type_name -> processing.smart_pay.smart_pay.PaymentMethod
	4,  // 1: processing.smart_pay.smart_pay.DecodeTokenRequestV1.data:type_name -> processing.smart_pay.smart_pay.PaymentData
	5,  // 2: processing.smart_pay.smart_pay.PaymentData.header:type_name -> processing.smart_pay.smart_pay.Header
	7,  // 3: processing.smart_pay.smart_pay.DecodeTokenResponseV1.payment_data:type_name -> processing.smart_pay.smart_pay.PaymentDataResponse
	10, // 4: processing.smart_pay.smart_pay.DecryptGPayTokenResponseV1.payment_method_details:type_name -> processing.smart_pay.smart_pay.PaymentMethodDetails
	11, // 5: processing.smart_pay.smart_pay.PaymentMethodDetails.assurance_details:type_name -> processing.smart_pay.smart_pay.AssuranceDetails
	0,  // 6: processing.smart_pay.smart_pay.SmartPay.ApplePaySession:input_type -> processing.smart_pay.smart_pay.ApplePaySessionRequestV1
	2,  // 7: processing.smart_pay.smart_pay.SmartPay.DecodeToken:input_type -> processing.smart_pay.smart_pay.DecodeTokenRequestV1
	8,  // 8: processing.smart_pay.smart_pay.SmartPay.DecryptGPayToken:input_type -> processing.smart_pay.smart_pay.DecryptGPayTokenRequestV1
	12, // 9: processing.smart_pay.smart_pay.SmartPay.GetGPayCredentials:input_type -> processing.smart_pay.smart_pay.GetGPayCredentialsRequestV1
	1,  // 10: processing.smart_pay.smart_pay.SmartPay.ApplePaySession:output_type -> processing.smart_pay.smart_pay.ApplePaySessionResponseV1
	6,  // 11: processing.smart_pay.smart_pay.SmartPay.DecodeToken:output_type -> processing.smart_pay.smart_pay.DecodeTokenResponseV1
	9,  // 12: processing.smart_pay.smart_pay.SmartPay.DecryptGPayToken:output_type -> processing.smart_pay.smart_pay.DecryptGPayTokenResponseV1
	13, // 13: processing.smart_pay.smart_pay.SmartPay.GetGPayCredentials:output_type -> processing.smart_pay.smart_pay.GetGPayCredentialsResponseV1
	10, // [10:14] is the sub-list for method output_type
	6,  // [6:10] is the sub-list for method input_type
	6,  // [6:6] is the sub-list for extension type_name
	6,  // [6:6] is the sub-list for extension extendee
	0,  // [0:6] is the sub-list for field type_name
}

func init() { file_inner_processing_grpc_smart_pay_proto_init() }
func file_inner_processing_grpc_smart_pay_proto_init() {
	if File_inner_processing_grpc_smart_pay_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_inner_processing_grpc_smart_pay_proto_rawDesc), len(file_inner_processing_grpc_smart_pay_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   14,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_inner_processing_grpc_smart_pay_proto_goTypes,
		DependencyIndexes: file_inner_processing_grpc_smart_pay_proto_depIdxs,
		MessageInfos:      file_inner_processing_grpc_smart_pay_proto_msgTypes,
	}.Build()
	File_inner_processing_grpc_smart_pay_proto = out.File
	file_inner_processing_grpc_smart_pay_proto_goTypes = nil
	file_inner_processing_grpc_smart_pay_proto_depIdxs = nil
}
