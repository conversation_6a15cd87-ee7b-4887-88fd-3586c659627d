// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.5.1
// - protoc             v5.29.3
// source: inner/processing/grpc/epay.proto

package grpc

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.64.0 or later.
const _ = grpc.SupportPackageIsVersion9

const (
	Epay_PayIn_FullMethodName                              = "/processing.epay.epay.Epay/PayIn"
	Epay_OneClickPayIn_FullMethodName                      = "/processing.epay.epay.Epay/OneClickPayIn"
	Epay_ThreeDSConfirm_FullMethodName                     = "/processing.epay.epay.Epay/ThreeDSConfirm"
	Epay_ThreeDSResume_FullMethodName                      = "/processing.epay.epay.Epay/ThreeDSResume"
	Epay_PayOut_FullMethodName                             = "/processing.epay.epay.Epay/PayOut"
	Epay_GetBankTransactionStatus_FullMethodName           = "/processing.epay.epay.Epay/GetBankTransactionStatus"
	Epay_GetBankTransactionStatusUnformated_FullMethodName = "/processing.epay.epay.Epay/GetBankTransactionStatusUnformated"
	Epay_Refund_FullMethodName                             = "/processing.epay.epay.Epay/Refund"
	Epay_GooglePay_FullMethodName                          = "/processing.epay.epay.Epay/GooglePay"
	Epay_ApplePay_FullMethodName                           = "/processing.epay.epay.Epay/ApplePay"
	Epay_TwoStagePayIn_FullMethodName                      = "/processing.epay.epay.Epay/TwoStagePayIn"
	Epay_Charge_FullMethodName                             = "/processing.epay.epay.Epay/Charge"
	Epay_Cancel_FullMethodName                             = "/processing.epay.epay.Epay/Cancel"
	Epay_MakeToken_FullMethodName                          = "/processing.epay.epay.Epay/MakeToken"
	Epay_GetAcquirerIdentifier_FullMethodName              = "/processing.epay.epay.Epay/GetAcquirerIdentifier"
	Epay_ResolveVisaAlias_FullMethodName                   = "/processing.epay.epay.Epay/ResolveVisaAlias"
	Epay_PayOutByPhone_FullMethodName                      = "/processing.epay.epay.Epay/PayOutByPhone"
)

// EpayClient is the client API for Epay service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type EpayClient interface {
	PayIn(ctx context.Context, in *PayInRequestData, opts ...grpc.CallOption) (*PayInResponseData, error)
	OneClickPayIn(ctx context.Context, in *OneClickPayInRequestData, opts ...grpc.CallOption) (*PayInResponseData, error)
	ThreeDSConfirm(ctx context.Context, in *ThreeDSRequestData, opts ...grpc.CallOption) (*ThreeDSResponseData, error)
	ThreeDSResume(ctx context.Context, in *ThreeDSResumeRequest, opts ...grpc.CallOption) (*ThreeDSResumeResponse, error)
	PayOut(ctx context.Context, in *PayOutRequestData, opts ...grpc.CallOption) (*PayOutResponseData, error)
	GetBankTransactionStatus(ctx context.Context, in *BankTransactionStatusRequest, opts ...grpc.CallOption) (*BankTransactionStatusResponse, error)
	GetBankTransactionStatusUnformated(ctx context.Context, in *BankTransactionStatusUnformatedRequest, opts ...grpc.CallOption) (*BankTransactionStatusUnformatedResponse, error)
	Refund(ctx context.Context, in *RefundRequest, opts ...grpc.CallOption) (*RefundResponse, error)
	GooglePay(ctx context.Context, in *GooglePayRequestData, opts ...grpc.CallOption) (*GooglePayResponseData, error)
	ApplePay(ctx context.Context, in *ApplePayRequestData, opts ...grpc.CallOption) (*ApplePayResponseData, error)
	TwoStagePayIn(ctx context.Context, in *TwoStagePayInRequest, opts ...grpc.CallOption) (*TwoStagePayInResponse, error)
	Charge(ctx context.Context, in *ChargeRequest, opts ...grpc.CallOption) (*ChargeResponse, error)
	Cancel(ctx context.Context, in *CancelRequest, opts ...grpc.CallOption) (*CancelResponse, error)
	MakeToken(ctx context.Context, in *PayInRequestData, opts ...grpc.CallOption) (*PayInResponseData, error)
	GetAcquirerIdentifier(ctx context.Context, in *emptypb.Empty, opts ...grpc.CallOption) (*GetAcquirerIdentifierResponse, error)
	ResolveVisaAlias(ctx context.Context, in *ResolveVisaAliasRequest, opts ...grpc.CallOption) (*ResolveVisaAliasResponse, error)
	PayOutByPhone(ctx context.Context, in *PayOutByPhoneRequestData, opts ...grpc.CallOption) (*PayOutResponseByPhoneData, error)
}

type epayClient struct {
	cc grpc.ClientConnInterface
}

func NewEpayClient(cc grpc.ClientConnInterface) EpayClient {
	return &epayClient{cc}
}

func (c *epayClient) PayIn(ctx context.Context, in *PayInRequestData, opts ...grpc.CallOption) (*PayInResponseData, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(PayInResponseData)
	err := c.cc.Invoke(ctx, Epay_PayIn_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *epayClient) OneClickPayIn(ctx context.Context, in *OneClickPayInRequestData, opts ...grpc.CallOption) (*PayInResponseData, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(PayInResponseData)
	err := c.cc.Invoke(ctx, Epay_OneClickPayIn_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *epayClient) ThreeDSConfirm(ctx context.Context, in *ThreeDSRequestData, opts ...grpc.CallOption) (*ThreeDSResponseData, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ThreeDSResponseData)
	err := c.cc.Invoke(ctx, Epay_ThreeDSConfirm_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *epayClient) ThreeDSResume(ctx context.Context, in *ThreeDSResumeRequest, opts ...grpc.CallOption) (*ThreeDSResumeResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ThreeDSResumeResponse)
	err := c.cc.Invoke(ctx, Epay_ThreeDSResume_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *epayClient) PayOut(ctx context.Context, in *PayOutRequestData, opts ...grpc.CallOption) (*PayOutResponseData, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(PayOutResponseData)
	err := c.cc.Invoke(ctx, Epay_PayOut_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *epayClient) GetBankTransactionStatus(ctx context.Context, in *BankTransactionStatusRequest, opts ...grpc.CallOption) (*BankTransactionStatusResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(BankTransactionStatusResponse)
	err := c.cc.Invoke(ctx, Epay_GetBankTransactionStatus_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *epayClient) GetBankTransactionStatusUnformated(ctx context.Context, in *BankTransactionStatusUnformatedRequest, opts ...grpc.CallOption) (*BankTransactionStatusUnformatedResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(BankTransactionStatusUnformatedResponse)
	err := c.cc.Invoke(ctx, Epay_GetBankTransactionStatusUnformated_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *epayClient) Refund(ctx context.Context, in *RefundRequest, opts ...grpc.CallOption) (*RefundResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(RefundResponse)
	err := c.cc.Invoke(ctx, Epay_Refund_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *epayClient) GooglePay(ctx context.Context, in *GooglePayRequestData, opts ...grpc.CallOption) (*GooglePayResponseData, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GooglePayResponseData)
	err := c.cc.Invoke(ctx, Epay_GooglePay_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *epayClient) ApplePay(ctx context.Context, in *ApplePayRequestData, opts ...grpc.CallOption) (*ApplePayResponseData, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ApplePayResponseData)
	err := c.cc.Invoke(ctx, Epay_ApplePay_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *epayClient) TwoStagePayIn(ctx context.Context, in *TwoStagePayInRequest, opts ...grpc.CallOption) (*TwoStagePayInResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(TwoStagePayInResponse)
	err := c.cc.Invoke(ctx, Epay_TwoStagePayIn_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *epayClient) Charge(ctx context.Context, in *ChargeRequest, opts ...grpc.CallOption) (*ChargeResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ChargeResponse)
	err := c.cc.Invoke(ctx, Epay_Charge_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *epayClient) Cancel(ctx context.Context, in *CancelRequest, opts ...grpc.CallOption) (*CancelResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(CancelResponse)
	err := c.cc.Invoke(ctx, Epay_Cancel_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *epayClient) MakeToken(ctx context.Context, in *PayInRequestData, opts ...grpc.CallOption) (*PayInResponseData, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(PayInResponseData)
	err := c.cc.Invoke(ctx, Epay_MakeToken_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *epayClient) GetAcquirerIdentifier(ctx context.Context, in *emptypb.Empty, opts ...grpc.CallOption) (*GetAcquirerIdentifierResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetAcquirerIdentifierResponse)
	err := c.cc.Invoke(ctx, Epay_GetAcquirerIdentifier_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *epayClient) ResolveVisaAlias(ctx context.Context, in *ResolveVisaAliasRequest, opts ...grpc.CallOption) (*ResolveVisaAliasResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ResolveVisaAliasResponse)
	err := c.cc.Invoke(ctx, Epay_ResolveVisaAlias_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *epayClient) PayOutByPhone(ctx context.Context, in *PayOutByPhoneRequestData, opts ...grpc.CallOption) (*PayOutResponseByPhoneData, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(PayOutResponseByPhoneData)
	err := c.cc.Invoke(ctx, Epay_PayOutByPhone_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// EpayServer is the server API for Epay service.
// All implementations must embed UnimplementedEpayServer
// for forward compatibility.
type EpayServer interface {
	PayIn(context.Context, *PayInRequestData) (*PayInResponseData, error)
	OneClickPayIn(context.Context, *OneClickPayInRequestData) (*PayInResponseData, error)
	ThreeDSConfirm(context.Context, *ThreeDSRequestData) (*ThreeDSResponseData, error)
	ThreeDSResume(context.Context, *ThreeDSResumeRequest) (*ThreeDSResumeResponse, error)
	PayOut(context.Context, *PayOutRequestData) (*PayOutResponseData, error)
	GetBankTransactionStatus(context.Context, *BankTransactionStatusRequest) (*BankTransactionStatusResponse, error)
	GetBankTransactionStatusUnformated(context.Context, *BankTransactionStatusUnformatedRequest) (*BankTransactionStatusUnformatedResponse, error)
	Refund(context.Context, *RefundRequest) (*RefundResponse, error)
	GooglePay(context.Context, *GooglePayRequestData) (*GooglePayResponseData, error)
	ApplePay(context.Context, *ApplePayRequestData) (*ApplePayResponseData, error)
	TwoStagePayIn(context.Context, *TwoStagePayInRequest) (*TwoStagePayInResponse, error)
	Charge(context.Context, *ChargeRequest) (*ChargeResponse, error)
	Cancel(context.Context, *CancelRequest) (*CancelResponse, error)
	MakeToken(context.Context, *PayInRequestData) (*PayInResponseData, error)
	GetAcquirerIdentifier(context.Context, *emptypb.Empty) (*GetAcquirerIdentifierResponse, error)
	ResolveVisaAlias(context.Context, *ResolveVisaAliasRequest) (*ResolveVisaAliasResponse, error)
	PayOutByPhone(context.Context, *PayOutByPhoneRequestData) (*PayOutResponseByPhoneData, error)
	mustEmbedUnimplementedEpayServer()
}

// UnimplementedEpayServer must be embedded to have
// forward compatible implementations.
//
// NOTE: this should be embedded by value instead of pointer to avoid a nil
// pointer dereference when methods are called.
type UnimplementedEpayServer struct{}

func (UnimplementedEpayServer) PayIn(context.Context, *PayInRequestData) (*PayInResponseData, error) {
	return nil, status.Errorf(codes.Unimplemented, "method PayIn not implemented")
}
func (UnimplementedEpayServer) OneClickPayIn(context.Context, *OneClickPayInRequestData) (*PayInResponseData, error) {
	return nil, status.Errorf(codes.Unimplemented, "method OneClickPayIn not implemented")
}
func (UnimplementedEpayServer) ThreeDSConfirm(context.Context, *ThreeDSRequestData) (*ThreeDSResponseData, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ThreeDSConfirm not implemented")
}
func (UnimplementedEpayServer) ThreeDSResume(context.Context, *ThreeDSResumeRequest) (*ThreeDSResumeResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ThreeDSResume not implemented")
}
func (UnimplementedEpayServer) PayOut(context.Context, *PayOutRequestData) (*PayOutResponseData, error) {
	return nil, status.Errorf(codes.Unimplemented, "method PayOut not implemented")
}
func (UnimplementedEpayServer) GetBankTransactionStatus(context.Context, *BankTransactionStatusRequest) (*BankTransactionStatusResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetBankTransactionStatus not implemented")
}
func (UnimplementedEpayServer) GetBankTransactionStatusUnformated(context.Context, *BankTransactionStatusUnformatedRequest) (*BankTransactionStatusUnformatedResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetBankTransactionStatusUnformated not implemented")
}
func (UnimplementedEpayServer) Refund(context.Context, *RefundRequest) (*RefundResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Refund not implemented")
}
func (UnimplementedEpayServer) GooglePay(context.Context, *GooglePayRequestData) (*GooglePayResponseData, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GooglePay not implemented")
}
func (UnimplementedEpayServer) ApplePay(context.Context, *ApplePayRequestData) (*ApplePayResponseData, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ApplePay not implemented")
}
func (UnimplementedEpayServer) TwoStagePayIn(context.Context, *TwoStagePayInRequest) (*TwoStagePayInResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method TwoStagePayIn not implemented")
}
func (UnimplementedEpayServer) Charge(context.Context, *ChargeRequest) (*ChargeResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Charge not implemented")
}
func (UnimplementedEpayServer) Cancel(context.Context, *CancelRequest) (*CancelResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Cancel not implemented")
}
func (UnimplementedEpayServer) MakeToken(context.Context, *PayInRequestData) (*PayInResponseData, error) {
	return nil, status.Errorf(codes.Unimplemented, "method MakeToken not implemented")
}
func (UnimplementedEpayServer) GetAcquirerIdentifier(context.Context, *emptypb.Empty) (*GetAcquirerIdentifierResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetAcquirerIdentifier not implemented")
}
func (UnimplementedEpayServer) ResolveVisaAlias(context.Context, *ResolveVisaAliasRequest) (*ResolveVisaAliasResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ResolveVisaAlias not implemented")
}
func (UnimplementedEpayServer) PayOutByPhone(context.Context, *PayOutByPhoneRequestData) (*PayOutResponseByPhoneData, error) {
	return nil, status.Errorf(codes.Unimplemented, "method PayOutByPhone not implemented")
}
func (UnimplementedEpayServer) mustEmbedUnimplementedEpayServer() {}
func (UnimplementedEpayServer) testEmbeddedByValue()              {}

// UnsafeEpayServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to EpayServer will
// result in compilation errors.
type UnsafeEpayServer interface {
	mustEmbedUnimplementedEpayServer()
}

func RegisterEpayServer(s grpc.ServiceRegistrar, srv EpayServer) {
	// If the following call pancis, it indicates UnimplementedEpayServer was
	// embedded by pointer and is nil.  This will cause panics if an
	// unimplemented method is ever invoked, so we test this at initialization
	// time to prevent it from happening at runtime later due to I/O.
	if t, ok := srv.(interface{ testEmbeddedByValue() }); ok {
		t.testEmbeddedByValue()
	}
	s.RegisterService(&Epay_ServiceDesc, srv)
}

func _Epay_PayIn_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(PayInRequestData)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EpayServer).PayIn(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Epay_PayIn_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EpayServer).PayIn(ctx, req.(*PayInRequestData))
	}
	return interceptor(ctx, in, info, handler)
}

func _Epay_OneClickPayIn_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(OneClickPayInRequestData)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EpayServer).OneClickPayIn(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Epay_OneClickPayIn_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EpayServer).OneClickPayIn(ctx, req.(*OneClickPayInRequestData))
	}
	return interceptor(ctx, in, info, handler)
}

func _Epay_ThreeDSConfirm_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ThreeDSRequestData)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EpayServer).ThreeDSConfirm(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Epay_ThreeDSConfirm_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EpayServer).ThreeDSConfirm(ctx, req.(*ThreeDSRequestData))
	}
	return interceptor(ctx, in, info, handler)
}

func _Epay_ThreeDSResume_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ThreeDSResumeRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EpayServer).ThreeDSResume(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Epay_ThreeDSResume_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EpayServer).ThreeDSResume(ctx, req.(*ThreeDSResumeRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Epay_PayOut_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(PayOutRequestData)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EpayServer).PayOut(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Epay_PayOut_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EpayServer).PayOut(ctx, req.(*PayOutRequestData))
	}
	return interceptor(ctx, in, info, handler)
}

func _Epay_GetBankTransactionStatus_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BankTransactionStatusRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EpayServer).GetBankTransactionStatus(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Epay_GetBankTransactionStatus_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EpayServer).GetBankTransactionStatus(ctx, req.(*BankTransactionStatusRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Epay_GetBankTransactionStatusUnformated_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BankTransactionStatusUnformatedRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EpayServer).GetBankTransactionStatusUnformated(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Epay_GetBankTransactionStatusUnformated_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EpayServer).GetBankTransactionStatusUnformated(ctx, req.(*BankTransactionStatusUnformatedRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Epay_Refund_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RefundRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EpayServer).Refund(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Epay_Refund_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EpayServer).Refund(ctx, req.(*RefundRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Epay_GooglePay_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GooglePayRequestData)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EpayServer).GooglePay(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Epay_GooglePay_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EpayServer).GooglePay(ctx, req.(*GooglePayRequestData))
	}
	return interceptor(ctx, in, info, handler)
}

func _Epay_ApplePay_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ApplePayRequestData)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EpayServer).ApplePay(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Epay_ApplePay_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EpayServer).ApplePay(ctx, req.(*ApplePayRequestData))
	}
	return interceptor(ctx, in, info, handler)
}

func _Epay_TwoStagePayIn_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(TwoStagePayInRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EpayServer).TwoStagePayIn(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Epay_TwoStagePayIn_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EpayServer).TwoStagePayIn(ctx, req.(*TwoStagePayInRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Epay_Charge_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ChargeRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EpayServer).Charge(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Epay_Charge_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EpayServer).Charge(ctx, req.(*ChargeRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Epay_Cancel_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CancelRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EpayServer).Cancel(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Epay_Cancel_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EpayServer).Cancel(ctx, req.(*CancelRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Epay_MakeToken_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(PayInRequestData)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EpayServer).MakeToken(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Epay_MakeToken_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EpayServer).MakeToken(ctx, req.(*PayInRequestData))
	}
	return interceptor(ctx, in, info, handler)
}

func _Epay_GetAcquirerIdentifier_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(emptypb.Empty)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EpayServer).GetAcquirerIdentifier(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Epay_GetAcquirerIdentifier_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EpayServer).GetAcquirerIdentifier(ctx, req.(*emptypb.Empty))
	}
	return interceptor(ctx, in, info, handler)
}

func _Epay_ResolveVisaAlias_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ResolveVisaAliasRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EpayServer).ResolveVisaAlias(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Epay_ResolveVisaAlias_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EpayServer).ResolveVisaAlias(ctx, req.(*ResolveVisaAliasRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Epay_PayOutByPhone_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(PayOutByPhoneRequestData)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EpayServer).PayOutByPhone(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Epay_PayOutByPhone_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EpayServer).PayOutByPhone(ctx, req.(*PayOutByPhoneRequestData))
	}
	return interceptor(ctx, in, info, handler)
}

// Epay_ServiceDesc is the grpc.ServiceDesc for Epay service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var Epay_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "processing.epay.epay.Epay",
	HandlerType: (*EpayServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "PayIn",
			Handler:    _Epay_PayIn_Handler,
		},
		{
			MethodName: "OneClickPayIn",
			Handler:    _Epay_OneClickPayIn_Handler,
		},
		{
			MethodName: "ThreeDSConfirm",
			Handler:    _Epay_ThreeDSConfirm_Handler,
		},
		{
			MethodName: "ThreeDSResume",
			Handler:    _Epay_ThreeDSResume_Handler,
		},
		{
			MethodName: "PayOut",
			Handler:    _Epay_PayOut_Handler,
		},
		{
			MethodName: "GetBankTransactionStatus",
			Handler:    _Epay_GetBankTransactionStatus_Handler,
		},
		{
			MethodName: "GetBankTransactionStatusUnformated",
			Handler:    _Epay_GetBankTransactionStatusUnformated_Handler,
		},
		{
			MethodName: "Refund",
			Handler:    _Epay_Refund_Handler,
		},
		{
			MethodName: "GooglePay",
			Handler:    _Epay_GooglePay_Handler,
		},
		{
			MethodName: "ApplePay",
			Handler:    _Epay_ApplePay_Handler,
		},
		{
			MethodName: "TwoStagePayIn",
			Handler:    _Epay_TwoStagePayIn_Handler,
		},
		{
			MethodName: "Charge",
			Handler:    _Epay_Charge_Handler,
		},
		{
			MethodName: "Cancel",
			Handler:    _Epay_Cancel_Handler,
		},
		{
			MethodName: "MakeToken",
			Handler:    _Epay_MakeToken_Handler,
		},
		{
			MethodName: "GetAcquirerIdentifier",
			Handler:    _Epay_GetAcquirerIdentifier_Handler,
		},
		{
			MethodName: "ResolveVisaAlias",
			Handler:    _Epay_ResolveVisaAlias_Handler,
		},
		{
			MethodName: "PayOutByPhone",
			Handler:    _Epay_PayOutByPhone_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "inner/processing/grpc/epay.proto",
}
