package service

import (
	"context"
	"crypto/aes"
	"errors"
	"testing"

	"github.com/golang/mock/gomock"
	"github.com/stretchr/testify/require"

	"git.local/sensitive/innerpb/processing/goerr"
	"git.local/sensitive/processing/acquirer/model"
	"git.local/sensitive/processing/acquirer/repository/database/databasemocks"
	"git.local/sensitive/sdk/dog/e2e"
)

func TestGetByParams(t *testing.T) {
	type getExactTerminalOp struct {
		inputProjectID, inputAcquirerID, inputTransactionTypeID uint64
		output                                                  model.Terminal
		outputErr                                               error
	}

	tests := []struct {
		name                                              string
		reqProjectID, reqAcquirerID, reqTransactionTypeID uint64
		want                                              model.Terminal
		wantErr                                           error
		getExactTerminal                                  getExactTerminalOp
		appConfig                                         map[string]any
	}{
		{
			name:                 "error when getting exact terminal",
			reqProjectID:         1,
			reqAcquirerID:        1,
			reqTransactionTypeID: 1,
			getExactTerminal: getExactTerminalOp{
				inputProjectID:         1,
				inputAcquirerID:        1,
				inputTransactionTypeID: 1,
				output:                 model.Terminal{},
				outputErr:              errors.New("some error"),
			},
			wantErr: errors.New("some error"),
			want:    model.Terminal{},
			appConfig: map[string]any{
				"SYMMETRIC_KEY": normalKey,
			},
		},
		{
			name:                 "bad key",
			reqProjectID:         1,
			reqAcquirerID:        1,
			reqTransactionTypeID: 1,
			getExactTerminal: getExactTerminalOp{
				inputProjectID:         1,
				inputAcquirerID:        1,
				inputTransactionTypeID: 1,
				output: model.Terminal{
					ID:                   22,
					AcquirerID:           1,
					Status:               model.TerminalOn,
					EncryptedConfig:      defaultEncryptedConfig,
					TwoStageTimeout:      10,
					AccountNumber:        "********************",
					IsTransit:            false,
					Description:          "some desc",
					AcquirerTerminalName: "test terminal",
				},
				outputErr: nil,
			},
			wantErr: aes.KeySizeError(3),
			want:    model.Terminal{},
			appConfig: map[string]any{
				"SYMMETRIC_KEY": badKey,
			},
		},
		{
			name:                 "success",
			reqProjectID:         1,
			reqAcquirerID:        1,
			reqTransactionTypeID: 1,
			getExactTerminal: getExactTerminalOp{
				inputProjectID:         1,
				inputAcquirerID:        1,
				inputTransactionTypeID: 1,
				output: model.Terminal{
					ID:                   22,
					AcquirerID:           1,
					Status:               model.TerminalOn,
					EncryptedConfig:      defaultEncryptedConfig,
					TwoStageTimeout:      10,
					AccountNumber:        "********************",
					IsTransit:            false,
					Description:          "some desc",
					AcquirerTerminalName: "test terminal",
				},
				outputErr: nil,
			},
			wantErr: nil,
			want: model.Terminal{
				ID:                   22,
				AcquirerID:           1,
				Status:               model.TerminalOn,
				EncryptedConfig:      "{\"my-name-is\":\"skrillex\"}",
				TwoStageTimeout:      10,
				AccountNumber:        "********************",
				IsTransit:            false,
				Description:          "some desc",
				AcquirerTerminalName: "test terminal",
			},
			appConfig: map[string]any{
				"SYMMETRIC_KEY": normalKey,
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctrl := gomock.NewController(t)
			defer ctrl.Finish()

			c := context.Background()
			ctx, cancel := context.WithCancel(c)
			e2e.InitAppE2E(t, ctx, cancel, nil, nil, tt.appConfig)

			terminalBalanceDBMock := databasemocks.NewMockTerminalBalancer(ctrl)

			terminalService := NewBasicTerminalService(terminalBalanceDBMock)

			terminalBalanceDBMock.EXPECT().GetExactTerminal(
				gomock.Any(),
				tt.getExactTerminal.inputProjectID,
				tt.getExactTerminal.inputTransactionTypeID,
				tt.getExactTerminal.inputAcquirerID,
			).Return(tt.getExactTerminal.output, tt.getExactTerminal.outputErr).Times(1)

			resp, err := terminalService.GetByParams(context.Background(), tt.reqProjectID, tt.reqAcquirerID, tt.reqTransactionTypeID)
			if tt.wantErr != nil {
				require.Error(t, err)
				require.Equal(t, tt.wantErr, err)
			} else {
				require.NoError(t, err)
			}

			require.Equal(t, tt.want, resp)

		})
	}
}

func TestGetTerminalsByID(t *testing.T) {
	type getTerminalByIDOp struct {
		input     []uint64
		output    []model.Terminal
		outputErr error
	}

	tests := []struct {
		name            string
		req             []uint64
		want            []model.Terminal
		wantErr         error
		getTerminalByID getTerminalByIDOp
	}{
		{
			name:    "error when getting terminal",
			req:     []uint64{22, 23},
			wantErr: goerr.ErrDbUnexpected,
			want:    nil,
			getTerminalByID: getTerminalByIDOp{
				input:     []uint64{22, 23},
				output:    nil,
				outputErr: goerr.ErrDbUnexpected,
			},
		},
		{
			name:    "0 terminals received",
			req:     []uint64{22, 23},
			wantErr: goerr.ErrTerminalNotFound,
			want:    nil,
			getTerminalByID: getTerminalByIDOp{
				input:     []uint64{22, 23},
				output:    nil,
				outputErr: nil,
			},
		},
		{
			name:    "success",
			req:     []uint64{22, 23},
			wantErr: nil,
			want: []model.Terminal{
				{
					ID:                   22,
					AcquirerID:           1,
					Status:               model.TerminalOn,
					EncryptedConfig:      defaultEncryptedConfig,
					TwoStageTimeout:      10,
					AccountNumber:        "********************",
					IsTransit:            false,
					Description:          "some desc",
					AcquirerTerminalName: "test terminal",
				},
				{
					ID:                   23,
					AcquirerID:           1,
					Status:               model.TerminalOff,
					EncryptedConfig:      defaultEncryptedConfig,
					TwoStageTimeout:      10,
					AccountNumber:        "********************",
					IsTransit:            false,
					Description:          "some desc desc",
					AcquirerTerminalName: "test test terminal",
				},
			},
			getTerminalByID: getTerminalByIDOp{
				input: []uint64{22, 23},
				output: []model.Terminal{
					{
						ID:                   22,
						AcquirerID:           1,
						Status:               model.TerminalOn,
						EncryptedConfig:      defaultEncryptedConfig,
						TwoStageTimeout:      10,
						AccountNumber:        "********************",
						IsTransit:            false,
						Description:          "some desc",
						AcquirerTerminalName: "test terminal",
					},
					{
						ID:                   23,
						AcquirerID:           1,
						Status:               model.TerminalOff,
						EncryptedConfig:      defaultEncryptedConfig,
						TwoStageTimeout:      10,
						AccountNumber:        "********************",
						IsTransit:            false,
						Description:          "some desc desc",
						AcquirerTerminalName: "test test terminal",
					},
				},
				outputErr: nil,
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctrl := gomock.NewController(t)
			defer ctrl.Finish()

			terminalBalanceDBMock := databasemocks.NewMockTerminalBalancer(ctrl)

			terminalService := NewBasicTerminalService(terminalBalanceDBMock)

			terminalBalanceDBMock.EXPECT().GetTerminalsByIDs(
				gomock.Any(),
				tt.getTerminalByID.input,
			).Return(tt.getTerminalByID.output, tt.getTerminalByID.outputErr).Times(1)

			resp, err := terminalService.GetTerminalsByIDs(context.Background(), tt.req)
			if tt.wantErr != nil {
				require.Error(t, err)
				require.ErrorIs(t, tt.wantErr, err)
			} else {
				require.NoError(t, err)
			}

			require.Equal(t, tt.want, resp)
		})
	}
}
