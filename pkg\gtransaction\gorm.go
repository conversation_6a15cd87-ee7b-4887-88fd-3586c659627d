package gtransaction

import (
	"context"
	"fmt"

	"gorm.io/gorm"
)

var _ Manager = (*GormTransactionManager)(nil)

type GormTransactionManager struct {
	db *gorm.DB
}

func NewGormTransactionManager(db *gorm.DB) *GormTransactionManager {
	return &GormTransactionManager{
		db: db,
	}
}

func (t *GormTransactionManager) Begin(ctx context.Context) context.Context {
	return setContext(ctx, t.db.Begin())
}

func (t *GormTransactionManager) Commit(ctx context.Context) error {
	db, ok := fromContext(ctx)
	if !ok {
		return ErrNoTransaction
	}

	return db.Commit().Error
}

func (t *GormTransactionManager) Rollback(ctx context.Context) error {
	db, ok := fromContext(ctx)
	if !ok {
		return ErrNoTransaction
	}

	return db.Rollback().Error
}

// Finish заканчивает транзакцию. Если в функции не произошло никаких ошибок - коммитит транзакцию,
// в ином случае роллбечит. Возвращает ошибки коммита/роллбека. ВАЖНО - для корректной работы
// необходимо правильно использовать в defer
//
// Пример использования:
//
//	 func (s *ExampleService) Example(ctx context.Context) (model *model.Example, err error) {  // именованный возврат err обязателен
//	 	ctx = s.transactions.Begin(ctx)
//	     defer func() {
//	         // В defer окажется текущая ошибка функции (или nil). Затем Finish, если
//	         // произошли ошибки коммита/роллбека, вернет обернутую ошибку. В итоге из функции
//	         // Example вернется именно эта ошибка за счет именованного возврата и defer
//	     	err = s.transactions.Finish(ctx, err)
//	     }()
//
//			model, err = s.repo.Example.Create(ctx, &model.Example{Text: "text"})
//	     if err != nil {
//	     	return nil, err
//	     }
//
//	     // some code...
//
//	     return model, nil
//	 }
func (t *GormTransactionManager) Finish(ctx context.Context, err error) error {
	if err != nil {
		if rollbackErr := t.Rollback(ctx); rollbackErr != nil {
			return fmt.Errorf("%s: %w", rollbackErr.Error(), err)
		}

		return err
	} else {
		if commitErr := t.Commit(ctx); commitErr != nil {
			return fmt.Errorf("failed to commit tx: %w", commitErr)
		}

		return nil
	}
}
