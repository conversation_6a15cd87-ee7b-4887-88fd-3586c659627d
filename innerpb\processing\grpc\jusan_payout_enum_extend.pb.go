// Code generated by protoc-gen-go-mvp. DO NOT EDIT.
// versions:
// - protoc-gen-go-mvp v2.0.1
// created by mvp authors

package grpc

import (
	math "math"
	strings "strings"
)

func (val JusanResponseCodePayout) Synonym() JusanResponseCodePayout {
	if _, ok := JusanResponseCodePayout_name[int32(val)]; ok {
		return val
	}

	return JusanResponseCodePayout(math.MinInt32)
}

func (val JusanResponseCodePayout) Int() int {
	return int(val.Synonym())
}

func (val JusanResponseCodePayout) IntPtr() *int {
	result := int(val.Synonym())
	return &result
}

func (val JusanResponseCodePayout) Int32() int32 {
	return int32(val.Synonym())
}

func (val JusanResponseCodePayout) Int32Ptr() *int32 {
	result := int32(val.Synonym())
	return &result
}

func (val JusanResponseCodePayout) Int64() int64 {
	return int64(val.Synonym())
}

func (val JusanResponseCodePayout) Int64Ptr() *int64 {
	result := int64(val.Synonym())
	return &result
}

func (val JusanResponseCodePayout) Uint() uint {
	return uint(val.Synonym())
}

func (val JusanResponseCodePayout) UintPtr() *uint {
	result := uint(val.Synonym())
	return &result
}

func (val JusanResponseCodePayout) Uint32() uint32 {
	return uint32(val.Synonym())
}

func (val JusanResponseCodePayout) Uint32Ptr() *uint32 {
	result := uint32(val.Synonym())
	return &result
}

func (val JusanResponseCodePayout) Uint64() uint64 {
	return uint64(val.Synonym())
}

func (val JusanResponseCodePayout) Uint64Ptr() *uint64 {
	result := uint64(val.Synonym())
	return &result
}

func (val JusanResponseCodePayout) IsKnown() bool {
	return val.Synonym() != JusanResponseCodePayout(math.MinInt32)
}

func ConvertIntToJusanResponseCodePayout(in int) JusanResponseCodePayout {
	return JusanResponseCodePayout(in).Synonym()
}

func ConvertUintToJusanResponseCodePayout(in uint) JusanResponseCodePayout {
	return JusanResponseCodePayout(in).Synonym()
}

func ConvertInt32ToJusanResponseCodePayout(in int32) JusanResponseCodePayout {
	return JusanResponseCodePayout(in).Synonym()
}

func ConvertUint32ToJusanResponseCodePayout(in uint32) JusanResponseCodePayout {
	return JusanResponseCodePayout(in).Synonym()
}

func ConvertInt64ToJusanResponseCodePayout(in int64) JusanResponseCodePayout {
	return JusanResponseCodePayout(in).Synonym()
}

func ConvertUint64ToJusanResponseCodePayout(in uint64) JusanResponseCodePayout {
	return JusanResponseCodePayout(in).Synonym()
}

var JusanResponseCodePayout_Lower_value = map[string]JusanResponseCodePayout{
	"-1":     0,
	"-2":     1,
	"-3":     2,
	"-4":     3,
	"-5":     4,
	"-100":   5,
	"-101":   6,
	"-102":   7,
	"-103":   8,
	"-104":   9,
	"-105":   10,
	"-106":   11,
	"-107":   12,
	"-108":   13,
	"-10000": 14,
	"-109":   15,
	"-110":   16,
	"-111":   17,
	"-112":   18,
	"-113":   19,
	"w05":    20,
	"w07":    21,
	"w12":    22,
	"w14":    23,
	"w17":    24,
	"w18":    25,
	"w19":    26,
	"w33":    27,
	"w36":    28,
	"w43":    29,
	"w54":    30,
	"w57":    31,
	"w58":    32,
	"w59":    33,
	"w61":    34,
	"w62":    35,
	"w63":    36,
	"w91":    37,
	"w93":    38,
	"w96":    39,
}

func ConvertStringToJusanResponseCodePayout(in string) JusanResponseCodePayout {
	if result, ok := JusanResponseCodePayout_value[in]; ok {
		return JusanResponseCodePayout(result)
	}

	if result, ok := JusanResponseCodePayout_Lower_value[strings.ToLower(in)]; ok {
		return JusanResponseCodePayout(result)
	}

	return JusanResponseCodePayout(math.MinInt32)
}

var SliceJusanResponseCodePayoutConvert *sliceJusanResponseCodePayoutConvert

type sliceJusanResponseCodePayoutConvert struct{}

func (*sliceJusanResponseCodePayoutConvert) Synonym(in []JusanResponseCodePayout) []JusanResponseCodePayout {
	result := make([]JusanResponseCodePayout, len(in))
	for i, v := range in {
		result[i] = v.Synonym()
	}

	return result
}

func (*sliceJusanResponseCodePayoutConvert) Int32(in []JusanResponseCodePayout) []int32 {
	result := make([]int32, len(in))
	for i, v := range in {
		result[i] = v.Int32()
	}

	return result
}

func (*sliceJusanResponseCodePayoutConvert) Uint32(in []JusanResponseCodePayout) []uint32 {
	result := make([]uint32, len(in))
	for i, v := range in {
		result[i] = v.Uint32()
	}

	return result
}

func (*sliceJusanResponseCodePayoutConvert) Uint64(in []JusanResponseCodePayout) []uint64 {
	result := make([]uint64, len(in))
	for i, v := range in {
		result[i] = v.Uint64()
	}

	return result
}

func (*sliceJusanResponseCodePayoutConvert) Int64(in []JusanResponseCodePayout) []int64 {
	result := make([]int64, len(in))
	for i, v := range in {
		result[i] = v.Int64()
	}

	return result
}

func (*sliceJusanResponseCodePayoutConvert) Uint(in []JusanResponseCodePayout) []uint {
	result := make([]uint, len(in))
	for i, v := range in {
		result[i] = v.Uint()
	}

	return result
}

func (*sliceJusanResponseCodePayoutConvert) Int(in []JusanResponseCodePayout) []int {
	result := make([]int, len(in))
	for i, v := range in {
		result[i] = v.Int()
	}

	return result
}

func (*sliceJusanResponseCodePayoutConvert) IsKnown(in []JusanResponseCodePayout) []bool {
	result := make([]bool, len(in))
	for i, v := range in {
		result[i] = v.IsKnown()
	}

	return result
}

func (*sliceJusanResponseCodePayoutConvert) ConvertIntToJusanResponseCodePayout(in []int) []JusanResponseCodePayout {
	result := make([]JusanResponseCodePayout, len(in))
	for i, v := range in {
		result[i] = ConvertIntToJusanResponseCodePayout(v)
	}

	return result
}

func (*sliceJusanResponseCodePayoutConvert) ConvertUintToJusanResponseCodePayout(in []uint) []JusanResponseCodePayout {
	result := make([]JusanResponseCodePayout, len(in))
	for i, v := range in {
		result[i] = ConvertUintToJusanResponseCodePayout(v)
	}

	return result
}

func (*sliceJusanResponseCodePayoutConvert) ConvertInt32ToJusanResponseCodePayout(in []int32) []JusanResponseCodePayout {
	result := make([]JusanResponseCodePayout, len(in))
	for i, v := range in {
		result[i] = ConvertInt32ToJusanResponseCodePayout(v)
	}

	return result
}

func (*sliceJusanResponseCodePayoutConvert) ConvertUint32ToJusanResponseCodePayout(in []uint32) []JusanResponseCodePayout {
	result := make([]JusanResponseCodePayout, len(in))
	for i, v := range in {
		result[i] = ConvertUint32ToJusanResponseCodePayout(v)
	}

	return result
}

func (*sliceJusanResponseCodePayoutConvert) ConvertInt64ToJusanResponseCodePayout(in []int64) []JusanResponseCodePayout {
	result := make([]JusanResponseCodePayout, len(in))
	for i, v := range in {
		result[i] = ConvertInt64ToJusanResponseCodePayout(v)
	}

	return result
}

func (*sliceJusanResponseCodePayoutConvert) ConvertUint64ToJusanResponseCodePayout(in []uint64) []JusanResponseCodePayout {
	result := make([]JusanResponseCodePayout, len(in))
	for i, v := range in {
		result[i] = ConvertUint64ToJusanResponseCodePayout(v)
	}

	return result
}

func (*sliceJusanResponseCodePayoutConvert) ConvertStringToJusanResponseCodePayout(in []string) []JusanResponseCodePayout {
	result := make([]JusanResponseCodePayout, len(in))
	for i, v := range in {
		result[i] = ConvertStringToJusanResponseCodePayout(v)
	}

	return result
}

func NewJusanResponseCodePayoutUsage() *JusanResponseCodePayoutUsage {
	return &JusanResponseCodePayoutUsage{
		enumMap: map[JusanResponseCodePayout]bool{
			JusanResponseCodePayout_PayoutUnhandledError:            false,
			JusanResponseCodePayout_PayoutValidationError:           false,
			JusanResponseCodePayout_PayoutIncorrectLoginPassword:    false,
			JusanResponseCodePayout_PayoutBlocked:                   false,
			JusanResponseCodePayout_PayoutRetry:                     false,
			JusanResponseCodePayout_PayoutEmptyPartnerCorrelationId: false,
			JusanResponseCodePayout_PayoutEmptyPartnerLogin:         false,
			JusanResponseCodePayout_PayoutEmptyAuthorization:        false,
			JusanResponseCodePayout_PayoutEmptyData:                 false,
			JusanResponseCodePayout_PayoutEmptyAuth:                 false,
			JusanResponseCodePayout_PayoutIncorrectAuth:             false,
			JusanResponseCodePayout_PayoutIncorrectCurrency:         false,
			JusanResponseCodePayout_PayoutEDNotFound:                false,
			JusanResponseCodePayout_PayoutEDNotEnabled:              false,
			JusanResponseCodePayout_PayoutInsufficientFunds:         false,
			JusanResponseCodePayout_PayoutIncorrectCard:             false,
			JusanResponseCodePayout_PayoutAccountBlocked:            false,
			JusanResponseCodePayout_PayoutIncorrectSession:          false,
			JusanResponseCodePayout_PayoutPaymentNotFound:           false,
			JusanResponseCodePayout_PayoutIncorrectAmount:           false,
			JusanResponseCodePayout_PayoutIssuerDeclined:            false,
			JusanResponseCodePayout_PayoutCardDisabled:              false,
			JusanResponseCodePayout_PayoutTransactionNotActive:      false,
			JusanResponseCodePayout_PayoutCardNumIncorrect:          false,
			JusanResponseCodePayout_PayoutClientDisabled:            false,
			JusanResponseCodePayout_PayoutSuspiciousClient:          false,
			JusanResponseCodePayout_PayoutRetryTransaction:          false,
			JusanResponseCodePayout_PayoutCardExpired:               false,
			JusanResponseCodePayout_PayoutCardLimited:               false,
			JusanResponseCodePayout_PayoutStolenCard:                false,
			JusanResponseCodePayout_PayoutExpCard:                   false,
			JusanResponseCodePayout_PayoutForbiddenForClient:        false,
			JusanResponseCodePayout_PayoutForbiddenTerminal:         false,
			JusanResponseCodePayout_PayoutClientSuspicious:          false,
			JusanResponseCodePayout_PayoutLimitExceeded:             false,
			JusanResponseCodePayout_PayoutLimitedCard:               false,
			JusanResponseCodePayout_PayoutSecurityBreached:          false,
			JusanResponseCodePayout_PayoutUnavailableIssuer:         false,
			JusanResponseCodePayout_PayoutTransactionNotFinished:    false,
			JusanResponseCodePayout_PayoutSystemMalfuction:          false,
		},
	}
}

func IsJusanResponseCodePayout(target JusanResponseCodePayout, matches ...JusanResponseCodePayout) bool {
	for _, v := range matches {
		if target == v {
			return true
		}
	}

	return false
}

type JusanResponseCodePayoutUsage struct {
	enumMap map[JusanResponseCodePayout]bool
}

func (u *JusanResponseCodePayoutUsage) Use(slice ...JusanResponseCodePayout) *JusanResponseCodePayoutUsage {
	for _, in := range slice {
		u.enumMap[in] = true
	}

	return u
}

func (u *JusanResponseCodePayoutUsage) Check() []string {
	var result []string
	for k, v := range u.enumMap {
		if v {
			continue
		}

		result = append(result, k.String())
	}

	return result
}

func (u *JusanResponseCodePayoutUsage) JusanResponseCodePayout_PayoutUnhandledError() JusanResponseCodePayout {
	u.Use(JusanResponseCodePayout_PayoutUnhandledError)
	return JusanResponseCodePayout_PayoutUnhandledError
}

func (u *JusanResponseCodePayoutUsage) JusanResponseCodePayout_PayoutValidationError() JusanResponseCodePayout {
	u.Use(JusanResponseCodePayout_PayoutValidationError)
	return JusanResponseCodePayout_PayoutValidationError
}

func (u *JusanResponseCodePayoutUsage) JusanResponseCodePayout_PayoutIncorrectLoginPassword() JusanResponseCodePayout {
	u.Use(JusanResponseCodePayout_PayoutIncorrectLoginPassword)
	return JusanResponseCodePayout_PayoutIncorrectLoginPassword
}

func (u *JusanResponseCodePayoutUsage) JusanResponseCodePayout_PayoutBlocked() JusanResponseCodePayout {
	u.Use(JusanResponseCodePayout_PayoutBlocked)
	return JusanResponseCodePayout_PayoutBlocked
}

func (u *JusanResponseCodePayoutUsage) JusanResponseCodePayout_PayoutRetry() JusanResponseCodePayout {
	u.Use(JusanResponseCodePayout_PayoutRetry)
	return JusanResponseCodePayout_PayoutRetry
}

func (u *JusanResponseCodePayoutUsage) JusanResponseCodePayout_PayoutEmptyPartnerCorrelationId() JusanResponseCodePayout {
	u.Use(JusanResponseCodePayout_PayoutEmptyPartnerCorrelationId)
	return JusanResponseCodePayout_PayoutEmptyPartnerCorrelationId
}

func (u *JusanResponseCodePayoutUsage) JusanResponseCodePayout_PayoutEmptyPartnerLogin() JusanResponseCodePayout {
	u.Use(JusanResponseCodePayout_PayoutEmptyPartnerLogin)
	return JusanResponseCodePayout_PayoutEmptyPartnerLogin
}

func (u *JusanResponseCodePayoutUsage) JusanResponseCodePayout_PayoutEmptyAuthorization() JusanResponseCodePayout {
	u.Use(JusanResponseCodePayout_PayoutEmptyAuthorization)
	return JusanResponseCodePayout_PayoutEmptyAuthorization
}

func (u *JusanResponseCodePayoutUsage) JusanResponseCodePayout_PayoutEmptyData() JusanResponseCodePayout {
	u.Use(JusanResponseCodePayout_PayoutEmptyData)
	return JusanResponseCodePayout_PayoutEmptyData
}

func (u *JusanResponseCodePayoutUsage) JusanResponseCodePayout_PayoutEmptyAuth() JusanResponseCodePayout {
	u.Use(JusanResponseCodePayout_PayoutEmptyAuth)
	return JusanResponseCodePayout_PayoutEmptyAuth
}

func (u *JusanResponseCodePayoutUsage) JusanResponseCodePayout_PayoutIncorrectAuth() JusanResponseCodePayout {
	u.Use(JusanResponseCodePayout_PayoutIncorrectAuth)
	return JusanResponseCodePayout_PayoutIncorrectAuth
}

func (u *JusanResponseCodePayoutUsage) JusanResponseCodePayout_PayoutIncorrectCurrency() JusanResponseCodePayout {
	u.Use(JusanResponseCodePayout_PayoutIncorrectCurrency)
	return JusanResponseCodePayout_PayoutIncorrectCurrency
}

func (u *JusanResponseCodePayoutUsage) JusanResponseCodePayout_PayoutEDNotFound() JusanResponseCodePayout {
	u.Use(JusanResponseCodePayout_PayoutEDNotFound)
	return JusanResponseCodePayout_PayoutEDNotFound
}

func (u *JusanResponseCodePayoutUsage) JusanResponseCodePayout_PayoutEDNotEnabled() JusanResponseCodePayout {
	u.Use(JusanResponseCodePayout_PayoutEDNotEnabled)
	return JusanResponseCodePayout_PayoutEDNotEnabled
}

func (u *JusanResponseCodePayoutUsage) JusanResponseCodePayout_PayoutInsufficientFunds() JusanResponseCodePayout {
	u.Use(JusanResponseCodePayout_PayoutInsufficientFunds)
	return JusanResponseCodePayout_PayoutInsufficientFunds
}

func (u *JusanResponseCodePayoutUsage) JusanResponseCodePayout_PayoutIncorrectCard() JusanResponseCodePayout {
	u.Use(JusanResponseCodePayout_PayoutIncorrectCard)
	return JusanResponseCodePayout_PayoutIncorrectCard
}

func (u *JusanResponseCodePayoutUsage) JusanResponseCodePayout_PayoutAccountBlocked() JusanResponseCodePayout {
	u.Use(JusanResponseCodePayout_PayoutAccountBlocked)
	return JusanResponseCodePayout_PayoutAccountBlocked
}

func (u *JusanResponseCodePayoutUsage) JusanResponseCodePayout_PayoutIncorrectSession() JusanResponseCodePayout {
	u.Use(JusanResponseCodePayout_PayoutIncorrectSession)
	return JusanResponseCodePayout_PayoutIncorrectSession
}

func (u *JusanResponseCodePayoutUsage) JusanResponseCodePayout_PayoutPaymentNotFound() JusanResponseCodePayout {
	u.Use(JusanResponseCodePayout_PayoutPaymentNotFound)
	return JusanResponseCodePayout_PayoutPaymentNotFound
}

func (u *JusanResponseCodePayoutUsage) JusanResponseCodePayout_PayoutIncorrectAmount() JusanResponseCodePayout {
	u.Use(JusanResponseCodePayout_PayoutIncorrectAmount)
	return JusanResponseCodePayout_PayoutIncorrectAmount
}

func (u *JusanResponseCodePayoutUsage) JusanResponseCodePayout_PayoutIssuerDeclined() JusanResponseCodePayout {
	u.Use(JusanResponseCodePayout_PayoutIssuerDeclined)
	return JusanResponseCodePayout_PayoutIssuerDeclined
}

func (u *JusanResponseCodePayoutUsage) JusanResponseCodePayout_PayoutCardDisabled() JusanResponseCodePayout {
	u.Use(JusanResponseCodePayout_PayoutCardDisabled)
	return JusanResponseCodePayout_PayoutCardDisabled
}

func (u *JusanResponseCodePayoutUsage) JusanResponseCodePayout_PayoutTransactionNotActive() JusanResponseCodePayout {
	u.Use(JusanResponseCodePayout_PayoutTransactionNotActive)
	return JusanResponseCodePayout_PayoutTransactionNotActive
}

func (u *JusanResponseCodePayoutUsage) JusanResponseCodePayout_PayoutCardNumIncorrect() JusanResponseCodePayout {
	u.Use(JusanResponseCodePayout_PayoutCardNumIncorrect)
	return JusanResponseCodePayout_PayoutCardNumIncorrect
}

func (u *JusanResponseCodePayoutUsage) JusanResponseCodePayout_PayoutClientDisabled() JusanResponseCodePayout {
	u.Use(JusanResponseCodePayout_PayoutClientDisabled)
	return JusanResponseCodePayout_PayoutClientDisabled
}

func (u *JusanResponseCodePayoutUsage) JusanResponseCodePayout_PayoutSuspiciousClient() JusanResponseCodePayout {
	u.Use(JusanResponseCodePayout_PayoutSuspiciousClient)
	return JusanResponseCodePayout_PayoutSuspiciousClient
}

func (u *JusanResponseCodePayoutUsage) JusanResponseCodePayout_PayoutRetryTransaction() JusanResponseCodePayout {
	u.Use(JusanResponseCodePayout_PayoutRetryTransaction)
	return JusanResponseCodePayout_PayoutRetryTransaction
}

func (u *JusanResponseCodePayoutUsage) JusanResponseCodePayout_PayoutCardExpired() JusanResponseCodePayout {
	u.Use(JusanResponseCodePayout_PayoutCardExpired)
	return JusanResponseCodePayout_PayoutCardExpired
}

func (u *JusanResponseCodePayoutUsage) JusanResponseCodePayout_PayoutCardLimited() JusanResponseCodePayout {
	u.Use(JusanResponseCodePayout_PayoutCardLimited)
	return JusanResponseCodePayout_PayoutCardLimited
}

func (u *JusanResponseCodePayoutUsage) JusanResponseCodePayout_PayoutStolenCard() JusanResponseCodePayout {
	u.Use(JusanResponseCodePayout_PayoutStolenCard)
	return JusanResponseCodePayout_PayoutStolenCard
}

func (u *JusanResponseCodePayoutUsage) JusanResponseCodePayout_PayoutExpCard() JusanResponseCodePayout {
	u.Use(JusanResponseCodePayout_PayoutExpCard)
	return JusanResponseCodePayout_PayoutExpCard
}

func (u *JusanResponseCodePayoutUsage) JusanResponseCodePayout_PayoutForbiddenForClient() JusanResponseCodePayout {
	u.Use(JusanResponseCodePayout_PayoutForbiddenForClient)
	return JusanResponseCodePayout_PayoutForbiddenForClient
}

func (u *JusanResponseCodePayoutUsage) JusanResponseCodePayout_PayoutForbiddenTerminal() JusanResponseCodePayout {
	u.Use(JusanResponseCodePayout_PayoutForbiddenTerminal)
	return JusanResponseCodePayout_PayoutForbiddenTerminal
}

func (u *JusanResponseCodePayoutUsage) JusanResponseCodePayout_PayoutClientSuspicious() JusanResponseCodePayout {
	u.Use(JusanResponseCodePayout_PayoutClientSuspicious)
	return JusanResponseCodePayout_PayoutClientSuspicious
}

func (u *JusanResponseCodePayoutUsage) JusanResponseCodePayout_PayoutLimitExceeded() JusanResponseCodePayout {
	u.Use(JusanResponseCodePayout_PayoutLimitExceeded)
	return JusanResponseCodePayout_PayoutLimitExceeded
}

func (u *JusanResponseCodePayoutUsage) JusanResponseCodePayout_PayoutLimitedCard() JusanResponseCodePayout {
	u.Use(JusanResponseCodePayout_PayoutLimitedCard)
	return JusanResponseCodePayout_PayoutLimitedCard
}

func (u *JusanResponseCodePayoutUsage) JusanResponseCodePayout_PayoutSecurityBreached() JusanResponseCodePayout {
	u.Use(JusanResponseCodePayout_PayoutSecurityBreached)
	return JusanResponseCodePayout_PayoutSecurityBreached
}

func (u *JusanResponseCodePayoutUsage) JusanResponseCodePayout_PayoutUnavailableIssuer() JusanResponseCodePayout {
	u.Use(JusanResponseCodePayout_PayoutUnavailableIssuer)
	return JusanResponseCodePayout_PayoutUnavailableIssuer
}

func (u *JusanResponseCodePayoutUsage) JusanResponseCodePayout_PayoutTransactionNotFinished() JusanResponseCodePayout {
	u.Use(JusanResponseCodePayout_PayoutTransactionNotFinished)
	return JusanResponseCodePayout_PayoutTransactionNotFinished
}

func (u *JusanResponseCodePayoutUsage) JusanResponseCodePayout_PayoutSystemMalfuction() JusanResponseCodePayout {
	u.Use(JusanResponseCodePayout_PayoutSystemMalfuction)
	return JusanResponseCodePayout_PayoutSystemMalfuction
}
