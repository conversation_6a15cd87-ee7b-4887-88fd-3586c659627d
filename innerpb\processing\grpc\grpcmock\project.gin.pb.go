// Code generated by MockGen. DO NOT EDIT.
// Source: project.gin.pb.go

// Package grpcmock is a generated GoMock package.
package grpcmock

import (
	reflect "reflect"

	gin "github.com/gin-gonic/gin"
	gomock "github.com/golang/mock/gomock"
)

// MockGinProjectServer is a mock of GinProjectServer interface.
type MockGinProjectServer struct {
	ctrl     *gomock.Controller
	recorder *MockGinProjectServerMockRecorder
}

// MockGinProjectServerMockRecorder is the mock recorder for MockGinProjectServer.
type MockGinProjectServerMockRecorder struct {
	mock *MockGinProjectServer
}

// NewMockGinProjectServer creates a new mock instance.
func NewMockGinProjectServer(ctrl *gomock.Controller) *MockGinProjectServer {
	mock := &MockGinProjectServer{ctrl: ctrl}
	mock.recorder = &MockGinProjectServerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockGinProjectServer) EXPECT() *MockGinProjectServerMockRecorder {
	return m.recorder
}

// GetProcessingProjectsByBUIDV1 mocks base method.
func (m *MockGinProjectServer) GetProcessingProjectsByBUIDV1(c *gin.Context) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetProcessingProjectsByBUIDV1", c)
	ret0, _ := ret[0].(error)
	return ret0
}

// GetProcessingProjectsByBUIDV1 indicates an expected call of GetProcessingProjectsByBUIDV1.
func (mr *MockGinProjectServerMockRecorder) GetProcessingProjectsByBUIDV1(c interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetProcessingProjectsByBUIDV1", reflect.TypeOf((*MockGinProjectServer)(nil).GetProcessingProjectsByBUIDV1), c)
}

// GetProjectsByMerchantID mocks base method.
func (m *MockGinProjectServer) GetProjectsByMerchantID(c *gin.Context) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetProjectsByMerchantID", c)
	ret0, _ := ret[0].(error)
	return ret0
}

// GetProjectsByMerchantID indicates an expected call of GetProjectsByMerchantID.
func (mr *MockGinProjectServerMockRecorder) GetProjectsByMerchantID(c interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetProjectsByMerchantID", reflect.TypeOf((*MockGinProjectServer)(nil).GetProjectsByMerchantID), c)
}

// IsSendEmail mocks base method.
func (m *MockGinProjectServer) IsSendEmail(c *gin.Context) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "IsSendEmail", c)
	ret0, _ := ret[0].(error)
	return ret0
}

// IsSendEmail indicates an expected call of IsSendEmail.
func (mr *MockGinProjectServerMockRecorder) IsSendEmail(c interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "IsSendEmail", reflect.TypeOf((*MockGinProjectServer)(nil).IsSendEmail), c)
}
