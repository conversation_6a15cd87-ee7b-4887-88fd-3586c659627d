// Code generated by protoc-gen-go-mvp. DO NOT EDIT.
// versions:
// - protoc-gen-go-mvp v2.0.1
// created by mvp authors

package grpc

import (
	context "context"
	grpc "google.golang.org/grpc"
	metadata "google.golang.org/grpc/metadata"
)

func NewIamMultiorderingServer(
	srv MultiorderingServer,
) MultiorderingServer {
	return &iamMultiorderingServer{
		srv: srv,
	}
}

var _ MultiorderingServer = (*iamMultiorderingServer)(nil)

type iamMultiorderingServer struct {
	UnimplementedMultiorderingServer

	srv MultiorderingServer
}

func (s *iamMultiorderingServer) GetPaymentOrder(
	ctx context.Context,
	req *GetPaymentOrderRequest,
) (
	*GetPaymentOrderResponse,
	error,
) {
	return s.srv.GetPaymentOrder(ctx, req)
}

func (s *iamMultiorderingServer) GetOrderingIdentifier(
	ctx context.Context,
	req *GetOrderingIdentifierRequest,
) (
	*GetOrderingIdentifierResponse,
	error,
) {
	return s.srv.GetOrderingIdentifier(ctx, req)
}

func NewIamMultiorderingClient(
	client MultiorderingClient,
) MultiorderingClient {
	return &iamMultiorderingClient{
		client: client,
	}
}

type iamMultiorderingClient struct {
	client MultiorderingClient
}

func (s *iamMultiorderingClient) GetPaymentOrder(
	ctx context.Context,
	req *GetPaymentOrderRequest,
	opts ...grpc.CallOption,
) (
	*GetPaymentOrderResponse,
	error,
) {
	md, _ := metadata.FromIncomingContext(ctx)
	return s.client.GetPaymentOrder(metadata.NewOutgoingContext(ctx, md), req)
}

func (s *iamMultiorderingClient) GetOrderingIdentifier(
	ctx context.Context,
	req *GetOrderingIdentifierRequest,
	opts ...grpc.CallOption,
) (
	*GetOrderingIdentifierResponse,
	error,
) {
	md, _ := metadata.FromIncomingContext(ctx)
	return s.client.GetOrderingIdentifier(metadata.NewOutgoingContext(ctx, md), req)
}
