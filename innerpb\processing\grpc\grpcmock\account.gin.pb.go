// Code generated by MockGen. DO NOT EDIT.
// Source: account.gin.pb.go

// Package grpcmock is a generated GoMock package.
package grpcmock

import (
	reflect "reflect"

	gin "github.com/gin-gonic/gin"
	gomock "github.com/golang/mock/gomock"
)

// MockGinAccountServer is a mock of GinAccountServer interface.
type MockGinAccountServer struct {
	ctrl     *gomock.Controller
	recorder *MockGinAccountServerMockRecorder
}

// MockGinAccountServerMockRecorder is the mock recorder for MockGinAccountServer.
type MockGinAccountServerMockRecorder struct {
	mock *MockGinAccountServer
}

// NewMockGinAccountServer creates a new mock instance.
func NewMockGinAccountServer(ctrl *gomock.Controller) *MockGinAccountServer {
	mock := &MockGinAccountServer{ctrl: ctrl}
	mock.recorder = &MockGinAccountServerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockGinAccountServer) EXPECT() *MockGinAccountServerMockRecorder {
	return m.recorder
}

// CreateTransfer mocks base method.
func (m *MockGinAccountServer) CreateTransfer(c *gin.Context) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateTransfer", c)
	ret0, _ := ret[0].(error)
	return ret0
}

// CreateTransfer indicates an expected call of CreateTransfer.
func (mr *MockGinAccountServerMockRecorder) CreateTransfer(c interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateTransfer", reflect.TypeOf((*MockGinAccountServer)(nil).CreateTransfer), c)
}

// ExtractFailOrCanceledTransfersByForeignIDs mocks base method.
func (m *MockGinAccountServer) ExtractFailOrCanceledTransfersByForeignIDs(c *gin.Context) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ExtractFailOrCanceledTransfersByForeignIDs", c)
	ret0, _ := ret[0].(error)
	return ret0
}

// ExtractFailOrCanceledTransfersByForeignIDs indicates an expected call of ExtractFailOrCanceledTransfersByForeignIDs.
func (mr *MockGinAccountServerMockRecorder) ExtractFailOrCanceledTransfersByForeignIDs(c interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ExtractFailOrCanceledTransfersByForeignIDs", reflect.TypeOf((*MockGinAccountServer)(nil).ExtractFailOrCanceledTransfersByForeignIDs), c)
}

// ExtractSuccessTransfersByForeignIDs mocks base method.
func (m *MockGinAccountServer) ExtractSuccessTransfersByForeignIDs(c *gin.Context) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ExtractSuccessTransfersByForeignIDs", c)
	ret0, _ := ret[0].(error)
	return ret0
}

// ExtractSuccessTransfersByForeignIDs indicates an expected call of ExtractSuccessTransfersByForeignIDs.
func (mr *MockGinAccountServerMockRecorder) ExtractSuccessTransfersByForeignIDs(c interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ExtractSuccessTransfersByForeignIDs", reflect.TypeOf((*MockGinAccountServer)(nil).ExtractSuccessTransfersByForeignIDs), c)
}

// GetAccountByID mocks base method.
func (m *MockGinAccountServer) GetAccountByID(c *gin.Context) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAccountByID", c)
	ret0, _ := ret[0].(error)
	return ret0
}

// GetAccountByID indicates an expected call of GetAccountByID.
func (mr *MockGinAccountServerMockRecorder) GetAccountByID(c interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAccountByID", reflect.TypeOf((*MockGinAccountServer)(nil).GetAccountByID), c)
}

// GetAccountByNumber mocks base method.
func (m *MockGinAccountServer) GetAccountByNumber(c *gin.Context) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAccountByNumber", c)
	ret0, _ := ret[0].(error)
	return ret0
}

// GetAccountByNumber indicates an expected call of GetAccountByNumber.
func (mr *MockGinAccountServerMockRecorder) GetAccountByNumber(c interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAccountByNumber", reflect.TypeOf((*MockGinAccountServer)(nil).GetAccountByNumber), c)
}

// GetSuccessTransferStatus mocks base method.
func (m *MockGinAccountServer) GetSuccessTransferStatus(c *gin.Context) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetSuccessTransferStatus", c)
	ret0, _ := ret[0].(error)
	return ret0
}

// GetSuccessTransferStatus indicates an expected call of GetSuccessTransferStatus.
func (mr *MockGinAccountServerMockRecorder) GetSuccessTransferStatus(c interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetSuccessTransferStatus", reflect.TypeOf((*MockGinAccountServer)(nil).GetSuccessTransferStatus), c)
}

// GetTransferByForeignID mocks base method.
func (m *MockGinAccountServer) GetTransferByForeignID(c *gin.Context) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetTransferByForeignID", c)
	ret0, _ := ret[0].(error)
	return ret0
}

// GetTransferByForeignID indicates an expected call of GetTransferByForeignID.
func (mr *MockGinAccountServerMockRecorder) GetTransferByForeignID(c interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetTransferByForeignID", reflect.TypeOf((*MockGinAccountServer)(nil).GetTransferByForeignID), c)
}

// GetTransferByIDV1 mocks base method.
func (m *MockGinAccountServer) GetTransferByIDV1(c *gin.Context) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetTransferByIDV1", c)
	ret0, _ := ret[0].(error)
	return ret0
}

// GetTransferByIDV1 indicates an expected call of GetTransferByIDV1.
func (mr *MockGinAccountServerMockRecorder) GetTransferByIDV1(c interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetTransferByIDV1", reflect.TypeOf((*MockGinAccountServer)(nil).GetTransferByIDV1), c)
}

// StartFinalizingOldTransfersWorker mocks base method.
func (m *MockGinAccountServer) StartFinalizingOldTransfersWorker(c *gin.Context) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "StartFinalizingOldTransfersWorker", c)
	ret0, _ := ret[0].(error)
	return ret0
}

// StartFinalizingOldTransfersWorker indicates an expected call of StartFinalizingOldTransfersWorker.
func (mr *MockGinAccountServerMockRecorder) StartFinalizingOldTransfersWorker(c interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "StartFinalizingOldTransfersWorker", reflect.TypeOf((*MockGinAccountServer)(nil).StartFinalizingOldTransfersWorker), c)
}

// StartFinalizingOutTransfersWorker mocks base method.
func (m *MockGinAccountServer) StartFinalizingOutTransfersWorker(c *gin.Context) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "StartFinalizingOutTransfersWorker", c)
	ret0, _ := ret[0].(error)
	return ret0
}

// StartFinalizingOutTransfersWorker indicates an expected call of StartFinalizingOutTransfersWorker.
func (mr *MockGinAccountServerMockRecorder) StartFinalizingOutTransfersWorker(c interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "StartFinalizingOutTransfersWorker", reflect.TypeOf((*MockGinAccountServer)(nil).StartFinalizingOutTransfersWorker), c)
}

// StartParsingIncomingTransfersWorker mocks base method.
func (m *MockGinAccountServer) StartParsingIncomingTransfersWorker(c *gin.Context) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "StartParsingIncomingTransfersWorker", c)
	ret0, _ := ret[0].(error)
	return ret0
}

// StartParsingIncomingTransfersWorker indicates an expected call of StartParsingIncomingTransfersWorker.
func (mr *MockGinAccountServerMockRecorder) StartParsingIncomingTransfersWorker(c interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "StartParsingIncomingTransfersWorker", reflect.TypeOf((*MockGinAccountServer)(nil).StartParsingIncomingTransfersWorker), c)
}

// StartProcessingAccountStatementWorker mocks base method.
func (m *MockGinAccountServer) StartProcessingAccountStatementWorker(c *gin.Context) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "StartProcessingAccountStatementWorker", c)
	ret0, _ := ret[0].(error)
	return ret0
}

// StartProcessingAccountStatementWorker indicates an expected call of StartProcessingAccountStatementWorker.
func (mr *MockGinAccountServerMockRecorder) StartProcessingAccountStatementWorker(c interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "StartProcessingAccountStatementWorker", reflect.TypeOf((*MockGinAccountServer)(nil).StartProcessingAccountStatementWorker), c)
}
