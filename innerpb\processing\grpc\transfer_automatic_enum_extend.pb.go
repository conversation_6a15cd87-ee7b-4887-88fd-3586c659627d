// Code generated by protoc-gen-go-mvp. DO NOT EDIT.
// versions:
// - protoc-gen-go-mvp v2.0.1
// created by mvp authors

package grpc

import (
	math "math"
	strings "strings"
)

func (val EnumFrequencyType) Synonym() EnumFrequencyType {
	if _, ok := EnumFrequencyType_name[int32(val)]; ok {
		return val
	}

	return EnumFrequencyType(math.MinInt32)
}

func (val EnumFrequencyType) Int() int {
	return int(val.Synonym())
}

func (val EnumFrequencyType) IntPtr() *int {
	result := int(val.Synonym())
	return &result
}

func (val EnumFrequencyType) Int32() int32 {
	return int32(val.Synonym())
}

func (val EnumFrequencyType) Int32Ptr() *int32 {
	result := int32(val.Synonym())
	return &result
}

func (val EnumFrequencyType) Int64() int64 {
	return int64(val.Synonym())
}

func (val EnumFrequencyType) Int64Ptr() *int64 {
	result := int64(val.Synonym())
	return &result
}

func (val EnumFrequencyType) Uint() uint {
	return uint(val.Synonym())
}

func (val EnumFrequencyType) UintPtr() *uint {
	result := uint(val.Synonym())
	return &result
}

func (val EnumFrequencyType) Uint32() uint32 {
	return uint32(val.Synonym())
}

func (val EnumFrequencyType) Uint32Ptr() *uint32 {
	result := uint32(val.Synonym())
	return &result
}

func (val EnumFrequencyType) Uint64() uint64 {
	return uint64(val.Synonym())
}

func (val EnumFrequencyType) Uint64Ptr() *uint64 {
	result := uint64(val.Synonym())
	return &result
}

func (val EnumFrequencyType) IsKnown() bool {
	return val.Synonym() != EnumFrequencyType(math.MinInt32)
}

func ConvertIntToEnumFrequencyType(in int) EnumFrequencyType {
	return EnumFrequencyType(in).Synonym()
}

func ConvertUintToEnumFrequencyType(in uint) EnumFrequencyType {
	return EnumFrequencyType(in).Synonym()
}

func ConvertInt32ToEnumFrequencyType(in int32) EnumFrequencyType {
	return EnumFrequencyType(in).Synonym()
}

func ConvertUint32ToEnumFrequencyType(in uint32) EnumFrequencyType {
	return EnumFrequencyType(in).Synonym()
}

func ConvertInt64ToEnumFrequencyType(in int64) EnumFrequencyType {
	return EnumFrequencyType(in).Synonym()
}

func ConvertUint64ToEnumFrequencyType(in uint64) EnumFrequencyType {
	return EnumFrequencyType(in).Synonym()
}

var EnumFrequencyType_Lower_value = map[string]EnumFrequencyType{
	"day":   0,
	"week":  1,
	"month": 2,
}

func ConvertStringToEnumFrequencyType(in string) EnumFrequencyType {
	if result, ok := EnumFrequencyType_value[in]; ok {
		return EnumFrequencyType(result)
	}

	if result, ok := EnumFrequencyType_Lower_value[strings.ToLower(in)]; ok {
		return EnumFrequencyType(result)
	}

	return EnumFrequencyType(math.MinInt32)
}

var SliceEnumFrequencyTypeConvert *sliceEnumFrequencyTypeConvert

type sliceEnumFrequencyTypeConvert struct{}

func (*sliceEnumFrequencyTypeConvert) Synonym(in []EnumFrequencyType) []EnumFrequencyType {
	result := make([]EnumFrequencyType, len(in))
	for i, v := range in {
		result[i] = v.Synonym()
	}

	return result
}

func (*sliceEnumFrequencyTypeConvert) Int32(in []EnumFrequencyType) []int32 {
	result := make([]int32, len(in))
	for i, v := range in {
		result[i] = v.Int32()
	}

	return result
}

func (*sliceEnumFrequencyTypeConvert) Uint32(in []EnumFrequencyType) []uint32 {
	result := make([]uint32, len(in))
	for i, v := range in {
		result[i] = v.Uint32()
	}

	return result
}

func (*sliceEnumFrequencyTypeConvert) Uint64(in []EnumFrequencyType) []uint64 {
	result := make([]uint64, len(in))
	for i, v := range in {
		result[i] = v.Uint64()
	}

	return result
}

func (*sliceEnumFrequencyTypeConvert) Int64(in []EnumFrequencyType) []int64 {
	result := make([]int64, len(in))
	for i, v := range in {
		result[i] = v.Int64()
	}

	return result
}

func (*sliceEnumFrequencyTypeConvert) Uint(in []EnumFrequencyType) []uint {
	result := make([]uint, len(in))
	for i, v := range in {
		result[i] = v.Uint()
	}

	return result
}

func (*sliceEnumFrequencyTypeConvert) Int(in []EnumFrequencyType) []int {
	result := make([]int, len(in))
	for i, v := range in {
		result[i] = v.Int()
	}

	return result
}

func (*sliceEnumFrequencyTypeConvert) IsKnown(in []EnumFrequencyType) []bool {
	result := make([]bool, len(in))
	for i, v := range in {
		result[i] = v.IsKnown()
	}

	return result
}

func (*sliceEnumFrequencyTypeConvert) ConvertIntToEnumFrequencyType(in []int) []EnumFrequencyType {
	result := make([]EnumFrequencyType, len(in))
	for i, v := range in {
		result[i] = ConvertIntToEnumFrequencyType(v)
	}

	return result
}

func (*sliceEnumFrequencyTypeConvert) ConvertUintToEnumFrequencyType(in []uint) []EnumFrequencyType {
	result := make([]EnumFrequencyType, len(in))
	for i, v := range in {
		result[i] = ConvertUintToEnumFrequencyType(v)
	}

	return result
}

func (*sliceEnumFrequencyTypeConvert) ConvertInt32ToEnumFrequencyType(in []int32) []EnumFrequencyType {
	result := make([]EnumFrequencyType, len(in))
	for i, v := range in {
		result[i] = ConvertInt32ToEnumFrequencyType(v)
	}

	return result
}

func (*sliceEnumFrequencyTypeConvert) ConvertUint32ToEnumFrequencyType(in []uint32) []EnumFrequencyType {
	result := make([]EnumFrequencyType, len(in))
	for i, v := range in {
		result[i] = ConvertUint32ToEnumFrequencyType(v)
	}

	return result
}

func (*sliceEnumFrequencyTypeConvert) ConvertInt64ToEnumFrequencyType(in []int64) []EnumFrequencyType {
	result := make([]EnumFrequencyType, len(in))
	for i, v := range in {
		result[i] = ConvertInt64ToEnumFrequencyType(v)
	}

	return result
}

func (*sliceEnumFrequencyTypeConvert) ConvertUint64ToEnumFrequencyType(in []uint64) []EnumFrequencyType {
	result := make([]EnumFrequencyType, len(in))
	for i, v := range in {
		result[i] = ConvertUint64ToEnumFrequencyType(v)
	}

	return result
}

func (*sliceEnumFrequencyTypeConvert) ConvertStringToEnumFrequencyType(in []string) []EnumFrequencyType {
	result := make([]EnumFrequencyType, len(in))
	for i, v := range in {
		result[i] = ConvertStringToEnumFrequencyType(v)
	}

	return result
}

func NewEnumFrequencyTypeUsage() *EnumFrequencyTypeUsage {
	return &EnumFrequencyTypeUsage{
		enumMap: map[EnumFrequencyType]bool{
			EnumFrequencyType_PerDay:   false,
			EnumFrequencyType_PerWeek:  false,
			EnumFrequencyType_PerMonth: false,
		},
	}
}

func IsEnumFrequencyType(target EnumFrequencyType, matches ...EnumFrequencyType) bool {
	for _, v := range matches {
		if target == v {
			return true
		}
	}

	return false
}

type EnumFrequencyTypeUsage struct {
	enumMap map[EnumFrequencyType]bool
}

func (u *EnumFrequencyTypeUsage) Use(slice ...EnumFrequencyType) *EnumFrequencyTypeUsage {
	for _, in := range slice {
		u.enumMap[in] = true
	}

	return u
}

func (u *EnumFrequencyTypeUsage) Check() []string {
	var result []string
	for k, v := range u.enumMap {
		if v {
			continue
		}

		result = append(result, k.String())
	}

	return result
}

func (u *EnumFrequencyTypeUsage) EnumFrequencyType_PerDay() EnumFrequencyType {
	u.Use(EnumFrequencyType_PerDay)
	return EnumFrequencyType_PerDay
}

func (u *EnumFrequencyTypeUsage) EnumFrequencyType_PerWeek() EnumFrequencyType {
	u.Use(EnumFrequencyType_PerWeek)
	return EnumFrequencyType_PerWeek
}

func (u *EnumFrequencyTypeUsage) EnumFrequencyType_PerMonth() EnumFrequencyType {
	u.Use(EnumFrequencyType_PerMonth)
	return EnumFrequencyType_PerMonth
}
