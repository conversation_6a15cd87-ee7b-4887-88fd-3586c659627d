// Code generated by protoc-gen-go-mvp. DO NOT EDIT.
// versions:
// - protoc-gen-go-mvp v2.0.1
// created by mvp authors

package grpc

func (x EnumTransactionType) Code() string {
	switch x {
	case EnumTransactionType_Unknown:
		return "Unknown"
	case EnumTransactionType_TransactionTypePayIn:
		return "in"
	case EnumTransactionType_TransactionTypePayOut:
		return "out"
	case EnumTransactionType_TransactionTypeOneClickPayIn:
		return "one_click_pay_in"
	case EnumTransactionType_TransactionTypeOneClickPayOut:
		return "one_click_pay_out"
	case EnumTransactionType_TransactionTypeApplePay:
		return "apple_pay"
	case EnumTransactionType_TransactionTypeCardLink:
		return "card_link"
	case EnumTransactionType_TransactionTypeRefund:
		return "refund"
	case EnumTransactionType_TransactionTypeGooglePay:
		return "google_pay"
	case EnumTransactionType_TransactionTypeTwoStagePayIn:
		return "two_stage_pay_in"
	default:
		return "Unknown"
	}
}

func (x EnumTransactionType) Name() string {
	switch x {
	case EnumTransactionType_Unknown:
		return "Unknown"
	case EnumTransactionType_TransactionTypePayIn:
		return "Приём"
	case EnumTransactionType_TransactionTypePayOut:
		return "Вывод"
	case EnumTransactionType_TransactionTypeOneClickPayIn:
		return "Рекуррентный приём"
	case EnumTransactionType_TransactionTypeOneClickPayOut:
		return "Рекуррентный вывод"
	case EnumTransactionType_TransactionTypeApplePay:
		return "Apple Pay"
	case EnumTransactionType_TransactionTypeCardLink:
		return "Привязка карты"
	case EnumTransactionType_TransactionTypeRefund:
		return "Возврат"
	case EnumTransactionType_TransactionTypeGooglePay:
		return "Гугл Пэй"
	case EnumTransactionType_TransactionTypeTwoStagePayIn:
		return "Двустадийный прием"
	default:
		return "Unknown"
	}
}

// Created reference to EnumTransactionType

//	|	EnumTransactionType                              	|	Code               	|	Name                                 	|
//	|	EnumTransactionType_Unknown                      	|	"Unknown"          	|	"Unknown"                            	|
//	|	EnumTransactionType_TransactionTypePayIn         	|	"in"               	|	"Приём"                              	|
//	|	EnumTransactionType_TransactionTypePayOut        	|	"out"              	|	"Вывод"                              	|
//	|	EnumTransactionType_TransactionTypeOneClickPayIn 	|	"one_click_pay_in" 	|	"Рекуррентный приём"                 	|
//	|	EnumTransactionType_TransactionTypeOneClickPayOut	|	"one_click_pay_out"	|	"Рекуррентный вывод"                 	|
//	|	EnumTransactionType_TransactionTypeApplePay      	|	"apple_pay"        	|	"Apple Pay"                          	|
//	|	EnumTransactionType_TransactionTypeCardLink      	|	"card_link"        	|	"Привязка карты"                     	|
//	|	EnumTransactionType_TransactionTypeRefund        	|	"refund"           	|	"Возврат"                            	|
//	|	EnumTransactionType_TransactionTypeGooglePay     	|	"google_pay"       	|	"Гугл Пэй"                           	|
//	|	EnumTransactionType_TransactionTypeTwoStagePayIn 	|	"two_stage_pay_in" 	|	"Двустадийный прием"                 	|

var SliceEnumTransactionTypeRefs *sliceEnumTransactionTypeRefs

type sliceEnumTransactionTypeRefs struct{}

func (*sliceEnumTransactionTypeRefs) Code(slice ...EnumTransactionType) []string {
	var result []string
	for _, val := range slice {
		result = append(result, val.Code())
	}

	return result
}

func (*sliceEnumTransactionTypeRefs) Name(slice ...EnumTransactionType) []string {
	var result []string
	for _, val := range slice {
		result = append(result, val.Name())
	}

	return result
}
