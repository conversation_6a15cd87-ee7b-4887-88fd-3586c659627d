edition = "2023";

package processing.cobra.transaction;

option go_package = "git.local/sensitive/innerpb/processing/cobra";

import "mvp/proto/cobra.proto";
import "google/protobuf/descriptor.proto";

option (mvp.cobra_root) = true;

service CardService {
  option (mvp.cobra_config) = {
    Components: [GormInit, GinEngineInit, NatsInit, JetStreamContext, Swagger, Stand],
    ServiceNames: ["card", "card", "Card"],
    package: "git.local/sensitive/processing/card",
    func_name: "CardService"

    grpcHandler: [
      {
        name: "Card"
        layers: [
          {
            type: ServiceLayer
            name: "ClientService"
            layers: [
              { type: RepoLayer, name: "CardsDb", components: [GormInit], package_path: "/database" },
              { type: RepoLayer, name: "ClientsDb", components: [GormInit], package_path: "/database" },
              { type: RepoLayer, name: "CardGetterDB", components: [GormInit], package_path: "/database" },
              { type: RepoLayer, name: "ProjectMaskFormatDB", components: [GormInit], package_path: "/database" },
              { type: RepoLayer, name: "KeyDB", components: [GormInit], package_path: "/database" },
              { type: RepoLayer, name: "CardUpdatorDB", components: [GormInit], package_path: "/database" },
              { type: RepoLayer, name: "CardValidityDb", components: [GormInit], package_path: "/database" },
              {type: GrpcClient, name: "Merchant"},
              {type: GrpcClient, name: "TerminalCards"}
            ]
          },
          {
            type: ServiceLayer
            name: "ProjectMaskFormatService"
            layers: [
              {type: RepoLayer, name: "ProjectMaskFormatDB", components: [GormInit], package_path: "/database"},
              {type: GrpcClient, name: "TerminalCards"}
            ]
          },
          {
            type: ServiceLayer
            name: "CardGetterService"
            layers: [
              { type: RepoLayer, name: "CardsDb", components: [GormInit], package_path: "/database" },
              { type: RepoLayer, name: "ClientsDb", components: [GormInit], package_path: "/database" },
              { type: RepoLayer, name: "KeyDB", components: [GormInit], package_path: "/database" },
              { type: RepoLayer, name: "ProjectMaskFormatDB", components: [GormInit], package_path: "/database" },
              {type: GrpcClient, name: "Acquirer"}
            ]
          },
          {
            type: ServiceLayer
            name: "OneClickCardService"
            layers: [
              { type: RepoLayer, name: "ClientsDb", components: [GormInit], package_path: "/database" },
              { type: RepoLayer, name: "CardGetterDB", components: [GormInit], package_path: "/database" },
              { type: RepoLayer, name: "ProjectMaskFormatDB", components: [GormInit], package_path: "/database" },
              { type: RepoLayer, name: "KeyDB", components: [GormInit], package_path: "/database" },
              {type: GrpcClient, name: "Terminal"},
              {type: GrpcClient, name: "Acquirer"}
            ]
          },
          {
            type: ServiceLayer
            name: "ClientVerificationManagerService"
            layers: [
              { type: RepoLayer, name: "ClientVerificationManagerDB", components: [GormInit], package_path: "/database" },
              {type: GrpcClient, name: "Merchant"}
            ]
          },
          {
            type: ServiceLayer
            name: "CardEncryptManagerService"
          }
        ]
      }
    ]

    restHandler: [
      {
        vault_configs: [
          { key: "PUBLIC_KEY", type: String }
        ]
        name: "Card"
        layers: [
          {
            type: ServiceLayer
            name: "ClientService"
            layers: [
              { type: RepoLayer, name: "CardsDb", components: [GormInit], package_path: "/database" },
              { type: RepoLayer, name: "ClientsDb", components: [GormInit], package_path: "/database" },
              { type: RepoLayer, name: "CardGetterDB", components: [GormInit], package_path: "/database" },
              { type: RepoLayer, name: "ProjectMaskFormatDB", components: [GormInit], package_path: "/database" },
              { type: RepoLayer, name: "KeyDB", components: [GormInit], package_path: "/database" },
              { type: RepoLayer, name: "CardUpdatorDB", components: [GormInit], package_path: "/database" },
              { type: RepoLayer, name: "CardValidityDb", components: [GormInit], package_path: "/database" },
              {type: GrpcClient, name: "Merchant"},
              {type: GrpcClient, name: "TerminalCards"}
            ]
          },
          {
            type: ServiceLayer
            name: "ProjectMaskFormatService"
            layers: [
              {type: RepoLayer, name: "ProjectMaskFormatDB", components: [GormInit], package_path: "/database"},
              {type: GrpcClient, name: "TerminalCards"}
            ]
          },
          {
            type: ServiceLayer
            name: "CardGetterService"
            layers: [
              { type: RepoLayer, name: "CardsDb", components: [GormInit], package_path: "/database" },
              { type: RepoLayer, name: "ClientsDb", components: [GormInit], package_path: "/database" },
              { type: RepoLayer, name: "KeyDB", components: [GormInit], package_path: "/database" },
              { type: RepoLayer, name: "ProjectMaskFormatDB", components: [GormInit], package_path: "/database" },
              {type: GrpcClient, name: "Acquirer"}
            ]
          },
          {
            type: ServiceLayer
            name: "OneClickCardService"
            layers: [
              { type: RepoLayer, name: "ClientsDb", components: [GormInit], package_path: "/database" },
              { type: RepoLayer, name: "CardsDb", components: [GormInit], package_path: "/database" },
              { type: RepoLayer, name: "ProjectMaskFormatDB", components: [GormInit], package_path: "/database" },
              { type: RepoLayer, name: "KeyDB", components: [GormInit], package_path: "/database" },
              {type: GrpcClient, name: "Terminal"},
              {type: GrpcClient, name: "Acquirer"}
            ]
          },
          {
            type: ServiceLayer
            name: "ClientVerificationManagerService"
            layers: [
              { type: RepoLayer, name: "ClientVerificationManagerDB", components: [GormInit], package_path: "/database" },
              {type: GrpcClient, name: "Merchant"}
            ]
          },
          {
            type: ServiceLayer
            name: "CardEncryptManagerService"
          }
        ]
      }
    ]

    natsConsumer: [
      {
        name: "Card"
        layers: [
          {
            type: ServiceLayer
            name: "ClientService"
            layers: [
              { type: RepoLayer, name: "CardsDb", components: [GormInit], package_path: "/database" },
              { type: RepoLayer, name: "ClientsDb", components: [GormInit], package_path: "/database" },
              { type: RepoLayer, name: "CardGetterDB", components: [GormInit], package_path: "/database" },
              { type: RepoLayer, name: "ProjectMaskFormatDB", components: [GormInit], package_path: "/database" },
              { type: RepoLayer, name: "KeyDB", components: [GormInit], package_path: "/database" },
              { type: RepoLayer, name: "CardUpdatorDB", components: [GormInit], package_path: "/database" },
              { type: RepoLayer, name: "CardValidityDb", components: [GormInit], package_path: "/database" },
              {type: GrpcClient, name: "Merchant"},
              {type: GrpcClient, name: "TerminalCards"}
            ]
          },
          {
            type: ServiceLayer
            name: "ProjectMaskFormatService"
            layers: [
              {type: RepoLayer, name: "ProjectMaskFormatDB", components: [GormInit], package_path: "/database"},
              {type: GrpcClient, name: "TerminalCards"}
            ]
          },
          {
            type: ServiceLayer
            name: "CardGetterService"
            layers: [
              { type: RepoLayer, name: "CardsDb", components: [GormInit], package_path: "/database" },
              { type: RepoLayer, name: "ClientsDb", components: [GormInit], package_path: "/database" },
              { type: RepoLayer, name: "KeyDB", components: [GormInit], package_path: "/database" },
              { type: RepoLayer, name: "ProjectMaskFormatDB", components: [GormInit], package_path: "/database" },
              {type: GrpcClient, name: "Acquirer"}
            ]
          },
          {
            type: ServiceLayer
            name: "OneClickCardService"
            layers: [
              { type: RepoLayer, name: "ClientsDb", components: [GormInit], package_path: "/database" },
              { type: RepoLayer, name: "CardGetterDB", components: [GormInit], package_path: "/database" },
              { type: RepoLayer, name: "ProjectMaskFormatDB", components: [GormInit], package_path: "/database" },
              { type: RepoLayer, name: "KeyDB", components: [GormInit], package_path: "/database" },
              {type: GrpcClient, name: "Terminal"},
              {type: GrpcClient, name: "Acquirer"}
            ]
          },
          {
            type: ServiceLayer
            name: "ClientVerificationManagerService"
            layers: [
              { type: RepoLayer, name: "ClientVerificationManagerDB", components: [GormInit], package_path: "/database" },
              {type: GrpcClient, name: "Merchant"}
            ]
          },
          {
            type: ServiceLayer
            name: "CardEncryptManagerService"
          }
        ]
      }
    ]
  };
}
