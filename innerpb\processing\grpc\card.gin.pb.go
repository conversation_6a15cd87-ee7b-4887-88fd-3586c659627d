// Code generated by protoc-gen-go-mvp. DO NOT EDIT.
// versions:
// - protoc-gen-go-mvp v2.0.1
// created by mvp authors

package grpc

import (
	dog "git.local/sensitive/sdk/dog"
	gin "github.com/gin-gonic/gin"
	metadata "google.golang.org/grpc/metadata"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
	http "net/http"
)

func RegisterGinCardRouterGroup(
	in *gin.RouterGroup,
	handler func(func(c *gin.Context) error) gin.HandlerFunc,
) error {
	service, err := NewGinCardService()
	if err != nil {
		return err
	}

	routerGroup := in.Group("processing.card.card.Card")
	routerGroup.PUT("/CreateClientV1", handler(service.CreateClientV1))
	routerGroup.PUT("/GetCardTokensV1", handler(service.GetCardTokensV1))
	routerGroup.PUT("/GetOneClickPayInCardsV1", handler(service.GetOneClickPayInCardsV1))
	routerGroup.PUT("/GetPanByCardIdV1", handler(service.GetPanByCardIdV1))
	routerGroup.PUT("/GetPanByHashedIdV1", handler(service.GetPanByHashedIdV1))
	routerGroup.PUT("/GetOneClickPayOutCardsV1", handler(service.GetOneClickPayOutCardsV1))
	routerGroup.PUT("/GetEncryptedCardToken", handler(service.GetEncryptedCardToken))
	routerGroup.PUT("/GetClientListByVerification", handler(service.GetClientListByVerification))
	routerGroup.PUT("/GetClientListByProjectClient", handler(service.GetClientListByProjectClient))
	routerGroup.PUT("/CheckClientActiveness", handler(service.CheckClientActiveness))
	routerGroup.PUT("/GetCardByPan", handler(service.GetCardByPan))
	routerGroup.PUT("/DecryptPayInCard", handler(service.DecryptPayInCard))
	routerGroup.PUT("/DecryptPayOutCard", handler(service.DecryptPayOutCard))
	routerGroup.PUT("/ReEncryptCard", handler(service.ReEncryptCard))
	routerGroup.PUT("/GetPanInfoByProjectId", handler(service.GetPanInfoByProjectId))
	routerGroup.PUT("/NewKey", handler(service.NewKey))
	routerGroup.PUT("/RotateCardKeys", handler(service.RotateCardKeys))
	routerGroup.PUT("/CheckExpireCards", handler(service.CheckExpireCards))
	routerGroup.PUT("/CreateNewHashKey", handler(service.CreateNewHashKey))
	routerGroup.PUT("/RotateHashKeys", handler(service.RotateHashKeys))
	return nil
}

func NewGinCardService() (GinCardServer, error) {
	client, err := NewPreparedCardClient()
	if err != nil {
		return nil, err
	}

	return &ginCardServer{
		client: NewLoggedCardClient(
			NewIamCardClient(client),
		),
	}, nil
}

//go:generate mockgen -destination=./grpcmock/card.gin.pb.go -package=grpcmock -source=card.gin.pb.go GinCardServer
type GinCardServer interface {
	CreateClientV1(c *gin.Context) error
	GetCardTokensV1(c *gin.Context) error
	GetOneClickPayInCardsV1(c *gin.Context) error
	GetPanByCardIdV1(c *gin.Context) error
	GetPanByHashedIdV1(c *gin.Context) error
	GetOneClickPayOutCardsV1(c *gin.Context) error
	GetEncryptedCardToken(c *gin.Context) error
	GetClientListByVerification(c *gin.Context) error
	GetClientListByProjectClient(c *gin.Context) error
	CheckClientActiveness(c *gin.Context) error
	GetCardByPan(c *gin.Context) error
	DecryptPayInCard(c *gin.Context) error
	DecryptPayOutCard(c *gin.Context) error
	ReEncryptCard(c *gin.Context) error
	GetPanInfoByProjectId(c *gin.Context) error
	NewKey(c *gin.Context) error
	RotateCardKeys(c *gin.Context) error
	CheckExpireCards(c *gin.Context) error
	CreateNewHashKey(c *gin.Context) error
	RotateHashKeys(c *gin.Context) error
}

var _ GinCardServer = (*ginCardServer)(nil)

type ginCardServer struct {
	client CardClient
}

type Card_CreateClientV1_Success struct {
	// Status is the status of the response. Set true
	Status bool `json:"status"`
	// Message is a human-readable explanation of the status
	Message string `json:"message"`
	// StatusCode is the GoErr status. Set 0
	StatusCode int `json:"status_code"`
	// Result is the result of the request
	Result *CreateClientResponseV1 `json:"result"`
}

type Card_CreateClientV1_Failure struct {
	// Status is the status of the response. Set false
	Status bool `json:"status"`
	// Message is a human-readable explanation of the status
	Message string `json:"message"`
	// StatusCode is the GoErr status
	StatusCode int `json:"status_code"`
}

// CreateClientV1
// @Summary CreateClientV1
// @Security bearerAuth
// @ID Card_CreateClientV1
// @Accept json
// @Param request body CreateClientRequestV1 true "CreateClientRequestV1"
// @Success 200 {object} Card_CreateClientV1_Success
// @Failure 401 {object} Card_CreateClientV1_Failure "goerrpkg.ErrorTypeAuthorization"
// @Failure 404 {object} Card_CreateClientV1_Failure "goerrpkg.ErrorTypeNotFound"
// @Failure 400 {object} Card_CreateClientV1_Failure "goerrpkg.ErrorTypeBadRequest"
// @Failure 500 {object} Card_CreateClientV1_Failure "goerrpkg.ErrorTypeUnexpectedError"
// @Failure 400 {object} Card_CreateClientV1_Failure "gin.ErrorTypeBind"
// @Failure 500 {object} Card_CreateClientV1_Failure "Undefined error"
// @Produce json
// @Router /processing.card.card.Card/CreateClientV1 [put]
// @tags Card
func (s *ginCardServer) CreateClientV1(c *gin.Context) error {
	ctx, span := dog.CreateSpan(c.Request.Context(), "GinCardServer_CreateClientV1")
	defer span.End()

	var request CreateClientRequestV1
	if err := c.ShouldBindJSON(&request); err != nil {
		return err
	}

	ctx = metadata.NewOutgoingContext(ctx, metadata.MD(c.Request.Header))

	response, err := s.client.CreateClientV1(ctx, &request)
	if err != nil {
		return err
	}

	c.JSON(http.StatusOK, &Card_CreateClientV1_Success{
		Status:     true,
		Message:    "Success",
		StatusCode: 0,
		Result:     response,
	})
	return nil
}

type Card_GetCardTokensV1_Success struct {
	// Status is the status of the response. Set true
	Status bool `json:"status"`
	// Message is a human-readable explanation of the status
	Message string `json:"message"`
	// StatusCode is the GoErr status. Set 0
	StatusCode int `json:"status_code"`
	// Result is the result of the request
	Result *GetCardTokensResponseV1 `json:"result"`
}

type Card_GetCardTokensV1_Failure struct {
	// Status is the status of the response. Set false
	Status bool `json:"status"`
	// Message is a human-readable explanation of the status
	Message string `json:"message"`
	// StatusCode is the GoErr status
	StatusCode int `json:"status_code"`
}

// GetCardTokensV1
// @Summary GetCardTokensV1
// @Security bearerAuth
// @ID Card_GetCardTokensV1
// @Accept json
// @Param request body GetCardTokensRequestV1 true "GetCardTokensRequestV1"
// @Success 200 {object} Card_GetCardTokensV1_Success
// @Failure 401 {object} Card_GetCardTokensV1_Failure "goerrpkg.ErrorTypeAuthorization"
// @Failure 404 {object} Card_GetCardTokensV1_Failure "goerrpkg.ErrorTypeNotFound"
// @Failure 400 {object} Card_GetCardTokensV1_Failure "goerrpkg.ErrorTypeBadRequest"
// @Failure 500 {object} Card_GetCardTokensV1_Failure "goerrpkg.ErrorTypeUnexpectedError"
// @Failure 400 {object} Card_GetCardTokensV1_Failure "gin.ErrorTypeBind"
// @Failure 500 {object} Card_GetCardTokensV1_Failure "Undefined error"
// @Produce json
// @Router /processing.card.card.Card/GetCardTokensV1 [put]
// @tags Card
func (s *ginCardServer) GetCardTokensV1(c *gin.Context) error {
	ctx, span := dog.CreateSpan(c.Request.Context(), "GinCardServer_GetCardTokensV1")
	defer span.End()

	var request GetCardTokensRequestV1
	if err := c.ShouldBindJSON(&request); err != nil {
		return err
	}

	ctx = metadata.NewOutgoingContext(ctx, metadata.MD(c.Request.Header))

	response, err := s.client.GetCardTokensV1(ctx, &request)
	if err != nil {
		return err
	}

	c.JSON(http.StatusOK, &Card_GetCardTokensV1_Success{
		Status:     true,
		Message:    "Success",
		StatusCode: 0,
		Result:     response,
	})
	return nil
}

type Card_GetOneClickPayInCardsV1_Success struct {
	// Status is the status of the response. Set true
	Status bool `json:"status"`
	// Message is a human-readable explanation of the status
	Message string `json:"message"`
	// StatusCode is the GoErr status. Set 0
	StatusCode int `json:"status_code"`
	// Result is the result of the request
	Result *GetOneClickPayInCardsResponseV1 `json:"result"`
}

type Card_GetOneClickPayInCardsV1_Failure struct {
	// Status is the status of the response. Set false
	Status bool `json:"status"`
	// Message is a human-readable explanation of the status
	Message string `json:"message"`
	// StatusCode is the GoErr status
	StatusCode int `json:"status_code"`
}

// GetOneClickPayInCardsV1
// @Summary GetOneClickPayInCardsV1
// @Security bearerAuth
// @ID Card_GetOneClickPayInCardsV1
// @Accept json
// @Param request body GetOneClickPayInCardsRequestV1 true "GetOneClickPayInCardsRequestV1"
// @Success 200 {object} Card_GetOneClickPayInCardsV1_Success
// @Failure 401 {object} Card_GetOneClickPayInCardsV1_Failure "goerrpkg.ErrorTypeAuthorization"
// @Failure 404 {object} Card_GetOneClickPayInCardsV1_Failure "goerrpkg.ErrorTypeNotFound"
// @Failure 400 {object} Card_GetOneClickPayInCardsV1_Failure "goerrpkg.ErrorTypeBadRequest"
// @Failure 500 {object} Card_GetOneClickPayInCardsV1_Failure "goerrpkg.ErrorTypeUnexpectedError"
// @Failure 400 {object} Card_GetOneClickPayInCardsV1_Failure "gin.ErrorTypeBind"
// @Failure 500 {object} Card_GetOneClickPayInCardsV1_Failure "Undefined error"
// @Produce json
// @Router /processing.card.card.Card/GetOneClickPayInCardsV1 [put]
// @tags Card
func (s *ginCardServer) GetOneClickPayInCardsV1(c *gin.Context) error {
	ctx, span := dog.CreateSpan(c.Request.Context(), "GinCardServer_GetOneClickPayInCardsV1")
	defer span.End()

	var request GetOneClickPayInCardsRequestV1
	if err := c.ShouldBindJSON(&request); err != nil {
		return err
	}

	ctx = metadata.NewOutgoingContext(ctx, metadata.MD(c.Request.Header))

	response, err := s.client.GetOneClickPayInCardsV1(ctx, &request)
	if err != nil {
		return err
	}

	c.JSON(http.StatusOK, &Card_GetOneClickPayInCardsV1_Success{
		Status:     true,
		Message:    "Success",
		StatusCode: 0,
		Result:     response,
	})
	return nil
}

type Card_GetPanByCardIdV1_Success struct {
	// Status is the status of the response. Set true
	Status bool `json:"status"`
	// Message is a human-readable explanation of the status
	Message string `json:"message"`
	// StatusCode is the GoErr status. Set 0
	StatusCode int `json:"status_code"`
	// Result is the result of the request
	Result *GetPanResponseV1 `json:"result"`
}

type Card_GetPanByCardIdV1_Failure struct {
	// Status is the status of the response. Set false
	Status bool `json:"status"`
	// Message is a human-readable explanation of the status
	Message string `json:"message"`
	// StatusCode is the GoErr status
	StatusCode int `json:"status_code"`
}

// GetPanByCardIdV1
// @Summary GetPanByCardIdV1
// @Security bearerAuth
// @ID Card_GetPanByCardIdV1
// @Accept json
// @Param request body GetPanByCardIdRequestV1 true "GetPanByCardIdRequestV1"
// @Success 200 {object} Card_GetPanByCardIdV1_Success
// @Failure 401 {object} Card_GetPanByCardIdV1_Failure "goerrpkg.ErrorTypeAuthorization"
// @Failure 404 {object} Card_GetPanByCardIdV1_Failure "goerrpkg.ErrorTypeNotFound"
// @Failure 400 {object} Card_GetPanByCardIdV1_Failure "goerrpkg.ErrorTypeBadRequest"
// @Failure 500 {object} Card_GetPanByCardIdV1_Failure "goerrpkg.ErrorTypeUnexpectedError"
// @Failure 400 {object} Card_GetPanByCardIdV1_Failure "gin.ErrorTypeBind"
// @Failure 500 {object} Card_GetPanByCardIdV1_Failure "Undefined error"
// @Produce json
// @Router /processing.card.card.Card/GetPanByCardIdV1 [put]
// @tags Card
func (s *ginCardServer) GetPanByCardIdV1(c *gin.Context) error {
	ctx, span := dog.CreateSpan(c.Request.Context(), "GinCardServer_GetPanByCardIdV1")
	defer span.End()

	var request GetPanByCardIdRequestV1
	if err := c.ShouldBindJSON(&request); err != nil {
		return err
	}

	ctx = metadata.NewOutgoingContext(ctx, metadata.MD(c.Request.Header))

	response, err := s.client.GetPanByCardIdV1(ctx, &request)
	if err != nil {
		return err
	}

	c.JSON(http.StatusOK, &Card_GetPanByCardIdV1_Success{
		Status:     true,
		Message:    "Success",
		StatusCode: 0,
		Result:     response,
	})
	return nil
}

type Card_GetPanByHashedIdV1_Success struct {
	// Status is the status of the response. Set true
	Status bool `json:"status"`
	// Message is a human-readable explanation of the status
	Message string `json:"message"`
	// StatusCode is the GoErr status. Set 0
	StatusCode int `json:"status_code"`
	// Result is the result of the request
	Result *GetPanResponseV1 `json:"result"`
}

type Card_GetPanByHashedIdV1_Failure struct {
	// Status is the status of the response. Set false
	Status bool `json:"status"`
	// Message is a human-readable explanation of the status
	Message string `json:"message"`
	// StatusCode is the GoErr status
	StatusCode int `json:"status_code"`
}

// GetPanByHashedIdV1
// @Summary GetPanByHashedIdV1
// @Security bearerAuth
// @ID Card_GetPanByHashedIdV1
// @Accept json
// @Param request body GetPanByHashedIdRequestV1 true "GetPanByHashedIdRequestV1"
// @Success 200 {object} Card_GetPanByHashedIdV1_Success
// @Failure 401 {object} Card_GetPanByHashedIdV1_Failure "goerrpkg.ErrorTypeAuthorization"
// @Failure 404 {object} Card_GetPanByHashedIdV1_Failure "goerrpkg.ErrorTypeNotFound"
// @Failure 400 {object} Card_GetPanByHashedIdV1_Failure "goerrpkg.ErrorTypeBadRequest"
// @Failure 500 {object} Card_GetPanByHashedIdV1_Failure "goerrpkg.ErrorTypeUnexpectedError"
// @Failure 400 {object} Card_GetPanByHashedIdV1_Failure "gin.ErrorTypeBind"
// @Failure 500 {object} Card_GetPanByHashedIdV1_Failure "Undefined error"
// @Produce json
// @Router /processing.card.card.Card/GetPanByHashedIdV1 [put]
// @tags Card
func (s *ginCardServer) GetPanByHashedIdV1(c *gin.Context) error {
	ctx, span := dog.CreateSpan(c.Request.Context(), "GinCardServer_GetPanByHashedIdV1")
	defer span.End()

	var request GetPanByHashedIdRequestV1
	if err := c.ShouldBindJSON(&request); err != nil {
		return err
	}

	ctx = metadata.NewOutgoingContext(ctx, metadata.MD(c.Request.Header))

	response, err := s.client.GetPanByHashedIdV1(ctx, &request)
	if err != nil {
		return err
	}

	c.JSON(http.StatusOK, &Card_GetPanByHashedIdV1_Success{
		Status:     true,
		Message:    "Success",
		StatusCode: 0,
		Result:     response,
	})
	return nil
}

type Card_GetOneClickPayOutCardsV1_Success struct {
	// Status is the status of the response. Set true
	Status bool `json:"status"`
	// Message is a human-readable explanation of the status
	Message string `json:"message"`
	// StatusCode is the GoErr status. Set 0
	StatusCode int `json:"status_code"`
	// Result is the result of the request
	Result *GetOneClickPayOutCardsResponseV1 `json:"result"`
}

type Card_GetOneClickPayOutCardsV1_Failure struct {
	// Status is the status of the response. Set false
	Status bool `json:"status"`
	// Message is a human-readable explanation of the status
	Message string `json:"message"`
	// StatusCode is the GoErr status
	StatusCode int `json:"status_code"`
}

// GetOneClickPayOutCardsV1
// @Summary GetOneClickPayOutCardsV1
// @Security bearerAuth
// @ID Card_GetOneClickPayOutCardsV1
// @Accept json
// @Param request body GetOneClickPayOutCardsRequestV1 true "GetOneClickPayOutCardsRequestV1"
// @Success 200 {object} Card_GetOneClickPayOutCardsV1_Success
// @Failure 401 {object} Card_GetOneClickPayOutCardsV1_Failure "goerrpkg.ErrorTypeAuthorization"
// @Failure 404 {object} Card_GetOneClickPayOutCardsV1_Failure "goerrpkg.ErrorTypeNotFound"
// @Failure 400 {object} Card_GetOneClickPayOutCardsV1_Failure "goerrpkg.ErrorTypeBadRequest"
// @Failure 500 {object} Card_GetOneClickPayOutCardsV1_Failure "goerrpkg.ErrorTypeUnexpectedError"
// @Failure 400 {object} Card_GetOneClickPayOutCardsV1_Failure "gin.ErrorTypeBind"
// @Failure 500 {object} Card_GetOneClickPayOutCardsV1_Failure "Undefined error"
// @Produce json
// @Router /processing.card.card.Card/GetOneClickPayOutCardsV1 [put]
// @tags Card
func (s *ginCardServer) GetOneClickPayOutCardsV1(c *gin.Context) error {
	ctx, span := dog.CreateSpan(c.Request.Context(), "GinCardServer_GetOneClickPayOutCardsV1")
	defer span.End()

	var request GetOneClickPayOutCardsRequestV1
	if err := c.ShouldBindJSON(&request); err != nil {
		return err
	}

	ctx = metadata.NewOutgoingContext(ctx, metadata.MD(c.Request.Header))

	response, err := s.client.GetOneClickPayOutCardsV1(ctx, &request)
	if err != nil {
		return err
	}

	c.JSON(http.StatusOK, &Card_GetOneClickPayOutCardsV1_Success{
		Status:     true,
		Message:    "Success",
		StatusCode: 0,
		Result:     response,
	})
	return nil
}

type Card_GetEncryptedCardToken_Success struct {
	// Status is the status of the response. Set true
	Status bool `json:"status"`
	// Message is a human-readable explanation of the status
	Message string `json:"message"`
	// StatusCode is the GoErr status. Set 0
	StatusCode int `json:"status_code"`
	// Result is the result of the request
	Result *GetEncryptedCardResponseV1 `json:"result"`
}

type Card_GetEncryptedCardToken_Failure struct {
	// Status is the status of the response. Set false
	Status bool `json:"status"`
	// Message is a human-readable explanation of the status
	Message string `json:"message"`
	// StatusCode is the GoErr status
	StatusCode int `json:"status_code"`
}

// GetEncryptedCardToken
// @Summary GetEncryptedCardToken
// @Security bearerAuth
// @ID Card_GetEncryptedCardToken
// @Accept json
// @Param request body GetEncryptedCardRequestV1 true "GetEncryptedCardRequestV1"
// @Success 200 {object} Card_GetEncryptedCardToken_Success
// @Failure 401 {object} Card_GetEncryptedCardToken_Failure "goerrpkg.ErrorTypeAuthorization"
// @Failure 404 {object} Card_GetEncryptedCardToken_Failure "goerrpkg.ErrorTypeNotFound"
// @Failure 400 {object} Card_GetEncryptedCardToken_Failure "goerrpkg.ErrorTypeBadRequest"
// @Failure 500 {object} Card_GetEncryptedCardToken_Failure "goerrpkg.ErrorTypeUnexpectedError"
// @Failure 400 {object} Card_GetEncryptedCardToken_Failure "gin.ErrorTypeBind"
// @Failure 500 {object} Card_GetEncryptedCardToken_Failure "Undefined error"
// @Produce json
// @Router /processing.card.card.Card/GetEncryptedCardToken [put]
// @tags Card
func (s *ginCardServer) GetEncryptedCardToken(c *gin.Context) error {
	ctx, span := dog.CreateSpan(c.Request.Context(), "GinCardServer_GetEncryptedCardToken")
	defer span.End()

	var request GetEncryptedCardRequestV1
	if err := c.ShouldBindJSON(&request); err != nil {
		return err
	}

	ctx = metadata.NewOutgoingContext(ctx, metadata.MD(c.Request.Header))

	response, err := s.client.GetEncryptedCardToken(ctx, &request)
	if err != nil {
		return err
	}

	c.JSON(http.StatusOK, &Card_GetEncryptedCardToken_Success{
		Status:     true,
		Message:    "Success",
		StatusCode: 0,
		Result:     response,
	})
	return nil
}

type Card_GetClientListByVerification_Success struct {
	// Status is the status of the response. Set true
	Status bool `json:"status"`
	// Message is a human-readable explanation of the status
	Message string `json:"message"`
	// StatusCode is the GoErr status. Set 0
	StatusCode int `json:"status_code"`
	// Result is the result of the request
	Result *GetClientListByProjectClientResponseV1 `json:"result"`
}

type Card_GetClientListByVerification_Failure struct {
	// Status is the status of the response. Set false
	Status bool `json:"status"`
	// Message is a human-readable explanation of the status
	Message string `json:"message"`
	// StatusCode is the GoErr status
	StatusCode int `json:"status_code"`
}

// GetClientListByVerification
// @Summary GetClientListByVerification
// @Security bearerAuth
// @ID Card_GetClientListByVerification
// @Accept json
// @Param request body GetClientListByVerificationRequestV1 true "GetClientListByVerificationRequestV1"
// @Success 200 {object} Card_GetClientListByVerification_Success
// @Failure 401 {object} Card_GetClientListByVerification_Failure "goerrpkg.ErrorTypeAuthorization"
// @Failure 404 {object} Card_GetClientListByVerification_Failure "goerrpkg.ErrorTypeNotFound"
// @Failure 400 {object} Card_GetClientListByVerification_Failure "goerrpkg.ErrorTypeBadRequest"
// @Failure 500 {object} Card_GetClientListByVerification_Failure "goerrpkg.ErrorTypeUnexpectedError"
// @Failure 400 {object} Card_GetClientListByVerification_Failure "gin.ErrorTypeBind"
// @Failure 500 {object} Card_GetClientListByVerification_Failure "Undefined error"
// @Produce json
// @Router /processing.card.card.Card/GetClientListByVerification [put]
// @tags Card
func (s *ginCardServer) GetClientListByVerification(c *gin.Context) error {
	ctx, span := dog.CreateSpan(c.Request.Context(), "GinCardServer_GetClientListByVerification")
	defer span.End()

	var request GetClientListByVerificationRequestV1
	if err := c.ShouldBindJSON(&request); err != nil {
		return err
	}

	ctx = metadata.NewOutgoingContext(ctx, metadata.MD(c.Request.Header))

	response, err := s.client.GetClientListByVerification(ctx, &request)
	if err != nil {
		return err
	}

	c.JSON(http.StatusOK, &Card_GetClientListByVerification_Success{
		Status:     true,
		Message:    "Success",
		StatusCode: 0,
		Result:     response,
	})
	return nil
}

type Card_GetClientListByProjectClient_Success struct {
	// Status is the status of the response. Set true
	Status bool `json:"status"`
	// Message is a human-readable explanation of the status
	Message string `json:"message"`
	// StatusCode is the GoErr status. Set 0
	StatusCode int `json:"status_code"`
	// Result is the result of the request
	Result *GetClientListByProjectClientResponseV1 `json:"result"`
}

type Card_GetClientListByProjectClient_Failure struct {
	// Status is the status of the response. Set false
	Status bool `json:"status"`
	// Message is a human-readable explanation of the status
	Message string `json:"message"`
	// StatusCode is the GoErr status
	StatusCode int `json:"status_code"`
}

// GetClientListByProjectClient
// @Summary GetClientListByProjectClient
// @Security bearerAuth
// @ID Card_GetClientListByProjectClient
// @Accept json
// @Param request body GetClientListByProjectClientRequestV1 true "GetClientListByProjectClientRequestV1"
// @Success 200 {object} Card_GetClientListByProjectClient_Success
// @Failure 401 {object} Card_GetClientListByProjectClient_Failure "goerrpkg.ErrorTypeAuthorization"
// @Failure 404 {object} Card_GetClientListByProjectClient_Failure "goerrpkg.ErrorTypeNotFound"
// @Failure 400 {object} Card_GetClientListByProjectClient_Failure "goerrpkg.ErrorTypeBadRequest"
// @Failure 500 {object} Card_GetClientListByProjectClient_Failure "goerrpkg.ErrorTypeUnexpectedError"
// @Failure 400 {object} Card_GetClientListByProjectClient_Failure "gin.ErrorTypeBind"
// @Failure 500 {object} Card_GetClientListByProjectClient_Failure "Undefined error"
// @Produce json
// @Router /processing.card.card.Card/GetClientListByProjectClient [put]
// @tags Card
func (s *ginCardServer) GetClientListByProjectClient(c *gin.Context) error {
	ctx, span := dog.CreateSpan(c.Request.Context(), "GinCardServer_GetClientListByProjectClient")
	defer span.End()

	var request GetClientListByProjectClientRequestV1
	if err := c.ShouldBindJSON(&request); err != nil {
		return err
	}

	ctx = metadata.NewOutgoingContext(ctx, metadata.MD(c.Request.Header))

	response, err := s.client.GetClientListByProjectClient(ctx, &request)
	if err != nil {
		return err
	}

	c.JSON(http.StatusOK, &Card_GetClientListByProjectClient_Success{
		Status:     true,
		Message:    "Success",
		StatusCode: 0,
		Result:     response,
	})
	return nil
}

type Card_CheckClientActiveness_Success struct {
	// Status is the status of the response. Set true
	Status bool `json:"status"`
	// Message is a human-readable explanation of the status
	Message string `json:"message"`
	// StatusCode is the GoErr status. Set 0
	StatusCode int `json:"status_code"`
	// Result is the result of the request
	Result *emptypb.Empty `json:"result"`
}

type Card_CheckClientActiveness_Failure struct {
	// Status is the status of the response. Set false
	Status bool `json:"status"`
	// Message is a human-readable explanation of the status
	Message string `json:"message"`
	// StatusCode is the GoErr status
	StatusCode int `json:"status_code"`
}

// CheckClientActiveness
// @Summary CheckClientActiveness
// @Security bearerAuth
// @ID Card_CheckClientActiveness
// @Accept json
// @Param request body CheckClientActivenessRequestV1 true "CheckClientActivenessRequestV1"
// @Success 200 {object} Card_CheckClientActiveness_Success
// @Failure 401 {object} Card_CheckClientActiveness_Failure "goerrpkg.ErrorTypeAuthorization"
// @Failure 404 {object} Card_CheckClientActiveness_Failure "goerrpkg.ErrorTypeNotFound"
// @Failure 400 {object} Card_CheckClientActiveness_Failure "goerrpkg.ErrorTypeBadRequest"
// @Failure 500 {object} Card_CheckClientActiveness_Failure "goerrpkg.ErrorTypeUnexpectedError"
// @Failure 400 {object} Card_CheckClientActiveness_Failure "gin.ErrorTypeBind"
// @Failure 500 {object} Card_CheckClientActiveness_Failure "Undefined error"
// @Produce json
// @Router /processing.card.card.Card/CheckClientActiveness [put]
// @tags Card
func (s *ginCardServer) CheckClientActiveness(c *gin.Context) error {
	ctx, span := dog.CreateSpan(c.Request.Context(), "GinCardServer_CheckClientActiveness")
	defer span.End()

	var request CheckClientActivenessRequestV1
	if err := c.ShouldBindJSON(&request); err != nil {
		return err
	}

	ctx = metadata.NewOutgoingContext(ctx, metadata.MD(c.Request.Header))

	response, err := s.client.CheckClientActiveness(ctx, &request)
	if err != nil {
		return err
	}

	c.JSON(http.StatusOK, &Card_CheckClientActiveness_Success{
		Status:     true,
		Message:    "Success",
		StatusCode: 0,
		Result:     response,
	})
	return nil
}

type Card_GetCardByPan_Success struct {
	// Status is the status of the response. Set true
	Status bool `json:"status"`
	// Message is a human-readable explanation of the status
	Message string `json:"message"`
	// StatusCode is the GoErr status. Set 0
	StatusCode int `json:"status_code"`
	// Result is the result of the request
	Result *GetCardByPanResponseV1 `json:"result"`
}

type Card_GetCardByPan_Failure struct {
	// Status is the status of the response. Set false
	Status bool `json:"status"`
	// Message is a human-readable explanation of the status
	Message string `json:"message"`
	// StatusCode is the GoErr status
	StatusCode int `json:"status_code"`
}

// GetCardByPan
// @Summary GetCardByPan
// @Security bearerAuth
// @ID Card_GetCardByPan
// @Accept json
// @Param request body GetCardByPanRequestV1 true "GetCardByPanRequestV1"
// @Success 200 {object} Card_GetCardByPan_Success
// @Failure 401 {object} Card_GetCardByPan_Failure "goerrpkg.ErrorTypeAuthorization"
// @Failure 404 {object} Card_GetCardByPan_Failure "goerrpkg.ErrorTypeNotFound"
// @Failure 400 {object} Card_GetCardByPan_Failure "goerrpkg.ErrorTypeBadRequest"
// @Failure 500 {object} Card_GetCardByPan_Failure "goerrpkg.ErrorTypeUnexpectedError"
// @Failure 400 {object} Card_GetCardByPan_Failure "gin.ErrorTypeBind"
// @Failure 500 {object} Card_GetCardByPan_Failure "Undefined error"
// @Produce json
// @Router /processing.card.card.Card/GetCardByPan [put]
// @tags Card
func (s *ginCardServer) GetCardByPan(c *gin.Context) error {
	ctx, span := dog.CreateSpan(c.Request.Context(), "GinCardServer_GetCardByPan")
	defer span.End()

	var request GetCardByPanRequestV1
	if err := c.ShouldBindJSON(&request); err != nil {
		return err
	}

	ctx = metadata.NewOutgoingContext(ctx, metadata.MD(c.Request.Header))

	response, err := s.client.GetCardByPan(ctx, &request)
	if err != nil {
		return err
	}

	c.JSON(http.StatusOK, &Card_GetCardByPan_Success{
		Status:     true,
		Message:    "Success",
		StatusCode: 0,
		Result:     response,
	})
	return nil
}

type Card_DecryptPayInCard_Success struct {
	// Status is the status of the response. Set true
	Status bool `json:"status"`
	// Message is a human-readable explanation of the status
	Message string `json:"message"`
	// StatusCode is the GoErr status. Set 0
	StatusCode int `json:"status_code"`
	// Result is the result of the request
	Result *DecryptPayInResponse `json:"result"`
}

type Card_DecryptPayInCard_Failure struct {
	// Status is the status of the response. Set false
	Status bool `json:"status"`
	// Message is a human-readable explanation of the status
	Message string `json:"message"`
	// StatusCode is the GoErr status
	StatusCode int `json:"status_code"`
}

// DecryptPayInCard
// @Summary DecryptPayInCard
// @Security bearerAuth
// @ID Card_DecryptPayInCard
// @Accept json
// @Param request body DecryptPayInRequest true "DecryptPayInRequest"
// @Success 200 {object} Card_DecryptPayInCard_Success
// @Failure 401 {object} Card_DecryptPayInCard_Failure "goerrpkg.ErrorTypeAuthorization"
// @Failure 404 {object} Card_DecryptPayInCard_Failure "goerrpkg.ErrorTypeNotFound"
// @Failure 400 {object} Card_DecryptPayInCard_Failure "goerrpkg.ErrorTypeBadRequest"
// @Failure 500 {object} Card_DecryptPayInCard_Failure "goerrpkg.ErrorTypeUnexpectedError"
// @Failure 400 {object} Card_DecryptPayInCard_Failure "gin.ErrorTypeBind"
// @Failure 500 {object} Card_DecryptPayInCard_Failure "Undefined error"
// @Produce json
// @Router /processing.card.card.Card/DecryptPayInCard [put]
// @tags Card
func (s *ginCardServer) DecryptPayInCard(c *gin.Context) error {
	ctx, span := dog.CreateSpan(c.Request.Context(), "GinCardServer_DecryptPayInCard")
	defer span.End()

	var request DecryptPayInRequest
	if err := c.ShouldBindJSON(&request); err != nil {
		return err
	}

	ctx = metadata.NewOutgoingContext(ctx, metadata.MD(c.Request.Header))

	response, err := s.client.DecryptPayInCard(ctx, &request)
	if err != nil {
		return err
	}

	c.JSON(http.StatusOK, &Card_DecryptPayInCard_Success{
		Status:     true,
		Message:    "Success",
		StatusCode: 0,
		Result:     response,
	})
	return nil
}

type Card_DecryptPayOutCard_Success struct {
	// Status is the status of the response. Set true
	Status bool `json:"status"`
	// Message is a human-readable explanation of the status
	Message string `json:"message"`
	// StatusCode is the GoErr status. Set 0
	StatusCode int `json:"status_code"`
	// Result is the result of the request
	Result *DecryptPayOutResponse `json:"result"`
}

type Card_DecryptPayOutCard_Failure struct {
	// Status is the status of the response. Set false
	Status bool `json:"status"`
	// Message is a human-readable explanation of the status
	Message string `json:"message"`
	// StatusCode is the GoErr status
	StatusCode int `json:"status_code"`
}

// DecryptPayOutCard
// @Summary DecryptPayOutCard
// @Security bearerAuth
// @ID Card_DecryptPayOutCard
// @Accept json
// @Param request body DecryptPayOutRequest true "DecryptPayOutRequest"
// @Success 200 {object} Card_DecryptPayOutCard_Success
// @Failure 401 {object} Card_DecryptPayOutCard_Failure "goerrpkg.ErrorTypeAuthorization"
// @Failure 404 {object} Card_DecryptPayOutCard_Failure "goerrpkg.ErrorTypeNotFound"
// @Failure 400 {object} Card_DecryptPayOutCard_Failure "goerrpkg.ErrorTypeBadRequest"
// @Failure 500 {object} Card_DecryptPayOutCard_Failure "goerrpkg.ErrorTypeUnexpectedError"
// @Failure 400 {object} Card_DecryptPayOutCard_Failure "gin.ErrorTypeBind"
// @Failure 500 {object} Card_DecryptPayOutCard_Failure "Undefined error"
// @Produce json
// @Router /processing.card.card.Card/DecryptPayOutCard [put]
// @tags Card
func (s *ginCardServer) DecryptPayOutCard(c *gin.Context) error {
	ctx, span := dog.CreateSpan(c.Request.Context(), "GinCardServer_DecryptPayOutCard")
	defer span.End()

	var request DecryptPayOutRequest
	if err := c.ShouldBindJSON(&request); err != nil {
		return err
	}

	ctx = metadata.NewOutgoingContext(ctx, metadata.MD(c.Request.Header))

	response, err := s.client.DecryptPayOutCard(ctx, &request)
	if err != nil {
		return err
	}

	c.JSON(http.StatusOK, &Card_DecryptPayOutCard_Success{
		Status:     true,
		Message:    "Success",
		StatusCode: 0,
		Result:     response,
	})
	return nil
}

type Card_ReEncryptCard_Success struct {
	// Status is the status of the response. Set true
	Status bool `json:"status"`
	// Message is a human-readable explanation of the status
	Message string `json:"message"`
	// StatusCode is the GoErr status. Set 0
	StatusCode int `json:"status_code"`
	// Result is the result of the request
	Result *ReEncryptCardResponse `json:"result"`
}

type Card_ReEncryptCard_Failure struct {
	// Status is the status of the response. Set false
	Status bool `json:"status"`
	// Message is a human-readable explanation of the status
	Message string `json:"message"`
	// StatusCode is the GoErr status
	StatusCode int `json:"status_code"`
}

// ReEncryptCard
// @Summary ReEncryptCard
// @Security bearerAuth
// @ID Card_ReEncryptCard
// @Accept json
// @Param request body ReEncryptCardRequest true "ReEncryptCardRequest"
// @Success 200 {object} Card_ReEncryptCard_Success
// @Failure 401 {object} Card_ReEncryptCard_Failure "goerrpkg.ErrorTypeAuthorization"
// @Failure 404 {object} Card_ReEncryptCard_Failure "goerrpkg.ErrorTypeNotFound"
// @Failure 400 {object} Card_ReEncryptCard_Failure "goerrpkg.ErrorTypeBadRequest"
// @Failure 500 {object} Card_ReEncryptCard_Failure "goerrpkg.ErrorTypeUnexpectedError"
// @Failure 400 {object} Card_ReEncryptCard_Failure "gin.ErrorTypeBind"
// @Failure 500 {object} Card_ReEncryptCard_Failure "Undefined error"
// @Produce json
// @Router /processing.card.card.Card/ReEncryptCard [put]
// @tags Card
func (s *ginCardServer) ReEncryptCard(c *gin.Context) error {
	ctx, span := dog.CreateSpan(c.Request.Context(), "GinCardServer_ReEncryptCard")
	defer span.End()

	var request ReEncryptCardRequest
	if err := c.ShouldBindJSON(&request); err != nil {
		return err
	}

	ctx = metadata.NewOutgoingContext(ctx, metadata.MD(c.Request.Header))

	response, err := s.client.ReEncryptCard(ctx, &request)
	if err != nil {
		return err
	}

	c.JSON(http.StatusOK, &Card_ReEncryptCard_Success{
		Status:     true,
		Message:    "Success",
		StatusCode: 0,
		Result:     response,
	})
	return nil
}

type Card_GetPanInfoByProjectId_Success struct {
	// Status is the status of the response. Set true
	Status bool `json:"status"`
	// Message is a human-readable explanation of the status
	Message string `json:"message"`
	// StatusCode is the GoErr status. Set 0
	StatusCode int `json:"status_code"`
	// Result is the result of the request
	Result *GetPanInfoByProjectIdResponse `json:"result"`
}

type Card_GetPanInfoByProjectId_Failure struct {
	// Status is the status of the response. Set false
	Status bool `json:"status"`
	// Message is a human-readable explanation of the status
	Message string `json:"message"`
	// StatusCode is the GoErr status
	StatusCode int `json:"status_code"`
}

// GetPanInfoByProjectId
// @Summary GetPanInfoByProjectId
// @Security bearerAuth
// @ID Card_GetPanInfoByProjectId
// @Accept json
// @Param request body GetPanInfoByProjectIdRequest true "GetPanInfoByProjectIdRequest"
// @Success 200 {object} Card_GetPanInfoByProjectId_Success
// @Failure 401 {object} Card_GetPanInfoByProjectId_Failure "goerrpkg.ErrorTypeAuthorization"
// @Failure 404 {object} Card_GetPanInfoByProjectId_Failure "goerrpkg.ErrorTypeNotFound"
// @Failure 400 {object} Card_GetPanInfoByProjectId_Failure "goerrpkg.ErrorTypeBadRequest"
// @Failure 500 {object} Card_GetPanInfoByProjectId_Failure "goerrpkg.ErrorTypeUnexpectedError"
// @Failure 400 {object} Card_GetPanInfoByProjectId_Failure "gin.ErrorTypeBind"
// @Failure 500 {object} Card_GetPanInfoByProjectId_Failure "Undefined error"
// @Produce json
// @Router /processing.card.card.Card/GetPanInfoByProjectId [put]
// @tags Card
func (s *ginCardServer) GetPanInfoByProjectId(c *gin.Context) error {
	ctx, span := dog.CreateSpan(c.Request.Context(), "GinCardServer_GetPanInfoByProjectId")
	defer span.End()

	var request GetPanInfoByProjectIdRequest
	if err := c.ShouldBindJSON(&request); err != nil {
		return err
	}

	ctx = metadata.NewOutgoingContext(ctx, metadata.MD(c.Request.Header))

	response, err := s.client.GetPanInfoByProjectId(ctx, &request)
	if err != nil {
		return err
	}

	c.JSON(http.StatusOK, &Card_GetPanInfoByProjectId_Success{
		Status:     true,
		Message:    "Success",
		StatusCode: 0,
		Result:     response,
	})
	return nil
}

type Card_NewKey_Success struct {
	// Status is the status of the response. Set true
	Status bool `json:"status"`
	// Message is a human-readable explanation of the status
	Message string `json:"message"`
	// StatusCode is the GoErr status. Set 0
	StatusCode int `json:"status_code"`
	// Result is the result of the request
	Result *emptypb.Empty `json:"result"`
}

type Card_NewKey_Failure struct {
	// Status is the status of the response. Set false
	Status bool `json:"status"`
	// Message is a human-readable explanation of the status
	Message string `json:"message"`
	// StatusCode is the GoErr status
	StatusCode int `json:"status_code"`
}

// NewKey
// @Summary  jobs
// @Security bearerAuth
// @ID Card_NewKey
// @Accept json
// @Param request body emptypb.Empty true "Empty"
// @Success 200 {object} Card_NewKey_Success
// @Failure 401 {object} Card_NewKey_Failure "goerrpkg.ErrorTypeAuthorization"
// @Failure 404 {object} Card_NewKey_Failure "goerrpkg.ErrorTypeNotFound"
// @Failure 400 {object} Card_NewKey_Failure "goerrpkg.ErrorTypeBadRequest"
// @Failure 500 {object} Card_NewKey_Failure "goerrpkg.ErrorTypeUnexpectedError"
// @Failure 400 {object} Card_NewKey_Failure "gin.ErrorTypeBind"
// @Failure 500 {object} Card_NewKey_Failure "Undefined error"
// @Produce json
// @Router /processing.card.card.Card/NewKey [put]
// @tags Card
func (s *ginCardServer) NewKey(c *gin.Context) error {
	ctx, span := dog.CreateSpan(c.Request.Context(), "GinCardServer_NewKey")
	defer span.End()

	var request emptypb.Empty
	if err := c.ShouldBindJSON(&request); err != nil {
		return err
	}

	ctx = metadata.NewOutgoingContext(ctx, metadata.MD(c.Request.Header))

	response, err := s.client.NewKey(ctx, &request)
	if err != nil {
		return err
	}

	c.JSON(http.StatusOK, &Card_NewKey_Success{
		Status:     true,
		Message:    "Success",
		StatusCode: 0,
		Result:     response,
	})
	return nil
}

type Card_RotateCardKeys_Success struct {
	// Status is the status of the response. Set true
	Status bool `json:"status"`
	// Message is a human-readable explanation of the status
	Message string `json:"message"`
	// StatusCode is the GoErr status. Set 0
	StatusCode int `json:"status_code"`
	// Result is the result of the request
	Result *emptypb.Empty `json:"result"`
}

type Card_RotateCardKeys_Failure struct {
	// Status is the status of the response. Set false
	Status bool `json:"status"`
	// Message is a human-readable explanation of the status
	Message string `json:"message"`
	// StatusCode is the GoErr status
	StatusCode int `json:"status_code"`
}

// RotateCardKeys
// @Summary RotateCardKeys
// @Security bearerAuth
// @ID Card_RotateCardKeys
// @Accept json
// @Param request body emptypb.Empty true "Empty"
// @Success 200 {object} Card_RotateCardKeys_Success
// @Failure 401 {object} Card_RotateCardKeys_Failure "goerrpkg.ErrorTypeAuthorization"
// @Failure 404 {object} Card_RotateCardKeys_Failure "goerrpkg.ErrorTypeNotFound"
// @Failure 400 {object} Card_RotateCardKeys_Failure "goerrpkg.ErrorTypeBadRequest"
// @Failure 500 {object} Card_RotateCardKeys_Failure "goerrpkg.ErrorTypeUnexpectedError"
// @Failure 400 {object} Card_RotateCardKeys_Failure "gin.ErrorTypeBind"
// @Failure 500 {object} Card_RotateCardKeys_Failure "Undefined error"
// @Produce json
// @Router /processing.card.card.Card/RotateCardKeys [put]
// @tags Card
func (s *ginCardServer) RotateCardKeys(c *gin.Context) error {
	ctx, span := dog.CreateSpan(c.Request.Context(), "GinCardServer_RotateCardKeys")
	defer span.End()

	var request emptypb.Empty
	if err := c.ShouldBindJSON(&request); err != nil {
		return err
	}

	ctx = metadata.NewOutgoingContext(ctx, metadata.MD(c.Request.Header))

	response, err := s.client.RotateCardKeys(ctx, &request)
	if err != nil {
		return err
	}

	c.JSON(http.StatusOK, &Card_RotateCardKeys_Success{
		Status:     true,
		Message:    "Success",
		StatusCode: 0,
		Result:     response,
	})
	return nil
}

type Card_CheckExpireCards_Success struct {
	// Status is the status of the response. Set true
	Status bool `json:"status"`
	// Message is a human-readable explanation of the status
	Message string `json:"message"`
	// StatusCode is the GoErr status. Set 0
	StatusCode int `json:"status_code"`
	// Result is the result of the request
	Result *emptypb.Empty `json:"result"`
}

type Card_CheckExpireCards_Failure struct {
	// Status is the status of the response. Set false
	Status bool `json:"status"`
	// Message is a human-readable explanation of the status
	Message string `json:"message"`
	// StatusCode is the GoErr status
	StatusCode int `json:"status_code"`
}

// CheckExpireCards
// @Summary CheckExpireCards
// @Security bearerAuth
// @ID Card_CheckExpireCards
// @Accept json
// @Param request body emptypb.Empty true "Empty"
// @Success 200 {object} Card_CheckExpireCards_Success
// @Failure 401 {object} Card_CheckExpireCards_Failure "goerrpkg.ErrorTypeAuthorization"
// @Failure 404 {object} Card_CheckExpireCards_Failure "goerrpkg.ErrorTypeNotFound"
// @Failure 400 {object} Card_CheckExpireCards_Failure "goerrpkg.ErrorTypeBadRequest"
// @Failure 500 {object} Card_CheckExpireCards_Failure "goerrpkg.ErrorTypeUnexpectedError"
// @Failure 400 {object} Card_CheckExpireCards_Failure "gin.ErrorTypeBind"
// @Failure 500 {object} Card_CheckExpireCards_Failure "Undefined error"
// @Produce json
// @Router /processing.card.card.Card/CheckExpireCards [put]
// @tags Card
func (s *ginCardServer) CheckExpireCards(c *gin.Context) error {
	ctx, span := dog.CreateSpan(c.Request.Context(), "GinCardServer_CheckExpireCards")
	defer span.End()

	var request emptypb.Empty
	if err := c.ShouldBindJSON(&request); err != nil {
		return err
	}

	ctx = metadata.NewOutgoingContext(ctx, metadata.MD(c.Request.Header))

	response, err := s.client.CheckExpireCards(ctx, &request)
	if err != nil {
		return err
	}

	c.JSON(http.StatusOK, &Card_CheckExpireCards_Success{
		Status:     true,
		Message:    "Success",
		StatusCode: 0,
		Result:     response,
	})
	return nil
}

type Card_CreateNewHashKey_Success struct {
	// Status is the status of the response. Set true
	Status bool `json:"status"`
	// Message is a human-readable explanation of the status
	Message string `json:"message"`
	// StatusCode is the GoErr status. Set 0
	StatusCode int `json:"status_code"`
	// Result is the result of the request
	Result *emptypb.Empty `json:"result"`
}

type Card_CreateNewHashKey_Failure struct {
	// Status is the status of the response. Set false
	Status bool `json:"status"`
	// Message is a human-readable explanation of the status
	Message string `json:"message"`
	// StatusCode is the GoErr status
	StatusCode int `json:"status_code"`
}

// CreateNewHashKey
// @Summary CreateNewHashKey
// @Security bearerAuth
// @ID Card_CreateNewHashKey
// @Accept json
// @Param request body emptypb.Empty true "Empty"
// @Success 200 {object} Card_CreateNewHashKey_Success
// @Failure 401 {object} Card_CreateNewHashKey_Failure "goerrpkg.ErrorTypeAuthorization"
// @Failure 404 {object} Card_CreateNewHashKey_Failure "goerrpkg.ErrorTypeNotFound"
// @Failure 400 {object} Card_CreateNewHashKey_Failure "goerrpkg.ErrorTypeBadRequest"
// @Failure 500 {object} Card_CreateNewHashKey_Failure "goerrpkg.ErrorTypeUnexpectedError"
// @Failure 400 {object} Card_CreateNewHashKey_Failure "gin.ErrorTypeBind"
// @Failure 500 {object} Card_CreateNewHashKey_Failure "Undefined error"
// @Produce json
// @Router /processing.card.card.Card/CreateNewHashKey [put]
// @tags Card
func (s *ginCardServer) CreateNewHashKey(c *gin.Context) error {
	ctx, span := dog.CreateSpan(c.Request.Context(), "GinCardServer_CreateNewHashKey")
	defer span.End()

	var request emptypb.Empty
	if err := c.ShouldBindJSON(&request); err != nil {
		return err
	}

	ctx = metadata.NewOutgoingContext(ctx, metadata.MD(c.Request.Header))

	response, err := s.client.CreateNewHashKey(ctx, &request)
	if err != nil {
		return err
	}

	c.JSON(http.StatusOK, &Card_CreateNewHashKey_Success{
		Status:     true,
		Message:    "Success",
		StatusCode: 0,
		Result:     response,
	})
	return nil
}

type Card_RotateHashKeys_Success struct {
	// Status is the status of the response. Set true
	Status bool `json:"status"`
	// Message is a human-readable explanation of the status
	Message string `json:"message"`
	// StatusCode is the GoErr status. Set 0
	StatusCode int `json:"status_code"`
	// Result is the result of the request
	Result *emptypb.Empty `json:"result"`
}

type Card_RotateHashKeys_Failure struct {
	// Status is the status of the response. Set false
	Status bool `json:"status"`
	// Message is a human-readable explanation of the status
	Message string `json:"message"`
	// StatusCode is the GoErr status
	StatusCode int `json:"status_code"`
}

// RotateHashKeys
// @Summary RotateHashKeys
// @Security bearerAuth
// @ID Card_RotateHashKeys
// @Accept json
// @Param request body emptypb.Empty true "Empty"
// @Success 200 {object} Card_RotateHashKeys_Success
// @Failure 401 {object} Card_RotateHashKeys_Failure "goerrpkg.ErrorTypeAuthorization"
// @Failure 404 {object} Card_RotateHashKeys_Failure "goerrpkg.ErrorTypeNotFound"
// @Failure 400 {object} Card_RotateHashKeys_Failure "goerrpkg.ErrorTypeBadRequest"
// @Failure 500 {object} Card_RotateHashKeys_Failure "goerrpkg.ErrorTypeUnexpectedError"
// @Failure 400 {object} Card_RotateHashKeys_Failure "gin.ErrorTypeBind"
// @Failure 500 {object} Card_RotateHashKeys_Failure "Undefined error"
// @Produce json
// @Router /processing.card.card.Card/RotateHashKeys [put]
// @tags Card
func (s *ginCardServer) RotateHashKeys(c *gin.Context) error {
	ctx, span := dog.CreateSpan(c.Request.Context(), "GinCardServer_RotateHashKeys")
	defer span.End()

	var request emptypb.Empty
	if err := c.ShouldBindJSON(&request); err != nil {
		return err
	}

	ctx = metadata.NewOutgoingContext(ctx, metadata.MD(c.Request.Header))

	response, err := s.client.RotateHashKeys(ctx, &request)
	if err != nil {
		return err
	}

	c.JSON(http.StatusOK, &Card_RotateHashKeys_Success{
		Status:     true,
		Message:    "Success",
		StatusCode: 0,
		Result:     response,
	})
	return nil
}
