package main

const (
	V2_PREPARED_CLIENTS      = "_clients.pb.go"
	V2_FAKE_FOLDER           = "fake"
	V2_FAKE_SUFFIX           = ".pb.go"
	V2_LOGGER_SUFFIX         = "_logger.pb.go"
	V2_IAM_FUNC_SUFFIX       = "_iam_func.pb.go"
	V2_IAM_SUFFIX            = "_iam.pb.go"
	V2_ENUM_EXTEND_SUFFIX    = "_enum_extend.pb.go"
	V2_ENUM_ERRORS_SUFFIX    = "_errors.pb.go"
	V2_ENUM_REFS_SUFFIX      = "_enum_refs.pb.go"
	V2_REST_SUFFIX           = "_rest.pb.go"
	V2_MOCKGEN_SUFFIX        = "_mockgen"
	V2_MOCKGEN_FULL_SUFFIX   = V2_MOCKGEN_SUFFIX + ".pb.go"
	V2_COBRA_SUFFIX          = "_cobra.pb.go"
	V2_REST_HELPERS_SUFFIX   = "_helpers_rest.pb.go"
	V2_REST_TEST_SUFFIX      = "_rest.pb_test.go"
	V2_EVENT_SUFFIX          = "_event.pb.go"
	V2GinSuffix              = ".gin.pb.go"
	V2_MESSAGE_EXTEND_SUFFIX = "_message_extend.pb.go"

	STAND_LOCAL       = "local"
	STAND_DEVELOPMENT = "dev"
	STAND_STAGE       = "stage"
	STAND_SANDBOX     = "sandbox"
	STAND_PRODUCTION  = "prod"
	STAND_TEST        = "test"

	MONGO_KEY          = "MONGO_DSN"
	POSTGRES_READ_KEY  = "DATABASE_READ_DSN"
	POSTGRES_WRITE_KEY = "DATABASE_WRITE_DSN"
	REDIS_PASSWORD_KEY = "REDIS_PASSWORD"
	REDIS_DSN_KEY      = "REDIS_DSN"
	LOG_LEVEL_KEY      = "LOG_LEVEL"
	JAEGER_DSN         = "JAEGER_DSN"
	SENTRY_DSN         = "SENTRY_DSN"
	NATS_DSN           = "NATS_DSN"
)

const (
	PrometheusEndPoint = "/actuator/prometheus"
	PingEndPoint       = "/ping"
	LivenessEndPoint   = "/liveness"
	ReadinessEndPoint  = "/readiness"

	TraceIdCTX = "traceId"
	UserIPCTX  = "user_ip"

	SentryFlushTimeout = "5 * time.Second"

	ConnectionsNum = 15

	openapiEndpoint = "http://processing-deployment-docs:3000/save-docs"
	clientTimeout   = "30 * time.Second"
)
