// Code generated by protoc-gen-go-mvp. DO NOT EDIT.
// versions:
// - protoc-gen-go-mvp v2.0.1
// created by mvp authors

package grpc

func (x EnumTransactionStatus) Code() string {
	switch x {
	case EnumTransactionStatus_Unknown:
		return "Unknown"
	case EnumTransactionStatus_TransactionStatusNew:
		return "new"
	case EnumTransactionStatus_TransactionStatusThreeDSWaiting:
		return "threeds_waiting"
	case EnumTransactionStatus_TransactionStatusProcessed:
		return "processed"
	case EnumTransactionStatus_TransactionStatusFailed:
		return "failed"
	case EnumTransactionStatus_TransactionStatusRefund:
		return "refund"
	case EnumTransactionStatus_TransactionStatusCanceled:
		return "canceled"
	case EnumTransactionStatus_TransactionStatusRetry:
		return "retry"
	case EnumTransactionStatus_TransactionStatusSuccess:
		return "success"
	case EnumTransactionStatus_TransactionStatusThreeDSReceived:
		return "threeds_received"
	case EnumTransactionStatus_TransactionStatusHolded:
		return "holded"
	case EnumTransactionStatus_TransactionStatusRefundWaiting:
		return "refund_waiting"
	case EnumTransactionStatus_TransactionStatusAuthorized:
		return "authorized"
	case EnumTransactionStatus_TransactionStatusError:
		return "error"
	case EnumTransactionStatus_TransactionStatusFingerPrint:
		return "fingerprint"
	default:
		return "error"
	}
}

func (x EnumTransactionStatus) IsFinal() bool {
	switch x {
	case EnumTransactionStatus_Unknown:
		return false
	case EnumTransactionStatus_TransactionStatusNew:
		return false
	case EnumTransactionStatus_TransactionStatusThreeDSWaiting:
		return false
	case EnumTransactionStatus_TransactionStatusProcessed:
		return false
	case EnumTransactionStatus_TransactionStatusFailed:
		return true
	case EnumTransactionStatus_TransactionStatusRefund:
		return true
	case EnumTransactionStatus_TransactionStatusCanceled:
		return true
	case EnumTransactionStatus_TransactionStatusRetry:
		return false
	case EnumTransactionStatus_TransactionStatusSuccess:
		return true
	case EnumTransactionStatus_TransactionStatusThreeDSReceived:
		return false
	case EnumTransactionStatus_TransactionStatusHolded:
		return false
	case EnumTransactionStatus_TransactionStatusRefundWaiting:
		return false
	case EnumTransactionStatus_TransactionStatusAuthorized:
		return true
	case EnumTransactionStatus_TransactionStatusError:
		return false
	case EnumTransactionStatus_TransactionStatusFingerPrint:
		return false
	default:
		return false
	}
}

func (x EnumTransactionStatus) Name() string {
	switch x {
	case EnumTransactionStatus_Unknown:
		return "Unknown"
	case EnumTransactionStatus_TransactionStatusNew:
		return "Транзакция создана"
	case EnumTransactionStatus_TransactionStatusThreeDSWaiting:
		return "Транзакция ожидает ответа от 3ds сервера"
	case EnumTransactionStatus_TransactionStatusProcessed:
		return "Транзакция в работе"
	case EnumTransactionStatus_TransactionStatusFailed:
		return "Транзакция прошла неуспешно"
	case EnumTransactionStatus_TransactionStatusRefund:
		return "Транзакция возвращена"
	case EnumTransactionStatus_TransactionStatusCanceled:
		return "Транзакция отменена"
	case EnumTransactionStatus_TransactionStatusRetry:
		return "Транзакция в статусе повтор"
	case EnumTransactionStatus_TransactionStatusSuccess:
		return "Транзакция прошла успешно"
	case EnumTransactionStatus_TransactionStatusThreeDSReceived:
		return "Транзакция получила ответ от 3ds сервера"
	case EnumTransactionStatus_TransactionStatusHolded:
		return "Транзакция в статусе ожидания"
	case EnumTransactionStatus_TransactionStatusRefundWaiting:
		return "Транзакция в статусе ожидания возврата"
	case EnumTransactionStatus_TransactionStatusAuthorized:
		return "Транзакция авторизована"
	case EnumTransactionStatus_TransactionStatusError:
		return "Произошла ошибка при проведении транзакции"
	case EnumTransactionStatus_TransactionStatusFingerPrint:
		return "Ожидается проверка отпечатка браузера банком"
	default:
		return "Произошла ошибка при проведении транзакции"
	}
}

// Created reference to EnumTransactionStatus

//	|	EnumTransactionStatus                                 	|	Code              	|	IsFinal	|	Name                                                                                  	|
//	|	EnumTransactionStatus_Unknown                         	|	"Unknown"         	|	false  	|	"Unknown"                                                                             	|
//	|	EnumTransactionStatus_TransactionStatusNew            	|	"new"             	|	false  	|	"Транзакция создана"                                                                  	|
//	|	EnumTransactionStatus_TransactionStatusThreeDSWaiting 	|	"threeds_waiting" 	|	false  	|	"Транзакция ожидает ответа от 3ds сервера"                                            	|
//	|	EnumTransactionStatus_TransactionStatusProcessed      	|	"processed"       	|	false  	|	"Транзакция в работе"                                                                 	|
//	|	EnumTransactionStatus_TransactionStatusFailed         	|	"failed"          	|	true   	|	"Транзакция прошла неуспешно"                                                         	|
//	|	EnumTransactionStatus_TransactionStatusRefund         	|	"refund"          	|	true   	|	"Транзакция возвращена"                                                               	|
//	|	EnumTransactionStatus_TransactionStatusCanceled       	|	"canceled"        	|	true   	|	"Транзакция отменена"                                                                 	|
//	|	EnumTransactionStatus_TransactionStatusRetry          	|	"retry"           	|	false  	|	"Транзакция в статусе повтор"                                                         	|
//	|	EnumTransactionStatus_TransactionStatusSuccess        	|	"success"         	|	true   	|	"Транзакция прошла успешно"                                                           	|
//	|	EnumTransactionStatus_TransactionStatusThreeDSReceived	|	"threeds_received"	|	false  	|	"Транзакция получила ответ от 3ds сервера"                                            	|
//	|	EnumTransactionStatus_TransactionStatusHolded         	|	"holded"          	|	false  	|	"Транзакция в статусе ожидания"                                                       	|
//	|	EnumTransactionStatus_TransactionStatusRefundWaiting  	|	"refund_waiting"  	|	false  	|	"Транзакция в статусе ожидания возврата"                                              	|
//	|	EnumTransactionStatus_TransactionStatusAuthorized     	|	"authorized"      	|	true   	|	"Транзакция авторизована"                                                             	|
//	|	EnumTransactionStatus_TransactionStatusError          	|	"error"           	|	false  	|	"Произошла ошибка при проведении транзакции"                                          	|
//	|	EnumTransactionStatus_TransactionStatusFingerPrint    	|	"fingerprint"     	|	false  	|	"Ожидается проверка отпечатка браузера банком"                                        	|

var SliceEnumTransactionStatusRefs *sliceEnumTransactionStatusRefs

type sliceEnumTransactionStatusRefs struct{}

func (*sliceEnumTransactionStatusRefs) Code(slice ...EnumTransactionStatus) []string {
	var result []string
	for _, val := range slice {
		result = append(result, val.Code())
	}

	return result
}

func (*sliceEnumTransactionStatusRefs) IsFinal(slice ...EnumTransactionStatus) []bool {
	var result []bool
	for _, val := range slice {
		result = append(result, val.IsFinal())
	}

	return result
}

func (*sliceEnumTransactionStatusRefs) Name(slice ...EnumTransactionStatus) []string {
	var result []string
	for _, val := range slice {
		result = append(result, val.Name())
	}

	return result
}
