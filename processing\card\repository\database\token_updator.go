package database

import (
	"git.local/sensitive/innerpb/processing/goerr"
	"time"

	"golang.org/x/net/context"
	"gorm.io/gorm"

	goevents "git.local/sensitive/innerpb/processing/events"
	"git.local/sensitive/processing/card/model"
	"git.local/sensitive/sdk/dog"
)

var _ TokenUpdator = (*TokenUpdatorDB)(nil)

type TokenUpdatorDB struct {
	db *gorm.DB
}

func NewTokenUpdatorDB(db *gorm.DB) TokenUpdator {
	return &TokenUpdatorDB{
		db: db,
	}
}

func (t TokenUpdatorDB) Update(ctx context.Context, tokenId uint64, tokenRequest *goevents.SaveToken) (err error) {
	ctx, span := dog.CreateSpan(ctx, "TokenUpdatorDB_Update")
	defer span.End()

	if tokenRequest == nil {
		return goerr.ErrParseErrorBody.WithCtx(ctx)
	}

	result := t.db.
		Model(&model.Token{}).
		Where("id = ?", tokenId).
		Updates(&model.Token{
			AcquirerId: tokenRequest.AcquirerId,
			TerminalId: tokenRequest.TerminalId,
			CardId:     tokenRequest.CardId,
			Token:      tokenRequest.Token,
			ApprovedAt: time.Now(),
		})
	if result.Error != nil {
		return goerr.ErrDbUnexpected.WithErr(err).WithCtx(ctx)
	}

	if result.RowsAffected == 0 {
		return goerr.ErrTokenNotFound.WithErr(err).WithCtx(ctx)
	}

	return nil
}

func (t TokenUpdatorDB) DeactivateToken(ctx context.Context) (err error) {
	ctx, span := dog.CreateSpan(ctx, "TokenUpdatorDB_DeactivateToken")
	defer span.End()

	result := t.db.WithContext(ctx).
		Where("expired_at <= ?", time.Now()).
		Update("approved", false)
	if result.Error != nil {
		return goerr.ErrDbUnexpected.WithErr(err).WithCtx(ctx)
	}

	if result.RowsAffected == 0 {
		return goerr.ErrTokenNotFound.WithErr(err).WithCtx(ctx)
	}

	return nil
}
