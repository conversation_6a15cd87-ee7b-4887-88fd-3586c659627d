edition = "2023";

package processing.anti_fraud.collector;

option go_package = "git.local/sensitive/innerpb/processing/grpc";

import "google/protobuf/timestamp.proto";
import "google/protobuf/empty.proto";

service Collector{
  // CollectTransaction method is used to collect transaction information and send it to anti-fraud F.A.C.C.T system
  rpc CollectTransaction(CollectTransactionRequestV1) returns (CollectorEmptyResponse);

  // jobs
  rpc GetTransactionStatus(google.protobuf.Empty) returns (google.protobuf.Empty);
}

message CollectorEmptyResponse{}

message CollectTransactionRequestV1 {
  uint64 transaction_id = 1; /* ctid field in antifraud F.A.C.C.T system */
  uint64 merchant_id = 2; /* user.merchantID field in antifraud F.A.C.C.T system */
  string project_client_id = 3; /* user.userID field in antifraud F.A.C.C.T system */
  string phone = 4; /* user.phone field in antifraud F.A.C.C.T system */
  string email = 5; /* user.email field in antifraud F.A.C.C.T system */
  bytes hashed_pan = 6; /* pan.hash_card field in antifraud F.A.C.C.T system */
  string transaction_aggregated_type = 7; /* transaction.type field in antifraud F.A.C.C.T system */
  google.protobuf.Timestamp session_generated_at = 8; /* transaction.start_generating field in antifraud F.A.C.C.T system */
  google.protobuf.Timestamp session_started_at = 9; /* transaction.send_request field in antifraud F.A.C.C.T system */
  double transaction_amount = 10; /* sum.wn and sum.fn fields in antifraud F.A.C.C.T system */
  string transaction_currency = 11; /* currency field in antifraud F.A.C.C.T system */
  string transaction_type = 12; /* subtype field in antifraud F.A.C.C.T system */
}