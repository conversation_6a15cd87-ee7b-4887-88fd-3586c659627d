// Code generated by protoc-gen-go-mvp. DO NOT EDIT.
// versions:
// - protoc-gen-go-mvp v2.0.1
// created by mvp authors

package rest

import (
	context "context"
	json "encoding/json"
	fmt "fmt"
	testhelpers "git.local/sensitive/mvp/pkg/testhelpers"
	mxj "github.com/clbanning/mxj"
	require "github.com/stretchr/testify/require"
	http "net/http"
	httptest "net/http/httptest"
	testing "testing"
)

func Test_AlatauCityBankPayIn_ProcessPayIn(t *testing.T) {
	request := &AlatauCityProcessPayInRequest{
		Body: &AlatauCityPayInRequest{
			AMOUNT:                   testhelpers.Ptr("string_1"),
			BACKREF:                  testhelpers.Ptr("string_1"),
			CLIENT_ID:                testhelpers.Ptr("string_1"),
			CURRENCY:                 testhelpers.Ptr("string_1"),
			DESC:                     testhelpers.Ptr("string_1"),
			DESC_ORDER:               testhelpers.Ptr("string_1"),
			EMAIL:                    testhelpers.Ptr("string_1"),
			EXT_MPI_ECI:              testhelpers.Ptr("string_1"),
			INT_REF:                  testhelpers.Ptr("string_1"),
			LANGUAGE:                 testhelpers.Ptr("string_1"),
			MERCHANT:                 testhelpers.Ptr("string_1"),
			MERCH_PAYTO_TOKEN_ID:     testhelpers.Ptr("string_1"),
			MERCH_RN_ID:              testhelpers.Ptr("string_1"),
			MERCH_TOKEN_ID:           testhelpers.Ptr("string_1"),
			MERCH_TRAN_STATE:         testhelpers.Ptr("string_1"),
			MK_TOKEN:                 testhelpers.Ptr("string_1"),
			M_INFO:                   testhelpers.Ptr("string_1"),
			NAME:                     testhelpers.Ptr("string_1"),
			NONCE:                    testhelpers.Ptr("string_1"),
			ORDER:                    testhelpers.Ptr("string_1"),
			PAYMENT_TO:               testhelpers.Ptr("string_1"),
			P_SIGN:                   testhelpers.Ptr("string_1"),
			RECUR_EXP:                testhelpers.Ptr("string_1"),
			RECUR_FREQ:               testhelpers.Ptr("string_1"),
			RECUR_REF:                testhelpers.Ptr("string_1"),
			TAVV:                     testhelpers.Ptr("string_1"),
			TERMINAL:                 testhelpers.Ptr("string_1"),
			Ucaf_Authentication_Data: testhelpers.Ptr("string_1"),
			Ucaf_Flag:                testhelpers.Ptr("string_1"),
			WTYPE:                    testhelpers.Ptr("string_1"),
			CrdCvc:                   testhelpers.Ptr("string_1"),
			CrdExp:                   testhelpers.Ptr("string_1"),
			CrdPan:                   testhelpers.Ptr("string_1"),
			REF:                      testhelpers.Ptr("string_1"),
			MERCH_3D_TERM_URL:        testhelpers.Ptr("string_1"),
			MERCH_SCA:                testhelpers.Ptr("string_1"),
		},
	}

	response := &AlatauCityProcessPayInResponse{
		Response: testhelpers.Ptr("string_1"),
	}
	_ = response

	// define rest arguments
	ctx := context.TODO()

	t.Run("argument_and_request_checks_testing_stand_2", func(t *testing.T) {
		// define test server
		var testFunc func(w http.ResponseWriter, r *http.Request)
		server := httptest.NewServer(
			http.HandlerFunc(
				func(w http.ResponseWriter, r *http.Request) {
					testFunc(w, r)
				},
			),
		)
		defer server.Close()

		restAlatauCityBankPayInBaseURL["argument_and_request_checks_testing_stand_2"] = server.URL

		testFunc = func(w http.ResponseWriter, r *http.Request) {
			require.Equal(t, "/ecom/api", r.URL.Path)
			require.Equal(t, http.MethodPost, r.Method)

			require.NoError(t, r.ParseForm())

			require.Equal(t, "string_1", r.PostForm.Get("CLIENT_ID"))
			require.Equal(t, "string_1", r.PostForm.Get("MERCH_TOKEN_ID"))
			require.Equal(t, "string_1", r.PostForm.Get("crd_cvc"))
			require.Equal(t, "string_1", r.PostForm.Get("MERCH_3D_TERM_URL"))
			require.Equal(t, "string_1", r.PostForm.Get("EXT_MPI_ECI"))
			require.Equal(t, "string_1", r.PostForm.Get("PAYMENT_TO"))
			require.Equal(t, "string_1", r.PostForm.Get("P_SIGN"))
			require.Equal(t, "string_1", r.PostForm.Get("REF"))
			require.Equal(t, "string_1", r.PostForm.Get("ORDER"))
			require.Equal(t, "string_1", r.PostForm.Get("RECUR_REF"))
			require.Equal(t, "string_1", r.PostForm.Get("BACKREF"))
			require.Equal(t, "string_1", r.PostForm.Get("CURRENCY"))
			require.Equal(t, "string_1", r.PostForm.Get("MERCHANT"))
			require.Equal(t, "string_1", r.PostForm.Get("M_INFO"))
			require.Equal(t, "string_1", r.PostForm.Get("EMAIL"))
			require.Equal(t, "string_1", r.PostForm.Get("MERCH_RN_ID"))
			require.Equal(t, "string_1", r.PostForm.Get("RECUR_FREQ"))
			require.Equal(t, "string_1", r.PostForm.Get("crd_pan"))
			require.Equal(t, "string_1", r.PostForm.Get("TERMINAL"))
			require.Equal(t, "string_1", r.PostForm.Get("Ucaf_Flag"))
			require.Equal(t, "string_1", r.PostForm.Get("DESC_ORDER"))
			require.Equal(t, "string_1", r.PostForm.Get("MK_TOKEN"))
			require.Equal(t, "string_1", r.PostForm.Get("RECUR_EXP"))
			require.Equal(t, "string_1", r.PostForm.Get("TAVV"))
			require.Equal(t, "string_1", r.PostForm.Get("MERCH_SCA"))
			require.Equal(t, "string_1", r.PostForm.Get("AMOUNT"))
			require.Equal(t, "string_1", r.PostForm.Get("INT_REF"))
			require.Equal(t, "string_1", r.PostForm.Get("WTYPE"))
			require.Equal(t, "string_1", r.PostForm.Get("crd_exp"))
			require.Equal(t, "string_1", r.PostForm.Get("DESC"))
			require.Equal(t, "string_1", r.PostForm.Get("LANGUAGE"))
			require.Equal(t, "string_1", r.PostForm.Get("MERCH_TRAN_STATE"))
			require.Equal(t, "string_1", r.PostForm.Get("Ucaf_Authentication_Data"))
			require.Equal(t, "string_1", r.PostForm.Get("MERCH_PAYTO_TOKEN_ID"))
			require.Equal(t, "string_1", r.PostForm.Get("NAME"))
			require.Equal(t, "string_1", r.PostForm.Get("NONCE"))
		}

		// define new rest client
		testingRest := NewRestAlatauCityBankPayInServer("argument_and_request_checks_testing_stand_2", nil)
		testingRest.ProcessPayIn(
			ctx,
			request,
		)

	})

	// Native
	t.Run("check_field_parse_Response_testing_stand_3", func(t *testing.T) {
		writeBytes := []byte(fmt.Sprint(*response.Response))

		// define test server
		server := httptest.NewServer(
			http.HandlerFunc(
				func(w http.ResponseWriter, r *http.Request) {
					_, err := w.Write(writeBytes)
					require.NoError(t, err)
				},
			),
		)
		defer server.Close()

		restAlatauCityBankPayInBaseURL["check_field_parse_Response_testing_stand_3"] = server.URL
		testingRest := NewRestAlatauCityBankPayInServer("check_field_parse_Response_testing_stand_3", nil)
		result, err := testingRest.ProcessPayIn(
			ctx,
			request,
		)
		require.NoError(t, err)
		require.NotNil(t, result)
		require.Equal(t, File_inner_processing_rest_alatau_city_payin_proto_AlatauCityBankPayIn_ProcessPayIn_Response, result.ResponseOutput)
		require.Equal(t, response.Response, result.Response.Response)

	})
}

func Test_AlatauCityBankPayIn_ProcessNoAcceptPay(t *testing.T) {
	request := &AlatauCityProcessNoAcceptPayRequest{
		Body: &AlatauCityPayInRequest{
			AMOUNT:                   testhelpers.Ptr("string_2"),
			BACKREF:                  testhelpers.Ptr("string_2"),
			CLIENT_ID:                testhelpers.Ptr("string_2"),
			CURRENCY:                 testhelpers.Ptr("string_2"),
			DESC:                     testhelpers.Ptr("string_2"),
			DESC_ORDER:               testhelpers.Ptr("string_2"),
			EMAIL:                    testhelpers.Ptr("string_2"),
			EXT_MPI_ECI:              testhelpers.Ptr("string_2"),
			INT_REF:                  testhelpers.Ptr("string_2"),
			LANGUAGE:                 testhelpers.Ptr("string_2"),
			MERCHANT:                 testhelpers.Ptr("string_2"),
			MERCH_PAYTO_TOKEN_ID:     testhelpers.Ptr("string_2"),
			MERCH_RN_ID:              testhelpers.Ptr("string_2"),
			MERCH_TOKEN_ID:           testhelpers.Ptr("string_2"),
			MERCH_TRAN_STATE:         testhelpers.Ptr("string_2"),
			MK_TOKEN:                 testhelpers.Ptr("string_2"),
			M_INFO:                   testhelpers.Ptr("string_2"),
			NAME:                     testhelpers.Ptr("string_2"),
			NONCE:                    testhelpers.Ptr("string_2"),
			ORDER:                    testhelpers.Ptr("string_2"),
			PAYMENT_TO:               testhelpers.Ptr("string_2"),
			P_SIGN:                   testhelpers.Ptr("string_2"),
			RECUR_EXP:                testhelpers.Ptr("string_2"),
			RECUR_FREQ:               testhelpers.Ptr("string_2"),
			RECUR_REF:                testhelpers.Ptr("string_2"),
			TAVV:                     testhelpers.Ptr("string_2"),
			TERMINAL:                 testhelpers.Ptr("string_2"),
			Ucaf_Authentication_Data: testhelpers.Ptr("string_2"),
			Ucaf_Flag:                testhelpers.Ptr("string_2"),
			WTYPE:                    testhelpers.Ptr("string_2"),
			CrdCvc:                   testhelpers.Ptr("string_2"),
			CrdExp:                   testhelpers.Ptr("string_2"),
			CrdPan:                   testhelpers.Ptr("string_2"),
			REF:                      testhelpers.Ptr("string_2"),
			MERCH_3D_TERM_URL:        testhelpers.Ptr("string_2"),
			MERCH_SCA:                testhelpers.Ptr("string_2"),
		},
	}

	response := &AlatauCityProcessNoAcceptPayResponse{
		Response: testhelpers.Ptr("string_2"),
	}
	_ = response

	// define rest arguments
	ctx := context.TODO()

	t.Run("argument_and_request_checks_testing_stand_4", func(t *testing.T) {
		// define test server
		var testFunc func(w http.ResponseWriter, r *http.Request)
		server := httptest.NewServer(
			http.HandlerFunc(
				func(w http.ResponseWriter, r *http.Request) {
					testFunc(w, r)
				},
			),
		)
		defer server.Close()

		restAlatauCityBankPayInBaseURL["argument_and_request_checks_testing_stand_4"] = server.URL

		testFunc = func(w http.ResponseWriter, r *http.Request) {
			require.Equal(t, "/ecom/api", r.URL.Path)
			require.Equal(t, http.MethodPost, r.Method)

			require.NoError(t, r.ParseForm())

			require.Equal(t, "string_2", r.PostForm.Get("NAME"))
			require.Equal(t, "string_2", r.PostForm.Get("Ucaf_Authentication_Data"))
			require.Equal(t, "string_2", r.PostForm.Get("MK_TOKEN"))
			require.Equal(t, "string_2", r.PostForm.Get("TAVV"))
			require.Equal(t, "string_2", r.PostForm.Get("BACKREF"))
			require.Equal(t, "string_2", r.PostForm.Get("DESC"))
			require.Equal(t, "string_2", r.PostForm.Get("MERCH_TOKEN_ID"))
			require.Equal(t, "string_2", r.PostForm.Get("RECUR_REF"))
			require.Equal(t, "string_2", r.PostForm.Get("crd_cvc"))
			require.Equal(t, "string_2", r.PostForm.Get("EMAIL"))
			require.Equal(t, "string_2", r.PostForm.Get("RECUR_EXP"))
			require.Equal(t, "string_2", r.PostForm.Get("RECUR_FREQ"))
			require.Equal(t, "string_2", r.PostForm.Get("TERMINAL"))
			require.Equal(t, "string_2", r.PostForm.Get("REF"))
			require.Equal(t, "string_2", r.PostForm.Get("MERCH_3D_TERM_URL"))
			require.Equal(t, "string_2", r.PostForm.Get("DESC_ORDER"))
			require.Equal(t, "string_2", r.PostForm.Get("MERCHANT"))
			require.Equal(t, "string_2", r.PostForm.Get("MERCH_RN_ID"))
			require.Equal(t, "string_2", r.PostForm.Get("CURRENCY"))
			require.Equal(t, "string_2", r.PostForm.Get("P_SIGN"))
			require.Equal(t, "string_2", r.PostForm.Get("crd_exp"))
			require.Equal(t, "string_2", r.PostForm.Get("ORDER"))
			require.Equal(t, "string_2", r.PostForm.Get("MERCH_SCA"))
			require.Equal(t, "string_2", r.PostForm.Get("MERCH_PAYTO_TOKEN_ID"))
			require.Equal(t, "string_2", r.PostForm.Get("MERCH_TRAN_STATE"))
			require.Equal(t, "string_2", r.PostForm.Get("NONCE"))
			require.Equal(t, "string_2", r.PostForm.Get("crd_pan"))
			require.Equal(t, "string_2", r.PostForm.Get("AMOUNT"))
			require.Equal(t, "string_2", r.PostForm.Get("EXT_MPI_ECI"))
			require.Equal(t, "string_2", r.PostForm.Get("INT_REF"))
			require.Equal(t, "string_2", r.PostForm.Get("PAYMENT_TO"))
			require.Equal(t, "string_2", r.PostForm.Get("Ucaf_Flag"))
			require.Equal(t, "string_2", r.PostForm.Get("WTYPE"))
			require.Equal(t, "string_2", r.PostForm.Get("CLIENT_ID"))
			require.Equal(t, "string_2", r.PostForm.Get("LANGUAGE"))
			require.Equal(t, "string_2", r.PostForm.Get("M_INFO"))
		}

		// define new rest client
		testingRest := NewRestAlatauCityBankPayInServer("argument_and_request_checks_testing_stand_4", nil)
		testingRest.ProcessNoAcceptPay(
			ctx,
			request,
		)

	})

	// Native
	t.Run("check_field_parse_Response_testing_stand_5", func(t *testing.T) {
		writeBytes := []byte(fmt.Sprint(*response.Response))

		// define test server
		server := httptest.NewServer(
			http.HandlerFunc(
				func(w http.ResponseWriter, r *http.Request) {
					_, err := w.Write(writeBytes)
					require.NoError(t, err)
				},
			),
		)
		defer server.Close()

		restAlatauCityBankPayInBaseURL["check_field_parse_Response_testing_stand_5"] = server.URL
		testingRest := NewRestAlatauCityBankPayInServer("check_field_parse_Response_testing_stand_5", nil)
		result, err := testingRest.ProcessNoAcceptPay(
			ctx,
			request,
		)
		require.NoError(t, err)
		require.NotNil(t, result)
		require.Equal(t, File_inner_processing_rest_alatau_city_payin_proto_AlatauCityBankPayIn_ProcessNoAcceptPay_Response, result.ResponseOutput)
		require.Equal(t, response.Response, result.Response.Response)

	})
}

func Test_AlatauCityBankPayIn_GetStatus(t *testing.T) {
	request := &AlatauCityGetStatusPayInRequest{
		Body: &AlatauCityGetStatusPayInRequest_GetStatusRequest{
			ORDER:     testhelpers.Ptr("string_3"),
			MERCHANT:  testhelpers.Ptr("string_3"),
			GETSTATUS: testhelpers.Ptr("string_1"),
			P_SIGN:    testhelpers.Ptr("string_3"),
		},
	}

	response := &AlatauCityGetStatusPayInResponse{
		StatusResponse: &AlatauCityGetStatusPayInResponse_StatusResponse{
			Result: &AlatauCityGetStatusPayInResponse_Result{
				Code:        testhelpers.Ptr("string_1"),
				Description: testhelpers.Ptr("string_1"),
				Operation: &AlatauCityGetStatusPayInResponse_Operation{
					Status:       testhelpers.Ptr("string_1"),
					StatusDesc:   testhelpers.Ptr("string_1"),
					Amount:       testhelpers.Ptr("string_1"),
					Currency:     testhelpers.Ptr("string_1"),
					Description:  testhelpers.Ptr("string_2"),
					DescOrder:    testhelpers.Ptr("string_1"),
					Email:        testhelpers.Ptr("string_1"),
					Lang:         testhelpers.Ptr("string_1"),
					MpiOrder:     testhelpers.Ptr("string_1"),
					Terminal:     testhelpers.Ptr("string_1"),
					Phone:        testhelpers.Ptr("string_1"),
					CardMasked:   testhelpers.Ptr("string_1"),
					CardName:     testhelpers.Ptr("string_1"),
					CardExpdt:    testhelpers.Ptr("string_1"),
					CardToken:    testhelpers.Ptr("string_1"),
					CreateDate:   testhelpers.Ptr("string_1"),
					Result:       testhelpers.Ptr("string_1"),
					ResultDesc:   testhelpers.Ptr("string_1"),
					Rc:           testhelpers.Ptr("string_1"),
					Rrn:          testhelpers.Ptr("string_1"),
					AuthCode:     testhelpers.Ptr("string_1"),
					InvId:        testhelpers.Ptr("string_1"),
					IntExpDate:   testhelpers.Ptr("string_1"),
					RevMaxAmount: testhelpers.Ptr("string_1"),
					RecurFreq:    testhelpers.Ptr("string_1"),
					RecurRef:     testhelpers.Ptr("string_1"),
					RecurIntRef:  testhelpers.Ptr("string_1"),
					ClientId:     testhelpers.Ptr("string_1"),
					CardToMasked: testhelpers.Ptr("string_1"),
					CardToToken:  testhelpers.Ptr("string_1"),
					Refunds: &AlatauCityGetStatusPayInResponse_Operation_Refunds{
						Rec: []*AlatauCityGetStatusPayInResponse_Operation_Rec{
							&AlatauCityGetStatusPayInResponse_Operation_Rec{
								Status:         testhelpers.Ptr("string_2"),
								StatusDesc:     testhelpers.Ptr("string_2"),
								RevRc:          testhelpers.Ptr("string_1"),
								RevAmount:      testhelpers.Ptr("string_1"),
								RevDescription: testhelpers.Ptr("string_1"),
								RevError:       testhelpers.Ptr("string_1"),
								RevDate:        testhelpers.Ptr("string_1"),
							},
							&AlatauCityGetStatusPayInResponse_Operation_Rec{
								Status:         testhelpers.Ptr("string_3"),
								StatusDesc:     testhelpers.Ptr("string_3"),
								RevRc:          testhelpers.Ptr("string_2"),
								RevAmount:      testhelpers.Ptr("string_2"),
								RevDescription: testhelpers.Ptr("string_2"),
								RevError:       testhelpers.Ptr("string_2"),
								RevDate:        testhelpers.Ptr("string_2"),
							},
							&AlatauCityGetStatusPayInResponse_Operation_Rec{
								Status:         testhelpers.Ptr("string_4"),
								StatusDesc:     testhelpers.Ptr("string_4"),
								RevRc:          testhelpers.Ptr("string_3"),
								RevAmount:      testhelpers.Ptr("string_3"),
								RevDescription: testhelpers.Ptr("string_3"),
								RevError:       testhelpers.Ptr("string_3"),
								RevDate:        testhelpers.Ptr("string_3"),
							},
						},
					},
				},
			},
		},
	}
	_ = response

	// define rest arguments
	ctx := context.TODO()

	t.Run("argument_and_request_checks_testing_stand_6", func(t *testing.T) {
		// define test server
		var testFunc func(w http.ResponseWriter, r *http.Request)
		server := httptest.NewServer(
			http.HandlerFunc(
				func(w http.ResponseWriter, r *http.Request) {
					testFunc(w, r)
				},
			),
		)
		defer server.Close()

		restAlatauCityBankPayInBaseURL["argument_and_request_checks_testing_stand_6"] = server.URL

		testFunc = func(w http.ResponseWriter, r *http.Request) {
			require.Equal(t, "/ecom/api", r.URL.Path)
			require.Equal(t, http.MethodPost, r.Method)

			require.NoError(t, r.ParseForm())

			require.Equal(t, "string_3", r.PostForm.Get("ORDER"))
			require.Equal(t, "string_3", r.PostForm.Get("MERCHANT"))
			require.Equal(t, "string_1", r.PostForm.Get("GETSTATUS"))
			require.Equal(t, "string_3", r.PostForm.Get("P_SIGN"))
		}

		// define new rest client
		testingRest := NewRestAlatauCityBankPayInServer("argument_and_request_checks_testing_stand_6", nil)
		testingRest.GetStatus(
			ctx,
			request,
		)

	})

	// XML
	t.Run("check_field_parse_StatusResponse_testing_stand_7", func(t *testing.T) {
		jsonByteSlice, err := json.Marshal(response.StatusResponse)
		require.NoError(t, err)

		mxjMap, err := mxj.NewMapJson(jsonByteSlice)
		require.NoError(t, err)

		writeBytes, err := mxjMap.Xml()
		require.NoError(t, err)

		// define test server
		server := httptest.NewServer(
			http.HandlerFunc(
				func(w http.ResponseWriter, r *http.Request) {
					_, err := w.Write(writeBytes)
					require.NoError(t, err)
				},
			),
		)
		defer server.Close()

		restAlatauCityBankPayInBaseURL["check_field_parse_StatusResponse_testing_stand_7"] = server.URL
		testingRest := NewRestAlatauCityBankPayInServer("check_field_parse_StatusResponse_testing_stand_7", nil)
		result, err := testingRest.GetStatus(
			ctx,
			request,
		)
		require.NoError(t, err)
		require.NotNil(t, result)
		require.Equal(t, File_inner_processing_rest_alatau_city_payin_proto_AlatauCityBankPayIn_GetStatus_StatusResponse, result.ResponseOutput)
		resultJsonByteSlice, err := json.Marshal(result.Response.StatusResponse)
		require.NoError(t, err)

		resultMxjMap, err := mxj.NewMapJson(resultJsonByteSlice)
		require.NoError(t, err)

		resultBytes, err := resultMxjMap.Xml()
		require.NoError(t, err)

		require.Equal(t, writeBytes, resultBytes)

	})
}

func Test_AlatauCityBankPayIn_ConfirmThreeDS(t *testing.T) {
	request := &AlatauCityConfirmThreeDSRequest{
		Body: &AlatauCityConfirmThreeDSRequest_ConfirmThreeDSRequest{
			Cres:               testhelpers.Ptr("string_1"),
			ThreeDSSessionData: testhelpers.Ptr("string_1"),
		},
	}

	response := &AlatauCityConfirmThreeDSResponse{
		Response: testhelpers.Ptr("string_3"),
	}
	_ = response

	// define rest arguments
	ctx := context.TODO()

	t.Run("argument_and_request_checks_testing_stand_8", func(t *testing.T) {
		// define test server
		var testFunc func(w http.ResponseWriter, r *http.Request)
		server := httptest.NewServer(
			http.HandlerFunc(
				func(w http.ResponseWriter, r *http.Request) {
					testFunc(w, r)
				},
			),
		)
		defer server.Close()

		restAlatauCityBankPayInBaseURL["argument_and_request_checks_testing_stand_8"] = server.URL

		testFunc = func(w http.ResponseWriter, r *http.Request) {
			require.Equal(t, "/", r.URL.Path)
			require.Equal(t, http.MethodPost, r.Method)

			require.NoError(t, r.ParseForm())

			require.Equal(t, "string_1", r.PostForm.Get("cres"))
			require.Equal(t, "string_1", r.PostForm.Get("threeDSSessionData"))
		}

		// define new rest client
		testingRest := NewRestAlatauCityBankPayInServer("argument_and_request_checks_testing_stand_8", nil)
		testingRest.ConfirmThreeDS(
			ctx,
			request,
		)

	})

	// Native
	t.Run("check_field_parse_Response_testing_stand_9", func(t *testing.T) {
		writeBytes := []byte(fmt.Sprint(*response.Response))

		// define test server
		server := httptest.NewServer(
			http.HandlerFunc(
				func(w http.ResponseWriter, r *http.Request) {
					_, err := w.Write(writeBytes)
					require.NoError(t, err)
				},
			),
		)
		defer server.Close()

		restAlatauCityBankPayInBaseURL["check_field_parse_Response_testing_stand_9"] = server.URL
		testingRest := NewRestAlatauCityBankPayInServer("check_field_parse_Response_testing_stand_9", nil)
		result, err := testingRest.ConfirmThreeDS(
			ctx,
			request,
		)
		require.NoError(t, err)
		require.NotNil(t, result)
		require.Equal(t, File_inner_processing_rest_alatau_city_payin_proto_AlatauCityBankPayIn_ConfirmThreeDS_Response, result.ResponseOutput)
		require.Equal(t, response.Response, result.Response.Response)

	})
}

func Test_AlatauCityBankPayIn_ResumeThreeDS(t *testing.T) {
	request := &AlatauCityResumeThreeDSRequest{
		Body: &AlatauCityResumeThreeDSRequest_ResumeThreeDSRequest{
			ThreeDSMethodData:  testhelpers.Ptr("string_1"),
			ThreeDSMethodState: testhelpers.Ptr("string_1"),
		},
	}

	response := &AlatauCityResumeThreeDSResponse{
		Response: testhelpers.Ptr("string_4"),
	}
	_ = response

	// define rest arguments
	ctx := context.TODO()

	t.Run("argument_and_request_checks_testing_stand_10", func(t *testing.T) {
		// define test server
		var testFunc func(w http.ResponseWriter, r *http.Request)
		server := httptest.NewServer(
			http.HandlerFunc(
				func(w http.ResponseWriter, r *http.Request) {
					testFunc(w, r)
				},
			),
		)
		defer server.Close()

		restAlatauCityBankPayInBaseURL["argument_and_request_checks_testing_stand_10"] = server.URL

		testFunc = func(w http.ResponseWriter, r *http.Request) {
			require.Equal(t, "/", r.URL.Path)
			require.Equal(t, http.MethodPost, r.Method)

			require.NoError(t, r.ParseForm())

			require.Equal(t, "string_1", r.PostForm.Get("threeDSMethodData"))
			require.Equal(t, "string_1", r.PostForm.Get("threeDSMethodState"))
		}

		// define new rest client
		testingRest := NewRestAlatauCityBankPayInServer("argument_and_request_checks_testing_stand_10", nil)
		testingRest.ResumeThreeDS(
			ctx,
			request,
		)

	})

	// Native
	t.Run("check_field_parse_Response_testing_stand_11", func(t *testing.T) {
		writeBytes := []byte(fmt.Sprint(*response.Response))

		// define test server
		server := httptest.NewServer(
			http.HandlerFunc(
				func(w http.ResponseWriter, r *http.Request) {
					_, err := w.Write(writeBytes)
					require.NoError(t, err)
				},
			),
		)
		defer server.Close()

		restAlatauCityBankPayInBaseURL["check_field_parse_Response_testing_stand_11"] = server.URL
		testingRest := NewRestAlatauCityBankPayInServer("check_field_parse_Response_testing_stand_11", nil)
		result, err := testingRest.ResumeThreeDS(
			ctx,
			request,
		)
		require.NoError(t, err)
		require.NotNil(t, result)
		require.Equal(t, File_inner_processing_rest_alatau_city_payin_proto_AlatauCityBankPayIn_ResumeThreeDS_Response, result.ResponseOutput)
		require.Equal(t, response.Response, result.Response.Response)

	})
}

func Test_AlatauCityBankPayIn_SendForm(t *testing.T) {
	request := &AlatauCitySendFormRequest{
		Body: &AlatauCitySendFormRequest_SendFormRequest{
			ThreeDSMethodData: testhelpers.Ptr("string_2"),
		},
	}

	response := &AlatauCitySendFormResponse{
		Response: testhelpers.Ptr("string_5"),
	}
	_ = response

	// define rest arguments
	ctx := context.TODO()

	t.Run("argument_and_request_checks_testing_stand_12", func(t *testing.T) {
		// define test server
		var testFunc func(w http.ResponseWriter, r *http.Request)
		server := httptest.NewServer(
			http.HandlerFunc(
				func(w http.ResponseWriter, r *http.Request) {
					testFunc(w, r)
				},
			),
		)
		defer server.Close()

		restAlatauCityBankPayInBaseURL["argument_and_request_checks_testing_stand_12"] = server.URL

		testFunc = func(w http.ResponseWriter, r *http.Request) {
			require.Equal(t, "/", r.URL.Path)
			require.Equal(t, http.MethodPost, r.Method)

			require.NoError(t, r.ParseForm())

			require.Equal(t, "string_2", r.PostForm.Get("threeDSMethodData"))
		}

		// define new rest client
		testingRest := NewRestAlatauCityBankPayInServer("argument_and_request_checks_testing_stand_12", nil)
		testingRest.SendForm(
			ctx,
			request,
		)

	})

	// Native
	t.Run("check_field_parse_Response_testing_stand_13", func(t *testing.T) {
		writeBytes := []byte(fmt.Sprint(*response.Response))

		// define test server
		server := httptest.NewServer(
			http.HandlerFunc(
				func(w http.ResponseWriter, r *http.Request) {
					_, err := w.Write(writeBytes)
					require.NoError(t, err)
				},
			),
		)
		defer server.Close()

		restAlatauCityBankPayInBaseURL["check_field_parse_Response_testing_stand_13"] = server.URL
		testingRest := NewRestAlatauCityBankPayInServer("check_field_parse_Response_testing_stand_13", nil)
		result, err := testingRest.SendForm(
			ctx,
			request,
		)
		require.NoError(t, err)
		require.NotNil(t, result)
		require.Equal(t, File_inner_processing_rest_alatau_city_payin_proto_AlatauCityBankPayIn_SendForm_Response, result.ResponseOutput)
		require.Equal(t, response.Response, result.Response.Response)

	})
}

func Test_AlatauCityBankPayIn_CancelPayment(t *testing.T) {
	request := &AlatauCityCancelPaymentRequest{
		Body: &AlatauCityCancelPaymentRequest_CancelPaymentRequest{
			ORDER:      testhelpers.Ptr("string_4"),
			MERCHANT:   testhelpers.Ptr("string_4"),
			P_SIGN:     testhelpers.Ptr("string_4"),
			REV_AMOUNT: testhelpers.Ptr("string_1"),
			REV_DESC:   testhelpers.Ptr("string_1"),
		},
	}

	response := &AlatauCityCancelPaymentResponse{
		CancelResponse: &AlatauCityCancelPaymentResponse_CancelPaymentResponse{
			Result: &AlatauCityCancelPaymentResponse_Result{
				Code:        testhelpers.Ptr("string_2"),
				Description: testhelpers.Ptr("string_3"),
				Operation: &AlatauCityCancelPaymentResponse_Operation{
					Status:     testhelpers.Ptr("string_5"),
					ResultDesc: testhelpers.Ptr("string_2"),
					Result:     testhelpers.Ptr("string_2"),
					Rc:         testhelpers.Ptr("string_2"),
					Ecode:      testhelpers.Ptr("string_1"),
					Edesc:      testhelpers.Ptr("string_1"),
					Amount:     testhelpers.Ptr("string_2"),
					Rrn:        testhelpers.Ptr("string_2"),
					RevDesc:    testhelpers.Ptr("string_1"),
					RevDate:    testhelpers.Ptr("string_4"),
				},
			},
		},
	}
	_ = response

	// define rest arguments
	ctx := context.TODO()

	t.Run("argument_and_request_checks_testing_stand_14", func(t *testing.T) {
		// define test server
		var testFunc func(w http.ResponseWriter, r *http.Request)
		server := httptest.NewServer(
			http.HandlerFunc(
				func(w http.ResponseWriter, r *http.Request) {
					testFunc(w, r)
				},
			),
		)
		defer server.Close()

		restAlatauCityBankPayInBaseURL["argument_and_request_checks_testing_stand_14"] = server.URL

		testFunc = func(w http.ResponseWriter, r *http.Request) {
			require.Equal(t, "/ecom/api", r.URL.Path)
			require.Equal(t, http.MethodPost, r.Method)

			require.NoError(t, r.ParseForm())

			require.Equal(t, "string_4", r.PostForm.Get("MERCHANT"))
			require.Equal(t, "string_4", r.PostForm.Get("P_SIGN"))
			require.Equal(t, "string_1", r.PostForm.Get("REV_AMOUNT"))
			require.Equal(t, "string_1", r.PostForm.Get("REV_DESC"))
			require.Equal(t, "string_4", r.PostForm.Get("ORDER"))
		}

		// define new rest client
		testingRest := NewRestAlatauCityBankPayInServer("argument_and_request_checks_testing_stand_14", nil)
		testingRest.CancelPayment(
			ctx,
			request,
		)

	})

	// XML
	t.Run("check_field_parse_CancelResponse_testing_stand_15", func(t *testing.T) {
		jsonByteSlice, err := json.Marshal(response.CancelResponse)
		require.NoError(t, err)

		mxjMap, err := mxj.NewMapJson(jsonByteSlice)
		require.NoError(t, err)

		writeBytes, err := mxjMap.Xml()
		require.NoError(t, err)

		// define test server
		server := httptest.NewServer(
			http.HandlerFunc(
				func(w http.ResponseWriter, r *http.Request) {
					_, err := w.Write(writeBytes)
					require.NoError(t, err)
				},
			),
		)
		defer server.Close()

		restAlatauCityBankPayInBaseURL["check_field_parse_CancelResponse_testing_stand_15"] = server.URL
		testingRest := NewRestAlatauCityBankPayInServer("check_field_parse_CancelResponse_testing_stand_15", nil)
		result, err := testingRest.CancelPayment(
			ctx,
			request,
		)
		require.NoError(t, err)
		require.NotNil(t, result)
		require.Equal(t, File_inner_processing_rest_alatau_city_payin_proto_AlatauCityBankPayIn_CancelPayment_CancelResponse, result.ResponseOutput)
		resultJsonByteSlice, err := json.Marshal(result.Response.CancelResponse)
		require.NoError(t, err)

		resultMxjMap, err := mxj.NewMapJson(resultJsonByteSlice)
		require.NoError(t, err)

		resultBytes, err := resultMxjMap.Xml()
		require.NoError(t, err)

		require.Equal(t, writeBytes, resultBytes)

	})
}

func Test_AlatauCityBankPayIn_RegisterToken(t *testing.T) {
	request := &AlatauCityRegisterTokenRequest{
		Body: &AlatauCityRegisterTokenRequest_RegisterTokenRequest{
			ORDER:     testhelpers.Ptr("string_5"),
			MERCHANT:  testhelpers.Ptr("string_5"),
			TERMINAL:  testhelpers.Ptr("string_3"),
			CLIENT_ID: testhelpers.Ptr("string_3"),
			CrdPan:    testhelpers.Ptr("string_3"),
			CrdExp:    testhelpers.Ptr("string_3"),
			CrdCvc:    testhelpers.Ptr("string_3"),
			NAME:      testhelpers.Ptr("string_3"),
			P_SIGN:    testhelpers.Ptr("string_5"),
			TOKEN_CMD: testhelpers.Ptr("string_1"),
		},
	}

	response := &AlatauCityRegisterTokenResponse{
		TokenResponse: &AlatauCityRegisterTokenResponse_RegisterTokenResponse{
			Result: &AlatauCityRegisterTokenResponse_Result{
				Code:        testhelpers.Ptr("string_3"),
				Description: testhelpers.Ptr("string_4"),
				Token:       testhelpers.Ptr("string_1"),
			},
		},
	}
	_ = response

	// define rest arguments
	ctx := context.TODO()

	t.Run("argument_and_request_checks_testing_stand_16", func(t *testing.T) {
		// define test server
		var testFunc func(w http.ResponseWriter, r *http.Request)
		server := httptest.NewServer(
			http.HandlerFunc(
				func(w http.ResponseWriter, r *http.Request) {
					testFunc(w, r)
				},
			),
		)
		defer server.Close()

		restAlatauCityBankPayInBaseURL["argument_and_request_checks_testing_stand_16"] = server.URL

		testFunc = func(w http.ResponseWriter, r *http.Request) {
			require.Equal(t, "/ecom/api", r.URL.Path)
			require.Equal(t, http.MethodPost, r.Method)

			require.NoError(t, r.ParseForm())

			require.Equal(t, "string_5", r.PostForm.Get("P_SIGN"))
			require.Equal(t, "string_1", r.PostForm.Get("TOKEN_CMD"))
			require.Equal(t, "string_3", r.PostForm.Get("CLIENT_ID"))
			require.Equal(t, "string_3", r.PostForm.Get("crd_exp"))
			require.Equal(t, "string_3", r.PostForm.Get("crd_cvc"))
			require.Equal(t, "string_3", r.PostForm.Get("crd_pan"))
			require.Equal(t, "string_3", r.PostForm.Get("NAME"))
			require.Equal(t, "string_5", r.PostForm.Get("ORDER"))
			require.Equal(t, "string_5", r.PostForm.Get("MERCHANT"))
			require.Equal(t, "string_3", r.PostForm.Get("TERMINAL"))
		}

		// define new rest client
		testingRest := NewRestAlatauCityBankPayInServer("argument_and_request_checks_testing_stand_16", nil)
		testingRest.RegisterToken(
			ctx,
			request,
		)

	})

	// XML
	t.Run("check_field_parse_TokenResponse_testing_stand_17", func(t *testing.T) {
		jsonByteSlice, err := json.Marshal(response.TokenResponse)
		require.NoError(t, err)

		mxjMap, err := mxj.NewMapJson(jsonByteSlice)
		require.NoError(t, err)

		writeBytes, err := mxjMap.Xml()
		require.NoError(t, err)

		// define test server
		server := httptest.NewServer(
			http.HandlerFunc(
				func(w http.ResponseWriter, r *http.Request) {
					_, err := w.Write(writeBytes)
					require.NoError(t, err)
				},
			),
		)
		defer server.Close()

		restAlatauCityBankPayInBaseURL["check_field_parse_TokenResponse_testing_stand_17"] = server.URL
		testingRest := NewRestAlatauCityBankPayInServer("check_field_parse_TokenResponse_testing_stand_17", nil)
		result, err := testingRest.RegisterToken(
			ctx,
			request,
		)
		require.NoError(t, err)
		require.NotNil(t, result)
		require.Equal(t, File_inner_processing_rest_alatau_city_payin_proto_AlatauCityBankPayIn_RegisterToken_TokenResponse, result.ResponseOutput)
		resultJsonByteSlice, err := json.Marshal(result.Response.TokenResponse)
		require.NoError(t, err)

		resultMxjMap, err := mxj.NewMapJson(resultJsonByteSlice)
		require.NoError(t, err)

		resultBytes, err := resultMxjMap.Xml()
		require.NoError(t, err)

		require.Equal(t, writeBytes, resultBytes)

	})
}

func Test_AlatauCityBankPayIn_Charge(t *testing.T) {
	request := &AlatauCityChargeRequest{
		Body: &AlatauCityChargeRequest_ChargeRequest{
			APPROVE:  testhelpers.Ptr("string_1"),
			ORDER:    testhelpers.Ptr("string_6"),
			AMOUNT:   testhelpers.Ptr("string_3"),
			MERCHANT: testhelpers.Ptr("string_6"),
			P_SIGN:   testhelpers.Ptr("string_6"),
		},
	}

	response := &AlatauCityChargeResponse{
		ChargeResponse: &AlatauCityChargeResponse_ChargeResponse{
			Result: &AlatauCityChargeResponse_Result{
				Code:        testhelpers.Ptr("string_4"),
				Description: testhelpers.Ptr("string_5"),
			},
		},
	}
	_ = response

	// define rest arguments
	ctx := context.TODO()

	t.Run("argument_and_request_checks_testing_stand_18", func(t *testing.T) {
		// define test server
		var testFunc func(w http.ResponseWriter, r *http.Request)
		server := httptest.NewServer(
			http.HandlerFunc(
				func(w http.ResponseWriter, r *http.Request) {
					testFunc(w, r)
				},
			),
		)
		defer server.Close()

		restAlatauCityBankPayInBaseURL["argument_and_request_checks_testing_stand_18"] = server.URL

		testFunc = func(w http.ResponseWriter, r *http.Request) {
			require.Equal(t, "/ecom/api", r.URL.Path)
			require.Equal(t, http.MethodPost, r.Method)

			require.NoError(t, r.ParseForm())

			require.Equal(t, "string_1", r.PostForm.Get("APPROVE"))
			require.Equal(t, "string_6", r.PostForm.Get("ORDER"))
			require.Equal(t, "string_3", r.PostForm.Get("AMOUNT"))
			require.Equal(t, "string_6", r.PostForm.Get("MERCHANT"))
			require.Equal(t, "string_6", r.PostForm.Get("P_SIGN"))
		}

		// define new rest client
		testingRest := NewRestAlatauCityBankPayInServer("argument_and_request_checks_testing_stand_18", nil)
		testingRest.Charge(
			ctx,
			request,
		)

	})

	// XML
	t.Run("check_field_parse_ChargeResponse_testing_stand_19", func(t *testing.T) {
		jsonByteSlice, err := json.Marshal(response.ChargeResponse)
		require.NoError(t, err)

		mxjMap, err := mxj.NewMapJson(jsonByteSlice)
		require.NoError(t, err)

		writeBytes, err := mxjMap.Xml()
		require.NoError(t, err)

		// define test server
		server := httptest.NewServer(
			http.HandlerFunc(
				func(w http.ResponseWriter, r *http.Request) {
					_, err := w.Write(writeBytes)
					require.NoError(t, err)
				},
			),
		)
		defer server.Close()

		restAlatauCityBankPayInBaseURL["check_field_parse_ChargeResponse_testing_stand_19"] = server.URL
		testingRest := NewRestAlatauCityBankPayInServer("check_field_parse_ChargeResponse_testing_stand_19", nil)
		result, err := testingRest.Charge(
			ctx,
			request,
		)
		require.NoError(t, err)
		require.NotNil(t, result)
		require.Equal(t, File_inner_processing_rest_alatau_city_payin_proto_AlatauCityBankPayIn_Charge_ChargeResponse, result.ResponseOutput)
		resultJsonByteSlice, err := json.Marshal(result.Response.ChargeResponse)
		require.NoError(t, err)

		resultMxjMap, err := mxj.NewMapJson(resultJsonByteSlice)
		require.NoError(t, err)

		resultBytes, err := resultMxjMap.Xml()
		require.NoError(t, err)

		require.Equal(t, writeBytes, resultBytes)

	})
}
