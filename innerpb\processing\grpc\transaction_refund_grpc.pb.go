// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.5.1
// - protoc             v5.29.3
// source: inner/processing/grpc/transaction_refund.proto

package grpc

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.64.0 or later.
const _ = grpc.SupportPackageIsVersion9

const (
	Refund_GetByTransactionIDV1_FullMethodName = "/processing.transaction.transaction_refund.Refund/GetByTransactionIDV1"
)

// RefundClient is the client API for Refund service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type RefundClient interface {
	GetByTransactionIDV1(ctx context.Context, in *TransactionRequestDataV1, opts ...grpc.CallOption) (*TransactionResponseDataV1, error)
}

type refundClient struct {
	cc grpc.ClientConnInterface
}

func NewRefundClient(cc grpc.ClientConnInterface) RefundClient {
	return &refundClient{cc}
}

func (c *refundClient) GetByTransactionIDV1(ctx context.Context, in *TransactionRequestDataV1, opts ...grpc.CallOption) (*TransactionResponseDataV1, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(TransactionResponseDataV1)
	err := c.cc.Invoke(ctx, Refund_GetByTransactionIDV1_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// RefundServer is the server API for Refund service.
// All implementations must embed UnimplementedRefundServer
// for forward compatibility.
type RefundServer interface {
	GetByTransactionIDV1(context.Context, *TransactionRequestDataV1) (*TransactionResponseDataV1, error)
	mustEmbedUnimplementedRefundServer()
}

// UnimplementedRefundServer must be embedded to have
// forward compatible implementations.
//
// NOTE: this should be embedded by value instead of pointer to avoid a nil
// pointer dereference when methods are called.
type UnimplementedRefundServer struct{}

func (UnimplementedRefundServer) GetByTransactionIDV1(context.Context, *TransactionRequestDataV1) (*TransactionResponseDataV1, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetByTransactionIDV1 not implemented")
}
func (UnimplementedRefundServer) mustEmbedUnimplementedRefundServer() {}
func (UnimplementedRefundServer) testEmbeddedByValue()                {}

// UnsafeRefundServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to RefundServer will
// result in compilation errors.
type UnsafeRefundServer interface {
	mustEmbedUnimplementedRefundServer()
}

func RegisterRefundServer(s grpc.ServiceRegistrar, srv RefundServer) {
	// If the following call pancis, it indicates UnimplementedRefundServer was
	// embedded by pointer and is nil.  This will cause panics if an
	// unimplemented method is ever invoked, so we test this at initialization
	// time to prevent it from happening at runtime later due to I/O.
	if t, ok := srv.(interface{ testEmbeddedByValue() }); ok {
		t.testEmbeddedByValue()
	}
	s.RegisterService(&Refund_ServiceDesc, srv)
}

func _Refund_GetByTransactionIDV1_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(TransactionRequestDataV1)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RefundServer).GetByTransactionIDV1(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Refund_GetByTransactionIDV1_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RefundServer).GetByTransactionIDV1(ctx, req.(*TransactionRequestDataV1))
	}
	return interceptor(ctx, in, info, handler)
}

// Refund_ServiceDesc is the grpc.ServiceDesc for Refund service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var Refund_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "processing.transaction.transaction_refund.Refund",
	HandlerType: (*RefundServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetByTransactionIDV1",
			Handler:    _Refund_GetByTransactionIDV1_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "inner/processing/grpc/transaction_refund.proto",
}
