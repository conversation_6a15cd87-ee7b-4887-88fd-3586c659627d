package schema

import (
	"strconv"

	"github.com/go-playground/validator/v10"

	"git.local/sensitive/innerpb/processing/goerr"
	gorpc "git.local/sensitive/innerpb/processing/grpc"
	"git.local/sensitive/processing/acquirer/model"
)

const (
	Percentage = 100
)

func NewRuleEventLogRequestInfo(ruleID uint64) string {
	return "rule_id: " + strconv.Itoa(int(ruleID))
}

type CreateRuleRequest struct {
	ProjectID         uint64                      `json:"project_id" validate:"required"`
	TransactionTypeID uint64                      `json:"transaction_type_id" validate:"required"`
	IpsID             *uint64                     `json:"ips_id,omitempty"`
	IssuerID          *uint64                     `json:"issuer_id,omitempty"`
	CountryID         *uint64                     `json:"country_id,omitempty"`
	AmountFrom        *float64                    `json:"amount_from,omitempty"`
	AmountTo          *float64                    `json:"amount_to,omitempty"`
	Percentages       CreateRulePercentageRequest `json:"acquirers" validate:"required"`
}

func (cr *CreateRuleRequest) Validate() error {
	if cr == nil {
		return goerr.ErrParseErrorBody
	}

	validation := validator.New()

	return validation.Struct(cr)
}

func (cr *CreateRuleRequest) ToRuleModel() *model.Rule {
	return &model.Rule{
		ProjectID:         cr.ProjectID,
		IpsID:             cr.IpsID,
		IssuerID:          cr.IssuerID,
		CountryID:         cr.CountryID,
		TransactionTypeID: cr.TransactionTypeID,
		AmountFrom:        cr.AmountFrom,
		AmountTo:          cr.AmountTo,
		IsActive:          false,
	}
}

type CreateBaseRuleRequest struct {
	ProjectID         uint64                      `json:"project_id" validate:"required"`
	TransactionTypeID uint64                      `json:"transaction_type_id" validate:"required"`
	Percentages       CreateRulePercentageRequest `json:"acquirers" validate:"required"`
}

func (cbr *CreateBaseRuleRequest) Validate() error {
	if cbr == nil {
		return goerr.ErrParseErrorBody
	}

	validation := validator.New()

	return validation.Struct(cbr)
}

func (cbr *CreateBaseRuleRequest) ToRuleModel() *model.Rule {
	return &model.Rule{
		ProjectID:         cbr.ProjectID,
		TransactionTypeID: cbr.TransactionTypeID,
		IsActive:          true,
		IsBase:            true,
		Weight:            model.BaseRuleWeight,
	}
}

type RuleListRequest struct {
	ProjectID         *uint64 `form:"project_id"`
	TransactionTypeID *uint8  `form:"transaction_type_id"`
	WeightSortBy      string  `form:"weight_sort_by" default:"ASC"`
	IsBase            *bool   `form:"is_base"`
}

type RuleSearchRequest struct {
	ProjectID         uint64  `form:"project_id" validate:"required"`
	TransactionTypeID uint64  `form:"transaction_type_id" validate:"required"`
	Amount            float64 `form:"amount" validate:"required"`
	IpsID             *uint64 `form:"ips_id"`
	IssuerID          *uint64 `form:"issuer_id"`
	CountryID         *uint64 `form:"country_id"`
	MerchantID        uint64  `form:"merchant_id" validate:"required"`
}

func (rsr *RuleSearchRequest) Validate() error {
	if rsr == nil {
		return goerr.ErrParseErrorBody
	}

	validation := validator.New()

	return validation.Struct(rsr)
}

func NewRuleSearchRequestFromRaw(terminalReqData *gorpc.SearchTerminalReqDataV1) RuleSearchRequest {
	return RuleSearchRequest{
		ProjectID:         terminalReqData.GetProjectId(),
		MerchantID:        terminalReqData.GetMerchantId(),
		TransactionTypeID: terminalReqData.GetTransactionTypeId(),
		Amount:            terminalReqData.GetAmount(),
		IssuerID:          terminalReqData.IssuerId,
		IpsID:             terminalReqData.IpsId,
		CountryID:         terminalReqData.CountryId,
	}
}

type ExtendedRuleSearchRequest struct {
	RuleSearchRequest
	TerminalIDs []uint64 `form:"terminal_ids" validate:"required"`
}

func (sr *ExtendedRuleSearchRequest) Validate() error {
	if sr == nil {
		return goerr.ErrParseErrorBody
	}

	validation := validator.New()

	return validation.Struct(sr)
}

type RuleByActiveTerminalsRequest struct {
	*RuleSearchRequest
	ActiveTerminals
}

func NewRuleByActiveTerminalsRequestFromRaw(
	terminalExtendedReq *gorpc.RuleByActiveTerminalsReqV1,
	terminals []model.Terminal,
) *RuleByActiveTerminalsRequest {
	var activeTerminals = make([]*ActiveTerminal, len(terminals))

	for i := range terminals {
		activeTerminals[i] = &ActiveTerminal{
			TerminalID:  terminals[i].ID,
			AcquirerID:  terminals[i].Acquirer.ID,
			AcquireCode: terminals[i].Acquirer.Code,
		}
	}

	newRuleSearchRequest := &RuleSearchRequest{
		ProjectID:         terminalExtendedReq.GetSearchTerminalReq().GetProjectId(),
		TransactionTypeID: terminalExtendedReq.GetSearchTerminalReq().GetTransactionTypeId(),
		Amount:            terminalExtendedReq.GetSearchTerminalReq().GetAmount(),
		IssuerID:          terminalExtendedReq.GetSearchTerminalReq().IssuerId,
		IpsID:             terminalExtendedReq.GetSearchTerminalReq().IpsId,
		CountryID:         terminalExtendedReq.GetSearchTerminalReq().CountryId,
	}

	return &RuleByActiveTerminalsRequest{
		RuleSearchRequest: newRuleSearchRequest,
		ActiveTerminals:   activeTerminals,
	}
}

func NewExtendedRuleSearchRequestFromRaw(
	terminalExtendedReq *gorpc.ExtendedSearchTerminalReqDataV1,
) ExtendedRuleSearchRequest {
	newRuleSearchRequest := RuleSearchRequest{
		ProjectID:         terminalExtendedReq.GetSearchTerminalReq().GetProjectId(),
		TransactionTypeID: terminalExtendedReq.GetSearchTerminalReq().GetTransactionTypeId(),
		Amount:            terminalExtendedReq.GetSearchTerminalReq().GetAmount(),
		IssuerID:          terminalExtendedReq.GetSearchTerminalReq().IssuerId,
		IpsID:             terminalExtendedReq.GetSearchTerminalReq().IpsId,
		CountryID:         terminalExtendedReq.GetSearchTerminalReq().CountryId,
	}

	return ExtendedRuleSearchRequest{
		RuleSearchRequest: newRuleSearchRequest,
		TerminalIDs:       terminalExtendedReq.TerminalIds,
	}
}

type RulePercentageMap []*PercentageItem

type PercentageItem struct {
	Percentage      int
	RulePercentages []model.RulePercentage
}

func (i *RulePercentageMap) Add(rulePercentage model.RulePercentage) {
	isFound := true

	for index, row := range *i {
		if row.Percentage == rulePercentage.Percentage {
			row.RulePercentages = append(row.RulePercentages, rulePercentage)
			(*i)[index] = row
			isFound = false
		}
	}

	if isFound {
		newRow := PercentageItem{
			Percentage:      rulePercentage.Percentage,
			RulePercentages: nil,
		}
		newRow.RulePercentages = append(newRow.RulePercentages, rulePercentage)
		*i = append(*i, &newRow)
	}
}

func (rl *RuleListRequest) RuleListValidation() error {
	if rl == nil {
		return goerr.ErrParseErrorBody
	}

	validation := validator.New()

	return validation.Struct(rl)
}
