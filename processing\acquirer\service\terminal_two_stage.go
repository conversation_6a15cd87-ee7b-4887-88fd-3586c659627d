package service

import (
	"context"
	"git.local/sensitive/processing/acquirer/repository/database"
	"git.local/sensitive/sdk/dog"
)

type TerminalTwoStageService struct {
	terminalTwoStageRepo database.TerminalTwoStager
}

func NewTerminalTwoStageService(
	terminalTwoStageRepo database.TerminalTwoStager,
) TerminalTwoStager {
	return &TerminalTwoStageService{
		terminalTwoStageRepo: terminalTwoStageRepo,
	}
}

func (t TerminalTwoStageService) UpdateTimeout(ctx context.Context, id uint64, timeout uint32) (err error) {
	ctx, span := dog.CreateSpan(ctx, "TerminalTwoStageService_UpdateTimeout")
	defer span.End()

	return t.terminalTwoStageRepo.UpdateTimeout(ctx, id, timeout)
}
