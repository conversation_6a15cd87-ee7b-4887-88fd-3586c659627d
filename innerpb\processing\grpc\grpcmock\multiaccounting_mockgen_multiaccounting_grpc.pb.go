// Code generated by MockGen. DO NOT EDIT.
// Source: multiaccounting_grpc.pb.go

// Package grpcmock is a generated GoMock package.
package grpcmock

import (
	context "context"
	reflect "reflect"

	grpc "git.local/sensitive/innerpb/processing/grpc"
	gomock "github.com/golang/mock/gomock"
	grpc0 "google.golang.org/grpc"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
)

// MockMultiaccountingClient is a mock of MultiaccountingClient interface.
type MockMultiaccountingClient struct {
	ctrl     *gomock.Controller
	recorder *MockMultiaccountingClientMockRecorder
}

// MockMultiaccountingClientMockRecorder is the mock recorder for MockMultiaccountingClient.
type MockMultiaccountingClientMockRecorder struct {
	mock *MockMultiaccountingClient
}

// NewMockMultiaccountingClient creates a new mock instance.
func NewMockMultiaccountingClient(ctrl *gomock.Controller) *MockMultiaccountingClient {
	mock := &MockMultiaccountingClient{ctrl: ctrl}
	mock.recorder = &MockMultiaccountingClientMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockMultiaccountingClient) EXPECT() *MockMultiaccountingClientMockRecorder {
	return m.recorder
}

// AcceptTransfer mocks base method.
func (m *MockMultiaccountingClient) AcceptTransfer(ctx context.Context, in *grpc.AcceptTransferRequest, opts ...grpc0.CallOption) (*grpc.AcceptTransferResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "AcceptTransfer", varargs...)
	ret0, _ := ret[0].(*grpc.AcceptTransferResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// AcceptTransfer indicates an expected call of AcceptTransfer.
func (mr *MockMultiaccountingClientMockRecorder) AcceptTransfer(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AcceptTransfer", reflect.TypeOf((*MockMultiaccountingClient)(nil).AcceptTransfer), varargs...)
}

// DeclineTransfer mocks base method.
func (m *MockMultiaccountingClient) DeclineTransfer(ctx context.Context, in *grpc.DeclineTransferRequest, opts ...grpc0.CallOption) (*grpc.DeclineTransferResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "DeclineTransfer", varargs...)
	ret0, _ := ret[0].(*grpc.DeclineTransferResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DeclineTransfer indicates an expected call of DeclineTransfer.
func (mr *MockMultiaccountingClientMockRecorder) DeclineTransfer(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeclineTransfer", reflect.TypeOf((*MockMultiaccountingClient)(nil).DeclineTransfer), varargs...)
}

// GetAccountBalance mocks base method.
func (m *MockMultiaccountingClient) GetAccountBalance(ctx context.Context, in *grpc.GetAccountBalanceRequest, opts ...grpc0.CallOption) (*grpc.GetAccountBalanceResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetAccountBalance", varargs...)
	ret0, _ := ret[0].(*grpc.GetAccountBalanceResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAccountBalance indicates an expected call of GetAccountBalance.
func (mr *MockMultiaccountingClientMockRecorder) GetAccountBalance(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAccountBalance", reflect.TypeOf((*MockMultiaccountingClient)(nil).GetAccountBalance), varargs...)
}

// GetAccountIdentifier mocks base method.
func (m *MockMultiaccountingClient) GetAccountIdentifier(ctx context.Context, in *emptypb.Empty, opts ...grpc0.CallOption) (*grpc.GetAccountIdentifierResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetAccountIdentifier", varargs...)
	ret0, _ := ret[0].(*grpc.GetAccountIdentifierResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAccountIdentifier indicates an expected call of GetAccountIdentifier.
func (mr *MockMultiaccountingClientMockRecorder) GetAccountIdentifier(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAccountIdentifier", reflect.TypeOf((*MockMultiaccountingClient)(nil).GetAccountIdentifier), varargs...)
}

// GetAccountStatement mocks base method.
func (m *MockMultiaccountingClient) GetAccountStatement(ctx context.Context, in *grpc.GetAccountStatementRequest, opts ...grpc0.CallOption) (*grpc.GetAccountStatementResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetAccountStatement", varargs...)
	ret0, _ := ret[0].(*grpc.GetAccountStatementResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAccountStatement indicates an expected call of GetAccountStatement.
func (mr *MockMultiaccountingClientMockRecorder) GetAccountStatement(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAccountStatement", reflect.TypeOf((*MockMultiaccountingClient)(nil).GetAccountStatement), varargs...)
}

// GetTransferDetails mocks base method.
func (m *MockMultiaccountingClient) GetTransferDetails(ctx context.Context, in *grpc.GetTransferDetailsRequest, opts ...grpc0.CallOption) (*grpc.GetTransferDetailsResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetTransferDetails", varargs...)
	ret0, _ := ret[0].(*grpc.GetTransferDetailsResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetTransferDetails indicates an expected call of GetTransferDetails.
func (mr *MockMultiaccountingClientMockRecorder) GetTransferDetails(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetTransferDetails", reflect.TypeOf((*MockMultiaccountingClient)(nil).GetTransferDetails), varargs...)
}

// GetTransfersList mocks base method.
func (m *MockMultiaccountingClient) GetTransfersList(ctx context.Context, in *grpc.GetTransfersListRequest, opts ...grpc0.CallOption) (*grpc.GetTransfersListResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetTransfersList", varargs...)
	ret0, _ := ret[0].(*grpc.GetTransfersListResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetTransfersList indicates an expected call of GetTransfersList.
func (mr *MockMultiaccountingClientMockRecorder) GetTransfersList(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetTransfersList", reflect.TypeOf((*MockMultiaccountingClient)(nil).GetTransfersList), varargs...)
}

// MakeMerchantCheck mocks base method.
func (m *MockMultiaccountingClient) MakeMerchantCheck(ctx context.Context, in *grpc.MakeMerchantCheckRequest, opts ...grpc0.CallOption) (*grpc.MakeMerchantCheckResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "MakeMerchantCheck", varargs...)
	ret0, _ := ret[0].(*grpc.MakeMerchantCheckResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// MakeMerchantCheck indicates an expected call of MakeMerchantCheck.
func (mr *MockMultiaccountingClientMockRecorder) MakeMerchantCheck(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "MakeMerchantCheck", reflect.TypeOf((*MockMultiaccountingClient)(nil).MakeMerchantCheck), varargs...)
}

// MakeTransfer mocks base method.
func (m *MockMultiaccountingClient) MakeTransfer(ctx context.Context, in *grpc.MakeTransferRequest, opts ...grpc0.CallOption) (*grpc.MakeTransferResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "MakeTransfer", varargs...)
	ret0, _ := ret[0].(*grpc.MakeTransferResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// MakeTransfer indicates an expected call of MakeTransfer.
func (mr *MockMultiaccountingClientMockRecorder) MakeTransfer(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "MakeTransfer", reflect.TypeOf((*MockMultiaccountingClient)(nil).MakeTransfer), varargs...)
}

// RedoTransfer mocks base method.
func (m *MockMultiaccountingClient) RedoTransfer(ctx context.Context, in *grpc.RedoTransferRequest, opts ...grpc0.CallOption) (*grpc.RedoTransferResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "RedoTransfer", varargs...)
	ret0, _ := ret[0].(*grpc.RedoTransferResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// RedoTransfer indicates an expected call of RedoTransfer.
func (mr *MockMultiaccountingClientMockRecorder) RedoTransfer(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "RedoTransfer", reflect.TypeOf((*MockMultiaccountingClient)(nil).RedoTransfer), varargs...)
}

// MockMultiaccountingServer is a mock of MultiaccountingServer interface.
type MockMultiaccountingServer struct {
	ctrl     *gomock.Controller
	recorder *MockMultiaccountingServerMockRecorder
}

// MockMultiaccountingServerMockRecorder is the mock recorder for MockMultiaccountingServer.
type MockMultiaccountingServerMockRecorder struct {
	mock *MockMultiaccountingServer
}

// NewMockMultiaccountingServer creates a new mock instance.
func NewMockMultiaccountingServer(ctrl *gomock.Controller) *MockMultiaccountingServer {
	mock := &MockMultiaccountingServer{ctrl: ctrl}
	mock.recorder = &MockMultiaccountingServerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockMultiaccountingServer) EXPECT() *MockMultiaccountingServerMockRecorder {
	return m.recorder
}

// AcceptTransfer mocks base method.
func (m *MockMultiaccountingServer) AcceptTransfer(arg0 context.Context, arg1 *grpc.AcceptTransferRequest) (*grpc.AcceptTransferResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AcceptTransfer", arg0, arg1)
	ret0, _ := ret[0].(*grpc.AcceptTransferResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// AcceptTransfer indicates an expected call of AcceptTransfer.
func (mr *MockMultiaccountingServerMockRecorder) AcceptTransfer(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AcceptTransfer", reflect.TypeOf((*MockMultiaccountingServer)(nil).AcceptTransfer), arg0, arg1)
}

// DeclineTransfer mocks base method.
func (m *MockMultiaccountingServer) DeclineTransfer(arg0 context.Context, arg1 *grpc.DeclineTransferRequest) (*grpc.DeclineTransferResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeclineTransfer", arg0, arg1)
	ret0, _ := ret[0].(*grpc.DeclineTransferResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DeclineTransfer indicates an expected call of DeclineTransfer.
func (mr *MockMultiaccountingServerMockRecorder) DeclineTransfer(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeclineTransfer", reflect.TypeOf((*MockMultiaccountingServer)(nil).DeclineTransfer), arg0, arg1)
}

// GetAccountBalance mocks base method.
func (m *MockMultiaccountingServer) GetAccountBalance(arg0 context.Context, arg1 *grpc.GetAccountBalanceRequest) (*grpc.GetAccountBalanceResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAccountBalance", arg0, arg1)
	ret0, _ := ret[0].(*grpc.GetAccountBalanceResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAccountBalance indicates an expected call of GetAccountBalance.
func (mr *MockMultiaccountingServerMockRecorder) GetAccountBalance(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAccountBalance", reflect.TypeOf((*MockMultiaccountingServer)(nil).GetAccountBalance), arg0, arg1)
}

// GetAccountIdentifier mocks base method.
func (m *MockMultiaccountingServer) GetAccountIdentifier(arg0 context.Context, arg1 *emptypb.Empty) (*grpc.GetAccountIdentifierResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAccountIdentifier", arg0, arg1)
	ret0, _ := ret[0].(*grpc.GetAccountIdentifierResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAccountIdentifier indicates an expected call of GetAccountIdentifier.
func (mr *MockMultiaccountingServerMockRecorder) GetAccountIdentifier(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAccountIdentifier", reflect.TypeOf((*MockMultiaccountingServer)(nil).GetAccountIdentifier), arg0, arg1)
}

// GetAccountStatement mocks base method.
func (m *MockMultiaccountingServer) GetAccountStatement(arg0 context.Context, arg1 *grpc.GetAccountStatementRequest) (*grpc.GetAccountStatementResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAccountStatement", arg0, arg1)
	ret0, _ := ret[0].(*grpc.GetAccountStatementResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAccountStatement indicates an expected call of GetAccountStatement.
func (mr *MockMultiaccountingServerMockRecorder) GetAccountStatement(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAccountStatement", reflect.TypeOf((*MockMultiaccountingServer)(nil).GetAccountStatement), arg0, arg1)
}

// GetTransferDetails mocks base method.
func (m *MockMultiaccountingServer) GetTransferDetails(arg0 context.Context, arg1 *grpc.GetTransferDetailsRequest) (*grpc.GetTransferDetailsResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetTransferDetails", arg0, arg1)
	ret0, _ := ret[0].(*grpc.GetTransferDetailsResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetTransferDetails indicates an expected call of GetTransferDetails.
func (mr *MockMultiaccountingServerMockRecorder) GetTransferDetails(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetTransferDetails", reflect.TypeOf((*MockMultiaccountingServer)(nil).GetTransferDetails), arg0, arg1)
}

// GetTransfersList mocks base method.
func (m *MockMultiaccountingServer) GetTransfersList(arg0 context.Context, arg1 *grpc.GetTransfersListRequest) (*grpc.GetTransfersListResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetTransfersList", arg0, arg1)
	ret0, _ := ret[0].(*grpc.GetTransfersListResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetTransfersList indicates an expected call of GetTransfersList.
func (mr *MockMultiaccountingServerMockRecorder) GetTransfersList(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetTransfersList", reflect.TypeOf((*MockMultiaccountingServer)(nil).GetTransfersList), arg0, arg1)
}

// MakeMerchantCheck mocks base method.
func (m *MockMultiaccountingServer) MakeMerchantCheck(arg0 context.Context, arg1 *grpc.MakeMerchantCheckRequest) (*grpc.MakeMerchantCheckResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "MakeMerchantCheck", arg0, arg1)
	ret0, _ := ret[0].(*grpc.MakeMerchantCheckResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// MakeMerchantCheck indicates an expected call of MakeMerchantCheck.
func (mr *MockMultiaccountingServerMockRecorder) MakeMerchantCheck(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "MakeMerchantCheck", reflect.TypeOf((*MockMultiaccountingServer)(nil).MakeMerchantCheck), arg0, arg1)
}

// MakeTransfer mocks base method.
func (m *MockMultiaccountingServer) MakeTransfer(arg0 context.Context, arg1 *grpc.MakeTransferRequest) (*grpc.MakeTransferResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "MakeTransfer", arg0, arg1)
	ret0, _ := ret[0].(*grpc.MakeTransferResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// MakeTransfer indicates an expected call of MakeTransfer.
func (mr *MockMultiaccountingServerMockRecorder) MakeTransfer(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "MakeTransfer", reflect.TypeOf((*MockMultiaccountingServer)(nil).MakeTransfer), arg0, arg1)
}

// RedoTransfer mocks base method.
func (m *MockMultiaccountingServer) RedoTransfer(arg0 context.Context, arg1 *grpc.RedoTransferRequest) (*grpc.RedoTransferResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "RedoTransfer", arg0, arg1)
	ret0, _ := ret[0].(*grpc.RedoTransferResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// RedoTransfer indicates an expected call of RedoTransfer.
func (mr *MockMultiaccountingServerMockRecorder) RedoTransfer(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "RedoTransfer", reflect.TypeOf((*MockMultiaccountingServer)(nil).RedoTransfer), arg0, arg1)
}

// mustEmbedUnimplementedMultiaccountingServer mocks base method.
func (m *MockMultiaccountingServer) mustEmbedUnimplementedMultiaccountingServer() {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "mustEmbedUnimplementedMultiaccountingServer")
}

// mustEmbedUnimplementedMultiaccountingServer indicates an expected call of mustEmbedUnimplementedMultiaccountingServer.
func (mr *MockMultiaccountingServerMockRecorder) mustEmbedUnimplementedMultiaccountingServer() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "mustEmbedUnimplementedMultiaccountingServer", reflect.TypeOf((*MockMultiaccountingServer)(nil).mustEmbedUnimplementedMultiaccountingServer))
}

// MockUnsafeMultiaccountingServer is a mock of UnsafeMultiaccountingServer interface.
type MockUnsafeMultiaccountingServer struct {
	ctrl     *gomock.Controller
	recorder *MockUnsafeMultiaccountingServerMockRecorder
}

// MockUnsafeMultiaccountingServerMockRecorder is the mock recorder for MockUnsafeMultiaccountingServer.
type MockUnsafeMultiaccountingServerMockRecorder struct {
	mock *MockUnsafeMultiaccountingServer
}

// NewMockUnsafeMultiaccountingServer creates a new mock instance.
func NewMockUnsafeMultiaccountingServer(ctrl *gomock.Controller) *MockUnsafeMultiaccountingServer {
	mock := &MockUnsafeMultiaccountingServer{ctrl: ctrl}
	mock.recorder = &MockUnsafeMultiaccountingServerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockUnsafeMultiaccountingServer) EXPECT() *MockUnsafeMultiaccountingServerMockRecorder {
	return m.recorder
}

// mustEmbedUnimplementedMultiaccountingServer mocks base method.
func (m *MockUnsafeMultiaccountingServer) mustEmbedUnimplementedMultiaccountingServer() {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "mustEmbedUnimplementedMultiaccountingServer")
}

// mustEmbedUnimplementedMultiaccountingServer indicates an expected call of mustEmbedUnimplementedMultiaccountingServer.
func (mr *MockUnsafeMultiaccountingServerMockRecorder) mustEmbedUnimplementedMultiaccountingServer() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "mustEmbedUnimplementedMultiaccountingServer", reflect.TypeOf((*MockUnsafeMultiaccountingServer)(nil).mustEmbedUnimplementedMultiaccountingServer))
}
