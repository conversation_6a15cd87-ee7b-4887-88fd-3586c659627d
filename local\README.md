# Руководство по настройке и запуску processing и Krakend через k8s

## 1.0 Скачиваем зависимости 

### 1.1 Установить Docker
- Скачать и установить [Docker Desktop](https://www.docker.com/products/docker-desktop).
- Включить Kubernetes:
    - Перейти в **Settings** → **Kubernetes** → **Enable Kubernetes**.
    - Применить изменения и дождаться готовности Kubernetes.
      ![img.png](readme/img-docker.png)

### 1.2 Установить `kubectl` и `helm`
- Установить [Chocolatey](https://chocolatey.org/install), если он ещё не установлен.

 ***Если у вас не OS Windows, используйте другой пакетный менеджер***
 
 ***ВАЖНО: Powershell от имени администратора***
- Установка `make` через `choco`:
  ```powershell
  choco install make 
  ``` 
- Установка `kubectl` через `choco`:
  ```powershell
  choco install kubernetes-cli 
  ``` 
- Установка `helm` через `choco`:
  ```powershell
  choco install kubernetes-helm
  ```

### 1.3 Установить GUI для Kubernetes
- Для удобства мониторинга Kubernetes можно установить любой k8s GUI
- Для примера, [Lens](https://k8slens.dev/download),.

***ВАЖНО: Устанавливайте Lens для текущего пользователя***
![img.png](readme/img-lens-install.png)

### 1.4. Клонирование в папку local deployment-репозиториев
processing-deployment
  ```powershell
  git clone https://git.tarlanpayments.kz/processing/processing-deployment.git
  ```
processing-deployment-krakend
  ```powershell
  git clone https://git.tarlanpayments.kz/processing/processing-deployment-krakend.git
  ```
- После установки в обеих репозиториях переключитесь на local-ветки

### 1.4.1 Клонирование в папку processing-base репозитория krakenD (опционально)
  ```powershell
  git clone https://git.tarlanpayments.kz/processing/krakend.git
  ```
- Необходима dev-ветка или иная в которой есть ***Dockerfile_local***

- В итоге папка processing-base должна быть такого вида, но вы можете делать по-своему, меняя переменные в makefile в следующих шагах
  ![img.png](readme/img-repos.png)

### 1.5.0 Использование makefile
- Стандартный Makefile содержит windows powershell - команды
- Для других OS вы можете использовать Makefile_bash, заменив им оригинал, или написать свой аналог

### 1.5.1 Сборка docker-image для k8s чартов

- В Makefile заполните пути для dockerfile'ов krakend и processing-base
- Сборка осуществляется командой
  ```powershell
  make all-build
  ```
  
### 1.6. Заполнение данных в values-local в k8s чартах

- Заполните необходимые данные в values-local у k8s чартов
- Это могут быть данные от vault, названия docker-образов и т д
- ***Главное чтобы переменные вида docker-image-name или docker-image-tag 
совпадали в values-local у чарта и в Makefile***

### 1.7. Запуск проекта
- Убедитесь что у вас запущен Docker


### 1.7.1 Установка processing namespace и service-account для k8s
- Namespace и service-account нужны для корректной установки helm-чартов processing
- Инициализация этих зависимостей k8s происходит командой
  ```powershell
  make all-init
  ```

- Для терминалов которые не поддерживают makefile или makefile_bash
  ```powershell
  kubectl create namespace processing
  kubectl create sa -n processing vault-auth
  ```

### 1.7.2 Запуск и мониторинг кластера 
- Запуск кластера осуществляется командой
  ```powershell
  make all-up
  ```
- Проверить статус под вы можете визуально в Lens, в разделе pods, либо набрав команду
  ```powershell
  kubectl get pods -n processing
  ```
  где "processing" - неймспейс в кластере

***ВАЖНО: После установки хелм чартов поды могут не отображаться в Lens или через команду kubectl***

- Вам в разделе workloads - pods нужно поставить галочку на отображение неймспейса
в верхней правой части окна.

  ![img.png](readme/img-lens.png) 

  После этой команды придется сделать редеплой чартов, сперва удаля их командой из раздела 1.8, а далее заного запустив
### 1.7.3 Открытие внешнего порта у krakend для postman

- Дождитесь когда krakenD пода начнет работу и откройте её порт командой
  ```powershell
  make open
  ```
- Наслаждайтесь работой, обращаясь к нужному api через postman по адресу
  127.0.0.1:3000/

### 1.8. Остановка проекта

- Запуск осуществляется командой
  ```powershell
  make all-down
  ```

### 1.9. Перезагрузка кластера
- Бывают случаи когда нужно перезапустить кластер
  ```powershell
  make cluster-restart
  ```

- Бывают случаи когда нужно пересобрать образы и перезапустить кластер
  ```powershell
  make all-restart
  ```

## 2. Как запускать проект без k8s
  
- установить cobra

```powershell
go get -u github.com/spf13/cobra@latest
go install github.com/spf13/cobra-cli@latest
```

- для каждого сеанса powershell задаем нужные env
в качестве примера service-provider 

***ВАЖНО: переменные среды существуют только для текущего сеанса powershell***

```powershell
$env:ENVIRONMENT = "local"
$env:VAULT_URL = "https://vault.dev-tarlanpayments.kz"
$env:VAULT_USER = "vault_user"
$env:VAULT_PASSWORD = "vault_password"
```

- переходим в папку с main.go
```powershell
cd processing/cobra
```

- запускаем нужный сервис в аргументах
```powershell
go run main.go service-provider
```

- или для примера сервис вложенный в сервис integration
```powershell
go run main.go integration drivers qiwi
```

## 3. Спасибо за внимание

- Если у вас есть идеи или предложения, вы можете принять участие в улучшении инфраструктуры
