edition = "2023";

package processing.cobra.requestlog;

option go_package = "git.local/sensitive/innerpb/processing/cobra";

import "mvp/proto/cobra.proto";
import "google/protobuf/descriptor.proto";

option (mvp.cobra_root) = true;

service TransactionService {
  option (mvp.cobra_config) = {
    Components: [
      GinEngineInit,
      GormInit,
      GTransactionManager,
      BackoffPolicy,
      HttpLogger,
      MongoInit,
      NatsInit,
      RedisInit,
      CSRF,
      JetStreamContext,
      NatsConn,
      NatsMessageRouter,
      Stand
    ],
    ServiceNames: ["transaction", "transaction", "Transaction"],
    package: "git.local/sensitive/processing/transaction",
    func_name: "TransactionService",

    restHandler: [
      {
        name: "Transaction"
        vault_configs: [
          { key: "PAYMENT_VIEW_URL", type: String}
        ]
        layers: [
          {
            name: "PayInService"
            type: UseCaseLayer
            layers: [
              {
                type: ServiceLayer,
                name: "ProjectService",
                layers: [
                  { type: RepoLayer, name: "ProjectRest", components: [BackoffPolicy, HttpLogger], package_path: "/rest" },
                  { type: GrpcClient, name: "Merchant" }
                ]
              },
              {
                type: ServiceLayer,
                name: "TransactionHelperService",
                vault_configs: [
                  { key: "PAYMENT_VIEW_URL", type: String }
                ]
              },
              {
                type: ServiceLayer,
                name: "TransactionVerifier",
                layers: [
                  { type: RepoLayer, name: "ProjectRest", components: [BackoffPolicy, HttpLogger], package_path: "/rest" },
                  { type: GrpcClient, name: "Card" },
                  { type: GrpcClient, name: "ProjectTransactions" },
                  { type: GrpcClient, name: "Merchant" }
                ]
              },
              {
                type: ServiceLayer,
                name: "TransactionTypeService",
                layers: [
                  { type: RepoLayer, name: "TransactionTypeDB", components: [GormInit], package_path: "/database" }
                ]
              },
              {
                type: ServiceLayer,
                name: "TransactionService",
                layers: [
                  { type: RepoLayer, name: "TransactionBaseDB", components: [GormInit], package_path: "/database" },
                  { type: RepoLayer, name: "TransactionTypeDB", components: [GormInit], package_path: "/database" },
                  { type: RepoLayer, name: "AggregatedTransactionTypeDB", components: [GormInit], package_path: "/database" },
                  { type: RepoLayer, name: "AggregatedTypeDB", components: [GormInit], package_path: "/database" },
                  { type: RepoLayer, name: "ViewInfoDB", components: [GormInit], package_path: "/database" },
                  { type: GrpcClient, name: "ProjectTransactions" },
                  { type: GrpcClient, name: "Card" },
                  { type: GrpcClient, name: "Commission" },
                  { type: GrpcClient, name: "Merchant" },
                  { type: GrpcClient, name: "Terminal" }
                ]
              },
              {
                type: ServiceLayer,
                name: "CardSetterService",
                layers: [
                  { type: RepoLayer, name: "TransactionCardInfoDB", components: [GormInit], package_path: "/database" },
                  { type: GrpcClient, name: "Card" }
                ]
              },
              {
                type: ServiceLayer,
                name: "CardGetterService",
                layers: [
                  { type: GrpcClient, name: "Card" },
                  { type: GrpcClient, name: "Terminal" }
                ]
              },
              {
                type: ServiceLayer,
                name: "CardProcessorService",
                components: [JetStreamContext]
                layers: [
                  { type: GrpcClient, name: "Card" }
                ]
              },
              {
                type: ServiceLayer,
                name: "AcquirerService",
                layers: [
                  { type: RepoLayer, name: "TransactionInfoDB", components: [GormInit], package_path: "/database" },
                  { type: RepoLayer, name: "TransactionDB", components: [GormInit], package_path: "/database" },
                  { type: GrpcClient, name: "Terminal" }
                ]
              },
              {
                type: ServiceLayer,
                name: "ThreeDSConfirmService",
                layers: [
                  { type: RepoLayer, name: "TransactionDB", components: [GormInit], package_path: "/database" },
                  { type: GrpcClient, name: "MultiAcquiring" }
                ]
              },
              {
                type: ServiceLayer,
                name: "TransactionCallBackService",
                layers: [
                  { type: RepoLayer, name: "TransactionBaseDB", components: [GormInit], package_path: "/database" },
                  { type: RepoLayer, name: "TransactionInfoDB", components: [GormInit], package_path: "/database" },
                  { type: RepoLayer, name: "AdditionalDataDB", components: [GormInit], package_path: "/database" },
                  { type: RepoLayer, name: "TransactionCallbackStatusDB", components: [GormInit], package_path: "/database" },
                  { type: RepoLayer, name: "ProjectRest", components: [BackoffPolicy, HttpLogger], package_path: "/rest" },
                  { type: GrpcClient, name: "Merchant" },
                  { type: GrpcClient, name: "Card" },
                  { type: GrpcClient, name: "Terminal" }
                ]
              },
              {
                type: ServiceLayer,
                name: "PayInManagerService",
                components: [GTransactionManager, JetStreamContext]
                layers: [
                  { type: RepoLayer, name: "TransactionInfoDB", components: [GormInit], package_path: "/database" },
                  { type: RepoLayer, name: "AggregatedTransactionTypeDB", components: [GormInit], package_path: "/database" },
                  { type: RepoLayer, name: "TransactionDB", components: [GormInit], package_path: "/database" },
                  { type: RepoLayer, name: "TransactionBaseDB", components: [GormInit], package_path: "/database" },
                  { type: GrpcClient, name: "MultiAcquiring" }
                ]
                vault_configs: [
                  { key: "DOMAIN", type: String },
                  { key: "THREEDS_PATH_V1", type: String }
                ]
              },
              {
                type: ServiceLayer,
                name: "AdditionalDataService",
                layers: [
                  { type: RepoLayer, name: "AdditionalDataDB", components: [GormInit], package_path: "/database" }
                ]
              },
              {
                type: ServiceLayer,
                name: "TransactionInfoService",
                layers: [
                  { type: RepoLayer, name: "TransactionInfoDB", components: [GormInit], package_path: "/database" },
                  { type: RepoLayer, name: "TransactionJobsDB", components: [GormInit], package_path: "/database" }
                ]
              },
              {
                type: ServiceLayer,
                name: "CommissionService",
                layers: [
                  { type: RepoLayer, name: "AggregatedTransactionTypeDB", components: [GormInit], package_path: "/database" },
                  { type: RepoLayer, name: "TransactionBaseDB", components: [GormInit], package_path: "/database" },
                  { type: RepoLayer, name: "TransactionDB", components: [GormInit], package_path: "/database" },
                  { type: GrpcClient, name: "Commission" }
                ]
              },
              {
                type: ServiceLayer,
                name: "ReceiptService",
                components: [JetStreamContext]
                vault_configs: [
                  { key: "RECEIPT_HTML_FILE", type: String },
                  { key: "EMAIL_SENDER", type: String },
                  { key: "RECEIPT_TITLE", type: String },
                  { key: "CDN_URL", type: String }
                ]
                layers: [
                  { type: RepoLayer, name: "LibreOfficeRest", components: [HttpLogger], package_path: "/rest", vault_configs: [ { key: "LIBRE_OFFICE_URL", type: String } ] },
                  { type: GrpcClient, name: "ViewCrafter" }
                ]
              },
              {
                type: ServiceLayer,
                name: "TransactionUpdateService",
                layers: [
                  { type: RepoLayer, name: "TransactionDB", components: [GormInit], package_path: "/database" }
                ]
              },
              {
                type: ServiceLayer,
                name: "ProjectSettingService",
                layers: [
                  { type: GrpcClient, name: "Project" }
                ]
              },
              {
                type: ServiceLayer,
                name: "AntiFraudManagerService",
                layers: [
                  { type: RepoLayer, name: "AggregatedTransactionTypeDB", components: [GormInit], package_path: "/database" },
                  { type: RepoLayer, name: "AggregatedTypeDB", components: [GormInit], package_path: "/database" },
                  { type: GrpcClient, name: "Collector" }
                ]
              },
              {
                type: ServiceLayer,
                name: "BillingService",
                layers: [
                  { type: RepoLayer, name: "TransactionBaseDB", components: [GormInit], package_path: "/database" },
                  { type: GrpcClient, name: "Billing" },
                  { type: GrpcClient, name: "Commission" }
                ]
              }
            ]
          }, // PayInService
          {
            type: UseCaseLayer,
            name: "PayOutService"
            layers: [
              {
                type: ServiceLayer,
                name: "ProjectService",
                layers: [
                  { type: RepoLayer, name: "ProjectRest", components: [BackoffPolicy, HttpLogger], package_path: "/rest" },
                  { type: GrpcClient, name: "Merchant" }
                ]
              },
              {
                type: ServiceLayer,
                name: "TransactionHelperService",
                vault_configs: [
                  { key: "PAYMENT_VIEW_URL", type: String }
                ]
              },
              {
                type: ServiceLayer,
                name: "TransactionVerifier",
                layers: [
                  { type: RepoLayer, name: "ProjectRest", components: [BackoffPolicy, HttpLogger], package_path: "/rest" },
                  { type: GrpcClient, name: "Card" },
                  { type: GrpcClient, name: "ProjectTransactions" },
                  { type: GrpcClient, name: "Merchant" }
                ]
              },
              {
                type: ServiceLayer,
                name: "TransactionService",
                layers: [
                  { type: RepoLayer, name: "TransactionBaseDB", components: [GormInit], package_path: "/database" },
                  { type: RepoLayer, name: "TransactionTypeDB", components: [GormInit], package_path: "/database" },
                  { type: RepoLayer, name: "AggregatedTransactionTypeDB", components: [GormInit], package_path: "/database" },
                  { type: RepoLayer, name: "AggregatedTypeDB", components: [GormInit], package_path: "/database" },
                  { type: RepoLayer, name: "ViewInfoDB", components: [GormInit], package_path: "/database" },
                  { type: GrpcClient, name: "ProjectTransactions" },
                  { type: GrpcClient, name: "Card" },
                  { type: GrpcClient, name: "Commission" },
                  { type: GrpcClient, name: "Merchant" },
                  { type: GrpcClient, name: "Terminal" }
                ]
              },
              {
                type: ServiceLayer,
                name: "CardSetterService",
                layers: [
                  { type: RepoLayer, name: "TransactionCardInfoDB", components: [GormInit], package_path: "/database" },
                  { type: GrpcClient, name: "Card" }
                ]
              },
              {
                type: ServiceLayer,
                name: "CardGetterService",
                layers: [
                  { type: GrpcClient, name: "Card" },
                  { type: GrpcClient, name: "Terminal" }
                ]
              },
              {
                type: ServiceLayer,
                name: "CardProcessorService",
                components: [JetStreamContext]
                layers: [
                  { type: GrpcClient, name: "Card" }
                ]
              },
              {
                type: ServiceLayer,
                name: "AcquirerService",
                layers: [
                  { type: RepoLayer, name: "TransactionInfoDB", components: [GormInit], package_path: "/database" },
                  { type: RepoLayer, name: "TransactionDB", components: [GormInit], package_path: "/database" },
                  { type: GrpcClient, name: "Terminal" }
                ]
              },
              {
                type: ServiceLayer,
                name: "TransactionCallBackService",
                layers: [
                  { type: RepoLayer, name: "TransactionBaseDB", components: [GormInit], package_path: "/database" },
                  { type: RepoLayer, name: "TransactionInfoDB", components: [GormInit], package_path: "/database" },
                  { type: RepoLayer, name: "AdditionalDataDB", components: [GormInit], package_path: "/database" },
                  { type: RepoLayer, name: "TransactionCallbackStatusDB", components: [GormInit], package_path: "/database" },
                  { type: RepoLayer, name: "ProjectRest", components: [BackoffPolicy, HttpLogger], package_path: "/rest" },
                  { type: GrpcClient, name: "Merchant" },
                  { type: GrpcClient, name: "Card" },
                  { type: GrpcClient, name: "Terminal" }
                ]
              },
              {
                type: ServiceLayer,
                name: "PayOutManagerService",
                components: [GTransactionManager]
                layers: [
                  { type: RepoLayer, name: "TransactionBaseDB", components: [GormInit], package_path: "/database" },
                  { type: RepoLayer, name: "TransactionInfoDB", components: [GormInit], package_path: "/database" },
                  { type: RepoLayer, name: "TransactionDB", components: [GormInit], package_path: "/database" },
                  { type: RepoLayer, name: "AggregatedTransactionTypeDB", components: [GormInit], package_path: "/database" },
                  { type: RepoLayer, name: "TransactionTypeDB", components: [GormInit], package_path: "/database" },
                  { type: GrpcClient, name: "MultiAcquiring" }
                ]
              },
              {
                type: ServiceLayer,
                name: "AdditionalDataService",
                layers: [
                  { type: RepoLayer, name: "AdditionalDataDB", components: [GormInit], package_path: "/database" }
                ]
              },
              {
                type: ServiceLayer,
                name: "TransactionInfoService",
                layers: [
                  { type: RepoLayer, name: "TransactionInfoDB", components: [GormInit], package_path: "/database" },
                  { type: RepoLayer, name: "TransactionJobsDB", components: [GormInit], package_path: "/database" }
                ]
              },
              {
                type: ServiceLayer,
                name: "TransactionTypeService",
                layers: [
                  { type: RepoLayer, name: "TransactionTypeDB", components: [GormInit], package_path: "/database" }
                ]
              },
              {
                type: ServiceLayer,
                name: "ReceiptService",
                components: [JetStreamContext]
                vault_configs: [
                  { key: "RECEIPT_HTML_FILE", type: String },
                  { key: "EMAIL_SENDER", type: String },
                  { key: "RECEIPT_TITLE", type: String },
                  { key: "CDN_URL", type: String }
                ]
                layers: [
                  { type: RepoLayer, name: "LibreOfficeRest", components: [HttpLogger], package_path: "/rest", vault_configs: [ { key: "LIBRE_OFFICE_URL", type: String } ] },
                  { type: GrpcClient, name: "ViewCrafter" }
                ]
              },
              {
                type: ServiceLayer,
                name: "CommissionService",
                layers: [
                  { type: RepoLayer, name: "AggregatedTransactionTypeDB", components: [GormInit], package_path: "/database" },
                  { type: RepoLayer, name: "TransactionBaseDB", components: [GormInit], package_path: "/database" },
                  { type: RepoLayer, name: "TransactionDB", components: [GormInit], package_path: "/database" },
                  { type: GrpcClient, name: "Commission" }
                ]
              },
              {
                type: ServiceLayer,
                name: "ProjectSettingService",
                layers: [
                  { type: GrpcClient, name: "Project" }
                ]
              },
              {
                type: ServiceLayer,
                name: "AntiFraudManagerService",
                layers: [
                  { type: RepoLayer, name: "AggregatedTransactionTypeDB", components: [GormInit], package_path: "/database" },
                  { type: RepoLayer, name: "AggregatedTypeDB", components: [GormInit], package_path: "/database" },
                  { type: GrpcClient, name: "Collector" }
                ]
              },
              {
                type: ServiceLayer,
                name: "BillingService",
                layers: [
                  { type: RepoLayer, name: "TransactionBaseDB", components: [GormInit], package_path: "/database" },
                  { type: GrpcClient, name: "Billing" },
                  { type: GrpcClient, name: "Commission" }
                ]
              }
            ]
          }, // PayOutService
          {
            type: UseCaseLayer,
            name: "ThreeDS"
            components: [JetStreamContext]
            layers: [
              {
                type: ServiceLayer,
                name: "ProjectService",
                layers: [
                  { type: RepoLayer, name: "ProjectRest", components: [BackoffPolicy, HttpLogger], package_path: "/rest" },
                  { type: GrpcClient, name: "Merchant" }
                ]
              },
              {
                type: ServiceLayer,
                name: "TransactionHelperService",
                vault_configs: [
                  { key: "PAYMENT_VIEW_URL", type: String }
                ]
              },
              {
                type: ServiceLayer,
                name: "TransactionVerifier",
                layers: [
                  { type: RepoLayer, name: "ProjectRest", components: [BackoffPolicy, HttpLogger], package_path: "/rest" },
                  { type: GrpcClient, name: "Card" },
                  { type: GrpcClient, name: "ProjectTransactions" },
                  { type: GrpcClient, name: "Merchant" }
                ]
              },
              {
                type: ServiceLayer,
                name: "AcquirerService",
                layers: [
                  { type: RepoLayer, name: "TransactionInfoDB", components: [GormInit], package_path: "/database" },
                  { type: RepoLayer, name: "TransactionDB", components: [GormInit], package_path: "/database" },
                  { type: GrpcClient, name: "Terminal" }
                ]
              },
              {
                type: ServiceLayer,
                name: "TransactionService",
                layers: [
                  { type: RepoLayer, name: "TransactionBaseDB", components: [GormInit], package_path: "/database" },
                  { type: RepoLayer, name: "TransactionTypeDB", components: [GormInit], package_path: "/database" },
                  { type: RepoLayer, name: "AggregatedTransactionTypeDB", components: [GormInit], package_path: "/database" },
                  { type: RepoLayer, name: "AggregatedTypeDB", components: [GormInit], package_path: "/database" },
                  { type: RepoLayer, name: "ViewInfoDB", components: [GormInit], package_path: "/database" },
                  { type: GrpcClient, name: "ProjectTransactions" },
                  { type: GrpcClient, name: "Card" },
                  { type: GrpcClient, name: "Commission" },
                  { type: GrpcClient, name: "Merchant" },
                  { type: GrpcClient, name: "Terminal" }
                ]
              },
              {
                type: ServiceLayer,
                name: "ThreeDSConfirmService",
                layers: [
                  { type: RepoLayer, name: "TransactionDB", components: [GormInit], package_path: "/database" },
                  { type: GrpcClient, name: "MultiAcquiring" }
                ]
              },
              {
                type: ServiceLayer,
                name: "TransactionCallBackService",
                layers: [
                  { type: RepoLayer, name: "TransactionBaseDB", components: [GormInit], package_path: "/database" },
                  { type: RepoLayer, name: "TransactionInfoDB", components: [GormInit], package_path: "/database" },
                  { type: RepoLayer, name: "AdditionalDataDB", components: [GormInit], package_path: "/database" },
                  { type: RepoLayer, name: "TransactionCallbackStatusDB", components: [GormInit], package_path: "/database" },
                  { type: RepoLayer, name: "ProjectRest", components: [BackoffPolicy, HttpLogger], package_path: "/rest" },
                  { type: GrpcClient, name: "Merchant" },
                  { type: GrpcClient, name: "Card" },
                  { type: GrpcClient, name: "Terminal" }
                ]
              },
              {
                type: ServiceLayer,
                name: "PayInManagerService",
                components: [GTransactionManager, JetStreamContext]
                layers: [
                  { type: RepoLayer, name: "TransactionInfoDB", components: [GormInit], package_path: "/database" },
                  { type: RepoLayer, name: "AggregatedTransactionTypeDB", components: [GormInit], package_path: "/database" },
                  { type: RepoLayer, name: "TransactionDB", components: [GormInit], package_path: "/database" },
                  { type: RepoLayer, name: "TransactionBaseDB", components: [GormInit], package_path: "/database" },
                  { type: GrpcClient, name: "MultiAcquiring" }
                ]
                vault_configs: [
                  { key: "DOMAIN", type: String },
                  { key: "THREEDS_PATH_V1", type: String }
                ]
              },
              {
                type: ServiceLayer,
                name: "CardSetterService",
                layers: [
                  { type: RepoLayer, name: "TransactionCardInfoDB", components: [GormInit], package_path: "/database" },
                  { type: GrpcClient, name: "Card" }
                ]
              },
              {
                type: ServiceLayer,
                name: "CardGetterService",
                layers: [
                  { type: GrpcClient, name: "Card" },
                  { type: GrpcClient, name: "Terminal" }
                ]
              },
              {
                type: ServiceLayer,
                name: "CardProcessorService",
                components: [JetStreamContext]
                layers: [
                  { type: GrpcClient, name: "Card" }
                ]
              },
              {
                type: ServiceLayer,
                name: "ReceiptService",
                components: [JetStreamContext]
                vault_configs: [
                  { key: "RECEIPT_HTML_FILE", type: String },
                  { key: "EMAIL_SENDER", type: String },
                  { key: "RECEIPT_TITLE", type: String },
                  { key: "CDN_URL", type: String }
                ]
                layers: [
                  { type: RepoLayer, name: "LibreOfficeRest", components: [HttpLogger], package_path: "/rest", vault_configs: [ { key: "LIBRE_OFFICE_URL", type: String } ] },
                  { type: GrpcClient, name: "ViewCrafter" }
                ]
              },
              {
                type: ServiceLayer,
                name: "ProjectSettingService",
                layers: [
                  { type: GrpcClient, name: "Project" }
                ]
              },
              {
                type: ServiceLayer,
                name: "BillingService",
                layers: [
                  { type: RepoLayer, name: "TransactionBaseDB", components: [GormInit], package_path: "/database" },
                  { type: GrpcClient, name: "Billing" },
                  { type: GrpcClient, name: "Commission" }
                ]
              }
            ]
          }, // ThreeDS
          {
            type: UseCaseLayer,
            name: "ApplePayService"
            layers: [
              {
                type: ServiceLayer,
                name: "ProjectService",
                layers: [
                  { type: RepoLayer, name: "ProjectRest", components: [BackoffPolicy, HttpLogger], package_path: "/rest" },
                  { type: GrpcClient, name: "Merchant" }
                ]
              },
              {
                type: ServiceLayer,
                name: "TransactionHelperService",
                vault_configs: [
                  { key: "PAYMENT_VIEW_URL", type: String }
                ]
              },
              {
                type: ServiceLayer,
                name: "TransactionVerifier",
                layers: [
                  { type: RepoLayer, name: "ProjectRest", components: [BackoffPolicy, HttpLogger], package_path: "/rest" },
                  { type: GrpcClient, name: "Card" },
                  { type: GrpcClient, name: "ProjectTransactions" },
                  { type: GrpcClient, name: "Merchant" }
                ]
              },
              {
                type: ServiceLayer,
                name: "TransactionService",
                layers: [
                  { type: RepoLayer, name: "TransactionBaseDB", components: [GormInit], package_path: "/database" },
                  { type: RepoLayer, name: "TransactionTypeDB", components: [GormInit], package_path: "/database" },
                  { type: RepoLayer, name: "AggregatedTransactionTypeDB", components: [GormInit], package_path: "/database" },
                  { type: RepoLayer, name: "AggregatedTypeDB", components: [GormInit], package_path: "/database" },
                  { type: RepoLayer, name: "ViewInfoDB", components: [GormInit], package_path: "/database" },
                  { type: GrpcClient, name: "ProjectTransactions" },
                  { type: GrpcClient, name: "Card" },
                  { type: GrpcClient, name: "Commission" },
                  { type: GrpcClient, name: "Merchant" },
                  { type: GrpcClient, name: "Terminal" }
                ]
              },
              {
                type: ServiceLayer,
                name: "CardSetterService",
                layers: [
                  { type: RepoLayer, name: "TransactionCardInfoDB", components: [GormInit], package_path: "/database" },
                  { type: GrpcClient, name: "Card" }
                ]
              },
              {
                type: ServiceLayer,
                name: "CardGetterService",
                layers: [
                  { type: GrpcClient, name: "Card" },
                  { type: GrpcClient, name: "Terminal" }
                ]
              },
              {
                type: ServiceLayer,
                name: "CardProcessorService",
                components: [JetStreamContext]
                layers: [
                  { type: GrpcClient, name: "Card" }
                ]
              },
              {
                type: ServiceLayer,
                name: "AcquirerService",
                layers: [
                  { type: RepoLayer, name: "TransactionInfoDB", components: [GormInit], package_path: "/database" },
                  { type: RepoLayer, name: "TransactionDB", components: [GormInit], package_path: "/database" },
                  { type: GrpcClient, name: "Terminal" }
                ]
              },
              {
                type: ServiceLayer,
                name: "ThreeDSConfirmService",
                layers: [
                  { type: RepoLayer, name: "TransactionDB", components: [GormInit], package_path: "/database" },
                  { type: GrpcClient, name: "MultiAcquiring" }
                ]
              },
              {
                type: ServiceLayer,
                name: "TransactionCallBackService",
                layers: [
                  { type: RepoLayer, name: "TransactionBaseDB", components: [GormInit], package_path: "/database" },
                  { type: RepoLayer, name: "TransactionInfoDB", components: [GormInit], package_path: "/database" },
                  { type: RepoLayer, name: "AdditionalDataDB", components: [GormInit], package_path: "/database" },
                  { type: RepoLayer, name: "TransactionCallbackStatusDB", components: [GormInit], package_path: "/database" },
                  { type: RepoLayer, name: "ProjectRest", components: [BackoffPolicy, HttpLogger], package_path: "/rest" },
                  { type: GrpcClient, name: "Merchant" },
                  { type: GrpcClient, name: "Card" },
                  { type: GrpcClient, name: "Terminal" }
                ]
              },
              {
                type: ServiceLayer,
                name: "PayInManagerService",
                components: [GTransactionManager, JetStreamContext]
                layers: [
                  { type: RepoLayer, name: "TransactionInfoDB", components: [GormInit], package_path: "/database" },
                  { type: RepoLayer, name: "AggregatedTransactionTypeDB", components: [GormInit], package_path: "/database" },
                  { type: RepoLayer, name: "TransactionDB", components: [GormInit], package_path: "/database" },
                  { type: RepoLayer, name: "TransactionBaseDB", components: [GormInit], package_path: "/database" },
                  { type: GrpcClient, name: "MultiAcquiring" }
                ]
                vault_configs: [
                  { key: "DOMAIN", type: String },
                  { key: "THREEDS_PATH_V1", type: String }
                ]
              },
              {
                type: ServiceLayer,
                name: "ApplePayManagerService",
                layers: [
                  { type: RepoLayer, name: "TransactionDB", components: [GormInit], package_path: "/database" },
                  { type: RepoLayer, name: "TransactionTypeDB", components: [GormInit], package_path: "/database" },
                  { type: RepoLayer, name: "AggregatedTransactionTypeDB", components: [GormInit], package_path: "/database" },
                  { type: GrpcClient, name: "SmartPay" },
                  { type: GrpcClient, name: "MultiAcquiring" },
                  { type: GrpcClient, name: "Card" }
                ]
              },
              {
                type: ServiceLayer,
                name: "TransactionTypeService",
                layers: [
                  { type: RepoLayer, name: "TransactionTypeDB", components: [GormInit], package_path: "/database" }
                ]
              },
              {
                type: ServiceLayer,
                name: "CommissionService",
                layers: [
                  { type: RepoLayer, name: "AggregatedTransactionTypeDB", components: [GormInit], package_path: "/database" },
                  { type: RepoLayer, name: "TransactionBaseDB", components: [GormInit], package_path: "/database" },
                  { type: RepoLayer, name: "TransactionDB", components: [GormInit], package_path: "/database" },
                  { type: GrpcClient, name: "Commission" }
                ]
              },
              {
                type: ServiceLayer,
                name: "AdditionalDataService",
                layers: [
                  { type: RepoLayer, name: "AdditionalDataDB", components: [GormInit], package_path: "/database" }
                ]
              },
              {
                type: ServiceLayer,
                name: "AntiFraudManagerService",
                layers: [
                  { type: RepoLayer, name: "AggregatedTransactionTypeDB", components: [GormInit], package_path: "/database" },
                  { type: RepoLayer, name: "AggregatedTypeDB", components: [GormInit], package_path: "/database" },
                  { type: GrpcClient, name: "Collector" }
                ]
              },
              {
                type: ServiceLayer,
                name: "BillingService",
                layers: [
                  { type: RepoLayer, name: "TransactionBaseDB", components: [GormInit], package_path: "/database" },
                  { type: GrpcClient, name: "Billing" },
                  { type: GrpcClient, name: "Commission" }
                ]
              }
            ]
          }, // ApplePayService
          {
            type: UseCaseLayer,
            name: "GooglePayService"
            layers: [
              {
                type: ServiceLayer,
                name: "ProjectService",
                layers: [
                  { type: RepoLayer, name: "ProjectRest", components: [BackoffPolicy, HttpLogger], package_path: "/rest" },
                  { type: GrpcClient, name: "Merchant" }
                ]
              },
              {
                type: ServiceLayer,
                name: "PayInManagerService",
                components: [GTransactionManager, JetStreamContext]
                layers: [
                  { type: RepoLayer, name: "TransactionInfoDB", components: [GormInit], package_path: "/database" },
                  { type: RepoLayer, name: "AggregatedTransactionTypeDB", components: [GormInit], package_path: "/database" },
                  { type: RepoLayer, name: "TransactionDB", components: [GormInit], package_path: "/database" },
                  { type: RepoLayer, name: "TransactionBaseDB", components: [GormInit], package_path: "/database" },
                  { type: GrpcClient, name: "MultiAcquiring" }
                ]
                vault_configs: [
                  { key: "DOMAIN", type: String },
                  { key: "THREEDS_PATH_V1", type: String }
                ]
              },
              {
                type: ServiceLayer,
                name: "TransactionHelperService",
                vault_configs: [
                  { key: "PAYMENT_VIEW_URL", type: String }
                ]
              },
              {
                type: ServiceLayer,
                name: "TransactionVerifier",
                layers: [
                  { type: RepoLayer, name: "ProjectRest", components: [BackoffPolicy, HttpLogger], package_path: "/rest" },
                  { type: GrpcClient, name: "Card" },
                  { type: GrpcClient, name: "ProjectTransactions" },
                  { type: GrpcClient, name: "Merchant" }
                ]
              },
              {
                type: ServiceLayer,
                name: "TransactionService",
                layers: [
                  { type: RepoLayer, name: "TransactionBaseDB", components: [GormInit], package_path: "/database" },
                  { type: RepoLayer, name: "TransactionTypeDB", components: [GormInit], package_path: "/database" },
                  { type: RepoLayer, name: "AggregatedTransactionTypeDB", components: [GormInit], package_path: "/database" },
                  { type: RepoLayer, name: "AggregatedTypeDB", components: [GormInit], package_path: "/database" },
                  { type: RepoLayer, name: "ViewInfoDB", components: [GormInit], package_path: "/database" },
                  { type: GrpcClient, name: "ProjectTransactions" },
                  { type: GrpcClient, name: "Card" },
                  { type: GrpcClient, name: "Commission" },
                  { type: GrpcClient, name: "Merchant" },
                  { type: GrpcClient, name: "Terminal" }
                ]
              },
              {
                type: ServiceLayer,
                name: "CardSetterService",
                layers: [
                  { type: RepoLayer, name: "TransactionCardInfoDB", components: [GormInit], package_path: "/database" },
                  { type: GrpcClient, name: "Card" }
                ]
              },
              {
                type: ServiceLayer,
                name: "CardGetterService",
                layers: [
                  { type: GrpcClient, name: "Card" },
                  { type: GrpcClient, name: "Terminal" }
                ]
              },
              {
                type: ServiceLayer,
                name: "CardProcessorService",
                components: [JetStreamContext]
                layers: [
                  { type: GrpcClient, name: "Card" }
                ]
              },
              {
                type: ServiceLayer,
                name: "AcquirerService",
                layers: [
                  { type: RepoLayer, name: "TransactionInfoDB", components: [GormInit], package_path: "/database" },
                  { type: RepoLayer, name: "TransactionDB", components: [GormInit], package_path: "/database" },
                  { type: GrpcClient, name: "Terminal" }
                ]
              },
              {
                type: ServiceLayer,
                name: "ThreeDSConfirmService",
                layers: [
                  { type: RepoLayer, name: "TransactionDB", components: [GormInit], package_path: "/database" },
                  { type: GrpcClient, name: "MultiAcquiring" }
                ]
              },
              {
                type: ServiceLayer,
                name: "TransactionCallBackService",
                layers: [
                  { type: RepoLayer, name: "TransactionBaseDB", components: [GormInit], package_path: "/database" },
                  { type: RepoLayer, name: "TransactionInfoDB", components: [GormInit], package_path: "/database" },
                  { type: RepoLayer, name: "AdditionalDataDB", components: [GormInit], package_path: "/database" },
                  { type: RepoLayer, name: "TransactionCallbackStatusDB", components: [GormInit], package_path: "/database" },
                  { type: RepoLayer, name: "ProjectRest", components: [BackoffPolicy, HttpLogger], package_path: "/rest" },
                  { type: GrpcClient, name: "Merchant" },
                  { type: GrpcClient, name: "Card" },
                  { type: GrpcClient, name: "Terminal" }
                ]
              },
              {
                type: ServiceLayer,
                name: "GooglePayManagerService",
                vault_configs: [
                  { key: "DOMAIN", type: String },
                  { key: "THREEDS_PATH_V1", type: String },
                  { key: "GOOGLE_PAY_GATEWAY", type: String }
                ]
                layers: [
                  { type: RepoLayer, name: "TransactionTypeDB", components: [GormInit], package_path: "/database" },
                  { type: RepoLayer, name: "TransactionDB", components: [GormInit], package_path: "/database" },
                  { type: RepoLayer, name: "AggregatedTransactionTypeDB", components: [GormInit], package_path: "/database" },
                  { type: GrpcClient, name: "SmartPay" },
                  { type: GrpcClient, name: "Merchant" },
                  { type: GrpcClient, name: "MultiAcquiring" }
                ]
              },
              {
                type: ServiceLayer,
                name: "TransactionTypeService",
                layers: [
                  { type: RepoLayer, name: "TransactionTypeDB", components: [GormInit], package_path: "/database" }
                ]
              },
              {
                type: ServiceLayer,
                name: "CommissionService",
                layers: [
                  { type: RepoLayer, name: "AggregatedTransactionTypeDB", components: [GormInit], package_path: "/database" },
                  { type: RepoLayer, name: "TransactionBaseDB", components: [GormInit], package_path: "/database" },
                  { type: RepoLayer, name: "TransactionDB", components: [GormInit], package_path: "/database" },
                  { type: GrpcClient, name: "Commission" }
                ]
              },
              {
                type: ServiceLayer,
                name: "AdditionalDataService",
                layers: [
                  { type: RepoLayer, name: "AdditionalDataDB", components: [GormInit], package_path: "/database" }
                ]
              },
              {
                type: ServiceLayer,
                name: "AntiFraudManagerService",
                layers: [
                  { type: RepoLayer, name: "AggregatedTransactionTypeDB", components: [GormInit], package_path: "/database" },
                  { type: RepoLayer, name: "AggregatedTypeDB", components: [GormInit], package_path: "/database" },
                  { type: GrpcClient, name: "Collector" }
                ]
              },
              {
                type: ServiceLayer,
                name: "BillingService",
                layers: [
                  { type: RepoLayer, name: "TransactionBaseDB", components: [GormInit], package_path: "/database" },
                  { type: GrpcClient, name: "Billing" },
                  { type: GrpcClient, name: "Commission" }
                ]
              }
            ]
          }, // GooglePayService
          {
            type: UseCaseLayer,
            name: "CardLinkService"
            layers: [
              {
                type: ServiceLayer,
                name: "ProjectService",
                layers: [
                  { type: RepoLayer, name: "ProjectRest", components: [BackoffPolicy, HttpLogger], package_path: "/rest" },
                  { type: GrpcClient, name: "Merchant" }
                ]
              },
              {
                type: ServiceLayer,
                name: "TransactionHelperService",
                vault_configs: [
                  { key: "PAYMENT_VIEW_URL", type: String }
                ]
              },
              {
                type: ServiceLayer,
                name: "TransactionVerifier",
                layers: [
                  { type: RepoLayer, name: "ProjectRest", components: [BackoffPolicy, HttpLogger], package_path: "/rest" },
                  { type: GrpcClient, name: "Card" },
                  { type: GrpcClient, name: "ProjectTransactions" },
                  { type: GrpcClient, name: "Merchant" }
                ]
              },
              {
                type: ServiceLayer,
                name: "TransactionService",
                layers: [
                  { type: RepoLayer, name: "TransactionBaseDB", components: [GormInit], package_path: "/database" },
                  { type: RepoLayer, name: "TransactionTypeDB", components: [GormInit], package_path: "/database" },
                  { type: RepoLayer, name: "AggregatedTransactionTypeDB", components: [GormInit], package_path: "/database" },
                  { type: RepoLayer, name: "AggregatedTypeDB", components: [GormInit], package_path: "/database" },
                  { type: RepoLayer, name: "ViewInfoDB", components: [GormInit], package_path: "/database" },
                  { type: GrpcClient, name: "ProjectTransactions" },
                  { type: GrpcClient, name: "Card" },
                  { type: GrpcClient, name: "Commission" },
                  { type: GrpcClient, name: "Merchant" },
                  { type: GrpcClient, name: "Terminal" }
                ]
              },
              {
                type: ServiceLayer,
                name: "TransactionInfoService",
                layers: [
                  { type: RepoLayer, name: "TransactionInfoDB", components: [GormInit], package_path: "/database" },
                  { type: RepoLayer, name: "TransactionJobsDB", components: [GormInit], package_path: "/database" }
                ]
              },
              {
                type: ServiceLayer,
                name: "CardSetterService",
                layers: [
                  { type: RepoLayer, name: "TransactionCardInfoDB", components: [GormInit], package_path: "/database" },
                  { type: GrpcClient, name: "Card" }
                ]
              },
              {
                type: ServiceLayer,
                name: "CardGetterService",
                layers: [
                  { type: GrpcClient, name: "Card" },
                  { type: GrpcClient, name: "Terminal" }
                ]
              },
              {
                type: ServiceLayer,
                name: "CardProcessorService",
                components: [JetStreamContext]
                layers: [
                  { type: GrpcClient, name: "Card" }
                ]
              },
              {
                type: ServiceLayer,
                name: "AcquirerService",
                layers: [
                  { type: RepoLayer, name: "TransactionInfoDB", components: [GormInit], package_path: "/database" },
                  { type: RepoLayer, name: "TransactionDB", components: [GormInit], package_path: "/database" },
                  { type: GrpcClient, name: "Terminal" }
                ]
              },
              {
                type: ServiceLayer,
                name: "ThreeDSConfirmService",
                layers: [
                  { type: RepoLayer, name: "TransactionDB", components: [GormInit], package_path: "/database" },
                  { type: GrpcClient, name: "MultiAcquiring" }
                ]
              },
              {
                type: ServiceLayer,
                name: "TransactionCallBackService",
                layers: [
                  { type: RepoLayer, name: "TransactionBaseDB", components: [GormInit], package_path: "/database" },
                  { type: RepoLayer, name: "TransactionInfoDB", components: [GormInit], package_path: "/database" },
                  { type: RepoLayer, name: "AdditionalDataDB", components: [GormInit], package_path: "/database" },
                  { type: RepoLayer, name: "TransactionCallbackStatusDB", components: [GormInit], package_path: "/database" },
                  { type: RepoLayer, name: "ProjectRest", components: [BackoffPolicy, HttpLogger], package_path: "/rest" },
                  { type: GrpcClient, name: "Merchant" },
                  { type: GrpcClient, name: "Card" },
                  { type: GrpcClient, name: "Terminal" }
                ]
              },
              {
                type: ServiceLayer,
                name: "CardLinkManagerService",
                components: [GTransactionManager]
                layers: [
                  { type: RepoLayer, name: "TransactionBaseDB", components: [GormInit], package_path: "/database" },
                  { type: RepoLayer, name: "TransactionInfoDB", components: [GormInit], package_path: "/database" },
                  { type: RepoLayer, name: "AdditionalDataDB", components: [GormInit], package_path: "/database" }
                ]
              },
              {
                type: ServiceLayer,
                name: "PayInManagerService",
                components: [GTransactionManager, JetStreamContext]
                layers: [
                  { type: RepoLayer, name: "TransactionInfoDB", components: [GormInit], package_path: "/database" },
                  { type: RepoLayer, name: "AggregatedTransactionTypeDB", components: [GormInit], package_path: "/database" },
                  { type: RepoLayer, name: "TransactionDB", components: [GormInit], package_path: "/database" },
                  { type: RepoLayer, name: "TransactionBaseDB", components: [GormInit], package_path: "/database" },
                  { type: GrpcClient, name: "MultiAcquiring" }
                ]
                vault_configs: [
                  { key: "DOMAIN", type: String },
                  { key: "THREEDS_PATH_V1", type: String }
                ]
              },
              {
                type: ServiceLayer,
                name: "RefundManagerService",
                components: [JetStreamContext]
                layers: [
                  { type: GrpcClient, name: "Refund" }
                ]
              },
              {
                type: ServiceLayer,
                name: "CommissionService",
                layers: [
                  { type: RepoLayer, name: "AggregatedTransactionTypeDB", components: [GormInit], package_path: "/database" },
                  { type: RepoLayer, name: "TransactionBaseDB", components: [GormInit], package_path: "/database" },
                  { type: RepoLayer, name: "TransactionDB", components: [GormInit], package_path: "/database" },
                  { type: GrpcClient, name: "Commission" }
                ]
              },
              {
                type: ServiceLayer,
                name: "AntiFraudManagerService",
                layers: [
                  { type: RepoLayer, name: "AggregatedTransactionTypeDB", components: [GormInit], package_path: "/database" },
                  { type: RepoLayer, name: "AggregatedTypeDB", components: [GormInit], package_path: "/database" },
                  { type: GrpcClient, name: "Collector" }
                ]
              },
              {
                type: ServiceLayer,
                name: "BillingService",
                layers: [
                  { type: RepoLayer, name: "TransactionBaseDB", components: [GormInit], package_path: "/database" },
                  { type: GrpcClient, name: "Billing" },
                  { type: GrpcClient, name: "Commission" }
                ]
              }
            ]
          }, // CardLinkService
          {
            type: UseCaseLayer,
            name: "Status"
            layers: [
              {
                type: ServiceLayer,
                name: "TransactionService",
                layers: [
                  { type: RepoLayer, name: "TransactionBaseDB", components: [GormInit], package_path: "/database" },
                  { type: RepoLayer, name: "TransactionTypeDB", components: [GormInit], package_path: "/database" },
                  { type: RepoLayer, name: "AggregatedTransactionTypeDB", components: [GormInit], package_path: "/database" },
                  { type: RepoLayer, name: "AggregatedTypeDB", components: [GormInit], package_path: "/database" },
                  { type: RepoLayer, name: "ViewInfoDB", components: [GormInit], package_path: "/database" },
                  { type: GrpcClient, name: "ProjectTransactions" },
                  { type: GrpcClient, name: "Card" },
                  { type: GrpcClient, name: "Commission" },
                  { type: GrpcClient, name: "Merchant" },
                  { type: GrpcClient, name: "Terminal" }
                ]
              },
              {
                type: ServiceLayer,
                name: "TransactionStatusService",
                components: [],
                layers: [
                  { type: RepoLayer, name: "TransactionStatusDB", components: [GormInit], package_path: "/database" },
                  { type: RepoLayer, name: "TransactionBaseDB", components: [GormInit], package_path: "/database" },
                  { type: RepoLayer, name: "TransactionInfoDB", components: [GormInit], package_path: "/database" },
                  { type: RepoLayer, name: "AggregatedTransactionTypeDB", components: [GormInit], package_path: "/database" },
                  { type: RepoLayer, name: "AggregatedTypeDB", components: [GormInit], package_path: "/database" },
                  { type: GrpcClient, name: "MultiAcquiring" },
                  { type: GrpcClient, name: "Terminal" }
                ]
              },
              {
                type: ServiceLayer,
                name: "TransactionStatusUpdaterService",
                components: [GTransactionManager],
                layers: [
                  { type: RepoLayer, name: "TransactionBaseDB", components: [GormInit], package_path: "/database" },
                  { type: RepoLayer, name: "TransactionDB", components: [GormInit], package_path: "/database" },
                  { type: RepoLayer, name: "AggregatedTransactionTypeDB", components: [GormInit], package_path: "/database" },
                  { type: RepoLayer, name: "AggregatedTypeDB", components: [GormInit], package_path: "/database" },
                  { type: GrpcClient, name: "Billing" }
                ]
              }
            ]
          }, // Status
          {
            type: UseCaseLayer,
            name: "Type",
            layers: [
              {
                type: ServiceLayer,
                name: "TransactionTypeService",
                layers: [
                  { type: RepoLayer, name: "TransactionTypeDB", components: [GormInit], package_path: "/database" }
                ]
              }
            ]
          }, // Type
          {
            type: UseCaseLayer,
            name: "TransactionService"
            layers: [
              {
                type: ServiceLayer,
                name: "TransactionService",
                layers: [
                  { type: RepoLayer, name: "TransactionBaseDB", components: [GormInit], package_path: "/database" },
                  { type: RepoLayer, name: "TransactionTypeDB", components: [GormInit], package_path: "/database" },
                  { type: RepoLayer, name: "AggregatedTransactionTypeDB", components: [GormInit], package_path: "/database" },
                  { type: RepoLayer, name: "AggregatedTypeDB", components: [GormInit], package_path: "/database" },
                  { type: RepoLayer, name: "ViewInfoDB", components: [GormInit], package_path: "/database" },
                  { type: GrpcClient, name: "ProjectTransactions" },
                  { type: GrpcClient, name: "Card" },
                  { type: GrpcClient, name: "Commission" },
                  { type: GrpcClient, name: "Merchant" },
                  { type: GrpcClient, name: "Terminal" }
                ]
              },
              {
                type: ServiceLayer,
                name: "TransactionTypeService",
                layers: [
                  { type: RepoLayer, name: "TransactionTypeDB", components: [GormInit], package_path: "/database" }
                ]
              },
              {
                type: ServiceLayer,
                name: "TransactionCallBackService",
                layers: [
                  { type: RepoLayer, name: "TransactionBaseDB", components: [GormInit], package_path: "/database" },
                  { type: RepoLayer, name: "TransactionInfoDB", components: [GormInit], package_path: "/database" },
                  { type: RepoLayer, name: "AdditionalDataDB", components: [GormInit], package_path: "/database" },
                  { type: RepoLayer, name: "TransactionCallbackStatusDB", components: [GormInit], package_path: "/database" },
                  { type: RepoLayer, name: "ProjectRest", components: [BackoffPolicy, HttpLogger], package_path: "/rest" },
                  { type: GrpcClient, name: "Merchant" },
                  { type: GrpcClient, name: "Card" },
                  { type: GrpcClient, name: "Terminal" }
                ]
              },
              {
                type: ServiceLayer,
                name: "TransactionAnchorerService",
                layers: [
                  { type: RepoLayer, name: "TransactionAnchorerDB", components: [GormInit], package_path: "/database" },
                  { type: GrpcClient, name: "Card" },
                  { type: GrpcClient, name: "Terminal" },
                  { type: GrpcClient, name: "Refund" }
                ]
              },
              {
                type: ServiceLayer,
                name: "ProjectService",
                layers: [
                  { type: RepoLayer, name: "ProjectRest", components: [BackoffPolicy, HttpLogger], package_path: "/rest" },
                  { type: GrpcClient, name: "Merchant" }
                ]
              },
              {
                type: ServiceLayer,
                name: "TransactionHelperService",
                vault_configs: [
                  { key: "PAYMENT_VIEW_URL", type: String }
                ]
              },
              {
                type: ServiceLayer,
                name: "AcquirerService",
                layers: [
                  { type: RepoLayer, name: "TransactionInfoDB", components: [GormInit], package_path: "/database" },
                  { type: RepoLayer, name: "TransactionDB", components: [GormInit], package_path: "/database" },
                  { type: GrpcClient, name: "Terminal" }
                ]
              },
              {
                type: ServiceLayer,
                name: "RefundManagerService",
                components: [JetStreamContext]
                layers: [
                  { type: GrpcClient, name: "Refund" }
                ]
              },
              {
                type: ServiceLayer,
                name: "ReceiptService",
                components: [JetStreamContext]
                vault_configs: [
                  { key: "RECEIPT_HTML_FILE", type: String },
                  { key: "EMAIL_SENDER", type: String },
                  { key: "RECEIPT_TITLE", type: String },
                  { key: "CDN_URL", type: String }
                ]
                layers: [
                  { type: RepoLayer, name: "LibreOfficeRest", components: [HttpLogger], package_path: "/rest", vault_configs: [ { key: "LIBRE_OFFICE_URL", type: String } ] },
                  { type: GrpcClient, name: "ViewCrafter" }
                ]
              }
            ]
          }, // TransactionService
          {
            name: "TwoStageService"
            type: UseCaseLayer
            layers: [
              {
                type: ServiceLayer,
                name: "TransactionService",
                layers: [
                  { type: RepoLayer, name: "TransactionBaseDB", components: [GormInit], package_path: "/database" },
                  { type: RepoLayer, name: "TransactionTypeDB", components: [GormInit], package_path: "/database" },
                  { type: RepoLayer, name: "AggregatedTransactionTypeDB", components: [GormInit], package_path: "/database" },
                  { type: RepoLayer, name: "AggregatedTypeDB", components: [GormInit], package_path: "/database" },
                  { type: RepoLayer, name: "ViewInfoDB", components: [GormInit], package_path: "/database" },
                  { type: GrpcClient, name: "ProjectTransactions" },
                  { type: GrpcClient, name: "Card" },
                  { type: GrpcClient, name: "Commission" },
                  { type: GrpcClient, name: "Merchant" },
                  { type: GrpcClient, name: "Terminal" }
                ]
              },
              {
                type: ServiceLayer,
                name: "TransactionCallBackService",
                layers: [
                  { type: RepoLayer, name: "TransactionBaseDB", components: [GormInit], package_path: "/database" },
                  { type: RepoLayer, name: "TransactionInfoDB", components: [GormInit], package_path: "/database" },
                  { type: RepoLayer, name: "AdditionalDataDB", components: [GormInit], package_path: "/database" },
                  { type: RepoLayer, name: "TransactionCallbackStatusDB", components: [GormInit], package_path: "/database" },
                  { type: RepoLayer, name: "ProjectRest", components: [BackoffPolicy, HttpLogger], package_path: "/rest" },
                  { type: GrpcClient, name: "Merchant" },
                  { type: GrpcClient, name: "Card" },
                  { type: GrpcClient, name: "Terminal" }
                ]
              },
              {
                type: ServiceLayer,
                name: "TransactionVerifier",
                layers: [
                  { type: RepoLayer, name: "ProjectRest", components: [BackoffPolicy, HttpLogger], package_path: "/rest" },
                  { type: GrpcClient, name: "Card" },
                  { type: GrpcClient, name: "ProjectTransactions" },
                  { type: GrpcClient, name: "Merchant" }
                ]
              },
              {
                type: ServiceLayer,
                name: "AcquirerService",
                layers: [
                  { type: RepoLayer, name: "TransactionInfoDB", components: [GormInit], package_path: "/database" },
                  { type: RepoLayer, name: "TransactionDB", components: [GormInit], package_path: "/database" },
                  { type: GrpcClient, name: "Terminal" }
                ]
              },
              {
                type: ServiceLayer,
                name: "TwoStageVerifierService",
                layers: [
                  { type: RepoLayer, name: "OperationDB", components: [GormInit], package_path: "/database" },
                  { type: RepoLayer, name: "ProjectRest", components: [BackoffPolicy, HttpLogger], package_path: "/rest" },
                  { type: GrpcClient, name: "Merchant" }
                ]
              },
              {
                type: ServiceLayer,
                name: "OperationService",
                layers: [
                  { type: RepoLayer, name: "OperationDB", components: [GormInit], package_path: "/database" }
                ]
              },
              {
                type: ServiceLayer,
                name: "TwoStageManagerService",
                layers: [
                  { type: RepoLayer, name: "TransactionDB", components: [GormInit], package_path: "/database" },
                  { type: RepoLayer, name: "OperationTypeDB", components: [GormInit], package_path: "/database" },
                  { type: RepoLayer, name: "OperationDB", components: [GormInit], package_path: "/database" },
                  { type: GrpcClient, name: "MultiAcquiring" }
                ]
              },
              {
                type: ServiceLayer,
                name: "BillingService",
                layers: [
                  { type: RepoLayer, name: "TransactionBaseDB", components: [GormInit], package_path: "/database" },
                  { type: GrpcClient, name: "Billing" },
                  { type: GrpcClient, name: "Commission" }
                ]
              }
            ]
          }, // TwoStageService
          {
            type: UseCaseLayer,
            name: "ClientCardService"
            layers: [
              {
                type: ServiceLayer,
                name: "ProjectService",
                layers: [
                  { type: RepoLayer, name: "ProjectRest", components: [BackoffPolicy, HttpLogger], package_path: "/rest" },
                  { type: GrpcClient, name: "Merchant" }
                ]
              },
              {
                type: ServiceLayer,
                name: "CardSetterService",
                layers: [
                  { type: RepoLayer, name: "TransactionCardInfoDB", components: [GormInit], package_path: "/database" },
                  { type: GrpcClient, name: "Card" }
                ]
              },
              {
                type: ServiceLayer,
                name: "CardGetterService",
                layers: [
                  { type: GrpcClient, name: "Card" },
                  { type: GrpcClient, name: "Terminal" }
                ]
              },
              {
                type: ServiceLayer,
                name: "CardProcessorService",
                components: [JetStreamContext]
                layers: [
                  { type: GrpcClient, name: "Card" }
                ]
              }
            ]
          }, // ClientCardService
          {
            name: "Authorize"
            type: UseCaseLayer
            layers: [
              {
                type: ServiceLayer,
                name: "TransactionService",
                layers: [
                  { type: RepoLayer, name: "TransactionBaseDB", components: [GormInit], package_path: "/database" },
                  { type: RepoLayer, name: "TransactionTypeDB", components: [GormInit], package_path: "/database" },
                  { type: RepoLayer, name: "AggregatedTransactionTypeDB", components: [GormInit], package_path: "/database" },
                  { type: RepoLayer, name: "AggregatedTypeDB", components: [GormInit], package_path: "/database" },
                  { type: RepoLayer, name: "ViewInfoDB", components: [GormInit], package_path: "/database" },
                  { type: GrpcClient, name: "ProjectTransactions" },
                  { type: GrpcClient, name: "Card" },
                  { type: GrpcClient, name: "Commission" },
                  { type: GrpcClient, name: "Merchant" },
                  { type: GrpcClient, name: "Terminal" }
                ]
              },
              {
                type: ServiceLayer,
                name: "TransactionVerifier",
                layers: [
                  { type: RepoLayer, name: "ProjectRest", components: [BackoffPolicy, HttpLogger], package_path: "/rest" },
                  { type: GrpcClient, name: "Card" },
                  { type: GrpcClient, name: "ProjectTransactions" },
                  { type: GrpcClient, name: "Merchant" }
                ]
              },
              {
                type: ServiceLayer,
                name: "ProjectService",
                layers: [
                  { type: RepoLayer, name: "ProjectRest", components: [BackoffPolicy, HttpLogger], package_path: "/rest" },
                  { type: GrpcClient, name: "Merchant" }
                ]
              },
              {
                type: ServiceLayer,
                name: "TransactionAnchorerService",
                layers: [
                  { type: RepoLayer, name: "TransactionAnchorerDB", components: [GormInit], package_path: "/database" },
                  { type: GrpcClient, name: "Card" },
                  { type: GrpcClient, name: "Terminal" },
                  { type: GrpcClient, name: "Refund" }
                ]
              }
            ]
          }, // Authorize
          {
            type: UseCaseLayer,
            name: "AdditionalDataService",
            layers: [
              {
                type: ServiceLayer,
                name: "TransactionService",
                layers: [
                  { type: RepoLayer, name: "TransactionBaseDB", components: [GormInit], package_path: "/database" },
                  { type: RepoLayer, name: "TransactionTypeDB", components: [GormInit], package_path: "/database" },
                  { type: RepoLayer, name: "AggregatedTransactionTypeDB", components: [GormInit], package_path: "/database" },
                  { type: RepoLayer, name: "AggregatedTypeDB", components: [GormInit], package_path: "/database" },
                  { type: RepoLayer, name: "ViewInfoDB", components: [GormInit], package_path: "/database" },
                  { type: GrpcClient, name: "ProjectTransactions" },
                  { type: GrpcClient, name: "Card" },
                  { type: GrpcClient, name: "Commission" },
                  { type: GrpcClient, name: "Merchant" },
                  { type: GrpcClient, name: "Terminal" }
                ]
              },
              {
                type: ServiceLayer,
                name: "AdditionalDataService",
                layers: [
                  { type: RepoLayer, name: "AdditionalDataDB", components: [GormInit], package_path: "/database" }
                ]
              }
            ]
          }, // AdditionalDataService
          {
            type: UseCaseLayer,
            name: "ProjectOrderService",
            layers: [
              {
                type: ServiceLayer,
                name: "ProjectOrderService",
                layers: [
                  { type: RepoLayer, name: "ProjectOrderDB", components: [GormInit], package_path: "/database" }
                ]
              }
            ]
          }, // ProjectOrderService
          {
            type: UseCaseLayer,
            name: "TransactionAnchorService",
            layers: [
              {
                type: ServiceLayer,
                name: "TransactionAnchorerService",
                layers: [
                  { type: RepoLayer, name: "TransactionAnchorerDB", components: [GormInit], package_path: "/database" },
                  { type: GrpcClient, name: "Card" },
                  { type: GrpcClient, name: "Terminal" },
                  { type: GrpcClient, name: "Refund" }
                ]
              }
            ]
          }, // TransactionAnchorService
          {
            name: "DonationService"
            type: UseCaseLayer
            layers: [
              {
                type: ServiceLayer,
                name: "ProjectService",
                layers: [
                  { type: RepoLayer, name: "ProjectRest", components: [BackoffPolicy, HttpLogger], package_path: "/rest" },
                  { type: GrpcClient, name: "Merchant" }
                ]
              },
              {
                type: ServiceLayer,
                name: "TransactionHelperService",
                vault_configs: [
                  { key: "PAYMENT_VIEW_URL", type: String }
                ]
              },
              {
                type: ServiceLayer,
                name: "TransactionVerifier",
                layers: [
                  { type: RepoLayer, name: "ProjectRest", components: [BackoffPolicy, HttpLogger], package_path: "/rest" },
                  { type: GrpcClient, name: "Card" },
                  { type: GrpcClient, name: "ProjectTransactions" },
                  { type: GrpcClient, name: "Merchant" }
                ]
              },
              {
                type: ServiceLayer,
                name: "TransactionTypeService",
                layers: [
                  { type: RepoLayer, name: "TransactionTypeDB", components: [GormInit], package_path: "/database" }
                ]
              },
              {
                type: ServiceLayer,
                name: "TransactionService",
                layers: [
                  { type: RepoLayer, name: "TransactionBaseDB", components: [GormInit], package_path: "/database" },
                  { type: RepoLayer, name: "TransactionTypeDB", components: [GormInit], package_path: "/database" },
                  { type: RepoLayer, name: "AggregatedTransactionTypeDB", components: [GormInit], package_path: "/database" },
                  { type: RepoLayer, name: "AggregatedTypeDB", components: [GormInit], package_path: "/database" },
                  { type: RepoLayer, name: "ViewInfoDB", components: [GormInit], package_path: "/database" },
                  { type: GrpcClient, name: "ProjectTransactions" },
                  { type: GrpcClient, name: "Card" },
                  { type: GrpcClient, name: "Commission" },
                  { type: GrpcClient, name: "Merchant" },
                  { type: GrpcClient, name: "Terminal" }
                ]
              },
              {
                type: ServiceLayer,
                name: "AcquirerService",
                layers: [
                  { type: RepoLayer, name: "TransactionInfoDB", components: [GormInit], package_path: "/database" },
                  { type: RepoLayer, name: "TransactionDB", components: [GormInit], package_path: "/database" },
                  { type: GrpcClient, name: "Terminal" }
                ]
              },
              {
                type: ServiceLayer,
                name: "ThreeDSConfirmService",
                layers: [
                  { type: RepoLayer, name: "TransactionDB", components: [GormInit], package_path: "/database" },
                  { type: GrpcClient, name: "MultiAcquiring" }
                ]
              },
              {
                type: ServiceLayer,
                name: "TransactionCallBackService",
                layers: [
                  { type: RepoLayer, name: "TransactionBaseDB", components: [GormInit], package_path: "/database" },
                  { type: RepoLayer, name: "TransactionInfoDB", components: [GormInit], package_path: "/database" },
                  { type: RepoLayer, name: "AdditionalDataDB", components: [GormInit], package_path: "/database" },
                  { type: RepoLayer, name: "TransactionCallbackStatusDB", components: [GormInit], package_path: "/database" },
                  { type: RepoLayer, name: "ProjectRest", components: [BackoffPolicy, HttpLogger], package_path: "/rest" },
                  { type: GrpcClient, name: "Merchant" },
                  { type: GrpcClient, name: "Card" },
                  { type: GrpcClient, name: "Terminal" }
                ]
              },
              {
                type: ServiceLayer,
                name: "PayInManagerService",
                components: [GTransactionManager, JetStreamContext]
                layers: [
                  { type: RepoLayer, name: "TransactionInfoDB", components: [GormInit], package_path: "/database" },
                  { type: RepoLayer, name: "AggregatedTransactionTypeDB", components: [GormInit], package_path: "/database" },
                  { type: RepoLayer, name: "TransactionDB", components: [GormInit], package_path: "/database" },
                  { type: RepoLayer, name: "TransactionBaseDB", components: [GormInit], package_path: "/database" },
                  { type: GrpcClient, name: "MultiAcquiring" }
                ]
                vault_configs: [
                  { key: "DOMAIN", type: String },
                  { key: "THREEDS_PATH_V1", type: String }
                ]
              },
              {
                type: ServiceLayer,
                name: "AdditionalDataService",
                layers: [
                  { type: RepoLayer, name: "AdditionalDataDB", components: [GormInit], package_path: "/database" }
                ]
              },
              {
                type: ServiceLayer,
                name: "TransactionInfoService",
                layers: [
                  { type: RepoLayer, name: "TransactionInfoDB", components: [GormInit], package_path: "/database" },
                  { type: RepoLayer, name: "TransactionJobsDB", components: [GormInit], package_path: "/database" }
                ]
              },
              {
                type: ServiceLayer,
                name: "CommissionService",
                layers: [
                  { type: RepoLayer, name: "AggregatedTransactionTypeDB", components: [GormInit], package_path: "/database" },
                  { type: RepoLayer, name: "TransactionBaseDB", components: [GormInit], package_path: "/database" },
                  { type: RepoLayer, name: "TransactionDB", components: [GormInit], package_path: "/database" },
                  { type: GrpcClient, name: "Commission" }
                ]
              },
              {
                type: ServiceLayer,
                name: "ReceiptService",
                components: [JetStreamContext]
                vault_configs: [
                  { key: "RECEIPT_HTML_FILE", type: String },
                  { key: "EMAIL_SENDER", type: String },
                  { key: "RECEIPT_TITLE", type: String },
                  { key: "CDN_URL", type: String }
                ]
                layers: [
                  { type: RepoLayer, name: "LibreOfficeRest", components: [HttpLogger], package_path: "/rest", vault_configs: [ { key: "LIBRE_OFFICE_URL", type: String } ] },
                  { type: GrpcClient, name: "ViewCrafter" }
                ]
              }
            ]
          }, // DonationService
          {
            type: UseCaseLayer,
            name: "AggregatedTransactionTypeService",
            layers: [
              {
                type: ServiceLayer,
                name: "AggregatedTransactionTypeService",
                layers: [
                  { type: RepoLayer, name: "AggregatedTransactionTypeDB", components: [GormInit], package_path: "/database" },
                  { type: RepoLayer, name: "AggregatedTypeDB", components: [GormInit], package_path: "/database" }
                ]
              }
            ]
          }, // AggregatedTransactionTypeService
          {
            type: UseCaseLayer,
            name: "ReceiptService",
            layers: [
              {
                type: ServiceLayer,
                name: "TransactionService",
                layers: [
                  { type: RepoLayer, name: "TransactionBaseDB", components: [GormInit], package_path: "/database" },
                  { type: RepoLayer, name: "TransactionTypeDB", components: [GormInit], package_path: "/database" },
                  { type: RepoLayer, name: "AggregatedTransactionTypeDB", components: [GormInit], package_path: "/database" },
                  { type: RepoLayer, name: "AggregatedTypeDB", components: [GormInit], package_path: "/database" },
                  { type: RepoLayer, name: "ViewInfoDB", components: [GormInit], package_path: "/database" },
                  { type: GrpcClient, name: "ProjectTransactions" },
                  { type: GrpcClient, name: "Card" },
                  { type: GrpcClient, name: "Commission" },
                  { type: GrpcClient, name: "Merchant" },
                  { type: GrpcClient, name: "Terminal" }
                ]
              },
              {
                type: ServiceLayer,
                name: "ReceiptService",
                components: [JetStreamContext]
                vault_configs: [
                  { key: "RECEIPT_HTML_FILE", type: String },
                  { key: "EMAIL_SENDER", type: String },
                  { key: "RECEIPT_TITLE", type: String },
                  { key: "CDN_URL", type: String }
                ]
                layers: [
                  { type: RepoLayer, name: "LibreOfficeRest", components: [HttpLogger], package_path: "/rest", vault_configs: [ { key: "LIBRE_OFFICE_URL", type: String } ] },
                  { type: GrpcClient, name: "ViewCrafter" }
                ]
              }
            ]
          }  // ReceiptService
        ],
      }
    ]

    natsConsumer: [
      {
        name: "Transaction"
        components: [JetStreamContext, NatsConn, NatsMessageRouter]
        layers: [
          {
            type: ServiceLayer,
            name: "TransactionStatusUpdaterService",
            components: [GTransactionManager],
            layers: [
              { type: RepoLayer, name: "TransactionBaseDB", components: [GormInit], package_path: "/database" },
              { type: RepoLayer, name: "TransactionDB", components: [GormInit], package_path: "/database" },
              { type: RepoLayer, name: "AggregatedTransactionTypeDB", components: [GormInit], package_path: "/database" },
              { type: RepoLayer, name: "AggregatedTypeDB", components: [GormInit], package_path: "/database" },
              { type: GrpcClient, name: "Billing" }
            ]
          }
        ]
      }
    ]

    grpcHandler: [
      {
        name: "Transaction"
        layers: [
          {

          },
          {
            name: "TwoStageService"
            type: UseCaseLayer
            layers: [
              {
                type: ServiceLayer,
                name: "TransactionService",
                layers: [
                  { type: RepoLayer, name: "TransactionBaseDB", components: [GormInit], package_path: "/database" },
                  { type: RepoLayer, name: "TransactionTypeDB", components: [GormInit], package_path: "/database" },
                  { type: RepoLayer, name: "AggregatedTransactionTypeDB", components: [GormInit], package_path: "/database" },
                  { type: RepoLayer, name: "AggregatedTypeDB", components: [GormInit], package_path: "/database" },
                  { type: RepoLayer, name: "ViewInfoDB", components: [GormInit], package_path: "/database" },
                  { type: GrpcClient, name: "ProjectTransactions" },
                  { type: GrpcClient, name: "Card" },
                  { type: GrpcClient, name: "Commission" },
                  { type: GrpcClient, name: "Merchant" },
                  { type: GrpcClient, name: "Terminal" }
                ]
              },
              {
                type: ServiceLayer,
                name: "TransactionCallBackService",
                layers: [
                  { type: RepoLayer, name: "TransactionBaseDB", components: [GormInit], package_path: "/database" },
                  { type: RepoLayer, name: "TransactionInfoDB", components: [GormInit], package_path: "/database" },
                  { type: RepoLayer, name: "AdditionalDataDB", components: [GormInit], package_path: "/database" },
                  { type: RepoLayer, name: "TransactionCallbackStatusDB", components: [GormInit], package_path: "/database" },
                  { type: RepoLayer, name: "ProjectRest", components: [BackoffPolicy, HttpLogger], package_path: "/rest" },
                  { type: GrpcClient, name: "Merchant" },
                  { type: GrpcClient, name: "Card" },
                  { type: GrpcClient, name: "Terminal" }
                ]
              },
              {
                type: ServiceLayer,
                name: "TransactionVerifier",
                layers: [
                  { type: RepoLayer, name: "ProjectRest", components: [BackoffPolicy, HttpLogger], package_path: "/rest" },
                  { type: GrpcClient, name: "Card" },
                  { type: GrpcClient, name: "ProjectTransactions" },
                  { type: GrpcClient, name: "Merchant" }
                ]
              },
              {
                type: ServiceLayer,
                name: "AcquirerService",
                layers: [
                  { type: RepoLayer, name: "TransactionInfoDB", components: [GormInit], package_path: "/database" },
                  { type: RepoLayer, name: "TransactionDB", components: [GormInit], package_path: "/database" },
                  { type: GrpcClient, name: "Terminal" }
                ]
              },
              {
                type: ServiceLayer,
                name: "TwoStageVerifierService",
                layers: [
                  { type: RepoLayer, name: "OperationDB", components: [GormInit], package_path: "/database" },
                  { type: RepoLayer, name: "ProjectRest", components: [BackoffPolicy, HttpLogger], package_path: "/rest" },
                  { type: GrpcClient, name: "Merchant" }
                ]
              },
              {
                type: ServiceLayer,
                name: "OperationService",
                layers: [
                  { type: RepoLayer, name: "OperationDB", components: [GormInit], package_path: "/database" }
                ]
              },
              {
                type: ServiceLayer,
                name: "TwoStageManagerService",
                layers: [
                  { type: RepoLayer, name: "TransactionDB", components: [GormInit], package_path: "/database" },
                  { type: RepoLayer, name: "OperationTypeDB", components: [GormInit], package_path: "/database" },
                  { type: RepoLayer, name: "OperationDB", components: [GormInit], package_path: "/database" },
                  { type: GrpcClient, name: "MultiAcquiring" }
                ]
              },
              {
                type: ServiceLayer,
                name: "BillingService",
                layers: [
                  { type: RepoLayer, name: "TransactionBaseDB", components: [GormInit], package_path: "/database" },
                  { type: GrpcClient, name: "Billing" },
                  { type: GrpcClient, name: "Commission" }
                ]
              }
            ]
          }, // TwoStageService
          {
            type: UseCaseLayer,
            name: "TransactionAnchorService",
            layers: [
              {
                type: ServiceLayer,
                name: "TransactionAnchorerService",
                layers: [
                  { type: RepoLayer, name: "TransactionAnchorerDB", components: [GormInit], package_path: "/database" },
                  { type: GrpcClient, name: "Card" },
                  { type: GrpcClient, name: "Terminal" },
                  { type: GrpcClient, name: "Refund" }
                ]
              }
            ]
          }, // TransactionAnchorService
          {
            type: UseCaseLayer,
            name: "AggregatedTransactionTypeService",
            layers: [
              {
                type: ServiceLayer,
                name: "AggregatedTransactionTypeService",
                layers: [
                  { type: RepoLayer, name: "AggregatedTransactionTypeDB", components: [GormInit], package_path: "/database" },
                  { type: RepoLayer, name: "AggregatedTypeDB", components: [GormInit], package_path: "/database" }
                ]
              }
            ]
          }, // AggregatedTransactionTypeService
          {
            name: "Authorize"
            type: UseCaseLayer
            layers: [
              {
                type: ServiceLayer,
                name: "TransactionService",
                layers: [
                  { type: RepoLayer, name: "TransactionBaseDB", components: [GormInit], package_path: "/database" },
                  { type: RepoLayer, name: "TransactionTypeDB", components: [GormInit], package_path: "/database" },
                  { type: RepoLayer, name: "AggregatedTransactionTypeDB", components: [GormInit], package_path: "/database" },
                  { type: RepoLayer, name: "AggregatedTypeDB", components: [GormInit], package_path: "/database" },
                  { type: RepoLayer, name: "ViewInfoDB", components: [GormInit], package_path: "/database" },
                  { type: GrpcClient, name: "ProjectTransactions" },
                  { type: GrpcClient, name: "Card" },
                  { type: GrpcClient, name: "Commission" },
                  { type: GrpcClient, name: "Merchant" },
                  { type: GrpcClient, name: "Terminal" }
                ]
              },
              {
                type: ServiceLayer,
                name: "TransactionVerifier",
                layers: [
                  { type: RepoLayer, name: "ProjectRest", components: [BackoffPolicy, HttpLogger], package_path: "/rest" },
                  { type: GrpcClient, name: "Card" },
                  { type: GrpcClient, name: "ProjectTransactions" },
                  { type: GrpcClient, name: "Merchant" }
                ]
              },
              {
                type: ServiceLayer,
                name: "ProjectService",
                layers: [
                  { type: RepoLayer, name: "ProjectRest", components: [BackoffPolicy, HttpLogger], package_path: "/rest" },
                  { type: GrpcClient, name: "Merchant" }
                ]
              },
              {
                type: ServiceLayer,
                name: "TransactionAnchorerService",
                layers: [
                  { type: RepoLayer, name: "TransactionAnchorerDB", components: [GormInit], package_path: "/database" },
                  { type: GrpcClient, name: "Card" },
                  { type: GrpcClient, name: "Terminal" },
                  { type: GrpcClient, name: "Refund" }
                ]
              }
            ]
          }, // Authorize
          {
            type: ServiceLayer,
            name: "TransactionStatusUpdaterService",
            components: [GTransactionManager],
            layers: [
              { type: RepoLayer, name: "TransactionBaseDB", components: [GormInit], package_path: "/database" },
              { type: RepoLayer, name: "TransactionDB", components: [GormInit], package_path: "/database" },
              { type: RepoLayer, name: "AggregatedTransactionTypeDB", components: [GormInit], package_path: "/database" },
              { type: RepoLayer, name: "AggregatedTypeDB", components: [GormInit], package_path: "/database" },
              { type: GrpcClient, name: "Billing" }
            ]
          },
          {
            type: ServiceLayer,
            name: "TransactionCallBackService",
            layers: [
              { type: RepoLayer, name: "TransactionBaseDB", components: [GormInit], package_path: "/database" },
              { type: RepoLayer, name: "TransactionInfoDB", components: [GormInit], package_path: "/database" },
              { type: RepoLayer, name: "AdditionalDataDB", components: [GormInit], package_path: "/database" },
              { type: RepoLayer, name: "TransactionCallbackStatusDB", components: [GormInit], package_path: "/database" },
              { type: RepoLayer, name: "ProjectRest", components: [BackoffPolicy, HttpLogger], package_path: "/rest" },
              { type: GrpcClient, name: "Merchant" },
              { type: GrpcClient, name: "Card" },
              { type: GrpcClient, name: "Terminal" }
            ]
          },
          {
            type: ServiceLayer,
            name: "TransactionsStatuserService",
            layers: [
              { type: RepoLayer, name: "TransactionsStatuserDB", components: [GormInit], package_path: "/database" },
              { type: RepoLayer, name: "TransactionBaseDB", components: [GormInit], package_path: "/database" }
            ]
          },
          {
            type: ServiceLayer,
            name: "TransactionService",
            layers: [
              { type: RepoLayer, name: "TransactionBaseDB", components: [GormInit], package_path: "/database" },
              { type: RepoLayer, name: "TransactionTypeDB", components: [GormInit], package_path: "/database" },
              { type: RepoLayer, name: "AggregatedTransactionTypeDB", components: [GormInit], package_path: "/database" },
              { type: RepoLayer, name: "AggregatedTypeDB", components: [GormInit], package_path: "/database" },
              { type: RepoLayer, name: "ViewInfoDB", components: [GormInit], package_path: "/database" },
              { type: GrpcClient, name: "ProjectTransactions" },
              { type: GrpcClient, name: "Card" },
              { type: GrpcClient, name: "Commission" },
              { type: GrpcClient, name: "Merchant" },
              { type: GrpcClient, name: "Terminal" }
            ]
          },
          {
            type: ServiceLayer,
            name: "PayOutManagerService",
            components: [GTransactionManager]
            layers: [
              { type: RepoLayer, name: "TransactionBaseDB", components: [GormInit], package_path: "/database" },
              { type: RepoLayer, name: "TransactionInfoDB", components: [GormInit], package_path: "/database" },
              { type: RepoLayer, name: "TransactionDB", components: [GormInit], package_path: "/database" },
              { type: RepoLayer, name: "AggregatedTransactionTypeDB", components: [GormInit], package_path: "/database" },
              { type: RepoLayer, name: "TransactionTypeDB", components: [GormInit], package_path: "/database" },
              { type: GrpcClient, name: "MultiAcquiring" }
            ]
          },
          {
            type: ServiceLayer,
            name: "AcquirerService",
            layers: [
              { type: RepoLayer, name: "TransactionInfoDB", components: [GormInit], package_path: "/database" },
              { type: RepoLayer, name: "TransactionDB", components: [GormInit], package_path: "/database" },
              { type: GrpcClient, name: "Terminal" }
            ]
          },
          {
            type: ServiceLayer,
            name: "ReceiptService",
            components: [JetStreamContext]
            vault_configs: [
              { key: "RECEIPT_HTML_FILE", type: String },
              { key: "EMAIL_SENDER", type: String },
              { key: "RECEIPT_TITLE", type: String },
              { key: "CDN_URL", type: String }
            ]
            layers: [
              { type: RepoLayer, name: "LibreOfficeRest", components: [HttpLogger], package_path: "/rest", vault_configs: [ { key: "LIBRE_OFFICE_URL", type: String } ] },
              { type: GrpcClient, name: "ViewCrafter" }
            ]
          },
          {
            type: ServiceLayer,
            name: "AdditionalDataService",
            layers: [
              { type: RepoLayer, name: "AdditionalDataDB", components: [GormInit], package_path: "/database" }
            ]
          },
          {
            type: ServiceLayer,
            name: "CommissionService",
            layers: [
              { type: RepoLayer, name: "AggregatedTransactionTypeDB", components: [GormInit], package_path: "/database" },
              { type: RepoLayer, name: "TransactionBaseDB", components: [GormInit], package_path: "/database" },
              { type: RepoLayer, name: "TransactionDB", components: [GormInit], package_path: "/database" },
              { type: GrpcClient, name: "Commission" }
            ]
          },
          {
            type: ServiceLayer,
            name: "BillingService",
            layers: [
              { type: RepoLayer, name: "TransactionBaseDB", components: [GormInit], package_path: "/database" },
              { type: GrpcClient, name: "Billing" },
              { type: GrpcClient, name: "Commission" }
            ]
          },
          { type: GrpcClient, name: "Project" }

        ],
      }
    ]
  };
}
