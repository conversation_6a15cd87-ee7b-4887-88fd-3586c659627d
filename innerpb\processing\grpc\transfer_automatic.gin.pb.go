// Code generated by protoc-gen-go-mvp. DO NOT EDIT.
// versions:
// - protoc-gen-go-mvp v2.0.1
// created by mvp authors

package grpc

import (
	dog "git.local/sensitive/sdk/dog"
	gin "github.com/gin-gonic/gin"
	metadata "google.golang.org/grpc/metadata"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
	http "net/http"
)

func RegisterGinTransferAutomaticRouterGroup(
	in *gin.RouterGroup,
	handler func(func(c *gin.Context) error) gin.HandlerFunc,
) error {
	service, err := NewGinTransferAutomaticService()
	if err != nil {
		return err
	}

	routerGroup := in.Group("processing.transfer_automatic.transfer_automatic.TransferAutomatic")
	routerGroup.PUT("/StartCreateTransferByRulesWorker", handler(service.StartCreateTransferByRulesWorker))
	return nil
}

func NewGinTransferAutomaticService() (GinTransferAutomaticServer, error) {
	client, err := NewPreparedTransferAutomaticClient()
	if err != nil {
		return nil, err
	}

	return &ginTransferAutomaticServer{
		client: NewLoggedTransferAutomaticClient(
			NewIamTransferAutomaticClient(client),
		),
	}, nil
}

//go:generate mockgen -destination=./grpcmock/transfer_automatic.gin.pb.go -package=grpcmock -source=transfer_automatic.gin.pb.go GinTransferAutomaticServer
type GinTransferAutomaticServer interface {
	StartCreateTransferByRulesWorker(c *gin.Context) error
}

var _ GinTransferAutomaticServer = (*ginTransferAutomaticServer)(nil)

type ginTransferAutomaticServer struct {
	client TransferAutomaticClient
}

type TransferAutomatic_StartCreateTransferByRulesWorker_Success struct {
	// Status is the status of the response. Set true
	Status bool `json:"status"`
	// Message is a human-readable explanation of the status
	Message string `json:"message"`
	// StatusCode is the GoErr status. Set 0
	StatusCode int `json:"status_code"`
	// Result is the result of the request
	Result *emptypb.Empty `json:"result"`
}

type TransferAutomatic_StartCreateTransferByRulesWorker_Failure struct {
	// Status is the status of the response. Set false
	Status bool `json:"status"`
	// Message is a human-readable explanation of the status
	Message string `json:"message"`
	// StatusCode is the GoErr status
	StatusCode int `json:"status_code"`
}

// StartCreateTransferByRulesWorker
// @Summary StartCreateTransferByRulesWorker
// @Security bearerAuth
// @ID TransferAutomatic_StartCreateTransferByRulesWorker
// @Accept json
// @Param request body CreateTransferByRulesRequest true "CreateTransferByRulesRequest"
// @Success 200 {object} TransferAutomatic_StartCreateTransferByRulesWorker_Success
// @Failure 401 {object} TransferAutomatic_StartCreateTransferByRulesWorker_Failure "goerrpkg.ErrorTypeAuthorization"
// @Failure 404 {object} TransferAutomatic_StartCreateTransferByRulesWorker_Failure "goerrpkg.ErrorTypeNotFound"
// @Failure 400 {object} TransferAutomatic_StartCreateTransferByRulesWorker_Failure "goerrpkg.ErrorTypeBadRequest"
// @Failure 500 {object} TransferAutomatic_StartCreateTransferByRulesWorker_Failure "goerrpkg.ErrorTypeUnexpectedError"
// @Failure 400 {object} TransferAutomatic_StartCreateTransferByRulesWorker_Failure "gin.ErrorTypeBind"
// @Failure 500 {object} TransferAutomatic_StartCreateTransferByRulesWorker_Failure "Undefined error"
// @Produce json
// @Router /processing.transfer_automatic.transfer_automatic.TransferAutomatic/StartCreateTransferByRulesWorker [put]
// @tags TransferAutomatic
func (s *ginTransferAutomaticServer) StartCreateTransferByRulesWorker(c *gin.Context) error {
	ctx, span := dog.CreateSpan(c.Request.Context(), "GinTransferAutomaticServer_StartCreateTransferByRulesWorker")
	defer span.End()

	var request CreateTransferByRulesRequest
	if err := c.ShouldBindJSON(&request); err != nil {
		return err
	}

	ctx = metadata.NewOutgoingContext(ctx, metadata.MD(c.Request.Header))

	response, err := s.client.StartCreateTransferByRulesWorker(ctx, &request)
	if err != nil {
		return err
	}

	c.JSON(http.StatusOK, &TransferAutomatic_StartCreateTransferByRulesWorker_Success{
		Status:     true,
		Message:    "Success",
		StatusCode: 0,
		Result:     response,
	})
	return nil
}
