edition = "2023";

package processing.splitting.splitting;

option go_package = "git.local/sensitive/innerpb/processing/grpc";

import "inner/processing/grpc/commission.proto";

service Splitting {
  rpc CalculatePaymentSplitTax(CalculatePaymentSplitTaxRequest) returns (CalculatePaymentSplitTaxResponse) {}
}

message CalculatePaymentSplitTaxRequest{
  uint64 project_id = 1;
  double amount = 2;
}

message CalculatePaymentSplitTaxResponse{
  processing.commission.commission.CommissionV1 payment_split_tax = 2;
}