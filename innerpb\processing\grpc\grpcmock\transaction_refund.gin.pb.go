// Code generated by MockGen. DO NOT EDIT.
// Source: transaction_refund.gin.pb.go

// Package grpcmock is a generated GoMock package.
package grpcmock

import (
	reflect "reflect"

	gin "github.com/gin-gonic/gin"
	gomock "github.com/golang/mock/gomock"
)

// MockGinRefundServer is a mock of GinRefundServer interface.
type MockGinRefundServer struct {
	ctrl     *gomock.Controller
	recorder *MockGinRefundServerMockRecorder
}

// MockGinRefundServerMockRecorder is the mock recorder for MockGinRefundServer.
type MockGinRefundServerMockRecorder struct {
	mock *MockGinRefundServer
}

// NewMockGinRefundServer creates a new mock instance.
func NewMockGinRefundServer(ctrl *gomock.Controller) *MockGinRefundServer {
	mock := &MockGinRefundServer{ctrl: ctrl}
	mock.recorder = &MockGinRefundServerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockGinRefundServer) EXPECT() *MockGinRefundServerMockRecorder {
	return m.recorder
}

// GetByTransactionIDV1 mocks base method.
func (m *MockGinRefundServer) GetByTransactionIDV1(c *gin.Context) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetByTransactionIDV1", c)
	ret0, _ := ret[0].(error)
	return ret0
}

// GetByTransactionIDV1 indicates an expected call of GetByTransactionIDV1.
func (mr *MockGinRefundServerMockRecorder) GetByTransactionIDV1(c interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetByTransactionIDV1", reflect.TypeOf((*MockGinRefundServer)(nil).GetByTransactionIDV1), c)
}
