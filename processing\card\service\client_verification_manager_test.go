package service

import (
	"context"
	"errors"
	"git.local/sensitive/innerpb/processing/grpc/grpcmock"
	"git.local/sensitive/testsdk"
	"testing"

	"github.com/golang/mock/gomock"
	"github.com/stretchr/testify/require"

	"git.local/sensitive/innerpb/processing/grpc"
	"git.local/sensitive/processing/card/model"
	"git.local/sensitive/processing/card/repository/database/databasemocks"
	"git.local/sensitive/processing/card/schema"
)

func TestBindVericationUserIDToClient(t *testing.T) {
	type bindVerificationUserOp struct {
		inputProjectID, inputVerUserID uint64
		inputProjectClientID           string
		otuputErr                      error
	}

	tests := []struct {
		name                                string
		reqProjectID, reqVerificationUserID uint64
		reqProjectClientID                  string
		wantErr                             error
		bindVerificationUser                bindVerificationUserOp
	}{
		{
			name:                  "error",
			reqProjectID:          1,
			reqVerificationUserID: 1,
			reqProjectClientID:    "something",
			wantErr:               errors.New("some error"),
			bindVerificationUser: bindVerificationUserOp{
				inputProjectID:       1,
				inputVerUserID:       1,
				inputProjectClientID: "something",
				otuputErr:            errors.New("some error"),
			},
		},
		{
			name:                  "success",
			reqProjectID:          1,
			reqVerificationUserID: 1,
			reqProjectClientID:    "something",
			wantErr:               nil,
			bindVerificationUser: bindVerificationUserOp{
				inputProjectID:       1,
				inputVerUserID:       1,
				inputProjectClientID: "something",
				otuputErr:            nil,
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctrl := gomock.NewController(t)
			defer ctrl.Finish()

			clientVerificationManagerMock := databasemocks.NewMockClientVerificationManager(ctrl)

			clientVerificationManagerMock.EXPECT().BindVerificationUserIDToClient(
				gomock.Any(),
				tt.bindVerificationUser.inputProjectID,
				tt.bindVerificationUser.inputProjectClientID,
				tt.bindVerificationUser.inputVerUserID,
			).Return(
				tt.bindVerificationUser.otuputErr,
			)

			s := NewClientVerificationManagerService(clientVerificationManagerMock, nil)

			err := s.BindVerificationUserIDToClient(
				context.Background(),
				tt.reqProjectID,
				tt.reqProjectClientID,
				tt.reqVerificationUserID,
			)
			if tt.wantErr != nil {
				require.Error(t, err)
				require.Equal(t, tt.wantErr, err)
			} else {
				require.NoError(t, err)
			}
		})
	}
}

func TestUnbindVerificationUserIDFromClient(t *testing.T) {
	type unbindOp struct {
		inputProjectID       uint64
		inputProjectClientID string
		outputErr            error
	}

	tests := []struct {
		name          string
		reqProjectID  uint64
		reqClientID   string
		wantErr       error
		unbindRequest unbindOp
	}{
		{
			name:         "error",
			reqProjectID: 1,
			reqClientID:  "something",
			wantErr:      errors.New("some error"),
			unbindRequest: unbindOp{
				inputProjectID:       1,
				inputProjectClientID: "something",
				outputErr:            errors.New("some error"),
			},
		},
		{
			name:         "success",
			reqProjectID: 1,
			reqClientID:  "something",
			wantErr:      nil,
			unbindRequest: unbindOp{
				inputProjectID:       1,
				inputProjectClientID: "something",
				outputErr:            nil,
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctrl := gomock.NewController(t)
			defer ctrl.Finish()

			cvmMock := databasemocks.NewMockClientVerificationManager(ctrl)

			cvmMock.EXPECT().
				UnbindVerificationUserIDFromClient(
					gomock.Any(),
					tt.unbindRequest.inputProjectID,
					tt.unbindRequest.inputProjectClientID,
				).
				Return(tt.unbindRequest.outputErr)

			s := NewClientVerificationManagerService(cvmMock, nil)

			err := s.UnbindVerificationUserIDFromClient(
				context.Background(),
				tt.reqProjectID,
				tt.reqClientID,
			)

			if tt.wantErr != nil {
				require.Error(t, err)
				require.Equal(t, tt.wantErr, err)
			} else {
				require.NoError(t, err)
			}
		})
	}
}

func TestBlockClient(t *testing.T) {
	type blockOp struct {
		inputProjectID       uint64
		inputProjectClientID string
		outputErr            error
	}

	tests := []struct {
		name        string
		reqProjID   uint64
		reqClientID string
		wantErr     error
		block       blockOp
	}{
		{
			name:        "error",
			reqProjID:   1,
			reqClientID: "something",
			wantErr:     errors.New("some error"),
			block: blockOp{
				inputProjectID:       1,
				inputProjectClientID: "something",
				outputErr:            errors.New("some error"),
			},
		},
		{
			name:        "success",
			reqProjID:   1,
			reqClientID: "something",
			wantErr:     nil,
			block: blockOp{
				inputProjectID:       1,
				inputProjectClientID: "something",
				outputErr:            nil,
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctrl := gomock.NewController(t)
			defer ctrl.Finish()

			cvmMock := databasemocks.NewMockClientVerificationManager(ctrl)

			cvmMock.EXPECT().
				BlockClient(
					gomock.Any(),
					tt.block.inputProjectID,
					tt.block.inputProjectClientID,
				).
				Return(tt.block.outputErr)

			s := NewClientVerificationManagerService(cvmMock, nil)

			err := s.BlockClient(
				context.Background(),
				tt.reqProjID,
				tt.reqClientID,
			)

			if tt.wantErr != nil {
				require.Error(t, err)
				require.Equal(t, tt.wantErr, err)
			} else {
				require.NoError(t, err)
			}
		})
	}
}

func TestGetClientActiveness(t *testing.T) {
	type activeOp struct {
		inputProjectID       uint64
		inputProjectClientID string
		outputActive         bool
		outputErr            error
	}

	tests := []struct {
		name        string
		reqProjID   uint64
		reqClientID string
		wantActive  bool
		wantErr     error
		active      activeOp
	}{
		{
			name:        "error",
			reqProjID:   1,
			reqClientID: "something",
			wantActive:  false,
			wantErr:     errors.New("some error"),
			active: activeOp{
				inputProjectID:       1,
				inputProjectClientID: "something",
				outputActive:         false,
				outputErr:            errors.New("some error"),
			},
		},
		{
			name:        "success_active",
			reqProjID:   1,
			reqClientID: "something",
			wantActive:  true,
			wantErr:     nil,
			active: activeOp{
				inputProjectID:       1,
				inputProjectClientID: "something",
				outputActive:         true,
				outputErr:            nil,
			},
		},
		{
			name:        "success_inactive",
			reqProjID:   1,
			reqClientID: "something",
			wantActive:  false,
			wantErr:     nil,
			active: activeOp{
				inputProjectID:       1,
				inputProjectClientID: "something",
				outputActive:         false,
				outputErr:            nil,
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctrl := gomock.NewController(t)
			defer ctrl.Finish()

			cvmMock := databasemocks.NewMockClientVerificationManager(ctrl)

			cvmMock.EXPECT().
				GetClientActiveness(
					gomock.Any(),
					tt.active.inputProjectID,
					tt.active.inputProjectClientID,
				).
				Return(tt.active.outputActive, tt.active.outputErr)

			s := NewClientVerificationManagerService(cvmMock, nil)

			active, err := s.GetClientActiveness(
				context.Background(),
				tt.reqProjID,
				tt.reqClientID,
			)

			if tt.wantErr != nil {
				require.Error(t, err)
				require.Equal(t, tt.wantErr, err)
			} else {
				require.NoError(t, err)
				require.Equal(t, tt.wantActive, active)
			}
		})
	}
}

func TestGetClientByVerificationID(t *testing.T) {
	type getByVerificationIDOp struct {
		inputVerificationID uint64
		output              []model.Client
		outputErr           error
	}

	type getMerchantInfoOp struct {
		isCalled  bool
		input     *grpc.GetMerchantInfoRequestV1
		output    *grpc.GetMerchantInfoResponseV1
		outputErr error
	}

	tests := []struct {
		name              string
		reqVerificationID uint64
		want              []schema.GetClientByVerificationResponse
		wantErr           error
		getByVerification getByVerificationIDOp
		getMerchantInfo   getMerchantInfoOp
	}{
		{
			name:              "error_getting_by_verification_id",
			reqVerificationID: 1,
			wantErr:           errors.New("some error"),
			want:              nil,
			getByVerification: getByVerificationIDOp{
				inputVerificationID: 1,
				output:              nil,
				outputErr:           errors.New("some error"),
			},
		},
		{
			name:              "error_getting_merchant_info",
			reqVerificationID: 1,
			wantErr:           errors.New("some error"),
			want:              nil,
			getByVerification: getByVerificationIDOp{
				inputVerificationID: 1,
				output: []model.Client{
					{
						Id:                 1,
						ProjectId:          10,
						VerificationUserID: 1,
						ProjectClientId:    "some project client id",
						IsBlocked:          false,
					},
				},
				outputErr: nil,
			},
			getMerchantInfo: getMerchantInfoOp{
				isCalled: true,
				input: &grpc.GetMerchantInfoRequestV1{
					ProjectId: testsdk.Ptr(uint64(10)),
				},
				output:    nil,
				outputErr: errors.New("some error"),
			},
		},
		{
			name:              "success",
			reqVerificationID: 1,
			wantErr:           nil,
			want: []schema.GetClientByVerificationResponse{
				{
					MerchantName:       "some name",
					ClientID:           1,
					ProjectId:          10,
					VerificationUserID: 1,
					ProjectClientId:    "some project client id",
				},
			},
			getByVerification: getByVerificationIDOp{
				inputVerificationID: 1,
				output: []model.Client{
					{
						Id:                 1,
						ProjectId:          10,
						VerificationUserID: 1,
						ProjectClientId:    "some project client id",
						IsBlocked:          false,
					},
				},
				outputErr: nil,
			},
			getMerchantInfo: getMerchantInfoOp{
				isCalled: true,
				input: &grpc.GetMerchantInfoRequestV1{
					ProjectId: testsdk.Ptr(uint64(10)),
				},
				output: &grpc.GetMerchantInfoResponseV1{
					Name: testsdk.Ptr("some name"),
				},
				outputErr: nil,
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctrl := gomock.NewController(t)
			defer ctrl.Finish()

			merchantCliMock := grpcmock.NewMockMerchantClient(ctrl)
			cvmMock := databasemocks.NewMockClientVerificationManager(ctrl)

			cvmMock.EXPECT().GetByVerificationID(
				gomock.Any(),
				tt.getByVerification.inputVerificationID,
			).Return(tt.getByVerification.output, tt.getByVerification.outputErr)

			if tt.getMerchantInfo.isCalled {
				merchantCliMock.EXPECT().GetMerchantInfo(
					gomock.Any(),
					tt.getMerchantInfo.input,
				).Return(
					tt.getMerchantInfo.output,
					tt.getMerchantInfo.outputErr,
				)
			}

			s := NewClientVerificationManagerService(cvmMock, merchantCliMock)

			res, err := s.GetClientByVerificationID(context.Background(), tt.reqVerificationID)
			if tt.wantErr != nil {
				require.Error(t, err)
				require.Equal(t, tt.wantErr, err)
			} else {
				require.NoError(t, err)
			}

			require.Equal(t, tt.want, res)
		})
	}
}
