package service

import (
	"context"
	"crypto/aes"
	"errors"
	"testing"

	"github.com/golang/mock/gomock"
	"github.com/stretchr/testify/require"

	"git.local/sensitive/processing/acquirer/model"
	"git.local/sensitive/processing/acquirer/repository/database/databasemocks"
	"git.local/sensitive/processing/acquirer/schema"
	"git.local/sensitive/sdk/dog/e2e"
)

func TestActiveFindActiveTerminalsByProject(t *testing.T) {
	type findActiveTerminalOp struct {
		inputPrID        uint64
		inputPaymentType uint64
		output           model.Terminals
		outputErr        error
	}

	tests := []struct {
		name               string
		reqProjectID       uint64
		reqPaymentType     uint64
		want               model.Terminals
		wantErr            error
		appConfig          map[string]any
		findActiveTerminal findActiveTerminalOp
	}{
		{
			name:           "error when requesting active terminals",
			reqProjectID:   1,
			reqPaymentType: 2,
			want:           nil,
			wantErr:        errors.New("some error"),
			appConfig: map[string]any{
				"SYMMETRIC_KEY": normalKey,
			},
			findActiveTerminal: findActiveTerminalOp{
				inputPrID:        1,
				inputPaymentType: 2,
				output:           nil,
				outputErr:        errors.New("some error"),
			},
		},
		{
			name:           "bad key",
			reqProjectID:   1,
			reqPaymentType: 2,
			want:           nil,
			wantErr:        aes.KeySizeError(3),
			appConfig: map[string]any{
				"SYMMETRIC_KEY": badKey,
			},
			findActiveTerminal: findActiveTerminalOp{
				inputPrID:        1,
				inputPaymentType: 2,
				output: model.Terminals{
					{
						ID:                   22,
						AcquirerID:           1,
						Status:               model.TerminalOn,
						EncryptedConfig:      defaultEncryptedConfig,
						TwoStageTimeout:      10,
						AccountNumber:        "********************",
						IsTransit:            false,
						Description:          "some desc",
						AcquirerTerminalName: "test terminal",
					},
					{
						ID:                   23,
						AcquirerID:           1,
						Status:               model.TerminalOff,
						EncryptedConfig:      defaultEncryptedConfig,
						TwoStageTimeout:      10,
						AccountNumber:        "********************",
						IsTransit:            false,
						Description:          "some desc desc",
						AcquirerTerminalName: "test test terminal",
					},
				},
				outputErr: nil,
			},
		},
		{
			name:           "success",
			reqProjectID:   1,
			reqPaymentType: 2,
			want: model.Terminals{
				{
					ID:                   22,
					AcquirerID:           1,
					Status:               model.TerminalOn,
					EncryptedConfig:      "{\"my-name-is\":\"skrillex\"}",
					TwoStageTimeout:      10,
					AccountNumber:        "********************",
					IsTransit:            false,
					Description:          "some desc",
					AcquirerTerminalName: "test terminal",
				},
				{
					ID:                   23,
					AcquirerID:           1,
					Status:               model.TerminalOff,
					EncryptedConfig:      "{\"my-name-is\":\"skrillex\"}",
					TwoStageTimeout:      10,
					AccountNumber:        "********************",
					IsTransit:            false,
					Description:          "some desc desc",
					AcquirerTerminalName: "test test terminal",
				},
			},
			wantErr: nil,
			appConfig: map[string]any{
				"SYMMETRIC_KEY": normalKey,
			},
			findActiveTerminal: findActiveTerminalOp{
				inputPrID:        1,
				inputPaymentType: 2,
				output: model.Terminals{
					{
						ID:                   22,
						AcquirerID:           1,
						Status:               model.TerminalOn,
						EncryptedConfig:      defaultEncryptedConfig,
						TwoStageTimeout:      10,
						AccountNumber:        "********************",
						IsTransit:            false,
						Description:          "some desc",
						AcquirerTerminalName: "test terminal",
					},
					{
						ID:                   23,
						AcquirerID:           1,
						Status:               model.TerminalOff,
						EncryptedConfig:      defaultEncryptedConfig,
						TwoStageTimeout:      10,
						AccountNumber:        "********************",
						IsTransit:            false,
						Description:          "some desc desc",
						AcquirerTerminalName: "test test terminal",
					},
				},
				outputErr: nil,
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctrl := gomock.NewController(t)
			defer ctrl.Finish()

			c := context.Background()
			ctx, cancel := context.WithCancel(c)
			e2e.InitAppE2E(t, ctx, cancel, nil, nil, tt.appConfig)

			terminalDBMock := databasemocks.NewMockTerminaler(ctrl)

			terminalActiveService := NewTerminalActiveService(terminalDBMock)

			terminalDBMock.EXPECT().FindActiveTerminalsByProject(
				gomock.Any(),
				tt.findActiveTerminal.inputPrID,
				tt.findActiveTerminal.inputPaymentType,
			).Return(tt.findActiveTerminal.output, tt.findActiveTerminal.outputErr).Times(1)

			resp, err := terminalActiveService.FindActiveTerminalsByProject(ctx, tt.reqProjectID, tt.reqPaymentType)
			if tt.wantErr != nil {
				require.Error(t, err)
				require.Equal(t, tt.wantErr, err)
			} else {
				require.NoError(t, err)
			}

			require.Equal(t, tt.want, resp)
		})
	}
}

func TestActiveFindActiveTerminalsByIDs(t *testing.T) {
	type findActiveTerminalOp struct {
		inputTerminalIDs []uint64
		inputProjectID   uint64
		inputPaymentType uint64
		output           []*model.Terminal
		outputErr        error
	}

	tests := []struct {
		name               string
		req                *schema.ActiveTerminalsByIDsReq
		want               []*model.Terminal
		wantErr            error
		findActiveTerminal findActiveTerminalOp
	}{
		{
			name: "error when getting terminals",
			req: &schema.ActiveTerminalsByIDsReq{
				TerminalIDs:       []uint64{22, 23},
				ProjectID:         11,
				TransactionTypeID: 2,
			},
			want:    nil,
			wantErr: errors.New("some error"),
			findActiveTerminal: findActiveTerminalOp{
				inputProjectID:   11,
				inputPaymentType: 2,
				inputTerminalIDs: []uint64{22, 23},
				output:           nil,
				outputErr:        errors.New("some error"),
			},
		},
		{
			name: "success",
			req: &schema.ActiveTerminalsByIDsReq{
				TerminalIDs:       []uint64{22, 23},
				ProjectID:         11,
				TransactionTypeID: 2,
			},
			want: []*model.Terminal{
				{
					ID:                   22,
					AcquirerID:           1,
					Status:               2,
					EncryptedConfig:      defaultEncryptedConfig,
					TwoStageTimeout:      10,
					AccountNumber:        "********************",
					IsTransit:            false,
					Description:          "some desc",
					AcquirerTerminalName: "test terminal",
				},
				{
					ID:                   23,
					AcquirerID:           2,
					Status:               1,
					EncryptedConfig:      defaultEncryptedConfig,
					TwoStageTimeout:      100,
					AccountNumber:        "********************",
					IsTransit:            false,
					Description:          "some desc",
					AcquirerTerminalName: "test terminal",
				},
			},
			wantErr: nil,
			findActiveTerminal: findActiveTerminalOp{
				inputProjectID:   11,
				inputPaymentType: 2,
				inputTerminalIDs: []uint64{22, 23},
				output: []*model.Terminal{
					{
						ID:                   22,
						AcquirerID:           1,
						Status:               2,
						EncryptedConfig:      defaultEncryptedConfig,
						TwoStageTimeout:      10,
						AccountNumber:        "********************",
						IsTransit:            false,
						Description:          "some desc",
						AcquirerTerminalName: "test terminal",
					},
					{
						ID:                   23,
						AcquirerID:           2,
						Status:               1,
						EncryptedConfig:      defaultEncryptedConfig,
						TwoStageTimeout:      100,
						AccountNumber:        "********************",
						IsTransit:            false,
						Description:          "some desc",
						AcquirerTerminalName: "test terminal",
					},
				},
				outputErr: nil,
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctrl := gomock.NewController(t)
			defer ctrl.Finish()

			c := context.Background()
			ctx, cancel := context.WithCancel(c)
			e2e.InitAppE2E(t, ctx, cancel, nil, nil, nil)

			terminalDBMock := databasemocks.NewMockTerminaler(ctrl)

			terminalActiveService := NewTerminalActiveService(terminalDBMock)

			terminalDBMock.EXPECT().FindActiveTerminalsByIDs(
				gomock.Any(),
				tt.findActiveTerminal.inputTerminalIDs,
				tt.findActiveTerminal.inputProjectID,
				tt.findActiveTerminal.inputPaymentType,
			).Return(tt.findActiveTerminal.output, tt.findActiveTerminal.outputErr).Times(1)

			resp, err := terminalActiveService.FindActiveTerminalsByIDs(ctx, tt.req)
			if tt.wantErr != nil {
				require.Error(t, err)
				require.Equal(t, tt.wantErr, err)
			} else {
				require.NoError(t, err)
			}

			require.Equal(t, tt.want, resp)
		})
	}
}
