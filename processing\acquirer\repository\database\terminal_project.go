package database

import (
	"context"

	"gorm.io/gorm"

	"git.local/sensitive/innerpb/processing/goerr"
	"git.local/sensitive/processing/acquirer/model"
	"git.local/sensitive/processing/acquirer/schema"
	"git.local/sensitive/sdk/dog"
)

type TerminalProjectDB struct {
	db *gorm.DB
}

func NewTerminalProjectDB(db *gorm.DB) TerminalProjecter {
	return &TerminalProjectDB{
		db: db,
	}
}

func (b *TerminalProjectDB) GetByID(ctx context.Context, id uint64) (
	terminalProject *model.TerminalProject,
	err error,
) {
	ctx, span := dog.CreateSpan(ctx, "TerminalProjectDB_GetById")
	defer span.End()

	err = b.db.WithContext(ctx).
		Where("id = ?", id).
		Find(&terminalProject).
		Error

	if err != nil {
		return nil, goerr.ErrDbUnexpected.WithErr(err).WithCtx(ctx)
	}

	return terminalProject, nil
}

func (r *TerminalProjectDB) Update(
	ctx context.Context,
	id uint64,
	request schema.UpdateTerminalProjectRequest,
) (err error) {
	ctx, span := dog.CreateSpan(ctx, "TerminalProjectDB_Update")
	defer span.End()

	err = r.db.WithContext(ctx).
		Model(&model.TerminalProject{}).
		Where("id = ?", id).
		Updates(map[string]interface{}{
			"project_id":          request.ProjectID,
			"transaction_type_id": request.TransactionTypeID,
			"is_active":           request.IsActive,
		}).Error
	if err != nil {
		return goerr.ErrDbUnexpected.WithErr(err).WithCtx(ctx)
	}

	return nil
}

func (t *TerminalProjectDB) UpdateStatus(
	ctx context.Context,
	id uint64,
	request schema.UpdateTerminalProjectStatusRequest,
) (err error) {
	ctx, span := dog.CreateSpan(ctx, "TerminalProjectDB_Update")
	defer span.End()

	err = t.db.WithContext(ctx).
		Model(&model.TerminalProject{}).
		Where("id = ?", id).
		Update("is_active", request.IsActive).
		Error
	if err != nil {
		return goerr.ErrDbUnexpected.WithErr(err).WithCtx(ctx)
	}

	return nil
}

func (t *TerminalProjectDB) Create(ctx context.Context, terminalProject *model.TerminalProject) (err error) {
	ctx, span := dog.CreateSpan(ctx, "TerminalProjectDB_Create")
	defer span.End()

	err = t.db.WithContext(ctx).Create(&terminalProject).Error
	if err != nil {
		return goerr.ErrDbUnexpected.WithErr(err).WithCtx(ctx)
	}

	return nil
}

func (t *TerminalProjectDB) GetByProjectID(ctx context.Context, projectID uint64) ([]*model.TerminalProject, error) {
	ctx, span := dog.CreateSpan(ctx, "TerminalProjectDB_GetByProjectID")
	defer span.End()

	query := t.db.
		WithContext(ctx).
		Model(&model.TerminalProject{}).
		Where("project_id = ?", projectID)

	var terminalProjects []*model.TerminalProject
	if err := query.Find(&terminalProjects).Error; err != nil {
		return nil, goerr.ErrDbUnexpected.WithCtx(ctx).WithErr(err)
	}

	return terminalProjects, nil
}
