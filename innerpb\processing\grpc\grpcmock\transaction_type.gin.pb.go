// Code generated by MockGen. DO NOT EDIT.
// Source: transaction_type.gin.pb.go

// Package grpcmock is a generated GoMock package.
package grpcmock

import (
	reflect "reflect"

	gin "github.com/gin-gonic/gin"
	gomock "github.com/golang/mock/gomock"
)

// MockGinTransactionTypeServer is a mock of GinTransactionTypeServer interface.
type MockGinTransactionTypeServer struct {
	ctrl     *gomock.Controller
	recorder *MockGinTransactionTypeServerMockRecorder
}

// MockGinTransactionTypeServerMockRecorder is the mock recorder for MockGinTransactionTypeServer.
type MockGinTransactionTypeServerMockRecorder struct {
	mock *MockGinTransactionTypeServer
}

// NewMockGinTransactionTypeServer creates a new mock instance.
func NewMockGinTransactionTypeServer(ctrl *gomock.Controller) *MockGinTransactionTypeServer {
	mock := &MockGinTransactionTypeServer{ctrl: ctrl}
	mock.recorder = &MockGinTransactionTypeServerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockGinTransactionTypeServer) EXPECT() *MockGinTransactionTypeServerMockRecorder {
	return m.recorder
}

// GetAggregatedTransactionType mocks base method.
func (m *MockGinTransactionTypeServer) GetAggregatedTransactionType(c *gin.Context) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAggregatedTransactionType", c)
	ret0, _ := ret[0].(error)
	return ret0
}

// GetAggregatedTransactionType indicates an expected call of GetAggregatedTransactionType.
func (mr *MockGinTransactionTypeServerMockRecorder) GetAggregatedTransactionType(c interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAggregatedTransactionType", reflect.TypeOf((*MockGinTransactionTypeServer)(nil).GetAggregatedTransactionType), c)
}

// GetAggregatedTransactionTypeByTypeID mocks base method.
func (m *MockGinTransactionTypeServer) GetAggregatedTransactionTypeByTypeID(c *gin.Context) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAggregatedTransactionTypeByTypeID", c)
	ret0, _ := ret[0].(error)
	return ret0
}

// GetAggregatedTransactionTypeByTypeID indicates an expected call of GetAggregatedTransactionTypeByTypeID.
func (mr *MockGinTransactionTypeServerMockRecorder) GetAggregatedTransactionTypeByTypeID(c interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAggregatedTransactionTypeByTypeID", reflect.TypeOf((*MockGinTransactionTypeServer)(nil).GetAggregatedTransactionTypeByTypeID), c)
}

// GetAggregatedTypeByID mocks base method.
func (m *MockGinTransactionTypeServer) GetAggregatedTypeByID(c *gin.Context) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAggregatedTypeByID", c)
	ret0, _ := ret[0].(error)
	return ret0
}

// GetAggregatedTypeByID indicates an expected call of GetAggregatedTypeByID.
func (mr *MockGinTransactionTypeServerMockRecorder) GetAggregatedTypeByID(c interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAggregatedTypeByID", reflect.TypeOf((*MockGinTransactionTypeServer)(nil).GetAggregatedTypeByID), c)
}

// GetAll mocks base method.
func (m *MockGinTransactionTypeServer) GetAll(c *gin.Context) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAll", c)
	ret0, _ := ret[0].(error)
	return ret0
}

// GetAll indicates an expected call of GetAll.
func (mr *MockGinTransactionTypeServerMockRecorder) GetAll(c interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAll", reflect.TypeOf((*MockGinTransactionTypeServer)(nil).GetAll), c)
}

// GetTransactionPayInTypes mocks base method.
func (m *MockGinTransactionTypeServer) GetTransactionPayInTypes(c *gin.Context) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetTransactionPayInTypes", c)
	ret0, _ := ret[0].(error)
	return ret0
}

// GetTransactionPayInTypes indicates an expected call of GetTransactionPayInTypes.
func (mr *MockGinTransactionTypeServerMockRecorder) GetTransactionPayInTypes(c interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetTransactionPayInTypes", reflect.TypeOf((*MockGinTransactionTypeServer)(nil).GetTransactionPayInTypes), c)
}

// GetTransactionPayOutTypes mocks base method.
func (m *MockGinTransactionTypeServer) GetTransactionPayOutTypes(c *gin.Context) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetTransactionPayOutTypes", c)
	ret0, _ := ret[0].(error)
	return ret0
}

// GetTransactionPayOutTypes indicates an expected call of GetTransactionPayOutTypes.
func (mr *MockGinTransactionTypeServerMockRecorder) GetTransactionPayOutTypes(c interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetTransactionPayOutTypes", reflect.TypeOf((*MockGinTransactionTypeServer)(nil).GetTransactionPayOutTypes), c)
}
