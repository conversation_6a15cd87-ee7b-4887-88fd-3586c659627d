// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.5.1
// - protoc             v5.29.3
// source: inner/processing/grpc/multiacquiring.proto

package grpc

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.64.0 or later.
const _ = grpc.SupportPackageIsVersion9

const (
	MultiAcquiring_PayIn_FullMethodName                              = "/processing.multiacquiring.multiacquiring.MultiAcquiring/PayIn"
	MultiAcquiring_OneClickPayIn_FullMethodName                      = "/processing.multiacquiring.multiacquiring.MultiAcquiring/OneClickPayIn"
	MultiAcquiring_ThreeDSConfirm_FullMethodName                     = "/processing.multiacquiring.multiacquiring.MultiAcquiring/ThreeDSConfirm"
	MultiAcquiring_ThreeDSResume_FullMethodName                      = "/processing.multiacquiring.multiacquiring.MultiAcquiring/ThreeDSResume"
	MultiAcquiring_PayOut_FullMethodName                             = "/processing.multiacquiring.multiacquiring.MultiAcquiring/PayOut"
	MultiAcquiring_GetBankTransactionStatus_FullMethodName           = "/processing.multiacquiring.multiacquiring.MultiAcquiring/GetBankTransactionStatus"
	MultiAcquiring_GetBankTransactionStatusUnformated_FullMethodName = "/processing.multiacquiring.multiacquiring.MultiAcquiring/GetBankTransactionStatusUnformated"
	MultiAcquiring_Refund_FullMethodName                             = "/processing.multiacquiring.multiacquiring.MultiAcquiring/Refund"
	MultiAcquiring_GooglePay_FullMethodName                          = "/processing.multiacquiring.multiacquiring.MultiAcquiring/GooglePay"
	MultiAcquiring_ApplePay_FullMethodName                           = "/processing.multiacquiring.multiacquiring.MultiAcquiring/ApplePay"
	MultiAcquiring_TwoStagePayIn_FullMethodName                      = "/processing.multiacquiring.multiacquiring.MultiAcquiring/TwoStagePayIn"
	MultiAcquiring_Charge_FullMethodName                             = "/processing.multiacquiring.multiacquiring.MultiAcquiring/Charge"
	MultiAcquiring_Cancel_FullMethodName                             = "/processing.multiacquiring.multiacquiring.MultiAcquiring/Cancel"
	MultiAcquiring_MakeToken_FullMethodName                          = "/processing.multiacquiring.multiacquiring.MultiAcquiring/MakeToken"
	MultiAcquiring_ResolveVisaAlias_FullMethodName                   = "/processing.multiacquiring.multiacquiring.MultiAcquiring/ResolveVisaAlias"
	MultiAcquiring_GetAcquirerIdentifier_FullMethodName              = "/processing.multiacquiring.multiacquiring.MultiAcquiring/GetAcquirerIdentifier"
	MultiAcquiring_PayOutByPhone_FullMethodName                      = "/processing.multiacquiring.multiacquiring.MultiAcquiring/PayOutByPhone"
)

// MultiAcquiringClient is the client API for MultiAcquiring service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type MultiAcquiringClient interface {
	PayIn(ctx context.Context, in *PayInRequestData, opts ...grpc.CallOption) (*PayInResponseData, error)
	OneClickPayIn(ctx context.Context, in *OneClickPayInRequestData, opts ...grpc.CallOption) (*PayInResponseData, error)
	ThreeDSConfirm(ctx context.Context, in *ThreeDSRequestData, opts ...grpc.CallOption) (*ThreeDSResponseData, error)
	ThreeDSResume(ctx context.Context, in *ThreeDSResumeRequest, opts ...grpc.CallOption) (*ThreeDSResumeResponse, error)
	PayOut(ctx context.Context, in *PayOutRequestData, opts ...grpc.CallOption) (*PayOutResponseData, error)
	GetBankTransactionStatus(ctx context.Context, in *BankTransactionStatusRequest, opts ...grpc.CallOption) (*BankTransactionStatusResponse, error)
	GetBankTransactionStatusUnformated(ctx context.Context, in *BankTransactionStatusUnformatedRequest, opts ...grpc.CallOption) (*BankTransactionStatusUnformatedResponse, error)
	Refund(ctx context.Context, in *RefundRequest, opts ...grpc.CallOption) (*RefundResponse, error)
	GooglePay(ctx context.Context, in *GooglePayRequestData, opts ...grpc.CallOption) (*GooglePayResponseData, error)
	ApplePay(ctx context.Context, in *ApplePayRequestData, opts ...grpc.CallOption) (*ApplePayResponseData, error)
	TwoStagePayIn(ctx context.Context, in *TwoStagePayInRequest, opts ...grpc.CallOption) (*TwoStagePayInResponse, error)
	Charge(ctx context.Context, in *ChargeRequest, opts ...grpc.CallOption) (*ChargeResponse, error)
	Cancel(ctx context.Context, in *CancelRequest, opts ...grpc.CallOption) (*CancelResponse, error)
	MakeToken(ctx context.Context, in *PayInRequestData, opts ...grpc.CallOption) (*PayInResponseData, error)
	ResolveVisaAlias(ctx context.Context, in *ResolveVisaAliasRequest, opts ...grpc.CallOption) (*ResolveVisaAliasResponse, error)
	GetAcquirerIdentifier(ctx context.Context, in *emptypb.Empty, opts ...grpc.CallOption) (*GetAcquirerIdentifierResponse, error)
	PayOutByPhone(ctx context.Context, in *PayOutByPhoneRequestData, opts ...grpc.CallOption) (*PayOutResponseByPhoneData, error)
}

type multiAcquiringClient struct {
	cc grpc.ClientConnInterface
}

func NewMultiAcquiringClient(cc grpc.ClientConnInterface) MultiAcquiringClient {
	return &multiAcquiringClient{cc}
}

func (c *multiAcquiringClient) PayIn(ctx context.Context, in *PayInRequestData, opts ...grpc.CallOption) (*PayInResponseData, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(PayInResponseData)
	err := c.cc.Invoke(ctx, MultiAcquiring_PayIn_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *multiAcquiringClient) OneClickPayIn(ctx context.Context, in *OneClickPayInRequestData, opts ...grpc.CallOption) (*PayInResponseData, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(PayInResponseData)
	err := c.cc.Invoke(ctx, MultiAcquiring_OneClickPayIn_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *multiAcquiringClient) ThreeDSConfirm(ctx context.Context, in *ThreeDSRequestData, opts ...grpc.CallOption) (*ThreeDSResponseData, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ThreeDSResponseData)
	err := c.cc.Invoke(ctx, MultiAcquiring_ThreeDSConfirm_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *multiAcquiringClient) ThreeDSResume(ctx context.Context, in *ThreeDSResumeRequest, opts ...grpc.CallOption) (*ThreeDSResumeResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ThreeDSResumeResponse)
	err := c.cc.Invoke(ctx, MultiAcquiring_ThreeDSResume_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *multiAcquiringClient) PayOut(ctx context.Context, in *PayOutRequestData, opts ...grpc.CallOption) (*PayOutResponseData, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(PayOutResponseData)
	err := c.cc.Invoke(ctx, MultiAcquiring_PayOut_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *multiAcquiringClient) GetBankTransactionStatus(ctx context.Context, in *BankTransactionStatusRequest, opts ...grpc.CallOption) (*BankTransactionStatusResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(BankTransactionStatusResponse)
	err := c.cc.Invoke(ctx, MultiAcquiring_GetBankTransactionStatus_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *multiAcquiringClient) GetBankTransactionStatusUnformated(ctx context.Context, in *BankTransactionStatusUnformatedRequest, opts ...grpc.CallOption) (*BankTransactionStatusUnformatedResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(BankTransactionStatusUnformatedResponse)
	err := c.cc.Invoke(ctx, MultiAcquiring_GetBankTransactionStatusUnformated_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *multiAcquiringClient) Refund(ctx context.Context, in *RefundRequest, opts ...grpc.CallOption) (*RefundResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(RefundResponse)
	err := c.cc.Invoke(ctx, MultiAcquiring_Refund_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *multiAcquiringClient) GooglePay(ctx context.Context, in *GooglePayRequestData, opts ...grpc.CallOption) (*GooglePayResponseData, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GooglePayResponseData)
	err := c.cc.Invoke(ctx, MultiAcquiring_GooglePay_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *multiAcquiringClient) ApplePay(ctx context.Context, in *ApplePayRequestData, opts ...grpc.CallOption) (*ApplePayResponseData, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ApplePayResponseData)
	err := c.cc.Invoke(ctx, MultiAcquiring_ApplePay_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *multiAcquiringClient) TwoStagePayIn(ctx context.Context, in *TwoStagePayInRequest, opts ...grpc.CallOption) (*TwoStagePayInResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(TwoStagePayInResponse)
	err := c.cc.Invoke(ctx, MultiAcquiring_TwoStagePayIn_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *multiAcquiringClient) Charge(ctx context.Context, in *ChargeRequest, opts ...grpc.CallOption) (*ChargeResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ChargeResponse)
	err := c.cc.Invoke(ctx, MultiAcquiring_Charge_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *multiAcquiringClient) Cancel(ctx context.Context, in *CancelRequest, opts ...grpc.CallOption) (*CancelResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(CancelResponse)
	err := c.cc.Invoke(ctx, MultiAcquiring_Cancel_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *multiAcquiringClient) MakeToken(ctx context.Context, in *PayInRequestData, opts ...grpc.CallOption) (*PayInResponseData, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(PayInResponseData)
	err := c.cc.Invoke(ctx, MultiAcquiring_MakeToken_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *multiAcquiringClient) ResolveVisaAlias(ctx context.Context, in *ResolveVisaAliasRequest, opts ...grpc.CallOption) (*ResolveVisaAliasResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ResolveVisaAliasResponse)
	err := c.cc.Invoke(ctx, MultiAcquiring_ResolveVisaAlias_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *multiAcquiringClient) GetAcquirerIdentifier(ctx context.Context, in *emptypb.Empty, opts ...grpc.CallOption) (*GetAcquirerIdentifierResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetAcquirerIdentifierResponse)
	err := c.cc.Invoke(ctx, MultiAcquiring_GetAcquirerIdentifier_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *multiAcquiringClient) PayOutByPhone(ctx context.Context, in *PayOutByPhoneRequestData, opts ...grpc.CallOption) (*PayOutResponseByPhoneData, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(PayOutResponseByPhoneData)
	err := c.cc.Invoke(ctx, MultiAcquiring_PayOutByPhone_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// MultiAcquiringServer is the server API for MultiAcquiring service.
// All implementations must embed UnimplementedMultiAcquiringServer
// for forward compatibility.
type MultiAcquiringServer interface {
	PayIn(context.Context, *PayInRequestData) (*PayInResponseData, error)
	OneClickPayIn(context.Context, *OneClickPayInRequestData) (*PayInResponseData, error)
	ThreeDSConfirm(context.Context, *ThreeDSRequestData) (*ThreeDSResponseData, error)
	ThreeDSResume(context.Context, *ThreeDSResumeRequest) (*ThreeDSResumeResponse, error)
	PayOut(context.Context, *PayOutRequestData) (*PayOutResponseData, error)
	GetBankTransactionStatus(context.Context, *BankTransactionStatusRequest) (*BankTransactionStatusResponse, error)
	GetBankTransactionStatusUnformated(context.Context, *BankTransactionStatusUnformatedRequest) (*BankTransactionStatusUnformatedResponse, error)
	Refund(context.Context, *RefundRequest) (*RefundResponse, error)
	GooglePay(context.Context, *GooglePayRequestData) (*GooglePayResponseData, error)
	ApplePay(context.Context, *ApplePayRequestData) (*ApplePayResponseData, error)
	TwoStagePayIn(context.Context, *TwoStagePayInRequest) (*TwoStagePayInResponse, error)
	Charge(context.Context, *ChargeRequest) (*ChargeResponse, error)
	Cancel(context.Context, *CancelRequest) (*CancelResponse, error)
	MakeToken(context.Context, *PayInRequestData) (*PayInResponseData, error)
	ResolveVisaAlias(context.Context, *ResolveVisaAliasRequest) (*ResolveVisaAliasResponse, error)
	GetAcquirerIdentifier(context.Context, *emptypb.Empty) (*GetAcquirerIdentifierResponse, error)
	PayOutByPhone(context.Context, *PayOutByPhoneRequestData) (*PayOutResponseByPhoneData, error)
	mustEmbedUnimplementedMultiAcquiringServer()
}

// UnimplementedMultiAcquiringServer must be embedded to have
// forward compatible implementations.
//
// NOTE: this should be embedded by value instead of pointer to avoid a nil
// pointer dereference when methods are called.
type UnimplementedMultiAcquiringServer struct{}

func (UnimplementedMultiAcquiringServer) PayIn(context.Context, *PayInRequestData) (*PayInResponseData, error) {
	return nil, status.Errorf(codes.Unimplemented, "method PayIn not implemented")
}
func (UnimplementedMultiAcquiringServer) OneClickPayIn(context.Context, *OneClickPayInRequestData) (*PayInResponseData, error) {
	return nil, status.Errorf(codes.Unimplemented, "method OneClickPayIn not implemented")
}
func (UnimplementedMultiAcquiringServer) ThreeDSConfirm(context.Context, *ThreeDSRequestData) (*ThreeDSResponseData, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ThreeDSConfirm not implemented")
}
func (UnimplementedMultiAcquiringServer) ThreeDSResume(context.Context, *ThreeDSResumeRequest) (*ThreeDSResumeResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ThreeDSResume not implemented")
}
func (UnimplementedMultiAcquiringServer) PayOut(context.Context, *PayOutRequestData) (*PayOutResponseData, error) {
	return nil, status.Errorf(codes.Unimplemented, "method PayOut not implemented")
}
func (UnimplementedMultiAcquiringServer) GetBankTransactionStatus(context.Context, *BankTransactionStatusRequest) (*BankTransactionStatusResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetBankTransactionStatus not implemented")
}
func (UnimplementedMultiAcquiringServer) GetBankTransactionStatusUnformated(context.Context, *BankTransactionStatusUnformatedRequest) (*BankTransactionStatusUnformatedResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetBankTransactionStatusUnformated not implemented")
}
func (UnimplementedMultiAcquiringServer) Refund(context.Context, *RefundRequest) (*RefundResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Refund not implemented")
}
func (UnimplementedMultiAcquiringServer) GooglePay(context.Context, *GooglePayRequestData) (*GooglePayResponseData, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GooglePay not implemented")
}
func (UnimplementedMultiAcquiringServer) ApplePay(context.Context, *ApplePayRequestData) (*ApplePayResponseData, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ApplePay not implemented")
}
func (UnimplementedMultiAcquiringServer) TwoStagePayIn(context.Context, *TwoStagePayInRequest) (*TwoStagePayInResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method TwoStagePayIn not implemented")
}
func (UnimplementedMultiAcquiringServer) Charge(context.Context, *ChargeRequest) (*ChargeResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Charge not implemented")
}
func (UnimplementedMultiAcquiringServer) Cancel(context.Context, *CancelRequest) (*CancelResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Cancel not implemented")
}
func (UnimplementedMultiAcquiringServer) MakeToken(context.Context, *PayInRequestData) (*PayInResponseData, error) {
	return nil, status.Errorf(codes.Unimplemented, "method MakeToken not implemented")
}
func (UnimplementedMultiAcquiringServer) ResolveVisaAlias(context.Context, *ResolveVisaAliasRequest) (*ResolveVisaAliasResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ResolveVisaAlias not implemented")
}
func (UnimplementedMultiAcquiringServer) GetAcquirerIdentifier(context.Context, *emptypb.Empty) (*GetAcquirerIdentifierResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetAcquirerIdentifier not implemented")
}
func (UnimplementedMultiAcquiringServer) PayOutByPhone(context.Context, *PayOutByPhoneRequestData) (*PayOutResponseByPhoneData, error) {
	return nil, status.Errorf(codes.Unimplemented, "method PayOutByPhone not implemented")
}
func (UnimplementedMultiAcquiringServer) mustEmbedUnimplementedMultiAcquiringServer() {}
func (UnimplementedMultiAcquiringServer) testEmbeddedByValue()                        {}

// UnsafeMultiAcquiringServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to MultiAcquiringServer will
// result in compilation errors.
type UnsafeMultiAcquiringServer interface {
	mustEmbedUnimplementedMultiAcquiringServer()
}

func RegisterMultiAcquiringServer(s grpc.ServiceRegistrar, srv MultiAcquiringServer) {
	// If the following call pancis, it indicates UnimplementedMultiAcquiringServer was
	// embedded by pointer and is nil.  This will cause panics if an
	// unimplemented method is ever invoked, so we test this at initialization
	// time to prevent it from happening at runtime later due to I/O.
	if t, ok := srv.(interface{ testEmbeddedByValue() }); ok {
		t.testEmbeddedByValue()
	}
	s.RegisterService(&MultiAcquiring_ServiceDesc, srv)
}

func _MultiAcquiring_PayIn_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(PayInRequestData)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MultiAcquiringServer).PayIn(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: MultiAcquiring_PayIn_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MultiAcquiringServer).PayIn(ctx, req.(*PayInRequestData))
	}
	return interceptor(ctx, in, info, handler)
}

func _MultiAcquiring_OneClickPayIn_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(OneClickPayInRequestData)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MultiAcquiringServer).OneClickPayIn(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: MultiAcquiring_OneClickPayIn_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MultiAcquiringServer).OneClickPayIn(ctx, req.(*OneClickPayInRequestData))
	}
	return interceptor(ctx, in, info, handler)
}

func _MultiAcquiring_ThreeDSConfirm_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ThreeDSRequestData)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MultiAcquiringServer).ThreeDSConfirm(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: MultiAcquiring_ThreeDSConfirm_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MultiAcquiringServer).ThreeDSConfirm(ctx, req.(*ThreeDSRequestData))
	}
	return interceptor(ctx, in, info, handler)
}

func _MultiAcquiring_ThreeDSResume_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ThreeDSResumeRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MultiAcquiringServer).ThreeDSResume(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: MultiAcquiring_ThreeDSResume_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MultiAcquiringServer).ThreeDSResume(ctx, req.(*ThreeDSResumeRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _MultiAcquiring_PayOut_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(PayOutRequestData)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MultiAcquiringServer).PayOut(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: MultiAcquiring_PayOut_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MultiAcquiringServer).PayOut(ctx, req.(*PayOutRequestData))
	}
	return interceptor(ctx, in, info, handler)
}

func _MultiAcquiring_GetBankTransactionStatus_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BankTransactionStatusRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MultiAcquiringServer).GetBankTransactionStatus(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: MultiAcquiring_GetBankTransactionStatus_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MultiAcquiringServer).GetBankTransactionStatus(ctx, req.(*BankTransactionStatusRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _MultiAcquiring_GetBankTransactionStatusUnformated_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BankTransactionStatusUnformatedRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MultiAcquiringServer).GetBankTransactionStatusUnformated(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: MultiAcquiring_GetBankTransactionStatusUnformated_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MultiAcquiringServer).GetBankTransactionStatusUnformated(ctx, req.(*BankTransactionStatusUnformatedRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _MultiAcquiring_Refund_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RefundRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MultiAcquiringServer).Refund(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: MultiAcquiring_Refund_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MultiAcquiringServer).Refund(ctx, req.(*RefundRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _MultiAcquiring_GooglePay_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GooglePayRequestData)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MultiAcquiringServer).GooglePay(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: MultiAcquiring_GooglePay_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MultiAcquiringServer).GooglePay(ctx, req.(*GooglePayRequestData))
	}
	return interceptor(ctx, in, info, handler)
}

func _MultiAcquiring_ApplePay_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ApplePayRequestData)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MultiAcquiringServer).ApplePay(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: MultiAcquiring_ApplePay_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MultiAcquiringServer).ApplePay(ctx, req.(*ApplePayRequestData))
	}
	return interceptor(ctx, in, info, handler)
}

func _MultiAcquiring_TwoStagePayIn_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(TwoStagePayInRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MultiAcquiringServer).TwoStagePayIn(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: MultiAcquiring_TwoStagePayIn_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MultiAcquiringServer).TwoStagePayIn(ctx, req.(*TwoStagePayInRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _MultiAcquiring_Charge_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ChargeRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MultiAcquiringServer).Charge(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: MultiAcquiring_Charge_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MultiAcquiringServer).Charge(ctx, req.(*ChargeRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _MultiAcquiring_Cancel_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CancelRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MultiAcquiringServer).Cancel(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: MultiAcquiring_Cancel_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MultiAcquiringServer).Cancel(ctx, req.(*CancelRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _MultiAcquiring_MakeToken_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(PayInRequestData)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MultiAcquiringServer).MakeToken(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: MultiAcquiring_MakeToken_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MultiAcquiringServer).MakeToken(ctx, req.(*PayInRequestData))
	}
	return interceptor(ctx, in, info, handler)
}

func _MultiAcquiring_ResolveVisaAlias_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ResolveVisaAliasRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MultiAcquiringServer).ResolveVisaAlias(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: MultiAcquiring_ResolveVisaAlias_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MultiAcquiringServer).ResolveVisaAlias(ctx, req.(*ResolveVisaAliasRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _MultiAcquiring_GetAcquirerIdentifier_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(emptypb.Empty)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MultiAcquiringServer).GetAcquirerIdentifier(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: MultiAcquiring_GetAcquirerIdentifier_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MultiAcquiringServer).GetAcquirerIdentifier(ctx, req.(*emptypb.Empty))
	}
	return interceptor(ctx, in, info, handler)
}

func _MultiAcquiring_PayOutByPhone_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(PayOutByPhoneRequestData)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MultiAcquiringServer).PayOutByPhone(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: MultiAcquiring_PayOutByPhone_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MultiAcquiringServer).PayOutByPhone(ctx, req.(*PayOutByPhoneRequestData))
	}
	return interceptor(ctx, in, info, handler)
}

// MultiAcquiring_ServiceDesc is the grpc.ServiceDesc for MultiAcquiring service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var MultiAcquiring_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "processing.multiacquiring.multiacquiring.MultiAcquiring",
	HandlerType: (*MultiAcquiringServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "PayIn",
			Handler:    _MultiAcquiring_PayIn_Handler,
		},
		{
			MethodName: "OneClickPayIn",
			Handler:    _MultiAcquiring_OneClickPayIn_Handler,
		},
		{
			MethodName: "ThreeDSConfirm",
			Handler:    _MultiAcquiring_ThreeDSConfirm_Handler,
		},
		{
			MethodName: "ThreeDSResume",
			Handler:    _MultiAcquiring_ThreeDSResume_Handler,
		},
		{
			MethodName: "PayOut",
			Handler:    _MultiAcquiring_PayOut_Handler,
		},
		{
			MethodName: "GetBankTransactionStatus",
			Handler:    _MultiAcquiring_GetBankTransactionStatus_Handler,
		},
		{
			MethodName: "GetBankTransactionStatusUnformated",
			Handler:    _MultiAcquiring_GetBankTransactionStatusUnformated_Handler,
		},
		{
			MethodName: "Refund",
			Handler:    _MultiAcquiring_Refund_Handler,
		},
		{
			MethodName: "GooglePay",
			Handler:    _MultiAcquiring_GooglePay_Handler,
		},
		{
			MethodName: "ApplePay",
			Handler:    _MultiAcquiring_ApplePay_Handler,
		},
		{
			MethodName: "TwoStagePayIn",
			Handler:    _MultiAcquiring_TwoStagePayIn_Handler,
		},
		{
			MethodName: "Charge",
			Handler:    _MultiAcquiring_Charge_Handler,
		},
		{
			MethodName: "Cancel",
			Handler:    _MultiAcquiring_Cancel_Handler,
		},
		{
			MethodName: "MakeToken",
			Handler:    _MultiAcquiring_MakeToken_Handler,
		},
		{
			MethodName: "ResolveVisaAlias",
			Handler:    _MultiAcquiring_ResolveVisaAlias_Handler,
		},
		{
			MethodName: "GetAcquirerIdentifier",
			Handler:    _MultiAcquiring_GetAcquirerIdentifier_Handler,
		},
		{
			MethodName: "PayOutByPhone",
			Handler:    _MultiAcquiring_PayOutByPhone_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "inner/processing/grpc/multiacquiring.proto",
}
