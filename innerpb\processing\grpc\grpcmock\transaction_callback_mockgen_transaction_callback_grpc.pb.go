// Code generated by MockGen. DO NOT EDIT.
// Source: transaction_callback_grpc.pb.go

// Package grpcmock is a generated GoMock package.
package grpcmock

import (
	context "context"
	reflect "reflect"

	grpc "git.local/sensitive/innerpb/processing/grpc"
	gomock "github.com/golang/mock/gomock"
	grpc0 "google.golang.org/grpc"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
)

// MockTransactionCallbackClient is a mock of TransactionCallbackClient interface.
type MockTransactionCallbackClient struct {
	ctrl     *gomock.Controller
	recorder *MockTransactionCallbackClientMockRecorder
}

// MockTransactionCallbackClientMockRecorder is the mock recorder for MockTransactionCallbackClient.
type MockTransactionCallbackClientMockRecorder struct {
	mock *MockTransactionCallbackClient
}

// NewMockTransactionCallbackClient creates a new mock instance.
func NewMockTransactionCallbackClient(ctrl *gomock.Controller) *MockTransactionCallbackClient {
	mock := &MockTransactionCallbackClient{ctrl: ctrl}
	mock.recorder = &MockTransactionCallbackClientMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockTransactionCallbackClient) EXPECT() *MockTransactionCallbackClientMockRecorder {
	return m.recorder
}

// SendCallback mocks base method.
func (m *MockTransactionCallbackClient) SendCallback(ctx context.Context, in *grpc.TransactionSendCallbackRequestV1, opts ...grpc0.CallOption) (*emptypb.Empty, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "SendCallback", varargs...)
	ret0, _ := ret[0].(*emptypb.Empty)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SendCallback indicates an expected call of SendCallback.
func (mr *MockTransactionCallbackClientMockRecorder) SendCallback(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SendCallback", reflect.TypeOf((*MockTransactionCallbackClient)(nil).SendCallback), varargs...)
}

// MockTransactionCallbackServer is a mock of TransactionCallbackServer interface.
type MockTransactionCallbackServer struct {
	ctrl     *gomock.Controller
	recorder *MockTransactionCallbackServerMockRecorder
}

// MockTransactionCallbackServerMockRecorder is the mock recorder for MockTransactionCallbackServer.
type MockTransactionCallbackServerMockRecorder struct {
	mock *MockTransactionCallbackServer
}

// NewMockTransactionCallbackServer creates a new mock instance.
func NewMockTransactionCallbackServer(ctrl *gomock.Controller) *MockTransactionCallbackServer {
	mock := &MockTransactionCallbackServer{ctrl: ctrl}
	mock.recorder = &MockTransactionCallbackServerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockTransactionCallbackServer) EXPECT() *MockTransactionCallbackServerMockRecorder {
	return m.recorder
}

// SendCallback mocks base method.
func (m *MockTransactionCallbackServer) SendCallback(arg0 context.Context, arg1 *grpc.TransactionSendCallbackRequestV1) (*emptypb.Empty, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SendCallback", arg0, arg1)
	ret0, _ := ret[0].(*emptypb.Empty)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SendCallback indicates an expected call of SendCallback.
func (mr *MockTransactionCallbackServerMockRecorder) SendCallback(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SendCallback", reflect.TypeOf((*MockTransactionCallbackServer)(nil).SendCallback), arg0, arg1)
}

// mustEmbedUnimplementedTransactionCallbackServer mocks base method.
func (m *MockTransactionCallbackServer) mustEmbedUnimplementedTransactionCallbackServer() {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "mustEmbedUnimplementedTransactionCallbackServer")
}

// mustEmbedUnimplementedTransactionCallbackServer indicates an expected call of mustEmbedUnimplementedTransactionCallbackServer.
func (mr *MockTransactionCallbackServerMockRecorder) mustEmbedUnimplementedTransactionCallbackServer() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "mustEmbedUnimplementedTransactionCallbackServer", reflect.TypeOf((*MockTransactionCallbackServer)(nil).mustEmbedUnimplementedTransactionCallbackServer))
}

// MockUnsafeTransactionCallbackServer is a mock of UnsafeTransactionCallbackServer interface.
type MockUnsafeTransactionCallbackServer struct {
	ctrl     *gomock.Controller
	recorder *MockUnsafeTransactionCallbackServerMockRecorder
}

// MockUnsafeTransactionCallbackServerMockRecorder is the mock recorder for MockUnsafeTransactionCallbackServer.
type MockUnsafeTransactionCallbackServerMockRecorder struct {
	mock *MockUnsafeTransactionCallbackServer
}

// NewMockUnsafeTransactionCallbackServer creates a new mock instance.
func NewMockUnsafeTransactionCallbackServer(ctrl *gomock.Controller) *MockUnsafeTransactionCallbackServer {
	mock := &MockUnsafeTransactionCallbackServer{ctrl: ctrl}
	mock.recorder = &MockUnsafeTransactionCallbackServerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockUnsafeTransactionCallbackServer) EXPECT() *MockUnsafeTransactionCallbackServerMockRecorder {
	return m.recorder
}

// mustEmbedUnimplementedTransactionCallbackServer mocks base method.
func (m *MockUnsafeTransactionCallbackServer) mustEmbedUnimplementedTransactionCallbackServer() {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "mustEmbedUnimplementedTransactionCallbackServer")
}

// mustEmbedUnimplementedTransactionCallbackServer indicates an expected call of mustEmbedUnimplementedTransactionCallbackServer.
func (mr *MockUnsafeTransactionCallbackServerMockRecorder) mustEmbedUnimplementedTransactionCallbackServer() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "mustEmbedUnimplementedTransactionCallbackServer", reflect.TypeOf((*MockUnsafeTransactionCallbackServer)(nil).mustEmbedUnimplementedTransactionCallbackServer))
}
