// Code generated by protoc-gen-go-mvp. DO NOT EDIT.
// versions:
// - protoc-gen-go-mvp v2.0.1
// created by mvp authors

package grpc

import (
	dog "git.local/sensitive/sdk/dog"
	gin "github.com/gin-gonic/gin"
	metadata "google.golang.org/grpc/metadata"
	http "net/http"
)

func RegisterGinEosiRouterGroup(
	in *gin.RouterGroup,
	handler func(func(c *gin.Context) error) gin.HandlerFunc,
) error {
	service, err := NewGinEosiService()
	if err != nil {
		return err
	}

	routerGroup := in.Group("processing.eosi.eosi.Eosi")
	routerGroup.PUT("/GetPaymentOrder", handler(service.GetPaymentOrder))
	routerGroup.PUT("/GetOrderingIdentifier", handler(service.GetOrderingIdentifier))
	return nil
}

func NewGinEosiService() (GinEosiServer, error) {
	client, err := NewPreparedEosiClient()
	if err != nil {
		return nil, err
	}

	return &ginEosiServer{
		client: NewLoggedEosiClient(
			NewIamEosiClient(client),
		),
	}, nil
}

//go:generate mockgen -destination=./grpcmock/eosi.gin.pb.go -package=grpcmock -source=eosi.gin.pb.go GinEosiServer
type GinEosiServer interface {
	GetPaymentOrder(c *gin.Context) error
	GetOrderingIdentifier(c *gin.Context) error
}

var _ GinEosiServer = (*ginEosiServer)(nil)

type ginEosiServer struct {
	client EosiClient
}

type Eosi_GetPaymentOrder_Success struct {
	// Status is the status of the response. Set true
	Status bool `json:"status"`
	// Message is a human-readable explanation of the status
	Message string `json:"message"`
	// StatusCode is the GoErr status. Set 0
	StatusCode int `json:"status_code"`
	// Result is the result of the request
	Result *GetPaymentOrderResponse `json:"result"`
}

type Eosi_GetPaymentOrder_Failure struct {
	// Status is the status of the response. Set false
	Status bool `json:"status"`
	// Message is a human-readable explanation of the status
	Message string `json:"message"`
	// StatusCode is the GoErr status
	StatusCode int `json:"status_code"`
}

// GetPaymentOrder
// @Summary GetPaymentOrder
// @Security bearerAuth
// @ID Eosi_GetPaymentOrder
// @Accept json
// @Param request body GetPaymentOrderRequest true "GetPaymentOrderRequest"
// @Success 200 {object} Eosi_GetPaymentOrder_Success
// @Failure 401 {object} Eosi_GetPaymentOrder_Failure "goerrpkg.ErrorTypeAuthorization"
// @Failure 404 {object} Eosi_GetPaymentOrder_Failure "goerrpkg.ErrorTypeNotFound"
// @Failure 400 {object} Eosi_GetPaymentOrder_Failure "goerrpkg.ErrorTypeBadRequest"
// @Failure 500 {object} Eosi_GetPaymentOrder_Failure "goerrpkg.ErrorTypeUnexpectedError"
// @Failure 400 {object} Eosi_GetPaymentOrder_Failure "gin.ErrorTypeBind"
// @Failure 500 {object} Eosi_GetPaymentOrder_Failure "Undefined error"
// @Produce json
// @Router /processing.eosi.eosi.Eosi/GetPaymentOrder [put]
// @tags Eosi
func (s *ginEosiServer) GetPaymentOrder(c *gin.Context) error {
	ctx, span := dog.CreateSpan(c.Request.Context(), "GinEosiServer_GetPaymentOrder")
	defer span.End()

	var request GetPaymentOrderRequest
	if err := c.ShouldBindJSON(&request); err != nil {
		return err
	}

	ctx = metadata.NewOutgoingContext(ctx, metadata.MD(c.Request.Header))

	response, err := s.client.GetPaymentOrder(ctx, &request)
	if err != nil {
		return err
	}

	c.JSON(http.StatusOK, &Eosi_GetPaymentOrder_Success{
		Status:     true,
		Message:    "Success",
		StatusCode: 0,
		Result:     response,
	})
	return nil
}

type Eosi_GetOrderingIdentifier_Success struct {
	// Status is the status of the response. Set true
	Status bool `json:"status"`
	// Message is a human-readable explanation of the status
	Message string `json:"message"`
	// StatusCode is the GoErr status. Set 0
	StatusCode int `json:"status_code"`
	// Result is the result of the request
	Result *GetOrderingIdentifierResponse `json:"result"`
}

type Eosi_GetOrderingIdentifier_Failure struct {
	// Status is the status of the response. Set false
	Status bool `json:"status"`
	// Message is a human-readable explanation of the status
	Message string `json:"message"`
	// StatusCode is the GoErr status
	StatusCode int `json:"status_code"`
}

// GetOrderingIdentifier
// @Summary GetOrderingIdentifier
// @Security bearerAuth
// @ID Eosi_GetOrderingIdentifier
// @Accept json
// @Param request body GetOrderingIdentifierRequest true "GetOrderingIdentifierRequest"
// @Success 200 {object} Eosi_GetOrderingIdentifier_Success
// @Failure 401 {object} Eosi_GetOrderingIdentifier_Failure "goerrpkg.ErrorTypeAuthorization"
// @Failure 404 {object} Eosi_GetOrderingIdentifier_Failure "goerrpkg.ErrorTypeNotFound"
// @Failure 400 {object} Eosi_GetOrderingIdentifier_Failure "goerrpkg.ErrorTypeBadRequest"
// @Failure 500 {object} Eosi_GetOrderingIdentifier_Failure "goerrpkg.ErrorTypeUnexpectedError"
// @Failure 400 {object} Eosi_GetOrderingIdentifier_Failure "gin.ErrorTypeBind"
// @Failure 500 {object} Eosi_GetOrderingIdentifier_Failure "Undefined error"
// @Produce json
// @Router /processing.eosi.eosi.Eosi/GetOrderingIdentifier [put]
// @tags Eosi
func (s *ginEosiServer) GetOrderingIdentifier(c *gin.Context) error {
	ctx, span := dog.CreateSpan(c.Request.Context(), "GinEosiServer_GetOrderingIdentifier")
	defer span.End()

	var request GetOrderingIdentifierRequest
	if err := c.ShouldBindJSON(&request); err != nil {
		return err
	}

	ctx = metadata.NewOutgoingContext(ctx, metadata.MD(c.Request.Header))

	response, err := s.client.GetOrderingIdentifier(ctx, &request)
	if err != nil {
		return err
	}

	c.JSON(http.StatusOK, &Eosi_GetOrderingIdentifier_Success{
		Status:     true,
		Message:    "Success",
		StatusCode: 0,
		Result:     response,
	})
	return nil
}
