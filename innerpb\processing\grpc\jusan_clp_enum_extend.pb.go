// Code generated by protoc-gen-go-mvp. DO NOT EDIT.
// versions:
// - protoc-gen-go-mvp v2.0.1
// created by mvp authors

package grpc

import (
	math "math"
	strings "strings"
)

func (val JusanResponseCodeClp) Synonym() JusanResponseCodeClp {
	if _, ok := JusanResponseCodeClp_name[int32(val)]; ok {
		return val
	}

	return JusanResponseCodeClp(math.MinInt32)
}

func (val JusanResponseCodeClp) Int() int {
	return int(val.Synonym())
}

func (val JusanResponseCodeClp) IntPtr() *int {
	result := int(val.Synonym())
	return &result
}

func (val JusanResponseCodeClp) Int32() int32 {
	return int32(val.Synonym())
}

func (val JusanResponseCodeClp) Int32Ptr() *int32 {
	result := int32(val.Synonym())
	return &result
}

func (val JusanResponseCodeClp) Int64() int64 {
	return int64(val.Synonym())
}

func (val JusanResponseCodeClp) Int64Ptr() *int64 {
	result := int64(val.Synonym())
	return &result
}

func (val JusanResponseCodeClp) Uint() uint {
	return uint(val.Synonym())
}

func (val JusanResponseCodeClp) UintPtr() *uint {
	result := uint(val.Synonym())
	return &result
}

func (val JusanResponseCodeClp) Uint32() uint32 {
	return uint32(val.Synonym())
}

func (val JusanResponseCodeClp) Uint32Ptr() *uint32 {
	result := uint32(val.Synonym())
	return &result
}

func (val JusanResponseCodeClp) Uint64() uint64 {
	return uint64(val.Synonym())
}

func (val JusanResponseCodeClp) Uint64Ptr() *uint64 {
	result := uint64(val.Synonym())
	return &result
}

func (val JusanResponseCodeClp) IsKnown() bool {
	return val.Synonym() != JusanResponseCodeClp(math.MinInt32)
}

func ConvertIntToJusanResponseCodeClp(in int) JusanResponseCodeClp {
	return JusanResponseCodeClp(in).Synonym()
}

func ConvertUintToJusanResponseCodeClp(in uint) JusanResponseCodeClp {
	return JusanResponseCodeClp(in).Synonym()
}

func ConvertInt32ToJusanResponseCodeClp(in int32) JusanResponseCodeClp {
	return JusanResponseCodeClp(in).Synonym()
}

func ConvertUint32ToJusanResponseCodeClp(in uint32) JusanResponseCodeClp {
	return JusanResponseCodeClp(in).Synonym()
}

func ConvertInt64ToJusanResponseCodeClp(in int64) JusanResponseCodeClp {
	return JusanResponseCodeClp(in).Synonym()
}

func ConvertUint64ToJusanResponseCodeClp(in uint64) JusanResponseCodeClp {
	return JusanResponseCodeClp(in).Synonym()
}

var JusanResponseCodeClp_Lower_value = map[string]JusanResponseCodeClp{
	"11":   0,
	"12":   1,
	"13":   2,
	"14":   3,
	"15":   4,
	"16":   5,
	"171":  6,
	"172 ": 7,
	"18":   8,
	"19":   9,
	"20":   10,
	"21":   11,
	"22":   12,
	"23":   13,
	"24":   14,
	"25":   15,
	"26":   16,
	"27":   17,
	"28":   18,
	"29":   19,
	"30":   20,
	"31":   21,
	"32":   22,
	"33":   23,
	"34":   24,
	"99":   25,
	"41":   26,
	"42":   27,
	"43":   28,
	"44":   29,
	"17":   30,
	"45":   31,
	"46":   32,
	"47":   33,
	"48":   34,
	"50":   35,
	"51":   36,
	"52":   37,
	"53":   38,
	"f":    39,
	"e":    40,
	"c":    41,
	"3":    43,
	"37":   44,
	"59":   45,
	"1":    47,
	"61":   49,
	"62":   50,
	"05":   51,
	"2":    52,
	"65":   53,
	"-19":  54,
	"07":   55,
}

func ConvertStringToJusanResponseCodeClp(in string) JusanResponseCodeClp {
	if result, ok := JusanResponseCodeClp_value[in]; ok {
		return JusanResponseCodeClp(result)
	}

	if result, ok := JusanResponseCodeClp_Lower_value[strings.ToLower(in)]; ok {
		return JusanResponseCodeClp(result)
	}

	return JusanResponseCodeClp(math.MinInt32)
}

var SliceJusanResponseCodeClpConvert *sliceJusanResponseCodeClpConvert

type sliceJusanResponseCodeClpConvert struct{}

func (*sliceJusanResponseCodeClpConvert) Synonym(in []JusanResponseCodeClp) []JusanResponseCodeClp {
	result := make([]JusanResponseCodeClp, len(in))
	for i, v := range in {
		result[i] = v.Synonym()
	}

	return result
}

func (*sliceJusanResponseCodeClpConvert) Int32(in []JusanResponseCodeClp) []int32 {
	result := make([]int32, len(in))
	for i, v := range in {
		result[i] = v.Int32()
	}

	return result
}

func (*sliceJusanResponseCodeClpConvert) Uint32(in []JusanResponseCodeClp) []uint32 {
	result := make([]uint32, len(in))
	for i, v := range in {
		result[i] = v.Uint32()
	}

	return result
}

func (*sliceJusanResponseCodeClpConvert) Uint64(in []JusanResponseCodeClp) []uint64 {
	result := make([]uint64, len(in))
	for i, v := range in {
		result[i] = v.Uint64()
	}

	return result
}

func (*sliceJusanResponseCodeClpConvert) Int64(in []JusanResponseCodeClp) []int64 {
	result := make([]int64, len(in))
	for i, v := range in {
		result[i] = v.Int64()
	}

	return result
}

func (*sliceJusanResponseCodeClpConvert) Uint(in []JusanResponseCodeClp) []uint {
	result := make([]uint, len(in))
	for i, v := range in {
		result[i] = v.Uint()
	}

	return result
}

func (*sliceJusanResponseCodeClpConvert) Int(in []JusanResponseCodeClp) []int {
	result := make([]int, len(in))
	for i, v := range in {
		result[i] = v.Int()
	}

	return result
}

func (*sliceJusanResponseCodeClpConvert) IsKnown(in []JusanResponseCodeClp) []bool {
	result := make([]bool, len(in))
	for i, v := range in {
		result[i] = v.IsKnown()
	}

	return result
}

func (*sliceJusanResponseCodeClpConvert) ConvertIntToJusanResponseCodeClp(in []int) []JusanResponseCodeClp {
	result := make([]JusanResponseCodeClp, len(in))
	for i, v := range in {
		result[i] = ConvertIntToJusanResponseCodeClp(v)
	}

	return result
}

func (*sliceJusanResponseCodeClpConvert) ConvertUintToJusanResponseCodeClp(in []uint) []JusanResponseCodeClp {
	result := make([]JusanResponseCodeClp, len(in))
	for i, v := range in {
		result[i] = ConvertUintToJusanResponseCodeClp(v)
	}

	return result
}

func (*sliceJusanResponseCodeClpConvert) ConvertInt32ToJusanResponseCodeClp(in []int32) []JusanResponseCodeClp {
	result := make([]JusanResponseCodeClp, len(in))
	for i, v := range in {
		result[i] = ConvertInt32ToJusanResponseCodeClp(v)
	}

	return result
}

func (*sliceJusanResponseCodeClpConvert) ConvertUint32ToJusanResponseCodeClp(in []uint32) []JusanResponseCodeClp {
	result := make([]JusanResponseCodeClp, len(in))
	for i, v := range in {
		result[i] = ConvertUint32ToJusanResponseCodeClp(v)
	}

	return result
}

func (*sliceJusanResponseCodeClpConvert) ConvertInt64ToJusanResponseCodeClp(in []int64) []JusanResponseCodeClp {
	result := make([]JusanResponseCodeClp, len(in))
	for i, v := range in {
		result[i] = ConvertInt64ToJusanResponseCodeClp(v)
	}

	return result
}

func (*sliceJusanResponseCodeClpConvert) ConvertUint64ToJusanResponseCodeClp(in []uint64) []JusanResponseCodeClp {
	result := make([]JusanResponseCodeClp, len(in))
	for i, v := range in {
		result[i] = ConvertUint64ToJusanResponseCodeClp(v)
	}

	return result
}

func (*sliceJusanResponseCodeClpConvert) ConvertStringToJusanResponseCodeClp(in []string) []JusanResponseCodeClp {
	result := make([]JusanResponseCodeClp, len(in))
	for i, v := range in {
		result[i] = ConvertStringToJusanResponseCodeClp(v)
	}

	return result
}

func NewJusanResponseCodeClpUsage() *JusanResponseCodeClpUsage {
	return &JusanResponseCodeClpUsage{
		enumMap: map[JusanResponseCodeClp]bool{
			JusanResponseCodeClp_ClpResponseUnavailable:     false,
			JusanResponseCodeClp_ClpIncorrectOrder:          false,
			JusanResponseCodeClp_ClpIncorrectAmount:         false,
			JusanResponseCodeClp_ClpIncorrectCurrency:       false,
			JusanResponseCodeClp_ClpMPIUnavailable:          false,
			JusanResponseCodeClp_ClpDbUnavailable:           false,
			JusanResponseCodeClp_ClpForbiddenMerchant:       false,
			JusanResponseCodeClp_ClpMerchantForbidden:       false,
			JusanResponseCodeClp_ClpRequestAlreadyCompleted: false,
			JusanResponseCodeClp_ClpIncorrectCardExpDate:    false,
			JusanResponseCodeClp_ClpIncorrectTerminal:       false,
			JusanResponseCodeClp_ClpInvalidSign:             false,
			JusanResponseCodeClp_ClpCurrencyNotFound:        false,
			JusanResponseCodeClp_ClpLimitExceeds:            false,
			JusanResponseCodeClp_ClpEmptyField:              false,
			JusanResponseCodeClp_ClpSizeLessSymbols:         false,
			JusanResponseCodeClp_ClpSizeMoreSymbols:         false,
			JusanResponseCodeClp_ClpInvalidValue:            false,
			JusanResponseCodeClp_ClpMPIError3DS:             false,
			JusanResponseCodeClp_ClpInvalidCardType:         false,
			JusanResponseCodeClp_ClpPaymentNotFound:         false,
			JusanResponseCodeClp_ClpEmptyClientKey:          false,
			JusanResponseCodeClp_ClpForbiddenTerminal:       false,
			JusanResponseCodeClp_ClpTokenNotFound:           false,
			JusanResponseCodeClp_ClpIncorrectBlockAmount:    false,
			JusanResponseCodeClp_ClpUnknownError:            false,
			JusanResponseCodeClp_ClpUnavailableService:      false,
			JusanResponseCodeClp_ClpAmountIncorrect:         false,
			JusanResponseCodeClp_ClpUnavailableDb:           false,
			JusanResponseCodeClp_ClpIncorrectMerchant:       false,
			JusanResponseCodeClp_ClpMerchantNotFound:        false,
			JusanResponseCodeClp_ClpOrderNotFound:           false,
			JusanResponseCodeClp_ClpSignInvalid:             false,
			JusanResponseCodeClp_ClpRefundAmountIncorrect:   false,
			JusanResponseCodeClp_ClpIncorrectStatus:         false,
			JusanResponseCodeClp_ClpIncorrectValue:          false,
			JusanResponseCodeClp_ClpTerminalForbidden:       false,
			JusanResponseCodeClp_ClpForbiddenOperation:      false,
			JusanResponseCodeClp_ClpDuplicateDescription:    false,
			JusanResponseCodeClp_ClpRefundHandleError:       false,
			JusanResponseCodeClp_ClpPaymentError:            false,
			JusanResponseCodeClp_ClpPaymentExpired:          false,
			JusanResponseCodeClp_ClpAuthError:               false,
			JusanResponseCodeClp_ClpIncorrectCardNum:        false,
			JusanResponseCodeClp_ClpSuspectedFraud:          false,
			JusanResponseCodeClp_ClpUndefinedError:          false,
			JusanResponseCodeClp_ClpExceedsLimit:            false,
			JusanResponseCodeClp_ClpLimitedCard:             false,
			JusanResponseCodeClp_ClpTransactionDeclined:     false,
			JusanResponseCodeClp_ClpNotEnough:               false,
			JusanResponseCodeClp_ClpLimitExceeded:           false,
			JusanResponseCodeClp_ClpErrorAuth:               false,
			JusanResponseCodeClp_ClpCardInactive:            false,
		},
	}
}

func IsJusanResponseCodeClp(target JusanResponseCodeClp, matches ...JusanResponseCodeClp) bool {
	for _, v := range matches {
		if target == v {
			return true
		}
	}

	return false
}

type JusanResponseCodeClpUsage struct {
	enumMap map[JusanResponseCodeClp]bool
}

func (u *JusanResponseCodeClpUsage) Use(slice ...JusanResponseCodeClp) *JusanResponseCodeClpUsage {
	for _, in := range slice {
		u.enumMap[in] = true
	}

	return u
}

func (u *JusanResponseCodeClpUsage) Check() []string {
	var result []string
	for k, v := range u.enumMap {
		if v {
			continue
		}

		result = append(result, k.String())
	}

	return result
}

func (u *JusanResponseCodeClpUsage) JusanResponseCodeClp_ClpResponseUnavailable() JusanResponseCodeClp {
	u.Use(JusanResponseCodeClp_ClpResponseUnavailable)
	return JusanResponseCodeClp_ClpResponseUnavailable
}

func (u *JusanResponseCodeClpUsage) JusanResponseCodeClp_ClpIncorrectOrder() JusanResponseCodeClp {
	u.Use(JusanResponseCodeClp_ClpIncorrectOrder)
	return JusanResponseCodeClp_ClpIncorrectOrder
}

func (u *JusanResponseCodeClpUsage) JusanResponseCodeClp_ClpIncorrectAmount() JusanResponseCodeClp {
	u.Use(JusanResponseCodeClp_ClpIncorrectAmount)
	return JusanResponseCodeClp_ClpIncorrectAmount
}

func (u *JusanResponseCodeClpUsage) JusanResponseCodeClp_ClpIncorrectCurrency() JusanResponseCodeClp {
	u.Use(JusanResponseCodeClp_ClpIncorrectCurrency)
	return JusanResponseCodeClp_ClpIncorrectCurrency
}

func (u *JusanResponseCodeClpUsage) JusanResponseCodeClp_ClpMPIUnavailable() JusanResponseCodeClp {
	u.Use(JusanResponseCodeClp_ClpMPIUnavailable)
	return JusanResponseCodeClp_ClpMPIUnavailable
}

func (u *JusanResponseCodeClpUsage) JusanResponseCodeClp_ClpDbUnavailable() JusanResponseCodeClp {
	u.Use(JusanResponseCodeClp_ClpDbUnavailable)
	return JusanResponseCodeClp_ClpDbUnavailable
}

func (u *JusanResponseCodeClpUsage) JusanResponseCodeClp_ClpForbiddenMerchant() JusanResponseCodeClp {
	u.Use(JusanResponseCodeClp_ClpForbiddenMerchant)
	return JusanResponseCodeClp_ClpForbiddenMerchant
}

func (u *JusanResponseCodeClpUsage) JusanResponseCodeClp_ClpMerchantForbidden() JusanResponseCodeClp {
	u.Use(JusanResponseCodeClp_ClpMerchantForbidden)
	return JusanResponseCodeClp_ClpMerchantForbidden
}

func (u *JusanResponseCodeClpUsage) JusanResponseCodeClp_ClpRequestAlreadyCompleted() JusanResponseCodeClp {
	u.Use(JusanResponseCodeClp_ClpRequestAlreadyCompleted)
	return JusanResponseCodeClp_ClpRequestAlreadyCompleted
}

func (u *JusanResponseCodeClpUsage) JusanResponseCodeClp_ClpIncorrectCardExpDate() JusanResponseCodeClp {
	u.Use(JusanResponseCodeClp_ClpIncorrectCardExpDate)
	return JusanResponseCodeClp_ClpIncorrectCardExpDate
}

func (u *JusanResponseCodeClpUsage) JusanResponseCodeClp_ClpIncorrectTerminal() JusanResponseCodeClp {
	u.Use(JusanResponseCodeClp_ClpIncorrectTerminal)
	return JusanResponseCodeClp_ClpIncorrectTerminal
}

func (u *JusanResponseCodeClpUsage) JusanResponseCodeClp_ClpInvalidSign() JusanResponseCodeClp {
	u.Use(JusanResponseCodeClp_ClpInvalidSign)
	return JusanResponseCodeClp_ClpInvalidSign
}

func (u *JusanResponseCodeClpUsage) JusanResponseCodeClp_ClpCurrencyNotFound() JusanResponseCodeClp {
	u.Use(JusanResponseCodeClp_ClpCurrencyNotFound)
	return JusanResponseCodeClp_ClpCurrencyNotFound
}

func (u *JusanResponseCodeClpUsage) JusanResponseCodeClp_ClpLimitExceeds() JusanResponseCodeClp {
	u.Use(JusanResponseCodeClp_ClpLimitExceeds)
	return JusanResponseCodeClp_ClpLimitExceeds
}

func (u *JusanResponseCodeClpUsage) JusanResponseCodeClp_ClpEmptyField() JusanResponseCodeClp {
	u.Use(JusanResponseCodeClp_ClpEmptyField)
	return JusanResponseCodeClp_ClpEmptyField
}

func (u *JusanResponseCodeClpUsage) JusanResponseCodeClp_ClpSizeLessSymbols() JusanResponseCodeClp {
	u.Use(JusanResponseCodeClp_ClpSizeLessSymbols)
	return JusanResponseCodeClp_ClpSizeLessSymbols
}

func (u *JusanResponseCodeClpUsage) JusanResponseCodeClp_ClpSizeMoreSymbols() JusanResponseCodeClp {
	u.Use(JusanResponseCodeClp_ClpSizeMoreSymbols)
	return JusanResponseCodeClp_ClpSizeMoreSymbols
}

func (u *JusanResponseCodeClpUsage) JusanResponseCodeClp_ClpInvalidValue() JusanResponseCodeClp {
	u.Use(JusanResponseCodeClp_ClpInvalidValue)
	return JusanResponseCodeClp_ClpInvalidValue
}

func (u *JusanResponseCodeClpUsage) JusanResponseCodeClp_ClpMPIError3DS() JusanResponseCodeClp {
	u.Use(JusanResponseCodeClp_ClpMPIError3DS)
	return JusanResponseCodeClp_ClpMPIError3DS
}

func (u *JusanResponseCodeClpUsage) JusanResponseCodeClp_ClpInvalidCardType() JusanResponseCodeClp {
	u.Use(JusanResponseCodeClp_ClpInvalidCardType)
	return JusanResponseCodeClp_ClpInvalidCardType
}

func (u *JusanResponseCodeClpUsage) JusanResponseCodeClp_ClpPaymentNotFound() JusanResponseCodeClp {
	u.Use(JusanResponseCodeClp_ClpPaymentNotFound)
	return JusanResponseCodeClp_ClpPaymentNotFound
}

func (u *JusanResponseCodeClpUsage) JusanResponseCodeClp_ClpEmptyClientKey() JusanResponseCodeClp {
	u.Use(JusanResponseCodeClp_ClpEmptyClientKey)
	return JusanResponseCodeClp_ClpEmptyClientKey
}

func (u *JusanResponseCodeClpUsage) JusanResponseCodeClp_ClpForbiddenTerminal() JusanResponseCodeClp {
	u.Use(JusanResponseCodeClp_ClpForbiddenTerminal)
	return JusanResponseCodeClp_ClpForbiddenTerminal
}

func (u *JusanResponseCodeClpUsage) JusanResponseCodeClp_ClpTokenNotFound() JusanResponseCodeClp {
	u.Use(JusanResponseCodeClp_ClpTokenNotFound)
	return JusanResponseCodeClp_ClpTokenNotFound
}

func (u *JusanResponseCodeClpUsage) JusanResponseCodeClp_ClpIncorrectBlockAmount() JusanResponseCodeClp {
	u.Use(JusanResponseCodeClp_ClpIncorrectBlockAmount)
	return JusanResponseCodeClp_ClpIncorrectBlockAmount
}

func (u *JusanResponseCodeClpUsage) JusanResponseCodeClp_ClpUnknownError() JusanResponseCodeClp {
	u.Use(JusanResponseCodeClp_ClpUnknownError)
	return JusanResponseCodeClp_ClpUnknownError
}

func (u *JusanResponseCodeClpUsage) JusanResponseCodeClp_ClpUnavailableService() JusanResponseCodeClp {
	u.Use(JusanResponseCodeClp_ClpUnavailableService)
	return JusanResponseCodeClp_ClpUnavailableService
}

func (u *JusanResponseCodeClpUsage) JusanResponseCodeClp_ClpAmountIncorrect() JusanResponseCodeClp {
	u.Use(JusanResponseCodeClp_ClpAmountIncorrect)
	return JusanResponseCodeClp_ClpAmountIncorrect
}

func (u *JusanResponseCodeClpUsage) JusanResponseCodeClp_ClpUnavailableDb() JusanResponseCodeClp {
	u.Use(JusanResponseCodeClp_ClpUnavailableDb)
	return JusanResponseCodeClp_ClpUnavailableDb
}

func (u *JusanResponseCodeClpUsage) JusanResponseCodeClp_ClpIncorrectMerchant() JusanResponseCodeClp {
	u.Use(JusanResponseCodeClp_ClpIncorrectMerchant)
	return JusanResponseCodeClp_ClpIncorrectMerchant
}

func (u *JusanResponseCodeClpUsage) JusanResponseCodeClp_ClpMerchantNotFound() JusanResponseCodeClp {
	u.Use(JusanResponseCodeClp_ClpMerchantNotFound)
	return JusanResponseCodeClp_ClpMerchantNotFound
}

func (u *JusanResponseCodeClpUsage) JusanResponseCodeClp_ClpOrderNotFound() JusanResponseCodeClp {
	u.Use(JusanResponseCodeClp_ClpOrderNotFound)
	return JusanResponseCodeClp_ClpOrderNotFound
}

func (u *JusanResponseCodeClpUsage) JusanResponseCodeClp_ClpSignInvalid() JusanResponseCodeClp {
	u.Use(JusanResponseCodeClp_ClpSignInvalid)
	return JusanResponseCodeClp_ClpSignInvalid
}

func (u *JusanResponseCodeClpUsage) JusanResponseCodeClp_ClpRefundAmountIncorrect() JusanResponseCodeClp {
	u.Use(JusanResponseCodeClp_ClpRefundAmountIncorrect)
	return JusanResponseCodeClp_ClpRefundAmountIncorrect
}

func (u *JusanResponseCodeClpUsage) JusanResponseCodeClp_ClpIncorrectStatus() JusanResponseCodeClp {
	u.Use(JusanResponseCodeClp_ClpIncorrectStatus)
	return JusanResponseCodeClp_ClpIncorrectStatus
}

func (u *JusanResponseCodeClpUsage) JusanResponseCodeClp_ClpIncorrectValue() JusanResponseCodeClp {
	u.Use(JusanResponseCodeClp_ClpIncorrectValue)
	return JusanResponseCodeClp_ClpIncorrectValue
}

func (u *JusanResponseCodeClpUsage) JusanResponseCodeClp_ClpTerminalForbidden() JusanResponseCodeClp {
	u.Use(JusanResponseCodeClp_ClpTerminalForbidden)
	return JusanResponseCodeClp_ClpTerminalForbidden
}

func (u *JusanResponseCodeClpUsage) JusanResponseCodeClp_ClpForbiddenOperation() JusanResponseCodeClp {
	u.Use(JusanResponseCodeClp_ClpForbiddenOperation)
	return JusanResponseCodeClp_ClpForbiddenOperation
}

func (u *JusanResponseCodeClpUsage) JusanResponseCodeClp_ClpDuplicateDescription() JusanResponseCodeClp {
	u.Use(JusanResponseCodeClp_ClpDuplicateDescription)
	return JusanResponseCodeClp_ClpDuplicateDescription
}

func (u *JusanResponseCodeClpUsage) JusanResponseCodeClp_ClpRefundHandleError() JusanResponseCodeClp {
	u.Use(JusanResponseCodeClp_ClpRefundHandleError)
	return JusanResponseCodeClp_ClpRefundHandleError
}

func (u *JusanResponseCodeClpUsage) JusanResponseCodeClp_ClpPaymentError() JusanResponseCodeClp {
	u.Use(JusanResponseCodeClp_ClpPaymentError)
	return JusanResponseCodeClp_ClpPaymentError
}

func (u *JusanResponseCodeClpUsage) JusanResponseCodeClp_ClpPaymentExpired() JusanResponseCodeClp {
	u.Use(JusanResponseCodeClp_ClpPaymentExpired)
	return JusanResponseCodeClp_ClpPaymentExpired
}

func (u *JusanResponseCodeClpUsage) JusanResponseCodeClp_ClpAuthError() JusanResponseCodeClp {
	u.Use(JusanResponseCodeClp_ClpAuthError)
	return JusanResponseCodeClp_ClpAuthError
}

func (u *JusanResponseCodeClpUsage) JusanResponseCodeClp_ClpIncorrectCardNum() JusanResponseCodeClp {
	u.Use(JusanResponseCodeClp_ClpIncorrectCardNum)
	return JusanResponseCodeClp_ClpIncorrectCardNum
}

func (u *JusanResponseCodeClpUsage) JusanResponseCodeClp_ClpSuspectedFraud() JusanResponseCodeClp {
	u.Use(JusanResponseCodeClp_ClpSuspectedFraud)
	return JusanResponseCodeClp_ClpSuspectedFraud
}

func (u *JusanResponseCodeClpUsage) JusanResponseCodeClp_ClpUndefinedError() JusanResponseCodeClp {
	u.Use(JusanResponseCodeClp_ClpUndefinedError)
	return JusanResponseCodeClp_ClpUndefinedError
}

func (u *JusanResponseCodeClpUsage) JusanResponseCodeClp_ClpExceedsLimit() JusanResponseCodeClp {
	u.Use(JusanResponseCodeClp_ClpExceedsLimit)
	return JusanResponseCodeClp_ClpExceedsLimit
}

func (u *JusanResponseCodeClpUsage) JusanResponseCodeClp_ClpLimitedCard() JusanResponseCodeClp {
	u.Use(JusanResponseCodeClp_ClpLimitedCard)
	return JusanResponseCodeClp_ClpLimitedCard
}

func (u *JusanResponseCodeClpUsage) JusanResponseCodeClp_ClpTransactionDeclined() JusanResponseCodeClp {
	u.Use(JusanResponseCodeClp_ClpTransactionDeclined)
	return JusanResponseCodeClp_ClpTransactionDeclined
}

func (u *JusanResponseCodeClpUsage) JusanResponseCodeClp_ClpNotEnough() JusanResponseCodeClp {
	u.Use(JusanResponseCodeClp_ClpNotEnough)
	return JusanResponseCodeClp_ClpNotEnough
}

func (u *JusanResponseCodeClpUsage) JusanResponseCodeClp_ClpLimitExceeded() JusanResponseCodeClp {
	u.Use(JusanResponseCodeClp_ClpLimitExceeded)
	return JusanResponseCodeClp_ClpLimitExceeded
}

func (u *JusanResponseCodeClpUsage) JusanResponseCodeClp_ClpErrorAuth() JusanResponseCodeClp {
	u.Use(JusanResponseCodeClp_ClpErrorAuth)
	return JusanResponseCodeClp_ClpErrorAuth
}

func (u *JusanResponseCodeClpUsage) JusanResponseCodeClp_ClpCardInactive() JusanResponseCodeClp {
	u.Use(JusanResponseCodeClp_ClpCardInactive)
	return JusanResponseCodeClp_ClpCardInactive
}
