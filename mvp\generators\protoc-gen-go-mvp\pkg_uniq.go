package main

import (
	"fmt"
	"strconv"
	"strings"
	"sync"

	"google.golang.org/protobuf/compiler/protogen"
	"google.golang.org/protobuf/proto"
	"google.golang.org/protobuf/reflect/protoreflect"

	"git.local/sensitive/mvp/pb"
)

type LoggerMap struct {
	FuncName   string
	FuncNameGo string
	Key        protogen.GoIdent
	Value      protogen.GoIdent
}
type Uniq struct {
	Data            [][]any
	DataGo          [][]any
	FuncName        string
	FuncNameGo      string
	NeedSlice       bool
	SliceData       [][]any
	SliceDataGo     [][]any
	SliceFuncName   string
	SliceFuncNameGo string
	Enum            *protogen.Enum
	Message         *protogen.Message
	LoggerMap       *LoggerMap
}

func (u *Uniq) P(data ...any) {
	u.Data = append(u.Data, data)
}

func (u *Uniq) PGo(data ...any) {
	u.DataGo = append(u.DataGo, data)
}

func (u *Uniq) PSlice(data ...any) {
	u.SliceData = append(u.SliceData, data)
}

func (u *Uniq) PSliceGo(data ...any) {
	u.SliceDataGo = append(u.SliceDataGo, data)
}

func EnumInfo(
	file *protogen.File,
	enum *protogen.Enum,
	linear map[protoreflect.FullName]*Uniq,
	funcNameStorage map[string]struct{},
) *Uniq {
	if result, ok := linear[enum.Desc.FullName()]; ok {
		return result
	}

	funcName := strings.ToLower(file.GoDescriptorIdent.GoName) + "_enum_" + enum.GoIdent.GoName
	funcName = UniqueFuncName(funcName, funcNameStorage)
	g := &Uniq{
		Enum:            enum,
		FuncName:        funcName + "ToZap",
		FuncNameGo:      funcName + "ToGo",
		SliceFuncName:   funcName + "SliceToZap",
		SliceFuncNameGo: funcName + "SliceToGo",
	}
	linear[enum.Desc.FullName()] = g

	ext, _ := proto.GetExtension(enum.Desc.Options(), pb.E_EnumLoggerLevel).([]pb.LoggerLevel)
	extMap := LoggerLevelsToMap(ext)

	func() {
		g.P("func ", g.FuncName, "(")
		g.P("label string,")
		g.P("in ", enum.GoIdent, ",")
		g.P(") ", zapField, " {")

		g.PGo("func ", g.FuncNameGo, "(")
		g.PGo("label string,")
		g.PGo("in ", enum.GoIdent, ",")
		g.PGo(") any {")

		if _, ok := extMap[pb.LoggerLevel_SkipAll]; ok {
			g.P("return ", zapAny, "(label, nil)")
			g.P("}")
			g.P()

			g.PGo("return nil")
			g.PGo("}")
			g.PGo()
			return
		}

		if _, ok := extMap[pb.LoggerLevel_Hidden]; ok {
			g.P("return ", zapField, "{")
			g.P(zapAny, "(label, \"[***hidden***]\"),")
			g.P("}")
			g.P("}")
			g.P()

			g.PGo("return map[string]any{label:\"[***hidden***]\"}")
			g.PGo("}")
			g.PGo()
			return
		}

		g.P("str, ok := ", enum.GoIdent, "_name[int32(in)]")
		g.P("if !ok {")
		g.P("str = ", fmtSprintf, "(\"unknown ", enum.GoIdent, " value %d\", in)")
		g.P("}")
		g.P()
		g.P("return ", zapDict, "(")
		g.P("label,")
		g.P(zapAny, "(\"value\",", enum.GoIdent, "(in)),")
		g.P(zapAny, "(\"name\",str),")
		g.P(")")
		g.P("}")
		g.P()

		g.PGo("str, ok := ", enum.GoIdent, "_name[int32(in)]")
		g.PGo("if !ok {")
		g.PGo("str = ", fmtSprintf, "(\"unknown ", enum.GoIdent, " value %d\", in)")
		g.PGo("}")
		g.PGo()
		g.PGo("return map[string]any{ ")
		g.PGo("label: map[string]any{")
		g.PGo("\"value\":", enum.GoIdent, "(in),")
		g.PGo("\"name\":str,")
		g.PGo("},")
		g.PGo("}")
		g.PGo("}")
		g.PGo()
	}()

	g.PSlice("func ", g.SliceFuncName, "(label string, in []", enum.GoIdent, ") ", zapField, " {")
	g.PSlice("var res []", zapField)
	g.PSlice("for index, val := range in {")
	g.PSlice("res = append(res, ", g.FuncName, "(", fmtSprint, "(index), val))")
	g.PSlice("}")
	g.PSlice()
	g.PSlice("return ", zapDict, "(label, res...)")
	g.PSlice("}")

	g.PSliceGo("func ", g.SliceFuncNameGo, "(label string, in []", enum.GoIdent, ") any {")
	g.PSliceGo("var res []any")
	g.PSliceGo("for index, val := range in {")
	g.PSliceGo("res = append(res, ", g.FuncNameGo, "(", fmtSprint, "(index), val))")
	g.PSliceGo("}")
	g.PSliceGo()
	g.PSliceGo("return res")
	g.PSliceGo("}")

	return g
}

func MapMessageInfo(
	file *protogen.File,
	message *protogen.Message,
	linear map[protoreflect.FullName]*Uniq,
	mapper map[protoreflect.FullName]map[protoreflect.FullName]*Uniq,
	funcNameStorage map[string]struct{},
) *Uniq {
	if mapperValue, okMapper := mapper[message.Fields[0].Desc.FullName()]; okMapper {
		if mapperValue[message.Fields[1].Desc.FullName()], okMapper = mapperValue[message.Fields[1].Desc.FullName()]; okMapper {
			return mapperValue[message.Fields[1].Desc.FullName()]
		}
	}

	g := &Uniq{
		Message:   message,
		LoggerMap: ParseLoggerMap(file, message, funcNameStorage),
	}

	if _, ok := mapper[message.Fields[0].Desc.FullName()]; !ok {
		mapper[message.Fields[0].Desc.FullName()] = map[protoreflect.FullName]*Uniq{}
	}

	mapper[message.Fields[0].Desc.FullName()][message.Fields[1].Desc.FullName()] = g

	var pointer string
	if message.Fields[1].Desc.Kind() == protoreflect.MessageKind {
		pointer = "*"
	}

	ext, _ := proto.GetExtension(message.Desc.Options(), pb.E_MessageLoggerLevel).([]pb.LoggerLevel)
	extMap := LoggerLevelsToMap(ext)

	if _, ok := extMap[pb.LoggerLevel_SkipAll]; ok {
		g.P("func ", g.LoggerMap.FuncName, "(")
		g.P("label string,")
		g.P("in map[", g.LoggerMap.Key, "]", pointer, g.LoggerMap.Value, ",")
		g.P(") ", zapField, " {")
		g.P("return ", zapAny, "(label, nil)")
		g.P("}")
		g.P()

		g.PGo("func ", g.LoggerMap.FuncNameGo, "(")
		g.PGo("label string,")
		g.PGo("in map[", g.LoggerMap.Key, "]", pointer, g.LoggerMap.Value, ",")
		g.PGo(") any {")
		g.PGo("return map[string]any{label: nil}")
		g.PGo("}")
		g.PGo()
		return g
	}

	if _, ok := extMap[pb.LoggerLevel_Hidden]; ok {
		g.P("func ", g.LoggerMap.FuncName, "(")
		g.P("label string,")
		g.P("in map[", g.LoggerMap.Key, "]", pointer, g.LoggerMap.Value, ",")
		g.P(") ", zapField, " {")
		g.P("return ", zapAny, "(label, \"[***hidden***]\")")
		g.P("}")
		g.P()

		g.PGo("func ", g.LoggerMap.FuncNameGo, "(")
		g.PGo("label string,")
		g.PGo("in map[", g.LoggerMap.Key, "]", pointer, g.LoggerMap.Value, ",")
		g.PGo(") any {")
		g.PGo("return map[string]any{label: \"[***hidden***]\"}")
		g.PGo("}")
		g.PGo()
		return g
	}

	value := []any{"res = append(res, ", zapAny, "(", fmtSprint, "(key), val))"}
	valueGo := []any{"res = append(res, ", zapAny, "(", fmtSprint, "(key), val))"}
	if message.Fields[1].Desc.Kind() == protoreflect.MessageKind {
		messageInfo := MessageInfo(file, message.Fields[1].Message, linear, mapper, funcNameStorage)
		value = []any{"res = append(res, ", messageInfo.FuncName, "(", fmtSprint, "(key), val))"}
		valueGo = []any{"res = append(res, ", messageInfo.FuncNameGo, "(", fmtSprint, "(key), val))"}
	}

	g.P("func ", g.LoggerMap.FuncName, "(")
	g.P("label string,")
	g.P("in map[", g.LoggerMap.Key, "]", pointer, g.LoggerMap.Value, ",")
	g.P(") ", zapField, " {")
	g.P("var res []", zapField)
	g.P("for key, val := range in {")
	g.P(value...)
	g.P("}")
	g.P()
	g.P("return ", zapDict, "(label, res...)")
	g.P("}")
	g.P()

	g.PGo("func ", g.LoggerMap.FuncNameGo, "(")
	g.PGo("label string,")
	g.PGo("in map[", g.LoggerMap.Key, "]", pointer, g.LoggerMap.Value, ",")
	g.PGo(") any {")
	g.PGo("var res []any")
	g.PGo("for key, val := range in {")
	g.PGo(valueGo...)
	g.PGo("}")
	g.PGo()
	g.PGo("return map[string]any{label: res}")
	g.PGo("}")
	g.PGo()

	return g
}

func MessageInfo(
	file *protogen.File,
	message *protogen.Message,
	linear map[protoreflect.FullName]*Uniq,
	mapper map[protoreflect.FullName]map[protoreflect.FullName]*Uniq,
	funcNameStorage map[string]struct{},
) *Uniq {
	if result, ok := linear[message.Desc.FullName()]; ok {
		return result
	}

	if message.Desc.IsMapEntry() {
		return MapMessageInfo(file, message, linear, mapper, funcNameStorage)
	}

	funcName := strings.ToLower(file.GoDescriptorIdent.GoName) + "_message_" + message.GoIdent.GoName
	funcName = UniqueFuncName(funcName, funcNameStorage)
	g := &Uniq{
		Message:         message,
		FuncName:        funcName + "ToZap",
		FuncNameGo:      funcName + "ToGo",
		SliceFuncName:   funcName + "SliceToZap",
		SliceFuncNameGo: funcName + "SliceToGo",
	}
	linear[message.Desc.FullName()] = g

	ext, _ := proto.GetExtension(message.Desc.Options(), pb.E_MessageLoggerLevel).([]pb.LoggerLevel)
	extMap := LoggerLevelsToMap(ext)

	if _, ok := extMap[pb.LoggerLevel_SkipAll]; ok {
		g.P("func ", g.FuncName, "(")
		g.P("label string,")
		g.P("_ *", message.GoIdent, ",")
		g.P(") ", zapField, " {")
		g.P("return ", zapAny, "(label, nil)")
		g.P("}")
		g.P()

		g.PGo("func ", g.FuncNameGo, "(")
		g.PGo("label string,")
		g.PGo("_ *", message.GoIdent, ",")
		g.PGo(") any {")
		g.PGo("return map[string]any{label: nil}")
		g.PGo("}")
		g.PGo()

		g.PSlice("func ", g.SliceFuncName, "(")
		g.PSlice("label string,")
		g.PSlice("in []*", message.GoIdent, ",")
		g.PSlice(") ", zapField, " {")
		g.PSlice("return ", zapAny, "(label, nil)")
		g.PSlice("}")
		g.PSlice()
		g.PGo()

		g.PSliceGo("func ", g.SliceFuncNameGo, "(")
		g.PSliceGo("label string,")
		g.PSliceGo("in []*", message.GoIdent, ",")
		g.PSliceGo(") any {")
		g.PSliceGo("return map[string]any{label: nil}")
		g.PSliceGo("}")
		g.PSliceGo()
		return g
	}

	if _, ok := extMap[pb.LoggerLevel_Hidden]; ok {
		g.P("func ", g.FuncName, "(")
		g.P("label string,")
		g.P("_ *", message.GoIdent, ",")
		g.P(") ", zapField, " {")
		g.P("return ", zapAny, "(label, \"[***hidden***]\")")
		g.P("}")
		g.P()

		g.PGo("func ", g.FuncNameGo, "(")
		g.PGo("label string,")
		g.PGo("_ *", message.GoIdent, ",")
		g.PGo(") any {")
		g.PGo("return  map[string]any{label: \"[***hidden***]\"}")
		g.PGo("}")
		g.PGo()

		g.PSlice("func ", g.SliceFuncName, "(")
		g.PSlice("label string,")
		g.PSlice("in []*", message.GoIdent, ",")
		g.PSlice(") ", zapField, " {")
		g.PSlice("return ", zapAny, "(label: \"[***hidden***]\"}")
		g.PSlice("}")
		g.PSlice()

		g.PSliceGo("func ", g.SliceFuncNameGo, "(")
		g.PSliceGo("label string,")
		g.PSliceGo("in []*", message.GoIdent, ",")
		g.PSliceGo(") any {")
		g.PSliceGo("return  map[string]any{label: \"[***hidden***]\"}")
		g.PSliceGo("}")
		g.PSliceGo()
		return g
	}

	g.P("func ", g.FuncName, "(")
	g.P("label string,")
	g.P("in *", message.GoIdent, ",")
	g.P(") ", zapField, " {")
	g.P("if in == nil { return ", zapReflect, "(label, in) }")
	g.P()
	g.P("return ", zapDict, "(")
	g.P("label,")

	g.PGo("func ", g.FuncNameGo, "(")
	g.PGo("label string,")
	g.PGo("in *", message.GoIdent, ",")
	g.PGo(") any {")
	g.PGo("if in == nil { return map[string]any{label: in} }")
	g.PGo()
	g.PGo("return map[string]any{")
	g.PGo("label: map[string]any{")
	for _, field := range message.Fields {
		fieldExtension := proto.GetExtension(field.Desc.Options(), pb.E_FieldLoggerLevel).([]pb.LoggerLevel)
		fieldMap := LoggerLevelsToMap(fieldExtension)

		if _, ok := fieldMap[pb.LoggerLevel_SkipAll]; ok {
			g.P(zapAny, "(\"", field.GoName, "\", nil),")
			g.PGo("\"", field.GoName, "\":, nil,")
			continue
		}

		if _, ok := fieldMap[pb.LoggerLevel_Hidden]; ok {
			g.P(zapAny, "(\"", field.GoName, "\", \"[***hidden***]\"),")
			g.PGo("\"", field.GoName, "\": \"[***hidden***]\",")
			continue
		}

		if _, ok := extMap[pb.LoggerLevel_SkipSlice]; ok && field.Desc.IsList() {
			g.P(zapAny, "(\"", field.GoName, "\", nil),")
			g.PGo("\"", field.GoName, "\":, nil,")
			continue
		}

		if _, ok := extMap[pb.LoggerLevel_SkipMap]; ok && field.Desc.IsMap() {
			g.P(zapAny, "(\"", field.GoName, "\", \"[***hidden***]\"),")
			g.PGo("\"", field.GoName, "\":, nil,")
			continue
		}

		if field.Desc.IsMap() {
			m := MessageInfo(file, field.Message, linear, mapper, funcNameStorage)
			g.P(m.LoggerMap.FuncName, "(\"", field.GoName, "\", in.Get", field.GoName, "()),")
			g.PGo("\"", field.GoName, "\":", m.LoggerMap.FuncNameGo, "(\"", field.GoName, "\", in.Get", field.GoName, "()),")
			continue
		}

		switch field.Desc.Kind() {
		case protoreflect.MessageKind:
			m := MessageInfo(file, field.Message, linear, mapper, funcNameStorage)
			switch {
			case field.Desc.IsList():
				m.NeedSlice = true
				g.P(m.SliceFuncName, "(\"", field.GoName, "\", in.Get", field.GoName, "()),")
				g.PGo("\"", field.GoName, "\":", m.SliceFuncNameGo, "(\"", field.GoName, "\", in.Get", field.GoName, "()),")
			default:
				g.P(m.FuncName, "(\"", field.GoName, "\", in.Get", field.GoName, "()),")
				g.PGo("\"", field.GoName, "\":", m.FuncNameGo, "(\"", field.GoName, "\", in.Get", field.GoName, "()),")
			}
		case protoreflect.EnumKind:
			e := EnumInfo(file, field.Enum, linear, funcNameStorage)
			switch {
			case field.Desc.IsList():
				e.NeedSlice = true
				g.P(e.SliceFuncName, "(\"", field.GoName, "\", in.Get", field.GoName, "()),")
				g.PGo("\"", field.GoName, "\":", e.SliceFuncNameGo, "(\"", field.GoName, "\", in.Get", field.GoName, "()),")
			default:
				g.P(e.FuncName, "(\"", field.GoName, "\", in.Get", field.GoName, "()),")
				g.PGo("\"", field.GoName, "\":", e.FuncNameGo, "(\"", field.GoName, "\", in.Get", field.GoName, "()),")
			}
		default:
			g.P(zapAny, "(\"", field.GoName, "\", in.Get", field.GoName, "()),")
			g.PGo("\"", field.GoName, "\":in.Get", field.GoName, "(),")
		}
	}
	g.P(")")
	g.P("}")
	g.PGo("},")
	g.PGo("}")
	g.PGo("}")

	g.PSlice("func ", g.SliceFuncName, "(")
	g.PSlice("label string,")
	g.PSlice("in []*", message.GoIdent, ",")
	g.PSlice(") ", zapField, " {")
	g.PSlice("var res []", zapField)
	g.PSlice("for index, val := range in {")
	g.PSlice("res = append(res, ", g.FuncName, "(", fmtSprint, "(index), val))")
	g.PSlice("}")
	g.PSlice()
	g.PSlice("return ", zapDict, "(label, res...)")
	g.PSlice("}")
	g.PSlice()

	g.PSliceGo("func ", g.SliceFuncNameGo, "(")
	g.PSliceGo("label string,")
	g.PSliceGo("in []*", message.GoIdent, ",")
	g.PSliceGo(") any {")
	g.PSliceGo("var res []any")
	g.PSliceGo("for index, val := range in {")
	g.PSliceGo("res = append(res, ", g.FuncNameGo, "(", fmtSprint, "(index), val))")
	g.PSliceGo("}")
	g.PSliceGo()
	g.PSliceGo("return res")
	g.PSliceGo("}")
	g.PSlice()

	return g
}

func ParseLoggerMap(
	file *protogen.File,
	message *protogen.Message,
	funcNameStorage map[string]struct{},
) *LoggerMap {
	if len(message.Fields) != 2 {
		panic(fmt.Sprintf(
			"unsupported map %s (%s)",
			message.GoIdent.GoName,
			message.Location.Path.String(),
		))
	}

	keyIdent := file.GoDescriptorIdent

	keyField := message.Fields[0]
	valueField := message.Fields[1]

	switch keyField.Desc.Kind() {
	case protoreflect.Int32Kind, protoreflect.Sfixed32Kind, protoreflect.Sint32Kind:
		keyIdent.GoName = "int32"
	case protoreflect.Int64Kind, protoreflect.Sint64Kind, protoreflect.Sfixed64Kind:
		keyIdent.GoName = "int64"
	case protoreflect.Uint64Kind, protoreflect.Fixed64Kind:
		keyIdent.GoName = "uint64"
	case protoreflect.Uint32Kind, protoreflect.Fixed32Kind:
		keyIdent.GoName = "uint32"
	case protoreflect.StringKind:
		keyIdent.GoName = "string"
	default:
		panic(fmt.Sprintf(
			"unsupported key type at map %s (%s)",
			message.GoIdent.GoName,
			message.Location.Path.String(),
		))
	}

	valueIdent := file.GoDescriptorIdent

	var suffix string
	switch valueField.Desc.Kind() {
	case protoreflect.BoolKind:
		valueIdent.GoName = "bool"
		suffix = "Bool"
	case protoreflect.Int32Kind, protoreflect.Sfixed32Kind, protoreflect.Sint32Kind:
		valueIdent.GoName = "int32"
		suffix = "Int32"
	case protoreflect.Int64Kind, protoreflect.Sint64Kind, protoreflect.Sfixed64Kind:
		valueIdent.GoName = "int64"
		suffix = "Int64"
	case protoreflect.Uint64Kind, protoreflect.Fixed64Kind:
		valueIdent.GoName = "uint64"
		suffix = "Uint64"
	case protoreflect.Uint32Kind, protoreflect.Fixed32Kind:
		valueIdent.GoName = "uint32"
		suffix = "Uint32"
	case protoreflect.FloatKind:
		valueIdent.GoName = "float32"
		suffix = "Float"
	case protoreflect.DoubleKind:
		valueIdent.GoName = "float64"
		suffix = "Double"
	case protoreflect.StringKind:
		valueIdent.GoName = "string"
		suffix = "String"
	case protoreflect.BytesKind:
		valueIdent.GoName = "[]byte"
		suffix = "Bytes"
	case protoreflect.EnumKind:
		valueIdent = valueField.Enum.GoIdent
		var packageName string
		if file.GoImportPath != valueField.GoIdent.GoImportPath {
			split := strings.Split(string(valueField.GoIdent.GoImportPath), ".")
			packageName = split[len(split)-1] + "_"
		}
		suffix = packageName + valueField.Enum.GoIdent.GoName
	case protoreflect.MessageKind:
		valueIdent = valueField.Message.GoIdent

		var packageName string
		if file.GoImportPath != valueField.GoIdent.GoImportPath {
			split := strings.Split(string(valueField.GoIdent.GoImportPath), "/")
			packageName = split[len(split)-1] + "_"
		}
		suffix = packageName + valueField.Message.GoIdent.GoName
	}

	funcName := strings.ToLower(file.GoDescriptorIdent.GoName) + "_" + keyIdent.GoName + "_" + suffix
	funcName = UniqueFuncName(funcName, funcNameStorage)

	return &LoggerMap{
		FuncName:   funcName,
		FuncNameGo: funcName + "Go",
		Key:        keyIdent,
		Value:      valueIdent,
	}
}

func UniqueFuncName(
	funcName string,
	funcNameStorage map[string]struct{},
) string {
	var (
		index int
		once  sync.Once
	)
	for {
		if _, ok := funcNameStorage[funcName]; !ok {
			break
		}
		index++
		once.Do(func() {
			funcName += "_"
		})
		funcName += strconv.Itoa(index)

	}
	funcNameStorage[funcName] = struct{}{}
	return funcName
}
