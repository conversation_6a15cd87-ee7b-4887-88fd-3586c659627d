// Code generated by protoc-gen-go-mvp. DO NOT EDIT.
// versions:
// - protoc-gen-go-mvp v2.0.1
// created by mvp authors

package grpc

import (
	dog "git.local/sensitive/sdk/dog"
	gin "github.com/gin-gonic/gin"
	metadata "google.golang.org/grpc/metadata"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
	http "net/http"
)

func RegisterGinCollectorRouterGroup(
	in *gin.RouterGroup,
	handler func(func(c *gin.Context) error) gin.HandlerFunc,
) error {
	service, err := NewGinCollectorService()
	if err != nil {
		return err
	}

	routerGroup := in.Group("processing.anti_fraud.collector.Collector")
	routerGroup.PUT("/CollectTransaction", handler(service.CollectTransaction))
	routerGroup.PUT("/GetTransactionStatus", handler(service.GetTransactionStatus))
	return nil
}

func NewGinCollectorService() (GinCollectorServer, error) {
	client, err := NewPreparedCollectorClient()
	if err != nil {
		return nil, err
	}

	return &ginCollectorServer{
		client: NewLoggedCollectorClient(
			NewIamCollectorClient(client),
		),
	}, nil
}

//go:generate mockgen -destination=./grpcmock/collector.gin.pb.go -package=grpcmock -source=collector.gin.pb.go GinCollectorServer
type GinCollectorServer interface {
	CollectTransaction(c *gin.Context) error
	GetTransactionStatus(c *gin.Context) error
}

var _ GinCollectorServer = (*ginCollectorServer)(nil)

type ginCollectorServer struct {
	client CollectorClient
}

type Collector_CollectTransaction_Success struct {
	// Status is the status of the response. Set true
	Status bool `json:"status"`
	// Message is a human-readable explanation of the status
	Message string `json:"message"`
	// StatusCode is the GoErr status. Set 0
	StatusCode int `json:"status_code"`
	// Result is the result of the request
	Result *CollectorEmptyResponse `json:"result"`
}

type Collector_CollectTransaction_Failure struct {
	// Status is the status of the response. Set false
	Status bool `json:"status"`
	// Message is a human-readable explanation of the status
	Message string `json:"message"`
	// StatusCode is the GoErr status
	StatusCode int `json:"status_code"`
}

// CollectTransaction
// @Summary  CollectTransaction method is used to collect transaction information and send it to anti-fraud F.A.C.C.T system
// @Security bearerAuth
// @ID Collector_CollectTransaction
// @Accept json
// @Param request body CollectTransactionRequestV1 true "CollectTransactionRequestV1"
// @Success 200 {object} Collector_CollectTransaction_Success
// @Failure 401 {object} Collector_CollectTransaction_Failure "goerrpkg.ErrorTypeAuthorization"
// @Failure 404 {object} Collector_CollectTransaction_Failure "goerrpkg.ErrorTypeNotFound"
// @Failure 400 {object} Collector_CollectTransaction_Failure "goerrpkg.ErrorTypeBadRequest"
// @Failure 500 {object} Collector_CollectTransaction_Failure "goerrpkg.ErrorTypeUnexpectedError"
// @Failure 400 {object} Collector_CollectTransaction_Failure "gin.ErrorTypeBind"
// @Failure 500 {object} Collector_CollectTransaction_Failure "Undefined error"
// @Produce json
// @Router /processing.anti_fraud.collector.Collector/CollectTransaction [put]
// @tags Collector
func (s *ginCollectorServer) CollectTransaction(c *gin.Context) error {
	ctx, span := dog.CreateSpan(c.Request.Context(), "GinCollectorServer_CollectTransaction")
	defer span.End()

	var request CollectTransactionRequestV1
	if err := c.ShouldBindJSON(&request); err != nil {
		return err
	}

	ctx = metadata.NewOutgoingContext(ctx, metadata.MD(c.Request.Header))

	response, err := s.client.CollectTransaction(ctx, &request)
	if err != nil {
		return err
	}

	c.JSON(http.StatusOK, &Collector_CollectTransaction_Success{
		Status:     true,
		Message:    "Success",
		StatusCode: 0,
		Result:     response,
	})
	return nil
}

type Collector_GetTransactionStatus_Success struct {
	// Status is the status of the response. Set true
	Status bool `json:"status"`
	// Message is a human-readable explanation of the status
	Message string `json:"message"`
	// StatusCode is the GoErr status. Set 0
	StatusCode int `json:"status_code"`
	// Result is the result of the request
	Result *emptypb.Empty `json:"result"`
}

type Collector_GetTransactionStatus_Failure struct {
	// Status is the status of the response. Set false
	Status bool `json:"status"`
	// Message is a human-readable explanation of the status
	Message string `json:"message"`
	// StatusCode is the GoErr status
	StatusCode int `json:"status_code"`
}

// GetTransactionStatus
// @Summary  jobs
// @Security bearerAuth
// @ID Collector_GetTransactionStatus
// @Accept json
// @Param request body emptypb.Empty true "Empty"
// @Success 200 {object} Collector_GetTransactionStatus_Success
// @Failure 401 {object} Collector_GetTransactionStatus_Failure "goerrpkg.ErrorTypeAuthorization"
// @Failure 404 {object} Collector_GetTransactionStatus_Failure "goerrpkg.ErrorTypeNotFound"
// @Failure 400 {object} Collector_GetTransactionStatus_Failure "goerrpkg.ErrorTypeBadRequest"
// @Failure 500 {object} Collector_GetTransactionStatus_Failure "goerrpkg.ErrorTypeUnexpectedError"
// @Failure 400 {object} Collector_GetTransactionStatus_Failure "gin.ErrorTypeBind"
// @Failure 500 {object} Collector_GetTransactionStatus_Failure "Undefined error"
// @Produce json
// @Router /processing.anti_fraud.collector.Collector/GetTransactionStatus [put]
// @tags Collector
func (s *ginCollectorServer) GetTransactionStatus(c *gin.Context) error {
	ctx, span := dog.CreateSpan(c.Request.Context(), "GinCollectorServer_GetTransactionStatus")
	defer span.End()

	var request emptypb.Empty
	if err := c.ShouldBindJSON(&request); err != nil {
		return err
	}

	ctx = metadata.NewOutgoingContext(ctx, metadata.MD(c.Request.Header))

	response, err := s.client.GetTransactionStatus(ctx, &request)
	if err != nil {
		return err
	}

	c.JSON(http.StatusOK, &Collector_GetTransactionStatus_Success{
		Status:     true,
		Message:    "Success",
		StatusCode: 0,
		Result:     response,
	})
	return nil
}
