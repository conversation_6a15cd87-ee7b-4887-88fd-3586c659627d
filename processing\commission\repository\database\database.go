package database

import (
	"context"
	"git.local/sensitive/processing/commission/model"
	"time"
)

//go:generate go run go.uber.org/mock/mockgen -package=databasemocks -destination=./databasemocks/mocks.go -source=database.go *
type AcquirerOptioner interface {
	Create(ctx context.Context, acquirerOpt model.AcquirerOption) (model.AcquirerOption, error)
	GetOptions(
		ctx context.Context,
		acquirerId, merchantID, projectID, aggregatedTypeID uint64,
	) (model.CommonOptionFinder[model.AcquirerOption], error)
	Update(ctx context.Context, optionID uint64, option model.AcquirerOption) error
	GetByID(ctx context.Context, optionID uint64) (model.AcquirerOption, error)
}

type AcquirerCommissioner interface {
	Create(ctx context.Context, acquirerComm model.AcquirerCommission) error
	GetByID(ctx context.Context, id uint64) (model.AcquirerCommission, error)
	UpdateExpiracy(ctx context.Context, id uint64, startDate, endDate *time.Time) error
	GetCommissionsByOptionID(
		ctx context.Context,
		optionID uint64,
		transactionCreatedAt time.Time,
	) (model.CommissionFinder[model.AcquirerCommission], error)
}

type ProjectLowerOptioner interface {
	Create(ctx context.Context, projectOpt model.ProjectLowerOption) (model.ProjectLowerOption, error)
	GetOptions(
		ctx context.Context,
		acquirerId, merchantID, projectID, aggregatedTypeID, lowerOptionTypeID uint64,
	) (model.CommonOptionFinder[model.ProjectLowerOption], error)
	Update(ctx context.Context, optionID uint64, option model.ProjectLowerOption) error
	GetByID(ctx context.Context, optionID uint64) (model.ProjectLowerOption, error)
}

type ProjectLowerCommissioner interface {
	Create(ctx context.Context, projectComm model.ProjectLowerCommission) error
	GetByID(ctx context.Context, id uint64) (model.ProjectLowerCommission, error)
	UpdateExpiracy(ctx context.Context, id uint64, startDate, endDate *time.Time) error
	GetCommissionsByOptionID(
		ctx context.Context,
		optionID uint64,
		transactionCreatedAt time.Time,
	) (model.CommissionFinder[model.ProjectLowerCommission], error)
}

type ProjectUpperOptioner interface {
	Create(ctx context.Context, projectOpt model.ProjectUpperOption) (model.ProjectUpperOption, error)
	GetWithCommissionsByID(ctx context.Context, id uint64) (model.ProjectUpperOptionWithCommissions, error)
	GetOptions(ctx context.Context, merchantID, projectID, aggregatedTypeID uint64) (model.UpperOptionFinder, error)
	Update(ctx context.Context, optionID uint64, option model.ProjectUpperOption) error
	GetByID(ctx context.Context, optionID uint64) (model.ProjectUpperOption, error)
}

type ProjectUpperCommissioner interface {
	Create(ctx context.Context, projectComm model.ProjectUpperCommission) error
	GetByID(ctx context.Context, id uint64) (model.ProjectUpperCommission, error)
	UpdateExpiracy(ctx context.Context, id uint64, startDate, endDate *time.Time) error
	GetCommissionsByOptionID(
		ctx context.Context,
		optionID uint64,
		transactionCreatedAt time.Time,
	) (model.CommissionFinder[model.ProjectUpperCommission], error)
}

type TransactionCommissioner interface {
	Create(ctx context.Context, transactionID, upperCommissionID uint64, upperCommissionAmount float64) error
	GetByTransactionID(ctx context.Context, transactionID uint64) (model.TransactionCommission, error)
	UpdateByTransactionID(
		ctx context.Context,
		transactionID uint64,
		transactionComm model.TransactionCommission,
	) error
}

type ProjectLowerOptionType interface {
	GetByCode(
		ctx context.Context,
		lowerOptionCode model.ProjectLowerOptionTypeCode,
	) (model.ProjectLowerOptionType, error)
	GetAll(ctx context.Context) ([]model.ProjectLowerOptionType, error)
	GetByID(ctx context.Context, optionTypeID uint64) (model.ProjectLowerOptionType, error)
}
