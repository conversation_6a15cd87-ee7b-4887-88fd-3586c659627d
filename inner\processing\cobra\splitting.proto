edition = "2023";

package processing.cobra.splitting;

option go_package = "git.local/sensitive/innerpb/processing/cobra";

import "mvp/proto/cobra.proto";
import "google/protobuf/descriptor.proto";

option (mvp.cobra_root) = true;

service SplittingService {
  option (mvp.cobra_config) = {
    Components: [
      GinEngineInit,
      GormInit,
      GTransactionManager,
      BackoffPolicy,
      HttpLogger,
      MongoInit,
      NatsInit,
      RedisInit,
      CSRF,
      JetStreamContext,
      NatsConn,
      NatsMessageRouter,
      Stand
    ],
    ServiceNames: ["splitting", "splitting", "Splitting"],
    package: "git.local/sensitive/processing/splitting",
    func_name: "SplittingService",



    restHandler: [
      {
        name: "Splitting"
        layers: [
          {
            name: "SplittingService"
            type: UseCaseLayer
            layers: [
              {
                type: ServiceLayer,
                name: "SplittingCalculatorService",
                layers: [
                  { type: GrpcClient, name: "Commission" },
                  { type: RepoLayer, name: "SplittingDB", components: [GormInit], package_path: "/database" }
                ]
              }
            ]
          }
        ]
      }
    ],

    grpcHandler: [
      {
        name: "Splitting"
        layers: [
          {
            type: ServiceLayer,
            name: "SplittingService",
            layers: [
              { type: GrpcClient, name: "Commission" },
              { type: RepoLayer, name: "SplittingDB", components: [GormInit], package_path: "/database" }
            ]
          }
        ]
      }
    ]
  };
}