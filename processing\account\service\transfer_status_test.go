package service

import (
	"context"
	"errors"
	"git.local/sensitive/processing/account/repository/databasemocks"
	"github.com/golang/mock/gomock"
	"testing"

	"git.local/sensitive/processing/account/model"
	"github.com/stretchr/testify/require"
)

func TestGetAllTransferStatuses(t *testing.T) {
	type getAllOp struct {
		output    []model.TransferStatus
		outputErr error
	}

	tests := []struct {
		name    string
		want    []model.TransferStatus
		wantErr error
		getAll  getAllOp
	}{
		{
			name: "error",
			getAll: getAllOp{
				output:    nil,
				outputErr: errors.New("some error"),
			},
			wantErr: errors.New("some error"),
		},
		{
			name: "success",
			getAll: getAllOp{
				output: []model.TransferStatus{
					{
						ID:   23,
						Code: "asdasd",
						Name: "dfsdfsdfds",
					},
					{
						ID:   24,
						Code: "asdasd",
						Name: "dfsdfsdfds",
					},
				},
				outputErr: nil,
			},
			wantErr: nil,
			want: []model.TransferStatus{
				{
					ID:   23,
					Code: "asdasd",
					Name: "dfsdfsdfds",
				},
				{
					ID:   24,
					Code: "asdasd",
					Name: "dfsdfsdfds",
				},
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctrl := gomock.NewController(t)
			defer ctrl.Finish()

			transferStatusDBMock := databasemocks.NewMockTransferStatuser(ctrl)

			transferStatusDBMock.EXPECT().GetAll(
				gomock.Any(),
			).Return(tt.getAll.output, tt.getAll.outputErr).Times(1)

			s := NewTransferStatusService(transferStatusDBMock)

			got, err := s.GetAll(context.Background())
			if tt.wantErr != nil {
				require.Error(t, err)
				require.Equal(t, tt.wantErr, err)
			} else {
				require.NoError(t, err)
			}

			require.Equal(t, tt.want, got)
		})
	}
}

func TestTransferStatusService_GetSuccess(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	transferStatusDBMock := databasemocks.NewMockTransferStatuser(ctrl)
	transferStatusDBMock.EXPECT().
		GetSuccess(gomock.Any()).
		Return(&model.TransferStatus{
			ID:   4,
			Code: "success",
			Name: "Success",
		}, nil)

	s := NewTransferStatusService(transferStatusDBMock)

	resp, err := s.GetSuccess(context.TODO())

	require.Equal(t, resp, &model.TransferStatus{
		ID:   4,
		Code: "success",
		Name: "Success",
	})
	require.NoError(t, err)
}
