package service

import (
	"context"
	"crypto/aes"
	"encoding/json"
	"errors"
	"testing"

	"git.local/sensitive/innerpb/processing/goerr"
	"git.local/sensitive/innerpb/processing/grpc/grpcmock"
	"git.local/sensitive/pkg/encryptor"
	"git.local/sensitive/testsdk"

	"github.com/golang/mock/gomock"
	"github.com/stretchr/testify/require"

	"git.local/sensitive/innerpb/processing/grpc"
	"git.local/sensitive/pkg/middlewares"
	"git.local/sensitive/processing/card/model"
	"git.local/sensitive/processing/card/repository/database/databasemocks"
	"git.local/sensitive/processing/card/schema"
	"git.local/sensitive/sdk/dog/e2e"
)

func TestGetClientCards(t *testing.T) {
	type getClientCardsOp struct {
		inputPagination *middlewares.PaginationInfo
		inputClientID   string
		inputProjectID  uint64
		output          []*model.Card
		outputErr       error
	}

	tests := []struct {
		name               string
		reqPagination      *middlewares.PaginationInfo
		reqProjectClientID string
		reqProjectID       uint64
		want               []*model.Card
		wantErr            error
		getClientCards     getClientCardsOp
	}{
		{
			name: "error",
			reqPagination: &middlewares.PaginationInfo{
				PerPage:    1,
				Page:       1,
				Pagination: true,
			},
			reqProjectClientID: "asd",
			reqProjectID:       1,
			wantErr:            errors.New("some error"),
			want:               nil,
			getClientCards: getClientCardsOp{
				inputPagination: &middlewares.PaginationInfo{
					PerPage:    1,
					Page:       1,
					Pagination: true,
				},
				inputClientID:  "asd",
				inputProjectID: 1,
				output:         nil,
				outputErr:      errors.New("some error"),
			},
		},
		{
			name: "success",
			reqPagination: &middlewares.PaginationInfo{
				PerPage:    1,
				Page:       1,
				Pagination: true,
			},
			reqProjectClientID: "asd",
			reqProjectID:       1,
			wantErr:            nil,
			want: []*model.Card{
				{
					Id:          1,
					MaskedPan:   "some pan",
					Pan:         "some pan",
					EncryptName: "some name",
					HashedPan:   "$2a$10$4q4g2vSqVd/9Q3ZpIlngheW01oTebFnMgqU.NydDm.OoGgjRFQKT.",
				},
			},
			getClientCards: getClientCardsOp{
				inputPagination: &middlewares.PaginationInfo{
					PerPage:    1,
					Page:       1,
					Pagination: true,
				},
				inputClientID:  "asd",
				inputProjectID: 1,
				output: []*model.Card{
					{
						Id:          1,
						MaskedPan:   "some pan",
						Pan:         "some pan",
						EncryptName: "some name",
						HashedPan:   "$2a$10$4q4g2vSqVd/9Q3ZpIlngheW01oTebFnMgqU.NydDm.OoGgjRFQKT.",
					},
				},
				outputErr: nil,
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctrl := gomock.NewController(t)

			cardsDBMock := databasemocks.NewMockCarder(ctrl)

			s := CardGetterService{
				cardsRepo: cardsDBMock,
			}

			cardsDBMock.EXPECT().GetClientCards(
				gomock.Any(),
				tt.getClientCards.inputPagination,
				tt.getClientCards.inputClientID,
				tt.getClientCards.inputProjectID,
			).Return(
				tt.getClientCards.output, tt.getClientCards.outputErr,
			)

			res, err := s.GetClientCards(context.Background(), tt.reqPagination, tt.reqProjectClientID, tt.reqProjectID)
			if tt.wantErr != nil {
				require.Error(t, err)
				require.ErrorContains(t, err, tt.wantErr.Error())
			} else {
				require.Equal(t, tt.wantErr, err)
			}

			require.Equal(t, tt.want, res)
		})
	}
}

func TestCardGetterGetProjectClients(t *testing.T) {
	type getProjectClientsOp struct {
		inputPagination *middlewares.PaginationInfo
		inputProjectID  uint64
		output          []*model.Client
		outputErr       error
	}

	tests := []struct {
		name              string
		reqPagination     *middlewares.PaginationInfo
		reqProjectID      uint64
		want              []*model.Client
		wantErr           error
		getProjectClients getProjectClientsOp
	}{
		{
			name: "error",
			reqPagination: &middlewares.PaginationInfo{
				PerPage:    1,
				Page:       1,
				Pagination: true,
			},
			reqProjectID: 1,
			want:         nil,
			wantErr:      errors.New("some error"),
			getProjectClients: getProjectClientsOp{
				inputPagination: &middlewares.PaginationInfo{
					PerPage:    1,
					Page:       1,
					Pagination: true,
				},
				inputProjectID: 1,
				output:         nil,
				outputErr:      errors.New("some error"),
			},
		},
		{
			name: "success",
			reqPagination: &middlewares.PaginationInfo{
				PerPage:    1,
				Page:       1,
				Pagination: true,
			},
			reqProjectID: 1,
			want: []*model.Client{
				{
					Id:              1,
					ProjectId:       1,
					ProjectClientId: "asd",
				},
			},
			wantErr: nil,
			getProjectClients: getProjectClientsOp{
				inputPagination: &middlewares.PaginationInfo{
					PerPage:    1,
					Page:       1,
					Pagination: true,
				},
				inputProjectID: 1,
				output: []*model.Client{
					{
						Id:              1,
						ProjectId:       1,
						ProjectClientId: "asd",
					},
				},
				outputErr: nil,
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctrl := gomock.NewController(t)

			clientDBMock := databasemocks.NewMockClientor(ctrl)

			s := CardGetterService{
				clientsRepo: clientDBMock,
			}

			clientDBMock.EXPECT().GetProjectClients(
				gomock.Any(),
				tt.getProjectClients.inputPagination,
				tt.getProjectClients.inputProjectID,
			).Return(
				tt.getProjectClients.output, tt.getProjectClients.outputErr)

			res, err := s.GetProjectClients(context.Background(), tt.reqPagination, tt.reqProjectID)
			if tt.wantErr != nil {
				require.Error(t, err)
				require.ErrorContains(t, err, tt.wantErr.Error())
			} else {
				require.NoError(t, err)
			}

			require.Equal(t, tt.want, res)
		})
	}
}

func TestGetCardTokens(t *testing.T) {
	defaultWithSep := true

	type getClientByProjectOp struct {
		inputProjectID       uint64
		inputProjectClientID string
		outputErr            error
		output               *model.Client
	}

	type getCardByIdAndClientOp struct {
		isCalled                   bool
		inputCardID, inputClientID uint64
		output                     model.Card
		outputErr                  error
	}

	type getKeyByIDOp struct {
		isCalled  bool
		input     uint64
		output    model.Key
		outputErr error
	}

	type getProjectMaskByProjectIDOp struct {
		isCalled  bool
		input     uint64
		output    *model.ProjectMaskFormat
		outputErr error
	}

	tests := []struct {
		name                      string
		req                       *schema.CardTokenRequest
		want                      *model.Card
		wantErr                   error
		getClientByProject        getClientByProjectOp
		getCardByIdAndClient      getCardByIdAndClientOp
		getKeyByID                getKeyByIDOp
		getProjectMaskByProjectID getProjectMaskByProjectIDOp
		appConfig                 map[string]any
	}{
		{
			name: "error_getting_by_project",
			req: &schema.CardTokenRequest{
				ProjectClientId: "asd",
				ProjectId:       1,
				CardId:          1,
			},
			want:    &model.Card{},
			wantErr: errors.New("some error"),
			getClientByProject: getClientByProjectOp{
				inputProjectID:       1,
				inputProjectClientID: "asd",
				outputErr:            errors.New("some error"),
				output:               nil,
			},
		},
		{
			name: "client_id_is_zero",
			req: &schema.CardTokenRequest{
				ProjectClientId: "asd",
				ProjectId:       1,
				CardId:          1,
			},
			want:    nil,
			wantErr: goerr.ErrClientNotFound,
			getClientByProject: getClientByProjectOp{
				inputProjectID:       1,
				inputProjectClientID: "asd",
				outputErr:            nil,
				output: &model.Client{
					Id: 0,
				},
			},
		},
		{
			name: "get_card_by_id_and_client_error",
			req: &schema.CardTokenRequest{
				ProjectClientId: "asd",
				ProjectId:       1,
				CardId:          1,
			},
			want:    &model.Card{},
			wantErr: errors.New("some error"),
			getClientByProject: getClientByProjectOp{
				inputProjectID:       1,
				inputProjectClientID: "asd",
				outputErr:            nil,
				output: &model.Client{
					Id:                 1,
					ProjectId:          1,
					ProjectClientId:    "asd",
					VerificationUserID: 1,
					IsBlocked:          false,
				},
			},
			getCardByIdAndClient: getCardByIdAndClientOp{
				isCalled:      true,
				inputCardID:   1,
				inputClientID: 1,
				outputErr:     errors.New("some error"),
				output:        model.Card{},
			},
		},
		{
			name: "get_key_error",
			req: &schema.CardTokenRequest{
				ProjectClientId: "asd",
				ProjectId:       1,
				CardId:          1,
			},
			want:    nil,
			wantErr: errors.New("some error"),
			getClientByProject: getClientByProjectOp{
				inputProjectID:       1,
				inputProjectClientID: "asd",
				outputErr:            nil,
				output: &model.Client{
					Id:                 1,
					ProjectId:          1,
					ProjectClientId:    "asd",
					VerificationUserID: 1,
					IsBlocked:          false,
				},
			},
			getCardByIdAndClient: getCardByIdAndClientOp{
				isCalled:      true,
				inputCardID:   1,
				inputClientID: 1,
				outputErr:     nil,
				output: model.Card{
					ClientId:      1,
					IssuerId:      1,
					IpsId:         1,
					CountryId:     1,
					SaveAccess:    true,
					Approved:      false,
					KeyId:         1,
					EncryptMonth:  "AAAAAAAAAAAAAAAAAAAAAM45CA6vQU2zcQJ3wyF7cpc=",
					EncryptYear:   "AAAAAAAAAAAAAAAAAAAAAM45CA6vQU2zcQJ3wyF7cpc=",
					EncryptName:   "AAAAAAAAAAAAAAAAAAAAAM45CA6vQU2zcQJ3wyF7cpc=",
					EncryptPan:    "AAAAAAAAAAAAAAAAAAAAAECsfK75SmtqDbta4UnLlH4=",
					IsPanModified: true,
				},
			},
			getKeyByID: getKeyByIDOp{
				isCalled:  true,
				input:     1,
				outputErr: errors.New("some error"),
				output:    model.Key{},
			},
		},
		{
			name: "decryption_error",
			req: &schema.CardTokenRequest{
				ProjectClientId: "asd",
				ProjectId:       1,
				CardId:          1,
			},
			want:    nil,
			wantErr: aes.KeySizeError(3),
			getClientByProject: getClientByProjectOp{
				inputProjectID:       1,
				inputProjectClientID: "asd",
				outputErr:            nil,
				output: &model.Client{
					Id:                 1,
					ProjectId:          1,
					ProjectClientId:    "asd",
					VerificationUserID: 1,
					IsBlocked:          false,
				},
			},
			getCardByIdAndClient: getCardByIdAndClientOp{
				isCalled:      true,
				inputCardID:   1,
				inputClientID: 1,
				outputErr:     nil,
				output: model.Card{
					ClientId:      1,
					IssuerId:      1,
					IpsId:         1,
					CountryId:     1,
					SaveAccess:    true,
					Approved:      false,
					KeyId:         1,
					EncryptMonth:  "AAAAAAAAAAAAAAAAAAAAAM45CA6vQU2zcQJ3wyF7cpc=",
					EncryptYear:   "AAAAAAAAAAAAAAAAAAAAAM45CA6vQU2zcQJ3wyF7cpc=",
					EncryptName:   "AAAAAAAAAAAAAAAAAAAAAM45CA6vQU2zcQJ3wyF7cpc=",
					EncryptPan:    "AAAAAAAAAAAAAAAAAAAAAECsfK75SmtqDbta4UnLlH4=",
					IsPanModified: true,
				},
			},
			getKeyByID: getKeyByIDOp{
				isCalled:  true,
				input:     1,
				outputErr: nil,
				output: model.Key{
					Id:  1,
					Key: "AAAAAAAAAAAAAAAAAAAAAD4r1NYivkHsOHdiq9dCiJiz0HJtt7wrVFA8fx/DenGL",
				},
			},
			appConfig: map[string]any{
				"SYMMETRIC_KEY": badKey,
			},
		},
		{
			name: "get_project_mask_by_project_id_error",
			req: &schema.CardTokenRequest{
				ProjectClientId: "asd",
				ProjectId:       1,
				CardId:          1,
			},
			want:    nil,
			wantErr: errors.New("some error"),
			getClientByProject: getClientByProjectOp{
				inputProjectID:       1,
				inputProjectClientID: "asd",
				outputErr:            nil,
				output: &model.Client{
					Id:                 1,
					ProjectId:          1,
					ProjectClientId:    "asd",
					VerificationUserID: 1,
					IsBlocked:          false,
				},
			},
			getCardByIdAndClient: getCardByIdAndClientOp{
				isCalled:      true,
				inputCardID:   1,
				inputClientID: 1,
				outputErr:     nil,
				output: model.Card{
					ClientId:      1,
					IssuerId:      1,
					IpsId:         1,
					CountryId:     1,
					SaveAccess:    true,
					Approved:      false,
					KeyId:         1,
					EncryptMonth:  "AAAAAAAAAAAAAAAAAAAAAM45CA6vQU2zcQJ3wyF7cpc=",
					EncryptYear:   "AAAAAAAAAAAAAAAAAAAAAM45CA6vQU2zcQJ3wyF7cpc=",
					EncryptName:   "AAAAAAAAAAAAAAAAAAAAAM45CA6vQU2zcQJ3wyF7cpc=",
					EncryptPan:    "AAAAAAAAAAAAAAAAAAAAAECsfK75SmtqDbta4UnLlH4=",
					IsPanModified: true,
				},
			},
			getKeyByID: getKeyByIDOp{
				isCalled:  true,
				input:     1,
				outputErr: nil,
				output: model.Key{
					Id:  1,
					Key: "AAAAAAAAAAAAAAAAAAAAAD4r1NYivkHsOHdiq9dCiJiz0HJtt7wrVFA8fx/DenGL",
				},
			},
			getProjectMaskByProjectID: getProjectMaskByProjectIDOp{
				isCalled:  true,
				input:     1,
				outputErr: errors.New("some error"),
				output:    nil,
			},
			appConfig: map[string]any{
				"SYMMETRIC_KEY": normalKey,
			},
		},
		{
			name: "get_masked_card_error",
			req: &schema.CardTokenRequest{
				ProjectClientId: "asd",
				ProjectId:       1,
				CardId:          1,
			},
			want:    nil,
			wantErr: goerr.ErrPanLength,
			getClientByProject: getClientByProjectOp{
				inputProjectID:       1,
				inputProjectClientID: "asd",
				outputErr:            nil,
				output: &model.Client{
					Id:                 1,
					ProjectId:          1,
					ProjectClientId:    "asd",
					VerificationUserID: 1,
					IsBlocked:          false,
				},
			},
			getCardByIdAndClient: getCardByIdAndClientOp{
				isCalled:      true,
				inputCardID:   1,
				inputClientID: 1,
				outputErr:     nil,
				output: model.Card{
					ClientId:      1,
					IssuerId:      1,
					IpsId:         1,
					CountryId:     1,
					SaveAccess:    true,
					Approved:      false,
					KeyId:         1,
					EncryptMonth:  "AAAAAAAAAAAAAAAAAAAAAM45CA6vQU2zcQJ3wyF7cpc=",
					EncryptYear:   "AAAAAAAAAAAAAAAAAAAAAM45CA6vQU2zcQJ3wyF7cpc=",
					EncryptName:   "AAAAAAAAAAAAAAAAAAAAAM45CA6vQU2zcQJ3wyF7cpc=",
					EncryptPan:    "AAAAAAAAAAAAAAAAAAAAAECsfK75SmtqDbta4UnLlH4=",
					IsPanModified: true,
				},
			},
			getKeyByID: getKeyByIDOp{
				isCalled:  true,
				input:     1,
				outputErr: nil,
				output: model.Key{
					Id:  1,
					Key: "AAAAAAAAAAAAAAAAAAAAAD4r1NYivkHsOHdiq9dCiJiz0HJtt7wrVFA8fx/DenGL",
				},
			},
			getProjectMaskByProjectID: getProjectMaskByProjectIDOp{
				isCalled:  true,
				input:     1,
				outputErr: nil,
				output: &model.ProjectMaskFormat{
					Id:              1,
					ProjectId:       1,
					PlaceholderSign: "*",
					WithSeparator:   &defaultWithSep,
				},
			},
			appConfig: map[string]any{
				"SYMMETRIC_KEY": normalKey,
			},
		},
		{
			name: "success",
			req: &schema.CardTokenRequest{
				ProjectClientId: "asd",
				ProjectId:       1,
				CardId:          1,
			},
			want: &model.Card{
				ClientId:      1,
				IssuerId:      1,
				IpsId:         1,
				CountryId:     1,
				SaveAccess:    true,
				Tokens:        nil,
				Approved:      false,
				KeyId:         1,
				HashedPan:     "",
				EncryptMonth:  "AAAAAAAAAAAAAAAAAAAAAM45CA6vQU2zcQJ3wyF7cpc=",
				EncryptYear:   "AAAAAAAAAAAAAAAAAAAAAM45CA6vQU2zcQJ3wyF7cpc=",
				EncryptName:   "AAAAAAAAAAAAAAAAAAAAAM45CA6vQU2zcQJ3wyF7cpc=",
				EncryptPan:    "AAAAAAAAAAAAAAAAAAAAACXJ7eT6CzNrPzsbyIRluEFgK65FdiS3n0szvcsdV2Tb",
				MaskedPan:     "4342-07******-3288",
				Pan:           "",
				IsPanModified: true,
			},
			wantErr: nil,
			getClientByProject: getClientByProjectOp{
				inputProjectID:       1,
				inputProjectClientID: "asd",
				outputErr:            nil,
				output: &model.Client{
					Id:                 1,
					ProjectId:          1,
					ProjectClientId:    "asd",
					VerificationUserID: 1,
					IsBlocked:          false,
				},
			},
			getCardByIdAndClient: getCardByIdAndClientOp{
				isCalled:      true,
				inputCardID:   1,
				inputClientID: 1,
				outputErr:     nil,
				output: model.Card{
					ClientId:      1,
					IssuerId:      1,
					IpsId:         1,
					CountryId:     1,
					SaveAccess:    true,
					Approved:      false,
					KeyId:         1,
					EncryptMonth:  "AAAAAAAAAAAAAAAAAAAAAM45CA6vQU2zcQJ3wyF7cpc=",
					EncryptYear:   "AAAAAAAAAAAAAAAAAAAAAM45CA6vQU2zcQJ3wyF7cpc=",
					EncryptName:   "AAAAAAAAAAAAAAAAAAAAAM45CA6vQU2zcQJ3wyF7cpc=",
					EncryptPan:    "AAAAAAAAAAAAAAAAAAAAACXJ7eT6CzNrPzsbyIRluEFgK65FdiS3n0szvcsdV2Tb",
					IsPanModified: true,
				},
			},
			getKeyByID: getKeyByIDOp{
				isCalled:  true,
				input:     1,
				outputErr: nil,
				output: model.Key{
					Id:  1,
					Key: "AAAAAAAAAAAAAAAAAAAAAD4r1NYivkHsOHdiq9dCiJiz0HJtt7wrVFA8fx/DenGL",
				},
			},
			getProjectMaskByProjectID: getProjectMaskByProjectIDOp{
				isCalled:  true,
				input:     1,
				outputErr: nil,
				output: &model.ProjectMaskFormat{
					Id:              1,
					ProjectId:       1,
					PlaceholderSign: "*",
					WithSeparator:   &defaultWithSep,
				},
			},
			appConfig: map[string]any{
				"SYMMETRIC_KEY": normalKey,
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctrl := gomock.NewController(t)

			ctx, cancel := context.WithCancel(context.Background())
			e2e.InitAppE2E(t, ctx, cancel, nil, nil, tt.appConfig)

			clientDBMock := databasemocks.NewMockClientor(ctrl)
			cardDBMock := databasemocks.NewMockCarder(ctrl)
			keyDBMock := databasemocks.NewMockKeyer(ctrl)
			projectMaskFormatDBMock := databasemocks.NewMockProjectMaskFormatter(ctrl)

			clientDBMock.EXPECT().GetByProject(
				gomock.Any(),
				tt.getClientByProject.inputProjectID,
				tt.getClientByProject.inputProjectClientID,
			).Return(
				tt.getClientByProject.output,
				tt.getClientByProject.outputErr,
			)

			if tt.getCardByIdAndClient.isCalled {
				cardDBMock.EXPECT().GetByIdAndClient(
					gomock.Any(),
					tt.getCardByIdAndClient.inputCardID,
					tt.getCardByIdAndClient.inputClientID,
				).Return(
					tt.getCardByIdAndClient.output,
					tt.getCardByIdAndClient.outputErr,
				)
			}

			if tt.getKeyByID.isCalled {
				keyDBMock.EXPECT().GetById(
					gomock.Any(),
					tt.getKeyByID.input,
				).Return(
					tt.getKeyByID.output,
					tt.getKeyByID.outputErr,
				)
			}

			if tt.getProjectMaskByProjectID.isCalled {
				projectMaskFormatDBMock.EXPECT().GetByProjectID(
					gomock.Any(),
					tt.getProjectMaskByProjectID.input,
				).Return(
					tt.getProjectMaskByProjectID.output,
					tt.getProjectMaskByProjectID.outputErr,
				)
			}

			s := CardGetterService{
				projectMaskFormatRepo: projectMaskFormatDBMock,
				clientsRepo:           clientDBMock,
				cardsRepo:             cardDBMock,
				keyRepo:               keyDBMock,
			}

			res, err := s.GetCardTokens(context.Background(), tt.req)
			if tt.wantErr != nil {
				require.Error(t, err)
				require.ErrorContains(t, err, tt.wantErr.Error())
			} else {
				require.NoError(t, err)
			}

			require.Equal(t, tt.want, res)
		})
	}
}

func TestGetCardByEncryptedID(t *testing.T) {
	type getCardByIDOp struct {
		isCalled  bool
		input     uint64
		output    model.Card
		outputErr error
	}

	type getKeyByIDOp struct {
		isCalled  bool
		input     uint64
		output    model.Key
		outputErr error
	}

	type getClientByIDOp struct {
		isCalled  bool
		input     uint64
		output    *model.Client
		outputErr error
	}

	type getProjectMaskByProjectIDOp struct {
		isCalled  bool
		input     uint64
		output    *model.ProjectMaskFormat
		outputErr error
	}

	defaultWithSep := true

	tests := []struct {
		name                      string
		req                       schema.PanByHashedIdRequest
		want                      model.Card
		wantErr                   error
		getCardByID               getCardByIDOp
		getKeyByID                getKeyByIDOp
		getClientByID             getClientByIDOp
		getProjectMaskByProjectID getProjectMaskByProjectIDOp
		appConfig                 map[string]any
	}{
		{
			name: "aes_decrypt_error",
			req: schema.PanByHashedIdRequest{
				EncryptedKey: "AAAAAAAAAAAAAAAAAAAAAPsV0dc4K5DZT9Y+k0TFDpQ=",
			},
			wantErr: aes.KeySizeError(3),
			want:    model.Card{},
			appConfig: map[string]any{
				"SYMMETRIC_KEY": badKey,
			},
		},
		{
			name: "json_unmarshal_error",
			req: schema.PanByHashedIdRequest{
				EncryptedKey: "AAAAAAAAAAAAAAAAAAAAAPsV0dc4K5DZT9Y+k0TFDpQ=",
			},
			wantErr: &json.SyntaxError{},
			want:    model.Card{},
			appConfig: map[string]any{
				"SYMMETRIC_KEY": normalKey,
			},
		},
		{
			name: "get_card_by_id_error",
			req: schema.PanByHashedIdRequest{
				EncryptedKey: "AAAAAAAAAAAAAAAAAAAAAAa1Z7+ZP7uEJjDtmP8f3pAGMN5i3Mol8TluUKlVYuAz",
			},
			wantErr: errors.New("some error"),
			want:    model.Card{},
			getCardByID: getCardByIDOp{
				isCalled:  true,
				input:     1,
				output:    model.Card{},
				outputErr: errors.New("some error"),
			},
			appConfig: map[string]any{
				"SYMMETRIC_KEY": normalKey,
			},
		},
		{
			name: "get_key_by_id_error",
			req: schema.PanByHashedIdRequest{
				EncryptedKey: "AAAAAAAAAAAAAAAAAAAAAAa1Z7+ZP7uEJjDtmP8f3pAGMN5i3Mol8TluUKlVYuAz",
			},
			wantErr: errors.New("some error"),
			want:    model.Card{},
			getCardByID: getCardByIDOp{
				isCalled: true,
				input:    1,
				output: model.Card{
					Id:            1,
					ClientId:      1,
					IssuerId:      1,
					IpsId:         1,
					CountryId:     1,
					SaveAccess:    true,
					Approved:      false,
					KeyId:         1,
					EncryptMonth:  "AAAAAAAAAAAAAAAAAAAAAM45CA6vQU2zcQJ3wyF7cpc=",
					EncryptYear:   "AAAAAAAAAAAAAAAAAAAAAM45CA6vQU2zcQJ3wyF7cpc=",
					EncryptName:   "AAAAAAAAAAAAAAAAAAAAAM45CA6vQU2zcQJ3wyF7cpc=",
					EncryptPan:    "AAAAAAAAAAAAAAAAAAAAACXJ7eT6CzNrPzsbyIRluEFgK65FdiS3n0szvcsdV2Tb",
					IsPanModified: true,
				},
				outputErr: nil,
			},
			getKeyByID: getKeyByIDOp{
				isCalled:  true,
				input:     1,
				output:    model.Key{},
				outputErr: errors.New("some error"),
			},
			appConfig: map[string]any{
				"SYMMETRIC_KEY": normalKey,
			},
		},
		{
			name: "decrypt_key_error",
			req: schema.PanByHashedIdRequest{
				EncryptedKey: "AAAAAAAAAAAAAAAAAAAAAAa1Z7+ZP7uEJjDtmP8f3pAGMN5i3Mol8TluUKlVYuAz",
			},
			wantErr: errors.New("invalid data length"),
			want:    model.Card{},
			getCardByID: getCardByIDOp{
				isCalled: true,
				input:    1,
				output: model.Card{
					Id:            1,
					ClientId:      1,
					IssuerId:      1,
					IpsId:         1,
					CountryId:     1,
					SaveAccess:    true,
					Approved:      false,
					KeyId:         1,
					EncryptMonth:  "AAAAAAAAAAAAAAAAAAAAAM45CA6vQU2zcQJ3wyF7cpc=",
					EncryptYear:   "AAAAAAAAAAAAAAAAAAAAAM45CA6vQU2zcQJ3wyF7cpc=",
					EncryptName:   "AAAAAAAAAAAAAAAAAAAAAM45CA6vQU2zcQJ3wyF7cpc=",
					EncryptPan:    "AAAAAAAAAAAAAAAAAAAAACXJ7eT6CzNrPzsbyIRluEFgK65FdiS3n0szvcsdV2Tb",
					IsPanModified: true,
				},
				outputErr: nil,
			},
			getKeyByID: getKeyByIDOp{
				isCalled: true,
				input:    1,
				output: model.Key{
					Id:  1,
					Key: "",
				},
				outputErr: nil,
			},
			appConfig: map[string]any{
				"SYMMETRIC_KEY": normalKey,
			},
		},
		{
			name: "decrypt_pan_error",
			req: schema.PanByHashedIdRequest{
				EncryptedKey: "AAAAAAAAAAAAAAAAAAAAAAa1Z7+ZP7uEJjDtmP8f3pAGMN5i3Mol8TluUKlVYuAz",
			},
			wantErr: errors.New("invalid data length"),
			want:    model.Card{},
			getCardByID: getCardByIDOp{
				isCalled: true,
				input:    1,
				output: model.Card{
					Id:            1,
					ClientId:      1,
					IssuerId:      1,
					IpsId:         1,
					CountryId:     1,
					SaveAccess:    true,
					Approved:      false,
					KeyId:         1,
					EncryptMonth:  "AAAAAAAAAAAAAAAAAAAAAM45CA6vQU2zcQJ3wyF7cpc=",
					EncryptYear:   "AAAAAAAAAAAAAAAAAAAAAM45CA6vQU2zcQJ3wyF7cpc=",
					EncryptName:   "AAAAAAAAAAAAAAAAAAAAAM45CA6vQU2zcQJ3wyF7cpc=",
					EncryptPan:    "",
					IsPanModified: true,
				},
				outputErr: nil,
			},
			getKeyByID: getKeyByIDOp{
				isCalled: true,
				input:    1,
				output: model.Key{
					Id:  1,
					Key: "AAAAAAAAAAAAAAAAAAAAAD4r1NYivkHsOHdiq9dCiJiz0HJtt7wrVFA8fx/DenGL",
				},
				outputErr: nil,
			},
			appConfig: map[string]any{
				"SYMMETRIC_KEY": normalKey,
			},
		},
		{
			name: "get_client_by_id_error",
			req: schema.PanByHashedIdRequest{
				EncryptedKey: "AAAAAAAAAAAAAAAAAAAAAAa1Z7+ZP7uEJjDtmP8f3pAGMN5i3Mol8TluUKlVYuAz",
			},
			wantErr: errors.New("some error"),
			want:    model.Card{},
			getCardByID: getCardByIDOp{
				isCalled: true,
				input:    1,
				output: model.Card{
					Id:            1,
					ClientId:      1,
					IssuerId:      1,
					IpsId:         1,
					CountryId:     1,
					SaveAccess:    true,
					Approved:      false,
					KeyId:         1,
					EncryptMonth:  "AAAAAAAAAAAAAAAAAAAAAM45CA6vQU2zcQJ3wyF7cpc=",
					EncryptYear:   "AAAAAAAAAAAAAAAAAAAAAM45CA6vQU2zcQJ3wyF7cpc=",
					EncryptName:   "AAAAAAAAAAAAAAAAAAAAAM45CA6vQU2zcQJ3wyF7cpc=",
					EncryptPan:    "AAAAAAAAAAAAAAAAAAAAACXJ7eT6CzNrPzsbyIRluEFgK65FdiS3n0szvcsdV2Tb",
					IsPanModified: true,
				},
				outputErr: nil,
			},
			getKeyByID: getKeyByIDOp{
				isCalled: true,
				input:    1,
				output: model.Key{
					Id:  1,
					Key: "AAAAAAAAAAAAAAAAAAAAAD4r1NYivkHsOHdiq9dCiJiz0HJtt7wrVFA8fx/DenGL",
				},
				outputErr: nil,
			},
			getClientByID: getClientByIDOp{
				isCalled:  true,
				input:     1,
				output:    nil,
				outputErr: errors.New("some error"),
			},
			appConfig: map[string]any{
				"SYMMETRIC_KEY": normalKey,
			},
		},
		{
			name: "get_project_mask_error",
			req: schema.PanByHashedIdRequest{
				EncryptedKey: "AAAAAAAAAAAAAAAAAAAAAAa1Z7+ZP7uEJjDtmP8f3pAGMN5i3Mol8TluUKlVYuAz",
			},
			wantErr: errors.New("some error"),
			want:    model.Card{},
			getCardByID: getCardByIDOp{
				isCalled: true,
				input:    1,
				output: model.Card{
					Id:            1,
					ClientId:      1,
					IssuerId:      1,
					IpsId:         1,
					CountryId:     1,
					SaveAccess:    true,
					Approved:      false,
					KeyId:         1,
					EncryptMonth:  "AAAAAAAAAAAAAAAAAAAAAM45CA6vQU2zcQJ3wyF7cpc=",
					EncryptYear:   "AAAAAAAAAAAAAAAAAAAAAM45CA6vQU2zcQJ3wyF7cpc=",
					EncryptName:   "AAAAAAAAAAAAAAAAAAAAAM45CA6vQU2zcQJ3wyF7cpc=",
					EncryptPan:    "AAAAAAAAAAAAAAAAAAAAACXJ7eT6CzNrPzsbyIRluEFgK65FdiS3n0szvcsdV2Tb",
					IsPanModified: true,
				},
				outputErr: nil,
			},
			getKeyByID: getKeyByIDOp{
				isCalled: true,
				input:    1,
				output: model.Key{
					Id:  1,
					Key: "AAAAAAAAAAAAAAAAAAAAAD4r1NYivkHsOHdiq9dCiJiz0HJtt7wrVFA8fx/DenGL",
				},
				outputErr: nil,
			},
			getClientByID: getClientByIDOp{
				isCalled: true,
				input:    1,
				output: &model.Client{
					Id:                 1,
					ProjectClientId:    "asd",
					ProjectId:          1,
					VerificationUserID: 1,
				},
				outputErr: nil,
			},
			getProjectMaskByProjectID: getProjectMaskByProjectIDOp{
				isCalled:  true,
				input:     1,
				output:    nil,
				outputErr: errors.New("some error"),
			},
			appConfig: map[string]any{
				"SYMMETRIC_KEY": normalKey,
			},
		},
		{
			name: "get_masked_card_error",
			req: schema.PanByHashedIdRequest{
				EncryptedKey: "AAAAAAAAAAAAAAAAAAAAAAa1Z7+ZP7uEJjDtmP8f3pAGMN5i3Mol8TluUKlVYuAz",
			},
			wantErr: goerr.ErrPanLength,
			want:    model.Card{},
			getCardByID: getCardByIDOp{
				isCalled: true,
				input:    1,
				output: model.Card{
					Id:            1,
					ClientId:      1,
					IssuerId:      1,
					IpsId:         1,
					CountryId:     1,
					SaveAccess:    true,
					Approved:      false,
					KeyId:         1,
					EncryptMonth:  "AAAAAAAAAAAAAAAAAAAAAM45CA6vQU2zcQJ3wyF7cpc=",
					EncryptYear:   "AAAAAAAAAAAAAAAAAAAAAM45CA6vQU2zcQJ3wyF7cpc=",
					EncryptName:   "AAAAAAAAAAAAAAAAAAAAAM45CA6vQU2zcQJ3wyF7cpc=",
					EncryptPan:    "AAAAAAAAAAAAAAAAAAAAAPsV0dc4K5DZT9Y+k0TFDpQ=",
					Pan:           "something",
					IsPanModified: true,
				},
				outputErr: nil,
			},
			getKeyByID: getKeyByIDOp{
				isCalled: true,
				input:    1,
				output: model.Key{
					Id:  1,
					Key: "AAAAAAAAAAAAAAAAAAAAAD4r1NYivkHsOHdiq9dCiJiz0HJtt7wrVFA8fx/DenGL",
				},
				outputErr: nil,
			},
			getClientByID: getClientByIDOp{
				isCalled: true,
				input:    1,
				output: &model.Client{
					Id:                 1,
					ProjectClientId:    "asd",
					ProjectId:          1,
					VerificationUserID: 1,
				},
				outputErr: nil,
			},
			getProjectMaskByProjectID: getProjectMaskByProjectIDOp{
				isCalled: true,
				input:    1,
				output: &model.ProjectMaskFormat{
					Id:              1,
					ProjectId:       1,
					PlaceholderSign: "*",
					WithSeparator:   &defaultWithSep,
				},
				outputErr: nil,
			},
			appConfig: map[string]any{
				"SYMMETRIC_KEY": normalKey,
			},
		},
		{
			name: "success",
			req: schema.PanByHashedIdRequest{
				EncryptedKey: "AAAAAAAAAAAAAAAAAAAAAAa1Z7+ZP7uEJjDtmP8f3pAGMN5i3Mol8TluUKlVYuAz",
			},
			wantErr: nil,
			want: model.Card{
				Id:            1,
				ClientId:      1,
				IssuerId:      1,
				IpsId:         1,
				CountryId:     1,
				SaveAccess:    true,
				Tokens:        nil,
				Approved:      false,
				KeyId:         1,
				HashedPan:     "",
				EncryptMonth:  "AAAAAAAAAAAAAAAAAAAAAM45CA6vQU2zcQJ3wyF7cpc=",
				EncryptYear:   "AAAAAAAAAAAAAAAAAAAAAM45CA6vQU2zcQJ3wyF7cpc=",
				EncryptName:   "AAAAAAAAAAAAAAAAAAAAAM45CA6vQU2zcQJ3wyF7cpc=",
				EncryptPan:    "AAAAAAAAAAAAAAAAAAAAACXJ7eT6CzNrPzsbyIRluEFgK65FdiS3n0szvcsdV2Tb",
				MaskedPan:     "4342-07******-3288",
				Pan:           "****************",
				IsPanModified: true,
			},
			getCardByID: getCardByIDOp{
				isCalled: true,
				input:    1,
				output: model.Card{
					Id:            1,
					ClientId:      1,
					IssuerId:      1,
					IpsId:         1,
					CountryId:     1,
					SaveAccess:    true,
					Approved:      false,
					KeyId:         1,
					EncryptMonth:  "AAAAAAAAAAAAAAAAAAAAAM45CA6vQU2zcQJ3wyF7cpc=",
					EncryptYear:   "AAAAAAAAAAAAAAAAAAAAAM45CA6vQU2zcQJ3wyF7cpc=",
					EncryptName:   "AAAAAAAAAAAAAAAAAAAAAM45CA6vQU2zcQJ3wyF7cpc=",
					EncryptPan:    "AAAAAAAAAAAAAAAAAAAAACXJ7eT6CzNrPzsbyIRluEFgK65FdiS3n0szvcsdV2Tb",
					Pan:           "something",
					IsPanModified: true,
				},
				outputErr: nil,
			},
			getKeyByID: getKeyByIDOp{
				isCalled: true,
				input:    1,
				output: model.Key{
					Id:  1,
					Key: "AAAAAAAAAAAAAAAAAAAAAD4r1NYivkHsOHdiq9dCiJiz0HJtt7wrVFA8fx/DenGL",
				},
				outputErr: nil,
			},
			getClientByID: getClientByIDOp{
				isCalled: true,
				input:    1,
				output: &model.Client{
					Id:                 1,
					ProjectClientId:    "asd",
					ProjectId:          1,
					VerificationUserID: 1,
				},
				outputErr: nil,
			},
			getProjectMaskByProjectID: getProjectMaskByProjectIDOp{
				isCalled: true,
				input:    1,
				output: &model.ProjectMaskFormat{
					Id:              1,
					ProjectId:       1,
					PlaceholderSign: "*",
					WithSeparator:   &defaultWithSep,
				},
				outputErr: nil,
			},
			appConfig: map[string]any{
				"SYMMETRIC_KEY": normalKey,
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctrl := gomock.NewController(t)

			ctx, cancel := context.WithCancel(context.Background())
			e2e.InitAppE2E(t, ctx, cancel, nil, nil, tt.appConfig)

			cardsDBMock := databasemocks.NewMockCarder(ctrl)
			keyDBMock := databasemocks.NewMockKeyer(ctrl)
			clientDBMock := databasemocks.NewMockClientor(ctrl)
			projectMaskDBMock := databasemocks.NewMockProjectMaskFormatter(ctrl)

			if tt.getCardByID.isCalled {
				cardsDBMock.EXPECT().GetByID(
					gomock.Any(),
					tt.getCardByID.input,
				).Return(
					tt.getCardByID.output,
					tt.getCardByID.outputErr,
				)
			}

			if tt.getKeyByID.isCalled {
				keyDBMock.EXPECT().GetById(
					gomock.Any(),
					tt.getKeyByID.input,
				).Return(
					tt.getKeyByID.output,
					tt.getKeyByID.outputErr,
				)
			}

			if tt.getClientByID.isCalled {
				clientDBMock.EXPECT().GetByID(
					gomock.Any(),
					tt.getClientByID.input,
				).Return(
					tt.getClientByID.output,
					tt.getClientByID.outputErr,
				)
			}

			if tt.getProjectMaskByProjectID.isCalled {
				projectMaskDBMock.EXPECT().GetByProjectID(
					gomock.Any(),
					tt.getProjectMaskByProjectID.input,
				).Return(
					tt.getProjectMaskByProjectID.output,
					tt.getProjectMaskByProjectID.outputErr,
				)
			}

			s := CardGetterService{
				projectMaskFormatRepo: projectMaskDBMock,
				clientsRepo:           clientDBMock,
				cardsRepo:             cardsDBMock,
				keyRepo:               keyDBMock,
			}

			res, err := s.GetByEncryptedId(context.Background(), tt.req)
			if tt.wantErr != nil {
				require.Error(t, err)
				require.ErrorContains(t, err, tt.wantErr.Error())
			} else {
				require.NoError(t, err)
			}

			require.Equal(t, tt.want, res)
		})
	}
}

func TestGetCardByID(t *testing.T) {
	type getCardByIDOp struct {
		input     uint64
		output    model.Card
		outputErr error
	}

	type getKeyByIDOp struct {
		isCalled  bool
		input     uint64
		output    model.Key
		outputErr error
	}

	type getClientByIDOp struct {
		isCalled  bool
		input     uint64
		output    *model.Client
		outputErr error
	}

	type getProjectMaskByProjectIDOp struct {
		isCalled  bool
		input     uint64
		output    *model.ProjectMaskFormat
		outputErr error
	}

	defaultWithSep := true

	tests := []struct {
		name                      string
		req                       schema.PanByCardIdRequest
		want                      model.Card
		wantErr                   error
		appConfig                 map[string]any
		getCardByID               getCardByIDOp
		getKeyByID                getKeyByIDOp
		getClientByID             getClientByIDOp
		getProjectMaskByProjectID getProjectMaskByProjectIDOp
	}{
		{
			name: "error_getting_card_by_id",
			req: schema.PanByCardIdRequest{
				CardId: 1,
			},
			want:    model.Card{},
			wantErr: errors.New("some error"),
			getCardByID: getCardByIDOp{
				input:     1,
				output:    model.Card{},
				outputErr: errors.New("some error"),
			},
		},
		{
			name: "error_getting_key_by_id",
			req: schema.PanByCardIdRequest{
				CardId: 1,
			},
			want:    model.Card{},
			wantErr: errors.New("some error"),
			getCardByID: getCardByIDOp{
				input: 1,
				output: model.Card{
					Id:            1,
					ClientId:      1,
					IssuerId:      1,
					IpsId:         1,
					CountryId:     1,
					SaveAccess:    true,
					Approved:      false,
					KeyId:         1,
					EncryptMonth:  "AAAAAAAAAAAAAAAAAAAAAM45CA6vQU2zcQJ3wyF7cpc=",
					EncryptYear:   "AAAAAAAAAAAAAAAAAAAAAM45CA6vQU2zcQJ3wyF7cpc=",
					EncryptName:   "AAAAAAAAAAAAAAAAAAAAAM45CA6vQU2zcQJ3wyF7cpc=",
					EncryptPan:    "AAAAAAAAAAAAAAAAAAAAACXJ7eT6CzNrPzsbyIRluEFgK65FdiS3n0szvcsdV2Tb",
					Pan:           "something",
					IsPanModified: true,
				},
				outputErr: nil,
			},
			getKeyByID: getKeyByIDOp{
				isCalled:  true,
				input:     1,
				output:    model.Key{},
				outputErr: errors.New("some error"),
			},
		},
		{
			name: "decrypt_key_error",
			req: schema.PanByCardIdRequest{
				CardId: 1,
			},
			want:    model.Card{},
			wantErr: aes.KeySizeError(3),
			getCardByID: getCardByIDOp{
				input: 1,
				output: model.Card{
					Id:            1,
					ClientId:      1,
					IssuerId:      1,
					IpsId:         1,
					CountryId:     1,
					SaveAccess:    true,
					Approved:      false,
					KeyId:         1,
					EncryptMonth:  "AAAAAAAAAAAAAAAAAAAAAM45CA6vQU2zcQJ3wyF7cpc=",
					EncryptYear:   "AAAAAAAAAAAAAAAAAAAAAM45CA6vQU2zcQJ3wyF7cpc=",
					EncryptName:   "AAAAAAAAAAAAAAAAAAAAAM45CA6vQU2zcQJ3wyF7cpc=",
					EncryptPan:    "AAAAAAAAAAAAAAAAAAAAACXJ7eT6CzNrPzsbyIRluEFgK65FdiS3n0szvcsdV2Tb",
					Pan:           "something",
					IsPanModified: true,
				},
				outputErr: nil,
			},
			getKeyByID: getKeyByIDOp{
				isCalled: true,
				input:    1,
				output: model.Key{
					Id:  1,
					Key: "AAAAAAAAAAAAAAAAAAAAAD4r1NYivkHsOHdiq9dCiJiz0HJtt7wrVFA8fx/DenGL",
				},
				outputErr: nil,
			},
			appConfig: map[string]any{
				"SYMMETRIC_KEY": badKey,
			},
		},
		{
			name: "decrypt_pan_error",
			req: schema.PanByCardIdRequest{
				CardId: 1,
			},
			want:    model.Card{},
			wantErr: errors.New("invalid data length"),
			getCardByID: getCardByIDOp{
				input: 1,
				output: model.Card{
					Id:            1,
					ClientId:      1,
					IssuerId:      1,
					IpsId:         1,
					CountryId:     1,
					SaveAccess:    true,
					Approved:      false,
					KeyId:         1,
					EncryptMonth:  "AAAAAAAAAAAAAAAAAAAAAM45CA6vQU2zcQJ3wyF7cpc=",
					EncryptYear:   "AAAAAAAAAAAAAAAAAAAAAM45CA6vQU2zcQJ3wyF7cpc=",
					EncryptName:   "AAAAAAAAAAAAAAAAAAAAAM45CA6vQU2zcQJ3wyF7cpc=",
					EncryptPan:    "",
					Pan:           "something",
					IsPanModified: true,
				},
				outputErr: nil,
			},
			getKeyByID: getKeyByIDOp{
				isCalled: true,
				input:    1,
				output: model.Key{
					Id:  1,
					Key: "AAAAAAAAAAAAAAAAAAAAAD4r1NYivkHsOHdiq9dCiJiz0HJtt7wrVFA8fx/DenGL",
				},
				outputErr: nil,
			},
			appConfig: map[string]any{
				"SYMMETRIC_KEY": normalKey,
			},
		},
		{
			name: "error_getting_client_by_id",
			req: schema.PanByCardIdRequest{
				CardId: 1,
			},
			want:    model.Card{},
			wantErr: errors.New("some error"),
			getCardByID: getCardByIDOp{
				input: 1,
				output: model.Card{
					Id:            1,
					ClientId:      1,
					IssuerId:      1,
					IpsId:         1,
					CountryId:     1,
					SaveAccess:    true,
					Approved:      false,
					KeyId:         1,
					EncryptMonth:  "AAAAAAAAAAAAAAAAAAAAAM45CA6vQU2zcQJ3wyF7cpc=",
					EncryptYear:   "AAAAAAAAAAAAAAAAAAAAAM45CA6vQU2zcQJ3wyF7cpc=",
					EncryptName:   "AAAAAAAAAAAAAAAAAAAAAM45CA6vQU2zcQJ3wyF7cpc=",
					EncryptPan:    "AAAAAAAAAAAAAAAAAAAAACXJ7eT6CzNrPzsbyIRluEFgK65FdiS3n0szvcsdV2Tb",
					Pan:           "something",
					IsPanModified: true,
				},
				outputErr: nil,
			},
			getKeyByID: getKeyByIDOp{
				isCalled: true,
				input:    1,
				output: model.Key{
					Id:  1,
					Key: "AAAAAAAAAAAAAAAAAAAAAD4r1NYivkHsOHdiq9dCiJiz0HJtt7wrVFA8fx/DenGL",
				},
				outputErr: nil,
			},
			getClientByID: getClientByIDOp{
				isCalled:  true,
				input:     1,
				output:    nil,
				outputErr: errors.New("some error"),
			},
			appConfig: map[string]any{
				"SYMMETRIC_KEY": normalKey,
			},
		},
		{
			name: "error_getting_project_mask",
			req: schema.PanByCardIdRequest{
				CardId: 1,
			},
			want:    model.Card{},
			wantErr: errors.New("some error"),
			getCardByID: getCardByIDOp{
				input: 1,
				output: model.Card{
					Id:            1,
					ClientId:      1,
					IssuerId:      1,
					IpsId:         1,
					CountryId:     1,
					SaveAccess:    true,
					Approved:      false,
					KeyId:         1,
					EncryptMonth:  "AAAAAAAAAAAAAAAAAAAAAM45CA6vQU2zcQJ3wyF7cpc=",
					EncryptYear:   "AAAAAAAAAAAAAAAAAAAAAM45CA6vQU2zcQJ3wyF7cpc=",
					EncryptName:   "AAAAAAAAAAAAAAAAAAAAAM45CA6vQU2zcQJ3wyF7cpc=",
					EncryptPan:    "AAAAAAAAAAAAAAAAAAAAACXJ7eT6CzNrPzsbyIRluEFgK65FdiS3n0szvcsdV2Tb",
					Pan:           "something",
					IsPanModified: true,
				},
				outputErr: nil,
			},
			getKeyByID: getKeyByIDOp{
				isCalled: true,
				input:    1,
				output: model.Key{
					Id:  1,
					Key: "AAAAAAAAAAAAAAAAAAAAAD4r1NYivkHsOHdiq9dCiJiz0HJtt7wrVFA8fx/DenGL",
				},
				outputErr: nil,
			},
			getClientByID: getClientByIDOp{
				isCalled: true,
				input:    1,
				output: &model.Client{
					Id:              1,
					ProjectClientId: "asd",
					ProjectId:       1,
				},
				outputErr: nil,
			},
			getProjectMaskByProjectID: getProjectMaskByProjectIDOp{
				isCalled:  true,
				input:     1,
				outputErr: errors.New("some error"),
				output:    nil,
			},
			appConfig: map[string]any{
				"SYMMETRIC_KEY": normalKey,
			},
		},
		{
			name: "error_getting_masked_card",
			req: schema.PanByCardIdRequest{
				CardId: 1,
			},
			want:    model.Card{},
			wantErr: goerr.ErrPanLength,
			getCardByID: getCardByIDOp{
				input: 1,
				output: model.Card{
					Id:            1,
					ClientId:      1,
					IssuerId:      1,
					IpsId:         1,
					CountryId:     1,
					SaveAccess:    true,
					Approved:      false,
					KeyId:         1,
					EncryptMonth:  "AAAAAAAAAAAAAAAAAAAAAM45CA6vQU2zcQJ3wyF7cpc=",
					EncryptYear:   "AAAAAAAAAAAAAAAAAAAAAM45CA6vQU2zcQJ3wyF7cpc=",
					EncryptName:   "AAAAAAAAAAAAAAAAAAAAAM45CA6vQU2zcQJ3wyF7cpc=",
					EncryptPan:    "AAAAAAAAAAAAAAAAAAAAAPsV0dc4K5DZT9Y+k0TFDpQ=",
					Pan:           "something",
					IsPanModified: true,
				},
				outputErr: nil,
			},
			getKeyByID: getKeyByIDOp{
				isCalled: true,
				input:    1,
				output: model.Key{
					Id:  1,
					Key: "AAAAAAAAAAAAAAAAAAAAAD4r1NYivkHsOHdiq9dCiJiz0HJtt7wrVFA8fx/DenGL",
				},
				outputErr: nil,
			},
			getClientByID: getClientByIDOp{
				isCalled: true,
				input:    1,
				output: &model.Client{
					Id:              1,
					ProjectClientId: "asd",
					ProjectId:       1,
				},
				outputErr: nil,
			},
			getProjectMaskByProjectID: getProjectMaskByProjectIDOp{
				isCalled:  true,
				input:     1,
				outputErr: nil,
				output: &model.ProjectMaskFormat{
					Id:              1,
					ProjectId:       1,
					PlaceholderSign: "*",
					WithSeparator:   &defaultWithSep,
				},
			},
			appConfig: map[string]any{
				"SYMMETRIC_KEY": normalKey,
			},
		},
		{
			name: "success",
			req: schema.PanByCardIdRequest{
				CardId: 1,
			},
			want: model.Card{
				Id:            1,
				ClientId:      1,
				IssuerId:      1,
				IpsId:         1,
				CountryId:     1,
				SaveAccess:    true,
				Tokens:        nil,
				Approved:      false,
				KeyId:         1,
				HashedPan:     "",
				EncryptMonth:  "AAAAAAAAAAAAAAAAAAAAAM45CA6vQU2zcQJ3wyF7cpc=",
				EncryptYear:   "AAAAAAAAAAAAAAAAAAAAAM45CA6vQU2zcQJ3wyF7cpc=",
				EncryptName:   "AAAAAAAAAAAAAAAAAAAAAM45CA6vQU2zcQJ3wyF7cpc=",
				EncryptPan:    "AAAAAAAAAAAAAAAAAAAAACXJ7eT6CzNrPzsbyIRluEFgK65FdiS3n0szvcsdV2Tb",
				MaskedPan:     "4342-07******-3288",
				Pan:           "****************",
				IsPanModified: true,
			},
			wantErr: nil,
			getCardByID: getCardByIDOp{
				input: 1,
				output: model.Card{
					Id:            1,
					ClientId:      1,
					IssuerId:      1,
					IpsId:         1,
					CountryId:     1,
					SaveAccess:    true,
					Approved:      false,
					KeyId:         1,
					EncryptMonth:  "AAAAAAAAAAAAAAAAAAAAAM45CA6vQU2zcQJ3wyF7cpc=",
					EncryptYear:   "AAAAAAAAAAAAAAAAAAAAAM45CA6vQU2zcQJ3wyF7cpc=",
					EncryptName:   "AAAAAAAAAAAAAAAAAAAAAM45CA6vQU2zcQJ3wyF7cpc=",
					EncryptPan:    "AAAAAAAAAAAAAAAAAAAAACXJ7eT6CzNrPzsbyIRluEFgK65FdiS3n0szvcsdV2Tb",
					Pan:           "something",
					IsPanModified: true,
				},
				outputErr: nil,
			},
			getKeyByID: getKeyByIDOp{
				isCalled: true,
				input:    1,
				output: model.Key{
					Id:  1,
					Key: "AAAAAAAAAAAAAAAAAAAAAD4r1NYivkHsOHdiq9dCiJiz0HJtt7wrVFA8fx/DenGL",
				},
				outputErr: nil,
			},
			getClientByID: getClientByIDOp{
				isCalled: true,
				input:    1,
				output: &model.Client{
					Id:              1,
					ProjectClientId: "asd",
					ProjectId:       1,
				},
				outputErr: nil,
			},
			getProjectMaskByProjectID: getProjectMaskByProjectIDOp{
				isCalled:  true,
				input:     1,
				outputErr: nil,
				output: &model.ProjectMaskFormat{
					Id:              1,
					ProjectId:       1,
					PlaceholderSign: "*",
					WithSeparator:   &defaultWithSep,
				},
			},
			appConfig: map[string]any{
				"SYMMETRIC_KEY": normalKey,
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctrl := gomock.NewController(t)

			ctx, cancel := context.WithCancel(context.Background())
			e2e.InitAppE2E(t, ctx, cancel, nil, nil, tt.appConfig)

			cardsDBMock := databasemocks.NewMockCarder(ctrl)
			keyDBMock := databasemocks.NewMockKeyer(ctrl)
			clientDBMock := databasemocks.NewMockClientor(ctrl)
			projectMaskFormatDBMock := databasemocks.NewMockProjectMaskFormatter(ctrl)

			cardsDBMock.EXPECT().GetByID(
				gomock.Any(),
				tt.getCardByID.input,
			).Return(
				tt.getCardByID.output,
				tt.getCardByID.outputErr,
			)

			if tt.getKeyByID.isCalled {
				keyDBMock.EXPECT().GetById(
					gomock.Any(),
					tt.getKeyByID.input,
				).Return(
					tt.getKeyByID.output,
					tt.getKeyByID.outputErr,
				)
			}

			if tt.getClientByID.isCalled {
				clientDBMock.EXPECT().GetByID(
					gomock.Any(),
					tt.getClientByID.input,
				).Return(
					tt.getClientByID.output,
					tt.getClientByID.outputErr,
				)
			}

			if tt.getProjectMaskByProjectID.isCalled {
				projectMaskFormatDBMock.EXPECT().GetByProjectID(
					gomock.Any(),
					tt.getProjectMaskByProjectID.input,
				).Return(
					tt.getProjectMaskByProjectID.output,
					tt.getProjectMaskByProjectID.outputErr,
				)
			}

			s := CardGetterService{
				projectMaskFormatRepo: projectMaskFormatDBMock,
				clientsRepo:           clientDBMock,
				cardsRepo:             cardsDBMock,
				keyRepo:               keyDBMock,
			}

			res, err := s.GetById(context.Background(), tt.req)
			if tt.wantErr != nil {
				require.Error(t, err)
				require.ErrorContains(t, err, tt.wantErr.Error())
			} else {
				require.NoError(t, err)
			}
			require.Equal(t, tt.want, res)
		})
	}
}

func TestGetCardByPan(t *testing.T) {
	type getClientCardsOp struct {
		inputProjectClientID string
		inputProjectID       uint64
		output               model.Cards
		outputErr            error
	}

	type decryptOp struct {
		input     encryptor.EncryptedCard
		output    encryptor.DecryptedCard
		outputErr error
	}

	tests := []struct {
		name                string
		reqProjectID        uint64
		reqProjectDClientID string
		reqPan              []byte
		want                *model.Card
		wantErr             error
		decrypt             decryptOp
		getClientCards      getClientCardsOp
	}{
		{
			name:                "error",
			reqProjectID:        1,
			reqProjectDClientID: "asd",
			reqPan:              []byte("something"),
			want:                nil,
			wantErr:             errors.New("some error"),
			getClientCards: getClientCardsOp{
				inputProjectClientID: "asd",
				inputProjectID:       1,
				output:               nil,
				outputErr:            errors.New("some error"),
			},
		},
		{
			name:                "success",
			reqProjectID:        1,
			reqProjectDClientID: "asd",
			reqPan:              []byte("some pan"),
			want: &model.Card{
				Id:          1,
				MaskedPan:   "some pan",
				Pan:         "some pan",
				EncryptName: "some name",
				HashedPan:   "$2a$10$4q4g2vSqVd/9Q3ZpIlngheW01oTebFnMgqU.NydDm.OoGgjRFQKT.",
			},
			wantErr: nil,
			getClientCards: getClientCardsOp{
				inputProjectClientID: "asd",
				inputProjectID:       1,
				output: model.Cards{
					{
						Id:          1,
						MaskedPan:   "some pan",
						Pan:         "some pan",
						EncryptName: "some name",
						HashedPan:   "$2a$10$4q4g2vSqVd/9Q3ZpIlngheW01oTebFnMgqU.NydDm.OoGgjRFQKT.",
					},
				},
				outputErr: nil,
			},
			decrypt: decryptOp{
				input: encryptor.EncryptedCard{
					Number: []byte("some pan"),
				},
				output: encryptor.DecryptedCard{
					Number: "some pan",
				},
				outputErr: nil,
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctrl := gomock.NewController(t)

			cardsDBMock := databasemocks.NewMockCarder(ctrl)
			decryptorMock := encryptor.NewMockDecryptor(ctrl)

			cardsDBMock.EXPECT().GetClientCards(
				gomock.Any(),
				&middlewares.PaginationInfo{},
				tt.getClientCards.inputProjectClientID,
				tt.getClientCards.inputProjectID,
			).Return(
				tt.getClientCards.output,
				tt.getClientCards.outputErr,
			)

			decryptorMock.EXPECT().DecryptCard(tt.decrypt.input).
				Return(tt.decrypt.output, tt.decrypt.outputErr).AnyTimes()

			s := CardGetterService{
				cardsRepo: cardsDBMock,
				decryptor: decryptorMock,
			}

			res, err := s.GetCardByPan(context.Background(), tt.reqProjectID, tt.reqProjectDClientID, tt.reqPan)
			if tt.wantErr != nil {
				require.Error(t, err)
				require.ErrorContains(t, err, tt.wantErr.Error())
			} else {
				require.NoError(t, err)
			}

			require.Equal(t, tt.want, res)
		})
	}
}

func TestGetCardToken(t *testing.T) {
	type getCardByIDOp struct {
		input     uint64
		output    model.Card
		outputErr error
	}

	type getKeyByIDOp struct {
		isCalled  bool
		input     uint64
		output    model.Key
		outputErr error
	}

	type getClientByIDOp struct {
		isCalled  bool
		input     uint64
		output    *model.Client
		outputErr error
	}

	type getProjectMaskFormatByProjectIDOp struct {
		isCalled  bool
		input     uint64
		output    *model.ProjectMaskFormat
		outputErr error
	}

	type getIpsByAcquirerIDOp struct {
		isCalled  bool
		input     *grpc.GetIpsByIDRequestV1
		output    *grpc.GetIpsByIDResponseV1
		outputErr error
	}

	type getIssuerByAcquirerIDOp struct {
		isCalled  bool
		input     *grpc.GetIssuerByIDRequestV1
		output    *grpc.GetIssuerByIDResponseV1
		outputErr error
	}

	defaultWithSep := true

	tests := []struct {
		name                            string
		req                             uint64
		want                            schema.EncryptedCardToken
		wantErr                         error
		getCardByID                     getCardByIDOp
		getKeyByID                      getKeyByIDOp
		getClientByID                   getClientByIDOp
		getProjectMaskFormatByProjectID getProjectMaskFormatByProjectIDOp
		getIpsByAcquirerID              getIpsByAcquirerIDOp
		getIssuerByAcquirerID           getIssuerByAcquirerIDOp
		appConfig                       map[string]any
	}{
		{
			name:    "error_getting_card_by_Id",
			req:     1,
			want:    schema.EncryptedCardToken{},
			wantErr: errors.New("some error"),
			getCardByID: getCardByIDOp{
				input:     1,
				outputErr: errors.New("some error"),
				output:    model.Card{},
			},
			appConfig: map[string]any{
				"SYMMETRIC_KEY": badKey,
			},
		},
		{
			name:    "error_getting_key_by_id",
			req:     1,
			want:    schema.EncryptedCardToken{},
			wantErr: errors.New("some error"),
			getCardByID: getCardByIDOp{
				input:     1,
				outputErr: nil,
				output: model.Card{
					Id:            1,
					ClientId:      1,
					IssuerId:      1,
					IpsId:         1,
					CountryId:     1,
					SaveAccess:    true,
					Approved:      false,
					KeyId:         1,
					EncryptMonth:  "AAAAAAAAAAAAAAAAAAAAAM45CA6vQU2zcQJ3wyF7cpc=",
					EncryptYear:   "AAAAAAAAAAAAAAAAAAAAAM45CA6vQU2zcQJ3wyF7cpc=",
					EncryptName:   "AAAAAAAAAAAAAAAAAAAAAM45CA6vQU2zcQJ3wyF7cpc=",
					EncryptPan:    "AAAAAAAAAAAAAAAAAAAAACXJ7eT6CzNrPzsbyIRluEFgK65FdiS3n0szvcsdV2Tb",
					Pan:           "something",
					IsPanModified: true,
				},
			},
			getKeyByID: getKeyByIDOp{
				isCalled:  true,
				input:     1,
				output:    model.Key{},
				outputErr: errors.New("some error"),
			},
			appConfig: map[string]any{
				"SYMMETRIC_KEY": badKey,
			},
		},
		{
			name:    "key_decryption_error",
			req:     1,
			want:    schema.EncryptedCardToken{},
			wantErr: aes.KeySizeError(3),
			getCardByID: getCardByIDOp{
				input:     1,
				outputErr: nil,
				output: model.Card{
					Id:            1,
					ClientId:      1,
					IssuerId:      1,
					IpsId:         1,
					CountryId:     1,
					SaveAccess:    true,
					Approved:      false,
					KeyId:         1,
					EncryptMonth:  "AAAAAAAAAAAAAAAAAAAAAJWXVsNZooT1Nj5qlh9r4ws=",
					EncryptYear:   "AAAAAAAAAAAAAAAAAAAAAJWXVsNZooT1Nj5qlh9r4ws=",
					EncryptName:   "AAAAAAAAAAAAAAAAAAAAAJWXVsNZooT1Nj5qlh9r4ws=",
					EncryptPan:    "AAAAAAAAAAAAAAAAAAAAACXJ7eT6CzNrPzsbyIRluEFgK65FdiS3n0szvcsdV2Tb",
					Pan:           "something",
					IsPanModified: true,
				},
			},
			getKeyByID: getKeyByIDOp{
				isCalled: true,
				input:    1,
				output: model.Key{
					Id:  1,
					Key: "AAAAAAAAAAAAAAAAAAAAAD4r1NYivkHsOHdiq9dCiJiz0HJtt7wrVFA8fx/DenGL",
				},
				outputErr: nil,
			},
			appConfig: map[string]any{
				"SYMMETRIC_KEY": badKey,
			},
		},
		{
			name:    "pan_decryption_error",
			req:     1,
			want:    schema.EncryptedCardToken{},
			wantErr: errors.New("error while decrypting pan"),
			getCardByID: getCardByIDOp{
				input:     1,
				outputErr: nil,
				output: model.Card{
					Id:            1,
					ClientId:      1,
					IssuerId:      1,
					IpsId:         1,
					CountryId:     1,
					SaveAccess:    true,
					Approved:      false,
					KeyId:         1,
					EncryptMonth:  "AAAAAAAAAAAAAAAAAAAAAJWXVsNZooT1Nj5qlh9r4ws=",
					EncryptYear:   "AAAAAAAAAAAAAAAAAAAAAJWXVsNZooT1Nj5qlh9r4ws=",
					EncryptName:   "AAAAAAAAAAAAAAAAAAAAAJWXVsNZooT1Nj5qlh9r4ws=",
					EncryptPan:    "",
					Pan:           "something",
					IsPanModified: true,
				},
			},
			getKeyByID: getKeyByIDOp{
				isCalled: true,
				input:    1,
				output: model.Key{
					Id:  1,
					Key: "AAAAAAAAAAAAAAAAAAAAAD4r1NYivkHsOHdiq9dCiJiz0HJtt7wrVFA8fx/DenGL",
				},
				outputErr: nil,
			},
			appConfig: map[string]any{
				"SYMMETRIC_KEY": normalKey,
			},
		},
		{
			name:    "get_client_by_id_error",
			req:     1,
			want:    schema.EncryptedCardToken{},
			wantErr: errors.New("some error"),
			getCardByID: getCardByIDOp{
				input:     1,
				outputErr: nil,
				output: model.Card{
					Id:            1,
					ClientId:      1,
					IssuerId:      1,
					IpsId:         1,
					CountryId:     1,
					SaveAccess:    true,
					Approved:      false,
					KeyId:         1,
					EncryptMonth:  "AAAAAAAAAAAAAAAAAAAAAJWXVsNZooT1Nj5qlh9r4ws=",
					EncryptYear:   "AAAAAAAAAAAAAAAAAAAAAJWXVsNZooT1Nj5qlh9r4ws=",
					EncryptName:   "AAAAAAAAAAAAAAAAAAAAAJWXVsNZooT1Nj5qlh9r4ws=",
					EncryptPan:    "AAAAAAAAAAAAAAAAAAAAACXJ7eT6CzNrPzsbyIRluEFgK65FdiS3n0szvcsdV2Tb",
					Pan:           "something",
					IsPanModified: true,
				},
			},
			getKeyByID: getKeyByIDOp{
				isCalled: true,
				input:    1,
				output: model.Key{
					Id:  1,
					Key: "AAAAAAAAAAAAAAAAAAAAAD4r1NYivkHsOHdiq9dCiJiz0HJtt7wrVFA8fx/DenGL",
				},
				outputErr: nil,
			},
			getClientByID: getClientByIDOp{
				isCalled:  true,
				input:     1,
				output:    nil,
				outputErr: errors.New("some error"),
			},
			appConfig: map[string]any{
				"SYMMETRIC_KEY": normalKey,
			},
		},
		{
			name:    "get_masked_card_error",
			req:     1,
			want:    schema.EncryptedCardToken{},
			wantErr: goerr.ErrPanLength,
			getCardByID: getCardByIDOp{
				input:     1,
				outputErr: nil,
				output: model.Card{
					Id:            1,
					ClientId:      1,
					IssuerId:      1,
					IpsId:         1,
					CountryId:     1,
					SaveAccess:    true,
					Approved:      false,
					KeyId:         1,
					EncryptMonth:  "AAAAAAAAAAAAAAAAAAAAAJWXVsNZooT1Nj5qlh9r4ws=",
					EncryptYear:   "AAAAAAAAAAAAAAAAAAAAAJWXVsNZooT1Nj5qlh9r4ws=",
					EncryptName:   "AAAAAAAAAAAAAAAAAAAAAJWXVsNZooT1Nj5qlh9r4ws=",
					EncryptPan:    "AAAAAAAAAAAAAAAAAAAAAJWXVsNZooT1Nj5qlh9r4ws=",
					Pan:           "something",
					IsPanModified: true,
				},
			},
			getKeyByID: getKeyByIDOp{
				isCalled: true,
				input:    1,
				output: model.Key{
					Id:  1,
					Key: "AAAAAAAAAAAAAAAAAAAAAD4r1NYivkHsOHdiq9dCiJiz0HJtt7wrVFA8fx/DenGL",
				},
				outputErr: nil,
			},
			getClientByID: getClientByIDOp{
				isCalled: true,
				input:    1,
				output: &model.Client{
					Id:              1,
					ProjectClientId: "asd",
					ProjectId:       1,
				},
				outputErr: nil,
			},
			getProjectMaskFormatByProjectID: getProjectMaskFormatByProjectIDOp{
				isCalled: true,
				input:    1,
				output: &model.ProjectMaskFormat{
					Id:              1,
					ProjectId:       1,
					PlaceholderSign: "*",
					WithSeparator:   &defaultWithSep,
				},
				outputErr: nil,
			},
			appConfig: map[string]any{
				"SYMMETRIC_KEY": normalKey,
			},
		},
		{
			name:    "get_project_mask_error",
			req:     1,
			want:    schema.EncryptedCardToken{},
			wantErr: errors.New("some error"),
			getCardByID: getCardByIDOp{
				input:     1,
				outputErr: nil,
				output: model.Card{
					Id:            1,
					ClientId:      1,
					IssuerId:      1,
					IpsId:         1,
					CountryId:     1,
					SaveAccess:    true,
					Approved:      false,
					KeyId:         1,
					EncryptMonth:  "AAAAAAAAAAAAAAAAAAAAAJWXVsNZooT1Nj5qlh9r4ws=",
					EncryptYear:   "AAAAAAAAAAAAAAAAAAAAAJWXVsNZooT1Nj5qlh9r4ws=",
					EncryptName:   "AAAAAAAAAAAAAAAAAAAAAJWXVsNZooT1Nj5qlh9r4ws=",
					EncryptPan:    "AAAAAAAAAAAAAAAAAAAAACXJ7eT6CzNrPzsbyIRluEFgK65FdiS3n0szvcsdV2Tb",
					Pan:           "something",
					IsPanModified: true,
				},
			},
			getKeyByID: getKeyByIDOp{
				isCalled: true,
				input:    1,
				output: model.Key{
					Id:  1,
					Key: "AAAAAAAAAAAAAAAAAAAAAD4r1NYivkHsOHdiq9dCiJiz0HJtt7wrVFA8fx/DenGL",
				},
				outputErr: nil,
			},
			getClientByID: getClientByIDOp{
				isCalled: true,
				input:    1,
				output: &model.Client{
					Id:              1,
					ProjectClientId: "asd",
					ProjectId:       1,
				},
				outputErr: nil,
			},
			getProjectMaskFormatByProjectID: getProjectMaskFormatByProjectIDOp{
				isCalled:  true,
				input:     1,
				output:    nil,
				outputErr: errors.New("some error"),
			},
			appConfig: map[string]any{
				"SYMMETRIC_KEY": normalKey,
			},
		},
		{
			name:    "get_ips_error",
			req:     1,
			want:    schema.EncryptedCardToken{},
			wantErr: errors.New("some error"),
			getCardByID: getCardByIDOp{
				input:     1,
				outputErr: nil,
				output: model.Card{
					Id:            1,
					ClientId:      1,
					IssuerId:      1,
					IpsId:         1,
					CountryId:     1,
					SaveAccess:    true,
					Approved:      false,
					KeyId:         1,
					EncryptMonth:  "AAAAAAAAAAAAAAAAAAAAAJWXVsNZooT1Nj5qlh9r4ws=",
					EncryptYear:   "AAAAAAAAAAAAAAAAAAAAAJWXVsNZooT1Nj5qlh9r4ws=",
					EncryptName:   "AAAAAAAAAAAAAAAAAAAAAJWXVsNZooT1Nj5qlh9r4ws=",
					EncryptPan:    "AAAAAAAAAAAAAAAAAAAAACXJ7eT6CzNrPzsbyIRluEFgK65FdiS3n0szvcsdV2Tb",
					Pan:           "something",
					IsPanModified: true,
				},
			},
			getKeyByID: getKeyByIDOp{
				isCalled: true,
				input:    1,
				output: model.Key{
					Id:  1,
					Key: "AAAAAAAAAAAAAAAAAAAAAD4r1NYivkHsOHdiq9dCiJiz0HJtt7wrVFA8fx/DenGL",
				},
				outputErr: nil,
			},
			getClientByID: getClientByIDOp{
				isCalled: true,
				input:    1,
				output: &model.Client{
					Id:              1,
					ProjectClientId: "asd",
					ProjectId:       1,
				},
				outputErr: nil,
			},
			getProjectMaskFormatByProjectID: getProjectMaskFormatByProjectIDOp{
				isCalled: true,
				input:    1,
				output: &model.ProjectMaskFormat{
					Id:              1,
					ProjectId:       1,
					PlaceholderSign: "*",
					WithSeparator:   &defaultWithSep,
				},
				outputErr: nil,
			},
			getIpsByAcquirerID: getIpsByAcquirerIDOp{
				isCalled: true,
				input: &grpc.GetIpsByIDRequestV1{
					IpsId: testsdk.Ptr(uint64(1)),
				},
				outputErr: errors.New("some error"),
				output:    nil,
			},
			appConfig: map[string]any{
				"SYMMETRIC_KEY": normalKey,
			},
		},
		{
			name:    "get_issuer_by_id_error",
			req:     1,
			want:    schema.EncryptedCardToken{},
			wantErr: errors.New("some error"),
			getCardByID: getCardByIDOp{
				input:     1,
				outputErr: nil,
				output: model.Card{
					Id:            1,
					ClientId:      1,
					IssuerId:      1,
					IpsId:         1,
					CountryId:     1,
					SaveAccess:    true,
					Approved:      false,
					KeyId:         1,
					EncryptMonth:  "AAAAAAAAAAAAAAAAAAAAAJWXVsNZooT1Nj5qlh9r4ws=",
					EncryptYear:   "AAAAAAAAAAAAAAAAAAAAAJWXVsNZooT1Nj5qlh9r4ws=",
					EncryptName:   "AAAAAAAAAAAAAAAAAAAAAJWXVsNZooT1Nj5qlh9r4ws=",
					EncryptPan:    "AAAAAAAAAAAAAAAAAAAAACXJ7eT6CzNrPzsbyIRluEFgK65FdiS3n0szvcsdV2Tb",
					Pan:           "something",
					IsPanModified: true,
				},
			},
			getKeyByID: getKeyByIDOp{
				isCalled: true,
				input:    1,
				output: model.Key{
					Id:  1,
					Key: "AAAAAAAAAAAAAAAAAAAAAD4r1NYivkHsOHdiq9dCiJiz0HJtt7wrVFA8fx/DenGL",
				},
				outputErr: nil,
			},
			getClientByID: getClientByIDOp{
				isCalled: true,
				input:    1,
				output: &model.Client{
					Id:              1,
					ProjectClientId: "asd",
					ProjectId:       1,
				},
				outputErr: nil,
			},
			getProjectMaskFormatByProjectID: getProjectMaskFormatByProjectIDOp{
				isCalled: true,
				input:    1,
				output: &model.ProjectMaskFormat{
					Id:              1,
					ProjectId:       1,
					PlaceholderSign: "*",
					WithSeparator:   &defaultWithSep,
				},
				outputErr: nil,
			},
			getIpsByAcquirerID: getIpsByAcquirerIDOp{
				isCalled: true,
				input: &grpc.GetIpsByIDRequestV1{
					IpsId: testsdk.Ptr(uint64(1)),
				},
				output: &grpc.GetIpsByIDResponseV1{
					Name: testsdk.Ptr("some name"),
				},
				outputErr: nil,
			},
			getIssuerByAcquirerID: getIssuerByAcquirerIDOp{
				isCalled: true,
				input: &grpc.GetIssuerByIDRequestV1{
					IssuerId: testsdk.Ptr(uint64(1)),
				},
				output:    nil,
				outputErr: errors.New("some error"),
			},
			appConfig: map[string]any{
				"SYMMETRIC_KEY": normalKey,
			},
		},
		{
			name: "success",
			req:  1,
			want: schema.EncryptedCardToken{
				CardToken: "AAAAAAAAAAAAAAAAAAAAAAa1Z7+ZP7uEJjDtmP8f3pDIe/kbCBSPpoa11F3kFocM",
				MaskedPan: "4342-07******-3288",
				IpsName:   "some name", IssuerName: "some name", Year: "something", Month: "something",
			},
			wantErr: nil,
			getCardByID: getCardByIDOp{
				input:     1,
				outputErr: nil,
				output: model.Card{
					Id:            1,
					ClientId:      1,
					IssuerId:      1,
					IpsId:         1,
					CountryId:     1,
					SaveAccess:    true,
					Approved:      false,
					KeyId:         1,
					EncryptMonth:  "AAAAAAAAAAAAAAAAAAAAAJWXVsNZooT1Nj5qlh9r4ws=",
					EncryptYear:   "AAAAAAAAAAAAAAAAAAAAAJWXVsNZooT1Nj5qlh9r4ws=",
					EncryptName:   "AAAAAAAAAAAAAAAAAAAAAJWXVsNZooT1Nj5qlh9r4ws=",
					EncryptPan:    "AAAAAAAAAAAAAAAAAAAAACXJ7eT6CzNrPzsbyIRluEFgK65FdiS3n0szvcsdV2Tb",
					Pan:           "something",
					IsPanModified: true,
				},
			},
			getKeyByID: getKeyByIDOp{
				isCalled: true,
				input:    1,
				output: model.Key{
					Id:  1,
					Key: "AAAAAAAAAAAAAAAAAAAAAD4r1NYivkHsOHdiq9dCiJiz0HJtt7wrVFA8fx/DenGL",
				},
				outputErr: nil,
			},
			getClientByID: getClientByIDOp{
				isCalled: true,
				input:    1,
				output: &model.Client{
					Id:              1,
					ProjectClientId: "asd",
					ProjectId:       1,
				},
				outputErr: nil,
			},
			getProjectMaskFormatByProjectID: getProjectMaskFormatByProjectIDOp{
				isCalled: true,
				input:    1,
				output: &model.ProjectMaskFormat{
					Id:              1,
					ProjectId:       1,
					PlaceholderSign: "*",
					WithSeparator:   &defaultWithSep,
				},
				outputErr: nil,
			},
			getIpsByAcquirerID: getIpsByAcquirerIDOp{
				isCalled: true,
				input: &grpc.GetIpsByIDRequestV1{
					IpsId: testsdk.Ptr(uint64(1)),
				},
				output: &grpc.GetIpsByIDResponseV1{
					Name: testsdk.Ptr("some name"),
				},
				outputErr: nil,
			},
			getIssuerByAcquirerID: getIssuerByAcquirerIDOp{
				isCalled: true,
				input: &grpc.GetIssuerByIDRequestV1{
					IssuerId: testsdk.Ptr(uint64(1)),
				},
				output: &grpc.GetIssuerByIDResponseV1{
					Name: testsdk.Ptr("some name"),
				},
				outputErr: nil,
			},
			appConfig: map[string]any{
				"SYMMETRIC_KEY": normalKey,
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctrl := gomock.NewController(t)

			ctx, cancel := context.WithCancel(context.Background())
			e2e.InitAppE2E(t, ctx, cancel, nil, nil, tt.appConfig)

			cardsDBMock := databasemocks.NewMockCarder(ctrl)
			keyDBMock := databasemocks.NewMockKeyer(ctrl)
			clientDBMock := databasemocks.NewMockClientor(ctrl)
			projectMaskFormatDBMock := databasemocks.NewMockProjectMaskFormatter(ctrl)
			acquirerCliMock := grpcmock.NewMockAcquirerClient(ctrl)

			s := CardGetterService{
				projectMaskFormatRepo: projectMaskFormatDBMock,
				clientsRepo:           clientDBMock,
				cardsRepo:             cardsDBMock,
				keyRepo:               keyDBMock,
				acquirerClient:        acquirerCliMock,
			}

			cardsDBMock.EXPECT().GetByID(
				gomock.Any(),
				tt.getCardByID.input,
			).Return(
				tt.getCardByID.output,
				tt.getCardByID.outputErr,
			)

			if tt.getKeyByID.isCalled {
				keyDBMock.EXPECT().GetById(
					gomock.Any(),
					tt.getKeyByID.input,
				).Return(
					tt.getKeyByID.output,
					tt.getKeyByID.outputErr,
				)
			}

			if tt.getClientByID.isCalled {
				clientDBMock.EXPECT().GetByID(
					gomock.Any(),
					tt.getClientByID.input,
				).Return(
					tt.getClientByID.output,
					tt.getClientByID.outputErr,
				)
			}

			if tt.getProjectMaskFormatByProjectID.isCalled {
				projectMaskFormatDBMock.EXPECT().GetByProjectID(
					gomock.Any(),
					tt.getProjectMaskFormatByProjectID.input,
				).Return(
					tt.getProjectMaskFormatByProjectID.output,
					tt.getProjectMaskFormatByProjectID.outputErr,
				)
			}

			if tt.getIpsByAcquirerID.isCalled {
				acquirerCliMock.EXPECT().GetIpsByID(
					gomock.Any(),
					tt.getIpsByAcquirerID.input,
				).Return(
					tt.getIpsByAcquirerID.output,
					tt.getIpsByAcquirerID.outputErr,
				)
			}

			if tt.getIssuerByAcquirerID.isCalled {
				acquirerCliMock.EXPECT().GetIssuerByID(
					gomock.Any(),
					tt.getIssuerByAcquirerID.input,
				).Return(
					tt.getIssuerByAcquirerID.output,
					tt.getIssuerByAcquirerID.outputErr,
				)
			}

			res, err := s.GetCardToken(context.Background(), tt.req)
			if tt.wantErr != nil {
				require.Error(t, err)
				require.ErrorContains(t, err, tt.wantErr.Error())
			} else {
				require.NoError(t, err)
			}

			require.Equal(t, tt.want, res)
		})
	}
}
