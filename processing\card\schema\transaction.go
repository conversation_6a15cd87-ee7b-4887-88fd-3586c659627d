package schema

import (
	gorpc "git.local/sensitive/innerpb/processing/grpc"
	"strconv"
	"time"
)

type (
	TransactionInfo struct {
		CallbackUrl        string
		ConfirmUrl         string
		SuccessRedirectUrl string
		FailureRedirectUrl string
		UserPhone          string
		UserEmail          string
		Shipment           string
		BankCode           string
		BankMessage        string
		BankOrderID        string
	}

	TransactionResponse struct {
		UpdatedAt          time.Time       `json:"updated_at"`
		CreatedAt          time.Time       `json:"created_at"`
		FinishedAt         time.Time       `json:"finished_at"`
		ID                 uint64          `json:"id"`
		AcquirerID         *uint64         `json:"acquirer_id"`
		TerminalID         *uint64         `json:"terminal_id"`
		BankReferenceID    *string         `json:"bank_reference_id"`
		ProjectReferenceID string          `json:"project_reference_id"`
		ProjectClientID    string          `json:"project_client_id"`
		MaskedPan          string          `json:"masked_pan"`
		ProjectID          uint64          `json:"project_id"`
		MerchantID         uint64          `json:"merchant_id"`
		StatusID           uint64          `json:"status_id"`
		TypeID             uint64          `json:"type_id"`
		ParentID           uint64          `json:"parent_id"`
		Amount             float64         `json:"amount"`
		Try                uint64          `json:"try"`
		Description        string          `json:"description"`
		CardID             *uint64         `json:"card_id"`
		StatusCode         string          `json:"status_code"`
		TypeCode           string          `json:"type_code"`
		TransactionInfo    TransactionInfo `json:"transaction_info"`
	}
)

func NewTransactionResponseFromRaw(v1 *gorpc.TransactionDataV1) TransactionResponse {
	return TransactionResponse{
		ID:                 v1.GetId(),
		AcquirerID:         v1.AcquirerId,
		TerminalID:         v1.TerminalId,
		BankReferenceID:    v1.BankReferenceId,
		ProjectReferenceID: v1.GetProjectReferenceId(),
		ProjectClientID:    v1.GetProjectClientId(),
		MerchantID:         v1.GetMerchantId(),
		ProjectID:          v1.GetProjectId(),
		StatusID:           v1.GetStatusId(),
		TypeID:             v1.GetTypeId(),
		CardID:             v1.CardId,
		Description:        v1.GetDescription(),
		Amount:             v1.GetAmount(),
		StatusCode:         v1.GetStatusCode(),
		TypeCode:           v1.GetTypeCode(),
		TransactionInfo: TransactionInfo{
			CallbackUrl:        v1.GetTransactionInfo().GetCallbackUrl(),
			ConfirmUrl:         v1.GetTransactionInfo().GetConfirmUrl(),
			SuccessRedirectUrl: v1.GetTransactionInfo().GetSuccessRedirectUrl(),
			FailureRedirectUrl: v1.GetTransactionInfo().GetFailureRedirectUrl(),
			UserPhone:          v1.GetTransactionInfo().GetUserPhone(),
			UserEmail:          v1.GetTransactionInfo().GetUserEmail(),
			Shipment:           v1.GetTransactionInfo().GetShipment(),
			BankOrderID:        v1.GetTransactionInfo().GetBankOrderId(),
		},
		CreatedAt: v1.CreatedAt.AsTime(),
		UpdatedAt: v1.UpdatedAt.AsTime(),
	}
}

func (t TransactionResponse) GetTransactionSecretForView() string {
	createdAt := strconv.Itoa(t.CreatedAt.Second())
	transactionID := strconv.FormatUint(t.ID, 10)
	projectID := strconv.FormatUint(t.ProjectID, 10)
	merchantID := strconv.FormatUint(t.MerchantID, 10)
	amount := strconv.FormatFloat(t.Amount, 'g', 2, 64)

	return createdAt + transactionID + t.ProjectReferenceID + projectID + merchantID + amount
}
