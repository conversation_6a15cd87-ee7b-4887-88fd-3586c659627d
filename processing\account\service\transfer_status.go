package service

import (
	"context"
	"git.local/sensitive/processing/account/model"
	"git.local/sensitive/processing/account/repository"
	"git.local/sensitive/sdk/dog"
)

type TransferStatusService struct {
	transferStatusRepo repository.TransferStatuser
}

func NewTransferStatusService(transferStatusRepo repository.TransferStatuser) TransferStatuser {
	return &TransferStatusService{
		transferStatusRepo: transferStatusRepo,
	}
}

func (ts *TransferStatusService) GetAll(
	ctx context.Context,
) (_ []model.TransferStatus, err error) {
	ctx, span := dog.CreateSpan(ctx, "TransferStatusService_GetAll")
	defer span.End()

	return ts.transferStatusRepo.GetAll(ctx)
}

func (ts *TransferStatusService) GetSuccess(ctx context.Context) (*model.TransferStatus, error) {
	ctx, span := dog.CreateSpan(ctx, "TransferStatusService_GetSuccess")
	defer span.End()

	return ts.transferStatusRepo.GetSuccess(ctx)
}
