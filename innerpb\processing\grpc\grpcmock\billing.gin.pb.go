// Code generated by MockGen. DO NOT EDIT.
// Source: billing.gin.pb.go

// Package grpcmock is a generated GoMock package.
package grpcmock

import (
	reflect "reflect"

	gin "github.com/gin-gonic/gin"
	gomock "github.com/golang/mock/gomock"
)

// MockGinBillingServer is a mock of GinBillingServer interface.
type MockGinBillingServer struct {
	ctrl     *gomock.Controller
	recorder *MockGinBillingServerMockRecorder
}

// MockGinBillingServerMockRecorder is the mock recorder for MockGinBillingServer.
type MockGinBillingServerMockRecorder struct {
	mock *MockGinBillingServer
}

// NewMockGinBillingServer creates a new mock instance.
func NewMockGinBillingServer(ctrl *gomock.Controller) *MockGinBillingServer {
	mock := &MockGinBillingServer{ctrl: ctrl}
	mock.recorder = &MockGinBillingServerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockGinBillingServer) EXPECT() *MockGinBillingServerMockRecorder {
	return m.recorder
}

// BillInTransferV1 mocks base method.
func (m *MockGinBillingServer) BillInTransferV1(c *gin.Context) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BillInTransferV1", c)
	ret0, _ := ret[0].(error)
	return ret0
}

// BillInTransferV1 indicates an expected call of BillInTransferV1.
func (mr *MockGinBillingServerMockRecorder) BillInTransferV1(c interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BillInTransferV1", reflect.TypeOf((*MockGinBillingServer)(nil).BillInTransferV1), c)
}

// BillOutTransferV1 mocks base method.
func (m *MockGinBillingServer) BillOutTransferV1(c *gin.Context) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BillOutTransferV1", c)
	ret0, _ := ret[0].(error)
	return ret0
}

// BillOutTransferV1 indicates an expected call of BillOutTransferV1.
func (mr *MockGinBillingServerMockRecorder) BillOutTransferV1(c interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BillOutTransferV1", reflect.TypeOf((*MockGinBillingServer)(nil).BillOutTransferV1), c)
}

// BillPayInTransactionV1 mocks base method.
func (m *MockGinBillingServer) BillPayInTransactionV1(c *gin.Context) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BillPayInTransactionV1", c)
	ret0, _ := ret[0].(error)
	return ret0
}

// BillPayInTransactionV1 indicates an expected call of BillPayInTransactionV1.
func (mr *MockGinBillingServerMockRecorder) BillPayInTransactionV1(c interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BillPayInTransactionV1", reflect.TypeOf((*MockGinBillingServer)(nil).BillPayInTransactionV1), c)
}

// BillPayOutTransactionV1 mocks base method.
func (m *MockGinBillingServer) BillPayOutTransactionV1(c *gin.Context) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BillPayOutTransactionV1", c)
	ret0, _ := ret[0].(error)
	return ret0
}

// BillPayOutTransactionV1 indicates an expected call of BillPayOutTransactionV1.
func (mr *MockGinBillingServerMockRecorder) BillPayOutTransactionV1(c interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BillPayOutTransactionV1", reflect.TypeOf((*MockGinBillingServer)(nil).BillPayOutTransactionV1), c)
}

// BillRefundTransactionV1 mocks base method.
func (m *MockGinBillingServer) BillRefundTransactionV1(c *gin.Context) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BillRefundTransactionV1", c)
	ret0, _ := ret[0].(error)
	return ret0
}

// BillRefundTransactionV1 indicates an expected call of BillRefundTransactionV1.
func (mr *MockGinBillingServerMockRecorder) BillRefundTransactionV1(c interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BillRefundTransactionV1", reflect.TypeOf((*MockGinBillingServer)(nil).BillRefundTransactionV1), c)
}

// BillSplitTransferV1 mocks base method.
func (m *MockGinBillingServer) BillSplitTransferV1(c *gin.Context) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BillSplitTransferV1", c)
	ret0, _ := ret[0].(error)
	return ret0
}

// BillSplitTransferV1 indicates an expected call of BillSplitTransferV1.
func (mr *MockGinBillingServerMockRecorder) BillSplitTransferV1(c interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BillSplitTransferV1", reflect.TypeOf((*MockGinBillingServer)(nil).BillSplitTransferV1), c)
}

// CheckBalanceCreditExpireDate mocks base method.
func (m *MockGinBillingServer) CheckBalanceCreditExpireDate(c *gin.Context) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CheckBalanceCreditExpireDate", c)
	ret0, _ := ret[0].(error)
	return ret0
}

// CheckBalanceCreditExpireDate indicates an expected call of CheckBalanceCreditExpireDate.
func (mr *MockGinBillingServerMockRecorder) CheckBalanceCreditExpireDate(c interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CheckBalanceCreditExpireDate", reflect.TypeOf((*MockGinBillingServer)(nil).CheckBalanceCreditExpireDate), c)
}

// CheckBalanceCreditStartDate mocks base method.
func (m *MockGinBillingServer) CheckBalanceCreditStartDate(c *gin.Context) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CheckBalanceCreditStartDate", c)
	ret0, _ := ret[0].(error)
	return ret0
}

// CheckBalanceCreditStartDate indicates an expected call of CheckBalanceCreditStartDate.
func (mr *MockGinBillingServerMockRecorder) CheckBalanceCreditStartDate(c interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CheckBalanceCreditStartDate", reflect.TypeOf((*MockGinBillingServer)(nil).CheckBalanceCreditStartDate), c)
}

// CheckBalanceOwnerAccountExistsByBalanceOwnerIdAndAccountIdV1 mocks base method.
func (m *MockGinBillingServer) CheckBalanceOwnerAccountExistsByBalanceOwnerIdAndAccountIdV1(c *gin.Context) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CheckBalanceOwnerAccountExistsByBalanceOwnerIdAndAccountIdV1", c)
	ret0, _ := ret[0].(error)
	return ret0
}

// CheckBalanceOwnerAccountExistsByBalanceOwnerIdAndAccountIdV1 indicates an expected call of CheckBalanceOwnerAccountExistsByBalanceOwnerIdAndAccountIdV1.
func (mr *MockGinBillingServerMockRecorder) CheckBalanceOwnerAccountExistsByBalanceOwnerIdAndAccountIdV1(c interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CheckBalanceOwnerAccountExistsByBalanceOwnerIdAndAccountIdV1", reflect.TypeOf((*MockGinBillingServer)(nil).CheckBalanceOwnerAccountExistsByBalanceOwnerIdAndAccountIdV1), c)
}

// CheckHasBalanceV1 mocks base method.
func (m *MockGinBillingServer) CheckHasBalanceV1(c *gin.Context) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CheckHasBalanceV1", c)
	ret0, _ := ret[0].(error)
	return ret0
}

// CheckHasBalanceV1 indicates an expected call of CheckHasBalanceV1.
func (mr *MockGinBillingServerMockRecorder) CheckHasBalanceV1(c interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CheckHasBalanceV1", reflect.TypeOf((*MockGinBillingServer)(nil).CheckHasBalanceV1), c)
}

// CheckOutTransferBalanceV1 mocks base method.
func (m *MockGinBillingServer) CheckOutTransferBalanceV1(c *gin.Context) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CheckOutTransferBalanceV1", c)
	ret0, _ := ret[0].(error)
	return ret0
}

// CheckOutTransferBalanceV1 indicates an expected call of CheckOutTransferBalanceV1.
func (mr *MockGinBillingServerMockRecorder) CheckOutTransferBalanceV1(c interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CheckOutTransferBalanceV1", reflect.TypeOf((*MockGinBillingServer)(nil).CheckOutTransferBalanceV1), c)
}

// CheckPayOutBalanceV1 mocks base method.
func (m *MockGinBillingServer) CheckPayOutBalanceV1(c *gin.Context) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CheckPayOutBalanceV1", c)
	ret0, _ := ret[0].(error)
	return ret0
}

// CheckPayOutBalanceV1 indicates an expected call of CheckPayOutBalanceV1.
func (mr *MockGinBillingServerMockRecorder) CheckPayOutBalanceV1(c interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CheckPayOutBalanceV1", reflect.TypeOf((*MockGinBillingServer)(nil).CheckPayOutBalanceV1), c)
}

// GetBalanceAccountByNumber mocks base method.
func (m *MockGinBillingServer) GetBalanceAccountByNumber(c *gin.Context) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetBalanceAccountByNumber", c)
	ret0, _ := ret[0].(error)
	return ret0
}

// GetBalanceAccountByNumber indicates an expected call of GetBalanceAccountByNumber.
func (mr *MockGinBillingServerMockRecorder) GetBalanceAccountByNumber(c interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetBalanceAccountByNumber", reflect.TypeOf((*MockGinBillingServer)(nil).GetBalanceAccountByNumber), c)
}

// GetBalanceByIDV1 mocks base method.
func (m *MockGinBillingServer) GetBalanceByIDV1(c *gin.Context) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetBalanceByIDV1", c)
	ret0, _ := ret[0].(error)
	return ret0
}

// GetBalanceByIDV1 indicates an expected call of GetBalanceByIDV1.
func (mr *MockGinBillingServerMockRecorder) GetBalanceByIDV1(c interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetBalanceByIDV1", reflect.TypeOf((*MockGinBillingServer)(nil).GetBalanceByIDV1), c)
}

// GetBalanceOwnerByIDV1 mocks base method.
func (m *MockGinBillingServer) GetBalanceOwnerByIDV1(c *gin.Context) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetBalanceOwnerByIDV1", c)
	ret0, _ := ret[0].(error)
	return ret0
}

// GetBalanceOwnerByIDV1 indicates an expected call of GetBalanceOwnerByIDV1.
func (mr *MockGinBillingServerMockRecorder) GetBalanceOwnerByIDV1(c interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetBalanceOwnerByIDV1", reflect.TypeOf((*MockGinBillingServer)(nil).GetBalanceOwnerByIDV1), c)
}

// GetBalanceOwnerV1 mocks base method.
func (m *MockGinBillingServer) GetBalanceOwnerV1(c *gin.Context) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetBalanceOwnerV1", c)
	ret0, _ := ret[0].(error)
	return ret0
}

// GetBalanceOwnerV1 indicates an expected call of GetBalanceOwnerV1.
func (mr *MockGinBillingServerMockRecorder) GetBalanceOwnerV1(c interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetBalanceOwnerV1", reflect.TypeOf((*MockGinBillingServer)(nil).GetBalanceOwnerV1), c)
}

// GetCountryCodeByIDV1 mocks base method.
func (m *MockGinBillingServer) GetCountryCodeByIDV1(c *gin.Context) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetCountryCodeByIDV1", c)
	ret0, _ := ret[0].(error)
	return ret0
}

// GetCountryCodeByIDV1 indicates an expected call of GetCountryCodeByIDV1.
func (mr *MockGinBillingServerMockRecorder) GetCountryCodeByIDV1(c interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetCountryCodeByIDV1", reflect.TypeOf((*MockGinBillingServer)(nil).GetCountryCodeByIDV1), c)
}

// GetCurrentBalanceAmountByBalanceOwnerID mocks base method.
func (m *MockGinBillingServer) GetCurrentBalanceAmountByBalanceOwnerID(c *gin.Context) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetCurrentBalanceAmountByBalanceOwnerID", c)
	ret0, _ := ret[0].(error)
	return ret0
}

// GetCurrentBalanceAmountByBalanceOwnerID indicates an expected call of GetCurrentBalanceAmountByBalanceOwnerID.
func (mr *MockGinBillingServerMockRecorder) GetCurrentBalanceAmountByBalanceOwnerID(c interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetCurrentBalanceAmountByBalanceOwnerID", reflect.TypeOf((*MockGinBillingServer)(nil).GetCurrentBalanceAmountByBalanceOwnerID), c)
}

// GetEntityTypeByIDV1 mocks base method.
func (m *MockGinBillingServer) GetEntityTypeByIDV1(c *gin.Context) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetEntityTypeByIDV1", c)
	ret0, _ := ret[0].(error)
	return ret0
}

// GetEntityTypeByIDV1 indicates an expected call of GetEntityTypeByIDV1.
func (mr *MockGinBillingServerMockRecorder) GetEntityTypeByIDV1(c interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetEntityTypeByIDV1", reflect.TypeOf((*MockGinBillingServer)(nil).GetEntityTypeByIDV1), c)
}

// GetMerchantByBalanceOwnerIDV1 mocks base method.
func (m *MockGinBillingServer) GetMerchantByBalanceOwnerIDV1(c *gin.Context) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetMerchantByBalanceOwnerIDV1", c)
	ret0, _ := ret[0].(error)
	return ret0
}

// GetMerchantByBalanceOwnerIDV1 indicates an expected call of GetMerchantByBalanceOwnerIDV1.
func (mr *MockGinBillingServerMockRecorder) GetMerchantByBalanceOwnerIDV1(c interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetMerchantByBalanceOwnerIDV1", reflect.TypeOf((*MockGinBillingServer)(nil).GetMerchantByBalanceOwnerIDV1), c)
}

// RecalculateCreditBalances mocks base method.
func (m *MockGinBillingServer) RecalculateCreditBalances(c *gin.Context) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "RecalculateCreditBalances", c)
	ret0, _ := ret[0].(error)
	return ret0
}

// RecalculateCreditBalances indicates an expected call of RecalculateCreditBalances.
func (mr *MockGinBillingServerMockRecorder) RecalculateCreditBalances(c interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "RecalculateCreditBalances", reflect.TypeOf((*MockGinBillingServer)(nil).RecalculateCreditBalances), c)
}

// RecalculateFinalBalances mocks base method.
func (m *MockGinBillingServer) RecalculateFinalBalances(c *gin.Context) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "RecalculateFinalBalances", c)
	ret0, _ := ret[0].(error)
	return ret0
}

// RecalculateFinalBalances indicates an expected call of RecalculateFinalBalances.
func (mr *MockGinBillingServerMockRecorder) RecalculateFinalBalances(c interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "RecalculateFinalBalances", reflect.TypeOf((*MockGinBillingServer)(nil).RecalculateFinalBalances), c)
}

// RecalculateProvisionalBalances mocks base method.
func (m *MockGinBillingServer) RecalculateProvisionalBalances(c *gin.Context) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "RecalculateProvisionalBalances", c)
	ret0, _ := ret[0].(error)
	return ret0
}

// RecalculateProvisionalBalances indicates an expected call of RecalculateProvisionalBalances.
func (mr *MockGinBillingServerMockRecorder) RecalculateProvisionalBalances(c interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "RecalculateProvisionalBalances", reflect.TypeOf((*MockGinBillingServer)(nil).RecalculateProvisionalBalances), c)
}

// SetBalanceOwnerSplittableV1 mocks base method.
func (m *MockGinBillingServer) SetBalanceOwnerSplittableV1(c *gin.Context) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SetBalanceOwnerSplittableV1", c)
	ret0, _ := ret[0].(error)
	return ret0
}

// SetBalanceOwnerSplittableV1 indicates an expected call of SetBalanceOwnerSplittableV1.
func (mr *MockGinBillingServerMockRecorder) SetBalanceOwnerSplittableV1(c interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetBalanceOwnerSplittableV1", reflect.TypeOf((*MockGinBillingServer)(nil).SetBalanceOwnerSplittableV1), c)
}

// SetInTransferV1 mocks base method.
func (m *MockGinBillingServer) SetInTransferV1(c *gin.Context) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SetInTransferV1", c)
	ret0, _ := ret[0].(error)
	return ret0
}

// SetInTransferV1 indicates an expected call of SetInTransferV1.
func (mr *MockGinBillingServerMockRecorder) SetInTransferV1(c interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetInTransferV1", reflect.TypeOf((*MockGinBillingServer)(nil).SetInTransferV1), c)
}
