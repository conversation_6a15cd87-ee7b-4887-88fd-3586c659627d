package encryptor

import (
	"crypto/rand"
	"crypto/rsa"
	"crypto/x509"
	"encoding/pem"
	"errors"
)

type DecryptManager struct {
	decryptionKey string
}

func NewDecryptManager(
	decryptionKey string,
) Decryptor {
	return &DecryptManager{
		decryptionKey: decryptionKey,
	}
}

func (d DecryptManager) DecryptCard(card EncryptedCard) (DecryptedCard, error) {
	var (
		decryptedPan,
		decryptedName,
		decryptedMonth,
		decryptedYear,
		decryptedCvc string
	)

	privateBlock, _ := pem.Decode([]byte(d.decryptionKey))
	if privateBlock == nil {
		return DecryptedCard{}, errors.New("failed to parse key")
	}

	parsedPrivateKey, err := x509.ParsePKCS8PrivateKey(privateBlock.Bytes)
	if err != nil {
		return DecryptedCard{}, err
	}

	rsaPrivKey, ok := parsedPrivateKey.(*rsa.<PERSON><PERSON>ey)
	if !ok {
		return DecryptedCard{}, errors.New("failed to parse key")
	}

	if card.Number != nil {
		decryptedPanBytes, err := rsa.DecryptPKCS1v15(rand.Reader, rsaPrivKey, card.Number)
		if err != nil {
			return DecryptedCard{}, err
		}

		decryptedPan = string(decryptedPanBytes)
	}

	if card.HolderName != nil {
		decryptedNameBytes, err := rsa.DecryptPKCS1v15(rand.Reader, rsaPrivKey, card.HolderName)
		if err != nil {
			return DecryptedCard{}, err
		}

		decryptedName = string(decryptedNameBytes)
	}

	if card.ExpirationMonth != nil {
		decryptedMonthBytes, err := rsa.DecryptPKCS1v15(rand.Reader, rsaPrivKey, card.ExpirationMonth)
		if err != nil {
			return DecryptedCard{}, err
		}

		decryptedMonth = string(decryptedMonthBytes)
	}

	if card.ExpirationYear != nil {
		decryptedYearBytes, err := rsa.DecryptPKCS1v15(rand.Reader, rsaPrivKey, card.ExpirationYear)
		if err != nil {
			return DecryptedCard{}, err
		}

		decryptedYear = string(decryptedYearBytes)
	}

	if card.CVC != nil {
		decryptedCvcBytes, err := rsa.DecryptPKCS1v15(rand.Reader, rsaPrivKey, card.CVC)
		if err != nil {
			return DecryptedCard{}, err
		}

		decryptedCvc = string(decryptedCvcBytes)
	}

	return DecryptedCard{
		Number:          decryptedPan,
		ExpirationMonth: decryptedMonth,
		ExpirationYear:  decryptedYear,
		CVC:             decryptedCvc,
		HolderName:      decryptedName,
	}, nil
}
