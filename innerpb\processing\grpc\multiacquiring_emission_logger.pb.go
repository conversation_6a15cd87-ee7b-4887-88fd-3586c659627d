// Code generated by protoc-gen-go-mvp. DO NOT EDIT.
// versions:
// - protoc-gen-go-mvp v2.0.1
// created by mvp authors

package grpc

import (
	context "context"
	fmt "fmt"
	cntx "git.local/sensitive/mvp/pkg/cntx"
	ctxzap "github.com/grpc-ecosystem/go-grpc-middleware/logging/zap/ctxzap"
	zap "go.uber.org/zap"
	grpc "google.golang.org/grpc"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
)

func file_inner_processing_grpc_multiacquiring_emission_proto_message_EmptyToZap(
	label string,
	in *emptypb.Empty,
) zap.Field {
	if in == nil {
		return zap.Reflect(label, in)
	}

	return zap.Dict(
		label,
	)
}

func file_inner_processing_grpc_multiacquiring_emission_proto_message_EmissionResponseToZap(
	label string,
	in *EmissionResponse,
) zap.Field {
	if in == nil {
		return zap.Reflect(label, in)
	}

	return zap.Dict(
		label,
		zap.Any("Code", in.GetCode()),
		zap.Any("Message", in.GetMessage()),
		file_inner_processing_grpc_multiacquiring_emission_proto_message_ValueSliceToZap("Value", in.GetValue()),
	)
}

func file_inner_processing_grpc_multiacquiring_emission_proto_message_EmoneyRequestToZap(
	label string,
	in *EmoneyRequest,
) zap.Field {
	if in == nil {
		return zap.Reflect(label, in)
	}

	return zap.Dict(
		label,
		zap.Any("Id", in.GetId()),
	)
}

func file_inner_processing_grpc_multiacquiring_emission_proto_message_EmoneyResponseToZap(
	label string,
	in *EmoneyResponse,
) zap.Field {
	if in == nil {
		return zap.Reflect(label, in)
	}

	return zap.Dict(
		label,
		zap.Any("Code", in.GetCode()),
		zap.Any("Message", in.GetMessage()),
	)
}

func file_inner_processing_grpc_multiacquiring_emission_proto_message_ValueToZap(
	label string,
	in *Value,
) zap.Field {
	if in == nil {
		return zap.Reflect(label, in)
	}

	return zap.Dict(
		label,
		zap.Any("Id", in.GetId()),
		zap.Any("Dscr", in.GetDscr()),
		zap.Any("Clibin", in.GetClibin()),
		zap.Any("Iban", in.GetIban()),
		zap.Any("AgentName", in.GetAgentName()),
		zap.Any("Agent", in.GetAgent()),
		zap.Any("Amount", in.GetAmount()),
	)
}

func file_inner_processing_grpc_multiacquiring_emission_proto_message_ValueSliceToZap(
	label string,
	in []*Value,
) zap.Field {
	var res []zap.Field
	for index, val := range in {
		res = append(res, file_inner_processing_grpc_multiacquiring_emission_proto_message_ValueToZap(fmt.Sprint(index), val))
	}

	return zap.Dict(label, res...)
}

var _ MultiacquiringEmissionServer = (*loggedMultiacquiringEmissionServer)(nil)

func NewLoggedMultiacquiringEmissionServer(srv MultiacquiringEmissionServer) MultiacquiringEmissionServer {
	return &loggedMultiacquiringEmissionServer{srv: srv}
}

type loggedMultiacquiringEmissionServer struct {
	UnimplementedMultiacquiringEmissionServer

	srv MultiacquiringEmissionServer
}

func (s *loggedMultiacquiringEmissionServer) GetEmission(
	ctx context.Context,
	request *emptypb.Empty,
) (
	response *EmissionResponse,
	err error,
) {
	label := cntx.Begin(ctx, "MultiacquiringEmissionServer_GetEmission")
	defer cntx.End(ctx, label)

	defer func() {
		if err != nil {
			ctxzap.AddFields(ctx, zap.Any(label+"error", err))
			return
		}

		ctxzap.AddFields(
			ctx,
			file_inner_processing_grpc_multiacquiring_emission_proto_message_EmissionResponseToZap(label+"response", response),
		)
	}()

	ctxzap.AddFields(
		ctx,
		file_inner_processing_grpc_multiacquiring_emission_proto_message_EmptyToZap(label+"request", request),
	)

	response, err = s.srv.GetEmission(ctx, request)

	return
}

func (s *loggedMultiacquiringEmissionServer) ConfirmEmission(
	ctx context.Context,
	request *EmoneyRequest,
) (
	response *EmoneyResponse,
	err error,
) {
	label := cntx.Begin(ctx, "MultiacquiringEmissionServer_ConfirmEmission")
	defer cntx.End(ctx, label)

	defer func() {
		if err != nil {
			ctxzap.AddFields(ctx, zap.Any(label+"error", err))
			return
		}

		ctxzap.AddFields(
			ctx,
			file_inner_processing_grpc_multiacquiring_emission_proto_message_EmoneyResponseToZap(label+"response", response),
		)
	}()

	ctxzap.AddFields(
		ctx,
		file_inner_processing_grpc_multiacquiring_emission_proto_message_EmoneyRequestToZap(label+"request", request),
	)

	response, err = s.srv.ConfirmEmission(ctx, request)

	return
}

var _ MultiacquiringEmissionClient = (*loggedMultiacquiringEmissionClient)(nil)

func NewLoggedMultiacquiringEmissionClient(client MultiacquiringEmissionClient) MultiacquiringEmissionClient {
	return &loggedMultiacquiringEmissionClient{client: client}
}

type loggedMultiacquiringEmissionClient struct {
	client MultiacquiringEmissionClient
}

func (s *loggedMultiacquiringEmissionClient) GetEmission(
	ctx context.Context,
	request *emptypb.Empty,
	opts ...grpc.CallOption,
) (
	response *EmissionResponse,
	err error,
) {
	label := cntx.Begin(ctx, "MultiacquiringEmissionClient_GetEmission")
	defer cntx.End(ctx, label)

	defer func() {
		if err != nil {
			ctxzap.AddFields(ctx, zap.Any(label+"error", err))
			return
		}

		ctxzap.AddFields(
			ctx,
			file_inner_processing_grpc_multiacquiring_emission_proto_message_EmissionResponseToZap(label+"response", response),
		)
	}()

	ctxzap.AddFields(
		ctx,
		file_inner_processing_grpc_multiacquiring_emission_proto_message_EmptyToZap(label+"request", request),
	)

	response, err = s.client.GetEmission(ctx, request, opts...)

	return
}

func (s *loggedMultiacquiringEmissionClient) ConfirmEmission(
	ctx context.Context,
	request *EmoneyRequest,
	opts ...grpc.CallOption,
) (
	response *EmoneyResponse,
	err error,
) {
	label := cntx.Begin(ctx, "MultiacquiringEmissionClient_ConfirmEmission")
	defer cntx.End(ctx, label)

	defer func() {
		if err != nil {
			ctxzap.AddFields(ctx, zap.Any(label+"error", err))
			return
		}

		ctxzap.AddFields(
			ctx,
			file_inner_processing_grpc_multiacquiring_emission_proto_message_EmoneyResponseToZap(label+"response", response),
		)
	}()

	ctxzap.AddFields(
		ctx,
		file_inner_processing_grpc_multiacquiring_emission_proto_message_EmoneyRequestToZap(label+"request", request),
	)

	response, err = s.client.ConfirmEmission(ctx, request, opts...)

	return
}
