package service

import (
	"context"
	"fmt"
	"regexp"
	"strconv"
	"time"

	"go.uber.org/zap"

	gorpc "git.local/sensitive/innerpb/processing/grpc"
	"git.local/sensitive/processing/account/model"
	"git.local/sensitive/processing/account/repository"
	"git.local/sensitive/processing/account/schema"
	"git.local/sensitive/sdk/dog"
)

const (
	matchLen   = 2
	HoursInDay = 24
)

type InTransferParserService struct {
	inTransferParsingScheduleRepo repository.InTransferParsingScheduler
	processedOrderStatusRepo      repository.ProcessedOrderStatuser
	processedOrderRepo            repository.ProcessedOrderer
	accountInfoRepo               repository.AccountInformer
	transferStatusRepo            repository.TransferStatuser
	transferManageRepo            repository.TransferManager
	billingClient                 gorpc.BillingClient
	merchantClient                gorpc.MerchantClient
	multiaccountingClient         gorpc.MultiaccountingClient
	getCurrentTime                func() time.Time
}

func NewInTransferParserService(
	inTransferParsingScheduleRepo repository.InTransferParsingScheduler,
	processedOrderStatusRepo repository.ProcessedOrderStatuser,
	processedOrderRepo repository.ProcessedOrderer,
	accountInfoRepo repository.AccountInformer,
	transferStatusRepo repository.TransferStatuser,
	transferManageRepo repository.TransferManager,
	billingClient gorpc.BillingClient,
	merchantClient gorpc.MerchantClient,
	multiaccountingClient gorpc.MultiaccountingClient,
) InTransferParser {
	return &InTransferParserService{
		inTransferParsingScheduleRepo: inTransferParsingScheduleRepo,
		processedOrderStatusRepo:      processedOrderStatusRepo,
		processedOrderRepo:            processedOrderRepo,
		accountInfoRepo:               accountInfoRepo,
		transferStatusRepo:            transferStatusRepo,
		transferManageRepo:            transferManageRepo,
		billingClient:                 billingClient,
		merchantClient:                merchantClient,
		multiaccountingClient:         multiaccountingClient,
		getCurrentTime:                time.Now,
	}
}

func (its *InTransferParserService) ParseIncomingTransfers(ctx context.Context) error {
	ctx, span := dog.CreateSpan(ctx, "InTransferParserService_ParseIncomingTransfers")
	defer span.End()

	schedules, err := its.inTransferParsingScheduleRepo.GetAll(ctx)
	if err != nil {
		return err
	}

	accounts, err := its.accountInfoRepo.GetAll(ctx)
	if err != nil {
		return err
	}

	processCount := 0

	for _, account := range accounts {
		for _, schedule := range schedules {
			if schedule.AccountID == account.ID {
				if err = its.ProcessScheduler(ctx, account); err != nil {
					return err
				}

				processCount += 1
			}
		}
	}

	return nil
}

// ProcessScheduler TODO: метод стал экспортируемым, однако не используется на других слоях. это было изменено
// для тестирования, так как пакет который мы используем для патчинга gomonkey, не смог запатчить
// неэкспортируемый метод. Без патчинга, если в методе будет потенциально еще больше вызывов других
// слоев (которые мы обычно мокаем), то работы было бы намного больше
// к прошлому дополнению: можно также поменять используемый пакет патчинга, смотреть
// https://github.com/agiledragon/gomonkey/issues/66
func (its *InTransferParserService) ProcessScheduler(
	ctx context.Context,
	account model.Account,
) error {
	accountInfo, err := schema.AccountInfoConverter(&account)
	if err != nil {
		return fmt.Errorf("account info is empty")
	}

	today := its.getCurrentTime().Truncate(HoursInDay * time.Hour)
	yesterday := today.AddDate(0, 0, -1)
	days := []time.Time{today, yesterday}

	for _, day := range days {
		request := schema.NewRawGetAccountStatementRequest(accountInfo, schema.TimeToDateTime(day))

		statement, statementErr := its.multiaccountingClient.GetAccountStatement(ctx, request)
		if statementErr != nil {
			dog.L().Error("MultiAccountingInfo_GetAccountStatement", zap.Any("request", request), zap.Error(statementErr))
			return statementErr
		}

		for _, operation := range statement.GetOperations() {
			if err = its.ProcessStatement(ctx, operation, account); err != nil {
				return err
			}
		}
	}

	return nil
}

// ProcessStatement TODO: метод стал экспортируемым, однако не используется на других слоях. это было изменено
// для тестирования, так как пакет который мы используем для патчинга gomonkey, не смог запатчить
// неэкспортируемый метод. Без патчинга, если в методе будет потенциально еще больше вызывов других
// слоев (которые мы обычно мокаем), то работы было бы намного больше
// к прошлому дополнению: можно также поменять используемый пакет патчинга, смотреть
// https://github.com/agiledragon/gomonkey/issues/66
func (its *InTransferParserService) ProcessStatement(
	ctx context.Context,
	statement *gorpc.TransitAccountOperation,
	account model.Account,
) error {
	processedOrderStatus, err := its.processedOrderStatusRepo.GetByCode(ctx, model.StatusActual)
	if err != nil {
		return err
	}

	exists, err := its.CompareTransferData(
		ctx,
		statement.GetExternalReferenceId(),
		statement.GetMerchantAccount(),
		account.BankCode,
		processedOrderStatus.ID,
	)
	if err != nil {
		return err
	}

	if !exists {
		if err = its.processedOrderRepo.Create(ctx, model.ProcessedOrder{
			BankOrderID:   statement.GetExternalReferenceId(),
			Date:          statement.GetDate(),
			AccountNumber: statement.GetMerchantAccount(),
			BankCode:      account.BankCode,
			StatusID:      processedOrderStatus.ID,
		}); err != nil {
			return err
		}

		createdTransfer, createdTransferErr := its.CreateTransfer(ctx, statement, account)
		if createdTransferErr != nil {
			return createdTransferErr
		}

		if _, err = its.billingClient.BillInTransferV1(ctx, &gorpc.BillInTransferRequestV1{
			ProjectId:     &createdTransfer.ProjectID,
			AccountNumber: &account.Number,
			Amount:        &createdTransfer.Amount,
			MerchantId:    &createdTransfer.MerchantID,
			TransferId:    &createdTransfer.ID,
		}); err != nil {
			dog.L().Error("BillingRpc_BillInTransfer", zap.Uint64("transfer_id", createdTransfer.ID), zap.Error(err))
			return err
		}
	}

	return nil
}

// CreateTransfer TODO: метод стал экспортируемым, однако не используется на других слоях. это было изменено
// для тестирования, так как пакет который мы используем для патчинга gomonkey, не смог запатчить
// неэкспортируемый метод. Без патчинга, если в методе будет потенциально еще больше вызывов других
// слоев (которые мы обычно мокаем), то работы было бы намного больше
// к прошлому дополнению: можно также поменять используемый пакет патчинга, смотреть
// https://github.com/agiledragon/gomonkey/issues/66
func (its *InTransferParserService) CreateTransfer(
	ctx context.Context,
	statement *gorpc.TransitAccountOperation,
	account model.Account,
) (transfer *model.Transfer, err error) {
	re := regexp.MustCompile(`pid(\d+)pid`)
	matches := re.FindStringSubmatch(statement.GetDescription())

	if len(matches) < matchLen {
		return nil, fmt.Errorf("pid not found in description")
	}

	projectID, projectErr := strconv.ParseUint(matches[1], 10, 64)
	if projectErr != nil {
		return nil, projectErr
	}

	merchantBin := statement.GetMerchantBin()
	merchantProjects, projectErr := its.merchantClient.GetMerchantProjectsByBin(
		ctx, &gorpc.GetMerchantProjectsByBinRequestV1{
			MerchantBin: &merchantBin,
		})

	if projectErr != nil {
		dog.L().Error("MerchantRpc_GetMerchantProjectsByBin", zap.String("merchant_bin", merchantBin), zap.Error(projectErr))
		return nil, projectErr
	}

	status, statusErr := its.transferStatusRepo.GetByCode(ctx, model.StatusSuccess)
	if statusErr != nil {
		return nil, statusErr
	}

	createdAt, err := time.Parse("2006-01-02 15:04:05", statement.GetDate())
	if err != nil {
		return nil, err
	}

	newTransfer, transferErr := its.transferManageRepo.Create(ctx, model.Transfer{
		AcquirerID:              account.BankID,
		AccountID:               account.ID,
		BankReferenceID:         statement.GetExternalReferenceId(),
		BankOrderID:             "",
		ProjectID:               projectID,
		MerchantID:              merchantProjects.GetMerchantId(),
		StatusID:                status.ID,
		Amount:                  statement.GetAmount(),
		MerchantBIN:             merchantBin,
		MerchantAccount:         statement.GetMerchantAccount(),
		MerchantBeneficiaryCode: bankKbe,
		Description:             statement.GetDescription(),
		FinishedAt:              &createdAt,
		TransferTypeID:          statement.GetTransferTypeId(),
		PaymentPurposeCode:      statement.GetPaymentPurposeCode(),
	})

	if transferErr != nil {
		dog.L().Error("InTransferParserService_TransferManageDB_Create", zap.Error(transferErr))
		return nil, transferErr
	}

	return newTransfer, transferErr
}

// CompareTransferData TODO: метод стал экспортируемым, однако не используется на других слоях. это было изменено
// для тестирования, так как пакет который мы используем для патчинга gomonkey, не смог запатчить
// неэкспортируемый метод. Без патчинга, если в методе будет потенциально еще больше вызывов других
// слоев (которые мы обычно мокаем), то работы было бы намного больше
// к прошлому дополнению: можно также поменять используемый пакет патчинга, смотреть
// https://github.com/agiledragon/gomonkey/issues/66
func (its *InTransferParserService) CompareTransferData(
	ctx context.Context,
	orderID, accountNumber, bankCode string,
	statusID uint64,
) (bool, error) {
	processedOrders, err := its.processedOrderRepo.GetByStatusID(ctx, statusID)
	if err != nil {
		return false, err
	}

	processedOrderStatus, err := its.processedOrderStatusRepo.GetByCode(ctx, model.StatusExpired)
	if err != nil {
		return false, err
	}

	for _, order := range processedOrders {
		if order.BankOrderID == orderID && order.AccountNumber == accountNumber &&
			order.BankCode == bankCode {
			return true, nil
		}

		orderDate, orderDateErr := time.Parse("2006-01-02 15:04:05", order.Date)
		if orderDateErr != nil {
			return false, orderDateErr
		}

		if orderDate.Before(its.getCurrentTime().AddDate(0, 0, -2)) {
			orderIDs := []string{orderID}

			if err = its.processedOrderRepo.UpdateStatusByIDs(ctx, orderIDs, processedOrderStatus.ID); err != nil {
				return false, err
			}
		}
	}

	return false, nil
}
