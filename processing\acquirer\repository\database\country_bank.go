package database

import (
	"context"
	"errors"

	"github.com/jackc/pgx/v5/pgconn"
	"gorm.io/gorm"

	"git.local/sensitive/innerpb/processing/goerr"
	"git.local/sensitive/processing/acquirer/model"
	"git.local/sensitive/sdk/dog"
)

type CountryBankDB struct {
	db *gorm.DB
}

func NewCountryBankDB(db *gorm.DB) CountryBanker {
	return &CountryBankDB{
		db: db,
	}
}

func (r *CountryBankDB) Create(ctx context.Context, countryBank *model.CountryBank) (err error) {
	ctx, span := dog.CreateSpan(ctx, "CountryBankDB_Create")
	defer span.End()

	if countryBank == nil {
		return goerr.ErrParseErrorBody.WithCtx(ctx)
	}

	err = r.db.WithContext(ctx).Create(&countryBank).Error
	if err != nil {
		if errors.Is(err, gorm.ErrDuplicatedKey) {
			return goerr.ErrDuplicatedKey.WithCtx(ctx)
		}

		pgErr, ok := err.(*pgconn.PgError)
		if !ok {
			return goerr.ErrDbUnexpected.WithErr(err).WithCtx(ctx)
		}

		if pgErr.Code == ForeignKeyViolationCode {
			return goerr.ErrForeignKeyViolation.WithErr(err).WithCtx(ctx)
		}

		return goerr.ErrDbUnexpected.WithErr(err).WithCtx(ctx)
	}

	return nil
}

func (r *CountryBankDB) Delete(ctx context.Context, countryId uint64, bankId uint64) (err error) {
	ctx, span := dog.CreateSpan(ctx, "CountryBankDB_Delete")
	defer span.End()

	err = r.db.WithContext(ctx).
		Where("country_id = ?", countryId).
		Where("bank_id = ?", bankId).
		First(&model.CountryBank{}).
		Delete(&model.CountryBank{}).
		Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return goerr.ErrCountryBankNotFound.WithCtx(ctx)
		}

		return goerr.ErrDbUnexpected.WithErr(err).WithCtx(ctx)
	}

	return nil
}

func (r *CountryBankDB) GetCountriesByBank(
	ctx context.Context,
	bankId uint64) (_ []*model.Country, err error) {
	ctx, span := dog.CreateSpan(ctx, "CountryBankDB_GetCountriesByBank")
	defer span.End()

	var countries []*model.Country
	err = r.db.Model(&model.Country{}).Where("id = ?", bankId).Preload("Banks").Find(&countries).Error

	if err != nil {
		return nil, goerr.ErrDbUnexpected.WithErr(err).WithCtx(ctx)
	}

	return countries, nil
}

func (r *CountryBankDB) GetBanksByCountryID(
	ctx context.Context,
	countryID uint64,
) (_ []*model.Bank, err error) {
	ctx, span := dog.CreateSpan(ctx, "CountryBankDB_GetBanksByCountryID")
	defer span.End()

	var banks []*model.Bank

	err = r.db.
		Table(model.Bank{}.TableName()+" AS b").
		Joins(`INNER JOIN acquirer.country_banks AS cb ON b.id = cb.bank_id`).
		Joins(`INNER JOIN acquirer.countries AS c ON c.id = cb.country_id`).
		Where(`c.id = ?`, countryID).
		Find(&banks).
		Error
	if err != nil {
		return nil, goerr.ErrDbUnexpected.WithErr(err).WithCtx(ctx)
	}

	return banks, nil
}
