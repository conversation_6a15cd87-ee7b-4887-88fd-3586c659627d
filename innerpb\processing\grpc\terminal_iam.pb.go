// Code generated by protoc-gen-go-mvp. DO NOT EDIT.
// versions:
// - protoc-gen-go-mvp v2.0.1
// created by mvp authors

package grpc

import (
	context "context"
	grpc "google.golang.org/grpc"
	metadata "google.golang.org/grpc/metadata"
)

func NewIamTerminalServer(
	srv TerminalServer,
) TerminalServer {
	return &iamTerminalServer{
		srv: srv,
	}
}

var _ TerminalServer = (*iamTerminalServer)(nil)

type iamTerminalServer struct {
	UnimplementedTerminalServer

	srv TerminalServer
}

func (s *iamTerminalServer) GetByTerminalID(
	ctx context.Context,
	req *TerminalRequestV1,
) (
	*TerminalResponseV1,
	error,
) {
	return s.srv.GetByTerminalID(ctx, req)
}

func (s *iamTerminalServer) FindActiveTerminalsByProject(
	ctx context.Context,
	req *ActiveTerminalsByProjectRequestV1,
) (
	*ActiveTerminalsByProjectResponseV1,
	error,
) {
	return s.srv.FindActiveTerminalsByProject(ctx, req)
}

func (s *iamTerminalServer) SearchTerminal(
	ctx context.Context,
	req *SearchTerminalReqDataV1,
) (
	*SearchTerminalResDataV1,
	error,
) {
	return s.srv.SearchTerminal(ctx, req)
}

func (s *iamTerminalServer) ExtendedSearchTerminal(
	ctx context.Context,
	req *ExtendedSearchTerminalReqDataV1,
) (
	*SearchTerminalResDataV1,
	error,
) {
	return s.srv.ExtendedSearchTerminal(ctx, req)
}

func (s *iamTerminalServer) GetTerminalsByProjectId(
	ctx context.Context,
	req *GetTerminalsByProjectIdRequestV1,
) (
	*ActiveTerminalsByProjectResponseV1,
	error,
) {
	return s.srv.GetTerminalsByProjectId(ctx, req)
}

func (s *iamTerminalServer) GetTerminalWithJusan(
	ctx context.Context,
	req *SearchTerminalReqDataV1,
) (
	*GetTerminalWithJusanResponseV1,
	error,
) {
	return s.srv.GetTerminalWithJusan(ctx, req)
}

func (s *iamTerminalServer) GetRuleByActiveTerminals(
	ctx context.Context,
	req *RuleByActiveTerminalsReqV1,
) (
	*RuleByActiveTerminalsResponseV1,
	error,
) {
	return s.srv.GetRuleByActiveTerminals(ctx, req)
}

func (s *iamTerminalServer) GetPayInProjectTerminals(
	ctx context.Context,
	req *GetPayInProjectTerminalsReqV1,
) (
	*GetPayInProjectTerminalsResponseV1,
	error,
) {
	return s.srv.GetPayInProjectTerminals(ctx, req)
}

func NewIamTerminalClient(
	client TerminalClient,
) TerminalClient {
	return &iamTerminalClient{
		client: client,
	}
}

type iamTerminalClient struct {
	client TerminalClient
}

func (s *iamTerminalClient) GetByTerminalID(
	ctx context.Context,
	req *TerminalRequestV1,
	opts ...grpc.CallOption,
) (
	*TerminalResponseV1,
	error,
) {
	md, _ := metadata.FromIncomingContext(ctx)
	return s.client.GetByTerminalID(metadata.NewOutgoingContext(ctx, md), req)
}

func (s *iamTerminalClient) FindActiveTerminalsByProject(
	ctx context.Context,
	req *ActiveTerminalsByProjectRequestV1,
	opts ...grpc.CallOption,
) (
	*ActiveTerminalsByProjectResponseV1,
	error,
) {
	md, _ := metadata.FromIncomingContext(ctx)
	return s.client.FindActiveTerminalsByProject(metadata.NewOutgoingContext(ctx, md), req)
}

func (s *iamTerminalClient) SearchTerminal(
	ctx context.Context,
	req *SearchTerminalReqDataV1,
	opts ...grpc.CallOption,
) (
	*SearchTerminalResDataV1,
	error,
) {
	md, _ := metadata.FromIncomingContext(ctx)
	return s.client.SearchTerminal(metadata.NewOutgoingContext(ctx, md), req)
}

func (s *iamTerminalClient) ExtendedSearchTerminal(
	ctx context.Context,
	req *ExtendedSearchTerminalReqDataV1,
	opts ...grpc.CallOption,
) (
	*SearchTerminalResDataV1,
	error,
) {
	md, _ := metadata.FromIncomingContext(ctx)
	return s.client.ExtendedSearchTerminal(metadata.NewOutgoingContext(ctx, md), req)
}

func (s *iamTerminalClient) GetTerminalsByProjectId(
	ctx context.Context,
	req *GetTerminalsByProjectIdRequestV1,
	opts ...grpc.CallOption,
) (
	*ActiveTerminalsByProjectResponseV1,
	error,
) {
	md, _ := metadata.FromIncomingContext(ctx)
	return s.client.GetTerminalsByProjectId(metadata.NewOutgoingContext(ctx, md), req)
}

func (s *iamTerminalClient) GetTerminalWithJusan(
	ctx context.Context,
	req *SearchTerminalReqDataV1,
	opts ...grpc.CallOption,
) (
	*GetTerminalWithJusanResponseV1,
	error,
) {
	md, _ := metadata.FromIncomingContext(ctx)
	return s.client.GetTerminalWithJusan(metadata.NewOutgoingContext(ctx, md), req)
}

func (s *iamTerminalClient) GetRuleByActiveTerminals(
	ctx context.Context,
	req *RuleByActiveTerminalsReqV1,
	opts ...grpc.CallOption,
) (
	*RuleByActiveTerminalsResponseV1,
	error,
) {
	md, _ := metadata.FromIncomingContext(ctx)
	return s.client.GetRuleByActiveTerminals(metadata.NewOutgoingContext(ctx, md), req)
}

func (s *iamTerminalClient) GetPayInProjectTerminals(
	ctx context.Context,
	req *GetPayInProjectTerminalsReqV1,
	opts ...grpc.CallOption,
) (
	*GetPayInProjectTerminalsResponseV1,
	error,
) {
	md, _ := metadata.FromIncomingContext(ctx)
	return s.client.GetPayInProjectTerminals(metadata.NewOutgoingContext(ctx, md), req)
}
