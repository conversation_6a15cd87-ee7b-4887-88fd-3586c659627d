edition = "2023";

package processing.card.card;

option go_package = "git.local/sensitive/innerpb/processing/grpc";

import "google/protobuf/timestamp.proto";
import "inner/processing/grpc/terminal.proto";
import "mvp/proto/logger.proto";
import "google/protobuf/empty.proto";

service Card {
  rpc CreateClientV1(CreateClientRequestV1) returns (CreateClientResponseV1) {}
  rpc GetCardTokensV1(GetCardTokensRequestV1) returns (GetCardTokensResponseV1) {}
  rpc GetOneClickPayInCardsV1(GetOneClickPayInCardsRequestV1) returns (GetOneClickPayInCardsResponseV1) {}
  rpc GetPanByCardIdV1(GetPanByCardIdRequestV1) returns (GetPanResponseV1) {}
  rpc GetPanByHashedIdV1(GetPanByHashedIdRequestV1) returns (GetPanResponseV1) {}
  rpc GetOneClickPayOutCardsV1(GetOneClickPayOutCardsRequestV1) returns (GetOneClickPayOutCardsResponseV1){}
  rpc GetEncryptedCardToken(GetEncryptedCardRequestV1)returns(GetEncryptedCardResponseV1){}
  rpc GetClientListByVerification(GetClientListByVerificationRequestV1)returns(GetClientListByProjectClientResponseV1){}
  rpc GetClientListByProjectClient(GetClientListByProjectClientRequestV1)returns(GetClientListByProjectClientResponseV1){}
  rpc CheckClientActiveness(CheckClientActivenessRequestV1)returns(google.protobuf.Empty){}
  rpc GetCardByPan(GetCardByPanRequestV1) returns (GetCardByPanResponseV1);
  rpc DecryptPayInCard(DecryptPayInRequest) returns (DecryptPayInResponse);
  rpc DecryptPayOutCard(DecryptPayOutRequest) returns (DecryptPayOutResponse);
  rpc ReEncryptCard(ReEncryptCardRequest) returns (ReEncryptCardResponse);
  rpc GetPanInfoByProjectId(GetPanInfoByProjectIdRequest) returns(GetPanInfoByProjectIdResponse);

  // jobs
  rpc NewKey(google.protobuf.Empty) returns (google.protobuf.Empty){}
  rpc RotateCardKeys(google.protobuf.Empty) returns (google.protobuf.Empty){}
  rpc CheckExpireCards(google.protobuf.Empty) returns (google.protobuf.Empty){}
  rpc CreateNewHashKey(google.protobuf.Empty) returns (google.protobuf.Empty){}
  rpc RotateHashKeys(google.protobuf.Empty) returns (google.protobuf.Empty){}
}

import "google/protobuf/descriptor.proto";

message CreateClientRequestV1 {
  uint64 project_id = 1;
  string project_client_id = 2;
  string email = 3;
  string phone = 4 [(mvp.FieldLoggerLevel) = Hidden];
  string ip = 5;
  bytes pan = 6 [(mvp.FieldLoggerLevel) = Hidden];
  bytes expiration_year = 7;
  bytes expiration_month = 8;
  bytes cardholder_name = 9;
  bool save_access = 10;
}

message CreateClientResponseV1{
  uint64 id = 1;
  uint64 project_id = 2;
  string project_client_id = 3;
  string ip = 4;
  string email = 5;
  string phone = 6 [(mvp.FieldLoggerLevel) = Hidden];
  ClientCard ClientCard = 8;
}

message CreatePayOutClientRequestV1{
  uint64 project_id = 1;
  string project_client_id = 2;
  string email = 3;
  string phone = 4 [(mvp.FieldLoggerLevel) = Hidden];
  string ip = 5;
  string pan = 6 [(mvp.FieldLoggerLevel) = Hidden];
  bool save_access = 7;
}

message CreatePayOutClientResponseV1{
  uint64 id = 1;
  uint64 project_id = 2;
  string project_client_id = 3;
  string ip = 4;
  string email = 5;
  string phone = 6 [(mvp.FieldLoggerLevel) = Hidden];
  ClientCard ClientCard = 8;
}

message ClientCard {
  uint64 id = 1;
  uint64 ips_id = 2;
  string masked_pan = 3;
  uint64 country_id = 4;
  uint64 issuer_id = 5;
}

message GetCardTokensRequestV1{
  uint64 project_id = 1;
  string project_client_id = 2;
  uint64 card_id = 3;
}

message GetCardTokensResponseV1{
  ClientCard card = 1;
  repeated CardTokenV1 tokens = 2 [(mvp.FieldLoggerLevel) = Hidden];
}

message CardTokenV1 {
  uint64 id = 1;
  uint64 acquirer_id = 2;
  uint64 terminal_id = 3;
  uint64 card_id = 4;
  string token = 5 [(mvp.FieldLoggerLevel) = Hidden];
}

message GetOneClickPayInCardsRequestV1{
  uint64 project_id = 1;
  string project_client_id = 2;
  uint64 transaction_type_id = 3;
  double amount = 4;
  repeated processing.acquirer.terminal.TerminalProjectV1 activeTerminals = 5;
}

message GetOneClickPayInCardsResponseV1{
  repeated GetEncryptedCardResponseV1 cards = 1;
}

message GetOneClickPayOutCardsRequestV1{
  uint64 project_id = 1;
  string project_client_id = 2;
  double amount = 3;
  repeated processing.acquirer.terminal.TerminalProjectV1 activeTerminals = 4;
}

message GetOneClickPayOutCardsResponseV1{
  map<string, string> cards = 1;
}

message GetEncryptedCardRequestV1{
  uint64 card_id = 1;
}

message GetEncryptedCardResponseV1{
  string card_token = 1 [(mvp.FieldLoggerLevel) = Hidden];
  string masked_pan = 2;
  string ips = 3;
  string issuer = 4;
  string year = 5;
  string month = 6;
}

message GetEncryptedPayOutCardResponseV1{
  string card_token = 1 [(mvp.FieldLoggerLevel) = Hidden];
  string masked_pan = 2;
  string ips = 3;
  string issuer = 4;
}

message GetPanByCardIdRequestV1{
  uint64 card_id = 1;
}
message GetPanByHashedIdRequestV1{
  string encrypted_key = 1 [(mvp.FieldLoggerLevel) = Hidden];
}

message GetPanResponseV1{
  string masked_pan = 1;
  uint64 ips_id = 2;
  uint64 country_id = 3;
  uint64 issuer_id = 4;
  uint64 card_id = 5;
  bytes pan = 6 [(mvp.FieldLoggerLevel) = Hidden];
}

message GetClientListByVerificationRequestV1{
  google.protobuf.Timestamp client_created_at_from = 1;
  google.protobuf.Timestamp client_created_at_to = 2;
  uint64 verification_user_id = 3;
}

message GetClientListByProjectClientRequestV1 {
  repeated ProjectAndClientData data = 1;
}

message ProjectAndClientData {
  uint64 project_id = 1;
  string project_client_id = 2;
}

message GetClientListByProjectClientResponseV1 {
  repeated GetClientListData data = 1;
}

message GetClientListData {
  uint64 client_id = 1;
  uint64 project_id = 2;
  string project_client_id = 3;
  uint64 verification_user_id = 4;
}

message CheckClientActivenessRequestV1 {
  uint64 project_id = 1;
  string project_client_id = 2;
}

message GetCardByPanRequestV1 {
  uint64 project_id = 1;
  string project_client_id = 2;
  bytes pan = 3 [(mvp.FieldLoggerLevel) = Hidden];
}

message GetCardByPanResponseV1 {
  uint64 card_id = 1;
}

message DecryptPayInRequest {
  bytes encrypted_card = 1;
}

message DecryptPayOutRequest {
  bytes encrypted_card = 1;
}

message ReEncryptCardRequest {
  bytes pan = 1 [(mvp.FieldLoggerLevel) = Hidden];
  bytes exp_month = 2;
  bytes exp_year = 3;
  bytes cvc = 4 [(mvp.FieldLoggerLevel) = Hidden];
  bytes full_name = 5;
}

message DecryptPayInResponse {
  bytes pan = 1 [(mvp.FieldLoggerLevel) = Hidden];
  bytes exp_month = 2;
  bytes exp_year = 3;
  bytes cvc = 4 [(mvp.FieldLoggerLevel) = Hidden];
  bytes full_name = 5;
}

message DecryptPayOutResponse {
  bytes pan = 1 [(mvp.FieldLoggerLevel) = Hidden];
}

message ReEncryptCardResponse {
  bytes pan = 1 [(mvp.FieldLoggerLevel) = Hidden];
  bytes exp_month = 2;
  bytes exp_year = 3;
  bytes cvc = 4 [(mvp.FieldLoggerLevel) = Hidden];
  bytes full_name = 5;
}

message GetPanInfoByProjectIdRequest{
  bytes pan = 1 [(mvp.FieldLoggerLevel) = Hidden];
  uint64 project_id = 2;
}

message GetPanInfoByProjectIdResponse{
  uint64 ips_id = 1;
  uint64 country_id = 2;
  uint64 issuer_id = 3;
  string masked_pan = 4;
}