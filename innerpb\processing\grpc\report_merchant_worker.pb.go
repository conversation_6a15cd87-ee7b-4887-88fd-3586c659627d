// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.5
// 	protoc        v5.29.3
// source: inner/processing/grpc/report_merchant_worker.proto

package grpc

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type ReportSchedulePeriodType int32

const (
	ReportSchedulePeriodType_ReportSchedulePeriodTypeUnknown ReportSchedulePeriodType = 0
	ReportSchedulePeriodType_ReportSchedulePeriodTypeDaily   ReportSchedulePeriodType = 1
	ReportSchedulePeriodType_ReportSchedulePeriodTypeWeekly  ReportSchedulePeriodType = 2
	ReportSchedulePeriodType_ReportSchedulePeriodTypeMonthly ReportSchedulePeriodType = 3
)

// Enum value maps for ReportSchedulePeriodType.
var (
	ReportSchedulePeriodType_name = map[int32]string{
		0: "ReportSchedulePeriodTypeUnknown",
		1: "ReportSchedulePeriodTypeDaily",
		2: "ReportSchedulePeriodTypeWeekly",
		3: "ReportSchedulePeriodTypeMonthly",
	}
	ReportSchedulePeriodType_value = map[string]int32{
		"ReportSchedulePeriodTypeUnknown": 0,
		"ReportSchedulePeriodTypeDaily":   1,
		"ReportSchedulePeriodTypeWeekly":  2,
		"ReportSchedulePeriodTypeMonthly": 3,
	}
)

func (x ReportSchedulePeriodType) Enum() *ReportSchedulePeriodType {
	p := new(ReportSchedulePeriodType)
	*p = x
	return p
}

func (x ReportSchedulePeriodType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ReportSchedulePeriodType) Descriptor() protoreflect.EnumDescriptor {
	return file_inner_processing_grpc_report_merchant_worker_proto_enumTypes[0].Descriptor()
}

func (ReportSchedulePeriodType) Type() protoreflect.EnumType {
	return &file_inner_processing_grpc_report_merchant_worker_proto_enumTypes[0]
}

func (x ReportSchedulePeriodType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ReportSchedulePeriodType.Descriptor instead.
func (ReportSchedulePeriodType) EnumDescriptor() ([]byte, []int) {
	return file_inner_processing_grpc_report_merchant_worker_proto_rawDescGZIP(), []int{0}
}

type ProcessReportScheduleByPeriodTypeRequest struct {
	state                    protoimpl.MessageState    `protogen:"open.v1"`
	ReportSchedulePeriodType *ReportSchedulePeriodType `protobuf:"varint,1,opt,name=report_schedule_period_type,json=reportSchedulePeriodType,enum=processing.report_merchant.report_merchant_worker.ReportSchedulePeriodType" json:"report_schedule_period_type,omitempty"`
	unknownFields            protoimpl.UnknownFields
	sizeCache                protoimpl.SizeCache
}

func (x *ProcessReportScheduleByPeriodTypeRequest) Reset() {
	*x = ProcessReportScheduleByPeriodTypeRequest{}
	mi := &file_inner_processing_grpc_report_merchant_worker_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ProcessReportScheduleByPeriodTypeRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ProcessReportScheduleByPeriodTypeRequest) ProtoMessage() {}

func (x *ProcessReportScheduleByPeriodTypeRequest) ProtoReflect() protoreflect.Message {
	mi := &file_inner_processing_grpc_report_merchant_worker_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ProcessReportScheduleByPeriodTypeRequest.ProtoReflect.Descriptor instead.
func (*ProcessReportScheduleByPeriodTypeRequest) Descriptor() ([]byte, []int) {
	return file_inner_processing_grpc_report_merchant_worker_proto_rawDescGZIP(), []int{0}
}

func (x *ProcessReportScheduleByPeriodTypeRequest) GetReportSchedulePeriodType() ReportSchedulePeriodType {
	if x != nil && x.ReportSchedulePeriodType != nil {
		return *x.ReportSchedulePeriodType
	}
	return ReportSchedulePeriodType_ReportSchedulePeriodTypeUnknown
}

var File_inner_processing_grpc_report_merchant_worker_proto protoreflect.FileDescriptor

var file_inner_processing_grpc_report_merchant_worker_proto_rawDesc = string([]byte{
	0x0a, 0x32, 0x69, 0x6e, 0x6e, 0x65, 0x72, 0x2f, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69,
	0x6e, 0x67, 0x2f, 0x67, 0x72, 0x70, 0x63, 0x2f, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x5f, 0x6d,
	0x65, 0x72, 0x63, 0x68, 0x61, 0x6e, 0x74, 0x5f, 0x77, 0x6f, 0x72, 0x6b, 0x65, 0x72, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x12, 0x31, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67,
	0x2e, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x5f, 0x6d, 0x65, 0x72, 0x63, 0x68, 0x61, 0x6e, 0x74,
	0x2e, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x5f, 0x6d, 0x65, 0x72, 0x63, 0x68, 0x61, 0x6e, 0x74,
	0x5f, 0x77, 0x6f, 0x72, 0x6b, 0x65, 0x72, 0x1a, 0x1b, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x65, 0x6d, 0x70, 0x74, 0x79, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x22, 0xb7, 0x01, 0x0a, 0x28, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73,
	0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x53, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x42, 0x79,
	0x50, 0x65, 0x72, 0x69, 0x6f, 0x64, 0x54, 0x79, 0x70, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x12, 0x8a, 0x01, 0x0a, 0x1b, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x5f, 0x73, 0x63, 0x68,
	0x65, 0x64, 0x75, 0x6c, 0x65, 0x5f, 0x70, 0x65, 0x72, 0x69, 0x6f, 0x64, 0x5f, 0x74, 0x79, 0x70,
	0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x4b, 0x2e, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73,
	0x73, 0x69, 0x6e, 0x67, 0x2e, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x5f, 0x6d, 0x65, 0x72, 0x63,
	0x68, 0x61, 0x6e, 0x74, 0x2e, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x5f, 0x6d, 0x65, 0x72, 0x63,
	0x68, 0x61, 0x6e, 0x74, 0x5f, 0x77, 0x6f, 0x72, 0x6b, 0x65, 0x72, 0x2e, 0x52, 0x65, 0x70, 0x6f,
	0x72, 0x74, 0x53, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x50, 0x65, 0x72, 0x69, 0x6f, 0x64,
	0x54, 0x79, 0x70, 0x65, 0x52, 0x18, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x53, 0x63, 0x68, 0x65,
	0x64, 0x75, 0x6c, 0x65, 0x50, 0x65, 0x72, 0x69, 0x6f, 0x64, 0x54, 0x79, 0x70, 0x65, 0x2a, 0xab,
	0x01, 0x0a, 0x18, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x53, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c,
	0x65, 0x50, 0x65, 0x72, 0x69, 0x6f, 0x64, 0x54, 0x79, 0x70, 0x65, 0x12, 0x23, 0x0a, 0x1f, 0x52,
	0x65, 0x70, 0x6f, 0x72, 0x74, 0x53, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x50, 0x65, 0x72,
	0x69, 0x6f, 0x64, 0x54, 0x79, 0x70, 0x65, 0x55, 0x6e, 0x6b, 0x6e, 0x6f, 0x77, 0x6e, 0x10, 0x00,
	0x12, 0x21, 0x0a, 0x1d, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x53, 0x63, 0x68, 0x65, 0x64, 0x75,
	0x6c, 0x65, 0x50, 0x65, 0x72, 0x69, 0x6f, 0x64, 0x54, 0x79, 0x70, 0x65, 0x44, 0x61, 0x69, 0x6c,
	0x79, 0x10, 0x01, 0x12, 0x22, 0x0a, 0x1e, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x53, 0x63, 0x68,
	0x65, 0x64, 0x75, 0x6c, 0x65, 0x50, 0x65, 0x72, 0x69, 0x6f, 0x64, 0x54, 0x79, 0x70, 0x65, 0x57,
	0x65, 0x65, 0x6b, 0x6c, 0x79, 0x10, 0x02, 0x12, 0x23, 0x0a, 0x1f, 0x52, 0x65, 0x70, 0x6f, 0x72,
	0x74, 0x53, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x50, 0x65, 0x72, 0x69, 0x6f, 0x64, 0x54,
	0x79, 0x70, 0x65, 0x4d, 0x6f, 0x6e, 0x74, 0x68, 0x6c, 0x79, 0x10, 0x03, 0x32, 0xb3, 0x01, 0x0a,
	0x14, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x4d, 0x65, 0x72, 0x63, 0x68, 0x61, 0x6e, 0x74, 0x57,
	0x6f, 0x72, 0x6b, 0x65, 0x72, 0x12, 0x9a, 0x01, 0x0a, 0x21, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73,
	0x73, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x53, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x42,
	0x79, 0x50, 0x65, 0x72, 0x69, 0x6f, 0x64, 0x54, 0x79, 0x70, 0x65, 0x12, 0x5b, 0x2e, 0x70, 0x72,
	0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x2e, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x5f,
	0x6d, 0x65, 0x72, 0x63, 0x68, 0x61, 0x6e, 0x74, 0x2e, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x5f,
	0x6d, 0x65, 0x72, 0x63, 0x68, 0x61, 0x6e, 0x74, 0x5f, 0x77, 0x6f, 0x72, 0x6b, 0x65, 0x72, 0x2e,
	0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x53, 0x63, 0x68,
	0x65, 0x64, 0x75, 0x6c, 0x65, 0x42, 0x79, 0x50, 0x65, 0x72, 0x69, 0x6f, 0x64, 0x54, 0x79, 0x70,
	0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x16, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79,
	0x22, 0x00, 0x42, 0x2d, 0x5a, 0x2b, 0x67, 0x69, 0x74, 0x2e, 0x6c, 0x6f, 0x63, 0x61, 0x6c, 0x2f,
	0x73, 0x65, 0x6e, 0x73, 0x69, 0x74, 0x69, 0x76, 0x65, 0x2f, 0x69, 0x6e, 0x6e, 0x65, 0x72, 0x70,
	0x62, 0x2f, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x2f, 0x67, 0x72, 0x70,
	0x63, 0x62, 0x08, 0x65, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x70, 0xe8, 0x07,
})

var (
	file_inner_processing_grpc_report_merchant_worker_proto_rawDescOnce sync.Once
	file_inner_processing_grpc_report_merchant_worker_proto_rawDescData []byte
)

func file_inner_processing_grpc_report_merchant_worker_proto_rawDescGZIP() []byte {
	file_inner_processing_grpc_report_merchant_worker_proto_rawDescOnce.Do(func() {
		file_inner_processing_grpc_report_merchant_worker_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_inner_processing_grpc_report_merchant_worker_proto_rawDesc), len(file_inner_processing_grpc_report_merchant_worker_proto_rawDesc)))
	})
	return file_inner_processing_grpc_report_merchant_worker_proto_rawDescData
}

var file_inner_processing_grpc_report_merchant_worker_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_inner_processing_grpc_report_merchant_worker_proto_msgTypes = make([]protoimpl.MessageInfo, 1)
var file_inner_processing_grpc_report_merchant_worker_proto_goTypes = []any{
	(ReportSchedulePeriodType)(0),                    // 0: processing.report_merchant.report_merchant_worker.ReportSchedulePeriodType
	(*ProcessReportScheduleByPeriodTypeRequest)(nil), // 1: processing.report_merchant.report_merchant_worker.ProcessReportScheduleByPeriodTypeRequest
	(*emptypb.Empty)(nil),                            // 2: google.protobuf.Empty
}
var file_inner_processing_grpc_report_merchant_worker_proto_depIdxs = []int32{
	0, // 0: processing.report_merchant.report_merchant_worker.ProcessReportScheduleByPeriodTypeRequest.report_schedule_period_type:type_name -> processing.report_merchant.report_merchant_worker.ReportSchedulePeriodType
	1, // 1: processing.report_merchant.report_merchant_worker.ReportMerchantWorker.ProcessReportScheduleByPeriodType:input_type -> processing.report_merchant.report_merchant_worker.ProcessReportScheduleByPeriodTypeRequest
	2, // 2: processing.report_merchant.report_merchant_worker.ReportMerchantWorker.ProcessReportScheduleByPeriodType:output_type -> google.protobuf.Empty
	2, // [2:3] is the sub-list for method output_type
	1, // [1:2] is the sub-list for method input_type
	1, // [1:1] is the sub-list for extension type_name
	1, // [1:1] is the sub-list for extension extendee
	0, // [0:1] is the sub-list for field type_name
}

func init() { file_inner_processing_grpc_report_merchant_worker_proto_init() }
func file_inner_processing_grpc_report_merchant_worker_proto_init() {
	if File_inner_processing_grpc_report_merchant_worker_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_inner_processing_grpc_report_merchant_worker_proto_rawDesc), len(file_inner_processing_grpc_report_merchant_worker_proto_rawDesc)),
			NumEnums:      1,
			NumMessages:   1,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_inner_processing_grpc_report_merchant_worker_proto_goTypes,
		DependencyIndexes: file_inner_processing_grpc_report_merchant_worker_proto_depIdxs,
		EnumInfos:         file_inner_processing_grpc_report_merchant_worker_proto_enumTypes,
		MessageInfos:      file_inner_processing_grpc_report_merchant_worker_proto_msgTypes,
	}.Build()
	File_inner_processing_grpc_report_merchant_worker_proto = out.File
	file_inner_processing_grpc_report_merchant_worker_proto_goTypes = nil
	file_inner_processing_grpc_report_merchant_worker_proto_depIdxs = nil
}
