package main

import (
	"fmt"
	"git.local/sensitive/mvp/pb"
	"google.golang.org/protobuf/compiler/protogen"
	"google.golang.org/protobuf/proto"
	"strings"
)

func init() {
	RegisterGenerator(&CobraGenerator{})
}

type CobraGenerator struct{}

func (generator *CobraGenerator) ProtoGenGenerate(gen *protogen.Plugin) {
	for _, file := range gen.Files {
		if !file.Generate {
			continue
		}

		g := gen.NewGeneratedFile(
			file.GeneratedFilenamePrefix+"_cobra.go",
			file.GoImportPath,
		)

		HeaderPrint(g, string(file.GoPackageName))

		g.P("import (")
		g.P("sentrygin \"github.com/getsentry/sentry-go/gin\"")
		g.P("otelgin \"go.opentelemetry.io/contrib/instrumentation/github.com/gin-gonic/gin/otelgin\"")

		g.P(")")
		g.P()

		for _, service := range file.Services {
			opts := service.Desc.Options()
			cfg, ok := proto.GetExtension(opts, pb.E_CobraConfig).(*pb.Configuration)
			if !ok || cfg == nil {
				continue
			}

			packagePath := cfg.GetPackage()
			components := cfg.GetComponents()

			var hasGinEngine, hasSwagger, hasRabbitMQ, hasStand, hasHL, hasMongo, hasGorm, hasGtxManager,
				hasBackoff, hasSocketHub, hasRedis, hasJSContext, hasNatsConn, hasNatsMessageRouter, hasCSRF bool

			for _, component := range components {
				switch component {
				case pb.InitComponents_GinEngineInit:
					hasGinEngine = true
				case pb.InitComponents_JetStreamContext:
					hasJSContext = true
				case pb.InitComponents_NatsConn:
					hasNatsConn = true
				case pb.InitComponents_NatsMessageRouter:
					hasNatsMessageRouter = true
				case pb.InitComponents_Swagger:
					hasSwagger = true
				case pb.InitComponents_RabbitMQInit:
					hasRabbitMQ = true
				case pb.InitComponents_Stand:
					hasStand = true
				case pb.InitComponents_HttpLogger:
					hasHL = true
				case pb.InitComponents_BackoffPolicy:
					hasBackoff = true
				case pb.InitComponents_SocketHub:
					hasSocketHub = true
				case pb.InitComponents_MongoInit:
					hasMongo = true
				case pb.InitComponents_RedisInit:
					hasRedis = true
				case pb.InitComponents_GormInit:
					hasGorm = true
				case pb.InitComponents_GTransactionManager:
					hasGtxManager = true
				case pb.InitComponents_CSRF:
					hasCSRF = true

				}
			}

			funcName := cfg.GetFuncName()

			// Configs
			g.P("var isInit", funcName, " bool")
			g.P()

			printSensitiveConfig(g, funcName)

			g.P("type config", funcName, " struct {")

			if hasBackoff {
				g.P("BACKOFF_INITIAL_INTERVAL string `json:\"BACKOFF_INITIAL_INTERVAL\"`")
				g.P("BACKOFF_MULTIPLIER string `json:\"BACKOFF_MULTIPLIER\"`")
				g.P("BACKOFF_MAX_INTERVAL string `json:\"BACKOFF_MAX_INTERVAL\"`")
				g.P("BACKOFF_MAX_ELAPSED_TIME string `json:\"BACKOFF_MAX_ELAPSED_TIME\"`")
				g.P("BACKOFF_RANDOMIZATION_FACTOR string `json:\"BACKOFF_RANDOMIZATION_FACTOR\"`")
			}

			if hasHL && hasMongo {
				g.P("MONGO_COLLECTION string `json:\"MONGO_COLLECTION\"`")
				g.P("MONGO_LOGS_STORAGE string `json:\"MONGO_LOGS_STORAGE\"`")
			}

			if hasRabbitMQ {
				g.P("RABBITMQ_USER string `json:\"RABBITMQ_USER\"`")
				g.P("RABBITMQ_PASSWORD string `json:\"RABBITMQ_PASSWORD\"`")
				g.P("RABBITMQ_HOST string `json:\"RABBITMQ_HOST\"`")
				g.P("RABBITMQ_PORT string `json:\"RABBITMQ_PORT\"`")
				g.P("RABBITMQ_VHOST string `json:\"RABBITMQ_VHOST\"`")
				g.P("PREFETCH_MESSAGES_COUNT int `json:\"PREFETCH_MESSAGES_COUNT\"`")
				g.P("MESSAGE_QUEUE string `json:\"MESSAGE_QUEUE\"`")
			}

			if hasCSRF {
				g.P("REDIS_DSN string `json:\"REDIS_DSN\"`")
				g.P("REDIS_PASSWORD string `json:\"REDIS_PASSWORD\"`")
				g.P("SESSION_KEY string `json:\"SESSION_KEY\"`")
				g.P("SESSION_SECRET string `json:\"SESSION_SECRET\"`")
			}

			if hasSwagger {
				g.P("DOMAIN string `json:\"DOMAIN\"`")
			}

			configFields := make(map[string]*pb.VaultConfig)
			if cfg.GetRestHandler() != nil {
				g.P("HTTP_PORT string `json:\"HTTP_PORT\"`")
				for _, handler := range cfg.GetRestHandler() {
					collectVaultConfigs(handler, configFields)
				}
			}

			if cfg.GetGrpcHandler() != nil {
				g.P("GRPC_PORT string `json:\"GRPC_PORT\"`")
				for _, handler := range cfg.GetGrpcHandler() {
					collectVaultConfigs(handler, configFields)
				}
			}

			for _, vaultCfg := range configFields {
				switch vaultCfg.GetType() {
				case pb.VaultConfigTypes_String:
					g.P(vaultCfg.GetKey(), " string `json:\"", vaultCfg.GetKey(), "\"`")
				case pb.VaultConfigTypes_Int:
					g.P(vaultCfg.GetKey(), " int `json:\"", vaultCfg.GetKey(), "\"`")
				case pb.VaultConfigTypes_Float:
					g.P(vaultCfg.GetKey(), " float64 `json:\"", vaultCfg.GetKey(), "\"`")
				case pb.VaultConfigTypes_Bool:
					g.P(vaultCfg.GetKey(), " bool `json:\"", vaultCfg.GetKey(), "\"`")
				}
			}

			g.P("}")
			g.P()

			g.P("func (c *config", funcName, ") Check() {")

			if hasBackoff {
				g.P("if c.BACKOFF_INITIAL_INTERVAL == \"0\" {")
				g.P("panic(\"empty BACKOFF_INITIAL_INTERVAL\")")
				g.P("}")
				g.P("if c.BACKOFF_MULTIPLIER == \"0\" {")
				g.P("panic(\"empty BACKOFF_MULTIPLIER\")")
				g.P("}")
				g.P("if c.BACKOFF_MAX_INTERVAL == \"0\" {")
				g.P("panic(\"empty BACKOFF_MAX_INTERVAL\")")
				g.P("}")
				g.P("if c.BACKOFF_MAX_ELAPSED_TIME == \"0\" {")
				g.P("panic(\"empty BACKOFF_MAX_ELAPSED_TIME\")")
				g.P("}")
				g.P("if c.BACKOFF_RANDOMIZATION_FACTOR == \"0\" {")
				g.P("panic(\"empty BACKOFF_RANDOMIZATION_FACTOR\")")
				g.P("}")
			}

			if hasHL && hasMongo {
				g.P("if c.MONGO_COLLECTION == \"\" {")
				g.P("panic(\"empty MONGO_COLLECTION\")")
				g.P("}")
				// FIXME: Привести все сервисы к испольованию единой переменной
				//g.P("if c.MONGO_LOGS_STORAGE == \"\" {")
				//g.P("panic(\"empty MONGO_LOGS_STORAGE\")")
				//g.P("}")
			}

			if hasCSRF {
				g.P("if c.REDIS_DSN == \"\" {")
				g.P("panic(\"empty REDIS_DSN\")")
				g.P("}")
				g.P("if c.REDIS_PASSWORD == \"\" {")
				g.P("panic(\"empty REDIS_PASSWORD\")")
				g.P("}")
				g.P("if c.SESSION_KEY == \"\" {")
				g.P("panic(\"empty SESSION_KEY\")")
				g.P("}")
				g.P("if c.SESSION_SECRET == \"\" {")
				g.P("panic(\"empty SESSION_SECRET\")")
				g.P("}")
			}

			if cfg.GetRestHandler() != nil {
				g.P("if c.HTTP_PORT == \"\" {")
				g.P("panic(\"empty HTTP_PORT\")")
				g.P("}")

				for _, handler := range cfg.GetRestHandler() {
					collectVaultConfigs(handler, configFields)
				}
			}

			if cfg.GrpcHandler != nil {
				g.P("if c.GRPC_PORT == \"\" {")
				g.P("panic(\"empty GRPC_PORT\")")
				g.P("}")
				for _, handler := range cfg.GetGrpcHandler() {
					collectVaultConfigs(handler, configFields)
				}
			}

			for _, vaultCfg := range configFields {
				switch vaultCfg.GetType() {
				case pb.VaultConfigTypes_String:
					g.P("if c.", vaultCfg.GetKey(), " == \"\" {")
				case pb.VaultConfigTypes_Int:
					g.P("if c.", vaultCfg.GetKey(), " == 0 {")
				case pb.VaultConfigTypes_Float:
					g.P("if c.", vaultCfg.GetKey(), " == 0 {")
				case pb.VaultConfigTypes_Bool:
					g.P("if c.", vaultCfg.GetKey(), " == false {")
				}

				g.P("panic(\"empty ", vaultCfg.GetKey(), "\")")
				g.P("}")
			}

			g.P("}")
			g.P()

			// -------------------------------- RUN --------------------------------

			g.P("func Run", funcName, "(cmd *", cobraPkgCommand, ", args []string) {")
			g.P("defer func() {")
			g.P("if err, hub := recover(), ", sentryCurrentHub, "(); err != nil && hub != nil {")
			g.P("hub.Recover(err)")
			g.P(sentryGetHubFromFlush, "(", SentryFlushTimeout, ")")
			g.P("panic(err)")
			g.P("}")
			g.P("}()")
			g.P()

			// Component initialization
			componentVarMap := map[pb.InitComponents]string{
				pb.InitComponents_GormInit:         "gormDB",
				pb.InitComponents_MongoInit:        "mongoClient",
				pb.InitComponents_RedisInit:        "redisClient",
				pb.InitComponents_GinEngineInit:    "ginEngine",
				pb.InitComponents_JetStreamContext: "jetStreamContext",
				pb.InitComponents_NatsConn:         "natsConn",
				pb.InitComponents_NatsInit:         "natsMessageRouter",
				pb.InitComponents_Stand:            "stand",
			}

			var varNames []string
			for _, component := range components {
				if varName, ok := componentVarMap[component]; ok {
					varNames = append(varNames, varName)
				}
			}

			if hasGorm {
				varNames = append(varNames, "healthCheckStore")
			}
			if configFields != nil {
				varNames = append(varNames, "config")
			}
			if cfg.GetRestHandler() != nil {
				varNames = append(varNames, "microservice")
			}
			if cfg.GetGrpcHandler() != nil {
				varNames = append(varNames, "grpcServer")
			}

			args := fmt.Sprintf(`"processing.%s"`, strings.Join(cfg.GetServiceNames(), "."))
			if len(varNames) > 0 {
				g.P("ctx, cancel, ", strings.Join(varNames, ", "), " := Init", cfg.GetFuncName(), "(", args, ")")
			} else {
				g.P("ctx, cancel := Init", cfg.GetFuncName(), "(", args, ")")

				g.P(cfg.GetFuncName(), "(", args, ")")
			}
			g.P()

			g.P("logger := zap.L()")
			g.P()

			// Health check and Swagger
			if hasGorm {
				g.P("logger.Info(\"begin register db healthCheck\")")
				g.P("registerDBHealthCheck", funcName, "(gormDB, healthCheckStore)")
				g.P("logger.Info(\"end register db healthCheck\")")
				g.P()
			}

			if hasSwagger {
				docsPackage := protogen.GoImportPath(packagePath + "/docs")
				docsPackageSwaggerInfo := docsPackage.Ident("SwaggerInfo")

				g.P("logger.Info(\"begin send swagger doc\")")

				g.P("err := initOpenAPI", funcName, "(", docsPackageSwaggerInfo, ",")
				g.P("config,")
				g.P("stand,")
				g.P("microservice,")
				g.P(")")

				g.P("if err != nil {")
				g.P("logger.Warn(\"error send swagger doc\", ", zapError, "(err))")
				g.P("}")
				g.P("logger.Info(\"end send swagger doc\")")
				g.P()
			}

			// HTTP Logger
			if hasHL && hasMongo {
				hlPackage := protogen.GoImportPath("git.local/sensitive/pkg/http_logger")
				g.P("hl := ", hlPackage.Ident("NewHttpLogMongo"),
					"(", componentVarMap[pb.InitComponents_MongoInit], ".Database(microservice), config.MONGO_LOGS_STORAGE)")
				g.P()
			}

			// Vault configs
			cfgTypes := map[pb.VaultConfigTypes]string{
				pb.VaultConfigTypes_String: "String",
				pb.VaultConfigTypes_Int:    "Int",
				pb.VaultConfigTypes_Float:  "Float",
				pb.VaultConfigTypes_Bool:   "Bool",
			}

			if hasBackoff {
				g.P("b := ", backoffPkg.Ident("NewExponentialBackOff"), "()")
				g.P("")
				g.P("b.InitialInterval = ", timeDuration, "(config.BACKOFF_INITIAL_INTERVAL) * ", timePackageSecond)
				g.P("b.Multiplier = config.BACKOFF_MULTIPLIER")
				g.P("b.MaxInterval = ", timeDuration, "(config.BACKOFF_MAX_INTERVAL) * ", timePackageSecond)
				g.P("b.MaxElapsedTime = ", timeDuration, "(config.BACKOFF_MAX_ELAPSED_TIME) * ", timePackageSecond)
				g.P("b.RandomizationFactor = config.BACKOFF_RANDOMIZATION_FACTOR")
				g.P()
			}

			if hasGtxManager && hasGorm {
				gtxManagerPkg := protogen.GoImportPath("git.local/sensitive/pkg/gtransaction")
				g.P("gtxManager := ", gtxManagerPkg.Ident("NewGormTransactionManager"), "(gormDB)")
			}

			fullComponentMap := map[pb.InitComponents]string{
				pb.InitComponents_GormInit:            "gormDB",
				pb.InitComponents_MongoInit:           "mongoClient",
				pb.InitComponents_RedisInit:           "redisClient",
				pb.InitComponents_GinEngineInit:       "ginEngine",
				pb.InitComponents_BackoffPolicy:       "b",
				pb.InitComponents_HttpLogger:          "hl",
				pb.InitComponents_GTransactionManager: "gtxManager",
				pb.InitComponents_NatsConn:            "natsConn",
				pb.InitComponents_JetStreamContext:    "jetStreamContext",
			}

			// Cache for repo, services and usecase
			repoCache := make(map[string]string)
			clientCache := make(map[string]string)
			serviceCache := make(map[string]string)
			useCaseCache := make(map[string]string)

			// grpcHandler processing
			for _, grpcHandler := range cfg.GetGrpcHandler() {
				for _, layer := range grpcHandler.GetLayers() {
					switch layer.GetType() {
					case pb.TypeLayers_ServiceLayer:
						generateServiceLayer(g, layer, packagePath, fullComponentMap, cfgTypes, repoCache, clientCache, serviceCache)
					case pb.TypeLayers_UseCaseLayer:
						generateUseCaseLayer(g, layer, packagePath, fullComponentMap, cfgTypes, repoCache, clientCache, serviceCache, useCaseCache)
					}
				}
			}

			// restHandler processing
			for _, restHandler := range cfg.GetRestHandler() {
				for _, layer := range restHandler.GetLayers() {
					switch layer.GetType() {
					case pb.TypeLayers_ServiceLayer:
						generateServiceLayer(g, layer, packagePath, fullComponentMap, cfgTypes, repoCache, clientCache, serviceCache)
					case pb.TypeLayers_UseCaseLayer:
						generateUseCaseLayer(g, layer, packagePath, fullComponentMap, cfgTypes, repoCache, clientCache, serviceCache, useCaseCache)

					}
				}
			}

			// natsConsumer processing
			for _, natsConsumer := range cfg.GetNatsConsumer() {
				for _, layer := range natsConsumer.GetLayers() {
					switch layer.GetType() {
					case pb.TypeLayers_ServiceLayer:
						generateServiceLayer(g, layer, packagePath, fullComponentMap, cfgTypes, repoCache, clientCache, serviceCache)
					case pb.TypeLayers_UseCaseLayer:
						generateUseCaseLayer(g, layer, packagePath, fullComponentMap, cfgTypes, repoCache, clientCache, serviceCache, useCaseCache)

					}
				}
			}

			// RabbitMQ
			if hasRabbitMQ && hasStand {
				eventDelivery := protogen.GoImportPath(packagePath + "/delivery/event")

				g.P("uri := fmt.Sprintf(")
				g.P("\"amqp://%s:%s@%s:%s/%s\",")
				g.P("config.RABBITMQ_USER")
				g.P("config.RABBITMQ_PASSWORD")
				g.P("config.RABBITMQ_HOST")
				g.P("config.RABBITMQ_PORT")
				g.P("config.RABBITMQ_VHOST")
				g.P(")")
				g.P()
				g.P("rabbitMQClient, err := ", amqpPkg.Ident("Dial"), "(uri)")
				g.P("if err != nil {")
				g.P("cobra.L().Fatal(\"RabbitMQ_Client\", ", zapError, "(err))")
				g.P("}")
				g.P()
				g.P("eventConsumer, err := ", eventDelivery.Ident("NewHandler"), "(services, rabbitMQClient,")
				g.P("config.PREFETCH_MESSAGES_COUNT, config.MESSAGE_QUEUE")
				g.P("if err != nil { cobra.L().Fatal(err.Error()) }")
				g.P()
				g.P("if stand == \"", STAND_LOCAL, "\" || stand == \"", STAND_PRODUCTION, "\" {")
				g.P("eventConsumer.Run()")
				g.P("}")
				g.P()
			}

			// natsConsumer
			if cfg.GetNatsConsumer() != nil {
				eventPath := protogen.GoImportPath(packagePath + "/delivery/event/consumer")

				hasUseCaseLayer := false
				var handlerArgs []string

				for _, natsConsumer := range cfg.GetNatsConsumer() {
					for _, layer := range natsConsumer.GetLayers() {
						if layer.GetType() == pb.TypeLayers_UseCaseLayer {
							hasUseCaseLayer = true
							handlerArgs = append(handlerArgs, fmt.Sprintf("useCase%s", layer.GetName()))
						}
					}
				}

				if !hasUseCaseLayer {
					for _, natsConsumer := range cfg.GetNatsConsumer() {
						for _, layer := range natsConsumer.GetLayers() {
							if layer.GetType() == pb.TypeLayers_ServiceLayer {
								handlerArgs = append(handlerArgs, fmt.Sprintf("service%s", layer.GetName()))
							}
						}
					}
				}

				g.P()
				g.P("_, cErr := ", eventPath.Ident("NewHandler"), "(")

				for _, arg := range handlerArgs {
					g.P(arg + ",")
				}

				if hasJSContext {
					g.P("jetStreamContext,")
				}

				if hasNatsConn {
					g.P("natsConn,")
				}

				if hasNatsMessageRouter {
					g.P("natsMessageRouter,")
				}

				g.P("ctx,")

				for _, natsConsumer := range cfg.GetNatsConsumer() {
					for _, vault := range natsConsumer.GetVaultConfigs() {
						if _, ok := cfgTypes[vault.GetType()]; ok {
							g.P("config.", vault.GetKey(), ",")
						}
					}
				}

				g.P(")")
				g.P()
				g.P("if cErr != nil {")
				g.P("logger.Panic(\"cannot create event consumer\", ", zapError, "(cErr))")
				g.P("return")
				g.P("}")
				g.P()

				// NATS Message Router
				g.P("go func() {")
				g.P("defer cancel()")
				g.P()
				g.P("logger.Info(\"start nats message engine\")")
				g.P("if err := natsMessageRouter.Run(ctx); err != nil {")
				g.P("logger.Panic(\"can not run nats message engine\", ", zapError, "(err))")
				g.P("}")
				g.P("}()")
				g.P()
			}

			g.P("go func() {")
			g.P("defer cancel()")
			g.P()

			g.P("serverAddr := config.HTTP_PORT")
			g.P("logger.Info(\"serverAdr\", ", zapString, "(\"HTTP_PORT\", serverAddr))")
			g.P()

			// restHandler
			if cfg.GetRestHandler() != nil {
				httpDeliveryPkg := protogen.GoImportPath(packagePath + "/delivery/http")

				hasUseCaseLayer := false
				var handlerArgs []string

				for _, restHandler := range cfg.GetRestHandler() {
					for _, layer := range restHandler.GetLayers() {
						if layer.GetType() == pb.TypeLayers_UseCaseLayer {
							hasUseCaseLayer = true
							handlerArgs = append(handlerArgs, fmt.Sprintf("useCase%s", layer.GetName()))
						}
					}
				}

				if !hasUseCaseLayer {
					for _, restHandler := range cfg.GetRestHandler() {
						for _, layer := range restHandler.GetLayers() {
							if layer.GetType() == pb.TypeLayers_ServiceLayer {
								handlerArgs = append(handlerArgs, fmt.Sprintf("service%s", layer.GetName()))
							}
						}
					}
				}

				g.P("handlerDelivery := ", httpDeliveryPkg.Ident("NewHandlerDelivery"), "(")

				for _, arg := range handlerArgs {
					g.P(arg + ",")
				}

				g.P("microservice,")

				if hasRedis {
					g.P("redisClient,")
				}

				for _, restHandler := range cfg.GetRestHandler() {
					for _, vaultCfg := range restHandler.GetVaultConfigs() {
						if _, ok := cfgTypes[vaultCfg.GetType()]; ok {
							g.P("config.", vaultCfg.GetKey(), ",")
						}
					}
				}

				if hasJSContext {
					g.P("jetStreamContext,")
				}

				g.P(")")
				g.P()

				if hasCSRF {
					redisSessionPkg := protogen.GoImportPath("github.com/gin-contrib/sessions/redis")

					g.P("store, sErr := ", redisSessionPkg.Ident("NewStoreWithDB"), "(")
					g.P("10,")
					g.P("\"tcp\",")
					g.P("config.REDIS_DSN,")
					g.P("config.REDIS_PASSWORD,")
					g.P("\"1\",")
					g.P("[]byte(config.SESSION_KEY),")
					g.P(")")
					g.P("if sErr != nil {")
					g.P("logger.Panic(\"cannot create redis connection\", ", zapError, "(sErr))")
					g.P("return")
					g.P("}")
					g.P()
				}

				g.P("handlerDelivery.Init(")
				if hasGinEngine {
					g.P("ginEngine,")
				}
				if hasRedis {
					g.P("stand,")
					g.P("config.SESSION_SECRET,")
					g.P("store,")
				}
				if hasSocketHub {
					g.P("sh,")
				}

				g.P(")")
				g.P()

			}

			g.P("go func() {")
			g.P("<-", timePackageNewTicker, "(", timePackageSecond, ").C")
			g.P(ginSetMode, "(", ginReleaseMode, ")")
			g.P("}()")
			g.P()

			g.P("if err := ginEngine.Run(\":\" + serverAddr); err != nil {")
			g.P("    logger.Panic(\"can not run gin engine\", ", zapError, "(err))")
			g.P("}")
			g.P("}()")
			g.P()

			// gRPC Server
			if cfg.GetGrpcHandler() != nil {

				g.P("go func() {")
				g.P("defer cancel()")
				g.P()

				grpcDeliveryV1 := protogen.GoImportPath(packagePath + "/delivery/grpc/v1")

				for _, grpcHandler := range cfg.GetGrpcHandler() {

					var handlerArgs []string

					for _, layer := range grpcHandler.GetLayers() {
						switch layer.GetType() {
						case pb.TypeLayers_UseCaseLayer:
							handlerArgs = append(handlerArgs, fmt.Sprintf("useCase%s", layer.GetName()))
						case pb.TypeLayers_ServiceLayer:
							handlerArgs = append(handlerArgs, fmt.Sprintf("service%s", layer.GetName()))
						}
					}

					registered := map[string]bool{}

					serverName := grpcHandler.GetName()

					if registered[serverName] {
						continue
					}
					registered[serverName] = true

					g.P(localGrpc.Ident(fmt.Sprintf("Register%sServer", serverName)), "(grpcServer, ")
					g.P(localGrpc.Ident(fmt.Sprintf("NewLogged%sServer", serverName)), "(")
					g.P(grpcDeliveryV1.Ident(fmt.Sprintf("New%sServer", serverName)), "(")

					for _, arg := range handlerArgs {
						g.P(arg + ",")
					}

					for _, layer := range grpcHandler.GetLayers() {
						if layer.GetType() == pb.TypeLayers_GrpcClient {
							if cliVar, ok := clientCache[layer.GetName()]; ok {
								g.P(cliVar + ",")
							}
						}
					}

					for _, vault := range grpcHandler.GetVaultConfigs() {
						if _, ok := cfgTypes[vault.GetType()]; ok {
							g.P("config.", vault.GetKey(), ",")
						}
					}

					g.P("),")
					g.P("),")
					g.P(")")
					g.P()

				}

				g.P("serverAddr := config.GRPC_PORT")
				g.P("logger.Info(\"serverAdr\", ", zapString, "(\"GRPC_PORT\", serverAddr))")
				g.P()
				g.P("listenerGrpc, err := ", netListen, "(\"tcp\", \":\"+serverAddr)")
				g.P("if err != nil {")
				g.P("logger.Fatal(\"can not prepare net.Listener for grpc service\", ", zapError, "(err))")
				g.P("}")
				g.P()
				g.P(reflectionRegister, "(grpcServer)")
				g.P()
				g.P("if err := grpcServer.Serve(listenerGrpc); err != nil {")
				g.P("logger.Fatal(\"can not run grpc server\", ", zapError, "(err))")
				g.P("}")
				g.P("}()")
				g.P()
			}

			g.P("<-ctx.Done()")
			g.P("}")
			g.P()

			// -------------------------------- INIT APP --------------------------------

			var varTypeMap = map[string]protogen.GoIdent{
				"gormDB":            gormDB,
				"mongoClient":       mongoClient,
				"redisClient":       redisClient,
				"ginEngine":         ginEngine,
				"jetStreamContext":  natsJetStreamContext,
				"natsConn":          natsConn,
				"natsMessageRouter": watermillMessageRouter,
			}

			g.P("func Init", cfg.GetFuncName(), "(appName string) (")

			g.P(contextContext, ",")
			g.P(contextCancelFunc, ",")

			for _, varName := range varNames {
				if varType, ok := getVarType(varName, varTypeMap); ok {
					if varType == natsJetStreamContext {
						g.P(varType, ",")
					} else {
						g.P("*", varType, ",")
					}
				}
			}

			if hasStand {
				g.P("string,")
			}

			if hasGorm {
				g.P("*healthCheckStore", funcName, ",")
			}

			if configFields != nil {
				g.P("config", funcName, ",")
			}

			if cfg.GetRestHandler() != nil {
				g.P("string,")
			}

			if cfg.GetGrpcHandler() != nil {
				g.P("*", grpcPackageServer, ",")
			}

			g.P(") {")

			g.P("if isInit", funcName, " {")
			g.P("panic(\"already init\")")
			g.P("}")
			g.P("isInit", funcName, " = true")
			g.P()

			g.P("stand := ", osGetEnv, "(\"ENVIRONMENT\")")
			g.P()

			printStand(g, "stand", funcName)

			g.P("pass := map[string]any{}")
			g.P("split := ", stringsSplit, "(appName, \".\")")
			g.P("namespace := ", toLower, "(split[0])")
			g.P("microservice := ", toLower, "(split[len(split)-1])")
			g.P()

			g.P("var opts []func(*", vaultConfig, ")")
			g.P("if vaultUrl := ", osGetEnv, `("VAULT_URL"); vaultUrl != "" {`)
			g.P("opts = append(opts, func(v *", vaultConfig, ") {")
			g.P("v.Address = vaultUrl")
			g.P("})")
			g.P("}")
			g.P()

			printVault(g, funcName)
			g.P()

			g.P("var config config", funcName)
			g.P("var sc sensitiveConfig", funcName)
			g.P()
			g.P("b, err := ", jsonPackageMarshal, "(pass)")
			g.P("if err != nil {")
			g.P("panic(err)")
			g.P("}")
			g.P()
			g.P("if err := ", jsonPackageUnmarshal, "(b, &config); err != nil {")
			g.P("panic(err)")
			g.P("}")
			g.P("config.Check()")
			g.P()
			g.P("if err := ", jsonPackageUnmarshal, "(b, &sc); err != nil {")
			g.P("panic(err)")
			g.P("}")
			g.P("sc.Check()")
			g.P()

			g.P("level := sc.LOG_LEVEL")
			g.P("if level == \"\" {")
			g.P("level = \"debug\"")
			g.P("}")
			g.P()

			printLogger(g)
			g.P()

			g.P("postgresReadConnectionsDirty := sc.", POSTGRES_READ_KEY)
			g.P("postgresWriteConnectionsDirty := sc.", POSTGRES_WRITE_KEY)
			g.P()
			g.P("postgresReadConnections := make([]string, 0, len(postgresReadConnectionsDirty))")
			g.P("postgresWriteConnections := make([]string, 0, len(postgresWriteConnectionsDirty))")
			g.P()
			g.P("for i := range postgresReadConnectionsDirty {")
			g.P("postgresReadConnections = append(postgresReadConnections, fmt.Sprint(postgresReadConnectionsDirty[i]))")
			g.P("}")
			g.P()
			g.P("for i := range postgresWriteConnectionsDirty {")
			g.P("postgresWriteConnections = append(postgresWriteConnections, fmt.Sprint(postgresWriteConnectionsDirty[i]))")
			g.P("}")
			g.P()

			printMongo(g)

			printRedis(g)

			printNotifier(g)

			printJaegerTraceProvider(g, funcName)

			printSentry(g)

			printNatsJSClient(g, funcName)

			printHealthCheckStore(g, funcName)

			printPrometheus(g)

			printGin(g, funcName)

			printGrpc(g, funcName)

			// printWorkerRegistry(g)

			printGracefulShutdown(g)

			printPostgresClient(g, funcName)

			if len(varNames) > 0 {
				g.P("return ctx, cancel, ", strings.Join(varNames, ", "))
			}

			g.P("}")

			printZapLoggerAdapter(g, funcName)

			printCustomResponseWriter(g, funcName)

			printHealthCheckStoreFunc(g, funcName)

			if hasSwagger {
				printSwagger(g, funcName)
			}

		}

	}
}

func collectVaultConfigs(layer *pb.LayersConfig, configFields map[string]*pb.VaultConfig) {
	for _, vaultCfg := range layer.GetVaultConfigs() {
		configFields[vaultCfg.GetKey()] = vaultCfg
	}

	for _, subLayer := range layer.GetLayers() {
		collectVaultConfigs(subLayer, configFields)
	}
}

func getVarType(varName string, varTypeMap map[string]protogen.GoIdent) (protogen.GoIdent, bool) {
	if varType, ok := varTypeMap[varName]; ok {
		return varType, true
	}
	return protogen.GoIdent{}, false
}
