// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.5.1
// - protoc             v5.29.3
// source: proto/iam.proto

package pb

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.64.0 or later.
const _ = grpc.SupportPackageIsVersion9

const (
	AccessControl_CheckGrant_FullMethodName = "/mvp.AccessControl/CheckGrant"
	AccessControl_WhoAmi_FullMethodName     = "/mvp.AccessControl/WhoAmi"
)

// AccessControlClient is the client API for AccessControl service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type AccessControlClient interface {
	CheckGrant(ctx context.Context, in *IAM, opts ...grpc.CallOption) (*emptypb.Empty, error)
	WhoAmi(ctx context.Context, in *emptypb.Empty, opts ...grpc.CallOption) (*emptypb.Empty, error)
}

type accessControlClient struct {
	cc grpc.ClientConnInterface
}

func NewAccessControlClient(cc grpc.ClientConnInterface) AccessControlClient {
	return &accessControlClient{cc}
}

func (c *accessControlClient) CheckGrant(ctx context.Context, in *IAM, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, AccessControl_CheckGrant_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *accessControlClient) WhoAmi(ctx context.Context, in *emptypb.Empty, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, AccessControl_WhoAmi_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// AccessControlServer is the server API for AccessControl service.
// All implementations must embed UnimplementedAccessControlServer
// for forward compatibility.
type AccessControlServer interface {
	CheckGrant(context.Context, *IAM) (*emptypb.Empty, error)
	WhoAmi(context.Context, *emptypb.Empty) (*emptypb.Empty, error)
	mustEmbedUnimplementedAccessControlServer()
}

// UnimplementedAccessControlServer must be embedded to have
// forward compatible implementations.
//
// NOTE: this should be embedded by value instead of pointer to avoid a nil
// pointer dereference when methods are called.
type UnimplementedAccessControlServer struct{}

func (UnimplementedAccessControlServer) CheckGrant(context.Context, *IAM) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CheckGrant not implemented")
}
func (UnimplementedAccessControlServer) WhoAmi(context.Context, *emptypb.Empty) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method WhoAmi not implemented")
}
func (UnimplementedAccessControlServer) mustEmbedUnimplementedAccessControlServer() {}
func (UnimplementedAccessControlServer) testEmbeddedByValue()                       {}

// UnsafeAccessControlServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to AccessControlServer will
// result in compilation errors.
type UnsafeAccessControlServer interface {
	mustEmbedUnimplementedAccessControlServer()
}

func RegisterAccessControlServer(s grpc.ServiceRegistrar, srv AccessControlServer) {
	// If the following call pancis, it indicates UnimplementedAccessControlServer was
	// embedded by pointer and is nil.  This will cause panics if an
	// unimplemented method is ever invoked, so we test this at initialization
	// time to prevent it from happening at runtime later due to I/O.
	if t, ok := srv.(interface{ testEmbeddedByValue() }); ok {
		t.testEmbeddedByValue()
	}
	s.RegisterService(&AccessControl_ServiceDesc, srv)
}

func _AccessControl_CheckGrant_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(IAM)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AccessControlServer).CheckGrant(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: AccessControl_CheckGrant_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AccessControlServer).CheckGrant(ctx, req.(*IAM))
	}
	return interceptor(ctx, in, info, handler)
}

func _AccessControl_WhoAmi_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(emptypb.Empty)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AccessControlServer).WhoAmi(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: AccessControl_WhoAmi_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AccessControlServer).WhoAmi(ctx, req.(*emptypb.Empty))
	}
	return interceptor(ctx, in, info, handler)
}

// AccessControl_ServiceDesc is the grpc.ServiceDesc for AccessControl service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var AccessControl_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "mvp.AccessControl",
	HandlerType: (*AccessControlServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "CheckGrant",
			Handler:    _AccessControl_CheckGrant_Handler,
		},
		{
			MethodName: "WhoAmi",
			Handler:    _AccessControl_WhoAmi_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "proto/iam.proto",
}
