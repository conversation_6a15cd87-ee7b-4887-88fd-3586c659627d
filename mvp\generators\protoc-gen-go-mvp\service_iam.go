package main

import (
	"sort"

	"google.golang.org/protobuf/compiler/protogen"
	"google.golang.org/protobuf/proto"
	"google.golang.org/protobuf/reflect/protoreflect"

	"git.local/sensitive/mvp/pb"
)

func init() {
	RegisterGenerator(&IamGenerator{})
}

type IamGenerator struct{}

func (generator *IamGenerator) ProtoGenGenerate(gen *protogen.Plugin) {
	var (
		roles                *protogen.Enum
		permissions          *protogen.Enum
		accessControlService *protogen.Service
	)

	for _, file := range gen.Files {
		if file.GoDescriptorIdent.GoName != "File_mvp_proto_iam_proto" {
			continue
		}

		for _, enum := range file.Enums {
			if enum.GoIdent.GoName == "Role" {
				roles = enum
			}

			if enum.GoIdent.GoName == "Permission" {
				permissions = enum
			}
		}

		for _, service := range file.Services {
			if service.Desc.Name() == "AccessControl" {
				accessControlService = service
				break
			}
		}
	}

	var (
		accessControlIdent    protogen.GoIdent
		accessControlIAMIdent protogen.GoIdent

		roleMap       = map[pb.Role]*protogen.EnumValue{}
		permissionMap = map[pb.Permission]*protogen.EnumValue{}
	)
	if !(roles == nil || permissions == nil || accessControlService == nil) {
		accessControlIdent = roles.GoIdent
		accessControlIdent.GoName = accessControlService.GoName

		accessControlIAMIdent = roles.GoIdent
		accessControlIAMIdent.GoName = "IAM"

		roleMap = map[pb.Role]*protogen.EnumValue{}
		for _, value := range roles.Values {
			roleMap[pb.Role(value.Desc.Number())] = value
		}

		permissionMap = map[pb.Permission]*protogen.EnumValue{}
		for _, value := range permissions.Values {
			permissionMap[pb.Permission(value.Desc.Number())] = value
		}
	} else {
		roles = &protogen.Enum{}
		permissions = &protogen.Enum{}
		accessControlService = &protogen.Service{}
	}

	for _, file := range gen.Files {
		if !file.Generate {
			continue
		}

		if ext, _ := proto.GetExtension(file.Desc.Options(), pb.E_GenerateIamFunctions).(bool); ext {
			g := gen.NewGeneratedFile(
				file.GeneratedFilenamePrefix+V2_IAM_FUNC_SUFFIX,
				file.GoImportPath,
			)
			HeaderPrint(g, string(file.GoPackageName))

			generator.GenerateIamFunctions(file, g, roles, permissions, roleMap, permissionMap)
		}

		if file.Services == nil {
			return
		}

		iamServices := make(map[protoreflect.FullName]struct{})
		for _, service := range file.Services {
			if ext, _ := proto.GetExtension(service.Desc.Options(), pb.E_ServiceIam).(*pb.IAM); ext != nil {
				iamServices[service.Desc.FullName()] = struct{}{}
				continue
			}

			for _, method := range service.Methods {
				if ext, _ := proto.GetExtension(method.Desc.Options(), pb.E_MethodIam).(*pb.IAM); ext != nil {
					iamServices[service.Desc.FullName()] = struct{}{}
					break
				}
			}
		}

		g := gen.NewGeneratedFile(
			file.GeneratedFilenamePrefix+V2_IAM_SUFFIX,
			file.GoImportPath,
		)
		HeaderPrint(g, string(file.GoPackageName))

		for _, service := range file.Services {
			g.P("func NewIam", service.GoName, "Server(")
			g.P("srv ", service.GoName, "Server, ")
			if _, ok := iamServices[service.Desc.FullName()]; ok {
				g.P("accessControlClient ", accessControlIdent, "Client,")
			}
			g.P(") ", service.GoName, "Server {")
			g.P("return &iam", service.GoName, "Server{")
			g.P("srv: srv,")
			if _, ok := iamServices[service.Desc.FullName()]; ok {
				g.P("accessControlClient: accessControlClient,")
			}
			g.P("}")
			g.P("}")
			g.P()

			g.P("var _ ", service.GoName, "Server = (*iam", service.GoName, "Server)(nil)")

			g.P("type iam", service.GoName, "Server struct {")
			g.P("Unimplemented", service.GoName, "Server")
			g.P()
			g.P("srv ", service.GoName, "Server")
			if _, ok := iamServices[service.Desc.FullName()]; ok {
				g.P("accessControlClient ", accessControlIdent, "Client")
			}
			g.P("}")
			g.P()

			serviceExt, _ := proto.GetExtension(service.Desc.Options(), pb.E_ServiceIam).(*pb.IAM)

			for _, method := range service.Methods {
				g.P("func (s *iam", service.GoName, "Server) ", method.GoName, "(")
				g.P("ctx ", contextContextIdent, ",")
				g.P("req *", method.Input.GoIdent, ",")
				g.P(") (")
				g.P("*", method.Output.GoIdent, ",")
				g.P("error,")
				g.P(") {")

				methodExt, _ := proto.GetExtension(method.Desc.Options(), pb.E_MethodIam).(*pb.IAM)
				joined := JoinIAM(serviceExt, methodExt)
				if len(joined.Roles) == 0 && len(joined.Permissions) == 0 {
					g.P("return s.srv.", method.GoName, "(ctx, req)")
					g.P("}")
					g.P()
					continue
				}

				g.P("if _, err := s.accessControlClient.CheckGrant(ctx, &", accessControlIAMIdent, "{")

				if joined.Roles != nil {
					g.P("Roles: []", roles.GoIdent, "{")
					for _, role := range joined.Roles {
						g.P(roleMap[role].GoIdent, ",")
					}
					g.P("},")
				}

				if joined.Permissions != nil {
					g.P("Permissions: []", permissions.GoIdent, "{")
					for _, permission := range joined.Permissions {
						g.P(permissionMap[permission].GoIdent, ",")
					}
					g.P("},")
				}

				g.P("}); err != nil {")
				g.P("return nil, err")
				g.P("}")
				g.P()
				g.P("return s.srv.", method.GoName, "(ctx, req)")
				g.P("}")
				g.P()
			}

			g.P("func NewIam", service.GoName, "Client(")
			g.P("client ", service.GoName, "Client,")
			g.P(") ", service.GoName, "Client {")
			g.P("return &iam", service.GoName, "Client{")
			g.P("client: client,")
			g.P("}")
			g.P("}")
			g.P()

			g.P("type iam", service.GoName, "Client struct {")
			g.P("client ", service.GoName, "Client")
			g.P("}")
			g.P()
			for _, method := range service.Methods {
				g.P("func (s *iam", service.GoName, "Client) ", method.GoName, "(")
				g.P("ctx ", contextContextIdent, ",")
				g.P("req *", method.Input.GoIdent, ",")
				g.P("opts ...", grpcPackageCallOption, ",")
				g.P(") (")
				g.P("*", method.Output.GoIdent, ",")
				g.P("error,")
				g.P("){")
				g.P("md, _ := ", fromIncomingContextMetadataPackage, "(ctx)")
				g.P("return s.client.", method.GoName, "(", metadataPackageNewOutgoingContext, "(ctx, md), req)")
				g.P("}")
				g.P()
			}
		}

	}
}

func JoinIAM(first, second *pb.IAM) *pb.IAM {
	result := &pb.IAM{}

	if first != nil {
		result.Permissions = append(result.Permissions, first.Permissions...)
		result.Roles = append(result.Roles, first.Roles...)
	}

	if second != nil {
		result.Permissions = append(result.Permissions, second.Permissions...)
		result.Roles = append(result.Roles, second.Roles...)
	}

	return result
}

type IamRole struct {
	Descriptor  protoreflect.EnumValueDescriptor
	Role        pb.Role
	Permissions map[pb.Permission]struct{}
	Children    map[*IamRole]struct{}
	Parents     map[*IamRole]struct{}
}

func RecursiveFindChildren(
	iamRole *IamRole,
	roles map[pb.Role]*IamRole,
	visited map[*IamRole]struct{},
) {
	if _, ok := visited[iamRole]; ok {
		return
	}

	visited[iamRole] = struct{}{}

	for child := range iamRole.Children {
		RecursiveFindChildren(child, roles, visited)

		for role := range child.Children {
			iamRole.Children[role] = struct{}{}
		}

		for permission := range child.Permissions {
			iamRole.Permissions[permission] = struct{}{}
		}
	}

	for role := range iamRole.Children {
		role.Parents[role] = struct{}{}
	}
}

func (generator *IamGenerator) GenerateIamFunctions(
	file *protogen.File,
	g *protogen.GeneratedFile,
	roles *protogen.Enum,
	permissions *protogen.Enum,
	roleMap map[pb.Role]*protogen.EnumValue,
	permissionMap map[pb.Permission]*protogen.EnumValue,
) {

	roleLength := roles.Desc.Values().Len()

	roleWithStruct := map[pb.Role]*IamRole{}
	for i := 0; i < roleLength; i++ {
		descriptor := roles.Desc.Values().Get(i)
		enumNumber := descriptor.Number()

		roleWithStruct[pb.Role(enumNumber)] = &IamRole{
			Descriptor:  descriptor,
			Role:        pb.Role(enumNumber),
			Permissions: map[pb.Permission]struct{}{},
			Children:    map[*IamRole]struct{}{},
			Parents:     map[*IamRole]struct{}{},
		}
	}

	for _, iamRole := range roleWithStruct {
		ext, _ := proto.GetExtension(iamRole.Descriptor.Options(), pb.E_RoleSpec).(*pb.RoleSpec)
		if ext == nil {
			continue
		}

		for _, permission := range ext.GetPermissions() {
			iamRole.Permissions[permission] = struct{}{}
		}

		for _, role := range ext.GetChildren() {
			child := roleWithStruct[role]

			iamRole.Children[child] = struct{}{}
			child.Parents[iamRole] = struct{}{}
		}
	}

	for _, iamRole := range roleWithStruct {
		RecursiveFindChildren(iamRole, roleWithStruct, map[*IamRole]struct{}{})
	}

	g.P("var local_", file.GoDescriptorIdent, "_Role_Children = map[", roles.GoIdent, "]map[", roles.GoIdent, "]struct{}{")

	roleWithStructKeys := make([]pb.Role, 0, len(roleWithStruct))
	for role := range roleWithStruct {
		roleWithStructKeys = append(roleWithStructKeys, role)
	}
	sort.Slice(roleWithStructKeys, func(i, j int) bool {
		return roleWithStructKeys[i] < roleWithStructKeys[j]
	})

	for _, roleKey := range roleWithStructKeys {
		iamRole := roleWithStruct[roleKey]

		children := make([]pb.Role, 0, len(iamRole.Children))
		for child := range iamRole.Children {
			children = append(children, child.Role)
		}
		sort.Slice(children, func(i, j int) bool {
			return children[i] < children[j]
		})

		g.P(roleMap[roleKey].GoIdent, ": map[", roles.GoIdent, "]struct{}{")
		for _, child := range children {
			g.P(roleMap[child].GoIdent, ": struct{}{},")
		}
		g.P("},")
	}

	g.P("}")
	g.P()

	g.P("func ", file.GoDescriptorIdent, "_Role_Children_Link() map[", roles.GoIdent, "]map[", roles.GoIdent, "]struct{} {")
	g.P("result := make(map[", roles.GoIdent, "]map[", roles.GoIdent, "]struct{}, len(local_", file.GoDescriptorIdent, "_Role_Children))")
	g.P("for role, children := range local_", file.GoDescriptorIdent, "_Role_Children {")
	g.P("newChildren := make(map[", roles.GoIdent, "]struct{}, len(children))")
	g.P()
	g.P("for child := range children {")
	g.P("newChildren[child] = struct{}{}")
	g.P("}")
	g.P()
	g.P("result[role] = newChildren")
	g.P("}")
	g.P()
	g.P("return result")
	g.P("}")
	g.P()

	g.P("var local_", file.GoDescriptorIdent, "_Role_Permissions = map[", roles.GoIdent, "]map[", permissions.GoIdent, "]struct{}{")

	for _, roleKey := range roleWithStructKeys {
		iamRole := roleWithStruct[roleKey]
		permissionSlice := make([]pb.Permission, 0, len(iamRole.Permissions))
		for permission := range iamRole.Permissions {
			permissionSlice = append(permissionSlice, permission)
		}
		sort.Slice(permissionSlice, func(i, j int) bool {
			return permissionSlice[i] < permissionSlice[j]
		})

		g.P(roleMap[iamRole.Role].GoIdent, ": map[", permissions.GoIdent, "]struct{}{")
		for _, permission := range permissionSlice {
			g.P(permissionMap[permission].GoIdent, ": struct{}{},")
		}
		g.P("},")
	}

	g.P("}")
	g.P()

	g.P("func ", file.GoDescriptorIdent, "_Role_Permission_Link() map[", roles.GoIdent, "]map[", permissions.GoIdent, "]struct{} {")
	g.P("result := make(map[", roles.GoIdent, "]map[", permissions.GoIdent, "]struct{}, len(local_", file.GoDescriptorIdent, "_Role_Permissions))")
	g.P("for role, permissions := range local_", file.GoDescriptorIdent, "_Role_Permissions {")
	g.P("newPermissions := make(map[", permissions.GoIdent, "]struct{}, len(permissions))")
	g.P()
	g.P("for permission := range permissions {")
	g.P("newPermissions[permission] = struct{}{}")
	g.P("}")
	g.P()
	g.P("result[role] = newPermissions")
	g.P("}")
	g.P()
	g.P("return result")
	g.P("}")
	g.P()

	g.P(`func `, file.GoDescriptorIdent, `_CheckAccess(
	currentRoles []`, roles.GoIdent, `,
	currentPermissions []`, permissions.GoIdent, `,
	requiredRoles []`, roles.GoIdent, `,
	requiredPermissions []`, permissions.GoIdent, `,
) bool {
	currentPermissionsMap := map[`, permissions.GoIdent, `]struct{}{}
	for _, currentPermission := range currentPermissions {
		currentPermissionsMap[currentPermission] = struct{}{}
	}

	currentRolesMap := map[`, roles.GoIdent, `]struct{}{}
	for _, currentRole := range currentRoles {
		currentRolesMap[currentRole] = struct{}{}
		
			for subRole := range local_`, file.GoDescriptorIdent, `_Role_Children[currentRole] {
			currentRolesMap[subRole] = struct{}{}			
		}

		for subPermission := range local_`, file.GoDescriptorIdent, `_Role_Permissions[currentRole] {
			currentPermissionsMap[subPermission] = struct{}{}
		}
	}

	for _, requiredRole := range requiredRoles {
		if _, ok := currentRolesMap[requiredRole]; !ok {
			return false
		}
	}
	
	for _, requiredPermission := range requiredPermissions {
		if _, ok := currentPermissionsMap[requiredPermission]; !ok {
			return false
		}
	}

	return true
}`)
	g.P()
}
