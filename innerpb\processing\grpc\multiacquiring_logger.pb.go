// Code generated by protoc-gen-go-mvp. DO NOT EDIT.
// versions:
// - protoc-gen-go-mvp v2.0.1
// created by mvp authors

package grpc

import (
	context "context"
	fmt "fmt"
	cntx "git.local/sensitive/mvp/pkg/cntx"
	ctxzap "github.com/grpc-ecosystem/go-grpc-middleware/logging/zap/ctxzap"
	zap "go.uber.org/zap"
	grpc "google.golang.org/grpc"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
	structpb "google.golang.org/protobuf/types/known/structpb"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
)

func file_inner_processing_grpc_multiacquiring_proto_string_structpb_Value(
	label string,
	in map[string]*structpb.Value,
) zap.Field {
	var res []zap.Field
	for key, val := range in {
		res = append(res, file_inner_processing_grpc_multiacquiring_proto_message_ValueToZap(fmt.Sprint(key), val))
	}

	return zap.Dict(label, res...)
}

func file_inner_processing_grpc_multiacquiring_proto_message_EmptyToZap(
	label string,
	in *emptypb.Empty,
) zap.Field {
	if in == nil {
		return zap.Reflect(label, in)
	}

	return zap.Dict(
		label,
	)
}

func file_inner_processing_grpc_multiacquiring_proto_message_ListValueToZap(
	label string,
	in *structpb.ListValue,
) zap.Field {
	if in == nil {
		return zap.Reflect(label, in)
	}

	return zap.Dict(
		label,
		file_inner_processing_grpc_multiacquiring_proto_message_ValueSliceToZap("Values", in.GetValues()),
	)
}

func file_inner_processing_grpc_multiacquiring_proto_enum_NullValueToZap(
	label string,
	in structpb.NullValue,
) zap.Field {
	str, ok := structpb.NullValue_name[int32(in)]
	if !ok {
		str = fmt.Sprintf("unknown structpb.NullValue value %d", in)
	}

	return zap.Dict(
		label,
		zap.Any("value", structpb.NullValue(in)),
		zap.Any("name", str),
	)
}

func file_inner_processing_grpc_multiacquiring_proto_message_StructToZap(
	label string,
	in *structpb.Struct,
) zap.Field {
	if in == nil {
		return zap.Reflect(label, in)
	}

	return zap.Dict(
		label,
		file_inner_processing_grpc_multiacquiring_proto_string_structpb_Value("Fields", in.GetFields()),
	)
}

func file_inner_processing_grpc_multiacquiring_proto_message_TimestampToZap(
	label string,
	in *timestamppb.Timestamp,
) zap.Field {
	if in == nil {
		return zap.Reflect(label, in)
	}

	return zap.Dict(
		label,
		zap.Any("Seconds", in.GetSeconds()),
		zap.Any("Nanos", in.GetNanos()),
	)
}

func file_inner_processing_grpc_multiacquiring_proto_message_ValueToZap(
	label string,
	in *structpb.Value,
) zap.Field {
	if in == nil {
		return zap.Reflect(label, in)
	}

	return zap.Dict(
		label,
		file_inner_processing_grpc_multiacquiring_proto_enum_NullValueToZap("NullValue", in.GetNullValue()),
		zap.Any("NumberValue", in.GetNumberValue()),
		zap.Any("StringValue", in.GetStringValue()),
		zap.Any("BoolValue", in.GetBoolValue()),
		file_inner_processing_grpc_multiacquiring_proto_message_StructToZap("StructValue", in.GetStructValue()),
		file_inner_processing_grpc_multiacquiring_proto_message_ListValueToZap("ListValue", in.GetListValue()),
	)
}

func file_inner_processing_grpc_multiacquiring_proto_message_ValueSliceToZap(
	label string,
	in []*structpb.Value,
) zap.Field {
	var res []zap.Field
	for index, val := range in {
		res = append(res, file_inner_processing_grpc_multiacquiring_proto_message_ValueToZap(fmt.Sprint(index), val))
	}

	return zap.Dict(label, res...)
}

func file_inner_processing_grpc_multiacquiring_proto_message_ApplePayRequestDataToZap(
	label string,
	in *ApplePayRequestData,
) zap.Field {
	if in == nil {
		return zap.Reflect(label, in)
	}

	return zap.Dict(
		label,
		zap.Any("TransactionId", in.GetTransactionId()),
		zap.Any("IsHold", in.GetIsHold()),
		zap.Any("Amount", in.GetAmount()),
		zap.Any("Description", in.GetDescription()),
		file_inner_processing_grpc_multiacquiring_proto_message_CardDataForBankToZap("Card", in.GetCard()),
		file_inner_processing_grpc_multiacquiring_proto_message_UserDataForBankToZap("User", in.GetUser()),
		file_inner_processing_grpc_multiacquiring_proto_message_TerminalDataForBankToZap("Terminal", in.GetTerminal()),
		zap.Any("TransactionTypeId", in.GetTransactionTypeId()),
		file_inner_processing_grpc_multiacquiring_proto_message_CardInfoToZap("CardInfo", in.GetCardInfo()),
		file_inner_processing_grpc_multiacquiring_proto_message_MerchantInfoToZap("MerchantInfo", in.GetMerchantInfo()),
		zap.Any("Tavv", in.GetTavv()),
		zap.Any("Eci", in.GetEci()),
		file_inner_processing_grpc_multiacquiring_proto_message_TimestampToZap("TransactionCreatedAt", in.GetTransactionCreatedAt()),
		zap.Any("AggregatedTypeId", in.GetAggregatedTypeId()),
		zap.Any("UserIpAddress", in.GetUserIpAddress()),
	)
}

func file_inner_processing_grpc_multiacquiring_proto_message_ApplePayResponseDataToZap(
	label string,
	in *ApplePayResponseData,
) zap.Field {
	if in == nil {
		return zap.Reflect(label, in)
	}

	return zap.Dict(
		label,
		zap.Any("TransactionId", in.GetTransactionId()),
		zap.Any("BankReferenceId", in.GetBankReferenceId()),
		file_inner_processing_grpc_multiacquiring_proto_enum_EnumTransactionStatusToZap("TransactionStatus", in.GetTransactionStatus()),
		zap.Any("CardId", in.GetCardId()),
		file_inner_processing_grpc_multiacquiring_proto_message_ThreeDSDataFromBankToZap("ThreeDS", in.GetThreeDS()),
		file_inner_processing_grpc_multiacquiring_proto_message_BankResponseToZap("BankResponse", in.GetBankResponse()),
		zap.Any("BankOrderId", in.GetBankOrderId()),
	)
}

func file_inner_processing_grpc_multiacquiring_proto_message_BankResponseToZap(
	label string,
	in *BankResponse,
) zap.Field {
	if in == nil {
		return zap.Reflect(label, in)
	}

	return zap.Dict(
		label,
		zap.Any("Message", in.GetMessage()),
		zap.Any("Code", in.GetCode()),
		zap.Any("IntegrationCode", in.GetIntegrationCode()),
		zap.Any("IntegrationMessage", in.GetIntegrationMessage()),
	)
}

func file_inner_processing_grpc_multiacquiring_proto_message_BankTransactionStatusRequestToZap(
	label string,
	in *BankTransactionStatusRequest,
) zap.Field {
	if in == nil {
		return zap.Reflect(label, in)
	}

	return zap.Dict(
		label,
		zap.Any("TransactionId", in.GetTransactionId()),
		zap.Any("TransactionType", in.GetTransactionType()),
		zap.Any("AcquirerCode", in.GetAcquirerCode()),
		file_inner_processing_grpc_multiacquiring_proto_message_StructToZap("Config", in.GetConfig()),
		zap.Any("BankReferenceId", in.GetBankReferenceId()),
		zap.Any("BankOrderId", in.GetBankOrderId()),
		file_inner_processing_grpc_multiacquiring_proto_message_TerminalDataForBankToZap("Terminal", in.GetTerminal()),
		file_inner_processing_grpc_multiacquiring_proto_message_TimestampToZap("TransactionCreatedAt", in.GetTransactionCreatedAt()),
	)
}

func file_inner_processing_grpc_multiacquiring_proto_message_BankTransactionStatusResponseToZap(
	label string,
	in *BankTransactionStatusResponse,
) zap.Field {
	if in == nil {
		return zap.Reflect(label, in)
	}

	return zap.Dict(
		label,
		zap.Any("TransactionId", in.GetTransactionId()),
		zap.Any("BankReferenceId", in.GetBankReferenceId()),
		file_inner_processing_grpc_multiacquiring_proto_enum_EnumTransactionStatusToZap("TransactionStatus", in.GetTransactionStatus()),
		file_inner_processing_grpc_multiacquiring_proto_message_BankResponseToZap("BankResponse", in.GetBankResponse()),
	)
}

func file_inner_processing_grpc_multiacquiring_proto_message_BankTransactionStatusUnformatedRequestToZap(
	label string,
	in *BankTransactionStatusUnformatedRequest,
) zap.Field {
	if in == nil {
		return zap.Reflect(label, in)
	}

	return zap.Dict(
		label,
		zap.Any("TransactionId", in.GetTransactionId()),
		zap.Any("TransactionType", in.GetTransactionType()),
		zap.Any("AcquirerCode", in.GetAcquirerCode()),
		file_inner_processing_grpc_multiacquiring_proto_message_StructToZap("Config", in.GetConfig()),
		zap.Any("BankReferenceId", in.GetBankReferenceId()),
		zap.Any("BankOrderId", in.GetBankOrderId()),
		file_inner_processing_grpc_multiacquiring_proto_message_TerminalDataForBankToZap("Terminal", in.GetTerminal()),
		file_inner_processing_grpc_multiacquiring_proto_message_TimestampToZap("TransactionCreatedAt", in.GetTransactionCreatedAt()),
	)
}

func file_inner_processing_grpc_multiacquiring_proto_message_BankTransactionStatusUnformatedResponseToZap(
	label string,
	in *BankTransactionStatusUnformatedResponse,
) zap.Field {
	if in == nil {
		return zap.Reflect(label, in)
	}

	return zap.Dict(
		label,
		zap.Any("TransactionId", in.GetTransactionId()),
		zap.Any("BankMessage", in.GetBankMessage()),
	)
}

func file_inner_processing_grpc_multiacquiring_proto_message_CancelRequestToZap(
	label string,
	in *CancelRequest,
) zap.Field {
	if in == nil {
		return zap.Reflect(label, in)
	}

	return zap.Dict(
		label,
		zap.Any("TransactionId", in.GetTransactionId()),
		zap.Any("Amount", in.GetAmount()),
		file_inner_processing_grpc_multiacquiring_proto_message_TerminalDataForBankToZap("Terminal", in.GetTerminal()),
		zap.Any("BankReferenceId", in.GetBankReferenceId()),
		zap.Any("BankOrderId", in.GetBankOrderId()),
		zap.Any("Description", in.GetDescription()),
		file_inner_processing_grpc_multiacquiring_proto_message_TimestampToZap("TransactionCreatedAt", in.GetTransactionCreatedAt()),
	)
}

func file_inner_processing_grpc_multiacquiring_proto_message_CancelResponseToZap(
	label string,
	in *CancelResponse,
) zap.Field {
	if in == nil {
		return zap.Reflect(label, in)
	}

	return zap.Dict(
		label,
		zap.Any("TransactionId", in.GetTransactionId()),
		zap.Any("BankReferenceId", in.GetBankReferenceId()),
		zap.Any("BankOrderId", in.GetBankOrderId()),
		file_inner_processing_grpc_multiacquiring_proto_message_BankResponseToZap("BankResponse", in.GetBankResponse()),
	)
}

func file_inner_processing_grpc_multiacquiring_proto_message_CardDataForBankToZap(
	label string,
	in *CardDataForBank,
) zap.Field {
	if in == nil {
		return zap.Reflect(label, in)
	}

	return zap.Dict(
		label,
		zap.Any("Pan", "[***hidden***]"),
		zap.Any("ExpMonth", in.GetExpMonth()),
		zap.Any("ExpYear", in.GetExpYear()),
		zap.Any("Cvc", "[***hidden***]"),
		zap.Any("FullName", in.GetFullName()),
		zap.Any("Token", in.GetToken()),
		zap.Any("Save", in.GetSave()),
	)
}

func file_inner_processing_grpc_multiacquiring_proto_message_CardInfoToZap(
	label string,
	in *CardInfo,
) zap.Field {
	if in == nil {
		return zap.Reflect(label, in)
	}

	return zap.Dict(
		label,
		zap.Any("IpsId", in.GetIpsId()),
		zap.Any("IssuerId", in.GetIssuerId()),
		zap.Any("CountryId", in.GetCountryId()),
	)
}

func file_inner_processing_grpc_multiacquiring_proto_message_ChargeRequestToZap(
	label string,
	in *ChargeRequest,
) zap.Field {
	if in == nil {
		return zap.Reflect(label, in)
	}

	return zap.Dict(
		label,
		zap.Any("TransactionId", in.GetTransactionId()),
		zap.Any("Amount", in.GetAmount()),
		file_inner_processing_grpc_multiacquiring_proto_message_TerminalDataForBankToZap("Terminal", in.GetTerminal()),
		zap.Any("BankReferenceId", in.GetBankReferenceId()),
		zap.Any("BankOrderId", in.GetBankOrderId()),
		file_inner_processing_grpc_multiacquiring_proto_message_TimestampToZap("TransactionCreatedAt", in.GetTransactionCreatedAt()),
	)
}

func file_inner_processing_grpc_multiacquiring_proto_message_ChargeResponseToZap(
	label string,
	in *ChargeResponse,
) zap.Field {
	if in == nil {
		return zap.Reflect(label, in)
	}

	return zap.Dict(
		label,
		zap.Any("TransactionId", in.GetTransactionId()),
		zap.Any("BankReferenceId", in.GetBankReferenceId()),
		zap.Any("BankOrderId", in.GetBankOrderId()),
		file_inner_processing_grpc_multiacquiring_proto_message_BankResponseToZap("BankResponse", in.GetBankResponse()),
	)
}

func file_inner_processing_grpc_multiacquiring_proto_message_FingerprintToZap(
	label string,
	in *Fingerprint,
) zap.Field {
	if in == nil {
		return zap.Reflect(label, in)
	}

	return zap.Dict(
		label,
		zap.Any("MethodUrl", in.GetMethodUrl()),
		zap.Any("MethodData", in.GetMethodData()),
	)
}

func file_inner_processing_grpc_multiacquiring_proto_message_GetAcquirerIdentifierResponseToZap(
	label string,
	in *GetAcquirerIdentifierResponse,
) zap.Field {
	if in == nil {
		return zap.Reflect(label, in)
	}

	return zap.Dict(
		label,
		zap.Any("AcquirerIdentifier", in.GetAcquirerIdentifier()),
	)
}

func file_inner_processing_grpc_multiacquiring_proto_message_GooglePayRequestDataToZap(
	label string,
	in *GooglePayRequestData,
) zap.Field {
	if in == nil {
		return zap.Reflect(label, in)
	}

	return zap.Dict(
		label,
		zap.Any("TransactionId", in.GetTransactionId()),
		zap.Any("Amount", in.GetAmount()),
		zap.Any("Description", in.GetDescription()),
		file_inner_processing_grpc_multiacquiring_proto_message_CardDataForBankToZap("Card", in.GetCard()),
		file_inner_processing_grpc_multiacquiring_proto_message_UserDataForBankToZap("User", in.GetUser()),
		file_inner_processing_grpc_multiacquiring_proto_message_TerminalDataForBankToZap("Terminal", in.GetTerminal()),
		file_inner_processing_grpc_multiacquiring_proto_message_CardInfoToZap("CardInfo", in.GetCardInfo()),
		file_inner_processing_grpc_multiacquiring_proto_message_MerchantInfoToZap("MerchantInfo", in.GetMerchantInfo()),
		zap.Any("AggregatedTypeId", in.GetAggregatedTypeId()),
		zap.Any("Token", in.GetToken()),
		zap.Any("Currency", in.GetCurrency()),
		zap.Any("EciIndicator", in.GetEciIndicator()),
		zap.Any("Cryptogram", in.GetCryptogram()),
		file_inner_processing_grpc_multiacquiring_proto_message_TimestampToZap("TransactionCreatedAt", in.GetTransactionCreatedAt()),
		zap.Any("UserIpAddress", in.GetUserIpAddress()),
	)
}

func file_inner_processing_grpc_multiacquiring_proto_message_GooglePayResponseDataToZap(
	label string,
	in *GooglePayResponseData,
) zap.Field {
	if in == nil {
		return zap.Reflect(label, in)
	}

	return zap.Dict(
		label,
		zap.Any("TransactionId", in.GetTransactionId()),
		zap.Any("BankReferenceId", in.GetBankReferenceId()),
		file_inner_processing_grpc_multiacquiring_proto_enum_EnumTransactionStatusToZap("TransactionStatus", in.GetTransactionStatus()),
		file_inner_processing_grpc_multiacquiring_proto_message_ThreeDSDataFromBankToZap("ThreeDS", in.GetThreeDS()),
		file_inner_processing_grpc_multiacquiring_proto_message_BankResponseToZap("BankResponse", in.GetBankResponse()),
		zap.Any("BankOrderId", in.GetBankOrderId()),
		file_inner_processing_grpc_multiacquiring_proto_message_FingerprintToZap("Fingerprint", in.GetFingerprint()),
	)
}

func file_inner_processing_grpc_multiacquiring_proto_message_MerchantInfoToZap(
	label string,
	in *MerchantInfo,
) zap.Field {
	if in == nil {
		return zap.Reflect(label, in)
	}

	return zap.Dict(
		label,
		zap.Any("ProjectId", in.GetProjectId()),
		zap.Any("MerchantId", in.GetMerchantId()),
	)
}

func file_inner_processing_grpc_multiacquiring_proto_message_OneClickPayInRequestDataToZap(
	label string,
	in *OneClickPayInRequestData,
) zap.Field {
	if in == nil {
		return zap.Reflect(label, in)
	}

	return zap.Dict(
		label,
		zap.Any("TransactionId", in.GetTransactionId()),
		zap.Any("CardId", in.GetCardId()),
		zap.Any("Amount", in.GetAmount()),
		zap.Any("Description", in.GetDescription()),
		file_inner_processing_grpc_multiacquiring_proto_message_UserDataForBankToZap("User", in.GetUser()),
		file_inner_processing_grpc_multiacquiring_proto_message_TerminalDataForBankToZap("Terminal", in.GetTerminal()),
		zap.Any("AggregatedTypeId", in.GetAggregatedTypeId()),
		file_inner_processing_grpc_multiacquiring_proto_message_CardInfoToZap("CardInfo", in.GetCardInfo()),
		file_inner_processing_grpc_multiacquiring_proto_message_MerchantInfoToZap("MerchantInfo", in.GetMerchantInfo()),
		file_inner_processing_grpc_multiacquiring_proto_message_TimestampToZap("TransactionCreatedAt", in.GetTransactionCreatedAt()),
		zap.Any("UserIpAddress", in.GetUserIpAddress()),
	)
}

func file_inner_processing_grpc_multiacquiring_proto_message_PayInRequestDataToZap(
	label string,
	in *PayInRequestData,
) zap.Field {
	if in == nil {
		return zap.Reflect(label, in)
	}

	return zap.Dict(
		label,
		zap.Any("TransactionId", in.GetTransactionId()),
		zap.Any("IsHold", in.GetIsHold()),
		zap.Any("Amount", in.GetAmount()),
		zap.Any("Description", in.GetDescription()),
		file_inner_processing_grpc_multiacquiring_proto_message_CardDataForBankToZap("Card", in.GetCard()),
		file_inner_processing_grpc_multiacquiring_proto_message_UserDataForBankToZap("User", in.GetUser()),
		file_inner_processing_grpc_multiacquiring_proto_message_TerminalDataForBankToZap("Terminal", in.GetTerminal()),
		zap.Any("AggregatedTypeId", in.GetAggregatedTypeId()),
		file_inner_processing_grpc_multiacquiring_proto_message_CardInfoToZap("CardInfo", in.GetCardInfo()),
		file_inner_processing_grpc_multiacquiring_proto_message_MerchantInfoToZap("MerchantInfo", in.GetMerchantInfo()),
		file_inner_processing_grpc_multiacquiring_proto_message_TimestampToZap("TransactionCreatedAt", in.GetTransactionCreatedAt()),
		zap.Any("GetForm", in.GetGetForm()),
		zap.Any("UserIpAddress", in.GetUserIpAddress()),
	)
}

func file_inner_processing_grpc_multiacquiring_proto_message_PayInResponseDataToZap(
	label string,
	in *PayInResponseData,
) zap.Field {
	if in == nil {
		return zap.Reflect(label, in)
	}

	return zap.Dict(
		label,
		zap.Any("TransactionId", in.GetTransactionId()),
		zap.Any("BankReferenceId", in.GetBankReferenceId()),
		file_inner_processing_grpc_multiacquiring_proto_enum_EnumTransactionStatusToZap("TransactionStatus", in.GetTransactionStatus()),
		zap.Any("CardId", in.GetCardId()),
		file_inner_processing_grpc_multiacquiring_proto_message_ThreeDSDataFromBankToZap("ThreeDS", in.GetThreeDS()),
		file_inner_processing_grpc_multiacquiring_proto_message_BankResponseToZap("BankResponse", in.GetBankResponse()),
		zap.Any("BankOrderId", in.GetBankOrderId()),
		file_inner_processing_grpc_multiacquiring_proto_message_FingerprintToZap("Fingerprint", in.GetFingerprint()),
	)
}

func file_inner_processing_grpc_multiacquiring_proto_message_PayOutByPhoneRequestDataToZap(
	label string,
	in *PayOutByPhoneRequestData,
) zap.Field {
	if in == nil {
		return zap.Reflect(label, in)
	}

	return zap.Dict(
		label,
		zap.Any("TransactionId", in.GetTransactionId()),
		zap.Any("VisaAliasToken", in.GetVisaAliasToken()),
		zap.Any("Amount", in.GetAmount()),
		file_inner_processing_grpc_multiacquiring_proto_message_UserDataForBankToZap("User", in.GetUser()),
		file_inner_processing_grpc_multiacquiring_proto_message_TerminalDataForBankToZap("Terminal", in.GetTerminal()),
		file_inner_processing_grpc_multiacquiring_proto_message_PayOutCardToZap("Card", in.GetCard()),
		zap.Any("AggregatedTypeId", in.GetAggregatedTypeId()),
		file_inner_processing_grpc_multiacquiring_proto_message_CardInfoToZap("CardInfo", in.GetCardInfo()),
		file_inner_processing_grpc_multiacquiring_proto_message_MerchantInfoToZap("MerchantInfo", in.GetMerchantInfo()),
		file_inner_processing_grpc_multiacquiring_proto_message_TimestampToZap("TransactionCreatedAt", in.GetTransactionCreatedAt()),
		zap.Any("CInfo", in.GetCInfo()),
	)
}

func file_inner_processing_grpc_multiacquiring_proto_message_PayOutCardToZap(
	label string,
	in *PayOutCard,
) zap.Field {
	if in == nil {
		return zap.Reflect(label, in)
	}

	return zap.Dict(
		label,
		zap.Any("Pan", "[***hidden***]"),
		zap.Any("ExpMonth", in.GetExpMonth()),
		zap.Any("ExpYear", in.GetExpYear()),
		zap.Any("Cvc", "[***hidden***]"),
	)
}

func file_inner_processing_grpc_multiacquiring_proto_message_PayOutRequestDataToZap(
	label string,
	in *PayOutRequestData,
) zap.Field {
	if in == nil {
		return zap.Reflect(label, in)
	}

	return zap.Dict(
		label,
		zap.Any("TransactionId", in.GetTransactionId()),
		zap.Any("Pan", "[***hidden***]"),
		zap.Any("Amount", in.GetAmount()),
		file_inner_processing_grpc_multiacquiring_proto_message_UserDataForBankToZap("User", in.GetUser()),
		file_inner_processing_grpc_multiacquiring_proto_message_TerminalDataForBankToZap("Terminal", in.GetTerminal()),
		file_inner_processing_grpc_multiacquiring_proto_message_PayOutCardToZap("Card", in.GetCard()),
		zap.Any("AggregatedTypeId", in.GetAggregatedTypeId()),
		file_inner_processing_grpc_multiacquiring_proto_message_CardInfoToZap("CardInfo", in.GetCardInfo()),
		file_inner_processing_grpc_multiacquiring_proto_message_MerchantInfoToZap("MerchantInfo", in.GetMerchantInfo()),
		file_inner_processing_grpc_multiacquiring_proto_message_TimestampToZap("TransactionCreatedAt", in.GetTransactionCreatedAt()),
		zap.Any("CInfo", in.GetCInfo()),
	)
}

func file_inner_processing_grpc_multiacquiring_proto_message_PayOutResponseByPhoneDataToZap(
	label string,
	in *PayOutResponseByPhoneData,
) zap.Field {
	if in == nil {
		return zap.Reflect(label, in)
	}

	return zap.Dict(
		label,
		zap.Any("TransactionId", in.GetTransactionId()),
		zap.Any("BankReferenceId", in.GetBankReferenceId()),
		zap.Any("BankOrderId", in.GetBankOrderId()),
		file_inner_processing_grpc_multiacquiring_proto_enum_EnumTransactionStatusToZap("TransactionStatus", in.GetTransactionStatus()),
		file_inner_processing_grpc_multiacquiring_proto_message_BankResponseToZap("BankResponse", in.GetBankResponse()),
	)
}

func file_inner_processing_grpc_multiacquiring_proto_message_PayOutResponseDataToZap(
	label string,
	in *PayOutResponseData,
) zap.Field {
	if in == nil {
		return zap.Reflect(label, in)
	}

	return zap.Dict(
		label,
		zap.Any("TransactionId", in.GetTransactionId()),
		zap.Any("BankReferenceId", in.GetBankReferenceId()),
		zap.Any("BankOrderId", in.GetBankOrderId()),
		file_inner_processing_grpc_multiacquiring_proto_enum_EnumTransactionStatusToZap("TransactionStatus", in.GetTransactionStatus()),
		file_inner_processing_grpc_multiacquiring_proto_message_BankResponseToZap("BankResponse", in.GetBankResponse()),
	)
}

func file_inner_processing_grpc_multiacquiring_proto_message_RefundRequestToZap(
	label string,
	in *RefundRequest,
) zap.Field {
	if in == nil {
		return zap.Reflect(label, in)
	}

	return zap.Dict(
		label,
		zap.Any("TransactionId", in.GetTransactionId()),
		zap.Any("RefundAmount", in.GetRefundAmount()),
		file_inner_processing_grpc_multiacquiring_proto_message_TerminalDataForBankToZap("Terminal", in.GetTerminal()),
		zap.Any("BankReferenceId", in.GetBankReferenceId()),
		zap.Any("BankOrderId", in.GetBankOrderId()),
		file_inner_processing_grpc_multiacquiring_proto_message_TimestampToZap("TransactionCreatedAt", in.GetTransactionCreatedAt()),
		zap.Any("TransactionAmount", in.GetTransactionAmount()),
	)
}

func file_inner_processing_grpc_multiacquiring_proto_message_RefundResponseToZap(
	label string,
	in *RefundResponse,
) zap.Field {
	if in == nil {
		return zap.Reflect(label, in)
	}

	return zap.Dict(
		label,
		zap.Any("TransactionId", in.GetTransactionId()),
		file_inner_processing_grpc_multiacquiring_proto_message_BankResponseToZap("BankResponse", in.GetBankResponse()),
		zap.Any("BankReferenceId", in.GetBankReferenceId()),
	)
}

func file_inner_processing_grpc_multiacquiring_proto_message_ResolveVisaAliasRequestToZap(
	label string,
	in *ResolveVisaAliasRequest,
) zap.Field {
	if in == nil {
		return zap.Reflect(label, in)
	}

	return zap.Dict(
		label,
		zap.Any("PhoneNumber", in.GetPhoneNumber()),
		file_inner_processing_grpc_multiacquiring_proto_message_TerminalDataForBankToZap("Terminal", in.GetTerminal()),
	)
}

func file_inner_processing_grpc_multiacquiring_proto_message_ResolveVisaAliasResponseToZap(
	label string,
	in *ResolveVisaAliasResponse,
) zap.Field {
	if in == nil {
		return zap.Reflect(label, in)
	}

	return zap.Dict(
		label,
		zap.Any("IssuerName", in.GetIssuerName()),
		zap.Any("RecipientName", in.GetRecipientName()),
		zap.Any("CardType", in.GetCardType()),
		file_inner_processing_grpc_multiacquiring_proto_message_BankResponseToZap("BankResponse", in.GetBankResponse()),
		zap.Any("VisaAliasToken", in.GetVisaAliasToken()),
	)
}

func file_inner_processing_grpc_multiacquiring_proto_message_TerminalDataForBankToZap(
	label string,
	in *TerminalDataForBank,
) zap.Field {
	if in == nil {
		return zap.Reflect(label, in)
	}

	return zap.Dict(
		label,
		zap.Any("Id", in.GetId()),
		zap.Any("AcquirerId", in.GetAcquirerId()),
		zap.Any("AcquirerCode", in.GetAcquirerCode()),
		zap.Any("Config", "[***hidden***]"),
	)
}

func file_inner_processing_grpc_multiacquiring_proto_message_ThreeDSDataFromBankToZap(
	label string,
	in *ThreeDSDataFromBank,
) zap.Field {
	if in == nil {
		return zap.Reflect(label, in)
	}

	return zap.Dict(
		label,
		zap.Any("Action", in.GetAction()),
		file_inner_processing_grpc_multiacquiring_proto_message_StructToZap("Params", in.GetParams()),
		zap.Any("Template", in.GetTemplate()),
	)
}

func file_inner_processing_grpc_multiacquiring_proto_message_ThreeDSRequestDataToZap(
	label string,
	in *ThreeDSRequestData,
) zap.Field {
	if in == nil {
		return zap.Reflect(label, in)
	}

	return zap.Dict(
		label,
		zap.Any("TransactionId", in.GetTransactionId()),
		zap.Any("BankReferenceId", in.GetBankReferenceId()),
		zap.Any("Pares", in.GetPares()),
		zap.Any("Md", in.GetMd()),
		zap.Any("Amount", in.GetAmount()),
		zap.Any("Cres", in.GetCres()),
		file_inner_processing_grpc_multiacquiring_proto_message_TerminalDataForBankToZap("Terminal", in.GetTerminal()),
		zap.Any("BankOrderId", in.GetBankOrderId()),
		zap.Any("IsHold", in.GetIsHold()),
	)
}

func file_inner_processing_grpc_multiacquiring_proto_message_ThreeDSResponseDataToZap(
	label string,
	in *ThreeDSResponseData,
) zap.Field {
	if in == nil {
		return zap.Reflect(label, in)
	}

	return zap.Dict(
		label,
		zap.Any("TransactionId", in.GetTransactionId()),
		zap.Any("BankReferenceId", in.GetBankReferenceId()),
		zap.Any("BankOrderId", in.GetBankOrderId()),
		file_inner_processing_grpc_multiacquiring_proto_enum_EnumTransactionStatusToZap("TransactionStatus", in.GetTransactionStatus()),
		zap.Any("CardToken", in.GetCardToken()),
		file_inner_processing_grpc_multiacquiring_proto_message_BankResponseToZap("BankResponse", in.GetBankResponse()),
	)
}

func file_inner_processing_grpc_multiacquiring_proto_message_ThreeDSResumeRequestToZap(
	label string,
	in *ThreeDSResumeRequest,
) zap.Field {
	if in == nil {
		return zap.Reflect(label, in)
	}

	return zap.Dict(
		label,
		zap.Any("TransactionId", in.GetTransactionId()),
		zap.Any("BankReferenceId", in.GetBankReferenceId()),
		file_inner_processing_grpc_multiacquiring_proto_message_TerminalDataForBankToZap("Terminal", in.GetTerminal()),
		zap.Any("BankOrderId", in.GetBankOrderId()),
		zap.Any("IsHold", in.GetIsHold()),
	)
}

func file_inner_processing_grpc_multiacquiring_proto_message_ThreeDSResumeResponseToZap(
	label string,
	in *ThreeDSResumeResponse,
) zap.Field {
	if in == nil {
		return zap.Reflect(label, in)
	}

	return zap.Dict(
		label,
		zap.Any("TransactionId", in.GetTransactionId()),
		zap.Any("BankOrderId", in.GetBankOrderId()),
		zap.Any("BankReferenceId", in.GetBankReferenceId()),
		file_inner_processing_grpc_multiacquiring_proto_enum_EnumTransactionStatusToZap("TransactionStatus", in.GetTransactionStatus()),
		zap.Any("CardToken", in.GetCardToken()),
		file_inner_processing_grpc_multiacquiring_proto_message_ThreeDSDataFromBankToZap("ThreeDS", in.GetThreeDS()),
		file_inner_processing_grpc_multiacquiring_proto_message_BankResponseToZap("BankResponse", in.GetBankResponse()),
	)
}

func file_inner_processing_grpc_multiacquiring_proto_message_TwoStagePayInRequestToZap(
	label string,
	in *TwoStagePayInRequest,
) zap.Field {
	if in == nil {
		return zap.Reflect(label, in)
	}

	return zap.Dict(
		label,
		zap.Any("TransactionId", in.GetTransactionId()),
		zap.Any("IsHold", in.GetIsHold()),
		zap.Any("Amount", in.GetAmount()),
		zap.Any("Description", in.GetDescription()),
		file_inner_processing_grpc_multiacquiring_proto_message_CardDataForBankToZap("Card", in.GetCard()),
		file_inner_processing_grpc_multiacquiring_proto_message_UserDataForBankToZap("User", in.GetUser()),
		file_inner_processing_grpc_multiacquiring_proto_message_TerminalDataForBankToZap("Terminal", in.GetTerminal()),
		zap.Any("TransactionTypeId", in.GetTransactionTypeId()),
		file_inner_processing_grpc_multiacquiring_proto_message_CardInfoToZap("CardInfo", in.GetCardInfo()),
		file_inner_processing_grpc_multiacquiring_proto_message_MerchantInfoToZap("MerchantInfo", in.GetMerchantInfo()),
		zap.Any("AggregatedTypeId", in.GetAggregatedTypeId()),
	)
}

func file_inner_processing_grpc_multiacquiring_proto_message_TwoStagePayInResponseToZap(
	label string,
	in *TwoStagePayInResponse,
) zap.Field {
	if in == nil {
		return zap.Reflect(label, in)
	}

	return zap.Dict(
		label,
		zap.Any("TransactionId", in.GetTransactionId()),
		zap.Any("BankReferenceId", in.GetBankReferenceId()),
		file_inner_processing_grpc_multiacquiring_proto_enum_EnumTransactionStatusToZap("TransactionStatus", in.GetTransactionStatus()),
		zap.Any("CardId", in.GetCardId()),
		file_inner_processing_grpc_multiacquiring_proto_message_ThreeDSDataFromBankToZap("ThreeDS", in.GetThreeDS()),
		file_inner_processing_grpc_multiacquiring_proto_message_BankResponseToZap("BankResponse", in.GetBankResponse()),
		zap.Any("BankOrderId", in.GetBankOrderId()),
	)
}

func file_inner_processing_grpc_multiacquiring_proto_message_UserDataForBankToZap(
	label string,
	in *UserDataForBank,
) zap.Field {
	if in == nil {
		return zap.Reflect(label, in)
	}

	return zap.Dict(
		label,
		zap.Any("Id", in.GetId()),
		zap.Any("Email", in.GetEmail()),
		zap.Any("Name", in.GetName()),
		zap.Any("Phone", in.GetPhone()),
	)
}

func file_inner_processing_grpc_multiacquiring_proto_enum_EnumTransactionStatusToZap(
	label string,
	in EnumTransactionStatus,
) zap.Field {
	str, ok := EnumTransactionStatus_name[int32(in)]
	if !ok {
		str = fmt.Sprintf("unknown EnumTransactionStatus value %d", in)
	}

	return zap.Dict(
		label,
		zap.Any("value", EnumTransactionStatus(in)),
		zap.Any("name", str),
	)
}

var _ MultiAcquiringServer = (*loggedMultiAcquiringServer)(nil)

func NewLoggedMultiAcquiringServer(srv MultiAcquiringServer) MultiAcquiringServer {
	return &loggedMultiAcquiringServer{srv: srv}
}

type loggedMultiAcquiringServer struct {
	UnimplementedMultiAcquiringServer

	srv MultiAcquiringServer
}

func (s *loggedMultiAcquiringServer) PayIn(
	ctx context.Context,
	request *PayInRequestData,
) (
	response *PayInResponseData,
	err error,
) {
	label := cntx.Begin(ctx, "MultiAcquiringServer_PayIn")
	defer cntx.End(ctx, label)

	defer func() {
		if err != nil {
			ctxzap.AddFields(ctx, zap.Any(label+"error", err))
			return
		}

		ctxzap.AddFields(
			ctx,
			file_inner_processing_grpc_multiacquiring_proto_message_PayInResponseDataToZap(label+"response", response),
		)
	}()

	ctxzap.AddFields(
		ctx,
		file_inner_processing_grpc_multiacquiring_proto_message_PayInRequestDataToZap(label+"request", request),
	)

	response, err = s.srv.PayIn(ctx, request)

	return
}

func (s *loggedMultiAcquiringServer) OneClickPayIn(
	ctx context.Context,
	request *OneClickPayInRequestData,
) (
	response *PayInResponseData,
	err error,
) {
	label := cntx.Begin(ctx, "MultiAcquiringServer_OneClickPayIn")
	defer cntx.End(ctx, label)

	defer func() {
		if err != nil {
			ctxzap.AddFields(ctx, zap.Any(label+"error", err))
			return
		}

		ctxzap.AddFields(
			ctx,
			file_inner_processing_grpc_multiacquiring_proto_message_PayInResponseDataToZap(label+"response", response),
		)
	}()

	ctxzap.AddFields(
		ctx,
		file_inner_processing_grpc_multiacquiring_proto_message_OneClickPayInRequestDataToZap(label+"request", request),
	)

	response, err = s.srv.OneClickPayIn(ctx, request)

	return
}

func (s *loggedMultiAcquiringServer) ThreeDSConfirm(
	ctx context.Context,
	request *ThreeDSRequestData,
) (
	response *ThreeDSResponseData,
	err error,
) {
	label := cntx.Begin(ctx, "MultiAcquiringServer_ThreeDSConfirm")
	defer cntx.End(ctx, label)

	defer func() {
		if err != nil {
			ctxzap.AddFields(ctx, zap.Any(label+"error", err))
			return
		}

		ctxzap.AddFields(
			ctx,
			file_inner_processing_grpc_multiacquiring_proto_message_ThreeDSResponseDataToZap(label+"response", response),
		)
	}()

	ctxzap.AddFields(
		ctx,
		file_inner_processing_grpc_multiacquiring_proto_message_ThreeDSRequestDataToZap(label+"request", request),
	)

	response, err = s.srv.ThreeDSConfirm(ctx, request)

	return
}

func (s *loggedMultiAcquiringServer) ThreeDSResume(
	ctx context.Context,
	request *ThreeDSResumeRequest,
) (
	response *ThreeDSResumeResponse,
	err error,
) {
	label := cntx.Begin(ctx, "MultiAcquiringServer_ThreeDSResume")
	defer cntx.End(ctx, label)

	defer func() {
		if err != nil {
			ctxzap.AddFields(ctx, zap.Any(label+"error", err))
			return
		}

		ctxzap.AddFields(
			ctx,
			file_inner_processing_grpc_multiacquiring_proto_message_ThreeDSResumeResponseToZap(label+"response", response),
		)
	}()

	ctxzap.AddFields(
		ctx,
		file_inner_processing_grpc_multiacquiring_proto_message_ThreeDSResumeRequestToZap(label+"request", request),
	)

	response, err = s.srv.ThreeDSResume(ctx, request)

	return
}

func (s *loggedMultiAcquiringServer) PayOut(
	ctx context.Context,
	request *PayOutRequestData,
) (
	response *PayOutResponseData,
	err error,
) {
	label := cntx.Begin(ctx, "MultiAcquiringServer_PayOut")
	defer cntx.End(ctx, label)

	defer func() {
		if err != nil {
			ctxzap.AddFields(ctx, zap.Any(label+"error", err))
			return
		}

		ctxzap.AddFields(
			ctx,
			file_inner_processing_grpc_multiacquiring_proto_message_PayOutResponseDataToZap(label+"response", response),
		)
	}()

	ctxzap.AddFields(
		ctx,
		file_inner_processing_grpc_multiacquiring_proto_message_PayOutRequestDataToZap(label+"request", request),
	)

	response, err = s.srv.PayOut(ctx, request)

	return
}

func (s *loggedMultiAcquiringServer) GetBankTransactionStatus(
	ctx context.Context,
	request *BankTransactionStatusRequest,
) (
	response *BankTransactionStatusResponse,
	err error,
) {
	label := cntx.Begin(ctx, "MultiAcquiringServer_GetBankTransactionStatus")
	defer cntx.End(ctx, label)

	defer func() {
		if err != nil {
			ctxzap.AddFields(ctx, zap.Any(label+"error", err))
			return
		}

		ctxzap.AddFields(
			ctx,
			file_inner_processing_grpc_multiacquiring_proto_message_BankTransactionStatusResponseToZap(label+"response", response),
		)
	}()

	ctxzap.AddFields(
		ctx,
		file_inner_processing_grpc_multiacquiring_proto_message_BankTransactionStatusRequestToZap(label+"request", request),
	)

	response, err = s.srv.GetBankTransactionStatus(ctx, request)

	return
}

func (s *loggedMultiAcquiringServer) GetBankTransactionStatusUnformated(
	ctx context.Context,
	request *BankTransactionStatusUnformatedRequest,
) (
	response *BankTransactionStatusUnformatedResponse,
	err error,
) {
	label := cntx.Begin(ctx, "MultiAcquiringServer_GetBankTransactionStatusUnformated")
	defer cntx.End(ctx, label)

	defer func() {
		if err != nil {
			ctxzap.AddFields(ctx, zap.Any(label+"error", err))
			return
		}

		ctxzap.AddFields(
			ctx,
			file_inner_processing_grpc_multiacquiring_proto_message_BankTransactionStatusUnformatedResponseToZap(label+"response", response),
		)
	}()

	ctxzap.AddFields(
		ctx,
		file_inner_processing_grpc_multiacquiring_proto_message_BankTransactionStatusUnformatedRequestToZap(label+"request", request),
	)

	response, err = s.srv.GetBankTransactionStatusUnformated(ctx, request)

	return
}

func (s *loggedMultiAcquiringServer) Refund(
	ctx context.Context,
	request *RefundRequest,
) (
	response *RefundResponse,
	err error,
) {
	label := cntx.Begin(ctx, "MultiAcquiringServer_Refund")
	defer cntx.End(ctx, label)

	defer func() {
		if err != nil {
			ctxzap.AddFields(ctx, zap.Any(label+"error", err))
			return
		}

		ctxzap.AddFields(
			ctx,
			file_inner_processing_grpc_multiacquiring_proto_message_RefundResponseToZap(label+"response", response),
		)
	}()

	ctxzap.AddFields(
		ctx,
		file_inner_processing_grpc_multiacquiring_proto_message_RefundRequestToZap(label+"request", request),
	)

	response, err = s.srv.Refund(ctx, request)

	return
}

func (s *loggedMultiAcquiringServer) GooglePay(
	ctx context.Context,
	request *GooglePayRequestData,
) (
	response *GooglePayResponseData,
	err error,
) {
	label := cntx.Begin(ctx, "MultiAcquiringServer_GooglePay")
	defer cntx.End(ctx, label)

	defer func() {
		if err != nil {
			ctxzap.AddFields(ctx, zap.Any(label+"error", err))
			return
		}

		ctxzap.AddFields(
			ctx,
			file_inner_processing_grpc_multiacquiring_proto_message_GooglePayResponseDataToZap(label+"response", response),
		)
	}()

	ctxzap.AddFields(
		ctx,
		file_inner_processing_grpc_multiacquiring_proto_message_GooglePayRequestDataToZap(label+"request", request),
	)

	response, err = s.srv.GooglePay(ctx, request)

	return
}

func (s *loggedMultiAcquiringServer) ApplePay(
	ctx context.Context,
	request *ApplePayRequestData,
) (
	response *ApplePayResponseData,
	err error,
) {
	label := cntx.Begin(ctx, "MultiAcquiringServer_ApplePay")
	defer cntx.End(ctx, label)

	defer func() {
		if err != nil {
			ctxzap.AddFields(ctx, zap.Any(label+"error", err))
			return
		}

		ctxzap.AddFields(
			ctx,
			file_inner_processing_grpc_multiacquiring_proto_message_ApplePayResponseDataToZap(label+"response", response),
		)
	}()

	ctxzap.AddFields(
		ctx,
		file_inner_processing_grpc_multiacquiring_proto_message_ApplePayRequestDataToZap(label+"request", request),
	)

	response, err = s.srv.ApplePay(ctx, request)

	return
}

func (s *loggedMultiAcquiringServer) TwoStagePayIn(
	ctx context.Context,
	request *TwoStagePayInRequest,
) (
	response *TwoStagePayInResponse,
	err error,
) {
	label := cntx.Begin(ctx, "MultiAcquiringServer_TwoStagePayIn")
	defer cntx.End(ctx, label)

	defer func() {
		if err != nil {
			ctxzap.AddFields(ctx, zap.Any(label+"error", err))
			return
		}

		ctxzap.AddFields(
			ctx,
			file_inner_processing_grpc_multiacquiring_proto_message_TwoStagePayInResponseToZap(label+"response", response),
		)
	}()

	ctxzap.AddFields(
		ctx,
		file_inner_processing_grpc_multiacquiring_proto_message_TwoStagePayInRequestToZap(label+"request", request),
	)

	response, err = s.srv.TwoStagePayIn(ctx, request)

	return
}

func (s *loggedMultiAcquiringServer) Charge(
	ctx context.Context,
	request *ChargeRequest,
) (
	response *ChargeResponse,
	err error,
) {
	label := cntx.Begin(ctx, "MultiAcquiringServer_Charge")
	defer cntx.End(ctx, label)

	defer func() {
		if err != nil {
			ctxzap.AddFields(ctx, zap.Any(label+"error", err))
			return
		}

		ctxzap.AddFields(
			ctx,
			file_inner_processing_grpc_multiacquiring_proto_message_ChargeResponseToZap(label+"response", response),
		)
	}()

	ctxzap.AddFields(
		ctx,
		file_inner_processing_grpc_multiacquiring_proto_message_ChargeRequestToZap(label+"request", request),
	)

	response, err = s.srv.Charge(ctx, request)

	return
}

func (s *loggedMultiAcquiringServer) Cancel(
	ctx context.Context,
	request *CancelRequest,
) (
	response *CancelResponse,
	err error,
) {
	label := cntx.Begin(ctx, "MultiAcquiringServer_Cancel")
	defer cntx.End(ctx, label)

	defer func() {
		if err != nil {
			ctxzap.AddFields(ctx, zap.Any(label+"error", err))
			return
		}

		ctxzap.AddFields(
			ctx,
			file_inner_processing_grpc_multiacquiring_proto_message_CancelResponseToZap(label+"response", response),
		)
	}()

	ctxzap.AddFields(
		ctx,
		file_inner_processing_grpc_multiacquiring_proto_message_CancelRequestToZap(label+"request", request),
	)

	response, err = s.srv.Cancel(ctx, request)

	return
}

func (s *loggedMultiAcquiringServer) MakeToken(
	ctx context.Context,
	request *PayInRequestData,
) (
	response *PayInResponseData,
	err error,
) {
	label := cntx.Begin(ctx, "MultiAcquiringServer_MakeToken")
	defer cntx.End(ctx, label)

	defer func() {
		if err != nil {
			ctxzap.AddFields(ctx, zap.Any(label+"error", err))
			return
		}

		ctxzap.AddFields(
			ctx,
			file_inner_processing_grpc_multiacquiring_proto_message_PayInResponseDataToZap(label+"response", response),
		)
	}()

	ctxzap.AddFields(
		ctx,
		file_inner_processing_grpc_multiacquiring_proto_message_PayInRequestDataToZap(label+"request", request),
	)

	response, err = s.srv.MakeToken(ctx, request)

	return
}

func (s *loggedMultiAcquiringServer) ResolveVisaAlias(
	ctx context.Context,
	request *ResolveVisaAliasRequest,
) (
	response *ResolveVisaAliasResponse,
	err error,
) {
	label := cntx.Begin(ctx, "MultiAcquiringServer_ResolveVisaAlias")
	defer cntx.End(ctx, label)

	defer func() {
		if err != nil {
			ctxzap.AddFields(ctx, zap.Any(label+"error", err))
			return
		}

		ctxzap.AddFields(
			ctx,
			file_inner_processing_grpc_multiacquiring_proto_message_ResolveVisaAliasResponseToZap(label+"response", response),
		)
	}()

	ctxzap.AddFields(
		ctx,
		file_inner_processing_grpc_multiacquiring_proto_message_ResolveVisaAliasRequestToZap(label+"request", request),
	)

	response, err = s.srv.ResolveVisaAlias(ctx, request)

	return
}

func (s *loggedMultiAcquiringServer) GetAcquirerIdentifier(
	ctx context.Context,
	request *emptypb.Empty,
) (
	response *GetAcquirerIdentifierResponse,
	err error,
) {
	label := cntx.Begin(ctx, "MultiAcquiringServer_GetAcquirerIdentifier")
	defer cntx.End(ctx, label)

	defer func() {
		if err != nil {
			ctxzap.AddFields(ctx, zap.Any(label+"error", err))
			return
		}

		ctxzap.AddFields(
			ctx,
			file_inner_processing_grpc_multiacquiring_proto_message_GetAcquirerIdentifierResponseToZap(label+"response", response),
		)
	}()

	ctxzap.AddFields(
		ctx,
		file_inner_processing_grpc_multiacquiring_proto_message_EmptyToZap(label+"request", request),
	)

	response, err = s.srv.GetAcquirerIdentifier(ctx, request)

	return
}

func (s *loggedMultiAcquiringServer) PayOutByPhone(
	ctx context.Context,
	request *PayOutByPhoneRequestData,
) (
	response *PayOutResponseByPhoneData,
	err error,
) {
	label := cntx.Begin(ctx, "MultiAcquiringServer_PayOutByPhone")
	defer cntx.End(ctx, label)

	defer func() {
		if err != nil {
			ctxzap.AddFields(ctx, zap.Any(label+"error", err))
			return
		}

		ctxzap.AddFields(
			ctx,
			file_inner_processing_grpc_multiacquiring_proto_message_PayOutResponseByPhoneDataToZap(label+"response", response),
		)
	}()

	ctxzap.AddFields(
		ctx,
		file_inner_processing_grpc_multiacquiring_proto_message_PayOutByPhoneRequestDataToZap(label+"request", request),
	)

	response, err = s.srv.PayOutByPhone(ctx, request)

	return
}

var _ MultiAcquiringClient = (*loggedMultiAcquiringClient)(nil)

func NewLoggedMultiAcquiringClient(client MultiAcquiringClient) MultiAcquiringClient {
	return &loggedMultiAcquiringClient{client: client}
}

type loggedMultiAcquiringClient struct {
	client MultiAcquiringClient
}

func (s *loggedMultiAcquiringClient) PayIn(
	ctx context.Context,
	request *PayInRequestData,
	opts ...grpc.CallOption,
) (
	response *PayInResponseData,
	err error,
) {
	label := cntx.Begin(ctx, "MultiAcquiringClient_PayIn")
	defer cntx.End(ctx, label)

	defer func() {
		if err != nil {
			ctxzap.AddFields(ctx, zap.Any(label+"error", err))
			return
		}

		ctxzap.AddFields(
			ctx,
			file_inner_processing_grpc_multiacquiring_proto_message_PayInResponseDataToZap(label+"response", response),
		)
	}()

	ctxzap.AddFields(
		ctx,
		file_inner_processing_grpc_multiacquiring_proto_message_PayInRequestDataToZap(label+"request", request),
	)

	response, err = s.client.PayIn(ctx, request, opts...)

	return
}

func (s *loggedMultiAcquiringClient) OneClickPayIn(
	ctx context.Context,
	request *OneClickPayInRequestData,
	opts ...grpc.CallOption,
) (
	response *PayInResponseData,
	err error,
) {
	label := cntx.Begin(ctx, "MultiAcquiringClient_OneClickPayIn")
	defer cntx.End(ctx, label)

	defer func() {
		if err != nil {
			ctxzap.AddFields(ctx, zap.Any(label+"error", err))
			return
		}

		ctxzap.AddFields(
			ctx,
			file_inner_processing_grpc_multiacquiring_proto_message_PayInResponseDataToZap(label+"response", response),
		)
	}()

	ctxzap.AddFields(
		ctx,
		file_inner_processing_grpc_multiacquiring_proto_message_OneClickPayInRequestDataToZap(label+"request", request),
	)

	response, err = s.client.OneClickPayIn(ctx, request, opts...)

	return
}

func (s *loggedMultiAcquiringClient) ThreeDSConfirm(
	ctx context.Context,
	request *ThreeDSRequestData,
	opts ...grpc.CallOption,
) (
	response *ThreeDSResponseData,
	err error,
) {
	label := cntx.Begin(ctx, "MultiAcquiringClient_ThreeDSConfirm")
	defer cntx.End(ctx, label)

	defer func() {
		if err != nil {
			ctxzap.AddFields(ctx, zap.Any(label+"error", err))
			return
		}

		ctxzap.AddFields(
			ctx,
			file_inner_processing_grpc_multiacquiring_proto_message_ThreeDSResponseDataToZap(label+"response", response),
		)
	}()

	ctxzap.AddFields(
		ctx,
		file_inner_processing_grpc_multiacquiring_proto_message_ThreeDSRequestDataToZap(label+"request", request),
	)

	response, err = s.client.ThreeDSConfirm(ctx, request, opts...)

	return
}

func (s *loggedMultiAcquiringClient) ThreeDSResume(
	ctx context.Context,
	request *ThreeDSResumeRequest,
	opts ...grpc.CallOption,
) (
	response *ThreeDSResumeResponse,
	err error,
) {
	label := cntx.Begin(ctx, "MultiAcquiringClient_ThreeDSResume")
	defer cntx.End(ctx, label)

	defer func() {
		if err != nil {
			ctxzap.AddFields(ctx, zap.Any(label+"error", err))
			return
		}

		ctxzap.AddFields(
			ctx,
			file_inner_processing_grpc_multiacquiring_proto_message_ThreeDSResumeResponseToZap(label+"response", response),
		)
	}()

	ctxzap.AddFields(
		ctx,
		file_inner_processing_grpc_multiacquiring_proto_message_ThreeDSResumeRequestToZap(label+"request", request),
	)

	response, err = s.client.ThreeDSResume(ctx, request, opts...)

	return
}

func (s *loggedMultiAcquiringClient) PayOut(
	ctx context.Context,
	request *PayOutRequestData,
	opts ...grpc.CallOption,
) (
	response *PayOutResponseData,
	err error,
) {
	label := cntx.Begin(ctx, "MultiAcquiringClient_PayOut")
	defer cntx.End(ctx, label)

	defer func() {
		if err != nil {
			ctxzap.AddFields(ctx, zap.Any(label+"error", err))
			return
		}

		ctxzap.AddFields(
			ctx,
			file_inner_processing_grpc_multiacquiring_proto_message_PayOutResponseDataToZap(label+"response", response),
		)
	}()

	ctxzap.AddFields(
		ctx,
		file_inner_processing_grpc_multiacquiring_proto_message_PayOutRequestDataToZap(label+"request", request),
	)

	response, err = s.client.PayOut(ctx, request, opts...)

	return
}

func (s *loggedMultiAcquiringClient) GetBankTransactionStatus(
	ctx context.Context,
	request *BankTransactionStatusRequest,
	opts ...grpc.CallOption,
) (
	response *BankTransactionStatusResponse,
	err error,
) {
	label := cntx.Begin(ctx, "MultiAcquiringClient_GetBankTransactionStatus")
	defer cntx.End(ctx, label)

	defer func() {
		if err != nil {
			ctxzap.AddFields(ctx, zap.Any(label+"error", err))
			return
		}

		ctxzap.AddFields(
			ctx,
			file_inner_processing_grpc_multiacquiring_proto_message_BankTransactionStatusResponseToZap(label+"response", response),
		)
	}()

	ctxzap.AddFields(
		ctx,
		file_inner_processing_grpc_multiacquiring_proto_message_BankTransactionStatusRequestToZap(label+"request", request),
	)

	response, err = s.client.GetBankTransactionStatus(ctx, request, opts...)

	return
}

func (s *loggedMultiAcquiringClient) GetBankTransactionStatusUnformated(
	ctx context.Context,
	request *BankTransactionStatusUnformatedRequest,
	opts ...grpc.CallOption,
) (
	response *BankTransactionStatusUnformatedResponse,
	err error,
) {
	label := cntx.Begin(ctx, "MultiAcquiringClient_GetBankTransactionStatusUnformated")
	defer cntx.End(ctx, label)

	defer func() {
		if err != nil {
			ctxzap.AddFields(ctx, zap.Any(label+"error", err))
			return
		}

		ctxzap.AddFields(
			ctx,
			file_inner_processing_grpc_multiacquiring_proto_message_BankTransactionStatusUnformatedResponseToZap(label+"response", response),
		)
	}()

	ctxzap.AddFields(
		ctx,
		file_inner_processing_grpc_multiacquiring_proto_message_BankTransactionStatusUnformatedRequestToZap(label+"request", request),
	)

	response, err = s.client.GetBankTransactionStatusUnformated(ctx, request, opts...)

	return
}

func (s *loggedMultiAcquiringClient) Refund(
	ctx context.Context,
	request *RefundRequest,
	opts ...grpc.CallOption,
) (
	response *RefundResponse,
	err error,
) {
	label := cntx.Begin(ctx, "MultiAcquiringClient_Refund")
	defer cntx.End(ctx, label)

	defer func() {
		if err != nil {
			ctxzap.AddFields(ctx, zap.Any(label+"error", err))
			return
		}

		ctxzap.AddFields(
			ctx,
			file_inner_processing_grpc_multiacquiring_proto_message_RefundResponseToZap(label+"response", response),
		)
	}()

	ctxzap.AddFields(
		ctx,
		file_inner_processing_grpc_multiacquiring_proto_message_RefundRequestToZap(label+"request", request),
	)

	response, err = s.client.Refund(ctx, request, opts...)

	return
}

func (s *loggedMultiAcquiringClient) GooglePay(
	ctx context.Context,
	request *GooglePayRequestData,
	opts ...grpc.CallOption,
) (
	response *GooglePayResponseData,
	err error,
) {
	label := cntx.Begin(ctx, "MultiAcquiringClient_GooglePay")
	defer cntx.End(ctx, label)

	defer func() {
		if err != nil {
			ctxzap.AddFields(ctx, zap.Any(label+"error", err))
			return
		}

		ctxzap.AddFields(
			ctx,
			file_inner_processing_grpc_multiacquiring_proto_message_GooglePayResponseDataToZap(label+"response", response),
		)
	}()

	ctxzap.AddFields(
		ctx,
		file_inner_processing_grpc_multiacquiring_proto_message_GooglePayRequestDataToZap(label+"request", request),
	)

	response, err = s.client.GooglePay(ctx, request, opts...)

	return
}

func (s *loggedMultiAcquiringClient) ApplePay(
	ctx context.Context,
	request *ApplePayRequestData,
	opts ...grpc.CallOption,
) (
	response *ApplePayResponseData,
	err error,
) {
	label := cntx.Begin(ctx, "MultiAcquiringClient_ApplePay")
	defer cntx.End(ctx, label)

	defer func() {
		if err != nil {
			ctxzap.AddFields(ctx, zap.Any(label+"error", err))
			return
		}

		ctxzap.AddFields(
			ctx,
			file_inner_processing_grpc_multiacquiring_proto_message_ApplePayResponseDataToZap(label+"response", response),
		)
	}()

	ctxzap.AddFields(
		ctx,
		file_inner_processing_grpc_multiacquiring_proto_message_ApplePayRequestDataToZap(label+"request", request),
	)

	response, err = s.client.ApplePay(ctx, request, opts...)

	return
}

func (s *loggedMultiAcquiringClient) TwoStagePayIn(
	ctx context.Context,
	request *TwoStagePayInRequest,
	opts ...grpc.CallOption,
) (
	response *TwoStagePayInResponse,
	err error,
) {
	label := cntx.Begin(ctx, "MultiAcquiringClient_TwoStagePayIn")
	defer cntx.End(ctx, label)

	defer func() {
		if err != nil {
			ctxzap.AddFields(ctx, zap.Any(label+"error", err))
			return
		}

		ctxzap.AddFields(
			ctx,
			file_inner_processing_grpc_multiacquiring_proto_message_TwoStagePayInResponseToZap(label+"response", response),
		)
	}()

	ctxzap.AddFields(
		ctx,
		file_inner_processing_grpc_multiacquiring_proto_message_TwoStagePayInRequestToZap(label+"request", request),
	)

	response, err = s.client.TwoStagePayIn(ctx, request, opts...)

	return
}

func (s *loggedMultiAcquiringClient) Charge(
	ctx context.Context,
	request *ChargeRequest,
	opts ...grpc.CallOption,
) (
	response *ChargeResponse,
	err error,
) {
	label := cntx.Begin(ctx, "MultiAcquiringClient_Charge")
	defer cntx.End(ctx, label)

	defer func() {
		if err != nil {
			ctxzap.AddFields(ctx, zap.Any(label+"error", err))
			return
		}

		ctxzap.AddFields(
			ctx,
			file_inner_processing_grpc_multiacquiring_proto_message_ChargeResponseToZap(label+"response", response),
		)
	}()

	ctxzap.AddFields(
		ctx,
		file_inner_processing_grpc_multiacquiring_proto_message_ChargeRequestToZap(label+"request", request),
	)

	response, err = s.client.Charge(ctx, request, opts...)

	return
}

func (s *loggedMultiAcquiringClient) Cancel(
	ctx context.Context,
	request *CancelRequest,
	opts ...grpc.CallOption,
) (
	response *CancelResponse,
	err error,
) {
	label := cntx.Begin(ctx, "MultiAcquiringClient_Cancel")
	defer cntx.End(ctx, label)

	defer func() {
		if err != nil {
			ctxzap.AddFields(ctx, zap.Any(label+"error", err))
			return
		}

		ctxzap.AddFields(
			ctx,
			file_inner_processing_grpc_multiacquiring_proto_message_CancelResponseToZap(label+"response", response),
		)
	}()

	ctxzap.AddFields(
		ctx,
		file_inner_processing_grpc_multiacquiring_proto_message_CancelRequestToZap(label+"request", request),
	)

	response, err = s.client.Cancel(ctx, request, opts...)

	return
}

func (s *loggedMultiAcquiringClient) MakeToken(
	ctx context.Context,
	request *PayInRequestData,
	opts ...grpc.CallOption,
) (
	response *PayInResponseData,
	err error,
) {
	label := cntx.Begin(ctx, "MultiAcquiringClient_MakeToken")
	defer cntx.End(ctx, label)

	defer func() {
		if err != nil {
			ctxzap.AddFields(ctx, zap.Any(label+"error", err))
			return
		}

		ctxzap.AddFields(
			ctx,
			file_inner_processing_grpc_multiacquiring_proto_message_PayInResponseDataToZap(label+"response", response),
		)
	}()

	ctxzap.AddFields(
		ctx,
		file_inner_processing_grpc_multiacquiring_proto_message_PayInRequestDataToZap(label+"request", request),
	)

	response, err = s.client.MakeToken(ctx, request, opts...)

	return
}

func (s *loggedMultiAcquiringClient) ResolveVisaAlias(
	ctx context.Context,
	request *ResolveVisaAliasRequest,
	opts ...grpc.CallOption,
) (
	response *ResolveVisaAliasResponse,
	err error,
) {
	label := cntx.Begin(ctx, "MultiAcquiringClient_ResolveVisaAlias")
	defer cntx.End(ctx, label)

	defer func() {
		if err != nil {
			ctxzap.AddFields(ctx, zap.Any(label+"error", err))
			return
		}

		ctxzap.AddFields(
			ctx,
			file_inner_processing_grpc_multiacquiring_proto_message_ResolveVisaAliasResponseToZap(label+"response", response),
		)
	}()

	ctxzap.AddFields(
		ctx,
		file_inner_processing_grpc_multiacquiring_proto_message_ResolveVisaAliasRequestToZap(label+"request", request),
	)

	response, err = s.client.ResolveVisaAlias(ctx, request, opts...)

	return
}

func (s *loggedMultiAcquiringClient) GetAcquirerIdentifier(
	ctx context.Context,
	request *emptypb.Empty,
	opts ...grpc.CallOption,
) (
	response *GetAcquirerIdentifierResponse,
	err error,
) {
	label := cntx.Begin(ctx, "MultiAcquiringClient_GetAcquirerIdentifier")
	defer cntx.End(ctx, label)

	defer func() {
		if err != nil {
			ctxzap.AddFields(ctx, zap.Any(label+"error", err))
			return
		}

		ctxzap.AddFields(
			ctx,
			file_inner_processing_grpc_multiacquiring_proto_message_GetAcquirerIdentifierResponseToZap(label+"response", response),
		)
	}()

	ctxzap.AddFields(
		ctx,
		file_inner_processing_grpc_multiacquiring_proto_message_EmptyToZap(label+"request", request),
	)

	response, err = s.client.GetAcquirerIdentifier(ctx, request, opts...)

	return
}

func (s *loggedMultiAcquiringClient) PayOutByPhone(
	ctx context.Context,
	request *PayOutByPhoneRequestData,
	opts ...grpc.CallOption,
) (
	response *PayOutResponseByPhoneData,
	err error,
) {
	label := cntx.Begin(ctx, "MultiAcquiringClient_PayOutByPhone")
	defer cntx.End(ctx, label)

	defer func() {
		if err != nil {
			ctxzap.AddFields(ctx, zap.Any(label+"error", err))
			return
		}

		ctxzap.AddFields(
			ctx,
			file_inner_processing_grpc_multiacquiring_proto_message_PayOutResponseByPhoneDataToZap(label+"response", response),
		)
	}()

	ctxzap.AddFields(
		ctx,
		file_inner_processing_grpc_multiacquiring_proto_message_PayOutByPhoneRequestDataToZap(label+"request", request),
	)

	response, err = s.client.PayOutByPhone(ctx, request, opts...)

	return
}
