// Code generated by protoc-gen-go-mvp. DO NOT EDIT.
// versions:
// - protoc-gen-go-mvp v2.0.1
// created by mvp authors

package grpc

import (
	context "context"
	grpc "google.golang.org/grpc"
	metadata "google.golang.org/grpc/metadata"
)

func NewIamKalkanServer(
	srv <PERSON>Server,
) KalkanServer {
	return &iamKalkanServer{
		srv: srv,
	}
}

var _ KalkanServer = (*iamKalkanServer)(nil)

type iamKalkanServer struct {
	UnimplementedKalkanServer

	srv KalkanServer
}

func (s *iamKalkanServer) MakeSignatureV1(
	ctx context.Context,
	req *MakeSignatureRequestV1,
) (
	*MakeSignatureResponseV1,
	error,
) {
	return s.srv.MakeSignatureV1(ctx, req)
}

func NewIamKalkanClient(
	client KalkanClient,
) KalkanClient {
	return &iamKalkanClient{
		client: client,
	}
}

type iamKalkanClient struct {
	client KalkanClient
}

func (s *iamKalkanClient) MakeSignatureV1(
	ctx context.Context,
	req *MakeSignatureRequestV1,
	opts ...grpc.CallOption,
) (
	*MakeSignatureResponseV1,
	error,
) {
	md, _ := metadata.FromIncomingContext(ctx)
	return s.client.MakeSignatureV1(metadata.NewOutgoingContext(ctx, md), req)
}
