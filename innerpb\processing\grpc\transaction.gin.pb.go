// Code generated by protoc-gen-go-mvp. DO NOT EDIT.
// versions:
// - protoc-gen-go-mvp v2.0.1
// created by mvp authors

package grpc

import (
	dog "git.local/sensitive/sdk/dog"
	gin "github.com/gin-gonic/gin"
	metadata "google.golang.org/grpc/metadata"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
	http "net/http"
)

func RegisterGinTransactionRouterGroup(
	in *gin.RouterGroup,
	handler func(func(c *gin.Context) error) gin.HandlerFunc,
) error {
	service, err := NewGinTransactionService()
	if err != nil {
		return err
	}

	routerGroup := in.Group("processing.transaction.transaction.Transaction")
	routerGroup.PUT("/UpdateTransactionStatus", handler(service.UpdateTransactionStatus))
	routerGroup.PUT("/UpdateCallbackStatus", handler(service.UpdateCallbackStatus))
	routerGroup.PUT("/GetTransactionsByPeriodAndStatus", handler(service.GetTransactionsByPeriodAndStatus))
	routerGroup.PUT("/GetTransactionsByPeriodAndCallbackStatus", handler(service.GetTransactionsByPeriodAndCallbackStatus))
	routerGroup.PUT("/GetTransactionsByFinalStatusAndPeriodWithLimit", handler(service.GetTransactionsByFinalStatusAndPeriodWithLimit))
	routerGroup.PUT("/GetPayInTransactionsByPeriodAndAcquirer", handler(service.GetPayInTransactionsByPeriodAndAcquirer))
	routerGroup.PUT("/GetTransactionTypeByID", handler(service.GetTransactionTypeByID))
	routerGroup.PUT("/GetTransactionByID", handler(service.GetTransactionByID))
	routerGroup.PUT("/MakeAutoCharge", handler(service.MakeAutoCharge))
	routerGroup.PUT("/SetRefundWaitingStatus", handler(service.SetRefundWaitingStatus))
	routerGroup.PUT("/GetTransactionsByProjectInfo", handler(service.GetTransactionsByProjectInfo))
	routerGroup.PUT("/CheckTransactionHash", handler(service.CheckTransactionHash))
	routerGroup.PUT("/CreateTransactionByPhone", handler(service.CreateTransactionByPhone))
	routerGroup.PUT("/GetByIDWithType", handler(service.GetByIDWithType))
	routerGroup.PUT("/UpdateStatus", handler(service.UpdateStatus))
	routerGroup.PUT("/IncreaseTryCount", handler(service.IncreaseTryCount))
	routerGroup.PUT("/SaveAcquirerResponse", handler(service.SaveAcquirerResponse))
	routerGroup.PUT("/SendReceipt", handler(service.SendReceipt))
	routerGroup.PUT("/SetAdditionalData", handler(service.SetAdditionalData))
	routerGroup.PUT("/CalculateAndUpdateTransactionAmount", handler(service.CalculateAndUpdateTransactionAmount))
	routerGroup.PUT("/BillPayOut", handler(service.BillPayOut))
	return nil
}

func NewGinTransactionService() (GinTransactionServer, error) {
	client, err := NewPreparedTransactionClient()
	if err != nil {
		return nil, err
	}

	return &ginTransactionServer{
		client: NewLoggedTransactionClient(
			NewIamTransactionClient(client),
		),
	}, nil
}

//go:generate mockgen -destination=./grpcmock/transaction.gin.pb.go -package=grpcmock -source=transaction.gin.pb.go GinTransactionServer
type GinTransactionServer interface {
	UpdateTransactionStatus(c *gin.Context) error
	UpdateCallbackStatus(c *gin.Context) error
	GetTransactionsByPeriodAndStatus(c *gin.Context) error
	GetTransactionsByPeriodAndCallbackStatus(c *gin.Context) error
	GetTransactionsByFinalStatusAndPeriodWithLimit(c *gin.Context) error
	GetPayInTransactionsByPeriodAndAcquirer(c *gin.Context) error
	GetTransactionTypeByID(c *gin.Context) error
	GetTransactionByID(c *gin.Context) error
	MakeAutoCharge(c *gin.Context) error
	SetRefundWaitingStatus(c *gin.Context) error
	GetTransactionsByProjectInfo(c *gin.Context) error
	CheckTransactionHash(c *gin.Context) error
	CreateTransactionByPhone(c *gin.Context) error
	GetByIDWithType(c *gin.Context) error
	UpdateStatus(c *gin.Context) error
	IncreaseTryCount(c *gin.Context) error
	SaveAcquirerResponse(c *gin.Context) error
	SendReceipt(c *gin.Context) error
	SetAdditionalData(c *gin.Context) error
	CalculateAndUpdateTransactionAmount(c *gin.Context) error
	BillPayOut(c *gin.Context) error
}

var _ GinTransactionServer = (*ginTransactionServer)(nil)

type ginTransactionServer struct {
	client TransactionClient
}

type Transaction_UpdateTransactionStatus_Success struct {
	// Status is the status of the response. Set true
	Status bool `json:"status"`
	// Message is a human-readable explanation of the status
	Message string `json:"message"`
	// StatusCode is the GoErr status. Set 0
	StatusCode int `json:"status_code"`
	// Result is the result of the request
	Result *emptypb.Empty `json:"result"`
}

type Transaction_UpdateTransactionStatus_Failure struct {
	// Status is the status of the response. Set false
	Status bool `json:"status"`
	// Message is a human-readable explanation of the status
	Message string `json:"message"`
	// StatusCode is the GoErr status
	StatusCode int `json:"status_code"`
}

// UpdateTransactionStatus
// @Summary UpdateTransactionStatus
// @Security bearerAuth
// @ID Transaction_UpdateTransactionStatus
// @Accept json
// @Param request body UpdateTransactionStatusRequestV1 true "UpdateTransactionStatusRequestV1"
// @Success 200 {object} Transaction_UpdateTransactionStatus_Success
// @Failure 401 {object} Transaction_UpdateTransactionStatus_Failure "goerrpkg.ErrorTypeAuthorization"
// @Failure 404 {object} Transaction_UpdateTransactionStatus_Failure "goerrpkg.ErrorTypeNotFound"
// @Failure 400 {object} Transaction_UpdateTransactionStatus_Failure "goerrpkg.ErrorTypeBadRequest"
// @Failure 500 {object} Transaction_UpdateTransactionStatus_Failure "goerrpkg.ErrorTypeUnexpectedError"
// @Failure 400 {object} Transaction_UpdateTransactionStatus_Failure "gin.ErrorTypeBind"
// @Failure 500 {object} Transaction_UpdateTransactionStatus_Failure "Undefined error"
// @Produce json
// @Router /processing.transaction.transaction.Transaction/UpdateTransactionStatus [put]
// @tags Transaction
func (s *ginTransactionServer) UpdateTransactionStatus(c *gin.Context) error {
	ctx, span := dog.CreateSpan(c.Request.Context(), "GinTransactionServer_UpdateTransactionStatus")
	defer span.End()

	var request UpdateTransactionStatusRequestV1
	if err := c.ShouldBindJSON(&request); err != nil {
		return err
	}

	ctx = metadata.NewOutgoingContext(ctx, metadata.MD(c.Request.Header))

	response, err := s.client.UpdateTransactionStatus(ctx, &request)
	if err != nil {
		return err
	}

	c.JSON(http.StatusOK, &Transaction_UpdateTransactionStatus_Success{
		Status:     true,
		Message:    "Success",
		StatusCode: 0,
		Result:     response,
	})
	return nil
}

type Transaction_UpdateCallbackStatus_Success struct {
	// Status is the status of the response. Set true
	Status bool `json:"status"`
	// Message is a human-readable explanation of the status
	Message string `json:"message"`
	// StatusCode is the GoErr status. Set 0
	StatusCode int `json:"status_code"`
	// Result is the result of the request
	Result *emptypb.Empty `json:"result"`
}

type Transaction_UpdateCallbackStatus_Failure struct {
	// Status is the status of the response. Set false
	Status bool `json:"status"`
	// Message is a human-readable explanation of the status
	Message string `json:"message"`
	// StatusCode is the GoErr status
	StatusCode int `json:"status_code"`
}

// UpdateCallbackStatus
// @Summary UpdateCallbackStatus
// @Security bearerAuth
// @ID Transaction_UpdateCallbackStatus
// @Accept json
// @Param request body UpdateCallbackStatusRequestV1 true "UpdateCallbackStatusRequestV1"
// @Success 200 {object} Transaction_UpdateCallbackStatus_Success
// @Failure 401 {object} Transaction_UpdateCallbackStatus_Failure "goerrpkg.ErrorTypeAuthorization"
// @Failure 404 {object} Transaction_UpdateCallbackStatus_Failure "goerrpkg.ErrorTypeNotFound"
// @Failure 400 {object} Transaction_UpdateCallbackStatus_Failure "goerrpkg.ErrorTypeBadRequest"
// @Failure 500 {object} Transaction_UpdateCallbackStatus_Failure "goerrpkg.ErrorTypeUnexpectedError"
// @Failure 400 {object} Transaction_UpdateCallbackStatus_Failure "gin.ErrorTypeBind"
// @Failure 500 {object} Transaction_UpdateCallbackStatus_Failure "Undefined error"
// @Produce json
// @Router /processing.transaction.transaction.Transaction/UpdateCallbackStatus [put]
// @tags Transaction
func (s *ginTransactionServer) UpdateCallbackStatus(c *gin.Context) error {
	ctx, span := dog.CreateSpan(c.Request.Context(), "GinTransactionServer_UpdateCallbackStatus")
	defer span.End()

	var request UpdateCallbackStatusRequestV1
	if err := c.ShouldBindJSON(&request); err != nil {
		return err
	}

	ctx = metadata.NewOutgoingContext(ctx, metadata.MD(c.Request.Header))

	response, err := s.client.UpdateCallbackStatus(ctx, &request)
	if err != nil {
		return err
	}

	c.JSON(http.StatusOK, &Transaction_UpdateCallbackStatus_Success{
		Status:     true,
		Message:    "Success",
		StatusCode: 0,
		Result:     response,
	})
	return nil
}

type Transaction_GetTransactionsByPeriodAndStatus_Success struct {
	// Status is the status of the response. Set true
	Status bool `json:"status"`
	// Message is a human-readable explanation of the status
	Message string `json:"message"`
	// StatusCode is the GoErr status. Set 0
	StatusCode int `json:"status_code"`
	// Result is the result of the request
	Result *GetTransactionsResponse `json:"result"`
}

type Transaction_GetTransactionsByPeriodAndStatus_Failure struct {
	// Status is the status of the response. Set false
	Status bool `json:"status"`
	// Message is a human-readable explanation of the status
	Message string `json:"message"`
	// StatusCode is the GoErr status
	StatusCode int `json:"status_code"`
}

// GetTransactionsByPeriodAndStatus
// @Summary GetTransactionsByPeriodAndStatus
// @Security bearerAuth
// @ID Transaction_GetTransactionsByPeriodAndStatus
// @Accept json
// @Param request body GetTransactionsByStatusReqV1 true "GetTransactionsByStatusReqV1"
// @Success 200 {object} Transaction_GetTransactionsByPeriodAndStatus_Success
// @Failure 401 {object} Transaction_GetTransactionsByPeriodAndStatus_Failure "goerrpkg.ErrorTypeAuthorization"
// @Failure 404 {object} Transaction_GetTransactionsByPeriodAndStatus_Failure "goerrpkg.ErrorTypeNotFound"
// @Failure 400 {object} Transaction_GetTransactionsByPeriodAndStatus_Failure "goerrpkg.ErrorTypeBadRequest"
// @Failure 500 {object} Transaction_GetTransactionsByPeriodAndStatus_Failure "goerrpkg.ErrorTypeUnexpectedError"
// @Failure 400 {object} Transaction_GetTransactionsByPeriodAndStatus_Failure "gin.ErrorTypeBind"
// @Failure 500 {object} Transaction_GetTransactionsByPeriodAndStatus_Failure "Undefined error"
// @Produce json
// @Router /processing.transaction.transaction.Transaction/GetTransactionsByPeriodAndStatus [put]
// @tags Transaction
func (s *ginTransactionServer) GetTransactionsByPeriodAndStatus(c *gin.Context) error {
	ctx, span := dog.CreateSpan(c.Request.Context(), "GinTransactionServer_GetTransactionsByPeriodAndStatus")
	defer span.End()

	var request GetTransactionsByStatusReqV1
	if err := c.ShouldBindJSON(&request); err != nil {
		return err
	}

	ctx = metadata.NewOutgoingContext(ctx, metadata.MD(c.Request.Header))

	response, err := s.client.GetTransactionsByPeriodAndStatus(ctx, &request)
	if err != nil {
		return err
	}

	c.JSON(http.StatusOK, &Transaction_GetTransactionsByPeriodAndStatus_Success{
		Status:     true,
		Message:    "Success",
		StatusCode: 0,
		Result:     response,
	})
	return nil
}

type Transaction_GetTransactionsByPeriodAndCallbackStatus_Success struct {
	// Status is the status of the response. Set true
	Status bool `json:"status"`
	// Message is a human-readable explanation of the status
	Message string `json:"message"`
	// StatusCode is the GoErr status. Set 0
	StatusCode int `json:"status_code"`
	// Result is the result of the request
	Result *GetTransactionsResponse `json:"result"`
}

type Transaction_GetTransactionsByPeriodAndCallbackStatus_Failure struct {
	// Status is the status of the response. Set false
	Status bool `json:"status"`
	// Message is a human-readable explanation of the status
	Message string `json:"message"`
	// StatusCode is the GoErr status
	StatusCode int `json:"status_code"`
}

// GetTransactionsByPeriodAndCallbackStatus
// @Summary GetTransactionsByPeriodAndCallbackStatus
// @Security bearerAuth
// @ID Transaction_GetTransactionsByPeriodAndCallbackStatus
// @Accept json
// @Param request body GetTransactionsByCallbackStatusReqV1 true "GetTransactionsByCallbackStatusReqV1"
// @Success 200 {object} Transaction_GetTransactionsByPeriodAndCallbackStatus_Success
// @Failure 401 {object} Transaction_GetTransactionsByPeriodAndCallbackStatus_Failure "goerrpkg.ErrorTypeAuthorization"
// @Failure 404 {object} Transaction_GetTransactionsByPeriodAndCallbackStatus_Failure "goerrpkg.ErrorTypeNotFound"
// @Failure 400 {object} Transaction_GetTransactionsByPeriodAndCallbackStatus_Failure "goerrpkg.ErrorTypeBadRequest"
// @Failure 500 {object} Transaction_GetTransactionsByPeriodAndCallbackStatus_Failure "goerrpkg.ErrorTypeUnexpectedError"
// @Failure 400 {object} Transaction_GetTransactionsByPeriodAndCallbackStatus_Failure "gin.ErrorTypeBind"
// @Failure 500 {object} Transaction_GetTransactionsByPeriodAndCallbackStatus_Failure "Undefined error"
// @Produce json
// @Router /processing.transaction.transaction.Transaction/GetTransactionsByPeriodAndCallbackStatus [put]
// @tags Transaction
func (s *ginTransactionServer) GetTransactionsByPeriodAndCallbackStatus(c *gin.Context) error {
	ctx, span := dog.CreateSpan(c.Request.Context(), "GinTransactionServer_GetTransactionsByPeriodAndCallbackStatus")
	defer span.End()

	var request GetTransactionsByCallbackStatusReqV1
	if err := c.ShouldBindJSON(&request); err != nil {
		return err
	}

	ctx = metadata.NewOutgoingContext(ctx, metadata.MD(c.Request.Header))

	response, err := s.client.GetTransactionsByPeriodAndCallbackStatus(ctx, &request)
	if err != nil {
		return err
	}

	c.JSON(http.StatusOK, &Transaction_GetTransactionsByPeriodAndCallbackStatus_Success{
		Status:     true,
		Message:    "Success",
		StatusCode: 0,
		Result:     response,
	})
	return nil
}

type Transaction_GetTransactionsByFinalStatusAndPeriodWithLimit_Success struct {
	// Status is the status of the response. Set true
	Status bool `json:"status"`
	// Message is a human-readable explanation of the status
	Message string `json:"message"`
	// StatusCode is the GoErr status. Set 0
	StatusCode int `json:"status_code"`
	// Result is the result of the request
	Result *GetTransactionsResponse `json:"result"`
}

type Transaction_GetTransactionsByFinalStatusAndPeriodWithLimit_Failure struct {
	// Status is the status of the response. Set false
	Status bool `json:"status"`
	// Message is a human-readable explanation of the status
	Message string `json:"message"`
	// StatusCode is the GoErr status
	StatusCode int `json:"status_code"`
}

// GetTransactionsByFinalStatusAndPeriodWithLimit
// @Summary GetTransactionsByFinalStatusAndPeriodWithLimit
// @Security bearerAuth
// @ID Transaction_GetTransactionsByFinalStatusAndPeriodWithLimit
// @Accept json
// @Param request body GetTransactionsByFinalStatusAndPeriodWithLimitReqV1 true "GetTransactionsByFinalStatusAndPeriodWithLimitReqV1"
// @Success 200 {object} Transaction_GetTransactionsByFinalStatusAndPeriodWithLimit_Success
// @Failure 401 {object} Transaction_GetTransactionsByFinalStatusAndPeriodWithLimit_Failure "goerrpkg.ErrorTypeAuthorization"
// @Failure 404 {object} Transaction_GetTransactionsByFinalStatusAndPeriodWithLimit_Failure "goerrpkg.ErrorTypeNotFound"
// @Failure 400 {object} Transaction_GetTransactionsByFinalStatusAndPeriodWithLimit_Failure "goerrpkg.ErrorTypeBadRequest"
// @Failure 500 {object} Transaction_GetTransactionsByFinalStatusAndPeriodWithLimit_Failure "goerrpkg.ErrorTypeUnexpectedError"
// @Failure 400 {object} Transaction_GetTransactionsByFinalStatusAndPeriodWithLimit_Failure "gin.ErrorTypeBind"
// @Failure 500 {object} Transaction_GetTransactionsByFinalStatusAndPeriodWithLimit_Failure "Undefined error"
// @Produce json
// @Router /processing.transaction.transaction.Transaction/GetTransactionsByFinalStatusAndPeriodWithLimit [put]
// @tags Transaction
func (s *ginTransactionServer) GetTransactionsByFinalStatusAndPeriodWithLimit(c *gin.Context) error {
	ctx, span := dog.CreateSpan(c.Request.Context(), "GinTransactionServer_GetTransactionsByFinalStatusAndPeriodWithLimit")
	defer span.End()

	var request GetTransactionsByFinalStatusAndPeriodWithLimitReqV1
	if err := c.ShouldBindJSON(&request); err != nil {
		return err
	}

	ctx = metadata.NewOutgoingContext(ctx, metadata.MD(c.Request.Header))

	response, err := s.client.GetTransactionsByFinalStatusAndPeriodWithLimit(ctx, &request)
	if err != nil {
		return err
	}

	c.JSON(http.StatusOK, &Transaction_GetTransactionsByFinalStatusAndPeriodWithLimit_Success{
		Status:     true,
		Message:    "Success",
		StatusCode: 0,
		Result:     response,
	})
	return nil
}

type Transaction_GetPayInTransactionsByPeriodAndAcquirer_Success struct {
	// Status is the status of the response. Set true
	Status bool `json:"status"`
	// Message is a human-readable explanation of the status
	Message string `json:"message"`
	// StatusCode is the GoErr status. Set 0
	StatusCode int `json:"status_code"`
	// Result is the result of the request
	Result *GetTransactionsResponse `json:"result"`
}

type Transaction_GetPayInTransactionsByPeriodAndAcquirer_Failure struct {
	// Status is the status of the response. Set false
	Status bool `json:"status"`
	// Message is a human-readable explanation of the status
	Message string `json:"message"`
	// StatusCode is the GoErr status
	StatusCode int `json:"status_code"`
}

// GetPayInTransactionsByPeriodAndAcquirer
// @Summary GetPayInTransactionsByPeriodAndAcquirer
// @Security bearerAuth
// @ID Transaction_GetPayInTransactionsByPeriodAndAcquirer
// @Accept json
// @Param request body GetPayInTransactionsByPeriodAndAcquirerReqV1 true "GetPayInTransactionsByPeriodAndAcquirerReqV1"
// @Success 200 {object} Transaction_GetPayInTransactionsByPeriodAndAcquirer_Success
// @Failure 401 {object} Transaction_GetPayInTransactionsByPeriodAndAcquirer_Failure "goerrpkg.ErrorTypeAuthorization"
// @Failure 404 {object} Transaction_GetPayInTransactionsByPeriodAndAcquirer_Failure "goerrpkg.ErrorTypeNotFound"
// @Failure 400 {object} Transaction_GetPayInTransactionsByPeriodAndAcquirer_Failure "goerrpkg.ErrorTypeBadRequest"
// @Failure 500 {object} Transaction_GetPayInTransactionsByPeriodAndAcquirer_Failure "goerrpkg.ErrorTypeUnexpectedError"
// @Failure 400 {object} Transaction_GetPayInTransactionsByPeriodAndAcquirer_Failure "gin.ErrorTypeBind"
// @Failure 500 {object} Transaction_GetPayInTransactionsByPeriodAndAcquirer_Failure "Undefined error"
// @Produce json
// @Router /processing.transaction.transaction.Transaction/GetPayInTransactionsByPeriodAndAcquirer [put]
// @tags Transaction
func (s *ginTransactionServer) GetPayInTransactionsByPeriodAndAcquirer(c *gin.Context) error {
	ctx, span := dog.CreateSpan(c.Request.Context(), "GinTransactionServer_GetPayInTransactionsByPeriodAndAcquirer")
	defer span.End()

	var request GetPayInTransactionsByPeriodAndAcquirerReqV1
	if err := c.ShouldBindJSON(&request); err != nil {
		return err
	}

	ctx = metadata.NewOutgoingContext(ctx, metadata.MD(c.Request.Header))

	response, err := s.client.GetPayInTransactionsByPeriodAndAcquirer(ctx, &request)
	if err != nil {
		return err
	}

	c.JSON(http.StatusOK, &Transaction_GetPayInTransactionsByPeriodAndAcquirer_Success{
		Status:     true,
		Message:    "Success",
		StatusCode: 0,
		Result:     response,
	})
	return nil
}

type Transaction_GetTransactionTypeByID_Success struct {
	// Status is the status of the response. Set true
	Status bool `json:"status"`
	// Message is a human-readable explanation of the status
	Message string `json:"message"`
	// StatusCode is the GoErr status. Set 0
	StatusCode int `json:"status_code"`
	// Result is the result of the request
	Result *GetTransactionTypeByIDResponseV1 `json:"result"`
}

type Transaction_GetTransactionTypeByID_Failure struct {
	// Status is the status of the response. Set false
	Status bool `json:"status"`
	// Message is a human-readable explanation of the status
	Message string `json:"message"`
	// StatusCode is the GoErr status
	StatusCode int `json:"status_code"`
}

// GetTransactionTypeByID
// @Summary GetTransactionTypeByID
// @Security bearerAuth
// @ID Transaction_GetTransactionTypeByID
// @Accept json
// @Param request body GetTransactionTypeByIDRequestV1 true "GetTransactionTypeByIDRequestV1"
// @Success 200 {object} Transaction_GetTransactionTypeByID_Success
// @Failure 401 {object} Transaction_GetTransactionTypeByID_Failure "goerrpkg.ErrorTypeAuthorization"
// @Failure 404 {object} Transaction_GetTransactionTypeByID_Failure "goerrpkg.ErrorTypeNotFound"
// @Failure 400 {object} Transaction_GetTransactionTypeByID_Failure "goerrpkg.ErrorTypeBadRequest"
// @Failure 500 {object} Transaction_GetTransactionTypeByID_Failure "goerrpkg.ErrorTypeUnexpectedError"
// @Failure 400 {object} Transaction_GetTransactionTypeByID_Failure "gin.ErrorTypeBind"
// @Failure 500 {object} Transaction_GetTransactionTypeByID_Failure "Undefined error"
// @Produce json
// @Router /processing.transaction.transaction.Transaction/GetTransactionTypeByID [put]
// @tags Transaction
func (s *ginTransactionServer) GetTransactionTypeByID(c *gin.Context) error {
	ctx, span := dog.CreateSpan(c.Request.Context(), "GinTransactionServer_GetTransactionTypeByID")
	defer span.End()

	var request GetTransactionTypeByIDRequestV1
	if err := c.ShouldBindJSON(&request); err != nil {
		return err
	}

	ctx = metadata.NewOutgoingContext(ctx, metadata.MD(c.Request.Header))

	response, err := s.client.GetTransactionTypeByID(ctx, &request)
	if err != nil {
		return err
	}

	c.JSON(http.StatusOK, &Transaction_GetTransactionTypeByID_Success{
		Status:     true,
		Message:    "Success",
		StatusCode: 0,
		Result:     response,
	})
	return nil
}

type Transaction_GetTransactionByID_Success struct {
	// Status is the status of the response. Set true
	Status bool `json:"status"`
	// Message is a human-readable explanation of the status
	Message string `json:"message"`
	// StatusCode is the GoErr status. Set 0
	StatusCode int `json:"status_code"`
	// Result is the result of the request
	Result *TransactionDataV1 `json:"result"`
}

type Transaction_GetTransactionByID_Failure struct {
	// Status is the status of the response. Set false
	Status bool `json:"status"`
	// Message is a human-readable explanation of the status
	Message string `json:"message"`
	// StatusCode is the GoErr status
	StatusCode int `json:"status_code"`
}

// GetTransactionByID
// @Summary GetTransactionByID
// @Security bearerAuth
// @ID Transaction_GetTransactionByID
// @Accept json
// @Param request body GetTransactionByIDRequestV1 true "GetTransactionByIDRequestV1"
// @Success 200 {object} Transaction_GetTransactionByID_Success
// @Failure 401 {object} Transaction_GetTransactionByID_Failure "goerrpkg.ErrorTypeAuthorization"
// @Failure 404 {object} Transaction_GetTransactionByID_Failure "goerrpkg.ErrorTypeNotFound"
// @Failure 400 {object} Transaction_GetTransactionByID_Failure "goerrpkg.ErrorTypeBadRequest"
// @Failure 500 {object} Transaction_GetTransactionByID_Failure "goerrpkg.ErrorTypeUnexpectedError"
// @Failure 400 {object} Transaction_GetTransactionByID_Failure "gin.ErrorTypeBind"
// @Failure 500 {object} Transaction_GetTransactionByID_Failure "Undefined error"
// @Produce json
// @Router /processing.transaction.transaction.Transaction/GetTransactionByID [put]
// @tags Transaction
func (s *ginTransactionServer) GetTransactionByID(c *gin.Context) error {
	ctx, span := dog.CreateSpan(c.Request.Context(), "GinTransactionServer_GetTransactionByID")
	defer span.End()

	var request GetTransactionByIDRequestV1
	if err := c.ShouldBindJSON(&request); err != nil {
		return err
	}

	ctx = metadata.NewOutgoingContext(ctx, metadata.MD(c.Request.Header))

	response, err := s.client.GetTransactionByID(ctx, &request)
	if err != nil {
		return err
	}

	c.JSON(http.StatusOK, &Transaction_GetTransactionByID_Success{
		Status:     true,
		Message:    "Success",
		StatusCode: 0,
		Result:     response,
	})
	return nil
}

type Transaction_MakeAutoCharge_Success struct {
	// Status is the status of the response. Set true
	Status bool `json:"status"`
	// Message is a human-readable explanation of the status
	Message string `json:"message"`
	// StatusCode is the GoErr status. Set 0
	StatusCode int `json:"status_code"`
	// Result is the result of the request
	Result *emptypb.Empty `json:"result"`
}

type Transaction_MakeAutoCharge_Failure struct {
	// Status is the status of the response. Set false
	Status bool `json:"status"`
	// Message is a human-readable explanation of the status
	Message string `json:"message"`
	// StatusCode is the GoErr status
	StatusCode int `json:"status_code"`
}

// MakeAutoCharge
// @Summary MakeAutoCharge
// @Security bearerAuth
// @ID Transaction_MakeAutoCharge
// @Accept json
// @Param request body MakeAutoChargeRequestV1 true "MakeAutoChargeRequestV1"
// @Success 200 {object} Transaction_MakeAutoCharge_Success
// @Failure 401 {object} Transaction_MakeAutoCharge_Failure "goerrpkg.ErrorTypeAuthorization"
// @Failure 404 {object} Transaction_MakeAutoCharge_Failure "goerrpkg.ErrorTypeNotFound"
// @Failure 400 {object} Transaction_MakeAutoCharge_Failure "goerrpkg.ErrorTypeBadRequest"
// @Failure 500 {object} Transaction_MakeAutoCharge_Failure "goerrpkg.ErrorTypeUnexpectedError"
// @Failure 400 {object} Transaction_MakeAutoCharge_Failure "gin.ErrorTypeBind"
// @Failure 500 {object} Transaction_MakeAutoCharge_Failure "Undefined error"
// @Produce json
// @Router /processing.transaction.transaction.Transaction/MakeAutoCharge [put]
// @tags Transaction
func (s *ginTransactionServer) MakeAutoCharge(c *gin.Context) error {
	ctx, span := dog.CreateSpan(c.Request.Context(), "GinTransactionServer_MakeAutoCharge")
	defer span.End()

	var request MakeAutoChargeRequestV1
	if err := c.ShouldBindJSON(&request); err != nil {
		return err
	}

	ctx = metadata.NewOutgoingContext(ctx, metadata.MD(c.Request.Header))

	response, err := s.client.MakeAutoCharge(ctx, &request)
	if err != nil {
		return err
	}

	c.JSON(http.StatusOK, &Transaction_MakeAutoCharge_Success{
		Status:     true,
		Message:    "Success",
		StatusCode: 0,
		Result:     response,
	})
	return nil
}

type Transaction_SetRefundWaitingStatus_Success struct {
	// Status is the status of the response. Set true
	Status bool `json:"status"`
	// Message is a human-readable explanation of the status
	Message string `json:"message"`
	// StatusCode is the GoErr status. Set 0
	StatusCode int `json:"status_code"`
	// Result is the result of the request
	Result *SetRefundWaitingStatusResponseV1 `json:"result"`
}

type Transaction_SetRefundWaitingStatus_Failure struct {
	// Status is the status of the response. Set false
	Status bool `json:"status"`
	// Message is a human-readable explanation of the status
	Message string `json:"message"`
	// StatusCode is the GoErr status
	StatusCode int `json:"status_code"`
}

// SetRefundWaitingStatus
// @Summary SetRefundWaitingStatus
// @Security bearerAuth
// @ID Transaction_SetRefundWaitingStatus
// @Accept json
// @Param request body SetRefundWaitingStatusRequestV1 true "SetRefundWaitingStatusRequestV1"
// @Success 200 {object} Transaction_SetRefundWaitingStatus_Success
// @Failure 401 {object} Transaction_SetRefundWaitingStatus_Failure "goerrpkg.ErrorTypeAuthorization"
// @Failure 404 {object} Transaction_SetRefundWaitingStatus_Failure "goerrpkg.ErrorTypeNotFound"
// @Failure 400 {object} Transaction_SetRefundWaitingStatus_Failure "goerrpkg.ErrorTypeBadRequest"
// @Failure 500 {object} Transaction_SetRefundWaitingStatus_Failure "goerrpkg.ErrorTypeUnexpectedError"
// @Failure 400 {object} Transaction_SetRefundWaitingStatus_Failure "gin.ErrorTypeBind"
// @Failure 500 {object} Transaction_SetRefundWaitingStatus_Failure "Undefined error"
// @Produce json
// @Router /processing.transaction.transaction.Transaction/SetRefundWaitingStatus [put]
// @tags Transaction
func (s *ginTransactionServer) SetRefundWaitingStatus(c *gin.Context) error {
	ctx, span := dog.CreateSpan(c.Request.Context(), "GinTransactionServer_SetRefundWaitingStatus")
	defer span.End()

	var request SetRefundWaitingStatusRequestV1
	if err := c.ShouldBindJSON(&request); err != nil {
		return err
	}

	ctx = metadata.NewOutgoingContext(ctx, metadata.MD(c.Request.Header))

	response, err := s.client.SetRefundWaitingStatus(ctx, &request)
	if err != nil {
		return err
	}

	c.JSON(http.StatusOK, &Transaction_SetRefundWaitingStatus_Success{
		Status:     true,
		Message:    "Success",
		StatusCode: 0,
		Result:     response,
	})
	return nil
}

type Transaction_GetTransactionsByProjectInfo_Success struct {
	// Status is the status of the response. Set true
	Status bool `json:"status"`
	// Message is a human-readable explanation of the status
	Message string `json:"message"`
	// StatusCode is the GoErr status. Set 0
	StatusCode int `json:"status_code"`
	// Result is the result of the request
	Result *GetTransactionsResponse `json:"result"`
}

type Transaction_GetTransactionsByProjectInfo_Failure struct {
	// Status is the status of the response. Set false
	Status bool `json:"status"`
	// Message is a human-readable explanation of the status
	Message string `json:"message"`
	// StatusCode is the GoErr status
	StatusCode int `json:"status_code"`
}

// GetTransactionsByProjectInfo
// @Summary GetTransactionsByProjectInfo
// @Security bearerAuth
// @ID Transaction_GetTransactionsByProjectInfo
// @Accept json
// @Param request body GetTransactionsByProjectInfoRequestV1 true "GetTransactionsByProjectInfoRequestV1"
// @Success 200 {object} Transaction_GetTransactionsByProjectInfo_Success
// @Failure 401 {object} Transaction_GetTransactionsByProjectInfo_Failure "goerrpkg.ErrorTypeAuthorization"
// @Failure 404 {object} Transaction_GetTransactionsByProjectInfo_Failure "goerrpkg.ErrorTypeNotFound"
// @Failure 400 {object} Transaction_GetTransactionsByProjectInfo_Failure "goerrpkg.ErrorTypeBadRequest"
// @Failure 500 {object} Transaction_GetTransactionsByProjectInfo_Failure "goerrpkg.ErrorTypeUnexpectedError"
// @Failure 400 {object} Transaction_GetTransactionsByProjectInfo_Failure "gin.ErrorTypeBind"
// @Failure 500 {object} Transaction_GetTransactionsByProjectInfo_Failure "Undefined error"
// @Produce json
// @Router /processing.transaction.transaction.Transaction/GetTransactionsByProjectInfo [put]
// @tags Transaction
func (s *ginTransactionServer) GetTransactionsByProjectInfo(c *gin.Context) error {
	ctx, span := dog.CreateSpan(c.Request.Context(), "GinTransactionServer_GetTransactionsByProjectInfo")
	defer span.End()

	var request GetTransactionsByProjectInfoRequestV1
	if err := c.ShouldBindJSON(&request); err != nil {
		return err
	}

	ctx = metadata.NewOutgoingContext(ctx, metadata.MD(c.Request.Header))

	response, err := s.client.GetTransactionsByProjectInfo(ctx, &request)
	if err != nil {
		return err
	}

	c.JSON(http.StatusOK, &Transaction_GetTransactionsByProjectInfo_Success{
		Status:     true,
		Message:    "Success",
		StatusCode: 0,
		Result:     response,
	})
	return nil
}

type Transaction_CheckTransactionHash_Success struct {
	// Status is the status of the response. Set true
	Status bool `json:"status"`
	// Message is a human-readable explanation of the status
	Message string `json:"message"`
	// StatusCode is the GoErr status. Set 0
	StatusCode int `json:"status_code"`
	// Result is the result of the request
	Result *emptypb.Empty `json:"result"`
}

type Transaction_CheckTransactionHash_Failure struct {
	// Status is the status of the response. Set false
	Status bool `json:"status"`
	// Message is a human-readable explanation of the status
	Message string `json:"message"`
	// StatusCode is the GoErr status
	StatusCode int `json:"status_code"`
}

// CheckTransactionHash
// @Summary CheckTransactionHash
// @Security bearerAuth
// @ID Transaction_CheckTransactionHash
// @Accept json
// @Param request body CheckTransactionHashRequestV1 true "CheckTransactionHashRequestV1"
// @Success 200 {object} Transaction_CheckTransactionHash_Success
// @Failure 401 {object} Transaction_CheckTransactionHash_Failure "goerrpkg.ErrorTypeAuthorization"
// @Failure 404 {object} Transaction_CheckTransactionHash_Failure "goerrpkg.ErrorTypeNotFound"
// @Failure 400 {object} Transaction_CheckTransactionHash_Failure "goerrpkg.ErrorTypeBadRequest"
// @Failure 500 {object} Transaction_CheckTransactionHash_Failure "goerrpkg.ErrorTypeUnexpectedError"
// @Failure 400 {object} Transaction_CheckTransactionHash_Failure "gin.ErrorTypeBind"
// @Failure 500 {object} Transaction_CheckTransactionHash_Failure "Undefined error"
// @Produce json
// @Router /processing.transaction.transaction.Transaction/CheckTransactionHash [put]
// @tags Transaction
func (s *ginTransactionServer) CheckTransactionHash(c *gin.Context) error {
	ctx, span := dog.CreateSpan(c.Request.Context(), "GinTransactionServer_CheckTransactionHash")
	defer span.End()

	var request CheckTransactionHashRequestV1
	if err := c.ShouldBindJSON(&request); err != nil {
		return err
	}

	ctx = metadata.NewOutgoingContext(ctx, metadata.MD(c.Request.Header))

	response, err := s.client.CheckTransactionHash(ctx, &request)
	if err != nil {
		return err
	}

	c.JSON(http.StatusOK, &Transaction_CheckTransactionHash_Success{
		Status:     true,
		Message:    "Success",
		StatusCode: 0,
		Result:     response,
	})
	return nil
}

type Transaction_CreateTransactionByPhone_Success struct {
	// Status is the status of the response. Set true
	Status bool `json:"status"`
	// Message is a human-readable explanation of the status
	Message string `json:"message"`
	// StatusCode is the GoErr status. Set 0
	StatusCode int `json:"status_code"`
	// Result is the result of the request
	Result *CreateTransactionByPhoneResponse `json:"result"`
}

type Transaction_CreateTransactionByPhone_Failure struct {
	// Status is the status of the response. Set false
	Status bool `json:"status"`
	// Message is a human-readable explanation of the status
	Message string `json:"message"`
	// StatusCode is the GoErr status
	StatusCode int `json:"status_code"`
}

// CreateTransactionByPhone
// @Summary CreateTransactionByPhone
// @Security bearerAuth
// @ID Transaction_CreateTransactionByPhone
// @Accept json
// @Param request body CreateTransactionByPhoneRequest true "CreateTransactionByPhoneRequest"
// @Success 200 {object} Transaction_CreateTransactionByPhone_Success
// @Failure 401 {object} Transaction_CreateTransactionByPhone_Failure "goerrpkg.ErrorTypeAuthorization"
// @Failure 404 {object} Transaction_CreateTransactionByPhone_Failure "goerrpkg.ErrorTypeNotFound"
// @Failure 400 {object} Transaction_CreateTransactionByPhone_Failure "goerrpkg.ErrorTypeBadRequest"
// @Failure 500 {object} Transaction_CreateTransactionByPhone_Failure "goerrpkg.ErrorTypeUnexpectedError"
// @Failure 400 {object} Transaction_CreateTransactionByPhone_Failure "gin.ErrorTypeBind"
// @Failure 500 {object} Transaction_CreateTransactionByPhone_Failure "Undefined error"
// @Produce json
// @Router /processing.transaction.transaction.Transaction/CreateTransactionByPhone [put]
// @tags Transaction
func (s *ginTransactionServer) CreateTransactionByPhone(c *gin.Context) error {
	ctx, span := dog.CreateSpan(c.Request.Context(), "GinTransactionServer_CreateTransactionByPhone")
	defer span.End()

	var request CreateTransactionByPhoneRequest
	if err := c.ShouldBindJSON(&request); err != nil {
		return err
	}

	ctx = metadata.NewOutgoingContext(ctx, metadata.MD(c.Request.Header))

	response, err := s.client.CreateTransactionByPhone(ctx, &request)
	if err != nil {
		return err
	}

	c.JSON(http.StatusOK, &Transaction_CreateTransactionByPhone_Success{
		Status:     true,
		Message:    "Success",
		StatusCode: 0,
		Result:     response,
	})
	return nil
}

type Transaction_GetByIDWithType_Success struct {
	// Status is the status of the response. Set true
	Status bool `json:"status"`
	// Message is a human-readable explanation of the status
	Message string `json:"message"`
	// StatusCode is the GoErr status. Set 0
	StatusCode int `json:"status_code"`
	// Result is the result of the request
	Result *GetByIDWithTypeResponse `json:"result"`
}

type Transaction_GetByIDWithType_Failure struct {
	// Status is the status of the response. Set false
	Status bool `json:"status"`
	// Message is a human-readable explanation of the status
	Message string `json:"message"`
	// StatusCode is the GoErr status
	StatusCode int `json:"status_code"`
}

// GetByIDWithType
// @Summary GetByIDWithType
// @Security bearerAuth
// @ID Transaction_GetByIDWithType
// @Accept json
// @Param request body GetByIDWithTypeRequest true "GetByIDWithTypeRequest"
// @Success 200 {object} Transaction_GetByIDWithType_Success
// @Failure 401 {object} Transaction_GetByIDWithType_Failure "goerrpkg.ErrorTypeAuthorization"
// @Failure 404 {object} Transaction_GetByIDWithType_Failure "goerrpkg.ErrorTypeNotFound"
// @Failure 400 {object} Transaction_GetByIDWithType_Failure "goerrpkg.ErrorTypeBadRequest"
// @Failure 500 {object} Transaction_GetByIDWithType_Failure "goerrpkg.ErrorTypeUnexpectedError"
// @Failure 400 {object} Transaction_GetByIDWithType_Failure "gin.ErrorTypeBind"
// @Failure 500 {object} Transaction_GetByIDWithType_Failure "Undefined error"
// @Produce json
// @Router /processing.transaction.transaction.Transaction/GetByIDWithType [put]
// @tags Transaction
func (s *ginTransactionServer) GetByIDWithType(c *gin.Context) error {
	ctx, span := dog.CreateSpan(c.Request.Context(), "GinTransactionServer_GetByIDWithType")
	defer span.End()

	var request GetByIDWithTypeRequest
	if err := c.ShouldBindJSON(&request); err != nil {
		return err
	}

	ctx = metadata.NewOutgoingContext(ctx, metadata.MD(c.Request.Header))

	response, err := s.client.GetByIDWithType(ctx, &request)
	if err != nil {
		return err
	}

	c.JSON(http.StatusOK, &Transaction_GetByIDWithType_Success{
		Status:     true,
		Message:    "Success",
		StatusCode: 0,
		Result:     response,
	})
	return nil
}

type Transaction_UpdateStatus_Success struct {
	// Status is the status of the response. Set true
	Status bool `json:"status"`
	// Message is a human-readable explanation of the status
	Message string `json:"message"`
	// StatusCode is the GoErr status. Set 0
	StatusCode int `json:"status_code"`
	// Result is the result of the request
	Result *emptypb.Empty `json:"result"`
}

type Transaction_UpdateStatus_Failure struct {
	// Status is the status of the response. Set false
	Status bool `json:"status"`
	// Message is a human-readable explanation of the status
	Message string `json:"message"`
	// StatusCode is the GoErr status
	StatusCode int `json:"status_code"`
}

// UpdateStatus
// @Summary UpdateStatus
// @Security bearerAuth
// @ID Transaction_UpdateStatus
// @Accept json
// @Param request body UpdateStatusRequest true "UpdateStatusRequest"
// @Success 200 {object} Transaction_UpdateStatus_Success
// @Failure 401 {object} Transaction_UpdateStatus_Failure "goerrpkg.ErrorTypeAuthorization"
// @Failure 404 {object} Transaction_UpdateStatus_Failure "goerrpkg.ErrorTypeNotFound"
// @Failure 400 {object} Transaction_UpdateStatus_Failure "goerrpkg.ErrorTypeBadRequest"
// @Failure 500 {object} Transaction_UpdateStatus_Failure "goerrpkg.ErrorTypeUnexpectedError"
// @Failure 400 {object} Transaction_UpdateStatus_Failure "gin.ErrorTypeBind"
// @Failure 500 {object} Transaction_UpdateStatus_Failure "Undefined error"
// @Produce json
// @Router /processing.transaction.transaction.Transaction/UpdateStatus [put]
// @tags Transaction
func (s *ginTransactionServer) UpdateStatus(c *gin.Context) error {
	ctx, span := dog.CreateSpan(c.Request.Context(), "GinTransactionServer_UpdateStatus")
	defer span.End()

	var request UpdateStatusRequest
	if err := c.ShouldBindJSON(&request); err != nil {
		return err
	}

	ctx = metadata.NewOutgoingContext(ctx, metadata.MD(c.Request.Header))

	response, err := s.client.UpdateStatus(ctx, &request)
	if err != nil {
		return err
	}

	c.JSON(http.StatusOK, &Transaction_UpdateStatus_Success{
		Status:     true,
		Message:    "Success",
		StatusCode: 0,
		Result:     response,
	})
	return nil
}

type Transaction_IncreaseTryCount_Success struct {
	// Status is the status of the response. Set true
	Status bool `json:"status"`
	// Message is a human-readable explanation of the status
	Message string `json:"message"`
	// StatusCode is the GoErr status. Set 0
	StatusCode int `json:"status_code"`
	// Result is the result of the request
	Result *emptypb.Empty `json:"result"`
}

type Transaction_IncreaseTryCount_Failure struct {
	// Status is the status of the response. Set false
	Status bool `json:"status"`
	// Message is a human-readable explanation of the status
	Message string `json:"message"`
	// StatusCode is the GoErr status
	StatusCode int `json:"status_code"`
}

// IncreaseTryCount
// @Summary IncreaseTryCount
// @Security bearerAuth
// @ID Transaction_IncreaseTryCount
// @Accept json
// @Param request body IncreaseTryCountRequest true "IncreaseTryCountRequest"
// @Success 200 {object} Transaction_IncreaseTryCount_Success
// @Failure 401 {object} Transaction_IncreaseTryCount_Failure "goerrpkg.ErrorTypeAuthorization"
// @Failure 404 {object} Transaction_IncreaseTryCount_Failure "goerrpkg.ErrorTypeNotFound"
// @Failure 400 {object} Transaction_IncreaseTryCount_Failure "goerrpkg.ErrorTypeBadRequest"
// @Failure 500 {object} Transaction_IncreaseTryCount_Failure "goerrpkg.ErrorTypeUnexpectedError"
// @Failure 400 {object} Transaction_IncreaseTryCount_Failure "gin.ErrorTypeBind"
// @Failure 500 {object} Transaction_IncreaseTryCount_Failure "Undefined error"
// @Produce json
// @Router /processing.transaction.transaction.Transaction/IncreaseTryCount [put]
// @tags Transaction
func (s *ginTransactionServer) IncreaseTryCount(c *gin.Context) error {
	ctx, span := dog.CreateSpan(c.Request.Context(), "GinTransactionServer_IncreaseTryCount")
	defer span.End()

	var request IncreaseTryCountRequest
	if err := c.ShouldBindJSON(&request); err != nil {
		return err
	}

	ctx = metadata.NewOutgoingContext(ctx, metadata.MD(c.Request.Header))

	response, err := s.client.IncreaseTryCount(ctx, &request)
	if err != nil {
		return err
	}

	c.JSON(http.StatusOK, &Transaction_IncreaseTryCount_Success{
		Status:     true,
		Message:    "Success",
		StatusCode: 0,
		Result:     response,
	})
	return nil
}

type Transaction_SaveAcquirerResponse_Success struct {
	// Status is the status of the response. Set true
	Status bool `json:"status"`
	// Message is a human-readable explanation of the status
	Message string `json:"message"`
	// StatusCode is the GoErr status. Set 0
	StatusCode int `json:"status_code"`
	// Result is the result of the request
	Result *emptypb.Empty `json:"result"`
}

type Transaction_SaveAcquirerResponse_Failure struct {
	// Status is the status of the response. Set false
	Status bool `json:"status"`
	// Message is a human-readable explanation of the status
	Message string `json:"message"`
	// StatusCode is the GoErr status
	StatusCode int `json:"status_code"`
}

// SaveAcquirerResponse
// @Summary SaveAcquirerResponse
// @Security bearerAuth
// @ID Transaction_SaveAcquirerResponse
// @Accept json
// @Param request body SaveAcquirerResponseRequest true "SaveAcquirerResponseRequest"
// @Success 200 {object} Transaction_SaveAcquirerResponse_Success
// @Failure 401 {object} Transaction_SaveAcquirerResponse_Failure "goerrpkg.ErrorTypeAuthorization"
// @Failure 404 {object} Transaction_SaveAcquirerResponse_Failure "goerrpkg.ErrorTypeNotFound"
// @Failure 400 {object} Transaction_SaveAcquirerResponse_Failure "goerrpkg.ErrorTypeBadRequest"
// @Failure 500 {object} Transaction_SaveAcquirerResponse_Failure "goerrpkg.ErrorTypeUnexpectedError"
// @Failure 400 {object} Transaction_SaveAcquirerResponse_Failure "gin.ErrorTypeBind"
// @Failure 500 {object} Transaction_SaveAcquirerResponse_Failure "Undefined error"
// @Produce json
// @Router /processing.transaction.transaction.Transaction/SaveAcquirerResponse [put]
// @tags Transaction
func (s *ginTransactionServer) SaveAcquirerResponse(c *gin.Context) error {
	ctx, span := dog.CreateSpan(c.Request.Context(), "GinTransactionServer_SaveAcquirerResponse")
	defer span.End()

	var request SaveAcquirerResponseRequest
	if err := c.ShouldBindJSON(&request); err != nil {
		return err
	}

	ctx = metadata.NewOutgoingContext(ctx, metadata.MD(c.Request.Header))

	response, err := s.client.SaveAcquirerResponse(ctx, &request)
	if err != nil {
		return err
	}

	c.JSON(http.StatusOK, &Transaction_SaveAcquirerResponse_Success{
		Status:     true,
		Message:    "Success",
		StatusCode: 0,
		Result:     response,
	})
	return nil
}

type Transaction_SendReceipt_Success struct {
	// Status is the status of the response. Set true
	Status bool `json:"status"`
	// Message is a human-readable explanation of the status
	Message string `json:"message"`
	// StatusCode is the GoErr status. Set 0
	StatusCode int `json:"status_code"`
	// Result is the result of the request
	Result *emptypb.Empty `json:"result"`
}

type Transaction_SendReceipt_Failure struct {
	// Status is the status of the response. Set false
	Status bool `json:"status"`
	// Message is a human-readable explanation of the status
	Message string `json:"message"`
	// StatusCode is the GoErr status
	StatusCode int `json:"status_code"`
}

// SendReceipt
// @Summary SendReceipt
// @Security bearerAuth
// @ID Transaction_SendReceipt
// @Accept json
// @Param request body SendRequest true "SendRequest"
// @Success 200 {object} Transaction_SendReceipt_Success
// @Failure 401 {object} Transaction_SendReceipt_Failure "goerrpkg.ErrorTypeAuthorization"
// @Failure 404 {object} Transaction_SendReceipt_Failure "goerrpkg.ErrorTypeNotFound"
// @Failure 400 {object} Transaction_SendReceipt_Failure "goerrpkg.ErrorTypeBadRequest"
// @Failure 500 {object} Transaction_SendReceipt_Failure "goerrpkg.ErrorTypeUnexpectedError"
// @Failure 400 {object} Transaction_SendReceipt_Failure "gin.ErrorTypeBind"
// @Failure 500 {object} Transaction_SendReceipt_Failure "Undefined error"
// @Produce json
// @Router /processing.transaction.transaction.Transaction/SendReceipt [put]
// @tags Transaction
func (s *ginTransactionServer) SendReceipt(c *gin.Context) error {
	ctx, span := dog.CreateSpan(c.Request.Context(), "GinTransactionServer_SendReceipt")
	defer span.End()

	var request SendRequest
	if err := c.ShouldBindJSON(&request); err != nil {
		return err
	}

	ctx = metadata.NewOutgoingContext(ctx, metadata.MD(c.Request.Header))

	response, err := s.client.SendReceipt(ctx, &request)
	if err != nil {
		return err
	}

	c.JSON(http.StatusOK, &Transaction_SendReceipt_Success{
		Status:     true,
		Message:    "Success",
		StatusCode: 0,
		Result:     response,
	})
	return nil
}

type Transaction_SetAdditionalData_Success struct {
	// Status is the status of the response. Set true
	Status bool `json:"status"`
	// Message is a human-readable explanation of the status
	Message string `json:"message"`
	// StatusCode is the GoErr status. Set 0
	StatusCode int `json:"status_code"`
	// Result is the result of the request
	Result *emptypb.Empty `json:"result"`
}

type Transaction_SetAdditionalData_Failure struct {
	// Status is the status of the response. Set false
	Status bool `json:"status"`
	// Message is a human-readable explanation of the status
	Message string `json:"message"`
	// StatusCode is the GoErr status
	StatusCode int `json:"status_code"`
}

// SetAdditionalData
// @Summary SetAdditionalData
// @Security bearerAuth
// @ID Transaction_SetAdditionalData
// @Accept json
// @Param request body SetAdditionalDataRequest true "SetAdditionalDataRequest"
// @Success 200 {object} Transaction_SetAdditionalData_Success
// @Failure 401 {object} Transaction_SetAdditionalData_Failure "goerrpkg.ErrorTypeAuthorization"
// @Failure 404 {object} Transaction_SetAdditionalData_Failure "goerrpkg.ErrorTypeNotFound"
// @Failure 400 {object} Transaction_SetAdditionalData_Failure "goerrpkg.ErrorTypeBadRequest"
// @Failure 500 {object} Transaction_SetAdditionalData_Failure "goerrpkg.ErrorTypeUnexpectedError"
// @Failure 400 {object} Transaction_SetAdditionalData_Failure "gin.ErrorTypeBind"
// @Failure 500 {object} Transaction_SetAdditionalData_Failure "Undefined error"
// @Produce json
// @Router /processing.transaction.transaction.Transaction/SetAdditionalData [put]
// @tags Transaction
func (s *ginTransactionServer) SetAdditionalData(c *gin.Context) error {
	ctx, span := dog.CreateSpan(c.Request.Context(), "GinTransactionServer_SetAdditionalData")
	defer span.End()

	var request SetAdditionalDataRequest
	if err := c.ShouldBindJSON(&request); err != nil {
		return err
	}

	ctx = metadata.NewOutgoingContext(ctx, metadata.MD(c.Request.Header))

	response, err := s.client.SetAdditionalData(ctx, &request)
	if err != nil {
		return err
	}

	c.JSON(http.StatusOK, &Transaction_SetAdditionalData_Success{
		Status:     true,
		Message:    "Success",
		StatusCode: 0,
		Result:     response,
	})
	return nil
}

type Transaction_CalculateAndUpdateTransactionAmount_Success struct {
	// Status is the status of the response. Set true
	Status bool `json:"status"`
	// Message is a human-readable explanation of the status
	Message string `json:"message"`
	// StatusCode is the GoErr status. Set 0
	StatusCode int `json:"status_code"`
	// Result is the result of the request
	Result *emptypb.Empty `json:"result"`
}

type Transaction_CalculateAndUpdateTransactionAmount_Failure struct {
	// Status is the status of the response. Set false
	Status bool `json:"status"`
	// Message is a human-readable explanation of the status
	Message string `json:"message"`
	// StatusCode is the GoErr status
	StatusCode int `json:"status_code"`
}

// CalculateAndUpdateTransactionAmount
// @Summary CalculateAndUpdateTransactionAmount
// @Security bearerAuth
// @ID Transaction_CalculateAndUpdateTransactionAmount
// @Accept json
// @Param request body CalculateAndUpdateTransactionAmountRequest true "CalculateAndUpdateTransactionAmountRequest"
// @Success 200 {object} Transaction_CalculateAndUpdateTransactionAmount_Success
// @Failure 401 {object} Transaction_CalculateAndUpdateTransactionAmount_Failure "goerrpkg.ErrorTypeAuthorization"
// @Failure 404 {object} Transaction_CalculateAndUpdateTransactionAmount_Failure "goerrpkg.ErrorTypeNotFound"
// @Failure 400 {object} Transaction_CalculateAndUpdateTransactionAmount_Failure "goerrpkg.ErrorTypeBadRequest"
// @Failure 500 {object} Transaction_CalculateAndUpdateTransactionAmount_Failure "goerrpkg.ErrorTypeUnexpectedError"
// @Failure 400 {object} Transaction_CalculateAndUpdateTransactionAmount_Failure "gin.ErrorTypeBind"
// @Failure 500 {object} Transaction_CalculateAndUpdateTransactionAmount_Failure "Undefined error"
// @Produce json
// @Router /processing.transaction.transaction.Transaction/CalculateAndUpdateTransactionAmount [put]
// @tags Transaction
func (s *ginTransactionServer) CalculateAndUpdateTransactionAmount(c *gin.Context) error {
	ctx, span := dog.CreateSpan(c.Request.Context(), "GinTransactionServer_CalculateAndUpdateTransactionAmount")
	defer span.End()

	var request CalculateAndUpdateTransactionAmountRequest
	if err := c.ShouldBindJSON(&request); err != nil {
		return err
	}

	ctx = metadata.NewOutgoingContext(ctx, metadata.MD(c.Request.Header))

	response, err := s.client.CalculateAndUpdateTransactionAmount(ctx, &request)
	if err != nil {
		return err
	}

	c.JSON(http.StatusOK, &Transaction_CalculateAndUpdateTransactionAmount_Success{
		Status:     true,
		Message:    "Success",
		StatusCode: 0,
		Result:     response,
	})
	return nil
}

type Transaction_BillPayOut_Success struct {
	// Status is the status of the response. Set true
	Status bool `json:"status"`
	// Message is a human-readable explanation of the status
	Message string `json:"message"`
	// StatusCode is the GoErr status. Set 0
	StatusCode int `json:"status_code"`
	// Result is the result of the request
	Result *emptypb.Empty `json:"result"`
}

type Transaction_BillPayOut_Failure struct {
	// Status is the status of the response. Set false
	Status bool `json:"status"`
	// Message is a human-readable explanation of the status
	Message string `json:"message"`
	// StatusCode is the GoErr status
	StatusCode int `json:"status_code"`
}

// BillPayOut
// @Summary BillPayOut
// @Security bearerAuth
// @ID Transaction_BillPayOut
// @Accept json
// @Param request body BillPayoutRequest true "BillPayoutRequest"
// @Success 200 {object} Transaction_BillPayOut_Success
// @Failure 401 {object} Transaction_BillPayOut_Failure "goerrpkg.ErrorTypeAuthorization"
// @Failure 404 {object} Transaction_BillPayOut_Failure "goerrpkg.ErrorTypeNotFound"
// @Failure 400 {object} Transaction_BillPayOut_Failure "goerrpkg.ErrorTypeBadRequest"
// @Failure 500 {object} Transaction_BillPayOut_Failure "goerrpkg.ErrorTypeUnexpectedError"
// @Failure 400 {object} Transaction_BillPayOut_Failure "gin.ErrorTypeBind"
// @Failure 500 {object} Transaction_BillPayOut_Failure "Undefined error"
// @Produce json
// @Router /processing.transaction.transaction.Transaction/BillPayOut [put]
// @tags Transaction
func (s *ginTransactionServer) BillPayOut(c *gin.Context) error {
	ctx, span := dog.CreateSpan(c.Request.Context(), "GinTransactionServer_BillPayOut")
	defer span.End()

	var request BillPayoutRequest
	if err := c.ShouldBindJSON(&request); err != nil {
		return err
	}

	ctx = metadata.NewOutgoingContext(ctx, metadata.MD(c.Request.Header))

	response, err := s.client.BillPayOut(ctx, &request)
	if err != nil {
		return err
	}

	c.JSON(http.StatusOK, &Transaction_BillPayOut_Success{
		Status:     true,
		Message:    "Success",
		StatusCode: 0,
		Result:     response,
	})
	return nil
}
