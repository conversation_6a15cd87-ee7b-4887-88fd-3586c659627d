// Code generated by protoc-gen-go-mvp. DO NOT EDIT.
// versions:
// - protoc-gen-go-mvp v2.0.1
// created by mvp authors

package grpc

import (
	dog "git.local/sensitive/sdk/dog"
	gin "github.com/gin-gonic/gin"
	metadata "google.golang.org/grpc/metadata"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
	http "net/http"
)

func RegisterGinEpayRouterGroup(
	in *gin.RouterGroup,
	handler func(func(c *gin.Context) error) gin.HandlerFunc,
) error {
	service, err := NewGinEpayService()
	if err != nil {
		return err
	}

	routerGroup := in.Group("processing.epay.epay.Epay")
	routerGroup.PUT("/PayIn", handler(service.PayIn))
	routerGroup.PUT("/OneClickPayIn", handler(service.OneClickPayIn))
	routerGroup.PUT("/ThreeDSConfirm", handler(service.ThreeDSConfirm))
	routerGroup.PUT("/ThreeDSResume", handler(service.ThreeDSResume))
	routerGroup.PUT("/PayOut", handler(service.PayOut))
	routerGroup.PUT("/GetBankTransactionStatus", handler(service.GetBankTransactionStatus))
	routerGroup.PUT("/GetBankTransactionStatusUnformated", handler(service.GetBankTransactionStatusUnformated))
	routerGroup.PUT("/Refund", handler(service.Refund))
	routerGroup.PUT("/GooglePay", handler(service.GooglePay))
	routerGroup.PUT("/ApplePay", handler(service.ApplePay))
	routerGroup.PUT("/TwoStagePayIn", handler(service.TwoStagePayIn))
	routerGroup.PUT("/Charge", handler(service.Charge))
	routerGroup.PUT("/Cancel", handler(service.Cancel))
	routerGroup.PUT("/MakeToken", handler(service.MakeToken))
	routerGroup.PUT("/GetAcquirerIdentifier", handler(service.GetAcquirerIdentifier))
	routerGroup.PUT("/ResolveVisaAlias", handler(service.ResolveVisaAlias))
	routerGroup.PUT("/PayOutByPhone", handler(service.PayOutByPhone))
	return nil
}

func NewGinEpayService() (GinEpayServer, error) {
	client, err := NewPreparedEpayClient()
	if err != nil {
		return nil, err
	}

	return &ginEpayServer{
		client: NewLoggedEpayClient(
			NewIamEpayClient(client),
		),
	}, nil
}

//go:generate mockgen -destination=./grpcmock/epay.gin.pb.go -package=grpcmock -source=epay.gin.pb.go GinEpayServer
type GinEpayServer interface {
	PayIn(c *gin.Context) error
	OneClickPayIn(c *gin.Context) error
	ThreeDSConfirm(c *gin.Context) error
	ThreeDSResume(c *gin.Context) error
	PayOut(c *gin.Context) error
	GetBankTransactionStatus(c *gin.Context) error
	GetBankTransactionStatusUnformated(c *gin.Context) error
	Refund(c *gin.Context) error
	GooglePay(c *gin.Context) error
	ApplePay(c *gin.Context) error
	TwoStagePayIn(c *gin.Context) error
	Charge(c *gin.Context) error
	Cancel(c *gin.Context) error
	MakeToken(c *gin.Context) error
	GetAcquirerIdentifier(c *gin.Context) error
	ResolveVisaAlias(c *gin.Context) error
	PayOutByPhone(c *gin.Context) error
}

var _ GinEpayServer = (*ginEpayServer)(nil)

type ginEpayServer struct {
	client EpayClient
}

type Epay_PayIn_Success struct {
	// Status is the status of the response. Set true
	Status bool `json:"status"`
	// Message is a human-readable explanation of the status
	Message string `json:"message"`
	// StatusCode is the GoErr status. Set 0
	StatusCode int `json:"status_code"`
	// Result is the result of the request
	Result *PayInResponseData `json:"result"`
}

type Epay_PayIn_Failure struct {
	// Status is the status of the response. Set false
	Status bool `json:"status"`
	// Message is a human-readable explanation of the status
	Message string `json:"message"`
	// StatusCode is the GoErr status
	StatusCode int `json:"status_code"`
}

// PayIn
// @Summary PayIn
// @Security bearerAuth
// @ID Epay_PayIn
// @Accept json
// @Param request body PayInRequestData true "PayInRequestData"
// @Success 200 {object} Epay_PayIn_Success
// @Failure 401 {object} Epay_PayIn_Failure "goerrpkg.ErrorTypeAuthorization"
// @Failure 404 {object} Epay_PayIn_Failure "goerrpkg.ErrorTypeNotFound"
// @Failure 400 {object} Epay_PayIn_Failure "goerrpkg.ErrorTypeBadRequest"
// @Failure 500 {object} Epay_PayIn_Failure "goerrpkg.ErrorTypeUnexpectedError"
// @Failure 400 {object} Epay_PayIn_Failure "gin.ErrorTypeBind"
// @Failure 500 {object} Epay_PayIn_Failure "Undefined error"
// @Produce json
// @Router /processing.epay.epay.Epay/PayIn [put]
// @tags Epay
func (s *ginEpayServer) PayIn(c *gin.Context) error {
	ctx, span := dog.CreateSpan(c.Request.Context(), "GinEpayServer_PayIn")
	defer span.End()

	var request PayInRequestData
	if err := c.ShouldBindJSON(&request); err != nil {
		return err
	}

	ctx = metadata.NewOutgoingContext(ctx, metadata.MD(c.Request.Header))

	response, err := s.client.PayIn(ctx, &request)
	if err != nil {
		return err
	}

	c.JSON(http.StatusOK, &Epay_PayIn_Success{
		Status:     true,
		Message:    "Success",
		StatusCode: 0,
		Result:     response,
	})
	return nil
}

type Epay_OneClickPayIn_Success struct {
	// Status is the status of the response. Set true
	Status bool `json:"status"`
	// Message is a human-readable explanation of the status
	Message string `json:"message"`
	// StatusCode is the GoErr status. Set 0
	StatusCode int `json:"status_code"`
	// Result is the result of the request
	Result *PayInResponseData `json:"result"`
}

type Epay_OneClickPayIn_Failure struct {
	// Status is the status of the response. Set false
	Status bool `json:"status"`
	// Message is a human-readable explanation of the status
	Message string `json:"message"`
	// StatusCode is the GoErr status
	StatusCode int `json:"status_code"`
}

// OneClickPayIn
// @Summary OneClickPayIn
// @Security bearerAuth
// @ID Epay_OneClickPayIn
// @Accept json
// @Param request body OneClickPayInRequestData true "OneClickPayInRequestData"
// @Success 200 {object} Epay_OneClickPayIn_Success
// @Failure 401 {object} Epay_OneClickPayIn_Failure "goerrpkg.ErrorTypeAuthorization"
// @Failure 404 {object} Epay_OneClickPayIn_Failure "goerrpkg.ErrorTypeNotFound"
// @Failure 400 {object} Epay_OneClickPayIn_Failure "goerrpkg.ErrorTypeBadRequest"
// @Failure 500 {object} Epay_OneClickPayIn_Failure "goerrpkg.ErrorTypeUnexpectedError"
// @Failure 400 {object} Epay_OneClickPayIn_Failure "gin.ErrorTypeBind"
// @Failure 500 {object} Epay_OneClickPayIn_Failure "Undefined error"
// @Produce json
// @Router /processing.epay.epay.Epay/OneClickPayIn [put]
// @tags Epay
func (s *ginEpayServer) OneClickPayIn(c *gin.Context) error {
	ctx, span := dog.CreateSpan(c.Request.Context(), "GinEpayServer_OneClickPayIn")
	defer span.End()

	var request OneClickPayInRequestData
	if err := c.ShouldBindJSON(&request); err != nil {
		return err
	}

	ctx = metadata.NewOutgoingContext(ctx, metadata.MD(c.Request.Header))

	response, err := s.client.OneClickPayIn(ctx, &request)
	if err != nil {
		return err
	}

	c.JSON(http.StatusOK, &Epay_OneClickPayIn_Success{
		Status:     true,
		Message:    "Success",
		StatusCode: 0,
		Result:     response,
	})
	return nil
}

type Epay_ThreeDSConfirm_Success struct {
	// Status is the status of the response. Set true
	Status bool `json:"status"`
	// Message is a human-readable explanation of the status
	Message string `json:"message"`
	// StatusCode is the GoErr status. Set 0
	StatusCode int `json:"status_code"`
	// Result is the result of the request
	Result *ThreeDSResponseData `json:"result"`
}

type Epay_ThreeDSConfirm_Failure struct {
	// Status is the status of the response. Set false
	Status bool `json:"status"`
	// Message is a human-readable explanation of the status
	Message string `json:"message"`
	// StatusCode is the GoErr status
	StatusCode int `json:"status_code"`
}

// ThreeDSConfirm
// @Summary ThreeDSConfirm
// @Security bearerAuth
// @ID Epay_ThreeDSConfirm
// @Accept json
// @Param request body ThreeDSRequestData true "ThreeDSRequestData"
// @Success 200 {object} Epay_ThreeDSConfirm_Success
// @Failure 401 {object} Epay_ThreeDSConfirm_Failure "goerrpkg.ErrorTypeAuthorization"
// @Failure 404 {object} Epay_ThreeDSConfirm_Failure "goerrpkg.ErrorTypeNotFound"
// @Failure 400 {object} Epay_ThreeDSConfirm_Failure "goerrpkg.ErrorTypeBadRequest"
// @Failure 500 {object} Epay_ThreeDSConfirm_Failure "goerrpkg.ErrorTypeUnexpectedError"
// @Failure 400 {object} Epay_ThreeDSConfirm_Failure "gin.ErrorTypeBind"
// @Failure 500 {object} Epay_ThreeDSConfirm_Failure "Undefined error"
// @Produce json
// @Router /processing.epay.epay.Epay/ThreeDSConfirm [put]
// @tags Epay
func (s *ginEpayServer) ThreeDSConfirm(c *gin.Context) error {
	ctx, span := dog.CreateSpan(c.Request.Context(), "GinEpayServer_ThreeDSConfirm")
	defer span.End()

	var request ThreeDSRequestData
	if err := c.ShouldBindJSON(&request); err != nil {
		return err
	}

	ctx = metadata.NewOutgoingContext(ctx, metadata.MD(c.Request.Header))

	response, err := s.client.ThreeDSConfirm(ctx, &request)
	if err != nil {
		return err
	}

	c.JSON(http.StatusOK, &Epay_ThreeDSConfirm_Success{
		Status:     true,
		Message:    "Success",
		StatusCode: 0,
		Result:     response,
	})
	return nil
}

type Epay_ThreeDSResume_Success struct {
	// Status is the status of the response. Set true
	Status bool `json:"status"`
	// Message is a human-readable explanation of the status
	Message string `json:"message"`
	// StatusCode is the GoErr status. Set 0
	StatusCode int `json:"status_code"`
	// Result is the result of the request
	Result *ThreeDSResumeResponse `json:"result"`
}

type Epay_ThreeDSResume_Failure struct {
	// Status is the status of the response. Set false
	Status bool `json:"status"`
	// Message is a human-readable explanation of the status
	Message string `json:"message"`
	// StatusCode is the GoErr status
	StatusCode int `json:"status_code"`
}

// ThreeDSResume
// @Summary ThreeDSResume
// @Security bearerAuth
// @ID Epay_ThreeDSResume
// @Accept json
// @Param request body ThreeDSResumeRequest true "ThreeDSResumeRequest"
// @Success 200 {object} Epay_ThreeDSResume_Success
// @Failure 401 {object} Epay_ThreeDSResume_Failure "goerrpkg.ErrorTypeAuthorization"
// @Failure 404 {object} Epay_ThreeDSResume_Failure "goerrpkg.ErrorTypeNotFound"
// @Failure 400 {object} Epay_ThreeDSResume_Failure "goerrpkg.ErrorTypeBadRequest"
// @Failure 500 {object} Epay_ThreeDSResume_Failure "goerrpkg.ErrorTypeUnexpectedError"
// @Failure 400 {object} Epay_ThreeDSResume_Failure "gin.ErrorTypeBind"
// @Failure 500 {object} Epay_ThreeDSResume_Failure "Undefined error"
// @Produce json
// @Router /processing.epay.epay.Epay/ThreeDSResume [put]
// @tags Epay
func (s *ginEpayServer) ThreeDSResume(c *gin.Context) error {
	ctx, span := dog.CreateSpan(c.Request.Context(), "GinEpayServer_ThreeDSResume")
	defer span.End()

	var request ThreeDSResumeRequest
	if err := c.ShouldBindJSON(&request); err != nil {
		return err
	}

	ctx = metadata.NewOutgoingContext(ctx, metadata.MD(c.Request.Header))

	response, err := s.client.ThreeDSResume(ctx, &request)
	if err != nil {
		return err
	}

	c.JSON(http.StatusOK, &Epay_ThreeDSResume_Success{
		Status:     true,
		Message:    "Success",
		StatusCode: 0,
		Result:     response,
	})
	return nil
}

type Epay_PayOut_Success struct {
	// Status is the status of the response. Set true
	Status bool `json:"status"`
	// Message is a human-readable explanation of the status
	Message string `json:"message"`
	// StatusCode is the GoErr status. Set 0
	StatusCode int `json:"status_code"`
	// Result is the result of the request
	Result *PayOutResponseData `json:"result"`
}

type Epay_PayOut_Failure struct {
	// Status is the status of the response. Set false
	Status bool `json:"status"`
	// Message is a human-readable explanation of the status
	Message string `json:"message"`
	// StatusCode is the GoErr status
	StatusCode int `json:"status_code"`
}

// PayOut
// @Summary PayOut
// @Security bearerAuth
// @ID Epay_PayOut
// @Accept json
// @Param request body PayOutRequestData true "PayOutRequestData"
// @Success 200 {object} Epay_PayOut_Success
// @Failure 401 {object} Epay_PayOut_Failure "goerrpkg.ErrorTypeAuthorization"
// @Failure 404 {object} Epay_PayOut_Failure "goerrpkg.ErrorTypeNotFound"
// @Failure 400 {object} Epay_PayOut_Failure "goerrpkg.ErrorTypeBadRequest"
// @Failure 500 {object} Epay_PayOut_Failure "goerrpkg.ErrorTypeUnexpectedError"
// @Failure 400 {object} Epay_PayOut_Failure "gin.ErrorTypeBind"
// @Failure 500 {object} Epay_PayOut_Failure "Undefined error"
// @Produce json
// @Router /processing.epay.epay.Epay/PayOut [put]
// @tags Epay
func (s *ginEpayServer) PayOut(c *gin.Context) error {
	ctx, span := dog.CreateSpan(c.Request.Context(), "GinEpayServer_PayOut")
	defer span.End()

	var request PayOutRequestData
	if err := c.ShouldBindJSON(&request); err != nil {
		return err
	}

	ctx = metadata.NewOutgoingContext(ctx, metadata.MD(c.Request.Header))

	response, err := s.client.PayOut(ctx, &request)
	if err != nil {
		return err
	}

	c.JSON(http.StatusOK, &Epay_PayOut_Success{
		Status:     true,
		Message:    "Success",
		StatusCode: 0,
		Result:     response,
	})
	return nil
}

type Epay_GetBankTransactionStatus_Success struct {
	// Status is the status of the response. Set true
	Status bool `json:"status"`
	// Message is a human-readable explanation of the status
	Message string `json:"message"`
	// StatusCode is the GoErr status. Set 0
	StatusCode int `json:"status_code"`
	// Result is the result of the request
	Result *BankTransactionStatusResponse `json:"result"`
}

type Epay_GetBankTransactionStatus_Failure struct {
	// Status is the status of the response. Set false
	Status bool `json:"status"`
	// Message is a human-readable explanation of the status
	Message string `json:"message"`
	// StatusCode is the GoErr status
	StatusCode int `json:"status_code"`
}

// GetBankTransactionStatus
// @Summary GetBankTransactionStatus
// @Security bearerAuth
// @ID Epay_GetBankTransactionStatus
// @Accept json
// @Param request body BankTransactionStatusRequest true "BankTransactionStatusRequest"
// @Success 200 {object} Epay_GetBankTransactionStatus_Success
// @Failure 401 {object} Epay_GetBankTransactionStatus_Failure "goerrpkg.ErrorTypeAuthorization"
// @Failure 404 {object} Epay_GetBankTransactionStatus_Failure "goerrpkg.ErrorTypeNotFound"
// @Failure 400 {object} Epay_GetBankTransactionStatus_Failure "goerrpkg.ErrorTypeBadRequest"
// @Failure 500 {object} Epay_GetBankTransactionStatus_Failure "goerrpkg.ErrorTypeUnexpectedError"
// @Failure 400 {object} Epay_GetBankTransactionStatus_Failure "gin.ErrorTypeBind"
// @Failure 500 {object} Epay_GetBankTransactionStatus_Failure "Undefined error"
// @Produce json
// @Router /processing.epay.epay.Epay/GetBankTransactionStatus [put]
// @tags Epay
func (s *ginEpayServer) GetBankTransactionStatus(c *gin.Context) error {
	ctx, span := dog.CreateSpan(c.Request.Context(), "GinEpayServer_GetBankTransactionStatus")
	defer span.End()

	var request BankTransactionStatusRequest
	if err := c.ShouldBindJSON(&request); err != nil {
		return err
	}

	ctx = metadata.NewOutgoingContext(ctx, metadata.MD(c.Request.Header))

	response, err := s.client.GetBankTransactionStatus(ctx, &request)
	if err != nil {
		return err
	}

	c.JSON(http.StatusOK, &Epay_GetBankTransactionStatus_Success{
		Status:     true,
		Message:    "Success",
		StatusCode: 0,
		Result:     response,
	})
	return nil
}

type Epay_GetBankTransactionStatusUnformated_Success struct {
	// Status is the status of the response. Set true
	Status bool `json:"status"`
	// Message is a human-readable explanation of the status
	Message string `json:"message"`
	// StatusCode is the GoErr status. Set 0
	StatusCode int `json:"status_code"`
	// Result is the result of the request
	Result *BankTransactionStatusUnformatedResponse `json:"result"`
}

type Epay_GetBankTransactionStatusUnformated_Failure struct {
	// Status is the status of the response. Set false
	Status bool `json:"status"`
	// Message is a human-readable explanation of the status
	Message string `json:"message"`
	// StatusCode is the GoErr status
	StatusCode int `json:"status_code"`
}

// GetBankTransactionStatusUnformated
// @Summary GetBankTransactionStatusUnformated
// @Security bearerAuth
// @ID Epay_GetBankTransactionStatusUnformated
// @Accept json
// @Param request body BankTransactionStatusUnformatedRequest true "BankTransactionStatusUnformatedRequest"
// @Success 200 {object} Epay_GetBankTransactionStatusUnformated_Success
// @Failure 401 {object} Epay_GetBankTransactionStatusUnformated_Failure "goerrpkg.ErrorTypeAuthorization"
// @Failure 404 {object} Epay_GetBankTransactionStatusUnformated_Failure "goerrpkg.ErrorTypeNotFound"
// @Failure 400 {object} Epay_GetBankTransactionStatusUnformated_Failure "goerrpkg.ErrorTypeBadRequest"
// @Failure 500 {object} Epay_GetBankTransactionStatusUnformated_Failure "goerrpkg.ErrorTypeUnexpectedError"
// @Failure 400 {object} Epay_GetBankTransactionStatusUnformated_Failure "gin.ErrorTypeBind"
// @Failure 500 {object} Epay_GetBankTransactionStatusUnformated_Failure "Undefined error"
// @Produce json
// @Router /processing.epay.epay.Epay/GetBankTransactionStatusUnformated [put]
// @tags Epay
func (s *ginEpayServer) GetBankTransactionStatusUnformated(c *gin.Context) error {
	ctx, span := dog.CreateSpan(c.Request.Context(), "GinEpayServer_GetBankTransactionStatusUnformated")
	defer span.End()

	var request BankTransactionStatusUnformatedRequest
	if err := c.ShouldBindJSON(&request); err != nil {
		return err
	}

	ctx = metadata.NewOutgoingContext(ctx, metadata.MD(c.Request.Header))

	response, err := s.client.GetBankTransactionStatusUnformated(ctx, &request)
	if err != nil {
		return err
	}

	c.JSON(http.StatusOK, &Epay_GetBankTransactionStatusUnformated_Success{
		Status:     true,
		Message:    "Success",
		StatusCode: 0,
		Result:     response,
	})
	return nil
}

type Epay_Refund_Success struct {
	// Status is the status of the response. Set true
	Status bool `json:"status"`
	// Message is a human-readable explanation of the status
	Message string `json:"message"`
	// StatusCode is the GoErr status. Set 0
	StatusCode int `json:"status_code"`
	// Result is the result of the request
	Result *RefundResponse `json:"result"`
}

type Epay_Refund_Failure struct {
	// Status is the status of the response. Set false
	Status bool `json:"status"`
	// Message is a human-readable explanation of the status
	Message string `json:"message"`
	// StatusCode is the GoErr status
	StatusCode int `json:"status_code"`
}

// Refund
// @Summary Refund
// @Security bearerAuth
// @ID Epay_Refund
// @Accept json
// @Param request body RefundRequest true "RefundRequest"
// @Success 200 {object} Epay_Refund_Success
// @Failure 401 {object} Epay_Refund_Failure "goerrpkg.ErrorTypeAuthorization"
// @Failure 404 {object} Epay_Refund_Failure "goerrpkg.ErrorTypeNotFound"
// @Failure 400 {object} Epay_Refund_Failure "goerrpkg.ErrorTypeBadRequest"
// @Failure 500 {object} Epay_Refund_Failure "goerrpkg.ErrorTypeUnexpectedError"
// @Failure 400 {object} Epay_Refund_Failure "gin.ErrorTypeBind"
// @Failure 500 {object} Epay_Refund_Failure "Undefined error"
// @Produce json
// @Router /processing.epay.epay.Epay/Refund [put]
// @tags Epay
func (s *ginEpayServer) Refund(c *gin.Context) error {
	ctx, span := dog.CreateSpan(c.Request.Context(), "GinEpayServer_Refund")
	defer span.End()

	var request RefundRequest
	if err := c.ShouldBindJSON(&request); err != nil {
		return err
	}

	ctx = metadata.NewOutgoingContext(ctx, metadata.MD(c.Request.Header))

	response, err := s.client.Refund(ctx, &request)
	if err != nil {
		return err
	}

	c.JSON(http.StatusOK, &Epay_Refund_Success{
		Status:     true,
		Message:    "Success",
		StatusCode: 0,
		Result:     response,
	})
	return nil
}

type Epay_GooglePay_Success struct {
	// Status is the status of the response. Set true
	Status bool `json:"status"`
	// Message is a human-readable explanation of the status
	Message string `json:"message"`
	// StatusCode is the GoErr status. Set 0
	StatusCode int `json:"status_code"`
	// Result is the result of the request
	Result *GooglePayResponseData `json:"result"`
}

type Epay_GooglePay_Failure struct {
	// Status is the status of the response. Set false
	Status bool `json:"status"`
	// Message is a human-readable explanation of the status
	Message string `json:"message"`
	// StatusCode is the GoErr status
	StatusCode int `json:"status_code"`
}

// GooglePay
// @Summary GooglePay
// @Security bearerAuth
// @ID Epay_GooglePay
// @Accept json
// @Param request body GooglePayRequestData true "GooglePayRequestData"
// @Success 200 {object} Epay_GooglePay_Success
// @Failure 401 {object} Epay_GooglePay_Failure "goerrpkg.ErrorTypeAuthorization"
// @Failure 404 {object} Epay_GooglePay_Failure "goerrpkg.ErrorTypeNotFound"
// @Failure 400 {object} Epay_GooglePay_Failure "goerrpkg.ErrorTypeBadRequest"
// @Failure 500 {object} Epay_GooglePay_Failure "goerrpkg.ErrorTypeUnexpectedError"
// @Failure 400 {object} Epay_GooglePay_Failure "gin.ErrorTypeBind"
// @Failure 500 {object} Epay_GooglePay_Failure "Undefined error"
// @Produce json
// @Router /processing.epay.epay.Epay/GooglePay [put]
// @tags Epay
func (s *ginEpayServer) GooglePay(c *gin.Context) error {
	ctx, span := dog.CreateSpan(c.Request.Context(), "GinEpayServer_GooglePay")
	defer span.End()

	var request GooglePayRequestData
	if err := c.ShouldBindJSON(&request); err != nil {
		return err
	}

	ctx = metadata.NewOutgoingContext(ctx, metadata.MD(c.Request.Header))

	response, err := s.client.GooglePay(ctx, &request)
	if err != nil {
		return err
	}

	c.JSON(http.StatusOK, &Epay_GooglePay_Success{
		Status:     true,
		Message:    "Success",
		StatusCode: 0,
		Result:     response,
	})
	return nil
}

type Epay_ApplePay_Success struct {
	// Status is the status of the response. Set true
	Status bool `json:"status"`
	// Message is a human-readable explanation of the status
	Message string `json:"message"`
	// StatusCode is the GoErr status. Set 0
	StatusCode int `json:"status_code"`
	// Result is the result of the request
	Result *ApplePayResponseData `json:"result"`
}

type Epay_ApplePay_Failure struct {
	// Status is the status of the response. Set false
	Status bool `json:"status"`
	// Message is a human-readable explanation of the status
	Message string `json:"message"`
	// StatusCode is the GoErr status
	StatusCode int `json:"status_code"`
}

// ApplePay
// @Summary ApplePay
// @Security bearerAuth
// @ID Epay_ApplePay
// @Accept json
// @Param request body ApplePayRequestData true "ApplePayRequestData"
// @Success 200 {object} Epay_ApplePay_Success
// @Failure 401 {object} Epay_ApplePay_Failure "goerrpkg.ErrorTypeAuthorization"
// @Failure 404 {object} Epay_ApplePay_Failure "goerrpkg.ErrorTypeNotFound"
// @Failure 400 {object} Epay_ApplePay_Failure "goerrpkg.ErrorTypeBadRequest"
// @Failure 500 {object} Epay_ApplePay_Failure "goerrpkg.ErrorTypeUnexpectedError"
// @Failure 400 {object} Epay_ApplePay_Failure "gin.ErrorTypeBind"
// @Failure 500 {object} Epay_ApplePay_Failure "Undefined error"
// @Produce json
// @Router /processing.epay.epay.Epay/ApplePay [put]
// @tags Epay
func (s *ginEpayServer) ApplePay(c *gin.Context) error {
	ctx, span := dog.CreateSpan(c.Request.Context(), "GinEpayServer_ApplePay")
	defer span.End()

	var request ApplePayRequestData
	if err := c.ShouldBindJSON(&request); err != nil {
		return err
	}

	ctx = metadata.NewOutgoingContext(ctx, metadata.MD(c.Request.Header))

	response, err := s.client.ApplePay(ctx, &request)
	if err != nil {
		return err
	}

	c.JSON(http.StatusOK, &Epay_ApplePay_Success{
		Status:     true,
		Message:    "Success",
		StatusCode: 0,
		Result:     response,
	})
	return nil
}

type Epay_TwoStagePayIn_Success struct {
	// Status is the status of the response. Set true
	Status bool `json:"status"`
	// Message is a human-readable explanation of the status
	Message string `json:"message"`
	// StatusCode is the GoErr status. Set 0
	StatusCode int `json:"status_code"`
	// Result is the result of the request
	Result *TwoStagePayInResponse `json:"result"`
}

type Epay_TwoStagePayIn_Failure struct {
	// Status is the status of the response. Set false
	Status bool `json:"status"`
	// Message is a human-readable explanation of the status
	Message string `json:"message"`
	// StatusCode is the GoErr status
	StatusCode int `json:"status_code"`
}

// TwoStagePayIn
// @Summary TwoStagePayIn
// @Security bearerAuth
// @ID Epay_TwoStagePayIn
// @Accept json
// @Param request body TwoStagePayInRequest true "TwoStagePayInRequest"
// @Success 200 {object} Epay_TwoStagePayIn_Success
// @Failure 401 {object} Epay_TwoStagePayIn_Failure "goerrpkg.ErrorTypeAuthorization"
// @Failure 404 {object} Epay_TwoStagePayIn_Failure "goerrpkg.ErrorTypeNotFound"
// @Failure 400 {object} Epay_TwoStagePayIn_Failure "goerrpkg.ErrorTypeBadRequest"
// @Failure 500 {object} Epay_TwoStagePayIn_Failure "goerrpkg.ErrorTypeUnexpectedError"
// @Failure 400 {object} Epay_TwoStagePayIn_Failure "gin.ErrorTypeBind"
// @Failure 500 {object} Epay_TwoStagePayIn_Failure "Undefined error"
// @Produce json
// @Router /processing.epay.epay.Epay/TwoStagePayIn [put]
// @tags Epay
func (s *ginEpayServer) TwoStagePayIn(c *gin.Context) error {
	ctx, span := dog.CreateSpan(c.Request.Context(), "GinEpayServer_TwoStagePayIn")
	defer span.End()

	var request TwoStagePayInRequest
	if err := c.ShouldBindJSON(&request); err != nil {
		return err
	}

	ctx = metadata.NewOutgoingContext(ctx, metadata.MD(c.Request.Header))

	response, err := s.client.TwoStagePayIn(ctx, &request)
	if err != nil {
		return err
	}

	c.JSON(http.StatusOK, &Epay_TwoStagePayIn_Success{
		Status:     true,
		Message:    "Success",
		StatusCode: 0,
		Result:     response,
	})
	return nil
}

type Epay_Charge_Success struct {
	// Status is the status of the response. Set true
	Status bool `json:"status"`
	// Message is a human-readable explanation of the status
	Message string `json:"message"`
	// StatusCode is the GoErr status. Set 0
	StatusCode int `json:"status_code"`
	// Result is the result of the request
	Result *ChargeResponse `json:"result"`
}

type Epay_Charge_Failure struct {
	// Status is the status of the response. Set false
	Status bool `json:"status"`
	// Message is a human-readable explanation of the status
	Message string `json:"message"`
	// StatusCode is the GoErr status
	StatusCode int `json:"status_code"`
}

// Charge
// @Summary Charge
// @Security bearerAuth
// @ID Epay_Charge
// @Accept json
// @Param request body ChargeRequest true "ChargeRequest"
// @Success 200 {object} Epay_Charge_Success
// @Failure 401 {object} Epay_Charge_Failure "goerrpkg.ErrorTypeAuthorization"
// @Failure 404 {object} Epay_Charge_Failure "goerrpkg.ErrorTypeNotFound"
// @Failure 400 {object} Epay_Charge_Failure "goerrpkg.ErrorTypeBadRequest"
// @Failure 500 {object} Epay_Charge_Failure "goerrpkg.ErrorTypeUnexpectedError"
// @Failure 400 {object} Epay_Charge_Failure "gin.ErrorTypeBind"
// @Failure 500 {object} Epay_Charge_Failure "Undefined error"
// @Produce json
// @Router /processing.epay.epay.Epay/Charge [put]
// @tags Epay
func (s *ginEpayServer) Charge(c *gin.Context) error {
	ctx, span := dog.CreateSpan(c.Request.Context(), "GinEpayServer_Charge")
	defer span.End()

	var request ChargeRequest
	if err := c.ShouldBindJSON(&request); err != nil {
		return err
	}

	ctx = metadata.NewOutgoingContext(ctx, metadata.MD(c.Request.Header))

	response, err := s.client.Charge(ctx, &request)
	if err != nil {
		return err
	}

	c.JSON(http.StatusOK, &Epay_Charge_Success{
		Status:     true,
		Message:    "Success",
		StatusCode: 0,
		Result:     response,
	})
	return nil
}

type Epay_Cancel_Success struct {
	// Status is the status of the response. Set true
	Status bool `json:"status"`
	// Message is a human-readable explanation of the status
	Message string `json:"message"`
	// StatusCode is the GoErr status. Set 0
	StatusCode int `json:"status_code"`
	// Result is the result of the request
	Result *CancelResponse `json:"result"`
}

type Epay_Cancel_Failure struct {
	// Status is the status of the response. Set false
	Status bool `json:"status"`
	// Message is a human-readable explanation of the status
	Message string `json:"message"`
	// StatusCode is the GoErr status
	StatusCode int `json:"status_code"`
}

// Cancel
// @Summary Cancel
// @Security bearerAuth
// @ID Epay_Cancel
// @Accept json
// @Param request body CancelRequest true "CancelRequest"
// @Success 200 {object} Epay_Cancel_Success
// @Failure 401 {object} Epay_Cancel_Failure "goerrpkg.ErrorTypeAuthorization"
// @Failure 404 {object} Epay_Cancel_Failure "goerrpkg.ErrorTypeNotFound"
// @Failure 400 {object} Epay_Cancel_Failure "goerrpkg.ErrorTypeBadRequest"
// @Failure 500 {object} Epay_Cancel_Failure "goerrpkg.ErrorTypeUnexpectedError"
// @Failure 400 {object} Epay_Cancel_Failure "gin.ErrorTypeBind"
// @Failure 500 {object} Epay_Cancel_Failure "Undefined error"
// @Produce json
// @Router /processing.epay.epay.Epay/Cancel [put]
// @tags Epay
func (s *ginEpayServer) Cancel(c *gin.Context) error {
	ctx, span := dog.CreateSpan(c.Request.Context(), "GinEpayServer_Cancel")
	defer span.End()

	var request CancelRequest
	if err := c.ShouldBindJSON(&request); err != nil {
		return err
	}

	ctx = metadata.NewOutgoingContext(ctx, metadata.MD(c.Request.Header))

	response, err := s.client.Cancel(ctx, &request)
	if err != nil {
		return err
	}

	c.JSON(http.StatusOK, &Epay_Cancel_Success{
		Status:     true,
		Message:    "Success",
		StatusCode: 0,
		Result:     response,
	})
	return nil
}

type Epay_MakeToken_Success struct {
	// Status is the status of the response. Set true
	Status bool `json:"status"`
	// Message is a human-readable explanation of the status
	Message string `json:"message"`
	// StatusCode is the GoErr status. Set 0
	StatusCode int `json:"status_code"`
	// Result is the result of the request
	Result *PayInResponseData `json:"result"`
}

type Epay_MakeToken_Failure struct {
	// Status is the status of the response. Set false
	Status bool `json:"status"`
	// Message is a human-readable explanation of the status
	Message string `json:"message"`
	// StatusCode is the GoErr status
	StatusCode int `json:"status_code"`
}

// MakeToken
// @Summary MakeToken
// @Security bearerAuth
// @ID Epay_MakeToken
// @Accept json
// @Param request body PayInRequestData true "PayInRequestData"
// @Success 200 {object} Epay_MakeToken_Success
// @Failure 401 {object} Epay_MakeToken_Failure "goerrpkg.ErrorTypeAuthorization"
// @Failure 404 {object} Epay_MakeToken_Failure "goerrpkg.ErrorTypeNotFound"
// @Failure 400 {object} Epay_MakeToken_Failure "goerrpkg.ErrorTypeBadRequest"
// @Failure 500 {object} Epay_MakeToken_Failure "goerrpkg.ErrorTypeUnexpectedError"
// @Failure 400 {object} Epay_MakeToken_Failure "gin.ErrorTypeBind"
// @Failure 500 {object} Epay_MakeToken_Failure "Undefined error"
// @Produce json
// @Router /processing.epay.epay.Epay/MakeToken [put]
// @tags Epay
func (s *ginEpayServer) MakeToken(c *gin.Context) error {
	ctx, span := dog.CreateSpan(c.Request.Context(), "GinEpayServer_MakeToken")
	defer span.End()

	var request PayInRequestData
	if err := c.ShouldBindJSON(&request); err != nil {
		return err
	}

	ctx = metadata.NewOutgoingContext(ctx, metadata.MD(c.Request.Header))

	response, err := s.client.MakeToken(ctx, &request)
	if err != nil {
		return err
	}

	c.JSON(http.StatusOK, &Epay_MakeToken_Success{
		Status:     true,
		Message:    "Success",
		StatusCode: 0,
		Result:     response,
	})
	return nil
}

type Epay_GetAcquirerIdentifier_Success struct {
	// Status is the status of the response. Set true
	Status bool `json:"status"`
	// Message is a human-readable explanation of the status
	Message string `json:"message"`
	// StatusCode is the GoErr status. Set 0
	StatusCode int `json:"status_code"`
	// Result is the result of the request
	Result *GetAcquirerIdentifierResponse `json:"result"`
}

type Epay_GetAcquirerIdentifier_Failure struct {
	// Status is the status of the response. Set false
	Status bool `json:"status"`
	// Message is a human-readable explanation of the status
	Message string `json:"message"`
	// StatusCode is the GoErr status
	StatusCode int `json:"status_code"`
}

// GetAcquirerIdentifier
// @Summary GetAcquirerIdentifier
// @Security bearerAuth
// @ID Epay_GetAcquirerIdentifier
// @Accept json
// @Param request body emptypb.Empty true "Empty"
// @Success 200 {object} Epay_GetAcquirerIdentifier_Success
// @Failure 401 {object} Epay_GetAcquirerIdentifier_Failure "goerrpkg.ErrorTypeAuthorization"
// @Failure 404 {object} Epay_GetAcquirerIdentifier_Failure "goerrpkg.ErrorTypeNotFound"
// @Failure 400 {object} Epay_GetAcquirerIdentifier_Failure "goerrpkg.ErrorTypeBadRequest"
// @Failure 500 {object} Epay_GetAcquirerIdentifier_Failure "goerrpkg.ErrorTypeUnexpectedError"
// @Failure 400 {object} Epay_GetAcquirerIdentifier_Failure "gin.ErrorTypeBind"
// @Failure 500 {object} Epay_GetAcquirerIdentifier_Failure "Undefined error"
// @Produce json
// @Router /processing.epay.epay.Epay/GetAcquirerIdentifier [put]
// @tags Epay
func (s *ginEpayServer) GetAcquirerIdentifier(c *gin.Context) error {
	ctx, span := dog.CreateSpan(c.Request.Context(), "GinEpayServer_GetAcquirerIdentifier")
	defer span.End()

	var request emptypb.Empty
	if err := c.ShouldBindJSON(&request); err != nil {
		return err
	}

	ctx = metadata.NewOutgoingContext(ctx, metadata.MD(c.Request.Header))

	response, err := s.client.GetAcquirerIdentifier(ctx, &request)
	if err != nil {
		return err
	}

	c.JSON(http.StatusOK, &Epay_GetAcquirerIdentifier_Success{
		Status:     true,
		Message:    "Success",
		StatusCode: 0,
		Result:     response,
	})
	return nil
}

type Epay_ResolveVisaAlias_Success struct {
	// Status is the status of the response. Set true
	Status bool `json:"status"`
	// Message is a human-readable explanation of the status
	Message string `json:"message"`
	// StatusCode is the GoErr status. Set 0
	StatusCode int `json:"status_code"`
	// Result is the result of the request
	Result *ResolveVisaAliasResponse `json:"result"`
}

type Epay_ResolveVisaAlias_Failure struct {
	// Status is the status of the response. Set false
	Status bool `json:"status"`
	// Message is a human-readable explanation of the status
	Message string `json:"message"`
	// StatusCode is the GoErr status
	StatusCode int `json:"status_code"`
}

// ResolveVisaAlias
// @Summary ResolveVisaAlias
// @Security bearerAuth
// @ID Epay_ResolveVisaAlias
// @Accept json
// @Param request body ResolveVisaAliasRequest true "ResolveVisaAliasRequest"
// @Success 200 {object} Epay_ResolveVisaAlias_Success
// @Failure 401 {object} Epay_ResolveVisaAlias_Failure "goerrpkg.ErrorTypeAuthorization"
// @Failure 404 {object} Epay_ResolveVisaAlias_Failure "goerrpkg.ErrorTypeNotFound"
// @Failure 400 {object} Epay_ResolveVisaAlias_Failure "goerrpkg.ErrorTypeBadRequest"
// @Failure 500 {object} Epay_ResolveVisaAlias_Failure "goerrpkg.ErrorTypeUnexpectedError"
// @Failure 400 {object} Epay_ResolveVisaAlias_Failure "gin.ErrorTypeBind"
// @Failure 500 {object} Epay_ResolveVisaAlias_Failure "Undefined error"
// @Produce json
// @Router /processing.epay.epay.Epay/ResolveVisaAlias [put]
// @tags Epay
func (s *ginEpayServer) ResolveVisaAlias(c *gin.Context) error {
	ctx, span := dog.CreateSpan(c.Request.Context(), "GinEpayServer_ResolveVisaAlias")
	defer span.End()

	var request ResolveVisaAliasRequest
	if err := c.ShouldBindJSON(&request); err != nil {
		return err
	}

	ctx = metadata.NewOutgoingContext(ctx, metadata.MD(c.Request.Header))

	response, err := s.client.ResolveVisaAlias(ctx, &request)
	if err != nil {
		return err
	}

	c.JSON(http.StatusOK, &Epay_ResolveVisaAlias_Success{
		Status:     true,
		Message:    "Success",
		StatusCode: 0,
		Result:     response,
	})
	return nil
}

type Epay_PayOutByPhone_Success struct {
	// Status is the status of the response. Set true
	Status bool `json:"status"`
	// Message is a human-readable explanation of the status
	Message string `json:"message"`
	// StatusCode is the GoErr status. Set 0
	StatusCode int `json:"status_code"`
	// Result is the result of the request
	Result *PayOutResponseByPhoneData `json:"result"`
}

type Epay_PayOutByPhone_Failure struct {
	// Status is the status of the response. Set false
	Status bool `json:"status"`
	// Message is a human-readable explanation of the status
	Message string `json:"message"`
	// StatusCode is the GoErr status
	StatusCode int `json:"status_code"`
}

// PayOutByPhone
// @Summary PayOutByPhone
// @Security bearerAuth
// @ID Epay_PayOutByPhone
// @Accept json
// @Param request body PayOutByPhoneRequestData true "PayOutByPhoneRequestData"
// @Success 200 {object} Epay_PayOutByPhone_Success
// @Failure 401 {object} Epay_PayOutByPhone_Failure "goerrpkg.ErrorTypeAuthorization"
// @Failure 404 {object} Epay_PayOutByPhone_Failure "goerrpkg.ErrorTypeNotFound"
// @Failure 400 {object} Epay_PayOutByPhone_Failure "goerrpkg.ErrorTypeBadRequest"
// @Failure 500 {object} Epay_PayOutByPhone_Failure "goerrpkg.ErrorTypeUnexpectedError"
// @Failure 400 {object} Epay_PayOutByPhone_Failure "gin.ErrorTypeBind"
// @Failure 500 {object} Epay_PayOutByPhone_Failure "Undefined error"
// @Produce json
// @Router /processing.epay.epay.Epay/PayOutByPhone [put]
// @tags Epay
func (s *ginEpayServer) PayOutByPhone(c *gin.Context) error {
	ctx, span := dog.CreateSpan(c.Request.Context(), "GinEpayServer_PayOutByPhone")
	defer span.End()

	var request PayOutByPhoneRequestData
	if err := c.ShouldBindJSON(&request); err != nil {
		return err
	}

	ctx = metadata.NewOutgoingContext(ctx, metadata.MD(c.Request.Header))

	response, err := s.client.PayOutByPhone(ctx, &request)
	if err != nil {
		return err
	}

	c.JSON(http.StatusOK, &Epay_PayOutByPhone_Success{
		Status:     true,
		Message:    "Success",
		StatusCode: 0,
		Result:     response,
	})
	return nil
}
