// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.5.1
// - protoc             v5.29.3
// source: inner/processing/grpc/notification.proto

package grpc

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.64.0 or later.
const _ = grpc.SupportPackageIsVersion9

const (
	Notification_GetLastMailByUserEmail_FullMethodName = "/processing.notification.notification.Notification/GetLastMailByUserEmail"
	Notification_GetLastSMSByUserPhone_FullMethodName  = "/processing.notification.notification.Notification/GetLastSMSByUserPhone"
)

// NotificationClient is the client API for Notification service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type NotificationClient interface {
	GetLastMailByUserEmail(ctx context.Context, in *GetMailReqDataV1, opts ...grpc.CallOption) (*GetMailResDataV1, error)
	GetLastSMSByUserPhone(ctx context.Context, in *GetSMSReqDataV1, opts ...grpc.CallOption) (*GetSMSResDataV1, error)
}

type notificationClient struct {
	cc grpc.ClientConnInterface
}

func NewNotificationClient(cc grpc.ClientConnInterface) NotificationClient {
	return &notificationClient{cc}
}

func (c *notificationClient) GetLastMailByUserEmail(ctx context.Context, in *GetMailReqDataV1, opts ...grpc.CallOption) (*GetMailResDataV1, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetMailResDataV1)
	err := c.cc.Invoke(ctx, Notification_GetLastMailByUserEmail_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *notificationClient) GetLastSMSByUserPhone(ctx context.Context, in *GetSMSReqDataV1, opts ...grpc.CallOption) (*GetSMSResDataV1, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetSMSResDataV1)
	err := c.cc.Invoke(ctx, Notification_GetLastSMSByUserPhone_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// NotificationServer is the server API for Notification service.
// All implementations must embed UnimplementedNotificationServer
// for forward compatibility.
type NotificationServer interface {
	GetLastMailByUserEmail(context.Context, *GetMailReqDataV1) (*GetMailResDataV1, error)
	GetLastSMSByUserPhone(context.Context, *GetSMSReqDataV1) (*GetSMSResDataV1, error)
	mustEmbedUnimplementedNotificationServer()
}

// UnimplementedNotificationServer must be embedded to have
// forward compatible implementations.
//
// NOTE: this should be embedded by value instead of pointer to avoid a nil
// pointer dereference when methods are called.
type UnimplementedNotificationServer struct{}

func (UnimplementedNotificationServer) GetLastMailByUserEmail(context.Context, *GetMailReqDataV1) (*GetMailResDataV1, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetLastMailByUserEmail not implemented")
}
func (UnimplementedNotificationServer) GetLastSMSByUserPhone(context.Context, *GetSMSReqDataV1) (*GetSMSResDataV1, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetLastSMSByUserPhone not implemented")
}
func (UnimplementedNotificationServer) mustEmbedUnimplementedNotificationServer() {}
func (UnimplementedNotificationServer) testEmbeddedByValue()                      {}

// UnsafeNotificationServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to NotificationServer will
// result in compilation errors.
type UnsafeNotificationServer interface {
	mustEmbedUnimplementedNotificationServer()
}

func RegisterNotificationServer(s grpc.ServiceRegistrar, srv NotificationServer) {
	// If the following call pancis, it indicates UnimplementedNotificationServer was
	// embedded by pointer and is nil.  This will cause panics if an
	// unimplemented method is ever invoked, so we test this at initialization
	// time to prevent it from happening at runtime later due to I/O.
	if t, ok := srv.(interface{ testEmbeddedByValue() }); ok {
		t.testEmbeddedByValue()
	}
	s.RegisterService(&Notification_ServiceDesc, srv)
}

func _Notification_GetLastMailByUserEmail_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetMailReqDataV1)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NotificationServer).GetLastMailByUserEmail(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Notification_GetLastMailByUserEmail_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NotificationServer).GetLastMailByUserEmail(ctx, req.(*GetMailReqDataV1))
	}
	return interceptor(ctx, in, info, handler)
}

func _Notification_GetLastSMSByUserPhone_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetSMSReqDataV1)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NotificationServer).GetLastSMSByUserPhone(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Notification_GetLastSMSByUserPhone_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NotificationServer).GetLastSMSByUserPhone(ctx, req.(*GetSMSReqDataV1))
	}
	return interceptor(ctx, in, info, handler)
}

// Notification_ServiceDesc is the grpc.ServiceDesc for Notification service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var Notification_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "processing.notification.notification.Notification",
	HandlerType: (*NotificationServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetLastMailByUserEmail",
			Handler:    _Notification_GetLastMailByUserEmail_Handler,
		},
		{
			MethodName: "GetLastSMSByUserPhone",
			Handler:    _Notification_GetLastSMSByUserPhone_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "inner/processing/grpc/notification.proto",
}
