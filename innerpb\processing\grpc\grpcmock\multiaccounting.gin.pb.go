// Code generated by MockGen. DO NOT EDIT.
// Source: multiaccounting.gin.pb.go

// Package grpcmock is a generated GoMock package.
package grpcmock

import (
	reflect "reflect"

	gin "github.com/gin-gonic/gin"
	gomock "github.com/golang/mock/gomock"
)

// MockGinMultiaccountingServer is a mock of GinMultiaccountingServer interface.
type MockGinMultiaccountingServer struct {
	ctrl     *gomock.Controller
	recorder *MockGinMultiaccountingServerMockRecorder
}

// MockGinMultiaccountingServerMockRecorder is the mock recorder for MockGinMultiaccountingServer.
type MockGinMultiaccountingServerMockRecorder struct {
	mock *MockGinMultiaccountingServer
}

// NewMockGinMultiaccountingServer creates a new mock instance.
func NewMockGinMultiaccountingServer(ctrl *gomock.Controller) *MockGinMultiaccountingServer {
	mock := &MockGinMultiaccountingServer{ctrl: ctrl}
	mock.recorder = &MockGinMultiaccountingServerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockGinMultiaccountingServer) EXPECT() *MockGinMultiaccountingServerMockRecorder {
	return m.recorder
}

// AcceptTransfer mocks base method.
func (m *MockGinMultiaccountingServer) AcceptTransfer(c *gin.Context) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AcceptTransfer", c)
	ret0, _ := ret[0].(error)
	return ret0
}

// AcceptTransfer indicates an expected call of AcceptTransfer.
func (mr *MockGinMultiaccountingServerMockRecorder) AcceptTransfer(c interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AcceptTransfer", reflect.TypeOf((*MockGinMultiaccountingServer)(nil).AcceptTransfer), c)
}

// DeclineTransfer mocks base method.
func (m *MockGinMultiaccountingServer) DeclineTransfer(c *gin.Context) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeclineTransfer", c)
	ret0, _ := ret[0].(error)
	return ret0
}

// DeclineTransfer indicates an expected call of DeclineTransfer.
func (mr *MockGinMultiaccountingServerMockRecorder) DeclineTransfer(c interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeclineTransfer", reflect.TypeOf((*MockGinMultiaccountingServer)(nil).DeclineTransfer), c)
}

// GetAccountBalance mocks base method.
func (m *MockGinMultiaccountingServer) GetAccountBalance(c *gin.Context) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAccountBalance", c)
	ret0, _ := ret[0].(error)
	return ret0
}

// GetAccountBalance indicates an expected call of GetAccountBalance.
func (mr *MockGinMultiaccountingServerMockRecorder) GetAccountBalance(c interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAccountBalance", reflect.TypeOf((*MockGinMultiaccountingServer)(nil).GetAccountBalance), c)
}

// GetAccountIdentifier mocks base method.
func (m *MockGinMultiaccountingServer) GetAccountIdentifier(c *gin.Context) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAccountIdentifier", c)
	ret0, _ := ret[0].(error)
	return ret0
}

// GetAccountIdentifier indicates an expected call of GetAccountIdentifier.
func (mr *MockGinMultiaccountingServerMockRecorder) GetAccountIdentifier(c interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAccountIdentifier", reflect.TypeOf((*MockGinMultiaccountingServer)(nil).GetAccountIdentifier), c)
}

// GetAccountStatement mocks base method.
func (m *MockGinMultiaccountingServer) GetAccountStatement(c *gin.Context) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAccountStatement", c)
	ret0, _ := ret[0].(error)
	return ret0
}

// GetAccountStatement indicates an expected call of GetAccountStatement.
func (mr *MockGinMultiaccountingServerMockRecorder) GetAccountStatement(c interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAccountStatement", reflect.TypeOf((*MockGinMultiaccountingServer)(nil).GetAccountStatement), c)
}

// GetTransferDetails mocks base method.
func (m *MockGinMultiaccountingServer) GetTransferDetails(c *gin.Context) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetTransferDetails", c)
	ret0, _ := ret[0].(error)
	return ret0
}

// GetTransferDetails indicates an expected call of GetTransferDetails.
func (mr *MockGinMultiaccountingServerMockRecorder) GetTransferDetails(c interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetTransferDetails", reflect.TypeOf((*MockGinMultiaccountingServer)(nil).GetTransferDetails), c)
}

// GetTransfersList mocks base method.
func (m *MockGinMultiaccountingServer) GetTransfersList(c *gin.Context) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetTransfersList", c)
	ret0, _ := ret[0].(error)
	return ret0
}

// GetTransfersList indicates an expected call of GetTransfersList.
func (mr *MockGinMultiaccountingServerMockRecorder) GetTransfersList(c interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetTransfersList", reflect.TypeOf((*MockGinMultiaccountingServer)(nil).GetTransfersList), c)
}

// MakeMerchantCheck mocks base method.
func (m *MockGinMultiaccountingServer) MakeMerchantCheck(c *gin.Context) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "MakeMerchantCheck", c)
	ret0, _ := ret[0].(error)
	return ret0
}

// MakeMerchantCheck indicates an expected call of MakeMerchantCheck.
func (mr *MockGinMultiaccountingServerMockRecorder) MakeMerchantCheck(c interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "MakeMerchantCheck", reflect.TypeOf((*MockGinMultiaccountingServer)(nil).MakeMerchantCheck), c)
}

// MakeTransfer mocks base method.
func (m *MockGinMultiaccountingServer) MakeTransfer(c *gin.Context) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "MakeTransfer", c)
	ret0, _ := ret[0].(error)
	return ret0
}

// MakeTransfer indicates an expected call of MakeTransfer.
func (mr *MockGinMultiaccountingServerMockRecorder) MakeTransfer(c interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "MakeTransfer", reflect.TypeOf((*MockGinMultiaccountingServer)(nil).MakeTransfer), c)
}

// RedoTransfer mocks base method.
func (m *MockGinMultiaccountingServer) RedoTransfer(c *gin.Context) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "RedoTransfer", c)
	ret0, _ := ret[0].(error)
	return ret0
}

// RedoTransfer indicates an expected call of RedoTransfer.
func (mr *MockGinMultiaccountingServerMockRecorder) RedoTransfer(c interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "RedoTransfer", reflect.TypeOf((*MockGinMultiaccountingServer)(nil).RedoTransfer), c)
}
