package v1

import (
	"git.local/sensitive/processing/account/service"
	"github.com/gin-gonic/gin"
)

type Handler struct {
	services *service.Services
}

func NewHandler(services *service.Services) *Handler {
	return &Handler{
		services: services,
	}
}

func (h *Handler) Init(api *gin.RouterGroup) {
	v1 := api.Group("/v1")
	{
		h.initMerchantTransferHandler(v1)
		h.initCRMTransferHandler(v1)
		h.initMerchantTransactionInfoHandler(v1)
		h.initCRMTransactionInfoHandler(v1)
		h.initAccountInfoHandler(v1)
		h.initTransferTypeHandler(v1)
		h.initTransferStatusHandler(v1)
	}
}
