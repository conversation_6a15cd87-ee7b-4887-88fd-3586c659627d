// Code generated by MockGen. DO NOT EDIT.
// Source: jusan_grpc.pb.go

// Package grpcmock is a generated GoMock package.
package grpcmock

import (
	context "context"
	reflect "reflect"

	grpc "git.local/sensitive/innerpb/processing/grpc"
	gomock "github.com/golang/mock/gomock"
	grpc0 "google.golang.org/grpc"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
)

// MockJusanClient is a mock of JusanClient interface.
type MockJusanClient struct {
	ctrl     *gomock.Controller
	recorder *MockJusanClientMockRecorder
}

// MockJusanClientMockRecorder is the mock recorder for MockJusanClient.
type MockJusanClientMockRecorder struct {
	mock *MockJusanClient
}

// NewMockJusanClient creates a new mock instance.
func NewMockJusanClient(ctrl *gomock.Controller) *MockJusanClient {
	mock := &MockJusanClient{ctrl: ctrl}
	mock.recorder = &MockJusanClientMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockJusanClient) EXPECT() *MockJusanClientMockRecorder {
	return m.recorder
}

// ApplePay mocks base method.
func (m *MockJusanClient) ApplePay(ctx context.Context, in *grpc.ApplePayRequestData, opts ...grpc0.CallOption) (*grpc.ApplePayResponseData, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "ApplePay", varargs...)
	ret0, _ := ret[0].(*grpc.ApplePayResponseData)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ApplePay indicates an expected call of ApplePay.
func (mr *MockJusanClientMockRecorder) ApplePay(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ApplePay", reflect.TypeOf((*MockJusanClient)(nil).ApplePay), varargs...)
}

// Cancel mocks base method.
func (m *MockJusanClient) Cancel(ctx context.Context, in *grpc.CancelRequest, opts ...grpc0.CallOption) (*grpc.CancelResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "Cancel", varargs...)
	ret0, _ := ret[0].(*grpc.CancelResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Cancel indicates an expected call of Cancel.
func (mr *MockJusanClientMockRecorder) Cancel(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Cancel", reflect.TypeOf((*MockJusanClient)(nil).Cancel), varargs...)
}

// Charge mocks base method.
func (m *MockJusanClient) Charge(ctx context.Context, in *grpc.ChargeRequest, opts ...grpc0.CallOption) (*grpc.ChargeResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "Charge", varargs...)
	ret0, _ := ret[0].(*grpc.ChargeResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Charge indicates an expected call of Charge.
func (mr *MockJusanClientMockRecorder) Charge(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Charge", reflect.TypeOf((*MockJusanClient)(nil).Charge), varargs...)
}

// CheckBalance mocks base method.
func (m *MockJusanClient) CheckBalance(ctx context.Context, in *grpc.CheckBalanceRequest, opts ...grpc0.CallOption) (*grpc.CheckBalanceResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "CheckBalance", varargs...)
	ret0, _ := ret[0].(*grpc.CheckBalanceResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CheckBalance indicates an expected call of CheckBalance.
func (mr *MockJusanClientMockRecorder) CheckBalance(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CheckBalance", reflect.TypeOf((*MockJusanClient)(nil).CheckBalance), varargs...)
}

// ConfirmEmission mocks base method.
func (m *MockJusanClient) ConfirmEmission(ctx context.Context, in *grpc.EmoneyRequest, opts ...grpc0.CallOption) (*grpc.EmoneyResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "ConfirmEmission", varargs...)
	ret0, _ := ret[0].(*grpc.EmoneyResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ConfirmEmission indicates an expected call of ConfirmEmission.
func (mr *MockJusanClientMockRecorder) ConfirmEmission(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ConfirmEmission", reflect.TypeOf((*MockJusanClient)(nil).ConfirmEmission), varargs...)
}

// GetAcquirerIdentifier mocks base method.
func (m *MockJusanClient) GetAcquirerIdentifier(ctx context.Context, in *emptypb.Empty, opts ...grpc0.CallOption) (*grpc.GetAcquirerIdentifierResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetAcquirerIdentifier", varargs...)
	ret0, _ := ret[0].(*grpc.GetAcquirerIdentifierResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAcquirerIdentifier indicates an expected call of GetAcquirerIdentifier.
func (mr *MockJusanClientMockRecorder) GetAcquirerIdentifier(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAcquirerIdentifier", reflect.TypeOf((*MockJusanClient)(nil).GetAcquirerIdentifier), varargs...)
}

// GetBankTransactionStatus mocks base method.
func (m *MockJusanClient) GetBankTransactionStatus(ctx context.Context, in *grpc.BankTransactionStatusRequest, opts ...grpc0.CallOption) (*grpc.BankTransactionStatusResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetBankTransactionStatus", varargs...)
	ret0, _ := ret[0].(*grpc.BankTransactionStatusResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetBankTransactionStatus indicates an expected call of GetBankTransactionStatus.
func (mr *MockJusanClientMockRecorder) GetBankTransactionStatus(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetBankTransactionStatus", reflect.TypeOf((*MockJusanClient)(nil).GetBankTransactionStatus), varargs...)
}

// GetBankTransactionStatusUnformated mocks base method.
func (m *MockJusanClient) GetBankTransactionStatusUnformated(ctx context.Context, in *grpc.BankTransactionStatusUnformatedRequest, opts ...grpc0.CallOption) (*grpc.BankTransactionStatusUnformatedResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetBankTransactionStatusUnformated", varargs...)
	ret0, _ := ret[0].(*grpc.BankTransactionStatusUnformatedResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetBankTransactionStatusUnformated indicates an expected call of GetBankTransactionStatusUnformated.
func (mr *MockJusanClientMockRecorder) GetBankTransactionStatusUnformated(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetBankTransactionStatusUnformated", reflect.TypeOf((*MockJusanClient)(nil).GetBankTransactionStatusUnformated), varargs...)
}

// GetEmission mocks base method.
func (m *MockJusanClient) GetEmission(ctx context.Context, in *emptypb.Empty, opts ...grpc0.CallOption) (*grpc.EmissionResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetEmission", varargs...)
	ret0, _ := ret[0].(*grpc.EmissionResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetEmission indicates an expected call of GetEmission.
func (mr *MockJusanClientMockRecorder) GetEmission(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetEmission", reflect.TypeOf((*MockJusanClient)(nil).GetEmission), varargs...)
}

// GooglePay mocks base method.
func (m *MockJusanClient) GooglePay(ctx context.Context, in *grpc.GooglePayRequestData, opts ...grpc0.CallOption) (*grpc.GooglePayResponseData, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GooglePay", varargs...)
	ret0, _ := ret[0].(*grpc.GooglePayResponseData)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GooglePay indicates an expected call of GooglePay.
func (mr *MockJusanClientMockRecorder) GooglePay(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GooglePay", reflect.TypeOf((*MockJusanClient)(nil).GooglePay), varargs...)
}

// MakeToken mocks base method.
func (m *MockJusanClient) MakeToken(ctx context.Context, in *grpc.PayInRequestData, opts ...grpc0.CallOption) (*grpc.PayInResponseData, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "MakeToken", varargs...)
	ret0, _ := ret[0].(*grpc.PayInResponseData)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// MakeToken indicates an expected call of MakeToken.
func (mr *MockJusanClientMockRecorder) MakeToken(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "MakeToken", reflect.TypeOf((*MockJusanClient)(nil).MakeToken), varargs...)
}

// OneClickPayIn mocks base method.
func (m *MockJusanClient) OneClickPayIn(ctx context.Context, in *grpc.OneClickPayInRequestData, opts ...grpc0.CallOption) (*grpc.PayInResponseData, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "OneClickPayIn", varargs...)
	ret0, _ := ret[0].(*grpc.PayInResponseData)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// OneClickPayIn indicates an expected call of OneClickPayIn.
func (mr *MockJusanClientMockRecorder) OneClickPayIn(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "OneClickPayIn", reflect.TypeOf((*MockJusanClient)(nil).OneClickPayIn), varargs...)
}

// PayIn mocks base method.
func (m *MockJusanClient) PayIn(ctx context.Context, in *grpc.PayInRequestData, opts ...grpc0.CallOption) (*grpc.PayInResponseData, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "PayIn", varargs...)
	ret0, _ := ret[0].(*grpc.PayInResponseData)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// PayIn indicates an expected call of PayIn.
func (mr *MockJusanClientMockRecorder) PayIn(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "PayIn", reflect.TypeOf((*MockJusanClient)(nil).PayIn), varargs...)
}

// PayOut mocks base method.
func (m *MockJusanClient) PayOut(ctx context.Context, in *grpc.PayOutRequestData, opts ...grpc0.CallOption) (*grpc.PayOutResponseData, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "PayOut", varargs...)
	ret0, _ := ret[0].(*grpc.PayOutResponseData)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// PayOut indicates an expected call of PayOut.
func (mr *MockJusanClientMockRecorder) PayOut(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "PayOut", reflect.TypeOf((*MockJusanClient)(nil).PayOut), varargs...)
}

// PayOutByPhone mocks base method.
func (m *MockJusanClient) PayOutByPhone(ctx context.Context, in *grpc.PayOutByPhoneRequestData, opts ...grpc0.CallOption) (*grpc.PayOutResponseByPhoneData, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "PayOutByPhone", varargs...)
	ret0, _ := ret[0].(*grpc.PayOutResponseByPhoneData)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// PayOutByPhone indicates an expected call of PayOutByPhone.
func (mr *MockJusanClientMockRecorder) PayOutByPhone(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "PayOutByPhone", reflect.TypeOf((*MockJusanClient)(nil).PayOutByPhone), varargs...)
}

// Refund mocks base method.
func (m *MockJusanClient) Refund(ctx context.Context, in *grpc.RefundRequest, opts ...grpc0.CallOption) (*grpc.RefundResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "Refund", varargs...)
	ret0, _ := ret[0].(*grpc.RefundResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Refund indicates an expected call of Refund.
func (mr *MockJusanClientMockRecorder) Refund(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Refund", reflect.TypeOf((*MockJusanClient)(nil).Refund), varargs...)
}

// ResolveVisaAlias mocks base method.
func (m *MockJusanClient) ResolveVisaAlias(ctx context.Context, in *grpc.ResolveVisaAliasRequest, opts ...grpc0.CallOption) (*grpc.ResolveVisaAliasResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "ResolveVisaAlias", varargs...)
	ret0, _ := ret[0].(*grpc.ResolveVisaAliasResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ResolveVisaAlias indicates an expected call of ResolveVisaAlias.
func (mr *MockJusanClientMockRecorder) ResolveVisaAlias(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ResolveVisaAlias", reflect.TypeOf((*MockJusanClient)(nil).ResolveVisaAlias), varargs...)
}

// ThreeDSConfirm mocks base method.
func (m *MockJusanClient) ThreeDSConfirm(ctx context.Context, in *grpc.ThreeDSRequestData, opts ...grpc0.CallOption) (*grpc.ThreeDSResponseData, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "ThreeDSConfirm", varargs...)
	ret0, _ := ret[0].(*grpc.ThreeDSResponseData)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ThreeDSConfirm indicates an expected call of ThreeDSConfirm.
func (mr *MockJusanClientMockRecorder) ThreeDSConfirm(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ThreeDSConfirm", reflect.TypeOf((*MockJusanClient)(nil).ThreeDSConfirm), varargs...)
}

// ThreeDSResume mocks base method.
func (m *MockJusanClient) ThreeDSResume(ctx context.Context, in *grpc.ThreeDSResumeRequest, opts ...grpc0.CallOption) (*grpc.ThreeDSResumeResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "ThreeDSResume", varargs...)
	ret0, _ := ret[0].(*grpc.ThreeDSResumeResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ThreeDSResume indicates an expected call of ThreeDSResume.
func (mr *MockJusanClientMockRecorder) ThreeDSResume(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ThreeDSResume", reflect.TypeOf((*MockJusanClient)(nil).ThreeDSResume), varargs...)
}

// TwoStagePayIn mocks base method.
func (m *MockJusanClient) TwoStagePayIn(ctx context.Context, in *grpc.TwoStagePayInRequest, opts ...grpc0.CallOption) (*grpc.TwoStagePayInResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "TwoStagePayIn", varargs...)
	ret0, _ := ret[0].(*grpc.TwoStagePayInResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// TwoStagePayIn indicates an expected call of TwoStagePayIn.
func (mr *MockJusanClientMockRecorder) TwoStagePayIn(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "TwoStagePayIn", reflect.TypeOf((*MockJusanClient)(nil).TwoStagePayIn), varargs...)
}

// MockJusanServer is a mock of JusanServer interface.
type MockJusanServer struct {
	ctrl     *gomock.Controller
	recorder *MockJusanServerMockRecorder
}

// MockJusanServerMockRecorder is the mock recorder for MockJusanServer.
type MockJusanServerMockRecorder struct {
	mock *MockJusanServer
}

// NewMockJusanServer creates a new mock instance.
func NewMockJusanServer(ctrl *gomock.Controller) *MockJusanServer {
	mock := &MockJusanServer{ctrl: ctrl}
	mock.recorder = &MockJusanServerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockJusanServer) EXPECT() *MockJusanServerMockRecorder {
	return m.recorder
}

// ApplePay mocks base method.
func (m *MockJusanServer) ApplePay(arg0 context.Context, arg1 *grpc.ApplePayRequestData) (*grpc.ApplePayResponseData, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ApplePay", arg0, arg1)
	ret0, _ := ret[0].(*grpc.ApplePayResponseData)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ApplePay indicates an expected call of ApplePay.
func (mr *MockJusanServerMockRecorder) ApplePay(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ApplePay", reflect.TypeOf((*MockJusanServer)(nil).ApplePay), arg0, arg1)
}

// Cancel mocks base method.
func (m *MockJusanServer) Cancel(arg0 context.Context, arg1 *grpc.CancelRequest) (*grpc.CancelResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Cancel", arg0, arg1)
	ret0, _ := ret[0].(*grpc.CancelResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Cancel indicates an expected call of Cancel.
func (mr *MockJusanServerMockRecorder) Cancel(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Cancel", reflect.TypeOf((*MockJusanServer)(nil).Cancel), arg0, arg1)
}

// Charge mocks base method.
func (m *MockJusanServer) Charge(arg0 context.Context, arg1 *grpc.ChargeRequest) (*grpc.ChargeResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Charge", arg0, arg1)
	ret0, _ := ret[0].(*grpc.ChargeResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Charge indicates an expected call of Charge.
func (mr *MockJusanServerMockRecorder) Charge(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Charge", reflect.TypeOf((*MockJusanServer)(nil).Charge), arg0, arg1)
}

// CheckBalance mocks base method.
func (m *MockJusanServer) CheckBalance(arg0 context.Context, arg1 *grpc.CheckBalanceRequest) (*grpc.CheckBalanceResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CheckBalance", arg0, arg1)
	ret0, _ := ret[0].(*grpc.CheckBalanceResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CheckBalance indicates an expected call of CheckBalance.
func (mr *MockJusanServerMockRecorder) CheckBalance(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CheckBalance", reflect.TypeOf((*MockJusanServer)(nil).CheckBalance), arg0, arg1)
}

// ConfirmEmission mocks base method.
func (m *MockJusanServer) ConfirmEmission(arg0 context.Context, arg1 *grpc.EmoneyRequest) (*grpc.EmoneyResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ConfirmEmission", arg0, arg1)
	ret0, _ := ret[0].(*grpc.EmoneyResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ConfirmEmission indicates an expected call of ConfirmEmission.
func (mr *MockJusanServerMockRecorder) ConfirmEmission(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ConfirmEmission", reflect.TypeOf((*MockJusanServer)(nil).ConfirmEmission), arg0, arg1)
}

// GetAcquirerIdentifier mocks base method.
func (m *MockJusanServer) GetAcquirerIdentifier(arg0 context.Context, arg1 *emptypb.Empty) (*grpc.GetAcquirerIdentifierResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAcquirerIdentifier", arg0, arg1)
	ret0, _ := ret[0].(*grpc.GetAcquirerIdentifierResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAcquirerIdentifier indicates an expected call of GetAcquirerIdentifier.
func (mr *MockJusanServerMockRecorder) GetAcquirerIdentifier(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAcquirerIdentifier", reflect.TypeOf((*MockJusanServer)(nil).GetAcquirerIdentifier), arg0, arg1)
}

// GetBankTransactionStatus mocks base method.
func (m *MockJusanServer) GetBankTransactionStatus(arg0 context.Context, arg1 *grpc.BankTransactionStatusRequest) (*grpc.BankTransactionStatusResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetBankTransactionStatus", arg0, arg1)
	ret0, _ := ret[0].(*grpc.BankTransactionStatusResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetBankTransactionStatus indicates an expected call of GetBankTransactionStatus.
func (mr *MockJusanServerMockRecorder) GetBankTransactionStatus(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetBankTransactionStatus", reflect.TypeOf((*MockJusanServer)(nil).GetBankTransactionStatus), arg0, arg1)
}

// GetBankTransactionStatusUnformated mocks base method.
func (m *MockJusanServer) GetBankTransactionStatusUnformated(arg0 context.Context, arg1 *grpc.BankTransactionStatusUnformatedRequest) (*grpc.BankTransactionStatusUnformatedResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetBankTransactionStatusUnformated", arg0, arg1)
	ret0, _ := ret[0].(*grpc.BankTransactionStatusUnformatedResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetBankTransactionStatusUnformated indicates an expected call of GetBankTransactionStatusUnformated.
func (mr *MockJusanServerMockRecorder) GetBankTransactionStatusUnformated(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetBankTransactionStatusUnformated", reflect.TypeOf((*MockJusanServer)(nil).GetBankTransactionStatusUnformated), arg0, arg1)
}

// GetEmission mocks base method.
func (m *MockJusanServer) GetEmission(arg0 context.Context, arg1 *emptypb.Empty) (*grpc.EmissionResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetEmission", arg0, arg1)
	ret0, _ := ret[0].(*grpc.EmissionResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetEmission indicates an expected call of GetEmission.
func (mr *MockJusanServerMockRecorder) GetEmission(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetEmission", reflect.TypeOf((*MockJusanServer)(nil).GetEmission), arg0, arg1)
}

// GooglePay mocks base method.
func (m *MockJusanServer) GooglePay(arg0 context.Context, arg1 *grpc.GooglePayRequestData) (*grpc.GooglePayResponseData, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GooglePay", arg0, arg1)
	ret0, _ := ret[0].(*grpc.GooglePayResponseData)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GooglePay indicates an expected call of GooglePay.
func (mr *MockJusanServerMockRecorder) GooglePay(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GooglePay", reflect.TypeOf((*MockJusanServer)(nil).GooglePay), arg0, arg1)
}

// MakeToken mocks base method.
func (m *MockJusanServer) MakeToken(arg0 context.Context, arg1 *grpc.PayInRequestData) (*grpc.PayInResponseData, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "MakeToken", arg0, arg1)
	ret0, _ := ret[0].(*grpc.PayInResponseData)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// MakeToken indicates an expected call of MakeToken.
func (mr *MockJusanServerMockRecorder) MakeToken(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "MakeToken", reflect.TypeOf((*MockJusanServer)(nil).MakeToken), arg0, arg1)
}

// OneClickPayIn mocks base method.
func (m *MockJusanServer) OneClickPayIn(arg0 context.Context, arg1 *grpc.OneClickPayInRequestData) (*grpc.PayInResponseData, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "OneClickPayIn", arg0, arg1)
	ret0, _ := ret[0].(*grpc.PayInResponseData)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// OneClickPayIn indicates an expected call of OneClickPayIn.
func (mr *MockJusanServerMockRecorder) OneClickPayIn(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "OneClickPayIn", reflect.TypeOf((*MockJusanServer)(nil).OneClickPayIn), arg0, arg1)
}

// PayIn mocks base method.
func (m *MockJusanServer) PayIn(arg0 context.Context, arg1 *grpc.PayInRequestData) (*grpc.PayInResponseData, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "PayIn", arg0, arg1)
	ret0, _ := ret[0].(*grpc.PayInResponseData)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// PayIn indicates an expected call of PayIn.
func (mr *MockJusanServerMockRecorder) PayIn(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "PayIn", reflect.TypeOf((*MockJusanServer)(nil).PayIn), arg0, arg1)
}

// PayOut mocks base method.
func (m *MockJusanServer) PayOut(arg0 context.Context, arg1 *grpc.PayOutRequestData) (*grpc.PayOutResponseData, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "PayOut", arg0, arg1)
	ret0, _ := ret[0].(*grpc.PayOutResponseData)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// PayOut indicates an expected call of PayOut.
func (mr *MockJusanServerMockRecorder) PayOut(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "PayOut", reflect.TypeOf((*MockJusanServer)(nil).PayOut), arg0, arg1)
}

// PayOutByPhone mocks base method.
func (m *MockJusanServer) PayOutByPhone(arg0 context.Context, arg1 *grpc.PayOutByPhoneRequestData) (*grpc.PayOutResponseByPhoneData, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "PayOutByPhone", arg0, arg1)
	ret0, _ := ret[0].(*grpc.PayOutResponseByPhoneData)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// PayOutByPhone indicates an expected call of PayOutByPhone.
func (mr *MockJusanServerMockRecorder) PayOutByPhone(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "PayOutByPhone", reflect.TypeOf((*MockJusanServer)(nil).PayOutByPhone), arg0, arg1)
}

// Refund mocks base method.
func (m *MockJusanServer) Refund(arg0 context.Context, arg1 *grpc.RefundRequest) (*grpc.RefundResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Refund", arg0, arg1)
	ret0, _ := ret[0].(*grpc.RefundResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Refund indicates an expected call of Refund.
func (mr *MockJusanServerMockRecorder) Refund(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Refund", reflect.TypeOf((*MockJusanServer)(nil).Refund), arg0, arg1)
}

// ResolveVisaAlias mocks base method.
func (m *MockJusanServer) ResolveVisaAlias(arg0 context.Context, arg1 *grpc.ResolveVisaAliasRequest) (*grpc.ResolveVisaAliasResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ResolveVisaAlias", arg0, arg1)
	ret0, _ := ret[0].(*grpc.ResolveVisaAliasResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ResolveVisaAlias indicates an expected call of ResolveVisaAlias.
func (mr *MockJusanServerMockRecorder) ResolveVisaAlias(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ResolveVisaAlias", reflect.TypeOf((*MockJusanServer)(nil).ResolveVisaAlias), arg0, arg1)
}

// ThreeDSConfirm mocks base method.
func (m *MockJusanServer) ThreeDSConfirm(arg0 context.Context, arg1 *grpc.ThreeDSRequestData) (*grpc.ThreeDSResponseData, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ThreeDSConfirm", arg0, arg1)
	ret0, _ := ret[0].(*grpc.ThreeDSResponseData)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ThreeDSConfirm indicates an expected call of ThreeDSConfirm.
func (mr *MockJusanServerMockRecorder) ThreeDSConfirm(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ThreeDSConfirm", reflect.TypeOf((*MockJusanServer)(nil).ThreeDSConfirm), arg0, arg1)
}

// ThreeDSResume mocks base method.
func (m *MockJusanServer) ThreeDSResume(arg0 context.Context, arg1 *grpc.ThreeDSResumeRequest) (*grpc.ThreeDSResumeResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ThreeDSResume", arg0, arg1)
	ret0, _ := ret[0].(*grpc.ThreeDSResumeResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ThreeDSResume indicates an expected call of ThreeDSResume.
func (mr *MockJusanServerMockRecorder) ThreeDSResume(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ThreeDSResume", reflect.TypeOf((*MockJusanServer)(nil).ThreeDSResume), arg0, arg1)
}

// TwoStagePayIn mocks base method.
func (m *MockJusanServer) TwoStagePayIn(arg0 context.Context, arg1 *grpc.TwoStagePayInRequest) (*grpc.TwoStagePayInResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "TwoStagePayIn", arg0, arg1)
	ret0, _ := ret[0].(*grpc.TwoStagePayInResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// TwoStagePayIn indicates an expected call of TwoStagePayIn.
func (mr *MockJusanServerMockRecorder) TwoStagePayIn(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "TwoStagePayIn", reflect.TypeOf((*MockJusanServer)(nil).TwoStagePayIn), arg0, arg1)
}

// mustEmbedUnimplementedJusanServer mocks base method.
func (m *MockJusanServer) mustEmbedUnimplementedJusanServer() {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "mustEmbedUnimplementedJusanServer")
}

// mustEmbedUnimplementedJusanServer indicates an expected call of mustEmbedUnimplementedJusanServer.
func (mr *MockJusanServerMockRecorder) mustEmbedUnimplementedJusanServer() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "mustEmbedUnimplementedJusanServer", reflect.TypeOf((*MockJusanServer)(nil).mustEmbedUnimplementedJusanServer))
}

// MockUnsafeJusanServer is a mock of UnsafeJusanServer interface.
type MockUnsafeJusanServer struct {
	ctrl     *gomock.Controller
	recorder *MockUnsafeJusanServerMockRecorder
}

// MockUnsafeJusanServerMockRecorder is the mock recorder for MockUnsafeJusanServer.
type MockUnsafeJusanServerMockRecorder struct {
	mock *MockUnsafeJusanServer
}

// NewMockUnsafeJusanServer creates a new mock instance.
func NewMockUnsafeJusanServer(ctrl *gomock.Controller) *MockUnsafeJusanServer {
	mock := &MockUnsafeJusanServer{ctrl: ctrl}
	mock.recorder = &MockUnsafeJusanServerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockUnsafeJusanServer) EXPECT() *MockUnsafeJusanServerMockRecorder {
	return m.recorder
}

// mustEmbedUnimplementedJusanServer mocks base method.
func (m *MockUnsafeJusanServer) mustEmbedUnimplementedJusanServer() {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "mustEmbedUnimplementedJusanServer")
}

// mustEmbedUnimplementedJusanServer indicates an expected call of mustEmbedUnimplementedJusanServer.
func (mr *MockUnsafeJusanServerMockRecorder) mustEmbedUnimplementedJusanServer() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "mustEmbedUnimplementedJusanServer", reflect.TypeOf((*MockUnsafeJusanServer)(nil).mustEmbedUnimplementedJusanServer))
}
