// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.5
// 	protoc        v5.29.3
// source: inner/processing/grpc/terminal_cards.proto

package grpc

import (
	_ "git.local/sensitive/mvp/pb"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type GetCardInfoByPanRequestV1 struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	EightDigitBin *string                `protobuf:"bytes,1,opt,name=eightDigitBin" json:"eightDigitBin,omitempty"`
	SixDigitBin   *string                `protobuf:"bytes,2,opt,name=sixDigitBin" json:"sixDigitBin,omitempty"`
	FiveDigitBin  *string                `protobuf:"bytes,3,opt,name=fiveDigitBin" json:"fiveDigitBin,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetCardInfoByPanRequestV1) Reset() {
	*x = GetCardInfoByPanRequestV1{}
	mi := &file_inner_processing_grpc_terminal_cards_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetCardInfoByPanRequestV1) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetCardInfoByPanRequestV1) ProtoMessage() {}

func (x *GetCardInfoByPanRequestV1) ProtoReflect() protoreflect.Message {
	mi := &file_inner_processing_grpc_terminal_cards_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetCardInfoByPanRequestV1.ProtoReflect.Descriptor instead.
func (*GetCardInfoByPanRequestV1) Descriptor() ([]byte, []int) {
	return file_inner_processing_grpc_terminal_cards_proto_rawDescGZIP(), []int{0}
}

func (x *GetCardInfoByPanRequestV1) GetEightDigitBin() string {
	if x != nil && x.EightDigitBin != nil {
		return *x.EightDigitBin
	}
	return ""
}

func (x *GetCardInfoByPanRequestV1) GetSixDigitBin() string {
	if x != nil && x.SixDigitBin != nil {
		return *x.SixDigitBin
	}
	return ""
}

func (x *GetCardInfoByPanRequestV1) GetFiveDigitBin() string {
	if x != nil && x.FiveDigitBin != nil {
		return *x.FiveDigitBin
	}
	return ""
}

type GetCardInfoByPanResponseV1 struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	IpsId         *uint64                `protobuf:"varint,1,opt,name=ips_id,json=ipsId" json:"ips_id,omitempty"`
	CountryId     *uint64                `protobuf:"varint,2,opt,name=country_id,json=countryId" json:"country_id,omitempty"`
	IssuerId      *uint64                `protobuf:"varint,3,opt,name=issuer_id,json=issuerId" json:"issuer_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetCardInfoByPanResponseV1) Reset() {
	*x = GetCardInfoByPanResponseV1{}
	mi := &file_inner_processing_grpc_terminal_cards_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetCardInfoByPanResponseV1) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetCardInfoByPanResponseV1) ProtoMessage() {}

func (x *GetCardInfoByPanResponseV1) ProtoReflect() protoreflect.Message {
	mi := &file_inner_processing_grpc_terminal_cards_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetCardInfoByPanResponseV1.ProtoReflect.Descriptor instead.
func (*GetCardInfoByPanResponseV1) Descriptor() ([]byte, []int) {
	return file_inner_processing_grpc_terminal_cards_proto_rawDescGZIP(), []int{1}
}

func (x *GetCardInfoByPanResponseV1) GetIpsId() uint64 {
	if x != nil && x.IpsId != nil {
		return *x.IpsId
	}
	return 0
}

func (x *GetCardInfoByPanResponseV1) GetCountryId() uint64 {
	if x != nil && x.CountryId != nil {
		return *x.CountryId
	}
	return 0
}

func (x *GetCardInfoByPanResponseV1) GetIssuerId() uint64 {
	if x != nil && x.IssuerId != nil {
		return *x.IssuerId
	}
	return 0
}

var File_inner_processing_grpc_terminal_cards_proto protoreflect.FileDescriptor

var file_inner_processing_grpc_terminal_cards_proto_rawDesc = string([]byte{
	0x0a, 0x2a, 0x69, 0x6e, 0x6e, 0x65, 0x72, 0x2f, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69,
	0x6e, 0x67, 0x2f, 0x67, 0x72, 0x70, 0x63, 0x2f, 0x74, 0x65, 0x72, 0x6d, 0x69, 0x6e, 0x61, 0x6c,
	0x5f, 0x63, 0x61, 0x72, 0x64, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x22, 0x70, 0x72,
	0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x2e, 0x61, 0x63, 0x71, 0x75, 0x69, 0x72, 0x65,
	0x72, 0x2e, 0x74, 0x65, 0x72, 0x6d, 0x69, 0x6e, 0x61, 0x6c, 0x5f, 0x63, 0x61, 0x72, 0x64, 0x73,
	0x1a, 0x16, 0x6d, 0x76, 0x70, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f, 0x6c, 0x6f, 0x67, 0x67,
	0x65, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0xa2, 0x01, 0x0a, 0x19, 0x47, 0x65, 0x74,
	0x43, 0x61, 0x72, 0x64, 0x49, 0x6e, 0x66, 0x6f, 0x42, 0x79, 0x50, 0x61, 0x6e, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x56, 0x31, 0x12, 0x2d, 0x0a, 0x0d, 0x65, 0x69, 0x67, 0x68, 0x74, 0x44,
	0x69, 0x67, 0x69, 0x74, 0x42, 0x69, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0x9a,
	0xfc, 0xeb, 0x8e, 0x02, 0x01, 0x64, 0x52, 0x0d, 0x65, 0x69, 0x67, 0x68, 0x74, 0x44, 0x69, 0x67,
	0x69, 0x74, 0x42, 0x69, 0x6e, 0x12, 0x29, 0x0a, 0x0b, 0x73, 0x69, 0x78, 0x44, 0x69, 0x67, 0x69,
	0x74, 0x42, 0x69, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0x9a, 0xfc, 0xeb, 0x8e,
	0x02, 0x01, 0x64, 0x52, 0x0b, 0x73, 0x69, 0x78, 0x44, 0x69, 0x67, 0x69, 0x74, 0x42, 0x69, 0x6e,
	0x12, 0x2b, 0x0a, 0x0c, 0x66, 0x69, 0x76, 0x65, 0x44, 0x69, 0x67, 0x69, 0x74, 0x42, 0x69, 0x6e,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0x9a, 0xfc, 0xeb, 0x8e, 0x02, 0x01, 0x64, 0x52,
	0x0c, 0x66, 0x69, 0x76, 0x65, 0x44, 0x69, 0x67, 0x69, 0x74, 0x42, 0x69, 0x6e, 0x22, 0x6f, 0x0a,
	0x1a, 0x47, 0x65, 0x74, 0x43, 0x61, 0x72, 0x64, 0x49, 0x6e, 0x66, 0x6f, 0x42, 0x79, 0x50, 0x61,
	0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x56, 0x31, 0x12, 0x15, 0x0a, 0x06, 0x69,
	0x70, 0x73, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x05, 0x69, 0x70, 0x73,
	0x49, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x72, 0x79, 0x5f, 0x69, 0x64,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x04, 0x52, 0x09, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x72, 0x79, 0x49,
	0x64, 0x12, 0x1b, 0x0a, 0x09, 0x69, 0x73, 0x73, 0x75, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x04, 0x52, 0x08, 0x69, 0x73, 0x73, 0x75, 0x65, 0x72, 0x49, 0x64, 0x32, 0xa5,
	0x01, 0x0a, 0x0d, 0x54, 0x65, 0x72, 0x6d, 0x69, 0x6e, 0x61, 0x6c, 0x43, 0x61, 0x72, 0x64, 0x73,
	0x12, 0x93, 0x01, 0x0a, 0x10, 0x47, 0x65, 0x74, 0x43, 0x61, 0x72, 0x64, 0x49, 0x6e, 0x66, 0x6f,
	0x42, 0x79, 0x50, 0x61, 0x6e, 0x12, 0x3d, 0x2e, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69,
	0x6e, 0x67, 0x2e, 0x61, 0x63, 0x71, 0x75, 0x69, 0x72, 0x65, 0x72, 0x2e, 0x74, 0x65, 0x72, 0x6d,
	0x69, 0x6e, 0x61, 0x6c, 0x5f, 0x63, 0x61, 0x72, 0x64, 0x73, 0x2e, 0x47, 0x65, 0x74, 0x43, 0x61,
	0x72, 0x64, 0x49, 0x6e, 0x66, 0x6f, 0x42, 0x79, 0x50, 0x61, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x56, 0x31, 0x1a, 0x3e, 0x2e, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e,
	0x67, 0x2e, 0x61, 0x63, 0x71, 0x75, 0x69, 0x72, 0x65, 0x72, 0x2e, 0x74, 0x65, 0x72, 0x6d, 0x69,
	0x6e, 0x61, 0x6c, 0x5f, 0x63, 0x61, 0x72, 0x64, 0x73, 0x2e, 0x47, 0x65, 0x74, 0x43, 0x61, 0x72,
	0x64, 0x49, 0x6e, 0x66, 0x6f, 0x42, 0x79, 0x50, 0x61, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x56, 0x31, 0x22, 0x00, 0x42, 0x2d, 0x5a, 0x2b, 0x67, 0x69, 0x74, 0x2e, 0x6c, 0x6f,
	0x63, 0x61, 0x6c, 0x2f, 0x73, 0x65, 0x6e, 0x73, 0x69, 0x74, 0x69, 0x76, 0x65, 0x2f, 0x69, 0x6e,
	0x6e, 0x65, 0x72, 0x70, 0x62, 0x2f, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67,
	0x2f, 0x67, 0x72, 0x70, 0x63, 0x62, 0x08, 0x65, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x70,
	0xe8, 0x07,
})

var (
	file_inner_processing_grpc_terminal_cards_proto_rawDescOnce sync.Once
	file_inner_processing_grpc_terminal_cards_proto_rawDescData []byte
)

func file_inner_processing_grpc_terminal_cards_proto_rawDescGZIP() []byte {
	file_inner_processing_grpc_terminal_cards_proto_rawDescOnce.Do(func() {
		file_inner_processing_grpc_terminal_cards_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_inner_processing_grpc_terminal_cards_proto_rawDesc), len(file_inner_processing_grpc_terminal_cards_proto_rawDesc)))
	})
	return file_inner_processing_grpc_terminal_cards_proto_rawDescData
}

var file_inner_processing_grpc_terminal_cards_proto_msgTypes = make([]protoimpl.MessageInfo, 2)
var file_inner_processing_grpc_terminal_cards_proto_goTypes = []any{
	(*GetCardInfoByPanRequestV1)(nil),  // 0: processing.acquirer.terminal_cards.GetCardInfoByPanRequestV1
	(*GetCardInfoByPanResponseV1)(nil), // 1: processing.acquirer.terminal_cards.GetCardInfoByPanResponseV1
}
var file_inner_processing_grpc_terminal_cards_proto_depIdxs = []int32{
	0, // 0: processing.acquirer.terminal_cards.TerminalCards.GetCardInfoByPan:input_type -> processing.acquirer.terminal_cards.GetCardInfoByPanRequestV1
	1, // 1: processing.acquirer.terminal_cards.TerminalCards.GetCardInfoByPan:output_type -> processing.acquirer.terminal_cards.GetCardInfoByPanResponseV1
	1, // [1:2] is the sub-list for method output_type
	0, // [0:1] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_inner_processing_grpc_terminal_cards_proto_init() }
func file_inner_processing_grpc_terminal_cards_proto_init() {
	if File_inner_processing_grpc_terminal_cards_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_inner_processing_grpc_terminal_cards_proto_rawDesc), len(file_inner_processing_grpc_terminal_cards_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   2,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_inner_processing_grpc_terminal_cards_proto_goTypes,
		DependencyIndexes: file_inner_processing_grpc_terminal_cards_proto_depIdxs,
		MessageInfos:      file_inner_processing_grpc_terminal_cards_proto_msgTypes,
	}.Build()
	File_inner_processing_grpc_terminal_cards_proto = out.File
	file_inner_processing_grpc_terminal_cards_proto_goTypes = nil
	file_inner_processing_grpc_terminal_cards_proto_depIdxs = nil
}
