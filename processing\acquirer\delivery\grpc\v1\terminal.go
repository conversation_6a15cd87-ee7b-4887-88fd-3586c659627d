package v1

import (
	"context"

	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"

	gorpc "git.local/sensitive/innerpb/processing/grpc"
	"git.local/sensitive/processing/acquirer/schema"
	"git.local/sensitive/processing/acquirer/service"
	"git.local/sensitive/sdk/dog"
)

type TerminalServer struct {
	services *service.Services
	gorpc.UnimplementedTerminalServer
}

func NewTerminalServer(services *service.Services) *TerminalServer {
	return &TerminalServer{
		services: services,
	}
}

func (ts *TerminalServer) GetByTerminalID(
	ctx context.Context,
	v1 *gorpc.TerminalRequestV1,
) (_ *gorpc.TerminalResponseV1, err error) {
	ctx, span := dog.CreateSpan(ctx, "TerminalServer_GetByTerminalID")
	defer span.End()

	terminal, err := ts.services.Terminal.GetAllInfoByID(ctx, v1.GetTerminalId())
	if err != nil {
		return nil, status.Errorf(codes.Internal, err.Error())
	}

	bank, err := ts.services.Bank.GetByID(ctx, terminal.Acquirer.BankID)
	if err != nil {
		return nil, status.Errorf(codes.Internal, err.Error())
	}

	config, err := terminal.ToStruct()
	if err != nil {
		return nil, err
	}

	var description string
	if terminal.Acquirer.Description != nil {
		description = *terminal.Acquirer.Description
	}

	return &gorpc.TerminalResponseV1{
		TerminalId: &terminal.ID,
		Config:     config,
		Acquirer: &gorpc.AcquirerData{
			AcquirerId:   &terminal.Acquirer.ID,
			AcquirerCode: &terminal.Acquirer.Code,
			BankId:       &terminal.Acquirer.BankID,
			BankName:     &bank.Name,
			Name:         &terminal.Acquirer.Name,
			//Contract:     terminal.Acquirer.Contract,
			Description: &description,
			CountryId:   &terminal.Acquirer.CountryID,
		},
		TwoStageTimeout: &terminal.TwoStageTimeout,
		AccountNumber:   &terminal.AccountNumber,
		IsTransit:       &terminal.IsTransit,
	}, nil
}

func (ts *TerminalServer) SearchTerminal(
	ctx context.Context,
	terminalReqData *gorpc.SearchTerminalReqDataV1,
) (_ *gorpc.SearchTerminalResDataV1, err error) {
	ctx, span := dog.CreateSpan(ctx, "TerminalServer_SearchTerminal")
	defer span.End()

	ruleSearchRequest := schema.NewRuleSearchRequestFromRaw(terminalReqData)

	terminal, err := ts.services.Balancer.Search(ctx, ruleSearchRequest)
	if err != nil {
		return nil, status.Errorf(codes.Internal, err.Error())
	}

	config, err := terminal.ToStruct()
	if err != nil {
		return nil, err
	}

	return &gorpc.SearchTerminalResDataV1{
		TerminalId: &terminal.ID,
		Config:     config,
		Acquirer: &gorpc.AcquirerDataV1{
			AcquirerId: &terminal.AcquirerID,
			Code:       &terminal.Acquirer.Code,
		},
	}, nil
}

func (ts *TerminalServer) GetRuleByActiveTerminals(
	ctx context.Context,
	v1 *gorpc.RuleByActiveTerminalsReqV1,
) (_ *gorpc.RuleByActiveTerminalsResponseV1, err error) {
	ctx, span := dog.CreateSpan(ctx, "TerminalServer_GetRuleByActiveTerminals")
	defer span.End()

	var (
		isRuleFound = false
	)

	terminals, err := ts.services.BasicTerminaler.GetTerminalsByIDs(ctx, v1.GetTerminalIds())
	if err != nil {
		return nil, err
	}

	ruleByActiveTerminalsReq := schema.NewRuleByActiveTerminalsRequestFromRaw(v1, terminals)

	rule, err := ts.services.Balancer.GetRuleByActiveTerminals(ctx, ruleByActiveTerminalsReq)
	if err != nil {
		return nil, status.Errorf(codes.Internal, err.Error())
	}
	// check if returned rule is an empty struct/rule
	if rule.ID != 0 {
		isRuleFound = true
	}

	ruleWeight := uint64(rule.Weight)

	var ruleResponse = &gorpc.RuleByActiveTerminals{
		RuleId:            &rule.ID,
		ProjectId:         &rule.ProjectID,
		TransactionTypeId: &rule.TransactionTypeID,
		Weight:            &ruleWeight,
		IsActive:          &rule.IsActive,
		IsBase:            &rule.IsBase,
	}

	if rule.IpsID != nil {
		ruleResponse.IpsId = rule.IpsID
	}

	if rule.IssuerID != nil {
		ruleResponse.IssuerId = rule.IssuerID
	}

	if rule.CountryID != nil {
		ruleResponse.CountryId = rule.CountryID
	}

	if rule.AmountFrom != nil {
		ruleResponse.AmountFrom = rule.AmountFrom
	}

	if rule.AmountTo != nil {
		ruleResponse.AmountTo = rule.AmountTo
	}

	return &gorpc.RuleByActiveTerminalsResponseV1{
		Rule:        ruleResponse,
		IsRuleFound: &isRuleFound,
	}, nil
}

func (ts *TerminalServer) ExtendedSearchTerminal(
	ctx context.Context,
	terminalExtendedReqData *gorpc.ExtendedSearchTerminalReqDataV1,
) (_ *gorpc.SearchTerminalResDataV1, err error) {
	ctx, span := dog.CreateSpan(ctx, "TerminalServer_ExtendedSearchTerminal")
	defer span.End()

	extendedRuleSearchRequest := schema.NewExtendedRuleSearchRequestFromRaw(terminalExtendedReqData)

	terminal, err := ts.services.Balancer.ExtendedSearch(ctx, extendedRuleSearchRequest)
	if err != nil {
		return nil, status.Errorf(codes.Internal, err.Error())
	}

	config, err := terminal.ToStruct()
	if err != nil {
		return nil, err
	}

	return &gorpc.SearchTerminalResDataV1{
		TerminalId: &terminal.ID,
		Config:     config,
		Acquirer: &gorpc.AcquirerDataV1{
			AcquirerId: &terminal.AcquirerID,
			Code:       &terminal.Acquirer.Code,
		},
	}, nil
}

func (ts *TerminalServer) FindActiveTerminalsByProject(
	ctx context.Context,
	v1 *gorpc.ActiveTerminalsByProjectRequestV1,
) (_ *gorpc.ActiveTerminalsByProjectResponseV1, err error) {
	ctx, span := dog.CreateSpan(ctx, "TerminalServer_FindActiveTerminalsByProject")
	defer span.End()

	terminal, err := ts.services.TerminalActive.FindActiveTerminalsByProject(ctx, v1.GetProjectId(), v1.GetTransactionTypeId())
	if err != nil {
		return nil, err
	}

	data := make([]*gorpc.ActiveTerminalsByProjectV1, 0)

	for _, t := range terminal {
		config, err := t.ToStruct()
		if err != nil {
			return nil, err
		}

		terminalStatus := int32(t.Status)

		data = append(data, &gorpc.ActiveTerminalsByProjectV1{
			Id:         &t.ID,
			AcquirerId: &t.AcquirerID,
			Config:     config,
			Status:     &terminalStatus,
		})
	}

	return &gorpc.ActiveTerminalsByProjectResponseV1{
		Data: data,
	}, nil
}

func (ts *TerminalServer) GetTerminalsByProjectId(ctx context.Context,
	v1 *gorpc.GetTerminalsByProjectIdRequestV1,
) (resp *gorpc.ActiveTerminalsByProjectResponseV1, err error) {
	ctx, span := dog.CreateSpan(ctx, "TerminalServer_GetTerminalsByProjectId")
	defer span.End()

	terminals, err := ts.services.Terminal.GetAll(ctx, v1.GetProjectId())

	if err != nil {
		return nil, status.Errorf(codes.Internal, err.Error())
	}

	return schema.NewModelToProtoResponse(terminals)
}

// GetTerminalWithJusan Метод для мультипривязки карты.
// Метод возвращает два терминала:
// 1. Один рандомный терминал
// 2. Терминал Жусан банка.
func (ts *TerminalServer) GetTerminalWithJusan(
	ctx context.Context,
	v1 *gorpc.SearchTerminalReqDataV1,
) (_ *gorpc.GetTerminalWithJusanResponseV1, err error) {
	ctx, span := dog.CreateSpan(ctx, "TerminalServer_GetTerminalsForMultiCardLink")
	defer span.End()

	ruleSearchRequest := schema.NewRuleSearchRequestFromRaw(v1)

	terminal, err := ts.services.Balancer.Search(ctx, ruleSearchRequest, schema.AcquirerCodeJusan)
	if err != nil {
		return nil, err
	}

	config, err := terminal.ToStruct()
	if err != nil {
		return nil, err
	}

	jusanAcquirer, err := ts.services.Acquirer.GetByCode(ctx, schema.AcquirerCodeJusan)
	if err != nil {
		return nil, err
	}

	jusanTerminal, err := ts.services.BasicTerminaler.GetByParams(
		ctx, v1.GetProjectId(), jusanAcquirer.ID, v1.GetTransactionTypeId(),
	)
	if err != nil {
		return nil, err
	}

	jusanTerminal.Acquirer = *jusanAcquirer

	jusanConfig, err := jusanTerminal.ToStruct()
	if err != nil {
		return nil, err
	}

	return &gorpc.GetTerminalWithJusanResponseV1{
		Terminal: &gorpc.SearchTerminalResDataV1{
			TerminalId: &terminal.ID,
			Config:     config,
			Acquirer: &gorpc.AcquirerDataV1{
				AcquirerId: &terminal.Acquirer.ID,
				Code:       &terminal.Acquirer.Code,
			},
		},
		JusanTerminalId: &jusanTerminal.ID,
		JusanConfig:     jusanConfig,
		JusanAcquirer: &gorpc.AcquirerDataV1{
			AcquirerId: &jusanAcquirer.ID,
			Code:       &jusanTerminal.Acquirer.Code,
		},
	}, nil
}

func (ts *TerminalServer) GetPayInProjectTerminals(
	ctx context.Context,
	req *gorpc.GetPayInProjectTerminalsReqV1,
) (_ *gorpc.GetPayInProjectTerminalsResponseV1, err error) {
	ctx, span := dog.CreateSpan(ctx, "TerminalServer_GetPayInProjectTerminals")
	defer span.End()

	if req.GetProjectId() == 0 {
		return nil, status.Error(codes.InvalidArgument, "ProjectId is required")
	}

	res, err := ts.services.TerminalProject.GetPayInProjectTerminals(ctx, req.GetProjectId())
	if err != nil {
		return nil, status.Error(codes.Internal, err.Error())
	}

	resp := make([]uint64, 0)
	for _, terminalProject := range res {
		resp = append(resp, terminalProject.TerminalID)
	}

	return &gorpc.GetPayInProjectTerminalsResponseV1{
		TerminalIds: resp,
	}, nil
}
