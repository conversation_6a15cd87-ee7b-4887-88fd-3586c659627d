package service

import (
	"context"
	"errors"
	"reflect"
	"testing"

	"github.com/golang/mock/gomock"
	"github.com/stretchr/testify/require"

	"git.local/sensitive/innerpb/processing/goerr"
	"git.local/sensitive/innerpb/processing/grpc"
	gtransactionMocks "git.local/sensitive/pkg/gtransaction/mocks"
	"git.local/sensitive/pkg/middlewares"
	"git.local/sensitive/processing/acquirer/model"
	"git.local/sensitive/processing/acquirer/repository/database/databasemocks"
	"git.local/sensitive/processing/acquirer/schema"
	"git.local/sensitive/sdk/dog/e2e"
)

func TestCreateRule(t *testing.T) {
	ctx := context.Background()

	type getBaseRuleByProjectOp struct {
		inputProjectID         uint64
		inputTransactionTypeID uint64
		output                 *model.Rule
		outputErr              error
	}

	type findByAllParamOp struct {
		isCalled      bool
		input         *model.Rule
		inputIsActive *bool
		output        []*model.Rule
		outputErr     error
	}

	type bankGetByIDOp struct {
		isCalled  bool
		input     uint64
		output    *model.Bank
		outputErr error
	}

	type createRuleOp struct {
		isCalled  bool
		input     *model.Rule
		output    *model.Rule
		outputErr error
	}

	type createRulePercentageOp struct {
		isCalled  bool
		input     []*model.RulePercentage
		outputErr error
	}

	type gtransactionBeginOp struct {
		isCalled  bool
		outputCtx context.Context
	}

	type gtransactionRollbackOp struct {
		isCalled  bool
		outputErr error
	}

	type gtransactionCommitOp struct {
		isCalled  bool
		outputErr error
	}

	var (
		ipsID       uint64  = 23
		issuerID    uint64  = 33
		CountryID   uint64  = 25
		AmountFrom  float64 = 100
		AmountTo    float64 = 200
		isActiveVal         = true
	)

	tests := []struct {
		name                         string
		req                          schema.CreateRuleRequest
		want                         *model.Rule
		wantErr                      error
		getBaseRuleByProject         getBaseRuleByProjectOp
		findByAllParam               findByAllParamOp
		bankGetByID                  bankGetByIDOp
		gtransactionBegin            gtransactionBeginOp
		createRule                   createRuleOp
		createRuleRollback           gtransactionRollbackOp
		createRulePercentage         createRulePercentageOp
		createRulePercentageRollback gtransactionRollbackOp
		gtransactionCommit           gtransactionCommitOp
	}{
		{
			name: "error when getting base rule",
			req: schema.CreateRuleRequest{
				ProjectID:         10,
				TransactionTypeID: uint64(grpc.EnumTransactionType_TransactionTypePayIn),
				IpsID:             &ipsID,
				IssuerID:          &issuerID,
				AmountFrom:        &AmountFrom,
				AmountTo:          &AmountTo,
				CountryID:         &CountryID,
			},
			want:    nil,
			wantErr: errors.New("some error"),
			getBaseRuleByProject: getBaseRuleByProjectOp{
				inputProjectID:         10,
				inputTransactionTypeID: uint64(grpc.EnumTransactionType_TransactionTypePayIn),
				output:                 nil,
				outputErr:              errors.New("some error"),
			},
		},
		{
			name: "error when finding by all params",
			req: schema.CreateRuleRequest{
				ProjectID:         10,
				TransactionTypeID: uint64(grpc.EnumTransactionType_TransactionTypePayIn),
				IpsID:             &ipsID,
				IssuerID:          &issuerID,
				AmountFrom:        &AmountFrom,
				AmountTo:          &AmountTo,
				CountryID:         &CountryID,
			},
			want:    nil,
			wantErr: errors.New("some error"),
			getBaseRuleByProject: getBaseRuleByProjectOp{
				inputProjectID:         10,
				inputTransactionTypeID: uint64(grpc.EnumTransactionType_TransactionTypePayIn),
				output:                 nil,
				outputErr:              nil,
			},
			findByAllParam: findByAllParamOp{
				isCalled: true,
				input: &model.Rule{
					ProjectID:         10,
					TransactionTypeID: uint64(grpc.EnumTransactionType_TransactionTypePayIn),
					IpsID:             &ipsID,
					IssuerID:          &issuerID,
					AmountFrom:        &AmountFrom,
					AmountTo:          &AmountTo,
					CountryID:         &CountryID,
					IsActive:          false,
				},
				inputIsActive: &isActiveVal,
				output:        nil,
				outputErr:     errors.New("some error"),
			},
		},
		{
			name: "rule already exists error",
			req: schema.CreateRuleRequest{
				ProjectID:         10,
				TransactionTypeID: uint64(grpc.EnumTransactionType_TransactionTypePayIn),
				IpsID:             &ipsID,
				IssuerID:          &issuerID,
				AmountFrom:        &AmountFrom,
				AmountTo:          &AmountTo,
				CountryID:         &CountryID,
			},
			want:    nil,
			wantErr: goerr.ErrRuleAlreadyExists,
			getBaseRuleByProject: getBaseRuleByProjectOp{
				inputProjectID:         10,
				inputTransactionTypeID: uint64(grpc.EnumTransactionType_TransactionTypePayIn),
				output:                 nil,
				outputErr:              nil,
			},
			findByAllParam: findByAllParamOp{
				isCalled: true,
				input: &model.Rule{
					ProjectID:         10,
					TransactionTypeID: uint64(grpc.EnumTransactionType_TransactionTypePayIn),
					IpsID:             &ipsID,
					IssuerID:          &issuerID,
					AmountFrom:        &AmountFrom,
					AmountTo:          &AmountTo,
					CountryID:         &CountryID,
					IsActive:          false,
				},
				inputIsActive: &isActiveVal,
				output: []*model.Rule{
					{
						ProjectID:         10,
						TransactionTypeID: uint64(grpc.EnumTransactionType_TransactionTypePayIn),
						IpsID:             &ipsID,
						IssuerID:          &issuerID,
						AmountFrom:        &AmountFrom,
						AmountTo:          &AmountTo,
						CountryID:         &CountryID,
						IsActive:          true,
						IsBase:            true,
					},
				},
				outputErr: nil,
			},
		},
		{
			name: "get bank by id error",
			req: schema.CreateRuleRequest{
				ProjectID:         10,
				TransactionTypeID: uint64(grpc.EnumTransactionType_TransactionTypePayIn),
				IpsID:             &ipsID,
				IssuerID:          &issuerID,
				AmountFrom:        &AmountFrom,
				AmountTo:          &AmountTo,
				CountryID:         &CountryID,
			},
			want:    nil,
			wantErr: errors.New("some error"),
			getBaseRuleByProject: getBaseRuleByProjectOp{
				inputProjectID:         10,
				inputTransactionTypeID: uint64(grpc.EnumTransactionType_TransactionTypePayIn),
				output:                 nil,
				outputErr:              nil,
			},
			findByAllParam: findByAllParamOp{
				isCalled: true,
				input: &model.Rule{
					ProjectID:         10,
					TransactionTypeID: uint64(grpc.EnumTransactionType_TransactionTypePayIn),
					IpsID:             &ipsID,
					IssuerID:          &issuerID,
					AmountFrom:        &AmountFrom,
					AmountTo:          &AmountTo,
					CountryID:         &CountryID,
					IsActive:          false,
				},
				inputIsActive: &isActiveVal,
				output:        nil,
				outputErr:     goerr.ErrRuleNotFound,
			},
			bankGetByID: bankGetByIDOp{
				isCalled:  true,
				input:     issuerID,
				output:    nil,
				outputErr: errors.New("some error"),
			},
		},
		{
			name: "create rule error + error in rollback",
			req: schema.CreateRuleRequest{
				ProjectID:         10,
				TransactionTypeID: uint64(grpc.EnumTransactionType_TransactionTypePayIn),
				IpsID:             &ipsID,
				IssuerID:          &issuerID,
				AmountFrom:        &AmountFrom,
				AmountTo:          &AmountTo,
				CountryID:         &CountryID,
			},
			want:    nil,
			wantErr: errors.New("some another error"),
			getBaseRuleByProject: getBaseRuleByProjectOp{
				inputProjectID:         10,
				inputTransactionTypeID: uint64(grpc.EnumTransactionType_TransactionTypePayIn),
				output:                 nil,
				outputErr:              nil,
			},
			findByAllParam: findByAllParamOp{
				isCalled: true,
				input: &model.Rule{
					ProjectID:         10,
					TransactionTypeID: uint64(grpc.EnumTransactionType_TransactionTypePayIn),
					IpsID:             &ipsID,
					IssuerID:          &issuerID,
					AmountFrom:        &AmountFrom,
					AmountTo:          &AmountTo,
					CountryID:         &CountryID,
					IsActive:          false,
				},
				inputIsActive: &isActiveVal,
				output:        nil,
				outputErr:     goerr.ErrRuleNotFound,
			},
			bankGetByID: bankGetByIDOp{
				isCalled:  true,
				input:     issuerID,
				output:    nil,
				outputErr: nil,
			},
			gtransactionBegin: gtransactionBeginOp{
				isCalled:  true,
				outputCtx: ctx,
			},
			createRule: createRuleOp{
				isCalled: true,
				input: &model.Rule{
					ProjectID:         10,
					TransactionTypeID: uint64(grpc.EnumTransactionType_TransactionTypePayIn),
					IpsID:             &ipsID,
					IssuerID:          &issuerID,
					AmountFrom:        &AmountFrom,
					AmountTo:          &AmountTo,
					CountryID:         &CountryID,
					IsActive:          false,
				},
				output:    nil,
				outputErr: errors.New("some orchestra error"),
			},
			createRuleRollback: gtransactionRollbackOp{
				isCalled:  true,
				outputErr: errors.New("some another error"),
			},
		},
		{
			name: "create rule error",
			req: schema.CreateRuleRequest{
				ProjectID:         10,
				TransactionTypeID: uint64(grpc.EnumTransactionType_TransactionTypePayIn),
				IpsID:             &ipsID,
				IssuerID:          &issuerID,
				AmountFrom:        &AmountFrom,
				AmountTo:          &AmountTo,
				CountryID:         &CountryID,
			},
			want:    nil,
			wantErr: errors.New("some another error"),
			getBaseRuleByProject: getBaseRuleByProjectOp{
				inputProjectID:         10,
				inputTransactionTypeID: uint64(grpc.EnumTransactionType_TransactionTypePayIn),
				output:                 nil,
				outputErr:              nil,
			},
			findByAllParam: findByAllParamOp{
				isCalled: true,
				input: &model.Rule{
					ProjectID:         10,
					TransactionTypeID: uint64(grpc.EnumTransactionType_TransactionTypePayIn),
					IpsID:             &ipsID,
					IssuerID:          &issuerID,
					AmountFrom:        &AmountFrom,
					AmountTo:          &AmountTo,
					CountryID:         &CountryID,
					IsActive:          false,
				},
				inputIsActive: &isActiveVal,
				output:        nil,
				outputErr:     goerr.ErrRuleNotFound,
			},
			bankGetByID: bankGetByIDOp{
				isCalled:  true,
				input:     issuerID,
				output:    nil,
				outputErr: nil,
			},
			gtransactionBegin: gtransactionBeginOp{
				isCalled:  true,
				outputCtx: ctx,
			},
			createRule: createRuleOp{
				isCalled: true,
				input: &model.Rule{
					ProjectID:         10,
					TransactionTypeID: uint64(grpc.EnumTransactionType_TransactionTypePayIn),
					IpsID:             &ipsID,
					IssuerID:          &issuerID,
					AmountFrom:        &AmountFrom,
					AmountTo:          &AmountTo,
					CountryID:         &CountryID,
					IsActive:          false,
				},
				output:    nil,
				outputErr: errors.New("some another error"),
			},
			createRuleRollback: gtransactionRollbackOp{
				isCalled:  true,
				outputErr: nil,
			},
		},
		{
			name: "create rule percentage error + rollback error",
			req: schema.CreateRuleRequest{
				ProjectID:         10,
				TransactionTypeID: uint64(grpc.EnumTransactionType_TransactionTypePayIn),
				IpsID:             &ipsID,
				IssuerID:          &issuerID,
				AmountFrom:        &AmountFrom,
				AmountTo:          &AmountTo,
				CountryID:         &CountryID,
				Percentages: schema.CreateRulePercentageRequest{
					{
						AcquirerID: 1,
						Percentage: 10,
					},
					{
						AcquirerID: 2,
						Percentage: 20,
					},
					{
						AcquirerID: 3,
						Percentage: 70,
					},
				},
			},
			want:    nil,
			wantErr: errors.New("some another error"),
			getBaseRuleByProject: getBaseRuleByProjectOp{
				inputProjectID:         10,
				inputTransactionTypeID: uint64(grpc.EnumTransactionType_TransactionTypePayIn),
				output:                 nil,
				outputErr:              nil,
			},
			findByAllParam: findByAllParamOp{
				isCalled: true,
				input: &model.Rule{
					ProjectID:         10,
					TransactionTypeID: uint64(grpc.EnumTransactionType_TransactionTypePayIn),
					IpsID:             &ipsID,
					IssuerID:          &issuerID,
					AmountFrom:        &AmountFrom,
					AmountTo:          &AmountTo,
					CountryID:         &CountryID,
					IsActive:          false,
				},
				inputIsActive: &isActiveVal,
				output:        nil,
				outputErr:     goerr.ErrRuleNotFound,
			},
			bankGetByID: bankGetByIDOp{
				isCalled:  true,
				input:     issuerID,
				output:    nil,
				outputErr: nil,
			},
			gtransactionBegin: gtransactionBeginOp{
				isCalled:  true,
				outputCtx: ctx,
			},
			createRule: createRuleOp{
				isCalled: true,
				input: &model.Rule{
					ProjectID:         10,
					TransactionTypeID: uint64(grpc.EnumTransactionType_TransactionTypePayIn),
					IpsID:             &ipsID,
					IssuerID:          &issuerID,
					AmountFrom:        &AmountFrom,
					AmountTo:          &AmountTo,
					CountryID:         &CountryID,
					IsActive:          false,
				},
				output: &model.Rule{
					ID:                90,
					ProjectID:         10,
					TransactionTypeID: uint64(grpc.EnumTransactionType_TransactionTypePayIn),
					IpsID:             &ipsID,
					IssuerID:          &issuerID,
					AmountFrom:        &AmountFrom,
					AmountTo:          &AmountTo,
					CountryID:         &CountryID,
					IsActive:          false,
				},
				outputErr: nil,
			},
			createRuleRollback: gtransactionRollbackOp{
				isCalled:  false,
				outputErr: nil,
			},
			createRulePercentage: createRulePercentageOp{
				isCalled: true,
				input: []*model.RulePercentage{
					{
						RuleID:     90,
						Percentage: 10,
						AcquirerID: 1,
					},
					{
						RuleID:     90,
						Percentage: 20,
						AcquirerID: 2,
					},
					{
						RuleID:     90,
						Percentage: 70,
						AcquirerID: 3,
					},
				},
				outputErr: errors.New("some zagadochniy error"),
			},
			createRulePercentageRollback: gtransactionRollbackOp{
				isCalled:  true,
				outputErr: errors.New("some another error"),
			},
		},
		{
			name: "create rule percentage error",
			req: schema.CreateRuleRequest{
				ProjectID:         10,
				TransactionTypeID: uint64(grpc.EnumTransactionType_TransactionTypePayIn),
				IpsID:             &ipsID,
				IssuerID:          &issuerID,
				AmountFrom:        &AmountFrom,
				AmountTo:          &AmountTo,
				CountryID:         &CountryID,
				Percentages: schema.CreateRulePercentageRequest{
					{
						AcquirerID: 1,
						Percentage: 10,
					},
					{
						AcquirerID: 2,
						Percentage: 20,
					},
					{
						AcquirerID: 3,
						Percentage: 70,
					},
				},
			},
			want:    nil,
			wantErr: errors.New("some zagadochniy error"),
			getBaseRuleByProject: getBaseRuleByProjectOp{
				inputProjectID:         10,
				inputTransactionTypeID: uint64(grpc.EnumTransactionType_TransactionTypePayIn),
				output:                 nil,
				outputErr:              nil,
			},
			findByAllParam: findByAllParamOp{
				isCalled: true,
				input: &model.Rule{
					ProjectID:         10,
					TransactionTypeID: uint64(grpc.EnumTransactionType_TransactionTypePayIn),
					IpsID:             &ipsID,
					IssuerID:          &issuerID,
					AmountFrom:        &AmountFrom,
					AmountTo:          &AmountTo,
					CountryID:         &CountryID,
					IsActive:          false,
				},
				inputIsActive: &isActiveVal,
				output:        nil,
				outputErr:     goerr.ErrRuleNotFound,
			},
			bankGetByID: bankGetByIDOp{
				isCalled:  true,
				input:     issuerID,
				output:    nil,
				outputErr: nil,
			},
			gtransactionBegin: gtransactionBeginOp{
				isCalled:  true,
				outputCtx: ctx,
			},
			createRule: createRuleOp{
				isCalled: true,
				input: &model.Rule{
					ProjectID:         10,
					TransactionTypeID: uint64(grpc.EnumTransactionType_TransactionTypePayIn),
					IpsID:             &ipsID,
					IssuerID:          &issuerID,
					AmountFrom:        &AmountFrom,
					AmountTo:          &AmountTo,
					CountryID:         &CountryID,
					IsActive:          false,
				},
				output: &model.Rule{
					ID:                90,
					ProjectID:         10,
					TransactionTypeID: uint64(grpc.EnumTransactionType_TransactionTypePayIn),
					IpsID:             &ipsID,
					IssuerID:          &issuerID,
					AmountFrom:        &AmountFrom,
					AmountTo:          &AmountTo,
					CountryID:         &CountryID,
					IsActive:          false,
				},
				outputErr: nil,
			},
			createRuleRollback: gtransactionRollbackOp{
				isCalled:  false,
				outputErr: nil,
			},
			createRulePercentage: createRulePercentageOp{
				isCalled: true,
				input: []*model.RulePercentage{
					{
						RuleID:     90,
						Percentage: 10,
						AcquirerID: 1,
					},
					{
						RuleID:     90,
						Percentage: 20,
						AcquirerID: 2,
					},
					{
						RuleID:     90,
						Percentage: 70,
						AcquirerID: 3,
					},
				},
				outputErr: errors.New("some zagadochniy error"),
			},
			createRulePercentageRollback: gtransactionRollbackOp{
				isCalled:  true,
				outputErr: nil,
			},
		},
		{
			name: "commit error",
			req: schema.CreateRuleRequest{
				ProjectID:         10,
				TransactionTypeID: uint64(grpc.EnumTransactionType_TransactionTypePayIn),
				IpsID:             &ipsID,
				IssuerID:          &issuerID,
				AmountFrom:        &AmountFrom,
				AmountTo:          &AmountTo,
				CountryID:         &CountryID,
				Percentages: schema.CreateRulePercentageRequest{
					{
						AcquirerID: 1,
						Percentage: 10,
					},
					{
						AcquirerID: 2,
						Percentage: 20,
					},
					{
						AcquirerID: 3,
						Percentage: 70,
					},
				},
			},
			want:    nil,
			wantErr: errors.New("some zagadochniyyyyyy error"),
			getBaseRuleByProject: getBaseRuleByProjectOp{
				inputProjectID:         10,
				inputTransactionTypeID: uint64(grpc.EnumTransactionType_TransactionTypePayIn),
				output:                 nil,
				outputErr:              nil,
			},
			findByAllParam: findByAllParamOp{
				isCalled: true,
				input: &model.Rule{
					ProjectID:         10,
					TransactionTypeID: uint64(grpc.EnumTransactionType_TransactionTypePayIn),
					IpsID:             &ipsID,
					IssuerID:          &issuerID,
					AmountFrom:        &AmountFrom,
					AmountTo:          &AmountTo,
					CountryID:         &CountryID,
					IsActive:          false,
				},
				inputIsActive: &isActiveVal,
				output:        nil,
				outputErr:     goerr.ErrRuleNotFound,
			},
			bankGetByID: bankGetByIDOp{
				isCalled:  true,
				input:     issuerID,
				output:    nil,
				outputErr: nil,
			},
			gtransactionBegin: gtransactionBeginOp{
				isCalled:  true,
				outputCtx: ctx,
			},
			createRule: createRuleOp{
				isCalled: true,
				input: &model.Rule{
					ProjectID:         10,
					TransactionTypeID: uint64(grpc.EnumTransactionType_TransactionTypePayIn),
					IpsID:             &ipsID,
					IssuerID:          &issuerID,
					AmountFrom:        &AmountFrom,
					AmountTo:          &AmountTo,
					CountryID:         &CountryID,
					IsActive:          false,
				},
				output: &model.Rule{
					ID:                90,
					ProjectID:         10,
					TransactionTypeID: uint64(grpc.EnumTransactionType_TransactionTypePayIn),
					IpsID:             &ipsID,
					IssuerID:          &issuerID,
					AmountFrom:        &AmountFrom,
					AmountTo:          &AmountTo,
					CountryID:         &CountryID,
					IsActive:          false,
				},
				outputErr: nil,
			},
			createRuleRollback: gtransactionRollbackOp{
				isCalled:  false,
				outputErr: nil,
			},
			createRulePercentage: createRulePercentageOp{
				isCalled: true,
				input: []*model.RulePercentage{
					{
						RuleID:     90,
						Percentage: 10,
						AcquirerID: 1,
					},
					{
						RuleID:     90,
						Percentage: 20,
						AcquirerID: 2,
					},
					{
						RuleID:     90,
						Percentage: 70,
						AcquirerID: 3,
					},
				},
				outputErr: nil,
			},
			createRulePercentageRollback: gtransactionRollbackOp{
				isCalled:  false,
				outputErr: nil,
			},
			gtransactionCommit: gtransactionCommitOp{
				isCalled:  true,
				outputErr: errors.New("some zagadochniyyyyyy error"),
			},
		},
		{
			name: "success",
			req: schema.CreateRuleRequest{
				ProjectID:         10,
				TransactionTypeID: uint64(grpc.EnumTransactionType_TransactionTypePayIn),
				IpsID:             &ipsID,
				IssuerID:          &issuerID,
				AmountFrom:        &AmountFrom,
				AmountTo:          &AmountTo,
				CountryID:         &CountryID,
				Percentages: schema.CreateRulePercentageRequest{
					{
						AcquirerID: 1,
						Percentage: 10,
					},
					{
						AcquirerID: 2,
						Percentage: 20,
					},
					{
						AcquirerID: 3,
						Percentage: 70,
					},
				},
			},
			want: &model.Rule{
				ID:                90,
				ProjectID:         10,
				TransactionTypeID: uint64(grpc.EnumTransactionType_TransactionTypePayIn),
				IpsID:             &ipsID,
				IssuerID:          &issuerID,
				AmountFrom:        &AmountFrom,
				AmountTo:          &AmountTo,
				CountryID:         &CountryID,
				IsActive:          false,
			},
			wantErr: nil,
			getBaseRuleByProject: getBaseRuleByProjectOp{
				inputProjectID:         10,
				inputTransactionTypeID: uint64(grpc.EnumTransactionType_TransactionTypePayIn),
				output:                 nil,
				outputErr:              nil,
			},
			findByAllParam: findByAllParamOp{
				isCalled: true,
				input: &model.Rule{
					ProjectID:         10,
					TransactionTypeID: uint64(grpc.EnumTransactionType_TransactionTypePayIn),
					IpsID:             &ipsID,
					IssuerID:          &issuerID,
					AmountFrom:        &AmountFrom,
					AmountTo:          &AmountTo,
					CountryID:         &CountryID,
					IsActive:          false,
				},
				inputIsActive: &isActiveVal,
				output:        nil,
				outputErr:     goerr.ErrRuleNotFound,
			},
			bankGetByID: bankGetByIDOp{
				isCalled:  true,
				input:     issuerID,
				output:    nil,
				outputErr: nil,
			},
			gtransactionBegin: gtransactionBeginOp{
				isCalled:  true,
				outputCtx: ctx,
			},
			createRule: createRuleOp{
				isCalled: true,
				input: &model.Rule{
					ProjectID:         10,
					TransactionTypeID: uint64(grpc.EnumTransactionType_TransactionTypePayIn),
					IpsID:             &ipsID,
					IssuerID:          &issuerID,
					AmountFrom:        &AmountFrom,
					AmountTo:          &AmountTo,
					CountryID:         &CountryID,
					IsActive:          false,
				},
				output: &model.Rule{
					ID:                90,
					ProjectID:         10,
					TransactionTypeID: uint64(grpc.EnumTransactionType_TransactionTypePayIn),
					IpsID:             &ipsID,
					IssuerID:          &issuerID,
					AmountFrom:        &AmountFrom,
					AmountTo:          &AmountTo,
					CountryID:         &CountryID,
					IsActive:          false,
				},
				outputErr: nil,
			},
			createRuleRollback: gtransactionRollbackOp{
				isCalled:  false,
				outputErr: nil,
			},
			createRulePercentage: createRulePercentageOp{
				isCalled: true,
				input: []*model.RulePercentage{
					{
						RuleID:     90,
						Percentage: 10,
						AcquirerID: 1,
					},
					{
						RuleID:     90,
						Percentage: 20,
						AcquirerID: 2,
					},
					{
						RuleID:     90,
						Percentage: 70,
						AcquirerID: 3,
					},
				},
				outputErr: nil,
			},
			createRulePercentageRollback: gtransactionRollbackOp{
				isCalled:  false,
				outputErr: nil,
			},
			gtransactionCommit: gtransactionCommitOp{
				isCalled:  true,
				outputErr: nil,
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctrl := gomock.NewController(t)
			defer ctrl.Finish()

			c := context.Background()
			ctx, cancel := context.WithCancel(c)
			e2e.InitAppE2E(t, ctx, cancel, nil, nil, nil)

			gtransactionMock := gtransactionMocks.NewMockManager(ctrl)
			ruleDBMock := databasemocks.NewMockRuler(ctrl)
			rulePercentageDBMock := databasemocks.NewMockRulePercentager(ctrl)
			bankDBMock := databasemocks.NewMockBanker(ctrl)

			ruleDBMock.EXPECT().GetBaseRuleByProject(
				gomock.Any(),
				tt.getBaseRuleByProject.inputProjectID,
				tt.getBaseRuleByProject.inputTransactionTypeID,
			).Return(tt.getBaseRuleByProject.output, tt.getBaseRuleByProject.outputErr)

			if tt.findByAllParam.isCalled {
				ruleDBMock.EXPECT().FindByAllParam(
					gomock.Any(),
					tt.findByAllParam.input,
					tt.findByAllParam.inputIsActive,
				).Return(tt.findByAllParam.output, tt.findByAllParam.outputErr).Times(1)
			}

			if tt.bankGetByID.isCalled {
				bankDBMock.EXPECT().GetById(
					gomock.Any(),
					tt.bankGetByID.input,
				).Return(
					tt.bankGetByID.output,
					tt.bankGetByID.outputErr,
				).Times(1)
			}

			if tt.gtransactionBegin.isCalled {
				gtransactionMock.EXPECT().Begin(
					gomock.Any(),
				).Return(tt.gtransactionBegin.outputCtx).Times(1)
			}

			if tt.createRule.isCalled {
				ruleDBMock.EXPECT().Create(
					gomock.Any(),
					tt.createRule.input,
				).Return(tt.createRule.output, tt.createRule.outputErr).Times(1)
			}

			if tt.createRuleRollback.isCalled {
				gtransactionMock.EXPECT().Rollback(
					gomock.Any(),
				).Return(
					tt.createRuleRollback.outputErr,
				).Times(1)
			}

			if tt.createRulePercentage.isCalled {
				rulePercentageDBMock.EXPECT().Create(
					gomock.Any(),
					tt.createRulePercentage.input,
				).Return(tt.createRulePercentage.outputErr).Times(1)
			}

			if tt.createRulePercentageRollback.isCalled {
				gtransactionMock.EXPECT().Rollback(
					gomock.Any(),
				).Return(tt.createRulePercentageRollback.outputErr).Times(1)
			}

			if tt.gtransactionCommit.isCalled {
				gtransactionMock.EXPECT().Commit(
					gomock.Any(),
				).Return(tt.gtransactionCommit.outputErr).Times(1)
			}

			s := NewRuleService(ruleDBMock, bankDBMock, rulePercentageDBMock, gtransactionMock)

			resp, err := s.Create(context.Background(), tt.req)
			if tt.wantErr != nil {
				require.Error(t, err)
				require.Equal(t, tt.wantErr, err)
			} else {
				require.NoError(t, err)
			}

			require.Equal(t, tt.want, resp)
		})
	}
}

func TestGetList(t *testing.T) {
	type getListOp struct {
		inputPagination *middlewares.PaginationInfo
		input           schema.RuleListRequest
		output          []*model.Rule
		outputErr       error
	}

	var (
		projectID         uint64 = 81
		transactionTypeID uint8  = 1
		isBase                   = false
	)

	tests := []struct {
		name          string
		reqPagination *middlewares.PaginationInfo
		req           schema.RuleListRequest
		getList       getListOp
		want          []*model.Rule
		wantErr       error
	}{
		{
			name: "error when getting list",
			reqPagination: &middlewares.PaginationInfo{
				PerPage:    2,
				Page:       1,
				Pagination: true,
			},
			req: schema.RuleListRequest{
				ProjectID:         &projectID,
				TransactionTypeID: &transactionTypeID,
				IsBase:            &isBase,
			},
			getList: getListOp{
				inputPagination: &middlewares.PaginationInfo{
					PerPage:    2,
					Page:       1,
					Pagination: true,
				},
				input: schema.RuleListRequest{
					ProjectID:         &projectID,
					TransactionTypeID: &transactionTypeID,
					IsBase:            &isBase,
				},
				output:    nil,
				outputErr: errors.New("some error"),
			},
			want:    nil,
			wantErr: errors.New("some error"),
		},
		{
			name: "success",
			reqPagination: &middlewares.PaginationInfo{
				PerPage:    2,
				Page:       1,
				Pagination: true,
			},
			req: schema.RuleListRequest{
				ProjectID:         &projectID,
				TransactionTypeID: &transactionTypeID,
				IsBase:            &isBase,
			},
			getList: getListOp{
				inputPagination: &middlewares.PaginationInfo{
					PerPage:    2,
					Page:       1,
					Pagination: true,
				},
				input: schema.RuleListRequest{
					ProjectID:         &projectID,
					TransactionTypeID: &transactionTypeID,
					IsBase:            &isBase,
				},
				output: []*model.Rule{
					{
						ID:                90,
						ProjectID:         10,
						TransactionTypeID: uint64(grpc.EnumTransactionType_TransactionTypePayIn),
						IsActive:          false,
					},
				},
				outputErr: nil,
			},
			want: []*model.Rule{
				{
					ID:                90,
					ProjectID:         10,
					TransactionTypeID: uint64(grpc.EnumTransactionType_TransactionTypePayIn),
					IsActive:          false,
				},
			},
			wantErr: nil,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctrl := gomock.NewController(t)
			defer ctrl.Finish()

			ruleDBMock := databasemocks.NewMockRuler(ctrl)

			s := NewRuleService(ruleDBMock, nil, nil, nil)

			ruleDBMock.EXPECT().GetListWithBalancer(
				gomock.Any(),
				tt.getList.inputPagination,
				tt.getList.input,
			).Return(tt.getList.output, tt.getList.outputErr).Times(1)

			resp, err := s.GetList(context.Background(), tt.reqPagination, tt.req)
			if tt.wantErr != nil {
				require.Error(t, err)
				require.Equal(t, tt.wantErr, err)
			} else {
				require.NoError(t, err)
			}

			require.Equal(t, tt.want, resp)
		})
	}
}

func TestCreateBaseRule(t *testing.T) {
	type getBaseRuleByProjectIDOp struct {
		inputProjectID         uint64
		inputTransactionTypeID uint64
		output                 *model.Rule
		outputErr              error
	}

	type createRuleOp struct {
		isCalled  bool
		input     *model.Rule
		output    *model.Rule
		outputErr error
	}

	type createRulePercentageOp struct {
		isCalled  bool
		input     []*model.RulePercentage
		outputErr error
	}

	type gtransactionCommitOp struct {
		isCalled  bool
		outputErr error
	}

	type gtransactionBeginOp struct {
		isCalled bool
	}

	type gtransactionRollbackOp struct {
		isCalled  bool
		outputErr error
	}

	tests := []struct {
		name                         string
		req                          schema.CreateBaseRuleRequest
		want                         *model.Rule
		wantErr                      error
		getBaseRuleByProjectID       getBaseRuleByProjectIDOp
		gtransacationBegin           gtransactionBeginOp
		createRule                   createRuleOp
		createRuleRollback           gtransactionRollbackOp
		createRulePercentage         createRulePercentageOp
		createRulePercentageRollback gtransactionRollbackOp
		gtransactionCommit           gtransactionCommitOp
	}{
		{
			name: "error when getting base rule by project ID",
			req: schema.CreateBaseRuleRequest{
				ProjectID:         73,
				TransactionTypeID: uint64(grpc.EnumTransactionType_TransactionTypePayIn),
			},
			want:    nil,
			wantErr: errors.New("some error"),
			getBaseRuleByProjectID: getBaseRuleByProjectIDOp{
				inputProjectID:         73,
				inputTransactionTypeID: uint64(grpc.EnumTransactionType_TransactionTypePayIn),
				output:                 nil,
				outputErr:              errors.New("some error"),
			},
		},
		{
			name: "existing rule already exists",
			req: schema.CreateBaseRuleRequest{
				ProjectID:         73,
				TransactionTypeID: uint64(grpc.EnumTransactionType_TransactionTypePayIn),
			},
			want:    nil,
			wantErr: goerr.ErrBaseRuleAlreadyExists,
			getBaseRuleByProjectID: getBaseRuleByProjectIDOp{
				inputProjectID:         73,
				inputTransactionTypeID: uint64(grpc.EnumTransactionType_TransactionTypePayIn),
				output: &model.Rule{
					ID:                90,
					ProjectID:         10,
					TransactionTypeID: uint64(grpc.EnumTransactionType_TransactionTypePayIn),
					IsActive:          false,
				},
				outputErr: nil,
			},
		},
		{
			name: "error whn creating rule + rollback error",
			req: schema.CreateBaseRuleRequest{
				ProjectID:         73,
				TransactionTypeID: uint64(grpc.EnumTransactionType_TransactionTypePayIn),
			},
			want:    nil,
			wantErr: errors.New("some rollback error"),
			getBaseRuleByProjectID: getBaseRuleByProjectIDOp{
				inputProjectID:         73,
				inputTransactionTypeID: uint64(grpc.EnumTransactionType_TransactionTypePayIn),
				output:                 nil,
				outputErr:              goerr.ErrBaseRuleNotFound,
			},
			gtransacationBegin: gtransactionBeginOp{
				isCalled: true,
			},
			createRule: createRuleOp{
				isCalled: true,
				input: &model.Rule{
					ProjectID:         73,
					TransactionTypeID: uint64(grpc.EnumTransactionType_TransactionTypePayIn),
					IsActive:          true,
					IsBase:            true,
					Weight:            model.BaseRuleWeight,
				},
				output:    nil,
				outputErr: errors.New("some error"),
			},
			createRuleRollback: gtransactionRollbackOp{
				isCalled:  true,
				outputErr: errors.New("some rollback error"),
			},
		},
		{
			name: "error when creating rule",
			req: schema.CreateBaseRuleRequest{
				ProjectID:         73,
				TransactionTypeID: uint64(grpc.EnumTransactionType_TransactionTypePayIn),
				Percentages: schema.CreateRulePercentageRequest{
					{
						AcquirerID: 1,
						Percentage: 10,
					},
					{
						AcquirerID: 2,
						Percentage: 20,
					},
					{
						AcquirerID: 3,
						Percentage: 70,
					},
				},
			},
			want:    nil,
			wantErr: errors.New("asd"),
			getBaseRuleByProjectID: getBaseRuleByProjectIDOp{
				inputProjectID:         73,
				inputTransactionTypeID: uint64(grpc.EnumTransactionType_TransactionTypePayIn),
				output:                 nil,
				outputErr:              goerr.ErrBaseRuleNotFound,
			},
			gtransacationBegin: gtransactionBeginOp{
				isCalled: true,
			},
			createRule: createRuleOp{
				isCalled: true,
				input: &model.Rule{
					ProjectID:         73,
					TransactionTypeID: uint64(grpc.EnumTransactionType_TransactionTypePayIn),
					IsActive:          true,
					IsBase:            true,
					Weight:            model.BaseRuleWeight,
				},
				output: &model.Rule{
					ID:                90,
					ProjectID:         73,
					TransactionTypeID: uint64(grpc.EnumTransactionType_TransactionTypePayIn),
					IsActive:          true,
					IsBase:            true,
					Weight:            model.BaseRuleWeight,
				},
				outputErr: errors.New("asd"),
			},
			createRuleRollback: gtransactionRollbackOp{
				isCalled:  true,
				outputErr: nil,
			},
		},
		{
			name: "create rule percentage error + rollback",
			req: schema.CreateBaseRuleRequest{
				ProjectID:         73,
				TransactionTypeID: uint64(grpc.EnumTransactionType_TransactionTypePayIn),
				Percentages: schema.CreateRulePercentageRequest{
					{
						AcquirerID: 1,
						Percentage: 10,
					},
					{
						AcquirerID: 2,
						Percentage: 20,
					},
					{
						AcquirerID: 3,
						Percentage: 70,
					},
				},
			},
			want:    nil,
			wantErr: errors.New("asd"),
			getBaseRuleByProjectID: getBaseRuleByProjectIDOp{
				inputProjectID:         73,
				inputTransactionTypeID: uint64(grpc.EnumTransactionType_TransactionTypePayIn),
				output:                 nil,
				outputErr:              goerr.ErrBaseRuleNotFound,
			},
			gtransacationBegin: gtransactionBeginOp{
				isCalled: true,
			},
			createRule: createRuleOp{
				isCalled: true,
				input: &model.Rule{
					ProjectID:         73,
					TransactionTypeID: uint64(grpc.EnumTransactionType_TransactionTypePayIn),
					IsActive:          true,
					IsBase:            true,
					Weight:            model.BaseRuleWeight,
				},
				output: &model.Rule{
					ID:                90,
					ProjectID:         73,
					TransactionTypeID: uint64(grpc.EnumTransactionType_TransactionTypePayIn),
					IsActive:          true,
					IsBase:            true,
					Weight:            model.BaseRuleWeight,
				},
				outputErr: nil,
			},
			createRuleRollback: gtransactionRollbackOp{
				isCalled:  false,
				outputErr: nil,
			},
			createRulePercentage: createRulePercentageOp{
				isCalled: true,
				input: []*model.RulePercentage{
					{
						RuleID:     90,
						AcquirerID: 1,
						Percentage: 10,
					},
					{
						RuleID:     90,
						AcquirerID: 2,
						Percentage: 20,
					},
					{
						AcquirerID: 3,
						Percentage: 70,
					},
				},
				outputErr: errors.New("some error"),
			},
			createRulePercentageRollback: gtransactionRollbackOp{
				isCalled:  true,
				outputErr: errors.New("asd"),
			},
		},
		{
			name: "create rule percentage error",
			req: schema.CreateBaseRuleRequest{
				ProjectID:         73,
				TransactionTypeID: uint64(grpc.EnumTransactionType_TransactionTypePayIn),
				Percentages: schema.CreateRulePercentageRequest{
					{
						AcquirerID: 1,
						Percentage: 10,
					},
					{
						AcquirerID: 2,
						Percentage: 20,
					},
					{
						AcquirerID: 3,
						Percentage: 70,
					},
				},
			},
			want:    nil,
			wantErr: errors.New("some error"),
			getBaseRuleByProjectID: getBaseRuleByProjectIDOp{
				inputProjectID:         73,
				inputTransactionTypeID: uint64(grpc.EnumTransactionType_TransactionTypePayIn),
				output:                 nil,
				outputErr:              goerr.ErrBaseRuleNotFound,
			},
			gtransacationBegin: gtransactionBeginOp{
				isCalled: true,
			},
			createRule: createRuleOp{
				isCalled: true,
				input: &model.Rule{
					ProjectID:         73,
					TransactionTypeID: uint64(grpc.EnumTransactionType_TransactionTypePayIn),
					IsActive:          true,
					IsBase:            true,
					Weight:            model.BaseRuleWeight,
				},
				output: &model.Rule{
					ID:                90,
					ProjectID:         73,
					TransactionTypeID: uint64(grpc.EnumTransactionType_TransactionTypePayIn),
					IsActive:          true,
					IsBase:            true,
					Weight:            model.BaseRuleWeight,
				},
				outputErr: nil,
			},
			createRuleRollback: gtransactionRollbackOp{
				isCalled:  false,
				outputErr: nil,
			},
			createRulePercentage: createRulePercentageOp{
				isCalled: true,
				input: []*model.RulePercentage{
					{
						RuleID:     90,
						AcquirerID: 1,
						Percentage: 10,
					},
					{
						RuleID:     90,
						AcquirerID: 2,
						Percentage: 20,
					},
					{
						AcquirerID: 3,
						Percentage: 70,
					},
				},
				outputErr: errors.New("some error"),
			},
			createRulePercentageRollback: gtransactionRollbackOp{
				isCalled:  true,
				outputErr: nil,
			},
		},
		{
			name: "commit error",
			req: schema.CreateBaseRuleRequest{
				ProjectID:         73,
				TransactionTypeID: uint64(grpc.EnumTransactionType_TransactionTypePayIn),
				Percentages: schema.CreateRulePercentageRequest{
					{
						AcquirerID: 1,
						Percentage: 10,
					},
					{
						AcquirerID: 2,
						Percentage: 20,
					},
					{
						AcquirerID: 3,
						Percentage: 70,
					},
				},
			},
			want:    nil,
			wantErr: errors.New("some new error"),
			getBaseRuleByProjectID: getBaseRuleByProjectIDOp{
				inputProjectID:         73,
				inputTransactionTypeID: uint64(grpc.EnumTransactionType_TransactionTypePayIn),
				output:                 nil,
				outputErr:              goerr.ErrBaseRuleNotFound,
			},
			gtransacationBegin: gtransactionBeginOp{
				isCalled: true,
			},
			createRule: createRuleOp{
				isCalled: true,
				input: &model.Rule{
					ProjectID:         73,
					TransactionTypeID: uint64(grpc.EnumTransactionType_TransactionTypePayIn),
					IsActive:          true,
					IsBase:            true,
					Weight:            model.BaseRuleWeight,
				},
				output: &model.Rule{
					ID:                90,
					ProjectID:         73,
					TransactionTypeID: uint64(grpc.EnumTransactionType_TransactionTypePayIn),
					IsActive:          true,
					IsBase:            true,
					Weight:            model.BaseRuleWeight,
				},
				outputErr: nil,
			},
			createRuleRollback: gtransactionRollbackOp{
				isCalled:  false,
				outputErr: nil,
			},
			createRulePercentage: createRulePercentageOp{
				isCalled: true,
				input: []*model.RulePercentage{
					{
						RuleID:     90,
						AcquirerID: 1,
						Percentage: 10,
					},
					{
						RuleID:     90,
						AcquirerID: 2,
						Percentage: 20,
					},
					{
						AcquirerID: 3,
						Percentage: 70,
					},
				},
				outputErr: nil,
			},
			createRulePercentageRollback: gtransactionRollbackOp{
				isCalled:  false,
				outputErr: nil,
			},
			gtransactionCommit: gtransactionCommitOp{
				isCalled:  true,
				outputErr: errors.New("some new error"),
			},
		},
		{
			name: "success",
			req: schema.CreateBaseRuleRequest{
				ProjectID:         73,
				TransactionTypeID: uint64(grpc.EnumTransactionType_TransactionTypePayIn),
				Percentages: schema.CreateRulePercentageRequest{
					{
						AcquirerID: 1,
						Percentage: 10,
					},
					{
						AcquirerID: 2,
						Percentage: 20,
					},
					{
						AcquirerID: 3,
						Percentage: 70,
					},
				},
			},
			want: &model.Rule{
				ID:                90,
				ProjectID:         73,
				TransactionTypeID: uint64(grpc.EnumTransactionType_TransactionTypePayIn),
				IsActive:          true,
				IsBase:            true,
				Weight:            model.BaseRuleWeight,
			},
			wantErr: nil,
			getBaseRuleByProjectID: getBaseRuleByProjectIDOp{
				inputProjectID:         73,
				inputTransactionTypeID: uint64(grpc.EnumTransactionType_TransactionTypePayIn),
				output:                 nil,
				outputErr:              goerr.ErrBaseRuleNotFound,
			},
			gtransacationBegin: gtransactionBeginOp{
				isCalled: true,
			},
			createRule: createRuleOp{
				isCalled: true,
				input: &model.Rule{
					ProjectID:         73,
					TransactionTypeID: uint64(grpc.EnumTransactionType_TransactionTypePayIn),
					IsActive:          true,
					IsBase:            true,
					Weight:            model.BaseRuleWeight,
				},
				output: &model.Rule{
					ID:                90,
					ProjectID:         73,
					TransactionTypeID: uint64(grpc.EnumTransactionType_TransactionTypePayIn),
					IsActive:          true,
					IsBase:            true,
					Weight:            model.BaseRuleWeight,
				},
				outputErr: nil,
			},
			createRuleRollback: gtransactionRollbackOp{
				isCalled:  false,
				outputErr: nil,
			},
			createRulePercentage: createRulePercentageOp{
				isCalled: true,
				input: []*model.RulePercentage{
					{
						RuleID:     90,
						AcquirerID: 1,
						Percentage: 10,
					},
					{
						RuleID:     90,
						AcquirerID: 2,
						Percentage: 20,
					},
					{
						AcquirerID: 3,
						Percentage: 70,
					},
				},
				outputErr: nil,
			},
			createRulePercentageRollback: gtransactionRollbackOp{
				isCalled:  false,
				outputErr: nil,
			},
			gtransactionCommit: gtransactionCommitOp{
				isCalled:  true,
				outputErr: nil,
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctrl := gomock.NewController(t)
			defer ctrl.Finish()

			c := context.Background()
			ctx, cancel := context.WithCancel(c)
			e2e.InitAppE2E(t, ctx, cancel, nil, nil, nil)

			gtransactionMock := gtransactionMocks.NewMockManager(ctrl)
			ruleDBMock := databasemocks.NewMockRuler(ctrl)
			rulePercentageDBMock := databasemocks.NewMockRulePercentager(ctrl)
			bankDBMock := databasemocks.NewMockBanker(ctrl)

			ruleDBMock.EXPECT().GetBaseRuleByProject(
				gomock.Any(),
				tt.getBaseRuleByProjectID.inputProjectID,
				tt.getBaseRuleByProjectID.inputTransactionTypeID,
			).Return(tt.getBaseRuleByProjectID.output, tt.getBaseRuleByProjectID.outputErr).Times(1)

			if tt.gtransacationBegin.isCalled {
				gtransactionMock.EXPECT().Begin(gomock.Any()).Return(context.Background()).Times(1)
			}

			if tt.createRule.isCalled {
				ruleDBMock.EXPECT().Create(
					gomock.Any(),
					tt.createRule.input,
				).Return(tt.createRule.output, tt.createRule.outputErr).Times(1)
			}

			if tt.createRuleRollback.isCalled {
				gtransactionMock.EXPECT().Rollback(
					gomock.Any()).Return(tt.createRuleRollback.outputErr).Times(1)
			}

			if tt.createRulePercentage.isCalled {
				rulePercentageDBMock.EXPECT().Create(
					gomock.Any(),
					gomock.Any(),
				).DoAndReturn(func(_ context.Context, balancers []*model.RulePercentage) error {
					reflect.DeepEqual(tt.createRulePercentage.input, balancers)

					return tt.createRulePercentage.outputErr
				}).Times(1)
			}

			if tt.createRulePercentageRollback.isCalled {
				gtransactionMock.EXPECT().Rollback(
					gomock.Any(),
				).Return(tt.createRulePercentageRollback.outputErr).Times(1)
			}

			if tt.gtransactionCommit.isCalled {
				gtransactionMock.EXPECT().Commit(
					gomock.Any(),
				).Return(tt.gtransactionCommit.outputErr).Times(1)
			}

			s := NewRuleService(ruleDBMock, bankDBMock, rulePercentageDBMock, gtransactionMock)

			resp, err := s.CreateBase(context.Background(), tt.req)
			if tt.wantErr != nil {
				require.Error(t, err)
				require.Equal(t, tt.wantErr, err)
			} else {
				require.NoError(t, err)
			}

			require.Equal(t, tt.want, resp)
		})
	}
}
