// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.5.1
// - protoc             v5.29.3
// source: inner/processing/grpc/smart_pay.proto

package grpc

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.64.0 or later.
const _ = grpc.SupportPackageIsVersion9

const (
	SmartPay_ApplePaySession_FullMethodName    = "/processing.smart_pay.smart_pay.SmartPay/ApplePaySession"
	SmartPay_DecodeToken_FullMethodName        = "/processing.smart_pay.smart_pay.SmartPay/DecodeToken"
	SmartPay_DecryptGPayToken_FullMethodName   = "/processing.smart_pay.smart_pay.SmartPay/DecryptGPayToken"
	SmartPay_GetGPayCredentials_FullMethodName = "/processing.smart_pay.smart_pay.SmartPay/GetGPayCredentials"
)

// SmartPayClient is the client API for SmartPay service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type SmartPayClient interface {
	ApplePaySession(ctx context.Context, in *ApplePaySessionRequestV1, opts ...grpc.CallOption) (*ApplePaySessionResponseV1, error)
	DecodeToken(ctx context.Context, in *DecodeTokenRequestV1, opts ...grpc.CallOption) (*DecodeTokenResponseV1, error)
	DecryptGPayToken(ctx context.Context, in *DecryptGPayTokenRequestV1, opts ...grpc.CallOption) (*DecryptGPayTokenResponseV1, error)
	GetGPayCredentials(ctx context.Context, in *GetGPayCredentialsRequestV1, opts ...grpc.CallOption) (*GetGPayCredentialsResponseV1, error)
}

type smartPayClient struct {
	cc grpc.ClientConnInterface
}

func NewSmartPayClient(cc grpc.ClientConnInterface) SmartPayClient {
	return &smartPayClient{cc}
}

func (c *smartPayClient) ApplePaySession(ctx context.Context, in *ApplePaySessionRequestV1, opts ...grpc.CallOption) (*ApplePaySessionResponseV1, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ApplePaySessionResponseV1)
	err := c.cc.Invoke(ctx, SmartPay_ApplePaySession_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *smartPayClient) DecodeToken(ctx context.Context, in *DecodeTokenRequestV1, opts ...grpc.CallOption) (*DecodeTokenResponseV1, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(DecodeTokenResponseV1)
	err := c.cc.Invoke(ctx, SmartPay_DecodeToken_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *smartPayClient) DecryptGPayToken(ctx context.Context, in *DecryptGPayTokenRequestV1, opts ...grpc.CallOption) (*DecryptGPayTokenResponseV1, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(DecryptGPayTokenResponseV1)
	err := c.cc.Invoke(ctx, SmartPay_DecryptGPayToken_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *smartPayClient) GetGPayCredentials(ctx context.Context, in *GetGPayCredentialsRequestV1, opts ...grpc.CallOption) (*GetGPayCredentialsResponseV1, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetGPayCredentialsResponseV1)
	err := c.cc.Invoke(ctx, SmartPay_GetGPayCredentials_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// SmartPayServer is the server API for SmartPay service.
// All implementations must embed UnimplementedSmartPayServer
// for forward compatibility.
type SmartPayServer interface {
	ApplePaySession(context.Context, *ApplePaySessionRequestV1) (*ApplePaySessionResponseV1, error)
	DecodeToken(context.Context, *DecodeTokenRequestV1) (*DecodeTokenResponseV1, error)
	DecryptGPayToken(context.Context, *DecryptGPayTokenRequestV1) (*DecryptGPayTokenResponseV1, error)
	GetGPayCredentials(context.Context, *GetGPayCredentialsRequestV1) (*GetGPayCredentialsResponseV1, error)
	mustEmbedUnimplementedSmartPayServer()
}

// UnimplementedSmartPayServer must be embedded to have
// forward compatible implementations.
//
// NOTE: this should be embedded by value instead of pointer to avoid a nil
// pointer dereference when methods are called.
type UnimplementedSmartPayServer struct{}

func (UnimplementedSmartPayServer) ApplePaySession(context.Context, *ApplePaySessionRequestV1) (*ApplePaySessionResponseV1, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ApplePaySession not implemented")
}
func (UnimplementedSmartPayServer) DecodeToken(context.Context, *DecodeTokenRequestV1) (*DecodeTokenResponseV1, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DecodeToken not implemented")
}
func (UnimplementedSmartPayServer) DecryptGPayToken(context.Context, *DecryptGPayTokenRequestV1) (*DecryptGPayTokenResponseV1, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DecryptGPayToken not implemented")
}
func (UnimplementedSmartPayServer) GetGPayCredentials(context.Context, *GetGPayCredentialsRequestV1) (*GetGPayCredentialsResponseV1, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetGPayCredentials not implemented")
}
func (UnimplementedSmartPayServer) mustEmbedUnimplementedSmartPayServer() {}
func (UnimplementedSmartPayServer) testEmbeddedByValue()                  {}

// UnsafeSmartPayServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to SmartPayServer will
// result in compilation errors.
type UnsafeSmartPayServer interface {
	mustEmbedUnimplementedSmartPayServer()
}

func RegisterSmartPayServer(s grpc.ServiceRegistrar, srv SmartPayServer) {
	// If the following call pancis, it indicates UnimplementedSmartPayServer was
	// embedded by pointer and is nil.  This will cause panics if an
	// unimplemented method is ever invoked, so we test this at initialization
	// time to prevent it from happening at runtime later due to I/O.
	if t, ok := srv.(interface{ testEmbeddedByValue() }); ok {
		t.testEmbeddedByValue()
	}
	s.RegisterService(&SmartPay_ServiceDesc, srv)
}

func _SmartPay_ApplePaySession_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ApplePaySessionRequestV1)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SmartPayServer).ApplePaySession(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: SmartPay_ApplePaySession_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SmartPayServer).ApplePaySession(ctx, req.(*ApplePaySessionRequestV1))
	}
	return interceptor(ctx, in, info, handler)
}

func _SmartPay_DecodeToken_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DecodeTokenRequestV1)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SmartPayServer).DecodeToken(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: SmartPay_DecodeToken_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SmartPayServer).DecodeToken(ctx, req.(*DecodeTokenRequestV1))
	}
	return interceptor(ctx, in, info, handler)
}

func _SmartPay_DecryptGPayToken_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DecryptGPayTokenRequestV1)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SmartPayServer).DecryptGPayToken(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: SmartPay_DecryptGPayToken_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SmartPayServer).DecryptGPayToken(ctx, req.(*DecryptGPayTokenRequestV1))
	}
	return interceptor(ctx, in, info, handler)
}

func _SmartPay_GetGPayCredentials_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetGPayCredentialsRequestV1)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SmartPayServer).GetGPayCredentials(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: SmartPay_GetGPayCredentials_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SmartPayServer).GetGPayCredentials(ctx, req.(*GetGPayCredentialsRequestV1))
	}
	return interceptor(ctx, in, info, handler)
}

// SmartPay_ServiceDesc is the grpc.ServiceDesc for SmartPay service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var SmartPay_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "processing.smart_pay.smart_pay.SmartPay",
	HandlerType: (*SmartPayServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "ApplePaySession",
			Handler:    _SmartPay_ApplePaySession_Handler,
		},
		{
			MethodName: "DecodeToken",
			Handler:    _SmartPay_DecodeToken_Handler,
		},
		{
			MethodName: "DecryptGPayToken",
			Handler:    _SmartPay_DecryptGPayToken_Handler,
		},
		{
			MethodName: "GetGPayCredentials",
			Handler:    _SmartPay_GetGPayCredentials_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "inner/processing/grpc/smart_pay.proto",
}
