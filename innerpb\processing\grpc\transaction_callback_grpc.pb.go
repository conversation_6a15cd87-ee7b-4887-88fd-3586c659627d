// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.5.1
// - protoc             v5.29.3
// source: inner/processing/grpc/transaction_callback.proto

package grpc

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.64.0 or later.
const _ = grpc.SupportPackageIsVersion9

const (
	TransactionCallback_SendCallback_FullMethodName = "/processing.transaction.transaction_callback.TransactionCallback/SendCallback"
)

// TransactionCallbackClient is the client API for TransactionCallback service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type TransactionCallbackClient interface {
	SendCallback(ctx context.Context, in *TransactionSendCallbackRequestV1, opts ...grpc.CallOption) (*emptypb.Empty, error)
}

type transactionCallbackClient struct {
	cc grpc.ClientConnInterface
}

func NewTransactionCallbackClient(cc grpc.ClientConnInterface) TransactionCallbackClient {
	return &transactionCallbackClient{cc}
}

func (c *transactionCallbackClient) SendCallback(ctx context.Context, in *TransactionSendCallbackRequestV1, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, TransactionCallback_SendCallback_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// TransactionCallbackServer is the server API for TransactionCallback service.
// All implementations must embed UnimplementedTransactionCallbackServer
// for forward compatibility.
type TransactionCallbackServer interface {
	SendCallback(context.Context, *TransactionSendCallbackRequestV1) (*emptypb.Empty, error)
	mustEmbedUnimplementedTransactionCallbackServer()
}

// UnimplementedTransactionCallbackServer must be embedded to have
// forward compatible implementations.
//
// NOTE: this should be embedded by value instead of pointer to avoid a nil
// pointer dereference when methods are called.
type UnimplementedTransactionCallbackServer struct{}

func (UnimplementedTransactionCallbackServer) SendCallback(context.Context, *TransactionSendCallbackRequestV1) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SendCallback not implemented")
}
func (UnimplementedTransactionCallbackServer) mustEmbedUnimplementedTransactionCallbackServer() {}
func (UnimplementedTransactionCallbackServer) testEmbeddedByValue()                             {}

// UnsafeTransactionCallbackServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to TransactionCallbackServer will
// result in compilation errors.
type UnsafeTransactionCallbackServer interface {
	mustEmbedUnimplementedTransactionCallbackServer()
}

func RegisterTransactionCallbackServer(s grpc.ServiceRegistrar, srv TransactionCallbackServer) {
	// If the following call pancis, it indicates UnimplementedTransactionCallbackServer was
	// embedded by pointer and is nil.  This will cause panics if an
	// unimplemented method is ever invoked, so we test this at initialization
	// time to prevent it from happening at runtime later due to I/O.
	if t, ok := srv.(interface{ testEmbeddedByValue() }); ok {
		t.testEmbeddedByValue()
	}
	s.RegisterService(&TransactionCallback_ServiceDesc, srv)
}

func _TransactionCallback_SendCallback_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(TransactionSendCallbackRequestV1)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TransactionCallbackServer).SendCallback(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: TransactionCallback_SendCallback_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TransactionCallbackServer).SendCallback(ctx, req.(*TransactionSendCallbackRequestV1))
	}
	return interceptor(ctx, in, info, handler)
}

// TransactionCallback_ServiceDesc is the grpc.ServiceDesc for TransactionCallback service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var TransactionCallback_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "processing.transaction.transaction_callback.TransactionCallback",
	HandlerType: (*TransactionCallbackServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "SendCallback",
			Handler:    _TransactionCallback_SendCallback_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "inner/processing/grpc/transaction_callback.proto",
}
