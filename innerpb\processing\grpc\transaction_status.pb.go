// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.5
// 	protoc        v5.29.3
// source: inner/processing/grpc/transaction_status.proto

package grpc

import (
	_ "git.local/sensitive/mvp/pb"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	descriptorpb "google.golang.org/protobuf/types/descriptorpb"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type EnumTransactionStatus int32

const (
	EnumTransactionStatus_Unknown                          EnumTransactionStatus = 0
	EnumTransactionStatus_TransactionStatusNew             EnumTransactionStatus = 2
	EnumTransactionStatus_TransactionStatusThreeDSWaiting  EnumTransactionStatus = 3
	EnumTransactionStatus_TransactionStatusProcessed       EnumTransactionStatus = 4
	EnumTransactionStatus_TransactionStatusFailed          EnumTransactionStatus = 5
	EnumTransactionStatus_TransactionStatusRefund          EnumTransactionStatus = 6
	EnumTransactionStatus_TransactionStatusCanceled        EnumTransactionStatus = 7
	EnumTransactionStatus_TransactionStatusRetry           EnumTransactionStatus = 8
	EnumTransactionStatus_TransactionStatusSuccess         EnumTransactionStatus = 9
	EnumTransactionStatus_TransactionStatusThreeDSReceived EnumTransactionStatus = 10
	EnumTransactionStatus_TransactionStatusHolded          EnumTransactionStatus = 11
	EnumTransactionStatus_TransactionStatusRefundWaiting   EnumTransactionStatus = 13
	EnumTransactionStatus_TransactionStatusAuthorized      EnumTransactionStatus = 14
	EnumTransactionStatus_TransactionStatusError           EnumTransactionStatus = 15
	EnumTransactionStatus_TransactionStatusFingerPrint     EnumTransactionStatus = 16
)

// Enum value maps for EnumTransactionStatus.
var (
	EnumTransactionStatus_name = map[int32]string{
		0:  "Unknown",
		2:  "TransactionStatusNew",
		3:  "TransactionStatusThreeDSWaiting",
		4:  "TransactionStatusProcessed",
		5:  "TransactionStatusFailed",
		6:  "TransactionStatusRefund",
		7:  "TransactionStatusCanceled",
		8:  "TransactionStatusRetry",
		9:  "TransactionStatusSuccess",
		10: "TransactionStatusThreeDSReceived",
		11: "TransactionStatusHolded",
		13: "TransactionStatusRefundWaiting",
		14: "TransactionStatusAuthorized",
		15: "TransactionStatusError",
		16: "TransactionStatusFingerPrint",
	}
	EnumTransactionStatus_value = map[string]int32{
		"Unknown":                          0,
		"TransactionStatusNew":             2,
		"TransactionStatusThreeDSWaiting":  3,
		"TransactionStatusProcessed":       4,
		"TransactionStatusFailed":          5,
		"TransactionStatusRefund":          6,
		"TransactionStatusCanceled":        7,
		"TransactionStatusRetry":           8,
		"TransactionStatusSuccess":         9,
		"TransactionStatusThreeDSReceived": 10,
		"TransactionStatusHolded":          11,
		"TransactionStatusRefundWaiting":   13,
		"TransactionStatusAuthorized":      14,
		"TransactionStatusError":           15,
		"TransactionStatusFingerPrint":     16,
	}
)

func (x EnumTransactionStatus) Enum() *EnumTransactionStatus {
	p := new(EnumTransactionStatus)
	*p = x
	return p
}

func (x EnumTransactionStatus) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (EnumTransactionStatus) Descriptor() protoreflect.EnumDescriptor {
	return file_inner_processing_grpc_transaction_status_proto_enumTypes[0].Descriptor()
}

func (EnumTransactionStatus) Type() protoreflect.EnumType {
	return &file_inner_processing_grpc_transaction_status_proto_enumTypes[0]
}

func (x EnumTransactionStatus) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use EnumTransactionStatus.Descriptor instead.
func (EnumTransactionStatus) EnumDescriptor() ([]byte, []int) {
	return file_inner_processing_grpc_transaction_status_proto_rawDescGZIP(), []int{0}
}

type TransactionStatusRef struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Name          *string                `protobuf:"bytes,2,opt,name=name" json:"name,omitempty"`
	Code          *string                `protobuf:"bytes,3,opt,name=code" json:"code,omitempty"`
	IsFinal       *bool                  `protobuf:"varint,4,opt,name=is_final,json=isFinal" json:"is_final,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *TransactionStatusRef) Reset() {
	*x = TransactionStatusRef{}
	mi := &file_inner_processing_grpc_transaction_status_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *TransactionStatusRef) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TransactionStatusRef) ProtoMessage() {}

func (x *TransactionStatusRef) ProtoReflect() protoreflect.Message {
	mi := &file_inner_processing_grpc_transaction_status_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TransactionStatusRef.ProtoReflect.Descriptor instead.
func (*TransactionStatusRef) Descriptor() ([]byte, []int) {
	return file_inner_processing_grpc_transaction_status_proto_rawDescGZIP(), []int{0}
}

func (x *TransactionStatusRef) GetName() string {
	if x != nil && x.Name != nil {
		return *x.Name
	}
	return ""
}

func (x *TransactionStatusRef) GetCode() string {
	if x != nil && x.Code != nil {
		return *x.Code
	}
	return ""
}

func (x *TransactionStatusRef) GetIsFinal() bool {
	if x != nil && x.IsFinal != nil {
		return *x.IsFinal
	}
	return false
}

type BatchTransactionRequestV1 struct {
	state          protoimpl.MessageState `protogen:"open.v1"`
	TransactionIds []uint64               `protobuf:"varint,1,rep,packed,name=transaction_ids,json=transactionIds" json:"transaction_ids,omitempty"`
	unknownFields  protoimpl.UnknownFields
	sizeCache      protoimpl.SizeCache
}

func (x *BatchTransactionRequestV1) Reset() {
	*x = BatchTransactionRequestV1{}
	mi := &file_inner_processing_grpc_transaction_status_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BatchTransactionRequestV1) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BatchTransactionRequestV1) ProtoMessage() {}

func (x *BatchTransactionRequestV1) ProtoReflect() protoreflect.Message {
	mi := &file_inner_processing_grpc_transaction_status_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BatchTransactionRequestV1.ProtoReflect.Descriptor instead.
func (*BatchTransactionRequestV1) Descriptor() ([]byte, []int) {
	return file_inner_processing_grpc_transaction_status_proto_rawDescGZIP(), []int{1}
}

func (x *BatchTransactionRequestV1) GetTransactionIds() []uint64 {
	if x != nil {
		return x.TransactionIds
	}
	return nil
}

type TransactionWithStatusResponseV1 struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	TransactionId *uint64                `protobuf:"varint,1,opt,name=transaction_id,json=transactionId" json:"transaction_id,omitempty"`
	StatusCode    *string                `protobuf:"bytes,2,opt,name=status_code,json=statusCode" json:"status_code,omitempty"`
	IsFinalStatus *bool                  `protobuf:"varint,3,opt,name=is_final_status,json=isFinalStatus" json:"is_final_status,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *TransactionWithStatusResponseV1) Reset() {
	*x = TransactionWithStatusResponseV1{}
	mi := &file_inner_processing_grpc_transaction_status_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *TransactionWithStatusResponseV1) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TransactionWithStatusResponseV1) ProtoMessage() {}

func (x *TransactionWithStatusResponseV1) ProtoReflect() protoreflect.Message {
	mi := &file_inner_processing_grpc_transaction_status_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TransactionWithStatusResponseV1.ProtoReflect.Descriptor instead.
func (*TransactionWithStatusResponseV1) Descriptor() ([]byte, []int) {
	return file_inner_processing_grpc_transaction_status_proto_rawDescGZIP(), []int{2}
}

func (x *TransactionWithStatusResponseV1) GetTransactionId() uint64 {
	if x != nil && x.TransactionId != nil {
		return *x.TransactionId
	}
	return 0
}

func (x *TransactionWithStatusResponseV1) GetStatusCode() string {
	if x != nil && x.StatusCode != nil {
		return *x.StatusCode
	}
	return ""
}

func (x *TransactionWithStatusResponseV1) GetIsFinalStatus() bool {
	if x != nil && x.IsFinalStatus != nil {
		return *x.IsFinalStatus
	}
	return false
}

type BatchTransactionResponseV1 struct {
	state         protoimpl.MessageState             `protogen:"open.v1"`
	BatchResponse []*TransactionWithStatusResponseV1 `protobuf:"bytes,1,rep,name=batch_response,json=batchResponse" json:"batch_response,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *BatchTransactionResponseV1) Reset() {
	*x = BatchTransactionResponseV1{}
	mi := &file_inner_processing_grpc_transaction_status_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BatchTransactionResponseV1) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BatchTransactionResponseV1) ProtoMessage() {}

func (x *BatchTransactionResponseV1) ProtoReflect() protoreflect.Message {
	mi := &file_inner_processing_grpc_transaction_status_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BatchTransactionResponseV1.ProtoReflect.Descriptor instead.
func (*BatchTransactionResponseV1) Descriptor() ([]byte, []int) {
	return file_inner_processing_grpc_transaction_status_proto_rawDescGZIP(), []int{3}
}

func (x *BatchTransactionResponseV1) GetBatchResponse() []*TransactionWithStatusResponseV1 {
	if x != nil {
		return x.BatchResponse
	}
	return nil
}

var file_inner_processing_grpc_transaction_status_proto_extTypes = []protoimpl.ExtensionInfo{
	{
		ExtendedType:  (*descriptorpb.EnumValueOptions)(nil),
		ExtensionType: (*TransactionStatusRef)(nil),
		Field:         200000,
		Name:          "processing.transaction.transaction_status.transaction_status_value",
		Tag:           "bytes,200000,opt,name=transaction_status_value",
		Filename:      "inner/processing/grpc/transaction_status.proto",
	},
	{
		ExtendedType:  (*descriptorpb.EnumOptions)(nil),
		ExtensionType: (*TransactionStatusRef)(nil),
		Field:         200001,
		Name:          "processing.transaction.transaction_status.default_transaction_status_value",
		Tag:           "bytes,200001,opt,name=default_transaction_status_value",
		Filename:      "inner/processing/grpc/transaction_status.proto",
	},
}

// Extension fields to descriptorpb.EnumValueOptions.
var (
	// optional processing.transaction.transaction_status.TransactionStatusRef transaction_status_value = 200000;
	E_TransactionStatusValue = &file_inner_processing_grpc_transaction_status_proto_extTypes[0]
)

// Extension fields to descriptorpb.EnumOptions.
var (
	// optional processing.transaction.transaction_status.TransactionStatusRef default_transaction_status_value = 200001;
	E_DefaultTransactionStatusValue = &file_inner_processing_grpc_transaction_status_proto_extTypes[1]
)

var File_inner_processing_grpc_transaction_status_proto protoreflect.FileDescriptor

var file_inner_processing_grpc_transaction_status_proto_rawDesc = string([]byte{
	0x0a, 0x2e, 0x69, 0x6e, 0x6e, 0x65, 0x72, 0x2f, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69,
	0x6e, 0x67, 0x2f, 0x67, 0x72, 0x70, 0x63, 0x2f, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74,
	0x69, 0x6f, 0x6e, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x12, 0x29, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x2e, 0x74, 0x72, 0x61,
	0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63,
	0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x1a, 0x20, 0x67, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x64, 0x65, 0x73,
	0x63, 0x72, 0x69, 0x70, 0x74, 0x6f, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x14, 0x6d,
	0x76, 0x70, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f, 0x72, 0x65, 0x66, 0x73, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x22, 0x59, 0x0a, 0x14, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69,
	0x6f, 0x6e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65, 0x66, 0x12, 0x12, 0x0a, 0x04, 0x6e,
	0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12,
	0x12, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x63,
	0x6f, 0x64, 0x65, 0x12, 0x19, 0x0a, 0x08, 0x69, 0x73, 0x5f, 0x66, 0x69, 0x6e, 0x61, 0x6c, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x08, 0x52, 0x07, 0x69, 0x73, 0x46, 0x69, 0x6e, 0x61, 0x6c, 0x22, 0x44,
	0x0a, 0x19, 0x42, 0x61, 0x74, 0x63, 0x68, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69,
	0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x56, 0x31, 0x12, 0x27, 0x0a, 0x0f, 0x74,
	0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x01,
	0x20, 0x03, 0x28, 0x04, 0x52, 0x0e, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f,
	0x6e, 0x49, 0x64, 0x73, 0x22, 0x91, 0x01, 0x0a, 0x1f, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63,
	0x74, 0x69, 0x6f, 0x6e, 0x57, 0x69, 0x74, 0x68, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x56, 0x31, 0x12, 0x25, 0x0a, 0x0e, 0x74, 0x72, 0x61, 0x6e,
	0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04,
	0x52, 0x0d, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x12,
	0x1f, 0x0a, 0x0b, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x43, 0x6f, 0x64, 0x65,
	0x12, 0x26, 0x0a, 0x0f, 0x69, 0x73, 0x5f, 0x66, 0x69, 0x6e, 0x61, 0x6c, 0x5f, 0x73, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x18, 0x03, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0d, 0x69, 0x73, 0x46, 0x69, 0x6e,
	0x61, 0x6c, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x22, 0x8f, 0x01, 0x0a, 0x1a, 0x42, 0x61, 0x74,
	0x63, 0x68, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x56, 0x31, 0x12, 0x71, 0x0a, 0x0e, 0x62, 0x61, 0x74, 0x63, 0x68,
	0x5f, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x4a, 0x2e, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x2e, 0x74, 0x72, 0x61,
	0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63,
	0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x2e, 0x54, 0x72, 0x61, 0x6e,
	0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x57, 0x69, 0x74, 0x68, 0x53, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x56, 0x31, 0x52, 0x0d, 0x62, 0x61, 0x74,
	0x63, 0x68, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2a, 0xcd, 0x0d, 0x0a, 0x15, 0x45,
	0x6e, 0x75, 0x6d, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x12, 0x25, 0x0a, 0x07, 0x55, 0x6e, 0x6b, 0x6e, 0x6f, 0x77, 0x6e, 0x10,
	0x00, 0x1a, 0x18, 0x82, 0xd4, 0x61, 0x14, 0x12, 0x07, 0x55, 0x6e, 0x6b, 0x6e, 0x6f, 0x77, 0x6e,
	0x1a, 0x07, 0x55, 0x6e, 0x6b, 0x6e, 0x6f, 0x77, 0x6e, 0x20, 0x00, 0x12, 0x4a, 0x0a, 0x14, 0x54,
	0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x4e, 0x65, 0x77, 0x10, 0x02, 0x1a, 0x30, 0x82, 0xd4, 0x61, 0x2c, 0x12, 0x23, 0xd0, 0xa2, 0xd1,
	0x80, 0xd0, 0xb0, 0xd0, 0xbd, 0xd0, 0xb7, 0xd0, 0xb0, 0xd0, 0xba, 0xd1, 0x86, 0xd0, 0xb8, 0xd1,
	0x8f, 0x20, 0xd1, 0x81, 0xd0, 0xbe, 0xd0, 0xb7, 0xd0, 0xb4, 0xd0, 0xb0, 0xd0, 0xbd, 0xd0, 0xb0,
	0x1a, 0x03, 0x6e, 0x65, 0x77, 0x20, 0x00, 0x12, 0x86, 0x01, 0x0a, 0x1f, 0x54, 0x72, 0x61, 0x6e,
	0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x54, 0x68, 0x72,
	0x65, 0x65, 0x44, 0x53, 0x57, 0x61, 0x69, 0x74, 0x69, 0x6e, 0x67, 0x10, 0x03, 0x1a, 0x61, 0x82,
	0xd4, 0x61, 0x5d, 0x12, 0x48, 0xd0, 0xa2, 0xd1, 0x80, 0xd0, 0xb0, 0xd0, 0xbd, 0xd0, 0xb7, 0xd0,
	0xb0, 0xd0, 0xba, 0xd1, 0x86, 0xd0, 0xb8, 0xd1, 0x8f, 0x20, 0xd0, 0xbe, 0xd0, 0xb6, 0xd0, 0xb8,
	0xd0, 0xb4, 0xd0, 0xb0, 0xd0, 0xb5, 0xd1, 0x82, 0x20, 0xd0, 0xbe, 0xd1, 0x82, 0xd0, 0xb2, 0xd0,
	0xb5, 0xd1, 0x82, 0xd0, 0xb0, 0x20, 0xd0, 0xbe, 0xd1, 0x82, 0x20, 0x33, 0x64, 0x73, 0x20, 0xd1,
	0x81, 0xd0, 0xb5, 0xd1, 0x80, 0xd0, 0xb2, 0xd0, 0xb5, 0xd1, 0x80, 0xd0, 0xb0, 0x1a, 0x0f, 0x74,
	0x68, 0x72, 0x65, 0x65, 0x64, 0x73, 0x5f, 0x77, 0x61, 0x69, 0x74, 0x69, 0x6e, 0x67, 0x20, 0x00,
	0x12, 0x57, 0x0a, 0x1a, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x53,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x65, 0x64, 0x10, 0x04,
	0x1a, 0x37, 0x82, 0xd4, 0x61, 0x33, 0x12, 0x24, 0xd0, 0xa2, 0xd1, 0x80, 0xd0, 0xb0, 0xd0, 0xbd,
	0xd0, 0xb7, 0xd0, 0xb0, 0xd0, 0xba, 0xd1, 0x86, 0xd0, 0xb8, 0xd1, 0x8f, 0x20, 0xd0, 0xb2, 0x20,
	0xd1, 0x80, 0xd0, 0xb0, 0xd0, 0xb1, 0xd0, 0xbe, 0xd1, 0x82, 0xd0, 0xb5, 0x1a, 0x09, 0x70, 0x72,
	0x6f, 0x63, 0x65, 0x73, 0x73, 0x65, 0x64, 0x20, 0x00, 0x12, 0x61, 0x0a, 0x17, 0x54, 0x72, 0x61,
	0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x46, 0x61,
	0x69, 0x6c, 0x65, 0x64, 0x10, 0x05, 0x1a, 0x44, 0x82, 0xd4, 0x61, 0x40, 0x12, 0x34, 0xd0, 0xa2,
	0xd1, 0x80, 0xd0, 0xb0, 0xd0, 0xbd, 0xd0, 0xb7, 0xd0, 0xb0, 0xd0, 0xba, 0xd1, 0x86, 0xd0, 0xb8,
	0xd1, 0x8f, 0x20, 0xd0, 0xbf, 0xd1, 0x80, 0xd0, 0xbe, 0xd1, 0x88, 0xd0, 0xbb, 0xd0, 0xb0, 0x20,
	0xd0, 0xbd, 0xd0, 0xb5, 0xd1, 0x83, 0xd1, 0x81, 0xd0, 0xbf, 0xd0, 0xb5, 0xd1, 0x88, 0xd0, 0xbd,
	0xd0, 0xbe, 0x1a, 0x06, 0x66, 0x61, 0x69, 0x6c, 0x65, 0x64, 0x20, 0x01, 0x12, 0x56, 0x0a, 0x17,
	0x54, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x52, 0x65, 0x66, 0x75, 0x6e, 0x64, 0x10, 0x06, 0x1a, 0x39, 0x82, 0xd4, 0x61, 0x35, 0x12,
	0x29, 0xd0, 0xa2, 0xd1, 0x80, 0xd0, 0xb0, 0xd0, 0xbd, 0xd0, 0xb7, 0xd0, 0xb0, 0xd0, 0xba, 0xd1,
	0x86, 0xd0, 0xb8, 0xd1, 0x8f, 0x20, 0xd0, 0xb2, 0xd0, 0xbe, 0xd0, 0xb7, 0xd0, 0xb2, 0xd1, 0x80,
	0xd0, 0xb0, 0xd1, 0x89, 0xd0, 0xb5, 0xd0, 0xbd, 0xd0, 0xb0, 0x1a, 0x06, 0x72, 0x65, 0x66, 0x75,
	0x6e, 0x64, 0x20, 0x01, 0x12, 0x56, 0x0a, 0x19, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74,
	0x69, 0x6f, 0x6e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x43, 0x61, 0x6e, 0x63, 0x65, 0x6c, 0x65,
	0x64, 0x10, 0x07, 0x1a, 0x37, 0x82, 0xd4, 0x61, 0x33, 0x12, 0x25, 0xd0, 0xa2, 0xd1, 0x80, 0xd0,
	0xb0, 0xd0, 0xbd, 0xd0, 0xb7, 0xd0, 0xb0, 0xd0, 0xba, 0xd1, 0x86, 0xd0, 0xb8, 0xd1, 0x8f, 0x20,
	0xd0, 0xbe, 0xd1, 0x82, 0xd0, 0xbc, 0xd0, 0xb5, 0xd0, 0xbd, 0xd0, 0xb5, 0xd0, 0xbd, 0xd0, 0xb0,
	0x1a, 0x08, 0x63, 0x61, 0x6e, 0x63, 0x65, 0x6c, 0x65, 0x64, 0x20, 0x01, 0x12, 0x5e, 0x0a, 0x16,
	0x54, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x52, 0x65, 0x74, 0x72, 0x79, 0x10, 0x08, 0x1a, 0x42, 0x82, 0xd4, 0x61, 0x3e, 0x12, 0x33,
	0xd0, 0xa2, 0xd1, 0x80, 0xd0, 0xb0, 0xd0, 0xbd, 0xd0, 0xb7, 0xd0, 0xb0, 0xd0, 0xba, 0xd1, 0x86,
	0xd0, 0xb8, 0xd1, 0x8f, 0x20, 0xd0, 0xb2, 0x20, 0xd1, 0x81, 0xd1, 0x82, 0xd0, 0xb0, 0xd1, 0x82,
	0xd1, 0x83, 0xd1, 0x81, 0xd0, 0xb5, 0x20, 0xd0, 0xbf, 0xd0, 0xbe, 0xd0, 0xb2, 0xd1, 0x82, 0xd0,
	0xbe, 0xd1, 0x80, 0x1a, 0x05, 0x72, 0x65, 0x74, 0x72, 0x79, 0x20, 0x00, 0x12, 0x5f, 0x0a, 0x18,
	0x54, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x53, 0x75, 0x63, 0x63, 0x65, 0x73, 0x73, 0x10, 0x09, 0x1a, 0x41, 0x82, 0xd4, 0x61, 0x3d,
	0x12, 0x30, 0xd0, 0xa2, 0xd1, 0x80, 0xd0, 0xb0, 0xd0, 0xbd, 0xd0, 0xb7, 0xd0, 0xb0, 0xd0, 0xba,
	0xd1, 0x86, 0xd0, 0xb8, 0xd1, 0x8f, 0x20, 0xd0, 0xbf, 0xd1, 0x80, 0xd0, 0xbe, 0xd1, 0x88, 0xd0,
	0xbb, 0xd0, 0xb0, 0x20, 0xd1, 0x83, 0xd1, 0x81, 0xd0, 0xbf, 0xd0, 0xb5, 0xd1, 0x88, 0xd0, 0xbd,
	0xd0, 0xbe, 0x1a, 0x07, 0x73, 0x75, 0x63, 0x63, 0x65, 0x73, 0x73, 0x20, 0x01, 0x12, 0x88, 0x01,
	0x0a, 0x20, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x54, 0x68, 0x72, 0x65, 0x65, 0x44, 0x53, 0x52, 0x65, 0x63, 0x65, 0x69, 0x76,
	0x65, 0x64, 0x10, 0x0a, 0x1a, 0x62, 0x82, 0xd4, 0x61, 0x5e, 0x12, 0x48, 0xd0, 0xa2, 0xd1, 0x80,
	0xd0, 0xb0, 0xd0, 0xbd, 0xd0, 0xb7, 0xd0, 0xb0, 0xd0, 0xba, 0xd1, 0x86, 0xd0, 0xb8, 0xd1, 0x8f,
	0x20, 0xd0, 0xbf, 0xd0, 0xbe, 0xd0, 0xbb, 0xd1, 0x83, 0xd1, 0x87, 0xd0, 0xb8, 0xd0, 0xbb, 0xd0,
	0xb0, 0x20, 0xd0, 0xbe, 0xd1, 0x82, 0xd0, 0xb2, 0xd0, 0xb5, 0xd1, 0x82, 0x20, 0xd0, 0xbe, 0xd1,
	0x82, 0x20, 0x33, 0x64, 0x73, 0x20, 0xd1, 0x81, 0xd0, 0xb5, 0xd1, 0x80, 0xd0, 0xb2, 0xd0, 0xb5,
	0xd1, 0x80, 0xd0, 0xb0, 0x1a, 0x10, 0x74, 0x68, 0x72, 0x65, 0x65, 0x64, 0x73, 0x5f, 0x72, 0x65,
	0x63, 0x65, 0x69, 0x76, 0x65, 0x64, 0x20, 0x00, 0x12, 0x64, 0x0a, 0x17, 0x54, 0x72, 0x61, 0x6e,
	0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x48, 0x6f, 0x6c,
	0x64, 0x65, 0x64, 0x10, 0x0b, 0x1a, 0x47, 0x82, 0xd4, 0x61, 0x43, 0x12, 0x37, 0xd0, 0xa2, 0xd1,
	0x80, 0xd0, 0xb0, 0xd0, 0xbd, 0xd0, 0xb7, 0xd0, 0xb0, 0xd0, 0xba, 0xd1, 0x86, 0xd0, 0xb8, 0xd1,
	0x8f, 0x20, 0xd0, 0xb2, 0x20, 0xd1, 0x81, 0xd1, 0x82, 0xd0, 0xb0, 0xd1, 0x82, 0xd1, 0x83, 0xd1,
	0x81, 0xd0, 0xb5, 0x20, 0xd0, 0xbe, 0xd0, 0xb6, 0xd0, 0xb8, 0xd0, 0xb4, 0xd0, 0xb0, 0xd0, 0xbd,
	0xd0, 0xb8, 0xd1, 0x8f, 0x1a, 0x06, 0x68, 0x6f, 0x6c, 0x64, 0x65, 0x64, 0x20, 0x00, 0x12, 0x84,
	0x01, 0x0a, 0x1e, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x52, 0x65, 0x66, 0x75, 0x6e, 0x64, 0x57, 0x61, 0x69, 0x74, 0x69, 0x6e,
	0x67, 0x10, 0x0d, 0x1a, 0x60, 0x82, 0xd4, 0x61, 0x5c, 0x12, 0x48, 0xd0, 0xa2, 0xd1, 0x80, 0xd0,
	0xb0, 0xd0, 0xbd, 0xd0, 0xb7, 0xd0, 0xb0, 0xd0, 0xba, 0xd1, 0x86, 0xd0, 0xb8, 0xd1, 0x8f, 0x20,
	0xd0, 0xb2, 0x20, 0xd1, 0x81, 0xd1, 0x82, 0xd0, 0xb0, 0xd1, 0x82, 0xd1, 0x83, 0xd1, 0x81, 0xd0,
	0xb5, 0x20, 0xd0, 0xbe, 0xd0, 0xb6, 0xd0, 0xb8, 0xd0, 0xb4, 0xd0, 0xb0, 0xd0, 0xbd, 0xd0, 0xb8,
	0xd1, 0x8f, 0x20, 0xd0, 0xb2, 0xd0, 0xbe, 0xd0, 0xb7, 0xd0, 0xb2, 0xd1, 0x80, 0xd0, 0xb0, 0xd1,
	0x82, 0xd0, 0xb0, 0x1a, 0x0e, 0x72, 0x65, 0x66, 0x75, 0x6e, 0x64, 0x5f, 0x77, 0x61, 0x69, 0x74,
	0x69, 0x6e, 0x67, 0x20, 0x00, 0x12, 0x62, 0x0a, 0x1b, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63,
	0x74, 0x69, 0x6f, 0x6e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x41, 0x75, 0x74, 0x68, 0x6f, 0x72,
	0x69, 0x7a, 0x65, 0x64, 0x10, 0x0e, 0x1a, 0x41, 0x82, 0xd4, 0x61, 0x3d, 0x12, 0x2d, 0xd0, 0xa2,
	0xd1, 0x80, 0xd0, 0xb0, 0xd0, 0xbd, 0xd0, 0xb7, 0xd0, 0xb0, 0xd0, 0xba, 0xd1, 0x86, 0xd0, 0xb8,
	0xd1, 0x8f, 0x20, 0xd0, 0xb0, 0xd0, 0xb2, 0xd1, 0x82, 0xd0, 0xbe, 0xd1, 0x80, 0xd0, 0xb8, 0xd0,
	0xb7, 0xd0, 0xbe, 0xd0, 0xb2, 0xd0, 0xb0, 0xd0, 0xbd, 0xd0, 0xb0, 0x1a, 0x0a, 0x61, 0x75, 0x74,
	0x68, 0x6f, 0x72, 0x69, 0x7a, 0x65, 0x64, 0x20, 0x01, 0x12, 0x7b, 0x0a, 0x16, 0x54, 0x72, 0x61,
	0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x45, 0x72,
	0x72, 0x6f, 0x72, 0x10, 0x0f, 0x1a, 0x5f, 0x82, 0xd4, 0x61, 0x5b, 0x12, 0x50, 0xd0, 0x9f, 0xd1,
	0x80, 0xd0, 0xbe, 0xd0, 0xb8, 0xd0, 0xb7, 0xd0, 0xbe, 0xd1, 0x88, 0xd0, 0xbb, 0xd0, 0xb0, 0x20,
	0xd0, 0xbe, 0xd1, 0x88, 0xd0, 0xb8, 0xd0, 0xb1, 0xd0, 0xba, 0xd0, 0xb0, 0x20, 0xd0, 0xbf, 0xd1,
	0x80, 0xd0, 0xb8, 0x20, 0xd0, 0xbf, 0xd1, 0x80, 0xd0, 0xbe, 0xd0, 0xb2, 0xd0, 0xb5, 0xd0, 0xb4,
	0xd0, 0xb5, 0xd0, 0xbd, 0xd0, 0xb8, 0xd0, 0xb8, 0x20, 0xd1, 0x82, 0xd1, 0x80, 0xd0, 0xb0, 0xd0,
	0xbd, 0xd0, 0xb7, 0xd0, 0xb0, 0xd0, 0xba, 0xd1, 0x86, 0xd0, 0xb8, 0xd0, 0xb8, 0x1a, 0x05, 0x65,
	0x72, 0x72, 0x6f, 0x72, 0x20, 0x00, 0x12, 0x8b, 0x01, 0x0a, 0x1c, 0x54, 0x72, 0x61, 0x6e, 0x73,
	0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x46, 0x69, 0x6e, 0x67,
	0x65, 0x72, 0x50, 0x72, 0x69, 0x6e, 0x74, 0x10, 0x10, 0x1a, 0x69, 0x82, 0xd4, 0x61, 0x65, 0x12,
	0x54, 0xd0, 0x9e, 0xd0, 0xb6, 0xd0, 0xb8, 0xd0, 0xb4, 0xd0, 0xb0, 0xd0, 0xb5, 0xd1, 0x82, 0xd1,
	0x81, 0xd1, 0x8f, 0x20, 0xd0, 0xbf, 0xd1, 0x80, 0xd0, 0xbe, 0xd0, 0xb2, 0xd0, 0xb5, 0xd1, 0x80,
	0xd0, 0xba, 0xd0, 0xb0, 0x20, 0xd0, 0xbe, 0xd1, 0x82, 0xd0, 0xbf, 0xd0, 0xb5, 0xd1, 0x87, 0xd0,
	0xb0, 0xd1, 0x82, 0xd0, 0xba, 0xd0, 0xb0, 0x20, 0xd0, 0xb1, 0xd1, 0x80, 0xd0, 0xb0, 0xd1, 0x83,
	0xd0, 0xb7, 0xd0, 0xb5, 0xd1, 0x80, 0xd0, 0xb0, 0x20, 0xd0, 0xb1, 0xd0, 0xb0, 0xd0, 0xbd, 0xd0,
	0xba, 0xd0, 0xbe, 0xd0, 0xbc, 0x1a, 0x0b, 0x66, 0x69, 0x6e, 0x67, 0x65, 0x72, 0x70, 0x72, 0x69,
	0x6e, 0x74, 0x20, 0x00, 0x1a, 0xa3, 0x01, 0x8a, 0xd4, 0x61, 0x5b, 0x12, 0x50, 0xd0, 0x9f, 0xd1,
	0x80, 0xd0, 0xbe, 0xd0, 0xb8, 0xd0, 0xb7, 0xd0, 0xbe, 0xd1, 0x88, 0xd0, 0xbb, 0xd0, 0xb0, 0x20,
	0xd0, 0xbe, 0xd1, 0x88, 0xd0, 0xb8, 0xd0, 0xb1, 0xd0, 0xba, 0xd0, 0xb0, 0x20, 0xd0, 0xbf, 0xd1,
	0x80, 0xd0, 0xb8, 0x20, 0xd0, 0xbf, 0xd1, 0x80, 0xd0, 0xbe, 0xd0, 0xb2, 0xd0, 0xb5, 0xd0, 0xb4,
	0xd0, 0xb5, 0xd0, 0xbd, 0xd0, 0xb8, 0xd0, 0xb8, 0x20, 0xd1, 0x82, 0xd1, 0x80, 0xd0, 0xb0, 0xd0,
	0xbd, 0xd0, 0xb7, 0xd0, 0xb0, 0xd0, 0xba, 0xd1, 0x86, 0xd0, 0xb8, 0xd0, 0xb8, 0x1a, 0x05, 0x65,
	0x72, 0x72, 0x6f, 0x72, 0x20, 0x00, 0xaa, 0x82, 0xec, 0x8e, 0x02, 0x20, 0x64, 0x65, 0x66, 0x61,
	0x75, 0x6c, 0x74, 0x5f, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x5f,
	0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x5f, 0x76, 0x61, 0x6c, 0x75, 0x65, 0xb2, 0x82, 0xec, 0x8e,
	0x02, 0x18, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x73, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x5f, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x32, 0xc4, 0x01, 0x0a, 0x11, 0x54,
	0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x12, 0xae, 0x01, 0x0a, 0x1f, 0x47, 0x65, 0x74, 0x42, 0x61, 0x74, 0x63, 0x68, 0x54, 0x72, 0x61,
	0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x57, 0x69, 0x74, 0x68, 0x53, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x65, 0x73, 0x12, 0x44, 0x2e, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e,
	0x67, 0x2e, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x74, 0x72,
	0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x2e, 0x42, 0x61, 0x74, 0x63, 0x68, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f,
	0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x56, 0x31, 0x1a, 0x45, 0x2e, 0x70, 0x72, 0x6f,
	0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x2e, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74,
	0x69, 0x6f, 0x6e, 0x2e, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x5f,
	0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x2e, 0x42, 0x61, 0x74, 0x63, 0x68, 0x54, 0x72, 0x61, 0x6e,
	0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x56,
	0x31, 0x3a, 0x9e, 0x01, 0x0a, 0x18, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f,
	0x6e, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x5f, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x12, 0x21,
	0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66,
	0x2e, 0x45, 0x6e, 0x75, 0x6d, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e,
	0x73, 0x18, 0xc0, 0x9a, 0x0c, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x3f, 0x2e, 0x70, 0x72, 0x6f, 0x63,
	0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x2e, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69,
	0x6f, 0x6e, 0x2e, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x73,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x2e, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f,
	0x6e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65, 0x66, 0x52, 0x16, 0x74, 0x72, 0x61, 0x6e,
	0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x56, 0x61, 0x6c,
	0x75, 0x65, 0x3a, 0xa8, 0x01, 0x0a, 0x20, 0x64, 0x65, 0x66, 0x61, 0x75, 0x6c, 0x74, 0x5f, 0x74,
	0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x5f, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x12, 0x1c, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x45, 0x6e, 0x75, 0x6d, 0x4f, 0x70,
	0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0xc1, 0x9a, 0x0c, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x3f, 0x2e,
	0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x2e, 0x74, 0x72, 0x61, 0x6e, 0x73,
	0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69,
	0x6f, 0x6e, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x2e, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x61,
	0x63, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65, 0x66, 0x52, 0x1d,
	0x64, 0x65, 0x66, 0x61, 0x75, 0x6c, 0x74, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69,
	0x6f, 0x6e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x42, 0x2d, 0x5a,
	0x2b, 0x67, 0x69, 0x74, 0x2e, 0x6c, 0x6f, 0x63, 0x61, 0x6c, 0x2f, 0x73, 0x65, 0x6e, 0x73, 0x69,
	0x74, 0x69, 0x76, 0x65, 0x2f, 0x69, 0x6e, 0x6e, 0x65, 0x72, 0x70, 0x62, 0x2f, 0x70, 0x72, 0x6f,
	0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x2f, 0x67, 0x72, 0x70, 0x63, 0x62, 0x08, 0x65, 0x64,
	0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x70, 0xe8, 0x07,
})

var (
	file_inner_processing_grpc_transaction_status_proto_rawDescOnce sync.Once
	file_inner_processing_grpc_transaction_status_proto_rawDescData []byte
)

func file_inner_processing_grpc_transaction_status_proto_rawDescGZIP() []byte {
	file_inner_processing_grpc_transaction_status_proto_rawDescOnce.Do(func() {
		file_inner_processing_grpc_transaction_status_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_inner_processing_grpc_transaction_status_proto_rawDesc), len(file_inner_processing_grpc_transaction_status_proto_rawDesc)))
	})
	return file_inner_processing_grpc_transaction_status_proto_rawDescData
}

var file_inner_processing_grpc_transaction_status_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_inner_processing_grpc_transaction_status_proto_msgTypes = make([]protoimpl.MessageInfo, 4)
var file_inner_processing_grpc_transaction_status_proto_goTypes = []any{
	(EnumTransactionStatus)(0),              // 0: processing.transaction.transaction_status.EnumTransactionStatus
	(*TransactionStatusRef)(nil),            // 1: processing.transaction.transaction_status.TransactionStatusRef
	(*BatchTransactionRequestV1)(nil),       // 2: processing.transaction.transaction_status.BatchTransactionRequestV1
	(*TransactionWithStatusResponseV1)(nil), // 3: processing.transaction.transaction_status.TransactionWithStatusResponseV1
	(*BatchTransactionResponseV1)(nil),      // 4: processing.transaction.transaction_status.BatchTransactionResponseV1
	(*descriptorpb.EnumValueOptions)(nil),   // 5: google.protobuf.EnumValueOptions
	(*descriptorpb.EnumOptions)(nil),        // 6: google.protobuf.EnumOptions
}
var file_inner_processing_grpc_transaction_status_proto_depIdxs = []int32{
	3, // 0: processing.transaction.transaction_status.BatchTransactionResponseV1.batch_response:type_name -> processing.transaction.transaction_status.TransactionWithStatusResponseV1
	5, // 1: processing.transaction.transaction_status.transaction_status_value:extendee -> google.protobuf.EnumValueOptions
	6, // 2: processing.transaction.transaction_status.default_transaction_status_value:extendee -> google.protobuf.EnumOptions
	1, // 3: processing.transaction.transaction_status.transaction_status_value:type_name -> processing.transaction.transaction_status.TransactionStatusRef
	1, // 4: processing.transaction.transaction_status.default_transaction_status_value:type_name -> processing.transaction.transaction_status.TransactionStatusRef
	2, // 5: processing.transaction.transaction_status.TransactionStatus.GetBatchTransactionWithStatuses:input_type -> processing.transaction.transaction_status.BatchTransactionRequestV1
	4, // 6: processing.transaction.transaction_status.TransactionStatus.GetBatchTransactionWithStatuses:output_type -> processing.transaction.transaction_status.BatchTransactionResponseV1
	6, // [6:7] is the sub-list for method output_type
	5, // [5:6] is the sub-list for method input_type
	3, // [3:5] is the sub-list for extension type_name
	1, // [1:3] is the sub-list for extension extendee
	0, // [0:1] is the sub-list for field type_name
}

func init() { file_inner_processing_grpc_transaction_status_proto_init() }
func file_inner_processing_grpc_transaction_status_proto_init() {
	if File_inner_processing_grpc_transaction_status_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_inner_processing_grpc_transaction_status_proto_rawDesc), len(file_inner_processing_grpc_transaction_status_proto_rawDesc)),
			NumEnums:      1,
			NumMessages:   4,
			NumExtensions: 2,
			NumServices:   1,
		},
		GoTypes:           file_inner_processing_grpc_transaction_status_proto_goTypes,
		DependencyIndexes: file_inner_processing_grpc_transaction_status_proto_depIdxs,
		EnumInfos:         file_inner_processing_grpc_transaction_status_proto_enumTypes,
		MessageInfos:      file_inner_processing_grpc_transaction_status_proto_msgTypes,
		ExtensionInfos:    file_inner_processing_grpc_transaction_status_proto_extTypes,
	}.Build()
	File_inner_processing_grpc_transaction_status_proto = out.File
	file_inner_processing_grpc_transaction_status_proto_goTypes = nil
	file_inner_processing_grpc_transaction_status_proto_depIdxs = nil
}
