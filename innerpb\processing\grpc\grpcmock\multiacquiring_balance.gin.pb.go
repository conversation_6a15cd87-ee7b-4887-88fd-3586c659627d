// Code generated by MockGen. DO NOT EDIT.
// Source: multiacquiring_balance.gin.pb.go

// Package grpcmock is a generated GoMock package.
package grpcmock

import (
	reflect "reflect"

	gin "github.com/gin-gonic/gin"
	gomock "github.com/golang/mock/gomock"
)

// MockGinMultiacquiringBalanceServer is a mock of GinMultiacquiringBalanceServer interface.
type MockGinMultiacquiringBalanceServer struct {
	ctrl     *gomock.Controller
	recorder *MockGinMultiacquiringBalanceServerMockRecorder
}

// MockGinMultiacquiringBalanceServerMockRecorder is the mock recorder for MockGinMultiacquiringBalanceServer.
type MockGinMultiacquiringBalanceServerMockRecorder struct {
	mock *MockGinMultiacquiringBalanceServer
}

// NewMockGinMultiacquiringBalanceServer creates a new mock instance.
func NewMockGinMultiacquiringBalanceServer(ctrl *gomock.Controller) *MockGinMultiacquiringBalanceServer {
	mock := &MockGinMultiacquiringBalanceServer{ctrl: ctrl}
	mock.recorder = &MockGinMultiacquiringBalanceServerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockGinMultiacquiringBalanceServer) EXPECT() *MockGinMultiacquiringBalanceServerMockRecorder {
	return m.recorder
}

// CheckBalance mocks base method.
func (m *MockGinMultiacquiringBalanceServer) CheckBalance(c *gin.Context) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CheckBalance", c)
	ret0, _ := ret[0].(error)
	return ret0
}

// CheckBalance indicates an expected call of CheckBalance.
func (mr *MockGinMultiacquiringBalanceServerMockRecorder) CheckBalance(c interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CheckBalance", reflect.TypeOf((*MockGinMultiacquiringBalanceServer)(nil).CheckBalance), c)
}
