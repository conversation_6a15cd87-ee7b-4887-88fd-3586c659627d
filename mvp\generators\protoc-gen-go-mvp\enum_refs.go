package main

import (
	"fmt"
	"sort"
	"sync"

	"google.golang.org/protobuf/compiler/protogen"
	"google.golang.org/protobuf/proto"
	"google.golang.org/protobuf/reflect/protoreflect"

	"git.local/sensitive/mvp/pb"
)

func init() {
	RegisterGenerator(&EnumRefsGenerator{})
}

type EnumRefsGenerator struct{}

func (generator *EnumRefsGenerator) ProtoGenGenerate(gen *protogen.Plugin) {
	messages := map[protoreflect.FullName]*protogen.Message{}
	enums := map[protoreflect.FullName]*protogen.Enum{}
	for _, file := range gen.Files {
		for _, message := range file.Messages {
			messages[message.Desc.FullName()] = message
		}

		for _, enum := range file.Enums {
			enums[enum.Desc.FullName()] = enum
		}
	}

	for _, file := range gen.Files {
		if !file.Generate {
			continue
		}

		if len(file.Enums) == 0 {
			return
		}

		g := gen.NewGeneratedFile(
			file.GeneratedFilenamePrefix+V2_ENUM_REFS_SUFFIX,
			file.GoImportPath,
		)
		g.<PERSON>()

		HeaderPrint(g, string(file.GoPackageName))

		var once sync.Once

		for enumIndex, enumGen := range file.Enums {
			extDefault, _ := proto.GetExtension(enumGen.Desc.Options(), pb.E_DefaultRef).(string)
			if len(extDefault) == 0 {
				continue
			}

			extValue, _ := proto.GetExtension(enumGen.Desc.Options(), pb.E_Ref).(string)

			enumDesc := file.Proto.GetEnumType()[enumIndex]
			enumDesc.GetOptions().ProtoReflect().Range(func(
				fd protoreflect.FieldDescriptor,
				v protoreflect.Value,
			) bool {
				if fd.Kind() != protoreflect.MessageKind {
					return true
				}

				if extDefault != string(fd.Name()) {
					return true
				}

				enumMessage, okEnumMessage := messages[fd.Message().FullName()]
				if !okEnumMessage {
					return true
				}

				enumData := &enumRef{
					defaultValues: make(map[string]any, len(enumMessage.Fields)),
					returns:       make(map[string]protogen.GoIdent, len(enumMessage.Fields)),
					fields:        make(map[string]*protogen.Field, len(enumMessage.Fields)),
					values:        make(map[int32]map[string]any, len(enumMessage.Fields)),
				}

				for _, field := range enumMessage.Fields {
					enumData.fields[field.GoName] = field
					enumData.returns[field.GoName], enumData.defaultValues[field.GoName] = PrepareValue(
						file,
						enumMessage,
						field,
						v.Message().Get(field.Desc),
						enums,
						false,
					)
				}

				for _, descriptorProto := range enumDesc.GetValue() {
					values := make(map[string]any, len(enumMessage.Fields))
					descriptorProto.GetOptions().ProtoReflect().Range(func(
						fdValue protoreflect.FieldDescriptor,
						vValue protoreflect.Value,
					) bool {
						if fdValue.Kind() != protoreflect.MessageKind {
							return true
						}

						if extValue != string(fdValue.Name()) {
							return true
						}

						vValueFields := map[protoreflect.FieldDescriptor]struct{}{}
						vValue.Message().Range(func(
							descriptor protoreflect.FieldDescriptor,
							value protoreflect.Value,
						) bool {
							vValueFields[descriptor] = struct{}{}
							return true
						})

						for _, field := range enumMessage.Fields {
							if _, ok := vValueFields[field.Desc]; !ok {
								continue
							}

							if _, ok := enumData.defaultValues[field.GoName]; !ok {
								continue
							}

							_, values[field.GoName] = PrepareValue(
								file,
								enumMessage,
								field,
								vValue.Message().Get(field.Desc),
								enums,
								false,
							)
						}

						enumData.values[descriptorProto.GetNumber()] = values
						return true
					})
				}

				funcNames := make([]string, 0, len(enumData.returns))
				for key := range enumData.fields {
					funcNames = append(funcNames, key)
				}
				sort.Strings(funcNames)

				enumNumbers := make(map[int32]protogen.GoIdent, len(enumGen.Values))
				for _, value := range enumGen.Values {
					enumNumbers[int32(value.Desc.Number())] = value.GoIdent
				}

				var values []int32
				for key := range enumData.values {
					values = append(values, key)
				}
				sort.Slice(values, func(i, j int) bool {
					return values[i] < values[j]
				})

				for _, funcName := range funcNames {
					if enumData.fields[funcName].Desc.IsList() {
						g.P("func (x ", enumGen.GoIdent, ") ", funcName, "() []", enumData.returns[funcName], " {")
					} else {
						g.P("func (x ", enumGen.GoIdent, ") ", funcName, "() ", enumData.returns[funcName], " {")
					}
					g.P("switch x {")

					for _, value := range values {
						val, okVal := enumData.values[value][funcName]
						if !okVal {
							continue
						}

						enumIdent, okEnumStr := enumNumbers[value]
						if !okEnumStr {
							continue
						}

						g.P("case  ", enumIdent, ":")

						if enumData.fields[funcName].Desc.IsList() {
							g.P("return []", enumData.returns[funcName], "{")
							switch typed := val.(type) {
							case []any:
								for _, a := range typed {
									if goIdent, okIdent := a.(protogen.GoIdent); okIdent {
										g.P(g.QualifiedGoIdent(goIdent), ",")
									} else {
										g.P(a, ",")
									}
								}
							case protogen.GoIdent:
								g.P(g.QualifiedGoIdent(typed))
							default:
								g.P(typed)
							}
							g.P("}")
						} else {
							g.P("return ", val)
						}
					}

					g.P("default:")
					if enumData.fields[funcName].Desc.IsList() {
						g.P("return []", enumData.returns[funcName], "{")
						switch typed := enumData.defaultValues[funcName].(type) {
						case []any:
							for _, a := range typed {
								if goIdent, okIdent := a.(protogen.GoIdent); okIdent {
									g.P(g.QualifiedGoIdent(goIdent), ",")
								} else {
									g.P(a, ",")
								}
							}
						case protogen.GoIdent:
							g.P(g.QualifiedGoIdent(typed))
						default:
							g.P(typed)
						}
						g.P("}")
					} else {
						g.P("return ", enumData.defaultValues[funcName])
					}
					g.P("}")
					g.P("}")
					g.P()
				}

				headers := []any{"//", "|", g.QualifiedGoIdent(enumGen.GoIdent), "|"}

				for _, col := range funcNames {
					headers = append(headers, col, "|")
				}

				table := NewSimpleTable(g)
				table.AddRow(headers...)

				for _, enumValue := range enumGen.Values {
					row := []any{"//", "|", g.QualifiedGoIdent(enumValue.GoIdent), "|"}

					value, ok := enumData.values[int32(enumValue.Desc.Number())]
					if !ok {
						value = enumData.defaultValues
					}

					for _, col := range funcNames {
						if _, okValue := value[col]; !okValue {
							row = append(row, table.ToPrint(enumData.defaultValues[col]), "|")
							continue
						}

						row = append(row, table.ToPrint(value[col]), "|")
					}
					table.AddRow(row...)

				}

				g.P("// Created reference to ", g.QualifiedGoIdent(enumGen.GoIdent))
				g.P()
				g.P(table.String())
				g.P()

				g.P("var Slice", enumGen.GoIdent, "Refs *slice", enumGen.GoIdent, "Refs")
				g.P()
				g.P("type slice", enumGen.GoIdent, "Refs struct {}")
				g.P()
				for _, funcName := range funcNames {
					if enumData.fields[funcName].Desc.IsList() {
						g.P("func (*slice", enumGen.GoIdent, "Refs) ", funcName, "(slice ...", enumGen.GoIdent, " ) [][]", enumData.returns[funcName], " {")
						g.P("var result [][]", enumData.returns[funcName])
					} else {
						g.P("func (*slice", enumGen.GoIdent, "Refs) ", funcName, "(slice ...", enumGen.GoIdent, " ) []", enumData.returns[funcName], " {")
						g.P("var result []", enumData.returns[funcName])
					}
					g.P("for _, val := range slice {")
					g.P("result = append(result, val.", funcName, "())")
					g.P("}")
					g.P()
					g.P("return result")
					g.P("}")
					g.P()
				}

				once.Do(g.Unskip)
				return true
			})
		}
	}
}

type enumRef struct {
	defaultValues map[string]any
	returns       map[string]protogen.GoIdent
	fields        map[string]*protogen.Field
	values        map[int32]map[string]any
}

func PrepareValue(
	file *protogen.File,
	msg *protogen.Message,
	protoGenField *protogen.Field,
	protoReflectValue protoreflect.Value,
	enums map[protoreflect.FullName]*protogen.Enum,
	isListDone bool,
) (result protogen.GoIdent, val any) {
	result.GoImportPath = file.GoImportPath
	val = protoGenField.Desc.Default()

	if protoGenField.Desc.IsList() && !isListDone {
		var (
			listResult []any
			oneResult  any
		)
		list := protoReflectValue.List()
		for i := 0; i < list.Len(); i++ {
			one := list.Get(i)
			result, oneResult = PrepareValue(
				file,
				msg,
				protoGenField,
				one,
				enums,
				true,
			)

			listResult = append(listResult, oneResult)
		}

		if list.Len() == 0 {
			result, _ = PrepareValue(
				file,
				msg,
				protoGenField,
				list.NewElement(),
				enums,
				true,
			)
		}

		return result, listResult
	}

	switch protoGenField.Desc.Kind() {
	case protoreflect.EnumKind:
		enumDesc, ok := enums[protoGenField.Enum.Desc.FullName()]
		if !ok || enumDesc == nil {
			panic(fmt.Sprintf("enum not found for enum: %s, %v", protoGenField.Desc.FullName(), protoReflectValue.Interface()))
		}

		var enumValue *protogen.EnumValue
		for ii := 0; ii < len(enumDesc.Values); ii++ {
			if enumDesc.Values[ii].Desc.Number() == protoReflectValue.Enum() {
				enumValue = enumDesc.Values[ii]
				break
			}
		}

		if enumValue == nil {
			panic(fmt.Sprintf("enum value not found for enum: %s, %v", protoGenField.Desc.FullName(), protoReflectValue.Enum()))
		}

		return enumDesc.GoIdent, enumValue.GoIdent
	case protoreflect.BoolKind:
		result.GoName = "bool"
		val = protoReflectValue.Bool()
	case protoreflect.StringKind:
		result.GoName = "string"
		val = "\"" + protoReflectValue.String() + "\""
	case protoreflect.Int64Kind:
		result.GoName = "int64"
		val = protoReflectValue.Int()
	case protoreflect.Int32Kind:
		result.GoName = "int32"
		val = protoReflectValue.Int()
	case protoreflect.Uint64Kind:
		result.GoName = "uint64"
		val = protoReflectValue.Uint()
	case protoreflect.Uint32Kind:
		result.GoName = "uint32"
		val = protoReflectValue.Uint()
	case protoreflect.DoubleKind:
		result.GoName = "float64"
		val = protoReflectValue.Float()
	case protoreflect.FloatKind:
		result.GoName = "float32"
		val = protoReflectValue.Float()
	default:
		panic(fmt.Sprintf("unsupported kind for ref: %s, %s", protoGenField.Desc.FullName(), protoGenField.Desc.Kind().String()))
	}

	return result, val
}
