// Code generated by protoc-gen-go-mvp. DO NOT EDIT.
// versions:
// - protoc-gen-go-mvp v2.0.1
// created by mvp authors

package grpc

import (
	context "context"
	fmt "fmt"
	cntx "git.local/sensitive/mvp/pkg/cntx"
	ctxzap "github.com/grpc-ecosystem/go-grpc-middleware/logging/zap/ctxzap"
	zap "go.uber.org/zap"
	grpc "google.golang.org/grpc"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
)

func file_inner_processing_grpc_notification_proto_string_String(
	label string,
	in map[string]string,
) zap.Field {
	var res []zap.Field
	for key, val := range in {
		res = append(res, zap.Any(fmt.Sprint(key), val))
	}

	return zap.Dict(label, res...)
}

func file_inner_processing_grpc_notification_proto_string_String_1(
	label string,
	in map[string]string,
) zap.Field {
	var res []zap.Field
	for key, val := range in {
		res = append(res, zap.Any(fmt.Sprint(key), val))
	}

	return zap.Dict(label, res...)
}

func file_inner_processing_grpc_notification_proto_message_TimestampToZap(
	label string,
	in *timestamppb.Timestamp,
) zap.Field {
	if in == nil {
		return zap.Reflect(label, in)
	}

	return zap.Dict(
		label,
		zap.Any("Seconds", in.GetSeconds()),
		zap.Any("Nanos", in.GetNanos()),
	)
}

func file_inner_processing_grpc_notification_proto_message_GetMailReqDataV1ToZap(
	label string,
	in *GetMailReqDataV1,
) zap.Field {
	if in == nil {
		return zap.Reflect(label, in)
	}

	return zap.Dict(
		label,
		zap.Any("UserEmail", in.GetUserEmail()),
	)
}

func file_inner_processing_grpc_notification_proto_message_GetMailResDataV1ToZap(
	label string,
	in *GetMailResDataV1,
) zap.Field {
	if in == nil {
		return zap.Reflect(label, in)
	}

	return zap.Dict(
		label,
		zap.Any("Sender", in.GetSender()),
		zap.Any("Body", in.GetBody()),
		file_inner_processing_grpc_notification_proto_string_String("Payload", in.GetPayload()),
		zap.Any("Receivers", in.GetReceivers()),
		file_inner_processing_grpc_notification_proto_message_TimestampToZap("CreatedAt", in.GetCreatedAt()),
	)
}

func file_inner_processing_grpc_notification_proto_message_GetSMSReqDataV1ToZap(
	label string,
	in *GetSMSReqDataV1,
) zap.Field {
	if in == nil {
		return zap.Reflect(label, in)
	}

	return zap.Dict(
		label,
		zap.Any("UserPhone", in.GetUserPhone()),
	)
}

func file_inner_processing_grpc_notification_proto_message_GetSMSResDataV1ToZap(
	label string,
	in *GetSMSResDataV1,
) zap.Field {
	if in == nil {
		return zap.Reflect(label, in)
	}

	return zap.Dict(
		label,
		zap.Any("Sender", in.GetSender()),
		zap.Any("Body", in.GetBody()),
		file_inner_processing_grpc_notification_proto_string_String_1("Payload", in.GetPayload()),
		zap.Any("Receiver", in.GetReceiver()),
		file_inner_processing_grpc_notification_proto_message_TimestampToZap("CreatedAt", in.GetCreatedAt()),
	)
}

var _ NotificationServer = (*loggedNotificationServer)(nil)

func NewLoggedNotificationServer(srv NotificationServer) NotificationServer {
	return &loggedNotificationServer{srv: srv}
}

type loggedNotificationServer struct {
	UnimplementedNotificationServer

	srv NotificationServer
}

func (s *loggedNotificationServer) GetLastMailByUserEmail(
	ctx context.Context,
	request *GetMailReqDataV1,
) (
	response *GetMailResDataV1,
	err error,
) {
	label := cntx.Begin(ctx, "NotificationServer_GetLastMailByUserEmail")
	defer cntx.End(ctx, label)

	defer func() {
		if err != nil {
			ctxzap.AddFields(ctx, zap.Any(label+"error", err))
			return
		}

		ctxzap.AddFields(
			ctx,
			file_inner_processing_grpc_notification_proto_message_GetMailResDataV1ToZap(label+"response", response),
		)
	}()

	ctxzap.AddFields(
		ctx,
		file_inner_processing_grpc_notification_proto_message_GetMailReqDataV1ToZap(label+"request", request),
	)

	response, err = s.srv.GetLastMailByUserEmail(ctx, request)

	return
}

func (s *loggedNotificationServer) GetLastSMSByUserPhone(
	ctx context.Context,
	request *GetSMSReqDataV1,
) (
	response *GetSMSResDataV1,
	err error,
) {
	label := cntx.Begin(ctx, "NotificationServer_GetLastSMSByUserPhone")
	defer cntx.End(ctx, label)

	defer func() {
		if err != nil {
			ctxzap.AddFields(ctx, zap.Any(label+"error", err))
			return
		}

		ctxzap.AddFields(
			ctx,
			file_inner_processing_grpc_notification_proto_message_GetSMSResDataV1ToZap(label+"response", response),
		)
	}()

	ctxzap.AddFields(
		ctx,
		file_inner_processing_grpc_notification_proto_message_GetSMSReqDataV1ToZap(label+"request", request),
	)

	response, err = s.srv.GetLastSMSByUserPhone(ctx, request)

	return
}

var _ NotificationClient = (*loggedNotificationClient)(nil)

func NewLoggedNotificationClient(client NotificationClient) NotificationClient {
	return &loggedNotificationClient{client: client}
}

type loggedNotificationClient struct {
	client NotificationClient
}

func (s *loggedNotificationClient) GetLastMailByUserEmail(
	ctx context.Context,
	request *GetMailReqDataV1,
	opts ...grpc.CallOption,
) (
	response *GetMailResDataV1,
	err error,
) {
	label := cntx.Begin(ctx, "NotificationClient_GetLastMailByUserEmail")
	defer cntx.End(ctx, label)

	defer func() {
		if err != nil {
			ctxzap.AddFields(ctx, zap.Any(label+"error", err))
			return
		}

		ctxzap.AddFields(
			ctx,
			file_inner_processing_grpc_notification_proto_message_GetMailResDataV1ToZap(label+"response", response),
		)
	}()

	ctxzap.AddFields(
		ctx,
		file_inner_processing_grpc_notification_proto_message_GetMailReqDataV1ToZap(label+"request", request),
	)

	response, err = s.client.GetLastMailByUserEmail(ctx, request, opts...)

	return
}

func (s *loggedNotificationClient) GetLastSMSByUserPhone(
	ctx context.Context,
	request *GetSMSReqDataV1,
	opts ...grpc.CallOption,
) (
	response *GetSMSResDataV1,
	err error,
) {
	label := cntx.Begin(ctx, "NotificationClient_GetLastSMSByUserPhone")
	defer cntx.End(ctx, label)

	defer func() {
		if err != nil {
			ctxzap.AddFields(ctx, zap.Any(label+"error", err))
			return
		}

		ctxzap.AddFields(
			ctx,
			file_inner_processing_grpc_notification_proto_message_GetSMSResDataV1ToZap(label+"response", response),
		)
	}()

	ctxzap.AddFields(
		ctx,
		file_inner_processing_grpc_notification_proto_message_GetSMSReqDataV1ToZap(label+"request", request),
	)

	response, err = s.client.GetLastSMSByUserPhone(ctx, request, opts...)

	return
}
