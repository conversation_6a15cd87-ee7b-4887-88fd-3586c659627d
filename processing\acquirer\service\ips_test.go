package service

import (
	"context"
	"errors"
	"testing"

	"github.com/golang/mock/gomock"
	"github.com/stretchr/testify/require"

	"git.local/sensitive/innerpb/processing/goerr"
	"git.local/sensitive/pkg/middlewares"
	"git.local/sensitive/processing/acquirer/model"
	"git.local/sensitive/processing/acquirer/repository/database/databasemocks"
	"git.local/sensitive/processing/acquirer/schema"
)

func TestCreateIps(t *testing.T) {
	type createOp struct {
		isCalled  bool
		input     *model.Ips
		outputErr error
	}

	tests := []struct {
		name    string
		request *schema.Ips
		wantErr error
		create  createOp
	}{
		{
			name:    "nil request",
			request: nil,
			wantErr: goerr.ErrParseErrorBody,
		},
		{
			name: "error in func",
			request: &schema.Ips{
				Name: "some ips name",
			},
			wantErr: errors.New("some error"),
			create: createOp{
				isCalled: true,
				input: &model.Ips{
					Name: "some ips name",
				},
				outputErr: errors.New("some error"),
			},
		},
		{
			name: "success",
			request: &schema.Ips{
				Name: "some ips name",
			},
			wantErr: nil,
			create: createOp{
				isCalled: true,
				input: &model.Ips{
					Name: "some ips name",
				},
				outputErr: nil,
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctrl := gomock.NewController(t)
			defer ctrl.Finish()

			ipsDBMock := databasemocks.NewMockIpser(ctrl)

			ipsService := NewIpsService(ipsDBMock)

			if tt.create.isCalled {
				ipsDBMock.EXPECT().Create(
					gomock.Any(),
					tt.create.input,
				).Return(tt.create.outputErr).Times(1)
			}

			err := ipsService.Create(context.Background(), tt.request)
			if tt.wantErr != nil {
				require.Error(t, err)
				require.Equal(t, tt.wantErr, err)
			} else {
				require.NoError(t, err)
			}
		})
	}
}

func TestUpdateIps(t *testing.T) {
	type updateOp struct {
		isCalled  bool
		input     *model.Ips
		inputID   uint64
		outputErr error
	}

	tests := []struct {
		name    string
		req     *schema.Ips
		reqID   uint64
		wantErr error
		update  updateOp
	}{
		{
			name:    "nil request",
			req:     nil,
			wantErr: goerr.ErrParseErrorBody,
		},
		{
			name: "error update",
			req: &schema.Ips{
				Name: "some ips name",
			},
			reqID: 22,
			update: updateOp{
				isCalled: true,
				input: &model.Ips{
					Name: "some ips name",
				},
				inputID:   22,
				outputErr: errors.New("some error"),
			},
			wantErr: errors.New("some error"),
		},
		{
			name: "success",
			req: &schema.Ips{
				Name: "some ips name",
			},
			reqID: 22,
			update: updateOp{
				isCalled: true,
				input: &model.Ips{
					Name: "some ips name",
				},
				inputID:   22,
				outputErr: nil,
			},
			wantErr: nil,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctrl := gomock.NewController(t)
			defer ctrl.Finish()

			ipsDBMock := databasemocks.NewMockIpser(ctrl)

			ipsService := NewIpsService(ipsDBMock)

			if tt.update.isCalled {
				ipsDBMock.EXPECT().Update(
					gomock.Any(),
					tt.update.inputID,
					tt.update.input,
				).Return(tt.update.outputErr).Times(1)
			}

			err := ipsService.Update(context.Background(), tt.reqID, tt.req)
			if tt.wantErr != nil {
				require.Error(t, err)
				require.Equal(t, tt.wantErr, err)
			} else {
				require.NoError(t, err)
			}
		})
	}
}

func TestDeleteIps(t *testing.T) {
	type deleteOp struct {
		req       uint64
		outputErr error
	}

	tests := []struct {
		name    string
		req     uint64
		delete  deleteOp
		wantErr error
	}{
		{
			name:    "error",
			req:     22,
			wantErr: errors.New("some error"),
			delete: deleteOp{
				req:       22,
				outputErr: errors.New("some error"),
			},
		},
		{
			name:    "success",
			req:     22,
			wantErr: nil,
			delete: deleteOp{
				req:       22,
				outputErr: nil,
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctrl := gomock.NewController(t)
			defer ctrl.Finish()

			ipsDBMock := databasemocks.NewMockIpser(ctrl)

			ipsService := NewIpsService(ipsDBMock)

			ipsDBMock.EXPECT().Delete(gomock.Any(), tt.delete.req).Return(tt.delete.outputErr).Times(1)

			err := ipsService.Delete(context.Background(), tt.req)
			if tt.wantErr != nil {
				require.Error(t, err)
				require.Equal(t, tt.wantErr, err)
			} else {
				require.NoError(t, err)
			}
		})
	}
}

func TestGetAllIps(t *testing.T) {
	type getAllOp struct {
		input     *middlewares.PaginationInfo
		output    []*model.Ips
		outputErr error
	}

	tests := []struct {
		name    string
		req     *middlewares.PaginationInfo
		getAll  getAllOp
		want    []*model.Ips
		wantErr error
	}{
		{
			name: "error when getting",
			req: &middlewares.PaginationInfo{
				PerPage:    3,
				Page:       1,
				Pagination: true,
			},
			getAll: getAllOp{
				input: &middlewares.PaginationInfo{
					PerPage:    3,
					Page:       1,
					Pagination: true,
				},
				output:    nil,
				outputErr: errors.New("some error"),
			},
			want:    nil,
			wantErr: errors.New("some error"),
		},
		{
			name: "success",
			req: &middlewares.PaginationInfo{
				PerPage:    3,
				Page:       1,
				Pagination: true,
			},
			getAll: getAllOp{
				input: &middlewares.PaginationInfo{
					PerPage:    3,
					Page:       1,
					Pagination: true,
				},
				output: []*model.Ips{
					{
						ID:   22,
						Name: "some ips name",
					},
					{
						ID:   23,
						Name: "some another ips name",
					},
				},
				outputErr: nil,
			},
			want: []*model.Ips{
				{
					ID:   22,
					Name: "some ips name",
				},
				{
					ID:   23,
					Name: "some another ips name",
				},
			},
			wantErr: nil,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctrl := gomock.NewController(t)
			defer ctrl.Finish()

			ipsDBMock := databasemocks.NewMockIpser(ctrl)

			ipsService := NewIpsService(ipsDBMock)

			ipsDBMock.EXPECT().GetAll(
				gomock.Any(),
				tt.getAll.input,
			).Return(tt.getAll.output, tt.getAll.outputErr).Times(1)

			resp, err := ipsService.GetAll(context.Background(), tt.req)
			if tt.wantErr != nil {
				require.Error(t, err)
				require.Equal(t, tt.wantErr, err)
			} else {
				require.NoError(t, err)
			}
			require.Equal(t, tt.want, resp)
		})
	}
}

func TestGetByIDIps(t *testing.T) {
	type getByIDOp struct {
		input     uint64
		output    model.Ips
		outputErr error
	}

	tests := []struct {
		name    string
		req     uint64
		want    model.Ips
		wantErr error
		getByID getByIDOp
	}{
		{
			name:    "error",
			req:     22,
			wantErr: errors.New("some error"),
			want:    model.Ips{},
			getByID: getByIDOp{
				input:     22,
				output:    model.Ips{},
				outputErr: errors.New("some error"),
			},
		},
		{
			name:    "success",
			req:     22,
			wantErr: nil,
			want: model.Ips{
				ID:   23,
				Name: "some ips name",
			},
			getByID: getByIDOp{
				input: 22,
				output: model.Ips{
					ID:   23,
					Name: "some ips name",
				},
				outputErr: nil,
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctrl := gomock.NewController(t)
			defer ctrl.Finish()

			ipsDBMock := databasemocks.NewMockIpser(ctrl)

			ipsService := NewIpsService(ipsDBMock)

			ipsDBMock.EXPECT().GetByID(
				gomock.Any(),
				tt.getByID.input,
			).Return(tt.getByID.output, tt.getByID.outputErr).Times(1)

			resp, err := ipsService.GetByID(context.Background(), tt.req)
			if tt.wantErr != nil {
				require.Error(t, err)
				require.Equal(t, tt.wantErr, err)
			} else {
				require.NoError(t, err)
			}
			require.Equal(t, tt.want, resp)

		})
	}
}
