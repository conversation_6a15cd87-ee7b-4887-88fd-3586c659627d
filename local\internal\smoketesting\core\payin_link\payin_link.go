package payin_link

import (
	"net/http"

	"github.com/sethvargo/go-password/password"

	"git.local/sensitive/local/internal/domain"
)

func TestExpectedData() []domain.SmokeData[string] {
	return []domain.SmokeData[string]{
		{
			Name: "test_success_payin_link",
			Expected: domain.Expected[string]{
				HttpStatusCode: http.StatusOK,
			},
		},
		{
			Name: "test_fail_payin_link",
			Expected: domain.Expected[string]{
				HttpStatusCode: http.StatusBadRequest,
			},
		},
	}
}

func TestReqData() []domain.GenerateTransactionRequest {
	return []domain.GenerateTransactionRequest{
		{
			Amount:             10,
			ProjectID:          42,
			MerchantID:         1,
			ProjectClientID:    "kambar6122",
			FailureRedirectUrl: "https://www.youtube.com",
			SuccessRedirectUrl: "https://www.youtube.com",
			CallbackUrl:        "https://webhook.site/7fb9e488-8128-4bee-bfcd-f085f9f89422",
			Description:        "afk_afk_afk",
			ProjectReferenceID: "qambar" + password.MustGenerate(20, 5, 0, false, true),
		},
		{
			ProjectID:          42,
			MerchantID:         1,
			ProjectClientID:    "kambar6122",
			FailureRedirectUrl: "https://www.youtube.com",
			SuccessRedirectUrl: "https://www.youtube.com",
			CallbackUrl:        "https://webhook.site/7fb9e488-8128-4bee-bfcd-f085f9f89422",
			Description:        "afk_afk_afk",
			ProjectReferenceID: "qambar" + password.MustGenerate(20, 5, 0, false, true),
		},
	}
}
