edition = "2023";

package processing.integration.integration;

option go_package = "git.local/sensitive/innerpb/processing/grpc";

import "google/protobuf/descriptor.proto";
import "mvp/proto/refs.proto";

message IntegrationErrorRef {
  string message = 1;
}

extend google.protobuf.EnumValueOptions {
  IntegrationErrorRef integration_error_value = 100210;
}

extend google.protobuf.EnumOptions {
  IntegrationErrorRef default_integration_error_value = 100211;
}

enum IntegrationError {
  option(mvp.default_ref) = "default_integration_error_value";
  option(mvp.ref) = "integration_error_value";
  option(default_integration_error_value) = {
    message: "None"
  };

  None = 0 [(integration_error_value) = {
    message: "None"
  }];

  IncorrectCardExpDate = 117 [(integration_error_value) = {
    message: "incorrect card expiration date"
  }];

  InvalidCard = 102 [(integration_error_value) = {
    message: "invalid card"
  }];

  ExceedsAmountLimit = 103 [(integration_error_value) = {
    message: "exceeds amount limit"
  }];

  TransactionDeclinedByIssuer = 105 [(integration_error_value) = {
    message: "transaction declined by an issuer"
  }];

  UnavailableIssuer = 107 [(integration_error_value) = {
    message: "unavailable issuer"
  }];

  IncorrectCardNumber = 116 [(integration_error_value) = {
    message: "incorrect card number"
  }];

  PaymentForbiddenForMerchant = 109 [(integration_error_value) = {
    message: "payment is forbidden for the merchant"
  }];

  TransactionDeclinedByAcquirer = 106 [(integration_error_value) = {
    message: "transaction declined by an acquirer"
  }];

  UnavailableAcquirer = 108 [(integration_error_value) = {
    message: "unavailable acquirer"
  }];

  IncorrectCVVCVC = 115 [(integration_error_value) = {
    message: "incorrect CVV/CVC"
  }];

  ThreeDSAuthFailed = 101 [(integration_error_value) = {
    message: "3DS authentication failed"
  }];

  ExceedsTransactionFrequencyLimit = 104 [(integration_error_value) = {
    message: "exceeds transaction frequency limit"
  }];

  LostCard = 113 [(integration_error_value) = {
    message: "lost card"
  }];

  SuspiciousClient = 119 [(integration_error_value) = {
    message: "suspicious client"
  }];

  NonExistentCard = 112 [(integration_error_value) = {
    message: "non existent card"
  }];

  CardHasExpired = 114 [(integration_error_value) = {
    message: "card has expired"
  }];

  InsufficientFunds = 118 [(integration_error_value) = {
    message: "insufficient funds"
  }];

  UndefinedError = 100 [(integration_error_value) = {
    message: "undefined error"
  }];

  StolenCard = 110 [(integration_error_value) = {
    message: "stolen card"
  }];

  BlockedCard = 111 [(integration_error_value) = {
    message: "blocked card"
  }];

  UserDidNotPay = 120 [(integration_error_value) = {
    message: "user did not pay"
  }];

  InvalidThreeDSecureParameters = 121 [(integration_error_value) = {
    message: "invalid threeD secure parameters"
  }];
}
