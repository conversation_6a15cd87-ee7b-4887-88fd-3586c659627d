package model

import (
	"encoding/json"
	"git.local/sensitive/innerpb/processing/goerr"
	"google.golang.org/protobuf/types/known/structpb"
)

type TerminalStatus int

const (
	TerminalOn TerminalStatus = iota + 1
	TerminalOff
	TerminalGlobalOff
)

const terminalTableName = "acquirer.terminals"

type Terminal struct {
	TimestampMixin
	ID                   uint64            `gorm:"column:id" json:"id"`
	AcquirerID           uint64            `gorm:"column:acquirer_id" json:"acquirer_id"`
	Status               TerminalStatus    `gorm:"column:status;default:2" json:"status"`
	EncryptedConfig      string            `gorm:"column:encrypted_config" json:"-"`
	TwoStageTimeout      uint32            `gorm:"column:two_stage_timeout" json:"two_stage_timeout"`
	Acquirer             Acquirer          `gorm:"foreignKey:AcquirerID" json:"acquirer"`
	TerminalProjects     []TerminalProject `json:"terminal_projects"`
	AccountNumber        string            `gorm:"account_number;default:null" json:"account_number,omitempty"`
	IsTransit            bool              `gorm:"is_transit" json:"is_transit"`
	TransitBankID        uint64            `gorm:"column:transit_bank_id;default:null" json:"transit_bank_id"`
	Description          string            `gorm:"column:description;default:null" json:"description"`
	AcquirerTerminalName string            `gorm:"column:acquirer_terminal_name" json:"acquirer_terminal_name"`
}

func (t *Terminal) TableName() string {
	return terminalTableName
}

type Terminals []*Terminal

func (ts Terminals) TableName() string {
	return terminalTableName
}

func (t *Terminal) ToStruct() (*structpb.Struct, error) {
	decryptedConfig := map[string]interface{}{}

	if err := json.Unmarshal([]byte(t.EncryptedConfig), &decryptedConfig); err != nil {
		return nil, err
	}

	return structpb.NewStruct(decryptedConfig)
}

func (ts Terminals) UniqueAcquirerIDs() (uniqueAcquirerIDs []uint64) {
	seen := make(map[uint64]bool)
	for _, terminal := range ts {
		if !seen[terminal.AcquirerID] {
			uniqueAcquirerIDs = append(uniqueAcquirerIDs, terminal.AcquirerID)
			seen[terminal.AcquirerID] = true
		}
	}

	return uniqueAcquirerIDs
}

// GetTerminalByAcquirerID находит активный терминал по-заданному acquirerID.
// в данный момент возвращает первый подходящий, но в будущем будет логика балансировки между терминалами
func (ts Terminals) GetTerminalByAcquirerID(acquirerID uint64) (*Terminal, error) {
	for _, terminal := range ts {
		if terminal.AcquirerID == acquirerID {
			return terminal, nil
		}
	}

	return nil, goerr.ErrTerminalNotFound
}

type ResponsePaymentType struct {
	ProjectID         uint64 `gorm:"project_id" json:"project_id"`
	TransactionTypeID uint64 `gorm:"transaction_type_id" json:"payment_type"`
}
