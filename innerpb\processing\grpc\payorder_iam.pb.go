// Code generated by protoc-gen-go-mvp. DO NOT EDIT.
// versions:
// - protoc-gen-go-mvp v2.0.1
// created by mvp authors

package grpc

import (
	context "context"
	grpc "google.golang.org/grpc"
	metadata "google.golang.org/grpc/metadata"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
)

func NewIamPayorderServer(
	srv PayorderServer,
) PayorderServer {
	return &iamPayorderServer{
		srv: srv,
	}
}

var _ PayorderServer = (*iamPayorderServer)(nil)

type iamPayorderServer struct {
	UnimplementedPayorderServer

	srv PayorderServer
}

func (s *iamPayorderServer) StartCheckInProcessOrdersWorker(
	ctx context.Context,
	req *emptypb.Empty,
) (
	*emptypb.Empty,
	error,
) {
	return s.srv.StartCheckInProcessOrdersWorker(ctx, req)
}

func (s *iamPayorderServer) StartCreateNewPaymentOrdersWorker(
	ctx context.Context,
	req *emptypb.Empty,
) (
	*emptypb.Empty,
	error,
) {
	return s.srv.StartCreateNewPaymentOrdersWorker(ctx, req)
}

func NewIamPayorderClient(
	client PayorderClient,
) PayorderClient {
	return &iamPayorderClient{
		client: client,
	}
}

type iamPayorderClient struct {
	client PayorderClient
}

func (s *iamPayorderClient) StartCheckInProcessOrdersWorker(
	ctx context.Context,
	req *emptypb.Empty,
	opts ...grpc.CallOption,
) (
	*emptypb.Empty,
	error,
) {
	md, _ := metadata.FromIncomingContext(ctx)
	return s.client.StartCheckInProcessOrdersWorker(metadata.NewOutgoingContext(ctx, md), req)
}

func (s *iamPayorderClient) StartCreateNewPaymentOrdersWorker(
	ctx context.Context,
	req *emptypb.Empty,
	opts ...grpc.CallOption,
) (
	*emptypb.Empty,
	error,
) {
	md, _ := metadata.FromIncomingContext(ctx)
	return s.client.StartCreateNewPaymentOrdersWorker(metadata.NewOutgoingContext(ctx, md), req)
}
