package domain

type GenerateTransactionRequest struct {
	Amount                  float64           `json:"amount" validate:"gt=4,required"`
	ProjectID               uint64            `json:"project_id" validate:"required"`
	MerchantID              uint64            `json:"merchant_id" validate:"required"`
	Description             string            `json:"description" validate:"required"`
	ProjectClientID         string            `json:"project_client_id,omitempty"`
	IsHold                  *bool             `json:"is_hold,omitempty"`
	ProjectReferenceID      string            `json:"project_reference_id"`
	ProjectOrderID          string            `json:"project_order_id,omitempty"`
	CallbackUrl             string            `json:"callback_url,omitempty"`
	ConfirmUrl              string            `json:"confirm_url,omitempty"`
	SuccessRedirectUrl      string            `json:"success_redirect_url" validate:"required"`
	FailureRedirectUrl      string            `json:"failure_redirect_url" validate:"required"`
	Shipment                string            `json:"shipment,omitempty"`
	AdditionalData          map[string]string `json:"additional_data,omitempty"`
	ExternalUpperCommission float64           `json:"external_upper_commission,omitempty"`
}

type VCardLinkRequest struct {
	Description        string `json:"description" validate:"required"`
	FailureRedirectUrl string `json:"failure_redirect_url" validate:"required"`
	MerchantId         int    `json:"merchant_id" validate:"required"`
	ProjectClientID    string `json:"project_client_id" validate:"required"`
	ProjectId          int    `json:"project_id" validate:"required"`
	SuccessRedirectUrl string `json:"success_redirect_url" validate:"required"`
	CallbackUrl        string `json:"callback_url,omitempty"`
}

type MakePayInRequest struct {
	TransactionID   uint64 `json:"transaction_id" validate:"required"`
	TransactionHash string `json:"transaction_hash" validate:"required"`
	Save            bool   `json:"save"`
	UserEmail       string `json:"user_email,omitempty" validate:"omitempty,email"`
	UserPhone       string `json:"user_phone"`
	GetForm         bool   `json:"get_form" default:"false"`
	EncryptedCard   string `json:"encrypted_card"  validate:"required"`
}

type MakePayInResponse struct {
	TransactionID         uint64                    `json:"transaction_id" validate:"required"`
	TransactionStatusCode TransactionStatusCode     `json:"transaction_status_code" validate:"required"`
	ThreeDS               *PayInResponseThreeDS     `json:"three_ds"`
	Fingerprint           *PayInResponseFingerprint `json:"fingerprint"`
	AcquirerCode          string                    `json:"acquirer_code" validate:"required"`
	BankResponse          BankResponse
}

type TransactionStatusCode string

const (
	StatusNew                    TransactionStatusCode = "new"
	StatusThreeDsResponseWaiting TransactionStatusCode = "threeds_waiting"
	StatusSuccess                TransactionStatusCode = "success"
	StatusFailed                 TransactionStatusCode = "failed"
	StatusRefund                 TransactionStatusCode = "refund"
	StatusCanceled               TransactionStatusCode = "canceled"
	StatusHold                   TransactionStatusCode = "holded"
	StatusProcessed              TransactionStatusCode = "processed"
	StatusRetry                  TransactionStatusCode = "retry"
	StatusRefundWaiting          TransactionStatusCode = "refund_waiting"
	StatusError                  TransactionStatusCode = "error"
	StatusAuthorized             TransactionStatusCode = "authorized"
	StatusFingerprint            TransactionStatusCode = "fingerprint"
)

type PayInResponseThreeDS struct {
	Is3ds   bool                   `json:"is_3ds"`
	Params  map[string]interface{} `json:"params"`
	Action  string                 `json:"action"`
	TermUrl string                 `json:"termUrl"`
}

type PayInResponseFingerprint struct {
	MethodURL  string `json:"method_url"`
	MethodData string `json:"method_data"`
}

type BankResponse struct {
	Code                    string `json:"code"`
	Message                 string `json:"message"`
	IntegrationErrorCode    string `json:"integration_error_code"`
	IntegrationErrorMessage string `json:"integration_error_message"`
}
