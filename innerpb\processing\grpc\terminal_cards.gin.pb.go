// Code generated by protoc-gen-go-mvp. DO NOT EDIT.
// versions:
// - protoc-gen-go-mvp v2.0.1
// created by mvp authors

package grpc

import (
	dog "git.local/sensitive/sdk/dog"
	gin "github.com/gin-gonic/gin"
	metadata "google.golang.org/grpc/metadata"
	http "net/http"
)

func RegisterGinTerminalCardsRouterGroup(
	in *gin.RouterGroup,
	handler func(func(c *gin.Context) error) gin.HandlerFunc,
) error {
	service, err := NewGinTerminalCardsService()
	if err != nil {
		return err
	}

	routerGroup := in.Group("processing.acquirer.terminal_cards.TerminalCards")
	routerGroup.PUT("/GetCardInfoByPan", handler(service.GetCardInfoByPan))
	return nil
}

func NewGinTerminalCardsService() (GinTerminalCardsServer, error) {
	client, err := NewPreparedTerminalCardsClient()
	if err != nil {
		return nil, err
	}

	return &ginTerminalCardsServer{
		client: NewLoggedTerminalCardsClient(
			NewIamTerminalCardsClient(client),
		),
	}, nil
}

//go:generate mockgen -destination=./grpcmock/terminal_cards.gin.pb.go -package=grpcmock -source=terminal_cards.gin.pb.go GinTerminalCardsServer
type GinTerminalCardsServer interface {
	GetCardInfoByPan(c *gin.Context) error
}

var _ GinTerminalCardsServer = (*ginTerminalCardsServer)(nil)

type ginTerminalCardsServer struct {
	client TerminalCardsClient
}

type TerminalCards_GetCardInfoByPan_Success struct {
	// Status is the status of the response. Set true
	Status bool `json:"status"`
	// Message is a human-readable explanation of the status
	Message string `json:"message"`
	// StatusCode is the GoErr status. Set 0
	StatusCode int `json:"status_code"`
	// Result is the result of the request
	Result *GetCardInfoByPanResponseV1 `json:"result"`
}

type TerminalCards_GetCardInfoByPan_Failure struct {
	// Status is the status of the response. Set false
	Status bool `json:"status"`
	// Message is a human-readable explanation of the status
	Message string `json:"message"`
	// StatusCode is the GoErr status
	StatusCode int `json:"status_code"`
}

// GetCardInfoByPan
// @Summary GetCardInfoByPan Запрос на получение данных карты по номеру карты
// @Security bearerAuth
// @ID TerminalCards_GetCardInfoByPan
// @Accept json
// @Param request body GetCardInfoByPanRequestV1 true "GetCardInfoByPanRequestV1"
// @Success 200 {object} TerminalCards_GetCardInfoByPan_Success
// @Failure 401 {object} TerminalCards_GetCardInfoByPan_Failure "goerrpkg.ErrorTypeAuthorization"
// @Failure 404 {object} TerminalCards_GetCardInfoByPan_Failure "goerrpkg.ErrorTypeNotFound"
// @Failure 400 {object} TerminalCards_GetCardInfoByPan_Failure "goerrpkg.ErrorTypeBadRequest"
// @Failure 500 {object} TerminalCards_GetCardInfoByPan_Failure "goerrpkg.ErrorTypeUnexpectedError"
// @Failure 400 {object} TerminalCards_GetCardInfoByPan_Failure "gin.ErrorTypeBind"
// @Failure 500 {object} TerminalCards_GetCardInfoByPan_Failure "Undefined error"
// @Produce json
// @Router /processing.acquirer.terminal_cards.TerminalCards/GetCardInfoByPan [put]
// @tags TerminalCards
func (s *ginTerminalCardsServer) GetCardInfoByPan(c *gin.Context) error {
	ctx, span := dog.CreateSpan(c.Request.Context(), "GinTerminalCardsServer_GetCardInfoByPan")
	defer span.End()

	var request GetCardInfoByPanRequestV1
	if err := c.ShouldBindJSON(&request); err != nil {
		return err
	}

	ctx = metadata.NewOutgoingContext(ctx, metadata.MD(c.Request.Header))

	response, err := s.client.GetCardInfoByPan(ctx, &request)
	if err != nil {
		return err
	}

	c.JSON(http.StatusOK, &TerminalCards_GetCardInfoByPan_Success{
		Status:     true,
		Message:    "Success",
		StatusCode: 0,
		Result:     response,
	})
	return nil
}
