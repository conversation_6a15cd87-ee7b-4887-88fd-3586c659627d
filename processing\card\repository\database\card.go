package database

import (
	"context"
	"errors"

	"gorm.io/gorm"

	"git.local/sensitive/innerpb/processing/goerr"
	"git.local/sensitive/pkg/middlewares"
	"git.local/sensitive/processing/card/model"
	"git.local/sensitive/sdk/dog"
)

type CardsDb struct {
	db *gorm.DB
}

func NewCardsDb(
	db *gorm.DB,
) Carder {
	return &CardsDb{
		db: db,
	}
}

func (r *CardsDb) GetClientCards(
	ctx context.Context,
	pagination *middlewares.PaginationInfo,
	projectClientId string,
	projectId uint64,
) (cards model.Cards, err error) {
	ctx, span := dog.CreateSpan(ctx, "CardsDB_GetClientCards")
	defer span.End()

	query := r.db.WithContext(ctx).
		Model(&model.Card{}).
		Joins("INNER JOIN card.clients ON cards.client_id = clients.id").
		Where("clients.project_client_id = ?", projectClientId).
		Where("clients.project_id = ?", projectId).
		Order("cards.updated_at")

	if pagination != nil {
		if pagination.Pagination {
			query = query.Offset((pagination.Page - 1) * pagination.PerPage).Limit(pagination.PerPage)
		}
	}

	if err = query.Find(&cards).Error; err != nil {
		return nil, goerr.ErrDbUnexpected.WithErr(err).WithCtx(ctx)
	}

	return cards, nil
}

func (r *CardsDb) GetByClient(ctx context.Context, clientId uint64) (cards model.Cards, err error) {
	ctx, span := dog.CreateSpan(ctx, "CardsDB_GetByClient")
	defer span.End()

	if err = r.db.WithContext(ctx).
		Where("client_id = ?", clientId).
		Preload("Tokens").
		Find(&cards).
		Error; err != nil {
		return nil, goerr.ErrDbUnexpected.WithErr(err).WithMsgf("CardsDb_GetByClient").WithCtx(ctx)
	}

	return cards, nil
}

func (r *CardsDb) Create(ctx context.Context, card *model.Card) (err error) {
	ctx, span := dog.CreateSpan(ctx, "CardsDB_Create")
	defer span.End()

	if err = r.db.WithContext(ctx).Create(card).Error; err != nil {
		return goerr.ErrDbUnexpected.WithErr(err).WithCtx(ctx)
	}

	return nil
}

func (r *CardsDb) GetByIdAndClient(
	ctx context.Context,
	id uint64,
	clientId uint64,
) (_ model.Card, err error) {
	ctx, span := dog.CreateSpan(ctx, "CardsDB_GetByIdAndClient")
	defer span.End()

	var card model.Card
	if err = r.db.WithContext(ctx).
		Where("client_id = ?", clientId).
		Where("id = ?", id).
		Where("approved = ?", true).
		Preload("Tokens").
		First(&card).
		Error; err != nil {
		return model.Card{}, goerr.ErrDbUnexpected.WithErr(err).WithCtx(ctx)
	}

	return card, nil
}

func (r *CardsDb) GetByID(ctx context.Context, id uint64) (_ model.Card, err error) {
	ctx, span := dog.CreateSpan(ctx, "CardsDB_GetByID")
	defer span.End()

	var card model.Card

	if err = r.db.WithContext(ctx).
		Where("id = ?", id).
		Find(&card).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return model.Card{}, err
		}

		return model.Card{}, goerr.ErrDbUnexpected.WithErr(err).WithCtx(ctx)
	}

	return card, nil
}

func (r *CardsDb) GetAllNotExpired(ctx context.Context, afterID uint64, limit int) (_ []model.Card, err error) {
	ctx, span := dog.CreateSpan(ctx, "CardsDB_GetAllNotExpired")
	defer span.End()

	cards := make([]model.Card, 0)

	if err = r.db.
		WithContext(ctx).
		Model(&model.Card{}).
		Where("is_expired = ?", false).
		Where("id > ?", afterID).
		Order("id ASC").
		Limit(limit).
		Find(&cards).Error; err != nil {
		return nil, goerr.ErrDbUnexpected.WithErr(err).WithCtx(ctx)
	}

	return cards, nil
}
