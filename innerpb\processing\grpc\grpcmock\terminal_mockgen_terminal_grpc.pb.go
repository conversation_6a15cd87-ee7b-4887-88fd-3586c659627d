// Code generated by MockGen. DO NOT EDIT.
// Source: terminal_grpc.pb.go

// Package grpcmock is a generated GoMock package.
package grpcmock

import (
	context "context"
	reflect "reflect"

	grpc "git.local/sensitive/innerpb/processing/grpc"
	gomock "github.com/golang/mock/gomock"
	grpc0 "google.golang.org/grpc"
)

// MockTerminalClient is a mock of TerminalClient interface.
type MockTerminalClient struct {
	ctrl     *gomock.Controller
	recorder *MockTerminalClientMockRecorder
}

// MockTerminalClientMockRecorder is the mock recorder for MockTerminalClient.
type MockTerminalClientMockRecorder struct {
	mock *MockTerminalClient
}

// NewMockTerminalClient creates a new mock instance.
func NewMockTerminalClient(ctrl *gomock.Controller) *MockTerminalClient {
	mock := &MockTerminalClient{ctrl: ctrl}
	mock.recorder = &MockTerminalClientMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockTerminalClient) EXPECT() *MockTerminalClientMockRecorder {
	return m.recorder
}

// ExtendedSearchTerminal mocks base method.
func (m *MockTerminalClient) ExtendedSearchTerminal(ctx context.Context, in *grpc.ExtendedSearchTerminalReqDataV1, opts ...grpc0.CallOption) (*grpc.SearchTerminalResDataV1, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "ExtendedSearchTerminal", varargs...)
	ret0, _ := ret[0].(*grpc.SearchTerminalResDataV1)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ExtendedSearchTerminal indicates an expected call of ExtendedSearchTerminal.
func (mr *MockTerminalClientMockRecorder) ExtendedSearchTerminal(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ExtendedSearchTerminal", reflect.TypeOf((*MockTerminalClient)(nil).ExtendedSearchTerminal), varargs...)
}

// FindActiveTerminalsByProject mocks base method.
func (m *MockTerminalClient) FindActiveTerminalsByProject(ctx context.Context, in *grpc.ActiveTerminalsByProjectRequestV1, opts ...grpc0.CallOption) (*grpc.ActiveTerminalsByProjectResponseV1, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "FindActiveTerminalsByProject", varargs...)
	ret0, _ := ret[0].(*grpc.ActiveTerminalsByProjectResponseV1)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// FindActiveTerminalsByProject indicates an expected call of FindActiveTerminalsByProject.
func (mr *MockTerminalClientMockRecorder) FindActiveTerminalsByProject(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "FindActiveTerminalsByProject", reflect.TypeOf((*MockTerminalClient)(nil).FindActiveTerminalsByProject), varargs...)
}

// GetByTerminalID mocks base method.
func (m *MockTerminalClient) GetByTerminalID(ctx context.Context, in *grpc.TerminalRequestV1, opts ...grpc0.CallOption) (*grpc.TerminalResponseV1, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetByTerminalID", varargs...)
	ret0, _ := ret[0].(*grpc.TerminalResponseV1)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetByTerminalID indicates an expected call of GetByTerminalID.
func (mr *MockTerminalClientMockRecorder) GetByTerminalID(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetByTerminalID", reflect.TypeOf((*MockTerminalClient)(nil).GetByTerminalID), varargs...)
}

// GetPayInProjectTerminals mocks base method.
func (m *MockTerminalClient) GetPayInProjectTerminals(ctx context.Context, in *grpc.GetPayInProjectTerminalsReqV1, opts ...grpc0.CallOption) (*grpc.GetPayInProjectTerminalsResponseV1, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetPayInProjectTerminals", varargs...)
	ret0, _ := ret[0].(*grpc.GetPayInProjectTerminalsResponseV1)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetPayInProjectTerminals indicates an expected call of GetPayInProjectTerminals.
func (mr *MockTerminalClientMockRecorder) GetPayInProjectTerminals(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetPayInProjectTerminals", reflect.TypeOf((*MockTerminalClient)(nil).GetPayInProjectTerminals), varargs...)
}

// GetRuleByActiveTerminals mocks base method.
func (m *MockTerminalClient) GetRuleByActiveTerminals(ctx context.Context, in *grpc.RuleByActiveTerminalsReqV1, opts ...grpc0.CallOption) (*grpc.RuleByActiveTerminalsResponseV1, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetRuleByActiveTerminals", varargs...)
	ret0, _ := ret[0].(*grpc.RuleByActiveTerminalsResponseV1)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetRuleByActiveTerminals indicates an expected call of GetRuleByActiveTerminals.
func (mr *MockTerminalClientMockRecorder) GetRuleByActiveTerminals(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetRuleByActiveTerminals", reflect.TypeOf((*MockTerminalClient)(nil).GetRuleByActiveTerminals), varargs...)
}

// GetTerminalWithJusan mocks base method.
func (m *MockTerminalClient) GetTerminalWithJusan(ctx context.Context, in *grpc.SearchTerminalReqDataV1, opts ...grpc0.CallOption) (*grpc.GetTerminalWithJusanResponseV1, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetTerminalWithJusan", varargs...)
	ret0, _ := ret[0].(*grpc.GetTerminalWithJusanResponseV1)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetTerminalWithJusan indicates an expected call of GetTerminalWithJusan.
func (mr *MockTerminalClientMockRecorder) GetTerminalWithJusan(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetTerminalWithJusan", reflect.TypeOf((*MockTerminalClient)(nil).GetTerminalWithJusan), varargs...)
}

// GetTerminalsByProjectId mocks base method.
func (m *MockTerminalClient) GetTerminalsByProjectId(ctx context.Context, in *grpc.GetTerminalsByProjectIdRequestV1, opts ...grpc0.CallOption) (*grpc.ActiveTerminalsByProjectResponseV1, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetTerminalsByProjectId", varargs...)
	ret0, _ := ret[0].(*grpc.ActiveTerminalsByProjectResponseV1)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetTerminalsByProjectId indicates an expected call of GetTerminalsByProjectId.
func (mr *MockTerminalClientMockRecorder) GetTerminalsByProjectId(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetTerminalsByProjectId", reflect.TypeOf((*MockTerminalClient)(nil).GetTerminalsByProjectId), varargs...)
}

// SearchTerminal mocks base method.
func (m *MockTerminalClient) SearchTerminal(ctx context.Context, in *grpc.SearchTerminalReqDataV1, opts ...grpc0.CallOption) (*grpc.SearchTerminalResDataV1, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "SearchTerminal", varargs...)
	ret0, _ := ret[0].(*grpc.SearchTerminalResDataV1)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SearchTerminal indicates an expected call of SearchTerminal.
func (mr *MockTerminalClientMockRecorder) SearchTerminal(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SearchTerminal", reflect.TypeOf((*MockTerminalClient)(nil).SearchTerminal), varargs...)
}

// MockTerminalServer is a mock of TerminalServer interface.
type MockTerminalServer struct {
	ctrl     *gomock.Controller
	recorder *MockTerminalServerMockRecorder
}

// MockTerminalServerMockRecorder is the mock recorder for MockTerminalServer.
type MockTerminalServerMockRecorder struct {
	mock *MockTerminalServer
}

// NewMockTerminalServer creates a new mock instance.
func NewMockTerminalServer(ctrl *gomock.Controller) *MockTerminalServer {
	mock := &MockTerminalServer{ctrl: ctrl}
	mock.recorder = &MockTerminalServerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockTerminalServer) EXPECT() *MockTerminalServerMockRecorder {
	return m.recorder
}

// ExtendedSearchTerminal mocks base method.
func (m *MockTerminalServer) ExtendedSearchTerminal(arg0 context.Context, arg1 *grpc.ExtendedSearchTerminalReqDataV1) (*grpc.SearchTerminalResDataV1, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ExtendedSearchTerminal", arg0, arg1)
	ret0, _ := ret[0].(*grpc.SearchTerminalResDataV1)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ExtendedSearchTerminal indicates an expected call of ExtendedSearchTerminal.
func (mr *MockTerminalServerMockRecorder) ExtendedSearchTerminal(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ExtendedSearchTerminal", reflect.TypeOf((*MockTerminalServer)(nil).ExtendedSearchTerminal), arg0, arg1)
}

// FindActiveTerminalsByProject mocks base method.
func (m *MockTerminalServer) FindActiveTerminalsByProject(arg0 context.Context, arg1 *grpc.ActiveTerminalsByProjectRequestV1) (*grpc.ActiveTerminalsByProjectResponseV1, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "FindActiveTerminalsByProject", arg0, arg1)
	ret0, _ := ret[0].(*grpc.ActiveTerminalsByProjectResponseV1)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// FindActiveTerminalsByProject indicates an expected call of FindActiveTerminalsByProject.
func (mr *MockTerminalServerMockRecorder) FindActiveTerminalsByProject(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "FindActiveTerminalsByProject", reflect.TypeOf((*MockTerminalServer)(nil).FindActiveTerminalsByProject), arg0, arg1)
}

// GetByTerminalID mocks base method.
func (m *MockTerminalServer) GetByTerminalID(arg0 context.Context, arg1 *grpc.TerminalRequestV1) (*grpc.TerminalResponseV1, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetByTerminalID", arg0, arg1)
	ret0, _ := ret[0].(*grpc.TerminalResponseV1)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetByTerminalID indicates an expected call of GetByTerminalID.
func (mr *MockTerminalServerMockRecorder) GetByTerminalID(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetByTerminalID", reflect.TypeOf((*MockTerminalServer)(nil).GetByTerminalID), arg0, arg1)
}

// GetPayInProjectTerminals mocks base method.
func (m *MockTerminalServer) GetPayInProjectTerminals(arg0 context.Context, arg1 *grpc.GetPayInProjectTerminalsReqV1) (*grpc.GetPayInProjectTerminalsResponseV1, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetPayInProjectTerminals", arg0, arg1)
	ret0, _ := ret[0].(*grpc.GetPayInProjectTerminalsResponseV1)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetPayInProjectTerminals indicates an expected call of GetPayInProjectTerminals.
func (mr *MockTerminalServerMockRecorder) GetPayInProjectTerminals(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetPayInProjectTerminals", reflect.TypeOf((*MockTerminalServer)(nil).GetPayInProjectTerminals), arg0, arg1)
}

// GetRuleByActiveTerminals mocks base method.
func (m *MockTerminalServer) GetRuleByActiveTerminals(arg0 context.Context, arg1 *grpc.RuleByActiveTerminalsReqV1) (*grpc.RuleByActiveTerminalsResponseV1, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetRuleByActiveTerminals", arg0, arg1)
	ret0, _ := ret[0].(*grpc.RuleByActiveTerminalsResponseV1)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetRuleByActiveTerminals indicates an expected call of GetRuleByActiveTerminals.
func (mr *MockTerminalServerMockRecorder) GetRuleByActiveTerminals(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetRuleByActiveTerminals", reflect.TypeOf((*MockTerminalServer)(nil).GetRuleByActiveTerminals), arg0, arg1)
}

// GetTerminalWithJusan mocks base method.
func (m *MockTerminalServer) GetTerminalWithJusan(arg0 context.Context, arg1 *grpc.SearchTerminalReqDataV1) (*grpc.GetTerminalWithJusanResponseV1, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetTerminalWithJusan", arg0, arg1)
	ret0, _ := ret[0].(*grpc.GetTerminalWithJusanResponseV1)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetTerminalWithJusan indicates an expected call of GetTerminalWithJusan.
func (mr *MockTerminalServerMockRecorder) GetTerminalWithJusan(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetTerminalWithJusan", reflect.TypeOf((*MockTerminalServer)(nil).GetTerminalWithJusan), arg0, arg1)
}

// GetTerminalsByProjectId mocks base method.
func (m *MockTerminalServer) GetTerminalsByProjectId(arg0 context.Context, arg1 *grpc.GetTerminalsByProjectIdRequestV1) (*grpc.ActiveTerminalsByProjectResponseV1, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetTerminalsByProjectId", arg0, arg1)
	ret0, _ := ret[0].(*grpc.ActiveTerminalsByProjectResponseV1)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetTerminalsByProjectId indicates an expected call of GetTerminalsByProjectId.
func (mr *MockTerminalServerMockRecorder) GetTerminalsByProjectId(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetTerminalsByProjectId", reflect.TypeOf((*MockTerminalServer)(nil).GetTerminalsByProjectId), arg0, arg1)
}

// SearchTerminal mocks base method.
func (m *MockTerminalServer) SearchTerminal(arg0 context.Context, arg1 *grpc.SearchTerminalReqDataV1) (*grpc.SearchTerminalResDataV1, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SearchTerminal", arg0, arg1)
	ret0, _ := ret[0].(*grpc.SearchTerminalResDataV1)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SearchTerminal indicates an expected call of SearchTerminal.
func (mr *MockTerminalServerMockRecorder) SearchTerminal(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SearchTerminal", reflect.TypeOf((*MockTerminalServer)(nil).SearchTerminal), arg0, arg1)
}

// mustEmbedUnimplementedTerminalServer mocks base method.
func (m *MockTerminalServer) mustEmbedUnimplementedTerminalServer() {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "mustEmbedUnimplementedTerminalServer")
}

// mustEmbedUnimplementedTerminalServer indicates an expected call of mustEmbedUnimplementedTerminalServer.
func (mr *MockTerminalServerMockRecorder) mustEmbedUnimplementedTerminalServer() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "mustEmbedUnimplementedTerminalServer", reflect.TypeOf((*MockTerminalServer)(nil).mustEmbedUnimplementedTerminalServer))
}

// MockUnsafeTerminalServer is a mock of UnsafeTerminalServer interface.
type MockUnsafeTerminalServer struct {
	ctrl     *gomock.Controller
	recorder *MockUnsafeTerminalServerMockRecorder
}

// MockUnsafeTerminalServerMockRecorder is the mock recorder for MockUnsafeTerminalServer.
type MockUnsafeTerminalServerMockRecorder struct {
	mock *MockUnsafeTerminalServer
}

// NewMockUnsafeTerminalServer creates a new mock instance.
func NewMockUnsafeTerminalServer(ctrl *gomock.Controller) *MockUnsafeTerminalServer {
	mock := &MockUnsafeTerminalServer{ctrl: ctrl}
	mock.recorder = &MockUnsafeTerminalServerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockUnsafeTerminalServer) EXPECT() *MockUnsafeTerminalServerMockRecorder {
	return m.recorder
}

// mustEmbedUnimplementedTerminalServer mocks base method.
func (m *MockUnsafeTerminalServer) mustEmbedUnimplementedTerminalServer() {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "mustEmbedUnimplementedTerminalServer")
}

// mustEmbedUnimplementedTerminalServer indicates an expected call of mustEmbedUnimplementedTerminalServer.
func (mr *MockUnsafeTerminalServerMockRecorder) mustEmbedUnimplementedTerminalServer() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "mustEmbedUnimplementedTerminalServer", reflect.TypeOf((*MockUnsafeTerminalServer)(nil).mustEmbedUnimplementedTerminalServer))
}
