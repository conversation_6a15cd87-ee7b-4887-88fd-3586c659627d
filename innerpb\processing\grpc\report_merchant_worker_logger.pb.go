// Code generated by protoc-gen-go-mvp. DO NOT EDIT.
// versions:
// - protoc-gen-go-mvp v2.0.1
// created by mvp authors

package grpc

import (
	context "context"
	fmt "fmt"
	cntx "git.local/sensitive/mvp/pkg/cntx"
	ctxzap "github.com/grpc-ecosystem/go-grpc-middleware/logging/zap/ctxzap"
	zap "go.uber.org/zap"
	grpc "google.golang.org/grpc"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
)

func file_inner_processing_grpc_report_merchant_worker_proto_message_EmptyToZap(
	label string,
	in *emptypb.Empty,
) zap.Field {
	if in == nil {
		return zap.Reflect(label, in)
	}

	return zap.Dict(
		label,
	)
}

func file_inner_processing_grpc_report_merchant_worker_proto_message_ProcessReportScheduleByPeriodTypeRequestToZap(
	label string,
	in *ProcessReportScheduleByPeriodTypeRequest,
) zap.Field {
	if in == nil {
		return zap.Reflect(label, in)
	}

	return zap.Dict(
		label,
		file_inner_processing_grpc_report_merchant_worker_proto_enum_ReportSchedulePeriodTypeToZap("ReportSchedulePeriodType", in.GetReportSchedulePeriodType()),
	)
}

func file_inner_processing_grpc_report_merchant_worker_proto_enum_ReportSchedulePeriodTypeToZap(
	label string,
	in ReportSchedulePeriodType,
) zap.Field {
	str, ok := ReportSchedulePeriodType_name[int32(in)]
	if !ok {
		str = fmt.Sprintf("unknown ReportSchedulePeriodType value %d", in)
	}

	return zap.Dict(
		label,
		zap.Any("value", ReportSchedulePeriodType(in)),
		zap.Any("name", str),
	)
}

var _ ReportMerchantWorkerServer = (*loggedReportMerchantWorkerServer)(nil)

func NewLoggedReportMerchantWorkerServer(srv ReportMerchantWorkerServer) ReportMerchantWorkerServer {
	return &loggedReportMerchantWorkerServer{srv: srv}
}

type loggedReportMerchantWorkerServer struct {
	UnimplementedReportMerchantWorkerServer

	srv ReportMerchantWorkerServer
}

func (s *loggedReportMerchantWorkerServer) ProcessReportScheduleByPeriodType(
	ctx context.Context,
	request *ProcessReportScheduleByPeriodTypeRequest,
) (
	response *emptypb.Empty,
	err error,
) {
	label := cntx.Begin(ctx, "ReportMerchantWorkerServer_ProcessReportScheduleByPeriodType")
	defer cntx.End(ctx, label)

	defer func() {
		if err != nil {
			ctxzap.AddFields(ctx, zap.Any(label+"error", err))
			return
		}

		ctxzap.AddFields(
			ctx,
			file_inner_processing_grpc_report_merchant_worker_proto_message_EmptyToZap(label+"response", response),
		)
	}()

	ctxzap.AddFields(
		ctx,
		file_inner_processing_grpc_report_merchant_worker_proto_message_ProcessReportScheduleByPeriodTypeRequestToZap(label+"request", request),
	)

	response, err = s.srv.ProcessReportScheduleByPeriodType(ctx, request)

	return
}

var _ ReportMerchantWorkerClient = (*loggedReportMerchantWorkerClient)(nil)

func NewLoggedReportMerchantWorkerClient(client ReportMerchantWorkerClient) ReportMerchantWorkerClient {
	return &loggedReportMerchantWorkerClient{client: client}
}

type loggedReportMerchantWorkerClient struct {
	client ReportMerchantWorkerClient
}

func (s *loggedReportMerchantWorkerClient) ProcessReportScheduleByPeriodType(
	ctx context.Context,
	request *ProcessReportScheduleByPeriodTypeRequest,
	opts ...grpc.CallOption,
) (
	response *emptypb.Empty,
	err error,
) {
	label := cntx.Begin(ctx, "ReportMerchantWorkerClient_ProcessReportScheduleByPeriodType")
	defer cntx.End(ctx, label)

	defer func() {
		if err != nil {
			ctxzap.AddFields(ctx, zap.Any(label+"error", err))
			return
		}

		ctxzap.AddFields(
			ctx,
			file_inner_processing_grpc_report_merchant_worker_proto_message_EmptyToZap(label+"response", response),
		)
	}()

	ctxzap.AddFields(
		ctx,
		file_inner_processing_grpc_report_merchant_worker_proto_message_ProcessReportScheduleByPeriodTypeRequestToZap(label+"request", request),
	)

	response, err = s.client.ProcessReportScheduleByPeriodType(ctx, request, opts...)

	return
}
