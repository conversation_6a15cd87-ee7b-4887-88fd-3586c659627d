edition = "2023";

package processing.kalkan.kalkan;

option go_package = "git.local/sensitive/innerpb/processing/grpc";

service Kalkan {
  rpc MakeSignatureV1(MakeSignatureRequestV1) returns (MakeSignatureResponseV1) {}
}

// requests
message MakeSignatureRequestV1 {
  string payload = 1;
}


// responses
message MakeSignatureResponseV1 {
  message ServerResponse {
    string message = 1;
    string code = 2;
  }

  message Data {
    string signature = 1;
  }

  ServerResponse server_response = 1;
  Data data = 2;
}