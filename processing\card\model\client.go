package model

type Client struct {
	TimestampMixin
	Id                 uint64 `gorm:"column:id" json:"id"`
	ProjectId          uint64 `gorm:"column:project_id" json:"project_id"`
	ProjectClientId    string `gorm:"column:project_client_id" json:"project_client_id"`
	VerificationUserID uint64 `gorm:"column:verification_user_id" json:"verification_user_id"`
	IsBlocked          bool   `gorm:"is_blocked" json:"is_blocked"`
	Card               Card
	Cards              Cards
}

func (i Client) TableName() string {
	return "card.clients" // database_schema.table_name
}
