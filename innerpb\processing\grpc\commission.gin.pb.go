// Code generated by protoc-gen-go-mvp. DO NOT EDIT.
// versions:
// - protoc-gen-go-mvp v2.0.1
// created by mvp authors

package grpc

import (
	dog "git.local/sensitive/sdk/dog"
	gin "github.com/gin-gonic/gin"
	metadata "google.golang.org/grpc/metadata"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
	http "net/http"
)

func RegisterGinCommissionRouterGroup(
	in *gin.RouterGroup,
	handler func(func(c *gin.Context) error) gin.HandlerFunc,
) error {
	service, err := NewGinCommissionService()
	if err != nil {
		return err
	}

	routerGroup := in.Group("processing.commission.commission.Commission")
	routerGroup.PUT("/CalculateAndSaveUpperCommission", handler(service.CalculateAndSaveUpperCommission))
	routerGroup.PUT("/GetCommissionForMainBalance", handler(service.GetCommissionForMainBalance))
	routerGroup.PUT("/UpdateCommissionForCreditBalance", handler(service.UpdateCommissionForCreditBalance))
	routerGroup.PUT("/CalculatePayInPrimalAmount", handler(service.CalculatePayInPrimalAmount))
	routerGroup.PUT("/CalculatePayOutPrimalAmount", handler(service.CalculatePayOutPrimalAmount))
	routerGroup.PUT("/FiscalizeUpperCommission", handler(service.FiscalizeUpperCommission))
	routerGroup.PUT("/GetCommissionByTransactionID", handler(service.GetCommissionByTransactionID))
	return nil
}

func NewGinCommissionService() (GinCommissionServer, error) {
	client, err := NewPreparedCommissionClient()
	if err != nil {
		return nil, err
	}

	return &ginCommissionServer{
		client: NewLoggedCommissionClient(
			NewIamCommissionClient(client),
		),
	}, nil
}

//go:generate mockgen -destination=./grpcmock/commission.gin.pb.go -package=grpcmock -source=commission.gin.pb.go GinCommissionServer
type GinCommissionServer interface {
	CalculateAndSaveUpperCommission(c *gin.Context) error
	GetCommissionForMainBalance(c *gin.Context) error
	UpdateCommissionForCreditBalance(c *gin.Context) error
	CalculatePayInPrimalAmount(c *gin.Context) error
	CalculatePayOutPrimalAmount(c *gin.Context) error
	FiscalizeUpperCommission(c *gin.Context) error
	GetCommissionByTransactionID(c *gin.Context) error
}

var _ GinCommissionServer = (*ginCommissionServer)(nil)

type ginCommissionServer struct {
	client CommissionClient
}

type Commission_CalculateAndSaveUpperCommission_Success struct {
	// Status is the status of the response. Set true
	Status bool `json:"status"`
	// Message is a human-readable explanation of the status
	Message string `json:"message"`
	// StatusCode is the GoErr status. Set 0
	StatusCode int `json:"status_code"`
	// Result is the result of the request
	Result *CalculateAndSaveUpperCommissionResponseV1 `json:"result"`
}

type Commission_CalculateAndSaveUpperCommission_Failure struct {
	// Status is the status of the response. Set false
	Status bool `json:"status"`
	// Message is a human-readable explanation of the status
	Message string `json:"message"`
	// StatusCode is the GoErr status
	StatusCode int `json:"status_code"`
}

// CalculateAndSaveUpperCommission
// @Summary CalculateAndSaveUpperCommission
// @Security bearerAuth
// @ID Commission_CalculateAndSaveUpperCommission
// @Accept json
// @Param request body CalculateAndSaveUpperCommissionRequestV1 true "CalculateAndSaveUpperCommissionRequestV1"
// @Success 200 {object} Commission_CalculateAndSaveUpperCommission_Success
// @Failure 401 {object} Commission_CalculateAndSaveUpperCommission_Failure "goerrpkg.ErrorTypeAuthorization"
// @Failure 404 {object} Commission_CalculateAndSaveUpperCommission_Failure "goerrpkg.ErrorTypeNotFound"
// @Failure 400 {object} Commission_CalculateAndSaveUpperCommission_Failure "goerrpkg.ErrorTypeBadRequest"
// @Failure 500 {object} Commission_CalculateAndSaveUpperCommission_Failure "goerrpkg.ErrorTypeUnexpectedError"
// @Failure 400 {object} Commission_CalculateAndSaveUpperCommission_Failure "gin.ErrorTypeBind"
// @Failure 500 {object} Commission_CalculateAndSaveUpperCommission_Failure "Undefined error"
// @Produce json
// @Router /processing.commission.commission.Commission/CalculateAndSaveUpperCommission [put]
// @tags Commission
func (s *ginCommissionServer) CalculateAndSaveUpperCommission(c *gin.Context) error {
	ctx, span := dog.CreateSpan(c.Request.Context(), "GinCommissionServer_CalculateAndSaveUpperCommission")
	defer span.End()

	var request CalculateAndSaveUpperCommissionRequestV1
	if err := c.ShouldBindJSON(&request); err != nil {
		return err
	}

	ctx = metadata.NewOutgoingContext(ctx, metadata.MD(c.Request.Header))

	response, err := s.client.CalculateAndSaveUpperCommission(ctx, &request)
	if err != nil {
		return err
	}

	c.JSON(http.StatusOK, &Commission_CalculateAndSaveUpperCommission_Success{
		Status:     true,
		Message:    "Success",
		StatusCode: 0,
		Result:     response,
	})
	return nil
}

type Commission_GetCommissionForMainBalance_Success struct {
	// Status is the status of the response. Set true
	Status bool `json:"status"`
	// Message is a human-readable explanation of the status
	Message string `json:"message"`
	// StatusCode is the GoErr status. Set 0
	StatusCode int `json:"status_code"`
	// Result is the result of the request
	Result *GetCommissionForMainBalanceResponseV1 `json:"result"`
}

type Commission_GetCommissionForMainBalance_Failure struct {
	// Status is the status of the response. Set false
	Status bool `json:"status"`
	// Message is a human-readable explanation of the status
	Message string `json:"message"`
	// StatusCode is the GoErr status
	StatusCode int `json:"status_code"`
}

// GetCommissionForMainBalance
// @Summary GetCommissionForMainBalance
// @Security bearerAuth
// @ID Commission_GetCommissionForMainBalance
// @Accept json
// @Param request body GetCommissionForMainBalanceRequestV1 true "GetCommissionForMainBalanceRequestV1"
// @Success 200 {object} Commission_GetCommissionForMainBalance_Success
// @Failure 401 {object} Commission_GetCommissionForMainBalance_Failure "goerrpkg.ErrorTypeAuthorization"
// @Failure 404 {object} Commission_GetCommissionForMainBalance_Failure "goerrpkg.ErrorTypeNotFound"
// @Failure 400 {object} Commission_GetCommissionForMainBalance_Failure "goerrpkg.ErrorTypeBadRequest"
// @Failure 500 {object} Commission_GetCommissionForMainBalance_Failure "goerrpkg.ErrorTypeUnexpectedError"
// @Failure 400 {object} Commission_GetCommissionForMainBalance_Failure "gin.ErrorTypeBind"
// @Failure 500 {object} Commission_GetCommissionForMainBalance_Failure "Undefined error"
// @Produce json
// @Router /processing.commission.commission.Commission/GetCommissionForMainBalance [put]
// @tags Commission
func (s *ginCommissionServer) GetCommissionForMainBalance(c *gin.Context) error {
	ctx, span := dog.CreateSpan(c.Request.Context(), "GinCommissionServer_GetCommissionForMainBalance")
	defer span.End()

	var request GetCommissionForMainBalanceRequestV1
	if err := c.ShouldBindJSON(&request); err != nil {
		return err
	}

	ctx = metadata.NewOutgoingContext(ctx, metadata.MD(c.Request.Header))

	response, err := s.client.GetCommissionForMainBalance(ctx, &request)
	if err != nil {
		return err
	}

	c.JSON(http.StatusOK, &Commission_GetCommissionForMainBalance_Success{
		Status:     true,
		Message:    "Success",
		StatusCode: 0,
		Result:     response,
	})
	return nil
}

type Commission_UpdateCommissionForCreditBalance_Success struct {
	// Status is the status of the response. Set true
	Status bool `json:"status"`
	// Message is a human-readable explanation of the status
	Message string `json:"message"`
	// StatusCode is the GoErr status. Set 0
	StatusCode int `json:"status_code"`
	// Result is the result of the request
	Result *UpdateCommissionForCreditBalanceResponseV1 `json:"result"`
}

type Commission_UpdateCommissionForCreditBalance_Failure struct {
	// Status is the status of the response. Set false
	Status bool `json:"status"`
	// Message is a human-readable explanation of the status
	Message string `json:"message"`
	// StatusCode is the GoErr status
	StatusCode int `json:"status_code"`
}

// UpdateCommissionForCreditBalance
// @Summary UpdateCommissionForCreditBalance
// @Security bearerAuth
// @ID Commission_UpdateCommissionForCreditBalance
// @Accept json
// @Param request body UpdateCommissionForCreditBalanceRequestV1 true "UpdateCommissionForCreditBalanceRequestV1"
// @Success 200 {object} Commission_UpdateCommissionForCreditBalance_Success
// @Failure 401 {object} Commission_UpdateCommissionForCreditBalance_Failure "goerrpkg.ErrorTypeAuthorization"
// @Failure 404 {object} Commission_UpdateCommissionForCreditBalance_Failure "goerrpkg.ErrorTypeNotFound"
// @Failure 400 {object} Commission_UpdateCommissionForCreditBalance_Failure "goerrpkg.ErrorTypeBadRequest"
// @Failure 500 {object} Commission_UpdateCommissionForCreditBalance_Failure "goerrpkg.ErrorTypeUnexpectedError"
// @Failure 400 {object} Commission_UpdateCommissionForCreditBalance_Failure "gin.ErrorTypeBind"
// @Failure 500 {object} Commission_UpdateCommissionForCreditBalance_Failure "Undefined error"
// @Produce json
// @Router /processing.commission.commission.Commission/UpdateCommissionForCreditBalance [put]
// @tags Commission
func (s *ginCommissionServer) UpdateCommissionForCreditBalance(c *gin.Context) error {
	ctx, span := dog.CreateSpan(c.Request.Context(), "GinCommissionServer_UpdateCommissionForCreditBalance")
	defer span.End()

	var request UpdateCommissionForCreditBalanceRequestV1
	if err := c.ShouldBindJSON(&request); err != nil {
		return err
	}

	ctx = metadata.NewOutgoingContext(ctx, metadata.MD(c.Request.Header))

	response, err := s.client.UpdateCommissionForCreditBalance(ctx, &request)
	if err != nil {
		return err
	}

	c.JSON(http.StatusOK, &Commission_UpdateCommissionForCreditBalance_Success{
		Status:     true,
		Message:    "Success",
		StatusCode: 0,
		Result:     response,
	})
	return nil
}

type Commission_CalculatePayInPrimalAmount_Success struct {
	// Status is the status of the response. Set true
	Status bool `json:"status"`
	// Message is a human-readable explanation of the status
	Message string `json:"message"`
	// StatusCode is the GoErr status. Set 0
	StatusCode int `json:"status_code"`
	// Result is the result of the request
	Result *CalculatePrimalAmountResponseV1 `json:"result"`
}

type Commission_CalculatePayInPrimalAmount_Failure struct {
	// Status is the status of the response. Set false
	Status bool `json:"status"`
	// Message is a human-readable explanation of the status
	Message string `json:"message"`
	// StatusCode is the GoErr status
	StatusCode int `json:"status_code"`
}

// CalculatePayInPrimalAmount
// @Summary CalculatePayInPrimalAmount
// @Security bearerAuth
// @ID Commission_CalculatePayInPrimalAmount
// @Accept json
// @Param request body CalculatePrimalAmountRequestV1 true "CalculatePrimalAmountRequestV1"
// @Success 200 {object} Commission_CalculatePayInPrimalAmount_Success
// @Failure 401 {object} Commission_CalculatePayInPrimalAmount_Failure "goerrpkg.ErrorTypeAuthorization"
// @Failure 404 {object} Commission_CalculatePayInPrimalAmount_Failure "goerrpkg.ErrorTypeNotFound"
// @Failure 400 {object} Commission_CalculatePayInPrimalAmount_Failure "goerrpkg.ErrorTypeBadRequest"
// @Failure 500 {object} Commission_CalculatePayInPrimalAmount_Failure "goerrpkg.ErrorTypeUnexpectedError"
// @Failure 400 {object} Commission_CalculatePayInPrimalAmount_Failure "gin.ErrorTypeBind"
// @Failure 500 {object} Commission_CalculatePayInPrimalAmount_Failure "Undefined error"
// @Produce json
// @Router /processing.commission.commission.Commission/CalculatePayInPrimalAmount [put]
// @tags Commission
func (s *ginCommissionServer) CalculatePayInPrimalAmount(c *gin.Context) error {
	ctx, span := dog.CreateSpan(c.Request.Context(), "GinCommissionServer_CalculatePayInPrimalAmount")
	defer span.End()

	var request CalculatePrimalAmountRequestV1
	if err := c.ShouldBindJSON(&request); err != nil {
		return err
	}

	ctx = metadata.NewOutgoingContext(ctx, metadata.MD(c.Request.Header))

	response, err := s.client.CalculatePayInPrimalAmount(ctx, &request)
	if err != nil {
		return err
	}

	c.JSON(http.StatusOK, &Commission_CalculatePayInPrimalAmount_Success{
		Status:     true,
		Message:    "Success",
		StatusCode: 0,
		Result:     response,
	})
	return nil
}

type Commission_CalculatePayOutPrimalAmount_Success struct {
	// Status is the status of the response. Set true
	Status bool `json:"status"`
	// Message is a human-readable explanation of the status
	Message string `json:"message"`
	// StatusCode is the GoErr status. Set 0
	StatusCode int `json:"status_code"`
	// Result is the result of the request
	Result *CalculatePrimalAmountResponseV1 `json:"result"`
}

type Commission_CalculatePayOutPrimalAmount_Failure struct {
	// Status is the status of the response. Set false
	Status bool `json:"status"`
	// Message is a human-readable explanation of the status
	Message string `json:"message"`
	// StatusCode is the GoErr status
	StatusCode int `json:"status_code"`
}

// CalculatePayOutPrimalAmount
// @Summary CalculatePayOutPrimalAmount
// @Security bearerAuth
// @ID Commission_CalculatePayOutPrimalAmount
// @Accept json
// @Param request body CalculatePrimalAmountRequestV1 true "CalculatePrimalAmountRequestV1"
// @Success 200 {object} Commission_CalculatePayOutPrimalAmount_Success
// @Failure 401 {object} Commission_CalculatePayOutPrimalAmount_Failure "goerrpkg.ErrorTypeAuthorization"
// @Failure 404 {object} Commission_CalculatePayOutPrimalAmount_Failure "goerrpkg.ErrorTypeNotFound"
// @Failure 400 {object} Commission_CalculatePayOutPrimalAmount_Failure "goerrpkg.ErrorTypeBadRequest"
// @Failure 500 {object} Commission_CalculatePayOutPrimalAmount_Failure "goerrpkg.ErrorTypeUnexpectedError"
// @Failure 400 {object} Commission_CalculatePayOutPrimalAmount_Failure "gin.ErrorTypeBind"
// @Failure 500 {object} Commission_CalculatePayOutPrimalAmount_Failure "Undefined error"
// @Produce json
// @Router /processing.commission.commission.Commission/CalculatePayOutPrimalAmount [put]
// @tags Commission
func (s *ginCommissionServer) CalculatePayOutPrimalAmount(c *gin.Context) error {
	ctx, span := dog.CreateSpan(c.Request.Context(), "GinCommissionServer_CalculatePayOutPrimalAmount")
	defer span.End()

	var request CalculatePrimalAmountRequestV1
	if err := c.ShouldBindJSON(&request); err != nil {
		return err
	}

	ctx = metadata.NewOutgoingContext(ctx, metadata.MD(c.Request.Header))

	response, err := s.client.CalculatePayOutPrimalAmount(ctx, &request)
	if err != nil {
		return err
	}

	c.JSON(http.StatusOK, &Commission_CalculatePayOutPrimalAmount_Success{
		Status:     true,
		Message:    "Success",
		StatusCode: 0,
		Result:     response,
	})
	return nil
}

type Commission_FiscalizeUpperCommission_Success struct {
	// Status is the status of the response. Set true
	Status bool `json:"status"`
	// Message is a human-readable explanation of the status
	Message string `json:"message"`
	// StatusCode is the GoErr status. Set 0
	StatusCode int `json:"status_code"`
	// Result is the result of the request
	Result *emptypb.Empty `json:"result"`
}

type Commission_FiscalizeUpperCommission_Failure struct {
	// Status is the status of the response. Set false
	Status bool `json:"status"`
	// Message is a human-readable explanation of the status
	Message string `json:"message"`
	// StatusCode is the GoErr status
	StatusCode int `json:"status_code"`
}

// FiscalizeUpperCommission
// @Summary FiscalizeUpperCommission
// @Security bearerAuth
// @ID Commission_FiscalizeUpperCommission
// @Accept json
// @Param request body FiscalizeUpperCommissionRequestV1 true "FiscalizeUpperCommissionRequestV1"
// @Success 200 {object} Commission_FiscalizeUpperCommission_Success
// @Failure 401 {object} Commission_FiscalizeUpperCommission_Failure "goerrpkg.ErrorTypeAuthorization"
// @Failure 404 {object} Commission_FiscalizeUpperCommission_Failure "goerrpkg.ErrorTypeNotFound"
// @Failure 400 {object} Commission_FiscalizeUpperCommission_Failure "goerrpkg.ErrorTypeBadRequest"
// @Failure 500 {object} Commission_FiscalizeUpperCommission_Failure "goerrpkg.ErrorTypeUnexpectedError"
// @Failure 400 {object} Commission_FiscalizeUpperCommission_Failure "gin.ErrorTypeBind"
// @Failure 500 {object} Commission_FiscalizeUpperCommission_Failure "Undefined error"
// @Produce json
// @Router /processing.commission.commission.Commission/FiscalizeUpperCommission [put]
// @tags Commission
func (s *ginCommissionServer) FiscalizeUpperCommission(c *gin.Context) error {
	ctx, span := dog.CreateSpan(c.Request.Context(), "GinCommissionServer_FiscalizeUpperCommission")
	defer span.End()

	var request FiscalizeUpperCommissionRequestV1
	if err := c.ShouldBindJSON(&request); err != nil {
		return err
	}

	ctx = metadata.NewOutgoingContext(ctx, metadata.MD(c.Request.Header))

	response, err := s.client.FiscalizeUpperCommission(ctx, &request)
	if err != nil {
		return err
	}

	c.JSON(http.StatusOK, &Commission_FiscalizeUpperCommission_Success{
		Status:     true,
		Message:    "Success",
		StatusCode: 0,
		Result:     response,
	})
	return nil
}

type Commission_GetCommissionByTransactionID_Success struct {
	// Status is the status of the response. Set true
	Status bool `json:"status"`
	// Message is a human-readable explanation of the status
	Message string `json:"message"`
	// StatusCode is the GoErr status. Set 0
	StatusCode int `json:"status_code"`
	// Result is the result of the request
	Result *GetCommissionByTransactionIDResponseV1 `json:"result"`
}

type Commission_GetCommissionByTransactionID_Failure struct {
	// Status is the status of the response. Set false
	Status bool `json:"status"`
	// Message is a human-readable explanation of the status
	Message string `json:"message"`
	// StatusCode is the GoErr status
	StatusCode int `json:"status_code"`
}

// GetCommissionByTransactionID
// @Summary GetCommissionByTransactionID
// @Security bearerAuth
// @ID Commission_GetCommissionByTransactionID
// @Accept json
// @Param request body GetCommissionByTransactionIDRequestV1 true "GetCommissionByTransactionIDRequestV1"
// @Success 200 {object} Commission_GetCommissionByTransactionID_Success
// @Failure 401 {object} Commission_GetCommissionByTransactionID_Failure "goerrpkg.ErrorTypeAuthorization"
// @Failure 404 {object} Commission_GetCommissionByTransactionID_Failure "goerrpkg.ErrorTypeNotFound"
// @Failure 400 {object} Commission_GetCommissionByTransactionID_Failure "goerrpkg.ErrorTypeBadRequest"
// @Failure 500 {object} Commission_GetCommissionByTransactionID_Failure "goerrpkg.ErrorTypeUnexpectedError"
// @Failure 400 {object} Commission_GetCommissionByTransactionID_Failure "gin.ErrorTypeBind"
// @Failure 500 {object} Commission_GetCommissionByTransactionID_Failure "Undefined error"
// @Produce json
// @Router /processing.commission.commission.Commission/GetCommissionByTransactionID [put]
// @tags Commission
func (s *ginCommissionServer) GetCommissionByTransactionID(c *gin.Context) error {
	ctx, span := dog.CreateSpan(c.Request.Context(), "GinCommissionServer_GetCommissionByTransactionID")
	defer span.End()

	var request GetCommissionByTransactionIDRequestV1
	if err := c.ShouldBindJSON(&request); err != nil {
		return err
	}

	ctx = metadata.NewOutgoingContext(ctx, metadata.MD(c.Request.Header))

	response, err := s.client.GetCommissionByTransactionID(ctx, &request)
	if err != nil {
		return err
	}

	c.JSON(http.StatusOK, &Commission_GetCommissionByTransactionID_Success{
		Status:     true,
		Message:    "Success",
		StatusCode: 0,
		Result:     response,
	})
	return nil
}
