// Code generated by protoc-gen-go-mvp. DO NOT EDIT.
// versions:
// - protoc-gen-go-mvp v2.0.1
// created by mvp authors

package grpc

import (
	context "context"
	grpc "google.golang.org/grpc"
	metadata "google.golang.org/grpc/metadata"
)

func NewIamMultiacquiringBalanceServer(
	srv MultiacquiringBalanceServer,
) MultiacquiringBalanceServer {
	return &iamMultiacquiringBalanceServer{
		srv: srv,
	}
}

var _ MultiacquiringBalanceServer = (*iamMultiacquiringBalanceServer)(nil)

type iamMultiacquiringBalanceServer struct {
	UnimplementedMultiacquiringBalanceServer

	srv MultiacquiringBalanceServer
}

func (s *iamMultiacquiringBalanceServer) CheckBalance(
	ctx context.Context,
	req *CheckBalanceRequest,
) (
	*CheckBalanceResponse,
	error,
) {
	return s.srv.CheckBalance(ctx, req)
}

func NewIamMultiacquiringBalanceClient(
	client MultiacquiringBalanceClient,
) MultiacquiringBalanceClient {
	return &iamMultiacquiringBalanceClient{
		client: client,
	}
}

type iamMultiacquiringBalanceClient struct {
	client MultiacquiringBalanceClient
}

func (s *iamMultiacquiringBalanceClient) CheckBalance(
	ctx context.Context,
	req *CheckBalanceRequest,
	opts ...grpc.CallOption,
) (
	*CheckBalanceResponse,
	error,
) {
	md, _ := metadata.FromIncomingContext(ctx)
	return s.client.CheckBalance(metadata.NewOutgoingContext(ctx, md), req)
}
