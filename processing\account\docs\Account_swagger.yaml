basePath: /account
definitions:
  delivery_http_v1.getAccounts.response:
    properties:
      account_number:
        type: string
      id:
        type: integer
    required:
    - account_number
    - id
    type: object
  delivery_http_v1.getAccountsWithBalance.response:
    properties:
      account_number:
        type: string
      amount:
        type: number
      bank:
        properties:
          bank_id:
            type: string
          bank_name:
            type: string
        required:
        - bank_id
        - bank_name
        type: object
      config:
        additionalProperties: {}
        type: object
      id:
        type: integer
    required:
    - account_number
    - amount
    - bank
    - id
    type: object
  middlewares.Empty:
    type: object
  middlewares.Response-array_delivery_http_v1_getAccounts_response:
    properties:
      message:
        type: string
      result:
        items:
          $ref: '#/definitions/delivery_http_v1.getAccounts.response'
        type: array
      status:
        type: boolean
    required:
    - message
    - result
    - status
    type: object
  middlewares.Response-array_delivery_http_v1_getAccountsWithBalance_response:
    properties:
      message:
        type: string
      result:
        items:
          $ref: '#/definitions/delivery_http_v1.getAccountsWithBalance.response'
        type: array
      status:
        type: boolean
    required:
    - message
    - result
    - status
    type: object
  middlewares.Response-array_model_Transfer:
    properties:
      message:
        type: string
      result:
        items:
          $ref: '#/definitions/model.Transfer'
        type: array
      status:
        type: boolean
    required:
    - message
    - result
    - status
    type: object
  middlewares.Response-array_model_TransferStatus:
    properties:
      message:
        type: string
      result:
        items:
          $ref: '#/definitions/model.TransferStatus'
        type: array
      status:
        type: boolean
    required:
    - message
    - result
    - status
    type: object
  middlewares.Response-array_model_TransferType:
    properties:
      message:
        type: string
      result:
        items:
          $ref: '#/definitions/model.TransferType'
        type: array
      status:
        type: boolean
    required:
    - message
    - result
    - status
    type: object
  middlewares.Response-middlewares_Empty:
    properties:
      message:
        type: string
      result:
        $ref: '#/definitions/middlewares.Empty'
      status:
        type: boolean
    required:
    - message
    - result
    - status
    type: object
  middlewares.Response-model_Transfer:
    properties:
      message:
        type: string
      result:
        $ref: '#/definitions/model.Transfer'
      status:
        type: boolean
    required:
    - message
    - result
    - status
    type: object
  middlewares.Response-schema_AcceptTransferResponse:
    properties:
      message:
        type: string
      result:
        $ref: '#/definitions/schema.AcceptTransferResponse'
      status:
        type: boolean
    required:
    - message
    - result
    - status
    type: object
  middlewares.Response-schema_AccountAcquirer:
    properties:
      message:
        type: string
      result:
        $ref: '#/definitions/schema.AccountAcquirer'
      status:
        type: boolean
    required:
    - message
    - result
    - status
    type: object
  middlewares.Response-schema_AccountBalance:
    properties:
      message:
        type: string
      result:
        $ref: '#/definitions/schema.AccountBalance'
      status:
        type: boolean
    required:
    - message
    - result
    - status
    type: object
  middlewares.Response-schema_CancelTransferResponse:
    properties:
      message:
        type: string
      result:
        $ref: '#/definitions/schema.CancelTransferResponse'
      status:
        type: boolean
    required:
    - message
    - result
    - status
    type: object
  middlewares.Response-schema_CreateTransferResponse:
    properties:
      message:
        type: string
      result:
        $ref: '#/definitions/schema.CreateTransferResponse'
      status:
        type: boolean
    required:
    - message
    - result
    - status
    type: object
  middlewares.Response-schema_DecryptedAccountConfig:
    properties:
      message:
        type: string
      result:
        $ref: '#/definitions/schema.DecryptedAccountConfig'
      status:
        type: boolean
    required:
    - message
    - result
    - status
    type: object
  middlewares.Response-schema_TransferResponse:
    properties:
      message:
        type: string
      result:
        $ref: '#/definitions/schema.TransferResponse'
      status:
        type: boolean
    required:
    - message
    - result
    - status
    type: object
  middlewares.Response-string:
    properties:
      message:
        type: string
      result:
        type: string
      status:
        type: boolean
    required:
    - message
    - result
    - status
    type: object
  model.Transfer:
    properties:
      account_id:
        type: integer
      acquirer_id:
        type: integer
      amount:
        type: number
      bank_order_id:
        type: string
      bank_reference_id:
        type: string
      country_code_id:
        type: integer
      created_at:
        type: string
      description:
        type: string
      entity_type_id:
        type: integer
      finished_at:
        type: string
      foreign_id:
        type: string
      id:
        type: integer
      merchant_account:
        type: string
      merchant_beneficiary_сode:
        type: string
      merchant_bin:
        type: string
      merchant_id:
        type: integer
      payment_purpose_code:
        type: string
      project_id:
        type: integer
      status_id:
        type: integer
      transfer_status:
        $ref: '#/definitions/model.TransferStatus'
      transfer_type:
        $ref: '#/definitions/model.TransferType'
      transfer_type_id:
        type: integer
      updated_at:
        type: string
    type: object
  model.TransferStatus:
    properties:
      code:
        $ref: '#/definitions/model.TransferStatusCode'
      created_at:
        type: string
      id:
        type: integer
      name:
        type: string
      updated_at:
        type: string
    type: object
  model.TransferStatusCode:
    enum:
    - new
    - authorized
    - success
    - canceled
    - failed
    - holded
    - error
    - in_progress
    - in_redo
    type: string
    x-enum-varnames:
    - StatusNew
    - StatusAuthorized
    - StatusSuccess
    - StatusCanceled
    - StatusFailed
    - StatusHold
    - StatusError
    - StatusInProgress
    - StatusInRedo
  model.TransferType:
    properties:
      code:
        $ref: '#/definitions/model.TransferTypeCode'
      created_at:
        type: string
      id:
        type: integer
      name:
        type: string
      updated_at:
        type: string
    type: object
  model.TransferTypeCode:
    enum:
    - in
    - out
    type: string
    x-enum-varnames:
    - TypeIn
    - TypeOut
  schema.AcceptTransferRequest:
    properties:
      transfer_ids:
        items:
          type: integer
        type: array
    required:
    - transfer_ids
    type: object
  schema.AcceptTransferResponse:
    properties:
      responses:
        items:
          $ref: '#/definitions/schema.TransferResponse'
        type: array
    type: object
  schema.AccountAcquirer:
    properties:
      bank_codes:
        items:
          type: string
        type: array
    type: object
  schema.AccountBalance:
    properties:
      balance:
        type: number
    type: object
  schema.CancelTransferRequest:
    properties:
      transfer_ids:
        items:
          type: integer
        type: array
    required:
    - transfer_ids
    type: object
  schema.CancelTransferResponse:
    properties:
      responses:
        items:
          $ref: '#/definitions/schema.TransferResponse'
        type: array
    type: object
  schema.CreateTransferRequest:
    properties:
      account_number:
        type: string
      balance_owner_bin:
        type: string
      balance_owner_id:
        type: integer
      beneficiary_code:
        type: string
      description:
        type: string
      payment_purpose_code:
        type: string
      recipient_account:
        type: string
      transfer_amount:
        type: number
    required:
    - account_number
    - balance_owner_bin
    - balance_owner_id
    - beneficiary_code
    - description
    - payment_purpose_code
    - recipient_account
    - transfer_amount
    type: object
  schema.CreateTransferResponse:
    properties:
      bank_order_id:
        type: string
      bank_reference_id:
        type: string
      status_id:
        type: integer
      transfer_id:
        type: integer
    type: object
  schema.DecryptedAccountConfig:
    additionalProperties: {}
    type: object
  schema.RedoTransferRequest:
    properties:
      merchant_id:
        type: integer
      project_id:
        type: integer
      transfer_id:
        type: integer
      transit_account:
        type: string
    type: object
  schema.TransferResponse:
    properties:
      status_id:
        type: integer
      transfer_id:
        type: integer
    type: object
host: api-dev.processing.kz
info:
  contact: {}
  description: account processing
  title: account
  version: 1.0.0
paths:
  /api/v1/manager/account/balance/{number}:
    get:
      description: Получение баланса транзитного счета по его номеру
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/middlewares.Response-schema_AccountBalance'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/middlewares.Response-middlewares_Empty'
      security:
      - bearerAuth: []
      summary: Получение баланса счета по номеру
      tags:
      - Get Account Balance By Number
  /api/v1/manager/accounts:
    get:
      description: Получение информации о всех транзитных счетах
      parameters:
      - description: bank_id
        in: query
        name: bank_id
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/middlewares.Response-array_delivery_http_v1_getAccounts_response'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/middlewares.Response-middlewares_Empty'
      security:
      - bearerAuth: []
      summary: Получение всех счетов
      tags:
      - Get All Accounts
  /api/v1/manager/accounts/{id}/config:
    get:
      consumes:
      - application/json
      parameters:
      - description: Account ID
        in: path
        name: id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/middlewares.Response-schema_DecryptedAccountConfig'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/middlewares.Response-middlewares_Empty'
      summary: Получение конфигурации аккаунта в расшифрованном виде
      tags:
      - Get Account Config
    patch:
      consumes:
      - application/json
      parameters:
      - description: Key and Value
        in: body
        name: data
        required: true
        schema:
          additionalProperties: true
          type: object
      - description: Account ID
        in: path
        name: id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/middlewares.Response-string'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/middlewares.Response-middlewares_Empty'
      summary: Сохранение конфигурации аккаунта в зашифрованном виде
      tags:
      - Add Account Config
  /api/v1/manager/accounts/acquirers:
    get:
      description: Получение всех банков-эквайеров аккаунта
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/middlewares.Response-schema_AccountAcquirer'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/middlewares.Response-middlewares_Empty'
      security:
      - bearerAuth: []
      summary: Получение всех банков-эквайеров аккаунта
      tags:
      - Get All Account Acquirers
  /api/v1/manager/accounts/with-balance:
    get:
      description: Получение информации о всех транзитных счетах
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/middlewares.Response-array_delivery_http_v1_getAccountsWithBalance_response'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/middlewares.Response-middlewares_Empty'
      security:
      - bearerAuth: []
      summary: Получение всех счетов
      tags:
      - Get All Accounts
  /api/v1/manager/transfer/{id}:
    get:
      description: Получение в CRM информации о заказе на вывод по ID
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/middlewares.Response-model_Transfer'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/middlewares.Response-middlewares_Empty'
      security:
      - bearerAuth: []
      summary: Получение в CRM заказа по ID
      tags:
      - Get Order By ID in CRM
  /api/v1/manager/transfer/{id}/accept:
    post:
      consumes:
      - application/json
      description: Подтверждение заказа
      operationId: PostAcceptTransferByID
      parameters:
      - description: TransactionID
        in: path
        name: id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/middlewares.Response-schema_TransferResponse'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/middlewares.Response-middlewares_Empty'
      security:
      - bearerAuth: []
      summary: Подтверждение заказа
      tags:
      - Accept transfer CRM
  /api/v1/manager/transfer/{id}/cancel:
    post:
      consumes:
      - application/json
      description: Отклонение заказа
      operationId: PostCancelTransferByID
      parameters:
      - description: transferID
        in: path
        name: id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/middlewares.Response-schema_TransferResponse'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/middlewares.Response-middlewares_Empty'
      security:
      - bearerAuth: []
      summary: Отклонение заказа
      tags:
      - Cancel transfer CRM
  /api/v1/manager/transfer/accept:
    post:
      consumes:
      - application/json
      description: Подтверждение заказов в CRM на вывод средств с транзитного счета
        на расчетный счет мерчанта
      operationId: PostAcceptTransfers
      parameters:
      - description: AcceptTransferRequest Data
        in: body
        name: data
        required: true
        schema:
          $ref: '#/definitions/schema.AcceptTransferRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/middlewares.Response-schema_AcceptTransferResponse'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/middlewares.Response-middlewares_Empty'
      security:
      - bearerAuth: []
      summary: Подтверждение заказов в CRM
      tags:
      - Accept transfer CRM
  /api/v1/manager/transfer/cancel:
    post:
      consumes:
      - application/json
      description: Отклонение заказов в CRM на вывод средств с транзитного счета на
        расчетный счет мерчанта
      operationId: PostCancelTransfers
      parameters:
      - description: CancelTransferRequest Data
        in: body
        name: data
        required: true
        schema:
          $ref: '#/definitions/schema.CancelTransferRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/middlewares.Response-schema_CancelTransferResponse'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/middlewares.Response-middlewares_Empty'
      security:
      - bearerAuth: []
      summary: Отклонение заказов в CRM
      tags:
      - Cancel transfer CRM
  /api/v1/manager/transfer/create:
    post:
      consumes:
      - application/json
      description: Создание нового заказа в CRM на вывод средств с транзитного счета
        на расчетный счет мерчанта
      parameters:
      - description: CreateTransferRequest Data
        in: body
        name: data
        required: true
        schema:
          $ref: '#/definitions/schema.CreateTransferRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/middlewares.Response-schema_CreateTransferResponse'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/middlewares.Response-middlewares_Empty'
      security:
      - bearerAuth: []
      summary: Создание заказа в CRM
      tags:
      - Create transfer CRM
  /api/v1/manager/transfer/filtered:
    get:
      description: Получение в CRM информации о заказах на вывод по фильтру
      parameters:
      - in: query
        name: account_number
        type: string
      - in: query
        name: amount_from
        type: string
      - in: query
        name: amount_to
        type: string
      - in: query
        name: date_from
        type: string
      - in: query
        name: date_to
        type: string
      - in: query
        name: merchant_id
        type: string
      - in: query
        name: project_id
        type: string
      - in: query
        name: status_id
        type: string
      - in: query
        name: type_code
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/middlewares.Response-array_model_Transfer'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/middlewares.Response-middlewares_Empty'
      security:
      - bearerAuth: []
      summary: Получение в CRM заказов по фильтру
      tags:
      - Get transfers By Filters in CRM
  /api/v1/manager/transfer/merchant/{id}:
    get:
      description: Получение в CRM информации о всех заказах на вывод по ID мерчанта
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/middlewares.Response-array_model_Transfer'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/middlewares.Response-middlewares_Empty'
      security:
      - bearerAuth: []
      summary: Получение в CRM заказов по ID мерчанта
      tags:
      - Get transfers By Merchant ID in CRM
  /api/v1/manager/transfer/project/{id}:
    get:
      description: Получение в CRM информации о заказах на вывод по ID проекта
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/middlewares.Response-array_model_Transfer'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/middlewares.Response-middlewares_Empty'
      security:
      - bearerAuth: []
      summary: Получение в CRM заказов по ID проекта
      tags:
      - Get transfers By Project ID in CRM
  /api/v1/manager/transfer/redo:
    post:
      consumes:
      - application/json
      description: Переотправка заказа через CRM на вывод средств с транзитного счета
        на расчетный счет мерчанта
      parameters:
      - description: RedoTransferRequest Data
        in: body
        name: data
        required: true
        schema:
          $ref: '#/definitions/schema.RedoTransferRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/middlewares.Response-schema_TransferResponse'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/middlewares.Response-middlewares_Empty'
      security:
      - bearerAuth: []
      summary: Переотправка заказа через CRM
      tags:
      - Redo transfer CRM
  /api/v1/merchant/transfer:
    get:
      description: Получение мерчантом информации о всех его заказах на вывод
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/middlewares.Response-array_model_Transfer'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/middlewares.Response-middlewares_Empty'
      security:
      - bearerAuth: []
      summary: Получение мерчантом всех его заказов
      tags:
      - Get Merchant's transfers
  /api/v1/merchant/transfer/{id}:
    get:
      description: Получение мерчантом информации о его заказе на вывод по ID
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/middlewares.Response-model_Transfer'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/middlewares.Response-middlewares_Empty'
      security:
      - bearerAuth: []
      summary: Получение мерчантом его заказа по ID
      tags:
      - Get Merchant's Transfer By ID
  /api/v1/merchant/transfer/create:
    post:
      consumes:
      - application/json
      description: Создание мерчантом нового заказа на вывод средств с транзитного
        счета на свой расчетный счет
      parameters:
      - description: CreateTransferRequest Data
        in: body
        name: data
        required: true
        schema:
          $ref: '#/definitions/schema.CreateTransferRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/middlewares.Response-schema_CreateTransferResponse'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/middlewares.Response-middlewares_Empty'
      security:
      - bearerAuth: []
      summary: Создание заказа в ЛК мерчанта
      tags:
      - Create transfer Merchant
  /api/v1/merchant/transfer/filtered/:
    get:
      description: Получение мерчантом информации о его заказах на вывод по фильтру
      parameters:
      - in: query
        name: account_number
        type: string
      - in: query
        name: amount_from
        type: string
      - in: query
        name: amount_to
        type: string
      - in: query
        name: date_from
        type: string
      - in: query
        name: date_to
        type: string
      - in: query
        name: merchant_id
        type: string
      - in: query
        name: project_id
        type: string
      - in: query
        name: status_id
        type: string
      - in: query
        name: type_code
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/middlewares.Response-array_model_Transfer'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/middlewares.Response-middlewares_Empty'
      security:
      - bearerAuth: []
      summary: Получение мерчантом его заказов по фильтру
      tags:
      - Get Merchant's Transfers By Filters
  /api/v1/merchant/transfer/project/{id}:
    get:
      description: Получение мерчантом информации о его заказах на вывод по ID проекта
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/middlewares.Response-array_model_Transfer'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/middlewares.Response-middlewares_Empty'
      security:
      - bearerAuth: []
      summary: Получение мерчантом его заказов по ID проекта
      tags:
      - Get Merchant's transfers By Project ID
  /api/v1/transfer/statuses:
    get:
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/middlewares.Response-array_model_TransferStatus'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/middlewares.Response-middlewares_Empty'
      security:
      - bearerAuth: []
      summary: Получение всех статусов трансферов
      tags:
      - transfer-statuses
  /api/v1/transfer/types:
    get:
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/middlewares.Response-array_model_TransferType'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/middlewares.Response-middlewares_Empty'
      security:
      - bearerAuth: []
      summary: Получение всех типов трансферов
      tags:
      - transfer-types
schemes:
- https
- http
securityDefinitions:
  bearerAuth:
    in: header
    name: Authorization
    type: apiKey
swagger: "2.0"
