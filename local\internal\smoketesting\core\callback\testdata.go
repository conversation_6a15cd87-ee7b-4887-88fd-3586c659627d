package callback

import "git.local/sensitive/local/internal/domain"

func TestExpectedData() map[string]domain.MerchantWebhookBase {
	res := map[string]domain.MerchantWebhookBase{
		"success": {
			ProjectID:       42,
			MerchantID:      1,
			ProjectClientID: "kambar6122",
			//CardToken:       "AAAAAAAAAAAAAAAAAAAAAIxq5RruaVd+t8379wbp7mS4cvADWZ+jPyejQmwnm/mafC5wadzAmuTPBOAfYTXwkY+0annLttTVxj41ho5vEw6Lkr3NhPq9YMMNp5TZI/ySWZMMWAwSRJ3VjVSAEeiA0w==",
			TypeCode:   "in",
			StatusCode: "success",
		},
	}

	return res
}
