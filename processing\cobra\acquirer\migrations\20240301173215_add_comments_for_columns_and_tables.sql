-- +goose Up
-- +goose StatementBegin

comment
on table "acquirer".acquirers is 'The table is responsible for an acquirer entity that are bank partners';
comment
on column "acquirer".acquirers.code is 'The field is a CONSTANT string used to identify the bank in other services';
comment
on column "acquirer".acquirers.is_active is 'This field is strictly informative and does not affect the actual activation or disabling of the acquirer';
comment
on column "acquirer".acquirers.name is 'The legal name of the bank';

comment
on table "acquirer".bank_bins is 'The table is responsible for storing the information about bank cards bins
that are used to identify an issuer';
comment
on column "acquirer".bank_bins.bin is 'First six or eight numbers of credit card (pan) that bank issues or supports';
comment
on column "acquirer".bank_bins.ips_id is 'Foreign key of International Payment System';

comment
on table "acquirer".countries is 'Here we store the list of countries';

comment
on table "acquirer".country_banks is 'Here we map the list of countries with the banks (acquirer/issuer)';

comment
on table "acquirer".ips is 'The list of International Payment Systems (visa, mastercard and etc.)';

comment
on table "acquirer".terminals is 'The table is responsible for storing a terminal of an acquirer';
comment
on column "acquirer".terminals.acquirer_id is 'The terminal belongs to the acquirer';
comment
on column "acquirer".terminals.status is 'This field is responsible for activating or disabling the terminal.
1 means terminal is ON, 2 terminal is OFF, 3 means GLOBAL_OFF.';
comment
on column "acquirer".terminals.config is 'Temporary field to show the config of an terminal.
Needs to be removed due to PCI DSS. Remove it immediately right after you are sure that CRUD is working from the view. #REMOVE';
comment
on column "acquirer".terminals.encrypted_config is 'Terminal config that can be configured from view.
It is encrypted due to PCI DSS regulations';
comment
on column "acquirer".terminals.two_stage_timeout is 'This field is responsible to set for two stage pay in
transactions a timeout. This timeout is to call a CHARGE or CANCEL operations. Needs to be removed from here and used in
 the other table due to database normalisation #REMOVE';

comment
on table "acquirer".terminal_projects is 'The table is used to link the terminal to the project and its payment type';
comment
on column "acquirer".terminal_projects.is_active is 'Enable or disable the terminal for exact project
and transaction type';

comment
on table "acquirer".rules is 'Here is the rules for balancer that are used to find an appropriate acquirer';

comment
on table "acquirer".rule_percentage is 'The table is used to map the rule with an acquire and his percentage';

-- +goose StatementEnd

-- +goose Down
-- +goose StatementBegin

-- comment on table "acquirer".acquirers is null;
-- comment on column "acquirer".acquirers.code is null;
-- comment on column "acquirer".acquirers.is_active is null;
-- comment on column "acquirer".acquirers.name is null;
-- comment on table "acquirer".bank_bins is null;
-- comment on column "acquirer".bank_bins.bin is null;
-- comment on column "acquirer".bank_bins.ips_id is null;
-- comment on table "acquirer".countries is null;
-- comment on table "acquirer".country_banks is null;
-- comment on table "acquirer".ips is null;
-- comment on table "acquirer".terminals is null;
-- comment on column "acquirer".terminals.acquirer_id is null;
-- comment on column "acquirer".terminals.status is null;
-- comment on column "acquirer".terminals.config is null;
-- comment on column "acquirer".terminals.encrypted_config is null;
-- comment on column "acquirer".terminals.two_stage_timeout is null;
-- comment on table "acquirer".terminal_projects is null;
-- comment on column "acquirer".terminal_projects.is_active is null;
-- comment on table "acquirer".rules is null;
-- comment on table "acquirer".rule_percentage is null;

-- +goose StatementEnd