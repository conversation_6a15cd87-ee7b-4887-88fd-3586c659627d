package schema

import (
	"strings"
	"time"

	"github.com/go-playground/validator/v10"
	"gorm.io/gorm"
)

const DateFormat = "2006-01-02 15:04:05"

type AggregatedInfoResponse struct {
	Count       int64   `json:"total_count"`
	TotalAmount float64 `json:"total_amount"`
}

type TransactionFilterRequest struct {
	ProjectReferenceID   string   `form:"project_reference_id,omitempty" validate:"omitempty"`
	ProjectClient        string   `form:"project_client_id,omitempty" validate:"omitempty"`
	MaskedPan            string   `form:"masked_pan,omitempty" validate:"omitempty"`
	MerchantID           uint64   `form:"merchant_id,omitempty" validate:"omitempty"`
	RRN                  string   `form:"rrn" validate:"omitempty"`
	Amount               *float64 `form:"amount" validate:"omitempty"`
	CreatedAtFrom        string   `form:"created_at_from" validate:"omitempty"` //date,date
	CreatedAtTo          string   `form:"created_at_to" validate:"omitempty"`
	FinishedAtFrom       string   `form:"finished_at_from" validate:"omitempty"`
	FinishedAtTo         string   `form:"finished_at_to" validate:"omitempty"`
	ProjectClientID      string   `form:"project_client_id" validate:"omitempty"`
	SortBy               string   `form:"sort_by" validate:"omitempty"`
	TransactionIDs       string   `form:"transaction_ids,omitempty" validate:"omitempty"`
	ProjectIDs           string   `form:"project_ids" validate:"omitempty"`
	AcquirerIDs          string   `form:"acquirer_ids,omitempty" validate:"omitempty"`
	TransactionTypeIDs   string   `form:"transaction_type_ids,omitempty" validate:"omitempty"`
	TransactionStatusIDs string   `form:"transaction_status_ids" validate:"omitempty"`
	LastRefundDateFrom   string   `form:"last_refund_date_from" validate:"omitempty"`
	LastRefundDateTo     string   `form:"last_refund_date_to" validate:"omitempty"`
	IssuerIDs            string   `form:"issuer_ids,omitempty" validate:"omitempty"`
}

func (tr TransactionFilterRequest) Validate() error {
	validate := validator.New()

	return validate.Struct(tr)
}

type TransactionFilters struct {
	ProjectReferenceID  string   `form:"project_reference_id,omitempty" validate:"omitempty"`
	ProjectClient       string   `form:"project_client_id,omitempty" validate:"omitempty"`
	MaskedPan           string   `form:"masked_pan,omitempty" validate:"omitempty"`
	MerchantID          uint64   `form:"merchant_id,omitempty" validate:"omitempty"`
	RRN                 string   `form:"rrn" validate:"omitempty"`
	Amount              *float64 `form:"amount" validate:"omitempty"`
	CreatedAtFrom       string   `form:"created_at_from" validate:"omitempty"` //date,date
	CreatedAtTo         string   `form:"created_at_to" validate:"omitempty"`
	FinishedAtFrom      string   `form:"finished_at_from" validate:"omitempty"`
	FinishedAtTo        string   `form:"finished_at_to" validate:"omitempty"`
	ProjectClientID     string   `form:"project_client_id" validate:"omitempty"`
	SortBy              string   `form:"sort_by" validate:"omitempty"`
	TransactionID       []string `form:"transaction_ids,omitempty" validate:"omitempty"`
	ProjectID           []string `form:"project_ids" validate:"omitempty"`
	AcquirerID          []string `form:"acquirer_ids,omitempty" validate:"omitempty"`
	TransactionTypeID   []string `form:"transaction_type_ids,omitempty" validate:"omitempty"`
	TransactionStatusID []string `form:"transaction_status_ids" validate:"omitempty"`
	LastRefundDateFrom  string   `form:"last_refund_date_from" validate:"omitempty"`
	LastRefundDateTo    string   `form:"last_refund_date_to" validate:"omitempty"`
	IssuerID            []string `form:"issuer_ids,omitempty" validate:"omitempty"`
}

func (pr TransactionFilters) Validate() error {
	validation := validator.New()

	return validation.Struct(pr)
}

func (r *TransactionFilters) SetFilters(tx *gorm.DB) (err error) {
	if len(r.ProjectReferenceID) != 0 {
		tx.Where(`project_reference_id = ?`, r.ProjectReferenceID)
	}

	if r.Amount != nil {
		tx.Where(`amount = ?`, &r.Amount)
	}

	if r.ProjectClient != "" {
		tx.Where(`project_client_id = ?`, r.ProjectClient)
	}

	err = r.dateFilter(tx)
	if err != nil {
		return err
	}

	if r.RRN != "" {
		tx.Where(`rrn = ?`, r.RRN)
	}

	if r.MaskedPan != "" {
		tx.Where(`masked_pan LIKE ?`, "%"+r.MaskedPan+"%")
	}

	if r.MerchantID != 0 {
		tx.Where(`merchant_id = ?`, r.MerchantID)
	}

	err = r.multiChoiceFilter(tx, r.TransactionID, "transaction_id")
	if err != nil {
		return err
	}

	err = r.multiChoiceFilter(tx, r.ProjectID, "project_id")
	if err != nil {
		return err
	}

	err = r.multiChoiceFilter(tx, r.AcquirerID, "acquirer_id")
	if err != nil {
		return err
	}

	err = r.multiChoiceFilter(tx, r.TransactionStatusID, "transaction_status_id")
	if err != nil {
		return err
	}

	err = r.multiChoiceFilter(tx, r.TransactionTypeID, "transaction_type_id")
	if err != nil {
		return err
	}

	err = r.multiChoiceFilter(tx, r.IssuerID, "issuer_id")
	if err != nil {
		return err
	}

	return nil
}

func (r TransactionFilters) multiChoiceFilter(
	tx *gorm.DB,
	multiString []string,
	dbColumnName string) error {
	if len(multiString) == 0 || multiString[0] == "" {
		return nil
	}

	queryString := strings.Split(strings.Join(multiString, ","), ",")

	tx.Where(dbColumnName+" IN ?", queryString)

	return nil
}

func (r *TransactionFilters) dateFilter(tx *gorm.DB) error {
	if r.CreatedAtFrom != "" {
		createdAtFrom, err := time.Parse(DateFormat, r.CreatedAtFrom)
		if err != nil {
			return err
		}

		tx.Where(`created_at >= ?`, createdAtFrom)
	}

	if r.CreatedAtTo != "" {
		createdAtTo, err := time.Parse(DateFormat, r.CreatedAtTo)
		if err != nil {
			return err
		}

		tx.Where(`created_at <= ?`, createdAtTo)
	}

	if r.FinishedAtFrom != "" {
		finishedAtFrom, err := time.Parse(DateFormat, r.FinishedAtFrom)
		if err != nil {
			return err
		}

		tx.Where(`finished_at >= ?`, finishedAtFrom)
	}

	if r.FinishedAtTo != "" {
		finishedAtTo, err := time.Parse(DateFormat, r.FinishedAtTo)
		if err != nil {
			return err
		}

		tx.Where(`finished_at <= ?`, finishedAtTo)
	}

	if r.LastRefundDateFrom != "" {
		lastRefundDateFrom, err := time.Parse(DateFormat, r.LastRefundDateFrom)
		if err != nil {
			return err
		}

		tx.Where(`last_refund_date >= ?`, lastRefundDateFrom)
	}

	if r.LastRefundDateTo != "" {
		lastRefundDateTo, err := time.Parse(DateFormat, r.LastRefundDateTo)
		if err != nil {
			return err
		}

		tx.Where(`last_refund_date <= ?`, lastRefundDateTo)
	}

	return nil
}

func (tr *TransactionFilterRequest) ToTransactionFilter() TransactionFilters {
	var (
		TransactionIDs       []string
		ProjectIDs           []string
		AcquirerIDs          []string
		TransactionTypeIDs   []string
		TransactionStatusIDs []string
		IssuerIDs            []string
	)

	if tr.TransactionStatusIDs != "" {
		TransactionStatusIDs = strings.Split(tr.TransactionStatusIDs, ",")
	}

	if tr.ProjectIDs != "" {
		ProjectIDs = strings.Split(tr.ProjectIDs, ",")
	}

	if tr.AcquirerIDs != "" {
		AcquirerIDs = strings.Split(tr.AcquirerIDs, ",")
	}

	if tr.TransactionTypeIDs != "" {
		TransactionTypeIDs = strings.Split(tr.TransactionTypeIDs, ",")
	}

	if tr.TransactionIDs != "" {
		TransactionIDs = strings.Split(tr.TransactionIDs, ",")
	}

	if tr.IssuerIDs != "" {
		IssuerIDs = strings.Split(tr.IssuerIDs, ",")
	}

	return TransactionFilters{
		ProjectReferenceID:  tr.ProjectReferenceID,
		ProjectClient:       tr.ProjectClient,
		MaskedPan:           tr.MaskedPan,
		MerchantID:          tr.MerchantID,
		RRN:                 tr.RRN,
		Amount:              tr.Amount,
		CreatedAtFrom:       tr.CreatedAtFrom,
		CreatedAtTo:         tr.CreatedAtTo,
		FinishedAtFrom:      tr.FinishedAtFrom,
		FinishedAtTo:        tr.FinishedAtTo,
		ProjectClientID:     tr.ProjectClientID,
		SortBy:              tr.SortBy,
		TransactionID:       TransactionIDs,
		ProjectID:           ProjectIDs,
		AcquirerID:          AcquirerIDs,
		TransactionTypeID:   TransactionTypeIDs,
		TransactionStatusID: TransactionStatusIDs,
		LastRefundDateFrom:  tr.LastRefundDateFrom,
		LastRefundDateTo:    tr.LastRefundDateTo,
		IssuerID:            IssuerIDs,
	}
}
