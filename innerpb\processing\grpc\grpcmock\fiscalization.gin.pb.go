// Code generated by MockGen. DO NOT EDIT.
// Source: fiscalization.gin.pb.go

// Package grpcmock is a generated GoMock package.
package grpcmock

import (
	reflect "reflect"

	gin "github.com/gin-gonic/gin"
	gomock "github.com/golang/mock/gomock"
)

// MockGinFiscalizationServer is a mock of GinFiscalizationServer interface.
type MockGinFiscalizationServer struct {
	ctrl     *gomock.Controller
	recorder *MockGinFiscalizationServerMockRecorder
}

// MockGinFiscalizationServerMockRecorder is the mock recorder for MockGinFiscalizationServer.
type MockGinFiscalizationServerMockRecorder struct {
	mock *MockGinFiscalizationServer
}

// NewMockGinFiscalizationServer creates a new mock instance.
func NewMockGinFiscalizationServer(ctrl *gomock.Controller) *MockGinFiscalizationServer {
	mock := &MockGinFiscalizationServer{ctrl: ctrl}
	mock.recorder = &MockGinFiscalizationServerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockGinFiscalizationServer) EXPECT() *MockGinFiscalizationServerMockRecorder {
	return m.recorder
}

// FinalizeFiscalizations mocks base method.
func (m *MockGinFiscalizationServer) FinalizeFiscalizations(c *gin.Context) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "FinalizeFiscalizations", c)
	ret0, _ := ret[0].(error)
	return ret0
}

// FinalizeFiscalizations indicates an expected call of FinalizeFiscalizations.
func (mr *MockGinFiscalizationServerMockRecorder) FinalizeFiscalizations(c interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "FinalizeFiscalizations", reflect.TypeOf((*MockGinFiscalizationServer)(nil).FinalizeFiscalizations), c)
}

// GetFiscalInfoByTransactionIDV1 mocks base method.
func (m *MockGinFiscalizationServer) GetFiscalInfoByTransactionIDV1(c *gin.Context) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetFiscalInfoByTransactionIDV1", c)
	ret0, _ := ret[0].(error)
	return ret0
}

// GetFiscalInfoByTransactionIDV1 indicates an expected call of GetFiscalInfoByTransactionIDV1.
func (mr *MockGinFiscalizationServerMockRecorder) GetFiscalInfoByTransactionIDV1(c interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetFiscalInfoByTransactionIDV1", reflect.TypeOf((*MockGinFiscalizationServer)(nil).GetFiscalInfoByTransactionIDV1), c)
}

// MakeFiscalizationV1 mocks base method.
func (m *MockGinFiscalizationServer) MakeFiscalizationV1(c *gin.Context) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "MakeFiscalizationV1", c)
	ret0, _ := ret[0].(error)
	return ret0
}

// MakeFiscalizationV1 indicates an expected call of MakeFiscalizationV1.
func (mr *MockGinFiscalizationServerMockRecorder) MakeFiscalizationV1(c interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "MakeFiscalizationV1", reflect.TypeOf((*MockGinFiscalizationServer)(nil).MakeFiscalizationV1), c)
}

// ManageShifts mocks base method.
func (m *MockGinFiscalizationServer) ManageShifts(c *gin.Context) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ManageShifts", c)
	ret0, _ := ret[0].(error)
	return ret0
}

// ManageShifts indicates an expected call of ManageShifts.
func (mr *MockGinFiscalizationServerMockRecorder) ManageShifts(c interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ManageShifts", reflect.TypeOf((*MockGinFiscalizationServer)(nil).ManageShifts), c)
}
