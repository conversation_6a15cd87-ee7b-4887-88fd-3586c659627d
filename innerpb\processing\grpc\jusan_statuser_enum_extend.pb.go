// Code generated by protoc-gen-go-mvp. DO NOT EDIT.
// versions:
// - protoc-gen-go-mvp v2.0.1
// created by mvp authors

package grpc

import (
	math "math"
	strings "strings"
)

func (val JusanResponseCodeStatuser) Synonym() JusanResponseCodeStatuser {
	if _, ok := JusanResponseCodeStatuser_name[int32(val)]; ok {
		return val
	}

	return JusanResponseCodeStatuser(math.MinInt32)
}

func (val JusanResponseCodeStatuser) Int() int {
	return int(val.Synonym())
}

func (val JusanResponseCodeStatuser) IntPtr() *int {
	result := int(val.Synonym())
	return &result
}

func (val JusanResponseCodeStatuser) Int32() int32 {
	return int32(val.Synonym())
}

func (val JusanResponseCodeStatuser) Int32Ptr() *int32 {
	result := int32(val.Synonym())
	return &result
}

func (val JusanResponseCodeStatuser) Int64() int64 {
	return int64(val.Synonym())
}

func (val JusanResponseCodeStatuser) Int64Ptr() *int64 {
	result := int64(val.Synonym())
	return &result
}

func (val JusanResponseCodeStatuser) Uint() uint {
	return uint(val.Synonym())
}

func (val JusanResponseCodeStatuser) UintPtr() *uint {
	result := uint(val.Synonym())
	return &result
}

func (val JusanResponseCodeStatuser) Uint32() uint32 {
	return uint32(val.Synonym())
}

func (val JusanResponseCodeStatuser) Uint32Ptr() *uint32 {
	result := uint32(val.Synonym())
	return &result
}

func (val JusanResponseCodeStatuser) Uint64() uint64 {
	return uint64(val.Synonym())
}

func (val JusanResponseCodeStatuser) Uint64Ptr() *uint64 {
	result := uint64(val.Synonym())
	return &result
}

func (val JusanResponseCodeStatuser) IsKnown() bool {
	return val.Synonym() != JusanResponseCodeStatuser(math.MinInt32)
}

func ConvertIntToJusanResponseCodeStatuser(in int) JusanResponseCodeStatuser {
	return JusanResponseCodeStatuser(in).Synonym()
}

func ConvertUintToJusanResponseCodeStatuser(in uint) JusanResponseCodeStatuser {
	return JusanResponseCodeStatuser(in).Synonym()
}

func ConvertInt32ToJusanResponseCodeStatuser(in int32) JusanResponseCodeStatuser {
	return JusanResponseCodeStatuser(in).Synonym()
}

func ConvertUint32ToJusanResponseCodeStatuser(in uint32) JusanResponseCodeStatuser {
	return JusanResponseCodeStatuser(in).Synonym()
}

func ConvertInt64ToJusanResponseCodeStatuser(in int64) JusanResponseCodeStatuser {
	return JusanResponseCodeStatuser(in).Synonym()
}

func ConvertUint64ToJusanResponseCodeStatuser(in uint64) JusanResponseCodeStatuser {
	return JusanResponseCodeStatuser(in).Synonym()
}

var JusanResponseCodeStatuser_Lower_value = map[string]JusanResponseCodeStatuser{
	"11":   0,
	"12":   1,
	"13":   2,
	"14":   3,
	"15":   4,
	"16":   5,
	"171":  6,
	"172 ": 7,
	"18":   8,
	"19":   9,
	"20":   10,
	"21":   11,
	"22":   12,
	"23":   13,
	"24":   14,
	"25":   15,
	"26":   16,
	"27":   17,
	"28":   18,
	"29":   19,
	"30":   20,
	"31":   21,
	"32":   22,
	"33":   23,
	"34":   24,
	"99":   25,
	"41":   26,
	"42":   27,
	"43":   28,
	"44":   29,
	"17":   30,
	"45":   31,
	"46":   32,
	"47 ":  33,
	"48":   34,
	"50":   35,
	"51":   36,
	"52":   37,
	"53":   38,
	"f":    39,
	"e":    40,
	"c":    41,
}

func ConvertStringToJusanResponseCodeStatuser(in string) JusanResponseCodeStatuser {
	if result, ok := JusanResponseCodeStatuser_value[in]; ok {
		return JusanResponseCodeStatuser(result)
	}

	if result, ok := JusanResponseCodeStatuser_Lower_value[strings.ToLower(in)]; ok {
		return JusanResponseCodeStatuser(result)
	}

	return JusanResponseCodeStatuser(math.MinInt32)
}

var SliceJusanResponseCodeStatuserConvert *sliceJusanResponseCodeStatuserConvert

type sliceJusanResponseCodeStatuserConvert struct{}

func (*sliceJusanResponseCodeStatuserConvert) Synonym(in []JusanResponseCodeStatuser) []JusanResponseCodeStatuser {
	result := make([]JusanResponseCodeStatuser, len(in))
	for i, v := range in {
		result[i] = v.Synonym()
	}

	return result
}

func (*sliceJusanResponseCodeStatuserConvert) Int32(in []JusanResponseCodeStatuser) []int32 {
	result := make([]int32, len(in))
	for i, v := range in {
		result[i] = v.Int32()
	}

	return result
}

func (*sliceJusanResponseCodeStatuserConvert) Uint32(in []JusanResponseCodeStatuser) []uint32 {
	result := make([]uint32, len(in))
	for i, v := range in {
		result[i] = v.Uint32()
	}

	return result
}

func (*sliceJusanResponseCodeStatuserConvert) Uint64(in []JusanResponseCodeStatuser) []uint64 {
	result := make([]uint64, len(in))
	for i, v := range in {
		result[i] = v.Uint64()
	}

	return result
}

func (*sliceJusanResponseCodeStatuserConvert) Int64(in []JusanResponseCodeStatuser) []int64 {
	result := make([]int64, len(in))
	for i, v := range in {
		result[i] = v.Int64()
	}

	return result
}

func (*sliceJusanResponseCodeStatuserConvert) Uint(in []JusanResponseCodeStatuser) []uint {
	result := make([]uint, len(in))
	for i, v := range in {
		result[i] = v.Uint()
	}

	return result
}

func (*sliceJusanResponseCodeStatuserConvert) Int(in []JusanResponseCodeStatuser) []int {
	result := make([]int, len(in))
	for i, v := range in {
		result[i] = v.Int()
	}

	return result
}

func (*sliceJusanResponseCodeStatuserConvert) IsKnown(in []JusanResponseCodeStatuser) []bool {
	result := make([]bool, len(in))
	for i, v := range in {
		result[i] = v.IsKnown()
	}

	return result
}

func (*sliceJusanResponseCodeStatuserConvert) ConvertIntToJusanResponseCodeStatuser(in []int) []JusanResponseCodeStatuser {
	result := make([]JusanResponseCodeStatuser, len(in))
	for i, v := range in {
		result[i] = ConvertIntToJusanResponseCodeStatuser(v)
	}

	return result
}

func (*sliceJusanResponseCodeStatuserConvert) ConvertUintToJusanResponseCodeStatuser(in []uint) []JusanResponseCodeStatuser {
	result := make([]JusanResponseCodeStatuser, len(in))
	for i, v := range in {
		result[i] = ConvertUintToJusanResponseCodeStatuser(v)
	}

	return result
}

func (*sliceJusanResponseCodeStatuserConvert) ConvertInt32ToJusanResponseCodeStatuser(in []int32) []JusanResponseCodeStatuser {
	result := make([]JusanResponseCodeStatuser, len(in))
	for i, v := range in {
		result[i] = ConvertInt32ToJusanResponseCodeStatuser(v)
	}

	return result
}

func (*sliceJusanResponseCodeStatuserConvert) ConvertUint32ToJusanResponseCodeStatuser(in []uint32) []JusanResponseCodeStatuser {
	result := make([]JusanResponseCodeStatuser, len(in))
	for i, v := range in {
		result[i] = ConvertUint32ToJusanResponseCodeStatuser(v)
	}

	return result
}

func (*sliceJusanResponseCodeStatuserConvert) ConvertInt64ToJusanResponseCodeStatuser(in []int64) []JusanResponseCodeStatuser {
	result := make([]JusanResponseCodeStatuser, len(in))
	for i, v := range in {
		result[i] = ConvertInt64ToJusanResponseCodeStatuser(v)
	}

	return result
}

func (*sliceJusanResponseCodeStatuserConvert) ConvertUint64ToJusanResponseCodeStatuser(in []uint64) []JusanResponseCodeStatuser {
	result := make([]JusanResponseCodeStatuser, len(in))
	for i, v := range in {
		result[i] = ConvertUint64ToJusanResponseCodeStatuser(v)
	}

	return result
}

func (*sliceJusanResponseCodeStatuserConvert) ConvertStringToJusanResponseCodeStatuser(in []string) []JusanResponseCodeStatuser {
	result := make([]JusanResponseCodeStatuser, len(in))
	for i, v := range in {
		result[i] = ConvertStringToJusanResponseCodeStatuser(v)
	}

	return result
}

func NewJusanResponseCodeStatuserUsage() *JusanResponseCodeStatuserUsage {
	return &JusanResponseCodeStatuserUsage{
		enumMap: map[JusanResponseCodeStatuser]bool{
			JusanResponseCodeStatuser_StatuserServiceUnavailable:      false,
			JusanResponseCodeStatuser_StatuserIncorrectFieldOrder:     false,
			JusanResponseCodeStatuser_StatuserIncorrectAmount:         false,
			JusanResponseCodeStatuser_StatuserIncorrectCurrency:       false,
			JusanResponseCodeStatuser_StatuserNoSuchCard:              false,
			JusanResponseCodeStatuser_StatuserDbUnavailable:           false,
			JusanResponseCodeStatuser_StatuserForbiddenForMerchant:    false,
			JusanResponseCodeStatuser_StatuserForbiddenMerchantByLaw:  false,
			JusanResponseCodeStatuser_StatuserRequestAlreadeCompleted: false,
			JusanResponseCodeStatuser_StatuserIncorrectCardExpDate:    false,
			JusanResponseCodeStatuser_StatuserIncorrectFieldTerminal:  false,
			JusanResponseCodeStatuser_StatuserIncorrectSign:           false,
			JusanResponseCodeStatuser_StatuserCurrencyNotFound:        false,
			JusanResponseCodeStatuser_StatuserLimitExceeded:           false,
			JusanResponseCodeStatuser_StatuserEmptyField:              false,
			JusanResponseCodeStatuser_StatuserSizeLessSymbol:          false,
			JusanResponseCodeStatuser_StatuserSizeMoreSymbol:          false,
			JusanResponseCodeStatuser_StatuserInvalidValue:            false,
			JusanResponseCodeStatuser_StatuserMPIError3DS:             false,
			JusanResponseCodeStatuser_StatuserInvalidCardType:         false,
			JusanResponseCodeStatuser_StatuserPaymentNotFound:         false,
			JusanResponseCodeStatuser_StatuserClientKeyNotFound:       false,
			JusanResponseCodeStatuser_StatuserForbiddenTerminal:       false,
			JusanResponseCodeStatuser_StatuserTokenNotFound:           false,
			JusanResponseCodeStatuser_StatuserIncorrectBlockAmount:    false,
			JusanResponseCodeStatuser_StatuserUnknownError:            false,
			JusanResponseCodeStatuser_StatuserUnavailableService:      false,
			JusanResponseCodeStatuser_StatuserSumIncorrect:            false,
			JusanResponseCodeStatuser_StatuserUnavailableDb:           false,
			JusanResponseCodeStatuser_StatuserIncorrectMerchant:       false,
			JusanResponseCodeStatuser_StatuserNotFoundMerchant:        false,
			JusanResponseCodeStatuser_StatuserOrderNotFound:           false,
			JusanResponseCodeStatuser_StatuserInvalidSign:             false,
			JusanResponseCodeStatuser_StatuserIncorrectRefundAmount:   false,
			JusanResponseCodeStatuser_StatuserIncorrectStatus:         false,
			JusanResponseCodeStatuser_StatuserIncorrectValue:          false,
			JusanResponseCodeStatuser_StatuserTerminalStatusIncorrect: false,
			JusanResponseCodeStatuser_StatuserTerminalForbidden:       false,
			JusanResponseCodeStatuser_StatuserDuplicateDescription:    false,
			JusanResponseCodeStatuser_StatuserRefundHandleError:       false,
			JusanResponseCodeStatuser_StatuserPaymentError:            false,
			JusanResponseCodeStatuser_StatuserPaymentExpired:          false,
		},
	}
}

func IsJusanResponseCodeStatuser(target JusanResponseCodeStatuser, matches ...JusanResponseCodeStatuser) bool {
	for _, v := range matches {
		if target == v {
			return true
		}
	}

	return false
}

type JusanResponseCodeStatuserUsage struct {
	enumMap map[JusanResponseCodeStatuser]bool
}

func (u *JusanResponseCodeStatuserUsage) Use(slice ...JusanResponseCodeStatuser) *JusanResponseCodeStatuserUsage {
	for _, in := range slice {
		u.enumMap[in] = true
	}

	return u
}

func (u *JusanResponseCodeStatuserUsage) Check() []string {
	var result []string
	for k, v := range u.enumMap {
		if v {
			continue
		}

		result = append(result, k.String())
	}

	return result
}

func (u *JusanResponseCodeStatuserUsage) JusanResponseCodeStatuser_StatuserServiceUnavailable() JusanResponseCodeStatuser {
	u.Use(JusanResponseCodeStatuser_StatuserServiceUnavailable)
	return JusanResponseCodeStatuser_StatuserServiceUnavailable
}

func (u *JusanResponseCodeStatuserUsage) JusanResponseCodeStatuser_StatuserIncorrectFieldOrder() JusanResponseCodeStatuser {
	u.Use(JusanResponseCodeStatuser_StatuserIncorrectFieldOrder)
	return JusanResponseCodeStatuser_StatuserIncorrectFieldOrder
}

func (u *JusanResponseCodeStatuserUsage) JusanResponseCodeStatuser_StatuserIncorrectAmount() JusanResponseCodeStatuser {
	u.Use(JusanResponseCodeStatuser_StatuserIncorrectAmount)
	return JusanResponseCodeStatuser_StatuserIncorrectAmount
}

func (u *JusanResponseCodeStatuserUsage) JusanResponseCodeStatuser_StatuserIncorrectCurrency() JusanResponseCodeStatuser {
	u.Use(JusanResponseCodeStatuser_StatuserIncorrectCurrency)
	return JusanResponseCodeStatuser_StatuserIncorrectCurrency
}

func (u *JusanResponseCodeStatuserUsage) JusanResponseCodeStatuser_StatuserNoSuchCard() JusanResponseCodeStatuser {
	u.Use(JusanResponseCodeStatuser_StatuserNoSuchCard)
	return JusanResponseCodeStatuser_StatuserNoSuchCard
}

func (u *JusanResponseCodeStatuserUsage) JusanResponseCodeStatuser_StatuserDbUnavailable() JusanResponseCodeStatuser {
	u.Use(JusanResponseCodeStatuser_StatuserDbUnavailable)
	return JusanResponseCodeStatuser_StatuserDbUnavailable
}

func (u *JusanResponseCodeStatuserUsage) JusanResponseCodeStatuser_StatuserForbiddenForMerchant() JusanResponseCodeStatuser {
	u.Use(JusanResponseCodeStatuser_StatuserForbiddenForMerchant)
	return JusanResponseCodeStatuser_StatuserForbiddenForMerchant
}

func (u *JusanResponseCodeStatuserUsage) JusanResponseCodeStatuser_StatuserForbiddenMerchantByLaw() JusanResponseCodeStatuser {
	u.Use(JusanResponseCodeStatuser_StatuserForbiddenMerchantByLaw)
	return JusanResponseCodeStatuser_StatuserForbiddenMerchantByLaw
}

func (u *JusanResponseCodeStatuserUsage) JusanResponseCodeStatuser_StatuserRequestAlreadeCompleted() JusanResponseCodeStatuser {
	u.Use(JusanResponseCodeStatuser_StatuserRequestAlreadeCompleted)
	return JusanResponseCodeStatuser_StatuserRequestAlreadeCompleted
}

func (u *JusanResponseCodeStatuserUsage) JusanResponseCodeStatuser_StatuserIncorrectCardExpDate() JusanResponseCodeStatuser {
	u.Use(JusanResponseCodeStatuser_StatuserIncorrectCardExpDate)
	return JusanResponseCodeStatuser_StatuserIncorrectCardExpDate
}

func (u *JusanResponseCodeStatuserUsage) JusanResponseCodeStatuser_StatuserIncorrectFieldTerminal() JusanResponseCodeStatuser {
	u.Use(JusanResponseCodeStatuser_StatuserIncorrectFieldTerminal)
	return JusanResponseCodeStatuser_StatuserIncorrectFieldTerminal
}

func (u *JusanResponseCodeStatuserUsage) JusanResponseCodeStatuser_StatuserIncorrectSign() JusanResponseCodeStatuser {
	u.Use(JusanResponseCodeStatuser_StatuserIncorrectSign)
	return JusanResponseCodeStatuser_StatuserIncorrectSign
}

func (u *JusanResponseCodeStatuserUsage) JusanResponseCodeStatuser_StatuserCurrencyNotFound() JusanResponseCodeStatuser {
	u.Use(JusanResponseCodeStatuser_StatuserCurrencyNotFound)
	return JusanResponseCodeStatuser_StatuserCurrencyNotFound
}

func (u *JusanResponseCodeStatuserUsage) JusanResponseCodeStatuser_StatuserLimitExceeded() JusanResponseCodeStatuser {
	u.Use(JusanResponseCodeStatuser_StatuserLimitExceeded)
	return JusanResponseCodeStatuser_StatuserLimitExceeded
}

func (u *JusanResponseCodeStatuserUsage) JusanResponseCodeStatuser_StatuserEmptyField() JusanResponseCodeStatuser {
	u.Use(JusanResponseCodeStatuser_StatuserEmptyField)
	return JusanResponseCodeStatuser_StatuserEmptyField
}

func (u *JusanResponseCodeStatuserUsage) JusanResponseCodeStatuser_StatuserSizeLessSymbol() JusanResponseCodeStatuser {
	u.Use(JusanResponseCodeStatuser_StatuserSizeLessSymbol)
	return JusanResponseCodeStatuser_StatuserSizeLessSymbol
}

func (u *JusanResponseCodeStatuserUsage) JusanResponseCodeStatuser_StatuserSizeMoreSymbol() JusanResponseCodeStatuser {
	u.Use(JusanResponseCodeStatuser_StatuserSizeMoreSymbol)
	return JusanResponseCodeStatuser_StatuserSizeMoreSymbol
}

func (u *JusanResponseCodeStatuserUsage) JusanResponseCodeStatuser_StatuserInvalidValue() JusanResponseCodeStatuser {
	u.Use(JusanResponseCodeStatuser_StatuserInvalidValue)
	return JusanResponseCodeStatuser_StatuserInvalidValue
}

func (u *JusanResponseCodeStatuserUsage) JusanResponseCodeStatuser_StatuserMPIError3DS() JusanResponseCodeStatuser {
	u.Use(JusanResponseCodeStatuser_StatuserMPIError3DS)
	return JusanResponseCodeStatuser_StatuserMPIError3DS
}

func (u *JusanResponseCodeStatuserUsage) JusanResponseCodeStatuser_StatuserInvalidCardType() JusanResponseCodeStatuser {
	u.Use(JusanResponseCodeStatuser_StatuserInvalidCardType)
	return JusanResponseCodeStatuser_StatuserInvalidCardType
}

func (u *JusanResponseCodeStatuserUsage) JusanResponseCodeStatuser_StatuserPaymentNotFound() JusanResponseCodeStatuser {
	u.Use(JusanResponseCodeStatuser_StatuserPaymentNotFound)
	return JusanResponseCodeStatuser_StatuserPaymentNotFound
}

func (u *JusanResponseCodeStatuserUsage) JusanResponseCodeStatuser_StatuserClientKeyNotFound() JusanResponseCodeStatuser {
	u.Use(JusanResponseCodeStatuser_StatuserClientKeyNotFound)
	return JusanResponseCodeStatuser_StatuserClientKeyNotFound
}

func (u *JusanResponseCodeStatuserUsage) JusanResponseCodeStatuser_StatuserForbiddenTerminal() JusanResponseCodeStatuser {
	u.Use(JusanResponseCodeStatuser_StatuserForbiddenTerminal)
	return JusanResponseCodeStatuser_StatuserForbiddenTerminal
}

func (u *JusanResponseCodeStatuserUsage) JusanResponseCodeStatuser_StatuserTokenNotFound() JusanResponseCodeStatuser {
	u.Use(JusanResponseCodeStatuser_StatuserTokenNotFound)
	return JusanResponseCodeStatuser_StatuserTokenNotFound
}

func (u *JusanResponseCodeStatuserUsage) JusanResponseCodeStatuser_StatuserIncorrectBlockAmount() JusanResponseCodeStatuser {
	u.Use(JusanResponseCodeStatuser_StatuserIncorrectBlockAmount)
	return JusanResponseCodeStatuser_StatuserIncorrectBlockAmount
}

func (u *JusanResponseCodeStatuserUsage) JusanResponseCodeStatuser_StatuserUnknownError() JusanResponseCodeStatuser {
	u.Use(JusanResponseCodeStatuser_StatuserUnknownError)
	return JusanResponseCodeStatuser_StatuserUnknownError
}

func (u *JusanResponseCodeStatuserUsage) JusanResponseCodeStatuser_StatuserUnavailableService() JusanResponseCodeStatuser {
	u.Use(JusanResponseCodeStatuser_StatuserUnavailableService)
	return JusanResponseCodeStatuser_StatuserUnavailableService
}

func (u *JusanResponseCodeStatuserUsage) JusanResponseCodeStatuser_StatuserSumIncorrect() JusanResponseCodeStatuser {
	u.Use(JusanResponseCodeStatuser_StatuserSumIncorrect)
	return JusanResponseCodeStatuser_StatuserSumIncorrect
}

func (u *JusanResponseCodeStatuserUsage) JusanResponseCodeStatuser_StatuserUnavailableDb() JusanResponseCodeStatuser {
	u.Use(JusanResponseCodeStatuser_StatuserUnavailableDb)
	return JusanResponseCodeStatuser_StatuserUnavailableDb
}

func (u *JusanResponseCodeStatuserUsage) JusanResponseCodeStatuser_StatuserIncorrectMerchant() JusanResponseCodeStatuser {
	u.Use(JusanResponseCodeStatuser_StatuserIncorrectMerchant)
	return JusanResponseCodeStatuser_StatuserIncorrectMerchant
}

func (u *JusanResponseCodeStatuserUsage) JusanResponseCodeStatuser_StatuserNotFoundMerchant() JusanResponseCodeStatuser {
	u.Use(JusanResponseCodeStatuser_StatuserNotFoundMerchant)
	return JusanResponseCodeStatuser_StatuserNotFoundMerchant
}

func (u *JusanResponseCodeStatuserUsage) JusanResponseCodeStatuser_StatuserOrderNotFound() JusanResponseCodeStatuser {
	u.Use(JusanResponseCodeStatuser_StatuserOrderNotFound)
	return JusanResponseCodeStatuser_StatuserOrderNotFound
}

func (u *JusanResponseCodeStatuserUsage) JusanResponseCodeStatuser_StatuserInvalidSign() JusanResponseCodeStatuser {
	u.Use(JusanResponseCodeStatuser_StatuserInvalidSign)
	return JusanResponseCodeStatuser_StatuserInvalidSign
}

func (u *JusanResponseCodeStatuserUsage) JusanResponseCodeStatuser_StatuserIncorrectRefundAmount() JusanResponseCodeStatuser {
	u.Use(JusanResponseCodeStatuser_StatuserIncorrectRefundAmount)
	return JusanResponseCodeStatuser_StatuserIncorrectRefundAmount
}

func (u *JusanResponseCodeStatuserUsage) JusanResponseCodeStatuser_StatuserIncorrectStatus() JusanResponseCodeStatuser {
	u.Use(JusanResponseCodeStatuser_StatuserIncorrectStatus)
	return JusanResponseCodeStatuser_StatuserIncorrectStatus
}

func (u *JusanResponseCodeStatuserUsage) JusanResponseCodeStatuser_StatuserIncorrectValue() JusanResponseCodeStatuser {
	u.Use(JusanResponseCodeStatuser_StatuserIncorrectValue)
	return JusanResponseCodeStatuser_StatuserIncorrectValue
}

func (u *JusanResponseCodeStatuserUsage) JusanResponseCodeStatuser_StatuserTerminalStatusIncorrect() JusanResponseCodeStatuser {
	u.Use(JusanResponseCodeStatuser_StatuserTerminalStatusIncorrect)
	return JusanResponseCodeStatuser_StatuserTerminalStatusIncorrect
}

func (u *JusanResponseCodeStatuserUsage) JusanResponseCodeStatuser_StatuserTerminalForbidden() JusanResponseCodeStatuser {
	u.Use(JusanResponseCodeStatuser_StatuserTerminalForbidden)
	return JusanResponseCodeStatuser_StatuserTerminalForbidden
}

func (u *JusanResponseCodeStatuserUsage) JusanResponseCodeStatuser_StatuserDuplicateDescription() JusanResponseCodeStatuser {
	u.Use(JusanResponseCodeStatuser_StatuserDuplicateDescription)
	return JusanResponseCodeStatuser_StatuserDuplicateDescription
}

func (u *JusanResponseCodeStatuserUsage) JusanResponseCodeStatuser_StatuserRefundHandleError() JusanResponseCodeStatuser {
	u.Use(JusanResponseCodeStatuser_StatuserRefundHandleError)
	return JusanResponseCodeStatuser_StatuserRefundHandleError
}

func (u *JusanResponseCodeStatuserUsage) JusanResponseCodeStatuser_StatuserPaymentError() JusanResponseCodeStatuser {
	u.Use(JusanResponseCodeStatuser_StatuserPaymentError)
	return JusanResponseCodeStatuser_StatuserPaymentError
}

func (u *JusanResponseCodeStatuserUsage) JusanResponseCodeStatuser_StatuserPaymentExpired() JusanResponseCodeStatuser {
	u.Use(JusanResponseCodeStatuser_StatuserPaymentExpired)
	return JusanResponseCodeStatuser_StatuserPaymentExpired
}
