// Code generated by protoc-gen-go-mvp. DO NOT EDIT.
// versions:
// - protoc-gen-go-mvp v2.0.1
// created by mvp authors

package grpc

import (
	math "math"
	strings "strings"
)

func (val EnumTransactionType) Synonym() EnumTransactionType {
	if _, ok := EnumTransactionType_name[int32(val)]; ok {
		return val
	}

	return EnumTransactionType(math.MinInt32)
}

func (val EnumTransactionType) Int() int {
	return int(val.Synonym())
}

func (val EnumTransactionType) IntPtr() *int {
	result := int(val.Synonym())
	return &result
}

func (val EnumTransactionType) Int32() int32 {
	return int32(val.Synonym())
}

func (val EnumTransactionType) Int32Ptr() *int32 {
	result := int32(val.Synonym())
	return &result
}

func (val EnumTransactionType) Int64() int64 {
	return int64(val.Synonym())
}

func (val EnumTransactionType) Int64Ptr() *int64 {
	result := int64(val.Synonym())
	return &result
}

func (val EnumTransactionType) Uint() uint {
	return uint(val.Synonym())
}

func (val EnumTransactionType) UintPtr() *uint {
	result := uint(val.Synonym())
	return &result
}

func (val EnumTransactionType) Uint32() uint32 {
	return uint32(val.Synonym())
}

func (val EnumTransactionType) Uint32Ptr() *uint32 {
	result := uint32(val.Synonym())
	return &result
}

func (val EnumTransactionType) Uint64() uint64 {
	return uint64(val.Synonym())
}

func (val EnumTransactionType) Uint64Ptr() *uint64 {
	result := uint64(val.Synonym())
	return &result
}

func (val EnumTransactionType) IsKnown() bool {
	return val.Synonym() != EnumTransactionType(math.MinInt32)
}

func ConvertIntToEnumTransactionType(in int) EnumTransactionType {
	return EnumTransactionType(in).Synonym()
}

func ConvertUintToEnumTransactionType(in uint) EnumTransactionType {
	return EnumTransactionType(in).Synonym()
}

func ConvertInt32ToEnumTransactionType(in int32) EnumTransactionType {
	return EnumTransactionType(in).Synonym()
}

func ConvertUint32ToEnumTransactionType(in uint32) EnumTransactionType {
	return EnumTransactionType(in).Synonym()
}

func ConvertInt64ToEnumTransactionType(in int64) EnumTransactionType {
	return EnumTransactionType(in).Synonym()
}

func ConvertUint64ToEnumTransactionType(in uint64) EnumTransactionType {
	return EnumTransactionType(in).Synonym()
}

var EnumTransactionType_Lower_value = map[string]EnumTransactionType{
	"unknown":                       0,
	"transactiontypepayin":          1,
	"transactiontypepayout":         2,
	"transactiontypeoneclickpayin":  3,
	"transactiontypeoneclickpayout": 4,
	"transactiontypeapplepay":       5,
	"transactiontypecardlink":       6,
	"transactiontyperefund":         7,
	"transactiontypegooglepay":      8,
	"transactiontypetwostagepayin":  9,
}

func ConvertStringToEnumTransactionType(in string) EnumTransactionType {
	if result, ok := EnumTransactionType_value[in]; ok {
		return EnumTransactionType(result)
	}

	if result, ok := EnumTransactionType_Lower_value[strings.ToLower(in)]; ok {
		return EnumTransactionType(result)
	}

	return EnumTransactionType(math.MinInt32)
}

var SliceEnumTransactionTypeConvert *sliceEnumTransactionTypeConvert

type sliceEnumTransactionTypeConvert struct{}

func (*sliceEnumTransactionTypeConvert) Synonym(in []EnumTransactionType) []EnumTransactionType {
	result := make([]EnumTransactionType, len(in))
	for i, v := range in {
		result[i] = v.Synonym()
	}

	return result
}

func (*sliceEnumTransactionTypeConvert) Int32(in []EnumTransactionType) []int32 {
	result := make([]int32, len(in))
	for i, v := range in {
		result[i] = v.Int32()
	}

	return result
}

func (*sliceEnumTransactionTypeConvert) Uint32(in []EnumTransactionType) []uint32 {
	result := make([]uint32, len(in))
	for i, v := range in {
		result[i] = v.Uint32()
	}

	return result
}

func (*sliceEnumTransactionTypeConvert) Uint64(in []EnumTransactionType) []uint64 {
	result := make([]uint64, len(in))
	for i, v := range in {
		result[i] = v.Uint64()
	}

	return result
}

func (*sliceEnumTransactionTypeConvert) Int64(in []EnumTransactionType) []int64 {
	result := make([]int64, len(in))
	for i, v := range in {
		result[i] = v.Int64()
	}

	return result
}

func (*sliceEnumTransactionTypeConvert) Uint(in []EnumTransactionType) []uint {
	result := make([]uint, len(in))
	for i, v := range in {
		result[i] = v.Uint()
	}

	return result
}

func (*sliceEnumTransactionTypeConvert) Int(in []EnumTransactionType) []int {
	result := make([]int, len(in))
	for i, v := range in {
		result[i] = v.Int()
	}

	return result
}

func (*sliceEnumTransactionTypeConvert) IsKnown(in []EnumTransactionType) []bool {
	result := make([]bool, len(in))
	for i, v := range in {
		result[i] = v.IsKnown()
	}

	return result
}

func (*sliceEnumTransactionTypeConvert) ConvertIntToEnumTransactionType(in []int) []EnumTransactionType {
	result := make([]EnumTransactionType, len(in))
	for i, v := range in {
		result[i] = ConvertIntToEnumTransactionType(v)
	}

	return result
}

func (*sliceEnumTransactionTypeConvert) ConvertUintToEnumTransactionType(in []uint) []EnumTransactionType {
	result := make([]EnumTransactionType, len(in))
	for i, v := range in {
		result[i] = ConvertUintToEnumTransactionType(v)
	}

	return result
}

func (*sliceEnumTransactionTypeConvert) ConvertInt32ToEnumTransactionType(in []int32) []EnumTransactionType {
	result := make([]EnumTransactionType, len(in))
	for i, v := range in {
		result[i] = ConvertInt32ToEnumTransactionType(v)
	}

	return result
}

func (*sliceEnumTransactionTypeConvert) ConvertUint32ToEnumTransactionType(in []uint32) []EnumTransactionType {
	result := make([]EnumTransactionType, len(in))
	for i, v := range in {
		result[i] = ConvertUint32ToEnumTransactionType(v)
	}

	return result
}

func (*sliceEnumTransactionTypeConvert) ConvertInt64ToEnumTransactionType(in []int64) []EnumTransactionType {
	result := make([]EnumTransactionType, len(in))
	for i, v := range in {
		result[i] = ConvertInt64ToEnumTransactionType(v)
	}

	return result
}

func (*sliceEnumTransactionTypeConvert) ConvertUint64ToEnumTransactionType(in []uint64) []EnumTransactionType {
	result := make([]EnumTransactionType, len(in))
	for i, v := range in {
		result[i] = ConvertUint64ToEnumTransactionType(v)
	}

	return result
}

func (*sliceEnumTransactionTypeConvert) ConvertStringToEnumTransactionType(in []string) []EnumTransactionType {
	result := make([]EnumTransactionType, len(in))
	for i, v := range in {
		result[i] = ConvertStringToEnumTransactionType(v)
	}

	return result
}

func NewEnumTransactionTypeUsage() *EnumTransactionTypeUsage {
	return &EnumTransactionTypeUsage{
		enumMap: map[EnumTransactionType]bool{
			EnumTransactionType_Unknown:                       false,
			EnumTransactionType_TransactionTypePayIn:          false,
			EnumTransactionType_TransactionTypePayOut:         false,
			EnumTransactionType_TransactionTypeOneClickPayIn:  false,
			EnumTransactionType_TransactionTypeOneClickPayOut: false,
			EnumTransactionType_TransactionTypeApplePay:       false,
			EnumTransactionType_TransactionTypeCardLink:       false,
			EnumTransactionType_TransactionTypeRefund:         false,
			EnumTransactionType_TransactionTypeGooglePay:      false,
			EnumTransactionType_TransactionTypeTwoStagePayIn:  false,
		},
	}
}

func IsEnumTransactionType(target EnumTransactionType, matches ...EnumTransactionType) bool {
	for _, v := range matches {
		if target == v {
			return true
		}
	}

	return false
}

type EnumTransactionTypeUsage struct {
	enumMap map[EnumTransactionType]bool
}

func (u *EnumTransactionTypeUsage) Use(slice ...EnumTransactionType) *EnumTransactionTypeUsage {
	for _, in := range slice {
		u.enumMap[in] = true
	}

	return u
}

func (u *EnumTransactionTypeUsage) Check() []string {
	var result []string
	for k, v := range u.enumMap {
		if v {
			continue
		}

		result = append(result, k.String())
	}

	return result
}

func (u *EnumTransactionTypeUsage) EnumTransactionType_Unknown() EnumTransactionType {
	u.Use(EnumTransactionType_Unknown)
	return EnumTransactionType_Unknown
}

func (u *EnumTransactionTypeUsage) EnumTransactionType_TransactionTypePayIn() EnumTransactionType {
	u.Use(EnumTransactionType_TransactionTypePayIn)
	return EnumTransactionType_TransactionTypePayIn
}

func (u *EnumTransactionTypeUsage) EnumTransactionType_TransactionTypePayOut() EnumTransactionType {
	u.Use(EnumTransactionType_TransactionTypePayOut)
	return EnumTransactionType_TransactionTypePayOut
}

func (u *EnumTransactionTypeUsage) EnumTransactionType_TransactionTypeOneClickPayIn() EnumTransactionType {
	u.Use(EnumTransactionType_TransactionTypeOneClickPayIn)
	return EnumTransactionType_TransactionTypeOneClickPayIn
}

func (u *EnumTransactionTypeUsage) EnumTransactionType_TransactionTypeOneClickPayOut() EnumTransactionType {
	u.Use(EnumTransactionType_TransactionTypeOneClickPayOut)
	return EnumTransactionType_TransactionTypeOneClickPayOut
}

func (u *EnumTransactionTypeUsage) EnumTransactionType_TransactionTypeApplePay() EnumTransactionType {
	u.Use(EnumTransactionType_TransactionTypeApplePay)
	return EnumTransactionType_TransactionTypeApplePay
}

func (u *EnumTransactionTypeUsage) EnumTransactionType_TransactionTypeCardLink() EnumTransactionType {
	u.Use(EnumTransactionType_TransactionTypeCardLink)
	return EnumTransactionType_TransactionTypeCardLink
}

func (u *EnumTransactionTypeUsage) EnumTransactionType_TransactionTypeRefund() EnumTransactionType {
	u.Use(EnumTransactionType_TransactionTypeRefund)
	return EnumTransactionType_TransactionTypeRefund
}

func (u *EnumTransactionTypeUsage) EnumTransactionType_TransactionTypeGooglePay() EnumTransactionType {
	u.Use(EnumTransactionType_TransactionTypeGooglePay)
	return EnumTransactionType_TransactionTypeGooglePay
}

func (u *EnumTransactionTypeUsage) EnumTransactionType_TransactionTypeTwoStagePayIn() EnumTransactionType {
	u.Use(EnumTransactionType_TransactionTypeTwoStagePayIn)
	return EnumTransactionType_TransactionTypeTwoStagePayIn
}
