edition = "2023";

package epay;

option go_package = "git.local/sensitive/innerpb/processing/rest";

import "mvp/proto/rest.proto";
import "mvp/proto/logger.proto";

service Epay {
  option(mvp.rest_service_options) = {
    hosts :[
      {
        stand: "local",
        base_uri: "https://testepay.homebank.kz/api"
      },
      {
        stand: "dev",
        base_uri: "https://testepay.homebank.kz/api"
      },
      {
        stand: "test",
        base_uri: "https://epay-api.homebank.kz"
      },
      {
        stand: "stage",
        base_uri: "https://epay-api.homebank.kz"
      },
      {
        stand: "prod",
        base_uri: "https://epay-api.homebank.kz"
      },
      {
        stand: "sandbox",
        base_uri: "https://testepay.homebank.kz/api"
      }
    ]
  };

  rpc ProcessPayment(EpayProcessPaymentRequest) returns (EpayProcessPaymentResponse) {
    option(mvp.rest_method_options) = {
      method: Post,
      path: "/payment/cryptopay",
      authorization: Bearer,
      request_marshal: JSON,
      response_unmarshal: JSON,
      max_request_timeout: 60,
    };
  }

  rpc ProcessOneClickPayment(EpayProcessOneClickPaymentRequest) returns (EpayProcessOneClickPaymentResponse) {
    option(mvp.rest_method_options) = {
      method: Post,
      path: "/payments/cards/auth",
      authorization: Bearer,
      request_marshal: JSON,
      response_unmarshal: JSON,
      max_request_timeout: 60,
    };
  }

  rpc ConfirmThreeDS(EpayConfirmThreeDSRequest) returns (EpayEmpty) {
    option(mvp.rest_method_options) = {
      method: Post,
      path: "/payment/confirm",
      authorization:Bearer,
      request_marshal: JSON,
    };
  }

  rpc GetStatusPayin(EpayEmpty) returns (EpayGetStatusPayinResponse) {
    option(mvp.rest_method_options) = {
      method: Get,
      path: "/check-status/payment/transaction/${invoice_id}",
      response_unmarshal: JSON,
      authorization: Bearer,
      max_request_timeout: 60,
    };
  }

  rpc GetStatusPayout(EpayEmpty) returns (EpayGetStatusPayoutResponse) {
    option(mvp.rest_method_options) = {
      method: Get,
      path: "/check-status/p2p/transaction/${order}",
      response_unmarshal: JSON,
      authorization: Bearer,
      max_request_timeout: 60,
    };
  }

  rpc Refund(EpayEmpty) returns (EpayRefundResponse) {
    option(mvp.rest_method_options) = {
      method: Post,
      path: "/operation/${bank_order_id}/refund",
      response_unmarshal: JSON,
      max_request_timeout: 60,
      authorization: Bearer,
      query_param: "amount"
    };
  }

  rpc ProcessPayOut(EpayProcessPayoutRequest) returns (EpayProcessPayoutResponse) {
    option(mvp.rest_method_options) = {
      method: Post,
      path: "/chpayment/transfer"
      max_request_timeout: 60,
      request_marshal: JSON,
      response_unmarshal: JSON,
      authorization: Bearer,
    };
  }

  rpc Charge(EpayEmpty) returns (EpayChargeResponse) {
    option(mvp.rest_method_options) = {
      method: Post,
      path: "/operation/${id}/charge",
      max_request_timeout: 60,
      response_unmarshal: JSON,
      authorization: Bearer,
      query_param: "amount"
    };
  }

  rpc Cancel(EpayEmpty) returns (EpayCancelResponse) {
    option(mvp.rest_method_options) = {
      method: Post,
      path: "/operation/${id}/cancel",
      max_request_timeout: 60,
      response_unmarshal: JSON,
      authorization: Bearer,
    };
  }

  rpc ApplePay(EpayApplePayRequest) returns (EpayApplePayResponse) {
    option(mvp.rest_method_options) = {
      method: Post,
      path: "/payment/cryptopay",
      max_request_timeout: 60,
      request_marshal: JSON,
      response_unmarshal: JSON,
      authorization: Bearer,
    };
  }

  rpc GooglePay(EpayGooglePayRequest) returns (EpayGooglePayResponse) {
    option(mvp.rest_method_options) = {
      method: Post,
      path: "/payment/cryptopay",
      max_request_timeout: 60,
      request_marshal: JSON,
      response_unmarshal: JSON,
      authorization: Bearer,
    };
  }
}

message EpaySecure3D {
  string action = 1;
  string md = 2 [(mvp.FieldLoggerLevel) = Hidden];
  string paReq = 3;
}

message EpayProcessPaymentRequest {
  message ProcessPaymentRequest {
    string accountId = 1;
    double amount = 2;
    bool cardSave = 3;
    string cryptogram = 4 [(mvp.FieldLoggerLevel) = Hidden];
    string currency = 5;
    string description = 6;
    string email = 7 [(mvp.FieldLoggerLevel) = Hidden];
    string invoiceId = 8;
    string name = 9 [(mvp.FieldLoggerLevel) = Hidden];
    string phone = 10 [(mvp.FieldLoggerLevel) = Hidden];
  }

  ProcessPaymentRequest body = 1;
}

message EpayProcessPaymentResponse {
  ProcessPaymentResponse payment_response = 1;

  message ProcessPaymentResponse {
    string id = 1;
    string accountId = 2;
    double amount = 3;
    double amountBonus = 4;
    string currency = 5;
    string description = 6;
    string email = 7 [(mvp.FieldLoggerLevel) = Hidden];
    string invoiceID = 8;
    string language = 9;
    string phone = 10 [(mvp.FieldLoggerLevel) = Hidden];
    string reference = 11;
    string intReference = 12;
    EpaySecure3D secure3D = 13;
    string cardID = 14 [(mvp.FieldLoggerLevel) = Hidden];
    double fee = 15;
    string approvalCode = 16;
    int32 code = 17;
    string message = 18;
    string status = 19;
    string secureDetails = 20;
    string qrReference = 21;
    string ip = 22;
    string ipCity = 23;
    string ipCountry = 24;
    string ipDistrict = 25;
    double ipLatitude = 26;
    double ipLongitude = 27;
    string ipRegion = 28;
    string issuerBankCountry = 29;
    bool isCredit = 30;
    string invoiceIdAlt = 31;
  }
}

message EpayProcessOneClickPaymentRequest {
  ProcessOneClickPaymentRequest body = 1;

  message ProcessOneClickPaymentRequest {
    string accountId = 1;
    double amount = 2;
    CardID cardId = 3;
    string currency = 4;
    string email = 5 [(mvp.FieldLoggerLevel) = Hidden];
    string invoiceId = 6;
    string name = 7 [(mvp.FieldLoggerLevel) = Hidden];
    string paymentType = 8;
    string phone = 9 [(mvp.FieldLoggerLevel) = Hidden];
    string terminalId = 10 [(mvp.FieldLoggerLevel) = Hidden];
  }

  message CardID {
    string id = 1 [(mvp.FieldLoggerLevel) = Hidden];
  }
}

message EpayProcessOneClickPaymentResponse {
  ProcessOneClickPaymentResponse one_click_response = 1;

  message ProcessOneClickPaymentResponse {
    string id = 1;
    string accountId = 2;
    double amount = 3;
    double amountBonus = 4;
    string currency = 5;
    string description = 6;
    string email = 7 [(mvp.FieldLoggerLevel) = Hidden];
    string invoiceID = 8;
    string invoiceIdAlt = 9;
    string language = 10;
    string phone = 11 [(mvp.FieldLoggerLevel) = Hidden];
    string reference = 12;
    string intReference = 13;
    EpaySecure3D secure3D = 14;
    string cardID = 15 [(mvp.FieldLoggerLevel) = Hidden];
    int32 code = 16;
    string status = 17;
    string message = 18;
  }
}

message EpayConfirmThreeDSRequest {
  message ConfirmThreeDSRequest {
    string ID = 1;
    string MD = 2 [(mvp.FieldLoggerLevel) = Hidden];
    string PaRes = 3 [(mvp.FieldLoggerLevel) = Hidden];
  }

  ConfirmThreeDSRequest body = 1;
}

message EpayEmpty {}

message EpayGetStatusPayinResponse {
  GetStatusResponse status_response = 1;

  message GetStatusResponse {
    string resultCode = 1;
    string resultMessage = 2;
    Transaction transaction = 3;
  }

  message Transaction {
    string id = 1;
    string createdDate = 2;
    string invoiceID = 3;
    double amount = 4;
    double amountBonus = 5;
    double payoutAmount = 6;
    double orgAmount = 7;
    string approvalCode = 8;
    string data = 9;
    string currency = 10;
    string terminal = 11 [(mvp.FieldLoggerLevel) = Hidden];
    string terminalID = 12 [(mvp.FieldLoggerLevel) = Hidden];
    string accountID = 13;
    string description = 14;
    string language = 15;
    string cardMask = 16;
    string cardType = 17;
    string issuer = 18;
    string reference = 19;
    string reason = 20;
    string reasonCode = 21;
    string intReference = 22;
    bool secure = 23;
    string secureDetails = 24;
    string statusID = 25;
    string statusName = 26;
    string name = 27 [(mvp.FieldLoggerLevel) = Hidden];
    string email = 28 [(mvp.FieldLoggerLevel) = Hidden];
    string phone = 29 [(mvp.FieldLoggerLevel) = Hidden];
    string cardID = 30 [(mvp.FieldLoggerLevel) = Hidden];
    string xlsRRN = 31;
    string ip = 32;
    string ipCountry = 33;
    string ipCity = 34;
    string ipRegion = 35;
    string ipDistrict = 36;
    double ipLatitude = 37;
    double ipLongitude = 38;
  }
}

message EpayGetStatusPayoutResponse {
  GetStatusPayoutResponse status_response = 1;
  message GetStatusPayoutResponse {
    Result result = 1;
    Transaction transaction = 2;
  }

  message Result {
    string code = 1;
    string message = 2;
  }

  message Transaction {
    P2P p2p = 1;
    Sender sender = 2;
    Receiver receiver = 3;

    message P2P {
      string id = 1;
      string createdDate = 2;
      string invoiceID = 3;
      double amount = 4;
      double amountBonus = 5;
      double payoutAmount = 6;
      string currency = 7;
      string cardType = 8;
      string terminal = 9 [(mvp.FieldLoggerLevel) = Hidden];
      string terminalID = 10 [(mvp.FieldLoggerLevel) = Hidden];
      string accountID = 11;
      string description = 12;
      string language = 13;
      string reference = 14;
      string intReference = 15;
      bool secure = 16;
      string statusName = 17;
      string reason = 18;
      int32 reasonCode = 19;
      string email = 20 [(mvp.FieldLoggerLevel) = Hidden];
      string phone = 21 [(mvp.FieldLoggerLevel) = Hidden];
      string ip = 22;
      string ipCountry = 23;
      string ipCity = 24;
      string ipRegion = 25;
      string ipDistrict = 26;
      double ipLongitude = 27;
      double ipLatitude = 28;
    }

    message Sender {
      string Name = 1 [(mvp.FieldLoggerLevel) = Hidden];
      string panMask = 2 [(mvp.FieldLoggerLevel) = Hidden];
      string cardIssuer = 3 [(mvp.FieldLoggerLevel) = Hidden];
      string cardType = 4 [(mvp.FieldLoggerLevel) = Hidden];
      string cardID = 5 [(mvp.FieldLoggerLevel) = Hidden];
    }

    message Receiver {
      string Name = 1 [(mvp.FieldLoggerLevel) = Hidden];
      string panMask = 2 [(mvp.FieldLoggerLevel) = Hidden];
      string cardIssuer = 3 [(mvp.FieldLoggerLevel) = Hidden];
      string cardType = 4 [(mvp.FieldLoggerLevel) = Hidden];
      string cardID = 5 [(mvp.FieldLoggerLevel) = Hidden];
    }
  }
}

message EpayRefundResponse {
  message RefundResponse {
    int32 code = 1;
    string status = 2;
    string message = 3;
  }

  RefundResponse refund_response = 1;
}

message EpayProcessPayoutRequest {
  ProcessPayoutRequest body = 1;

  message ProcessPayoutRequest {
    Card card = 1;
    Order order = 2;
  }

  message Order {
    double amount = 1;
    string currency = 2;
    string description = 3;
    bool foreign = 4;
    string id = 5;
    string merchantIdForSavingCards = 6;
    string senderEmail = 7 [(mvp.FieldLoggerLevel) = Hidden];
    string senderIP = 8;
    string terminalId = 9 [(mvp.FieldLoggerLevel) = Hidden];
  }

  message Card {
    Receiver receiver = 1;

    message Receiver {
      string cardCred = 1 [(mvp.FieldLoggerLevel) = Hidden];
      string id = 2;
      bool save = 3;
      string transferType = 4;
    }
  }
}

message EpayProcessPayoutResponse {
  ProcessPayoutResponse payout_response = 1;

  message ProcessPayoutResponse {
    string id = 1;
    double amount = 2;
    string currency = 3;
    string email = 4 [(mvp.FieldLoggerLevel) = Hidden];
    string description = 5;
    string reference = 6;
    string orderID = 7;
    string senderCardID = 8 [(mvp.FieldLoggerLevel) = Hidden];
    string senderCardPAN = 9 [(mvp.FieldLoggerLevel) = Hidden];
    string receiverCardID = 10 [(mvp.FieldLoggerLevel) = Hidden];
    string receiverCardPAN = 11 [(mvp.FieldLoggerLevel) = Hidden];
    string intReference = 12;
    string terminalID = 13 [(mvp.FieldLoggerLevel) = Hidden];
    int32 code = 14;
    string message = 15;
    string status = 16;
    string accountId = 17;
  }
}

message EpayChargeResponse {
  ChargeResponse charge_response = 1;

  message ChargeResponse {
    int32 code = 1;
    string message = 2;
  }
}

message EpayCancelResponse {
  CancelResponse cancel_response = 1;

  message CancelResponse {
    int32 code = 1;
    string message = 2;
  }
}

message EpayApplePayRequest {
  ApplePayRequest body = 1;

  message ApplePayRequest {
    double amount = 1;
    string cryptogram = 2;
    bytes cryptogramApplePay = 3 [(mvp.FieldLoggerLevel) = Hidden];
    string currency = 4 [(mvp.FieldLoggerLevel) = Hidden];
    string description = 5;
    string email = 6 [(mvp.FieldLoggerLevel) = Hidden];
    string invoiceId = 7;
    string name = 8 [(mvp.FieldLoggerLevel) = Hidden];
    string paymentType = 9;
  }
}

message EpayApplePayResponse {
  ApplePayResponse apple_pay_response = 1;

  message ApplePayResponse {
    string id = 1;
    string accountId = 2;
    double amount = 3;
    double amountBonus = 4;
    string currency = 5;
    string description = 6;
    string email = 7 [(mvp.FieldLoggerLevel) = Hidden];
    string invoiceID = 8;
    string language = 9;
    string phone = 10 [(mvp.FieldLoggerLevel) = Hidden];
    string reference = 11;
    string intReference = 12;
    EpaySecure3D secure3D = 13;
    string cardID = 14 [(mvp.FieldLoggerLevel) = Hidden];
    double fee = 15;
    string approvalCode = 16;
    int32 code = 17;
    string status = 18;
    string secureDetails = 19;
    string qrReference = 20;
    string ip = 21;
    string ipCity = 22;
    string ipCountry = 23;
    string ipDistrict = 24;
    string ipRegion = 25;
    double ipLatitude = 26;
    double ipLongitude = 27;
    string issuerBankCountry = 28;
    bool isCredit = 29;
    string message = 30;
    string invoiceIdAlt = 31;
  }
}

message EpayGooglePayRequest {
  GooglePayRequest body = 1;

  message GooglePayRequest {
    double amount = 1;
    string cryptogram = 2;
    string currency = 3 [(mvp.FieldLoggerLevel) = Hidden];
    string description = 4 [(mvp.FieldLoggerLevel) = Hidden];
    string email = 5;
    string invoiceId = 6;
    string name = 7 [(mvp.FieldLoggerLevel) = Hidden];
    string paymentType = 8;
    string phone = 9 [(mvp.FieldLoggerLevel) = Hidden];
  }
}

message EpayGooglePayResponse {
  GooglePayResponse google_pay_response = 1;

  message GooglePayResponse {
    string id = 1;
    string accountId = 2;
    double amount = 3;
    double amountBonus = 4;
    string currency = 5;
    string description = 6;
    string email = 7 [(mvp.FieldLoggerLevel) = Hidden];
    string invoiceID = 8;
    string language = 9;
    string phone = 10 [(mvp.FieldLoggerLevel) = Hidden];
    string reference = 11;
    string intReference = 12;
    EpaySecure3D secure3D = 13;
    string cardID = 14 [(mvp.FieldLoggerLevel) = Hidden];
    double fee = 15;
    string approvalCode = 16;
    int32 code = 17;
    string status = 18;
    string secureDetails = 19;
    string qrReference = 20;
    string ip = 21;
    string ipCity = 22;
    string ipCountry = 23;
    string ipDistrict = 24;
    double ipLatitude = 25;
    double ipLongitude = 26;
    string ipRegion = 27;
    string issuerBankCountry = 28;
    bool isCredit = 29;
    string message = 30;
    string invoiceIdAlt = 31;
  }
}