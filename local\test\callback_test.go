package test

import (
	"context"
	"net/http"
	"testing"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/stretchr/testify/assert"

	"git.local/sensitive/local/internal/domain"
)

func TestCallback(t *testing.T) {
	t.Skip()

	expected := domain.MerchantWebhookBase{
		ProjectID:       42,
		MerchantID:      1,
		ProjectClientID: "kambar6122",
		//CardToken:       "AAAAAAAAAAAAAAAAAAAAAIxq5RruaVd+t8379wbp7mS4cvADWZ+jPyejQmwnm/mafC5wadzAmuTPBOAfYTXwkY+0annLttTVxj41ho5vEw6Lkr3NhPq9YMMNp5TZI/ySWZMMWAwSRJ3VjVSAEeiA0w==",
		TypeCode:   "in",
		StatusCode: "success",
	}

	ch := make(chan domain.MerchantWebhookBase, 3)

	go func() {
		r := gin.New()

		r.POST("/general", func(c *gin.Context) {

			var res domain.MerchantWebhookBase
			if err := c.ShouldBind<PERSON>(&res); err != nil {
				t.Error(err)
				return
			}

			ch <- res

			c.J<PERSON>(http.StatusOK, gin.H{"general": "ok"})
		})

		if err := r.Run("localhost:5050"); err != nil {
			t.Error(err)
			return
		}
	}()

	//making payin
	transactionId, hash := generatePayInTransaction(t, &domain.GenerateTransactionRequest{
		Amount:             10,
		ProjectID:          devProjectID,
		MerchantID:         devMerchantID,
		ProjectClientID:    devProjectClientID,
		FailureRedirectUrl: "http://host.docker.internal:5050/success",
		SuccessRedirectUrl: "http://host.docker.internal:5050/fail",
		CallbackUrl:        "http://host.docker.internal:5050/general",
		Description:        "afk_afk_afk",
		ProjectReferenceID: generateSecretKey(),
	}, domain.SmokeData[string]{
		Expected: domain.Expected[string]{
			HttpStatusCode: http.StatusOK,
		},
	})
	makePayIn(t, &domain.MakePayInRequest{
		TransactionID:   transactionId,
		TransactionHash: hash,
		UserEmail:       "<EMAIL>",
		UserPhone:       "+77771581078", // dont call pls
		EncryptedCard:   devBccCard,
	}, &domain.Expected[*domain.MakePayInResponse]{
		HttpStatusCode: http.StatusOK,
		Data: &domain.MakePayInResponse{
			TransactionID: transactionId,
		},
	})

	ctx, cancel := context.WithTimeout(context.Background(), 15*time.Second)
	defer cancel()

	select {
	case res := <-ch:
		assert.NotEmpty(t, res.TransactionID, "transaction id is empty")
		assert.NotEmpty(t, res.CreatedAt, "created_at is empty")
		assert.NotEmpty(t, res.TransactionID, "transaction_id is empty")
		assert.NotEmpty(t, res.FinishedAt, "finished_at is empty")
		assert.NotEmpty(t, res.ProjectReferenceID, "project_reference_id is empty")
		assert.NotEmpty(t, res.MerchantID, "merchant_id is empty")
		assert.NotEmpty(t, res.Description, "description is empty")
		assert.NotEmpty(t, res.TypeCode, "type_code is empty")
		assert.NotEmpty(t, res.StatusCode, "status_code is empty")

		assert.Equal(t, transactionId, res.TransactionID, "transaction_id incorrect")
		assert.Equal(t, expected.StatusCode, res.StatusCode, "status code incorrect")
		assert.Equal(t, expected.ProjectClientID, res.ProjectClientID, "project client id incorrect")
		assert.Equal(t, expected.MerchantID, res.MerchantID, "merchant id incorrect")
		assert.Equal(t, expected.TypeCode, res.TypeCode, "type code incorrect")
	case <-ctx.Done():
		t.Fatal("no callback has been received")
		return
	}
}
