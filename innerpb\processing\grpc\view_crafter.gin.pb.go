// Code generated by protoc-gen-go-mvp. DO NOT EDIT.
// versions:
// - protoc-gen-go-mvp v2.0.1
// created by mvp authors

package grpc

import (
	dog "git.local/sensitive/sdk/dog"
	gin "github.com/gin-gonic/gin"
	metadata "google.golang.org/grpc/metadata"
	http "net/http"
)

func RegisterGinViewCrafterRouterGroup(
	in *gin.RouterGroup,
	handler func(func(c *gin.Context) error) gin.HandlerFunc,
) error {
	service, err := NewGinViewCrafterService()
	if err != nil {
		return err
	}

	routerGroup := in.Group("processing.view_crafter.view_crafter.ViewCrafter")
	routerGroup.PUT("/GetProjectFormInfoV1", handler(service.GetProjectFormInfoV1))
	return nil
}

func NewGinViewCrafterService() (GinViewCrafterServer, error) {
	client, err := NewPreparedViewCrafterClient()
	if err != nil {
		return nil, err
	}

	return &ginViewCrafterServer{
		client: NewLoggedViewCrafterClient(
			NewIamViewCrafterClient(client),
		),
	}, nil
}

//go:generate mockgen -destination=./grpcmock/view_crafter.gin.pb.go -package=grpcmock -source=view_crafter.gin.pb.go GinViewCrafterServer
type GinViewCrafterServer interface {
	GetProjectFormInfoV1(c *gin.Context) error
}

var _ GinViewCrafterServer = (*ginViewCrafterServer)(nil)

type ginViewCrafterServer struct {
	client ViewCrafterClient
}

type ViewCrafter_GetProjectFormInfoV1_Success struct {
	// Status is the status of the response. Set true
	Status bool `json:"status"`
	// Message is a human-readable explanation of the status
	Message string `json:"message"`
	// StatusCode is the GoErr status. Set 0
	StatusCode int `json:"status_code"`
	// Result is the result of the request
	Result *GetProjectFormInfoResV1 `json:"result"`
}

type ViewCrafter_GetProjectFormInfoV1_Failure struct {
	// Status is the status of the response. Set false
	Status bool `json:"status"`
	// Message is a human-readable explanation of the status
	Message string `json:"message"`
	// StatusCode is the GoErr status
	StatusCode int `json:"status_code"`
}

// GetProjectFormInfoV1
// @Summary GetProjectFormInfoV1
// @Security bearerAuth
// @ID ViewCrafter_GetProjectFormInfoV1
// @Accept json
// @Param request body GetProjectFormInfoReqV1 true "GetProjectFormInfoReqV1"
// @Success 200 {object} ViewCrafter_GetProjectFormInfoV1_Success
// @Failure 401 {object} ViewCrafter_GetProjectFormInfoV1_Failure "goerrpkg.ErrorTypeAuthorization"
// @Failure 404 {object} ViewCrafter_GetProjectFormInfoV1_Failure "goerrpkg.ErrorTypeNotFound"
// @Failure 400 {object} ViewCrafter_GetProjectFormInfoV1_Failure "goerrpkg.ErrorTypeBadRequest"
// @Failure 500 {object} ViewCrafter_GetProjectFormInfoV1_Failure "goerrpkg.ErrorTypeUnexpectedError"
// @Failure 400 {object} ViewCrafter_GetProjectFormInfoV1_Failure "gin.ErrorTypeBind"
// @Failure 500 {object} ViewCrafter_GetProjectFormInfoV1_Failure "Undefined error"
// @Produce json
// @Router /processing.view_crafter.view_crafter.ViewCrafter/GetProjectFormInfoV1 [put]
// @tags ViewCrafter
func (s *ginViewCrafterServer) GetProjectFormInfoV1(c *gin.Context) error {
	ctx, span := dog.CreateSpan(c.Request.Context(), "GinViewCrafterServer_GetProjectFormInfoV1")
	defer span.End()

	var request GetProjectFormInfoReqV1
	if err := c.ShouldBindJSON(&request); err != nil {
		return err
	}

	ctx = metadata.NewOutgoingContext(ctx, metadata.MD(c.Request.Header))

	response, err := s.client.GetProjectFormInfoV1(ctx, &request)
	if err != nil {
		return err
	}

	c.JSON(http.StatusOK, &ViewCrafter_GetProjectFormInfoV1_Success{
		Status:     true,
		Message:    "Success",
		StatusCode: 0,
		Result:     response,
	})
	return nil
}
