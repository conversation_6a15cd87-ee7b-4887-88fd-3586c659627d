// Code generated by MockGen. DO NOT EDIT.
// Source: multiacquiring_balance_grpc.pb.go

// Package grpcmock is a generated GoMock package.
package grpcmock

import (
	context "context"
	reflect "reflect"

	grpc "git.local/sensitive/innerpb/processing/grpc"
	gomock "github.com/golang/mock/gomock"
	grpc0 "google.golang.org/grpc"
)

// MockMultiacquiringBalanceClient is a mock of MultiacquiringBalanceClient interface.
type MockMultiacquiringBalanceClient struct {
	ctrl     *gomock.Controller
	recorder *MockMultiacquiringBalanceClientMockRecorder
}

// MockMultiacquiringBalanceClientMockRecorder is the mock recorder for MockMultiacquiringBalanceClient.
type MockMultiacquiringBalanceClientMockRecorder struct {
	mock *MockMultiacquiringBalanceClient
}

// NewMockMultiacquiringBalanceClient creates a new mock instance.
func NewMockMultiacquiringBalanceClient(ctrl *gomock.Controller) *MockMultiacquiringBalanceClient {
	mock := &MockMultiacquiringBalanceClient{ctrl: ctrl}
	mock.recorder = &MockMultiacquiringBalanceClientMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockMultiacquiringBalanceClient) EXPECT() *MockMultiacquiringBalanceClientMockRecorder {
	return m.recorder
}

// CheckBalance mocks base method.
func (m *MockMultiacquiringBalanceClient) CheckBalance(ctx context.Context, in *grpc.CheckBalanceRequest, opts ...grpc0.CallOption) (*grpc.CheckBalanceResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "CheckBalance", varargs...)
	ret0, _ := ret[0].(*grpc.CheckBalanceResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CheckBalance indicates an expected call of CheckBalance.
func (mr *MockMultiacquiringBalanceClientMockRecorder) CheckBalance(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CheckBalance", reflect.TypeOf((*MockMultiacquiringBalanceClient)(nil).CheckBalance), varargs...)
}

// MockMultiacquiringBalanceServer is a mock of MultiacquiringBalanceServer interface.
type MockMultiacquiringBalanceServer struct {
	ctrl     *gomock.Controller
	recorder *MockMultiacquiringBalanceServerMockRecorder
}

// MockMultiacquiringBalanceServerMockRecorder is the mock recorder for MockMultiacquiringBalanceServer.
type MockMultiacquiringBalanceServerMockRecorder struct {
	mock *MockMultiacquiringBalanceServer
}

// NewMockMultiacquiringBalanceServer creates a new mock instance.
func NewMockMultiacquiringBalanceServer(ctrl *gomock.Controller) *MockMultiacquiringBalanceServer {
	mock := &MockMultiacquiringBalanceServer{ctrl: ctrl}
	mock.recorder = &MockMultiacquiringBalanceServerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockMultiacquiringBalanceServer) EXPECT() *MockMultiacquiringBalanceServerMockRecorder {
	return m.recorder
}

// CheckBalance mocks base method.
func (m *MockMultiacquiringBalanceServer) CheckBalance(arg0 context.Context, arg1 *grpc.CheckBalanceRequest) (*grpc.CheckBalanceResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CheckBalance", arg0, arg1)
	ret0, _ := ret[0].(*grpc.CheckBalanceResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CheckBalance indicates an expected call of CheckBalance.
func (mr *MockMultiacquiringBalanceServerMockRecorder) CheckBalance(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CheckBalance", reflect.TypeOf((*MockMultiacquiringBalanceServer)(nil).CheckBalance), arg0, arg1)
}

// mustEmbedUnimplementedMultiacquiringBalanceServer mocks base method.
func (m *MockMultiacquiringBalanceServer) mustEmbedUnimplementedMultiacquiringBalanceServer() {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "mustEmbedUnimplementedMultiacquiringBalanceServer")
}

// mustEmbedUnimplementedMultiacquiringBalanceServer indicates an expected call of mustEmbedUnimplementedMultiacquiringBalanceServer.
func (mr *MockMultiacquiringBalanceServerMockRecorder) mustEmbedUnimplementedMultiacquiringBalanceServer() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "mustEmbedUnimplementedMultiacquiringBalanceServer", reflect.TypeOf((*MockMultiacquiringBalanceServer)(nil).mustEmbedUnimplementedMultiacquiringBalanceServer))
}

// MockUnsafeMultiacquiringBalanceServer is a mock of UnsafeMultiacquiringBalanceServer interface.
type MockUnsafeMultiacquiringBalanceServer struct {
	ctrl     *gomock.Controller
	recorder *MockUnsafeMultiacquiringBalanceServerMockRecorder
}

// MockUnsafeMultiacquiringBalanceServerMockRecorder is the mock recorder for MockUnsafeMultiacquiringBalanceServer.
type MockUnsafeMultiacquiringBalanceServerMockRecorder struct {
	mock *MockUnsafeMultiacquiringBalanceServer
}

// NewMockUnsafeMultiacquiringBalanceServer creates a new mock instance.
func NewMockUnsafeMultiacquiringBalanceServer(ctrl *gomock.Controller) *MockUnsafeMultiacquiringBalanceServer {
	mock := &MockUnsafeMultiacquiringBalanceServer{ctrl: ctrl}
	mock.recorder = &MockUnsafeMultiacquiringBalanceServerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockUnsafeMultiacquiringBalanceServer) EXPECT() *MockUnsafeMultiacquiringBalanceServerMockRecorder {
	return m.recorder
}

// mustEmbedUnimplementedMultiacquiringBalanceServer mocks base method.
func (m *MockUnsafeMultiacquiringBalanceServer) mustEmbedUnimplementedMultiacquiringBalanceServer() {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "mustEmbedUnimplementedMultiacquiringBalanceServer")
}

// mustEmbedUnimplementedMultiacquiringBalanceServer indicates an expected call of mustEmbedUnimplementedMultiacquiringBalanceServer.
func (mr *MockUnsafeMultiacquiringBalanceServerMockRecorder) mustEmbedUnimplementedMultiacquiringBalanceServer() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "mustEmbedUnimplementedMultiacquiringBalanceServer", reflect.TypeOf((*MockUnsafeMultiacquiringBalanceServer)(nil).mustEmbedUnimplementedMultiacquiringBalanceServer))
}
