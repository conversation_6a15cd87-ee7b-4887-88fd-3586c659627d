package database

import (
	"golang.org/x/net/context"

	"git.local/sensitive/pkg/middlewares"
	"git.local/sensitive/processing/acquirer/model"
	"git.local/sensitive/processing/acquirer/schema"
)

const (
	ForeignKeyViolationCode = "23503"
)

//go:generate mockgen -package=databasemocks -destination=./databasemocks/mocks.go -source=database.go *
type Banker interface {
	Create(ctx context.Context, bank *model.Bank) error
	GetAll(
		ctx context.Context,
		pagination *middlewares.PaginationInfo,
		filter schema.BankFilter,
	) ([]*model.Bank, error)
	GetById(ctx context.Context, id uint64) (bank *model.Bank, err error)
	Update(ctx context.Context, id uint64, data *model.Bank) error
	Delete(ctx context.Context, id uint64) error
	GetByName(ctx context.Context, name string, pagination *middlewares.PaginationInfo) (_ []*model.Bank, err error)
}

type BankBiner interface {
	Create(ctx context.Context, bankBin model.BankBin) error
	GetAll(ctx context.Context, pagination *middlewares.PaginationInfo) ([]*model.BankBin, error)
	Update(ctx context.Context, id uint64, data model.BankBin) error
	Delete(ctx context.Context, id uint64) error
	GetByBin(ctx context.Context, eightDigitBin, sixDigitBin, fiveDigitBin string) (*model.BankBin, error)
}

type Ipser interface {
	Create(ctx context.Context, ips *model.Ips) error
	GetAll(ctx context.Context, pagination *middlewares.PaginationInfo) ([]*model.Ips, error)
	Update(ctx context.Context, id uint64, data *model.Ips) error
	Delete(ctx context.Context, id uint64) error
	GetByID(ctx context.Context, id uint64) (model.Ips, error)
}

type Countrier interface {
	Create(ctx context.Context, country *model.Country) error
	GetAll(ctx context.Context, pagination *middlewares.PaginationInfo) ([]*model.Country, error)
	GetByName(ctx context.Context, name string, pagination *middlewares.PaginationInfo) ([]*model.CountryBasic, error)
	Update(ctx context.Context, id uint64, data *model.Country) error
	Delete(ctx context.Context, id uint64) error
	GetByID(ctx context.Context, id uint64) (model.Country, error)
}

type Ruler interface {
	Create(ctx context.Context, rule *model.Rule) (*model.Rule, error)
	GetListWithBalancer(
		ctx context.Context,
		pagination *middlewares.PaginationInfo,
		request schema.RuleListRequest,
	) (rules []*model.Rule, err error)
	FindActiveRulesByProject(ctx context.Context, projectId uint64, paymentType uint64) (model.Rules, error)
	FindByAllParam(ctx context.Context, rule *model.Rule, isActive *bool) ([]*model.Rule, error)
	GetBaseRuleByProject(ctx context.Context, projectId uint64, paymentType uint64) (*model.Rule, error)
	GetByID(ctx context.Context, id uint64) (*model.Rule, error)
}

type RuleWeighter interface {
	GetNextRuleByWeight(ctx context.Context, rule *model.Rule) (*model.Rule, error)
	GetPreviousRuleByWeight(ctx context.Context, rule *model.Rule) (*model.Rule, error)
	ChangeRuleWeightByID(ctx context.Context, ruleID uint64, weight uint8) error
}

type RuleActivator interface {
	Activate(ctx context.Context, ruleID uint64) error
	Deactivate(ctx context.Context, ruleID uint64) error
}

type RulePercentager interface {
	Create(ctx context.Context, balancers []*model.RulePercentage) error
	GetAllByRuleID(ctx context.Context, ruleID uint64) (model.RulePercentages, error)
	Delete(ctx context.Context, ruleID uint64) error
}

type AcquirerBasicer interface {
	GetAllByFilter(
		ctx context.Context,
		filter schema.AcquirerFilter,
		pagination *middlewares.PaginationInfo,
	) ([]*model.Acquirer, error)
	GetAllActive(ctx context.Context, pagination *middlewares.PaginationInfo) ([]*model.Acquirer, error)
	Create(ctx context.Context, acquirer *model.Acquirer) error
	Update(ctx context.Context, id uint64, data *model.Acquirer) error
	GetByCode(ctx context.Context, acquirerCode string) (*model.Acquirer, error)
	GetByID(ctx context.Context, acquirerID uint64) (*model.Acquirer, error)
}

type Acquirer interface {
	GetAcquirersById(ctx context.Context, acquirerIds []uint64) ([]*model.Acquirer, error)
	UpdateStatus(ctx context.Context, id uint64, isActive bool) error
}

type Terminaler interface {
	GetAllByPaymentType(ctx context.Context, projectID, paymentType uint64) ([]*model.Terminal, error)
	GetAllPaymentTypes(ctx context.Context, projectID uint64) ([]*model.ResponsePaymentType, error)
	UpdateByAcquirerAndStatus(
		ctx context.Context,
		acquirerId uint64,
		searchStatus model.TerminalStatus,
		data *model.Terminal,
	) (count int64, total int, err error)
	FindActiveTerminalsByProject(ctx context.Context, projectID uint64, paymentType uint64) (model.Terminals, error)
	FindActiveTerminalsByIDs(
		ctx context.Context,
		ids []uint64,
		projectID uint64,
		transactionTypeID uint64,
	) (model.Terminals, error)
	UpdateConfig(ctx context.Context, id uint64, encryptedConfig string) error
}

type TerminalBasicer interface {
	Create(ctx context.Context, terminal *model.Terminal) (*model.Terminal, error)
	GetAll(ctx context.Context, projectID uint64) ([]model.Terminal, error)
	UpdateStatus(ctx context.Context, id uint64, status model.TerminalStatus) error
	GetAllInfoByID(ctx context.Context, id uint64) (*model.Terminal, error)
	GetByFilters(
		ctx context.Context,
		filters schema.FiltersTerminalRequest,
		pagination *middlewares.PaginationInfo,
	) ([]model.Terminal, error)
	GetByAcquirers(ctx context.Context, acquirers []*model.Acquirer) ([]*model.Terminal, error)
}

type TerminalTwoStager interface {
	UpdateTimeout(ctx context.Context, id uint64, timeout uint32) error
}

type TerminalBalancer interface {
	GetTerminals(ctx context.Context, projectID, transactionTypeID uint64) (model.Terminals, error)
	GetExactTerminal(ctx context.Context, projectID, transcationTypeID, acquirerID uint64) (model.Terminal, error)
	GetTerminalsByIDs(ctx context.Context, terminalIDs []uint64) ([]model.Terminal, error)
}

type Issuer interface {
	GetAll(
		ctx context.Context,
		pagination *middlewares.PaginationInfo,
	) ([]*model.BankBins, error)
	GetByID(ctx context.Context, id uint64) (model.Bank, error)
}

type CountryBanker interface {
	Create(ctx context.Context, countryBank *model.CountryBank) error
	Delete(ctx context.Context, countryId uint64, currencyId uint64) error
	GetCountriesByBank(
		ctx context.Context,
		bankId uint64,
	) ([]*model.Country, error)
	GetBanksByCountryID(
		ctx context.Context,
		countryID uint64,
	) ([]*model.Bank, error)
}

type TerminalProjecter interface {
	Create(ctx context.Context, terminalProject *model.TerminalProject) error
	Update(ctx context.Context, id uint64, request schema.UpdateTerminalProjectRequest) error
	UpdateStatus(ctx context.Context, id uint64, request schema.UpdateTerminalProjectStatusRequest) error
	GetByID(ctx context.Context, id uint64) (*model.TerminalProject, error)
	GetByProjectID(ctx context.Context, projectID uint64) ([]*model.TerminalProject, error)
}
