edition = "2023";

package processing.eosi.eosi;

option go_package = "git.local/sensitive/innerpb/processing/grpc";

import "inner/processing/grpc/multiordering.proto";

service Eosi {
  rpc GetPaymentOrder(processing.multiordering.multiordering.GetPaymentOrderRequest) returns (processing.multiordering.multiordering.GetPaymentOrderResponse) {}
  rpc GetOrderingIdentifier(processing.multiordering.multiordering.GetOrderingIdentifierRequest) returns (processing.multiordering.multiordering.GetOrderingIdentifierResponse) {}
}
