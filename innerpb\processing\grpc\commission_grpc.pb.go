// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.5.1
// - protoc             v5.29.3
// source: inner/processing/grpc/commission.proto

package grpc

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.64.0 or later.
const _ = grpc.SupportPackageIsVersion9

const (
	Commission_CalculateAndSaveUpperCommission_FullMethodName  = "/processing.commission.commission.Commission/CalculateAndSaveUpperCommission"
	Commission_GetCommissionForMainBalance_FullMethodName      = "/processing.commission.commission.Commission/GetCommissionForMainBalance"
	Commission_UpdateCommissionForCreditBalance_FullMethodName = "/processing.commission.commission.Commission/UpdateCommissionForCreditBalance"
	Commission_CalculatePayInPrimalAmount_FullMethodName       = "/processing.commission.commission.Commission/CalculatePayInPrimalAmount"
	Commission_CalculatePayOutPrimalAmount_FullMethodName      = "/processing.commission.commission.Commission/CalculatePayOutPrimalAmount"
	Commission_FiscalizeUpperCommission_FullMethodName         = "/processing.commission.commission.Commission/FiscalizeUpperCommission"
	Commission_GetCommissionByTransactionID_FullMethodName     = "/processing.commission.commission.Commission/GetCommissionByTransactionID"
)

// CommissionClient is the client API for Commission service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type CommissionClient interface {
	CalculateAndSaveUpperCommission(ctx context.Context, in *CalculateAndSaveUpperCommissionRequestV1, opts ...grpc.CallOption) (*CalculateAndSaveUpperCommissionResponseV1, error)
	GetCommissionForMainBalance(ctx context.Context, in *GetCommissionForMainBalanceRequestV1, opts ...grpc.CallOption) (*GetCommissionForMainBalanceResponseV1, error)
	UpdateCommissionForCreditBalance(ctx context.Context, in *UpdateCommissionForCreditBalanceRequestV1, opts ...grpc.CallOption) (*UpdateCommissionForCreditBalanceResponseV1, error)
	CalculatePayInPrimalAmount(ctx context.Context, in *CalculatePrimalAmountRequestV1, opts ...grpc.CallOption) (*CalculatePrimalAmountResponseV1, error)
	CalculatePayOutPrimalAmount(ctx context.Context, in *CalculatePrimalAmountRequestV1, opts ...grpc.CallOption) (*CalculatePrimalAmountResponseV1, error)
	FiscalizeUpperCommission(ctx context.Context, in *FiscalizeUpperCommissionRequestV1, opts ...grpc.CallOption) (*emptypb.Empty, error)
	GetCommissionByTransactionID(ctx context.Context, in *GetCommissionByTransactionIDRequestV1, opts ...grpc.CallOption) (*GetCommissionByTransactionIDResponseV1, error)
}

type commissionClient struct {
	cc grpc.ClientConnInterface
}

func NewCommissionClient(cc grpc.ClientConnInterface) CommissionClient {
	return &commissionClient{cc}
}

func (c *commissionClient) CalculateAndSaveUpperCommission(ctx context.Context, in *CalculateAndSaveUpperCommissionRequestV1, opts ...grpc.CallOption) (*CalculateAndSaveUpperCommissionResponseV1, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(CalculateAndSaveUpperCommissionResponseV1)
	err := c.cc.Invoke(ctx, Commission_CalculateAndSaveUpperCommission_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *commissionClient) GetCommissionForMainBalance(ctx context.Context, in *GetCommissionForMainBalanceRequestV1, opts ...grpc.CallOption) (*GetCommissionForMainBalanceResponseV1, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetCommissionForMainBalanceResponseV1)
	err := c.cc.Invoke(ctx, Commission_GetCommissionForMainBalance_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *commissionClient) UpdateCommissionForCreditBalance(ctx context.Context, in *UpdateCommissionForCreditBalanceRequestV1, opts ...grpc.CallOption) (*UpdateCommissionForCreditBalanceResponseV1, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(UpdateCommissionForCreditBalanceResponseV1)
	err := c.cc.Invoke(ctx, Commission_UpdateCommissionForCreditBalance_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *commissionClient) CalculatePayInPrimalAmount(ctx context.Context, in *CalculatePrimalAmountRequestV1, opts ...grpc.CallOption) (*CalculatePrimalAmountResponseV1, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(CalculatePrimalAmountResponseV1)
	err := c.cc.Invoke(ctx, Commission_CalculatePayInPrimalAmount_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *commissionClient) CalculatePayOutPrimalAmount(ctx context.Context, in *CalculatePrimalAmountRequestV1, opts ...grpc.CallOption) (*CalculatePrimalAmountResponseV1, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(CalculatePrimalAmountResponseV1)
	err := c.cc.Invoke(ctx, Commission_CalculatePayOutPrimalAmount_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *commissionClient) FiscalizeUpperCommission(ctx context.Context, in *FiscalizeUpperCommissionRequestV1, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, Commission_FiscalizeUpperCommission_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *commissionClient) GetCommissionByTransactionID(ctx context.Context, in *GetCommissionByTransactionIDRequestV1, opts ...grpc.CallOption) (*GetCommissionByTransactionIDResponseV1, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetCommissionByTransactionIDResponseV1)
	err := c.cc.Invoke(ctx, Commission_GetCommissionByTransactionID_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// CommissionServer is the server API for Commission service.
// All implementations must embed UnimplementedCommissionServer
// for forward compatibility.
type CommissionServer interface {
	CalculateAndSaveUpperCommission(context.Context, *CalculateAndSaveUpperCommissionRequestV1) (*CalculateAndSaveUpperCommissionResponseV1, error)
	GetCommissionForMainBalance(context.Context, *GetCommissionForMainBalanceRequestV1) (*GetCommissionForMainBalanceResponseV1, error)
	UpdateCommissionForCreditBalance(context.Context, *UpdateCommissionForCreditBalanceRequestV1) (*UpdateCommissionForCreditBalanceResponseV1, error)
	CalculatePayInPrimalAmount(context.Context, *CalculatePrimalAmountRequestV1) (*CalculatePrimalAmountResponseV1, error)
	CalculatePayOutPrimalAmount(context.Context, *CalculatePrimalAmountRequestV1) (*CalculatePrimalAmountResponseV1, error)
	FiscalizeUpperCommission(context.Context, *FiscalizeUpperCommissionRequestV1) (*emptypb.Empty, error)
	GetCommissionByTransactionID(context.Context, *GetCommissionByTransactionIDRequestV1) (*GetCommissionByTransactionIDResponseV1, error)
	mustEmbedUnimplementedCommissionServer()
}

// UnimplementedCommissionServer must be embedded to have
// forward compatible implementations.
//
// NOTE: this should be embedded by value instead of pointer to avoid a nil
// pointer dereference when methods are called.
type UnimplementedCommissionServer struct{}

func (UnimplementedCommissionServer) CalculateAndSaveUpperCommission(context.Context, *CalculateAndSaveUpperCommissionRequestV1) (*CalculateAndSaveUpperCommissionResponseV1, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CalculateAndSaveUpperCommission not implemented")
}
func (UnimplementedCommissionServer) GetCommissionForMainBalance(context.Context, *GetCommissionForMainBalanceRequestV1) (*GetCommissionForMainBalanceResponseV1, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetCommissionForMainBalance not implemented")
}
func (UnimplementedCommissionServer) UpdateCommissionForCreditBalance(context.Context, *UpdateCommissionForCreditBalanceRequestV1) (*UpdateCommissionForCreditBalanceResponseV1, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateCommissionForCreditBalance not implemented")
}
func (UnimplementedCommissionServer) CalculatePayInPrimalAmount(context.Context, *CalculatePrimalAmountRequestV1) (*CalculatePrimalAmountResponseV1, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CalculatePayInPrimalAmount not implemented")
}
func (UnimplementedCommissionServer) CalculatePayOutPrimalAmount(context.Context, *CalculatePrimalAmountRequestV1) (*CalculatePrimalAmountResponseV1, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CalculatePayOutPrimalAmount not implemented")
}
func (UnimplementedCommissionServer) FiscalizeUpperCommission(context.Context, *FiscalizeUpperCommissionRequestV1) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method FiscalizeUpperCommission not implemented")
}
func (UnimplementedCommissionServer) GetCommissionByTransactionID(context.Context, *GetCommissionByTransactionIDRequestV1) (*GetCommissionByTransactionIDResponseV1, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetCommissionByTransactionID not implemented")
}
func (UnimplementedCommissionServer) mustEmbedUnimplementedCommissionServer() {}
func (UnimplementedCommissionServer) testEmbeddedByValue()                    {}

// UnsafeCommissionServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to CommissionServer will
// result in compilation errors.
type UnsafeCommissionServer interface {
	mustEmbedUnimplementedCommissionServer()
}

func RegisterCommissionServer(s grpc.ServiceRegistrar, srv CommissionServer) {
	// If the following call pancis, it indicates UnimplementedCommissionServer was
	// embedded by pointer and is nil.  This will cause panics if an
	// unimplemented method is ever invoked, so we test this at initialization
	// time to prevent it from happening at runtime later due to I/O.
	if t, ok := srv.(interface{ testEmbeddedByValue() }); ok {
		t.testEmbeddedByValue()
	}
	s.RegisterService(&Commission_ServiceDesc, srv)
}

func _Commission_CalculateAndSaveUpperCommission_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CalculateAndSaveUpperCommissionRequestV1)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CommissionServer).CalculateAndSaveUpperCommission(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Commission_CalculateAndSaveUpperCommission_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CommissionServer).CalculateAndSaveUpperCommission(ctx, req.(*CalculateAndSaveUpperCommissionRequestV1))
	}
	return interceptor(ctx, in, info, handler)
}

func _Commission_GetCommissionForMainBalance_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetCommissionForMainBalanceRequestV1)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CommissionServer).GetCommissionForMainBalance(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Commission_GetCommissionForMainBalance_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CommissionServer).GetCommissionForMainBalance(ctx, req.(*GetCommissionForMainBalanceRequestV1))
	}
	return interceptor(ctx, in, info, handler)
}

func _Commission_UpdateCommissionForCreditBalance_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateCommissionForCreditBalanceRequestV1)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CommissionServer).UpdateCommissionForCreditBalance(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Commission_UpdateCommissionForCreditBalance_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CommissionServer).UpdateCommissionForCreditBalance(ctx, req.(*UpdateCommissionForCreditBalanceRequestV1))
	}
	return interceptor(ctx, in, info, handler)
}

func _Commission_CalculatePayInPrimalAmount_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CalculatePrimalAmountRequestV1)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CommissionServer).CalculatePayInPrimalAmount(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Commission_CalculatePayInPrimalAmount_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CommissionServer).CalculatePayInPrimalAmount(ctx, req.(*CalculatePrimalAmountRequestV1))
	}
	return interceptor(ctx, in, info, handler)
}

func _Commission_CalculatePayOutPrimalAmount_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CalculatePrimalAmountRequestV1)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CommissionServer).CalculatePayOutPrimalAmount(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Commission_CalculatePayOutPrimalAmount_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CommissionServer).CalculatePayOutPrimalAmount(ctx, req.(*CalculatePrimalAmountRequestV1))
	}
	return interceptor(ctx, in, info, handler)
}

func _Commission_FiscalizeUpperCommission_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(FiscalizeUpperCommissionRequestV1)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CommissionServer).FiscalizeUpperCommission(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Commission_FiscalizeUpperCommission_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CommissionServer).FiscalizeUpperCommission(ctx, req.(*FiscalizeUpperCommissionRequestV1))
	}
	return interceptor(ctx, in, info, handler)
}

func _Commission_GetCommissionByTransactionID_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetCommissionByTransactionIDRequestV1)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CommissionServer).GetCommissionByTransactionID(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Commission_GetCommissionByTransactionID_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CommissionServer).GetCommissionByTransactionID(ctx, req.(*GetCommissionByTransactionIDRequestV1))
	}
	return interceptor(ctx, in, info, handler)
}

// Commission_ServiceDesc is the grpc.ServiceDesc for Commission service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var Commission_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "processing.commission.commission.Commission",
	HandlerType: (*CommissionServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "CalculateAndSaveUpperCommission",
			Handler:    _Commission_CalculateAndSaveUpperCommission_Handler,
		},
		{
			MethodName: "GetCommissionForMainBalance",
			Handler:    _Commission_GetCommissionForMainBalance_Handler,
		},
		{
			MethodName: "UpdateCommissionForCreditBalance",
			Handler:    _Commission_UpdateCommissionForCreditBalance_Handler,
		},
		{
			MethodName: "CalculatePayInPrimalAmount",
			Handler:    _Commission_CalculatePayInPrimalAmount_Handler,
		},
		{
			MethodName: "CalculatePayOutPrimalAmount",
			Handler:    _Commission_CalculatePayOutPrimalAmount_Handler,
		},
		{
			MethodName: "FiscalizeUpperCommission",
			Handler:    _Commission_FiscalizeUpperCommission_Handler,
		},
		{
			MethodName: "GetCommissionByTransactionID",
			Handler:    _Commission_GetCommissionByTransactionID_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "inner/processing/grpc/commission.proto",
}
