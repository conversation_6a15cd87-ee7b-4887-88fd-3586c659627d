// Code generated by protoc-gen-go-mvp. DO NOT EDIT.
// versions:
// - protoc-gen-go-mvp v2.0.1
// created by mvp authors

package grpc

import (
	dog "git.local/sensitive/sdk/dog"
	gin "github.com/gin-gonic/gin"
	metadata "google.golang.org/grpc/metadata"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
	http "net/http"
)

func RegisterGinTransactionTypeRouterGroup(
	in *gin.RouterGroup,
	handler func(func(c *gin.Context) error) gin.HandlerFunc,
) error {
	service, err := NewGinTransactionTypeService()
	if err != nil {
		return err
	}

	routerGroup := in.Group("processing.transaction.transaction_type.TransactionType")
	routerGroup.PUT("/GetAll", handler(service.GetAll))
	routerGroup.PUT("/GetTransactionPayOutTypes", handler(service.GetTransactionPayOutTypes))
	routerGroup.PUT("/GetTransactionPayInTypes", handler(service.GetTransactionPayInTypes))
	routerGroup.PUT("/GetAggregatedTransactionType", handler(service.GetAggregatedTransactionType))
	routerGroup.PUT("/GetAggregatedTypeByID", handler(service.GetAggregatedTypeByID))
	routerGroup.PUT("/GetAggregatedTransactionTypeByTypeID", handler(service.GetAggregatedTransactionTypeByTypeID))
	return nil
}

func NewGinTransactionTypeService() (GinTransactionTypeServer, error) {
	client, err := NewPreparedTransactionTypeClient()
	if err != nil {
		return nil, err
	}

	return &ginTransactionTypeServer{
		client: NewLoggedTransactionTypeClient(
			NewIamTransactionTypeClient(client),
		),
	}, nil
}

//go:generate mockgen -destination=./grpcmock/transaction_type.gin.pb.go -package=grpcmock -source=transaction_type.gin.pb.go GinTransactionTypeServer
type GinTransactionTypeServer interface {
	GetAll(c *gin.Context) error
	GetTransactionPayOutTypes(c *gin.Context) error
	GetTransactionPayInTypes(c *gin.Context) error
	GetAggregatedTransactionType(c *gin.Context) error
	GetAggregatedTypeByID(c *gin.Context) error
	GetAggregatedTransactionTypeByTypeID(c *gin.Context) error
}

var _ GinTransactionTypeServer = (*ginTransactionTypeServer)(nil)

type ginTransactionTypeServer struct {
	client TransactionTypeClient
}

type TransactionType_GetAll_Success struct {
	// Status is the status of the response. Set true
	Status bool `json:"status"`
	// Message is a human-readable explanation of the status
	Message string `json:"message"`
	// StatusCode is the GoErr status. Set 0
	StatusCode int `json:"status_code"`
	// Result is the result of the request
	Result *TransactionTypeResponseV1 `json:"result"`
}

type TransactionType_GetAll_Failure struct {
	// Status is the status of the response. Set false
	Status bool `json:"status"`
	// Message is a human-readable explanation of the status
	Message string `json:"message"`
	// StatusCode is the GoErr status
	StatusCode int `json:"status_code"`
}

// GetAll
// @Summary GetAll
// @Security bearerAuth
// @ID TransactionType_GetAll
// @Accept json
// @Param request body emptypb.Empty true "Empty"
// @Success 200 {object} TransactionType_GetAll_Success
// @Failure 401 {object} TransactionType_GetAll_Failure "goerrpkg.ErrorTypeAuthorization"
// @Failure 404 {object} TransactionType_GetAll_Failure "goerrpkg.ErrorTypeNotFound"
// @Failure 400 {object} TransactionType_GetAll_Failure "goerrpkg.ErrorTypeBadRequest"
// @Failure 500 {object} TransactionType_GetAll_Failure "goerrpkg.ErrorTypeUnexpectedError"
// @Failure 400 {object} TransactionType_GetAll_Failure "gin.ErrorTypeBind"
// @Failure 500 {object} TransactionType_GetAll_Failure "Undefined error"
// @Produce json
// @Router /processing.transaction.transaction_type.TransactionType/GetAll [put]
// @tags TransactionType
func (s *ginTransactionTypeServer) GetAll(c *gin.Context) error {
	ctx, span := dog.CreateSpan(c.Request.Context(), "GinTransactionTypeServer_GetAll")
	defer span.End()

	var request emptypb.Empty
	if err := c.ShouldBindJSON(&request); err != nil {
		return err
	}

	ctx = metadata.NewOutgoingContext(ctx, metadata.MD(c.Request.Header))

	response, err := s.client.GetAll(ctx, &request)
	if err != nil {
		return err
	}

	c.JSON(http.StatusOK, &TransactionType_GetAll_Success{
		Status:     true,
		Message:    "Success",
		StatusCode: 0,
		Result:     response,
	})
	return nil
}

type TransactionType_GetTransactionPayOutTypes_Success struct {
	// Status is the status of the response. Set true
	Status bool `json:"status"`
	// Message is a human-readable explanation of the status
	Message string `json:"message"`
	// StatusCode is the GoErr status. Set 0
	StatusCode int `json:"status_code"`
	// Result is the result of the request
	Result *TransactionTypeResponseV1 `json:"result"`
}

type TransactionType_GetTransactionPayOutTypes_Failure struct {
	// Status is the status of the response. Set false
	Status bool `json:"status"`
	// Message is a human-readable explanation of the status
	Message string `json:"message"`
	// StatusCode is the GoErr status
	StatusCode int `json:"status_code"`
}

// GetTransactionPayOutTypes
// @Summary GetTransactionPayOutTypes
// @Security bearerAuth
// @ID TransactionType_GetTransactionPayOutTypes
// @Accept json
// @Param request body emptypb.Empty true "Empty"
// @Success 200 {object} TransactionType_GetTransactionPayOutTypes_Success
// @Failure 401 {object} TransactionType_GetTransactionPayOutTypes_Failure "goerrpkg.ErrorTypeAuthorization"
// @Failure 404 {object} TransactionType_GetTransactionPayOutTypes_Failure "goerrpkg.ErrorTypeNotFound"
// @Failure 400 {object} TransactionType_GetTransactionPayOutTypes_Failure "goerrpkg.ErrorTypeBadRequest"
// @Failure 500 {object} TransactionType_GetTransactionPayOutTypes_Failure "goerrpkg.ErrorTypeUnexpectedError"
// @Failure 400 {object} TransactionType_GetTransactionPayOutTypes_Failure "gin.ErrorTypeBind"
// @Failure 500 {object} TransactionType_GetTransactionPayOutTypes_Failure "Undefined error"
// @Produce json
// @Router /processing.transaction.transaction_type.TransactionType/GetTransactionPayOutTypes [put]
// @tags TransactionType
func (s *ginTransactionTypeServer) GetTransactionPayOutTypes(c *gin.Context) error {
	ctx, span := dog.CreateSpan(c.Request.Context(), "GinTransactionTypeServer_GetTransactionPayOutTypes")
	defer span.End()

	var request emptypb.Empty
	if err := c.ShouldBindJSON(&request); err != nil {
		return err
	}

	ctx = metadata.NewOutgoingContext(ctx, metadata.MD(c.Request.Header))

	response, err := s.client.GetTransactionPayOutTypes(ctx, &request)
	if err != nil {
		return err
	}

	c.JSON(http.StatusOK, &TransactionType_GetTransactionPayOutTypes_Success{
		Status:     true,
		Message:    "Success",
		StatusCode: 0,
		Result:     response,
	})
	return nil
}

type TransactionType_GetTransactionPayInTypes_Success struct {
	// Status is the status of the response. Set true
	Status bool `json:"status"`
	// Message is a human-readable explanation of the status
	Message string `json:"message"`
	// StatusCode is the GoErr status. Set 0
	StatusCode int `json:"status_code"`
	// Result is the result of the request
	Result *TransactionTypeResponseV1 `json:"result"`
}

type TransactionType_GetTransactionPayInTypes_Failure struct {
	// Status is the status of the response. Set false
	Status bool `json:"status"`
	// Message is a human-readable explanation of the status
	Message string `json:"message"`
	// StatusCode is the GoErr status
	StatusCode int `json:"status_code"`
}

// GetTransactionPayInTypes
// @Summary GetTransactionPayInTypes
// @Security bearerAuth
// @ID TransactionType_GetTransactionPayInTypes
// @Accept json
// @Param request body emptypb.Empty true "Empty"
// @Success 200 {object} TransactionType_GetTransactionPayInTypes_Success
// @Failure 401 {object} TransactionType_GetTransactionPayInTypes_Failure "goerrpkg.ErrorTypeAuthorization"
// @Failure 404 {object} TransactionType_GetTransactionPayInTypes_Failure "goerrpkg.ErrorTypeNotFound"
// @Failure 400 {object} TransactionType_GetTransactionPayInTypes_Failure "goerrpkg.ErrorTypeBadRequest"
// @Failure 500 {object} TransactionType_GetTransactionPayInTypes_Failure "goerrpkg.ErrorTypeUnexpectedError"
// @Failure 400 {object} TransactionType_GetTransactionPayInTypes_Failure "gin.ErrorTypeBind"
// @Failure 500 {object} TransactionType_GetTransactionPayInTypes_Failure "Undefined error"
// @Produce json
// @Router /processing.transaction.transaction_type.TransactionType/GetTransactionPayInTypes [put]
// @tags TransactionType
func (s *ginTransactionTypeServer) GetTransactionPayInTypes(c *gin.Context) error {
	ctx, span := dog.CreateSpan(c.Request.Context(), "GinTransactionTypeServer_GetTransactionPayInTypes")
	defer span.End()

	var request emptypb.Empty
	if err := c.ShouldBindJSON(&request); err != nil {
		return err
	}

	ctx = metadata.NewOutgoingContext(ctx, metadata.MD(c.Request.Header))

	response, err := s.client.GetTransactionPayInTypes(ctx, &request)
	if err != nil {
		return err
	}

	c.JSON(http.StatusOK, &TransactionType_GetTransactionPayInTypes_Success{
		Status:     true,
		Message:    "Success",
		StatusCode: 0,
		Result:     response,
	})
	return nil
}

type TransactionType_GetAggregatedTransactionType_Success struct {
	// Status is the status of the response. Set true
	Status bool `json:"status"`
	// Message is a human-readable explanation of the status
	Message string `json:"message"`
	// StatusCode is the GoErr status. Set 0
	StatusCode int `json:"status_code"`
	// Result is the result of the request
	Result *GetAggregatedTransactionTypeResponseV1 `json:"result"`
}

type TransactionType_GetAggregatedTransactionType_Failure struct {
	// Status is the status of the response. Set false
	Status bool `json:"status"`
	// Message is a human-readable explanation of the status
	Message string `json:"message"`
	// StatusCode is the GoErr status
	StatusCode int `json:"status_code"`
}

// GetAggregatedTransactionType
// @Summary GetAggregatedTransactionType
// @Security bearerAuth
// @ID TransactionType_GetAggregatedTransactionType
// @Accept json
// @Param request body GetAggregatedTransactionTypeRequestV1 true "GetAggregatedTransactionTypeRequestV1"
// @Success 200 {object} TransactionType_GetAggregatedTransactionType_Success
// @Failure 401 {object} TransactionType_GetAggregatedTransactionType_Failure "goerrpkg.ErrorTypeAuthorization"
// @Failure 404 {object} TransactionType_GetAggregatedTransactionType_Failure "goerrpkg.ErrorTypeNotFound"
// @Failure 400 {object} TransactionType_GetAggregatedTransactionType_Failure "goerrpkg.ErrorTypeBadRequest"
// @Failure 500 {object} TransactionType_GetAggregatedTransactionType_Failure "goerrpkg.ErrorTypeUnexpectedError"
// @Failure 400 {object} TransactionType_GetAggregatedTransactionType_Failure "gin.ErrorTypeBind"
// @Failure 500 {object} TransactionType_GetAggregatedTransactionType_Failure "Undefined error"
// @Produce json
// @Router /processing.transaction.transaction_type.TransactionType/GetAggregatedTransactionType [put]
// @tags TransactionType
func (s *ginTransactionTypeServer) GetAggregatedTransactionType(c *gin.Context) error {
	ctx, span := dog.CreateSpan(c.Request.Context(), "GinTransactionTypeServer_GetAggregatedTransactionType")
	defer span.End()

	var request GetAggregatedTransactionTypeRequestV1
	if err := c.ShouldBindJSON(&request); err != nil {
		return err
	}

	ctx = metadata.NewOutgoingContext(ctx, metadata.MD(c.Request.Header))

	response, err := s.client.GetAggregatedTransactionType(ctx, &request)
	if err != nil {
		return err
	}

	c.JSON(http.StatusOK, &TransactionType_GetAggregatedTransactionType_Success{
		Status:     true,
		Message:    "Success",
		StatusCode: 0,
		Result:     response,
	})
	return nil
}

type TransactionType_GetAggregatedTypeByID_Success struct {
	// Status is the status of the response. Set true
	Status bool `json:"status"`
	// Message is a human-readable explanation of the status
	Message string `json:"message"`
	// StatusCode is the GoErr status. Set 0
	StatusCode int `json:"status_code"`
	// Result is the result of the request
	Result *GetAggregatedTypeByIDResponseV1 `json:"result"`
}

type TransactionType_GetAggregatedTypeByID_Failure struct {
	// Status is the status of the response. Set false
	Status bool `json:"status"`
	// Message is a human-readable explanation of the status
	Message string `json:"message"`
	// StatusCode is the GoErr status
	StatusCode int `json:"status_code"`
}

// GetAggregatedTypeByID
// @Summary GetAggregatedTypeByID
// @Security bearerAuth
// @ID TransactionType_GetAggregatedTypeByID
// @Accept json
// @Param request body GetAggregatedTypeByIDRequestV1 true "GetAggregatedTypeByIDRequestV1"
// @Success 200 {object} TransactionType_GetAggregatedTypeByID_Success
// @Failure 401 {object} TransactionType_GetAggregatedTypeByID_Failure "goerrpkg.ErrorTypeAuthorization"
// @Failure 404 {object} TransactionType_GetAggregatedTypeByID_Failure "goerrpkg.ErrorTypeNotFound"
// @Failure 400 {object} TransactionType_GetAggregatedTypeByID_Failure "goerrpkg.ErrorTypeBadRequest"
// @Failure 500 {object} TransactionType_GetAggregatedTypeByID_Failure "goerrpkg.ErrorTypeUnexpectedError"
// @Failure 400 {object} TransactionType_GetAggregatedTypeByID_Failure "gin.ErrorTypeBind"
// @Failure 500 {object} TransactionType_GetAggregatedTypeByID_Failure "Undefined error"
// @Produce json
// @Router /processing.transaction.transaction_type.TransactionType/GetAggregatedTypeByID [put]
// @tags TransactionType
func (s *ginTransactionTypeServer) GetAggregatedTypeByID(c *gin.Context) error {
	ctx, span := dog.CreateSpan(c.Request.Context(), "GinTransactionTypeServer_GetAggregatedTypeByID")
	defer span.End()

	var request GetAggregatedTypeByIDRequestV1
	if err := c.ShouldBindJSON(&request); err != nil {
		return err
	}

	ctx = metadata.NewOutgoingContext(ctx, metadata.MD(c.Request.Header))

	response, err := s.client.GetAggregatedTypeByID(ctx, &request)
	if err != nil {
		return err
	}

	c.JSON(http.StatusOK, &TransactionType_GetAggregatedTypeByID_Success{
		Status:     true,
		Message:    "Success",
		StatusCode: 0,
		Result:     response,
	})
	return nil
}

type TransactionType_GetAggregatedTransactionTypeByTypeID_Success struct {
	// Status is the status of the response. Set true
	Status bool `json:"status"`
	// Message is a human-readable explanation of the status
	Message string `json:"message"`
	// StatusCode is the GoErr status. Set 0
	StatusCode int `json:"status_code"`
	// Result is the result of the request
	Result *GetAggregatedTransactionTypeByTypeIDResponse `json:"result"`
}

type TransactionType_GetAggregatedTransactionTypeByTypeID_Failure struct {
	// Status is the status of the response. Set false
	Status bool `json:"status"`
	// Message is a human-readable explanation of the status
	Message string `json:"message"`
	// StatusCode is the GoErr status
	StatusCode int `json:"status_code"`
}

// GetAggregatedTransactionTypeByTypeID
// @Summary GetAggregatedTransactionTypeByTypeID
// @Security bearerAuth
// @ID TransactionType_GetAggregatedTransactionTypeByTypeID
// @Accept json
// @Param request body GetAggregatedTransactionTypeByTypeIDRequest true "GetAggregatedTransactionTypeByTypeIDRequest"
// @Success 200 {object} TransactionType_GetAggregatedTransactionTypeByTypeID_Success
// @Failure 401 {object} TransactionType_GetAggregatedTransactionTypeByTypeID_Failure "goerrpkg.ErrorTypeAuthorization"
// @Failure 404 {object} TransactionType_GetAggregatedTransactionTypeByTypeID_Failure "goerrpkg.ErrorTypeNotFound"
// @Failure 400 {object} TransactionType_GetAggregatedTransactionTypeByTypeID_Failure "goerrpkg.ErrorTypeBadRequest"
// @Failure 500 {object} TransactionType_GetAggregatedTransactionTypeByTypeID_Failure "goerrpkg.ErrorTypeUnexpectedError"
// @Failure 400 {object} TransactionType_GetAggregatedTransactionTypeByTypeID_Failure "gin.ErrorTypeBind"
// @Failure 500 {object} TransactionType_GetAggregatedTransactionTypeByTypeID_Failure "Undefined error"
// @Produce json
// @Router /processing.transaction.transaction_type.TransactionType/GetAggregatedTransactionTypeByTypeID [put]
// @tags TransactionType
func (s *ginTransactionTypeServer) GetAggregatedTransactionTypeByTypeID(c *gin.Context) error {
	ctx, span := dog.CreateSpan(c.Request.Context(), "GinTransactionTypeServer_GetAggregatedTransactionTypeByTypeID")
	defer span.End()

	var request GetAggregatedTransactionTypeByTypeIDRequest
	if err := c.ShouldBindJSON(&request); err != nil {
		return err
	}

	ctx = metadata.NewOutgoingContext(ctx, metadata.MD(c.Request.Header))

	response, err := s.client.GetAggregatedTransactionTypeByTypeID(ctx, &request)
	if err != nil {
		return err
	}

	c.JSON(http.StatusOK, &TransactionType_GetAggregatedTransactionTypeByTypeID_Success{
		Status:     true,
		Message:    "Success",
		StatusCode: 0,
		Result:     response,
	})
	return nil
}
