// Code generated by MockGen. DO NOT EDIT.
// Source: commission.gin.pb.go

// Package grpcmock is a generated GoMock package.
package grpcmock

import (
	reflect "reflect"

	gin "github.com/gin-gonic/gin"
	gomock "github.com/golang/mock/gomock"
)

// MockGinCommissionServer is a mock of GinCommissionServer interface.
type MockGinCommissionServer struct {
	ctrl     *gomock.Controller
	recorder *MockGinCommissionServerMockRecorder
}

// MockGinCommissionServerMockRecorder is the mock recorder for MockGinCommissionServer.
type MockGinCommissionServerMockRecorder struct {
	mock *MockGinCommissionServer
}

// NewMockGinCommissionServer creates a new mock instance.
func NewMockGinCommissionServer(ctrl *gomock.Controller) *MockGinCommissionServer {
	mock := &MockGinCommissionServer{ctrl: ctrl}
	mock.recorder = &MockGinCommissionServerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockGinCommissionServer) EXPECT() *MockGinCommissionServerMockRecorder {
	return m.recorder
}

// CalculateAndSaveUpperCommission mocks base method.
func (m *MockGinCommissionServer) CalculateAndSaveUpperCommission(c *gin.Context) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CalculateAndSaveUpperCommission", c)
	ret0, _ := ret[0].(error)
	return ret0
}

// CalculateAndSaveUpperCommission indicates an expected call of CalculateAndSaveUpperCommission.
func (mr *MockGinCommissionServerMockRecorder) CalculateAndSaveUpperCommission(c interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CalculateAndSaveUpperCommission", reflect.TypeOf((*MockGinCommissionServer)(nil).CalculateAndSaveUpperCommission), c)
}

// CalculatePayInPrimalAmount mocks base method.
func (m *MockGinCommissionServer) CalculatePayInPrimalAmount(c *gin.Context) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CalculatePayInPrimalAmount", c)
	ret0, _ := ret[0].(error)
	return ret0
}

// CalculatePayInPrimalAmount indicates an expected call of CalculatePayInPrimalAmount.
func (mr *MockGinCommissionServerMockRecorder) CalculatePayInPrimalAmount(c interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CalculatePayInPrimalAmount", reflect.TypeOf((*MockGinCommissionServer)(nil).CalculatePayInPrimalAmount), c)
}

// CalculatePayOutPrimalAmount mocks base method.
func (m *MockGinCommissionServer) CalculatePayOutPrimalAmount(c *gin.Context) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CalculatePayOutPrimalAmount", c)
	ret0, _ := ret[0].(error)
	return ret0
}

// CalculatePayOutPrimalAmount indicates an expected call of CalculatePayOutPrimalAmount.
func (mr *MockGinCommissionServerMockRecorder) CalculatePayOutPrimalAmount(c interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CalculatePayOutPrimalAmount", reflect.TypeOf((*MockGinCommissionServer)(nil).CalculatePayOutPrimalAmount), c)
}

// FiscalizeUpperCommission mocks base method.
func (m *MockGinCommissionServer) FiscalizeUpperCommission(c *gin.Context) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "FiscalizeUpperCommission", c)
	ret0, _ := ret[0].(error)
	return ret0
}

// FiscalizeUpperCommission indicates an expected call of FiscalizeUpperCommission.
func (mr *MockGinCommissionServerMockRecorder) FiscalizeUpperCommission(c interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "FiscalizeUpperCommission", reflect.TypeOf((*MockGinCommissionServer)(nil).FiscalizeUpperCommission), c)
}

// GetCommissionByTransactionID mocks base method.
func (m *MockGinCommissionServer) GetCommissionByTransactionID(c *gin.Context) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetCommissionByTransactionID", c)
	ret0, _ := ret[0].(error)
	return ret0
}

// GetCommissionByTransactionID indicates an expected call of GetCommissionByTransactionID.
func (mr *MockGinCommissionServerMockRecorder) GetCommissionByTransactionID(c interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetCommissionByTransactionID", reflect.TypeOf((*MockGinCommissionServer)(nil).GetCommissionByTransactionID), c)
}

// GetCommissionForMainBalance mocks base method.
func (m *MockGinCommissionServer) GetCommissionForMainBalance(c *gin.Context) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetCommissionForMainBalance", c)
	ret0, _ := ret[0].(error)
	return ret0
}

// GetCommissionForMainBalance indicates an expected call of GetCommissionForMainBalance.
func (mr *MockGinCommissionServerMockRecorder) GetCommissionForMainBalance(c interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetCommissionForMainBalance", reflect.TypeOf((*MockGinCommissionServer)(nil).GetCommissionForMainBalance), c)
}

// UpdateCommissionForCreditBalance mocks base method.
func (m *MockGinCommissionServer) UpdateCommissionForCreditBalance(c *gin.Context) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateCommissionForCreditBalance", c)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpdateCommissionForCreditBalance indicates an expected call of UpdateCommissionForCreditBalance.
func (mr *MockGinCommissionServerMockRecorder) UpdateCommissionForCreditBalance(c interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateCommissionForCreditBalance", reflect.TypeOf((*MockGinCommissionServer)(nil).UpdateCommissionForCreditBalance), c)
}
