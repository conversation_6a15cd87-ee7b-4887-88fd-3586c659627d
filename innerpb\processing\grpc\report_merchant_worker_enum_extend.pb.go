// Code generated by protoc-gen-go-mvp. DO NOT EDIT.
// versions:
// - protoc-gen-go-mvp v2.0.1
// created by mvp authors

package grpc

import (
	math "math"
	strings "strings"
)

func (val ReportSchedulePeriodType) Synonym() ReportSchedulePeriodType {
	if _, ok := ReportSchedulePeriodType_name[int32(val)]; ok {
		return val
	}

	return ReportSchedulePeriodType(math.MinInt32)
}

func (val ReportSchedulePeriodType) Int() int {
	return int(val.Synonym())
}

func (val ReportSchedulePeriodType) IntPtr() *int {
	result := int(val.Synonym())
	return &result
}

func (val ReportSchedulePeriodType) Int32() int32 {
	return int32(val.Synonym())
}

func (val ReportSchedulePeriodType) Int32Ptr() *int32 {
	result := int32(val.Synonym())
	return &result
}

func (val ReportSchedulePeriodType) Int64() int64 {
	return int64(val.Synonym())
}

func (val ReportSchedulePeriodType) Int64Ptr() *int64 {
	result := int64(val.Synonym())
	return &result
}

func (val ReportSchedulePeriodType) Uint() uint {
	return uint(val.Synonym())
}

func (val ReportSchedulePeriodType) UintPtr() *uint {
	result := uint(val.Synonym())
	return &result
}

func (val ReportSchedulePeriodType) Uint32() uint32 {
	return uint32(val.Synonym())
}

func (val ReportSchedulePeriodType) Uint32Ptr() *uint32 {
	result := uint32(val.Synonym())
	return &result
}

func (val ReportSchedulePeriodType) Uint64() uint64 {
	return uint64(val.Synonym())
}

func (val ReportSchedulePeriodType) Uint64Ptr() *uint64 {
	result := uint64(val.Synonym())
	return &result
}

func (val ReportSchedulePeriodType) IsKnown() bool {
	return val.Synonym() != ReportSchedulePeriodType(math.MinInt32)
}

func ConvertIntToReportSchedulePeriodType(in int) ReportSchedulePeriodType {
	return ReportSchedulePeriodType(in).Synonym()
}

func ConvertUintToReportSchedulePeriodType(in uint) ReportSchedulePeriodType {
	return ReportSchedulePeriodType(in).Synonym()
}

func ConvertInt32ToReportSchedulePeriodType(in int32) ReportSchedulePeriodType {
	return ReportSchedulePeriodType(in).Synonym()
}

func ConvertUint32ToReportSchedulePeriodType(in uint32) ReportSchedulePeriodType {
	return ReportSchedulePeriodType(in).Synonym()
}

func ConvertInt64ToReportSchedulePeriodType(in int64) ReportSchedulePeriodType {
	return ReportSchedulePeriodType(in).Synonym()
}

func ConvertUint64ToReportSchedulePeriodType(in uint64) ReportSchedulePeriodType {
	return ReportSchedulePeriodType(in).Synonym()
}

var ReportSchedulePeriodType_Lower_value = map[string]ReportSchedulePeriodType{
	"reportscheduleperiodtypeunknown": 0,
	"reportscheduleperiodtypedaily":   1,
	"reportscheduleperiodtypeweekly":  2,
	"reportscheduleperiodtypemonthly": 3,
}

func ConvertStringToReportSchedulePeriodType(in string) ReportSchedulePeriodType {
	if result, ok := ReportSchedulePeriodType_value[in]; ok {
		return ReportSchedulePeriodType(result)
	}

	if result, ok := ReportSchedulePeriodType_Lower_value[strings.ToLower(in)]; ok {
		return ReportSchedulePeriodType(result)
	}

	return ReportSchedulePeriodType(math.MinInt32)
}

var SliceReportSchedulePeriodTypeConvert *sliceReportSchedulePeriodTypeConvert

type sliceReportSchedulePeriodTypeConvert struct{}

func (*sliceReportSchedulePeriodTypeConvert) Synonym(in []ReportSchedulePeriodType) []ReportSchedulePeriodType {
	result := make([]ReportSchedulePeriodType, len(in))
	for i, v := range in {
		result[i] = v.Synonym()
	}

	return result
}

func (*sliceReportSchedulePeriodTypeConvert) Int32(in []ReportSchedulePeriodType) []int32 {
	result := make([]int32, len(in))
	for i, v := range in {
		result[i] = v.Int32()
	}

	return result
}

func (*sliceReportSchedulePeriodTypeConvert) Uint32(in []ReportSchedulePeriodType) []uint32 {
	result := make([]uint32, len(in))
	for i, v := range in {
		result[i] = v.Uint32()
	}

	return result
}

func (*sliceReportSchedulePeriodTypeConvert) Uint64(in []ReportSchedulePeriodType) []uint64 {
	result := make([]uint64, len(in))
	for i, v := range in {
		result[i] = v.Uint64()
	}

	return result
}

func (*sliceReportSchedulePeriodTypeConvert) Int64(in []ReportSchedulePeriodType) []int64 {
	result := make([]int64, len(in))
	for i, v := range in {
		result[i] = v.Int64()
	}

	return result
}

func (*sliceReportSchedulePeriodTypeConvert) Uint(in []ReportSchedulePeriodType) []uint {
	result := make([]uint, len(in))
	for i, v := range in {
		result[i] = v.Uint()
	}

	return result
}

func (*sliceReportSchedulePeriodTypeConvert) Int(in []ReportSchedulePeriodType) []int {
	result := make([]int, len(in))
	for i, v := range in {
		result[i] = v.Int()
	}

	return result
}

func (*sliceReportSchedulePeriodTypeConvert) IsKnown(in []ReportSchedulePeriodType) []bool {
	result := make([]bool, len(in))
	for i, v := range in {
		result[i] = v.IsKnown()
	}

	return result
}

func (*sliceReportSchedulePeriodTypeConvert) ConvertIntToReportSchedulePeriodType(in []int) []ReportSchedulePeriodType {
	result := make([]ReportSchedulePeriodType, len(in))
	for i, v := range in {
		result[i] = ConvertIntToReportSchedulePeriodType(v)
	}

	return result
}

func (*sliceReportSchedulePeriodTypeConvert) ConvertUintToReportSchedulePeriodType(in []uint) []ReportSchedulePeriodType {
	result := make([]ReportSchedulePeriodType, len(in))
	for i, v := range in {
		result[i] = ConvertUintToReportSchedulePeriodType(v)
	}

	return result
}

func (*sliceReportSchedulePeriodTypeConvert) ConvertInt32ToReportSchedulePeriodType(in []int32) []ReportSchedulePeriodType {
	result := make([]ReportSchedulePeriodType, len(in))
	for i, v := range in {
		result[i] = ConvertInt32ToReportSchedulePeriodType(v)
	}

	return result
}

func (*sliceReportSchedulePeriodTypeConvert) ConvertUint32ToReportSchedulePeriodType(in []uint32) []ReportSchedulePeriodType {
	result := make([]ReportSchedulePeriodType, len(in))
	for i, v := range in {
		result[i] = ConvertUint32ToReportSchedulePeriodType(v)
	}

	return result
}

func (*sliceReportSchedulePeriodTypeConvert) ConvertInt64ToReportSchedulePeriodType(in []int64) []ReportSchedulePeriodType {
	result := make([]ReportSchedulePeriodType, len(in))
	for i, v := range in {
		result[i] = ConvertInt64ToReportSchedulePeriodType(v)
	}

	return result
}

func (*sliceReportSchedulePeriodTypeConvert) ConvertUint64ToReportSchedulePeriodType(in []uint64) []ReportSchedulePeriodType {
	result := make([]ReportSchedulePeriodType, len(in))
	for i, v := range in {
		result[i] = ConvertUint64ToReportSchedulePeriodType(v)
	}

	return result
}

func (*sliceReportSchedulePeriodTypeConvert) ConvertStringToReportSchedulePeriodType(in []string) []ReportSchedulePeriodType {
	result := make([]ReportSchedulePeriodType, len(in))
	for i, v := range in {
		result[i] = ConvertStringToReportSchedulePeriodType(v)
	}

	return result
}

func NewReportSchedulePeriodTypeUsage() *ReportSchedulePeriodTypeUsage {
	return &ReportSchedulePeriodTypeUsage{
		enumMap: map[ReportSchedulePeriodType]bool{
			ReportSchedulePeriodType_ReportSchedulePeriodTypeUnknown: false,
			ReportSchedulePeriodType_ReportSchedulePeriodTypeDaily:   false,
			ReportSchedulePeriodType_ReportSchedulePeriodTypeWeekly:  false,
			ReportSchedulePeriodType_ReportSchedulePeriodTypeMonthly: false,
		},
	}
}

func IsReportSchedulePeriodType(target ReportSchedulePeriodType, matches ...ReportSchedulePeriodType) bool {
	for _, v := range matches {
		if target == v {
			return true
		}
	}

	return false
}

type ReportSchedulePeriodTypeUsage struct {
	enumMap map[ReportSchedulePeriodType]bool
}

func (u *ReportSchedulePeriodTypeUsage) Use(slice ...ReportSchedulePeriodType) *ReportSchedulePeriodTypeUsage {
	for _, in := range slice {
		u.enumMap[in] = true
	}

	return u
}

func (u *ReportSchedulePeriodTypeUsage) Check() []string {
	var result []string
	for k, v := range u.enumMap {
		if v {
			continue
		}

		result = append(result, k.String())
	}

	return result
}

func (u *ReportSchedulePeriodTypeUsage) ReportSchedulePeriodType_ReportSchedulePeriodTypeUnknown() ReportSchedulePeriodType {
	u.Use(ReportSchedulePeriodType_ReportSchedulePeriodTypeUnknown)
	return ReportSchedulePeriodType_ReportSchedulePeriodTypeUnknown
}

func (u *ReportSchedulePeriodTypeUsage) ReportSchedulePeriodType_ReportSchedulePeriodTypeDaily() ReportSchedulePeriodType {
	u.Use(ReportSchedulePeriodType_ReportSchedulePeriodTypeDaily)
	return ReportSchedulePeriodType_ReportSchedulePeriodTypeDaily
}

func (u *ReportSchedulePeriodTypeUsage) ReportSchedulePeriodType_ReportSchedulePeriodTypeWeekly() ReportSchedulePeriodType {
	u.Use(ReportSchedulePeriodType_ReportSchedulePeriodTypeWeekly)
	return ReportSchedulePeriodType_ReportSchedulePeriodTypeWeekly
}

func (u *ReportSchedulePeriodTypeUsage) ReportSchedulePeriodType_ReportSchedulePeriodTypeMonthly() ReportSchedulePeriodType {
	u.Use(ReportSchedulePeriodType_ReportSchedulePeriodTypeMonthly)
	return ReportSchedulePeriodType_ReportSchedulePeriodTypeMonthly
}
