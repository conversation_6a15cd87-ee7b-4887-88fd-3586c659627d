// Code generated by MockGen. DO NOT EDIT.
// Source: eosi.gin.pb.go

// Package grpcmock is a generated GoMock package.
package grpcmock

import (
	reflect "reflect"

	gin "github.com/gin-gonic/gin"
	gomock "github.com/golang/mock/gomock"
)

// MockGinEosiServer is a mock of GinEosiServer interface.
type MockGinEosiServer struct {
	ctrl     *gomock.Controller
	recorder *MockGinEosiServerMockRecorder
}

// MockGinEosiServerMockRecorder is the mock recorder for MockGinEosiServer.
type MockGinEosiServerMockRecorder struct {
	mock *MockGinEosiServer
}

// NewMockGinEosiServer creates a new mock instance.
func NewMockGinEosiServer(ctrl *gomock.Controller) *MockGinEosiServer {
	mock := &MockGinEosiServer{ctrl: ctrl}
	mock.recorder = &MockGinEosiServerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockGinEosiServer) EXPECT() *MockGinEosiServerMockRecorder {
	return m.recorder
}

// GetOrderingIdentifier mocks base method.
func (m *MockGinEosiServer) GetOrderingIdentifier(c *gin.Context) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetOrderingIdentifier", c)
	ret0, _ := ret[0].(error)
	return ret0
}

// GetOrderingIdentifier indicates an expected call of GetOrderingIdentifier.
func (mr *MockGinEosiServerMockRecorder) GetOrderingIdentifier(c interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetOrderingIdentifier", reflect.TypeOf((*MockGinEosiServer)(nil).GetOrderingIdentifier), c)
}

// GetPaymentOrder mocks base method.
func (m *MockGinEosiServer) GetPaymentOrder(c *gin.Context) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetPaymentOrder", c)
	ret0, _ := ret[0].(error)
	return ret0
}

// GetPaymentOrder indicates an expected call of GetPaymentOrder.
func (mr *MockGinEosiServerMockRecorder) GetPaymentOrder(c interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetPaymentOrder", reflect.TypeOf((*MockGinEosiServer)(nil).GetPaymentOrder), c)
}
