// Code generated by MockGen. DO NOT EDIT.
// Source: merchant.gin.pb.go

// Package grpcmock is a generated GoMock package.
package grpcmock

import (
	reflect "reflect"

	gin "github.com/gin-gonic/gin"
	gomock "github.com/golang/mock/gomock"
)

// MockGinMerchantServer is a mock of GinMerchantServer interface.
type MockGinMerchantServer struct {
	ctrl     *gomock.Controller
	recorder *MockGinMerchantServerMockRecorder
}

// MockGinMerchantServerMockRecorder is the mock recorder for MockGinMerchantServer.
type MockGinMerchantServerMockRecorder struct {
	mock *MockGinMerchantServer
}

// NewMockGinMerchantServer creates a new mock instance.
func NewMockGinMerchantServer(ctrl *gomock.Controller) *MockGinMerchantServer {
	mock := &MockGinMerchantServer{ctrl: ctrl}
	mock.recorder = &MockGinMerchantServerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockGinMerchantServer) EXPECT() *MockGinMerchantServerMockRecorder {
	return m.recorder
}

// CheckProject mocks base method.
func (m *MockGinMerchantServer) CheckProject(c *gin.Context) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CheckProject", c)
	ret0, _ := ret[0].(error)
	return ret0
}

// CheckProject indicates an expected call of CheckProject.
func (mr *MockGinMerchantServerMockRecorder) CheckProject(c interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CheckProject", reflect.TypeOf((*MockGinMerchantServer)(nil).CheckProject), c)
}

// CheckProjectAuth mocks base method.
func (m *MockGinMerchantServer) CheckProjectAuth(c *gin.Context) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CheckProjectAuth", c)
	ret0, _ := ret[0].(error)
	return ret0
}

// CheckProjectAuth indicates an expected call of CheckProjectAuth.
func (mr *MockGinMerchantServerMockRecorder) CheckProjectAuth(c interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CheckProjectAuth", reflect.TypeOf((*MockGinMerchantServer)(nil).CheckProjectAuth), c)
}

// CheckProjectAuthSHA256 mocks base method.
func (m *MockGinMerchantServer) CheckProjectAuthSHA256(c *gin.Context) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CheckProjectAuthSHA256", c)
	ret0, _ := ret[0].(error)
	return ret0
}

// CheckProjectAuthSHA256 indicates an expected call of CheckProjectAuthSHA256.
func (mr *MockGinMerchantServerMockRecorder) CheckProjectAuthSHA256(c interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CheckProjectAuthSHA256", reflect.TypeOf((*MockGinMerchantServer)(nil).CheckProjectAuthSHA256), c)
}

// GenerateProjectHash mocks base method.
func (m *MockGinMerchantServer) GenerateProjectHash(c *gin.Context) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GenerateProjectHash", c)
	ret0, _ := ret[0].(error)
	return ret0
}

// GenerateProjectHash indicates an expected call of GenerateProjectHash.
func (mr *MockGinMerchantServerMockRecorder) GenerateProjectHash(c interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GenerateProjectHash", reflect.TypeOf((*MockGinMerchantServer)(nil).GenerateProjectHash), c)
}

// GenerateProjectSHA256Hash mocks base method.
func (m *MockGinMerchantServer) GenerateProjectSHA256Hash(c *gin.Context) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GenerateProjectSHA256Hash", c)
	ret0, _ := ret[0].(error)
	return ret0
}

// GenerateProjectSHA256Hash indicates an expected call of GenerateProjectSHA256Hash.
func (mr *MockGinMerchantServerMockRecorder) GenerateProjectSHA256Hash(c interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GenerateProjectSHA256Hash", reflect.TypeOf((*MockGinMerchantServer)(nil).GenerateProjectSHA256Hash), c)
}

// GetMerchantDataByID mocks base method.
func (m *MockGinMerchantServer) GetMerchantDataByID(c *gin.Context) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetMerchantDataByID", c)
	ret0, _ := ret[0].(error)
	return ret0
}

// GetMerchantDataByID indicates an expected call of GetMerchantDataByID.
func (mr *MockGinMerchantServerMockRecorder) GetMerchantDataByID(c interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetMerchantDataByID", reflect.TypeOf((*MockGinMerchantServer)(nil).GetMerchantDataByID), c)
}

// GetMerchantInfo mocks base method.
func (m *MockGinMerchantServer) GetMerchantInfo(c *gin.Context) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetMerchantInfo", c)
	ret0, _ := ret[0].(error)
	return ret0
}

// GetMerchantInfo indicates an expected call of GetMerchantInfo.
func (mr *MockGinMerchantServerMockRecorder) GetMerchantInfo(c interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetMerchantInfo", reflect.TypeOf((*MockGinMerchantServer)(nil).GetMerchantInfo), c)
}

// GetMerchantProjectsByBin mocks base method.
func (m *MockGinMerchantServer) GetMerchantProjectsByBin(c *gin.Context) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetMerchantProjectsByBin", c)
	ret0, _ := ret[0].(error)
	return ret0
}

// GetMerchantProjectsByBin indicates an expected call of GetMerchantProjectsByBin.
func (mr *MockGinMerchantServerMockRecorder) GetMerchantProjectsByBin(c interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetMerchantProjectsByBin", reflect.TypeOf((*MockGinMerchantServer)(nil).GetMerchantProjectsByBin), c)
}

// GetProject mocks base method.
func (m *MockGinMerchantServer) GetProject(c *gin.Context) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetProject", c)
	ret0, _ := ret[0].(error)
	return ret0
}

// GetProject indicates an expected call of GetProject.
func (mr *MockGinMerchantServerMockRecorder) GetProject(c interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetProject", reflect.TypeOf((*MockGinMerchantServer)(nil).GetProject), c)
}
