// Code generated by protoc-gen-go-mvp. DO NOT EDIT.
// versions:
// - protoc-gen-go-mvp v2.0.1
// created by mvp authors

package grpc

import (
	protojson "google.golang.org/protobuf/encoding/protojson"
)

func (m *MakeFiscalizationRequestV1) UnmarshalJSON(in []byte) error {
	val := new(MakeFiscalizationRequestV1)
	if err := (&protojson.UnmarshalOptions{
		DiscardUnknown: true,
	}).Unmarshal(in, val); err != nil {
		return err
	}

	*m = *val
	return nil
}

func (m *MakeFiscalizationRequestV1) MarshalJSON() ([]byte, error) {
	return (&protojson.MarshalOptions{
		UseProtoNames: true,
	}).Marshal(m)
}

func (m *GetFiscalInfoByTransactionIDRequestV1) UnmarshalJSON(in []byte) error {
	val := new(GetFiscalInfoByTransactionIDRequestV1)
	if err := (&protojson.UnmarshalOptions{
		DiscardUnknown: true,
	}).Unmarshal(in, val); err != nil {
		return err
	}

	*m = *val
	return nil
}

func (m *GetFiscalInfoByTransactionIDRequestV1) MarshalJSON() ([]byte, error) {
	return (&protojson.MarshalOptions{
		UseProtoNames: true,
	}).Marshal(m)
}

func (m *GetFiscalInfoByTransactionIDResponseV1) UnmarshalJSON(in []byte) error {
	val := new(GetFiscalInfoByTransactionIDResponseV1)
	if err := (&protojson.UnmarshalOptions{
		DiscardUnknown: true,
	}).Unmarshal(in, val); err != nil {
		return err
	}

	*m = *val
	return nil
}

func (m *GetFiscalInfoByTransactionIDResponseV1) MarshalJSON() ([]byte, error) {
	return (&protojson.MarshalOptions{
		UseProtoNames: true,
	}).Marshal(m)
}
