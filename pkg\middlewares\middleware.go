package middlewares

import (
	"git.local/sensitive/sdk/dog"
	"github.com/gin-gonic/gin"
)

type Middleware struct {
	CSRF ICsrf
}

func Protection() gin.HandlerFunc {
	return func(c *gin.Context) {
		if c.<PERSON>("SECRET_KEY") != dog.ConfigString("SECRET_KEY") {
			c.<PERSON>(404, gin.H{
				"status":  false,
				"message": "Page not found",
				"result":  "",
			})
			c.Abort()

			return
		}
	}
}
