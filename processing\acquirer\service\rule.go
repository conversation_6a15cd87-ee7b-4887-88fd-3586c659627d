package service

import (
	"context"
	"errors"
	"git.local/sensitive/innerpb/processing/goerr"
	"git.local/sensitive/pkg/gtransaction"
	"git.local/sensitive/pkg/middlewares"
	"git.local/sensitive/processing/acquirer/model"
	"git.local/sensitive/processing/acquirer/repository/database"
	"git.local/sensitive/processing/acquirer/schema"
	"git.local/sensitive/sdk/dog"
	"go.uber.org/zap"
)

type RuleService struct {
	ruleRepo           database.Ruler
	bankRepo           database.Banker
	rulePercentageRepo database.RulePercentager
	transaction        gtransaction.Manager
}

func NewRuleService(
	ruleRepo database.Ruler,
	bankRepo database.Banker,
	rulePercentageRepo database.RulePercentager,
	transaction gtransaction.Manager,
) *RuleService {
	return &RuleService{
		ruleRepo:           ruleRepo,
		bankRepo:           bankRepo,
		rulePercentageRepo: rulePercentageRepo,
		transaction:        transaction,
	}
}

func (rs *RuleService) checkCreate(ctx context.Context, request schema.CreateRuleRequest) error {
	isActive := true

	rules, err := rs.ruleRepo.FindByAllParam(ctx, request.ToRuleModel(), &isActive)
	if err != nil && !errors.Is(err, goerr.ErrRuleNotFound) {
		return err
	}

	if len(rules) > 0 {
		return goerr.ErrRuleAlreadyExists
	}

	if request.IssuerID != nil {
		_, err = rs.bankRepo.GetById(ctx, *request.IssuerID)
		if err != nil {
			return err
		}
	}

	return nil
}

func (rs *RuleService) Create(
	ctx context.Context,
	request schema.CreateRuleRequest,
) (rule *model.Rule, err error) {
	ctx, span := dog.CreateSpan(ctx, "RuleService_Create")
	defer span.End()

	_, err = rs.ruleRepo.GetBaseRuleByProject(ctx, request.ProjectID, request.TransactionTypeID)
	if err != nil {
		dog.L().Error("RuleService_Create", zap.Uint64("project_id", request.ProjectID), zap.Uint64("type_id", request.TransactionTypeID),
			zap.Error(err))
		return nil, err
	}

	if err = rs.checkCreate(ctx, request); err != nil {
		return nil, err
	}

	ctx = rs.transaction.Begin(ctx)

	rule, err = rs.ruleRepo.Create(ctx, request.ToRuleModel())
	if err != nil {
		if transactionErr := rs.transaction.Rollback(ctx); transactionErr != nil {
			return nil, transactionErr
		}

		return nil, err
	}

	rulePercentages := request.Percentages.ToRulePercentages(rule.ID)
	if err = rs.rulePercentageRepo.Create(ctx, rulePercentages); err != nil {
		if transactionErr := rs.transaction.Rollback(ctx); transactionErr != nil {
			return nil, transactionErr
		}

		return nil, err
	}

	if err = rs.transaction.Commit(ctx); err != nil {
		return nil, err
	}

	return rule, nil
}

func (rs *RuleService) CreateBase(
	ctx context.Context,
	request schema.CreateBaseRuleRequest,
) (rule *model.Rule, err error) {
	ctx, span := dog.CreateSpan(ctx, "RuleService_CreateBase")
	defer span.End()

	existingBase, err := rs.ruleRepo.GetBaseRuleByProject(ctx, request.ProjectID, request.TransactionTypeID)
	if err != nil && err != goerr.ErrBaseRuleNotFound {
		dog.L().Error("RuleService_CreateBase", zap.Uint64("project_id", request.ProjectID), zap.Uint64("type_id", request.TransactionTypeID),
			zap.Error(err))
		return nil, err
	}

	if existingBase != nil {
		return nil, goerr.ErrBaseRuleAlreadyExists
	}

	if rs.transaction != nil {
		ctx = rs.transaction.Begin(ctx)
	}

	rule, err = rs.ruleRepo.Create(ctx, request.ToRuleModel())
	if err != nil {
		if transactionErr := rs.transaction.Rollback(ctx); transactionErr != nil {
			return nil, transactionErr
		}

		return nil, err
	}

	rulePercentages := request.Percentages.ToRulePercentages(rule.ID)
	if err = rs.rulePercentageRepo.Create(ctx, rulePercentages); err != nil {
		if transactionErr := rs.transaction.Rollback(ctx); transactionErr != nil {
			return nil, transactionErr
		}

		return nil, err
	}

	err = rs.transaction.Commit(ctx)
	if err != nil {
		return nil, err
	}

	return rule, nil
}

func (rs *RuleService) GetList(
	ctx context.Context,
	pagination *middlewares.PaginationInfo,
	request schema.RuleListRequest,
) (_ []*model.Rule, err error) {
	ctx, span := dog.CreateSpan(ctx, "RuleService_GetList")
	defer span.End()

	rules, err := rs.ruleRepo.GetListWithBalancer(
		ctx,
		pagination,
		request,
	)
	if err != nil {
		return nil, err
	}

	return rules, nil
}
