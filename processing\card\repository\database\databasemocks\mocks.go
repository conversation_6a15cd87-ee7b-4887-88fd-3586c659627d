// Code generated by MockGen. DO NOT EDIT.
// Source: database.go

// Package databasemocks is a generated GoMock package.
package databasemocks

import (
	context "context"
	reflect "reflect"

	events "git.local/sensitive/innerpb/processing/events"
	middlewares "git.local/sensitive/pkg/middlewares"
	model "git.local/sensitive/processing/card/model"
	schema "git.local/sensitive/processing/card/schema"
	gomock "github.com/golang/mock/gomock"
)

// MockCarder is a mock of Carder interface.
type MockCarder struct {
	ctrl     *gomock.Controller
	recorder *MockCarderMockRecorder
}

// MockCarderMockRecorder is the mock recorder for MockCarder.
type MockCarderMockRecorder struct {
	mock *MockCarder
}

// NewMockCarder creates a new mock instance.
func NewMockCarder(ctrl *gomock.Controller) *MockCarder {
	mock := &MockCarder{ctrl: ctrl}
	mock.recorder = &MockCarderMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockCarder) EXPECT() *MockCarderMockRecorder {
	return m.recorder
}

// Create mocks base method.
func (m *MockCarder) Create(ctx context.Context, card *model.Card) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Create", ctx, card)
	ret0, _ := ret[0].(error)
	return ret0
}

// Create indicates an expected call of Create.
func (mr *MockCarderMockRecorder) Create(ctx, card interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Create", reflect.TypeOf((*MockCarder)(nil).Create), ctx, card)
}

// GetAllNotExpired mocks base method.
func (m *MockCarder) GetAllNotExpired(ctx context.Context, afterID uint64, limit int) ([]model.Card, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAllNotExpired", ctx, afterID, limit)
	ret0, _ := ret[0].([]model.Card)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAllNotExpired indicates an expected call of GetAllNotExpired.
func (mr *MockCarderMockRecorder) GetAllNotExpired(ctx, afterID, limit interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAllNotExpired", reflect.TypeOf((*MockCarder)(nil).GetAllNotExpired), ctx, afterID, limit)
}

// GetByClient mocks base method.
func (m *MockCarder) GetByClient(ctx context.Context, clientId uint64) (model.Cards, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetByClient", ctx, clientId)
	ret0, _ := ret[0].(model.Cards)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetByClient indicates an expected call of GetByClient.
func (mr *MockCarderMockRecorder) GetByClient(ctx, clientId interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetByClient", reflect.TypeOf((*MockCarder)(nil).GetByClient), ctx, clientId)
}

// GetByID mocks base method.
func (m *MockCarder) GetByID(ctx context.Context, id uint64) (model.Card, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetByID", ctx, id)
	ret0, _ := ret[0].(model.Card)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetByID indicates an expected call of GetByID.
func (mr *MockCarderMockRecorder) GetByID(ctx, id interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetByID", reflect.TypeOf((*MockCarder)(nil).GetByID), ctx, id)
}

// GetByIdAndClient mocks base method.
func (m *MockCarder) GetByIdAndClient(ctx context.Context, id, clientId uint64) (model.Card, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetByIdAndClient", ctx, id, clientId)
	ret0, _ := ret[0].(model.Card)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetByIdAndClient indicates an expected call of GetByIdAndClient.
func (mr *MockCarderMockRecorder) GetByIdAndClient(ctx, id, clientId interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetByIdAndClient", reflect.TypeOf((*MockCarder)(nil).GetByIdAndClient), ctx, id, clientId)
}

// GetClientCards mocks base method.
func (m *MockCarder) GetClientCards(ctx context.Context, pagination *middlewares.PaginationInfo, projectClientId string, projectId uint64) (model.Cards, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetClientCards", ctx, pagination, projectClientId, projectId)
	ret0, _ := ret[0].(model.Cards)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetClientCards indicates an expected call of GetClientCards.
func (mr *MockCarderMockRecorder) GetClientCards(ctx, pagination, projectClientId, projectId interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetClientCards", reflect.TypeOf((*MockCarder)(nil).GetClientCards), ctx, pagination, projectClientId, projectId)
}

// MockKeyer is a mock of Keyer interface.
type MockKeyer struct {
	ctrl     *gomock.Controller
	recorder *MockKeyerMockRecorder
}

// MockKeyerMockRecorder is the mock recorder for MockKeyer.
type MockKeyerMockRecorder struct {
	mock *MockKeyer
}

// NewMockKeyer creates a new mock instance.
func NewMockKeyer(ctrl *gomock.Controller) *MockKeyer {
	mock := &MockKeyer{ctrl: ctrl}
	mock.recorder = &MockKeyerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockKeyer) EXPECT() *MockKeyerMockRecorder {
	return m.recorder
}

// Create mocks base method.
func (m *MockKeyer) Create(ctx context.Context, key *model.Key) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Create", ctx, key)
	ret0, _ := ret[0].(error)
	return ret0
}

// Create indicates an expected call of Create.
func (mr *MockKeyerMockRecorder) Create(ctx, key interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Create", reflect.TypeOf((*MockKeyer)(nil).Create), ctx, key)
}

// GetActualKey mocks base method.
func (m *MockKeyer) GetActualKey(ctx context.Context) (model.Key, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetActualKey", ctx)
	ret0, _ := ret[0].(model.Key)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetActualKey indicates an expected call of GetActualKey.
func (mr *MockKeyerMockRecorder) GetActualKey(ctx interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetActualKey", reflect.TypeOf((*MockKeyer)(nil).GetActualKey), ctx)
}

// GetById mocks base method.
func (m *MockKeyer) GetById(ctx context.Context, id uint64) (model.Key, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetById", ctx, id)
	ret0, _ := ret[0].(model.Key)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetById indicates an expected call of GetById.
func (mr *MockKeyerMockRecorder) GetById(ctx, id interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetById", reflect.TypeOf((*MockKeyer)(nil).GetById), ctx, id)
}

// GetCardsByOldKey mocks base method.
func (m *MockKeyer) GetCardsByOldKey(ctx context.Context, actualKey, afterID uint64, limit int) ([]*model.Card, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetCardsByOldKey", ctx, actualKey, afterID, limit)
	ret0, _ := ret[0].([]*model.Card)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetCardsByOldKey indicates an expected call of GetCardsByOldKey.
func (mr *MockKeyerMockRecorder) GetCardsByOldKey(ctx, actualKey, afterID, limit interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetCardsByOldKey", reflect.TypeOf((*MockKeyer)(nil).GetCardsByOldKey), ctx, actualKey, afterID, limit)
}

// MockHashKeyer is a mock of HashKeyer interface.
type MockHashKeyer struct {
	ctrl     *gomock.Controller
	recorder *MockHashKeyerMockRecorder
}

// MockHashKeyerMockRecorder is the mock recorder for MockHashKeyer.
type MockHashKeyerMockRecorder struct {
	mock *MockHashKeyer
}

// NewMockHashKeyer creates a new mock instance.
func NewMockHashKeyer(ctrl *gomock.Controller) *MockHashKeyer {
	mock := &MockHashKeyer{ctrl: ctrl}
	mock.recorder = &MockHashKeyerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockHashKeyer) EXPECT() *MockHashKeyerMockRecorder {
	return m.recorder
}

// Create mocks base method.
func (m *MockHashKeyer) Create(ctx context.Context, key *model.HashKey) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Create", ctx, key)
	ret0, _ := ret[0].(error)
	return ret0
}

// Create indicates an expected call of Create.
func (mr *MockHashKeyerMockRecorder) Create(ctx, key interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Create", reflect.TypeOf((*MockHashKeyer)(nil).Create), ctx, key)
}

// GetActualKey mocks base method.
func (m *MockHashKeyer) GetActualKey(ctx context.Context) (model.HashKey, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetActualKey", ctx)
	ret0, _ := ret[0].(model.HashKey)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetActualKey indicates an expected call of GetActualKey.
func (mr *MockHashKeyerMockRecorder) GetActualKey(ctx interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetActualKey", reflect.TypeOf((*MockHashKeyer)(nil).GetActualKey), ctx)
}

// GetCardsByOldKey mocks base method.
func (m *MockHashKeyer) GetCardsByOldKey(ctx context.Context, actualKey, afterID uint64, limit int) ([]*model.Card, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetCardsByOldKey", ctx, actualKey, afterID, limit)
	ret0, _ := ret[0].([]*model.Card)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetCardsByOldKey indicates an expected call of GetCardsByOldKey.
func (mr *MockHashKeyerMockRecorder) GetCardsByOldKey(ctx, actualKey, afterID, limit interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetCardsByOldKey", reflect.TypeOf((*MockHashKeyer)(nil).GetCardsByOldKey), ctx, actualKey, afterID, limit)
}

// MockCardGetter is a mock of CardGetter interface.
type MockCardGetter struct {
	ctrl     *gomock.Controller
	recorder *MockCardGetterMockRecorder
}

// MockCardGetterMockRecorder is the mock recorder for MockCardGetter.
type MockCardGetterMockRecorder struct {
	mock *MockCardGetter
}

// NewMockCardGetter creates a new mock instance.
func NewMockCardGetter(ctrl *gomock.Controller) *MockCardGetter {
	mock := &MockCardGetter{ctrl: ctrl}
	mock.recorder = &MockCardGetterMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockCardGetter) EXPECT() *MockCardGetterMockRecorder {
	return m.recorder
}

// GetActiveCardsByClient mocks base method.
func (m *MockCardGetter) GetActiveCardsByClient(ctx context.Context, id uint64) (model.Cards, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetActiveCardsByClient", ctx, id)
	ret0, _ := ret[0].(model.Cards)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetActiveCardsByClient indicates an expected call of GetActiveCardsByClient.
func (mr *MockCardGetterMockRecorder) GetActiveCardsByClient(ctx, id interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetActiveCardsByClient", reflect.TypeOf((*MockCardGetter)(nil).GetActiveCardsByClient), ctx, id)
}

// GetByClient mocks base method.
func (m *MockCardGetter) GetByClient(ctx context.Context, id uint64, pagination *middlewares.PaginationInfo) ([]model.Card, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetByClient", ctx, id, pagination)
	ret0, _ := ret[0].([]model.Card)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetByClient indicates an expected call of GetByClient.
func (mr *MockCardGetterMockRecorder) GetByClient(ctx, id, pagination interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetByClient", reflect.TypeOf((*MockCardGetter)(nil).GetByClient), ctx, id, pagination)
}

// MockCardUpdator is a mock of CardUpdator interface.
type MockCardUpdator struct {
	ctrl     *gomock.Controller
	recorder *MockCardUpdatorMockRecorder
}

// MockCardUpdatorMockRecorder is the mock recorder for MockCardUpdator.
type MockCardUpdatorMockRecorder struct {
	mock *MockCardUpdator
}

// NewMockCardUpdator creates a new mock instance.
func NewMockCardUpdator(ctrl *gomock.Controller) *MockCardUpdator {
	mock := &MockCardUpdator{ctrl: ctrl}
	mock.recorder = &MockCardUpdatorMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockCardUpdator) EXPECT() *MockCardUpdatorMockRecorder {
	return m.recorder
}

// DeactivateClientCard mocks base method.
func (m *MockCardUpdator) DeactivateClientCard(ctx context.Context, cardID uint64) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeactivateClientCard", ctx, cardID)
	ret0, _ := ret[0].(error)
	return ret0
}

// DeactivateClientCard indicates an expected call of DeactivateClientCard.
func (mr *MockCardUpdatorMockRecorder) DeactivateClientCard(ctx, cardID interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeactivateClientCard", reflect.TypeOf((*MockCardUpdator)(nil).DeactivateClientCard), ctx, cardID)
}

// ModifyCardPan mocks base method.
func (m *MockCardUpdator) ModifyCardPan(ctx context.Context, cardID uint64, encryptedPan string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ModifyCardPan", ctx, cardID, encryptedPan)
	ret0, _ := ret[0].(error)
	return ret0
}

// ModifyCardPan indicates an expected call of ModifyCardPan.
func (mr *MockCardUpdatorMockRecorder) ModifyCardPan(ctx, cardID, encryptedPan interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ModifyCardPan", reflect.TypeOf((*MockCardUpdator)(nil).ModifyCardPan), ctx, cardID, encryptedPan)
}

// SetExpiredBatch mocks base method.
func (m *MockCardUpdator) SetExpiredBatch(ctx context.Context, cardIDs []uint64) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SetExpiredBatch", ctx, cardIDs)
	ret0, _ := ret[0].(error)
	return ret0
}

// SetExpiredBatch indicates an expected call of SetExpiredBatch.
func (mr *MockCardUpdatorMockRecorder) SetExpiredBatch(ctx, cardIDs interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetExpiredBatch", reflect.TypeOf((*MockCardUpdator)(nil).SetExpiredBatch), ctx, cardIDs)
}

// UpdateApproved mocks base method.
func (m *MockCardUpdator) UpdateApproved(ctx context.Context, cardId uint64, approvedState bool) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateApproved", ctx, cardId, approvedState)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpdateApproved indicates an expected call of UpdateApproved.
func (mr *MockCardUpdatorMockRecorder) UpdateApproved(ctx, cardId, approvedState interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateApproved", reflect.TypeOf((*MockCardUpdator)(nil).UpdateApproved), ctx, cardId, approvedState)
}

// UpdateSaveAccess mocks base method.
func (m *MockCardUpdator) UpdateSaveAccess(ctx context.Context, cardId uint64, saveAccess bool) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateSaveAccess", ctx, cardId, saveAccess)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpdateSaveAccess indicates an expected call of UpdateSaveAccess.
func (mr *MockCardUpdatorMockRecorder) UpdateSaveAccess(ctx, cardId, saveAccess interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateSaveAccess", reflect.TypeOf((*MockCardUpdator)(nil).UpdateSaveAccess), ctx, cardId, saveAccess)
}

// MockBulkCardUpdater is a mock of BulkCardUpdater interface.
type MockBulkCardUpdater struct {
	ctrl     *gomock.Controller
	recorder *MockBulkCardUpdaterMockRecorder
}

// MockBulkCardUpdaterMockRecorder is the mock recorder for MockBulkCardUpdater.
type MockBulkCardUpdaterMockRecorder struct {
	mock *MockBulkCardUpdater
}

// NewMockBulkCardUpdater creates a new mock instance.
func NewMockBulkCardUpdater(ctrl *gomock.Controller) *MockBulkCardUpdater {
	mock := &MockBulkCardUpdater{ctrl: ctrl}
	mock.recorder = &MockBulkCardUpdaterMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockBulkCardUpdater) EXPECT() *MockBulkCardUpdaterMockRecorder {
	return m.recorder
}

// UpdateEncryptData mocks base method.
func (m *MockBulkCardUpdater) UpdateEncryptData(ctx context.Context, cards []*model.Card) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateEncryptData", ctx, cards)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpdateEncryptData indicates an expected call of UpdateEncryptData.
func (mr *MockBulkCardUpdaterMockRecorder) UpdateEncryptData(ctx, cards interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateEncryptData", reflect.TypeOf((*MockBulkCardUpdater)(nil).UpdateEncryptData), ctx, cards)
}

// UpdateHashedPan mocks base method.
func (m *MockBulkCardUpdater) UpdateHashedPan(ctx context.Context, cards []*model.Card) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateHashedPan", ctx, cards)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpdateHashedPan indicates an expected call of UpdateHashedPan.
func (mr *MockBulkCardUpdaterMockRecorder) UpdateHashedPan(ctx, cards interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateHashedPan", reflect.TypeOf((*MockBulkCardUpdater)(nil).UpdateHashedPan), ctx, cards)
}

// MockClientor is a mock of Clientor interface.
type MockClientor struct {
	ctrl     *gomock.Controller
	recorder *MockClientorMockRecorder
}

// MockClientorMockRecorder is the mock recorder for MockClientor.
type MockClientorMockRecorder struct {
	mock *MockClientor
}

// NewMockClientor creates a new mock instance.
func NewMockClientor(ctrl *gomock.Controller) *MockClientor {
	mock := &MockClientor{ctrl: ctrl}
	mock.recorder = &MockClientorMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockClientor) EXPECT() *MockClientorMockRecorder {
	return m.recorder
}

// Create mocks base method.
func (m *MockClientor) Create(ctx context.Context, client *model.Client) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Create", ctx, client)
	ret0, _ := ret[0].(error)
	return ret0
}

// Create indicates an expected call of Create.
func (mr *MockClientorMockRecorder) Create(ctx, client interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Create", reflect.TypeOf((*MockClientor)(nil).Create), ctx, client)
}

// CreateClient mocks base method.
func (m *MockClientor) CreateClient(ctx context.Context, projectClientId, projectId uint64) (*model.Client, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateClient", ctx, projectClientId, projectId)
	ret0, _ := ret[0].(*model.Client)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateClient indicates an expected call of CreateClient.
func (mr *MockClientorMockRecorder) CreateClient(ctx, projectClientId, projectId interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateClient", reflect.TypeOf((*MockClientor)(nil).CreateClient), ctx, projectClientId, projectId)
}

// GetByFilter mocks base method.
func (m *MockClientor) GetByFilter(ctx context.Context, request schema.ClientRequest, pagination *middlewares.PaginationInfo, isApproved *bool) ([]model.Client, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetByFilter", ctx, request, pagination, isApproved)
	ret0, _ := ret[0].([]model.Client)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetByFilter indicates an expected call of GetByFilter.
func (mr *MockClientorMockRecorder) GetByFilter(ctx, request, pagination, isApproved interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetByFilter", reflect.TypeOf((*MockClientor)(nil).GetByFilter), ctx, request, pagination, isApproved)
}

// GetByID mocks base method.
func (m *MockClientor) GetByID(ctx context.Context, ID uint64) (*model.Client, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetByID", ctx, ID)
	ret0, _ := ret[0].(*model.Client)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetByID indicates an expected call of GetByID.
func (mr *MockClientorMockRecorder) GetByID(ctx, ID interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetByID", reflect.TypeOf((*MockClientor)(nil).GetByID), ctx, ID)
}

// GetByProject mocks base method.
func (m *MockClientor) GetByProject(ctx context.Context, projectId uint64, projectClientId string) (*model.Client, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetByProject", ctx, projectId, projectClientId)
	ret0, _ := ret[0].(*model.Client)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetByProject indicates an expected call of GetByProject.
func (mr *MockClientorMockRecorder) GetByProject(ctx, projectId, projectClientId interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetByProject", reflect.TypeOf((*MockClientor)(nil).GetByProject), ctx, projectId, projectClientId)
}

// GetProjectClients mocks base method.
func (m *MockClientor) GetProjectClients(ctx context.Context, pagination *middlewares.PaginationInfo, projectId uint64) ([]*model.Client, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetProjectClients", ctx, pagination, projectId)
	ret0, _ := ret[0].([]*model.Client)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetProjectClients indicates an expected call of GetProjectClients.
func (mr *MockClientorMockRecorder) GetProjectClients(ctx, pagination, projectId interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetProjectClients", reflect.TypeOf((*MockClientor)(nil).GetProjectClients), ctx, pagination, projectId)
}

// MockCardTokener is a mock of CardTokener interface.
type MockCardTokener struct {
	ctrl     *gomock.Controller
	recorder *MockCardTokenerMockRecorder
}

// MockCardTokenerMockRecorder is the mock recorder for MockCardTokener.
type MockCardTokenerMockRecorder struct {
	mock *MockCardTokener
}

// NewMockCardTokener creates a new mock instance.
func NewMockCardTokener(ctrl *gomock.Controller) *MockCardTokener {
	mock := &MockCardTokener{ctrl: ctrl}
	mock.recorder = &MockCardTokenerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockCardTokener) EXPECT() *MockCardTokenerMockRecorder {
	return m.recorder
}

// Create mocks base method.
func (m *MockCardTokener) Create(ctx context.Context, token model.Token) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Create", ctx, token)
	ret0, _ := ret[0].(error)
	return ret0
}

// Create indicates an expected call of Create.
func (mr *MockCardTokenerMockRecorder) Create(ctx, token interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Create", reflect.TypeOf((*MockCardTokener)(nil).Create), ctx, token)
}

// GetAll mocks base method.
func (m *MockCardTokener) GetAll(ctx context.Context, pagination *middlewares.PaginationInfo) ([]*model.Token, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAll", ctx, pagination)
	ret0, _ := ret[0].([]*model.Token)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAll indicates an expected call of GetAll.
func (mr *MockCardTokenerMockRecorder) GetAll(ctx, pagination interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAll", reflect.TypeOf((*MockCardTokener)(nil).GetAll), ctx, pagination)
}

// GetByCard mocks base method.
func (m *MockCardTokener) GetByCard(ctx context.Context, cardIds, acquirerIds, terminalIds []uint64) ([]model.Token, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetByCard", ctx, cardIds, acquirerIds, terminalIds)
	ret0, _ := ret[0].([]model.Token)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetByCard indicates an expected call of GetByCard.
func (mr *MockCardTokenerMockRecorder) GetByCard(ctx, cardIds, acquirerIds, terminalIds interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetByCard", reflect.TypeOf((*MockCardTokener)(nil).GetByCard), ctx, cardIds, acquirerIds, terminalIds)
}

// GetByCardAndAcquirer mocks base method.
func (m *MockCardTokener) GetByCardAndAcquirer(ctx context.Context, cardId, acquirerId uint64) (model.Token, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetByCardAndAcquirer", ctx, cardId, acquirerId)
	ret0, _ := ret[0].(model.Token)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetByCardAndAcquirer indicates an expected call of GetByCardAndAcquirer.
func (mr *MockCardTokenerMockRecorder) GetByCardAndAcquirer(ctx, cardId, acquirerId interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetByCardAndAcquirer", reflect.TypeOf((*MockCardTokener)(nil).GetByCardAndAcquirer), ctx, cardId, acquirerId)
}

// GetTokensByPan mocks base method.
func (m *MockCardTokener) GetTokensByPan(ctx context.Context, projectId, projectClientId uint64, maskedPan string) (string, uint64, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetTokensByPan", ctx, projectId, projectClientId, maskedPan)
	ret0, _ := ret[0].(string)
	ret1, _ := ret[1].(uint64)
	ret2, _ := ret[2].(error)
	return ret0, ret1, ret2
}

// GetTokensByPan indicates an expected call of GetTokensByPan.
func (mr *MockCardTokenerMockRecorder) GetTokensByPan(ctx, projectId, projectClientId, maskedPan interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetTokensByPan", reflect.TypeOf((*MockCardTokener)(nil).GetTokensByPan), ctx, projectId, projectClientId, maskedPan)
}

// MockTokenUpdator is a mock of TokenUpdator interface.
type MockTokenUpdator struct {
	ctrl     *gomock.Controller
	recorder *MockTokenUpdatorMockRecorder
}

// MockTokenUpdatorMockRecorder is the mock recorder for MockTokenUpdator.
type MockTokenUpdatorMockRecorder struct {
	mock *MockTokenUpdator
}

// NewMockTokenUpdator creates a new mock instance.
func NewMockTokenUpdator(ctrl *gomock.Controller) *MockTokenUpdator {
	mock := &MockTokenUpdator{ctrl: ctrl}
	mock.recorder = &MockTokenUpdatorMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockTokenUpdator) EXPECT() *MockTokenUpdatorMockRecorder {
	return m.recorder
}

// DeactivateToken mocks base method.
func (m *MockTokenUpdator) DeactivateToken(ctx context.Context) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeactivateToken", ctx)
	ret0, _ := ret[0].(error)
	return ret0
}

// DeactivateToken indicates an expected call of DeactivateToken.
func (mr *MockTokenUpdatorMockRecorder) DeactivateToken(ctx interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeactivateToken", reflect.TypeOf((*MockTokenUpdator)(nil).DeactivateToken), ctx)
}

// Update mocks base method.
func (m *MockTokenUpdator) Update(ctx context.Context, tokenId uint64, tokenRequest *events.SaveToken) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Update", ctx, tokenId, tokenRequest)
	ret0, _ := ret[0].(error)
	return ret0
}

// Update indicates an expected call of Update.
func (mr *MockTokenUpdatorMockRecorder) Update(ctx, tokenId, tokenRequest interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Update", reflect.TypeOf((*MockTokenUpdator)(nil).Update), ctx, tokenId, tokenRequest)
}

// MockCardValidator is a mock of CardValidator interface.
type MockCardValidator struct {
	ctrl     *gomock.Controller
	recorder *MockCardValidatorMockRecorder
}

// MockCardValidatorMockRecorder is the mock recorder for MockCardValidator.
type MockCardValidatorMockRecorder struct {
	mock *MockCardValidator
}

// NewMockCardValidator creates a new mock instance.
func NewMockCardValidator(ctrl *gomock.Controller) *MockCardValidator {
	mock := &MockCardValidator{ctrl: ctrl}
	mock.recorder = &MockCardValidatorMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockCardValidator) EXPECT() *MockCardValidatorMockRecorder {
	return m.recorder
}

// GetByProject mocks base method.
func (m *MockCardValidator) GetByProject(ctx context.Context, projectId uint64) (*model.CardValidity, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetByProject", ctx, projectId)
	ret0, _ := ret[0].(*model.CardValidity)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetByProject indicates an expected call of GetByProject.
func (mr *MockCardValidatorMockRecorder) GetByProject(ctx, projectId interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetByProject", reflect.TypeOf((*MockCardValidator)(nil).GetByProject), ctx, projectId)
}

// MockClientVerificationManager is a mock of ClientVerificationManager interface.
type MockClientVerificationManager struct {
	ctrl     *gomock.Controller
	recorder *MockClientVerificationManagerMockRecorder
}

// MockClientVerificationManagerMockRecorder is the mock recorder for MockClientVerificationManager.
type MockClientVerificationManagerMockRecorder struct {
	mock *MockClientVerificationManager
}

// NewMockClientVerificationManager creates a new mock instance.
func NewMockClientVerificationManager(ctrl *gomock.Controller) *MockClientVerificationManager {
	mock := &MockClientVerificationManager{ctrl: ctrl}
	mock.recorder = &MockClientVerificationManagerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockClientVerificationManager) EXPECT() *MockClientVerificationManagerMockRecorder {
	return m.recorder
}

// BindVerificationUserIDToClient mocks base method.
func (m *MockClientVerificationManager) BindVerificationUserIDToClient(ctx context.Context, projectID uint64, projectClientID string, verificationUserID uint64) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BindVerificationUserIDToClient", ctx, projectID, projectClientID, verificationUserID)
	ret0, _ := ret[0].(error)
	return ret0
}

// BindVerificationUserIDToClient indicates an expected call of BindVerificationUserIDToClient.
func (mr *MockClientVerificationManagerMockRecorder) BindVerificationUserIDToClient(ctx, projectID, projectClientID, verificationUserID interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BindVerificationUserIDToClient", reflect.TypeOf((*MockClientVerificationManager)(nil).BindVerificationUserIDToClient), ctx, projectID, projectClientID, verificationUserID)
}

// BlockClient mocks base method.
func (m *MockClientVerificationManager) BlockClient(ctx context.Context, projectID uint64, projectClientID string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BlockClient", ctx, projectID, projectClientID)
	ret0, _ := ret[0].(error)
	return ret0
}

// BlockClient indicates an expected call of BlockClient.
func (mr *MockClientVerificationManagerMockRecorder) BlockClient(ctx, projectID, projectClientID interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BlockClient", reflect.TypeOf((*MockClientVerificationManager)(nil).BlockClient), ctx, projectID, projectClientID)
}

// GetByVerificationID mocks base method.
func (m *MockClientVerificationManager) GetByVerificationID(ctx context.Context, verificationUserID uint64) ([]model.Client, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetByVerificationID", ctx, verificationUserID)
	ret0, _ := ret[0].([]model.Client)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetByVerificationID indicates an expected call of GetByVerificationID.
func (mr *MockClientVerificationManagerMockRecorder) GetByVerificationID(ctx, verificationUserID interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetByVerificationID", reflect.TypeOf((*MockClientVerificationManager)(nil).GetByVerificationID), ctx, verificationUserID)
}

// GetClientActiveness mocks base method.
func (m *MockClientVerificationManager) GetClientActiveness(ctx context.Context, projectID uint64, projectClientID string) (bool, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetClientActiveness", ctx, projectID, projectClientID)
	ret0, _ := ret[0].(bool)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetClientActiveness indicates an expected call of GetClientActiveness.
func (mr *MockClientVerificationManagerMockRecorder) GetClientActiveness(ctx, projectID, projectClientID interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetClientActiveness", reflect.TypeOf((*MockClientVerificationManager)(nil).GetClientActiveness), ctx, projectID, projectClientID)
}

// UnbindVerificationUserIDFromClient mocks base method.
func (m *MockClientVerificationManager) UnbindVerificationUserIDFromClient(ctx context.Context, projectID uint64, projectClientID string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UnbindVerificationUserIDFromClient", ctx, projectID, projectClientID)
	ret0, _ := ret[0].(error)
	return ret0
}

// UnbindVerificationUserIDFromClient indicates an expected call of UnbindVerificationUserIDFromClient.
func (mr *MockClientVerificationManagerMockRecorder) UnbindVerificationUserIDFromClient(ctx, projectID, projectClientID interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UnbindVerificationUserIDFromClient", reflect.TypeOf((*MockClientVerificationManager)(nil).UnbindVerificationUserIDFromClient), ctx, projectID, projectClientID)
}

// MockProjectMaskFormatter is a mock of ProjectMaskFormatter interface.
type MockProjectMaskFormatter struct {
	ctrl     *gomock.Controller
	recorder *MockProjectMaskFormatterMockRecorder
}

// MockProjectMaskFormatterMockRecorder is the mock recorder for MockProjectMaskFormatter.
type MockProjectMaskFormatterMockRecorder struct {
	mock *MockProjectMaskFormatter
}

// NewMockProjectMaskFormatter creates a new mock instance.
func NewMockProjectMaskFormatter(ctrl *gomock.Controller) *MockProjectMaskFormatter {
	mock := &MockProjectMaskFormatter{ctrl: ctrl}
	mock.recorder = &MockProjectMaskFormatterMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockProjectMaskFormatter) EXPECT() *MockProjectMaskFormatterMockRecorder {
	return m.recorder
}

// Create mocks base method.
func (m *MockProjectMaskFormatter) Create(ctx context.Context, maskFormat *model.ProjectMaskFormat) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Create", ctx, maskFormat)
	ret0, _ := ret[0].(error)
	return ret0
}

// Create indicates an expected call of Create.
func (mr *MockProjectMaskFormatterMockRecorder) Create(ctx, maskFormat interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Create", reflect.TypeOf((*MockProjectMaskFormatter)(nil).Create), ctx, maskFormat)
}

// GetByProjectID mocks base method.
func (m *MockProjectMaskFormatter) GetByProjectID(ctx context.Context, projectID uint64) (*model.ProjectMaskFormat, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetByProjectID", ctx, projectID)
	ret0, _ := ret[0].(*model.ProjectMaskFormat)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetByProjectID indicates an expected call of GetByProjectID.
func (mr *MockProjectMaskFormatterMockRecorder) GetByProjectID(ctx, projectID interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetByProjectID", reflect.TypeOf((*MockProjectMaskFormatter)(nil).GetByProjectID), ctx, projectID)
}

// UpdateByProjectId mocks base method.
func (m *MockProjectMaskFormatter) UpdateByProjectId(ctx context.Context, projectID uint64, placeholderSign string, withSeparator *bool) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateByProjectId", ctx, projectID, placeholderSign, withSeparator)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpdateByProjectId indicates an expected call of UpdateByProjectId.
func (mr *MockProjectMaskFormatterMockRecorder) UpdateByProjectId(ctx, projectID, placeholderSign, withSeparator interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateByProjectId", reflect.TypeOf((*MockProjectMaskFormatter)(nil).UpdateByProjectId), ctx, projectID, placeholderSign, withSeparator)
}
