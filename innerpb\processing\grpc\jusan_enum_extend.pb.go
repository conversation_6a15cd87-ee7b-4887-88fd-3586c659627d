// Code generated by protoc-gen-go-mvp. DO NOT EDIT.
// versions:
// - protoc-gen-go-mvp v2.0.1
// created by mvp authors

package grpc

import (
	math "math"
	strings "strings"
)

func (val JusanResponseStatus) Synonym() JusanResponseStatus {
	if _, ok := JusanResponseStatus_name[int32(val)]; ok {
		return val
	}

	return JusanResponseStatus(math.MinInt32)
}

func (val JusanResponseStatus) Int() int {
	return int(val.Synonym())
}

func (val JusanResponseStatus) IntPtr() *int {
	result := int(val.Synonym())
	return &result
}

func (val JusanResponseStatus) Int32() int32 {
	return int32(val.Synonym())
}

func (val JusanResponseStatus) Int32Ptr() *int32 {
	result := int32(val.Synonym())
	return &result
}

func (val JusanResponseStatus) Int64() int64 {
	return int64(val.Synonym())
}

func (val JusanResponseStatus) Int64Ptr() *int64 {
	result := int64(val.Synonym())
	return &result
}

func (val JusanResponseStatus) Uint() uint {
	return uint(val.Synonym())
}

func (val JusanResponseStatus) UintPtr() *uint {
	result := uint(val.Synonym())
	return &result
}

func (val JusanResponseStatus) Uint32() uint32 {
	return uint32(val.Synonym())
}

func (val JusanResponseStatus) Uint32Ptr() *uint32 {
	result := uint32(val.Synonym())
	return &result
}

func (val JusanResponseStatus) Uint64() uint64 {
	return uint64(val.Synonym())
}

func (val JusanResponseStatus) Uint64Ptr() *uint64 {
	result := uint64(val.Synonym())
	return &result
}

func (val JusanResponseStatus) IsKnown() bool {
	return val.Synonym() != JusanResponseStatus(math.MinInt32)
}

func ConvertIntToJusanResponseStatus(in int) JusanResponseStatus {
	return JusanResponseStatus(in).Synonym()
}

func ConvertUintToJusanResponseStatus(in uint) JusanResponseStatus {
	return JusanResponseStatus(in).Synonym()
}

func ConvertInt32ToJusanResponseStatus(in int32) JusanResponseStatus {
	return JusanResponseStatus(in).Synonym()
}

func ConvertUint32ToJusanResponseStatus(in uint32) JusanResponseStatus {
	return JusanResponseStatus(in).Synonym()
}

func ConvertInt64ToJusanResponseStatus(in int64) JusanResponseStatus {
	return JusanResponseStatus(in).Synonym()
}

func ConvertUint64ToJusanResponseStatus(in uint64) JusanResponseStatus {
	return JusanResponseStatus(in).Synonym()
}

var JusanResponseStatus_Lower_value = map[string]JusanResponseStatus{
	"1:1": 0,
	"2:1": 1,
	"3:1": 2,
	"1:0": 3,
	"2:0": 4,
	"1:2": 5,
	"2:2": 6,
	"3:2": 7,
	"4:0": 8,
	"4:1": 9,
	"4:2": 10,
}

func ConvertStringToJusanResponseStatus(in string) JusanResponseStatus {
	if result, ok := JusanResponseStatus_value[in]; ok {
		return JusanResponseStatus(result)
	}

	if result, ok := JusanResponseStatus_Lower_value[strings.ToLower(in)]; ok {
		return JusanResponseStatus(result)
	}

	return JusanResponseStatus(math.MinInt32)
}

var SliceJusanResponseStatusConvert *sliceJusanResponseStatusConvert

type sliceJusanResponseStatusConvert struct{}

func (*sliceJusanResponseStatusConvert) Synonym(in []JusanResponseStatus) []JusanResponseStatus {
	result := make([]JusanResponseStatus, len(in))
	for i, v := range in {
		result[i] = v.Synonym()
	}

	return result
}

func (*sliceJusanResponseStatusConvert) Int32(in []JusanResponseStatus) []int32 {
	result := make([]int32, len(in))
	for i, v := range in {
		result[i] = v.Int32()
	}

	return result
}

func (*sliceJusanResponseStatusConvert) Uint32(in []JusanResponseStatus) []uint32 {
	result := make([]uint32, len(in))
	for i, v := range in {
		result[i] = v.Uint32()
	}

	return result
}

func (*sliceJusanResponseStatusConvert) Uint64(in []JusanResponseStatus) []uint64 {
	result := make([]uint64, len(in))
	for i, v := range in {
		result[i] = v.Uint64()
	}

	return result
}

func (*sliceJusanResponseStatusConvert) Int64(in []JusanResponseStatus) []int64 {
	result := make([]int64, len(in))
	for i, v := range in {
		result[i] = v.Int64()
	}

	return result
}

func (*sliceJusanResponseStatusConvert) Uint(in []JusanResponseStatus) []uint {
	result := make([]uint, len(in))
	for i, v := range in {
		result[i] = v.Uint()
	}

	return result
}

func (*sliceJusanResponseStatusConvert) Int(in []JusanResponseStatus) []int {
	result := make([]int, len(in))
	for i, v := range in {
		result[i] = v.Int()
	}

	return result
}

func (*sliceJusanResponseStatusConvert) IsKnown(in []JusanResponseStatus) []bool {
	result := make([]bool, len(in))
	for i, v := range in {
		result[i] = v.IsKnown()
	}

	return result
}

func (*sliceJusanResponseStatusConvert) ConvertIntToJusanResponseStatus(in []int) []JusanResponseStatus {
	result := make([]JusanResponseStatus, len(in))
	for i, v := range in {
		result[i] = ConvertIntToJusanResponseStatus(v)
	}

	return result
}

func (*sliceJusanResponseStatusConvert) ConvertUintToJusanResponseStatus(in []uint) []JusanResponseStatus {
	result := make([]JusanResponseStatus, len(in))
	for i, v := range in {
		result[i] = ConvertUintToJusanResponseStatus(v)
	}

	return result
}

func (*sliceJusanResponseStatusConvert) ConvertInt32ToJusanResponseStatus(in []int32) []JusanResponseStatus {
	result := make([]JusanResponseStatus, len(in))
	for i, v := range in {
		result[i] = ConvertInt32ToJusanResponseStatus(v)
	}

	return result
}

func (*sliceJusanResponseStatusConvert) ConvertUint32ToJusanResponseStatus(in []uint32) []JusanResponseStatus {
	result := make([]JusanResponseStatus, len(in))
	for i, v := range in {
		result[i] = ConvertUint32ToJusanResponseStatus(v)
	}

	return result
}

func (*sliceJusanResponseStatusConvert) ConvertInt64ToJusanResponseStatus(in []int64) []JusanResponseStatus {
	result := make([]JusanResponseStatus, len(in))
	for i, v := range in {
		result[i] = ConvertInt64ToJusanResponseStatus(v)
	}

	return result
}

func (*sliceJusanResponseStatusConvert) ConvertUint64ToJusanResponseStatus(in []uint64) []JusanResponseStatus {
	result := make([]JusanResponseStatus, len(in))
	for i, v := range in {
		result[i] = ConvertUint64ToJusanResponseStatus(v)
	}

	return result
}

func (*sliceJusanResponseStatusConvert) ConvertStringToJusanResponseStatus(in []string) []JusanResponseStatus {
	result := make([]JusanResponseStatus, len(in))
	for i, v := range in {
		result[i] = ConvertStringToJusanResponseStatus(v)
	}

	return result
}

func NewJusanResponseStatusUsage() *JusanResponseStatusUsage {
	return &JusanResponseStatusUsage{
		enumMap: map[JusanResponseStatus]bool{
			JusanResponseStatus_Stage1Status1: false,
			JusanResponseStatus_Stage2Status1: false,
			JusanResponseStatus_Stage3Status1: false,
			JusanResponseStatus_Stage1Status0: false,
			JusanResponseStatus_Stage2Status0: false,
			JusanResponseStatus_Stage1Status2: false,
			JusanResponseStatus_Stage2Status2: false,
			JusanResponseStatus_Stage3Status2: false,
			JusanResponseStatus_Stage4Status0: false,
			JusanResponseStatus_Stage4Status1: false,
			JusanResponseStatus_Stage4Status2: false,
		},
	}
}

func IsJusanResponseStatus(target JusanResponseStatus, matches ...JusanResponseStatus) bool {
	for _, v := range matches {
		if target == v {
			return true
		}
	}

	return false
}

type JusanResponseStatusUsage struct {
	enumMap map[JusanResponseStatus]bool
}

func (u *JusanResponseStatusUsage) Use(slice ...JusanResponseStatus) *JusanResponseStatusUsage {
	for _, in := range slice {
		u.enumMap[in] = true
	}

	return u
}

func (u *JusanResponseStatusUsage) Check() []string {
	var result []string
	for k, v := range u.enumMap {
		if v {
			continue
		}

		result = append(result, k.String())
	}

	return result
}

func (u *JusanResponseStatusUsage) JusanResponseStatus_Stage1Status1() JusanResponseStatus {
	u.Use(JusanResponseStatus_Stage1Status1)
	return JusanResponseStatus_Stage1Status1
}

func (u *JusanResponseStatusUsage) JusanResponseStatus_Stage2Status1() JusanResponseStatus {
	u.Use(JusanResponseStatus_Stage2Status1)
	return JusanResponseStatus_Stage2Status1
}

func (u *JusanResponseStatusUsage) JusanResponseStatus_Stage3Status1() JusanResponseStatus {
	u.Use(JusanResponseStatus_Stage3Status1)
	return JusanResponseStatus_Stage3Status1
}

func (u *JusanResponseStatusUsage) JusanResponseStatus_Stage1Status0() JusanResponseStatus {
	u.Use(JusanResponseStatus_Stage1Status0)
	return JusanResponseStatus_Stage1Status0
}

func (u *JusanResponseStatusUsage) JusanResponseStatus_Stage2Status0() JusanResponseStatus {
	u.Use(JusanResponseStatus_Stage2Status0)
	return JusanResponseStatus_Stage2Status0
}

func (u *JusanResponseStatusUsage) JusanResponseStatus_Stage1Status2() JusanResponseStatus {
	u.Use(JusanResponseStatus_Stage1Status2)
	return JusanResponseStatus_Stage1Status2
}

func (u *JusanResponseStatusUsage) JusanResponseStatus_Stage2Status2() JusanResponseStatus {
	u.Use(JusanResponseStatus_Stage2Status2)
	return JusanResponseStatus_Stage2Status2
}

func (u *JusanResponseStatusUsage) JusanResponseStatus_Stage3Status2() JusanResponseStatus {
	u.Use(JusanResponseStatus_Stage3Status2)
	return JusanResponseStatus_Stage3Status2
}

func (u *JusanResponseStatusUsage) JusanResponseStatus_Stage4Status0() JusanResponseStatus {
	u.Use(JusanResponseStatus_Stage4Status0)
	return JusanResponseStatus_Stage4Status0
}

func (u *JusanResponseStatusUsage) JusanResponseStatus_Stage4Status1() JusanResponseStatus {
	u.Use(JusanResponseStatus_Stage4Status1)
	return JusanResponseStatus_Stage4Status1
}

func (u *JusanResponseStatusUsage) JusanResponseStatus_Stage4Status2() JusanResponseStatus {
	u.Use(JusanResponseStatus_Stage4Status2)
	return JusanResponseStatus_Stage4Status2
}
