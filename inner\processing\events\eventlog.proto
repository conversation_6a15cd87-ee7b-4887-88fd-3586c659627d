syntax = "proto3";

option go_package = "git.local/sensitive/innerpb/processing/events";

import "google/protobuf/timestamp.proto";
import "mvp/proto/events.proto";

message SaveLog {
  option (mvp.events.is_event) = true;
  string method = 1;
  string url = 2;
  google.protobuf.Timestamp event_at = 3;
  string entity_id = 4;
  string request = 5;
  string user_email = 6;
  string user_id = 7;
  string message = 8;
  string payload = 9;
  string collection = 10;
}
