// Code generated by protoc-gen-go-mvp. DO NOT EDIT.
// versions:
// - protoc-gen-go-mvp v2.0.1
// created by mvp authors

package grpc

import (
	context "context"
	fmt "fmt"
	cntx "git.local/sensitive/mvp/pkg/cntx"
	ctxzap "github.com/grpc-ecosystem/go-grpc-middleware/logging/zap/ctxzap"
	zap "go.uber.org/zap"
	grpc "google.golang.org/grpc"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
	structpb "google.golang.org/protobuf/types/known/structpb"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
)

func file_inner_processing_grpc_jusan_proto_string_structpb_Value(
	label string,
	in map[string]*structpb.Value,
) zap.Field {
	var res []zap.Field
	for key, val := range in {
		res = append(res, file_inner_processing_grpc_jusan_proto_message_ValueToZap(fmt.Sprint(key), val))
	}

	return zap.Dict(label, res...)
}

func file_inner_processing_grpc_jusan_proto_message_EmptyToZap(
	label string,
	in *emptypb.Empty,
) zap.Field {
	if in == nil {
		return zap.Reflect(label, in)
	}

	return zap.Dict(
		label,
	)
}

func file_inner_processing_grpc_jusan_proto_message_ListValueToZap(
	label string,
	in *structpb.ListValue,
) zap.Field {
	if in == nil {
		return zap.Reflect(label, in)
	}

	return zap.Dict(
		label,
		file_inner_processing_grpc_jusan_proto_message_ValueSliceToZap("Values", in.GetValues()),
	)
}

func file_inner_processing_grpc_jusan_proto_enum_NullValueToZap(
	label string,
	in structpb.NullValue,
) zap.Field {
	str, ok := structpb.NullValue_name[int32(in)]
	if !ok {
		str = fmt.Sprintf("unknown structpb.NullValue value %d", in)
	}

	return zap.Dict(
		label,
		zap.Any("value", structpb.NullValue(in)),
		zap.Any("name", str),
	)
}

func file_inner_processing_grpc_jusan_proto_message_StructToZap(
	label string,
	in *structpb.Struct,
) zap.Field {
	if in == nil {
		return zap.Reflect(label, in)
	}

	return zap.Dict(
		label,
		file_inner_processing_grpc_jusan_proto_string_structpb_Value("Fields", in.GetFields()),
	)
}

func file_inner_processing_grpc_jusan_proto_message_TimestampToZap(
	label string,
	in *timestamppb.Timestamp,
) zap.Field {
	if in == nil {
		return zap.Reflect(label, in)
	}

	return zap.Dict(
		label,
		zap.Any("Seconds", in.GetSeconds()),
		zap.Any("Nanos", in.GetNanos()),
	)
}

func file_inner_processing_grpc_jusan_proto_message_ValueToZap(
	label string,
	in *structpb.Value,
) zap.Field {
	if in == nil {
		return zap.Reflect(label, in)
	}

	return zap.Dict(
		label,
		file_inner_processing_grpc_jusan_proto_enum_NullValueToZap("NullValue", in.GetNullValue()),
		zap.Any("NumberValue", in.GetNumberValue()),
		zap.Any("StringValue", in.GetStringValue()),
		zap.Any("BoolValue", in.GetBoolValue()),
		file_inner_processing_grpc_jusan_proto_message_StructToZap("StructValue", in.GetStructValue()),
		file_inner_processing_grpc_jusan_proto_message_ListValueToZap("ListValue", in.GetListValue()),
	)
}

func file_inner_processing_grpc_jusan_proto_message_ValueSliceToZap(
	label string,
	in []*structpb.Value,
) zap.Field {
	var res []zap.Field
	for index, val := range in {
		res = append(res, file_inner_processing_grpc_jusan_proto_message_ValueToZap(fmt.Sprint(index), val))
	}

	return zap.Dict(label, res...)
}

func file_inner_processing_grpc_jusan_proto_enum_JusanResponseStatusToZap(
	label string,
	in JusanResponseStatus,
) zap.Field {
	str, ok := JusanResponseStatus_name[int32(in)]
	if !ok {
		str = fmt.Sprintf("unknown JusanResponseStatus value %d", in)
	}

	return zap.Dict(
		label,
		zap.Any("value", JusanResponseStatus(in)),
		zap.Any("name", str),
	)
}

func file_inner_processing_grpc_jusan_proto_message_JusanResponseStatusRefToZap(
	label string,
	in *JusanResponseStatusRef,
) zap.Field {
	if in == nil {
		return zap.Reflect(label, in)
	}

	return zap.Dict(
		label,
		zap.Any("Stage", in.GetStage()),
		zap.Any("Status", in.GetStatus()),
		zap.Any("Description", in.GetDescription()),
		file_inner_processing_grpc_jusan_proto_enum_EnumTransactionStatusToZap("TransactionStatus", in.GetTransactionStatus()),
	)
}

func file_inner_processing_grpc_jusan_proto_message_ApplePayRequestDataToZap(
	label string,
	in *ApplePayRequestData,
) zap.Field {
	if in == nil {
		return zap.Reflect(label, in)
	}

	return zap.Dict(
		label,
		zap.Any("TransactionId", in.GetTransactionId()),
		zap.Any("IsHold", in.GetIsHold()),
		zap.Any("Amount", in.GetAmount()),
		zap.Any("Description", in.GetDescription()),
		file_inner_processing_grpc_jusan_proto_message_CardDataForBankToZap("Card", in.GetCard()),
		file_inner_processing_grpc_jusan_proto_message_UserDataForBankToZap("User", in.GetUser()),
		file_inner_processing_grpc_jusan_proto_message_TerminalDataForBankToZap("Terminal", in.GetTerminal()),
		zap.Any("TransactionTypeId", in.GetTransactionTypeId()),
		file_inner_processing_grpc_jusan_proto_message_CardInfoToZap("CardInfo", in.GetCardInfo()),
		file_inner_processing_grpc_jusan_proto_message_MerchantInfoToZap("MerchantInfo", in.GetMerchantInfo()),
		zap.Any("Tavv", in.GetTavv()),
		zap.Any("Eci", in.GetEci()),
		file_inner_processing_grpc_jusan_proto_message_TimestampToZap("TransactionCreatedAt", in.GetTransactionCreatedAt()),
		zap.Any("AggregatedTypeId", in.GetAggregatedTypeId()),
		zap.Any("UserIpAddress", in.GetUserIpAddress()),
	)
}

func file_inner_processing_grpc_jusan_proto_message_ApplePayResponseDataToZap(
	label string,
	in *ApplePayResponseData,
) zap.Field {
	if in == nil {
		return zap.Reflect(label, in)
	}

	return zap.Dict(
		label,
		zap.Any("TransactionId", in.GetTransactionId()),
		zap.Any("BankReferenceId", in.GetBankReferenceId()),
		file_inner_processing_grpc_jusan_proto_enum_EnumTransactionStatusToZap("TransactionStatus", in.GetTransactionStatus()),
		zap.Any("CardId", in.GetCardId()),
		file_inner_processing_grpc_jusan_proto_message_ThreeDSDataFromBankToZap("ThreeDS", in.GetThreeDS()),
		file_inner_processing_grpc_jusan_proto_message_BankResponseToZap("BankResponse", in.GetBankResponse()),
		zap.Any("BankOrderId", in.GetBankOrderId()),
	)
}

func file_inner_processing_grpc_jusan_proto_message_BankResponseToZap(
	label string,
	in *BankResponse,
) zap.Field {
	if in == nil {
		return zap.Reflect(label, in)
	}

	return zap.Dict(
		label,
		zap.Any("Message", in.GetMessage()),
		zap.Any("Code", in.GetCode()),
		zap.Any("IntegrationCode", in.GetIntegrationCode()),
		zap.Any("IntegrationMessage", in.GetIntegrationMessage()),
	)
}

func file_inner_processing_grpc_jusan_proto_message_BankTransactionStatusRequestToZap(
	label string,
	in *BankTransactionStatusRequest,
) zap.Field {
	if in == nil {
		return zap.Reflect(label, in)
	}

	return zap.Dict(
		label,
		zap.Any("TransactionId", in.GetTransactionId()),
		zap.Any("TransactionType", in.GetTransactionType()),
		zap.Any("AcquirerCode", in.GetAcquirerCode()),
		file_inner_processing_grpc_jusan_proto_message_StructToZap("Config", in.GetConfig()),
		zap.Any("BankReferenceId", in.GetBankReferenceId()),
		zap.Any("BankOrderId", in.GetBankOrderId()),
		file_inner_processing_grpc_jusan_proto_message_TerminalDataForBankToZap("Terminal", in.GetTerminal()),
		file_inner_processing_grpc_jusan_proto_message_TimestampToZap("TransactionCreatedAt", in.GetTransactionCreatedAt()),
	)
}

func file_inner_processing_grpc_jusan_proto_message_BankTransactionStatusResponseToZap(
	label string,
	in *BankTransactionStatusResponse,
) zap.Field {
	if in == nil {
		return zap.Reflect(label, in)
	}

	return zap.Dict(
		label,
		zap.Any("TransactionId", in.GetTransactionId()),
		zap.Any("BankReferenceId", in.GetBankReferenceId()),
		file_inner_processing_grpc_jusan_proto_enum_EnumTransactionStatusToZap("TransactionStatus", in.GetTransactionStatus()),
		file_inner_processing_grpc_jusan_proto_message_BankResponseToZap("BankResponse", in.GetBankResponse()),
	)
}

func file_inner_processing_grpc_jusan_proto_message_BankTransactionStatusUnformatedRequestToZap(
	label string,
	in *BankTransactionStatusUnformatedRequest,
) zap.Field {
	if in == nil {
		return zap.Reflect(label, in)
	}

	return zap.Dict(
		label,
		zap.Any("TransactionId", in.GetTransactionId()),
		zap.Any("TransactionType", in.GetTransactionType()),
		zap.Any("AcquirerCode", in.GetAcquirerCode()),
		file_inner_processing_grpc_jusan_proto_message_StructToZap("Config", in.GetConfig()),
		zap.Any("BankReferenceId", in.GetBankReferenceId()),
		zap.Any("BankOrderId", in.GetBankOrderId()),
		file_inner_processing_grpc_jusan_proto_message_TerminalDataForBankToZap("Terminal", in.GetTerminal()),
		file_inner_processing_grpc_jusan_proto_message_TimestampToZap("TransactionCreatedAt", in.GetTransactionCreatedAt()),
	)
}

func file_inner_processing_grpc_jusan_proto_message_BankTransactionStatusUnformatedResponseToZap(
	label string,
	in *BankTransactionStatusUnformatedResponse,
) zap.Field {
	if in == nil {
		return zap.Reflect(label, in)
	}

	return zap.Dict(
		label,
		zap.Any("TransactionId", in.GetTransactionId()),
		zap.Any("BankMessage", in.GetBankMessage()),
	)
}

func file_inner_processing_grpc_jusan_proto_message_CancelRequestToZap(
	label string,
	in *CancelRequest,
) zap.Field {
	if in == nil {
		return zap.Reflect(label, in)
	}

	return zap.Dict(
		label,
		zap.Any("TransactionId", in.GetTransactionId()),
		zap.Any("Amount", in.GetAmount()),
		file_inner_processing_grpc_jusan_proto_message_TerminalDataForBankToZap("Terminal", in.GetTerminal()),
		zap.Any("BankReferenceId", in.GetBankReferenceId()),
		zap.Any("BankOrderId", in.GetBankOrderId()),
		zap.Any("Description", in.GetDescription()),
		file_inner_processing_grpc_jusan_proto_message_TimestampToZap("TransactionCreatedAt", in.GetTransactionCreatedAt()),
	)
}

func file_inner_processing_grpc_jusan_proto_message_CancelResponseToZap(
	label string,
	in *CancelResponse,
) zap.Field {
	if in == nil {
		return zap.Reflect(label, in)
	}

	return zap.Dict(
		label,
		zap.Any("TransactionId", in.GetTransactionId()),
		zap.Any("BankReferenceId", in.GetBankReferenceId()),
		zap.Any("BankOrderId", in.GetBankOrderId()),
		file_inner_processing_grpc_jusan_proto_message_BankResponseToZap("BankResponse", in.GetBankResponse()),
	)
}

func file_inner_processing_grpc_jusan_proto_message_CardDataForBankToZap(
	label string,
	in *CardDataForBank,
) zap.Field {
	if in == nil {
		return zap.Reflect(label, in)
	}

	return zap.Dict(
		label,
		zap.Any("Pan", "[***hidden***]"),
		zap.Any("ExpMonth", in.GetExpMonth()),
		zap.Any("ExpYear", in.GetExpYear()),
		zap.Any("Cvc", "[***hidden***]"),
		zap.Any("FullName", in.GetFullName()),
		zap.Any("Token", in.GetToken()),
		zap.Any("Save", in.GetSave()),
	)
}

func file_inner_processing_grpc_jusan_proto_message_CardInfoToZap(
	label string,
	in *CardInfo,
) zap.Field {
	if in == nil {
		return zap.Reflect(label, in)
	}

	return zap.Dict(
		label,
		zap.Any("IpsId", in.GetIpsId()),
		zap.Any("IssuerId", in.GetIssuerId()),
		zap.Any("CountryId", in.GetCountryId()),
	)
}

func file_inner_processing_grpc_jusan_proto_message_ChargeRequestToZap(
	label string,
	in *ChargeRequest,
) zap.Field {
	if in == nil {
		return zap.Reflect(label, in)
	}

	return zap.Dict(
		label,
		zap.Any("TransactionId", in.GetTransactionId()),
		zap.Any("Amount", in.GetAmount()),
		file_inner_processing_grpc_jusan_proto_message_TerminalDataForBankToZap("Terminal", in.GetTerminal()),
		zap.Any("BankReferenceId", in.GetBankReferenceId()),
		zap.Any("BankOrderId", in.GetBankOrderId()),
		file_inner_processing_grpc_jusan_proto_message_TimestampToZap("TransactionCreatedAt", in.GetTransactionCreatedAt()),
	)
}

func file_inner_processing_grpc_jusan_proto_message_ChargeResponseToZap(
	label string,
	in *ChargeResponse,
) zap.Field {
	if in == nil {
		return zap.Reflect(label, in)
	}

	return zap.Dict(
		label,
		zap.Any("TransactionId", in.GetTransactionId()),
		zap.Any("BankReferenceId", in.GetBankReferenceId()),
		zap.Any("BankOrderId", in.GetBankOrderId()),
		file_inner_processing_grpc_jusan_proto_message_BankResponseToZap("BankResponse", in.GetBankResponse()),
	)
}

func file_inner_processing_grpc_jusan_proto_message_FingerprintToZap(
	label string,
	in *Fingerprint,
) zap.Field {
	if in == nil {
		return zap.Reflect(label, in)
	}

	return zap.Dict(
		label,
		zap.Any("MethodUrl", in.GetMethodUrl()),
		zap.Any("MethodData", in.GetMethodData()),
	)
}

func file_inner_processing_grpc_jusan_proto_message_GetAcquirerIdentifierResponseToZap(
	label string,
	in *GetAcquirerIdentifierResponse,
) zap.Field {
	if in == nil {
		return zap.Reflect(label, in)
	}

	return zap.Dict(
		label,
		zap.Any("AcquirerIdentifier", in.GetAcquirerIdentifier()),
	)
}

func file_inner_processing_grpc_jusan_proto_message_GooglePayRequestDataToZap(
	label string,
	in *GooglePayRequestData,
) zap.Field {
	if in == nil {
		return zap.Reflect(label, in)
	}

	return zap.Dict(
		label,
		zap.Any("TransactionId", in.GetTransactionId()),
		zap.Any("Amount", in.GetAmount()),
		zap.Any("Description", in.GetDescription()),
		file_inner_processing_grpc_jusan_proto_message_CardDataForBankToZap("Card", in.GetCard()),
		file_inner_processing_grpc_jusan_proto_message_UserDataForBankToZap("User", in.GetUser()),
		file_inner_processing_grpc_jusan_proto_message_TerminalDataForBankToZap("Terminal", in.GetTerminal()),
		file_inner_processing_grpc_jusan_proto_message_CardInfoToZap("CardInfo", in.GetCardInfo()),
		file_inner_processing_grpc_jusan_proto_message_MerchantInfoToZap("MerchantInfo", in.GetMerchantInfo()),
		zap.Any("AggregatedTypeId", in.GetAggregatedTypeId()),
		zap.Any("Token", in.GetToken()),
		zap.Any("Currency", in.GetCurrency()),
		zap.Any("EciIndicator", in.GetEciIndicator()),
		zap.Any("Cryptogram", in.GetCryptogram()),
		file_inner_processing_grpc_jusan_proto_message_TimestampToZap("TransactionCreatedAt", in.GetTransactionCreatedAt()),
		zap.Any("UserIpAddress", in.GetUserIpAddress()),
	)
}

func file_inner_processing_grpc_jusan_proto_message_GooglePayResponseDataToZap(
	label string,
	in *GooglePayResponseData,
) zap.Field {
	if in == nil {
		return zap.Reflect(label, in)
	}

	return zap.Dict(
		label,
		zap.Any("TransactionId", in.GetTransactionId()),
		zap.Any("BankReferenceId", in.GetBankReferenceId()),
		file_inner_processing_grpc_jusan_proto_enum_EnumTransactionStatusToZap("TransactionStatus", in.GetTransactionStatus()),
		file_inner_processing_grpc_jusan_proto_message_ThreeDSDataFromBankToZap("ThreeDS", in.GetThreeDS()),
		file_inner_processing_grpc_jusan_proto_message_BankResponseToZap("BankResponse", in.GetBankResponse()),
		zap.Any("BankOrderId", in.GetBankOrderId()),
		file_inner_processing_grpc_jusan_proto_message_FingerprintToZap("Fingerprint", in.GetFingerprint()),
	)
}

func file_inner_processing_grpc_jusan_proto_message_MerchantInfoToZap(
	label string,
	in *MerchantInfo,
) zap.Field {
	if in == nil {
		return zap.Reflect(label, in)
	}

	return zap.Dict(
		label,
		zap.Any("ProjectId", in.GetProjectId()),
		zap.Any("MerchantId", in.GetMerchantId()),
	)
}

func file_inner_processing_grpc_jusan_proto_message_OneClickPayInRequestDataToZap(
	label string,
	in *OneClickPayInRequestData,
) zap.Field {
	if in == nil {
		return zap.Reflect(label, in)
	}

	return zap.Dict(
		label,
		zap.Any("TransactionId", in.GetTransactionId()),
		zap.Any("CardId", in.GetCardId()),
		zap.Any("Amount", in.GetAmount()),
		zap.Any("Description", in.GetDescription()),
		file_inner_processing_grpc_jusan_proto_message_UserDataForBankToZap("User", in.GetUser()),
		file_inner_processing_grpc_jusan_proto_message_TerminalDataForBankToZap("Terminal", in.GetTerminal()),
		zap.Any("AggregatedTypeId", in.GetAggregatedTypeId()),
		file_inner_processing_grpc_jusan_proto_message_CardInfoToZap("CardInfo", in.GetCardInfo()),
		file_inner_processing_grpc_jusan_proto_message_MerchantInfoToZap("MerchantInfo", in.GetMerchantInfo()),
		file_inner_processing_grpc_jusan_proto_message_TimestampToZap("TransactionCreatedAt", in.GetTransactionCreatedAt()),
		zap.Any("UserIpAddress", in.GetUserIpAddress()),
	)
}

func file_inner_processing_grpc_jusan_proto_message_PayInRequestDataToZap(
	label string,
	in *PayInRequestData,
) zap.Field {
	if in == nil {
		return zap.Reflect(label, in)
	}

	return zap.Dict(
		label,
		zap.Any("TransactionId", in.GetTransactionId()),
		zap.Any("IsHold", in.GetIsHold()),
		zap.Any("Amount", in.GetAmount()),
		zap.Any("Description", in.GetDescription()),
		file_inner_processing_grpc_jusan_proto_message_CardDataForBankToZap("Card", in.GetCard()),
		file_inner_processing_grpc_jusan_proto_message_UserDataForBankToZap("User", in.GetUser()),
		file_inner_processing_grpc_jusan_proto_message_TerminalDataForBankToZap("Terminal", in.GetTerminal()),
		zap.Any("AggregatedTypeId", in.GetAggregatedTypeId()),
		file_inner_processing_grpc_jusan_proto_message_CardInfoToZap("CardInfo", in.GetCardInfo()),
		file_inner_processing_grpc_jusan_proto_message_MerchantInfoToZap("MerchantInfo", in.GetMerchantInfo()),
		file_inner_processing_grpc_jusan_proto_message_TimestampToZap("TransactionCreatedAt", in.GetTransactionCreatedAt()),
		zap.Any("GetForm", in.GetGetForm()),
		zap.Any("UserIpAddress", in.GetUserIpAddress()),
	)
}

func file_inner_processing_grpc_jusan_proto_message_PayInResponseDataToZap(
	label string,
	in *PayInResponseData,
) zap.Field {
	if in == nil {
		return zap.Reflect(label, in)
	}

	return zap.Dict(
		label,
		zap.Any("TransactionId", in.GetTransactionId()),
		zap.Any("BankReferenceId", in.GetBankReferenceId()),
		file_inner_processing_grpc_jusan_proto_enum_EnumTransactionStatusToZap("TransactionStatus", in.GetTransactionStatus()),
		zap.Any("CardId", in.GetCardId()),
		file_inner_processing_grpc_jusan_proto_message_ThreeDSDataFromBankToZap("ThreeDS", in.GetThreeDS()),
		file_inner_processing_grpc_jusan_proto_message_BankResponseToZap("BankResponse", in.GetBankResponse()),
		zap.Any("BankOrderId", in.GetBankOrderId()),
		file_inner_processing_grpc_jusan_proto_message_FingerprintToZap("Fingerprint", in.GetFingerprint()),
	)
}

func file_inner_processing_grpc_jusan_proto_message_PayOutByPhoneRequestDataToZap(
	label string,
	in *PayOutByPhoneRequestData,
) zap.Field {
	if in == nil {
		return zap.Reflect(label, in)
	}

	return zap.Dict(
		label,
		zap.Any("TransactionId", in.GetTransactionId()),
		zap.Any("VisaAliasToken", in.GetVisaAliasToken()),
		zap.Any("Amount", in.GetAmount()),
		file_inner_processing_grpc_jusan_proto_message_UserDataForBankToZap("User", in.GetUser()),
		file_inner_processing_grpc_jusan_proto_message_TerminalDataForBankToZap("Terminal", in.GetTerminal()),
		file_inner_processing_grpc_jusan_proto_message_PayOutCardToZap("Card", in.GetCard()),
		zap.Any("AggregatedTypeId", in.GetAggregatedTypeId()),
		file_inner_processing_grpc_jusan_proto_message_CardInfoToZap("CardInfo", in.GetCardInfo()),
		file_inner_processing_grpc_jusan_proto_message_MerchantInfoToZap("MerchantInfo", in.GetMerchantInfo()),
		file_inner_processing_grpc_jusan_proto_message_TimestampToZap("TransactionCreatedAt", in.GetTransactionCreatedAt()),
		zap.Any("CInfo", in.GetCInfo()),
	)
}

func file_inner_processing_grpc_jusan_proto_message_PayOutCardToZap(
	label string,
	in *PayOutCard,
) zap.Field {
	if in == nil {
		return zap.Reflect(label, in)
	}

	return zap.Dict(
		label,
		zap.Any("Pan", "[***hidden***]"),
		zap.Any("ExpMonth", in.GetExpMonth()),
		zap.Any("ExpYear", in.GetExpYear()),
		zap.Any("Cvc", "[***hidden***]"),
	)
}

func file_inner_processing_grpc_jusan_proto_message_PayOutRequestDataToZap(
	label string,
	in *PayOutRequestData,
) zap.Field {
	if in == nil {
		return zap.Reflect(label, in)
	}

	return zap.Dict(
		label,
		zap.Any("TransactionId", in.GetTransactionId()),
		zap.Any("Pan", "[***hidden***]"),
		zap.Any("Amount", in.GetAmount()),
		file_inner_processing_grpc_jusan_proto_message_UserDataForBankToZap("User", in.GetUser()),
		file_inner_processing_grpc_jusan_proto_message_TerminalDataForBankToZap("Terminal", in.GetTerminal()),
		file_inner_processing_grpc_jusan_proto_message_PayOutCardToZap("Card", in.GetCard()),
		zap.Any("AggregatedTypeId", in.GetAggregatedTypeId()),
		file_inner_processing_grpc_jusan_proto_message_CardInfoToZap("CardInfo", in.GetCardInfo()),
		file_inner_processing_grpc_jusan_proto_message_MerchantInfoToZap("MerchantInfo", in.GetMerchantInfo()),
		file_inner_processing_grpc_jusan_proto_message_TimestampToZap("TransactionCreatedAt", in.GetTransactionCreatedAt()),
		zap.Any("CInfo", in.GetCInfo()),
	)
}

func file_inner_processing_grpc_jusan_proto_message_PayOutResponseByPhoneDataToZap(
	label string,
	in *PayOutResponseByPhoneData,
) zap.Field {
	if in == nil {
		return zap.Reflect(label, in)
	}

	return zap.Dict(
		label,
		zap.Any("TransactionId", in.GetTransactionId()),
		zap.Any("BankReferenceId", in.GetBankReferenceId()),
		zap.Any("BankOrderId", in.GetBankOrderId()),
		file_inner_processing_grpc_jusan_proto_enum_EnumTransactionStatusToZap("TransactionStatus", in.GetTransactionStatus()),
		file_inner_processing_grpc_jusan_proto_message_BankResponseToZap("BankResponse", in.GetBankResponse()),
	)
}

func file_inner_processing_grpc_jusan_proto_message_PayOutResponseDataToZap(
	label string,
	in *PayOutResponseData,
) zap.Field {
	if in == nil {
		return zap.Reflect(label, in)
	}

	return zap.Dict(
		label,
		zap.Any("TransactionId", in.GetTransactionId()),
		zap.Any("BankReferenceId", in.GetBankReferenceId()),
		zap.Any("BankOrderId", in.GetBankOrderId()),
		file_inner_processing_grpc_jusan_proto_enum_EnumTransactionStatusToZap("TransactionStatus", in.GetTransactionStatus()),
		file_inner_processing_grpc_jusan_proto_message_BankResponseToZap("BankResponse", in.GetBankResponse()),
	)
}

func file_inner_processing_grpc_jusan_proto_message_RefundRequestToZap(
	label string,
	in *RefundRequest,
) zap.Field {
	if in == nil {
		return zap.Reflect(label, in)
	}

	return zap.Dict(
		label,
		zap.Any("TransactionId", in.GetTransactionId()),
		zap.Any("RefundAmount", in.GetRefundAmount()),
		file_inner_processing_grpc_jusan_proto_message_TerminalDataForBankToZap("Terminal", in.GetTerminal()),
		zap.Any("BankReferenceId", in.GetBankReferenceId()),
		zap.Any("BankOrderId", in.GetBankOrderId()),
		file_inner_processing_grpc_jusan_proto_message_TimestampToZap("TransactionCreatedAt", in.GetTransactionCreatedAt()),
		zap.Any("TransactionAmount", in.GetTransactionAmount()),
	)
}

func file_inner_processing_grpc_jusan_proto_message_RefundResponseToZap(
	label string,
	in *RefundResponse,
) zap.Field {
	if in == nil {
		return zap.Reflect(label, in)
	}

	return zap.Dict(
		label,
		zap.Any("TransactionId", in.GetTransactionId()),
		file_inner_processing_grpc_jusan_proto_message_BankResponseToZap("BankResponse", in.GetBankResponse()),
		zap.Any("BankReferenceId", in.GetBankReferenceId()),
	)
}

func file_inner_processing_grpc_jusan_proto_message_ResolveVisaAliasRequestToZap(
	label string,
	in *ResolveVisaAliasRequest,
) zap.Field {
	if in == nil {
		return zap.Reflect(label, in)
	}

	return zap.Dict(
		label,
		zap.Any("PhoneNumber", in.GetPhoneNumber()),
		file_inner_processing_grpc_jusan_proto_message_TerminalDataForBankToZap("Terminal", in.GetTerminal()),
	)
}

func file_inner_processing_grpc_jusan_proto_message_ResolveVisaAliasResponseToZap(
	label string,
	in *ResolveVisaAliasResponse,
) zap.Field {
	if in == nil {
		return zap.Reflect(label, in)
	}

	return zap.Dict(
		label,
		zap.Any("IssuerName", in.GetIssuerName()),
		zap.Any("RecipientName", in.GetRecipientName()),
		zap.Any("CardType", in.GetCardType()),
		file_inner_processing_grpc_jusan_proto_message_BankResponseToZap("BankResponse", in.GetBankResponse()),
		zap.Any("VisaAliasToken", in.GetVisaAliasToken()),
	)
}

func file_inner_processing_grpc_jusan_proto_message_TerminalDataForBankToZap(
	label string,
	in *TerminalDataForBank,
) zap.Field {
	if in == nil {
		return zap.Reflect(label, in)
	}

	return zap.Dict(
		label,
		zap.Any("Id", in.GetId()),
		zap.Any("AcquirerId", in.GetAcquirerId()),
		zap.Any("AcquirerCode", in.GetAcquirerCode()),
		zap.Any("Config", "[***hidden***]"),
	)
}

func file_inner_processing_grpc_jusan_proto_message_ThreeDSDataFromBankToZap(
	label string,
	in *ThreeDSDataFromBank,
) zap.Field {
	if in == nil {
		return zap.Reflect(label, in)
	}

	return zap.Dict(
		label,
		zap.Any("Action", in.GetAction()),
		file_inner_processing_grpc_jusan_proto_message_StructToZap("Params", in.GetParams()),
		zap.Any("Template", in.GetTemplate()),
	)
}

func file_inner_processing_grpc_jusan_proto_message_ThreeDSRequestDataToZap(
	label string,
	in *ThreeDSRequestData,
) zap.Field {
	if in == nil {
		return zap.Reflect(label, in)
	}

	return zap.Dict(
		label,
		zap.Any("TransactionId", in.GetTransactionId()),
		zap.Any("BankReferenceId", in.GetBankReferenceId()),
		zap.Any("Pares", in.GetPares()),
		zap.Any("Md", in.GetMd()),
		zap.Any("Amount", in.GetAmount()),
		zap.Any("Cres", in.GetCres()),
		file_inner_processing_grpc_jusan_proto_message_TerminalDataForBankToZap("Terminal", in.GetTerminal()),
		zap.Any("BankOrderId", in.GetBankOrderId()),
		zap.Any("IsHold", in.GetIsHold()),
	)
}

func file_inner_processing_grpc_jusan_proto_message_ThreeDSResponseDataToZap(
	label string,
	in *ThreeDSResponseData,
) zap.Field {
	if in == nil {
		return zap.Reflect(label, in)
	}

	return zap.Dict(
		label,
		zap.Any("TransactionId", in.GetTransactionId()),
		zap.Any("BankReferenceId", in.GetBankReferenceId()),
		zap.Any("BankOrderId", in.GetBankOrderId()),
		file_inner_processing_grpc_jusan_proto_enum_EnumTransactionStatusToZap("TransactionStatus", in.GetTransactionStatus()),
		zap.Any("CardToken", in.GetCardToken()),
		file_inner_processing_grpc_jusan_proto_message_BankResponseToZap("BankResponse", in.GetBankResponse()),
	)
}

func file_inner_processing_grpc_jusan_proto_message_ThreeDSResumeRequestToZap(
	label string,
	in *ThreeDSResumeRequest,
) zap.Field {
	if in == nil {
		return zap.Reflect(label, in)
	}

	return zap.Dict(
		label,
		zap.Any("TransactionId", in.GetTransactionId()),
		zap.Any("BankReferenceId", in.GetBankReferenceId()),
		file_inner_processing_grpc_jusan_proto_message_TerminalDataForBankToZap("Terminal", in.GetTerminal()),
		zap.Any("BankOrderId", in.GetBankOrderId()),
		zap.Any("IsHold", in.GetIsHold()),
	)
}

func file_inner_processing_grpc_jusan_proto_message_ThreeDSResumeResponseToZap(
	label string,
	in *ThreeDSResumeResponse,
) zap.Field {
	if in == nil {
		return zap.Reflect(label, in)
	}

	return zap.Dict(
		label,
		zap.Any("TransactionId", in.GetTransactionId()),
		zap.Any("BankOrderId", in.GetBankOrderId()),
		zap.Any("BankReferenceId", in.GetBankReferenceId()),
		file_inner_processing_grpc_jusan_proto_enum_EnumTransactionStatusToZap("TransactionStatus", in.GetTransactionStatus()),
		zap.Any("CardToken", in.GetCardToken()),
		file_inner_processing_grpc_jusan_proto_message_ThreeDSDataFromBankToZap("ThreeDS", in.GetThreeDS()),
		file_inner_processing_grpc_jusan_proto_message_BankResponseToZap("BankResponse", in.GetBankResponse()),
	)
}

func file_inner_processing_grpc_jusan_proto_message_TwoStagePayInRequestToZap(
	label string,
	in *TwoStagePayInRequest,
) zap.Field {
	if in == nil {
		return zap.Reflect(label, in)
	}

	return zap.Dict(
		label,
		zap.Any("TransactionId", in.GetTransactionId()),
		zap.Any("IsHold", in.GetIsHold()),
		zap.Any("Amount", in.GetAmount()),
		zap.Any("Description", in.GetDescription()),
		file_inner_processing_grpc_jusan_proto_message_CardDataForBankToZap("Card", in.GetCard()),
		file_inner_processing_grpc_jusan_proto_message_UserDataForBankToZap("User", in.GetUser()),
		file_inner_processing_grpc_jusan_proto_message_TerminalDataForBankToZap("Terminal", in.GetTerminal()),
		zap.Any("TransactionTypeId", in.GetTransactionTypeId()),
		file_inner_processing_grpc_jusan_proto_message_CardInfoToZap("CardInfo", in.GetCardInfo()),
		file_inner_processing_grpc_jusan_proto_message_MerchantInfoToZap("MerchantInfo", in.GetMerchantInfo()),
		zap.Any("AggregatedTypeId", in.GetAggregatedTypeId()),
	)
}

func file_inner_processing_grpc_jusan_proto_message_TwoStagePayInResponseToZap(
	label string,
	in *TwoStagePayInResponse,
) zap.Field {
	if in == nil {
		return zap.Reflect(label, in)
	}

	return zap.Dict(
		label,
		zap.Any("TransactionId", in.GetTransactionId()),
		zap.Any("BankReferenceId", in.GetBankReferenceId()),
		file_inner_processing_grpc_jusan_proto_enum_EnumTransactionStatusToZap("TransactionStatus", in.GetTransactionStatus()),
		zap.Any("CardId", in.GetCardId()),
		file_inner_processing_grpc_jusan_proto_message_ThreeDSDataFromBankToZap("ThreeDS", in.GetThreeDS()),
		file_inner_processing_grpc_jusan_proto_message_BankResponseToZap("BankResponse", in.GetBankResponse()),
		zap.Any("BankOrderId", in.GetBankOrderId()),
	)
}

func file_inner_processing_grpc_jusan_proto_message_UserDataForBankToZap(
	label string,
	in *UserDataForBank,
) zap.Field {
	if in == nil {
		return zap.Reflect(label, in)
	}

	return zap.Dict(
		label,
		zap.Any("Id", in.GetId()),
		zap.Any("Email", in.GetEmail()),
		zap.Any("Name", in.GetName()),
		zap.Any("Phone", in.GetPhone()),
	)
}

func file_inner_processing_grpc_jusan_proto_message_CheckBalanceRequestToZap(
	label string,
	in *CheckBalanceRequest,
) zap.Field {
	if in == nil {
		return zap.Reflect(label, in)
	}

	return zap.Dict(
		label,
		zap.Any("TerminalId", in.GetTerminalId()),
		zap.Any("AcquirerCode", in.GetAcquirerCode()),
		file_inner_processing_grpc_jusan_proto_message_StructToZap("Config", in.GetConfig()),
	)
}

func file_inner_processing_grpc_jusan_proto_message_CheckBalanceResponseToZap(
	label string,
	in *CheckBalanceResponse,
) zap.Field {
	if in == nil {
		return zap.Reflect(label, in)
	}

	return zap.Dict(
		label,
		zap.Any("Amount", in.GetAmount()),
		zap.Any("Message", in.GetMessage()),
		zap.Any("TerminalId", in.GetTerminalId()),
		zap.Any("AcquirerCode", in.GetAcquirerCode()),
	)
}

func file_inner_processing_grpc_jusan_proto_message_EmissionResponseToZap(
	label string,
	in *EmissionResponse,
) zap.Field {
	if in == nil {
		return zap.Reflect(label, in)
	}

	return zap.Dict(
		label,
		zap.Any("Code", in.GetCode()),
		zap.Any("Message", in.GetMessage()),
		file_inner_processing_grpc_jusan_proto_message_Value_1SliceToZap("Value", in.GetValue()),
	)
}

func file_inner_processing_grpc_jusan_proto_message_EmoneyRequestToZap(
	label string,
	in *EmoneyRequest,
) zap.Field {
	if in == nil {
		return zap.Reflect(label, in)
	}

	return zap.Dict(
		label,
		zap.Any("Id", in.GetId()),
	)
}

func file_inner_processing_grpc_jusan_proto_message_EmoneyResponseToZap(
	label string,
	in *EmoneyResponse,
) zap.Field {
	if in == nil {
		return zap.Reflect(label, in)
	}

	return zap.Dict(
		label,
		zap.Any("Code", in.GetCode()),
		zap.Any("Message", in.GetMessage()),
	)
}

func file_inner_processing_grpc_jusan_proto_message_Value_1ToZap(
	label string,
	in *Value,
) zap.Field {
	if in == nil {
		return zap.Reflect(label, in)
	}

	return zap.Dict(
		label,
		zap.Any("Id", in.GetId()),
		zap.Any("Dscr", in.GetDscr()),
		zap.Any("Clibin", in.GetClibin()),
		zap.Any("Iban", in.GetIban()),
		zap.Any("AgentName", in.GetAgentName()),
		zap.Any("Agent", in.GetAgent()),
		zap.Any("Amount", in.GetAmount()),
	)
}

func file_inner_processing_grpc_jusan_proto_message_Value_1SliceToZap(
	label string,
	in []*Value,
) zap.Field {
	var res []zap.Field
	for index, val := range in {
		res = append(res, file_inner_processing_grpc_jusan_proto_message_Value_1ToZap(fmt.Sprint(index), val))
	}

	return zap.Dict(label, res...)
}

func file_inner_processing_grpc_jusan_proto_enum_EnumTransactionStatusToZap(
	label string,
	in EnumTransactionStatus,
) zap.Field {
	str, ok := EnumTransactionStatus_name[int32(in)]
	if !ok {
		str = fmt.Sprintf("unknown EnumTransactionStatus value %d", in)
	}

	return zap.Dict(
		label,
		zap.Any("value", EnumTransactionStatus(in)),
		zap.Any("name", str),
	)
}

var _ JusanServer = (*loggedJusanServer)(nil)

func NewLoggedJusanServer(srv JusanServer) JusanServer {
	return &loggedJusanServer{srv: srv}
}

type loggedJusanServer struct {
	UnimplementedJusanServer

	srv JusanServer
}

func (s *loggedJusanServer) PayIn(
	ctx context.Context,
	request *PayInRequestData,
) (
	response *PayInResponseData,
	err error,
) {
	label := cntx.Begin(ctx, "JusanServer_PayIn")
	defer cntx.End(ctx, label)

	defer func() {
		if err != nil {
			ctxzap.AddFields(ctx, zap.Any(label+"error", err))
			return
		}

		ctxzap.AddFields(
			ctx,
			file_inner_processing_grpc_jusan_proto_message_PayInResponseDataToZap(label+"response", response),
		)
	}()

	ctxzap.AddFields(
		ctx,
		file_inner_processing_grpc_jusan_proto_message_PayInRequestDataToZap(label+"request", request),
	)

	response, err = s.srv.PayIn(ctx, request)

	return
}

func (s *loggedJusanServer) OneClickPayIn(
	ctx context.Context,
	request *OneClickPayInRequestData,
) (
	response *PayInResponseData,
	err error,
) {
	label := cntx.Begin(ctx, "JusanServer_OneClickPayIn")
	defer cntx.End(ctx, label)

	defer func() {
		if err != nil {
			ctxzap.AddFields(ctx, zap.Any(label+"error", err))
			return
		}

		ctxzap.AddFields(
			ctx,
			file_inner_processing_grpc_jusan_proto_message_PayInResponseDataToZap(label+"response", response),
		)
	}()

	ctxzap.AddFields(
		ctx,
		file_inner_processing_grpc_jusan_proto_message_OneClickPayInRequestDataToZap(label+"request", request),
	)

	response, err = s.srv.OneClickPayIn(ctx, request)

	return
}

func (s *loggedJusanServer) ThreeDSConfirm(
	ctx context.Context,
	request *ThreeDSRequestData,
) (
	response *ThreeDSResponseData,
	err error,
) {
	label := cntx.Begin(ctx, "JusanServer_ThreeDSConfirm")
	defer cntx.End(ctx, label)

	defer func() {
		if err != nil {
			ctxzap.AddFields(ctx, zap.Any(label+"error", err))
			return
		}

		ctxzap.AddFields(
			ctx,
			file_inner_processing_grpc_jusan_proto_message_ThreeDSResponseDataToZap(label+"response", response),
		)
	}()

	ctxzap.AddFields(
		ctx,
		file_inner_processing_grpc_jusan_proto_message_ThreeDSRequestDataToZap(label+"request", request),
	)

	response, err = s.srv.ThreeDSConfirm(ctx, request)

	return
}

func (s *loggedJusanServer) ThreeDSResume(
	ctx context.Context,
	request *ThreeDSResumeRequest,
) (
	response *ThreeDSResumeResponse,
	err error,
) {
	label := cntx.Begin(ctx, "JusanServer_ThreeDSResume")
	defer cntx.End(ctx, label)

	defer func() {
		if err != nil {
			ctxzap.AddFields(ctx, zap.Any(label+"error", err))
			return
		}

		ctxzap.AddFields(
			ctx,
			file_inner_processing_grpc_jusan_proto_message_ThreeDSResumeResponseToZap(label+"response", response),
		)
	}()

	ctxzap.AddFields(
		ctx,
		file_inner_processing_grpc_jusan_proto_message_ThreeDSResumeRequestToZap(label+"request", request),
	)

	response, err = s.srv.ThreeDSResume(ctx, request)

	return
}

func (s *loggedJusanServer) PayOut(
	ctx context.Context,
	request *PayOutRequestData,
) (
	response *PayOutResponseData,
	err error,
) {
	label := cntx.Begin(ctx, "JusanServer_PayOut")
	defer cntx.End(ctx, label)

	defer func() {
		if err != nil {
			ctxzap.AddFields(ctx, zap.Any(label+"error", err))
			return
		}

		ctxzap.AddFields(
			ctx,
			file_inner_processing_grpc_jusan_proto_message_PayOutResponseDataToZap(label+"response", response),
		)
	}()

	ctxzap.AddFields(
		ctx,
		file_inner_processing_grpc_jusan_proto_message_PayOutRequestDataToZap(label+"request", request),
	)

	response, err = s.srv.PayOut(ctx, request)

	return
}

func (s *loggedJusanServer) GetBankTransactionStatus(
	ctx context.Context,
	request *BankTransactionStatusRequest,
) (
	response *BankTransactionStatusResponse,
	err error,
) {
	label := cntx.Begin(ctx, "JusanServer_GetBankTransactionStatus")
	defer cntx.End(ctx, label)

	defer func() {
		if err != nil {
			ctxzap.AddFields(ctx, zap.Any(label+"error", err))
			return
		}

		ctxzap.AddFields(
			ctx,
			file_inner_processing_grpc_jusan_proto_message_BankTransactionStatusResponseToZap(label+"response", response),
		)
	}()

	ctxzap.AddFields(
		ctx,
		file_inner_processing_grpc_jusan_proto_message_BankTransactionStatusRequestToZap(label+"request", request),
	)

	response, err = s.srv.GetBankTransactionStatus(ctx, request)

	return
}

func (s *loggedJusanServer) GetBankTransactionStatusUnformated(
	ctx context.Context,
	request *BankTransactionStatusUnformatedRequest,
) (
	response *BankTransactionStatusUnformatedResponse,
	err error,
) {
	label := cntx.Begin(ctx, "JusanServer_GetBankTransactionStatusUnformated")
	defer cntx.End(ctx, label)

	defer func() {
		if err != nil {
			ctxzap.AddFields(ctx, zap.Any(label+"error", err))
			return
		}

		ctxzap.AddFields(
			ctx,
			file_inner_processing_grpc_jusan_proto_message_BankTransactionStatusUnformatedResponseToZap(label+"response", response),
		)
	}()

	ctxzap.AddFields(
		ctx,
		file_inner_processing_grpc_jusan_proto_message_BankTransactionStatusUnformatedRequestToZap(label+"request", request),
	)

	response, err = s.srv.GetBankTransactionStatusUnformated(ctx, request)

	return
}

func (s *loggedJusanServer) Refund(
	ctx context.Context,
	request *RefundRequest,
) (
	response *RefundResponse,
	err error,
) {
	label := cntx.Begin(ctx, "JusanServer_Refund")
	defer cntx.End(ctx, label)

	defer func() {
		if err != nil {
			ctxzap.AddFields(ctx, zap.Any(label+"error", err))
			return
		}

		ctxzap.AddFields(
			ctx,
			file_inner_processing_grpc_jusan_proto_message_RefundResponseToZap(label+"response", response),
		)
	}()

	ctxzap.AddFields(
		ctx,
		file_inner_processing_grpc_jusan_proto_message_RefundRequestToZap(label+"request", request),
	)

	response, err = s.srv.Refund(ctx, request)

	return
}

func (s *loggedJusanServer) GooglePay(
	ctx context.Context,
	request *GooglePayRequestData,
) (
	response *GooglePayResponseData,
	err error,
) {
	label := cntx.Begin(ctx, "JusanServer_GooglePay")
	defer cntx.End(ctx, label)

	defer func() {
		if err != nil {
			ctxzap.AddFields(ctx, zap.Any(label+"error", err))
			return
		}

		ctxzap.AddFields(
			ctx,
			file_inner_processing_grpc_jusan_proto_message_GooglePayResponseDataToZap(label+"response", response),
		)
	}()

	ctxzap.AddFields(
		ctx,
		file_inner_processing_grpc_jusan_proto_message_GooglePayRequestDataToZap(label+"request", request),
	)

	response, err = s.srv.GooglePay(ctx, request)

	return
}

func (s *loggedJusanServer) ApplePay(
	ctx context.Context,
	request *ApplePayRequestData,
) (
	response *ApplePayResponseData,
	err error,
) {
	label := cntx.Begin(ctx, "JusanServer_ApplePay")
	defer cntx.End(ctx, label)

	defer func() {
		if err != nil {
			ctxzap.AddFields(ctx, zap.Any(label+"error", err))
			return
		}

		ctxzap.AddFields(
			ctx,
			file_inner_processing_grpc_jusan_proto_message_ApplePayResponseDataToZap(label+"response", response),
		)
	}()

	ctxzap.AddFields(
		ctx,
		file_inner_processing_grpc_jusan_proto_message_ApplePayRequestDataToZap(label+"request", request),
	)

	response, err = s.srv.ApplePay(ctx, request)

	return
}

func (s *loggedJusanServer) TwoStagePayIn(
	ctx context.Context,
	request *TwoStagePayInRequest,
) (
	response *TwoStagePayInResponse,
	err error,
) {
	label := cntx.Begin(ctx, "JusanServer_TwoStagePayIn")
	defer cntx.End(ctx, label)

	defer func() {
		if err != nil {
			ctxzap.AddFields(ctx, zap.Any(label+"error", err))
			return
		}

		ctxzap.AddFields(
			ctx,
			file_inner_processing_grpc_jusan_proto_message_TwoStagePayInResponseToZap(label+"response", response),
		)
	}()

	ctxzap.AddFields(
		ctx,
		file_inner_processing_grpc_jusan_proto_message_TwoStagePayInRequestToZap(label+"request", request),
	)

	response, err = s.srv.TwoStagePayIn(ctx, request)

	return
}

func (s *loggedJusanServer) Charge(
	ctx context.Context,
	request *ChargeRequest,
) (
	response *ChargeResponse,
	err error,
) {
	label := cntx.Begin(ctx, "JusanServer_Charge")
	defer cntx.End(ctx, label)

	defer func() {
		if err != nil {
			ctxzap.AddFields(ctx, zap.Any(label+"error", err))
			return
		}

		ctxzap.AddFields(
			ctx,
			file_inner_processing_grpc_jusan_proto_message_ChargeResponseToZap(label+"response", response),
		)
	}()

	ctxzap.AddFields(
		ctx,
		file_inner_processing_grpc_jusan_proto_message_ChargeRequestToZap(label+"request", request),
	)

	response, err = s.srv.Charge(ctx, request)

	return
}

func (s *loggedJusanServer) Cancel(
	ctx context.Context,
	request *CancelRequest,
) (
	response *CancelResponse,
	err error,
) {
	label := cntx.Begin(ctx, "JusanServer_Cancel")
	defer cntx.End(ctx, label)

	defer func() {
		if err != nil {
			ctxzap.AddFields(ctx, zap.Any(label+"error", err))
			return
		}

		ctxzap.AddFields(
			ctx,
			file_inner_processing_grpc_jusan_proto_message_CancelResponseToZap(label+"response", response),
		)
	}()

	ctxzap.AddFields(
		ctx,
		file_inner_processing_grpc_jusan_proto_message_CancelRequestToZap(label+"request", request),
	)

	response, err = s.srv.Cancel(ctx, request)

	return
}

func (s *loggedJusanServer) MakeToken(
	ctx context.Context,
	request *PayInRequestData,
) (
	response *PayInResponseData,
	err error,
) {
	label := cntx.Begin(ctx, "JusanServer_MakeToken")
	defer cntx.End(ctx, label)

	defer func() {
		if err != nil {
			ctxzap.AddFields(ctx, zap.Any(label+"error", err))
			return
		}

		ctxzap.AddFields(
			ctx,
			file_inner_processing_grpc_jusan_proto_message_PayInResponseDataToZap(label+"response", response),
		)
	}()

	ctxzap.AddFields(
		ctx,
		file_inner_processing_grpc_jusan_proto_message_PayInRequestDataToZap(label+"request", request),
	)

	response, err = s.srv.MakeToken(ctx, request)

	return
}

func (s *loggedJusanServer) GetEmission(
	ctx context.Context,
	request *emptypb.Empty,
) (
	response *EmissionResponse,
	err error,
) {
	label := cntx.Begin(ctx, "JusanServer_GetEmission")
	defer cntx.End(ctx, label)

	defer func() {
		if err != nil {
			ctxzap.AddFields(ctx, zap.Any(label+"error", err))
			return
		}

		ctxzap.AddFields(
			ctx,
			file_inner_processing_grpc_jusan_proto_message_EmissionResponseToZap(label+"response", response),
		)
	}()

	ctxzap.AddFields(
		ctx,
		file_inner_processing_grpc_jusan_proto_message_EmptyToZap(label+"request", request),
	)

	response, err = s.srv.GetEmission(ctx, request)

	return
}

func (s *loggedJusanServer) ConfirmEmission(
	ctx context.Context,
	request *EmoneyRequest,
) (
	response *EmoneyResponse,
	err error,
) {
	label := cntx.Begin(ctx, "JusanServer_ConfirmEmission")
	defer cntx.End(ctx, label)

	defer func() {
		if err != nil {
			ctxzap.AddFields(ctx, zap.Any(label+"error", err))
			return
		}

		ctxzap.AddFields(
			ctx,
			file_inner_processing_grpc_jusan_proto_message_EmoneyResponseToZap(label+"response", response),
		)
	}()

	ctxzap.AddFields(
		ctx,
		file_inner_processing_grpc_jusan_proto_message_EmoneyRequestToZap(label+"request", request),
	)

	response, err = s.srv.ConfirmEmission(ctx, request)

	return
}

func (s *loggedJusanServer) GetAcquirerIdentifier(
	ctx context.Context,
	request *emptypb.Empty,
) (
	response *GetAcquirerIdentifierResponse,
	err error,
) {
	label := cntx.Begin(ctx, "JusanServer_GetAcquirerIdentifier")
	defer cntx.End(ctx, label)

	defer func() {
		if err != nil {
			ctxzap.AddFields(ctx, zap.Any(label+"error", err))
			return
		}

		ctxzap.AddFields(
			ctx,
			file_inner_processing_grpc_jusan_proto_message_GetAcquirerIdentifierResponseToZap(label+"response", response),
		)
	}()

	ctxzap.AddFields(
		ctx,
		file_inner_processing_grpc_jusan_proto_message_EmptyToZap(label+"request", request),
	)

	response, err = s.srv.GetAcquirerIdentifier(ctx, request)

	return
}

func (s *loggedJusanServer) CheckBalance(
	ctx context.Context,
	request *CheckBalanceRequest,
) (
	response *CheckBalanceResponse,
	err error,
) {
	label := cntx.Begin(ctx, "JusanServer_CheckBalance")
	defer cntx.End(ctx, label)

	defer func() {
		if err != nil {
			ctxzap.AddFields(ctx, zap.Any(label+"error", err))
			return
		}

		ctxzap.AddFields(
			ctx,
			file_inner_processing_grpc_jusan_proto_message_CheckBalanceResponseToZap(label+"response", response),
		)
	}()

	ctxzap.AddFields(
		ctx,
		file_inner_processing_grpc_jusan_proto_message_CheckBalanceRequestToZap(label+"request", request),
	)

	response, err = s.srv.CheckBalance(ctx, request)

	return
}

func (s *loggedJusanServer) ResolveVisaAlias(
	ctx context.Context,
	request *ResolveVisaAliasRequest,
) (
	response *ResolveVisaAliasResponse,
	err error,
) {
	label := cntx.Begin(ctx, "JusanServer_ResolveVisaAlias")
	defer cntx.End(ctx, label)

	defer func() {
		if err != nil {
			ctxzap.AddFields(ctx, zap.Any(label+"error", err))
			return
		}

		ctxzap.AddFields(
			ctx,
			file_inner_processing_grpc_jusan_proto_message_ResolveVisaAliasResponseToZap(label+"response", response),
		)
	}()

	ctxzap.AddFields(
		ctx,
		file_inner_processing_grpc_jusan_proto_message_ResolveVisaAliasRequestToZap(label+"request", request),
	)

	response, err = s.srv.ResolveVisaAlias(ctx, request)

	return
}

func (s *loggedJusanServer) PayOutByPhone(
	ctx context.Context,
	request *PayOutByPhoneRequestData,
) (
	response *PayOutResponseByPhoneData,
	err error,
) {
	label := cntx.Begin(ctx, "JusanServer_PayOutByPhone")
	defer cntx.End(ctx, label)

	defer func() {
		if err != nil {
			ctxzap.AddFields(ctx, zap.Any(label+"error", err))
			return
		}

		ctxzap.AddFields(
			ctx,
			file_inner_processing_grpc_jusan_proto_message_PayOutResponseByPhoneDataToZap(label+"response", response),
		)
	}()

	ctxzap.AddFields(
		ctx,
		file_inner_processing_grpc_jusan_proto_message_PayOutByPhoneRequestDataToZap(label+"request", request),
	)

	response, err = s.srv.PayOutByPhone(ctx, request)

	return
}

var _ JusanClient = (*loggedJusanClient)(nil)

func NewLoggedJusanClient(client JusanClient) JusanClient {
	return &loggedJusanClient{client: client}
}

type loggedJusanClient struct {
	client JusanClient
}

func (s *loggedJusanClient) PayIn(
	ctx context.Context,
	request *PayInRequestData,
	opts ...grpc.CallOption,
) (
	response *PayInResponseData,
	err error,
) {
	label := cntx.Begin(ctx, "JusanClient_PayIn")
	defer cntx.End(ctx, label)

	defer func() {
		if err != nil {
			ctxzap.AddFields(ctx, zap.Any(label+"error", err))
			return
		}

		ctxzap.AddFields(
			ctx,
			file_inner_processing_grpc_jusan_proto_message_PayInResponseDataToZap(label+"response", response),
		)
	}()

	ctxzap.AddFields(
		ctx,
		file_inner_processing_grpc_jusan_proto_message_PayInRequestDataToZap(label+"request", request),
	)

	response, err = s.client.PayIn(ctx, request, opts...)

	return
}

func (s *loggedJusanClient) OneClickPayIn(
	ctx context.Context,
	request *OneClickPayInRequestData,
	opts ...grpc.CallOption,
) (
	response *PayInResponseData,
	err error,
) {
	label := cntx.Begin(ctx, "JusanClient_OneClickPayIn")
	defer cntx.End(ctx, label)

	defer func() {
		if err != nil {
			ctxzap.AddFields(ctx, zap.Any(label+"error", err))
			return
		}

		ctxzap.AddFields(
			ctx,
			file_inner_processing_grpc_jusan_proto_message_PayInResponseDataToZap(label+"response", response),
		)
	}()

	ctxzap.AddFields(
		ctx,
		file_inner_processing_grpc_jusan_proto_message_OneClickPayInRequestDataToZap(label+"request", request),
	)

	response, err = s.client.OneClickPayIn(ctx, request, opts...)

	return
}

func (s *loggedJusanClient) ThreeDSConfirm(
	ctx context.Context,
	request *ThreeDSRequestData,
	opts ...grpc.CallOption,
) (
	response *ThreeDSResponseData,
	err error,
) {
	label := cntx.Begin(ctx, "JusanClient_ThreeDSConfirm")
	defer cntx.End(ctx, label)

	defer func() {
		if err != nil {
			ctxzap.AddFields(ctx, zap.Any(label+"error", err))
			return
		}

		ctxzap.AddFields(
			ctx,
			file_inner_processing_grpc_jusan_proto_message_ThreeDSResponseDataToZap(label+"response", response),
		)
	}()

	ctxzap.AddFields(
		ctx,
		file_inner_processing_grpc_jusan_proto_message_ThreeDSRequestDataToZap(label+"request", request),
	)

	response, err = s.client.ThreeDSConfirm(ctx, request, opts...)

	return
}

func (s *loggedJusanClient) ThreeDSResume(
	ctx context.Context,
	request *ThreeDSResumeRequest,
	opts ...grpc.CallOption,
) (
	response *ThreeDSResumeResponse,
	err error,
) {
	label := cntx.Begin(ctx, "JusanClient_ThreeDSResume")
	defer cntx.End(ctx, label)

	defer func() {
		if err != nil {
			ctxzap.AddFields(ctx, zap.Any(label+"error", err))
			return
		}

		ctxzap.AddFields(
			ctx,
			file_inner_processing_grpc_jusan_proto_message_ThreeDSResumeResponseToZap(label+"response", response),
		)
	}()

	ctxzap.AddFields(
		ctx,
		file_inner_processing_grpc_jusan_proto_message_ThreeDSResumeRequestToZap(label+"request", request),
	)

	response, err = s.client.ThreeDSResume(ctx, request, opts...)

	return
}

func (s *loggedJusanClient) PayOut(
	ctx context.Context,
	request *PayOutRequestData,
	opts ...grpc.CallOption,
) (
	response *PayOutResponseData,
	err error,
) {
	label := cntx.Begin(ctx, "JusanClient_PayOut")
	defer cntx.End(ctx, label)

	defer func() {
		if err != nil {
			ctxzap.AddFields(ctx, zap.Any(label+"error", err))
			return
		}

		ctxzap.AddFields(
			ctx,
			file_inner_processing_grpc_jusan_proto_message_PayOutResponseDataToZap(label+"response", response),
		)
	}()

	ctxzap.AddFields(
		ctx,
		file_inner_processing_grpc_jusan_proto_message_PayOutRequestDataToZap(label+"request", request),
	)

	response, err = s.client.PayOut(ctx, request, opts...)

	return
}

func (s *loggedJusanClient) GetBankTransactionStatus(
	ctx context.Context,
	request *BankTransactionStatusRequest,
	opts ...grpc.CallOption,
) (
	response *BankTransactionStatusResponse,
	err error,
) {
	label := cntx.Begin(ctx, "JusanClient_GetBankTransactionStatus")
	defer cntx.End(ctx, label)

	defer func() {
		if err != nil {
			ctxzap.AddFields(ctx, zap.Any(label+"error", err))
			return
		}

		ctxzap.AddFields(
			ctx,
			file_inner_processing_grpc_jusan_proto_message_BankTransactionStatusResponseToZap(label+"response", response),
		)
	}()

	ctxzap.AddFields(
		ctx,
		file_inner_processing_grpc_jusan_proto_message_BankTransactionStatusRequestToZap(label+"request", request),
	)

	response, err = s.client.GetBankTransactionStatus(ctx, request, opts...)

	return
}

func (s *loggedJusanClient) GetBankTransactionStatusUnformated(
	ctx context.Context,
	request *BankTransactionStatusUnformatedRequest,
	opts ...grpc.CallOption,
) (
	response *BankTransactionStatusUnformatedResponse,
	err error,
) {
	label := cntx.Begin(ctx, "JusanClient_GetBankTransactionStatusUnformated")
	defer cntx.End(ctx, label)

	defer func() {
		if err != nil {
			ctxzap.AddFields(ctx, zap.Any(label+"error", err))
			return
		}

		ctxzap.AddFields(
			ctx,
			file_inner_processing_grpc_jusan_proto_message_BankTransactionStatusUnformatedResponseToZap(label+"response", response),
		)
	}()

	ctxzap.AddFields(
		ctx,
		file_inner_processing_grpc_jusan_proto_message_BankTransactionStatusUnformatedRequestToZap(label+"request", request),
	)

	response, err = s.client.GetBankTransactionStatusUnformated(ctx, request, opts...)

	return
}

func (s *loggedJusanClient) Refund(
	ctx context.Context,
	request *RefundRequest,
	opts ...grpc.CallOption,
) (
	response *RefundResponse,
	err error,
) {
	label := cntx.Begin(ctx, "JusanClient_Refund")
	defer cntx.End(ctx, label)

	defer func() {
		if err != nil {
			ctxzap.AddFields(ctx, zap.Any(label+"error", err))
			return
		}

		ctxzap.AddFields(
			ctx,
			file_inner_processing_grpc_jusan_proto_message_RefundResponseToZap(label+"response", response),
		)
	}()

	ctxzap.AddFields(
		ctx,
		file_inner_processing_grpc_jusan_proto_message_RefundRequestToZap(label+"request", request),
	)

	response, err = s.client.Refund(ctx, request, opts...)

	return
}

func (s *loggedJusanClient) GooglePay(
	ctx context.Context,
	request *GooglePayRequestData,
	opts ...grpc.CallOption,
) (
	response *GooglePayResponseData,
	err error,
) {
	label := cntx.Begin(ctx, "JusanClient_GooglePay")
	defer cntx.End(ctx, label)

	defer func() {
		if err != nil {
			ctxzap.AddFields(ctx, zap.Any(label+"error", err))
			return
		}

		ctxzap.AddFields(
			ctx,
			file_inner_processing_grpc_jusan_proto_message_GooglePayResponseDataToZap(label+"response", response),
		)
	}()

	ctxzap.AddFields(
		ctx,
		file_inner_processing_grpc_jusan_proto_message_GooglePayRequestDataToZap(label+"request", request),
	)

	response, err = s.client.GooglePay(ctx, request, opts...)

	return
}

func (s *loggedJusanClient) ApplePay(
	ctx context.Context,
	request *ApplePayRequestData,
	opts ...grpc.CallOption,
) (
	response *ApplePayResponseData,
	err error,
) {
	label := cntx.Begin(ctx, "JusanClient_ApplePay")
	defer cntx.End(ctx, label)

	defer func() {
		if err != nil {
			ctxzap.AddFields(ctx, zap.Any(label+"error", err))
			return
		}

		ctxzap.AddFields(
			ctx,
			file_inner_processing_grpc_jusan_proto_message_ApplePayResponseDataToZap(label+"response", response),
		)
	}()

	ctxzap.AddFields(
		ctx,
		file_inner_processing_grpc_jusan_proto_message_ApplePayRequestDataToZap(label+"request", request),
	)

	response, err = s.client.ApplePay(ctx, request, opts...)

	return
}

func (s *loggedJusanClient) TwoStagePayIn(
	ctx context.Context,
	request *TwoStagePayInRequest,
	opts ...grpc.CallOption,
) (
	response *TwoStagePayInResponse,
	err error,
) {
	label := cntx.Begin(ctx, "JusanClient_TwoStagePayIn")
	defer cntx.End(ctx, label)

	defer func() {
		if err != nil {
			ctxzap.AddFields(ctx, zap.Any(label+"error", err))
			return
		}

		ctxzap.AddFields(
			ctx,
			file_inner_processing_grpc_jusan_proto_message_TwoStagePayInResponseToZap(label+"response", response),
		)
	}()

	ctxzap.AddFields(
		ctx,
		file_inner_processing_grpc_jusan_proto_message_TwoStagePayInRequestToZap(label+"request", request),
	)

	response, err = s.client.TwoStagePayIn(ctx, request, opts...)

	return
}

func (s *loggedJusanClient) Charge(
	ctx context.Context,
	request *ChargeRequest,
	opts ...grpc.CallOption,
) (
	response *ChargeResponse,
	err error,
) {
	label := cntx.Begin(ctx, "JusanClient_Charge")
	defer cntx.End(ctx, label)

	defer func() {
		if err != nil {
			ctxzap.AddFields(ctx, zap.Any(label+"error", err))
			return
		}

		ctxzap.AddFields(
			ctx,
			file_inner_processing_grpc_jusan_proto_message_ChargeResponseToZap(label+"response", response),
		)
	}()

	ctxzap.AddFields(
		ctx,
		file_inner_processing_grpc_jusan_proto_message_ChargeRequestToZap(label+"request", request),
	)

	response, err = s.client.Charge(ctx, request, opts...)

	return
}

func (s *loggedJusanClient) Cancel(
	ctx context.Context,
	request *CancelRequest,
	opts ...grpc.CallOption,
) (
	response *CancelResponse,
	err error,
) {
	label := cntx.Begin(ctx, "JusanClient_Cancel")
	defer cntx.End(ctx, label)

	defer func() {
		if err != nil {
			ctxzap.AddFields(ctx, zap.Any(label+"error", err))
			return
		}

		ctxzap.AddFields(
			ctx,
			file_inner_processing_grpc_jusan_proto_message_CancelResponseToZap(label+"response", response),
		)
	}()

	ctxzap.AddFields(
		ctx,
		file_inner_processing_grpc_jusan_proto_message_CancelRequestToZap(label+"request", request),
	)

	response, err = s.client.Cancel(ctx, request, opts...)

	return
}

func (s *loggedJusanClient) MakeToken(
	ctx context.Context,
	request *PayInRequestData,
	opts ...grpc.CallOption,
) (
	response *PayInResponseData,
	err error,
) {
	label := cntx.Begin(ctx, "JusanClient_MakeToken")
	defer cntx.End(ctx, label)

	defer func() {
		if err != nil {
			ctxzap.AddFields(ctx, zap.Any(label+"error", err))
			return
		}

		ctxzap.AddFields(
			ctx,
			file_inner_processing_grpc_jusan_proto_message_PayInResponseDataToZap(label+"response", response),
		)
	}()

	ctxzap.AddFields(
		ctx,
		file_inner_processing_grpc_jusan_proto_message_PayInRequestDataToZap(label+"request", request),
	)

	response, err = s.client.MakeToken(ctx, request, opts...)

	return
}

func (s *loggedJusanClient) GetEmission(
	ctx context.Context,
	request *emptypb.Empty,
	opts ...grpc.CallOption,
) (
	response *EmissionResponse,
	err error,
) {
	label := cntx.Begin(ctx, "JusanClient_GetEmission")
	defer cntx.End(ctx, label)

	defer func() {
		if err != nil {
			ctxzap.AddFields(ctx, zap.Any(label+"error", err))
			return
		}

		ctxzap.AddFields(
			ctx,
			file_inner_processing_grpc_jusan_proto_message_EmissionResponseToZap(label+"response", response),
		)
	}()

	ctxzap.AddFields(
		ctx,
		file_inner_processing_grpc_jusan_proto_message_EmptyToZap(label+"request", request),
	)

	response, err = s.client.GetEmission(ctx, request, opts...)

	return
}

func (s *loggedJusanClient) ConfirmEmission(
	ctx context.Context,
	request *EmoneyRequest,
	opts ...grpc.CallOption,
) (
	response *EmoneyResponse,
	err error,
) {
	label := cntx.Begin(ctx, "JusanClient_ConfirmEmission")
	defer cntx.End(ctx, label)

	defer func() {
		if err != nil {
			ctxzap.AddFields(ctx, zap.Any(label+"error", err))
			return
		}

		ctxzap.AddFields(
			ctx,
			file_inner_processing_grpc_jusan_proto_message_EmoneyResponseToZap(label+"response", response),
		)
	}()

	ctxzap.AddFields(
		ctx,
		file_inner_processing_grpc_jusan_proto_message_EmoneyRequestToZap(label+"request", request),
	)

	response, err = s.client.ConfirmEmission(ctx, request, opts...)

	return
}

func (s *loggedJusanClient) GetAcquirerIdentifier(
	ctx context.Context,
	request *emptypb.Empty,
	opts ...grpc.CallOption,
) (
	response *GetAcquirerIdentifierResponse,
	err error,
) {
	label := cntx.Begin(ctx, "JusanClient_GetAcquirerIdentifier")
	defer cntx.End(ctx, label)

	defer func() {
		if err != nil {
			ctxzap.AddFields(ctx, zap.Any(label+"error", err))
			return
		}

		ctxzap.AddFields(
			ctx,
			file_inner_processing_grpc_jusan_proto_message_GetAcquirerIdentifierResponseToZap(label+"response", response),
		)
	}()

	ctxzap.AddFields(
		ctx,
		file_inner_processing_grpc_jusan_proto_message_EmptyToZap(label+"request", request),
	)

	response, err = s.client.GetAcquirerIdentifier(ctx, request, opts...)

	return
}

func (s *loggedJusanClient) CheckBalance(
	ctx context.Context,
	request *CheckBalanceRequest,
	opts ...grpc.CallOption,
) (
	response *CheckBalanceResponse,
	err error,
) {
	label := cntx.Begin(ctx, "JusanClient_CheckBalance")
	defer cntx.End(ctx, label)

	defer func() {
		if err != nil {
			ctxzap.AddFields(ctx, zap.Any(label+"error", err))
			return
		}

		ctxzap.AddFields(
			ctx,
			file_inner_processing_grpc_jusan_proto_message_CheckBalanceResponseToZap(label+"response", response),
		)
	}()

	ctxzap.AddFields(
		ctx,
		file_inner_processing_grpc_jusan_proto_message_CheckBalanceRequestToZap(label+"request", request),
	)

	response, err = s.client.CheckBalance(ctx, request, opts...)

	return
}

func (s *loggedJusanClient) ResolveVisaAlias(
	ctx context.Context,
	request *ResolveVisaAliasRequest,
	opts ...grpc.CallOption,
) (
	response *ResolveVisaAliasResponse,
	err error,
) {
	label := cntx.Begin(ctx, "JusanClient_ResolveVisaAlias")
	defer cntx.End(ctx, label)

	defer func() {
		if err != nil {
			ctxzap.AddFields(ctx, zap.Any(label+"error", err))
			return
		}

		ctxzap.AddFields(
			ctx,
			file_inner_processing_grpc_jusan_proto_message_ResolveVisaAliasResponseToZap(label+"response", response),
		)
	}()

	ctxzap.AddFields(
		ctx,
		file_inner_processing_grpc_jusan_proto_message_ResolveVisaAliasRequestToZap(label+"request", request),
	)

	response, err = s.client.ResolveVisaAlias(ctx, request, opts...)

	return
}

func (s *loggedJusanClient) PayOutByPhone(
	ctx context.Context,
	request *PayOutByPhoneRequestData,
	opts ...grpc.CallOption,
) (
	response *PayOutResponseByPhoneData,
	err error,
) {
	label := cntx.Begin(ctx, "JusanClient_PayOutByPhone")
	defer cntx.End(ctx, label)

	defer func() {
		if err != nil {
			ctxzap.AddFields(ctx, zap.Any(label+"error", err))
			return
		}

		ctxzap.AddFields(
			ctx,
			file_inner_processing_grpc_jusan_proto_message_PayOutResponseByPhoneDataToZap(label+"response", response),
		)
	}()

	ctxzap.AddFields(
		ctx,
		file_inner_processing_grpc_jusan_proto_message_PayOutByPhoneRequestDataToZap(label+"request", request),
	)

	response, err = s.client.PayOutByPhone(ctx, request, opts...)

	return
}
