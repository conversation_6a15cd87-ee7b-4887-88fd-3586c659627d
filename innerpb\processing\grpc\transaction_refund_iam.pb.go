// Code generated by protoc-gen-go-mvp. DO NOT EDIT.
// versions:
// - protoc-gen-go-mvp v2.0.1
// created by mvp authors

package grpc

import (
	context "context"
	grpc "google.golang.org/grpc"
	metadata "google.golang.org/grpc/metadata"
)

func NewIamRefundServer(
	srv RefundServer,
) RefundServer {
	return &iamRefundServer{
		srv: srv,
	}
}

var _ RefundServer = (*iamRefundServer)(nil)

type iamRefundServer struct {
	UnimplementedRefundServer

	srv RefundServer
}

func (s *iamRefundServer) GetByTransactionIDV1(
	ctx context.Context,
	req *TransactionRequestDataV1,
) (
	*TransactionResponseDataV1,
	error,
) {
	return s.srv.GetByTransactionIDV1(ctx, req)
}

func NewIamRefundClient(
	client RefundClient,
) RefundClient {
	return &iamRefundClient{
		client: client,
	}
}

type iamRefundClient struct {
	client RefundClient
}

func (s *iamRefundClient) GetByTransactionIDV1(
	ctx context.Context,
	req *TransactionRequestDataV1,
	opts ...grpc.CallOption,
) (
	*TransactionResponseDataV1,
	error,
) {
	md, _ := metadata.FromIncomingContext(ctx)
	return s.client.GetByTransactionIDV1(metadata.NewOutgoingContext(ctx, md), req)
}
