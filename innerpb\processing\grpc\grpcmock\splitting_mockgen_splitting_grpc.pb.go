// Code generated by MockGen. DO NOT EDIT.
// Source: splitting_grpc.pb.go

// Package grpcmock is a generated GoMock package.
package grpcmock

import (
	context "context"
	reflect "reflect"

	grpc "git.local/sensitive/innerpb/processing/grpc"
	gomock "github.com/golang/mock/gomock"
	grpc0 "google.golang.org/grpc"
)

// MockSplittingClient is a mock of SplittingClient interface.
type MockSplittingClient struct {
	ctrl     *gomock.Controller
	recorder *MockSplittingClientMockRecorder
}

// MockSplittingClientMockRecorder is the mock recorder for MockSplittingClient.
type MockSplittingClientMockRecorder struct {
	mock *MockSplittingClient
}

// NewMockSplittingClient creates a new mock instance.
func NewMockSplittingClient(ctrl *gomock.Controller) *MockSplittingClient {
	mock := &MockSplittingClient{ctrl: ctrl}
	mock.recorder = &MockSplittingClientMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockSplittingClient) EXPECT() *MockSplittingClientMockRecorder {
	return m.recorder
}

// CalculatePaymentSplitTax mocks base method.
func (m *MockSplittingClient) CalculatePaymentSplitTax(ctx context.Context, in *grpc.CalculatePaymentSplitTaxRequest, opts ...grpc0.CallOption) (*grpc.CalculatePaymentSplitTaxResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "CalculatePaymentSplitTax", varargs...)
	ret0, _ := ret[0].(*grpc.CalculatePaymentSplitTaxResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CalculatePaymentSplitTax indicates an expected call of CalculatePaymentSplitTax.
func (mr *MockSplittingClientMockRecorder) CalculatePaymentSplitTax(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CalculatePaymentSplitTax", reflect.TypeOf((*MockSplittingClient)(nil).CalculatePaymentSplitTax), varargs...)
}

// MockSplittingServer is a mock of SplittingServer interface.
type MockSplittingServer struct {
	ctrl     *gomock.Controller
	recorder *MockSplittingServerMockRecorder
}

// MockSplittingServerMockRecorder is the mock recorder for MockSplittingServer.
type MockSplittingServerMockRecorder struct {
	mock *MockSplittingServer
}

// NewMockSplittingServer creates a new mock instance.
func NewMockSplittingServer(ctrl *gomock.Controller) *MockSplittingServer {
	mock := &MockSplittingServer{ctrl: ctrl}
	mock.recorder = &MockSplittingServerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockSplittingServer) EXPECT() *MockSplittingServerMockRecorder {
	return m.recorder
}

// CalculatePaymentSplitTax mocks base method.
func (m *MockSplittingServer) CalculatePaymentSplitTax(arg0 context.Context, arg1 *grpc.CalculatePaymentSplitTaxRequest) (*grpc.CalculatePaymentSplitTaxResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CalculatePaymentSplitTax", arg0, arg1)
	ret0, _ := ret[0].(*grpc.CalculatePaymentSplitTaxResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CalculatePaymentSplitTax indicates an expected call of CalculatePaymentSplitTax.
func (mr *MockSplittingServerMockRecorder) CalculatePaymentSplitTax(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CalculatePaymentSplitTax", reflect.TypeOf((*MockSplittingServer)(nil).CalculatePaymentSplitTax), arg0, arg1)
}

// mustEmbedUnimplementedSplittingServer mocks base method.
func (m *MockSplittingServer) mustEmbedUnimplementedSplittingServer() {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "mustEmbedUnimplementedSplittingServer")
}

// mustEmbedUnimplementedSplittingServer indicates an expected call of mustEmbedUnimplementedSplittingServer.
func (mr *MockSplittingServerMockRecorder) mustEmbedUnimplementedSplittingServer() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "mustEmbedUnimplementedSplittingServer", reflect.TypeOf((*MockSplittingServer)(nil).mustEmbedUnimplementedSplittingServer))
}

// MockUnsafeSplittingServer is a mock of UnsafeSplittingServer interface.
type MockUnsafeSplittingServer struct {
	ctrl     *gomock.Controller
	recorder *MockUnsafeSplittingServerMockRecorder
}

// MockUnsafeSplittingServerMockRecorder is the mock recorder for MockUnsafeSplittingServer.
type MockUnsafeSplittingServerMockRecorder struct {
	mock *MockUnsafeSplittingServer
}

// NewMockUnsafeSplittingServer creates a new mock instance.
func NewMockUnsafeSplittingServer(ctrl *gomock.Controller) *MockUnsafeSplittingServer {
	mock := &MockUnsafeSplittingServer{ctrl: ctrl}
	mock.recorder = &MockUnsafeSplittingServerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockUnsafeSplittingServer) EXPECT() *MockUnsafeSplittingServerMockRecorder {
	return m.recorder
}

// mustEmbedUnimplementedSplittingServer mocks base method.
func (m *MockUnsafeSplittingServer) mustEmbedUnimplementedSplittingServer() {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "mustEmbedUnimplementedSplittingServer")
}

// mustEmbedUnimplementedSplittingServer indicates an expected call of mustEmbedUnimplementedSplittingServer.
func (mr *MockUnsafeSplittingServerMockRecorder) mustEmbedUnimplementedSplittingServer() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "mustEmbedUnimplementedSplittingServer", reflect.TypeOf((*MockUnsafeSplittingServer)(nil).mustEmbedUnimplementedSplittingServer))
}
