// Code generated by MockGen. DO NOT EDIT.
// Source: database.go

// Package databasemocks is a generated GoMock package.
package databasemocks

import (
	reflect "reflect"

	middlewares "git.local/sensitive/pkg/middlewares"
	model "git.local/sensitive/processing/acquirer/model"
	schema "git.local/sensitive/processing/acquirer/schema"
	gomock "github.com/golang/mock/gomock"
	context "golang.org/x/net/context"
)

// MockBanker is a mock of Banker interface.
type MockBanker struct {
	ctrl     *gomock.Controller
	recorder *MockBankerMockRecorder
}

// MockBankerMockRecorder is the mock recorder for MockBanker.
type MockBankerMockRecorder struct {
	mock *MockBanker
}

// NewMockBanker creates a new mock instance.
func NewMockBanker(ctrl *gomock.Controller) *MockBanker {
	mock := &MockBanker{ctrl: ctrl}
	mock.recorder = &MockBankerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockBanker) EXPECT() *MockBankerMockRecorder {
	return m.recorder
}

// Create mocks base method.
func (m *MockBanker) Create(ctx context.Context, bank *model.Bank) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Create", ctx, bank)
	ret0, _ := ret[0].(error)
	return ret0
}

// Create indicates an expected call of Create.
func (mr *MockBankerMockRecorder) Create(ctx, bank interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Create", reflect.TypeOf((*MockBanker)(nil).Create), ctx, bank)
}

// Delete mocks base method.
func (m *MockBanker) Delete(ctx context.Context, id uint64) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Delete", ctx, id)
	ret0, _ := ret[0].(error)
	return ret0
}

// Delete indicates an expected call of Delete.
func (mr *MockBankerMockRecorder) Delete(ctx, id interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Delete", reflect.TypeOf((*MockBanker)(nil).Delete), ctx, id)
}

// GetAll mocks base method.
func (m *MockBanker) GetAll(ctx context.Context, pagination *middlewares.PaginationInfo, filter schema.BankFilter) ([]*model.Bank, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAll", ctx, pagination, filter)
	ret0, _ := ret[0].([]*model.Bank)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAll indicates an expected call of GetAll.
func (mr *MockBankerMockRecorder) GetAll(ctx, pagination, filter interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAll", reflect.TypeOf((*MockBanker)(nil).GetAll), ctx, pagination, filter)
}

// GetById mocks base method.
func (m *MockBanker) GetById(ctx context.Context, id uint64) (*model.Bank, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetById", ctx, id)
	ret0, _ := ret[0].(*model.Bank)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetById indicates an expected call of GetById.
func (mr *MockBankerMockRecorder) GetById(ctx, id interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetById", reflect.TypeOf((*MockBanker)(nil).GetById), ctx, id)
}

// GetByName mocks base method.
func (m *MockBanker) GetByName(ctx context.Context, name string, pagination *middlewares.PaginationInfo) ([]*model.Bank, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetByName", ctx, name, pagination)
	ret0, _ := ret[0].([]*model.Bank)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetByName indicates an expected call of GetByName.
func (mr *MockBankerMockRecorder) GetByName(ctx, name, pagination interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetByName", reflect.TypeOf((*MockBanker)(nil).GetByName), ctx, name, pagination)
}

// Update mocks base method.
func (m *MockBanker) Update(ctx context.Context, id uint64, data *model.Bank) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Update", ctx, id, data)
	ret0, _ := ret[0].(error)
	return ret0
}

// Update indicates an expected call of Update.
func (mr *MockBankerMockRecorder) Update(ctx, id, data interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Update", reflect.TypeOf((*MockBanker)(nil).Update), ctx, id, data)
}

// MockBankBiner is a mock of BankBiner interface.
type MockBankBiner struct {
	ctrl     *gomock.Controller
	recorder *MockBankBinerMockRecorder
}

// MockBankBinerMockRecorder is the mock recorder for MockBankBiner.
type MockBankBinerMockRecorder struct {
	mock *MockBankBiner
}

// NewMockBankBiner creates a new mock instance.
func NewMockBankBiner(ctrl *gomock.Controller) *MockBankBiner {
	mock := &MockBankBiner{ctrl: ctrl}
	mock.recorder = &MockBankBinerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockBankBiner) EXPECT() *MockBankBinerMockRecorder {
	return m.recorder
}

// Create mocks base method.
func (m *MockBankBiner) Create(ctx context.Context, bankBin model.BankBin) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Create", ctx, bankBin)
	ret0, _ := ret[0].(error)
	return ret0
}

// Create indicates an expected call of Create.
func (mr *MockBankBinerMockRecorder) Create(ctx, bankBin interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Create", reflect.TypeOf((*MockBankBiner)(nil).Create), ctx, bankBin)
}

// Delete mocks base method.
func (m *MockBankBiner) Delete(ctx context.Context, id uint64) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Delete", ctx, id)
	ret0, _ := ret[0].(error)
	return ret0
}

// Delete indicates an expected call of Delete.
func (mr *MockBankBinerMockRecorder) Delete(ctx, id interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Delete", reflect.TypeOf((*MockBankBiner)(nil).Delete), ctx, id)
}

// GetAll mocks base method.
func (m *MockBankBiner) GetAll(ctx context.Context, pagination *middlewares.PaginationInfo) ([]*model.BankBin, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAll", ctx, pagination)
	ret0, _ := ret[0].([]*model.BankBin)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAll indicates an expected call of GetAll.
func (mr *MockBankBinerMockRecorder) GetAll(ctx, pagination interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAll", reflect.TypeOf((*MockBankBiner)(nil).GetAll), ctx, pagination)
}

// GetByBin mocks base method.
func (m *MockBankBiner) GetByBin(ctx context.Context, eightDigitBin, sixDigitBin, fiveDigitBin string) (*model.BankBin, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetByBin", ctx, eightDigitBin, sixDigitBin, fiveDigitBin)
	ret0, _ := ret[0].(*model.BankBin)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetByBin indicates an expected call of GetByBin.
func (mr *MockBankBinerMockRecorder) GetByBin(ctx, eightDigitBin, sixDigitBin, fiveDigitBin interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetByBin", reflect.TypeOf((*MockBankBiner)(nil).GetByBin), ctx, eightDigitBin, sixDigitBin, fiveDigitBin)
}

// Update mocks base method.
func (m *MockBankBiner) Update(ctx context.Context, id uint64, data model.BankBin) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Update", ctx, id, data)
	ret0, _ := ret[0].(error)
	return ret0
}

// Update indicates an expected call of Update.
func (mr *MockBankBinerMockRecorder) Update(ctx, id, data interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Update", reflect.TypeOf((*MockBankBiner)(nil).Update), ctx, id, data)
}

// MockIpser is a mock of Ipser interface.
type MockIpser struct {
	ctrl     *gomock.Controller
	recorder *MockIpserMockRecorder
}

// MockIpserMockRecorder is the mock recorder for MockIpser.
type MockIpserMockRecorder struct {
	mock *MockIpser
}

// NewMockIpser creates a new mock instance.
func NewMockIpser(ctrl *gomock.Controller) *MockIpser {
	mock := &MockIpser{ctrl: ctrl}
	mock.recorder = &MockIpserMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockIpser) EXPECT() *MockIpserMockRecorder {
	return m.recorder
}

// Create mocks base method.
func (m *MockIpser) Create(ctx context.Context, ips *model.Ips) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Create", ctx, ips)
	ret0, _ := ret[0].(error)
	return ret0
}

// Create indicates an expected call of Create.
func (mr *MockIpserMockRecorder) Create(ctx, ips interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Create", reflect.TypeOf((*MockIpser)(nil).Create), ctx, ips)
}

// Delete mocks base method.
func (m *MockIpser) Delete(ctx context.Context, id uint64) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Delete", ctx, id)
	ret0, _ := ret[0].(error)
	return ret0
}

// Delete indicates an expected call of Delete.
func (mr *MockIpserMockRecorder) Delete(ctx, id interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Delete", reflect.TypeOf((*MockIpser)(nil).Delete), ctx, id)
}

// GetAll mocks base method.
func (m *MockIpser) GetAll(ctx context.Context, pagination *middlewares.PaginationInfo) ([]*model.Ips, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAll", ctx, pagination)
	ret0, _ := ret[0].([]*model.Ips)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAll indicates an expected call of GetAll.
func (mr *MockIpserMockRecorder) GetAll(ctx, pagination interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAll", reflect.TypeOf((*MockIpser)(nil).GetAll), ctx, pagination)
}

// GetByID mocks base method.
func (m *MockIpser) GetByID(ctx context.Context, id uint64) (model.Ips, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetByID", ctx, id)
	ret0, _ := ret[0].(model.Ips)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetByID indicates an expected call of GetByID.
func (mr *MockIpserMockRecorder) GetByID(ctx, id interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetByID", reflect.TypeOf((*MockIpser)(nil).GetByID), ctx, id)
}

// Update mocks base method.
func (m *MockIpser) Update(ctx context.Context, id uint64, data *model.Ips) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Update", ctx, id, data)
	ret0, _ := ret[0].(error)
	return ret0
}

// Update indicates an expected call of Update.
func (mr *MockIpserMockRecorder) Update(ctx, id, data interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Update", reflect.TypeOf((*MockIpser)(nil).Update), ctx, id, data)
}

// MockCountrier is a mock of Countrier interface.
type MockCountrier struct {
	ctrl     *gomock.Controller
	recorder *MockCountrierMockRecorder
}

// MockCountrierMockRecorder is the mock recorder for MockCountrier.
type MockCountrierMockRecorder struct {
	mock *MockCountrier
}

// NewMockCountrier creates a new mock instance.
func NewMockCountrier(ctrl *gomock.Controller) *MockCountrier {
	mock := &MockCountrier{ctrl: ctrl}
	mock.recorder = &MockCountrierMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockCountrier) EXPECT() *MockCountrierMockRecorder {
	return m.recorder
}

// Create mocks base method.
func (m *MockCountrier) Create(ctx context.Context, country *model.Country) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Create", ctx, country)
	ret0, _ := ret[0].(error)
	return ret0
}

// Create indicates an expected call of Create.
func (mr *MockCountrierMockRecorder) Create(ctx, country interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Create", reflect.TypeOf((*MockCountrier)(nil).Create), ctx, country)
}

// Delete mocks base method.
func (m *MockCountrier) Delete(ctx context.Context, id uint64) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Delete", ctx, id)
	ret0, _ := ret[0].(error)
	return ret0
}

// Delete indicates an expected call of Delete.
func (mr *MockCountrierMockRecorder) Delete(ctx, id interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Delete", reflect.TypeOf((*MockCountrier)(nil).Delete), ctx, id)
}

// GetAll mocks base method.
func (m *MockCountrier) GetAll(ctx context.Context, pagination *middlewares.PaginationInfo) ([]*model.Country, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAll", ctx, pagination)
	ret0, _ := ret[0].([]*model.Country)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAll indicates an expected call of GetAll.
func (mr *MockCountrierMockRecorder) GetAll(ctx, pagination interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAll", reflect.TypeOf((*MockCountrier)(nil).GetAll), ctx, pagination)
}

// GetByID mocks base method.
func (m *MockCountrier) GetByID(ctx context.Context, id uint64) (model.Country, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetByID", ctx, id)
	ret0, _ := ret[0].(model.Country)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetByID indicates an expected call of GetByID.
func (mr *MockCountrierMockRecorder) GetByID(ctx, id interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetByID", reflect.TypeOf((*MockCountrier)(nil).GetByID), ctx, id)
}

// GetByName mocks base method.
func (m *MockCountrier) GetByName(ctx context.Context, name string, pagination *middlewares.PaginationInfo) ([]*model.CountryBasic, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetByName", ctx, name, pagination)
	ret0, _ := ret[0].([]*model.CountryBasic)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetByName indicates an expected call of GetByName.
func (mr *MockCountrierMockRecorder) GetByName(ctx, name, pagination interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetByName", reflect.TypeOf((*MockCountrier)(nil).GetByName), ctx, name, pagination)
}

// Update mocks base method.
func (m *MockCountrier) Update(ctx context.Context, id uint64, data *model.Country) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Update", ctx, id, data)
	ret0, _ := ret[0].(error)
	return ret0
}

// Update indicates an expected call of Update.
func (mr *MockCountrierMockRecorder) Update(ctx, id, data interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Update", reflect.TypeOf((*MockCountrier)(nil).Update), ctx, id, data)
}

// MockRuler is a mock of Ruler interface.
type MockRuler struct {
	ctrl     *gomock.Controller
	recorder *MockRulerMockRecorder
}

// MockRulerMockRecorder is the mock recorder for MockRuler.
type MockRulerMockRecorder struct {
	mock *MockRuler
}

// NewMockRuler creates a new mock instance.
func NewMockRuler(ctrl *gomock.Controller) *MockRuler {
	mock := &MockRuler{ctrl: ctrl}
	mock.recorder = &MockRulerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockRuler) EXPECT() *MockRulerMockRecorder {
	return m.recorder
}

// Create mocks base method.
func (m *MockRuler) Create(ctx context.Context, rule *model.Rule) (*model.Rule, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Create", ctx, rule)
	ret0, _ := ret[0].(*model.Rule)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Create indicates an expected call of Create.
func (mr *MockRulerMockRecorder) Create(ctx, rule interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Create", reflect.TypeOf((*MockRuler)(nil).Create), ctx, rule)
}

// FindActiveRulesByProject mocks base method.
func (m *MockRuler) FindActiveRulesByProject(ctx context.Context, projectId, paymentType uint64) (model.Rules, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "FindActiveRulesByProject", ctx, projectId, paymentType)
	ret0, _ := ret[0].(model.Rules)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// FindActiveRulesByProject indicates an expected call of FindActiveRulesByProject.
func (mr *MockRulerMockRecorder) FindActiveRulesByProject(ctx, projectId, paymentType interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "FindActiveRulesByProject", reflect.TypeOf((*MockRuler)(nil).FindActiveRulesByProject), ctx, projectId, paymentType)
}

// FindByAllParam mocks base method.
func (m *MockRuler) FindByAllParam(ctx context.Context, rule *model.Rule, isActive *bool) ([]*model.Rule, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "FindByAllParam", ctx, rule, isActive)
	ret0, _ := ret[0].([]*model.Rule)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// FindByAllParam indicates an expected call of FindByAllParam.
func (mr *MockRulerMockRecorder) FindByAllParam(ctx, rule, isActive interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "FindByAllParam", reflect.TypeOf((*MockRuler)(nil).FindByAllParam), ctx, rule, isActive)
}

// GetBaseRuleByProject mocks base method.
func (m *MockRuler) GetBaseRuleByProject(ctx context.Context, projectId, paymentType uint64) (*model.Rule, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetBaseRuleByProject", ctx, projectId, paymentType)
	ret0, _ := ret[0].(*model.Rule)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetBaseRuleByProject indicates an expected call of GetBaseRuleByProject.
func (mr *MockRulerMockRecorder) GetBaseRuleByProject(ctx, projectId, paymentType interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetBaseRuleByProject", reflect.TypeOf((*MockRuler)(nil).GetBaseRuleByProject), ctx, projectId, paymentType)
}

// GetByID mocks base method.
func (m *MockRuler) GetByID(ctx context.Context, id uint64) (*model.Rule, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetByID", ctx, id)
	ret0, _ := ret[0].(*model.Rule)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetByID indicates an expected call of GetByID.
func (mr *MockRulerMockRecorder) GetByID(ctx, id interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetByID", reflect.TypeOf((*MockRuler)(nil).GetByID), ctx, id)
}

// GetListWithBalancer mocks base method.
func (m *MockRuler) GetListWithBalancer(ctx context.Context, pagination *middlewares.PaginationInfo, request schema.RuleListRequest) ([]*model.Rule, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetListWithBalancer", ctx, pagination, request)
	ret0, _ := ret[0].([]*model.Rule)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetListWithBalancer indicates an expected call of GetListWithBalancer.
func (mr *MockRulerMockRecorder) GetListWithBalancer(ctx, pagination, request interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetListWithBalancer", reflect.TypeOf((*MockRuler)(nil).GetListWithBalancer), ctx, pagination, request)
}

// MockRuleWeighter is a mock of RuleWeighter interface.
type MockRuleWeighter struct {
	ctrl     *gomock.Controller
	recorder *MockRuleWeighterMockRecorder
}

// MockRuleWeighterMockRecorder is the mock recorder for MockRuleWeighter.
type MockRuleWeighterMockRecorder struct {
	mock *MockRuleWeighter
}

// NewMockRuleWeighter creates a new mock instance.
func NewMockRuleWeighter(ctrl *gomock.Controller) *MockRuleWeighter {
	mock := &MockRuleWeighter{ctrl: ctrl}
	mock.recorder = &MockRuleWeighterMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockRuleWeighter) EXPECT() *MockRuleWeighterMockRecorder {
	return m.recorder
}

// ChangeRuleWeightByID mocks base method.
func (m *MockRuleWeighter) ChangeRuleWeightByID(ctx context.Context, ruleID uint64, weight uint8) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ChangeRuleWeightByID", ctx, ruleID, weight)
	ret0, _ := ret[0].(error)
	return ret0
}

// ChangeRuleWeightByID indicates an expected call of ChangeRuleWeightByID.
func (mr *MockRuleWeighterMockRecorder) ChangeRuleWeightByID(ctx, ruleID, weight interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ChangeRuleWeightByID", reflect.TypeOf((*MockRuleWeighter)(nil).ChangeRuleWeightByID), ctx, ruleID, weight)
}

// GetNextRuleByWeight mocks base method.
func (m *MockRuleWeighter) GetNextRuleByWeight(ctx context.Context, rule *model.Rule) (*model.Rule, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetNextRuleByWeight", ctx, rule)
	ret0, _ := ret[0].(*model.Rule)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetNextRuleByWeight indicates an expected call of GetNextRuleByWeight.
func (mr *MockRuleWeighterMockRecorder) GetNextRuleByWeight(ctx, rule interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetNextRuleByWeight", reflect.TypeOf((*MockRuleWeighter)(nil).GetNextRuleByWeight), ctx, rule)
}

// GetPreviousRuleByWeight mocks base method.
func (m *MockRuleWeighter) GetPreviousRuleByWeight(ctx context.Context, rule *model.Rule) (*model.Rule, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetPreviousRuleByWeight", ctx, rule)
	ret0, _ := ret[0].(*model.Rule)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetPreviousRuleByWeight indicates an expected call of GetPreviousRuleByWeight.
func (mr *MockRuleWeighterMockRecorder) GetPreviousRuleByWeight(ctx, rule interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetPreviousRuleByWeight", reflect.TypeOf((*MockRuleWeighter)(nil).GetPreviousRuleByWeight), ctx, rule)
}

// MockRuleActivator is a mock of RuleActivator interface.
type MockRuleActivator struct {
	ctrl     *gomock.Controller
	recorder *MockRuleActivatorMockRecorder
}

// MockRuleActivatorMockRecorder is the mock recorder for MockRuleActivator.
type MockRuleActivatorMockRecorder struct {
	mock *MockRuleActivator
}

// NewMockRuleActivator creates a new mock instance.
func NewMockRuleActivator(ctrl *gomock.Controller) *MockRuleActivator {
	mock := &MockRuleActivator{ctrl: ctrl}
	mock.recorder = &MockRuleActivatorMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockRuleActivator) EXPECT() *MockRuleActivatorMockRecorder {
	return m.recorder
}

// Activate mocks base method.
func (m *MockRuleActivator) Activate(ctx context.Context, ruleID uint64) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Activate", ctx, ruleID)
	ret0, _ := ret[0].(error)
	return ret0
}

// Activate indicates an expected call of Activate.
func (mr *MockRuleActivatorMockRecorder) Activate(ctx, ruleID interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Activate", reflect.TypeOf((*MockRuleActivator)(nil).Activate), ctx, ruleID)
}

// Deactivate mocks base method.
func (m *MockRuleActivator) Deactivate(ctx context.Context, ruleID uint64) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Deactivate", ctx, ruleID)
	ret0, _ := ret[0].(error)
	return ret0
}

// Deactivate indicates an expected call of Deactivate.
func (mr *MockRuleActivatorMockRecorder) Deactivate(ctx, ruleID interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Deactivate", reflect.TypeOf((*MockRuleActivator)(nil).Deactivate), ctx, ruleID)
}

// MockRulePercentager is a mock of RulePercentager interface.
type MockRulePercentager struct {
	ctrl     *gomock.Controller
	recorder *MockRulePercentagerMockRecorder
}

// MockRulePercentagerMockRecorder is the mock recorder for MockRulePercentager.
type MockRulePercentagerMockRecorder struct {
	mock *MockRulePercentager
}

// NewMockRulePercentager creates a new mock instance.
func NewMockRulePercentager(ctrl *gomock.Controller) *MockRulePercentager {
	mock := &MockRulePercentager{ctrl: ctrl}
	mock.recorder = &MockRulePercentagerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockRulePercentager) EXPECT() *MockRulePercentagerMockRecorder {
	return m.recorder
}

// Create mocks base method.
func (m *MockRulePercentager) Create(ctx context.Context, balancers []*model.RulePercentage) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Create", ctx, balancers)
	ret0, _ := ret[0].(error)
	return ret0
}

// Create indicates an expected call of Create.
func (mr *MockRulePercentagerMockRecorder) Create(ctx, balancers interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Create", reflect.TypeOf((*MockRulePercentager)(nil).Create), ctx, balancers)
}

// Delete mocks base method.
func (m *MockRulePercentager) Delete(ctx context.Context, ruleID uint64) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Delete", ctx, ruleID)
	ret0, _ := ret[0].(error)
	return ret0
}

// Delete indicates an expected call of Delete.
func (mr *MockRulePercentagerMockRecorder) Delete(ctx, ruleID interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Delete", reflect.TypeOf((*MockRulePercentager)(nil).Delete), ctx, ruleID)
}

// GetAllByRuleID mocks base method.
func (m *MockRulePercentager) GetAllByRuleID(ctx context.Context, ruleID uint64) (model.RulePercentages, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAllByRuleID", ctx, ruleID)
	ret0, _ := ret[0].(model.RulePercentages)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAllByRuleID indicates an expected call of GetAllByRuleID.
func (mr *MockRulePercentagerMockRecorder) GetAllByRuleID(ctx, ruleID interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAllByRuleID", reflect.TypeOf((*MockRulePercentager)(nil).GetAllByRuleID), ctx, ruleID)
}

// MockAcquirerBasicer is a mock of AcquirerBasicer interface.
type MockAcquirerBasicer struct {
	ctrl     *gomock.Controller
	recorder *MockAcquirerBasicerMockRecorder
}

// MockAcquirerBasicerMockRecorder is the mock recorder for MockAcquirerBasicer.
type MockAcquirerBasicerMockRecorder struct {
	mock *MockAcquirerBasicer
}

// NewMockAcquirerBasicer creates a new mock instance.
func NewMockAcquirerBasicer(ctrl *gomock.Controller) *MockAcquirerBasicer {
	mock := &MockAcquirerBasicer{ctrl: ctrl}
	mock.recorder = &MockAcquirerBasicerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockAcquirerBasicer) EXPECT() *MockAcquirerBasicerMockRecorder {
	return m.recorder
}

// Create mocks base method.
func (m *MockAcquirerBasicer) Create(ctx context.Context, acquirer *model.Acquirer) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Create", ctx, acquirer)
	ret0, _ := ret[0].(error)
	return ret0
}

// Create indicates an expected call of Create.
func (mr *MockAcquirerBasicerMockRecorder) Create(ctx, acquirer interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Create", reflect.TypeOf((*MockAcquirerBasicer)(nil).Create), ctx, acquirer)
}

// GetAllActive mocks base method.
func (m *MockAcquirerBasicer) GetAllActive(ctx context.Context, pagination *middlewares.PaginationInfo) ([]*model.Acquirer, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAllActive", ctx, pagination)
	ret0, _ := ret[0].([]*model.Acquirer)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAllActive indicates an expected call of GetAllActive.
func (mr *MockAcquirerBasicerMockRecorder) GetAllActive(ctx, pagination interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAllActive", reflect.TypeOf((*MockAcquirerBasicer)(nil).GetAllActive), ctx, pagination)
}

// GetAllByFilter mocks base method.
func (m *MockAcquirerBasicer) GetAllByFilter(ctx context.Context, filter schema.AcquirerFilter, pagination *middlewares.PaginationInfo) ([]*model.Acquirer, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAllByFilter", ctx, filter, pagination)
	ret0, _ := ret[0].([]*model.Acquirer)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAllByFilter indicates an expected call of GetAllByFilter.
func (mr *MockAcquirerBasicerMockRecorder) GetAllByFilter(ctx, filter, pagination interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAllByFilter", reflect.TypeOf((*MockAcquirerBasicer)(nil).GetAllByFilter), ctx, filter, pagination)
}

// GetByCode mocks base method.
func (m *MockAcquirerBasicer) GetByCode(ctx context.Context, acquirerCode string) (*model.Acquirer, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetByCode", ctx, acquirerCode)
	ret0, _ := ret[0].(*model.Acquirer)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetByCode indicates an expected call of GetByCode.
func (mr *MockAcquirerBasicerMockRecorder) GetByCode(ctx, acquirerCode interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetByCode", reflect.TypeOf((*MockAcquirerBasicer)(nil).GetByCode), ctx, acquirerCode)
}

// GetByID mocks base method.
func (m *MockAcquirerBasicer) GetByID(ctx context.Context, acquirerID uint64) (*model.Acquirer, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetByID", ctx, acquirerID)
	ret0, _ := ret[0].(*model.Acquirer)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetByID indicates an expected call of GetByID.
func (mr *MockAcquirerBasicerMockRecorder) GetByID(ctx, acquirerID interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetByID", reflect.TypeOf((*MockAcquirerBasicer)(nil).GetByID), ctx, acquirerID)
}

// Update mocks base method.
func (m *MockAcquirerBasicer) Update(ctx context.Context, id uint64, data *model.Acquirer) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Update", ctx, id, data)
	ret0, _ := ret[0].(error)
	return ret0
}

// Update indicates an expected call of Update.
func (mr *MockAcquirerBasicerMockRecorder) Update(ctx, id, data interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Update", reflect.TypeOf((*MockAcquirerBasicer)(nil).Update), ctx, id, data)
}

// MockAcquirer is a mock of Acquirer interface.
type MockAcquirer struct {
	ctrl     *gomock.Controller
	recorder *MockAcquirerMockRecorder
}

// MockAcquirerMockRecorder is the mock recorder for MockAcquirer.
type MockAcquirerMockRecorder struct {
	mock *MockAcquirer
}

// NewMockAcquirer creates a new mock instance.
func NewMockAcquirer(ctrl *gomock.Controller) *MockAcquirer {
	mock := &MockAcquirer{ctrl: ctrl}
	mock.recorder = &MockAcquirerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockAcquirer) EXPECT() *MockAcquirerMockRecorder {
	return m.recorder
}

// GetAcquirersById mocks base method.
func (m *MockAcquirer) GetAcquirersById(ctx context.Context, acquirerIds []uint64) ([]*model.Acquirer, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAcquirersById", ctx, acquirerIds)
	ret0, _ := ret[0].([]*model.Acquirer)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAcquirersById indicates an expected call of GetAcquirersById.
func (mr *MockAcquirerMockRecorder) GetAcquirersById(ctx, acquirerIds interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAcquirersById", reflect.TypeOf((*MockAcquirer)(nil).GetAcquirersById), ctx, acquirerIds)
}

// UpdateStatus mocks base method.
func (m *MockAcquirer) UpdateStatus(ctx context.Context, id uint64, isActive bool) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateStatus", ctx, id, isActive)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpdateStatus indicates an expected call of UpdateStatus.
func (mr *MockAcquirerMockRecorder) UpdateStatus(ctx, id, isActive interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateStatus", reflect.TypeOf((*MockAcquirer)(nil).UpdateStatus), ctx, id, isActive)
}

// MockTerminaler is a mock of Terminaler interface.
type MockTerminaler struct {
	ctrl     *gomock.Controller
	recorder *MockTerminalerMockRecorder
}

// MockTerminalerMockRecorder is the mock recorder for MockTerminaler.
type MockTerminalerMockRecorder struct {
	mock *MockTerminaler
}

// NewMockTerminaler creates a new mock instance.
func NewMockTerminaler(ctrl *gomock.Controller) *MockTerminaler {
	mock := &MockTerminaler{ctrl: ctrl}
	mock.recorder = &MockTerminalerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockTerminaler) EXPECT() *MockTerminalerMockRecorder {
	return m.recorder
}

// FindActiveTerminalsByIDs mocks base method.
func (m *MockTerminaler) FindActiveTerminalsByIDs(ctx context.Context, ids []uint64, projectID, transactionTypeID uint64) (model.Terminals, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "FindActiveTerminalsByIDs", ctx, ids, projectID, transactionTypeID)
	ret0, _ := ret[0].(model.Terminals)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// FindActiveTerminalsByIDs indicates an expected call of FindActiveTerminalsByIDs.
func (mr *MockTerminalerMockRecorder) FindActiveTerminalsByIDs(ctx, ids, projectID, transactionTypeID interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "FindActiveTerminalsByIDs", reflect.TypeOf((*MockTerminaler)(nil).FindActiveTerminalsByIDs), ctx, ids, projectID, transactionTypeID)
}

// FindActiveTerminalsByProject mocks base method.
func (m *MockTerminaler) FindActiveTerminalsByProject(ctx context.Context, projectID, paymentType uint64) (model.Terminals, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "FindActiveTerminalsByProject", ctx, projectID, paymentType)
	ret0, _ := ret[0].(model.Terminals)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// FindActiveTerminalsByProject indicates an expected call of FindActiveTerminalsByProject.
func (mr *MockTerminalerMockRecorder) FindActiveTerminalsByProject(ctx, projectID, paymentType interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "FindActiveTerminalsByProject", reflect.TypeOf((*MockTerminaler)(nil).FindActiveTerminalsByProject), ctx, projectID, paymentType)
}

// GetAllByPaymentType mocks base method.
func (m *MockTerminaler) GetAllByPaymentType(ctx context.Context, projectID, paymentType uint64) ([]*model.Terminal, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAllByPaymentType", ctx, projectID, paymentType)
	ret0, _ := ret[0].([]*model.Terminal)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAllByPaymentType indicates an expected call of GetAllByPaymentType.
func (mr *MockTerminalerMockRecorder) GetAllByPaymentType(ctx, projectID, paymentType interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAllByPaymentType", reflect.TypeOf((*MockTerminaler)(nil).GetAllByPaymentType), ctx, projectID, paymentType)
}

// GetAllPaymentTypes mocks base method.
func (m *MockTerminaler) GetAllPaymentTypes(ctx context.Context, projectID uint64) ([]*model.ResponsePaymentType, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAllPaymentTypes", ctx, projectID)
	ret0, _ := ret[0].([]*model.ResponsePaymentType)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAllPaymentTypes indicates an expected call of GetAllPaymentTypes.
func (mr *MockTerminalerMockRecorder) GetAllPaymentTypes(ctx, projectID interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAllPaymentTypes", reflect.TypeOf((*MockTerminaler)(nil).GetAllPaymentTypes), ctx, projectID)
}

// UpdateByAcquirerAndStatus mocks base method.
func (m *MockTerminaler) UpdateByAcquirerAndStatus(ctx context.Context, acquirerId uint64, searchStatus model.TerminalStatus, data *model.Terminal) (int64, int, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateByAcquirerAndStatus", ctx, acquirerId, searchStatus, data)
	ret0, _ := ret[0].(int64)
	ret1, _ := ret[1].(int)
	ret2, _ := ret[2].(error)
	return ret0, ret1, ret2
}

// UpdateByAcquirerAndStatus indicates an expected call of UpdateByAcquirerAndStatus.
func (mr *MockTerminalerMockRecorder) UpdateByAcquirerAndStatus(ctx, acquirerId, searchStatus, data interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateByAcquirerAndStatus", reflect.TypeOf((*MockTerminaler)(nil).UpdateByAcquirerAndStatus), ctx, acquirerId, searchStatus, data)
}

// UpdateConfig mocks base method.
func (m *MockTerminaler) UpdateConfig(ctx context.Context, id uint64, encryptedConfig string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateConfig", ctx, id, encryptedConfig)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpdateConfig indicates an expected call of UpdateConfig.
func (mr *MockTerminalerMockRecorder) UpdateConfig(ctx, id, encryptedConfig interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateConfig", reflect.TypeOf((*MockTerminaler)(nil).UpdateConfig), ctx, id, encryptedConfig)
}

// MockTerminalBasicer is a mock of TerminalBasicer interface.
type MockTerminalBasicer struct {
	ctrl     *gomock.Controller
	recorder *MockTerminalBasicerMockRecorder
}

// MockTerminalBasicerMockRecorder is the mock recorder for MockTerminalBasicer.
type MockTerminalBasicerMockRecorder struct {
	mock *MockTerminalBasicer
}

// NewMockTerminalBasicer creates a new mock instance.
func NewMockTerminalBasicer(ctrl *gomock.Controller) *MockTerminalBasicer {
	mock := &MockTerminalBasicer{ctrl: ctrl}
	mock.recorder = &MockTerminalBasicerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockTerminalBasicer) EXPECT() *MockTerminalBasicerMockRecorder {
	return m.recorder
}

// Create mocks base method.
func (m *MockTerminalBasicer) Create(ctx context.Context, terminal *model.Terminal) (*model.Terminal, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Create", ctx, terminal)
	ret0, _ := ret[0].(*model.Terminal)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Create indicates an expected call of Create.
func (mr *MockTerminalBasicerMockRecorder) Create(ctx, terminal interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Create", reflect.TypeOf((*MockTerminalBasicer)(nil).Create), ctx, terminal)
}

// GetAll mocks base method.
func (m *MockTerminalBasicer) GetAll(ctx context.Context, projectID uint64) ([]model.Terminal, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAll", ctx, projectID)
	ret0, _ := ret[0].([]model.Terminal)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAll indicates an expected call of GetAll.
func (mr *MockTerminalBasicerMockRecorder) GetAll(ctx, projectID interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAll", reflect.TypeOf((*MockTerminalBasicer)(nil).GetAll), ctx, projectID)
}

// GetAllInfoByID mocks base method.
func (m *MockTerminalBasicer) GetAllInfoByID(ctx context.Context, id uint64) (*model.Terminal, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAllInfoByID", ctx, id)
	ret0, _ := ret[0].(*model.Terminal)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAllInfoByID indicates an expected call of GetAllInfoByID.
func (mr *MockTerminalBasicerMockRecorder) GetAllInfoByID(ctx, id interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAllInfoByID", reflect.TypeOf((*MockTerminalBasicer)(nil).GetAllInfoByID), ctx, id)
}

// GetByAcquirers mocks base method.
func (m *MockTerminalBasicer) GetByAcquirers(ctx context.Context, acquirers []*model.Acquirer) ([]*model.Terminal, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetByAcquirers", ctx, acquirers)
	ret0, _ := ret[0].([]*model.Terminal)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetByAcquirers indicates an expected call of GetByAcquirers.
func (mr *MockTerminalBasicerMockRecorder) GetByAcquirers(ctx, acquirers interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetByAcquirers", reflect.TypeOf((*MockTerminalBasicer)(nil).GetByAcquirers), ctx, acquirers)
}

// GetByFilters mocks base method.
func (m *MockTerminalBasicer) GetByFilters(ctx context.Context, filters schema.FiltersTerminalRequest, pagination *middlewares.PaginationInfo) ([]model.Terminal, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetByFilters", ctx, filters, pagination)
	ret0, _ := ret[0].([]model.Terminal)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetByFilters indicates an expected call of GetByFilters.
func (mr *MockTerminalBasicerMockRecorder) GetByFilters(ctx, filters, pagination interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetByFilters", reflect.TypeOf((*MockTerminalBasicer)(nil).GetByFilters), ctx, filters, pagination)
}

// UpdateStatus mocks base method.
func (m *MockTerminalBasicer) UpdateStatus(ctx context.Context, id uint64, status model.TerminalStatus) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateStatus", ctx, id, status)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpdateStatus indicates an expected call of UpdateStatus.
func (mr *MockTerminalBasicerMockRecorder) UpdateStatus(ctx, id, status interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateStatus", reflect.TypeOf((*MockTerminalBasicer)(nil).UpdateStatus), ctx, id, status)
}

// MockTerminalTwoStager is a mock of TerminalTwoStager interface.
type MockTerminalTwoStager struct {
	ctrl     *gomock.Controller
	recorder *MockTerminalTwoStagerMockRecorder
}

// MockTerminalTwoStagerMockRecorder is the mock recorder for MockTerminalTwoStager.
type MockTerminalTwoStagerMockRecorder struct {
	mock *MockTerminalTwoStager
}

// NewMockTerminalTwoStager creates a new mock instance.
func NewMockTerminalTwoStager(ctrl *gomock.Controller) *MockTerminalTwoStager {
	mock := &MockTerminalTwoStager{ctrl: ctrl}
	mock.recorder = &MockTerminalTwoStagerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockTerminalTwoStager) EXPECT() *MockTerminalTwoStagerMockRecorder {
	return m.recorder
}

// UpdateTimeout mocks base method.
func (m *MockTerminalTwoStager) UpdateTimeout(ctx context.Context, id uint64, timeout uint32) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateTimeout", ctx, id, timeout)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpdateTimeout indicates an expected call of UpdateTimeout.
func (mr *MockTerminalTwoStagerMockRecorder) UpdateTimeout(ctx, id, timeout interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateTimeout", reflect.TypeOf((*MockTerminalTwoStager)(nil).UpdateTimeout), ctx, id, timeout)
}

// MockTerminalBalancer is a mock of TerminalBalancer interface.
type MockTerminalBalancer struct {
	ctrl     *gomock.Controller
	recorder *MockTerminalBalancerMockRecorder
}

// MockTerminalBalancerMockRecorder is the mock recorder for MockTerminalBalancer.
type MockTerminalBalancerMockRecorder struct {
	mock *MockTerminalBalancer
}

// NewMockTerminalBalancer creates a new mock instance.
func NewMockTerminalBalancer(ctrl *gomock.Controller) *MockTerminalBalancer {
	mock := &MockTerminalBalancer{ctrl: ctrl}
	mock.recorder = &MockTerminalBalancerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockTerminalBalancer) EXPECT() *MockTerminalBalancerMockRecorder {
	return m.recorder
}

// GetExactTerminal mocks base method.
func (m *MockTerminalBalancer) GetExactTerminal(ctx context.Context, projectID, transcationTypeID, acquirerID uint64) (model.Terminal, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetExactTerminal", ctx, projectID, transcationTypeID, acquirerID)
	ret0, _ := ret[0].(model.Terminal)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetExactTerminal indicates an expected call of GetExactTerminal.
func (mr *MockTerminalBalancerMockRecorder) GetExactTerminal(ctx, projectID, transcationTypeID, acquirerID interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetExactTerminal", reflect.TypeOf((*MockTerminalBalancer)(nil).GetExactTerminal), ctx, projectID, transcationTypeID, acquirerID)
}

// GetTerminals mocks base method.
func (m *MockTerminalBalancer) GetTerminals(ctx context.Context, projectID, transactionTypeID uint64) (model.Terminals, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetTerminals", ctx, projectID, transactionTypeID)
	ret0, _ := ret[0].(model.Terminals)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetTerminals indicates an expected call of GetTerminals.
func (mr *MockTerminalBalancerMockRecorder) GetTerminals(ctx, projectID, transactionTypeID interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetTerminals", reflect.TypeOf((*MockTerminalBalancer)(nil).GetTerminals), ctx, projectID, transactionTypeID)
}

// GetTerminalsByIDs mocks base method.
func (m *MockTerminalBalancer) GetTerminalsByIDs(ctx context.Context, terminalIDs []uint64) ([]model.Terminal, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetTerminalsByIDs", ctx, terminalIDs)
	ret0, _ := ret[0].([]model.Terminal)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetTerminalsByIDs indicates an expected call of GetTerminalsByIDs.
func (mr *MockTerminalBalancerMockRecorder) GetTerminalsByIDs(ctx, terminalIDs interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetTerminalsByIDs", reflect.TypeOf((*MockTerminalBalancer)(nil).GetTerminalsByIDs), ctx, terminalIDs)
}

// MockIssuer is a mock of Issuer interface.
type MockIssuer struct {
	ctrl     *gomock.Controller
	recorder *MockIssuerMockRecorder
}

// MockIssuerMockRecorder is the mock recorder for MockIssuer.
type MockIssuerMockRecorder struct {
	mock *MockIssuer
}

// NewMockIssuer creates a new mock instance.
func NewMockIssuer(ctrl *gomock.Controller) *MockIssuer {
	mock := &MockIssuer{ctrl: ctrl}
	mock.recorder = &MockIssuerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockIssuer) EXPECT() *MockIssuerMockRecorder {
	return m.recorder
}

// GetAll mocks base method.
func (m *MockIssuer) GetAll(ctx context.Context, pagination *middlewares.PaginationInfo) ([]*model.BankBins, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAll", ctx, pagination)
	ret0, _ := ret[0].([]*model.BankBins)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAll indicates an expected call of GetAll.
func (mr *MockIssuerMockRecorder) GetAll(ctx, pagination interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAll", reflect.TypeOf((*MockIssuer)(nil).GetAll), ctx, pagination)
}

// GetByID mocks base method.
func (m *MockIssuer) GetByID(ctx context.Context, id uint64) (model.Bank, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetByID", ctx, id)
	ret0, _ := ret[0].(model.Bank)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetByID indicates an expected call of GetByID.
func (mr *MockIssuerMockRecorder) GetByID(ctx, id interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetByID", reflect.TypeOf((*MockIssuer)(nil).GetByID), ctx, id)
}

// MockCountryBanker is a mock of CountryBanker interface.
type MockCountryBanker struct {
	ctrl     *gomock.Controller
	recorder *MockCountryBankerMockRecorder
}

// MockCountryBankerMockRecorder is the mock recorder for MockCountryBanker.
type MockCountryBankerMockRecorder struct {
	mock *MockCountryBanker
}

// NewMockCountryBanker creates a new mock instance.
func NewMockCountryBanker(ctrl *gomock.Controller) *MockCountryBanker {
	mock := &MockCountryBanker{ctrl: ctrl}
	mock.recorder = &MockCountryBankerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockCountryBanker) EXPECT() *MockCountryBankerMockRecorder {
	return m.recorder
}

// Create mocks base method.
func (m *MockCountryBanker) Create(ctx context.Context, countryBank *model.CountryBank) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Create", ctx, countryBank)
	ret0, _ := ret[0].(error)
	return ret0
}

// Create indicates an expected call of Create.
func (mr *MockCountryBankerMockRecorder) Create(ctx, countryBank interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Create", reflect.TypeOf((*MockCountryBanker)(nil).Create), ctx, countryBank)
}

// Delete mocks base method.
func (m *MockCountryBanker) Delete(ctx context.Context, countryId, currencyId uint64) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Delete", ctx, countryId, currencyId)
	ret0, _ := ret[0].(error)
	return ret0
}

// Delete indicates an expected call of Delete.
func (mr *MockCountryBankerMockRecorder) Delete(ctx, countryId, currencyId interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Delete", reflect.TypeOf((*MockCountryBanker)(nil).Delete), ctx, countryId, currencyId)
}

// GetBanksByCountryID mocks base method.
func (m *MockCountryBanker) GetBanksByCountryID(ctx context.Context, countryID uint64) ([]*model.Bank, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetBanksByCountryID", ctx, countryID)
	ret0, _ := ret[0].([]*model.Bank)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetBanksByCountryID indicates an expected call of GetBanksByCountryID.
func (mr *MockCountryBankerMockRecorder) GetBanksByCountryID(ctx, countryID interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetBanksByCountryID", reflect.TypeOf((*MockCountryBanker)(nil).GetBanksByCountryID), ctx, countryID)
}

// GetCountriesByBank mocks base method.
func (m *MockCountryBanker) GetCountriesByBank(ctx context.Context, bankId uint64) ([]*model.Country, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetCountriesByBank", ctx, bankId)
	ret0, _ := ret[0].([]*model.Country)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetCountriesByBank indicates an expected call of GetCountriesByBank.
func (mr *MockCountryBankerMockRecorder) GetCountriesByBank(ctx, bankId interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetCountriesByBank", reflect.TypeOf((*MockCountryBanker)(nil).GetCountriesByBank), ctx, bankId)
}

// MockTerminalProjecter is a mock of TerminalProjecter interface.
type MockTerminalProjecter struct {
	ctrl     *gomock.Controller
	recorder *MockTerminalProjecterMockRecorder
}

// MockTerminalProjecterMockRecorder is the mock recorder for MockTerminalProjecter.
type MockTerminalProjecterMockRecorder struct {
	mock *MockTerminalProjecter
}

// NewMockTerminalProjecter creates a new mock instance.
func NewMockTerminalProjecter(ctrl *gomock.Controller) *MockTerminalProjecter {
	mock := &MockTerminalProjecter{ctrl: ctrl}
	mock.recorder = &MockTerminalProjecterMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockTerminalProjecter) EXPECT() *MockTerminalProjecterMockRecorder {
	return m.recorder
}

// Create mocks base method.
func (m *MockTerminalProjecter) Create(ctx context.Context, terminalProject *model.TerminalProject) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Create", ctx, terminalProject)
	ret0, _ := ret[0].(error)
	return ret0
}

// Create indicates an expected call of Create.
func (mr *MockTerminalProjecterMockRecorder) Create(ctx, terminalProject interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Create", reflect.TypeOf((*MockTerminalProjecter)(nil).Create), ctx, terminalProject)
}

// GetByID mocks base method.
func (m *MockTerminalProjecter) GetByID(ctx context.Context, id uint64) (*model.TerminalProject, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetByID", ctx, id)
	ret0, _ := ret[0].(*model.TerminalProject)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetByID indicates an expected call of GetByID.
func (mr *MockTerminalProjecterMockRecorder) GetByID(ctx, id interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetByID", reflect.TypeOf((*MockTerminalProjecter)(nil).GetByID), ctx, id)
}

// GetByProjectID mocks base method.
func (m *MockTerminalProjecter) GetByProjectID(ctx context.Context, projectID uint64) ([]*model.TerminalProject, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetByProjectID", ctx, projectID)
	ret0, _ := ret[0].([]*model.TerminalProject)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetByProjectID indicates an expected call of GetByProjectID.
func (mr *MockTerminalProjecterMockRecorder) GetByProjectID(ctx, projectID interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetByProjectID", reflect.TypeOf((*MockTerminalProjecter)(nil).GetByProjectID), ctx, projectID)
}

// Update mocks base method.
func (m *MockTerminalProjecter) Update(ctx context.Context, id uint64, request schema.UpdateTerminalProjectRequest) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Update", ctx, id, request)
	ret0, _ := ret[0].(error)
	return ret0
}

// Update indicates an expected call of Update.
func (mr *MockTerminalProjecterMockRecorder) Update(ctx, id, request interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Update", reflect.TypeOf((*MockTerminalProjecter)(nil).Update), ctx, id, request)
}

// UpdateStatus mocks base method.
func (m *MockTerminalProjecter) UpdateStatus(ctx context.Context, id uint64, request schema.UpdateTerminalProjectStatusRequest) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateStatus", ctx, id, request)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpdateStatus indicates an expected call of UpdateStatus.
func (mr *MockTerminalProjecterMockRecorder) UpdateStatus(ctx, id, request interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateStatus", reflect.TypeOf((*MockTerminalProjecter)(nil).UpdateStatus), ctx, id, request)
}
