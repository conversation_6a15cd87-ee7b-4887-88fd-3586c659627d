// Code generated by MockGen. DO NOT EDIT.
// Source: transaction_type_grpc.pb.go

// Package grpcmock is a generated GoMock package.
package grpcmock

import (
	context "context"
	reflect "reflect"

	grpc "git.local/sensitive/innerpb/processing/grpc"
	gomock "github.com/golang/mock/gomock"
	grpc0 "google.golang.org/grpc"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
)

// MockTransactionTypeClient is a mock of TransactionTypeClient interface.
type MockTransactionTypeClient struct {
	ctrl     *gomock.Controller
	recorder *MockTransactionTypeClientMockRecorder
}

// MockTransactionTypeClientMockRecorder is the mock recorder for MockTransactionTypeClient.
type MockTransactionTypeClientMockRecorder struct {
	mock *MockTransactionTypeClient
}

// NewMockTransactionTypeClient creates a new mock instance.
func NewMockTransactionTypeClient(ctrl *gomock.Controller) *MockTransactionTypeClient {
	mock := &MockTransactionTypeClient{ctrl: ctrl}
	mock.recorder = &MockTransactionTypeClientMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockTransactionTypeClient) EXPECT() *MockTransactionTypeClientMockRecorder {
	return m.recorder
}

// GetAggregatedTransactionType mocks base method.
func (m *MockTransactionTypeClient) GetAggregatedTransactionType(ctx context.Context, in *grpc.GetAggregatedTransactionTypeRequestV1, opts ...grpc0.CallOption) (*grpc.GetAggregatedTransactionTypeResponseV1, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetAggregatedTransactionType", varargs...)
	ret0, _ := ret[0].(*grpc.GetAggregatedTransactionTypeResponseV1)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAggregatedTransactionType indicates an expected call of GetAggregatedTransactionType.
func (mr *MockTransactionTypeClientMockRecorder) GetAggregatedTransactionType(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAggregatedTransactionType", reflect.TypeOf((*MockTransactionTypeClient)(nil).GetAggregatedTransactionType), varargs...)
}

// GetAggregatedTransactionTypeByTypeID mocks base method.
func (m *MockTransactionTypeClient) GetAggregatedTransactionTypeByTypeID(ctx context.Context, in *grpc.GetAggregatedTransactionTypeByTypeIDRequest, opts ...grpc0.CallOption) (*grpc.GetAggregatedTransactionTypeByTypeIDResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetAggregatedTransactionTypeByTypeID", varargs...)
	ret0, _ := ret[0].(*grpc.GetAggregatedTransactionTypeByTypeIDResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAggregatedTransactionTypeByTypeID indicates an expected call of GetAggregatedTransactionTypeByTypeID.
func (mr *MockTransactionTypeClientMockRecorder) GetAggregatedTransactionTypeByTypeID(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAggregatedTransactionTypeByTypeID", reflect.TypeOf((*MockTransactionTypeClient)(nil).GetAggregatedTransactionTypeByTypeID), varargs...)
}

// GetAggregatedTypeByID mocks base method.
func (m *MockTransactionTypeClient) GetAggregatedTypeByID(ctx context.Context, in *grpc.GetAggregatedTypeByIDRequestV1, opts ...grpc0.CallOption) (*grpc.GetAggregatedTypeByIDResponseV1, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetAggregatedTypeByID", varargs...)
	ret0, _ := ret[0].(*grpc.GetAggregatedTypeByIDResponseV1)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAggregatedTypeByID indicates an expected call of GetAggregatedTypeByID.
func (mr *MockTransactionTypeClientMockRecorder) GetAggregatedTypeByID(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAggregatedTypeByID", reflect.TypeOf((*MockTransactionTypeClient)(nil).GetAggregatedTypeByID), varargs...)
}

// GetAll mocks base method.
func (m *MockTransactionTypeClient) GetAll(ctx context.Context, in *emptypb.Empty, opts ...grpc0.CallOption) (*grpc.TransactionTypeResponseV1, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetAll", varargs...)
	ret0, _ := ret[0].(*grpc.TransactionTypeResponseV1)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAll indicates an expected call of GetAll.
func (mr *MockTransactionTypeClientMockRecorder) GetAll(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAll", reflect.TypeOf((*MockTransactionTypeClient)(nil).GetAll), varargs...)
}

// GetTransactionPayInTypes mocks base method.
func (m *MockTransactionTypeClient) GetTransactionPayInTypes(ctx context.Context, in *emptypb.Empty, opts ...grpc0.CallOption) (*grpc.TransactionTypeResponseV1, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetTransactionPayInTypes", varargs...)
	ret0, _ := ret[0].(*grpc.TransactionTypeResponseV1)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetTransactionPayInTypes indicates an expected call of GetTransactionPayInTypes.
func (mr *MockTransactionTypeClientMockRecorder) GetTransactionPayInTypes(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetTransactionPayInTypes", reflect.TypeOf((*MockTransactionTypeClient)(nil).GetTransactionPayInTypes), varargs...)
}

// GetTransactionPayOutTypes mocks base method.
func (m *MockTransactionTypeClient) GetTransactionPayOutTypes(ctx context.Context, in *emptypb.Empty, opts ...grpc0.CallOption) (*grpc.TransactionTypeResponseV1, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetTransactionPayOutTypes", varargs...)
	ret0, _ := ret[0].(*grpc.TransactionTypeResponseV1)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetTransactionPayOutTypes indicates an expected call of GetTransactionPayOutTypes.
func (mr *MockTransactionTypeClientMockRecorder) GetTransactionPayOutTypes(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetTransactionPayOutTypes", reflect.TypeOf((*MockTransactionTypeClient)(nil).GetTransactionPayOutTypes), varargs...)
}

// MockTransactionTypeServer is a mock of TransactionTypeServer interface.
type MockTransactionTypeServer struct {
	ctrl     *gomock.Controller
	recorder *MockTransactionTypeServerMockRecorder
}

// MockTransactionTypeServerMockRecorder is the mock recorder for MockTransactionTypeServer.
type MockTransactionTypeServerMockRecorder struct {
	mock *MockTransactionTypeServer
}

// NewMockTransactionTypeServer creates a new mock instance.
func NewMockTransactionTypeServer(ctrl *gomock.Controller) *MockTransactionTypeServer {
	mock := &MockTransactionTypeServer{ctrl: ctrl}
	mock.recorder = &MockTransactionTypeServerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockTransactionTypeServer) EXPECT() *MockTransactionTypeServerMockRecorder {
	return m.recorder
}

// GetAggregatedTransactionType mocks base method.
func (m *MockTransactionTypeServer) GetAggregatedTransactionType(arg0 context.Context, arg1 *grpc.GetAggregatedTransactionTypeRequestV1) (*grpc.GetAggregatedTransactionTypeResponseV1, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAggregatedTransactionType", arg0, arg1)
	ret0, _ := ret[0].(*grpc.GetAggregatedTransactionTypeResponseV1)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAggregatedTransactionType indicates an expected call of GetAggregatedTransactionType.
func (mr *MockTransactionTypeServerMockRecorder) GetAggregatedTransactionType(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAggregatedTransactionType", reflect.TypeOf((*MockTransactionTypeServer)(nil).GetAggregatedTransactionType), arg0, arg1)
}

// GetAggregatedTransactionTypeByTypeID mocks base method.
func (m *MockTransactionTypeServer) GetAggregatedTransactionTypeByTypeID(arg0 context.Context, arg1 *grpc.GetAggregatedTransactionTypeByTypeIDRequest) (*grpc.GetAggregatedTransactionTypeByTypeIDResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAggregatedTransactionTypeByTypeID", arg0, arg1)
	ret0, _ := ret[0].(*grpc.GetAggregatedTransactionTypeByTypeIDResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAggregatedTransactionTypeByTypeID indicates an expected call of GetAggregatedTransactionTypeByTypeID.
func (mr *MockTransactionTypeServerMockRecorder) GetAggregatedTransactionTypeByTypeID(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAggregatedTransactionTypeByTypeID", reflect.TypeOf((*MockTransactionTypeServer)(nil).GetAggregatedTransactionTypeByTypeID), arg0, arg1)
}

// GetAggregatedTypeByID mocks base method.
func (m *MockTransactionTypeServer) GetAggregatedTypeByID(arg0 context.Context, arg1 *grpc.GetAggregatedTypeByIDRequestV1) (*grpc.GetAggregatedTypeByIDResponseV1, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAggregatedTypeByID", arg0, arg1)
	ret0, _ := ret[0].(*grpc.GetAggregatedTypeByIDResponseV1)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAggregatedTypeByID indicates an expected call of GetAggregatedTypeByID.
func (mr *MockTransactionTypeServerMockRecorder) GetAggregatedTypeByID(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAggregatedTypeByID", reflect.TypeOf((*MockTransactionTypeServer)(nil).GetAggregatedTypeByID), arg0, arg1)
}

// GetAll mocks base method.
func (m *MockTransactionTypeServer) GetAll(arg0 context.Context, arg1 *emptypb.Empty) (*grpc.TransactionTypeResponseV1, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAll", arg0, arg1)
	ret0, _ := ret[0].(*grpc.TransactionTypeResponseV1)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAll indicates an expected call of GetAll.
func (mr *MockTransactionTypeServerMockRecorder) GetAll(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAll", reflect.TypeOf((*MockTransactionTypeServer)(nil).GetAll), arg0, arg1)
}

// GetTransactionPayInTypes mocks base method.
func (m *MockTransactionTypeServer) GetTransactionPayInTypes(arg0 context.Context, arg1 *emptypb.Empty) (*grpc.TransactionTypeResponseV1, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetTransactionPayInTypes", arg0, arg1)
	ret0, _ := ret[0].(*grpc.TransactionTypeResponseV1)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetTransactionPayInTypes indicates an expected call of GetTransactionPayInTypes.
func (mr *MockTransactionTypeServerMockRecorder) GetTransactionPayInTypes(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetTransactionPayInTypes", reflect.TypeOf((*MockTransactionTypeServer)(nil).GetTransactionPayInTypes), arg0, arg1)
}

// GetTransactionPayOutTypes mocks base method.
func (m *MockTransactionTypeServer) GetTransactionPayOutTypes(arg0 context.Context, arg1 *emptypb.Empty) (*grpc.TransactionTypeResponseV1, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetTransactionPayOutTypes", arg0, arg1)
	ret0, _ := ret[0].(*grpc.TransactionTypeResponseV1)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetTransactionPayOutTypes indicates an expected call of GetTransactionPayOutTypes.
func (mr *MockTransactionTypeServerMockRecorder) GetTransactionPayOutTypes(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetTransactionPayOutTypes", reflect.TypeOf((*MockTransactionTypeServer)(nil).GetTransactionPayOutTypes), arg0, arg1)
}

// mustEmbedUnimplementedTransactionTypeServer mocks base method.
func (m *MockTransactionTypeServer) mustEmbedUnimplementedTransactionTypeServer() {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "mustEmbedUnimplementedTransactionTypeServer")
}

// mustEmbedUnimplementedTransactionTypeServer indicates an expected call of mustEmbedUnimplementedTransactionTypeServer.
func (mr *MockTransactionTypeServerMockRecorder) mustEmbedUnimplementedTransactionTypeServer() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "mustEmbedUnimplementedTransactionTypeServer", reflect.TypeOf((*MockTransactionTypeServer)(nil).mustEmbedUnimplementedTransactionTypeServer))
}

// MockUnsafeTransactionTypeServer is a mock of UnsafeTransactionTypeServer interface.
type MockUnsafeTransactionTypeServer struct {
	ctrl     *gomock.Controller
	recorder *MockUnsafeTransactionTypeServerMockRecorder
}

// MockUnsafeTransactionTypeServerMockRecorder is the mock recorder for MockUnsafeTransactionTypeServer.
type MockUnsafeTransactionTypeServerMockRecorder struct {
	mock *MockUnsafeTransactionTypeServer
}

// NewMockUnsafeTransactionTypeServer creates a new mock instance.
func NewMockUnsafeTransactionTypeServer(ctrl *gomock.Controller) *MockUnsafeTransactionTypeServer {
	mock := &MockUnsafeTransactionTypeServer{ctrl: ctrl}
	mock.recorder = &MockUnsafeTransactionTypeServerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockUnsafeTransactionTypeServer) EXPECT() *MockUnsafeTransactionTypeServerMockRecorder {
	return m.recorder
}

// mustEmbedUnimplementedTransactionTypeServer mocks base method.
func (m *MockUnsafeTransactionTypeServer) mustEmbedUnimplementedTransactionTypeServer() {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "mustEmbedUnimplementedTransactionTypeServer")
}

// mustEmbedUnimplementedTransactionTypeServer indicates an expected call of mustEmbedUnimplementedTransactionTypeServer.
func (mr *MockUnsafeTransactionTypeServerMockRecorder) mustEmbedUnimplementedTransactionTypeServer() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "mustEmbedUnimplementedTransactionTypeServer", reflect.TypeOf((*MockUnsafeTransactionTypeServer)(nil).mustEmbedUnimplementedTransactionTypeServer))
}
