// Code generated by protoc-gen-go-mvp. DO NOT EDIT.
// versions:
// - protoc-gen-go-mvp v2.0.1
// created by mvp authors

package grpc

import (
	context "context"
	grpc "google.golang.org/grpc"
	metadata "google.golang.org/grpc/metadata"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
)

func NewIamTransferAutomaticServer(
	srv TransferAutomaticServer,
) TransferAutomaticServer {
	return &iamTransferAutomaticServer{
		srv: srv,
	}
}

var _ TransferAutomaticServer = (*iamTransferAutomaticServer)(nil)

type iamTransferAutomaticServer struct {
	UnimplementedTransferAutomaticServer

	srv TransferAutomaticServer
}

func (s *iamTransferAutomaticServer) StartCreateTransferByRulesWorker(
	ctx context.Context,
	req *CreateTransferByRulesRequest,
) (
	*emptypb.Empty,
	error,
) {
	return s.srv.StartCreateTransferByRulesWorker(ctx, req)
}

func NewIamTransferAutomaticClient(
	client TransferAutomaticClient,
) TransferAutomaticClient {
	return &iamTransferAutomaticClient{
		client: client,
	}
}

type iamTransferAutomaticClient struct {
	client TransferAutomaticClient
}

func (s *iamTransferAutomaticClient) StartCreateTransferByRulesWorker(
	ctx context.Context,
	req *CreateTransferByRulesRequest,
	opts ...grpc.CallOption,
) (
	*emptypb.Empty,
	error,
) {
	md, _ := metadata.FromIncomingContext(ctx)
	return s.client.StartCreateTransferByRulesWorker(metadata.NewOutgoingContext(ctx, md), req)
}
