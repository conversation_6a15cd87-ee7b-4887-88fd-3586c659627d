edition = "2023";

package processing.epay_status.epay;

option go_package = "git.local/sensitive/innerpb/processing/grpc";

import "inner/processing/grpc/transaction_status.proto";

import "mvp/proto/refs.proto";
import "mvp/proto/enum.proto";

import "google/protobuf/descriptor.proto";

message EpayStatusCodeRef {
  string status = 1;
  processing.transaction.transaction_status.EnumTransactionStatus transaction_status = 2;
}

extend google.protobuf.EnumValueOptions {
  EpayStatusCodeRef epay_status_code_value = 200008;
}

extend google.protobuf.EnumOptions {
  EpayStatusCodeRef default_epay_status_code_value = 200009;
}

enum EnumEpayStatusCode {
  option(mvp.default_ref) = "default_epay_status_code_value";
  option(mvp.ref) = "epay_status_code_value";
  option(default_epay_status_code_value) = {
    status: "undefined",
    transaction_status: TransactionStatusHolded,
  };
  Charge = 0 [(epay_status_code_value) = {
    status: "CHARGE",
    transaction_status: TransactionStatusSuccess,
  }, (mvp.from_string) = "CHARGE"];
  New = 1 [(epay_status_code_value) = {
    status: "NEW",
    transaction_status: TransactionStatusHolded,
  }, (mvp.from_string) = "NEW"];
  Refund = 2 [(epay_status_code_value) = {
    status: "REFUND",
    transaction_status: TransactionStatusRefund,
  }, (mvp.from_string) = "REFUND"];
  Cancel = 3 [(epay_status_code_value) = {
    status: "CANCEL",
    transaction_status: TransactionStatusCanceled,
  }, (mvp.from_string) = "CANCEL"];
  Cancel_Old = 4 [(epay_status_code_value) = {
    status: "CANCEL_OLD",
    transaction_status: TransactionStatusHolded,
  }, (mvp.from_string) = "CANCEL_OLD"];
  ThreeD = 5 [(epay_status_code_value) = {
    status: "3D",
    transaction_status: TransactionStatusThreeDSWaiting,
  }, (mvp.from_string) = "3D"];
  Reject = 6 [(epay_status_code_value) = {
    status: "REJECT",
    transaction_status: TransactionStatusFailed,
  }, (mvp.from_string) = "REJECT"];
  Auth = 7 [(epay_status_code_value) = {
    status: "AUTH",
    transaction_status: TransactionStatusAuthorized,
  }, (mvp.from_string) = "AUTH"];
  Failed = 8 [(epay_status_code_value) = {
    status: "FAILED",
    transaction_status: TransactionStatusAuthorized,
  }, (mvp.from_string) = "FAILED"];
}