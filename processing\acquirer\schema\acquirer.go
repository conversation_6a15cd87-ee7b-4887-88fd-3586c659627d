package schema

import (
	"github.com/go-playground/validator/v10"

	"git.local/sensitive/innerpb/processing/goerr"
	"git.local/sensitive/processing/acquirer/model"
)

type Acquirer struct {
	Code   string `json:"code" validate:"required"`
	Name   string `json:"name" validate:"required"`
	BankID uint64 `json:"bank_id" validate:"required"`
	//Contract    *string `json:"contract" validate:"required"`
	Description *string `json:"description" validate:"required"`
	CountryID   uint64  `json:"country_id" validate:"required"`
}

type (
	AcquirerFilter struct {
		AcquirerID uint64 `form:"acquirer_id"`
		IsActive   *bool  `form:"is_active"`
	}

	AcquirerTerminal struct {
		ID          uint64  `json:"id" validate:"required"`
		Code        string  `json:"code" validate:"required"`
		Name        string  `json:"name" validate:"required"`
		BankID      uint64  `json:"bank_id"`
		Description *string `json:"description"`
		CountryID   uint64  `json:"country_id"`
		CountryName string  `json:"country_name"`
		IsActive    bool    `json:"is_active" validate:"required"`
		Terminals   uint64  `json:"terminals"`
	}
)

func (ac *Acquirer) Validate() error {
	if ac == nil {
		return goerr.ErrParseErrorBody
	}

	validation := validator.New()

	return validation.Struct(ac)
}

func (ac *Acquirer) ToModel() *model.Acquirer {
	if ac == nil {
		return nil
	}

	return &model.Acquirer{
		Code:        ac.Code,
		Name:        ac.Name,
		BankID:      ac.BankID,
		Description: ac.Description,
		CountryID:   ac.CountryID,
	}
}

// NewAcquirerTerminal
// Матчит кол-во терминалов на каждого эквайера
func NewAcquirerTerminal(
	acquirers []*model.Acquirer,
	terminals model.Terminals,
) []AcquirerTerminal {
	result := make([]AcquirerTerminal, len(acquirers))

	for i := range acquirers {
		var count uint64 = 0

		for j := range terminals {
			if acquirers[i].ID == terminals[j].AcquirerID {
				count++

				result[i] = AcquirerTerminal{
					ID:          acquirers[i].ID,
					Code:        acquirers[i].Code,
					Name:        acquirers[i].Name,
					BankID:      acquirers[i].BankID,
					Description: acquirers[i].Description,
					CountryID:   acquirers[i].CountryID,
					CountryName: acquirers[i].CountryName,
					IsActive:    acquirers[i].IsActive,
					Terminals:   count,
				}
			} else {
				result[i] = AcquirerTerminal{
					ID:          acquirers[i].ID,
					Code:        acquirers[i].Code,
					Name:        acquirers[i].Name,
					BankID:      acquirers[i].BankID,
					Description: acquirers[i].Description,
					CountryName: acquirers[i].CountryName,
					CountryID:   acquirers[i].CountryID,
					IsActive:    acquirers[i].IsActive,
					Terminals:   0,
				}
			}
		}
	}

	return result
}
