// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.5
// 	protoc        v5.29.3
// source: proto/enum.proto

package pb

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	descriptorpb "google.golang.org/protobuf/types/descriptorpb"
	reflect "reflect"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

var file_proto_enum_proto_extTypes = []protoimpl.ExtensionInfo{
	{
		ExtendedType:  (*descriptorpb.EnumValueOptions)(nil),
		ExtensionType: (*string)(nil),
		Field:         71000601,
		Name:          "mvp.from_string",
		Tag:           "bytes,71000601,opt,name=from_string",
		Filename:      "proto/enum.proto",
	},
}

// Extension fields to descriptorpb.EnumValueOptions.
var (
	// optional string from_string = 71000601;
	E_FromString = &file_proto_enum_proto_extTypes[0]
)

var File_proto_enum_proto protoreflect.FileDescriptor

var file_proto_enum_proto_rawDesc = string([]byte{
	0x0a, 0x10, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f, 0x65, 0x6e, 0x75, 0x6d, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x12, 0x03, 0x6d, 0x76, 0x70, 0x1a, 0x20, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70,
	0x74, 0x6f, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x3a, 0x45, 0x0a, 0x0b, 0x66, 0x72, 0x6f,
	0x6d, 0x5f, 0x73, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x12, 0x21, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x45, 0x6e, 0x75, 0x6d, 0x56,
	0x61, 0x6c, 0x75, 0x65, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x99, 0xc4, 0xed, 0x21,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x66, 0x72, 0x6f, 0x6d, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67,
	0x42, 0x1c, 0x5a, 0x1a, 0x67, 0x69, 0x74, 0x2e, 0x6c, 0x6f, 0x63, 0x61, 0x6c, 0x2f, 0x73, 0x65,
	0x6e, 0x73, 0x69, 0x74, 0x69, 0x76, 0x65, 0x2f, 0x6d, 0x76, 0x70, 0x2f, 0x70, 0x62, 0x62, 0x08,
	0x65, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x70, 0xe8, 0x07,
})

var file_proto_enum_proto_goTypes = []any{
	(*descriptorpb.EnumValueOptions)(nil), // 0: google.protobuf.EnumValueOptions
}
var file_proto_enum_proto_depIdxs = []int32{
	0, // 0: mvp.from_string:extendee -> google.protobuf.EnumValueOptions
	1, // [1:1] is the sub-list for method output_type
	1, // [1:1] is the sub-list for method input_type
	1, // [1:1] is the sub-list for extension type_name
	0, // [0:1] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_proto_enum_proto_init() }
func file_proto_enum_proto_init() {
	if File_proto_enum_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_proto_enum_proto_rawDesc), len(file_proto_enum_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   0,
			NumExtensions: 1,
			NumServices:   0,
		},
		GoTypes:           file_proto_enum_proto_goTypes,
		DependencyIndexes: file_proto_enum_proto_depIdxs,
		ExtensionInfos:    file_proto_enum_proto_extTypes,
	}.Build()
	File_proto_enum_proto = out.File
	file_proto_enum_proto_goTypes = nil
	file_proto_enum_proto_depIdxs = nil
}
