// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.5
// 	protoc        v5.29.3
// source: inner/processing/grpc/kalkan.proto

package grpc

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// requests
type MakeSignatureRequestV1 struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Payload       *string                `protobuf:"bytes,1,opt,name=payload" json:"payload,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *MakeSignatureRequestV1) Reset() {
	*x = MakeSignatureRequestV1{}
	mi := &file_inner_processing_grpc_kalkan_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *MakeSignatureRequestV1) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MakeSignatureRequestV1) ProtoMessage() {}

func (x *MakeSignatureRequestV1) ProtoReflect() protoreflect.Message {
	mi := &file_inner_processing_grpc_kalkan_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MakeSignatureRequestV1.ProtoReflect.Descriptor instead.
func (*MakeSignatureRequestV1) Descriptor() ([]byte, []int) {
	return file_inner_processing_grpc_kalkan_proto_rawDescGZIP(), []int{0}
}

func (x *MakeSignatureRequestV1) GetPayload() string {
	if x != nil && x.Payload != nil {
		return *x.Payload
	}
	return ""
}

// responses
type MakeSignatureResponseV1 struct {
	state          protoimpl.MessageState                  `protogen:"open.v1"`
	ServerResponse *MakeSignatureResponseV1_ServerResponse `protobuf:"bytes,1,opt,name=server_response,json=serverResponse" json:"server_response,omitempty"`
	Data           *MakeSignatureResponseV1_Data           `protobuf:"bytes,2,opt,name=data" json:"data,omitempty"`
	unknownFields  protoimpl.UnknownFields
	sizeCache      protoimpl.SizeCache
}

func (x *MakeSignatureResponseV1) Reset() {
	*x = MakeSignatureResponseV1{}
	mi := &file_inner_processing_grpc_kalkan_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *MakeSignatureResponseV1) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MakeSignatureResponseV1) ProtoMessage() {}

func (x *MakeSignatureResponseV1) ProtoReflect() protoreflect.Message {
	mi := &file_inner_processing_grpc_kalkan_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MakeSignatureResponseV1.ProtoReflect.Descriptor instead.
func (*MakeSignatureResponseV1) Descriptor() ([]byte, []int) {
	return file_inner_processing_grpc_kalkan_proto_rawDescGZIP(), []int{1}
}

func (x *MakeSignatureResponseV1) GetServerResponse() *MakeSignatureResponseV1_ServerResponse {
	if x != nil {
		return x.ServerResponse
	}
	return nil
}

func (x *MakeSignatureResponseV1) GetData() *MakeSignatureResponseV1_Data {
	if x != nil {
		return x.Data
	}
	return nil
}

type MakeSignatureResponseV1_ServerResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Message       *string                `protobuf:"bytes,1,opt,name=message" json:"message,omitempty"`
	Code          *string                `protobuf:"bytes,2,opt,name=code" json:"code,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *MakeSignatureResponseV1_ServerResponse) Reset() {
	*x = MakeSignatureResponseV1_ServerResponse{}
	mi := &file_inner_processing_grpc_kalkan_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *MakeSignatureResponseV1_ServerResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MakeSignatureResponseV1_ServerResponse) ProtoMessage() {}

func (x *MakeSignatureResponseV1_ServerResponse) ProtoReflect() protoreflect.Message {
	mi := &file_inner_processing_grpc_kalkan_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MakeSignatureResponseV1_ServerResponse.ProtoReflect.Descriptor instead.
func (*MakeSignatureResponseV1_ServerResponse) Descriptor() ([]byte, []int) {
	return file_inner_processing_grpc_kalkan_proto_rawDescGZIP(), []int{1, 0}
}

func (x *MakeSignatureResponseV1_ServerResponse) GetMessage() string {
	if x != nil && x.Message != nil {
		return *x.Message
	}
	return ""
}

func (x *MakeSignatureResponseV1_ServerResponse) GetCode() string {
	if x != nil && x.Code != nil {
		return *x.Code
	}
	return ""
}

type MakeSignatureResponseV1_Data struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Signature     *string                `protobuf:"bytes,1,opt,name=signature" json:"signature,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *MakeSignatureResponseV1_Data) Reset() {
	*x = MakeSignatureResponseV1_Data{}
	mi := &file_inner_processing_grpc_kalkan_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *MakeSignatureResponseV1_Data) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MakeSignatureResponseV1_Data) ProtoMessage() {}

func (x *MakeSignatureResponseV1_Data) ProtoReflect() protoreflect.Message {
	mi := &file_inner_processing_grpc_kalkan_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MakeSignatureResponseV1_Data.ProtoReflect.Descriptor instead.
func (*MakeSignatureResponseV1_Data) Descriptor() ([]byte, []int) {
	return file_inner_processing_grpc_kalkan_proto_rawDescGZIP(), []int{1, 1}
}

func (x *MakeSignatureResponseV1_Data) GetSignature() string {
	if x != nil && x.Signature != nil {
		return *x.Signature
	}
	return ""
}

var File_inner_processing_grpc_kalkan_proto protoreflect.FileDescriptor

var file_inner_processing_grpc_kalkan_proto_rawDesc = string([]byte{
	0x0a, 0x22, 0x69, 0x6e, 0x6e, 0x65, 0x72, 0x2f, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69,
	0x6e, 0x67, 0x2f, 0x67, 0x72, 0x70, 0x63, 0x2f, 0x6b, 0x61, 0x6c, 0x6b, 0x61, 0x6e, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x12, 0x18, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67,
	0x2e, 0x6b, 0x61, 0x6c, 0x6b, 0x61, 0x6e, 0x2e, 0x6b, 0x61, 0x6c, 0x6b, 0x61, 0x6e, 0x22, 0x32,
	0x0a, 0x16, 0x4d, 0x61, 0x6b, 0x65, 0x53, 0x69, 0x67, 0x6e, 0x61, 0x74, 0x75, 0x72, 0x65, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x56, 0x31, 0x12, 0x18, 0x0a, 0x07, 0x70, 0x61, 0x79, 0x6c,
	0x6f, 0x61, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x70, 0x61, 0x79, 0x6c, 0x6f,
	0x61, 0x64, 0x22, 0xb6, 0x02, 0x0a, 0x17, 0x4d, 0x61, 0x6b, 0x65, 0x53, 0x69, 0x67, 0x6e, 0x61,
	0x74, 0x75, 0x72, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x56, 0x31, 0x12, 0x69,
	0x0a, 0x0f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x5f, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x40, 0x2e, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73,
	0x73, 0x69, 0x6e, 0x67, 0x2e, 0x6b, 0x61, 0x6c, 0x6b, 0x61, 0x6e, 0x2e, 0x6b, 0x61, 0x6c, 0x6b,
	0x61, 0x6e, 0x2e, 0x4d, 0x61, 0x6b, 0x65, 0x53, 0x69, 0x67, 0x6e, 0x61, 0x74, 0x75, 0x72, 0x65,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x56, 0x31, 0x2e, 0x53, 0x65, 0x72, 0x76, 0x65,
	0x72, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x52, 0x0e, 0x73, 0x65, 0x72, 0x76, 0x65,
	0x72, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x4a, 0x0a, 0x04, 0x64, 0x61, 0x74,
	0x61, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x36, 0x2e, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73,
	0x73, 0x69, 0x6e, 0x67, 0x2e, 0x6b, 0x61, 0x6c, 0x6b, 0x61, 0x6e, 0x2e, 0x6b, 0x61, 0x6c, 0x6b,
	0x61, 0x6e, 0x2e, 0x4d, 0x61, 0x6b, 0x65, 0x53, 0x69, 0x67, 0x6e, 0x61, 0x74, 0x75, 0x72, 0x65,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x56, 0x31, 0x2e, 0x44, 0x61, 0x74, 0x61, 0x52,
	0x04, 0x64, 0x61, 0x74, 0x61, 0x1a, 0x3e, 0x0a, 0x0e, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x6d, 0x65, 0x73, 0x73, 0x61,
	0x67, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67,
	0x65, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x04, 0x63, 0x6f, 0x64, 0x65, 0x1a, 0x24, 0x0a, 0x04, 0x44, 0x61, 0x74, 0x61, 0x12, 0x1c, 0x0a,
	0x09, 0x73, 0x69, 0x67, 0x6e, 0x61, 0x74, 0x75, 0x72, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x09, 0x73, 0x69, 0x67, 0x6e, 0x61, 0x74, 0x75, 0x72, 0x65, 0x32, 0x82, 0x01, 0x0a, 0x06,
	0x4b, 0x61, 0x6c, 0x6b, 0x61, 0x6e, 0x12, 0x78, 0x0a, 0x0f, 0x4d, 0x61, 0x6b, 0x65, 0x53, 0x69,
	0x67, 0x6e, 0x61, 0x74, 0x75, 0x72, 0x65, 0x56, 0x31, 0x12, 0x30, 0x2e, 0x70, 0x72, 0x6f, 0x63,
	0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x2e, 0x6b, 0x61, 0x6c, 0x6b, 0x61, 0x6e, 0x2e, 0x6b, 0x61,
	0x6c, 0x6b, 0x61, 0x6e, 0x2e, 0x4d, 0x61, 0x6b, 0x65, 0x53, 0x69, 0x67, 0x6e, 0x61, 0x74, 0x75,
	0x72, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x56, 0x31, 0x1a, 0x31, 0x2e, 0x70, 0x72,
	0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x2e, 0x6b, 0x61, 0x6c, 0x6b, 0x61, 0x6e, 0x2e,
	0x6b, 0x61, 0x6c, 0x6b, 0x61, 0x6e, 0x2e, 0x4d, 0x61, 0x6b, 0x65, 0x53, 0x69, 0x67, 0x6e, 0x61,
	0x74, 0x75, 0x72, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x56, 0x31, 0x22, 0x00,
	0x42, 0x2d, 0x5a, 0x2b, 0x67, 0x69, 0x74, 0x2e, 0x6c, 0x6f, 0x63, 0x61, 0x6c, 0x2f, 0x73, 0x65,
	0x6e, 0x73, 0x69, 0x74, 0x69, 0x76, 0x65, 0x2f, 0x69, 0x6e, 0x6e, 0x65, 0x72, 0x70, 0x62, 0x2f,
	0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x2f, 0x67, 0x72, 0x70, 0x63, 0x62,
	0x08, 0x65, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x70, 0xe8, 0x07,
})

var (
	file_inner_processing_grpc_kalkan_proto_rawDescOnce sync.Once
	file_inner_processing_grpc_kalkan_proto_rawDescData []byte
)

func file_inner_processing_grpc_kalkan_proto_rawDescGZIP() []byte {
	file_inner_processing_grpc_kalkan_proto_rawDescOnce.Do(func() {
		file_inner_processing_grpc_kalkan_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_inner_processing_grpc_kalkan_proto_rawDesc), len(file_inner_processing_grpc_kalkan_proto_rawDesc)))
	})
	return file_inner_processing_grpc_kalkan_proto_rawDescData
}

var file_inner_processing_grpc_kalkan_proto_msgTypes = make([]protoimpl.MessageInfo, 4)
var file_inner_processing_grpc_kalkan_proto_goTypes = []any{
	(*MakeSignatureRequestV1)(nil),                 // 0: processing.kalkan.kalkan.MakeSignatureRequestV1
	(*MakeSignatureResponseV1)(nil),                // 1: processing.kalkan.kalkan.MakeSignatureResponseV1
	(*MakeSignatureResponseV1_ServerResponse)(nil), // 2: processing.kalkan.kalkan.MakeSignatureResponseV1.ServerResponse
	(*MakeSignatureResponseV1_Data)(nil),           // 3: processing.kalkan.kalkan.MakeSignatureResponseV1.Data
}
var file_inner_processing_grpc_kalkan_proto_depIdxs = []int32{
	2, // 0: processing.kalkan.kalkan.MakeSignatureResponseV1.server_response:type_name -> processing.kalkan.kalkan.MakeSignatureResponseV1.ServerResponse
	3, // 1: processing.kalkan.kalkan.MakeSignatureResponseV1.data:type_name -> processing.kalkan.kalkan.MakeSignatureResponseV1.Data
	0, // 2: processing.kalkan.kalkan.Kalkan.MakeSignatureV1:input_type -> processing.kalkan.kalkan.MakeSignatureRequestV1
	1, // 3: processing.kalkan.kalkan.Kalkan.MakeSignatureV1:output_type -> processing.kalkan.kalkan.MakeSignatureResponseV1
	3, // [3:4] is the sub-list for method output_type
	2, // [2:3] is the sub-list for method input_type
	2, // [2:2] is the sub-list for extension type_name
	2, // [2:2] is the sub-list for extension extendee
	0, // [0:2] is the sub-list for field type_name
}

func init() { file_inner_processing_grpc_kalkan_proto_init() }
func file_inner_processing_grpc_kalkan_proto_init() {
	if File_inner_processing_grpc_kalkan_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_inner_processing_grpc_kalkan_proto_rawDesc), len(file_inner_processing_grpc_kalkan_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   4,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_inner_processing_grpc_kalkan_proto_goTypes,
		DependencyIndexes: file_inner_processing_grpc_kalkan_proto_depIdxs,
		MessageInfos:      file_inner_processing_grpc_kalkan_proto_msgTypes,
	}.Build()
	File_inner_processing_grpc_kalkan_proto = out.File
	file_inner_processing_grpc_kalkan_proto_goTypes = nil
	file_inner_processing_grpc_kalkan_proto_depIdxs = nil
}
