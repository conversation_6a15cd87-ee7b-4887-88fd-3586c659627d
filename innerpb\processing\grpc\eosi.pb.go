// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.5
// 	protoc        v5.29.3
// source: inner/processing/grpc/eosi.proto

package grpc

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

var File_inner_processing_grpc_eosi_proto protoreflect.FileDescriptor

var file_inner_processing_grpc_eosi_proto_rawDesc = string([]byte{
	0x0a, 0x20, 0x69, 0x6e, 0x6e, 0x65, 0x72, 0x2f, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69,
	0x6e, 0x67, 0x2f, 0x67, 0x72, 0x70, 0x63, 0x2f, 0x65, 0x6f, 0x73, 0x69, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x12, 0x14, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x2e, 0x65,
	0x6f, 0x73, 0x69, 0x2e, 0x65, 0x6f, 0x73, 0x69, 0x1a, 0x29, 0x69, 0x6e, 0x6e, 0x65, 0x72, 0x2f,
	0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x2f, 0x67, 0x72, 0x70, 0x63, 0x2f,
	0x6d, 0x75, 0x6c, 0x74, 0x69, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x32, 0xc6, 0x02, 0x0a, 0x04, 0x45, 0x6f, 0x73, 0x69, 0x12, 0x94, 0x01, 0x0a,
	0x0f, 0x47, 0x65, 0x74, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x4f, 0x72, 0x64, 0x65, 0x72,
	0x12, 0x3e, 0x2e, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x2e, 0x6d, 0x75,
	0x6c, 0x74, 0x69, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x6d, 0x75, 0x6c, 0x74,
	0x69, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x47, 0x65, 0x74, 0x50, 0x61, 0x79,
	0x6d, 0x65, 0x6e, 0x74, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x1a, 0x3f, 0x2e, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x2e, 0x6d, 0x75,
	0x6c, 0x74, 0x69, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x6d, 0x75, 0x6c, 0x74,
	0x69, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x47, 0x65, 0x74, 0x50, 0x61, 0x79,
	0x6d, 0x65, 0x6e, 0x74, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x22, 0x00, 0x12, 0xa6, 0x01, 0x0a, 0x15, 0x47, 0x65, 0x74, 0x4f, 0x72, 0x64, 0x65, 0x72,
	0x69, 0x6e, 0x67, 0x49, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x66, 0x69, 0x65, 0x72, 0x12, 0x44, 0x2e,
	0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x2e, 0x6d, 0x75, 0x6c, 0x74, 0x69,
	0x6f, 0x72, 0x64, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x6d, 0x75, 0x6c, 0x74, 0x69, 0x6f, 0x72,
	0x64, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x47, 0x65, 0x74, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x69,
	0x6e, 0x67, 0x49, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x66, 0x69, 0x65, 0x72, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x1a, 0x45, 0x2e, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67,
	0x2e, 0x6d, 0x75, 0x6c, 0x74, 0x69, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x6d,
	0x75, 0x6c, 0x74, 0x69, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x47, 0x65, 0x74,
	0x4f, 0x72, 0x64, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x49, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x66, 0x69,
	0x65, 0x72, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x42, 0x2d, 0x5a, 0x2b,
	0x67, 0x69, 0x74, 0x2e, 0x6c, 0x6f, 0x63, 0x61, 0x6c, 0x2f, 0x73, 0x65, 0x6e, 0x73, 0x69, 0x74,
	0x69, 0x76, 0x65, 0x2f, 0x69, 0x6e, 0x6e, 0x65, 0x72, 0x70, 0x62, 0x2f, 0x70, 0x72, 0x6f, 0x63,
	0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x2f, 0x67, 0x72, 0x70, 0x63, 0x62, 0x08, 0x65, 0x64, 0x69,
	0x74, 0x69, 0x6f, 0x6e, 0x73, 0x70, 0xe8, 0x07,
})

var file_inner_processing_grpc_eosi_proto_goTypes = []any{
	(*GetPaymentOrderRequest)(nil),        // 0: processing.multiordering.multiordering.GetPaymentOrderRequest
	(*GetOrderingIdentifierRequest)(nil),  // 1: processing.multiordering.multiordering.GetOrderingIdentifierRequest
	(*GetPaymentOrderResponse)(nil),       // 2: processing.multiordering.multiordering.GetPaymentOrderResponse
	(*GetOrderingIdentifierResponse)(nil), // 3: processing.multiordering.multiordering.GetOrderingIdentifierResponse
}
var file_inner_processing_grpc_eosi_proto_depIdxs = []int32{
	0, // 0: processing.eosi.eosi.Eosi.GetPaymentOrder:input_type -> processing.multiordering.multiordering.GetPaymentOrderRequest
	1, // 1: processing.eosi.eosi.Eosi.GetOrderingIdentifier:input_type -> processing.multiordering.multiordering.GetOrderingIdentifierRequest
	2, // 2: processing.eosi.eosi.Eosi.GetPaymentOrder:output_type -> processing.multiordering.multiordering.GetPaymentOrderResponse
	3, // 3: processing.eosi.eosi.Eosi.GetOrderingIdentifier:output_type -> processing.multiordering.multiordering.GetOrderingIdentifierResponse
	2, // [2:4] is the sub-list for method output_type
	0, // [0:2] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_inner_processing_grpc_eosi_proto_init() }
func file_inner_processing_grpc_eosi_proto_init() {
	if File_inner_processing_grpc_eosi_proto != nil {
		return
	}
	file_inner_processing_grpc_multiordering_proto_init()
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_inner_processing_grpc_eosi_proto_rawDesc), len(file_inner_processing_grpc_eosi_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   0,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_inner_processing_grpc_eosi_proto_goTypes,
		DependencyIndexes: file_inner_processing_grpc_eosi_proto_depIdxs,
	}.Build()
	File_inner_processing_grpc_eosi_proto = out.File
	file_inner_processing_grpc_eosi_proto_goTypes = nil
	file_inner_processing_grpc_eosi_proto_depIdxs = nil
}
