// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.5.1
// - protoc             v5.29.3
// source: inner/processing/grpc/fiscalization.proto

package grpc

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.64.0 or later.
const _ = grpc.SupportPackageIsVersion9

const (
	Fiscalization_MakeFiscalizationV1_FullMethodName            = "/processing.fiscalization.fiscalization.Fiscalization/MakeFiscalizationV1"
	Fiscalization_GetFiscalInfoByTransactionIDV1_FullMethodName = "/processing.fiscalization.fiscalization.Fiscalization/GetFiscalInfoByTransactionIDV1"
	Fiscalization_ManageShifts_FullMethodName                   = "/processing.fiscalization.fiscalization.Fiscalization/ManageShifts"
	Fiscalization_FinalizeFiscalizations_FullMethodName         = "/processing.fiscalization.fiscalization.Fiscalization/FinalizeFiscalizations"
)

// FiscalizationClient is the client API for Fiscalization service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type FiscalizationClient interface {
	MakeFiscalizationV1(ctx context.Context, in *MakeFiscalizationRequestV1, opts ...grpc.CallOption) (*emptypb.Empty, error)
	GetFiscalInfoByTransactionIDV1(ctx context.Context, in *GetFiscalInfoByTransactionIDRequestV1, opts ...grpc.CallOption) (*GetFiscalInfoByTransactionIDResponseV1, error)
	// jobs
	ManageShifts(ctx context.Context, in *emptypb.Empty, opts ...grpc.CallOption) (*emptypb.Empty, error)
	FinalizeFiscalizations(ctx context.Context, in *emptypb.Empty, opts ...grpc.CallOption) (*emptypb.Empty, error)
}

type fiscalizationClient struct {
	cc grpc.ClientConnInterface
}

func NewFiscalizationClient(cc grpc.ClientConnInterface) FiscalizationClient {
	return &fiscalizationClient{cc}
}

func (c *fiscalizationClient) MakeFiscalizationV1(ctx context.Context, in *MakeFiscalizationRequestV1, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, Fiscalization_MakeFiscalizationV1_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *fiscalizationClient) GetFiscalInfoByTransactionIDV1(ctx context.Context, in *GetFiscalInfoByTransactionIDRequestV1, opts ...grpc.CallOption) (*GetFiscalInfoByTransactionIDResponseV1, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetFiscalInfoByTransactionIDResponseV1)
	err := c.cc.Invoke(ctx, Fiscalization_GetFiscalInfoByTransactionIDV1_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *fiscalizationClient) ManageShifts(ctx context.Context, in *emptypb.Empty, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, Fiscalization_ManageShifts_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *fiscalizationClient) FinalizeFiscalizations(ctx context.Context, in *emptypb.Empty, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, Fiscalization_FinalizeFiscalizations_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// FiscalizationServer is the server API for Fiscalization service.
// All implementations must embed UnimplementedFiscalizationServer
// for forward compatibility.
type FiscalizationServer interface {
	MakeFiscalizationV1(context.Context, *MakeFiscalizationRequestV1) (*emptypb.Empty, error)
	GetFiscalInfoByTransactionIDV1(context.Context, *GetFiscalInfoByTransactionIDRequestV1) (*GetFiscalInfoByTransactionIDResponseV1, error)
	// jobs
	ManageShifts(context.Context, *emptypb.Empty) (*emptypb.Empty, error)
	FinalizeFiscalizations(context.Context, *emptypb.Empty) (*emptypb.Empty, error)
	mustEmbedUnimplementedFiscalizationServer()
}

// UnimplementedFiscalizationServer must be embedded to have
// forward compatible implementations.
//
// NOTE: this should be embedded by value instead of pointer to avoid a nil
// pointer dereference when methods are called.
type UnimplementedFiscalizationServer struct{}

func (UnimplementedFiscalizationServer) MakeFiscalizationV1(context.Context, *MakeFiscalizationRequestV1) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method MakeFiscalizationV1 not implemented")
}
func (UnimplementedFiscalizationServer) GetFiscalInfoByTransactionIDV1(context.Context, *GetFiscalInfoByTransactionIDRequestV1) (*GetFiscalInfoByTransactionIDResponseV1, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetFiscalInfoByTransactionIDV1 not implemented")
}
func (UnimplementedFiscalizationServer) ManageShifts(context.Context, *emptypb.Empty) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ManageShifts not implemented")
}
func (UnimplementedFiscalizationServer) FinalizeFiscalizations(context.Context, *emptypb.Empty) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method FinalizeFiscalizations not implemented")
}
func (UnimplementedFiscalizationServer) mustEmbedUnimplementedFiscalizationServer() {}
func (UnimplementedFiscalizationServer) testEmbeddedByValue()                       {}

// UnsafeFiscalizationServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to FiscalizationServer will
// result in compilation errors.
type UnsafeFiscalizationServer interface {
	mustEmbedUnimplementedFiscalizationServer()
}

func RegisterFiscalizationServer(s grpc.ServiceRegistrar, srv FiscalizationServer) {
	// If the following call pancis, it indicates UnimplementedFiscalizationServer was
	// embedded by pointer and is nil.  This will cause panics if an
	// unimplemented method is ever invoked, so we test this at initialization
	// time to prevent it from happening at runtime later due to I/O.
	if t, ok := srv.(interface{ testEmbeddedByValue() }); ok {
		t.testEmbeddedByValue()
	}
	s.RegisterService(&Fiscalization_ServiceDesc, srv)
}

func _Fiscalization_MakeFiscalizationV1_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(MakeFiscalizationRequestV1)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(FiscalizationServer).MakeFiscalizationV1(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Fiscalization_MakeFiscalizationV1_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(FiscalizationServer).MakeFiscalizationV1(ctx, req.(*MakeFiscalizationRequestV1))
	}
	return interceptor(ctx, in, info, handler)
}

func _Fiscalization_GetFiscalInfoByTransactionIDV1_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetFiscalInfoByTransactionIDRequestV1)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(FiscalizationServer).GetFiscalInfoByTransactionIDV1(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Fiscalization_GetFiscalInfoByTransactionIDV1_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(FiscalizationServer).GetFiscalInfoByTransactionIDV1(ctx, req.(*GetFiscalInfoByTransactionIDRequestV1))
	}
	return interceptor(ctx, in, info, handler)
}

func _Fiscalization_ManageShifts_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(emptypb.Empty)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(FiscalizationServer).ManageShifts(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Fiscalization_ManageShifts_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(FiscalizationServer).ManageShifts(ctx, req.(*emptypb.Empty))
	}
	return interceptor(ctx, in, info, handler)
}

func _Fiscalization_FinalizeFiscalizations_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(emptypb.Empty)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(FiscalizationServer).FinalizeFiscalizations(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Fiscalization_FinalizeFiscalizations_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(FiscalizationServer).FinalizeFiscalizations(ctx, req.(*emptypb.Empty))
	}
	return interceptor(ctx, in, info, handler)
}

// Fiscalization_ServiceDesc is the grpc.ServiceDesc for Fiscalization service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var Fiscalization_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "processing.fiscalization.fiscalization.Fiscalization",
	HandlerType: (*FiscalizationServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "MakeFiscalizationV1",
			Handler:    _Fiscalization_MakeFiscalizationV1_Handler,
		},
		{
			MethodName: "GetFiscalInfoByTransactionIDV1",
			Handler:    _Fiscalization_GetFiscalInfoByTransactionIDV1_Handler,
		},
		{
			MethodName: "ManageShifts",
			Handler:    _Fiscalization_ManageShifts_Handler,
		},
		{
			MethodName: "FinalizeFiscalizations",
			Handler:    _Fiscalization_FinalizeFiscalizations_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "inner/processing/grpc/fiscalization.proto",
}
