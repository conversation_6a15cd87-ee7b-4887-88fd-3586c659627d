package model

import (
	"sync"
	"time"

	"golang.org/x/crypto/bcrypt"
)

const (
	cardTableName = "card.cards"
	maxRoutines   = 7

	FiveDigitBin  = 5
	SixDigitBin   = 6
	EightDigitBin = 8
)

type Card struct {
	TimestampMixin
	DeleteMixin
	Id            uint64    `gorm:"column:id" json:"id"`
	ClientId      uint64    `gorm:"column:client_id" json:"client_id"`
	IssuerId      uint64    `gorm:"column:issuer_id" json:"issuer_id"`
	IpsId         uint64    `gorm:"column:ips_id" json:"ips_id"`
	CountryId     uint64    `gorm:"column:country_id" json:"country_id"`
	SaveAccess    bool      `gorm:"column:save_access" json:"save_access"`
	Tokens        []Token   `gorm:"foreignKey:CardId"`
	Approved      bool      `gorm:"column:approved" json:"approved"`
	ExpiredAt     time.Time `gorm:"column:expired_at" json:"expired_at"`
	KeyId         uint64    `gorm:"column:key_id" json:"key_id"`
	HashedPan     string    `gorm:"column:hashed_pan" json:"hashed_pan"`
	EncryptMonth  string    `gorm:"column:encrypt_month" json:"encrypt_month"`
	EncryptYear   string    `gorm:"column:encrypt_year" json:"encrypt_year"`
	EncryptName   string    `gorm:"column:encrypt_name" json:"encrypt_name"`
	EncryptPan    string    `gorm:"column:encrypt_pan" json:"encrypt_pan"`
	MaskedPan     string    `gorm:"-" json:"-"`
	Pan           string    `gorm:"-" json:"-"`
	IsPanModified bool      `gorm:"column:is_pan_modified" default:"true" json:"-"`
	IsMaskedPan   *bool     `gorm:"column:is_masked_pan" json:"is_masked_pan"`
	IsExpired     *bool     `gorm:"column:is_expired" json:"is_expired"`
	Transactions  uint64    `gorm:"column:transactions" json:"transactions"`
	HashKeyId     uint64    `gorm:"column:hash_key_id" json:"hash_key_id"`
	HashedPan1    string    `gorm:"column:hashed_pan_1" json:"hashed_pan_1"`
}

type PayCardData struct {
	Month     string `gorm:"column:month" json:"month"`
	Year      string `gorm:"column:year" json:"year"`
	Name      string `gorm:"column:name" json:"name"`
	Pan       string `gorm:"column:pan" json:"pan"`
	IssuerId  uint64 `gorm:"column:issuer_id" json:"issuer_id"`
	IpsId     uint64 `gorm:"column:ips_id" json:"ips_id"`
	CountryId uint64 `gorm:"column:country_id" json:"country_id"`
}

func (c Card) TableName() string {
	return cardTableName // database_schema.table_name
}

type Cards []*Card

func (cs Cards) TableName() string {
	return cardTableName // database_schema.table_name
}

func (cs Cards) FindByPan(pan string) *Card {
	var (
		wg        = &sync.WaitGroup{}
		ch        = make(chan Card, len(cs))
		semaphore = make(chan struct{}, maxRoutines) // set limit for go routines
	)

	for i := range cs {
		wg.Add(1)
		semaphore <- struct{}{} // the loop will wait here for other routines

		go func(pan string, ch chan Card, card Card, wg *sync.WaitGroup) {
			defer wg.Done()
			defer func() {
				<-semaphore // clean an element for the next routine
			}()

			if err := bcrypt.CompareHashAndPassword([]byte(card.HashedPan), []byte(pan)); err != nil {
				return
			}

			ch <- card
		}(pan, ch, *cs[i], wg)
	}

	go func() {
		wg.Wait()
		close(ch)
	}()

	for payInCard := range ch {
		if payInCard.Id != 0 {
			return &payInCard
		}
	}

	return nil
}

// IsExistToken Check if at least one token exists and approved
func (c Card) IsExistToken() bool {
	for _, i := range c.Tokens {
		if i.Id != 0 {
			return true
		}
	}

	return false
}
