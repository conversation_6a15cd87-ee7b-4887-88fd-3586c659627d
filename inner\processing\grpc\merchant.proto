edition = "2023";

package processing.merchant.merchant;

option go_package = "git.local/sensitive/innerpb/processing/grpc";

import "google/protobuf/empty.proto";

service Merchant {
  rpc CheckProjectAuth(CheckProjectAuthRequestV1) returns (CheckProjectAuthResponseV1) {}
  rpc CheckProjectAuthSHA256(CheckProjectAuthSHA256RequestV1) returns (CheckProjectAuthResponseV1) {}
  rpc GetProject(ProjectRequestV1) returns (ProjectResponseV1) {}
  rpc GenerateProjectHash(GenerateProjectHashRequestV1) returns (GenerateProjectHashResponseV1) {}
  rpc GenerateProjectSHA256Hash(GeneratePayloadHashRequestV1) returns (GenerateProjectHashResponseV1) {}
  rpc CheckProject(CheckMerchantProjectRequestV1) returns (google.protobuf.Empty){}
  rpc GetMerchantInfo(GetMerchantInfoRequestV1) returns (GetMerchantInfoResponseV1){}
  rpc GetMerchantProjectsByBin(GetMerchantProjectsByBinRequestV1) returns (GetMerchantProjectsByBinResponseV1){}
  rpc GetMerchantDataByID(GetMerchantDataByIDRequestV1) returns (MerchantData) {}
}

message CheckProjectAuthRequestV1 {
  uint64 merchant_id = 1;
  uint64 project_id = 2;
  string concatinated_secret_param = 3;
  string secret_hash = 4;
}

message CheckProjectAuthSHA256RequestV1 {
  uint64 merchant_id = 1;
  uint64 project_id = 2;
  string payload = 3;
  string hash = 4;
}

message CheckProjectAuthResponseV1 {
  bool is_authenticated = 1;
}

message ProjectRequestV1 {
  uint64 merchant_id = 1;
  uint64 project_id = 2;
}

message ProjectResponseV1 {
  uint64 project_id = 1;
  string name = 2;
  string main_activity_type = 3;
  uint64 merchant_id = 4;
  bool has_multi_card_link = 5;
  MerchantData merchant = 6;
}

message MerchantData {
  uint64 merchant_id = 1;
  string name = 2;
  string email = 3;
  string site_url = 4;
  bool is_active = 5;
  bool is_hold = 6;
  bool is_blocked = 7;
  string description = 8;
  string contacts = 9;
  string logo = 10;
  string bin = 11;
}

message GenerateProjectHashRequestV1 {
  uint64 project_id = 1;
  uint64 merchant_id = 2;
  string ProjectReferenceID = 3;
}

message GeneratePayloadHashRequestV1 {
  uint64 project_id = 1;
  uint64 merchant_id = 2;
  string payload = 3;
}

message GenerateProjectHashResponseV1 {
  string hash = 1;
}

message CheckMerchantProjectRequestV1 {
  uint64 merchant_id = 1;
  uint64 project_id = 2;
}

message GetMerchantInfoRequestV1 {
  uint64 project_id = 1;
}

message GetMerchantInfoResponseV1 {
  uint64 merchant_id = 1;
  string name = 2;
  string company_name = 3;
}

message GetMerchantProjectsByBinRequestV1 {
  string merchant_bin = 1;
}

message GetMerchantProjectsByBinResponseV1 {
  uint64 merchant_id = 1;
  repeated ProjectIDs projects = 2;
}

message ProjectIDs {
  uint64 project_id = 1;
}

message GetMerchantDataByIDRequestV1 {
  uint64 merchant_id = 1;
}
