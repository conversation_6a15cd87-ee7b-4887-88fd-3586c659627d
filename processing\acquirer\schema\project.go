package schema

import (
	gorpc "git.local/sensitive/innerpb/processing/grpc"
	"git.local/sensitive/processing/acquirer/model"
)

func NewModelToProtoResponse(res []model.Terminal) (*gorpc.ActiveTerminalsByProjectResponseV1, error) {
	response := gorpc.ActiveTerminalsByProjectResponseV1{}

	for _, v := range res {
		config, err := v.ToStruct()

		if err != nil {
			return nil, err
		}

		terminalProjects := make([]*gorpc.TerminalProjectV1, len(v.TerminalProjects))

		for i, x := range v.TerminalProjects {
			terminalProjects[i] = &gorpc.TerminalProjectV1{
				Id:                &x.ID,
				ProjectId:         &x.ProjectID,
				TransactionTypeId: &x.TransactionTypeID,
				TerminalId:        &x.TerminalID,
			}
		}

		resp := gorpc.ActiveTerminalsByProjectV1{}
		resp.Id = &v.ID
		resp.TerminalProjects = terminalProjects
		resp.AcquirerId = &v.AcquirerID

		status := int32(v.Status)
		resp.Status = &status

		resp.Config = config

		response.Data = append(response.Data, &resp)
	}

	return &response, nil
}
