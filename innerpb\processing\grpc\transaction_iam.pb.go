// Code generated by protoc-gen-go-mvp. DO NOT EDIT.
// versions:
// - protoc-gen-go-mvp v2.0.1
// created by mvp authors

package grpc

import (
	context "context"
	grpc "google.golang.org/grpc"
	metadata "google.golang.org/grpc/metadata"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
)

func NewIamTransactionServer(
	srv TransactionServer,
) TransactionServer {
	return &iamTransactionServer{
		srv: srv,
	}
}

var _ TransactionServer = (*iamTransactionServer)(nil)

type iamTransactionServer struct {
	UnimplementedTransactionServer

	srv TransactionServer
}

func (s *iamTransactionServer) UpdateTransactionStatus(
	ctx context.Context,
	req *UpdateTransactionStatusRequestV1,
) (
	*emptypb.Empty,
	error,
) {
	return s.srv.UpdateTransactionStatus(ctx, req)
}

func (s *iamTransactionServer) UpdateCallbackStatus(
	ctx context.Context,
	req *UpdateCallbackStatusRequestV1,
) (
	*emptypb.Empty,
	error,
) {
	return s.srv.UpdateCallbackStatus(ctx, req)
}

func (s *iamTransactionServer) GetTransactionsByPeriodAndStatus(
	ctx context.Context,
	req *GetTransactionsByStatusReqV1,
) (
	*GetTransactionsResponse,
	error,
) {
	return s.srv.GetTransactionsByPeriodAndStatus(ctx, req)
}

func (s *iamTransactionServer) GetTransactionsByPeriodAndCallbackStatus(
	ctx context.Context,
	req *GetTransactionsByCallbackStatusReqV1,
) (
	*GetTransactionsResponse,
	error,
) {
	return s.srv.GetTransactionsByPeriodAndCallbackStatus(ctx, req)
}

func (s *iamTransactionServer) GetTransactionsByFinalStatusAndPeriodWithLimit(
	ctx context.Context,
	req *GetTransactionsByFinalStatusAndPeriodWithLimitReqV1,
) (
	*GetTransactionsResponse,
	error,
) {
	return s.srv.GetTransactionsByFinalStatusAndPeriodWithLimit(ctx, req)
}

func (s *iamTransactionServer) GetPayInTransactionsByPeriodAndAcquirer(
	ctx context.Context,
	req *GetPayInTransactionsByPeriodAndAcquirerReqV1,
) (
	*GetTransactionsResponse,
	error,
) {
	return s.srv.GetPayInTransactionsByPeriodAndAcquirer(ctx, req)
}

func (s *iamTransactionServer) GetTransactionTypeByID(
	ctx context.Context,
	req *GetTransactionTypeByIDRequestV1,
) (
	*GetTransactionTypeByIDResponseV1,
	error,
) {
	return s.srv.GetTransactionTypeByID(ctx, req)
}

func (s *iamTransactionServer) GetTransactionByID(
	ctx context.Context,
	req *GetTransactionByIDRequestV1,
) (
	*TransactionDataV1,
	error,
) {
	return s.srv.GetTransactionByID(ctx, req)
}

func (s *iamTransactionServer) MakeAutoCharge(
	ctx context.Context,
	req *MakeAutoChargeRequestV1,
) (
	*emptypb.Empty,
	error,
) {
	return s.srv.MakeAutoCharge(ctx, req)
}

func (s *iamTransactionServer) SetRefundWaitingStatus(
	ctx context.Context,
	req *SetRefundWaitingStatusRequestV1,
) (
	*SetRefundWaitingStatusResponseV1,
	error,
) {
	return s.srv.SetRefundWaitingStatus(ctx, req)
}

func (s *iamTransactionServer) GetTransactionsByProjectInfo(
	ctx context.Context,
	req *GetTransactionsByProjectInfoRequestV1,
) (
	*GetTransactionsResponse,
	error,
) {
	return s.srv.GetTransactionsByProjectInfo(ctx, req)
}

func (s *iamTransactionServer) CheckTransactionHash(
	ctx context.Context,
	req *CheckTransactionHashRequestV1,
) (
	*emptypb.Empty,
	error,
) {
	return s.srv.CheckTransactionHash(ctx, req)
}

func (s *iamTransactionServer) CreateTransactionByPhone(
	ctx context.Context,
	req *CreateTransactionByPhoneRequest,
) (
	*CreateTransactionByPhoneResponse,
	error,
) {
	return s.srv.CreateTransactionByPhone(ctx, req)
}

func (s *iamTransactionServer) GetByIDWithType(
	ctx context.Context,
	req *GetByIDWithTypeRequest,
) (
	*GetByIDWithTypeResponse,
	error,
) {
	return s.srv.GetByIDWithType(ctx, req)
}

func (s *iamTransactionServer) UpdateStatus(
	ctx context.Context,
	req *UpdateStatusRequest,
) (
	*emptypb.Empty,
	error,
) {
	return s.srv.UpdateStatus(ctx, req)
}

func (s *iamTransactionServer) IncreaseTryCount(
	ctx context.Context,
	req *IncreaseTryCountRequest,
) (
	*emptypb.Empty,
	error,
) {
	return s.srv.IncreaseTryCount(ctx, req)
}

func (s *iamTransactionServer) SaveAcquirerResponse(
	ctx context.Context,
	req *SaveAcquirerResponseRequest,
) (
	*emptypb.Empty,
	error,
) {
	return s.srv.SaveAcquirerResponse(ctx, req)
}

func (s *iamTransactionServer) SendReceipt(
	ctx context.Context,
	req *SendRequest,
) (
	*emptypb.Empty,
	error,
) {
	return s.srv.SendReceipt(ctx, req)
}

func (s *iamTransactionServer) SetAdditionalData(
	ctx context.Context,
	req *SetAdditionalDataRequest,
) (
	*emptypb.Empty,
	error,
) {
	return s.srv.SetAdditionalData(ctx, req)
}

func (s *iamTransactionServer) CalculateAndUpdateTransactionAmount(
	ctx context.Context,
	req *CalculateAndUpdateTransactionAmountRequest,
) (
	*emptypb.Empty,
	error,
) {
	return s.srv.CalculateAndUpdateTransactionAmount(ctx, req)
}

func (s *iamTransactionServer) BillPayOut(
	ctx context.Context,
	req *BillPayoutRequest,
) (
	*emptypb.Empty,
	error,
) {
	return s.srv.BillPayOut(ctx, req)
}

func NewIamTransactionClient(
	client TransactionClient,
) TransactionClient {
	return &iamTransactionClient{
		client: client,
	}
}

type iamTransactionClient struct {
	client TransactionClient
}

func (s *iamTransactionClient) UpdateTransactionStatus(
	ctx context.Context,
	req *UpdateTransactionStatusRequestV1,
	opts ...grpc.CallOption,
) (
	*emptypb.Empty,
	error,
) {
	md, _ := metadata.FromIncomingContext(ctx)
	return s.client.UpdateTransactionStatus(metadata.NewOutgoingContext(ctx, md), req)
}

func (s *iamTransactionClient) UpdateCallbackStatus(
	ctx context.Context,
	req *UpdateCallbackStatusRequestV1,
	opts ...grpc.CallOption,
) (
	*emptypb.Empty,
	error,
) {
	md, _ := metadata.FromIncomingContext(ctx)
	return s.client.UpdateCallbackStatus(metadata.NewOutgoingContext(ctx, md), req)
}

func (s *iamTransactionClient) GetTransactionsByPeriodAndStatus(
	ctx context.Context,
	req *GetTransactionsByStatusReqV1,
	opts ...grpc.CallOption,
) (
	*GetTransactionsResponse,
	error,
) {
	md, _ := metadata.FromIncomingContext(ctx)
	return s.client.GetTransactionsByPeriodAndStatus(metadata.NewOutgoingContext(ctx, md), req)
}

func (s *iamTransactionClient) GetTransactionsByPeriodAndCallbackStatus(
	ctx context.Context,
	req *GetTransactionsByCallbackStatusReqV1,
	opts ...grpc.CallOption,
) (
	*GetTransactionsResponse,
	error,
) {
	md, _ := metadata.FromIncomingContext(ctx)
	return s.client.GetTransactionsByPeriodAndCallbackStatus(metadata.NewOutgoingContext(ctx, md), req)
}

func (s *iamTransactionClient) GetTransactionsByFinalStatusAndPeriodWithLimit(
	ctx context.Context,
	req *GetTransactionsByFinalStatusAndPeriodWithLimitReqV1,
	opts ...grpc.CallOption,
) (
	*GetTransactionsResponse,
	error,
) {
	md, _ := metadata.FromIncomingContext(ctx)
	return s.client.GetTransactionsByFinalStatusAndPeriodWithLimit(metadata.NewOutgoingContext(ctx, md), req)
}

func (s *iamTransactionClient) GetPayInTransactionsByPeriodAndAcquirer(
	ctx context.Context,
	req *GetPayInTransactionsByPeriodAndAcquirerReqV1,
	opts ...grpc.CallOption,
) (
	*GetTransactionsResponse,
	error,
) {
	md, _ := metadata.FromIncomingContext(ctx)
	return s.client.GetPayInTransactionsByPeriodAndAcquirer(metadata.NewOutgoingContext(ctx, md), req)
}

func (s *iamTransactionClient) GetTransactionTypeByID(
	ctx context.Context,
	req *GetTransactionTypeByIDRequestV1,
	opts ...grpc.CallOption,
) (
	*GetTransactionTypeByIDResponseV1,
	error,
) {
	md, _ := metadata.FromIncomingContext(ctx)
	return s.client.GetTransactionTypeByID(metadata.NewOutgoingContext(ctx, md), req)
}

func (s *iamTransactionClient) GetTransactionByID(
	ctx context.Context,
	req *GetTransactionByIDRequestV1,
	opts ...grpc.CallOption,
) (
	*TransactionDataV1,
	error,
) {
	md, _ := metadata.FromIncomingContext(ctx)
	return s.client.GetTransactionByID(metadata.NewOutgoingContext(ctx, md), req)
}

func (s *iamTransactionClient) MakeAutoCharge(
	ctx context.Context,
	req *MakeAutoChargeRequestV1,
	opts ...grpc.CallOption,
) (
	*emptypb.Empty,
	error,
) {
	md, _ := metadata.FromIncomingContext(ctx)
	return s.client.MakeAutoCharge(metadata.NewOutgoingContext(ctx, md), req)
}

func (s *iamTransactionClient) SetRefundWaitingStatus(
	ctx context.Context,
	req *SetRefundWaitingStatusRequestV1,
	opts ...grpc.CallOption,
) (
	*SetRefundWaitingStatusResponseV1,
	error,
) {
	md, _ := metadata.FromIncomingContext(ctx)
	return s.client.SetRefundWaitingStatus(metadata.NewOutgoingContext(ctx, md), req)
}

func (s *iamTransactionClient) GetTransactionsByProjectInfo(
	ctx context.Context,
	req *GetTransactionsByProjectInfoRequestV1,
	opts ...grpc.CallOption,
) (
	*GetTransactionsResponse,
	error,
) {
	md, _ := metadata.FromIncomingContext(ctx)
	return s.client.GetTransactionsByProjectInfo(metadata.NewOutgoingContext(ctx, md), req)
}

func (s *iamTransactionClient) CheckTransactionHash(
	ctx context.Context,
	req *CheckTransactionHashRequestV1,
	opts ...grpc.CallOption,
) (
	*emptypb.Empty,
	error,
) {
	md, _ := metadata.FromIncomingContext(ctx)
	return s.client.CheckTransactionHash(metadata.NewOutgoingContext(ctx, md), req)
}

func (s *iamTransactionClient) CreateTransactionByPhone(
	ctx context.Context,
	req *CreateTransactionByPhoneRequest,
	opts ...grpc.CallOption,
) (
	*CreateTransactionByPhoneResponse,
	error,
) {
	md, _ := metadata.FromIncomingContext(ctx)
	return s.client.CreateTransactionByPhone(metadata.NewOutgoingContext(ctx, md), req)
}

func (s *iamTransactionClient) GetByIDWithType(
	ctx context.Context,
	req *GetByIDWithTypeRequest,
	opts ...grpc.CallOption,
) (
	*GetByIDWithTypeResponse,
	error,
) {
	md, _ := metadata.FromIncomingContext(ctx)
	return s.client.GetByIDWithType(metadata.NewOutgoingContext(ctx, md), req)
}

func (s *iamTransactionClient) UpdateStatus(
	ctx context.Context,
	req *UpdateStatusRequest,
	opts ...grpc.CallOption,
) (
	*emptypb.Empty,
	error,
) {
	md, _ := metadata.FromIncomingContext(ctx)
	return s.client.UpdateStatus(metadata.NewOutgoingContext(ctx, md), req)
}

func (s *iamTransactionClient) IncreaseTryCount(
	ctx context.Context,
	req *IncreaseTryCountRequest,
	opts ...grpc.CallOption,
) (
	*emptypb.Empty,
	error,
) {
	md, _ := metadata.FromIncomingContext(ctx)
	return s.client.IncreaseTryCount(metadata.NewOutgoingContext(ctx, md), req)
}

func (s *iamTransactionClient) SaveAcquirerResponse(
	ctx context.Context,
	req *SaveAcquirerResponseRequest,
	opts ...grpc.CallOption,
) (
	*emptypb.Empty,
	error,
) {
	md, _ := metadata.FromIncomingContext(ctx)
	return s.client.SaveAcquirerResponse(metadata.NewOutgoingContext(ctx, md), req)
}

func (s *iamTransactionClient) SendReceipt(
	ctx context.Context,
	req *SendRequest,
	opts ...grpc.CallOption,
) (
	*emptypb.Empty,
	error,
) {
	md, _ := metadata.FromIncomingContext(ctx)
	return s.client.SendReceipt(metadata.NewOutgoingContext(ctx, md), req)
}

func (s *iamTransactionClient) SetAdditionalData(
	ctx context.Context,
	req *SetAdditionalDataRequest,
	opts ...grpc.CallOption,
) (
	*emptypb.Empty,
	error,
) {
	md, _ := metadata.FromIncomingContext(ctx)
	return s.client.SetAdditionalData(metadata.NewOutgoingContext(ctx, md), req)
}

func (s *iamTransactionClient) CalculateAndUpdateTransactionAmount(
	ctx context.Context,
	req *CalculateAndUpdateTransactionAmountRequest,
	opts ...grpc.CallOption,
) (
	*emptypb.Empty,
	error,
) {
	md, _ := metadata.FromIncomingContext(ctx)
	return s.client.CalculateAndUpdateTransactionAmount(metadata.NewOutgoingContext(ctx, md), req)
}

func (s *iamTransactionClient) BillPayOut(
	ctx context.Context,
	req *BillPayoutRequest,
	opts ...grpc.CallOption,
) (
	*emptypb.Empty,
	error,
) {
	md, _ := metadata.FromIncomingContext(ctx)
	return s.client.BillPayOut(metadata.NewOutgoingContext(ctx, md), req)
}
