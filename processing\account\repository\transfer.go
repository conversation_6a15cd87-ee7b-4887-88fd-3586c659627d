package repository

import (
	"context"
	"errors"
	"fmt"
	"git.local/sensitive/innerpb/processing/goerr"
	"git.local/sensitive/processing/account/model"
	"git.local/sensitive/processing/account/schema"
	"git.local/sensitive/sdk/dog"
	"github.com/google/uuid"
	"gorm.io/gorm"
	"strconv"
	"time"
)

type TransferDB struct {
	db *gorm.DB
}

func NewTransferDB(db *gorm.DB) Transferer {
	return &TransferDB{
		db: db,
	}
}

func (td *TransferDB) GetByID(ctx context.Context, id uint64) (_ *model.Transfer, err error) {
	ctx, span := dog.CreateSpan(ctx, "TransferDB_GetByID")
	defer span.End()

	transfer := new(model.Transfer)

	if err = td.db.WithContext(ctx).
		Where("id = ?", id).
		Preload("TransferStatus").
		Preload("TransferType").
		First(transfer).
		Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, goerr.ErrTransferNotFound.WithCtx(ctx)
		}

		return nil, goerr.ErrDbUnexpected.WithErr(err).WithCtx(ctx)
	}

	return transfer, nil
}

func (td *TransferDB) GetByForeignID(ctx context.Context, foreignID uuid.UUID) (*model.Transfer, error) {
	ctx, span := dog.CreateSpan(ctx, "TransferDB_GetByForeignID")
	defer span.End()

	transfer := new(model.Transfer)

	err := td.db.WithContext(ctx).
		Where("foreign_id = ?", foreignID).
		Preload("TransferStatus").
		Preload("TransferType").
		First(transfer).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, goerr.ErrTransferNotFound.WithCtx(ctx)
		}

		return nil, goerr.ErrDbUnexpected.WithErr(err).WithCtx(ctx)
	}

	return transfer, nil
}

func (td *TransferDB) GetByFilters(ctx context.Context, filters schema.TransferFilters) (_ []model.Transfer, err error) {
	ctx, span := dog.CreateSpan(ctx, "TransferDB_GetByFilters")
	defer span.End()

	transferList := make([]model.Transfer, 0)

	tx := td.db.WithContext(ctx).Model(&transferList)
	if err = setFilters(tx, filters); err != nil {
		return nil, err
	}

	if err = tx.
		Model(&transferList).
		Find(&transferList).Error; err != nil {
		return nil, goerr.ErrDbUnexpected.WithErr(err).WithCtx(ctx)
	}

	return transferList, nil
}

func (td *TransferDB) GetByMerchantID(ctx context.Context, merchantID uint64) (_ []model.Transfer, err error) {
	ctx, span := dog.CreateSpan(ctx, "TransferDB_GetByMerchantID")
	defer span.End()

	transferList := make([]model.Transfer, 0)

	if err = td.db.WithContext(ctx).
		Where(`merchant_id = ?`, merchantID).
		Preload(`TransferStatus`).
		Preload(`TransferType`).
		Find(&transferList).Error; err != nil {
		return nil, goerr.ErrDbUnexpected.WithErr(err).WithCtx(ctx)
	}

	return transferList, nil
}

func (td *TransferDB) GetByProjectID(ctx context.Context, projectID uint64) (_ []model.Transfer, err error) {
	ctx, span := dog.CreateSpan(ctx, "TransferDB_GetByProjectID")
	defer span.End()

	transferList := make([]model.Transfer, 0)

	if err = td.db.WithContext(ctx).
		Where(`project_id = ?`, projectID).
		Preload(`TransferStatus`).
		Preload(`TransferType`).
		Find(&transferList).Error; err != nil {
		return nil, goerr.ErrDbUnexpected.WithErr(err).WithCtx(ctx)
	}

	return transferList, nil
}

func (td *TransferDB) GetByIDs(ctx context.Context, ids []uint64) ([]model.Transfer, error) {
	ctx, span := dog.CreateSpan(ctx, "TransferDB_GetByIDs")
	defer span.End()

	transferList := make([]model.Transfer, 0)

	err := td.db.WithContext(ctx).
		Where("id in (?)", ids).
		Preload(`TransferStatus`).
		Preload(`TransferType`).
		Find(&transferList).Error
	if err != nil {
		return nil, goerr.ErrDbUnexpected.WithErr(err).WithCtx(ctx)
	}

	return transferList, nil
}

func (td *TransferDB) GetByForeignIDs(ctx context.Context, foreignIDs []uuid.UUID) ([]model.Transfer, error) {
	ctx, span := dog.CreateSpan(ctx, "TransferDB_GetByForeignIDs")
	defer span.End()

	transferList := make([]model.Transfer, 0)

	err := td.db.WithContext(ctx).
		Where("foreign_id in (?)", foreignIDs).
		Preload(`TransferStatus`).
		Preload(`TransferType`).
		Find(&transferList).Error
	if err != nil {
		return nil, goerr.ErrDbUnexpected.WithErr(err).WithCtx(ctx)
	}

	return transferList, nil
}

func (td *TransferDB) ExtractFailOrCanceledTransfersByForeignIDs(ctx context.Context, foreignIDs []uuid.UUID) ([]model.Transfer, error) {
	ctx, span := dog.CreateSpan(ctx, "TransferDB_ExtractFailOrCanceledTransfersByForeignIDs")
	defer span.End()

	statusFail := new(model.TransferStatus)
	err := td.db.WithContext(ctx).
		Where("code = ?", model.StatusFailed).
		First(statusFail).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, goerr.ErrStatusNotFound.WithCtx(ctx)
		}

		return nil, goerr.ErrDbUnexpected.WithErr(err).WithCtx(ctx)
	}

	statusCanceled := new(model.TransferStatus)
	err = td.db.WithContext(ctx).
		Where("code = ?", model.StatusCanceled).
		First(statusCanceled).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, goerr.ErrStatusNotFound.WithCtx(ctx)
		}

		return nil, goerr.ErrDbUnexpected.WithErr(err).WithCtx(ctx)
	}

	transferList := make([]model.Transfer, 0)
	err = td.db.WithContext(ctx).
		Where("foreign_id in (?)", foreignIDs).
		Where("status_id in (?)", []uint64{statusFail.ID, statusCanceled.ID}).
		Preload(`TransferStatus`).
		Preload(`TransferType`).
		Find(&transferList).Error
	if err != nil {
		return nil, goerr.ErrDbUnexpected.WithErr(err).WithCtx(ctx)
	}

	return transferList, nil
}

func (td *TransferDB) ExtractSuccessTransfersByForeignIDs(ctx context.Context, foreignIDs []uuid.UUID) ([]model.Transfer, error) {
	ctx, span := dog.CreateSpan(ctx, "TransferDB_ExtractSuccessTransfersByForeignIDs")
	defer span.End()

	statusFail := new(model.TransferStatus)
	err := td.db.WithContext(ctx).
		Where("code = ?", model.StatusSuccess).
		First(statusFail).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, goerr.ErrStatusNotFound.WithCtx(ctx)
		}

		return nil, goerr.ErrDbUnexpected.WithErr(err).WithCtx(ctx)
	}

	transferList := make([]model.Transfer, 0)
	err = td.db.WithContext(ctx).
		Where("foreign_id in (?)", foreignIDs).
		Where("status_id = ?", statusFail.ID).
		Preload(`TransferStatus`).
		Preload(`TransferType`).
		Find(&transferList).Error
	if err != nil {
		return nil, goerr.ErrDbUnexpected.WithErr(err).WithCtx(ctx)
	}

	return transferList, nil
}

func setFilters(tx *gorm.DB, filters schema.TransferFilters) error {
	if filters.MerchantID != "" {
		tx.Where(`merchant_id = ?`, filters.MerchantID)
	}

	if filters.ProjectID != "" {
		tx.Where(`project_id = ?`, filters.ProjectID)
	}

	if filters.StatusID != "" {
		tx.Where(`status_id = ?`, filters.StatusID)
	}

	if filters.AmountFrom != "" {
		amountFrom, err := strconv.ParseFloat(filters.AmountFrom, 64)
		if err != nil {
			return fmt.Errorf("invalid float number %s", filters.AmountFrom)
		}

		tx.Where(`amount >= ?`, amountFrom)
	}

	if filters.AmountTo != "" {
		amountTo, err := strconv.ParseFloat(filters.AmountTo, 64)
		if err != nil {
			return fmt.Errorf("invalid float number %s", filters.AmountTo)
		}

		tx.Where(`amount <= ?`, amountTo)
	}

	if filters.DateFrom != "" {
		dateFrom, err := time.Parse("2006-01-02 15:04:05", filters.DateFrom)
		if err != nil {
			return fmt.Errorf("invalid datetime parameter %s", filters.DateFrom)
		}

		tx.Where(`account.transfers.created_at >= ?`, dateFrom)
	}

	if filters.DateTo != "" {
		dateTo, err := time.Parse("2006-01-02 15:04:05", filters.DateTo)
		if err != nil {
			return fmt.Errorf("invalid datetime parameter %s", filters.DateTo)
		}

		tx.Where(`account.transfers.created_at <= ?`, dateTo)
	}

	if filters.AccountNumber != "" {
		tx.Joins(`
			JOIN account.accounts
			ON account.accounts.id = account.transfers.account_id
		`).
			Where("account.accounts.number = ?", filters.AccountNumber)
	}

	if filters.TypeCode != "" {
		tx.Joins(`
			JOIN account.transfer_types
			ON account.transfer_types.id = account.transfers.transfer_type_id
		`).
			Where("account.transfer_types.code = ?", filters.TypeCode)
	}

	return nil
}

func (td *TransferDB) GetByPeriod(
	ctx context.Context,
	createdDateFrom,
	createdDateTo time.Time,
	statusID uint64,
) (_ []model.Transfer, err error) {
	ctx, span := dog.CreateSpan(ctx, "TransferDB_GetByPeriod")
	defer span.End()

	transferList := make([]model.Transfer, 0)

	if err := td.db.
		WithContext(ctx).
		Preload("TransferType").
		Where("created_at BETWEEN ? AND ? AND status_id = ?", createdDateFrom, createdDateTo, statusID).
		Order("created_at").
		Find(&transferList).Error; err != nil {
		return nil, goerr.ErrDbUnexpected.WithErr(err).WithCtx(ctx)
	}

	return transferList, err
}
