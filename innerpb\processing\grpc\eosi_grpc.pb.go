// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.5.1
// - protoc             v5.29.3
// source: inner/processing/grpc/eosi.proto

package grpc

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.64.0 or later.
const _ = grpc.SupportPackageIsVersion9

const (
	Eosi_GetPaymentOrder_FullMethodName       = "/processing.eosi.eosi.Eosi/GetPaymentOrder"
	Eosi_GetOrderingIdentifier_FullMethodName = "/processing.eosi.eosi.Eosi/GetOrderingIdentifier"
)

// EosiClient is the client API for Eosi service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type EosiClient interface {
	GetPaymentOrder(ctx context.Context, in *GetPaymentOrderRequest, opts ...grpc.CallOption) (*GetPaymentOrderResponse, error)
	GetOrderingIdentifier(ctx context.Context, in *GetOrderingIdentifierRequest, opts ...grpc.CallOption) (*GetOrderingIdentifierResponse, error)
}

type eosiClient struct {
	cc grpc.ClientConnInterface
}

func NewEosiClient(cc grpc.ClientConnInterface) EosiClient {
	return &eosiClient{cc}
}

func (c *eosiClient) GetPaymentOrder(ctx context.Context, in *GetPaymentOrderRequest, opts ...grpc.CallOption) (*GetPaymentOrderResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetPaymentOrderResponse)
	err := c.cc.Invoke(ctx, Eosi_GetPaymentOrder_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *eosiClient) GetOrderingIdentifier(ctx context.Context, in *GetOrderingIdentifierRequest, opts ...grpc.CallOption) (*GetOrderingIdentifierResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetOrderingIdentifierResponse)
	err := c.cc.Invoke(ctx, Eosi_GetOrderingIdentifier_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// EosiServer is the server API for Eosi service.
// All implementations must embed UnimplementedEosiServer
// for forward compatibility.
type EosiServer interface {
	GetPaymentOrder(context.Context, *GetPaymentOrderRequest) (*GetPaymentOrderResponse, error)
	GetOrderingIdentifier(context.Context, *GetOrderingIdentifierRequest) (*GetOrderingIdentifierResponse, error)
	mustEmbedUnimplementedEosiServer()
}

// UnimplementedEosiServer must be embedded to have
// forward compatible implementations.
//
// NOTE: this should be embedded by value instead of pointer to avoid a nil
// pointer dereference when methods are called.
type UnimplementedEosiServer struct{}

func (UnimplementedEosiServer) GetPaymentOrder(context.Context, *GetPaymentOrderRequest) (*GetPaymentOrderResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetPaymentOrder not implemented")
}
func (UnimplementedEosiServer) GetOrderingIdentifier(context.Context, *GetOrderingIdentifierRequest) (*GetOrderingIdentifierResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetOrderingIdentifier not implemented")
}
func (UnimplementedEosiServer) mustEmbedUnimplementedEosiServer() {}
func (UnimplementedEosiServer) testEmbeddedByValue()              {}

// UnsafeEosiServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to EosiServer will
// result in compilation errors.
type UnsafeEosiServer interface {
	mustEmbedUnimplementedEosiServer()
}

func RegisterEosiServer(s grpc.ServiceRegistrar, srv EosiServer) {
	// If the following call pancis, it indicates UnimplementedEosiServer was
	// embedded by pointer and is nil.  This will cause panics if an
	// unimplemented method is ever invoked, so we test this at initialization
	// time to prevent it from happening at runtime later due to I/O.
	if t, ok := srv.(interface{ testEmbeddedByValue() }); ok {
		t.testEmbeddedByValue()
	}
	s.RegisterService(&Eosi_ServiceDesc, srv)
}

func _Eosi_GetPaymentOrder_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetPaymentOrderRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EosiServer).GetPaymentOrder(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Eosi_GetPaymentOrder_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EosiServer).GetPaymentOrder(ctx, req.(*GetPaymentOrderRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Eosi_GetOrderingIdentifier_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetOrderingIdentifierRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EosiServer).GetOrderingIdentifier(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Eosi_GetOrderingIdentifier_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EosiServer).GetOrderingIdentifier(ctx, req.(*GetOrderingIdentifierRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// Eosi_ServiceDesc is the grpc.ServiceDesc for Eosi service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var Eosi_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "processing.eosi.eosi.Eosi",
	HandlerType: (*EosiServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetPaymentOrder",
			Handler:    _Eosi_GetPaymentOrder_Handler,
		},
		{
			MethodName: "GetOrderingIdentifier",
			Handler:    _Eosi_GetOrderingIdentifier_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "inner/processing/grpc/eosi.proto",
}
