package v1

import (
	"git.local/sensitive/innerpb/processing/goerr"
	"git.local/sensitive/pkg/middlewares"
	"git.local/sensitive/processing/acquirer/schema"
	"git.local/sensitive/sdk/dog"
	"github.com/gin-gonic/gin"
	"strconv"
)

func (h *Handler) initTerminalProjectsHandler(v1 *gin.RouterGroup) {
	v1.POST("/terminal-projects", middlewares.GinErrorHandle(h.CreateTerminalProject))
	v1.PUT("/terminal-projects/:id", middlewares.GinErrorHandle(h.UpdateTerminalProject))
	v1.PUT("/terminal-projects/:id/status", middlewares.GinErrorHandle(h.UpdateTerminalProjectStatus))
	v1.GET("/terminal-projects/projects/:project_id/pay-in", middlewares.GinErrorHandle(h.GetPayInProjectTerminals))
}

// UpdateTerminalProjectStatus
// @Summary Изменение статус проекта терминала
// @Accept json
// @Produce json
// @Param id path int true "Terminal Project ID"
// @Param data body schema.UpdateTerminalProjectStatusRequest true "TerminalProject Data"
// @Success 200 {object} middlewares.Response[middlewares.Empty]
// @Failure 400 {object} middlewares.Response[middlewares.Empty]
// @Security bearerAuth
// @tags terminal-project
// @Router /api/v1/terminal-projects/{id}/status [put]
func (h *Handler) UpdateTerminalProjectStatus(c *gin.Context) (err error) {
	ctx, span := dog.CreateSpan(c.Request.Context(), "Handler_UpdateTerminalProject")
	defer span.End()

	id, err := strconv.ParseUint(c.Param("id"), 10, 64)
	if err != nil {
		return goerr.ErrInvalidPathParam.WithErr(err).WithCtx(ctx).WithResult(map[string]string{
			"param":   "id",
			"message": "must be number",
		})
	}

	var request schema.UpdateTerminalProjectStatusRequest

	if err = c.ShouldBind(&request); err != nil {
		return goerr.ErrRequestValidation.WithErr(err).WithResult(map[string]string{
			"error": err.Error(),
		})
	}

	err = h.services.TerminalProject.UpdateStatus(ctx, id, request)
	if err != nil {
		return err
	}

	return middlewares.Respond("success", c)
}

// UpdateTerminalProject
// @Summary Изменение проекта терминала
// @Accept json
// @Produce json
// @Param id path int true "Terminal Project ID"
// @Param data body schema.TerminalProjectRequest true "TerminalProject Data"
// @Success 200 {object} middlewares.Response[middlewares.Empty]
// @Failure 400 {object} middlewares.Response[middlewares.Empty]
// @Security bearerAuth
// @tags terminal-project
// @Router /api/v1/terminal-projects/{id} [put]
func (h *Handler) UpdateTerminalProject(c *gin.Context) (err error) {
	ctx, span := dog.CreateSpan(c.Request.Context(), "Handler_UpdateTerminalProject")
	defer span.End()

	id, err := strconv.ParseUint(c.Param("id"), 10, 64)
	if err != nil {
		return goerr.ErrInvalidPathParam.WithErr(err).WithCtx(ctx).WithResult(map[string]string{
			"param":   "id",
			"message": "must be number",
		})
	}

	var request schema.UpdateTerminalProjectRequest

	if err = c.ShouldBind(&request); err != nil {
		return goerr.ErrRequestValidation.WithErr(err).WithResult(map[string]string{
			"error": err.Error(),
		})
	}

	err = h.services.TerminalProject.Update(ctx, id, request)
	if err != nil {
		return err
	}

	return middlewares.Respond("success", c)
}

// CreateTerminalProject
// WhoAmi godoc
// @Summary Создание проект терминала
// @Accept json
// @Produce json
// @Param data body schema.TerminalProjectRequest true "TerminalProject Data"
// @Success 200 {object} middlewares.Response[schema.TerminalResponse]
// @Failure 400 {object} middlewares.Response[middlewares.Empty]
// @Security bearerAuth
// @tags terminal-project
// @Router /api/v1/terminal-projects [post]
func (h *Handler) CreateTerminalProject(c *gin.Context) (err error) {
	ctx, span := dog.CreateSpan(c.Request.Context(), "Handler_CreateTerminalProject")
	defer span.End()

	var request schema.TerminalProjectRequest

	if err = c.ShouldBind(&request); err != nil {
		return goerr.ErrRequestValidation.WithErr(err).WithResult(map[string]string{
			"error": err.Error(),
		})
	}

	if err = request.Validate(); err != nil {
		return goerr.ErrRequestValidation.WithErr(err).WithResult(map[string]string{
			"error": err.Error(),
		})
	}

	err = h.services.TerminalProject.Create(ctx, request)
	if err != nil {
		return err
	}

	return middlewares.Respond("success", c)
}

// GetPayInProjectTerminals
// WhoAmi godoc
// @Summary Получение терминалов по типу "прием"
// @Accept json
// @Produce json
// @Param project_id path int true "Terminal Project ID"
// @Success 200 {object} middlewares.Response[[]schema.GetTerminalProjectPayIn]
// @Failure 400 {object} middlewares.Response[middlewares.Empty]
// @Security bearerAuth
// @tags terminal-project
// @Router /api/v1/terminal-projects/projects/{project_id}/pay-in [get]
func (h *Handler) GetPayInProjectTerminals(c *gin.Context) error {
	ctx, span := dog.CreateSpan(c.Request.Context(), "Handler_GetPayInProjectTerminals")
	defer span.End()

	id, err := strconv.ParseUint(c.Param("project_id"), 10, 64)
	if err != nil {
		return goerr.ErrInvalidPathParam.WithErr(err).WithCtx(ctx).WithResult(map[string]string{
			"param":   "project_id",
			"message": "must be number",
		})
	}

	resp, err := h.services.TerminalProject.GetPayInProjectTerminals(ctx, id)
	if err != nil {
		return err
	}

	return middlewares.Respond(resp, c)
}
