package service

import (
	"context"
	"encoding/json"
	"git.local/sensitive/innerpb/processing/goerr"
	"git.local/sensitive/processing/acquirer/repository/database"
	"git.local/sensitive/processing/acquirer/schema"
	"git.local/sensitive/sdk/dog"
)

type TerminalConfigService struct {
	terminalRepo      database.Terminaler
	terminalBasicRepo database.TerminalBasicer
}

func NewTerminalConfigService(
	terminalRepo database.Terminaler,
	terminalBasicRepo database.TerminalBasicer,
) TerminalConfiger {
	return &TerminalConfigService{
		terminalRepo:      terminalRepo,
		terminalBasicRepo: terminalBasicRepo,
	}
}

func (t TerminalConfigService) UpdateConfig(ctx context.Context, id uint64, config map[string]any) (err error) {
	ctx, span := dog.CreateSpan(ctx, "TerminalConfigService_UpdateConfig")
	defer span.End()

	jsonConfig, err := json.Marshal(config)
	if err != nil {
		return goerr.ErrTerminalConfigEncryption.WithErr(err).WithCtx(ctx)
	}

	encryptedConfig, err := dog.AESEncrypt(string(jsonConfig), dog.ConfigString("SYMMETRIC_KEY"))
	if err != nil {
		return goerr.ErrTerminalConfigEncryption.WithErr(err).WithCtx(ctx)
	}

	return t.terminalRepo.UpdateConfig(ctx, id, encryptedConfig)
}

func (t TerminalConfigService) GetConfig(
	ctx context.Context,
	id uint64,
) (decryptedConfig schema.DecryptedConfig, err error) {
	ctx, span := dog.CreateSpan(ctx, "TerminalConfigService_GetConfig")
	defer span.End()

	terminal, err := t.terminalBasicRepo.GetAllInfoByID(ctx, id)
	if err != nil {
		return nil, err
	}

	decrypted, err := dog.AESDecrypt(terminal.EncryptedConfig, dog.ConfigString("SYMMETRIC_KEY"))
	if err != nil {
		return nil, err
	}

	if err = json.Unmarshal([]byte(decrypted), &decryptedConfig); err != nil {
		return nil, err
	}

	return decryptedConfig, nil
}
