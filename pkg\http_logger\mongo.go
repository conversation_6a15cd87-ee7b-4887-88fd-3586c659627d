package http_logger

import (
	"context"
	"net/http"
	"time"

	"go.mongodb.org/mongo-driver/mongo"
	"go.uber.org/zap"

	"git.local/sensitive/sdk/dog"
)

type HttpLogMongo struct {
	db *mongo.Collection
}

func NewHttpLogMongo(database *mongo.Database, collection string) HttpLogger {
	db := database.Collection(collection)
	return HttpLogMongo{
		db: db,
	}
}

func (tm HttpLogMongo) Infow(
	ctx context.Context,
	res *http.Response,
	keyAndValues ...interface{},
) {
	fields := sweetenFields(keyAndValues).toMap()

	httpLog := parseResponse(res)
	if httpLog == nil {
		httpLog = &HttpLog{}
	}

	httpLog.OptionalData = fields
	httpLog.CtxData = nil

	if res != nil {
		httpLog.HeaderRes = res.Header
		httpLog.HeaderReq = res.Request.Header
		httpLog.HttpStatus = res.StatusCode
	}

	httpLog.CreatedAt = time.Now()

	_, err := tm.db.InsertOne(ctx, httpLog)
	if err != nil {
		dog.L().Error("Mongo: InsertOne error", zap.Error(err))
		return
	}
}
