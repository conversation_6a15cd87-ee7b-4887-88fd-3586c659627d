// Code generated by MockGen. DO NOT EDIT.
// Source: splitting.gin.pb.go

// Package grpcmock is a generated GoMock package.
package grpcmock

import (
	reflect "reflect"

	gin "github.com/gin-gonic/gin"
	gomock "github.com/golang/mock/gomock"
)

// MockGinSplittingServer is a mock of GinSplittingServer interface.
type MockGinSplittingServer struct {
	ctrl     *gomock.Controller
	recorder *MockGinSplittingServerMockRecorder
}

// MockGinSplittingServerMockRecorder is the mock recorder for MockGinSplittingServer.
type MockGinSplittingServerMockRecorder struct {
	mock *MockGinSplittingServer
}

// NewMockGinSplittingServer creates a new mock instance.
func NewMockGinSplittingServer(ctrl *gomock.Controller) *MockGinSplittingServer {
	mock := &MockGinSplittingServer{ctrl: ctrl}
	mock.recorder = &MockGinSplittingServerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockGinSplittingServer) EXPECT() *MockGinSplittingServerMockRecorder {
	return m.recorder
}

// CalculatePaymentSplitTax mocks base method.
func (m *MockGinSplittingServer) CalculatePaymentSplitTax(c *gin.Context) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CalculatePaymentSplitTax", c)
	ret0, _ := ret[0].(error)
	return ret0
}

// CalculatePaymentSplitTax indicates an expected call of CalculatePaymentSplitTax.
func (mr *MockGinSplittingServerMockRecorder) CalculatePaymentSplitTax(c interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CalculatePaymentSplitTax", reflect.TypeOf((*MockGinSplittingServer)(nil).CalculatePaymentSplitTax), c)
}
