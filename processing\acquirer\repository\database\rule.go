package database

import (
	"context"
	"errors"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"

	"git.local/sensitive/innerpb/processing/goerr"
	"git.local/sensitive/pkg/gtransaction"
	"git.local/sensitive/pkg/middlewares"
	"git.local/sensitive/processing/acquirer/model"
	"git.local/sensitive/processing/acquirer/schema"
	"git.local/sensitive/sdk/dog"
)

type RuleDB struct {
	db *gorm.DB
}

func NewRuleDB(db *gorm.DB) Ruler {
	return &RuleDB{
		db: db,
	}
}

func (r *RuleDB) Create(ctx context.Context, rule *model.Rule) (_ *model.Rule, err error) {
	ctx, span := dog.CreateSpan(ctx, "RuleDB_Create")
	defer span.End()

	err = gtransaction.FromContextOrFallback(ctx, r.db).WithContext(ctx).Create(rule).Error
	if err != nil {
		return nil, goerr.ErrDbUnexpected.WithErr(err).WithCtx(ctx)
	}

	return rule, nil
}

func (r *RuleDB) GetListWithBalancer(
	ctx context.Context,
	pagination *middlewares.PaginationInfo,
	request schema.RuleListRequest,
) (_ []*model.Rule, err error) {
	ctx, span := dog.CreateSpan(ctx, "RuleDB_GetListWithBalancer")
	defer span.End()

	rules := make([]*model.Rule, 0)

	query := r.db.WithContext(ctx).
		Model(model.Rule{}).
		Preload("RulePercentages").
		Preload("RulePercentages.Acquirer").
		Preload("Issuer")
	if request.ProjectID != nil {
		query.Where("project_id = ?", *request.ProjectID)
	}

	if request.TransactionTypeID != nil {
		query.Where("transaction_type_id = ?", *request.TransactionTypeID)
	}

	if request.IsBase != nil {
		query.Where("is_base = ?", request.IsBase)
	}

	query.Order("id DESC")

	if pagination == nil {
		if err = query.Find(&rules).Error; err != nil {
			return nil, goerr.ErrDbUnexpected.WithErr(err).WithCtx(ctx)
		}

		return rules, nil
	}

	if !pagination.Pagination {
		if err = query.Find(&rules).Error; err != nil {
			return nil, goerr.ErrDbUnexpected.WithErr(err).WithCtx(ctx)
		}

		return rules, nil
	}

	if err = query.WithContext(ctx).Model(&rules).
		Count(&pagination.Total).Error; err != nil {
		return nil, goerr.ErrDbUnexpected.WithErr(err).WithCtx(ctx)
	}

	if err = query.
		Offset((pagination.Page - 1) * pagination.PerPage).
		Limit(pagination.PerPage).
		Find(&rules).Error; err != nil {
		return nil, goerr.ErrDbUnexpected.WithErr(err).WithCtx(ctx)
	}

	return rules, nil
}

func (r *RuleDB) FindActiveRulesByProject(
	ctx context.Context,
	projectId uint64,
	TransactionTypeID uint64,
) (rules model.Rules, err error) {
	ctx, span := dog.CreateSpan(ctx, "RuleDB_FindActiveRulesByProject")
	defer span.End()

	err = r.db.WithContext(ctx).
		Where("project_id = ?", projectId).
		Where("transaction_type_id = ?", TransactionTypeID).
		Where("is_active", true).
		Order("weight DESC").
		Preload("RulePercentages").
		Find(&rules).
		Error

	if err != nil {
		return nil, goerr.ErrDbUnexpected.WithErr(err).WithCtx(ctx)
	}

	return rules, nil
}

func (r *RuleDB) GetBaseRuleByProject(
	ctx context.Context,
	projectId uint64,
	TransactionTypeID uint64,
) (baseRule *model.Rule, err error) {
	ctx, span := dog.CreateSpan(ctx, "RuleDB_GetBaseRuleByProject")
	defer span.End()

	var rule model.Rule

	err = r.db.WithContext(ctx).
		Model(rule).
		Where("project_id = ?", projectId).
		Where("transaction_type_id = ?", TransactionTypeID).
		Where("is_active = ?", true).
		Where("is_base = ?", true).
		Where("weight", model.BaseRuleWeight).
		First(&baseRule).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, goerr.ErrBaseRuleNotFound
		}

		return nil, goerr.ErrDbUnexpected.WithErr(err).WithCtx(ctx)
	}

	return baseRule, nil
}

func (r *RuleDB) FindByAllParam(
	ctx context.Context,
	rule *model.Rule,
	isActive *bool,
) (_ []*model.Rule, err error) {
	ctx, span := dog.CreateSpan(ctx, "RuleDB_FindByAllParam")
	defer span.End()

	if rule == nil {
		return nil, goerr.ErrParseErrorBody
	}

	rules := make([]*model.Rule, 0)

	db := r.db.WithContext(ctx).
		Model(rule).
		Where("project_id = ?", rule.ProjectID).
		Where("transaction_type_id = ?", rule.TransactionTypeID).
		Clauses(
			clause.Eq{
				Column: clause.Column{Table: rule.TableName(), Name: "ips_id"},
				Value:  rule.IpsID,
			},
			clause.Eq{
				Column: clause.Column{Table: rule.TableName(), Name: "issuer_id"},
				Value:  rule.IssuerID,
			},
			clause.Eq{
				Column: clause.Column{Table: rule.TableName(), Name: "country_id"},
				Value:  rule.CountryID,
			},
			clause.Eq{
				Column: clause.Column{Table: rule.TableName(), Name: "amount_from"},
				Value:  rule.AmountFrom,
			},
			clause.Eq{
				Column: clause.Column{Table: rule.TableName(), Name: "amount_to"},
				Value:  rule.AmountTo,
			},
		)

	if isActive != nil {
		db = db.Where("is_active", isActive)
	}

	err = db.Find(&rules).Error
	if err != nil {
		return nil, goerr.ErrDbUnexpected.WithErr(err).WithCtx(ctx)
	}

	return rules, nil
}

func (r *RuleDB) GetByID(ctx context.Context, id uint64) (rule *model.Rule, err error) {
	ctx, span := dog.CreateSpan(ctx, "RuleDB_GetByID")
	defer span.End()

	err = r.db.WithContext(ctx).
		Where("id = ?", id).
		First(&rule).
		Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, goerr.ErrRuleNotFound
		}

		return nil, goerr.ErrDbUnexpected.WithErr(err).WithCtx(ctx)
	}

	return rule, nil
}
