package service

import (
	"context"
	"git.local/sensitive/pkg/middlewares"
	"git.local/sensitive/processing/acquirer/model"
	"git.local/sensitive/processing/acquirer/repository/database"
	"git.local/sensitive/sdk/dog"
)

type IssuerService struct {
	issuerRepo database.Issuer
}

func NewIssuerService(
	issuerRepo database.Issuer,
) *IssuerService {
	return &IssuerService{
		issuerRepo: issuerRepo,
	}
}

func (s *IssuerService) GetAll(
	ctx context.Context,
	pagination *middlewares.PaginationInfo,
) (res []*model.BankBins, err error) {
	ctx, span := dog.CreateSpan(ctx, "IssuerService_GetAll")
	defer span.End()

	return s.issuerRepo.GetAll(ctx, pagination)
}

func (s *IssuerService) GetByID(ctx context.Context, id uint64) (_ model.Bank, err error) {
	ctx, span := dog.CreateSpan(ctx, "IssuerService_GetByID")
	defer span.End()

	return s.issuerRepo.GetByID(ctx, id)
}
