// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.5
// 	protoc        v5.29.3
// source: inner/processing/events/merchant.proto

package events

import (
	_ "git.local/sensitive/mvp/pb"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type ActivateMerchant struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            uint64                 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ActivateMerchant) Reset() {
	*x = ActivateMerchant{}
	mi := &file_inner_processing_events_merchant_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ActivateMerchant) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ActivateMerchant) ProtoMessage() {}

func (x *ActivateMerchant) ProtoReflect() protoreflect.Message {
	mi := &file_inner_processing_events_merchant_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ActivateMerchant.ProtoReflect.Descriptor instead.
func (*ActivateMerchant) Descriptor() ([]byte, []int) {
	return file_inner_processing_events_merchant_proto_rawDescGZIP(), []int{0}
}

func (x *ActivateMerchant) GetId() uint64 {
	if x != nil {
		return x.Id
	}
	return 0
}

type CreateProcessingMerchant struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	MerchantId    uint64                 `protobuf:"varint,1,opt,name=merchant_id,json=merchantId,proto3" json:"merchant_id,omitempty"`
	ProjectId     uint64                 `protobuf:"varint,2,opt,name=project_id,json=projectId,proto3" json:"project_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CreateProcessingMerchant) Reset() {
	*x = CreateProcessingMerchant{}
	mi := &file_inner_processing_events_merchant_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreateProcessingMerchant) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateProcessingMerchant) ProtoMessage() {}

func (x *CreateProcessingMerchant) ProtoReflect() protoreflect.Message {
	mi := &file_inner_processing_events_merchant_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateProcessingMerchant.ProtoReflect.Descriptor instead.
func (*CreateProcessingMerchant) Descriptor() ([]byte, []int) {
	return file_inner_processing_events_merchant_proto_rawDescGZIP(), []int{1}
}

func (x *CreateProcessingMerchant) GetMerchantId() uint64 {
	if x != nil {
		return x.MerchantId
	}
	return 0
}

func (x *CreateProcessingMerchant) GetProjectId() uint64 {
	if x != nil {
		return x.ProjectId
	}
	return 0
}

type CreateSetting struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Producer      string                 `protobuf:"bytes,1,opt,name=producer,proto3" json:"producer,omitempty"`
	MerchantId    uint64                 `protobuf:"varint,2,opt,name=merchant_id,json=merchantId,proto3" json:"merchant_id,omitempty"`
	ProjectId     uint64                 `protobuf:"varint,3,opt,name=project_id,json=projectId,proto3" json:"project_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CreateSetting) Reset() {
	*x = CreateSetting{}
	mi := &file_inner_processing_events_merchant_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreateSetting) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateSetting) ProtoMessage() {}

func (x *CreateSetting) ProtoReflect() protoreflect.Message {
	mi := &file_inner_processing_events_merchant_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateSetting.ProtoReflect.Descriptor instead.
func (*CreateSetting) Descriptor() ([]byte, []int) {
	return file_inner_processing_events_merchant_proto_rawDescGZIP(), []int{2}
}

func (x *CreateSetting) GetProducer() string {
	if x != nil {
		return x.Producer
	}
	return ""
}

func (x *CreateSetting) GetMerchantId() uint64 {
	if x != nil {
		return x.MerchantId
	}
	return 0
}

func (x *CreateSetting) GetProjectId() uint64 {
	if x != nil {
		return x.ProjectId
	}
	return 0
}

type DeactivateMerchant struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            uint64                 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DeactivateMerchant) Reset() {
	*x = DeactivateMerchant{}
	mi := &file_inner_processing_events_merchant_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DeactivateMerchant) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeactivateMerchant) ProtoMessage() {}

func (x *DeactivateMerchant) ProtoReflect() protoreflect.Message {
	mi := &file_inner_processing_events_merchant_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeactivateMerchant.ProtoReflect.Descriptor instead.
func (*DeactivateMerchant) Descriptor() ([]byte, []int) {
	return file_inner_processing_events_merchant_proto_rawDescGZIP(), []int{3}
}

func (x *DeactivateMerchant) GetId() uint64 {
	if x != nil {
		return x.Id
	}
	return 0
}

var File_inner_processing_events_merchant_proto protoreflect.FileDescriptor

var file_inner_processing_events_merchant_proto_rawDesc = string([]byte{
	0x0a, 0x26, 0x69, 0x6e, 0x6e, 0x65, 0x72, 0x2f, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69,
	0x6e, 0x67, 0x2f, 0x65, 0x76, 0x65, 0x6e, 0x74, 0x73, 0x2f, 0x6d, 0x65, 0x72, 0x63, 0x68, 0x61,
	0x6e, 0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x16, 0x6d, 0x76, 0x70, 0x2f, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x2f, 0x65, 0x76, 0x65, 0x6e, 0x74, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x22, 0x29, 0x0a, 0x10, 0x41, 0x63, 0x74, 0x69, 0x76, 0x61, 0x74, 0x65, 0x4d, 0x65, 0x72, 0x63,
	0x68, 0x61, 0x6e, 0x74, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04,
	0x52, 0x02, 0x69, 0x64, 0x3a, 0x05, 0xb8, 0xe9, 0xe4, 0x03, 0x01, 0x22, 0x5a, 0x0a, 0x18, 0x43,
	0x72, 0x65, 0x61, 0x74, 0x65, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x4d,
	0x65, 0x72, 0x63, 0x68, 0x61, 0x6e, 0x74, 0x12, 0x1f, 0x0a, 0x0b, 0x6d, 0x65, 0x72, 0x63, 0x68,
	0x61, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x0a, 0x6d, 0x65,
	0x72, 0x63, 0x68, 0x61, 0x6e, 0x74, 0x49, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x70, 0x72, 0x6f, 0x6a,
	0x65, 0x63, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x04, 0x52, 0x09, 0x70, 0x72,
	0x6f, 0x6a, 0x65, 0x63, 0x74, 0x49, 0x64, 0x22, 0x6b, 0x0a, 0x0d, 0x43, 0x72, 0x65, 0x61, 0x74,
	0x65, 0x53, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x12, 0x1a, 0x0a, 0x08, 0x70, 0x72, 0x6f, 0x64,
	0x75, 0x63, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x70, 0x72, 0x6f, 0x64,
	0x75, 0x63, 0x65, 0x72, 0x12, 0x1f, 0x0a, 0x0b, 0x6d, 0x65, 0x72, 0x63, 0x68, 0x61, 0x6e, 0x74,
	0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x04, 0x52, 0x0a, 0x6d, 0x65, 0x72, 0x63, 0x68,
	0x61, 0x6e, 0x74, 0x49, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74,
	0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x04, 0x52, 0x09, 0x70, 0x72, 0x6f, 0x6a, 0x65,
	0x63, 0x74, 0x49, 0x64, 0x22, 0x24, 0x0a, 0x12, 0x44, 0x65, 0x61, 0x63, 0x74, 0x69, 0x76, 0x61,
	0x74, 0x65, 0x4d, 0x65, 0x72, 0x63, 0x68, 0x61, 0x6e, 0x74, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x02, 0x69, 0x64, 0x42, 0x2f, 0x5a, 0x2d, 0x67, 0x69,
	0x74, 0x2e, 0x6c, 0x6f, 0x63, 0x61, 0x6c, 0x2f, 0x73, 0x65, 0x6e, 0x73, 0x69, 0x74, 0x69, 0x76,
	0x65, 0x2f, 0x69, 0x6e, 0x6e, 0x65, 0x72, 0x70, 0x62, 0x2f, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73,
	0x73, 0x69, 0x6e, 0x67, 0x2f, 0x65, 0x76, 0x65, 0x6e, 0x74, 0x73, 0x62, 0x06, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x33,
})

var (
	file_inner_processing_events_merchant_proto_rawDescOnce sync.Once
	file_inner_processing_events_merchant_proto_rawDescData []byte
)

func file_inner_processing_events_merchant_proto_rawDescGZIP() []byte {
	file_inner_processing_events_merchant_proto_rawDescOnce.Do(func() {
		file_inner_processing_events_merchant_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_inner_processing_events_merchant_proto_rawDesc), len(file_inner_processing_events_merchant_proto_rawDesc)))
	})
	return file_inner_processing_events_merchant_proto_rawDescData
}

var file_inner_processing_events_merchant_proto_msgTypes = make([]protoimpl.MessageInfo, 4)
var file_inner_processing_events_merchant_proto_goTypes = []any{
	(*ActivateMerchant)(nil),         // 0: ActivateMerchant
	(*CreateProcessingMerchant)(nil), // 1: CreateProcessingMerchant
	(*CreateSetting)(nil),            // 2: CreateSetting
	(*DeactivateMerchant)(nil),       // 3: DeactivateMerchant
}
var file_inner_processing_events_merchant_proto_depIdxs = []int32{
	0, // [0:0] is the sub-list for method output_type
	0, // [0:0] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_inner_processing_events_merchant_proto_init() }
func file_inner_processing_events_merchant_proto_init() {
	if File_inner_processing_events_merchant_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_inner_processing_events_merchant_proto_rawDesc), len(file_inner_processing_events_merchant_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   4,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_inner_processing_events_merchant_proto_goTypes,
		DependencyIndexes: file_inner_processing_events_merchant_proto_depIdxs,
		MessageInfos:      file_inner_processing_events_merchant_proto_msgTypes,
	}.Build()
	File_inner_processing_events_merchant_proto = out.File
	file_inner_processing_events_merchant_proto_goTypes = nil
	file_inner_processing_events_merchant_proto_depIdxs = nil
}
