package schema

import (
	"context"
	"encoding/base64"
	"encoding/json"
	"strconv"
	"time"

	goluhn "github.com/theplant/luhn"

	"git.local/sensitive/innerpb/processing/goerr"

	"github.com/go-playground/validator/v10"

	gorpc "git.local/sensitive/innerpb/processing/grpc"
)

const (
	panLenghts     = 16
	xCount         = 6
	monthMaxValue  = 12
	yearBeginValue = "20"
)

type (
	CardInfoRequest struct {
		Pan string
	}

	CardInfoResponse struct {
		IpsID     uint64
		CountryID uint64
		IssuerID  uint64
	}

	MaskedPanInfo struct {
		ID        uint64 `json:"id"`
		MaskedPan string `json:"masked_pan"`
	}

	CardInfoForView struct {
		CardID uint64 `json:"card_id"`
		Key    string `json:"key"`
	}

	GetClientCardsRequest struct {
		MerchantID      uint64 `form:"merchant_id" json:"merchant_id" validate:"required"`
		ProjectID       uint64 `form:"project_id" json:"project_id" validate:"required"`
		ProjectClientID string `form:"project_client_id" json:"project_client_id" validate:"required"`
	}

	DecryptedCard struct {
		Pan      string `json:"pan" validate:"required,min=16,max=19,startsnotwith=-"`
		ExpMonth string `json:"exp_month" validate:"required,numeric,len=2,startsnotwith=-,ne=00"`
		ExpYear  string `json:"exp_year" validate:"required,numeric,len=2,startsnotwith=-"`
		Cvc      string `json:"cvc" validate:"required,numeric,min=3,max=4,startsnotwith=-"`
		FullName string `json:"full_name" validate:"required,min=3"`
	}

	DecryptedPan struct {
		Pan string `json:"pan" validate:"required,min=16,max=19,startsnotwith=-"`
	}
)

func (d *DecryptedCard) Validate() error {
	validation := validator.New()

	if err := panValidate(d.Pan); err != nil {
		return err
	}

	if err := d.expirationDateValidate(); err != nil {
		return err
	}

	return validation.Struct(d)
}

func (d *DecryptedPan) Validate() error {
	validation := validator.New()

	if err := panValidate(d.Pan); err != nil {
		return err
	}

	return validation.Struct(d)
}

func panValidate(panStr string) error {
	pan, err := strconv.Atoi(panStr)

	if err != nil {
		return goerr.ErrPanValidation
	}

	if panValid := goluhn.Valid(pan); !panValid {
		return goerr.ErrPanValidation
	}

	return nil
}

func (d *DecryptedCard) expirationDateValidate() error {
	expMonth, err := strconv.Atoi(d.ExpMonth)
	if err != nil {
		return goerr.ErrDateValidation
	}

	expYear, err := strconv.Atoi(yearBeginValue + d.ExpYear)
	if err != nil {
		return goerr.ErrDateValidation
	}

	if expMonth > monthMaxValue {
		return goerr.ErrDateValidation
	}

	// the day of expDate will be always the last of the month
	expDate := time.Date(
		expYear, time.Month(expMonth), 1, 23, 59, 59, 59, time.UTC)

	expDate = expDate.AddDate(0, 1, -expDate.Day())

	if expDate.Before(time.Now()) {
		return goerr.ErrCardExpiration
	}

	return nil
}

func NewCardInfoResponseFromRaw(v1 *gorpc.GetCardInfoByPanResponseV1) CardInfoResponse {
	return CardInfoResponse{
		IpsID:     v1.GetIpsId(),
		CountryID: v1.GetCountryId(),
		IssuerID:  v1.GetIssuerId(),
	}
}

func (c DeactivateTokenRequest) Validate() error {
	return validator.New().Struct(c)
}

func (c DeactivateTokenRequest) ToBase64(ctx context.Context) (string, error) {
	notSortedJson, err := json.Marshal(c)
	if err != nil {
		return "", goerr.ErrHttpRequestUnexpected.WithErr(err).WithCtx(ctx)
	}

	var notSortedMap map[string]interface{}

	if err = json.Unmarshal(notSortedJson, &notSortedMap); err != nil {
		return "", goerr.ErrHttpRequestUnexpected.WithErr(err).WithCtx(ctx)
	}

	sortedJson, err := json.Marshal(notSortedMap)
	if err != nil {
		return "", goerr.ErrHttpRequestUnexpected.WithErr(err).WithCtx(ctx)
	}

	return base64.StdEncoding.EncodeToString(sortedJson), nil
}
