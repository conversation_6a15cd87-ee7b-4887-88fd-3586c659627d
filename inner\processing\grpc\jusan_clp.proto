edition = "2023";

package processing.jusan_tokenize.jusan_tokenize;

option go_package = "git.local/sensitive/innerpb/processing/grpc";

import "inner/processing/grpc/transaction_status.proto";
import "inner/processing/grpc/integration.proto";

import "mvp/proto/refs.proto";
import "mvp/proto/enum.proto";

import "google/protobuf/descriptor.proto";

message JusanResponseCodeClpRef {
  string code = 1;
  string message = 2;
  transaction.transaction_status.EnumTransactionStatus transaction_status = 3;
  integration.integration.IntegrationError integration_error = 4;
}

extend google.protobuf.EnumValueOptions {
  JusanResponseCodeClpRef jusan_response_code_clp_value = 100121;
}

extend google.protobuf.EnumOptions {
  JusanResponseCodeClpRef default_jusan_response_code_clp_value = 100122;
}

enum JusanResponseCodeClp {
  option(mvp.default_ref) = "default_jusan_response_code_clp_value";
  option(mvp.ref) = "jusan_response_code_clp_value";
  option(default_jusan_response_code_clp_value) = {
    code: "0"
    message: "default"
    transaction_status: TransactionStatusError
    integration_error: None
  };

  ClpResponseUnavailable = 0 [(jusan_response_code_clp_value) = {
    code: "11"
    message: "Сервис временно недоступен, попробуйте позже"
    transaction_status: TransactionStatusFailed
    integration_error: 108
  }, (mvp.from_string) = "11"];

  ClpIncorrectOrder = 1 [(jusan_response_code_clp_value) = {
    code: "12"
    message: "Неправильное значение в поле ORDER:"
    transaction_status: TransactionStatusFailed
    integration_error: TransactionDeclinedByAcquirer
  }, (mvp.from_string) = "12"];

  ClpIncorrectAmount = 2 [(jusan_response_code_clp_value) = {
    code: "13"
    message: "Неправильная сумма: "
    transaction_status: TransactionStatusFailed
    integration_error: TransactionDeclinedByAcquirer
  }, (mvp.from_string) = "13"];

  ClpIncorrectCurrency = 3 [(jusan_response_code_clp_value) = {
    code: "14"
    message: "Неправильная валюта:"
    transaction_status: TransactionStatusFailed
    integration_error: TransactionDeclinedByAcquirer
  }, (mvp.from_string) = "14"];

  ClpMPIUnavailable = 4 [(jusan_response_code_clp_value) = {
    code: "15"
    message: "Сервис MPI временно недоступен, попробуйте позже"
    transaction_status: TransactionStatusFailed
    integration_error: UnavailableAcquirer
  }, (mvp.from_string) = "15"];

  ClpDbUnavailable = 5 [(jusan_response_code_clp_value) = {
    code: "16"
    message: "Сервис Db временно недоступен, попробуйте позже"
    transaction_status: TransactionStatusFailed
    integration_error: UnavailableAcquirer
  }, (mvp.from_string) = "16"];

  ClpForbiddenMerchant = 6 [(jusan_response_code_clp_value) = {
    code: "171"
    message: "Коммерсанту запрещено выполнение операций"
    transaction_status: TransactionStatusFailed
    integration_error: PaymentForbiddenForMerchant
  }, (mvp.from_string) = "171"];

  ClpMerchantForbidden = 7 [(jusan_response_code_clp_value) = {
    code: "172 "
    message: "Коммерсанту запрещено проведение операций в соответствии с Законом о ПОД/ФТ"
    transaction_status: TransactionStatusFailed
    integration_error: PaymentForbiddenForMerchant
  }, (mvp.from_string) = "172 "];

  ClpRequestAlreadyCompleted = 8 [(jusan_response_code_clp_value) = {
    code: "18"
    message: "Запрос уже выполнялся"
    transaction_status: TransactionStatusFailed
    integration_error: TransactionDeclinedByAcquirer
  }, (mvp.from_string) = "18"];

  ClpIncorrectCardExpDate = 9 [(jusan_response_code_clp_value) = {
    code: "19"
    message: "Неправильная дата дейстия карты (MM/ГГ)"
    transaction_status: TransactionStatusFailed
    integration_error: IncorrectCardExpDate
  }, (mvp.from_string) = "19"];

  ClpIncorrectTerminal = 10 [(jusan_response_code_clp_value) = {
    code: "20"
    message: "Неправильное значение в поле TERMINAL:"
    transaction_status: TransactionStatusFailed
    integration_error: TransactionDeclinedByAcquirer
  }, (mvp.from_string) = "20"];

  ClpInvalidSign = 11 [(jusan_response_code_clp_value) = {
    code: "21"
    message: "Неправильная подпись!"
    transaction_status: TransactionStatusFailed
    integration_error: TransactionDeclinedByAcquirer
  }, (mvp.from_string) = "21"];

  ClpCurrencyNotFound = 12 [(jusan_response_code_clp_value) = {
    code: "22"
    message: "Не найден курс валюты"
    transaction_status: TransactionStatusFailed
    integration_error: TransactionDeclinedByAcquirer
  }, (mvp.from_string) = "22"];

  ClpLimitExceeds = 13 [(jusan_response_code_clp_value) = {
    code: "23"
    message: "Превышен лимит!"
    transaction_status: TransactionStatusFailed
    integration_error: ExceedsAmountLimit
  }, (mvp.from_string) = "23"];

  ClpEmptyField = 14 [(jusan_response_code_clp_value) = {
    code: "24"
    message: "Не указано значение в поле"
    transaction_status: TransactionStatusFailed
    integration_error: TransactionDeclinedByAcquirer
  }, (mvp.from_string) = "24"];

  ClpSizeLessSymbols = 15 [(jusan_response_code_clp_value) = {
    code: "25"
    message: "Размер значения в поле менее симоволов"
    transaction_status: TransactionStatusFailed
    integration_error: TransactionDeclinedByAcquirer
  }, (mvp.from_string) = "25"];

  ClpSizeMoreSymbols = 16 [(jusan_response_code_clp_value) = {
    code: "26"
    message: "Размер значения в поле больше симоволов"
    transaction_status: TransactionStatusFailed
    integration_error: TransactionDeclinedByAcquirer
  }, (mvp.from_string) = "26"];

  ClpInvalidValue = 17 [(jusan_response_code_clp_value) = {
    code: "27"
    message: "Введите валидное значение в поле"
    transaction_status: TransactionStatusFailed
    integration_error: TransactionDeclinedByAcquirer
  }, (mvp.from_string) = "27"];

  ClpMPIError3DS = 18 [(jusan_response_code_clp_value) = {
    code: "28"
    message: "Ошибка MPI при выполнении проверки 3DS:"
    transaction_status: TransactionStatusFailed
    integration_error: TransactionDeclinedByAcquirer
  }, (mvp.from_string) = "28"];

  ClpInvalidCardType = 19 [(jusan_response_code_clp_value) = {
    code: "29"
    message: "Недопустимый тип карты"
    transaction_status: TransactionStatusFailed
    integration_error: 102
  }, (mvp.from_string) = "29"];

  ClpPaymentNotFound = 20 [(jusan_response_code_clp_value) = {
    code: "30"
    message: "Счет на оплату не найден"
    transaction_status: TransactionStatusFailed
    integration_error: TransactionDeclinedByAcquirer
  }, (mvp.from_string) = "30"];

  ClpEmptyClientKey = 21 [(jusan_response_code_clp_value) = {
    code: "31"
    message: "Не передан ключ указанного клиента"
    transaction_status: TransactionStatusFailed
    integration_error: TransactionDeclinedByAcquirer
  }, (mvp.from_string) = "31"];

  ClpForbiddenTerminal = 22 [(jusan_response_code_clp_value) = {
    code: "32"
    message: "Для терминала запрещена токенизация"
    transaction_status: TransactionStatusFailed
    integration_error: PaymentForbiddenForMerchant
  }, (mvp.from_string) = "32"];

  ClpTokenNotFound = 23 [(jusan_response_code_clp_value) = {
    code: "33"
    message: "Для данного клиента в вашей организации не зарегистрирован токен"
    transaction_status: TransactionStatusFailed
    integration_error: InvalidCard
  }, (mvp.from_string) = "33"];

  ClpIncorrectBlockAmount = 24 [(jusan_response_code_clp_value) = {
    code: "34"
    message: "Неверная сумма блокирования, заявка отменена!"
    transaction_status: TransactionStatusFailed
    integration_error: TransactionDeclinedByAcquirer
  }, (mvp.from_string) = "34"];

  ClpUnknownError = 25 [(jusan_response_code_clp_value) = {
    code: "99"
    message: "Неизвестная ошибка: "
    transaction_status: TransactionStatusHolded
    integration_error: TransactionDeclinedByAcquirer
  }, (mvp.from_string) = "99"];

  ClpUnavailableService = 26 [(jusan_response_code_clp_value) = {
    code: "41"
    message: "Сервис временно недоступен, попробуйте позже"
    transaction_status: TransactionStatusFailed
    integration_error: UnavailableAcquirer
  }, (mvp.from_string) = "41"];

  ClpAmountIncorrect = 27 [(jusan_response_code_clp_value) = {
    code: "42"
    message: "Неправильная сумма"
    transaction_status: TransactionStatusFailed
    integration_error: TransactionDeclinedByAcquirer
  }, (mvp.from_string) = "42"];

  ClpUnavailableDb = 28 [(jusan_response_code_clp_value) = {
    code: "43"
    message: "Сервис Db временно недоступен, попробуйте позже "
    transaction_status: TransactionStatusFailed
    integration_error: UnavailableAcquirer
  }, (mvp.from_string) = "43"];

  ClpIncorrectMerchant = 29 [(jusan_response_code_clp_value) = {
    code: "44"
    message: "Неправильное значение в поле MERCHANT"
    transaction_status: TransactionStatusFailed
    integration_error: TransactionDeclinedByAcquirer
  }, (mvp.from_string) = "44"];

  ClpMerchantNotFound = 30 [(jusan_response_code_clp_value) = {
    code: "17"
    message: "Коммерсант не найден"
    transaction_status: TransactionStatusFailed
    integration_error: PaymentForbiddenForMerchant
  }, (mvp.from_string) = "17"];

  ClpOrderNotFound = 31 [(jusan_response_code_clp_value) = {
    code: "45"
    message: "Заявка ORDER не найдена"
    transaction_status: TransactionStatusFailed
    integration_error: TransactionDeclinedByAcquirer
  }, (mvp.from_string) = "45"];

  ClpSignInvalid = 32 [(jusan_response_code_clp_value) = {
    code: "46"
    message: "Неправильная подпись!"
    transaction_status: TransactionStatusFailed
    integration_error: TransactionDeclinedByAcquirer
  }, (mvp.from_string) = "46"];

  ClpRefundAmountIncorrect = 33 [(jusan_response_code_clp_value) = {
    code: "47"
    message: "Сумма возврта '%s' больше чем сумма заказа"
    transaction_status: TransactionStatusFailed
    integration_error: TransactionDeclinedByAcquirer
  }, (mvp.from_string) = "47"];

  ClpIncorrectStatus = 34 [(jusan_response_code_clp_value) = {
    code: "48"
    message: "Текущий статус заказа не позволяет делать возврат/отмену"
    transaction_status: TransactionStatusFailed
    integration_error: TransactionDeclinedByAcquirer
  }, (mvp.from_string) = "48"];

  ClpIncorrectValue = 35 [(jusan_response_code_clp_value) = {
    code: "50"
    message: "Неправильное значение"
    transaction_status: TransactionStatusFailed
    integration_error: TransactionDeclinedByAcquirer
  }, (mvp.from_string) = "50"];

  ClpTerminalForbidden = 36 [(jusan_response_code_clp_value) = {
    code: "51"
    message: "Текущий статус терминала не позволяет производить операции"
    transaction_status: TransactionStatusFailed
    integration_error: PaymentForbiddenForMerchant
  }, (mvp.from_string) = "51"];

  ClpForbiddenOperation = 37 [(jusan_response_code_clp_value) = {
    code: "52"
    message: "Операция отмены/возврата через API для терминала запрещена"
    transaction_status: TransactionStatusFailed
    integration_error: PaymentForbiddenForMerchant
  }, (mvp.from_string) = "52"];

  ClpDuplicateDescription = 38 [(jusan_response_code_clp_value) = {
    code: "53"
    message: "Дублирование описания отмены"
    transaction_status: TransactionStatusFailed
    integration_error: TransactionDeclinedByAcquirer
  }, (mvp.from_string) = "53"];

  ClpRefundHandleError = 39 [(jusan_response_code_clp_value) = {
    code: "F"
    message: "Ошибка при обработке возврата"
    transaction_status: TransactionStatusFailed
    integration_error: TransactionDeclinedByAcquirer
  }, (mvp.from_string) = "F"];

  ClpPaymentError = 40 [(jusan_response_code_clp_value) = {
    code: "E"
    message: "Ошибка при оплате"
    transaction_status: TransactionStatusFailed
    integration_error: TransactionDeclinedByAcquirer
  }, (mvp.from_string) = "E"];

  ClpPaymentExpired = 41 [(jusan_response_code_clp_value) = {
    code: "c"
    message: "Счет на оплату устарел"
    transaction_status: TransactionStatusFailed
    integration_error: TransactionDeclinedByAcquirer
  }, (mvp.from_string) = "c"];

  ClpAuthError = 43 [(jusan_response_code_clp_value) = {
    code: "3"
    message: "Ошибка авторизации платежа"
    transaction_status: TransactionStatusFailed
    integration_error: TransactionDeclinedByAcquirer
  }, (mvp.from_string) = "3"];

  ClpIncorrectCardNum = 44 [(jusan_response_code_clp_value) = {
    code: "37"
    message: "Неправильный номер карты"
    transaction_status: TransactionStatusFailed
    integration_error: IncorrectCardExpDate
  }, (mvp.from_string) = "37"];

  ClpSuspectedFraud = 45 [(jusan_response_code_clp_value) = {
    code: "59"
    transaction_status: TransactionStatusFailed
    integration_error: SuspiciousClient
    message: "Suspected fraud"
  }, (mvp.from_string) = "59"];

  ClpUndefinedError = 47 [(jusan_response_code_clp_value) = {
    code: "1"
    transaction_status: TransactionStatusFailed
    integration_error: UndefinedError
    message: "Неизвестная ошибка"
  }, (mvp.from_string) = "1"];

  ClpExceedsLimit = 49 [(jusan_response_code_clp_value) = {
    code: "61"
    integration_error: ExceedsAmountLimit
    transaction_status: TransactionStatusFailed
    message: "Превышает лимит суммы"
  }, (mvp.from_string) = "61"];

  ClpLimitedCard = 50 [(jusan_response_code_clp_value) = {
    code: "62"
    integration_error: BlockedCard
    transaction_status: TransactionStatusFailed
    message: "Карта с ограниченным доступом"
  }, (mvp.from_string) = "62"];

  ClpTransactionDeclined = 51 [(jusan_response_code_clp_value) = {
    code: "05"
    transaction_status: TransactionStatusFailed
    integration_error: TransactionDeclinedByAcquirer
    message: "Транзакция отклонена банком или МПС"
  }, (mvp.from_string) = "05"];

  ClpNotEnough = 52 [(jusan_response_code_clp_value) = {
    code: "2"
    transaction_status: TransactionStatusFailed
    integration_error: InsufficientFunds
    message: "Не достаточно средств"
  }, (mvp.from_string) = "2"];

  ClpLimitExceeded = 53 [(jusan_response_code_clp_value) = {
    code: "65"
    integration_error: ExceedsTransactionFrequencyLimit
    transaction_status: TransactionStatusFailed
    message: "Превышен лимит"
  }, (mvp.from_string) = "65"];

  ClpErrorAuth = 54 [(jusan_response_code_clp_value) = {
    code: "-19"
    transaction_status: TransactionStatusFailed
    integration_error: TransactionDeclinedByAcquirer
    message: "Ошибка авторизации"
  }, (mvp.from_string) = "-19"];

  ClpCardInactive = 55 [(jusan_response_code_clp_value) = {
    code: "07"
    transaction_status: TransactionStatusFailed
    integration_error: CardHasExpired
    message: "Ваша карта не активна"
  }, (mvp.from_string) = "07"];
}