// Code generated by protoc-gen-go-mvp. DO NOT EDIT.
// versions:
// - protoc-gen-go-mvp v2.0.1
// created by mvp authors

package grpc

import (
	dog "git.local/sensitive/sdk/dog"
	gin "github.com/gin-gonic/gin"
	metadata "google.golang.org/grpc/metadata"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
	http "net/http"
)

func RegisterGinMagnetiqRouterGroup(
	in *gin.RouterGroup,
	handler func(func(c *gin.Context) error) gin.HandlerFunc,
) error {
	service, err := NewGinMagnetiqService()
	if err != nil {
		return err
	}

	routerGroup := in.Group("processing.magnetiq.magnetiq.Magnetiq")
	routerGroup.PUT("/PayIn", handler(service.PayIn))
	routerGroup.PUT("/OneClickPayIn", handler(service.OneClickPayIn))
	routerGroup.PUT("/ThreeDSConfirm", handler(service.ThreeDSConfirm))
	routerGroup.PUT("/ThreeDSResume", handler(service.ThreeDSResume))
	routerGroup.PUT("/PayOut", handler(service.PayOut))
	routerGroup.PUT("/GetBankTransactionStatus", handler(service.GetBankTransactionStatus))
	routerGroup.PUT("/GetBankTransactionStatusUnformated", handler(service.GetBankTransactionStatusUnformated))
	routerGroup.PUT("/Refund", handler(service.Refund))
	routerGroup.PUT("/GooglePay", handler(service.GooglePay))
	routerGroup.PUT("/ApplePay", handler(service.ApplePay))
	routerGroup.PUT("/TwoStagePayIn", handler(service.TwoStagePayIn))
	routerGroup.PUT("/Charge", handler(service.Charge))
	routerGroup.PUT("/Cancel", handler(service.Cancel))
	routerGroup.PUT("/MakeToken", handler(service.MakeToken))
	routerGroup.PUT("/GetAcquirerIdentifier", handler(service.GetAcquirerIdentifier))
	routerGroup.PUT("/ResolveVisaAlias", handler(service.ResolveVisaAlias))
	routerGroup.PUT("/PayOutByPhone", handler(service.PayOutByPhone))
	return nil
}

func NewGinMagnetiqService() (GinMagnetiqServer, error) {
	client, err := NewPreparedMagnetiqClient()
	if err != nil {
		return nil, err
	}

	return &ginMagnetiqServer{
		client: NewLoggedMagnetiqClient(
			NewIamMagnetiqClient(client),
		),
	}, nil
}

//go:generate mockgen -destination=./grpcmock/magnetiq.gin.pb.go -package=grpcmock -source=magnetiq.gin.pb.go GinMagnetiqServer
type GinMagnetiqServer interface {
	PayIn(c *gin.Context) error
	OneClickPayIn(c *gin.Context) error
	ThreeDSConfirm(c *gin.Context) error
	ThreeDSResume(c *gin.Context) error
	PayOut(c *gin.Context) error
	GetBankTransactionStatus(c *gin.Context) error
	GetBankTransactionStatusUnformated(c *gin.Context) error
	Refund(c *gin.Context) error
	GooglePay(c *gin.Context) error
	ApplePay(c *gin.Context) error
	TwoStagePayIn(c *gin.Context) error
	Charge(c *gin.Context) error
	Cancel(c *gin.Context) error
	MakeToken(c *gin.Context) error
	GetAcquirerIdentifier(c *gin.Context) error
	ResolveVisaAlias(c *gin.Context) error
	PayOutByPhone(c *gin.Context) error
}

var _ GinMagnetiqServer = (*ginMagnetiqServer)(nil)

type ginMagnetiqServer struct {
	client MagnetiqClient
}

type Magnetiq_PayIn_Success struct {
	// Status is the status of the response. Set true
	Status bool `json:"status"`
	// Message is a human-readable explanation of the status
	Message string `json:"message"`
	// StatusCode is the GoErr status. Set 0
	StatusCode int `json:"status_code"`
	// Result is the result of the request
	Result *PayInResponseData `json:"result"`
}

type Magnetiq_PayIn_Failure struct {
	// Status is the status of the response. Set false
	Status bool `json:"status"`
	// Message is a human-readable explanation of the status
	Message string `json:"message"`
	// StatusCode is the GoErr status
	StatusCode int `json:"status_code"`
}

// PayIn
// @Summary PayIn
// @Security bearerAuth
// @ID Magnetiq_PayIn
// @Accept json
// @Param request body PayInRequestData true "PayInRequestData"
// @Success 200 {object} Magnetiq_PayIn_Success
// @Failure 401 {object} Magnetiq_PayIn_Failure "goerrpkg.ErrorTypeAuthorization"
// @Failure 404 {object} Magnetiq_PayIn_Failure "goerrpkg.ErrorTypeNotFound"
// @Failure 400 {object} Magnetiq_PayIn_Failure "goerrpkg.ErrorTypeBadRequest"
// @Failure 500 {object} Magnetiq_PayIn_Failure "goerrpkg.ErrorTypeUnexpectedError"
// @Failure 400 {object} Magnetiq_PayIn_Failure "gin.ErrorTypeBind"
// @Failure 500 {object} Magnetiq_PayIn_Failure "Undefined error"
// @Produce json
// @Router /processing.magnetiq.magnetiq.Magnetiq/PayIn [put]
// @tags Magnetiq
func (s *ginMagnetiqServer) PayIn(c *gin.Context) error {
	ctx, span := dog.CreateSpan(c.Request.Context(), "GinMagnetiqServer_PayIn")
	defer span.End()

	var request PayInRequestData
	if err := c.ShouldBindJSON(&request); err != nil {
		return err
	}

	ctx = metadata.NewOutgoingContext(ctx, metadata.MD(c.Request.Header))

	response, err := s.client.PayIn(ctx, &request)
	if err != nil {
		return err
	}

	c.JSON(http.StatusOK, &Magnetiq_PayIn_Success{
		Status:     true,
		Message:    "Success",
		StatusCode: 0,
		Result:     response,
	})
	return nil
}

type Magnetiq_OneClickPayIn_Success struct {
	// Status is the status of the response. Set true
	Status bool `json:"status"`
	// Message is a human-readable explanation of the status
	Message string `json:"message"`
	// StatusCode is the GoErr status. Set 0
	StatusCode int `json:"status_code"`
	// Result is the result of the request
	Result *PayInResponseData `json:"result"`
}

type Magnetiq_OneClickPayIn_Failure struct {
	// Status is the status of the response. Set false
	Status bool `json:"status"`
	// Message is a human-readable explanation of the status
	Message string `json:"message"`
	// StatusCode is the GoErr status
	StatusCode int `json:"status_code"`
}

// OneClickPayIn
// @Summary OneClickPayIn
// @Security bearerAuth
// @ID Magnetiq_OneClickPayIn
// @Accept json
// @Param request body OneClickPayInRequestData true "OneClickPayInRequestData"
// @Success 200 {object} Magnetiq_OneClickPayIn_Success
// @Failure 401 {object} Magnetiq_OneClickPayIn_Failure "goerrpkg.ErrorTypeAuthorization"
// @Failure 404 {object} Magnetiq_OneClickPayIn_Failure "goerrpkg.ErrorTypeNotFound"
// @Failure 400 {object} Magnetiq_OneClickPayIn_Failure "goerrpkg.ErrorTypeBadRequest"
// @Failure 500 {object} Magnetiq_OneClickPayIn_Failure "goerrpkg.ErrorTypeUnexpectedError"
// @Failure 400 {object} Magnetiq_OneClickPayIn_Failure "gin.ErrorTypeBind"
// @Failure 500 {object} Magnetiq_OneClickPayIn_Failure "Undefined error"
// @Produce json
// @Router /processing.magnetiq.magnetiq.Magnetiq/OneClickPayIn [put]
// @tags Magnetiq
func (s *ginMagnetiqServer) OneClickPayIn(c *gin.Context) error {
	ctx, span := dog.CreateSpan(c.Request.Context(), "GinMagnetiqServer_OneClickPayIn")
	defer span.End()

	var request OneClickPayInRequestData
	if err := c.ShouldBindJSON(&request); err != nil {
		return err
	}

	ctx = metadata.NewOutgoingContext(ctx, metadata.MD(c.Request.Header))

	response, err := s.client.OneClickPayIn(ctx, &request)
	if err != nil {
		return err
	}

	c.JSON(http.StatusOK, &Magnetiq_OneClickPayIn_Success{
		Status:     true,
		Message:    "Success",
		StatusCode: 0,
		Result:     response,
	})
	return nil
}

type Magnetiq_ThreeDSConfirm_Success struct {
	// Status is the status of the response. Set true
	Status bool `json:"status"`
	// Message is a human-readable explanation of the status
	Message string `json:"message"`
	// StatusCode is the GoErr status. Set 0
	StatusCode int `json:"status_code"`
	// Result is the result of the request
	Result *ThreeDSResponseData `json:"result"`
}

type Magnetiq_ThreeDSConfirm_Failure struct {
	// Status is the status of the response. Set false
	Status bool `json:"status"`
	// Message is a human-readable explanation of the status
	Message string `json:"message"`
	// StatusCode is the GoErr status
	StatusCode int `json:"status_code"`
}

// ThreeDSConfirm
// @Summary ThreeDSConfirm
// @Security bearerAuth
// @ID Magnetiq_ThreeDSConfirm
// @Accept json
// @Param request body ThreeDSRequestData true "ThreeDSRequestData"
// @Success 200 {object} Magnetiq_ThreeDSConfirm_Success
// @Failure 401 {object} Magnetiq_ThreeDSConfirm_Failure "goerrpkg.ErrorTypeAuthorization"
// @Failure 404 {object} Magnetiq_ThreeDSConfirm_Failure "goerrpkg.ErrorTypeNotFound"
// @Failure 400 {object} Magnetiq_ThreeDSConfirm_Failure "goerrpkg.ErrorTypeBadRequest"
// @Failure 500 {object} Magnetiq_ThreeDSConfirm_Failure "goerrpkg.ErrorTypeUnexpectedError"
// @Failure 400 {object} Magnetiq_ThreeDSConfirm_Failure "gin.ErrorTypeBind"
// @Failure 500 {object} Magnetiq_ThreeDSConfirm_Failure "Undefined error"
// @Produce json
// @Router /processing.magnetiq.magnetiq.Magnetiq/ThreeDSConfirm [put]
// @tags Magnetiq
func (s *ginMagnetiqServer) ThreeDSConfirm(c *gin.Context) error {
	ctx, span := dog.CreateSpan(c.Request.Context(), "GinMagnetiqServer_ThreeDSConfirm")
	defer span.End()

	var request ThreeDSRequestData
	if err := c.ShouldBindJSON(&request); err != nil {
		return err
	}

	ctx = metadata.NewOutgoingContext(ctx, metadata.MD(c.Request.Header))

	response, err := s.client.ThreeDSConfirm(ctx, &request)
	if err != nil {
		return err
	}

	c.JSON(http.StatusOK, &Magnetiq_ThreeDSConfirm_Success{
		Status:     true,
		Message:    "Success",
		StatusCode: 0,
		Result:     response,
	})
	return nil
}

type Magnetiq_ThreeDSResume_Success struct {
	// Status is the status of the response. Set true
	Status bool `json:"status"`
	// Message is a human-readable explanation of the status
	Message string `json:"message"`
	// StatusCode is the GoErr status. Set 0
	StatusCode int `json:"status_code"`
	// Result is the result of the request
	Result *ThreeDSResumeResponse `json:"result"`
}

type Magnetiq_ThreeDSResume_Failure struct {
	// Status is the status of the response. Set false
	Status bool `json:"status"`
	// Message is a human-readable explanation of the status
	Message string `json:"message"`
	// StatusCode is the GoErr status
	StatusCode int `json:"status_code"`
}

// ThreeDSResume
// @Summary ThreeDSResume
// @Security bearerAuth
// @ID Magnetiq_ThreeDSResume
// @Accept json
// @Param request body ThreeDSResumeRequest true "ThreeDSResumeRequest"
// @Success 200 {object} Magnetiq_ThreeDSResume_Success
// @Failure 401 {object} Magnetiq_ThreeDSResume_Failure "goerrpkg.ErrorTypeAuthorization"
// @Failure 404 {object} Magnetiq_ThreeDSResume_Failure "goerrpkg.ErrorTypeNotFound"
// @Failure 400 {object} Magnetiq_ThreeDSResume_Failure "goerrpkg.ErrorTypeBadRequest"
// @Failure 500 {object} Magnetiq_ThreeDSResume_Failure "goerrpkg.ErrorTypeUnexpectedError"
// @Failure 400 {object} Magnetiq_ThreeDSResume_Failure "gin.ErrorTypeBind"
// @Failure 500 {object} Magnetiq_ThreeDSResume_Failure "Undefined error"
// @Produce json
// @Router /processing.magnetiq.magnetiq.Magnetiq/ThreeDSResume [put]
// @tags Magnetiq
func (s *ginMagnetiqServer) ThreeDSResume(c *gin.Context) error {
	ctx, span := dog.CreateSpan(c.Request.Context(), "GinMagnetiqServer_ThreeDSResume")
	defer span.End()

	var request ThreeDSResumeRequest
	if err := c.ShouldBindJSON(&request); err != nil {
		return err
	}

	ctx = metadata.NewOutgoingContext(ctx, metadata.MD(c.Request.Header))

	response, err := s.client.ThreeDSResume(ctx, &request)
	if err != nil {
		return err
	}

	c.JSON(http.StatusOK, &Magnetiq_ThreeDSResume_Success{
		Status:     true,
		Message:    "Success",
		StatusCode: 0,
		Result:     response,
	})
	return nil
}

type Magnetiq_PayOut_Success struct {
	// Status is the status of the response. Set true
	Status bool `json:"status"`
	// Message is a human-readable explanation of the status
	Message string `json:"message"`
	// StatusCode is the GoErr status. Set 0
	StatusCode int `json:"status_code"`
	// Result is the result of the request
	Result *PayOutResponseData `json:"result"`
}

type Magnetiq_PayOut_Failure struct {
	// Status is the status of the response. Set false
	Status bool `json:"status"`
	// Message is a human-readable explanation of the status
	Message string `json:"message"`
	// StatusCode is the GoErr status
	StatusCode int `json:"status_code"`
}

// PayOut
// @Summary PayOut
// @Security bearerAuth
// @ID Magnetiq_PayOut
// @Accept json
// @Param request body PayOutRequestData true "PayOutRequestData"
// @Success 200 {object} Magnetiq_PayOut_Success
// @Failure 401 {object} Magnetiq_PayOut_Failure "goerrpkg.ErrorTypeAuthorization"
// @Failure 404 {object} Magnetiq_PayOut_Failure "goerrpkg.ErrorTypeNotFound"
// @Failure 400 {object} Magnetiq_PayOut_Failure "goerrpkg.ErrorTypeBadRequest"
// @Failure 500 {object} Magnetiq_PayOut_Failure "goerrpkg.ErrorTypeUnexpectedError"
// @Failure 400 {object} Magnetiq_PayOut_Failure "gin.ErrorTypeBind"
// @Failure 500 {object} Magnetiq_PayOut_Failure "Undefined error"
// @Produce json
// @Router /processing.magnetiq.magnetiq.Magnetiq/PayOut [put]
// @tags Magnetiq
func (s *ginMagnetiqServer) PayOut(c *gin.Context) error {
	ctx, span := dog.CreateSpan(c.Request.Context(), "GinMagnetiqServer_PayOut")
	defer span.End()

	var request PayOutRequestData
	if err := c.ShouldBindJSON(&request); err != nil {
		return err
	}

	ctx = metadata.NewOutgoingContext(ctx, metadata.MD(c.Request.Header))

	response, err := s.client.PayOut(ctx, &request)
	if err != nil {
		return err
	}

	c.JSON(http.StatusOK, &Magnetiq_PayOut_Success{
		Status:     true,
		Message:    "Success",
		StatusCode: 0,
		Result:     response,
	})
	return nil
}

type Magnetiq_GetBankTransactionStatus_Success struct {
	// Status is the status of the response. Set true
	Status bool `json:"status"`
	// Message is a human-readable explanation of the status
	Message string `json:"message"`
	// StatusCode is the GoErr status. Set 0
	StatusCode int `json:"status_code"`
	// Result is the result of the request
	Result *BankTransactionStatusResponse `json:"result"`
}

type Magnetiq_GetBankTransactionStatus_Failure struct {
	// Status is the status of the response. Set false
	Status bool `json:"status"`
	// Message is a human-readable explanation of the status
	Message string `json:"message"`
	// StatusCode is the GoErr status
	StatusCode int `json:"status_code"`
}

// GetBankTransactionStatus
// @Summary GetBankTransactionStatus
// @Security bearerAuth
// @ID Magnetiq_GetBankTransactionStatus
// @Accept json
// @Param request body BankTransactionStatusRequest true "BankTransactionStatusRequest"
// @Success 200 {object} Magnetiq_GetBankTransactionStatus_Success
// @Failure 401 {object} Magnetiq_GetBankTransactionStatus_Failure "goerrpkg.ErrorTypeAuthorization"
// @Failure 404 {object} Magnetiq_GetBankTransactionStatus_Failure "goerrpkg.ErrorTypeNotFound"
// @Failure 400 {object} Magnetiq_GetBankTransactionStatus_Failure "goerrpkg.ErrorTypeBadRequest"
// @Failure 500 {object} Magnetiq_GetBankTransactionStatus_Failure "goerrpkg.ErrorTypeUnexpectedError"
// @Failure 400 {object} Magnetiq_GetBankTransactionStatus_Failure "gin.ErrorTypeBind"
// @Failure 500 {object} Magnetiq_GetBankTransactionStatus_Failure "Undefined error"
// @Produce json
// @Router /processing.magnetiq.magnetiq.Magnetiq/GetBankTransactionStatus [put]
// @tags Magnetiq
func (s *ginMagnetiqServer) GetBankTransactionStatus(c *gin.Context) error {
	ctx, span := dog.CreateSpan(c.Request.Context(), "GinMagnetiqServer_GetBankTransactionStatus")
	defer span.End()

	var request BankTransactionStatusRequest
	if err := c.ShouldBindJSON(&request); err != nil {
		return err
	}

	ctx = metadata.NewOutgoingContext(ctx, metadata.MD(c.Request.Header))

	response, err := s.client.GetBankTransactionStatus(ctx, &request)
	if err != nil {
		return err
	}

	c.JSON(http.StatusOK, &Magnetiq_GetBankTransactionStatus_Success{
		Status:     true,
		Message:    "Success",
		StatusCode: 0,
		Result:     response,
	})
	return nil
}

type Magnetiq_GetBankTransactionStatusUnformated_Success struct {
	// Status is the status of the response. Set true
	Status bool `json:"status"`
	// Message is a human-readable explanation of the status
	Message string `json:"message"`
	// StatusCode is the GoErr status. Set 0
	StatusCode int `json:"status_code"`
	// Result is the result of the request
	Result *BankTransactionStatusUnformatedResponse `json:"result"`
}

type Magnetiq_GetBankTransactionStatusUnformated_Failure struct {
	// Status is the status of the response. Set false
	Status bool `json:"status"`
	// Message is a human-readable explanation of the status
	Message string `json:"message"`
	// StatusCode is the GoErr status
	StatusCode int `json:"status_code"`
}

// GetBankTransactionStatusUnformated
// @Summary GetBankTransactionStatusUnformated
// @Security bearerAuth
// @ID Magnetiq_GetBankTransactionStatusUnformated
// @Accept json
// @Param request body BankTransactionStatusUnformatedRequest true "BankTransactionStatusUnformatedRequest"
// @Success 200 {object} Magnetiq_GetBankTransactionStatusUnformated_Success
// @Failure 401 {object} Magnetiq_GetBankTransactionStatusUnformated_Failure "goerrpkg.ErrorTypeAuthorization"
// @Failure 404 {object} Magnetiq_GetBankTransactionStatusUnformated_Failure "goerrpkg.ErrorTypeNotFound"
// @Failure 400 {object} Magnetiq_GetBankTransactionStatusUnformated_Failure "goerrpkg.ErrorTypeBadRequest"
// @Failure 500 {object} Magnetiq_GetBankTransactionStatusUnformated_Failure "goerrpkg.ErrorTypeUnexpectedError"
// @Failure 400 {object} Magnetiq_GetBankTransactionStatusUnformated_Failure "gin.ErrorTypeBind"
// @Failure 500 {object} Magnetiq_GetBankTransactionStatusUnformated_Failure "Undefined error"
// @Produce json
// @Router /processing.magnetiq.magnetiq.Magnetiq/GetBankTransactionStatusUnformated [put]
// @tags Magnetiq
func (s *ginMagnetiqServer) GetBankTransactionStatusUnformated(c *gin.Context) error {
	ctx, span := dog.CreateSpan(c.Request.Context(), "GinMagnetiqServer_GetBankTransactionStatusUnformated")
	defer span.End()

	var request BankTransactionStatusUnformatedRequest
	if err := c.ShouldBindJSON(&request); err != nil {
		return err
	}

	ctx = metadata.NewOutgoingContext(ctx, metadata.MD(c.Request.Header))

	response, err := s.client.GetBankTransactionStatusUnformated(ctx, &request)
	if err != nil {
		return err
	}

	c.JSON(http.StatusOK, &Magnetiq_GetBankTransactionStatusUnformated_Success{
		Status:     true,
		Message:    "Success",
		StatusCode: 0,
		Result:     response,
	})
	return nil
}

type Magnetiq_Refund_Success struct {
	// Status is the status of the response. Set true
	Status bool `json:"status"`
	// Message is a human-readable explanation of the status
	Message string `json:"message"`
	// StatusCode is the GoErr status. Set 0
	StatusCode int `json:"status_code"`
	// Result is the result of the request
	Result *RefundResponse `json:"result"`
}

type Magnetiq_Refund_Failure struct {
	// Status is the status of the response. Set false
	Status bool `json:"status"`
	// Message is a human-readable explanation of the status
	Message string `json:"message"`
	// StatusCode is the GoErr status
	StatusCode int `json:"status_code"`
}

// Refund
// @Summary Refund
// @Security bearerAuth
// @ID Magnetiq_Refund
// @Accept json
// @Param request body RefundRequest true "RefundRequest"
// @Success 200 {object} Magnetiq_Refund_Success
// @Failure 401 {object} Magnetiq_Refund_Failure "goerrpkg.ErrorTypeAuthorization"
// @Failure 404 {object} Magnetiq_Refund_Failure "goerrpkg.ErrorTypeNotFound"
// @Failure 400 {object} Magnetiq_Refund_Failure "goerrpkg.ErrorTypeBadRequest"
// @Failure 500 {object} Magnetiq_Refund_Failure "goerrpkg.ErrorTypeUnexpectedError"
// @Failure 400 {object} Magnetiq_Refund_Failure "gin.ErrorTypeBind"
// @Failure 500 {object} Magnetiq_Refund_Failure "Undefined error"
// @Produce json
// @Router /processing.magnetiq.magnetiq.Magnetiq/Refund [put]
// @tags Magnetiq
func (s *ginMagnetiqServer) Refund(c *gin.Context) error {
	ctx, span := dog.CreateSpan(c.Request.Context(), "GinMagnetiqServer_Refund")
	defer span.End()

	var request RefundRequest
	if err := c.ShouldBindJSON(&request); err != nil {
		return err
	}

	ctx = metadata.NewOutgoingContext(ctx, metadata.MD(c.Request.Header))

	response, err := s.client.Refund(ctx, &request)
	if err != nil {
		return err
	}

	c.JSON(http.StatusOK, &Magnetiq_Refund_Success{
		Status:     true,
		Message:    "Success",
		StatusCode: 0,
		Result:     response,
	})
	return nil
}

type Magnetiq_GooglePay_Success struct {
	// Status is the status of the response. Set true
	Status bool `json:"status"`
	// Message is a human-readable explanation of the status
	Message string `json:"message"`
	// StatusCode is the GoErr status. Set 0
	StatusCode int `json:"status_code"`
	// Result is the result of the request
	Result *GooglePayResponseData `json:"result"`
}

type Magnetiq_GooglePay_Failure struct {
	// Status is the status of the response. Set false
	Status bool `json:"status"`
	// Message is a human-readable explanation of the status
	Message string `json:"message"`
	// StatusCode is the GoErr status
	StatusCode int `json:"status_code"`
}

// GooglePay
// @Summary GooglePay
// @Security bearerAuth
// @ID Magnetiq_GooglePay
// @Accept json
// @Param request body GooglePayRequestData true "GooglePayRequestData"
// @Success 200 {object} Magnetiq_GooglePay_Success
// @Failure 401 {object} Magnetiq_GooglePay_Failure "goerrpkg.ErrorTypeAuthorization"
// @Failure 404 {object} Magnetiq_GooglePay_Failure "goerrpkg.ErrorTypeNotFound"
// @Failure 400 {object} Magnetiq_GooglePay_Failure "goerrpkg.ErrorTypeBadRequest"
// @Failure 500 {object} Magnetiq_GooglePay_Failure "goerrpkg.ErrorTypeUnexpectedError"
// @Failure 400 {object} Magnetiq_GooglePay_Failure "gin.ErrorTypeBind"
// @Failure 500 {object} Magnetiq_GooglePay_Failure "Undefined error"
// @Produce json
// @Router /processing.magnetiq.magnetiq.Magnetiq/GooglePay [put]
// @tags Magnetiq
func (s *ginMagnetiqServer) GooglePay(c *gin.Context) error {
	ctx, span := dog.CreateSpan(c.Request.Context(), "GinMagnetiqServer_GooglePay")
	defer span.End()

	var request GooglePayRequestData
	if err := c.ShouldBindJSON(&request); err != nil {
		return err
	}

	ctx = metadata.NewOutgoingContext(ctx, metadata.MD(c.Request.Header))

	response, err := s.client.GooglePay(ctx, &request)
	if err != nil {
		return err
	}

	c.JSON(http.StatusOK, &Magnetiq_GooglePay_Success{
		Status:     true,
		Message:    "Success",
		StatusCode: 0,
		Result:     response,
	})
	return nil
}

type Magnetiq_ApplePay_Success struct {
	// Status is the status of the response. Set true
	Status bool `json:"status"`
	// Message is a human-readable explanation of the status
	Message string `json:"message"`
	// StatusCode is the GoErr status. Set 0
	StatusCode int `json:"status_code"`
	// Result is the result of the request
	Result *ApplePayResponseData `json:"result"`
}

type Magnetiq_ApplePay_Failure struct {
	// Status is the status of the response. Set false
	Status bool `json:"status"`
	// Message is a human-readable explanation of the status
	Message string `json:"message"`
	// StatusCode is the GoErr status
	StatusCode int `json:"status_code"`
}

// ApplePay
// @Summary ApplePay
// @Security bearerAuth
// @ID Magnetiq_ApplePay
// @Accept json
// @Param request body ApplePayRequestData true "ApplePayRequestData"
// @Success 200 {object} Magnetiq_ApplePay_Success
// @Failure 401 {object} Magnetiq_ApplePay_Failure "goerrpkg.ErrorTypeAuthorization"
// @Failure 404 {object} Magnetiq_ApplePay_Failure "goerrpkg.ErrorTypeNotFound"
// @Failure 400 {object} Magnetiq_ApplePay_Failure "goerrpkg.ErrorTypeBadRequest"
// @Failure 500 {object} Magnetiq_ApplePay_Failure "goerrpkg.ErrorTypeUnexpectedError"
// @Failure 400 {object} Magnetiq_ApplePay_Failure "gin.ErrorTypeBind"
// @Failure 500 {object} Magnetiq_ApplePay_Failure "Undefined error"
// @Produce json
// @Router /processing.magnetiq.magnetiq.Magnetiq/ApplePay [put]
// @tags Magnetiq
func (s *ginMagnetiqServer) ApplePay(c *gin.Context) error {
	ctx, span := dog.CreateSpan(c.Request.Context(), "GinMagnetiqServer_ApplePay")
	defer span.End()

	var request ApplePayRequestData
	if err := c.ShouldBindJSON(&request); err != nil {
		return err
	}

	ctx = metadata.NewOutgoingContext(ctx, metadata.MD(c.Request.Header))

	response, err := s.client.ApplePay(ctx, &request)
	if err != nil {
		return err
	}

	c.JSON(http.StatusOK, &Magnetiq_ApplePay_Success{
		Status:     true,
		Message:    "Success",
		StatusCode: 0,
		Result:     response,
	})
	return nil
}

type Magnetiq_TwoStagePayIn_Success struct {
	// Status is the status of the response. Set true
	Status bool `json:"status"`
	// Message is a human-readable explanation of the status
	Message string `json:"message"`
	// StatusCode is the GoErr status. Set 0
	StatusCode int `json:"status_code"`
	// Result is the result of the request
	Result *TwoStagePayInResponse `json:"result"`
}

type Magnetiq_TwoStagePayIn_Failure struct {
	// Status is the status of the response. Set false
	Status bool `json:"status"`
	// Message is a human-readable explanation of the status
	Message string `json:"message"`
	// StatusCode is the GoErr status
	StatusCode int `json:"status_code"`
}

// TwoStagePayIn
// @Summary TwoStagePayIn
// @Security bearerAuth
// @ID Magnetiq_TwoStagePayIn
// @Accept json
// @Param request body TwoStagePayInRequest true "TwoStagePayInRequest"
// @Success 200 {object} Magnetiq_TwoStagePayIn_Success
// @Failure 401 {object} Magnetiq_TwoStagePayIn_Failure "goerrpkg.ErrorTypeAuthorization"
// @Failure 404 {object} Magnetiq_TwoStagePayIn_Failure "goerrpkg.ErrorTypeNotFound"
// @Failure 400 {object} Magnetiq_TwoStagePayIn_Failure "goerrpkg.ErrorTypeBadRequest"
// @Failure 500 {object} Magnetiq_TwoStagePayIn_Failure "goerrpkg.ErrorTypeUnexpectedError"
// @Failure 400 {object} Magnetiq_TwoStagePayIn_Failure "gin.ErrorTypeBind"
// @Failure 500 {object} Magnetiq_TwoStagePayIn_Failure "Undefined error"
// @Produce json
// @Router /processing.magnetiq.magnetiq.Magnetiq/TwoStagePayIn [put]
// @tags Magnetiq
func (s *ginMagnetiqServer) TwoStagePayIn(c *gin.Context) error {
	ctx, span := dog.CreateSpan(c.Request.Context(), "GinMagnetiqServer_TwoStagePayIn")
	defer span.End()

	var request TwoStagePayInRequest
	if err := c.ShouldBindJSON(&request); err != nil {
		return err
	}

	ctx = metadata.NewOutgoingContext(ctx, metadata.MD(c.Request.Header))

	response, err := s.client.TwoStagePayIn(ctx, &request)
	if err != nil {
		return err
	}

	c.JSON(http.StatusOK, &Magnetiq_TwoStagePayIn_Success{
		Status:     true,
		Message:    "Success",
		StatusCode: 0,
		Result:     response,
	})
	return nil
}

type Magnetiq_Charge_Success struct {
	// Status is the status of the response. Set true
	Status bool `json:"status"`
	// Message is a human-readable explanation of the status
	Message string `json:"message"`
	// StatusCode is the GoErr status. Set 0
	StatusCode int `json:"status_code"`
	// Result is the result of the request
	Result *ChargeResponse `json:"result"`
}

type Magnetiq_Charge_Failure struct {
	// Status is the status of the response. Set false
	Status bool `json:"status"`
	// Message is a human-readable explanation of the status
	Message string `json:"message"`
	// StatusCode is the GoErr status
	StatusCode int `json:"status_code"`
}

// Charge
// @Summary Charge
// @Security bearerAuth
// @ID Magnetiq_Charge
// @Accept json
// @Param request body ChargeRequest true "ChargeRequest"
// @Success 200 {object} Magnetiq_Charge_Success
// @Failure 401 {object} Magnetiq_Charge_Failure "goerrpkg.ErrorTypeAuthorization"
// @Failure 404 {object} Magnetiq_Charge_Failure "goerrpkg.ErrorTypeNotFound"
// @Failure 400 {object} Magnetiq_Charge_Failure "goerrpkg.ErrorTypeBadRequest"
// @Failure 500 {object} Magnetiq_Charge_Failure "goerrpkg.ErrorTypeUnexpectedError"
// @Failure 400 {object} Magnetiq_Charge_Failure "gin.ErrorTypeBind"
// @Failure 500 {object} Magnetiq_Charge_Failure "Undefined error"
// @Produce json
// @Router /processing.magnetiq.magnetiq.Magnetiq/Charge [put]
// @tags Magnetiq
func (s *ginMagnetiqServer) Charge(c *gin.Context) error {
	ctx, span := dog.CreateSpan(c.Request.Context(), "GinMagnetiqServer_Charge")
	defer span.End()

	var request ChargeRequest
	if err := c.ShouldBindJSON(&request); err != nil {
		return err
	}

	ctx = metadata.NewOutgoingContext(ctx, metadata.MD(c.Request.Header))

	response, err := s.client.Charge(ctx, &request)
	if err != nil {
		return err
	}

	c.JSON(http.StatusOK, &Magnetiq_Charge_Success{
		Status:     true,
		Message:    "Success",
		StatusCode: 0,
		Result:     response,
	})
	return nil
}

type Magnetiq_Cancel_Success struct {
	// Status is the status of the response. Set true
	Status bool `json:"status"`
	// Message is a human-readable explanation of the status
	Message string `json:"message"`
	// StatusCode is the GoErr status. Set 0
	StatusCode int `json:"status_code"`
	// Result is the result of the request
	Result *CancelResponse `json:"result"`
}

type Magnetiq_Cancel_Failure struct {
	// Status is the status of the response. Set false
	Status bool `json:"status"`
	// Message is a human-readable explanation of the status
	Message string `json:"message"`
	// StatusCode is the GoErr status
	StatusCode int `json:"status_code"`
}

// Cancel
// @Summary Cancel
// @Security bearerAuth
// @ID Magnetiq_Cancel
// @Accept json
// @Param request body CancelRequest true "CancelRequest"
// @Success 200 {object} Magnetiq_Cancel_Success
// @Failure 401 {object} Magnetiq_Cancel_Failure "goerrpkg.ErrorTypeAuthorization"
// @Failure 404 {object} Magnetiq_Cancel_Failure "goerrpkg.ErrorTypeNotFound"
// @Failure 400 {object} Magnetiq_Cancel_Failure "goerrpkg.ErrorTypeBadRequest"
// @Failure 500 {object} Magnetiq_Cancel_Failure "goerrpkg.ErrorTypeUnexpectedError"
// @Failure 400 {object} Magnetiq_Cancel_Failure "gin.ErrorTypeBind"
// @Failure 500 {object} Magnetiq_Cancel_Failure "Undefined error"
// @Produce json
// @Router /processing.magnetiq.magnetiq.Magnetiq/Cancel [put]
// @tags Magnetiq
func (s *ginMagnetiqServer) Cancel(c *gin.Context) error {
	ctx, span := dog.CreateSpan(c.Request.Context(), "GinMagnetiqServer_Cancel")
	defer span.End()

	var request CancelRequest
	if err := c.ShouldBindJSON(&request); err != nil {
		return err
	}

	ctx = metadata.NewOutgoingContext(ctx, metadata.MD(c.Request.Header))

	response, err := s.client.Cancel(ctx, &request)
	if err != nil {
		return err
	}

	c.JSON(http.StatusOK, &Magnetiq_Cancel_Success{
		Status:     true,
		Message:    "Success",
		StatusCode: 0,
		Result:     response,
	})
	return nil
}

type Magnetiq_MakeToken_Success struct {
	// Status is the status of the response. Set true
	Status bool `json:"status"`
	// Message is a human-readable explanation of the status
	Message string `json:"message"`
	// StatusCode is the GoErr status. Set 0
	StatusCode int `json:"status_code"`
	// Result is the result of the request
	Result *PayInResponseData `json:"result"`
}

type Magnetiq_MakeToken_Failure struct {
	// Status is the status of the response. Set false
	Status bool `json:"status"`
	// Message is a human-readable explanation of the status
	Message string `json:"message"`
	// StatusCode is the GoErr status
	StatusCode int `json:"status_code"`
}

// MakeToken
// @Summary MakeToken
// @Security bearerAuth
// @ID Magnetiq_MakeToken
// @Accept json
// @Param request body PayInRequestData true "PayInRequestData"
// @Success 200 {object} Magnetiq_MakeToken_Success
// @Failure 401 {object} Magnetiq_MakeToken_Failure "goerrpkg.ErrorTypeAuthorization"
// @Failure 404 {object} Magnetiq_MakeToken_Failure "goerrpkg.ErrorTypeNotFound"
// @Failure 400 {object} Magnetiq_MakeToken_Failure "goerrpkg.ErrorTypeBadRequest"
// @Failure 500 {object} Magnetiq_MakeToken_Failure "goerrpkg.ErrorTypeUnexpectedError"
// @Failure 400 {object} Magnetiq_MakeToken_Failure "gin.ErrorTypeBind"
// @Failure 500 {object} Magnetiq_MakeToken_Failure "Undefined error"
// @Produce json
// @Router /processing.magnetiq.magnetiq.Magnetiq/MakeToken [put]
// @tags Magnetiq
func (s *ginMagnetiqServer) MakeToken(c *gin.Context) error {
	ctx, span := dog.CreateSpan(c.Request.Context(), "GinMagnetiqServer_MakeToken")
	defer span.End()

	var request PayInRequestData
	if err := c.ShouldBindJSON(&request); err != nil {
		return err
	}

	ctx = metadata.NewOutgoingContext(ctx, metadata.MD(c.Request.Header))

	response, err := s.client.MakeToken(ctx, &request)
	if err != nil {
		return err
	}

	c.JSON(http.StatusOK, &Magnetiq_MakeToken_Success{
		Status:     true,
		Message:    "Success",
		StatusCode: 0,
		Result:     response,
	})
	return nil
}

type Magnetiq_GetAcquirerIdentifier_Success struct {
	// Status is the status of the response. Set true
	Status bool `json:"status"`
	// Message is a human-readable explanation of the status
	Message string `json:"message"`
	// StatusCode is the GoErr status. Set 0
	StatusCode int `json:"status_code"`
	// Result is the result of the request
	Result *GetAcquirerIdentifierResponse `json:"result"`
}

type Magnetiq_GetAcquirerIdentifier_Failure struct {
	// Status is the status of the response. Set false
	Status bool `json:"status"`
	// Message is a human-readable explanation of the status
	Message string `json:"message"`
	// StatusCode is the GoErr status
	StatusCode int `json:"status_code"`
}

// GetAcquirerIdentifier
// @Summary GetAcquirerIdentifier
// @Security bearerAuth
// @ID Magnetiq_GetAcquirerIdentifier
// @Accept json
// @Param request body emptypb.Empty true "Empty"
// @Success 200 {object} Magnetiq_GetAcquirerIdentifier_Success
// @Failure 401 {object} Magnetiq_GetAcquirerIdentifier_Failure "goerrpkg.ErrorTypeAuthorization"
// @Failure 404 {object} Magnetiq_GetAcquirerIdentifier_Failure "goerrpkg.ErrorTypeNotFound"
// @Failure 400 {object} Magnetiq_GetAcquirerIdentifier_Failure "goerrpkg.ErrorTypeBadRequest"
// @Failure 500 {object} Magnetiq_GetAcquirerIdentifier_Failure "goerrpkg.ErrorTypeUnexpectedError"
// @Failure 400 {object} Magnetiq_GetAcquirerIdentifier_Failure "gin.ErrorTypeBind"
// @Failure 500 {object} Magnetiq_GetAcquirerIdentifier_Failure "Undefined error"
// @Produce json
// @Router /processing.magnetiq.magnetiq.Magnetiq/GetAcquirerIdentifier [put]
// @tags Magnetiq
func (s *ginMagnetiqServer) GetAcquirerIdentifier(c *gin.Context) error {
	ctx, span := dog.CreateSpan(c.Request.Context(), "GinMagnetiqServer_GetAcquirerIdentifier")
	defer span.End()

	var request emptypb.Empty
	if err := c.ShouldBindJSON(&request); err != nil {
		return err
	}

	ctx = metadata.NewOutgoingContext(ctx, metadata.MD(c.Request.Header))

	response, err := s.client.GetAcquirerIdentifier(ctx, &request)
	if err != nil {
		return err
	}

	c.JSON(http.StatusOK, &Magnetiq_GetAcquirerIdentifier_Success{
		Status:     true,
		Message:    "Success",
		StatusCode: 0,
		Result:     response,
	})
	return nil
}

type Magnetiq_ResolveVisaAlias_Success struct {
	// Status is the status of the response. Set true
	Status bool `json:"status"`
	// Message is a human-readable explanation of the status
	Message string `json:"message"`
	// StatusCode is the GoErr status. Set 0
	StatusCode int `json:"status_code"`
	// Result is the result of the request
	Result *ResolveVisaAliasResponse `json:"result"`
}

type Magnetiq_ResolveVisaAlias_Failure struct {
	// Status is the status of the response. Set false
	Status bool `json:"status"`
	// Message is a human-readable explanation of the status
	Message string `json:"message"`
	// StatusCode is the GoErr status
	StatusCode int `json:"status_code"`
}

// ResolveVisaAlias
// @Summary ResolveVisaAlias
// @Security bearerAuth
// @ID Magnetiq_ResolveVisaAlias
// @Accept json
// @Param request body ResolveVisaAliasRequest true "ResolveVisaAliasRequest"
// @Success 200 {object} Magnetiq_ResolveVisaAlias_Success
// @Failure 401 {object} Magnetiq_ResolveVisaAlias_Failure "goerrpkg.ErrorTypeAuthorization"
// @Failure 404 {object} Magnetiq_ResolveVisaAlias_Failure "goerrpkg.ErrorTypeNotFound"
// @Failure 400 {object} Magnetiq_ResolveVisaAlias_Failure "goerrpkg.ErrorTypeBadRequest"
// @Failure 500 {object} Magnetiq_ResolveVisaAlias_Failure "goerrpkg.ErrorTypeUnexpectedError"
// @Failure 400 {object} Magnetiq_ResolveVisaAlias_Failure "gin.ErrorTypeBind"
// @Failure 500 {object} Magnetiq_ResolveVisaAlias_Failure "Undefined error"
// @Produce json
// @Router /processing.magnetiq.magnetiq.Magnetiq/ResolveVisaAlias [put]
// @tags Magnetiq
func (s *ginMagnetiqServer) ResolveVisaAlias(c *gin.Context) error {
	ctx, span := dog.CreateSpan(c.Request.Context(), "GinMagnetiqServer_ResolveVisaAlias")
	defer span.End()

	var request ResolveVisaAliasRequest
	if err := c.ShouldBindJSON(&request); err != nil {
		return err
	}

	ctx = metadata.NewOutgoingContext(ctx, metadata.MD(c.Request.Header))

	response, err := s.client.ResolveVisaAlias(ctx, &request)
	if err != nil {
		return err
	}

	c.JSON(http.StatusOK, &Magnetiq_ResolveVisaAlias_Success{
		Status:     true,
		Message:    "Success",
		StatusCode: 0,
		Result:     response,
	})
	return nil
}

type Magnetiq_PayOutByPhone_Success struct {
	// Status is the status of the response. Set true
	Status bool `json:"status"`
	// Message is a human-readable explanation of the status
	Message string `json:"message"`
	// StatusCode is the GoErr status. Set 0
	StatusCode int `json:"status_code"`
	// Result is the result of the request
	Result *PayOutResponseByPhoneData `json:"result"`
}

type Magnetiq_PayOutByPhone_Failure struct {
	// Status is the status of the response. Set false
	Status bool `json:"status"`
	// Message is a human-readable explanation of the status
	Message string `json:"message"`
	// StatusCode is the GoErr status
	StatusCode int `json:"status_code"`
}

// PayOutByPhone
// @Summary PayOutByPhone
// @Security bearerAuth
// @ID Magnetiq_PayOutByPhone
// @Accept json
// @Param request body PayOutByPhoneRequestData true "PayOutByPhoneRequestData"
// @Success 200 {object} Magnetiq_PayOutByPhone_Success
// @Failure 401 {object} Magnetiq_PayOutByPhone_Failure "goerrpkg.ErrorTypeAuthorization"
// @Failure 404 {object} Magnetiq_PayOutByPhone_Failure "goerrpkg.ErrorTypeNotFound"
// @Failure 400 {object} Magnetiq_PayOutByPhone_Failure "goerrpkg.ErrorTypeBadRequest"
// @Failure 500 {object} Magnetiq_PayOutByPhone_Failure "goerrpkg.ErrorTypeUnexpectedError"
// @Failure 400 {object} Magnetiq_PayOutByPhone_Failure "gin.ErrorTypeBind"
// @Failure 500 {object} Magnetiq_PayOutByPhone_Failure "Undefined error"
// @Produce json
// @Router /processing.magnetiq.magnetiq.Magnetiq/PayOutByPhone [put]
// @tags Magnetiq
func (s *ginMagnetiqServer) PayOutByPhone(c *gin.Context) error {
	ctx, span := dog.CreateSpan(c.Request.Context(), "GinMagnetiqServer_PayOutByPhone")
	defer span.End()

	var request PayOutByPhoneRequestData
	if err := c.ShouldBindJSON(&request); err != nil {
		return err
	}

	ctx = metadata.NewOutgoingContext(ctx, metadata.MD(c.Request.Header))

	response, err := s.client.PayOutByPhone(ctx, &request)
	if err != nil {
		return err
	}

	c.JSON(http.StatusOK, &Magnetiq_PayOutByPhone_Success{
		Status:     true,
		Message:    "Success",
		StatusCode: 0,
		Result:     response,
	})
	return nil
}
