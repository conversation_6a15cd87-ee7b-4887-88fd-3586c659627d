// Code generated by MockGen. DO NOT EDIT.
// Source: http_logger.go

// Package http_logger is a generated GoMock package.
package http_logger

import (
	context "context"
	http "net/http"
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
)

// MockHttpLogger is a mock of HttpLogger interface.
type MockHttpLogger struct {
	ctrl     *gomock.Controller
	recorder *MockHttpLoggerMockRecorder
}

// MockHttpLoggerMockRecorder is the mock recorder for MockHttpLogger.
type MockHttpLoggerMockRecorder struct {
	mock *MockHttpLogger
}

// NewMockHttpLogger creates a new mock instance.
func NewMockHttpLogger(ctrl *gomock.Controller) *MockHttpLogger {
	mock := &MockHttpLogger{ctrl: ctrl}
	mock.recorder = &MockHttpLoggerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockHttpLogger) EXPECT() *MockHttpLoggerMockRecorder {
	return m.recorder
}

// Infow repomocks base method.
func (m *MockHttpLogger) Infow(ctx context.Context, res *http.Response, withoutRequest bool, keyAndValues ...interface{}) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, res, withoutRequest}
	for _, a := range keyAndValues {
		varargs = append(varargs, a)
	}
	m.ctrl.Call(m, "Infow", varargs...)
}

// Infow indicates an expected call of Infow.
func (mr *MockHttpLoggerMockRecorder) Infow(ctx, res, withoutRequest interface{}, keyAndValues ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, res, withoutRequest}, keyAndValues...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Infow", reflect.TypeOf((*MockHttpLogger)(nil).Infow), varargs...)
}
