package v1

import (
	"github.com/gin-gonic/gin"

	"git.local/sensitive/pkg/middlewares"
	"git.local/sensitive/sdk/dog"
)

func (h *Handler) initTransferStatusHandler(v1 *gin.RouterGroup) {
	v1.GET("transfer/statuses", middlewares.GinErrorHandle(h.getTransferStatuses))
}

// getTransferStatuses
// @Summary Получение всех статусов трансферов
// @Produce json
// @Success 200 {object} middlewares.Response[[]model.TransferStatus]
// @Failure 400 {object} middlewares.Response[middlewares.Empty]
// @Security bearerAuth
// @tags transfer-statuses
// @Router /api/v1/transfer/statuses [get]
func (h *Hand<PERSON>) getTransferStatuses(c *gin.Context) (err error) {
	ctx, span := dog.CreateSpan(c.Request.Context(), "Handler_getTransferStatuses")
	defer span.End()

	res, err := h.services.TransferStatus.GetAll(ctx)
	if err != nil {
		return err
	}

	return middlewares.Respond(res, c)
}
