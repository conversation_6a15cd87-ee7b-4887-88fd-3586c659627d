// Code generated by protoc-gen-go-mvp. DO NOT EDIT.
// versions:
// - protoc-gen-go-mvp v2.0.1
// created by mvp authors

package grpc

import (
	protojson "google.golang.org/protobuf/encoding/protojson"
)

func (m *CreateClientRequestV1) UnmarshalJSON(in []byte) error {
	val := new(CreateClientRequestV1)
	if err := (&protojson.UnmarshalOptions{
		DiscardUnknown: true,
	}).Unmarshal(in, val); err != nil {
		return err
	}

	*m = *val
	return nil
}

func (m *CreateClientRequestV1) MarshalJSON() ([]byte, error) {
	return (&protojson.MarshalOptions{
		UseProtoNames: true,
	}).Marshal(m)
}

func (m *CreateClientResponseV1) UnmarshalJSON(in []byte) error {
	val := new(CreateClientResponseV1)
	if err := (&protojson.UnmarshalOptions{
		DiscardUnknown: true,
	}).Unmarshal(in, val); err != nil {
		return err
	}

	*m = *val
	return nil
}

func (m *CreateClientResponseV1) MarshalJSON() ([]byte, error) {
	return (&protojson.MarshalOptions{
		UseProtoNames: true,
	}).Marshal(m)
}

func (m *CreatePayOutClientRequestV1) UnmarshalJSON(in []byte) error {
	val := new(CreatePayOutClientRequestV1)
	if err := (&protojson.UnmarshalOptions{
		DiscardUnknown: true,
	}).Unmarshal(in, val); err != nil {
		return err
	}

	*m = *val
	return nil
}

func (m *CreatePayOutClientRequestV1) MarshalJSON() ([]byte, error) {
	return (&protojson.MarshalOptions{
		UseProtoNames: true,
	}).Marshal(m)
}

func (m *CreatePayOutClientResponseV1) UnmarshalJSON(in []byte) error {
	val := new(CreatePayOutClientResponseV1)
	if err := (&protojson.UnmarshalOptions{
		DiscardUnknown: true,
	}).Unmarshal(in, val); err != nil {
		return err
	}

	*m = *val
	return nil
}

func (m *CreatePayOutClientResponseV1) MarshalJSON() ([]byte, error) {
	return (&protojson.MarshalOptions{
		UseProtoNames: true,
	}).Marshal(m)
}

func (m *ClientCard) UnmarshalJSON(in []byte) error {
	val := new(ClientCard)
	if err := (&protojson.UnmarshalOptions{
		DiscardUnknown: true,
	}).Unmarshal(in, val); err != nil {
		return err
	}

	*m = *val
	return nil
}

func (m *ClientCard) MarshalJSON() ([]byte, error) {
	return (&protojson.MarshalOptions{
		UseProtoNames: true,
	}).Marshal(m)
}

func (m *GetCardTokensRequestV1) UnmarshalJSON(in []byte) error {
	val := new(GetCardTokensRequestV1)
	if err := (&protojson.UnmarshalOptions{
		DiscardUnknown: true,
	}).Unmarshal(in, val); err != nil {
		return err
	}

	*m = *val
	return nil
}

func (m *GetCardTokensRequestV1) MarshalJSON() ([]byte, error) {
	return (&protojson.MarshalOptions{
		UseProtoNames: true,
	}).Marshal(m)
}

func (m *GetCardTokensResponseV1) UnmarshalJSON(in []byte) error {
	val := new(GetCardTokensResponseV1)
	if err := (&protojson.UnmarshalOptions{
		DiscardUnknown: true,
	}).Unmarshal(in, val); err != nil {
		return err
	}

	*m = *val
	return nil
}

func (m *GetCardTokensResponseV1) MarshalJSON() ([]byte, error) {
	return (&protojson.MarshalOptions{
		UseProtoNames: true,
	}).Marshal(m)
}

func (m *CardTokenV1) UnmarshalJSON(in []byte) error {
	val := new(CardTokenV1)
	if err := (&protojson.UnmarshalOptions{
		DiscardUnknown: true,
	}).Unmarshal(in, val); err != nil {
		return err
	}

	*m = *val
	return nil
}

func (m *CardTokenV1) MarshalJSON() ([]byte, error) {
	return (&protojson.MarshalOptions{
		UseProtoNames: true,
	}).Marshal(m)
}

func (m *GetOneClickPayInCardsRequestV1) UnmarshalJSON(in []byte) error {
	val := new(GetOneClickPayInCardsRequestV1)
	if err := (&protojson.UnmarshalOptions{
		DiscardUnknown: true,
	}).Unmarshal(in, val); err != nil {
		return err
	}

	*m = *val
	return nil
}

func (m *GetOneClickPayInCardsRequestV1) MarshalJSON() ([]byte, error) {
	return (&protojson.MarshalOptions{
		UseProtoNames: true,
	}).Marshal(m)
}

func (m *GetOneClickPayInCardsResponseV1) UnmarshalJSON(in []byte) error {
	val := new(GetOneClickPayInCardsResponseV1)
	if err := (&protojson.UnmarshalOptions{
		DiscardUnknown: true,
	}).Unmarshal(in, val); err != nil {
		return err
	}

	*m = *val
	return nil
}

func (m *GetOneClickPayInCardsResponseV1) MarshalJSON() ([]byte, error) {
	return (&protojson.MarshalOptions{
		UseProtoNames: true,
	}).Marshal(m)
}

func (m *GetOneClickPayOutCardsRequestV1) UnmarshalJSON(in []byte) error {
	val := new(GetOneClickPayOutCardsRequestV1)
	if err := (&protojson.UnmarshalOptions{
		DiscardUnknown: true,
	}).Unmarshal(in, val); err != nil {
		return err
	}

	*m = *val
	return nil
}

func (m *GetOneClickPayOutCardsRequestV1) MarshalJSON() ([]byte, error) {
	return (&protojson.MarshalOptions{
		UseProtoNames: true,
	}).Marshal(m)
}

func (m *GetOneClickPayOutCardsResponseV1) UnmarshalJSON(in []byte) error {
	val := new(GetOneClickPayOutCardsResponseV1)
	if err := (&protojson.UnmarshalOptions{
		DiscardUnknown: true,
	}).Unmarshal(in, val); err != nil {
		return err
	}

	*m = *val
	return nil
}

func (m *GetOneClickPayOutCardsResponseV1) MarshalJSON() ([]byte, error) {
	return (&protojson.MarshalOptions{
		UseProtoNames: true,
	}).Marshal(m)
}

func (m *GetEncryptedCardRequestV1) UnmarshalJSON(in []byte) error {
	val := new(GetEncryptedCardRequestV1)
	if err := (&protojson.UnmarshalOptions{
		DiscardUnknown: true,
	}).Unmarshal(in, val); err != nil {
		return err
	}

	*m = *val
	return nil
}

func (m *GetEncryptedCardRequestV1) MarshalJSON() ([]byte, error) {
	return (&protojson.MarshalOptions{
		UseProtoNames: true,
	}).Marshal(m)
}

func (m *GetEncryptedCardResponseV1) UnmarshalJSON(in []byte) error {
	val := new(GetEncryptedCardResponseV1)
	if err := (&protojson.UnmarshalOptions{
		DiscardUnknown: true,
	}).Unmarshal(in, val); err != nil {
		return err
	}

	*m = *val
	return nil
}

func (m *GetEncryptedCardResponseV1) MarshalJSON() ([]byte, error) {
	return (&protojson.MarshalOptions{
		UseProtoNames: true,
	}).Marshal(m)
}

func (m *GetEncryptedPayOutCardResponseV1) UnmarshalJSON(in []byte) error {
	val := new(GetEncryptedPayOutCardResponseV1)
	if err := (&protojson.UnmarshalOptions{
		DiscardUnknown: true,
	}).Unmarshal(in, val); err != nil {
		return err
	}

	*m = *val
	return nil
}

func (m *GetEncryptedPayOutCardResponseV1) MarshalJSON() ([]byte, error) {
	return (&protojson.MarshalOptions{
		UseProtoNames: true,
	}).Marshal(m)
}

func (m *GetPanByCardIdRequestV1) UnmarshalJSON(in []byte) error {
	val := new(GetPanByCardIdRequestV1)
	if err := (&protojson.UnmarshalOptions{
		DiscardUnknown: true,
	}).Unmarshal(in, val); err != nil {
		return err
	}

	*m = *val
	return nil
}

func (m *GetPanByCardIdRequestV1) MarshalJSON() ([]byte, error) {
	return (&protojson.MarshalOptions{
		UseProtoNames: true,
	}).Marshal(m)
}

func (m *GetPanByHashedIdRequestV1) UnmarshalJSON(in []byte) error {
	val := new(GetPanByHashedIdRequestV1)
	if err := (&protojson.UnmarshalOptions{
		DiscardUnknown: true,
	}).Unmarshal(in, val); err != nil {
		return err
	}

	*m = *val
	return nil
}

func (m *GetPanByHashedIdRequestV1) MarshalJSON() ([]byte, error) {
	return (&protojson.MarshalOptions{
		UseProtoNames: true,
	}).Marshal(m)
}

func (m *GetPanResponseV1) UnmarshalJSON(in []byte) error {
	val := new(GetPanResponseV1)
	if err := (&protojson.UnmarshalOptions{
		DiscardUnknown: true,
	}).Unmarshal(in, val); err != nil {
		return err
	}

	*m = *val
	return nil
}

func (m *GetPanResponseV1) MarshalJSON() ([]byte, error) {
	return (&protojson.MarshalOptions{
		UseProtoNames: true,
	}).Marshal(m)
}

func (m *GetClientListByVerificationRequestV1) UnmarshalJSON(in []byte) error {
	val := new(GetClientListByVerificationRequestV1)
	if err := (&protojson.UnmarshalOptions{
		DiscardUnknown: true,
	}).Unmarshal(in, val); err != nil {
		return err
	}

	*m = *val
	return nil
}

func (m *GetClientListByVerificationRequestV1) MarshalJSON() ([]byte, error) {
	return (&protojson.MarshalOptions{
		UseProtoNames: true,
	}).Marshal(m)
}

func (m *GetClientListByProjectClientRequestV1) UnmarshalJSON(in []byte) error {
	val := new(GetClientListByProjectClientRequestV1)
	if err := (&protojson.UnmarshalOptions{
		DiscardUnknown: true,
	}).Unmarshal(in, val); err != nil {
		return err
	}

	*m = *val
	return nil
}

func (m *GetClientListByProjectClientRequestV1) MarshalJSON() ([]byte, error) {
	return (&protojson.MarshalOptions{
		UseProtoNames: true,
	}).Marshal(m)
}

func (m *ProjectAndClientData) UnmarshalJSON(in []byte) error {
	val := new(ProjectAndClientData)
	if err := (&protojson.UnmarshalOptions{
		DiscardUnknown: true,
	}).Unmarshal(in, val); err != nil {
		return err
	}

	*m = *val
	return nil
}

func (m *ProjectAndClientData) MarshalJSON() ([]byte, error) {
	return (&protojson.MarshalOptions{
		UseProtoNames: true,
	}).Marshal(m)
}

func (m *GetClientListByProjectClientResponseV1) UnmarshalJSON(in []byte) error {
	val := new(GetClientListByProjectClientResponseV1)
	if err := (&protojson.UnmarshalOptions{
		DiscardUnknown: true,
	}).Unmarshal(in, val); err != nil {
		return err
	}

	*m = *val
	return nil
}

func (m *GetClientListByProjectClientResponseV1) MarshalJSON() ([]byte, error) {
	return (&protojson.MarshalOptions{
		UseProtoNames: true,
	}).Marshal(m)
}

func (m *GetClientListData) UnmarshalJSON(in []byte) error {
	val := new(GetClientListData)
	if err := (&protojson.UnmarshalOptions{
		DiscardUnknown: true,
	}).Unmarshal(in, val); err != nil {
		return err
	}

	*m = *val
	return nil
}

func (m *GetClientListData) MarshalJSON() ([]byte, error) {
	return (&protojson.MarshalOptions{
		UseProtoNames: true,
	}).Marshal(m)
}

func (m *CheckClientActivenessRequestV1) UnmarshalJSON(in []byte) error {
	val := new(CheckClientActivenessRequestV1)
	if err := (&protojson.UnmarshalOptions{
		DiscardUnknown: true,
	}).Unmarshal(in, val); err != nil {
		return err
	}

	*m = *val
	return nil
}

func (m *CheckClientActivenessRequestV1) MarshalJSON() ([]byte, error) {
	return (&protojson.MarshalOptions{
		UseProtoNames: true,
	}).Marshal(m)
}

func (m *GetCardByPanRequestV1) UnmarshalJSON(in []byte) error {
	val := new(GetCardByPanRequestV1)
	if err := (&protojson.UnmarshalOptions{
		DiscardUnknown: true,
	}).Unmarshal(in, val); err != nil {
		return err
	}

	*m = *val
	return nil
}

func (m *GetCardByPanRequestV1) MarshalJSON() ([]byte, error) {
	return (&protojson.MarshalOptions{
		UseProtoNames: true,
	}).Marshal(m)
}

func (m *GetCardByPanResponseV1) UnmarshalJSON(in []byte) error {
	val := new(GetCardByPanResponseV1)
	if err := (&protojson.UnmarshalOptions{
		DiscardUnknown: true,
	}).Unmarshal(in, val); err != nil {
		return err
	}

	*m = *val
	return nil
}

func (m *GetCardByPanResponseV1) MarshalJSON() ([]byte, error) {
	return (&protojson.MarshalOptions{
		UseProtoNames: true,
	}).Marshal(m)
}

func (m *DecryptPayInRequest) UnmarshalJSON(in []byte) error {
	val := new(DecryptPayInRequest)
	if err := (&protojson.UnmarshalOptions{
		DiscardUnknown: true,
	}).Unmarshal(in, val); err != nil {
		return err
	}

	*m = *val
	return nil
}

func (m *DecryptPayInRequest) MarshalJSON() ([]byte, error) {
	return (&protojson.MarshalOptions{
		UseProtoNames: true,
	}).Marshal(m)
}

func (m *DecryptPayOutRequest) UnmarshalJSON(in []byte) error {
	val := new(DecryptPayOutRequest)
	if err := (&protojson.UnmarshalOptions{
		DiscardUnknown: true,
	}).Unmarshal(in, val); err != nil {
		return err
	}

	*m = *val
	return nil
}

func (m *DecryptPayOutRequest) MarshalJSON() ([]byte, error) {
	return (&protojson.MarshalOptions{
		UseProtoNames: true,
	}).Marshal(m)
}

func (m *ReEncryptCardRequest) UnmarshalJSON(in []byte) error {
	val := new(ReEncryptCardRequest)
	if err := (&protojson.UnmarshalOptions{
		DiscardUnknown: true,
	}).Unmarshal(in, val); err != nil {
		return err
	}

	*m = *val
	return nil
}

func (m *ReEncryptCardRequest) MarshalJSON() ([]byte, error) {
	return (&protojson.MarshalOptions{
		UseProtoNames: true,
	}).Marshal(m)
}

func (m *DecryptPayInResponse) UnmarshalJSON(in []byte) error {
	val := new(DecryptPayInResponse)
	if err := (&protojson.UnmarshalOptions{
		DiscardUnknown: true,
	}).Unmarshal(in, val); err != nil {
		return err
	}

	*m = *val
	return nil
}

func (m *DecryptPayInResponse) MarshalJSON() ([]byte, error) {
	return (&protojson.MarshalOptions{
		UseProtoNames: true,
	}).Marshal(m)
}

func (m *DecryptPayOutResponse) UnmarshalJSON(in []byte) error {
	val := new(DecryptPayOutResponse)
	if err := (&protojson.UnmarshalOptions{
		DiscardUnknown: true,
	}).Unmarshal(in, val); err != nil {
		return err
	}

	*m = *val
	return nil
}

func (m *DecryptPayOutResponse) MarshalJSON() ([]byte, error) {
	return (&protojson.MarshalOptions{
		UseProtoNames: true,
	}).Marshal(m)
}

func (m *ReEncryptCardResponse) UnmarshalJSON(in []byte) error {
	val := new(ReEncryptCardResponse)
	if err := (&protojson.UnmarshalOptions{
		DiscardUnknown: true,
	}).Unmarshal(in, val); err != nil {
		return err
	}

	*m = *val
	return nil
}

func (m *ReEncryptCardResponse) MarshalJSON() ([]byte, error) {
	return (&protojson.MarshalOptions{
		UseProtoNames: true,
	}).Marshal(m)
}

func (m *GetPanInfoByProjectIdRequest) UnmarshalJSON(in []byte) error {
	val := new(GetPanInfoByProjectIdRequest)
	if err := (&protojson.UnmarshalOptions{
		DiscardUnknown: true,
	}).Unmarshal(in, val); err != nil {
		return err
	}

	*m = *val
	return nil
}

func (m *GetPanInfoByProjectIdRequest) MarshalJSON() ([]byte, error) {
	return (&protojson.MarshalOptions{
		UseProtoNames: true,
	}).Marshal(m)
}

func (m *GetPanInfoByProjectIdResponse) UnmarshalJSON(in []byte) error {
	val := new(GetPanInfoByProjectIdResponse)
	if err := (&protojson.UnmarshalOptions{
		DiscardUnknown: true,
	}).Unmarshal(in, val); err != nil {
		return err
	}

	*m = *val
	return nil
}

func (m *GetPanInfoByProjectIdResponse) MarshalJSON() ([]byte, error) {
	return (&protojson.MarshalOptions{
		UseProtoNames: true,
	}).Marshal(m)
}
