package repository

import (
	"gorm.io/gorm"
)

type Repositories struct {
	TransferDB                  Transferer
	TransferManageDB            TransferManager
	TransferStatusDB            TransferStatuser
	TransferTypeDB              TransferTyper
	AccountInfoDB               AccountInformer
	AccountStatementDB          AccountStatementer
	AccountScheduleDB           AccountScheduler
	AccountTypeDB               AccountTyper
	InTransferParsingScheduleDB InTransferParsingScheduler
	ProcessedOrderDB            ProcessedOrderer
	ProcessedOrderStatusDB      ProcessedOrderStatuser
	AccountBalanceHistoryDB     AccountBalanceHistorier
	AccountUpdaterDB            AccountUpdater
}

func NewRepositories(
	db *gorm.DB,
) *Repositories {
	return &Repositories{
		TransferDB:                  NewTransferDB(db),
		TransferStatusDB:            NewTransferStatusDB(db),
		TransferTypeDB:              NewTransferTypeDB(db),
		AccountInfoDB:               NewAccountInfoDB(db),
		TransferManageDB:            NewTransferManageDB(db),
		AccountStatementDB:          NewAccountStatementDB(db),
		AccountScheduleDB:           NewAccountSchedulerDB(db),
		InTransferParsingScheduleDB: NewInTransferParsingScheduleDB(db),
		ProcessedOrderDB:            NewProcessedOrderDB(db),
		ProcessedOrderStatusDB:      NewProcessedOrderStatusDB(db),
		AccountBalanceHistoryDB:     NewAccountBalanceHistoryDB(db),
		AccountTypeDB:               NewAccountTypeDB(db),
		AccountUpdaterDB:            NewAccountUpdatorDB(db),
	}
}
