// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.5
// 	protoc        v5.29.3
// source: inner/processing/events/notification.proto

package events

import (
	_ "git.local/sensitive/mvp/pb"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type SendEmail struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Receivers     []string               `protobuf:"bytes,1,rep,name=receivers,proto3" json:"receivers,omitempty"`
	Text          string                 `protobuf:"bytes,2,opt,name=text,proto3" json:"text,omitempty"`
	Sender        string                 `protobuf:"bytes,3,opt,name=sender,proto3" json:"sender,omitempty"`
	Title         string                 `protobuf:"bytes,4,opt,name=title,proto3" json:"title,omitempty"`
	Payload       map[string]string      `protobuf:"bytes,5,rep,name=payload,proto3" json:"payload,omitempty" protobuf_key:"bytes,1,opt,name=key" protobuf_val:"bytes,2,opt,name=value"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SendEmail) Reset() {
	*x = SendEmail{}
	mi := &file_inner_processing_events_notification_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SendEmail) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SendEmail) ProtoMessage() {}

func (x *SendEmail) ProtoReflect() protoreflect.Message {
	mi := &file_inner_processing_events_notification_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SendEmail.ProtoReflect.Descriptor instead.
func (*SendEmail) Descriptor() ([]byte, []int) {
	return file_inner_processing_events_notification_proto_rawDescGZIP(), []int{0}
}

func (x *SendEmail) GetReceivers() []string {
	if x != nil {
		return x.Receivers
	}
	return nil
}

func (x *SendEmail) GetText() string {
	if x != nil {
		return x.Text
	}
	return ""
}

func (x *SendEmail) GetSender() string {
	if x != nil {
		return x.Sender
	}
	return ""
}

func (x *SendEmail) GetTitle() string {
	if x != nil {
		return x.Title
	}
	return ""
}

func (x *SendEmail) GetPayload() map[string]string {
	if x != nil {
		return x.Payload
	}
	return nil
}

type SendEmailAttached struct {
	state          protoimpl.MessageState `protogen:"open.v1"`
	Receivers      []string               `protobuf:"bytes,1,rep,name=receivers,proto3" json:"receivers,omitempty"`
	Text           string                 `protobuf:"bytes,2,opt,name=text,proto3" json:"text,omitempty"`
	Sender         string                 `protobuf:"bytes,3,opt,name=sender,proto3" json:"sender,omitempty"`
	Title          string                 `protobuf:"bytes,4,opt,name=title,proto3" json:"title,omitempty"`
	Payload        map[string]string      `protobuf:"bytes,5,rep,name=payload,proto3" json:"payload,omitempty" protobuf_key:"bytes,1,opt,name=key" protobuf_val:"bytes,2,opt,name=value"`
	Attachment     []byte                 `protobuf:"bytes,6,opt,name=attachment,proto3" json:"attachment,omitempty"`
	AttachmentName string                 `protobuf:"bytes,7,opt,name=attachment_name,json=attachmentName,proto3" json:"attachment_name,omitempty"`
	unknownFields  protoimpl.UnknownFields
	sizeCache      protoimpl.SizeCache
}

func (x *SendEmailAttached) Reset() {
	*x = SendEmailAttached{}
	mi := &file_inner_processing_events_notification_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SendEmailAttached) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SendEmailAttached) ProtoMessage() {}

func (x *SendEmailAttached) ProtoReflect() protoreflect.Message {
	mi := &file_inner_processing_events_notification_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SendEmailAttached.ProtoReflect.Descriptor instead.
func (*SendEmailAttached) Descriptor() ([]byte, []int) {
	return file_inner_processing_events_notification_proto_rawDescGZIP(), []int{1}
}

func (x *SendEmailAttached) GetReceivers() []string {
	if x != nil {
		return x.Receivers
	}
	return nil
}

func (x *SendEmailAttached) GetText() string {
	if x != nil {
		return x.Text
	}
	return ""
}

func (x *SendEmailAttached) GetSender() string {
	if x != nil {
		return x.Sender
	}
	return ""
}

func (x *SendEmailAttached) GetTitle() string {
	if x != nil {
		return x.Title
	}
	return ""
}

func (x *SendEmailAttached) GetPayload() map[string]string {
	if x != nil {
		return x.Payload
	}
	return nil
}

func (x *SendEmailAttached) GetAttachment() []byte {
	if x != nil {
		return x.Attachment
	}
	return nil
}

func (x *SendEmailAttached) GetAttachmentName() string {
	if x != nil {
		return x.AttachmentName
	}
	return ""
}

type SendOtp struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Phone         string                 `protobuf:"bytes,1,opt,name=phone,proto3" json:"phone,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SendOtp) Reset() {
	*x = SendOtp{}
	mi := &file_inner_processing_events_notification_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SendOtp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SendOtp) ProtoMessage() {}

func (x *SendOtp) ProtoReflect() protoreflect.Message {
	mi := &file_inner_processing_events_notification_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SendOtp.ProtoReflect.Descriptor instead.
func (*SendOtp) Descriptor() ([]byte, []int) {
	return file_inner_processing_events_notification_proto_rawDescGZIP(), []int{2}
}

func (x *SendOtp) GetPhone() string {
	if x != nil {
		return x.Phone
	}
	return ""
}

type SendSms struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Phone         string                 `protobuf:"bytes,1,opt,name=phone,proto3" json:"phone,omitempty"`
	Text          string                 `protobuf:"bytes,2,opt,name=text,proto3" json:"text,omitempty"`
	Sender        string                 `protobuf:"bytes,3,opt,name=sender,proto3" json:"sender,omitempty"`
	Payload       map[string]string      `protobuf:"bytes,4,rep,name=payload,proto3" json:"payload,omitempty" protobuf_key:"bytes,1,opt,name=key" protobuf_val:"bytes,2,opt,name=value"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SendSms) Reset() {
	*x = SendSms{}
	mi := &file_inner_processing_events_notification_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SendSms) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SendSms) ProtoMessage() {}

func (x *SendSms) ProtoReflect() protoreflect.Message {
	mi := &file_inner_processing_events_notification_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SendSms.ProtoReflect.Descriptor instead.
func (*SendSms) Descriptor() ([]byte, []int) {
	return file_inner_processing_events_notification_proto_rawDescGZIP(), []int{3}
}

func (x *SendSms) GetPhone() string {
	if x != nil {
		return x.Phone
	}
	return ""
}

func (x *SendSms) GetText() string {
	if x != nil {
		return x.Text
	}
	return ""
}

func (x *SendSms) GetSender() string {
	if x != nil {
		return x.Sender
	}
	return ""
}

func (x *SendSms) GetPayload() map[string]string {
	if x != nil {
		return x.Payload
	}
	return nil
}

var File_inner_processing_events_notification_proto protoreflect.FileDescriptor

var file_inner_processing_events_notification_proto_rawDesc = string([]byte{
	0x0a, 0x2a, 0x69, 0x6e, 0x6e, 0x65, 0x72, 0x2f, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69,
	0x6e, 0x67, 0x2f, 0x65, 0x76, 0x65, 0x6e, 0x74, 0x73, 0x2f, 0x6e, 0x6f, 0x74, 0x69, 0x66, 0x69,
	0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x16, 0x6d, 0x76,
	0x70, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f, 0x65, 0x76, 0x65, 0x6e, 0x74, 0x73, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x22, 0xe1, 0x01, 0x0a, 0x09, 0x53, 0x65, 0x6e, 0x64, 0x45, 0x6d, 0x61,
	0x69, 0x6c, 0x12, 0x1c, 0x0a, 0x09, 0x72, 0x65, 0x63, 0x65, 0x69, 0x76, 0x65, 0x72, 0x73, 0x18,
	0x01, 0x20, 0x03, 0x28, 0x09, 0x52, 0x09, 0x72, 0x65, 0x63, 0x65, 0x69, 0x76, 0x65, 0x72, 0x73,
	0x12, 0x12, 0x0a, 0x04, 0x74, 0x65, 0x78, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04,
	0x74, 0x65, 0x78, 0x74, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x65, 0x6e, 0x64, 0x65, 0x72, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x73, 0x65, 0x6e, 0x64, 0x65, 0x72, 0x12, 0x14, 0x0a, 0x05,
	0x74, 0x69, 0x74, 0x6c, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x74, 0x69, 0x74,
	0x6c, 0x65, 0x12, 0x31, 0x0a, 0x07, 0x70, 0x61, 0x79, 0x6c, 0x6f, 0x61, 0x64, 0x18, 0x05, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x17, 0x2e, 0x53, 0x65, 0x6e, 0x64, 0x45, 0x6d, 0x61, 0x69, 0x6c, 0x2e,
	0x50, 0x61, 0x79, 0x6c, 0x6f, 0x61, 0x64, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x07, 0x70, 0x61,
	0x79, 0x6c, 0x6f, 0x61, 0x64, 0x1a, 0x3a, 0x0a, 0x0c, 0x50, 0x61, 0x79, 0x6c, 0x6f, 0x61, 0x64,
	0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38,
	0x01, 0x3a, 0x05, 0xb8, 0xe9, 0xe4, 0x03, 0x01, 0x22, 0xba, 0x02, 0x0a, 0x11, 0x53, 0x65, 0x6e,
	0x64, 0x45, 0x6d, 0x61, 0x69, 0x6c, 0x41, 0x74, 0x74, 0x61, 0x63, 0x68, 0x65, 0x64, 0x12, 0x1c,
	0x0a, 0x09, 0x72, 0x65, 0x63, 0x65, 0x69, 0x76, 0x65, 0x72, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28,
	0x09, 0x52, 0x09, 0x72, 0x65, 0x63, 0x65, 0x69, 0x76, 0x65, 0x72, 0x73, 0x12, 0x12, 0x0a, 0x04,
	0x74, 0x65, 0x78, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x74, 0x65, 0x78, 0x74,
	0x12, 0x16, 0x0a, 0x06, 0x73, 0x65, 0x6e, 0x64, 0x65, 0x72, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x06, 0x73, 0x65, 0x6e, 0x64, 0x65, 0x72, 0x12, 0x14, 0x0a, 0x05, 0x74, 0x69, 0x74, 0x6c,
	0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x12, 0x39,
	0x0a, 0x07, 0x70, 0x61, 0x79, 0x6c, 0x6f, 0x61, 0x64, 0x18, 0x05, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x1f, 0x2e, 0x53, 0x65, 0x6e, 0x64, 0x45, 0x6d, 0x61, 0x69, 0x6c, 0x41, 0x74, 0x74, 0x61, 0x63,
	0x68, 0x65, 0x64, 0x2e, 0x50, 0x61, 0x79, 0x6c, 0x6f, 0x61, 0x64, 0x45, 0x6e, 0x74, 0x72, 0x79,
	0x52, 0x07, 0x70, 0x61, 0x79, 0x6c, 0x6f, 0x61, 0x64, 0x12, 0x1e, 0x0a, 0x0a, 0x61, 0x74, 0x74,
	0x61, 0x63, 0x68, 0x6d, 0x65, 0x6e, 0x74, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x0a, 0x61,
	0x74, 0x74, 0x61, 0x63, 0x68, 0x6d, 0x65, 0x6e, 0x74, 0x12, 0x27, 0x0a, 0x0f, 0x61, 0x74, 0x74,
	0x61, 0x63, 0x68, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x07, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0e, 0x61, 0x74, 0x74, 0x61, 0x63, 0x68, 0x6d, 0x65, 0x6e, 0x74, 0x4e, 0x61,
	0x6d, 0x65, 0x1a, 0x3a, 0x0a, 0x0c, 0x50, 0x61, 0x79, 0x6c, 0x6f, 0x61, 0x64, 0x45, 0x6e, 0x74,
	0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x3a, 0x05,
	0xb8, 0xe9, 0xe4, 0x03, 0x01, 0x22, 0x26, 0x0a, 0x07, 0x53, 0x65, 0x6e, 0x64, 0x4f, 0x74, 0x70,
	0x12, 0x14, 0x0a, 0x05, 0x70, 0x68, 0x6f, 0x6e, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x05, 0x70, 0x68, 0x6f, 0x6e, 0x65, 0x3a, 0x05, 0xb8, 0xe9, 0xe4, 0x03, 0x01, 0x22, 0xbf, 0x01,
	0x0a, 0x07, 0x53, 0x65, 0x6e, 0x64, 0x53, 0x6d, 0x73, 0x12, 0x14, 0x0a, 0x05, 0x70, 0x68, 0x6f,
	0x6e, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x70, 0x68, 0x6f, 0x6e, 0x65, 0x12,
	0x12, 0x0a, 0x04, 0x74, 0x65, 0x78, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x74,
	0x65, 0x78, 0x74, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x65, 0x6e, 0x64, 0x65, 0x72, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x06, 0x73, 0x65, 0x6e, 0x64, 0x65, 0x72, 0x12, 0x2f, 0x0a, 0x07, 0x70,
	0x61, 0x79, 0x6c, 0x6f, 0x61, 0x64, 0x18, 0x04, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x53,
	0x65, 0x6e, 0x64, 0x53, 0x6d, 0x73, 0x2e, 0x50, 0x61, 0x79, 0x6c, 0x6f, 0x61, 0x64, 0x45, 0x6e,
	0x74, 0x72, 0x79, 0x52, 0x07, 0x70, 0x61, 0x79, 0x6c, 0x6f, 0x61, 0x64, 0x1a, 0x3a, 0x0a, 0x0c,
	0x50, 0x61, 0x79, 0x6c, 0x6f, 0x61, 0x64, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03,
	0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14,
	0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x76,
	0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x3a, 0x05, 0xb8, 0xe9, 0xe4, 0x03, 0x01, 0x42,
	0x2f, 0x5a, 0x2d, 0x67, 0x69, 0x74, 0x2e, 0x6c, 0x6f, 0x63, 0x61, 0x6c, 0x2f, 0x73, 0x65, 0x6e,
	0x73, 0x69, 0x74, 0x69, 0x76, 0x65, 0x2f, 0x69, 0x6e, 0x6e, 0x65, 0x72, 0x70, 0x62, 0x2f, 0x70,
	0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x2f, 0x65, 0x76, 0x65, 0x6e, 0x74, 0x73,
	0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
})

var (
	file_inner_processing_events_notification_proto_rawDescOnce sync.Once
	file_inner_processing_events_notification_proto_rawDescData []byte
)

func file_inner_processing_events_notification_proto_rawDescGZIP() []byte {
	file_inner_processing_events_notification_proto_rawDescOnce.Do(func() {
		file_inner_processing_events_notification_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_inner_processing_events_notification_proto_rawDesc), len(file_inner_processing_events_notification_proto_rawDesc)))
	})
	return file_inner_processing_events_notification_proto_rawDescData
}

var file_inner_processing_events_notification_proto_msgTypes = make([]protoimpl.MessageInfo, 7)
var file_inner_processing_events_notification_proto_goTypes = []any{
	(*SendEmail)(nil),         // 0: SendEmail
	(*SendEmailAttached)(nil), // 1: SendEmailAttached
	(*SendOtp)(nil),           // 2: SendOtp
	(*SendSms)(nil),           // 3: SendSms
	nil,                       // 4: SendEmail.PayloadEntry
	nil,                       // 5: SendEmailAttached.PayloadEntry
	nil,                       // 6: SendSms.PayloadEntry
}
var file_inner_processing_events_notification_proto_depIdxs = []int32{
	4, // 0: SendEmail.payload:type_name -> SendEmail.PayloadEntry
	5, // 1: SendEmailAttached.payload:type_name -> SendEmailAttached.PayloadEntry
	6, // 2: SendSms.payload:type_name -> SendSms.PayloadEntry
	3, // [3:3] is the sub-list for method output_type
	3, // [3:3] is the sub-list for method input_type
	3, // [3:3] is the sub-list for extension type_name
	3, // [3:3] is the sub-list for extension extendee
	0, // [0:3] is the sub-list for field type_name
}

func init() { file_inner_processing_events_notification_proto_init() }
func file_inner_processing_events_notification_proto_init() {
	if File_inner_processing_events_notification_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_inner_processing_events_notification_proto_rawDesc), len(file_inner_processing_events_notification_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   7,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_inner_processing_events_notification_proto_goTypes,
		DependencyIndexes: file_inner_processing_events_notification_proto_depIdxs,
		MessageInfos:      file_inner_processing_events_notification_proto_msgTypes,
	}.Build()
	File_inner_processing_events_notification_proto = out.File
	file_inner_processing_events_notification_proto_goTypes = nil
	file_inner_processing_events_notification_proto_depIdxs = nil
}
