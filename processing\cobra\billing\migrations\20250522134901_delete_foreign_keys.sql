ALTER TABLE billing.operations DROP CONSTRAINT operations_balance_type_id_fkey
ALTER TABLE billing.operations DROP CONSTRAINT operations_status_id_fkey
ALTER TABLE billing.operations DROP CONSTRAINT operations_type_id_fkey

ALTER TABLE billing.merchant_owner_type_relation DROP CONSTRAINT merchant_owner_type_relation_owner_type_id_fkey

ALTER TABLE billing.balances DROP CONSTRAINT balances_type_id_fkey

ALTER TABLE billing.balance_credits DROP CONSTRAINT balance_credits_status_id_fkey
ALTER TABLE billing.balance_credits DROP CONSTRAINT balance_credits_type_id_fkey

ALTER TABLE billing.balance_owners DROP CONSTRAINT balance_owners_earn_type_id_fkey
ALTER TABLE billing.balance_owners DROP CONSTRAINT balance_owners_status_id_fkey

--

ALTER TABLE billing.operation_types RENAME TO _operation_types;
ALTER TABLE billing.operation_type_groups RENAME TO _operation_type_groups;
ALTER TABLE billing.operation_type_group_relation RENAME TO _operation_type_group_relation;
ALTER TABLE billing.operation_statuses RENAME TO _operation_statuses;
ALTER TABLE billing.operation_balance_types RENAME TO _operation_balance_types;
ALTER TABLE billing.earn_types RENAME TO _earn_types;
ALTER TABLE billing.balance_owner_types RENAME TO _balance_owner_types;
ALTER TABLE billing.balance_owner_statuses RENAME TO _balance_owner_statuses;
ALTER TABLE billing.balance_credit_statuses RENAME TO _balance_credit_statuses;
ALTER TABLE billing.balance_account_statuses RENAME TO _balance_account_statuses;
