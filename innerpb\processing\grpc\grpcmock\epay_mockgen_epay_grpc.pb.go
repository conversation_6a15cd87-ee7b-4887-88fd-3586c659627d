// Code generated by MockGen. DO NOT EDIT.
// Source: epay_grpc.pb.go

// Package grpcmock is a generated GoMock package.
package grpcmock

import (
	context "context"
	reflect "reflect"

	grpc "git.local/sensitive/innerpb/processing/grpc"
	gomock "github.com/golang/mock/gomock"
	grpc0 "google.golang.org/grpc"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
)

// MockEpayClient is a mock of EpayClient interface.
type MockEpayClient struct {
	ctrl     *gomock.Controller
	recorder *MockEpayClientMockRecorder
}

// MockEpayClientMockRecorder is the mock recorder for MockEpayClient.
type MockEpayClientMockRecorder struct {
	mock *MockEpayClient
}

// NewMockEpayClient creates a new mock instance.
func NewMockEpayClient(ctrl *gomock.Controller) *MockEpayClient {
	mock := &MockEpayClient{ctrl: ctrl}
	mock.recorder = &MockEpayClientMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockEpayClient) EXPECT() *MockEpayClientMockRecorder {
	return m.recorder
}

// ApplePay mocks base method.
func (m *MockEpayClient) ApplePay(ctx context.Context, in *grpc.ApplePayRequestData, opts ...grpc0.CallOption) (*grpc.ApplePayResponseData, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "ApplePay", varargs...)
	ret0, _ := ret[0].(*grpc.ApplePayResponseData)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ApplePay indicates an expected call of ApplePay.
func (mr *MockEpayClientMockRecorder) ApplePay(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ApplePay", reflect.TypeOf((*MockEpayClient)(nil).ApplePay), varargs...)
}

// Cancel mocks base method.
func (m *MockEpayClient) Cancel(ctx context.Context, in *grpc.CancelRequest, opts ...grpc0.CallOption) (*grpc.CancelResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "Cancel", varargs...)
	ret0, _ := ret[0].(*grpc.CancelResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Cancel indicates an expected call of Cancel.
func (mr *MockEpayClientMockRecorder) Cancel(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Cancel", reflect.TypeOf((*MockEpayClient)(nil).Cancel), varargs...)
}

// Charge mocks base method.
func (m *MockEpayClient) Charge(ctx context.Context, in *grpc.ChargeRequest, opts ...grpc0.CallOption) (*grpc.ChargeResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "Charge", varargs...)
	ret0, _ := ret[0].(*grpc.ChargeResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Charge indicates an expected call of Charge.
func (mr *MockEpayClientMockRecorder) Charge(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Charge", reflect.TypeOf((*MockEpayClient)(nil).Charge), varargs...)
}

// GetAcquirerIdentifier mocks base method.
func (m *MockEpayClient) GetAcquirerIdentifier(ctx context.Context, in *emptypb.Empty, opts ...grpc0.CallOption) (*grpc.GetAcquirerIdentifierResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetAcquirerIdentifier", varargs...)
	ret0, _ := ret[0].(*grpc.GetAcquirerIdentifierResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAcquirerIdentifier indicates an expected call of GetAcquirerIdentifier.
func (mr *MockEpayClientMockRecorder) GetAcquirerIdentifier(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAcquirerIdentifier", reflect.TypeOf((*MockEpayClient)(nil).GetAcquirerIdentifier), varargs...)
}

// GetBankTransactionStatus mocks base method.
func (m *MockEpayClient) GetBankTransactionStatus(ctx context.Context, in *grpc.BankTransactionStatusRequest, opts ...grpc0.CallOption) (*grpc.BankTransactionStatusResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetBankTransactionStatus", varargs...)
	ret0, _ := ret[0].(*grpc.BankTransactionStatusResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetBankTransactionStatus indicates an expected call of GetBankTransactionStatus.
func (mr *MockEpayClientMockRecorder) GetBankTransactionStatus(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetBankTransactionStatus", reflect.TypeOf((*MockEpayClient)(nil).GetBankTransactionStatus), varargs...)
}

// GetBankTransactionStatusUnformated mocks base method.
func (m *MockEpayClient) GetBankTransactionStatusUnformated(ctx context.Context, in *grpc.BankTransactionStatusUnformatedRequest, opts ...grpc0.CallOption) (*grpc.BankTransactionStatusUnformatedResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetBankTransactionStatusUnformated", varargs...)
	ret0, _ := ret[0].(*grpc.BankTransactionStatusUnformatedResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetBankTransactionStatusUnformated indicates an expected call of GetBankTransactionStatusUnformated.
func (mr *MockEpayClientMockRecorder) GetBankTransactionStatusUnformated(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetBankTransactionStatusUnformated", reflect.TypeOf((*MockEpayClient)(nil).GetBankTransactionStatusUnformated), varargs...)
}

// GooglePay mocks base method.
func (m *MockEpayClient) GooglePay(ctx context.Context, in *grpc.GooglePayRequestData, opts ...grpc0.CallOption) (*grpc.GooglePayResponseData, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GooglePay", varargs...)
	ret0, _ := ret[0].(*grpc.GooglePayResponseData)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GooglePay indicates an expected call of GooglePay.
func (mr *MockEpayClientMockRecorder) GooglePay(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GooglePay", reflect.TypeOf((*MockEpayClient)(nil).GooglePay), varargs...)
}

// MakeToken mocks base method.
func (m *MockEpayClient) MakeToken(ctx context.Context, in *grpc.PayInRequestData, opts ...grpc0.CallOption) (*grpc.PayInResponseData, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "MakeToken", varargs...)
	ret0, _ := ret[0].(*grpc.PayInResponseData)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// MakeToken indicates an expected call of MakeToken.
func (mr *MockEpayClientMockRecorder) MakeToken(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "MakeToken", reflect.TypeOf((*MockEpayClient)(nil).MakeToken), varargs...)
}

// OneClickPayIn mocks base method.
func (m *MockEpayClient) OneClickPayIn(ctx context.Context, in *grpc.OneClickPayInRequestData, opts ...grpc0.CallOption) (*grpc.PayInResponseData, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "OneClickPayIn", varargs...)
	ret0, _ := ret[0].(*grpc.PayInResponseData)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// OneClickPayIn indicates an expected call of OneClickPayIn.
func (mr *MockEpayClientMockRecorder) OneClickPayIn(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "OneClickPayIn", reflect.TypeOf((*MockEpayClient)(nil).OneClickPayIn), varargs...)
}

// PayIn mocks base method.
func (m *MockEpayClient) PayIn(ctx context.Context, in *grpc.PayInRequestData, opts ...grpc0.CallOption) (*grpc.PayInResponseData, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "PayIn", varargs...)
	ret0, _ := ret[0].(*grpc.PayInResponseData)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// PayIn indicates an expected call of PayIn.
func (mr *MockEpayClientMockRecorder) PayIn(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "PayIn", reflect.TypeOf((*MockEpayClient)(nil).PayIn), varargs...)
}

// PayOut mocks base method.
func (m *MockEpayClient) PayOut(ctx context.Context, in *grpc.PayOutRequestData, opts ...grpc0.CallOption) (*grpc.PayOutResponseData, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "PayOut", varargs...)
	ret0, _ := ret[0].(*grpc.PayOutResponseData)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// PayOut indicates an expected call of PayOut.
func (mr *MockEpayClientMockRecorder) PayOut(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "PayOut", reflect.TypeOf((*MockEpayClient)(nil).PayOut), varargs...)
}

// PayOutByPhone mocks base method.
func (m *MockEpayClient) PayOutByPhone(ctx context.Context, in *grpc.PayOutByPhoneRequestData, opts ...grpc0.CallOption) (*grpc.PayOutResponseByPhoneData, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "PayOutByPhone", varargs...)
	ret0, _ := ret[0].(*grpc.PayOutResponseByPhoneData)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// PayOutByPhone indicates an expected call of PayOutByPhone.
func (mr *MockEpayClientMockRecorder) PayOutByPhone(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "PayOutByPhone", reflect.TypeOf((*MockEpayClient)(nil).PayOutByPhone), varargs...)
}

// Refund mocks base method.
func (m *MockEpayClient) Refund(ctx context.Context, in *grpc.RefundRequest, opts ...grpc0.CallOption) (*grpc.RefundResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "Refund", varargs...)
	ret0, _ := ret[0].(*grpc.RefundResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Refund indicates an expected call of Refund.
func (mr *MockEpayClientMockRecorder) Refund(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Refund", reflect.TypeOf((*MockEpayClient)(nil).Refund), varargs...)
}

// ResolveVisaAlias mocks base method.
func (m *MockEpayClient) ResolveVisaAlias(ctx context.Context, in *grpc.ResolveVisaAliasRequest, opts ...grpc0.CallOption) (*grpc.ResolveVisaAliasResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "ResolveVisaAlias", varargs...)
	ret0, _ := ret[0].(*grpc.ResolveVisaAliasResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ResolveVisaAlias indicates an expected call of ResolveVisaAlias.
func (mr *MockEpayClientMockRecorder) ResolveVisaAlias(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ResolveVisaAlias", reflect.TypeOf((*MockEpayClient)(nil).ResolveVisaAlias), varargs...)
}

// ThreeDSConfirm mocks base method.
func (m *MockEpayClient) ThreeDSConfirm(ctx context.Context, in *grpc.ThreeDSRequestData, opts ...grpc0.CallOption) (*grpc.ThreeDSResponseData, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "ThreeDSConfirm", varargs...)
	ret0, _ := ret[0].(*grpc.ThreeDSResponseData)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ThreeDSConfirm indicates an expected call of ThreeDSConfirm.
func (mr *MockEpayClientMockRecorder) ThreeDSConfirm(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ThreeDSConfirm", reflect.TypeOf((*MockEpayClient)(nil).ThreeDSConfirm), varargs...)
}

// ThreeDSResume mocks base method.
func (m *MockEpayClient) ThreeDSResume(ctx context.Context, in *grpc.ThreeDSResumeRequest, opts ...grpc0.CallOption) (*grpc.ThreeDSResumeResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "ThreeDSResume", varargs...)
	ret0, _ := ret[0].(*grpc.ThreeDSResumeResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ThreeDSResume indicates an expected call of ThreeDSResume.
func (mr *MockEpayClientMockRecorder) ThreeDSResume(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ThreeDSResume", reflect.TypeOf((*MockEpayClient)(nil).ThreeDSResume), varargs...)
}

// TwoStagePayIn mocks base method.
func (m *MockEpayClient) TwoStagePayIn(ctx context.Context, in *grpc.TwoStagePayInRequest, opts ...grpc0.CallOption) (*grpc.TwoStagePayInResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "TwoStagePayIn", varargs...)
	ret0, _ := ret[0].(*grpc.TwoStagePayInResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// TwoStagePayIn indicates an expected call of TwoStagePayIn.
func (mr *MockEpayClientMockRecorder) TwoStagePayIn(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "TwoStagePayIn", reflect.TypeOf((*MockEpayClient)(nil).TwoStagePayIn), varargs...)
}

// MockEpayServer is a mock of EpayServer interface.
type MockEpayServer struct {
	ctrl     *gomock.Controller
	recorder *MockEpayServerMockRecorder
}

// MockEpayServerMockRecorder is the mock recorder for MockEpayServer.
type MockEpayServerMockRecorder struct {
	mock *MockEpayServer
}

// NewMockEpayServer creates a new mock instance.
func NewMockEpayServer(ctrl *gomock.Controller) *MockEpayServer {
	mock := &MockEpayServer{ctrl: ctrl}
	mock.recorder = &MockEpayServerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockEpayServer) EXPECT() *MockEpayServerMockRecorder {
	return m.recorder
}

// ApplePay mocks base method.
func (m *MockEpayServer) ApplePay(arg0 context.Context, arg1 *grpc.ApplePayRequestData) (*grpc.ApplePayResponseData, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ApplePay", arg0, arg1)
	ret0, _ := ret[0].(*grpc.ApplePayResponseData)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ApplePay indicates an expected call of ApplePay.
func (mr *MockEpayServerMockRecorder) ApplePay(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ApplePay", reflect.TypeOf((*MockEpayServer)(nil).ApplePay), arg0, arg1)
}

// Cancel mocks base method.
func (m *MockEpayServer) Cancel(arg0 context.Context, arg1 *grpc.CancelRequest) (*grpc.CancelResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Cancel", arg0, arg1)
	ret0, _ := ret[0].(*grpc.CancelResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Cancel indicates an expected call of Cancel.
func (mr *MockEpayServerMockRecorder) Cancel(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Cancel", reflect.TypeOf((*MockEpayServer)(nil).Cancel), arg0, arg1)
}

// Charge mocks base method.
func (m *MockEpayServer) Charge(arg0 context.Context, arg1 *grpc.ChargeRequest) (*grpc.ChargeResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Charge", arg0, arg1)
	ret0, _ := ret[0].(*grpc.ChargeResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Charge indicates an expected call of Charge.
func (mr *MockEpayServerMockRecorder) Charge(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Charge", reflect.TypeOf((*MockEpayServer)(nil).Charge), arg0, arg1)
}

// GetAcquirerIdentifier mocks base method.
func (m *MockEpayServer) GetAcquirerIdentifier(arg0 context.Context, arg1 *emptypb.Empty) (*grpc.GetAcquirerIdentifierResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAcquirerIdentifier", arg0, arg1)
	ret0, _ := ret[0].(*grpc.GetAcquirerIdentifierResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAcquirerIdentifier indicates an expected call of GetAcquirerIdentifier.
func (mr *MockEpayServerMockRecorder) GetAcquirerIdentifier(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAcquirerIdentifier", reflect.TypeOf((*MockEpayServer)(nil).GetAcquirerIdentifier), arg0, arg1)
}

// GetBankTransactionStatus mocks base method.
func (m *MockEpayServer) GetBankTransactionStatus(arg0 context.Context, arg1 *grpc.BankTransactionStatusRequest) (*grpc.BankTransactionStatusResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetBankTransactionStatus", arg0, arg1)
	ret0, _ := ret[0].(*grpc.BankTransactionStatusResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetBankTransactionStatus indicates an expected call of GetBankTransactionStatus.
func (mr *MockEpayServerMockRecorder) GetBankTransactionStatus(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetBankTransactionStatus", reflect.TypeOf((*MockEpayServer)(nil).GetBankTransactionStatus), arg0, arg1)
}

// GetBankTransactionStatusUnformated mocks base method.
func (m *MockEpayServer) GetBankTransactionStatusUnformated(arg0 context.Context, arg1 *grpc.BankTransactionStatusUnformatedRequest) (*grpc.BankTransactionStatusUnformatedResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetBankTransactionStatusUnformated", arg0, arg1)
	ret0, _ := ret[0].(*grpc.BankTransactionStatusUnformatedResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetBankTransactionStatusUnformated indicates an expected call of GetBankTransactionStatusUnformated.
func (mr *MockEpayServerMockRecorder) GetBankTransactionStatusUnformated(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetBankTransactionStatusUnformated", reflect.TypeOf((*MockEpayServer)(nil).GetBankTransactionStatusUnformated), arg0, arg1)
}

// GooglePay mocks base method.
func (m *MockEpayServer) GooglePay(arg0 context.Context, arg1 *grpc.GooglePayRequestData) (*grpc.GooglePayResponseData, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GooglePay", arg0, arg1)
	ret0, _ := ret[0].(*grpc.GooglePayResponseData)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GooglePay indicates an expected call of GooglePay.
func (mr *MockEpayServerMockRecorder) GooglePay(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GooglePay", reflect.TypeOf((*MockEpayServer)(nil).GooglePay), arg0, arg1)
}

// MakeToken mocks base method.
func (m *MockEpayServer) MakeToken(arg0 context.Context, arg1 *grpc.PayInRequestData) (*grpc.PayInResponseData, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "MakeToken", arg0, arg1)
	ret0, _ := ret[0].(*grpc.PayInResponseData)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// MakeToken indicates an expected call of MakeToken.
func (mr *MockEpayServerMockRecorder) MakeToken(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "MakeToken", reflect.TypeOf((*MockEpayServer)(nil).MakeToken), arg0, arg1)
}

// OneClickPayIn mocks base method.
func (m *MockEpayServer) OneClickPayIn(arg0 context.Context, arg1 *grpc.OneClickPayInRequestData) (*grpc.PayInResponseData, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "OneClickPayIn", arg0, arg1)
	ret0, _ := ret[0].(*grpc.PayInResponseData)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// OneClickPayIn indicates an expected call of OneClickPayIn.
func (mr *MockEpayServerMockRecorder) OneClickPayIn(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "OneClickPayIn", reflect.TypeOf((*MockEpayServer)(nil).OneClickPayIn), arg0, arg1)
}

// PayIn mocks base method.
func (m *MockEpayServer) PayIn(arg0 context.Context, arg1 *grpc.PayInRequestData) (*grpc.PayInResponseData, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "PayIn", arg0, arg1)
	ret0, _ := ret[0].(*grpc.PayInResponseData)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// PayIn indicates an expected call of PayIn.
func (mr *MockEpayServerMockRecorder) PayIn(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "PayIn", reflect.TypeOf((*MockEpayServer)(nil).PayIn), arg0, arg1)
}

// PayOut mocks base method.
func (m *MockEpayServer) PayOut(arg0 context.Context, arg1 *grpc.PayOutRequestData) (*grpc.PayOutResponseData, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "PayOut", arg0, arg1)
	ret0, _ := ret[0].(*grpc.PayOutResponseData)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// PayOut indicates an expected call of PayOut.
func (mr *MockEpayServerMockRecorder) PayOut(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "PayOut", reflect.TypeOf((*MockEpayServer)(nil).PayOut), arg0, arg1)
}

// PayOutByPhone mocks base method.
func (m *MockEpayServer) PayOutByPhone(arg0 context.Context, arg1 *grpc.PayOutByPhoneRequestData) (*grpc.PayOutResponseByPhoneData, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "PayOutByPhone", arg0, arg1)
	ret0, _ := ret[0].(*grpc.PayOutResponseByPhoneData)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// PayOutByPhone indicates an expected call of PayOutByPhone.
func (mr *MockEpayServerMockRecorder) PayOutByPhone(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "PayOutByPhone", reflect.TypeOf((*MockEpayServer)(nil).PayOutByPhone), arg0, arg1)
}

// Refund mocks base method.
func (m *MockEpayServer) Refund(arg0 context.Context, arg1 *grpc.RefundRequest) (*grpc.RefundResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Refund", arg0, arg1)
	ret0, _ := ret[0].(*grpc.RefundResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Refund indicates an expected call of Refund.
func (mr *MockEpayServerMockRecorder) Refund(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Refund", reflect.TypeOf((*MockEpayServer)(nil).Refund), arg0, arg1)
}

// ResolveVisaAlias mocks base method.
func (m *MockEpayServer) ResolveVisaAlias(arg0 context.Context, arg1 *grpc.ResolveVisaAliasRequest) (*grpc.ResolveVisaAliasResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ResolveVisaAlias", arg0, arg1)
	ret0, _ := ret[0].(*grpc.ResolveVisaAliasResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ResolveVisaAlias indicates an expected call of ResolveVisaAlias.
func (mr *MockEpayServerMockRecorder) ResolveVisaAlias(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ResolveVisaAlias", reflect.TypeOf((*MockEpayServer)(nil).ResolveVisaAlias), arg0, arg1)
}

// ThreeDSConfirm mocks base method.
func (m *MockEpayServer) ThreeDSConfirm(arg0 context.Context, arg1 *grpc.ThreeDSRequestData) (*grpc.ThreeDSResponseData, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ThreeDSConfirm", arg0, arg1)
	ret0, _ := ret[0].(*grpc.ThreeDSResponseData)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ThreeDSConfirm indicates an expected call of ThreeDSConfirm.
func (mr *MockEpayServerMockRecorder) ThreeDSConfirm(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ThreeDSConfirm", reflect.TypeOf((*MockEpayServer)(nil).ThreeDSConfirm), arg0, arg1)
}

// ThreeDSResume mocks base method.
func (m *MockEpayServer) ThreeDSResume(arg0 context.Context, arg1 *grpc.ThreeDSResumeRequest) (*grpc.ThreeDSResumeResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ThreeDSResume", arg0, arg1)
	ret0, _ := ret[0].(*grpc.ThreeDSResumeResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ThreeDSResume indicates an expected call of ThreeDSResume.
func (mr *MockEpayServerMockRecorder) ThreeDSResume(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ThreeDSResume", reflect.TypeOf((*MockEpayServer)(nil).ThreeDSResume), arg0, arg1)
}

// TwoStagePayIn mocks base method.
func (m *MockEpayServer) TwoStagePayIn(arg0 context.Context, arg1 *grpc.TwoStagePayInRequest) (*grpc.TwoStagePayInResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "TwoStagePayIn", arg0, arg1)
	ret0, _ := ret[0].(*grpc.TwoStagePayInResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// TwoStagePayIn indicates an expected call of TwoStagePayIn.
func (mr *MockEpayServerMockRecorder) TwoStagePayIn(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "TwoStagePayIn", reflect.TypeOf((*MockEpayServer)(nil).TwoStagePayIn), arg0, arg1)
}

// mustEmbedUnimplementedEpayServer mocks base method.
func (m *MockEpayServer) mustEmbedUnimplementedEpayServer() {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "mustEmbedUnimplementedEpayServer")
}

// mustEmbedUnimplementedEpayServer indicates an expected call of mustEmbedUnimplementedEpayServer.
func (mr *MockEpayServerMockRecorder) mustEmbedUnimplementedEpayServer() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "mustEmbedUnimplementedEpayServer", reflect.TypeOf((*MockEpayServer)(nil).mustEmbedUnimplementedEpayServer))
}

// MockUnsafeEpayServer is a mock of UnsafeEpayServer interface.
type MockUnsafeEpayServer struct {
	ctrl     *gomock.Controller
	recorder *MockUnsafeEpayServerMockRecorder
}

// MockUnsafeEpayServerMockRecorder is the mock recorder for MockUnsafeEpayServer.
type MockUnsafeEpayServerMockRecorder struct {
	mock *MockUnsafeEpayServer
}

// NewMockUnsafeEpayServer creates a new mock instance.
func NewMockUnsafeEpayServer(ctrl *gomock.Controller) *MockUnsafeEpayServer {
	mock := &MockUnsafeEpayServer{ctrl: ctrl}
	mock.recorder = &MockUnsafeEpayServerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockUnsafeEpayServer) EXPECT() *MockUnsafeEpayServerMockRecorder {
	return m.recorder
}

// mustEmbedUnimplementedEpayServer mocks base method.
func (m *MockUnsafeEpayServer) mustEmbedUnimplementedEpayServer() {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "mustEmbedUnimplementedEpayServer")
}

// mustEmbedUnimplementedEpayServer indicates an expected call of mustEmbedUnimplementedEpayServer.
func (mr *MockUnsafeEpayServerMockRecorder) mustEmbedUnimplementedEpayServer() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "mustEmbedUnimplementedEpayServer", reflect.TypeOf((*MockUnsafeEpayServer)(nil).mustEmbedUnimplementedEpayServer))
}
