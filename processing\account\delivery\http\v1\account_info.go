package v1

import (
	"context"
	"git.local/sensitive/processing/account/model"
	"strconv"

	"github.com/gin-gonic/gin"

	"git.local/sensitive/innerpb/processing/goerr"
	"git.local/sensitive/pkg/middlewares"
	"git.local/sensitive/sdk/dog"
)

func (h *Handler) initAccountInfoHandler(v1 *gin.RouterGroup) {
	v1 = v1.Group("manager/")

	v1.GET("account/balance/:number", middlewares.GinErrorHandle(h.getBalanceByAccountNumber))
	v1.GET("accounts/with-balance", middlewares.GinErrorHandle(h.getAccountsWithBalance))
	v1.GET("accounts", middlewares.GinErrorHandle(h.getAccounts))
	v1.PATCH("/accounts/:id/config", middlewares.GinErrorHandle(h.addAccountConfig))
	v1.GET("/accounts/:id/config", middlewares.GinErrorHandle(h.getAccountConfig))
	v1.GET("/accounts/acquirers", middlewares.GinErrorHandle(h.getAccountAcquirers))
}

// getBalanceByAccountNumber
// @Summary Получение баланса счета по номеру
// @Description Получение баланса транзитного счета по его номеру
// @Produce json
// @Success 200 {object} middlewares.Response[schema.AccountBalance]
// @Failure 400 {object} middlewares.Response[middlewares.Empty]
// @Security bearerAuth
// @tags Get Account Balance By Number
// @Router /api/v1/manager/account/balance/{number} [get]
func (h *Handler) getBalanceByAccountNumber(c *gin.Context) (err error) {
	ctx, span := dog.CreateSpan(c.Request.Context(), "Handler_getBalanceByAccountNumber")
	defer span.End()

	accountNumber := c.Param("number")

	accountBalance, err := h.services.AccountInfo.GetBalance(ctx, accountNumber)
	if err != nil {
		return err
	}

	return middlewares.Respond(accountBalance, c)
}

// getAccountsWithBalance
// @Summary Получение всех счетов
// @Description Получение информации о всех транзитных счетах
// @Produce json
// @Success 200 {object} middlewares.Response[[]delivery_http_v1.getAccountsWithBalance.response]
// @Failure 400 {object} middlewares.Response[middlewares.Empty]
// @Security bearerAuth
// @tags Get All Accounts
// @Router /api/v1/manager/accounts/with-balance [get]
func (h *Handler) getAccountsWithBalance(c *gin.Context) (err error) {
	ctx, span := dog.CreateSpan(c.Request.Context(), "Handler_getAccounts")
	defer span.End()

	accounts, err := h.services.AccountInfo.GetAccountsWithBalance(ctx)
	if err != nil {
		return err
	}

	type response struct {
		ID            uint64         `json:"id" validate:"required"`
		AccountNumber string         `json:"account_number" validate:"required"`
		Amount        float64        `json:"amount" validate:"required"`
		Config        map[string]any `json:"config"`
		Bank          struct {
			BankID   string `json:"bank_id" validate:"required"`
			BankName string `json:"bank_name" validate:"required"`
		} `json:"bank" validate:"required"`
	}

	var resp []response
	for _, account := range accounts {
		resp = append(resp, response{
			ID:            account.ID,
			AccountNumber: account.AccountNumber,
			Config:        account.Config,
			Amount:        account.Amount,
			Bank: struct {
				BankID   string `json:"bank_id" validate:"required"`
				BankName string `json:"bank_name" validate:"required"`
			}{BankID: strconv.FormatUint(account.BankID, 10), BankName: account.BankName},
		})
	}

	return middlewares.Respond(resp, c)
}

// getAccounts
// @Summary Получение всех счетов
// @Description Получение информации о всех транзитных счетах
// @Produce json
// @Param bank_id query int false "bank_id"
// @Success 200 {object} middlewares.Response[[]delivery_http_v1.getAccounts.response]
// @Failure 400 {object} middlewares.Response[middlewares.Empty]
// @Security bearerAuth
// @tags Get All Accounts
// @Router /api/v1/manager/accounts [get]
func (h *Handler) getAccounts(c *gin.Context) (err error) {
	ctx, span := dog.CreateSpan(c.Request.Context(), "Handler_getAccounts")
	defer span.End()

	var (
		bankID   uint64
		accounts []model.Account
	)

	bankIDStr := c.Query("bank_id")
	if bankIDStr != "" {
		bankID, err = strconv.ParseUint(bankIDStr, 10, 64)
		if err != nil {
			return goerr.ErrRequestValidation.WithCtx(ctx).WithResult(map[string]interface{}{
				"error": err.Error(),
			})
		}
	}
	if bankID == 0 {
		accounts, err = h.services.AccountBasic.GetAccounts(ctx)
	} else {
		accounts, err = h.services.AccountBasic.GetAccountsByBankID(ctx, bankID)
	}
	if err != nil {
		return err
	}

	type response struct {
		ID            uint64 `json:"id" validate:"required"`
		AccountNumber string `json:"account_number" validate:"required"`
	}

	var resp []response
	for _, account := range accounts {
		resp = append(resp, response{
			ID:            account.ID,
			AccountNumber: account.Number,
		})
	}

	return middlewares.Respond(resp, c)
}

// addAccountConfig
// @Summary Сохранение конфигурации аккаунта в зашифрованном виде
// @Produce json
// @Accept json
// @Param data body map[string]any true "Key and Value"
// @Param id path int true "Account ID"
// @Failure 400 {object} middlewares.Response[middlewares.Empty]
// @Success 200 {object} middlewares.Response[string]
// @tags Add Account Config
// @Router /api/v1/manager/accounts/{id}/config [patch]
func (h *Handler) addAccountConfig(c *gin.Context) error {
	var (
		err error
		ctx context.Context
	)

	config := map[string]any{}

	ctx, span := dog.CreateSpan(c.Request.Context(), "Handler_addAccountConfig")
	defer span.End()

	accountID, err := strconv.ParseUint(c.Param("id"), 10, 64)
	if err != nil {
		return goerr.ErrInvalidPathParam.WithErr(err).WithCtx(ctx).WithResult(map[string]string{
			"param":   "account_id",
			"message": "must be number",
		})
	}

	if err = c.ShouldBindJSON(&config); err != nil {
		return goerr.ErrInvalidPathParam.WithErr(err).WithCtx(ctx).WithResult(map[string]string{
			"error": err.Error(),
		})
	}

	if err = h.services.AccountConfig.UpdateAccountConfig(ctx, accountID, config); err != nil {
		return err
	}

	return middlewares.Respond("success", c)
}

// getAccountConfig
// @Summary Получение конфигурации аккаунта в расшифрованном виде
// @Produce json
// @Accept json
// @Param id path int true "Account ID"
// @Failure 400 {object} middlewares.Response[middlewares.Empty]
// @Failure 200 {object} middlewares.Response[schema.DecryptedAccountConfig]
// @tags Get Account Config
// @Router /api/v1/manager/accounts/{id}/config [get]
func (h *Handler) getAccountConfig(c *gin.Context) error {
	var (
		err error
		ctx context.Context
	)

	ctx, span := dog.CreateSpan(c.Request.Context(), "Handler_getAccountConfig")
	defer span.End()

	accountID, err := strconv.ParseUint(c.Param("id"), 10, 64)
	if err != nil {
		return goerr.ErrInvalidPathParam.WithErr(err).WithCtx(ctx).WithResult(map[string]string{
			"param":   "account_id",
			"message": "must be number",
		})
	}

	decryptedConfig, err := h.services.AccountConfig.GetAccountConfig(ctx, accountID)
	if err != nil {
		return err
	}

	return middlewares.Respond(decryptedConfig, c)
}

// getAccountAcquirers
// @Summary Получение всех банков-эквайеров аккаунта
// @Description Получение всех банков-эквайеров аккаунта
// @Produce json
// @Success 200 {object} middlewares.Response[schema.AccountAcquirer]
// @Failure 400 {object} middlewares.Response[middlewares.Empty]
// @Security bearerAuth
// @tags Get All Account Acquirers
// @Router /api/v1/manager/accounts/acquirers [get]
func (h *Handler) getAccountAcquirers(c *gin.Context) (err error) {
	ctx, span := dog.CreateSpan(c.Request.Context(), "Handler_getAccountAcquirers")
	defer span.End()

	acquirers, err := h.services.AccountInfo.GetAccountAcquirers(ctx)
	if err != nil {
		return err
	}

	return middlewares.Respond(acquirers, c)
}
