package database

import (
	"context"

	goevents "git.local/sensitive/innerpb/processing/events"
	"git.local/sensitive/pkg/middlewares"
	"git.local/sensitive/processing/card/model"
	"git.local/sensitive/processing/card/schema"
)

//go:generate mockgen -package=databasemocks -destination=./databasemocks/mocks.go -source=database.go *
type Carder interface {
	GetByIdAndClient(ctx context.Context, id uint64, clientId uint64) (model.Card, error)
	GetByID(ctx context.Context, id uint64) (model.Card, error)
	GetByClient(ctx context.Context, clientId uint64) (cards model.Cards, err error)
	GetAllNotExpired(ctx context.Context, afterID uint64, limit int) ([]model.Card, error)
	GetClientCards(
		ctx context.Context,
		pagination *middlewares.PaginationInfo,
		projectClientId string,
		projectId uint64,
	) (cards model.Cards, err error)
	Create(ctx context.Context, card *model.Card) error
}

type Keyer interface {
	Create(ctx context.Context, key *model.Key) error
	GetCardsByOldKey(
		ctx context.Context,
		actualKey uint64,
		afterID uint64,
		limit int,
	) (_ []*model.Card, err error)
	GetById(ctx context.Context, id uint64) (model.Key, error)
	GetActualKey(ctx context.Context) (model.Key, error)
}

type HashKeyer interface {
	Create(ctx context.Context, key *model.HashKey) error
	GetActualKey(ctx context.Context) (model.HashKey, error)
	GetCardsByOldKey(
		ctx context.Context,
		actualKey uint64,
		afterID uint64,
		limit int,
	) (_ []*model.Card, err error)
}

type CardGetter interface {
	GetByClient(
		ctx context.Context,
		id uint64,
		pagination *middlewares.PaginationInfo,
	) (cards []model.Card, err error)
	GetActiveCardsByClient(ctx context.Context, id uint64) (model.Cards, error)
}

type CardUpdator interface {
	UpdateSaveAccess(ctx context.Context, cardId uint64, saveAccess bool) error
	UpdateApproved(ctx context.Context, cardId uint64, approvedState bool) error
	DeactivateClientCard(ctx context.Context, cardID uint64) error
	SetExpiredBatch(ctx context.Context, cardIDs []uint64) error
	ModifyCardPan(ctx context.Context, cardID uint64, encryptedPan string) error
}

type BulkCardUpdater interface {
	UpdateHashedPan(ctx context.Context, cards []*model.Card) error
	UpdateEncryptData(ctx context.Context, cards []*model.Card) error
}

//go:generate go run github.com/vektra/mockery/v2 --name=Clientor
type Clientor interface {
	GetByProject(
		ctx context.Context,
		projectId uint64,
		projectClientId string,
	) (client *model.Client, err error)
	Create(ctx context.Context, client *model.Client) error
	CreateClient(
		ctx context.Context,
		projectClientId uint64,
		projectId uint64,
	) (client *model.Client, err error)
	GetProjectClients(
		ctx context.Context,
		pagination *middlewares.PaginationInfo,
		projectId uint64) (client []*model.Client, err error)
	GetByID(ctx context.Context, ID uint64) (client *model.Client, err error)
	GetByFilter(ctx context.Context,
		request schema.ClientRequest,
		pagination *middlewares.PaginationInfo,
		isApproved *bool,
	) (_ []model.Client, err error)
}

type CardTokener interface {
	Create(ctx context.Context, token model.Token) error
	GetByCardAndAcquirer(ctx context.Context, cardId uint64, acquirerId uint64) (model.Token, error)
	GetByCard(
		ctx context.Context,
		cardIds []uint64,
		acquirerIds []uint64,
		terminalIds []uint64,
	) (token []model.Token, err error)
	GetTokensByPan(
		ctx context.Context,
		projectId uint64,
		projectClientId uint64,
		maskedPan string,
	) (token string, acquirerId uint64, err error)
	GetAll(ctx context.Context, pagination *middlewares.PaginationInfo) (_ []*model.Token, err error)
}

type TokenUpdator interface {
	DeactivateToken(ctx context.Context) error
	Update(ctx context.Context, tokenId uint64, tokenRequest *goevents.SaveToken) error
}

type CardValidator interface {
	GetByProject(ctx context.Context, projectId uint64) (cardValidity *model.CardValidity, err error)
}

type ClientVerificationManager interface {
	BindVerificationUserIDToClient(
		ctx context.Context,
		projectID uint64,
		projectClientID string,
		verificationUserID uint64,
	) error
	UnbindVerificationUserIDFromClient(
		ctx context.Context,
		projectID uint64,
		projectClientID string,
	) error
	GetByVerificationID(
		ctx context.Context,
		verificationUserID uint64,
	) (res []model.Client, err error)
	BlockClient(
		ctx context.Context,
		projectID uint64,
		projectClientID string,
	) error
	GetClientActiveness(
		ctx context.Context,
		projectID uint64,
		projectClientID string,
	) (bool, error)
}

type ProjectMaskFormatter interface {
	// GetByProjectID - функция ищет формат маски карты проекта, если не находит,
	// то возвращает дефолтный формат маскирования (1234-12XXXXXX-1234)
	GetByProjectID(ctx context.Context, projectID uint64) (*model.ProjectMaskFormat, error)
	Create(ctx context.Context, maskFormat *model.ProjectMaskFormat) error
	UpdateByProjectId(
		ctx context.Context,
		projectID uint64,
		placeholderSign string,
		withSeparator *bool,
	) error
}
