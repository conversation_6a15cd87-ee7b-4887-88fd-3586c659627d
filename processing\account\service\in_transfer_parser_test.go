package service

import (
	"context"
	"errors"
	"fmt"
	"git.local/sensitive/innerpb/processing/grpc/grpcmock"
	"git.local/sensitive/processing/account/repository/databasemocks"
	"git.local/sensitive/testsdk"
	"github.com/golang/mock/gomock"
	"reflect"
	"strconv"
	"testing"
	"time"

	"github.com/agiledragon/gomonkey"
	"github.com/stretchr/testify/require"
	"google.golang.org/protobuf/types/known/structpb"

	"git.local/sensitive/innerpb/processing/grpc"
	"git.local/sensitive/processing/account/model"
	"git.local/sensitive/sdk/dog/e2e"
)

func TestCompareTransferData(t *testing.T) {
	testNow := time.Date(2009, time.November, 10, 23, 0, 0, 0, time.UTC)

	type getByStatusIDOp struct {
		input     uint64
		output    []model.ProcessedOrder
		outputErr error
	}

	type getByCodeOp struct {
		isCalled  bool
		output    *model.ProcessedOrderStatus
		outputErr error
	}

	type updateStatusByIDsOP struct {
		isCalled      bool
		inputOrderIDs []string
		inputStatusID uint64
		outputErr     error
	}

	tests := []struct {
		name                                      string
		reqOrderID, reqAccountNumber, reqBankCode string
		reqStatusID                               uint64
		want                                      bool
		wantErr                                   error
		getByStatusID                             getByStatusIDOp
		getByCode                                 getByCodeOp
		updateStatusByIDs                         updateStatusByIDsOP
	}{
		{
			name:             "error_getting_processed_orders_by_status_ID",
			reqStatusID:      1,
			reqAccountNumber: "some acc number",
			reqBankCode:      "bcc",
			reqOrderID:       "tarlan123",
			want:             false,
			wantErr:          errors.New("some error"),
			getByStatusID: getByStatusIDOp{
				input:     1,
				output:    nil,
				outputErr: errors.New("some error"),
			},
		},
		{
			name:             "error_getting_order_status_by_code_error",
			reqStatusID:      1,
			reqAccountNumber: "some acc number",
			reqBankCode:      "bcc",
			reqOrderID:       "tarlan123",
			want:             false,
			wantErr:          errors.New("some error"),
			getByStatusID: getByStatusIDOp{
				input: 1,
				output: []model.ProcessedOrder{
					{
						BankOrderID:   "tarlan123",
						BankCode:      "bcc",
						AccountNumber: "some acc number",
						Date:          "2009-11-10 23:00:00",
					},
				},
				outputErr: nil,
			},
			getByCode: getByCodeOp{
				isCalled:  true,
				output:    nil,
				outputErr: errors.New("some error"),
			},
		},
		{
			name:             "all_params_match",
			reqStatusID:      1,
			reqAccountNumber: "some acc number",
			reqBankCode:      "bcc",
			reqOrderID:       "tarlan123",
			want:             true,
			wantErr:          nil,
			getByStatusID: getByStatusIDOp{
				input: 1,
				output: []model.ProcessedOrder{
					{
						BankOrderID:   "tarlan123",
						BankCode:      "bcc",
						AccountNumber: "some acc number",
						Date:          "2009-11-10 23:00:00",
					},
				},
				outputErr: nil,
			},
			getByCode: getByCodeOp{
				isCalled: true,
				output: &model.ProcessedOrderStatus{
					ID:   2,
					Name: "expired",
					Code: "expired",
				},
				outputErr: nil,
			},
		},
		{
			name:             "params_dont_match_error_parsing_time",
			reqStatusID:      1,
			reqAccountNumber: "some acc number",
			reqBankCode:      "bcc",
			reqOrderID:       "tarlan123",
			want:             false,
			wantErr:          &time.ParseError{},
			getByStatusID: getByStatusIDOp{
				input: 1,
				output: []model.ProcessedOrder{
					{
						BankOrderID:   "tarlan123",
						BankCode:      "bcc",
						AccountNumber: "some acc number 2",
						Date:          "bad date",
					},
				},
				outputErr: nil,
			},
			getByCode: getByCodeOp{
				isCalled: true,
				output: &model.ProcessedOrderStatus{
					ID:   2,
					Name: "expired",
					Code: "expired",
				},
				outputErr: nil,
			},
		},
		{
			name:             "error_when_updating_status",
			reqStatusID:      1,
			reqAccountNumber: "some acc number",
			reqBankCode:      "bcc",
			reqOrderID:       "tarlan123",
			want:             false,
			wantErr:          errors.New("some error"),
			getByStatusID: getByStatusIDOp{
				input: 1,
				output: []model.ProcessedOrder{
					{
						BankOrderID:   "tarlan123",
						BankCode:      "bcc",
						AccountNumber: "some acc number 2",
						Date:          "2009-11-03 23:00:00",
					},
				},
				outputErr: nil,
			},
			getByCode: getByCodeOp{
				isCalled: true,
				output: &model.ProcessedOrderStatus{
					ID:   2,
					Name: "expired",
					Code: "expired",
				},
				outputErr: nil,
			},
			updateStatusByIDs: updateStatusByIDsOP{
				isCalled:      true,
				inputOrderIDs: []string{"tarlan123"},
				inputStatusID: 2,
				outputErr:     errors.New("some error"),
			},
		},
		{
			name:             "zero_orders_returned",
			reqStatusID:      1,
			reqAccountNumber: "some acc number",
			reqBankCode:      "bcc",
			reqOrderID:       "tarlan123",
			want:             false,
			wantErr:          nil,
			getByStatusID: getByStatusIDOp{
				input:     1,
				output:    nil,
				outputErr: nil,
			},
			getByCode: getByCodeOp{
				isCalled: true,
				output: &model.ProcessedOrderStatus{
					ID:   2,
					Name: "expired",
					Code: "expired",
				},
				outputErr: nil,
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctrl := gomock.NewController(t)
			defer ctrl.Finish()

			processOrderDBMock := databasemocks.NewMockProcessedOrderer(ctrl)
			processOrderStatusDBMock := databasemocks.NewMockProcessedOrderStatuser(ctrl)

			processOrderDBMock.EXPECT().GetByStatusID(
				gomock.Any(),
				tt.getByStatusID.input,
			).Return(
				tt.getByStatusID.output,
				tt.getByStatusID.outputErr,
			).Times(1)

			if tt.getByCode.isCalled {
				processOrderStatusDBMock.EXPECT().GetByCode(
					gomock.Any(),
					model.StatusExpired,
				).Return(
					tt.getByCode.output,
					tt.getByCode.outputErr,
				).Times(1)
			}

			if tt.updateStatusByIDs.isCalled {
				processOrderDBMock.EXPECT().UpdateStatusByIDs(
					gomock.Any(),
					tt.updateStatusByIDs.inputOrderIDs,
					tt.updateStatusByIDs.inputStatusID,
				).Return(tt.updateStatusByIDs.outputErr).Times(1)
			}

			s := &InTransferParserService{
				processedOrderRepo:       processOrderDBMock,
				processedOrderStatusRepo: processOrderStatusDBMock,
				billingClient:            nil,
				multiaccountingClient:    nil,
				getCurrentTime: func() time.Time {
					return testNow
				},
			}

			res, err := s.CompareTransferData(context.Background(), tt.reqOrderID, tt.reqAccountNumber, tt.reqBankCode, tt.reqStatusID)
			if tt.wantErr != nil {
				require.Error(t, err)
				require.ErrorAs(t, err, &tt.wantErr)
			} else {
				require.NoError(t, err)
			}

			require.Equal(t, tt.want, res)
		})
	}
}

func TestCreateTransfer(t *testing.T) {
	testNow := time.Date(2009, time.November, 10, 23, 0, 0, 0, time.UTC)

	type getMerchantProjectOp struct {
		isCalled  bool
		input     *grpc.GetMerchantProjectsByBinRequestV1
		output    *grpc.GetMerchantProjectsByBinResponseV1
		outputErr error
	}

	type getByCodeOp struct {
		isCalled  bool
		output    *model.TransferStatus
		outputErr error
	}

	type createTransferOp struct {
		isCalled  bool
		input     model.Transfer
		output    *model.Transfer
		outputErr error
	}

	tests := []struct {
		name               string
		req                *grpc.TransitAccountOperation
		reqAccount         model.Account
		want               *model.Transfer
		wantErr            error
		getMerchantProject getMerchantProjectOp
		getByCode          getByCodeOp
		createTransfer     createTransferOp
	}{
		{
			name: "regex_error",
			req: &grpc.TransitAccountOperation{
				Description:         testsdk.Ptr("nothing to find for regex"),
				Amount:              testsdk.Ptr(float64(33)),
				MerchantAccount:     testsdk.Ptr("some merchant account"),
				TransferTypeId:      testsdk.Ptr(uint64(2)),
				PaymentPurposeCode:  testsdk.Ptr("some payment purpose code"),
				ExternalReferenceId: testsdk.Ptr("some external reference"),
			},
			reqAccount: model.Account{
				ID:     1,
				BankID: 25,
			},
			wantErr: fmt.Errorf("pid not found in description"),
			want:    nil,
		},
		{
			name: "parse_uint_error",
			req: &grpc.TransitAccountOperation{
				Description:         testsdk.Ptr("pid184467440737095516158pid"),
				Date:                testsdk.Ptr("bad date"),
				Amount:              testsdk.Ptr(33.0),
				MerchantAccount:     testsdk.Ptr("some merchant account"),
				TransferTypeId:      testsdk.Ptr(uint64(2)),
				PaymentPurposeCode:  testsdk.Ptr("some payment purpose code"),
				ExternalReferenceId: testsdk.Ptr("some external reference"),
				MerchantBin:         testsdk.Ptr("some bin"),
			},
			reqAccount: model.Account{
				ID:     1,
				BankID: 25,
			},
			wantErr: &strconv.NumError{
				Func: "ParseUint",
				Num:  "184467440737095516158",
				Err:  strconv.ErrRange,
			},
			want: nil,
		},
		{
			name: "get_merchant_project_error",
			req: &grpc.TransitAccountOperation{
				Description:         testsdk.Ptr("pid123pid"),
				Date:                testsdk.Ptr("bad date"),
				Amount:              testsdk.Ptr(float64(33)),
				MerchantAccount:     testsdk.Ptr("some merchant account"),
				TransferTypeId:      testsdk.Ptr(uint64(2)),
				PaymentPurposeCode:  testsdk.Ptr("some payment purpose code"),
				ExternalReferenceId: testsdk.Ptr("some external reference"),
				MerchantBin:         testsdk.Ptr("some bin"),
			},
			reqAccount: model.Account{
				ID:     1,
				BankID: 25,
			},
			getMerchantProject: getMerchantProjectOp{
				isCalled: true,
				input: &grpc.GetMerchantProjectsByBinRequestV1{
					MerchantBin: testsdk.Ptr("some bin"),
				},
				output:    nil,
				outputErr: errors.New("some error"),
			},
			wantErr: errors.New("some error"),
			want:    nil,
		},
		{
			name: "get_by_code_error",
			req: &grpc.TransitAccountOperation{
				Description:         testsdk.Ptr("pid123pid"),
				Date:                testsdk.Ptr("bad date"),
				Amount:              testsdk.Ptr(float64(33)),
				MerchantAccount:     testsdk.Ptr("some merchant account"),
				TransferTypeId:      testsdk.Ptr(uint64(2)),
				PaymentPurposeCode:  testsdk.Ptr("some payment purpose code"),
				ExternalReferenceId: testsdk.Ptr("some external reference"),
				MerchantBin:         testsdk.Ptr("some bin"),
			},
			reqAccount: model.Account{
				ID:     1,
				BankID: 25,
			},
			getMerchantProject: getMerchantProjectOp{
				isCalled: true,
				input: &grpc.GetMerchantProjectsByBinRequestV1{
					MerchantBin: testsdk.Ptr("some bin"),
				},
				output: &grpc.GetMerchantProjectsByBinResponseV1{
					MerchantId: testsdk.Ptr(uint64(101)),
					Projects:   nil,
				},
				outputErr: nil,
			},
			getByCode: getByCodeOp{
				isCalled:  true,
				output:    nil,
				outputErr: errors.New("some error"),
			},
			wantErr: errors.New("some error"),
			want:    nil,
		},
		{
			name: "parse_time_error",
			req: &grpc.TransitAccountOperation{
				Description:         testsdk.Ptr("pid123pid"),
				Date:                testsdk.Ptr("bad date"),
				Amount:              testsdk.Ptr(float64(33)),
				MerchantAccount:     testsdk.Ptr("some merchant account"),
				TransferTypeId:      testsdk.Ptr(uint64(2)),
				PaymentPurposeCode:  testsdk.Ptr("some payment purpose code"),
				ExternalReferenceId: testsdk.Ptr("some external reference"),
				MerchantBin:         testsdk.Ptr("some bin"),
			},
			reqAccount: model.Account{
				ID:     1,
				BankID: 25,
			},
			getMerchantProject: getMerchantProjectOp{
				isCalled: true,
				input: &grpc.GetMerchantProjectsByBinRequestV1{
					MerchantBin: testsdk.Ptr("some bin"),
				},
				output: &grpc.GetMerchantProjectsByBinResponseV1{
					MerchantId: testsdk.Ptr(uint64(101)),
					Projects:   nil,
				},
				outputErr: nil,
			},
			getByCode: getByCodeOp{
				isCalled: true,
				output: &model.TransferStatus{
					ID:   1,
					Name: "success",
					Code: model.StatusSuccess,
				},
				outputErr: nil,
			},
			wantErr: &time.ParseError{
				Layout:     "2006-01-02 15:04:05",
				Value:      "bad date",
				LayoutElem: "2006",
				ValueElem:  "bad date",
				Message:    "",
			},
			want: nil,
		},
		{
			name: "create_transfer_error",
			req: &grpc.TransitAccountOperation{
				Description:         testsdk.Ptr("pid123pid"),
				Date:                testsdk.Ptr("2009-11-10 23:00:00"),
				Amount:              testsdk.Ptr(float64(33)),
				MerchantAccount:     testsdk.Ptr("some merchant account"),
				TransferTypeId:      testsdk.Ptr(uint64(2)),
				PaymentPurposeCode:  testsdk.Ptr("some payment purpose code"),
				ExternalReferenceId: testsdk.Ptr("some external reference"),
				MerchantBin:         testsdk.Ptr("some bin"),
			},
			reqAccount: model.Account{
				ID:     1,
				BankID: 25,
			},
			getMerchantProject: getMerchantProjectOp{
				isCalled: true,
				input: &grpc.GetMerchantProjectsByBinRequestV1{
					MerchantBin: testsdk.Ptr("some bin"),
				},
				output: &grpc.GetMerchantProjectsByBinResponseV1{
					MerchantId: testsdk.Ptr(uint64(101)),
					Projects:   nil,
				},
				outputErr: nil,
			},
			getByCode: getByCodeOp{
				isCalled: true,
				output: &model.TransferStatus{
					ID:   1,
					Name: "success",
					Code: model.StatusSuccess,
				},
				outputErr: nil,
			},
			createTransfer: createTransferOp{
				isCalled: true,
				input: model.Transfer{
					AcquirerID:              25,
					AccountID:               1,
					BankReferenceID:         "some external reference",
					BankOrderID:             "",
					ProjectID:               123,
					MerchantID:              101,
					StatusID:                1,
					MerchantBIN:             "some bin",
					Amount:                  33,
					MerchantAccount:         "some merchant account",
					MerchantBeneficiaryCode: bankKbe,
					Description:             "pid123pid",
					FinishedAt:              &testNow,
					TransferTypeID:          2,
					PaymentPurposeCode:      "some payment purpose code",
				},
				outputErr: errors.New("some error"),
				output:    nil,
			},
			wantErr: errors.New("some error"),
			want:    nil,
		},
		{
			name: "success",
			req: &grpc.TransitAccountOperation{
				Description:         testsdk.Ptr("pid123pid"),
				Date:                testsdk.Ptr("2009-11-10 23:00:00"),
				Amount:              testsdk.Ptr(float64(33)),
				MerchantAccount:     testsdk.Ptr("some merchant account"),
				TransferTypeId:      testsdk.Ptr(uint64(2)),
				PaymentPurposeCode:  testsdk.Ptr("some payment purpose code"),
				ExternalReferenceId: testsdk.Ptr("some external reference"),
				MerchantBin:         testsdk.Ptr("some bin"),
			},
			reqAccount: model.Account{
				ID:     1,
				BankID: 25,
			},
			getMerchantProject: getMerchantProjectOp{
				isCalled: true,
				input: &grpc.GetMerchantProjectsByBinRequestV1{
					MerchantBin: testsdk.Ptr("some bin"),
				},
				output: &grpc.GetMerchantProjectsByBinResponseV1{
					MerchantId: testsdk.Ptr(uint64(101)),
					Projects:   nil,
				},
				outputErr: nil,
			},
			getByCode: getByCodeOp{
				isCalled: true,
				output: &model.TransferStatus{
					ID:   1,
					Name: "success",
					Code: model.StatusSuccess,
				},
				outputErr: nil,
			},
			createTransfer: createTransferOp{
				isCalled: true,
				input: model.Transfer{
					AcquirerID:              25,
					AccountID:               1,
					BankReferenceID:         "some external reference",
					BankOrderID:             "",
					ProjectID:               123,
					MerchantID:              101,
					StatusID:                1,
					MerchantBIN:             "some bin",
					Amount:                  33,
					MerchantAccount:         "some merchant account",
					MerchantBeneficiaryCode: bankKbe,
					Description:             "pid123pid",
					FinishedAt:              &testNow,
					TransferTypeID:          2,
					PaymentPurposeCode:      "some payment purpose code",
				},
				outputErr: nil,
				output: &model.Transfer{
					ID:                      1,
					AcquirerID:              25,
					AccountID:               1,
					BankReferenceID:         "some external reference",
					BankOrderID:             "",
					ProjectID:               123,
					MerchantID:              101,
					StatusID:                1,
					MerchantBIN:             "some bin",
					Amount:                  33,
					MerchantAccount:         "some merchant account",
					MerchantBeneficiaryCode: bankKbe,
					Description:             "pid123pid",
					FinishedAt:              &testNow,
					TransferTypeID:          2,
					PaymentPurposeCode:      "some payment purpose code",
				},
			},
			wantErr: nil,
			want: &model.Transfer{
				ID:                      1,
				AcquirerID:              25,
				AccountID:               1,
				BankReferenceID:         "some external reference",
				BankOrderID:             "",
				ProjectID:               123,
				MerchantID:              101,
				StatusID:                1,
				MerchantBIN:             "some bin",
				Amount:                  33,
				MerchantAccount:         "some merchant account",
				MerchantBeneficiaryCode: bankKbe,
				Description:             "pid123pid",
				FinishedAt:              &testNow,
				TransferTypeID:          2,
				PaymentPurposeCode:      "some payment purpose code",
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctrl := gomock.NewController(t)
			defer ctrl.Finish()

			merchantCliMock := grpcmock.NewMockMerchantClient(ctrl)
			transferStatusDBMock := databasemocks.NewMockTransferStatuser(ctrl)
			transferManagerDBMock := databasemocks.NewMockTransferManager(ctrl)

			s := InTransferParserService{
				transferStatusRepo: transferStatusDBMock,
				transferManageRepo: transferManagerDBMock,
				merchantClient:     merchantCliMock,
			}

			c := context.Background()
			ctx, cancel := context.WithCancel(c)
			e2e.InitAppE2E(t, ctx, cancel, nil, nil, nil)

			if tt.getMerchantProject.isCalled {
				merchantCliMock.EXPECT().GetMerchantProjectsByBin(
					gomock.Any(),
					tt.getMerchantProject.input,
				).Return(
					tt.getMerchantProject.output,
					tt.getMerchantProject.outputErr,
				).Times(1)
			}

			if tt.getByCode.isCalled {
				transferStatusDBMock.EXPECT().GetByCode(
					gomock.Any(),
					model.StatusSuccess,
				).Return(tt.getByCode.output, tt.getByCode.outputErr).Times(1)
			}

			if tt.createTransfer.isCalled {
				transferManagerDBMock.EXPECT().Create(
					gomock.Any(),
					tt.createTransfer.input,
				).Return(tt.createTransfer.output, tt.createTransfer.outputErr).Times(1)
			}

			res, err := s.CreateTransfer(context.Background(), tt.req, tt.reqAccount)
			if tt.wantErr != nil {
				require.Error(t, err)
				require.Equal(t, tt.wantErr, err)
			} else {
				require.NoError(t, err)
			}

			require.Equal(t, tt.want, res)
		})
	}
}

func TestProcessStatement(t *testing.T) {
	type getByCodeOp struct {
		output    *model.ProcessedOrderStatus
		outputErr error
	}

	type compareTransferDataOp struct {
		isCalled             bool
		inputExternalID      string
		inputMerchantAccount string
		inputBankCode        string
		inputStatusID        uint64
		output               bool
		outputErr            error
	}

	type createProcessOrderOp struct {
		isCalled  bool
		input     model.ProcessedOrder
		outputErr error
	}

	type createTransferOp struct {
		isCalled     bool
		input        *grpc.TransitAccountOperation
		inputAccount model.Account
		output       *model.Transfer
		outputErr    error
	}

	type billTransferOp struct {
		isCalled  bool
		input     *grpc.BillInTransferRequestV1
		outputErr error
	}

	tests := []struct {
		name                string
		req                 *grpc.TransitAccountOperation
		reqAccount          model.Account
		wantErr             error
		getByCode           getByCodeOp
		compareTransferData compareTransferDataOp
		createProcessOrder  createProcessOrderOp
		createTransfer      createTransferOp
		billTransfer        billTransferOp
	}{
		{
			name: "error_getting_status_by_code",
			req: &grpc.TransitAccountOperation{
				ExternalReferenceId: testsdk.Ptr("some external reference id"),
				MerchantAccount:     testsdk.Ptr("some merchant account"),
				Date:                testsdk.Ptr("2009-11-10 23:00:00"),
			},
			reqAccount: model.Account{
				BankID:   25,
				BankCode: "bcc",
				Number:   "some number",
			},
			wantErr: errors.New("some error"),
			getByCode: getByCodeOp{
				output:    nil,
				outputErr: errors.New("some error"),
			},
		},
		{
			name: "error_comparing_transfer_data",
			req: &grpc.TransitAccountOperation{
				ExternalReferenceId: testsdk.Ptr("some external reference id"),
				MerchantAccount:     testsdk.Ptr("some merchant account"),
				Date:                testsdk.Ptr("2009-11-10 23:00:00"),
			},
			reqAccount: model.Account{
				BankID:   25,
				BankCode: "bcc",
				Number:   "some number",
			},
			wantErr: errors.New("some error"),
			getByCode: getByCodeOp{
				output: &model.ProcessedOrderStatus{
					ID:   5,
					Code: "actual",
					Name: "actual",
				},
				outputErr: nil,
			},
			compareTransferData: compareTransferDataOp{
				isCalled:             true,
				inputExternalID:      "some external reference id",
				inputMerchantAccount: "some merchant account",
				inputBankCode:        "bcc",
				inputStatusID:        5,
				output:               false,
				outputErr:            errors.New("some error"),
			},
		},
		{
			name: "easy_flow_exist_is_true",
			req: &grpc.TransitAccountOperation{
				ExternalReferenceId: testsdk.Ptr("some external reference id"),
				MerchantAccount:     testsdk.Ptr("some merchant account"),
				Date:                testsdk.Ptr("2009-11-10 23:00:00"),
			},
			reqAccount: model.Account{
				BankID:   25,
				BankCode: "bcc",
				Number:   "some number",
			},
			wantErr: nil,
			getByCode: getByCodeOp{
				output: &model.ProcessedOrderStatus{
					ID:   5,
					Code: "actual",
					Name: "actual",
				},
				outputErr: nil,
			},
			compareTransferData: compareTransferDataOp{
				isCalled:             true,
				inputExternalID:      "some external reference id",
				inputMerchantAccount: "some merchant account",
				inputBankCode:        "bcc",
				inputStatusID:        5,
				output:               true,
				outputErr:            nil,
			},
		},
		{
			name: "error creating process order",
			req: &grpc.TransitAccountOperation{
				ExternalReferenceId: testsdk.Ptr("some external reference id"),
				MerchantAccount:     testsdk.Ptr("some merchant account"),
				Date:                testsdk.Ptr("2009-11-10 23:00:00"),
			},
			reqAccount: model.Account{
				BankID:   25,
				BankCode: "bcc",
				Number:   "some number",
			},
			wantErr: errors.New("some error"),
			getByCode: getByCodeOp{
				output: &model.ProcessedOrderStatus{
					ID:   5,
					Code: "actual",
					Name: "actual",
				},
				outputErr: nil,
			},
			compareTransferData: compareTransferDataOp{
				isCalled:             true,
				inputExternalID:      "some external reference id",
				inputMerchantAccount: "some merchant account",
				inputBankCode:        "bcc",
				inputStatusID:        5,
				output:               false,
				outputErr:            nil,
			},
			createProcessOrder: createProcessOrderOp{
				isCalled: true,
				input: model.ProcessedOrder{
					BankOrderID:   "some external reference id",
					Date:          "2009-11-10 23:00:00",
					AccountNumber: "some merchant account",
					BankCode:      "bcc",
					StatusID:      5,
				},
				outputErr: errors.New("some error"),
			},
		},
		{
			name: "error_creating_transfer",
			req: &grpc.TransitAccountOperation{
				ExternalReferenceId: testsdk.Ptr("some external reference id"),
				MerchantAccount:     testsdk.Ptr("some merchant account"),
				Date:                testsdk.Ptr("2009-11-10 23:00:00"),
			},
			reqAccount: model.Account{
				BankID:   25,
				BankCode: "bcc",
				Number:   "some number",
			},
			wantErr: errors.New("some error"),
			getByCode: getByCodeOp{
				output: &model.ProcessedOrderStatus{
					ID:   5,
					Code: "actual",
					Name: "actual",
				},
				outputErr: nil,
			},
			compareTransferData: compareTransferDataOp{
				isCalled:             true,
				inputExternalID:      "some external reference id",
				inputMerchantAccount: "some merchant account",
				inputBankCode:        "bcc",
				inputStatusID:        5,
				output:               false,
				outputErr:            nil,
			},
			createProcessOrder: createProcessOrderOp{
				isCalled: true,
				input: model.ProcessedOrder{
					BankOrderID:   "some external reference id",
					Date:          "2009-11-10 23:00:00",
					AccountNumber: "some merchant account",
					BankCode:      "bcc",
					StatusID:      5,
				},
				outputErr: nil,
			},
			createTransfer: createTransferOp{
				isCalled: true,
				input: &grpc.TransitAccountOperation{
					ExternalReferenceId: testsdk.Ptr("some external reference id"),
					MerchantAccount:     testsdk.Ptr("some merchant account"),
					Date:                testsdk.Ptr("2009-11-10 23:00:00"),
				},
				inputAccount: model.Account{
					BankID:   25,
					BankCode: "bcc",
					Number:   "some number",
				},
				output:    nil,
				outputErr: errors.New("some error"),
			},
		},
		{
			name: "error_billing_transfer",
			req: &grpc.TransitAccountOperation{
				ExternalReferenceId: testsdk.Ptr("some external reference id"),
				MerchantAccount:     testsdk.Ptr("some merchant account"),
				Date:                testsdk.Ptr("2009-11-10 23:00:00"),
			},
			reqAccount: model.Account{
				BankID:   25,
				BankCode: "bcc",
				Number:   "some number",
			},
			wantErr: errors.New("some error"),
			getByCode: getByCodeOp{
				output: &model.ProcessedOrderStatus{
					ID:   5,
					Code: "actual",
					Name: "actual",
				},
				outputErr: nil,
			},
			compareTransferData: compareTransferDataOp{
				isCalled:             true,
				inputExternalID:      "some external reference id",
				inputMerchantAccount: "some merchant account",
				inputBankCode:        "bcc",
				inputStatusID:        5,
				output:               false,
				outputErr:            nil,
			},
			createProcessOrder: createProcessOrderOp{
				isCalled: true,
				input: model.ProcessedOrder{
					BankOrderID:   "some external reference id",
					Date:          "2009-11-10 23:00:00",
					AccountNumber: "some merchant account",
					BankCode:      "bcc",
					StatusID:      5,
				},
				outputErr: nil,
			},
			createTransfer: createTransferOp{
				isCalled: true,
				input: &grpc.TransitAccountOperation{
					ExternalReferenceId: testsdk.Ptr("some external reference id"),
					MerchantAccount:     testsdk.Ptr("some merchant account"),
					Date:                testsdk.Ptr("2009-11-10 23:00:00"),
				},
				inputAccount: model.Account{
					BankID:   25,
					BankCode: "bcc",
					Number:   "some number",
				},
				output: &model.Transfer{
					ProjectID:  10,
					Amount:     10000,
					MerchantID: 25,
					ID:         1,
				},
				outputErr: nil,
			},
			billTransfer: billTransferOp{
				isCalled: true,
				input: &grpc.BillInTransferRequestV1{
					ProjectId:     testsdk.Ptr(uint64(10)),
					AccountNumber: testsdk.Ptr("some number"),
					Amount:        testsdk.Ptr(10000.0),
					MerchantId:    testsdk.Ptr(uint64(25)),
					TransferId:    testsdk.Ptr(uint64(1)),
				},
				outputErr: errors.New("some error"),
			},
		},
		{
			name: "success_all_ops",
			req: &grpc.TransitAccountOperation{
				ExternalReferenceId: testsdk.Ptr("some external reference id"),
				MerchantAccount:     testsdk.Ptr("some merchant account"),
				Date:                testsdk.Ptr("2009-11-10 23:00:00"),
			},
			reqAccount: model.Account{
				BankID:   25,
				BankCode: "bcc",
				Number:   "some number",
			},
			wantErr: nil,
			getByCode: getByCodeOp{
				output: &model.ProcessedOrderStatus{
					ID:   5,
					Code: "actual",
					Name: "actual",
				},
				outputErr: nil,
			},
			compareTransferData: compareTransferDataOp{
				isCalled:             true,
				inputExternalID:      "some external reference id",
				inputMerchantAccount: "some merchant account",
				inputBankCode:        "bcc",
				inputStatusID:        5,
				output:               false,
				outputErr:            nil,
			},
			createProcessOrder: createProcessOrderOp{
				isCalled: true,
				input: model.ProcessedOrder{
					BankOrderID:   "some external reference id",
					Date:          "2009-11-10 23:00:00",
					AccountNumber: "some merchant account",
					BankCode:      "bcc",
					StatusID:      5,
				},
				outputErr: nil,
			},
			createTransfer: createTransferOp{
				isCalled: true,
				input: &grpc.TransitAccountOperation{
					ExternalReferenceId: testsdk.Ptr("some external reference id"),
					MerchantAccount:     testsdk.Ptr("some merchant account"),
					Date:                testsdk.Ptr("2009-11-10 23:00:00"),
				},
				inputAccount: model.Account{
					BankID:   25,
					BankCode: "bcc",
					Number:   "some number",
				},
				output: &model.Transfer{
					ProjectID:  10,
					Amount:     10000,
					MerchantID: 25,
					ID:         1,
				},
				outputErr: nil,
			},
			billTransfer: billTransferOp{
				isCalled: true,
				input: &grpc.BillInTransferRequestV1{
					ProjectId:     testsdk.Ptr(uint64(10)),
					AccountNumber: testsdk.Ptr("some number"),
					Amount:        testsdk.Ptr(10000.0),
					MerchantId:    testsdk.Ptr(uint64(25)),
					TransferId:    testsdk.Ptr(uint64(1)),
				},
				outputErr: nil,
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctrl := gomock.NewController(t)
			defer ctrl.Finish()

			c := context.Background()
			ctx, cancel := context.WithCancel(c)
			e2e.InitAppE2E(t, ctx, cancel, nil, nil, nil)

			processedOrderStatusDBMock := databasemocks.NewMockProcessedOrderStatuser(ctrl)
			processOrderDBMock := databasemocks.NewMockProcessedOrderer(ctrl)
			billingCliMock := grpcmock.NewMockBillingClient(ctrl)

			processedOrderStatusDBMock.EXPECT().GetByCode(
				gomock.Any(),
				model.StatusActual,
			).Return(
				tt.getByCode.output,
				tt.getByCode.outputErr,
			).Times(1)

			if tt.createProcessOrder.isCalled {
				processOrderDBMock.EXPECT().Create(
					gomock.Any(),
					tt.createProcessOrder.input,
				).Return(
					tt.createProcessOrder.outputErr,
				).Times(1)
			}

			if tt.billTransfer.isCalled {
				billingCliMock.EXPECT().BillInTransferV1(
					gomock.Any(),
					tt.billTransfer.input,
				).Return(
					nil,
					tt.billTransfer.outputErr,
				).Times(1)
			}

			s := InTransferParserService{
				processedOrderRepo:       processOrderDBMock,
				processedOrderStatusRepo: processedOrderStatusDBMock,
				billingClient:            billingCliMock,
			}

			patches := gomonkey.NewPatches()
			defer patches.Reset()

			if tt.compareTransferData.isCalled {
				patches.ApplyMethod(reflect.TypeOf(&s), "CompareTransferData", func(
					_ *InTransferParserService,
					ctx context.Context,
					orderID,
					accountNumber,
					bankCode string,
					statusID uint64,
				) (bool, error) {
					t.Helper()

					require.Equal(t, tt.compareTransferData.inputExternalID, orderID)
					require.Equal(t, tt.compareTransferData.inputMerchantAccount, accountNumber)
					require.Equal(t, tt.compareTransferData.inputBankCode, bankCode)
					require.Equal(t, tt.compareTransferData.inputStatusID, statusID)

					return tt.compareTransferData.output, tt.compareTransferData.outputErr
				})
			}

			if tt.createTransfer.isCalled {
				patches.ApplyMethod(reflect.TypeOf(&s), "CreateTransfer", func(
					_ *InTransferParserService,
					ctx context.Context,
					statement *grpc.TransitAccountOperation,
					account model.Account,
				) (*model.Transfer, error) {
					t.Helper()

					require.Equal(t, tt.createTransfer.input, statement)
					require.Equal(t, tt.createTransfer.inputAccount, account)

					return tt.createTransfer.output, tt.createTransfer.outputErr
				})
			}

			err := s.ProcessStatement(context.Background(), tt.req, tt.reqAccount)
			if tt.wantErr != nil {
				require.Error(t, err)
				require.Equal(t, tt.wantErr, err)
			} else {
				require.NoError(t, err)
			}
		})
	}
}

func TestProcessScheduler(t *testing.T) {
	testNow := time.Date(2009, time.November, 10, 23, 0, 0, 0, time.UTC)

	structbConfig, err := structpb.NewStruct(map[string]any{
		"my-name-is": "skrillex",
	})
	if err != nil {
		t.Fatal(err)
	}

	type getAccountStatementOp struct {
		isCalled  bool
		input     *grpc.GetAccountStatementRequest
		output    *grpc.GetAccountStatementResponse
		outputErr error
	}

	type processStatementOp struct {
		isCalled       bool
		inputOperation *grpc.TransitAccountOperation
		inputAccount   model.Account
		outputErr      error
	}

	tests := []struct {
		name                string
		req                 model.Account
		wantErr             error
		appConfig           map[string]any
		getAccountStatement getAccountStatementOp
		processStatement    processStatementOp
	}{
		{
			name: "bad_key",
			req: model.Account{
				ID:              1,
				BankID:          25,
				BankCode:        "bcc",
				Number:          "some number",
				EncryptedConfig: defaultEncryptedConfig,
			},
			wantErr: fmt.Errorf("account info is empty"),
			appConfig: map[string]any{
				"SYMMETRIC_KEY": badKey,
			},
		},
		{
			name: "get_account_statement_error",
			req: model.Account{
				ID:              1,
				BankID:          25,
				BankCode:        "bcc",
				Number:          "some number",
				EncryptedConfig: defaultEncryptedConfig,
			},
			wantErr: errors.New("some error"),
			getAccountStatement: getAccountStatementOp{
				isCalled: true,
				input: &grpc.GetAccountStatementRequest{
					Account: &grpc.AccountTerminal{
						BankCode: testsdk.Ptr("bcc"),
						Config:   structbConfig,
					},
					Date: testsdk.Ptr("10.11.2009"),
				},
				output:    nil,
				outputErr: errors.New("some error"),
			},
			appConfig: map[string]any{
				"SYMMETRIC_KEY": normalKey,
			},
		},
		{
			name: "error_process_statement",
			req: model.Account{
				ID:              1,
				BankID:          25,
				BankCode:        "bcc",
				Number:          "some number",
				EncryptedConfig: defaultEncryptedConfig,
			},
			wantErr: errors.New("some error"),
			getAccountStatement: getAccountStatementOp{
				isCalled: true,
				input: &grpc.GetAccountStatementRequest{
					Account: &grpc.AccountTerminal{
						BankCode: testsdk.Ptr("bcc"),
						Config:   structbConfig,
					},
					Date: testsdk.Ptr("10.11.2009"),
				},
				output: &grpc.GetAccountStatementResponse{
					Operations: []*grpc.TransitAccountOperation{
						{
							ExternalReferenceId: testsdk.Ptr("some external reference id"),
							MerchantAccount:     testsdk.Ptr("some merchant account"),
							Date:                testsdk.Ptr("2009-11-10 23:00:00"),
						},
					},
				},
				outputErr: nil,
			},
			processStatement: processStatementOp{
				isCalled: true,
				inputOperation: &grpc.TransitAccountOperation{
					ExternalReferenceId: testsdk.Ptr("some external reference id"),
					MerchantAccount:     testsdk.Ptr("some merchant account"),
					Date:                testsdk.Ptr("2009-11-10 23:00:00"),
				},
				inputAccount: model.Account{
					ID:              1,
					BankID:          25,
					BankCode:        "bcc",
					Number:          "some number",
					EncryptedConfig: defaultEncryptedConfig,
				},
				outputErr: errors.New("some error"),
			},
			appConfig: map[string]any{
				"SYMMETRIC_KEY": normalKey,
			},
		},
		{
			name: "success",
			req: model.Account{
				ID:              1,
				BankID:          25,
				BankCode:        "bcc",
				Number:          "some number",
				EncryptedConfig: defaultEncryptedConfig,
			},
			wantErr: nil,
			getAccountStatement: getAccountStatementOp{
				isCalled: true,
				input: &grpc.GetAccountStatementRequest{
					Account: &grpc.AccountTerminal{
						BankCode: testsdk.Ptr("bcc"),
						Config:   structbConfig,
					},
					Date: testsdk.Ptr("10.11.2009"),
				},
				output: &grpc.GetAccountStatementResponse{
					Operations: []*grpc.TransitAccountOperation{
						{
							ExternalReferenceId: testsdk.Ptr("some external reference id"),
							MerchantAccount:     testsdk.Ptr("some merchant account"),
							Date:                testsdk.Ptr("2009-11-10 23:00:00"),
						},
					},
				},
				outputErr: nil,
			},
			processStatement: processStatementOp{
				isCalled: true,
				inputOperation: &grpc.TransitAccountOperation{
					ExternalReferenceId: testsdk.Ptr("some external reference id"),
					MerchantAccount:     testsdk.Ptr("some merchant account"),
					Date:                testsdk.Ptr("2009-11-10 23:00:00"),
				},
				inputAccount: model.Account{
					ID:              1,
					BankID:          25,
					BankCode:        "bcc",
					Number:          "some number",
					EncryptedConfig: defaultEncryptedConfig,
				},
				outputErr: nil,
			},
			appConfig: map[string]any{
				"SYMMETRIC_KEY": normalKey,
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctrl := gomock.NewController(t)
			defer ctrl.Finish()

			c := context.Background()
			ctx, cancel := context.WithCancel(c)
			e2e.InitAppE2E(t, ctx, cancel, nil, nil, tt.appConfig)

			multiaccountingCliMock := grpcmock.NewMockMultiaccountingClient(ctrl)

			s := InTransferParserService{
				multiaccountingClient: multiaccountingCliMock,
				getCurrentTime: func() time.Time {
					return testNow
				},
			}

			if tt.getAccountStatement.isCalled {
				multiaccountingCliMock.EXPECT().GetAccountStatement(
					gomock.Any(),
					tt.getAccountStatement.input,
				).Return(
					tt.getAccountStatement.output, tt.getAccountStatement.outputErr,
				).Times(1)

				multiaccountingCliMock.EXPECT().GetAccountStatement(
					gomock.Any(),
					gomock.Any(),
				).Return(
					nil, nil,
				).AnyTimes() // required to cover case for second iteration (when yesterday is in for)
			}

			patches := gomonkey.NewPatches()
			if tt.processStatement.isCalled {
				patches.ApplyMethod(reflect.TypeOf(&s), "ProcessStatement", func(
					_ *InTransferParserService,
					ctx context.Context,
					statement *grpc.TransitAccountOperation,
					account model.Account,
				) error {
					t.Helper()

					require.Equal(t, tt.processStatement.inputAccount, account)
					require.Equal(t, tt.processStatement.inputOperation, statement)

					return tt.processStatement.outputErr
				})
			}

			err = s.ProcessScheduler(context.Background(), tt.req)
			if tt.wantErr != nil {
				require.Error(t, err)
				require.Equal(t, tt.wantErr, err)
			} else {
				require.NoError(t, err)
			}
		})
	}
}

func TestParseIncomintTransfers(t *testing.T) {
	type getAllOp struct {
		output    []model.InTransferParsingSchedule
		outputErr error
	}

	type getAllAccountInfoOp struct {
		isCalled  bool
		output    []model.Account
		outputErr error
	}

	type processSchedulerOp struct {
		isCalled  bool
		input     model.Account
		outputErr error
	}

	tests := []struct {
		name              string
		wantErr           error
		getAll            getAllOp
		getAllAccountInfo getAllAccountInfoOp
		processScheduler  processSchedulerOp
	}{
		{
			name:    "error_when_getting_all_schedule",
			wantErr: errors.New("some error"),
			getAll: getAllOp{
				output:    nil,
				outputErr: errors.New("some error"),
			},
		},
		{
			name:    "error_getting_all_account_info",
			wantErr: errors.New("some error"),
			getAll: getAllOp{
				output: []model.InTransferParsingSchedule{
					{
						ID:        1,
						AccountID: 1,
					},
				},
				outputErr: nil,
			},
			getAllAccountInfo: getAllAccountInfoOp{
				isCalled:  true,
				output:    nil,
				outputErr: errors.New("some error"),
			},
		},
		{
			name:    "error_process_scheduler",
			wantErr: errors.New("some error"),
			getAll: getAllOp{
				output: []model.InTransferParsingSchedule{
					{
						ID:        1,
						AccountID: 1,
					},
				},
				outputErr: nil,
			},
			getAllAccountInfo: getAllAccountInfoOp{
				isCalled: true,
				output: []model.Account{
					{
						ID:           1,
						Number:       "some num",
						CurrencyCode: "some currency",
						BankID:       25,
						BankCode:     "bcc",
					},
				},
				outputErr: nil,
			},
			processScheduler: processSchedulerOp{
				isCalled: true,
				input: model.Account{
					ID:           1,
					BankID:       25,
					BankCode:     "bcc",
					Number:       "some num",
					CurrencyCode: "some currency",
				},
				outputErr: errors.New("some error"),
			},
		},
		{
			name:    "success",
			wantErr: nil,
			getAll: getAllOp{
				output: []model.InTransferParsingSchedule{
					{
						ID:        1,
						AccountID: 1,
					},
				},
				outputErr: nil,
			},
			getAllAccountInfo: getAllAccountInfoOp{
				isCalled: true,
				output: []model.Account{
					{
						ID:           1,
						Number:       "some num",
						CurrencyCode: "some currency",
						BankID:       25,
						BankCode:     "bcc",
					},
				},
				outputErr: nil,
			},
			processScheduler: processSchedulerOp{
				isCalled: true,
				input: model.Account{
					ID:           1,
					BankID:       25,
					BankCode:     "bcc",
					Number:       "some num",
					CurrencyCode: "some currency",
				},
				outputErr: nil,
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctrl := gomock.NewController(t)
			defer ctrl.Finish()

			patches := gomonkey.NewPatches()
			defer patches.Reset()

			inTransferParsingScheduleDBMock := databasemocks.NewMockInTransferParsingScheduler(ctrl)
			accountInfoDBMock := databasemocks.NewMockAccountInformer(ctrl)

			inTransferParsingScheduleDBMock.EXPECT().GetAll(
				gomock.Any(),
			).Return(
				tt.getAll.output, tt.getAll.outputErr,
			).Times(1)

			if tt.getAllAccountInfo.isCalled {
				accountInfoDBMock.EXPECT().GetAll(
					gomock.Any(),
				).Return(
					tt.getAllAccountInfo.output, tt.getAllAccountInfo.outputErr,
				).Times(1)
			}

			s := InTransferParserService{
				accountInfoRepo:               accountInfoDBMock,
				inTransferParsingScheduleRepo: inTransferParsingScheduleDBMock,
			}

			if tt.processScheduler.isCalled {
				patches.ApplyMethod(reflect.TypeOf(&s), "ProcessScheduler", func(
					_ *InTransferParserService,
					ctx context.Context,
					account model.Account,
				) error {
					t.Helper()

					require.Equal(t, tt.processScheduler.input, account)

					return tt.processScheduler.outputErr
				})
			}

			err := s.ParseIncomingTransfers(context.Background())
			if tt.wantErr != nil {
				require.Error(t, err)
				require.Equal(t, tt.wantErr, err)
			} else {
				require.NoError(t, err)
			}

		})
	}
}
