// Code generated by protoc-gen-go-mvp. DO NOT EDIT.
// versions:
// - protoc-gen-go-mvp v2.0.1
// created by mvp authors

package grpc

import (
	math "math"
	strings "strings"
)

func (val EnumEpayPayInCode) Synonym() EnumEpayPayInCode {
	if _, ok := EnumEpayPayInCode_name[int32(val)]; ok {
		return val
	}

	return EnumEpayPayInCode(math.MinInt32)
}

func (val EnumEpayPayInCode) Int() int {
	return int(val.Synonym())
}

func (val EnumEpayPayInCode) IntPtr() *int {
	result := int(val.Synonym())
	return &result
}

func (val EnumEpayPayInCode) Int32() int32 {
	return int32(val.Synonym())
}

func (val EnumEpayPayInCode) Int32Ptr() *int32 {
	result := int32(val.Synonym())
	return &result
}

func (val EnumEpayPayInCode) Int64() int64 {
	return int64(val.Synonym())
}

func (val EnumEpayPayInCode) Int64Ptr() *int64 {
	result := int64(val.Synonym())
	return &result
}

func (val EnumEpayPayInCode) Uint() uint {
	return uint(val.Synonym())
}

func (val EnumEpayPayInCode) UintPtr() *uint {
	result := uint(val.Synonym())
	return &result
}

func (val EnumEpayPayInCode) Uint32() uint32 {
	return uint32(val.Synonym())
}

func (val EnumEpayPayInCode) Uint32Ptr() *uint32 {
	result := uint32(val.Synonym())
	return &result
}

func (val EnumEpayPayInCode) Uint64() uint64 {
	return uint64(val.Synonym())
}

func (val EnumEpayPayInCode) Uint64Ptr() *uint64 {
	result := uint64(val.Synonym())
	return &result
}

func (val EnumEpayPayInCode) IsKnown() bool {
	return val.Synonym() != EnumEpayPayInCode(math.MinInt32)
}

func ConvertIntToEnumEpayPayInCode(in int) EnumEpayPayInCode {
	return EnumEpayPayInCode(in).Synonym()
}

func ConvertUintToEnumEpayPayInCode(in uint) EnumEpayPayInCode {
	return EnumEpayPayInCode(in).Synonym()
}

func ConvertInt32ToEnumEpayPayInCode(in int32) EnumEpayPayInCode {
	return EnumEpayPayInCode(in).Synonym()
}

func ConvertUint32ToEnumEpayPayInCode(in uint32) EnumEpayPayInCode {
	return EnumEpayPayInCode(in).Synonym()
}

func ConvertInt64ToEnumEpayPayInCode(in int64) EnumEpayPayInCode {
	return EnumEpayPayInCode(in).Synonym()
}

func ConvertUint64ToEnumEpayPayInCode(in uint64) EnumEpayPayInCode {
	return EnumEpayPayInCode(in).Synonym()
}

var EnumEpayPayInCode_Lower_value = map[string]EnumEpayPayInCode{
	"empty_response_code":              0,
	"clientauthenticationcode_33":      -33,
	"systemerrorpleasetrycode_50":      -50,
	"errorincvc2orcvc2descode_18":      -18,
	"invalidretrievalrefecode_15":      -15,
	"threedsecurecheckfailedcode_455":  455,
	"accessdeniedcode_456":             456,
	"errorincardexpiratiocode_457":     457,
	"serverisnotrespondincode_458":     458,
	"noorinvalidresponcercode_460":     460,
	"badcgirequestcode_461":            461,
	"callyourbankcode_462":             462,
	"callyourbankcode_463":             463,
	"invalidmerchantcode_464":          464,
	"yourcardisrestrictedcode_465":     465,
	"notpermittedtoclientcode_466":     466,
	"yourcardisdisabledcode_467":       467,
	"additionalidentificacode_468":     468,
	"invalidtransactioncode_469":       469,
	"invalidamountcode_470":            470,
	"nosuchcardcode_471":               471,
	"nosuchcardcode_472":               472,
	"authenticationfailedcode_473":     473,
	"invalidresponsecode_475":          475,
	"noactiontakencode_476":            476,
	"formaterrorcode_477":              477,
	"expiredcardcode_478":              478,
	"restrictedcardcode_479":           479,
	"callyourbankcode_480":             480,
	"lostcardcode_481":                 481,
	"lostcardcode_482":                 482,
	"stolencardcode_483":               483,
	"expiredcardcode_485":              485,
	"notpermittedtoclientcode_486":     486,
	"notpermittedtomerchacode_487":     487,
	"exceedsamountlimitcode_488":       488,
	"restrictedcardcode_489":           489,
	"invalidcontractcardcode_490":      490,
	"exceedsfrequencylimicode_491":     491,
	"pintriesexceededcode_492":         492,
	"timeoutatissuersystecode_493":     493,
	"issuerunavailablecode_494":        494,
	"cannotbecompletedviocode_495":     495,
	"threedsecuresecurecodeabcode_496": 496,
	"serverisnotrespondincode_497":     497,
	"threedsecurecheckfailedcode_499":  499,
	"threedsecurecheckfailedcode_500":  500,
	"cardcheckfailedcode_501":          501,
	"threedsecuresecurecodeabcode_502": 502,
	"threedsecuresecurecodeabcode_503": 503,
	"transactiondeclinedccode_521":     521,
	"recordnotfoundchecktcode_522":     522,
	"transactiondeclinedccode_523":     523,
	"yourcardisrestrictedcode_524":     524,
	"yourcardisrestrictedcode_525":     525,
	"systemerrorpleasetrycode_526":     526,
	"transactiondeclinedccode_527":     527,
	"thedailylimitofincomcode_528":     528,
	"transactiondeclinedccode_531":     531,
	"unabletoverifypincalcode_532":     532,
	"authenticationfailedcode_19":      -19,
	"errorincardexpiratiocode_9":       -9,
	"serverisnotrespondincode_8":       -8,
	"serverisnotrespondincode_4":       -4,
	"errorbindactioncreatcode_1":       1,
	"errorcreatingrecordicode_2":       2,
	"errorbindactionupdatcode_3":       3,
	"errorwhileupdatingrecode_4":       4,
	"errorwhiledeletingthcode_5":       5,
	"errorgetbynameactioncode_6":       6,
	"errorgetbyidactiongecode_7":       7,
	"errorbindactioncreatcode_8":       8,
	"errorcreatingrecordicode_9":       9,
	"redisactioncreateerrcode_10":      10,
	"errorbindactionupdatcode_11":      11,
	"errorwhileupdatingrecode_12":      12,
	"redisactionupdateerrcode_13":      13,
	"errorwhiledeletingthcode_14":      14,
	"redisactiondeleteerrcode_15":      15,
	"errorgetbycodeinpostcode_16":      16,
	"codeactiongeterrorbycode_17":      17,
	"idapplicationidapplicode_18":      18,
	"codecode_19":                      19,
	"idapplicationidapplicode_20":      20,
	"idapplicationapplicacode_21":      21,
	"code_22":                          22,
	"successfullycode_23":              23,
	"test111code_24":                   24,
	"invoiceidcode_25":                 25,
	"bindjsonbindjsoncode_27":          27,
	"transactionalreadyprcode_28":      28,
	"transactionalreadyprcode_29":      29,
	"errorgetshopsbymerchcode_54":      54,
	"idcode_55":                        55,
	"errorgetshopbyidcode_56":          56,
	"errorbindcode_57":                 57,
	"errorbindcode_58":                 58,
	"errorcreateshopcode_59":           59,
	"errorgetmerchantbyidcode_60":      60,
	"errorgetstatusbynamecode_61":      61,
	"errorcreatedefaultcocode_64":      64,
	"errorcreatemerchantcode_65":       65,
	"errorverificationsercode_71":      71,
	"errorcheckmerchantcode_72":        72,
	"errorsendverificatiocode_73":      73,
	"errorverificationsercode_74":      74,
	"errorfindmerchantbyecode_75":      75,
	"errorupdatemerchantcode_76":       76,
	"errorregistrationusecode_77":      77,
	"rrncode_83":                       83,
	"errorgetcardtypebyrrcode_84":      84,
	"errorchecktransactiocode_85":      85,
	"unauthorizedcode_86":              86,
	"tokenisnotvalidcode_87":           87,
	"errorchecktokencode_88":           88,
	"errornoscopescode_89":             89,
	"errornoscopescode_90":             90,
	"errorchecktokencode_91":           91,
	"cardnumber14code_92":              92,
	"errorgetcardcorporatcode_94":      94,
	"errorgetcountrybybincode_95":      95,
	"invaliddatacode_96":               96,
	"errorchecktokencode_97":           97,
	"errornoscopescode_98":             98,
	"contractcode_99":                  99,
	"invaliddateformatplecode_111":     111,
	"contractcode_112":                 112,
	"extcsvxlsxcode_113":               113,
	"errorgetstatementbydcode_114":     114,
	"errorgetstatementbyocode_115":     115,
	"errorgetstatementaircode_116":     116,
	"errorgetstatementbytcode_117":     117,
	"errorchecktokencode_121":          121,
	"code_122":                         122,
	"errorbindcode_123":                123,
	"code_124":                         124,
	"code_125":                         125,
	"code_126":                         126,
	"code_127":                         127,
	"erroremptymerchantidcode_128":     128,
	"errorunknownmerchantcode_129":     129,
	"servererrorcode_130":              130,
	"errorchecktokencode_131":          131,
	"errorinvalidscopecode_132":        132,
	"errorbindcode_133":                133,
	"errorrequestpostformcode_134":     134,
	"errorbodyisemptycode_135":         135,
	"errorrequestisnotsuccode_136":     136,
	"unkwonerrorcode_137":              137,
	"secretincorrecterrorcode_138":     138,
	"code_139":                         139,
	"secretincorrectcode_140":          140,
	"errorgetclientfromrecode_141":     141,
	"errorparseclientcode_142":         142,
	"checkscopescode_143":              143,
	"useridpasswordcode_144":           144,
	"userstatuschangepasscode_145":     145,
	"usernotfoundcode_146":             146,
	"statuscode_147":                   147,
	"code_148":                         148,
	"userstatusinvalidcode_149":        149,
	"statusisnotdefinedcode_150":       150,
	"passwordincorrectcode_151":        151,
	"granttypecode_152":                152,
	"refreshtokencode_153":             153,
	"code_154":                         154,
	"scopecode_155":                    155,
	"refreshtokencode_156":             156,
	"generateaccesserrorcode_157":      157,
	"generateaccesserrorcode_158":      158,
	"extensionfieldadderrcode_159":     159,
	"extensionfieldadderrcode_160":     160,
	"code_161":                         161,
	"code_162":                         162,
	"getuserbyloginerrorccode_163":     163,
	"code_164":                         164,
	"getstatusbynameerrorcode_165":     165,
	"hashpassworderrorcode_166":        166,
	"saveusererrorcode_167":            167,
	"updateusererrorcode_169":          169,
	"updateusererrorcode_170":          170,
	"getuserbyloginerrorccode_171":     171,
	"code_172":                         172,
	"code_173":                         173,
	"actionservicegetdebicode_178":     178,
	"actionservicegettrancode_179":     179,
	"referenceepaycode_180":            180,
	"rediscode_181":                    181,
	"code_182":                         182,
	"code_185":                         185,
	"errorunmarshalcode_186":           186,
	"startdateandenddatescode_193":     193,
	"epaycode_194":                     194,
	"noauthorizecode_197":              197,
	"badtokencode_198":                 198,
	"parseerrorcode_199":               199,
	"paymenterrorcode_200":             200,
	"code_216":                         216,
	"errorbindjsoncannotucode_224":     224,
	"likecode_225":                     225,
	"code_226":                         226,
	"betweencode_227":                  227,
	"code_228":                         228,
	"code_229":                         229,
	"code_230":                         230,
	"code_231":                         231,
	"code_232":                         232,
	"errorchecktokencode_233":          233,
	"errornoscopescode_234":            234,
	"fromdateyyyymmddcode_235":         235,
	"todateyyyymmddcode_236":           236,
	"errorchecktokencode_237":          237,
	"errornoscopescode_238":            238,
	"cannotunmarshalintoscode_239":     239,
	"code_240":                         240,
	"code_241":                         241,
	"code_242":                         242,
	"idcode_243":                       243,
	"merchantidcode_244":               244,
	"startdateandenddatescode_245":     245,
	"code_246":                         246,
	"notemailcode_247":                 247,
	"jsoncode_248":                     248,
	"code_249":                         249,
	"msgvaluecode_250":                 250,
	"code_251":                         251,
	"code_252":                         252,
	"code_263":                         263,
	"code_264":                         264,
	"code_265":                         265,
	"secretincorrecterrorcode_266":     266,
	"scopenotinchangepasscode_267":     267,
	"unsupportedscopecode_268":         268,
	"blockcode_269":                    269,
	"blockcode_270":                    270,
	"usernotsavecode_271":              271,
	"usernotsavecode_272":              272,
	"failedtogetstatuscode_273":        273,
	"useralreadyexistcode_274":         274,
	"useralreadyexistcode_275":         275,
	"incorrectdatacode_276":            276,
	"incorrectdatacode_277":            277,
	"notemailcode_278":                 278,
	"notemailcode_279":                 279,
	"code_280":                         280,
	"transactioncreateerrcode_281":     281,
	"code_282":                         282,
	"code_283":                         283,
	"testmecode_285":                   285,
	"statementcode_286":                286,
	"errorchecktokencode_287":          287,
	"errornoscopescode_288":            288,
	"tofromyyyymmddcode_289":           289,
	"code_290":                         290,
	"fromdateandtodateshocode_291":     291,
	"errorwhilecreatingrecode_292":     292,
	"code_293":                         293,
	"requiredclientcredencode_294":     294,
	"ididcode_295":                     295,
	"errorupdatestaffcode_296":         296,
	"staffcode_297":                    297,
	"code_298":                         298,
	"staffcode_299":                    299,
	"binderrorcode_300":                300,
	"updatestaffcode_301":              301,
	"multipartformcode_302":            302,
	"expireatyyyymmddcode_303":         303,
	"expireatyyyymmddcode_304":         304,
	"expireatcode_305":                 305,
	"expireatcode_306":                 306,
	"code_307":                         307,
	"code_308":                         308,
	"code_309":                         309,
	"commiterrorcode_310":              310,
	"bindjsonerrorcode_311":            311,
	"code_312":                         312,
	"searchtransactionerrcode_318":     318,
	"jsoncode_319":                     319,
	"code_320":                         320,
	"code_321":                         321,
	"code_322":                         322,
	"fromdateyyyymmddcode_323":         323,
	"todateyyyymmddcode_324":           324,
	"epaycode_325":                     325,
	"errorgetmerchantinfocode_326":     326,
	"code_327":                         327,
	"code_328":                         328,
	"invoiceidcode_329":                329,
	"terminalcode_330":                 330,
	"currencycode_331":                 331,
	"amountcode_332":                   332,
	"code_333":                         333,
	"shopidcode_334":                   334,
	"merchantidcode_335":               335,
	"shopidcode_336":                   336,
	"errorgetmerchantinfocode_337":     337,
	"code_338":                         338,
	"code_339":                         339,
	"code_340":                         340,
	"contractcode_342":                 342,
	"getshopbycontractcode_343":        343,
	"code_344":                         344,
	"marshalerrorcode_345":             345,
	"code_346":                         346,
	"code_347":                         347,
	"code_348":                         348,
	"code_349":                         349,
	"code_350":                         350,
	"code_351":                         351,
	"code_352":                         352,
	"topcode_353":                      353,
	"code_354":                         354,
	"emailcode_355":                    355,
	"code_356":                         356,
	"code_357":                         357,
	"code_358":                         358,
	"code_359":                         359,
	"code_360":                         360,
	"code_361":                         361,
	"code_362":                         362,
	"parseerrorcode_363":               363,
	"parseerrorcode_364":               364,
	"code_365":                         365,
	"parseerrorcode_366":               366,
	"parseerrorcode_367":               367,
	"code_368":                         368,
	"parseerrorcode_369":               369,
	"parseerrorcode_370":               370,
	"code_371":                         371,
	"code_372":                         372,
	"noemailcode_373":                  373,
	"code_374":                         374,
	"code_375":                         375,
	"code_376":                         376,
	"incorrectformatdatecode_377":      377,
	"getfilefiledoesnotexcode_378":     378,
	"code_379":                         379,
	"code_380":                         380,
	"code_381":                         381,
	"code_382":                         382,
	"code_383":                         383,
	"egatewaycode_384":                 384,
	"egatewaycode_385":                 385,
	"egatewaycode_386":                 386,
	"egatewaycode_387":                 387,
	"egatewaycode_388":                 388,
	"userdeletecode_389":               389,
	"parseerrorcode_390":               390,
	"callbacksendmessageecode_391":     391,
	"code_392":                         392,
	"code_393":                         393,
	"code_394":                         394,
	"code_395":                         395,
	"code_396":                         396,
	"mvisacode_397":                    397,
	"threedsecurecode_398":             398,
	"mvisaverificationcode_399":        399,
	"code_400":                         400,
	"code_401":                         401,
	"code_402":                         402,
	"carddataisnotrequirecode_403":     403,
	"code_404":                         404,
	"code_405":                         405,
	"code_406":                         406,
	"code_407":                         407,
	"code_408":                         408,
	"code_409":                         409,
	"mvisacode_410":                    410,
	"noauthorizecode_411":              411,
	"badtokencode_412":                 412,
	"code_413":                         413,
	"timeouttransactioncode_504":       504,
	"exceededattemptscode_505":         505,
	"code_506":                         506,
	"code_507":                         507,
	"code_508":                         508,
	"code_509":                         509,
	"rabbitcode_510":                   510,
	"code_512":                         512,
	"postlinkcode_513":                 513,
	"code_514":                         514,
	"code_515":                         515,
	"code_516":                         516,
	"therewasnoattempttopcode_517":     517,
	"ipcode_518":                       518,
	"invoiceidcode_520":                520,
	"terminalidcode_511":               511,
	"egwcode_546":                      546,
	"egwhttpcode200code_547":           547,
	"code_559":                         559,
	"egwcode_560":                      560,
	"egwhttpcode200code_561":           561,
	"mdcode_529":                       529,
	"egwcode_530":                      530,
	"code_570":                         570,
	"egwcode_571":                      571,
	"egwhttpcode200code_533":           533,
	"code_534":                         534,
	"code_535":                         535,
	"merchantcode_536":                 536,
	"merchantisactivefalscode_537":     537,
	"code_538":                         538,
	"merchantcode_539":                 539,
	"scopenotfoundchecksccode_540":     540,
	"errorparameternamecode_541":       541,
	"unauthorizedchecktokcode_542":     542,
	"errorinvalidscopecode_543":        543,
	"errorbindcode_544":                544,
	"unkwonerrorcode_548":              548,
	"transactionalreadycacode_549":     549,
	"requesttoxlsserverfacode_550":     550,
	"requesttoxlsserverrecode_551":     551,
	"invalidbonusamountcode_552":       552,
	"invalidinputdatacode_553":         553,
	"terminalconfiguratiocode_554":     554,
	"unabletogetgetrequescode_555":     555,
	"requesttoapixlsfailecode_556":     556,
	"requesttoapixlsreturcode_557":     557,
	"gobonusconvertiontofcode_558":     558,
	"code_563":                         563,
	"terminalidcode_564":               564,
	"requesttogetcardinfocode_565":     565,
	"requesttogetcardinfocode_566":     566,
	"cardnotfoundcode_567":             567,
	"cardownernotfoundcode_568":        568,
	"code_569":                         569,
	"code_572":                         572,
	"requesttoapixlsfailecode_573":     573,
	"requesttoapixlsreturcode_574":     574,
	"unauthorizedchecktokcode_576":     576,
	"cannotcancelxlstranscode_578":     578,
	"cannotsendtransactiocode_579":     579,
	"cannotsendcancelxlstcode_580":     580,
	"cannotcreaterabbitincode_581":     581,
	"changepaymentcode_582":            582,
	"code_583":                         583,
	"xlscode_584":                      584,
	"gocode_585":                       585,
	"code_586":                         586,
	"code_587":                         587,
	"code_588":                         588,
	"code_589":                         589,
	"code_590":                         590,
	"code_591":                         591,
	"code_592":                         592,
	"code_593":                         593,
	"code_594":                         594,
	"code_595":                         595,
	"epay1documentcode_596":            596,
	"crtificateidcode_597":             597,
	"code_598":                         598,
	"code_599":                         599,
	"code_600":                         600,
	"code_601":                         601,
	"code_602":                         602,
	"code_603":                         603,
	"code_604":                         604,
	"code_605":                         605,
	"code_606":                         606,
	"code_607":                         607,
	"code_608":                         608,
	"code_609":                         609,
	"code_610":                         610,
	"code_611":                         611,
	"code_612":                         612,
	"code_613":                         613,
	"code_614":                         614,
	"code_615":                         615,
	"code_616":                         616,
	"invoiceidcode_617":                617,
	"terminalcode_618":                 618,
	"terminalcode_619":                 619,
	"currencycode_620":                 620,
	"amountcode_621":                   621,
	"amountcode_622":                   622,
	"amountcode_623":                   623,
	"code_624":                         624,
	"code_625":                         625,
	"code_626":                         626,
	"code_627":                         627,
	"code_628":                         628,
	"code_629":                         629,
	"code_630":                         630,
	"code_631":                         631,
	"code_632":                         632,
	"code_633":                         633,
	"code_634":                         634,
	"egatewaycode_635":                 635,
	"egatewaycode_636":                 636,
	"egatewaycode_637":                 637,
	"code_638":                         638,
	"code_639":                         639,
	"code_640":                         640,
	"code_641":                         641,
	"code_642":                         642,
	"callbacksendmessageecode_643":     643,
	"code_644":                         644,
	"code_645":                         645,
	"code_646":                         646,
	"code_647":                         647,
	"code_648":                         648,
	"code_649":                         649,
	"code_650":                         650,
	"code_651":                         651,
	"code_652":                         652,
	"code_653":                         653,
	"code_654":                         654,
	"code_655":                         655,
	"code_656":                         656,
	"code_657":                         657,
	"code_658":                         658,
	"code_659":                         659,
	"code_660":                         660,
	"code_661":                         661,
	"code_662":                         662,
	"code_663":                         663,
	"code_664":                         664,
	"code_665":                         665,
	"code_666":                         666,
	"code_667":                         667,
	"code_668":                         668,
	"code_669":                         669,
	"code_670":                         670,
	"code_671":                         671,
	"code_672":                         672,
	"code_673":                         673,
	"egwhttpcode200code_674":           674,
	"code_675":                         675,
	"code_676":                         676,
	"code_677":                         677,
	"code_678":                         678,
	"code_679":                         679,
	"requesttogetcardinfocode_680":     680,
	"requesttogetcardinfocode_681":     681,
	"xlscode_683":                      683,
	"xlscode_684":                      684,
	"code_685":                         685,
	"terminalconfiguratiocode_686":     686,
	"requesttoxlsserverfacode_687":     687,
	"requesttoxlsserverfacode_688":     688,
	"code_689":                         689,
	"code_690":                         690,
	"code_691":                         691,
	"jsoncode_692":                     692,
	"code_693":                         693,
	"code_694":                         694,
	"statementcode_695":                695,
	"statementcode_696":                696,
	"statementcode_697":                697,
	"statementcode_698":                698,
	"statementcode_699":                699,
	"tofromyyyymmddcode_700":           700,
	"tofromyyyymmddcode_701":           701,
	"tofromyyyymmddcode_702":           702,
	"code_703":                         703,
	"code_704":                         704,
	"code_705":                         705,
	"code_706":                         706,
	"code_707":                         707,
	"staffcode_708":                    708,
	"staffcode_709":                    709,
	"staffcode_710":                    710,
	"code_711":                         711,
	"code_712":                         712,
	"code_713":                         713,
	"code_714":                         714,
	"code_715":                         715,
	"code_716":                         716,
	"code_717":                         717,
	"code_718":                         718,
	"code_719":                         719,
	"errorinservicingtheccode_720":     720,
	"errorinservicingtheccode_721":     721,
	"errorinservicingtheccode_722":     722,
	"errorinservicingtheccode_723":     723,
	"errorinservicingtheccode_724":     724,
	"errorinservicingtheccode_725":     725,
	"errorinservicingtheccode_726":     726,
	"errorinservicingtheccode_727":     727,
	"noauthorizecode_728":              728,
	"badtokencode_729":                 729,
	"code_730":                         730,
	"invalidrequestinputcode_731":      731,
	"code_732":                         732,
	"code_733":                         733,
	"code_734":                         734,
	"code_735":                         735,
	"code_736":                         736,
	"idcode_737":                       737,
	"code_738":                         738,
	"code_739":                         739,
	"code_740":                         740,
	"code_741":                         741,
	"code_742":                         742,
	"code_743":                         743,
	"code_744":                         744,
	"code_745":                         745,
	"code_746":                         746,
	"code_747":                         747,
	"code_748":                         748,
	"code_749":                         749,
	"code_750":                         750,
	"code_751":                         751,
	"code_752":                         752,
	"code_753":                         753,
	"apimakepaymentscorercode_754":     754,
	"code_756":                         756,
	"code_757":                         757,
	"merchantcode_758":                 758,
	"code_759":                         759,
	"code_760":                         760,
	"code_761":                         761,
	"code_762":                         762,
	"code_765":                         765,
	"egatewaycode_766":                 766,
	"code_767":                         767,
	"code_768":                         768,
	"code_769":                         769,
	"code_770":                         770,
	"code_771":                         771,
	"code_772":                         772,
	"code_773":                         773,
	"code_774":                         774,
	"code_775":                         775,
	"invoiceidcode_776":                776,
	"terminalcode_777":                 777,
	"currencycode_778":                 778,
	"amountcode_779":                   779,
	"amountcode_780":                   780,
	"corepaymentcode_781":              781,
	"code_782":                         782,
	"code_783":                         783,
	"code_784":                         784,
	"idcode_785":                       785,
	"terminalidcode_786":               786,
	"code_787":                         787,
	"code_789":                         789,
	"code_790":                         790,
	"code_791":                         791,
	"code_792":                         792,
	"code_793":                         793,
	"code_795":                         795,
	"corerecurrentcode_796":            796,
	"code_797":                         797,
	"code_798":                         798,
	"code_799":                         799,
	"code_800":                         800,
	"code_801":                         801,
	"code_802":                         802,
	"code_803":                         803,
	"code_804":                         804,
	"code_805":                         805,
	"publicidcode_806":                 806,
	"code_807":                         807,
	"code_808":                         808,
	"code_809":                         809,
	"code_810":                         810,
	"code_811":                         811,
	"code_812":                         812,
	"code_813":                         813,
	"code_814":                         814,
	"code_815":                         815,
	"code_816":                         816,
	"code_817":                         817,
	"code_818":                         818,
	"code_819":                         819,
	"code_820":                         820,
	"code_821":                         821,
	"code_822":                         822,
	"code_823":                         823,
	"code_824":                         824,
	"code_825":                         825,
	"code_826":                         826,
	"code_827":                         827,
	"code_828":                         828,
	"code_829":                         829,
	"code_830":                         830,
	"code_831":                         831,
	"code_832":                         832,
	"code_833":                         833,
	"code_834":                         834,
	"code_835":                         835,
	"code_836":                         836,
	"code_837":                         837,
	"code_838":                         838,
	"code_839":                         839,
	"code_840":                         840,
	"code_841":                         841,
	"code_842":                         842,
	"code_843":                         843,
	"code_844":                         844,
	"code_845":                         845,
	"publicidcode_846":                 846,
	"merchantapicode_847":              847,
	"code_848":                         848,
	"code_849":                         849,
	"code_850":                         850,
	"staffinviterequestcode_851":       851,
	"publicidcode_852":                 852,
	"code_853":                         853,
	"code_854":                         854,
	"publicidcode_855":                 855,
	"publicidcode_856":                 856,
	"code_857":                         857,
	"publicidcode_858":                 858,
	"code_859":                         859,
	"code_860":                         860,
	"code_861":                         861,
	"code_862":                         862,
	"code_863":                         863,
	"code_864":                         864,
	"code_865":                         865,
	"oauthcode_866":                    866,
	"code_867":                         867,
	"code_868":                         868,
	"code_869":                         869,
	"code_870":                         870,
	"code_871":                         871,
	"code_872":                         872,
	"oauthcode_873":                    873,
	"code_874":                         874,
	"oauthcode_875":                    875,
	"code_876":                         876,
	"mdcode_877":                       877,
	"code_878":                         878,
	"code_879":                         879,
	"code_880":                         880,
	"code_881":                         881,
	"egwcode_882":                      882,
	"rediscode_883":                    883,
	"rediscode_884":                    884,
	"rediscode_885":                    885,
	"rediscode_886":                    886,
	"code_887":                         887,
	"aclapicode_888":                   888,
	"code_889":                         889,
	"merchantapicode_890":              890,
	"code_891":                         891,
	"code_892":                         892,
	"merchantapicode_893":              893,
	"code_894":                         894,
	"corepaymentcode_895":              895,
	"confirmcode_896":                  896,
	"code_897":                         897,
	"publicidcode_898":                 898,
	"code_899":                         899,
	"code_900":                         900,
	"code_901":                         901,
	"publicidcode_902":                 902,
	"code_903":                         903,
	"code_904":                         904,
	"code_905":                         905,
	"code_906":                         906,
	"code_907":                         907,
	"code_908":                         908,
	"merchantapicode_909":              909,
	"merchantapicode_910":              910,
	"code_911":                         911,
	"code_912":                         912,
	"code_913":                         913,
	"code_914":                         914,
	"code_915":                         915,
	"code_916":                         916,
	"code_917":                         917,
	"code_918":                         918,
	"code_919":                         919,
	"code_920":                         920,
	"code_921":                         921,
	"code_922":                         922,
	"code_923":                         923,
	"code_924":                         924,
	"code_925":                         925,
	"code_926":                         926,
	"code_927":                         927,
	"code_928":                         928,
	"publicidcode_929":                 929,
	"code_930":                         930,
	"code_931":                         931,
	"scopescode_932":                   932,
	"code_933":                         933,
	"code_934":                         934,
	"code_935":                         935,
	"code_937":                         937,
	"code_938":                         938,
	"code_939":                         939,
	"code_940":                         940,
	"code_941":                         941,
	"code_942":                         942,
	"code_943":                         943,
	"code_944":                         944,
	"publicidcode_945":                 945,
	"code_946":                         946,
	"code_947":                         947,
	"code_948":                         948,
	"code_949":                         949,
	"code_950":                         950,
	"publicidcode_951":                 951,
	"code_952":                         952,
	"code_953":                         953,
	"code_954":                         954,
	"code_955":                         955,
	"code_956":                         956,
	"code_957":                         957,
	"code_958":                         958,
	"code_959":                         959,
	"code_960":                         960,
	"code_961":                         961,
	"code_962":                         962,
	"authorizedrequestjsocode_963":     963,
	"clientsecretcode_964":             964,
	"clientidclientsecretcode_965":     965,
	"merchantidclientsecrcode_966":     966,
	"clientsecretclientidcode_967":     967,
	"emailclientidclientscode_968":     968,
	"code_969":                         969,
	"code_970":                         970,
	"code_971":                         971,
	"code_972":                         972,
	"idcode_973":                       973,
	"code_974":                         974,
	"code_975":                         975,
	"userclientidclientsecode_976":     976,
	"authorizedrequestjsocode_977":     977,
	"jsonmarshalsendemailcode_978":     978,
	"code_979":                         979,
	"formfilecode_980":                 980,
	"code_981":                         981,
	"code_982":                         982,
	"code_983":                         983,
	"code_984":                         984,
	"code_985":                         985,
	"code_986":                         986,
	"code_987":                         987,
	"code_988":                         988,
	"code_989":                         989,
	"code_990":                         990,
	"code_991":                         991,
	"code_992":                         992,
	"servicecode_993":                  993,
	"shouldbindjsonserviccode_994":     994,
	"authorizedrequestjsocode_995":     995,
	"authorizedrequestjsocode_996":     996,
	"useruserusernameservcode_997":     997,
	"merchantservicecode_999":          999,
	"namewebservicecode_1000":          1000,
	"contacttypenameservicode_1001":    1001,
	"publicidcode_1002":                1002,
	"code_1003":                        1003,
	"code_1004":                        1004,
	"code_1005":                        1005,
	"oauthcode_1006":                   1006,
	"code_1007":                        1007,
	"code_1008":                        1008,
	"code_1009":                        1009,
	"idservicecode_1010":               1010,
	"rsaservicecode_1011":              1011,
	"servicecode_1012":                 1012,
	"codebase64stringbasecode_1013":    1013,
	"xmlunmarshalservicecode_1014":     1014,
	"codebase64stringbasecode_1015":    1015,
	"xmlmarshalservicecode_1016":       1016,
	"oauthcode_1017":                   1017,
	"servicecode_1018":                 1018,
	"shopidcode_1019":                  1019,
	"jsonservicecode_1020":             1020,
	"authservicecode_1021":             1021,
	"deviceidauthservicecode_1022":     1022,
	"servicecode_1023":                 1023,
	"idservicecode_1024":               1024,
	"bindxmlwebservicecode_1025":       1025,
	"authorizedrequestjsocode_1026":    1026,
	"bindclientidpostlinkcode_1027":    1027,
	"clientidpostlinkinfocode_1028":    1028,
	"merchantidpostlinkincode_1029":    1029,
	"postlinkrabbitserviccode_1030":    1030,
	"terminalidpostlinkincode_1031":    1031,
	"cbindpostlinkinfoposcode_1032":    1032,
	"jsonmarshalpostlinkicode_1033":    1033,
	"clientidclientidservcode_1034":    1034,
	"shopmerchantidclientcode_1035":    1035,
	"shopmerchantidclientcode_1036":    1036,
	"bindinvoicewebserviccode_1037":    1037,
	"expireperiodinvoicescode_1038":    1038,
	"postlinkrabbitserviccode_1039":    1039,
	"servicecode_1040":                 1040,
	"jsonservicecode_1041":             1041,
	"egwcode_1042":                     1042,
	"emailclientsecretsercode_1043":    1043,
	"emailwebservicecode_1044":         1044,
	"authorizedrequestjsocode_1045":    1045,
	"jsonmarshalmerchantocode_1046":    1046,
	"jsonmarshalmerchantocode_1047":    1047,
	"bindjsonmerchantwebscode_1048":    1048,
	"clientidservicecode_1049":         1049,
	"clientscopesclientidcode_1050":    1050,
	"userloginservicecode_1051":        1051,
	"clientclientscopesuscode_1052":    1052,
	"bindjsoninvoicecode_1053":         1053,
	"invoicecode_1054":                 1054,
	"xmlway4code_1055":                 1055,
	"invoicecode_1056":                 1056,
	"code_1057":                        1057,
	"code_1058":                        1058,
	"code_1059":                        1059,
	"coreinvoicecode_1060":             1060,
	"bindjsoninvoiceinfocode_1061":     1061,
	"code_1062":                        1062,
	"code_1063":                        1063,
	"scannererrcode_1064":              1064,
	"bindcode_1065":                    1065,
	"shopidcode_1066":                  1066,
	"invoicelinkcode_1067":             1067,
	"marshalinvoicelinkcode_1068":      1068,
	"invoicelinkcode_1069":             1069,
	"invoicelinkcode_1070":             1070,
	"invoicelinkcode_1071":             1071,
	"publicidinvoicecode_1072":         1072,
	"paramsinvoicecode_1073":           1073,
	"invoicelinkcode_1074":             1074,
	"bindcode_1075":                    1075,
	"strconvatoiexpirepercode_1076":    1076,
	"invoicelinkcode_1077":             1077,
	"emailcode_1078":                   1078,
	"smscode_1079":                     1079,
	"invoicelinkcode_1080":             1080,
	"bindcode_1081":                    1081,
	"publicidcode_1082":                1082,
	"nameinvoicelinkcode_1083":         1083,
	"invoicelinkcode_1084":             1084,
	"searchparamsinvoicelcode_1085":    1085,
	"incorrectsearchmethocode_1086":    1086,
	"code_1087":                        1087,
	"invoicelinkidcode_1088":           1088,
	"invoicelinkcode_1089":             1089,
	"code_1090":                        1090,
	"invoicelinkcode_1091":             1091,
	"publicidcode_1092":                1092,
	"code_1093":                        1093,
	"code_1096":                        1096,
	"homebankcode_1097":                1097,
	"emailcode_1098":                   1098,
	"code_1099":                        1099,
	"code_1100":                        1100,
	"coreinvoicecode_1101":             1101,
	"authorizedrequestjsocode_1103":    1103,
	"authorizedrequestjsocode_1104":    1104,
	"idservicecode_1105":               1105,
	"clientidservicecode_1106":         1106,
	"terminalxlsmidtermincode_1107":    1107,
	"code_1130":                        1130,
	"code_1132":                        1132,
	"namecode_1133":                    1133,
	"postlinkcorecallbackcode_1144":    1144,
	"code_1145":                        1145,
	"cannotsendpostlinkcode_1146":      1146,
	"cannotsendpostlinkcode_1147":      1147,
	"bindjsoncode_1148":                1148,
	"base64code_1149":                  1149,
	"unmarshalcode_1150":               1150,
	"httpcoremigrationcode_1151":       1151,
	"code_1152":                        1152,
	"unmarshalouathcode_1153":          1153,
	"epay1code_1154":                   1154,
	"unmarshalepay1code_1155":          1155,
	"cannotsendpostlinkcode_1156":      1156,
	"code_1157":                        1157,
	"userservicecode_1158":             1158,
	"userservicecode_1159":             1159,
	"userservicecode_1160":             1160,
	"userservicecode_1161":             1161,
	"userservicecode_1162":             1162,
	"merchantcompanynamescode_1163":    1163,
	"merchantservicecode_1164":         1164,
	"merchantmerchantcode_1165":        1165,
	"userloginuserclientcode_1166":     1166,
	"merchantnameusercliecode_1167":    1167,
	"authorizedrequestjsocode_1168":    1168,
	"merchantnamecode_1169":            1169,
	"emailmerchantcode_1170":           1170,
	"shopmerchantcode_1171":            1171,
	"terminalmerchantcode_1172":        1172,
	"userusercode_1173":                1173,
	"userclientidcode_1174":            1174,
	"userclientusercode_1175":          1175,
	"terminalterminalidcode_1176":      1176,
	"currencycurrencynamecode_1177":    1177,
	"terminalidcode_1178":              1178,
	"terminalterminalidtecode_1179":    1179,
	"terminalidcode_1180":              1180,
	"authorizedrequestjsocode_1182":    1182,
	"code_1184":                        1184,
	"terminalcode_1185":                1185,
	"terminalcode_1186":                1186,
	"code_1187":                        1187,
	"merchantcontacttypencode_1188":    1188,
	"senderrorreportcontacode_1189":    1189,
	"code_1190":                        1190,
	"idcode_1191":                      1191,
	"excelcode_1192":                   1192,
	"currencycurrencyidcode_1193":      1193,
	"cardtypecardtypeidcode_1194":      1194,
	"emailcode_1195":                   1195,
	"jsonmarshalemailcode_1196":        1196,
	"authorizedrequestjsocode_1197":    1197,
	"merchantcontacttypeecode_1198":    1198,
	"merchantcontactemailcode_1199":    1199,
	"excelfileemailcode_1200":          1200,
	"code_1201":                        1201,
	"userlogincode_1202":               1202,
	"rowsexcelfilereportcode_1203":     1203,
	"userconnectionsusericode_1205":    1205,
	"kafkacode_1206":                   1206,
	"code_1207":                        1207,
	"code_1208":                        1208,
	"code_1209":                        1209,
	"code_1210":                        1210,
	"authorizedrequestmulcode_1211":    1211,
	"formatdataheaderwritcode_1212":    1212,
	"writerreadermultiparcode_1213":    1213,
	"apicdncode_1214":                  1214,
	"logourlshopidcode_1215":           1215,
	"bindjsonshopshopidcode_1216":      1216,
	"shopiddbshopidcode_1217":          1217,
	"jsonsamsungpaycode_1219":          1219,
	"requestjsonsamsungpacode_1220":    1220,
	"shopidshopidcode_1221":            1221,
	"terminalshopidcode_1222":          1222,
	"authorizedrequestjsocode_1223":    1223,
	"shopidshopidcode_1224":            1224,
	"bindjsoninoutterminacode_1225":    1225,
	"ididcode_1226":                    1226,
	"authorizedrequestjsocode_1227":    1227,
	"terminalterminalidcode_1228":      1228,
	"terminalidterminalidcode_1229":    1229,
	"terminalterminalidcode_1230":      1230,
	"jsonmarshalterminaltcode_1231":    1231,
	"pancode_1232":                     1232,
	"samsungpaycallbackrecode_1233":    1233,
	"staffstaffcode_1234":              1234,
	"bindcreatemvisatranscode_1235":    1235,
	"requestpostformxmlcrcode_1236":    1236,
	"requestpostformxmlcrcode_1237":    1237,
	"requestpostformxmlcrcode_1238":    1238,
	"requestpostformxmlcrcode_1239":    1239,
	"bindget3dsecurecode_1240":         1240,
	"requestpostformxmlgecode_1241":    1241,
	"requestpostformxmlgecode_1242":    1242,
	"requestpostformxmlgecode_1243":    1243,
	"requestpostformxmlgecode_1244":    1244,
	"bindmanagetranscode_1245":         1245,
	"requestpostformxmlmacode_1246":    1246,
	"requestpostformxmlmacode_1247":    1247,
	"requestpostformxmlmacode_1248":    1248,
	"requestpostformxmlmacode_1249":    1249,
	"bindcreatetranscode_1250":         1250,
	"requestpostformxmlcrcode_1251":    1251,
	"requestpostformxmlcrcode_1252":    1252,
	"requestpostformxmlcrcode_1253":    1253,
	"requestpostformxmlcrcode_1254":    1254,
	"bindrepaymentcode_1255":           1255,
	"requestpostformxmlrecode_1256":    1256,
	"requestpostformxmlrecode_1257":    1257,
	"requestpostformxmlrecode_1258":    1258,
	"requestpostformxmlrecode_1259":    1259,
	"bindgettokencode_1260":            1260,
	"requestpostformxmlgecode_1261":    1261,
	"requestpostformxmlgecode_1262":    1262,
	"requestpostformxmlgecode_1263":    1263,
	"requestpostformxmlgecode_1264":    1264,
	"bindtransactioncode_1265":         1265,
	"requestpostformxmltrcode_1266":    1266,
	"cardsystemcommunicatcode_1267":    1267,
	"requestpostformxmltrcode_1268":    1268,
	"requestpostformxmltrcode_1269":    1269,
	"requestpostformxmltrcode_1270":    1270,
	"bindtransactionconficode_1271":    1271,
	"requestpostformxmltrcode_1272":    1272,
	"requestpostformxmltrcode_1273":    1273,
	"requestpostformxmltrcode_1274":    1274,
	"requestpostformxmltrcode_1275":    1275,
	"invoicelinksgetinvoicode_1276":    1276,
	"publiciduseridcode_1277":          1277,
	"paramscode_1278":                  1278,
	"postlinkcorecallbackcode_1279":    1279,
	"rabbitpostlinkcode_1280":          1280,
	"publicidtokeninvoicecode_1281":    1281,
	"slowpostlinkcorecallcode_1282":    1282,
	"getopenwayidhalykboncode_1283":    1283,
	"cardtypenilcode_1284":             1284,
	"cardtypecode_1285":                1285,
	"postlinkcorecallbackcode_1286":    1286,
	"postlinkcode_1287":                1287,
	"postlinkinvoiceidcode_1288":       1288,
	"callbackcorecallbackcode_1289":    1289,
	"invoicelinkinvoiceidcode_1290":    1290,
	"invoicelinkinvoiceidcode_1291":    1291,
	"invoicelinkinvoicelicode_1293":    1293,
	"errormessageinvoicelcode_1294":    1294,
	"transactionstatusautcode_1295":    1295,
	"filepathfileidcode_1296":          1296,
	"bindchargecode_1297":              1297,
	"code_1298":                        1298,
	"informationstatementcode_1299":    1299,
	"refidcode_1301":                   1301,
	"emailcode_1302":                   1302,
	"statementhistoryomitcode_1303":    1303,
	"stafflistcode_1304":               1304,
	"getcode_1305":                     1305,
	"authorizedrequestjsocode_1306":    1306,
	"code_1307":                        1307,
	"coreinvoiceinvoicelicode_1308":    1308,
	"coreinvoiceinvoicelicode_1309":    1309,
	"invoicelinkcode_1310":             1310,
	"invoicelinkinvoiceidcode_1311":    1311,
	"coreinvoiceinvoiceincode_1312":    1312,
	"kafkacode_1315":                   1315,
	"homebankacode_1317":               1317,
	"code_1318":                        1318,
	"useracode_1319":                   1319,
	"staffupdatepasswordcode_1320":     1320,
	"updatestaffrolecode_1321":         1321,
	"code_1322":                        1322,
	"commitcode_1323":                  1323,
	"code_1324":                        1324,
	"code_1325":                        1325,
	"code_1326":                        1326,
	"float64feeamountstricode_1327":    1327,
	"float64settlamountstcode_1328":    1328,
	"float64tramountstrincode_1329":    1329,
	"code_1330":                        1330,
	"code_1331":                        1331,
	"parsemediatypecode_1332":          1332,
	"createfilecode_1333":              1333,
	"emailcode_1334":                   1334,
	"hbcode_1335":                      1335,
	"code_1336":                        1336,
	"code_1337":                        1337,
	"activecode_1338":                  1338,
	"chargedcode_1339":                 1339,
	"merchantterminaltermcode_1340":    1340,
	"postingdatestringordcode_1341":    1341,
	"hbcode_1342":                      1342,
	"ftpcode_1343":                     1343,
	"jobexecutiongocroncode_1344":      1344,
	"float64feeamountstricode_1346":    1346,
	"float64settlamountstcode_1347":    1347,
	"float64tramountstrincode_1348":    1348,
	"merchantpublicidcode_1361":        1361,
	"merchantinfopublicidcode_1366":    1366,
	"publicidcode_1367":                1367,
	"code_1368":                        1368,
	"code_1369":                        1369,
	"code_1370":                        1370,
	"authorizedrequestjsocode_1371":    1371,
	"coregetcardcode_1372":             1372,
	"code_1373":                        1373,
	"oauthcode_1374":                   1374,
	"requestclientcode_1375":           1375,
	"code_1376":                        1376,
	"code_1378":                        1378,
	"scopecode_1379":                   1379,
	"scopecode_1380":                   1380,
	"clientscopescode_1381":            1381,
	"code_1382":                        1382,
	"code_1383":                        1383,
	"code_1384":                        1384,
	"code_1385":                        1385,
	"code_1386":                        1386,
	"homebankcode_1387":                1387,
	"hmcode_1388":                      1388,
	"requestclientupdatecode_1389":     1389,
	"clientidcode_1390":                1390,
	"getclientclientcode_1391":         1391,
	"getclientscopecode_1392":          1392,
	"updatescopecode_1393":             1393,
	"scopescode_1394":                  1394,
	"clientcode_1395":                  1395,
	"scopecode_1396":                   1396,
	"clientscopescode_1397":            1397,
	"readererrorcode_1398":             1398,
	"terminaldbmerchantidcode_1400":    1400,
	"clientclientiddbtildcode_1403":    1403,
	"clientclientiddbcode_1405":        1405,
	"clientiptildacode_1406":           1406,
	"bindmultipartformdatcode_1407":    1407,
	"tildatildacode_1408":              1408,
	"tildacode_1409":                   1409,
	"float64amounttildacode_1410":      1410,
	"authorizedrequestjsocode_1411":    1411,
	"sourcecode_1412":                  1412,
	"callbackcorecallbackcode_1413":    1413,
	"postlinktildacode_1414":           1414,
	"code_1415":                        1415,
	"code_1416":                        1416,
	"authorizedrequestjsocode_1418":    1418,
	"corestatementcode_1419":           1419,
	"code_1421":                        1421,
	"code_1422":                        1422,
	"merchantcompanynamecode_1423":     1423,
	"clienttypecode_1424":              1424,
	"clienttypenametildacode_1425":     1425,
	"clienttypetildacliencode_1426":    1426,
	"bindjsonbindjsoncode_1427":        1427,
	"parsingerrorcode_1428":            1428,
	"code_1429":                        1429,
	"code_1430":                        1430,
	"code_1431":                        1431,
	"likecode_1432":                    1432,
	"betweencode_1433":                 1433,
	"code_1434":                        1434,
	"code_1436":                        1436,
	"code_1437":                        1437,
	"code_1438":                        1438,
	"countcode_1439":                   1439,
	"code_1440":                        1440,
	"code_1441":                        1441,
	"referenceepaycode_1442":           1442,
	"code_1443":                        1443,
	"code_1444":                        1444,
	"code_1445":                        1445,
	"hmhmbcode_1446":                   1446,
	"jsonjsoncode_1447":                1447,
	"urlbodycode_1448":                 1448,
	"code_1449":                        1449,
	"getusescopebyclientcode_1451":     1451,
	"code_1452":                        1452,
	"jsonunmarshalcode_1453":           1453,
	"code_1454":                        1454,
	"code_1455":                        1455,
	"websocketcode_1457":               1457,
	"websocketcode_1458":               1458,
	"websocketcode_1459":               1459,
	"websocketcode_1460":               1460,
	"websocketcode_1461":               1461,
	"websocketcode_1462":               1462,
	"code_1463":                        1463,
	"code_1464":                        1464,
	"code_1465":                        1465,
	"code_1466":                        1466,
	"code_1467":                        1467,
	"updatemerchantcode_1468":          1468,
	"updatemerchantshopcode_1469":      1469,
	"updatemerchanttermincode_1470":    1470,
	"code_1471":                        1471,
	"code_1472":                        1472,
	"emailcode_1473":                   1473,
	"nameservicecode_1475":             1475,
	"idparamscode_1476":                1476,
	"merchantpublicidcode_1477":        1477,
	"terminalmerchantidcode_1478":      1478,
	"shopidshopwebservicecode_1479":    1479,
	"code_1480":                        1480,
	"code_1481":                        1481,
	"idparamscode_1482":                1482,
	"jsonjsoncode_1483":                1483,
	"jsonjsonmarshaljsoncode_1484":     1484,
	"idparamscode_1486":                1486,
	"code_1487":                        1487,
	"code_1488":                        1488,
	"code_1489":                        1489,
	"code_1490":                        1490,
	"paymentrequestcode_1491":          1491,
	"code_1492":                        1492,
	"jsonunmarshalcode_1493":           1493,
	"jsonmarshalcode_1494":             1494,
	"code_1495":                        1495,
	"jsonjsoncode_1496":                1496,
	"code_1497":                        1497,
	"code_1498":                        1498,
	"authorizedrequestjsocode_1499":    1499,
	"authorizedrequestjsocode_1500":    1500,
	"authorizedrequestjsocode_1502":    1502,
	"authorizedrequestjsocode_1503":    1503,
	"authorizedrequestjsocode_1504":    1504,
	"terminaluuidcode_1505":            1505,
	"bindgrafanacode_1506":             1506,
	"httpgrafanacode_1507":             1507,
	"unauthorizedcode_1508":            1508,
	"badtokencode_1509":                1509,
	"jsonunmarshalcode_1510":           1510,
	"jsonmarshalcode_1511":             1511,
	"requestjsongeoipcode_1512":        1512,
	"code_1513":                        1513,
	"jsonunmarshalcode_1514":           1514,
	"jsonmarshalcode_1515":             1515,
	"code_1516":                        1516,
	"qrtransactioninfocode_1517":       1517,
	"cardtypeidcode_1518":              1518,
	"currencyidcode_1519":              1519,
	"statusidcode_1520":                1520,
	"code_1521":                        1521,
	"code_1522":                        1522,
	"code_1523":                        1523,
	"code_1524":                        1524,
	"shopidmerchantidcliecode_1525":    1525,
	"code_1526":                        1526,
	"qrcode_1527":                      1527,
	"sourcelistidcode_1528":            1528,
	"authorizedrequestjsocode_1529":    1529,
	"xlsidcode_1530":                   1530,
	"jsonjsoncode_1531":                1531,
	"redispartialtransactcode_1532":    1532,
	"code_1533":                        1533,
	"code_1534":                        1534,
	"code_1535":                        1535,
	"code_1536":                        1536,
	"code_1537":                        1537,
	"redisinvoiceidcode_1538":          1538,
	"epay1code_1539":                   1539,
	"epay1code_1540":                   1540,
	"epay1code_1541":                   1541,
	"epay1code_1542":                   1542,
	"transactiontypeidcode_1543":       1543,
	"clienttypeidcode_1544":            1544,
	"jsonmarshalucscode_1545":          1545,
	"code_1546":                        1546,
	"scopecode_1547":                   1547,
	"redisqrstatuscode_1548":           1548,
	"authorizedrequestjsocode_1549":    1549,
	"code_1550":                        1550,
	"redisrealipcode_1551":             1551,
	"requestjsoncoreqrqrscode_1552":    1552,
	"kafkareadmessagecode_1553":        1553,
	"jsonunmarshalkafkamecode_1554":    1554,
	"jsonjsonmarshaljsoncode_1555":     1555,
	"kafkacode_1556":                   1556,
	"setoffsetkafkacode_1557":          1557,
	"code_1558":                        1558,
	"code_1559":                        1559,
	"code_1560":                        1560,
	"code_1561":                        1561,
	"httpcode_1562":                    1562,
	"httpcode_1563":                    1563,
	"httpcode_1564":                    1564,
	"httpcode_1565":                    1565,
	"authcode_1566":                    1566,
	"egatewaycode_1567":                1567,
	"egatewaycode_1568":                1568,
	"code_1569":                        1569,
	"jsoncode_1570":                    1570,
	"code_1571":                        1571,
	"code_1572":                        1572,
	"code_1578":                        1578,
	"code_1579":                        1579,
	"readallfromrequestbocode_1604":    1604,
	"parsefloatrefundcode_1605":        1605,
	"amountbodyurlrefundcode_1606":     1606,
	"code_1607":                        1607,
	"chargecode_1608":                  1608,
	"cancelcode_1609":                  1609,
	"refundcode_1610":                  1610,
	"httpcode_1611":                    1611,
	"code_1612":                        1612,
	"code_1614":                        1614,
	"terminalidcode_1615":              1615,
	"code_1616":                        1616,
	"jsonunmarshalbincode_1617":        1617,
	"jsonmarshalcode_1618":             1618,
	"code_1619":                        1619,
	"jsonapiosuvoxcode_1620":           1620,
	"apiosuvoxcode_1621":               1621,
	"apiosuvoxhttpcode200code_1622":    1622,
	"code_1623":                        1623,
	"sourcelistcode_1624":              1624,
	"code_1636":                        1636,
	"code_1637":                        1637,
	"osuvoxcode_1638":                  1638,
	"invalidcardidcode_1639":           1639,
	"osuvoxcode_1640":                  1640,
	"code_1641":                        1641,
	"osuvoxcode_1642":                  1642,
	"singlemessageschemecode_1650":     1650,
	"code_1651":                        1651,
	"otpcode_1652":                     1652,
	"otpcode_1653":                     1653,
	"otpcodecode_1654":                 1654,
	"otpcodecode_1655":                 1655,
	"redisterminaluuidinvcode_1656":    1656,
	"invalidterminalidcode_1658":       1658,
	"invalidamoutcode_1659":            1659,
	"code_1660":                        1660,
	"code_1661":                        1661,
	"authorizedrequestjsocode_1663":    1663,
	"bindgettokenbycardcode_1664":      1664,
	"requestpostformxmlgecode_1665":    1665,
	"requestpostformxmlgecode_1666":    1666,
	"requestpostformxmlgecode_1667":    1667,
	"requestpostformxmlgecode_1668":    1668,
	"codebase64stringbasecode_1670":    1670,
	"authorizedrequestjsocode_1671":    1671,
	"cardcode_1672":                    1672,
	"homebankpayosuvoxcarcode_1673":    1673,
	"osuvoxcode_1675":                  1675,
	"osuvoxcode_1677":                  1677,
	"osuvoxcode_1679":                  1679,
	"osuvoxcode_1680":                  1680,
	"osuvoxcode_1681":                  1681,
	"code_1682":                        1682,
	"osuvoxcode_1683":                  1683,
	"osuvoxcode_1685":                  1685,
	"transactioncode_1690":             1690,
	"couldnotgetphonenumbcode_1692":    1692,
	"code_1693":                        1693,
	"code_1694":                        1694,
	"osuvoxcode_1695":                  1695,
	"xmlbodycode_1699":                 1699,
	"xmlmarhsalswitchpaymcode_1700":    1700,
	"terminalidcode_1703":              1703,
	"terminalxlsmidtermincode_1704":    1704,
	"ibancode_1705":                    1705,
	"ibancode_1706":                    1706,
	"terminalidcode_1707":              1707,
	"ibancode_1708":                    1708,
	"code_1709":                        1709,
	"code_1710":                        1710,
	"code_1711":                        1711,
	"code_1712":                        1712,
	"invoiceidcode_1713":               1713,
	"code_1714":                        1714,
	"authcode_1715":                    1715,
	"paymenttypecode_1716":             1716,
	"osuvoxcode_1717":                  1717,
	"paymenttypecode_1719":             1719,
	"paymentsystemcode_1720":           1720,
	"code_1723":                        1723,
	"code_1724":                        1724,
	"code_1759":                        1759,
	"code_1760":                        1760,
	"code_1761":                        1761,
	"code_1762":                        1762,
	"shopidcode_1763":                  1763,
	"code_1764":                        1764,
	"orderidcode_1765":                 1765,
	"coremerchantcode_1771":            1771,
	"postlinkcorecallbackcode_1780":    1780,
	"cardtypenilcode_1781":             1781,
	"switchpaymentcode_1786":           1786,
	"code_1787":                        1787,
	"httpgetmerchantcerticode_1788":    1788,
	"httpgetmerchantcreatcode_1789":    1789,
	"enabledcode_1849":                 1849,
	"shopcode_1850":                    1850,
	"code_1855":                        1855,
	"terminalidcode_1856":              1856,
	"shopidcode_1857":                  1857,
	"idcode_1858":                      1858,
	"shopidcode_1859":                  1859,
	"tokenexpireinsecondscode_1860":    1860,
	"apiepay1code_1899":                1899,
	"requestcode_1900":                 1900,
	"code_1901":                        1901,
	"code_1902":                        1902,
	"jsoncode_1903":                    1903,
	"idservicecode_1944":               1944,
	"updatemerchantcontaccode_1945":    1945,
	"contacttypenameservicode_1946":    1946,
	"code_1954":                        1954,
	"code_1955":                        1955,
	"code_1956":                        1956,
	"code_1961":                        1961,
	"code_1962":                        1962,
	"code_1964":                        1964,
	"terminalshopnamecode_1965":        1965,
	"code_1970":                        1970,
	"invoiceidcode_1971":               1971,
	"amount0code_1975":                 1975,
	"code_1976":                        1976,
	"code_1977":                        1977,
	"tokenexpiredtryagaincode_1979":    1979,
	"mpancode_1980":                    1980,
	"code_2008":                        2008,
	"recordnotfoundcode_2009":          2009,
	"code_2015":                        2015,
	"code_2016":                        2016,
	"redismaxreqperseccode_2017":       2017,
	"code_2018":                        2018,
	"googlepaycode_2028":               2028,
	"googlepaycode_2030":               2030,
	"accountidcode_2031":               2031,
	"callbackcode_2032":                2032,
	"code_2033":                        2033,
	"code_2046":                        2046,
	"jsoncode_2048":                    2048,
	"code_2049":                        2049,
	"code_2050":                        2050,
	"code_2051":                        2051,
	"xlsurlcode_2052":                  2052,
	"code_2053":                        2053,
	"jsonupdateprofilestacode_2057":    2057,
	"jsoncreatestaffproficode_2058":    2058,
	"code_2059":                        2059,
	"code_2060":                        2060,
	"invalidamountamountmcode_2061":    2061,
	"corebusinessreportcode_2067":      2067,
	"code_2069":                        2069,
	"emailcode_2070":                   2070,
	"ginbindcode_2074":                 2074,
	"xlstidxlsmidcode_2075":            2075,
	"code_2076":                        2076,
	"mpgscode_2085":                    2085,
	"mpgscode_2086":                    2086,
	"mpgscode_2087":                    2087,
	"mpgscode_2088":                    2088,
	"mpgscode_2089":                    2089,
	"mpgscode_2090":                    2090,
	"panbincode_2091":                  2091,
	"binorpanrestrictedcode_2092":      2092,
	"bindcode_2122":                    2122,
	"code_2123":                        2123,
	"code_2124":                        2124,
	"merchantcode_2125":                2125,
	"merchantcode_2126":                2126,
	"code_2127":                        2127,
	"code_2128":                        2128,
	"code_2129":                        2129,
	"openwayidcode_2130":               2130,
	"usdcode_2131":                     2131,
	"code_2132":                        2132,
	"code_2133":                        2133,
	"code_2134":                        2134,
	"code_2135":                        2135,
	"xlscode_2136":                     2136,
	"code_2137":                        2137,
	"postlinkcorecallbackcode_2138":    2138,
	"code_2139":                        2139,
	"cryptogramopenwayidcode_2146":     2146,
	"ipcode_2147":                      2147,
	"code_2148":                        2148,
	"formdatasftpproxycode_2152":       2152,
	"httpnewrequestsftpprcode_2153":    2153,
	"redispipelinecode_2154":           2154,
	"transactionamountshocode_2156":    2156,
	"code_2157":                        2157,
	"urlpagesizecode_2158":             2158,
	"code_2159":                        2159,
	"urlpagesizecode_2191":             2191,
	"pagesizecode_2193":                2193,
	"pagesizecode_2194":                2194,
	"code_2200":                        2200,
	"code_2203":                        2203,
	"code_2204":                        2204,
	"code_2205":                        2205,
	"unmarshalcode_2206":               2206,
	"code_2207":                        2207,
	"code_2210":                        2210,
	"publicidonboardingcode_2211":      2211,
	"aclservicecode_2212":              2212,
	"staffcode_2213":                   2213,
	"openwaycardidcode_2214":           2214,
	"code_2218":                        2218,
	"code_2237":                        2237,
	"postlinkacode_2238":               2238,
	"postlinkacode_2239":               2239,
	"postlinkacode_2240":               2240,
	"postlinkacode_2241":               2241,
	"postlinkcode_2242":                2242,
	"code_2243":                        2243,
	"code_2244":                        2244,
	"code_2245":                        2245,
	"code_2249":                        2249,
	"p2pcode_2250":                     2250,
	"code_2257":                        2257,
	"jsonjsonmarshaljsoncode_2268":     2268,
	"code_2271":                        2271,
	"code_2301":                        2301,
	"code_2305":                        2305,
	"qrbyqrcode_2322":                  2322,
	"code_2323":                        2323,
	"code_2326":                        2326,
	"cardidcode_2339":                  2339,
	"code_2349":                        2349,
	"code_2350":                        2350,
	"code_2351":                        2351,
	"code_2352":                        2352,
	"code_2353":                        2353,
	"code_2354":                        2354,
	"clientidcode_2355":                2355,
	"code_2356":                        2356,
	"resultcodestatusidcode_2360":      2360,
	"paymentsystemcode_2362":           2362,
	"kafkacode_2365":                   2365,
	"shopinfocode_2366":                2366,
	"code_2367":                        2367,
	"code_2433":                        2433,
	"uzgwcode_2435":                    2435,
	"code_2624":                        2624,
	"checktransactionstatcode_2660":    2660,
	"corepaymentcode_2678":             2678,
	"corepaymentcode_2679":             2679,
	"otpcodeapiuzgatewaycode_2704":     2704,
	"codeapiuzgatewaycode_2705":        2705,
	"cannotapproveatthistcode_2740":    2740,
	"notsufficientfundscode_484":       484,
	"theoperationfailedplcode_454":     454,
	"serverisnotrespondincode_459":     459,
	"donotreattemptrestricode_2872":    2872,
}

func ConvertStringToEnumEpayPayInCode(in string) EnumEpayPayInCode {
	if result, ok := EnumEpayPayInCode_value[in]; ok {
		return EnumEpayPayInCode(result)
	}

	if result, ok := EnumEpayPayInCode_Lower_value[strings.ToLower(in)]; ok {
		return EnumEpayPayInCode(result)
	}

	return EnumEpayPayInCode(math.MinInt32)
}

var SliceEnumEpayPayInCodeConvert *sliceEnumEpayPayInCodeConvert

type sliceEnumEpayPayInCodeConvert struct{}

func (*sliceEnumEpayPayInCodeConvert) Synonym(in []EnumEpayPayInCode) []EnumEpayPayInCode {
	result := make([]EnumEpayPayInCode, len(in))
	for i, v := range in {
		result[i] = v.Synonym()
	}

	return result
}

func (*sliceEnumEpayPayInCodeConvert) Int32(in []EnumEpayPayInCode) []int32 {
	result := make([]int32, len(in))
	for i, v := range in {
		result[i] = v.Int32()
	}

	return result
}

func (*sliceEnumEpayPayInCodeConvert) Uint32(in []EnumEpayPayInCode) []uint32 {
	result := make([]uint32, len(in))
	for i, v := range in {
		result[i] = v.Uint32()
	}

	return result
}

func (*sliceEnumEpayPayInCodeConvert) Uint64(in []EnumEpayPayInCode) []uint64 {
	result := make([]uint64, len(in))
	for i, v := range in {
		result[i] = v.Uint64()
	}

	return result
}

func (*sliceEnumEpayPayInCodeConvert) Int64(in []EnumEpayPayInCode) []int64 {
	result := make([]int64, len(in))
	for i, v := range in {
		result[i] = v.Int64()
	}

	return result
}

func (*sliceEnumEpayPayInCodeConvert) Uint(in []EnumEpayPayInCode) []uint {
	result := make([]uint, len(in))
	for i, v := range in {
		result[i] = v.Uint()
	}

	return result
}

func (*sliceEnumEpayPayInCodeConvert) Int(in []EnumEpayPayInCode) []int {
	result := make([]int, len(in))
	for i, v := range in {
		result[i] = v.Int()
	}

	return result
}

func (*sliceEnumEpayPayInCodeConvert) IsKnown(in []EnumEpayPayInCode) []bool {
	result := make([]bool, len(in))
	for i, v := range in {
		result[i] = v.IsKnown()
	}

	return result
}

func (*sliceEnumEpayPayInCodeConvert) ConvertIntToEnumEpayPayInCode(in []int) []EnumEpayPayInCode {
	result := make([]EnumEpayPayInCode, len(in))
	for i, v := range in {
		result[i] = ConvertIntToEnumEpayPayInCode(v)
	}

	return result
}

func (*sliceEnumEpayPayInCodeConvert) ConvertUintToEnumEpayPayInCode(in []uint) []EnumEpayPayInCode {
	result := make([]EnumEpayPayInCode, len(in))
	for i, v := range in {
		result[i] = ConvertUintToEnumEpayPayInCode(v)
	}

	return result
}

func (*sliceEnumEpayPayInCodeConvert) ConvertInt32ToEnumEpayPayInCode(in []int32) []EnumEpayPayInCode {
	result := make([]EnumEpayPayInCode, len(in))
	for i, v := range in {
		result[i] = ConvertInt32ToEnumEpayPayInCode(v)
	}

	return result
}

func (*sliceEnumEpayPayInCodeConvert) ConvertUint32ToEnumEpayPayInCode(in []uint32) []EnumEpayPayInCode {
	result := make([]EnumEpayPayInCode, len(in))
	for i, v := range in {
		result[i] = ConvertUint32ToEnumEpayPayInCode(v)
	}

	return result
}

func (*sliceEnumEpayPayInCodeConvert) ConvertInt64ToEnumEpayPayInCode(in []int64) []EnumEpayPayInCode {
	result := make([]EnumEpayPayInCode, len(in))
	for i, v := range in {
		result[i] = ConvertInt64ToEnumEpayPayInCode(v)
	}

	return result
}

func (*sliceEnumEpayPayInCodeConvert) ConvertUint64ToEnumEpayPayInCode(in []uint64) []EnumEpayPayInCode {
	result := make([]EnumEpayPayInCode, len(in))
	for i, v := range in {
		result[i] = ConvertUint64ToEnumEpayPayInCode(v)
	}

	return result
}

func (*sliceEnumEpayPayInCodeConvert) ConvertStringToEnumEpayPayInCode(in []string) []EnumEpayPayInCode {
	result := make([]EnumEpayPayInCode, len(in))
	for i, v := range in {
		result[i] = ConvertStringToEnumEpayPayInCode(v)
	}

	return result
}

func NewEnumEpayPayInCodeUsage() *EnumEpayPayInCodeUsage {
	return &EnumEpayPayInCodeUsage{
		enumMap: map[EnumEpayPayInCode]bool{
			EnumEpayPayInCode_Empty_Response_Code:              false,
			EnumEpayPayInCode_ClientauthenticationCode_33:      false,
			EnumEpayPayInCode_SystemerrorpleasetryCode_50:      false,
			EnumEpayPayInCode_ErrorinCVC2orCVC2DesCode_18:      false,
			EnumEpayPayInCode_InvalidRetrievalrefeCode_15:      false,
			EnumEpayPayInCode_ThreeDSecurecheckfailedCode_455:  false,
			EnumEpayPayInCode_AccessdeniedCode_456:             false,
			EnumEpayPayInCode_ErrorincardexpiratioCode_457:     false,
			EnumEpayPayInCode_ServerisnotrespondinCode_458:     false,
			EnumEpayPayInCode_NoorinvalidresponcerCode_460:     false,
			EnumEpayPayInCode_BadCGIrequestCode_461:            false,
			EnumEpayPayInCode_CallyourbankCode_462:             false,
			EnumEpayPayInCode_CallyourbankCode_463:             false,
			EnumEpayPayInCode_InvalidmerchantCode_464:          false,
			EnumEpayPayInCode_YourcardisrestrictedCode_465:     false,
			EnumEpayPayInCode_NotpermittedtoclientCode_466:     false,
			EnumEpayPayInCode_YourcardisdisabledCode_467:       false,
			EnumEpayPayInCode_AdditionalidentificaCode_468:     false,
			EnumEpayPayInCode_InvalidtransactionCode_469:       false,
			EnumEpayPayInCode_InvalidamountCode_470:            false,
			EnumEpayPayInCode_NosuchcardCode_471:               false,
			EnumEpayPayInCode_NosuchcardCode_472:               false,
			EnumEpayPayInCode_AuthenticationfailedCode_473:     false,
			EnumEpayPayInCode_InvalidresponseCode_475:          false,
			EnumEpayPayInCode_NoactiontakenCode_476:            false,
			EnumEpayPayInCode_FormaterrorCode_477:              false,
			EnumEpayPayInCode_ExpiredcardCode_478:              false,
			EnumEpayPayInCode_RestrictedcardCode_479:           false,
			EnumEpayPayInCode_CallyourbankCode_480:             false,
			EnumEpayPayInCode_LostcardCode_481:                 false,
			EnumEpayPayInCode_LostcardCode_482:                 false,
			EnumEpayPayInCode_StolencardCode_483:               false,
			EnumEpayPayInCode_ExpiredcardCode_485:              false,
			EnumEpayPayInCode_NotpermittedtoclientCode_486:     false,
			EnumEpayPayInCode_NotpermittedtomerchaCode_487:     false,
			EnumEpayPayInCode_ExceedsamountlimitCode_488:       false,
			EnumEpayPayInCode_RestrictedcardCode_489:           false,
			EnumEpayPayInCode_InvalidContractcardCode_490:      false,
			EnumEpayPayInCode_ExceedsfrequencylimiCode_491:     false,
			EnumEpayPayInCode_PINtriesexceededCode_492:         false,
			EnumEpayPayInCode_TimeoutatissuersysteCode_493:     false,
			EnumEpayPayInCode_IssuerunavailableCode_494:        false,
			EnumEpayPayInCode_CannotbecompletedvioCode_495:     false,
			EnumEpayPayInCode_ThreeDSecureSecureCodeabCode_496: false,
			EnumEpayPayInCode_ServerisnotrespondinCode_497:     false,
			EnumEpayPayInCode_ThreeDSecurecheckfailedCode_499:  false,
			EnumEpayPayInCode_ThreeDSecurecheckfailedCode_500:  false,
			EnumEpayPayInCode_CardcheckfailedCode_501:          false,
			EnumEpayPayInCode_ThreeDSecureSecureCodeabCode_502: false,
			EnumEpayPayInCode_ThreeDSecureSecureCodeabCode_503: false,
			EnumEpayPayInCode_TransactiondeclinedCCode_521:     false,
			EnumEpayPayInCode_RecordNotFoundchecktCode_522:     false,
			EnumEpayPayInCode_TransactiondeclinedCCode_523:     false,
			EnumEpayPayInCode_YourcardisrestrictedCode_524:     false,
			EnumEpayPayInCode_YourcardisrestrictedCode_525:     false,
			EnumEpayPayInCode_SystemerrorPleasetryCode_526:     false,
			EnumEpayPayInCode_TransactiondeclinedCCode_527:     false,
			EnumEpayPayInCode_ThedailylimitofincomCode_528:     false,
			EnumEpayPayInCode_TransactiondeclinedCCode_531:     false,
			EnumEpayPayInCode_UnabletoverifyPINcalCode_532:     false,
			EnumEpayPayInCode_AuthenticationfailedCode_19:      false,
			EnumEpayPayInCode_ErrorincardexpiratioCode_9:       false,
			EnumEpayPayInCode_ServerisnotrespondinCode_8:       false,
			EnumEpayPayInCode_ServerisnotrespondinCode_4:       false,
			EnumEpayPayInCode_ErrorBindActionCreatCode_1:       false,
			EnumEpayPayInCode_ErrorcreatingrecordiCode_2:       false,
			EnumEpayPayInCode_ErrorBindActionUpdatCode_3:       false,
			EnumEpayPayInCode_ErrorwhileupdatingreCode_4:       false,
			EnumEpayPayInCode_ErrorwhiledeletingthCode_5:       false,
			EnumEpayPayInCode_ErrorGetByNameActionCode_6:       false,
			EnumEpayPayInCode_ErrorGetByIDActionGeCode_7:       false,
			EnumEpayPayInCode_ErrorBindActionCreatCode_8:       false,
			EnumEpayPayInCode_ErrorcreatingrecordiCode_9:       false,
			EnumEpayPayInCode_RedisActionCreateErrCode_10:      false,
			EnumEpayPayInCode_ErrorBindActionUpdatCode_11:      false,
			EnumEpayPayInCode_ErrorwhileupdatingreCode_12:      false,
			EnumEpayPayInCode_RedisActionUpdateErrCode_13:      false,
			EnumEpayPayInCode_ErrorwhiledeletingthCode_14:      false,
			EnumEpayPayInCode_RedisActionDeleteErrCode_15:      false,
			EnumEpayPayInCode_ErrorGetByCodeinPostCode_16:      false,
			EnumEpayPayInCode_CodeActionGetErrorByCode_17:      false,
			EnumEpayPayInCode_IDApplicationIDAppliCode_18:      false,
			EnumEpayPayInCode_CodeCode_19:                      false,
			EnumEpayPayInCode_IDApplicationIDAppliCode_20:      false,
			EnumEpayPayInCode_IDApplicationApplicaCode_21:      false,
			EnumEpayPayInCode_Code_22:                          false,
			EnumEpayPayInCode_SuccessfullyCode_23:              false,
			EnumEpayPayInCode_Test111Code_24:                   false,
			EnumEpayPayInCode_InvoiceidCode_25:                 false,
			EnumEpayPayInCode_BindJSONBindJSONCode_27:          false,
			EnumEpayPayInCode_TransactionalreadyprCode_28:      false,
			EnumEpayPayInCode_TransactionalreadyprCode_29:      false,
			EnumEpayPayInCode_ErrorGetShopsByMerchCode_54:      false,
			EnumEpayPayInCode_IdCode_55:                        false,
			EnumEpayPayInCode_ErrorGetShopByIDCode_56:          false,
			EnumEpayPayInCode_ErrorBindCode_57:                 false,
			EnumEpayPayInCode_ErrorBindCode_58:                 false,
			EnumEpayPayInCode_ErrorCreateShopCode_59:           false,
			EnumEpayPayInCode_ErrorGetMerchantByIDCode_60:      false,
			EnumEpayPayInCode_ErrorGetStatusByNameCode_61:      false,
			EnumEpayPayInCode_ErrorCreateDefaultCoCode_64:      false,
			EnumEpayPayInCode_ErrorCreateMerchantCode_65:       false,
			EnumEpayPayInCode_ErrorVerificationSerCode_71:      false,
			EnumEpayPayInCode_ErrorCheckMerchantCode_72:        false,
			EnumEpayPayInCode_ErrorSendVerificatioCode_73:      false,
			EnumEpayPayInCode_ErrorVerificationSerCode_74:      false,
			EnumEpayPayInCode_ErrorFindMerchantByECode_75:      false,
			EnumEpayPayInCode_ErrorUpdateMerchantCode_76:       false,
			EnumEpayPayInCode_ErrorRegistrationUseCode_77:      false,
			EnumEpayPayInCode_RrnCode_83:                       false,
			EnumEpayPayInCode_ErrorGetCardTypebyRRCode_84:      false,
			EnumEpayPayInCode_ErrorCheckTransactioCode_85:      false,
			EnumEpayPayInCode_UnauthorizedCode_86:              false,
			EnumEpayPayInCode_TokenisnotvalidCode_87:           false,
			EnumEpayPayInCode_ErrorCheckTokenCode_88:           false,
			EnumEpayPayInCode_ErrorNoScopesCode_89:             false,
			EnumEpayPayInCode_ErrorNoScopesCode_90:             false,
			EnumEpayPayInCode_ErrorCheckTokenCode_91:           false,
			EnumEpayPayInCode_Cardnumber14Code_92:              false,
			EnumEpayPayInCode_ErrorGetCardCorporatCode_94:      false,
			EnumEpayPayInCode_ErrorGetCountryByBINCode_95:      false,
			EnumEpayPayInCode_InvaliddataCode_96:               false,
			EnumEpayPayInCode_ErrorCheckTokenCode_97:           false,
			EnumEpayPayInCode_ErrorNoScopesCode_98:             false,
			EnumEpayPayInCode_ContractCode_99:                  false,
			EnumEpayPayInCode_InvalidDateformatPleCode_111:     false,
			EnumEpayPayInCode_ContractCode_112:                 false,
			EnumEpayPayInCode_ExtcsvxlsxCode_113:               false,
			EnumEpayPayInCode_ErrorGetStatementByDCode_114:     false,
			EnumEpayPayInCode_ErrorGetStatementByOCode_115:     false,
			EnumEpayPayInCode_ErrorGetStatementAirCode_116:     false,
			EnumEpayPayInCode_ErrorGetStatementByTCode_117:     false,
			EnumEpayPayInCode_ErrorCheckTokenCode_121:          false,
			EnumEpayPayInCode_Code_122:                         false,
			EnumEpayPayInCode_ErrorBindCode_123:                false,
			EnumEpayPayInCode_Code_124:                         false,
			EnumEpayPayInCode_Code_125:                         false,
			EnumEpayPayInCode_Code_126:                         false,
			EnumEpayPayInCode_Code_127:                         false,
			EnumEpayPayInCode_ErrorEmptyMerchantIDCode_128:     false,
			EnumEpayPayInCode_ErrorUnknownMerchantCode_129:     false,
			EnumEpayPayInCode_ServerErrorCode_130:              false,
			EnumEpayPayInCode_ErrorCheckTokenCode_131:          false,
			EnumEpayPayInCode_ErrorInvalidScopeCode_132:        false,
			EnumEpayPayInCode_ErrorBindCode_133:                false,
			EnumEpayPayInCode_ErrorRequestPostFormCode_134:     false,
			EnumEpayPayInCode_ErrorBodyIsEmptyCode_135:         false,
			EnumEpayPayInCode_ErrorRequestIsNotSucCode_136:     false,
			EnumEpayPayInCode_UnkwonErrorCode_137:              false,
			EnumEpayPayInCode_SecretincorrecterrorCode_138:     false,
			EnumEpayPayInCode_Code_139:                         false,
			EnumEpayPayInCode_SecretincorrectCode_140:          false,
			EnumEpayPayInCode_ErrorgetclientfromreCode_141:     false,
			EnumEpayPayInCode_ErrorparseclientCode_142:         false,
			EnumEpayPayInCode_CheckscopesCode_143:              false,
			EnumEpayPayInCode_UseridpasswordCode_144:           false,
			EnumEpayPayInCode_UserstatusCHANGEPASSCode_145:     false,
			EnumEpayPayInCode_UsernotfoundCode_146:             false,
			EnumEpayPayInCode_StatusCode_147:                   false,
			EnumEpayPayInCode_Code_148:                         false,
			EnumEpayPayInCode_UserstatusinvalidCode_149:        false,
			EnumEpayPayInCode_StatusisnotdefinedCode_150:       false,
			EnumEpayPayInCode_PASSWORDINCORRECTCode_151:        false,
			EnumEpayPayInCode_GranttypeCode_152:                false,
			EnumEpayPayInCode_RefreshTokenCode_153:             false,
			EnumEpayPayInCode_Code_154:                         false,
			EnumEpayPayInCode_ScopeCode_155:                    false,
			EnumEpayPayInCode_RefreshtokenCode_156:             false,
			EnumEpayPayInCode_GENERATEACCESSERRORCode_157:      false,
			EnumEpayPayInCode_GENERATEACCESSERRORCode_158:      false,
			EnumEpayPayInCode_EXTENSIONFIELDADDERRCode_159:     false,
			EnumEpayPayInCode_EXTENSIONFIELDADDERRCode_160:     false,
			EnumEpayPayInCode_Code_161:                         false,
			EnumEpayPayInCode_Code_162:                         false,
			EnumEpayPayInCode_GetUserByLoginerrorcCode_163:     false,
			EnumEpayPayInCode_Code_164:                         false,
			EnumEpayPayInCode_GetStatusByNameerrorCode_165:     false,
			EnumEpayPayInCode_HashPassworderrorCode_166:        false,
			EnumEpayPayInCode_SaveUsererrorCode_167:            false,
			EnumEpayPayInCode_UpdateUsererrorCode_169:          false,
			EnumEpayPayInCode_UpdateUsererrorCode_170:          false,
			EnumEpayPayInCode_GetUserByLoginerrorcCode_171:     false,
			EnumEpayPayInCode_Code_172:                         false,
			EnumEpayPayInCode_Code_173:                         false,
			EnumEpayPayInCode_ActionServiceGetDebiCode_178:     false,
			EnumEpayPayInCode_ActionServiceGetTranCode_179:     false,
			EnumEpayPayInCode_ReferenceEPAYCode_180:            false,
			EnumEpayPayInCode_RedisCode_181:                    false,
			EnumEpayPayInCode_Code_182:                         false,
			EnumEpayPayInCode_Code_185:                         false,
			EnumEpayPayInCode_ErrorUnmarshalCode_186:           false,
			EnumEpayPayInCode_StartDateandEndDatesCode_193:     false,
			EnumEpayPayInCode_EPayCode_194:                     false,
			EnumEpayPayInCode_NoAuthorizeCode_197:              false,
			EnumEpayPayInCode_BadtokenCode_198:                 false,
			EnumEpayPayInCode_ParseerrorCode_199:               false,
			EnumEpayPayInCode_PaymenterrorCode_200:             false,
			EnumEpayPayInCode_Code_216:                         false,
			EnumEpayPayInCode_ErrorBindJSONCannotuCode_224:     false,
			EnumEpayPayInCode_LIKECode_225:                     false,
			EnumEpayPayInCode_Code_226:                         false,
			EnumEpayPayInCode_BETWEENCode_227:                  false,
			EnumEpayPayInCode_Code_228:                         false,
			EnumEpayPayInCode_Code_229:                         false,
			EnumEpayPayInCode_Code_230:                         false,
			EnumEpayPayInCode_Code_231:                         false,
			EnumEpayPayInCode_Code_232:                         false,
			EnumEpayPayInCode_ErrorCheckTokenCode_233:          false,
			EnumEpayPayInCode_ErrorNoScopesCode_234:            false,
			EnumEpayPayInCode_FromdateYYYYMMDDCode_235:         false,
			EnumEpayPayInCode_TodateYYYYMMDDCode_236:           false,
			EnumEpayPayInCode_ErrorCheckTokenCode_237:          false,
			EnumEpayPayInCode_ErrorNoScopesCode_238:            false,
			EnumEpayPayInCode_CannotunmarshalintosCode_239:     false,
			EnumEpayPayInCode_Code_240:                         false,
			EnumEpayPayInCode_Code_241:                         false,
			EnumEpayPayInCode_Code_242:                         false,
			EnumEpayPayInCode_IDCode_243:                       false,
			EnumEpayPayInCode_MerchantIDCode_244:               false,
			EnumEpayPayInCode_StartDateandEndDatesCode_245:     false,
			EnumEpayPayInCode_Code_246:                         false,
			EnumEpayPayInCode_NotemailCode_247:                 false,
			EnumEpayPayInCode_JsonCode_248:                     false,
			EnumEpayPayInCode_Code_249:                         false,
			EnumEpayPayInCode_MsgValueCode_250:                 false,
			EnumEpayPayInCode_Code_251:                         false,
			EnumEpayPayInCode_Code_252:                         false,
			EnumEpayPayInCode_Code_263:                         false,
			EnumEpayPayInCode_Code_264:                         false,
			EnumEpayPayInCode_Code_265:                         false,
			EnumEpayPayInCode_SecretincorrecterrorCode_266:     false,
			EnumEpayPayInCode_ScopenotinchangepassCode_267:     false,
			EnumEpayPayInCode_UnsupportedscopeCode_268:         false,
			EnumEpayPayInCode_BLOCKCode_269:                    false,
			EnumEpayPayInCode_BLOCKCode_270:                    false,
			EnumEpayPayInCode_UsernotsaveCode_271:              false,
			EnumEpayPayInCode_UsernotsaveCode_272:              false,
			EnumEpayPayInCode_FailedtogetstatusCode_273:        false,
			EnumEpayPayInCode_UseralreadyexistCode_274:         false,
			EnumEpayPayInCode_UseralreadyexistCode_275:         false,
			EnumEpayPayInCode_IncorrectdataCode_276:            false,
			EnumEpayPayInCode_IncorrectdataCode_277:            false,
			EnumEpayPayInCode_NotemailCode_278:                 false,
			EnumEpayPayInCode_NotemailCode_279:                 false,
			EnumEpayPayInCode_Code_280:                         false,
			EnumEpayPayInCode_TransactioncreateerrCode_281:     false,
			EnumEpayPayInCode_Code_282:                         false,
			EnumEpayPayInCode_Code_283:                         false,
			EnumEpayPayInCode_TestMECode_285:                   false,
			EnumEpayPayInCode_StatementCode_286:                false,
			EnumEpayPayInCode_ErrorCheckTokenCode_287:          false,
			EnumEpayPayInCode_ErrorNoScopesCode_288:            false,
			EnumEpayPayInCode_TofromYYYYMMDDCode_289:           false,
			EnumEpayPayInCode_Code_290:                         false,
			EnumEpayPayInCode_FromDateandToDateshoCode_291:     false,
			EnumEpayPayInCode_ERRORwhilecreatingreCode_292:     false,
			EnumEpayPayInCode_Code_293:                         false,
			EnumEpayPayInCode_RequiredclientcredenCode_294:     false,
			EnumEpayPayInCode_IDIDCode_295:                     false,
			EnumEpayPayInCode_ErrorUpdateStaffCode_296:         false,
			EnumEpayPayInCode_StaffCode_297:                    false,
			EnumEpayPayInCode_Code_298:                         false,
			EnumEpayPayInCode_StaffCode_299:                    false,
			EnumEpayPayInCode_BindErrorCode_300:                false,
			EnumEpayPayInCode_UpdateStaffCode_301:              false,
			EnumEpayPayInCode_MultipartFormCode_302:            false,
			EnumEpayPayInCode_ExpireAtYYYYMMDDCode_303:         false,
			EnumEpayPayInCode_ExpireAtYYYYMMDDCode_304:         false,
			EnumEpayPayInCode_ExpireAtCode_305:                 false,
			EnumEpayPayInCode_ExpireAtCode_306:                 false,
			EnumEpayPayInCode_Code_307:                         false,
			EnumEpayPayInCode_Code_308:                         false,
			EnumEpayPayInCode_Code_309:                         false,
			EnumEpayPayInCode_CommitErrorCode_310:              false,
			EnumEpayPayInCode_BindJSONErrorCode_311:            false,
			EnumEpayPayInCode_Code_312:                         false,
			EnumEpayPayInCode_SearchtransactionerrCode_318:     false,
			EnumEpayPayInCode_JsonCode_319:                     false,
			EnumEpayPayInCode_Code_320:                         false,
			EnumEpayPayInCode_Code_321:                         false,
			EnumEpayPayInCode_Code_322:                         false,
			EnumEpayPayInCode_FromdateYYYYMMDDCode_323:         false,
			EnumEpayPayInCode_TodateYYYYMMDDCode_324:           false,
			EnumEpayPayInCode_EPayCode_325:                     false,
			EnumEpayPayInCode_ErrorgetMerchantinfoCode_326:     false,
			EnumEpayPayInCode_Code_327:                         false,
			EnumEpayPayInCode_Code_328:                         false,
			EnumEpayPayInCode_InvoiceIDCode_329:                false,
			EnumEpayPayInCode_TerminalCode_330:                 false,
			EnumEpayPayInCode_CurrencyCode_331:                 false,
			EnumEpayPayInCode_AmountCode_332:                   false,
			EnumEpayPayInCode_Code_333:                         false,
			EnumEpayPayInCode_ShopIDCode_334:                   false,
			EnumEpayPayInCode_MerchantIDCode_335:               false,
			EnumEpayPayInCode_ShopIDCode_336:                   false,
			EnumEpayPayInCode_ErrorgetMerchantinfoCode_337:     false,
			EnumEpayPayInCode_Code_338:                         false,
			EnumEpayPayInCode_Code_339:                         false,
			EnumEpayPayInCode_Code_340:                         false,
			EnumEpayPayInCode_ContractCode_342:                 false,
			EnumEpayPayInCode_GetShopByContractCode_343:        false,
			EnumEpayPayInCode_Code_344:                         false,
			EnumEpayPayInCode_MarshalerrorCode_345:             false,
			EnumEpayPayInCode_Code_346:                         false,
			EnumEpayPayInCode_Code_347:                         false,
			EnumEpayPayInCode_Code_348:                         false,
			EnumEpayPayInCode_Code_349:                         false,
			EnumEpayPayInCode_Code_350:                         false,
			EnumEpayPayInCode_Code_351:                         false,
			EnumEpayPayInCode_Code_352:                         false,
			EnumEpayPayInCode_TopCode_353:                      false,
			EnumEpayPayInCode_Code_354:                         false,
			EnumEpayPayInCode_EmailCode_355:                    false,
			EnumEpayPayInCode_Code_356:                         false,
			EnumEpayPayInCode_Code_357:                         false,
			EnumEpayPayInCode_Code_358:                         false,
			EnumEpayPayInCode_Code_359:                         false,
			EnumEpayPayInCode_Code_360:                         false,
			EnumEpayPayInCode_Code_361:                         false,
			EnumEpayPayInCode_Code_362:                         false,
			EnumEpayPayInCode_ParseerrorCode_363:               false,
			EnumEpayPayInCode_ParseerrorCode_364:               false,
			EnumEpayPayInCode_Code_365:                         false,
			EnumEpayPayInCode_ParseerrorCode_366:               false,
			EnumEpayPayInCode_ParseerrorCode_367:               false,
			EnumEpayPayInCode_Code_368:                         false,
			EnumEpayPayInCode_ParseerrorCode_369:               false,
			EnumEpayPayInCode_ParseerrorCode_370:               false,
			EnumEpayPayInCode_Code_371:                         false,
			EnumEpayPayInCode_Code_372:                         false,
			EnumEpayPayInCode_NoemailCode_373:                  false,
			EnumEpayPayInCode_Code_374:                         false,
			EnumEpayPayInCode_Code_375:                         false,
			EnumEpayPayInCode_Code_376:                         false,
			EnumEpayPayInCode_IncorrectformatdateCode_377:      false,
			EnumEpayPayInCode_GetFileFiledoesnotexCode_378:     false,
			EnumEpayPayInCode_Code_379:                         false,
			EnumEpayPayInCode_Code_380:                         false,
			EnumEpayPayInCode_Code_381:                         false,
			EnumEpayPayInCode_Code_382:                         false,
			EnumEpayPayInCode_Code_383:                         false,
			EnumEpayPayInCode_EgatewayCode_384:                 false,
			EnumEpayPayInCode_EgatewayCode_385:                 false,
			EnumEpayPayInCode_EgatewayCode_386:                 false,
			EnumEpayPayInCode_EgatewayCode_387:                 false,
			EnumEpayPayInCode_EgatewayCode_388:                 false,
			EnumEpayPayInCode_UserdeleteCode_389:               false,
			EnumEpayPayInCode_ParseerrorCode_390:               false,
			EnumEpayPayInCode_CallbacksendmessageeCode_391:     false,
			EnumEpayPayInCode_Code_392:                         false,
			EnumEpayPayInCode_Code_393:                         false,
			EnumEpayPayInCode_Code_394:                         false,
			EnumEpayPayInCode_Code_395:                         false,
			EnumEpayPayInCode_Code_396:                         false,
			EnumEpayPayInCode_MVisaCode_397:                    false,
			EnumEpayPayInCode_ThreeDSecureCode_398:             false,
			EnumEpayPayInCode_MVisaverificationCode_399:        false,
			EnumEpayPayInCode_Code_400:                         false,
			EnumEpayPayInCode_Code_401:                         false,
			EnumEpayPayInCode_Code_402:                         false,
			EnumEpayPayInCode_CarddataisnotrequireCode_403:     false,
			EnumEpayPayInCode_Code_404:                         false,
			EnumEpayPayInCode_Code_405:                         false,
			EnumEpayPayInCode_Code_406:                         false,
			EnumEpayPayInCode_Code_407:                         false,
			EnumEpayPayInCode_Code_408:                         false,
			EnumEpayPayInCode_Code_409:                         false,
			EnumEpayPayInCode_MVISACode_410:                    false,
			EnumEpayPayInCode_NoAuthorizeCode_411:              false,
			EnumEpayPayInCode_BadtokenCode_412:                 false,
			EnumEpayPayInCode_Code_413:                         false,
			EnumEpayPayInCode_TimeouttransactionCode_504:       false,
			EnumEpayPayInCode_ExceededattemptsCode_505:         false,
			EnumEpayPayInCode_Code_506:                         false,
			EnumEpayPayInCode_Code_507:                         false,
			EnumEpayPayInCode_Code_508:                         false,
			EnumEpayPayInCode_Code_509:                         false,
			EnumEpayPayInCode_RabbitCode_510:                   false,
			EnumEpayPayInCode_Code_512:                         false,
			EnumEpayPayInCode_PostlinkCode_513:                 false,
			EnumEpayPayInCode_Code_514:                         false,
			EnumEpayPayInCode_Code_515:                         false,
			EnumEpayPayInCode_Code_516:                         false,
			EnumEpayPayInCode_TherewasnoattempttopCode_517:     false,
			EnumEpayPayInCode_IpCode_518:                       false,
			EnumEpayPayInCode_InvoiceIDCode_520:                false,
			EnumEpayPayInCode_TerminalIDCode_511:               false,
			EnumEpayPayInCode_EGWCode_546:                      false,
			EnumEpayPayInCode_EGWHTTPCODE200Code_547:           false,
			EnumEpayPayInCode_Code_559:                         false,
			EnumEpayPayInCode_EGWCode_560:                      false,
			EnumEpayPayInCode_EGWHTTPCODE200Code_561:           false,
			EnumEpayPayInCode_MDCode_529:                       false,
			EnumEpayPayInCode_EGWCode_530:                      false,
			EnumEpayPayInCode_Code_570:                         false,
			EnumEpayPayInCode_EGWCode_571:                      false,
			EnumEpayPayInCode_EGWHTTPCODE200Code_533:           false,
			EnumEpayPayInCode_Code_534:                         false,
			EnumEpayPayInCode_Code_535:                         false,
			EnumEpayPayInCode_MerchantCode_536:                 false,
			EnumEpayPayInCode_MerchantIsActivefalsCode_537:     false,
			EnumEpayPayInCode_Code_538:                         false,
			EnumEpayPayInCode_MerchantCode_539:                 false,
			EnumEpayPayInCode_ScopenotfoundcheckscCode_540:     false,
			EnumEpayPayInCode_ErrorparameternameCode_541:       false,
			EnumEpayPayInCode_UnauthorizedChecktokCode_542:     false,
			EnumEpayPayInCode_ErrorInvalidScopeCode_543:        false,
			EnumEpayPayInCode_ErrorBindCode_544:                false,
			EnumEpayPayInCode_UnkwonErrorCode_548:              false,
			EnumEpayPayInCode_TransactionalreadycaCode_549:     false,
			EnumEpayPayInCode_RequesttoXLSServerfaCode_550:     false,
			EnumEpayPayInCode_RequesttoXLSServerreCode_551:     false,
			EnumEpayPayInCode_InvalidbonusamountCode_552:       false,
			EnumEpayPayInCode_InvalidinputdataCode_553:         false,
			EnumEpayPayInCode_TerminalconfiguratioCode_554:     false,
			EnumEpayPayInCode_UnabletogetGETrequesCode_555:     false,
			EnumEpayPayInCode_RequesttoapixlsfaileCode_556:     false,
			EnumEpayPayInCode_RequesttoapixlsreturCode_557:     false,
			EnumEpayPayInCode_GobonusconvertiontofCode_558:     false,
			EnumEpayPayInCode_Code_563:                         false,
			EnumEpayPayInCode_TerminalIDCode_564:               false,
			EnumEpayPayInCode_RequesttogetcardinfoCode_565:     false,
			EnumEpayPayInCode_RequesttogetcardinfoCode_566:     false,
			EnumEpayPayInCode_CardnotfoundCode_567:             false,
			EnumEpayPayInCode_CardownernotfoundCode_568:        false,
			EnumEpayPayInCode_Code_569:                         false,
			EnumEpayPayInCode_Code_572:                         false,
			EnumEpayPayInCode_RequesttoapixlsfaileCode_573:     false,
			EnumEpayPayInCode_RequesttoapixlsreturCode_574:     false,
			EnumEpayPayInCode_UnauthorizedChecktokCode_576:     false,
			EnumEpayPayInCode_CannotcancelxlstransCode_578:     false,
			EnumEpayPayInCode_CannotsendtransactioCode_579:     false,
			EnumEpayPayInCode_CannotsendcancelxlstCode_580:     false,
			EnumEpayPayInCode_CannotcreaterabbitinCode_581:     false,
			EnumEpayPayInCode_ChangePaymentCode_582:            false,
			EnumEpayPayInCode_Code_583:                         false,
			EnumEpayPayInCode_XlsCode_584:                      false,
			EnumEpayPayInCode_GoCode_585:                       false,
			EnumEpayPayInCode_Code_586:                         false,
			EnumEpayPayInCode_Code_587:                         false,
			EnumEpayPayInCode_Code_588:                         false,
			EnumEpayPayInCode_Code_589:                         false,
			EnumEpayPayInCode_Code_590:                         false,
			EnumEpayPayInCode_Code_591:                         false,
			EnumEpayPayInCode_Code_592:                         false,
			EnumEpayPayInCode_Code_593:                         false,
			EnumEpayPayInCode_Code_594:                         false,
			EnumEpayPayInCode_Code_595:                         false,
			EnumEpayPayInCode_Epay1documentCode_596:            false,
			EnumEpayPayInCode_CrtificateIDCode_597:             false,
			EnumEpayPayInCode_Code_598:                         false,
			EnumEpayPayInCode_Code_599:                         false,
			EnumEpayPayInCode_Code_600:                         false,
			EnumEpayPayInCode_Code_601:                         false,
			EnumEpayPayInCode_Code_602:                         false,
			EnumEpayPayInCode_Code_603:                         false,
			EnumEpayPayInCode_Code_604:                         false,
			EnumEpayPayInCode_Code_605:                         false,
			EnumEpayPayInCode_Code_606:                         false,
			EnumEpayPayInCode_Code_607:                         false,
			EnumEpayPayInCode_Code_608:                         false,
			EnumEpayPayInCode_Code_609:                         false,
			EnumEpayPayInCode_Code_610:                         false,
			EnumEpayPayInCode_Code_611:                         false,
			EnumEpayPayInCode_Code_612:                         false,
			EnumEpayPayInCode_Code_613:                         false,
			EnumEpayPayInCode_Code_614:                         false,
			EnumEpayPayInCode_Code_615:                         false,
			EnumEpayPayInCode_Code_616:                         false,
			EnumEpayPayInCode_InvoiceIDCode_617:                false,
			EnumEpayPayInCode_TerminalCode_618:                 false,
			EnumEpayPayInCode_TerminalCode_619:                 false,
			EnumEpayPayInCode_CurrencyCode_620:                 false,
			EnumEpayPayInCode_AmountCode_621:                   false,
			EnumEpayPayInCode_AmountCode_622:                   false,
			EnumEpayPayInCode_AmountCode_623:                   false,
			EnumEpayPayInCode_Code_624:                         false,
			EnumEpayPayInCode_Code_625:                         false,
			EnumEpayPayInCode_Code_626:                         false,
			EnumEpayPayInCode_Code_627:                         false,
			EnumEpayPayInCode_Code_628:                         false,
			EnumEpayPayInCode_Code_629:                         false,
			EnumEpayPayInCode_Code_630:                         false,
			EnumEpayPayInCode_Code_631:                         false,
			EnumEpayPayInCode_Code_632:                         false,
			EnumEpayPayInCode_Code_633:                         false,
			EnumEpayPayInCode_Code_634:                         false,
			EnumEpayPayInCode_EgatewayCode_635:                 false,
			EnumEpayPayInCode_EgatewayCode_636:                 false,
			EnumEpayPayInCode_EgatewayCode_637:                 false,
			EnumEpayPayInCode_Code_638:                         false,
			EnumEpayPayInCode_Code_639:                         false,
			EnumEpayPayInCode_Code_640:                         false,
			EnumEpayPayInCode_Code_641:                         false,
			EnumEpayPayInCode_Code_642:                         false,
			EnumEpayPayInCode_CallbacksendmessageeCode_643:     false,
			EnumEpayPayInCode_Code_644:                         false,
			EnumEpayPayInCode_Code_645:                         false,
			EnumEpayPayInCode_Code_646:                         false,
			EnumEpayPayInCode_Code_647:                         false,
			EnumEpayPayInCode_Code_648:                         false,
			EnumEpayPayInCode_Code_649:                         false,
			EnumEpayPayInCode_Code_650:                         false,
			EnumEpayPayInCode_Code_651:                         false,
			EnumEpayPayInCode_Code_652:                         false,
			EnumEpayPayInCode_Code_653:                         false,
			EnumEpayPayInCode_Code_654:                         false,
			EnumEpayPayInCode_Code_655:                         false,
			EnumEpayPayInCode_Code_656:                         false,
			EnumEpayPayInCode_Code_657:                         false,
			EnumEpayPayInCode_Code_658:                         false,
			EnumEpayPayInCode_Code_659:                         false,
			EnumEpayPayInCode_Code_660:                         false,
			EnumEpayPayInCode_Code_661:                         false,
			EnumEpayPayInCode_Code_662:                         false,
			EnumEpayPayInCode_Code_663:                         false,
			EnumEpayPayInCode_Code_664:                         false,
			EnumEpayPayInCode_Code_665:                         false,
			EnumEpayPayInCode_Code_666:                         false,
			EnumEpayPayInCode_Code_667:                         false,
			EnumEpayPayInCode_Code_668:                         false,
			EnumEpayPayInCode_Code_669:                         false,
			EnumEpayPayInCode_Code_670:                         false,
			EnumEpayPayInCode_Code_671:                         false,
			EnumEpayPayInCode_Code_672:                         false,
			EnumEpayPayInCode_Code_673:                         false,
			EnumEpayPayInCode_EGWHTTPCODE200Code_674:           false,
			EnumEpayPayInCode_Code_675:                         false,
			EnumEpayPayInCode_Code_676:                         false,
			EnumEpayPayInCode_Code_677:                         false,
			EnumEpayPayInCode_Code_678:                         false,
			EnumEpayPayInCode_Code_679:                         false,
			EnumEpayPayInCode_RequesttogetcardinfoCode_680:     false,
			EnumEpayPayInCode_RequesttogetcardinfoCode_681:     false,
			EnumEpayPayInCode_XlsCode_683:                      false,
			EnumEpayPayInCode_XlsCode_684:                      false,
			EnumEpayPayInCode_Code_685:                         false,
			EnumEpayPayInCode_TerminalconfiguratioCode_686:     false,
			EnumEpayPayInCode_RequesttoXLSServerfaCode_687:     false,
			EnumEpayPayInCode_RequesttoXLSServerfaCode_688:     false,
			EnumEpayPayInCode_Code_689:                         false,
			EnumEpayPayInCode_Code_690:                         false,
			EnumEpayPayInCode_Code_691:                         false,
			EnumEpayPayInCode_JsonCode_692:                     false,
			EnumEpayPayInCode_Code_693:                         false,
			EnumEpayPayInCode_Code_694:                         false,
			EnumEpayPayInCode_StatementCode_695:                false,
			EnumEpayPayInCode_StatementCode_696:                false,
			EnumEpayPayInCode_StatementCode_697:                false,
			EnumEpayPayInCode_StatementCode_698:                false,
			EnumEpayPayInCode_StatementCode_699:                false,
			EnumEpayPayInCode_TofromYYYYMMDDCode_700:           false,
			EnumEpayPayInCode_TofromYYYYMMDDCode_701:           false,
			EnumEpayPayInCode_TofromYYYYMMDDCode_702:           false,
			EnumEpayPayInCode_Code_703:                         false,
			EnumEpayPayInCode_Code_704:                         false,
			EnumEpayPayInCode_Code_705:                         false,
			EnumEpayPayInCode_Code_706:                         false,
			EnumEpayPayInCode_Code_707:                         false,
			EnumEpayPayInCode_StaffCode_708:                    false,
			EnumEpayPayInCode_StaffCode_709:                    false,
			EnumEpayPayInCode_StaffCode_710:                    false,
			EnumEpayPayInCode_Code_711:                         false,
			EnumEpayPayInCode_Code_712:                         false,
			EnumEpayPayInCode_Code_713:                         false,
			EnumEpayPayInCode_Code_714:                         false,
			EnumEpayPayInCode_Code_715:                         false,
			EnumEpayPayInCode_Code_716:                         false,
			EnumEpayPayInCode_Code_717:                         false,
			EnumEpayPayInCode_Code_718:                         false,
			EnumEpayPayInCode_Code_719:                         false,
			EnumEpayPayInCode_ErrorinservicingthecCode_720:     false,
			EnumEpayPayInCode_ErrorinservicingthecCode_721:     false,
			EnumEpayPayInCode_ErrorinservicingthecCode_722:     false,
			EnumEpayPayInCode_ErrorinservicingthecCode_723:     false,
			EnumEpayPayInCode_ErrorinservicingthecCode_724:     false,
			EnumEpayPayInCode_ErrorinservicingthecCode_725:     false,
			EnumEpayPayInCode_ErrorinservicingthecCode_726:     false,
			EnumEpayPayInCode_ErrorinservicingthecCode_727:     false,
			EnumEpayPayInCode_NoAuthorizeCode_728:              false,
			EnumEpayPayInCode_BadtokenCode_729:                 false,
			EnumEpayPayInCode_Code_730:                         false,
			EnumEpayPayInCode_InvalidrequestinputCode_731:      false,
			EnumEpayPayInCode_Code_732:                         false,
			EnumEpayPayInCode_Code_733:                         false,
			EnumEpayPayInCode_Code_734:                         false,
			EnumEpayPayInCode_Code_735:                         false,
			EnumEpayPayInCode_Code_736:                         false,
			EnumEpayPayInCode_IdCode_737:                       false,
			EnumEpayPayInCode_Code_738:                         false,
			EnumEpayPayInCode_Code_739:                         false,
			EnumEpayPayInCode_Code_740:                         false,
			EnumEpayPayInCode_Code_741:                         false,
			EnumEpayPayInCode_Code_742:                         false,
			EnumEpayPayInCode_Code_743:                         false,
			EnumEpayPayInCode_Code_744:                         false,
			EnumEpayPayInCode_Code_745:                         false,
			EnumEpayPayInCode_Code_746:                         false,
			EnumEpayPayInCode_Code_747:                         false,
			EnumEpayPayInCode_Code_748:                         false,
			EnumEpayPayInCode_Code_749:                         false,
			EnumEpayPayInCode_Code_750:                         false,
			EnumEpayPayInCode_Code_751:                         false,
			EnumEpayPayInCode_Code_752:                         false,
			EnumEpayPayInCode_Code_753:                         false,
			EnumEpayPayInCode_ApimakePaymentscorerCode_754:     false,
			EnumEpayPayInCode_Code_756:                         false,
			EnumEpayPayInCode_Code_757:                         false,
			EnumEpayPayInCode_MerchantCode_758:                 false,
			EnumEpayPayInCode_Code_759:                         false,
			EnumEpayPayInCode_Code_760:                         false,
			EnumEpayPayInCode_Code_761:                         false,
			EnumEpayPayInCode_Code_762:                         false,
			EnumEpayPayInCode_Code_765:                         false,
			EnumEpayPayInCode_EgatewayCode_766:                 false,
			EnumEpayPayInCode_Code_767:                         false,
			EnumEpayPayInCode_Code_768:                         false,
			EnumEpayPayInCode_Code_769:                         false,
			EnumEpayPayInCode_Code_770:                         false,
			EnumEpayPayInCode_Code_771:                         false,
			EnumEpayPayInCode_Code_772:                         false,
			EnumEpayPayInCode_Code_773:                         false,
			EnumEpayPayInCode_Code_774:                         false,
			EnumEpayPayInCode_Code_775:                         false,
			EnumEpayPayInCode_InvoiceIDCode_776:                false,
			EnumEpayPayInCode_TerminalCode_777:                 false,
			EnumEpayPayInCode_CurrencyCode_778:                 false,
			EnumEpayPayInCode_AmountCode_779:                   false,
			EnumEpayPayInCode_AmountCode_780:                   false,
			EnumEpayPayInCode_CorepaymentCode_781:              false,
			EnumEpayPayInCode_Code_782:                         false,
			EnumEpayPayInCode_Code_783:                         false,
			EnumEpayPayInCode_Code_784:                         false,
			EnumEpayPayInCode_IdCode_785:                       false,
			EnumEpayPayInCode_TerminalIdCode_786:               false,
			EnumEpayPayInCode_Code_787:                         false,
			EnumEpayPayInCode_Code_789:                         false,
			EnumEpayPayInCode_Code_790:                         false,
			EnumEpayPayInCode_Code_791:                         false,
			EnumEpayPayInCode_Code_792:                         false,
			EnumEpayPayInCode_Code_793:                         false,
			EnumEpayPayInCode_Code_795:                         false,
			EnumEpayPayInCode_CorerecurrentCode_796:            false,
			EnumEpayPayInCode_Code_797:                         false,
			EnumEpayPayInCode_Code_798:                         false,
			EnumEpayPayInCode_Code_799:                         false,
			EnumEpayPayInCode_Code_800:                         false,
			EnumEpayPayInCode_Code_801:                         false,
			EnumEpayPayInCode_Code_802:                         false,
			EnumEpayPayInCode_Code_803:                         false,
			EnumEpayPayInCode_Code_804:                         false,
			EnumEpayPayInCode_Code_805:                         false,
			EnumEpayPayInCode_PublicIDCode_806:                 false,
			EnumEpayPayInCode_Code_807:                         false,
			EnumEpayPayInCode_Code_808:                         false,
			EnumEpayPayInCode_Code_809:                         false,
			EnumEpayPayInCode_Code_810:                         false,
			EnumEpayPayInCode_Code_811:                         false,
			EnumEpayPayInCode_Code_812:                         false,
			EnumEpayPayInCode_Code_813:                         false,
			EnumEpayPayInCode_Code_814:                         false,
			EnumEpayPayInCode_Code_815:                         false,
			EnumEpayPayInCode_Code_816:                         false,
			EnumEpayPayInCode_Code_817:                         false,
			EnumEpayPayInCode_Code_818:                         false,
			EnumEpayPayInCode_Code_819:                         false,
			EnumEpayPayInCode_Code_820:                         false,
			EnumEpayPayInCode_Code_821:                         false,
			EnumEpayPayInCode_Code_822:                         false,
			EnumEpayPayInCode_Code_823:                         false,
			EnumEpayPayInCode_Code_824:                         false,
			EnumEpayPayInCode_Code_825:                         false,
			EnumEpayPayInCode_Code_826:                         false,
			EnumEpayPayInCode_Code_827:                         false,
			EnumEpayPayInCode_Code_828:                         false,
			EnumEpayPayInCode_Code_829:                         false,
			EnumEpayPayInCode_Code_830:                         false,
			EnumEpayPayInCode_Code_831:                         false,
			EnumEpayPayInCode_Code_832:                         false,
			EnumEpayPayInCode_Code_833:                         false,
			EnumEpayPayInCode_Code_834:                         false,
			EnumEpayPayInCode_Code_835:                         false,
			EnumEpayPayInCode_Code_836:                         false,
			EnumEpayPayInCode_Code_837:                         false,
			EnumEpayPayInCode_Code_838:                         false,
			EnumEpayPayInCode_Code_839:                         false,
			EnumEpayPayInCode_Code_840:                         false,
			EnumEpayPayInCode_Code_841:                         false,
			EnumEpayPayInCode_Code_842:                         false,
			EnumEpayPayInCode_Code_843:                         false,
			EnumEpayPayInCode_Code_844:                         false,
			EnumEpayPayInCode_Code_845:                         false,
			EnumEpayPayInCode_PublicIdCode_846:                 false,
			EnumEpayPayInCode_MerchantAPICode_847:              false,
			EnumEpayPayInCode_Code_848:                         false,
			EnumEpayPayInCode_Code_849:                         false,
			EnumEpayPayInCode_Code_850:                         false,
			EnumEpayPayInCode_StaffInviteRequestCode_851:       false,
			EnumEpayPayInCode_PublicIdCode_852:                 false,
			EnumEpayPayInCode_Code_853:                         false,
			EnumEpayPayInCode_Code_854:                         false,
			EnumEpayPayInCode_PublicIdCode_855:                 false,
			EnumEpayPayInCode_PublicIdCode_856:                 false,
			EnumEpayPayInCode_Code_857:                         false,
			EnumEpayPayInCode_PublicIdCode_858:                 false,
			EnumEpayPayInCode_Code_859:                         false,
			EnumEpayPayInCode_Code_860:                         false,
			EnumEpayPayInCode_Code_861:                         false,
			EnumEpayPayInCode_Code_862:                         false,
			EnumEpayPayInCode_Code_863:                         false,
			EnumEpayPayInCode_Code_864:                         false,
			EnumEpayPayInCode_Code_865:                         false,
			EnumEpayPayInCode_OauthCode_866:                    false,
			EnumEpayPayInCode_Code_867:                         false,
			EnumEpayPayInCode_Code_868:                         false,
			EnumEpayPayInCode_Code_869:                         false,
			EnumEpayPayInCode_Code_870:                         false,
			EnumEpayPayInCode_Code_871:                         false,
			EnumEpayPayInCode_Code_872:                         false,
			EnumEpayPayInCode_OauthCode_873:                    false,
			EnumEpayPayInCode_Code_874:                         false,
			EnumEpayPayInCode_OauthCode_875:                    false,
			EnumEpayPayInCode_Code_876:                         false,
			EnumEpayPayInCode_MDCode_877:                       false,
			EnumEpayPayInCode_Code_878:                         false,
			EnumEpayPayInCode_Code_879:                         false,
			EnumEpayPayInCode_Code_880:                         false,
			EnumEpayPayInCode_Code_881:                         false,
			EnumEpayPayInCode_EGWCode_882:                      false,
			EnumEpayPayInCode_RedisCode_883:                    false,
			EnumEpayPayInCode_RedisCode_884:                    false,
			EnumEpayPayInCode_RedisCode_885:                    false,
			EnumEpayPayInCode_RedisCode_886:                    false,
			EnumEpayPayInCode_Code_887:                         false,
			EnumEpayPayInCode_AclapiCode_888:                   false,
			EnumEpayPayInCode_Code_889:                         false,
			EnumEpayPayInCode_MerchantapiCode_890:              false,
			EnumEpayPayInCode_Code_891:                         false,
			EnumEpayPayInCode_Code_892:                         false,
			EnumEpayPayInCode_MerchantapiCode_893:              false,
			EnumEpayPayInCode_Code_894:                         false,
			EnumEpayPayInCode_CorepaymentCode_895:              false,
			EnumEpayPayInCode_ConfirmCode_896:                  false,
			EnumEpayPayInCode_Code_897:                         false,
			EnumEpayPayInCode_PublicIDCode_898:                 false,
			EnumEpayPayInCode_Code_899:                         false,
			EnumEpayPayInCode_Code_900:                         false,
			EnumEpayPayInCode_Code_901:                         false,
			EnumEpayPayInCode_PublicIDCode_902:                 false,
			EnumEpayPayInCode_Code_903:                         false,
			EnumEpayPayInCode_Code_904:                         false,
			EnumEpayPayInCode_Code_905:                         false,
			EnumEpayPayInCode_Code_906:                         false,
			EnumEpayPayInCode_Code_907:                         false,
			EnumEpayPayInCode_Code_908:                         false,
			EnumEpayPayInCode_MerchantapiCode_909:              false,
			EnumEpayPayInCode_MerchantapiCode_910:              false,
			EnumEpayPayInCode_Code_911:                         false,
			EnumEpayPayInCode_Code_912:                         false,
			EnumEpayPayInCode_Code_913:                         false,
			EnumEpayPayInCode_Code_914:                         false,
			EnumEpayPayInCode_Code_915:                         false,
			EnumEpayPayInCode_Code_916:                         false,
			EnumEpayPayInCode_Code_917:                         false,
			EnumEpayPayInCode_Code_918:                         false,
			EnumEpayPayInCode_Code_919:                         false,
			EnumEpayPayInCode_Code_920:                         false,
			EnumEpayPayInCode_Code_921:                         false,
			EnumEpayPayInCode_Code_922:                         false,
			EnumEpayPayInCode_Code_923:                         false,
			EnumEpayPayInCode_Code_924:                         false,
			EnumEpayPayInCode_Code_925:                         false,
			EnumEpayPayInCode_Code_926:                         false,
			EnumEpayPayInCode_Code_927:                         false,
			EnumEpayPayInCode_Code_928:                         false,
			EnumEpayPayInCode_PublicIDCode_929:                 false,
			EnumEpayPayInCode_Code_930:                         false,
			EnumEpayPayInCode_Code_931:                         false,
			EnumEpayPayInCode_ScopesCode_932:                   false,
			EnumEpayPayInCode_Code_933:                         false,
			EnumEpayPayInCode_Code_934:                         false,
			EnumEpayPayInCode_Code_935:                         false,
			EnumEpayPayInCode_Code_937:                         false,
			EnumEpayPayInCode_Code_938:                         false,
			EnumEpayPayInCode_Code_939:                         false,
			EnumEpayPayInCode_Code_940:                         false,
			EnumEpayPayInCode_Code_941:                         false,
			EnumEpayPayInCode_Code_942:                         false,
			EnumEpayPayInCode_Code_943:                         false,
			EnumEpayPayInCode_Code_944:                         false,
			EnumEpayPayInCode_PublicIDCode_945:                 false,
			EnumEpayPayInCode_Code_946:                         false,
			EnumEpayPayInCode_Code_947:                         false,
			EnumEpayPayInCode_Code_948:                         false,
			EnumEpayPayInCode_Code_949:                         false,
			EnumEpayPayInCode_Code_950:                         false,
			EnumEpayPayInCode_PublicIdCode_951:                 false,
			EnumEpayPayInCode_Code_952:                         false,
			EnumEpayPayInCode_Code_953:                         false,
			EnumEpayPayInCode_Code_954:                         false,
			EnumEpayPayInCode_Code_955:                         false,
			EnumEpayPayInCode_Code_956:                         false,
			EnumEpayPayInCode_Code_957:                         false,
			EnumEpayPayInCode_Code_958:                         false,
			EnumEpayPayInCode_Code_959:                         false,
			EnumEpayPayInCode_Code_960:                         false,
			EnumEpayPayInCode_Code_961:                         false,
			EnumEpayPayInCode_Code_962:                         false,
			EnumEpayPayInCode_AuthorizedRequestJSOCode_963:     false,
			EnumEpayPayInCode_ClientsecretCode_964:             false,
			EnumEpayPayInCode_ClientidclientsecretCode_965:     false,
			EnumEpayPayInCode_MerchantIDclientsecrCode_966:     false,
			EnumEpayPayInCode_ClientsecretclientIDCode_967:     false,
			EnumEpayPayInCode_EmailclientIDclientsCode_968:     false,
			EnumEpayPayInCode_Code_969:                         false,
			EnumEpayPayInCode_Code_970:                         false,
			EnumEpayPayInCode_Code_971:                         false,
			EnumEpayPayInCode_Code_972:                         false,
			EnumEpayPayInCode_IDCode_973:                       false,
			EnumEpayPayInCode_Code_974:                         false,
			EnumEpayPayInCode_Code_975:                         false,
			EnumEpayPayInCode_UserclientIDclientseCode_976:     false,
			EnumEpayPayInCode_AuthorizedRequestJSOCode_977:     false,
			EnumEpayPayInCode_JsonMarshalsendEmailCode_978:     false,
			EnumEpayPayInCode_Code_979:                         false,
			EnumEpayPayInCode_FormFileCode_980:                 false,
			EnumEpayPayInCode_Code_981:                         false,
			EnumEpayPayInCode_Code_982:                         false,
			EnumEpayPayInCode_Code_983:                         false,
			EnumEpayPayInCode_Code_984:                         false,
			EnumEpayPayInCode_Code_985:                         false,
			EnumEpayPayInCode_Code_986:                         false,
			EnumEpayPayInCode_Code_987:                         false,
			EnumEpayPayInCode_Code_988:                         false,
			EnumEpayPayInCode_Code_989:                         false,
			EnumEpayPayInCode_Code_990:                         false,
			EnumEpayPayInCode_Code_991:                         false,
			EnumEpayPayInCode_Code_992:                         false,
			EnumEpayPayInCode_ServiceCode_993:                  false,
			EnumEpayPayInCode_ShouldBindJSONservicCode_994:     false,
			EnumEpayPayInCode_AuthorizedRequestJSOCode_995:     false,
			EnumEpayPayInCode_AuthorizedRequestJSOCode_996:     false,
			EnumEpayPayInCode_UseruserusernameservCode_997:     false,
			EnumEpayPayInCode_MerchantserviceCode_999:          false,
			EnumEpayPayInCode_NamewebserviceCode_1000:          false,
			EnumEpayPayInCode_ContactTypenameserviCode_1001:    false,
			EnumEpayPayInCode_PublicIdCode_1002:                false,
			EnumEpayPayInCode_Code_1003:                        false,
			EnumEpayPayInCode_Code_1004:                        false,
			EnumEpayPayInCode_Code_1005:                        false,
			EnumEpayPayInCode_OauthCode_1006:                   false,
			EnumEpayPayInCode_Code_1007:                        false,
			EnumEpayPayInCode_Code_1008:                        false,
			EnumEpayPayInCode_Code_1009:                        false,
			EnumEpayPayInCode_IdserviceCode_1010:               false,
			EnumEpayPayInCode_RSAserviceCode_1011:              false,
			EnumEpayPayInCode_ServiceCode_1012:                 false,
			EnumEpayPayInCode_Codebase64stringbaseCode_1013:    false,
			EnumEpayPayInCode_XmlUnmarshalserviceCode_1014:     false,
			EnumEpayPayInCode_Codebase64stringbaseCode_1015:    false,
			EnumEpayPayInCode_XmlMarshalserviceCode_1016:       false,
			EnumEpayPayInCode_OAUTHCode_1017:                   false,
			EnumEpayPayInCode_ServiceCode_1018:                 false,
			EnumEpayPayInCode_ShopidCode_1019:                  false,
			EnumEpayPayInCode_JsonserviceCode_1020:             false,
			EnumEpayPayInCode_AuthserviceCode_1021:             false,
			EnumEpayPayInCode_DeviceIDauthserviceCode_1022:     false,
			EnumEpayPayInCode_ServiceCode_1023:                 false,
			EnumEpayPayInCode_IdserviceCode_1024:               false,
			EnumEpayPayInCode_BindXMLwebserviceCode_1025:       false,
			EnumEpayPayInCode_AuthorizedRequestJSOCode_1026:    false,
			EnumEpayPayInCode_BindclientIDpostLinkCode_1027:    false,
			EnumEpayPayInCode_ClientIDpostLinkInfoCode_1028:    false,
			EnumEpayPayInCode_MerchantIDpostLinkInCode_1029:    false,
			EnumEpayPayInCode_PostlinkrabbitservicCode_1030:    false,
			EnumEpayPayInCode_TerminalidpostLinkInCode_1031:    false,
			EnumEpayPayInCode_CBindpostLinkInfoposCode_1032:    false,
			EnumEpayPayInCode_JsonMarshalpostlinkICode_1033:    false,
			EnumEpayPayInCode_ClientIDclientIDservCode_1034:    false,
			EnumEpayPayInCode_ShopmerchantIDclientCode_1035:    false,
			EnumEpayPayInCode_ShopMerchantIDclientCode_1036:    false,
			EnumEpayPayInCode_BindinvoicewebservicCode_1037:    false,
			EnumEpayPayInCode_ExpirePeriodinvoicesCode_1038:    false,
			EnumEpayPayInCode_PostlinkRabbitservicCode_1039:    false,
			EnumEpayPayInCode_ServiceCode_1040:                 false,
			EnumEpayPayInCode_JsonserviceCode_1041:             false,
			EnumEpayPayInCode_EgwCode_1042:                     false,
			EnumEpayPayInCode_EmailclientsecretserCode_1043:    false,
			EnumEpayPayInCode_EmailwebserviceCode_1044:         false,
			EnumEpayPayInCode_AuthorizedRequestJSOCode_1045:    false,
			EnumEpayPayInCode_JsonMarshalmerchantoCode_1046:    false,
			EnumEpayPayInCode_JsonMarshalmerchantoCode_1047:    false,
			EnumEpayPayInCode_BindJSONmerchantwebsCode_1048:    false,
			EnumEpayPayInCode_ClientidserviceCode_1049:         false,
			EnumEpayPayInCode_ClientScopesclientIDCode_1050:    false,
			EnumEpayPayInCode_UserloginserviceCode_1051:        false,
			EnumEpayPayInCode_ClientclientscopesusCode_1052:    false,
			EnumEpayPayInCode_BindJSONinvoiceCode_1053:         false,
			EnumEpayPayInCode_InvoiceCode_1054:                 false,
			EnumEpayPayInCode_XMLway4Code_1055:                 false,
			EnumEpayPayInCode_InvoiceCode_1056:                 false,
			EnumEpayPayInCode_Code_1057:                        false,
			EnumEpayPayInCode_Code_1058:                        false,
			EnumEpayPayInCode_Code_1059:                        false,
			EnumEpayPayInCode_CoreinvoiceCode_1060:             false,
			EnumEpayPayInCode_BindJSONinvoiceInfoCode_1061:     false,
			EnumEpayPayInCode_Code_1062:                        false,
			EnumEpayPayInCode_Code_1063:                        false,
			EnumEpayPayInCode_ScannererrCode_1064:              false,
			EnumEpayPayInCode_BindCode_1065:                    false,
			EnumEpayPayInCode_ShopIdCode_1066:                  false,
			EnumEpayPayInCode_InvoiceLinkCode_1067:             false,
			EnumEpayPayInCode_MarshalinvoicelinkCode_1068:      false,
			EnumEpayPayInCode_InvoiceLinkCode_1069:             false,
			EnumEpayPayInCode_InvoiceLinkCode_1070:             false,
			EnumEpayPayInCode_InvoiceLinkCode_1071:             false,
			EnumEpayPayInCode_PublicIDinvoiceCode_1072:         false,
			EnumEpayPayInCode_ParamsinvoiceCode_1073:           false,
			EnumEpayPayInCode_InvoiceLinkCode_1074:             false,
			EnumEpayPayInCode_BindCode_1075:                    false,
			EnumEpayPayInCode_StrconvAtoiExpirePerCode_1076:    false,
			EnumEpayPayInCode_InvoiceLinkCode_1077:             false,
			EnumEpayPayInCode_EmailCode_1078:                   false,
			EnumEpayPayInCode_SmsCode_1079:                     false,
			EnumEpayPayInCode_InvoiceLinkCode_1080:             false,
			EnumEpayPayInCode_BindCode_1081:                    false,
			EnumEpayPayInCode_PublicidCode_1082:                false,
			EnumEpayPayInCode_NameinvoicelinkCode_1083:         false,
			EnumEpayPayInCode_InvoiceLinkCode_1084:             false,
			EnumEpayPayInCode_SearchparamsinvoicelCode_1085:    false,
			EnumEpayPayInCode_IncorrectsearchmethoCode_1086:    false,
			EnumEpayPayInCode_Code_1087:                        false,
			EnumEpayPayInCode_InvoiceLinkidCode_1088:           false,
			EnumEpayPayInCode_InvoiceLinkCode_1089:             false,
			EnumEpayPayInCode_Code_1090:                        false,
			EnumEpayPayInCode_InvoiceLinkCode_1091:             false,
			EnumEpayPayInCode_PublicIDCode_1092:                false,
			EnumEpayPayInCode_Code_1093:                        false,
			EnumEpayPayInCode_Code_1096:                        false,
			EnumEpayPayInCode_HomeBankCode_1097:                false,
			EnumEpayPayInCode_EmailCode_1098:                   false,
			EnumEpayPayInCode_Code_1099:                        false,
			EnumEpayPayInCode_Code_1100:                        false,
			EnumEpayPayInCode_CoreinvoiceCode_1101:             false,
			EnumEpayPayInCode_AuthorizedRequestJSOCode_1103:    false,
			EnumEpayPayInCode_AuthorizedRequestJSOCode_1104:    false,
			EnumEpayPayInCode_IdserviceCode_1105:               false,
			EnumEpayPayInCode_ClientIDserviceCode_1106:         false,
			EnumEpayPayInCode_TerminalXLSMIDterminCode_1107:    false,
			EnumEpayPayInCode_Code_1130:                        false,
			EnumEpayPayInCode_Code_1132:                        false,
			EnumEpayPayInCode_NameCode_1133:                    false,
			EnumEpayPayInCode_PostlinkcorecallbackCode_1144:    false,
			EnumEpayPayInCode_Code_1145:                        false,
			EnumEpayPayInCode_CannotsendpostlinkCode_1146:      false,
			EnumEpayPayInCode_CannotsendpostlinkCode_1147:      false,
			EnumEpayPayInCode_BindJsonCode_1148:                false,
			EnumEpayPayInCode_Base64Code_1149:                  false,
			EnumEpayPayInCode_UnmarshalCode_1150:               false,
			EnumEpayPayInCode_HttpcoremigrationCode_1151:       false,
			EnumEpayPayInCode_Code_1152:                        false,
			EnumEpayPayInCode_UnmarshalouathCode_1153:          false,
			EnumEpayPayInCode_EPAY1Code_1154:                   false,
			EnumEpayPayInCode_UnmarshalEPAY1Code_1155:          false,
			EnumEpayPayInCode_CannotsendpostlinkCode_1156:      false,
			EnumEpayPayInCode_Code_1157:                        false,
			EnumEpayPayInCode_UserserviceCode_1158:             false,
			EnumEpayPayInCode_UserserviceCode_1159:             false,
			EnumEpayPayInCode_UserserviceCode_1160:             false,
			EnumEpayPayInCode_UserserviceCode_1161:             false,
			EnumEpayPayInCode_UserserviceCode_1162:             false,
			EnumEpayPayInCode_MerchantcompanynamesCode_1163:    false,
			EnumEpayPayInCode_MerchantserviceCode_1164:         false,
			EnumEpayPayInCode_MerchantmerchantCode_1165:        false,
			EnumEpayPayInCode_UserloginuserclientCode_1166:     false,
			EnumEpayPayInCode_MerchantnameuserclieCode_1167:    false,
			EnumEpayPayInCode_AuthorizedRequestJSOCode_1168:    false,
			EnumEpayPayInCode_MerchantnameCode_1169:            false,
			EnumEpayPayInCode_EmailmerchantCode_1170:           false,
			EnumEpayPayInCode_ShopmerchantCode_1171:            false,
			EnumEpayPayInCode_TerminalmerchantCode_1172:        false,
			EnumEpayPayInCode_UseruserCode_1173:                false,
			EnumEpayPayInCode_UserclientIDCode_1174:            false,
			EnumEpayPayInCode_UserclientuserCode_1175:          false,
			EnumEpayPayInCode_TerminalterminalIDCode_1176:      false,
			EnumEpayPayInCode_CurrencycurrencyNameCode_1177:    false,
			EnumEpayPayInCode_TerminalIDCode_1178:              false,
			EnumEpayPayInCode_TerminalterminalIDteCode_1179:    false,
			EnumEpayPayInCode_TerminalIDCode_1180:              false,
			EnumEpayPayInCode_AuthorizedRequestJSOCode_1182:    false,
			EnumEpayPayInCode_Code_1184:                        false,
			EnumEpayPayInCode_TerminalCode_1185:                false,
			EnumEpayPayInCode_TerminalCode_1186:                false,
			EnumEpayPayInCode_Code_1187:                        false,
			EnumEpayPayInCode_MerchantContactTypenCode_1188:    false,
			EnumEpayPayInCode_SenderrorreportcontaCode_1189:    false,
			EnumEpayPayInCode_Code_1190:                        false,
			EnumEpayPayInCode_IdCode_1191:                      false,
			EnumEpayPayInCode_ExcelCode_1192:                   false,
			EnumEpayPayInCode_CurrencycurrencyIDCode_1193:      false,
			EnumEpayPayInCode_CardTypeCardTypeIDCode_1194:      false,
			EnumEpayPayInCode_EmailCode_1195:                   false,
			EnumEpayPayInCode_JsonMarshalemailCode_1196:        false,
			EnumEpayPayInCode_AuthorizedRequestJSOCode_1197:    false,
			EnumEpayPayInCode_MerchantContactTypeeCode_1198:    false,
			EnumEpayPayInCode_MerchantContactemailCode_1199:    false,
			EnumEpayPayInCode_ExcelfileemailCode_1200:          false,
			EnumEpayPayInCode_Code_1201:                        false,
			EnumEpayPayInCode_UserloginCode_1202:               false,
			EnumEpayPayInCode_RowsexcelfilereportCode_1203:     false,
			EnumEpayPayInCode_UserConnectionsuserICode_1205:    false,
			EnumEpayPayInCode_KafkaCode_1206:                   false,
			EnumEpayPayInCode_Code_1207:                        false,
			EnumEpayPayInCode_Code_1208:                        false,
			EnumEpayPayInCode_Code_1209:                        false,
			EnumEpayPayInCode_Code_1210:                        false,
			EnumEpayPayInCode_AuthorizedRequestMulCode_1211:    false,
			EnumEpayPayInCode_FormatdataheaderwritCode_1212:    false,
			EnumEpayPayInCode_WriterreadermultiparCode_1213:    false,
			EnumEpayPayInCode_ApicdnCode_1214:                  false,
			EnumEpayPayInCode_LogourlshopIDCode_1215:           false,
			EnumEpayPayInCode_BindJSONshopshopIDCode_1216:      false,
			EnumEpayPayInCode_ShopIDdbshopIDCode_1217:          false,
			EnumEpayPayInCode_JSONSamsungPayCode_1219:          false,
			EnumEpayPayInCode_RequestJSONSamsungPaCode_1220:    false,
			EnumEpayPayInCode_ShopIDshopIDCode_1221:            false,
			EnumEpayPayInCode_TerminalshopIDCode_1222:          false,
			EnumEpayPayInCode_AuthorizedRequestJSOCode_1223:    false,
			EnumEpayPayInCode_ShopIDshopIDCode_1224:            false,
			EnumEpayPayInCode_BindJSONinOutTerminaCode_1225:    false,
			EnumEpayPayInCode_IdidCode_1226:                    false,
			EnumEpayPayInCode_AuthorizedRequestJSOCode_1227:    false,
			EnumEpayPayInCode_TerminalterminalidCode_1228:      false,
			EnumEpayPayInCode_TerminalidterminalidCode_1229:    false,
			EnumEpayPayInCode_TerminalterminalidCode_1230:      false,
			EnumEpayPayInCode_JsonMarshalterminaltCode_1231:    false,
			EnumEpayPayInCode_PANCode_1232:                     false,
			EnumEpayPayInCode_SamsungPaycallbackreCode_1233:    false,
			EnumEpayPayInCode_StaffstaffCode_1234:              false,
			EnumEpayPayInCode_BindCreateMVisaTransCode_1235:    false,
			EnumEpayPayInCode_RequestPostFormXMLCrCode_1236:    false,
			EnumEpayPayInCode_RequestPostFormXMLCrCode_1237:    false,
			EnumEpayPayInCode_RequestPostFormXMLCrCode_1238:    false,
			EnumEpayPayInCode_RequestPostFormXMLCrCode_1239:    false,
			EnumEpayPayInCode_BindGet3DSecureCode_1240:         false,
			EnumEpayPayInCode_RequestPostFormXMLGeCode_1241:    false,
			EnumEpayPayInCode_RequestPostFormXMLGeCode_1242:    false,
			EnumEpayPayInCode_RequestPostFormXMLGeCode_1243:    false,
			EnumEpayPayInCode_RequestPostFormXMLGeCode_1244:    false,
			EnumEpayPayInCode_BindManageTransCode_1245:         false,
			EnumEpayPayInCode_RequestPostFormXMLMaCode_1246:    false,
			EnumEpayPayInCode_RequestPostFormXMLMaCode_1247:    false,
			EnumEpayPayInCode_RequestPostFormXMLMaCode_1248:    false,
			EnumEpayPayInCode_RequestPostFormXMLMaCode_1249:    false,
			EnumEpayPayInCode_BindCreateTransCode_1250:         false,
			EnumEpayPayInCode_RequestPostFormXMLCrCode_1251:    false,
			EnumEpayPayInCode_RequestPostFormXMLCrCode_1252:    false,
			EnumEpayPayInCode_RequestPostFormXMLCrCode_1253:    false,
			EnumEpayPayInCode_RequestPostFormXMLCrCode_1254:    false,
			EnumEpayPayInCode_BindRePaymentCode_1255:           false,
			EnumEpayPayInCode_RequestPostFormXMLReCode_1256:    false,
			EnumEpayPayInCode_RequestPostFormXMLReCode_1257:    false,
			EnumEpayPayInCode_RequestPostFormXMLReCode_1258:    false,
			EnumEpayPayInCode_RequestPostFormXMLReCode_1259:    false,
			EnumEpayPayInCode_BindGetTokenCode_1260:            false,
			EnumEpayPayInCode_RequestPostFormXMLGeCode_1261:    false,
			EnumEpayPayInCode_RequestPostFormXMLGeCode_1262:    false,
			EnumEpayPayInCode_RequestPostFormXMLGeCode_1263:    false,
			EnumEpayPayInCode_RequestPostFormXMLGeCode_1264:    false,
			EnumEpayPayInCode_BindTransactionCode_1265:         false,
			EnumEpayPayInCode_RequestPostFormXMLTrCode_1266:    false,
			EnumEpayPayInCode_CardsystemcommunicatCode_1267:    false,
			EnumEpayPayInCode_RequestPostFormXMLTrCode_1268:    false,
			EnumEpayPayInCode_RequestPostFormXMLTrCode_1269:    false,
			EnumEpayPayInCode_RequestPostFormXMLTrCode_1270:    false,
			EnumEpayPayInCode_BindTransactionConfiCode_1271:    false,
			EnumEpayPayInCode_RequestPostFormXMLTrCode_1272:    false,
			EnumEpayPayInCode_RequestPostFormXMLTrCode_1273:    false,
			EnumEpayPayInCode_RequestPostFormXMLTrCode_1274:    false,
			EnumEpayPayInCode_RequestPostFormXMLTrCode_1275:    false,
			EnumEpayPayInCode_InvoiceLinksGetInvoiCode_1276:    false,
			EnumEpayPayInCode_PublicIDuserIDCode_1277:          false,
			EnumEpayPayInCode_ParamsCode_1278:                  false,
			EnumEpayPayInCode_PostlinkcorecallbackCode_1279:    false,
			EnumEpayPayInCode_RabbitpostlinkCode_1280:          false,
			EnumEpayPayInCode_PublicIDtokeninvoiceCode_1281:    false,
			EnumEpayPayInCode_SlowpostlinkcorecallCode_1282:    false,
			EnumEpayPayInCode_GetOpenWayIDHalykBonCode_1283:    false,
			EnumEpayPayInCode_CardTypenilCode_1284:             false,
			EnumEpayPayInCode_CardtypeCode_1285:                false,
			EnumEpayPayInCode_PostlinkcorecallbackCode_1286:    false,
			EnumEpayPayInCode_PostlinkCode_1287:                false,
			EnumEpayPayInCode_PostlinkinvoiceIDCode_1288:       false,
			EnumEpayPayInCode_CallbackcorecallbackCode_1289:    false,
			EnumEpayPayInCode_InvoiceLinkInvoiceIDCode_1290:    false,
			EnumEpayPayInCode_InvoiceLinkInvoiceIDCode_1291:    false,
			EnumEpayPayInCode_InvoiceLinkinvoiceLiCode_1293:    false,
			EnumEpayPayInCode_ErrorMessageinvoiceLCode_1294:    false,
			EnumEpayPayInCode_TransactionStatusAUTCode_1295:    false,
			EnumEpayPayInCode_FilePathFileIDCode_1296:          false,
			EnumEpayPayInCode_BindChargeCode_1297:              false,
			EnumEpayPayInCode_Code_1298:                        false,
			EnumEpayPayInCode_InformationStatementCode_1299:    false,
			EnumEpayPayInCode_RefIDCode_1301:                   false,
			EnumEpayPayInCode_EmailCode_1302:                   false,
			EnumEpayPayInCode_StatementHistoryOmitCode_1303:    false,
			EnumEpayPayInCode_StaffListCode_1304:               false,
			EnumEpayPayInCode_GETCode_1305:                     false,
			EnumEpayPayInCode_AuthorizedRequestJSOCode_1306:    false,
			EnumEpayPayInCode_Code_1307:                        false,
			EnumEpayPayInCode_CoreinvoiceinvoiceLiCode_1308:    false,
			EnumEpayPayInCode_CoreinvoiceinvoiceLiCode_1309:    false,
			EnumEpayPayInCode_InvoiceLinkCode_1310:             false,
			EnumEpayPayInCode_InvoiceLinkinvoiceIDCode_1311:    false,
			EnumEpayPayInCode_CoreinvoiceinvoiceInCode_1312:    false,
			EnumEpayPayInCode_KafkaCode_1315:                   false,
			EnumEpayPayInCode_HomebankaCode_1317:               false,
			EnumEpayPayInCode_Code_1318:                        false,
			EnumEpayPayInCode_UseraCode_1319:                   false,
			EnumEpayPayInCode_StaffupdatepasswordCode_1320:     false,
			EnumEpayPayInCode_UpdateStaffRoleCode_1321:         false,
			EnumEpayPayInCode_Code_1322:                        false,
			EnumEpayPayInCode_COMMITCode_1323:                  false,
			EnumEpayPayInCode_Code_1324:                        false,
			EnumEpayPayInCode_Code_1325:                        false,
			EnumEpayPayInCode_Code_1326:                        false,
			EnumEpayPayInCode_Float64FeeAmountstriCode_1327:    false,
			EnumEpayPayInCode_Float64SettlAmountstCode_1328:    false,
			EnumEpayPayInCode_Float64TrAmountstrinCode_1329:    false,
			EnumEpayPayInCode_Code_1330:                        false,
			EnumEpayPayInCode_Code_1331:                        false,
			EnumEpayPayInCode_ParseMediaTypeCode_1332:          false,
			EnumEpayPayInCode_CreateFileCode_1333:              false,
			EnumEpayPayInCode_EmailCode_1334:                   false,
			EnumEpayPayInCode_HBCode_1335:                      false,
			EnumEpayPayInCode_Code_1336:                        false,
			EnumEpayPayInCode_Code_1337:                        false,
			EnumEpayPayInCode_ACTIVECode_1338:                  false,
			EnumEpayPayInCode_CHARGEDCode_1339:                 false,
			EnumEpayPayInCode_MerchantterminaltermCode_1340:    false,
			EnumEpayPayInCode_PostingDatestringOrdCode_1341:    false,
			EnumEpayPayInCode_HBCode_1342:                      false,
			EnumEpayPayInCode_FTPCode_1343:                     false,
			EnumEpayPayInCode_JobExecutiongocronCode_1344:      false,
			EnumEpayPayInCode_Float64FeeAmountstriCode_1346:    false,
			EnumEpayPayInCode_Float64SettlAmountstCode_1347:    false,
			EnumEpayPayInCode_Float64TrAmountstrinCode_1348:    false,
			EnumEpayPayInCode_MerchantPublicIDCode_1361:        false,
			EnumEpayPayInCode_MerchantInfoPublicIDCode_1366:    false,
			EnumEpayPayInCode_PublicIDCode_1367:                false,
			EnumEpayPayInCode_Code_1368:                        false,
			EnumEpayPayInCode_Code_1369:                        false,
			EnumEpayPayInCode_Code_1370:                        false,
			EnumEpayPayInCode_AuthorizedRequestJSOCode_1371:    false,
			EnumEpayPayInCode_CoreGetCardCode_1372:             false,
			EnumEpayPayInCode_Code_1373:                        false,
			EnumEpayPayInCode_OauthCode_1374:                   false,
			EnumEpayPayInCode_RequestClientCode_1375:           false,
			EnumEpayPayInCode_Code_1376:                        false,
			EnumEpayPayInCode_Code_1378:                        false,
			EnumEpayPayInCode_ScopeCode_1379:                   false,
			EnumEpayPayInCode_ScopeCode_1380:                   false,
			EnumEpayPayInCode_ClientScopesCode_1381:            false,
			EnumEpayPayInCode_Code_1382:                        false,
			EnumEpayPayInCode_Code_1383:                        false,
			EnumEpayPayInCode_Code_1384:                        false,
			EnumEpayPayInCode_Code_1385:                        false,
			EnumEpayPayInCode_Code_1386:                        false,
			EnumEpayPayInCode_HomeBankCode_1387:                false,
			EnumEpayPayInCode_HMCode_1388:                      false,
			EnumEpayPayInCode_RequestClientUpdateCode_1389:     false,
			EnumEpayPayInCode_ClientIDCode_1390:                false,
			EnumEpayPayInCode_GetClientclientCode_1391:         false,
			EnumEpayPayInCode_GetClientScopeCode_1392:          false,
			EnumEpayPayInCode_UpdateScopeCode_1393:             false,
			EnumEpayPayInCode_ScopesCode_1394:                  false,
			EnumEpayPayInCode_ClientCode_1395:                  false,
			EnumEpayPayInCode_ScopeCode_1396:                   false,
			EnumEpayPayInCode_ClientScopesCode_1397:            false,
			EnumEpayPayInCode_ReadererrorCode_1398:             false,
			EnumEpayPayInCode_TerminaldbmerchantIDCode_1400:    false,
			EnumEpayPayInCode_ClientclientIDdbTildCode_1403:    false,
			EnumEpayPayInCode_ClientclientIDdbCode_1405:        false,
			EnumEpayPayInCode_ClientIPTildaCode_1406:           false,
			EnumEpayPayInCode_BindmultipartformdatCode_1407:    false,
			EnumEpayPayInCode_TildaTildaCode_1408:              false,
			EnumEpayPayInCode_TildaCode_1409:                   false,
			EnumEpayPayInCode_Float64amountTildaCode_1410:      false,
			EnumEpayPayInCode_AuthorizedRequestJSOCode_1411:    false,
			EnumEpayPayInCode_SourceCode_1412:                  false,
			EnumEpayPayInCode_CallbackcorecallbackCode_1413:    false,
			EnumEpayPayInCode_PostlinkTildaCode_1414:           false,
			EnumEpayPayInCode_Code_1415:                        false,
			EnumEpayPayInCode_Code_1416:                        false,
			EnumEpayPayInCode_AuthorizedRequestJSOCode_1418:    false,
			EnumEpayPayInCode_CoreStatementCode_1419:           false,
			EnumEpayPayInCode_Code_1421:                        false,
			EnumEpayPayInCode_Code_1422:                        false,
			EnumEpayPayInCode_MerchantcompanynameCode_1423:     false,
			EnumEpayPayInCode_ClientTypeCode_1424:              false,
			EnumEpayPayInCode_ClientTypenameTildaCode_1425:     false,
			EnumEpayPayInCode_ClientTypeTildaClienCode_1426:    false,
			EnumEpayPayInCode_BindJSONBindJSONCode_1427:        false,
			EnumEpayPayInCode_ParsingerrorCode_1428:            false,
			EnumEpayPayInCode_Code_1429:                        false,
			EnumEpayPayInCode_Code_1430:                        false,
			EnumEpayPayInCode_Code_1431:                        false,
			EnumEpayPayInCode_LIKECode_1432:                    false,
			EnumEpayPayInCode_BETWEENCode_1433:                 false,
			EnumEpayPayInCode_Code_1434:                        false,
			EnumEpayPayInCode_Code_1436:                        false,
			EnumEpayPayInCode_Code_1437:                        false,
			EnumEpayPayInCode_Code_1438:                        false,
			EnumEpayPayInCode_CountCode_1439:                   false,
			EnumEpayPayInCode_Code_1440:                        false,
			EnumEpayPayInCode_Code_1441:                        false,
			EnumEpayPayInCode_ReferenceEPAYCode_1442:           false,
			EnumEpayPayInCode_Code_1443:                        false,
			EnumEpayPayInCode_Code_1444:                        false,
			EnumEpayPayInCode_Code_1445:                        false,
			EnumEpayPayInCode_HMHMBCode_1446:                   false,
			EnumEpayPayInCode_JSONJSONCode_1447:                false,
			EnumEpayPayInCode_UrlbodyCode_1448:                 false,
			EnumEpayPayInCode_Code_1449:                        false,
			EnumEpayPayInCode_GetUseScopeByClientCode_1451:     false,
			EnumEpayPayInCode_Code_1452:                        false,
			EnumEpayPayInCode_JSONUnmarshalCode_1453:           false,
			EnumEpayPayInCode_Code_1454:                        false,
			EnumEpayPayInCode_Code_1455:                        false,
			EnumEpayPayInCode_WebsocketCode_1457:               false,
			EnumEpayPayInCode_WebsocketCode_1458:               false,
			EnumEpayPayInCode_WebsocketCode_1459:               false,
			EnumEpayPayInCode_WebsocketCode_1460:               false,
			EnumEpayPayInCode_WebsocketCode_1461:               false,
			EnumEpayPayInCode_WebsocketCode_1462:               false,
			EnumEpayPayInCode_Code_1463:                        false,
			EnumEpayPayInCode_Code_1464:                        false,
			EnumEpayPayInCode_Code_1465:                        false,
			EnumEpayPayInCode_Code_1466:                        false,
			EnumEpayPayInCode_Code_1467:                        false,
			EnumEpayPayInCode_UpdateMerchantCode_1468:          false,
			EnumEpayPayInCode_UpdateMerchantShopCode_1469:      false,
			EnumEpayPayInCode_UpdateMerchantTerminCode_1470:    false,
			EnumEpayPayInCode_Code_1471:                        false,
			EnumEpayPayInCode_Code_1472:                        false,
			EnumEpayPayInCode_EmailCode_1473:                   false,
			EnumEpayPayInCode_NameserviceCode_1475:             false,
			EnumEpayPayInCode_IdparamsCode_1476:                false,
			EnumEpayPayInCode_MerchantpublicIDCode_1477:        false,
			EnumEpayPayInCode_TerminalmerchantIDCode_1478:      false,
			EnumEpayPayInCode_ShopidshopwebserviceCode_1479:    false,
			EnumEpayPayInCode_Code_1480:                        false,
			EnumEpayPayInCode_Code_1481:                        false,
			EnumEpayPayInCode_IdparamsCode_1482:                false,
			EnumEpayPayInCode_JSONJSONCode_1483:                false,
			EnumEpayPayInCode_JSONjsonMarshalJSONCode_1484:     false,
			EnumEpayPayInCode_IdparamsCode_1486:                false,
			EnumEpayPayInCode_Code_1487:                        false,
			EnumEpayPayInCode_Code_1488:                        false,
			EnumEpayPayInCode_Code_1489:                        false,
			EnumEpayPayInCode_Code_1490:                        false,
			EnumEpayPayInCode_PaymentRequestCode_1491:          false,
			EnumEpayPayInCode_Code_1492:                        false,
			EnumEpayPayInCode_JsonUnmarshalCode_1493:           false,
			EnumEpayPayInCode_JsonMarshalCode_1494:             false,
			EnumEpayPayInCode_Code_1495:                        false,
			EnumEpayPayInCode_JSONJSONCode_1496:                false,
			EnumEpayPayInCode_Code_1497:                        false,
			EnumEpayPayInCode_Code_1498:                        false,
			EnumEpayPayInCode_AuthorizedRequestJSOCode_1499:    false,
			EnumEpayPayInCode_AuthorizedRequestJSOCode_1500:    false,
			EnumEpayPayInCode_AuthorizedRequestJSOCode_1502:    false,
			EnumEpayPayInCode_AuthorizedRequestJSOCode_1503:    false,
			EnumEpayPayInCode_AuthorizedRequestJSOCode_1504:    false,
			EnumEpayPayInCode_TerminaluuidCode_1505:            false,
			EnumEpayPayInCode_BindgrafanaCode_1506:             false,
			EnumEpayPayInCode_HttpgrafanaCode_1507:             false,
			EnumEpayPayInCode_UnauthorizedCode_1508:            false,
			EnumEpayPayInCode_BadtokenCode_1509:                false,
			EnumEpayPayInCode_JsonUnmarshalCode_1510:           false,
			EnumEpayPayInCode_JsonMarshalCode_1511:             false,
			EnumEpayPayInCode_RequestJSONgeoIPCode_1512:        false,
			EnumEpayPayInCode_Code_1513:                        false,
			EnumEpayPayInCode_JsonUnmarshalCode_1514:           false,
			EnumEpayPayInCode_JsonMarshalCode_1515:             false,
			EnumEpayPayInCode_Code_1516:                        false,
			EnumEpayPayInCode_QrTransactionInfoCode_1517:       false,
			EnumEpayPayInCode_CardTypeIDCode_1518:              false,
			EnumEpayPayInCode_CurrencyIDCode_1519:              false,
			EnumEpayPayInCode_StatusIDCode_1520:                false,
			EnumEpayPayInCode_Code_1521:                        false,
			EnumEpayPayInCode_Code_1522:                        false,
			EnumEpayPayInCode_Code_1523:                        false,
			EnumEpayPayInCode_Code_1524:                        false,
			EnumEpayPayInCode_ShopIDmerchantIDclieCode_1525:    false,
			EnumEpayPayInCode_Code_1526:                        false,
			EnumEpayPayInCode_QRCode_1527:                      false,
			EnumEpayPayInCode_SourceListIDCode_1528:            false,
			EnumEpayPayInCode_AuthorizedRequestJSOCode_1529:    false,
			EnumEpayPayInCode_XLSIDCode_1530:                   false,
			EnumEpayPayInCode_JSONJSONCode_1531:                false,
			EnumEpayPayInCode_RedispartialTransactCode_1532:    false,
			EnumEpayPayInCode_Code_1533:                        false,
			EnumEpayPayInCode_Code_1534:                        false,
			EnumEpayPayInCode_Code_1535:                        false,
			EnumEpayPayInCode_Code_1536:                        false,
			EnumEpayPayInCode_Code_1537:                        false,
			EnumEpayPayInCode_RedisinvoiceIdCode_1538:          false,
			EnumEpayPayInCode_Epay1Code_1539:                   false,
			EnumEpayPayInCode_Epay1Code_1540:                   false,
			EnumEpayPayInCode_Epay1Code_1541:                   false,
			EnumEpayPayInCode_Epay1Code_1542:                   false,
			EnumEpayPayInCode_TransactionTypeIDCode_1543:       false,
			EnumEpayPayInCode_ClientTypeIDCode_1544:            false,
			EnumEpayPayInCode_JsonMarshalucsCode_1545:          false,
			EnumEpayPayInCode_Code_1546:                        false,
			EnumEpayPayInCode_ScopeCode_1547:                   false,
			EnumEpayPayInCode_RedisqrstatusCode_1548:           false,
			EnumEpayPayInCode_AuthorizedRequestJSOCode_1549:    false,
			EnumEpayPayInCode_Code_1550:                        false,
			EnumEpayPayInCode_RedisrealIPCode_1551:             false,
			EnumEpayPayInCode_RequestJSONcoreqrQRSCode_1552:    false,
			EnumEpayPayInCode_KafkaReadMessageCode_1553:        false,
			EnumEpayPayInCode_JsonUnmarshalkafkameCode_1554:    false,
			EnumEpayPayInCode_JSONjsonMarshalJSONCode_1555:     false,
			EnumEpayPayInCode_KafkaCode_1556:                   false,
			EnumEpayPayInCode_SetOffsetkafkaCode_1557:          false,
			EnumEpayPayInCode_Code_1558:                        false,
			EnumEpayPayInCode_Code_1559:                        false,
			EnumEpayPayInCode_Code_1560:                        false,
			EnumEpayPayInCode_Code_1561:                        false,
			EnumEpayPayInCode_HTTPCode_1562:                    false,
			EnumEpayPayInCode_HTTPCode_1563:                    false,
			EnumEpayPayInCode_HTTPCode_1564:                    false,
			EnumEpayPayInCode_HTTPCode_1565:                    false,
			EnumEpayPayInCode_AUTHCode_1566:                    false,
			EnumEpayPayInCode_EGateWayCode_1567:                false,
			EnumEpayPayInCode_EGateWayCode_1568:                false,
			EnumEpayPayInCode_Code_1569:                        false,
			EnumEpayPayInCode_JsonCode_1570:                    false,
			EnumEpayPayInCode_Code_1571:                        false,
			EnumEpayPayInCode_Code_1572:                        false,
			EnumEpayPayInCode_Code_1578:                        false,
			EnumEpayPayInCode_Code_1579:                        false,
			EnumEpayPayInCode_ReadAllfromRequestBoCode_1604:    false,
			EnumEpayPayInCode_ParseFloatrefundCode_1605:        false,
			EnumEpayPayInCode_AmountbodyurlrefundCode_1606:     false,
			EnumEpayPayInCode_Code_1607:                        false,
			EnumEpayPayInCode_ChargeCode_1608:                  false,
			EnumEpayPayInCode_CancelCode_1609:                  false,
			EnumEpayPayInCode_RefundCode_1610:                  false,
			EnumEpayPayInCode_HttpCode_1611:                    false,
			EnumEpayPayInCode_Code_1612:                        false,
			EnumEpayPayInCode_Code_1614:                        false,
			EnumEpayPayInCode_TerminalIDCode_1615:              false,
			EnumEpayPayInCode_Code_1616:                        false,
			EnumEpayPayInCode_JsonUnmarshalbinCode_1617:        false,
			EnumEpayPayInCode_JsonMarshalCode_1618:             false,
			EnumEpayPayInCode_Code_1619:                        false,
			EnumEpayPayInCode_JSONapiosuvoxCode_1620:           false,
			EnumEpayPayInCode_ApiosuvoxCode_1621:               false,
			EnumEpayPayInCode_ApiosuvoxHTTPCODE200Code_1622:    false,
			EnumEpayPayInCode_Code_1623:                        false,
			EnumEpayPayInCode_SourceListCode_1624:              false,
			EnumEpayPayInCode_Code_1636:                        false,
			EnumEpayPayInCode_Code_1637:                        false,
			EnumEpayPayInCode_OsuvoxCode_1638:                  false,
			EnumEpayPayInCode_InvalidCardIDCode_1639:           false,
			EnumEpayPayInCode_OsuvoxCode_1640:                  false,
			EnumEpayPayInCode_Code_1641:                        false,
			EnumEpayPayInCode_OsuvoxCode_1642:                  false,
			EnumEpayPayInCode_SingleMessageSchemeCode_1650:     false,
			EnumEpayPayInCode_Code_1651:                        false,
			EnumEpayPayInCode_OTPCode_1652:                     false,
			EnumEpayPayInCode_OTPCode_1653:                     false,
			EnumEpayPayInCode_OTPcodeCode_1654:                 false,
			EnumEpayPayInCode_OTPcodeCode_1655:                 false,
			EnumEpayPayInCode_RedisterminaluuidinvCode_1656:    false,
			EnumEpayPayInCode_InvalidterminalIdCode_1658:       false,
			EnumEpayPayInCode_InvalidAmoutCode_1659:            false,
			EnumEpayPayInCode_Code_1660:                        false,
			EnumEpayPayInCode_Code_1661:                        false,
			EnumEpayPayInCode_AuthorizedRequestJSOCode_1663:    false,
			EnumEpayPayInCode_BindGetTokenByCardCode_1664:      false,
			EnumEpayPayInCode_RequestPostFormXMLGeCode_1665:    false,
			EnumEpayPayInCode_RequestPostFormXMLGeCode_1666:    false,
			EnumEpayPayInCode_RequestPostFormXMLGeCode_1667:    false,
			EnumEpayPayInCode_RequestPostFormXMLGeCode_1668:    false,
			EnumEpayPayInCode_Codebase64stringbaseCode_1670:    false,
			EnumEpayPayInCode_AuthorizedRequestJSOCode_1671:    false,
			EnumEpayPayInCode_CardCode_1672:                    false,
			EnumEpayPayInCode_HomebankPayOsuvoxCarCode_1673:    false,
			EnumEpayPayInCode_OsuvoxCode_1675:                  false,
			EnumEpayPayInCode_OsuvoxCode_1677:                  false,
			EnumEpayPayInCode_OsuvoxCode_1679:                  false,
			EnumEpayPayInCode_OsuvoxCode_1680:                  false,
			EnumEpayPayInCode_OsuvoxCode_1681:                  false,
			EnumEpayPayInCode_Code_1682:                        false,
			EnumEpayPayInCode_OsuvoxCode_1683:                  false,
			EnumEpayPayInCode_OsuvoxCode_1685:                  false,
			EnumEpayPayInCode_TransactionCode_1690:             false,
			EnumEpayPayInCode_CouldnotgetphoneNumbCode_1692:    false,
			EnumEpayPayInCode_Code_1693:                        false,
			EnumEpayPayInCode_Code_1694:                        false,
			EnumEpayPayInCode_OsuvoxCode_1695:                  false,
			EnumEpayPayInCode_XMLbodyCode_1699:                 false,
			EnumEpayPayInCode_XMLMarhsalswitchpaymCode_1700:    false,
			EnumEpayPayInCode_TerminalIdCode_1703:              false,
			EnumEpayPayInCode_TerminalXLSMIDterminCode_1704:    false,
			EnumEpayPayInCode_IbanCode_1705:                    false,
			EnumEpayPayInCode_IbanCode_1706:                    false,
			EnumEpayPayInCode_TerminalIdCode_1707:              false,
			EnumEpayPayInCode_IbanCode_1708:                    false,
			EnumEpayPayInCode_Code_1709:                        false,
			EnumEpayPayInCode_Code_1710:                        false,
			EnumEpayPayInCode_Code_1711:                        false,
			EnumEpayPayInCode_Code_1712:                        false,
			EnumEpayPayInCode_InvoiceIdCode_1713:               false,
			EnumEpayPayInCode_Code_1714:                        false,
			EnumEpayPayInCode_AUTHCode_1715:                    false,
			EnumEpayPayInCode_PaymentTypeCode_1716:             false,
			EnumEpayPayInCode_OsuvoxCode_1717:                  false,
			EnumEpayPayInCode_PaymentTypeCode_1719:             false,
			EnumEpayPayInCode_PaymentSystemCode_1720:           false,
			EnumEpayPayInCode_Code_1723:                        false,
			EnumEpayPayInCode_Code_1724:                        false,
			EnumEpayPayInCode_Code_1759:                        false,
			EnumEpayPayInCode_Code_1760:                        false,
			EnumEpayPayInCode_Code_1761:                        false,
			EnumEpayPayInCode_Code_1762:                        false,
			EnumEpayPayInCode_ShopIdCode_1763:                  false,
			EnumEpayPayInCode_Code_1764:                        false,
			EnumEpayPayInCode_OrderIdCode_1765:                 false,
			EnumEpayPayInCode_CoreMerchantCode_1771:            false,
			EnumEpayPayInCode_PostlinkcorecallbackCode_1780:    false,
			EnumEpayPayInCode_CardTypenilCode_1781:             false,
			EnumEpayPayInCode_SwitchpaymentCode_1786:           false,
			EnumEpayPayInCode_Code_1787:                        false,
			EnumEpayPayInCode_HttpGetMerchantCertiCode_1788:    false,
			EnumEpayPayInCode_HttpGetMerchantCreatCode_1789:    false,
			EnumEpayPayInCode_EnabledCode_1849:                 false,
			EnumEpayPayInCode_ShopCode_1850:                    false,
			EnumEpayPayInCode_Code_1855:                        false,
			EnumEpayPayInCode_TerminalIDCode_1856:              false,
			EnumEpayPayInCode_ShopIDCode_1857:                  false,
			EnumEpayPayInCode_IDCode_1858:                      false,
			EnumEpayPayInCode_ShopidCode_1859:                  false,
			EnumEpayPayInCode_TokenExpireInSecondsCode_1860:    false,
			EnumEpayPayInCode_Apiepay1Code_1899:                false,
			EnumEpayPayInCode_RequestCode_1900:                 false,
			EnumEpayPayInCode_Code_1901:                        false,
			EnumEpayPayInCode_Code_1902:                        false,
			EnumEpayPayInCode_JSONCode_1903:                    false,
			EnumEpayPayInCode_IdserviceCode_1944:               false,
			EnumEpayPayInCode_UpdateMerchantContacCode_1945:    false,
			EnumEpayPayInCode_ContactTypenameserviCode_1946:    false,
			EnumEpayPayInCode_Code_1954:                        false,
			EnumEpayPayInCode_Code_1955:                        false,
			EnumEpayPayInCode_Code_1956:                        false,
			EnumEpayPayInCode_Code_1961:                        false,
			EnumEpayPayInCode_Code_1962:                        false,
			EnumEpayPayInCode_Code_1964:                        false,
			EnumEpayPayInCode_TerminalshopNameCode_1965:        false,
			EnumEpayPayInCode_Code_1970:                        false,
			EnumEpayPayInCode_InvoiceIDCode_1971:               false,
			EnumEpayPayInCode_Amount0Code_1975:                 false,
			EnumEpayPayInCode_Code_1976:                        false,
			EnumEpayPayInCode_Code_1977:                        false,
			EnumEpayPayInCode_TokenexpiredtryagainCode_1979:    false,
			EnumEpayPayInCode_MPANCode_1980:                    false,
			EnumEpayPayInCode_Code_2008:                        false,
			EnumEpayPayInCode_RecordnotfoundCode_2009:          false,
			EnumEpayPayInCode_Code_2015:                        false,
			EnumEpayPayInCode_Code_2016:                        false,
			EnumEpayPayInCode_RedismaxReqPerSecCode_2017:       false,
			EnumEpayPayInCode_Code_2018:                        false,
			EnumEpayPayInCode_GooglePayCode_2028:               false,
			EnumEpayPayInCode_GooglePayCode_2030:               false,
			EnumEpayPayInCode_AccountIDCode_2031:               false,
			EnumEpayPayInCode_CallbackCode_2032:                false,
			EnumEpayPayInCode_Code_2033:                        false,
			EnumEpayPayInCode_Code_2046:                        false,
			EnumEpayPayInCode_JSONCode_2048:                    false,
			EnumEpayPayInCode_Code_2049:                        false,
			EnumEpayPayInCode_Code_2050:                        false,
			EnumEpayPayInCode_Code_2051:                        false,
			EnumEpayPayInCode_XLSURLCode_2052:                  false,
			EnumEpayPayInCode_Code_2053:                        false,
			EnumEpayPayInCode_JSONUpdateProfileStaCode_2057:    false,
			EnumEpayPayInCode_JSONCreateStaffProfiCode_2058:    false,
			EnumEpayPayInCode_Code_2059:                        false,
			EnumEpayPayInCode_Code_2060:                        false,
			EnumEpayPayInCode_InvalidamountamountmCode_2061:    false,
			EnumEpayPayInCode_CorebusinessreportCode_2067:      false,
			EnumEpayPayInCode_Code_2069:                        false,
			EnumEpayPayInCode_EmailCode_2070:                   false,
			EnumEpayPayInCode_GinBindCode_2074:                 false,
			EnumEpayPayInCode_XLSTIDXLSMIDCode_2075:            false,
			EnumEpayPayInCode_Code_2076:                        false,
			EnumEpayPayInCode_MPGSCode_2085:                    false,
			EnumEpayPayInCode_MPGSCode_2086:                    false,
			EnumEpayPayInCode_MPGSCode_2087:                    false,
			EnumEpayPayInCode_MPGSCode_2088:                    false,
			EnumEpayPayInCode_MPGSCode_2089:                    false,
			EnumEpayPayInCode_MPGSCode_2090:                    false,
			EnumEpayPayInCode_PANBINCode_2091:                  false,
			EnumEpayPayInCode_BINorPANrestrictedCode_2092:      false,
			EnumEpayPayInCode_BindCode_2122:                    false,
			EnumEpayPayInCode_Code_2123:                        false,
			EnumEpayPayInCode_Code_2124:                        false,
			EnumEpayPayInCode_MerchantCode_2125:                false,
			EnumEpayPayInCode_MerchantCode_2126:                false,
			EnumEpayPayInCode_Code_2127:                        false,
			EnumEpayPayInCode_Code_2128:                        false,
			EnumEpayPayInCode_Code_2129:                        false,
			EnumEpayPayInCode_OpenwayIDCode_2130:               false,
			EnumEpayPayInCode_USDCode_2131:                     false,
			EnumEpayPayInCode_Code_2132:                        false,
			EnumEpayPayInCode_Code_2133:                        false,
			EnumEpayPayInCode_Code_2134:                        false,
			EnumEpayPayInCode_Code_2135:                        false,
			EnumEpayPayInCode_XlsCode_2136:                     false,
			EnumEpayPayInCode_Code_2137:                        false,
			EnumEpayPayInCode_PostlinkcorecallbackCode_2138:    false,
			EnumEpayPayInCode_Code_2139:                        false,
			EnumEpayPayInCode_CryptogramopenwayIDCode_2146:     false,
			EnumEpayPayInCode_IPCode_2147:                      false,
			EnumEpayPayInCode_Code_2148:                        false,
			EnumEpayPayInCode_FormdatasftpproxyCode_2152:       false,
			EnumEpayPayInCode_HttpNewRequestsftpprCode_2153:    false,
			EnumEpayPayInCode_RedispipelineCode_2154:           false,
			EnumEpayPayInCode_TransactionamountshoCode_2156:    false,
			EnumEpayPayInCode_Code_2157:                        false,
			EnumEpayPayInCode_URLpagesizeCode_2158:             false,
			EnumEpayPayInCode_Code_2159:                        false,
			EnumEpayPayInCode_URLpagesizeCode_2191:             false,
			EnumEpayPayInCode_PagesizeCode_2193:                false,
			EnumEpayPayInCode_PagesizeCode_2194:                false,
			EnumEpayPayInCode_Code_2200:                        false,
			EnumEpayPayInCode_Code_2203:                        false,
			EnumEpayPayInCode_Code_2204:                        false,
			EnumEpayPayInCode_Code_2205:                        false,
			EnumEpayPayInCode_UnmarshalCode_2206:               false,
			EnumEpayPayInCode_Code_2207:                        false,
			EnumEpayPayInCode_Code_2210:                        false,
			EnumEpayPayInCode_PublicIDonboardingCode_2211:      false,
			EnumEpayPayInCode_AclserviceCode_2212:              false,
			EnumEpayPayInCode_StaffCode_2213:                   false,
			EnumEpayPayInCode_OpenWayCardIdCode_2214:           false,
			EnumEpayPayInCode_Code_2218:                        false,
			EnumEpayPayInCode_Code_2237:                        false,
			EnumEpayPayInCode_PostlinkaCode_2238:               false,
			EnumEpayPayInCode_PostlinkaCode_2239:               false,
			EnumEpayPayInCode_PostlinkaCode_2240:               false,
			EnumEpayPayInCode_PostlinkaCode_2241:               false,
			EnumEpayPayInCode_PostlinkCode_2242:                false,
			EnumEpayPayInCode_Code_2243:                        false,
			EnumEpayPayInCode_Code_2244:                        false,
			EnumEpayPayInCode_Code_2245:                        false,
			EnumEpayPayInCode_Code_2249:                        false,
			EnumEpayPayInCode_P2PCode_2250:                     false,
			EnumEpayPayInCode_Code_2257:                        false,
			EnumEpayPayInCode_JSONjsonMarshalJSONCode_2268:     false,
			EnumEpayPayInCode_Code_2271:                        false,
			EnumEpayPayInCode_Code_2301:                        false,
			EnumEpayPayInCode_Code_2305:                        false,
			EnumEpayPayInCode_QrbyqrCode_2322:                  false,
			EnumEpayPayInCode_Code_2323:                        false,
			EnumEpayPayInCode_Code_2326:                        false,
			EnumEpayPayInCode_CardIDCode_2339:                  false,
			EnumEpayPayInCode_Code_2349:                        false,
			EnumEpayPayInCode_Code_2350:                        false,
			EnumEpayPayInCode_Code_2351:                        false,
			EnumEpayPayInCode_Code_2352:                        false,
			EnumEpayPayInCode_Code_2353:                        false,
			EnumEpayPayInCode_Code_2354:                        false,
			EnumEpayPayInCode_ClientIDCode_2355:                false,
			EnumEpayPayInCode_Code_2356:                        false,
			EnumEpayPayInCode_ResultCodestatusIDCode_2360:      false,
			EnumEpayPayInCode_PaymentsystemCode_2362:           false,
			EnumEpayPayInCode_KafkaCode_2365:                   false,
			EnumEpayPayInCode_ShopInfoCode_2366:                false,
			EnumEpayPayInCode_Code_2367:                        false,
			EnumEpayPayInCode_Code_2433:                        false,
			EnumEpayPayInCode_UZGWCode_2435:                    false,
			EnumEpayPayInCode_Code_2624:                        false,
			EnumEpayPayInCode_ChecktransactionstatCode_2660:    false,
			EnumEpayPayInCode_CorePaymentCode_2678:             false,
			EnumEpayPayInCode_CorePaymentCode_2679:             false,
			EnumEpayPayInCode_OtpcodeapiuzgatewayCode_2704:     false,
			EnumEpayPayInCode_CodeapiuzgatewayCode_2705:        false,
			EnumEpayPayInCode_CannotapproveatthistCode_2740:    false,
			EnumEpayPayInCode_NotsufficientfundsCode_484:       false,
			EnumEpayPayInCode_TheoperationfailedplCode_454:     false,
			EnumEpayPayInCode_ServerisnotrespondinCode_459:     false,
			EnumEpayPayInCode_DonotreattemptrestriCode_2872:    false,
		},
	}
}

func IsEnumEpayPayInCode(target EnumEpayPayInCode, matches ...EnumEpayPayInCode) bool {
	for _, v := range matches {
		if target == v {
			return true
		}
	}

	return false
}

type EnumEpayPayInCodeUsage struct {
	enumMap map[EnumEpayPayInCode]bool
}

func (u *EnumEpayPayInCodeUsage) Use(slice ...EnumEpayPayInCode) *EnumEpayPayInCodeUsage {
	for _, in := range slice {
		u.enumMap[in] = true
	}

	return u
}

func (u *EnumEpayPayInCodeUsage) Check() []string {
	var result []string
	for k, v := range u.enumMap {
		if v {
			continue
		}

		result = append(result, k.String())
	}

	return result
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Empty_Response_Code() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Empty_Response_Code)
	return EnumEpayPayInCode_Empty_Response_Code
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_ClientauthenticationCode_33() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_ClientauthenticationCode_33)
	return EnumEpayPayInCode_ClientauthenticationCode_33
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_SystemerrorpleasetryCode_50() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_SystemerrorpleasetryCode_50)
	return EnumEpayPayInCode_SystemerrorpleasetryCode_50
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_ErrorinCVC2orCVC2DesCode_18() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_ErrorinCVC2orCVC2DesCode_18)
	return EnumEpayPayInCode_ErrorinCVC2orCVC2DesCode_18
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_InvalidRetrievalrefeCode_15() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_InvalidRetrievalrefeCode_15)
	return EnumEpayPayInCode_InvalidRetrievalrefeCode_15
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_ThreeDSecurecheckfailedCode_455() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_ThreeDSecurecheckfailedCode_455)
	return EnumEpayPayInCode_ThreeDSecurecheckfailedCode_455
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_AccessdeniedCode_456() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_AccessdeniedCode_456)
	return EnumEpayPayInCode_AccessdeniedCode_456
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_ErrorincardexpiratioCode_457() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_ErrorincardexpiratioCode_457)
	return EnumEpayPayInCode_ErrorincardexpiratioCode_457
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_ServerisnotrespondinCode_458() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_ServerisnotrespondinCode_458)
	return EnumEpayPayInCode_ServerisnotrespondinCode_458
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_NoorinvalidresponcerCode_460() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_NoorinvalidresponcerCode_460)
	return EnumEpayPayInCode_NoorinvalidresponcerCode_460
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_BadCGIrequestCode_461() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_BadCGIrequestCode_461)
	return EnumEpayPayInCode_BadCGIrequestCode_461
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_CallyourbankCode_462() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_CallyourbankCode_462)
	return EnumEpayPayInCode_CallyourbankCode_462
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_CallyourbankCode_463() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_CallyourbankCode_463)
	return EnumEpayPayInCode_CallyourbankCode_463
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_InvalidmerchantCode_464() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_InvalidmerchantCode_464)
	return EnumEpayPayInCode_InvalidmerchantCode_464
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_YourcardisrestrictedCode_465() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_YourcardisrestrictedCode_465)
	return EnumEpayPayInCode_YourcardisrestrictedCode_465
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_NotpermittedtoclientCode_466() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_NotpermittedtoclientCode_466)
	return EnumEpayPayInCode_NotpermittedtoclientCode_466
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_YourcardisdisabledCode_467() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_YourcardisdisabledCode_467)
	return EnumEpayPayInCode_YourcardisdisabledCode_467
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_AdditionalidentificaCode_468() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_AdditionalidentificaCode_468)
	return EnumEpayPayInCode_AdditionalidentificaCode_468
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_InvalidtransactionCode_469() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_InvalidtransactionCode_469)
	return EnumEpayPayInCode_InvalidtransactionCode_469
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_InvalidamountCode_470() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_InvalidamountCode_470)
	return EnumEpayPayInCode_InvalidamountCode_470
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_NosuchcardCode_471() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_NosuchcardCode_471)
	return EnumEpayPayInCode_NosuchcardCode_471
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_NosuchcardCode_472() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_NosuchcardCode_472)
	return EnumEpayPayInCode_NosuchcardCode_472
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_AuthenticationfailedCode_473() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_AuthenticationfailedCode_473)
	return EnumEpayPayInCode_AuthenticationfailedCode_473
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_InvalidresponseCode_475() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_InvalidresponseCode_475)
	return EnumEpayPayInCode_InvalidresponseCode_475
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_NoactiontakenCode_476() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_NoactiontakenCode_476)
	return EnumEpayPayInCode_NoactiontakenCode_476
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_FormaterrorCode_477() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_FormaterrorCode_477)
	return EnumEpayPayInCode_FormaterrorCode_477
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_ExpiredcardCode_478() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_ExpiredcardCode_478)
	return EnumEpayPayInCode_ExpiredcardCode_478
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_RestrictedcardCode_479() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_RestrictedcardCode_479)
	return EnumEpayPayInCode_RestrictedcardCode_479
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_CallyourbankCode_480() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_CallyourbankCode_480)
	return EnumEpayPayInCode_CallyourbankCode_480
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_LostcardCode_481() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_LostcardCode_481)
	return EnumEpayPayInCode_LostcardCode_481
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_LostcardCode_482() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_LostcardCode_482)
	return EnumEpayPayInCode_LostcardCode_482
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_StolencardCode_483() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_StolencardCode_483)
	return EnumEpayPayInCode_StolencardCode_483
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_ExpiredcardCode_485() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_ExpiredcardCode_485)
	return EnumEpayPayInCode_ExpiredcardCode_485
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_NotpermittedtoclientCode_486() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_NotpermittedtoclientCode_486)
	return EnumEpayPayInCode_NotpermittedtoclientCode_486
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_NotpermittedtomerchaCode_487() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_NotpermittedtomerchaCode_487)
	return EnumEpayPayInCode_NotpermittedtomerchaCode_487
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_ExceedsamountlimitCode_488() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_ExceedsamountlimitCode_488)
	return EnumEpayPayInCode_ExceedsamountlimitCode_488
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_RestrictedcardCode_489() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_RestrictedcardCode_489)
	return EnumEpayPayInCode_RestrictedcardCode_489
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_InvalidContractcardCode_490() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_InvalidContractcardCode_490)
	return EnumEpayPayInCode_InvalidContractcardCode_490
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_ExceedsfrequencylimiCode_491() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_ExceedsfrequencylimiCode_491)
	return EnumEpayPayInCode_ExceedsfrequencylimiCode_491
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_PINtriesexceededCode_492() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_PINtriesexceededCode_492)
	return EnumEpayPayInCode_PINtriesexceededCode_492
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_TimeoutatissuersysteCode_493() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_TimeoutatissuersysteCode_493)
	return EnumEpayPayInCode_TimeoutatissuersysteCode_493
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_IssuerunavailableCode_494() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_IssuerunavailableCode_494)
	return EnumEpayPayInCode_IssuerunavailableCode_494
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_CannotbecompletedvioCode_495() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_CannotbecompletedvioCode_495)
	return EnumEpayPayInCode_CannotbecompletedvioCode_495
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_ThreeDSecureSecureCodeabCode_496() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_ThreeDSecureSecureCodeabCode_496)
	return EnumEpayPayInCode_ThreeDSecureSecureCodeabCode_496
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_ServerisnotrespondinCode_497() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_ServerisnotrespondinCode_497)
	return EnumEpayPayInCode_ServerisnotrespondinCode_497
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_ThreeDSecurecheckfailedCode_499() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_ThreeDSecurecheckfailedCode_499)
	return EnumEpayPayInCode_ThreeDSecurecheckfailedCode_499
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_ThreeDSecurecheckfailedCode_500() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_ThreeDSecurecheckfailedCode_500)
	return EnumEpayPayInCode_ThreeDSecurecheckfailedCode_500
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_CardcheckfailedCode_501() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_CardcheckfailedCode_501)
	return EnumEpayPayInCode_CardcheckfailedCode_501
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_ThreeDSecureSecureCodeabCode_502() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_ThreeDSecureSecureCodeabCode_502)
	return EnumEpayPayInCode_ThreeDSecureSecureCodeabCode_502
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_ThreeDSecureSecureCodeabCode_503() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_ThreeDSecureSecureCodeabCode_503)
	return EnumEpayPayInCode_ThreeDSecureSecureCodeabCode_503
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_TransactiondeclinedCCode_521() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_TransactiondeclinedCCode_521)
	return EnumEpayPayInCode_TransactiondeclinedCCode_521
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_RecordNotFoundchecktCode_522() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_RecordNotFoundchecktCode_522)
	return EnumEpayPayInCode_RecordNotFoundchecktCode_522
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_TransactiondeclinedCCode_523() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_TransactiondeclinedCCode_523)
	return EnumEpayPayInCode_TransactiondeclinedCCode_523
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_YourcardisrestrictedCode_524() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_YourcardisrestrictedCode_524)
	return EnumEpayPayInCode_YourcardisrestrictedCode_524
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_YourcardisrestrictedCode_525() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_YourcardisrestrictedCode_525)
	return EnumEpayPayInCode_YourcardisrestrictedCode_525
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_SystemerrorPleasetryCode_526() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_SystemerrorPleasetryCode_526)
	return EnumEpayPayInCode_SystemerrorPleasetryCode_526
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_TransactiondeclinedCCode_527() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_TransactiondeclinedCCode_527)
	return EnumEpayPayInCode_TransactiondeclinedCCode_527
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_ThedailylimitofincomCode_528() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_ThedailylimitofincomCode_528)
	return EnumEpayPayInCode_ThedailylimitofincomCode_528
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_TransactiondeclinedCCode_531() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_TransactiondeclinedCCode_531)
	return EnumEpayPayInCode_TransactiondeclinedCCode_531
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_UnabletoverifyPINcalCode_532() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_UnabletoverifyPINcalCode_532)
	return EnumEpayPayInCode_UnabletoverifyPINcalCode_532
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_AuthenticationfailedCode_19() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_AuthenticationfailedCode_19)
	return EnumEpayPayInCode_AuthenticationfailedCode_19
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_ErrorincardexpiratioCode_9() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_ErrorincardexpiratioCode_9)
	return EnumEpayPayInCode_ErrorincardexpiratioCode_9
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_ServerisnotrespondinCode_8() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_ServerisnotrespondinCode_8)
	return EnumEpayPayInCode_ServerisnotrespondinCode_8
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_ServerisnotrespondinCode_4() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_ServerisnotrespondinCode_4)
	return EnumEpayPayInCode_ServerisnotrespondinCode_4
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_ErrorBindActionCreatCode_1() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_ErrorBindActionCreatCode_1)
	return EnumEpayPayInCode_ErrorBindActionCreatCode_1
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_ErrorcreatingrecordiCode_2() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_ErrorcreatingrecordiCode_2)
	return EnumEpayPayInCode_ErrorcreatingrecordiCode_2
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_ErrorBindActionUpdatCode_3() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_ErrorBindActionUpdatCode_3)
	return EnumEpayPayInCode_ErrorBindActionUpdatCode_3
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_ErrorwhileupdatingreCode_4() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_ErrorwhileupdatingreCode_4)
	return EnumEpayPayInCode_ErrorwhileupdatingreCode_4
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_ErrorwhiledeletingthCode_5() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_ErrorwhiledeletingthCode_5)
	return EnumEpayPayInCode_ErrorwhiledeletingthCode_5
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_ErrorGetByNameActionCode_6() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_ErrorGetByNameActionCode_6)
	return EnumEpayPayInCode_ErrorGetByNameActionCode_6
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_ErrorGetByIDActionGeCode_7() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_ErrorGetByIDActionGeCode_7)
	return EnumEpayPayInCode_ErrorGetByIDActionGeCode_7
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_ErrorBindActionCreatCode_8() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_ErrorBindActionCreatCode_8)
	return EnumEpayPayInCode_ErrorBindActionCreatCode_8
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_ErrorcreatingrecordiCode_9() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_ErrorcreatingrecordiCode_9)
	return EnumEpayPayInCode_ErrorcreatingrecordiCode_9
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_RedisActionCreateErrCode_10() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_RedisActionCreateErrCode_10)
	return EnumEpayPayInCode_RedisActionCreateErrCode_10
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_ErrorBindActionUpdatCode_11() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_ErrorBindActionUpdatCode_11)
	return EnumEpayPayInCode_ErrorBindActionUpdatCode_11
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_ErrorwhileupdatingreCode_12() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_ErrorwhileupdatingreCode_12)
	return EnumEpayPayInCode_ErrorwhileupdatingreCode_12
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_RedisActionUpdateErrCode_13() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_RedisActionUpdateErrCode_13)
	return EnumEpayPayInCode_RedisActionUpdateErrCode_13
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_ErrorwhiledeletingthCode_14() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_ErrorwhiledeletingthCode_14)
	return EnumEpayPayInCode_ErrorwhiledeletingthCode_14
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_RedisActionDeleteErrCode_15() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_RedisActionDeleteErrCode_15)
	return EnumEpayPayInCode_RedisActionDeleteErrCode_15
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_ErrorGetByCodeinPostCode_16() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_ErrorGetByCodeinPostCode_16)
	return EnumEpayPayInCode_ErrorGetByCodeinPostCode_16
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_CodeActionGetErrorByCode_17() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_CodeActionGetErrorByCode_17)
	return EnumEpayPayInCode_CodeActionGetErrorByCode_17
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_IDApplicationIDAppliCode_18() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_IDApplicationIDAppliCode_18)
	return EnumEpayPayInCode_IDApplicationIDAppliCode_18
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_CodeCode_19() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_CodeCode_19)
	return EnumEpayPayInCode_CodeCode_19
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_IDApplicationIDAppliCode_20() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_IDApplicationIDAppliCode_20)
	return EnumEpayPayInCode_IDApplicationIDAppliCode_20
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_IDApplicationApplicaCode_21() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_IDApplicationApplicaCode_21)
	return EnumEpayPayInCode_IDApplicationApplicaCode_21
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_22() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_22)
	return EnumEpayPayInCode_Code_22
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_SuccessfullyCode_23() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_SuccessfullyCode_23)
	return EnumEpayPayInCode_SuccessfullyCode_23
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Test111Code_24() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Test111Code_24)
	return EnumEpayPayInCode_Test111Code_24
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_InvoiceidCode_25() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_InvoiceidCode_25)
	return EnumEpayPayInCode_InvoiceidCode_25
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_BindJSONBindJSONCode_27() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_BindJSONBindJSONCode_27)
	return EnumEpayPayInCode_BindJSONBindJSONCode_27
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_TransactionalreadyprCode_28() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_TransactionalreadyprCode_28)
	return EnumEpayPayInCode_TransactionalreadyprCode_28
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_TransactionalreadyprCode_29() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_TransactionalreadyprCode_29)
	return EnumEpayPayInCode_TransactionalreadyprCode_29
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_ErrorGetShopsByMerchCode_54() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_ErrorGetShopsByMerchCode_54)
	return EnumEpayPayInCode_ErrorGetShopsByMerchCode_54
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_IdCode_55() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_IdCode_55)
	return EnumEpayPayInCode_IdCode_55
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_ErrorGetShopByIDCode_56() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_ErrorGetShopByIDCode_56)
	return EnumEpayPayInCode_ErrorGetShopByIDCode_56
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_ErrorBindCode_57() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_ErrorBindCode_57)
	return EnumEpayPayInCode_ErrorBindCode_57
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_ErrorBindCode_58() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_ErrorBindCode_58)
	return EnumEpayPayInCode_ErrorBindCode_58
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_ErrorCreateShopCode_59() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_ErrorCreateShopCode_59)
	return EnumEpayPayInCode_ErrorCreateShopCode_59
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_ErrorGetMerchantByIDCode_60() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_ErrorGetMerchantByIDCode_60)
	return EnumEpayPayInCode_ErrorGetMerchantByIDCode_60
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_ErrorGetStatusByNameCode_61() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_ErrorGetStatusByNameCode_61)
	return EnumEpayPayInCode_ErrorGetStatusByNameCode_61
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_ErrorCreateDefaultCoCode_64() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_ErrorCreateDefaultCoCode_64)
	return EnumEpayPayInCode_ErrorCreateDefaultCoCode_64
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_ErrorCreateMerchantCode_65() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_ErrorCreateMerchantCode_65)
	return EnumEpayPayInCode_ErrorCreateMerchantCode_65
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_ErrorVerificationSerCode_71() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_ErrorVerificationSerCode_71)
	return EnumEpayPayInCode_ErrorVerificationSerCode_71
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_ErrorCheckMerchantCode_72() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_ErrorCheckMerchantCode_72)
	return EnumEpayPayInCode_ErrorCheckMerchantCode_72
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_ErrorSendVerificatioCode_73() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_ErrorSendVerificatioCode_73)
	return EnumEpayPayInCode_ErrorSendVerificatioCode_73
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_ErrorVerificationSerCode_74() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_ErrorVerificationSerCode_74)
	return EnumEpayPayInCode_ErrorVerificationSerCode_74
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_ErrorFindMerchantByECode_75() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_ErrorFindMerchantByECode_75)
	return EnumEpayPayInCode_ErrorFindMerchantByECode_75
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_ErrorUpdateMerchantCode_76() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_ErrorUpdateMerchantCode_76)
	return EnumEpayPayInCode_ErrorUpdateMerchantCode_76
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_ErrorRegistrationUseCode_77() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_ErrorRegistrationUseCode_77)
	return EnumEpayPayInCode_ErrorRegistrationUseCode_77
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_RrnCode_83() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_RrnCode_83)
	return EnumEpayPayInCode_RrnCode_83
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_ErrorGetCardTypebyRRCode_84() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_ErrorGetCardTypebyRRCode_84)
	return EnumEpayPayInCode_ErrorGetCardTypebyRRCode_84
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_ErrorCheckTransactioCode_85() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_ErrorCheckTransactioCode_85)
	return EnumEpayPayInCode_ErrorCheckTransactioCode_85
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_UnauthorizedCode_86() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_UnauthorizedCode_86)
	return EnumEpayPayInCode_UnauthorizedCode_86
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_TokenisnotvalidCode_87() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_TokenisnotvalidCode_87)
	return EnumEpayPayInCode_TokenisnotvalidCode_87
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_ErrorCheckTokenCode_88() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_ErrorCheckTokenCode_88)
	return EnumEpayPayInCode_ErrorCheckTokenCode_88
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_ErrorNoScopesCode_89() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_ErrorNoScopesCode_89)
	return EnumEpayPayInCode_ErrorNoScopesCode_89
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_ErrorNoScopesCode_90() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_ErrorNoScopesCode_90)
	return EnumEpayPayInCode_ErrorNoScopesCode_90
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_ErrorCheckTokenCode_91() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_ErrorCheckTokenCode_91)
	return EnumEpayPayInCode_ErrorCheckTokenCode_91
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Cardnumber14Code_92() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Cardnumber14Code_92)
	return EnumEpayPayInCode_Cardnumber14Code_92
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_ErrorGetCardCorporatCode_94() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_ErrorGetCardCorporatCode_94)
	return EnumEpayPayInCode_ErrorGetCardCorporatCode_94
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_ErrorGetCountryByBINCode_95() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_ErrorGetCountryByBINCode_95)
	return EnumEpayPayInCode_ErrorGetCountryByBINCode_95
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_InvaliddataCode_96() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_InvaliddataCode_96)
	return EnumEpayPayInCode_InvaliddataCode_96
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_ErrorCheckTokenCode_97() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_ErrorCheckTokenCode_97)
	return EnumEpayPayInCode_ErrorCheckTokenCode_97
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_ErrorNoScopesCode_98() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_ErrorNoScopesCode_98)
	return EnumEpayPayInCode_ErrorNoScopesCode_98
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_ContractCode_99() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_ContractCode_99)
	return EnumEpayPayInCode_ContractCode_99
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_InvalidDateformatPleCode_111() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_InvalidDateformatPleCode_111)
	return EnumEpayPayInCode_InvalidDateformatPleCode_111
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_ContractCode_112() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_ContractCode_112)
	return EnumEpayPayInCode_ContractCode_112
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_ExtcsvxlsxCode_113() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_ExtcsvxlsxCode_113)
	return EnumEpayPayInCode_ExtcsvxlsxCode_113
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_ErrorGetStatementByDCode_114() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_ErrorGetStatementByDCode_114)
	return EnumEpayPayInCode_ErrorGetStatementByDCode_114
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_ErrorGetStatementByOCode_115() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_ErrorGetStatementByOCode_115)
	return EnumEpayPayInCode_ErrorGetStatementByOCode_115
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_ErrorGetStatementAirCode_116() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_ErrorGetStatementAirCode_116)
	return EnumEpayPayInCode_ErrorGetStatementAirCode_116
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_ErrorGetStatementByTCode_117() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_ErrorGetStatementByTCode_117)
	return EnumEpayPayInCode_ErrorGetStatementByTCode_117
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_ErrorCheckTokenCode_121() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_ErrorCheckTokenCode_121)
	return EnumEpayPayInCode_ErrorCheckTokenCode_121
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_122() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_122)
	return EnumEpayPayInCode_Code_122
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_ErrorBindCode_123() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_ErrorBindCode_123)
	return EnumEpayPayInCode_ErrorBindCode_123
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_124() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_124)
	return EnumEpayPayInCode_Code_124
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_125() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_125)
	return EnumEpayPayInCode_Code_125
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_126() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_126)
	return EnumEpayPayInCode_Code_126
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_127() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_127)
	return EnumEpayPayInCode_Code_127
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_ErrorEmptyMerchantIDCode_128() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_ErrorEmptyMerchantIDCode_128)
	return EnumEpayPayInCode_ErrorEmptyMerchantIDCode_128
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_ErrorUnknownMerchantCode_129() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_ErrorUnknownMerchantCode_129)
	return EnumEpayPayInCode_ErrorUnknownMerchantCode_129
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_ServerErrorCode_130() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_ServerErrorCode_130)
	return EnumEpayPayInCode_ServerErrorCode_130
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_ErrorCheckTokenCode_131() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_ErrorCheckTokenCode_131)
	return EnumEpayPayInCode_ErrorCheckTokenCode_131
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_ErrorInvalidScopeCode_132() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_ErrorInvalidScopeCode_132)
	return EnumEpayPayInCode_ErrorInvalidScopeCode_132
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_ErrorBindCode_133() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_ErrorBindCode_133)
	return EnumEpayPayInCode_ErrorBindCode_133
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_ErrorRequestPostFormCode_134() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_ErrorRequestPostFormCode_134)
	return EnumEpayPayInCode_ErrorRequestPostFormCode_134
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_ErrorBodyIsEmptyCode_135() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_ErrorBodyIsEmptyCode_135)
	return EnumEpayPayInCode_ErrorBodyIsEmptyCode_135
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_ErrorRequestIsNotSucCode_136() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_ErrorRequestIsNotSucCode_136)
	return EnumEpayPayInCode_ErrorRequestIsNotSucCode_136
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_UnkwonErrorCode_137() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_UnkwonErrorCode_137)
	return EnumEpayPayInCode_UnkwonErrorCode_137
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_SecretincorrecterrorCode_138() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_SecretincorrecterrorCode_138)
	return EnumEpayPayInCode_SecretincorrecterrorCode_138
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_139() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_139)
	return EnumEpayPayInCode_Code_139
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_SecretincorrectCode_140() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_SecretincorrectCode_140)
	return EnumEpayPayInCode_SecretincorrectCode_140
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_ErrorgetclientfromreCode_141() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_ErrorgetclientfromreCode_141)
	return EnumEpayPayInCode_ErrorgetclientfromreCode_141
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_ErrorparseclientCode_142() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_ErrorparseclientCode_142)
	return EnumEpayPayInCode_ErrorparseclientCode_142
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_CheckscopesCode_143() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_CheckscopesCode_143)
	return EnumEpayPayInCode_CheckscopesCode_143
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_UseridpasswordCode_144() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_UseridpasswordCode_144)
	return EnumEpayPayInCode_UseridpasswordCode_144
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_UserstatusCHANGEPASSCode_145() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_UserstatusCHANGEPASSCode_145)
	return EnumEpayPayInCode_UserstatusCHANGEPASSCode_145
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_UsernotfoundCode_146() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_UsernotfoundCode_146)
	return EnumEpayPayInCode_UsernotfoundCode_146
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_StatusCode_147() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_StatusCode_147)
	return EnumEpayPayInCode_StatusCode_147
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_148() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_148)
	return EnumEpayPayInCode_Code_148
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_UserstatusinvalidCode_149() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_UserstatusinvalidCode_149)
	return EnumEpayPayInCode_UserstatusinvalidCode_149
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_StatusisnotdefinedCode_150() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_StatusisnotdefinedCode_150)
	return EnumEpayPayInCode_StatusisnotdefinedCode_150
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_PASSWORDINCORRECTCode_151() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_PASSWORDINCORRECTCode_151)
	return EnumEpayPayInCode_PASSWORDINCORRECTCode_151
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_GranttypeCode_152() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_GranttypeCode_152)
	return EnumEpayPayInCode_GranttypeCode_152
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_RefreshTokenCode_153() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_RefreshTokenCode_153)
	return EnumEpayPayInCode_RefreshTokenCode_153
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_154() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_154)
	return EnumEpayPayInCode_Code_154
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_ScopeCode_155() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_ScopeCode_155)
	return EnumEpayPayInCode_ScopeCode_155
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_RefreshtokenCode_156() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_RefreshtokenCode_156)
	return EnumEpayPayInCode_RefreshtokenCode_156
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_GENERATEACCESSERRORCode_157() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_GENERATEACCESSERRORCode_157)
	return EnumEpayPayInCode_GENERATEACCESSERRORCode_157
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_GENERATEACCESSERRORCode_158() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_GENERATEACCESSERRORCode_158)
	return EnumEpayPayInCode_GENERATEACCESSERRORCode_158
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_EXTENSIONFIELDADDERRCode_159() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_EXTENSIONFIELDADDERRCode_159)
	return EnumEpayPayInCode_EXTENSIONFIELDADDERRCode_159
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_EXTENSIONFIELDADDERRCode_160() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_EXTENSIONFIELDADDERRCode_160)
	return EnumEpayPayInCode_EXTENSIONFIELDADDERRCode_160
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_161() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_161)
	return EnumEpayPayInCode_Code_161
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_162() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_162)
	return EnumEpayPayInCode_Code_162
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_GetUserByLoginerrorcCode_163() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_GetUserByLoginerrorcCode_163)
	return EnumEpayPayInCode_GetUserByLoginerrorcCode_163
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_164() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_164)
	return EnumEpayPayInCode_Code_164
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_GetStatusByNameerrorCode_165() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_GetStatusByNameerrorCode_165)
	return EnumEpayPayInCode_GetStatusByNameerrorCode_165
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_HashPassworderrorCode_166() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_HashPassworderrorCode_166)
	return EnumEpayPayInCode_HashPassworderrorCode_166
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_SaveUsererrorCode_167() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_SaveUsererrorCode_167)
	return EnumEpayPayInCode_SaveUsererrorCode_167
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_UpdateUsererrorCode_169() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_UpdateUsererrorCode_169)
	return EnumEpayPayInCode_UpdateUsererrorCode_169
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_UpdateUsererrorCode_170() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_UpdateUsererrorCode_170)
	return EnumEpayPayInCode_UpdateUsererrorCode_170
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_GetUserByLoginerrorcCode_171() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_GetUserByLoginerrorcCode_171)
	return EnumEpayPayInCode_GetUserByLoginerrorcCode_171
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_172() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_172)
	return EnumEpayPayInCode_Code_172
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_173() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_173)
	return EnumEpayPayInCode_Code_173
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_ActionServiceGetDebiCode_178() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_ActionServiceGetDebiCode_178)
	return EnumEpayPayInCode_ActionServiceGetDebiCode_178
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_ActionServiceGetTranCode_179() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_ActionServiceGetTranCode_179)
	return EnumEpayPayInCode_ActionServiceGetTranCode_179
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_ReferenceEPAYCode_180() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_ReferenceEPAYCode_180)
	return EnumEpayPayInCode_ReferenceEPAYCode_180
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_RedisCode_181() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_RedisCode_181)
	return EnumEpayPayInCode_RedisCode_181
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_182() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_182)
	return EnumEpayPayInCode_Code_182
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_185() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_185)
	return EnumEpayPayInCode_Code_185
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_ErrorUnmarshalCode_186() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_ErrorUnmarshalCode_186)
	return EnumEpayPayInCode_ErrorUnmarshalCode_186
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_StartDateandEndDatesCode_193() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_StartDateandEndDatesCode_193)
	return EnumEpayPayInCode_StartDateandEndDatesCode_193
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_EPayCode_194() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_EPayCode_194)
	return EnumEpayPayInCode_EPayCode_194
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_NoAuthorizeCode_197() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_NoAuthorizeCode_197)
	return EnumEpayPayInCode_NoAuthorizeCode_197
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_BadtokenCode_198() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_BadtokenCode_198)
	return EnumEpayPayInCode_BadtokenCode_198
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_ParseerrorCode_199() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_ParseerrorCode_199)
	return EnumEpayPayInCode_ParseerrorCode_199
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_PaymenterrorCode_200() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_PaymenterrorCode_200)
	return EnumEpayPayInCode_PaymenterrorCode_200
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_216() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_216)
	return EnumEpayPayInCode_Code_216
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_ErrorBindJSONCannotuCode_224() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_ErrorBindJSONCannotuCode_224)
	return EnumEpayPayInCode_ErrorBindJSONCannotuCode_224
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_LIKECode_225() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_LIKECode_225)
	return EnumEpayPayInCode_LIKECode_225
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_226() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_226)
	return EnumEpayPayInCode_Code_226
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_BETWEENCode_227() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_BETWEENCode_227)
	return EnumEpayPayInCode_BETWEENCode_227
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_228() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_228)
	return EnumEpayPayInCode_Code_228
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_229() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_229)
	return EnumEpayPayInCode_Code_229
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_230() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_230)
	return EnumEpayPayInCode_Code_230
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_231() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_231)
	return EnumEpayPayInCode_Code_231
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_232() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_232)
	return EnumEpayPayInCode_Code_232
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_ErrorCheckTokenCode_233() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_ErrorCheckTokenCode_233)
	return EnumEpayPayInCode_ErrorCheckTokenCode_233
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_ErrorNoScopesCode_234() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_ErrorNoScopesCode_234)
	return EnumEpayPayInCode_ErrorNoScopesCode_234
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_FromdateYYYYMMDDCode_235() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_FromdateYYYYMMDDCode_235)
	return EnumEpayPayInCode_FromdateYYYYMMDDCode_235
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_TodateYYYYMMDDCode_236() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_TodateYYYYMMDDCode_236)
	return EnumEpayPayInCode_TodateYYYYMMDDCode_236
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_ErrorCheckTokenCode_237() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_ErrorCheckTokenCode_237)
	return EnumEpayPayInCode_ErrorCheckTokenCode_237
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_ErrorNoScopesCode_238() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_ErrorNoScopesCode_238)
	return EnumEpayPayInCode_ErrorNoScopesCode_238
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_CannotunmarshalintosCode_239() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_CannotunmarshalintosCode_239)
	return EnumEpayPayInCode_CannotunmarshalintosCode_239
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_240() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_240)
	return EnumEpayPayInCode_Code_240
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_241() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_241)
	return EnumEpayPayInCode_Code_241
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_242() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_242)
	return EnumEpayPayInCode_Code_242
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_IDCode_243() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_IDCode_243)
	return EnumEpayPayInCode_IDCode_243
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_MerchantIDCode_244() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_MerchantIDCode_244)
	return EnumEpayPayInCode_MerchantIDCode_244
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_StartDateandEndDatesCode_245() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_StartDateandEndDatesCode_245)
	return EnumEpayPayInCode_StartDateandEndDatesCode_245
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_246() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_246)
	return EnumEpayPayInCode_Code_246
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_NotemailCode_247() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_NotemailCode_247)
	return EnumEpayPayInCode_NotemailCode_247
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_JsonCode_248() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_JsonCode_248)
	return EnumEpayPayInCode_JsonCode_248
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_249() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_249)
	return EnumEpayPayInCode_Code_249
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_MsgValueCode_250() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_MsgValueCode_250)
	return EnumEpayPayInCode_MsgValueCode_250
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_251() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_251)
	return EnumEpayPayInCode_Code_251
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_252() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_252)
	return EnumEpayPayInCode_Code_252
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_263() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_263)
	return EnumEpayPayInCode_Code_263
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_264() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_264)
	return EnumEpayPayInCode_Code_264
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_265() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_265)
	return EnumEpayPayInCode_Code_265
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_SecretincorrecterrorCode_266() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_SecretincorrecterrorCode_266)
	return EnumEpayPayInCode_SecretincorrecterrorCode_266
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_ScopenotinchangepassCode_267() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_ScopenotinchangepassCode_267)
	return EnumEpayPayInCode_ScopenotinchangepassCode_267
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_UnsupportedscopeCode_268() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_UnsupportedscopeCode_268)
	return EnumEpayPayInCode_UnsupportedscopeCode_268
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_BLOCKCode_269() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_BLOCKCode_269)
	return EnumEpayPayInCode_BLOCKCode_269
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_BLOCKCode_270() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_BLOCKCode_270)
	return EnumEpayPayInCode_BLOCKCode_270
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_UsernotsaveCode_271() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_UsernotsaveCode_271)
	return EnumEpayPayInCode_UsernotsaveCode_271
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_UsernotsaveCode_272() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_UsernotsaveCode_272)
	return EnumEpayPayInCode_UsernotsaveCode_272
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_FailedtogetstatusCode_273() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_FailedtogetstatusCode_273)
	return EnumEpayPayInCode_FailedtogetstatusCode_273
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_UseralreadyexistCode_274() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_UseralreadyexistCode_274)
	return EnumEpayPayInCode_UseralreadyexistCode_274
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_UseralreadyexistCode_275() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_UseralreadyexistCode_275)
	return EnumEpayPayInCode_UseralreadyexistCode_275
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_IncorrectdataCode_276() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_IncorrectdataCode_276)
	return EnumEpayPayInCode_IncorrectdataCode_276
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_IncorrectdataCode_277() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_IncorrectdataCode_277)
	return EnumEpayPayInCode_IncorrectdataCode_277
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_NotemailCode_278() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_NotemailCode_278)
	return EnumEpayPayInCode_NotemailCode_278
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_NotemailCode_279() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_NotemailCode_279)
	return EnumEpayPayInCode_NotemailCode_279
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_280() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_280)
	return EnumEpayPayInCode_Code_280
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_TransactioncreateerrCode_281() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_TransactioncreateerrCode_281)
	return EnumEpayPayInCode_TransactioncreateerrCode_281
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_282() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_282)
	return EnumEpayPayInCode_Code_282
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_283() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_283)
	return EnumEpayPayInCode_Code_283
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_TestMECode_285() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_TestMECode_285)
	return EnumEpayPayInCode_TestMECode_285
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_StatementCode_286() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_StatementCode_286)
	return EnumEpayPayInCode_StatementCode_286
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_ErrorCheckTokenCode_287() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_ErrorCheckTokenCode_287)
	return EnumEpayPayInCode_ErrorCheckTokenCode_287
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_ErrorNoScopesCode_288() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_ErrorNoScopesCode_288)
	return EnumEpayPayInCode_ErrorNoScopesCode_288
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_TofromYYYYMMDDCode_289() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_TofromYYYYMMDDCode_289)
	return EnumEpayPayInCode_TofromYYYYMMDDCode_289
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_290() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_290)
	return EnumEpayPayInCode_Code_290
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_FromDateandToDateshoCode_291() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_FromDateandToDateshoCode_291)
	return EnumEpayPayInCode_FromDateandToDateshoCode_291
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_ERRORwhilecreatingreCode_292() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_ERRORwhilecreatingreCode_292)
	return EnumEpayPayInCode_ERRORwhilecreatingreCode_292
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_293() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_293)
	return EnumEpayPayInCode_Code_293
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_RequiredclientcredenCode_294() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_RequiredclientcredenCode_294)
	return EnumEpayPayInCode_RequiredclientcredenCode_294
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_IDIDCode_295() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_IDIDCode_295)
	return EnumEpayPayInCode_IDIDCode_295
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_ErrorUpdateStaffCode_296() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_ErrorUpdateStaffCode_296)
	return EnumEpayPayInCode_ErrorUpdateStaffCode_296
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_StaffCode_297() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_StaffCode_297)
	return EnumEpayPayInCode_StaffCode_297
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_298() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_298)
	return EnumEpayPayInCode_Code_298
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_StaffCode_299() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_StaffCode_299)
	return EnumEpayPayInCode_StaffCode_299
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_BindErrorCode_300() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_BindErrorCode_300)
	return EnumEpayPayInCode_BindErrorCode_300
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_UpdateStaffCode_301() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_UpdateStaffCode_301)
	return EnumEpayPayInCode_UpdateStaffCode_301
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_MultipartFormCode_302() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_MultipartFormCode_302)
	return EnumEpayPayInCode_MultipartFormCode_302
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_ExpireAtYYYYMMDDCode_303() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_ExpireAtYYYYMMDDCode_303)
	return EnumEpayPayInCode_ExpireAtYYYYMMDDCode_303
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_ExpireAtYYYYMMDDCode_304() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_ExpireAtYYYYMMDDCode_304)
	return EnumEpayPayInCode_ExpireAtYYYYMMDDCode_304
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_ExpireAtCode_305() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_ExpireAtCode_305)
	return EnumEpayPayInCode_ExpireAtCode_305
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_ExpireAtCode_306() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_ExpireAtCode_306)
	return EnumEpayPayInCode_ExpireAtCode_306
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_307() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_307)
	return EnumEpayPayInCode_Code_307
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_308() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_308)
	return EnumEpayPayInCode_Code_308
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_309() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_309)
	return EnumEpayPayInCode_Code_309
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_CommitErrorCode_310() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_CommitErrorCode_310)
	return EnumEpayPayInCode_CommitErrorCode_310
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_BindJSONErrorCode_311() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_BindJSONErrorCode_311)
	return EnumEpayPayInCode_BindJSONErrorCode_311
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_312() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_312)
	return EnumEpayPayInCode_Code_312
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_SearchtransactionerrCode_318() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_SearchtransactionerrCode_318)
	return EnumEpayPayInCode_SearchtransactionerrCode_318
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_JsonCode_319() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_JsonCode_319)
	return EnumEpayPayInCode_JsonCode_319
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_320() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_320)
	return EnumEpayPayInCode_Code_320
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_321() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_321)
	return EnumEpayPayInCode_Code_321
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_322() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_322)
	return EnumEpayPayInCode_Code_322
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_FromdateYYYYMMDDCode_323() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_FromdateYYYYMMDDCode_323)
	return EnumEpayPayInCode_FromdateYYYYMMDDCode_323
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_TodateYYYYMMDDCode_324() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_TodateYYYYMMDDCode_324)
	return EnumEpayPayInCode_TodateYYYYMMDDCode_324
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_EPayCode_325() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_EPayCode_325)
	return EnumEpayPayInCode_EPayCode_325
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_ErrorgetMerchantinfoCode_326() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_ErrorgetMerchantinfoCode_326)
	return EnumEpayPayInCode_ErrorgetMerchantinfoCode_326
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_327() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_327)
	return EnumEpayPayInCode_Code_327
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_328() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_328)
	return EnumEpayPayInCode_Code_328
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_InvoiceIDCode_329() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_InvoiceIDCode_329)
	return EnumEpayPayInCode_InvoiceIDCode_329
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_TerminalCode_330() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_TerminalCode_330)
	return EnumEpayPayInCode_TerminalCode_330
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_CurrencyCode_331() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_CurrencyCode_331)
	return EnumEpayPayInCode_CurrencyCode_331
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_AmountCode_332() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_AmountCode_332)
	return EnumEpayPayInCode_AmountCode_332
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_333() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_333)
	return EnumEpayPayInCode_Code_333
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_ShopIDCode_334() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_ShopIDCode_334)
	return EnumEpayPayInCode_ShopIDCode_334
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_MerchantIDCode_335() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_MerchantIDCode_335)
	return EnumEpayPayInCode_MerchantIDCode_335
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_ShopIDCode_336() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_ShopIDCode_336)
	return EnumEpayPayInCode_ShopIDCode_336
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_ErrorgetMerchantinfoCode_337() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_ErrorgetMerchantinfoCode_337)
	return EnumEpayPayInCode_ErrorgetMerchantinfoCode_337
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_338() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_338)
	return EnumEpayPayInCode_Code_338
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_339() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_339)
	return EnumEpayPayInCode_Code_339
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_340() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_340)
	return EnumEpayPayInCode_Code_340
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_ContractCode_342() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_ContractCode_342)
	return EnumEpayPayInCode_ContractCode_342
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_GetShopByContractCode_343() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_GetShopByContractCode_343)
	return EnumEpayPayInCode_GetShopByContractCode_343
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_344() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_344)
	return EnumEpayPayInCode_Code_344
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_MarshalerrorCode_345() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_MarshalerrorCode_345)
	return EnumEpayPayInCode_MarshalerrorCode_345
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_346() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_346)
	return EnumEpayPayInCode_Code_346
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_347() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_347)
	return EnumEpayPayInCode_Code_347
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_348() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_348)
	return EnumEpayPayInCode_Code_348
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_349() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_349)
	return EnumEpayPayInCode_Code_349
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_350() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_350)
	return EnumEpayPayInCode_Code_350
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_351() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_351)
	return EnumEpayPayInCode_Code_351
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_352() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_352)
	return EnumEpayPayInCode_Code_352
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_TopCode_353() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_TopCode_353)
	return EnumEpayPayInCode_TopCode_353
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_354() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_354)
	return EnumEpayPayInCode_Code_354
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_EmailCode_355() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_EmailCode_355)
	return EnumEpayPayInCode_EmailCode_355
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_356() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_356)
	return EnumEpayPayInCode_Code_356
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_357() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_357)
	return EnumEpayPayInCode_Code_357
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_358() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_358)
	return EnumEpayPayInCode_Code_358
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_359() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_359)
	return EnumEpayPayInCode_Code_359
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_360() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_360)
	return EnumEpayPayInCode_Code_360
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_361() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_361)
	return EnumEpayPayInCode_Code_361
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_362() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_362)
	return EnumEpayPayInCode_Code_362
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_ParseerrorCode_363() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_ParseerrorCode_363)
	return EnumEpayPayInCode_ParseerrorCode_363
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_ParseerrorCode_364() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_ParseerrorCode_364)
	return EnumEpayPayInCode_ParseerrorCode_364
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_365() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_365)
	return EnumEpayPayInCode_Code_365
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_ParseerrorCode_366() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_ParseerrorCode_366)
	return EnumEpayPayInCode_ParseerrorCode_366
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_ParseerrorCode_367() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_ParseerrorCode_367)
	return EnumEpayPayInCode_ParseerrorCode_367
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_368() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_368)
	return EnumEpayPayInCode_Code_368
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_ParseerrorCode_369() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_ParseerrorCode_369)
	return EnumEpayPayInCode_ParseerrorCode_369
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_ParseerrorCode_370() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_ParseerrorCode_370)
	return EnumEpayPayInCode_ParseerrorCode_370
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_371() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_371)
	return EnumEpayPayInCode_Code_371
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_372() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_372)
	return EnumEpayPayInCode_Code_372
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_NoemailCode_373() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_NoemailCode_373)
	return EnumEpayPayInCode_NoemailCode_373
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_374() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_374)
	return EnumEpayPayInCode_Code_374
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_375() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_375)
	return EnumEpayPayInCode_Code_375
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_376() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_376)
	return EnumEpayPayInCode_Code_376
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_IncorrectformatdateCode_377() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_IncorrectformatdateCode_377)
	return EnumEpayPayInCode_IncorrectformatdateCode_377
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_GetFileFiledoesnotexCode_378() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_GetFileFiledoesnotexCode_378)
	return EnumEpayPayInCode_GetFileFiledoesnotexCode_378
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_379() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_379)
	return EnumEpayPayInCode_Code_379
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_380() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_380)
	return EnumEpayPayInCode_Code_380
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_381() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_381)
	return EnumEpayPayInCode_Code_381
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_382() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_382)
	return EnumEpayPayInCode_Code_382
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_383() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_383)
	return EnumEpayPayInCode_Code_383
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_EgatewayCode_384() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_EgatewayCode_384)
	return EnumEpayPayInCode_EgatewayCode_384
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_EgatewayCode_385() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_EgatewayCode_385)
	return EnumEpayPayInCode_EgatewayCode_385
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_EgatewayCode_386() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_EgatewayCode_386)
	return EnumEpayPayInCode_EgatewayCode_386
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_EgatewayCode_387() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_EgatewayCode_387)
	return EnumEpayPayInCode_EgatewayCode_387
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_EgatewayCode_388() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_EgatewayCode_388)
	return EnumEpayPayInCode_EgatewayCode_388
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_UserdeleteCode_389() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_UserdeleteCode_389)
	return EnumEpayPayInCode_UserdeleteCode_389
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_ParseerrorCode_390() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_ParseerrorCode_390)
	return EnumEpayPayInCode_ParseerrorCode_390
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_CallbacksendmessageeCode_391() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_CallbacksendmessageeCode_391)
	return EnumEpayPayInCode_CallbacksendmessageeCode_391
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_392() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_392)
	return EnumEpayPayInCode_Code_392
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_393() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_393)
	return EnumEpayPayInCode_Code_393
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_394() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_394)
	return EnumEpayPayInCode_Code_394
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_395() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_395)
	return EnumEpayPayInCode_Code_395
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_396() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_396)
	return EnumEpayPayInCode_Code_396
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_MVisaCode_397() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_MVisaCode_397)
	return EnumEpayPayInCode_MVisaCode_397
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_ThreeDSecureCode_398() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_ThreeDSecureCode_398)
	return EnumEpayPayInCode_ThreeDSecureCode_398
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_MVisaverificationCode_399() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_MVisaverificationCode_399)
	return EnumEpayPayInCode_MVisaverificationCode_399
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_400() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_400)
	return EnumEpayPayInCode_Code_400
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_401() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_401)
	return EnumEpayPayInCode_Code_401
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_402() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_402)
	return EnumEpayPayInCode_Code_402
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_CarddataisnotrequireCode_403() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_CarddataisnotrequireCode_403)
	return EnumEpayPayInCode_CarddataisnotrequireCode_403
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_404() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_404)
	return EnumEpayPayInCode_Code_404
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_405() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_405)
	return EnumEpayPayInCode_Code_405
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_406() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_406)
	return EnumEpayPayInCode_Code_406
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_407() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_407)
	return EnumEpayPayInCode_Code_407
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_408() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_408)
	return EnumEpayPayInCode_Code_408
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_409() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_409)
	return EnumEpayPayInCode_Code_409
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_MVISACode_410() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_MVISACode_410)
	return EnumEpayPayInCode_MVISACode_410
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_NoAuthorizeCode_411() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_NoAuthorizeCode_411)
	return EnumEpayPayInCode_NoAuthorizeCode_411
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_BadtokenCode_412() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_BadtokenCode_412)
	return EnumEpayPayInCode_BadtokenCode_412
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_413() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_413)
	return EnumEpayPayInCode_Code_413
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_TimeouttransactionCode_504() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_TimeouttransactionCode_504)
	return EnumEpayPayInCode_TimeouttransactionCode_504
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_ExceededattemptsCode_505() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_ExceededattemptsCode_505)
	return EnumEpayPayInCode_ExceededattemptsCode_505
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_506() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_506)
	return EnumEpayPayInCode_Code_506
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_507() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_507)
	return EnumEpayPayInCode_Code_507
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_508() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_508)
	return EnumEpayPayInCode_Code_508
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_509() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_509)
	return EnumEpayPayInCode_Code_509
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_RabbitCode_510() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_RabbitCode_510)
	return EnumEpayPayInCode_RabbitCode_510
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_512() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_512)
	return EnumEpayPayInCode_Code_512
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_PostlinkCode_513() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_PostlinkCode_513)
	return EnumEpayPayInCode_PostlinkCode_513
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_514() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_514)
	return EnumEpayPayInCode_Code_514
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_515() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_515)
	return EnumEpayPayInCode_Code_515
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_516() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_516)
	return EnumEpayPayInCode_Code_516
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_TherewasnoattempttopCode_517() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_TherewasnoattempttopCode_517)
	return EnumEpayPayInCode_TherewasnoattempttopCode_517
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_IpCode_518() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_IpCode_518)
	return EnumEpayPayInCode_IpCode_518
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_InvoiceIDCode_520() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_InvoiceIDCode_520)
	return EnumEpayPayInCode_InvoiceIDCode_520
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_TerminalIDCode_511() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_TerminalIDCode_511)
	return EnumEpayPayInCode_TerminalIDCode_511
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_EGWCode_546() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_EGWCode_546)
	return EnumEpayPayInCode_EGWCode_546
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_EGWHTTPCODE200Code_547() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_EGWHTTPCODE200Code_547)
	return EnumEpayPayInCode_EGWHTTPCODE200Code_547
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_559() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_559)
	return EnumEpayPayInCode_Code_559
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_EGWCode_560() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_EGWCode_560)
	return EnumEpayPayInCode_EGWCode_560
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_EGWHTTPCODE200Code_561() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_EGWHTTPCODE200Code_561)
	return EnumEpayPayInCode_EGWHTTPCODE200Code_561
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_MDCode_529() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_MDCode_529)
	return EnumEpayPayInCode_MDCode_529
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_EGWCode_530() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_EGWCode_530)
	return EnumEpayPayInCode_EGWCode_530
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_570() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_570)
	return EnumEpayPayInCode_Code_570
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_EGWCode_571() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_EGWCode_571)
	return EnumEpayPayInCode_EGWCode_571
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_EGWHTTPCODE200Code_533() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_EGWHTTPCODE200Code_533)
	return EnumEpayPayInCode_EGWHTTPCODE200Code_533
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_534() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_534)
	return EnumEpayPayInCode_Code_534
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_535() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_535)
	return EnumEpayPayInCode_Code_535
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_MerchantCode_536() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_MerchantCode_536)
	return EnumEpayPayInCode_MerchantCode_536
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_MerchantIsActivefalsCode_537() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_MerchantIsActivefalsCode_537)
	return EnumEpayPayInCode_MerchantIsActivefalsCode_537
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_538() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_538)
	return EnumEpayPayInCode_Code_538
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_MerchantCode_539() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_MerchantCode_539)
	return EnumEpayPayInCode_MerchantCode_539
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_ScopenotfoundcheckscCode_540() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_ScopenotfoundcheckscCode_540)
	return EnumEpayPayInCode_ScopenotfoundcheckscCode_540
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_ErrorparameternameCode_541() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_ErrorparameternameCode_541)
	return EnumEpayPayInCode_ErrorparameternameCode_541
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_UnauthorizedChecktokCode_542() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_UnauthorizedChecktokCode_542)
	return EnumEpayPayInCode_UnauthorizedChecktokCode_542
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_ErrorInvalidScopeCode_543() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_ErrorInvalidScopeCode_543)
	return EnumEpayPayInCode_ErrorInvalidScopeCode_543
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_ErrorBindCode_544() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_ErrorBindCode_544)
	return EnumEpayPayInCode_ErrorBindCode_544
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_UnkwonErrorCode_548() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_UnkwonErrorCode_548)
	return EnumEpayPayInCode_UnkwonErrorCode_548
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_TransactionalreadycaCode_549() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_TransactionalreadycaCode_549)
	return EnumEpayPayInCode_TransactionalreadycaCode_549
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_RequesttoXLSServerfaCode_550() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_RequesttoXLSServerfaCode_550)
	return EnumEpayPayInCode_RequesttoXLSServerfaCode_550
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_RequesttoXLSServerreCode_551() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_RequesttoXLSServerreCode_551)
	return EnumEpayPayInCode_RequesttoXLSServerreCode_551
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_InvalidbonusamountCode_552() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_InvalidbonusamountCode_552)
	return EnumEpayPayInCode_InvalidbonusamountCode_552
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_InvalidinputdataCode_553() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_InvalidinputdataCode_553)
	return EnumEpayPayInCode_InvalidinputdataCode_553
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_TerminalconfiguratioCode_554() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_TerminalconfiguratioCode_554)
	return EnumEpayPayInCode_TerminalconfiguratioCode_554
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_UnabletogetGETrequesCode_555() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_UnabletogetGETrequesCode_555)
	return EnumEpayPayInCode_UnabletogetGETrequesCode_555
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_RequesttoapixlsfaileCode_556() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_RequesttoapixlsfaileCode_556)
	return EnumEpayPayInCode_RequesttoapixlsfaileCode_556
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_RequesttoapixlsreturCode_557() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_RequesttoapixlsreturCode_557)
	return EnumEpayPayInCode_RequesttoapixlsreturCode_557
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_GobonusconvertiontofCode_558() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_GobonusconvertiontofCode_558)
	return EnumEpayPayInCode_GobonusconvertiontofCode_558
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_563() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_563)
	return EnumEpayPayInCode_Code_563
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_TerminalIDCode_564() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_TerminalIDCode_564)
	return EnumEpayPayInCode_TerminalIDCode_564
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_RequesttogetcardinfoCode_565() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_RequesttogetcardinfoCode_565)
	return EnumEpayPayInCode_RequesttogetcardinfoCode_565
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_RequesttogetcardinfoCode_566() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_RequesttogetcardinfoCode_566)
	return EnumEpayPayInCode_RequesttogetcardinfoCode_566
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_CardnotfoundCode_567() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_CardnotfoundCode_567)
	return EnumEpayPayInCode_CardnotfoundCode_567
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_CardownernotfoundCode_568() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_CardownernotfoundCode_568)
	return EnumEpayPayInCode_CardownernotfoundCode_568
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_569() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_569)
	return EnumEpayPayInCode_Code_569
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_572() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_572)
	return EnumEpayPayInCode_Code_572
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_RequesttoapixlsfaileCode_573() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_RequesttoapixlsfaileCode_573)
	return EnumEpayPayInCode_RequesttoapixlsfaileCode_573
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_RequesttoapixlsreturCode_574() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_RequesttoapixlsreturCode_574)
	return EnumEpayPayInCode_RequesttoapixlsreturCode_574
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_UnauthorizedChecktokCode_576() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_UnauthorizedChecktokCode_576)
	return EnumEpayPayInCode_UnauthorizedChecktokCode_576
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_CannotcancelxlstransCode_578() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_CannotcancelxlstransCode_578)
	return EnumEpayPayInCode_CannotcancelxlstransCode_578
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_CannotsendtransactioCode_579() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_CannotsendtransactioCode_579)
	return EnumEpayPayInCode_CannotsendtransactioCode_579
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_CannotsendcancelxlstCode_580() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_CannotsendcancelxlstCode_580)
	return EnumEpayPayInCode_CannotsendcancelxlstCode_580
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_CannotcreaterabbitinCode_581() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_CannotcreaterabbitinCode_581)
	return EnumEpayPayInCode_CannotcreaterabbitinCode_581
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_ChangePaymentCode_582() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_ChangePaymentCode_582)
	return EnumEpayPayInCode_ChangePaymentCode_582
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_583() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_583)
	return EnumEpayPayInCode_Code_583
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_XlsCode_584() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_XlsCode_584)
	return EnumEpayPayInCode_XlsCode_584
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_GoCode_585() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_GoCode_585)
	return EnumEpayPayInCode_GoCode_585
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_586() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_586)
	return EnumEpayPayInCode_Code_586
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_587() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_587)
	return EnumEpayPayInCode_Code_587
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_588() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_588)
	return EnumEpayPayInCode_Code_588
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_589() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_589)
	return EnumEpayPayInCode_Code_589
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_590() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_590)
	return EnumEpayPayInCode_Code_590
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_591() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_591)
	return EnumEpayPayInCode_Code_591
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_592() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_592)
	return EnumEpayPayInCode_Code_592
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_593() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_593)
	return EnumEpayPayInCode_Code_593
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_594() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_594)
	return EnumEpayPayInCode_Code_594
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_595() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_595)
	return EnumEpayPayInCode_Code_595
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Epay1documentCode_596() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Epay1documentCode_596)
	return EnumEpayPayInCode_Epay1documentCode_596
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_CrtificateIDCode_597() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_CrtificateIDCode_597)
	return EnumEpayPayInCode_CrtificateIDCode_597
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_598() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_598)
	return EnumEpayPayInCode_Code_598
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_599() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_599)
	return EnumEpayPayInCode_Code_599
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_600() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_600)
	return EnumEpayPayInCode_Code_600
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_601() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_601)
	return EnumEpayPayInCode_Code_601
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_602() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_602)
	return EnumEpayPayInCode_Code_602
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_603() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_603)
	return EnumEpayPayInCode_Code_603
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_604() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_604)
	return EnumEpayPayInCode_Code_604
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_605() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_605)
	return EnumEpayPayInCode_Code_605
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_606() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_606)
	return EnumEpayPayInCode_Code_606
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_607() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_607)
	return EnumEpayPayInCode_Code_607
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_608() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_608)
	return EnumEpayPayInCode_Code_608
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_609() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_609)
	return EnumEpayPayInCode_Code_609
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_610() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_610)
	return EnumEpayPayInCode_Code_610
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_611() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_611)
	return EnumEpayPayInCode_Code_611
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_612() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_612)
	return EnumEpayPayInCode_Code_612
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_613() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_613)
	return EnumEpayPayInCode_Code_613
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_614() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_614)
	return EnumEpayPayInCode_Code_614
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_615() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_615)
	return EnumEpayPayInCode_Code_615
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_616() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_616)
	return EnumEpayPayInCode_Code_616
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_InvoiceIDCode_617() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_InvoiceIDCode_617)
	return EnumEpayPayInCode_InvoiceIDCode_617
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_TerminalCode_618() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_TerminalCode_618)
	return EnumEpayPayInCode_TerminalCode_618
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_TerminalCode_619() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_TerminalCode_619)
	return EnumEpayPayInCode_TerminalCode_619
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_CurrencyCode_620() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_CurrencyCode_620)
	return EnumEpayPayInCode_CurrencyCode_620
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_AmountCode_621() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_AmountCode_621)
	return EnumEpayPayInCode_AmountCode_621
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_AmountCode_622() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_AmountCode_622)
	return EnumEpayPayInCode_AmountCode_622
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_AmountCode_623() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_AmountCode_623)
	return EnumEpayPayInCode_AmountCode_623
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_624() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_624)
	return EnumEpayPayInCode_Code_624
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_625() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_625)
	return EnumEpayPayInCode_Code_625
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_626() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_626)
	return EnumEpayPayInCode_Code_626
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_627() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_627)
	return EnumEpayPayInCode_Code_627
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_628() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_628)
	return EnumEpayPayInCode_Code_628
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_629() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_629)
	return EnumEpayPayInCode_Code_629
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_630() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_630)
	return EnumEpayPayInCode_Code_630
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_631() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_631)
	return EnumEpayPayInCode_Code_631
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_632() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_632)
	return EnumEpayPayInCode_Code_632
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_633() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_633)
	return EnumEpayPayInCode_Code_633
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_634() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_634)
	return EnumEpayPayInCode_Code_634
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_EgatewayCode_635() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_EgatewayCode_635)
	return EnumEpayPayInCode_EgatewayCode_635
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_EgatewayCode_636() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_EgatewayCode_636)
	return EnumEpayPayInCode_EgatewayCode_636
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_EgatewayCode_637() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_EgatewayCode_637)
	return EnumEpayPayInCode_EgatewayCode_637
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_638() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_638)
	return EnumEpayPayInCode_Code_638
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_639() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_639)
	return EnumEpayPayInCode_Code_639
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_640() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_640)
	return EnumEpayPayInCode_Code_640
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_641() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_641)
	return EnumEpayPayInCode_Code_641
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_642() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_642)
	return EnumEpayPayInCode_Code_642
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_CallbacksendmessageeCode_643() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_CallbacksendmessageeCode_643)
	return EnumEpayPayInCode_CallbacksendmessageeCode_643
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_644() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_644)
	return EnumEpayPayInCode_Code_644
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_645() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_645)
	return EnumEpayPayInCode_Code_645
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_646() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_646)
	return EnumEpayPayInCode_Code_646
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_647() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_647)
	return EnumEpayPayInCode_Code_647
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_648() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_648)
	return EnumEpayPayInCode_Code_648
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_649() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_649)
	return EnumEpayPayInCode_Code_649
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_650() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_650)
	return EnumEpayPayInCode_Code_650
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_651() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_651)
	return EnumEpayPayInCode_Code_651
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_652() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_652)
	return EnumEpayPayInCode_Code_652
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_653() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_653)
	return EnumEpayPayInCode_Code_653
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_654() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_654)
	return EnumEpayPayInCode_Code_654
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_655() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_655)
	return EnumEpayPayInCode_Code_655
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_656() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_656)
	return EnumEpayPayInCode_Code_656
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_657() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_657)
	return EnumEpayPayInCode_Code_657
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_658() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_658)
	return EnumEpayPayInCode_Code_658
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_659() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_659)
	return EnumEpayPayInCode_Code_659
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_660() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_660)
	return EnumEpayPayInCode_Code_660
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_661() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_661)
	return EnumEpayPayInCode_Code_661
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_662() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_662)
	return EnumEpayPayInCode_Code_662
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_663() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_663)
	return EnumEpayPayInCode_Code_663
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_664() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_664)
	return EnumEpayPayInCode_Code_664
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_665() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_665)
	return EnumEpayPayInCode_Code_665
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_666() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_666)
	return EnumEpayPayInCode_Code_666
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_667() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_667)
	return EnumEpayPayInCode_Code_667
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_668() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_668)
	return EnumEpayPayInCode_Code_668
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_669() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_669)
	return EnumEpayPayInCode_Code_669
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_670() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_670)
	return EnumEpayPayInCode_Code_670
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_671() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_671)
	return EnumEpayPayInCode_Code_671
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_672() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_672)
	return EnumEpayPayInCode_Code_672
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_673() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_673)
	return EnumEpayPayInCode_Code_673
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_EGWHTTPCODE200Code_674() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_EGWHTTPCODE200Code_674)
	return EnumEpayPayInCode_EGWHTTPCODE200Code_674
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_675() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_675)
	return EnumEpayPayInCode_Code_675
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_676() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_676)
	return EnumEpayPayInCode_Code_676
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_677() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_677)
	return EnumEpayPayInCode_Code_677
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_678() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_678)
	return EnumEpayPayInCode_Code_678
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_679() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_679)
	return EnumEpayPayInCode_Code_679
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_RequesttogetcardinfoCode_680() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_RequesttogetcardinfoCode_680)
	return EnumEpayPayInCode_RequesttogetcardinfoCode_680
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_RequesttogetcardinfoCode_681() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_RequesttogetcardinfoCode_681)
	return EnumEpayPayInCode_RequesttogetcardinfoCode_681
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_XlsCode_683() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_XlsCode_683)
	return EnumEpayPayInCode_XlsCode_683
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_XlsCode_684() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_XlsCode_684)
	return EnumEpayPayInCode_XlsCode_684
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_685() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_685)
	return EnumEpayPayInCode_Code_685
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_TerminalconfiguratioCode_686() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_TerminalconfiguratioCode_686)
	return EnumEpayPayInCode_TerminalconfiguratioCode_686
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_RequesttoXLSServerfaCode_687() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_RequesttoXLSServerfaCode_687)
	return EnumEpayPayInCode_RequesttoXLSServerfaCode_687
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_RequesttoXLSServerfaCode_688() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_RequesttoXLSServerfaCode_688)
	return EnumEpayPayInCode_RequesttoXLSServerfaCode_688
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_689() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_689)
	return EnumEpayPayInCode_Code_689
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_690() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_690)
	return EnumEpayPayInCode_Code_690
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_691() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_691)
	return EnumEpayPayInCode_Code_691
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_JsonCode_692() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_JsonCode_692)
	return EnumEpayPayInCode_JsonCode_692
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_693() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_693)
	return EnumEpayPayInCode_Code_693
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_694() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_694)
	return EnumEpayPayInCode_Code_694
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_StatementCode_695() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_StatementCode_695)
	return EnumEpayPayInCode_StatementCode_695
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_StatementCode_696() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_StatementCode_696)
	return EnumEpayPayInCode_StatementCode_696
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_StatementCode_697() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_StatementCode_697)
	return EnumEpayPayInCode_StatementCode_697
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_StatementCode_698() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_StatementCode_698)
	return EnumEpayPayInCode_StatementCode_698
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_StatementCode_699() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_StatementCode_699)
	return EnumEpayPayInCode_StatementCode_699
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_TofromYYYYMMDDCode_700() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_TofromYYYYMMDDCode_700)
	return EnumEpayPayInCode_TofromYYYYMMDDCode_700
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_TofromYYYYMMDDCode_701() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_TofromYYYYMMDDCode_701)
	return EnumEpayPayInCode_TofromYYYYMMDDCode_701
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_TofromYYYYMMDDCode_702() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_TofromYYYYMMDDCode_702)
	return EnumEpayPayInCode_TofromYYYYMMDDCode_702
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_703() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_703)
	return EnumEpayPayInCode_Code_703
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_704() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_704)
	return EnumEpayPayInCode_Code_704
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_705() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_705)
	return EnumEpayPayInCode_Code_705
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_706() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_706)
	return EnumEpayPayInCode_Code_706
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_707() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_707)
	return EnumEpayPayInCode_Code_707
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_StaffCode_708() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_StaffCode_708)
	return EnumEpayPayInCode_StaffCode_708
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_StaffCode_709() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_StaffCode_709)
	return EnumEpayPayInCode_StaffCode_709
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_StaffCode_710() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_StaffCode_710)
	return EnumEpayPayInCode_StaffCode_710
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_711() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_711)
	return EnumEpayPayInCode_Code_711
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_712() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_712)
	return EnumEpayPayInCode_Code_712
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_713() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_713)
	return EnumEpayPayInCode_Code_713
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_714() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_714)
	return EnumEpayPayInCode_Code_714
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_715() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_715)
	return EnumEpayPayInCode_Code_715
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_716() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_716)
	return EnumEpayPayInCode_Code_716
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_717() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_717)
	return EnumEpayPayInCode_Code_717
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_718() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_718)
	return EnumEpayPayInCode_Code_718
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_719() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_719)
	return EnumEpayPayInCode_Code_719
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_ErrorinservicingthecCode_720() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_ErrorinservicingthecCode_720)
	return EnumEpayPayInCode_ErrorinservicingthecCode_720
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_ErrorinservicingthecCode_721() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_ErrorinservicingthecCode_721)
	return EnumEpayPayInCode_ErrorinservicingthecCode_721
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_ErrorinservicingthecCode_722() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_ErrorinservicingthecCode_722)
	return EnumEpayPayInCode_ErrorinservicingthecCode_722
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_ErrorinservicingthecCode_723() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_ErrorinservicingthecCode_723)
	return EnumEpayPayInCode_ErrorinservicingthecCode_723
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_ErrorinservicingthecCode_724() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_ErrorinservicingthecCode_724)
	return EnumEpayPayInCode_ErrorinservicingthecCode_724
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_ErrorinservicingthecCode_725() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_ErrorinservicingthecCode_725)
	return EnumEpayPayInCode_ErrorinservicingthecCode_725
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_ErrorinservicingthecCode_726() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_ErrorinservicingthecCode_726)
	return EnumEpayPayInCode_ErrorinservicingthecCode_726
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_ErrorinservicingthecCode_727() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_ErrorinservicingthecCode_727)
	return EnumEpayPayInCode_ErrorinservicingthecCode_727
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_NoAuthorizeCode_728() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_NoAuthorizeCode_728)
	return EnumEpayPayInCode_NoAuthorizeCode_728
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_BadtokenCode_729() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_BadtokenCode_729)
	return EnumEpayPayInCode_BadtokenCode_729
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_730() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_730)
	return EnumEpayPayInCode_Code_730
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_InvalidrequestinputCode_731() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_InvalidrequestinputCode_731)
	return EnumEpayPayInCode_InvalidrequestinputCode_731
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_732() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_732)
	return EnumEpayPayInCode_Code_732
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_733() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_733)
	return EnumEpayPayInCode_Code_733
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_734() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_734)
	return EnumEpayPayInCode_Code_734
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_735() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_735)
	return EnumEpayPayInCode_Code_735
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_736() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_736)
	return EnumEpayPayInCode_Code_736
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_IdCode_737() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_IdCode_737)
	return EnumEpayPayInCode_IdCode_737
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_738() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_738)
	return EnumEpayPayInCode_Code_738
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_739() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_739)
	return EnumEpayPayInCode_Code_739
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_740() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_740)
	return EnumEpayPayInCode_Code_740
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_741() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_741)
	return EnumEpayPayInCode_Code_741
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_742() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_742)
	return EnumEpayPayInCode_Code_742
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_743() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_743)
	return EnumEpayPayInCode_Code_743
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_744() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_744)
	return EnumEpayPayInCode_Code_744
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_745() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_745)
	return EnumEpayPayInCode_Code_745
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_746() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_746)
	return EnumEpayPayInCode_Code_746
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_747() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_747)
	return EnumEpayPayInCode_Code_747
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_748() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_748)
	return EnumEpayPayInCode_Code_748
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_749() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_749)
	return EnumEpayPayInCode_Code_749
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_750() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_750)
	return EnumEpayPayInCode_Code_750
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_751() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_751)
	return EnumEpayPayInCode_Code_751
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_752() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_752)
	return EnumEpayPayInCode_Code_752
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_753() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_753)
	return EnumEpayPayInCode_Code_753
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_ApimakePaymentscorerCode_754() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_ApimakePaymentscorerCode_754)
	return EnumEpayPayInCode_ApimakePaymentscorerCode_754
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_756() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_756)
	return EnumEpayPayInCode_Code_756
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_757() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_757)
	return EnumEpayPayInCode_Code_757
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_MerchantCode_758() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_MerchantCode_758)
	return EnumEpayPayInCode_MerchantCode_758
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_759() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_759)
	return EnumEpayPayInCode_Code_759
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_760() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_760)
	return EnumEpayPayInCode_Code_760
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_761() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_761)
	return EnumEpayPayInCode_Code_761
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_762() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_762)
	return EnumEpayPayInCode_Code_762
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_765() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_765)
	return EnumEpayPayInCode_Code_765
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_EgatewayCode_766() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_EgatewayCode_766)
	return EnumEpayPayInCode_EgatewayCode_766
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_767() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_767)
	return EnumEpayPayInCode_Code_767
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_768() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_768)
	return EnumEpayPayInCode_Code_768
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_769() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_769)
	return EnumEpayPayInCode_Code_769
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_770() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_770)
	return EnumEpayPayInCode_Code_770
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_771() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_771)
	return EnumEpayPayInCode_Code_771
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_772() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_772)
	return EnumEpayPayInCode_Code_772
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_773() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_773)
	return EnumEpayPayInCode_Code_773
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_774() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_774)
	return EnumEpayPayInCode_Code_774
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_775() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_775)
	return EnumEpayPayInCode_Code_775
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_InvoiceIDCode_776() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_InvoiceIDCode_776)
	return EnumEpayPayInCode_InvoiceIDCode_776
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_TerminalCode_777() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_TerminalCode_777)
	return EnumEpayPayInCode_TerminalCode_777
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_CurrencyCode_778() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_CurrencyCode_778)
	return EnumEpayPayInCode_CurrencyCode_778
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_AmountCode_779() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_AmountCode_779)
	return EnumEpayPayInCode_AmountCode_779
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_AmountCode_780() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_AmountCode_780)
	return EnumEpayPayInCode_AmountCode_780
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_CorepaymentCode_781() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_CorepaymentCode_781)
	return EnumEpayPayInCode_CorepaymentCode_781
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_782() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_782)
	return EnumEpayPayInCode_Code_782
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_783() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_783)
	return EnumEpayPayInCode_Code_783
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_784() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_784)
	return EnumEpayPayInCode_Code_784
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_IdCode_785() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_IdCode_785)
	return EnumEpayPayInCode_IdCode_785
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_TerminalIdCode_786() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_TerminalIdCode_786)
	return EnumEpayPayInCode_TerminalIdCode_786
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_787() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_787)
	return EnumEpayPayInCode_Code_787
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_789() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_789)
	return EnumEpayPayInCode_Code_789
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_790() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_790)
	return EnumEpayPayInCode_Code_790
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_791() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_791)
	return EnumEpayPayInCode_Code_791
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_792() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_792)
	return EnumEpayPayInCode_Code_792
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_793() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_793)
	return EnumEpayPayInCode_Code_793
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_795() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_795)
	return EnumEpayPayInCode_Code_795
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_CorerecurrentCode_796() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_CorerecurrentCode_796)
	return EnumEpayPayInCode_CorerecurrentCode_796
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_797() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_797)
	return EnumEpayPayInCode_Code_797
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_798() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_798)
	return EnumEpayPayInCode_Code_798
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_799() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_799)
	return EnumEpayPayInCode_Code_799
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_800() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_800)
	return EnumEpayPayInCode_Code_800
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_801() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_801)
	return EnumEpayPayInCode_Code_801
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_802() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_802)
	return EnumEpayPayInCode_Code_802
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_803() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_803)
	return EnumEpayPayInCode_Code_803
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_804() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_804)
	return EnumEpayPayInCode_Code_804
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_805() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_805)
	return EnumEpayPayInCode_Code_805
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_PublicIDCode_806() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_PublicIDCode_806)
	return EnumEpayPayInCode_PublicIDCode_806
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_807() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_807)
	return EnumEpayPayInCode_Code_807
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_808() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_808)
	return EnumEpayPayInCode_Code_808
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_809() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_809)
	return EnumEpayPayInCode_Code_809
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_810() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_810)
	return EnumEpayPayInCode_Code_810
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_811() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_811)
	return EnumEpayPayInCode_Code_811
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_812() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_812)
	return EnumEpayPayInCode_Code_812
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_813() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_813)
	return EnumEpayPayInCode_Code_813
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_814() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_814)
	return EnumEpayPayInCode_Code_814
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_815() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_815)
	return EnumEpayPayInCode_Code_815
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_816() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_816)
	return EnumEpayPayInCode_Code_816
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_817() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_817)
	return EnumEpayPayInCode_Code_817
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_818() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_818)
	return EnumEpayPayInCode_Code_818
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_819() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_819)
	return EnumEpayPayInCode_Code_819
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_820() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_820)
	return EnumEpayPayInCode_Code_820
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_821() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_821)
	return EnumEpayPayInCode_Code_821
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_822() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_822)
	return EnumEpayPayInCode_Code_822
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_823() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_823)
	return EnumEpayPayInCode_Code_823
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_824() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_824)
	return EnumEpayPayInCode_Code_824
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_825() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_825)
	return EnumEpayPayInCode_Code_825
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_826() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_826)
	return EnumEpayPayInCode_Code_826
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_827() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_827)
	return EnumEpayPayInCode_Code_827
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_828() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_828)
	return EnumEpayPayInCode_Code_828
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_829() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_829)
	return EnumEpayPayInCode_Code_829
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_830() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_830)
	return EnumEpayPayInCode_Code_830
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_831() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_831)
	return EnumEpayPayInCode_Code_831
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_832() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_832)
	return EnumEpayPayInCode_Code_832
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_833() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_833)
	return EnumEpayPayInCode_Code_833
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_834() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_834)
	return EnumEpayPayInCode_Code_834
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_835() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_835)
	return EnumEpayPayInCode_Code_835
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_836() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_836)
	return EnumEpayPayInCode_Code_836
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_837() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_837)
	return EnumEpayPayInCode_Code_837
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_838() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_838)
	return EnumEpayPayInCode_Code_838
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_839() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_839)
	return EnumEpayPayInCode_Code_839
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_840() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_840)
	return EnumEpayPayInCode_Code_840
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_841() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_841)
	return EnumEpayPayInCode_Code_841
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_842() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_842)
	return EnumEpayPayInCode_Code_842
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_843() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_843)
	return EnumEpayPayInCode_Code_843
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_844() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_844)
	return EnumEpayPayInCode_Code_844
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_845() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_845)
	return EnumEpayPayInCode_Code_845
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_PublicIdCode_846() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_PublicIdCode_846)
	return EnumEpayPayInCode_PublicIdCode_846
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_MerchantAPICode_847() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_MerchantAPICode_847)
	return EnumEpayPayInCode_MerchantAPICode_847
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_848() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_848)
	return EnumEpayPayInCode_Code_848
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_849() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_849)
	return EnumEpayPayInCode_Code_849
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_850() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_850)
	return EnumEpayPayInCode_Code_850
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_StaffInviteRequestCode_851() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_StaffInviteRequestCode_851)
	return EnumEpayPayInCode_StaffInviteRequestCode_851
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_PublicIdCode_852() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_PublicIdCode_852)
	return EnumEpayPayInCode_PublicIdCode_852
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_853() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_853)
	return EnumEpayPayInCode_Code_853
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_854() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_854)
	return EnumEpayPayInCode_Code_854
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_PublicIdCode_855() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_PublicIdCode_855)
	return EnumEpayPayInCode_PublicIdCode_855
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_PublicIdCode_856() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_PublicIdCode_856)
	return EnumEpayPayInCode_PublicIdCode_856
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_857() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_857)
	return EnumEpayPayInCode_Code_857
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_PublicIdCode_858() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_PublicIdCode_858)
	return EnumEpayPayInCode_PublicIdCode_858
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_859() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_859)
	return EnumEpayPayInCode_Code_859
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_860() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_860)
	return EnumEpayPayInCode_Code_860
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_861() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_861)
	return EnumEpayPayInCode_Code_861
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_862() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_862)
	return EnumEpayPayInCode_Code_862
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_863() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_863)
	return EnumEpayPayInCode_Code_863
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_864() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_864)
	return EnumEpayPayInCode_Code_864
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_865() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_865)
	return EnumEpayPayInCode_Code_865
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_OauthCode_866() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_OauthCode_866)
	return EnumEpayPayInCode_OauthCode_866
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_867() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_867)
	return EnumEpayPayInCode_Code_867
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_868() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_868)
	return EnumEpayPayInCode_Code_868
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_869() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_869)
	return EnumEpayPayInCode_Code_869
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_870() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_870)
	return EnumEpayPayInCode_Code_870
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_871() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_871)
	return EnumEpayPayInCode_Code_871
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_872() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_872)
	return EnumEpayPayInCode_Code_872
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_OauthCode_873() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_OauthCode_873)
	return EnumEpayPayInCode_OauthCode_873
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_874() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_874)
	return EnumEpayPayInCode_Code_874
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_OauthCode_875() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_OauthCode_875)
	return EnumEpayPayInCode_OauthCode_875
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_876() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_876)
	return EnumEpayPayInCode_Code_876
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_MDCode_877() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_MDCode_877)
	return EnumEpayPayInCode_MDCode_877
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_878() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_878)
	return EnumEpayPayInCode_Code_878
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_879() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_879)
	return EnumEpayPayInCode_Code_879
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_880() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_880)
	return EnumEpayPayInCode_Code_880
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_881() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_881)
	return EnumEpayPayInCode_Code_881
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_EGWCode_882() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_EGWCode_882)
	return EnumEpayPayInCode_EGWCode_882
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_RedisCode_883() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_RedisCode_883)
	return EnumEpayPayInCode_RedisCode_883
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_RedisCode_884() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_RedisCode_884)
	return EnumEpayPayInCode_RedisCode_884
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_RedisCode_885() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_RedisCode_885)
	return EnumEpayPayInCode_RedisCode_885
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_RedisCode_886() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_RedisCode_886)
	return EnumEpayPayInCode_RedisCode_886
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_887() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_887)
	return EnumEpayPayInCode_Code_887
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_AclapiCode_888() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_AclapiCode_888)
	return EnumEpayPayInCode_AclapiCode_888
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_889() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_889)
	return EnumEpayPayInCode_Code_889
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_MerchantapiCode_890() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_MerchantapiCode_890)
	return EnumEpayPayInCode_MerchantapiCode_890
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_891() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_891)
	return EnumEpayPayInCode_Code_891
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_892() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_892)
	return EnumEpayPayInCode_Code_892
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_MerchantapiCode_893() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_MerchantapiCode_893)
	return EnumEpayPayInCode_MerchantapiCode_893
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_894() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_894)
	return EnumEpayPayInCode_Code_894
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_CorepaymentCode_895() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_CorepaymentCode_895)
	return EnumEpayPayInCode_CorepaymentCode_895
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_ConfirmCode_896() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_ConfirmCode_896)
	return EnumEpayPayInCode_ConfirmCode_896
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_897() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_897)
	return EnumEpayPayInCode_Code_897
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_PublicIDCode_898() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_PublicIDCode_898)
	return EnumEpayPayInCode_PublicIDCode_898
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_899() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_899)
	return EnumEpayPayInCode_Code_899
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_900() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_900)
	return EnumEpayPayInCode_Code_900
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_901() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_901)
	return EnumEpayPayInCode_Code_901
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_PublicIDCode_902() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_PublicIDCode_902)
	return EnumEpayPayInCode_PublicIDCode_902
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_903() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_903)
	return EnumEpayPayInCode_Code_903
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_904() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_904)
	return EnumEpayPayInCode_Code_904
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_905() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_905)
	return EnumEpayPayInCode_Code_905
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_906() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_906)
	return EnumEpayPayInCode_Code_906
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_907() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_907)
	return EnumEpayPayInCode_Code_907
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_908() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_908)
	return EnumEpayPayInCode_Code_908
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_MerchantapiCode_909() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_MerchantapiCode_909)
	return EnumEpayPayInCode_MerchantapiCode_909
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_MerchantapiCode_910() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_MerchantapiCode_910)
	return EnumEpayPayInCode_MerchantapiCode_910
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_911() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_911)
	return EnumEpayPayInCode_Code_911
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_912() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_912)
	return EnumEpayPayInCode_Code_912
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_913() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_913)
	return EnumEpayPayInCode_Code_913
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_914() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_914)
	return EnumEpayPayInCode_Code_914
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_915() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_915)
	return EnumEpayPayInCode_Code_915
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_916() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_916)
	return EnumEpayPayInCode_Code_916
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_917() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_917)
	return EnumEpayPayInCode_Code_917
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_918() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_918)
	return EnumEpayPayInCode_Code_918
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_919() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_919)
	return EnumEpayPayInCode_Code_919
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_920() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_920)
	return EnumEpayPayInCode_Code_920
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_921() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_921)
	return EnumEpayPayInCode_Code_921
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_922() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_922)
	return EnumEpayPayInCode_Code_922
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_923() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_923)
	return EnumEpayPayInCode_Code_923
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_924() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_924)
	return EnumEpayPayInCode_Code_924
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_925() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_925)
	return EnumEpayPayInCode_Code_925
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_926() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_926)
	return EnumEpayPayInCode_Code_926
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_927() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_927)
	return EnumEpayPayInCode_Code_927
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_928() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_928)
	return EnumEpayPayInCode_Code_928
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_PublicIDCode_929() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_PublicIDCode_929)
	return EnumEpayPayInCode_PublicIDCode_929
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_930() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_930)
	return EnumEpayPayInCode_Code_930
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_931() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_931)
	return EnumEpayPayInCode_Code_931
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_ScopesCode_932() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_ScopesCode_932)
	return EnumEpayPayInCode_ScopesCode_932
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_933() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_933)
	return EnumEpayPayInCode_Code_933
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_934() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_934)
	return EnumEpayPayInCode_Code_934
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_935() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_935)
	return EnumEpayPayInCode_Code_935
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_937() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_937)
	return EnumEpayPayInCode_Code_937
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_938() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_938)
	return EnumEpayPayInCode_Code_938
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_939() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_939)
	return EnumEpayPayInCode_Code_939
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_940() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_940)
	return EnumEpayPayInCode_Code_940
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_941() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_941)
	return EnumEpayPayInCode_Code_941
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_942() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_942)
	return EnumEpayPayInCode_Code_942
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_943() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_943)
	return EnumEpayPayInCode_Code_943
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_944() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_944)
	return EnumEpayPayInCode_Code_944
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_PublicIDCode_945() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_PublicIDCode_945)
	return EnumEpayPayInCode_PublicIDCode_945
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_946() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_946)
	return EnumEpayPayInCode_Code_946
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_947() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_947)
	return EnumEpayPayInCode_Code_947
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_948() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_948)
	return EnumEpayPayInCode_Code_948
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_949() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_949)
	return EnumEpayPayInCode_Code_949
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_950() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_950)
	return EnumEpayPayInCode_Code_950
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_PublicIdCode_951() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_PublicIdCode_951)
	return EnumEpayPayInCode_PublicIdCode_951
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_952() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_952)
	return EnumEpayPayInCode_Code_952
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_953() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_953)
	return EnumEpayPayInCode_Code_953
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_954() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_954)
	return EnumEpayPayInCode_Code_954
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_955() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_955)
	return EnumEpayPayInCode_Code_955
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_956() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_956)
	return EnumEpayPayInCode_Code_956
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_957() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_957)
	return EnumEpayPayInCode_Code_957
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_958() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_958)
	return EnumEpayPayInCode_Code_958
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_959() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_959)
	return EnumEpayPayInCode_Code_959
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_960() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_960)
	return EnumEpayPayInCode_Code_960
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_961() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_961)
	return EnumEpayPayInCode_Code_961
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_962() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_962)
	return EnumEpayPayInCode_Code_962
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_AuthorizedRequestJSOCode_963() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_AuthorizedRequestJSOCode_963)
	return EnumEpayPayInCode_AuthorizedRequestJSOCode_963
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_ClientsecretCode_964() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_ClientsecretCode_964)
	return EnumEpayPayInCode_ClientsecretCode_964
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_ClientidclientsecretCode_965() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_ClientidclientsecretCode_965)
	return EnumEpayPayInCode_ClientidclientsecretCode_965
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_MerchantIDclientsecrCode_966() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_MerchantIDclientsecrCode_966)
	return EnumEpayPayInCode_MerchantIDclientsecrCode_966
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_ClientsecretclientIDCode_967() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_ClientsecretclientIDCode_967)
	return EnumEpayPayInCode_ClientsecretclientIDCode_967
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_EmailclientIDclientsCode_968() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_EmailclientIDclientsCode_968)
	return EnumEpayPayInCode_EmailclientIDclientsCode_968
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_969() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_969)
	return EnumEpayPayInCode_Code_969
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_970() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_970)
	return EnumEpayPayInCode_Code_970
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_971() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_971)
	return EnumEpayPayInCode_Code_971
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_972() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_972)
	return EnumEpayPayInCode_Code_972
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_IDCode_973() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_IDCode_973)
	return EnumEpayPayInCode_IDCode_973
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_974() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_974)
	return EnumEpayPayInCode_Code_974
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_975() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_975)
	return EnumEpayPayInCode_Code_975
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_UserclientIDclientseCode_976() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_UserclientIDclientseCode_976)
	return EnumEpayPayInCode_UserclientIDclientseCode_976
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_AuthorizedRequestJSOCode_977() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_AuthorizedRequestJSOCode_977)
	return EnumEpayPayInCode_AuthorizedRequestJSOCode_977
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_JsonMarshalsendEmailCode_978() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_JsonMarshalsendEmailCode_978)
	return EnumEpayPayInCode_JsonMarshalsendEmailCode_978
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_979() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_979)
	return EnumEpayPayInCode_Code_979
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_FormFileCode_980() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_FormFileCode_980)
	return EnumEpayPayInCode_FormFileCode_980
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_981() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_981)
	return EnumEpayPayInCode_Code_981
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_982() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_982)
	return EnumEpayPayInCode_Code_982
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_983() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_983)
	return EnumEpayPayInCode_Code_983
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_984() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_984)
	return EnumEpayPayInCode_Code_984
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_985() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_985)
	return EnumEpayPayInCode_Code_985
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_986() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_986)
	return EnumEpayPayInCode_Code_986
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_987() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_987)
	return EnumEpayPayInCode_Code_987
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_988() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_988)
	return EnumEpayPayInCode_Code_988
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_989() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_989)
	return EnumEpayPayInCode_Code_989
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_990() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_990)
	return EnumEpayPayInCode_Code_990
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_991() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_991)
	return EnumEpayPayInCode_Code_991
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_992() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_992)
	return EnumEpayPayInCode_Code_992
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_ServiceCode_993() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_ServiceCode_993)
	return EnumEpayPayInCode_ServiceCode_993
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_ShouldBindJSONservicCode_994() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_ShouldBindJSONservicCode_994)
	return EnumEpayPayInCode_ShouldBindJSONservicCode_994
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_AuthorizedRequestJSOCode_995() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_AuthorizedRequestJSOCode_995)
	return EnumEpayPayInCode_AuthorizedRequestJSOCode_995
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_AuthorizedRequestJSOCode_996() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_AuthorizedRequestJSOCode_996)
	return EnumEpayPayInCode_AuthorizedRequestJSOCode_996
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_UseruserusernameservCode_997() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_UseruserusernameservCode_997)
	return EnumEpayPayInCode_UseruserusernameservCode_997
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_MerchantserviceCode_999() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_MerchantserviceCode_999)
	return EnumEpayPayInCode_MerchantserviceCode_999
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_NamewebserviceCode_1000() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_NamewebserviceCode_1000)
	return EnumEpayPayInCode_NamewebserviceCode_1000
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_ContactTypenameserviCode_1001() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_ContactTypenameserviCode_1001)
	return EnumEpayPayInCode_ContactTypenameserviCode_1001
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_PublicIdCode_1002() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_PublicIdCode_1002)
	return EnumEpayPayInCode_PublicIdCode_1002
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_1003() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_1003)
	return EnumEpayPayInCode_Code_1003
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_1004() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_1004)
	return EnumEpayPayInCode_Code_1004
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_1005() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_1005)
	return EnumEpayPayInCode_Code_1005
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_OauthCode_1006() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_OauthCode_1006)
	return EnumEpayPayInCode_OauthCode_1006
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_1007() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_1007)
	return EnumEpayPayInCode_Code_1007
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_1008() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_1008)
	return EnumEpayPayInCode_Code_1008
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_1009() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_1009)
	return EnumEpayPayInCode_Code_1009
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_IdserviceCode_1010() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_IdserviceCode_1010)
	return EnumEpayPayInCode_IdserviceCode_1010
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_RSAserviceCode_1011() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_RSAserviceCode_1011)
	return EnumEpayPayInCode_RSAserviceCode_1011
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_ServiceCode_1012() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_ServiceCode_1012)
	return EnumEpayPayInCode_ServiceCode_1012
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Codebase64stringbaseCode_1013() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Codebase64stringbaseCode_1013)
	return EnumEpayPayInCode_Codebase64stringbaseCode_1013
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_XmlUnmarshalserviceCode_1014() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_XmlUnmarshalserviceCode_1014)
	return EnumEpayPayInCode_XmlUnmarshalserviceCode_1014
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Codebase64stringbaseCode_1015() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Codebase64stringbaseCode_1015)
	return EnumEpayPayInCode_Codebase64stringbaseCode_1015
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_XmlMarshalserviceCode_1016() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_XmlMarshalserviceCode_1016)
	return EnumEpayPayInCode_XmlMarshalserviceCode_1016
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_OAUTHCode_1017() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_OAUTHCode_1017)
	return EnumEpayPayInCode_OAUTHCode_1017
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_ServiceCode_1018() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_ServiceCode_1018)
	return EnumEpayPayInCode_ServiceCode_1018
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_ShopidCode_1019() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_ShopidCode_1019)
	return EnumEpayPayInCode_ShopidCode_1019
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_JsonserviceCode_1020() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_JsonserviceCode_1020)
	return EnumEpayPayInCode_JsonserviceCode_1020
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_AuthserviceCode_1021() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_AuthserviceCode_1021)
	return EnumEpayPayInCode_AuthserviceCode_1021
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_DeviceIDauthserviceCode_1022() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_DeviceIDauthserviceCode_1022)
	return EnumEpayPayInCode_DeviceIDauthserviceCode_1022
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_ServiceCode_1023() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_ServiceCode_1023)
	return EnumEpayPayInCode_ServiceCode_1023
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_IdserviceCode_1024() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_IdserviceCode_1024)
	return EnumEpayPayInCode_IdserviceCode_1024
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_BindXMLwebserviceCode_1025() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_BindXMLwebserviceCode_1025)
	return EnumEpayPayInCode_BindXMLwebserviceCode_1025
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_AuthorizedRequestJSOCode_1026() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_AuthorizedRequestJSOCode_1026)
	return EnumEpayPayInCode_AuthorizedRequestJSOCode_1026
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_BindclientIDpostLinkCode_1027() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_BindclientIDpostLinkCode_1027)
	return EnumEpayPayInCode_BindclientIDpostLinkCode_1027
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_ClientIDpostLinkInfoCode_1028() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_ClientIDpostLinkInfoCode_1028)
	return EnumEpayPayInCode_ClientIDpostLinkInfoCode_1028
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_MerchantIDpostLinkInCode_1029() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_MerchantIDpostLinkInCode_1029)
	return EnumEpayPayInCode_MerchantIDpostLinkInCode_1029
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_PostlinkrabbitservicCode_1030() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_PostlinkrabbitservicCode_1030)
	return EnumEpayPayInCode_PostlinkrabbitservicCode_1030
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_TerminalidpostLinkInCode_1031() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_TerminalidpostLinkInCode_1031)
	return EnumEpayPayInCode_TerminalidpostLinkInCode_1031
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_CBindpostLinkInfoposCode_1032() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_CBindpostLinkInfoposCode_1032)
	return EnumEpayPayInCode_CBindpostLinkInfoposCode_1032
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_JsonMarshalpostlinkICode_1033() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_JsonMarshalpostlinkICode_1033)
	return EnumEpayPayInCode_JsonMarshalpostlinkICode_1033
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_ClientIDclientIDservCode_1034() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_ClientIDclientIDservCode_1034)
	return EnumEpayPayInCode_ClientIDclientIDservCode_1034
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_ShopmerchantIDclientCode_1035() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_ShopmerchantIDclientCode_1035)
	return EnumEpayPayInCode_ShopmerchantIDclientCode_1035
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_ShopMerchantIDclientCode_1036() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_ShopMerchantIDclientCode_1036)
	return EnumEpayPayInCode_ShopMerchantIDclientCode_1036
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_BindinvoicewebservicCode_1037() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_BindinvoicewebservicCode_1037)
	return EnumEpayPayInCode_BindinvoicewebservicCode_1037
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_ExpirePeriodinvoicesCode_1038() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_ExpirePeriodinvoicesCode_1038)
	return EnumEpayPayInCode_ExpirePeriodinvoicesCode_1038
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_PostlinkRabbitservicCode_1039() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_PostlinkRabbitservicCode_1039)
	return EnumEpayPayInCode_PostlinkRabbitservicCode_1039
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_ServiceCode_1040() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_ServiceCode_1040)
	return EnumEpayPayInCode_ServiceCode_1040
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_JsonserviceCode_1041() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_JsonserviceCode_1041)
	return EnumEpayPayInCode_JsonserviceCode_1041
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_EgwCode_1042() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_EgwCode_1042)
	return EnumEpayPayInCode_EgwCode_1042
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_EmailclientsecretserCode_1043() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_EmailclientsecretserCode_1043)
	return EnumEpayPayInCode_EmailclientsecretserCode_1043
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_EmailwebserviceCode_1044() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_EmailwebserviceCode_1044)
	return EnumEpayPayInCode_EmailwebserviceCode_1044
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_AuthorizedRequestJSOCode_1045() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_AuthorizedRequestJSOCode_1045)
	return EnumEpayPayInCode_AuthorizedRequestJSOCode_1045
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_JsonMarshalmerchantoCode_1046() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_JsonMarshalmerchantoCode_1046)
	return EnumEpayPayInCode_JsonMarshalmerchantoCode_1046
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_JsonMarshalmerchantoCode_1047() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_JsonMarshalmerchantoCode_1047)
	return EnumEpayPayInCode_JsonMarshalmerchantoCode_1047
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_BindJSONmerchantwebsCode_1048() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_BindJSONmerchantwebsCode_1048)
	return EnumEpayPayInCode_BindJSONmerchantwebsCode_1048
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_ClientidserviceCode_1049() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_ClientidserviceCode_1049)
	return EnumEpayPayInCode_ClientidserviceCode_1049
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_ClientScopesclientIDCode_1050() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_ClientScopesclientIDCode_1050)
	return EnumEpayPayInCode_ClientScopesclientIDCode_1050
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_UserloginserviceCode_1051() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_UserloginserviceCode_1051)
	return EnumEpayPayInCode_UserloginserviceCode_1051
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_ClientclientscopesusCode_1052() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_ClientclientscopesusCode_1052)
	return EnumEpayPayInCode_ClientclientscopesusCode_1052
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_BindJSONinvoiceCode_1053() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_BindJSONinvoiceCode_1053)
	return EnumEpayPayInCode_BindJSONinvoiceCode_1053
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_InvoiceCode_1054() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_InvoiceCode_1054)
	return EnumEpayPayInCode_InvoiceCode_1054
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_XMLway4Code_1055() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_XMLway4Code_1055)
	return EnumEpayPayInCode_XMLway4Code_1055
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_InvoiceCode_1056() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_InvoiceCode_1056)
	return EnumEpayPayInCode_InvoiceCode_1056
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_1057() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_1057)
	return EnumEpayPayInCode_Code_1057
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_1058() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_1058)
	return EnumEpayPayInCode_Code_1058
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_1059() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_1059)
	return EnumEpayPayInCode_Code_1059
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_CoreinvoiceCode_1060() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_CoreinvoiceCode_1060)
	return EnumEpayPayInCode_CoreinvoiceCode_1060
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_BindJSONinvoiceInfoCode_1061() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_BindJSONinvoiceInfoCode_1061)
	return EnumEpayPayInCode_BindJSONinvoiceInfoCode_1061
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_1062() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_1062)
	return EnumEpayPayInCode_Code_1062
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_1063() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_1063)
	return EnumEpayPayInCode_Code_1063
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_ScannererrCode_1064() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_ScannererrCode_1064)
	return EnumEpayPayInCode_ScannererrCode_1064
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_BindCode_1065() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_BindCode_1065)
	return EnumEpayPayInCode_BindCode_1065
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_ShopIdCode_1066() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_ShopIdCode_1066)
	return EnumEpayPayInCode_ShopIdCode_1066
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_InvoiceLinkCode_1067() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_InvoiceLinkCode_1067)
	return EnumEpayPayInCode_InvoiceLinkCode_1067
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_MarshalinvoicelinkCode_1068() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_MarshalinvoicelinkCode_1068)
	return EnumEpayPayInCode_MarshalinvoicelinkCode_1068
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_InvoiceLinkCode_1069() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_InvoiceLinkCode_1069)
	return EnumEpayPayInCode_InvoiceLinkCode_1069
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_InvoiceLinkCode_1070() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_InvoiceLinkCode_1070)
	return EnumEpayPayInCode_InvoiceLinkCode_1070
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_InvoiceLinkCode_1071() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_InvoiceLinkCode_1071)
	return EnumEpayPayInCode_InvoiceLinkCode_1071
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_PublicIDinvoiceCode_1072() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_PublicIDinvoiceCode_1072)
	return EnumEpayPayInCode_PublicIDinvoiceCode_1072
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_ParamsinvoiceCode_1073() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_ParamsinvoiceCode_1073)
	return EnumEpayPayInCode_ParamsinvoiceCode_1073
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_InvoiceLinkCode_1074() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_InvoiceLinkCode_1074)
	return EnumEpayPayInCode_InvoiceLinkCode_1074
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_BindCode_1075() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_BindCode_1075)
	return EnumEpayPayInCode_BindCode_1075
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_StrconvAtoiExpirePerCode_1076() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_StrconvAtoiExpirePerCode_1076)
	return EnumEpayPayInCode_StrconvAtoiExpirePerCode_1076
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_InvoiceLinkCode_1077() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_InvoiceLinkCode_1077)
	return EnumEpayPayInCode_InvoiceLinkCode_1077
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_EmailCode_1078() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_EmailCode_1078)
	return EnumEpayPayInCode_EmailCode_1078
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_SmsCode_1079() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_SmsCode_1079)
	return EnumEpayPayInCode_SmsCode_1079
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_InvoiceLinkCode_1080() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_InvoiceLinkCode_1080)
	return EnumEpayPayInCode_InvoiceLinkCode_1080
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_BindCode_1081() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_BindCode_1081)
	return EnumEpayPayInCode_BindCode_1081
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_PublicidCode_1082() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_PublicidCode_1082)
	return EnumEpayPayInCode_PublicidCode_1082
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_NameinvoicelinkCode_1083() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_NameinvoicelinkCode_1083)
	return EnumEpayPayInCode_NameinvoicelinkCode_1083
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_InvoiceLinkCode_1084() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_InvoiceLinkCode_1084)
	return EnumEpayPayInCode_InvoiceLinkCode_1084
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_SearchparamsinvoicelCode_1085() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_SearchparamsinvoicelCode_1085)
	return EnumEpayPayInCode_SearchparamsinvoicelCode_1085
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_IncorrectsearchmethoCode_1086() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_IncorrectsearchmethoCode_1086)
	return EnumEpayPayInCode_IncorrectsearchmethoCode_1086
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_1087() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_1087)
	return EnumEpayPayInCode_Code_1087
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_InvoiceLinkidCode_1088() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_InvoiceLinkidCode_1088)
	return EnumEpayPayInCode_InvoiceLinkidCode_1088
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_InvoiceLinkCode_1089() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_InvoiceLinkCode_1089)
	return EnumEpayPayInCode_InvoiceLinkCode_1089
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_1090() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_1090)
	return EnumEpayPayInCode_Code_1090
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_InvoiceLinkCode_1091() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_InvoiceLinkCode_1091)
	return EnumEpayPayInCode_InvoiceLinkCode_1091
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_PublicIDCode_1092() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_PublicIDCode_1092)
	return EnumEpayPayInCode_PublicIDCode_1092
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_1093() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_1093)
	return EnumEpayPayInCode_Code_1093
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_1096() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_1096)
	return EnumEpayPayInCode_Code_1096
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_HomeBankCode_1097() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_HomeBankCode_1097)
	return EnumEpayPayInCode_HomeBankCode_1097
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_EmailCode_1098() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_EmailCode_1098)
	return EnumEpayPayInCode_EmailCode_1098
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_1099() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_1099)
	return EnumEpayPayInCode_Code_1099
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_1100() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_1100)
	return EnumEpayPayInCode_Code_1100
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_CoreinvoiceCode_1101() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_CoreinvoiceCode_1101)
	return EnumEpayPayInCode_CoreinvoiceCode_1101
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_AuthorizedRequestJSOCode_1103() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_AuthorizedRequestJSOCode_1103)
	return EnumEpayPayInCode_AuthorizedRequestJSOCode_1103
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_AuthorizedRequestJSOCode_1104() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_AuthorizedRequestJSOCode_1104)
	return EnumEpayPayInCode_AuthorizedRequestJSOCode_1104
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_IdserviceCode_1105() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_IdserviceCode_1105)
	return EnumEpayPayInCode_IdserviceCode_1105
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_ClientIDserviceCode_1106() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_ClientIDserviceCode_1106)
	return EnumEpayPayInCode_ClientIDserviceCode_1106
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_TerminalXLSMIDterminCode_1107() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_TerminalXLSMIDterminCode_1107)
	return EnumEpayPayInCode_TerminalXLSMIDterminCode_1107
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_1130() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_1130)
	return EnumEpayPayInCode_Code_1130
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_1132() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_1132)
	return EnumEpayPayInCode_Code_1132
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_NameCode_1133() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_NameCode_1133)
	return EnumEpayPayInCode_NameCode_1133
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_PostlinkcorecallbackCode_1144() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_PostlinkcorecallbackCode_1144)
	return EnumEpayPayInCode_PostlinkcorecallbackCode_1144
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_1145() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_1145)
	return EnumEpayPayInCode_Code_1145
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_CannotsendpostlinkCode_1146() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_CannotsendpostlinkCode_1146)
	return EnumEpayPayInCode_CannotsendpostlinkCode_1146
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_CannotsendpostlinkCode_1147() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_CannotsendpostlinkCode_1147)
	return EnumEpayPayInCode_CannotsendpostlinkCode_1147
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_BindJsonCode_1148() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_BindJsonCode_1148)
	return EnumEpayPayInCode_BindJsonCode_1148
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Base64Code_1149() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Base64Code_1149)
	return EnumEpayPayInCode_Base64Code_1149
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_UnmarshalCode_1150() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_UnmarshalCode_1150)
	return EnumEpayPayInCode_UnmarshalCode_1150
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_HttpcoremigrationCode_1151() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_HttpcoremigrationCode_1151)
	return EnumEpayPayInCode_HttpcoremigrationCode_1151
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_1152() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_1152)
	return EnumEpayPayInCode_Code_1152
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_UnmarshalouathCode_1153() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_UnmarshalouathCode_1153)
	return EnumEpayPayInCode_UnmarshalouathCode_1153
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_EPAY1Code_1154() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_EPAY1Code_1154)
	return EnumEpayPayInCode_EPAY1Code_1154
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_UnmarshalEPAY1Code_1155() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_UnmarshalEPAY1Code_1155)
	return EnumEpayPayInCode_UnmarshalEPAY1Code_1155
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_CannotsendpostlinkCode_1156() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_CannotsendpostlinkCode_1156)
	return EnumEpayPayInCode_CannotsendpostlinkCode_1156
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_1157() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_1157)
	return EnumEpayPayInCode_Code_1157
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_UserserviceCode_1158() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_UserserviceCode_1158)
	return EnumEpayPayInCode_UserserviceCode_1158
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_UserserviceCode_1159() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_UserserviceCode_1159)
	return EnumEpayPayInCode_UserserviceCode_1159
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_UserserviceCode_1160() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_UserserviceCode_1160)
	return EnumEpayPayInCode_UserserviceCode_1160
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_UserserviceCode_1161() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_UserserviceCode_1161)
	return EnumEpayPayInCode_UserserviceCode_1161
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_UserserviceCode_1162() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_UserserviceCode_1162)
	return EnumEpayPayInCode_UserserviceCode_1162
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_MerchantcompanynamesCode_1163() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_MerchantcompanynamesCode_1163)
	return EnumEpayPayInCode_MerchantcompanynamesCode_1163
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_MerchantserviceCode_1164() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_MerchantserviceCode_1164)
	return EnumEpayPayInCode_MerchantserviceCode_1164
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_MerchantmerchantCode_1165() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_MerchantmerchantCode_1165)
	return EnumEpayPayInCode_MerchantmerchantCode_1165
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_UserloginuserclientCode_1166() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_UserloginuserclientCode_1166)
	return EnumEpayPayInCode_UserloginuserclientCode_1166
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_MerchantnameuserclieCode_1167() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_MerchantnameuserclieCode_1167)
	return EnumEpayPayInCode_MerchantnameuserclieCode_1167
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_AuthorizedRequestJSOCode_1168() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_AuthorizedRequestJSOCode_1168)
	return EnumEpayPayInCode_AuthorizedRequestJSOCode_1168
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_MerchantnameCode_1169() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_MerchantnameCode_1169)
	return EnumEpayPayInCode_MerchantnameCode_1169
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_EmailmerchantCode_1170() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_EmailmerchantCode_1170)
	return EnumEpayPayInCode_EmailmerchantCode_1170
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_ShopmerchantCode_1171() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_ShopmerchantCode_1171)
	return EnumEpayPayInCode_ShopmerchantCode_1171
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_TerminalmerchantCode_1172() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_TerminalmerchantCode_1172)
	return EnumEpayPayInCode_TerminalmerchantCode_1172
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_UseruserCode_1173() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_UseruserCode_1173)
	return EnumEpayPayInCode_UseruserCode_1173
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_UserclientIDCode_1174() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_UserclientIDCode_1174)
	return EnumEpayPayInCode_UserclientIDCode_1174
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_UserclientuserCode_1175() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_UserclientuserCode_1175)
	return EnumEpayPayInCode_UserclientuserCode_1175
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_TerminalterminalIDCode_1176() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_TerminalterminalIDCode_1176)
	return EnumEpayPayInCode_TerminalterminalIDCode_1176
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_CurrencycurrencyNameCode_1177() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_CurrencycurrencyNameCode_1177)
	return EnumEpayPayInCode_CurrencycurrencyNameCode_1177
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_TerminalIDCode_1178() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_TerminalIDCode_1178)
	return EnumEpayPayInCode_TerminalIDCode_1178
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_TerminalterminalIDteCode_1179() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_TerminalterminalIDteCode_1179)
	return EnumEpayPayInCode_TerminalterminalIDteCode_1179
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_TerminalIDCode_1180() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_TerminalIDCode_1180)
	return EnumEpayPayInCode_TerminalIDCode_1180
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_AuthorizedRequestJSOCode_1182() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_AuthorizedRequestJSOCode_1182)
	return EnumEpayPayInCode_AuthorizedRequestJSOCode_1182
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_1184() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_1184)
	return EnumEpayPayInCode_Code_1184
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_TerminalCode_1185() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_TerminalCode_1185)
	return EnumEpayPayInCode_TerminalCode_1185
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_TerminalCode_1186() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_TerminalCode_1186)
	return EnumEpayPayInCode_TerminalCode_1186
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_1187() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_1187)
	return EnumEpayPayInCode_Code_1187
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_MerchantContactTypenCode_1188() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_MerchantContactTypenCode_1188)
	return EnumEpayPayInCode_MerchantContactTypenCode_1188
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_SenderrorreportcontaCode_1189() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_SenderrorreportcontaCode_1189)
	return EnumEpayPayInCode_SenderrorreportcontaCode_1189
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_1190() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_1190)
	return EnumEpayPayInCode_Code_1190
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_IdCode_1191() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_IdCode_1191)
	return EnumEpayPayInCode_IdCode_1191
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_ExcelCode_1192() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_ExcelCode_1192)
	return EnumEpayPayInCode_ExcelCode_1192
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_CurrencycurrencyIDCode_1193() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_CurrencycurrencyIDCode_1193)
	return EnumEpayPayInCode_CurrencycurrencyIDCode_1193
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_CardTypeCardTypeIDCode_1194() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_CardTypeCardTypeIDCode_1194)
	return EnumEpayPayInCode_CardTypeCardTypeIDCode_1194
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_EmailCode_1195() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_EmailCode_1195)
	return EnumEpayPayInCode_EmailCode_1195
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_JsonMarshalemailCode_1196() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_JsonMarshalemailCode_1196)
	return EnumEpayPayInCode_JsonMarshalemailCode_1196
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_AuthorizedRequestJSOCode_1197() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_AuthorizedRequestJSOCode_1197)
	return EnumEpayPayInCode_AuthorizedRequestJSOCode_1197
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_MerchantContactTypeeCode_1198() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_MerchantContactTypeeCode_1198)
	return EnumEpayPayInCode_MerchantContactTypeeCode_1198
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_MerchantContactemailCode_1199() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_MerchantContactemailCode_1199)
	return EnumEpayPayInCode_MerchantContactemailCode_1199
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_ExcelfileemailCode_1200() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_ExcelfileemailCode_1200)
	return EnumEpayPayInCode_ExcelfileemailCode_1200
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_1201() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_1201)
	return EnumEpayPayInCode_Code_1201
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_UserloginCode_1202() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_UserloginCode_1202)
	return EnumEpayPayInCode_UserloginCode_1202
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_RowsexcelfilereportCode_1203() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_RowsexcelfilereportCode_1203)
	return EnumEpayPayInCode_RowsexcelfilereportCode_1203
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_UserConnectionsuserICode_1205() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_UserConnectionsuserICode_1205)
	return EnumEpayPayInCode_UserConnectionsuserICode_1205
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_KafkaCode_1206() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_KafkaCode_1206)
	return EnumEpayPayInCode_KafkaCode_1206
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_1207() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_1207)
	return EnumEpayPayInCode_Code_1207
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_1208() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_1208)
	return EnumEpayPayInCode_Code_1208
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_1209() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_1209)
	return EnumEpayPayInCode_Code_1209
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_1210() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_1210)
	return EnumEpayPayInCode_Code_1210
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_AuthorizedRequestMulCode_1211() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_AuthorizedRequestMulCode_1211)
	return EnumEpayPayInCode_AuthorizedRequestMulCode_1211
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_FormatdataheaderwritCode_1212() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_FormatdataheaderwritCode_1212)
	return EnumEpayPayInCode_FormatdataheaderwritCode_1212
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_WriterreadermultiparCode_1213() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_WriterreadermultiparCode_1213)
	return EnumEpayPayInCode_WriterreadermultiparCode_1213
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_ApicdnCode_1214() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_ApicdnCode_1214)
	return EnumEpayPayInCode_ApicdnCode_1214
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_LogourlshopIDCode_1215() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_LogourlshopIDCode_1215)
	return EnumEpayPayInCode_LogourlshopIDCode_1215
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_BindJSONshopshopIDCode_1216() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_BindJSONshopshopIDCode_1216)
	return EnumEpayPayInCode_BindJSONshopshopIDCode_1216
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_ShopIDdbshopIDCode_1217() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_ShopIDdbshopIDCode_1217)
	return EnumEpayPayInCode_ShopIDdbshopIDCode_1217
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_JSONSamsungPayCode_1219() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_JSONSamsungPayCode_1219)
	return EnumEpayPayInCode_JSONSamsungPayCode_1219
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_RequestJSONSamsungPaCode_1220() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_RequestJSONSamsungPaCode_1220)
	return EnumEpayPayInCode_RequestJSONSamsungPaCode_1220
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_ShopIDshopIDCode_1221() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_ShopIDshopIDCode_1221)
	return EnumEpayPayInCode_ShopIDshopIDCode_1221
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_TerminalshopIDCode_1222() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_TerminalshopIDCode_1222)
	return EnumEpayPayInCode_TerminalshopIDCode_1222
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_AuthorizedRequestJSOCode_1223() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_AuthorizedRequestJSOCode_1223)
	return EnumEpayPayInCode_AuthorizedRequestJSOCode_1223
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_ShopIDshopIDCode_1224() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_ShopIDshopIDCode_1224)
	return EnumEpayPayInCode_ShopIDshopIDCode_1224
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_BindJSONinOutTerminaCode_1225() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_BindJSONinOutTerminaCode_1225)
	return EnumEpayPayInCode_BindJSONinOutTerminaCode_1225
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_IdidCode_1226() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_IdidCode_1226)
	return EnumEpayPayInCode_IdidCode_1226
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_AuthorizedRequestJSOCode_1227() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_AuthorizedRequestJSOCode_1227)
	return EnumEpayPayInCode_AuthorizedRequestJSOCode_1227
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_TerminalterminalidCode_1228() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_TerminalterminalidCode_1228)
	return EnumEpayPayInCode_TerminalterminalidCode_1228
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_TerminalidterminalidCode_1229() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_TerminalidterminalidCode_1229)
	return EnumEpayPayInCode_TerminalidterminalidCode_1229
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_TerminalterminalidCode_1230() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_TerminalterminalidCode_1230)
	return EnumEpayPayInCode_TerminalterminalidCode_1230
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_JsonMarshalterminaltCode_1231() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_JsonMarshalterminaltCode_1231)
	return EnumEpayPayInCode_JsonMarshalterminaltCode_1231
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_PANCode_1232() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_PANCode_1232)
	return EnumEpayPayInCode_PANCode_1232
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_SamsungPaycallbackreCode_1233() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_SamsungPaycallbackreCode_1233)
	return EnumEpayPayInCode_SamsungPaycallbackreCode_1233
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_StaffstaffCode_1234() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_StaffstaffCode_1234)
	return EnumEpayPayInCode_StaffstaffCode_1234
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_BindCreateMVisaTransCode_1235() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_BindCreateMVisaTransCode_1235)
	return EnumEpayPayInCode_BindCreateMVisaTransCode_1235
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_RequestPostFormXMLCrCode_1236() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_RequestPostFormXMLCrCode_1236)
	return EnumEpayPayInCode_RequestPostFormXMLCrCode_1236
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_RequestPostFormXMLCrCode_1237() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_RequestPostFormXMLCrCode_1237)
	return EnumEpayPayInCode_RequestPostFormXMLCrCode_1237
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_RequestPostFormXMLCrCode_1238() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_RequestPostFormXMLCrCode_1238)
	return EnumEpayPayInCode_RequestPostFormXMLCrCode_1238
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_RequestPostFormXMLCrCode_1239() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_RequestPostFormXMLCrCode_1239)
	return EnumEpayPayInCode_RequestPostFormXMLCrCode_1239
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_BindGet3DSecureCode_1240() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_BindGet3DSecureCode_1240)
	return EnumEpayPayInCode_BindGet3DSecureCode_1240
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_RequestPostFormXMLGeCode_1241() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_RequestPostFormXMLGeCode_1241)
	return EnumEpayPayInCode_RequestPostFormXMLGeCode_1241
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_RequestPostFormXMLGeCode_1242() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_RequestPostFormXMLGeCode_1242)
	return EnumEpayPayInCode_RequestPostFormXMLGeCode_1242
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_RequestPostFormXMLGeCode_1243() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_RequestPostFormXMLGeCode_1243)
	return EnumEpayPayInCode_RequestPostFormXMLGeCode_1243
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_RequestPostFormXMLGeCode_1244() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_RequestPostFormXMLGeCode_1244)
	return EnumEpayPayInCode_RequestPostFormXMLGeCode_1244
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_BindManageTransCode_1245() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_BindManageTransCode_1245)
	return EnumEpayPayInCode_BindManageTransCode_1245
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_RequestPostFormXMLMaCode_1246() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_RequestPostFormXMLMaCode_1246)
	return EnumEpayPayInCode_RequestPostFormXMLMaCode_1246
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_RequestPostFormXMLMaCode_1247() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_RequestPostFormXMLMaCode_1247)
	return EnumEpayPayInCode_RequestPostFormXMLMaCode_1247
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_RequestPostFormXMLMaCode_1248() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_RequestPostFormXMLMaCode_1248)
	return EnumEpayPayInCode_RequestPostFormXMLMaCode_1248
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_RequestPostFormXMLMaCode_1249() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_RequestPostFormXMLMaCode_1249)
	return EnumEpayPayInCode_RequestPostFormXMLMaCode_1249
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_BindCreateTransCode_1250() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_BindCreateTransCode_1250)
	return EnumEpayPayInCode_BindCreateTransCode_1250
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_RequestPostFormXMLCrCode_1251() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_RequestPostFormXMLCrCode_1251)
	return EnumEpayPayInCode_RequestPostFormXMLCrCode_1251
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_RequestPostFormXMLCrCode_1252() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_RequestPostFormXMLCrCode_1252)
	return EnumEpayPayInCode_RequestPostFormXMLCrCode_1252
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_RequestPostFormXMLCrCode_1253() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_RequestPostFormXMLCrCode_1253)
	return EnumEpayPayInCode_RequestPostFormXMLCrCode_1253
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_RequestPostFormXMLCrCode_1254() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_RequestPostFormXMLCrCode_1254)
	return EnumEpayPayInCode_RequestPostFormXMLCrCode_1254
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_BindRePaymentCode_1255() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_BindRePaymentCode_1255)
	return EnumEpayPayInCode_BindRePaymentCode_1255
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_RequestPostFormXMLReCode_1256() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_RequestPostFormXMLReCode_1256)
	return EnumEpayPayInCode_RequestPostFormXMLReCode_1256
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_RequestPostFormXMLReCode_1257() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_RequestPostFormXMLReCode_1257)
	return EnumEpayPayInCode_RequestPostFormXMLReCode_1257
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_RequestPostFormXMLReCode_1258() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_RequestPostFormXMLReCode_1258)
	return EnumEpayPayInCode_RequestPostFormXMLReCode_1258
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_RequestPostFormXMLReCode_1259() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_RequestPostFormXMLReCode_1259)
	return EnumEpayPayInCode_RequestPostFormXMLReCode_1259
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_BindGetTokenCode_1260() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_BindGetTokenCode_1260)
	return EnumEpayPayInCode_BindGetTokenCode_1260
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_RequestPostFormXMLGeCode_1261() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_RequestPostFormXMLGeCode_1261)
	return EnumEpayPayInCode_RequestPostFormXMLGeCode_1261
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_RequestPostFormXMLGeCode_1262() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_RequestPostFormXMLGeCode_1262)
	return EnumEpayPayInCode_RequestPostFormXMLGeCode_1262
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_RequestPostFormXMLGeCode_1263() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_RequestPostFormXMLGeCode_1263)
	return EnumEpayPayInCode_RequestPostFormXMLGeCode_1263
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_RequestPostFormXMLGeCode_1264() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_RequestPostFormXMLGeCode_1264)
	return EnumEpayPayInCode_RequestPostFormXMLGeCode_1264
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_BindTransactionCode_1265() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_BindTransactionCode_1265)
	return EnumEpayPayInCode_BindTransactionCode_1265
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_RequestPostFormXMLTrCode_1266() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_RequestPostFormXMLTrCode_1266)
	return EnumEpayPayInCode_RequestPostFormXMLTrCode_1266
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_CardsystemcommunicatCode_1267() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_CardsystemcommunicatCode_1267)
	return EnumEpayPayInCode_CardsystemcommunicatCode_1267
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_RequestPostFormXMLTrCode_1268() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_RequestPostFormXMLTrCode_1268)
	return EnumEpayPayInCode_RequestPostFormXMLTrCode_1268
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_RequestPostFormXMLTrCode_1269() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_RequestPostFormXMLTrCode_1269)
	return EnumEpayPayInCode_RequestPostFormXMLTrCode_1269
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_RequestPostFormXMLTrCode_1270() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_RequestPostFormXMLTrCode_1270)
	return EnumEpayPayInCode_RequestPostFormXMLTrCode_1270
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_BindTransactionConfiCode_1271() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_BindTransactionConfiCode_1271)
	return EnumEpayPayInCode_BindTransactionConfiCode_1271
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_RequestPostFormXMLTrCode_1272() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_RequestPostFormXMLTrCode_1272)
	return EnumEpayPayInCode_RequestPostFormXMLTrCode_1272
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_RequestPostFormXMLTrCode_1273() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_RequestPostFormXMLTrCode_1273)
	return EnumEpayPayInCode_RequestPostFormXMLTrCode_1273
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_RequestPostFormXMLTrCode_1274() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_RequestPostFormXMLTrCode_1274)
	return EnumEpayPayInCode_RequestPostFormXMLTrCode_1274
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_RequestPostFormXMLTrCode_1275() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_RequestPostFormXMLTrCode_1275)
	return EnumEpayPayInCode_RequestPostFormXMLTrCode_1275
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_InvoiceLinksGetInvoiCode_1276() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_InvoiceLinksGetInvoiCode_1276)
	return EnumEpayPayInCode_InvoiceLinksGetInvoiCode_1276
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_PublicIDuserIDCode_1277() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_PublicIDuserIDCode_1277)
	return EnumEpayPayInCode_PublicIDuserIDCode_1277
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_ParamsCode_1278() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_ParamsCode_1278)
	return EnumEpayPayInCode_ParamsCode_1278
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_PostlinkcorecallbackCode_1279() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_PostlinkcorecallbackCode_1279)
	return EnumEpayPayInCode_PostlinkcorecallbackCode_1279
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_RabbitpostlinkCode_1280() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_RabbitpostlinkCode_1280)
	return EnumEpayPayInCode_RabbitpostlinkCode_1280
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_PublicIDtokeninvoiceCode_1281() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_PublicIDtokeninvoiceCode_1281)
	return EnumEpayPayInCode_PublicIDtokeninvoiceCode_1281
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_SlowpostlinkcorecallCode_1282() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_SlowpostlinkcorecallCode_1282)
	return EnumEpayPayInCode_SlowpostlinkcorecallCode_1282
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_GetOpenWayIDHalykBonCode_1283() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_GetOpenWayIDHalykBonCode_1283)
	return EnumEpayPayInCode_GetOpenWayIDHalykBonCode_1283
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_CardTypenilCode_1284() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_CardTypenilCode_1284)
	return EnumEpayPayInCode_CardTypenilCode_1284
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_CardtypeCode_1285() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_CardtypeCode_1285)
	return EnumEpayPayInCode_CardtypeCode_1285
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_PostlinkcorecallbackCode_1286() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_PostlinkcorecallbackCode_1286)
	return EnumEpayPayInCode_PostlinkcorecallbackCode_1286
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_PostlinkCode_1287() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_PostlinkCode_1287)
	return EnumEpayPayInCode_PostlinkCode_1287
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_PostlinkinvoiceIDCode_1288() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_PostlinkinvoiceIDCode_1288)
	return EnumEpayPayInCode_PostlinkinvoiceIDCode_1288
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_CallbackcorecallbackCode_1289() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_CallbackcorecallbackCode_1289)
	return EnumEpayPayInCode_CallbackcorecallbackCode_1289
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_InvoiceLinkInvoiceIDCode_1290() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_InvoiceLinkInvoiceIDCode_1290)
	return EnumEpayPayInCode_InvoiceLinkInvoiceIDCode_1290
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_InvoiceLinkInvoiceIDCode_1291() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_InvoiceLinkInvoiceIDCode_1291)
	return EnumEpayPayInCode_InvoiceLinkInvoiceIDCode_1291
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_InvoiceLinkinvoiceLiCode_1293() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_InvoiceLinkinvoiceLiCode_1293)
	return EnumEpayPayInCode_InvoiceLinkinvoiceLiCode_1293
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_ErrorMessageinvoiceLCode_1294() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_ErrorMessageinvoiceLCode_1294)
	return EnumEpayPayInCode_ErrorMessageinvoiceLCode_1294
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_TransactionStatusAUTCode_1295() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_TransactionStatusAUTCode_1295)
	return EnumEpayPayInCode_TransactionStatusAUTCode_1295
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_FilePathFileIDCode_1296() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_FilePathFileIDCode_1296)
	return EnumEpayPayInCode_FilePathFileIDCode_1296
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_BindChargeCode_1297() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_BindChargeCode_1297)
	return EnumEpayPayInCode_BindChargeCode_1297
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_1298() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_1298)
	return EnumEpayPayInCode_Code_1298
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_InformationStatementCode_1299() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_InformationStatementCode_1299)
	return EnumEpayPayInCode_InformationStatementCode_1299
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_RefIDCode_1301() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_RefIDCode_1301)
	return EnumEpayPayInCode_RefIDCode_1301
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_EmailCode_1302() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_EmailCode_1302)
	return EnumEpayPayInCode_EmailCode_1302
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_StatementHistoryOmitCode_1303() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_StatementHistoryOmitCode_1303)
	return EnumEpayPayInCode_StatementHistoryOmitCode_1303
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_StaffListCode_1304() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_StaffListCode_1304)
	return EnumEpayPayInCode_StaffListCode_1304
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_GETCode_1305() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_GETCode_1305)
	return EnumEpayPayInCode_GETCode_1305
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_AuthorizedRequestJSOCode_1306() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_AuthorizedRequestJSOCode_1306)
	return EnumEpayPayInCode_AuthorizedRequestJSOCode_1306
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_1307() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_1307)
	return EnumEpayPayInCode_Code_1307
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_CoreinvoiceinvoiceLiCode_1308() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_CoreinvoiceinvoiceLiCode_1308)
	return EnumEpayPayInCode_CoreinvoiceinvoiceLiCode_1308
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_CoreinvoiceinvoiceLiCode_1309() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_CoreinvoiceinvoiceLiCode_1309)
	return EnumEpayPayInCode_CoreinvoiceinvoiceLiCode_1309
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_InvoiceLinkCode_1310() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_InvoiceLinkCode_1310)
	return EnumEpayPayInCode_InvoiceLinkCode_1310
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_InvoiceLinkinvoiceIDCode_1311() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_InvoiceLinkinvoiceIDCode_1311)
	return EnumEpayPayInCode_InvoiceLinkinvoiceIDCode_1311
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_CoreinvoiceinvoiceInCode_1312() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_CoreinvoiceinvoiceInCode_1312)
	return EnumEpayPayInCode_CoreinvoiceinvoiceInCode_1312
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_KafkaCode_1315() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_KafkaCode_1315)
	return EnumEpayPayInCode_KafkaCode_1315
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_HomebankaCode_1317() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_HomebankaCode_1317)
	return EnumEpayPayInCode_HomebankaCode_1317
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_1318() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_1318)
	return EnumEpayPayInCode_Code_1318
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_UseraCode_1319() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_UseraCode_1319)
	return EnumEpayPayInCode_UseraCode_1319
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_StaffupdatepasswordCode_1320() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_StaffupdatepasswordCode_1320)
	return EnumEpayPayInCode_StaffupdatepasswordCode_1320
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_UpdateStaffRoleCode_1321() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_UpdateStaffRoleCode_1321)
	return EnumEpayPayInCode_UpdateStaffRoleCode_1321
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_1322() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_1322)
	return EnumEpayPayInCode_Code_1322
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_COMMITCode_1323() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_COMMITCode_1323)
	return EnumEpayPayInCode_COMMITCode_1323
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_1324() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_1324)
	return EnumEpayPayInCode_Code_1324
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_1325() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_1325)
	return EnumEpayPayInCode_Code_1325
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_1326() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_1326)
	return EnumEpayPayInCode_Code_1326
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Float64FeeAmountstriCode_1327() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Float64FeeAmountstriCode_1327)
	return EnumEpayPayInCode_Float64FeeAmountstriCode_1327
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Float64SettlAmountstCode_1328() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Float64SettlAmountstCode_1328)
	return EnumEpayPayInCode_Float64SettlAmountstCode_1328
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Float64TrAmountstrinCode_1329() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Float64TrAmountstrinCode_1329)
	return EnumEpayPayInCode_Float64TrAmountstrinCode_1329
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_1330() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_1330)
	return EnumEpayPayInCode_Code_1330
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_1331() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_1331)
	return EnumEpayPayInCode_Code_1331
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_ParseMediaTypeCode_1332() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_ParseMediaTypeCode_1332)
	return EnumEpayPayInCode_ParseMediaTypeCode_1332
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_CreateFileCode_1333() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_CreateFileCode_1333)
	return EnumEpayPayInCode_CreateFileCode_1333
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_EmailCode_1334() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_EmailCode_1334)
	return EnumEpayPayInCode_EmailCode_1334
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_HBCode_1335() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_HBCode_1335)
	return EnumEpayPayInCode_HBCode_1335
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_1336() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_1336)
	return EnumEpayPayInCode_Code_1336
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_1337() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_1337)
	return EnumEpayPayInCode_Code_1337
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_ACTIVECode_1338() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_ACTIVECode_1338)
	return EnumEpayPayInCode_ACTIVECode_1338
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_CHARGEDCode_1339() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_CHARGEDCode_1339)
	return EnumEpayPayInCode_CHARGEDCode_1339
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_MerchantterminaltermCode_1340() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_MerchantterminaltermCode_1340)
	return EnumEpayPayInCode_MerchantterminaltermCode_1340
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_PostingDatestringOrdCode_1341() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_PostingDatestringOrdCode_1341)
	return EnumEpayPayInCode_PostingDatestringOrdCode_1341
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_HBCode_1342() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_HBCode_1342)
	return EnumEpayPayInCode_HBCode_1342
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_FTPCode_1343() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_FTPCode_1343)
	return EnumEpayPayInCode_FTPCode_1343
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_JobExecutiongocronCode_1344() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_JobExecutiongocronCode_1344)
	return EnumEpayPayInCode_JobExecutiongocronCode_1344
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Float64FeeAmountstriCode_1346() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Float64FeeAmountstriCode_1346)
	return EnumEpayPayInCode_Float64FeeAmountstriCode_1346
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Float64SettlAmountstCode_1347() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Float64SettlAmountstCode_1347)
	return EnumEpayPayInCode_Float64SettlAmountstCode_1347
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Float64TrAmountstrinCode_1348() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Float64TrAmountstrinCode_1348)
	return EnumEpayPayInCode_Float64TrAmountstrinCode_1348
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_MerchantPublicIDCode_1361() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_MerchantPublicIDCode_1361)
	return EnumEpayPayInCode_MerchantPublicIDCode_1361
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_MerchantInfoPublicIDCode_1366() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_MerchantInfoPublicIDCode_1366)
	return EnumEpayPayInCode_MerchantInfoPublicIDCode_1366
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_PublicIDCode_1367() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_PublicIDCode_1367)
	return EnumEpayPayInCode_PublicIDCode_1367
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_1368() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_1368)
	return EnumEpayPayInCode_Code_1368
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_1369() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_1369)
	return EnumEpayPayInCode_Code_1369
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_1370() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_1370)
	return EnumEpayPayInCode_Code_1370
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_AuthorizedRequestJSOCode_1371() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_AuthorizedRequestJSOCode_1371)
	return EnumEpayPayInCode_AuthorizedRequestJSOCode_1371
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_CoreGetCardCode_1372() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_CoreGetCardCode_1372)
	return EnumEpayPayInCode_CoreGetCardCode_1372
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_1373() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_1373)
	return EnumEpayPayInCode_Code_1373
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_OauthCode_1374() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_OauthCode_1374)
	return EnumEpayPayInCode_OauthCode_1374
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_RequestClientCode_1375() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_RequestClientCode_1375)
	return EnumEpayPayInCode_RequestClientCode_1375
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_1376() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_1376)
	return EnumEpayPayInCode_Code_1376
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_1378() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_1378)
	return EnumEpayPayInCode_Code_1378
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_ScopeCode_1379() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_ScopeCode_1379)
	return EnumEpayPayInCode_ScopeCode_1379
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_ScopeCode_1380() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_ScopeCode_1380)
	return EnumEpayPayInCode_ScopeCode_1380
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_ClientScopesCode_1381() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_ClientScopesCode_1381)
	return EnumEpayPayInCode_ClientScopesCode_1381
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_1382() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_1382)
	return EnumEpayPayInCode_Code_1382
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_1383() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_1383)
	return EnumEpayPayInCode_Code_1383
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_1384() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_1384)
	return EnumEpayPayInCode_Code_1384
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_1385() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_1385)
	return EnumEpayPayInCode_Code_1385
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_1386() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_1386)
	return EnumEpayPayInCode_Code_1386
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_HomeBankCode_1387() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_HomeBankCode_1387)
	return EnumEpayPayInCode_HomeBankCode_1387
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_HMCode_1388() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_HMCode_1388)
	return EnumEpayPayInCode_HMCode_1388
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_RequestClientUpdateCode_1389() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_RequestClientUpdateCode_1389)
	return EnumEpayPayInCode_RequestClientUpdateCode_1389
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_ClientIDCode_1390() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_ClientIDCode_1390)
	return EnumEpayPayInCode_ClientIDCode_1390
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_GetClientclientCode_1391() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_GetClientclientCode_1391)
	return EnumEpayPayInCode_GetClientclientCode_1391
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_GetClientScopeCode_1392() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_GetClientScopeCode_1392)
	return EnumEpayPayInCode_GetClientScopeCode_1392
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_UpdateScopeCode_1393() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_UpdateScopeCode_1393)
	return EnumEpayPayInCode_UpdateScopeCode_1393
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_ScopesCode_1394() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_ScopesCode_1394)
	return EnumEpayPayInCode_ScopesCode_1394
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_ClientCode_1395() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_ClientCode_1395)
	return EnumEpayPayInCode_ClientCode_1395
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_ScopeCode_1396() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_ScopeCode_1396)
	return EnumEpayPayInCode_ScopeCode_1396
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_ClientScopesCode_1397() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_ClientScopesCode_1397)
	return EnumEpayPayInCode_ClientScopesCode_1397
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_ReadererrorCode_1398() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_ReadererrorCode_1398)
	return EnumEpayPayInCode_ReadererrorCode_1398
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_TerminaldbmerchantIDCode_1400() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_TerminaldbmerchantIDCode_1400)
	return EnumEpayPayInCode_TerminaldbmerchantIDCode_1400
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_ClientclientIDdbTildCode_1403() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_ClientclientIDdbTildCode_1403)
	return EnumEpayPayInCode_ClientclientIDdbTildCode_1403
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_ClientclientIDdbCode_1405() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_ClientclientIDdbCode_1405)
	return EnumEpayPayInCode_ClientclientIDdbCode_1405
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_ClientIPTildaCode_1406() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_ClientIPTildaCode_1406)
	return EnumEpayPayInCode_ClientIPTildaCode_1406
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_BindmultipartformdatCode_1407() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_BindmultipartformdatCode_1407)
	return EnumEpayPayInCode_BindmultipartformdatCode_1407
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_TildaTildaCode_1408() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_TildaTildaCode_1408)
	return EnumEpayPayInCode_TildaTildaCode_1408
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_TildaCode_1409() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_TildaCode_1409)
	return EnumEpayPayInCode_TildaCode_1409
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Float64amountTildaCode_1410() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Float64amountTildaCode_1410)
	return EnumEpayPayInCode_Float64amountTildaCode_1410
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_AuthorizedRequestJSOCode_1411() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_AuthorizedRequestJSOCode_1411)
	return EnumEpayPayInCode_AuthorizedRequestJSOCode_1411
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_SourceCode_1412() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_SourceCode_1412)
	return EnumEpayPayInCode_SourceCode_1412
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_CallbackcorecallbackCode_1413() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_CallbackcorecallbackCode_1413)
	return EnumEpayPayInCode_CallbackcorecallbackCode_1413
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_PostlinkTildaCode_1414() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_PostlinkTildaCode_1414)
	return EnumEpayPayInCode_PostlinkTildaCode_1414
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_1415() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_1415)
	return EnumEpayPayInCode_Code_1415
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_1416() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_1416)
	return EnumEpayPayInCode_Code_1416
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_AuthorizedRequestJSOCode_1418() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_AuthorizedRequestJSOCode_1418)
	return EnumEpayPayInCode_AuthorizedRequestJSOCode_1418
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_CoreStatementCode_1419() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_CoreStatementCode_1419)
	return EnumEpayPayInCode_CoreStatementCode_1419
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_1421() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_1421)
	return EnumEpayPayInCode_Code_1421
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_1422() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_1422)
	return EnumEpayPayInCode_Code_1422
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_MerchantcompanynameCode_1423() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_MerchantcompanynameCode_1423)
	return EnumEpayPayInCode_MerchantcompanynameCode_1423
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_ClientTypeCode_1424() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_ClientTypeCode_1424)
	return EnumEpayPayInCode_ClientTypeCode_1424
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_ClientTypenameTildaCode_1425() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_ClientTypenameTildaCode_1425)
	return EnumEpayPayInCode_ClientTypenameTildaCode_1425
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_ClientTypeTildaClienCode_1426() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_ClientTypeTildaClienCode_1426)
	return EnumEpayPayInCode_ClientTypeTildaClienCode_1426
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_BindJSONBindJSONCode_1427() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_BindJSONBindJSONCode_1427)
	return EnumEpayPayInCode_BindJSONBindJSONCode_1427
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_ParsingerrorCode_1428() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_ParsingerrorCode_1428)
	return EnumEpayPayInCode_ParsingerrorCode_1428
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_1429() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_1429)
	return EnumEpayPayInCode_Code_1429
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_1430() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_1430)
	return EnumEpayPayInCode_Code_1430
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_1431() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_1431)
	return EnumEpayPayInCode_Code_1431
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_LIKECode_1432() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_LIKECode_1432)
	return EnumEpayPayInCode_LIKECode_1432
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_BETWEENCode_1433() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_BETWEENCode_1433)
	return EnumEpayPayInCode_BETWEENCode_1433
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_1434() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_1434)
	return EnumEpayPayInCode_Code_1434
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_1436() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_1436)
	return EnumEpayPayInCode_Code_1436
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_1437() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_1437)
	return EnumEpayPayInCode_Code_1437
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_1438() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_1438)
	return EnumEpayPayInCode_Code_1438
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_CountCode_1439() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_CountCode_1439)
	return EnumEpayPayInCode_CountCode_1439
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_1440() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_1440)
	return EnumEpayPayInCode_Code_1440
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_1441() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_1441)
	return EnumEpayPayInCode_Code_1441
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_ReferenceEPAYCode_1442() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_ReferenceEPAYCode_1442)
	return EnumEpayPayInCode_ReferenceEPAYCode_1442
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_1443() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_1443)
	return EnumEpayPayInCode_Code_1443
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_1444() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_1444)
	return EnumEpayPayInCode_Code_1444
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_1445() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_1445)
	return EnumEpayPayInCode_Code_1445
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_HMHMBCode_1446() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_HMHMBCode_1446)
	return EnumEpayPayInCode_HMHMBCode_1446
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_JSONJSONCode_1447() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_JSONJSONCode_1447)
	return EnumEpayPayInCode_JSONJSONCode_1447
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_UrlbodyCode_1448() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_UrlbodyCode_1448)
	return EnumEpayPayInCode_UrlbodyCode_1448
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_1449() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_1449)
	return EnumEpayPayInCode_Code_1449
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_GetUseScopeByClientCode_1451() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_GetUseScopeByClientCode_1451)
	return EnumEpayPayInCode_GetUseScopeByClientCode_1451
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_1452() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_1452)
	return EnumEpayPayInCode_Code_1452
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_JSONUnmarshalCode_1453() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_JSONUnmarshalCode_1453)
	return EnumEpayPayInCode_JSONUnmarshalCode_1453
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_1454() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_1454)
	return EnumEpayPayInCode_Code_1454
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_1455() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_1455)
	return EnumEpayPayInCode_Code_1455
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_WebsocketCode_1457() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_WebsocketCode_1457)
	return EnumEpayPayInCode_WebsocketCode_1457
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_WebsocketCode_1458() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_WebsocketCode_1458)
	return EnumEpayPayInCode_WebsocketCode_1458
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_WebsocketCode_1459() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_WebsocketCode_1459)
	return EnumEpayPayInCode_WebsocketCode_1459
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_WebsocketCode_1460() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_WebsocketCode_1460)
	return EnumEpayPayInCode_WebsocketCode_1460
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_WebsocketCode_1461() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_WebsocketCode_1461)
	return EnumEpayPayInCode_WebsocketCode_1461
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_WebsocketCode_1462() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_WebsocketCode_1462)
	return EnumEpayPayInCode_WebsocketCode_1462
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_1463() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_1463)
	return EnumEpayPayInCode_Code_1463
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_1464() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_1464)
	return EnumEpayPayInCode_Code_1464
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_1465() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_1465)
	return EnumEpayPayInCode_Code_1465
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_1466() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_1466)
	return EnumEpayPayInCode_Code_1466
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_1467() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_1467)
	return EnumEpayPayInCode_Code_1467
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_UpdateMerchantCode_1468() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_UpdateMerchantCode_1468)
	return EnumEpayPayInCode_UpdateMerchantCode_1468
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_UpdateMerchantShopCode_1469() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_UpdateMerchantShopCode_1469)
	return EnumEpayPayInCode_UpdateMerchantShopCode_1469
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_UpdateMerchantTerminCode_1470() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_UpdateMerchantTerminCode_1470)
	return EnumEpayPayInCode_UpdateMerchantTerminCode_1470
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_1471() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_1471)
	return EnumEpayPayInCode_Code_1471
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_1472() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_1472)
	return EnumEpayPayInCode_Code_1472
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_EmailCode_1473() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_EmailCode_1473)
	return EnumEpayPayInCode_EmailCode_1473
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_NameserviceCode_1475() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_NameserviceCode_1475)
	return EnumEpayPayInCode_NameserviceCode_1475
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_IdparamsCode_1476() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_IdparamsCode_1476)
	return EnumEpayPayInCode_IdparamsCode_1476
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_MerchantpublicIDCode_1477() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_MerchantpublicIDCode_1477)
	return EnumEpayPayInCode_MerchantpublicIDCode_1477
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_TerminalmerchantIDCode_1478() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_TerminalmerchantIDCode_1478)
	return EnumEpayPayInCode_TerminalmerchantIDCode_1478
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_ShopidshopwebserviceCode_1479() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_ShopidshopwebserviceCode_1479)
	return EnumEpayPayInCode_ShopidshopwebserviceCode_1479
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_1480() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_1480)
	return EnumEpayPayInCode_Code_1480
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_1481() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_1481)
	return EnumEpayPayInCode_Code_1481
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_IdparamsCode_1482() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_IdparamsCode_1482)
	return EnumEpayPayInCode_IdparamsCode_1482
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_JSONJSONCode_1483() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_JSONJSONCode_1483)
	return EnumEpayPayInCode_JSONJSONCode_1483
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_JSONjsonMarshalJSONCode_1484() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_JSONjsonMarshalJSONCode_1484)
	return EnumEpayPayInCode_JSONjsonMarshalJSONCode_1484
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_IdparamsCode_1486() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_IdparamsCode_1486)
	return EnumEpayPayInCode_IdparamsCode_1486
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_1487() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_1487)
	return EnumEpayPayInCode_Code_1487
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_1488() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_1488)
	return EnumEpayPayInCode_Code_1488
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_1489() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_1489)
	return EnumEpayPayInCode_Code_1489
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_1490() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_1490)
	return EnumEpayPayInCode_Code_1490
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_PaymentRequestCode_1491() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_PaymentRequestCode_1491)
	return EnumEpayPayInCode_PaymentRequestCode_1491
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_1492() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_1492)
	return EnumEpayPayInCode_Code_1492
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_JsonUnmarshalCode_1493() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_JsonUnmarshalCode_1493)
	return EnumEpayPayInCode_JsonUnmarshalCode_1493
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_JsonMarshalCode_1494() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_JsonMarshalCode_1494)
	return EnumEpayPayInCode_JsonMarshalCode_1494
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_1495() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_1495)
	return EnumEpayPayInCode_Code_1495
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_JSONJSONCode_1496() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_JSONJSONCode_1496)
	return EnumEpayPayInCode_JSONJSONCode_1496
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_1497() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_1497)
	return EnumEpayPayInCode_Code_1497
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_1498() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_1498)
	return EnumEpayPayInCode_Code_1498
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_AuthorizedRequestJSOCode_1499() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_AuthorizedRequestJSOCode_1499)
	return EnumEpayPayInCode_AuthorizedRequestJSOCode_1499
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_AuthorizedRequestJSOCode_1500() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_AuthorizedRequestJSOCode_1500)
	return EnumEpayPayInCode_AuthorizedRequestJSOCode_1500
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_AuthorizedRequestJSOCode_1502() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_AuthorizedRequestJSOCode_1502)
	return EnumEpayPayInCode_AuthorizedRequestJSOCode_1502
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_AuthorizedRequestJSOCode_1503() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_AuthorizedRequestJSOCode_1503)
	return EnumEpayPayInCode_AuthorizedRequestJSOCode_1503
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_AuthorizedRequestJSOCode_1504() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_AuthorizedRequestJSOCode_1504)
	return EnumEpayPayInCode_AuthorizedRequestJSOCode_1504
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_TerminaluuidCode_1505() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_TerminaluuidCode_1505)
	return EnumEpayPayInCode_TerminaluuidCode_1505
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_BindgrafanaCode_1506() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_BindgrafanaCode_1506)
	return EnumEpayPayInCode_BindgrafanaCode_1506
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_HttpgrafanaCode_1507() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_HttpgrafanaCode_1507)
	return EnumEpayPayInCode_HttpgrafanaCode_1507
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_UnauthorizedCode_1508() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_UnauthorizedCode_1508)
	return EnumEpayPayInCode_UnauthorizedCode_1508
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_BadtokenCode_1509() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_BadtokenCode_1509)
	return EnumEpayPayInCode_BadtokenCode_1509
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_JsonUnmarshalCode_1510() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_JsonUnmarshalCode_1510)
	return EnumEpayPayInCode_JsonUnmarshalCode_1510
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_JsonMarshalCode_1511() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_JsonMarshalCode_1511)
	return EnumEpayPayInCode_JsonMarshalCode_1511
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_RequestJSONgeoIPCode_1512() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_RequestJSONgeoIPCode_1512)
	return EnumEpayPayInCode_RequestJSONgeoIPCode_1512
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_1513() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_1513)
	return EnumEpayPayInCode_Code_1513
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_JsonUnmarshalCode_1514() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_JsonUnmarshalCode_1514)
	return EnumEpayPayInCode_JsonUnmarshalCode_1514
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_JsonMarshalCode_1515() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_JsonMarshalCode_1515)
	return EnumEpayPayInCode_JsonMarshalCode_1515
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_1516() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_1516)
	return EnumEpayPayInCode_Code_1516
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_QrTransactionInfoCode_1517() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_QrTransactionInfoCode_1517)
	return EnumEpayPayInCode_QrTransactionInfoCode_1517
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_CardTypeIDCode_1518() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_CardTypeIDCode_1518)
	return EnumEpayPayInCode_CardTypeIDCode_1518
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_CurrencyIDCode_1519() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_CurrencyIDCode_1519)
	return EnumEpayPayInCode_CurrencyIDCode_1519
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_StatusIDCode_1520() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_StatusIDCode_1520)
	return EnumEpayPayInCode_StatusIDCode_1520
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_1521() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_1521)
	return EnumEpayPayInCode_Code_1521
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_1522() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_1522)
	return EnumEpayPayInCode_Code_1522
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_1523() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_1523)
	return EnumEpayPayInCode_Code_1523
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_1524() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_1524)
	return EnumEpayPayInCode_Code_1524
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_ShopIDmerchantIDclieCode_1525() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_ShopIDmerchantIDclieCode_1525)
	return EnumEpayPayInCode_ShopIDmerchantIDclieCode_1525
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_1526() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_1526)
	return EnumEpayPayInCode_Code_1526
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_QRCode_1527() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_QRCode_1527)
	return EnumEpayPayInCode_QRCode_1527
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_SourceListIDCode_1528() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_SourceListIDCode_1528)
	return EnumEpayPayInCode_SourceListIDCode_1528
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_AuthorizedRequestJSOCode_1529() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_AuthorizedRequestJSOCode_1529)
	return EnumEpayPayInCode_AuthorizedRequestJSOCode_1529
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_XLSIDCode_1530() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_XLSIDCode_1530)
	return EnumEpayPayInCode_XLSIDCode_1530
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_JSONJSONCode_1531() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_JSONJSONCode_1531)
	return EnumEpayPayInCode_JSONJSONCode_1531
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_RedispartialTransactCode_1532() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_RedispartialTransactCode_1532)
	return EnumEpayPayInCode_RedispartialTransactCode_1532
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_1533() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_1533)
	return EnumEpayPayInCode_Code_1533
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_1534() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_1534)
	return EnumEpayPayInCode_Code_1534
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_1535() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_1535)
	return EnumEpayPayInCode_Code_1535
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_1536() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_1536)
	return EnumEpayPayInCode_Code_1536
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_1537() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_1537)
	return EnumEpayPayInCode_Code_1537
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_RedisinvoiceIdCode_1538() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_RedisinvoiceIdCode_1538)
	return EnumEpayPayInCode_RedisinvoiceIdCode_1538
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Epay1Code_1539() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Epay1Code_1539)
	return EnumEpayPayInCode_Epay1Code_1539
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Epay1Code_1540() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Epay1Code_1540)
	return EnumEpayPayInCode_Epay1Code_1540
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Epay1Code_1541() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Epay1Code_1541)
	return EnumEpayPayInCode_Epay1Code_1541
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Epay1Code_1542() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Epay1Code_1542)
	return EnumEpayPayInCode_Epay1Code_1542
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_TransactionTypeIDCode_1543() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_TransactionTypeIDCode_1543)
	return EnumEpayPayInCode_TransactionTypeIDCode_1543
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_ClientTypeIDCode_1544() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_ClientTypeIDCode_1544)
	return EnumEpayPayInCode_ClientTypeIDCode_1544
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_JsonMarshalucsCode_1545() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_JsonMarshalucsCode_1545)
	return EnumEpayPayInCode_JsonMarshalucsCode_1545
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_1546() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_1546)
	return EnumEpayPayInCode_Code_1546
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_ScopeCode_1547() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_ScopeCode_1547)
	return EnumEpayPayInCode_ScopeCode_1547
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_RedisqrstatusCode_1548() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_RedisqrstatusCode_1548)
	return EnumEpayPayInCode_RedisqrstatusCode_1548
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_AuthorizedRequestJSOCode_1549() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_AuthorizedRequestJSOCode_1549)
	return EnumEpayPayInCode_AuthorizedRequestJSOCode_1549
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_1550() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_1550)
	return EnumEpayPayInCode_Code_1550
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_RedisrealIPCode_1551() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_RedisrealIPCode_1551)
	return EnumEpayPayInCode_RedisrealIPCode_1551
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_RequestJSONcoreqrQRSCode_1552() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_RequestJSONcoreqrQRSCode_1552)
	return EnumEpayPayInCode_RequestJSONcoreqrQRSCode_1552
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_KafkaReadMessageCode_1553() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_KafkaReadMessageCode_1553)
	return EnumEpayPayInCode_KafkaReadMessageCode_1553
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_JsonUnmarshalkafkameCode_1554() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_JsonUnmarshalkafkameCode_1554)
	return EnumEpayPayInCode_JsonUnmarshalkafkameCode_1554
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_JSONjsonMarshalJSONCode_1555() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_JSONjsonMarshalJSONCode_1555)
	return EnumEpayPayInCode_JSONjsonMarshalJSONCode_1555
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_KafkaCode_1556() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_KafkaCode_1556)
	return EnumEpayPayInCode_KafkaCode_1556
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_SetOffsetkafkaCode_1557() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_SetOffsetkafkaCode_1557)
	return EnumEpayPayInCode_SetOffsetkafkaCode_1557
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_1558() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_1558)
	return EnumEpayPayInCode_Code_1558
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_1559() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_1559)
	return EnumEpayPayInCode_Code_1559
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_1560() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_1560)
	return EnumEpayPayInCode_Code_1560
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_1561() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_1561)
	return EnumEpayPayInCode_Code_1561
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_HTTPCode_1562() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_HTTPCode_1562)
	return EnumEpayPayInCode_HTTPCode_1562
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_HTTPCode_1563() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_HTTPCode_1563)
	return EnumEpayPayInCode_HTTPCode_1563
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_HTTPCode_1564() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_HTTPCode_1564)
	return EnumEpayPayInCode_HTTPCode_1564
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_HTTPCode_1565() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_HTTPCode_1565)
	return EnumEpayPayInCode_HTTPCode_1565
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_AUTHCode_1566() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_AUTHCode_1566)
	return EnumEpayPayInCode_AUTHCode_1566
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_EGateWayCode_1567() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_EGateWayCode_1567)
	return EnumEpayPayInCode_EGateWayCode_1567
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_EGateWayCode_1568() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_EGateWayCode_1568)
	return EnumEpayPayInCode_EGateWayCode_1568
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_1569() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_1569)
	return EnumEpayPayInCode_Code_1569
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_JsonCode_1570() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_JsonCode_1570)
	return EnumEpayPayInCode_JsonCode_1570
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_1571() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_1571)
	return EnumEpayPayInCode_Code_1571
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_1572() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_1572)
	return EnumEpayPayInCode_Code_1572
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_1578() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_1578)
	return EnumEpayPayInCode_Code_1578
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_1579() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_1579)
	return EnumEpayPayInCode_Code_1579
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_ReadAllfromRequestBoCode_1604() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_ReadAllfromRequestBoCode_1604)
	return EnumEpayPayInCode_ReadAllfromRequestBoCode_1604
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_ParseFloatrefundCode_1605() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_ParseFloatrefundCode_1605)
	return EnumEpayPayInCode_ParseFloatrefundCode_1605
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_AmountbodyurlrefundCode_1606() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_AmountbodyurlrefundCode_1606)
	return EnumEpayPayInCode_AmountbodyurlrefundCode_1606
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_1607() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_1607)
	return EnumEpayPayInCode_Code_1607
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_ChargeCode_1608() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_ChargeCode_1608)
	return EnumEpayPayInCode_ChargeCode_1608
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_CancelCode_1609() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_CancelCode_1609)
	return EnumEpayPayInCode_CancelCode_1609
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_RefundCode_1610() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_RefundCode_1610)
	return EnumEpayPayInCode_RefundCode_1610
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_HttpCode_1611() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_HttpCode_1611)
	return EnumEpayPayInCode_HttpCode_1611
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_1612() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_1612)
	return EnumEpayPayInCode_Code_1612
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_1614() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_1614)
	return EnumEpayPayInCode_Code_1614
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_TerminalIDCode_1615() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_TerminalIDCode_1615)
	return EnumEpayPayInCode_TerminalIDCode_1615
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_1616() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_1616)
	return EnumEpayPayInCode_Code_1616
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_JsonUnmarshalbinCode_1617() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_JsonUnmarshalbinCode_1617)
	return EnumEpayPayInCode_JsonUnmarshalbinCode_1617
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_JsonMarshalCode_1618() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_JsonMarshalCode_1618)
	return EnumEpayPayInCode_JsonMarshalCode_1618
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_1619() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_1619)
	return EnumEpayPayInCode_Code_1619
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_JSONapiosuvoxCode_1620() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_JSONapiosuvoxCode_1620)
	return EnumEpayPayInCode_JSONapiosuvoxCode_1620
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_ApiosuvoxCode_1621() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_ApiosuvoxCode_1621)
	return EnumEpayPayInCode_ApiosuvoxCode_1621
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_ApiosuvoxHTTPCODE200Code_1622() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_ApiosuvoxHTTPCODE200Code_1622)
	return EnumEpayPayInCode_ApiosuvoxHTTPCODE200Code_1622
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_1623() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_1623)
	return EnumEpayPayInCode_Code_1623
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_SourceListCode_1624() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_SourceListCode_1624)
	return EnumEpayPayInCode_SourceListCode_1624
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_1636() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_1636)
	return EnumEpayPayInCode_Code_1636
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_1637() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_1637)
	return EnumEpayPayInCode_Code_1637
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_OsuvoxCode_1638() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_OsuvoxCode_1638)
	return EnumEpayPayInCode_OsuvoxCode_1638
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_InvalidCardIDCode_1639() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_InvalidCardIDCode_1639)
	return EnumEpayPayInCode_InvalidCardIDCode_1639
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_OsuvoxCode_1640() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_OsuvoxCode_1640)
	return EnumEpayPayInCode_OsuvoxCode_1640
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_1641() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_1641)
	return EnumEpayPayInCode_Code_1641
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_OsuvoxCode_1642() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_OsuvoxCode_1642)
	return EnumEpayPayInCode_OsuvoxCode_1642
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_SingleMessageSchemeCode_1650() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_SingleMessageSchemeCode_1650)
	return EnumEpayPayInCode_SingleMessageSchemeCode_1650
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_1651() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_1651)
	return EnumEpayPayInCode_Code_1651
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_OTPCode_1652() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_OTPCode_1652)
	return EnumEpayPayInCode_OTPCode_1652
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_OTPCode_1653() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_OTPCode_1653)
	return EnumEpayPayInCode_OTPCode_1653
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_OTPcodeCode_1654() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_OTPcodeCode_1654)
	return EnumEpayPayInCode_OTPcodeCode_1654
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_OTPcodeCode_1655() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_OTPcodeCode_1655)
	return EnumEpayPayInCode_OTPcodeCode_1655
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_RedisterminaluuidinvCode_1656() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_RedisterminaluuidinvCode_1656)
	return EnumEpayPayInCode_RedisterminaluuidinvCode_1656
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_InvalidterminalIdCode_1658() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_InvalidterminalIdCode_1658)
	return EnumEpayPayInCode_InvalidterminalIdCode_1658
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_InvalidAmoutCode_1659() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_InvalidAmoutCode_1659)
	return EnumEpayPayInCode_InvalidAmoutCode_1659
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_1660() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_1660)
	return EnumEpayPayInCode_Code_1660
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_1661() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_1661)
	return EnumEpayPayInCode_Code_1661
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_AuthorizedRequestJSOCode_1663() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_AuthorizedRequestJSOCode_1663)
	return EnumEpayPayInCode_AuthorizedRequestJSOCode_1663
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_BindGetTokenByCardCode_1664() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_BindGetTokenByCardCode_1664)
	return EnumEpayPayInCode_BindGetTokenByCardCode_1664
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_RequestPostFormXMLGeCode_1665() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_RequestPostFormXMLGeCode_1665)
	return EnumEpayPayInCode_RequestPostFormXMLGeCode_1665
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_RequestPostFormXMLGeCode_1666() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_RequestPostFormXMLGeCode_1666)
	return EnumEpayPayInCode_RequestPostFormXMLGeCode_1666
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_RequestPostFormXMLGeCode_1667() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_RequestPostFormXMLGeCode_1667)
	return EnumEpayPayInCode_RequestPostFormXMLGeCode_1667
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_RequestPostFormXMLGeCode_1668() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_RequestPostFormXMLGeCode_1668)
	return EnumEpayPayInCode_RequestPostFormXMLGeCode_1668
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Codebase64stringbaseCode_1670() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Codebase64stringbaseCode_1670)
	return EnumEpayPayInCode_Codebase64stringbaseCode_1670
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_AuthorizedRequestJSOCode_1671() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_AuthorizedRequestJSOCode_1671)
	return EnumEpayPayInCode_AuthorizedRequestJSOCode_1671
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_CardCode_1672() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_CardCode_1672)
	return EnumEpayPayInCode_CardCode_1672
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_HomebankPayOsuvoxCarCode_1673() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_HomebankPayOsuvoxCarCode_1673)
	return EnumEpayPayInCode_HomebankPayOsuvoxCarCode_1673
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_OsuvoxCode_1675() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_OsuvoxCode_1675)
	return EnumEpayPayInCode_OsuvoxCode_1675
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_OsuvoxCode_1677() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_OsuvoxCode_1677)
	return EnumEpayPayInCode_OsuvoxCode_1677
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_OsuvoxCode_1679() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_OsuvoxCode_1679)
	return EnumEpayPayInCode_OsuvoxCode_1679
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_OsuvoxCode_1680() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_OsuvoxCode_1680)
	return EnumEpayPayInCode_OsuvoxCode_1680
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_OsuvoxCode_1681() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_OsuvoxCode_1681)
	return EnumEpayPayInCode_OsuvoxCode_1681
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_1682() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_1682)
	return EnumEpayPayInCode_Code_1682
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_OsuvoxCode_1683() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_OsuvoxCode_1683)
	return EnumEpayPayInCode_OsuvoxCode_1683
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_OsuvoxCode_1685() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_OsuvoxCode_1685)
	return EnumEpayPayInCode_OsuvoxCode_1685
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_TransactionCode_1690() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_TransactionCode_1690)
	return EnumEpayPayInCode_TransactionCode_1690
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_CouldnotgetphoneNumbCode_1692() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_CouldnotgetphoneNumbCode_1692)
	return EnumEpayPayInCode_CouldnotgetphoneNumbCode_1692
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_1693() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_1693)
	return EnumEpayPayInCode_Code_1693
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_1694() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_1694)
	return EnumEpayPayInCode_Code_1694
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_OsuvoxCode_1695() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_OsuvoxCode_1695)
	return EnumEpayPayInCode_OsuvoxCode_1695
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_XMLbodyCode_1699() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_XMLbodyCode_1699)
	return EnumEpayPayInCode_XMLbodyCode_1699
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_XMLMarhsalswitchpaymCode_1700() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_XMLMarhsalswitchpaymCode_1700)
	return EnumEpayPayInCode_XMLMarhsalswitchpaymCode_1700
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_TerminalIdCode_1703() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_TerminalIdCode_1703)
	return EnumEpayPayInCode_TerminalIdCode_1703
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_TerminalXLSMIDterminCode_1704() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_TerminalXLSMIDterminCode_1704)
	return EnumEpayPayInCode_TerminalXLSMIDterminCode_1704
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_IbanCode_1705() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_IbanCode_1705)
	return EnumEpayPayInCode_IbanCode_1705
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_IbanCode_1706() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_IbanCode_1706)
	return EnumEpayPayInCode_IbanCode_1706
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_TerminalIdCode_1707() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_TerminalIdCode_1707)
	return EnumEpayPayInCode_TerminalIdCode_1707
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_IbanCode_1708() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_IbanCode_1708)
	return EnumEpayPayInCode_IbanCode_1708
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_1709() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_1709)
	return EnumEpayPayInCode_Code_1709
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_1710() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_1710)
	return EnumEpayPayInCode_Code_1710
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_1711() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_1711)
	return EnumEpayPayInCode_Code_1711
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_1712() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_1712)
	return EnumEpayPayInCode_Code_1712
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_InvoiceIdCode_1713() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_InvoiceIdCode_1713)
	return EnumEpayPayInCode_InvoiceIdCode_1713
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_1714() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_1714)
	return EnumEpayPayInCode_Code_1714
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_AUTHCode_1715() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_AUTHCode_1715)
	return EnumEpayPayInCode_AUTHCode_1715
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_PaymentTypeCode_1716() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_PaymentTypeCode_1716)
	return EnumEpayPayInCode_PaymentTypeCode_1716
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_OsuvoxCode_1717() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_OsuvoxCode_1717)
	return EnumEpayPayInCode_OsuvoxCode_1717
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_PaymentTypeCode_1719() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_PaymentTypeCode_1719)
	return EnumEpayPayInCode_PaymentTypeCode_1719
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_PaymentSystemCode_1720() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_PaymentSystemCode_1720)
	return EnumEpayPayInCode_PaymentSystemCode_1720
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_1723() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_1723)
	return EnumEpayPayInCode_Code_1723
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_1724() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_1724)
	return EnumEpayPayInCode_Code_1724
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_1759() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_1759)
	return EnumEpayPayInCode_Code_1759
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_1760() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_1760)
	return EnumEpayPayInCode_Code_1760
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_1761() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_1761)
	return EnumEpayPayInCode_Code_1761
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_1762() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_1762)
	return EnumEpayPayInCode_Code_1762
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_ShopIdCode_1763() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_ShopIdCode_1763)
	return EnumEpayPayInCode_ShopIdCode_1763
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_1764() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_1764)
	return EnumEpayPayInCode_Code_1764
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_OrderIdCode_1765() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_OrderIdCode_1765)
	return EnumEpayPayInCode_OrderIdCode_1765
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_CoreMerchantCode_1771() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_CoreMerchantCode_1771)
	return EnumEpayPayInCode_CoreMerchantCode_1771
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_PostlinkcorecallbackCode_1780() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_PostlinkcorecallbackCode_1780)
	return EnumEpayPayInCode_PostlinkcorecallbackCode_1780
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_CardTypenilCode_1781() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_CardTypenilCode_1781)
	return EnumEpayPayInCode_CardTypenilCode_1781
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_SwitchpaymentCode_1786() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_SwitchpaymentCode_1786)
	return EnumEpayPayInCode_SwitchpaymentCode_1786
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_1787() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_1787)
	return EnumEpayPayInCode_Code_1787
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_HttpGetMerchantCertiCode_1788() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_HttpGetMerchantCertiCode_1788)
	return EnumEpayPayInCode_HttpGetMerchantCertiCode_1788
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_HttpGetMerchantCreatCode_1789() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_HttpGetMerchantCreatCode_1789)
	return EnumEpayPayInCode_HttpGetMerchantCreatCode_1789
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_EnabledCode_1849() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_EnabledCode_1849)
	return EnumEpayPayInCode_EnabledCode_1849
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_ShopCode_1850() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_ShopCode_1850)
	return EnumEpayPayInCode_ShopCode_1850
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_1855() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_1855)
	return EnumEpayPayInCode_Code_1855
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_TerminalIDCode_1856() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_TerminalIDCode_1856)
	return EnumEpayPayInCode_TerminalIDCode_1856
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_ShopIDCode_1857() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_ShopIDCode_1857)
	return EnumEpayPayInCode_ShopIDCode_1857
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_IDCode_1858() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_IDCode_1858)
	return EnumEpayPayInCode_IDCode_1858
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_ShopidCode_1859() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_ShopidCode_1859)
	return EnumEpayPayInCode_ShopidCode_1859
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_TokenExpireInSecondsCode_1860() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_TokenExpireInSecondsCode_1860)
	return EnumEpayPayInCode_TokenExpireInSecondsCode_1860
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Apiepay1Code_1899() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Apiepay1Code_1899)
	return EnumEpayPayInCode_Apiepay1Code_1899
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_RequestCode_1900() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_RequestCode_1900)
	return EnumEpayPayInCode_RequestCode_1900
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_1901() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_1901)
	return EnumEpayPayInCode_Code_1901
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_1902() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_1902)
	return EnumEpayPayInCode_Code_1902
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_JSONCode_1903() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_JSONCode_1903)
	return EnumEpayPayInCode_JSONCode_1903
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_IdserviceCode_1944() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_IdserviceCode_1944)
	return EnumEpayPayInCode_IdserviceCode_1944
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_UpdateMerchantContacCode_1945() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_UpdateMerchantContacCode_1945)
	return EnumEpayPayInCode_UpdateMerchantContacCode_1945
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_ContactTypenameserviCode_1946() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_ContactTypenameserviCode_1946)
	return EnumEpayPayInCode_ContactTypenameserviCode_1946
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_1954() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_1954)
	return EnumEpayPayInCode_Code_1954
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_1955() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_1955)
	return EnumEpayPayInCode_Code_1955
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_1956() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_1956)
	return EnumEpayPayInCode_Code_1956
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_1961() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_1961)
	return EnumEpayPayInCode_Code_1961
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_1962() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_1962)
	return EnumEpayPayInCode_Code_1962
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_1964() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_1964)
	return EnumEpayPayInCode_Code_1964
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_TerminalshopNameCode_1965() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_TerminalshopNameCode_1965)
	return EnumEpayPayInCode_TerminalshopNameCode_1965
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_1970() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_1970)
	return EnumEpayPayInCode_Code_1970
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_InvoiceIDCode_1971() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_InvoiceIDCode_1971)
	return EnumEpayPayInCode_InvoiceIDCode_1971
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Amount0Code_1975() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Amount0Code_1975)
	return EnumEpayPayInCode_Amount0Code_1975
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_1976() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_1976)
	return EnumEpayPayInCode_Code_1976
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_1977() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_1977)
	return EnumEpayPayInCode_Code_1977
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_TokenexpiredtryagainCode_1979() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_TokenexpiredtryagainCode_1979)
	return EnumEpayPayInCode_TokenexpiredtryagainCode_1979
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_MPANCode_1980() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_MPANCode_1980)
	return EnumEpayPayInCode_MPANCode_1980
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_2008() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_2008)
	return EnumEpayPayInCode_Code_2008
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_RecordnotfoundCode_2009() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_RecordnotfoundCode_2009)
	return EnumEpayPayInCode_RecordnotfoundCode_2009
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_2015() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_2015)
	return EnumEpayPayInCode_Code_2015
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_2016() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_2016)
	return EnumEpayPayInCode_Code_2016
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_RedismaxReqPerSecCode_2017() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_RedismaxReqPerSecCode_2017)
	return EnumEpayPayInCode_RedismaxReqPerSecCode_2017
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_2018() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_2018)
	return EnumEpayPayInCode_Code_2018
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_GooglePayCode_2028() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_GooglePayCode_2028)
	return EnumEpayPayInCode_GooglePayCode_2028
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_GooglePayCode_2030() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_GooglePayCode_2030)
	return EnumEpayPayInCode_GooglePayCode_2030
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_AccountIDCode_2031() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_AccountIDCode_2031)
	return EnumEpayPayInCode_AccountIDCode_2031
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_CallbackCode_2032() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_CallbackCode_2032)
	return EnumEpayPayInCode_CallbackCode_2032
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_2033() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_2033)
	return EnumEpayPayInCode_Code_2033
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_2046() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_2046)
	return EnumEpayPayInCode_Code_2046
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_JSONCode_2048() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_JSONCode_2048)
	return EnumEpayPayInCode_JSONCode_2048
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_2049() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_2049)
	return EnumEpayPayInCode_Code_2049
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_2050() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_2050)
	return EnumEpayPayInCode_Code_2050
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_2051() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_2051)
	return EnumEpayPayInCode_Code_2051
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_XLSURLCode_2052() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_XLSURLCode_2052)
	return EnumEpayPayInCode_XLSURLCode_2052
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_2053() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_2053)
	return EnumEpayPayInCode_Code_2053
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_JSONUpdateProfileStaCode_2057() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_JSONUpdateProfileStaCode_2057)
	return EnumEpayPayInCode_JSONUpdateProfileStaCode_2057
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_JSONCreateStaffProfiCode_2058() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_JSONCreateStaffProfiCode_2058)
	return EnumEpayPayInCode_JSONCreateStaffProfiCode_2058
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_2059() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_2059)
	return EnumEpayPayInCode_Code_2059
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_2060() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_2060)
	return EnumEpayPayInCode_Code_2060
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_InvalidamountamountmCode_2061() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_InvalidamountamountmCode_2061)
	return EnumEpayPayInCode_InvalidamountamountmCode_2061
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_CorebusinessreportCode_2067() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_CorebusinessreportCode_2067)
	return EnumEpayPayInCode_CorebusinessreportCode_2067
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_2069() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_2069)
	return EnumEpayPayInCode_Code_2069
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_EmailCode_2070() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_EmailCode_2070)
	return EnumEpayPayInCode_EmailCode_2070
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_GinBindCode_2074() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_GinBindCode_2074)
	return EnumEpayPayInCode_GinBindCode_2074
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_XLSTIDXLSMIDCode_2075() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_XLSTIDXLSMIDCode_2075)
	return EnumEpayPayInCode_XLSTIDXLSMIDCode_2075
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_2076() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_2076)
	return EnumEpayPayInCode_Code_2076
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_MPGSCode_2085() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_MPGSCode_2085)
	return EnumEpayPayInCode_MPGSCode_2085
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_MPGSCode_2086() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_MPGSCode_2086)
	return EnumEpayPayInCode_MPGSCode_2086
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_MPGSCode_2087() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_MPGSCode_2087)
	return EnumEpayPayInCode_MPGSCode_2087
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_MPGSCode_2088() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_MPGSCode_2088)
	return EnumEpayPayInCode_MPGSCode_2088
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_MPGSCode_2089() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_MPGSCode_2089)
	return EnumEpayPayInCode_MPGSCode_2089
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_MPGSCode_2090() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_MPGSCode_2090)
	return EnumEpayPayInCode_MPGSCode_2090
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_PANBINCode_2091() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_PANBINCode_2091)
	return EnumEpayPayInCode_PANBINCode_2091
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_BINorPANrestrictedCode_2092() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_BINorPANrestrictedCode_2092)
	return EnumEpayPayInCode_BINorPANrestrictedCode_2092
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_BindCode_2122() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_BindCode_2122)
	return EnumEpayPayInCode_BindCode_2122
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_2123() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_2123)
	return EnumEpayPayInCode_Code_2123
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_2124() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_2124)
	return EnumEpayPayInCode_Code_2124
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_MerchantCode_2125() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_MerchantCode_2125)
	return EnumEpayPayInCode_MerchantCode_2125
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_MerchantCode_2126() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_MerchantCode_2126)
	return EnumEpayPayInCode_MerchantCode_2126
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_2127() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_2127)
	return EnumEpayPayInCode_Code_2127
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_2128() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_2128)
	return EnumEpayPayInCode_Code_2128
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_2129() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_2129)
	return EnumEpayPayInCode_Code_2129
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_OpenwayIDCode_2130() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_OpenwayIDCode_2130)
	return EnumEpayPayInCode_OpenwayIDCode_2130
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_USDCode_2131() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_USDCode_2131)
	return EnumEpayPayInCode_USDCode_2131
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_2132() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_2132)
	return EnumEpayPayInCode_Code_2132
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_2133() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_2133)
	return EnumEpayPayInCode_Code_2133
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_2134() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_2134)
	return EnumEpayPayInCode_Code_2134
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_2135() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_2135)
	return EnumEpayPayInCode_Code_2135
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_XlsCode_2136() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_XlsCode_2136)
	return EnumEpayPayInCode_XlsCode_2136
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_2137() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_2137)
	return EnumEpayPayInCode_Code_2137
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_PostlinkcorecallbackCode_2138() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_PostlinkcorecallbackCode_2138)
	return EnumEpayPayInCode_PostlinkcorecallbackCode_2138
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_2139() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_2139)
	return EnumEpayPayInCode_Code_2139
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_CryptogramopenwayIDCode_2146() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_CryptogramopenwayIDCode_2146)
	return EnumEpayPayInCode_CryptogramopenwayIDCode_2146
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_IPCode_2147() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_IPCode_2147)
	return EnumEpayPayInCode_IPCode_2147
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_2148() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_2148)
	return EnumEpayPayInCode_Code_2148
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_FormdatasftpproxyCode_2152() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_FormdatasftpproxyCode_2152)
	return EnumEpayPayInCode_FormdatasftpproxyCode_2152
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_HttpNewRequestsftpprCode_2153() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_HttpNewRequestsftpprCode_2153)
	return EnumEpayPayInCode_HttpNewRequestsftpprCode_2153
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_RedispipelineCode_2154() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_RedispipelineCode_2154)
	return EnumEpayPayInCode_RedispipelineCode_2154
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_TransactionamountshoCode_2156() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_TransactionamountshoCode_2156)
	return EnumEpayPayInCode_TransactionamountshoCode_2156
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_2157() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_2157)
	return EnumEpayPayInCode_Code_2157
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_URLpagesizeCode_2158() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_URLpagesizeCode_2158)
	return EnumEpayPayInCode_URLpagesizeCode_2158
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_2159() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_2159)
	return EnumEpayPayInCode_Code_2159
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_URLpagesizeCode_2191() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_URLpagesizeCode_2191)
	return EnumEpayPayInCode_URLpagesizeCode_2191
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_PagesizeCode_2193() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_PagesizeCode_2193)
	return EnumEpayPayInCode_PagesizeCode_2193
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_PagesizeCode_2194() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_PagesizeCode_2194)
	return EnumEpayPayInCode_PagesizeCode_2194
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_2200() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_2200)
	return EnumEpayPayInCode_Code_2200
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_2203() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_2203)
	return EnumEpayPayInCode_Code_2203
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_2204() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_2204)
	return EnumEpayPayInCode_Code_2204
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_2205() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_2205)
	return EnumEpayPayInCode_Code_2205
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_UnmarshalCode_2206() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_UnmarshalCode_2206)
	return EnumEpayPayInCode_UnmarshalCode_2206
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_2207() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_2207)
	return EnumEpayPayInCode_Code_2207
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_2210() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_2210)
	return EnumEpayPayInCode_Code_2210
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_PublicIDonboardingCode_2211() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_PublicIDonboardingCode_2211)
	return EnumEpayPayInCode_PublicIDonboardingCode_2211
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_AclserviceCode_2212() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_AclserviceCode_2212)
	return EnumEpayPayInCode_AclserviceCode_2212
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_StaffCode_2213() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_StaffCode_2213)
	return EnumEpayPayInCode_StaffCode_2213
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_OpenWayCardIdCode_2214() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_OpenWayCardIdCode_2214)
	return EnumEpayPayInCode_OpenWayCardIdCode_2214
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_2218() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_2218)
	return EnumEpayPayInCode_Code_2218
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_2237() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_2237)
	return EnumEpayPayInCode_Code_2237
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_PostlinkaCode_2238() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_PostlinkaCode_2238)
	return EnumEpayPayInCode_PostlinkaCode_2238
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_PostlinkaCode_2239() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_PostlinkaCode_2239)
	return EnumEpayPayInCode_PostlinkaCode_2239
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_PostlinkaCode_2240() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_PostlinkaCode_2240)
	return EnumEpayPayInCode_PostlinkaCode_2240
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_PostlinkaCode_2241() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_PostlinkaCode_2241)
	return EnumEpayPayInCode_PostlinkaCode_2241
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_PostlinkCode_2242() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_PostlinkCode_2242)
	return EnumEpayPayInCode_PostlinkCode_2242
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_2243() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_2243)
	return EnumEpayPayInCode_Code_2243
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_2244() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_2244)
	return EnumEpayPayInCode_Code_2244
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_2245() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_2245)
	return EnumEpayPayInCode_Code_2245
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_2249() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_2249)
	return EnumEpayPayInCode_Code_2249
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_P2PCode_2250() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_P2PCode_2250)
	return EnumEpayPayInCode_P2PCode_2250
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_2257() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_2257)
	return EnumEpayPayInCode_Code_2257
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_JSONjsonMarshalJSONCode_2268() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_JSONjsonMarshalJSONCode_2268)
	return EnumEpayPayInCode_JSONjsonMarshalJSONCode_2268
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_2271() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_2271)
	return EnumEpayPayInCode_Code_2271
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_2301() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_2301)
	return EnumEpayPayInCode_Code_2301
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_2305() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_2305)
	return EnumEpayPayInCode_Code_2305
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_QrbyqrCode_2322() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_QrbyqrCode_2322)
	return EnumEpayPayInCode_QrbyqrCode_2322
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_2323() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_2323)
	return EnumEpayPayInCode_Code_2323
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_2326() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_2326)
	return EnumEpayPayInCode_Code_2326
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_CardIDCode_2339() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_CardIDCode_2339)
	return EnumEpayPayInCode_CardIDCode_2339
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_2349() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_2349)
	return EnumEpayPayInCode_Code_2349
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_2350() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_2350)
	return EnumEpayPayInCode_Code_2350
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_2351() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_2351)
	return EnumEpayPayInCode_Code_2351
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_2352() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_2352)
	return EnumEpayPayInCode_Code_2352
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_2353() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_2353)
	return EnumEpayPayInCode_Code_2353
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_2354() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_2354)
	return EnumEpayPayInCode_Code_2354
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_ClientIDCode_2355() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_ClientIDCode_2355)
	return EnumEpayPayInCode_ClientIDCode_2355
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_2356() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_2356)
	return EnumEpayPayInCode_Code_2356
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_ResultCodestatusIDCode_2360() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_ResultCodestatusIDCode_2360)
	return EnumEpayPayInCode_ResultCodestatusIDCode_2360
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_PaymentsystemCode_2362() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_PaymentsystemCode_2362)
	return EnumEpayPayInCode_PaymentsystemCode_2362
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_KafkaCode_2365() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_KafkaCode_2365)
	return EnumEpayPayInCode_KafkaCode_2365
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_ShopInfoCode_2366() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_ShopInfoCode_2366)
	return EnumEpayPayInCode_ShopInfoCode_2366
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_2367() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_2367)
	return EnumEpayPayInCode_Code_2367
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_2433() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_2433)
	return EnumEpayPayInCode_Code_2433
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_UZGWCode_2435() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_UZGWCode_2435)
	return EnumEpayPayInCode_UZGWCode_2435
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_Code_2624() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_Code_2624)
	return EnumEpayPayInCode_Code_2624
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_ChecktransactionstatCode_2660() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_ChecktransactionstatCode_2660)
	return EnumEpayPayInCode_ChecktransactionstatCode_2660
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_CorePaymentCode_2678() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_CorePaymentCode_2678)
	return EnumEpayPayInCode_CorePaymentCode_2678
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_CorePaymentCode_2679() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_CorePaymentCode_2679)
	return EnumEpayPayInCode_CorePaymentCode_2679
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_OtpcodeapiuzgatewayCode_2704() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_OtpcodeapiuzgatewayCode_2704)
	return EnumEpayPayInCode_OtpcodeapiuzgatewayCode_2704
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_CodeapiuzgatewayCode_2705() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_CodeapiuzgatewayCode_2705)
	return EnumEpayPayInCode_CodeapiuzgatewayCode_2705
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_CannotapproveatthistCode_2740() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_CannotapproveatthistCode_2740)
	return EnumEpayPayInCode_CannotapproveatthistCode_2740
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_NotsufficientfundsCode_484() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_NotsufficientfundsCode_484)
	return EnumEpayPayInCode_NotsufficientfundsCode_484
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_TheoperationfailedplCode_454() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_TheoperationfailedplCode_454)
	return EnumEpayPayInCode_TheoperationfailedplCode_454
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_ServerisnotrespondinCode_459() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_ServerisnotrespondinCode_459)
	return EnumEpayPayInCode_ServerisnotrespondinCode_459
}

func (u *EnumEpayPayInCodeUsage) EnumEpayPayInCode_DonotreattemptrestriCode_2872() EnumEpayPayInCode {
	u.Use(EnumEpayPayInCode_DonotreattemptrestriCode_2872)
	return EnumEpayPayInCode_DonotreattemptrestriCode_2872
}
