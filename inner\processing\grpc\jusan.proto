edition = "2023";

package processing.jusan.jusan;

option go_package = "git.local/sensitive/innerpb/processing/grpc";

import "inner/processing/grpc/multiacquiring.proto";
import "inner/processing/grpc/multiacquiring_emission.proto";
import "inner/processing/grpc/multiacquiring_balance.proto";
import "inner/processing/grpc/transaction_status.proto";
import "google/protobuf/empty.proto";
import "mvp/proto/refs.proto";
import "mvp/proto/enum.proto";
import "google/protobuf/descriptor.proto";

service Jusan {
  rpc PayIn(processing.multiacquiring.multiacquiring.PayInRequestData) returns (processing.multiacquiring.multiacquiring.PayInResponseData) {}
  rpc OneClickPayIn(processing.multiacquiring.multiacquiring.OneClickPayInRequestData) returns (processing.multiacquiring.multiacquiring.PayInResponseData) {}
  rpc ThreeDSConfirm(processing.multiacquiring.multiacquiring.ThreeDSRequestData) returns (processing.multiacquiring.multiacquiring.ThreeDSResponseData) {}
  rpc ThreeDSResume(processing.multiacquiring.multiacquiring.ThreeDSResumeRequest) returns (processing.multiacquiring.multiacquiring.ThreeDSResumeResponse) {}
  rpc PayOut(processing.multiacquiring.multiacquiring.PayOutRequestData) returns (processing.multiacquiring.multiacquiring.PayOutResponseData) {}
  rpc GetBankTransactionStatus(processing.multiacquiring.multiacquiring.BankTransactionStatusRequest) returns (processing.multiacquiring.multiacquiring.BankTransactionStatusResponse) {}
  rpc GetBankTransactionStatusUnformated(processing.multiacquiring.multiacquiring.BankTransactionStatusUnformatedRequest) returns (processing.multiacquiring.multiacquiring.BankTransactionStatusUnformatedResponse) {}
  rpc Refund(processing.multiacquiring.multiacquiring.RefundRequest) returns (processing.multiacquiring.multiacquiring.RefundResponse) {}
  rpc GooglePay(processing.multiacquiring.multiacquiring.GooglePayRequestData) returns (processing.multiacquiring.multiacquiring.GooglePayResponseData) {}
  rpc ApplePay(processing.multiacquiring.multiacquiring.ApplePayRequestData) returns (processing.multiacquiring.multiacquiring.ApplePayResponseData) {}
  rpc TwoStagePayIn(processing.multiacquiring.multiacquiring.TwoStagePayInRequest) returns (processing.multiacquiring.multiacquiring.TwoStagePayInResponse) {}
  rpc Charge(processing.multiacquiring.multiacquiring.ChargeRequest) returns (processing.multiacquiring.multiacquiring.ChargeResponse) {}
  rpc Cancel(processing.multiacquiring.multiacquiring.CancelRequest) returns (processing.multiacquiring.multiacquiring.CancelResponse) {}
  rpc MakeToken(processing.multiacquiring.multiacquiring.PayInRequestData) returns (processing.multiacquiring.multiacquiring.PayInResponseData) {}
  rpc GetEmission(google.protobuf.Empty) returns (processing.multiacquiring.multiacquiring_emission.EmissionResponse) {}
  rpc ConfirmEmission(processing.multiacquiring.multiacquiring_emission.EmoneyRequest) returns (processing.multiacquiring.multiacquiring_emission.EmoneyResponse) {}
  rpc GetAcquirerIdentifier(google.protobuf.Empty) returns (processing.multiacquiring.multiacquiring.GetAcquirerIdentifierResponse) {}
  rpc CheckBalance(processing.multiacquiring.multiacquiring_balance.CheckBalanceRequest) returns (processing.multiacquiring.multiacquiring_balance.CheckBalanceResponse) {}
  rpc ResolveVisaAlias(processing.multiacquiring.multiacquiring.ResolveVisaAliasRequest) returns (processing.multiacquiring.multiacquiring.ResolveVisaAliasResponse) {}
  rpc PayOutByPhone(processing.multiacquiring.multiacquiring.PayOutByPhoneRequestData) returns (processing.multiacquiring.multiacquiring.PayOutResponseByPhoneData) {}
}

message JusanResponseStatusRef {
  string stage = 1;
  string status = 2;
  string description = 3;
  processing.transaction.transaction_status.EnumTransactionStatus transaction_status = 4;
}

extend google.protobuf.EnumValueOptions {
  JusanResponseStatusRef jusan_response_status_value = 100110;
}

extend google.protobuf.EnumOptions {
  JusanResponseStatusRef default_jusan_response_status_value = 100111;
}

enum JusanResponseStatus {
  option(mvp.default_ref) = "default_jusan_response_status_value";
  option(mvp.ref) = "jusan_response_status_value";

  option(default_jusan_response_status_value) = {
    stage: "0"
    status: "0"
    description: "0"
    transaction_status: TransactionStatusError
  };

  Stage1Status1 = 0 [(jusan_response_status_value) = {
    stage: "1"
    status: "1"
    description: "Ошибка"
    transaction_status: TransactionStatusFailed
  }, (mvp.from_string) = "1:1"];

  Stage2Status1 = 1 [(jusan_response_status_value) = {
    stage: "2"
    status: "1"
    description: "Ошибка"
    transaction_status: TransactionStatusFailed
  }, (mvp.from_string) = "2:1"];

  Stage3Status1 = 2 [(jusan_response_status_value) = {
    stage: "3"
    status: "1"
    description: "Ошибка"
    transaction_status: TransactionStatusFailed
  }, (mvp.from_string) = "3:1"];

  Stage1Status0 = 3 [(jusan_response_status_value) = {
    stage: "1"
    status: "0"
    description: "В процессе"
    transaction_status: TransactionStatusProcessed
  }, (mvp.from_string) = "1:0"];

  Stage2Status0 = 4 [(jusan_response_status_value) = {
    stage: "2"
    status: "0"
    description: "В процессе"
    transaction_status: TransactionStatusProcessed
  }, (mvp.from_string) = "2:0"];

  Stage1Status2 = 5 [(jusan_response_status_value) = {
    stage: "1"
    status: "2"
    description: "В процессе"
    transaction_status: TransactionStatusProcessed
  }, (mvp.from_string) = "1:2"];

  Stage2Status2 = 6 [(jusan_response_status_value) = {
    stage: "2"
    status: "2"
    description: "В процессе"
    transaction_status: TransactionStatusProcessed
  }, (mvp.from_string) = "2:2"];

  Stage3Status2 = 7 [(jusan_response_status_value) = {
    stage: "3"
    status: "2"
    description: "В процессе"
    transaction_status: TransactionStatusProcessed
  }, (mvp.from_string) = "3:2"];

  Stage4Status0 = 8 [(jusan_response_status_value) = {
    stage: "4"
    status: "0"
    description: "Успешно"
    transaction_status: TransactionStatusSuccess
  }, (mvp.from_string) = "4:0"];

  Stage4Status1 = 9 [(jusan_response_status_value) = {
    stage: "4"
    status: "1"
    description: "Успешно"
    transaction_status: TransactionStatusSuccess
  }, (mvp.from_string) = "4:1"];

  Stage4Status2 = 10 [(jusan_response_status_value) = {
    stage: "4"
    status: "2"
    description: "Успешно"
    transaction_status: TransactionStatusSuccess
  }, (mvp.from_string) = "4:2"];
}
