// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.5.1
// - protoc             v5.29.3
// source: inner/processing/grpc/terminal_cards.proto

package grpc

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.64.0 or later.
const _ = grpc.SupportPackageIsVersion9

const (
	TerminalCards_GetCardInfoByPan_FullMethodName = "/processing.acquirer.terminal_cards.TerminalCards/GetCardInfoByPan"
)

// TerminalCardsClient is the client API for TerminalCards service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type TerminalCardsClient interface {
	// GetCardInfoByPan Запрос на получение данных карты по номеру карты
	GetCardInfoByPan(ctx context.Context, in *GetCardInfoByPanRequestV1, opts ...grpc.CallOption) (*GetCardInfoByPanResponseV1, error)
}

type terminalCardsClient struct {
	cc grpc.ClientConnInterface
}

func NewTerminalCardsClient(cc grpc.ClientConnInterface) TerminalCardsClient {
	return &terminalCardsClient{cc}
}

func (c *terminalCardsClient) GetCardInfoByPan(ctx context.Context, in *GetCardInfoByPanRequestV1, opts ...grpc.CallOption) (*GetCardInfoByPanResponseV1, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetCardInfoByPanResponseV1)
	err := c.cc.Invoke(ctx, TerminalCards_GetCardInfoByPan_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// TerminalCardsServer is the server API for TerminalCards service.
// All implementations must embed UnimplementedTerminalCardsServer
// for forward compatibility.
type TerminalCardsServer interface {
	// GetCardInfoByPan Запрос на получение данных карты по номеру карты
	GetCardInfoByPan(context.Context, *GetCardInfoByPanRequestV1) (*GetCardInfoByPanResponseV1, error)
	mustEmbedUnimplementedTerminalCardsServer()
}

// UnimplementedTerminalCardsServer must be embedded to have
// forward compatible implementations.
//
// NOTE: this should be embedded by value instead of pointer to avoid a nil
// pointer dereference when methods are called.
type UnimplementedTerminalCardsServer struct{}

func (UnimplementedTerminalCardsServer) GetCardInfoByPan(context.Context, *GetCardInfoByPanRequestV1) (*GetCardInfoByPanResponseV1, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetCardInfoByPan not implemented")
}
func (UnimplementedTerminalCardsServer) mustEmbedUnimplementedTerminalCardsServer() {}
func (UnimplementedTerminalCardsServer) testEmbeddedByValue()                       {}

// UnsafeTerminalCardsServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to TerminalCardsServer will
// result in compilation errors.
type UnsafeTerminalCardsServer interface {
	mustEmbedUnimplementedTerminalCardsServer()
}

func RegisterTerminalCardsServer(s grpc.ServiceRegistrar, srv TerminalCardsServer) {
	// If the following call pancis, it indicates UnimplementedTerminalCardsServer was
	// embedded by pointer and is nil.  This will cause panics if an
	// unimplemented method is ever invoked, so we test this at initialization
	// time to prevent it from happening at runtime later due to I/O.
	if t, ok := srv.(interface{ testEmbeddedByValue() }); ok {
		t.testEmbeddedByValue()
	}
	s.RegisterService(&TerminalCards_ServiceDesc, srv)
}

func _TerminalCards_GetCardInfoByPan_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetCardInfoByPanRequestV1)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TerminalCardsServer).GetCardInfoByPan(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: TerminalCards_GetCardInfoByPan_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TerminalCardsServer).GetCardInfoByPan(ctx, req.(*GetCardInfoByPanRequestV1))
	}
	return interceptor(ctx, in, info, handler)
}

// TerminalCards_ServiceDesc is the grpc.ServiceDesc for TerminalCards service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var TerminalCards_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "processing.acquirer.terminal_cards.TerminalCards",
	HandlerType: (*TerminalCardsServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetCardInfoByPan",
			Handler:    _TerminalCards_GetCardInfoByPan_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "inner/processing/grpc/terminal_cards.proto",
}
