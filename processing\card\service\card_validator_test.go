package service

import (
	"context"
	"crypto/aes"
	"errors"
	"testing"

	"git.local/sensitive/innerpb/processing/goerr"
	"git.local/sensitive/sdk/dog"
	"git.local/sensitive/testsdk"

	"github.com/golang/mock/gomock"
	"github.com/stretchr/testify/require"

	"git.local/sensitive/processing/card/model"
	"git.local/sensitive/processing/card/repository/database/databasemocks"
	"git.local/sensitive/sdk/dog/e2e"
)

func TestCreateNewKey(t *testing.T) {
	type createOp struct {
		isCalled bool
		input    *model.Key
		wantErr  error
	}

	tests := []struct {
		name      string
		wantErr   error
		appConfig map[string]any
		create    createOp
	}{
		{
			name:    "error_encrypting",
			wantErr: aes.KeySizeError(3),
			appConfig: map[string]any{
				"SYMMETRIC_KEY": badKey,
			},
		},
		{
			name:    "error_creating",
			wantErr: errors.New("some error"),
			appConfig: map[string]any{
				"SYMMETRIC_KEY": normalKey,
			},
			create: createOp{
				input: &model.Key{
					Key: "AAAAAAAAAAAAAAAAAAAAAJWXVsNZooT1Nj5qlh9r4ws=",
				},
				isCalled: true,
				wantErr:  errors.New("some error"),
			},
		},
		{
			name:    "success",
			wantErr: nil,
			appConfig: map[string]any{
				"SYMMETRIC_KEY": normalKey,
			},
			create: createOp{
				input: &model.Key{
					Key: "AAAAAAAAAAAAAAAAAAAAAJWXVsNZooT1Nj5qlh9r4ws=",
				},
				isCalled: true,
				wantErr:  nil,
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctrl := gomock.NewController(t)

			ctx, cancel := context.WithCancel(context.Background())
			e2e.InitAppE2E(t, ctx, cancel, nil, nil, tt.appConfig)

			keyDBMock := databasemocks.NewMockKeyer(ctrl)

			if tt.create.isCalled {
				keyDBMock.EXPECT().Create(
					gomock.Any(),
					tt.create.input,
				).Return(tt.create.wantErr)
			}

			s := KeyRotationService{
				keyRepo: keyDBMock,
				randomStringGenerator: func() string {
					return "something"
				},
			}

			err := s.CreateNewKey(context.Background())
			if tt.wantErr != nil {
				require.Error(t, err)
				require.Equal(t, tt.wantErr, err)
			} else {
				require.NoError(t, err)
			}
		})
	}
}

func TestRotateKeyInCards(t *testing.T) {
	type getActualKeyOp struct {
		output    model.Key
		outputErr error
	}

	type getCardsByOldKeyOp struct {
		outputs []struct {
			lastID    uint64
			output    []*model.Card
			outputErr error
		}
	}

	type getKeyByIDOp struct {
		isCalled  bool
		inputID   uint64
		output    model.Key
		outputErr error
	}

	type updateDataOp struct {
		isCalled  bool
		input     []*model.Card
		outputErr error
	}

	oldKeyCards := []*model.Card{
		{
			Id:           1,
			KeyId:        1,
			EncryptPan:   "AAAAAAAAAAAAAAAAAAAAAJWXVsNZooT1Nj5qlh9r4ws=",
			EncryptMonth: "AAAAAAAAAAAAAAAAAAAAAJWXVsNZooT1Nj5qlh9r4ws=",
			EncryptYear:  "AAAAAAAAAAAAAAAAAAAAAJWXVsNZooT1Nj5qlh9r4ws=",
			EncryptName:  "AAAAAAAAAAAAAAAAAAAAAJWXVsNZooT1Nj5qlh9r4ws=",
		},
	}

	tests := []struct {
		name             string
		wantErr          error
		appConfig        map[string]any
		getActualKey     getActualKeyOp
		getCardsByOldKey getCardsByOldKeyOp
		getKeyByID       getKeyByIDOp
		updateData       updateDataOp
	}{
		{
			name:    "error_get_actual_key",
			wantErr: errors.New("some error"),
			appConfig: map[string]any{
				"SYMMETRIC_KEY": badKey,
			},
			getActualKey: getActualKeyOp{
				outputErr: errors.New("some error"),
			},
		},
		{
			name:    "error_AESDecrypt",
			wantErr: errors.New("some error"),
			appConfig: map[string]any{
				"SYMMETRIC_KEY": badKey,
			},
			getActualKey: getActualKeyOp{
				output: model.Key{
					Key: normalKey,
				},
				outputErr: errors.New("some error"),
			},
		},
		{
			name:    "error_get_cards_by_old_key",
			wantErr: errors.New("some error"),
			appConfig: map[string]any{
				"SYMMETRIC_KEY": normalKey,
			},
			getActualKey: getActualKeyOp{
				output: model.Key{
					Id:  1,
					Key: "AAAAAAAAAAAAAAAAAAAAAD4r1NYivkHsOHdiq9dCiJiz0HJtt7wrVFA8fx/DenGL",
				},
			},
			getCardsByOldKey: getCardsByOldKeyOp{
				outputs: []struct {
					lastID    uint64
					output    []*model.Card
					outputErr error
				}{
					{
						lastID:    0,
						output:    nil,
						outputErr: errors.New("some error"),
					},
				},
			},
		},
		{
			name:    "nil_cards_response",
			wantErr: nil,
			appConfig: map[string]any{
				"SYMMETRIC_KEY": normalKey,
			},
			getActualKey: getActualKeyOp{
				output: model.Key{
					Id:  1,
					Key: "AAAAAAAAAAAAAAAAAAAAAD4r1NYivkHsOHdiq9dCiJiz0HJtt7wrVFA8fx/DenGL",
				},
			},
			getCardsByOldKey: getCardsByOldKeyOp{
				outputs: []struct {
					lastID    uint64
					output    []*model.Card
					outputErr error
				}{
					{
						lastID: 0,
						output: nil,
					},
				},
			},
		},
		{
			name:    "success_one_batch",
			wantErr: nil,
			appConfig: map[string]any{
				"SYMMETRIC_KEY": normalKey,
			},
			getActualKey: getActualKeyOp{
				output: model.Key{
					Id:  1,
					Key: "AAAAAAAAAAAAAAAAAAAAAD4r1NYivkHsOHdiq9dCiJiz0HJtt7wrVFA8fx/DenGL",
				},
			},
			getCardsByOldKey: getCardsByOldKeyOp{
				outputs: []struct {
					lastID    uint64
					output    []*model.Card
					outputErr error
				}{
					{
						lastID:    0,
						output:    oldKeyCards,
						outputErr: nil,
					},
					{
						lastID:    1,
						output:    []*model.Card{}, // цикл остановится
						outputErr: nil,
					},
				},
			},
			getKeyByID: getKeyByIDOp{
				isCalled: true,
				inputID:  1,
				output: model.Key{
					Id:  1,
					Key: "AAAAAAAAAAAAAAAAAAAAAD4r1NYivkHsOHdiq9dCiJiz0HJtt7wrVFA8fx/DenGL",
				},
				outputErr: nil,
			},
			updateData: updateDataOp{
				isCalled:  true,
				input:     oldKeyCards,
				outputErr: nil,
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctrl := gomock.NewController(t)
			defer ctrl.Finish()

			ctx := context.Background()

			e2e.InitAppE2E(t, ctx, nil, nil, nil, tt.appConfig)

			keyRepoMock := databasemocks.NewMockKeyer(ctrl)
			bulkCardUpdaterRepoMock := databasemocks.NewMockBulkCardUpdater(ctrl)

			s := KeyRotationService{
				keyRepo:             keyRepoMock,
				bulkCardUpdaterRepo: bulkCardUpdaterRepoMock,
			}

			keyRepoMock.EXPECT().
				GetActualKey(gomock.Any()).
				Return(tt.getActualKey.output, tt.getActualKey.outputErr)

			if len(tt.getCardsByOldKey.outputs) > 0 {
				for _, out := range tt.getCardsByOldKey.outputs {
					keyRepoMock.EXPECT().
						GetCardsByOldKey(gomock.Any(), tt.getActualKey.output.Id, out.lastID, batchSize).
						Return(out.output, out.outputErr)
				}
			}

			if tt.getKeyByID.isCalled {
				keyRepoMock.EXPECT().GetById(
					gomock.Any(),
					tt.getKeyByID.inputID,
				).Return(
					tt.getKeyByID.output,
					tt.getKeyByID.outputErr,
				)
			}

			if tt.updateData.isCalled {
				bulkCardUpdaterRepoMock.EXPECT().
					UpdateEncryptData(gomock.Any(), tt.updateData.input).
					Return(tt.updateData.outputErr)
			}

			err := s.RotateKeyInCards(ctx)
			if tt.wantErr != nil {
				require.Error(t, err)
				require.Equal(t, tt.wantErr, err)
			} else {
				require.NoError(t, err)
			}
		})
	}
}

func TestDecryptData(t *testing.T) {
	tests := []struct {
		name          string
		wantErr       string
		appConfig     map[string]any
		inputCard     *model.Card
		expectedPan   string
		expectedName  string
		expectedMonth string
		expectedYear  string
	}{
		{
			name:    "nil_card",
			wantErr: goerr.ErrEmptyCard.Error(),
			appConfig: map[string]any{
				"SYMMETRIC_KEY": normalKey,
			},
			inputCard: nil,
		},
		{
			name:    "decrypting_pan_error",
			wantErr: "invalid data length",
			appConfig: map[string]any{
				"SYMMETRIC_KEY": normalKey,
			},
			inputCard: &model.Card{
				KeyId:      1,
				EncryptPan: "",
			},
		},
		{
			name:    "decrypting_name_error",
			wantErr: "invalid data length",
			appConfig: map[string]any{
				"SYMMETRIC_KEY": normalKey,
			},
			inputCard: &model.Card{
				KeyId:       1,
				EncryptPan:  "AAAAAAAAAAAAAAAAAAAAAJWXVsNZooT1Nj5qlh9r4ws=",
				EncryptName: "",
			},
		},
		{
			name:    "decrypting_month_error",
			wantErr: "base64 decode string error illegal base64 data at input byte 0",
			appConfig: map[string]any{
				"SYMMETRIC_KEY": normalKey,
			},
			inputCard: &model.Card{
				KeyId:        1,
				EncryptPan:   "AAAAAAAAAAAAAAAAAAAAAJWXVsNZooT1Nj5qlh9r4ws=",
				EncryptName:  "AAAAAAAAAAAAAAAAAAAAAJWXVsNZooT1Nj5qlh9r4ws=",
				EncryptMonth: "abc",
			},
		},
		{
			name:    "decrypting_year_error",
			wantErr: "base64 decode string error illegal base64 data at input byte 0",
			appConfig: map[string]any{
				"SYMMETRIC_KEY": normalKey,
			},
			inputCard: &model.Card{
				KeyId:        1,
				EncryptPan:   "AAAAAAAAAAAAAAAAAAAAAJWXVsNZooT1Nj5qlh9r4ws=",
				EncryptName:  "AAAAAAAAAAAAAAAAAAAAAJWXVsNZooT1Nj5qlh9r4ws=",
				EncryptMonth: "AAAAAAAAAAAAAAAAAAAAAJWXVsNZooT1Nj5qlh9r4ws=",
				EncryptYear:  "abc",
			},
		},
		{
			name:    "success",
			wantErr: "",
			appConfig: map[string]any{
				"SYMMETRIC_KEY": normalKey,
			},
			inputCard: &model.Card{
				KeyId:       1,
				EncryptPan:  "AAAAAAAAAAAAAAAAAAAAAJWXVsNZooT1Nj5qlh9r4ws=",
				EncryptName: "AAAAAAAAAAAAAAAAAAAAAJWXVsNZooT1Nj5qlh9r4ws=",
			},
			expectedPan:  "something",
			expectedName: "something",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctrl := gomock.NewController(t)
			defer ctrl.Finish()

			ctx := context.Background()

			e2e.InitAppE2E(t, ctx, nil, nil, nil, tt.appConfig)

			keyRepoMock := databasemocks.NewMockKeyer(ctrl)

			s := KeyRotationService{
				keyRepo: keyRepoMock,
			}

			decryptCardKey, _ := dog.AESDecrypt("AAAAAAAAAAAAAAAAAAAAAD4r1NYivkHsOHdiq9dCiJiz0HJtt7wrVFA8fx/DenGL", normalKey)

			pan, name, month, year, err := s.decryptData(tt.inputCard, decryptCardKey)

			if tt.wantErr != "" {
				require.Equal(t, tt.wantErr, testsdk.ErrorString(err))
				require.Error(t, err)
			} else {
				require.NoError(t, err)
			}

			require.Equal(t, tt.expectedPan, pan)
			require.Equal(t, tt.expectedName, name)
			require.Equal(t, tt.expectedMonth, month)
			require.Equal(t, tt.expectedYear, year)
		})
	}
}

func TestEncryptData(t *testing.T) {
	tests := []struct {
		name           string
		wantErr        string
		appConfig      map[string]any
		inputPan       string
		inputName      string
		inputMonth     string
		inputYear      string
		inputActualKey string
		expectedPan    string
		expectedName   string
		expectedMonth  string
		expectedYear   string
	}{
		{
			name:    "decrypting_pan_error",
			wantErr: "crypto/aes: invalid key size 64",
			appConfig: map[string]any{
				"SYMMETRIC_KEY": normalKey,
			},
			inputActualKey: "AAAAAAAAAAAAAAAAAAAAAD4r1NYivkHsOHdiq9dCiJiz0HJtt7wrVFA8fx/DenGL",
			inputPan:       "",
		},
		{
			name:    "decrypting_name_error",
			wantErr: "crypto/aes: invalid key size 64",
			appConfig: map[string]any{
				"SYMMETRIC_KEY": normalKey,
			},
			inputActualKey: "AAAAAAAAAAAAAAAAAAAAAD4r1NYivkHsOHdiq9dCiJiz0HJtt7wrVFA8fx/DenGL",
			inputPan:       "AAAAAAAAAAAAAAAAAAAAAJWXVsNZooT1Nj5qlh9r4ws=",
			inputName:      "",
		},
		{
			name:    "decrypting_month_error",
			wantErr: "crypto/aes: invalid key size 64",
			appConfig: map[string]any{
				"SYMMETRIC_KEY": normalKey,
			},
			inputActualKey: "AAAAAAAAAAAAAAAAAAAAAD4r1NYivkHsOHdiq9dCiJiz0HJtt7wrVFA8fx/DenGL",
			inputPan:       "AAAAAAAAAAAAAAAAAAAAAJWXVsNZooT1Nj5qlh9r4ws=",
			inputName:      "AAAAAAAAAAAAAAAAAAAAAJWXVsNZooT1Nj5qlh9r4ws=",
			inputMonth:     "",
		},
		{
			name:    "decrypting_year_error",
			wantErr: "crypto/aes: invalid key size 64",
			appConfig: map[string]any{
				"SYMMETRIC_KEY": normalKey,
			},
			inputActualKey: "AAAAAAAAAAAAAAAAAAAAAD4r1NYivkHsOHdiq9dCiJiz0HJtt7wrVFA8fx/DenGL",
			inputPan:       "AAAAAAAAAAAAAAAAAAAAAJWXVsNZooT1Nj5qlh9r4ws=",
			inputName:      "AAAAAAAAAAAAAAAAAAAAAJWXVsNZooT1Nj5qlh9r4ws=",
			inputYear:      "",
		},
		{
			name:    "success",
			wantErr: "crypto/aes: invalid key size 64",
			appConfig: map[string]any{
				"SYMMETRIC_KEY": normalKey,
			},
			inputActualKey: "AAAAAAAAAAAAAAAAAAAAAD4r1NYivkHsOHdiq9dCiJiz0HJtt7wrVFA8fx/DenGL",
			inputPan:       "AAAAAAAAAAAAAAAAAAAAAJWXVsNZooT1Nj5qlh9r4ws=",
			inputName:      "AAAAAAAAAAAAAAAAAAAAAJWXVsNZooT1Nj5qlh9r4ws=",
			inputMonth:     "AAAAAAAAAAAAAAAAAAAAAJWXVsNZooT1Nj5qlh9r4ws=",
			inputYear:      "AAAAAAAAAAAAAAAAAAAAAJWXVsNZooT1Nj5qlh9r4ws=",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctrl := gomock.NewController(t)
			defer ctrl.Finish()

			ctx := context.Background()

			e2e.InitAppE2E(t, ctx, nil, nil, nil, tt.appConfig)

			keyRepoMock := databasemocks.NewMockKeyer(ctrl)

			s := KeyRotationService{
				keyRepo: keyRepoMock,
			}

			pan, name, month, year, err := s.encryptData(
				tt.inputPan, tt.inputName, tt.inputMonth, tt.inputYear, tt.inputActualKey,
			)

			require.Equal(t, tt.wantErr, testsdk.ErrorString(err))
			require.Equal(t, tt.expectedPan, pan)
			require.Equal(t, tt.expectedName, name)
			require.Equal(t, tt.expectedMonth, month)
			require.Equal(t, tt.expectedYear, year)
		})
	}
}
