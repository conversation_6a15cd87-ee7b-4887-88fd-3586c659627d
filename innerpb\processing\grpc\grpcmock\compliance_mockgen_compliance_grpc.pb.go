// Code generated by MockGen. DO NOT EDIT.
// Source: compliance_grpc.pb.go

// Package grpcmock is a generated GoMock package.
package grpcmock

import (
	context "context"
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
	grpc "google.golang.org/grpc"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
)

// MockComplianceClient is a mock of ComplianceClient interface.
type MockComplianceClient struct {
	ctrl     *gomock.Controller
	recorder *MockComplianceClientMockRecorder
}

// MockComplianceClientMockRecorder is the mock recorder for MockComplianceClient.
type MockComplianceClientMockRecorder struct {
	mock *MockComplianceClient
}

// NewMockComplianceClient creates a new mock instance.
func NewMockComplianceClient(ctrl *gomock.Controller) *MockComplianceClient {
	mock := &MockComplianceClient{ctrl: ctrl}
	mock.recorder = &MockComplianceClientMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockComplianceClient) EXPECT() *MockComplianceClientMockRecorder {
	return m.recorder
}

// UpdateSanctionFinanciersList mocks base method.
func (m *MockComplianceClient) UpdateSanctionFinanciersList(ctx context.Context, in *emptypb.Empty, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "UpdateSanctionFinanciersList", varargs...)
	ret0, _ := ret[0].(*emptypb.Empty)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpdateSanctionFinanciersList indicates an expected call of UpdateSanctionFinanciersList.
func (mr *MockComplianceClientMockRecorder) UpdateSanctionFinanciersList(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateSanctionFinanciersList", reflect.TypeOf((*MockComplianceClient)(nil).UpdateSanctionFinanciersList), varargs...)
}

// UpdateSanctionInvolvedList mocks base method.
func (m *MockComplianceClient) UpdateSanctionInvolvedList(ctx context.Context, in *emptypb.Empty, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "UpdateSanctionInvolvedList", varargs...)
	ret0, _ := ret[0].(*emptypb.Empty)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpdateSanctionInvolvedList indicates an expected call of UpdateSanctionInvolvedList.
func (mr *MockComplianceClientMockRecorder) UpdateSanctionInvolvedList(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateSanctionInvolvedList", reflect.TypeOf((*MockComplianceClient)(nil).UpdateSanctionInvolvedList), varargs...)
}

// UpdateSanctionUNSCList mocks base method.
func (m *MockComplianceClient) UpdateSanctionUNSCList(ctx context.Context, in *emptypb.Empty, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "UpdateSanctionUNSCList", varargs...)
	ret0, _ := ret[0].(*emptypb.Empty)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpdateSanctionUNSCList indicates an expected call of UpdateSanctionUNSCList.
func (mr *MockComplianceClientMockRecorder) UpdateSanctionUNSCList(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateSanctionUNSCList", reflect.TypeOf((*MockComplianceClient)(nil).UpdateSanctionUNSCList), varargs...)
}

// UpdateSanctionWMDList mocks base method.
func (m *MockComplianceClient) UpdateSanctionWMDList(ctx context.Context, in *emptypb.Empty, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "UpdateSanctionWMDList", varargs...)
	ret0, _ := ret[0].(*emptypb.Empty)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpdateSanctionWMDList indicates an expected call of UpdateSanctionWMDList.
func (mr *MockComplianceClientMockRecorder) UpdateSanctionWMDList(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateSanctionWMDList", reflect.TypeOf((*MockComplianceClient)(nil).UpdateSanctionWMDList), varargs...)
}

// MockComplianceServer is a mock of ComplianceServer interface.
type MockComplianceServer struct {
	ctrl     *gomock.Controller
	recorder *MockComplianceServerMockRecorder
}

// MockComplianceServerMockRecorder is the mock recorder for MockComplianceServer.
type MockComplianceServerMockRecorder struct {
	mock *MockComplianceServer
}

// NewMockComplianceServer creates a new mock instance.
func NewMockComplianceServer(ctrl *gomock.Controller) *MockComplianceServer {
	mock := &MockComplianceServer{ctrl: ctrl}
	mock.recorder = &MockComplianceServerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockComplianceServer) EXPECT() *MockComplianceServerMockRecorder {
	return m.recorder
}

// UpdateSanctionFinanciersList mocks base method.
func (m *MockComplianceServer) UpdateSanctionFinanciersList(arg0 context.Context, arg1 *emptypb.Empty) (*emptypb.Empty, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateSanctionFinanciersList", arg0, arg1)
	ret0, _ := ret[0].(*emptypb.Empty)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpdateSanctionFinanciersList indicates an expected call of UpdateSanctionFinanciersList.
func (mr *MockComplianceServerMockRecorder) UpdateSanctionFinanciersList(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateSanctionFinanciersList", reflect.TypeOf((*MockComplianceServer)(nil).UpdateSanctionFinanciersList), arg0, arg1)
}

// UpdateSanctionInvolvedList mocks base method.
func (m *MockComplianceServer) UpdateSanctionInvolvedList(arg0 context.Context, arg1 *emptypb.Empty) (*emptypb.Empty, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateSanctionInvolvedList", arg0, arg1)
	ret0, _ := ret[0].(*emptypb.Empty)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpdateSanctionInvolvedList indicates an expected call of UpdateSanctionInvolvedList.
func (mr *MockComplianceServerMockRecorder) UpdateSanctionInvolvedList(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateSanctionInvolvedList", reflect.TypeOf((*MockComplianceServer)(nil).UpdateSanctionInvolvedList), arg0, arg1)
}

// UpdateSanctionUNSCList mocks base method.
func (m *MockComplianceServer) UpdateSanctionUNSCList(arg0 context.Context, arg1 *emptypb.Empty) (*emptypb.Empty, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateSanctionUNSCList", arg0, arg1)
	ret0, _ := ret[0].(*emptypb.Empty)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpdateSanctionUNSCList indicates an expected call of UpdateSanctionUNSCList.
func (mr *MockComplianceServerMockRecorder) UpdateSanctionUNSCList(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateSanctionUNSCList", reflect.TypeOf((*MockComplianceServer)(nil).UpdateSanctionUNSCList), arg0, arg1)
}

// UpdateSanctionWMDList mocks base method.
func (m *MockComplianceServer) UpdateSanctionWMDList(arg0 context.Context, arg1 *emptypb.Empty) (*emptypb.Empty, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateSanctionWMDList", arg0, arg1)
	ret0, _ := ret[0].(*emptypb.Empty)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpdateSanctionWMDList indicates an expected call of UpdateSanctionWMDList.
func (mr *MockComplianceServerMockRecorder) UpdateSanctionWMDList(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateSanctionWMDList", reflect.TypeOf((*MockComplianceServer)(nil).UpdateSanctionWMDList), arg0, arg1)
}

// mustEmbedUnimplementedComplianceServer mocks base method.
func (m *MockComplianceServer) mustEmbedUnimplementedComplianceServer() {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "mustEmbedUnimplementedComplianceServer")
}

// mustEmbedUnimplementedComplianceServer indicates an expected call of mustEmbedUnimplementedComplianceServer.
func (mr *MockComplianceServerMockRecorder) mustEmbedUnimplementedComplianceServer() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "mustEmbedUnimplementedComplianceServer", reflect.TypeOf((*MockComplianceServer)(nil).mustEmbedUnimplementedComplianceServer))
}

// MockUnsafeComplianceServer is a mock of UnsafeComplianceServer interface.
type MockUnsafeComplianceServer struct {
	ctrl     *gomock.Controller
	recorder *MockUnsafeComplianceServerMockRecorder
}

// MockUnsafeComplianceServerMockRecorder is the mock recorder for MockUnsafeComplianceServer.
type MockUnsafeComplianceServerMockRecorder struct {
	mock *MockUnsafeComplianceServer
}

// NewMockUnsafeComplianceServer creates a new mock instance.
func NewMockUnsafeComplianceServer(ctrl *gomock.Controller) *MockUnsafeComplianceServer {
	mock := &MockUnsafeComplianceServer{ctrl: ctrl}
	mock.recorder = &MockUnsafeComplianceServerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockUnsafeComplianceServer) EXPECT() *MockUnsafeComplianceServerMockRecorder {
	return m.recorder
}

// mustEmbedUnimplementedComplianceServer mocks base method.
func (m *MockUnsafeComplianceServer) mustEmbedUnimplementedComplianceServer() {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "mustEmbedUnimplementedComplianceServer")
}

// mustEmbedUnimplementedComplianceServer indicates an expected call of mustEmbedUnimplementedComplianceServer.
func (mr *MockUnsafeComplianceServerMockRecorder) mustEmbedUnimplementedComplianceServer() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "mustEmbedUnimplementedComplianceServer", reflect.TypeOf((*MockUnsafeComplianceServer)(nil).mustEmbedUnimplementedComplianceServer))
}
