// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.5
// 	protoc        v5.29.3
// source: inner/processing/grpc/jusan_payout.proto

package grpc

import (
	_ "git.local/sensitive/mvp/pb"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	descriptorpb "google.golang.org/protobuf/types/descriptorpb"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type JusanResponseCodePayout int32

const (
	JusanResponseCodePayout_PayoutUnhandledError            JusanResponseCodePayout = 0
	JusanResponseCodePayout_PayoutValidationError           JusanResponseCodePayout = 1
	JusanResponseCodePayout_PayoutIncorrectLoginPassword    JusanResponseCodePayout = 2
	JusanResponseCodePayout_PayoutBlocked                   JusanResponseCodePayout = 3
	JusanResponseCodePayout_PayoutRetry                     JusanResponseCodePayout = 4
	JusanResponseCodePayout_PayoutEmptyPartnerCorrelationId JusanResponseCodePayout = 5
	JusanResponseCodePayout_PayoutEmptyPartnerLogin         JusanResponseCodePayout = 6
	JusanResponseCodePayout_PayoutEmptyAuthorization        JusanResponseCodePayout = 7
	JusanResponseCodePayout_PayoutEmptyData                 JusanResponseCodePayout = 8
	JusanResponseCodePayout_PayoutEmptyAuth                 JusanResponseCodePayout = 9
	JusanResponseCodePayout_PayoutIncorrectAuth             JusanResponseCodePayout = 10
	JusanResponseCodePayout_PayoutIncorrectCurrency         JusanResponseCodePayout = 11
	JusanResponseCodePayout_PayoutEDNotFound                JusanResponseCodePayout = 12
	JusanResponseCodePayout_PayoutEDNotEnabled              JusanResponseCodePayout = 13
	JusanResponseCodePayout_PayoutInsufficientFunds         JusanResponseCodePayout = 14
	JusanResponseCodePayout_PayoutIncorrectCard             JusanResponseCodePayout = 15
	JusanResponseCodePayout_PayoutAccountBlocked            JusanResponseCodePayout = 16
	JusanResponseCodePayout_PayoutIncorrectSession          JusanResponseCodePayout = 17
	JusanResponseCodePayout_PayoutPaymentNotFound           JusanResponseCodePayout = 18
	JusanResponseCodePayout_PayoutIncorrectAmount           JusanResponseCodePayout = 19
	JusanResponseCodePayout_PayoutIssuerDeclined            JusanResponseCodePayout = 20
	JusanResponseCodePayout_PayoutCardDisabled              JusanResponseCodePayout = 21
	JusanResponseCodePayout_PayoutTransactionNotActive      JusanResponseCodePayout = 22
	JusanResponseCodePayout_PayoutCardNumIncorrect          JusanResponseCodePayout = 23
	JusanResponseCodePayout_PayoutClientDisabled            JusanResponseCodePayout = 24
	JusanResponseCodePayout_PayoutSuspiciousClient          JusanResponseCodePayout = 25
	JusanResponseCodePayout_PayoutRetryTransaction          JusanResponseCodePayout = 26
	JusanResponseCodePayout_PayoutCardExpired               JusanResponseCodePayout = 27
	JusanResponseCodePayout_PayoutCardLimited               JusanResponseCodePayout = 28
	JusanResponseCodePayout_PayoutStolenCard                JusanResponseCodePayout = 29
	JusanResponseCodePayout_PayoutExpCard                   JusanResponseCodePayout = 30
	JusanResponseCodePayout_PayoutForbiddenForClient        JusanResponseCodePayout = 31
	JusanResponseCodePayout_PayoutForbiddenTerminal         JusanResponseCodePayout = 32
	JusanResponseCodePayout_PayoutClientSuspicious          JusanResponseCodePayout = 33
	JusanResponseCodePayout_PayoutLimitExceeded             JusanResponseCodePayout = 34
	JusanResponseCodePayout_PayoutLimitedCard               JusanResponseCodePayout = 35
	JusanResponseCodePayout_PayoutSecurityBreached          JusanResponseCodePayout = 36
	JusanResponseCodePayout_PayoutUnavailableIssuer         JusanResponseCodePayout = 37
	JusanResponseCodePayout_PayoutTransactionNotFinished    JusanResponseCodePayout = 38
	JusanResponseCodePayout_PayoutSystemMalfuction          JusanResponseCodePayout = 39
)

// Enum value maps for JusanResponseCodePayout.
var (
	JusanResponseCodePayout_name = map[int32]string{
		0:  "PayoutUnhandledError",
		1:  "PayoutValidationError",
		2:  "PayoutIncorrectLoginPassword",
		3:  "PayoutBlocked",
		4:  "PayoutRetry",
		5:  "PayoutEmptyPartnerCorrelationId",
		6:  "PayoutEmptyPartnerLogin",
		7:  "PayoutEmptyAuthorization",
		8:  "PayoutEmptyData",
		9:  "PayoutEmptyAuth",
		10: "PayoutIncorrectAuth",
		11: "PayoutIncorrectCurrency",
		12: "PayoutEDNotFound",
		13: "PayoutEDNotEnabled",
		14: "PayoutInsufficientFunds",
		15: "PayoutIncorrectCard",
		16: "PayoutAccountBlocked",
		17: "PayoutIncorrectSession",
		18: "PayoutPaymentNotFound",
		19: "PayoutIncorrectAmount",
		20: "PayoutIssuerDeclined",
		21: "PayoutCardDisabled",
		22: "PayoutTransactionNotActive",
		23: "PayoutCardNumIncorrect",
		24: "PayoutClientDisabled",
		25: "PayoutSuspiciousClient",
		26: "PayoutRetryTransaction",
		27: "PayoutCardExpired",
		28: "PayoutCardLimited",
		29: "PayoutStolenCard",
		30: "PayoutExpCard",
		31: "PayoutForbiddenForClient",
		32: "PayoutForbiddenTerminal",
		33: "PayoutClientSuspicious",
		34: "PayoutLimitExceeded",
		35: "PayoutLimitedCard",
		36: "PayoutSecurityBreached",
		37: "PayoutUnavailableIssuer",
		38: "PayoutTransactionNotFinished",
		39: "PayoutSystemMalfuction",
	}
	JusanResponseCodePayout_value = map[string]int32{
		"PayoutUnhandledError":            0,
		"PayoutValidationError":           1,
		"PayoutIncorrectLoginPassword":    2,
		"PayoutBlocked":                   3,
		"PayoutRetry":                     4,
		"PayoutEmptyPartnerCorrelationId": 5,
		"PayoutEmptyPartnerLogin":         6,
		"PayoutEmptyAuthorization":        7,
		"PayoutEmptyData":                 8,
		"PayoutEmptyAuth":                 9,
		"PayoutIncorrectAuth":             10,
		"PayoutIncorrectCurrency":         11,
		"PayoutEDNotFound":                12,
		"PayoutEDNotEnabled":              13,
		"PayoutInsufficientFunds":         14,
		"PayoutIncorrectCard":             15,
		"PayoutAccountBlocked":            16,
		"PayoutIncorrectSession":          17,
		"PayoutPaymentNotFound":           18,
		"PayoutIncorrectAmount":           19,
		"PayoutIssuerDeclined":            20,
		"PayoutCardDisabled":              21,
		"PayoutTransactionNotActive":      22,
		"PayoutCardNumIncorrect":          23,
		"PayoutClientDisabled":            24,
		"PayoutSuspiciousClient":          25,
		"PayoutRetryTransaction":          26,
		"PayoutCardExpired":               27,
		"PayoutCardLimited":               28,
		"PayoutStolenCard":                29,
		"PayoutExpCard":                   30,
		"PayoutForbiddenForClient":        31,
		"PayoutForbiddenTerminal":         32,
		"PayoutClientSuspicious":          33,
		"PayoutLimitExceeded":             34,
		"PayoutLimitedCard":               35,
		"PayoutSecurityBreached":          36,
		"PayoutUnavailableIssuer":         37,
		"PayoutTransactionNotFinished":    38,
		"PayoutSystemMalfuction":          39,
	}
)

func (x JusanResponseCodePayout) Enum() *JusanResponseCodePayout {
	p := new(JusanResponseCodePayout)
	*p = x
	return p
}

func (x JusanResponseCodePayout) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (JusanResponseCodePayout) Descriptor() protoreflect.EnumDescriptor {
	return file_inner_processing_grpc_jusan_payout_proto_enumTypes[0].Descriptor()
}

func (JusanResponseCodePayout) Type() protoreflect.EnumType {
	return &file_inner_processing_grpc_jusan_payout_proto_enumTypes[0]
}

func (x JusanResponseCodePayout) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use JusanResponseCodePayout.Descriptor instead.
func (JusanResponseCodePayout) EnumDescriptor() ([]byte, []int) {
	return file_inner_processing_grpc_jusan_payout_proto_rawDescGZIP(), []int{0}
}

type JusanResponseCodePayoutRef struct {
	state             protoimpl.MessageState `protogen:"open.v1"`
	Code              *string                `protobuf:"bytes,1,opt,name=code" json:"code,omitempty"`
	Message           *string                `protobuf:"bytes,2,opt,name=message" json:"message,omitempty"`
	TransactionStatus *EnumTransactionStatus `protobuf:"varint,3,opt,name=transaction_status,json=transactionStatus,enum=processing.transaction.transaction_status.EnumTransactionStatus" json:"transaction_status,omitempty"`
	IntegrationError  *IntegrationError      `protobuf:"varint,4,opt,name=integration_error,json=integrationError,enum=processing.integration.integration.IntegrationError" json:"integration_error,omitempty"`
	unknownFields     protoimpl.UnknownFields
	sizeCache         protoimpl.SizeCache
}

func (x *JusanResponseCodePayoutRef) Reset() {
	*x = JusanResponseCodePayoutRef{}
	mi := &file_inner_processing_grpc_jusan_payout_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *JusanResponseCodePayoutRef) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*JusanResponseCodePayoutRef) ProtoMessage() {}

func (x *JusanResponseCodePayoutRef) ProtoReflect() protoreflect.Message {
	mi := &file_inner_processing_grpc_jusan_payout_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use JusanResponseCodePayoutRef.ProtoReflect.Descriptor instead.
func (*JusanResponseCodePayoutRef) Descriptor() ([]byte, []int) {
	return file_inner_processing_grpc_jusan_payout_proto_rawDescGZIP(), []int{0}
}

func (x *JusanResponseCodePayoutRef) GetCode() string {
	if x != nil && x.Code != nil {
		return *x.Code
	}
	return ""
}

func (x *JusanResponseCodePayoutRef) GetMessage() string {
	if x != nil && x.Message != nil {
		return *x.Message
	}
	return ""
}

func (x *JusanResponseCodePayoutRef) GetTransactionStatus() EnumTransactionStatus {
	if x != nil && x.TransactionStatus != nil {
		return *x.TransactionStatus
	}
	return EnumTransactionStatus_Unknown
}

func (x *JusanResponseCodePayoutRef) GetIntegrationError() IntegrationError {
	if x != nil && x.IntegrationError != nil {
		return *x.IntegrationError
	}
	return IntegrationError_None
}

var file_inner_processing_grpc_jusan_payout_proto_extTypes = []protoimpl.ExtensionInfo{
	{
		ExtendedType:  (*descriptorpb.EnumValueOptions)(nil),
		ExtensionType: (*JusanResponseCodePayoutRef)(nil),
		Field:         100117,
		Name:          "processing.jusan_payout.jusan_payout.jusan_response_code_payout_value",
		Tag:           "bytes,100117,opt,name=jusan_response_code_payout_value",
		Filename:      "inner/processing/grpc/jusan_payout.proto",
	},
	{
		ExtendedType:  (*descriptorpb.EnumOptions)(nil),
		ExtensionType: (*JusanResponseCodePayoutRef)(nil),
		Field:         100118,
		Name:          "processing.jusan_payout.jusan_payout.default_jusan_response_code_payout_value",
		Tag:           "bytes,100118,opt,name=default_jusan_response_code_payout_value",
		Filename:      "inner/processing/grpc/jusan_payout.proto",
	},
}

// Extension fields to descriptorpb.EnumValueOptions.
var (
	// optional processing.jusan_payout.jusan_payout.JusanResponseCodePayoutRef jusan_response_code_payout_value = 100117;
	E_JusanResponseCodePayoutValue = &file_inner_processing_grpc_jusan_payout_proto_extTypes[0]
)

// Extension fields to descriptorpb.EnumOptions.
var (
	// optional processing.jusan_payout.jusan_payout.JusanResponseCodePayoutRef default_jusan_response_code_payout_value = 100118;
	E_DefaultJusanResponseCodePayoutValue = &file_inner_processing_grpc_jusan_payout_proto_extTypes[1]
)

var File_inner_processing_grpc_jusan_payout_proto protoreflect.FileDescriptor

var file_inner_processing_grpc_jusan_payout_proto_rawDesc = string([]byte{
	0x0a, 0x28, 0x69, 0x6e, 0x6e, 0x65, 0x72, 0x2f, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69,
	0x6e, 0x67, 0x2f, 0x67, 0x72, 0x70, 0x63, 0x2f, 0x6a, 0x75, 0x73, 0x61, 0x6e, 0x5f, 0x70, 0x61,
	0x79, 0x6f, 0x75, 0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x24, 0x70, 0x72, 0x6f, 0x63,
	0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x2e, 0x6a, 0x75, 0x73, 0x61, 0x6e, 0x5f, 0x70, 0x61, 0x79,
	0x6f, 0x75, 0x74, 0x2e, 0x6a, 0x75, 0x73, 0x61, 0x6e, 0x5f, 0x70, 0x61, 0x79, 0x6f, 0x75, 0x74,
	0x1a, 0x2e, 0x69, 0x6e, 0x6e, 0x65, 0x72, 0x2f, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69,
	0x6e, 0x67, 0x2f, 0x67, 0x72, 0x70, 0x63, 0x2f, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74,
	0x69, 0x6f, 0x6e, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x1a, 0x27, 0x69, 0x6e, 0x6e, 0x65, 0x72, 0x2f, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69,
	0x6e, 0x67, 0x2f, 0x67, 0x72, 0x70, 0x63, 0x2f, 0x69, 0x6e, 0x74, 0x65, 0x67, 0x72, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x14, 0x6d, 0x76, 0x70, 0x2f, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x2f, 0x72, 0x65, 0x66, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a,
	0x14, 0x6d, 0x76, 0x70, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f, 0x65, 0x6e, 0x75, 0x6d, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x20, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x6f,
	0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x9e, 0x02, 0x0a, 0x1a, 0x4a, 0x75, 0x73, 0x61,
	0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x43, 0x6f, 0x64, 0x65, 0x50, 0x61, 0x79,
	0x6f, 0x75, 0x74, 0x52, 0x65, 0x66, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x6d, 0x65,
	0x73, 0x73, 0x61, 0x67, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6d, 0x65, 0x73,
	0x73, 0x61, 0x67, 0x65, 0x12, 0x6f, 0x0a, 0x12, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74,
	0x69, 0x6f, 0x6e, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0e,
	0x32, 0x40, 0x2e, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x2e, 0x74, 0x72,
	0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61,
	0x63, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x2e, 0x45, 0x6e, 0x75,
	0x6d, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x52, 0x11, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x53,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x61, 0x0a, 0x11, 0x69, 0x6e, 0x74, 0x65, 0x67, 0x72, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0e,
	0x32, 0x34, 0x2e, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x2e, 0x69, 0x6e,
	0x74, 0x65, 0x67, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x69, 0x6e, 0x74, 0x65, 0x67, 0x72,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x49, 0x6e, 0x74, 0x65, 0x67, 0x72, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x52, 0x10, 0x69, 0x6e, 0x74, 0x65, 0x67, 0x72, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x2a, 0x92, 0x22, 0x0a, 0x17, 0x4a, 0x75, 0x73,
	0x61, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x43, 0x6f, 0x64, 0x65, 0x50, 0x61,
	0x79, 0x6f, 0x75, 0x74, 0x12, 0x59, 0x0a, 0x14, 0x50, 0x61, 0x79, 0x6f, 0x75, 0x74, 0x55, 0x6e,
	0x68, 0x61, 0x6e, 0x64, 0x6c, 0x65, 0x64, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x10, 0x00, 0x1a, 0x3f,
	0xaa, 0xf1, 0x30, 0x33, 0x0a, 0x02, 0x2d, 0x31, 0x12, 0x29, 0xd0, 0x9d, 0xd0, 0xb5, 0xd0, 0xbe,
	0xd0, 0xb1, 0xd1, 0x80, 0xd0, 0xb0, 0xd0, 0xb1, 0xd0, 0xbe, 0xd1, 0x82, 0xd0, 0xb0, 0xd0, 0xbd,
	0xd0, 0xbd, 0xd0, 0xb0, 0xd1, 0x8f, 0x20, 0xd0, 0xbe, 0xd1, 0x88, 0xd0, 0xb8, 0xd0, 0xb1, 0xd0,
	0xba, 0xd0, 0xb0, 0x18, 0x0b, 0x20, 0x6a, 0xca, 0xa1, 0xec, 0x8e, 0x02, 0x02, 0x2d, 0x31, 0x12,
	0x50, 0x0a, 0x15, 0x50, 0x61, 0x79, 0x6f, 0x75, 0x74, 0x56, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x10, 0x01, 0x1a, 0x35, 0xaa, 0xf1, 0x30, 0x29,
	0x0a, 0x02, 0x2d, 0x32, 0x12, 0x1f, 0xd0, 0x9e, 0xd1, 0x88, 0xd0, 0xb8, 0xd0, 0xb1, 0xd0, 0xba,
	0xd0, 0xb0, 0x20, 0xd0, 0xb2, 0xd0, 0xb0, 0xd0, 0xbb, 0xd0, 0xb8, 0xd0, 0xb4, 0xd0, 0xb0, 0xd1,
	0x86, 0xd0, 0xb8, 0xd0, 0xb8, 0x18, 0x05, 0x20, 0x6a, 0xca, 0xa1, 0xec, 0x8e, 0x02, 0x02, 0x2d,
	0x32, 0x12, 0x6f, 0x0a, 0x1c, 0x50, 0x61, 0x79, 0x6f, 0x75, 0x74, 0x49, 0x6e, 0x63, 0x6f, 0x72,
	0x72, 0x65, 0x63, 0x74, 0x4c, 0x6f, 0x67, 0x69, 0x6e, 0x50, 0x61, 0x73, 0x73, 0x77, 0x6f, 0x72,
	0x64, 0x10, 0x02, 0x1a, 0x4d, 0xaa, 0xf1, 0x30, 0x41, 0x0a, 0x02, 0x2d, 0x33, 0x12, 0x37, 0xd0,
	0x9d, 0xd0, 0xb5, 0xd0, 0xba, 0xd0, 0xbe, 0xd1, 0x80, 0xd1, 0x80, 0xd0, 0xb5, 0xd0, 0xba, 0xd1,
	0x82, 0xd0, 0xbd, 0xd1, 0x8b, 0xd0, 0xb9, 0x20, 0xd0, 0xbb, 0xd0, 0xbe, 0xd0, 0xb3, 0xd0, 0xb8,
	0xd0, 0xbd, 0x20, 0xd0, 0xb8, 0xd0, 0xbb, 0xd0, 0xb8, 0x20, 0xd0, 0xbf, 0xd0, 0xb0, 0xd1, 0x80,
	0xd0, 0xbe, 0xd0, 0xbb, 0xd1, 0x8c, 0x18, 0x05, 0x20, 0x6a, 0xca, 0xa1, 0xec, 0x8e, 0x02, 0x02,
	0x2d, 0x33, 0x12, 0x5f, 0x0a, 0x0d, 0x50, 0x61, 0x79, 0x6f, 0x75, 0x74, 0x42, 0x6c, 0x6f, 0x63,
	0x6b, 0x65, 0x64, 0x10, 0x03, 0x1a, 0x4c, 0xaa, 0xf1, 0x30, 0x40, 0x0a, 0x02, 0x2d, 0x34, 0x12,
	0x36, 0xd0, 0xa3, 0xd1, 0x87, 0xd0, 0xb5, 0xd1, 0x82, 0xd0, 0xbd, 0xd0, 0xb0, 0xd1, 0x8f, 0x20,
	0xd0, 0xb7, 0xd0, 0xb0, 0xd0, 0xbf, 0xd0, 0xb8, 0xd1, 0x81, 0xd1, 0x8c, 0x20, 0xd0, 0xb7, 0xd0,
	0xb0, 0xd0, 0xb1, 0xd0, 0xbb, 0xd0, 0xbe, 0xd0, 0xba, 0xd0, 0xb8, 0xd1, 0x80, 0xd0, 0xbe, 0xd0,
	0xb2, 0xd0, 0xb0, 0xd0, 0xbd, 0xd0, 0xb0, 0x18, 0x05, 0x20, 0x6a, 0xca, 0xa1, 0xec, 0x8e, 0x02,
	0x02, 0x2d, 0x34, 0x12, 0x46, 0x0a, 0x0b, 0x50, 0x61, 0x79, 0x6f, 0x75, 0x74, 0x52, 0x65, 0x74,
	0x72, 0x79, 0x10, 0x04, 0x1a, 0x35, 0xaa, 0xf1, 0x30, 0x29, 0x0a, 0x02, 0x2d, 0x35, 0x12, 0x1f,
	0xd0, 0x9f, 0xd0, 0xbe, 0xd0, 0xb2, 0xd1, 0x82, 0xd0, 0xbe, 0xd1, 0x80, 0xd0, 0xb8, 0xd1, 0x82,
	0xd0, 0xb5, 0x20, 0xd0, 0xb7, 0xd0, 0xb0, 0xd0, 0xbf, 0xd1, 0x80, 0xd0, 0xbe, 0xd1, 0x81, 0x18,
	0x08, 0x20, 0x6a, 0xca, 0xa1, 0xec, 0x8e, 0x02, 0x02, 0x2d, 0x35, 0x12, 0x73, 0x0a, 0x1f, 0x50,
	0x61, 0x79, 0x6f, 0x75, 0x74, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x50, 0x61, 0x72, 0x74, 0x6e, 0x65,
	0x72, 0x43, 0x6f, 0x72, 0x72, 0x65, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x10, 0x05,
	0x1a, 0x4e, 0xaa, 0xf1, 0x30, 0x40, 0x0a, 0x04, 0x2d, 0x31, 0x30, 0x30, 0x12, 0x34, 0xd0, 0x9f,
	0xd1, 0x83, 0xd1, 0x81, 0xd1, 0x82, 0xd0, 0xbe, 0xd0, 0xb9, 0x20, 0xd0, 0xb7, 0xd0, 0xb0, 0xd0,
	0xb3, 0xd0, 0xbe, 0xd0, 0xbb, 0xd0, 0xbe, 0xd0, 0xb2, 0xd0, 0xbe, 0xd0, 0xba, 0x20, 0x50, 0x61,
	0x72, 0x74, 0x6e, 0x65, 0x72, 0x43, 0x6f, 0x72, 0x72, 0x65, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x49, 0x64, 0x18, 0x05, 0x20, 0x6a, 0xca, 0xa1, 0xec, 0x8e, 0x02, 0x04, 0x2d, 0x31, 0x30, 0x30,
	0x12, 0x63, 0x0a, 0x17, 0x50, 0x61, 0x79, 0x6f, 0x75, 0x74, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x50,
	0x61, 0x72, 0x74, 0x6e, 0x65, 0x72, 0x4c, 0x6f, 0x67, 0x69, 0x6e, 0x10, 0x06, 0x1a, 0x46, 0xaa,
	0xf1, 0x30, 0x38, 0x0a, 0x04, 0x2d, 0x31, 0x30, 0x31, 0x12, 0x2c, 0xd0, 0x9f, 0xd1, 0x83, 0xd1,
	0x81, 0xd1, 0x82, 0xd0, 0xbe, 0xd0, 0xb9, 0x20, 0xd0, 0xb7, 0xd0, 0xb0, 0xd0, 0xb3, 0xd0, 0xbe,
	0xd0, 0xbb, 0xd0, 0xbe, 0xd0, 0xb2, 0xd0, 0xbe, 0xd0, 0xba, 0x20, 0x50, 0x61, 0x72, 0x74, 0x6e,
	0x65, 0x72, 0x4c, 0x6f, 0x67, 0x69, 0x6e, 0x18, 0x05, 0x20, 0x6a, 0xca, 0xa1, 0xec, 0x8e, 0x02,
	0x04, 0x2d, 0x31, 0x30, 0x31, 0x12, 0x81, 0x01, 0x0a, 0x18, 0x50, 0x61, 0x79, 0x6f, 0x75, 0x74,
	0x45, 0x6d, 0x70, 0x74, 0x79, 0x41, 0x75, 0x74, 0x68, 0x6f, 0x72, 0x69, 0x7a, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x10, 0x07, 0x1a, 0x63, 0xaa, 0xf1, 0x30, 0x55, 0x0a, 0x04, 0x2d, 0x31, 0x30, 0x32,
	0x12, 0x49, 0xd0, 0x9d, 0xd0, 0xb5, 0xd0, 0xba, 0xd0, 0xbe, 0xd1, 0x80, 0xd1, 0x80, 0xd0, 0xb5,
	0xd0, 0xba, 0xd1, 0x82, 0xd0, 0xbd, 0xd0, 0xbe, 0xd0, 0xb5, 0x20, 0xd0, 0xb7, 0xd0, 0xbd, 0xd0,
	0xb0, 0xd1, 0x87, 0xd0, 0xb5, 0xd0, 0xbd, 0xd0, 0xb8, 0xd0, 0xb5, 0x20, 0xd0, 0xb7, 0xd0, 0xb0,
	0xd0, 0xb3, 0xd0, 0xbe, 0xd0, 0xbb, 0xd0, 0xbe, 0xd0, 0xb2, 0xd0, 0xba, 0xd0, 0xb0, 0x20, 0x41,
	0x75, 0x74, 0x6f, 0x72, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x05, 0x20, 0x6a, 0xca,
	0xa1, 0xec, 0x8e, 0x02, 0x04, 0x2d, 0x31, 0x30, 0x32, 0x12, 0x63, 0x0a, 0x0f, 0x50, 0x61, 0x79,
	0x6f, 0x75, 0x74, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x44, 0x61, 0x74, 0x61, 0x10, 0x08, 0x1a, 0x4e,
	0xaa, 0xf1, 0x30, 0x40, 0x0a, 0x04, 0x2d, 0x31, 0x30, 0x33, 0x12, 0x34, 0xd0, 0x9f, 0xd1, 0x83,
	0xd1, 0x81, 0xd1, 0x82, 0xd1, 0x8b, 0xd0, 0xb5, 0x20, 0xd0, 0xb7, 0xd0, 0xb0, 0xd1, 0x88, 0xd0,
	0xb8, 0xd1, 0x84, 0xd1, 0x80, 0xd0, 0xbe, 0xd0, 0xb2, 0xd0, 0xb0, 0xd0, 0xbd, 0xd0, 0xbd, 0xd1,
	0x8b, 0xd0, 0xb5, 0x20, 0xd0, 0xb4, 0xd0, 0xb0, 0xd0, 0xbd, 0xd0, 0xbd, 0xd1, 0x8b, 0xd0, 0xb5,
	0x18, 0x05, 0x20, 0x6a, 0xca, 0xa1, 0xec, 0x8e, 0x02, 0x04, 0x2d, 0x31, 0x30, 0x33, 0x12, 0x5c,
	0x0a, 0x0f, 0x50, 0x61, 0x79, 0x6f, 0x75, 0x74, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x41, 0x75, 0x74,
	0x68, 0x10, 0x09, 0x1a, 0x47, 0xaa, 0xf1, 0x30, 0x39, 0x0a, 0x04, 0x2d, 0x31, 0x30, 0x34, 0x12,
	0x2d, 0xd0, 0x9f, 0xd1, 0x83, 0xd1, 0x81, 0xd1, 0x82, 0xd0, 0xbe, 0xd0, 0xb9, 0x20, 0xd0, 0xb7,
	0xd0, 0xb0, 0xd0, 0xb3, 0xd0, 0xbe, 0xd0, 0xbb, 0xd0, 0xbe, 0xd0, 0xb2, 0xd0, 0xbe, 0xd0, 0xba,
	0x20, 0x41, 0x75, 0x74, 0x68, 0x6f, 0x72, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x05,
	0x20, 0x6a, 0xca, 0xa1, 0xec, 0x8e, 0x02, 0x04, 0x2d, 0x31, 0x30, 0x34, 0x12, 0x6c, 0x0a, 0x13,
	0x50, 0x61, 0x79, 0x6f, 0x75, 0x74, 0x49, 0x6e, 0x63, 0x6f, 0x72, 0x72, 0x65, 0x63, 0x74, 0x41,
	0x75, 0x74, 0x68, 0x10, 0x0a, 0x1a, 0x53, 0xaa, 0xf1, 0x30, 0x45, 0x0a, 0x04, 0x2d, 0x31, 0x30,
	0x35, 0x12, 0x39, 0xd0, 0x9d, 0xd0, 0xb5, 0xd0, 0xba, 0xd0, 0xbe, 0xd1, 0x80, 0xd1, 0x80, 0xd0,
	0xb5, 0xd0, 0xba, 0xd1, 0x82, 0xd0, 0xbd, 0xd1, 0x8b, 0xd0, 0xb9, 0x20, 0xd0, 0xb7, 0xd0, 0xb0,
	0xd0, 0xb3, 0xd0, 0xbe, 0xd0, 0xbb, 0xd0, 0xbe, 0xd0, 0xb2, 0xd0, 0xbe, 0xd0, 0xba, 0x20, 0x41,
	0x75, 0x74, 0x68, 0x6f, 0x72, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x05, 0x20, 0x6a,
	0xca, 0xa1, 0xec, 0x8e, 0x02, 0x04, 0x2d, 0x31, 0x30, 0x35, 0x12, 0x69, 0x0a, 0x17, 0x50, 0x61,
	0x79, 0x6f, 0x75, 0x74, 0x49, 0x6e, 0x63, 0x6f, 0x72, 0x72, 0x65, 0x63, 0x74, 0x43, 0x75, 0x72,
	0x72, 0x65, 0x6e, 0x63, 0x79, 0x10, 0x0b, 0x1a, 0x4c, 0xaa, 0xf1, 0x30, 0x3e, 0x0a, 0x04, 0x2d,
	0x31, 0x30, 0x36, 0x12, 0x32, 0xd0, 0x9d, 0xd0, 0xb5, 0xd0, 0xba, 0xd0, 0xbe, 0xd1, 0x80, 0xd1,
	0x80, 0xd0, 0xb5, 0xd0, 0xba, 0xd1, 0x82, 0xd0, 0xbd, 0xd0, 0xbe, 0x20, 0xd1, 0x83, 0xd0, 0xba,
	0xd0, 0xb0, 0xd0, 0xb7, 0xd0, 0xb0, 0xd0, 0xbd, 0xd0, 0xb0, 0x20, 0xd0, 0xb2, 0xd0, 0xb0, 0xd0,
	0xbb, 0xd1, 0x8e, 0xd1, 0x82, 0xd0, 0xb0, 0x18, 0x05, 0x20, 0x6a, 0xca, 0xa1, 0xec, 0x8e, 0x02,
	0x04, 0x2d, 0x31, 0x30, 0x36, 0x12, 0x5b, 0x0a, 0x10, 0x50, 0x61, 0x79, 0x6f, 0x75, 0x74, 0x45,
	0x44, 0x4e, 0x6f, 0x74, 0x46, 0x6f, 0x75, 0x6e, 0x64, 0x10, 0x0c, 0x1a, 0x45, 0xaa, 0xf1, 0x30,
	0x37, 0x0a, 0x04, 0x2d, 0x31, 0x30, 0x37, 0x12, 0x2b, 0xd0, 0xa2, 0xd1, 0x80, 0xd0, 0xb0, 0xd0,
	0xbd, 0xd0, 0xb7, 0xd0, 0xb0, 0xd0, 0xba, 0xd1, 0x86, 0xd0, 0xb8, 0xd1, 0x8f, 0x20, 0x45, 0x44,
	0x20, 0xd0, 0xbd, 0xd0, 0xb5, 0x20, 0xd0, 0xbd, 0xd0, 0xb0, 0xd0, 0xb9, 0xd0, 0xb4, 0xd0, 0xb5,
	0xd0, 0xbd, 0xd0, 0xb0, 0x18, 0x05, 0x20, 0x6a, 0xca, 0xa1, 0xec, 0x8e, 0x02, 0x04, 0x2d, 0x31,
	0x30, 0x37, 0x12, 0x78, 0x0a, 0x12, 0x50, 0x61, 0x79, 0x6f, 0x75, 0x74, 0x45, 0x44, 0x4e, 0x6f,
	0x74, 0x45, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x64, 0x10, 0x0d, 0x1a, 0x60, 0xaa, 0xf1, 0x30, 0x52,
	0x0a, 0x04, 0x2d, 0x31, 0x30, 0x38, 0x12, 0x46, 0xd0, 0x92, 0xd0, 0xbe, 0xd0, 0xb7, 0xd0, 0xbc,
	0xd0, 0xbe, 0xd0, 0xb6, 0xd0, 0xbd, 0xd0, 0xbe, 0xd1, 0x81, 0xd1, 0x82, 0xd1, 0x8c, 0x20, 0xd0,
	0xb7, 0xd0, 0xb0, 0xd1, 0x87, 0xd0, 0xb8, 0xd1, 0x81, 0xd0, 0xbb, 0xd0, 0xb5, 0xd0, 0xbd, 0xd0,
	0xb8, 0xd1, 0x8f, 0x20, 0xd1, 0x81, 0x20, 0xd0, 0xad, 0xd0, 0x94, 0x20, 0xd0, 0xbe, 0xd1, 0x82,
	0xd0, 0xba, 0xd0, 0xbb, 0xd1, 0x8e, 0xd1, 0x87, 0xd0, 0xb5, 0xd0, 0xbd, 0xd0, 0xb0, 0x18, 0x05,
	0x20, 0x6a, 0xca, 0xa1, 0xec, 0x8e, 0x02, 0x04, 0x2d, 0x31, 0x30, 0x38, 0x12, 0xa9, 0x01, 0x0a,
	0x17, 0x50, 0x61, 0x79, 0x6f, 0x75, 0x74, 0x49, 0x6e, 0x73, 0x75, 0x66, 0x66, 0x69, 0x63, 0x69,
	0x65, 0x6e, 0x74, 0x46, 0x75, 0x6e, 0x64, 0x73, 0x10, 0x0e, 0x1a, 0x8b, 0x01, 0xaa, 0xf1, 0x30,
	0x7b, 0x0a, 0x06, 0x2d, 0x31, 0x30, 0x30, 0x30, 0x30, 0x12, 0x6d, 0xd0, 0xa1, 0xd1, 0x83, 0xd0,
	0xbc, 0xd0, 0xbc, 0xd0, 0xb0, 0x20, 0xd0, 0xbe, 0xd1, 0x81, 0xd1, 0x82, 0xd0, 0xb0, 0xd1, 0x82,
	0xd0, 0xba, 0xd0, 0xb0, 0x20, 0xd0, 0xbd, 0xd0, 0xb0, 0x20, 0xd1, 0x81, 0xd1, 0x87, 0xd0, 0xb5,
	0xd1, 0x82, 0xd0, 0xb5, 0x20, 0xd0, 0xbd, 0xd0, 0xb5, 0x20, 0xd0, 0xbf, 0xd0, 0xbe, 0xd0, 0xb7,
	0xd0, 0xb2, 0xd0, 0xbe, 0xd0, 0xbb, 0xd1, 0x8f, 0xd0, 0xb5, 0xd1, 0x82, 0x20, 0xd0, 0xbf, 0xd1,
	0x80, 0xd0, 0xbe, 0xd0, 0xb2, 0xd0, 0xb5, 0xd1, 0x81, 0xd1, 0x82, 0xd0, 0xb8, 0x20, 0xd0, 0xb4,
	0xd0, 0xb0, 0xd0, 0xbd, 0xd0, 0xbd, 0xd1, 0x8b, 0xd0, 0xb9, 0x20, 0xd0, 0xbf, 0xd0, 0xbb, 0xd0,
	0xb0, 0xd1, 0x82, 0xd0, 0xb5, 0xd0, 0xb6, 0x21, 0x18, 0x05, 0x20, 0x76, 0xca, 0xa1, 0xec, 0x8e,
	0x02, 0x06, 0x2d, 0x31, 0x30, 0x30, 0x30, 0x30, 0x12, 0x64, 0x0a, 0x13, 0x50, 0x61, 0x79, 0x6f,
	0x75, 0x74, 0x49, 0x6e, 0x63, 0x6f, 0x72, 0x72, 0x65, 0x63, 0x74, 0x43, 0x61, 0x72, 0x64, 0x10,
	0x0f, 0x1a, 0x4b, 0xaa, 0xf1, 0x30, 0x3d, 0x0a, 0x04, 0x2d, 0x31, 0x30, 0x39, 0x12, 0x31, 0xd0,
	0x9d, 0xd0, 0xb5, 0xd0, 0xb2, 0xd0, 0xb5, 0xd1, 0x80, 0xd0, 0xbd, 0xd0, 0xbe, 0x20, 0xd1, 0x83,
	0xd0, 0xba, 0xd0, 0xb0, 0xd0, 0xb7, 0xd0, 0xb0, 0xd0, 0xbd, 0x20, 0xd0, 0xbd, 0xd0, 0xbe, 0xd0,
	0xbc, 0xd0, 0xb5, 0xd1, 0x80, 0x20, 0xd0, 0xba, 0xd0, 0xb0, 0xd1, 0x80, 0xd1, 0x82, 0xd1, 0x8b,
	0x18, 0x05, 0x20, 0x74, 0xca, 0xa1, 0xec, 0x8e, 0x02, 0x04, 0x2d, 0x31, 0x30, 0x39, 0x12, 0x7b,
	0x0a, 0x14, 0x50, 0x61, 0x79, 0x6f, 0x75, 0x74, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x42,
	0x6c, 0x6f, 0x63, 0x6b, 0x65, 0x64, 0x10, 0x10, 0x1a, 0x61, 0xaa, 0xf1, 0x30, 0x53, 0x0a, 0x04,
	0x2d, 0x31, 0x31, 0x30, 0x12, 0x47, 0xd0, 0xa3, 0xd1, 0x87, 0xd0, 0xb5, 0xd1, 0x82, 0xd0, 0xbd,
	0xd0, 0xb0, 0xd1, 0x8f, 0x20, 0xd0, 0xb7, 0xd0, 0xb0, 0xd0, 0xbf, 0xd0, 0xb8, 0xd1, 0x81, 0xd1,
	0x8c, 0x20, 0xd0, 0xbf, 0xd0, 0xb0, 0xd1, 0x80, 0xd1, 0x82, 0xd0, 0xbd, 0xd0, 0xb5, 0xd1, 0x80,
	0xd0, 0xb0, 0x20, 0xd0, 0xb7, 0xd0, 0xb0, 0xd0, 0xb1, 0xd0, 0xbb, 0xd0, 0xbe, 0xd0, 0xba, 0xd0,
	0xb8, 0xd1, 0x80, 0xd0, 0xbe, 0xd0, 0xb2, 0xd0, 0xb0, 0xd0, 0xbd, 0xd0, 0xb0, 0x18, 0x05, 0x20,
	0x6a, 0xca, 0xa1, 0xec, 0x8e, 0x02, 0x04, 0x2d, 0x31, 0x31, 0x30, 0x12, 0x60, 0x0a, 0x16, 0x50,
	0x61, 0x79, 0x6f, 0x75, 0x74, 0x49, 0x6e, 0x63, 0x6f, 0x72, 0x72, 0x65, 0x63, 0x74, 0x53, 0x65,
	0x73, 0x73, 0x69, 0x6f, 0x6e, 0x10, 0x11, 0x1a, 0x44, 0xaa, 0xf1, 0x30, 0x36, 0x0a, 0x04, 0x2d,
	0x31, 0x31, 0x31, 0x12, 0x2a, 0xd0, 0x9d, 0xd0, 0xb5, 0xd0, 0xb2, 0xd0, 0xb5, 0xd1, 0x80, 0xd0,
	0xbd, 0xd0, 0xbe, 0x20, 0xd1, 0x83, 0xd0, 0xba, 0xd0, 0xb0, 0xd0, 0xb7, 0xd0, 0xb0, 0xd0, 0xbd,
	0xd0, 0xb0, 0x20, 0xd1, 0x81, 0xd0, 0xb5, 0xd1, 0x81, 0xd1, 0x81, 0xd0, 0xb8, 0xd1, 0x8f, 0x18,
	0x05, 0x20, 0x6a, 0xca, 0xa1, 0xec, 0x8e, 0x02, 0x04, 0x2d, 0x31, 0x31, 0x31, 0x12, 0x6f, 0x0a,
	0x15, 0x50, 0x61, 0x79, 0x6f, 0x75, 0x74, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x4e, 0x6f,
	0x74, 0x46, 0x6f, 0x75, 0x6e, 0x64, 0x10, 0x12, 0x1a, 0x54, 0xaa, 0xf1, 0x30, 0x46, 0x0a, 0x04,
	0x2d, 0x31, 0x31, 0x32, 0x12, 0x3a, 0xd0, 0x98, 0xd0, 0xbd, 0xd1, 0x84, 0xd0, 0xbe, 0xd1, 0x80,
	0xd0, 0xbc, 0xd0, 0xb0, 0xd1, 0x86, 0xd0, 0xb8, 0xd1, 0x8f, 0x20, 0xd0, 0xbe, 0x20, 0xd0, 0xbf,
	0xd0, 0xbb, 0xd0, 0xb0, 0xd1, 0x82, 0xd0, 0xb5, 0xd0, 0xb6, 0xd0, 0xb5, 0x20, 0xd0, 0xbd, 0xd0,
	0xb5, 0x20, 0xd0, 0xbd, 0xd0, 0xb0, 0xd0, 0xb9, 0xd0, 0xb4, 0xd0, 0xb5, 0xd0, 0xbd, 0xd0, 0xb0,
	0x18, 0x05, 0x20, 0x6a, 0xca, 0xa1, 0xec, 0x8e, 0x02, 0x04, 0x2d, 0x31, 0x31, 0x32, 0x12, 0x77,
	0x0a, 0x15, 0x50, 0x61, 0x79, 0x6f, 0x75, 0x74, 0x49, 0x6e, 0x63, 0x6f, 0x72, 0x72, 0x65, 0x63,
	0x74, 0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x10, 0x13, 0x1a, 0x5c, 0xaa, 0xf1, 0x30, 0x4e, 0x0a,
	0x04, 0x2d, 0x31, 0x31, 0x33, 0x12, 0x42, 0xd0, 0xa1, 0xd1, 0x83, 0xd0, 0xbc, 0xd0, 0xbc, 0xd1,
	0x8b, 0x20, 0xd0, 0xbe, 0xd0, 0xbf, 0xd0, 0xb5, 0xd1, 0x80, 0xd0, 0xb0, 0xd1, 0x86, 0xd0, 0xb8,
	0xd0, 0xb8, 0x20, 0xd0, 0xb8, 0x20, 0xd0, 0xb3, 0xd0, 0xb0, 0xd1, 0x88, 0xd0, 0xb5, 0xd0, 0xbd,
	0xd0, 0xb8, 0xd1, 0x8f, 0x20, 0xd0, 0xbe, 0xd1, 0x82, 0xd0, 0xbb, 0xd0, 0xb8, 0xd1, 0x87, 0xd0,
	0xb0, 0xd1, 0x8e, 0xd1, 0x82, 0xd1, 0x81, 0xd1, 0x8f, 0x18, 0x05, 0x20, 0x6a, 0xca, 0xa1, 0xec,
	0x8e, 0x02, 0x04, 0x2d, 0x31, 0x31, 0x33, 0x12, 0x77, 0x0a, 0x14, 0x50, 0x61, 0x79, 0x6f, 0x75,
	0x74, 0x49, 0x73, 0x73, 0x75, 0x65, 0x72, 0x44, 0x65, 0x63, 0x6c, 0x69, 0x6e, 0x65, 0x64, 0x10,
	0x14, 0x1a, 0x5d, 0xaa, 0xf1, 0x30, 0x50, 0x0a, 0x03, 0x57, 0x30, 0x35, 0x12, 0x45, 0xd0, 0x9e,
	0xd1, 0x82, 0xd0, 0xba, 0xd0, 0xb0, 0xd0, 0xb7, 0x20, 0xd0, 0xad, 0xd0, 0xbc, 0xd0, 0xb8, 0xd1,
	0x82, 0xd0, 0xb5, 0xd0, 0xbd, 0xd1, 0x82, 0xd0, 0xb0, 0x2c, 0x20, 0xd0, 0xbf, 0xd1, 0x80, 0xd0,
	0xb8, 0xd1, 0x87, 0xd0, 0xb8, 0xd0, 0xbd, 0xd0, 0xb0, 0x20, 0xd0, 0xbd, 0xd0, 0xb5, 0x20, 0xd0,
	0xbe, 0xd0, 0xbf, 0xd1, 0x80, 0xd0, 0xb5, 0xd0, 0xb4, 0xd0, 0xb5, 0xd0, 0xbb, 0xd0, 0xb5, 0xd0,
	0xbd, 0xd0, 0xb0, 0x18, 0x05, 0x20, 0x69, 0xca, 0xa1, 0xec, 0x8e, 0x02, 0x03, 0x57, 0x30, 0x35,
	0x12, 0x4d, 0x0a, 0x12, 0x50, 0x61, 0x79, 0x6f, 0x75, 0x74, 0x43, 0x61, 0x72, 0x64, 0x44, 0x69,
	0x73, 0x61, 0x62, 0x6c, 0x65, 0x64, 0x10, 0x15, 0x1a, 0x35, 0xaa, 0xf1, 0x30, 0x28, 0x0a, 0x03,
	0x57, 0x30, 0x37, 0x12, 0x1d, 0xd0, 0x9a, 0xd0, 0xb0, 0xd1, 0x80, 0xd1, 0x82, 0xd0, 0xb0, 0x20,
	0xd0, 0xbe, 0xd1, 0x82, 0xd0, 0xba, 0xd0, 0xbb, 0xd1, 0x8e, 0xd1, 0x87, 0xd0, 0xb5, 0xd0, 0xbd,
	0xd0, 0xb0, 0x18, 0x05, 0x20, 0x6f, 0xca, 0xa1, 0xec, 0x8e, 0x02, 0x03, 0x57, 0x30, 0x37, 0x12,
	0x6d, 0x0a, 0x1a, 0x50, 0x61, 0x79, 0x6f, 0x75, 0x74, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63,
	0x74, 0x69, 0x6f, 0x6e, 0x4e, 0x6f, 0x74, 0x41, 0x63, 0x74, 0x69, 0x76, 0x65, 0x10, 0x16, 0x1a,
	0x4d, 0xaa, 0xf1, 0x30, 0x40, 0x0a, 0x03, 0x57, 0x31, 0x32, 0x12, 0x35, 0xd0, 0x9d, 0xd0, 0xb5,
	0xd0, 0xb4, 0xd0, 0xb5, 0xd0, 0xb9, 0xd1, 0x81, 0xd1, 0x82, 0xd0, 0xb2, 0xd0, 0xb8, 0xd1, 0x82,
	0xd0, 0xb5, 0xd0, 0xbb, 0xd1, 0x8c, 0xd0, 0xbd, 0xd0, 0xb0, 0xd1, 0x8f, 0x20, 0xd1, 0x82, 0xd1,
	0x80, 0xd0, 0xb0, 0xd0, 0xbd, 0xd0, 0xb7, 0xd0, 0xb0, 0xd0, 0xba, 0xd1, 0x86, 0xd0, 0xb8, 0xd1,
	0x8f, 0x18, 0x05, 0x20, 0x6a, 0xca, 0xa1, 0xec, 0x8e, 0x02, 0x03, 0x57, 0x31, 0x32, 0x12, 0x70,
	0x0a, 0x16, 0x50, 0x61, 0x79, 0x6f, 0x75, 0x74, 0x43, 0x61, 0x72, 0x64, 0x4e, 0x75, 0x6d, 0x49,
	0x6e, 0x63, 0x6f, 0x72, 0x72, 0x65, 0x63, 0x74, 0x10, 0x17, 0x1a, 0x54, 0xaa, 0xf1, 0x30, 0x47,
	0x0a, 0x03, 0x57, 0x31, 0x34, 0x12, 0x3c, 0xd0, 0x9d, 0xd0, 0xb5, 0xd0, 0xb4, 0xd0, 0xb5, 0xd0,
	0xb9, 0xd1, 0x81, 0xd1, 0x82, 0xd0, 0xb2, 0xd0, 0xb8, 0xd1, 0x82, 0xd0, 0xb5, 0xd0, 0xbb, 0xd1,
	0x8c, 0xd0, 0xbd, 0xd1, 0x8b, 0xd0, 0xb9, 0x20, 0xd0, 0xbd, 0xd0, 0xbe, 0xd0, 0xbc, 0xd0, 0xb5,
	0xd1, 0x80, 0x20, 0xd0, 0xba, 0xd0, 0xb0, 0xd1, 0x80, 0xd1, 0x82, 0xd0, 0xbe, 0xd1, 0x87, 0xd0,
	0xba, 0xd0, 0xb8, 0x18, 0x05, 0x20, 0x74, 0xca, 0xa1, 0xec, 0x8e, 0x02, 0x03, 0x57, 0x31, 0x34,
	0x12, 0x4f, 0x0a, 0x14, 0x50, 0x61, 0x79, 0x6f, 0x75, 0x74, 0x43, 0x6c, 0x69, 0x65, 0x6e, 0x74,
	0x44, 0x69, 0x73, 0x61, 0x62, 0x6c, 0x65, 0x64, 0x10, 0x18, 0x1a, 0x35, 0xaa, 0xf1, 0x30, 0x28,
	0x0a, 0x03, 0x57, 0x31, 0x37, 0x12, 0x1d, 0xd0, 0x9a, 0xd0, 0xbb, 0xd0, 0xb8, 0xd0, 0xb5, 0xd0,
	0xbd, 0xd1, 0x82, 0x20, 0xd0, 0xb8, 0xd1, 0x81, 0xd0, 0xba, 0xd0, 0xbb, 0xd1, 0x8e, 0xd1, 0x87,
	0xd0, 0xb5, 0xd0, 0xbd, 0x18, 0x05, 0x20, 0x77, 0xca, 0xa1, 0xec, 0x8e, 0x02, 0x03, 0x57, 0x31,
	0x37, 0x12, 0x5d, 0x0a, 0x16, 0x50, 0x61, 0x79, 0x6f, 0x75, 0x74, 0x53, 0x75, 0x73, 0x70, 0x69,
	0x63, 0x69, 0x6f, 0x75, 0x73, 0x43, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x10, 0x19, 0x1a, 0x41, 0xaa,
	0xf1, 0x30, 0x34, 0x0a, 0x03, 0x57, 0x31, 0x38, 0x12, 0x29, 0xd0, 0x9f, 0xd0, 0xbe, 0xd0, 0xb4,
	0xd0, 0xbe, 0xd0, 0xb7, 0xd1, 0x80, 0xd0, 0xb8, 0xd1, 0x82, 0xd0, 0xb5, 0xd0, 0xbb, 0xd1, 0x8c,
	0xd0, 0xbd, 0xd1, 0x8b, 0xd0, 0xb9, 0x20, 0xd0, 0xba, 0xd0, 0xbb, 0xd0, 0xb8, 0xd0, 0xb5, 0xd0,
	0xbd, 0xd1, 0x82, 0x18, 0x05, 0x20, 0x77, 0xca, 0xa1, 0xec, 0x8e, 0x02, 0x03, 0x57, 0x31, 0x38,
	0x12, 0x5b, 0x0a, 0x16, 0x50, 0x61, 0x79, 0x6f, 0x75, 0x74, 0x52, 0x65, 0x74, 0x72, 0x79, 0x54,
	0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x10, 0x1a, 0x1a, 0x3f, 0xaa, 0xf1,
	0x30, 0x32, 0x0a, 0x03, 0x57, 0x31, 0x39, 0x12, 0x27, 0xd0, 0x9f, 0xd0, 0xbe, 0xd0, 0xb2, 0xd1,
	0x82, 0xd0, 0xbe, 0xd1, 0x80, 0xd0, 0xbd, 0xd0, 0xb0, 0xd1, 0x8f, 0x20, 0xd1, 0x82, 0xd1, 0x80,
	0xd0, 0xb0, 0xd0, 0xbd, 0xd0, 0xb7, 0xd0, 0xb0, 0xd0, 0xba, 0xd1, 0x86, 0xd0, 0xb8, 0xd1, 0x8f,
	0x18, 0x05, 0x20, 0x6a, 0xca, 0xa1, 0xec, 0x8e, 0x02, 0x03, 0x57, 0x31, 0x39, 0x12, 0x54, 0x0a,
	0x11, 0x50, 0x61, 0x79, 0x6f, 0x75, 0x74, 0x43, 0x61, 0x72, 0x64, 0x45, 0x78, 0x70, 0x69, 0x72,
	0x65, 0x64, 0x10, 0x1b, 0x1a, 0x3d, 0xaa, 0xf1, 0x30, 0x30, 0x0a, 0x03, 0x57, 0x33, 0x33, 0x12,
	0x25, 0xd0, 0x9a, 0xd0, 0xb0, 0xd1, 0x80, 0xd1, 0x82, 0xd0, 0xbe, 0xd1, 0x87, 0xd0, 0xba, 0xd0,
	0xb0, 0x20, 0xd0, 0xbf, 0xd1, 0x80, 0xd0, 0xbe, 0xd1, 0x81, 0xd1, 0x80, 0xd0, 0xbe, 0xd1, 0x87,
	0xd0, 0xb5, 0xd0, 0xbd, 0xd0, 0xb0, 0x18, 0x05, 0x20, 0x72, 0xca, 0xa1, 0xec, 0x8e, 0x02, 0x03,
	0x57, 0x33, 0x33, 0x12, 0x72, 0x0a, 0x11, 0x50, 0x61, 0x79, 0x6f, 0x75, 0x74, 0x43, 0x61, 0x72,
	0x64, 0x4c, 0x69, 0x6d, 0x69, 0x74, 0x65, 0x64, 0x10, 0x1c, 0x1a, 0x5b, 0xaa, 0xf1, 0x30, 0x4e,
	0x0a, 0x03, 0x57, 0x33, 0x36, 0x12, 0x43, 0xd0, 0x9a, 0xd0, 0xb0, 0xd1, 0x80, 0xd1, 0x82, 0xd0,
	0xbe, 0xd1, 0x87, 0xd0, 0xba, 0xd0, 0xb0, 0x20, 0xd0, 0xbe, 0xd0, 0xb3, 0xd1, 0x80, 0xd0, 0xb0,
	0xd0, 0xbd, 0xd0, 0xb8, 0xd1, 0x87, 0xd0, 0xb5, 0xd0, 0xbd, 0xd0, 0xb0, 0x20, 0xd0, 0xb2, 0x20,
	0xd0, 0xb8, 0xd1, 0x81, 0xd0, 0xbf, 0xd0, 0xbe, 0xd0, 0xbb, 0xd1, 0x8c, 0xd0, 0xb7, 0xd0, 0xbe,
	0xd0, 0xb2, 0xd0, 0xb0, 0xd0, 0xbd, 0xd0, 0xb8, 0xd0, 0xb8, 0x18, 0x05, 0x20, 0x66, 0xca, 0xa1,
	0xec, 0x8e, 0x02, 0x03, 0x57, 0x33, 0x36, 0x12, 0x53, 0x0a, 0x10, 0x50, 0x61, 0x79, 0x6f, 0x75,
	0x74, 0x53, 0x74, 0x6f, 0x6c, 0x65, 0x6e, 0x43, 0x61, 0x72, 0x64, 0x10, 0x1d, 0x1a, 0x3d, 0xaa,
	0xf1, 0x30, 0x30, 0x0a, 0x03, 0x57, 0x34, 0x33, 0x12, 0x25, 0xd0, 0xa3, 0xd0, 0xba, 0xd1, 0x80,
	0xd0, 0xb0, 0xd0, 0xb4, 0xd0, 0xb5, 0xd0, 0xbd, 0xd0, 0xbd, 0xd0, 0xb0, 0xd1, 0x8f, 0x20, 0xd0,
	0xba, 0xd0, 0xb0, 0xd1, 0x80, 0xd1, 0x82, 0xd0, 0xbe, 0xd1, 0x87, 0xd0, 0xba, 0xd0, 0xb0, 0x18,
	0x05, 0x20, 0x6e, 0xca, 0xa1, 0xec, 0x8e, 0x02, 0x03, 0x57, 0x34, 0x33, 0x12, 0x50, 0x0a, 0x0d,
	0x50, 0x61, 0x79, 0x6f, 0x75, 0x74, 0x45, 0x78, 0x70, 0x43, 0x61, 0x72, 0x64, 0x10, 0x1e, 0x1a,
	0x3d, 0xaa, 0xf1, 0x30, 0x30, 0x0a, 0x03, 0x57, 0x35, 0x34, 0x12, 0x25, 0xd0, 0x9a, 0xd0, 0xb0,
	0xd1, 0x80, 0xd1, 0x82, 0xd0, 0xbe, 0xd1, 0x87, 0xd0, 0xba, 0xd0, 0xb0, 0x20, 0xd0, 0xbf, 0xd1,
	0x80, 0xd0, 0xbe, 0xd1, 0x81, 0xd1, 0x80, 0xd0, 0xbe, 0xd1, 0x87, 0xd0, 0xb5, 0xd0, 0xbd, 0xd0,
	0xb0, 0x18, 0x05, 0x20, 0x72, 0xca, 0xa1, 0xec, 0x8e, 0x02, 0x03, 0x57, 0x35, 0x34, 0x12, 0x73,
	0x0a, 0x18, 0x50, 0x61, 0x79, 0x6f, 0x75, 0x74, 0x46, 0x6f, 0x72, 0x62, 0x69, 0x64, 0x64, 0x65,
	0x6e, 0x46, 0x6f, 0x72, 0x43, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x10, 0x1f, 0x1a, 0x55, 0xaa, 0xf1,
	0x30, 0x48, 0x0a, 0x03, 0x57, 0x35, 0x37, 0x12, 0x3d, 0xd0, 0xa2, 0xd1, 0x80, 0xd0, 0xb0, 0xd0,
	0xbd, 0xd0, 0xb7, 0xd0, 0xb0, 0xd0, 0xba, 0xd1, 0x86, 0xd0, 0xb8, 0xd1, 0x8f, 0x20, 0xd0, 0xb7,
	0xd0, 0xb0, 0xd0, 0xbf, 0xd1, 0x80, 0xd0, 0xb5, 0xd1, 0x89, 0xd0, 0xb5, 0xd0, 0xbd, 0xd0, 0xb0,
	0x20, 0xd0, 0xb4, 0xd0, 0xbb, 0xd1, 0x8f, 0x20, 0xd0, 0xba, 0xd0, 0xbb, 0xd0, 0xb8, 0xd0, 0xb5,
	0xd0, 0xbd, 0xd1, 0x82, 0xd0, 0xb0, 0x18, 0x05, 0x20, 0x77, 0xca, 0xa1, 0xec, 0x8e, 0x02, 0x03,
	0x57, 0x35, 0x37, 0x12, 0x76, 0x0a, 0x17, 0x50, 0x61, 0x79, 0x6f, 0x75, 0x74, 0x46, 0x6f, 0x72,
	0x62, 0x69, 0x64, 0x64, 0x65, 0x6e, 0x54, 0x65, 0x72, 0x6d, 0x69, 0x6e, 0x61, 0x6c, 0x10, 0x20,
	0x1a, 0x59, 0xaa, 0xf1, 0x30, 0x4c, 0x0a, 0x03, 0x57, 0x35, 0x38, 0x12, 0x41, 0xd0, 0xa2, 0xd1,
	0x80, 0xd0, 0xb0, 0xd0, 0xbd, 0xd0, 0xb7, 0xd0, 0xb0, 0xd0, 0xba, 0xd1, 0x86, 0xd0, 0xb8, 0xd1,
	0x8f, 0x20, 0xd0, 0xb7, 0xd0, 0xb0, 0xd0, 0xbf, 0xd1, 0x80, 0xd0, 0xb5, 0xd1, 0x89, 0xd0, 0xb5,
	0xd0, 0xbd, 0xd0, 0xb0, 0x20, 0xd0, 0xb4, 0xd0, 0xbb, 0xd1, 0x8f, 0x20, 0xd1, 0x82, 0xd0, 0xb5,
	0xd1, 0x80, 0xd0, 0xbc, 0xd0, 0xb8, 0xd0, 0xbd, 0xd0, 0xb0, 0xd0, 0xbb, 0xd0, 0xb0, 0x18, 0x05,
	0x20, 0x6d, 0xca, 0xa1, 0xec, 0x8e, 0x02, 0x03, 0x57, 0x35, 0x38, 0x12, 0x66, 0x0a, 0x16, 0x50,
	0x61, 0x79, 0x6f, 0x75, 0x74, 0x43, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x53, 0x75, 0x73, 0x70, 0x69,
	0x63, 0x69, 0x6f, 0x75, 0x73, 0x10, 0x21, 0x1a, 0x4a, 0xaa, 0xf1, 0x30, 0x3d, 0x0a, 0x03, 0x57,
	0x35, 0x39, 0x12, 0x32, 0xd0, 0x9f, 0xd0, 0xbe, 0xd0, 0xb4, 0xd0, 0xbe, 0xd0, 0xb7, 0xd1, 0x80,
	0xd0, 0xb5, 0xd0, 0xbd, 0xd0, 0xb8, 0xd0, 0xb5, 0x20, 0xd0, 0xb2, 0x20, 0xd0, 0xbc, 0xd0, 0xbe,
	0xd1, 0x88, 0xd0, 0xb5, 0xd0, 0xbd, 0xd0, 0xbd, 0xd0, 0xb8, 0xd1, 0x87, 0xd0, 0xb5, 0xd1, 0x81,
	0xd1, 0x82, 0xd0, 0xb2, 0xd0, 0xb5, 0x18, 0x05, 0x20, 0x77, 0xca, 0xa1, 0xec, 0x8e, 0x02, 0x03,
	0x57, 0x35, 0x39, 0x12, 0x79, 0x0a, 0x13, 0x50, 0x61, 0x79, 0x6f, 0x75, 0x74, 0x4c, 0x69, 0x6d,
	0x69, 0x74, 0x45, 0x78, 0x63, 0x65, 0x65, 0x64, 0x65, 0x64, 0x10, 0x22, 0x1a, 0x60, 0xaa, 0xf1,
	0x30, 0x53, 0x0a, 0x03, 0x57, 0x36, 0x31, 0x12, 0x48, 0xd0, 0x9f, 0xd1, 0x80, 0xd0, 0xb5, 0xd0,
	0xb2, 0xd1, 0x8b, 0xd1, 0x88, 0xd0, 0xb0, 0xd0, 0xb5, 0xd1, 0x82, 0x20, 0xd0, 0xbb, 0xd0, 0xb8,
	0xd0, 0xbc, 0xd0, 0xb8, 0xd1, 0x82, 0x20, 0xd1, 0x81, 0xd1, 0x83, 0xd0, 0xbc, 0xd0, 0xbc, 0xd1,
	0x8b, 0x20, 0xd0, 0xba, 0xd0, 0xb0, 0xd1, 0x80, 0xd1, 0x82, 0xd1, 0x8b, 0x20, 0xd0, 0xbf, 0xd0,
	0xbe, 0xd0, 0xbb, 0xd1, 0x83, 0xd1, 0x87, 0xd0, 0xb0, 0xd1, 0x82, 0xd0, 0xb5, 0xd0, 0xbb, 0xd1,
	0x8f, 0x18, 0x05, 0x20, 0x6a, 0xca, 0xa1, 0xec, 0x8e, 0x02, 0x03, 0x57, 0x36, 0x31, 0x12, 0x78,
	0x0a, 0x11, 0x50, 0x61, 0x79, 0x6f, 0x75, 0x74, 0x4c, 0x69, 0x6d, 0x69, 0x74, 0x65, 0x64, 0x43,
	0x61, 0x72, 0x64, 0x10, 0x23, 0x1a, 0x61, 0xaa, 0xf1, 0x30, 0x54, 0x0a, 0x03, 0x57, 0x36, 0x32,
	0x12, 0x49, 0xd0, 0x9a, 0xd0, 0xb0, 0xd1, 0x80, 0xd1, 0x82, 0xd0, 0xbe, 0xd1, 0x87, 0xd0, 0xba,
	0xd0, 0xb0, 0x20, 0xd1, 0x81, 0x20, 0xd0, 0xbe, 0xd0, 0xb3, 0xd1, 0x80, 0xd0, 0xb0, 0xd0, 0xbd,
	0xd0, 0xb8, 0xd1, 0x87, 0xd0, 0xb5, 0xd0, 0xbd, 0xd0, 0xbd, 0xd1, 0x8b, 0xd0, 0xbc, 0x20, 0xd0,
	0xb8, 0xd1, 0x81, 0xd0, 0xbf, 0xd0, 0xbe, 0xd0, 0xbb, 0xd1, 0x8c, 0xd0, 0xb7, 0xd0, 0xbe, 0xd0,
	0xb2, 0xd0, 0xb0, 0xd0, 0xbd, 0xd0, 0xb8, 0xd0, 0xb5, 0xd0, 0xbc, 0x18, 0x05, 0x20, 0x66, 0xca,
	0xa1, 0xec, 0x8e, 0x02, 0x03, 0x57, 0x36, 0x32, 0x12, 0x5d, 0x0a, 0x16, 0x50, 0x61, 0x79, 0x6f,
	0x75, 0x74, 0x53, 0x65, 0x63, 0x75, 0x72, 0x69, 0x74, 0x79, 0x42, 0x72, 0x65, 0x61, 0x63, 0x68,
	0x65, 0x64, 0x10, 0x24, 0x1a, 0x41, 0xaa, 0xf1, 0x30, 0x34, 0x0a, 0x03, 0x57, 0x36, 0x33, 0x12,
	0x29, 0xd0, 0x9d, 0xd0, 0xb0, 0xd1, 0x80, 0xd1, 0x83, 0xd1, 0x88, 0xd0, 0xb5, 0xd0, 0xbd, 0xd0,
	0xb0, 0x20, 0xd0, 0xb1, 0xd0, 0xb5, 0xd0, 0xb7, 0xd0, 0xbe, 0xd0, 0xbf, 0xd0, 0xb0, 0xd1, 0x81,
	0xd0, 0xbd, 0xd0, 0xbe, 0xd1, 0x81, 0xd1, 0x82, 0xd1, 0x8c, 0x18, 0x05, 0x20, 0x6a, 0xca, 0xa1,
	0xec, 0x8e, 0x02, 0x03, 0x57, 0x36, 0x33, 0x12, 0x5e, 0x0a, 0x17, 0x50, 0x61, 0x79, 0x6f, 0x75,
	0x74, 0x55, 0x6e, 0x61, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x6c, 0x65, 0x49, 0x73, 0x73, 0x75,
	0x65, 0x72, 0x10, 0x25, 0x1a, 0x41, 0xaa, 0xf1, 0x30, 0x34, 0x0a, 0x03, 0x57, 0x39, 0x31, 0x12,
	0x29, 0xd0, 0x9d, 0xd0, 0xb5, 0xd0, 0xb4, 0xd0, 0xb5, 0xd0, 0xb9, 0xd1, 0x81, 0xd1, 0x82, 0xd0,
	0xb2, 0xd1, 0x83, 0xd1, 0x8e, 0xd1, 0x89, 0xd0, 0xb8, 0xd0, 0xb9, 0x20, 0xd1, 0x8d, 0xd0, 0xbc,
	0xd0, 0xb8, 0xd1, 0x82, 0xd0, 0xb5, 0xd0, 0xbd, 0xd1, 0x82, 0x18, 0x05, 0x20, 0x6b, 0xca, 0xa1,
	0xec, 0x8e, 0x02, 0x03, 0x57, 0x39, 0x31, 0x12, 0x87, 0x01, 0x0a, 0x1c, 0x50, 0x61, 0x79, 0x6f,
	0x75, 0x74, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x4e, 0x6f, 0x74,
	0x46, 0x69, 0x6e, 0x69, 0x73, 0x68, 0x65, 0x64, 0x10, 0x26, 0x1a, 0x65, 0xaa, 0xf1, 0x30, 0x58,
	0x0a, 0x03, 0x57, 0x39, 0x33, 0x12, 0x4d, 0xd0, 0xa2, 0xd1, 0x80, 0xd0, 0xb0, 0xd0, 0xbd, 0xd0,
	0xb7, 0xd0, 0xb0, 0xd0, 0xba, 0xd1, 0x86, 0xd0, 0xb8, 0xd1, 0x8f, 0x20, 0xd0, 0xbd, 0xd0, 0xb5,
	0x20, 0xd0, 0xb7, 0xd0, 0xb0, 0xd0, 0xb2, 0xd0, 0xb5, 0xd1, 0x80, 0xd1, 0x88, 0xd0, 0xb5, 0xd0,
	0xbd, 0xd0, 0xb0, 0x2e, 0x20, 0xd0, 0x9d, 0xd0, 0xb0, 0xd1, 0x80, 0xd1, 0x83, 0xd1, 0x88, 0xd0,
	0xb5, 0xd0, 0xbd, 0xd0, 0xb8, 0xd0, 0xb5, 0x20, 0xd0, 0xb7, 0xd0, 0xb0, 0xd0, 0xba, 0xd0, 0xbe,
	0xd0, 0xbd, 0xd0, 0xb0, 0x18, 0x05, 0x20, 0x6a, 0xca, 0xa1, 0xec, 0x8e, 0x02, 0x03, 0x57, 0x39,
	0x33, 0x12, 0x60, 0x0a, 0x16, 0x50, 0x61, 0x79, 0x6f, 0x75, 0x74, 0x53, 0x79, 0x73, 0x74, 0x65,
	0x6d, 0x4d, 0x61, 0x6c, 0x66, 0x75, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x10, 0x27, 0x1a, 0x44, 0xaa,
	0xf1, 0x30, 0x37, 0x0a, 0x03, 0x57, 0x39, 0x36, 0x12, 0x2c, 0xd0, 0x9d, 0xd0, 0xb5, 0xd0, 0xb8,
	0xd1, 0x81, 0xd0, 0xbf, 0xd1, 0x80, 0xd0, 0xb0, 0xd0, 0xb2, 0xd0, 0xbd, 0xd0, 0xbe, 0xd1, 0x81,
	0xd1, 0x82, 0xd1, 0x8c, 0x20, 0xd0, 0xb2, 0x20, 0xd1, 0x81, 0xd0, 0xb8, 0xd1, 0x81, 0xd1, 0x82,
	0xd0, 0xb5, 0xd0, 0xbc, 0xd0, 0xb5, 0x18, 0x0b, 0x20, 0x6c, 0xca, 0xa1, 0xec, 0x8e, 0x02, 0x03,
	0x57, 0x39, 0x36, 0x1a, 0x68, 0xb2, 0xf1, 0x30, 0x10, 0x0a, 0x01, 0x30, 0x12, 0x07, 0x64, 0x65,
	0x66, 0x61, 0x75, 0x6c, 0x74, 0x18, 0x0f, 0x20, 0x00, 0xaa, 0x82, 0xec, 0x8e, 0x02, 0x28, 0x64,
	0x65, 0x66, 0x61, 0x75, 0x6c, 0x74, 0x5f, 0x6a, 0x75, 0x73, 0x61, 0x6e, 0x5f, 0x72, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x5f, 0x70, 0x61, 0x79, 0x6f, 0x75,
	0x74, 0x5f, 0x76, 0x61, 0x6c, 0x75, 0x65, 0xb2, 0x82, 0xec, 0x8e, 0x02, 0x20, 0x6a, 0x75, 0x73,
	0x61, 0x6e, 0x5f, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x5f, 0x63, 0x6f, 0x64, 0x65,
	0x5f, 0x70, 0x61, 0x79, 0x6f, 0x75, 0x74, 0x5f, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0xad, 0x01,
	0x0a, 0x20, 0x6a, 0x75, 0x73, 0x61, 0x6e, 0x5f, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x5f, 0x63, 0x6f, 0x64, 0x65, 0x5f, 0x70, 0x61, 0x79, 0x6f, 0x75, 0x74, 0x5f, 0x76, 0x61, 0x6c,
	0x75, 0x65, 0x12, 0x21, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x62, 0x75, 0x66, 0x2e, 0x45, 0x6e, 0x75, 0x6d, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x4f, 0x70,
	0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x95, 0x8e, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x40, 0x2e,
	0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x2e, 0x6a, 0x75, 0x73, 0x61, 0x6e,
	0x5f, 0x70, 0x61, 0x79, 0x6f, 0x75, 0x74, 0x2e, 0x6a, 0x75, 0x73, 0x61, 0x6e, 0x5f, 0x70, 0x61,
	0x79, 0x6f, 0x75, 0x74, 0x2e, 0x4a, 0x75, 0x73, 0x61, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x43, 0x6f, 0x64, 0x65, 0x50, 0x61, 0x79, 0x6f, 0x75, 0x74, 0x52, 0x65, 0x66, 0x52,
	0x1c, 0x6a, 0x75, 0x73, 0x61, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x43, 0x6f,
	0x64, 0x65, 0x50, 0x61, 0x79, 0x6f, 0x75, 0x74, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0xb7, 0x01,
	0x0a, 0x28, 0x64, 0x65, 0x66, 0x61, 0x75, 0x6c, 0x74, 0x5f, 0x6a, 0x75, 0x73, 0x61, 0x6e, 0x5f,
	0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x5f, 0x70, 0x61,
	0x79, 0x6f, 0x75, 0x74, 0x5f, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x12, 0x1c, 0x2e, 0x67, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x45, 0x6e, 0x75,
	0x6d, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x96, 0x8e, 0x06, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x40, 0x2e, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x2e, 0x6a, 0x75,
	0x73, 0x61, 0x6e, 0x5f, 0x70, 0x61, 0x79, 0x6f, 0x75, 0x74, 0x2e, 0x6a, 0x75, 0x73, 0x61, 0x6e,
	0x5f, 0x70, 0x61, 0x79, 0x6f, 0x75, 0x74, 0x2e, 0x4a, 0x75, 0x73, 0x61, 0x6e, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x43, 0x6f, 0x64, 0x65, 0x50, 0x61, 0x79, 0x6f, 0x75, 0x74, 0x52,
	0x65, 0x66, 0x52, 0x23, 0x64, 0x65, 0x66, 0x61, 0x75, 0x6c, 0x74, 0x4a, 0x75, 0x73, 0x61, 0x6e,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x43, 0x6f, 0x64, 0x65, 0x50, 0x61, 0x79, 0x6f,
	0x75, 0x74, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x42, 0x2d, 0x5a, 0x2b, 0x67, 0x69, 0x74, 0x2e, 0x6c,
	0x6f, 0x63, 0x61, 0x6c, 0x2f, 0x73, 0x65, 0x6e, 0x73, 0x69, 0x74, 0x69, 0x76, 0x65, 0x2f, 0x69,
	0x6e, 0x6e, 0x65, 0x72, 0x70, 0x62, 0x2f, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e,
	0x67, 0x2f, 0x67, 0x72, 0x70, 0x63, 0x62, 0x08, 0x65, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73,
	0x70, 0xe8, 0x07,
})

var (
	file_inner_processing_grpc_jusan_payout_proto_rawDescOnce sync.Once
	file_inner_processing_grpc_jusan_payout_proto_rawDescData []byte
)

func file_inner_processing_grpc_jusan_payout_proto_rawDescGZIP() []byte {
	file_inner_processing_grpc_jusan_payout_proto_rawDescOnce.Do(func() {
		file_inner_processing_grpc_jusan_payout_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_inner_processing_grpc_jusan_payout_proto_rawDesc), len(file_inner_processing_grpc_jusan_payout_proto_rawDesc)))
	})
	return file_inner_processing_grpc_jusan_payout_proto_rawDescData
}

var file_inner_processing_grpc_jusan_payout_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_inner_processing_grpc_jusan_payout_proto_msgTypes = make([]protoimpl.MessageInfo, 1)
var file_inner_processing_grpc_jusan_payout_proto_goTypes = []any{
	(JusanResponseCodePayout)(0),          // 0: processing.jusan_payout.jusan_payout.JusanResponseCodePayout
	(*JusanResponseCodePayoutRef)(nil),    // 1: processing.jusan_payout.jusan_payout.JusanResponseCodePayoutRef
	(EnumTransactionStatus)(0),            // 2: processing.transaction.transaction_status.EnumTransactionStatus
	(IntegrationError)(0),                 // 3: processing.integration.integration.IntegrationError
	(*descriptorpb.EnumValueOptions)(nil), // 4: google.protobuf.EnumValueOptions
	(*descriptorpb.EnumOptions)(nil),      // 5: google.protobuf.EnumOptions
}
var file_inner_processing_grpc_jusan_payout_proto_depIdxs = []int32{
	2, // 0: processing.jusan_payout.jusan_payout.JusanResponseCodePayoutRef.transaction_status:type_name -> processing.transaction.transaction_status.EnumTransactionStatus
	3, // 1: processing.jusan_payout.jusan_payout.JusanResponseCodePayoutRef.integration_error:type_name -> processing.integration.integration.IntegrationError
	4, // 2: processing.jusan_payout.jusan_payout.jusan_response_code_payout_value:extendee -> google.protobuf.EnumValueOptions
	5, // 3: processing.jusan_payout.jusan_payout.default_jusan_response_code_payout_value:extendee -> google.protobuf.EnumOptions
	1, // 4: processing.jusan_payout.jusan_payout.jusan_response_code_payout_value:type_name -> processing.jusan_payout.jusan_payout.JusanResponseCodePayoutRef
	1, // 5: processing.jusan_payout.jusan_payout.default_jusan_response_code_payout_value:type_name -> processing.jusan_payout.jusan_payout.JusanResponseCodePayoutRef
	6, // [6:6] is the sub-list for method output_type
	6, // [6:6] is the sub-list for method input_type
	4, // [4:6] is the sub-list for extension type_name
	2, // [2:4] is the sub-list for extension extendee
	0, // [0:2] is the sub-list for field type_name
}

func init() { file_inner_processing_grpc_jusan_payout_proto_init() }
func file_inner_processing_grpc_jusan_payout_proto_init() {
	if File_inner_processing_grpc_jusan_payout_proto != nil {
		return
	}
	file_inner_processing_grpc_transaction_status_proto_init()
	file_inner_processing_grpc_integration_proto_init()
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_inner_processing_grpc_jusan_payout_proto_rawDesc), len(file_inner_processing_grpc_jusan_payout_proto_rawDesc)),
			NumEnums:      1,
			NumMessages:   1,
			NumExtensions: 2,
			NumServices:   0,
		},
		GoTypes:           file_inner_processing_grpc_jusan_payout_proto_goTypes,
		DependencyIndexes: file_inner_processing_grpc_jusan_payout_proto_depIdxs,
		EnumInfos:         file_inner_processing_grpc_jusan_payout_proto_enumTypes,
		MessageInfos:      file_inner_processing_grpc_jusan_payout_proto_msgTypes,
		ExtensionInfos:    file_inner_processing_grpc_jusan_payout_proto_extTypes,
	}.Build()
	File_inner_processing_grpc_jusan_payout_proto = out.File
	file_inner_processing_grpc_jusan_payout_proto_goTypes = nil
	file_inner_processing_grpc_jusan_payout_proto_depIdxs = nil
}
