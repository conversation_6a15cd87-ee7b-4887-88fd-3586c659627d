// Code generated by MockGen. DO NOT EDIT.
// Source: transaction_grpc.pb.go

// Package grpcmock is a generated GoMock package.
package grpcmock

import (
	context "context"
	reflect "reflect"

	grpc "git.local/sensitive/innerpb/processing/grpc"
	gomock "github.com/golang/mock/gomock"
	grpc0 "google.golang.org/grpc"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
)

// MockTransactionClient is a mock of TransactionClient interface.
type MockTransactionClient struct {
	ctrl     *gomock.Controller
	recorder *MockTransactionClientMockRecorder
}

// MockTransactionClientMockRecorder is the mock recorder for MockTransactionClient.
type MockTransactionClientMockRecorder struct {
	mock *MockTransactionClient
}

// NewMockTransactionClient creates a new mock instance.
func NewMockTransactionClient(ctrl *gomock.Controller) *MockTransactionClient {
	mock := &MockTransactionClient{ctrl: ctrl}
	mock.recorder = &MockTransactionClientMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockTransactionClient) EXPECT() *MockTransactionClientMockRecorder {
	return m.recorder
}

// BillPayOut mocks base method.
func (m *MockTransactionClient) BillPayOut(ctx context.Context, in *grpc.BillPayoutRequest, opts ...grpc0.CallOption) (*emptypb.Empty, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "BillPayOut", varargs...)
	ret0, _ := ret[0].(*emptypb.Empty)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BillPayOut indicates an expected call of BillPayOut.
func (mr *MockTransactionClientMockRecorder) BillPayOut(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BillPayOut", reflect.TypeOf((*MockTransactionClient)(nil).BillPayOut), varargs...)
}

// CalculateAndUpdateTransactionAmount mocks base method.
func (m *MockTransactionClient) CalculateAndUpdateTransactionAmount(ctx context.Context, in *grpc.CalculateAndUpdateTransactionAmountRequest, opts ...grpc0.CallOption) (*emptypb.Empty, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "CalculateAndUpdateTransactionAmount", varargs...)
	ret0, _ := ret[0].(*emptypb.Empty)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CalculateAndUpdateTransactionAmount indicates an expected call of CalculateAndUpdateTransactionAmount.
func (mr *MockTransactionClientMockRecorder) CalculateAndUpdateTransactionAmount(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CalculateAndUpdateTransactionAmount", reflect.TypeOf((*MockTransactionClient)(nil).CalculateAndUpdateTransactionAmount), varargs...)
}

// CheckTransactionHash mocks base method.
func (m *MockTransactionClient) CheckTransactionHash(ctx context.Context, in *grpc.CheckTransactionHashRequestV1, opts ...grpc0.CallOption) (*emptypb.Empty, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "CheckTransactionHash", varargs...)
	ret0, _ := ret[0].(*emptypb.Empty)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CheckTransactionHash indicates an expected call of CheckTransactionHash.
func (mr *MockTransactionClientMockRecorder) CheckTransactionHash(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CheckTransactionHash", reflect.TypeOf((*MockTransactionClient)(nil).CheckTransactionHash), varargs...)
}

// CreateTransactionByPhone mocks base method.
func (m *MockTransactionClient) CreateTransactionByPhone(ctx context.Context, in *grpc.CreateTransactionByPhoneRequest, opts ...grpc0.CallOption) (*grpc.CreateTransactionByPhoneResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "CreateTransactionByPhone", varargs...)
	ret0, _ := ret[0].(*grpc.CreateTransactionByPhoneResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateTransactionByPhone indicates an expected call of CreateTransactionByPhone.
func (mr *MockTransactionClientMockRecorder) CreateTransactionByPhone(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateTransactionByPhone", reflect.TypeOf((*MockTransactionClient)(nil).CreateTransactionByPhone), varargs...)
}

// GetByIDWithType mocks base method.
func (m *MockTransactionClient) GetByIDWithType(ctx context.Context, in *grpc.GetByIDWithTypeRequest, opts ...grpc0.CallOption) (*grpc.GetByIDWithTypeResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetByIDWithType", varargs...)
	ret0, _ := ret[0].(*grpc.GetByIDWithTypeResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetByIDWithType indicates an expected call of GetByIDWithType.
func (mr *MockTransactionClientMockRecorder) GetByIDWithType(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetByIDWithType", reflect.TypeOf((*MockTransactionClient)(nil).GetByIDWithType), varargs...)
}

// GetPayInTransactionsByPeriodAndAcquirer mocks base method.
func (m *MockTransactionClient) GetPayInTransactionsByPeriodAndAcquirer(ctx context.Context, in *grpc.GetPayInTransactionsByPeriodAndAcquirerReqV1, opts ...grpc0.CallOption) (*grpc.GetTransactionsResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetPayInTransactionsByPeriodAndAcquirer", varargs...)
	ret0, _ := ret[0].(*grpc.GetTransactionsResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetPayInTransactionsByPeriodAndAcquirer indicates an expected call of GetPayInTransactionsByPeriodAndAcquirer.
func (mr *MockTransactionClientMockRecorder) GetPayInTransactionsByPeriodAndAcquirer(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetPayInTransactionsByPeriodAndAcquirer", reflect.TypeOf((*MockTransactionClient)(nil).GetPayInTransactionsByPeriodAndAcquirer), varargs...)
}

// GetTransactionByID mocks base method.
func (m *MockTransactionClient) GetTransactionByID(ctx context.Context, in *grpc.GetTransactionByIDRequestV1, opts ...grpc0.CallOption) (*grpc.TransactionDataV1, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetTransactionByID", varargs...)
	ret0, _ := ret[0].(*grpc.TransactionDataV1)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetTransactionByID indicates an expected call of GetTransactionByID.
func (mr *MockTransactionClientMockRecorder) GetTransactionByID(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetTransactionByID", reflect.TypeOf((*MockTransactionClient)(nil).GetTransactionByID), varargs...)
}

// GetTransactionTypeByID mocks base method.
func (m *MockTransactionClient) GetTransactionTypeByID(ctx context.Context, in *grpc.GetTransactionTypeByIDRequestV1, opts ...grpc0.CallOption) (*grpc.GetTransactionTypeByIDResponseV1, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetTransactionTypeByID", varargs...)
	ret0, _ := ret[0].(*grpc.GetTransactionTypeByIDResponseV1)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetTransactionTypeByID indicates an expected call of GetTransactionTypeByID.
func (mr *MockTransactionClientMockRecorder) GetTransactionTypeByID(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetTransactionTypeByID", reflect.TypeOf((*MockTransactionClient)(nil).GetTransactionTypeByID), varargs...)
}

// GetTransactionsByFinalStatusAndPeriodWithLimit mocks base method.
func (m *MockTransactionClient) GetTransactionsByFinalStatusAndPeriodWithLimit(ctx context.Context, in *grpc.GetTransactionsByFinalStatusAndPeriodWithLimitReqV1, opts ...grpc0.CallOption) (*grpc.GetTransactionsResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetTransactionsByFinalStatusAndPeriodWithLimit", varargs...)
	ret0, _ := ret[0].(*grpc.GetTransactionsResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetTransactionsByFinalStatusAndPeriodWithLimit indicates an expected call of GetTransactionsByFinalStatusAndPeriodWithLimit.
func (mr *MockTransactionClientMockRecorder) GetTransactionsByFinalStatusAndPeriodWithLimit(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetTransactionsByFinalStatusAndPeriodWithLimit", reflect.TypeOf((*MockTransactionClient)(nil).GetTransactionsByFinalStatusAndPeriodWithLimit), varargs...)
}

// GetTransactionsByPeriodAndCallbackStatus mocks base method.
func (m *MockTransactionClient) GetTransactionsByPeriodAndCallbackStatus(ctx context.Context, in *grpc.GetTransactionsByCallbackStatusReqV1, opts ...grpc0.CallOption) (*grpc.GetTransactionsResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetTransactionsByPeriodAndCallbackStatus", varargs...)
	ret0, _ := ret[0].(*grpc.GetTransactionsResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetTransactionsByPeriodAndCallbackStatus indicates an expected call of GetTransactionsByPeriodAndCallbackStatus.
func (mr *MockTransactionClientMockRecorder) GetTransactionsByPeriodAndCallbackStatus(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetTransactionsByPeriodAndCallbackStatus", reflect.TypeOf((*MockTransactionClient)(nil).GetTransactionsByPeriodAndCallbackStatus), varargs...)
}

// GetTransactionsByPeriodAndStatus mocks base method.
func (m *MockTransactionClient) GetTransactionsByPeriodAndStatus(ctx context.Context, in *grpc.GetTransactionsByStatusReqV1, opts ...grpc0.CallOption) (*grpc.GetTransactionsResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetTransactionsByPeriodAndStatus", varargs...)
	ret0, _ := ret[0].(*grpc.GetTransactionsResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetTransactionsByPeriodAndStatus indicates an expected call of GetTransactionsByPeriodAndStatus.
func (mr *MockTransactionClientMockRecorder) GetTransactionsByPeriodAndStatus(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetTransactionsByPeriodAndStatus", reflect.TypeOf((*MockTransactionClient)(nil).GetTransactionsByPeriodAndStatus), varargs...)
}

// GetTransactionsByProjectInfo mocks base method.
func (m *MockTransactionClient) GetTransactionsByProjectInfo(ctx context.Context, in *grpc.GetTransactionsByProjectInfoRequestV1, opts ...grpc0.CallOption) (*grpc.GetTransactionsResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetTransactionsByProjectInfo", varargs...)
	ret0, _ := ret[0].(*grpc.GetTransactionsResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetTransactionsByProjectInfo indicates an expected call of GetTransactionsByProjectInfo.
func (mr *MockTransactionClientMockRecorder) GetTransactionsByProjectInfo(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetTransactionsByProjectInfo", reflect.TypeOf((*MockTransactionClient)(nil).GetTransactionsByProjectInfo), varargs...)
}

// IncreaseTryCount mocks base method.
func (m *MockTransactionClient) IncreaseTryCount(ctx context.Context, in *grpc.IncreaseTryCountRequest, opts ...grpc0.CallOption) (*emptypb.Empty, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "IncreaseTryCount", varargs...)
	ret0, _ := ret[0].(*emptypb.Empty)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// IncreaseTryCount indicates an expected call of IncreaseTryCount.
func (mr *MockTransactionClientMockRecorder) IncreaseTryCount(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "IncreaseTryCount", reflect.TypeOf((*MockTransactionClient)(nil).IncreaseTryCount), varargs...)
}

// MakeAutoCharge mocks base method.
func (m *MockTransactionClient) MakeAutoCharge(ctx context.Context, in *grpc.MakeAutoChargeRequestV1, opts ...grpc0.CallOption) (*emptypb.Empty, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "MakeAutoCharge", varargs...)
	ret0, _ := ret[0].(*emptypb.Empty)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// MakeAutoCharge indicates an expected call of MakeAutoCharge.
func (mr *MockTransactionClientMockRecorder) MakeAutoCharge(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "MakeAutoCharge", reflect.TypeOf((*MockTransactionClient)(nil).MakeAutoCharge), varargs...)
}

// SaveAcquirerResponse mocks base method.
func (m *MockTransactionClient) SaveAcquirerResponse(ctx context.Context, in *grpc.SaveAcquirerResponseRequest, opts ...grpc0.CallOption) (*emptypb.Empty, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "SaveAcquirerResponse", varargs...)
	ret0, _ := ret[0].(*emptypb.Empty)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SaveAcquirerResponse indicates an expected call of SaveAcquirerResponse.
func (mr *MockTransactionClientMockRecorder) SaveAcquirerResponse(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SaveAcquirerResponse", reflect.TypeOf((*MockTransactionClient)(nil).SaveAcquirerResponse), varargs...)
}

// SendReceipt mocks base method.
func (m *MockTransactionClient) SendReceipt(ctx context.Context, in *grpc.SendRequest, opts ...grpc0.CallOption) (*emptypb.Empty, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "SendReceipt", varargs...)
	ret0, _ := ret[0].(*emptypb.Empty)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SendReceipt indicates an expected call of SendReceipt.
func (mr *MockTransactionClientMockRecorder) SendReceipt(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SendReceipt", reflect.TypeOf((*MockTransactionClient)(nil).SendReceipt), varargs...)
}

// SetAdditionalData mocks base method.
func (m *MockTransactionClient) SetAdditionalData(ctx context.Context, in *grpc.SetAdditionalDataRequest, opts ...grpc0.CallOption) (*emptypb.Empty, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "SetAdditionalData", varargs...)
	ret0, _ := ret[0].(*emptypb.Empty)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SetAdditionalData indicates an expected call of SetAdditionalData.
func (mr *MockTransactionClientMockRecorder) SetAdditionalData(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetAdditionalData", reflect.TypeOf((*MockTransactionClient)(nil).SetAdditionalData), varargs...)
}

// SetRefundWaitingStatus mocks base method.
func (m *MockTransactionClient) SetRefundWaitingStatus(ctx context.Context, in *grpc.SetRefundWaitingStatusRequestV1, opts ...grpc0.CallOption) (*grpc.SetRefundWaitingStatusResponseV1, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "SetRefundWaitingStatus", varargs...)
	ret0, _ := ret[0].(*grpc.SetRefundWaitingStatusResponseV1)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SetRefundWaitingStatus indicates an expected call of SetRefundWaitingStatus.
func (mr *MockTransactionClientMockRecorder) SetRefundWaitingStatus(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetRefundWaitingStatus", reflect.TypeOf((*MockTransactionClient)(nil).SetRefundWaitingStatus), varargs...)
}

// UpdateCallbackStatus mocks base method.
func (m *MockTransactionClient) UpdateCallbackStatus(ctx context.Context, in *grpc.UpdateCallbackStatusRequestV1, opts ...grpc0.CallOption) (*emptypb.Empty, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "UpdateCallbackStatus", varargs...)
	ret0, _ := ret[0].(*emptypb.Empty)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpdateCallbackStatus indicates an expected call of UpdateCallbackStatus.
func (mr *MockTransactionClientMockRecorder) UpdateCallbackStatus(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateCallbackStatus", reflect.TypeOf((*MockTransactionClient)(nil).UpdateCallbackStatus), varargs...)
}

// UpdateStatus mocks base method.
func (m *MockTransactionClient) UpdateStatus(ctx context.Context, in *grpc.UpdateStatusRequest, opts ...grpc0.CallOption) (*emptypb.Empty, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "UpdateStatus", varargs...)
	ret0, _ := ret[0].(*emptypb.Empty)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpdateStatus indicates an expected call of UpdateStatus.
func (mr *MockTransactionClientMockRecorder) UpdateStatus(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateStatus", reflect.TypeOf((*MockTransactionClient)(nil).UpdateStatus), varargs...)
}

// UpdateTransactionStatus mocks base method.
func (m *MockTransactionClient) UpdateTransactionStatus(ctx context.Context, in *grpc.UpdateTransactionStatusRequestV1, opts ...grpc0.CallOption) (*emptypb.Empty, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "UpdateTransactionStatus", varargs...)
	ret0, _ := ret[0].(*emptypb.Empty)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpdateTransactionStatus indicates an expected call of UpdateTransactionStatus.
func (mr *MockTransactionClientMockRecorder) UpdateTransactionStatus(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateTransactionStatus", reflect.TypeOf((*MockTransactionClient)(nil).UpdateTransactionStatus), varargs...)
}

// MockTransactionServer is a mock of TransactionServer interface.
type MockTransactionServer struct {
	ctrl     *gomock.Controller
	recorder *MockTransactionServerMockRecorder
}

// MockTransactionServerMockRecorder is the mock recorder for MockTransactionServer.
type MockTransactionServerMockRecorder struct {
	mock *MockTransactionServer
}

// NewMockTransactionServer creates a new mock instance.
func NewMockTransactionServer(ctrl *gomock.Controller) *MockTransactionServer {
	mock := &MockTransactionServer{ctrl: ctrl}
	mock.recorder = &MockTransactionServerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockTransactionServer) EXPECT() *MockTransactionServerMockRecorder {
	return m.recorder
}

// BillPayOut mocks base method.
func (m *MockTransactionServer) BillPayOut(arg0 context.Context, arg1 *grpc.BillPayoutRequest) (*emptypb.Empty, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BillPayOut", arg0, arg1)
	ret0, _ := ret[0].(*emptypb.Empty)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BillPayOut indicates an expected call of BillPayOut.
func (mr *MockTransactionServerMockRecorder) BillPayOut(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BillPayOut", reflect.TypeOf((*MockTransactionServer)(nil).BillPayOut), arg0, arg1)
}

// CalculateAndUpdateTransactionAmount mocks base method.
func (m *MockTransactionServer) CalculateAndUpdateTransactionAmount(arg0 context.Context, arg1 *grpc.CalculateAndUpdateTransactionAmountRequest) (*emptypb.Empty, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CalculateAndUpdateTransactionAmount", arg0, arg1)
	ret0, _ := ret[0].(*emptypb.Empty)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CalculateAndUpdateTransactionAmount indicates an expected call of CalculateAndUpdateTransactionAmount.
func (mr *MockTransactionServerMockRecorder) CalculateAndUpdateTransactionAmount(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CalculateAndUpdateTransactionAmount", reflect.TypeOf((*MockTransactionServer)(nil).CalculateAndUpdateTransactionAmount), arg0, arg1)
}

// CheckTransactionHash mocks base method.
func (m *MockTransactionServer) CheckTransactionHash(arg0 context.Context, arg1 *grpc.CheckTransactionHashRequestV1) (*emptypb.Empty, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CheckTransactionHash", arg0, arg1)
	ret0, _ := ret[0].(*emptypb.Empty)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CheckTransactionHash indicates an expected call of CheckTransactionHash.
func (mr *MockTransactionServerMockRecorder) CheckTransactionHash(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CheckTransactionHash", reflect.TypeOf((*MockTransactionServer)(nil).CheckTransactionHash), arg0, arg1)
}

// CreateTransactionByPhone mocks base method.
func (m *MockTransactionServer) CreateTransactionByPhone(arg0 context.Context, arg1 *grpc.CreateTransactionByPhoneRequest) (*grpc.CreateTransactionByPhoneResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateTransactionByPhone", arg0, arg1)
	ret0, _ := ret[0].(*grpc.CreateTransactionByPhoneResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateTransactionByPhone indicates an expected call of CreateTransactionByPhone.
func (mr *MockTransactionServerMockRecorder) CreateTransactionByPhone(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateTransactionByPhone", reflect.TypeOf((*MockTransactionServer)(nil).CreateTransactionByPhone), arg0, arg1)
}

// GetByIDWithType mocks base method.
func (m *MockTransactionServer) GetByIDWithType(arg0 context.Context, arg1 *grpc.GetByIDWithTypeRequest) (*grpc.GetByIDWithTypeResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetByIDWithType", arg0, arg1)
	ret0, _ := ret[0].(*grpc.GetByIDWithTypeResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetByIDWithType indicates an expected call of GetByIDWithType.
func (mr *MockTransactionServerMockRecorder) GetByIDWithType(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetByIDWithType", reflect.TypeOf((*MockTransactionServer)(nil).GetByIDWithType), arg0, arg1)
}

// GetPayInTransactionsByPeriodAndAcquirer mocks base method.
func (m *MockTransactionServer) GetPayInTransactionsByPeriodAndAcquirer(arg0 context.Context, arg1 *grpc.GetPayInTransactionsByPeriodAndAcquirerReqV1) (*grpc.GetTransactionsResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetPayInTransactionsByPeriodAndAcquirer", arg0, arg1)
	ret0, _ := ret[0].(*grpc.GetTransactionsResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetPayInTransactionsByPeriodAndAcquirer indicates an expected call of GetPayInTransactionsByPeriodAndAcquirer.
func (mr *MockTransactionServerMockRecorder) GetPayInTransactionsByPeriodAndAcquirer(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetPayInTransactionsByPeriodAndAcquirer", reflect.TypeOf((*MockTransactionServer)(nil).GetPayInTransactionsByPeriodAndAcquirer), arg0, arg1)
}

// GetTransactionByID mocks base method.
func (m *MockTransactionServer) GetTransactionByID(arg0 context.Context, arg1 *grpc.GetTransactionByIDRequestV1) (*grpc.TransactionDataV1, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetTransactionByID", arg0, arg1)
	ret0, _ := ret[0].(*grpc.TransactionDataV1)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetTransactionByID indicates an expected call of GetTransactionByID.
func (mr *MockTransactionServerMockRecorder) GetTransactionByID(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetTransactionByID", reflect.TypeOf((*MockTransactionServer)(nil).GetTransactionByID), arg0, arg1)
}

// GetTransactionTypeByID mocks base method.
func (m *MockTransactionServer) GetTransactionTypeByID(arg0 context.Context, arg1 *grpc.GetTransactionTypeByIDRequestV1) (*grpc.GetTransactionTypeByIDResponseV1, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetTransactionTypeByID", arg0, arg1)
	ret0, _ := ret[0].(*grpc.GetTransactionTypeByIDResponseV1)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetTransactionTypeByID indicates an expected call of GetTransactionTypeByID.
func (mr *MockTransactionServerMockRecorder) GetTransactionTypeByID(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetTransactionTypeByID", reflect.TypeOf((*MockTransactionServer)(nil).GetTransactionTypeByID), arg0, arg1)
}

// GetTransactionsByFinalStatusAndPeriodWithLimit mocks base method.
func (m *MockTransactionServer) GetTransactionsByFinalStatusAndPeriodWithLimit(arg0 context.Context, arg1 *grpc.GetTransactionsByFinalStatusAndPeriodWithLimitReqV1) (*grpc.GetTransactionsResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetTransactionsByFinalStatusAndPeriodWithLimit", arg0, arg1)
	ret0, _ := ret[0].(*grpc.GetTransactionsResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetTransactionsByFinalStatusAndPeriodWithLimit indicates an expected call of GetTransactionsByFinalStatusAndPeriodWithLimit.
func (mr *MockTransactionServerMockRecorder) GetTransactionsByFinalStatusAndPeriodWithLimit(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetTransactionsByFinalStatusAndPeriodWithLimit", reflect.TypeOf((*MockTransactionServer)(nil).GetTransactionsByFinalStatusAndPeriodWithLimit), arg0, arg1)
}

// GetTransactionsByPeriodAndCallbackStatus mocks base method.
func (m *MockTransactionServer) GetTransactionsByPeriodAndCallbackStatus(arg0 context.Context, arg1 *grpc.GetTransactionsByCallbackStatusReqV1) (*grpc.GetTransactionsResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetTransactionsByPeriodAndCallbackStatus", arg0, arg1)
	ret0, _ := ret[0].(*grpc.GetTransactionsResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetTransactionsByPeriodAndCallbackStatus indicates an expected call of GetTransactionsByPeriodAndCallbackStatus.
func (mr *MockTransactionServerMockRecorder) GetTransactionsByPeriodAndCallbackStatus(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetTransactionsByPeriodAndCallbackStatus", reflect.TypeOf((*MockTransactionServer)(nil).GetTransactionsByPeriodAndCallbackStatus), arg0, arg1)
}

// GetTransactionsByPeriodAndStatus mocks base method.
func (m *MockTransactionServer) GetTransactionsByPeriodAndStatus(arg0 context.Context, arg1 *grpc.GetTransactionsByStatusReqV1) (*grpc.GetTransactionsResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetTransactionsByPeriodAndStatus", arg0, arg1)
	ret0, _ := ret[0].(*grpc.GetTransactionsResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetTransactionsByPeriodAndStatus indicates an expected call of GetTransactionsByPeriodAndStatus.
func (mr *MockTransactionServerMockRecorder) GetTransactionsByPeriodAndStatus(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetTransactionsByPeriodAndStatus", reflect.TypeOf((*MockTransactionServer)(nil).GetTransactionsByPeriodAndStatus), arg0, arg1)
}

// GetTransactionsByProjectInfo mocks base method.
func (m *MockTransactionServer) GetTransactionsByProjectInfo(arg0 context.Context, arg1 *grpc.GetTransactionsByProjectInfoRequestV1) (*grpc.GetTransactionsResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetTransactionsByProjectInfo", arg0, arg1)
	ret0, _ := ret[0].(*grpc.GetTransactionsResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetTransactionsByProjectInfo indicates an expected call of GetTransactionsByProjectInfo.
func (mr *MockTransactionServerMockRecorder) GetTransactionsByProjectInfo(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetTransactionsByProjectInfo", reflect.TypeOf((*MockTransactionServer)(nil).GetTransactionsByProjectInfo), arg0, arg1)
}

// IncreaseTryCount mocks base method.
func (m *MockTransactionServer) IncreaseTryCount(arg0 context.Context, arg1 *grpc.IncreaseTryCountRequest) (*emptypb.Empty, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "IncreaseTryCount", arg0, arg1)
	ret0, _ := ret[0].(*emptypb.Empty)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// IncreaseTryCount indicates an expected call of IncreaseTryCount.
func (mr *MockTransactionServerMockRecorder) IncreaseTryCount(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "IncreaseTryCount", reflect.TypeOf((*MockTransactionServer)(nil).IncreaseTryCount), arg0, arg1)
}

// MakeAutoCharge mocks base method.
func (m *MockTransactionServer) MakeAutoCharge(arg0 context.Context, arg1 *grpc.MakeAutoChargeRequestV1) (*emptypb.Empty, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "MakeAutoCharge", arg0, arg1)
	ret0, _ := ret[0].(*emptypb.Empty)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// MakeAutoCharge indicates an expected call of MakeAutoCharge.
func (mr *MockTransactionServerMockRecorder) MakeAutoCharge(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "MakeAutoCharge", reflect.TypeOf((*MockTransactionServer)(nil).MakeAutoCharge), arg0, arg1)
}

// SaveAcquirerResponse mocks base method.
func (m *MockTransactionServer) SaveAcquirerResponse(arg0 context.Context, arg1 *grpc.SaveAcquirerResponseRequest) (*emptypb.Empty, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SaveAcquirerResponse", arg0, arg1)
	ret0, _ := ret[0].(*emptypb.Empty)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SaveAcquirerResponse indicates an expected call of SaveAcquirerResponse.
func (mr *MockTransactionServerMockRecorder) SaveAcquirerResponse(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SaveAcquirerResponse", reflect.TypeOf((*MockTransactionServer)(nil).SaveAcquirerResponse), arg0, arg1)
}

// SendReceipt mocks base method.
func (m *MockTransactionServer) SendReceipt(arg0 context.Context, arg1 *grpc.SendRequest) (*emptypb.Empty, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SendReceipt", arg0, arg1)
	ret0, _ := ret[0].(*emptypb.Empty)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SendReceipt indicates an expected call of SendReceipt.
func (mr *MockTransactionServerMockRecorder) SendReceipt(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SendReceipt", reflect.TypeOf((*MockTransactionServer)(nil).SendReceipt), arg0, arg1)
}

// SetAdditionalData mocks base method.
func (m *MockTransactionServer) SetAdditionalData(arg0 context.Context, arg1 *grpc.SetAdditionalDataRequest) (*emptypb.Empty, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SetAdditionalData", arg0, arg1)
	ret0, _ := ret[0].(*emptypb.Empty)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SetAdditionalData indicates an expected call of SetAdditionalData.
func (mr *MockTransactionServerMockRecorder) SetAdditionalData(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetAdditionalData", reflect.TypeOf((*MockTransactionServer)(nil).SetAdditionalData), arg0, arg1)
}

// SetRefundWaitingStatus mocks base method.
func (m *MockTransactionServer) SetRefundWaitingStatus(arg0 context.Context, arg1 *grpc.SetRefundWaitingStatusRequestV1) (*grpc.SetRefundWaitingStatusResponseV1, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SetRefundWaitingStatus", arg0, arg1)
	ret0, _ := ret[0].(*grpc.SetRefundWaitingStatusResponseV1)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SetRefundWaitingStatus indicates an expected call of SetRefundWaitingStatus.
func (mr *MockTransactionServerMockRecorder) SetRefundWaitingStatus(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetRefundWaitingStatus", reflect.TypeOf((*MockTransactionServer)(nil).SetRefundWaitingStatus), arg0, arg1)
}

// UpdateCallbackStatus mocks base method.
func (m *MockTransactionServer) UpdateCallbackStatus(arg0 context.Context, arg1 *grpc.UpdateCallbackStatusRequestV1) (*emptypb.Empty, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateCallbackStatus", arg0, arg1)
	ret0, _ := ret[0].(*emptypb.Empty)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpdateCallbackStatus indicates an expected call of UpdateCallbackStatus.
func (mr *MockTransactionServerMockRecorder) UpdateCallbackStatus(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateCallbackStatus", reflect.TypeOf((*MockTransactionServer)(nil).UpdateCallbackStatus), arg0, arg1)
}

// UpdateStatus mocks base method.
func (m *MockTransactionServer) UpdateStatus(arg0 context.Context, arg1 *grpc.UpdateStatusRequest) (*emptypb.Empty, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateStatus", arg0, arg1)
	ret0, _ := ret[0].(*emptypb.Empty)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpdateStatus indicates an expected call of UpdateStatus.
func (mr *MockTransactionServerMockRecorder) UpdateStatus(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateStatus", reflect.TypeOf((*MockTransactionServer)(nil).UpdateStatus), arg0, arg1)
}

// UpdateTransactionStatus mocks base method.
func (m *MockTransactionServer) UpdateTransactionStatus(arg0 context.Context, arg1 *grpc.UpdateTransactionStatusRequestV1) (*emptypb.Empty, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateTransactionStatus", arg0, arg1)
	ret0, _ := ret[0].(*emptypb.Empty)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpdateTransactionStatus indicates an expected call of UpdateTransactionStatus.
func (mr *MockTransactionServerMockRecorder) UpdateTransactionStatus(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateTransactionStatus", reflect.TypeOf((*MockTransactionServer)(nil).UpdateTransactionStatus), arg0, arg1)
}

// mustEmbedUnimplementedTransactionServer mocks base method.
func (m *MockTransactionServer) mustEmbedUnimplementedTransactionServer() {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "mustEmbedUnimplementedTransactionServer")
}

// mustEmbedUnimplementedTransactionServer indicates an expected call of mustEmbedUnimplementedTransactionServer.
func (mr *MockTransactionServerMockRecorder) mustEmbedUnimplementedTransactionServer() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "mustEmbedUnimplementedTransactionServer", reflect.TypeOf((*MockTransactionServer)(nil).mustEmbedUnimplementedTransactionServer))
}

// MockUnsafeTransactionServer is a mock of UnsafeTransactionServer interface.
type MockUnsafeTransactionServer struct {
	ctrl     *gomock.Controller
	recorder *MockUnsafeTransactionServerMockRecorder
}

// MockUnsafeTransactionServerMockRecorder is the mock recorder for MockUnsafeTransactionServer.
type MockUnsafeTransactionServerMockRecorder struct {
	mock *MockUnsafeTransactionServer
}

// NewMockUnsafeTransactionServer creates a new mock instance.
func NewMockUnsafeTransactionServer(ctrl *gomock.Controller) *MockUnsafeTransactionServer {
	mock := &MockUnsafeTransactionServer{ctrl: ctrl}
	mock.recorder = &MockUnsafeTransactionServerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockUnsafeTransactionServer) EXPECT() *MockUnsafeTransactionServerMockRecorder {
	return m.recorder
}

// mustEmbedUnimplementedTransactionServer mocks base method.
func (m *MockUnsafeTransactionServer) mustEmbedUnimplementedTransactionServer() {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "mustEmbedUnimplementedTransactionServer")
}

// mustEmbedUnimplementedTransactionServer indicates an expected call of mustEmbedUnimplementedTransactionServer.
func (mr *MockUnsafeTransactionServerMockRecorder) mustEmbedUnimplementedTransactionServer() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "mustEmbedUnimplementedTransactionServer", reflect.TypeOf((*MockUnsafeTransactionServer)(nil).mustEmbedUnimplementedTransactionServer))
}
