// Code generated by protoc-gen-go-mvp. DO NOT EDIT.
// versions:
// - protoc-gen-go-mvp v2.0.1
// created by mvp authors

package grpc

import (
	math "math"
	strings "strings"
)

func (val JusanResponseCodeTokenize) Synonym() JusanResponseCodeTokenize {
	if _, ok := JusanResponseCodeTokenize_name[int32(val)]; ok {
		return val
	}

	return JusanResponseCodeTokenize(math.MinInt32)
}

func (val JusanResponseCodeTokenize) Int() int {
	return int(val.Synonym())
}

func (val JusanResponseCodeTokenize) IntPtr() *int {
	result := int(val.Synonym())
	return &result
}

func (val JusanResponseCodeTokenize) Int32() int32 {
	return int32(val.Synonym())
}

func (val JusanResponseCodeTokenize) Int32Ptr() *int32 {
	result := int32(val.Synonym())
	return &result
}

func (val JusanResponseCodeTokenize) Int64() int64 {
	return int64(val.Synonym())
}

func (val JusanResponseCodeTokenize) Int64Ptr() *int64 {
	result := int64(val.Synonym())
	return &result
}

func (val JusanResponseCodeTokenize) Uint() uint {
	return uint(val.Synonym())
}

func (val JusanResponseCodeTokenize) UintPtr() *uint {
	result := uint(val.Synonym())
	return &result
}

func (val JusanResponseCodeTokenize) Uint32() uint32 {
	return uint32(val.Synonym())
}

func (val JusanResponseCodeTokenize) Uint32Ptr() *uint32 {
	result := uint32(val.Synonym())
	return &result
}

func (val JusanResponseCodeTokenize) Uint64() uint64 {
	return uint64(val.Synonym())
}

func (val JusanResponseCodeTokenize) Uint64Ptr() *uint64 {
	result := uint64(val.Synonym())
	return &result
}

func (val JusanResponseCodeTokenize) IsKnown() bool {
	return val.Synonym() != JusanResponseCodeTokenize(math.MinInt32)
}

func ConvertIntToJusanResponseCodeTokenize(in int) JusanResponseCodeTokenize {
	return JusanResponseCodeTokenize(in).Synonym()
}

func ConvertUintToJusanResponseCodeTokenize(in uint) JusanResponseCodeTokenize {
	return JusanResponseCodeTokenize(in).Synonym()
}

func ConvertInt32ToJusanResponseCodeTokenize(in int32) JusanResponseCodeTokenize {
	return JusanResponseCodeTokenize(in).Synonym()
}

func ConvertUint32ToJusanResponseCodeTokenize(in uint32) JusanResponseCodeTokenize {
	return JusanResponseCodeTokenize(in).Synonym()
}

func ConvertInt64ToJusanResponseCodeTokenize(in int64) JusanResponseCodeTokenize {
	return JusanResponseCodeTokenize(in).Synonym()
}

func ConvertUint64ToJusanResponseCodeTokenize(in uint64) JusanResponseCodeTokenize {
	return JusanResponseCodeTokenize(in).Synonym()
}

var JusanResponseCodeTokenize_Lower_value = map[string]JusanResponseCodeTokenize{
	"11":   0,
	"12":   1,
	"13":   2,
	"14":   3,
	"15":   4,
	"16":   5,
	"171":  6,
	"172 ": 7,
	"18":   8,
	"19":   9,
	"20":   10,
	"21":   11,
	"22":   12,
	"23":   13,
	"24":   14,
	"25":   15,
	"26":   16,
	"27":   17,
	"28":   18,
	"29":   19,
	"30":   20,
	"31":   21,
	"32":   22,
	"33":   23,
	"34":   24,
	"99":   25,
	"41":   26,
	"42":   27,
	"43":   28,
	"44":   29,
	"17":   30,
	"45":   31,
	"46":   32,
	"47 ":  33,
	"48":   34,
	"50":   35,
	"51":   36,
	"52":   37,
	"53":   38,
	"f":    39,
	"e":    40,
	"c":    41,
	"411":  43,
}

func ConvertStringToJusanResponseCodeTokenize(in string) JusanResponseCodeTokenize {
	if result, ok := JusanResponseCodeTokenize_value[in]; ok {
		return JusanResponseCodeTokenize(result)
	}

	if result, ok := JusanResponseCodeTokenize_Lower_value[strings.ToLower(in)]; ok {
		return JusanResponseCodeTokenize(result)
	}

	return JusanResponseCodeTokenize(math.MinInt32)
}

var SliceJusanResponseCodeTokenizeConvert *sliceJusanResponseCodeTokenizeConvert

type sliceJusanResponseCodeTokenizeConvert struct{}

func (*sliceJusanResponseCodeTokenizeConvert) Synonym(in []JusanResponseCodeTokenize) []JusanResponseCodeTokenize {
	result := make([]JusanResponseCodeTokenize, len(in))
	for i, v := range in {
		result[i] = v.Synonym()
	}

	return result
}

func (*sliceJusanResponseCodeTokenizeConvert) Int32(in []JusanResponseCodeTokenize) []int32 {
	result := make([]int32, len(in))
	for i, v := range in {
		result[i] = v.Int32()
	}

	return result
}

func (*sliceJusanResponseCodeTokenizeConvert) Uint32(in []JusanResponseCodeTokenize) []uint32 {
	result := make([]uint32, len(in))
	for i, v := range in {
		result[i] = v.Uint32()
	}

	return result
}

func (*sliceJusanResponseCodeTokenizeConvert) Uint64(in []JusanResponseCodeTokenize) []uint64 {
	result := make([]uint64, len(in))
	for i, v := range in {
		result[i] = v.Uint64()
	}

	return result
}

func (*sliceJusanResponseCodeTokenizeConvert) Int64(in []JusanResponseCodeTokenize) []int64 {
	result := make([]int64, len(in))
	for i, v := range in {
		result[i] = v.Int64()
	}

	return result
}

func (*sliceJusanResponseCodeTokenizeConvert) Uint(in []JusanResponseCodeTokenize) []uint {
	result := make([]uint, len(in))
	for i, v := range in {
		result[i] = v.Uint()
	}

	return result
}

func (*sliceJusanResponseCodeTokenizeConvert) Int(in []JusanResponseCodeTokenize) []int {
	result := make([]int, len(in))
	for i, v := range in {
		result[i] = v.Int()
	}

	return result
}

func (*sliceJusanResponseCodeTokenizeConvert) IsKnown(in []JusanResponseCodeTokenize) []bool {
	result := make([]bool, len(in))
	for i, v := range in {
		result[i] = v.IsKnown()
	}

	return result
}

func (*sliceJusanResponseCodeTokenizeConvert) ConvertIntToJusanResponseCodeTokenize(in []int) []JusanResponseCodeTokenize {
	result := make([]JusanResponseCodeTokenize, len(in))
	for i, v := range in {
		result[i] = ConvertIntToJusanResponseCodeTokenize(v)
	}

	return result
}

func (*sliceJusanResponseCodeTokenizeConvert) ConvertUintToJusanResponseCodeTokenize(in []uint) []JusanResponseCodeTokenize {
	result := make([]JusanResponseCodeTokenize, len(in))
	for i, v := range in {
		result[i] = ConvertUintToJusanResponseCodeTokenize(v)
	}

	return result
}

func (*sliceJusanResponseCodeTokenizeConvert) ConvertInt32ToJusanResponseCodeTokenize(in []int32) []JusanResponseCodeTokenize {
	result := make([]JusanResponseCodeTokenize, len(in))
	for i, v := range in {
		result[i] = ConvertInt32ToJusanResponseCodeTokenize(v)
	}

	return result
}

func (*sliceJusanResponseCodeTokenizeConvert) ConvertUint32ToJusanResponseCodeTokenize(in []uint32) []JusanResponseCodeTokenize {
	result := make([]JusanResponseCodeTokenize, len(in))
	for i, v := range in {
		result[i] = ConvertUint32ToJusanResponseCodeTokenize(v)
	}

	return result
}

func (*sliceJusanResponseCodeTokenizeConvert) ConvertInt64ToJusanResponseCodeTokenize(in []int64) []JusanResponseCodeTokenize {
	result := make([]JusanResponseCodeTokenize, len(in))
	for i, v := range in {
		result[i] = ConvertInt64ToJusanResponseCodeTokenize(v)
	}

	return result
}

func (*sliceJusanResponseCodeTokenizeConvert) ConvertUint64ToJusanResponseCodeTokenize(in []uint64) []JusanResponseCodeTokenize {
	result := make([]JusanResponseCodeTokenize, len(in))
	for i, v := range in {
		result[i] = ConvertUint64ToJusanResponseCodeTokenize(v)
	}

	return result
}

func (*sliceJusanResponseCodeTokenizeConvert) ConvertStringToJusanResponseCodeTokenize(in []string) []JusanResponseCodeTokenize {
	result := make([]JusanResponseCodeTokenize, len(in))
	for i, v := range in {
		result[i] = ConvertStringToJusanResponseCodeTokenize(v)
	}

	return result
}

func NewJusanResponseCodeTokenizeUsage() *JusanResponseCodeTokenizeUsage {
	return &JusanResponseCodeTokenizeUsage{
		enumMap: map[JusanResponseCodeTokenize]bool{
			JusanResponseCodeTokenize_TokenizeServiceUnavailable:         false,
			JusanResponseCodeTokenize_TokenizeIncorrectFieldOrder:        false,
			JusanResponseCodeTokenize_TokenizeIncorrectAmount:            false,
			JusanResponseCodeTokenize_TokenizeIncorrectCurrency:          false,
			JusanResponseCodeTokenize_TokenizeUnavailableMPI:             false,
			JusanResponseCodeTokenize_TokenizeUnavailableDb:              false,
			JusanResponseCodeTokenize_TokenizeOperationForbidden:         false,
			JusanResponseCodeTokenize_TokenizeOperationForbiddenByLaw:    false,
			JusanResponseCodeTokenize_TokenizeRequestAlreadyCompleted:    false,
			JusanResponseCodeTokenize_TokenizeIncorrectCardExpDate:       false,
			JusanResponseCodeTokenize_TokenizeIncorrectFieldTerminal:     false,
			JusanResponseCodeTokenize_TokenizeIncorrectSign:              false,
			JusanResponseCodeTokenize_TokenizeCurrencyNotFound:           false,
			JusanResponseCodeTokenize_TokenizeLimitExceeded:              false,
			JusanResponseCodeTokenize_TokenizeEmptyField:                 false,
			JusanResponseCodeTokenize_TokenizeFieldSizeLessSymbols:       false,
			JusanResponseCodeTokenize_TokenizeFieldSizeMoreSymbols:       false,
			JusanResponseCodeTokenize_TokenizeInvalidField:               false,
			JusanResponseCodeTokenize_TokenizeMPIError3DS:                false,
			JusanResponseCodeTokenize_TokenizeUnacceptableCardType:       false,
			JusanResponseCodeTokenize_TokenizePaymentNotFound:            false,
			JusanResponseCodeTokenize_TokenizeClientKeyNotFound:          false,
			JusanResponseCodeTokenize_TokenizeForbidden:                  false,
			JusanResponseCodeTokenize_TokenizeTokenNotFound:              false,
			JusanResponseCodeTokenize_TokenizeIncorrectBlockAmount:       false,
			JusanResponseCodeTokenize_TokenizeUnknownError:               false,
			JusanResponseCodeTokenize_TokenizeServiceUnavailableTryLater: false,
			JusanResponseCodeTokenize_TokenizeInvalidAmount:              false,
			JusanResponseCodeTokenize_TokenizeServiceDbUnavailable:       false,
			JusanResponseCodeTokenize_TokenizeIncorrectFieldMerchant:     false,
			JusanResponseCodeTokenize_TokenizeMerchantNotFound:           false,
			JusanResponseCodeTokenize_TokenizeOrderRequestNotFound:       false,
			JusanResponseCodeTokenize_TokenizeInvalidSign:                false,
			JusanResponseCodeTokenize_TokenizeIncorrectRefundSum:         false,
			JusanResponseCodeTokenize_TokenizeIncorrectStatus:            false,
			JusanResponseCodeTokenize_TokenizeIncorrectValue:             false,
			JusanResponseCodeTokenize_TokenizeIncorrectTerminalStatus:    false,
			JusanResponseCodeTokenize_TokenizeForbiddenOperation:         false,
			JusanResponseCodeTokenize_TokenizeDuplicateDescription:       false,
			JusanResponseCodeTokenize_TokenizeRefundError:                false,
			JusanResponseCodeTokenize_TokenizePayError:                   false,
			JusanResponseCodeTokenize_TokenizePaymentExpired:             false,
			JusanResponseCodeTokenize_TokenizeTransactionHandleError:     false,
		},
	}
}

func IsJusanResponseCodeTokenize(target JusanResponseCodeTokenize, matches ...JusanResponseCodeTokenize) bool {
	for _, v := range matches {
		if target == v {
			return true
		}
	}

	return false
}

type JusanResponseCodeTokenizeUsage struct {
	enumMap map[JusanResponseCodeTokenize]bool
}

func (u *JusanResponseCodeTokenizeUsage) Use(slice ...JusanResponseCodeTokenize) *JusanResponseCodeTokenizeUsage {
	for _, in := range slice {
		u.enumMap[in] = true
	}

	return u
}

func (u *JusanResponseCodeTokenizeUsage) Check() []string {
	var result []string
	for k, v := range u.enumMap {
		if v {
			continue
		}

		result = append(result, k.String())
	}

	return result
}

func (u *JusanResponseCodeTokenizeUsage) JusanResponseCodeTokenize_TokenizeServiceUnavailable() JusanResponseCodeTokenize {
	u.Use(JusanResponseCodeTokenize_TokenizeServiceUnavailable)
	return JusanResponseCodeTokenize_TokenizeServiceUnavailable
}

func (u *JusanResponseCodeTokenizeUsage) JusanResponseCodeTokenize_TokenizeIncorrectFieldOrder() JusanResponseCodeTokenize {
	u.Use(JusanResponseCodeTokenize_TokenizeIncorrectFieldOrder)
	return JusanResponseCodeTokenize_TokenizeIncorrectFieldOrder
}

func (u *JusanResponseCodeTokenizeUsage) JusanResponseCodeTokenize_TokenizeIncorrectAmount() JusanResponseCodeTokenize {
	u.Use(JusanResponseCodeTokenize_TokenizeIncorrectAmount)
	return JusanResponseCodeTokenize_TokenizeIncorrectAmount
}

func (u *JusanResponseCodeTokenizeUsage) JusanResponseCodeTokenize_TokenizeIncorrectCurrency() JusanResponseCodeTokenize {
	u.Use(JusanResponseCodeTokenize_TokenizeIncorrectCurrency)
	return JusanResponseCodeTokenize_TokenizeIncorrectCurrency
}

func (u *JusanResponseCodeTokenizeUsage) JusanResponseCodeTokenize_TokenizeUnavailableMPI() JusanResponseCodeTokenize {
	u.Use(JusanResponseCodeTokenize_TokenizeUnavailableMPI)
	return JusanResponseCodeTokenize_TokenizeUnavailableMPI
}

func (u *JusanResponseCodeTokenizeUsage) JusanResponseCodeTokenize_TokenizeUnavailableDb() JusanResponseCodeTokenize {
	u.Use(JusanResponseCodeTokenize_TokenizeUnavailableDb)
	return JusanResponseCodeTokenize_TokenizeUnavailableDb
}

func (u *JusanResponseCodeTokenizeUsage) JusanResponseCodeTokenize_TokenizeOperationForbidden() JusanResponseCodeTokenize {
	u.Use(JusanResponseCodeTokenize_TokenizeOperationForbidden)
	return JusanResponseCodeTokenize_TokenizeOperationForbidden
}

func (u *JusanResponseCodeTokenizeUsage) JusanResponseCodeTokenize_TokenizeOperationForbiddenByLaw() JusanResponseCodeTokenize {
	u.Use(JusanResponseCodeTokenize_TokenizeOperationForbiddenByLaw)
	return JusanResponseCodeTokenize_TokenizeOperationForbiddenByLaw
}

func (u *JusanResponseCodeTokenizeUsage) JusanResponseCodeTokenize_TokenizeRequestAlreadyCompleted() JusanResponseCodeTokenize {
	u.Use(JusanResponseCodeTokenize_TokenizeRequestAlreadyCompleted)
	return JusanResponseCodeTokenize_TokenizeRequestAlreadyCompleted
}

func (u *JusanResponseCodeTokenizeUsage) JusanResponseCodeTokenize_TokenizeIncorrectCardExpDate() JusanResponseCodeTokenize {
	u.Use(JusanResponseCodeTokenize_TokenizeIncorrectCardExpDate)
	return JusanResponseCodeTokenize_TokenizeIncorrectCardExpDate
}

func (u *JusanResponseCodeTokenizeUsage) JusanResponseCodeTokenize_TokenizeIncorrectFieldTerminal() JusanResponseCodeTokenize {
	u.Use(JusanResponseCodeTokenize_TokenizeIncorrectFieldTerminal)
	return JusanResponseCodeTokenize_TokenizeIncorrectFieldTerminal
}

func (u *JusanResponseCodeTokenizeUsage) JusanResponseCodeTokenize_TokenizeIncorrectSign() JusanResponseCodeTokenize {
	u.Use(JusanResponseCodeTokenize_TokenizeIncorrectSign)
	return JusanResponseCodeTokenize_TokenizeIncorrectSign
}

func (u *JusanResponseCodeTokenizeUsage) JusanResponseCodeTokenize_TokenizeCurrencyNotFound() JusanResponseCodeTokenize {
	u.Use(JusanResponseCodeTokenize_TokenizeCurrencyNotFound)
	return JusanResponseCodeTokenize_TokenizeCurrencyNotFound
}

func (u *JusanResponseCodeTokenizeUsage) JusanResponseCodeTokenize_TokenizeLimitExceeded() JusanResponseCodeTokenize {
	u.Use(JusanResponseCodeTokenize_TokenizeLimitExceeded)
	return JusanResponseCodeTokenize_TokenizeLimitExceeded
}

func (u *JusanResponseCodeTokenizeUsage) JusanResponseCodeTokenize_TokenizeEmptyField() JusanResponseCodeTokenize {
	u.Use(JusanResponseCodeTokenize_TokenizeEmptyField)
	return JusanResponseCodeTokenize_TokenizeEmptyField
}

func (u *JusanResponseCodeTokenizeUsage) JusanResponseCodeTokenize_TokenizeFieldSizeLessSymbols() JusanResponseCodeTokenize {
	u.Use(JusanResponseCodeTokenize_TokenizeFieldSizeLessSymbols)
	return JusanResponseCodeTokenize_TokenizeFieldSizeLessSymbols
}

func (u *JusanResponseCodeTokenizeUsage) JusanResponseCodeTokenize_TokenizeFieldSizeMoreSymbols() JusanResponseCodeTokenize {
	u.Use(JusanResponseCodeTokenize_TokenizeFieldSizeMoreSymbols)
	return JusanResponseCodeTokenize_TokenizeFieldSizeMoreSymbols
}

func (u *JusanResponseCodeTokenizeUsage) JusanResponseCodeTokenize_TokenizeInvalidField() JusanResponseCodeTokenize {
	u.Use(JusanResponseCodeTokenize_TokenizeInvalidField)
	return JusanResponseCodeTokenize_TokenizeInvalidField
}

func (u *JusanResponseCodeTokenizeUsage) JusanResponseCodeTokenize_TokenizeMPIError3DS() JusanResponseCodeTokenize {
	u.Use(JusanResponseCodeTokenize_TokenizeMPIError3DS)
	return JusanResponseCodeTokenize_TokenizeMPIError3DS
}

func (u *JusanResponseCodeTokenizeUsage) JusanResponseCodeTokenize_TokenizeUnacceptableCardType() JusanResponseCodeTokenize {
	u.Use(JusanResponseCodeTokenize_TokenizeUnacceptableCardType)
	return JusanResponseCodeTokenize_TokenizeUnacceptableCardType
}

func (u *JusanResponseCodeTokenizeUsage) JusanResponseCodeTokenize_TokenizePaymentNotFound() JusanResponseCodeTokenize {
	u.Use(JusanResponseCodeTokenize_TokenizePaymentNotFound)
	return JusanResponseCodeTokenize_TokenizePaymentNotFound
}

func (u *JusanResponseCodeTokenizeUsage) JusanResponseCodeTokenize_TokenizeClientKeyNotFound() JusanResponseCodeTokenize {
	u.Use(JusanResponseCodeTokenize_TokenizeClientKeyNotFound)
	return JusanResponseCodeTokenize_TokenizeClientKeyNotFound
}

func (u *JusanResponseCodeTokenizeUsage) JusanResponseCodeTokenize_TokenizeForbidden() JusanResponseCodeTokenize {
	u.Use(JusanResponseCodeTokenize_TokenizeForbidden)
	return JusanResponseCodeTokenize_TokenizeForbidden
}

func (u *JusanResponseCodeTokenizeUsage) JusanResponseCodeTokenize_TokenizeTokenNotFound() JusanResponseCodeTokenize {
	u.Use(JusanResponseCodeTokenize_TokenizeTokenNotFound)
	return JusanResponseCodeTokenize_TokenizeTokenNotFound
}

func (u *JusanResponseCodeTokenizeUsage) JusanResponseCodeTokenize_TokenizeIncorrectBlockAmount() JusanResponseCodeTokenize {
	u.Use(JusanResponseCodeTokenize_TokenizeIncorrectBlockAmount)
	return JusanResponseCodeTokenize_TokenizeIncorrectBlockAmount
}

func (u *JusanResponseCodeTokenizeUsage) JusanResponseCodeTokenize_TokenizeUnknownError() JusanResponseCodeTokenize {
	u.Use(JusanResponseCodeTokenize_TokenizeUnknownError)
	return JusanResponseCodeTokenize_TokenizeUnknownError
}

func (u *JusanResponseCodeTokenizeUsage) JusanResponseCodeTokenize_TokenizeServiceUnavailableTryLater() JusanResponseCodeTokenize {
	u.Use(JusanResponseCodeTokenize_TokenizeServiceUnavailableTryLater)
	return JusanResponseCodeTokenize_TokenizeServiceUnavailableTryLater
}

func (u *JusanResponseCodeTokenizeUsage) JusanResponseCodeTokenize_TokenizeInvalidAmount() JusanResponseCodeTokenize {
	u.Use(JusanResponseCodeTokenize_TokenizeInvalidAmount)
	return JusanResponseCodeTokenize_TokenizeInvalidAmount
}

func (u *JusanResponseCodeTokenizeUsage) JusanResponseCodeTokenize_TokenizeServiceDbUnavailable() JusanResponseCodeTokenize {
	u.Use(JusanResponseCodeTokenize_TokenizeServiceDbUnavailable)
	return JusanResponseCodeTokenize_TokenizeServiceDbUnavailable
}

func (u *JusanResponseCodeTokenizeUsage) JusanResponseCodeTokenize_TokenizeIncorrectFieldMerchant() JusanResponseCodeTokenize {
	u.Use(JusanResponseCodeTokenize_TokenizeIncorrectFieldMerchant)
	return JusanResponseCodeTokenize_TokenizeIncorrectFieldMerchant
}

func (u *JusanResponseCodeTokenizeUsage) JusanResponseCodeTokenize_TokenizeMerchantNotFound() JusanResponseCodeTokenize {
	u.Use(JusanResponseCodeTokenize_TokenizeMerchantNotFound)
	return JusanResponseCodeTokenize_TokenizeMerchantNotFound
}

func (u *JusanResponseCodeTokenizeUsage) JusanResponseCodeTokenize_TokenizeOrderRequestNotFound() JusanResponseCodeTokenize {
	u.Use(JusanResponseCodeTokenize_TokenizeOrderRequestNotFound)
	return JusanResponseCodeTokenize_TokenizeOrderRequestNotFound
}

func (u *JusanResponseCodeTokenizeUsage) JusanResponseCodeTokenize_TokenizeInvalidSign() JusanResponseCodeTokenize {
	u.Use(JusanResponseCodeTokenize_TokenizeInvalidSign)
	return JusanResponseCodeTokenize_TokenizeInvalidSign
}

func (u *JusanResponseCodeTokenizeUsage) JusanResponseCodeTokenize_TokenizeIncorrectRefundSum() JusanResponseCodeTokenize {
	u.Use(JusanResponseCodeTokenize_TokenizeIncorrectRefundSum)
	return JusanResponseCodeTokenize_TokenizeIncorrectRefundSum
}

func (u *JusanResponseCodeTokenizeUsage) JusanResponseCodeTokenize_TokenizeIncorrectStatus() JusanResponseCodeTokenize {
	u.Use(JusanResponseCodeTokenize_TokenizeIncorrectStatus)
	return JusanResponseCodeTokenize_TokenizeIncorrectStatus
}

func (u *JusanResponseCodeTokenizeUsage) JusanResponseCodeTokenize_TokenizeIncorrectValue() JusanResponseCodeTokenize {
	u.Use(JusanResponseCodeTokenize_TokenizeIncorrectValue)
	return JusanResponseCodeTokenize_TokenizeIncorrectValue
}

func (u *JusanResponseCodeTokenizeUsage) JusanResponseCodeTokenize_TokenizeIncorrectTerminalStatus() JusanResponseCodeTokenize {
	u.Use(JusanResponseCodeTokenize_TokenizeIncorrectTerminalStatus)
	return JusanResponseCodeTokenize_TokenizeIncorrectTerminalStatus
}

func (u *JusanResponseCodeTokenizeUsage) JusanResponseCodeTokenize_TokenizeForbiddenOperation() JusanResponseCodeTokenize {
	u.Use(JusanResponseCodeTokenize_TokenizeForbiddenOperation)
	return JusanResponseCodeTokenize_TokenizeForbiddenOperation
}

func (u *JusanResponseCodeTokenizeUsage) JusanResponseCodeTokenize_TokenizeDuplicateDescription() JusanResponseCodeTokenize {
	u.Use(JusanResponseCodeTokenize_TokenizeDuplicateDescription)
	return JusanResponseCodeTokenize_TokenizeDuplicateDescription
}

func (u *JusanResponseCodeTokenizeUsage) JusanResponseCodeTokenize_TokenizeRefundError() JusanResponseCodeTokenize {
	u.Use(JusanResponseCodeTokenize_TokenizeRefundError)
	return JusanResponseCodeTokenize_TokenizeRefundError
}

func (u *JusanResponseCodeTokenizeUsage) JusanResponseCodeTokenize_TokenizePayError() JusanResponseCodeTokenize {
	u.Use(JusanResponseCodeTokenize_TokenizePayError)
	return JusanResponseCodeTokenize_TokenizePayError
}

func (u *JusanResponseCodeTokenizeUsage) JusanResponseCodeTokenize_TokenizePaymentExpired() JusanResponseCodeTokenize {
	u.Use(JusanResponseCodeTokenize_TokenizePaymentExpired)
	return JusanResponseCodeTokenize_TokenizePaymentExpired
}

func (u *JusanResponseCodeTokenizeUsage) JusanResponseCodeTokenize_TokenizeTransactionHandleError() JusanResponseCodeTokenize {
	u.Use(JusanResponseCodeTokenize_TokenizeTransactionHandleError)
	return JusanResponseCodeTokenize_TokenizeTransactionHandleError
}
