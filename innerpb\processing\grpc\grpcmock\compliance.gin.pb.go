// Code generated by MockGen. DO NOT EDIT.
// Source: compliance.gin.pb.go

// Package grpcmock is a generated GoMock package.
package grpcmock

import (
	reflect "reflect"

	gin "github.com/gin-gonic/gin"
	gomock "github.com/golang/mock/gomock"
)

// MockGinComplianceServer is a mock of GinComplianceServer interface.
type MockGinComplianceServer struct {
	ctrl     *gomock.Controller
	recorder *MockGinComplianceServerMockRecorder
}

// MockGinComplianceServerMockRecorder is the mock recorder for MockGinComplianceServer.
type MockGinComplianceServerMockRecorder struct {
	mock *MockGinComplianceServer
}

// NewMockGinComplianceServer creates a new mock instance.
func NewMockGinComplianceServer(ctrl *gomock.Controller) *MockGinComplianceServer {
	mock := &MockGinComplianceServer{ctrl: ctrl}
	mock.recorder = &MockGinComplianceServerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockGinComplianceServer) EXPECT() *MockGinComplianceServerMockRecorder {
	return m.recorder
}

// UpdateSanctionFinanciersList mocks base method.
func (m *MockGinComplianceServer) UpdateSanctionFinanciersList(c *gin.Context) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateSanctionFinanciersList", c)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpdateSanctionFinanciersList indicates an expected call of UpdateSanctionFinanciersList.
func (mr *MockGinComplianceServerMockRecorder) UpdateSanctionFinanciersList(c interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateSanctionFinanciersList", reflect.TypeOf((*MockGinComplianceServer)(nil).UpdateSanctionFinanciersList), c)
}

// UpdateSanctionInvolvedList mocks base method.
func (m *MockGinComplianceServer) UpdateSanctionInvolvedList(c *gin.Context) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateSanctionInvolvedList", c)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpdateSanctionInvolvedList indicates an expected call of UpdateSanctionInvolvedList.
func (mr *MockGinComplianceServerMockRecorder) UpdateSanctionInvolvedList(c interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateSanctionInvolvedList", reflect.TypeOf((*MockGinComplianceServer)(nil).UpdateSanctionInvolvedList), c)
}

// UpdateSanctionUNSCList mocks base method.
func (m *MockGinComplianceServer) UpdateSanctionUNSCList(c *gin.Context) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateSanctionUNSCList", c)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpdateSanctionUNSCList indicates an expected call of UpdateSanctionUNSCList.
func (mr *MockGinComplianceServerMockRecorder) UpdateSanctionUNSCList(c interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateSanctionUNSCList", reflect.TypeOf((*MockGinComplianceServer)(nil).UpdateSanctionUNSCList), c)
}

// UpdateSanctionWMDList mocks base method.
func (m *MockGinComplianceServer) UpdateSanctionWMDList(c *gin.Context) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateSanctionWMDList", c)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpdateSanctionWMDList indicates an expected call of UpdateSanctionWMDList.
func (mr *MockGinComplianceServerMockRecorder) UpdateSanctionWMDList(c interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateSanctionWMDList", reflect.TypeOf((*MockGinComplianceServer)(nil).UpdateSanctionWMDList), c)
}
