package repository

import (
	"context"
	"errors"
	"git.local/sensitive/innerpb/processing/goerr"
	"git.local/sensitive/pkg/gtransaction"
	"git.local/sensitive/processing/account/model"
	"git.local/sensitive/processing/account/schema"
	"git.local/sensitive/sdk/dog"
	"gorm.io/gorm"
	"time"
)

type TransferManageDB struct {
	db *gorm.DB
}

func NewTransferManageDB(db *gorm.DB) TransferManager {
	return &TransferManageDB{
		db: db,
	}
}

func (tm *TransferManageDB) Create(ctx context.Context, transfer model.Transfer) (_ *model.Transfer, err error) {
	ctx, span := dog.CreateSpan(ctx, "TransferManageDB_Create")
	defer span.End()

	db := gtransaction.FromContextOrFallback(ctx, tm.db)

	if err = db.WithContext(ctx).Create(&transfer).Error; err != nil {
		return nil, goerr.ErrTransferCreation.WithErr(err).WithCtx(ctx)
	}

	return &transfer, nil
}

func (tm *TransferManageDB) UpdateStatus(ctx context.Context, transferID, statusID uint64, finishedAt *time.Time) (err error) {
	ctx, span := dog.CreateSpan(ctx, "TransferManageDB_UpdateStatus")
	defer span.End()

	updateTransfer := model.Transfer{
		StatusID: statusID,
	}

	if finishedAt != nil {
		updateTransfer.FinishedAt = finishedAt
	}

	transferStatus := new(model.TransferStatus)

	if err = tm.db.Where("id = ?", statusID).First(transferStatus).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return goerr.ErrTransferStatusNotFound.WithErr(err).WithCtx(ctx)
		}

		return goerr.ErrDbUnexpected.WithErr(err).WithCtx(ctx)
	}

	if err = tm.db.
		Model(&model.Transfer{}).
		Where(`id = ?`, transferID).
		Updates(&updateTransfer).
		Error; err != nil {
		return goerr.ErrDbUnexpected.WithErr(err).WithCtx(ctx)
	}

	return nil
}

func (tm *TransferManageDB) UpdateTransfer(ctx context.Context, transfer *schema.CreateTransferResponse) (err error) {
	ctx, span := dog.CreateSpan(ctx, "TransferManageDB_UpdateTransfer")
	defer span.End()

	updateTransfer := model.Transfer{
		BankReferenceID: transfer.BankReferenceID,
		BankOrderID:     transfer.BankOrderID,
	}

	if err = tm.db.
		Model(&model.Transfer{}).
		Where(`id = ?`, transfer.TransferID).
		Updates(&updateTransfer).
		Error; err != nil {
		return goerr.ErrDbUnexpected.WithErr(err).WithCtx(ctx)
	}

	return nil
}
