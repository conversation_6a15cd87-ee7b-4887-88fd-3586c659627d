// Code generated by protoc-gen-go-mvp. DO NOT EDIT.
// versions:
// - protoc-gen-go-mvp v2.0.1
// created by mvp authors

package grpc

import (
	dog "git.local/sensitive/sdk/dog"
	gin "github.com/gin-gonic/gin"
	metadata "google.golang.org/grpc/metadata"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
	http "net/http"
)

func RegisterGinReportMerchantWorkerRouterGroup(
	in *gin.RouterGroup,
	handler func(func(c *gin.Context) error) gin.HandlerFunc,
) error {
	service, err := NewGinReportMerchantWorkerService()
	if err != nil {
		return err
	}

	routerGroup := in.Group("processing.report_merchant.report_merchant_worker.ReportMerchantWorker")
	routerGroup.PUT("/ProcessReportScheduleByPeriodType", handler(service.ProcessReportScheduleByPeriodType))
	return nil
}

func NewGinReportMerchantWorkerService() (GinReportMerchantWorkerServer, error) {
	client, err := NewPreparedReportMerchantWorkerClient()
	if err != nil {
		return nil, err
	}

	return &ginReportMerchantWorkerServer{
		client: NewLoggedReportMerchantWorkerClient(
			NewIamReportMerchantWorkerClient(client),
		),
	}, nil
}

//go:generate mockgen -destination=./grpcmock/report_merchant_worker.gin.pb.go -package=grpcmock -source=report_merchant_worker.gin.pb.go GinReportMerchantWorkerServer
type GinReportMerchantWorkerServer interface {
	ProcessReportScheduleByPeriodType(c *gin.Context) error
}

var _ GinReportMerchantWorkerServer = (*ginReportMerchantWorkerServer)(nil)

type ginReportMerchantWorkerServer struct {
	client ReportMerchantWorkerClient
}

type ReportMerchantWorker_ProcessReportScheduleByPeriodType_Success struct {
	// Status is the status of the response. Set true
	Status bool `json:"status"`
	// Message is a human-readable explanation of the status
	Message string `json:"message"`
	// StatusCode is the GoErr status. Set 0
	StatusCode int `json:"status_code"`
	// Result is the result of the request
	Result *emptypb.Empty `json:"result"`
}

type ReportMerchantWorker_ProcessReportScheduleByPeriodType_Failure struct {
	// Status is the status of the response. Set false
	Status bool `json:"status"`
	// Message is a human-readable explanation of the status
	Message string `json:"message"`
	// StatusCode is the GoErr status
	StatusCode int `json:"status_code"`
}

// ProcessReportScheduleByPeriodType
// @Summary ProcessReportScheduleByPeriodType
// @Security bearerAuth
// @ID ReportMerchantWorker_ProcessReportScheduleByPeriodType
// @Accept json
// @Param request body ProcessReportScheduleByPeriodTypeRequest true "ProcessReportScheduleByPeriodTypeRequest"
// @Success 200 {object} ReportMerchantWorker_ProcessReportScheduleByPeriodType_Success
// @Failure 401 {object} ReportMerchantWorker_ProcessReportScheduleByPeriodType_Failure "goerrpkg.ErrorTypeAuthorization"
// @Failure 404 {object} ReportMerchantWorker_ProcessReportScheduleByPeriodType_Failure "goerrpkg.ErrorTypeNotFound"
// @Failure 400 {object} ReportMerchantWorker_ProcessReportScheduleByPeriodType_Failure "goerrpkg.ErrorTypeBadRequest"
// @Failure 500 {object} ReportMerchantWorker_ProcessReportScheduleByPeriodType_Failure "goerrpkg.ErrorTypeUnexpectedError"
// @Failure 400 {object} ReportMerchantWorker_ProcessReportScheduleByPeriodType_Failure "gin.ErrorTypeBind"
// @Failure 500 {object} ReportMerchantWorker_ProcessReportScheduleByPeriodType_Failure "Undefined error"
// @Produce json
// @Router /processing.report_merchant.report_merchant_worker.ReportMerchantWorker/ProcessReportScheduleByPeriodType [put]
// @tags ReportMerchantWorker
func (s *ginReportMerchantWorkerServer) ProcessReportScheduleByPeriodType(c *gin.Context) error {
	ctx, span := dog.CreateSpan(c.Request.Context(), "GinReportMerchantWorkerServer_ProcessReportScheduleByPeriodType")
	defer span.End()

	var request ProcessReportScheduleByPeriodTypeRequest
	if err := c.ShouldBindJSON(&request); err != nil {
		return err
	}

	ctx = metadata.NewOutgoingContext(ctx, metadata.MD(c.Request.Header))

	response, err := s.client.ProcessReportScheduleByPeriodType(ctx, &request)
	if err != nil {
		return err
	}

	c.JSON(http.StatusOK, &ReportMerchantWorker_ProcessReportScheduleByPeriodType_Success{
		Status:     true,
		Message:    "Success",
		StatusCode: 0,
		Result:     response,
	})
	return nil
}
