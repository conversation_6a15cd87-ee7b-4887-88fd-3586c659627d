// Code generated by protoc-gen-go-mvp. DO NOT EDIT.
// versions:
// - protoc-gen-go-mvp v2.0.1
// created by mvp authors

package grpc

import (
	context "context"
	cntx "git.local/sensitive/mvp/pkg/cntx"
	ctxzap "github.com/grpc-ecosystem/go-grpc-middleware/logging/zap/ctxzap"
	zap "go.uber.org/zap"
	grpc "google.golang.org/grpc"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
)

func file_inner_processing_grpc_fiscalization_proto_message_EmptyToZap(
	label string,
	in *emptypb.Empty,
) zap.Field {
	if in == nil {
		return zap.Reflect(label, in)
	}

	return zap.Dict(
		label,
	)
}

func file_inner_processing_grpc_fiscalization_proto_message_TimestampToZap(
	label string,
	in *timestamppb.Timestamp,
) zap.Field {
	if in == nil {
		return zap.Reflect(label, in)
	}

	return zap.Dict(
		label,
		zap.Any("Seconds", in.GetSeconds()),
		zap.Any("Nanos", in.GetNanos()),
	)
}

func file_inner_processing_grpc_fiscalization_proto_message_GetFiscalInfoByTransactionIDRequestV1ToZap(
	label string,
	in *GetFiscalInfoByTransactionIDRequestV1,
) zap.Field {
	if in == nil {
		return zap.Reflect(label, in)
	}

	return zap.Dict(
		label,
		zap.Any("TransactionId", in.GetTransactionId()),
	)
}

func file_inner_processing_grpc_fiscalization_proto_message_GetFiscalInfoByTransactionIDResponseV1ToZap(
	label string,
	in *GetFiscalInfoByTransactionIDResponseV1,
) zap.Field {
	if in == nil {
		return zap.Reflect(label, in)
	}

	return zap.Dict(
		label,
		zap.Any("FiscalNumber", in.GetFiscalNumber()),
		zap.Any("FiscalCheckUrl", in.GetFiscalCheckUrl()),
	)
}

func file_inner_processing_grpc_fiscalization_proto_message_MakeFiscalizationRequestV1ToZap(
	label string,
	in *MakeFiscalizationRequestV1,
) zap.Field {
	if in == nil {
		return zap.Reflect(label, in)
	}

	return zap.Dict(
		label,
		zap.Any("TransactionId", in.GetTransactionId()),
		file_inner_processing_grpc_fiscalization_proto_message_TimestampToZap("TransactionCreatedAt", in.GetTransactionCreatedAt()),
		zap.Any("TransactionTotalAmount", in.GetTransactionTotalAmount()),
		zap.Any("UpperCommissionAmount", in.GetUpperCommissionAmount()),
		zap.Any("Description", in.GetDescription()),
		zap.Any("CustomerId", in.GetCustomerId()),
	)
}

var _ FiscalizationServer = (*loggedFiscalizationServer)(nil)

func NewLoggedFiscalizationServer(srv FiscalizationServer) FiscalizationServer {
	return &loggedFiscalizationServer{srv: srv}
}

type loggedFiscalizationServer struct {
	UnimplementedFiscalizationServer

	srv FiscalizationServer
}

func (s *loggedFiscalizationServer) MakeFiscalizationV1(
	ctx context.Context,
	request *MakeFiscalizationRequestV1,
) (
	response *emptypb.Empty,
	err error,
) {
	label := cntx.Begin(ctx, "FiscalizationServer_MakeFiscalizationV1")
	defer cntx.End(ctx, label)

	defer func() {
		if err != nil {
			ctxzap.AddFields(ctx, zap.Any(label+"error", err))
			return
		}

		ctxzap.AddFields(
			ctx,
			file_inner_processing_grpc_fiscalization_proto_message_EmptyToZap(label+"response", response),
		)
	}()

	ctxzap.AddFields(
		ctx,
		file_inner_processing_grpc_fiscalization_proto_message_MakeFiscalizationRequestV1ToZap(label+"request", request),
	)

	response, err = s.srv.MakeFiscalizationV1(ctx, request)

	return
}

func (s *loggedFiscalizationServer) GetFiscalInfoByTransactionIDV1(
	ctx context.Context,
	request *GetFiscalInfoByTransactionIDRequestV1,
) (
	response *GetFiscalInfoByTransactionIDResponseV1,
	err error,
) {
	label := cntx.Begin(ctx, "FiscalizationServer_GetFiscalInfoByTransactionIDV1")
	defer cntx.End(ctx, label)

	defer func() {
		if err != nil {
			ctxzap.AddFields(ctx, zap.Any(label+"error", err))
			return
		}

		ctxzap.AddFields(
			ctx,
			file_inner_processing_grpc_fiscalization_proto_message_GetFiscalInfoByTransactionIDResponseV1ToZap(label+"response", response),
		)
	}()

	ctxzap.AddFields(
		ctx,
		file_inner_processing_grpc_fiscalization_proto_message_GetFiscalInfoByTransactionIDRequestV1ToZap(label+"request", request),
	)

	response, err = s.srv.GetFiscalInfoByTransactionIDV1(ctx, request)

	return
}

func (s *loggedFiscalizationServer) ManageShifts(
	ctx context.Context,
	request *emptypb.Empty,
) (
	response *emptypb.Empty,
	err error,
) {
	label := cntx.Begin(ctx, "FiscalizationServer_ManageShifts")
	defer cntx.End(ctx, label)

	defer func() {
		if err != nil {
			ctxzap.AddFields(ctx, zap.Any(label+"error", err))
			return
		}

		ctxzap.AddFields(
			ctx,
			file_inner_processing_grpc_fiscalization_proto_message_EmptyToZap(label+"response", response),
		)
	}()

	ctxzap.AddFields(
		ctx,
		file_inner_processing_grpc_fiscalization_proto_message_EmptyToZap(label+"request", request),
	)

	response, err = s.srv.ManageShifts(ctx, request)

	return
}

func (s *loggedFiscalizationServer) FinalizeFiscalizations(
	ctx context.Context,
	request *emptypb.Empty,
) (
	response *emptypb.Empty,
	err error,
) {
	label := cntx.Begin(ctx, "FiscalizationServer_FinalizeFiscalizations")
	defer cntx.End(ctx, label)

	defer func() {
		if err != nil {
			ctxzap.AddFields(ctx, zap.Any(label+"error", err))
			return
		}

		ctxzap.AddFields(
			ctx,
			file_inner_processing_grpc_fiscalization_proto_message_EmptyToZap(label+"response", response),
		)
	}()

	ctxzap.AddFields(
		ctx,
		file_inner_processing_grpc_fiscalization_proto_message_EmptyToZap(label+"request", request),
	)

	response, err = s.srv.FinalizeFiscalizations(ctx, request)

	return
}

var _ FiscalizationClient = (*loggedFiscalizationClient)(nil)

func NewLoggedFiscalizationClient(client FiscalizationClient) FiscalizationClient {
	return &loggedFiscalizationClient{client: client}
}

type loggedFiscalizationClient struct {
	client FiscalizationClient
}

func (s *loggedFiscalizationClient) MakeFiscalizationV1(
	ctx context.Context,
	request *MakeFiscalizationRequestV1,
	opts ...grpc.CallOption,
) (
	response *emptypb.Empty,
	err error,
) {
	label := cntx.Begin(ctx, "FiscalizationClient_MakeFiscalizationV1")
	defer cntx.End(ctx, label)

	defer func() {
		if err != nil {
			ctxzap.AddFields(ctx, zap.Any(label+"error", err))
			return
		}

		ctxzap.AddFields(
			ctx,
			file_inner_processing_grpc_fiscalization_proto_message_EmptyToZap(label+"response", response),
		)
	}()

	ctxzap.AddFields(
		ctx,
		file_inner_processing_grpc_fiscalization_proto_message_MakeFiscalizationRequestV1ToZap(label+"request", request),
	)

	response, err = s.client.MakeFiscalizationV1(ctx, request, opts...)

	return
}

func (s *loggedFiscalizationClient) GetFiscalInfoByTransactionIDV1(
	ctx context.Context,
	request *GetFiscalInfoByTransactionIDRequestV1,
	opts ...grpc.CallOption,
) (
	response *GetFiscalInfoByTransactionIDResponseV1,
	err error,
) {
	label := cntx.Begin(ctx, "FiscalizationClient_GetFiscalInfoByTransactionIDV1")
	defer cntx.End(ctx, label)

	defer func() {
		if err != nil {
			ctxzap.AddFields(ctx, zap.Any(label+"error", err))
			return
		}

		ctxzap.AddFields(
			ctx,
			file_inner_processing_grpc_fiscalization_proto_message_GetFiscalInfoByTransactionIDResponseV1ToZap(label+"response", response),
		)
	}()

	ctxzap.AddFields(
		ctx,
		file_inner_processing_grpc_fiscalization_proto_message_GetFiscalInfoByTransactionIDRequestV1ToZap(label+"request", request),
	)

	response, err = s.client.GetFiscalInfoByTransactionIDV1(ctx, request, opts...)

	return
}

func (s *loggedFiscalizationClient) ManageShifts(
	ctx context.Context,
	request *emptypb.Empty,
	opts ...grpc.CallOption,
) (
	response *emptypb.Empty,
	err error,
) {
	label := cntx.Begin(ctx, "FiscalizationClient_ManageShifts")
	defer cntx.End(ctx, label)

	defer func() {
		if err != nil {
			ctxzap.AddFields(ctx, zap.Any(label+"error", err))
			return
		}

		ctxzap.AddFields(
			ctx,
			file_inner_processing_grpc_fiscalization_proto_message_EmptyToZap(label+"response", response),
		)
	}()

	ctxzap.AddFields(
		ctx,
		file_inner_processing_grpc_fiscalization_proto_message_EmptyToZap(label+"request", request),
	)

	response, err = s.client.ManageShifts(ctx, request, opts...)

	return
}

func (s *loggedFiscalizationClient) FinalizeFiscalizations(
	ctx context.Context,
	request *emptypb.Empty,
	opts ...grpc.CallOption,
) (
	response *emptypb.Empty,
	err error,
) {
	label := cntx.Begin(ctx, "FiscalizationClient_FinalizeFiscalizations")
	defer cntx.End(ctx, label)

	defer func() {
		if err != nil {
			ctxzap.AddFields(ctx, zap.Any(label+"error", err))
			return
		}

		ctxzap.AddFields(
			ctx,
			file_inner_processing_grpc_fiscalization_proto_message_EmptyToZap(label+"response", response),
		)
	}()

	ctxzap.AddFields(
		ctx,
		file_inner_processing_grpc_fiscalization_proto_message_EmptyToZap(label+"request", request),
	)

	response, err = s.client.FinalizeFiscalizations(ctx, request, opts...)

	return
}
