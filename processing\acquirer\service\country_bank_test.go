package service

import (
	"context"
	"errors"
	"testing"

	"github.com/golang/mock/gomock"
	"github.com/stretchr/testify/require"

	"git.local/sensitive/innerpb/processing/goerr"
	"git.local/sensitive/processing/acquirer/model"
	"git.local/sensitive/processing/acquirer/repository/database/databasemocks"
	"git.local/sensitive/processing/acquirer/schema"
	"git.local/sensitive/sdk/dog/e2e"
)

func TestCreateCountryBank(t *testing.T) {
	type createOp struct {
		isCalled  bool
		input     *model.CountryBank
		outputErr error
	}

	tests := []struct {
		name    string
		req     *schema.CountryBank
		wantErr error
		create  createOp
	}{
		{
			name:    "nil request",
			req:     nil,
			wantErr: goerr.ErrParseErrorBody,
		},
		{
			name: "create error",
			req: &schema.CountryBank{
				CountryID: 22,
				BankID:    15,
			},
			wantErr: errors.New("some error"),
			create: createOp{
				isCalled: true,
				input: &model.CountryBank{
					CountryID: 22,
					BankID:    15,
				},
				outputErr: errors.New("some error"),
			},
		},
		{
			name: "success",
			req: &schema.CountryBank{
				CountryID: 22,
				BankID:    15,
			},
			wantErr: nil,
			create: createOp{
				isCalled: true,
				input: &model.CountryBank{
					CountryID: 22,
					BankID:    15,
				},
				outputErr: nil,
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctrl := gomock.NewController(t)
			defer ctrl.Finish()

			countryBankDBMock := databasemocks.NewMockCountryBanker(ctrl)

			countryBankService := NewCountryBankService(countryBankDBMock)

			if tt.create.isCalled {
				countryBankDBMock.EXPECT().Create(
					gomock.Any(),
					tt.create.input,
				).Return(tt.create.outputErr).Times(1)
			}

			err := countryBankService.Create(context.Background(), tt.req)
			if tt.wantErr != nil {
				require.Error(t, err)
				require.Equal(t, tt.wantErr, err)
			} else {
				require.NoError(t, err)
			}
		})
	}
}

func TestDeleteCountryBank(t *testing.T) {
	type deleteOp struct {
		inputCountryID uint64
		inputBankID    uint64
		outputErr      error
	}

	tests := []struct {
		name         string
		reqCountryID uint64
		reqBankID    uint64
		wantErr      error
		delete       deleteOp
	}{
		{
			name:         "error when deleting",
			reqCountryID: 2,
			reqBankID:    2,
			wantErr:      errors.New("some error"),
			delete: deleteOp{
				inputCountryID: 2,
				inputBankID:    2,
				outputErr:      errors.New("some error"),
			},
		},
		{
			name:         "success",
			reqCountryID: 2,
			reqBankID:    2,
			wantErr:      nil,
			delete: deleteOp{
				inputCountryID: 2,
				inputBankID:    2,
				outputErr:      nil,
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctrl := gomock.NewController(t)
			defer ctrl.Finish()

			countryBankDBMock := databasemocks.NewMockCountryBanker(ctrl)

			countryBankDBMock.EXPECT().Delete(
				gomock.Any(),
				tt.delete.inputCountryID,
				tt.delete.inputBankID,
			).Return(tt.delete.outputErr).Times(1)

			countryBankService := NewCountryBankService(countryBankDBMock)
			err := countryBankService.Delete(context.Background(), tt.reqCountryID, tt.reqBankID)
			if tt.wantErr != nil {
				require.Error(t, err)
				require.Equal(t, tt.wantErr, err)
			} else {
				require.NoError(t, err)
			}
		})
	}
}

func TestGetCountriesByBank(t *testing.T) {
	type getCountriesByBankOp struct {
		input     uint64
		output    []*model.Country
		outputErr error
	}

	tests := []struct {
		name               string
		reqBankID          uint64
		want               []*model.Country
		wantErr            error
		getCountriesByBank getCountriesByBankOp
	}{
		{
			name:      "error when getting country",
			reqBankID: 2,
			want:      nil,
			wantErr:   errors.New("some error"),
			getCountriesByBank: getCountriesByBankOp{
				input:     2,
				output:    nil,
				outputErr: errors.New("some error"),
			},
		},
		{
			name:      "success",
			reqBankID: 2,
			want: []*model.Country{
				{
					ID:   1,
					Name: "kz",
					Banks: []*model.Bank{
						{
							ID:    2,
							Name:  "some name",
							Bik:   "some bik",
							Swift: "some swift",
						},
					},
				},
			},
			wantErr: nil,
			getCountriesByBank: getCountriesByBankOp{
				input: 2,
				output: []*model.Country{
					{
						ID:   1,
						Name: "kz",
						Banks: []*model.Bank{
							{
								ID:    2,
								Name:  "some name",
								Bik:   "some bik",
								Swift: "some swift",
							},
						},
					},
				},
				outputErr: nil,
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctrl := gomock.NewController(t)
			defer ctrl.Finish()

			c := context.Background()

			ctx, cancel := context.WithCancel(c)

			e2e.InitAppE2E(t, ctx, cancel, nil, nil, map[string]any{})

			countryBankDBMock := databasemocks.NewMockCountryBanker(ctrl)

			countryBankDBMock.EXPECT().GetCountriesByBank(
				gomock.Any(),
				tt.getCountriesByBank.input,
			).Return(tt.getCountriesByBank.output, tt.getCountriesByBank.outputErr).Times(1)

			countryBankService := NewCountryBankService(countryBankDBMock)

			resp, err := countryBankService.GetCountriesByBank(context.Background(), tt.reqBankID)
			if tt.wantErr != nil {
				require.Error(t, err)
				require.Equal(t, tt.wantErr, err)
			} else {
				require.NoError(t, err)
			}

			require.Equal(t, tt.want, resp)
		})
	}

}

func TestGetBanksByCountryID(t *testing.T) {
	type getBanksByCountryIDOp struct {
		input     uint64
		output    []*model.Bank
		outputErr error
	}

	tests := []struct {
		name                string
		req                 uint64
		want                []*model.Bank
		wantErr             error
		getBanksByCountryID getBanksByCountryIDOp
	}{
		{
			name:    "error when getting banks",
			req:     2,
			want:    nil,
			wantErr: errors.New("some error"),
			getBanksByCountryID: getBanksByCountryIDOp{
				input:     2,
				output:    nil,
				outputErr: errors.New("some error"),
			},
		},
		{
			name: "success",
			req:  2,
			want: []*model.Bank{
				{
					ID:    1,
					Name:  "kz",
					Bik:   "some bik",
					Swift: "some swift",
				},
				{
					ID:    2,
					Name:  "us",
					Bik:   "some another bik",
					Swift: "some another swift",
				},
			},
			wantErr: nil,
			getBanksByCountryID: getBanksByCountryIDOp{
				input: 2,
				output: []*model.Bank{
					{
						ID:    1,
						Name:  "kz",
						Bik:   "some bik",
						Swift: "some swift",
					},
					{
						ID:    2,
						Name:  "us",
						Bik:   "some another bik",
						Swift: "some another swift",
					},
				},
				outputErr: nil,
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctrl := gomock.NewController(t)
			defer ctrl.Finish()

			c := context.Background()

			ctx, cancel := context.WithCancel(c)

			e2e.InitAppE2E(t, ctx, cancel, nil, nil, map[string]any{})

			countryBankDBMock := databasemocks.NewMockCountryBanker(ctrl)

			countryBankDBMock.EXPECT().GetBanksByCountryID(
				gomock.Any(),
				tt.getBanksByCountryID.input,
			).Return(tt.getBanksByCountryID.output, tt.getBanksByCountryID.outputErr).Times(1)

			countryBankService := NewCountryBankService(countryBankDBMock)

			resp, err := countryBankService.GetBanksByCountryID(context.Background(), tt.req)
			if tt.wantErr != nil {
				require.Error(t, err)
				require.Equal(t, tt.wantErr, err)
			} else {
				require.NoError(t, err)
			}

			require.Equal(t, tt.want, resp)
		})
	}
}
