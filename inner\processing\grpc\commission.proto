edition = "2023";

package processing.commission.commission;

option go_package = "git.local/sensitive/innerpb/processing/grpc";

import "google/protobuf/timestamp.proto";
import "google/protobuf/empty.proto";

service Commission {
  rpc CalculateAndSaveUpperCommission(CalculateAndSaveUpperCommissionRequestV1) returns (CalculateAndSaveUpperCommissionResponseV1) {}
  rpc GetCommissionForMainBalance(GetCommissionForMainBalanceRequestV1) returns (GetCommissionForMainBalanceResponseV1) {}
  rpc UpdateCommissionForCreditBalance (UpdateCommissionForCreditBalanceRequestV1) returns (UpdateCommissionForCreditBalanceResponseV1) {}
  rpc CalculatePayInPrimalAmount(CalculatePrimalAmountRequestV1) returns (CalculatePrimalAmountResponseV1) {}
  rpc CalculatePayOutPrimalAmount(CalculatePrimalAmountRequestV1) returns (CalculatePrimalAmountResponseV1) {}
  rpc FiscalizeUpperCommission(FiscalizeUpperCommissionRequestV1) returns (google.protobuf.Empty) {}
  rpc GetCommissionByTransactionID(GetCommissionByTransactionIDRequestV1) returns (GetCommissionByTransactionIDResponseV1) {}
}

message CalculatePrimalAmountRequestV1 {
  uint64 transactionID = 1;
  double amount = 2;
}

message TransactionCommissionResponseV1 {
  uint64 id = 1;
  uint64 acquirer_commission_id = 2;
  uint64 project_lower_commission_id = 3;
  uint64 project_upper_commission_id = 4;
  double acquirer_commission_amount = 5;
  double project_lower_commission_amount = 6;
  double project_upper_commission_amount = 7;
}

message CalculateAndSaveUpperCommissionResponseV1 {
  uint64 commission_id = 1;
  double total_commission_amount = 2;
}

message CalculatePrimalAmountResponseV1 {
  uint64 commission_id = 1;
  double primal_amount = 2;
}

message CalculateAndSaveUpperCommissionRequestV1{
  uint64 project_id = 1;
  uint64 merchant_id = 2;
  uint64 aggregated_type_id = 3;
  uint64 transaction_id = 4;
  double transaction_amount = 5;
  google.protobuf.Timestamp transaction_created_at = 6;
}

message UpdateCommissionForCreditBalanceRequestV1 {
  uint64 transaction_id = 1;
  double amount = 2;
  uint64 project_id = 3;
  google.protobuf.Timestamp transaction_created_at = 4;
}

message UpdateCommissionForCreditBalanceResponseV1 {
  double tarlan_commission = 1;
  double acquirer_commission = 2;
}

message GetCommissionForMainBalanceRequestV1 {
  uint64 transaction_id = 1;
}

message GetCommissionForMainBalanceResponseV1 {
  double tarlan_commission = 1;
  double acquirer_commission = 2;
}

message FiscalizeUpperCommissionRequestV1 {
  uint64 transaction_id = 1;
}

message GetCommissionByTransactionIDRequestV1{
  uint64 transaction_id = 1;
}

message GetCommissionByTransactionIDResponseV1{
  double project_lower_commission_amount = 1;
  double project_upper_commission_amount = 2;
}

message CommissionV1{
  uint64 tax_project_id = 1;
  double amount = 2;
}