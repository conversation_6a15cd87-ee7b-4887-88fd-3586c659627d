// Code generated by protoc-gen-go-mvp. DO NOT EDIT.
// versions:
// - protoc-gen-go-mvp v2.0.1
// created by mvp authors

package grpc

import (
	dog "git.local/sensitive/sdk/dog"
	gin "github.com/gin-gonic/gin"
	metadata "google.golang.org/grpc/metadata"
	http "net/http"
)

func RegisterGinSmartPayRouterGroup(
	in *gin.RouterGroup,
	handler func(func(c *gin.Context) error) gin.HandlerFunc,
) error {
	service, err := NewGinSmartPayService()
	if err != nil {
		return err
	}

	routerGroup := in.Group("processing.smart_pay.smart_pay.SmartPay")
	routerGroup.PUT("/ApplePaySession", handler(service.ApplePaySession))
	routerGroup.PUT("/DecodeToken", handler(service.DecodeToken))
	routerGroup.PUT("/DecryptGPayToken", handler(service.DecryptGPayToken))
	routerGroup.PUT("/GetGPayCredentials", handler(service.GetGPayCredentials))
	return nil
}

func NewGinSmartPayService() (GinSmartPayServer, error) {
	client, err := NewPreparedSmartPayClient()
	if err != nil {
		return nil, err
	}

	return &ginSmartPayServer{
		client: NewLoggedSmartPayClient(
			NewIamSmartPayClient(client),
		),
	}, nil
}

//go:generate mockgen -destination=./grpcmock/smart_pay.gin.pb.go -package=grpcmock -source=smart_pay.gin.pb.go GinSmartPayServer
type GinSmartPayServer interface {
	ApplePaySession(c *gin.Context) error
	DecodeToken(c *gin.Context) error
	DecryptGPayToken(c *gin.Context) error
	GetGPayCredentials(c *gin.Context) error
}

var _ GinSmartPayServer = (*ginSmartPayServer)(nil)

type ginSmartPayServer struct {
	client SmartPayClient
}

type SmartPay_ApplePaySession_Success struct {
	// Status is the status of the response. Set true
	Status bool `json:"status"`
	// Message is a human-readable explanation of the status
	Message string `json:"message"`
	// StatusCode is the GoErr status. Set 0
	StatusCode int `json:"status_code"`
	// Result is the result of the request
	Result *ApplePaySessionResponseV1 `json:"result"`
}

type SmartPay_ApplePaySession_Failure struct {
	// Status is the status of the response. Set false
	Status bool `json:"status"`
	// Message is a human-readable explanation of the status
	Message string `json:"message"`
	// StatusCode is the GoErr status
	StatusCode int `json:"status_code"`
}

// ApplePaySession
// @Summary ApplePaySession
// @Security bearerAuth
// @ID SmartPay_ApplePaySession
// @Accept json
// @Param request body ApplePaySessionRequestV1 true "ApplePaySessionRequestV1"
// @Success 200 {object} SmartPay_ApplePaySession_Success
// @Failure 401 {object} SmartPay_ApplePaySession_Failure "goerrpkg.ErrorTypeAuthorization"
// @Failure 404 {object} SmartPay_ApplePaySession_Failure "goerrpkg.ErrorTypeNotFound"
// @Failure 400 {object} SmartPay_ApplePaySession_Failure "goerrpkg.ErrorTypeBadRequest"
// @Failure 500 {object} SmartPay_ApplePaySession_Failure "goerrpkg.ErrorTypeUnexpectedError"
// @Failure 400 {object} SmartPay_ApplePaySession_Failure "gin.ErrorTypeBind"
// @Failure 500 {object} SmartPay_ApplePaySession_Failure "Undefined error"
// @Produce json
// @Router /processing.smart_pay.smart_pay.SmartPay/ApplePaySession [put]
// @tags SmartPay
func (s *ginSmartPayServer) ApplePaySession(c *gin.Context) error {
	ctx, span := dog.CreateSpan(c.Request.Context(), "GinSmartPayServer_ApplePaySession")
	defer span.End()

	var request ApplePaySessionRequestV1
	if err := c.ShouldBindJSON(&request); err != nil {
		return err
	}

	ctx = metadata.NewOutgoingContext(ctx, metadata.MD(c.Request.Header))

	response, err := s.client.ApplePaySession(ctx, &request)
	if err != nil {
		return err
	}

	c.JSON(http.StatusOK, &SmartPay_ApplePaySession_Success{
		Status:     true,
		Message:    "Success",
		StatusCode: 0,
		Result:     response,
	})
	return nil
}

type SmartPay_DecodeToken_Success struct {
	// Status is the status of the response. Set true
	Status bool `json:"status"`
	// Message is a human-readable explanation of the status
	Message string `json:"message"`
	// StatusCode is the GoErr status. Set 0
	StatusCode int `json:"status_code"`
	// Result is the result of the request
	Result *DecodeTokenResponseV1 `json:"result"`
}

type SmartPay_DecodeToken_Failure struct {
	// Status is the status of the response. Set false
	Status bool `json:"status"`
	// Message is a human-readable explanation of the status
	Message string `json:"message"`
	// StatusCode is the GoErr status
	StatusCode int `json:"status_code"`
}

// DecodeToken
// @Summary DecodeToken
// @Security bearerAuth
// @ID SmartPay_DecodeToken
// @Accept json
// @Param request body DecodeTokenRequestV1 true "DecodeTokenRequestV1"
// @Success 200 {object} SmartPay_DecodeToken_Success
// @Failure 401 {object} SmartPay_DecodeToken_Failure "goerrpkg.ErrorTypeAuthorization"
// @Failure 404 {object} SmartPay_DecodeToken_Failure "goerrpkg.ErrorTypeNotFound"
// @Failure 400 {object} SmartPay_DecodeToken_Failure "goerrpkg.ErrorTypeBadRequest"
// @Failure 500 {object} SmartPay_DecodeToken_Failure "goerrpkg.ErrorTypeUnexpectedError"
// @Failure 400 {object} SmartPay_DecodeToken_Failure "gin.ErrorTypeBind"
// @Failure 500 {object} SmartPay_DecodeToken_Failure "Undefined error"
// @Produce json
// @Router /processing.smart_pay.smart_pay.SmartPay/DecodeToken [put]
// @tags SmartPay
func (s *ginSmartPayServer) DecodeToken(c *gin.Context) error {
	ctx, span := dog.CreateSpan(c.Request.Context(), "GinSmartPayServer_DecodeToken")
	defer span.End()

	var request DecodeTokenRequestV1
	if err := c.ShouldBindJSON(&request); err != nil {
		return err
	}

	ctx = metadata.NewOutgoingContext(ctx, metadata.MD(c.Request.Header))

	response, err := s.client.DecodeToken(ctx, &request)
	if err != nil {
		return err
	}

	c.JSON(http.StatusOK, &SmartPay_DecodeToken_Success{
		Status:     true,
		Message:    "Success",
		StatusCode: 0,
		Result:     response,
	})
	return nil
}

type SmartPay_DecryptGPayToken_Success struct {
	// Status is the status of the response. Set true
	Status bool `json:"status"`
	// Message is a human-readable explanation of the status
	Message string `json:"message"`
	// StatusCode is the GoErr status. Set 0
	StatusCode int `json:"status_code"`
	// Result is the result of the request
	Result *DecryptGPayTokenResponseV1 `json:"result"`
}

type SmartPay_DecryptGPayToken_Failure struct {
	// Status is the status of the response. Set false
	Status bool `json:"status"`
	// Message is a human-readable explanation of the status
	Message string `json:"message"`
	// StatusCode is the GoErr status
	StatusCode int `json:"status_code"`
}

// DecryptGPayToken
// @Summary DecryptGPayToken
// @Security bearerAuth
// @ID SmartPay_DecryptGPayToken
// @Accept json
// @Param request body DecryptGPayTokenRequestV1 true "DecryptGPayTokenRequestV1"
// @Success 200 {object} SmartPay_DecryptGPayToken_Success
// @Failure 401 {object} SmartPay_DecryptGPayToken_Failure "goerrpkg.ErrorTypeAuthorization"
// @Failure 404 {object} SmartPay_DecryptGPayToken_Failure "goerrpkg.ErrorTypeNotFound"
// @Failure 400 {object} SmartPay_DecryptGPayToken_Failure "goerrpkg.ErrorTypeBadRequest"
// @Failure 500 {object} SmartPay_DecryptGPayToken_Failure "goerrpkg.ErrorTypeUnexpectedError"
// @Failure 400 {object} SmartPay_DecryptGPayToken_Failure "gin.ErrorTypeBind"
// @Failure 500 {object} SmartPay_DecryptGPayToken_Failure "Undefined error"
// @Produce json
// @Router /processing.smart_pay.smart_pay.SmartPay/DecryptGPayToken [put]
// @tags SmartPay
func (s *ginSmartPayServer) DecryptGPayToken(c *gin.Context) error {
	ctx, span := dog.CreateSpan(c.Request.Context(), "GinSmartPayServer_DecryptGPayToken")
	defer span.End()

	var request DecryptGPayTokenRequestV1
	if err := c.ShouldBindJSON(&request); err != nil {
		return err
	}

	ctx = metadata.NewOutgoingContext(ctx, metadata.MD(c.Request.Header))

	response, err := s.client.DecryptGPayToken(ctx, &request)
	if err != nil {
		return err
	}

	c.JSON(http.StatusOK, &SmartPay_DecryptGPayToken_Success{
		Status:     true,
		Message:    "Success",
		StatusCode: 0,
		Result:     response,
	})
	return nil
}

type SmartPay_GetGPayCredentials_Success struct {
	// Status is the status of the response. Set true
	Status bool `json:"status"`
	// Message is a human-readable explanation of the status
	Message string `json:"message"`
	// StatusCode is the GoErr status. Set 0
	StatusCode int `json:"status_code"`
	// Result is the result of the request
	Result *GetGPayCredentialsResponseV1 `json:"result"`
}

type SmartPay_GetGPayCredentials_Failure struct {
	// Status is the status of the response. Set false
	Status bool `json:"status"`
	// Message is a human-readable explanation of the status
	Message string `json:"message"`
	// StatusCode is the GoErr status
	StatusCode int `json:"status_code"`
}

// GetGPayCredentials
// @Summary GetGPayCredentials
// @Security bearerAuth
// @ID SmartPay_GetGPayCredentials
// @Accept json
// @Param request body GetGPayCredentialsRequestV1 true "GetGPayCredentialsRequestV1"
// @Success 200 {object} SmartPay_GetGPayCredentials_Success
// @Failure 401 {object} SmartPay_GetGPayCredentials_Failure "goerrpkg.ErrorTypeAuthorization"
// @Failure 404 {object} SmartPay_GetGPayCredentials_Failure "goerrpkg.ErrorTypeNotFound"
// @Failure 400 {object} SmartPay_GetGPayCredentials_Failure "goerrpkg.ErrorTypeBadRequest"
// @Failure 500 {object} SmartPay_GetGPayCredentials_Failure "goerrpkg.ErrorTypeUnexpectedError"
// @Failure 400 {object} SmartPay_GetGPayCredentials_Failure "gin.ErrorTypeBind"
// @Failure 500 {object} SmartPay_GetGPayCredentials_Failure "Undefined error"
// @Produce json
// @Router /processing.smart_pay.smart_pay.SmartPay/GetGPayCredentials [put]
// @tags SmartPay
func (s *ginSmartPayServer) GetGPayCredentials(c *gin.Context) error {
	ctx, span := dog.CreateSpan(c.Request.Context(), "GinSmartPayServer_GetGPayCredentials")
	defer span.End()

	var request GetGPayCredentialsRequestV1
	if err := c.ShouldBindJSON(&request); err != nil {
		return err
	}

	ctx = metadata.NewOutgoingContext(ctx, metadata.MD(c.Request.Header))

	response, err := s.client.GetGPayCredentials(ctx, &request)
	if err != nil {
		return err
	}

	c.JSON(http.StatusOK, &SmartPay_GetGPayCredentials_Success{
		Status:     true,
		Message:    "Success",
		StatusCode: 0,
		Result:     response,
	})
	return nil
}
