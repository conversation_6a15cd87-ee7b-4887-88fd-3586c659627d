package service

import (
	"context"
	"crypto/aes"
	"errors"
	"git.local/sensitive/innerpb/processing/grpc/grpcmock"
	"testing"

	"github.com/golang/mock/gomock"
	"github.com/stretchr/testify/require"
	"google.golang.org/protobuf/types/known/structpb"

	"git.local/sensitive/innerpb/processing/grpc"
	"git.local/sensitive/processing/acquirer/model"
	"git.local/sensitive/processing/acquirer/repository/database/databasemocks"
	"git.local/sensitive/processing/acquirer/schema"
	"git.local/sensitive/sdk/dog/e2e"
	"git.local/sensitive/testsdk"
)

func TestGetTerminalsBalanceByProjectID(t *testing.T) {
	structbConfig, err := structpb.NewStruct(map[string]any{
		"my-name-is": "skrillex",
	})
	if err != nil {
		t.Fatal(err)
	}

	type getPayoutTypesOp struct {
		output    *grpc.TransactionTypeResponseV1
		outputErr error
	}

	type getTerminalsOp struct {
		isCalled       bool
		inputProjectID uint64
		inputTypeID    uint64
		output         model.Terminals
		outputErr      error
	}

	type checkBalanceOp struct {
		isCalled  bool
		input     *grpc.CheckBalanceRequest
		output    *grpc.CheckBalanceResponse
		outputErr error
	}

	tests := []struct {
		name         string
		reqProjectID uint64
		want         []schema.GetBalanceResponse
		wantErr      error
		getPayout    getPayoutTypesOp
		getTerminals getTerminalsOp
		checkBalance checkBalanceOp
		appConfig    map[string]any
	}{
		{
			name:         "error when getting payout transaction types",
			reqProjectID: 28,
			getPayout: getPayoutTypesOp{
				output:    nil,
				outputErr: errors.New("some network error"),
			},
			wantErr: errors.New("some network error"),
			want:    nil,
			appConfig: map[string]any{
				"SYMMETRIC_KEY": normalKey,
			},
		},
		{
			name:         "error when getting terminals",
			reqProjectID: 28,
			getPayout: getPayoutTypesOp{
				output: &grpc.TransactionTypeResponseV1{
					Data: []*grpc.TransactionTypesResponseV1{
						{
							Id:   testsdk.Ptr(uint64(2)),
							Name: testsdk.Ptr("Вывод"),
							Code: testsdk.Ptr("out"),
						},
					},
				},
				outputErr: nil,
			},
			getTerminals: getTerminalsOp{
				isCalled:       true,
				inputProjectID: 28,
				inputTypeID:    2,
				output:         nil,
				outputErr:      errors.New("some error"),
			},
			wantErr: errors.New("some error"),
			want:    nil,
			appConfig: map[string]any{
				"SYMMETRIC_KEY": normalKey,
			},
		},
		{
			name:         "nil terminals returned",
			reqProjectID: 28,
			getPayout: getPayoutTypesOp{
				output: &grpc.TransactionTypeResponseV1{
					Data: []*grpc.TransactionTypesResponseV1{
						{
							Id:   testsdk.Ptr(uint64(2)),
							Name: testsdk.Ptr("Вывод"),
							Code: testsdk.Ptr("out"),
						},
					},
				},
				outputErr: nil,
			},
			getTerminals: getTerminalsOp{
				isCalled:       true,
				inputProjectID: 28,
				inputTypeID:    2,
				output:         nil,
				outputErr:      nil,
			},
			wantErr: nil,
			want:    make([]schema.GetBalanceResponse, 0),
			appConfig: map[string]any{
				"SYMMETRIC_KEY": normalKey,
			},
		},
		{
			name:         "aesDescrypt error",
			reqProjectID: 28,
			getPayout: getPayoutTypesOp{
				output: &grpc.TransactionTypeResponseV1{
					Data: []*grpc.TransactionTypesResponseV1{
						{
							Id:   testsdk.Ptr(uint64(2)),
							Name: testsdk.Ptr("Вывод"),
							Code: testsdk.Ptr("out"),
						},
					},
				},
				outputErr: nil,
			},
			getTerminals: getTerminalsOp{
				isCalled:       true,
				inputProjectID: 28,
				inputTypeID:    2,
				output: model.Terminals{
					{
						ID:                   22,
						AcquirerID:           1,
						Status:               model.TerminalOn,
						EncryptedConfig:      defaultEncryptedConfig,
						TwoStageTimeout:      10,
						AccountNumber:        "********************",
						IsTransit:            false,
						Description:          "some desc",
						AcquirerTerminalName: "test terminal",
					},
				},
				outputErr: nil,
			},
			wantErr: aes.KeySizeError(3),
			want:    nil,
			appConfig: map[string]any{
				"SYMMETRIC_KEY": badKey,
			},
		},
		{
			name:         "aesDescrypt error",
			reqProjectID: 28,
			getPayout: getPayoutTypesOp{
				output: &grpc.TransactionTypeResponseV1{
					Data: []*grpc.TransactionTypesResponseV1{
						{
							Id:   testsdk.Ptr(uint64(2)),
							Name: testsdk.Ptr("Вывод"),
							Code: testsdk.Ptr("out"),
						},
					},
				},
				outputErr: nil,
			},
			getTerminals: getTerminalsOp{
				isCalled:       true,
				inputProjectID: 28,
				inputTypeID:    2,
				output: model.Terminals{
					{
						ID:                   22,
						AcquirerID:           1,
						Status:               model.TerminalOn,
						EncryptedConfig:      defaultEncryptedConfig,
						TwoStageTimeout:      10,
						AccountNumber:        "********************",
						IsTransit:            false,
						Description:          "some desc",
						AcquirerTerminalName: "test terminal",
					},
				},
				outputErr: nil,
			},
			wantErr: aes.KeySizeError(3),
			want:    nil,
			appConfig: map[string]any{
				"SYMMETRIC_KEY": badKey,
			},
		},
		{
			name:         "check balance error",
			reqProjectID: 28,
			getPayout: getPayoutTypesOp{
				output: &grpc.TransactionTypeResponseV1{
					Data: []*grpc.TransactionTypesResponseV1{
						{
							Id:   testsdk.Ptr(uint64(2)),
							Name: testsdk.Ptr("Вывод"),
							Code: testsdk.Ptr("out"),
						},
					},
				},
				outputErr: nil,
			},
			getTerminals: getTerminalsOp{
				isCalled:       true,
				inputProjectID: 28,
				inputTypeID:    2,
				output: model.Terminals{
					{
						ID:                   22,
						AcquirerID:           1,
						Status:               model.TerminalOn,
						EncryptedConfig:      defaultEncryptedConfig,
						TwoStageTimeout:      10,
						AccountNumber:        "********************",
						IsTransit:            false,
						Description:          "some desc",
						AcquirerTerminalName: "test terminal",
						Acquirer: model.Acquirer{
							Code: "bcc",
						},
					},
				},
				outputErr: nil,
			},
			checkBalance: checkBalanceOp{
				isCalled: true,
				input: &grpc.CheckBalanceRequest{
					TerminalId:   testsdk.Ptr(uint64(22)),
					AcquirerCode: testsdk.Ptr("bcc"),
					Config:       structbConfig,
				},
				output:    nil,
				outputErr: errors.New("some mystic error"),
			},
			wantErr: errors.New("some mystic error"),
			want:    nil,
			appConfig: map[string]any{
				"SYMMETRIC_KEY": normalKey,
			},
		},
		{
			name:         "success",
			reqProjectID: 28,
			getPayout: getPayoutTypesOp{
				output: &grpc.TransactionTypeResponseV1{
					Data: []*grpc.TransactionTypesResponseV1{
						{
							Id:   testsdk.Ptr(uint64(2)),
							Name: testsdk.Ptr("Вывод"),
							Code: testsdk.Ptr("out"),
						},
					},
				},
				outputErr: nil,
			},
			getTerminals: getTerminalsOp{
				isCalled:       true,
				inputProjectID: 28,
				inputTypeID:    2,
				output: model.Terminals{
					{
						ID:                   22,
						AcquirerID:           1,
						Status:               model.TerminalOn,
						EncryptedConfig:      defaultEncryptedConfig,
						TwoStageTimeout:      10,
						AccountNumber:        "********************",
						IsTransit:            false,
						Description:          "some desc",
						AcquirerTerminalName: "test terminal",
						Acquirer: model.Acquirer{
							Code: "bcc",
						},
					},
				},
				outputErr: nil,
			},
			checkBalance: checkBalanceOp{
				isCalled: true,
				input: &grpc.CheckBalanceRequest{
					TerminalId:   testsdk.Ptr(uint64(22)),
					AcquirerCode: testsdk.Ptr("bcc"),
					Config:       structbConfig,
				},
				output: &grpc.CheckBalanceResponse{
					Amount:       testsdk.Ptr(float64(10)),
					Message:      testsdk.Ptr("some message"),
					TerminalId:   testsdk.Ptr(uint64(22)),
					AcquirerCode: testsdk.Ptr("bcc"),
				},
				outputErr: nil,
			},
			wantErr: nil,
			want: []schema.GetBalanceResponse{
				{
					TerminalID:   22,
					AcquirerName: "bcc",
					Amount:       10,
					Message:      "some message",
				},
			},
			appConfig: map[string]any{
				"SYMMETRIC_KEY": normalKey,
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctrl := gomock.NewController(t)
			defer ctrl.Finish()

			c := context.Background()
			ctx, cancel := context.WithCancel(c)
			e2e.InitAppE2E(t, ctx, cancel, nil, nil, tt.appConfig)

			transactionTypeCliMock := grpcmock.NewMockTransactionTypeClient(ctrl)
			multiAcquiringCliMock := grpcmock.NewMockMultiacquiringBalanceClient(ctrl)
			terminalBalanceDBMock := databasemocks.NewMockTerminalBalancer(ctrl)

			transactionTypeCliMock.EXPECT().GetTransactionPayOutTypes(
				gomock.Any(),
				gomock.Any(),
			).Return(tt.getPayout.output, tt.getPayout.outputErr).Times(1)

			if tt.getTerminals.isCalled {
				terminalBalanceDBMock.EXPECT().GetTerminals(
					gomock.Any(),
					tt.getTerminals.inputProjectID,
					tt.getTerminals.inputTypeID,
				).Return(tt.getTerminals.output, tt.getTerminals.outputErr).Times(1)
			}

			if tt.checkBalance.isCalled {
				multiAcquiringCliMock.EXPECT().CheckBalance(
					gomock.Any(),
					gomock.Any(),
				).Return(tt.checkBalance.output, tt.checkBalance.outputErr).Times(1)
			}

			s := NewTerminalBalanceService(terminalBalanceDBMock, multiAcquiringCliMock, transactionTypeCliMock)

			resp, err := s.GetTerminalsBalanceByProjectID(context.Background(), tt.reqProjectID)
			if tt.wantErr != nil {
				require.Error(t, err)
				require.Equal(t, tt.wantErr, err)
			} else {
				require.NoError(t, err)
			}

			require.Equal(t, tt.want, resp)
		})
	}
}
