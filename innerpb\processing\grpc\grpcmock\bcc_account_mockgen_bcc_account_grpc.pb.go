// Code generated by MockGen. DO NOT EDIT.
// Source: bcc_account_grpc.pb.go

// Package grpcmock is a generated GoMock package.
package grpcmock

import (
	context "context"
	reflect "reflect"

	grpc "git.local/sensitive/innerpb/processing/grpc"
	gomock "github.com/golang/mock/gomock"
	grpc0 "google.golang.org/grpc"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
)

// MockBCCAccountClient is a mock of BCCAccountClient interface.
type MockBCCAccountClient struct {
	ctrl     *gomock.Controller
	recorder *MockBCCAccountClientMockRecorder
}

// MockBCCAccountClientMockRecorder is the mock recorder for MockBCCAccountClient.
type MockBCCAccountClientMockRecorder struct {
	mock *MockBCCAccountClient
}

// NewMockBCCAccountClient creates a new mock instance.
func NewMockBCCAccountClient(ctrl *gomock.Controller) *MockBCCAccountClient {
	mock := &MockBCCAccountClient{ctrl: ctrl}
	mock.recorder = &MockBCCAccountClientMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockBCCAccountClient) EXPECT() *MockBCCAccountClientMockRecorder {
	return m.recorder
}

// AcceptTransfer mocks base method.
func (m *MockBCCAccountClient) AcceptTransfer(ctx context.Context, in *grpc.AcceptTransferRequest, opts ...grpc0.CallOption) (*grpc.AcceptTransferResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "AcceptTransfer", varargs...)
	ret0, _ := ret[0].(*grpc.AcceptTransferResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// AcceptTransfer indicates an expected call of AcceptTransfer.
func (mr *MockBCCAccountClientMockRecorder) AcceptTransfer(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AcceptTransfer", reflect.TypeOf((*MockBCCAccountClient)(nil).AcceptTransfer), varargs...)
}

// DeclineTransfer mocks base method.
func (m *MockBCCAccountClient) DeclineTransfer(ctx context.Context, in *grpc.DeclineTransferRequest, opts ...grpc0.CallOption) (*grpc.DeclineTransferResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "DeclineTransfer", varargs...)
	ret0, _ := ret[0].(*grpc.DeclineTransferResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DeclineTransfer indicates an expected call of DeclineTransfer.
func (mr *MockBCCAccountClientMockRecorder) DeclineTransfer(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeclineTransfer", reflect.TypeOf((*MockBCCAccountClient)(nil).DeclineTransfer), varargs...)
}

// GetAccountBalance mocks base method.
func (m *MockBCCAccountClient) GetAccountBalance(ctx context.Context, in *grpc.GetAccountBalanceRequest, opts ...grpc0.CallOption) (*grpc.GetAccountBalanceResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetAccountBalance", varargs...)
	ret0, _ := ret[0].(*grpc.GetAccountBalanceResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAccountBalance indicates an expected call of GetAccountBalance.
func (mr *MockBCCAccountClientMockRecorder) GetAccountBalance(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAccountBalance", reflect.TypeOf((*MockBCCAccountClient)(nil).GetAccountBalance), varargs...)
}

// GetAccountIdentifier mocks base method.
func (m *MockBCCAccountClient) GetAccountIdentifier(ctx context.Context, in *emptypb.Empty, opts ...grpc0.CallOption) (*grpc.GetAccountIdentifierResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetAccountIdentifier", varargs...)
	ret0, _ := ret[0].(*grpc.GetAccountIdentifierResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAccountIdentifier indicates an expected call of GetAccountIdentifier.
func (mr *MockBCCAccountClientMockRecorder) GetAccountIdentifier(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAccountIdentifier", reflect.TypeOf((*MockBCCAccountClient)(nil).GetAccountIdentifier), varargs...)
}

// GetAccountStatement mocks base method.
func (m *MockBCCAccountClient) GetAccountStatement(ctx context.Context, in *grpc.GetAccountStatementRequest, opts ...grpc0.CallOption) (*grpc.GetAccountStatementResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetAccountStatement", varargs...)
	ret0, _ := ret[0].(*grpc.GetAccountStatementResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAccountStatement indicates an expected call of GetAccountStatement.
func (mr *MockBCCAccountClientMockRecorder) GetAccountStatement(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAccountStatement", reflect.TypeOf((*MockBCCAccountClient)(nil).GetAccountStatement), varargs...)
}

// GetTransferDetails mocks base method.
func (m *MockBCCAccountClient) GetTransferDetails(ctx context.Context, in *grpc.GetTransferDetailsRequest, opts ...grpc0.CallOption) (*grpc.GetTransferDetailsResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetTransferDetails", varargs...)
	ret0, _ := ret[0].(*grpc.GetTransferDetailsResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetTransferDetails indicates an expected call of GetTransferDetails.
func (mr *MockBCCAccountClientMockRecorder) GetTransferDetails(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetTransferDetails", reflect.TypeOf((*MockBCCAccountClient)(nil).GetTransferDetails), varargs...)
}

// GetTransfersList mocks base method.
func (m *MockBCCAccountClient) GetTransfersList(ctx context.Context, in *grpc.GetTransfersListRequest, opts ...grpc0.CallOption) (*grpc.GetTransfersListResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetTransfersList", varargs...)
	ret0, _ := ret[0].(*grpc.GetTransfersListResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetTransfersList indicates an expected call of GetTransfersList.
func (mr *MockBCCAccountClientMockRecorder) GetTransfersList(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetTransfersList", reflect.TypeOf((*MockBCCAccountClient)(nil).GetTransfersList), varargs...)
}

// MakeMerchantCheck mocks base method.
func (m *MockBCCAccountClient) MakeMerchantCheck(ctx context.Context, in *grpc.MakeMerchantCheckRequest, opts ...grpc0.CallOption) (*grpc.MakeMerchantCheckResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "MakeMerchantCheck", varargs...)
	ret0, _ := ret[0].(*grpc.MakeMerchantCheckResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// MakeMerchantCheck indicates an expected call of MakeMerchantCheck.
func (mr *MockBCCAccountClientMockRecorder) MakeMerchantCheck(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "MakeMerchantCheck", reflect.TypeOf((*MockBCCAccountClient)(nil).MakeMerchantCheck), varargs...)
}

// MakeTransfer mocks base method.
func (m *MockBCCAccountClient) MakeTransfer(ctx context.Context, in *grpc.MakeTransferRequest, opts ...grpc0.CallOption) (*grpc.MakeTransferResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "MakeTransfer", varargs...)
	ret0, _ := ret[0].(*grpc.MakeTransferResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// MakeTransfer indicates an expected call of MakeTransfer.
func (mr *MockBCCAccountClientMockRecorder) MakeTransfer(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "MakeTransfer", reflect.TypeOf((*MockBCCAccountClient)(nil).MakeTransfer), varargs...)
}

// RedoTransfer mocks base method.
func (m *MockBCCAccountClient) RedoTransfer(ctx context.Context, in *grpc.RedoTransferRequest, opts ...grpc0.CallOption) (*grpc.RedoTransferResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "RedoTransfer", varargs...)
	ret0, _ := ret[0].(*grpc.RedoTransferResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// RedoTransfer indicates an expected call of RedoTransfer.
func (mr *MockBCCAccountClientMockRecorder) RedoTransfer(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "RedoTransfer", reflect.TypeOf((*MockBCCAccountClient)(nil).RedoTransfer), varargs...)
}

// MockBCCAccountServer is a mock of BCCAccountServer interface.
type MockBCCAccountServer struct {
	ctrl     *gomock.Controller
	recorder *MockBCCAccountServerMockRecorder
}

// MockBCCAccountServerMockRecorder is the mock recorder for MockBCCAccountServer.
type MockBCCAccountServerMockRecorder struct {
	mock *MockBCCAccountServer
}

// NewMockBCCAccountServer creates a new mock instance.
func NewMockBCCAccountServer(ctrl *gomock.Controller) *MockBCCAccountServer {
	mock := &MockBCCAccountServer{ctrl: ctrl}
	mock.recorder = &MockBCCAccountServerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockBCCAccountServer) EXPECT() *MockBCCAccountServerMockRecorder {
	return m.recorder
}

// AcceptTransfer mocks base method.
func (m *MockBCCAccountServer) AcceptTransfer(arg0 context.Context, arg1 *grpc.AcceptTransferRequest) (*grpc.AcceptTransferResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AcceptTransfer", arg0, arg1)
	ret0, _ := ret[0].(*grpc.AcceptTransferResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// AcceptTransfer indicates an expected call of AcceptTransfer.
func (mr *MockBCCAccountServerMockRecorder) AcceptTransfer(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AcceptTransfer", reflect.TypeOf((*MockBCCAccountServer)(nil).AcceptTransfer), arg0, arg1)
}

// DeclineTransfer mocks base method.
func (m *MockBCCAccountServer) DeclineTransfer(arg0 context.Context, arg1 *grpc.DeclineTransferRequest) (*grpc.DeclineTransferResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeclineTransfer", arg0, arg1)
	ret0, _ := ret[0].(*grpc.DeclineTransferResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DeclineTransfer indicates an expected call of DeclineTransfer.
func (mr *MockBCCAccountServerMockRecorder) DeclineTransfer(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeclineTransfer", reflect.TypeOf((*MockBCCAccountServer)(nil).DeclineTransfer), arg0, arg1)
}

// GetAccountBalance mocks base method.
func (m *MockBCCAccountServer) GetAccountBalance(arg0 context.Context, arg1 *grpc.GetAccountBalanceRequest) (*grpc.GetAccountBalanceResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAccountBalance", arg0, arg1)
	ret0, _ := ret[0].(*grpc.GetAccountBalanceResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAccountBalance indicates an expected call of GetAccountBalance.
func (mr *MockBCCAccountServerMockRecorder) GetAccountBalance(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAccountBalance", reflect.TypeOf((*MockBCCAccountServer)(nil).GetAccountBalance), arg0, arg1)
}

// GetAccountIdentifier mocks base method.
func (m *MockBCCAccountServer) GetAccountIdentifier(arg0 context.Context, arg1 *emptypb.Empty) (*grpc.GetAccountIdentifierResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAccountIdentifier", arg0, arg1)
	ret0, _ := ret[0].(*grpc.GetAccountIdentifierResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAccountIdentifier indicates an expected call of GetAccountIdentifier.
func (mr *MockBCCAccountServerMockRecorder) GetAccountIdentifier(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAccountIdentifier", reflect.TypeOf((*MockBCCAccountServer)(nil).GetAccountIdentifier), arg0, arg1)
}

// GetAccountStatement mocks base method.
func (m *MockBCCAccountServer) GetAccountStatement(arg0 context.Context, arg1 *grpc.GetAccountStatementRequest) (*grpc.GetAccountStatementResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAccountStatement", arg0, arg1)
	ret0, _ := ret[0].(*grpc.GetAccountStatementResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAccountStatement indicates an expected call of GetAccountStatement.
func (mr *MockBCCAccountServerMockRecorder) GetAccountStatement(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAccountStatement", reflect.TypeOf((*MockBCCAccountServer)(nil).GetAccountStatement), arg0, arg1)
}

// GetTransferDetails mocks base method.
func (m *MockBCCAccountServer) GetTransferDetails(arg0 context.Context, arg1 *grpc.GetTransferDetailsRequest) (*grpc.GetTransferDetailsResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetTransferDetails", arg0, arg1)
	ret0, _ := ret[0].(*grpc.GetTransferDetailsResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetTransferDetails indicates an expected call of GetTransferDetails.
func (mr *MockBCCAccountServerMockRecorder) GetTransferDetails(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetTransferDetails", reflect.TypeOf((*MockBCCAccountServer)(nil).GetTransferDetails), arg0, arg1)
}

// GetTransfersList mocks base method.
func (m *MockBCCAccountServer) GetTransfersList(arg0 context.Context, arg1 *grpc.GetTransfersListRequest) (*grpc.GetTransfersListResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetTransfersList", arg0, arg1)
	ret0, _ := ret[0].(*grpc.GetTransfersListResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetTransfersList indicates an expected call of GetTransfersList.
func (mr *MockBCCAccountServerMockRecorder) GetTransfersList(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetTransfersList", reflect.TypeOf((*MockBCCAccountServer)(nil).GetTransfersList), arg0, arg1)
}

// MakeMerchantCheck mocks base method.
func (m *MockBCCAccountServer) MakeMerchantCheck(arg0 context.Context, arg1 *grpc.MakeMerchantCheckRequest) (*grpc.MakeMerchantCheckResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "MakeMerchantCheck", arg0, arg1)
	ret0, _ := ret[0].(*grpc.MakeMerchantCheckResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// MakeMerchantCheck indicates an expected call of MakeMerchantCheck.
func (mr *MockBCCAccountServerMockRecorder) MakeMerchantCheck(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "MakeMerchantCheck", reflect.TypeOf((*MockBCCAccountServer)(nil).MakeMerchantCheck), arg0, arg1)
}

// MakeTransfer mocks base method.
func (m *MockBCCAccountServer) MakeTransfer(arg0 context.Context, arg1 *grpc.MakeTransferRequest) (*grpc.MakeTransferResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "MakeTransfer", arg0, arg1)
	ret0, _ := ret[0].(*grpc.MakeTransferResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// MakeTransfer indicates an expected call of MakeTransfer.
func (mr *MockBCCAccountServerMockRecorder) MakeTransfer(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "MakeTransfer", reflect.TypeOf((*MockBCCAccountServer)(nil).MakeTransfer), arg0, arg1)
}

// RedoTransfer mocks base method.
func (m *MockBCCAccountServer) RedoTransfer(arg0 context.Context, arg1 *grpc.RedoTransferRequest) (*grpc.RedoTransferResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "RedoTransfer", arg0, arg1)
	ret0, _ := ret[0].(*grpc.RedoTransferResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// RedoTransfer indicates an expected call of RedoTransfer.
func (mr *MockBCCAccountServerMockRecorder) RedoTransfer(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "RedoTransfer", reflect.TypeOf((*MockBCCAccountServer)(nil).RedoTransfer), arg0, arg1)
}

// mustEmbedUnimplementedBCCAccountServer mocks base method.
func (m *MockBCCAccountServer) mustEmbedUnimplementedBCCAccountServer() {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "mustEmbedUnimplementedBCCAccountServer")
}

// mustEmbedUnimplementedBCCAccountServer indicates an expected call of mustEmbedUnimplementedBCCAccountServer.
func (mr *MockBCCAccountServerMockRecorder) mustEmbedUnimplementedBCCAccountServer() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "mustEmbedUnimplementedBCCAccountServer", reflect.TypeOf((*MockBCCAccountServer)(nil).mustEmbedUnimplementedBCCAccountServer))
}

// MockUnsafeBCCAccountServer is a mock of UnsafeBCCAccountServer interface.
type MockUnsafeBCCAccountServer struct {
	ctrl     *gomock.Controller
	recorder *MockUnsafeBCCAccountServerMockRecorder
}

// MockUnsafeBCCAccountServerMockRecorder is the mock recorder for MockUnsafeBCCAccountServer.
type MockUnsafeBCCAccountServerMockRecorder struct {
	mock *MockUnsafeBCCAccountServer
}

// NewMockUnsafeBCCAccountServer creates a new mock instance.
func NewMockUnsafeBCCAccountServer(ctrl *gomock.Controller) *MockUnsafeBCCAccountServer {
	mock := &MockUnsafeBCCAccountServer{ctrl: ctrl}
	mock.recorder = &MockUnsafeBCCAccountServerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockUnsafeBCCAccountServer) EXPECT() *MockUnsafeBCCAccountServerMockRecorder {
	return m.recorder
}

// mustEmbedUnimplementedBCCAccountServer mocks base method.
func (m *MockUnsafeBCCAccountServer) mustEmbedUnimplementedBCCAccountServer() {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "mustEmbedUnimplementedBCCAccountServer")
}

// mustEmbedUnimplementedBCCAccountServer indicates an expected call of mustEmbedUnimplementedBCCAccountServer.
func (mr *MockUnsafeBCCAccountServerMockRecorder) mustEmbedUnimplementedBCCAccountServer() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "mustEmbedUnimplementedBCCAccountServer", reflect.TypeOf((*MockUnsafeBCCAccountServer)(nil).mustEmbedUnimplementedBCCAccountServer))
}
