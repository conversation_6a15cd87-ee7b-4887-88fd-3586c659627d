// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.5
// 	protoc        v5.29.3
// source: inner/processing/grpc/terminal.proto

package grpc

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	structpb "google.golang.org/protobuf/types/known/structpb"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// запрос на получение данных терминала и его эквайера
type TerminalRequestV1 struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	TerminalId    *uint64                `protobuf:"varint,1,opt,name=terminal_id,json=terminalId" json:"terminal_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *TerminalRequestV1) Reset() {
	*x = TerminalRequestV1{}
	mi := &file_inner_processing_grpc_terminal_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *TerminalRequestV1) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TerminalRequestV1) ProtoMessage() {}

func (x *TerminalRequestV1) ProtoReflect() protoreflect.Message {
	mi := &file_inner_processing_grpc_terminal_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TerminalRequestV1.ProtoReflect.Descriptor instead.
func (*TerminalRequestV1) Descriptor() ([]byte, []int) {
	return file_inner_processing_grpc_terminal_proto_rawDescGZIP(), []int{0}
}

func (x *TerminalRequestV1) GetTerminalId() uint64 {
	if x != nil && x.TerminalId != nil {
		return *x.TerminalId
	}
	return 0
}

type AcquirerData struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	AcquirerId    *uint64                `protobuf:"varint,1,opt,name=acquirer_id,json=acquirerId" json:"acquirer_id,omitempty"`
	AcquirerCode  *string                `protobuf:"bytes,2,opt,name=acquirer_code,json=acquirerCode" json:"acquirer_code,omitempty"`
	Name          *string                `protobuf:"bytes,3,opt,name=name" json:"name,omitempty"`
	BankId        *uint64                `protobuf:"varint,4,opt,name=bank_id,json=bankId" json:"bank_id,omitempty"`
	BankName      *string                `protobuf:"bytes,5,opt,name=bank_name,json=bankName" json:"bank_name,omitempty"`
	Contract      *string                `protobuf:"bytes,6,opt,name=contract" json:"contract,omitempty"`
	Description   *string                `protobuf:"bytes,7,opt,name=description" json:"description,omitempty"`
	CountryId     *uint64                `protobuf:"varint,8,opt,name=country_id,json=countryId" json:"country_id,omitempty"`
	IsActive      *bool                  `protobuf:"varint,9,opt,name=is_active,json=isActive" json:"is_active,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *AcquirerData) Reset() {
	*x = AcquirerData{}
	mi := &file_inner_processing_grpc_terminal_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AcquirerData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AcquirerData) ProtoMessage() {}

func (x *AcquirerData) ProtoReflect() protoreflect.Message {
	mi := &file_inner_processing_grpc_terminal_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AcquirerData.ProtoReflect.Descriptor instead.
func (*AcquirerData) Descriptor() ([]byte, []int) {
	return file_inner_processing_grpc_terminal_proto_rawDescGZIP(), []int{1}
}

func (x *AcquirerData) GetAcquirerId() uint64 {
	if x != nil && x.AcquirerId != nil {
		return *x.AcquirerId
	}
	return 0
}

func (x *AcquirerData) GetAcquirerCode() string {
	if x != nil && x.AcquirerCode != nil {
		return *x.AcquirerCode
	}
	return ""
}

func (x *AcquirerData) GetName() string {
	if x != nil && x.Name != nil {
		return *x.Name
	}
	return ""
}

func (x *AcquirerData) GetBankId() uint64 {
	if x != nil && x.BankId != nil {
		return *x.BankId
	}
	return 0
}

func (x *AcquirerData) GetBankName() string {
	if x != nil && x.BankName != nil {
		return *x.BankName
	}
	return ""
}

func (x *AcquirerData) GetContract() string {
	if x != nil && x.Contract != nil {
		return *x.Contract
	}
	return ""
}

func (x *AcquirerData) GetDescription() string {
	if x != nil && x.Description != nil {
		return *x.Description
	}
	return ""
}

func (x *AcquirerData) GetCountryId() uint64 {
	if x != nil && x.CountryId != nil {
		return *x.CountryId
	}
	return 0
}

func (x *AcquirerData) GetIsActive() bool {
	if x != nil && x.IsActive != nil {
		return *x.IsActive
	}
	return false
}

// ответ на получение данных терминала и его эквайера
type TerminalResponseV1 struct {
	state           protoimpl.MessageState `protogen:"open.v1"`
	TerminalId      *uint64                `protobuf:"varint,1,opt,name=terminal_id,json=terminalId" json:"terminal_id,omitempty"`
	ProjectId       *uint64                `protobuf:"varint,2,opt,name=project_id,json=projectId" json:"project_id,omitempty"`
	Config          *structpb.Struct       `protobuf:"bytes,3,opt,name=config" json:"config,omitempty"`
	Acquirer        *AcquirerData          `protobuf:"bytes,4,opt,name=acquirer" json:"acquirer,omitempty"`
	TwoStageTimeout *uint32                `protobuf:"varint,5,opt,name=two_stage_timeout,json=twoStageTimeout" json:"two_stage_timeout,omitempty"`
	AccountNumber   *string                `protobuf:"bytes,6,opt,name=account_number,json=accountNumber" json:"account_number,omitempty"`
	IsTransit       *bool                  `protobuf:"varint,7,opt,name=is_transit,json=isTransit" json:"is_transit,omitempty"`
	unknownFields   protoimpl.UnknownFields
	sizeCache       protoimpl.SizeCache
}

func (x *TerminalResponseV1) Reset() {
	*x = TerminalResponseV1{}
	mi := &file_inner_processing_grpc_terminal_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *TerminalResponseV1) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TerminalResponseV1) ProtoMessage() {}

func (x *TerminalResponseV1) ProtoReflect() protoreflect.Message {
	mi := &file_inner_processing_grpc_terminal_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TerminalResponseV1.ProtoReflect.Descriptor instead.
func (*TerminalResponseV1) Descriptor() ([]byte, []int) {
	return file_inner_processing_grpc_terminal_proto_rawDescGZIP(), []int{2}
}

func (x *TerminalResponseV1) GetTerminalId() uint64 {
	if x != nil && x.TerminalId != nil {
		return *x.TerminalId
	}
	return 0
}

func (x *TerminalResponseV1) GetProjectId() uint64 {
	if x != nil && x.ProjectId != nil {
		return *x.ProjectId
	}
	return 0
}

func (x *TerminalResponseV1) GetConfig() *structpb.Struct {
	if x != nil {
		return x.Config
	}
	return nil
}

func (x *TerminalResponseV1) GetAcquirer() *AcquirerData {
	if x != nil {
		return x.Acquirer
	}
	return nil
}

func (x *TerminalResponseV1) GetTwoStageTimeout() uint32 {
	if x != nil && x.TwoStageTimeout != nil {
		return *x.TwoStageTimeout
	}
	return 0
}

func (x *TerminalResponseV1) GetAccountNumber() string {
	if x != nil && x.AccountNumber != nil {
		return *x.AccountNumber
	}
	return ""
}

func (x *TerminalResponseV1) GetIsTransit() bool {
	if x != nil && x.IsTransit != nil {
		return *x.IsTransit
	}
	return false
}

// Данные в сервис acquirer для cards
type ActiveTerminalsByProjectRequestV1 struct {
	state             protoimpl.MessageState `protogen:"open.v1"`
	ProjectId         *uint64                `protobuf:"varint,1,opt,name=project_id,json=projectId" json:"project_id,omitempty"`
	TransactionTypeId *uint64                `protobuf:"varint,2,opt,name=transaction_type_id,json=transactionTypeId" json:"transaction_type_id,omitempty"`
	unknownFields     protoimpl.UnknownFields
	sizeCache         protoimpl.SizeCache
}

func (x *ActiveTerminalsByProjectRequestV1) Reset() {
	*x = ActiveTerminalsByProjectRequestV1{}
	mi := &file_inner_processing_grpc_terminal_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ActiveTerminalsByProjectRequestV1) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ActiveTerminalsByProjectRequestV1) ProtoMessage() {}

func (x *ActiveTerminalsByProjectRequestV1) ProtoReflect() protoreflect.Message {
	mi := &file_inner_processing_grpc_terminal_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ActiveTerminalsByProjectRequestV1.ProtoReflect.Descriptor instead.
func (*ActiveTerminalsByProjectRequestV1) Descriptor() ([]byte, []int) {
	return file_inner_processing_grpc_terminal_proto_rawDescGZIP(), []int{3}
}

func (x *ActiveTerminalsByProjectRequestV1) GetProjectId() uint64 {
	if x != nil && x.ProjectId != nil {
		return *x.ProjectId
	}
	return 0
}

func (x *ActiveTerminalsByProjectRequestV1) GetTransactionTypeId() uint64 {
	if x != nil && x.TransactionTypeId != nil {
		return *x.TransactionTypeId
	}
	return 0
}

type ActiveTerminalsByProjectResponseV1 struct {
	state         protoimpl.MessageState        `protogen:"open.v1"`
	Data          []*ActiveTerminalsByProjectV1 `protobuf:"bytes,1,rep,name=Data" json:"Data,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ActiveTerminalsByProjectResponseV1) Reset() {
	*x = ActiveTerminalsByProjectResponseV1{}
	mi := &file_inner_processing_grpc_terminal_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ActiveTerminalsByProjectResponseV1) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ActiveTerminalsByProjectResponseV1) ProtoMessage() {}

func (x *ActiveTerminalsByProjectResponseV1) ProtoReflect() protoreflect.Message {
	mi := &file_inner_processing_grpc_terminal_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ActiveTerminalsByProjectResponseV1.ProtoReflect.Descriptor instead.
func (*ActiveTerminalsByProjectResponseV1) Descriptor() ([]byte, []int) {
	return file_inner_processing_grpc_terminal_proto_rawDescGZIP(), []int{4}
}

func (x *ActiveTerminalsByProjectResponseV1) GetData() []*ActiveTerminalsByProjectV1 {
	if x != nil {
		return x.Data
	}
	return nil
}

type TerminalProjectV1 struct {
	state             protoimpl.MessageState `protogen:"open.v1"`
	Id                *uint64                `protobuf:"varint,1,opt,name=id" json:"id,omitempty"`
	ProjectId         *uint64                `protobuf:"varint,2,opt,name=project_id,json=projectId" json:"project_id,omitempty"`
	TransactionTypeId *uint64                `protobuf:"varint,3,opt,name=transaction_type_id,json=transactionTypeId" json:"transaction_type_id,omitempty"`
	TerminalId        *uint64                `protobuf:"varint,4,opt,name=terminal_id,json=terminalId" json:"terminal_id,omitempty"`
	unknownFields     protoimpl.UnknownFields
	sizeCache         protoimpl.SizeCache
}

func (x *TerminalProjectV1) Reset() {
	*x = TerminalProjectV1{}
	mi := &file_inner_processing_grpc_terminal_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *TerminalProjectV1) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TerminalProjectV1) ProtoMessage() {}

func (x *TerminalProjectV1) ProtoReflect() protoreflect.Message {
	mi := &file_inner_processing_grpc_terminal_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TerminalProjectV1.ProtoReflect.Descriptor instead.
func (*TerminalProjectV1) Descriptor() ([]byte, []int) {
	return file_inner_processing_grpc_terminal_proto_rawDescGZIP(), []int{5}
}

func (x *TerminalProjectV1) GetId() uint64 {
	if x != nil && x.Id != nil {
		return *x.Id
	}
	return 0
}

func (x *TerminalProjectV1) GetProjectId() uint64 {
	if x != nil && x.ProjectId != nil {
		return *x.ProjectId
	}
	return 0
}

func (x *TerminalProjectV1) GetTransactionTypeId() uint64 {
	if x != nil && x.TransactionTypeId != nil {
		return *x.TransactionTypeId
	}
	return 0
}

func (x *TerminalProjectV1) GetTerminalId() uint64 {
	if x != nil && x.TerminalId != nil {
		return *x.TerminalId
	}
	return 0
}

// Данные из сервиса acquirer для cards
type ActiveTerminalsByProjectV1 struct {
	state            protoimpl.MessageState `protogen:"open.v1"`
	Id               *uint64                `protobuf:"varint,1,opt,name=id" json:"id,omitempty"`
	AcquirerId       *uint64                `protobuf:"varint,2,opt,name=acquirer_id,json=acquirerId" json:"acquirer_id,omitempty"`
	Config           *structpb.Struct       `protobuf:"bytes,3,opt,name=config" json:"config,omitempty"`
	Status           *int32                 `protobuf:"varint,4,opt,name=status" json:"status,omitempty"`
	TerminalProjects []*TerminalProjectV1   `protobuf:"bytes,5,rep,name=terminal_projects,json=terminalProjects" json:"terminal_projects,omitempty"`
	unknownFields    protoimpl.UnknownFields
	sizeCache        protoimpl.SizeCache
}

func (x *ActiveTerminalsByProjectV1) Reset() {
	*x = ActiveTerminalsByProjectV1{}
	mi := &file_inner_processing_grpc_terminal_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ActiveTerminalsByProjectV1) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ActiveTerminalsByProjectV1) ProtoMessage() {}

func (x *ActiveTerminalsByProjectV1) ProtoReflect() protoreflect.Message {
	mi := &file_inner_processing_grpc_terminal_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ActiveTerminalsByProjectV1.ProtoReflect.Descriptor instead.
func (*ActiveTerminalsByProjectV1) Descriptor() ([]byte, []int) {
	return file_inner_processing_grpc_terminal_proto_rawDescGZIP(), []int{6}
}

func (x *ActiveTerminalsByProjectV1) GetId() uint64 {
	if x != nil && x.Id != nil {
		return *x.Id
	}
	return 0
}

func (x *ActiveTerminalsByProjectV1) GetAcquirerId() uint64 {
	if x != nil && x.AcquirerId != nil {
		return *x.AcquirerId
	}
	return 0
}

func (x *ActiveTerminalsByProjectV1) GetConfig() *structpb.Struct {
	if x != nil {
		return x.Config
	}
	return nil
}

func (x *ActiveTerminalsByProjectV1) GetStatus() int32 {
	if x != nil && x.Status != nil {
		return *x.Status
	}
	return 0
}

func (x *ActiveTerminalsByProjectV1) GetTerminalProjects() []*TerminalProjectV1 {
	if x != nil {
		return x.TerminalProjects
	}
	return nil
}

type SearchTerminalReqDataV1 struct {
	state             protoimpl.MessageState `protogen:"open.v1"`
	ProjectId         *uint64                `protobuf:"varint,1,opt,name=project_id,json=projectId" json:"project_id,omitempty"`
	TransactionTypeId *uint64                `protobuf:"varint,2,opt,name=transaction_type_id,json=transactionTypeId" json:"transaction_type_id,omitempty"`
	Amount            *float64               `protobuf:"fixed64,3,opt,name=amount" json:"amount,omitempty"`
	IpsId             *uint64                `protobuf:"varint,4,opt,name=ips_id,json=ipsId" json:"ips_id,omitempty"`
	IssuerId          *uint64                `protobuf:"varint,5,opt,name=issuer_id,json=issuerId" json:"issuer_id,omitempty"`
	CountryId         *uint64                `protobuf:"varint,6,opt,name=country_id,json=countryId" json:"country_id,omitempty"`
	MerchantId        *uint64                `protobuf:"varint,7,opt,name=merchant_id,json=merchantId" json:"merchant_id,omitempty"`
	unknownFields     protoimpl.UnknownFields
	sizeCache         protoimpl.SizeCache
}

func (x *SearchTerminalReqDataV1) Reset() {
	*x = SearchTerminalReqDataV1{}
	mi := &file_inner_processing_grpc_terminal_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SearchTerminalReqDataV1) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SearchTerminalReqDataV1) ProtoMessage() {}

func (x *SearchTerminalReqDataV1) ProtoReflect() protoreflect.Message {
	mi := &file_inner_processing_grpc_terminal_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SearchTerminalReqDataV1.ProtoReflect.Descriptor instead.
func (*SearchTerminalReqDataV1) Descriptor() ([]byte, []int) {
	return file_inner_processing_grpc_terminal_proto_rawDescGZIP(), []int{7}
}

func (x *SearchTerminalReqDataV1) GetProjectId() uint64 {
	if x != nil && x.ProjectId != nil {
		return *x.ProjectId
	}
	return 0
}

func (x *SearchTerminalReqDataV1) GetTransactionTypeId() uint64 {
	if x != nil && x.TransactionTypeId != nil {
		return *x.TransactionTypeId
	}
	return 0
}

func (x *SearchTerminalReqDataV1) GetAmount() float64 {
	if x != nil && x.Amount != nil {
		return *x.Amount
	}
	return 0
}

func (x *SearchTerminalReqDataV1) GetIpsId() uint64 {
	if x != nil && x.IpsId != nil {
		return *x.IpsId
	}
	return 0
}

func (x *SearchTerminalReqDataV1) GetIssuerId() uint64 {
	if x != nil && x.IssuerId != nil {
		return *x.IssuerId
	}
	return 0
}

func (x *SearchTerminalReqDataV1) GetCountryId() uint64 {
	if x != nil && x.CountryId != nil {
		return *x.CountryId
	}
	return 0
}

func (x *SearchTerminalReqDataV1) GetMerchantId() uint64 {
	if x != nil && x.MerchantId != nil {
		return *x.MerchantId
	}
	return 0
}

type ExtendedSearchTerminalReqDataV1 struct {
	state             protoimpl.MessageState   `protogen:"open.v1"`
	SearchTerminalReq *SearchTerminalReqDataV1 `protobuf:"bytes,1,opt,name=search_terminal_req,json=searchTerminalReq" json:"search_terminal_req,omitempty"`
	TerminalIds       []uint64                 `protobuf:"varint,2,rep,packed,name=terminal_ids,json=terminalIds" json:"terminal_ids,omitempty"`
	unknownFields     protoimpl.UnknownFields
	sizeCache         protoimpl.SizeCache
}

func (x *ExtendedSearchTerminalReqDataV1) Reset() {
	*x = ExtendedSearchTerminalReqDataV1{}
	mi := &file_inner_processing_grpc_terminal_proto_msgTypes[8]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ExtendedSearchTerminalReqDataV1) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ExtendedSearchTerminalReqDataV1) ProtoMessage() {}

func (x *ExtendedSearchTerminalReqDataV1) ProtoReflect() protoreflect.Message {
	mi := &file_inner_processing_grpc_terminal_proto_msgTypes[8]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ExtendedSearchTerminalReqDataV1.ProtoReflect.Descriptor instead.
func (*ExtendedSearchTerminalReqDataV1) Descriptor() ([]byte, []int) {
	return file_inner_processing_grpc_terminal_proto_rawDescGZIP(), []int{8}
}

func (x *ExtendedSearchTerminalReqDataV1) GetSearchTerminalReq() *SearchTerminalReqDataV1 {
	if x != nil {
		return x.SearchTerminalReq
	}
	return nil
}

func (x *ExtendedSearchTerminalReqDataV1) GetTerminalIds() []uint64 {
	if x != nil {
		return x.TerminalIds
	}
	return nil
}

type SearchTerminalResDataV1 struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	TerminalId    *uint64                `protobuf:"varint,1,opt,name=terminal_id,json=terminalId" json:"terminal_id,omitempty"`
	Config        *structpb.Struct       `protobuf:"bytes,2,opt,name=config" json:"config,omitempty"`
	Acquirer      *AcquirerDataV1        `protobuf:"bytes,3,opt,name=acquirer" json:"acquirer,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SearchTerminalResDataV1) Reset() {
	*x = SearchTerminalResDataV1{}
	mi := &file_inner_processing_grpc_terminal_proto_msgTypes[9]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SearchTerminalResDataV1) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SearchTerminalResDataV1) ProtoMessage() {}

func (x *SearchTerminalResDataV1) ProtoReflect() protoreflect.Message {
	mi := &file_inner_processing_grpc_terminal_proto_msgTypes[9]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SearchTerminalResDataV1.ProtoReflect.Descriptor instead.
func (*SearchTerminalResDataV1) Descriptor() ([]byte, []int) {
	return file_inner_processing_grpc_terminal_proto_rawDescGZIP(), []int{9}
}

func (x *SearchTerminalResDataV1) GetTerminalId() uint64 {
	if x != nil && x.TerminalId != nil {
		return *x.TerminalId
	}
	return 0
}

func (x *SearchTerminalResDataV1) GetConfig() *structpb.Struct {
	if x != nil {
		return x.Config
	}
	return nil
}

func (x *SearchTerminalResDataV1) GetAcquirer() *AcquirerDataV1 {
	if x != nil {
		return x.Acquirer
	}
	return nil
}

type AcquirerDataV1 struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	AcquirerId    *uint64                `protobuf:"varint,1,opt,name=acquirer_id,json=acquirerId" json:"acquirer_id,omitempty"`
	Code          *string                `protobuf:"bytes,2,opt,name=code" json:"code,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *AcquirerDataV1) Reset() {
	*x = AcquirerDataV1{}
	mi := &file_inner_processing_grpc_terminal_proto_msgTypes[10]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AcquirerDataV1) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AcquirerDataV1) ProtoMessage() {}

func (x *AcquirerDataV1) ProtoReflect() protoreflect.Message {
	mi := &file_inner_processing_grpc_terminal_proto_msgTypes[10]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AcquirerDataV1.ProtoReflect.Descriptor instead.
func (*AcquirerDataV1) Descriptor() ([]byte, []int) {
	return file_inner_processing_grpc_terminal_proto_rawDescGZIP(), []int{10}
}

func (x *AcquirerDataV1) GetAcquirerId() uint64 {
	if x != nil && x.AcquirerId != nil {
		return *x.AcquirerId
	}
	return 0
}

func (x *AcquirerDataV1) GetCode() string {
	if x != nil && x.Code != nil {
		return *x.Code
	}
	return ""
}

type GetTerminalsByProjectIdRequestV1 struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	ProjectId     *uint64                `protobuf:"varint,1,opt,name=project_id,json=projectId" json:"project_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetTerminalsByProjectIdRequestV1) Reset() {
	*x = GetTerminalsByProjectIdRequestV1{}
	mi := &file_inner_processing_grpc_terminal_proto_msgTypes[11]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetTerminalsByProjectIdRequestV1) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetTerminalsByProjectIdRequestV1) ProtoMessage() {}

func (x *GetTerminalsByProjectIdRequestV1) ProtoReflect() protoreflect.Message {
	mi := &file_inner_processing_grpc_terminal_proto_msgTypes[11]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetTerminalsByProjectIdRequestV1.ProtoReflect.Descriptor instead.
func (*GetTerminalsByProjectIdRequestV1) Descriptor() ([]byte, []int) {
	return file_inner_processing_grpc_terminal_proto_rawDescGZIP(), []int{11}
}

func (x *GetTerminalsByProjectIdRequestV1) GetProjectId() uint64 {
	if x != nil && x.ProjectId != nil {
		return *x.ProjectId
	}
	return 0
}

type GetTerminalWithJusanResponseV1 struct {
	state           protoimpl.MessageState   `protogen:"open.v1"`
	Terminal        *SearchTerminalResDataV1 `protobuf:"bytes,1,opt,name=terminal" json:"terminal,omitempty"`
	JusanTerminalId *uint64                  `protobuf:"varint,2,opt,name=jusan_terminal_id,json=jusanTerminalId" json:"jusan_terminal_id,omitempty"`
	JusanConfig     *structpb.Struct         `protobuf:"bytes,3,opt,name=jusan_config,json=jusanConfig" json:"jusan_config,omitempty"`
	JusanAcquirer   *AcquirerDataV1          `protobuf:"bytes,4,opt,name=jusan_acquirer,json=jusanAcquirer" json:"jusan_acquirer,omitempty"`
	unknownFields   protoimpl.UnknownFields
	sizeCache       protoimpl.SizeCache
}

func (x *GetTerminalWithJusanResponseV1) Reset() {
	*x = GetTerminalWithJusanResponseV1{}
	mi := &file_inner_processing_grpc_terminal_proto_msgTypes[12]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetTerminalWithJusanResponseV1) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetTerminalWithJusanResponseV1) ProtoMessage() {}

func (x *GetTerminalWithJusanResponseV1) ProtoReflect() protoreflect.Message {
	mi := &file_inner_processing_grpc_terminal_proto_msgTypes[12]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetTerminalWithJusanResponseV1.ProtoReflect.Descriptor instead.
func (*GetTerminalWithJusanResponseV1) Descriptor() ([]byte, []int) {
	return file_inner_processing_grpc_terminal_proto_rawDescGZIP(), []int{12}
}

func (x *GetTerminalWithJusanResponseV1) GetTerminal() *SearchTerminalResDataV1 {
	if x != nil {
		return x.Terminal
	}
	return nil
}

func (x *GetTerminalWithJusanResponseV1) GetJusanTerminalId() uint64 {
	if x != nil && x.JusanTerminalId != nil {
		return *x.JusanTerminalId
	}
	return 0
}

func (x *GetTerminalWithJusanResponseV1) GetJusanConfig() *structpb.Struct {
	if x != nil {
		return x.JusanConfig
	}
	return nil
}

func (x *GetTerminalWithJusanResponseV1) GetJusanAcquirer() *AcquirerDataV1 {
	if x != nil {
		return x.JusanAcquirer
	}
	return nil
}

type RuleByActiveTerminalsReqV1 struct {
	state             protoimpl.MessageState   `protogen:"open.v1"`
	SearchTerminalReq *SearchTerminalReqDataV1 `protobuf:"bytes,1,opt,name=search_terminal_req,json=searchTerminalReq" json:"search_terminal_req,omitempty"`
	TerminalIds       []uint64                 `protobuf:"varint,2,rep,packed,name=terminal_ids,json=terminalIds" json:"terminal_ids,omitempty"`
	unknownFields     protoimpl.UnknownFields
	sizeCache         protoimpl.SizeCache
}

func (x *RuleByActiveTerminalsReqV1) Reset() {
	*x = RuleByActiveTerminalsReqV1{}
	mi := &file_inner_processing_grpc_terminal_proto_msgTypes[13]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RuleByActiveTerminalsReqV1) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RuleByActiveTerminalsReqV1) ProtoMessage() {}

func (x *RuleByActiveTerminalsReqV1) ProtoReflect() protoreflect.Message {
	mi := &file_inner_processing_grpc_terminal_proto_msgTypes[13]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RuleByActiveTerminalsReqV1.ProtoReflect.Descriptor instead.
func (*RuleByActiveTerminalsReqV1) Descriptor() ([]byte, []int) {
	return file_inner_processing_grpc_terminal_proto_rawDescGZIP(), []int{13}
}

func (x *RuleByActiveTerminalsReqV1) GetSearchTerminalReq() *SearchTerminalReqDataV1 {
	if x != nil {
		return x.SearchTerminalReq
	}
	return nil
}

func (x *RuleByActiveTerminalsReqV1) GetTerminalIds() []uint64 {
	if x != nil {
		return x.TerminalIds
	}
	return nil
}

type RuleByActiveTerminals struct {
	state             protoimpl.MessageState `protogen:"open.v1"`
	RuleId            *uint64                `protobuf:"varint,1,opt,name=rule_id,json=ruleId" json:"rule_id,omitempty"`
	ProjectId         *uint64                `protobuf:"varint,2,opt,name=project_id,json=projectId" json:"project_id,omitempty"`
	TransactionTypeId *uint64                `protobuf:"varint,3,opt,name=transaction_type_id,json=transactionTypeId" json:"transaction_type_id,omitempty"`
	IpsId             *uint64                `protobuf:"varint,4,opt,name=ips_id,json=ipsId" json:"ips_id,omitempty"`
	IssuerId          *uint64                `protobuf:"varint,5,opt,name=issuer_id,json=issuerId" json:"issuer_id,omitempty"`
	CountryId         *uint64                `protobuf:"varint,6,opt,name=country_id,json=countryId" json:"country_id,omitempty"`
	AmountFrom        *float64               `protobuf:"fixed64,7,opt,name=amount_from,json=amountFrom" json:"amount_from,omitempty"`
	AmountTo          *float64               `protobuf:"fixed64,8,opt,name=amount_to,json=amountTo" json:"amount_to,omitempty"`
	Weight            *uint64                `protobuf:"varint,9,opt,name=weight" json:"weight,omitempty"`
	IsActive          *bool                  `protobuf:"varint,10,opt,name=is_active,json=isActive" json:"is_active,omitempty"`
	IsBase            *bool                  `protobuf:"varint,11,opt,name=is_base,json=isBase" json:"is_base,omitempty"`
	unknownFields     protoimpl.UnknownFields
	sizeCache         protoimpl.SizeCache
}

func (x *RuleByActiveTerminals) Reset() {
	*x = RuleByActiveTerminals{}
	mi := &file_inner_processing_grpc_terminal_proto_msgTypes[14]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RuleByActiveTerminals) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RuleByActiveTerminals) ProtoMessage() {}

func (x *RuleByActiveTerminals) ProtoReflect() protoreflect.Message {
	mi := &file_inner_processing_grpc_terminal_proto_msgTypes[14]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RuleByActiveTerminals.ProtoReflect.Descriptor instead.
func (*RuleByActiveTerminals) Descriptor() ([]byte, []int) {
	return file_inner_processing_grpc_terminal_proto_rawDescGZIP(), []int{14}
}

func (x *RuleByActiveTerminals) GetRuleId() uint64 {
	if x != nil && x.RuleId != nil {
		return *x.RuleId
	}
	return 0
}

func (x *RuleByActiveTerminals) GetProjectId() uint64 {
	if x != nil && x.ProjectId != nil {
		return *x.ProjectId
	}
	return 0
}

func (x *RuleByActiveTerminals) GetTransactionTypeId() uint64 {
	if x != nil && x.TransactionTypeId != nil {
		return *x.TransactionTypeId
	}
	return 0
}

func (x *RuleByActiveTerminals) GetIpsId() uint64 {
	if x != nil && x.IpsId != nil {
		return *x.IpsId
	}
	return 0
}

func (x *RuleByActiveTerminals) GetIssuerId() uint64 {
	if x != nil && x.IssuerId != nil {
		return *x.IssuerId
	}
	return 0
}

func (x *RuleByActiveTerminals) GetCountryId() uint64 {
	if x != nil && x.CountryId != nil {
		return *x.CountryId
	}
	return 0
}

func (x *RuleByActiveTerminals) GetAmountFrom() float64 {
	if x != nil && x.AmountFrom != nil {
		return *x.AmountFrom
	}
	return 0
}

func (x *RuleByActiveTerminals) GetAmountTo() float64 {
	if x != nil && x.AmountTo != nil {
		return *x.AmountTo
	}
	return 0
}

func (x *RuleByActiveTerminals) GetWeight() uint64 {
	if x != nil && x.Weight != nil {
		return *x.Weight
	}
	return 0
}

func (x *RuleByActiveTerminals) GetIsActive() bool {
	if x != nil && x.IsActive != nil {
		return *x.IsActive
	}
	return false
}

func (x *RuleByActiveTerminals) GetIsBase() bool {
	if x != nil && x.IsBase != nil {
		return *x.IsBase
	}
	return false
}

type RuleByActiveTerminalsResponseV1 struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Rule          *RuleByActiveTerminals `protobuf:"bytes,1,opt,name=rule" json:"rule,omitempty"`
	IsRuleFound   *bool                  `protobuf:"varint,2,opt,name=isRuleFound" json:"isRuleFound,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *RuleByActiveTerminalsResponseV1) Reset() {
	*x = RuleByActiveTerminalsResponseV1{}
	mi := &file_inner_processing_grpc_terminal_proto_msgTypes[15]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RuleByActiveTerminalsResponseV1) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RuleByActiveTerminalsResponseV1) ProtoMessage() {}

func (x *RuleByActiveTerminalsResponseV1) ProtoReflect() protoreflect.Message {
	mi := &file_inner_processing_grpc_terminal_proto_msgTypes[15]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RuleByActiveTerminalsResponseV1.ProtoReflect.Descriptor instead.
func (*RuleByActiveTerminalsResponseV1) Descriptor() ([]byte, []int) {
	return file_inner_processing_grpc_terminal_proto_rawDescGZIP(), []int{15}
}

func (x *RuleByActiveTerminalsResponseV1) GetRule() *RuleByActiveTerminals {
	if x != nil {
		return x.Rule
	}
	return nil
}

func (x *RuleByActiveTerminalsResponseV1) GetIsRuleFound() bool {
	if x != nil && x.IsRuleFound != nil {
		return *x.IsRuleFound
	}
	return false
}

type GetPayInProjectTerminalsReqV1 struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	ProjectId     *uint64                `protobuf:"varint,1,opt,name=project_id,json=projectId" json:"project_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetPayInProjectTerminalsReqV1) Reset() {
	*x = GetPayInProjectTerminalsReqV1{}
	mi := &file_inner_processing_grpc_terminal_proto_msgTypes[16]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetPayInProjectTerminalsReqV1) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetPayInProjectTerminalsReqV1) ProtoMessage() {}

func (x *GetPayInProjectTerminalsReqV1) ProtoReflect() protoreflect.Message {
	mi := &file_inner_processing_grpc_terminal_proto_msgTypes[16]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetPayInProjectTerminalsReqV1.ProtoReflect.Descriptor instead.
func (*GetPayInProjectTerminalsReqV1) Descriptor() ([]byte, []int) {
	return file_inner_processing_grpc_terminal_proto_rawDescGZIP(), []int{16}
}

func (x *GetPayInProjectTerminalsReqV1) GetProjectId() uint64 {
	if x != nil && x.ProjectId != nil {
		return *x.ProjectId
	}
	return 0
}

type GetPayInProjectTerminalsResponseV1 struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	TerminalIds   []uint64               `protobuf:"varint,1,rep,packed,name=terminal_ids,json=terminalIds" json:"terminal_ids,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetPayInProjectTerminalsResponseV1) Reset() {
	*x = GetPayInProjectTerminalsResponseV1{}
	mi := &file_inner_processing_grpc_terminal_proto_msgTypes[17]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetPayInProjectTerminalsResponseV1) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetPayInProjectTerminalsResponseV1) ProtoMessage() {}

func (x *GetPayInProjectTerminalsResponseV1) ProtoReflect() protoreflect.Message {
	mi := &file_inner_processing_grpc_terminal_proto_msgTypes[17]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetPayInProjectTerminalsResponseV1.ProtoReflect.Descriptor instead.
func (*GetPayInProjectTerminalsResponseV1) Descriptor() ([]byte, []int) {
	return file_inner_processing_grpc_terminal_proto_rawDescGZIP(), []int{17}
}

func (x *GetPayInProjectTerminalsResponseV1) GetTerminalIds() []uint64 {
	if x != nil {
		return x.TerminalIds
	}
	return nil
}

var File_inner_processing_grpc_terminal_proto protoreflect.FileDescriptor

var file_inner_processing_grpc_terminal_proto_rawDesc = string([]byte{
	0x0a, 0x24, 0x69, 0x6e, 0x6e, 0x65, 0x72, 0x2f, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69,
	0x6e, 0x67, 0x2f, 0x67, 0x72, 0x70, 0x63, 0x2f, 0x74, 0x65, 0x72, 0x6d, 0x69, 0x6e, 0x61, 0x6c,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x1c, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69,
	0x6e, 0x67, 0x2e, 0x61, 0x63, 0x71, 0x75, 0x69, 0x72, 0x65, 0x72, 0x2e, 0x74, 0x65, 0x72, 0x6d,
	0x69, 0x6e, 0x61, 0x6c, 0x1a, 0x1c, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x73, 0x74, 0x72, 0x75, 0x63, 0x74, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x22, 0x34, 0x0a, 0x11, 0x54, 0x65, 0x72, 0x6d, 0x69, 0x6e, 0x61, 0x6c, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x56, 0x31, 0x12, 0x1f, 0x0a, 0x0b, 0x74, 0x65, 0x72, 0x6d, 0x69,
	0x6e, 0x61, 0x6c, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x0a, 0x74, 0x65,
	0x72, 0x6d, 0x69, 0x6e, 0x61, 0x6c, 0x49, 0x64, 0x22, 0x98, 0x02, 0x0a, 0x0c, 0x41, 0x63, 0x71,
	0x75, 0x69, 0x72, 0x65, 0x72, 0x44, 0x61, 0x74, 0x61, 0x12, 0x1f, 0x0a, 0x0b, 0x61, 0x63, 0x71,
	0x75, 0x69, 0x72, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x0a,
	0x61, 0x63, 0x71, 0x75, 0x69, 0x72, 0x65, 0x72, 0x49, 0x64, 0x12, 0x23, 0x0a, 0x0d, 0x61, 0x63,
	0x71, 0x75, 0x69, 0x72, 0x65, 0x72, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0c, 0x61, 0x63, 0x71, 0x75, 0x69, 0x72, 0x65, 0x72, 0x43, 0x6f, 0x64, 0x65, 0x12,
	0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e,
	0x61, 0x6d, 0x65, 0x12, 0x17, 0x0a, 0x07, 0x62, 0x61, 0x6e, 0x6b, 0x5f, 0x69, 0x64, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x04, 0x52, 0x06, 0x62, 0x61, 0x6e, 0x6b, 0x49, 0x64, 0x12, 0x1b, 0x0a, 0x09,
	0x62, 0x61, 0x6e, 0x6b, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x08, 0x62, 0x61, 0x6e, 0x6b, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x63, 0x6f, 0x6e,
	0x74, 0x72, 0x61, 0x63, 0x74, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x63, 0x6f, 0x6e,
	0x74, 0x72, 0x61, 0x63, 0x74, 0x12, 0x20, 0x0a, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70,
	0x74, 0x69, 0x6f, 0x6e, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x64, 0x65, 0x73, 0x63,
	0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x1d, 0x0a, 0x0a, 0x63, 0x6f, 0x75, 0x6e, 0x74,
	0x72, 0x79, 0x5f, 0x69, 0x64, 0x18, 0x08, 0x20, 0x01, 0x28, 0x04, 0x52, 0x09, 0x63, 0x6f, 0x75,
	0x6e, 0x74, 0x72, 0x79, 0x49, 0x64, 0x12, 0x1b, 0x0a, 0x09, 0x69, 0x73, 0x5f, 0x61, 0x63, 0x74,
	0x69, 0x76, 0x65, 0x18, 0x09, 0x20, 0x01, 0x28, 0x08, 0x52, 0x08, 0x69, 0x73, 0x41, 0x63, 0x74,
	0x69, 0x76, 0x65, 0x22, 0xbf, 0x02, 0x0a, 0x12, 0x54, 0x65, 0x72, 0x6d, 0x69, 0x6e, 0x61, 0x6c,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x56, 0x31, 0x12, 0x1f, 0x0a, 0x0b, 0x74, 0x65,
	0x72, 0x6d, 0x69, 0x6e, 0x61, 0x6c, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52,
	0x0a, 0x74, 0x65, 0x72, 0x6d, 0x69, 0x6e, 0x61, 0x6c, 0x49, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x70,
	0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x04, 0x52,
	0x09, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x49, 0x64, 0x12, 0x2f, 0x0a, 0x06, 0x63, 0x6f,
	0x6e, 0x66, 0x69, 0x67, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x17, 0x2e, 0x67, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x53, 0x74, 0x72,
	0x75, 0x63, 0x74, 0x52, 0x06, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x46, 0x0a, 0x08, 0x61,
	0x63, 0x71, 0x75, 0x69, 0x72, 0x65, 0x72, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2a, 0x2e,
	0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x2e, 0x61, 0x63, 0x71, 0x75, 0x69,
	0x72, 0x65, 0x72, 0x2e, 0x74, 0x65, 0x72, 0x6d, 0x69, 0x6e, 0x61, 0x6c, 0x2e, 0x41, 0x63, 0x71,
	0x75, 0x69, 0x72, 0x65, 0x72, 0x44, 0x61, 0x74, 0x61, 0x52, 0x08, 0x61, 0x63, 0x71, 0x75, 0x69,
	0x72, 0x65, 0x72, 0x12, 0x2a, 0x0a, 0x11, 0x74, 0x77, 0x6f, 0x5f, 0x73, 0x74, 0x61, 0x67, 0x65,
	0x5f, 0x74, 0x69, 0x6d, 0x65, 0x6f, 0x75, 0x74, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0f,
	0x74, 0x77, 0x6f, 0x53, 0x74, 0x61, 0x67, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x6f, 0x75, 0x74, 0x12,
	0x25, 0x0a, 0x0e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x6e, 0x75, 0x6d, 0x62, 0x65,
	0x72, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74,
	0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x12, 0x1d, 0x0a, 0x0a, 0x69, 0x73, 0x5f, 0x74, 0x72, 0x61,
	0x6e, 0x73, 0x69, 0x74, 0x18, 0x07, 0x20, 0x01, 0x28, 0x08, 0x52, 0x09, 0x69, 0x73, 0x54, 0x72,
	0x61, 0x6e, 0x73, 0x69, 0x74, 0x22, 0x72, 0x0a, 0x21, 0x41, 0x63, 0x74, 0x69, 0x76, 0x65, 0x54,
	0x65, 0x72, 0x6d, 0x69, 0x6e, 0x61, 0x6c, 0x73, 0x42, 0x79, 0x50, 0x72, 0x6f, 0x6a, 0x65, 0x63,
	0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x56, 0x31, 0x12, 0x1d, 0x0a, 0x0a, 0x70, 0x72,
	0x6f, 0x6a, 0x65, 0x63, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x09,
	0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x49, 0x64, 0x12, 0x2e, 0x0a, 0x13, 0x74, 0x72, 0x61,
	0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x5f, 0x69, 0x64,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x04, 0x52, 0x11, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74,
	0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x49, 0x64, 0x22, 0x72, 0x0a, 0x22, 0x41, 0x63, 0x74,
	0x69, 0x76, 0x65, 0x54, 0x65, 0x72, 0x6d, 0x69, 0x6e, 0x61, 0x6c, 0x73, 0x42, 0x79, 0x50, 0x72,
	0x6f, 0x6a, 0x65, 0x63, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x56, 0x31, 0x12,
	0x4c, 0x0a, 0x04, 0x44, 0x61, 0x74, 0x61, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x38, 0x2e,
	0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x2e, 0x61, 0x63, 0x71, 0x75, 0x69,
	0x72, 0x65, 0x72, 0x2e, 0x74, 0x65, 0x72, 0x6d, 0x69, 0x6e, 0x61, 0x6c, 0x2e, 0x41, 0x63, 0x74,
	0x69, 0x76, 0x65, 0x54, 0x65, 0x72, 0x6d, 0x69, 0x6e, 0x61, 0x6c, 0x73, 0x42, 0x79, 0x50, 0x72,
	0x6f, 0x6a, 0x65, 0x63, 0x74, 0x56, 0x31, 0x52, 0x04, 0x44, 0x61, 0x74, 0x61, 0x22, 0x93, 0x01,
	0x0a, 0x11, 0x54, 0x65, 0x72, 0x6d, 0x69, 0x6e, 0x61, 0x6c, 0x50, 0x72, 0x6f, 0x6a, 0x65, 0x63,
	0x74, 0x56, 0x31, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52,
	0x02, 0x69, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x5f, 0x69,
	0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x04, 0x52, 0x09, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74,
	0x49, 0x64, 0x12, 0x2e, 0x0a, 0x13, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f,
	0x6e, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x04, 0x52,
	0x11, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65,
	0x49, 0x64, 0x12, 0x1f, 0x0a, 0x0b, 0x74, 0x65, 0x72, 0x6d, 0x69, 0x6e, 0x61, 0x6c, 0x5f, 0x69,
	0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x04, 0x52, 0x0a, 0x74, 0x65, 0x72, 0x6d, 0x69, 0x6e, 0x61,
	0x6c, 0x49, 0x64, 0x22, 0xf4, 0x01, 0x0a, 0x1a, 0x41, 0x63, 0x74, 0x69, 0x76, 0x65, 0x54, 0x65,
	0x72, 0x6d, 0x69, 0x6e, 0x61, 0x6c, 0x73, 0x42, 0x79, 0x50, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74,
	0x56, 0x31, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x02,
	0x69, 0x64, 0x12, 0x1f, 0x0a, 0x0b, 0x61, 0x63, 0x71, 0x75, 0x69, 0x72, 0x65, 0x72, 0x5f, 0x69,
	0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x04, 0x52, 0x0a, 0x61, 0x63, 0x71, 0x75, 0x69, 0x72, 0x65,
	0x72, 0x49, 0x64, 0x12, 0x2f, 0x0a, 0x06, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x17, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x53, 0x74, 0x72, 0x75, 0x63, 0x74, 0x52, 0x06, 0x63, 0x6f,
	0x6e, 0x66, 0x69, 0x67, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x5c, 0x0a, 0x11,
	0x74, 0x65, 0x72, 0x6d, 0x69, 0x6e, 0x61, 0x6c, 0x5f, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74,
	0x73, 0x18, 0x05, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2f, 0x2e, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73,
	0x73, 0x69, 0x6e, 0x67, 0x2e, 0x61, 0x63, 0x71, 0x75, 0x69, 0x72, 0x65, 0x72, 0x2e, 0x74, 0x65,
	0x72, 0x6d, 0x69, 0x6e, 0x61, 0x6c, 0x2e, 0x54, 0x65, 0x72, 0x6d, 0x69, 0x6e, 0x61, 0x6c, 0x50,
	0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x56, 0x31, 0x52, 0x10, 0x74, 0x65, 0x72, 0x6d, 0x69, 0x6e,
	0x61, 0x6c, 0x50, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x73, 0x22, 0xf4, 0x01, 0x0a, 0x17, 0x53,
	0x65, 0x61, 0x72, 0x63, 0x68, 0x54, 0x65, 0x72, 0x6d, 0x69, 0x6e, 0x61, 0x6c, 0x52, 0x65, 0x71,
	0x44, 0x61, 0x74, 0x61, 0x56, 0x31, 0x12, 0x1d, 0x0a, 0x0a, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63,
	0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x09, 0x70, 0x72, 0x6f, 0x6a,
	0x65, 0x63, 0x74, 0x49, 0x64, 0x12, 0x2e, 0x0a, 0x13, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63,
	0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x04, 0x52, 0x11, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x54,
	0x79, 0x70, 0x65, 0x49, 0x64, 0x12, 0x16, 0x0a, 0x06, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x01, 0x52, 0x06, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x15, 0x0a,
	0x06, 0x69, 0x70, 0x73, 0x5f, 0x69, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x04, 0x52, 0x05, 0x69,
	0x70, 0x73, 0x49, 0x64, 0x12, 0x1b, 0x0a, 0x09, 0x69, 0x73, 0x73, 0x75, 0x65, 0x72, 0x5f, 0x69,
	0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x04, 0x52, 0x08, 0x69, 0x73, 0x73, 0x75, 0x65, 0x72, 0x49,
	0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x72, 0x79, 0x5f, 0x69, 0x64, 0x18,
	0x06, 0x20, 0x01, 0x28, 0x04, 0x52, 0x09, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x72, 0x79, 0x49, 0x64,
	0x12, 0x1f, 0x0a, 0x0b, 0x6d, 0x65, 0x72, 0x63, 0x68, 0x61, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18,
	0x07, 0x20, 0x01, 0x28, 0x04, 0x52, 0x0a, 0x6d, 0x65, 0x72, 0x63, 0x68, 0x61, 0x6e, 0x74, 0x49,
	0x64, 0x22, 0xab, 0x01, 0x0a, 0x1f, 0x45, 0x78, 0x74, 0x65, 0x6e, 0x64, 0x65, 0x64, 0x53, 0x65,
	0x61, 0x72, 0x63, 0x68, 0x54, 0x65, 0x72, 0x6d, 0x69, 0x6e, 0x61, 0x6c, 0x52, 0x65, 0x71, 0x44,
	0x61, 0x74, 0x61, 0x56, 0x31, 0x12, 0x65, 0x0a, 0x13, 0x73, 0x65, 0x61, 0x72, 0x63, 0x68, 0x5f,
	0x74, 0x65, 0x72, 0x6d, 0x69, 0x6e, 0x61, 0x6c, 0x5f, 0x72, 0x65, 0x71, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x35, 0x2e, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x2e,
	0x61, 0x63, 0x71, 0x75, 0x69, 0x72, 0x65, 0x72, 0x2e, 0x74, 0x65, 0x72, 0x6d, 0x69, 0x6e, 0x61,
	0x6c, 0x2e, 0x53, 0x65, 0x61, 0x72, 0x63, 0x68, 0x54, 0x65, 0x72, 0x6d, 0x69, 0x6e, 0x61, 0x6c,
	0x52, 0x65, 0x71, 0x44, 0x61, 0x74, 0x61, 0x56, 0x31, 0x52, 0x11, 0x73, 0x65, 0x61, 0x72, 0x63,
	0x68, 0x54, 0x65, 0x72, 0x6d, 0x69, 0x6e, 0x61, 0x6c, 0x52, 0x65, 0x71, 0x12, 0x21, 0x0a, 0x0c,
	0x74, 0x65, 0x72, 0x6d, 0x69, 0x6e, 0x61, 0x6c, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x02, 0x20, 0x03,
	0x28, 0x04, 0x52, 0x0b, 0x74, 0x65, 0x72, 0x6d, 0x69, 0x6e, 0x61, 0x6c, 0x49, 0x64, 0x73, 0x22,
	0xb5, 0x01, 0x0a, 0x17, 0x53, 0x65, 0x61, 0x72, 0x63, 0x68, 0x54, 0x65, 0x72, 0x6d, 0x69, 0x6e,
	0x61, 0x6c, 0x52, 0x65, 0x73, 0x44, 0x61, 0x74, 0x61, 0x56, 0x31, 0x12, 0x1f, 0x0a, 0x0b, 0x74,
	0x65, 0x72, 0x6d, 0x69, 0x6e, 0x61, 0x6c, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04,
	0x52, 0x0a, 0x74, 0x65, 0x72, 0x6d, 0x69, 0x6e, 0x61, 0x6c, 0x49, 0x64, 0x12, 0x2f, 0x0a, 0x06,
	0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x17, 0x2e, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x53,
	0x74, 0x72, 0x75, 0x63, 0x74, 0x52, 0x06, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x48, 0x0a,
	0x08, 0x61, 0x63, 0x71, 0x75, 0x69, 0x72, 0x65, 0x72, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x2c, 0x2e, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x2e, 0x61, 0x63, 0x71,
	0x75, 0x69, 0x72, 0x65, 0x72, 0x2e, 0x74, 0x65, 0x72, 0x6d, 0x69, 0x6e, 0x61, 0x6c, 0x2e, 0x41,
	0x63, 0x71, 0x75, 0x69, 0x72, 0x65, 0x72, 0x44, 0x61, 0x74, 0x61, 0x56, 0x31, 0x52, 0x08, 0x61,
	0x63, 0x71, 0x75, 0x69, 0x72, 0x65, 0x72, 0x22, 0x45, 0x0a, 0x0e, 0x41, 0x63, 0x71, 0x75, 0x69,
	0x72, 0x65, 0x72, 0x44, 0x61, 0x74, 0x61, 0x56, 0x31, 0x12, 0x1f, 0x0a, 0x0b, 0x61, 0x63, 0x71,
	0x75, 0x69, 0x72, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x0a,
	0x61, 0x63, 0x71, 0x75, 0x69, 0x72, 0x65, 0x72, 0x49, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x6f,
	0x64, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x22, 0x41,
	0x0a, 0x20, 0x47, 0x65, 0x74, 0x54, 0x65, 0x72, 0x6d, 0x69, 0x6e, 0x61, 0x6c, 0x73, 0x42, 0x79,
	0x50, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x49, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x56, 0x31, 0x12, 0x1d, 0x0a, 0x0a, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x5f, 0x69, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x09, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x49,
	0x64, 0x22, 0xb0, 0x02, 0x0a, 0x1e, 0x47, 0x65, 0x74, 0x54, 0x65, 0x72, 0x6d, 0x69, 0x6e, 0x61,
	0x6c, 0x57, 0x69, 0x74, 0x68, 0x4a, 0x75, 0x73, 0x61, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x56, 0x31, 0x12, 0x51, 0x0a, 0x08, 0x74, 0x65, 0x72, 0x6d, 0x69, 0x6e, 0x61, 0x6c,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x35, 0x2e, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73,
	0x69, 0x6e, 0x67, 0x2e, 0x61, 0x63, 0x71, 0x75, 0x69, 0x72, 0x65, 0x72, 0x2e, 0x74, 0x65, 0x72,
	0x6d, 0x69, 0x6e, 0x61, 0x6c, 0x2e, 0x53, 0x65, 0x61, 0x72, 0x63, 0x68, 0x54, 0x65, 0x72, 0x6d,
	0x69, 0x6e, 0x61, 0x6c, 0x52, 0x65, 0x73, 0x44, 0x61, 0x74, 0x61, 0x56, 0x31, 0x52, 0x08, 0x74,
	0x65, 0x72, 0x6d, 0x69, 0x6e, 0x61, 0x6c, 0x12, 0x2a, 0x0a, 0x11, 0x6a, 0x75, 0x73, 0x61, 0x6e,
	0x5f, 0x74, 0x65, 0x72, 0x6d, 0x69, 0x6e, 0x61, 0x6c, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x04, 0x52, 0x0f, 0x6a, 0x75, 0x73, 0x61, 0x6e, 0x54, 0x65, 0x72, 0x6d, 0x69, 0x6e, 0x61,
	0x6c, 0x49, 0x64, 0x12, 0x3a, 0x0a, 0x0c, 0x6a, 0x75, 0x73, 0x61, 0x6e, 0x5f, 0x63, 0x6f, 0x6e,
	0x66, 0x69, 0x67, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x17, 0x2e, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x53, 0x74, 0x72, 0x75,
	0x63, 0x74, 0x52, 0x0b, 0x6a, 0x75, 0x73, 0x61, 0x6e, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12,
	0x53, 0x0a, 0x0e, 0x6a, 0x75, 0x73, 0x61, 0x6e, 0x5f, 0x61, 0x63, 0x71, 0x75, 0x69, 0x72, 0x65,
	0x72, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2c, 0x2e, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73,
	0x73, 0x69, 0x6e, 0x67, 0x2e, 0x61, 0x63, 0x71, 0x75, 0x69, 0x72, 0x65, 0x72, 0x2e, 0x74, 0x65,
	0x72, 0x6d, 0x69, 0x6e, 0x61, 0x6c, 0x2e, 0x41, 0x63, 0x71, 0x75, 0x69, 0x72, 0x65, 0x72, 0x44,
	0x61, 0x74, 0x61, 0x56, 0x31, 0x52, 0x0d, 0x6a, 0x75, 0x73, 0x61, 0x6e, 0x41, 0x63, 0x71, 0x75,
	0x69, 0x72, 0x65, 0x72, 0x22, 0xa6, 0x01, 0x0a, 0x1a, 0x52, 0x75, 0x6c, 0x65, 0x42, 0x79, 0x41,
	0x63, 0x74, 0x69, 0x76, 0x65, 0x54, 0x65, 0x72, 0x6d, 0x69, 0x6e, 0x61, 0x6c, 0x73, 0x52, 0x65,
	0x71, 0x56, 0x31, 0x12, 0x65, 0x0a, 0x13, 0x73, 0x65, 0x61, 0x72, 0x63, 0x68, 0x5f, 0x74, 0x65,
	0x72, 0x6d, 0x69, 0x6e, 0x61, 0x6c, 0x5f, 0x72, 0x65, 0x71, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x35, 0x2e, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x2e, 0x61, 0x63,
	0x71, 0x75, 0x69, 0x72, 0x65, 0x72, 0x2e, 0x74, 0x65, 0x72, 0x6d, 0x69, 0x6e, 0x61, 0x6c, 0x2e,
	0x53, 0x65, 0x61, 0x72, 0x63, 0x68, 0x54, 0x65, 0x72, 0x6d, 0x69, 0x6e, 0x61, 0x6c, 0x52, 0x65,
	0x71, 0x44, 0x61, 0x74, 0x61, 0x56, 0x31, 0x52, 0x11, 0x73, 0x65, 0x61, 0x72, 0x63, 0x68, 0x54,
	0x65, 0x72, 0x6d, 0x69, 0x6e, 0x61, 0x6c, 0x52, 0x65, 0x71, 0x12, 0x21, 0x0a, 0x0c, 0x74, 0x65,
	0x72, 0x6d, 0x69, 0x6e, 0x61, 0x6c, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x04,
	0x52, 0x0b, 0x74, 0x65, 0x72, 0x6d, 0x69, 0x6e, 0x61, 0x6c, 0x49, 0x64, 0x73, 0x22, 0xde, 0x02,
	0x0a, 0x15, 0x52, 0x75, 0x6c, 0x65, 0x42, 0x79, 0x41, 0x63, 0x74, 0x69, 0x76, 0x65, 0x54, 0x65,
	0x72, 0x6d, 0x69, 0x6e, 0x61, 0x6c, 0x73, 0x12, 0x17, 0x0a, 0x07, 0x72, 0x75, 0x6c, 0x65, 0x5f,
	0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x06, 0x72, 0x75, 0x6c, 0x65, 0x49, 0x64,
	0x12, 0x1d, 0x0a, 0x0a, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x04, 0x52, 0x09, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x49, 0x64, 0x12,
	0x2e, 0x0a, 0x13, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x74,
	0x79, 0x70, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x04, 0x52, 0x11, 0x74, 0x72,
	0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x49, 0x64, 0x12,
	0x15, 0x0a, 0x06, 0x69, 0x70, 0x73, 0x5f, 0x69, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x04, 0x52,
	0x05, 0x69, 0x70, 0x73, 0x49, 0x64, 0x12, 0x1b, 0x0a, 0x09, 0x69, 0x73, 0x73, 0x75, 0x65, 0x72,
	0x5f, 0x69, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x04, 0x52, 0x08, 0x69, 0x73, 0x73, 0x75, 0x65,
	0x72, 0x49, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x72, 0x79, 0x5f, 0x69,
	0x64, 0x18, 0x06, 0x20, 0x01, 0x28, 0x04, 0x52, 0x09, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x72, 0x79,
	0x49, 0x64, 0x12, 0x1f, 0x0a, 0x0b, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x66, 0x72, 0x6f,
	0x6d, 0x18, 0x07, 0x20, 0x01, 0x28, 0x01, 0x52, 0x0a, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x46,
	0x72, 0x6f, 0x6d, 0x12, 0x1b, 0x0a, 0x09, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x74, 0x6f,
	0x18, 0x08, 0x20, 0x01, 0x28, 0x01, 0x52, 0x08, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x54, 0x6f,
	0x12, 0x16, 0x0a, 0x06, 0x77, 0x65, 0x69, 0x67, 0x68, 0x74, 0x18, 0x09, 0x20, 0x01, 0x28, 0x04,
	0x52, 0x06, 0x77, 0x65, 0x69, 0x67, 0x68, 0x74, 0x12, 0x1b, 0x0a, 0x09, 0x69, 0x73, 0x5f, 0x61,
	0x63, 0x74, 0x69, 0x76, 0x65, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x08, 0x52, 0x08, 0x69, 0x73, 0x41,
	0x63, 0x74, 0x69, 0x76, 0x65, 0x12, 0x17, 0x0a, 0x07, 0x69, 0x73, 0x5f, 0x62, 0x61, 0x73, 0x65,
	0x18, 0x0b, 0x20, 0x01, 0x28, 0x08, 0x52, 0x06, 0x69, 0x73, 0x42, 0x61, 0x73, 0x65, 0x22, 0x8c,
	0x01, 0x0a, 0x1f, 0x52, 0x75, 0x6c, 0x65, 0x42, 0x79, 0x41, 0x63, 0x74, 0x69, 0x76, 0x65, 0x54,
	0x65, 0x72, 0x6d, 0x69, 0x6e, 0x61, 0x6c, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x56, 0x31, 0x12, 0x47, 0x0a, 0x04, 0x72, 0x75, 0x6c, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x33, 0x2e, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x2e, 0x61, 0x63,
	0x71, 0x75, 0x69, 0x72, 0x65, 0x72, 0x2e, 0x74, 0x65, 0x72, 0x6d, 0x69, 0x6e, 0x61, 0x6c, 0x2e,
	0x52, 0x75, 0x6c, 0x65, 0x42, 0x79, 0x41, 0x63, 0x74, 0x69, 0x76, 0x65, 0x54, 0x65, 0x72, 0x6d,
	0x69, 0x6e, 0x61, 0x6c, 0x73, 0x52, 0x04, 0x72, 0x75, 0x6c, 0x65, 0x12, 0x20, 0x0a, 0x0b, 0x69,
	0x73, 0x52, 0x75, 0x6c, 0x65, 0x46, 0x6f, 0x75, 0x6e, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x08,
	0x52, 0x0b, 0x69, 0x73, 0x52, 0x75, 0x6c, 0x65, 0x46, 0x6f, 0x75, 0x6e, 0x64, 0x22, 0x3e, 0x0a,
	0x1d, 0x47, 0x65, 0x74, 0x50, 0x61, 0x79, 0x49, 0x6e, 0x50, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74,
	0x54, 0x65, 0x72, 0x6d, 0x69, 0x6e, 0x61, 0x6c, 0x73, 0x52, 0x65, 0x71, 0x56, 0x31, 0x12, 0x1d,
	0x0a, 0x0a, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x04, 0x52, 0x09, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x49, 0x64, 0x22, 0x47, 0x0a,
	0x22, 0x47, 0x65, 0x74, 0x50, 0x61, 0x79, 0x49, 0x6e, 0x50, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74,
	0x54, 0x65, 0x72, 0x6d, 0x69, 0x6e, 0x61, 0x6c, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x56, 0x31, 0x12, 0x21, 0x0a, 0x0c, 0x74, 0x65, 0x72, 0x6d, 0x69, 0x6e, 0x61, 0x6c, 0x5f,
	0x69, 0x64, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x04, 0x52, 0x0b, 0x74, 0x65, 0x72, 0x6d, 0x69,
	0x6e, 0x61, 0x6c, 0x49, 0x64, 0x73, 0x32, 0xa4, 0x09, 0x0a, 0x08, 0x54, 0x65, 0x72, 0x6d, 0x69,
	0x6e, 0x61, 0x6c, 0x12, 0x76, 0x0a, 0x0f, 0x47, 0x65, 0x74, 0x42, 0x79, 0x54, 0x65, 0x72, 0x6d,
	0x69, 0x6e, 0x61, 0x6c, 0x49, 0x44, 0x12, 0x2f, 0x2e, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73,
	0x69, 0x6e, 0x67, 0x2e, 0x61, 0x63, 0x71, 0x75, 0x69, 0x72, 0x65, 0x72, 0x2e, 0x74, 0x65, 0x72,
	0x6d, 0x69, 0x6e, 0x61, 0x6c, 0x2e, 0x54, 0x65, 0x72, 0x6d, 0x69, 0x6e, 0x61, 0x6c, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x56, 0x31, 0x1a, 0x30, 0x2e, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73,
	0x73, 0x69, 0x6e, 0x67, 0x2e, 0x61, 0x63, 0x71, 0x75, 0x69, 0x72, 0x65, 0x72, 0x2e, 0x74, 0x65,
	0x72, 0x6d, 0x69, 0x6e, 0x61, 0x6c, 0x2e, 0x54, 0x65, 0x72, 0x6d, 0x69, 0x6e, 0x61, 0x6c, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x56, 0x31, 0x22, 0x00, 0x12, 0xa3, 0x01, 0x0a, 0x1c,
	0x46, 0x69, 0x6e, 0x64, 0x41, 0x63, 0x74, 0x69, 0x76, 0x65, 0x54, 0x65, 0x72, 0x6d, 0x69, 0x6e,
	0x61, 0x6c, 0x73, 0x42, 0x79, 0x50, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x12, 0x3f, 0x2e, 0x70,
	0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x2e, 0x61, 0x63, 0x71, 0x75, 0x69, 0x72,
	0x65, 0x72, 0x2e, 0x74, 0x65, 0x72, 0x6d, 0x69, 0x6e, 0x61, 0x6c, 0x2e, 0x41, 0x63, 0x74, 0x69,
	0x76, 0x65, 0x54, 0x65, 0x72, 0x6d, 0x69, 0x6e, 0x61, 0x6c, 0x73, 0x42, 0x79, 0x50, 0x72, 0x6f,
	0x6a, 0x65, 0x63, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x56, 0x31, 0x1a, 0x40, 0x2e,
	0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x2e, 0x61, 0x63, 0x71, 0x75, 0x69,
	0x72, 0x65, 0x72, 0x2e, 0x74, 0x65, 0x72, 0x6d, 0x69, 0x6e, 0x61, 0x6c, 0x2e, 0x41, 0x63, 0x74,
	0x69, 0x76, 0x65, 0x54, 0x65, 0x72, 0x6d, 0x69, 0x6e, 0x61, 0x6c, 0x73, 0x42, 0x79, 0x50, 0x72,
	0x6f, 0x6a, 0x65, 0x63, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x56, 0x31, 0x22,
	0x00, 0x12, 0x80, 0x01, 0x0a, 0x0e, 0x53, 0x65, 0x61, 0x72, 0x63, 0x68, 0x54, 0x65, 0x72, 0x6d,
	0x69, 0x6e, 0x61, 0x6c, 0x12, 0x35, 0x2e, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e,
	0x67, 0x2e, 0x61, 0x63, 0x71, 0x75, 0x69, 0x72, 0x65, 0x72, 0x2e, 0x74, 0x65, 0x72, 0x6d, 0x69,
	0x6e, 0x61, 0x6c, 0x2e, 0x53, 0x65, 0x61, 0x72, 0x63, 0x68, 0x54, 0x65, 0x72, 0x6d, 0x69, 0x6e,
	0x61, 0x6c, 0x52, 0x65, 0x71, 0x44, 0x61, 0x74, 0x61, 0x56, 0x31, 0x1a, 0x35, 0x2e, 0x70, 0x72,
	0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x2e, 0x61, 0x63, 0x71, 0x75, 0x69, 0x72, 0x65,
	0x72, 0x2e, 0x74, 0x65, 0x72, 0x6d, 0x69, 0x6e, 0x61, 0x6c, 0x2e, 0x53, 0x65, 0x61, 0x72, 0x63,
	0x68, 0x54, 0x65, 0x72, 0x6d, 0x69, 0x6e, 0x61, 0x6c, 0x52, 0x65, 0x73, 0x44, 0x61, 0x74, 0x61,
	0x56, 0x31, 0x22, 0x00, 0x12, 0x90, 0x01, 0x0a, 0x16, 0x45, 0x78, 0x74, 0x65, 0x6e, 0x64, 0x65,
	0x64, 0x53, 0x65, 0x61, 0x72, 0x63, 0x68, 0x54, 0x65, 0x72, 0x6d, 0x69, 0x6e, 0x61, 0x6c, 0x12,
	0x3d, 0x2e, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x2e, 0x61, 0x63, 0x71,
	0x75, 0x69, 0x72, 0x65, 0x72, 0x2e, 0x74, 0x65, 0x72, 0x6d, 0x69, 0x6e, 0x61, 0x6c, 0x2e, 0x45,
	0x78, 0x74, 0x65, 0x6e, 0x64, 0x65, 0x64, 0x53, 0x65, 0x61, 0x72, 0x63, 0x68, 0x54, 0x65, 0x72,
	0x6d, 0x69, 0x6e, 0x61, 0x6c, 0x52, 0x65, 0x71, 0x44, 0x61, 0x74, 0x61, 0x56, 0x31, 0x1a, 0x35,
	0x2e, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x2e, 0x61, 0x63, 0x71, 0x75,
	0x69, 0x72, 0x65, 0x72, 0x2e, 0x74, 0x65, 0x72, 0x6d, 0x69, 0x6e, 0x61, 0x6c, 0x2e, 0x53, 0x65,
	0x61, 0x72, 0x63, 0x68, 0x54, 0x65, 0x72, 0x6d, 0x69, 0x6e, 0x61, 0x6c, 0x52, 0x65, 0x73, 0x44,
	0x61, 0x74, 0x61, 0x56, 0x31, 0x22, 0x00, 0x12, 0x9d, 0x01, 0x0a, 0x17, 0x47, 0x65, 0x74, 0x54,
	0x65, 0x72, 0x6d, 0x69, 0x6e, 0x61, 0x6c, 0x73, 0x42, 0x79, 0x50, 0x72, 0x6f, 0x6a, 0x65, 0x63,
	0x74, 0x49, 0x64, 0x12, 0x3e, 0x2e, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67,
	0x2e, 0x61, 0x63, 0x71, 0x75, 0x69, 0x72, 0x65, 0x72, 0x2e, 0x74, 0x65, 0x72, 0x6d, 0x69, 0x6e,
	0x61, 0x6c, 0x2e, 0x47, 0x65, 0x74, 0x54, 0x65, 0x72, 0x6d, 0x69, 0x6e, 0x61, 0x6c, 0x73, 0x42,
	0x79, 0x50, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x49, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x56, 0x31, 0x1a, 0x40, 0x2e, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67,
	0x2e, 0x61, 0x63, 0x71, 0x75, 0x69, 0x72, 0x65, 0x72, 0x2e, 0x74, 0x65, 0x72, 0x6d, 0x69, 0x6e,
	0x61, 0x6c, 0x2e, 0x41, 0x63, 0x74, 0x69, 0x76, 0x65, 0x54, 0x65, 0x72, 0x6d, 0x69, 0x6e, 0x61,
	0x6c, 0x73, 0x42, 0x79, 0x50, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x56, 0x31, 0x22, 0x00, 0x12, 0x8d, 0x01, 0x0a, 0x14, 0x47, 0x65, 0x74, 0x54,
	0x65, 0x72, 0x6d, 0x69, 0x6e, 0x61, 0x6c, 0x57, 0x69, 0x74, 0x68, 0x4a, 0x75, 0x73, 0x61, 0x6e,
	0x12, 0x35, 0x2e, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x2e, 0x61, 0x63,
	0x71, 0x75, 0x69, 0x72, 0x65, 0x72, 0x2e, 0x74, 0x65, 0x72, 0x6d, 0x69, 0x6e, 0x61, 0x6c, 0x2e,
	0x53, 0x65, 0x61, 0x72, 0x63, 0x68, 0x54, 0x65, 0x72, 0x6d, 0x69, 0x6e, 0x61, 0x6c, 0x52, 0x65,
	0x71, 0x44, 0x61, 0x74, 0x61, 0x56, 0x31, 0x1a, 0x3c, 0x2e, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73,
	0x73, 0x69, 0x6e, 0x67, 0x2e, 0x61, 0x63, 0x71, 0x75, 0x69, 0x72, 0x65, 0x72, 0x2e, 0x74, 0x65,
	0x72, 0x6d, 0x69, 0x6e, 0x61, 0x6c, 0x2e, 0x47, 0x65, 0x74, 0x54, 0x65, 0x72, 0x6d, 0x69, 0x6e,
	0x61, 0x6c, 0x57, 0x69, 0x74, 0x68, 0x4a, 0x75, 0x73, 0x61, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x56, 0x31, 0x22, 0x00, 0x12, 0x95, 0x01, 0x0a, 0x18, 0x47, 0x65, 0x74, 0x52,
	0x75, 0x6c, 0x65, 0x42, 0x79, 0x41, 0x63, 0x74, 0x69, 0x76, 0x65, 0x54, 0x65, 0x72, 0x6d, 0x69,
	0x6e, 0x61, 0x6c, 0x73, 0x12, 0x38, 0x2e, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e,
	0x67, 0x2e, 0x61, 0x63, 0x71, 0x75, 0x69, 0x72, 0x65, 0x72, 0x2e, 0x74, 0x65, 0x72, 0x6d, 0x69,
	0x6e, 0x61, 0x6c, 0x2e, 0x52, 0x75, 0x6c, 0x65, 0x42, 0x79, 0x41, 0x63, 0x74, 0x69, 0x76, 0x65,
	0x54, 0x65, 0x72, 0x6d, 0x69, 0x6e, 0x61, 0x6c, 0x73, 0x52, 0x65, 0x71, 0x56, 0x31, 0x1a, 0x3d,
	0x2e, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x2e, 0x61, 0x63, 0x71, 0x75,
	0x69, 0x72, 0x65, 0x72, 0x2e, 0x74, 0x65, 0x72, 0x6d, 0x69, 0x6e, 0x61, 0x6c, 0x2e, 0x52, 0x75,
	0x6c, 0x65, 0x42, 0x79, 0x41, 0x63, 0x74, 0x69, 0x76, 0x65, 0x54, 0x65, 0x72, 0x6d, 0x69, 0x6e,
	0x61, 0x6c, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x56, 0x31, 0x22, 0x00, 0x12,
	0x9b, 0x01, 0x0a, 0x18, 0x47, 0x65, 0x74, 0x50, 0x61, 0x79, 0x49, 0x6e, 0x50, 0x72, 0x6f, 0x6a,
	0x65, 0x63, 0x74, 0x54, 0x65, 0x72, 0x6d, 0x69, 0x6e, 0x61, 0x6c, 0x73, 0x12, 0x3b, 0x2e, 0x70,
	0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x2e, 0x61, 0x63, 0x71, 0x75, 0x69, 0x72,
	0x65, 0x72, 0x2e, 0x74, 0x65, 0x72, 0x6d, 0x69, 0x6e, 0x61, 0x6c, 0x2e, 0x47, 0x65, 0x74, 0x50,
	0x61, 0x79, 0x49, 0x6e, 0x50, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x54, 0x65, 0x72, 0x6d, 0x69,
	0x6e, 0x61, 0x6c, 0x73, 0x52, 0x65, 0x71, 0x56, 0x31, 0x1a, 0x40, 0x2e, 0x70, 0x72, 0x6f, 0x63,
	0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x2e, 0x61, 0x63, 0x71, 0x75, 0x69, 0x72, 0x65, 0x72, 0x2e,
	0x74, 0x65, 0x72, 0x6d, 0x69, 0x6e, 0x61, 0x6c, 0x2e, 0x47, 0x65, 0x74, 0x50, 0x61, 0x79, 0x49,
	0x6e, 0x50, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x54, 0x65, 0x72, 0x6d, 0x69, 0x6e, 0x61, 0x6c,
	0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x56, 0x31, 0x22, 0x00, 0x42, 0x2d, 0x5a,
	0x2b, 0x67, 0x69, 0x74, 0x2e, 0x6c, 0x6f, 0x63, 0x61, 0x6c, 0x2f, 0x73, 0x65, 0x6e, 0x73, 0x69,
	0x74, 0x69, 0x76, 0x65, 0x2f, 0x69, 0x6e, 0x6e, 0x65, 0x72, 0x70, 0x62, 0x2f, 0x70, 0x72, 0x6f,
	0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x2f, 0x67, 0x72, 0x70, 0x63, 0x62, 0x08, 0x65, 0x64,
	0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x70, 0xe8, 0x07,
})

var (
	file_inner_processing_grpc_terminal_proto_rawDescOnce sync.Once
	file_inner_processing_grpc_terminal_proto_rawDescData []byte
)

func file_inner_processing_grpc_terminal_proto_rawDescGZIP() []byte {
	file_inner_processing_grpc_terminal_proto_rawDescOnce.Do(func() {
		file_inner_processing_grpc_terminal_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_inner_processing_grpc_terminal_proto_rawDesc), len(file_inner_processing_grpc_terminal_proto_rawDesc)))
	})
	return file_inner_processing_grpc_terminal_proto_rawDescData
}

var file_inner_processing_grpc_terminal_proto_msgTypes = make([]protoimpl.MessageInfo, 18)
var file_inner_processing_grpc_terminal_proto_goTypes = []any{
	(*TerminalRequestV1)(nil),                  // 0: processing.acquirer.terminal.TerminalRequestV1
	(*AcquirerData)(nil),                       // 1: processing.acquirer.terminal.AcquirerData
	(*TerminalResponseV1)(nil),                 // 2: processing.acquirer.terminal.TerminalResponseV1
	(*ActiveTerminalsByProjectRequestV1)(nil),  // 3: processing.acquirer.terminal.ActiveTerminalsByProjectRequestV1
	(*ActiveTerminalsByProjectResponseV1)(nil), // 4: processing.acquirer.terminal.ActiveTerminalsByProjectResponseV1
	(*TerminalProjectV1)(nil),                  // 5: processing.acquirer.terminal.TerminalProjectV1
	(*ActiveTerminalsByProjectV1)(nil),         // 6: processing.acquirer.terminal.ActiveTerminalsByProjectV1
	(*SearchTerminalReqDataV1)(nil),            // 7: processing.acquirer.terminal.SearchTerminalReqDataV1
	(*ExtendedSearchTerminalReqDataV1)(nil),    // 8: processing.acquirer.terminal.ExtendedSearchTerminalReqDataV1
	(*SearchTerminalResDataV1)(nil),            // 9: processing.acquirer.terminal.SearchTerminalResDataV1
	(*AcquirerDataV1)(nil),                     // 10: processing.acquirer.terminal.AcquirerDataV1
	(*GetTerminalsByProjectIdRequestV1)(nil),   // 11: processing.acquirer.terminal.GetTerminalsByProjectIdRequestV1
	(*GetTerminalWithJusanResponseV1)(nil),     // 12: processing.acquirer.terminal.GetTerminalWithJusanResponseV1
	(*RuleByActiveTerminalsReqV1)(nil),         // 13: processing.acquirer.terminal.RuleByActiveTerminalsReqV1
	(*RuleByActiveTerminals)(nil),              // 14: processing.acquirer.terminal.RuleByActiveTerminals
	(*RuleByActiveTerminalsResponseV1)(nil),    // 15: processing.acquirer.terminal.RuleByActiveTerminalsResponseV1
	(*GetPayInProjectTerminalsReqV1)(nil),      // 16: processing.acquirer.terminal.GetPayInProjectTerminalsReqV1
	(*GetPayInProjectTerminalsResponseV1)(nil), // 17: processing.acquirer.terminal.GetPayInProjectTerminalsResponseV1
	(*structpb.Struct)(nil),                    // 18: google.protobuf.Struct
}
var file_inner_processing_grpc_terminal_proto_depIdxs = []int32{
	18, // 0: processing.acquirer.terminal.TerminalResponseV1.config:type_name -> google.protobuf.Struct
	1,  // 1: processing.acquirer.terminal.TerminalResponseV1.acquirer:type_name -> processing.acquirer.terminal.AcquirerData
	6,  // 2: processing.acquirer.terminal.ActiveTerminalsByProjectResponseV1.Data:type_name -> processing.acquirer.terminal.ActiveTerminalsByProjectV1
	18, // 3: processing.acquirer.terminal.ActiveTerminalsByProjectV1.config:type_name -> google.protobuf.Struct
	5,  // 4: processing.acquirer.terminal.ActiveTerminalsByProjectV1.terminal_projects:type_name -> processing.acquirer.terminal.TerminalProjectV1
	7,  // 5: processing.acquirer.terminal.ExtendedSearchTerminalReqDataV1.search_terminal_req:type_name -> processing.acquirer.terminal.SearchTerminalReqDataV1
	18, // 6: processing.acquirer.terminal.SearchTerminalResDataV1.config:type_name -> google.protobuf.Struct
	10, // 7: processing.acquirer.terminal.SearchTerminalResDataV1.acquirer:type_name -> processing.acquirer.terminal.AcquirerDataV1
	9,  // 8: processing.acquirer.terminal.GetTerminalWithJusanResponseV1.terminal:type_name -> processing.acquirer.terminal.SearchTerminalResDataV1
	18, // 9: processing.acquirer.terminal.GetTerminalWithJusanResponseV1.jusan_config:type_name -> google.protobuf.Struct
	10, // 10: processing.acquirer.terminal.GetTerminalWithJusanResponseV1.jusan_acquirer:type_name -> processing.acquirer.terminal.AcquirerDataV1
	7,  // 11: processing.acquirer.terminal.RuleByActiveTerminalsReqV1.search_terminal_req:type_name -> processing.acquirer.terminal.SearchTerminalReqDataV1
	14, // 12: processing.acquirer.terminal.RuleByActiveTerminalsResponseV1.rule:type_name -> processing.acquirer.terminal.RuleByActiveTerminals
	0,  // 13: processing.acquirer.terminal.Terminal.GetByTerminalID:input_type -> processing.acquirer.terminal.TerminalRequestV1
	3,  // 14: processing.acquirer.terminal.Terminal.FindActiveTerminalsByProject:input_type -> processing.acquirer.terminal.ActiveTerminalsByProjectRequestV1
	7,  // 15: processing.acquirer.terminal.Terminal.SearchTerminal:input_type -> processing.acquirer.terminal.SearchTerminalReqDataV1
	8,  // 16: processing.acquirer.terminal.Terminal.ExtendedSearchTerminal:input_type -> processing.acquirer.terminal.ExtendedSearchTerminalReqDataV1
	11, // 17: processing.acquirer.terminal.Terminal.GetTerminalsByProjectId:input_type -> processing.acquirer.terminal.GetTerminalsByProjectIdRequestV1
	7,  // 18: processing.acquirer.terminal.Terminal.GetTerminalWithJusan:input_type -> processing.acquirer.terminal.SearchTerminalReqDataV1
	13, // 19: processing.acquirer.terminal.Terminal.GetRuleByActiveTerminals:input_type -> processing.acquirer.terminal.RuleByActiveTerminalsReqV1
	16, // 20: processing.acquirer.terminal.Terminal.GetPayInProjectTerminals:input_type -> processing.acquirer.terminal.GetPayInProjectTerminalsReqV1
	2,  // 21: processing.acquirer.terminal.Terminal.GetByTerminalID:output_type -> processing.acquirer.terminal.TerminalResponseV1
	4,  // 22: processing.acquirer.terminal.Terminal.FindActiveTerminalsByProject:output_type -> processing.acquirer.terminal.ActiveTerminalsByProjectResponseV1
	9,  // 23: processing.acquirer.terminal.Terminal.SearchTerminal:output_type -> processing.acquirer.terminal.SearchTerminalResDataV1
	9,  // 24: processing.acquirer.terminal.Terminal.ExtendedSearchTerminal:output_type -> processing.acquirer.terminal.SearchTerminalResDataV1
	4,  // 25: processing.acquirer.terminal.Terminal.GetTerminalsByProjectId:output_type -> processing.acquirer.terminal.ActiveTerminalsByProjectResponseV1
	12, // 26: processing.acquirer.terminal.Terminal.GetTerminalWithJusan:output_type -> processing.acquirer.terminal.GetTerminalWithJusanResponseV1
	15, // 27: processing.acquirer.terminal.Terminal.GetRuleByActiveTerminals:output_type -> processing.acquirer.terminal.RuleByActiveTerminalsResponseV1
	17, // 28: processing.acquirer.terminal.Terminal.GetPayInProjectTerminals:output_type -> processing.acquirer.terminal.GetPayInProjectTerminalsResponseV1
	21, // [21:29] is the sub-list for method output_type
	13, // [13:21] is the sub-list for method input_type
	13, // [13:13] is the sub-list for extension type_name
	13, // [13:13] is the sub-list for extension extendee
	0,  // [0:13] is the sub-list for field type_name
}

func init() { file_inner_processing_grpc_terminal_proto_init() }
func file_inner_processing_grpc_terminal_proto_init() {
	if File_inner_processing_grpc_terminal_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_inner_processing_grpc_terminal_proto_rawDesc), len(file_inner_processing_grpc_terminal_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   18,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_inner_processing_grpc_terminal_proto_goTypes,
		DependencyIndexes: file_inner_processing_grpc_terminal_proto_depIdxs,
		MessageInfos:      file_inner_processing_grpc_terminal_proto_msgTypes,
	}.Build()
	File_inner_processing_grpc_terminal_proto = out.File
	file_inner_processing_grpc_terminal_proto_goTypes = nil
	file_inner_processing_grpc_terminal_proto_depIdxs = nil
}
