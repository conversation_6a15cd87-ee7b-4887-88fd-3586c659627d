// Code generated by protoc-gen-go-mvp. DO NOT EDIT.
// versions:
// - protoc-gen-go-mvp v2.0.1
// created by mvp authors

package grpc

import (
	math "math"
	strings "strings"
)

func (val IntegrationError) Synonym() IntegrationError {
	if _, ok := IntegrationError_name[int32(val)]; ok {
		return val
	}

	return IntegrationError(math.MinInt32)
}

func (val IntegrationError) Int() int {
	return int(val.Synonym())
}

func (val IntegrationError) IntPtr() *int {
	result := int(val.Synonym())
	return &result
}

func (val IntegrationError) Int32() int32 {
	return int32(val.Synonym())
}

func (val IntegrationError) Int32Ptr() *int32 {
	result := int32(val.Synonym())
	return &result
}

func (val IntegrationError) Int64() int64 {
	return int64(val.Synonym())
}

func (val IntegrationError) Int64Ptr() *int64 {
	result := int64(val.Synonym())
	return &result
}

func (val IntegrationError) Uint() uint {
	return uint(val.Synonym())
}

func (val IntegrationError) UintPtr() *uint {
	result := uint(val.Synonym())
	return &result
}

func (val IntegrationError) Uint32() uint32 {
	return uint32(val.Synonym())
}

func (val IntegrationError) Uint32Ptr() *uint32 {
	result := uint32(val.Synonym())
	return &result
}

func (val IntegrationError) Uint64() uint64 {
	return uint64(val.Synonym())
}

func (val IntegrationError) Uint64Ptr() *uint64 {
	result := uint64(val.Synonym())
	return &result
}

func (val IntegrationError) IsKnown() bool {
	return val.Synonym() != IntegrationError(math.MinInt32)
}

func ConvertIntToIntegrationError(in int) IntegrationError {
	return IntegrationError(in).Synonym()
}

func ConvertUintToIntegrationError(in uint) IntegrationError {
	return IntegrationError(in).Synonym()
}

func ConvertInt32ToIntegrationError(in int32) IntegrationError {
	return IntegrationError(in).Synonym()
}

func ConvertUint32ToIntegrationError(in uint32) IntegrationError {
	return IntegrationError(in).Synonym()
}

func ConvertInt64ToIntegrationError(in int64) IntegrationError {
	return IntegrationError(in).Synonym()
}

func ConvertUint64ToIntegrationError(in uint64) IntegrationError {
	return IntegrationError(in).Synonym()
}

var IntegrationError_Lower_value = map[string]IntegrationError{
	"none":                             0,
	"incorrectcardexpdate":             117,
	"invalidcard":                      102,
	"exceedsamountlimit":               103,
	"transactiondeclinedbyissuer":      105,
	"unavailableissuer":                107,
	"incorrectcardnumber":              116,
	"paymentforbiddenformerchant":      109,
	"transactiondeclinedbyacquirer":    106,
	"unavailableacquirer":              108,
	"incorrectcvvcvc":                  115,
	"threedsauthfailed":                101,
	"exceedstransactionfrequencylimit": 104,
	"lostcard":                         113,
	"suspiciousclient":                 119,
	"nonexistentcard":                  112,
	"cardhasexpired":                   114,
	"insufficientfunds":                118,
	"undefinederror":                   100,
	"stolencard":                       110,
	"blockedcard":                      111,
	"userdidnotpay":                    120,
	"invalidthreedsecureparameters":    121,
}

func ConvertStringToIntegrationError(in string) IntegrationError {
	if result, ok := IntegrationError_value[in]; ok {
		return IntegrationError(result)
	}

	if result, ok := IntegrationError_Lower_value[strings.ToLower(in)]; ok {
		return IntegrationError(result)
	}

	return IntegrationError(math.MinInt32)
}

var SliceIntegrationErrorConvert *sliceIntegrationErrorConvert

type sliceIntegrationErrorConvert struct{}

func (*sliceIntegrationErrorConvert) Synonym(in []IntegrationError) []IntegrationError {
	result := make([]IntegrationError, len(in))
	for i, v := range in {
		result[i] = v.Synonym()
	}

	return result
}

func (*sliceIntegrationErrorConvert) Int32(in []IntegrationError) []int32 {
	result := make([]int32, len(in))
	for i, v := range in {
		result[i] = v.Int32()
	}

	return result
}

func (*sliceIntegrationErrorConvert) Uint32(in []IntegrationError) []uint32 {
	result := make([]uint32, len(in))
	for i, v := range in {
		result[i] = v.Uint32()
	}

	return result
}

func (*sliceIntegrationErrorConvert) Uint64(in []IntegrationError) []uint64 {
	result := make([]uint64, len(in))
	for i, v := range in {
		result[i] = v.Uint64()
	}

	return result
}

func (*sliceIntegrationErrorConvert) Int64(in []IntegrationError) []int64 {
	result := make([]int64, len(in))
	for i, v := range in {
		result[i] = v.Int64()
	}

	return result
}

func (*sliceIntegrationErrorConvert) Uint(in []IntegrationError) []uint {
	result := make([]uint, len(in))
	for i, v := range in {
		result[i] = v.Uint()
	}

	return result
}

func (*sliceIntegrationErrorConvert) Int(in []IntegrationError) []int {
	result := make([]int, len(in))
	for i, v := range in {
		result[i] = v.Int()
	}

	return result
}

func (*sliceIntegrationErrorConvert) IsKnown(in []IntegrationError) []bool {
	result := make([]bool, len(in))
	for i, v := range in {
		result[i] = v.IsKnown()
	}

	return result
}

func (*sliceIntegrationErrorConvert) ConvertIntToIntegrationError(in []int) []IntegrationError {
	result := make([]IntegrationError, len(in))
	for i, v := range in {
		result[i] = ConvertIntToIntegrationError(v)
	}

	return result
}

func (*sliceIntegrationErrorConvert) ConvertUintToIntegrationError(in []uint) []IntegrationError {
	result := make([]IntegrationError, len(in))
	for i, v := range in {
		result[i] = ConvertUintToIntegrationError(v)
	}

	return result
}

func (*sliceIntegrationErrorConvert) ConvertInt32ToIntegrationError(in []int32) []IntegrationError {
	result := make([]IntegrationError, len(in))
	for i, v := range in {
		result[i] = ConvertInt32ToIntegrationError(v)
	}

	return result
}

func (*sliceIntegrationErrorConvert) ConvertUint32ToIntegrationError(in []uint32) []IntegrationError {
	result := make([]IntegrationError, len(in))
	for i, v := range in {
		result[i] = ConvertUint32ToIntegrationError(v)
	}

	return result
}

func (*sliceIntegrationErrorConvert) ConvertInt64ToIntegrationError(in []int64) []IntegrationError {
	result := make([]IntegrationError, len(in))
	for i, v := range in {
		result[i] = ConvertInt64ToIntegrationError(v)
	}

	return result
}

func (*sliceIntegrationErrorConvert) ConvertUint64ToIntegrationError(in []uint64) []IntegrationError {
	result := make([]IntegrationError, len(in))
	for i, v := range in {
		result[i] = ConvertUint64ToIntegrationError(v)
	}

	return result
}

func (*sliceIntegrationErrorConvert) ConvertStringToIntegrationError(in []string) []IntegrationError {
	result := make([]IntegrationError, len(in))
	for i, v := range in {
		result[i] = ConvertStringToIntegrationError(v)
	}

	return result
}

func NewIntegrationErrorUsage() *IntegrationErrorUsage {
	return &IntegrationErrorUsage{
		enumMap: map[IntegrationError]bool{
			IntegrationError_None:                             false,
			IntegrationError_IncorrectCardExpDate:             false,
			IntegrationError_InvalidCard:                      false,
			IntegrationError_ExceedsAmountLimit:               false,
			IntegrationError_TransactionDeclinedByIssuer:      false,
			IntegrationError_UnavailableIssuer:                false,
			IntegrationError_IncorrectCardNumber:              false,
			IntegrationError_PaymentForbiddenForMerchant:      false,
			IntegrationError_TransactionDeclinedByAcquirer:    false,
			IntegrationError_UnavailableAcquirer:              false,
			IntegrationError_IncorrectCVVCVC:                  false,
			IntegrationError_ThreeDSAuthFailed:                false,
			IntegrationError_ExceedsTransactionFrequencyLimit: false,
			IntegrationError_LostCard:                         false,
			IntegrationError_SuspiciousClient:                 false,
			IntegrationError_NonExistentCard:                  false,
			IntegrationError_CardHasExpired:                   false,
			IntegrationError_InsufficientFunds:                false,
			IntegrationError_UndefinedError:                   false,
			IntegrationError_StolenCard:                       false,
			IntegrationError_BlockedCard:                      false,
			IntegrationError_UserDidNotPay:                    false,
			IntegrationError_InvalidThreeDSecureParameters:    false,
		},
	}
}

func IsIntegrationError(target IntegrationError, matches ...IntegrationError) bool {
	for _, v := range matches {
		if target == v {
			return true
		}
	}

	return false
}

type IntegrationErrorUsage struct {
	enumMap map[IntegrationError]bool
}

func (u *IntegrationErrorUsage) Use(slice ...IntegrationError) *IntegrationErrorUsage {
	for _, in := range slice {
		u.enumMap[in] = true
	}

	return u
}

func (u *IntegrationErrorUsage) Check() []string {
	var result []string
	for k, v := range u.enumMap {
		if v {
			continue
		}

		result = append(result, k.String())
	}

	return result
}

func (u *IntegrationErrorUsage) IntegrationError_None() IntegrationError {
	u.Use(IntegrationError_None)
	return IntegrationError_None
}

func (u *IntegrationErrorUsage) IntegrationError_IncorrectCardExpDate() IntegrationError {
	u.Use(IntegrationError_IncorrectCardExpDate)
	return IntegrationError_IncorrectCardExpDate
}

func (u *IntegrationErrorUsage) IntegrationError_InvalidCard() IntegrationError {
	u.Use(IntegrationError_InvalidCard)
	return IntegrationError_InvalidCard
}

func (u *IntegrationErrorUsage) IntegrationError_ExceedsAmountLimit() IntegrationError {
	u.Use(IntegrationError_ExceedsAmountLimit)
	return IntegrationError_ExceedsAmountLimit
}

func (u *IntegrationErrorUsage) IntegrationError_TransactionDeclinedByIssuer() IntegrationError {
	u.Use(IntegrationError_TransactionDeclinedByIssuer)
	return IntegrationError_TransactionDeclinedByIssuer
}

func (u *IntegrationErrorUsage) IntegrationError_UnavailableIssuer() IntegrationError {
	u.Use(IntegrationError_UnavailableIssuer)
	return IntegrationError_UnavailableIssuer
}

func (u *IntegrationErrorUsage) IntegrationError_IncorrectCardNumber() IntegrationError {
	u.Use(IntegrationError_IncorrectCardNumber)
	return IntegrationError_IncorrectCardNumber
}

func (u *IntegrationErrorUsage) IntegrationError_PaymentForbiddenForMerchant() IntegrationError {
	u.Use(IntegrationError_PaymentForbiddenForMerchant)
	return IntegrationError_PaymentForbiddenForMerchant
}

func (u *IntegrationErrorUsage) IntegrationError_TransactionDeclinedByAcquirer() IntegrationError {
	u.Use(IntegrationError_TransactionDeclinedByAcquirer)
	return IntegrationError_TransactionDeclinedByAcquirer
}

func (u *IntegrationErrorUsage) IntegrationError_UnavailableAcquirer() IntegrationError {
	u.Use(IntegrationError_UnavailableAcquirer)
	return IntegrationError_UnavailableAcquirer
}

func (u *IntegrationErrorUsage) IntegrationError_IncorrectCVVCVC() IntegrationError {
	u.Use(IntegrationError_IncorrectCVVCVC)
	return IntegrationError_IncorrectCVVCVC
}

func (u *IntegrationErrorUsage) IntegrationError_ThreeDSAuthFailed() IntegrationError {
	u.Use(IntegrationError_ThreeDSAuthFailed)
	return IntegrationError_ThreeDSAuthFailed
}

func (u *IntegrationErrorUsage) IntegrationError_ExceedsTransactionFrequencyLimit() IntegrationError {
	u.Use(IntegrationError_ExceedsTransactionFrequencyLimit)
	return IntegrationError_ExceedsTransactionFrequencyLimit
}

func (u *IntegrationErrorUsage) IntegrationError_LostCard() IntegrationError {
	u.Use(IntegrationError_LostCard)
	return IntegrationError_LostCard
}

func (u *IntegrationErrorUsage) IntegrationError_SuspiciousClient() IntegrationError {
	u.Use(IntegrationError_SuspiciousClient)
	return IntegrationError_SuspiciousClient
}

func (u *IntegrationErrorUsage) IntegrationError_NonExistentCard() IntegrationError {
	u.Use(IntegrationError_NonExistentCard)
	return IntegrationError_NonExistentCard
}

func (u *IntegrationErrorUsage) IntegrationError_CardHasExpired() IntegrationError {
	u.Use(IntegrationError_CardHasExpired)
	return IntegrationError_CardHasExpired
}

func (u *IntegrationErrorUsage) IntegrationError_InsufficientFunds() IntegrationError {
	u.Use(IntegrationError_InsufficientFunds)
	return IntegrationError_InsufficientFunds
}

func (u *IntegrationErrorUsage) IntegrationError_UndefinedError() IntegrationError {
	u.Use(IntegrationError_UndefinedError)
	return IntegrationError_UndefinedError
}

func (u *IntegrationErrorUsage) IntegrationError_StolenCard() IntegrationError {
	u.Use(IntegrationError_StolenCard)
	return IntegrationError_StolenCard
}

func (u *IntegrationErrorUsage) IntegrationError_BlockedCard() IntegrationError {
	u.Use(IntegrationError_BlockedCard)
	return IntegrationError_BlockedCard
}

func (u *IntegrationErrorUsage) IntegrationError_UserDidNotPay() IntegrationError {
	u.Use(IntegrationError_UserDidNotPay)
	return IntegrationError_UserDidNotPay
}

func (u *IntegrationErrorUsage) IntegrationError_InvalidThreeDSecureParameters() IntegrationError {
	u.Use(IntegrationError_InvalidThreeDSecureParameters)
	return IntegrationError_InvalidThreeDSecureParameters
}
