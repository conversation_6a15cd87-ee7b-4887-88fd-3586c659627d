// Code generated by MockGen. DO NOT EDIT.
// Source: project_transactions_grpc.pb.go

// Package grpcmock is a generated GoMock package.
package grpcmock

import (
	context "context"
	reflect "reflect"

	grpc "git.local/sensitive/innerpb/processing/grpc"
	gomock "github.com/golang/mock/gomock"
	grpc0 "google.golang.org/grpc"
)

// MockProjectTransactionsClient is a mock of ProjectTransactionsClient interface.
type MockProjectTransactionsClient struct {
	ctrl     *gomock.Controller
	recorder *MockProjectTransactionsClientMockRecorder
}

// MockProjectTransactionsClientMockRecorder is the mock recorder for MockProjectTransactionsClient.
type MockProjectTransactionsClientMockRecorder struct {
	mock *MockProjectTransactionsClient
}

// NewMockProjectTransactionsClient creates a new mock instance.
func NewMockProjectTransactionsClient(ctrl *gomock.Controller) *MockProjectTransactionsClient {
	mock := &MockProjectTransactionsClient{ctrl: ctrl}
	mock.recorder = &MockProjectTransactionsClientMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockProjectTransactionsClient) EXPECT() *MockProjectTransactionsClientMockRecorder {
	return m.recorder
}

// CheckAmountLimit mocks base method.
func (m *MockProjectTransactionsClient) CheckAmountLimit(ctx context.Context, in *grpc.CheckAmountLimitRequestV1, opts ...grpc0.CallOption) (*grpc.CheckAmountLimitResponseV1, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "CheckAmountLimit", varargs...)
	ret0, _ := ret[0].(*grpc.CheckAmountLimitResponseV1)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CheckAmountLimit indicates an expected call of CheckAmountLimit.
func (mr *MockProjectTransactionsClientMockRecorder) CheckAmountLimit(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CheckAmountLimit", reflect.TypeOf((*MockProjectTransactionsClient)(nil).CheckAmountLimit), varargs...)
}

// CheckAttemptsWithinTimeout mocks base method.
func (m *MockProjectTransactionsClient) CheckAttemptsWithinTimeout(ctx context.Context, in *grpc.CheckAttemptsWithinTimeoutRequestV1, opts ...grpc0.CallOption) (*grpc.CheckAttemptsWithinTimeoutResponseV1, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "CheckAttemptsWithinTimeout", varargs...)
	ret0, _ := ret[0].(*grpc.CheckAttemptsWithinTimeoutResponseV1)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CheckAttemptsWithinTimeout indicates an expected call of CheckAttemptsWithinTimeout.
func (mr *MockProjectTransactionsClientMockRecorder) CheckAttemptsWithinTimeout(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CheckAttemptsWithinTimeout", reflect.TypeOf((*MockProjectTransactionsClient)(nil).CheckAttemptsWithinTimeout), varargs...)
}

// GetTransactionLimit mocks base method.
func (m *MockProjectTransactionsClient) GetTransactionLimit(ctx context.Context, in *grpc.GetTransactionLimitRequestV1, opts ...grpc0.CallOption) (*grpc.GetTransactionLimitResponseV1, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetTransactionLimit", varargs...)
	ret0, _ := ret[0].(*grpc.GetTransactionLimitResponseV1)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetTransactionLimit indicates an expected call of GetTransactionLimit.
func (mr *MockProjectTransactionsClientMockRecorder) GetTransactionLimit(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetTransactionLimit", reflect.TypeOf((*MockProjectTransactionsClient)(nil).GetTransactionLimit), varargs...)
}

// MockProjectTransactionsServer is a mock of ProjectTransactionsServer interface.
type MockProjectTransactionsServer struct {
	ctrl     *gomock.Controller
	recorder *MockProjectTransactionsServerMockRecorder
}

// MockProjectTransactionsServerMockRecorder is the mock recorder for MockProjectTransactionsServer.
type MockProjectTransactionsServerMockRecorder struct {
	mock *MockProjectTransactionsServer
}

// NewMockProjectTransactionsServer creates a new mock instance.
func NewMockProjectTransactionsServer(ctrl *gomock.Controller) *MockProjectTransactionsServer {
	mock := &MockProjectTransactionsServer{ctrl: ctrl}
	mock.recorder = &MockProjectTransactionsServerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockProjectTransactionsServer) EXPECT() *MockProjectTransactionsServerMockRecorder {
	return m.recorder
}

// CheckAmountLimit mocks base method.
func (m *MockProjectTransactionsServer) CheckAmountLimit(arg0 context.Context, arg1 *grpc.CheckAmountLimitRequestV1) (*grpc.CheckAmountLimitResponseV1, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CheckAmountLimit", arg0, arg1)
	ret0, _ := ret[0].(*grpc.CheckAmountLimitResponseV1)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CheckAmountLimit indicates an expected call of CheckAmountLimit.
func (mr *MockProjectTransactionsServerMockRecorder) CheckAmountLimit(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CheckAmountLimit", reflect.TypeOf((*MockProjectTransactionsServer)(nil).CheckAmountLimit), arg0, arg1)
}

// CheckAttemptsWithinTimeout mocks base method.
func (m *MockProjectTransactionsServer) CheckAttemptsWithinTimeout(arg0 context.Context, arg1 *grpc.CheckAttemptsWithinTimeoutRequestV1) (*grpc.CheckAttemptsWithinTimeoutResponseV1, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CheckAttemptsWithinTimeout", arg0, arg1)
	ret0, _ := ret[0].(*grpc.CheckAttemptsWithinTimeoutResponseV1)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CheckAttemptsWithinTimeout indicates an expected call of CheckAttemptsWithinTimeout.
func (mr *MockProjectTransactionsServerMockRecorder) CheckAttemptsWithinTimeout(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CheckAttemptsWithinTimeout", reflect.TypeOf((*MockProjectTransactionsServer)(nil).CheckAttemptsWithinTimeout), arg0, arg1)
}

// GetTransactionLimit mocks base method.
func (m *MockProjectTransactionsServer) GetTransactionLimit(arg0 context.Context, arg1 *grpc.GetTransactionLimitRequestV1) (*grpc.GetTransactionLimitResponseV1, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetTransactionLimit", arg0, arg1)
	ret0, _ := ret[0].(*grpc.GetTransactionLimitResponseV1)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetTransactionLimit indicates an expected call of GetTransactionLimit.
func (mr *MockProjectTransactionsServerMockRecorder) GetTransactionLimit(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetTransactionLimit", reflect.TypeOf((*MockProjectTransactionsServer)(nil).GetTransactionLimit), arg0, arg1)
}

// mustEmbedUnimplementedProjectTransactionsServer mocks base method.
func (m *MockProjectTransactionsServer) mustEmbedUnimplementedProjectTransactionsServer() {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "mustEmbedUnimplementedProjectTransactionsServer")
}

// mustEmbedUnimplementedProjectTransactionsServer indicates an expected call of mustEmbedUnimplementedProjectTransactionsServer.
func (mr *MockProjectTransactionsServerMockRecorder) mustEmbedUnimplementedProjectTransactionsServer() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "mustEmbedUnimplementedProjectTransactionsServer", reflect.TypeOf((*MockProjectTransactionsServer)(nil).mustEmbedUnimplementedProjectTransactionsServer))
}

// MockUnsafeProjectTransactionsServer is a mock of UnsafeProjectTransactionsServer interface.
type MockUnsafeProjectTransactionsServer struct {
	ctrl     *gomock.Controller
	recorder *MockUnsafeProjectTransactionsServerMockRecorder
}

// MockUnsafeProjectTransactionsServerMockRecorder is the mock recorder for MockUnsafeProjectTransactionsServer.
type MockUnsafeProjectTransactionsServerMockRecorder struct {
	mock *MockUnsafeProjectTransactionsServer
}

// NewMockUnsafeProjectTransactionsServer creates a new mock instance.
func NewMockUnsafeProjectTransactionsServer(ctrl *gomock.Controller) *MockUnsafeProjectTransactionsServer {
	mock := &MockUnsafeProjectTransactionsServer{ctrl: ctrl}
	mock.recorder = &MockUnsafeProjectTransactionsServerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockUnsafeProjectTransactionsServer) EXPECT() *MockUnsafeProjectTransactionsServerMockRecorder {
	return m.recorder
}

// mustEmbedUnimplementedProjectTransactionsServer mocks base method.
func (m *MockUnsafeProjectTransactionsServer) mustEmbedUnimplementedProjectTransactionsServer() {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "mustEmbedUnimplementedProjectTransactionsServer")
}

// mustEmbedUnimplementedProjectTransactionsServer indicates an expected call of mustEmbedUnimplementedProjectTransactionsServer.
func (mr *MockUnsafeProjectTransactionsServerMockRecorder) mustEmbedUnimplementedProjectTransactionsServer() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "mustEmbedUnimplementedProjectTransactionsServer", reflect.TypeOf((*MockUnsafeProjectTransactionsServer)(nil).mustEmbedUnimplementedProjectTransactionsServer))
}
