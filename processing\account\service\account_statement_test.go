package service

import (
	"context"
	"crypto/aes"
	"errors"
	"git.local/sensitive/innerpb/processing/grpc/grpcmock"
	"git.local/sensitive/processing/account/repository/databasemocks"
	"git.local/sensitive/testsdk"
	"github.com/golang/mock/gomock"
	"testing"
	"time"

	"github.com/stretchr/testify/require"
	"google.golang.org/protobuf/types/known/structpb"

	"git.local/sensitive/innerpb/processing/grpc"
	"git.local/sensitive/processing/account/model"
	"git.local/sensitive/sdk/dog/e2e"
)

func TestProcessAccountStatement(t *testing.T) {
	testNow := time.Date(2009, time.November, 10, 23, 0, 0, 0, time.UTC)

	defaultCfg, err := structpb.NewStruct(map[string]any{
		"my-name-is": "skrillex",
	})
	if err != nil {
		t.Fatal(err)
	}

	type getAllScheduleOp struct {
		outputErr error
		output    []model.AccountSchedule
	}

	type getAllAccountInfoOp struct {
		isCalled  bool
		output    []model.Account
		outputErr error
	}

	type getNumberOp struct {
		isCalled  bool
		input     string
		output    *model.Account
		outputErr error
	}

	type getAccountStatementOp struct {
		isCalled  bool
		input     *grpc.GetAccountStatementRequest
		output    *grpc.GetAccountStatementResponse
		outputErr error
	}

	type createBalanceHistoryOp struct {
		isCalled  bool
		input     *model.AccountBalanceHistory
		outputErr error
	}

	type createAccountStatementOp struct {
		isCalled  bool
		input     *model.AccountStatement
		outputErr error
	}

	tests := []struct {
		name                   string
		wantErr                error
		getAllSchedule         getAllScheduleOp
		getAllAccountInfo      getAllAccountInfoOp
		getNumber              getNumberOp
		getAccountStatement    getAccountStatementOp
		createBalanceHistory   createBalanceHistoryOp
		createAccountStatement createAccountStatementOp
		appConfig              map[string]any
	}{
		{
			name:    "error_getting_all_schedules",
			wantErr: errors.New("some error"),
			getAllSchedule: getAllScheduleOp{
				outputErr: errors.New("some error"),
				output:    nil,
			},
			appConfig: map[string]any{
				"SYMMETRIC_KEY": badKey,
			},
		},
		{
			name:    "current_hour_is_not_the_same_as_start_hour",
			wantErr: nil,
			getAllSchedule: getAllScheduleOp{
				outputErr: nil,
				output: []model.AccountSchedule{
					{
						StartHour: 22,
						UTC:       0,
					},
				},
			},
			appConfig: map[string]any{
				"SYMMETRIC_KEY": badKey,
			},
		},
		{
			name:    "error_when_getting_all_account_info",
			wantErr: errors.New("some error"),
			getAllSchedule: getAllScheduleOp{
				outputErr: nil,
				output: []model.AccountSchedule{
					{
						StartHour: 23,
						UTC:       0,
					},
				},
			},
			getAllAccountInfo: getAllAccountInfoOp{
				isCalled:  true,
				outputErr: errors.New("some error"),
				output:    nil,
			},
			appConfig: map[string]any{
				"SYMMETRIC_KEY": badKey,
			},
		},
		{
			name:    "error_when_getting_account_number",
			wantErr: errors.New("some error"),
			getAllSchedule: getAllScheduleOp{
				outputErr: nil,
				output: []model.AccountSchedule{
					{
						StartHour: 23,
						UTC:       0,
					},
				},
			},
			getAllAccountInfo: getAllAccountInfoOp{
				isCalled:  true,
				outputErr: nil,
				output: []model.Account{
					{
						ID:              23,
						Number:          "some number",
						CurrencyCode:    "kzt",
						BankCode:        "bcc",
						BankID:          25,
						AccountTypeID:   3,
						EncryptedConfig: defaultEncryptedConfig,
					},
				},
			},
			appConfig: map[string]any{
				"SYMMETRIC_KEY": badKey,
			},
			getNumber: getNumberOp{
				isCalled:  true,
				input:     "some number",
				output:    nil,
				outputErr: errors.New("some error"),
			},
		},
		{
			name:    "error_when_getting_account_number",
			wantErr: aes.KeySizeError(3),
			getAllSchedule: getAllScheduleOp{
				outputErr: nil,
				output: []model.AccountSchedule{
					{
						StartHour: 23,
						UTC:       0,
					},
				},
			},
			getAllAccountInfo: getAllAccountInfoOp{
				isCalled:  true,
				outputErr: nil,
				output: []model.Account{
					{
						ID:              23,
						Number:          "some number",
						CurrencyCode:    "kzt",
						BankCode:        "bcc",
						BankID:          25,
						AccountTypeID:   3,
						EncryptedConfig: defaultEncryptedConfig,
					},
				},
			},
			getNumber: getNumberOp{
				isCalled: true,
				input:    "some number",
				output: &model.Account{
					ID:              23,
					Number:          "some number",
					CurrencyCode:    "kzt",
					BankCode:        "bcc",
					BankID:          25,
					AccountTypeID:   3,
					EncryptedConfig: defaultEncryptedConfig,
				},
				outputErr: nil,
			},
			appConfig: map[string]any{
				"SYMMETRIC_KEY": badKey,
			},
		},
		{
			name:    "get_account_statement_error",
			wantErr: errors.New("some error"),
			getAllSchedule: getAllScheduleOp{
				outputErr: nil,
				output: []model.AccountSchedule{
					{
						StartHour: 23,
						UTC:       0,
					},
				},
			},
			getAllAccountInfo: getAllAccountInfoOp{
				isCalled:  true,
				outputErr: nil,
				output: []model.Account{
					{
						ID:              23,
						Number:          "some number",
						CurrencyCode:    "kzt",
						BankCode:        "bcc",
						BankID:          25,
						AccountTypeID:   3,
						EncryptedConfig: defaultEncryptedConfig,
					},
				},
			},
			getNumber: getNumberOp{
				isCalled: true,
				input:    "some number",
				output: &model.Account{
					ID:              23,
					Number:          "some number",
					CurrencyCode:    "kzt",
					BankCode:        "bcc",
					BankID:          25,
					AccountTypeID:   3,
					EncryptedConfig: defaultEncryptedConfig,
				},
				outputErr: nil,
			},
			getAccountStatement: getAccountStatementOp{
				isCalled: true,
				input: &grpc.GetAccountStatementRequest{
					Account: &grpc.AccountTerminal{
						BankCode: testsdk.Ptr("bcc"),
						Config:   defaultCfg,
					},
					Date: testsdk.Ptr("09.11.2009"),
				},
				output:    nil,
				outputErr: errors.New("some error"),
			},
			appConfig: map[string]any{
				"SYMMETRIC_KEY": normalKey,
			},
		},
		{
			name:    "create_account_balance_history_error",
			wantErr: errors.New("some error"),
			getAllSchedule: getAllScheduleOp{
				outputErr: nil,
				output: []model.AccountSchedule{
					{
						StartHour: 23,
						UTC:       0,
					},
				},
			},
			getAllAccountInfo: getAllAccountInfoOp{
				isCalled:  true,
				outputErr: nil,
				output: []model.Account{
					{
						ID:              23,
						Number:          "some number",
						CurrencyCode:    "kzt",
						BankCode:        "bcc",
						BankID:          25,
						AccountTypeID:   3,
						EncryptedConfig: defaultEncryptedConfig,
					},
				},
			},
			getNumber: getNumberOp{
				isCalled: true,
				input:    "some number",
				output: &model.Account{
					ID:              23,
					Number:          "some number",
					CurrencyCode:    "kzt",
					BankCode:        "bcc",
					BankID:          25,
					AccountTypeID:   3,
					EncryptedConfig: defaultEncryptedConfig,
				},
				outputErr: nil,
			},
			getAccountStatement: getAccountStatementOp{
				isCalled: true,
				input: &grpc.GetAccountStatementRequest{
					Account: &grpc.AccountTerminal{
						BankCode: testsdk.Ptr("bcc"),
						Config:   defaultCfg,
					},
					Date: testsdk.Ptr("09.11.2009"),
				},
				output: &grpc.GetAccountStatementResponse{
					InBalance:  testsdk.Ptr(float64(10000)),
					OutBalance: testsdk.Ptr(float64(10000)),
					Page:       testsdk.Ptr(int32(1)),
					PagesCount: testsdk.Ptr(int32(100)),
					Operations: []*grpc.TransitAccountOperation{
						{
							ExternalReferenceId: testsdk.Ptr("tarlan123"),
							Date:                testsdk.Ptr("bad date"),
							MerchantAccount:     testsdk.Ptr("asd"),
							MerchantBank:        testsdk.Ptr("bcc"),
							MerchantBin:         testsdk.Ptr("some bin"),
							MerchantName:        testsdk.Ptr("debil"),
							Currency:            testsdk.Ptr("kzt"),
							PaymentPurposeCode:  testsdk.Ptr("1"),
							Description:         testsdk.Ptr("some description"),
							OperationType:       testsdk.Ptr(debitOperation),
						},
					},
				},
				outputErr: nil,
			},
			createBalanceHistory: createBalanceHistoryOp{
				isCalled: true,
				input: &model.AccountBalanceHistory{
					InBalance:  10000,
					OutBalance: 10000,
					FinishedAt: testNow.AddDate(0, 0, -1).Truncate(time.Hour), // yesterday
					AccountID:  23,
				},
				outputErr: errors.New("some error"),
			},
			appConfig: map[string]any{
				"SYMMETRIC_KEY": normalKey,
			},
		},
		{
			name:    "parse_error_with_debit_operation",
			wantErr: &time.ParseError{},
			getAllSchedule: getAllScheduleOp{
				outputErr: nil,
				output: []model.AccountSchedule{
					{
						StartHour: 23,
						UTC:       0,
					},
				},
			},
			getAllAccountInfo: getAllAccountInfoOp{
				isCalled:  true,
				outputErr: nil,
				output: []model.Account{
					{
						ID:              23,
						Number:          "some number",
						CurrencyCode:    "kzt",
						BankCode:        "bcc",
						BankID:          25,
						AccountTypeID:   3,
						EncryptedConfig: defaultEncryptedConfig,
					},
				},
			},
			getNumber: getNumberOp{
				isCalled: true,
				input:    "some number",
				output: &model.Account{
					ID:              23,
					Number:          "some number",
					CurrencyCode:    "kzt",
					BankCode:        "bcc",
					BankID:          25,
					AccountTypeID:   3,
					EncryptedConfig: defaultEncryptedConfig,
				},
				outputErr: nil,
			},
			getAccountStatement: getAccountStatementOp{
				isCalled: true,
				input: &grpc.GetAccountStatementRequest{
					Account: &grpc.AccountTerminal{
						BankCode: testsdk.Ptr("bcc"),
						Config:   defaultCfg,
					},
					Date: testsdk.Ptr("09.11.2009"),
				},
				output: &grpc.GetAccountStatementResponse{
					InBalance:  testsdk.Ptr(float64(10000)),
					OutBalance: testsdk.Ptr(float64(10000)),
					Page:       testsdk.Ptr(int32(1)),
					PagesCount: testsdk.Ptr(int32(100)),
					Operations: []*grpc.TransitAccountOperation{
						{
							ExternalReferenceId: testsdk.Ptr("tarlan123"),
							Date:                testsdk.Ptr("bad date"),
							MerchantAccount:     testsdk.Ptr("asd"),
							MerchantBank:        testsdk.Ptr("bcc"),
							MerchantBin:         testsdk.Ptr("some bin"),
							MerchantName:        testsdk.Ptr("debil"),
							Currency:            testsdk.Ptr("kzt"),
							PaymentPurposeCode:  testsdk.Ptr("1"),
							Description:         testsdk.Ptr("some description"),
							OperationType:       testsdk.Ptr(debitOperation),
						},
					},
				},
				outputErr: nil,
			},
			createBalanceHistory: createBalanceHistoryOp{
				isCalled: true,
				input: &model.AccountBalanceHistory{
					InBalance:  10000,
					OutBalance: 10000,
					FinishedAt: testNow.AddDate(0, 0, -1).Truncate(time.Hour), // yesterday
					AccountID:  23,
				},
				outputErr: nil,
			},
			appConfig: map[string]any{
				"SYMMETRIC_KEY": normalKey,
			},
		},
		{
			name:    "create_account_statement_error",
			wantErr: errors.New("some error"),
			getAllSchedule: getAllScheduleOp{
				outputErr: nil,
				output: []model.AccountSchedule{
					{
						StartHour: 23,
						UTC:       0,
					},
				},
			},
			getAllAccountInfo: getAllAccountInfoOp{
				isCalled:  true,
				outputErr: nil,
				output: []model.Account{
					{
						ID:              23,
						Number:          "some number",
						CurrencyCode:    "kzt",
						BankCode:        "bcc",
						BankID:          25,
						AccountTypeID:   3,
						EncryptedConfig: defaultEncryptedConfig,
					},
				},
			},
			getNumber: getNumberOp{
				isCalled: true,
				input:    "some number",
				output: &model.Account{
					ID:              23,
					Number:          "some number",
					CurrencyCode:    "kzt",
					BankCode:        "bcc",
					BankID:          25,
					AccountTypeID:   3,
					EncryptedConfig: defaultEncryptedConfig,
				},
				outputErr: nil,
			},
			getAccountStatement: getAccountStatementOp{
				isCalled: true,
				input: &grpc.GetAccountStatementRequest{
					Account: &grpc.AccountTerminal{
						BankCode: testsdk.Ptr("bcc"),
						Config:   defaultCfg,
					},
					Date: testsdk.Ptr("09.11.2009"),
				},
				output: &grpc.GetAccountStatementResponse{
					InBalance:  testsdk.Ptr(float64(10000)),
					OutBalance: testsdk.Ptr(float64(10000)),
					Page:       testsdk.Ptr(int32(1)),
					PagesCount: testsdk.Ptr(int32(100)),
					Operations: []*grpc.TransitAccountOperation{
						{
							ExternalReferenceId: testsdk.Ptr("tarlan123"),
							Date:                testsdk.Ptr("2009-11-10 23:00:00"),
							MerchantAccount:     testsdk.Ptr("asd"),
							MerchantBank:        testsdk.Ptr("bcc"),
							MerchantBin:         testsdk.Ptr("some bin"),
							MerchantName:        testsdk.Ptr("debil"),
							Currency:            testsdk.Ptr("kzt"),
							PaymentPurposeCode:  testsdk.Ptr("1"),
							Description:         testsdk.Ptr("some description"),
							OperationType:       testsdk.Ptr(creditOperation),
							Amount:              testsdk.Ptr(float64(33)),
						},
					},
				},
				outputErr: nil,
			},
			createBalanceHistory: createBalanceHistoryOp{
				isCalled: true,
				input: &model.AccountBalanceHistory{
					InBalance:  10000,
					OutBalance: 10000,
					FinishedAt: testNow.AddDate(0, 0, -1).Truncate(time.Hour), // yesterday
					AccountID:  23,
				},
				outputErr: nil,
			},
			createAccountStatement: createAccountStatementOp{
				isCalled: true,
				input: &model.AccountStatement{
					ExternalReferenceID: "tarlan123",
					MerchantAccount:     "asd",
					MerchantBankIDCode:  "bcc",
					MerchantBIN:         "some bin",
					MerchantName:        "debil",
					Currency:            "kzt",
					PaymentPurposeCode:  "1",
					Description:         "some description",
					FinishedAt:          testNow,
					CreditTurnover:      33,
					DebitTurnover:       0,
					BeneficiaryCode:     bankKbe,
					AccountID:           23,
				},
				outputErr: errors.New("some error"),
			},
			appConfig: map[string]any{
				"SYMMETRIC_KEY": normalKey,
			},
		},
		{
			name:    "success",
			wantErr: nil,
			getAllSchedule: getAllScheduleOp{
				outputErr: nil,
				output: []model.AccountSchedule{
					{
						StartHour: 23,
						UTC:       0,
					},
				},
			},
			getAllAccountInfo: getAllAccountInfoOp{
				isCalled:  true,
				outputErr: nil,
				output: []model.Account{
					{
						ID:              23,
						Number:          "some number",
						CurrencyCode:    "kzt",
						BankCode:        "bcc",
						BankID:          25,
						AccountTypeID:   3,
						EncryptedConfig: defaultEncryptedConfig,
					},
				},
			},
			getNumber: getNumberOp{
				isCalled: true,
				input:    "some number",
				output: &model.Account{
					ID:              23,
					Number:          "some number",
					CurrencyCode:    "kzt",
					BankCode:        "bcc",
					BankID:          25,
					AccountTypeID:   3,
					EncryptedConfig: defaultEncryptedConfig,
				},
				outputErr: nil,
			},
			getAccountStatement: getAccountStatementOp{
				isCalled: true,
				input: &grpc.GetAccountStatementRequest{
					Account: &grpc.AccountTerminal{
						BankCode: testsdk.Ptr("bcc"),
						Config:   defaultCfg,
					},
					Date: testsdk.Ptr("09.11.2009"),
				},
				output: &grpc.GetAccountStatementResponse{
					InBalance:  testsdk.Ptr(float64(10000)),
					OutBalance: testsdk.Ptr(float64(10000)),
					Page:       testsdk.Ptr(int32(1)),
					PagesCount: testsdk.Ptr(int32(100)),
					Operations: []*grpc.TransitAccountOperation{
						{
							ExternalReferenceId: testsdk.Ptr("tarlan123"),
							Date:                testsdk.Ptr("2009-11-10 23:00:00"),
							MerchantAccount:     testsdk.Ptr("asd"),
							MerchantBank:        testsdk.Ptr("bcc"),
							MerchantBin:         testsdk.Ptr("some bin"),
							MerchantName:        testsdk.Ptr("debil"),
							Currency:            testsdk.Ptr("kzt"),
							PaymentPurposeCode:  testsdk.Ptr("1"),
							Description:         testsdk.Ptr("some description"),
							OperationType:       testsdk.Ptr(creditOperation),
							Amount:              testsdk.Ptr(float64(33)),
						},
					},
				},
				outputErr: nil,
			},
			createBalanceHistory: createBalanceHistoryOp{
				isCalled: true,
				input: &model.AccountBalanceHistory{
					InBalance:  10000,
					OutBalance: 10000,
					FinishedAt: testNow.AddDate(0, 0, -1).Truncate(time.Hour), // yesterday
					AccountID:  23,
				},
				outputErr: nil,
			},
			createAccountStatement: createAccountStatementOp{
				isCalled: true,
				input: &model.AccountStatement{
					ExternalReferenceID: "tarlan123",
					MerchantAccount:     "asd",
					MerchantBankIDCode:  "bcc",
					MerchantBIN:         "some bin",
					MerchantName:        "debil",
					Currency:            "kzt",
					PaymentPurposeCode:  "1",
					Description:         "some description",
					FinishedAt:          testNow,
					CreditTurnover:      33,
					DebitTurnover:       0,
					BeneficiaryCode:     bankKbe,
					AccountID:           23,
				},
				outputErr: nil,
			},
			appConfig: map[string]any{
				"SYMMETRIC_KEY": normalKey,
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctrl := gomock.NewController(t)
			defer ctrl.Finish()

			accountScheduleDBMock := databasemocks.NewMockAccountScheduler(ctrl)
			accountInfoDBMock := databasemocks.NewMockAccountInformer(ctrl)
			accountBalanceHistoryDBMock := databasemocks.NewMockAccountBalanceHistorier(ctrl)
			accountStatementDBMock := databasemocks.NewMockAccountStatementer(ctrl)
			multiaccountingCliMock := grpcmock.NewMockMultiaccountingClient(ctrl)

			c := context.Background()
			ctx, cancel := context.WithCancel(c)
			e2e.InitAppE2E(t, ctx, cancel, nil, nil, tt.appConfig)

			accountScheduleDBMock.EXPECT().GetAll(
				gomock.Any(),
			).Return(
				tt.getAllSchedule.output,
				tt.getAllSchedule.outputErr,
			).Times(1)

			if tt.getAllAccountInfo.isCalled {
				accountInfoDBMock.EXPECT().GetAll(
					gomock.Any(),
				).Return(
					tt.getAllAccountInfo.output,
					tt.getAllAccountInfo.outputErr,
				).Times(1)
			}

			if tt.getNumber.isCalled {
				accountInfoDBMock.EXPECT().GetByNumber(
					gomock.Any(),
					tt.getNumber.input,
				).Return(
					tt.getNumber.output,
					tt.getNumber.outputErr,
				).Times(1)
			}

			if tt.getAccountStatement.isCalled {
				multiaccountingCliMock.EXPECT().GetAccountStatement(
					gomock.Any(),
					tt.getAccountStatement.input,
				).Return(tt.getAccountStatement.output, tt.getAccountStatement.outputErr).Times(1)
			}

			if tt.createBalanceHistory.isCalled {
				accountBalanceHistoryDBMock.EXPECT().Create(
					gomock.Any(),
					tt.createBalanceHistory.input,
				).Return(tt.createBalanceHistory.outputErr).Times(1)
			}

			if tt.createAccountStatement.isCalled {
				accountStatementDBMock.EXPECT().Create(
					gomock.Any(),
					tt.createAccountStatement.input,
				).Return(tt.createAccountStatement.outputErr).Times(1)
			}

			s := AccountStatementService{
				accountScheduleRepo:       accountScheduleDBMock,
				accountInfoRepo:           accountInfoDBMock,
				accountBalanceHistoryRepo: accountBalanceHistoryDBMock,
				accountStatementRepo:      accountStatementDBMock,
				multiaccountingClient:     multiaccountingCliMock,
				getCurrentTime: func() time.Time {
					return testNow
				},
			}

			err = s.ProcessAccountStatement(context.Background())
			if tt.wantErr != nil {
				require.Error(t, err)
				require.ErrorAs(t, err, &tt.wantErr)
			} else {
				require.NoError(t, err)
			}
		})
	}
}
