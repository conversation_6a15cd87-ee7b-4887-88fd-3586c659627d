package database

import (
	"context"
	"errors"

	"gorm.io/gorm"

	"git.local/sensitive/innerpb/processing/goerr"
	"git.local/sensitive/pkg/middlewares"
	"git.local/sensitive/processing/acquirer/model"
	"git.local/sensitive/sdk/dog"
)

type IpsDB struct {
	db *gorm.DB
}

func NewIpsDB(db *gorm.DB) Ipser {
	return &IpsDB{
		db: db,
	}
}

func (r *IpsDB) Create(ctx context.Context, ips *model.Ips) (err error) {
	ctx, span := dog.CreateSpan(ctx, "IpsDB_Create")
	defer span.End()

	err = r.db.WithContext(ctx).Create(&ips).Error
	if err != nil {
		return goerr.ErrDbUnexpected.WithErr(err).WithCtx(ctx)
	}

	return nil
}

func (r *IpsDB) GetAll(ctx context.Context, pagination *middlewares.PaginationInfo) (_ []*model.Ips, err error) {
	ctx, span := dog.CreateSpan(ctx, "IpsDB_GetAll")
	defer span.End()

	result := make([]*model.Ips, 0)

	request := r.db.WithContext(ctx).
		Model(&model.Ips{})

	if pagination.Pagination {
		request = request.Offset((pagination.Page - 1) * pagination.PerPage).Limit(pagination.PerPage)
	}

	if err := request.Find(&result).Error; err != nil {
		return nil, goerr.ErrDbUnexpected.WithErr(err).WithCtx(ctx)
	}

	return result, nil
}

func (r *IpsDB) Update(ctx context.Context, id uint64, data *model.Ips) (err error) {
	ctx, span := dog.CreateSpan(ctx, "IpsDB_Update")
	defer span.End()

	err = r.db.WithContext(ctx).
		Model(&model.Ips{}).
		Where("id = ?", id).
		Updates(model.Ips{
			Name: data.Name,
		}).First(&model.Ips{}).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return goerr.ErrIpsNotFound.WithCtx(ctx)
		}

		return goerr.ErrDbUnexpected.WithErr(err).WithCtx(ctx)
	}

	return nil
}

func (r *IpsDB) Delete(ctx context.Context, id uint64) (err error) {
	ctx, span := dog.CreateSpan(ctx, "IpsDB_Delete")
	defer span.End()

	err = r.db.WithContext(ctx).
		Where("id = ?", id).
		First(&model.Ips{}).
		Delete(&model.Ips{}, id).
		Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return goerr.ErrIpsNotFound
		}

		return goerr.ErrDbUnexpected.WithErr(err).WithCtx(ctx)
	}

	return nil
}

func (r *IpsDB) GetByID(ctx context.Context, id uint64) (_ model.Ips, err error) {
	ctx, span := dog.CreateSpan(ctx, "IpsDB_GetByID")
	defer span.End()

	ips := new(model.Ips)

	if err := r.db.WithContext(ctx).Where("id = ?", id).First(ips).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return model.Ips{}, goerr.ErrIpsNotFound
		}

		return model.Ips{}, err
	}

	return *ips, nil
}
