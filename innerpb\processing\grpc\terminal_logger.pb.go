// Code generated by protoc-gen-go-mvp. DO NOT EDIT.
// versions:
// - protoc-gen-go-mvp v2.0.1
// created by mvp authors

package grpc

import (
	context "context"
	fmt "fmt"
	cntx "git.local/sensitive/mvp/pkg/cntx"
	ctxzap "github.com/grpc-ecosystem/go-grpc-middleware/logging/zap/ctxzap"
	zap "go.uber.org/zap"
	grpc "google.golang.org/grpc"
	structpb "google.golang.org/protobuf/types/known/structpb"
)

func file_inner_processing_grpc_terminal_proto_string_structpb_Value(
	label string,
	in map[string]*structpb.Value,
) zap.Field {
	var res []zap.Field
	for key, val := range in {
		res = append(res, file_inner_processing_grpc_terminal_proto_message_ValueToZap(fmt.Sprint(key), val))
	}

	return zap.Dict(label, res...)
}

func file_inner_processing_grpc_terminal_proto_message_ListValueToZap(
	label string,
	in *structpb.ListValue,
) zap.Field {
	if in == nil {
		return zap.Reflect(label, in)
	}

	return zap.Dict(
		label,
		file_inner_processing_grpc_terminal_proto_message_ValueSliceToZap("Values", in.GetValues()),
	)
}

func file_inner_processing_grpc_terminal_proto_enum_NullValueToZap(
	label string,
	in structpb.NullValue,
) zap.Field {
	str, ok := structpb.NullValue_name[int32(in)]
	if !ok {
		str = fmt.Sprintf("unknown structpb.NullValue value %d", in)
	}

	return zap.Dict(
		label,
		zap.Any("value", structpb.NullValue(in)),
		zap.Any("name", str),
	)
}

func file_inner_processing_grpc_terminal_proto_message_StructToZap(
	label string,
	in *structpb.Struct,
) zap.Field {
	if in == nil {
		return zap.Reflect(label, in)
	}

	return zap.Dict(
		label,
		file_inner_processing_grpc_terminal_proto_string_structpb_Value("Fields", in.GetFields()),
	)
}

func file_inner_processing_grpc_terminal_proto_message_ValueToZap(
	label string,
	in *structpb.Value,
) zap.Field {
	if in == nil {
		return zap.Reflect(label, in)
	}

	return zap.Dict(
		label,
		file_inner_processing_grpc_terminal_proto_enum_NullValueToZap("NullValue", in.GetNullValue()),
		zap.Any("NumberValue", in.GetNumberValue()),
		zap.Any("StringValue", in.GetStringValue()),
		zap.Any("BoolValue", in.GetBoolValue()),
		file_inner_processing_grpc_terminal_proto_message_StructToZap("StructValue", in.GetStructValue()),
		file_inner_processing_grpc_terminal_proto_message_ListValueToZap("ListValue", in.GetListValue()),
	)
}

func file_inner_processing_grpc_terminal_proto_message_ValueSliceToZap(
	label string,
	in []*structpb.Value,
) zap.Field {
	var res []zap.Field
	for index, val := range in {
		res = append(res, file_inner_processing_grpc_terminal_proto_message_ValueToZap(fmt.Sprint(index), val))
	}

	return zap.Dict(label, res...)
}

func file_inner_processing_grpc_terminal_proto_message_AcquirerDataToZap(
	label string,
	in *AcquirerData,
) zap.Field {
	if in == nil {
		return zap.Reflect(label, in)
	}

	return zap.Dict(
		label,
		zap.Any("AcquirerId", in.GetAcquirerId()),
		zap.Any("AcquirerCode", in.GetAcquirerCode()),
		zap.Any("Name", in.GetName()),
		zap.Any("BankId", in.GetBankId()),
		zap.Any("BankName", in.GetBankName()),
		zap.Any("Contract", in.GetContract()),
		zap.Any("Description", in.GetDescription()),
		zap.Any("CountryId", in.GetCountryId()),
		zap.Any("IsActive", in.GetIsActive()),
	)
}

func file_inner_processing_grpc_terminal_proto_message_AcquirerDataV1ToZap(
	label string,
	in *AcquirerDataV1,
) zap.Field {
	if in == nil {
		return zap.Reflect(label, in)
	}

	return zap.Dict(
		label,
		zap.Any("AcquirerId", in.GetAcquirerId()),
		zap.Any("Code", in.GetCode()),
	)
}

func file_inner_processing_grpc_terminal_proto_message_ActiveTerminalsByProjectRequestV1ToZap(
	label string,
	in *ActiveTerminalsByProjectRequestV1,
) zap.Field {
	if in == nil {
		return zap.Reflect(label, in)
	}

	return zap.Dict(
		label,
		zap.Any("ProjectId", in.GetProjectId()),
		zap.Any("TransactionTypeId", in.GetTransactionTypeId()),
	)
}

func file_inner_processing_grpc_terminal_proto_message_ActiveTerminalsByProjectResponseV1ToZap(
	label string,
	in *ActiveTerminalsByProjectResponseV1,
) zap.Field {
	if in == nil {
		return zap.Reflect(label, in)
	}

	return zap.Dict(
		label,
		file_inner_processing_grpc_terminal_proto_message_ActiveTerminalsByProjectV1SliceToZap("Data", in.GetData()),
	)
}

func file_inner_processing_grpc_terminal_proto_message_ActiveTerminalsByProjectV1ToZap(
	label string,
	in *ActiveTerminalsByProjectV1,
) zap.Field {
	if in == nil {
		return zap.Reflect(label, in)
	}

	return zap.Dict(
		label,
		zap.Any("Id", in.GetId()),
		zap.Any("AcquirerId", in.GetAcquirerId()),
		file_inner_processing_grpc_terminal_proto_message_StructToZap("Config", in.GetConfig()),
		zap.Any("Status", in.GetStatus()),
		file_inner_processing_grpc_terminal_proto_message_TerminalProjectV1SliceToZap("TerminalProjects", in.GetTerminalProjects()),
	)
}

func file_inner_processing_grpc_terminal_proto_message_ActiveTerminalsByProjectV1SliceToZap(
	label string,
	in []*ActiveTerminalsByProjectV1,
) zap.Field {
	var res []zap.Field
	for index, val := range in {
		res = append(res, file_inner_processing_grpc_terminal_proto_message_ActiveTerminalsByProjectV1ToZap(fmt.Sprint(index), val))
	}

	return zap.Dict(label, res...)
}

func file_inner_processing_grpc_terminal_proto_message_ExtendedSearchTerminalReqDataV1ToZap(
	label string,
	in *ExtendedSearchTerminalReqDataV1,
) zap.Field {
	if in == nil {
		return zap.Reflect(label, in)
	}

	return zap.Dict(
		label,
		file_inner_processing_grpc_terminal_proto_message_SearchTerminalReqDataV1ToZap("SearchTerminalReq", in.GetSearchTerminalReq()),
		zap.Any("TerminalIds", in.GetTerminalIds()),
	)
}

func file_inner_processing_grpc_terminal_proto_message_GetPayInProjectTerminalsReqV1ToZap(
	label string,
	in *GetPayInProjectTerminalsReqV1,
) zap.Field {
	if in == nil {
		return zap.Reflect(label, in)
	}

	return zap.Dict(
		label,
		zap.Any("ProjectId", in.GetProjectId()),
	)
}

func file_inner_processing_grpc_terminal_proto_message_GetPayInProjectTerminalsResponseV1ToZap(
	label string,
	in *GetPayInProjectTerminalsResponseV1,
) zap.Field {
	if in == nil {
		return zap.Reflect(label, in)
	}

	return zap.Dict(
		label,
		zap.Any("TerminalIds", in.GetTerminalIds()),
	)
}

func file_inner_processing_grpc_terminal_proto_message_GetTerminalWithJusanResponseV1ToZap(
	label string,
	in *GetTerminalWithJusanResponseV1,
) zap.Field {
	if in == nil {
		return zap.Reflect(label, in)
	}

	return zap.Dict(
		label,
		file_inner_processing_grpc_terminal_proto_message_SearchTerminalResDataV1ToZap("Terminal", in.GetTerminal()),
		zap.Any("JusanTerminalId", in.GetJusanTerminalId()),
		file_inner_processing_grpc_terminal_proto_message_StructToZap("JusanConfig", in.GetJusanConfig()),
		file_inner_processing_grpc_terminal_proto_message_AcquirerDataV1ToZap("JusanAcquirer", in.GetJusanAcquirer()),
	)
}

func file_inner_processing_grpc_terminal_proto_message_GetTerminalsByProjectIdRequestV1ToZap(
	label string,
	in *GetTerminalsByProjectIdRequestV1,
) zap.Field {
	if in == nil {
		return zap.Reflect(label, in)
	}

	return zap.Dict(
		label,
		zap.Any("ProjectId", in.GetProjectId()),
	)
}

func file_inner_processing_grpc_terminal_proto_message_RuleByActiveTerminalsToZap(
	label string,
	in *RuleByActiveTerminals,
) zap.Field {
	if in == nil {
		return zap.Reflect(label, in)
	}

	return zap.Dict(
		label,
		zap.Any("RuleId", in.GetRuleId()),
		zap.Any("ProjectId", in.GetProjectId()),
		zap.Any("TransactionTypeId", in.GetTransactionTypeId()),
		zap.Any("IpsId", in.GetIpsId()),
		zap.Any("IssuerId", in.GetIssuerId()),
		zap.Any("CountryId", in.GetCountryId()),
		zap.Any("AmountFrom", in.GetAmountFrom()),
		zap.Any("AmountTo", in.GetAmountTo()),
		zap.Any("Weight", in.GetWeight()),
		zap.Any("IsActive", in.GetIsActive()),
		zap.Any("IsBase", in.GetIsBase()),
	)
}

func file_inner_processing_grpc_terminal_proto_message_RuleByActiveTerminalsReqV1ToZap(
	label string,
	in *RuleByActiveTerminalsReqV1,
) zap.Field {
	if in == nil {
		return zap.Reflect(label, in)
	}

	return zap.Dict(
		label,
		file_inner_processing_grpc_terminal_proto_message_SearchTerminalReqDataV1ToZap("SearchTerminalReq", in.GetSearchTerminalReq()),
		zap.Any("TerminalIds", in.GetTerminalIds()),
	)
}

func file_inner_processing_grpc_terminal_proto_message_RuleByActiveTerminalsResponseV1ToZap(
	label string,
	in *RuleByActiveTerminalsResponseV1,
) zap.Field {
	if in == nil {
		return zap.Reflect(label, in)
	}

	return zap.Dict(
		label,
		file_inner_processing_grpc_terminal_proto_message_RuleByActiveTerminalsToZap("Rule", in.GetRule()),
		zap.Any("IsRuleFound", in.GetIsRuleFound()),
	)
}

func file_inner_processing_grpc_terminal_proto_message_SearchTerminalReqDataV1ToZap(
	label string,
	in *SearchTerminalReqDataV1,
) zap.Field {
	if in == nil {
		return zap.Reflect(label, in)
	}

	return zap.Dict(
		label,
		zap.Any("ProjectId", in.GetProjectId()),
		zap.Any("TransactionTypeId", in.GetTransactionTypeId()),
		zap.Any("Amount", in.GetAmount()),
		zap.Any("IpsId", in.GetIpsId()),
		zap.Any("IssuerId", in.GetIssuerId()),
		zap.Any("CountryId", in.GetCountryId()),
		zap.Any("MerchantId", in.GetMerchantId()),
	)
}

func file_inner_processing_grpc_terminal_proto_message_SearchTerminalResDataV1ToZap(
	label string,
	in *SearchTerminalResDataV1,
) zap.Field {
	if in == nil {
		return zap.Reflect(label, in)
	}

	return zap.Dict(
		label,
		zap.Any("TerminalId", in.GetTerminalId()),
		file_inner_processing_grpc_terminal_proto_message_StructToZap("Config", in.GetConfig()),
		file_inner_processing_grpc_terminal_proto_message_AcquirerDataV1ToZap("Acquirer", in.GetAcquirer()),
	)
}

func file_inner_processing_grpc_terminal_proto_message_TerminalProjectV1ToZap(
	label string,
	in *TerminalProjectV1,
) zap.Field {
	if in == nil {
		return zap.Reflect(label, in)
	}

	return zap.Dict(
		label,
		zap.Any("Id", in.GetId()),
		zap.Any("ProjectId", in.GetProjectId()),
		zap.Any("TransactionTypeId", in.GetTransactionTypeId()),
		zap.Any("TerminalId", in.GetTerminalId()),
	)
}

func file_inner_processing_grpc_terminal_proto_message_TerminalProjectV1SliceToZap(
	label string,
	in []*TerminalProjectV1,
) zap.Field {
	var res []zap.Field
	for index, val := range in {
		res = append(res, file_inner_processing_grpc_terminal_proto_message_TerminalProjectV1ToZap(fmt.Sprint(index), val))
	}

	return zap.Dict(label, res...)
}

func file_inner_processing_grpc_terminal_proto_message_TerminalRequestV1ToZap(
	label string,
	in *TerminalRequestV1,
) zap.Field {
	if in == nil {
		return zap.Reflect(label, in)
	}

	return zap.Dict(
		label,
		zap.Any("TerminalId", in.GetTerminalId()),
	)
}

func file_inner_processing_grpc_terminal_proto_message_TerminalResponseV1ToZap(
	label string,
	in *TerminalResponseV1,
) zap.Field {
	if in == nil {
		return zap.Reflect(label, in)
	}

	return zap.Dict(
		label,
		zap.Any("TerminalId", in.GetTerminalId()),
		zap.Any("ProjectId", in.GetProjectId()),
		file_inner_processing_grpc_terminal_proto_message_StructToZap("Config", in.GetConfig()),
		file_inner_processing_grpc_terminal_proto_message_AcquirerDataToZap("Acquirer", in.GetAcquirer()),
		zap.Any("TwoStageTimeout", in.GetTwoStageTimeout()),
		zap.Any("AccountNumber", in.GetAccountNumber()),
		zap.Any("IsTransit", in.GetIsTransit()),
	)
}

var _ TerminalServer = (*loggedTerminalServer)(nil)

func NewLoggedTerminalServer(srv TerminalServer) TerminalServer {
	return &loggedTerminalServer{srv: srv}
}

type loggedTerminalServer struct {
	UnimplementedTerminalServer

	srv TerminalServer
}

func (s *loggedTerminalServer) GetByTerminalID(
	ctx context.Context,
	request *TerminalRequestV1,
) (
	response *TerminalResponseV1,
	err error,
) {
	label := cntx.Begin(ctx, "TerminalServer_GetByTerminalID")
	defer cntx.End(ctx, label)

	defer func() {
		if err != nil {
			ctxzap.AddFields(ctx, zap.Any(label+"error", err))
			return
		}

		ctxzap.AddFields(
			ctx,
			file_inner_processing_grpc_terminal_proto_message_TerminalResponseV1ToZap(label+"response", response),
		)
	}()

	ctxzap.AddFields(
		ctx,
		file_inner_processing_grpc_terminal_proto_message_TerminalRequestV1ToZap(label+"request", request),
	)

	response, err = s.srv.GetByTerminalID(ctx, request)

	return
}

func (s *loggedTerminalServer) FindActiveTerminalsByProject(
	ctx context.Context,
	request *ActiveTerminalsByProjectRequestV1,
) (
	response *ActiveTerminalsByProjectResponseV1,
	err error,
) {
	label := cntx.Begin(ctx, "TerminalServer_FindActiveTerminalsByProject")
	defer cntx.End(ctx, label)

	defer func() {
		if err != nil {
			ctxzap.AddFields(ctx, zap.Any(label+"error", err))
			return
		}

		ctxzap.AddFields(
			ctx,
			file_inner_processing_grpc_terminal_proto_message_ActiveTerminalsByProjectResponseV1ToZap(label+"response", response),
		)
	}()

	ctxzap.AddFields(
		ctx,
		file_inner_processing_grpc_terminal_proto_message_ActiveTerminalsByProjectRequestV1ToZap(label+"request", request),
	)

	response, err = s.srv.FindActiveTerminalsByProject(ctx, request)

	return
}

func (s *loggedTerminalServer) SearchTerminal(
	ctx context.Context,
	request *SearchTerminalReqDataV1,
) (
	response *SearchTerminalResDataV1,
	err error,
) {
	label := cntx.Begin(ctx, "TerminalServer_SearchTerminal")
	defer cntx.End(ctx, label)

	defer func() {
		if err != nil {
			ctxzap.AddFields(ctx, zap.Any(label+"error", err))
			return
		}

		ctxzap.AddFields(
			ctx,
			file_inner_processing_grpc_terminal_proto_message_SearchTerminalResDataV1ToZap(label+"response", response),
		)
	}()

	ctxzap.AddFields(
		ctx,
		file_inner_processing_grpc_terminal_proto_message_SearchTerminalReqDataV1ToZap(label+"request", request),
	)

	response, err = s.srv.SearchTerminal(ctx, request)

	return
}

func (s *loggedTerminalServer) ExtendedSearchTerminal(
	ctx context.Context,
	request *ExtendedSearchTerminalReqDataV1,
) (
	response *SearchTerminalResDataV1,
	err error,
) {
	label := cntx.Begin(ctx, "TerminalServer_ExtendedSearchTerminal")
	defer cntx.End(ctx, label)

	defer func() {
		if err != nil {
			ctxzap.AddFields(ctx, zap.Any(label+"error", err))
			return
		}

		ctxzap.AddFields(
			ctx,
			file_inner_processing_grpc_terminal_proto_message_SearchTerminalResDataV1ToZap(label+"response", response),
		)
	}()

	ctxzap.AddFields(
		ctx,
		file_inner_processing_grpc_terminal_proto_message_ExtendedSearchTerminalReqDataV1ToZap(label+"request", request),
	)

	response, err = s.srv.ExtendedSearchTerminal(ctx, request)

	return
}

func (s *loggedTerminalServer) GetTerminalsByProjectId(
	ctx context.Context,
	request *GetTerminalsByProjectIdRequestV1,
) (
	response *ActiveTerminalsByProjectResponseV1,
	err error,
) {
	label := cntx.Begin(ctx, "TerminalServer_GetTerminalsByProjectId")
	defer cntx.End(ctx, label)

	defer func() {
		if err != nil {
			ctxzap.AddFields(ctx, zap.Any(label+"error", err))
			return
		}

		ctxzap.AddFields(
			ctx,
			file_inner_processing_grpc_terminal_proto_message_ActiveTerminalsByProjectResponseV1ToZap(label+"response", response),
		)
	}()

	ctxzap.AddFields(
		ctx,
		file_inner_processing_grpc_terminal_proto_message_GetTerminalsByProjectIdRequestV1ToZap(label+"request", request),
	)

	response, err = s.srv.GetTerminalsByProjectId(ctx, request)

	return
}

func (s *loggedTerminalServer) GetTerminalWithJusan(
	ctx context.Context,
	request *SearchTerminalReqDataV1,
) (
	response *GetTerminalWithJusanResponseV1,
	err error,
) {
	label := cntx.Begin(ctx, "TerminalServer_GetTerminalWithJusan")
	defer cntx.End(ctx, label)

	defer func() {
		if err != nil {
			ctxzap.AddFields(ctx, zap.Any(label+"error", err))
			return
		}

		ctxzap.AddFields(
			ctx,
			file_inner_processing_grpc_terminal_proto_message_GetTerminalWithJusanResponseV1ToZap(label+"response", response),
		)
	}()

	ctxzap.AddFields(
		ctx,
		file_inner_processing_grpc_terminal_proto_message_SearchTerminalReqDataV1ToZap(label+"request", request),
	)

	response, err = s.srv.GetTerminalWithJusan(ctx, request)

	return
}

func (s *loggedTerminalServer) GetRuleByActiveTerminals(
	ctx context.Context,
	request *RuleByActiveTerminalsReqV1,
) (
	response *RuleByActiveTerminalsResponseV1,
	err error,
) {
	label := cntx.Begin(ctx, "TerminalServer_GetRuleByActiveTerminals")
	defer cntx.End(ctx, label)

	defer func() {
		if err != nil {
			ctxzap.AddFields(ctx, zap.Any(label+"error", err))
			return
		}

		ctxzap.AddFields(
			ctx,
			file_inner_processing_grpc_terminal_proto_message_RuleByActiveTerminalsResponseV1ToZap(label+"response", response),
		)
	}()

	ctxzap.AddFields(
		ctx,
		file_inner_processing_grpc_terminal_proto_message_RuleByActiveTerminalsReqV1ToZap(label+"request", request),
	)

	response, err = s.srv.GetRuleByActiveTerminals(ctx, request)

	return
}

func (s *loggedTerminalServer) GetPayInProjectTerminals(
	ctx context.Context,
	request *GetPayInProjectTerminalsReqV1,
) (
	response *GetPayInProjectTerminalsResponseV1,
	err error,
) {
	label := cntx.Begin(ctx, "TerminalServer_GetPayInProjectTerminals")
	defer cntx.End(ctx, label)

	defer func() {
		if err != nil {
			ctxzap.AddFields(ctx, zap.Any(label+"error", err))
			return
		}

		ctxzap.AddFields(
			ctx,
			file_inner_processing_grpc_terminal_proto_message_GetPayInProjectTerminalsResponseV1ToZap(label+"response", response),
		)
	}()

	ctxzap.AddFields(
		ctx,
		file_inner_processing_grpc_terminal_proto_message_GetPayInProjectTerminalsReqV1ToZap(label+"request", request),
	)

	response, err = s.srv.GetPayInProjectTerminals(ctx, request)

	return
}

var _ TerminalClient = (*loggedTerminalClient)(nil)

func NewLoggedTerminalClient(client TerminalClient) TerminalClient {
	return &loggedTerminalClient{client: client}
}

type loggedTerminalClient struct {
	client TerminalClient
}

func (s *loggedTerminalClient) GetByTerminalID(
	ctx context.Context,
	request *TerminalRequestV1,
	opts ...grpc.CallOption,
) (
	response *TerminalResponseV1,
	err error,
) {
	label := cntx.Begin(ctx, "TerminalClient_GetByTerminalID")
	defer cntx.End(ctx, label)

	defer func() {
		if err != nil {
			ctxzap.AddFields(ctx, zap.Any(label+"error", err))
			return
		}

		ctxzap.AddFields(
			ctx,
			file_inner_processing_grpc_terminal_proto_message_TerminalResponseV1ToZap(label+"response", response),
		)
	}()

	ctxzap.AddFields(
		ctx,
		file_inner_processing_grpc_terminal_proto_message_TerminalRequestV1ToZap(label+"request", request),
	)

	response, err = s.client.GetByTerminalID(ctx, request, opts...)

	return
}

func (s *loggedTerminalClient) FindActiveTerminalsByProject(
	ctx context.Context,
	request *ActiveTerminalsByProjectRequestV1,
	opts ...grpc.CallOption,
) (
	response *ActiveTerminalsByProjectResponseV1,
	err error,
) {
	label := cntx.Begin(ctx, "TerminalClient_FindActiveTerminalsByProject")
	defer cntx.End(ctx, label)

	defer func() {
		if err != nil {
			ctxzap.AddFields(ctx, zap.Any(label+"error", err))
			return
		}

		ctxzap.AddFields(
			ctx,
			file_inner_processing_grpc_terminal_proto_message_ActiveTerminalsByProjectResponseV1ToZap(label+"response", response),
		)
	}()

	ctxzap.AddFields(
		ctx,
		file_inner_processing_grpc_terminal_proto_message_ActiveTerminalsByProjectRequestV1ToZap(label+"request", request),
	)

	response, err = s.client.FindActiveTerminalsByProject(ctx, request, opts...)

	return
}

func (s *loggedTerminalClient) SearchTerminal(
	ctx context.Context,
	request *SearchTerminalReqDataV1,
	opts ...grpc.CallOption,
) (
	response *SearchTerminalResDataV1,
	err error,
) {
	label := cntx.Begin(ctx, "TerminalClient_SearchTerminal")
	defer cntx.End(ctx, label)

	defer func() {
		if err != nil {
			ctxzap.AddFields(ctx, zap.Any(label+"error", err))
			return
		}

		ctxzap.AddFields(
			ctx,
			file_inner_processing_grpc_terminal_proto_message_SearchTerminalResDataV1ToZap(label+"response", response),
		)
	}()

	ctxzap.AddFields(
		ctx,
		file_inner_processing_grpc_terminal_proto_message_SearchTerminalReqDataV1ToZap(label+"request", request),
	)

	response, err = s.client.SearchTerminal(ctx, request, opts...)

	return
}

func (s *loggedTerminalClient) ExtendedSearchTerminal(
	ctx context.Context,
	request *ExtendedSearchTerminalReqDataV1,
	opts ...grpc.CallOption,
) (
	response *SearchTerminalResDataV1,
	err error,
) {
	label := cntx.Begin(ctx, "TerminalClient_ExtendedSearchTerminal")
	defer cntx.End(ctx, label)

	defer func() {
		if err != nil {
			ctxzap.AddFields(ctx, zap.Any(label+"error", err))
			return
		}

		ctxzap.AddFields(
			ctx,
			file_inner_processing_grpc_terminal_proto_message_SearchTerminalResDataV1ToZap(label+"response", response),
		)
	}()

	ctxzap.AddFields(
		ctx,
		file_inner_processing_grpc_terminal_proto_message_ExtendedSearchTerminalReqDataV1ToZap(label+"request", request),
	)

	response, err = s.client.ExtendedSearchTerminal(ctx, request, opts...)

	return
}

func (s *loggedTerminalClient) GetTerminalsByProjectId(
	ctx context.Context,
	request *GetTerminalsByProjectIdRequestV1,
	opts ...grpc.CallOption,
) (
	response *ActiveTerminalsByProjectResponseV1,
	err error,
) {
	label := cntx.Begin(ctx, "TerminalClient_GetTerminalsByProjectId")
	defer cntx.End(ctx, label)

	defer func() {
		if err != nil {
			ctxzap.AddFields(ctx, zap.Any(label+"error", err))
			return
		}

		ctxzap.AddFields(
			ctx,
			file_inner_processing_grpc_terminal_proto_message_ActiveTerminalsByProjectResponseV1ToZap(label+"response", response),
		)
	}()

	ctxzap.AddFields(
		ctx,
		file_inner_processing_grpc_terminal_proto_message_GetTerminalsByProjectIdRequestV1ToZap(label+"request", request),
	)

	response, err = s.client.GetTerminalsByProjectId(ctx, request, opts...)

	return
}

func (s *loggedTerminalClient) GetTerminalWithJusan(
	ctx context.Context,
	request *SearchTerminalReqDataV1,
	opts ...grpc.CallOption,
) (
	response *GetTerminalWithJusanResponseV1,
	err error,
) {
	label := cntx.Begin(ctx, "TerminalClient_GetTerminalWithJusan")
	defer cntx.End(ctx, label)

	defer func() {
		if err != nil {
			ctxzap.AddFields(ctx, zap.Any(label+"error", err))
			return
		}

		ctxzap.AddFields(
			ctx,
			file_inner_processing_grpc_terminal_proto_message_GetTerminalWithJusanResponseV1ToZap(label+"response", response),
		)
	}()

	ctxzap.AddFields(
		ctx,
		file_inner_processing_grpc_terminal_proto_message_SearchTerminalReqDataV1ToZap(label+"request", request),
	)

	response, err = s.client.GetTerminalWithJusan(ctx, request, opts...)

	return
}

func (s *loggedTerminalClient) GetRuleByActiveTerminals(
	ctx context.Context,
	request *RuleByActiveTerminalsReqV1,
	opts ...grpc.CallOption,
) (
	response *RuleByActiveTerminalsResponseV1,
	err error,
) {
	label := cntx.Begin(ctx, "TerminalClient_GetRuleByActiveTerminals")
	defer cntx.End(ctx, label)

	defer func() {
		if err != nil {
			ctxzap.AddFields(ctx, zap.Any(label+"error", err))
			return
		}

		ctxzap.AddFields(
			ctx,
			file_inner_processing_grpc_terminal_proto_message_RuleByActiveTerminalsResponseV1ToZap(label+"response", response),
		)
	}()

	ctxzap.AddFields(
		ctx,
		file_inner_processing_grpc_terminal_proto_message_RuleByActiveTerminalsReqV1ToZap(label+"request", request),
	)

	response, err = s.client.GetRuleByActiveTerminals(ctx, request, opts...)

	return
}

func (s *loggedTerminalClient) GetPayInProjectTerminals(
	ctx context.Context,
	request *GetPayInProjectTerminalsReqV1,
	opts ...grpc.CallOption,
) (
	response *GetPayInProjectTerminalsResponseV1,
	err error,
) {
	label := cntx.Begin(ctx, "TerminalClient_GetPayInProjectTerminals")
	defer cntx.End(ctx, label)

	defer func() {
		if err != nil {
			ctxzap.AddFields(ctx, zap.Any(label+"error", err))
			return
		}

		ctxzap.AddFields(
			ctx,
			file_inner_processing_grpc_terminal_proto_message_GetPayInProjectTerminalsResponseV1ToZap(label+"response", response),
		)
	}()

	ctxzap.AddFields(
		ctx,
		file_inner_processing_grpc_terminal_proto_message_GetPayInProjectTerminalsReqV1ToZap(label+"request", request),
	)

	response, err = s.client.GetPayInProjectTerminals(ctx, request, opts...)

	return
}
