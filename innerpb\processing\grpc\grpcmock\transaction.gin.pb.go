// Code generated by MockGen. DO NOT EDIT.
// Source: transaction.gin.pb.go

// Package grpcmock is a generated GoMock package.
package grpcmock

import (
	reflect "reflect"

	gin "github.com/gin-gonic/gin"
	gomock "github.com/golang/mock/gomock"
)

// MockGinTransactionServer is a mock of GinTransactionServer interface.
type MockGinTransactionServer struct {
	ctrl     *gomock.Controller
	recorder *MockGinTransactionServerMockRecorder
}

// MockGinTransactionServerMockRecorder is the mock recorder for MockGinTransactionServer.
type MockGinTransactionServerMockRecorder struct {
	mock *MockGinTransactionServer
}

// NewMockGinTransactionServer creates a new mock instance.
func NewMockGinTransactionServer(ctrl *gomock.Controller) *MockGinTransactionServer {
	mock := &MockGinTransactionServer{ctrl: ctrl}
	mock.recorder = &MockGinTransactionServerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockGinTransactionServer) EXPECT() *MockGinTransactionServerMockRecorder {
	return m.recorder
}

// BillPayOut mocks base method.
func (m *MockGinTransactionServer) BillPayOut(c *gin.Context) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BillPayOut", c)
	ret0, _ := ret[0].(error)
	return ret0
}

// BillPayOut indicates an expected call of BillPayOut.
func (mr *MockGinTransactionServerMockRecorder) BillPayOut(c interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BillPayOut", reflect.TypeOf((*MockGinTransactionServer)(nil).BillPayOut), c)
}

// CalculateAndUpdateTransactionAmount mocks base method.
func (m *MockGinTransactionServer) CalculateAndUpdateTransactionAmount(c *gin.Context) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CalculateAndUpdateTransactionAmount", c)
	ret0, _ := ret[0].(error)
	return ret0
}

// CalculateAndUpdateTransactionAmount indicates an expected call of CalculateAndUpdateTransactionAmount.
func (mr *MockGinTransactionServerMockRecorder) CalculateAndUpdateTransactionAmount(c interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CalculateAndUpdateTransactionAmount", reflect.TypeOf((*MockGinTransactionServer)(nil).CalculateAndUpdateTransactionAmount), c)
}

// CheckTransactionHash mocks base method.
func (m *MockGinTransactionServer) CheckTransactionHash(c *gin.Context) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CheckTransactionHash", c)
	ret0, _ := ret[0].(error)
	return ret0
}

// CheckTransactionHash indicates an expected call of CheckTransactionHash.
func (mr *MockGinTransactionServerMockRecorder) CheckTransactionHash(c interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CheckTransactionHash", reflect.TypeOf((*MockGinTransactionServer)(nil).CheckTransactionHash), c)
}

// CreateTransactionByPhone mocks base method.
func (m *MockGinTransactionServer) CreateTransactionByPhone(c *gin.Context) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateTransactionByPhone", c)
	ret0, _ := ret[0].(error)
	return ret0
}

// CreateTransactionByPhone indicates an expected call of CreateTransactionByPhone.
func (mr *MockGinTransactionServerMockRecorder) CreateTransactionByPhone(c interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateTransactionByPhone", reflect.TypeOf((*MockGinTransactionServer)(nil).CreateTransactionByPhone), c)
}

// GetByIDWithType mocks base method.
func (m *MockGinTransactionServer) GetByIDWithType(c *gin.Context) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetByIDWithType", c)
	ret0, _ := ret[0].(error)
	return ret0
}

// GetByIDWithType indicates an expected call of GetByIDWithType.
func (mr *MockGinTransactionServerMockRecorder) GetByIDWithType(c interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetByIDWithType", reflect.TypeOf((*MockGinTransactionServer)(nil).GetByIDWithType), c)
}

// GetPayInTransactionsByPeriodAndAcquirer mocks base method.
func (m *MockGinTransactionServer) GetPayInTransactionsByPeriodAndAcquirer(c *gin.Context) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetPayInTransactionsByPeriodAndAcquirer", c)
	ret0, _ := ret[0].(error)
	return ret0
}

// GetPayInTransactionsByPeriodAndAcquirer indicates an expected call of GetPayInTransactionsByPeriodAndAcquirer.
func (mr *MockGinTransactionServerMockRecorder) GetPayInTransactionsByPeriodAndAcquirer(c interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetPayInTransactionsByPeriodAndAcquirer", reflect.TypeOf((*MockGinTransactionServer)(nil).GetPayInTransactionsByPeriodAndAcquirer), c)
}

// GetTransactionByID mocks base method.
func (m *MockGinTransactionServer) GetTransactionByID(c *gin.Context) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetTransactionByID", c)
	ret0, _ := ret[0].(error)
	return ret0
}

// GetTransactionByID indicates an expected call of GetTransactionByID.
func (mr *MockGinTransactionServerMockRecorder) GetTransactionByID(c interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetTransactionByID", reflect.TypeOf((*MockGinTransactionServer)(nil).GetTransactionByID), c)
}

// GetTransactionTypeByID mocks base method.
func (m *MockGinTransactionServer) GetTransactionTypeByID(c *gin.Context) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetTransactionTypeByID", c)
	ret0, _ := ret[0].(error)
	return ret0
}

// GetTransactionTypeByID indicates an expected call of GetTransactionTypeByID.
func (mr *MockGinTransactionServerMockRecorder) GetTransactionTypeByID(c interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetTransactionTypeByID", reflect.TypeOf((*MockGinTransactionServer)(nil).GetTransactionTypeByID), c)
}

// GetTransactionsByFinalStatusAndPeriodWithLimit mocks base method.
func (m *MockGinTransactionServer) GetTransactionsByFinalStatusAndPeriodWithLimit(c *gin.Context) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetTransactionsByFinalStatusAndPeriodWithLimit", c)
	ret0, _ := ret[0].(error)
	return ret0
}

// GetTransactionsByFinalStatusAndPeriodWithLimit indicates an expected call of GetTransactionsByFinalStatusAndPeriodWithLimit.
func (mr *MockGinTransactionServerMockRecorder) GetTransactionsByFinalStatusAndPeriodWithLimit(c interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetTransactionsByFinalStatusAndPeriodWithLimit", reflect.TypeOf((*MockGinTransactionServer)(nil).GetTransactionsByFinalStatusAndPeriodWithLimit), c)
}

// GetTransactionsByPeriodAndCallbackStatus mocks base method.
func (m *MockGinTransactionServer) GetTransactionsByPeriodAndCallbackStatus(c *gin.Context) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetTransactionsByPeriodAndCallbackStatus", c)
	ret0, _ := ret[0].(error)
	return ret0
}

// GetTransactionsByPeriodAndCallbackStatus indicates an expected call of GetTransactionsByPeriodAndCallbackStatus.
func (mr *MockGinTransactionServerMockRecorder) GetTransactionsByPeriodAndCallbackStatus(c interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetTransactionsByPeriodAndCallbackStatus", reflect.TypeOf((*MockGinTransactionServer)(nil).GetTransactionsByPeriodAndCallbackStatus), c)
}

// GetTransactionsByPeriodAndStatus mocks base method.
func (m *MockGinTransactionServer) GetTransactionsByPeriodAndStatus(c *gin.Context) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetTransactionsByPeriodAndStatus", c)
	ret0, _ := ret[0].(error)
	return ret0
}

// GetTransactionsByPeriodAndStatus indicates an expected call of GetTransactionsByPeriodAndStatus.
func (mr *MockGinTransactionServerMockRecorder) GetTransactionsByPeriodAndStatus(c interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetTransactionsByPeriodAndStatus", reflect.TypeOf((*MockGinTransactionServer)(nil).GetTransactionsByPeriodAndStatus), c)
}

// GetTransactionsByProjectInfo mocks base method.
func (m *MockGinTransactionServer) GetTransactionsByProjectInfo(c *gin.Context) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetTransactionsByProjectInfo", c)
	ret0, _ := ret[0].(error)
	return ret0
}

// GetTransactionsByProjectInfo indicates an expected call of GetTransactionsByProjectInfo.
func (mr *MockGinTransactionServerMockRecorder) GetTransactionsByProjectInfo(c interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetTransactionsByProjectInfo", reflect.TypeOf((*MockGinTransactionServer)(nil).GetTransactionsByProjectInfo), c)
}

// IncreaseTryCount mocks base method.
func (m *MockGinTransactionServer) IncreaseTryCount(c *gin.Context) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "IncreaseTryCount", c)
	ret0, _ := ret[0].(error)
	return ret0
}

// IncreaseTryCount indicates an expected call of IncreaseTryCount.
func (mr *MockGinTransactionServerMockRecorder) IncreaseTryCount(c interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "IncreaseTryCount", reflect.TypeOf((*MockGinTransactionServer)(nil).IncreaseTryCount), c)
}

// MakeAutoCharge mocks base method.
func (m *MockGinTransactionServer) MakeAutoCharge(c *gin.Context) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "MakeAutoCharge", c)
	ret0, _ := ret[0].(error)
	return ret0
}

// MakeAutoCharge indicates an expected call of MakeAutoCharge.
func (mr *MockGinTransactionServerMockRecorder) MakeAutoCharge(c interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "MakeAutoCharge", reflect.TypeOf((*MockGinTransactionServer)(nil).MakeAutoCharge), c)
}

// SaveAcquirerResponse mocks base method.
func (m *MockGinTransactionServer) SaveAcquirerResponse(c *gin.Context) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SaveAcquirerResponse", c)
	ret0, _ := ret[0].(error)
	return ret0
}

// SaveAcquirerResponse indicates an expected call of SaveAcquirerResponse.
func (mr *MockGinTransactionServerMockRecorder) SaveAcquirerResponse(c interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SaveAcquirerResponse", reflect.TypeOf((*MockGinTransactionServer)(nil).SaveAcquirerResponse), c)
}

// SendReceipt mocks base method.
func (m *MockGinTransactionServer) SendReceipt(c *gin.Context) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SendReceipt", c)
	ret0, _ := ret[0].(error)
	return ret0
}

// SendReceipt indicates an expected call of SendReceipt.
func (mr *MockGinTransactionServerMockRecorder) SendReceipt(c interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SendReceipt", reflect.TypeOf((*MockGinTransactionServer)(nil).SendReceipt), c)
}

// SetAdditionalData mocks base method.
func (m *MockGinTransactionServer) SetAdditionalData(c *gin.Context) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SetAdditionalData", c)
	ret0, _ := ret[0].(error)
	return ret0
}

// SetAdditionalData indicates an expected call of SetAdditionalData.
func (mr *MockGinTransactionServerMockRecorder) SetAdditionalData(c interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetAdditionalData", reflect.TypeOf((*MockGinTransactionServer)(nil).SetAdditionalData), c)
}

// SetRefundWaitingStatus mocks base method.
func (m *MockGinTransactionServer) SetRefundWaitingStatus(c *gin.Context) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SetRefundWaitingStatus", c)
	ret0, _ := ret[0].(error)
	return ret0
}

// SetRefundWaitingStatus indicates an expected call of SetRefundWaitingStatus.
func (mr *MockGinTransactionServerMockRecorder) SetRefundWaitingStatus(c interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetRefundWaitingStatus", reflect.TypeOf((*MockGinTransactionServer)(nil).SetRefundWaitingStatus), c)
}

// UpdateCallbackStatus mocks base method.
func (m *MockGinTransactionServer) UpdateCallbackStatus(c *gin.Context) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateCallbackStatus", c)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpdateCallbackStatus indicates an expected call of UpdateCallbackStatus.
func (mr *MockGinTransactionServerMockRecorder) UpdateCallbackStatus(c interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateCallbackStatus", reflect.TypeOf((*MockGinTransactionServer)(nil).UpdateCallbackStatus), c)
}

// UpdateStatus mocks base method.
func (m *MockGinTransactionServer) UpdateStatus(c *gin.Context) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateStatus", c)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpdateStatus indicates an expected call of UpdateStatus.
func (mr *MockGinTransactionServerMockRecorder) UpdateStatus(c interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateStatus", reflect.TypeOf((*MockGinTransactionServer)(nil).UpdateStatus), c)
}

// UpdateTransactionStatus mocks base method.
func (m *MockGinTransactionServer) UpdateTransactionStatus(c *gin.Context) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateTransactionStatus", c)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpdateTransactionStatus indicates an expected call of UpdateTransactionStatus.
func (mr *MockGinTransactionServerMockRecorder) UpdateTransactionStatus(c interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateTransactionStatus", reflect.TypeOf((*MockGinTransactionServer)(nil).UpdateTransactionStatus), c)
}
