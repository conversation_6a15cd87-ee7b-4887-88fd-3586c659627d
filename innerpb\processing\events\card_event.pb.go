// Code generated by protoc-gen-go-mvp. DO NOT EDIT.
// versions:
// - protoc-gen-go-mvp v2.0.1
// created by mvp authors

package events

import (
	context "context"
	errors "errors"
	fmt "fmt"
	pb "git.local/sensitive/mvp/pb"
	watermill "github.com/ThreeDotsLabs/watermill"
	nats "github.com/ThreeDotsLabs/watermill-nats/v2/pkg/nats"
	message "github.com/ThreeDotsLabs/watermill/message"
	uuid "github.com/google/uuid"
	nats_go "github.com/nats-io/nats.go"
	otel "go.opentelemetry.io/otel"
	attribute "go.opentelemetry.io/otel/attribute"
	propagation "go.opentelemetry.io/otel/propagation"
	zap "go.uber.org/zap"
	protojson "google.golang.org/protobuf/encoding/protojson"
	anypb "google.golang.org/protobuf/types/known/anypb"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	strings "strings"
	time "time"
)

const subjectNameTestMessage = "card.testmessage"

type eventTestMessage struct {
	jsContext         nats_go.JetStreamContext
	natsConn          *nats_go.Conn
	natsMessageRouter *message.Router
}

func newTestMessage(
	jsContext nats_go.JetStreamContext,
	conn *nats_go.Conn,
	router *message.Router,
) *eventTestMessage {
	return &eventTestMessage{
		jsContext:         jsContext,
		natsConn:          conn,
		natsMessageRouter: router,
	}
}

var singleTestMessage func(ctx context.Context) (<-chan *TestMessage, error)

func NewTestMessageSubscriber(
	js nats_go.JetStreamContext,
	conn *nats_go.Conn,
	router *message.Router,
) func(ctx context.Context) (<-chan *TestMessage, error) {
	if singleTestMessage == nil {
		singleTestMessage = newTestMessage(js, conn, router).Subscribe
	}

	return singleTestMessage
}

func NewTestMessagePublisher(
	js nats_go.JetStreamContext,
) func(ctx context.Context, src *TestMessage) error {
	return newTestMessage(js, nil, nil).Publish
}

func (s *eventTestMessage) Publish(ctx context.Context, src *TestMessage) error {
	var err error
	eventId := uuid.New().String()

	ctx, span := otel.Tracer("otel_trace").Start(ctx, "TestMessage.Publish")
	defer span.End()
	defer func() {
		span.RecordError(err)
		span.SetAttributes(
			attribute.Bool("error", err != nil),
			attribute.String("nats_uuid", eventId),
		)
	}()

	m, err := anypb.New(src)
	if err != nil {
		return err
	}

	data := pb.EventInfo{
		EventId:   &eventId,
		EventTime: timestamppb.Now(),
		Data:      m,
	}
	marshal, err := protojson.Marshal(&data)
	if err != nil {
		return err
	}

	header := make(nats_go.Header)
	otel.GetTextMapPropagator().Inject(ctx, propagation.HeaderCarrier(header))

	_, err = s.jsContext.PublishMsg(&nats_go.Msg{
		Subject: subjectNameTestMessage,
		Data:    marshal,
		Header:  header,
	})

	return err
}

func (s *eventTestMessage) Subscribe(ctx context.Context) (<-chan *TestMessage, error) {
	ch := make(chan *TestMessage)

	streamCfg := newTestMessageStreamConfig()
	consumerCfg := newTestMessageConsumerConfig()
	watermillCfg := newTestMessageWatermillConfigs()

	fun := func(msg *message.Message) error {
		data := new(pb.EventInfo)
		resTestMessage := new(TestMessage)
		if err := protojson.Unmarshal(msg.Payload, data); err != nil {
			return err
		}

		if err := data.GetData().UnmarshalTo(resTestMessage); err != nil {
			return err
		}

		ch <- resTestMessage

		return nil
	}

	err := s.createConsumerTestMessage(streamCfg, consumerCfg)
	if err != nil {
		return nil, err
	}
	subscriber, err := nats.NewSubscriberWithNatsConn(
		s.natsConn,
		*watermillCfg,
		newZapLoggerAdapterTestMessage(zap.L()),
	)
	if err != nil {
		return nil, fmt.Errorf("err watermillNats.NewSubscriberWithNatsConn: %w", err)
	}
	go func() {
		select {
		case <-ctx.Done():
			if err := subscriber.Close(); err != nil {
				zap.L().Error("subscriber.Close", zap.Error(err))
			}
		}
	}()

	s.natsMessageRouter.AddNoPublisherHandler(
		consumerCfg.Name,
		subjectNameTestMessage,
		subscriber,
		fun,
	)

	return ch, nil
}

func newTestMessageStreamConfig() *nats_go.StreamConfig {
	return &nats_go.StreamConfig{
		Name:         strings.ReplaceAll(subjectNameTestMessage, ".", "_"),
		Subjects:     []string{subjectNameTestMessage, fmt.Sprintf("%s.>", subjectNameTestMessage)},
		Retention:    nats_go.LimitsPolicy,
		MaxConsumers: -1,
		Discard:      nats_go.DiscardOld,
		MaxAge:       time.Hour * 24 * 7,
		Storage:      nats_go.FileStorage,
		NoAck:        false,
		AllowRollup:  true,
	}
}

func newTestMessageConsumerConfig() *nats_go.ConsumerConfig {
	defaultAckWait := time.Second * 30

	streamNameWithoutDots := strings.ReplaceAll(subjectNameTestMessage, ".", "_")

	consumerName := strings.ReplaceAll(fmt.Sprintf("%s_%s",
		"events",
		streamNameWithoutDots), ".", "_")

	return &nats_go.ConsumerConfig{
		Durable:        consumerName,
		Name:           consumerName,
		AckPolicy:      nats_go.AckExplicitPolicy,
		AckWait:        defaultAckWait,
		MaxDeliver:     -1,
		DeliverGroup:   consumerName,
		DeliverSubject: consumerName,
	}
}

func newTestMessageWatermillConfigs() *nats.SubscriberSubscriptionConfig {
	defaultAckWait := time.Second * 30

	streamNameWithoutDots := strings.ReplaceAll(subjectNameTestMessage, ".", "_")

	consumerName := strings.ReplaceAll(fmt.Sprintf("%s_%s",
		"events",
		streamNameWithoutDots), ".", "_")

	return &nats.SubscriberSubscriptionConfig{
		Unmarshaler:    &eventMarshalerTestMessage{},
		AckWaitTimeout: defaultAckWait,
		CloseTimeout:   defaultAckWait,
		SubjectCalculator: func(queueGroupPrefix, topic string) *nats.SubjectDetail {
			return &nats.SubjectDetail{
				Primary:    subjectNameTestMessage,
				QueueGroup: consumerName,
			}
		},
		JetStream: nats.JetStreamConfig{
			Disabled:      false,
			AutoProvision: false,
			SubscribeOptions: []nats_go.SubOpt{
				nats_go.ManualAck(),
				nats_go.DeliverAll(),
				nats_go.AckExplicit(),
				nats_go.Bind(streamNameWithoutDots, consumerName),
				nats_go.MaxDeliver(-1),
			},
			TrackMsgId:        true,
			AckAsync:          false,
			DurableCalculator: func(prefix, topic string) string { return consumerName },
		},
	}
}

func (s *eventTestMessage) createConsumerTestMessage(streamCfg *nats_go.StreamConfig, consumerCfg *nats_go.ConsumerConfig) error {
	_, err := s.jsContext.StreamInfo(streamCfg.Name)
	if err != nil {
		if errors.Is(err, nats_go.ErrStreamNotFound) {
			_, err = s.jsContext.AddStream(streamCfg)
			if err != nil {
				return fmt.Errorf("err jsContext.AddStream: %w", err)
			}
		} else {
			return fmt.Errorf("can't get NATS JetStream Stream: %w", err)
		}
	}

	_, err = s.jsContext.UpdateStream(streamCfg)
	if err != nil {
		return fmt.Errorf("err jsContext.UpdateStream: %w", err)
	}

	_, err = s.jsContext.ConsumerInfo(streamCfg.Name, streamCfg.Name)
	if err != nil {
		if errors.Is(err, nats_go.ErrConsumerNotFound) {
			_, err = s.jsContext.AddConsumer(streamCfg.Name, consumerCfg)
			if err != nil {
				return fmt.Errorf("err jsContext.AddConsumer: %w", err)
			}
		} else {
			return fmt.Errorf("can't get NATS JetStream Consumer %s: %w", consumerCfg.Name, err)
		}
	}

	_, err = s.jsContext.UpdateConsumer(streamCfg.Name, consumerCfg)
	if err != nil {
		return fmt.Errorf("err jsContext.UpdateConsumer: %w", err)
	}

	return nil
}

type eventMarshalerTestMessage struct{}

func (*eventMarshalerTestMessage) Marshal(topic string, msg *message.Message) (*nats_go.Msg, error) {
	natsMsg := nats_go.NewMsg(topic)
	natsMsg.Data = msg.Payload

	return natsMsg, nil
}
func (*eventMarshalerTestMessage) Unmarshal(msg *nats_go.Msg) (*message.Message, error) {
	pbMsg := &pb.EventInfo{}
	err := protojson.Unmarshal(msg.Data, pbMsg)
	if err != nil {
		return nil, err
	}

	wmMsg := message.NewMessage(pbMsg.GetEventId(), msg.Data)
	wmMsg.Metadata = map[string]string{}

	return wmMsg, nil
}

type zapLoggerAdapterTestMessage struct {
	logger *zap.Logger
}

func newZapLoggerAdapterTestMessage(logger *zap.Logger) *zapLoggerAdapterTestMessage {
	return &zapLoggerAdapterTestMessage{logger: logger}
}

func (z zapLoggerAdapterTestMessage) Error(msg string, err error, fields watermill.LogFields) {
	z.logger.Error(msg, append(z.toZapFields(fields), zap.Error(err))...)
}

func (z zapLoggerAdapterTestMessage) Info(msg string, fields watermill.LogFields) {
	z.logger.Info(msg, z.toZapFields(fields)...)
}

func (z zapLoggerAdapterTestMessage) Debug(msg string, fields watermill.LogFields) {
	z.logger.Debug(msg, z.toZapFields(fields)...)
}

func (z zapLoggerAdapterTestMessage) Trace(msg string, fields watermill.LogFields) {
	z.logger.Debug(msg, z.toZapFields(fields)...)
}

func (z zapLoggerAdapterTestMessage) With(fields watermill.LogFields) watermill.LoggerAdapter {
	newLogger := z.logger.With(z.toZapFields(fields)...)
	return &zapLoggerAdapterTestMessage{logger: newLogger}
}

func (z zapLoggerAdapterTestMessage) toZapFields(fields watermill.LogFields) []zap.Field {
	zapFields := make([]zap.Field, 0, len(fields))
	for k, v := range fields {
		zapFields = append(zapFields, zap.Any(k, v))
	}

	return zapFields
}

const subjectNameApproveCard = "card.approvecard"

type eventApproveCard struct {
	jsContext         nats_go.JetStreamContext
	natsConn          *nats_go.Conn
	natsMessageRouter *message.Router
}

func newApproveCard(
	jsContext nats_go.JetStreamContext,
	conn *nats_go.Conn,
	router *message.Router,
) *eventApproveCard {
	return &eventApproveCard{
		jsContext:         jsContext,
		natsConn:          conn,
		natsMessageRouter: router,
	}
}

var singleApproveCard func(ctx context.Context) (<-chan *ApproveCard, error)

func NewApproveCardSubscriber(
	js nats_go.JetStreamContext,
	conn *nats_go.Conn,
	router *message.Router,
) func(ctx context.Context) (<-chan *ApproveCard, error) {
	if singleApproveCard == nil {
		singleApproveCard = newApproveCard(js, conn, router).Subscribe
	}

	return singleApproveCard
}

func NewApproveCardPublisher(
	js nats_go.JetStreamContext,
) func(ctx context.Context, src *ApproveCard) error {
	return newApproveCard(js, nil, nil).Publish
}

func (s *eventApproveCard) Publish(ctx context.Context, src *ApproveCard) error {
	var err error
	eventId := uuid.New().String()

	ctx, span := otel.Tracer("otel_trace").Start(ctx, "ApproveCard.Publish")
	defer span.End()
	defer func() {
		span.RecordError(err)
		span.SetAttributes(
			attribute.Bool("error", err != nil),
			attribute.String("nats_uuid", eventId),
		)
	}()

	m, err := anypb.New(src)
	if err != nil {
		return err
	}

	data := pb.EventInfo{
		EventId:   &eventId,
		EventTime: timestamppb.Now(),
		Data:      m,
	}
	marshal, err := protojson.Marshal(&data)
	if err != nil {
		return err
	}

	header := make(nats_go.Header)
	otel.GetTextMapPropagator().Inject(ctx, propagation.HeaderCarrier(header))

	_, err = s.jsContext.PublishMsg(&nats_go.Msg{
		Subject: subjectNameApproveCard,
		Data:    marshal,
		Header:  header,
	})

	return err
}

func (s *eventApproveCard) Subscribe(ctx context.Context) (<-chan *ApproveCard, error) {
	ch := make(chan *ApproveCard)

	streamCfg := newApproveCardStreamConfig()
	consumerCfg := newApproveCardConsumerConfig()
	watermillCfg := newApproveCardWatermillConfigs()

	fun := func(msg *message.Message) error {
		data := new(pb.EventInfo)
		resApproveCard := new(ApproveCard)
		if err := protojson.Unmarshal(msg.Payload, data); err != nil {
			return err
		}

		if err := data.GetData().UnmarshalTo(resApproveCard); err != nil {
			return err
		}

		ch <- resApproveCard

		return nil
	}

	err := s.createConsumerApproveCard(streamCfg, consumerCfg)
	if err != nil {
		return nil, err
	}
	subscriber, err := nats.NewSubscriberWithNatsConn(
		s.natsConn,
		*watermillCfg,
		newZapLoggerAdapterApproveCard(zap.L()),
	)
	if err != nil {
		return nil, fmt.Errorf("err watermillNats.NewSubscriberWithNatsConn: %w", err)
	}
	go func() {
		select {
		case <-ctx.Done():
			if err := subscriber.Close(); err != nil {
				zap.L().Error("subscriber.Close", zap.Error(err))
			}
		}
	}()

	s.natsMessageRouter.AddNoPublisherHandler(
		consumerCfg.Name,
		subjectNameApproveCard,
		subscriber,
		fun,
	)

	return ch, nil
}

func newApproveCardStreamConfig() *nats_go.StreamConfig {
	return &nats_go.StreamConfig{
		Name:         strings.ReplaceAll(subjectNameApproveCard, ".", "_"),
		Subjects:     []string{subjectNameApproveCard, fmt.Sprintf("%s.>", subjectNameApproveCard)},
		Retention:    nats_go.LimitsPolicy,
		MaxConsumers: -1,
		Discard:      nats_go.DiscardOld,
		MaxAge:       time.Hour * 24 * 7,
		Storage:      nats_go.FileStorage,
		NoAck:        false,
		AllowRollup:  true,
	}
}

func newApproveCardConsumerConfig() *nats_go.ConsumerConfig {
	defaultAckWait := time.Second * 30

	streamNameWithoutDots := strings.ReplaceAll(subjectNameApproveCard, ".", "_")

	consumerName := strings.ReplaceAll(fmt.Sprintf("%s_%s",
		"events",
		streamNameWithoutDots), ".", "_")

	return &nats_go.ConsumerConfig{
		Durable:        consumerName,
		Name:           consumerName,
		AckPolicy:      nats_go.AckExplicitPolicy,
		AckWait:        defaultAckWait,
		MaxDeliver:     -1,
		DeliverGroup:   consumerName,
		DeliverSubject: consumerName,
	}
}

func newApproveCardWatermillConfigs() *nats.SubscriberSubscriptionConfig {
	defaultAckWait := time.Second * 30

	streamNameWithoutDots := strings.ReplaceAll(subjectNameApproveCard, ".", "_")

	consumerName := strings.ReplaceAll(fmt.Sprintf("%s_%s",
		"events",
		streamNameWithoutDots), ".", "_")

	return &nats.SubscriberSubscriptionConfig{
		Unmarshaler:    &eventMarshalerApproveCard{},
		AckWaitTimeout: defaultAckWait,
		CloseTimeout:   defaultAckWait,
		SubjectCalculator: func(queueGroupPrefix, topic string) *nats.SubjectDetail {
			return &nats.SubjectDetail{
				Primary:    subjectNameApproveCard,
				QueueGroup: consumerName,
			}
		},
		JetStream: nats.JetStreamConfig{
			Disabled:      false,
			AutoProvision: false,
			SubscribeOptions: []nats_go.SubOpt{
				nats_go.ManualAck(),
				nats_go.DeliverAll(),
				nats_go.AckExplicit(),
				nats_go.Bind(streamNameWithoutDots, consumerName),
				nats_go.MaxDeliver(-1),
			},
			TrackMsgId:        true,
			AckAsync:          false,
			DurableCalculator: func(prefix, topic string) string { return consumerName },
		},
	}
}

func (s *eventApproveCard) createConsumerApproveCard(streamCfg *nats_go.StreamConfig, consumerCfg *nats_go.ConsumerConfig) error {
	_, err := s.jsContext.StreamInfo(streamCfg.Name)
	if err != nil {
		if errors.Is(err, nats_go.ErrStreamNotFound) {
			_, err = s.jsContext.AddStream(streamCfg)
			if err != nil {
				return fmt.Errorf("err jsContext.AddStream: %w", err)
			}
		} else {
			return fmt.Errorf("can't get NATS JetStream Stream: %w", err)
		}
	}

	_, err = s.jsContext.UpdateStream(streamCfg)
	if err != nil {
		return fmt.Errorf("err jsContext.UpdateStream: %w", err)
	}

	_, err = s.jsContext.ConsumerInfo(streamCfg.Name, streamCfg.Name)
	if err != nil {
		if errors.Is(err, nats_go.ErrConsumerNotFound) {
			_, err = s.jsContext.AddConsumer(streamCfg.Name, consumerCfg)
			if err != nil {
				return fmt.Errorf("err jsContext.AddConsumer: %w", err)
			}
		} else {
			return fmt.Errorf("can't get NATS JetStream Consumer %s: %w", consumerCfg.Name, err)
		}
	}

	_, err = s.jsContext.UpdateConsumer(streamCfg.Name, consumerCfg)
	if err != nil {
		return fmt.Errorf("err jsContext.UpdateConsumer: %w", err)
	}

	return nil
}

type eventMarshalerApproveCard struct{}

func (*eventMarshalerApproveCard) Marshal(topic string, msg *message.Message) (*nats_go.Msg, error) {
	natsMsg := nats_go.NewMsg(topic)
	natsMsg.Data = msg.Payload

	return natsMsg, nil
}
func (*eventMarshalerApproveCard) Unmarshal(msg *nats_go.Msg) (*message.Message, error) {
	pbMsg := &pb.EventInfo{}
	err := protojson.Unmarshal(msg.Data, pbMsg)
	if err != nil {
		return nil, err
	}

	wmMsg := message.NewMessage(pbMsg.GetEventId(), msg.Data)
	wmMsg.Metadata = map[string]string{}

	return wmMsg, nil
}

type zapLoggerAdapterApproveCard struct {
	logger *zap.Logger
}

func newZapLoggerAdapterApproveCard(logger *zap.Logger) *zapLoggerAdapterApproveCard {
	return &zapLoggerAdapterApproveCard{logger: logger}
}

func (z zapLoggerAdapterApproveCard) Error(msg string, err error, fields watermill.LogFields) {
	z.logger.Error(msg, append(z.toZapFields(fields), zap.Error(err))...)
}

func (z zapLoggerAdapterApproveCard) Info(msg string, fields watermill.LogFields) {
	z.logger.Info(msg, z.toZapFields(fields)...)
}

func (z zapLoggerAdapterApproveCard) Debug(msg string, fields watermill.LogFields) {
	z.logger.Debug(msg, z.toZapFields(fields)...)
}

func (z zapLoggerAdapterApproveCard) Trace(msg string, fields watermill.LogFields) {
	z.logger.Debug(msg, z.toZapFields(fields)...)
}

func (z zapLoggerAdapterApproveCard) With(fields watermill.LogFields) watermill.LoggerAdapter {
	newLogger := z.logger.With(z.toZapFields(fields)...)
	return &zapLoggerAdapterApproveCard{logger: newLogger}
}

func (z zapLoggerAdapterApproveCard) toZapFields(fields watermill.LogFields) []zap.Field {
	zapFields := make([]zap.Field, 0, len(fields))
	for k, v := range fields {
		zapFields = append(zapFields, zap.Any(k, v))
	}

	return zapFields
}

const subjectNameModifyPan = "card.modifypan"

type eventModifyPan struct {
	jsContext         nats_go.JetStreamContext
	natsConn          *nats_go.Conn
	natsMessageRouter *message.Router
}

func newModifyPan(
	jsContext nats_go.JetStreamContext,
	conn *nats_go.Conn,
	router *message.Router,
) *eventModifyPan {
	return &eventModifyPan{
		jsContext:         jsContext,
		natsConn:          conn,
		natsMessageRouter: router,
	}
}

var singleModifyPan func(ctx context.Context) (<-chan *ModifyPan, error)

func NewModifyPanSubscriber(
	js nats_go.JetStreamContext,
	conn *nats_go.Conn,
	router *message.Router,
) func(ctx context.Context) (<-chan *ModifyPan, error) {
	if singleModifyPan == nil {
		singleModifyPan = newModifyPan(js, conn, router).Subscribe
	}

	return singleModifyPan
}

func NewModifyPanPublisher(
	js nats_go.JetStreamContext,
) func(ctx context.Context, src *ModifyPan) error {
	return newModifyPan(js, nil, nil).Publish
}

func (s *eventModifyPan) Publish(ctx context.Context, src *ModifyPan) error {
	var err error
	eventId := uuid.New().String()

	ctx, span := otel.Tracer("otel_trace").Start(ctx, "ModifyPan.Publish")
	defer span.End()
	defer func() {
		span.RecordError(err)
		span.SetAttributes(
			attribute.Bool("error", err != nil),
			attribute.String("nats_uuid", eventId),
		)
	}()

	m, err := anypb.New(src)
	if err != nil {
		return err
	}

	data := pb.EventInfo{
		EventId:   &eventId,
		EventTime: timestamppb.Now(),
		Data:      m,
	}
	marshal, err := protojson.Marshal(&data)
	if err != nil {
		return err
	}

	header := make(nats_go.Header)
	otel.GetTextMapPropagator().Inject(ctx, propagation.HeaderCarrier(header))

	_, err = s.jsContext.PublishMsg(&nats_go.Msg{
		Subject: subjectNameModifyPan,
		Data:    marshal,
		Header:  header,
	})

	return err
}

func (s *eventModifyPan) Subscribe(ctx context.Context) (<-chan *ModifyPan, error) {
	ch := make(chan *ModifyPan)

	streamCfg := newModifyPanStreamConfig()
	consumerCfg := newModifyPanConsumerConfig()
	watermillCfg := newModifyPanWatermillConfigs()

	fun := func(msg *message.Message) error {
		data := new(pb.EventInfo)
		resModifyPan := new(ModifyPan)
		if err := protojson.Unmarshal(msg.Payload, data); err != nil {
			return err
		}

		if err := data.GetData().UnmarshalTo(resModifyPan); err != nil {
			return err
		}

		ch <- resModifyPan

		return nil
	}

	err := s.createConsumerModifyPan(streamCfg, consumerCfg)
	if err != nil {
		return nil, err
	}
	subscriber, err := nats.NewSubscriberWithNatsConn(
		s.natsConn,
		*watermillCfg,
		newZapLoggerAdapterModifyPan(zap.L()),
	)
	if err != nil {
		return nil, fmt.Errorf("err watermillNats.NewSubscriberWithNatsConn: %w", err)
	}
	go func() {
		select {
		case <-ctx.Done():
			if err := subscriber.Close(); err != nil {
				zap.L().Error("subscriber.Close", zap.Error(err))
			}
		}
	}()

	s.natsMessageRouter.AddNoPublisherHandler(
		consumerCfg.Name,
		subjectNameModifyPan,
		subscriber,
		fun,
	)

	return ch, nil
}

func newModifyPanStreamConfig() *nats_go.StreamConfig {
	return &nats_go.StreamConfig{
		Name:         strings.ReplaceAll(subjectNameModifyPan, ".", "_"),
		Subjects:     []string{subjectNameModifyPan, fmt.Sprintf("%s.>", subjectNameModifyPan)},
		Retention:    nats_go.LimitsPolicy,
		MaxConsumers: -1,
		Discard:      nats_go.DiscardOld,
		MaxAge:       time.Hour * 24 * 7,
		Storage:      nats_go.FileStorage,
		NoAck:        false,
		AllowRollup:  true,
	}
}

func newModifyPanConsumerConfig() *nats_go.ConsumerConfig {
	defaultAckWait := time.Second * 30

	streamNameWithoutDots := strings.ReplaceAll(subjectNameModifyPan, ".", "_")

	consumerName := strings.ReplaceAll(fmt.Sprintf("%s_%s",
		"events",
		streamNameWithoutDots), ".", "_")

	return &nats_go.ConsumerConfig{
		Durable:        consumerName,
		Name:           consumerName,
		AckPolicy:      nats_go.AckExplicitPolicy,
		AckWait:        defaultAckWait,
		MaxDeliver:     -1,
		DeliverGroup:   consumerName,
		DeliverSubject: consumerName,
	}
}

func newModifyPanWatermillConfigs() *nats.SubscriberSubscriptionConfig {
	defaultAckWait := time.Second * 30

	streamNameWithoutDots := strings.ReplaceAll(subjectNameModifyPan, ".", "_")

	consumerName := strings.ReplaceAll(fmt.Sprintf("%s_%s",
		"events",
		streamNameWithoutDots), ".", "_")

	return &nats.SubscriberSubscriptionConfig{
		Unmarshaler:    &eventMarshalerModifyPan{},
		AckWaitTimeout: defaultAckWait,
		CloseTimeout:   defaultAckWait,
		SubjectCalculator: func(queueGroupPrefix, topic string) *nats.SubjectDetail {
			return &nats.SubjectDetail{
				Primary:    subjectNameModifyPan,
				QueueGroup: consumerName,
			}
		},
		JetStream: nats.JetStreamConfig{
			Disabled:      false,
			AutoProvision: false,
			SubscribeOptions: []nats_go.SubOpt{
				nats_go.ManualAck(),
				nats_go.DeliverAll(),
				nats_go.AckExplicit(),
				nats_go.Bind(streamNameWithoutDots, consumerName),
				nats_go.MaxDeliver(-1),
			},
			TrackMsgId:        true,
			AckAsync:          false,
			DurableCalculator: func(prefix, topic string) string { return consumerName },
		},
	}
}

func (s *eventModifyPan) createConsumerModifyPan(streamCfg *nats_go.StreamConfig, consumerCfg *nats_go.ConsumerConfig) error {
	_, err := s.jsContext.StreamInfo(streamCfg.Name)
	if err != nil {
		if errors.Is(err, nats_go.ErrStreamNotFound) {
			_, err = s.jsContext.AddStream(streamCfg)
			if err != nil {
				return fmt.Errorf("err jsContext.AddStream: %w", err)
			}
		} else {
			return fmt.Errorf("can't get NATS JetStream Stream: %w", err)
		}
	}

	_, err = s.jsContext.UpdateStream(streamCfg)
	if err != nil {
		return fmt.Errorf("err jsContext.UpdateStream: %w", err)
	}

	_, err = s.jsContext.ConsumerInfo(streamCfg.Name, streamCfg.Name)
	if err != nil {
		if errors.Is(err, nats_go.ErrConsumerNotFound) {
			_, err = s.jsContext.AddConsumer(streamCfg.Name, consumerCfg)
			if err != nil {
				return fmt.Errorf("err jsContext.AddConsumer: %w", err)
			}
		} else {
			return fmt.Errorf("can't get NATS JetStream Consumer %s: %w", consumerCfg.Name, err)
		}
	}

	_, err = s.jsContext.UpdateConsumer(streamCfg.Name, consumerCfg)
	if err != nil {
		return fmt.Errorf("err jsContext.UpdateConsumer: %w", err)
	}

	return nil
}

type eventMarshalerModifyPan struct{}

func (*eventMarshalerModifyPan) Marshal(topic string, msg *message.Message) (*nats_go.Msg, error) {
	natsMsg := nats_go.NewMsg(topic)
	natsMsg.Data = msg.Payload

	return natsMsg, nil
}
func (*eventMarshalerModifyPan) Unmarshal(msg *nats_go.Msg) (*message.Message, error) {
	pbMsg := &pb.EventInfo{}
	err := protojson.Unmarshal(msg.Data, pbMsg)
	if err != nil {
		return nil, err
	}

	wmMsg := message.NewMessage(pbMsg.GetEventId(), msg.Data)
	wmMsg.Metadata = map[string]string{}

	return wmMsg, nil
}

type zapLoggerAdapterModifyPan struct {
	logger *zap.Logger
}

func newZapLoggerAdapterModifyPan(logger *zap.Logger) *zapLoggerAdapterModifyPan {
	return &zapLoggerAdapterModifyPan{logger: logger}
}

func (z zapLoggerAdapterModifyPan) Error(msg string, err error, fields watermill.LogFields) {
	z.logger.Error(msg, append(z.toZapFields(fields), zap.Error(err))...)
}

func (z zapLoggerAdapterModifyPan) Info(msg string, fields watermill.LogFields) {
	z.logger.Info(msg, z.toZapFields(fields)...)
}

func (z zapLoggerAdapterModifyPan) Debug(msg string, fields watermill.LogFields) {
	z.logger.Debug(msg, z.toZapFields(fields)...)
}

func (z zapLoggerAdapterModifyPan) Trace(msg string, fields watermill.LogFields) {
	z.logger.Debug(msg, z.toZapFields(fields)...)
}

func (z zapLoggerAdapterModifyPan) With(fields watermill.LogFields) watermill.LoggerAdapter {
	newLogger := z.logger.With(z.toZapFields(fields)...)
	return &zapLoggerAdapterModifyPan{logger: newLogger}
}

func (z zapLoggerAdapterModifyPan) toZapFields(fields watermill.LogFields) []zap.Field {
	zapFields := make([]zap.Field, 0, len(fields))
	for k, v := range fields {
		zapFields = append(zapFields, zap.Any(k, v))
	}

	return zapFields
}

const subjectNameDeactivateToken = "card.deactivatetoken"

type eventDeactivateToken struct {
	jsContext         nats_go.JetStreamContext
	natsConn          *nats_go.Conn
	natsMessageRouter *message.Router
}

func newDeactivateToken(
	jsContext nats_go.JetStreamContext,
	conn *nats_go.Conn,
	router *message.Router,
) *eventDeactivateToken {
	return &eventDeactivateToken{
		jsContext:         jsContext,
		natsConn:          conn,
		natsMessageRouter: router,
	}
}

var singleDeactivateToken func(ctx context.Context) (<-chan *DeactivateToken, error)

func NewDeactivateTokenSubscriber(
	js nats_go.JetStreamContext,
	conn *nats_go.Conn,
	router *message.Router,
) func(ctx context.Context) (<-chan *DeactivateToken, error) {
	if singleDeactivateToken == nil {
		singleDeactivateToken = newDeactivateToken(js, conn, router).Subscribe
	}

	return singleDeactivateToken
}

func NewDeactivateTokenPublisher(
	js nats_go.JetStreamContext,
) func(ctx context.Context, src *DeactivateToken) error {
	return newDeactivateToken(js, nil, nil).Publish
}

func (s *eventDeactivateToken) Publish(ctx context.Context, src *DeactivateToken) error {
	var err error
	eventId := uuid.New().String()

	ctx, span := otel.Tracer("otel_trace").Start(ctx, "DeactivateToken.Publish")
	defer span.End()
	defer func() {
		span.RecordError(err)
		span.SetAttributes(
			attribute.Bool("error", err != nil),
			attribute.String("nats_uuid", eventId),
		)
	}()

	m, err := anypb.New(src)
	if err != nil {
		return err
	}

	data := pb.EventInfo{
		EventId:   &eventId,
		EventTime: timestamppb.Now(),
		Data:      m,
	}
	marshal, err := protojson.Marshal(&data)
	if err != nil {
		return err
	}

	header := make(nats_go.Header)
	otel.GetTextMapPropagator().Inject(ctx, propagation.HeaderCarrier(header))

	_, err = s.jsContext.PublishMsg(&nats_go.Msg{
		Subject: subjectNameDeactivateToken,
		Data:    marshal,
		Header:  header,
	})

	return err
}

func (s *eventDeactivateToken) Subscribe(ctx context.Context) (<-chan *DeactivateToken, error) {
	ch := make(chan *DeactivateToken)

	streamCfg := newDeactivateTokenStreamConfig()
	consumerCfg := newDeactivateTokenConsumerConfig()
	watermillCfg := newDeactivateTokenWatermillConfigs()

	fun := func(msg *message.Message) error {
		data := new(pb.EventInfo)
		resDeactivateToken := new(DeactivateToken)
		if err := protojson.Unmarshal(msg.Payload, data); err != nil {
			return err
		}

		if err := data.GetData().UnmarshalTo(resDeactivateToken); err != nil {
			return err
		}

		ch <- resDeactivateToken

		return nil
	}

	err := s.createConsumerDeactivateToken(streamCfg, consumerCfg)
	if err != nil {
		return nil, err
	}
	subscriber, err := nats.NewSubscriberWithNatsConn(
		s.natsConn,
		*watermillCfg,
		newZapLoggerAdapterDeactivateToken(zap.L()),
	)
	if err != nil {
		return nil, fmt.Errorf("err watermillNats.NewSubscriberWithNatsConn: %w", err)
	}
	go func() {
		select {
		case <-ctx.Done():
			if err := subscriber.Close(); err != nil {
				zap.L().Error("subscriber.Close", zap.Error(err))
			}
		}
	}()

	s.natsMessageRouter.AddNoPublisherHandler(
		consumerCfg.Name,
		subjectNameDeactivateToken,
		subscriber,
		fun,
	)

	return ch, nil
}

func newDeactivateTokenStreamConfig() *nats_go.StreamConfig {
	return &nats_go.StreamConfig{
		Name:         strings.ReplaceAll(subjectNameDeactivateToken, ".", "_"),
		Subjects:     []string{subjectNameDeactivateToken, fmt.Sprintf("%s.>", subjectNameDeactivateToken)},
		Retention:    nats_go.LimitsPolicy,
		MaxConsumers: -1,
		Discard:      nats_go.DiscardOld,
		MaxAge:       time.Hour * 24 * 7,
		Storage:      nats_go.FileStorage,
		NoAck:        false,
		AllowRollup:  true,
	}
}

func newDeactivateTokenConsumerConfig() *nats_go.ConsumerConfig {
	defaultAckWait := time.Second * 30

	streamNameWithoutDots := strings.ReplaceAll(subjectNameDeactivateToken, ".", "_")

	consumerName := strings.ReplaceAll(fmt.Sprintf("%s_%s",
		"events",
		streamNameWithoutDots), ".", "_")

	return &nats_go.ConsumerConfig{
		Durable:        consumerName,
		Name:           consumerName,
		AckPolicy:      nats_go.AckExplicitPolicy,
		AckWait:        defaultAckWait,
		MaxDeliver:     -1,
		DeliverGroup:   consumerName,
		DeliverSubject: consumerName,
	}
}

func newDeactivateTokenWatermillConfigs() *nats.SubscriberSubscriptionConfig {
	defaultAckWait := time.Second * 30

	streamNameWithoutDots := strings.ReplaceAll(subjectNameDeactivateToken, ".", "_")

	consumerName := strings.ReplaceAll(fmt.Sprintf("%s_%s",
		"events",
		streamNameWithoutDots), ".", "_")

	return &nats.SubscriberSubscriptionConfig{
		Unmarshaler:    &eventMarshalerDeactivateToken{},
		AckWaitTimeout: defaultAckWait,
		CloseTimeout:   defaultAckWait,
		SubjectCalculator: func(queueGroupPrefix, topic string) *nats.SubjectDetail {
			return &nats.SubjectDetail{
				Primary:    subjectNameDeactivateToken,
				QueueGroup: consumerName,
			}
		},
		JetStream: nats.JetStreamConfig{
			Disabled:      false,
			AutoProvision: false,
			SubscribeOptions: []nats_go.SubOpt{
				nats_go.ManualAck(),
				nats_go.DeliverAll(),
				nats_go.AckExplicit(),
				nats_go.Bind(streamNameWithoutDots, consumerName),
				nats_go.MaxDeliver(-1),
			},
			TrackMsgId:        true,
			AckAsync:          false,
			DurableCalculator: func(prefix, topic string) string { return consumerName },
		},
	}
}

func (s *eventDeactivateToken) createConsumerDeactivateToken(streamCfg *nats_go.StreamConfig, consumerCfg *nats_go.ConsumerConfig) error {
	_, err := s.jsContext.StreamInfo(streamCfg.Name)
	if err != nil {
		if errors.Is(err, nats_go.ErrStreamNotFound) {
			_, err = s.jsContext.AddStream(streamCfg)
			if err != nil {
				return fmt.Errorf("err jsContext.AddStream: %w", err)
			}
		} else {
			return fmt.Errorf("can't get NATS JetStream Stream: %w", err)
		}
	}

	_, err = s.jsContext.UpdateStream(streamCfg)
	if err != nil {
		return fmt.Errorf("err jsContext.UpdateStream: %w", err)
	}

	_, err = s.jsContext.ConsumerInfo(streamCfg.Name, streamCfg.Name)
	if err != nil {
		if errors.Is(err, nats_go.ErrConsumerNotFound) {
			_, err = s.jsContext.AddConsumer(streamCfg.Name, consumerCfg)
			if err != nil {
				return fmt.Errorf("err jsContext.AddConsumer: %w", err)
			}
		} else {
			return fmt.Errorf("can't get NATS JetStream Consumer %s: %w", consumerCfg.Name, err)
		}
	}

	_, err = s.jsContext.UpdateConsumer(streamCfg.Name, consumerCfg)
	if err != nil {
		return fmt.Errorf("err jsContext.UpdateConsumer: %w", err)
	}

	return nil
}

type eventMarshalerDeactivateToken struct{}

func (*eventMarshalerDeactivateToken) Marshal(topic string, msg *message.Message) (*nats_go.Msg, error) {
	natsMsg := nats_go.NewMsg(topic)
	natsMsg.Data = msg.Payload

	return natsMsg, nil
}
func (*eventMarshalerDeactivateToken) Unmarshal(msg *nats_go.Msg) (*message.Message, error) {
	pbMsg := &pb.EventInfo{}
	err := protojson.Unmarshal(msg.Data, pbMsg)
	if err != nil {
		return nil, err
	}

	wmMsg := message.NewMessage(pbMsg.GetEventId(), msg.Data)
	wmMsg.Metadata = map[string]string{}

	return wmMsg, nil
}

type zapLoggerAdapterDeactivateToken struct {
	logger *zap.Logger
}

func newZapLoggerAdapterDeactivateToken(logger *zap.Logger) *zapLoggerAdapterDeactivateToken {
	return &zapLoggerAdapterDeactivateToken{logger: logger}
}

func (z zapLoggerAdapterDeactivateToken) Error(msg string, err error, fields watermill.LogFields) {
	z.logger.Error(msg, append(z.toZapFields(fields), zap.Error(err))...)
}

func (z zapLoggerAdapterDeactivateToken) Info(msg string, fields watermill.LogFields) {
	z.logger.Info(msg, z.toZapFields(fields)...)
}

func (z zapLoggerAdapterDeactivateToken) Debug(msg string, fields watermill.LogFields) {
	z.logger.Debug(msg, z.toZapFields(fields)...)
}

func (z zapLoggerAdapterDeactivateToken) Trace(msg string, fields watermill.LogFields) {
	z.logger.Debug(msg, z.toZapFields(fields)...)
}

func (z zapLoggerAdapterDeactivateToken) With(fields watermill.LogFields) watermill.LoggerAdapter {
	newLogger := z.logger.With(z.toZapFields(fields)...)
	return &zapLoggerAdapterDeactivateToken{logger: newLogger}
}

func (z zapLoggerAdapterDeactivateToken) toZapFields(fields watermill.LogFields) []zap.Field {
	zapFields := make([]zap.Field, 0, len(fields))
	for k, v := range fields {
		zapFields = append(zapFields, zap.Any(k, v))
	}

	return zapFields
}

const subjectNameSaveToken = "card.savetoken"

type eventSaveToken struct {
	jsContext         nats_go.JetStreamContext
	natsConn          *nats_go.Conn
	natsMessageRouter *message.Router
}

func newSaveToken(
	jsContext nats_go.JetStreamContext,
	conn *nats_go.Conn,
	router *message.Router,
) *eventSaveToken {
	return &eventSaveToken{
		jsContext:         jsContext,
		natsConn:          conn,
		natsMessageRouter: router,
	}
}

var singleSaveToken func(ctx context.Context) (<-chan *SaveToken, error)

func NewSaveTokenSubscriber(
	js nats_go.JetStreamContext,
	conn *nats_go.Conn,
	router *message.Router,
) func(ctx context.Context) (<-chan *SaveToken, error) {
	if singleSaveToken == nil {
		singleSaveToken = newSaveToken(js, conn, router).Subscribe
	}

	return singleSaveToken
}

func NewSaveTokenPublisher(
	js nats_go.JetStreamContext,
) func(ctx context.Context, src *SaveToken) error {
	return newSaveToken(js, nil, nil).Publish
}

func (s *eventSaveToken) Publish(ctx context.Context, src *SaveToken) error {
	var err error
	eventId := uuid.New().String()

	ctx, span := otel.Tracer("otel_trace").Start(ctx, "SaveToken.Publish")
	defer span.End()
	defer func() {
		span.RecordError(err)
		span.SetAttributes(
			attribute.Bool("error", err != nil),
			attribute.String("nats_uuid", eventId),
		)
	}()

	m, err := anypb.New(src)
	if err != nil {
		return err
	}

	data := pb.EventInfo{
		EventId:   &eventId,
		EventTime: timestamppb.Now(),
		Data:      m,
	}
	marshal, err := protojson.Marshal(&data)
	if err != nil {
		return err
	}

	header := make(nats_go.Header)
	otel.GetTextMapPropagator().Inject(ctx, propagation.HeaderCarrier(header))

	_, err = s.jsContext.PublishMsg(&nats_go.Msg{
		Subject: subjectNameSaveToken,
		Data:    marshal,
		Header:  header,
	})

	return err
}

func (s *eventSaveToken) Subscribe(ctx context.Context) (<-chan *SaveToken, error) {
	ch := make(chan *SaveToken)

	streamCfg := newSaveTokenStreamConfig()
	consumerCfg := newSaveTokenConsumerConfig()
	watermillCfg := newSaveTokenWatermillConfigs()

	fun := func(msg *message.Message) error {
		data := new(pb.EventInfo)
		resSaveToken := new(SaveToken)
		if err := protojson.Unmarshal(msg.Payload, data); err != nil {
			return err
		}

		if err := data.GetData().UnmarshalTo(resSaveToken); err != nil {
			return err
		}

		ch <- resSaveToken

		return nil
	}

	err := s.createConsumerSaveToken(streamCfg, consumerCfg)
	if err != nil {
		return nil, err
	}
	subscriber, err := nats.NewSubscriberWithNatsConn(
		s.natsConn,
		*watermillCfg,
		newZapLoggerAdapterSaveToken(zap.L()),
	)
	if err != nil {
		return nil, fmt.Errorf("err watermillNats.NewSubscriberWithNatsConn: %w", err)
	}
	go func() {
		select {
		case <-ctx.Done():
			if err := subscriber.Close(); err != nil {
				zap.L().Error("subscriber.Close", zap.Error(err))
			}
		}
	}()

	s.natsMessageRouter.AddNoPublisherHandler(
		consumerCfg.Name,
		subjectNameSaveToken,
		subscriber,
		fun,
	)

	return ch, nil
}

func newSaveTokenStreamConfig() *nats_go.StreamConfig {
	return &nats_go.StreamConfig{
		Name:         strings.ReplaceAll(subjectNameSaveToken, ".", "_"),
		Subjects:     []string{subjectNameSaveToken, fmt.Sprintf("%s.>", subjectNameSaveToken)},
		Retention:    nats_go.LimitsPolicy,
		MaxConsumers: -1,
		Discard:      nats_go.DiscardOld,
		MaxAge:       time.Hour * 24 * 7,
		Storage:      nats_go.FileStorage,
		NoAck:        false,
		AllowRollup:  true,
	}
}

func newSaveTokenConsumerConfig() *nats_go.ConsumerConfig {
	defaultAckWait := time.Second * 30

	streamNameWithoutDots := strings.ReplaceAll(subjectNameSaveToken, ".", "_")

	consumerName := strings.ReplaceAll(fmt.Sprintf("%s_%s",
		"events",
		streamNameWithoutDots), ".", "_")

	return &nats_go.ConsumerConfig{
		Durable:        consumerName,
		Name:           consumerName,
		AckPolicy:      nats_go.AckExplicitPolicy,
		AckWait:        defaultAckWait,
		MaxDeliver:     -1,
		DeliverGroup:   consumerName,
		DeliverSubject: consumerName,
	}
}

func newSaveTokenWatermillConfigs() *nats.SubscriberSubscriptionConfig {
	defaultAckWait := time.Second * 30

	streamNameWithoutDots := strings.ReplaceAll(subjectNameSaveToken, ".", "_")

	consumerName := strings.ReplaceAll(fmt.Sprintf("%s_%s",
		"events",
		streamNameWithoutDots), ".", "_")

	return &nats.SubscriberSubscriptionConfig{
		Unmarshaler:    &eventMarshalerSaveToken{},
		AckWaitTimeout: defaultAckWait,
		CloseTimeout:   defaultAckWait,
		SubjectCalculator: func(queueGroupPrefix, topic string) *nats.SubjectDetail {
			return &nats.SubjectDetail{
				Primary:    subjectNameSaveToken,
				QueueGroup: consumerName,
			}
		},
		JetStream: nats.JetStreamConfig{
			Disabled:      false,
			AutoProvision: false,
			SubscribeOptions: []nats_go.SubOpt{
				nats_go.ManualAck(),
				nats_go.DeliverAll(),
				nats_go.AckExplicit(),
				nats_go.Bind(streamNameWithoutDots, consumerName),
				nats_go.MaxDeliver(-1),
			},
			TrackMsgId:        true,
			AckAsync:          false,
			DurableCalculator: func(prefix, topic string) string { return consumerName },
		},
	}
}

func (s *eventSaveToken) createConsumerSaveToken(streamCfg *nats_go.StreamConfig, consumerCfg *nats_go.ConsumerConfig) error {
	_, err := s.jsContext.StreamInfo(streamCfg.Name)
	if err != nil {
		if errors.Is(err, nats_go.ErrStreamNotFound) {
			_, err = s.jsContext.AddStream(streamCfg)
			if err != nil {
				return fmt.Errorf("err jsContext.AddStream: %w", err)
			}
		} else {
			return fmt.Errorf("can't get NATS JetStream Stream: %w", err)
		}
	}

	_, err = s.jsContext.UpdateStream(streamCfg)
	if err != nil {
		return fmt.Errorf("err jsContext.UpdateStream: %w", err)
	}

	_, err = s.jsContext.ConsumerInfo(streamCfg.Name, streamCfg.Name)
	if err != nil {
		if errors.Is(err, nats_go.ErrConsumerNotFound) {
			_, err = s.jsContext.AddConsumer(streamCfg.Name, consumerCfg)
			if err != nil {
				return fmt.Errorf("err jsContext.AddConsumer: %w", err)
			}
		} else {
			return fmt.Errorf("can't get NATS JetStream Consumer %s: %w", consumerCfg.Name, err)
		}
	}

	_, err = s.jsContext.UpdateConsumer(streamCfg.Name, consumerCfg)
	if err != nil {
		return fmt.Errorf("err jsContext.UpdateConsumer: %w", err)
	}

	return nil
}

type eventMarshalerSaveToken struct{}

func (*eventMarshalerSaveToken) Marshal(topic string, msg *message.Message) (*nats_go.Msg, error) {
	natsMsg := nats_go.NewMsg(topic)
	natsMsg.Data = msg.Payload

	return natsMsg, nil
}
func (*eventMarshalerSaveToken) Unmarshal(msg *nats_go.Msg) (*message.Message, error) {
	pbMsg := &pb.EventInfo{}
	err := protojson.Unmarshal(msg.Data, pbMsg)
	if err != nil {
		return nil, err
	}

	wmMsg := message.NewMessage(pbMsg.GetEventId(), msg.Data)
	wmMsg.Metadata = map[string]string{}

	return wmMsg, nil
}

type zapLoggerAdapterSaveToken struct {
	logger *zap.Logger
}

func newZapLoggerAdapterSaveToken(logger *zap.Logger) *zapLoggerAdapterSaveToken {
	return &zapLoggerAdapterSaveToken{logger: logger}
}

func (z zapLoggerAdapterSaveToken) Error(msg string, err error, fields watermill.LogFields) {
	z.logger.Error(msg, append(z.toZapFields(fields), zap.Error(err))...)
}

func (z zapLoggerAdapterSaveToken) Info(msg string, fields watermill.LogFields) {
	z.logger.Info(msg, z.toZapFields(fields)...)
}

func (z zapLoggerAdapterSaveToken) Debug(msg string, fields watermill.LogFields) {
	z.logger.Debug(msg, z.toZapFields(fields)...)
}

func (z zapLoggerAdapterSaveToken) Trace(msg string, fields watermill.LogFields) {
	z.logger.Debug(msg, z.toZapFields(fields)...)
}

func (z zapLoggerAdapterSaveToken) With(fields watermill.LogFields) watermill.LoggerAdapter {
	newLogger := z.logger.With(z.toZapFields(fields)...)
	return &zapLoggerAdapterSaveToken{logger: newLogger}
}

func (z zapLoggerAdapterSaveToken) toZapFields(fields watermill.LogFields) []zap.Field {
	zapFields := make([]zap.Field, 0, len(fields))
	for k, v := range fields {
		zapFields = append(zapFields, zap.Any(k, v))
	}

	return zapFields
}
