package main

import (
	"encoding/json"
	"fmt"
	"net/url"
	"path/filepath"
	"regexp"
	"sort"
	"strconv"
	"strings"

	"github.com/clbanning/mxj"
	"google.golang.org/protobuf/compiler/protogen"
	"google.golang.org/protobuf/encoding/protojson"
	"google.golang.org/protobuf/proto"
	"google.golang.org/protobuf/reflect/protoreflect"
	"google.golang.org/protobuf/types/dynamicpb"

	"git.local/sensitive/mvp/pb"
)

type RestGenerator struct{}

func (generator *RestGenerator) ProtoGenGenerate(gen *protogen.Plugin) {
	for _, file := range gen.Files {
		if !file.Generate {
			continue
		}

		dir := filepath.Dir(file.GeneratedFilenamePrefix)
		mockGenNamePackage := filepath.Base(dir)
		fileName := filepath.Base(file.GeneratedFilenamePrefix + V2_REST_SUFFIX)
		mockGenFileName := filepath.Base(file.GeneratedFilenamePrefix + V2_REST_SUFFIX)

		g := gen.NewGeneratedFile(
			file.GeneratedFilenamePrefix+V2_REST_SUFFIX,
			file.GoImportPath,
		)

		HeaderPrint(g, string(file.GoPackageName))

		gTest := gen.NewGeneratedFile(
			file.GeneratedFilenamePrefix+V2_REST_TEST_SUFFIX,
			file.GoImportPath,
		)

		HeaderPrint(gTest, string(file.GoPackageName))

		linear := map[protoreflect.FullName]*Uniq{}
		mapper := map[protoreflect.FullName]map[protoreflect.FullName]*Uniq{}
		funcNameStorage := map[string]struct{}{}

		for _, service := range file.Services {
			serviceSettings, _ := proto.GetExtension(service.Desc.Options(), pb.E_RestServiceOptions).(*pb.RestServiceOptions)
			if serviceSettings == nil {
				continue
			}

			if len(serviceSettings.GetHosts()) == 0 {
				panic("no hosts")
			}

			g.P("var rest", service.GoName, "BaseURL = map[string]string{")

			for _, host := range serviceSettings.GetHosts() {
				if host.GetStand() == "" {
					panic("no stand")
				}

				if host.GetBaseUri() == "" {
					panic("no base uri")
				}

				if _, err := url.Parse(host.GetBaseUri()); err != nil {
					panic(err)
				}

				g.P("\"", host.GetStand(), "\": \"", host.GetBaseUri(), "\",")
			}

			g.P("}")
			g.P()

			g.P("type Rest", service.GoName, "ServerConfig struct {")
			g.P("DocFunc ", docPackageFunc)
			g.P()
			for _, method := range service.Methods {
				methodHookIdent := CreateMethodHookIdent(file, service, method)
				g.P("Hooks", method.GoName, " *", methodHookIdent)
			}
			g.P("}")
			g.P()

			g.P("func (config *Rest", service.GoName, "ServerConfig) Build(stand string) *rest", service.GoName, "Server {")
			g.P("if config == nil {")
			g.P("config = &Rest", service.GoName, "ServerConfig{}")
			g.P("}")
			g.P()
			g.P("if config.DocFunc == nil {")
			g.P("config.DocFunc = func(ctx context.Context, doc *doc.Struct) {}")
			g.P("}")
			g.P()
			g.P("return &rest", service.GoName, "Server{")
			g.P("baseUrl: rest", service.GoName, "BaseURL[stand],")
			g.P("docFunc: config.DocFunc,")
			g.P()
			for _, method := range service.Methods {
				ident := CreateMethodHookIdent(file, service, method)
				g.P("hooks", method.GoName, ": Prepare", ident, "(config.Hooks", method.GoName, "),")
			}
			g.P("}")
			g.P("}")
			g.P()

			g.P("type rest", service.GoName, "Server struct {")
			g.P("baseUrl string")
			g.P("docFunc ", docPackageFunc)
			g.P()
			for _, method := range service.Methods {
				methodHookIdent := CreateMethodHookIdent(file, service, method)
				g.P("hooks", method.GoName, " *", methodHookIdent)
			}
			g.P("}")
			g.P()

			g.P("func NewRest", service.GoName, "Server(")
			g.P("stand string,")
			g.P("config *Rest", service.GoName, "ServerConfig,")
			g.P(") Rest", service.GoName, " {")
			g.P("return config.Build(stand)")
			g.P("}")
			g.P()

			var methods []*RestMethod
			g.P("//go:generate mockgen -destination=./", mockGenNamePackage, "mock/", mockGenFileName, " -package=", mockGenNamePackage, "mock -source=", fileName, " Rest", service.GoName)
			g.P("type Rest", service.GoName, " interface {")
			for _, method := range service.Methods {

				sort.Slice(method.Output.Fields, func(i, j int) bool {
					return method.Output.Fields[i].Desc.Number() < method.Output.Fields[j].Desc.Number()
				})

				computed := NewRestMethod(
					file,
					g,
					gTest,
					service,
					method,
					serviceSettings,
					linear,
					mapper,
					funcNameStorage,
					CreateMethodHookIdent(file, service, method),
				)

				computed.GenerateInterface()
				methods = append(methods, computed)
			}
			g.P("}")
			g.P()

			for _, computed := range methods {
				computed.Generate()
				computed.GenerateTests()
			}
		}

		g = gen.NewGeneratedFile(
			file.GeneratedFilenamePrefix+V2_REST_HELPERS_SUFFIX,
			file.GoImportPath,
		)

		HeaderPrint(g, string(file.GoPackageName))

		{
			var keys []protoreflect.FullName
			for k := range mapper {
				keys = append(keys, k)
			}
			sort.Slice(keys, func(i, j int) bool {
				return keys[i] < keys[j]
			})

			for _, key := range keys {
				uniq := mapper[key]

				var childrenKeys []protoreflect.FullName
				for k := range uniq {
					childrenKeys = append(childrenKeys, k)
				}
				sort.Slice(childrenKeys, func(i, j int) bool {
					return childrenKeys[i] < childrenKeys[j]
				})

				for _, childKey := range childrenKeys {
					u := uniq[childKey]

					for _, datum := range u.DataGo {
						g.P(datum...)
					}
					g.P()
					for _, datum := range u.Data {
						g.P(datum...)
					}
					g.P()
				}
			}
		}

		{
			var keys []protoreflect.FullName
			for k := range linear {
				keys = append(keys, k)
			}
			sort.Slice(keys, func(i, j int) bool {
				return keys[i] < keys[j]
			})

			for k := range keys {
				uniq := linear[keys[k]]

				for _, datum := range uniq.DataGo {
					g.P(datum...)
				}
				g.P()
				for _, datum := range uniq.Data {
					g.P(datum...)
				}
				g.P()

				if uniq.NeedSlice {
					for _, datum := range uniq.SliceDataGo {
						g.P(datum...)
					}
					g.P()

					for _, datum := range uniq.SliceData {
						g.P(datum...)
					}
					g.P()
				}
			}
		}

	}
}

func CreateMethodHookIdent(
	file *protogen.File,
	service *protogen.Service,
	method *protogen.Method,
) protogen.GoIdent {
	return protogen.GoIdent{
		GoName:       file.GoDescriptorIdent.GoName + "_" + service.GoName + "_" + method.GoName + "_Hooks",
		GoImportPath: file.GoImportPath,
	}
}

func MarshalJoin(marshals ...*pb.RestMarshal) *pb.RestMarshal {
	for _, marshal := range marshals {
		if marshal != nil {
			return marshal
		}
	}

	native := pb.RestMarshal_Native
	return &native
}

func RestMethodOptionsJoin(
	service *pb.RestServiceOptions,
	method *pb.RestMethodOptions,
) *pb.RestMethodOptions {
	if service == nil && method == nil {
		return nil
	}

	result := &pb.RestMethodOptions{}
	if service != nil {
		result = &pb.RestMethodOptions{
			Method:        nil,
			Authorization: service.DefaultAuthorization,
			RequestMarshal: MarshalJoin(
				method.RequestMarshal,
				service.DefaultRequestMarshal,
				service.DefaultMarshal,
			),
			ResponseUnmarshal: MarshalJoin(
				method.ResponseUnmarshal,
				service.DefaultResponseUnmarshal,
				service.DefaultUnmarshal,
			),
			Path:              nil,
			QueryParam:        nil,
			QueryParamSlice:   nil,
			MaxRequestTimeout: nil,
			BackoffPolicy:     nil,
		}
	}

	if method != nil {
		result.Method = method.Method
		result.Path = method.Path
		result.QueryParam = method.QueryParam
		result.QueryParamSlice = method.QueryParamSlice
		result.MaxRequestTimeout = method.MaxRequestTimeout
		result.BackoffPolicy = method.BackoffPolicy

		if method.Authorization != nil {
			result.Authorization = method.Authorization
		}

		if method.RequestMarshal != nil {
			result.RequestMarshal = method.RequestMarshal
		}

		if method.ResponseUnmarshal != nil {
			result.ResponseUnmarshal = method.ResponseUnmarshal
		}
	}

	if result.Method == nil {
		methodGet := pb.RestHttpMethod_Get
		result.Method = &methodGet
	}

	return result
}

func GetBuiltinTypeName(
	file *protogen.File,
	kind protoreflect.Kind,
) (protogen.GoIdent, bool) {
	ident := file.GoDescriptorIdent
	switch kind {
	case protoreflect.BoolKind:
		ident.GoName = "bool"
	case protoreflect.Int32Kind, protoreflect.Sint32Kind, protoreflect.Sfixed32Kind:
		ident.GoName = "int32"
	case protoreflect.Uint32Kind, protoreflect.Fixed32Kind:
		ident.GoName = "uint32"
	case protoreflect.Int64Kind, protoreflect.Sint64Kind, protoreflect.Sfixed64Kind:
		ident.GoName = "int64"
	case protoreflect.Uint64Kind, protoreflect.Fixed64Kind:
		ident.GoName = "uint64"
	case protoreflect.FloatKind:
		ident.GoName = "float32"
	case protoreflect.DoubleKind:
		ident.GoName = "float64"
	case protoreflect.StringKind:
		ident.GoName = "string"
	case protoreflect.BytesKind:
		ident.GoName = "[]byte"
	default:
		return ident, false
	}

	return ident, true
}

func hasBody(method *pb.RestHttpMethod) bool {
	return method != nil && (*method == pb.RestHttpMethod_Post || *method == pb.RestHttpMethod_Put || *method == pb.RestHttpMethod_Patch)
}

var (
	pathRestGeneratorReg = regexp.MustCompile(`\$\{([A-z0-9]+)\}`)
)

type RestPathParam struct {
	ClearName string
	Name      string
	GoIdent   protogen.GoIdent
}

type RestPath struct {
	ctx              RestPathParam
	input            RestPathParam
	authorization    pb.RestHttpAuthorization
	authParams       []RestPathParam
	pathParams       []RestPathParam
	queryParams      []RestPathParam
	querySliceParams []RestPathParam

	pathFormat string
	formatArg  []RestPathParam
}

func (p *RestPath) PrintAllArguments(
	g *protogen.GeneratedFile,
	withIdents bool,
) {
	if withIdents {
		g.P(p.ctx.Name, " ", p.ctx.GoIdent, ",")
		g.P(p.input.Name, " *", p.input.GoIdent, ",")
		for _, param := range p.authParams {
			g.P(param.Name, " ", param.GoIdent, ",")
		}
		for _, param := range p.pathParams {
			g.P(param.Name, " ", param.GoIdent, ",")
		}
		for _, param := range p.queryParams {
			g.P(param.Name, " ", param.GoIdent, ",")
		}
		for _, param := range p.querySliceParams {
			g.P(param.Name, " ", param.GoIdent, ",")
		}
	} else {
		g.P(p.ctx.Name, ",")
		g.P(p.input.Name, ",")
		for _, param := range p.authParams {
			g.P(param.Name, ",")
		}
		for _, param := range p.pathParams {
			g.P(param.Name, ",")
		}
		for _, param := range p.queryParams {
			g.P(param.Name, ",")
		}
		for _, param := range p.querySliceParams {
			g.P(param.Name, ",")
		}
	}
}

func ParseRestPath(
	file *protogen.File,
	method *protogen.Method,
	opts *pb.RestMethodOptions,
) *RestPath {
	result := RestPath{
		ctx: RestPathParam{
			Name:    "ctx",
			GoIdent: contextContextIdent,
		},
		input: RestPathParam{
			Name:    "request",
			GoIdent: method.Input.GoIdent,
		},
		authParams:       nil,
		pathParams:       nil,
		queryParams:      nil,
		querySliceParams: nil,
		pathFormat:       "",
		formatArg:        nil,
	}

	if opts == nil {
		return &result
	}

	for _, param := range opts.GetQueryParam() {
		result.queryParams = append(result.queryParams, RestPathParam{
			ClearName: param,
			Name:      "query_param_" + param,
			GoIdent:   protogen.GoIdent{GoName: "any", GoImportPath: file.GoImportPath},
		})
	}

	for _, param := range opts.GetQueryParamSlice() {
		result.querySliceParams = append(result.querySliceParams, RestPathParam{
			ClearName: param,
			Name:      "query_slice_param_" + param,
			GoIdent:   protogen.GoIdent{GoName: "[]any", GoImportPath: file.GoImportPath},
		})
	}

	switch result.authorization = opts.GetAuthorization(); result.authorization {
	case pb.RestHttpAuthorization_Basic:
		usernameParam := RestPathParam{
			Name:    "username",
			GoIdent: protogen.GoIdent{GoName: "string", GoImportPath: file.GoImportPath},
		}
		result.authParams = append(result.authParams, usernameParam)

		passwordParam := RestPathParam{
			Name:    "password",
			GoIdent: protogen.GoIdent{GoName: "string", GoImportPath: file.GoImportPath},
		}
		result.authParams = append(result.authParams, passwordParam)
	case pb.RestHttpAuthorization_Bearer:
		authTokenParam := RestPathParam{
			Name:    "token",
			GoIdent: protogen.GoIdent{GoName: "string", GoImportPath: file.GoImportPath},
		}
		result.authParams = append(result.authParams, authTokenParam)
	}

	if result.pathFormat = opts.GetPath(); result.pathFormat == "" {
		return &result
	}

	matches := pathRestGeneratorReg.FindAllStringSubmatch(result.pathFormat, -1)
	uniqPathParams := map[string]struct{}{}
	for _, match := range matches {
		pathParam := match[1]

		arg := RestPathParam{
			Name:    "path_param_" + pathParam,
			GoIdent: protogen.GoIdent{GoName: "any", GoImportPath: file.GoImportPath},
		}
		result.formatArg = append(result.formatArg, arg)

		if _, ok := uniqPathParams[pathParam]; ok {
			continue
		}

		uniqPathParams[pathParam] = struct{}{}
		result.pathParams = append(result.pathParams, arg)
	}

	if len(matches) > 0 {
		result.pathFormat = pathRestGeneratorReg.ReplaceAllString(result.pathFormat, "%v")
	}

	return &result
}

var _PrettyBytesLong = 16

func PrettyBytes(
	b []byte,
) []any {
	var stringSlice []string
	for _, v := range b {
		stringSlice = append(stringSlice, "0x"+strconv.FormatInt(int64(v), 16))
	}

	var result []any
	for i := 0; i < len(stringSlice); i += _PrettyBytesLong {
		end := i + _PrettyBytesLong
		if end > len(stringSlice) {
			end = len(stringSlice)
		}

		result = append(result, strings.Join(stringSlice[i:end], ",")+",")
	}

	return result
}

func NewRestMethod(
	file *protogen.File,
	g *protogen.GeneratedFile,
	gTest *protogen.GeneratedFile,
	service *protogen.Service,
	method *protogen.Method,
	serviceOpts *pb.RestServiceOptions,
	linear map[protoreflect.FullName]*Uniq,
	mapper map[protoreflect.FullName]map[protoreflect.FullName]*Uniq,
	funcNameStorage map[string]struct{},
	methodHookIdent protogen.GoIdent,
) *RestMethod {
	methodOpts, _ := proto.GetExtension(method.Desc.Options(), pb.E_RestMethodOptions).(*pb.RestMethodOptions)
	var (
		path           string
		pathParams     []any
		uniqPathParams = map[string]struct{}{}
	)

	if methodOpts != nil {
		path = methodOpts.GetPath()
		if path != "" {
			matches := pathRestGeneratorReg.FindAllStringSubmatch(path, -1)

			for _, match := range matches {
				pathParam := match[1]

				if _, ok := uniqPathParams[pathParam]; !ok {
					pathParams = append(pathParams, "path_param_"+pathParam)
					uniqPathParams[pathParam] = struct{}{}
				}
			}

			if len(matches) > 0 {
				path = pathRestGeneratorReg.ReplaceAllString(path, "%v")
			}
		}
	} else {
		methodOpts = &pb.RestMethodOptions{}
	}

	var answersIdentities []string
	for _, field := range method.Output.Fields {
		answersIdentities = append(answersIdentities, field.GoName)
	}

	opts := RestMethodOptionsJoin(serviceOpts, methodOpts)
	inputUniq := MessageInfo(file, method.Input, linear, mapper, funcNameStorage)
	withBody := (opts.GetMethod() == pb.RestHttpMethod_Put || opts.GetMethod() == pb.RestHttpMethod_Patch || opts.GetMethod() == pb.RestHttpMethod_Post) && len(inputUniq.Message.Fields) == 1
	if withBody {
		if ext := proto.GetExtension(method.Input.Fields[0].Desc.Options(), pb.E_FieldMarshal); ext != nil {
			if fieldExt, ok := ext.(pb.RestMarshal); ok && fieldExt != pb.RestMarshal_NoneRestMarshal {
				opts.RequestMarshal = &fieldExt
			}
		}
	}
	return &RestMethod{
		file:    file,
		g:       g,
		gTest:   gTest,
		service: service,
		method:  method,
		answerTypeIdent: protogen.GoIdent{
			GoName:       file.GoDescriptorIdent.GoName + "_" + service.GoName + "_" + method.GoName,
			GoImportPath: file.GoDescriptorIdent.GoImportPath,
		},
		responseIdent: protogen.GoIdent{
			GoName:       service.GoName + "_" + method.GoName + "_Response",
			GoImportPath: file.GoDescriptorIdent.GoImportPath,
		},

		backoffPolicyResponseIdent: protogen.GoIdent{
			GoName:       service.GoName + "_" + method.GoName + "_BackOff_Response",
			GoImportPath: file.GoDescriptorIdent.GoImportPath,
		},
		opts:            opts,
		withBody:        withBody,
		restPath:        ParseRestPath(file, method, methodOpts),
		inputUniq:       inputUniq,
		outputUniq:      MessageInfo(file, method.Output, linear, mapper, funcNameStorage),
		methodHookIdent: methodHookIdent,
		linear:          linear,
		mapper:          mapper,
		funcNameStorage: funcNameStorage,
	}
}

type RestMethod struct {
	file                       *protogen.File
	g                          *protogen.GeneratedFile
	gTest                      *protogen.GeneratedFile
	service                    *protogen.Service
	method                     *protogen.Method
	answerTypeIdent            protogen.GoIdent
	responseIdent              protogen.GoIdent
	backoffPolicyResponseIdent protogen.GoIdent
	opts                       *pb.RestMethodOptions
	withBody                   bool
	restPath                   *RestPath
	inputUniq                  *Uniq
	outputUniq                 *Uniq
	methodHookIdent            protogen.GoIdent
	linear                     map[protoreflect.FullName]*Uniq
	mapper                     map[protoreflect.FullName]map[protoreflect.FullName]*Uniq
	funcNameStorage            map[string]struct{}
}

func (m *RestMethod) Generate() {
	m.PrintTypeResponseAnswerIdentity()
	m.PrintBackOffFunction()
	m.PrintHooks()
	m.PrintFunction()
}

func (m *RestMethod) PrintTypeResponseAnswerIdentity() {
	m.g.P("type ", m.answerTypeIdent, " int32")
	m.g.P("const (")
	m.g.P(m.answerTypeIdent, "_OutOfScope ", m.answerTypeIdent, " = iota")
	for _, answer := range m.method.Output.Fields {
		m.g.P(m.answerTypeIdent, "_", answer.GoName)
	}
	m.g.P(")")
	m.g.P()
}

func (m *RestMethod) PrintBackOffFunction() {
	policy := m.opts.BackoffPolicy
	if policy == nil {
		return
	}

	m.g.P("type ", m.backoffPolicyResponseIdent, " struct {")
	m.g.P("Start ", timePackageTime)
	m.g.P("End ", timePackageTime)
	m.g.P("Request *", m.method.Input.GoIdent)
	m.g.P("Response *", m.responseIdent)
	m.g.P("Responses []*", m.responseIdent)
	m.g.P("Error error")
	m.g.P("Errors []error")
	m.g.P("Label string")
	m.g.P("}")
	m.g.P()

	m.g.P("func (s *rest", m.service.GoName, "Server) ", m.method.GoName, "_BackOff(")
	m.restPath.PrintAllArguments(m.g, true)
	m.g.P(") (")
	m.g.P("*", m.backoffPolicyResponseIdent, ",")
	m.g.P("error,")
	m.g.P(") {")
	m.g.P("label := ", cntxBegin, "(ctx, \"", m.service.GoName, "Server_", m.method.GoName, "_BackOff\")")
	m.g.P("defer ", cntxEnd, "(ctx, label)")
	m.g.P()
	m.g.P("result := &", m.backoffPolicyResponseIdent, "{")
	m.g.P("Start: ", timeNow, "(),")
	m.g.P("Request: request,")
	m.g.P("}")
	m.g.P("defer func() { result.End = ", timeNow, "() }()")
	m.g.P()

	if policy.GetMaxRunSecond() > 0 {
		m.g.P("ctx, cancel := context.WithTimeout(ctx, ", policy.GetMaxRunSecond(), "*", timePackageSecond, ")")
		m.g.P("defer cancel()")
		m.g.P()
	}

	m.g.P("for attempt := 0; attempt < ", policy.GetAttempt(), "; attempt++ {")
	m.g.P(ctxZapAddFields, "(ctx, ", zapTime, "(label+\"attempt::\"+", fmtSprint, "(attempt), ", timeNow, "()))")
	m.g.P("result.Response, result.Error = func () (")
	m.g.P("*", m.responseIdent, ",")
	m.g.P("error,")
	m.g.P(") {")
	if policy.GetRunSecond() > 0 {
		m.g.P("ctx, cancel := context.WithTimeout(ctx, ", policy.GetRunSecond(), "*", timePackageSecond, ")")
		m.g.P("defer cancel()")
		m.g.P()
	}
	m.g.P("return s.", m.method.GoName, "(")
	m.restPath.PrintAllArguments(m.g, false)
	m.g.P(")")
	m.g.P("}()")
	m.g.P()

	m.g.P("result.Responses = append(result.Responses, result.Response)")
	m.g.P("result.Errors = append(result.Errors, result.Error)")
	m.g.P()
	m.g.P("if result.Error == nil {")
	m.g.P("return result, nil")
	m.g.P("}")
	m.g.P()
	m.g.P("select {")
	m.g.P("case <-ctx.Done():")
	m.g.P("result.Error = ctx.Err()")
	m.g.P("return result, result.Error")
	m.g.P("case <-", timePackageAfter, "(", policy.GetTimeoutSec(), "*", timePackageSecond, "+", timePackageMillisecond, "):")
	m.g.P("}")
	m.g.P("}")
	m.g.P()

	m.g.P("return result, result.Error")
	m.g.P("}")
	m.g.P()
}

func (m *RestMethod) printHooksType() {
	m.g.P("type ", m.methodHookIdent, " struct {")
	m.g.P("ValidateArguments func(")
	m.restPath.PrintAllArguments(m.g, true)
	m.g.P("response *", m.responseIdent, ",")
	m.g.P("docData map[string]any,")
	m.g.P("hooksData map[string]any,")
	m.g.P(") error")
	m.g.P()

	m.g.P("MutatePath func(")
	m.restPath.PrintAllArguments(m.g, true)
	m.g.P("response *", m.responseIdent, ",")
	m.g.P("calculatedPath string,")
	m.g.P("docData map[string]any,")
	m.g.P("hooksData map[string]any,")
	m.g.P(") (mutatePath string, err error)")
	m.g.P()

	if m.withBody {
		m.g.P("CustomMarshal func(")
		m.restPath.PrintAllArguments(m.g, true)
		m.g.P("response *", m.responseIdent, ",")
		m.g.P("path string,")
		m.g.P("docData map[string]any,")
		m.g.P("hooksData map[string]any,")
		m.g.P(") (body []byte, contentType string, err error)")
		m.g.P()

		m.g.P("MutateBody func(")
		m.restPath.PrintAllArguments(m.g, true)
		m.g.P("response *", m.responseIdent, ",")
		m.g.P("body []byte,")
		m.g.P("docData map[string]any,")
		m.g.P("hooksData map[string]any,")
		m.g.P(") (mutatedBody []byte, err error)")
		m.g.P()
	}

	m.g.P("BeforeHttpRequest func(")
	m.restPath.PrintAllArguments(m.g, true)
	m.g.P("response *", m.responseIdent, ",")
	m.g.P("httpRequest *", httpPackageRequest, ",")
	m.g.P("docData map[string]any,")
	m.g.P("hooksData map[string]any,")
	m.g.P(") error")
	m.g.P()

	m.g.P("AfterHttpResponse func(")
	m.restPath.PrintAllArguments(m.g, true)
	m.g.P("httpRequest *", httpPackageRequest, ",")
	m.g.P("httpResponse *", httpPackageResponse, ",")
	m.g.P("response *", m.responseIdent, ",")
	m.g.P("docData map[string]any,")
	m.g.P("hooksData map[string]any,")
	m.g.P(")  (done bool, err error)")
	m.g.P()

	m.g.P("CustomUnmarshal func(")
	m.restPath.PrintAllArguments(m.g, true)
	m.g.P("response *", m.responseIdent, ",")
	m.g.P("rspBody []byte,")
	m.g.P("docData map[string]any,")
	m.g.P("hooksData map[string]any,")
	m.g.P(") error")
	m.g.P()

	m.g.P("}")
	m.g.P()
}

func (m *RestMethod) printPrepareHooksType() {
	m.g.P("func Prepare", m.methodHookIdent, "(")
	m.g.P("in *", m.methodHookIdent, ",")
	m.g.P(") *", m.methodHookIdent, " {")
	m.g.P("defaultHook := New", m.methodHookIdent, "()")
	m.g.P("if in == nil {")
	m.g.P("return defaultHook")
	m.g.P("}")
	m.g.P()
	m.g.P("if in.ValidateArguments == nil {")
	m.g.P("in.ValidateArguments = defaultHook.ValidateArguments")
	m.g.P("}")
	m.g.P()
	m.g.P("if in.MutatePath == nil {")
	m.g.P("in.MutatePath = defaultHook.MutatePath")
	m.g.P("}")
	if m.withBody {
		m.g.P("if in.MutateBody == nil {")
		m.g.P("in.MutateBody = defaultHook.MutateBody")
		m.g.P("}")
		m.g.P()
	}
	m.g.P()
	m.g.P("if in.BeforeHttpRequest == nil {")
	m.g.P("in.BeforeHttpRequest = defaultHook.BeforeHttpRequest")
	m.g.P("}")
	m.g.P()
	m.g.P("if in.AfterHttpResponse == nil {")
	m.g.P("in.AfterHttpResponse = defaultHook.AfterHttpResponse")
	m.g.P("}")
	m.g.P()

	m.g.P("return in")
	m.g.P("}")
	m.g.P()
}

func (m *RestMethod) printNewHooksType() {
	m.g.P("func New", m.methodHookIdent, "() *", m.methodHookIdent, " {")
	m.g.P("return &", m.methodHookIdent, "{")

	m.g.P("ValidateArguments: func(")
	m.restPath.PrintAllArguments(m.g, true)
	m.g.P("response *", m.responseIdent, ",")
	m.g.P("docData map[string]any,")
	m.g.P("hooksData map[string]any,")
	m.g.P(") error { return nil },")

	m.g.P("MutatePath: func(")
	m.restPath.PrintAllArguments(m.g, true)
	m.g.P("response *", m.responseIdent, ",")
	m.g.P("calculatedPath string,")
	m.g.P("docData map[string]any,")
	m.g.P("hooksData map[string]any,")
	m.g.P(") (mutatePath string, err error) { return calculatedPath, nil },")

	if m.withBody {
		m.g.P("MutateBody: func(")
		m.restPath.PrintAllArguments(m.g, true)
		m.g.P("response *", m.responseIdent, ",")
		m.g.P("body []byte,")
		m.g.P("docData map[string]any,")
		m.g.P("hooksData map[string]any,")
		m.g.P(") (mutatedBody []byte, err error) { return body, nil },")
	}

	m.g.P("BeforeHttpRequest: func(")
	m.restPath.PrintAllArguments(m.g, true)
	m.g.P("response *", m.responseIdent, ",")
	m.g.P("httpRequest *", httpPackageRequest, ",")
	m.g.P("docData map[string]any,")
	m.g.P("hooksData map[string]any,")
	m.g.P(") error { return nil },")

	m.g.P("AfterHttpResponse: func(")
	m.restPath.PrintAllArguments(m.g, true)
	m.g.P("httpRequest *", httpPackageRequest, ",")
	m.g.P("httpResponse *", httpPackageResponse, ",")
	m.g.P("response *", m.responseIdent, ",")
	m.g.P("docData map[string]any,")
	m.g.P("hooksData map[string]any,")
	m.g.P(")  (done bool, err error) { return false, nil },")

	m.g.P("}")
	m.g.P("}")
	m.g.P()
}

func (m *RestMethod) PrintHooks() {
	m.printHooksType()
	m.printNewHooksType()
	m.printPrepareHooksType()
}

func (m *RestMethod) PrintFunction() {
	m.g.P("type ", m.responseIdent, " struct {")
	m.g.P("Request *", m.method.Input.GoIdent)
	m.g.P("Response *", m.method.Output.GoIdent)
	m.g.P("ResponseOutput ", m.answerTypeIdent)
	m.g.P("Error error")
	m.g.P("Doc *", docPackageStruct)
	m.g.P("}")
	m.g.P()

	m.g.P("func (s *rest", m.service.GoName, "Server) ", m.method.GoName, "(")
	m.restPath.PrintAllArguments(m.g, true)
	m.g.P(") (")
	m.g.P("response *", m.responseIdent, ",")
	m.g.P("err error,")
	m.g.P(") {")
	m.g.P("label := ", cntxBegin, "(ctx, \"", m.service.GoName, "Server_", m.method.GoName, "\")")
	m.g.P("defer ", cntxEnd, "(ctx, label)")
	m.g.P()

	path := m.opts.GetPath()

	m.g.P("response = &", m.responseIdent, "{")
	m.g.P("Request: request,")
	m.g.P("Response: &", m.method.Output.GoIdent, "{},")
	m.g.P("ResponseOutput: ", m.answerTypeIdent, "_OutOfScope,")
	m.g.P("Error: nil,")
	m.g.P("Doc: &", docPackageStruct, "{")
	m.g.P("UUID: ", uuidPackageNew, "(),")
	m.g.P("CreatedAt: ", timeNow, "(),")
	m.g.P("CreatedAtNano: ", timeNow, "().UnixNano(),")
	m.g.P("FuncName: \"", m.service.GoName, "Server_", m.method.GoName, "\",")
	m.g.P("Label: label,")
	m.g.P("Logs:            make(map[string]any),")
	m.g.P("Arguments:       map[string]any{")
	if path != "" {
		m.g.P("\"clear_path\": \"", path, "\",")
		if path != m.restPath.pathFormat {
			m.g.P("\"path_format\": \"", m.restPath.pathFormat, "\",")
		}
	}
	for _, argument := range m.restPath.pathParams {
		m.g.P("\"", argument.Name, "\": ", argument.Name, ",")
	}
	for _, argument := range m.restPath.queryParams {
		m.g.P("\"", argument.Name, "\": ", argument.Name, ",")
	}
	for _, argument := range m.restPath.querySliceParams {
		m.g.P("\"", argument.Name, "\": ", argument.Name, ",")
	}
	m.g.P("},")
	m.g.P("Request: ", m.inputUniq.FuncNameGo, "(label, request),")
	m.g.P("RequestMethod: \"", m.opts.GetMethod().String(), "\",")

	m.g.P("RequestHeaders:  make(map[string][]string),")
	m.g.P("ResponseHeaders: make(map[string][]string),")
	m.g.P("HooksData:       make(map[string]any),")
	m.g.P("},")
	m.g.P("}")
	m.g.P("defer func () { s.docFunc(ctx, response.Doc) } ()")
	m.g.P()

	m.g.P("defer func() {")
	m.g.P("if err != nil {")
	m.g.P(ctxZapAddFields, "(ctx, ", zapAny, "(label+\"error\", err))")
	m.g.P("response.Error = err")
	m.g.P("response.Doc.Error = err")
	m.g.P()
	m.g.P("return")
	m.g.P("}")
	m.g.P()
	m.g.P(ctxZapAddFields, "(")
	m.g.P("ctx, ")
	m.g.P(m.outputUniq.FuncName, "(label+\"response.Response\", response.Response),")
	m.g.P(zapAny, "(label+\"response.ResponseOutput\", response.ResponseOutput),")
	m.g.P(")")
	m.g.P()
	m.g.P("response.Doc.Response = ", m.outputUniq.FuncNameGo, "(label, response.Response)")
	m.g.P("response.Doc.ResponseOutput = int32(response.ResponseOutput)")
	m.g.P("}()")
	m.g.P()

	m.g.P()
	m.g.P(ctxZapAddFields, "(")
	m.g.P("ctx, ")
	m.g.P(m.inputUniq.FuncName, "(label+\"response.Request\", response.Request),")
	if path != "" {
		m.g.P(zapAny, "(label+\"clear_path\", \"", path, "\"),")
		if path != m.restPath.pathFormat {
			m.g.P(zapAny, "(label+\"path_format\", \"", m.restPath.pathFormat, "\"),")
		}
	}
	for _, argument := range m.restPath.pathParams {
		m.g.P(zapAny, "(label+\"", argument.Name, "\", ", argument.Name, "),")
	}
	for _, argument := range m.restPath.queryParams {
		m.g.P(zapAny, "(label+\"", argument.Name, "\", ", argument.Name, "),")
	}
	for _, argument := range m.restPath.querySliceParams {
		m.g.P(zapAny, "(label+\"", argument.Name, "\", ", argument.Name, "),")
	}
	m.g.P(")")
	m.g.P()

	m.g.P("if err = s.hooks", m.method.GoName, ".ValidateArguments(")
	m.restPath.PrintAllArguments(m.g, false)
	m.g.P("response,")
	m.g.P("response.Doc.Logs,")
	m.g.P("response.Doc.HooksData,")
	m.g.P("); err != nil {")
	m.g.P("return")
	m.g.P("}")
	m.g.P()

	m.PrintPath(m.g, "s.baseUrl")
	m.g.P()

	if len(m.restPath.queryParams) > 0 || len(m.restPath.querySliceParams) > 0 {
		m.g.P("query := ", urlPackageValues, "{}")
		m.g.P()

		for _, param := range m.opts.GetQueryParam() {
			m.g.P("if query_param_", param, " != nil {")
			m.g.P("query.Set(\"", param, "\",", fmtSprint, "(query_param_", param, "))")
			m.g.P("}")
			m.g.P()
		}

		for _, param := range m.opts.GetQueryParamSlice() {
			m.g.P("for _, param := range query_slice_param_", param, " {")
			m.g.P("query.Add(\"", param, "\",", fmtSprint, "(param))")
			m.g.P("}")
			m.g.P()
		}

		m.g.P("path += \"?\"+query.Encode()")
		m.g.P()
	}

	m.g.P("response.Doc.RequestURI = path")
	m.g.P()
	m.g.P("if path, err = s.hooks", m.method.GoName, ".MutatePath(")
	m.restPath.PrintAllArguments(m.g, false)
	m.g.P("response,")
	m.g.P("path,")
	m.g.P("response.Doc.Logs,")
	m.g.P("response.Doc.HooksData,")
	m.g.P("); err != nil {")
	m.g.P("return")
	m.g.P("}")
	m.g.P()

	m.g.P("response.Doc.MutatedRequestURI = path")
	m.g.P()

	m.g.P("var contentType string")
	m.g.P()

	if m.withBody {
		m.g.P("var requestBody []byte")
		m.g.P("if s.hooks", m.method.GoName, ".CustomMarshal != nil {")
		m.g.P("if requestBody, contentType, err = s.hooks", m.method.GoName, ".CustomMarshal(")
		m.restPath.PrintAllArguments(m.g, false)
		m.g.P("response,")
		m.g.P("path,")
		m.g.P("response.Doc.Logs,")
		m.g.P("response.Doc.HooksData,")
		m.g.P("); err != nil {")
		m.g.P("return")
		m.g.P("}")
		m.g.P("} else {")

		field := m.inputUniq.Message.Fields[0]
		jsonMarshal := jsonPackageMarshal
		isList := field.Desc.IsList()
		kind := field.Desc.Kind()
		isMap := field.Desc.IsMap()
		//if kind == protoreflect.MessageKind && !(isMap || isList) {
		//	jsonMarshal = protojsonPackageMarshal
		//}

		marshal := m.opts.GetRequestMarshal()
		if ext := proto.GetExtension(field.Desc.Options(), pb.E_FieldMarshal); ext != nil {
			if fieldExt, ok := ext.(pb.RestMarshal); ok && fieldExt != pb.RestMarshal_NoneRestMarshal {
				marshal = fieldExt
			}
		}

		switch marshal {
		case pb.RestMarshal_Native:
			m.g.P("requestBody = []byte(", fmtSprint, "(request.Get", field.GoName, "()", "))")
			m.g.P()
		case pb.RestMarshal_JSON, pb.RestMarshal_XML:
			if marshal == pb.RestMarshal_XML {
				if kind != protoreflect.MessageKind && !isMap {
					panic("XML marshal only for message or map")
				}
			}

			if isMap && field.Desc.MapValue().Kind() == protoreflect.MessageKind {
				loggerMap := m.mapper[field.Desc.MapKey().FullName()][field.Desc.MapValue().FullName()].LoggerMap
				m.g.P("if requestBody, err = ", jsonMarshal, "(", marshallerPackageMap, "[", loggerMap.Key, ", *", loggerMap.Value, "](request.Get", field.GoName, "())); err != nil {")
			} else if isList && kind == protoreflect.MessageKind {
				m.g.P("if requestBody, err = ", jsonMarshal, "(", marshallerPackageSlice, "[*", field.Message.GoIdent, "](request.Get", field.GoName, "())); err != nil {")
			} else if kind == protoreflect.MessageKind {
				m.g.P("if requestBody, err = ", jsonMarshal, "(request.Get", field.GoName, "()); err != nil {")
			} else {
				m.g.P("if requestBody, err = ", jsonMarshal, "(request.Get", field.GoName, "()); err != nil {")
			}
			m.g.P("return")
			m.g.P("}")
			m.g.P()

			if marshal == pb.RestMarshal_XML {
				m.g.P("var mRequest ", mxjMap)
				m.g.P("if mRequest, err = ", mxjNewMapJson, "(requestBody); err != nil {")
				m.g.P("return")
				m.g.P("}")
				m.g.P()
				m.g.P("if requestBody, err = mRequest.Xml(); err != nil {")
				m.g.P("return")
				m.g.P("}")
				m.g.P()
				m.g.P("contentType = \"application/xml\"")
				m.g.P()
			} else {
				m.g.P("contentType = \"application/json\"")
				m.g.P()
			}
		case pb.RestMarshal_Protobuf:
			if kind != protoreflect.MessageKind {
				panic("protobuf marshal only for message")
			}
			m.g.P("if requestBody, err = ", protoPackageMarshal, "(request.Get", field.GoName, "()); err != nil {")
			m.g.P("return")
			m.g.P("}")
			m.g.P()
			m.g.P("contentType = \"application/protobuf\"")
		case pb.RestMarshal_FormData:
			m.g.P("buffer := &", bytesPackageBuffer, "{}")
			m.g.P("formData := ", multipartNewWriter, "(buffer)")
			m.g.P()

			if isMap {
				if field.Desc.MapKey().Kind() != protoreflect.StringKind {
					panic("FormData marshal: map key must be string")
				}

				if field.Desc.MapValue().Kind() != protoreflect.StringKind {
					panic("FormData marshal: map value must be string")
				}

				m.g.P("for key, value := range request.Get", field.GoName, "() {")
				m.g.P("if err = formData.WriteField(key, value); err != nil {")
				m.g.P("return")
				m.g.P("}")
				m.g.P("}")
			} else if isList {
				if kind != protoreflect.StringKind {
					panic("FormData marshal: list item must be string")
				}

				m.g.P("for index, item := range request.Get", field.GoName, "() {")
				m.g.P("if err = formData.WriteField(\"", field.GoName, "[\"+", fmtSprint, "(index)+\"]\", item); err != nil {")
				m.g.P("return")
				m.g.P("}")
				m.g.P("}")
			} else if kind == protoreflect.MessageKind {
				for _, messageField := range field.Message.Fields {
					if messageField.Desc.Kind() != protoreflect.StringKind {
						panic("FormData marshal: message field must be string")
					}

					m.g.P("if request.Get", field.GoName, "().", messageField.GoName, " != nil {")
					m.g.P("if err = formData.WriteField(\"", messageField.Desc.TextName(), "\", request.Get", field.GoName, "().Get", messageField.GoName, "()); err != nil {")
					m.g.P("return")
					m.g.P("}")
					m.g.P("}")
				}
			} else if kind == protoreflect.StringKind {
				m.g.P("if err = formData.WriteField(\"", field.GoName, "\", request.Get", field.GoName, "()); err != nil {")
				m.g.P("return")
				m.g.P("}")
			} else {
				panic("FormData marshal only for string, message[string fields], list[string], map[string, string]")
			}

			m.g.P()
			m.g.P("if err = formData.Close(); err != nil {")
			m.g.P("return")
			m.g.P("}")
			m.g.P()
			m.g.P("contentType = formData.FormDataContentType()")
			m.g.P("requestBody = buffer.Bytes()")
		case pb.RestMarshal_XWwwFormUrlencoded:
			m.g.P("requestForm := ", urlPackageValues, "{}")
			if isMap {
				if field.Desc.MapKey().Kind() != protoreflect.StringKind {
					panic("x-www-form-urlencoded marshal: map key must be string")
				}

				if field.Desc.MapValue().Kind() != protoreflect.StringKind {
					panic("x-www-form-urlencoded marshal: map value must be string")
				}

				m.g.P("for key, value := range request.Get", field.GoName, "() {")
				m.g.P("requestForm.Set(key, value)")
				m.g.P("}")
			} else if isList {
				if kind != protoreflect.StringKind {
					panic("x-www-form-urlencoded marshal: list value must be string")
				}

				m.g.P("for index, value := range request.Get", field.GoName, "() {")
				m.g.P("requestForm.Set(\"", field.GoName, "[\"+", fmtSprint, "(index)+\"]\", value)")
				m.g.P("}")
			} else if kind == protoreflect.MessageKind {
				for _, messageField := range field.Message.Fields {
					if messageField.Desc.Kind() != protoreflect.StringKind {
						panic("x-www-form-urlencoded marshal: message field must be string")
					}

					m.g.P("if request.Get", field.GoName, "().", messageField.GoName, " != nil {")
					m.g.P("requestForm.Set(\"", messageField.Desc.TextName(), "\", request.Get", field.GoName, "().Get", messageField.GoName, "())")
					m.g.P("}")
					m.g.P()
				}
			} else if kind == protoreflect.StringKind {
				m.g.P("requestForm.Set(\"", field.Desc.TextName(), "\", request.Get", field.GoName, "())")
			} else {
				panic("x-www-form-urlencoded marshal only for message[string fields], map[string, string], list[string] or string" + kind.String() + field.GoName)
			}

			m.g.P()

			m.g.P("contentType = \"application/x-www-form-urlencoded\"")
			m.g.P("requestBody = []byte(requestForm.Encode())")
		default:
			panic("unknown marshal type")
		}

		m.g.P("}")
		m.g.P()

		m.g.P("if requestBody, err = s.hooks", m.method.GoName, ".MutateBody(")
		m.restPath.PrintAllArguments(m.g, false)
		m.g.P("response,")
		m.g.P("requestBody,")
		m.g.P("response.Doc.Logs,")
		m.g.P("response.Doc.HooksData,")
		m.g.P("); err != nil {")
		m.g.P("return")
		m.g.P("}")
		m.g.P()

		m.g.P("response.Doc.RequestBody = requestBody")
		m.g.P()

		//m.g.P("var requestReader ", ioPackageReader, " = ", bytesPackageNewReader, "(requestBody)")
		m.g.P("requestReader := ", bytesPackageNewReader, "(requestBody)")
		m.g.P()
	}

	m.g.P("var httpRequest *", httpPackageRequest)
	m.g.P("if httpRequest, err = ", httpPackageNewRequestWithContext, "(")
	m.g.P("ctx,")
	m.g.P("http.Method", m.opts.GetMethod().String(), ",")
	m.g.P("path,")
	if m.withBody {
		m.g.P("requestReader,")
	} else {
		m.g.P("nil,")
	}
	m.g.P("); err != nil {")
	m.g.P("return")
	m.g.P("}")
	m.g.P()

	switch m.opts.GetAuthorization() {
	case pb.RestHttpAuthorization_Basic:
		m.g.P("httpRequest.SetBasicAuth(username, password)")
		m.g.P()
	case pb.RestHttpAuthorization_Bearer:
		m.g.P("httpRequest.Header.Add(\"Authorization\", \"Bearer \"+token)")
		m.g.P()
	}

	m.g.P("httpRequest.Header.Set(\"Content-Type\", contentType)")
	m.g.P()

	m.g.P("if err = s.hooks", m.method.GoName, ".BeforeHttpRequest(")
	m.restPath.PrintAllArguments(m.g, false)
	m.g.P("response,")
	m.g.P("httpRequest,")
	m.g.P("response.Doc.Logs,")
	m.g.P("response.Doc.HooksData,")
	m.g.P("); err != nil {")
	m.g.P("return")
	m.g.P("}")
	m.g.P()

	m.g.P("response.Doc.RequestHeaders = httpRequest.Header")
	m.g.P()

	m.g.P("var httpResponse *", httpPackageResponse)
	m.g.P("if httpResponse, err = http.DefaultClient.Do(httpRequest); err != nil {")
	m.g.P("response.Doc.ResponseError = err.Error()")
	m.g.P("return")
	m.g.P("}")
	m.g.P("defer httpResponse.Body.Close()")
	m.g.P()
	m.g.P("response.Doc.ResponseStatus = httpResponse.Status")
	m.g.P("response.Doc.ResponseHeaders = httpResponse.Header")
	m.g.P()

	m.g.P("var done bool")
	m.g.P("if done, err = s.hooks", m.method.GoName, ".AfterHttpResponse(")
	m.restPath.PrintAllArguments(m.g, false)
	m.g.P("httpRequest,")
	m.g.P("httpResponse,")
	m.g.P("response,")
	m.g.P("response.Doc.Logs,")
	m.g.P("response.Doc.HooksData,")
	m.g.P("); done || err != nil {")
	m.g.P("return")
	m.g.P("}")
	m.g.P()

	m.g.P("var rspBody []byte")
	m.g.P("if rspBody, err = ", ioPackageReadAll, "(httpResponse.Body); err != nil {")
	m.g.P("return")
	m.g.P("}")
	m.g.P()
	m.g.P("response.Doc.ResponseBody = rspBody")
	m.g.P()

	m.g.P("if s.hooks", m.method.GoName, ".CustomUnmarshal != nil {")
	m.g.P("err = s.hooks", m.method.GoName, ".CustomUnmarshal(")
	m.restPath.PrintAllArguments(m.g, false)
	m.g.P("response,")
	m.g.P("rspBody,")
	m.g.P("response.Doc.Logs,")
	m.g.P("response.Doc.HooksData,")
	m.g.P(")")
	m.g.P("return")
	m.g.P("}")
	m.g.P()

	if len(m.method.Output.Fields) == 0 {
		m.g.P("return")
		m.g.P("}")
		return
	}

	for index, field := range m.method.Output.Fields {
		unmarshal := m.opts.GetResponseUnmarshal()
		if ext := proto.GetExtension(field.Desc.Options(), pb.E_FieldUnmarshal); ext != nil {
			if fieldExt, ok := ext.(pb.RestMarshal); ok && fieldExt != pb.RestMarshal_NoneRestMarshal {
				unmarshal = fieldExt
			}
		}

		parent := field
		parentKind := parent.Desc.Kind()
		isMap := field.Desc.IsMap()
		isList := field.Desc.IsList()

		var mapKey string
		if isMap {
			switch parent.Message.Fields[0].Desc.Kind() {
			case protoreflect.Int32Kind, protoreflect.Sint32Kind, protoreflect.Sfixed32Kind:
				mapKey = "int32"
			case protoreflect.Uint32Kind, protoreflect.Fixed32Kind:
				mapKey = "uint32"
			case protoreflect.Int64Kind, protoreflect.Sint64Kind, protoreflect.Sfixed64Kind:
				mapKey = "int64"
			case protoreflect.Uint64Kind, protoreflect.Fixed64Kind:
				mapKey = "uint64"
			case protoreflect.BoolKind:
				mapKey = "bool"
			case protoreflect.StringKind:
				mapKey = "string"
			default:
				panic("map key can not be float, double, bytes, message, or enum types")
			}

			field = field.Message.Fields[1]
		}

		kind := field.Desc.Kind()
		typeName, ok := GetBuiltinTypeName(m.file, kind)
		if !ok {
			switch kind {
			case protoreflect.MessageKind:
				typeName = field.Message.GoIdent
			case protoreflect.EnumKind:
				typeName = field.Enum.GoIdent
			}
		}

		switch unmarshal {
		case pb.RestMarshal_Native:
			if isList {
				panic("native unmarshal can not be list")
			}

			if isMap {
				panic("native unmarshal can not be map")
			}

			if parentKind == protoreflect.MessageKind {
				panic("native unmarshal can not be message")
			}

			if parentKind == protoreflect.GroupKind {
				panic("native unmarshal can not be message")
			}

			switch parentKind {
			case protoreflect.EnumKind, protoreflect.Int32Kind, protoreflect.Sint32Kind, protoreflect.Sfixed32Kind,
				protoreflect.Int64Kind, protoreflect.Sint64Kind, protoreflect.Sfixed64Kind:
				m.g.P("if response.Response.", field.GoName, ", err = ", wrapsParseIntPtr, "[", typeName, "](string(rspBody));err == nil {")
			case protoreflect.Uint32Kind, protoreflect.Fixed32Kind,
				protoreflect.Uint64Kind, protoreflect.Fixed64Kind:
				m.g.P("if response.Response.", field.GoName, ", err = ", wrapsParseUintPtr, "[", typeName, "](string(rspBody));err == nil {")
			case protoreflect.FloatKind, protoreflect.DoubleKind:
				m.g.P("if response.Response.", field.GoName, ", err = ", wrapsParseFloatPtr, "[", typeName, "](string(rspBody));err == nil {")
			case protoreflect.StringKind:
				m.g.P("if response.Response.", field.GoName, ", err = ", wrapsParseStringPtr, "[", typeName, "](string(rspBody));err == nil {")
			case protoreflect.BoolKind:
				m.g.P("if response.Response.", field.GoName, ", err = ", wrapsParseBoolPtr, "[", typeName, "](string(rspBody));err == nil {")
			case protoreflect.BytesKind:
				m.g.P("if response.Response.", field.GoName, ", err = ", wrapsParseBytes, "[", typeName, "](string(rspBody));err == nil {")
			}
		case pb.RestMarshal_JSON:
			var prefix string
			if kind == protoreflect.MessageKind {
				prefix = "*"
			}

			if isMap {
				m.g.P("if response.Response.", parent.GoName, ", err = ", wrapsUnmarshalJSON, "[map[", mapKey, "]", prefix, typeName, "](rspBody); err == nil {")
			} else if isList {
				m.g.P("if response.Response.", parent.GoName, ", err = ", wrapsUnmarshalJSON, "[[]", prefix, typeName, "](rspBody); err == nil {")
			} else {
				if parentKind == protoreflect.BytesKind {
					m.g.P("if response.Response.", parent.GoName, ", err = ", wrapsUnmarshalJSON, "[", prefix, typeName, "](rspBody); err == nil {")
				} else {
					m.g.P("if response.Response.", parent.GoName, ", err = ", wrapsUnmarshalJSONPtr, "[", typeName, "](rspBody); err == nil {")
				}
			}
		case pb.RestMarshal_Protobuf:
			if kind != protoreflect.MessageKind {
				panic("protobuf only marshal for message")
			}

			if isMap {
				panic("protobuf can not marshal for map")
			}

			if isList {
				panic("protobuf can not marshal for list")
			}

			m.g.P("response.Response.", parent.GoName, " = &", parent.Message.GoIdent, "{}")
			m.g.P("if err = ", protoPackageUnmarshal, "(rspBody, response.Response.", parent.GoName, "); err == nil {")
		case pb.RestMarshal_XML:
			if isList || (kind != protoreflect.MessageKind && !isMap) {
				panic("XML marshal only for message or map")
			}

			if isMap && ok {
				m.g.P("if response.Response.", parent.GoName, ", err = ", marshallerPackageXMLUnmarshal, "[map[", mapKey, "]", typeName, "](rspBody); err == nil {")
			} else if isMap && kind == protoreflect.EnumKind {
				m.g.P("if response.Response.", parent.GoName, ", err = ", marshallerPackageXMLUnmarshal, "[map[", mapKey, "]", typeName, "](rspBody); err == nil {")
			} else if isMap {
				m.g.P("if response.Response.", parent.GoName, ", err = ", marshallerPackageXMLUnmarshal, "[map[", mapKey, "]*", typeName, "](rspBody); err == nil {")
			} else if isList && ok {
				m.g.P("if response.Response.", parent.GoName, ", err = ", marshallerPackageXMLUnmarshal, "[[]", typeName, "](rspBody); err == nil {")
			} else if isList && kind == protoreflect.EnumKind {
				m.g.P("if response.Response.", parent.GoName, ", err = ", marshallerPackageXMLUnmarshal, "[[]", typeName, "](rspBody); err == nil {")
			} else if isList {
				m.g.P("if response.Response.", parent.GoName, ", err = ", marshallerPackageXMLUnmarshal, "[[]*", typeName, "](rspBody); err == nil {")
			} else {
				if field.Desc.Kind() == protoreflect.BytesKind {
					m.g.P("if response.Response.", parent.GoName, ", err = ", marshallerPackageXMLUnmarshal, "[", typeName, "](rspBody); err == nil {")
				} else {
					m.g.P("if response.Response.", parent.GoName, ", err = ", marshallerPackageXMLUnmarshalPtr, "[", typeName, "](rspBody); err == nil {")
				}
			}
		case pb.RestMarshal_FormData:
			if isList {
				panic("for formdata unmarshal u need use message with string fields or map[string]string")
			}

			if isMap {
				if parent.Desc.MapKey().Kind() != protoreflect.StringKind {
					panic("for formdata unmarshal u need use message with string fields or map[string]string" + parentKind.String() + m.method.GoName)
				}

				if kind != protoreflect.StringKind {
					panic("for formdata unmarshal u need use message with string fields or map[string]string")
				}

				m.g.P("if response.Response.", parent.GoName, ", err = ", formPackageMap, "(")
				m.g.P("httpResponse.Header,")
				m.g.P("rspBody,")
				m.g.P("); err == nil {")
			} else {
				if kind != protoreflect.MessageKind {
					panic("for formdata unmarshal u need use message with string fields fields or map[string]string")
				}

				m.g.P("if response.Response.", field.GoName, ", err = func() (*", field.Message.GoIdent, ", error){")
				m.g.P("m, err := ", formPackageMap, "(")
				m.g.P("httpResponse.Header,")
				m.g.P("rspBody,")
				m.g.P(")")
				m.g.P("if err != nil {")
				m.g.P("return nil, err")
				m.g.P("}")
				m.g.P()
				m.g.P("result := &", field.Message.GoIdent, "{}")
				for _, msgField := range field.Message.Fields {
					if msgField.Desc.Kind() != protoreflect.StringKind {
						panic("for formdata unmarshal u need use message with string fields or map[string]string")
					}

					m.g.P("result.", msgField.GoName, ", _ = ", formPackageParseStringPtr, "(m, \"", msgField.GoName, "\")")
				}

				m.g.P("return result, nil")
				m.g.P("}(); err == nil {")
			}

		case pb.RestMarshal_XWwwFormUrlencoded:
			if isMap {
				if kind == protoreflect.MessageKind {
					panic("for xwwwformurlencoded unmarshal u need use message with string fields or map[string]NOT_MESSAGE_TYPE")
				}

				m.g.P("if response.Response.", parent.GoName, ", err = ", valuesParse, "(")
				m.g.P("rspBody,")
				switch kind {
				case protoreflect.EnumKind, protoreflect.Int32Kind, protoreflect.Sint32Kind, protoreflect.Sfixed32Kind,
					protoreflect.Int64Kind, protoreflect.Sint64Kind, protoreflect.Sfixed64Kind:
					m.g.P(valuesInt, "[", typeName, "],")
				case protoreflect.Uint32Kind, protoreflect.Fixed32Kind,
					protoreflect.Uint64Kind, protoreflect.Fixed64Kind:
					m.g.P(valuesUint, "[", typeName, "],")
				case protoreflect.FloatKind, protoreflect.DoubleKind:
					m.g.P(valuesFloat, "[", typeName, "],")
				case protoreflect.StringKind:
					m.g.P(valuesString, "[", typeName, "],")
				case protoreflect.BoolKind:
					m.g.P(valuesBool, "[", typeName, "],")
				case protoreflect.BytesKind:
					m.g.P(valuesBytes, "[", typeName, "],")
				case protoreflect.GroupKind:
					panic("native marshal for primitive")
				}
				m.g.P("); err == nil {")
			} else {
				if isList {
					panic("for XWwwFormUrlencoded unmarshal u need use message with NOT_MESSAGE_TYPE fields or map[string]NOT_MESSAGE_TYPE")
				}

				if field.Desc.Kind() != protoreflect.MessageKind {
					panic("for formdata unmarshal u need use message with NOT_MESSAGE_TYPE fields or map[string]NOT_MESSAGE_TYPE")
				}

				m.g.P("if structGen := ", valuesNew, "(rspBody); structGen.Error() == nil {")
				m.g.P("err = nil")
				m.g.P("response.Response.", field.GoName, " = &", field.Message.GoIdent, "{")
				for _, msgField := range field.Message.Fields {
					if msgField.Desc.Kind() == protoreflect.MessageKind || msgField.Desc.Kind() == protoreflect.GroupKind {
						panic("for formdata unmarshal u need use message with NOT_MESSAGE_TYPE fields or map[string]NOT_MESSAGE_TYPE")
					}

					mgdFieldIdent, ok := GetBuiltinTypeName(m.file, msgField.Desc.Kind())
					if !ok {
						mgdFieldIdent = msgField.Enum.GoIdent
					}

					switch msgField.Desc.Kind() {
					case protoreflect.EnumKind, protoreflect.Int32Kind, protoreflect.Sint32Kind, protoreflect.Sfixed32Kind,
						protoreflect.Int64Kind, protoreflect.Sint64Kind, protoreflect.Sfixed64Kind:
						m.g.P(msgField.GoName, ": ", valuesIntStruct, "[", mgdFieldIdent, "](structGen, \"", msgField.GoName, "\"),")
					case protoreflect.Uint32Kind, protoreflect.Fixed32Kind,
						protoreflect.Uint64Kind, protoreflect.Fixed64Kind:
						m.g.P(msgField.GoName, ": ", valuesUintStruct, "[", mgdFieldIdent, "](structGen, \"", msgField.GoName, "\"),")
					case protoreflect.FloatKind, protoreflect.DoubleKind:
						m.g.P(msgField.GoName, ": ", valuesFloatStruct, "[", mgdFieldIdent, "](structGen, \"", msgField.GoName, "\"),")
					case protoreflect.StringKind:
						m.g.P(msgField.GoName, ": ", valuesStringStruct, "[", mgdFieldIdent, "](structGen, \"", msgField.GoName, "\"),")
					case protoreflect.BoolKind:
						m.g.P(msgField.GoName, ": ", valuesBoolStruct, "[", mgdFieldIdent, "](structGen, \"", msgField.GoName, "\"),")
					case protoreflect.BytesKind:
						m.g.P(msgField.GoName, ": ", valuesBytesStruct, "[", mgdFieldIdent, "](structGen, \"", msgField.GoName, "\"),")
					case protoreflect.GroupKind:
						panic("native marshal for primitive")
					}
				}
				m.g.P("}")
			}
		default:
			panic("unknown marshal")
		}
		m.g.P("response.ResponseOutput = ", m.answerTypeIdent, "_", parent.GoName)
		m.g.P("return")
		m.g.P("}")

		if index != len(m.method.Output.Fields)-1 {
			m.g.P()
		}
	}

	m.g.P("return")
	m.g.P("}")
	m.g.P()
}

func (m *RestMethod) PrintPath(
	g *protogen.GeneratedFile,
	baseUrl string,
) {
	if len(m.restPath.formatArg) > 0 {
		g.P("path := ", baseUrl, " + ", fmtSprintf, "(")
		g.P("\"", m.restPath.pathFormat, "\",")
		for _, arg := range m.restPath.formatArg {
			g.P(arg.Name, ",")
		}
		g.P(")")
	} else if path := m.opts.GetPath(); path != "" {
		g.P("path := ", baseUrl, "+ \""+path+"\"")
	} else {
		if baseUrl == "\"\"" {
			baseUrl = "\"/\""
		}

		g.P("path := ", baseUrl)
	}
}

var standNameNumber = 1

func StandName(prefix string) string {
	standNameNumber++
	if len(prefix) > 0 {
		if prefix[len(prefix)-1] != '_' {
			prefix += "_"
		}
	}

	return fmt.Sprintf("\"%stesting_stand_%d\"", prefix, standNameNumber)
}

type KindType int

const (
	Int32Kind KindType = iota + 1
	Uint32Kind
	Int64Kind
	Uint64Kind
	BoolKind
	StringKind
	MessageKind
)

func (m *RestMethod) GenerateTests() {
	m.gTest.P("func Test_", m.service.GoName, "_", m.method.GoName, "(t *", testingPackageT, ") {")
	var (
		messageValue  proto.Message
		responseValue proto.Message

		inputMarshal = m.opts.GetRequestMarshal()
	)

	if m.withBody {
		if ext := proto.GetExtension(m.method.Input.Fields[0].Desc.Options(), pb.E_FieldMarshal); ext != nil {
			if fieldExt, ok := ext.(pb.RestMarshal); ok && fieldExt != pb.RestMarshal_NoneRestMarshal {
				inputMarshal = fieldExt
			}
		}

		{
			messageValue = NewProtoMessageValue(m.method.Input)
			PrintProtoMessage(
				"request := ",
				m.gTest,
				messageValue,
				m.method.Input,
				m.mapper,
				true,
			)

			protoGenField := m.method.Input.Fields[0]
			field := messageValue.ProtoReflect().Get(protoGenField.Desc)
			fieldInterface := field.Interface()
			switch inputMarshal {
			case pb.RestMarshal_Native:
				m.gTest.P("requestBody := []byte(", strconv.Quote(fmt.Sprint(fieldInterface)), ")")
			case pb.RestMarshal_JSON:
				protoJ, err := protojson.MarshalOptions{
					UseProtoNames: true,
				}.Marshal(messageValue)
				if err != nil {
					panic(err)
				}

				var mJ map[string]any
				if err = json.Unmarshal(protoJ, &mJ); err != nil {
					panic(err)
				}

				requestBody, err := json.Marshal(mJ[protoGenField.Desc.JSONName()])
				if err != nil {
					panic(err)
				}

				m.gTest.P("requestBody := []byte(", strconv.Quote(string(requestBody)), ")")
			case pb.RestMarshal_Protobuf:
				requestBody, err := proto.Marshal(fieldInterface.(proto.Message))
				if err != nil {
					panic(err)
				}

				m.gTest.P("requestBody := []byte{")
				for _, prettyByte := range PrettyBytes(requestBody) {
					m.gTest.P(prettyByte)
				}
				m.gTest.P("}")
			case pb.RestMarshal_XML:
				protoJsonBody, err := protojson.MarshalOptions{
					UseProtoNames: true,
				}.Marshal(fieldInterface.(proto.Message))
				if err != nil {
					panic(err)
				}

				newMapJson, err := mxj.NewMapJson(protoJsonBody)
				if err != nil {
					panic(err)
				}

				requestBody, err := newMapJson.XmlIndent("", "  ")
				if err != nil {
					panic(err)
				}
				m.gTest.P("requestBody := []byte(", strconv.Quote(string(requestBody)), ")")
			case pb.RestMarshal_FormData:

			case pb.RestMarshal_NoneRestMarshal:
				m.gTest.P("requestBody := []byte{}")
			}
			m.gTest.P()
		}
	} else {
		m.gTest.P("request := &", m.method.Input.GoIdent, "{}")
		m.gTest.P()
	}

	if len(m.method.Output.Fields) > 0 {
		responseValue = NewProtoMessageValue(m.method.Output)
		PrintProtoMessage(
			"response := ",
			m.gTest,
			responseValue,
			m.method.Output,
			m.mapper,
			true,
		)
		m.gTest.P("_ = response")
		m.gTest.P()
	}

	m.gTest.P("// define rest arguments")
	m.gTest.P("ctx := ", contextContextTODO, "()")

	for _, param := range m.restPath.authParams {
		m.gTest.P(param.Name, " := ", NewString())
	}

	pathParams := map[string]string{}
	for _, param := range m.restPath.pathParams {
		s := NewString()
		m.gTest.P(param.Name, " := ", s)
		pathParams[param.Name], _ = strconv.Unquote(s)
	}

	if len(m.restPath.queryParams) > 0 || len(m.restPath.querySliceParams) > 0 {
		m.gTest.P("queryParams := make(", urlPackageValues, ")")
	}

	for _, param := range m.restPath.queryParams {
		value := NewAny()
		m.gTest.P(param.Name, " := ", value)
		m.gTest.P("queryParams.Add(\"", param.ClearName, "\", ", fmtSprint, "(", value, "))")
	}

	for _, param := range m.restPath.querySliceParams {
		m.gTest.P(param.Name, ":= []any{")
		value1 := NewAny()
		value2 := NewAny()
		value3 := NewAny()
		m.gTest.P(value1, ",")
		m.gTest.P(value2, ",")
		m.gTest.P(value3, ",")
		m.gTest.P("}")
		m.gTest.P("queryParams.Add(\"", param.ClearName, "\", ", fmtSprint, "(", value1, "))")
		m.gTest.P("queryParams.Add(\"", param.ClearName, "\", ", fmtSprint, "(", value2, "))")
		m.gTest.P("queryParams.Add(\"", param.ClearName, "\", ", fmtSprint, "(", value3, "))")
	}

	m.gTest.P()

	standName := StandName("argument_and_request_checks")
	m.gTest.P("t.Run(", standName, ", func(t *testing.T) {")
	m.gTest.P("// define test server")
	m.gTest.P("var testFunc func(w ", httpPackageResponseWriter, ", r *", httpPackageRequest, ")")
	m.gTest.P("server := ", httptestPackageNewServer, "(")
	m.gTest.P(httpPackageHandlerFunc, "(")
	m.gTest.P("func(w ", httpPackageResponseWriter, ", r *", httpPackageRequest, ") {")
	m.gTest.P("testFunc(w, r)")
	m.gTest.P("},")
	m.gTest.P("),")
	m.gTest.P(")")
	m.gTest.P("defer server.Close()")
	m.gTest.P()
	m.gTest.P("rest", m.service.GoName, "BaseURL[", standName, "] = server.URL")
	m.gTest.P()
	m.gTest.P("testFunc = func(w ", httpPackageResponseWriter, ", r *", httpPackageRequest, "){")
	switch m.opts.GetAuthorization() {
	case pb.RestHttpAuthorization_Basic:
		m.gTest.P("requestUserName, requestPassword, ok := r.BasicAuth()")
		m.gTest.P(requireTrue, "(t, ok)")
		m.gTest.P(requireEqual, "(t, username, requestUserName)")
		m.gTest.P(requireEqual, "(t, password, requestPassword)")
		m.gTest.P()
	case pb.RestHttpAuthorization_Bearer:
		m.gTest.P("authorizationHeader := r.Header.Get(\"Authorization\")")
		m.gTest.P(requireEqual, "(t, \"Bearer \"+ token, authorizationHeader)")
		m.gTest.P()
	}
	if len(m.restPath.queryParams) > 0 || len(m.restPath.querySliceParams) > 0 {
		m.gTest.P(requireEqual, "(t, queryParams, r.URL.Query())")
		m.gTest.P()
	}
	var args []any
	for _, arg := range m.restPath.formatArg {
		args = append(args, pathParams[arg.Name])
	}

	path := fmt.Sprintf(m.restPath.pathFormat, args...)
	if path == "" {
		path = "/"
	}
	m.gTest.P(requireEqual, "(t, \"", path, "\", r.URL.Path)")
	m.gTest.P(requireEqual, "(t, ", httpPackageMethod, m.opts.GetMethod(), ", r.Method)")
	if m.withBody {
		m.gTest.P()
		protoGenField := m.method.Input.Fields[0]
		field := messageValue.ProtoReflect().Get(protoGenField.Desc)
		fieldInterface := field.Interface()

		switch inputMarshal {
		case pb.RestMarshal_NoneRestMarshal, pb.RestMarshal_Native,
			pb.RestMarshal_JSON, pb.RestMarshal_Protobuf, pb.RestMarshal_XML:
			m.gTest.P("body, err := ", ioPackageReadAll, "(r.Body)")
			m.gTest.P(requireNoError, "(t, err)")
			m.gTest.P(requireEqual, "(t, requestBody, body)")
		case pb.RestMarshal_FormData:
			m.gTest.P("multipartReader, err := r.MultipartReader()")
			m.gTest.P(requireNoError, "(t, err)")
			m.gTest.P(requireNotNil, "(t, multipartReader)")
			m.gTest.P()
			m.gTest.P("form, err := multipartReader.ReadForm(", mathPackageMaxInt64, ")")
			m.gTest.P(requireNoError, "(t, err)")
			m.gTest.P(requireNotNil, "(t, form)")
			m.gTest.P()

			if protoGenField.Desc.IsMap() {

				var keys []protoreflect.MapKey
				field.Map().Range(func(
					key protoreflect.MapKey,
					value protoreflect.Value,
				) bool {
					keys = append(keys, key)
					return true
				})

				sort.Slice(keys, func(i, j int) bool {
					return keys[i].String() < keys[j].String()
				})

				for _, key := range keys {
					value := field.Map().Get(key)
					m.gTest.P(requireEqual, "(t, []string{\"", value.String(), "\"}, form.Value[\"", key.Interface(), "\"])")
				}
			} else if protoGenField.Desc.IsList() {
				for i := 0; i < field.List().Len(); i++ {
					m.gTest.P(requireEqual, "(t, []string{\"", field.List().Get(i).String(), "\"}, form.Value[\"", protoGenField.GoName+fmt.Sprintf("[%v]", i), "\"])")
				}
			} else if protoGenField.Desc.Kind() == protoreflect.MessageKind {
				fieldInterface.(proto.Message).ProtoReflect().Range(func(
					descriptor protoreflect.FieldDescriptor,
					value protoreflect.Value,
				) bool {
					m.gTest.P(requireEqual, "(t, []string{\"", value.String(), "\"}, form.Value[\"", protoGenField.Message.Fields[descriptor.Index()].Desc.TextName(), "\"])")
					return true
				})
			}
		case pb.RestMarshal_XWwwFormUrlencoded:
			m.gTest.P("require.NoError(t, r.ParseForm())")
			m.gTest.P()
			if protoGenField.Desc.IsMap() {
				var keys []protoreflect.MapKey
				field.Map().Range(func(
					key protoreflect.MapKey,
					value protoreflect.Value,
				) bool {
					keys = append(keys, key)
					return true
				})

				sort.Slice(keys, func(i, j int) bool {
					return keys[i].String() < keys[j].String()
				})

				for _, key := range keys {
					value := field.Map().Get(key)
					m.gTest.P(requireEqual, "(t, \"", value.String(), "\", r.PostForm.Get(\"", key.Interface(), "\"))")
				}
			} else if protoGenField.Desc.IsList() {
				for i := 0; i < field.List().Len(); i++ {
					m.gTest.P(requireEqual, "(t, \"", field.List().Get(i).String(), "\", r.PostForm.Get(\"", protoGenField.Desc.TextName()+fmt.Sprintf("[%v]", i), "\"))")
				}
			} else if protoGenField.Desc.Kind() == protoreflect.MessageKind {
				fieldInterface.(proto.Message).ProtoReflect().Range(func(
					descriptor protoreflect.FieldDescriptor,
					value protoreflect.Value,
				) bool {
					m.gTest.P(requireEqual, "(t, \"", value.String(), "\", r.PostForm.Get(\"", protoGenField.Message.Fields[descriptor.Index()].Desc.TextName(), "\"))")
					return true
				})
			} else if protoGenField.Desc.Kind() == protoreflect.StringKind {
				m.gTest.P(requireEqual, "(t, \"", field.String(), "\", r.PostForm.Get(\"", protoGenField.Desc.TextName(), "\"))")
			}
		}
	}
	m.gTest.P("}")
	m.gTest.P("")

	m.gTest.P("// define new rest client")
	m.gTest.P("testingRest := NewRest", m.service.GoName, "Server(", standName, ", nil)")
	m.gTest.P("testingRest.", m.method.GoName, "(")
	m.restPath.PrintAllArguments(m.gTest, false)
	m.gTest.P(")")

	m.gTest.P("")
	m.gTest.P("})")

	var (
		skipAll string
		skipXML string

		nativeNumber string
		nativeFloat  string
		nativeBool   string

		jsonMap         = map[KindType]map[KindType]string{}
		jsonNumberSlice string

		jsonFloatSlice string

		jsonBoolSlice string

		jsonStringSlice string
	)

	for _, field := range m.method.Output.Fields {
		outputMarshal := m.opts.GetResponseUnmarshal()
		if ext := proto.GetExtension(field.Desc.Options(), pb.E_FieldUnmarshal); ext != nil {
			if fieldExt, ok := ext.(pb.RestMarshal); ok && fieldExt != pb.RestMarshal_NoneRestMarshal {
				outputMarshal = fieldExt
			}
		}

		m.gTest.P()
		if skipAll != "" {
			m.gTest.P("// Skip ", field.GoName, " logic used at ", skipAll, "(", outputMarshal.String(), ")", "(", outputMarshal.String(), ")")
			continue
		}

		unmarshalStandName := StandName("check_field_parse_" + field.GoName)
		kind := field.Desc.Kind()
		switch outputMarshal {
		case pb.RestMarshal_Native:
			switch kind {
			case protoreflect.EnumKind,
				protoreflect.Int32Kind, protoreflect.Sint32Kind, protoreflect.Sfixed32Kind,
				protoreflect.Int64Kind, protoreflect.Sint64Kind, protoreflect.Sfixed64Kind,
				protoreflect.Uint32Kind, protoreflect.Fixed32Kind,
				protoreflect.Uint64Kind, protoreflect.Fixed64Kind:
				if nativeNumber != "" {
					m.gTest.P("// Skip ", field.GoName, " logic used at ", nativeNumber, "(", outputMarshal.String(), ")")
					continue
				}

				nativeNumber = "response." + field.GoName
			case protoreflect.FloatKind, protoreflect.DoubleKind:
				if nativeFloat != "" {
					m.gTest.P("// Skip ", field.GoName, " logic used at ", nativeFloat, "(", outputMarshal.String(), ")")
					continue
				}

				nativeFloat = "response." + field.GoName
			case protoreflect.BoolKind:
				if nativeBool != "" {
					m.gTest.P("// Skip ", field.GoName, " logic used at ", nativeBool, "(", outputMarshal.String(), ")")
					continue
				}

				nativeBool = "response." + field.GoName
			case protoreflect.StringKind:
				skipAll = "response." + field.GoName
			case protoreflect.BytesKind:
				skipAll = "response." + field.GoName
			}
		case pb.RestMarshal_JSON:
			if field.Desc.IsMap() {
				var mapKey KindType
				switch field.Desc.MapKey().Kind() {
				case protoreflect.Int32Kind, protoreflect.Sint32Kind, protoreflect.Sfixed32Kind:
					mapKey = Int32Kind
				case protoreflect.Uint32Kind, protoreflect.Fixed32Kind:
					mapKey = Uint32Kind
				case protoreflect.Int64Kind, protoreflect.Sint64Kind, protoreflect.Sfixed64Kind:
					mapKey = Int64Kind
				case protoreflect.Uint64Kind, protoreflect.Fixed64Kind:
					mapKey = Uint64Kind
				case protoreflect.BoolKind:
					mapKey = BoolKind
				case protoreflect.StringKind:
					mapKey = StringKind
				default:
					panic("map key can not be float, double, bytes, message, or enum types")
				}

				var valueKey KindType
				switch field.Desc.MapKey().Kind() {
				case protoreflect.Int32Kind, protoreflect.Sint32Kind, protoreflect.Sfixed32Kind:
					valueKey = Int32Kind
				case protoreflect.Uint32Kind, protoreflect.Fixed32Kind:
					valueKey = Uint32Kind
				case protoreflect.Int64Kind, protoreflect.Sint64Kind, protoreflect.Sfixed64Kind:
					valueKey = Int64Kind
				case protoreflect.Uint64Kind, protoreflect.Fixed64Kind:
					valueKey = Uint64Kind
				case protoreflect.BoolKind:
					valueKey = BoolKind
				case protoreflect.StringKind:
					valueKey = StringKind
				default:
					valueKey = MessageKind
				}

				if _, ok := jsonMap[mapKey]; !ok {
					jsonMap[mapKey] = map[KindType]string{}
				}

				if _, ok := jsonMap[mapKey][valueKey]; ok {
					m.gTest.P("// Skip ", field.GoName, " logic used at ", jsonMap[mapKey][valueKey], "(", outputMarshal.String(), ")")
					continue
				}

				jsonMap[mapKey][valueKey] = "response." + field.GoName
			} else if field.Desc.IsList() {
				switch kind {
				case protoreflect.EnumKind,
					protoreflect.Int32Kind, protoreflect.Sint32Kind, protoreflect.Sfixed32Kind,
					protoreflect.Int64Kind, protoreflect.Sint64Kind, protoreflect.Sfixed64Kind,
					protoreflect.Uint32Kind, protoreflect.Fixed32Kind,
					protoreflect.Uint64Kind, protoreflect.Fixed64Kind:
					if jsonNumberSlice != "" {
						m.gTest.P("// Skip ", field.GoName, " logic used at ", jsonNumberSlice, "(", outputMarshal.String(), ")")
						continue
					}

					jsonNumberSlice = "response." + field.GoName
				case protoreflect.FloatKind, protoreflect.DoubleKind:
					if jsonFloatSlice != "" {
						m.gTest.P("// Skip ", field.GoName, " logic used at ", jsonFloatSlice, "(", outputMarshal.String(), ")")
						continue
					}

					jsonFloatSlice = "response." + field.GoName
				case protoreflect.BoolKind:
					if jsonBoolSlice != "" {
						m.gTest.P("// Skip ", field.GoName, " logic used at ", jsonBoolSlice, "(", outputMarshal.String(), ")")
						continue
					}

					jsonBoolSlice = "response." + field.GoName
				case protoreflect.StringKind:
					if jsonStringSlice != "" {
						m.gTest.P("// Skip ", field.GoName, " logic used at ", jsonStringSlice, "(", outputMarshal.String(), ")")
						continue
					}

					jsonStringSlice = "response." + field.GoName
				case protoreflect.BytesKind:
					if jsonStringSlice != "" {
						m.gTest.P("// Skip ", field.GoName, " logic used at ", jsonStringSlice, "(", outputMarshal.String(), ")")
						continue
					}

					jsonStringSlice = "response." + field.GoName
				}
			} else {
				switch kind {
				case protoreflect.EnumKind,
					protoreflect.Int32Kind, protoreflect.Sint32Kind, protoreflect.Sfixed32Kind,
					protoreflect.Int64Kind, protoreflect.Sint64Kind, protoreflect.Sfixed64Kind,
					protoreflect.Uint32Kind, protoreflect.Fixed32Kind,
					protoreflect.Uint64Kind, protoreflect.Fixed64Kind:
					if nativeNumber != "" {
						m.gTest.P("// Skip ", field.GoName, " logic used at ", nativeNumber, "(", outputMarshal.String(), ")")
						continue
					}

					nativeNumber = "response." + field.GoName
				case protoreflect.FloatKind, protoreflect.DoubleKind:
					if nativeFloat != "" {
						m.gTest.P("// Skip ", field.GoName, " logic used at ", nativeFloat, "(", outputMarshal.String(), ")")
						continue
					}

					nativeFloat = "response." + field.GoName
				case protoreflect.BoolKind:
					if nativeBool != "" {
						m.gTest.P("// Skip ", field.GoName, " logic used at ", nativeBool, "(", outputMarshal.String(), ")")
						continue
					}

					nativeBool = "response." + field.GoName
				case protoreflect.StringKind:
					skipAll = "response." + field.GoName
				case protoreflect.BytesKind:
					skipAll = "response." + field.GoName
				}
			}
		case pb.RestMarshal_Protobuf:
		case pb.RestMarshal_XML:
			if skipXML != "" {
				m.gTest.P("// Skip ", field.GoName, " logic used at ", skipXML, "(", outputMarshal.String(), ")")
				continue
			}

			skipXML = "response." + field.GoName
		case pb.RestMarshal_FormData:
			skipAll = "response." + field.GoName
		case pb.RestMarshal_XWwwFormUrlencoded:
			skipAll = "response." + field.GoName
		case pb.RestMarshal_NoneRestMarshal:
			skipAll = "response." + field.GoName
		}

		m.gTest.P("// ", outputMarshal.String())
		m.gTest.P("t.Run(", unmarshalStandName, ", func(t *testing.T) {")
		if field.Desc.IsMap() || field.Desc.IsList() {
			switch outputMarshal {
			case pb.RestMarshal_Native:
				m.gTest.P("writeBytes := []byte(", fmtSprint, "(response.", field.GoName, "))")
			case pb.RestMarshal_JSON:
				m.gTest.P("writeBytes, _ := ", jsonPackageMarshal, "(response.", field.GoName, ")")
			case pb.RestMarshal_XML:
				m.gTest.P("jsonByteSlice, err := ", jsonPackageMarshal, "(response.", field.GoName, ")")
				m.gTest.P(requireNoError, "(t, err)")
				m.gTest.P()
				m.gTest.P("mxjMap, err := ", mxjNewMapJson, "(jsonByteSlice)")
				m.gTest.P(requireNoError, "(t, err)")
				m.gTest.P()
				m.gTest.P("writeBytes, err := mxjMap.Xml()")
				m.gTest.P(requireNoError, "(t, err)")
			case pb.RestMarshal_FormData:
			case pb.RestMarshal_XWwwFormUrlencoded:
			default:
				m.gTest.P("writeBytes := []byte(", fmtSprint, "(response.", field.GoName, "))")
			}

		} else {
			switch outputMarshal {
			case pb.RestMarshal_Native:
				m.gTest.P("writeBytes := []byte(", fmtSprint, "(*response.", field.GoName, "))")
			case pb.RestMarshal_JSON:
				m.gTest.P("writeBytes, err := ", jsonPackageMarshal, "(response.", field.GoName, ")")
				m.gTest.P(requireNoError, "(t, err)")
			case pb.RestMarshal_Protobuf:
				m.gTest.P("writeBytes, err := ", protoPackageMarshal, "(response.", field.GoName, ")")
				m.gTest.P(requireNoError, "(t, err)")
			case pb.RestMarshal_XML:
				m.gTest.P("jsonByteSlice, err := ", jsonPackageMarshal, "(response.", field.GoName, ")")
				m.gTest.P(requireNoError, "(t, err)")
				m.gTest.P()
				m.gTest.P("mxjMap, err := ", mxjNewMapJson, "(jsonByteSlice)")
				m.gTest.P(requireNoError, "(t, err)")
				m.gTest.P()
				m.gTest.P("writeBytes, err := mxjMap.Xml()")
				m.gTest.P(requireNoError, "(t, err)")
			case pb.RestMarshal_FormData:
			case pb.RestMarshal_XWwwFormUrlencoded:
			default:
				m.gTest.P("writeBytes := []byte(", fmtSprint, "(*response.", field.GoName, "))")
			}
		}

		switch outputMarshal {
		case pb.RestMarshal_FormData:
			m.gTest.P("buffer := &", bytesPackageBuffer, "{}")
			m.gTest.P("formData := ", multipartNewWriter, "(buffer)")
			m.gTest.P()

			if field.Desc.IsMap() {
				m.gTest.P("for key, value := range response.Get", field.GoName, "() {")
				m.gTest.P(requireNoError, "(t, formData.WriteField(key, value))")
				m.gTest.P("}")
			} else if field.Desc.IsList() {
				m.gTest.P("for index, item := range response.Get", field.GoName, "() {")
				m.gTest.P(requireNoError, "(t, formData.WriteField(\"", field.GoName, "[\"+", fmtSprint, "(index)+\"]\", item))")
				m.gTest.P("}")
			} else if kind == protoreflect.MessageKind {
				for _, messageField := range field.Message.Fields {
					m.gTest.P(requireNoError, "(t, formData.WriteField(\"", messageField.GoName, "\", response.Get", field.GoName, "().Get", messageField.GoName, "()))")
				}
			} else if kind == protoreflect.StringKind {
				m.gTest.P(requireNoError, "(t, formData.WriteField(\"", field.GoName, "\", response.Get", field.GoName, "()))")
			}

			m.gTest.P(requireNoError, "(t, formData.Close())")
			m.gTest.P()
			m.gTest.P("writeBytes := buffer.Bytes()")
		case pb.RestMarshal_XWwwFormUrlencoded:
			m.gTest.P("requestForm := ", urlPackageValues, "{}")
			if field.Desc.IsMap() {
				m.gTest.P("for key, value := range response.Get", field.GoName, "() {")
				m.gTest.P("requestForm.Set(key, value)")
				m.gTest.P("}")
			} else if field.Desc.IsList() {
				m.gTest.P("for index, value := range response.Get", field.GoName, "() {")
				m.gTest.P("requestForm.Set(\"", field.GoName, "[\"+", fmtSprint, "(index)+\"]\", value)")
				m.gTest.P("}")
			} else if kind == protoreflect.MessageKind {
				for _, messageField := range field.Message.Fields {
					m.gTest.P("requestForm.Set(\"", messageField.GoName, "\", response.Get", field.GoName, "().Get", messageField.GoName, "())")
				}
			} else if kind == protoreflect.StringKind {
				m.gTest.P("requestForm.Set(\"", field.GoName, "\", response.Get", field.GoName, "())")
			}

			m.gTest.P()
			m.gTest.P("writeBytes := []byte(requestForm.Encode())")
		}

		m.gTest.P()
		m.gTest.P("// define test server")
		m.gTest.P("server := ", httptestPackageNewServer, "(")
		m.gTest.P(httpPackageHandlerFunc, "(")
		m.gTest.P("func(w ", httpPackageResponseWriter, ", r *", httpPackageRequest, ") {")
		switch outputMarshal {
		case pb.RestMarshal_FormData:
			m.gTest.P("w.Header().Set(\"Content-Type\", formData.FormDataContentType())")
		case pb.RestMarshal_XWwwFormUrlencoded:
			m.gTest.P("w.Header().Set(\"Content-Type\", \"application/x-www-form-urlencoded\")")
		}
		m.gTest.P("_, err := w.Write(writeBytes)")
		m.gTest.P(requireNoError, "(t, err)")
		m.gTest.P("},")
		m.gTest.P("),")
		m.gTest.P(")")
		m.gTest.P("defer server.Close()")
		m.gTest.P()
		m.gTest.P("rest", m.service.GoName, "BaseURL[", unmarshalStandName, "] = server.URL")
		m.gTest.P("testingRest := NewRest", m.service.GoName, "Server(", unmarshalStandName, ", nil)")
		m.gTest.P("result, err := testingRest.", m.method.GoName, "(")
		m.restPath.PrintAllArguments(m.gTest, false)
		m.gTest.P(")")
		m.gTest.P(requireNoError, "(t, err)")
		m.gTest.P(requireNotNil, "(t, result)")
		m.gTest.P(requireEqual, "(t, ", m.answerTypeIdent, "_", field.GoName, ", result.ResponseOutput)")

		switch outputMarshal {
		case pb.RestMarshal_Protobuf:
			m.gTest.P("resultBytes, err := ", protoPackageMarshal, "(result.Response.", field.GoName, ")")
			m.gTest.P(requireNoError, "(t, err)")
			m.gTest.P()
			m.gTest.P(requireEqual, "(t, writeBytes, resultBytes)")
		case pb.RestMarshal_XML:
			m.gTest.P("resultJsonByteSlice, err := ", jsonPackageMarshal, "(result.Response.", field.GoName, ")")
			m.gTest.P(requireNoError, "(t, err)")
			m.gTest.P()
			m.gTest.P("resultMxjMap, err := ", mxjNewMapJson, "(resultJsonByteSlice)")
			m.gTest.P(requireNoError, "(t, err)")
			m.gTest.P()
			m.gTest.P("resultBytes, err := resultMxjMap.Xml()")
			m.gTest.P(requireNoError, "(t, err)")
			m.gTest.P()
			m.gTest.P(requireEqual, "(t, writeBytes, resultBytes)")
		//case pb.RestMarshal_FormData:
		//case pb.RestMarshal_XWwwFormUrlencoded:
		default:
			m.gTest.P(requireEqual, "(t, response.", field.GoName, ", result.Response.", field.GoName, ")")
		}
		m.gTest.P()
		m.gTest.P("})")

	}

	m.gTest.P("}")
	m.gTest.P()
}

func (m *RestMethod) GenerateInterface() {
	m.g.P(m.method.GoName, "(")
	m.restPath.PrintAllArguments(m.g, true)
	m.g.P(") (")
	m.g.P("response *", m.responseIdent, ",")
	m.g.P("err error,")
	m.g.P(")")
}

var newBool bool

func NewBool() any {
	newBool = !newBool
	return newBool
}

var newNumber uint

func NewNumber() any {
	newNumber++
	return newNumber
}

var newFloat float32 = 0.1

func NewFloat() any {
	newFloat++
	return newFloat
}

var newAny int

func NewAny() any {
	newAny++
	switch newAny % 5 {
	case 1:
		return NewBool()
	case 2:
		return NewNumber()
	case 3:
		return NewFloat()
	default:
		return NewString()
	}
}

var newString int

func NewString() string {
	newString++
	return "\"string_" + fmt.Sprint(newString) + "\""
}

var _BoolValueMap = map[string]bool{}

func BoolValue(key string) bool {
	_BoolValueMap[key] = !_BoolValueMap[key]
	return _BoolValueMap[key]
}

var _IntValueMap = map[string]int{}

func IntValue(key string) int {
	_IntValueMap[key]++
	return _IntValueMap[key]
}

var _FloatValueMap = map[string]float32{}

func FloatValue(key string) float32 {
	if _, ok := _FloatValueMap[key]; !ok {
		_FloatValueMap[key] = 1.1
	}

	_FloatValueMap[key]++
	return _FloatValueMap[key]
}

var _StringValueMap = map[string]int{}

func StringValue(key string) string {
	_StringValueMap[key]++
	return "string_" + fmt.Sprint(_StringValueMap[key])
}

var _BytesValueMap = map[string]int{}

func BytesValue(key string) []byte {
	_BytesValueMap[key]++
	return []byte("bytes_" + fmt.Sprint(_BytesValueMap[key]))
}

func CreateMapKey(descriptor protoreflect.FieldDescriptor) protoreflect.MapKey {
	switch descriptor.Kind() {
	case protoreflect.BoolKind:
		return protoreflect.ValueOf(BoolValue(descriptor.JSONName())).MapKey()
	case protoreflect.EnumKind:
		return protoreflect.ValueOf(protoreflect.EnumNumber(IntValue(descriptor.JSONName()))).MapKey()
	case protoreflect.Int32Kind, protoreflect.Sint32Kind, protoreflect.Sfixed32Kind:
		return protoreflect.ValueOf(int32(IntValue(descriptor.JSONName()))).MapKey()
	case protoreflect.Uint32Kind, protoreflect.Fixed32Kind:
		return protoreflect.ValueOf(uint32(IntValue(descriptor.JSONName()))).MapKey()
	case protoreflect.Int64Kind, protoreflect.Sint64Kind, protoreflect.Sfixed64Kind:
		return protoreflect.ValueOf(int64(IntValue(descriptor.JSONName()))).MapKey()
	case protoreflect.Uint64Kind, protoreflect.Fixed64Kind:
		return protoreflect.ValueOf(uint64(IntValue(descriptor.JSONName()))).MapKey()
	case protoreflect.FloatKind:
		return protoreflect.ValueOf(float32(FloatValue(descriptor.JSONName()))).MapKey()
	case protoreflect.DoubleKind:
		return protoreflect.ValueOf(float64(FloatValue(descriptor.JSONName()))).MapKey()
	case protoreflect.StringKind:
		return protoreflect.ValueOf(StringValue(descriptor.JSONName())).MapKey()
	default:
		panic("unknown map key")
	}
}

func CreateValue(descriptor protoreflect.FieldDescriptor) (protoreflect.Value, any) {
	switch descriptor.Kind() {
	case protoreflect.BoolKind:
		value := BoolValue(descriptor.JSONName())
		return protoreflect.ValueOf(value), value
	case protoreflect.EnumKind:
		value := IntValue(descriptor.JSONName())
		return protoreflect.ValueOf(protoreflect.EnumNumber(value)), value
	case protoreflect.Int32Kind, protoreflect.Sint32Kind, protoreflect.Sfixed32Kind:
		value := IntValue(descriptor.JSONName())
		return protoreflect.ValueOf(int32(value)), value
	case protoreflect.Uint32Kind, protoreflect.Fixed32Kind:
		value := IntValue(descriptor.JSONName())
		return protoreflect.ValueOf(uint32(value)), value
	case protoreflect.Int64Kind, protoreflect.Sint64Kind, protoreflect.Sfixed64Kind:
		value := IntValue(descriptor.JSONName())
		return protoreflect.ValueOf(int64(value)), value
	case protoreflect.Uint64Kind, protoreflect.Fixed64Kind:
		value := IntValue(descriptor.JSONName())
		return protoreflect.ValueOf(uint64(value)), value
	case protoreflect.FloatKind:
		value := FloatValue(descriptor.JSONName())
		return protoreflect.ValueOf(float32(value)), value
	case protoreflect.DoubleKind:
		value := FloatValue(descriptor.JSONName())
		return protoreflect.ValueOf(float64(value)), value
	case protoreflect.StringKind:
		value := StringValue(descriptor.JSONName())
		return protoreflect.ValueOf(value), value
	case protoreflect.BytesKind:
		value := BytesValue(descriptor.JSONName())
		return protoreflect.ValueOf(value), value
	case protoreflect.MessageKind:
		return CreateMessageValue(descriptor.Message())
	default:
		panic("unknown type: " + " " + descriptor.Kind().String())
	}
}

var _CountOfValues = 3

func CreateMessageValue(descriptor protoreflect.MessageDescriptor) (protoreflect.Value, any) {
	message := dynamicpb.NewMessage(descriptor)
	result := map[string]any{}
	for i := 0; i < descriptor.Fields().Len(); i++ {
		fieldDescriptor := descriptor.Fields().Get(i)

		if fieldDescriptor.IsMap() {
			anyValues := make(map[any]any)

			m := message.Mutable(fieldDescriptor).Map()

			for j := 0; j < _CountOfValues; j++ {
				key := CreateMapKey(fieldDescriptor.Message().Fields().Get(0))
				value, valueAny := CreateValue(fieldDescriptor.Message().Fields().Get(1))
				m.Set(key, value)
				anyValues[key.Interface()] = valueAny
			}

			result[fieldDescriptor.JSONName()] = anyValues
			continue
		}

		if fieldDescriptor.IsList() {
			var anyValues []any

			list := message.Mutable(fieldDescriptor).List()

			for j := 0; j < _CountOfValues; j++ {
				sliceValue, anyValue := CreateValue(fieldDescriptor)
				list.Append(sliceValue)
				anyValues = append(anyValues, anyValue)
			}

			result[fieldDescriptor.JSONName()] = anyValues
			continue
		}

		var value protoreflect.Value
		value, result[fieldDescriptor.JSONName()] = CreateValue(fieldDescriptor)

		message.Set(fieldDescriptor, value)
	}

	return protoreflect.ValueOf(message), result
}

func NewProtoMessageValue(msg *protogen.Message) proto.Message {
	m, _ := CreateMessageValue(msg.Desc)
	return m.Interface().(proto.Message)
}

type mapKeyHelper struct {
	Name string
	Key  protoreflect.MapKey
}

func PrintProtoMessage(
	prefix string,
	g *protogen.GeneratedFile,
	message proto.Message,
	msg *protogen.Message,
	mapper map[protoreflect.FullName]map[protoreflect.FullName]*Uniq,
	parent bool,
) {
	g.P(prefix, "&", msg.GoIdent, "{")
	for _, field := range msg.Fields {
		if field.Desc.IsMap() {
			var mapKey string
			switch field.Desc.MapKey().Kind() {
			case protoreflect.Int32Kind, protoreflect.Sint32Kind, protoreflect.Sfixed32Kind:
				mapKey = "int32"
			case protoreflect.Uint32Kind, protoreflect.Fixed32Kind:
				mapKey = "uint32"
			case protoreflect.Int64Kind, protoreflect.Sint64Kind, protoreflect.Sfixed64Kind:
				mapKey = "int64"
			case protoreflect.Uint64Kind, protoreflect.Fixed64Kind:
				mapKey = "uint64"
			case protoreflect.BoolKind:
				mapKey = "bool"
			case protoreflect.StringKind:
				mapKey = "string"
			default:
				panic("map key can not be float, double, bytes, message, or enum types")
			}

			var keys []mapKeyHelper
			message.ProtoReflect().Get(field.Desc).Map().Range(func(
				key protoreflect.MapKey,
				value protoreflect.Value,
			) bool {
				keyInterface := key.Interface()
				switch keyKind := field.Desc.MapKey().Kind(); keyKind {
				case protoreflect.StringKind:
					keyInterface = "\"" + key.Interface().(string) + "\""
				}

				keys = append(keys, mapKeyHelper{
					Name: fmt.Sprint(keyInterface),
					Key:  key,
				})

				return true
			})
			sort.Slice(keys, func(i, j int) bool {
				return keys[i].Name < keys[j].Name
			})

			kind := field.Desc.MapValue().Kind()
			m := message.ProtoReflect().Get(field.Desc).Map()
			if kind == protoreflect.MessageKind {
				loggerMap := mapper[field.Desc.MapKey().FullName()][field.Desc.MapValue().FullName()].LoggerMap
				g.P(field.GoName, ": map[", mapKey, "]*", loggerMap.Value, "{")

				for _, key := range keys {
					PrintProtoMessage(
						key.Name+":",
						g,
						m.Get(key.Key).Interface().(proto.Message),
						field.Message.Fields[1].Message,
						mapper,
						false,
					)
				}
				g.P("},")
			} else {
				var mapValue any
				switch field.Desc.MapValue().Kind() {
				case protoreflect.Int32Kind, protoreflect.Sint32Kind, protoreflect.Sfixed32Kind:
					mapValue = "int32"
				case protoreflect.Uint32Kind, protoreflect.Fixed32Kind:
					mapValue = "uint32"
				case protoreflect.Int64Kind, protoreflect.Sint64Kind, protoreflect.Sfixed64Kind:
					mapValue = "int64"
				case protoreflect.Uint64Kind, protoreflect.Fixed64Kind:
					mapValue = "uint64"
				case protoreflect.BoolKind:
					mapValue = "bool"
				case protoreflect.FloatKind:
					mapValue = "float32"
				case protoreflect.DoubleKind:
					mapValue = "float64"
				case protoreflect.StringKind:
					mapValue = "string"
				case protoreflect.EnumKind:
					mapValue = field.Message.Fields[1].Enum.GoIdent
				case protoreflect.BytesKind:
					mapValue = "[]byte"
				default:
					panic(field.Desc.MapValue().Kind())
				}

				g.P(field.GoName, ": map[", mapKey, "]", mapValue, "{")
				for _, key := range keys {
					valueInterface := m.Get(key.Key).Interface()
					switch valueKind := field.Desc.MapValue().Kind(); valueKind {
					case protoreflect.StringKind:
						valueInterface = "\"" + valueInterface.(string) + "\""
					case protoreflect.BytesKind:
						var strInterface []string
						for _, v := range valueInterface.([]byte) {
							strInterface = append(strInterface, fmt.Sprint(v))
						}
						valueInterface = "[]byte{" + strings.Join(strInterface, ",") + "}"
					}
					g.P(key.Name, ":", valueInterface, ",")
				}

				g.P("},")
			}

			continue
		}

		kind := field.Desc.Kind()
		if kind == protoreflect.MessageKind {
			if field.Desc.IsList() {
				g.P(field.GoName, ": []*", field.Message.GoIdent, "{")
				for i := 0; i < message.ProtoReflect().Get(field.Desc).List().Len(); i++ {
					PrintProtoMessage(
						"",
						g,
						message.ProtoReflect().Get(field.Desc).List().Get(i).Message().Interface().(proto.Message),
						field.Message,
						mapper,
						false,
					)
				}
				g.P("},")
			} else {
				PrintProtoMessage(
					field.GoName+":",
					g,
					message.ProtoReflect().Get(field.Desc).Message().Interface().(proto.Message),
					field.Message,
					mapper,
					false,
				)
			}
			continue
		}

		var ptrType any
		switch field.Desc.Kind() {
		case protoreflect.Int32Kind, protoreflect.Sint32Kind, protoreflect.Sfixed32Kind:
			ptrType = "int32"
		case protoreflect.Uint32Kind, protoreflect.Fixed32Kind:
			ptrType = "uint32"
		case protoreflect.Int64Kind, protoreflect.Sint64Kind, protoreflect.Sfixed64Kind:
			ptrType = "int64"
		case protoreflect.Uint64Kind, protoreflect.Fixed64Kind:
			ptrType = "uint64"
		case protoreflect.BoolKind:
			ptrType = "bool"
		case protoreflect.FloatKind:
			ptrType = "float32"
		case protoreflect.DoubleKind:
			ptrType = "float64"
		case protoreflect.StringKind:
			ptrType = "string"
		case protoreflect.EnumKind:
			ptrType = field.Enum.GoIdent
		case protoreflect.BytesKind:
			ptrType = "[]byte"
		}

		if field.Desc.IsList() {
			list := message.ProtoReflect().Get(field.Desc).List()
			switch kind {
			case protoreflect.BytesKind:
				g.P(field.GoName, ": [][]byte{")
				for i := 0; i < list.Len(); i++ {
					var strInterface []string
					for _, v := range list.Get(i).Interface().([]byte) {
						strInterface = append(strInterface, fmt.Sprint(v))
					}
					valueInterface := "[]byte{" + strings.Join(strInterface, ",") + "}"
					g.P(valueInterface, ",")
				}
				g.P("},")
			case protoreflect.StringKind:
				g.P(field.GoName, ": []string{")
				for i := 0; i < list.Len(); i++ {
					g.P("\"", message.ProtoReflect().Get(field.Desc).List().Get(i).Interface(), "\",")
				}
				g.P("},")
			default:
				g.P(field.GoName, ": []", ptrType, "{")
				for i := 0; i < message.ProtoReflect().Get(field.Desc).List().Len(); i++ {
					g.P(message.ProtoReflect().Get(field.Desc).List().Get(i).Interface(), ",")
				}
				g.P("},")
			}

			continue
		}

		switch kind {
		case protoreflect.StringKind:
			g.P(field.GoName, ": ", testHelpersPackagePtr, "(\"", message.ProtoReflect().Get(field.Desc).Interface(), "\"),")
		case protoreflect.BytesKind:
			var strInterface []string
			for _, v := range message.ProtoReflect().Get(field.Desc).Interface().([]byte) {
				strInterface = append(strInterface, fmt.Sprint(v))
			}
			valueInterface := "[]byte{" + strings.Join(strInterface, ",") + "}"
			g.P(field.GoName, ": ", valueInterface, ",")
		default:
			g.P(field.GoName, ": ", testHelpersPackagePtr, "[", ptrType, "](", message.ProtoReflect().Get(field.Desc).Interface(), "),")
		}

	}

	if parent {
		g.P("}")
	} else {
		g.P("},")
	}
}
