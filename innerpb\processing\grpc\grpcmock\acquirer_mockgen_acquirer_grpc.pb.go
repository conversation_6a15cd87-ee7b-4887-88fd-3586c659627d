// Code generated by MockGen. DO NOT EDIT.
// Source: acquirer_grpc.pb.go

// Package grpcmock is a generated GoMock package.
package grpcmock

import (
	context "context"
	reflect "reflect"

	grpc "git.local/sensitive/innerpb/processing/grpc"
	gomock "github.com/golang/mock/gomock"
	grpc0 "google.golang.org/grpc"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
)

// MockAcquirerClient is a mock of AcquirerClient interface.
type MockAcquirerClient struct {
	ctrl     *gomock.Controller
	recorder *MockAcquirerClientMockRecorder
}

// MockAcquirerClientMockRecorder is the mock recorder for MockAcquirerClient.
type MockAcquirerClientMockRecorder struct {
	mock *MockAcquirerClient
}

// NewMockAcquirerClient creates a new mock instance.
func NewMockAcquirerClient(ctrl *gomock.Controller) *MockAcquirerClient {
	mock := &MockAcquirerClient{ctrl: ctrl}
	mock.recorder = &MockAcquirerClientMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockAcquirerClient) EXPECT() *MockAcquirerClientMockRecorder {
	return m.recorder
}

// GetAcquirerByID mocks base method.
func (m *MockAcquirerClient) GetAcquirerByID(ctx context.Context, in *grpc.GetAcquirerByIDRequestV1, opts ...grpc0.CallOption) (*grpc.GetAcquirerByIDResponseV1, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetAcquirerByID", varargs...)
	ret0, _ := ret[0].(*grpc.GetAcquirerByIDResponseV1)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAcquirerByID indicates an expected call of GetAcquirerByID.
func (mr *MockAcquirerClientMockRecorder) GetAcquirerByID(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAcquirerByID", reflect.TypeOf((*MockAcquirerClient)(nil).GetAcquirerByID), varargs...)
}

// GetAcquirers mocks base method.
func (m *MockAcquirerClient) GetAcquirers(ctx context.Context, in *emptypb.Empty, opts ...grpc0.CallOption) (*grpc.GetAcquirersV1, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetAcquirers", varargs...)
	ret0, _ := ret[0].(*grpc.GetAcquirersV1)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAcquirers indicates an expected call of GetAcquirers.
func (mr *MockAcquirerClientMockRecorder) GetAcquirers(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAcquirers", reflect.TypeOf((*MockAcquirerClient)(nil).GetAcquirers), varargs...)
}

// GetBankByID mocks base method.
func (m *MockAcquirerClient) GetBankByID(ctx context.Context, in *grpc.GetBankByIDRequestV1, opts ...grpc0.CallOption) (*grpc.GetBankByIDResponseV1, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetBankByID", varargs...)
	ret0, _ := ret[0].(*grpc.GetBankByIDResponseV1)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetBankByID indicates an expected call of GetBankByID.
func (mr *MockAcquirerClientMockRecorder) GetBankByID(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetBankByID", reflect.TypeOf((*MockAcquirerClient)(nil).GetBankByID), varargs...)
}

// GetCountryByID mocks base method.
func (m *MockAcquirerClient) GetCountryByID(ctx context.Context, in *grpc.GetCountryByIDRequestV1, opts ...grpc0.CallOption) (*grpc.GetCountryByIDResponseV1, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetCountryByID", varargs...)
	ret0, _ := ret[0].(*grpc.GetCountryByIDResponseV1)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetCountryByID indicates an expected call of GetCountryByID.
func (mr *MockAcquirerClientMockRecorder) GetCountryByID(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetCountryByID", reflect.TypeOf((*MockAcquirerClient)(nil).GetCountryByID), varargs...)
}

// GetIpsByID mocks base method.
func (m *MockAcquirerClient) GetIpsByID(ctx context.Context, in *grpc.GetIpsByIDRequestV1, opts ...grpc0.CallOption) (*grpc.GetIpsByIDResponseV1, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetIpsByID", varargs...)
	ret0, _ := ret[0].(*grpc.GetIpsByIDResponseV1)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetIpsByID indicates an expected call of GetIpsByID.
func (mr *MockAcquirerClientMockRecorder) GetIpsByID(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetIpsByID", reflect.TypeOf((*MockAcquirerClient)(nil).GetIpsByID), varargs...)
}

// GetIssuerByID mocks base method.
func (m *MockAcquirerClient) GetIssuerByID(ctx context.Context, in *grpc.GetIssuerByIDRequestV1, opts ...grpc0.CallOption) (*grpc.GetIssuerByIDResponseV1, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetIssuerByID", varargs...)
	ret0, _ := ret[0].(*grpc.GetIssuerByIDResponseV1)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetIssuerByID indicates an expected call of GetIssuerByID.
func (mr *MockAcquirerClientMockRecorder) GetIssuerByID(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetIssuerByID", reflect.TypeOf((*MockAcquirerClient)(nil).GetIssuerByID), varargs...)
}

// MockAcquirerServer is a mock of AcquirerServer interface.
type MockAcquirerServer struct {
	ctrl     *gomock.Controller
	recorder *MockAcquirerServerMockRecorder
}

// MockAcquirerServerMockRecorder is the mock recorder for MockAcquirerServer.
type MockAcquirerServerMockRecorder struct {
	mock *MockAcquirerServer
}

// NewMockAcquirerServer creates a new mock instance.
func NewMockAcquirerServer(ctrl *gomock.Controller) *MockAcquirerServer {
	mock := &MockAcquirerServer{ctrl: ctrl}
	mock.recorder = &MockAcquirerServerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockAcquirerServer) EXPECT() *MockAcquirerServerMockRecorder {
	return m.recorder
}

// GetAcquirerByID mocks base method.
func (m *MockAcquirerServer) GetAcquirerByID(arg0 context.Context, arg1 *grpc.GetAcquirerByIDRequestV1) (*grpc.GetAcquirerByIDResponseV1, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAcquirerByID", arg0, arg1)
	ret0, _ := ret[0].(*grpc.GetAcquirerByIDResponseV1)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAcquirerByID indicates an expected call of GetAcquirerByID.
func (mr *MockAcquirerServerMockRecorder) GetAcquirerByID(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAcquirerByID", reflect.TypeOf((*MockAcquirerServer)(nil).GetAcquirerByID), arg0, arg1)
}

// GetAcquirers mocks base method.
func (m *MockAcquirerServer) GetAcquirers(arg0 context.Context, arg1 *emptypb.Empty) (*grpc.GetAcquirersV1, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAcquirers", arg0, arg1)
	ret0, _ := ret[0].(*grpc.GetAcquirersV1)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAcquirers indicates an expected call of GetAcquirers.
func (mr *MockAcquirerServerMockRecorder) GetAcquirers(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAcquirers", reflect.TypeOf((*MockAcquirerServer)(nil).GetAcquirers), arg0, arg1)
}

// GetBankByID mocks base method.
func (m *MockAcquirerServer) GetBankByID(arg0 context.Context, arg1 *grpc.GetBankByIDRequestV1) (*grpc.GetBankByIDResponseV1, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetBankByID", arg0, arg1)
	ret0, _ := ret[0].(*grpc.GetBankByIDResponseV1)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetBankByID indicates an expected call of GetBankByID.
func (mr *MockAcquirerServerMockRecorder) GetBankByID(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetBankByID", reflect.TypeOf((*MockAcquirerServer)(nil).GetBankByID), arg0, arg1)
}

// GetCountryByID mocks base method.
func (m *MockAcquirerServer) GetCountryByID(arg0 context.Context, arg1 *grpc.GetCountryByIDRequestV1) (*grpc.GetCountryByIDResponseV1, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetCountryByID", arg0, arg1)
	ret0, _ := ret[0].(*grpc.GetCountryByIDResponseV1)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetCountryByID indicates an expected call of GetCountryByID.
func (mr *MockAcquirerServerMockRecorder) GetCountryByID(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetCountryByID", reflect.TypeOf((*MockAcquirerServer)(nil).GetCountryByID), arg0, arg1)
}

// GetIpsByID mocks base method.
func (m *MockAcquirerServer) GetIpsByID(arg0 context.Context, arg1 *grpc.GetIpsByIDRequestV1) (*grpc.GetIpsByIDResponseV1, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetIpsByID", arg0, arg1)
	ret0, _ := ret[0].(*grpc.GetIpsByIDResponseV1)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetIpsByID indicates an expected call of GetIpsByID.
func (mr *MockAcquirerServerMockRecorder) GetIpsByID(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetIpsByID", reflect.TypeOf((*MockAcquirerServer)(nil).GetIpsByID), arg0, arg1)
}

// GetIssuerByID mocks base method.
func (m *MockAcquirerServer) GetIssuerByID(arg0 context.Context, arg1 *grpc.GetIssuerByIDRequestV1) (*grpc.GetIssuerByIDResponseV1, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetIssuerByID", arg0, arg1)
	ret0, _ := ret[0].(*grpc.GetIssuerByIDResponseV1)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetIssuerByID indicates an expected call of GetIssuerByID.
func (mr *MockAcquirerServerMockRecorder) GetIssuerByID(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetIssuerByID", reflect.TypeOf((*MockAcquirerServer)(nil).GetIssuerByID), arg0, arg1)
}

// mustEmbedUnimplementedAcquirerServer mocks base method.
func (m *MockAcquirerServer) mustEmbedUnimplementedAcquirerServer() {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "mustEmbedUnimplementedAcquirerServer")
}

// mustEmbedUnimplementedAcquirerServer indicates an expected call of mustEmbedUnimplementedAcquirerServer.
func (mr *MockAcquirerServerMockRecorder) mustEmbedUnimplementedAcquirerServer() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "mustEmbedUnimplementedAcquirerServer", reflect.TypeOf((*MockAcquirerServer)(nil).mustEmbedUnimplementedAcquirerServer))
}

// MockUnsafeAcquirerServer is a mock of UnsafeAcquirerServer interface.
type MockUnsafeAcquirerServer struct {
	ctrl     *gomock.Controller
	recorder *MockUnsafeAcquirerServerMockRecorder
}

// MockUnsafeAcquirerServerMockRecorder is the mock recorder for MockUnsafeAcquirerServer.
type MockUnsafeAcquirerServerMockRecorder struct {
	mock *MockUnsafeAcquirerServer
}

// NewMockUnsafeAcquirerServer creates a new mock instance.
func NewMockUnsafeAcquirerServer(ctrl *gomock.Controller) *MockUnsafeAcquirerServer {
	mock := &MockUnsafeAcquirerServer{ctrl: ctrl}
	mock.recorder = &MockUnsafeAcquirerServerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockUnsafeAcquirerServer) EXPECT() *MockUnsafeAcquirerServerMockRecorder {
	return m.recorder
}

// mustEmbedUnimplementedAcquirerServer mocks base method.
func (m *MockUnsafeAcquirerServer) mustEmbedUnimplementedAcquirerServer() {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "mustEmbedUnimplementedAcquirerServer")
}

// mustEmbedUnimplementedAcquirerServer indicates an expected call of mustEmbedUnimplementedAcquirerServer.
func (mr *MockUnsafeAcquirerServerMockRecorder) mustEmbedUnimplementedAcquirerServer() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "mustEmbedUnimplementedAcquirerServer", reflect.TypeOf((*MockUnsafeAcquirerServer)(nil).mustEmbedUnimplementedAcquirerServer))
}
