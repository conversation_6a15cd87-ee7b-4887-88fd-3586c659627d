// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.5
// 	protoc        v5.29.3
// source: inner/processing/grpc/collector.proto

package grpc

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type CollectorEmptyResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CollectorEmptyResponse) Reset() {
	*x = CollectorEmptyResponse{}
	mi := &file_inner_processing_grpc_collector_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CollectorEmptyResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CollectorEmptyResponse) ProtoMessage() {}

func (x *CollectorEmptyResponse) ProtoReflect() protoreflect.Message {
	mi := &file_inner_processing_grpc_collector_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CollectorEmptyResponse.ProtoReflect.Descriptor instead.
func (*CollectorEmptyResponse) Descriptor() ([]byte, []int) {
	return file_inner_processing_grpc_collector_proto_rawDescGZIP(), []int{0}
}

type CollectTransactionRequestV1 struct {
	state                     protoimpl.MessageState `protogen:"open.v1"`
	TransactionId             *uint64                `protobuf:"varint,1,opt,name=transaction_id,json=transactionId" json:"transaction_id,omitempty"`                                      // ctid field in antifraud F.A.C.C.T system
	MerchantId                *uint64                `protobuf:"varint,2,opt,name=merchant_id,json=merchantId" json:"merchant_id,omitempty"`                                               // user.merchantID field in antifraud F.A.C.C.T system
	ProjectClientId           *string                `protobuf:"bytes,3,opt,name=project_client_id,json=projectClientId" json:"project_client_id,omitempty"`                               // user.userID field in antifraud F.A.C.C.T system
	Phone                     *string                `protobuf:"bytes,4,opt,name=phone" json:"phone,omitempty"`                                                                            // user.phone field in antifraud F.A.C.C.T system
	Email                     *string                `protobuf:"bytes,5,opt,name=email" json:"email,omitempty"`                                                                            // user.email field in antifraud F.A.C.C.T system
	HashedPan                 []byte                 `protobuf:"bytes,6,opt,name=hashed_pan,json=hashedPan" json:"hashed_pan,omitempty"`                                                   // pan.hash_card field in antifraud F.A.C.C.T system
	TransactionAggregatedType *string                `protobuf:"bytes,7,opt,name=transaction_aggregated_type,json=transactionAggregatedType" json:"transaction_aggregated_type,omitempty"` // transaction.type field in antifraud F.A.C.C.T system
	SessionGeneratedAt        *timestamppb.Timestamp `protobuf:"bytes,8,opt,name=session_generated_at,json=sessionGeneratedAt" json:"session_generated_at,omitempty"`                      // transaction.start_generating field in antifraud F.A.C.C.T system
	SessionStartedAt          *timestamppb.Timestamp `protobuf:"bytes,9,opt,name=session_started_at,json=sessionStartedAt" json:"session_started_at,omitempty"`                            // transaction.send_request field in antifraud F.A.C.C.T system
	TransactionAmount         *float64               `protobuf:"fixed64,10,opt,name=transaction_amount,json=transactionAmount" json:"transaction_amount,omitempty"`                        // sum.wn and sum.fn fields in antifraud F.A.C.C.T system
	TransactionCurrency       *string                `protobuf:"bytes,11,opt,name=transaction_currency,json=transactionCurrency" json:"transaction_currency,omitempty"`                    // currency field in antifraud F.A.C.C.T system
	TransactionType           *string                `protobuf:"bytes,12,opt,name=transaction_type,json=transactionType" json:"transaction_type,omitempty"`                                // subtype field in antifraud F.A.C.C.T system
	unknownFields             protoimpl.UnknownFields
	sizeCache                 protoimpl.SizeCache
}

func (x *CollectTransactionRequestV1) Reset() {
	*x = CollectTransactionRequestV1{}
	mi := &file_inner_processing_grpc_collector_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CollectTransactionRequestV1) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CollectTransactionRequestV1) ProtoMessage() {}

func (x *CollectTransactionRequestV1) ProtoReflect() protoreflect.Message {
	mi := &file_inner_processing_grpc_collector_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CollectTransactionRequestV1.ProtoReflect.Descriptor instead.
func (*CollectTransactionRequestV1) Descriptor() ([]byte, []int) {
	return file_inner_processing_grpc_collector_proto_rawDescGZIP(), []int{1}
}

func (x *CollectTransactionRequestV1) GetTransactionId() uint64 {
	if x != nil && x.TransactionId != nil {
		return *x.TransactionId
	}
	return 0
}

func (x *CollectTransactionRequestV1) GetMerchantId() uint64 {
	if x != nil && x.MerchantId != nil {
		return *x.MerchantId
	}
	return 0
}

func (x *CollectTransactionRequestV1) GetProjectClientId() string {
	if x != nil && x.ProjectClientId != nil {
		return *x.ProjectClientId
	}
	return ""
}

func (x *CollectTransactionRequestV1) GetPhone() string {
	if x != nil && x.Phone != nil {
		return *x.Phone
	}
	return ""
}

func (x *CollectTransactionRequestV1) GetEmail() string {
	if x != nil && x.Email != nil {
		return *x.Email
	}
	return ""
}

func (x *CollectTransactionRequestV1) GetHashedPan() []byte {
	if x != nil {
		return x.HashedPan
	}
	return nil
}

func (x *CollectTransactionRequestV1) GetTransactionAggregatedType() string {
	if x != nil && x.TransactionAggregatedType != nil {
		return *x.TransactionAggregatedType
	}
	return ""
}

func (x *CollectTransactionRequestV1) GetSessionGeneratedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.SessionGeneratedAt
	}
	return nil
}

func (x *CollectTransactionRequestV1) GetSessionStartedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.SessionStartedAt
	}
	return nil
}

func (x *CollectTransactionRequestV1) GetTransactionAmount() float64 {
	if x != nil && x.TransactionAmount != nil {
		return *x.TransactionAmount
	}
	return 0
}

func (x *CollectTransactionRequestV1) GetTransactionCurrency() string {
	if x != nil && x.TransactionCurrency != nil {
		return *x.TransactionCurrency
	}
	return ""
}

func (x *CollectTransactionRequestV1) GetTransactionType() string {
	if x != nil && x.TransactionType != nil {
		return *x.TransactionType
	}
	return ""
}

var File_inner_processing_grpc_collector_proto protoreflect.FileDescriptor

var file_inner_processing_grpc_collector_proto_rawDesc = string([]byte{
	0x0a, 0x25, 0x69, 0x6e, 0x6e, 0x65, 0x72, 0x2f, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69,
	0x6e, 0x67, 0x2f, 0x67, 0x72, 0x70, 0x63, 0x2f, 0x63, 0x6f, 0x6c, 0x6c, 0x65, 0x63, 0x74, 0x6f,
	0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x1f, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73,
	0x69, 0x6e, 0x67, 0x2e, 0x61, 0x6e, 0x74, 0x69, 0x5f, 0x66, 0x72, 0x61, 0x75, 0x64, 0x2e, 0x63,
	0x6f, 0x6c, 0x6c, 0x65, 0x63, 0x74, 0x6f, 0x72, 0x1a, 0x1f, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74,
	0x61, 0x6d, 0x70, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1b, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x65, 0x6d, 0x70, 0x74, 0x79,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x18, 0x0a, 0x16, 0x43, 0x6f, 0x6c, 0x6c, 0x65, 0x63,
	0x74, 0x6f, 0x72, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x22, 0xc1, 0x04, 0x0a, 0x1b, 0x43, 0x6f, 0x6c, 0x6c, 0x65, 0x63, 0x74, 0x54, 0x72, 0x61, 0x6e,
	0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x56, 0x31,
	0x12, 0x25, 0x0a, 0x0e, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x5f,
	0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x0d, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61,
	0x63, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x12, 0x1f, 0x0a, 0x0b, 0x6d, 0x65, 0x72, 0x63, 0x68,
	0x61, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x04, 0x52, 0x0a, 0x6d, 0x65,
	0x72, 0x63, 0x68, 0x61, 0x6e, 0x74, 0x49, 0x64, 0x12, 0x2a, 0x0a, 0x11, 0x70, 0x72, 0x6f, 0x6a,
	0x65, 0x63, 0x74, 0x5f, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0f, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x43, 0x6c, 0x69, 0x65,
	0x6e, 0x74, 0x49, 0x64, 0x12, 0x14, 0x0a, 0x05, 0x70, 0x68, 0x6f, 0x6e, 0x65, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x05, 0x70, 0x68, 0x6f, 0x6e, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x65, 0x6d,
	0x61, 0x69, 0x6c, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x65, 0x6d, 0x61, 0x69, 0x6c,
	0x12, 0x1d, 0x0a, 0x0a, 0x68, 0x61, 0x73, 0x68, 0x65, 0x64, 0x5f, 0x70, 0x61, 0x6e, 0x18, 0x06,
	0x20, 0x01, 0x28, 0x0c, 0x52, 0x09, 0x68, 0x61, 0x73, 0x68, 0x65, 0x64, 0x50, 0x61, 0x6e, 0x12,
	0x3e, 0x0a, 0x1b, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x61,
	0x67, 0x67, 0x72, 0x65, 0x67, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x07,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x19, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f,
	0x6e, 0x41, 0x67, 0x67, 0x72, 0x65, 0x67, 0x61, 0x74, 0x65, 0x64, 0x54, 0x79, 0x70, 0x65, 0x12,
	0x4c, 0x0a, 0x14, 0x73, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x5f, 0x67, 0x65, 0x6e, 0x65, 0x72,
	0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e,
	0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x12, 0x73, 0x65, 0x73, 0x73, 0x69,
	0x6f, 0x6e, 0x47, 0x65, 0x6e, 0x65, 0x72, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x12, 0x48, 0x0a,
	0x12, 0x73, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x5f, 0x73, 0x74, 0x61, 0x72, 0x74, 0x65, 0x64,
	0x5f, 0x61, 0x74, 0x18, 0x09, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65,
	0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x10, 0x73, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x53, 0x74,
	0x61, 0x72, 0x74, 0x65, 0x64, 0x41, 0x74, 0x12, 0x2d, 0x0a, 0x12, 0x74, 0x72, 0x61, 0x6e, 0x73,
	0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x0a, 0x20,
	0x01, 0x28, 0x01, 0x52, 0x11, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e,
	0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x31, 0x0a, 0x14, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61,
	0x63, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x63, 0x79, 0x18, 0x0b,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x13, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f,
	0x6e, 0x43, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x63, 0x79, 0x12, 0x29, 0x0a, 0x10, 0x74, 0x72, 0x61,
	0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x0c, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0f, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e,
	0x54, 0x79, 0x70, 0x65, 0x32, 0xe1, 0x01, 0x0a, 0x09, 0x43, 0x6f, 0x6c, 0x6c, 0x65, 0x63, 0x74,
	0x6f, 0x72, 0x12, 0x8b, 0x01, 0x0a, 0x12, 0x43, 0x6f, 0x6c, 0x6c, 0x65, 0x63, 0x74, 0x54, 0x72,
	0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x3c, 0x2e, 0x70, 0x72, 0x6f, 0x63,
	0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x2e, 0x61, 0x6e, 0x74, 0x69, 0x5f, 0x66, 0x72, 0x61, 0x75,
	0x64, 0x2e, 0x63, 0x6f, 0x6c, 0x6c, 0x65, 0x63, 0x74, 0x6f, 0x72, 0x2e, 0x43, 0x6f, 0x6c, 0x6c,
	0x65, 0x63, 0x74, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x56, 0x31, 0x1a, 0x37, 0x2e, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73,
	0x73, 0x69, 0x6e, 0x67, 0x2e, 0x61, 0x6e, 0x74, 0x69, 0x5f, 0x66, 0x72, 0x61, 0x75, 0x64, 0x2e,
	0x63, 0x6f, 0x6c, 0x6c, 0x65, 0x63, 0x74, 0x6f, 0x72, 0x2e, 0x43, 0x6f, 0x6c, 0x6c, 0x65, 0x63,
	0x74, 0x6f, 0x72, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x12, 0x46, 0x0a, 0x14, 0x47, 0x65, 0x74, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69,
	0x6f, 0x6e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x16, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79,
	0x1a, 0x16, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62,
	0x75, 0x66, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x42, 0x2d, 0x5a, 0x2b, 0x67, 0x69, 0x74, 0x2e,
	0x6c, 0x6f, 0x63, 0x61, 0x6c, 0x2f, 0x73, 0x65, 0x6e, 0x73, 0x69, 0x74, 0x69, 0x76, 0x65, 0x2f,
	0x69, 0x6e, 0x6e, 0x65, 0x72, 0x70, 0x62, 0x2f, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69,
	0x6e, 0x67, 0x2f, 0x67, 0x72, 0x70, 0x63, 0x62, 0x08, 0x65, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e,
	0x73, 0x70, 0xe8, 0x07,
})

var (
	file_inner_processing_grpc_collector_proto_rawDescOnce sync.Once
	file_inner_processing_grpc_collector_proto_rawDescData []byte
)

func file_inner_processing_grpc_collector_proto_rawDescGZIP() []byte {
	file_inner_processing_grpc_collector_proto_rawDescOnce.Do(func() {
		file_inner_processing_grpc_collector_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_inner_processing_grpc_collector_proto_rawDesc), len(file_inner_processing_grpc_collector_proto_rawDesc)))
	})
	return file_inner_processing_grpc_collector_proto_rawDescData
}

var file_inner_processing_grpc_collector_proto_msgTypes = make([]protoimpl.MessageInfo, 2)
var file_inner_processing_grpc_collector_proto_goTypes = []any{
	(*CollectorEmptyResponse)(nil),      // 0: processing.anti_fraud.collector.CollectorEmptyResponse
	(*CollectTransactionRequestV1)(nil), // 1: processing.anti_fraud.collector.CollectTransactionRequestV1
	(*timestamppb.Timestamp)(nil),       // 2: google.protobuf.Timestamp
	(*emptypb.Empty)(nil),               // 3: google.protobuf.Empty
}
var file_inner_processing_grpc_collector_proto_depIdxs = []int32{
	2, // 0: processing.anti_fraud.collector.CollectTransactionRequestV1.session_generated_at:type_name -> google.protobuf.Timestamp
	2, // 1: processing.anti_fraud.collector.CollectTransactionRequestV1.session_started_at:type_name -> google.protobuf.Timestamp
	1, // 2: processing.anti_fraud.collector.Collector.CollectTransaction:input_type -> processing.anti_fraud.collector.CollectTransactionRequestV1
	3, // 3: processing.anti_fraud.collector.Collector.GetTransactionStatus:input_type -> google.protobuf.Empty
	0, // 4: processing.anti_fraud.collector.Collector.CollectTransaction:output_type -> processing.anti_fraud.collector.CollectorEmptyResponse
	3, // 5: processing.anti_fraud.collector.Collector.GetTransactionStatus:output_type -> google.protobuf.Empty
	4, // [4:6] is the sub-list for method output_type
	2, // [2:4] is the sub-list for method input_type
	2, // [2:2] is the sub-list for extension type_name
	2, // [2:2] is the sub-list for extension extendee
	0, // [0:2] is the sub-list for field type_name
}

func init() { file_inner_processing_grpc_collector_proto_init() }
func file_inner_processing_grpc_collector_proto_init() {
	if File_inner_processing_grpc_collector_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_inner_processing_grpc_collector_proto_rawDesc), len(file_inner_processing_grpc_collector_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   2,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_inner_processing_grpc_collector_proto_goTypes,
		DependencyIndexes: file_inner_processing_grpc_collector_proto_depIdxs,
		MessageInfos:      file_inner_processing_grpc_collector_proto_msgTypes,
	}.Build()
	File_inner_processing_grpc_collector_proto = out.File
	file_inner_processing_grpc_collector_proto_goTypes = nil
	file_inner_processing_grpc_collector_proto_depIdxs = nil
}
