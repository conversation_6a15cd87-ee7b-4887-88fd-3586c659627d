// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.5.1
// - protoc             v5.29.3
// source: inner/processing/grpc/card.proto

package grpc

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.64.0 or later.
const _ = grpc.SupportPackageIsVersion9

const (
	Card_CreateClientV1_FullMethodName               = "/processing.card.card.Card/CreateClientV1"
	Card_GetCardTokensV1_FullMethodName              = "/processing.card.card.Card/GetCardTokensV1"
	Card_GetOneClickPayInCardsV1_FullMethodName      = "/processing.card.card.Card/GetOneClickPayInCardsV1"
	Card_GetPanByCardIdV1_FullMethodName             = "/processing.card.card.Card/GetPanByCardIdV1"
	Card_GetPanByHashedIdV1_FullMethodName           = "/processing.card.card.Card/GetPanByHashedIdV1"
	Card_GetOneClickPayOutCardsV1_FullMethodName     = "/processing.card.card.Card/GetOneClickPayOutCardsV1"
	Card_GetEncryptedCardToken_FullMethodName        = "/processing.card.card.Card/GetEncryptedCardToken"
	Card_GetClientListByVerification_FullMethodName  = "/processing.card.card.Card/GetClientListByVerification"
	Card_GetClientListByProjectClient_FullMethodName = "/processing.card.card.Card/GetClientListByProjectClient"
	Card_CheckClientActiveness_FullMethodName        = "/processing.card.card.Card/CheckClientActiveness"
	Card_GetCardByPan_FullMethodName                 = "/processing.card.card.Card/GetCardByPan"
	Card_DecryptPayInCard_FullMethodName             = "/processing.card.card.Card/DecryptPayInCard"
	Card_DecryptPayOutCard_FullMethodName            = "/processing.card.card.Card/DecryptPayOutCard"
	Card_ReEncryptCard_FullMethodName                = "/processing.card.card.Card/ReEncryptCard"
	Card_GetPanInfoByProjectId_FullMethodName        = "/processing.card.card.Card/GetPanInfoByProjectId"
	Card_NewKey_FullMethodName                       = "/processing.card.card.Card/NewKey"
	Card_RotateCardKeys_FullMethodName               = "/processing.card.card.Card/RotateCardKeys"
	Card_CheckExpireCards_FullMethodName             = "/processing.card.card.Card/CheckExpireCards"
	Card_CreateNewHashKey_FullMethodName             = "/processing.card.card.Card/CreateNewHashKey"
	Card_RotateHashKeys_FullMethodName               = "/processing.card.card.Card/RotateHashKeys"
)

// CardClient is the client API for Card service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type CardClient interface {
	CreateClientV1(ctx context.Context, in *CreateClientRequestV1, opts ...grpc.CallOption) (*CreateClientResponseV1, error)
	GetCardTokensV1(ctx context.Context, in *GetCardTokensRequestV1, opts ...grpc.CallOption) (*GetCardTokensResponseV1, error)
	GetOneClickPayInCardsV1(ctx context.Context, in *GetOneClickPayInCardsRequestV1, opts ...grpc.CallOption) (*GetOneClickPayInCardsResponseV1, error)
	GetPanByCardIdV1(ctx context.Context, in *GetPanByCardIdRequestV1, opts ...grpc.CallOption) (*GetPanResponseV1, error)
	GetPanByHashedIdV1(ctx context.Context, in *GetPanByHashedIdRequestV1, opts ...grpc.CallOption) (*GetPanResponseV1, error)
	GetOneClickPayOutCardsV1(ctx context.Context, in *GetOneClickPayOutCardsRequestV1, opts ...grpc.CallOption) (*GetOneClickPayOutCardsResponseV1, error)
	GetEncryptedCardToken(ctx context.Context, in *GetEncryptedCardRequestV1, opts ...grpc.CallOption) (*GetEncryptedCardResponseV1, error)
	GetClientListByVerification(ctx context.Context, in *GetClientListByVerificationRequestV1, opts ...grpc.CallOption) (*GetClientListByProjectClientResponseV1, error)
	GetClientListByProjectClient(ctx context.Context, in *GetClientListByProjectClientRequestV1, opts ...grpc.CallOption) (*GetClientListByProjectClientResponseV1, error)
	CheckClientActiveness(ctx context.Context, in *CheckClientActivenessRequestV1, opts ...grpc.CallOption) (*emptypb.Empty, error)
	GetCardByPan(ctx context.Context, in *GetCardByPanRequestV1, opts ...grpc.CallOption) (*GetCardByPanResponseV1, error)
	DecryptPayInCard(ctx context.Context, in *DecryptPayInRequest, opts ...grpc.CallOption) (*DecryptPayInResponse, error)
	DecryptPayOutCard(ctx context.Context, in *DecryptPayOutRequest, opts ...grpc.CallOption) (*DecryptPayOutResponse, error)
	ReEncryptCard(ctx context.Context, in *ReEncryptCardRequest, opts ...grpc.CallOption) (*ReEncryptCardResponse, error)
	GetPanInfoByProjectId(ctx context.Context, in *GetPanInfoByProjectIdRequest, opts ...grpc.CallOption) (*GetPanInfoByProjectIdResponse, error)
	// jobs
	NewKey(ctx context.Context, in *emptypb.Empty, opts ...grpc.CallOption) (*emptypb.Empty, error)
	RotateCardKeys(ctx context.Context, in *emptypb.Empty, opts ...grpc.CallOption) (*emptypb.Empty, error)
	CheckExpireCards(ctx context.Context, in *emptypb.Empty, opts ...grpc.CallOption) (*emptypb.Empty, error)
	CreateNewHashKey(ctx context.Context, in *emptypb.Empty, opts ...grpc.CallOption) (*emptypb.Empty, error)
	RotateHashKeys(ctx context.Context, in *emptypb.Empty, opts ...grpc.CallOption) (*emptypb.Empty, error)
}

type cardClient struct {
	cc grpc.ClientConnInterface
}

func NewCardClient(cc grpc.ClientConnInterface) CardClient {
	return &cardClient{cc}
}

func (c *cardClient) CreateClientV1(ctx context.Context, in *CreateClientRequestV1, opts ...grpc.CallOption) (*CreateClientResponseV1, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(CreateClientResponseV1)
	err := c.cc.Invoke(ctx, Card_CreateClientV1_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *cardClient) GetCardTokensV1(ctx context.Context, in *GetCardTokensRequestV1, opts ...grpc.CallOption) (*GetCardTokensResponseV1, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetCardTokensResponseV1)
	err := c.cc.Invoke(ctx, Card_GetCardTokensV1_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *cardClient) GetOneClickPayInCardsV1(ctx context.Context, in *GetOneClickPayInCardsRequestV1, opts ...grpc.CallOption) (*GetOneClickPayInCardsResponseV1, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetOneClickPayInCardsResponseV1)
	err := c.cc.Invoke(ctx, Card_GetOneClickPayInCardsV1_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *cardClient) GetPanByCardIdV1(ctx context.Context, in *GetPanByCardIdRequestV1, opts ...grpc.CallOption) (*GetPanResponseV1, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetPanResponseV1)
	err := c.cc.Invoke(ctx, Card_GetPanByCardIdV1_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *cardClient) GetPanByHashedIdV1(ctx context.Context, in *GetPanByHashedIdRequestV1, opts ...grpc.CallOption) (*GetPanResponseV1, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetPanResponseV1)
	err := c.cc.Invoke(ctx, Card_GetPanByHashedIdV1_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *cardClient) GetOneClickPayOutCardsV1(ctx context.Context, in *GetOneClickPayOutCardsRequestV1, opts ...grpc.CallOption) (*GetOneClickPayOutCardsResponseV1, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetOneClickPayOutCardsResponseV1)
	err := c.cc.Invoke(ctx, Card_GetOneClickPayOutCardsV1_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *cardClient) GetEncryptedCardToken(ctx context.Context, in *GetEncryptedCardRequestV1, opts ...grpc.CallOption) (*GetEncryptedCardResponseV1, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetEncryptedCardResponseV1)
	err := c.cc.Invoke(ctx, Card_GetEncryptedCardToken_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *cardClient) GetClientListByVerification(ctx context.Context, in *GetClientListByVerificationRequestV1, opts ...grpc.CallOption) (*GetClientListByProjectClientResponseV1, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetClientListByProjectClientResponseV1)
	err := c.cc.Invoke(ctx, Card_GetClientListByVerification_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *cardClient) GetClientListByProjectClient(ctx context.Context, in *GetClientListByProjectClientRequestV1, opts ...grpc.CallOption) (*GetClientListByProjectClientResponseV1, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetClientListByProjectClientResponseV1)
	err := c.cc.Invoke(ctx, Card_GetClientListByProjectClient_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *cardClient) CheckClientActiveness(ctx context.Context, in *CheckClientActivenessRequestV1, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, Card_CheckClientActiveness_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *cardClient) GetCardByPan(ctx context.Context, in *GetCardByPanRequestV1, opts ...grpc.CallOption) (*GetCardByPanResponseV1, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetCardByPanResponseV1)
	err := c.cc.Invoke(ctx, Card_GetCardByPan_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *cardClient) DecryptPayInCard(ctx context.Context, in *DecryptPayInRequest, opts ...grpc.CallOption) (*DecryptPayInResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(DecryptPayInResponse)
	err := c.cc.Invoke(ctx, Card_DecryptPayInCard_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *cardClient) DecryptPayOutCard(ctx context.Context, in *DecryptPayOutRequest, opts ...grpc.CallOption) (*DecryptPayOutResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(DecryptPayOutResponse)
	err := c.cc.Invoke(ctx, Card_DecryptPayOutCard_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *cardClient) ReEncryptCard(ctx context.Context, in *ReEncryptCardRequest, opts ...grpc.CallOption) (*ReEncryptCardResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ReEncryptCardResponse)
	err := c.cc.Invoke(ctx, Card_ReEncryptCard_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *cardClient) GetPanInfoByProjectId(ctx context.Context, in *GetPanInfoByProjectIdRequest, opts ...grpc.CallOption) (*GetPanInfoByProjectIdResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetPanInfoByProjectIdResponse)
	err := c.cc.Invoke(ctx, Card_GetPanInfoByProjectId_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *cardClient) NewKey(ctx context.Context, in *emptypb.Empty, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, Card_NewKey_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *cardClient) RotateCardKeys(ctx context.Context, in *emptypb.Empty, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, Card_RotateCardKeys_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *cardClient) CheckExpireCards(ctx context.Context, in *emptypb.Empty, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, Card_CheckExpireCards_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *cardClient) CreateNewHashKey(ctx context.Context, in *emptypb.Empty, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, Card_CreateNewHashKey_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *cardClient) RotateHashKeys(ctx context.Context, in *emptypb.Empty, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, Card_RotateHashKeys_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// CardServer is the server API for Card service.
// All implementations must embed UnimplementedCardServer
// for forward compatibility.
type CardServer interface {
	CreateClientV1(context.Context, *CreateClientRequestV1) (*CreateClientResponseV1, error)
	GetCardTokensV1(context.Context, *GetCardTokensRequestV1) (*GetCardTokensResponseV1, error)
	GetOneClickPayInCardsV1(context.Context, *GetOneClickPayInCardsRequestV1) (*GetOneClickPayInCardsResponseV1, error)
	GetPanByCardIdV1(context.Context, *GetPanByCardIdRequestV1) (*GetPanResponseV1, error)
	GetPanByHashedIdV1(context.Context, *GetPanByHashedIdRequestV1) (*GetPanResponseV1, error)
	GetOneClickPayOutCardsV1(context.Context, *GetOneClickPayOutCardsRequestV1) (*GetOneClickPayOutCardsResponseV1, error)
	GetEncryptedCardToken(context.Context, *GetEncryptedCardRequestV1) (*GetEncryptedCardResponseV1, error)
	GetClientListByVerification(context.Context, *GetClientListByVerificationRequestV1) (*GetClientListByProjectClientResponseV1, error)
	GetClientListByProjectClient(context.Context, *GetClientListByProjectClientRequestV1) (*GetClientListByProjectClientResponseV1, error)
	CheckClientActiveness(context.Context, *CheckClientActivenessRequestV1) (*emptypb.Empty, error)
	GetCardByPan(context.Context, *GetCardByPanRequestV1) (*GetCardByPanResponseV1, error)
	DecryptPayInCard(context.Context, *DecryptPayInRequest) (*DecryptPayInResponse, error)
	DecryptPayOutCard(context.Context, *DecryptPayOutRequest) (*DecryptPayOutResponse, error)
	ReEncryptCard(context.Context, *ReEncryptCardRequest) (*ReEncryptCardResponse, error)
	GetPanInfoByProjectId(context.Context, *GetPanInfoByProjectIdRequest) (*GetPanInfoByProjectIdResponse, error)
	// jobs
	NewKey(context.Context, *emptypb.Empty) (*emptypb.Empty, error)
	RotateCardKeys(context.Context, *emptypb.Empty) (*emptypb.Empty, error)
	CheckExpireCards(context.Context, *emptypb.Empty) (*emptypb.Empty, error)
	CreateNewHashKey(context.Context, *emptypb.Empty) (*emptypb.Empty, error)
	RotateHashKeys(context.Context, *emptypb.Empty) (*emptypb.Empty, error)
	mustEmbedUnimplementedCardServer()
}

// UnimplementedCardServer must be embedded to have
// forward compatible implementations.
//
// NOTE: this should be embedded by value instead of pointer to avoid a nil
// pointer dereference when methods are called.
type UnimplementedCardServer struct{}

func (UnimplementedCardServer) CreateClientV1(context.Context, *CreateClientRequestV1) (*CreateClientResponseV1, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateClientV1 not implemented")
}
func (UnimplementedCardServer) GetCardTokensV1(context.Context, *GetCardTokensRequestV1) (*GetCardTokensResponseV1, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetCardTokensV1 not implemented")
}
func (UnimplementedCardServer) GetOneClickPayInCardsV1(context.Context, *GetOneClickPayInCardsRequestV1) (*GetOneClickPayInCardsResponseV1, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetOneClickPayInCardsV1 not implemented")
}
func (UnimplementedCardServer) GetPanByCardIdV1(context.Context, *GetPanByCardIdRequestV1) (*GetPanResponseV1, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetPanByCardIdV1 not implemented")
}
func (UnimplementedCardServer) GetPanByHashedIdV1(context.Context, *GetPanByHashedIdRequestV1) (*GetPanResponseV1, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetPanByHashedIdV1 not implemented")
}
func (UnimplementedCardServer) GetOneClickPayOutCardsV1(context.Context, *GetOneClickPayOutCardsRequestV1) (*GetOneClickPayOutCardsResponseV1, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetOneClickPayOutCardsV1 not implemented")
}
func (UnimplementedCardServer) GetEncryptedCardToken(context.Context, *GetEncryptedCardRequestV1) (*GetEncryptedCardResponseV1, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetEncryptedCardToken not implemented")
}
func (UnimplementedCardServer) GetClientListByVerification(context.Context, *GetClientListByVerificationRequestV1) (*GetClientListByProjectClientResponseV1, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetClientListByVerification not implemented")
}
func (UnimplementedCardServer) GetClientListByProjectClient(context.Context, *GetClientListByProjectClientRequestV1) (*GetClientListByProjectClientResponseV1, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetClientListByProjectClient not implemented")
}
func (UnimplementedCardServer) CheckClientActiveness(context.Context, *CheckClientActivenessRequestV1) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CheckClientActiveness not implemented")
}
func (UnimplementedCardServer) GetCardByPan(context.Context, *GetCardByPanRequestV1) (*GetCardByPanResponseV1, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetCardByPan not implemented")
}
func (UnimplementedCardServer) DecryptPayInCard(context.Context, *DecryptPayInRequest) (*DecryptPayInResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DecryptPayInCard not implemented")
}
func (UnimplementedCardServer) DecryptPayOutCard(context.Context, *DecryptPayOutRequest) (*DecryptPayOutResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DecryptPayOutCard not implemented")
}
func (UnimplementedCardServer) ReEncryptCard(context.Context, *ReEncryptCardRequest) (*ReEncryptCardResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ReEncryptCard not implemented")
}
func (UnimplementedCardServer) GetPanInfoByProjectId(context.Context, *GetPanInfoByProjectIdRequest) (*GetPanInfoByProjectIdResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetPanInfoByProjectId not implemented")
}
func (UnimplementedCardServer) NewKey(context.Context, *emptypb.Empty) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method NewKey not implemented")
}
func (UnimplementedCardServer) RotateCardKeys(context.Context, *emptypb.Empty) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method RotateCardKeys not implemented")
}
func (UnimplementedCardServer) CheckExpireCards(context.Context, *emptypb.Empty) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CheckExpireCards not implemented")
}
func (UnimplementedCardServer) CreateNewHashKey(context.Context, *emptypb.Empty) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateNewHashKey not implemented")
}
func (UnimplementedCardServer) RotateHashKeys(context.Context, *emptypb.Empty) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method RotateHashKeys not implemented")
}
func (UnimplementedCardServer) mustEmbedUnimplementedCardServer() {}
func (UnimplementedCardServer) testEmbeddedByValue()              {}

// UnsafeCardServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to CardServer will
// result in compilation errors.
type UnsafeCardServer interface {
	mustEmbedUnimplementedCardServer()
}

func RegisterCardServer(s grpc.ServiceRegistrar, srv CardServer) {
	// If the following call pancis, it indicates UnimplementedCardServer was
	// embedded by pointer and is nil.  This will cause panics if an
	// unimplemented method is ever invoked, so we test this at initialization
	// time to prevent it from happening at runtime later due to I/O.
	if t, ok := srv.(interface{ testEmbeddedByValue() }); ok {
		t.testEmbeddedByValue()
	}
	s.RegisterService(&Card_ServiceDesc, srv)
}

func _Card_CreateClientV1_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateClientRequestV1)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CardServer).CreateClientV1(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Card_CreateClientV1_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CardServer).CreateClientV1(ctx, req.(*CreateClientRequestV1))
	}
	return interceptor(ctx, in, info, handler)
}

func _Card_GetCardTokensV1_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetCardTokensRequestV1)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CardServer).GetCardTokensV1(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Card_GetCardTokensV1_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CardServer).GetCardTokensV1(ctx, req.(*GetCardTokensRequestV1))
	}
	return interceptor(ctx, in, info, handler)
}

func _Card_GetOneClickPayInCardsV1_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetOneClickPayInCardsRequestV1)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CardServer).GetOneClickPayInCardsV1(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Card_GetOneClickPayInCardsV1_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CardServer).GetOneClickPayInCardsV1(ctx, req.(*GetOneClickPayInCardsRequestV1))
	}
	return interceptor(ctx, in, info, handler)
}

func _Card_GetPanByCardIdV1_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetPanByCardIdRequestV1)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CardServer).GetPanByCardIdV1(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Card_GetPanByCardIdV1_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CardServer).GetPanByCardIdV1(ctx, req.(*GetPanByCardIdRequestV1))
	}
	return interceptor(ctx, in, info, handler)
}

func _Card_GetPanByHashedIdV1_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetPanByHashedIdRequestV1)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CardServer).GetPanByHashedIdV1(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Card_GetPanByHashedIdV1_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CardServer).GetPanByHashedIdV1(ctx, req.(*GetPanByHashedIdRequestV1))
	}
	return interceptor(ctx, in, info, handler)
}

func _Card_GetOneClickPayOutCardsV1_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetOneClickPayOutCardsRequestV1)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CardServer).GetOneClickPayOutCardsV1(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Card_GetOneClickPayOutCardsV1_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CardServer).GetOneClickPayOutCardsV1(ctx, req.(*GetOneClickPayOutCardsRequestV1))
	}
	return interceptor(ctx, in, info, handler)
}

func _Card_GetEncryptedCardToken_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetEncryptedCardRequestV1)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CardServer).GetEncryptedCardToken(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Card_GetEncryptedCardToken_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CardServer).GetEncryptedCardToken(ctx, req.(*GetEncryptedCardRequestV1))
	}
	return interceptor(ctx, in, info, handler)
}

func _Card_GetClientListByVerification_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetClientListByVerificationRequestV1)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CardServer).GetClientListByVerification(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Card_GetClientListByVerification_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CardServer).GetClientListByVerification(ctx, req.(*GetClientListByVerificationRequestV1))
	}
	return interceptor(ctx, in, info, handler)
}

func _Card_GetClientListByProjectClient_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetClientListByProjectClientRequestV1)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CardServer).GetClientListByProjectClient(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Card_GetClientListByProjectClient_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CardServer).GetClientListByProjectClient(ctx, req.(*GetClientListByProjectClientRequestV1))
	}
	return interceptor(ctx, in, info, handler)
}

func _Card_CheckClientActiveness_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CheckClientActivenessRequestV1)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CardServer).CheckClientActiveness(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Card_CheckClientActiveness_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CardServer).CheckClientActiveness(ctx, req.(*CheckClientActivenessRequestV1))
	}
	return interceptor(ctx, in, info, handler)
}

func _Card_GetCardByPan_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetCardByPanRequestV1)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CardServer).GetCardByPan(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Card_GetCardByPan_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CardServer).GetCardByPan(ctx, req.(*GetCardByPanRequestV1))
	}
	return interceptor(ctx, in, info, handler)
}

func _Card_DecryptPayInCard_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DecryptPayInRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CardServer).DecryptPayInCard(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Card_DecryptPayInCard_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CardServer).DecryptPayInCard(ctx, req.(*DecryptPayInRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Card_DecryptPayOutCard_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DecryptPayOutRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CardServer).DecryptPayOutCard(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Card_DecryptPayOutCard_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CardServer).DecryptPayOutCard(ctx, req.(*DecryptPayOutRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Card_ReEncryptCard_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ReEncryptCardRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CardServer).ReEncryptCard(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Card_ReEncryptCard_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CardServer).ReEncryptCard(ctx, req.(*ReEncryptCardRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Card_GetPanInfoByProjectId_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetPanInfoByProjectIdRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CardServer).GetPanInfoByProjectId(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Card_GetPanInfoByProjectId_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CardServer).GetPanInfoByProjectId(ctx, req.(*GetPanInfoByProjectIdRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Card_NewKey_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(emptypb.Empty)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CardServer).NewKey(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Card_NewKey_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CardServer).NewKey(ctx, req.(*emptypb.Empty))
	}
	return interceptor(ctx, in, info, handler)
}

func _Card_RotateCardKeys_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(emptypb.Empty)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CardServer).RotateCardKeys(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Card_RotateCardKeys_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CardServer).RotateCardKeys(ctx, req.(*emptypb.Empty))
	}
	return interceptor(ctx, in, info, handler)
}

func _Card_CheckExpireCards_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(emptypb.Empty)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CardServer).CheckExpireCards(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Card_CheckExpireCards_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CardServer).CheckExpireCards(ctx, req.(*emptypb.Empty))
	}
	return interceptor(ctx, in, info, handler)
}

func _Card_CreateNewHashKey_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(emptypb.Empty)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CardServer).CreateNewHashKey(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Card_CreateNewHashKey_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CardServer).CreateNewHashKey(ctx, req.(*emptypb.Empty))
	}
	return interceptor(ctx, in, info, handler)
}

func _Card_RotateHashKeys_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(emptypb.Empty)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CardServer).RotateHashKeys(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Card_RotateHashKeys_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CardServer).RotateHashKeys(ctx, req.(*emptypb.Empty))
	}
	return interceptor(ctx, in, info, handler)
}

// Card_ServiceDesc is the grpc.ServiceDesc for Card service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var Card_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "processing.card.card.Card",
	HandlerType: (*CardServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "CreateClientV1",
			Handler:    _Card_CreateClientV1_Handler,
		},
		{
			MethodName: "GetCardTokensV1",
			Handler:    _Card_GetCardTokensV1_Handler,
		},
		{
			MethodName: "GetOneClickPayInCardsV1",
			Handler:    _Card_GetOneClickPayInCardsV1_Handler,
		},
		{
			MethodName: "GetPanByCardIdV1",
			Handler:    _Card_GetPanByCardIdV1_Handler,
		},
		{
			MethodName: "GetPanByHashedIdV1",
			Handler:    _Card_GetPanByHashedIdV1_Handler,
		},
		{
			MethodName: "GetOneClickPayOutCardsV1",
			Handler:    _Card_GetOneClickPayOutCardsV1_Handler,
		},
		{
			MethodName: "GetEncryptedCardToken",
			Handler:    _Card_GetEncryptedCardToken_Handler,
		},
		{
			MethodName: "GetClientListByVerification",
			Handler:    _Card_GetClientListByVerification_Handler,
		},
		{
			MethodName: "GetClientListByProjectClient",
			Handler:    _Card_GetClientListByProjectClient_Handler,
		},
		{
			MethodName: "CheckClientActiveness",
			Handler:    _Card_CheckClientActiveness_Handler,
		},
		{
			MethodName: "GetCardByPan",
			Handler:    _Card_GetCardByPan_Handler,
		},
		{
			MethodName: "DecryptPayInCard",
			Handler:    _Card_DecryptPayInCard_Handler,
		},
		{
			MethodName: "DecryptPayOutCard",
			Handler:    _Card_DecryptPayOutCard_Handler,
		},
		{
			MethodName: "ReEncryptCard",
			Handler:    _Card_ReEncryptCard_Handler,
		},
		{
			MethodName: "GetPanInfoByProjectId",
			Handler:    _Card_GetPanInfoByProjectId_Handler,
		},
		{
			MethodName: "NewKey",
			Handler:    _Card_NewKey_Handler,
		},
		{
			MethodName: "RotateCardKeys",
			Handler:    _Card_RotateCardKeys_Handler,
		},
		{
			MethodName: "CheckExpireCards",
			Handler:    _Card_CheckExpireCards_Handler,
		},
		{
			MethodName: "CreateNewHashKey",
			Handler:    _Card_CreateNewHashKey_Handler,
		},
		{
			MethodName: "RotateHashKeys",
			Handler:    _Card_RotateHashKeys_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "inner/processing/grpc/card.proto",
}
