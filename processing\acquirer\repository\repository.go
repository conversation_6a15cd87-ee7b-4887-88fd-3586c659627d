package repository

import (
	"git.local/sensitive/processing/acquirer/repository/database"

	"gorm.io/gorm"
)

//go:generate go run github.com/golang/mock/mockgen -source=repository.go -destination=repository_mock.go -package=repository

type Repositories struct {
	BankDB             database.Banker
	IpsDB              database.Ipser
	CountryDB          database.Countrier
	BankBinDB          database.BankBiner
	RuleDB             database.Ruler
	RuleActivatorDB    database.RuleActivator
	RuleWeighterDB     database.RuleWeighter
	RulePercentageDB   database.RulePercentager
	AcquirerDB         database.Acquirer
	BasicAcquirerDB    database.AcquirerBasicer
	TerminalDB         database.Terminaler
	TerminalBasicDB    database.TerminalBasicer
	TerminalTwoStageDB database.TerminalTwoStager
	IssuerDB           database.Issuer
	CountryBankDB      database.CountryBanker
	TerminalBalanceDB  database.TerminalBalancer
	TerminalProjectDB  database.TerminalProjecter
}

func NewRepositories(
	db *gorm.DB,
) *Repositories {
	return &Repositories{
		BankDB:             database.NewBankDB(db),
		IpsDB:              database.NewIpsDB(db),
		CountryDB:          database.NewCountryDB(db),
		BankBinDB:          database.NewBankBinDB(db),
		RuleDB:             database.NewRuleDB(db),
		RuleActivatorDB:    database.NewRuleActivatorDB(db),
		RuleWeighterDB:     database.NewRuleWeighterDB(db),
		RulePercentageDB:   database.NewRulePercentageDB(db),
		AcquirerDB:         database.NewAcquirerDB(db),
		BasicAcquirerDB:    database.NewBasicAcquirerDB(db),
		TerminalDB:         database.NewTerminalDB(db),
		TerminalBasicDB:    database.NewTerminalBasicDB(db),
		TerminalTwoStageDB: database.NewTerminalTwoStageDB(db),
		IssuerDB:           database.NewIssuerDB(db),
		CountryBankDB:      database.NewCountryBankDB(db),
		TerminalBalanceDB:  database.NewTerminalBalanceDB(db),
		TerminalProjectDB:  database.NewTerminalProjectDB(db),
	}
}
