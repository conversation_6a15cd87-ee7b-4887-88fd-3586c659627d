// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.5
// 	protoc        v5.29.3
// source: inner/processing/grpc/jusan_tokenize.proto

package grpc

import (
	_ "git.local/sensitive/mvp/pb"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	descriptorpb "google.golang.org/protobuf/types/descriptorpb"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type JusanResponseCodeTokenize int32

const (
	JusanResponseCodeTokenize_TokenizeServiceUnavailable         JusanResponseCodeTokenize = 0
	JusanResponseCodeTokenize_TokenizeIncorrectFieldOrder        JusanResponseCodeTokenize = 1
	JusanResponseCodeTokenize_TokenizeIncorrectAmount            JusanResponseCodeTokenize = 2
	JusanResponseCodeTokenize_TokenizeIncorrectCurrency          JusanResponseCodeTokenize = 3
	JusanResponseCodeTokenize_TokenizeUnavailableMPI             JusanResponseCodeTokenize = 4
	JusanResponseCodeTokenize_TokenizeUnavailableDb              JusanResponseCodeTokenize = 5
	JusanResponseCodeTokenize_TokenizeOperationForbidden         JusanResponseCodeTokenize = 6
	JusanResponseCodeTokenize_TokenizeOperationForbiddenByLaw    JusanResponseCodeTokenize = 7
	JusanResponseCodeTokenize_TokenizeRequestAlreadyCompleted    JusanResponseCodeTokenize = 8
	JusanResponseCodeTokenize_TokenizeIncorrectCardExpDate       JusanResponseCodeTokenize = 9
	JusanResponseCodeTokenize_TokenizeIncorrectFieldTerminal     JusanResponseCodeTokenize = 10
	JusanResponseCodeTokenize_TokenizeIncorrectSign              JusanResponseCodeTokenize = 11
	JusanResponseCodeTokenize_TokenizeCurrencyNotFound           JusanResponseCodeTokenize = 12
	JusanResponseCodeTokenize_TokenizeLimitExceeded              JusanResponseCodeTokenize = 13
	JusanResponseCodeTokenize_TokenizeEmptyField                 JusanResponseCodeTokenize = 14
	JusanResponseCodeTokenize_TokenizeFieldSizeLessSymbols       JusanResponseCodeTokenize = 15
	JusanResponseCodeTokenize_TokenizeFieldSizeMoreSymbols       JusanResponseCodeTokenize = 16
	JusanResponseCodeTokenize_TokenizeInvalidField               JusanResponseCodeTokenize = 17
	JusanResponseCodeTokenize_TokenizeMPIError3DS                JusanResponseCodeTokenize = 18
	JusanResponseCodeTokenize_TokenizeUnacceptableCardType       JusanResponseCodeTokenize = 19
	JusanResponseCodeTokenize_TokenizePaymentNotFound            JusanResponseCodeTokenize = 20
	JusanResponseCodeTokenize_TokenizeClientKeyNotFound          JusanResponseCodeTokenize = 21
	JusanResponseCodeTokenize_TokenizeForbidden                  JusanResponseCodeTokenize = 22
	JusanResponseCodeTokenize_TokenizeTokenNotFound              JusanResponseCodeTokenize = 23
	JusanResponseCodeTokenize_TokenizeIncorrectBlockAmount       JusanResponseCodeTokenize = 24
	JusanResponseCodeTokenize_TokenizeUnknownError               JusanResponseCodeTokenize = 25
	JusanResponseCodeTokenize_TokenizeServiceUnavailableTryLater JusanResponseCodeTokenize = 26
	JusanResponseCodeTokenize_TokenizeInvalidAmount              JusanResponseCodeTokenize = 27
	JusanResponseCodeTokenize_TokenizeServiceDbUnavailable       JusanResponseCodeTokenize = 28
	JusanResponseCodeTokenize_TokenizeIncorrectFieldMerchant     JusanResponseCodeTokenize = 29
	JusanResponseCodeTokenize_TokenizeMerchantNotFound           JusanResponseCodeTokenize = 30
	JusanResponseCodeTokenize_TokenizeOrderRequestNotFound       JusanResponseCodeTokenize = 31
	JusanResponseCodeTokenize_TokenizeInvalidSign                JusanResponseCodeTokenize = 32
	JusanResponseCodeTokenize_TokenizeIncorrectRefundSum         JusanResponseCodeTokenize = 33
	JusanResponseCodeTokenize_TokenizeIncorrectStatus            JusanResponseCodeTokenize = 34
	JusanResponseCodeTokenize_TokenizeIncorrectValue             JusanResponseCodeTokenize = 35
	JusanResponseCodeTokenize_TokenizeIncorrectTerminalStatus    JusanResponseCodeTokenize = 36
	JusanResponseCodeTokenize_TokenizeForbiddenOperation         JusanResponseCodeTokenize = 37
	JusanResponseCodeTokenize_TokenizeDuplicateDescription       JusanResponseCodeTokenize = 38
	JusanResponseCodeTokenize_TokenizeRefundError                JusanResponseCodeTokenize = 39
	JusanResponseCodeTokenize_TokenizePayError                   JusanResponseCodeTokenize = 40
	JusanResponseCodeTokenize_TokenizePaymentExpired             JusanResponseCodeTokenize = 41
	JusanResponseCodeTokenize_TokenizeTransactionHandleError     JusanResponseCodeTokenize = 43
)

// Enum value maps for JusanResponseCodeTokenize.
var (
	JusanResponseCodeTokenize_name = map[int32]string{
		0:  "TokenizeServiceUnavailable",
		1:  "TokenizeIncorrectFieldOrder",
		2:  "TokenizeIncorrectAmount",
		3:  "TokenizeIncorrectCurrency",
		4:  "TokenizeUnavailableMPI",
		5:  "TokenizeUnavailableDb",
		6:  "TokenizeOperationForbidden",
		7:  "TokenizeOperationForbiddenByLaw",
		8:  "TokenizeRequestAlreadyCompleted",
		9:  "TokenizeIncorrectCardExpDate",
		10: "TokenizeIncorrectFieldTerminal",
		11: "TokenizeIncorrectSign",
		12: "TokenizeCurrencyNotFound",
		13: "TokenizeLimitExceeded",
		14: "TokenizeEmptyField",
		15: "TokenizeFieldSizeLessSymbols",
		16: "TokenizeFieldSizeMoreSymbols",
		17: "TokenizeInvalidField",
		18: "TokenizeMPIError3DS",
		19: "TokenizeUnacceptableCardType",
		20: "TokenizePaymentNotFound",
		21: "TokenizeClientKeyNotFound",
		22: "TokenizeForbidden",
		23: "TokenizeTokenNotFound",
		24: "TokenizeIncorrectBlockAmount",
		25: "TokenizeUnknownError",
		26: "TokenizeServiceUnavailableTryLater",
		27: "TokenizeInvalidAmount",
		28: "TokenizeServiceDbUnavailable",
		29: "TokenizeIncorrectFieldMerchant",
		30: "TokenizeMerchantNotFound",
		31: "TokenizeOrderRequestNotFound",
		32: "TokenizeInvalidSign",
		33: "TokenizeIncorrectRefundSum",
		34: "TokenizeIncorrectStatus",
		35: "TokenizeIncorrectValue",
		36: "TokenizeIncorrectTerminalStatus",
		37: "TokenizeForbiddenOperation",
		38: "TokenizeDuplicateDescription",
		39: "TokenizeRefundError",
		40: "TokenizePayError",
		41: "TokenizePaymentExpired",
		43: "TokenizeTransactionHandleError",
	}
	JusanResponseCodeTokenize_value = map[string]int32{
		"TokenizeServiceUnavailable":         0,
		"TokenizeIncorrectFieldOrder":        1,
		"TokenizeIncorrectAmount":            2,
		"TokenizeIncorrectCurrency":          3,
		"TokenizeUnavailableMPI":             4,
		"TokenizeUnavailableDb":              5,
		"TokenizeOperationForbidden":         6,
		"TokenizeOperationForbiddenByLaw":    7,
		"TokenizeRequestAlreadyCompleted":    8,
		"TokenizeIncorrectCardExpDate":       9,
		"TokenizeIncorrectFieldTerminal":     10,
		"TokenizeIncorrectSign":              11,
		"TokenizeCurrencyNotFound":           12,
		"TokenizeLimitExceeded":              13,
		"TokenizeEmptyField":                 14,
		"TokenizeFieldSizeLessSymbols":       15,
		"TokenizeFieldSizeMoreSymbols":       16,
		"TokenizeInvalidField":               17,
		"TokenizeMPIError3DS":                18,
		"TokenizeUnacceptableCardType":       19,
		"TokenizePaymentNotFound":            20,
		"TokenizeClientKeyNotFound":          21,
		"TokenizeForbidden":                  22,
		"TokenizeTokenNotFound":              23,
		"TokenizeIncorrectBlockAmount":       24,
		"TokenizeUnknownError":               25,
		"TokenizeServiceUnavailableTryLater": 26,
		"TokenizeInvalidAmount":              27,
		"TokenizeServiceDbUnavailable":       28,
		"TokenizeIncorrectFieldMerchant":     29,
		"TokenizeMerchantNotFound":           30,
		"TokenizeOrderRequestNotFound":       31,
		"TokenizeInvalidSign":                32,
		"TokenizeIncorrectRefundSum":         33,
		"TokenizeIncorrectStatus":            34,
		"TokenizeIncorrectValue":             35,
		"TokenizeIncorrectTerminalStatus":    36,
		"TokenizeForbiddenOperation":         37,
		"TokenizeDuplicateDescription":       38,
		"TokenizeRefundError":                39,
		"TokenizePayError":                   40,
		"TokenizePaymentExpired":             41,
		"TokenizeTransactionHandleError":     43,
	}
)

func (x JusanResponseCodeTokenize) Enum() *JusanResponseCodeTokenize {
	p := new(JusanResponseCodeTokenize)
	*p = x
	return p
}

func (x JusanResponseCodeTokenize) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (JusanResponseCodeTokenize) Descriptor() protoreflect.EnumDescriptor {
	return file_inner_processing_grpc_jusan_tokenize_proto_enumTypes[0].Descriptor()
}

func (JusanResponseCodeTokenize) Type() protoreflect.EnumType {
	return &file_inner_processing_grpc_jusan_tokenize_proto_enumTypes[0]
}

func (x JusanResponseCodeTokenize) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use JusanResponseCodeTokenize.Descriptor instead.
func (JusanResponseCodeTokenize) EnumDescriptor() ([]byte, []int) {
	return file_inner_processing_grpc_jusan_tokenize_proto_rawDescGZIP(), []int{0}
}

type JusanResponseCodeTokenizeRef struct {
	state             protoimpl.MessageState `protogen:"open.v1"`
	Code              *string                `protobuf:"bytes,1,opt,name=code" json:"code,omitempty"`
	Description       *string                `protobuf:"bytes,2,opt,name=description" json:"description,omitempty"`
	TransactionStatus *EnumTransactionStatus `protobuf:"varint,3,opt,name=transaction_status,json=transactionStatus,enum=processing.transaction.transaction_status.EnumTransactionStatus" json:"transaction_status,omitempty"`
	IntegrationError  *IntegrationError      `protobuf:"varint,4,opt,name=integration_error,json=integrationError,enum=processing.integration.integration.IntegrationError" json:"integration_error,omitempty"`
	unknownFields     protoimpl.UnknownFields
	sizeCache         protoimpl.SizeCache
}

func (x *JusanResponseCodeTokenizeRef) Reset() {
	*x = JusanResponseCodeTokenizeRef{}
	mi := &file_inner_processing_grpc_jusan_tokenize_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *JusanResponseCodeTokenizeRef) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*JusanResponseCodeTokenizeRef) ProtoMessage() {}

func (x *JusanResponseCodeTokenizeRef) ProtoReflect() protoreflect.Message {
	mi := &file_inner_processing_grpc_jusan_tokenize_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use JusanResponseCodeTokenizeRef.ProtoReflect.Descriptor instead.
func (*JusanResponseCodeTokenizeRef) Descriptor() ([]byte, []int) {
	return file_inner_processing_grpc_jusan_tokenize_proto_rawDescGZIP(), []int{0}
}

func (x *JusanResponseCodeTokenizeRef) GetCode() string {
	if x != nil && x.Code != nil {
		return *x.Code
	}
	return ""
}

func (x *JusanResponseCodeTokenizeRef) GetDescription() string {
	if x != nil && x.Description != nil {
		return *x.Description
	}
	return ""
}

func (x *JusanResponseCodeTokenizeRef) GetTransactionStatus() EnumTransactionStatus {
	if x != nil && x.TransactionStatus != nil {
		return *x.TransactionStatus
	}
	return EnumTransactionStatus_Unknown
}

func (x *JusanResponseCodeTokenizeRef) GetIntegrationError() IntegrationError {
	if x != nil && x.IntegrationError != nil {
		return *x.IntegrationError
	}
	return IntegrationError_None
}

var file_inner_processing_grpc_jusan_tokenize_proto_extTypes = []protoimpl.ExtensionInfo{
	{
		ExtendedType:  (*descriptorpb.EnumValueOptions)(nil),
		ExtensionType: (*JusanResponseCodeTokenizeRef)(nil),
		Field:         100113,
		Name:          "processing.jusan_tokenize.jusan_tokenize.jusan_response_code_tokenize_value",
		Tag:           "bytes,100113,opt,name=jusan_response_code_tokenize_value",
		Filename:      "inner/processing/grpc/jusan_tokenize.proto",
	},
	{
		ExtendedType:  (*descriptorpb.EnumOptions)(nil),
		ExtensionType: (*JusanResponseCodeTokenizeRef)(nil),
		Field:         100114,
		Name:          "processing.jusan_tokenize.jusan_tokenize.default_jusan_response_code_tokenize_value",
		Tag:           "bytes,100114,opt,name=default_jusan_response_code_tokenize_value",
		Filename:      "inner/processing/grpc/jusan_tokenize.proto",
	},
}

// Extension fields to descriptorpb.EnumValueOptions.
var (
	// optional processing.jusan_tokenize.jusan_tokenize.JusanResponseCodeTokenizeRef jusan_response_code_tokenize_value = 100113;
	E_JusanResponseCodeTokenizeValue = &file_inner_processing_grpc_jusan_tokenize_proto_extTypes[0]
)

// Extension fields to descriptorpb.EnumOptions.
var (
	// optional processing.jusan_tokenize.jusan_tokenize.JusanResponseCodeTokenizeRef default_jusan_response_code_tokenize_value = 100114;
	E_DefaultJusanResponseCodeTokenizeValue = &file_inner_processing_grpc_jusan_tokenize_proto_extTypes[1]
)

var File_inner_processing_grpc_jusan_tokenize_proto protoreflect.FileDescriptor

var file_inner_processing_grpc_jusan_tokenize_proto_rawDesc = string([]byte{
	0x0a, 0x2a, 0x69, 0x6e, 0x6e, 0x65, 0x72, 0x2f, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69,
	0x6e, 0x67, 0x2f, 0x67, 0x72, 0x70, 0x63, 0x2f, 0x6a, 0x75, 0x73, 0x61, 0x6e, 0x5f, 0x74, 0x6f,
	0x6b, 0x65, 0x6e, 0x69, 0x7a, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x28, 0x70, 0x72,
	0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x2e, 0x6a, 0x75, 0x73, 0x61, 0x6e, 0x5f, 0x74,
	0x6f, 0x6b, 0x65, 0x6e, 0x69, 0x7a, 0x65, 0x2e, 0x6a, 0x75, 0x73, 0x61, 0x6e, 0x5f, 0x74, 0x6f,
	0x6b, 0x65, 0x6e, 0x69, 0x7a, 0x65, 0x1a, 0x2e, 0x69, 0x6e, 0x6e, 0x65, 0x72, 0x2f, 0x70, 0x72,
	0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x2f, 0x67, 0x72, 0x70, 0x63, 0x2f, 0x74, 0x72,
	0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x27, 0x69, 0x6e, 0x6e, 0x65, 0x72, 0x2f, 0x70, 0x72,
	0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x2f, 0x67, 0x72, 0x70, 0x63, 0x2f, 0x69, 0x6e,
	0x74, 0x65, 0x67, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a,
	0x14, 0x6d, 0x76, 0x70, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f, 0x72, 0x65, 0x66, 0x73, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x14, 0x6d, 0x76, 0x70, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x2f, 0x65, 0x6e, 0x75, 0x6d, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x20, 0x67, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x64, 0x65, 0x73,
	0x63, 0x72, 0x69, 0x70, 0x74, 0x6f, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0xa8, 0x02,
	0x0a, 0x1c, 0x4a, 0x75, 0x73, 0x61, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x43,
	0x6f, 0x64, 0x65, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x69, 0x7a, 0x65, 0x52, 0x65, 0x66, 0x12, 0x12,
	0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x63, 0x6f,
	0x64, 0x65, 0x12, 0x20, 0x0a, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f,
	0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70,
	0x74, 0x69, 0x6f, 0x6e, 0x12, 0x6f, 0x0a, 0x12, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74,
	0x69, 0x6f, 0x6e, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0e,
	0x32, 0x40, 0x2e, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x2e, 0x74, 0x72,
	0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61,
	0x63, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x2e, 0x45, 0x6e, 0x75,
	0x6d, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x52, 0x11, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x53,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x61, 0x0a, 0x11, 0x69, 0x6e, 0x74, 0x65, 0x67, 0x72, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0e,
	0x32, 0x34, 0x2e, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x2e, 0x69, 0x6e,
	0x74, 0x65, 0x67, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x69, 0x6e, 0x74, 0x65, 0x67, 0x72,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x49, 0x6e, 0x74, 0x65, 0x67, 0x72, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x52, 0x10, 0x69, 0x6e, 0x74, 0x65, 0x67, 0x72, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x2a, 0xf3, 0x28, 0x0a, 0x19, 0x4a, 0x75, 0x73,
	0x61, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x43, 0x6f, 0x64, 0x65, 0x54, 0x6f,
	0x6b, 0x65, 0x6e, 0x69, 0x7a, 0x65, 0x12, 0x89, 0x01, 0x0a, 0x1a, 0x54, 0x6f, 0x6b, 0x65, 0x6e,
	0x69, 0x7a, 0x65, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x55, 0x6e, 0x61, 0x76, 0x61, 0x69,
	0x6c, 0x61, 0x62, 0x6c, 0x65, 0x10, 0x00, 0x1a, 0x69, 0x8a, 0xf1, 0x30, 0x5d, 0x0a, 0x02, 0x31,
	0x31, 0x12, 0x53, 0xd0, 0xa1, 0xd0, 0xb5, 0xd1, 0x80, 0xd0, 0xb2, 0xd0, 0xb8, 0xd1, 0x81, 0x20,
	0xd0, 0xb2, 0xd1, 0x80, 0xd0, 0xb5, 0xd0, 0xbc, 0xd0, 0xb5, 0xd0, 0xbd, 0xd0, 0xbd, 0xd0, 0xbe,
	0x20, 0xd0, 0xbd, 0xd0, 0xb5, 0xd0, 0xb4, 0xd0, 0xbe, 0xd1, 0x81, 0xd1, 0x82, 0xd1, 0x83, 0xd0,
	0xbf, 0xd0, 0xb5, 0xd0, 0xbd, 0x2c, 0x20, 0xd0, 0xbf, 0xd0, 0xbe, 0xd0, 0xbf, 0xd1, 0x80, 0xd0,
	0xbe, 0xd0, 0xb1, 0xd1, 0x83, 0xd0, 0xb9, 0xd1, 0x82, 0xd0, 0xb5, 0x20, 0xd0, 0xbf, 0xd0, 0xbe,
	0xd0, 0xb7, 0xd0, 0xb6, 0xd0, 0xb5, 0x18, 0x05, 0x20, 0x6c, 0xca, 0xa1, 0xec, 0x8e, 0x02, 0x02,
	0x31, 0x31, 0x12, 0x73, 0x0a, 0x1b, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x69, 0x7a, 0x65, 0x49, 0x6e,
	0x63, 0x6f, 0x72, 0x72, 0x65, 0x63, 0x74, 0x46, 0x69, 0x65, 0x6c, 0x64, 0x4f, 0x72, 0x64, 0x65,
	0x72, 0x10, 0x01, 0x1a, 0x52, 0x8a, 0xf1, 0x30, 0x46, 0x0a, 0x02, 0x31, 0x32, 0x12, 0x3c, 0xd0,
	0x9d, 0xd0, 0xb5, 0xd0, 0xbf, 0xd1, 0x80, 0xd0, 0xb0, 0xd0, 0xb2, 0xd0, 0xb8, 0xd0, 0xbb, 0xd1,
	0x8c, 0xd0, 0xbd, 0xd0, 0xbe, 0xd0, 0xb5, 0x20, 0xd0, 0xb7, 0xd0, 0xbd, 0xd0, 0xb0, 0xd1, 0x87,
	0xd0, 0xb5, 0xd0, 0xbd, 0xd0, 0xb8, 0xd0, 0xb5, 0x20, 0xd0, 0xb2, 0x20, 0xd0, 0xbf, 0xd0, 0xbe,
	0xd0, 0xbb, 0xd0, 0xb5, 0x20, 0x4f, 0x52, 0x44, 0x45, 0x52, 0x3a, 0x18, 0x05, 0x20, 0x6a, 0xca,
	0xa1, 0xec, 0x8e, 0x02, 0x02, 0x31, 0x32, 0x12, 0x58, 0x0a, 0x17, 0x54, 0x6f, 0x6b, 0x65, 0x6e,
	0x69, 0x7a, 0x65, 0x49, 0x6e, 0x63, 0x6f, 0x72, 0x72, 0x65, 0x63, 0x74, 0x41, 0x6d, 0x6f, 0x75,
	0x6e, 0x74, 0x10, 0x02, 0x1a, 0x3b, 0x8a, 0xf1, 0x30, 0x2f, 0x0a, 0x02, 0x31, 0x33, 0x12, 0x25,
	0xd0, 0x9d, 0xd0, 0xb5, 0xd0, 0xbf, 0xd1, 0x80, 0xd0, 0xb0, 0xd0, 0xb2, 0xd0, 0xb8, 0xd0, 0xbb,
	0xd1, 0x8c, 0xd0, 0xbd, 0xd0, 0xb0, 0xd1, 0x8f, 0x20, 0xd1, 0x81, 0xd1, 0x83, 0xd0, 0xbc, 0xd0,
	0xbc, 0xd0, 0xb0, 0x3a, 0x20, 0x18, 0x05, 0x20, 0x6a, 0xca, 0xa1, 0xec, 0x8e, 0x02, 0x02, 0x31,
	0x33, 0x12, 0x5b, 0x0a, 0x19, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x69, 0x7a, 0x65, 0x49, 0x6e, 0x63,
	0x6f, 0x72, 0x72, 0x65, 0x63, 0x74, 0x43, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x63, 0x79, 0x10, 0x03,
	0x1a, 0x3c, 0x8a, 0xf1, 0x30, 0x30, 0x0a, 0x02, 0x31, 0x34, 0x12, 0x26, 0xd0, 0x9d, 0xd0, 0xb5,
	0xd0, 0xbf, 0xd1, 0x80, 0xd0, 0xb0, 0xd0, 0xb2, 0xd0, 0xb8, 0xd0, 0xbb, 0xd1, 0x8c, 0xd0, 0xbd,
	0xd0, 0xb0, 0xd1, 0x8f, 0x20, 0xd0, 0xb2, 0xd0, 0xb0, 0xd0, 0xbb, 0xd1, 0x8e, 0xd1, 0x82, 0xd0,
	0xb0, 0x3a, 0x18, 0x05, 0x20, 0x6a, 0xca, 0xa1, 0xec, 0x8e, 0x02, 0x02, 0x31, 0x34, 0x12, 0x89,
	0x01, 0x0a, 0x16, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x69, 0x7a, 0x65, 0x55, 0x6e, 0x61, 0x76, 0x61,
	0x69, 0x6c, 0x61, 0x62, 0x6c, 0x65, 0x4d, 0x50, 0x49, 0x10, 0x04, 0x1a, 0x6d, 0x8a, 0xf1, 0x30,
	0x61, 0x0a, 0x02, 0x31, 0x35, 0x12, 0x57, 0xd0, 0xa1, 0xd0, 0xb5, 0xd1, 0x80, 0xd0, 0xb2, 0xd0,
	0xb8, 0xd1, 0x81, 0x20, 0x4d, 0x50, 0x49, 0x20, 0xd0, 0xb2, 0xd1, 0x80, 0xd0, 0xb5, 0xd0, 0xbc,
	0xd0, 0xb5, 0xd0, 0xbd, 0xd0, 0xbd, 0xd0, 0xbe, 0x20, 0xd0, 0xbd, 0xd0, 0xb5, 0xd0, 0xb4, 0xd0,
	0xbe, 0xd1, 0x81, 0xd1, 0x82, 0xd1, 0x83, 0xd0, 0xbf, 0xd0, 0xb5, 0xd0, 0xbd, 0x2c, 0x20, 0xd0,
	0xbf, 0xd0, 0xbe, 0xd0, 0xbf, 0xd1, 0x80, 0xd0, 0xbe, 0xd0, 0xb1, 0xd1, 0x83, 0xd0, 0xb9, 0xd1,
	0x82, 0xd0, 0xb5, 0x20, 0xd0, 0xbf, 0xd0, 0xbe, 0xd0, 0xb7, 0xd0, 0xb6, 0xd0, 0xb5, 0x18, 0x05,
	0x20, 0x6c, 0xca, 0xa1, 0xec, 0x8e, 0x02, 0x02, 0x31, 0x35, 0x12, 0x87, 0x01, 0x0a, 0x15, 0x54,
	0x6f, 0x6b, 0x65, 0x6e, 0x69, 0x7a, 0x65, 0x55, 0x6e, 0x61, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62,
	0x6c, 0x65, 0x44, 0x62, 0x10, 0x05, 0x1a, 0x6c, 0x8a, 0xf1, 0x30, 0x60, 0x0a, 0x02, 0x31, 0x36,
	0x12, 0x56, 0xd0, 0xa1, 0xd0, 0xb5, 0xd1, 0x80, 0xd0, 0xb2, 0xd0, 0xb8, 0xd1, 0x81, 0x20, 0x44,
	0x62, 0x20, 0xd0, 0xb2, 0xd1, 0x80, 0xd0, 0xb5, 0xd0, 0xbc, 0xd0, 0xb5, 0xd0, 0xbd, 0xd0, 0xbd,
	0xd0, 0xbe, 0x20, 0xd0, 0xbd, 0xd0, 0xb5, 0xd0, 0xb4, 0xd0, 0xbe, 0xd1, 0x81, 0xd1, 0x82, 0xd1,
	0x83, 0xd0, 0xbf, 0xd0, 0xb5, 0xd0, 0xbd, 0x2c, 0x20, 0xd0, 0xbf, 0xd0, 0xbe, 0xd0, 0xbf, 0xd1,
	0x80, 0xd0, 0xbe, 0xd0, 0xb1, 0xd1, 0x83, 0xd0, 0xb9, 0xd1, 0x82, 0xd0, 0xb5, 0x20, 0xd0, 0xbf,
	0xd0, 0xbe, 0xd0, 0xb7, 0xd0, 0xb6, 0xd0, 0xb5, 0x18, 0x05, 0x20, 0x6c, 0xca, 0xa1, 0xec, 0x8e,
	0x02, 0x02, 0x31, 0x36, 0x12, 0x87, 0x01, 0x0a, 0x1a, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x69, 0x7a,
	0x65, 0x4f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x46, 0x6f, 0x72, 0x62, 0x69, 0x64,
	0x64, 0x65, 0x6e, 0x10, 0x06, 0x1a, 0x67, 0x8a, 0xf1, 0x30, 0x5a, 0x0a, 0x03, 0x31, 0x37, 0x31,
	0x12, 0x4f, 0xd0, 0x9a, 0xd0, 0xbe, 0xd0, 0xbc, 0xd0, 0xbc, 0xd0, 0xb5, 0xd1, 0x80, 0xd1, 0x81,
	0xd0, 0xb0, 0xd0, 0xbd, 0xd1, 0x82, 0xd1, 0x83, 0x20, 0xd0, 0xb7, 0xd0, 0xb0, 0xd0, 0xbf, 0xd1,
	0x80, 0xd0, 0xb5, 0xd1, 0x89, 0xd0, 0xb5, 0xd0, 0xbd, 0xd0, 0xbe, 0x20, 0xd0, 0xb2, 0xd1, 0x8b,
	0xd0, 0xbf, 0xd0, 0xbe, 0xd0, 0xbb, 0xd0, 0xbd, 0xd0, 0xb5, 0xd0, 0xbd, 0xd0, 0xb8, 0xd0, 0xb5,
	0x20, 0xd0, 0xbe, 0xd0, 0xbf, 0xd0, 0xb5, 0xd1, 0x80, 0xd0, 0xb0, 0xd1, 0x86, 0xd0, 0xb8, 0xd0,
	0xb9, 0x18, 0x05, 0x20, 0x6d, 0xca, 0xa1, 0xec, 0x8e, 0x02, 0x03, 0x31, 0x37, 0x31, 0x12, 0xce,
	0x01, 0x0a, 0x1f, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x69, 0x7a, 0x65, 0x4f, 0x70, 0x65, 0x72, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x46, 0x6f, 0x72, 0x62, 0x69, 0x64, 0x64, 0x65, 0x6e, 0x42, 0x79, 0x4c,
	0x61, 0x77, 0x10, 0x07, 0x1a, 0xa8, 0x01, 0x8a, 0xf1, 0x30, 0x99, 0x01, 0x0a, 0x04, 0x31, 0x37,
	0x32, 0x20, 0x12, 0x8c, 0x01, 0xd0, 0x9a, 0xd0, 0xbe, 0xd0, 0xbc, 0xd0, 0xbc, 0xd0, 0xb5, 0xd1,
	0x80, 0xd1, 0x81, 0xd0, 0xb0, 0xd0, 0xbd, 0xd1, 0x82, 0xd1, 0x83, 0x20, 0xd0, 0xb7, 0xd0, 0xb0,
	0xd0, 0xbf, 0xd1, 0x80, 0xd0, 0xb5, 0xd1, 0x89, 0xd0, 0xb5, 0xd0, 0xbd, 0xd0, 0xbe, 0x20, 0xd0,
	0xbf, 0xd1, 0x80, 0xd0, 0xbe, 0xd0, 0xb2, 0xd0, 0xb5, 0xd0, 0xb4, 0xd0, 0xb5, 0xd0, 0xbd, 0xd0,
	0xb8, 0xd0, 0xb5, 0x20, 0xd0, 0xbe, 0xd0, 0xbf, 0xd0, 0xb5, 0xd1, 0x80, 0xd0, 0xb0, 0xd1, 0x86,
	0xd0, 0xb8, 0xd0, 0xb9, 0x20, 0xd0, 0xb2, 0x20, 0xd1, 0x81, 0xd0, 0xbe, 0xd0, 0xbe, 0xd1, 0x82,
	0xd0, 0xb2, 0xd0, 0xb5, 0xd1, 0x82, 0xd1, 0x81, 0xd1, 0x82, 0xd0, 0xb2, 0xd0, 0xb8, 0xd0, 0xb8,
	0x20, 0xd1, 0x81, 0x20, 0xd0, 0x97, 0xd0, 0xb0, 0xd0, 0xba, 0xd0, 0xbe, 0xd0, 0xbd, 0xd0, 0xbe,
	0xd0, 0xbc, 0x20, 0xd0, 0xbe, 0x20, 0xd0, 0x9f, 0xd0, 0x9e, 0xd0, 0x94, 0x2f, 0xd0, 0xa4, 0xd0,
	0xa2, 0x18, 0x05, 0x20, 0x6d, 0xca, 0xa1, 0xec, 0x8e, 0x02, 0x04, 0x31, 0x37, 0x32, 0x20, 0x12,
	0x63, 0x0a, 0x1f, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x69, 0x7a, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x41, 0x6c, 0x72, 0x65, 0x61, 0x64, 0x79, 0x43, 0x6f, 0x6d, 0x70, 0x6c, 0x65, 0x74,
	0x65, 0x64, 0x10, 0x08, 0x1a, 0x3e, 0x8a, 0xf1, 0x30, 0x32, 0x0a, 0x02, 0x31, 0x38, 0x12, 0x28,
	0xd0, 0x97, 0xd0, 0xb0, 0xd0, 0xbf, 0xd1, 0x80, 0xd0, 0xbe, 0xd1, 0x81, 0x20, 0xd1, 0x83, 0xd0,
	0xb6, 0xd0, 0xb5, 0x20, 0xd0, 0xb2, 0xd1, 0x8b, 0xd0, 0xbf, 0xd0, 0xbe, 0xd0, 0xbb, 0xd0, 0xbd,
	0xd1, 0x8f, 0xd0, 0xbb, 0xd1, 0x81, 0xd1, 0x8f, 0x18, 0x05, 0x20, 0x6a, 0xca, 0xa1, 0xec, 0x8e,
	0x02, 0x02, 0x31, 0x38, 0x12, 0x7d, 0x0a, 0x1c, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x69, 0x7a, 0x65,
	0x49, 0x6e, 0x63, 0x6f, 0x72, 0x72, 0x65, 0x63, 0x74, 0x43, 0x61, 0x72, 0x64, 0x45, 0x78, 0x70,
	0x44, 0x61, 0x74, 0x65, 0x10, 0x09, 0x1a, 0x5b, 0x8a, 0xf1, 0x30, 0x4f, 0x0a, 0x02, 0x31, 0x39,
	0x12, 0x45, 0xd0, 0x9d, 0xd0, 0xb5, 0xd0, 0xbf, 0xd1, 0x80, 0xd0, 0xb0, 0xd0, 0xb2, 0xd0, 0xb8,
	0xd0, 0xbb, 0xd1, 0x8c, 0xd0, 0xbd, 0xd0, 0xb0, 0xd1, 0x8f, 0x20, 0xd0, 0xb4, 0xd0, 0xb0, 0xd1,
	0x82, 0xd0, 0xb0, 0x20, 0xd0, 0xb4, 0xd0, 0xb5, 0xd0, 0xb9, 0xd1, 0x81, 0xd1, 0x82, 0xd0, 0xb8,
	0xd1, 0x8f, 0x20, 0xd0, 0xba, 0xd0, 0xb0, 0xd1, 0x80, 0xd1, 0x82, 0xd1, 0x8b, 0x20, 0x28, 0x4d,
	0x4d, 0x2f, 0xd0, 0x93, 0xd0, 0x93, 0x29, 0x18, 0x05, 0x20, 0x75, 0xca, 0xa1, 0xec, 0x8e, 0x02,
	0x02, 0x31, 0x39, 0x12, 0x79, 0x0a, 0x1e, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x69, 0x7a, 0x65, 0x49,
	0x6e, 0x63, 0x6f, 0x72, 0x72, 0x65, 0x63, 0x74, 0x46, 0x69, 0x65, 0x6c, 0x64, 0x54, 0x65, 0x72,
	0x6d, 0x69, 0x6e, 0x61, 0x6c, 0x10, 0x0a, 0x1a, 0x55, 0x8a, 0xf1, 0x30, 0x49, 0x0a, 0x02, 0x32,
	0x30, 0x12, 0x3f, 0xd0, 0x9d, 0xd0, 0xb5, 0xd0, 0xbf, 0xd1, 0x80, 0xd0, 0xb0, 0xd0, 0xb2, 0xd0,
	0xb8, 0xd0, 0xbb, 0xd1, 0x8c, 0xd0, 0xbd, 0xd0, 0xbe, 0xd0, 0xb5, 0x20, 0xd0, 0xb7, 0xd0, 0xbd,
	0xd0, 0xb0, 0xd1, 0x87, 0xd0, 0xb5, 0xd0, 0xbd, 0xd0, 0xb8, 0xd0, 0xb5, 0x20, 0xd0, 0xb2, 0x20,
	0xd0, 0xbf, 0xd0, 0xbe, 0xd0, 0xbb, 0xd0, 0xb5, 0x20, 0x54, 0x45, 0x52, 0x4d, 0x49, 0x4e, 0x41,
	0x4c, 0x3a, 0x18, 0x05, 0x20, 0x6a, 0xca, 0xa1, 0xec, 0x8e, 0x02, 0x02, 0x32, 0x30, 0x12, 0x59,
	0x0a, 0x15, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x69, 0x7a, 0x65, 0x49, 0x6e, 0x63, 0x6f, 0x72, 0x72,
	0x65, 0x63, 0x74, 0x53, 0x69, 0x67, 0x6e, 0x10, 0x0b, 0x1a, 0x3e, 0x8a, 0xf1, 0x30, 0x32, 0x0a,
	0x02, 0x32, 0x31, 0x12, 0x28, 0xd0, 0x9d, 0xd0, 0xb5, 0xd0, 0xbf, 0xd1, 0x80, 0xd0, 0xb0, 0xd0,
	0xb2, 0xd0, 0xb8, 0xd0, 0xbb, 0xd1, 0x8c, 0xd0, 0xbd, 0xd0, 0xb0, 0xd1, 0x8f, 0x20, 0xd0, 0xbf,
	0xd0, 0xbe, 0xd0, 0xb4, 0xd0, 0xbf, 0xd0, 0xb8, 0xd1, 0x81, 0xd1, 0x8c, 0x21, 0x18, 0x05, 0x20,
	0x6a, 0xca, 0xa1, 0xec, 0x8e, 0x02, 0x02, 0x32, 0x31, 0x12, 0x5b, 0x0a, 0x18, 0x54, 0x6f, 0x6b,
	0x65, 0x6e, 0x69, 0x7a, 0x65, 0x43, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x63, 0x79, 0x4e, 0x6f, 0x74,
	0x46, 0x6f, 0x75, 0x6e, 0x64, 0x10, 0x0c, 0x1a, 0x3d, 0x8a, 0xf1, 0x30, 0x31, 0x0a, 0x02, 0x32,
	0x32, 0x12, 0x27, 0xd0, 0x9d, 0xd0, 0xb5, 0x20, 0xd0, 0xbd, 0xd0, 0xb0, 0xd0, 0xb9, 0xd0, 0xb4,
	0xd0, 0xb5, 0xd0, 0xbd, 0x20, 0xd0, 0xba, 0xd1, 0x83, 0xd1, 0x80, 0xd1, 0x81, 0x20, 0xd0, 0xb2,
	0xd0, 0xb0, 0xd0, 0xbb, 0xd1, 0x8e, 0xd1, 0x82, 0xd1, 0x8b, 0x18, 0x05, 0x20, 0x6a, 0xca, 0xa1,
	0xec, 0x8e, 0x02, 0x02, 0x32, 0x32, 0x12, 0x4d, 0x0a, 0x15, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x69,
	0x7a, 0x65, 0x4c, 0x69, 0x6d, 0x69, 0x74, 0x45, 0x78, 0x63, 0x65, 0x65, 0x64, 0x65, 0x64, 0x10,
	0x0d, 0x1a, 0x32, 0x8a, 0xf1, 0x30, 0x26, 0x0a, 0x02, 0x32, 0x33, 0x12, 0x1c, 0xd0, 0x9f, 0xd1,
	0x80, 0xd0, 0xb5, 0xd0, 0xb2, 0xd1, 0x8b, 0xd1, 0x88, 0xd0, 0xb5, 0xd0, 0xbd, 0x20, 0xd0, 0xbb,
	0xd0, 0xb8, 0xd0, 0xbc, 0xd0, 0xb8, 0xd1, 0x82, 0x21, 0x18, 0x05, 0x20, 0x67, 0xca, 0xa1, 0xec,
	0x8e, 0x02, 0x02, 0x32, 0x33, 0x12, 0x5e, 0x0a, 0x12, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x69, 0x7a,
	0x65, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x46, 0x69, 0x65, 0x6c, 0x64, 0x10, 0x0e, 0x1a, 0x46, 0x8a,
	0xf1, 0x30, 0x3a, 0x0a, 0x02, 0x32, 0x34, 0x12, 0x30, 0xd0, 0x9d, 0xd0, 0xb5, 0x20, 0xd1, 0x83,
	0xd0, 0xba, 0xd0, 0xb0, 0xd0, 0xb7, 0xd0, 0xb0, 0xd0, 0xbd, 0xd0, 0xbe, 0x20, 0xd0, 0xb7, 0xd0,
	0xbd, 0xd0, 0xb0, 0xd1, 0x87, 0xd0, 0xb5, 0xd0, 0xbd, 0xd0, 0xb8, 0xd0, 0xb5, 0x20, 0xd0, 0xb2,
	0x20, 0xd0, 0xbf, 0xd0, 0xbe, 0xd0, 0xbb, 0xd0, 0xb5, 0x18, 0x05, 0x20, 0x6a, 0xca, 0xa1, 0xec,
	0x8e, 0x02, 0x02, 0x32, 0x34, 0x12, 0x7f, 0x0a, 0x1c, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x69, 0x7a,
	0x65, 0x46, 0x69, 0x65, 0x6c, 0x64, 0x53, 0x69, 0x7a, 0x65, 0x4c, 0x65, 0x73, 0x73, 0x53, 0x79,
	0x6d, 0x62, 0x6f, 0x6c, 0x73, 0x10, 0x0f, 0x1a, 0x5d, 0x8a, 0xf1, 0x30, 0x51, 0x0a, 0x02, 0x32,
	0x35, 0x12, 0x47, 0xd0, 0xa0, 0xd0, 0xb0, 0xd0, 0xb7, 0xd0, 0xbc, 0xd0, 0xb5, 0xd1, 0x80, 0x20,
	0xd0, 0xb7, 0xd0, 0xbd, 0xd0, 0xb0, 0xd1, 0x87, 0xd0, 0xb5, 0xd0, 0xbd, 0xd0, 0xb8, 0xd1, 0x8f,
	0x20, 0xd0, 0xb2, 0x20, 0xd0, 0xbf, 0xd0, 0xbe, 0xd0, 0xbb, 0xd0, 0xb5, 0x20, 0xd0, 0xbc, 0xd0,
	0xb5, 0xd0, 0xbd, 0xd0, 0xb5, 0xd0, 0xb5, 0x20, 0xd1, 0x81, 0xd0, 0xb8, 0xd0, 0xbc, 0xd0, 0xbe,
	0xd0, 0xb2, 0xd0, 0xbe, 0xd0, 0xbb, 0xd0, 0xbe, 0xd0, 0xb2, 0x18, 0x05, 0x20, 0x6a, 0xca, 0xa1,
	0xec, 0x8e, 0x02, 0x02, 0x32, 0x35, 0x12, 0x81, 0x01, 0x0a, 0x1c, 0x54, 0x6f, 0x6b, 0x65, 0x6e,
	0x69, 0x7a, 0x65, 0x46, 0x69, 0x65, 0x6c, 0x64, 0x53, 0x69, 0x7a, 0x65, 0x4d, 0x6f, 0x72, 0x65,
	0x53, 0x79, 0x6d, 0x62, 0x6f, 0x6c, 0x73, 0x10, 0x10, 0x1a, 0x5f, 0x8a, 0xf1, 0x30, 0x53, 0x0a,
	0x02, 0x32, 0x36, 0x12, 0x49, 0xd0, 0xa0, 0xd0, 0xb0, 0xd0, 0xb7, 0xd0, 0xbc, 0xd0, 0xb5, 0xd1,
	0x80, 0x20, 0xd0, 0xb7, 0xd0, 0xbd, 0xd0, 0xb0, 0xd1, 0x87, 0xd0, 0xb5, 0xd0, 0xbd, 0xd0, 0xb8,
	0xd1, 0x8f, 0x20, 0xd0, 0xb2, 0x20, 0xd0, 0xbf, 0xd0, 0xbe, 0xd0, 0xbb, 0xd0, 0xb5, 0x20, 0xd0,
	0xb1, 0xd0, 0xbe, 0xd0, 0xbb, 0xd1, 0x8c, 0xd1, 0x88, 0xd0, 0xb5, 0x20, 0xd1, 0x81, 0xd0, 0xb8,
	0xd0, 0xbc, 0xd0, 0xbe, 0xd0, 0xb2, 0xd0, 0xbe, 0xd0, 0xbb, 0xd0, 0xbe, 0xd0, 0xb2, 0x18, 0x05,
	0x20, 0x6a, 0xca, 0xa1, 0xec, 0x8e, 0x02, 0x02, 0x32, 0x36, 0x12, 0x6c, 0x0a, 0x14, 0x54, 0x6f,
	0x6b, 0x65, 0x6e, 0x69, 0x7a, 0x65, 0x49, 0x6e, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x46, 0x69, 0x65,
	0x6c, 0x64, 0x10, 0x11, 0x1a, 0x52, 0x8a, 0xf1, 0x30, 0x46, 0x0a, 0x02, 0x32, 0x37, 0x12, 0x3c,
	0xd0, 0x92, 0xd0, 0xb2, 0xd0, 0xb5, 0xd0, 0xb4, 0xd0, 0xb8, 0xd1, 0x82, 0xd0, 0xb5, 0x20, 0xd0,
	0xb2, 0xd0, 0xb0, 0xd0, 0xbb, 0xd0, 0xb8, 0xd0, 0xb4, 0xd0, 0xbd, 0xd0, 0xbe, 0xd0, 0xb5, 0x20,
	0xd0, 0xb7, 0xd0, 0xbd, 0xd0, 0xb0, 0xd1, 0x87, 0xd0, 0xb5, 0xd0, 0xbd, 0xd0, 0xb8, 0xd0, 0xb5,
	0x20, 0xd0, 0xb2, 0x20, 0xd0, 0xbf, 0xd0, 0xbe, 0xd0, 0xbb, 0xd0, 0xb5, 0x18, 0x05, 0x20, 0x6a,
	0xca, 0xa1, 0xec, 0x8e, 0x02, 0x02, 0x32, 0x37, 0x12, 0x71, 0x0a, 0x13, 0x54, 0x6f, 0x6b, 0x65,
	0x6e, 0x69, 0x7a, 0x65, 0x4d, 0x50, 0x49, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x33, 0x44, 0x53, 0x10,
	0x12, 0x1a, 0x58, 0x8a, 0xf1, 0x30, 0x4c, 0x0a, 0x02, 0x32, 0x38, 0x12, 0x42, 0xd0, 0x9e, 0xd1,
	0x88, 0xd0, 0xb8, 0xd0, 0xb1, 0xd0, 0xba, 0xd0, 0xb0, 0x20, 0x4d, 0x50, 0x49, 0x20, 0xd0, 0xbf,
	0xd1, 0x80, 0xd0, 0xb8, 0x20, 0xd0, 0xb2, 0xd1, 0x8b, 0xd0, 0xbf, 0xd0, 0xbe, 0xd0, 0xbb, 0xd0,
	0xbd, 0xd0, 0xb5, 0xd0, 0xbd, 0xd0, 0xb8, 0xd0, 0xb8, 0x20, 0xd0, 0xbf, 0xd1, 0x80, 0xd0, 0xbe,
	0xd0, 0xb2, 0xd0, 0xb5, 0xd1, 0x80, 0xd0, 0xba, 0xd0, 0xb8, 0x20, 0x33, 0x44, 0x53, 0x3a, 0x18,
	0x05, 0x20, 0x65, 0xca, 0xa1, 0xec, 0x8e, 0x02, 0x02, 0x32, 0x38, 0x12, 0x62, 0x0a, 0x1c, 0x54,
	0x6f, 0x6b, 0x65, 0x6e, 0x69, 0x7a, 0x65, 0x55, 0x6e, 0x61, 0x63, 0x63, 0x65, 0x70, 0x74, 0x61,
	0x62, 0x6c, 0x65, 0x43, 0x61, 0x72, 0x64, 0x54, 0x79, 0x70, 0x65, 0x10, 0x13, 0x1a, 0x40, 0x8a,
	0xf1, 0x30, 0x34, 0x0a, 0x02, 0x32, 0x39, 0x12, 0x2a, 0xd0, 0x9d, 0xd0, 0xb5, 0xd0, 0xb4, 0xd0,
	0xbe, 0xd0, 0xbf, 0xd1, 0x83, 0xd1, 0x81, 0xd1, 0x82, 0xd0, 0xb8, 0xd0, 0xbc, 0xd1, 0x8b, 0xd0,
	0xb9, 0x20, 0xd1, 0x82, 0xd0, 0xb8, 0xd0, 0xbf, 0x20, 0xd0, 0xba, 0xd0, 0xb0, 0xd1, 0x80, 0xd1,
	0x82, 0xd1, 0x8b, 0x18, 0x05, 0x20, 0x66, 0xca, 0xa1, 0xec, 0x8e, 0x02, 0x02, 0x32, 0x39, 0x12,
	0x5f, 0x0a, 0x17, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x69, 0x7a, 0x65, 0x50, 0x61, 0x79, 0x6d, 0x65,
	0x6e, 0x74, 0x4e, 0x6f, 0x74, 0x46, 0x6f, 0x75, 0x6e, 0x64, 0x10, 0x14, 0x1a, 0x42, 0x8a, 0xf1,
	0x30, 0x36, 0x0a, 0x02, 0x33, 0x30, 0x12, 0x2c, 0xd0, 0xa1, 0xd1, 0x87, 0xd0, 0xb5, 0xd1, 0x82,
	0x20, 0xd0, 0xbd, 0xd0, 0xb0, 0x20, 0xd0, 0xbe, 0xd0, 0xbf, 0xd0, 0xbb, 0xd0, 0xb0, 0xd1, 0x82,
	0xd1, 0x83, 0x20, 0xd0, 0xbd, 0xd0, 0xb5, 0x20, 0xd0, 0xbd, 0xd0, 0xb0, 0xd0, 0xb9, 0xd0, 0xb4,
	0xd0, 0xb5, 0xd0, 0xbd, 0x18, 0x05, 0x20, 0x6a, 0xca, 0xa1, 0xec, 0x8e, 0x02, 0x02, 0x33, 0x30,
	0x12, 0x75, 0x0a, 0x19, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x69, 0x7a, 0x65, 0x43, 0x6c, 0x69, 0x65,
	0x6e, 0x74, 0x4b, 0x65, 0x79, 0x4e, 0x6f, 0x74, 0x46, 0x6f, 0x75, 0x6e, 0x64, 0x10, 0x15, 0x1a,
	0x56, 0x8a, 0xf1, 0x30, 0x4a, 0x0a, 0x02, 0x33, 0x31, 0x12, 0x40, 0xd0, 0x9d, 0xd0, 0xb5, 0x20,
	0xd0, 0xbf, 0xd0, 0xb5, 0xd1, 0x80, 0xd0, 0xb5, 0xd0, 0xb4, 0xd0, 0xb0, 0xd0, 0xbd, 0x20, 0xd0,
	0xba, 0xd0, 0xbb, 0xd1, 0x8e, 0xd1, 0x87, 0x20, 0xd1, 0x83, 0xd0, 0xba, 0xd0, 0xb0, 0xd0, 0xb7,
	0xd0, 0xb0, 0xd0, 0xbd, 0xd0, 0xbd, 0xd0, 0xbe, 0xd0, 0xb3, 0xd0, 0xbe, 0x20, 0xd0, 0xba, 0xd0,
	0xbb, 0xd0, 0xb8, 0xd0, 0xb5, 0xd0, 0xbd, 0xd1, 0x82, 0xd0, 0xb0, 0x18, 0x05, 0x20, 0x6a, 0xca,
	0xa1, 0xec, 0x8e, 0x02, 0x02, 0x33, 0x31, 0x12, 0x70, 0x0a, 0x11, 0x54, 0x6f, 0x6b, 0x65, 0x6e,
	0x69, 0x7a, 0x65, 0x46, 0x6f, 0x72, 0x62, 0x69, 0x64, 0x64, 0x65, 0x6e, 0x10, 0x16, 0x1a, 0x59,
	0x8a, 0xf1, 0x30, 0x4d, 0x0a, 0x02, 0x33, 0x32, 0x12, 0x43, 0xd0, 0x94, 0xd0, 0xbb, 0xd1, 0x8f,
	0x20, 0xd1, 0x82, 0xd0, 0xb5, 0xd1, 0x80, 0xd0, 0xbc, 0xd0, 0xb8, 0xd0, 0xbd, 0xd0, 0xb0, 0xd0,
	0xbb, 0xd0, 0xb0, 0x20, 0xd0, 0xb7, 0xd0, 0xb0, 0xd0, 0xbf, 0xd1, 0x80, 0xd0, 0xb5, 0xd1, 0x89,
	0xd0, 0xb5, 0xd0, 0xbd, 0xd0, 0xb0, 0x20, 0xd1, 0x82, 0xd0, 0xbe, 0xd0, 0xba, 0xd0, 0xb5, 0xd0,
	0xbd, 0xd0, 0xb8, 0xd0, 0xb7, 0xd0, 0xb0, 0xd1, 0x86, 0xd0, 0xb8, 0xd1, 0x8f, 0x18, 0x05, 0x20,
	0x6d, 0xca, 0xa1, 0xec, 0x8e, 0x02, 0x02, 0x33, 0x32, 0x12, 0xab, 0x01, 0x0a, 0x15, 0x54, 0x6f,
	0x6b, 0x65, 0x6e, 0x69, 0x7a, 0x65, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x4e, 0x6f, 0x74, 0x46, 0x6f,
	0x75, 0x6e, 0x64, 0x10, 0x17, 0x1a, 0x8f, 0x01, 0x8a, 0xf1, 0x30, 0x82, 0x01, 0x0a, 0x02, 0x33,
	0x33, 0x12, 0x78, 0xd0, 0x94, 0xd0, 0xbb, 0xd1, 0x8f, 0x20, 0xd0, 0xb4, 0xd0, 0xb0, 0xd0, 0xbd,
	0xd0, 0xbd, 0xd0, 0xbe, 0xd0, 0xb3, 0xd0, 0xbe, 0x20, 0xd0, 0xba, 0xd0, 0xbb, 0xd0, 0xb8, 0xd0,
	0xb5, 0xd0, 0xbd, 0xd1, 0x82, 0xd0, 0xb0, 0x20, 0xd0, 0xb2, 0x20, 0xd0, 0xb2, 0xd0, 0xb0, 0xd1,
	0x88, 0xd0, 0xb5, 0xd0, 0xb9, 0x20, 0xd0, 0xbe, 0xd1, 0x80, 0xd0, 0xb3, 0xd0, 0xb0, 0xd0, 0xbd,
	0xd0, 0xb8, 0xd0, 0xb7, 0xd0, 0xb0, 0xd1, 0x86, 0xd0, 0xb8, 0xd0, 0xb8, 0x20, 0xd0, 0xbd, 0xd0,
	0xb5, 0x20, 0xd0, 0xb7, 0xd0, 0xb0, 0xd1, 0x80, 0xd0, 0xb5, 0xd0, 0xb3, 0xd0, 0xb8, 0xd1, 0x81,
	0xd1, 0x82, 0xd1, 0x80, 0xd0, 0xb8, 0xd1, 0x80, 0xd0, 0xbe, 0xd0, 0xb2, 0xd0, 0xb0, 0xd0, 0xbd,
	0x20, 0xd1, 0x82, 0xd0, 0xbe, 0xd0, 0xba, 0xd0, 0xb5, 0xd0, 0xbd, 0x18, 0x05, 0x20, 0x66, 0xca,
	0xa1, 0xec, 0x8e, 0x02, 0x02, 0x33, 0x33, 0x12, 0x8c, 0x01, 0x0a, 0x1c, 0x54, 0x6f, 0x6b, 0x65,
	0x6e, 0x69, 0x7a, 0x65, 0x49, 0x6e, 0x63, 0x6f, 0x72, 0x72, 0x65, 0x63, 0x74, 0x42, 0x6c, 0x6f,
	0x63, 0x6b, 0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x10, 0x18, 0x1a, 0x6a, 0x8a, 0xf1, 0x30, 0x5e,
	0x0a, 0x02, 0x33, 0x34, 0x12, 0x54, 0xd0, 0x9d, 0xd0, 0xb5, 0xd0, 0xb2, 0xd0, 0xb5, 0xd1, 0x80,
	0xd0, 0xbd, 0xd0, 0xb0, 0xd1, 0x8f, 0x20, 0xd1, 0x81, 0xd1, 0x83, 0xd0, 0xbc, 0xd0, 0xbc, 0xd0,
	0xb0, 0x20, 0xd0, 0xb1, 0xd0, 0xbb, 0xd0, 0xbe, 0xd0, 0xba, 0xd0, 0xb8, 0xd1, 0x80, 0xd0, 0xbe,
	0xd0, 0xb2, 0xd0, 0xb0, 0xd0, 0xbd, 0xd0, 0xb8, 0xd1, 0x8f, 0x2c, 0x20, 0xd0, 0xb7, 0xd0, 0xb0,
	0xd1, 0x8f, 0xd0, 0xb2, 0xd0, 0xba, 0xd0, 0xb0, 0x20, 0xd0, 0xbe, 0xd1, 0x82, 0xd0, 0xbc, 0xd0,
	0xb5, 0xd0, 0xbd, 0xd0, 0xb5, 0xd0, 0xbd, 0xd0, 0xb0, 0x21, 0x18, 0x05, 0x20, 0x6a, 0xca, 0xa1,
	0xec, 0x8e, 0x02, 0x02, 0x33, 0x34, 0x12, 0x55, 0x0a, 0x14, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x69,
	0x7a, 0x65, 0x55, 0x6e, 0x6b, 0x6e, 0x6f, 0x77, 0x6e, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x10, 0x19,
	0x1a, 0x3b, 0x8a, 0xf1, 0x30, 0x2f, 0x0a, 0x02, 0x39, 0x39, 0x12, 0x25, 0xd0, 0x9d, 0xd0, 0xb5,
	0xd0, 0xb8, 0xd0, 0xb7, 0xd0, 0xb2, 0xd0, 0xb5, 0xd1, 0x81, 0xd1, 0x82, 0xd0, 0xbd, 0xd0, 0xb0,
	0xd1, 0x8f, 0x20, 0xd0, 0xbe, 0xd1, 0x88, 0xd0, 0xb8, 0xd0, 0xb1, 0xd0, 0xba, 0xd0, 0xb0, 0x3a,
	0x20, 0x18, 0x0b, 0x20, 0x6a, 0xca, 0xa1, 0xec, 0x8e, 0x02, 0x02, 0x39, 0x39, 0x12, 0x91, 0x01,
	0x0a, 0x22, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x69, 0x7a, 0x65, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x55, 0x6e, 0x61, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x6c, 0x65, 0x54, 0x72, 0x79, 0x4c,
	0x61, 0x74, 0x65, 0x72, 0x10, 0x1a, 0x1a, 0x69, 0x8a, 0xf1, 0x30, 0x5d, 0x0a, 0x02, 0x34, 0x31,
	0x12, 0x53, 0xd0, 0xa1, 0xd0, 0xb5, 0xd1, 0x80, 0xd0, 0xb2, 0xd0, 0xb8, 0xd1, 0x81, 0x20, 0xd0,
	0xb2, 0xd1, 0x80, 0xd0, 0xb5, 0xd0, 0xbc, 0xd0, 0xb5, 0xd0, 0xbd, 0xd0, 0xbd, 0xd0, 0xbe, 0x20,
	0xd0, 0xbd, 0xd0, 0xb5, 0xd0, 0xb4, 0xd0, 0xbe, 0xd1, 0x81, 0xd1, 0x82, 0xd1, 0x83, 0xd0, 0xbf,
	0xd0, 0xb5, 0xd0, 0xbd, 0x2c, 0x20, 0xd0, 0xbf, 0xd0, 0xbe, 0xd0, 0xbf, 0xd1, 0x80, 0xd0, 0xbe,
	0xd0, 0xb1, 0xd1, 0x83, 0xd0, 0xb9, 0xd1, 0x82, 0xd0, 0xb5, 0x20, 0xd0, 0xbf, 0xd0, 0xbe, 0xd0,
	0xb7, 0xd0, 0xb6, 0xd0, 0xb5, 0x18, 0x05, 0x20, 0x6c, 0xca, 0xa1, 0xec, 0x8e, 0x02, 0x02, 0x34,
	0x31, 0x12, 0x54, 0x0a, 0x15, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x69, 0x7a, 0x65, 0x49, 0x6e, 0x76,
	0x61, 0x6c, 0x69, 0x64, 0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x10, 0x1b, 0x1a, 0x39, 0x8a, 0xf1,
	0x30, 0x2d, 0x0a, 0x02, 0x34, 0x32, 0x12, 0x23, 0xd0, 0x9d, 0xd0, 0xb5, 0xd0, 0xbf, 0xd1, 0x80,
	0xd0, 0xb0, 0xd0, 0xb2, 0xd0, 0xb8, 0xd0, 0xbb, 0xd1, 0x8c, 0xd0, 0xbd, 0xd0, 0xb0, 0xd1, 0x8f,
	0x20, 0xd1, 0x81, 0xd1, 0x83, 0xd0, 0xbc, 0xd0, 0xbc, 0xd0, 0xb0, 0x18, 0x05, 0x20, 0x6a, 0xca,
	0xa1, 0xec, 0x8e, 0x02, 0x02, 0x34, 0x32, 0x12, 0x8f, 0x01, 0x0a, 0x1c, 0x54, 0x6f, 0x6b, 0x65,
	0x6e, 0x69, 0x7a, 0x65, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x44, 0x62, 0x55, 0x6e, 0x61,
	0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x6c, 0x65, 0x10, 0x1c, 0x1a, 0x6d, 0x8a, 0xf1, 0x30, 0x61,
	0x0a, 0x02, 0x34, 0x33, 0x12, 0x57, 0xd0, 0xa1, 0xd0, 0xb5, 0xd1, 0x80, 0xd0, 0xb2, 0xd0, 0xb8,
	0xd1, 0x81, 0x20, 0x44, 0x62, 0x20, 0xd0, 0xb2, 0xd1, 0x80, 0xd0, 0xb5, 0xd0, 0xbc, 0xd0, 0xb5,
	0xd0, 0xbd, 0xd0, 0xbd, 0xd0, 0xbe, 0x20, 0xd0, 0xbd, 0xd0, 0xb5, 0xd0, 0xb4, 0xd0, 0xbe, 0xd1,
	0x81, 0xd1, 0x82, 0xd1, 0x83, 0xd0, 0xbf, 0xd0, 0xb5, 0xd0, 0xbd, 0x2c, 0x20, 0xd0, 0xbf, 0xd0,
	0xbe, 0xd0, 0xbf, 0xd1, 0x80, 0xd0, 0xbe, 0xd0, 0xb1, 0xd1, 0x83, 0xd0, 0xb9, 0xd1, 0x82, 0xd0,
	0xb5, 0x20, 0xd0, 0xbf, 0xd0, 0xbe, 0xd0, 0xb7, 0xd0, 0xb6, 0xd0, 0xb5, 0x20, 0x18, 0x05, 0x20,
	0x6c, 0xca, 0xa1, 0xec, 0x8e, 0x02, 0x02, 0x34, 0x33, 0x12, 0x78, 0x0a, 0x1e, 0x54, 0x6f, 0x6b,
	0x65, 0x6e, 0x69, 0x7a, 0x65, 0x49, 0x6e, 0x63, 0x6f, 0x72, 0x72, 0x65, 0x63, 0x74, 0x46, 0x69,
	0x65, 0x6c, 0x64, 0x4d, 0x65, 0x72, 0x63, 0x68, 0x61, 0x6e, 0x74, 0x10, 0x1d, 0x1a, 0x54, 0x8a,
	0xf1, 0x30, 0x48, 0x0a, 0x02, 0x34, 0x34, 0x12, 0x3e, 0xd0, 0x9d, 0xd0, 0xb5, 0xd0, 0xbf, 0xd1,
	0x80, 0xd0, 0xb0, 0xd0, 0xb2, 0xd0, 0xb8, 0xd0, 0xbb, 0xd1, 0x8c, 0xd0, 0xbd, 0xd0, 0xbe, 0xd0,
	0xb5, 0x20, 0xd0, 0xb7, 0xd0, 0xbd, 0xd0, 0xb0, 0xd1, 0x87, 0xd0, 0xb5, 0xd0, 0xbd, 0xd0, 0xb8,
	0xd0, 0xb5, 0x20, 0xd0, 0xb2, 0x20, 0xd0, 0xbf, 0xd0, 0xbe, 0xd0, 0xbb, 0xd0, 0xb5, 0x20, 0x4d,
	0x45, 0x52, 0x43, 0x48, 0x41, 0x4e, 0x54, 0x18, 0x05, 0x20, 0x6a, 0xca, 0xa1, 0xec, 0x8e, 0x02,
	0x02, 0x34, 0x34, 0x12, 0x5a, 0x0a, 0x18, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x69, 0x7a, 0x65, 0x4d,
	0x65, 0x72, 0x63, 0x68, 0x61, 0x6e, 0x74, 0x4e, 0x6f, 0x74, 0x46, 0x6f, 0x75, 0x6e, 0x64, 0x10,
	0x1e, 0x1a, 0x3c, 0x8a, 0xf1, 0x30, 0x30, 0x0a, 0x02, 0x31, 0x37, 0x12, 0x26, 0xd0, 0x9a, 0xd0,
	0xbe, 0xd0, 0xbc, 0xd0, 0xbc, 0xd0, 0xb5, 0xd1, 0x80, 0xd1, 0x81, 0xd0, 0xb0, 0xd0, 0xbd, 0xd1,
	0x82, 0x20, 0xd0, 0xbd, 0xd0, 0xb5, 0x20, 0xd0, 0xbd, 0xd0, 0xb0, 0xd0, 0xb9, 0xd0, 0xb4, 0xd0,
	0xb5, 0xd0, 0xbd, 0x18, 0x05, 0x20, 0x6d, 0xca, 0xa1, 0xec, 0x8e, 0x02, 0x02, 0x31, 0x37, 0x12,
	0x5e, 0x0a, 0x1c, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x69, 0x7a, 0x65, 0x4f, 0x72, 0x64, 0x65, 0x72,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x4e, 0x6f, 0x74, 0x46, 0x6f, 0x75, 0x6e, 0x64, 0x10,
	0x1f, 0x1a, 0x3c, 0x8a, 0xf1, 0x30, 0x30, 0x0a, 0x02, 0x34, 0x35, 0x12, 0x26, 0xd0, 0x97, 0xd0,
	0xb0, 0xd1, 0x8f, 0xd0, 0xb2, 0xd0, 0xba, 0xd0, 0xb0, 0x20, 0x4f, 0x52, 0x44, 0x45, 0x52, 0x20,
	0xd0, 0xbd, 0xd0, 0xb5, 0x20, 0xd0, 0xbd, 0xd0, 0xb0, 0xd0, 0xb9, 0xd0, 0xb4, 0xd0, 0xb5, 0xd0,
	0xbd, 0xd0, 0xb0, 0x18, 0x05, 0x20, 0x6a, 0xca, 0xa1, 0xec, 0x8e, 0x02, 0x02, 0x34, 0x35, 0x12,
	0x57, 0x0a, 0x13, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x69, 0x7a, 0x65, 0x49, 0x6e, 0x76, 0x61, 0x6c,
	0x69, 0x64, 0x53, 0x69, 0x67, 0x6e, 0x10, 0x20, 0x1a, 0x3e, 0x8a, 0xf1, 0x30, 0x32, 0x0a, 0x02,
	0x34, 0x36, 0x12, 0x28, 0xd0, 0x9d, 0xd0, 0xb5, 0xd0, 0xbf, 0xd1, 0x80, 0xd0, 0xb0, 0xd0, 0xb2,
	0xd0, 0xb8, 0xd0, 0xbb, 0xd1, 0x8c, 0xd0, 0xbd, 0xd0, 0xb0, 0xd1, 0x8f, 0x20, 0xd0, 0xbf, 0xd0,
	0xbe, 0xd0, 0xb4, 0xd0, 0xbf, 0xd0, 0xb8, 0xd1, 0x81, 0xd1, 0x8c, 0x21, 0x18, 0x05, 0x20, 0x6a,
	0xca, 0xa1, 0xec, 0x8e, 0x02, 0x02, 0x34, 0x36, 0x12, 0x82, 0x01, 0x0a, 0x1a, 0x54, 0x6f, 0x6b,
	0x65, 0x6e, 0x69, 0x7a, 0x65, 0x49, 0x6e, 0x63, 0x6f, 0x72, 0x72, 0x65, 0x63, 0x74, 0x52, 0x65,
	0x66, 0x75, 0x6e, 0x64, 0x53, 0x75, 0x6d, 0x10, 0x21, 0x1a, 0x62, 0x8a, 0xf1, 0x30, 0x55, 0x0a,
	0x03, 0x34, 0x37, 0x20, 0x12, 0x4a, 0xd0, 0xa1, 0xd1, 0x83, 0xd0, 0xbc, 0xd0, 0xbc, 0xd0, 0xb0,
	0x20, 0xd0, 0xb2, 0xd0, 0xbe, 0xd0, 0xb7, 0xd0, 0xb2, 0xd1, 0x80, 0xd1, 0x82, 0xd0, 0xb0, 0x20,
	0x27, 0x25, 0x73, 0x27, 0x20, 0xd0, 0xb1, 0xd0, 0xbe, 0xd0, 0xbb, 0xd1, 0x8c, 0xd1, 0x88, 0xd0,
	0xb5, 0x20, 0xd1, 0x87, 0xd0, 0xb5, 0xd0, 0xbc, 0x20, 0xd1, 0x81, 0xd1, 0x83, 0xd0, 0xbc, 0xd0,
	0xbc, 0xd0, 0xb0, 0x20, 0xd0, 0xb7, 0xd0, 0xb0, 0xd0, 0xba, 0xd0, 0xb0, 0xd0, 0xb7, 0xd0, 0xb0,
	0x18, 0x05, 0x20, 0x6a, 0xca, 0xa1, 0xec, 0x8e, 0x02, 0x03, 0x34, 0x37, 0x20, 0x12, 0x9c, 0x01,
	0x0a, 0x17, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x69, 0x7a, 0x65, 0x49, 0x6e, 0x63, 0x6f, 0x72, 0x72,
	0x65, 0x63, 0x74, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x10, 0x22, 0x1a, 0x7f, 0x8a, 0xf1, 0x30,
	0x73, 0x0a, 0x02, 0x34, 0x38, 0x12, 0x69, 0xd0, 0xa2, 0xd0, 0xb5, 0xd0, 0xba, 0xd1, 0x83, 0xd1,
	0x89, 0xd0, 0xb8, 0xd0, 0xb9, 0x20, 0xd1, 0x81, 0xd1, 0x82, 0xd0, 0xb0, 0xd1, 0x82, 0xd1, 0x83,
	0xd1, 0x81, 0x20, 0xd0, 0xb7, 0xd0, 0xb0, 0xd0, 0xba, 0xd0, 0xb0, 0xd0, 0xb7, 0xd0, 0xb0, 0x20,
	0xd0, 0xbd, 0xd0, 0xb5, 0x20, 0xd0, 0xbf, 0xd0, 0xbe, 0xd0, 0xb7, 0xd0, 0xb2, 0xd0, 0xbe, 0xd0,
	0xbb, 0xd1, 0x8f, 0xd0, 0xb5, 0xd1, 0x82, 0x20, 0xd0, 0xb4, 0xd0, 0xb5, 0xd0, 0xbb, 0xd0, 0xb0,
	0xd1, 0x82, 0xd1, 0x8c, 0x20, 0xd0, 0xb2, 0xd0, 0xbe, 0xd0, 0xb7, 0xd0, 0xb2, 0xd1, 0x80, 0xd0,
	0xb0, 0xd1, 0x82, 0x2f, 0xd0, 0xbe, 0xd1, 0x82, 0xd0, 0xbc, 0xd0, 0xb5, 0xd0, 0xbd, 0xd1, 0x83,
	0x18, 0x05, 0x20, 0x6a, 0xca, 0xa1, 0xec, 0x8e, 0x02, 0x02, 0x34, 0x38, 0x12, 0x5b, 0x0a, 0x16,
	0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x69, 0x7a, 0x65, 0x49, 0x6e, 0x63, 0x6f, 0x72, 0x72, 0x65, 0x63,
	0x74, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x10, 0x23, 0x1a, 0x3f, 0x8a, 0xf1, 0x30, 0x33, 0x0a, 0x02,
	0x35, 0x30, 0x12, 0x29, 0xd0, 0x9d, 0xd0, 0xb5, 0xd0, 0xbf, 0xd1, 0x80, 0xd0, 0xb0, 0xd0, 0xb2,
	0xd0, 0xb8, 0xd0, 0xbb, 0xd1, 0x8c, 0xd0, 0xbd, 0xd0, 0xbe, 0xd0, 0xb5, 0x20, 0xd0, 0xb7, 0xd0,
	0xbd, 0xd0, 0xb0, 0xd1, 0x87, 0xd0, 0xb5, 0xd0, 0xbd, 0xd0, 0xb8, 0xd0, 0xb5, 0x18, 0x05, 0x20,
	0x6a, 0xca, 0xa1, 0xec, 0x8e, 0x02, 0x02, 0x35, 0x30, 0x12, 0xaa, 0x01, 0x0a, 0x1f, 0x54, 0x6f,
	0x6b, 0x65, 0x6e, 0x69, 0x7a, 0x65, 0x49, 0x6e, 0x63, 0x6f, 0x72, 0x72, 0x65, 0x63, 0x74, 0x54,
	0x65, 0x72, 0x6d, 0x69, 0x6e, 0x61, 0x6c, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x10, 0x24, 0x1a,
	0x84, 0x01, 0x8a, 0xf1, 0x30, 0x78, 0x0a, 0x02, 0x35, 0x31, 0x12, 0x6e, 0xd0, 0xa2, 0xd0, 0xb5,
	0xd0, 0xba, 0xd1, 0x83, 0xd1, 0x89, 0xd0, 0xb8, 0xd0, 0xb9, 0x20, 0xd1, 0x81, 0xd1, 0x82, 0xd0,
	0xb0, 0xd1, 0x82, 0xd1, 0x83, 0xd1, 0x81, 0x20, 0xd1, 0x82, 0xd0, 0xb5, 0xd1, 0x80, 0xd0, 0xbc,
	0xd0, 0xb8, 0xd0, 0xbd, 0xd0, 0xb0, 0xd0, 0xbb, 0xd0, 0xb0, 0x20, 0xd0, 0xbd, 0xd0, 0xb5, 0x20,
	0xd0, 0xbf, 0xd0, 0xbe, 0xd0, 0xb7, 0xd0, 0xb2, 0xd0, 0xbe, 0xd0, 0xbb, 0xd1, 0x8f, 0xd0, 0xb5,
	0xd1, 0x82, 0x20, 0xd0, 0xbf, 0xd1, 0x80, 0xd0, 0xbe, 0xd0, 0xb8, 0xd0, 0xb7, 0xd0, 0xb2, 0xd0,
	0xbe, 0xd0, 0xb4, 0xd0, 0xb8, 0xd1, 0x82, 0xd1, 0x8c, 0x20, 0xd0, 0xbe, 0xd0, 0xbf, 0xd0, 0xb5,
	0xd1, 0x80, 0xd0, 0xb0, 0xd1, 0x86, 0xd0, 0xb8, 0xd0, 0xb8, 0x18, 0x05, 0x20, 0x6d, 0xca, 0xa1,
	0xec, 0x8e, 0x02, 0x02, 0x35, 0x31, 0x12, 0xa1, 0x01, 0x0a, 0x1a, 0x54, 0x6f, 0x6b, 0x65, 0x6e,
	0x69, 0x7a, 0x65, 0x46, 0x6f, 0x72, 0x62, 0x69, 0x64, 0x64, 0x65, 0x6e, 0x4f, 0x70, 0x65, 0x72,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x10, 0x25, 0x1a, 0x80, 0x01, 0x8a, 0xf1, 0x30, 0x74, 0x0a, 0x02,
	0x35, 0x32, 0x12, 0x6a, 0xd0, 0x9e, 0xd0, 0xbf, 0xd0, 0xb5, 0xd1, 0x80, 0xd0, 0xb0, 0xd1, 0x86,
	0xd0, 0xb8, 0xd1, 0x8f, 0x20, 0xd0, 0xbe, 0xd1, 0x82, 0xd0, 0xbc, 0xd0, 0xb5, 0xd0, 0xbd, 0xd1,
	0x8b, 0x2f, 0xd0, 0xb2, 0xd0, 0xbe, 0xd0, 0xb7, 0xd0, 0xb2, 0xd1, 0x80, 0xd0, 0xb0, 0xd1, 0x82,
	0xd0, 0xb0, 0x20, 0xd1, 0x87, 0xd0, 0xb5, 0xd1, 0x80, 0xd0, 0xb5, 0xd0, 0xb7, 0x20, 0x41, 0x50,
	0x49, 0x20, 0xd0, 0xb4, 0xd0, 0xbb, 0xd1, 0x8f, 0x20, 0xd1, 0x82, 0xd0, 0xb5, 0xd1, 0x80, 0xd0,
	0xbc, 0xd0, 0xb8, 0xd0, 0xbd, 0xd0, 0xb0, 0xd0, 0xbb, 0xd0, 0xb0, 0x20, 0xd0, 0xb7, 0xd0, 0xb0,
	0xd0, 0xbf, 0xd1, 0x80, 0xd0, 0xb5, 0xd1, 0x89, 0xd0, 0xb5, 0xd0, 0xbd, 0xd0, 0xb0, 0x18, 0x05,
	0x20, 0x6d, 0xca, 0xa1, 0xec, 0x8e, 0x02, 0x02, 0x35, 0x32, 0x12, 0x6e, 0x0a, 0x1c, 0x54, 0x6f,
	0x6b, 0x65, 0x6e, 0x69, 0x7a, 0x65, 0x44, 0x75, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x74, 0x65, 0x44,
	0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x10, 0x26, 0x1a, 0x4c, 0x8a, 0xf1,
	0x30, 0x40, 0x0a, 0x02, 0x35, 0x33, 0x12, 0x36, 0xd0, 0x94, 0xd1, 0x83, 0xd0, 0xb1, 0xd0, 0xbb,
	0xd0, 0xb8, 0xd1, 0x80, 0xd0, 0xbe, 0xd0, 0xb2, 0xd0, 0xb0, 0xd0, 0xbd, 0xd0, 0xb8, 0xd0, 0xb5,
	0x20, 0xd0, 0xbe, 0xd0, 0xbf, 0xd0, 0xb8, 0xd1, 0x81, 0xd0, 0xb0, 0xd0, 0xbd, 0xd0, 0xb8, 0xd1,
	0x8f, 0x20, 0xd0, 0xbe, 0xd1, 0x82, 0xd0, 0xbc, 0xd0, 0xb5, 0xd0, 0xbd, 0xd1, 0x8b, 0x18, 0x05,
	0x20, 0x6a, 0xca, 0xa1, 0xec, 0x8e, 0x02, 0x02, 0x35, 0x33, 0x12, 0x64, 0x0a, 0x13, 0x54, 0x6f,
	0x6b, 0x65, 0x6e, 0x69, 0x7a, 0x65, 0x52, 0x65, 0x66, 0x75, 0x6e, 0x64, 0x45, 0x72, 0x72, 0x6f,
	0x72, 0x10, 0x27, 0x1a, 0x4b, 0x8a, 0xf1, 0x30, 0x40, 0x0a, 0x01, 0x46, 0x12, 0x37, 0xd0, 0x9e,
	0xd1, 0x88, 0xd0, 0xb8, 0xd0, 0xb1, 0xd0, 0xba, 0xd0, 0xb0, 0x20, 0xd0, 0xbf, 0xd1, 0x80, 0xd0,
	0xb8, 0x20, 0xd0, 0xbe, 0xd0, 0xb1, 0xd1, 0x80, 0xd0, 0xb0, 0xd0, 0xb1, 0xd0, 0xbe, 0xd1, 0x82,
	0xd0, 0xba, 0xd0, 0xb5, 0x20, 0xd0, 0xb2, 0xd0, 0xbe, 0xd0, 0xb7, 0xd0, 0xb2, 0xd1, 0x80, 0xd0,
	0xb0, 0xd1, 0x82, 0xd0, 0xb0, 0x18, 0x05, 0x20, 0x6a, 0xca, 0xa1, 0xec, 0x8e, 0x02, 0x01, 0x46,
	0x12, 0x4a, 0x0a, 0x10, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x69, 0x7a, 0x65, 0x50, 0x61, 0x79, 0x45,
	0x72, 0x72, 0x6f, 0x72, 0x10, 0x28, 0x1a, 0x34, 0x8a, 0xf1, 0x30, 0x29, 0x0a, 0x01, 0x45, 0x12,
	0x20, 0xd0, 0x9e, 0xd1, 0x88, 0xd0, 0xb8, 0xd0, 0xb1, 0xd0, 0xba, 0xd0, 0xb0, 0x20, 0xd0, 0xbf,
	0xd1, 0x80, 0xd0, 0xb8, 0x20, 0xd0, 0xbe, 0xd0, 0xbf, 0xd0, 0xbb, 0xd0, 0xb0, 0xd1, 0x82, 0xd0,
	0xb5, 0x18, 0x05, 0x20, 0x6a, 0xca, 0xa1, 0xec, 0x8e, 0x02, 0x01, 0x45, 0x12, 0x59, 0x0a, 0x16,
	0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x69, 0x7a, 0x65, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x45,
	0x78, 0x70, 0x69, 0x72, 0x65, 0x64, 0x10, 0x29, 0x1a, 0x3d, 0x8a, 0xf1, 0x30, 0x32, 0x0a, 0x01,
	0x63, 0x12, 0x29, 0xd0, 0xa1, 0xd1, 0x87, 0xd0, 0xb5, 0xd1, 0x82, 0x20, 0xd0, 0xbd, 0xd0, 0xb0,
	0x20, 0xd0, 0xbe, 0xd0, 0xbf, 0xd0, 0xbb, 0xd0, 0xb0, 0xd1, 0x82, 0xd1, 0x83, 0x20, 0xd1, 0x83,
	0xd1, 0x81, 0xd1, 0x82, 0xd0, 0xb0, 0xd1, 0x80, 0xd0, 0xb5, 0xd0, 0xbb, 0x18, 0x05, 0x20, 0x6a,
	0xca, 0xa1, 0xec, 0x8e, 0x02, 0x01, 0x63, 0x12, 0x77, 0x0a, 0x1e, 0x54, 0x6f, 0x6b, 0x65, 0x6e,
	0x69, 0x7a, 0x65, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x48, 0x61,
	0x6e, 0x64, 0x6c, 0x65, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x10, 0x2b, 0x1a, 0x53, 0x8a, 0xf1, 0x30,
	0x46, 0x0a, 0x03, 0x34, 0x31, 0x31, 0x12, 0x3b, 0xd0, 0x9e, 0xd1, 0x88, 0xd0, 0xb8, 0xd0, 0xb1,
	0xd0, 0xba, 0xd0, 0xb0, 0x20, 0xd0, 0xbf, 0xd1, 0x80, 0xd0, 0xb8, 0x20, 0xd0, 0xbe, 0xd0, 0xb1,
	0xd1, 0x80, 0xd0, 0xb0, 0xd0, 0xb1, 0xd0, 0xbe, 0xd1, 0x82, 0xd0, 0xba, 0xd0, 0xb5, 0x20, 0xd1,
	0x82, 0xd1, 0x80, 0xd0, 0xb0, 0xd0, 0xbd, 0xd0, 0xb7, 0xd0, 0xb0, 0xd0, 0xba, 0xd1, 0x86, 0xd0,
	0xb8, 0xd0, 0xb8, 0x18, 0x05, 0x20, 0x6a, 0xca, 0xa1, 0xec, 0x8e, 0x02, 0x03, 0x34, 0x31, 0x31,
	0x1a, 0x6c, 0x92, 0xf1, 0x30, 0x10, 0x0a, 0x01, 0x30, 0x12, 0x07, 0x64, 0x65, 0x66, 0x61, 0x75,
	0x6c, 0x74, 0x18, 0x0f, 0x20, 0x00, 0xaa, 0x82, 0xec, 0x8e, 0x02, 0x2a, 0x64, 0x65, 0x66, 0x61,
	0x75, 0x6c, 0x74, 0x5f, 0x6a, 0x75, 0x73, 0x61, 0x6e, 0x5f, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x5f, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x69, 0x7a, 0x65,
	0x5f, 0x76, 0x61, 0x6c, 0x75, 0x65, 0xb2, 0x82, 0xec, 0x8e, 0x02, 0x22, 0x6a, 0x75, 0x73, 0x61,
	0x6e, 0x5f, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x5f,
	0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x69, 0x7a, 0x65, 0x5f, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0xb7,
	0x01, 0x0a, 0x22, 0x6a, 0x75, 0x73, 0x61, 0x6e, 0x5f, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x5f, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x69, 0x7a, 0x65, 0x5f,
	0x76, 0x61, 0x6c, 0x75, 0x65, 0x12, 0x21, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x45, 0x6e, 0x75, 0x6d, 0x56, 0x61, 0x6c, 0x75,
	0x65, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x91, 0x8e, 0x06, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x46, 0x2e, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x2e, 0x6a, 0x75,
	0x73, 0x61, 0x6e, 0x5f, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x69, 0x7a, 0x65, 0x2e, 0x6a, 0x75, 0x73,
	0x61, 0x6e, 0x5f, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x69, 0x7a, 0x65, 0x2e, 0x4a, 0x75, 0x73, 0x61,
	0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x43, 0x6f, 0x64, 0x65, 0x54, 0x6f, 0x6b,
	0x65, 0x6e, 0x69, 0x7a, 0x65, 0x52, 0x65, 0x66, 0x52, 0x1e, 0x6a, 0x75, 0x73, 0x61, 0x6e, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x43, 0x6f, 0x64, 0x65, 0x54, 0x6f, 0x6b, 0x65, 0x6e,
	0x69, 0x7a, 0x65, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0xc1, 0x01, 0x0a, 0x2a, 0x64, 0x65, 0x66,
	0x61, 0x75, 0x6c, 0x74, 0x5f, 0x6a, 0x75, 0x73, 0x61, 0x6e, 0x5f, 0x72, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x5f, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x69, 0x7a,
	0x65, 0x5f, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x12, 0x1c, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x45, 0x6e, 0x75, 0x6d, 0x4f, 0x70,
	0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x92, 0x8e, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x46, 0x2e,
	0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x2e, 0x6a, 0x75, 0x73, 0x61, 0x6e,
	0x5f, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x69, 0x7a, 0x65, 0x2e, 0x6a, 0x75, 0x73, 0x61, 0x6e, 0x5f,
	0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x69, 0x7a, 0x65, 0x2e, 0x4a, 0x75, 0x73, 0x61, 0x6e, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x43, 0x6f, 0x64, 0x65, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x69,
	0x7a, 0x65, 0x52, 0x65, 0x66, 0x52, 0x25, 0x64, 0x65, 0x66, 0x61, 0x75, 0x6c, 0x74, 0x4a, 0x75,
	0x73, 0x61, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x43, 0x6f, 0x64, 0x65, 0x54,
	0x6f, 0x6b, 0x65, 0x6e, 0x69, 0x7a, 0x65, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x42, 0x2d, 0x5a, 0x2b,
	0x67, 0x69, 0x74, 0x2e, 0x6c, 0x6f, 0x63, 0x61, 0x6c, 0x2f, 0x73, 0x65, 0x6e, 0x73, 0x69, 0x74,
	0x69, 0x76, 0x65, 0x2f, 0x69, 0x6e, 0x6e, 0x65, 0x72, 0x70, 0x62, 0x2f, 0x70, 0x72, 0x6f, 0x63,
	0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x2f, 0x67, 0x72, 0x70, 0x63, 0x62, 0x08, 0x65, 0x64, 0x69,
	0x74, 0x69, 0x6f, 0x6e, 0x73, 0x70, 0xe8, 0x07,
})

var (
	file_inner_processing_grpc_jusan_tokenize_proto_rawDescOnce sync.Once
	file_inner_processing_grpc_jusan_tokenize_proto_rawDescData []byte
)

func file_inner_processing_grpc_jusan_tokenize_proto_rawDescGZIP() []byte {
	file_inner_processing_grpc_jusan_tokenize_proto_rawDescOnce.Do(func() {
		file_inner_processing_grpc_jusan_tokenize_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_inner_processing_grpc_jusan_tokenize_proto_rawDesc), len(file_inner_processing_grpc_jusan_tokenize_proto_rawDesc)))
	})
	return file_inner_processing_grpc_jusan_tokenize_proto_rawDescData
}

var file_inner_processing_grpc_jusan_tokenize_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_inner_processing_grpc_jusan_tokenize_proto_msgTypes = make([]protoimpl.MessageInfo, 1)
var file_inner_processing_grpc_jusan_tokenize_proto_goTypes = []any{
	(JusanResponseCodeTokenize)(0),        // 0: processing.jusan_tokenize.jusan_tokenize.JusanResponseCodeTokenize
	(*JusanResponseCodeTokenizeRef)(nil),  // 1: processing.jusan_tokenize.jusan_tokenize.JusanResponseCodeTokenizeRef
	(EnumTransactionStatus)(0),            // 2: processing.transaction.transaction_status.EnumTransactionStatus
	(IntegrationError)(0),                 // 3: processing.integration.integration.IntegrationError
	(*descriptorpb.EnumValueOptions)(nil), // 4: google.protobuf.EnumValueOptions
	(*descriptorpb.EnumOptions)(nil),      // 5: google.protobuf.EnumOptions
}
var file_inner_processing_grpc_jusan_tokenize_proto_depIdxs = []int32{
	2, // 0: processing.jusan_tokenize.jusan_tokenize.JusanResponseCodeTokenizeRef.transaction_status:type_name -> processing.transaction.transaction_status.EnumTransactionStatus
	3, // 1: processing.jusan_tokenize.jusan_tokenize.JusanResponseCodeTokenizeRef.integration_error:type_name -> processing.integration.integration.IntegrationError
	4, // 2: processing.jusan_tokenize.jusan_tokenize.jusan_response_code_tokenize_value:extendee -> google.protobuf.EnumValueOptions
	5, // 3: processing.jusan_tokenize.jusan_tokenize.default_jusan_response_code_tokenize_value:extendee -> google.protobuf.EnumOptions
	1, // 4: processing.jusan_tokenize.jusan_tokenize.jusan_response_code_tokenize_value:type_name -> processing.jusan_tokenize.jusan_tokenize.JusanResponseCodeTokenizeRef
	1, // 5: processing.jusan_tokenize.jusan_tokenize.default_jusan_response_code_tokenize_value:type_name -> processing.jusan_tokenize.jusan_tokenize.JusanResponseCodeTokenizeRef
	6, // [6:6] is the sub-list for method output_type
	6, // [6:6] is the sub-list for method input_type
	4, // [4:6] is the sub-list for extension type_name
	2, // [2:4] is the sub-list for extension extendee
	0, // [0:2] is the sub-list for field type_name
}

func init() { file_inner_processing_grpc_jusan_tokenize_proto_init() }
func file_inner_processing_grpc_jusan_tokenize_proto_init() {
	if File_inner_processing_grpc_jusan_tokenize_proto != nil {
		return
	}
	file_inner_processing_grpc_transaction_status_proto_init()
	file_inner_processing_grpc_integration_proto_init()
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_inner_processing_grpc_jusan_tokenize_proto_rawDesc), len(file_inner_processing_grpc_jusan_tokenize_proto_rawDesc)),
			NumEnums:      1,
			NumMessages:   1,
			NumExtensions: 2,
			NumServices:   0,
		},
		GoTypes:           file_inner_processing_grpc_jusan_tokenize_proto_goTypes,
		DependencyIndexes: file_inner_processing_grpc_jusan_tokenize_proto_depIdxs,
		EnumInfos:         file_inner_processing_grpc_jusan_tokenize_proto_enumTypes,
		MessageInfos:      file_inner_processing_grpc_jusan_tokenize_proto_msgTypes,
		ExtensionInfos:    file_inner_processing_grpc_jusan_tokenize_proto_extTypes,
	}.Build()
	File_inner_processing_grpc_jusan_tokenize_proto = out.File
	file_inner_processing_grpc_jusan_tokenize_proto_goTypes = nil
	file_inner_processing_grpc_jusan_tokenize_proto_depIdxs = nil
}
