package gtransaction

import (
	"context"
	"errors"
)

var (
	ErrNoTransaction = errors.New("no transaction in context")
)

//go:generate go run go.uber.org/mock/mockgen -package=mocks -destination=./mocks/mocks.go -source=transaction.go *
type Manager interface {
	// Begin Открывает начало транзакции. Возвращает контекст
	// с объектом базы с открытой транзакцией. Важно во всех методах
	// репозиториев использовать этот возвращаемый контекст
	Begin(ctx context.Context) context.Context
	Commit(ctx context.Context) error
	Rollback(ctx context.Context) error

	// Finish заканчивает транзакцию. Если в функции не произошло никаких ошибок - коммитит транзакцию,
	// в ином случае роллбечит. Возвращает ошибки коммита/роллбека. ВАЖНО - для корректной работы
	// необходимо правильно использовать в defer
	//
	//Пример использования:
	// func (s *ExampleService) Example(ctx context.Context) (model *model.Example, err error) {  // именованный возврат err обязателен
	// 	ctx = s.transactions.Begin(ctx)
	//     defer func() {
	//         // В defer окажется текущая ошибка функции (или nil). Затем Finish, если
	//         // произошли ошибки коммита/роллбека, вернет обернутую ошибку. В итоге из функции
	//         // Example вернется именно эта ошибка за счет именованного возврата и defer
	//     	err = s.transactions.Finish(ctx, err)
	//     }()
	//
	//		model, err = s.repo.Example.Create(ctx, &model.Example{Text: "text"})
	//     if err != nil {
	//     	return nil, err
	//     }
	//
	//     // some code...
	//
	//     return model, nil
	// }
	Finish(ctx context.Context, err error) error
}
