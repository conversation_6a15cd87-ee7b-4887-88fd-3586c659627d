// Code generated by MockGen. DO NOT EDIT.
// Source: merchant_grpc.pb.go

// Package grpcmock is a generated GoMock package.
package grpcmock

import (
	context "context"
	reflect "reflect"

	grpc "git.local/sensitive/innerpb/processing/grpc"
	gomock "github.com/golang/mock/gomock"
	grpc0 "google.golang.org/grpc"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
)

// MockMerchantClient is a mock of MerchantClient interface.
type MockMerchantClient struct {
	ctrl     *gomock.Controller
	recorder *MockMerchantClientMockRecorder
}

// MockMerchantClientMockRecorder is the mock recorder for MockMerchantClient.
type MockMerchantClientMockRecorder struct {
	mock *MockMerchantClient
}

// NewMockMerchantClient creates a new mock instance.
func NewMockMerchantClient(ctrl *gomock.Controller) *MockMerchantClient {
	mock := &MockMerchantClient{ctrl: ctrl}
	mock.recorder = &MockMerchantClientMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockMerchantClient) EXPECT() *MockMerchantClientMockRecorder {
	return m.recorder
}

// CheckProject mocks base method.
func (m *MockMerchantClient) CheckProject(ctx context.Context, in *grpc.CheckMerchantProjectRequestV1, opts ...grpc0.CallOption) (*emptypb.Empty, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "CheckProject", varargs...)
	ret0, _ := ret[0].(*emptypb.Empty)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CheckProject indicates an expected call of CheckProject.
func (mr *MockMerchantClientMockRecorder) CheckProject(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CheckProject", reflect.TypeOf((*MockMerchantClient)(nil).CheckProject), varargs...)
}

// CheckProjectAuth mocks base method.
func (m *MockMerchantClient) CheckProjectAuth(ctx context.Context, in *grpc.CheckProjectAuthRequestV1, opts ...grpc0.CallOption) (*grpc.CheckProjectAuthResponseV1, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "CheckProjectAuth", varargs...)
	ret0, _ := ret[0].(*grpc.CheckProjectAuthResponseV1)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CheckProjectAuth indicates an expected call of CheckProjectAuth.
func (mr *MockMerchantClientMockRecorder) CheckProjectAuth(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CheckProjectAuth", reflect.TypeOf((*MockMerchantClient)(nil).CheckProjectAuth), varargs...)
}

// CheckProjectAuthSHA256 mocks base method.
func (m *MockMerchantClient) CheckProjectAuthSHA256(ctx context.Context, in *grpc.CheckProjectAuthSHA256RequestV1, opts ...grpc0.CallOption) (*grpc.CheckProjectAuthResponseV1, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "CheckProjectAuthSHA256", varargs...)
	ret0, _ := ret[0].(*grpc.CheckProjectAuthResponseV1)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CheckProjectAuthSHA256 indicates an expected call of CheckProjectAuthSHA256.
func (mr *MockMerchantClientMockRecorder) CheckProjectAuthSHA256(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CheckProjectAuthSHA256", reflect.TypeOf((*MockMerchantClient)(nil).CheckProjectAuthSHA256), varargs...)
}

// GenerateProjectHash mocks base method.
func (m *MockMerchantClient) GenerateProjectHash(ctx context.Context, in *grpc.GenerateProjectHashRequestV1, opts ...grpc0.CallOption) (*grpc.GenerateProjectHashResponseV1, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GenerateProjectHash", varargs...)
	ret0, _ := ret[0].(*grpc.GenerateProjectHashResponseV1)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GenerateProjectHash indicates an expected call of GenerateProjectHash.
func (mr *MockMerchantClientMockRecorder) GenerateProjectHash(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GenerateProjectHash", reflect.TypeOf((*MockMerchantClient)(nil).GenerateProjectHash), varargs...)
}

// GenerateProjectSHA256Hash mocks base method.
func (m *MockMerchantClient) GenerateProjectSHA256Hash(ctx context.Context, in *grpc.GeneratePayloadHashRequestV1, opts ...grpc0.CallOption) (*grpc.GenerateProjectHashResponseV1, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GenerateProjectSHA256Hash", varargs...)
	ret0, _ := ret[0].(*grpc.GenerateProjectHashResponseV1)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GenerateProjectSHA256Hash indicates an expected call of GenerateProjectSHA256Hash.
func (mr *MockMerchantClientMockRecorder) GenerateProjectSHA256Hash(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GenerateProjectSHA256Hash", reflect.TypeOf((*MockMerchantClient)(nil).GenerateProjectSHA256Hash), varargs...)
}

// GetMerchantDataByID mocks base method.
func (m *MockMerchantClient) GetMerchantDataByID(ctx context.Context, in *grpc.GetMerchantDataByIDRequestV1, opts ...grpc0.CallOption) (*grpc.MerchantData, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetMerchantDataByID", varargs...)
	ret0, _ := ret[0].(*grpc.MerchantData)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetMerchantDataByID indicates an expected call of GetMerchantDataByID.
func (mr *MockMerchantClientMockRecorder) GetMerchantDataByID(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetMerchantDataByID", reflect.TypeOf((*MockMerchantClient)(nil).GetMerchantDataByID), varargs...)
}

// GetMerchantInfo mocks base method.
func (m *MockMerchantClient) GetMerchantInfo(ctx context.Context, in *grpc.GetMerchantInfoRequestV1, opts ...grpc0.CallOption) (*grpc.GetMerchantInfoResponseV1, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetMerchantInfo", varargs...)
	ret0, _ := ret[0].(*grpc.GetMerchantInfoResponseV1)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetMerchantInfo indicates an expected call of GetMerchantInfo.
func (mr *MockMerchantClientMockRecorder) GetMerchantInfo(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetMerchantInfo", reflect.TypeOf((*MockMerchantClient)(nil).GetMerchantInfo), varargs...)
}

// GetMerchantProjectsByBin mocks base method.
func (m *MockMerchantClient) GetMerchantProjectsByBin(ctx context.Context, in *grpc.GetMerchantProjectsByBinRequestV1, opts ...grpc0.CallOption) (*grpc.GetMerchantProjectsByBinResponseV1, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetMerchantProjectsByBin", varargs...)
	ret0, _ := ret[0].(*grpc.GetMerchantProjectsByBinResponseV1)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetMerchantProjectsByBin indicates an expected call of GetMerchantProjectsByBin.
func (mr *MockMerchantClientMockRecorder) GetMerchantProjectsByBin(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetMerchantProjectsByBin", reflect.TypeOf((*MockMerchantClient)(nil).GetMerchantProjectsByBin), varargs...)
}

// GetProject mocks base method.
func (m *MockMerchantClient) GetProject(ctx context.Context, in *grpc.ProjectRequestV1, opts ...grpc0.CallOption) (*grpc.ProjectResponseV1, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetProject", varargs...)
	ret0, _ := ret[0].(*grpc.ProjectResponseV1)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetProject indicates an expected call of GetProject.
func (mr *MockMerchantClientMockRecorder) GetProject(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetProject", reflect.TypeOf((*MockMerchantClient)(nil).GetProject), varargs...)
}

// MockMerchantServer is a mock of MerchantServer interface.
type MockMerchantServer struct {
	ctrl     *gomock.Controller
	recorder *MockMerchantServerMockRecorder
}

// MockMerchantServerMockRecorder is the mock recorder for MockMerchantServer.
type MockMerchantServerMockRecorder struct {
	mock *MockMerchantServer
}

// NewMockMerchantServer creates a new mock instance.
func NewMockMerchantServer(ctrl *gomock.Controller) *MockMerchantServer {
	mock := &MockMerchantServer{ctrl: ctrl}
	mock.recorder = &MockMerchantServerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockMerchantServer) EXPECT() *MockMerchantServerMockRecorder {
	return m.recorder
}

// CheckProject mocks base method.
func (m *MockMerchantServer) CheckProject(arg0 context.Context, arg1 *grpc.CheckMerchantProjectRequestV1) (*emptypb.Empty, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CheckProject", arg0, arg1)
	ret0, _ := ret[0].(*emptypb.Empty)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CheckProject indicates an expected call of CheckProject.
func (mr *MockMerchantServerMockRecorder) CheckProject(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CheckProject", reflect.TypeOf((*MockMerchantServer)(nil).CheckProject), arg0, arg1)
}

// CheckProjectAuth mocks base method.
func (m *MockMerchantServer) CheckProjectAuth(arg0 context.Context, arg1 *grpc.CheckProjectAuthRequestV1) (*grpc.CheckProjectAuthResponseV1, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CheckProjectAuth", arg0, arg1)
	ret0, _ := ret[0].(*grpc.CheckProjectAuthResponseV1)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CheckProjectAuth indicates an expected call of CheckProjectAuth.
func (mr *MockMerchantServerMockRecorder) CheckProjectAuth(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CheckProjectAuth", reflect.TypeOf((*MockMerchantServer)(nil).CheckProjectAuth), arg0, arg1)
}

// CheckProjectAuthSHA256 mocks base method.
func (m *MockMerchantServer) CheckProjectAuthSHA256(arg0 context.Context, arg1 *grpc.CheckProjectAuthSHA256RequestV1) (*grpc.CheckProjectAuthResponseV1, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CheckProjectAuthSHA256", arg0, arg1)
	ret0, _ := ret[0].(*grpc.CheckProjectAuthResponseV1)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CheckProjectAuthSHA256 indicates an expected call of CheckProjectAuthSHA256.
func (mr *MockMerchantServerMockRecorder) CheckProjectAuthSHA256(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CheckProjectAuthSHA256", reflect.TypeOf((*MockMerchantServer)(nil).CheckProjectAuthSHA256), arg0, arg1)
}

// GenerateProjectHash mocks base method.
func (m *MockMerchantServer) GenerateProjectHash(arg0 context.Context, arg1 *grpc.GenerateProjectHashRequestV1) (*grpc.GenerateProjectHashResponseV1, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GenerateProjectHash", arg0, arg1)
	ret0, _ := ret[0].(*grpc.GenerateProjectHashResponseV1)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GenerateProjectHash indicates an expected call of GenerateProjectHash.
func (mr *MockMerchantServerMockRecorder) GenerateProjectHash(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GenerateProjectHash", reflect.TypeOf((*MockMerchantServer)(nil).GenerateProjectHash), arg0, arg1)
}

// GenerateProjectSHA256Hash mocks base method.
func (m *MockMerchantServer) GenerateProjectSHA256Hash(arg0 context.Context, arg1 *grpc.GeneratePayloadHashRequestV1) (*grpc.GenerateProjectHashResponseV1, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GenerateProjectSHA256Hash", arg0, arg1)
	ret0, _ := ret[0].(*grpc.GenerateProjectHashResponseV1)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GenerateProjectSHA256Hash indicates an expected call of GenerateProjectSHA256Hash.
func (mr *MockMerchantServerMockRecorder) GenerateProjectSHA256Hash(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GenerateProjectSHA256Hash", reflect.TypeOf((*MockMerchantServer)(nil).GenerateProjectSHA256Hash), arg0, arg1)
}

// GetMerchantDataByID mocks base method.
func (m *MockMerchantServer) GetMerchantDataByID(arg0 context.Context, arg1 *grpc.GetMerchantDataByIDRequestV1) (*grpc.MerchantData, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetMerchantDataByID", arg0, arg1)
	ret0, _ := ret[0].(*grpc.MerchantData)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetMerchantDataByID indicates an expected call of GetMerchantDataByID.
func (mr *MockMerchantServerMockRecorder) GetMerchantDataByID(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetMerchantDataByID", reflect.TypeOf((*MockMerchantServer)(nil).GetMerchantDataByID), arg0, arg1)
}

// GetMerchantInfo mocks base method.
func (m *MockMerchantServer) GetMerchantInfo(arg0 context.Context, arg1 *grpc.GetMerchantInfoRequestV1) (*grpc.GetMerchantInfoResponseV1, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetMerchantInfo", arg0, arg1)
	ret0, _ := ret[0].(*grpc.GetMerchantInfoResponseV1)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetMerchantInfo indicates an expected call of GetMerchantInfo.
func (mr *MockMerchantServerMockRecorder) GetMerchantInfo(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetMerchantInfo", reflect.TypeOf((*MockMerchantServer)(nil).GetMerchantInfo), arg0, arg1)
}

// GetMerchantProjectsByBin mocks base method.
func (m *MockMerchantServer) GetMerchantProjectsByBin(arg0 context.Context, arg1 *grpc.GetMerchantProjectsByBinRequestV1) (*grpc.GetMerchantProjectsByBinResponseV1, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetMerchantProjectsByBin", arg0, arg1)
	ret0, _ := ret[0].(*grpc.GetMerchantProjectsByBinResponseV1)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetMerchantProjectsByBin indicates an expected call of GetMerchantProjectsByBin.
func (mr *MockMerchantServerMockRecorder) GetMerchantProjectsByBin(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetMerchantProjectsByBin", reflect.TypeOf((*MockMerchantServer)(nil).GetMerchantProjectsByBin), arg0, arg1)
}

// GetProject mocks base method.
func (m *MockMerchantServer) GetProject(arg0 context.Context, arg1 *grpc.ProjectRequestV1) (*grpc.ProjectResponseV1, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetProject", arg0, arg1)
	ret0, _ := ret[0].(*grpc.ProjectResponseV1)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetProject indicates an expected call of GetProject.
func (mr *MockMerchantServerMockRecorder) GetProject(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetProject", reflect.TypeOf((*MockMerchantServer)(nil).GetProject), arg0, arg1)
}

// mustEmbedUnimplementedMerchantServer mocks base method.
func (m *MockMerchantServer) mustEmbedUnimplementedMerchantServer() {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "mustEmbedUnimplementedMerchantServer")
}

// mustEmbedUnimplementedMerchantServer indicates an expected call of mustEmbedUnimplementedMerchantServer.
func (mr *MockMerchantServerMockRecorder) mustEmbedUnimplementedMerchantServer() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "mustEmbedUnimplementedMerchantServer", reflect.TypeOf((*MockMerchantServer)(nil).mustEmbedUnimplementedMerchantServer))
}

// MockUnsafeMerchantServer is a mock of UnsafeMerchantServer interface.
type MockUnsafeMerchantServer struct {
	ctrl     *gomock.Controller
	recorder *MockUnsafeMerchantServerMockRecorder
}

// MockUnsafeMerchantServerMockRecorder is the mock recorder for MockUnsafeMerchantServer.
type MockUnsafeMerchantServerMockRecorder struct {
	mock *MockUnsafeMerchantServer
}

// NewMockUnsafeMerchantServer creates a new mock instance.
func NewMockUnsafeMerchantServer(ctrl *gomock.Controller) *MockUnsafeMerchantServer {
	mock := &MockUnsafeMerchantServer{ctrl: ctrl}
	mock.recorder = &MockUnsafeMerchantServerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockUnsafeMerchantServer) EXPECT() *MockUnsafeMerchantServerMockRecorder {
	return m.recorder
}

// mustEmbedUnimplementedMerchantServer mocks base method.
func (m *MockUnsafeMerchantServer) mustEmbedUnimplementedMerchantServer() {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "mustEmbedUnimplementedMerchantServer")
}

// mustEmbedUnimplementedMerchantServer indicates an expected call of mustEmbedUnimplementedMerchantServer.
func (mr *MockUnsafeMerchantServerMockRecorder) mustEmbedUnimplementedMerchantServer() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "mustEmbedUnimplementedMerchantServer", reflect.TypeOf((*MockUnsafeMerchantServer)(nil).mustEmbedUnimplementedMerchantServer))
}
