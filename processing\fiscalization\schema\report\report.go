package report

type ReportResponse struct {
	Data ReportData `json:"data"`
}

type (
	ReportData struct {
		DocumentType string `json:"document_"`
		ReportJSON   struct {
			Cashbox  Cashbox  `json:"cashbox"`
			Cashier  Cashier  `json:"cashier"`
			Ofd      Ofd      `json:"ofd"`
			Report   Report   `json:"report"`
			Taxpayer Taxpayer `json:"taxpayer"`
		} `json:"report_json"`
		ReportText []string `json:"report_text"`
		Type       string   `json:""`
		Withdraw   bool     `json:"withdraw"`
	}

	Cashbox struct {
		KKMID        int    `json:"kkm_id"`
		SerialNumber string `json:"serial_number"`
		StateNumber  string `json:"state_number"`
	}

	Cashier struct {
		Code  int    `json:"code"`
		Title string `json:"title"`
	}

	Ofd struct {
		Title   string `json:"title"`
		Website string `json:"web_site"`
	}

	Revenue struct {
		IsNegative bool `json:"is_negative"`
		Sum        struct {
			Bills int `json:"bills"`
			Coins int `json:"coins"`
		} `json:"sum"`
	}

	NonNullableSum struct {
		Operation string `json:"operation"`
		Sum       struct {
			Bills int `json:"bills"`
			Coins int `json:"coins"`
		} `json:"sum"`
	}

	Section struct {
		Operations  []interface{} `json:"operations"`
		SectionCode string        `json:"section_code"`
	}

	Report struct {
		AnnulledTickets interface{} `json:"annulled_tickets"`
		CashSum         struct {
			Bills int `json:"bills"`
			Coins int `json:"coins"`
		} `json:"cash_sum"`
		Checksum          string           `json:"checksum"`
		CloseShiftTime    interface{}      `json:"close_shift_time"`
		DateTime          string           `json:"date_time"`
		Discounts         []interface{}    `json:"discounts"`
		EndShiftCashSum   interface{}      `json:"end_shift_cash_sum"`
		Markups           []interface{}    `json:"markups"`
		MoneyPlacements   []interface{}    `json:"money_placements"`
		NonNullableSums   []NonNullableSum `json:"non_nullable_sums"`
		OpenShiftTime     string           `json:"open_shift_time"`
		Operations        []interface{}    `json:"operations"`
		Revenue           Revenue          `json:"revenue"`
		Sections          []Section        `json:"sections"`
		ShiftNumber       int              `json:"shift_number"`
		StartShiftCashSum struct {
			Bills int `json:"bills"`
			Coins int `json:"coins"`
		} `json:"start_shift_cash_sum"`
		StartShiftNonNullableSums []NonNullableSum `json:"start_shift_non_nullable_sums"`
		Taxes                     []interface{}    `json:"taxes"`
		TicketOperations          []interface{}    `json:"ticket_operations"`
		TotalResult               []interface{}    `json:"total_result"`
	}

	Taxpayer struct {
		Code         string      `json:"code"`
		StoreAddress string      `json:"store_address"`
		Title        string      `json:"title"`
		VATNumber    interface{} `json:"vat_number"`
		VATSeries    interface{} `json:"vat_series"`
	}
)
