// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.5.1
// - protoc             v5.29.3
// source: inner/processing/grpc/multiacquiring_emission.proto

package grpc

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.64.0 or later.
const _ = grpc.SupportPackageIsVersion9

const (
	MultiacquiringEmission_GetEmission_FullMethodName     = "/processing.multiacquiring.multiacquiring_emission.MultiacquiringEmission/GetEmission"
	MultiacquiringEmission_ConfirmEmission_FullMethodName = "/processing.multiacquiring.multiacquiring_emission.MultiacquiringEmission/ConfirmEmission"
)

// MultiacquiringEmissionClient is the client API for MultiacquiringEmission service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type MultiacquiringEmissionClient interface {
	GetEmission(ctx context.Context, in *emptypb.Empty, opts ...grpc.CallOption) (*EmissionResponse, error)
	ConfirmEmission(ctx context.Context, in *EmoneyRequest, opts ...grpc.CallOption) (*EmoneyResponse, error)
}

type multiacquiringEmissionClient struct {
	cc grpc.ClientConnInterface
}

func NewMultiacquiringEmissionClient(cc grpc.ClientConnInterface) MultiacquiringEmissionClient {
	return &multiacquiringEmissionClient{cc}
}

func (c *multiacquiringEmissionClient) GetEmission(ctx context.Context, in *emptypb.Empty, opts ...grpc.CallOption) (*EmissionResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(EmissionResponse)
	err := c.cc.Invoke(ctx, MultiacquiringEmission_GetEmission_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *multiacquiringEmissionClient) ConfirmEmission(ctx context.Context, in *EmoneyRequest, opts ...grpc.CallOption) (*EmoneyResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(EmoneyResponse)
	err := c.cc.Invoke(ctx, MultiacquiringEmission_ConfirmEmission_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// MultiacquiringEmissionServer is the server API for MultiacquiringEmission service.
// All implementations must embed UnimplementedMultiacquiringEmissionServer
// for forward compatibility.
type MultiacquiringEmissionServer interface {
	GetEmission(context.Context, *emptypb.Empty) (*EmissionResponse, error)
	ConfirmEmission(context.Context, *EmoneyRequest) (*EmoneyResponse, error)
	mustEmbedUnimplementedMultiacquiringEmissionServer()
}

// UnimplementedMultiacquiringEmissionServer must be embedded to have
// forward compatible implementations.
//
// NOTE: this should be embedded by value instead of pointer to avoid a nil
// pointer dereference when methods are called.
type UnimplementedMultiacquiringEmissionServer struct{}

func (UnimplementedMultiacquiringEmissionServer) GetEmission(context.Context, *emptypb.Empty) (*EmissionResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetEmission not implemented")
}
func (UnimplementedMultiacquiringEmissionServer) ConfirmEmission(context.Context, *EmoneyRequest) (*EmoneyResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ConfirmEmission not implemented")
}
func (UnimplementedMultiacquiringEmissionServer) mustEmbedUnimplementedMultiacquiringEmissionServer() {
}
func (UnimplementedMultiacquiringEmissionServer) testEmbeddedByValue() {}

// UnsafeMultiacquiringEmissionServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to MultiacquiringEmissionServer will
// result in compilation errors.
type UnsafeMultiacquiringEmissionServer interface {
	mustEmbedUnimplementedMultiacquiringEmissionServer()
}

func RegisterMultiacquiringEmissionServer(s grpc.ServiceRegistrar, srv MultiacquiringEmissionServer) {
	// If the following call pancis, it indicates UnimplementedMultiacquiringEmissionServer was
	// embedded by pointer and is nil.  This will cause panics if an
	// unimplemented method is ever invoked, so we test this at initialization
	// time to prevent it from happening at runtime later due to I/O.
	if t, ok := srv.(interface{ testEmbeddedByValue() }); ok {
		t.testEmbeddedByValue()
	}
	s.RegisterService(&MultiacquiringEmission_ServiceDesc, srv)
}

func _MultiacquiringEmission_GetEmission_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(emptypb.Empty)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MultiacquiringEmissionServer).GetEmission(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: MultiacquiringEmission_GetEmission_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MultiacquiringEmissionServer).GetEmission(ctx, req.(*emptypb.Empty))
	}
	return interceptor(ctx, in, info, handler)
}

func _MultiacquiringEmission_ConfirmEmission_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(EmoneyRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MultiacquiringEmissionServer).ConfirmEmission(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: MultiacquiringEmission_ConfirmEmission_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MultiacquiringEmissionServer).ConfirmEmission(ctx, req.(*EmoneyRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// MultiacquiringEmission_ServiceDesc is the grpc.ServiceDesc for MultiacquiringEmission service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var MultiacquiringEmission_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "processing.multiacquiring.multiacquiring_emission.MultiacquiringEmission",
	HandlerType: (*MultiacquiringEmissionServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetEmission",
			Handler:    _MultiacquiringEmission_GetEmission_Handler,
		},
		{
			MethodName: "ConfirmEmission",
			Handler:    _MultiacquiringEmission_ConfirmEmission_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "inner/processing/grpc/multiacquiring_emission.proto",
}
