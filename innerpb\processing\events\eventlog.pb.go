// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.5
// 	protoc        v5.29.3
// source: inner/processing/events/eventlog.proto

package events

import (
	_ "git.local/sensitive/mvp/pb"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type SaveLog struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Method        string                 `protobuf:"bytes,1,opt,name=method,proto3" json:"method,omitempty"`
	Url           string                 `protobuf:"bytes,2,opt,name=url,proto3" json:"url,omitempty"`
	EventAt       *timestamppb.Timestamp `protobuf:"bytes,3,opt,name=event_at,json=eventAt,proto3" json:"event_at,omitempty"`
	EntityId      string                 `protobuf:"bytes,4,opt,name=entity_id,json=entityId,proto3" json:"entity_id,omitempty"`
	Request       string                 `protobuf:"bytes,5,opt,name=request,proto3" json:"request,omitempty"`
	UserEmail     string                 `protobuf:"bytes,6,opt,name=user_email,json=userEmail,proto3" json:"user_email,omitempty"`
	UserId        string                 `protobuf:"bytes,7,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	Message       string                 `protobuf:"bytes,8,opt,name=message,proto3" json:"message,omitempty"`
	Payload       string                 `protobuf:"bytes,9,opt,name=payload,proto3" json:"payload,omitempty"`
	Collection    string                 `protobuf:"bytes,10,opt,name=collection,proto3" json:"collection,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SaveLog) Reset() {
	*x = SaveLog{}
	mi := &file_inner_processing_events_eventlog_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SaveLog) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SaveLog) ProtoMessage() {}

func (x *SaveLog) ProtoReflect() protoreflect.Message {
	mi := &file_inner_processing_events_eventlog_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SaveLog.ProtoReflect.Descriptor instead.
func (*SaveLog) Descriptor() ([]byte, []int) {
	return file_inner_processing_events_eventlog_proto_rawDescGZIP(), []int{0}
}

func (x *SaveLog) GetMethod() string {
	if x != nil {
		return x.Method
	}
	return ""
}

func (x *SaveLog) GetUrl() string {
	if x != nil {
		return x.Url
	}
	return ""
}

func (x *SaveLog) GetEventAt() *timestamppb.Timestamp {
	if x != nil {
		return x.EventAt
	}
	return nil
}

func (x *SaveLog) GetEntityId() string {
	if x != nil {
		return x.EntityId
	}
	return ""
}

func (x *SaveLog) GetRequest() string {
	if x != nil {
		return x.Request
	}
	return ""
}

func (x *SaveLog) GetUserEmail() string {
	if x != nil {
		return x.UserEmail
	}
	return ""
}

func (x *SaveLog) GetUserId() string {
	if x != nil {
		return x.UserId
	}
	return ""
}

func (x *SaveLog) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

func (x *SaveLog) GetPayload() string {
	if x != nil {
		return x.Payload
	}
	return ""
}

func (x *SaveLog) GetCollection() string {
	if x != nil {
		return x.Collection
	}
	return ""
}

var File_inner_processing_events_eventlog_proto protoreflect.FileDescriptor

var file_inner_processing_events_eventlog_proto_rawDesc = string([]byte{
	0x0a, 0x26, 0x69, 0x6e, 0x6e, 0x65, 0x72, 0x2f, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69,
	0x6e, 0x67, 0x2f, 0x65, 0x76, 0x65, 0x6e, 0x74, 0x73, 0x2f, 0x65, 0x76, 0x65, 0x6e, 0x74, 0x6c,
	0x6f, 0x67, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1f, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74,
	0x61, 0x6d, 0x70, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x16, 0x6d, 0x76, 0x70, 0x2f, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x2f, 0x65, 0x76, 0x65, 0x6e, 0x74, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x22, 0xb4, 0x02, 0x0a, 0x07, 0x53, 0x61, 0x76, 0x65, 0x4c, 0x6f, 0x67, 0x12, 0x16, 0x0a,
	0x06, 0x6d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x6d,
	0x65, 0x74, 0x68, 0x6f, 0x64, 0x12, 0x10, 0x0a, 0x03, 0x75, 0x72, 0x6c, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x03, 0x75, 0x72, 0x6c, 0x12, 0x35, 0x0a, 0x08, 0x65, 0x76, 0x65, 0x6e, 0x74,
	0x5f, 0x61, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65,
	0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x07, 0x65, 0x76, 0x65, 0x6e, 0x74, 0x41, 0x74, 0x12, 0x1b,
	0x0a, 0x09, 0x65, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x5f, 0x69, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x08, 0x65, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x49, 0x64, 0x12, 0x18, 0x0a, 0x07, 0x72,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x72, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1d, 0x0a, 0x0a, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x65, 0x6d,
	0x61, 0x69, 0x6c, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x75, 0x73, 0x65, 0x72, 0x45,
	0x6d, 0x61, 0x69, 0x6c, 0x12, 0x17, 0x0a, 0x07, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18,
	0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x12, 0x18, 0x0a,
	0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07,
	0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x70, 0x61, 0x79, 0x6c, 0x6f,
	0x61, 0x64, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x70, 0x61, 0x79, 0x6c, 0x6f, 0x61,
	0x64, 0x12, 0x1e, 0x0a, 0x0a, 0x63, 0x6f, 0x6c, 0x6c, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x18,
	0x0a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x63, 0x6f, 0x6c, 0x6c, 0x65, 0x63, 0x74, 0x69, 0x6f,
	0x6e, 0x3a, 0x05, 0xb8, 0xe9, 0xe4, 0x03, 0x01, 0x42, 0x2f, 0x5a, 0x2d, 0x67, 0x69, 0x74, 0x2e,
	0x6c, 0x6f, 0x63, 0x61, 0x6c, 0x2f, 0x73, 0x65, 0x6e, 0x73, 0x69, 0x74, 0x69, 0x76, 0x65, 0x2f,
	0x69, 0x6e, 0x6e, 0x65, 0x72, 0x70, 0x62, 0x2f, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69,
	0x6e, 0x67, 0x2f, 0x65, 0x76, 0x65, 0x6e, 0x74, 0x73, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x33,
})

var (
	file_inner_processing_events_eventlog_proto_rawDescOnce sync.Once
	file_inner_processing_events_eventlog_proto_rawDescData []byte
)

func file_inner_processing_events_eventlog_proto_rawDescGZIP() []byte {
	file_inner_processing_events_eventlog_proto_rawDescOnce.Do(func() {
		file_inner_processing_events_eventlog_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_inner_processing_events_eventlog_proto_rawDesc), len(file_inner_processing_events_eventlog_proto_rawDesc)))
	})
	return file_inner_processing_events_eventlog_proto_rawDescData
}

var file_inner_processing_events_eventlog_proto_msgTypes = make([]protoimpl.MessageInfo, 1)
var file_inner_processing_events_eventlog_proto_goTypes = []any{
	(*SaveLog)(nil),               // 0: SaveLog
	(*timestamppb.Timestamp)(nil), // 1: google.protobuf.Timestamp
}
var file_inner_processing_events_eventlog_proto_depIdxs = []int32{
	1, // 0: SaveLog.event_at:type_name -> google.protobuf.Timestamp
	1, // [1:1] is the sub-list for method output_type
	1, // [1:1] is the sub-list for method input_type
	1, // [1:1] is the sub-list for extension type_name
	1, // [1:1] is the sub-list for extension extendee
	0, // [0:1] is the sub-list for field type_name
}

func init() { file_inner_processing_events_eventlog_proto_init() }
func file_inner_processing_events_eventlog_proto_init() {
	if File_inner_processing_events_eventlog_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_inner_processing_events_eventlog_proto_rawDesc), len(file_inner_processing_events_eventlog_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   1,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_inner_processing_events_eventlog_proto_goTypes,
		DependencyIndexes: file_inner_processing_events_eventlog_proto_depIdxs,
		MessageInfos:      file_inner_processing_events_eventlog_proto_msgTypes,
	}.Build()
	File_inner_processing_events_eventlog_proto = out.File
	file_inner_processing_events_eventlog_proto_goTypes = nil
	file_inner_processing_events_eventlog_proto_depIdxs = nil
}
