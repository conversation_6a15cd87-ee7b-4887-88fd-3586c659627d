package model

type Account struct {
	TimestampMixin
	ID              uint64 `gorm:"primaryKey;column:id" json:"id"`
	Number          string `gorm:"column:number" json:"number"`
	CurrencyCode    string `gorm:"currency_code" json:"currency_code"`
	BankCode        string `gorm:"bank_code" json:"bank_code"`
	AccountTypeID   uint64 `gorm:"account_type_id" json:"account_type_id"`
	EncryptedConfig string `gorm:"column:encrypted_config" json:"-"`
	BankID          uint64 `gorm:"bank_id" json:"bank_id"`
}

func (a Account) TableName() string {
	return "account.accounts"
}
