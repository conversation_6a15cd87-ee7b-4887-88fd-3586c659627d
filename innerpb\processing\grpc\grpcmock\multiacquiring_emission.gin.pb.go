// Code generated by MockGen. DO NOT EDIT.
// Source: multiacquiring_emission.gin.pb.go

// Package grpcmock is a generated GoMock package.
package grpcmock

import (
	reflect "reflect"

	gin "github.com/gin-gonic/gin"
	gomock "github.com/golang/mock/gomock"
)

// MockGinMultiacquiringEmissionServer is a mock of GinMultiacquiringEmissionServer interface.
type MockGinMultiacquiringEmissionServer struct {
	ctrl     *gomock.Controller
	recorder *MockGinMultiacquiringEmissionServerMockRecorder
}

// MockGinMultiacquiringEmissionServerMockRecorder is the mock recorder for MockGinMultiacquiringEmissionServer.
type MockGinMultiacquiringEmissionServerMockRecorder struct {
	mock *MockGinMultiacquiringEmissionServer
}

// NewMockGinMultiacquiringEmissionServer creates a new mock instance.
func NewMockGinMultiacquiringEmissionServer(ctrl *gomock.Controller) *MockGinMultiacquiringEmissionServer {
	mock := &MockGinMultiacquiringEmissionServer{ctrl: ctrl}
	mock.recorder = &MockGinMultiacquiringEmissionServerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockGinMultiacquiringEmissionServer) EXPECT() *MockGinMultiacquiringEmissionServerMockRecorder {
	return m.recorder
}

// ConfirmEmission mocks base method.
func (m *MockGinMultiacquiringEmissionServer) ConfirmEmission(c *gin.Context) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ConfirmEmission", c)
	ret0, _ := ret[0].(error)
	return ret0
}

// ConfirmEmission indicates an expected call of ConfirmEmission.
func (mr *MockGinMultiacquiringEmissionServerMockRecorder) ConfirmEmission(c interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ConfirmEmission", reflect.TypeOf((*MockGinMultiacquiringEmissionServer)(nil).ConfirmEmission), c)
}

// GetEmission mocks base method.
func (m *MockGinMultiacquiringEmissionServer) GetEmission(c *gin.Context) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetEmission", c)
	ret0, _ := ret[0].(error)
	return ret0
}

// GetEmission indicates an expected call of GetEmission.
func (mr *MockGinMultiacquiringEmissionServerMockRecorder) GetEmission(c interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetEmission", reflect.TypeOf((*MockGinMultiacquiringEmissionServer)(nil).GetEmission), c)
}
