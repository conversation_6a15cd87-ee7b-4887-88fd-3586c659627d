// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.5
// 	protoc        v5.29.3
// source: inner/processing/grpc/project_transactions.proto

package grpc

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type CheckAmountLimitRequestV1 struct {
	state             protoimpl.MessageState `protogen:"open.v1"`
	ProjectId         *uint64                `protobuf:"varint,1,opt,name=project_id,json=projectId" json:"project_id,omitempty"`
	TransactionTypeId *uint64                `protobuf:"varint,2,opt,name=transaction_type_id,json=transactionTypeId" json:"transaction_type_id,omitempty"`
	TransactionAmount *float64               `protobuf:"fixed64,3,opt,name=transaction_amount,json=transactionAmount" json:"transaction_amount,omitempty"`
	unknownFields     protoimpl.UnknownFields
	sizeCache         protoimpl.SizeCache
}

func (x *CheckAmountLimitRequestV1) Reset() {
	*x = CheckAmountLimitRequestV1{}
	mi := &file_inner_processing_grpc_project_transactions_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CheckAmountLimitRequestV1) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CheckAmountLimitRequestV1) ProtoMessage() {}

func (x *CheckAmountLimitRequestV1) ProtoReflect() protoreflect.Message {
	mi := &file_inner_processing_grpc_project_transactions_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CheckAmountLimitRequestV1.ProtoReflect.Descriptor instead.
func (*CheckAmountLimitRequestV1) Descriptor() ([]byte, []int) {
	return file_inner_processing_grpc_project_transactions_proto_rawDescGZIP(), []int{0}
}

func (x *CheckAmountLimitRequestV1) GetProjectId() uint64 {
	if x != nil && x.ProjectId != nil {
		return *x.ProjectId
	}
	return 0
}

func (x *CheckAmountLimitRequestV1) GetTransactionTypeId() uint64 {
	if x != nil && x.TransactionTypeId != nil {
		return *x.TransactionTypeId
	}
	return 0
}

func (x *CheckAmountLimitRequestV1) GetTransactionAmount() float64 {
	if x != nil && x.TransactionAmount != nil {
		return *x.TransactionAmount
	}
	return 0
}

type CheckAmountLimitResponseV1 struct {
	state          protoimpl.MessageState `protogen:"open.v1"`
	VerifiedAmount *bool                  `protobuf:"varint,1,opt,name=verified_amount,json=verifiedAmount" json:"verified_amount,omitempty"`
	unknownFields  protoimpl.UnknownFields
	sizeCache      protoimpl.SizeCache
}

func (x *CheckAmountLimitResponseV1) Reset() {
	*x = CheckAmountLimitResponseV1{}
	mi := &file_inner_processing_grpc_project_transactions_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CheckAmountLimitResponseV1) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CheckAmountLimitResponseV1) ProtoMessage() {}

func (x *CheckAmountLimitResponseV1) ProtoReflect() protoreflect.Message {
	mi := &file_inner_processing_grpc_project_transactions_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CheckAmountLimitResponseV1.ProtoReflect.Descriptor instead.
func (*CheckAmountLimitResponseV1) Descriptor() ([]byte, []int) {
	return file_inner_processing_grpc_project_transactions_proto_rawDescGZIP(), []int{1}
}

func (x *CheckAmountLimitResponseV1) GetVerifiedAmount() bool {
	if x != nil && x.VerifiedAmount != nil {
		return *x.VerifiedAmount
	}
	return false
}

type CheckAttemptsWithinTimeoutRequestV1 struct {
	state               protoimpl.MessageState `protogen:"open.v1"`
	ProjectId           *uint64                `protobuf:"varint,1,opt,name=project_id,json=projectId" json:"project_id,omitempty"`
	TransactionTypeId   *uint64                `protobuf:"varint,2,opt,name=transaction_type_id,json=transactionTypeId" json:"transaction_type_id,omitempty"`
	Try                 *uint64                `protobuf:"varint,3,opt,name=try" json:"try,omitempty"`
	TransactionLifeTime *uint64                `protobuf:"varint,4,opt,name=transaction_life_time,json=transactionLifeTime" json:"transaction_life_time,omitempty"`
	unknownFields       protoimpl.UnknownFields
	sizeCache           protoimpl.SizeCache
}

func (x *CheckAttemptsWithinTimeoutRequestV1) Reset() {
	*x = CheckAttemptsWithinTimeoutRequestV1{}
	mi := &file_inner_processing_grpc_project_transactions_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CheckAttemptsWithinTimeoutRequestV1) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CheckAttemptsWithinTimeoutRequestV1) ProtoMessage() {}

func (x *CheckAttemptsWithinTimeoutRequestV1) ProtoReflect() protoreflect.Message {
	mi := &file_inner_processing_grpc_project_transactions_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CheckAttemptsWithinTimeoutRequestV1.ProtoReflect.Descriptor instead.
func (*CheckAttemptsWithinTimeoutRequestV1) Descriptor() ([]byte, []int) {
	return file_inner_processing_grpc_project_transactions_proto_rawDescGZIP(), []int{2}
}

func (x *CheckAttemptsWithinTimeoutRequestV1) GetProjectId() uint64 {
	if x != nil && x.ProjectId != nil {
		return *x.ProjectId
	}
	return 0
}

func (x *CheckAttemptsWithinTimeoutRequestV1) GetTransactionTypeId() uint64 {
	if x != nil && x.TransactionTypeId != nil {
		return *x.TransactionTypeId
	}
	return 0
}

func (x *CheckAttemptsWithinTimeoutRequestV1) GetTry() uint64 {
	if x != nil && x.Try != nil {
		return *x.Try
	}
	return 0
}

func (x *CheckAttemptsWithinTimeoutRequestV1) GetTransactionLifeTime() uint64 {
	if x != nil && x.TransactionLifeTime != nil {
		return *x.TransactionLifeTime
	}
	return 0
}

type CheckAttemptsWithinTimeoutResponseV1 struct {
	state           protoimpl.MessageState `protogen:"open.v1"`
	VerifiedTimeout *bool                  `protobuf:"varint,1,opt,name=verified_timeout,json=verifiedTimeout" json:"verified_timeout,omitempty"`
	VerifiedTry     *bool                  `protobuf:"varint,2,opt,name=verified_try,json=verifiedTry" json:"verified_try,omitempty"`
	unknownFields   protoimpl.UnknownFields
	sizeCache       protoimpl.SizeCache
}

func (x *CheckAttemptsWithinTimeoutResponseV1) Reset() {
	*x = CheckAttemptsWithinTimeoutResponseV1{}
	mi := &file_inner_processing_grpc_project_transactions_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CheckAttemptsWithinTimeoutResponseV1) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CheckAttemptsWithinTimeoutResponseV1) ProtoMessage() {}

func (x *CheckAttemptsWithinTimeoutResponseV1) ProtoReflect() protoreflect.Message {
	mi := &file_inner_processing_grpc_project_transactions_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CheckAttemptsWithinTimeoutResponseV1.ProtoReflect.Descriptor instead.
func (*CheckAttemptsWithinTimeoutResponseV1) Descriptor() ([]byte, []int) {
	return file_inner_processing_grpc_project_transactions_proto_rawDescGZIP(), []int{3}
}

func (x *CheckAttemptsWithinTimeoutResponseV1) GetVerifiedTimeout() bool {
	if x != nil && x.VerifiedTimeout != nil {
		return *x.VerifiedTimeout
	}
	return false
}

func (x *CheckAttemptsWithinTimeoutResponseV1) GetVerifiedTry() bool {
	if x != nil && x.VerifiedTry != nil {
		return *x.VerifiedTry
	}
	return false
}

type GetTransactionLimitRequestV1 struct {
	state             protoimpl.MessageState `protogen:"open.v1"`
	ProjectId         *uint64                `protobuf:"varint,1,opt,name=project_id,json=projectId" json:"project_id,omitempty"`
	TransactionTypeId *uint64                `protobuf:"varint,2,opt,name=transaction_type_id,json=transactionTypeId" json:"transaction_type_id,omitempty"`
	unknownFields     protoimpl.UnknownFields
	sizeCache         protoimpl.SizeCache
}

func (x *GetTransactionLimitRequestV1) Reset() {
	*x = GetTransactionLimitRequestV1{}
	mi := &file_inner_processing_grpc_project_transactions_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetTransactionLimitRequestV1) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetTransactionLimitRequestV1) ProtoMessage() {}

func (x *GetTransactionLimitRequestV1) ProtoReflect() protoreflect.Message {
	mi := &file_inner_processing_grpc_project_transactions_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetTransactionLimitRequestV1.ProtoReflect.Descriptor instead.
func (*GetTransactionLimitRequestV1) Descriptor() ([]byte, []int) {
	return file_inner_processing_grpc_project_transactions_proto_rawDescGZIP(), []int{4}
}

func (x *GetTransactionLimitRequestV1) GetProjectId() uint64 {
	if x != nil && x.ProjectId != nil {
		return *x.ProjectId
	}
	return 0
}

func (x *GetTransactionLimitRequestV1) GetTransactionTypeId() uint64 {
	if x != nil && x.TransactionTypeId != nil {
		return *x.TransactionTypeId
	}
	return 0
}

type GetTransactionLimitResponseV1 struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	CreatedAt     *timestamppb.Timestamp `protobuf:"bytes,1,opt,name=created_at,json=createdAt" json:"created_at,omitempty"`
	UpdatedAt     *timestamppb.Timestamp `protobuf:"bytes,2,opt,name=updated_at,json=updatedAt" json:"updated_at,omitempty"`
	Id            *uint64                `protobuf:"varint,3,opt,name=id" json:"id,omitempty"`
	ProjectId     *uint64                `protobuf:"varint,4,opt,name=project_id,json=projectId" json:"project_id,omitempty"`
	TypeId        *uint64                `protobuf:"varint,5,opt,name=type_id,json=typeId" json:"type_id,omitempty"`
	Timeout       *uint64                `protobuf:"varint,6,opt,name=timeout" json:"timeout,omitempty"`
	Try           *uint64                `protobuf:"varint,7,opt,name=try" json:"try,omitempty"`
	Amount        *float64               `protobuf:"fixed64,8,opt,name=amount" json:"amount,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetTransactionLimitResponseV1) Reset() {
	*x = GetTransactionLimitResponseV1{}
	mi := &file_inner_processing_grpc_project_transactions_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetTransactionLimitResponseV1) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetTransactionLimitResponseV1) ProtoMessage() {}

func (x *GetTransactionLimitResponseV1) ProtoReflect() protoreflect.Message {
	mi := &file_inner_processing_grpc_project_transactions_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetTransactionLimitResponseV1.ProtoReflect.Descriptor instead.
func (*GetTransactionLimitResponseV1) Descriptor() ([]byte, []int) {
	return file_inner_processing_grpc_project_transactions_proto_rawDescGZIP(), []int{5}
}

func (x *GetTransactionLimitResponseV1) GetCreatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.CreatedAt
	}
	return nil
}

func (x *GetTransactionLimitResponseV1) GetUpdatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.UpdatedAt
	}
	return nil
}

func (x *GetTransactionLimitResponseV1) GetId() uint64 {
	if x != nil && x.Id != nil {
		return *x.Id
	}
	return 0
}

func (x *GetTransactionLimitResponseV1) GetProjectId() uint64 {
	if x != nil && x.ProjectId != nil {
		return *x.ProjectId
	}
	return 0
}

func (x *GetTransactionLimitResponseV1) GetTypeId() uint64 {
	if x != nil && x.TypeId != nil {
		return *x.TypeId
	}
	return 0
}

func (x *GetTransactionLimitResponseV1) GetTimeout() uint64 {
	if x != nil && x.Timeout != nil {
		return *x.Timeout
	}
	return 0
}

func (x *GetTransactionLimitResponseV1) GetTry() uint64 {
	if x != nil && x.Try != nil {
		return *x.Try
	}
	return 0
}

func (x *GetTransactionLimitResponseV1) GetAmount() float64 {
	if x != nil && x.Amount != nil {
		return *x.Amount
	}
	return 0
}

var File_inner_processing_grpc_project_transactions_proto protoreflect.FileDescriptor

var file_inner_processing_grpc_project_transactions_proto_rawDesc = string([]byte{
	0x0a, 0x30, 0x69, 0x6e, 0x6e, 0x65, 0x72, 0x2f, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69,
	0x6e, 0x67, 0x2f, 0x67, 0x72, 0x70, 0x63, 0x2f, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x5f,
	0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x12, 0x28, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x2e, 0x6d,
	0x65, 0x72, 0x63, 0x68, 0x61, 0x6e, 0x74, 0x2e, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x5f,
	0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x1a, 0x1f, 0x67, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x74, 0x69,
	0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x99, 0x01,
	0x0a, 0x19, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x4c, 0x69, 0x6d,
	0x69, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x56, 0x31, 0x12, 0x1d, 0x0a, 0x0a, 0x70,
	0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52,
	0x09, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x49, 0x64, 0x12, 0x2e, 0x0a, 0x13, 0x74, 0x72,
	0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x5f, 0x69,
	0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x04, 0x52, 0x11, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63,
	0x74, 0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x49, 0x64, 0x12, 0x2d, 0x0a, 0x12, 0x74, 0x72,
	0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x01, 0x52, 0x11, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74,
	0x69, 0x6f, 0x6e, 0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x22, 0x45, 0x0a, 0x1a, 0x43, 0x68, 0x65,
	0x63, 0x6b, 0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x4c, 0x69, 0x6d, 0x69, 0x74, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x56, 0x31, 0x12, 0x27, 0x0a, 0x0f, 0x76, 0x65, 0x72, 0x69, 0x66,
	0x69, 0x65, 0x64, 0x5f, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08,
	0x52, 0x0e, 0x76, 0x65, 0x72, 0x69, 0x66, 0x69, 0x65, 0x64, 0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74,
	0x22, 0xba, 0x01, 0x0a, 0x23, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x41, 0x74, 0x74, 0x65, 0x6d, 0x70,
	0x74, 0x73, 0x57, 0x69, 0x74, 0x68, 0x69, 0x6e, 0x54, 0x69, 0x6d, 0x65, 0x6f, 0x75, 0x74, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x56, 0x31, 0x12, 0x1d, 0x0a, 0x0a, 0x70, 0x72, 0x6f, 0x6a,
	0x65, 0x63, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x09, 0x70, 0x72,
	0x6f, 0x6a, 0x65, 0x63, 0x74, 0x49, 0x64, 0x12, 0x2e, 0x0a, 0x13, 0x74, 0x72, 0x61, 0x6e, 0x73,
	0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x04, 0x52, 0x11, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f,
	0x6e, 0x54, 0x79, 0x70, 0x65, 0x49, 0x64, 0x12, 0x10, 0x0a, 0x03, 0x74, 0x72, 0x79, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x04, 0x52, 0x03, 0x74, 0x72, 0x79, 0x12, 0x32, 0x0a, 0x15, 0x74, 0x72, 0x61,
	0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x6c, 0x69, 0x66, 0x65, 0x5f, 0x74, 0x69,
	0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x04, 0x52, 0x13, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61,
	0x63, 0x74, 0x69, 0x6f, 0x6e, 0x4c, 0x69, 0x66, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x22, 0x74, 0x0a,
	0x24, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x41, 0x74, 0x74, 0x65, 0x6d, 0x70, 0x74, 0x73, 0x57, 0x69,
	0x74, 0x68, 0x69, 0x6e, 0x54, 0x69, 0x6d, 0x65, 0x6f, 0x75, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x56, 0x31, 0x12, 0x29, 0x0a, 0x10, 0x76, 0x65, 0x72, 0x69, 0x66, 0x69, 0x65,
	0x64, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x6f, 0x75, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x52,
	0x0f, 0x76, 0x65, 0x72, 0x69, 0x66, 0x69, 0x65, 0x64, 0x54, 0x69, 0x6d, 0x65, 0x6f, 0x75, 0x74,
	0x12, 0x21, 0x0a, 0x0c, 0x76, 0x65, 0x72, 0x69, 0x66, 0x69, 0x65, 0x64, 0x5f, 0x74, 0x72, 0x79,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0b, 0x76, 0x65, 0x72, 0x69, 0x66, 0x69, 0x65, 0x64,
	0x54, 0x72, 0x79, 0x22, 0x6d, 0x0a, 0x1c, 0x47, 0x65, 0x74, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x61,
	0x63, 0x74, 0x69, 0x6f, 0x6e, 0x4c, 0x69, 0x6d, 0x69, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x56, 0x31, 0x12, 0x1d, 0x0a, 0x0a, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x5f, 0x69,
	0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x09, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74,
	0x49, 0x64, 0x12, 0x2e, 0x0a, 0x13, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f,
	0x6e, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x04, 0x52,
	0x11, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65,
	0x49, 0x64, 0x22, 0xa1, 0x02, 0x0a, 0x1d, 0x47, 0x65, 0x74, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x61,
	0x63, 0x74, 0x69, 0x6f, 0x6e, 0x4c, 0x69, 0x6d, 0x69, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x56, 0x31, 0x12, 0x39, 0x0a, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f,
	0x61, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73,
	0x74, 0x61, 0x6d, 0x70, 0x52, 0x09, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x12,
	0x39, 0x0a, 0x0a, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52,
	0x09, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x04, 0x52, 0x02, 0x69, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x70, 0x72,
	0x6f, 0x6a, 0x65, 0x63, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x04, 0x52, 0x09,
	0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x49, 0x64, 0x12, 0x17, 0x0a, 0x07, 0x74, 0x79, 0x70,
	0x65, 0x5f, 0x69, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x04, 0x52, 0x06, 0x74, 0x79, 0x70, 0x65,
	0x49, 0x64, 0x12, 0x18, 0x0a, 0x07, 0x74, 0x69, 0x6d, 0x65, 0x6f, 0x75, 0x74, 0x18, 0x06, 0x20,
	0x01, 0x28, 0x04, 0x52, 0x07, 0x74, 0x69, 0x6d, 0x65, 0x6f, 0x75, 0x74, 0x12, 0x10, 0x0a, 0x03,
	0x74, 0x72, 0x79, 0x18, 0x07, 0x20, 0x01, 0x28, 0x04, 0x52, 0x03, 0x74, 0x72, 0x79, 0x12, 0x16,
	0x0a, 0x06, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x08, 0x20, 0x01, 0x28, 0x01, 0x52, 0x06,
	0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x32, 0xa2, 0x04, 0x0a, 0x13, 0x50, 0x72, 0x6f, 0x6a, 0x65,
	0x63, 0x74, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x12, 0x9f,
	0x01, 0x0a, 0x10, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x4c, 0x69,
	0x6d, 0x69, 0x74, 0x12, 0x43, 0x2e, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67,
	0x2e, 0x6d, 0x65, 0x72, 0x63, 0x68, 0x61, 0x6e, 0x74, 0x2e, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63,
	0x74, 0x5f, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x43,
	0x68, 0x65, 0x63, 0x6b, 0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x4c, 0x69, 0x6d, 0x69, 0x74, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x56, 0x31, 0x1a, 0x44, 0x2e, 0x70, 0x72, 0x6f, 0x63, 0x65,
	0x73, 0x73, 0x69, 0x6e, 0x67, 0x2e, 0x6d, 0x65, 0x72, 0x63, 0x68, 0x61, 0x6e, 0x74, 0x2e, 0x70,
	0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x5f, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69,
	0x6f, 0x6e, 0x73, 0x2e, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x4c,
	0x69, 0x6d, 0x69, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x56, 0x31, 0x22, 0x00,
	0x12, 0xbd, 0x01, 0x0a, 0x1a, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x41, 0x74, 0x74, 0x65, 0x6d, 0x70,
	0x74, 0x73, 0x57, 0x69, 0x74, 0x68, 0x69, 0x6e, 0x54, 0x69, 0x6d, 0x65, 0x6f, 0x75, 0x74, 0x12,
	0x4d, 0x2e, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x2e, 0x6d, 0x65, 0x72,
	0x63, 0x68, 0x61, 0x6e, 0x74, 0x2e, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x5f, 0x74, 0x72,
	0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x43, 0x68, 0x65, 0x63, 0x6b,
	0x41, 0x74, 0x74, 0x65, 0x6d, 0x70, 0x74, 0x73, 0x57, 0x69, 0x74, 0x68, 0x69, 0x6e, 0x54, 0x69,
	0x6d, 0x65, 0x6f, 0x75, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x56, 0x31, 0x1a, 0x4e,
	0x2e, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x2e, 0x6d, 0x65, 0x72, 0x63,
	0x68, 0x61, 0x6e, 0x74, 0x2e, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x5f, 0x74, 0x72, 0x61,
	0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x41,
	0x74, 0x74, 0x65, 0x6d, 0x70, 0x74, 0x73, 0x57, 0x69, 0x74, 0x68, 0x69, 0x6e, 0x54, 0x69, 0x6d,
	0x65, 0x6f, 0x75, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x56, 0x31, 0x22, 0x00,
	0x12, 0xa8, 0x01, 0x0a, 0x13, 0x47, 0x65, 0x74, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74,
	0x69, 0x6f, 0x6e, 0x4c, 0x69, 0x6d, 0x69, 0x74, 0x12, 0x46, 0x2e, 0x70, 0x72, 0x6f, 0x63, 0x65,
	0x73, 0x73, 0x69, 0x6e, 0x67, 0x2e, 0x6d, 0x65, 0x72, 0x63, 0x68, 0x61, 0x6e, 0x74, 0x2e, 0x70,
	0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x5f, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69,
	0x6f, 0x6e, 0x73, 0x2e, 0x47, 0x65, 0x74, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69,
	0x6f, 0x6e, 0x4c, 0x69, 0x6d, 0x69, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x56, 0x31,
	0x1a, 0x47, 0x2e, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x2e, 0x6d, 0x65,
	0x72, 0x63, 0x68, 0x61, 0x6e, 0x74, 0x2e, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x5f, 0x74,
	0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x47, 0x65, 0x74, 0x54,
	0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x4c, 0x69, 0x6d, 0x69, 0x74, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x56, 0x31, 0x22, 0x00, 0x42, 0x2d, 0x5a, 0x2b, 0x67,
	0x69, 0x74, 0x2e, 0x6c, 0x6f, 0x63, 0x61, 0x6c, 0x2f, 0x73, 0x65, 0x6e, 0x73, 0x69, 0x74, 0x69,
	0x76, 0x65, 0x2f, 0x69, 0x6e, 0x6e, 0x65, 0x72, 0x70, 0x62, 0x2f, 0x70, 0x72, 0x6f, 0x63, 0x65,
	0x73, 0x73, 0x69, 0x6e, 0x67, 0x2f, 0x67, 0x72, 0x70, 0x63, 0x62, 0x08, 0x65, 0x64, 0x69, 0x74,
	0x69, 0x6f, 0x6e, 0x73, 0x70, 0xe8, 0x07,
})

var (
	file_inner_processing_grpc_project_transactions_proto_rawDescOnce sync.Once
	file_inner_processing_grpc_project_transactions_proto_rawDescData []byte
)

func file_inner_processing_grpc_project_transactions_proto_rawDescGZIP() []byte {
	file_inner_processing_grpc_project_transactions_proto_rawDescOnce.Do(func() {
		file_inner_processing_grpc_project_transactions_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_inner_processing_grpc_project_transactions_proto_rawDesc), len(file_inner_processing_grpc_project_transactions_proto_rawDesc)))
	})
	return file_inner_processing_grpc_project_transactions_proto_rawDescData
}

var file_inner_processing_grpc_project_transactions_proto_msgTypes = make([]protoimpl.MessageInfo, 6)
var file_inner_processing_grpc_project_transactions_proto_goTypes = []any{
	(*CheckAmountLimitRequestV1)(nil),            // 0: processing.merchant.project_transactions.CheckAmountLimitRequestV1
	(*CheckAmountLimitResponseV1)(nil),           // 1: processing.merchant.project_transactions.CheckAmountLimitResponseV1
	(*CheckAttemptsWithinTimeoutRequestV1)(nil),  // 2: processing.merchant.project_transactions.CheckAttemptsWithinTimeoutRequestV1
	(*CheckAttemptsWithinTimeoutResponseV1)(nil), // 3: processing.merchant.project_transactions.CheckAttemptsWithinTimeoutResponseV1
	(*GetTransactionLimitRequestV1)(nil),         // 4: processing.merchant.project_transactions.GetTransactionLimitRequestV1
	(*GetTransactionLimitResponseV1)(nil),        // 5: processing.merchant.project_transactions.GetTransactionLimitResponseV1
	(*timestamppb.Timestamp)(nil),                // 6: google.protobuf.Timestamp
}
var file_inner_processing_grpc_project_transactions_proto_depIdxs = []int32{
	6, // 0: processing.merchant.project_transactions.GetTransactionLimitResponseV1.created_at:type_name -> google.protobuf.Timestamp
	6, // 1: processing.merchant.project_transactions.GetTransactionLimitResponseV1.updated_at:type_name -> google.protobuf.Timestamp
	0, // 2: processing.merchant.project_transactions.ProjectTransactions.CheckAmountLimit:input_type -> processing.merchant.project_transactions.CheckAmountLimitRequestV1
	2, // 3: processing.merchant.project_transactions.ProjectTransactions.CheckAttemptsWithinTimeout:input_type -> processing.merchant.project_transactions.CheckAttemptsWithinTimeoutRequestV1
	4, // 4: processing.merchant.project_transactions.ProjectTransactions.GetTransactionLimit:input_type -> processing.merchant.project_transactions.GetTransactionLimitRequestV1
	1, // 5: processing.merchant.project_transactions.ProjectTransactions.CheckAmountLimit:output_type -> processing.merchant.project_transactions.CheckAmountLimitResponseV1
	3, // 6: processing.merchant.project_transactions.ProjectTransactions.CheckAttemptsWithinTimeout:output_type -> processing.merchant.project_transactions.CheckAttemptsWithinTimeoutResponseV1
	5, // 7: processing.merchant.project_transactions.ProjectTransactions.GetTransactionLimit:output_type -> processing.merchant.project_transactions.GetTransactionLimitResponseV1
	5, // [5:8] is the sub-list for method output_type
	2, // [2:5] is the sub-list for method input_type
	2, // [2:2] is the sub-list for extension type_name
	2, // [2:2] is the sub-list for extension extendee
	0, // [0:2] is the sub-list for field type_name
}

func init() { file_inner_processing_grpc_project_transactions_proto_init() }
func file_inner_processing_grpc_project_transactions_proto_init() {
	if File_inner_processing_grpc_project_transactions_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_inner_processing_grpc_project_transactions_proto_rawDesc), len(file_inner_processing_grpc_project_transactions_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   6,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_inner_processing_grpc_project_transactions_proto_goTypes,
		DependencyIndexes: file_inner_processing_grpc_project_transactions_proto_depIdxs,
		MessageInfos:      file_inner_processing_grpc_project_transactions_proto_msgTypes,
	}.Build()
	File_inner_processing_grpc_project_transactions_proto = out.File
	file_inner_processing_grpc_project_transactions_proto_goTypes = nil
	file_inner_processing_grpc_project_transactions_proto_depIdxs = nil
}
