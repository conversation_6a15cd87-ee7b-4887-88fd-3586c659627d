edition = "2023";

package processing.mock_acquirer.mock_acquirer;

option go_package = "git.local/sensitive/innerpb/processing/grpc";

import "inner/processing/grpc/multiacquiring.proto";
import "google/protobuf/empty.proto";

service MockAcquirer {
  rpc PayIn(processing.multiacquiring.multiacquiring.PayInRequestData) returns (processing.multiacquiring.multiacquiring.PayInResponseData) {}
  rpc OneClickPayIn(processing.multiacquiring.multiacquiring.OneClickPayInRequestData) returns (processing.multiacquiring.multiacquiring.PayInResponseData) {}
  rpc ThreeDSConfirm(processing.multiacquiring.multiacquiring.ThreeDSRequestData) returns (processing.multiacquiring.multiacquiring.ThreeDSResponseData) {}
  rpc ThreeDSResume(processing.multiacquiring.multiacquiring.ThreeDSResumeRequest) returns (processing.multiacquiring.multiacquiring.ThreeDSResumeResponse) {}
  rpc PayOut(processing.multiacquiring.multiacquiring.PayOutRequestData) returns (processing.multiacquiring.multiacquiring.PayOutResponseData) {}
  rpc GetBankTransactionStatus(processing.multiacquiring.multiacquiring.BankTransactionStatusRequest) returns (processing.multiacquiring.multiacquiring.BankTransactionStatusResponse) {}
  rpc GetBankTransactionStatusUnformated(processing.multiacquiring.multiacquiring.BankTransactionStatusUnformatedRequest) returns (processing.multiacquiring.multiacquiring.BankTransactionStatusUnformatedResponse) {}
  rpc Refund(processing.multiacquiring.multiacquiring.RefundRequest) returns (processing.multiacquiring.multiacquiring.RefundResponse) {}
  rpc GooglePay(processing.multiacquiring.multiacquiring.GooglePayRequestData) returns (processing.multiacquiring.multiacquiring.GooglePayResponseData) {}
  rpc ApplePay(processing.multiacquiring.multiacquiring.ApplePayRequestData) returns (processing.multiacquiring.multiacquiring.ApplePayResponseData) {}
  rpc TwoStagePayIn(processing.multiacquiring.multiacquiring.TwoStagePayInRequest) returns (processing.multiacquiring.multiacquiring.TwoStagePayInResponse) {}
  rpc Charge(processing.multiacquiring.multiacquiring.ChargeRequest) returns (processing.multiacquiring.multiacquiring.ChargeResponse) {}
  rpc Cancel(processing.multiacquiring.multiacquiring.CancelRequest) returns (processing.multiacquiring.multiacquiring.CancelResponse) {}
  rpc MakeToken(processing.multiacquiring.multiacquiring.PayInRequestData) returns (processing.multiacquiring.multiacquiring.PayInResponseData) {}
  rpc GetAcquirerIdentifier(google.protobuf.Empty) returns (processing.multiacquiring.multiacquiring.GetAcquirerIdentifierResponse) {}
  rpc ResolveVisaAlias(processing.multiacquiring.multiacquiring.ResolveVisaAliasRequest) returns (processing.multiacquiring.multiacquiring.ResolveVisaAliasResponse) {}
  rpc PayOutByPhone(processing.multiacquiring.multiacquiring.PayOutByPhoneRequestData) returns (processing.multiacquiring.multiacquiring.PayOutResponseByPhoneData) {}
}