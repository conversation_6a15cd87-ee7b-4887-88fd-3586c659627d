// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.5.1
// - protoc             v5.29.3
// source: inner/processing/grpc/bcc_account.proto

package grpc

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.64.0 or later.
const _ = grpc.SupportPackageIsVersion9

const (
	BCCAccount_MakeMerchantCheck_FullMethodName    = "/processing.bcc_account.bcc_account.BCCAccount/MakeMerchantCheck"
	BCCAccount_MakeTransfer_FullMethodName         = "/processing.bcc_account.bcc_account.BCCAccount/MakeTransfer"
	BCCAccount_AcceptTransfer_FullMethodName       = "/processing.bcc_account.bcc_account.BCCAccount/AcceptTransfer"
	BCCAccount_DeclineTransfer_FullMethodName      = "/processing.bcc_account.bcc_account.BCCAccount/DeclineTransfer"
	BCCAccount_RedoTransfer_FullMethodName         = "/processing.bcc_account.bcc_account.BCCAccount/RedoTransfer"
	BCCAccount_GetTransferDetails_FullMethodName   = "/processing.bcc_account.bcc_account.BCCAccount/GetTransferDetails"
	BCCAccount_GetTransfersList_FullMethodName     = "/processing.bcc_account.bcc_account.BCCAccount/GetTransfersList"
	BCCAccount_GetAccountBalance_FullMethodName    = "/processing.bcc_account.bcc_account.BCCAccount/GetAccountBalance"
	BCCAccount_GetAccountStatement_FullMethodName  = "/processing.bcc_account.bcc_account.BCCAccount/GetAccountStatement"
	BCCAccount_GetAccountIdentifier_FullMethodName = "/processing.bcc_account.bcc_account.BCCAccount/GetAccountIdentifier"
)

// BCCAccountClient is the client API for BCCAccount service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type BCCAccountClient interface {
	MakeMerchantCheck(ctx context.Context, in *MakeMerchantCheckRequest, opts ...grpc.CallOption) (*MakeMerchantCheckResponse, error)
	MakeTransfer(ctx context.Context, in *MakeTransferRequest, opts ...grpc.CallOption) (*MakeTransferResponse, error)
	AcceptTransfer(ctx context.Context, in *AcceptTransferRequest, opts ...grpc.CallOption) (*AcceptTransferResponse, error)
	DeclineTransfer(ctx context.Context, in *DeclineTransferRequest, opts ...grpc.CallOption) (*DeclineTransferResponse, error)
	RedoTransfer(ctx context.Context, in *RedoTransferRequest, opts ...grpc.CallOption) (*RedoTransferResponse, error)
	GetTransferDetails(ctx context.Context, in *GetTransferDetailsRequest, opts ...grpc.CallOption) (*GetTransferDetailsResponse, error)
	GetTransfersList(ctx context.Context, in *GetTransfersListRequest, opts ...grpc.CallOption) (*GetTransfersListResponse, error)
	GetAccountBalance(ctx context.Context, in *GetAccountBalanceRequest, opts ...grpc.CallOption) (*GetAccountBalanceResponse, error)
	GetAccountStatement(ctx context.Context, in *GetAccountStatementRequest, opts ...grpc.CallOption) (*GetAccountStatementResponse, error)
	GetAccountIdentifier(ctx context.Context, in *emptypb.Empty, opts ...grpc.CallOption) (*GetAccountIdentifierResponse, error)
}

type bCCAccountClient struct {
	cc grpc.ClientConnInterface
}

func NewBCCAccountClient(cc grpc.ClientConnInterface) BCCAccountClient {
	return &bCCAccountClient{cc}
}

func (c *bCCAccountClient) MakeMerchantCheck(ctx context.Context, in *MakeMerchantCheckRequest, opts ...grpc.CallOption) (*MakeMerchantCheckResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(MakeMerchantCheckResponse)
	err := c.cc.Invoke(ctx, BCCAccount_MakeMerchantCheck_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *bCCAccountClient) MakeTransfer(ctx context.Context, in *MakeTransferRequest, opts ...grpc.CallOption) (*MakeTransferResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(MakeTransferResponse)
	err := c.cc.Invoke(ctx, BCCAccount_MakeTransfer_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *bCCAccountClient) AcceptTransfer(ctx context.Context, in *AcceptTransferRequest, opts ...grpc.CallOption) (*AcceptTransferResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(AcceptTransferResponse)
	err := c.cc.Invoke(ctx, BCCAccount_AcceptTransfer_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *bCCAccountClient) DeclineTransfer(ctx context.Context, in *DeclineTransferRequest, opts ...grpc.CallOption) (*DeclineTransferResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(DeclineTransferResponse)
	err := c.cc.Invoke(ctx, BCCAccount_DeclineTransfer_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *bCCAccountClient) RedoTransfer(ctx context.Context, in *RedoTransferRequest, opts ...grpc.CallOption) (*RedoTransferResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(RedoTransferResponse)
	err := c.cc.Invoke(ctx, BCCAccount_RedoTransfer_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *bCCAccountClient) GetTransferDetails(ctx context.Context, in *GetTransferDetailsRequest, opts ...grpc.CallOption) (*GetTransferDetailsResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetTransferDetailsResponse)
	err := c.cc.Invoke(ctx, BCCAccount_GetTransferDetails_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *bCCAccountClient) GetTransfersList(ctx context.Context, in *GetTransfersListRequest, opts ...grpc.CallOption) (*GetTransfersListResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetTransfersListResponse)
	err := c.cc.Invoke(ctx, BCCAccount_GetTransfersList_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *bCCAccountClient) GetAccountBalance(ctx context.Context, in *GetAccountBalanceRequest, opts ...grpc.CallOption) (*GetAccountBalanceResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetAccountBalanceResponse)
	err := c.cc.Invoke(ctx, BCCAccount_GetAccountBalance_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *bCCAccountClient) GetAccountStatement(ctx context.Context, in *GetAccountStatementRequest, opts ...grpc.CallOption) (*GetAccountStatementResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetAccountStatementResponse)
	err := c.cc.Invoke(ctx, BCCAccount_GetAccountStatement_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *bCCAccountClient) GetAccountIdentifier(ctx context.Context, in *emptypb.Empty, opts ...grpc.CallOption) (*GetAccountIdentifierResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetAccountIdentifierResponse)
	err := c.cc.Invoke(ctx, BCCAccount_GetAccountIdentifier_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// BCCAccountServer is the server API for BCCAccount service.
// All implementations must embed UnimplementedBCCAccountServer
// for forward compatibility.
type BCCAccountServer interface {
	MakeMerchantCheck(context.Context, *MakeMerchantCheckRequest) (*MakeMerchantCheckResponse, error)
	MakeTransfer(context.Context, *MakeTransferRequest) (*MakeTransferResponse, error)
	AcceptTransfer(context.Context, *AcceptTransferRequest) (*AcceptTransferResponse, error)
	DeclineTransfer(context.Context, *DeclineTransferRequest) (*DeclineTransferResponse, error)
	RedoTransfer(context.Context, *RedoTransferRequest) (*RedoTransferResponse, error)
	GetTransferDetails(context.Context, *GetTransferDetailsRequest) (*GetTransferDetailsResponse, error)
	GetTransfersList(context.Context, *GetTransfersListRequest) (*GetTransfersListResponse, error)
	GetAccountBalance(context.Context, *GetAccountBalanceRequest) (*GetAccountBalanceResponse, error)
	GetAccountStatement(context.Context, *GetAccountStatementRequest) (*GetAccountStatementResponse, error)
	GetAccountIdentifier(context.Context, *emptypb.Empty) (*GetAccountIdentifierResponse, error)
	mustEmbedUnimplementedBCCAccountServer()
}

// UnimplementedBCCAccountServer must be embedded to have
// forward compatible implementations.
//
// NOTE: this should be embedded by value instead of pointer to avoid a nil
// pointer dereference when methods are called.
type UnimplementedBCCAccountServer struct{}

func (UnimplementedBCCAccountServer) MakeMerchantCheck(context.Context, *MakeMerchantCheckRequest) (*MakeMerchantCheckResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method MakeMerchantCheck not implemented")
}
func (UnimplementedBCCAccountServer) MakeTransfer(context.Context, *MakeTransferRequest) (*MakeTransferResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method MakeTransfer not implemented")
}
func (UnimplementedBCCAccountServer) AcceptTransfer(context.Context, *AcceptTransferRequest) (*AcceptTransferResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method AcceptTransfer not implemented")
}
func (UnimplementedBCCAccountServer) DeclineTransfer(context.Context, *DeclineTransferRequest) (*DeclineTransferResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeclineTransfer not implemented")
}
func (UnimplementedBCCAccountServer) RedoTransfer(context.Context, *RedoTransferRequest) (*RedoTransferResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method RedoTransfer not implemented")
}
func (UnimplementedBCCAccountServer) GetTransferDetails(context.Context, *GetTransferDetailsRequest) (*GetTransferDetailsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetTransferDetails not implemented")
}
func (UnimplementedBCCAccountServer) GetTransfersList(context.Context, *GetTransfersListRequest) (*GetTransfersListResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetTransfersList not implemented")
}
func (UnimplementedBCCAccountServer) GetAccountBalance(context.Context, *GetAccountBalanceRequest) (*GetAccountBalanceResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetAccountBalance not implemented")
}
func (UnimplementedBCCAccountServer) GetAccountStatement(context.Context, *GetAccountStatementRequest) (*GetAccountStatementResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetAccountStatement not implemented")
}
func (UnimplementedBCCAccountServer) GetAccountIdentifier(context.Context, *emptypb.Empty) (*GetAccountIdentifierResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetAccountIdentifier not implemented")
}
func (UnimplementedBCCAccountServer) mustEmbedUnimplementedBCCAccountServer() {}
func (UnimplementedBCCAccountServer) testEmbeddedByValue()                    {}

// UnsafeBCCAccountServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to BCCAccountServer will
// result in compilation errors.
type UnsafeBCCAccountServer interface {
	mustEmbedUnimplementedBCCAccountServer()
}

func RegisterBCCAccountServer(s grpc.ServiceRegistrar, srv BCCAccountServer) {
	// If the following call pancis, it indicates UnimplementedBCCAccountServer was
	// embedded by pointer and is nil.  This will cause panics if an
	// unimplemented method is ever invoked, so we test this at initialization
	// time to prevent it from happening at runtime later due to I/O.
	if t, ok := srv.(interface{ testEmbeddedByValue() }); ok {
		t.testEmbeddedByValue()
	}
	s.RegisterService(&BCCAccount_ServiceDesc, srv)
}

func _BCCAccount_MakeMerchantCheck_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(MakeMerchantCheckRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BCCAccountServer).MakeMerchantCheck(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: BCCAccount_MakeMerchantCheck_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BCCAccountServer).MakeMerchantCheck(ctx, req.(*MakeMerchantCheckRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _BCCAccount_MakeTransfer_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(MakeTransferRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BCCAccountServer).MakeTransfer(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: BCCAccount_MakeTransfer_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BCCAccountServer).MakeTransfer(ctx, req.(*MakeTransferRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _BCCAccount_AcceptTransfer_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AcceptTransferRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BCCAccountServer).AcceptTransfer(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: BCCAccount_AcceptTransfer_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BCCAccountServer).AcceptTransfer(ctx, req.(*AcceptTransferRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _BCCAccount_DeclineTransfer_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeclineTransferRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BCCAccountServer).DeclineTransfer(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: BCCAccount_DeclineTransfer_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BCCAccountServer).DeclineTransfer(ctx, req.(*DeclineTransferRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _BCCAccount_RedoTransfer_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RedoTransferRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BCCAccountServer).RedoTransfer(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: BCCAccount_RedoTransfer_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BCCAccountServer).RedoTransfer(ctx, req.(*RedoTransferRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _BCCAccount_GetTransferDetails_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetTransferDetailsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BCCAccountServer).GetTransferDetails(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: BCCAccount_GetTransferDetails_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BCCAccountServer).GetTransferDetails(ctx, req.(*GetTransferDetailsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _BCCAccount_GetTransfersList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetTransfersListRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BCCAccountServer).GetTransfersList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: BCCAccount_GetTransfersList_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BCCAccountServer).GetTransfersList(ctx, req.(*GetTransfersListRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _BCCAccount_GetAccountBalance_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetAccountBalanceRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BCCAccountServer).GetAccountBalance(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: BCCAccount_GetAccountBalance_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BCCAccountServer).GetAccountBalance(ctx, req.(*GetAccountBalanceRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _BCCAccount_GetAccountStatement_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetAccountStatementRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BCCAccountServer).GetAccountStatement(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: BCCAccount_GetAccountStatement_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BCCAccountServer).GetAccountStatement(ctx, req.(*GetAccountStatementRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _BCCAccount_GetAccountIdentifier_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(emptypb.Empty)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BCCAccountServer).GetAccountIdentifier(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: BCCAccount_GetAccountIdentifier_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BCCAccountServer).GetAccountIdentifier(ctx, req.(*emptypb.Empty))
	}
	return interceptor(ctx, in, info, handler)
}

// BCCAccount_ServiceDesc is the grpc.ServiceDesc for BCCAccount service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var BCCAccount_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "processing.bcc_account.bcc_account.BCCAccount",
	HandlerType: (*BCCAccountServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "MakeMerchantCheck",
			Handler:    _BCCAccount_MakeMerchantCheck_Handler,
		},
		{
			MethodName: "MakeTransfer",
			Handler:    _BCCAccount_MakeTransfer_Handler,
		},
		{
			MethodName: "AcceptTransfer",
			Handler:    _BCCAccount_AcceptTransfer_Handler,
		},
		{
			MethodName: "DeclineTransfer",
			Handler:    _BCCAccount_DeclineTransfer_Handler,
		},
		{
			MethodName: "RedoTransfer",
			Handler:    _BCCAccount_RedoTransfer_Handler,
		},
		{
			MethodName: "GetTransferDetails",
			Handler:    _BCCAccount_GetTransferDetails_Handler,
		},
		{
			MethodName: "GetTransfersList",
			Handler:    _BCCAccount_GetTransfersList_Handler,
		},
		{
			MethodName: "GetAccountBalance",
			Handler:    _BCCAccount_GetAccountBalance_Handler,
		},
		{
			MethodName: "GetAccountStatement",
			Handler:    _BCCAccount_GetAccountStatement_Handler,
		},
		{
			MethodName: "GetAccountIdentifier",
			Handler:    _BCCAccount_GetAccountIdentifier_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "inner/processing/grpc/bcc_account.proto",
}
