// Code generated by MockGen. DO NOT EDIT.
// Source: commission_grpc.pb.go

// Package grpcmock is a generated GoMock package.
package grpcmock

import (
	context "context"
	reflect "reflect"

	grpc "git.local/sensitive/innerpb/processing/grpc"
	gomock "github.com/golang/mock/gomock"
	grpc0 "google.golang.org/grpc"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
)

// MockCommissionClient is a mock of CommissionClient interface.
type MockCommissionClient struct {
	ctrl     *gomock.Controller
	recorder *MockCommissionClientMockRecorder
}

// MockCommissionClientMockRecorder is the mock recorder for MockCommissionClient.
type MockCommissionClientMockRecorder struct {
	mock *MockCommissionClient
}

// NewMockCommissionClient creates a new mock instance.
func NewMockCommissionClient(ctrl *gomock.Controller) *MockCommissionClient {
	mock := &MockCommissionClient{ctrl: ctrl}
	mock.recorder = &MockCommissionClientMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockCommissionClient) EXPECT() *MockCommissionClientMockRecorder {
	return m.recorder
}

// CalculateAndSaveUpperCommission mocks base method.
func (m *MockCommissionClient) CalculateAndSaveUpperCommission(ctx context.Context, in *grpc.CalculateAndSaveUpperCommissionRequestV1, opts ...grpc0.CallOption) (*grpc.CalculateAndSaveUpperCommissionResponseV1, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "CalculateAndSaveUpperCommission", varargs...)
	ret0, _ := ret[0].(*grpc.CalculateAndSaveUpperCommissionResponseV1)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CalculateAndSaveUpperCommission indicates an expected call of CalculateAndSaveUpperCommission.
func (mr *MockCommissionClientMockRecorder) CalculateAndSaveUpperCommission(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CalculateAndSaveUpperCommission", reflect.TypeOf((*MockCommissionClient)(nil).CalculateAndSaveUpperCommission), varargs...)
}

// CalculatePayInPrimalAmount mocks base method.
func (m *MockCommissionClient) CalculatePayInPrimalAmount(ctx context.Context, in *grpc.CalculatePrimalAmountRequestV1, opts ...grpc0.CallOption) (*grpc.CalculatePrimalAmountResponseV1, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "CalculatePayInPrimalAmount", varargs...)
	ret0, _ := ret[0].(*grpc.CalculatePrimalAmountResponseV1)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CalculatePayInPrimalAmount indicates an expected call of CalculatePayInPrimalAmount.
func (mr *MockCommissionClientMockRecorder) CalculatePayInPrimalAmount(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CalculatePayInPrimalAmount", reflect.TypeOf((*MockCommissionClient)(nil).CalculatePayInPrimalAmount), varargs...)
}

// CalculatePayOutPrimalAmount mocks base method.
func (m *MockCommissionClient) CalculatePayOutPrimalAmount(ctx context.Context, in *grpc.CalculatePrimalAmountRequestV1, opts ...grpc0.CallOption) (*grpc.CalculatePrimalAmountResponseV1, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "CalculatePayOutPrimalAmount", varargs...)
	ret0, _ := ret[0].(*grpc.CalculatePrimalAmountResponseV1)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CalculatePayOutPrimalAmount indicates an expected call of CalculatePayOutPrimalAmount.
func (mr *MockCommissionClientMockRecorder) CalculatePayOutPrimalAmount(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CalculatePayOutPrimalAmount", reflect.TypeOf((*MockCommissionClient)(nil).CalculatePayOutPrimalAmount), varargs...)
}

// FiscalizeUpperCommission mocks base method.
func (m *MockCommissionClient) FiscalizeUpperCommission(ctx context.Context, in *grpc.FiscalizeUpperCommissionRequestV1, opts ...grpc0.CallOption) (*emptypb.Empty, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "FiscalizeUpperCommission", varargs...)
	ret0, _ := ret[0].(*emptypb.Empty)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// FiscalizeUpperCommission indicates an expected call of FiscalizeUpperCommission.
func (mr *MockCommissionClientMockRecorder) FiscalizeUpperCommission(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "FiscalizeUpperCommission", reflect.TypeOf((*MockCommissionClient)(nil).FiscalizeUpperCommission), varargs...)
}

// GetCommissionByTransactionID mocks base method.
func (m *MockCommissionClient) GetCommissionByTransactionID(ctx context.Context, in *grpc.GetCommissionByTransactionIDRequestV1, opts ...grpc0.CallOption) (*grpc.GetCommissionByTransactionIDResponseV1, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetCommissionByTransactionID", varargs...)
	ret0, _ := ret[0].(*grpc.GetCommissionByTransactionIDResponseV1)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetCommissionByTransactionID indicates an expected call of GetCommissionByTransactionID.
func (mr *MockCommissionClientMockRecorder) GetCommissionByTransactionID(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetCommissionByTransactionID", reflect.TypeOf((*MockCommissionClient)(nil).GetCommissionByTransactionID), varargs...)
}

// GetCommissionForMainBalance mocks base method.
func (m *MockCommissionClient) GetCommissionForMainBalance(ctx context.Context, in *grpc.GetCommissionForMainBalanceRequestV1, opts ...grpc0.CallOption) (*grpc.GetCommissionForMainBalanceResponseV1, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetCommissionForMainBalance", varargs...)
	ret0, _ := ret[0].(*grpc.GetCommissionForMainBalanceResponseV1)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetCommissionForMainBalance indicates an expected call of GetCommissionForMainBalance.
func (mr *MockCommissionClientMockRecorder) GetCommissionForMainBalance(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetCommissionForMainBalance", reflect.TypeOf((*MockCommissionClient)(nil).GetCommissionForMainBalance), varargs...)
}

// UpdateCommissionForCreditBalance mocks base method.
func (m *MockCommissionClient) UpdateCommissionForCreditBalance(ctx context.Context, in *grpc.UpdateCommissionForCreditBalanceRequestV1, opts ...grpc0.CallOption) (*grpc.UpdateCommissionForCreditBalanceResponseV1, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "UpdateCommissionForCreditBalance", varargs...)
	ret0, _ := ret[0].(*grpc.UpdateCommissionForCreditBalanceResponseV1)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpdateCommissionForCreditBalance indicates an expected call of UpdateCommissionForCreditBalance.
func (mr *MockCommissionClientMockRecorder) UpdateCommissionForCreditBalance(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateCommissionForCreditBalance", reflect.TypeOf((*MockCommissionClient)(nil).UpdateCommissionForCreditBalance), varargs...)
}

// MockCommissionServer is a mock of CommissionServer interface.
type MockCommissionServer struct {
	ctrl     *gomock.Controller
	recorder *MockCommissionServerMockRecorder
}

// MockCommissionServerMockRecorder is the mock recorder for MockCommissionServer.
type MockCommissionServerMockRecorder struct {
	mock *MockCommissionServer
}

// NewMockCommissionServer creates a new mock instance.
func NewMockCommissionServer(ctrl *gomock.Controller) *MockCommissionServer {
	mock := &MockCommissionServer{ctrl: ctrl}
	mock.recorder = &MockCommissionServerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockCommissionServer) EXPECT() *MockCommissionServerMockRecorder {
	return m.recorder
}

// CalculateAndSaveUpperCommission mocks base method.
func (m *MockCommissionServer) CalculateAndSaveUpperCommission(arg0 context.Context, arg1 *grpc.CalculateAndSaveUpperCommissionRequestV1) (*grpc.CalculateAndSaveUpperCommissionResponseV1, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CalculateAndSaveUpperCommission", arg0, arg1)
	ret0, _ := ret[0].(*grpc.CalculateAndSaveUpperCommissionResponseV1)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CalculateAndSaveUpperCommission indicates an expected call of CalculateAndSaveUpperCommission.
func (mr *MockCommissionServerMockRecorder) CalculateAndSaveUpperCommission(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CalculateAndSaveUpperCommission", reflect.TypeOf((*MockCommissionServer)(nil).CalculateAndSaveUpperCommission), arg0, arg1)
}

// CalculatePayInPrimalAmount mocks base method.
func (m *MockCommissionServer) CalculatePayInPrimalAmount(arg0 context.Context, arg1 *grpc.CalculatePrimalAmountRequestV1) (*grpc.CalculatePrimalAmountResponseV1, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CalculatePayInPrimalAmount", arg0, arg1)
	ret0, _ := ret[0].(*grpc.CalculatePrimalAmountResponseV1)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CalculatePayInPrimalAmount indicates an expected call of CalculatePayInPrimalAmount.
func (mr *MockCommissionServerMockRecorder) CalculatePayInPrimalAmount(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CalculatePayInPrimalAmount", reflect.TypeOf((*MockCommissionServer)(nil).CalculatePayInPrimalAmount), arg0, arg1)
}

// CalculatePayOutPrimalAmount mocks base method.
func (m *MockCommissionServer) CalculatePayOutPrimalAmount(arg0 context.Context, arg1 *grpc.CalculatePrimalAmountRequestV1) (*grpc.CalculatePrimalAmountResponseV1, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CalculatePayOutPrimalAmount", arg0, arg1)
	ret0, _ := ret[0].(*grpc.CalculatePrimalAmountResponseV1)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CalculatePayOutPrimalAmount indicates an expected call of CalculatePayOutPrimalAmount.
func (mr *MockCommissionServerMockRecorder) CalculatePayOutPrimalAmount(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CalculatePayOutPrimalAmount", reflect.TypeOf((*MockCommissionServer)(nil).CalculatePayOutPrimalAmount), arg0, arg1)
}

// FiscalizeUpperCommission mocks base method.
func (m *MockCommissionServer) FiscalizeUpperCommission(arg0 context.Context, arg1 *grpc.FiscalizeUpperCommissionRequestV1) (*emptypb.Empty, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "FiscalizeUpperCommission", arg0, arg1)
	ret0, _ := ret[0].(*emptypb.Empty)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// FiscalizeUpperCommission indicates an expected call of FiscalizeUpperCommission.
func (mr *MockCommissionServerMockRecorder) FiscalizeUpperCommission(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "FiscalizeUpperCommission", reflect.TypeOf((*MockCommissionServer)(nil).FiscalizeUpperCommission), arg0, arg1)
}

// GetCommissionByTransactionID mocks base method.
func (m *MockCommissionServer) GetCommissionByTransactionID(arg0 context.Context, arg1 *grpc.GetCommissionByTransactionIDRequestV1) (*grpc.GetCommissionByTransactionIDResponseV1, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetCommissionByTransactionID", arg0, arg1)
	ret0, _ := ret[0].(*grpc.GetCommissionByTransactionIDResponseV1)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetCommissionByTransactionID indicates an expected call of GetCommissionByTransactionID.
func (mr *MockCommissionServerMockRecorder) GetCommissionByTransactionID(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetCommissionByTransactionID", reflect.TypeOf((*MockCommissionServer)(nil).GetCommissionByTransactionID), arg0, arg1)
}

// GetCommissionForMainBalance mocks base method.
func (m *MockCommissionServer) GetCommissionForMainBalance(arg0 context.Context, arg1 *grpc.GetCommissionForMainBalanceRequestV1) (*grpc.GetCommissionForMainBalanceResponseV1, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetCommissionForMainBalance", arg0, arg1)
	ret0, _ := ret[0].(*grpc.GetCommissionForMainBalanceResponseV1)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetCommissionForMainBalance indicates an expected call of GetCommissionForMainBalance.
func (mr *MockCommissionServerMockRecorder) GetCommissionForMainBalance(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetCommissionForMainBalance", reflect.TypeOf((*MockCommissionServer)(nil).GetCommissionForMainBalance), arg0, arg1)
}

// UpdateCommissionForCreditBalance mocks base method.
func (m *MockCommissionServer) UpdateCommissionForCreditBalance(arg0 context.Context, arg1 *grpc.UpdateCommissionForCreditBalanceRequestV1) (*grpc.UpdateCommissionForCreditBalanceResponseV1, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateCommissionForCreditBalance", arg0, arg1)
	ret0, _ := ret[0].(*grpc.UpdateCommissionForCreditBalanceResponseV1)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpdateCommissionForCreditBalance indicates an expected call of UpdateCommissionForCreditBalance.
func (mr *MockCommissionServerMockRecorder) UpdateCommissionForCreditBalance(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateCommissionForCreditBalance", reflect.TypeOf((*MockCommissionServer)(nil).UpdateCommissionForCreditBalance), arg0, arg1)
}

// mustEmbedUnimplementedCommissionServer mocks base method.
func (m *MockCommissionServer) mustEmbedUnimplementedCommissionServer() {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "mustEmbedUnimplementedCommissionServer")
}

// mustEmbedUnimplementedCommissionServer indicates an expected call of mustEmbedUnimplementedCommissionServer.
func (mr *MockCommissionServerMockRecorder) mustEmbedUnimplementedCommissionServer() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "mustEmbedUnimplementedCommissionServer", reflect.TypeOf((*MockCommissionServer)(nil).mustEmbedUnimplementedCommissionServer))
}

// MockUnsafeCommissionServer is a mock of UnsafeCommissionServer interface.
type MockUnsafeCommissionServer struct {
	ctrl     *gomock.Controller
	recorder *MockUnsafeCommissionServerMockRecorder
}

// MockUnsafeCommissionServerMockRecorder is the mock recorder for MockUnsafeCommissionServer.
type MockUnsafeCommissionServerMockRecorder struct {
	mock *MockUnsafeCommissionServer
}

// NewMockUnsafeCommissionServer creates a new mock instance.
func NewMockUnsafeCommissionServer(ctrl *gomock.Controller) *MockUnsafeCommissionServer {
	mock := &MockUnsafeCommissionServer{ctrl: ctrl}
	mock.recorder = &MockUnsafeCommissionServerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockUnsafeCommissionServer) EXPECT() *MockUnsafeCommissionServerMockRecorder {
	return m.recorder
}

// mustEmbedUnimplementedCommissionServer mocks base method.
func (m *MockUnsafeCommissionServer) mustEmbedUnimplementedCommissionServer() {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "mustEmbedUnimplementedCommissionServer")
}

// mustEmbedUnimplementedCommissionServer indicates an expected call of mustEmbedUnimplementedCommissionServer.
func (mr *MockUnsafeCommissionServerMockRecorder) mustEmbedUnimplementedCommissionServer() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "mustEmbedUnimplementedCommissionServer", reflect.TypeOf((*MockUnsafeCommissionServer)(nil).mustEmbedUnimplementedCommissionServer))
}
