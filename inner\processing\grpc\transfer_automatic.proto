edition = "2023";

package processing.transfer_automatic.transfer_automatic;
option go_package = "git.local/sensitive/innerpb/processing/grpc";

import "google/protobuf/empty.proto";
import "google/protobuf/descriptor.proto";
import "mvp/proto/refs.proto";
import "mvp/proto/enum.proto";

message FrequencyRef {
  string code = 1;
  string name = 2;
}

extend google.protobuf.EnumValueOptions {
  FrequencyRef frequency_value = 50001;
}
extend google.protobuf.EnumOptions {
  FrequencyRef default_frequency_value = 50002;
}

enum EnumFrequencyType {
  option (mvp.default_ref) = "default_frequency_value";
  option (mvp.ref)         = "frequency_value";

  option (default_frequency_value) = { code: "" };

  PerDay   = 0 [(frequency_value) = {
    code: "day",
    name: "День",
  }, (mvp.from_string) = "day"];
  PerWeek  = 1 [(frequency_value) = {
    code: "week",
    name: "Неделя"
  }, (mvp.from_string) = "week"];
  PerMonth = 2 [(frequency_value) = {
    code: "month",
    name: "Месяц",
  }, (mvp.from_string) = "month"];
}

service TransferAutomatic {
  rpc StartCreateTransferByRulesWorker(CreateTransferByRulesRequest)
      returns (google.protobuf.Empty);
}

message CreateTransferByRulesRequest {
  EnumFrequencyType frequency = 1;
}
