// Code generated by protoc-gen-go-mvp. DO NOT EDIT.
// versions:
// - protoc-gen-go-mvp v2.0.1
// created by mvp authors

package grpc

import (
	dog "git.local/sensitive/sdk/dog"
	gin "github.com/gin-gonic/gin"
	metadata "google.golang.org/grpc/metadata"
	http "net/http"
)

func RegisterGinMultiacquiringBalanceRouterGroup(
	in *gin.RouterGroup,
	handler func(func(c *gin.Context) error) gin.HandlerFunc,
) error {
	service, err := NewGinMultiacquiringBalanceService()
	if err != nil {
		return err
	}

	routerGroup := in.Group("processing.multiacquiring.multiacquiring_balance.MultiacquiringBalance")
	routerGroup.PUT("/CheckBalance", handler(service.CheckBalance))
	return nil
}

func NewGinMultiacquiringBalanceService() (GinMultiacquiringBalanceServer, error) {
	client, err := NewPreparedMultiacquiringBalanceClient()
	if err != nil {
		return nil, err
	}

	return &ginMultiacquiringBalanceServer{
		client: NewLoggedMultiacquiringBalanceClient(
			NewIamMultiacquiringBalanceClient(client),
		),
	}, nil
}

//go:generate mockgen -destination=./grpcmock/multiacquiring_balance.gin.pb.go -package=grpcmock -source=multiacquiring_balance.gin.pb.go GinMultiacquiringBalanceServer
type GinMultiacquiringBalanceServer interface {
	CheckBalance(c *gin.Context) error
}

var _ GinMultiacquiringBalanceServer = (*ginMultiacquiringBalanceServer)(nil)

type ginMultiacquiringBalanceServer struct {
	client MultiacquiringBalanceClient
}

type MultiacquiringBalance_CheckBalance_Success struct {
	// Status is the status of the response. Set true
	Status bool `json:"status"`
	// Message is a human-readable explanation of the status
	Message string `json:"message"`
	// StatusCode is the GoErr status. Set 0
	StatusCode int `json:"status_code"`
	// Result is the result of the request
	Result *CheckBalanceResponse `json:"result"`
}

type MultiacquiringBalance_CheckBalance_Failure struct {
	// Status is the status of the response. Set false
	Status bool `json:"status"`
	// Message is a human-readable explanation of the status
	Message string `json:"message"`
	// StatusCode is the GoErr status
	StatusCode int `json:"status_code"`
}

// CheckBalance
// @Summary CheckBalance
// @Security bearerAuth
// @ID MultiacquiringBalance_CheckBalance
// @Accept json
// @Param request body CheckBalanceRequest true "CheckBalanceRequest"
// @Success 200 {object} MultiacquiringBalance_CheckBalance_Success
// @Failure 401 {object} MultiacquiringBalance_CheckBalance_Failure "goerrpkg.ErrorTypeAuthorization"
// @Failure 404 {object} MultiacquiringBalance_CheckBalance_Failure "goerrpkg.ErrorTypeNotFound"
// @Failure 400 {object} MultiacquiringBalance_CheckBalance_Failure "goerrpkg.ErrorTypeBadRequest"
// @Failure 500 {object} MultiacquiringBalance_CheckBalance_Failure "goerrpkg.ErrorTypeUnexpectedError"
// @Failure 400 {object} MultiacquiringBalance_CheckBalance_Failure "gin.ErrorTypeBind"
// @Failure 500 {object} MultiacquiringBalance_CheckBalance_Failure "Undefined error"
// @Produce json
// @Router /processing.multiacquiring.multiacquiring_balance.MultiacquiringBalance/CheckBalance [put]
// @tags MultiacquiringBalance
func (s *ginMultiacquiringBalanceServer) CheckBalance(c *gin.Context) error {
	ctx, span := dog.CreateSpan(c.Request.Context(), "GinMultiacquiringBalanceServer_CheckBalance")
	defer span.End()

	var request CheckBalanceRequest
	if err := c.ShouldBindJSON(&request); err != nil {
		return err
	}

	ctx = metadata.NewOutgoingContext(ctx, metadata.MD(c.Request.Header))

	response, err := s.client.CheckBalance(ctx, &request)
	if err != nil {
		return err
	}

	c.JSON(http.StatusOK, &MultiacquiringBalance_CheckBalance_Success{
		Status:     true,
		Message:    "Success",
		StatusCode: 0,
		Result:     response,
	})
	return nil
}
