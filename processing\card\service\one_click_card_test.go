package service

import (
	"context"
	"crypto/aes"
	"errors"
	"git.local/sensitive/innerpb/processing/goerr"
	"git.local/sensitive/innerpb/processing/grpc/grpcmock"
	"git.local/sensitive/testsdk"
	"testing"

	"github.com/golang/mock/gomock"
	"github.com/stretchr/testify/require"

	"git.local/sensitive/innerpb/processing/grpc"
	"git.local/sensitive/processing/card/model"
	"git.local/sensitive/processing/card/repository/database/databasemocks"
	"git.local/sensitive/processing/card/schema"
	"git.local/sensitive/sdk/dog/e2e"
)

func TestGetOneClickPayInCards(t *testing.T) {
	var (
		ipsId     uint64 = 1
		countryId uint64 = 1
		issuerId  uint64 = 1
	)

	type getClientByProjectOp struct {
		inputProjectID       uint64
		inputProjectClientID string
		output               *model.Client
		outputErr            error
	}

	type getActiveCardsByClientOp struct {
		isCalled  bool
		input     uint64
		output    model.Cards
		outputErr error
	}

	type getRuleByActiveTerminalsOp struct {
		isCalled  bool
		input     *grpc.RuleByActiveTerminalsReqV1
		output    *grpc.RuleByActiveTerminalsResponseV1
		outputErr error
	}

	type getProjectMaskFormatByProjectIDOp struct {
		isCalled  bool
		input     uint64
		output    *model.ProjectMaskFormat
		outputErr error
	}

	type getKeyByIDOp struct {
		isCalled  bool
		input     uint64
		output    model.Key
		outputErr error
	}

	type getIpsByIDOp struct {
		isCalled  bool
		input     *grpc.GetIpsByIDRequestV1
		output    *grpc.GetIpsByIDResponseV1
		outputErr error
	}

	type getIssuerByIDOp struct {
		isCalled  bool
		input     *grpc.GetIssuerByIDRequestV1
		output    *grpc.GetIssuerByIDResponseV1
		outputErr error
	}

	defaultWithSep := true

	tests := []struct {
		name                          string
		req                           *grpc.GetOneClickPayInCardsRequestV1
		want                          []*schema.EncryptedCardToken
		wantErr                       error
		getClientByProject            getClientByProjectOp
		getActiveCardsByClient        getActiveCardsByClientOp
		getRuleByActiveTerminals      getRuleByActiveTerminalsOp
		getProjectMaskFormatByProject getProjectMaskFormatByProjectIDOp
		getKeyByID                    getKeyByIDOp
		getIpsByID                    getIpsByIDOp
		getIssuerByID                 getIssuerByIDOp
		appConfig                     map[string]any
	}{
		{
			name: "error_getting_client_by_project",
			req: &grpc.GetOneClickPayInCardsRequestV1{
				ProjectId:         testsdk.Ptr(uint64(1)),
				ProjectClientId:   testsdk.Ptr("asd"),
				TransactionTypeId: testsdk.Ptr(uint64(1)),
				Amount:            testsdk.Ptr(10.0),
				ActiveTerminals: []*grpc.TerminalProjectV1{
					{
						Id:         testsdk.Ptr(uint64(1)),
						TerminalId: testsdk.Ptr(uint64(1)),
					},
				},
			},
			want:    nil,
			wantErr: errors.New("some error"),
			getClientByProject: getClientByProjectOp{
				inputProjectID:       1,
				inputProjectClientID: "asd",
				output:               nil,
				outputErr:            errors.New("some error"),
			},
		},
		{
			name: "error_getting_active_cards_by_client",
			req: &grpc.GetOneClickPayInCardsRequestV1{
				ProjectId:         testsdk.Ptr(uint64(1)),
				ProjectClientId:   testsdk.Ptr("asd"),
				TransactionTypeId: testsdk.Ptr(uint64(1)),
				Amount:            testsdk.Ptr(10.0),
				ActiveTerminals: []*grpc.TerminalProjectV1{
					{
						Id:         testsdk.Ptr(uint64(1)),
						TerminalId: testsdk.Ptr(uint64(1)),
					},
				},
			},
			want:    nil,
			wantErr: errors.New("some error"),
			getClientByProject: getClientByProjectOp{
				inputProjectID:       1,
				inputProjectClientID: "asd",
				output: &model.Client{
					Id:              1,
					ProjectClientId: "asd",
					ProjectId:       1,
				},
				outputErr: nil,
			},
			getActiveCardsByClient: getActiveCardsByClientOp{
				isCalled:  true,
				input:     1,
				output:    nil,
				outputErr: errors.New("some error"),
			},
		},
		{
			name: "len_of_tokens_is_zero",
			req: &grpc.GetOneClickPayInCardsRequestV1{
				ProjectId:         testsdk.Ptr(uint64(1)),
				ProjectClientId:   testsdk.Ptr("asd"),
				TransactionTypeId: testsdk.Ptr(uint64(1)),
				Amount:            testsdk.Ptr(10.0),
				ActiveTerminals: []*grpc.TerminalProjectV1{
					{
						Id:         testsdk.Ptr(uint64(1)),
						TerminalId: testsdk.Ptr(uint64(1)),
					},
				},
			},
			want:    make([]*schema.EncryptedCardToken, 0),
			wantErr: nil,
			getClientByProject: getClientByProjectOp{
				inputProjectID:       1,
				inputProjectClientID: "asd",
				output: &model.Client{
					Id:              1,
					ProjectClientId: "asd",
					ProjectId:       1,
				},
				outputErr: nil,
			},
			getActiveCardsByClient: getActiveCardsByClientOp{
				isCalled: true,
				input:    1,
				output: model.Cards{
					{
						Id:            1,
						ClientId:      1,
						IssuerId:      1,
						IpsId:         1,
						CountryId:     1,
						SaveAccess:    true,
						Approved:      false,
						KeyId:         1,
						EncryptMonth:  "AAAAAAAAAAAAAAAAAAAAAJWXVsNZooT1Nj5qlh9r4ws=",
						EncryptYear:   "AAAAAAAAAAAAAAAAAAAAAJWXVsNZooT1Nj5qlh9r4ws=",
						EncryptName:   "AAAAAAAAAAAAAAAAAAAAAJWXVsNZooT1Nj5qlh9r4ws=",
						EncryptPan:    "AAAAAAAAAAAAAAAAAAAAACXJ7eT6CzNrPzsbyIRluEFgK65FdiS3n0szvcsdV2Tb",
						Pan:           "something",
						IsPanModified: true,
					},
				},
				outputErr: nil,
			},
		},
		{
			name: "terminal_doesnt_matches",
			req: &grpc.GetOneClickPayInCardsRequestV1{
				ProjectId:         testsdk.Ptr(uint64(1)),
				ProjectClientId:   testsdk.Ptr("asd"),
				TransactionTypeId: testsdk.Ptr(uint64(1)),
				Amount:            testsdk.Ptr(10.0),
				ActiveTerminals: []*grpc.TerminalProjectV1{
					{
						Id:         testsdk.Ptr(uint64(1)),
						TerminalId: testsdk.Ptr(uint64(1)),
					},
				},
			},
			want:    make([]*schema.EncryptedCardToken, 0),
			wantErr: nil,
			getClientByProject: getClientByProjectOp{
				inputProjectID:       1,
				inputProjectClientID: "asd",
				output: &model.Client{
					Id:              1,
					ProjectClientId: "asd",
					ProjectId:       1,
				},
				outputErr: nil,
			},
			getActiveCardsByClient: getActiveCardsByClientOp{
				isCalled: true,
				input:    1,
				output: model.Cards{
					{
						Id:            1,
						ClientId:      1,
						IssuerId:      1,
						IpsId:         1,
						CountryId:     1,
						SaveAccess:    true,
						Approved:      false,
						KeyId:         1,
						EncryptMonth:  "AAAAAAAAAAAAAAAAAAAAAJWXVsNZooT1Nj5qlh9r4ws=",
						EncryptYear:   "AAAAAAAAAAAAAAAAAAAAAJWXVsNZooT1Nj5qlh9r4ws=",
						EncryptName:   "AAAAAAAAAAAAAAAAAAAAAJWXVsNZooT1Nj5qlh9r4ws=",
						EncryptPan:    "AAAAAAAAAAAAAAAAAAAAACXJ7eT6CzNrPzsbyIRluEFgK65FdiS3n0szvcsdV2Tb",
						Pan:           "something",
						IsPanModified: true,
						Tokens: []model.Token{
							{
								Id:         1,
								TerminalId: 2,
							},
						},
					},
				},
				outputErr: nil,
			},
		},
		{
			name: "get_rule_by_active_terminals_error",
			req: &grpc.GetOneClickPayInCardsRequestV1{
				ProjectId:         testsdk.Ptr(uint64(1)),
				ProjectClientId:   testsdk.Ptr("asd"),
				TransactionTypeId: testsdk.Ptr(uint64(1)),
				Amount:            testsdk.Ptr(10.0),
				ActiveTerminals: []*grpc.TerminalProjectV1{
					{
						Id:         testsdk.Ptr(uint64(1)),
						TerminalId: testsdk.Ptr(uint64(1)),
					},
				},
			},
			want:    make([]*schema.EncryptedCardToken, 0),
			wantErr: nil,
			getClientByProject: getClientByProjectOp{
				inputProjectID:       1,
				inputProjectClientID: "asd",
				output: &model.Client{
					Id:              1,
					ProjectClientId: "asd",
					ProjectId:       1,
				},
				outputErr: nil,
			},
			getActiveCardsByClient: getActiveCardsByClientOp{
				isCalled: true,
				input:    1,
				output: model.Cards{
					{
						Id:            1,
						ClientId:      1,
						IssuerId:      1,
						IpsId:         1,
						CountryId:     1,
						SaveAccess:    true,
						Approved:      false,
						KeyId:         1,
						EncryptMonth:  "AAAAAAAAAAAAAAAAAAAAAJWXVsNZooT1Nj5qlh9r4ws=",
						EncryptYear:   "AAAAAAAAAAAAAAAAAAAAAJWXVsNZooT1Nj5qlh9r4ws=",
						EncryptName:   "AAAAAAAAAAAAAAAAAAAAAJWXVsNZooT1Nj5qlh9r4ws=",
						EncryptPan:    "AAAAAAAAAAAAAAAAAAAAACXJ7eT6CzNrPzsbyIRluEFgK65FdiS3n0szvcsdV2Tb",
						Pan:           "something",
						IsPanModified: true,
						Tokens: []model.Token{
							{
								Id:         1,
								TerminalId: 1,
							},
						},
					},
				},
				outputErr: nil,
			},
			getRuleByActiveTerminals: getRuleByActiveTerminalsOp{
				isCalled: true,
				input: schema.NewRuleByActiveTerminalsReq(schema.RuleByActiveTerminalsReq{
					ProjectID:         1,
					TransactionTypeID: 1,
					Amount:            10,
					IpsID:             &ipsId,
					CountryID:         &countryId,
					IssuerID:          &issuerId,
					ActiveTerminalIDs: []uint64{1},
				}),
				output:    nil,
				outputErr: errors.New("some error"),
			},
		},
		{
			name: "get_project_mask_error",
			req: &grpc.GetOneClickPayInCardsRequestV1{
				ProjectId:         testsdk.Ptr(uint64(1)),
				ProjectClientId:   testsdk.Ptr("asd"),
				TransactionTypeId: testsdk.Ptr(uint64(1)),
				Amount:            testsdk.Ptr(10.0),
				ActiveTerminals: []*grpc.TerminalProjectV1{
					{
						Id:         testsdk.Ptr(uint64(1)),
						TerminalId: testsdk.Ptr(uint64(1)),
					},
				},
			},
			want:    nil,
			wantErr: errors.New("some error"),
			getClientByProject: getClientByProjectOp{
				inputProjectID:       1,
				inputProjectClientID: "asd",
				output: &model.Client{
					Id:              1,
					ProjectClientId: "asd",
					ProjectId:       1,
				},
				outputErr: nil,
			},
			getActiveCardsByClient: getActiveCardsByClientOp{
				isCalled: true,
				input:    1,
				output: model.Cards{
					{
						Id:            1,
						ClientId:      1,
						IssuerId:      1,
						IpsId:         1,
						CountryId:     1,
						SaveAccess:    true,
						Approved:      false,
						KeyId:         1,
						EncryptMonth:  "AAAAAAAAAAAAAAAAAAAAAJWXVsNZooT1Nj5qlh9r4ws=",
						EncryptYear:   "AAAAAAAAAAAAAAAAAAAAAJWXVsNZooT1Nj5qlh9r4ws=",
						EncryptName:   "AAAAAAAAAAAAAAAAAAAAAJWXVsNZooT1Nj5qlh9r4ws=",
						EncryptPan:    "AAAAAAAAAAAAAAAAAAAAACXJ7eT6CzNrPzsbyIRluEFgK65FdiS3n0szvcsdV2Tb",
						Pan:           "something",
						IsPanModified: true,
						Tokens: []model.Token{
							{
								Id:         1,
								TerminalId: 1,
							},
						},
					},
				},
				outputErr: nil,
			},
			getRuleByActiveTerminals: getRuleByActiveTerminalsOp{
				isCalled: true,
				input: schema.NewRuleByActiveTerminalsReq(schema.RuleByActiveTerminalsReq{
					ProjectID:         1,
					TransactionTypeID: 1,
					Amount:            10,
					IpsID:             &ipsId,
					CountryID:         &countryId,
					IssuerID:          &issuerId,
					ActiveTerminalIDs: []uint64{1},
				}),
				output: &grpc.RuleByActiveTerminalsResponseV1{
					IsRuleFound: testsdk.Ptr(true),
				},
				outputErr: nil,
			},
			getProjectMaskFormatByProject: getProjectMaskFormatByProjectIDOp{
				isCalled:  true,
				input:     1,
				output:    nil,
				outputErr: errors.New("some error"),
			},
		},
		{
			name: "get_key_by_id_error",
			req: &grpc.GetOneClickPayInCardsRequestV1{
				ProjectId:         testsdk.Ptr(uint64(1)),
				ProjectClientId:   testsdk.Ptr("asd"),
				TransactionTypeId: testsdk.Ptr(uint64(1)),
				Amount:            testsdk.Ptr(10.0),
				ActiveTerminals: []*grpc.TerminalProjectV1{
					{
						Id:         testsdk.Ptr(uint64(1)),
						TerminalId: testsdk.Ptr(uint64(1)),
					},
				},
			},
			want:    nil,
			wantErr: errors.New("some error"),
			getClientByProject: getClientByProjectOp{
				inputProjectID:       1,
				inputProjectClientID: "asd",
				output: &model.Client{
					Id:              1,
					ProjectClientId: "asd",
					ProjectId:       1,
				},
				outputErr: nil,
			},
			getActiveCardsByClient: getActiveCardsByClientOp{
				isCalled: true,
				input:    1,
				output: model.Cards{
					{
						Id:            1,
						ClientId:      1,
						IssuerId:      1,
						IpsId:         1,
						CountryId:     1,
						SaveAccess:    true,
						Approved:      false,
						KeyId:         1,
						EncryptMonth:  "AAAAAAAAAAAAAAAAAAAAAJWXVsNZooT1Nj5qlh9r4ws=",
						EncryptYear:   "AAAAAAAAAAAAAAAAAAAAAJWXVsNZooT1Nj5qlh9r4ws=",
						EncryptName:   "AAAAAAAAAAAAAAAAAAAAAJWXVsNZooT1Nj5qlh9r4ws=",
						EncryptPan:    "AAAAAAAAAAAAAAAAAAAAACXJ7eT6CzNrPzsbyIRluEFgK65FdiS3n0szvcsdV2Tb",
						Pan:           "something",
						IsPanModified: true,
						Tokens: []model.Token{
							{
								Id:         1,
								TerminalId: 1,
							},
						},
					},
				},
				outputErr: nil,
			},
			getRuleByActiveTerminals: getRuleByActiveTerminalsOp{
				isCalled: true,
				input: schema.NewRuleByActiveTerminalsReq(schema.RuleByActiveTerminalsReq{
					ProjectID:         1,
					TransactionTypeID: 1,
					Amount:            10,
					IpsID:             &ipsId,
					CountryID:         &countryId,
					IssuerID:          &issuerId,
					ActiveTerminalIDs: []uint64{1},
				}),
				output: &grpc.RuleByActiveTerminalsResponseV1{
					IsRuleFound: testsdk.Ptr(true),
				},
				outputErr: nil,
			},
			getProjectMaskFormatByProject: getProjectMaskFormatByProjectIDOp{
				isCalled: true,
				input:    1,
				output: &model.ProjectMaskFormat{
					Id:              1,
					ProjectId:       1,
					PlaceholderSign: "*",
					WithSeparator:   &defaultWithSep,
				},
				outputErr: nil,
			},
			getKeyByID: getKeyByIDOp{
				isCalled:  true,
				input:     1,
				output:    model.Key{},
				outputErr: errors.New("some error"),
			},
		},
		{
			name: "get_ips_by_id_error",
			req: &grpc.GetOneClickPayInCardsRequestV1{
				ProjectId:         testsdk.Ptr(uint64(1)),
				ProjectClientId:   testsdk.Ptr("asd"),
				TransactionTypeId: testsdk.Ptr(uint64(1)),
				Amount:            testsdk.Ptr(10.0),
				ActiveTerminals: []*grpc.TerminalProjectV1{
					{
						Id:         testsdk.Ptr(uint64(1)),
						TerminalId: testsdk.Ptr(uint64(1)),
					},
				},
			},
			want:    make([]*schema.EncryptedCardToken, 0),
			wantErr: errors.New("some error"),
			getClientByProject: getClientByProjectOp{
				inputProjectID:       1,
				inputProjectClientID: "asd",
				output: &model.Client{
					Id:              1,
					ProjectClientId: "asd",
					ProjectId:       1,
				},
				outputErr: nil,
			},
			getActiveCardsByClient: getActiveCardsByClientOp{
				isCalled: true,
				input:    1,
				output: model.Cards{
					{
						Id:            1,
						ClientId:      1,
						IssuerId:      1,
						IpsId:         1,
						CountryId:     1,
						SaveAccess:    true,
						Approved:      false,
						KeyId:         1,
						EncryptMonth:  "AAAAAAAAAAAAAAAAAAAAAJWXVsNZooT1Nj5qlh9r4ws=",
						EncryptYear:   "AAAAAAAAAAAAAAAAAAAAAJWXVsNZooT1Nj5qlh9r4ws=",
						EncryptName:   "AAAAAAAAAAAAAAAAAAAAAJWXVsNZooT1Nj5qlh9r4ws=",
						EncryptPan:    "AAAAAAAAAAAAAAAAAAAAACXJ7eT6CzNrPzsbyIRluEFgK65FdiS3n0szvcsdV2Tb",
						Pan:           "something",
						IsPanModified: true,
						Tokens: []model.Token{
							{
								Id:         1,
								TerminalId: 1,
							},
						},
					},
				},
				outputErr: nil,
			},
			getRuleByActiveTerminals: getRuleByActiveTerminalsOp{
				isCalled: true,
				input: schema.NewRuleByActiveTerminalsReq(schema.RuleByActiveTerminalsReq{
					ProjectID:         1,
					TransactionTypeID: 1,
					Amount:            10,
					IpsID:             &ipsId,
					CountryID:         &countryId,
					IssuerID:          &issuerId,
					ActiveTerminalIDs: []uint64{1},
				}),
				output: &grpc.RuleByActiveTerminalsResponseV1{
					IsRuleFound: testsdk.Ptr(true),
				},
				outputErr: nil,
			},
			getProjectMaskFormatByProject: getProjectMaskFormatByProjectIDOp{
				isCalled: true,
				input:    1,
				output: &model.ProjectMaskFormat{
					Id:              1,
					ProjectId:       1,
					PlaceholderSign: "*",
					WithSeparator:   &defaultWithSep,
				},
				outputErr: nil,
			},
			getKeyByID: getKeyByIDOp{
				isCalled: true,
				input:    1,
				output: model.Key{
					Id:  1,
					Key: "AAAAAAAAAAAAAAAAAAAAAD4r1NYivkHsOHdiq9dCiJiz0HJtt7wrVFA8fx/DenGL",
				},
				outputErr: nil,
			},
			getIpsByID: getIpsByIDOp{
				isCalled: true,
				input: &grpc.GetIpsByIDRequestV1{
					IpsId: testsdk.Ptr(uint64(1)),
				},
				output:    nil,
				outputErr: errors.New("some error"),
			},
		},
		{
			name: "get_issuer_by_id_error",
			req: &grpc.GetOneClickPayInCardsRequestV1{
				ProjectId:         testsdk.Ptr(uint64(1)),
				ProjectClientId:   testsdk.Ptr("asd"),
				TransactionTypeId: testsdk.Ptr(uint64(1)),
				Amount:            testsdk.Ptr(10.0),
				ActiveTerminals: []*grpc.TerminalProjectV1{
					{
						Id:         testsdk.Ptr(uint64(1)),
						TerminalId: testsdk.Ptr(uint64(1)),
					},
				},
			},
			want:    make([]*schema.EncryptedCardToken, 0),
			wantErr: errors.New("some error"),
			getClientByProject: getClientByProjectOp{
				inputProjectID:       1,
				inputProjectClientID: "asd",
				output: &model.Client{
					Id:              1,
					ProjectClientId: "asd",
					ProjectId:       1,
				},
				outputErr: nil,
			},
			getActiveCardsByClient: getActiveCardsByClientOp{
				isCalled: true,
				input:    1,
				output: model.Cards{
					{
						Id:            1,
						ClientId:      1,
						IssuerId:      1,
						IpsId:         1,
						CountryId:     1,
						SaveAccess:    true,
						Approved:      false,
						KeyId:         1,
						EncryptMonth:  "AAAAAAAAAAAAAAAAAAAAAJWXVsNZooT1Nj5qlh9r4ws=",
						EncryptYear:   "AAAAAAAAAAAAAAAAAAAAAJWXVsNZooT1Nj5qlh9r4ws=",
						EncryptName:   "AAAAAAAAAAAAAAAAAAAAAJWXVsNZooT1Nj5qlh9r4ws=",
						EncryptPan:    "AAAAAAAAAAAAAAAAAAAAACXJ7eT6CzNrPzsbyIRluEFgK65FdiS3n0szvcsdV2Tb",
						Pan:           "something",
						IsPanModified: true,
						Tokens: []model.Token{
							{
								Id:         1,
								TerminalId: 1,
							},
						},
					},
				},
				outputErr: nil,
			},
			getRuleByActiveTerminals: getRuleByActiveTerminalsOp{
				isCalled: true,
				input: schema.NewRuleByActiveTerminalsReq(schema.RuleByActiveTerminalsReq{
					ProjectID:         1,
					TransactionTypeID: 1,
					Amount:            10,
					IpsID:             &ipsId,
					CountryID:         &countryId,
					IssuerID:          &issuerId,
					ActiveTerminalIDs: []uint64{1},
				}),
				output: &grpc.RuleByActiveTerminalsResponseV1{
					IsRuleFound: testsdk.Ptr(true),
				},
				outputErr: nil,
			},
			getProjectMaskFormatByProject: getProjectMaskFormatByProjectIDOp{
				isCalled: true,
				input:    1,
				output: &model.ProjectMaskFormat{
					Id:              1,
					ProjectId:       1,
					PlaceholderSign: "*",
					WithSeparator:   &defaultWithSep,
				},
				outputErr: nil,
			},
			getKeyByID: getKeyByIDOp{
				isCalled: true,
				input:    1,
				output: model.Key{
					Id:  1,
					Key: "AAAAAAAAAAAAAAAAAAAAAD4r1NYivkHsOHdiq9dCiJiz0HJtt7wrVFA8fx/DenGL",
				},
				outputErr: nil,
			},
			getIpsByID: getIpsByIDOp{
				isCalled: true,
				input: &grpc.GetIpsByIDRequestV1{
					IpsId: testsdk.Ptr(uint64(1)),
				},
				output: &grpc.GetIpsByIDResponseV1{
					Name: testsdk.Ptr("some name"),
				},
				outputErr: nil,
			},
			getIssuerByID: getIssuerByIDOp{
				isCalled: true,
				input: &grpc.GetIssuerByIDRequestV1{
					IssuerId: testsdk.Ptr(uint64(1)),
				},
				output:    nil,
				outputErr: errors.New("some error"),
			},
		},
		{
			name: "encrypted_card_token_error",
			req: &grpc.GetOneClickPayInCardsRequestV1{
				ProjectId:         testsdk.Ptr(uint64(1)),
				ProjectClientId:   testsdk.Ptr("asd"),
				TransactionTypeId: testsdk.Ptr(uint64(1)),
				Amount:            testsdk.Ptr(10.0),
				ActiveTerminals: []*grpc.TerminalProjectV1{
					{
						Id:         testsdk.Ptr(uint64(1)),
						TerminalId: testsdk.Ptr(uint64(1)),
					},
				},
			},
			want:    nil,
			wantErr: aes.KeySizeError(3),
			getClientByProject: getClientByProjectOp{
				inputProjectID:       1,
				inputProjectClientID: "asd",
				output: &model.Client{
					Id:              1,
					ProjectClientId: "asd",
					ProjectId:       1,
				},
				outputErr: nil,
			},
			getActiveCardsByClient: getActiveCardsByClientOp{
				isCalled: true,
				input:    1,
				output: model.Cards{
					{
						Id:            1,
						ClientId:      1,
						IssuerId:      1,
						IpsId:         1,
						CountryId:     1,
						SaveAccess:    true,
						Approved:      false,
						KeyId:         1,
						EncryptMonth:  "AAAAAAAAAAAAAAAAAAAAAJWXVsNZooT1Nj5qlh9r4ws=",
						EncryptYear:   "AAAAAAAAAAAAAAAAAAAAAJWXVsNZooT1Nj5qlh9r4ws=",
						EncryptName:   "AAAAAAAAAAAAAAAAAAAAAJWXVsNZooT1Nj5qlh9r4ws=",
						EncryptPan:    "AAAAAAAAAAAAAAAAAAAAACXJ7eT6CzNrPzsbyIRluEFgK65FdiS3n0szvcsdV2Tb",
						Pan:           "something",
						IsPanModified: true,
						Tokens: []model.Token{
							{
								Id:         1,
								TerminalId: 1,
							},
						},
					},
				},
				outputErr: nil,
			},
			getRuleByActiveTerminals: getRuleByActiveTerminalsOp{
				isCalled: true,
				input: schema.NewRuleByActiveTerminalsReq(
					schema.RuleByActiveTerminalsReq{
						ProjectID:         1,
						TransactionTypeID: 1,
						Amount:            10,
						IpsID:             &ipsId,
						CountryID:         &countryId,
						IssuerID:          &issuerId,
						ActiveTerminalIDs: []uint64{1},
					}),
				output: &grpc.RuleByActiveTerminalsResponseV1{
					IsRuleFound: testsdk.Ptr(true),
				},
				outputErr: nil,
			},
			getProjectMaskFormatByProject: getProjectMaskFormatByProjectIDOp{
				isCalled: true,
				input:    1,
				output: &model.ProjectMaskFormat{
					Id:              1,
					ProjectId:       1,
					PlaceholderSign: "*",
					WithSeparator:   &defaultWithSep,
				},
				outputErr: nil,
			},
			getKeyByID: getKeyByIDOp{
				isCalled: true,
				input:    1,
				output: model.Key{
					Id:  1,
					Key: "AAAAAAAAAAAAAAAAAAAAAD4r1NYivkHsOHdiq9dCiJiz0HJtt7wrVFA8fx/DenGL",
				},
				outputErr: nil,
			},
			getIpsByID: getIpsByIDOp{
				isCalled: true,
				input: &grpc.GetIpsByIDRequestV1{
					IpsId: testsdk.Ptr(uint64(1)),
				},
				output: &grpc.GetIpsByIDResponseV1{
					Name: testsdk.Ptr("some name"),
				},
				outputErr: nil,
			},
			getIssuerByID: getIssuerByIDOp{
				isCalled: true,
				input: &grpc.GetIssuerByIDRequestV1{
					IssuerId: testsdk.Ptr(uint64(1)),
				},
				output: &grpc.GetIssuerByIDResponseV1{
					Name: testsdk.Ptr("some name"),
				},
				outputErr: nil,
			},
			appConfig: map[string]any{
				"SYMMETRIC_KEY": badKey,
			},
		},
		{
			name: "success",
			req: &grpc.GetOneClickPayInCardsRequestV1{
				ProjectId:         testsdk.Ptr(uint64(1)),
				ProjectClientId:   testsdk.Ptr("asd"),
				TransactionTypeId: testsdk.Ptr(uint64(1)),
				Amount:            testsdk.Ptr(10.0),
				ActiveTerminals: []*grpc.TerminalProjectV1{
					{
						Id:         testsdk.Ptr(uint64(1)),
						TerminalId: testsdk.Ptr(uint64(1)),
					},
				},
			},
			want: []*schema.EncryptedCardToken{
				{
					CardToken:  "AAAAAAAAAAAAAAAAAAAAAAa1Z7+ZP7uEJjDtmP8f3pDIe/kbCBSPpoa11F3kFocM",
					MaskedPan:  "4342-07******-3288",
					IpsName:    "some name",
					IssuerName: "some name",
					Year:       "something",
					Month:      "something",
				},
			},
			wantErr: nil,
			getClientByProject: getClientByProjectOp{
				inputProjectID:       1,
				inputProjectClientID: "asd",
				output: &model.Client{
					Id:              1,
					ProjectClientId: "asd",
					ProjectId:       1,
				},
				outputErr: nil,
			},
			getActiveCardsByClient: getActiveCardsByClientOp{
				isCalled: true,
				input:    1,
				output: model.Cards{
					{
						Id:            1,
						ClientId:      1,
						IssuerId:      1,
						IpsId:         1,
						CountryId:     1,
						SaveAccess:    true,
						Approved:      false,
						KeyId:         1,
						EncryptMonth:  "AAAAAAAAAAAAAAAAAAAAAJWXVsNZooT1Nj5qlh9r4ws=",
						EncryptYear:   "AAAAAAAAAAAAAAAAAAAAAJWXVsNZooT1Nj5qlh9r4ws=",
						EncryptName:   "AAAAAAAAAAAAAAAAAAAAAJWXVsNZooT1Nj5qlh9r4ws=",
						EncryptPan:    "AAAAAAAAAAAAAAAAAAAAACXJ7eT6CzNrPzsbyIRluEFgK65FdiS3n0szvcsdV2Tb",
						Pan:           "something",
						IsPanModified: true,
						Tokens: []model.Token{
							{
								Id:         1,
								TerminalId: 1,
							},
						},
					},
				},
				outputErr: nil,
			},
			getRuleByActiveTerminals: getRuleByActiveTerminalsOp{
				isCalled: true,
				input: schema.NewRuleByActiveTerminalsReq(
					schema.RuleByActiveTerminalsReq{
						ProjectID:         1,
						TransactionTypeID: 1,
						Amount:            10,
						IpsID:             &ipsId,
						CountryID:         &countryId,
						IssuerID:          &issuerId,
						ActiveTerminalIDs: []uint64{1},
					}),
				output: &grpc.RuleByActiveTerminalsResponseV1{
					IsRuleFound: testsdk.Ptr(true),
				},
				outputErr: nil,
			},
			getProjectMaskFormatByProject: getProjectMaskFormatByProjectIDOp{
				isCalled: true,
				input:    1,
				output: &model.ProjectMaskFormat{
					Id:              1,
					ProjectId:       1,
					PlaceholderSign: "*",
					WithSeparator:   &defaultWithSep,
				},
				outputErr: nil,
			},
			getKeyByID: getKeyByIDOp{
				isCalled: true,
				input:    1,
				output: model.Key{
					Id:  1,
					Key: "AAAAAAAAAAAAAAAAAAAAAD4r1NYivkHsOHdiq9dCiJiz0HJtt7wrVFA8fx/DenGL",
				},
				outputErr: nil,
			},
			getIpsByID: getIpsByIDOp{
				isCalled: true,
				input: &grpc.GetIpsByIDRequestV1{
					IpsId: testsdk.Ptr(uint64(1)),
				},
				output: &grpc.GetIpsByIDResponseV1{
					Name: testsdk.Ptr("some name"),
				},
				outputErr: nil,
			},
			getIssuerByID: getIssuerByIDOp{
				isCalled: true,
				input: &grpc.GetIssuerByIDRequestV1{
					IssuerId: testsdk.Ptr(uint64(1)),
				},
				output: &grpc.GetIssuerByIDResponseV1{
					Name: testsdk.Ptr("some name"),
				},
				outputErr: nil,
			},
			appConfig: map[string]any{
				"SYMMETRIC_KEY": normalKey,
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctrl := gomock.NewController(t)

			ctx, cancel := context.WithCancel(context.Background())
			e2e.InitAppE2E(t, ctx, cancel, nil, nil, tt.appConfig)

			clientDBMock := databasemocks.NewMockClientor(ctrl)
			cardGetterDBMock := databasemocks.NewMockCardGetter(ctrl)
			terminalCliMock := grpcmock.NewMockTerminalClient(ctrl)
			projectMaskDBMock := databasemocks.NewMockProjectMaskFormatter(ctrl)
			keyDBMock := databasemocks.NewMockKeyer(ctrl)
			acquirerCliMock := grpcmock.NewMockAcquirerClient(ctrl)

			clientDBMock.EXPECT().GetByProject(
				gomock.Any(),
				tt.getClientByProject.inputProjectID,
				tt.getClientByProject.inputProjectClientID,
			).Return(
				tt.getClientByProject.output,
				tt.getClientByProject.outputErr,
			)

			if tt.getActiveCardsByClient.isCalled {
				cardGetterDBMock.EXPECT().GetActiveCardsByClient(
					gomock.Any(),
					tt.getActiveCardsByClient.input,
				).Return(
					tt.getActiveCardsByClient.output,
					tt.getActiveCardsByClient.outputErr,
				)
			}

			if tt.getRuleByActiveTerminals.isCalled {
				terminalCliMock.EXPECT().GetRuleByActiveTerminals(
					gomock.Any(),
					tt.getRuleByActiveTerminals.input,
				).Return(
					tt.getRuleByActiveTerminals.output,
					tt.getRuleByActiveTerminals.outputErr,
				)
			}

			if tt.getProjectMaskFormatByProject.isCalled {
				projectMaskDBMock.EXPECT().GetByProjectID(
					gomock.Any(),
					tt.getProjectMaskFormatByProject.input,
				).Return(
					tt.getProjectMaskFormatByProject.output,
					tt.getProjectMaskFormatByProject.outputErr,
				)
			}

			if tt.getKeyByID.isCalled {
				keyDBMock.EXPECT().GetById(
					gomock.Any(),
					tt.getKeyByID.input,
				).Return(
					tt.getKeyByID.output,
					tt.getKeyByID.outputErr,
				)
			}

			if tt.getIpsByID.isCalled {
				acquirerCliMock.EXPECT().GetIpsByID(
					gomock.Any(),
					tt.getIpsByID.input,
				).Return(
					tt.getIpsByID.output,
					tt.getIpsByID.outputErr,
				)
			}

			if tt.getIssuerByID.isCalled {
				acquirerCliMock.EXPECT().GetIssuerByID(
					gomock.Any(),
					tt.getIssuerByID.input,
				).Return(
					tt.getIssuerByID.output,
					tt.getIssuerByID.outputErr,
				)
			}

			s := OneClickCardService{
				keyRepo:               keyDBMock,
				clientsRepo:           clientDBMock,
				cardGetterRepo:        cardGetterDBMock,
				projectMaskFormatRepo: projectMaskDBMock,
				acquirerClient:        acquirerCliMock,
				terminalClient:        terminalCliMock,
			}

			res, err := s.GetOneClickPayInCards(context.Background(), tt.req)
			if tt.wantErr != nil {
				require.Error(t, err)
				require.ErrorContains(t, err, tt.wantErr.Error())
			} else {
				require.NoError(t, err)
			}

			require.Equal(t, tt.want, res)
		})
	}
}

func TestGetOneClickPayOutCards(t *testing.T) {
	type getClientByProjectOp struct {
		inputProjectID       uint64
		inputProjectClientID string
		output               *model.Client
		outputErr            error
	}

	type getActiveCardsByClientOp struct {
		isCalled  bool
		input     uint64
		output    model.Cards
		outputErr error
	}

	type getRuleByActiveTerminalsOp struct {
		isCalled  bool
		input     *grpc.RuleByActiveTerminalsReqV1
		output    *grpc.RuleByActiveTerminalsResponseV1
		outputErr error
	}

	type getProjectMaskByProjectIDOp struct {
		isCalled  bool
		input     uint64
		output    *model.ProjectMaskFormat
		outputErr error
	}

	type getKeyByIDOp struct {
		isCalled  bool
		input     uint64
		output    model.Key
		outputErr error
	}

	var (
		ipsID          uint64 = 1
		issuerID       uint64 = 1
		countryID      uint64 = 1
		defaultWithSep        = true
	)

	tests := []struct {
		name                      string
		req                       *grpc.GetOneClickPayOutCardsRequestV1
		want                      map[string]string
		wantErr                   error
		appConfig                 map[string]any
		getClientByProject        getClientByProjectOp
		getActiveCardsByClient    getActiveCardsByClientOp
		getRuleByActiveTerminals  getRuleByActiveTerminalsOp
		getProjectMaskByProjectID getProjectMaskByProjectIDOp
		getKeyByID                getKeyByIDOp
	}{
		{
			name: "error_getting_client_by_project",
			req: &grpc.GetOneClickPayOutCardsRequestV1{
				ProjectId:       testsdk.Ptr(uint64(1)),
				ProjectClientId: testsdk.Ptr("asd"),
				Amount:          testsdk.Ptr(10.0),
				ActiveTerminals: []*grpc.TerminalProjectV1{
					{
						Id:                testsdk.Ptr(uint64(1)),
						TerminalId:        testsdk.Ptr(uint64(1)),
						TransactionTypeId: testsdk.Ptr(uint64(1)),
					},
				},
			},
			want:    nil,
			wantErr: errors.New("some error"),
			getClientByProject: getClientByProjectOp{
				inputProjectID:       1,
				inputProjectClientID: "asd",
				outputErr:            errors.New("some error"),
				output:               nil,
			},
		},
		{
			name: "error_getting_active_card_by_client",
			req: &grpc.GetOneClickPayOutCardsRequestV1{
				ProjectId:       testsdk.Ptr(uint64(1)),
				ProjectClientId: testsdk.Ptr("asd"),
				Amount:          testsdk.Ptr(10.0),
				ActiveTerminals: []*grpc.TerminalProjectV1{
					{
						Id:                testsdk.Ptr(uint64(1)),
						TerminalId:        testsdk.Ptr(uint64(1)),
						TransactionTypeId: testsdk.Ptr(uint64(1)),
					},
				},
			},
			want:    nil,
			wantErr: errors.New("some error"),
			getClientByProject: getClientByProjectOp{
				inputProjectID:       1,
				inputProjectClientID: "asd",
				outputErr:            nil,
				output: &model.Client{
					Id:              1,
					ProjectClientId: "asd",
					ProjectId:       1,
				},
			},
			getActiveCardsByClient: getActiveCardsByClientOp{
				isCalled:  true,
				input:     1,
				output:    nil,
				outputErr: errors.New("some error"),
			},
		},
		{
			name: "error_getting_rule_by_active_terminals",
			req: &grpc.GetOneClickPayOutCardsRequestV1{
				ProjectId:       testsdk.Ptr(uint64(1)),
				ProjectClientId: testsdk.Ptr("asd"),
				Amount:          testsdk.Ptr(10.0),
				ActiveTerminals: []*grpc.TerminalProjectV1{
					{
						Id:                testsdk.Ptr(uint64(1)),
						TerminalId:        testsdk.Ptr(uint64(1)),
						TransactionTypeId: testsdk.Ptr(uint64(1)),
					},
				},
			},
			want:    nil,
			wantErr: nil,
			getClientByProject: getClientByProjectOp{
				inputProjectID:       1,
				inputProjectClientID: "asd",
				outputErr:            nil,
				output: &model.Client{
					Id:              1,
					ProjectClientId: "asd",
					ProjectId:       1,
				},
			},
			getActiveCardsByClient: getActiveCardsByClientOp{
				isCalled: true,
				input:    1,
				output: model.Cards{
					{
						Id:            1,
						ClientId:      1,
						IssuerId:      1,
						IpsId:         1,
						CountryId:     1,
						SaveAccess:    true,
						Approved:      false,
						KeyId:         1,
						EncryptMonth:  "AAAAAAAAAAAAAAAAAAAAAJWXVsNZooT1Nj5qlh9r4ws=",
						EncryptYear:   "AAAAAAAAAAAAAAAAAAAAAJWXVsNZooT1Nj5qlh9r4ws=",
						EncryptName:   "AAAAAAAAAAAAAAAAAAAAAJWXVsNZooT1Nj5qlh9r4ws=",
						EncryptPan:    "AAAAAAAAAAAAAAAAAAAAACXJ7eT6CzNrPzsbyIRluEFgK65FdiS3n0szvcsdV2Tb",
						Pan:           "something",
						IsPanModified: true,
					},
				},
				outputErr: nil,
			},
			getRuleByActiveTerminals: getRuleByActiveTerminalsOp{
				isCalled: true,
				input: schema.NewRuleByActiveTerminalsReq(
					schema.RuleByActiveTerminalsReq{
						ProjectID:         1,
						TransactionTypeID: 1,
						Amount:            10,
						IpsID:             &ipsID,
						CountryID:         &countryID,
						IssuerID:          &issuerID,
						ActiveTerminalIDs: []uint64{1},
					}),
				output:    nil,
				outputErr: errors.New("some error"),
			},
		},
		{
			name: "error_getting_project_mask",
			req: &grpc.GetOneClickPayOutCardsRequestV1{
				ProjectId:       testsdk.Ptr(uint64(1)),
				ProjectClientId: testsdk.Ptr("asd"),
				Amount:          testsdk.Ptr(10.0),
				ActiveTerminals: []*grpc.TerminalProjectV1{
					{
						Id:                testsdk.Ptr(uint64(1)),
						TerminalId:        testsdk.Ptr(uint64(1)),
						TransactionTypeId: testsdk.Ptr(uint64(1)),
					},
				},
			},
			want:    nil,
			wantErr: errors.New("some error"),
			getClientByProject: getClientByProjectOp{
				inputProjectID:       1,
				inputProjectClientID: "asd",
				outputErr:            nil,
				output: &model.Client{
					Id:              1,
					ProjectClientId: "asd",
					ProjectId:       1,
				},
			},
			getActiveCardsByClient: getActiveCardsByClientOp{
				isCalled: true,
				input:    1,
				output: model.Cards{
					{
						Id:            1,
						ClientId:      1,
						IssuerId:      1,
						IpsId:         1,
						CountryId:     1,
						SaveAccess:    true,
						Approved:      true,
						KeyId:         1,
						EncryptMonth:  "AAAAAAAAAAAAAAAAAAAAAJWXVsNZooT1Nj5qlh9r4ws=",
						EncryptYear:   "AAAAAAAAAAAAAAAAAAAAAJWXVsNZooT1Nj5qlh9r4ws=",
						EncryptName:   "AAAAAAAAAAAAAAAAAAAAAJWXVsNZooT1Nj5qlh9r4ws=",
						EncryptPan:    "AAAAAAAAAAAAAAAAAAAAACXJ7eT6CzNrPzsbyIRluEFgK65FdiS3n0szvcsdV2Tb",
						Pan:           "something",
						IsPanModified: true,
						Tokens: []model.Token{
							{
								Id:         1,
								TerminalId: 1,
							},
						},
					},
				},
				outputErr: nil,
			},
			getRuleByActiveTerminals: getRuleByActiveTerminalsOp{
				isCalled: true,
				input: schema.NewRuleByActiveTerminalsReq(
					schema.RuleByActiveTerminalsReq{
						ProjectID:         1,
						TransactionTypeID: 1,
						Amount:            10,
						IpsID:             &ipsID,
						CountryID:         &countryID,
						IssuerID:          &issuerID,
						ActiveTerminalIDs: []uint64{1},
					}),
				output: &grpc.RuleByActiveTerminalsResponseV1{
					IsRuleFound: testsdk.Ptr(true),
				},
				outputErr: nil,
			},
			getProjectMaskByProjectID: getProjectMaskByProjectIDOp{
				isCalled:  true,
				input:     1,
				output:    nil,
				outputErr: errors.New("some error"),
			},
		},
		{
			name: "error_getting_key_by_id",
			req: &grpc.GetOneClickPayOutCardsRequestV1{
				ProjectId:       testsdk.Ptr(uint64(1)),
				ProjectClientId: testsdk.Ptr("asd"),
				Amount:          testsdk.Ptr(10.0),
				ActiveTerminals: []*grpc.TerminalProjectV1{
					{
						Id:                testsdk.Ptr(uint64(1)),
						TerminalId:        testsdk.Ptr(uint64(1)),
						TransactionTypeId: testsdk.Ptr(uint64(1)),
					},
				},
			},
			want:    nil,
			wantErr: errors.New("some error"),
			getClientByProject: getClientByProjectOp{
				inputProjectID:       1,
				inputProjectClientID: "asd",
				outputErr:            nil,
				output: &model.Client{
					Id:              1,
					ProjectClientId: "asd",
					ProjectId:       1,
				},
			},
			getActiveCardsByClient: getActiveCardsByClientOp{
				isCalled: true,
				input:    1,
				output: model.Cards{
					{
						Id:            1,
						ClientId:      1,
						IssuerId:      1,
						IpsId:         1,
						CountryId:     1,
						SaveAccess:    true,
						Approved:      true,
						KeyId:         1,
						EncryptMonth:  "AAAAAAAAAAAAAAAAAAAAAJWXVsNZooT1Nj5qlh9r4ws=",
						EncryptYear:   "AAAAAAAAAAAAAAAAAAAAAJWXVsNZooT1Nj5qlh9r4ws=",
						EncryptName:   "AAAAAAAAAAAAAAAAAAAAAJWXVsNZooT1Nj5qlh9r4ws=",
						EncryptPan:    "AAAAAAAAAAAAAAAAAAAAACXJ7eT6CzNrPzsbyIRluEFgK65FdiS3n0szvcsdV2Tb",
						Pan:           "something",
						IsPanModified: true,
						Tokens: []model.Token{
							{
								Id:         1,
								TerminalId: 1,
							},
						},
					},
				},
				outputErr: nil,
			},
			getRuleByActiveTerminals: getRuleByActiveTerminalsOp{
				isCalled: true,
				input: schema.NewRuleByActiveTerminalsReq(
					schema.RuleByActiveTerminalsReq{
						ProjectID:         1,
						TransactionTypeID: 1,
						Amount:            10,
						IpsID:             &ipsID,
						CountryID:         &countryID,
						IssuerID:          &issuerID,
						ActiveTerminalIDs: []uint64{1},
					}),
				output: &grpc.RuleByActiveTerminalsResponseV1{
					IsRuleFound: testsdk.Ptr(true),
				},
				outputErr: nil,
			},
			getProjectMaskByProjectID: getProjectMaskByProjectIDOp{
				isCalled: true,
				input:    1,
				output: &model.ProjectMaskFormat{
					WithSeparator:   &defaultWithSep,
					Id:              1,
					ProjectId:       1,
					PlaceholderSign: "*",
				},
				outputErr: nil,
			},
			getKeyByID: getKeyByIDOp{
				isCalled:  true,
				input:     1,
				output:    model.Key{},
				outputErr: errors.New("some error"),
			},
			appConfig: map[string]any{
				"SYMMETRIC_KEY": badKey,
			},
		},
		{
			name: "decryption_error",
			req: &grpc.GetOneClickPayOutCardsRequestV1{
				ProjectId:       testsdk.Ptr(uint64(1)),
				ProjectClientId: testsdk.Ptr("asd"),
				Amount:          testsdk.Ptr(10.0),
				ActiveTerminals: []*grpc.TerminalProjectV1{
					{
						Id:                testsdk.Ptr(uint64(1)),
						TerminalId:        testsdk.Ptr(uint64(1)),
						TransactionTypeId: testsdk.Ptr(uint64(1)),
					},
				},
			},
			want:    nil,
			wantErr: aes.KeySizeError(3),
			getClientByProject: getClientByProjectOp{
				inputProjectID:       1,
				inputProjectClientID: "asd",
				outputErr:            nil,
				output: &model.Client{
					Id:              1,
					ProjectClientId: "asd",
					ProjectId:       1,
				},
			},
			getActiveCardsByClient: getActiveCardsByClientOp{
				isCalled: true,
				input:    1,
				output: model.Cards{
					{
						Id:            1,
						ClientId:      1,
						IssuerId:      1,
						IpsId:         1,
						CountryId:     1,
						SaveAccess:    true,
						Approved:      true,
						KeyId:         1,
						EncryptMonth:  "AAAAAAAAAAAAAAAAAAAAAJWXVsNZooT1Nj5qlh9r4ws=",
						EncryptYear:   "AAAAAAAAAAAAAAAAAAAAAJWXVsNZooT1Nj5qlh9r4ws=",
						EncryptName:   "AAAAAAAAAAAAAAAAAAAAAJWXVsNZooT1Nj5qlh9r4ws=",
						EncryptPan:    "AAAAAAAAAAAAAAAAAAAAACXJ7eT6CzNrPzsbyIRluEFgK65FdiS3n0szvcsdV2Tb",
						Pan:           "something",
						IsPanModified: true,
						Tokens: []model.Token{
							{
								Id:         1,
								TerminalId: 1,
							},
						},
					},
				},
				outputErr: nil,
			},
			getRuleByActiveTerminals: getRuleByActiveTerminalsOp{
				isCalled: true,
				input: schema.NewRuleByActiveTerminalsReq(
					schema.RuleByActiveTerminalsReq{
						ProjectID:         1,
						TransactionTypeID: 1,
						Amount:            10,
						IpsID:             &ipsID,
						CountryID:         &countryID,
						IssuerID:          &issuerID,
						ActiveTerminalIDs: []uint64{1},
					}),
				output: &grpc.RuleByActiveTerminalsResponseV1{
					IsRuleFound: testsdk.Ptr(true),
				},
				outputErr: nil,
			},
			getProjectMaskByProjectID: getProjectMaskByProjectIDOp{
				isCalled: true,
				input:    1,
				output: &model.ProjectMaskFormat{
					WithSeparator:   &defaultWithSep,
					Id:              1,
					ProjectId:       1,
					PlaceholderSign: "*",
				},
				outputErr: nil,
			},
			getKeyByID: getKeyByIDOp{
				isCalled: true,
				input:    1,
				output: model.Key{
					Id:  1,
					Key: "AAAAAAAAAAAAAAAAAAAAAD4r1NYivkHsOHdiq9dCiJiz0HJtt7wrVFA8fx/DenGL",
				},
				outputErr: nil,
			},
			appConfig: map[string]any{
				"SYMMETRIC_KEY": badKey,
			},
		},
		{
			name: "get_masked_card_error",
			req: &grpc.GetOneClickPayOutCardsRequestV1{
				ProjectId:       testsdk.Ptr(uint64(1)),
				ProjectClientId: testsdk.Ptr("asd"),
				Amount:          testsdk.Ptr(10.0),
				ActiveTerminals: []*grpc.TerminalProjectV1{
					{
						Id:                testsdk.Ptr(uint64(1)),
						TerminalId:        testsdk.Ptr(uint64(1)),
						TransactionTypeId: testsdk.Ptr(uint64(1)),
					},
				},
			},
			want:    nil,
			wantErr: goerr.ErrPanLength,
			getClientByProject: getClientByProjectOp{
				inputProjectID:       1,
				inputProjectClientID: "asd",
				outputErr:            nil,
				output: &model.Client{
					Id:              1,
					ProjectClientId: "asd",
					ProjectId:       1,
				},
			},
			getActiveCardsByClient: getActiveCardsByClientOp{
				isCalled: true,
				input:    1,
				output: model.Cards{
					{
						Id:            1,
						ClientId:      1,
						IssuerId:      1,
						IpsId:         1,
						CountryId:     1,
						SaveAccess:    true,
						Approved:      true,
						KeyId:         1,
						EncryptMonth:  "AAAAAAAAAAAAAAAAAAAAAJWXVsNZooT1Nj5qlh9r4ws=",
						EncryptYear:   "AAAAAAAAAAAAAAAAAAAAAJWXVsNZooT1Nj5qlh9r4ws=",
						EncryptName:   "AAAAAAAAAAAAAAAAAAAAAJWXVsNZooT1Nj5qlh9r4ws=",
						EncryptPan:    "AAAAAAAAAAAAAAAAAAAAAJWXVsNZooT1Nj5qlh9r4ws=",
						HashedPan:     "some pan",
						Pan:           "something",
						IsPanModified: true,
						Tokens: []model.Token{
							{
								Id:         1,
								TerminalId: 1,
							},
						},
					},
				},
				outputErr: nil,
			},
			getRuleByActiveTerminals: getRuleByActiveTerminalsOp{
				isCalled: true,
				input: schema.NewRuleByActiveTerminalsReq(
					schema.RuleByActiveTerminalsReq{
						ProjectID:         1,
						TransactionTypeID: 1,
						Amount:            10,
						IpsID:             &ipsID,
						CountryID:         &countryID,
						IssuerID:          &issuerID,
						ActiveTerminalIDs: []uint64{1},
					}),
				output: &grpc.RuleByActiveTerminalsResponseV1{
					IsRuleFound: testsdk.Ptr(true),
				},
				outputErr: nil,
			},
			getProjectMaskByProjectID: getProjectMaskByProjectIDOp{
				isCalled: true,
				input:    1,
				output: &model.ProjectMaskFormat{
					WithSeparator:   &defaultWithSep,
					Id:              1,
					ProjectId:       1,
					PlaceholderSign: "*",
				},
				outputErr: nil,
			},
			getKeyByID: getKeyByIDOp{
				isCalled: true,
				input:    1,
				output: model.Key{
					Id:  1,
					Key: "AAAAAAAAAAAAAAAAAAAAAD4r1NYivkHsOHdiq9dCiJiz0HJtt7wrVFA8fx/DenGL",
				},
				outputErr: nil,
			},
			appConfig: map[string]any{
				"SYMMETRIC_KEY": normalKey,
			},
		},
		{
			name: "success",
			req: &grpc.GetOneClickPayOutCardsRequestV1{
				ProjectId:       testsdk.Ptr(uint64(1)),
				ProjectClientId: testsdk.Ptr("asd"),
				Amount:          testsdk.Ptr(10.0),
				ActiveTerminals: []*grpc.TerminalProjectV1{
					{
						Id:                testsdk.Ptr(uint64(1)),
						TerminalId:        testsdk.Ptr(uint64(1)),
						TransactionTypeId: testsdk.Ptr(uint64(1)),
					},
				},
			},
			want: map[string]string{
				"AAAAAAAAAAAAAAAAAAAAAAa1Z7+ZP7uEJjDtmP8f3pD4u0oe/96rjUFy/R82zEeO": "4342-07******-3288",
			},
			wantErr: nil,
			getClientByProject: getClientByProjectOp{
				inputProjectID:       1,
				inputProjectClientID: "asd",
				outputErr:            nil,
				output: &model.Client{
					Id:              1,
					ProjectClientId: "asd",
					ProjectId:       1,
				},
			},
			getActiveCardsByClient: getActiveCardsByClientOp{
				isCalled: true,
				input:    1,
				output: model.Cards{
					{
						Id:            1,
						ClientId:      1,
						IssuerId:      1,
						IpsId:         1,
						CountryId:     1,
						SaveAccess:    true,
						Approved:      true,
						KeyId:         1,
						EncryptMonth:  "AAAAAAAAAAAAAAAAAAAAAJWXVsNZooT1Nj5qlh9r4ws=",
						EncryptYear:   "AAAAAAAAAAAAAAAAAAAAAJWXVsNZooT1Nj5qlh9r4ws=",
						EncryptName:   "AAAAAAAAAAAAAAAAAAAAAJWXVsNZooT1Nj5qlh9r4ws=",
						EncryptPan:    "AAAAAAAAAAAAAAAAAAAAACXJ7eT6CzNrPzsbyIRluEFgK65FdiS3n0szvcsdV2Tb",
						HashedPan:     "some pan",
						Pan:           "something",
						IsPanModified: true,
						Tokens: []model.Token{
							{
								Id:         1,
								TerminalId: 1,
							},
						},
					},
				},
				outputErr: nil,
			},
			getRuleByActiveTerminals: getRuleByActiveTerminalsOp{
				isCalled: true,
				input: schema.NewRuleByActiveTerminalsReq(
					schema.RuleByActiveTerminalsReq{
						ProjectID:         1,
						TransactionTypeID: 1,
						Amount:            10,
						IpsID:             &ipsID,
						CountryID:         &countryID,
						IssuerID:          &issuerID,
						ActiveTerminalIDs: []uint64{1},
					}),
				output: &grpc.RuleByActiveTerminalsResponseV1{
					IsRuleFound: testsdk.Ptr(true),
				},
				outputErr: nil,
			},
			getProjectMaskByProjectID: getProjectMaskByProjectIDOp{
				isCalled: true,
				input:    1,
				output: &model.ProjectMaskFormat{
					WithSeparator:   &defaultWithSep,
					Id:              1,
					ProjectId:       1,
					PlaceholderSign: "*",
				},
				outputErr: nil,
			},
			getKeyByID: getKeyByIDOp{
				isCalled: true,
				input:    1,
				output: model.Key{
					Id:  1,
					Key: "AAAAAAAAAAAAAAAAAAAAAD4r1NYivkHsOHdiq9dCiJiz0HJtt7wrVFA8fx/DenGL",
				},
				outputErr: nil,
			},
			appConfig: map[string]any{
				"SYMMETRIC_KEY": normalKey,
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctrl := gomock.NewController(t)

			ctx, cancel := context.WithCancel(context.Background())
			e2e.InitAppE2E(t, ctx, cancel, nil, nil, tt.appConfig)

			clientDBMock := databasemocks.NewMockClientor(ctrl)
			cardGetterDBMock := databasemocks.NewMockCardGetter(ctrl)
			terminalCliMock := grpcmock.NewMockTerminalClient(ctrl)
			projectMaskFormatDBMock := databasemocks.NewMockProjectMaskFormatter(ctrl)
			keyDBMock := databasemocks.NewMockKeyer(ctrl)

			clientDBMock.EXPECT().GetByProject(
				gomock.Any(),
				tt.getClientByProject.inputProjectID,
				tt.getClientByProject.inputProjectClientID,
			).Return(
				tt.getClientByProject.output,
				tt.getClientByProject.outputErr,
			)

			if tt.getActiveCardsByClient.isCalled {
				cardGetterDBMock.EXPECT().GetActiveCardsByClient(
					gomock.Any(),
					tt.getActiveCardsByClient.input,
				).Return(
					tt.getActiveCardsByClient.output,
					tt.getActiveCardsByClient.outputErr,
				)
			}

			if tt.getRuleByActiveTerminals.isCalled {
				terminalCliMock.EXPECT().GetRuleByActiveTerminals(
					gomock.Any(),
					tt.getRuleByActiveTerminals.input,
				).Return(
					tt.getRuleByActiveTerminals.output,
					tt.getRuleByActiveTerminals.outputErr,
				)
			}

			if tt.getProjectMaskByProjectID.isCalled {
				projectMaskFormatDBMock.EXPECT().GetByProjectID(
					gomock.Any(),
					tt.getProjectMaskByProjectID.input,
				).Return(
					tt.getProjectMaskByProjectID.output,
					tt.getProjectMaskByProjectID.outputErr,
				)
			}

			if tt.getKeyByID.isCalled {
				keyDBMock.EXPECT().GetById(
					gomock.Any(),
					tt.getKeyByID.input,
				).Return(
					tt.getKeyByID.output,
					tt.getKeyByID.outputErr,
				)
			}

			s := OneClickCardService{
				keyRepo:               keyDBMock,
				clientsRepo:           clientDBMock,
				cardGetterRepo:        cardGetterDBMock,
				projectMaskFormatRepo: projectMaskFormatDBMock,
				terminalClient:        terminalCliMock,
			}

			res, err := s.GetOneClickPayOutCards(context.Background(), tt.req)
			if tt.wantErr != nil {
				require.Error(t, err)
				require.ErrorContains(t, err, tt.wantErr.Error())
			} else {
				require.NoError(t, err)
			}

			require.Equal(t, tt.want, res)
		})
	}
}
