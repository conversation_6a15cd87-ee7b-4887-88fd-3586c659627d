// Code generated by MockGen. DO NOT EDIT.
// Source: terminal_cards_grpc.pb.go

// Package grpcmock is a generated GoMock package.
package grpcmock

import (
	context "context"
	reflect "reflect"

	grpc "git.local/sensitive/innerpb/processing/grpc"
	gomock "github.com/golang/mock/gomock"
	grpc0 "google.golang.org/grpc"
)

// MockTerminalCardsClient is a mock of TerminalCardsClient interface.
type MockTerminalCardsClient struct {
	ctrl     *gomock.Controller
	recorder *MockTerminalCardsClientMockRecorder
}

// MockTerminalCardsClientMockRecorder is the mock recorder for MockTerminalCardsClient.
type MockTerminalCardsClientMockRecorder struct {
	mock *MockTerminalCardsClient
}

// NewMockTerminalCardsClient creates a new mock instance.
func NewMockTerminalCardsClient(ctrl *gomock.Controller) *MockTerminalCardsClient {
	mock := &MockTerminalCardsClient{ctrl: ctrl}
	mock.recorder = &MockTerminalCardsClientMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockTerminalCardsClient) EXPECT() *MockTerminalCardsClientMockRecorder {
	return m.recorder
}

// GetCardInfoByPan mocks base method.
func (m *MockTerminalCardsClient) GetCardInfoByPan(ctx context.Context, in *grpc.GetCardInfoByPanRequestV1, opts ...grpc0.CallOption) (*grpc.GetCardInfoByPanResponseV1, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetCardInfoByPan", varargs...)
	ret0, _ := ret[0].(*grpc.GetCardInfoByPanResponseV1)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetCardInfoByPan indicates an expected call of GetCardInfoByPan.
func (mr *MockTerminalCardsClientMockRecorder) GetCardInfoByPan(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetCardInfoByPan", reflect.TypeOf((*MockTerminalCardsClient)(nil).GetCardInfoByPan), varargs...)
}

// MockTerminalCardsServer is a mock of TerminalCardsServer interface.
type MockTerminalCardsServer struct {
	ctrl     *gomock.Controller
	recorder *MockTerminalCardsServerMockRecorder
}

// MockTerminalCardsServerMockRecorder is the mock recorder for MockTerminalCardsServer.
type MockTerminalCardsServerMockRecorder struct {
	mock *MockTerminalCardsServer
}

// NewMockTerminalCardsServer creates a new mock instance.
func NewMockTerminalCardsServer(ctrl *gomock.Controller) *MockTerminalCardsServer {
	mock := &MockTerminalCardsServer{ctrl: ctrl}
	mock.recorder = &MockTerminalCardsServerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockTerminalCardsServer) EXPECT() *MockTerminalCardsServerMockRecorder {
	return m.recorder
}

// GetCardInfoByPan mocks base method.
func (m *MockTerminalCardsServer) GetCardInfoByPan(arg0 context.Context, arg1 *grpc.GetCardInfoByPanRequestV1) (*grpc.GetCardInfoByPanResponseV1, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetCardInfoByPan", arg0, arg1)
	ret0, _ := ret[0].(*grpc.GetCardInfoByPanResponseV1)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetCardInfoByPan indicates an expected call of GetCardInfoByPan.
func (mr *MockTerminalCardsServerMockRecorder) GetCardInfoByPan(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetCardInfoByPan", reflect.TypeOf((*MockTerminalCardsServer)(nil).GetCardInfoByPan), arg0, arg1)
}

// mustEmbedUnimplementedTerminalCardsServer mocks base method.
func (m *MockTerminalCardsServer) mustEmbedUnimplementedTerminalCardsServer() {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "mustEmbedUnimplementedTerminalCardsServer")
}

// mustEmbedUnimplementedTerminalCardsServer indicates an expected call of mustEmbedUnimplementedTerminalCardsServer.
func (mr *MockTerminalCardsServerMockRecorder) mustEmbedUnimplementedTerminalCardsServer() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "mustEmbedUnimplementedTerminalCardsServer", reflect.TypeOf((*MockTerminalCardsServer)(nil).mustEmbedUnimplementedTerminalCardsServer))
}

// MockUnsafeTerminalCardsServer is a mock of UnsafeTerminalCardsServer interface.
type MockUnsafeTerminalCardsServer struct {
	ctrl     *gomock.Controller
	recorder *MockUnsafeTerminalCardsServerMockRecorder
}

// MockUnsafeTerminalCardsServerMockRecorder is the mock recorder for MockUnsafeTerminalCardsServer.
type MockUnsafeTerminalCardsServerMockRecorder struct {
	mock *MockUnsafeTerminalCardsServer
}

// NewMockUnsafeTerminalCardsServer creates a new mock instance.
func NewMockUnsafeTerminalCardsServer(ctrl *gomock.Controller) *MockUnsafeTerminalCardsServer {
	mock := &MockUnsafeTerminalCardsServer{ctrl: ctrl}
	mock.recorder = &MockUnsafeTerminalCardsServerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockUnsafeTerminalCardsServer) EXPECT() *MockUnsafeTerminalCardsServerMockRecorder {
	return m.recorder
}

// mustEmbedUnimplementedTerminalCardsServer mocks base method.
func (m *MockUnsafeTerminalCardsServer) mustEmbedUnimplementedTerminalCardsServer() {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "mustEmbedUnimplementedTerminalCardsServer")
}

// mustEmbedUnimplementedTerminalCardsServer indicates an expected call of mustEmbedUnimplementedTerminalCardsServer.
func (mr *MockUnsafeTerminalCardsServerMockRecorder) mustEmbedUnimplementedTerminalCardsServer() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "mustEmbedUnimplementedTerminalCardsServer", reflect.TypeOf((*MockUnsafeTerminalCardsServer)(nil).mustEmbedUnimplementedTerminalCardsServer))
}
