package service

import (
	"context"
	"crypto/aes"
	"errors"
	"testing"

	"git.local/sensitive/innerpb/processing/goerr"
	"git.local/sensitive/innerpb/processing/grpc/grpcmock"
	"git.local/sensitive/pkg/encryptor"
	"git.local/sensitive/testsdk"

	"github.com/golang/mock/gomock"
	"github.com/stretchr/testify/require"

	"git.local/sensitive/innerpb/processing/grpc"
	"git.local/sensitive/pkg/middlewares"
	"git.local/sensitive/processing/card/model"
	"git.local/sensitive/processing/card/repository/database/databasemocks"
	"git.local/sensitive/processing/card/schema"
	"git.local/sensitive/sdk/dog/e2e"
)

func isCardMatches(got, want *model.Card) bool {
	if got == nil && got == want {
		return true
	}

	if got == nil || want == nil {
		return false
	}

	return got.ClientId == want.ClientId &&
		got.IssuerId == want.IssuerId &&
		got.IpsId == want.IpsId &&
		got.CountryId == want.CountryId &&
		got.SaveAccess == want.SaveAccess &&
		got.KeyId == want.KeyId &&
		got.EncryptPan == want.EncryptPan
}

type cardMatcher struct{ want *model.Card }

func (m cardMatcher) Matches(x interface{}) bool {
	got, ok := x.(*model.Card)
	if !ok {
		return false
	}
	return isCardMatches(got, m.want)
}

func (m cardMatcher) String() string { return "card equal (except HashedPan)" }

func TestGetProjectClients(t *testing.T) {
	type getProjectClientsOp struct {
		inputPagination *middlewares.PaginationInfo
		inputProjectID  uint64
		output          []*model.Client
		outputErr       error
	}

	tests := []struct {
		name              string
		reqPagination     *middlewares.PaginationInfo
		req               uint64
		want              []*model.Client
		wantErr           error
		getProjectClients getProjectClientsOp
	}{
		{
			name: "error",
			reqPagination: &middlewares.PaginationInfo{
				Page:       1,
				PerPage:    1,
				Pagination: true,
			},
			req:     1,
			wantErr: errors.New("some error"),
			want:    nil,
			getProjectClients: getProjectClientsOp{
				inputPagination: &middlewares.PaginationInfo{
					Page:       1,
					PerPage:    1,
					Pagination: true,
				},
				inputProjectID: 1,
				output:         nil,
				outputErr:      errors.New("some error"),
			},
		},
		{
			name: "success",
			reqPagination: &middlewares.PaginationInfo{
				Page:       1,
				PerPage:    1,
				Pagination: true,
			},
			req:     1,
			wantErr: nil,
			want: []*model.Client{
				{
					Id:              1,
					ProjectId:       1,
					ProjectClientId: "asd",
				},
			},
			getProjectClients: getProjectClientsOp{
				inputPagination: &middlewares.PaginationInfo{
					Page:       1,
					PerPage:    1,
					Pagination: true,
				},
				inputProjectID: 1,
				output: []*model.Client{
					{
						Id:              1,
						ProjectId:       1,
						ProjectClientId: "asd",
					},
				},
				outputErr: nil,
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctrl := gomock.NewController(t)

			clientsDBMock := databasemocks.NewMockClientor(ctrl)

			clientsDBMock.EXPECT().GetProjectClients(
				gomock.Any(),
				tt.getProjectClients.inputPagination,
				tt.getProjectClients.inputProjectID,
			).Return(
				tt.getProjectClients.output,
				tt.getProjectClients.outputErr,
			)

			s := ClientService{
				clientsRepo: clientsDBMock,
			}

			res, err := s.GetProjectClients(context.Background(), tt.reqPagination, tt.req)
			if tt.wantErr != nil {
				require.Error(t, err)
				require.ErrorContains(t, err, tt.wantErr.Error())
			} else {
				require.NoError(t, err)
			}

			require.Equal(t, tt.want, res)
		})
	}
}

func TestGetCardPans(t *testing.T) {
	defaultWithSep := true

	type getClientByIDOp struct {
		input     uint64
		output    *model.Client
		outputErr error
	}

	type checkProjectOp struct {
		isCalled  bool
		input     *grpc.CheckMerchantProjectRequestV1
		outputErr error
	}

	type getCardByClientOp struct {
		isCalled        bool
		input           uint64
		inputPagination *middlewares.PaginationInfo
		output          []model.Card
		outputErr       error
	}

	type getProjectMaskByProjectIDOp struct {
		isCalled  bool
		input     uint64
		output    *model.ProjectMaskFormat
		outputErr error
	}

	type getKeyByIDOp struct {
		isCalled  bool
		input     uint64
		output    model.Key
		outputErr error
	}

	tests := []struct {
		name                    string
		reqClientID, reqMerchID uint64
		reqPagination           *middlewares.PaginationInfo
		want                    []schema.MaskedPanInfo
		wantErr                 error
		getClientByID           getClientByIDOp
		checkProject            checkProjectOp
		getCardByClient         getCardByClientOp
		getProjectMaskByProject getProjectMaskByProjectIDOp
		getKeyByID              getKeyByIDOp
		appConfig               map[string]any
	}{
		{
			name:        "error_getting_by_id",
			reqClientID: 1,
			reqMerchID:  1,
			reqPagination: &middlewares.PaginationInfo{
				Page:       1,
				PerPage:    1,
				Pagination: true,
			},
			getClientByID: getClientByIDOp{
				input:     1,
				output:    nil,
				outputErr: errors.New("some error"),
			},
			wantErr: errors.New("some error"),
			want:    nil,
		},
		{
			name:        "check_project_error",
			reqClientID: 1,
			reqMerchID:  1,
			reqPagination: &middlewares.PaginationInfo{
				Page:       1,
				PerPage:    1,
				Pagination: true,
			},
			getClientByID: getClientByIDOp{
				input: 1,
				output: &model.Client{
					Id:              1,
					ProjectId:       1,
					ProjectClientId: "asd",
				},
				outputErr: nil,
			},
			checkProject: checkProjectOp{
				isCalled: true,
				input: &grpc.CheckMerchantProjectRequestV1{
					MerchantId: testsdk.Ptr(uint64(1)),
					ProjectId:  testsdk.Ptr(uint64(1)),
				},
				outputErr: errors.New("some error"),
			},
			wantErr: errors.New("some error"),
			want:    nil,
		},
		{
			name:        "get_client_cards_error",
			reqClientID: 1,
			reqMerchID:  1,
			reqPagination: &middlewares.PaginationInfo{
				Page:       1,
				PerPage:    1,
				Pagination: true,
			},
			getClientByID: getClientByIDOp{
				input: 1,
				output: &model.Client{
					Id:              1,
					ProjectId:       1,
					ProjectClientId: "asd",
				},
				outputErr: nil,
			},
			checkProject: checkProjectOp{
				isCalled: true,
				input: &grpc.CheckMerchantProjectRequestV1{
					MerchantId: testsdk.Ptr(uint64(1)),
					ProjectId:  testsdk.Ptr(uint64(1)),
				},
				outputErr: nil,
			},
			getCardByClient: getCardByClientOp{
				isCalled: true,
				input:    1,
				inputPagination: &middlewares.PaginationInfo{
					Page:       1,
					PerPage:    1,
					Pagination: true,
				},
				output:    nil,
				outputErr: errors.New("some error"),
			},
			wantErr: errors.New("some error"),
			want:    nil,
		},
		{
			name:        "get_mask_format_error",
			reqClientID: 1,
			reqMerchID:  1,
			reqPagination: &middlewares.PaginationInfo{
				Page:       1,
				PerPage:    1,
				Pagination: true,
			},
			getClientByID: getClientByIDOp{
				input: 1,
				output: &model.Client{
					Id:              1,
					ProjectId:       1,
					ProjectClientId: "asd",
				},
				outputErr: nil,
			},
			checkProject: checkProjectOp{
				isCalled: true,
				input: &grpc.CheckMerchantProjectRequestV1{
					MerchantId: testsdk.Ptr(uint64(1)),
					ProjectId:  testsdk.Ptr(uint64(1)),
				},
				outputErr: nil,
			},
			getCardByClient: getCardByClientOp{
				isCalled: true,
				input:    1,
				inputPagination: &middlewares.PaginationInfo{
					Page:       1,
					PerPage:    1,
					Pagination: true,
				},
				output: []model.Card{
					{
						Id:          1,
						MaskedPan:   "some pan",
						Pan:         "some pan",
						EncryptName: "some name",
						HashedPan:   "$2a$10$4q4g2vSqVd/9Q3ZpIlngheW01oTebFnMgqU.NydDm.OoGgjRFQKT.",
						KeyId:       1,
					},
				},
				outputErr: nil,
			},
			getProjectMaskByProject: getProjectMaskByProjectIDOp{
				isCalled:  true,
				input:     1,
				output:    nil,
				outputErr: errors.New("some error"),
			},
			wantErr: errors.New("some error"),
			want:    nil,
		},
		{
			name:        "get_key_by_id_error",
			reqClientID: 1,
			reqMerchID:  1,
			reqPagination: &middlewares.PaginationInfo{
				Page:       1,
				PerPage:    1,
				Pagination: true,
			},
			getClientByID: getClientByIDOp{
				input: 1,
				output: &model.Client{
					Id:              1,
					ProjectId:       1,
					ProjectClientId: "asd",
				},
				outputErr: nil,
			},
			checkProject: checkProjectOp{
				isCalled: true,
				input: &grpc.CheckMerchantProjectRequestV1{
					MerchantId: testsdk.Ptr(uint64(1)),
					ProjectId:  testsdk.Ptr(uint64(1)),
				},
				outputErr: nil,
			},
			getCardByClient: getCardByClientOp{
				isCalled: true,
				input:    1,
				inputPagination: &middlewares.PaginationInfo{
					Page:       1,
					PerPage:    1,
					Pagination: true,
				},
				output: []model.Card{
					{
						Id:          1,
						MaskedPan:   "some pan",
						Pan:         "some pan",
						EncryptName: "some name",
						HashedPan:   "$2a$10$4q4g2vSqVd/9Q3ZpIlngheW01oTebFnMgqU.NydDm.OoGgjRFQKT.",
						KeyId:       1,
					},
				},
				outputErr: nil,
			},
			getProjectMaskByProject: getProjectMaskByProjectIDOp{
				isCalled: true,
				input:    1,
				output: &model.ProjectMaskFormat{
					Id:              1,
					ProjectId:       1,
					PlaceholderSign: "*",
					WithSeparator:   &defaultWithSep,
				},
				outputErr: nil,
			},
			getKeyByID: getKeyByIDOp{
				isCalled:  true,
				input:     1,
				output:    model.Key{},
				outputErr: errors.New("some error"),
			},
			wantErr: errors.New("some error"),
			want:    nil,
		},
		{
			name:        "pan_encryption_error",
			reqClientID: 1,
			reqMerchID:  1,
			reqPagination: &middlewares.PaginationInfo{
				Page:       1,
				PerPage:    1,
				Pagination: true,
			},
			getClientByID: getClientByIDOp{
				input: 1,
				output: &model.Client{
					Id:              1,
					ProjectId:       1,
					ProjectClientId: "asd",
				},
				outputErr: nil,
			},
			checkProject: checkProjectOp{
				isCalled: true,
				input: &grpc.CheckMerchantProjectRequestV1{
					MerchantId: testsdk.Ptr(uint64(1)),
					ProjectId:  testsdk.Ptr(uint64(1)),
				},
				outputErr: nil,
			},
			getCardByClient: getCardByClientOp{
				isCalled: true,
				input:    1,
				inputPagination: &middlewares.PaginationInfo{
					Page:       1,
					PerPage:    1,
					Pagination: true,
				},
				output: []model.Card{
					{
						Id:          1,
						MaskedPan:   "some pan",
						Pan:         "some pan",
						EncryptName: "some name",
						HashedPan:   "$2a$10$4q4g2vSqVd/9Q3ZpIlngheW01oTebFnMgqU.NydDm.OoGgjRFQKT.",
						EncryptPan:  "AAAAAAAAAAAAAAAAAAAAAECsfK75SmtqDbta4UnLlH4=",
						KeyId:       1,
					},
				},
				outputErr: nil,
			},
			getProjectMaskByProject: getProjectMaskByProjectIDOp{
				isCalled: true,
				input:    1,
				output: &model.ProjectMaskFormat{
					Id:              1,
					ProjectId:       1,
					PlaceholderSign: "*",
					WithSeparator:   &defaultWithSep,
				},
				outputErr: nil,
			},
			getKeyByID: getKeyByIDOp{
				isCalled: true,
				input:    1,
				output: model.Key{
					Id:  1,
					Key: badKey,
				},
				outputErr: nil,
			},
			appConfig: map[string]any{
				"SYMMETRIC_KEY": badKey,
			},
			wantErr: aes.KeySizeError(3),
			want:    nil,
		},
		{
			name:        "bad_pan",
			reqClientID: 1,
			reqMerchID:  1,
			reqPagination: &middlewares.PaginationInfo{
				Page:       1,
				PerPage:    1,
				Pagination: true,
			},
			getClientByID: getClientByIDOp{
				input: 1,
				output: &model.Client{
					Id:              1,
					ProjectId:       1,
					ProjectClientId: "asd",
				},
				outputErr: nil,
			},
			checkProject: checkProjectOp{
				isCalled: true,
				input: &grpc.CheckMerchantProjectRequestV1{
					MerchantId: testsdk.Ptr(uint64(1)),
					ProjectId:  testsdk.Ptr(uint64(1)),
				},
				outputErr: nil,
			},
			getCardByClient: getCardByClientOp{
				isCalled: true,
				input:    1,
				inputPagination: &middlewares.PaginationInfo{
					Page:       1,
					PerPage:    1,
					Pagination: true,
				},
				output: []model.Card{
					{
						Id:          1,
						MaskedPan:   "some pan",
						Pan:         "some pan",
						EncryptName: "some name",
						HashedPan:   "$2a$10$4q4g2vSqVd/9Q3ZpIlngheW01oTebFnMgqU.NydDm.OoGgjRFQKT.",
						EncryptPan:  "AAAAAAAAAAAAAAAAAAAAAECsfK75SmtqDbta4UnLlH4=",
						KeyId:       1,
					},
				},
				outputErr: nil,
			},
			getProjectMaskByProject: getProjectMaskByProjectIDOp{
				isCalled: true,
				input:    1,
				output: &model.ProjectMaskFormat{
					Id:              1,
					ProjectId:       1,
					PlaceholderSign: "*",
					WithSeparator:   &defaultWithSep,
				},
				outputErr: nil,
			},
			getKeyByID: getKeyByIDOp{
				isCalled: true,
				input:    1,
				output: model.Key{
					Id:  1,
					Key: normalKey,
				},
				outputErr: nil,
			},
			appConfig: map[string]any{
				"SYMMETRIC_KEY": badKey,
			},
			wantErr: goerr.ErrPanLength,
			want:    nil,
		},
		{
			name:        "success",
			reqClientID: 1,
			reqMerchID:  1,
			reqPagination: &middlewares.PaginationInfo{
				Page:       1,
				PerPage:    1,
				Pagination: true,
			},
			getClientByID: getClientByIDOp{
				input: 1,
				output: &model.Client{
					Id:              1,
					ProjectId:       1,
					ProjectClientId: "asd",
				},
				outputErr: nil,
			},
			checkProject: checkProjectOp{
				isCalled: true,
				input: &grpc.CheckMerchantProjectRequestV1{
					MerchantId: testsdk.Ptr(uint64(1)),
					ProjectId:  testsdk.Ptr(uint64(1)),
				},
				outputErr: nil,
			},
			getCardByClient: getCardByClientOp{
				isCalled: true,
				input:    1,
				inputPagination: &middlewares.PaginationInfo{
					Page:       1,
					PerPage:    1,
					Pagination: true,
				},
				output: []model.Card{
					{
						Id:          1,
						MaskedPan:   "some pan",
						Pan:         "some pan",
						EncryptName: "some name",
						HashedPan:   "$2a$10$4q4g2vSqVd/9Q3ZpIlngheW01oTebFnMgqU.NydDm.OoGgjRFQKT.",
						EncryptPan:  "AAAAAAAAAAAAAAAAAAAAACXJ7eT6CzNrPzsbyIRluEFgK65FdiS3n0szvcsdV2Tb",
						KeyId:       1,
					},
				},
				outputErr: nil,
			},
			getProjectMaskByProject: getProjectMaskByProjectIDOp{
				isCalled: true,
				input:    1,
				output: &model.ProjectMaskFormat{
					Id:              1,
					ProjectId:       1,
					PlaceholderSign: "*",
					WithSeparator:   &defaultWithSep,
				},
				outputErr: nil,
			},
			getKeyByID: getKeyByIDOp{
				isCalled: true,
				input:    1,
				output: model.Key{
					Id:  1,
					Key: normalKey,
				},
				outputErr: nil,
			},
			appConfig: map[string]any{
				"SYMMETRIC_KEY": badKey,
			},
			wantErr: nil,
			want: []schema.MaskedPanInfo{
				{
					ID:        1,
					MaskedPan: "4342-07******-3288",
				},
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctrl := gomock.NewController(t)

			ctx, cancel := context.WithCancel(context.Background())
			e2e.InitAppE2E(t, ctx, cancel, nil, nil, tt.appConfig)

			clientsDBMock := databasemocks.NewMockClientor(ctrl)
			merchantCliMock := grpcmock.NewMockMerchantClient(ctrl)
			cardGetterMock := databasemocks.NewMockCardGetter(ctrl)
			projectMaskMock := databasemocks.NewMockProjectMaskFormatter(ctrl)
			keyDBMock := databasemocks.NewMockKeyer(ctrl)

			clientsDBMock.EXPECT().GetByID(
				gomock.Any(),
				tt.getClientByID.input,
			).Return(
				tt.getClientByID.output,
				tt.getClientByID.outputErr,
			)

			if tt.checkProject.isCalled {
				merchantCliMock.EXPECT().CheckProject(
					gomock.Any(),
					tt.checkProject.input,
				).Return(
					nil,
					tt.checkProject.outputErr,
				)
			}

			if tt.getCardByClient.isCalled {
				cardGetterMock.EXPECT().GetByClient(
					gomock.Any(),
					tt.getCardByClient.input,
					tt.getCardByClient.inputPagination,
				).Return(
					tt.getCardByClient.output,
					tt.getCardByClient.outputErr,
				)
			}

			if tt.getProjectMaskByProject.isCalled {
				projectMaskMock.EXPECT().GetByProjectID(
					gomock.Any(),
					tt.getProjectMaskByProject.input,
				).Return(
					tt.getProjectMaskByProject.output,
					tt.getProjectMaskByProject.outputErr,
				)
			}

			if tt.getKeyByID.isCalled {
				keyDBMock.EXPECT().GetById(
					gomock.Any(),
					tt.getKeyByID.input,
				).Return(
					tt.getKeyByID.output,
					tt.getKeyByID.outputErr,
				)
			}

			s := ClientService{
				clientsRepo:           clientsDBMock,
				cardGetterRepo:        cardGetterMock,
				keyRepo:               keyDBMock,
				projectMaskFormatRepo: projectMaskMock,
				merchantClient:        merchantCliMock,
			}

			res, err := s.GetCardPans(context.Background(), tt.reqClientID, tt.reqMerchID, tt.reqPagination)
			if tt.wantErr != nil {
				require.Error(t, err)
				require.ErrorContains(t, err, tt.wantErr.Error())
			} else {
				require.NoError(t, err)
			}

			require.Equal(t, tt.want, res)
		})
	}
}

func TestGetClients(t *testing.T) {
	type checkProjectOp struct {
		input     *grpc.CheckMerchantProjectRequestV1
		outputErr error
	}

	type getByFiltersOp struct {
		isCalled        bool
		input           schema.ClientRequest
		inputPagination *middlewares.PaginationInfo
		outputErr       error
		output          []model.Client
	}

	tests := []struct {
		name          string
		reqMerchID    uint64
		reqPagination *middlewares.PaginationInfo
		req           schema.ClientRequest
		want          []schema.ClientResponse
		wantErr       error
		checkProject  checkProjectOp
		getByFilters  getByFiltersOp
	}{
		{
			name: "error_checking_project_id",
			reqPagination: &middlewares.PaginationInfo{
				PerPage:    1,
				Pagination: true,
				Page:       1,
			},
			req: schema.ClientRequest{
				ProjectID: 1,
			},
			reqMerchID: 1,
			want:       nil,
			wantErr:    errors.New("some error"),
			checkProject: checkProjectOp{
				input: &grpc.CheckMerchantProjectRequestV1{
					MerchantId: testsdk.Ptr(uint64(1)),
					ProjectId:  testsdk.Ptr(uint64(1)),
				},
				outputErr: errors.New("some error"),
			},
		},
		{
			name: "get_by_filters_error",
			reqPagination: &middlewares.PaginationInfo{
				PerPage:    1,
				Pagination: true,
				Page:       1,
			},
			req: schema.ClientRequest{
				ProjectID: 1,
			},
			reqMerchID: 1,
			want:       nil,
			wantErr:    errors.New("some error"),
			checkProject: checkProjectOp{
				input: &grpc.CheckMerchantProjectRequestV1{
					MerchantId: testsdk.Ptr(uint64(1)),
					ProjectId:  testsdk.Ptr(uint64(1)),
				},
				outputErr: nil,
			},
			getByFilters: getByFiltersOp{
				isCalled: true,
				input: schema.ClientRequest{
					ProjectID: 1,
				},
				inputPagination: &middlewares.PaginationInfo{
					PerPage:    1,
					Pagination: true,
					Page:       1,
				},
				output:    nil,
				outputErr: errors.New("some error"),
			},
		},
		{
			name: "success",
			reqPagination: &middlewares.PaginationInfo{
				PerPage:    1,
				Pagination: true,
				Page:       1,
			},
			req: schema.ClientRequest{
				ProjectID: 1,
			},
			reqMerchID: 1,
			want: []schema.ClientResponse{
				{
					ID:                 1,
					ProjectID:          1,
					ProjectClientID:    "asd",
					VerificationUserID: 1,
					CardNumber:         0,
				},
			},
			wantErr: nil,
			checkProject: checkProjectOp{
				input: &grpc.CheckMerchantProjectRequestV1{
					MerchantId: testsdk.Ptr(uint64(1)),
					ProjectId:  testsdk.Ptr(uint64(1)),
				},
				outputErr: nil,
			},
			getByFilters: getByFiltersOp{
				isCalled: true,
				input: schema.ClientRequest{
					ProjectID: 1,
				},
				inputPagination: &middlewares.PaginationInfo{
					PerPage:    1,
					Pagination: true,
					Page:       1,
				},
				output: []model.Client{
					{
						Id:                 1,
						ProjectId:          1,
						ProjectClientId:    "asd",
						VerificationUserID: 1,
					},
				},
				outputErr: nil,
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			isApproved := true

			ctrl := gomock.NewController(t)

			merchantCliMock := grpcmock.NewMockMerchantClient(ctrl)
			clientDBMock := databasemocks.NewMockClientor(ctrl)

			s := ClientService{
				clientsRepo:    clientDBMock,
				merchantClient: merchantCliMock,
			}

			merchantCliMock.EXPECT().CheckProject(
				gomock.Any(),
				tt.checkProject.input,
			).Return(
				nil,
				tt.checkProject.outputErr,
			)

			if tt.getByFilters.isCalled {
				clientDBMock.EXPECT().GetByFilter(
					gomock.Any(),
					tt.getByFilters.input,
					tt.getByFilters.inputPagination,
					&isApproved,
				).Return(
					tt.getByFilters.output,
					tt.getByFilters.outputErr,
				)
			}

			res, err := s.GetClients(context.Background(), tt.reqMerchID, tt.reqPagination, tt.req)
			if tt.wantErr != nil {
				require.Error(t, err)
				require.ErrorContains(t, err, tt.wantErr.Error())
			} else {
				require.NoError(t, err)
			}

			require.Equal(t, tt.want, res)
		})
	}
}

func TestGetClientByProjectClientInfo(t *testing.T) {
	type getByProjectOp struct {
		inputProjectID       uint64
		inputProjectClientID string
		output               *model.Client
		outputErr            error
	}

	tests := []struct {
		name         string
		req          []schema.ClientRequest
		want         []model.Client
		wantErr      error
		getByProject getByProjectOp
	}{
		{
			name: "error",
			req: []schema.ClientRequest{
				{
					ProjectID:       1,
					ProjectClientID: "asd",
				},
			},
			want:    []model.Client{},
			wantErr: nil,
			getByProject: getByProjectOp{
				inputProjectID:       1,
				inputProjectClientID: "asd",
				output:               nil,
				outputErr:            errors.New("some error"),
			},
		},
		{
			name: "success",
			req: []schema.ClientRequest{
				{
					ProjectID:       1,
					ProjectClientID: "asd",
				},
			},
			want: []model.Client{
				{
					Id:              1,
					ProjectId:       1,
					ProjectClientId: "asd",
				},
			},
			wantErr: nil,
			getByProject: getByProjectOp{
				inputProjectID:       1,
				inputProjectClientID: "asd",
				output: &model.Client{
					Id:              1,
					ProjectId:       1,
					ProjectClientId: "asd",
				},
				outputErr: nil,
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctrl := gomock.NewController(t)

			clientDBMock := databasemocks.NewMockClientor(ctrl)

			s := ClientService{
				clientsRepo: clientDBMock,
			}

			clientDBMock.EXPECT().GetByProject(
				gomock.Any(),
				tt.getByProject.inputProjectID,
				tt.getByProject.inputProjectClientID,
			).Return(
				tt.getByProject.output,
				tt.getByProject.outputErr,
			)

			res, err := s.GetByProjectClientInfo(context.Background(), tt.req)
			if tt.wantErr != nil {
				require.Error(t, err)
				require.ErrorContains(t, err, tt.wantErr.Error())
			} else {
				require.NoError(t, err)
			}

			require.Equal(t, tt.want, res)
		})
	}
}

func TestGetClientsByFilters(t *testing.T) {
	type getByFiltersOp struct {
		input     schema.ClientRequest
		output    []model.Client
		outputErr error
	}

	tests := []struct {
		name         string
		req          schema.ClientRequest
		want         []model.Client
		wantErr      error
		getByFilters getByFiltersOp
	}{
		{
			name: "error",
			req: schema.ClientRequest{
				ProjectID:       1,
				ProjectClientID: "asd",
			},
			want:    nil,
			wantErr: errors.New("some error"),
			getByFilters: getByFiltersOp{
				input: schema.ClientRequest{
					ProjectID:       1,
					ProjectClientID: "asd",
				},
				output:    nil,
				outputErr: errors.New("some error"),
			},
		},
		{
			name: "success",
			req: schema.ClientRequest{
				ProjectID:       1,
				ProjectClientID: "asd",
			},
			want: []model.Client{
				{
					Id:              1,
					ProjectId:       1,
					ProjectClientId: "asd",
				},
			},
			wantErr: nil,
			getByFilters: getByFiltersOp{
				input: schema.ClientRequest{
					ProjectID:       1,
					ProjectClientID: "asd",
				},
				output: []model.Client{
					{
						Id:              1,
						ProjectId:       1,
						ProjectClientId: "asd",
					},
				},
				outputErr: nil,
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctrl := gomock.NewController(t)

			clientDBMock := databasemocks.NewMockClientor(ctrl)

			s := ClientService{
				clientsRepo: clientDBMock,
			}

			clientDBMock.EXPECT().GetByFilter(
				gomock.Any(),
				tt.getByFilters.input,
				nil, nil,
			).Return(
				tt.getByFilters.output,
				tt.getByFilters.outputErr,
			)

			res, err := s.GetClientsByFilter(context.Background(), tt.req)
			if tt.wantErr != nil {
				require.Error(t, err)
				require.ErrorContains(t, err, tt.wantErr.Error())
			} else {
				require.NoError(t, err)
			}

			require.Equal(t, tt.want, res)
		})
	}
}

func TestCreateClient(t *testing.T) {
	type decryptOp struct {
		input     encryptor.EncryptedCard
		output    encryptor.DecryptedCard
		outputErr error
	}

	type getClientByProjectOp struct {
		inputProjectID       uint64
		inputProjectClientID string
		output               *model.Client
		outputErr            error
	}

	type createClientOp struct {
		isCalled  bool
		input     *model.Client
		outputErr error
	}

	type getCardsByClientOp struct {
		isCalled  bool
		input     uint64
		output    model.Cards
		outputErr error
	}

	type updateSaveAccessOp struct {
		isCalled        bool
		inputID         uint64
		inputSaveAccess bool
		outputErr       error
	}

	type getCardValidityByProjectOp struct {
		isCalled  bool
		input     uint64
		output    *model.CardValidity
		outputErr error
	}

	type getCardInfoByPanOp struct {
		isCalled  bool
		input     *grpc.GetCardInfoByPanRequestV1
		outputErr error
		output    *grpc.GetCardInfoByPanResponseV1
	}

	type getActualKeyOp struct {
		isCalled  bool
		output    model.Key
		outputErr error
	}

	type createCardOp struct {
		isCalled  bool
		input     *model.Card
		outputErr error
	}

	type getByProjectIdOp struct {
		isCalled  bool
		projectId uint64
		output    *model.ProjectMaskFormat
		outputErr error
	}

	type getActualHashKeyOp struct {
		isCalled  bool
		output    model.HashKey
		outputErr error
	}

	tests := []struct {
		name                     string
		req                      *schema.CreateClientRequest
		want                     *model.Card
		wantErr                  error
		decrypt                  decryptOp
		getClientByProject       getClientByProjectOp
		createClient             createClientOp
		getCardsByClient         getCardsByClientOp
		updateSaveAccess         updateSaveAccessOp
		getCardValidityByProject getCardValidityByProjectOp
		getCardInfoByPan         getCardInfoByPanOp
		getActualKey             getActualKeyOp
		getActualHashKey         getActualHashKeyOp
		createCard               createCardOp
		getByProjectId           getByProjectIdOp
		appConfig                map[string]any
	}{
		{
			name: "error_getting_client_by_project",
			req: &schema.CreateClientRequest{
				ProjectId:       1,
				ProjectClientId: "asd",
			},
			want:    nil,
			wantErr: errors.New("some error"),
			decrypt: decryptOp{
				input:     encryptor.EncryptedCard{},
				output:    encryptor.DecryptedCard{},
				outputErr: nil,
			},
			getClientByProject: getClientByProjectOp{
				inputProjectID:       1,
				inputProjectClientID: "asd",
				output:               nil,
				outputErr:            errors.New("some error"),
			},
		},
		{
			name: "client_id_is_zero_creation_error",
			req: &schema.CreateClientRequest{
				ProjectId:       1,
				ProjectClientId: "asd",
			},
			want:    nil,
			wantErr: errors.New("some error"),
			decrypt: decryptOp{
				input:     encryptor.EncryptedCard{},
				output:    encryptor.DecryptedCard{},
				outputErr: nil,
			},
			getClientByProject: getClientByProjectOp{
				inputProjectID:       1,
				inputProjectClientID: "asd",
				output: &model.Client{
					Id: 0,
				},
				outputErr: nil,
			},
			createClient: createClientOp{
				isCalled: true,
				input: &model.Client{
					ProjectId:       1,
					ProjectClientId: "asd",
				},
				outputErr: errors.New("some error"),
			},
		},
		{
			name: "get_by_client_error",
			req: &schema.CreateClientRequest{
				ProjectId:       1,
				ProjectClientId: "asd",
			},
			want:    nil,
			wantErr: errors.New("some error"),
			decrypt: decryptOp{
				input:     encryptor.EncryptedCard{},
				output:    encryptor.DecryptedCard{},
				outputErr: nil,
			},
			getClientByProject: getClientByProjectOp{
				inputProjectID:       1,
				inputProjectClientID: "asd",
				output: &model.Client{
					Id:                 1,
					ProjectId:          1,
					ProjectClientId:    "asd",
					VerificationUserID: 1,
					IsBlocked:          false,
				},
				outputErr: nil,
			},
			createClient: createClientOp{
				isCalled: false,
				input: &model.Client{
					ProjectId:       1,
					ProjectClientId: "asd",
				},
				outputErr: errors.New("some error"),
			},
			getCardsByClient: getCardsByClientOp{
				isCalled:  true,
				input:     1,
				output:    nil,
				outputErr: errors.New("some error"),
			},
		},
		{
			name: "update_save_access_error",
			req: &schema.CreateClientRequest{
				ProjectId:       1,
				ProjectClientId: "asd",
				SaveAccess:      true,
				EncryptedCard:   encryptor.EncryptedCard{Number: []byte("some pan")},
			},
			want:    nil,
			wantErr: errors.New("some error"),
			decrypt: decryptOp{
				input: encryptor.EncryptedCard{
					Number: []byte("some pan"),
				},
				output: encryptor.DecryptedCard{
					Number: "some pan",
				},
				outputErr: nil,
			},
			getClientByProject: getClientByProjectOp{
				inputProjectID:       1,
				inputProjectClientID: "asd",
				output: &model.Client{
					Id:                 1,
					ProjectId:          1,
					ProjectClientId:    "asd",
					VerificationUserID: 1,
					IsBlocked:          false,
				},
				outputErr: nil,
			},
			createClient: createClientOp{
				isCalled: false,
				input: &model.Client{
					ProjectId:       1,
					ProjectClientId: "asd",
				},
				outputErr: errors.New("some error"),
			},
			getCardsByClient: getCardsByClientOp{
				isCalled: true,
				input:    1,
				output: model.Cards{
					{
						Id:          1,
						MaskedPan:   "some pan",
						Pan:         "some pan",
						EncryptName: "some name",
						HashedPan:   "$2a$10$QucMOA64bB89p3AkT7yOFePx/FbtxOFPCE.G/hbKLYvxuUBche83y",
						EncryptPan:  "AAAAAAAAAAAAAAAAAAAAAECsfK75SmtqDbta4UnLlH4=",
						KeyId:       1,
						SaveAccess:  false,
					},
				},
				outputErr: nil,
			},
			updateSaveAccess: updateSaveAccessOp{
				isCalled:        true,
				inputID:         1,
				inputSaveAccess: true,
				outputErr:       errors.New("some error"),
			},
		},
		{
			name: "short_success",
			req: &schema.CreateClientRequest{
				ProjectId:       1,
				ProjectClientId: "asd",
				SaveAccess:      true,
				EncryptedCard:   encryptor.EncryptedCard{Number: []byte("some pan")},
			},
			want: &model.Card{
				Id:          1,
				MaskedPan:   "some pan",
				Pan:         "some pan",
				EncryptName: "some name",
				HashedPan:   "$2a$10$QucMOA64bB89p3AkT7yOFePx/FbtxOFPCE.G/hbKLYvxuUBche83y",
				EncryptPan:  "AAAAAAAAAAAAAAAAAAAAAECsfK75SmtqDbta4UnLlH4=",
				KeyId:       1,
				SaveAccess:  false,
			},
			wantErr: nil,
			decrypt: decryptOp{
				input: encryptor.EncryptedCard{
					Number: []byte("some pan"),
				},
				output: encryptor.DecryptedCard{
					Number: "some pan",
				},
				outputErr: nil,
			},
			getClientByProject: getClientByProjectOp{
				inputProjectID:       1,
				inputProjectClientID: "asd",
				output: &model.Client{
					Id:                 1,
					ProjectId:          1,
					ProjectClientId:    "asd",
					VerificationUserID: 1,
					IsBlocked:          false,
				},
				outputErr: nil,
			},
			createClient: createClientOp{
				isCalled: false,
				input: &model.Client{
					ProjectId:       1,
					ProjectClientId: "asd",
				},
				outputErr: errors.New("some error"),
			},
			getCardsByClient: getCardsByClientOp{
				isCalled: true,
				input:    1,
				output: model.Cards{
					{
						Id:          1,
						MaskedPan:   "some pan",
						Pan:         "some pan",
						EncryptName: "some name",
						HashedPan:   "$2a$10$QucMOA64bB89p3AkT7yOFePx/FbtxOFPCE.G/hbKLYvxuUBche83y",
						EncryptPan:  "AAAAAAAAAAAAAAAAAAAAAECsfK75SmtqDbta4UnLlH4=",
						KeyId:       1,
						SaveAccess:  false,
					},
				},
				outputErr: nil,
			},
			updateSaveAccess: updateSaveAccessOp{
				isCalled:        true,
				inputID:         1,
				inputSaveAccess: true,
				outputErr:       nil,
			},
		},
		{
			name: "get_card_validity_error",
			req: &schema.CreateClientRequest{
				ProjectId:       1,
				ProjectClientId: "asd",
				SaveAccess:      true,
				EncryptedCard:   encryptor.EncryptedCard{Number: []byte("some pan")},
			},
			want:    nil,
			wantErr: errors.New("some error"),
			decrypt: decryptOp{
				input: encryptor.EncryptedCard{
					Number: []byte("some pan"),
				},
				output: encryptor.DecryptedCard{
					Number: "some pan",
				},
				outputErr: nil,
			},
			getClientByProject: getClientByProjectOp{
				inputProjectID:       1,
				inputProjectClientID: "asd",
				output: &model.Client{
					Id:                 1,
					ProjectId:          1,
					ProjectClientId:    "asd",
					VerificationUserID: 1,
					IsBlocked:          false,
				},
				outputErr: nil,
			},
			createClient: createClientOp{
				isCalled: false,
				input: &model.Client{
					ProjectId:       1,
					ProjectClientId: "asd",
				},
				outputErr: errors.New("some error"),
			},
			getCardsByClient: getCardsByClientOp{
				isCalled: true,
				input:    1,
				output: model.Cards{
					{
						Id:          1,
						MaskedPan:   "some pan",
						Pan:         "some pan",
						EncryptName: "some name",
						KeyId:       1,
						SaveAccess:  false,
					},
				},
				outputErr: nil,
			},
			updateSaveAccess: updateSaveAccessOp{
				isCalled:        false,
				inputID:         1,
				inputSaveAccess: true,
				outputErr:       nil,
			},
			getCardValidityByProject: getCardValidityByProjectOp{
				isCalled:  true,
				input:     1,
				output:    nil,
				outputErr: errors.New("some error"),
			},
		},
		{
			name: "get_card_info_by_pan",
			req: &schema.CreateClientRequest{
				ProjectId:       1,
				ProjectClientId: "asd",
				EncryptedCard:   encryptor.EncryptedCard{Number: []byte("some pan")},
				SaveAccess:      true,
			},
			want:    nil,
			wantErr: errors.New("some error"),
			decrypt: decryptOp{
				input: encryptor.EncryptedCard{
					Number: []byte("some pan"),
				},
				output: encryptor.DecryptedCard{
					Number: "some pan",
				},
				outputErr: nil,
			},
			getClientByProject: getClientByProjectOp{
				inputProjectID:       1,
				inputProjectClientID: "asd",
				output: &model.Client{
					Id:                 1,
					ProjectId:          1,
					ProjectClientId:    "asd",
					VerificationUserID: 1,
					IsBlocked:          false,
				},
				outputErr: nil,
			},
			createClient: createClientOp{
				isCalled: false,
				input: &model.Client{
					ProjectId:       1,
					ProjectClientId: "asd",
				},
				outputErr: errors.New("some error"),
			},
			getCardsByClient: getCardsByClientOp{
				isCalled: true,
				input:    1,
				output: model.Cards{
					{
						Id:          1,
						MaskedPan:   "some pan",
						Pan:         "some pan",
						EncryptName: "some name",
						KeyId:       1,
						SaveAccess:  false,
					},
				},
				outputErr: nil,
			},
			updateSaveAccess: updateSaveAccessOp{
				isCalled:        false,
				inputID:         1,
				inputSaveAccess: true,
				outputErr:       nil,
			},
			getCardValidityByProject: getCardValidityByProjectOp{
				isCalled:  true,
				input:     1,
				output:    nil,
				outputErr: nil,
			},
			getCardInfoByPan: getCardInfoByPanOp{
				isCalled: true,
				input: &grpc.GetCardInfoByPanRequestV1{
					EightDigitBin: testsdk.Ptr("some pan"),
					SixDigitBin:   testsdk.Ptr("some p"),
					FiveDigitBin:  testsdk.Ptr("some "),
				},
				outputErr: errors.New("some error"),
				output:    nil,
			},
		},
		{
			name: "get_actual_key_error",
			req: &schema.CreateClientRequest{
				ProjectId:       1,
				ProjectClientId: "asd",
				SaveAccess:      true,
				EncryptedCard:   encryptor.EncryptedCard{Number: []byte("some pan")},
			},
			want:    nil,
			wantErr: errors.New("some error"),
			decrypt: decryptOp{
				input: encryptor.EncryptedCard{
					Number: []byte("some pan"),
				},
				output: encryptor.DecryptedCard{
					Number: "some pan",
				},
				outputErr: nil,
			},
			getClientByProject: getClientByProjectOp{
				inputProjectID:       1,
				inputProjectClientID: "asd",
				output: &model.Client{
					Id:                 1,
					ProjectId:          1,
					ProjectClientId:    "asd",
					VerificationUserID: 1,
					IsBlocked:          false,
				},
				outputErr: nil,
			},
			createClient: createClientOp{
				isCalled: false,
				input: &model.Client{
					ProjectId:       1,
					ProjectClientId: "asd",
				},
				outputErr: errors.New("some error"),
			},
			getCardsByClient: getCardsByClientOp{
				isCalled: true,
				input:    1,
				output: model.Cards{
					{
						Id:          1,
						MaskedPan:   "some pan",
						Pan:         "some pan",
						EncryptName: "some name",
						KeyId:       1,
						SaveAccess:  false,
					},
				},
				outputErr: nil,
			},
			updateSaveAccess: updateSaveAccessOp{
				isCalled:        false,
				inputID:         1,
				inputSaveAccess: true,
				outputErr:       nil,
			},
			getCardValidityByProject: getCardValidityByProjectOp{
				isCalled:  true,
				input:     1,
				output:    nil,
				outputErr: nil,
			},
			getCardInfoByPan: getCardInfoByPanOp{
				isCalled: true,
				input: &grpc.GetCardInfoByPanRequestV1{
					EightDigitBin: testsdk.Ptr("some pan"),
					SixDigitBin:   testsdk.Ptr("some p"),
					FiveDigitBin:  testsdk.Ptr("some "),
				},
				outputErr: nil,
				output: &grpc.GetCardInfoByPanResponseV1{
					IpsId:     testsdk.Ptr(uint64(1)),
					IssuerId:  testsdk.Ptr(uint64(1)),
					CountryId: testsdk.Ptr(uint64(1)),
				},
			},
			getActualKey: getActualKeyOp{
				isCalled:  true,
				output:    model.Key{},
				outputErr: errors.New("some error"),
			},
		},
		{
			name: "error_decrypting_key",
			req: &schema.CreateClientRequest{
				ProjectId:       1,
				ProjectClientId: "asd",
				SaveAccess:      true,
				EncryptedCard:   encryptor.EncryptedCard{Number: []byte("some pan")},
			},
			want:    nil,
			wantErr: aes.KeySizeError(3),
			decrypt: decryptOp{
				input: encryptor.EncryptedCard{
					Number: []byte("some pan"),
				},
				output: encryptor.DecryptedCard{
					Number: "some pan",
				},
				outputErr: nil,
			},
			getClientByProject: getClientByProjectOp{
				inputProjectID:       1,
				inputProjectClientID: "asd",
				output: &model.Client{
					Id:                 1,
					ProjectId:          1,
					ProjectClientId:    "asd",
					VerificationUserID: 1,
					IsBlocked:          false,
				},
				outputErr: nil,
			},
			createClient: createClientOp{
				isCalled: false,
				input: &model.Client{
					ProjectId:       1,
					ProjectClientId: "asd",
				},
				outputErr: errors.New("some error"),
			},
			getCardsByClient: getCardsByClientOp{
				isCalled: true,
				input:    1,
				output: model.Cards{
					{
						Id:          1,
						MaskedPan:   "some pan",
						Pan:         "some pan",
						EncryptName: "some name",
						KeyId:       1,
						SaveAccess:  false,
					},
				},
				outputErr: nil,
			},
			updateSaveAccess: updateSaveAccessOp{
				isCalled:        false,
				inputID:         1,
				inputSaveAccess: true,
				outputErr:       nil,
			},
			getCardValidityByProject: getCardValidityByProjectOp{
				isCalled:  true,
				input:     1,
				output:    nil,
				outputErr: nil,
			},
			getCardInfoByPan: getCardInfoByPanOp{
				isCalled: true,
				input: &grpc.GetCardInfoByPanRequestV1{
					EightDigitBin: testsdk.Ptr("some pan"),
					SixDigitBin:   testsdk.Ptr("some p"),
					FiveDigitBin:  testsdk.Ptr("some "),
				},
				outputErr: nil,
				output: &grpc.GetCardInfoByPanResponseV1{
					IpsId:     testsdk.Ptr(uint64(1)),
					IssuerId:  testsdk.Ptr(uint64(1)),
					CountryId: testsdk.Ptr(uint64(1)),
				},
			},
			getActualKey: getActualKeyOp{
				isCalled: true,
				output: model.Key{
					Id:  1,
					Key: "AAAAAAAAAAAAAAAAAAAAAD4r1NYivkHsOHdiq9dCiJiz0HJtt7wrVFA8fx/DenGL",
				},
				outputErr: nil,
			},
			appConfig: map[string]any{
				"SYMMETRIC_KEY": badKey,
			},
		},
		{
			name: "error_creating_card",
			req: &schema.CreateClientRequest{
				ProjectId:       1,
				ProjectClientId: "asd",
				SaveAccess:      true,
				EncryptedCard:   encryptor.EncryptedCard{Number: []byte("some pan")},
			},
			want:    nil,
			wantErr: errors.New("some error"),
			decrypt: decryptOp{
				input: encryptor.EncryptedCard{
					Number: []byte("some pan"),
				},
				output: encryptor.DecryptedCard{
					Number: "some pan",
				},
				outputErr: nil,
			},
			getClientByProject: getClientByProjectOp{
				inputProjectID:       1,
				inputProjectClientID: "asd",
				output: &model.Client{
					Id:                 1,
					ProjectId:          1,
					ProjectClientId:    "asd",
					VerificationUserID: 1,
					IsBlocked:          false,
				},
				outputErr: nil,
			},
			createClient: createClientOp{
				isCalled: false,
				input: &model.Client{
					ProjectId:       1,
					ProjectClientId: "asd",
				},
				outputErr: errors.New("some error"),
			},
			getCardsByClient: getCardsByClientOp{
				isCalled: true,
				input:    1,
				output: model.Cards{
					{
						Id:          1,
						MaskedPan:   "some pan",
						Pan:         "some pan",
						EncryptName: "some name",
						KeyId:       1,
						SaveAccess:  false,
					},
				},
				outputErr: nil,
			},
			updateSaveAccess: updateSaveAccessOp{
				isCalled:        false,
				inputID:         1,
				inputSaveAccess: true,
				outputErr:       nil,
			},
			getCardValidityByProject: getCardValidityByProjectOp{
				isCalled:  true,
				input:     1,
				output:    nil,
				outputErr: nil,
			},
			getCardInfoByPan: getCardInfoByPanOp{
				isCalled: true,
				input: &grpc.GetCardInfoByPanRequestV1{
					EightDigitBin: testsdk.Ptr("some pan"),
					SixDigitBin:   testsdk.Ptr("some p"),
					FiveDigitBin:  testsdk.Ptr("some "),
				},
				outputErr: nil,
				output: &grpc.GetCardInfoByPanResponseV1{
					IpsId:     testsdk.Ptr(uint64(1)),
					IssuerId:  testsdk.Ptr(uint64(1)),
					CountryId: testsdk.Ptr(uint64(1)),
				},
			},
			getActualKey: getActualKeyOp{
				isCalled: true,
				output: model.Key{
					Id:  1,
					Key: "AAAAAAAAAAAAAAAAAAAAAD4r1NYivkHsOHdiq9dCiJiz0HJtt7wrVFA8fx/DenGL",
				},
				outputErr: nil,
			},
			getActualHashKey: getActualHashKeyOp{
				isCalled: true,
				output: model.HashKey{
					Id:  1,
					Key: encryptHashKey,
				},
				outputErr: nil,
			},
			createCard: createCardOp{
				isCalled: true,
				input: &model.Card{
					ClientId:      1,
					IssuerId:      1,
					IpsId:         1,
					CountryId:     1,
					SaveAccess:    true,
					KeyId:         1,
					HashedPan:     "$2a$10$ZXxp8Qq05NIqJvdv8NE5AunMEYxQgg.65D3vDh4OsT1dapmb3QN4y",
					EncryptMonth:  "AAAAAAAAAAAAAAAAAAAAAM45CA6vQU2zcQJ3wyF7cpc=",
					EncryptYear:   "AAAAAAAAAAAAAAAAAAAAAM45CA6vQU2zcQJ3wyF7cpc=",
					EncryptName:   "AAAAAAAAAAAAAAAAAAAAAM45CA6vQU2zcQJ3wyF7cpc=",
					EncryptPan:    "AAAAAAAAAAAAAAAAAAAAAECsfK75SmtqDbta4UnLlH4=",
					IsPanModified: true,
					HashKeyId:     1,
				},
				outputErr: errors.New("some error"),
			},
			appConfig: map[string]any{
				"SYMMETRIC_KEY": normalKey,
			},
		},
		//{
		//	name: "success",
		//	req: &schema.CreateClientRequest{
		//		ProjectId:       1,
		//		ProjectClientId: "asd",
		//		SaveAccess:      true,
		//		EncryptedCard:   encryptor.EncryptedCard{Number: []byte("some pan")},
		//	},
		//	want: &model.Card{
		//		ClientId:      1,
		//		IssuerId:      1,
		//		IpsId:         1,
		//		CountryId:     1,
		//		SaveAccess:    true,
		//		Approved:      false,
		//		KeyId:         1,
		//		EncryptMonth:  "AAAAAAAAAAAAAAAAAAAAAM45CA6vQU2zcQJ3wyF7cpc=",
		//		EncryptYear:   "AAAAAAAAAAAAAAAAAAAAAM45CA6vQU2zcQJ3wyF7cpc=",
		//		EncryptName:   "AAAAAAAAAAAAAAAAAAAAAM45CA6vQU2zcQJ3wyF7cpc=",
		//		EncryptPan:    "AAAAAAAAAAAAAAAAAAAAAECsfK75SmtqDbta4UnLlH4=",
		//		IsPanModified: true,
		//		MaskedPan:     "",
		//	},
		//	wantErr: nil,
		//	decrypt: decryptOp{
		//		input: encryptor.EncryptedCard{
		//			Number: []byte("some pan"),
		//		},
		//		output: encryptor.DecryptedCard{
		//			Number: "some pan",
		//		},
		//		outputErr: nil,
		//	},
		//	getClientByProject: getClientByProjectOp{
		//		inputProjectID:       1,
		//		inputProjectClientID: "asd",
		//		output: &model.Client{
		//			Id:                 1,
		//			ProjectId:          1,
		//			ProjectClientId:    "asd",
		//			VerificationUserID: 1,
		//			IsBlocked:          false,
		//		},
		//		outputErr: nil,
		//	},
		//	createClient: createClientOp{
		//		isCalled: false,
		//		input: &model.Client{
		//			ProjectId:       1,
		//			ProjectClientId: "asd",
		//		},
		//		outputErr: errors.New("some error"),
		//	},
		//	getCardsByClient: getCardsByClientOp{
		//		isCalled: true,
		//		input:    1,
		//		output: model.Cards{
		//			{
		//				Id:          1,
		//				MaskedPan:   "some pan",
		//				Pan:         "some pan",
		//				EncryptName: "some name",
		//				KeyId:       1,
		//				SaveAccess:  false,
		//			},
		//		},
		//		outputErr: nil,
		//	},
		//	updateSaveAccess: updateSaveAccessOp{
		//		isCalled:        false,
		//		inputID:         1,
		//		inputSaveAccess: true,
		//		outputErr:       nil,
		//	},
		//	getCardValidityByProject: getCardValidityByProjectOp{
		//		isCalled:  true,
		//		input:     1,
		//		output:    nil,
		//		outputErr: nil,
		//	},
		//	getCardInfoByPan: getCardInfoByPanOp{
		//		isCalled: true,
		//		input: &grpc.GetCardInfoByPanRequestV1{
		//			EightDigitBin: testsdk.Ptr("some pan"),
		//			SixDigitBin:   testsdk.Ptr("some p"),
		//			FiveDigitBin:  testsdk.Ptr("some "),
		//		},
		//		outputErr: nil,
		//		output: &grpc.GetCardInfoByPanResponseV1{
		//			IpsId:     testsdk.Ptr(uint64(1)),
		//			IssuerId:  testsdk.Ptr(uint64(1)),
		//			CountryId: testsdk.Ptr(uint64(1)),
		//		},
		//	},
		//	getActualKey: getActualKeyOp{
		//		isCalled: true,
		//		output: model.Key{
		//			Id:  1,
		//			Key: "AAAAAAAAAAAAAAAAAAAAAD4r1NYivkHsOHdiq9dCiJiz0HJtt7wrVFA8fx/DenGL",
		//		},
		//		outputErr: nil,
		//	},
		//	createCard: createCardOp{
		//		isCalled: true,
		//		input: &model.Card{
		//			ClientId:      1,
		//			IssuerId:      1,
		//			IpsId:         1,
		//			CountryId:     1,
		//			SaveAccess:    true,
		//			KeyId:         1,
		//			HashedPan:     "$2a$10$ZXxp8Qq05NIqJvdv8NE5AunMEYxQgg.65D3vDh4OsT1dapmb3QN4y",
		//			EncryptMonth:  "AAAAAAAAAAAAAAAAAAAAAM45CA6vQU2zcQJ3wyF7cpc=",
		//			EncryptYear:   "AAAAAAAAAAAAAAAAAAAAAM45CA6vQU2zcQJ3wyF7cpc=",
		//			EncryptName:   "AAAAAAAAAAAAAAAAAAAAAM45CA6vQU2zcQJ3wyF7cpc=",
		//			EncryptPan:    "AAAAAAAAAAAAAAAAAAAAAECsfK75SmtqDbta4UnLlH4=",
		//			IsPanModified: true,
		//		},
		//		outputErr: nil,
		//	},
		//	appConfig: map[string]any{
		//		"SYMMETRIC_KEY": normalKey,
		//	},
		//	getByProjectId: getByProjectIdOp{
		//		isCalled:  true,
		//		projectId: 1,
		//		output: &model.ProjectMaskFormat{
		//			Id:              1,
		//			ProjectId:       1,
		//			PlaceholderSign: "X",
		//		},
		//		outputErr: nil,
		//	},
		//},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctrl := gomock.NewController(t)

			ctx, cancel := context.WithCancel(context.Background())
			e2e.InitAppE2E(t, ctx, cancel, nil, nil, tt.appConfig)

			clientDBMock := databasemocks.NewMockClientor(ctrl)
			cardsDBMock := databasemocks.NewMockCarder(ctrl)
			cardUpdatorDBMock := databasemocks.NewMockCardUpdator(ctrl)
			cardValidityDBMock := databasemocks.NewMockCardValidator(ctrl)
			terminalCardMock := grpcmock.NewMockTerminalCardsClient(ctrl)
			keyDBMock := databasemocks.NewMockKeyer(ctrl)
			hashKeyDBMock := databasemocks.NewMockHashKeyer(ctrl)
			decryptorMock := encryptor.NewMockDecryptor(ctrl)
			projectMaskMock := databasemocks.NewMockProjectMaskFormatter(ctrl)

			decryptorMock.EXPECT().DecryptCard(tt.decrypt.input).
				Return(tt.decrypt.output, tt.decrypt.outputErr).AnyTimes()

			clientDBMock.EXPECT().GetByProject(
				gomock.Any(),
				tt.getClientByProject.inputProjectID,
				tt.getClientByProject.inputProjectClientID,
			).Return(
				tt.getClientByProject.output,
				tt.getClientByProject.outputErr,
			)

			if tt.createClient.isCalled {
				clientDBMock.EXPECT().Create(
					gomock.Any(),
					tt.createClient.input,
				).Return(
					tt.createClient.outputErr,
				)
			}

			if tt.getCardsByClient.isCalled {
				cardsDBMock.EXPECT().GetByClient(
					gomock.Any(),
					tt.getCardsByClient.input,
				).Return(
					tt.getCardsByClient.output,
					tt.getCardsByClient.outputErr,
				)
			}

			if tt.updateSaveAccess.isCalled {
				cardUpdatorDBMock.EXPECT().UpdateSaveAccess(
					gomock.Any(),
					tt.updateSaveAccess.inputID,
					tt.updateSaveAccess.inputSaveAccess,
				).Return(
					tt.updateSaveAccess.outputErr,
				)
			}

			if tt.getCardValidityByProject.isCalled {
				cardValidityDBMock.EXPECT().GetByProject(
					gomock.Any(),
					tt.getCardValidityByProject.input,
				).Return(
					tt.getCardValidityByProject.output,
					tt.getCardValidityByProject.outputErr,
				)
			}

			if tt.getCardInfoByPan.isCalled {
				terminalCardMock.EXPECT().GetCardInfoByPan(
					gomock.Any(),
					tt.getCardInfoByPan.input,
				).Return(
					tt.getCardInfoByPan.output,
					tt.getCardInfoByPan.outputErr,
				)
			}

			if tt.getActualKey.isCalled {
				keyDBMock.EXPECT().GetActualKey(
					gomock.Any(),
				).Return(
					tt.getActualKey.output,
					tt.getActualKey.outputErr,
				)
			}

			if tt.getActualHashKey.isCalled {
				hashKeyDBMock.EXPECT().GetActualKey(
					gomock.Any(),
				).Return(
					tt.getActualHashKey.output,
					tt.getActualHashKey.outputErr,
				)
			}

			if tt.createCard.isCalled {
				cardsDBMock.EXPECT().Create(
					gomock.Any(),
					cardMatcher{want: tt.createCard.input},
				).Return(
					tt.createCard.outputErr,
				)
			}

			if tt.getByProjectId.isCalled {
				projectMaskMock.EXPECT().GetByProjectID(
					gomock.Any(),
					tt.getByProjectId.projectId).Return(
					tt.getByProjectId.output,
					tt.getByProjectId.outputErr,
				)
			}

			s := ClientService{
				keyRepo:               keyDBMock,
				clientsRepo:           clientDBMock,
				cardsRepo:             cardsDBMock,
				cardUpdatorRepo:       cardUpdatorDBMock,
				cardValidityRepo:      cardValidityDBMock,
				terminalCards:         terminalCardMock,
				decryptor:             decryptorMock,
				projectMaskFormatRepo: projectMaskMock,
				hashKeyRepo:           hashKeyDBMock,
				hashKeySecret:         hashKeySecret,
			}

			res, err := s.CreateClient(context.Background(), tt.req)
			if tt.wantErr != nil {
				require.Error(t, err)
				require.ErrorContains(t, err, tt.wantErr.Error())
			} else {
				require.NoError(t, err)
			}

			require.True(t, isCardMatches(res, tt.want))
		})
	}
}
