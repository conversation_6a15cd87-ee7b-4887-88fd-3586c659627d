package domain

type GetClientCardsRequest struct {
	MerchantID      uint64 `json:"merchant_id" url:"merchant_id"`
	ProjectID       uint64 `json:"project_id" url:"project_id"`
	ProjectClientID string `json:"project_client_id" url:"project_client_id"`
}

type GetCardResponse struct {
	CardToken  string `json:"card_token"`
	MaskedPan  string `json:"masked_pan"`
	IpsName    string `json:"ips"`
	IssuerName string `json:"issuer"`
	Month      string `json:"month"`
	Year       string `json:"year"`
}
