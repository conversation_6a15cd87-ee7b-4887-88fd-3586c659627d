// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.5
// 	protoc        v5.29.3
// source: inner/processing/grpc/multiaccounting.proto

package grpc

import (
	_ "git.local/sensitive/mvp/pb"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
	structpb "google.golang.org/protobuf/types/known/structpb"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type AccountTerminal struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            *uint64                `protobuf:"varint,1,opt,name=id" json:"id,omitempty"`
	BankCode      *string                `protobuf:"bytes,2,opt,name=bank_code,json=bankCode" json:"bank_code,omitempty"`
	Config        *structpb.Struct       `protobuf:"bytes,3,opt,name=config" json:"config,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *AccountTerminal) Reset() {
	*x = AccountTerminal{}
	mi := &file_inner_processing_grpc_multiaccounting_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AccountTerminal) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AccountTerminal) ProtoMessage() {}

func (x *AccountTerminal) ProtoReflect() protoreflect.Message {
	mi := &file_inner_processing_grpc_multiaccounting_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AccountTerminal.ProtoReflect.Descriptor instead.
func (*AccountTerminal) Descriptor() ([]byte, []int) {
	return file_inner_processing_grpc_multiaccounting_proto_rawDescGZIP(), []int{0}
}

func (x *AccountTerminal) GetId() uint64 {
	if x != nil && x.Id != nil {
		return *x.Id
	}
	return 0
}

func (x *AccountTerminal) GetBankCode() string {
	if x != nil && x.BankCode != nil {
		return *x.BankCode
	}
	return ""
}

func (x *AccountTerminal) GetConfig() *structpb.Struct {
	if x != nil {
		return x.Config
	}
	return nil
}

// -- transit accounts
type ServiceParametersList struct {
	state             protoimpl.MessageState `protogen:"open.v1"`
	ServiceParamsList []*ServiceParameter    `protobuf:"bytes,1,rep,name=service_params_list,json=serviceParamsList" json:"service_params_list,omitempty"`
	unknownFields     protoimpl.UnknownFields
	sizeCache         protoimpl.SizeCache
}

func (x *ServiceParametersList) Reset() {
	*x = ServiceParametersList{}
	mi := &file_inner_processing_grpc_multiaccounting_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ServiceParametersList) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ServiceParametersList) ProtoMessage() {}

func (x *ServiceParametersList) ProtoReflect() protoreflect.Message {
	mi := &file_inner_processing_grpc_multiaccounting_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ServiceParametersList.ProtoReflect.Descriptor instead.
func (*ServiceParametersList) Descriptor() ([]byte, []int) {
	return file_inner_processing_grpc_multiaccounting_proto_rawDescGZIP(), []int{1}
}

func (x *ServiceParametersList) GetServiceParamsList() []*ServiceParameter {
	if x != nil {
		return x.ServiceParamsList
	}
	return nil
}

type GetAccountIdentifierResponse struct {
	state             protoimpl.MessageState `protogen:"open.v1"`
	AccountIdentifier *string                `protobuf:"bytes,1,opt,name=account_identifier,json=accountIdentifier" json:"account_identifier,omitempty"`
	unknownFields     protoimpl.UnknownFields
	sizeCache         protoimpl.SizeCache
}

func (x *GetAccountIdentifierResponse) Reset() {
	*x = GetAccountIdentifierResponse{}
	mi := &file_inner_processing_grpc_multiaccounting_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetAccountIdentifierResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAccountIdentifierResponse) ProtoMessage() {}

func (x *GetAccountIdentifierResponse) ProtoReflect() protoreflect.Message {
	mi := &file_inner_processing_grpc_multiaccounting_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAccountIdentifierResponse.ProtoReflect.Descriptor instead.
func (*GetAccountIdentifierResponse) Descriptor() ([]byte, []int) {
	return file_inner_processing_grpc_multiaccounting_proto_rawDescGZIP(), []int{2}
}

func (x *GetAccountIdentifierResponse) GetAccountIdentifier() string {
	if x != nil && x.AccountIdentifier != nil {
		return *x.AccountIdentifier
	}
	return ""
}

type ServiceParameter struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Code          *string                `protobuf:"bytes,1,opt,name=code" json:"code,omitempty"`
	Value         *string                `protobuf:"bytes,2,opt,name=value" json:"value,omitempty"`
	ParameterType *string                `protobuf:"bytes,3,opt,name=parameter_type,json=parameterType" json:"parameter_type,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ServiceParameter) Reset() {
	*x = ServiceParameter{}
	mi := &file_inner_processing_grpc_multiaccounting_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ServiceParameter) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ServiceParameter) ProtoMessage() {}

func (x *ServiceParameter) ProtoReflect() protoreflect.Message {
	mi := &file_inner_processing_grpc_multiaccounting_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ServiceParameter.ProtoReflect.Descriptor instead.
func (*ServiceParameter) Descriptor() ([]byte, []int) {
	return file_inner_processing_grpc_multiaccounting_proto_rawDescGZIP(), []int{3}
}

func (x *ServiceParameter) GetCode() string {
	if x != nil && x.Code != nil {
		return *x.Code
	}
	return ""
}

func (x *ServiceParameter) GetValue() string {
	if x != nil && x.Value != nil {
		return *x.Value
	}
	return ""
}

func (x *ServiceParameter) GetParameterType() string {
	if x != nil && x.ParameterType != nil {
		return *x.ParameterType
	}
	return ""
}

type TransitAccountTransfer struct {
	state               protoimpl.MessageState `protogen:"open.v1"`
	Id                  *int32                 `protobuf:"varint,1,opt,name=id" json:"id,omitempty"`
	ServiceId           *int32                 `protobuf:"varint,2,opt,name=service_id,json=serviceId" json:"service_id,omitempty"`
	Amount              *float64               `protobuf:"fixed64,3,opt,name=amount" json:"amount,omitempty"`
	Currency            *string                `protobuf:"bytes,4,opt,name=currency" json:"currency,omitempty"`
	ExternalReferenceId *string                `protobuf:"bytes,5,opt,name=external_reference_id,json=externalReferenceId" json:"external_reference_id,omitempty"`
	TransferStatus      *string                `protobuf:"bytes,6,opt,name=transfer_status,json=transferStatus" json:"transfer_status,omitempty"`
	TransferStatusName  *string                `protobuf:"bytes,7,opt,name=transfer_status_name,json=transferStatusName" json:"transfer_status_name,omitempty"`
	CreationDate        *string                `protobuf:"bytes,8,opt,name=creation_date,json=creationDate" json:"creation_date,omitempty"`
	FinishingDate       *string                `protobuf:"bytes,9,opt,name=finishing_date,json=finishingDate" json:"finishing_date,omitempty"`
	ServiceParameters   []*ServiceParameter    `protobuf:"bytes,10,rep,name=service_parameters,json=serviceParameters" json:"service_parameters,omitempty"`
	AuthCode            *string                `protobuf:"bytes,11,opt,name=auth_code,json=authCode" json:"auth_code,omitempty"`
	Card                *string                `protobuf:"bytes,12,opt,name=card" json:"card,omitempty"`
	Terminal            *string                `protobuf:"bytes,13,opt,name=terminal" json:"terminal,omitempty"`
	unknownFields       protoimpl.UnknownFields
	sizeCache           protoimpl.SizeCache
}

func (x *TransitAccountTransfer) Reset() {
	*x = TransitAccountTransfer{}
	mi := &file_inner_processing_grpc_multiaccounting_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *TransitAccountTransfer) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TransitAccountTransfer) ProtoMessage() {}

func (x *TransitAccountTransfer) ProtoReflect() protoreflect.Message {
	mi := &file_inner_processing_grpc_multiaccounting_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TransitAccountTransfer.ProtoReflect.Descriptor instead.
func (*TransitAccountTransfer) Descriptor() ([]byte, []int) {
	return file_inner_processing_grpc_multiaccounting_proto_rawDescGZIP(), []int{4}
}

func (x *TransitAccountTransfer) GetId() int32 {
	if x != nil && x.Id != nil {
		return *x.Id
	}
	return 0
}

func (x *TransitAccountTransfer) GetServiceId() int32 {
	if x != nil && x.ServiceId != nil {
		return *x.ServiceId
	}
	return 0
}

func (x *TransitAccountTransfer) GetAmount() float64 {
	if x != nil && x.Amount != nil {
		return *x.Amount
	}
	return 0
}

func (x *TransitAccountTransfer) GetCurrency() string {
	if x != nil && x.Currency != nil {
		return *x.Currency
	}
	return ""
}

func (x *TransitAccountTransfer) GetExternalReferenceId() string {
	if x != nil && x.ExternalReferenceId != nil {
		return *x.ExternalReferenceId
	}
	return ""
}

func (x *TransitAccountTransfer) GetTransferStatus() string {
	if x != nil && x.TransferStatus != nil {
		return *x.TransferStatus
	}
	return ""
}

func (x *TransitAccountTransfer) GetTransferStatusName() string {
	if x != nil && x.TransferStatusName != nil {
		return *x.TransferStatusName
	}
	return ""
}

func (x *TransitAccountTransfer) GetCreationDate() string {
	if x != nil && x.CreationDate != nil {
		return *x.CreationDate
	}
	return ""
}

func (x *TransitAccountTransfer) GetFinishingDate() string {
	if x != nil && x.FinishingDate != nil {
		return *x.FinishingDate
	}
	return ""
}

func (x *TransitAccountTransfer) GetServiceParameters() []*ServiceParameter {
	if x != nil {
		return x.ServiceParameters
	}
	return nil
}

func (x *TransitAccountTransfer) GetAuthCode() string {
	if x != nil && x.AuthCode != nil {
		return *x.AuthCode
	}
	return ""
}

func (x *TransitAccountTransfer) GetCard() string {
	if x != nil && x.Card != nil {
		return *x.Card
	}
	return ""
}

func (x *TransitAccountTransfer) GetTerminal() string {
	if x != nil && x.Terminal != nil {
		return *x.Terminal
	}
	return ""
}

type TransitAccountOperation struct {
	state               protoimpl.MessageState `protogen:"open.v1"`
	ExternalReferenceId *string                `protobuf:"bytes,1,opt,name=external_reference_id,json=externalReferenceId" json:"external_reference_id,omitempty"`
	Date                *string                `protobuf:"bytes,2,opt,name=date" json:"date,omitempty"`
	OperationType       *string                `protobuf:"bytes,3,opt,name=operation_type,json=operationType" json:"operation_type,omitempty"`
	MerchantAccount     *string                `protobuf:"bytes,4,opt,name=merchant_account,json=merchantAccount" json:"merchant_account,omitempty"`
	MerchantBank        *string                `protobuf:"bytes,5,opt,name=merchant_bank,json=merchantBank" json:"merchant_bank,omitempty"`
	MerchantBin         *string                `protobuf:"bytes,6,opt,name=merchant_bin,json=merchantBin" json:"merchant_bin,omitempty"`
	MerchantName        *string                `protobuf:"bytes,7,opt,name=merchant_name,json=merchantName" json:"merchant_name,omitempty"`
	Amount              *float64               `protobuf:"fixed64,8,opt,name=amount" json:"amount,omitempty"`
	Currency            *string                `protobuf:"bytes,9,opt,name=currency" json:"currency,omitempty"`
	PaymentPurposeCode  *string                `protobuf:"bytes,10,opt,name=payment_purpose_code,json=paymentPurposeCode" json:"payment_purpose_code,omitempty"`
	Description         *string                `protobuf:"bytes,11,opt,name=description" json:"description,omitempty"`
	TransferTypeId      *uint64                `protobuf:"varint,12,opt,name=transfer_type_id,json=transferTypeId" json:"transfer_type_id,omitempty"`
	unknownFields       protoimpl.UnknownFields
	sizeCache           protoimpl.SizeCache
}

func (x *TransitAccountOperation) Reset() {
	*x = TransitAccountOperation{}
	mi := &file_inner_processing_grpc_multiaccounting_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *TransitAccountOperation) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TransitAccountOperation) ProtoMessage() {}

func (x *TransitAccountOperation) ProtoReflect() protoreflect.Message {
	mi := &file_inner_processing_grpc_multiaccounting_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TransitAccountOperation.ProtoReflect.Descriptor instead.
func (*TransitAccountOperation) Descriptor() ([]byte, []int) {
	return file_inner_processing_grpc_multiaccounting_proto_rawDescGZIP(), []int{5}
}

func (x *TransitAccountOperation) GetExternalReferenceId() string {
	if x != nil && x.ExternalReferenceId != nil {
		return *x.ExternalReferenceId
	}
	return ""
}

func (x *TransitAccountOperation) GetDate() string {
	if x != nil && x.Date != nil {
		return *x.Date
	}
	return ""
}

func (x *TransitAccountOperation) GetOperationType() string {
	if x != nil && x.OperationType != nil {
		return *x.OperationType
	}
	return ""
}

func (x *TransitAccountOperation) GetMerchantAccount() string {
	if x != nil && x.MerchantAccount != nil {
		return *x.MerchantAccount
	}
	return ""
}

func (x *TransitAccountOperation) GetMerchantBank() string {
	if x != nil && x.MerchantBank != nil {
		return *x.MerchantBank
	}
	return ""
}

func (x *TransitAccountOperation) GetMerchantBin() string {
	if x != nil && x.MerchantBin != nil {
		return *x.MerchantBin
	}
	return ""
}

func (x *TransitAccountOperation) GetMerchantName() string {
	if x != nil && x.MerchantName != nil {
		return *x.MerchantName
	}
	return ""
}

func (x *TransitAccountOperation) GetAmount() float64 {
	if x != nil && x.Amount != nil {
		return *x.Amount
	}
	return 0
}

func (x *TransitAccountOperation) GetCurrency() string {
	if x != nil && x.Currency != nil {
		return *x.Currency
	}
	return ""
}

func (x *TransitAccountOperation) GetPaymentPurposeCode() string {
	if x != nil && x.PaymentPurposeCode != nil {
		return *x.PaymentPurposeCode
	}
	return ""
}

func (x *TransitAccountOperation) GetDescription() string {
	if x != nil && x.Description != nil {
		return *x.Description
	}
	return ""
}

func (x *TransitAccountOperation) GetTransferTypeId() uint64 {
	if x != nil && x.TransferTypeId != nil {
		return *x.TransferTypeId
	}
	return 0
}

type MakeTransferRequest struct {
	state               protoimpl.MessageState `protogen:"open.v1"`
	TransferId          *uint64                `protobuf:"varint,1,opt,name=transfer_id,json=transferId" json:"transfer_id,omitempty"`
	Account             *AccountTerminal       `protobuf:"bytes,2,opt,name=account" json:"account,omitempty"`
	ExternalReferenceId *string                `protobuf:"bytes,3,opt,name=external_reference_id,json=externalReferenceId" json:"external_reference_id,omitempty"`
	Amount              *float64               `protobuf:"fixed64,4,opt,name=amount" json:"amount,omitempty"`
	Currency            *string                `protobuf:"bytes,5,opt,name=currency" json:"currency,omitempty"`
	IsNeedAcceptation   *int32                 `protobuf:"varint,6,opt,name=is_need_acceptation,json=isNeedAcceptation" json:"is_need_acceptation,omitempty"`
	ServiceParameters   []*ServiceParameter    `protobuf:"bytes,7,rep,name=service_parameters,json=serviceParameters" json:"service_parameters,omitempty"`
	unknownFields       protoimpl.UnknownFields
	sizeCache           protoimpl.SizeCache
}

func (x *MakeTransferRequest) Reset() {
	*x = MakeTransferRequest{}
	mi := &file_inner_processing_grpc_multiaccounting_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *MakeTransferRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MakeTransferRequest) ProtoMessage() {}

func (x *MakeTransferRequest) ProtoReflect() protoreflect.Message {
	mi := &file_inner_processing_grpc_multiaccounting_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MakeTransferRequest.ProtoReflect.Descriptor instead.
func (*MakeTransferRequest) Descriptor() ([]byte, []int) {
	return file_inner_processing_grpc_multiaccounting_proto_rawDescGZIP(), []int{6}
}

func (x *MakeTransferRequest) GetTransferId() uint64 {
	if x != nil && x.TransferId != nil {
		return *x.TransferId
	}
	return 0
}

func (x *MakeTransferRequest) GetAccount() *AccountTerminal {
	if x != nil {
		return x.Account
	}
	return nil
}

func (x *MakeTransferRequest) GetExternalReferenceId() string {
	if x != nil && x.ExternalReferenceId != nil {
		return *x.ExternalReferenceId
	}
	return ""
}

func (x *MakeTransferRequest) GetAmount() float64 {
	if x != nil && x.Amount != nil {
		return *x.Amount
	}
	return 0
}

func (x *MakeTransferRequest) GetCurrency() string {
	if x != nil && x.Currency != nil {
		return *x.Currency
	}
	return ""
}

func (x *MakeTransferRequest) GetIsNeedAcceptation() int32 {
	if x != nil && x.IsNeedAcceptation != nil {
		return *x.IsNeedAcceptation
	}
	return 0
}

func (x *MakeTransferRequest) GetServiceParameters() []*ServiceParameter {
	if x != nil {
		return x.ServiceParameters
	}
	return nil
}

type MakeMerchantCheckRequest struct {
	state               protoimpl.MessageState `protogen:"open.v1"`
	Account             *AccountTerminal       `protobuf:"bytes,1,opt,name=account" json:"account,omitempty"`
	MerchantBin         *string                `protobuf:"bytes,2,opt,name=merchant_bin,json=merchantBin" json:"merchant_bin,omitempty"`
	MerchantBankAccount *string                `protobuf:"bytes,3,opt,name=merchant_bank_account,json=merchantBankAccount" json:"merchant_bank_account,omitempty"`
	unknownFields       protoimpl.UnknownFields
	sizeCache           protoimpl.SizeCache
}

func (x *MakeMerchantCheckRequest) Reset() {
	*x = MakeMerchantCheckRequest{}
	mi := &file_inner_processing_grpc_multiaccounting_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *MakeMerchantCheckRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MakeMerchantCheckRequest) ProtoMessage() {}

func (x *MakeMerchantCheckRequest) ProtoReflect() protoreflect.Message {
	mi := &file_inner_processing_grpc_multiaccounting_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MakeMerchantCheckRequest.ProtoReflect.Descriptor instead.
func (*MakeMerchantCheckRequest) Descriptor() ([]byte, []int) {
	return file_inner_processing_grpc_multiaccounting_proto_rawDescGZIP(), []int{7}
}

func (x *MakeMerchantCheckRequest) GetAccount() *AccountTerminal {
	if x != nil {
		return x.Account
	}
	return nil
}

func (x *MakeMerchantCheckRequest) GetMerchantBin() string {
	if x != nil && x.MerchantBin != nil {
		return *x.MerchantBin
	}
	return ""
}

func (x *MakeMerchantCheckRequest) GetMerchantBankAccount() string {
	if x != nil && x.MerchantBankAccount != nil {
		return *x.MerchantBankAccount
	}
	return ""
}

type AcceptTransferRequest struct {
	state               protoimpl.MessageState `protogen:"open.v1"`
	TransferId          *uint64                `protobuf:"varint,1,opt,name=transfer_id,json=transferId" json:"transfer_id,omitempty"`
	Account             *AccountTerminal       `protobuf:"bytes,2,opt,name=account" json:"account,omitempty"`
	ExternalReferenceId *string                `protobuf:"bytes,3,opt,name=external_reference_id,json=externalReferenceId" json:"external_reference_id,omitempty"`
	unknownFields       protoimpl.UnknownFields
	sizeCache           protoimpl.SizeCache
}

func (x *AcceptTransferRequest) Reset() {
	*x = AcceptTransferRequest{}
	mi := &file_inner_processing_grpc_multiaccounting_proto_msgTypes[8]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AcceptTransferRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AcceptTransferRequest) ProtoMessage() {}

func (x *AcceptTransferRequest) ProtoReflect() protoreflect.Message {
	mi := &file_inner_processing_grpc_multiaccounting_proto_msgTypes[8]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AcceptTransferRequest.ProtoReflect.Descriptor instead.
func (*AcceptTransferRequest) Descriptor() ([]byte, []int) {
	return file_inner_processing_grpc_multiaccounting_proto_rawDescGZIP(), []int{8}
}

func (x *AcceptTransferRequest) GetTransferId() uint64 {
	if x != nil && x.TransferId != nil {
		return *x.TransferId
	}
	return 0
}

func (x *AcceptTransferRequest) GetAccount() *AccountTerminal {
	if x != nil {
		return x.Account
	}
	return nil
}

func (x *AcceptTransferRequest) GetExternalReferenceId() string {
	if x != nil && x.ExternalReferenceId != nil {
		return *x.ExternalReferenceId
	}
	return ""
}

type DeclineTransferRequest struct {
	state               protoimpl.MessageState `protogen:"open.v1"`
	TransferId          *uint64                `protobuf:"varint,1,opt,name=transfer_id,json=transferId" json:"transfer_id,omitempty"`
	Account             *AccountTerminal       `protobuf:"bytes,2,opt,name=account" json:"account,omitempty"`
	ExternalReferenceId *string                `protobuf:"bytes,3,opt,name=external_reference_id,json=externalReferenceId" json:"external_reference_id,omitempty"`
	unknownFields       protoimpl.UnknownFields
	sizeCache           protoimpl.SizeCache
}

func (x *DeclineTransferRequest) Reset() {
	*x = DeclineTransferRequest{}
	mi := &file_inner_processing_grpc_multiaccounting_proto_msgTypes[9]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DeclineTransferRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeclineTransferRequest) ProtoMessage() {}

func (x *DeclineTransferRequest) ProtoReflect() protoreflect.Message {
	mi := &file_inner_processing_grpc_multiaccounting_proto_msgTypes[9]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeclineTransferRequest.ProtoReflect.Descriptor instead.
func (*DeclineTransferRequest) Descriptor() ([]byte, []int) {
	return file_inner_processing_grpc_multiaccounting_proto_rawDescGZIP(), []int{9}
}

func (x *DeclineTransferRequest) GetTransferId() uint64 {
	if x != nil && x.TransferId != nil {
		return *x.TransferId
	}
	return 0
}

func (x *DeclineTransferRequest) GetAccount() *AccountTerminal {
	if x != nil {
		return x.Account
	}
	return nil
}

func (x *DeclineTransferRequest) GetExternalReferenceId() string {
	if x != nil && x.ExternalReferenceId != nil {
		return *x.ExternalReferenceId
	}
	return ""
}

type RedoTransferRequest struct {
	state               protoimpl.MessageState `protogen:"open.v1"`
	TransferId          *uint64                `protobuf:"varint,1,opt,name=transfer_id,json=transferId" json:"transfer_id,omitempty"`
	Account             *AccountTerminal       `protobuf:"bytes,2,opt,name=account" json:"account,omitempty"`
	ExternalReferenceId *string                `protobuf:"bytes,3,opt,name=external_reference_id,json=externalReferenceId" json:"external_reference_id,omitempty"`
	unknownFields       protoimpl.UnknownFields
	sizeCache           protoimpl.SizeCache
}

func (x *RedoTransferRequest) Reset() {
	*x = RedoTransferRequest{}
	mi := &file_inner_processing_grpc_multiaccounting_proto_msgTypes[10]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RedoTransferRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RedoTransferRequest) ProtoMessage() {}

func (x *RedoTransferRequest) ProtoReflect() protoreflect.Message {
	mi := &file_inner_processing_grpc_multiaccounting_proto_msgTypes[10]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RedoTransferRequest.ProtoReflect.Descriptor instead.
func (*RedoTransferRequest) Descriptor() ([]byte, []int) {
	return file_inner_processing_grpc_multiaccounting_proto_rawDescGZIP(), []int{10}
}

func (x *RedoTransferRequest) GetTransferId() uint64 {
	if x != nil && x.TransferId != nil {
		return *x.TransferId
	}
	return 0
}

func (x *RedoTransferRequest) GetAccount() *AccountTerminal {
	if x != nil {
		return x.Account
	}
	return nil
}

func (x *RedoTransferRequest) GetExternalReferenceId() string {
	if x != nil && x.ExternalReferenceId != nil {
		return *x.ExternalReferenceId
	}
	return ""
}

type GetTransferDetailsRequest struct {
	state               protoimpl.MessageState `protogen:"open.v1"`
	TransferId          *uint64                `protobuf:"varint,1,opt,name=transfer_id,json=transferId" json:"transfer_id,omitempty"`
	Account             *AccountTerminal       `protobuf:"bytes,2,opt,name=account" json:"account,omitempty"`
	ExternalReferenceId *string                `protobuf:"bytes,3,opt,name=external_reference_id,json=externalReferenceId" json:"external_reference_id,omitempty"`
	unknownFields       protoimpl.UnknownFields
	sizeCache           protoimpl.SizeCache
}

func (x *GetTransferDetailsRequest) Reset() {
	*x = GetTransferDetailsRequest{}
	mi := &file_inner_processing_grpc_multiaccounting_proto_msgTypes[11]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetTransferDetailsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetTransferDetailsRequest) ProtoMessage() {}

func (x *GetTransferDetailsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_inner_processing_grpc_multiaccounting_proto_msgTypes[11]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetTransferDetailsRequest.ProtoReflect.Descriptor instead.
func (*GetTransferDetailsRequest) Descriptor() ([]byte, []int) {
	return file_inner_processing_grpc_multiaccounting_proto_rawDescGZIP(), []int{11}
}

func (x *GetTransferDetailsRequest) GetTransferId() uint64 {
	if x != nil && x.TransferId != nil {
		return *x.TransferId
	}
	return 0
}

func (x *GetTransferDetailsRequest) GetAccount() *AccountTerminal {
	if x != nil {
		return x.Account
	}
	return nil
}

func (x *GetTransferDetailsRequest) GetExternalReferenceId() string {
	if x != nil && x.ExternalReferenceId != nil {
		return *x.ExternalReferenceId
	}
	return ""
}

type GetTransfersListRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Account       *AccountTerminal       `protobuf:"bytes,1,opt,name=account" json:"account,omitempty"`
	DateFrom      *string                `protobuf:"bytes,2,opt,name=date_from,json=dateFrom" json:"date_from,omitempty"`
	DateTo        *string                `protobuf:"bytes,3,opt,name=date_to,json=dateTo" json:"date_to,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetTransfersListRequest) Reset() {
	*x = GetTransfersListRequest{}
	mi := &file_inner_processing_grpc_multiaccounting_proto_msgTypes[12]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetTransfersListRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetTransfersListRequest) ProtoMessage() {}

func (x *GetTransfersListRequest) ProtoReflect() protoreflect.Message {
	mi := &file_inner_processing_grpc_multiaccounting_proto_msgTypes[12]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetTransfersListRequest.ProtoReflect.Descriptor instead.
func (*GetTransfersListRequest) Descriptor() ([]byte, []int) {
	return file_inner_processing_grpc_multiaccounting_proto_rawDescGZIP(), []int{12}
}

func (x *GetTransfersListRequest) GetAccount() *AccountTerminal {
	if x != nil {
		return x.Account
	}
	return nil
}

func (x *GetTransfersListRequest) GetDateFrom() string {
	if x != nil && x.DateFrom != nil {
		return *x.DateFrom
	}
	return ""
}

func (x *GetTransfersListRequest) GetDateTo() string {
	if x != nil && x.DateTo != nil {
		return *x.DateTo
	}
	return ""
}

type GetAccountBalanceRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Account       *AccountTerminal       `protobuf:"bytes,1,opt,name=account" json:"account,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetAccountBalanceRequest) Reset() {
	*x = GetAccountBalanceRequest{}
	mi := &file_inner_processing_grpc_multiaccounting_proto_msgTypes[13]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetAccountBalanceRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAccountBalanceRequest) ProtoMessage() {}

func (x *GetAccountBalanceRequest) ProtoReflect() protoreflect.Message {
	mi := &file_inner_processing_grpc_multiaccounting_proto_msgTypes[13]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAccountBalanceRequest.ProtoReflect.Descriptor instead.
func (*GetAccountBalanceRequest) Descriptor() ([]byte, []int) {
	return file_inner_processing_grpc_multiaccounting_proto_rawDescGZIP(), []int{13}
}

func (x *GetAccountBalanceRequest) GetAccount() *AccountTerminal {
	if x != nil {
		return x.Account
	}
	return nil
}

type GetAccountStatementRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Account       *AccountTerminal       `protobuf:"bytes,1,opt,name=account" json:"account,omitempty"`
	Date          *string                `protobuf:"bytes,2,opt,name=date" json:"date,omitempty"`
	Page          *int32                 `protobuf:"varint,3,opt,name=page" json:"page,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetAccountStatementRequest) Reset() {
	*x = GetAccountStatementRequest{}
	mi := &file_inner_processing_grpc_multiaccounting_proto_msgTypes[14]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetAccountStatementRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAccountStatementRequest) ProtoMessage() {}

func (x *GetAccountStatementRequest) ProtoReflect() protoreflect.Message {
	mi := &file_inner_processing_grpc_multiaccounting_proto_msgTypes[14]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAccountStatementRequest.ProtoReflect.Descriptor instead.
func (*GetAccountStatementRequest) Descriptor() ([]byte, []int) {
	return file_inner_processing_grpc_multiaccounting_proto_rawDescGZIP(), []int{14}
}

func (x *GetAccountStatementRequest) GetAccount() *AccountTerminal {
	if x != nil {
		return x.Account
	}
	return nil
}

func (x *GetAccountStatementRequest) GetDate() string {
	if x != nil && x.Date != nil {
		return *x.Date
	}
	return ""
}

func (x *GetAccountStatementRequest) GetPage() int32 {
	if x != nil && x.Page != nil {
		return *x.Page
	}
	return 0
}

// responses
type MakeTransferResponse struct {
	state               protoimpl.MessageState `protogen:"open.v1"`
	TransferId          *uint64                `protobuf:"varint,1,opt,name=transfer_id,json=transferId" json:"transfer_id,omitempty"`
	TransferStatusId    *uint64                `protobuf:"varint,2,opt,name=transfer_status_id,json=transferStatusId" json:"transfer_status_id,omitempty"`
	Status              *string                `protobuf:"bytes,3,opt,name=status" json:"status,omitempty"`
	Id                  *int32                 `protobuf:"varint,4,opt,name=id" json:"id,omitempty"`
	ServiceId           *int32                 `protobuf:"varint,5,opt,name=service_id,json=serviceId" json:"service_id,omitempty"`
	Amount              *float64               `protobuf:"fixed64,6,opt,name=amount" json:"amount,omitempty"`
	Currency            *string                `protobuf:"bytes,7,opt,name=currency" json:"currency,omitempty"`
	ExternalReferenceId *string                `protobuf:"bytes,8,opt,name=external_reference_id,json=externalReferenceId" json:"external_reference_id,omitempty"`
	TransferStatus      *string                `protobuf:"bytes,9,opt,name=transfer_status,json=transferStatus" json:"transfer_status,omitempty"`
	TransferStatusName  *string                `protobuf:"bytes,10,opt,name=transfer_status_name,json=transferStatusName" json:"transfer_status_name,omitempty"`
	CreationDate        *string                `protobuf:"bytes,11,opt,name=creation_date,json=creationDate" json:"creation_date,omitempty"`
	FinishingDate       *string                `protobuf:"bytes,12,opt,name=finishing_date,json=finishingDate" json:"finishing_date,omitempty"`
	ServiceParameters   []*ServiceParameter    `protobuf:"bytes,13,rep,name=service_parameters,json=serviceParameters" json:"service_parameters,omitempty"`
	Rrn                 *string                `protobuf:"bytes,14,opt,name=rrn" json:"rrn,omitempty"`
	AuthCode            *string                `protobuf:"bytes,15,opt,name=auth_code,json=authCode" json:"auth_code,omitempty"`
	Card                *string                `protobuf:"bytes,16,opt,name=card" json:"card,omitempty"`
	Terminal            *string                `protobuf:"bytes,17,opt,name=terminal" json:"terminal,omitempty"`
	unknownFields       protoimpl.UnknownFields
	sizeCache           protoimpl.SizeCache
}

func (x *MakeTransferResponse) Reset() {
	*x = MakeTransferResponse{}
	mi := &file_inner_processing_grpc_multiaccounting_proto_msgTypes[15]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *MakeTransferResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MakeTransferResponse) ProtoMessage() {}

func (x *MakeTransferResponse) ProtoReflect() protoreflect.Message {
	mi := &file_inner_processing_grpc_multiaccounting_proto_msgTypes[15]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MakeTransferResponse.ProtoReflect.Descriptor instead.
func (*MakeTransferResponse) Descriptor() ([]byte, []int) {
	return file_inner_processing_grpc_multiaccounting_proto_rawDescGZIP(), []int{15}
}

func (x *MakeTransferResponse) GetTransferId() uint64 {
	if x != nil && x.TransferId != nil {
		return *x.TransferId
	}
	return 0
}

func (x *MakeTransferResponse) GetTransferStatusId() uint64 {
	if x != nil && x.TransferStatusId != nil {
		return *x.TransferStatusId
	}
	return 0
}

func (x *MakeTransferResponse) GetStatus() string {
	if x != nil && x.Status != nil {
		return *x.Status
	}
	return ""
}

func (x *MakeTransferResponse) GetId() int32 {
	if x != nil && x.Id != nil {
		return *x.Id
	}
	return 0
}

func (x *MakeTransferResponse) GetServiceId() int32 {
	if x != nil && x.ServiceId != nil {
		return *x.ServiceId
	}
	return 0
}

func (x *MakeTransferResponse) GetAmount() float64 {
	if x != nil && x.Amount != nil {
		return *x.Amount
	}
	return 0
}

func (x *MakeTransferResponse) GetCurrency() string {
	if x != nil && x.Currency != nil {
		return *x.Currency
	}
	return ""
}

func (x *MakeTransferResponse) GetExternalReferenceId() string {
	if x != nil && x.ExternalReferenceId != nil {
		return *x.ExternalReferenceId
	}
	return ""
}

func (x *MakeTransferResponse) GetTransferStatus() string {
	if x != nil && x.TransferStatus != nil {
		return *x.TransferStatus
	}
	return ""
}

func (x *MakeTransferResponse) GetTransferStatusName() string {
	if x != nil && x.TransferStatusName != nil {
		return *x.TransferStatusName
	}
	return ""
}

func (x *MakeTransferResponse) GetCreationDate() string {
	if x != nil && x.CreationDate != nil {
		return *x.CreationDate
	}
	return ""
}

func (x *MakeTransferResponse) GetFinishingDate() string {
	if x != nil && x.FinishingDate != nil {
		return *x.FinishingDate
	}
	return ""
}

func (x *MakeTransferResponse) GetServiceParameters() []*ServiceParameter {
	if x != nil {
		return x.ServiceParameters
	}
	return nil
}

func (x *MakeTransferResponse) GetRrn() string {
	if x != nil && x.Rrn != nil {
		return *x.Rrn
	}
	return ""
}

func (x *MakeTransferResponse) GetAuthCode() string {
	if x != nil && x.AuthCode != nil {
		return *x.AuthCode
	}
	return ""
}

func (x *MakeTransferResponse) GetCard() string {
	if x != nil && x.Card != nil {
		return *x.Card
	}
	return ""
}

func (x *MakeTransferResponse) GetTerminal() string {
	if x != nil && x.Terminal != nil {
		return *x.Terminal
	}
	return ""
}

type MakeMerchantCheckResponse struct {
	state         protoimpl.MessageState   `protogen:"open.v1"`
	Status        *string                  `protobuf:"bytes,1,opt,name=status" json:"status,omitempty"`
	ResponseData  []*ServiceParametersList `protobuf:"bytes,2,rep,name=response_data,json=responseData" json:"response_data,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *MakeMerchantCheckResponse) Reset() {
	*x = MakeMerchantCheckResponse{}
	mi := &file_inner_processing_grpc_multiaccounting_proto_msgTypes[16]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *MakeMerchantCheckResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MakeMerchantCheckResponse) ProtoMessage() {}

func (x *MakeMerchantCheckResponse) ProtoReflect() protoreflect.Message {
	mi := &file_inner_processing_grpc_multiaccounting_proto_msgTypes[16]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MakeMerchantCheckResponse.ProtoReflect.Descriptor instead.
func (*MakeMerchantCheckResponse) Descriptor() ([]byte, []int) {
	return file_inner_processing_grpc_multiaccounting_proto_rawDescGZIP(), []int{16}
}

func (x *MakeMerchantCheckResponse) GetStatus() string {
	if x != nil && x.Status != nil {
		return *x.Status
	}
	return ""
}

func (x *MakeMerchantCheckResponse) GetResponseData() []*ServiceParametersList {
	if x != nil {
		return x.ResponseData
	}
	return nil
}

type AcceptTransferResponse struct {
	state               protoimpl.MessageState `protogen:"open.v1"`
	TransferId          *uint64                `protobuf:"varint,1,opt,name=transfer_id,json=transferId" json:"transfer_id,omitempty"`
	TransferStatusId    *uint64                `protobuf:"varint,2,opt,name=transfer_status_id,json=transferStatusId" json:"transfer_status_id,omitempty"`
	Status              *string                `protobuf:"bytes,3,opt,name=status" json:"status,omitempty"`
	Id                  *int32                 `protobuf:"varint,4,opt,name=id" json:"id,omitempty"`
	ServiceId           *int32                 `protobuf:"varint,5,opt,name=service_id,json=serviceId" json:"service_id,omitempty"`
	Amount              *float64               `protobuf:"fixed64,6,opt,name=amount" json:"amount,omitempty"`
	Currency            *string                `protobuf:"bytes,7,opt,name=currency" json:"currency,omitempty"`
	ExternalReferenceId *string                `protobuf:"bytes,8,opt,name=external_reference_id,json=externalReferenceId" json:"external_reference_id,omitempty"`
	TransferStatus      *string                `protobuf:"bytes,9,opt,name=transfer_status,json=transferStatus" json:"transfer_status,omitempty"`
	TransferStatusName  *string                `protobuf:"bytes,10,opt,name=transfer_status_name,json=transferStatusName" json:"transfer_status_name,omitempty"`
	CreationDate        *string                `protobuf:"bytes,11,opt,name=creation_date,json=creationDate" json:"creation_date,omitempty"`
	FinishingDate       *string                `protobuf:"bytes,12,opt,name=finishing_date,json=finishingDate" json:"finishing_date,omitempty"`
	ServiceParameters   []*ServiceParameter    `protobuf:"bytes,13,rep,name=service_parameters,json=serviceParameters" json:"service_parameters,omitempty"`
	AuthCode            *string                `protobuf:"bytes,14,opt,name=auth_code,json=authCode" json:"auth_code,omitempty"`
	Card                *string                `protobuf:"bytes,15,opt,name=card" json:"card,omitempty"`
	Terminal            *string                `protobuf:"bytes,16,opt,name=terminal" json:"terminal,omitempty"`
	unknownFields       protoimpl.UnknownFields
	sizeCache           protoimpl.SizeCache
}

func (x *AcceptTransferResponse) Reset() {
	*x = AcceptTransferResponse{}
	mi := &file_inner_processing_grpc_multiaccounting_proto_msgTypes[17]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AcceptTransferResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AcceptTransferResponse) ProtoMessage() {}

func (x *AcceptTransferResponse) ProtoReflect() protoreflect.Message {
	mi := &file_inner_processing_grpc_multiaccounting_proto_msgTypes[17]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AcceptTransferResponse.ProtoReflect.Descriptor instead.
func (*AcceptTransferResponse) Descriptor() ([]byte, []int) {
	return file_inner_processing_grpc_multiaccounting_proto_rawDescGZIP(), []int{17}
}

func (x *AcceptTransferResponse) GetTransferId() uint64 {
	if x != nil && x.TransferId != nil {
		return *x.TransferId
	}
	return 0
}

func (x *AcceptTransferResponse) GetTransferStatusId() uint64 {
	if x != nil && x.TransferStatusId != nil {
		return *x.TransferStatusId
	}
	return 0
}

func (x *AcceptTransferResponse) GetStatus() string {
	if x != nil && x.Status != nil {
		return *x.Status
	}
	return ""
}

func (x *AcceptTransferResponse) GetId() int32 {
	if x != nil && x.Id != nil {
		return *x.Id
	}
	return 0
}

func (x *AcceptTransferResponse) GetServiceId() int32 {
	if x != nil && x.ServiceId != nil {
		return *x.ServiceId
	}
	return 0
}

func (x *AcceptTransferResponse) GetAmount() float64 {
	if x != nil && x.Amount != nil {
		return *x.Amount
	}
	return 0
}

func (x *AcceptTransferResponse) GetCurrency() string {
	if x != nil && x.Currency != nil {
		return *x.Currency
	}
	return ""
}

func (x *AcceptTransferResponse) GetExternalReferenceId() string {
	if x != nil && x.ExternalReferenceId != nil {
		return *x.ExternalReferenceId
	}
	return ""
}

func (x *AcceptTransferResponse) GetTransferStatus() string {
	if x != nil && x.TransferStatus != nil {
		return *x.TransferStatus
	}
	return ""
}

func (x *AcceptTransferResponse) GetTransferStatusName() string {
	if x != nil && x.TransferStatusName != nil {
		return *x.TransferStatusName
	}
	return ""
}

func (x *AcceptTransferResponse) GetCreationDate() string {
	if x != nil && x.CreationDate != nil {
		return *x.CreationDate
	}
	return ""
}

func (x *AcceptTransferResponse) GetFinishingDate() string {
	if x != nil && x.FinishingDate != nil {
		return *x.FinishingDate
	}
	return ""
}

func (x *AcceptTransferResponse) GetServiceParameters() []*ServiceParameter {
	if x != nil {
		return x.ServiceParameters
	}
	return nil
}

func (x *AcceptTransferResponse) GetAuthCode() string {
	if x != nil && x.AuthCode != nil {
		return *x.AuthCode
	}
	return ""
}

func (x *AcceptTransferResponse) GetCard() string {
	if x != nil && x.Card != nil {
		return *x.Card
	}
	return ""
}

func (x *AcceptTransferResponse) GetTerminal() string {
	if x != nil && x.Terminal != nil {
		return *x.Terminal
	}
	return ""
}

type DeclineTransferResponse struct {
	state               protoimpl.MessageState `protogen:"open.v1"`
	TransferId          *uint64                `protobuf:"varint,1,opt,name=transfer_id,json=transferId" json:"transfer_id,omitempty"`
	TransferStatusId    *uint64                `protobuf:"varint,2,opt,name=transfer_status_id,json=transferStatusId" json:"transfer_status_id,omitempty"`
	Status              *string                `protobuf:"bytes,3,opt,name=status" json:"status,omitempty"`
	Id                  *int32                 `protobuf:"varint,4,opt,name=id" json:"id,omitempty"`
	ServiceId           *int32                 `protobuf:"varint,5,opt,name=service_id,json=serviceId" json:"service_id,omitempty"`
	Amount              *float64               `protobuf:"fixed64,6,opt,name=amount" json:"amount,omitempty"`
	Currency            *string                `protobuf:"bytes,7,opt,name=currency" json:"currency,omitempty"`
	ExternalReferenceId *string                `protobuf:"bytes,8,opt,name=external_reference_id,json=externalReferenceId" json:"external_reference_id,omitempty"`
	TransferStatus      *string                `protobuf:"bytes,9,opt,name=transfer_status,json=transferStatus" json:"transfer_status,omitempty"`
	TransferStatusName  *string                `protobuf:"bytes,10,opt,name=transfer_status_name,json=transferStatusName" json:"transfer_status_name,omitempty"`
	CreationDate        *string                `protobuf:"bytes,11,opt,name=creation_date,json=creationDate" json:"creation_date,omitempty"`
	FinishingDate       *string                `protobuf:"bytes,12,opt,name=finishing_date,json=finishingDate" json:"finishing_date,omitempty"`
	ServiceParameters   []*ServiceParameter    `protobuf:"bytes,13,rep,name=service_parameters,json=serviceParameters" json:"service_parameters,omitempty"`
	AuthCode            *string                `protobuf:"bytes,14,opt,name=auth_code,json=authCode" json:"auth_code,omitempty"`
	Card                *string                `protobuf:"bytes,15,opt,name=card" json:"card,omitempty"`
	Terminal            *string                `protobuf:"bytes,16,opt,name=terminal" json:"terminal,omitempty"`
	unknownFields       protoimpl.UnknownFields
	sizeCache           protoimpl.SizeCache
}

func (x *DeclineTransferResponse) Reset() {
	*x = DeclineTransferResponse{}
	mi := &file_inner_processing_grpc_multiaccounting_proto_msgTypes[18]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DeclineTransferResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeclineTransferResponse) ProtoMessage() {}

func (x *DeclineTransferResponse) ProtoReflect() protoreflect.Message {
	mi := &file_inner_processing_grpc_multiaccounting_proto_msgTypes[18]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeclineTransferResponse.ProtoReflect.Descriptor instead.
func (*DeclineTransferResponse) Descriptor() ([]byte, []int) {
	return file_inner_processing_grpc_multiaccounting_proto_rawDescGZIP(), []int{18}
}

func (x *DeclineTransferResponse) GetTransferId() uint64 {
	if x != nil && x.TransferId != nil {
		return *x.TransferId
	}
	return 0
}

func (x *DeclineTransferResponse) GetTransferStatusId() uint64 {
	if x != nil && x.TransferStatusId != nil {
		return *x.TransferStatusId
	}
	return 0
}

func (x *DeclineTransferResponse) GetStatus() string {
	if x != nil && x.Status != nil {
		return *x.Status
	}
	return ""
}

func (x *DeclineTransferResponse) GetId() int32 {
	if x != nil && x.Id != nil {
		return *x.Id
	}
	return 0
}

func (x *DeclineTransferResponse) GetServiceId() int32 {
	if x != nil && x.ServiceId != nil {
		return *x.ServiceId
	}
	return 0
}

func (x *DeclineTransferResponse) GetAmount() float64 {
	if x != nil && x.Amount != nil {
		return *x.Amount
	}
	return 0
}

func (x *DeclineTransferResponse) GetCurrency() string {
	if x != nil && x.Currency != nil {
		return *x.Currency
	}
	return ""
}

func (x *DeclineTransferResponse) GetExternalReferenceId() string {
	if x != nil && x.ExternalReferenceId != nil {
		return *x.ExternalReferenceId
	}
	return ""
}

func (x *DeclineTransferResponse) GetTransferStatus() string {
	if x != nil && x.TransferStatus != nil {
		return *x.TransferStatus
	}
	return ""
}

func (x *DeclineTransferResponse) GetTransferStatusName() string {
	if x != nil && x.TransferStatusName != nil {
		return *x.TransferStatusName
	}
	return ""
}

func (x *DeclineTransferResponse) GetCreationDate() string {
	if x != nil && x.CreationDate != nil {
		return *x.CreationDate
	}
	return ""
}

func (x *DeclineTransferResponse) GetFinishingDate() string {
	if x != nil && x.FinishingDate != nil {
		return *x.FinishingDate
	}
	return ""
}

func (x *DeclineTransferResponse) GetServiceParameters() []*ServiceParameter {
	if x != nil {
		return x.ServiceParameters
	}
	return nil
}

func (x *DeclineTransferResponse) GetAuthCode() string {
	if x != nil && x.AuthCode != nil {
		return *x.AuthCode
	}
	return ""
}

func (x *DeclineTransferResponse) GetCard() string {
	if x != nil && x.Card != nil {
		return *x.Card
	}
	return ""
}

func (x *DeclineTransferResponse) GetTerminal() string {
	if x != nil && x.Terminal != nil {
		return *x.Terminal
	}
	return ""
}

type RedoTransferResponse struct {
	state               protoimpl.MessageState `protogen:"open.v1"`
	TransferId          *uint64                `protobuf:"varint,1,opt,name=transfer_id,json=transferId" json:"transfer_id,omitempty"`
	TransferStatusId    *uint64                `protobuf:"varint,2,opt,name=transfer_status_id,json=transferStatusId" json:"transfer_status_id,omitempty"`
	Status              *string                `protobuf:"bytes,3,opt,name=status" json:"status,omitempty"`
	Id                  *int32                 `protobuf:"varint,4,opt,name=id" json:"id,omitempty"`
	ServiceId           *int32                 `protobuf:"varint,5,opt,name=service_id,json=serviceId" json:"service_id,omitempty"`
	Amount              *float64               `protobuf:"fixed64,6,opt,name=amount" json:"amount,omitempty"`
	Currency            *string                `protobuf:"bytes,7,opt,name=currency" json:"currency,omitempty"`
	ExternalReferenceId *string                `protobuf:"bytes,8,opt,name=external_reference_id,json=externalReferenceId" json:"external_reference_id,omitempty"`
	TransferStatus      *string                `protobuf:"bytes,9,opt,name=transfer_status,json=transferStatus" json:"transfer_status,omitempty"`
	TransferStatusName  *string                `protobuf:"bytes,10,opt,name=transfer_status_name,json=transferStatusName" json:"transfer_status_name,omitempty"`
	CreationDate        *string                `protobuf:"bytes,11,opt,name=creation_date,json=creationDate" json:"creation_date,omitempty"`
	FinishingDate       *string                `protobuf:"bytes,12,opt,name=finishing_date,json=finishingDate" json:"finishing_date,omitempty"`
	ServiceParameters   []*ServiceParameter    `protobuf:"bytes,13,rep,name=service_parameters,json=serviceParameters" json:"service_parameters,omitempty"`
	AuthCode            *string                `protobuf:"bytes,14,opt,name=auth_code,json=authCode" json:"auth_code,omitempty"`
	Card                *string                `protobuf:"bytes,15,opt,name=card" json:"card,omitempty"`
	Terminal            *string                `protobuf:"bytes,16,opt,name=terminal" json:"terminal,omitempty"`
	unknownFields       protoimpl.UnknownFields
	sizeCache           protoimpl.SizeCache
}

func (x *RedoTransferResponse) Reset() {
	*x = RedoTransferResponse{}
	mi := &file_inner_processing_grpc_multiaccounting_proto_msgTypes[19]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RedoTransferResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RedoTransferResponse) ProtoMessage() {}

func (x *RedoTransferResponse) ProtoReflect() protoreflect.Message {
	mi := &file_inner_processing_grpc_multiaccounting_proto_msgTypes[19]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RedoTransferResponse.ProtoReflect.Descriptor instead.
func (*RedoTransferResponse) Descriptor() ([]byte, []int) {
	return file_inner_processing_grpc_multiaccounting_proto_rawDescGZIP(), []int{19}
}

func (x *RedoTransferResponse) GetTransferId() uint64 {
	if x != nil && x.TransferId != nil {
		return *x.TransferId
	}
	return 0
}

func (x *RedoTransferResponse) GetTransferStatusId() uint64 {
	if x != nil && x.TransferStatusId != nil {
		return *x.TransferStatusId
	}
	return 0
}

func (x *RedoTransferResponse) GetStatus() string {
	if x != nil && x.Status != nil {
		return *x.Status
	}
	return ""
}

func (x *RedoTransferResponse) GetId() int32 {
	if x != nil && x.Id != nil {
		return *x.Id
	}
	return 0
}

func (x *RedoTransferResponse) GetServiceId() int32 {
	if x != nil && x.ServiceId != nil {
		return *x.ServiceId
	}
	return 0
}

func (x *RedoTransferResponse) GetAmount() float64 {
	if x != nil && x.Amount != nil {
		return *x.Amount
	}
	return 0
}

func (x *RedoTransferResponse) GetCurrency() string {
	if x != nil && x.Currency != nil {
		return *x.Currency
	}
	return ""
}

func (x *RedoTransferResponse) GetExternalReferenceId() string {
	if x != nil && x.ExternalReferenceId != nil {
		return *x.ExternalReferenceId
	}
	return ""
}

func (x *RedoTransferResponse) GetTransferStatus() string {
	if x != nil && x.TransferStatus != nil {
		return *x.TransferStatus
	}
	return ""
}

func (x *RedoTransferResponse) GetTransferStatusName() string {
	if x != nil && x.TransferStatusName != nil {
		return *x.TransferStatusName
	}
	return ""
}

func (x *RedoTransferResponse) GetCreationDate() string {
	if x != nil && x.CreationDate != nil {
		return *x.CreationDate
	}
	return ""
}

func (x *RedoTransferResponse) GetFinishingDate() string {
	if x != nil && x.FinishingDate != nil {
		return *x.FinishingDate
	}
	return ""
}

func (x *RedoTransferResponse) GetServiceParameters() []*ServiceParameter {
	if x != nil {
		return x.ServiceParameters
	}
	return nil
}

func (x *RedoTransferResponse) GetAuthCode() string {
	if x != nil && x.AuthCode != nil {
		return *x.AuthCode
	}
	return ""
}

func (x *RedoTransferResponse) GetCard() string {
	if x != nil && x.Card != nil {
		return *x.Card
	}
	return ""
}

func (x *RedoTransferResponse) GetTerminal() string {
	if x != nil && x.Terminal != nil {
		return *x.Terminal
	}
	return ""
}

type GetTransferDetailsResponse struct {
	state               protoimpl.MessageState `protogen:"open.v1"`
	TransferId          *uint64                `protobuf:"varint,1,opt,name=transfer_id,json=transferId" json:"transfer_id,omitempty"`
	TransferStatusId    *uint64                `protobuf:"varint,2,opt,name=transfer_status_id,json=transferStatusId" json:"transfer_status_id,omitempty"`
	Status              *string                `protobuf:"bytes,3,opt,name=status" json:"status,omitempty"`
	Id                  *int32                 `protobuf:"varint,4,opt,name=id" json:"id,omitempty"`
	ServiceId           *int32                 `protobuf:"varint,5,opt,name=service_id,json=serviceId" json:"service_id,omitempty"`
	Amount              *float64               `protobuf:"fixed64,6,opt,name=amount" json:"amount,omitempty"`
	Currency            *string                `protobuf:"bytes,7,opt,name=currency" json:"currency,omitempty"`
	ExternalReferenceId *string                `protobuf:"bytes,8,opt,name=external_reference_id,json=externalReferenceId" json:"external_reference_id,omitempty"`
	TransferStatus      *string                `protobuf:"bytes,9,opt,name=transfer_status,json=transferStatus" json:"transfer_status,omitempty"`
	TransferStatusName  *string                `protobuf:"bytes,10,opt,name=transfer_status_name,json=transferStatusName" json:"transfer_status_name,omitempty"`
	CreationDate        *string                `protobuf:"bytes,11,opt,name=creation_date,json=creationDate" json:"creation_date,omitempty"`
	FinishingDate       *string                `protobuf:"bytes,12,opt,name=finishing_date,json=finishingDate" json:"finishing_date,omitempty"`
	ServiceParameters   []*ServiceParameter    `protobuf:"bytes,13,rep,name=service_parameters,json=serviceParameters" json:"service_parameters,omitempty"`
	AuthCode            *string                `protobuf:"bytes,14,opt,name=auth_code,json=authCode" json:"auth_code,omitempty"`
	Card                *string                `protobuf:"bytes,15,opt,name=card" json:"card,omitempty"`
	Terminal            *string                `protobuf:"bytes,16,opt,name=terminal" json:"terminal,omitempty"`
	unknownFields       protoimpl.UnknownFields
	sizeCache           protoimpl.SizeCache
}

func (x *GetTransferDetailsResponse) Reset() {
	*x = GetTransferDetailsResponse{}
	mi := &file_inner_processing_grpc_multiaccounting_proto_msgTypes[20]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetTransferDetailsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetTransferDetailsResponse) ProtoMessage() {}

func (x *GetTransferDetailsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_inner_processing_grpc_multiaccounting_proto_msgTypes[20]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetTransferDetailsResponse.ProtoReflect.Descriptor instead.
func (*GetTransferDetailsResponse) Descriptor() ([]byte, []int) {
	return file_inner_processing_grpc_multiaccounting_proto_rawDescGZIP(), []int{20}
}

func (x *GetTransferDetailsResponse) GetTransferId() uint64 {
	if x != nil && x.TransferId != nil {
		return *x.TransferId
	}
	return 0
}

func (x *GetTransferDetailsResponse) GetTransferStatusId() uint64 {
	if x != nil && x.TransferStatusId != nil {
		return *x.TransferStatusId
	}
	return 0
}

func (x *GetTransferDetailsResponse) GetStatus() string {
	if x != nil && x.Status != nil {
		return *x.Status
	}
	return ""
}

func (x *GetTransferDetailsResponse) GetId() int32 {
	if x != nil && x.Id != nil {
		return *x.Id
	}
	return 0
}

func (x *GetTransferDetailsResponse) GetServiceId() int32 {
	if x != nil && x.ServiceId != nil {
		return *x.ServiceId
	}
	return 0
}

func (x *GetTransferDetailsResponse) GetAmount() float64 {
	if x != nil && x.Amount != nil {
		return *x.Amount
	}
	return 0
}

func (x *GetTransferDetailsResponse) GetCurrency() string {
	if x != nil && x.Currency != nil {
		return *x.Currency
	}
	return ""
}

func (x *GetTransferDetailsResponse) GetExternalReferenceId() string {
	if x != nil && x.ExternalReferenceId != nil {
		return *x.ExternalReferenceId
	}
	return ""
}

func (x *GetTransferDetailsResponse) GetTransferStatus() string {
	if x != nil && x.TransferStatus != nil {
		return *x.TransferStatus
	}
	return ""
}

func (x *GetTransferDetailsResponse) GetTransferStatusName() string {
	if x != nil && x.TransferStatusName != nil {
		return *x.TransferStatusName
	}
	return ""
}

func (x *GetTransferDetailsResponse) GetCreationDate() string {
	if x != nil && x.CreationDate != nil {
		return *x.CreationDate
	}
	return ""
}

func (x *GetTransferDetailsResponse) GetFinishingDate() string {
	if x != nil && x.FinishingDate != nil {
		return *x.FinishingDate
	}
	return ""
}

func (x *GetTransferDetailsResponse) GetServiceParameters() []*ServiceParameter {
	if x != nil {
		return x.ServiceParameters
	}
	return nil
}

func (x *GetTransferDetailsResponse) GetAuthCode() string {
	if x != nil && x.AuthCode != nil {
		return *x.AuthCode
	}
	return ""
}

func (x *GetTransferDetailsResponse) GetCard() string {
	if x != nil && x.Card != nil {
		return *x.Card
	}
	return ""
}

func (x *GetTransferDetailsResponse) GetTerminal() string {
	if x != nil && x.Terminal != nil {
		return *x.Terminal
	}
	return ""
}

type GetTransfersListResponse struct {
	state         protoimpl.MessageState    `protogen:"open.v1"`
	Status        *string                   `protobuf:"bytes,1,opt,name=status" json:"status,omitempty"`
	Data          []*TransitAccountTransfer `protobuf:"bytes,2,rep,name=data" json:"data,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetTransfersListResponse) Reset() {
	*x = GetTransfersListResponse{}
	mi := &file_inner_processing_grpc_multiaccounting_proto_msgTypes[21]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetTransfersListResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetTransfersListResponse) ProtoMessage() {}

func (x *GetTransfersListResponse) ProtoReflect() protoreflect.Message {
	mi := &file_inner_processing_grpc_multiaccounting_proto_msgTypes[21]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetTransfersListResponse.ProtoReflect.Descriptor instead.
func (*GetTransfersListResponse) Descriptor() ([]byte, []int) {
	return file_inner_processing_grpc_multiaccounting_proto_rawDescGZIP(), []int{21}
}

func (x *GetTransfersListResponse) GetStatus() string {
	if x != nil && x.Status != nil {
		return *x.Status
	}
	return ""
}

func (x *GetTransfersListResponse) GetData() []*TransitAccountTransfer {
	if x != nil {
		return x.Data
	}
	return nil
}

type GetAccountBalanceResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Status        *string                `protobuf:"bytes,1,opt,name=status" json:"status,omitempty"`
	Balance       *float64               `protobuf:"fixed64,2,opt,name=balance" json:"balance,omitempty"`
	Processed     *float64               `protobuf:"fixed64,3,opt,name=processed" json:"processed,omitempty"`
	Threshold     *float64               `protobuf:"fixed64,4,opt,name=threshold" json:"threshold,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetAccountBalanceResponse) Reset() {
	*x = GetAccountBalanceResponse{}
	mi := &file_inner_processing_grpc_multiaccounting_proto_msgTypes[22]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetAccountBalanceResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAccountBalanceResponse) ProtoMessage() {}

func (x *GetAccountBalanceResponse) ProtoReflect() protoreflect.Message {
	mi := &file_inner_processing_grpc_multiaccounting_proto_msgTypes[22]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAccountBalanceResponse.ProtoReflect.Descriptor instead.
func (*GetAccountBalanceResponse) Descriptor() ([]byte, []int) {
	return file_inner_processing_grpc_multiaccounting_proto_rawDescGZIP(), []int{22}
}

func (x *GetAccountBalanceResponse) GetStatus() string {
	if x != nil && x.Status != nil {
		return *x.Status
	}
	return ""
}

func (x *GetAccountBalanceResponse) GetBalance() float64 {
	if x != nil && x.Balance != nil {
		return *x.Balance
	}
	return 0
}

func (x *GetAccountBalanceResponse) GetProcessed() float64 {
	if x != nil && x.Processed != nil {
		return *x.Processed
	}
	return 0
}

func (x *GetAccountBalanceResponse) GetThreshold() float64 {
	if x != nil && x.Threshold != nil {
		return *x.Threshold
	}
	return 0
}

type GetAccountStatementResponse struct {
	state         protoimpl.MessageState     `protogen:"open.v1"`
	Status        *string                    `protobuf:"bytes,1,opt,name=status" json:"status,omitempty"`
	InBalance     *float64                   `protobuf:"fixed64,2,opt,name=in_balance,json=inBalance" json:"in_balance,omitempty"`
	OutBalance    *float64                   `protobuf:"fixed64,3,opt,name=out_balance,json=outBalance" json:"out_balance,omitempty"`
	Page          *int32                     `protobuf:"varint,4,opt,name=page" json:"page,omitempty"`
	PagesCount    *int32                     `protobuf:"varint,5,opt,name=pages_count,json=pagesCount" json:"pages_count,omitempty"`
	Operations    []*TransitAccountOperation `protobuf:"bytes,6,rep,name=operations" json:"operations,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetAccountStatementResponse) Reset() {
	*x = GetAccountStatementResponse{}
	mi := &file_inner_processing_grpc_multiaccounting_proto_msgTypes[23]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetAccountStatementResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAccountStatementResponse) ProtoMessage() {}

func (x *GetAccountStatementResponse) ProtoReflect() protoreflect.Message {
	mi := &file_inner_processing_grpc_multiaccounting_proto_msgTypes[23]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAccountStatementResponse.ProtoReflect.Descriptor instead.
func (*GetAccountStatementResponse) Descriptor() ([]byte, []int) {
	return file_inner_processing_grpc_multiaccounting_proto_rawDescGZIP(), []int{23}
}

func (x *GetAccountStatementResponse) GetStatus() string {
	if x != nil && x.Status != nil {
		return *x.Status
	}
	return ""
}

func (x *GetAccountStatementResponse) GetInBalance() float64 {
	if x != nil && x.InBalance != nil {
		return *x.InBalance
	}
	return 0
}

func (x *GetAccountStatementResponse) GetOutBalance() float64 {
	if x != nil && x.OutBalance != nil {
		return *x.OutBalance
	}
	return 0
}

func (x *GetAccountStatementResponse) GetPage() int32 {
	if x != nil && x.Page != nil {
		return *x.Page
	}
	return 0
}

func (x *GetAccountStatementResponse) GetPagesCount() int32 {
	if x != nil && x.PagesCount != nil {
		return *x.PagesCount
	}
	return 0
}

func (x *GetAccountStatementResponse) GetOperations() []*TransitAccountOperation {
	if x != nil {
		return x.Operations
	}
	return nil
}

var File_inner_processing_grpc_multiaccounting_proto protoreflect.FileDescriptor

var file_inner_processing_grpc_multiaccounting_proto_rawDesc = string([]byte{
	0x0a, 0x2b, 0x69, 0x6e, 0x6e, 0x65, 0x72, 0x2f, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69,
	0x6e, 0x67, 0x2f, 0x67, 0x72, 0x70, 0x63, 0x2f, 0x6d, 0x75, 0x6c, 0x74, 0x69, 0x61, 0x63, 0x63,
	0x6f, 0x75, 0x6e, 0x74, 0x69, 0x6e, 0x67, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x2a, 0x70,
	0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x2e, 0x6d, 0x75, 0x6c, 0x74, 0x69, 0x61,
	0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x69, 0x6e, 0x67, 0x2e, 0x6d, 0x75, 0x6c, 0x74, 0x69, 0x61,
	0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x69, 0x6e, 0x67, 0x1a, 0x1c, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x73, 0x74, 0x72, 0x75, 0x63,
	0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1b, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x65, 0x6d, 0x70, 0x74, 0x79, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x16, 0x6d, 0x76, 0x70, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f,
	0x6c, 0x6f, 0x67, 0x67, 0x65, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x78, 0x0a, 0x0f,
	0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x54, 0x65, 0x72, 0x6d, 0x69, 0x6e, 0x61, 0x6c, 0x12,
	0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x02, 0x69, 0x64, 0x12,
	0x1b, 0x0a, 0x09, 0x62, 0x61, 0x6e, 0x6b, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x08, 0x62, 0x61, 0x6e, 0x6b, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x38, 0x0a, 0x06,
	0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x17, 0x2e, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x53,
	0x74, 0x72, 0x75, 0x63, 0x74, 0x42, 0x07, 0x9a, 0xfc, 0xeb, 0x8e, 0x02, 0x01, 0x64, 0x52, 0x06,
	0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x22, 0x85, 0x01, 0x0a, 0x15, 0x53, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x65, 0x74, 0x65, 0x72, 0x73, 0x4c, 0x69, 0x73, 0x74,
	0x12, 0x6c, 0x0a, 0x13, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x70, 0x61, 0x72, 0x61,
	0x6d, 0x73, 0x5f, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x3c, 0x2e,
	0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x2e, 0x6d, 0x75, 0x6c, 0x74, 0x69,
	0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x69, 0x6e, 0x67, 0x2e, 0x6d, 0x75, 0x6c, 0x74, 0x69,
	0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x69, 0x6e, 0x67, 0x2e, 0x53, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x65, 0x74, 0x65, 0x72, 0x52, 0x11, 0x73, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x4c, 0x69, 0x73, 0x74, 0x22, 0x4d,
	0x0a, 0x1c, 0x47, 0x65, 0x74, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x49, 0x64, 0x65, 0x6e,
	0x74, 0x69, 0x66, 0x69, 0x65, 0x72, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x2d,
	0x0a, 0x12, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x65, 0x6e, 0x74, 0x69,
	0x66, 0x69, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x11, 0x61, 0x63, 0x63, 0x6f,
	0x75, 0x6e, 0x74, 0x49, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x66, 0x69, 0x65, 0x72, 0x22, 0x63, 0x0a,
	0x10, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x65, 0x74, 0x65,
	0x72, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x04, 0x63, 0x6f, 0x64, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x12, 0x25, 0x0a, 0x0e, 0x70,
	0x61, 0x72, 0x61, 0x6d, 0x65, 0x74, 0x65, 0x72, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0d, 0x70, 0x61, 0x72, 0x61, 0x6d, 0x65, 0x74, 0x65, 0x72, 0x54, 0x79,
	0x70, 0x65, 0x22, 0x90, 0x04, 0x0a, 0x16, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x69, 0x74, 0x41, 0x63,
	0x63, 0x6f, 0x75, 0x6e, 0x74, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x66, 0x65, 0x72, 0x12, 0x0e, 0x0a,
	0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x02, 0x69, 0x64, 0x12, 0x1d, 0x0a,
	0x0a, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x05, 0x52, 0x09, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x49, 0x64, 0x12, 0x16, 0x0a, 0x06,
	0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x01, 0x52, 0x06, 0x61, 0x6d,
	0x6f, 0x75, 0x6e, 0x74, 0x12, 0x1a, 0x0a, 0x08, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x63, 0x79,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x63, 0x79,
	0x12, 0x32, 0x0a, 0x15, 0x65, 0x78, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x5f, 0x72, 0x65, 0x66,
	0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x13, 0x65, 0x78, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x52, 0x65, 0x66, 0x65, 0x72, 0x65, 0x6e,
	0x63, 0x65, 0x49, 0x64, 0x12, 0x27, 0x0a, 0x0f, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x66, 0x65, 0x72,
	0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x74,
	0x72, 0x61, 0x6e, 0x73, 0x66, 0x65, 0x72, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x30, 0x0a,
	0x14, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x66, 0x65, 0x72, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x12, 0x74, 0x72, 0x61,
	0x6e, 0x73, 0x66, 0x65, 0x72, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x4e, 0x61, 0x6d, 0x65, 0x12,
	0x23, 0x0a, 0x0d, 0x63, 0x72, 0x65, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x64, 0x61, 0x74, 0x65,
	0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x63, 0x72, 0x65, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x44, 0x61, 0x74, 0x65, 0x12, 0x25, 0x0a, 0x0e, 0x66, 0x69, 0x6e, 0x69, 0x73, 0x68, 0x69, 0x6e,
	0x67, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x66, 0x69,
	0x6e, 0x69, 0x73, 0x68, 0x69, 0x6e, 0x67, 0x44, 0x61, 0x74, 0x65, 0x12, 0x6b, 0x0a, 0x12, 0x73,
	0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x70, 0x61, 0x72, 0x61, 0x6d, 0x65, 0x74, 0x65, 0x72,
	0x73, 0x18, 0x0a, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x3c, 0x2e, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73,
	0x73, 0x69, 0x6e, 0x67, 0x2e, 0x6d, 0x75, 0x6c, 0x74, 0x69, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e,
	0x74, 0x69, 0x6e, 0x67, 0x2e, 0x6d, 0x75, 0x6c, 0x74, 0x69, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e,
	0x74, 0x69, 0x6e, 0x67, 0x2e, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x50, 0x61, 0x72, 0x61,
	0x6d, 0x65, 0x74, 0x65, 0x72, 0x52, 0x11, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x50, 0x61,
	0x72, 0x61, 0x6d, 0x65, 0x74, 0x65, 0x72, 0x73, 0x12, 0x1b, 0x0a, 0x09, 0x61, 0x75, 0x74, 0x68,
	0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x61, 0x75, 0x74,
	0x68, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x61, 0x72, 0x64, 0x18, 0x0c, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x04, 0x63, 0x61, 0x72, 0x64, 0x12, 0x1a, 0x0a, 0x08, 0x74, 0x65, 0x72,
	0x6d, 0x69, 0x6e, 0x61, 0x6c, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x74, 0x65, 0x72,
	0x6d, 0x69, 0x6e, 0x61, 0x6c, 0x22, 0xd2, 0x03, 0x0a, 0x17, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x69,
	0x74, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x4f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x12, 0x32, 0x0a, 0x15, 0x65, 0x78, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x5f, 0x72, 0x65,
	0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x13, 0x65, 0x78, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x52, 0x65, 0x66, 0x65, 0x72, 0x65,
	0x6e, 0x63, 0x65, 0x49, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x64, 0x61, 0x74, 0x65, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x04, 0x64, 0x61, 0x74, 0x65, 0x12, 0x25, 0x0a, 0x0e, 0x6f, 0x70, 0x65,
	0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0d, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65,
	0x12, 0x29, 0x0a, 0x10, 0x6d, 0x65, 0x72, 0x63, 0x68, 0x61, 0x6e, 0x74, 0x5f, 0x61, 0x63, 0x63,
	0x6f, 0x75, 0x6e, 0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f, 0x6d, 0x65, 0x72, 0x63,
	0x68, 0x61, 0x6e, 0x74, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x23, 0x0a, 0x0d, 0x6d,
	0x65, 0x72, 0x63, 0x68, 0x61, 0x6e, 0x74, 0x5f, 0x62, 0x61, 0x6e, 0x6b, 0x18, 0x05, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0c, 0x6d, 0x65, 0x72, 0x63, 0x68, 0x61, 0x6e, 0x74, 0x42, 0x61, 0x6e, 0x6b,
	0x12, 0x21, 0x0a, 0x0c, 0x6d, 0x65, 0x72, 0x63, 0x68, 0x61, 0x6e, 0x74, 0x5f, 0x62, 0x69, 0x6e,
	0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x6d, 0x65, 0x72, 0x63, 0x68, 0x61, 0x6e, 0x74,
	0x42, 0x69, 0x6e, 0x12, 0x23, 0x0a, 0x0d, 0x6d, 0x65, 0x72, 0x63, 0x68, 0x61, 0x6e, 0x74, 0x5f,
	0x6e, 0x61, 0x6d, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x6d, 0x65, 0x72, 0x63,
	0x68, 0x61, 0x6e, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x61, 0x6d, 0x6f, 0x75,
	0x6e, 0x74, 0x18, 0x08, 0x20, 0x01, 0x28, 0x01, 0x52, 0x06, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74,
	0x12, 0x1a, 0x0a, 0x08, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x63, 0x79, 0x18, 0x09, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x08, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x63, 0x79, 0x12, 0x30, 0x0a, 0x14,
	0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x70, 0x75, 0x72, 0x70, 0x6f, 0x73, 0x65, 0x5f,
	0x63, 0x6f, 0x64, 0x65, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x12, 0x70, 0x61, 0x79, 0x6d,
	0x65, 0x6e, 0x74, 0x50, 0x75, 0x72, 0x70, 0x6f, 0x73, 0x65, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x20,
	0x0a, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x0b, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e,
	0x12, 0x28, 0x0a, 0x10, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x66, 0x65, 0x72, 0x5f, 0x74, 0x79, 0x70,
	0x65, 0x5f, 0x69, 0x64, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x04, 0x52, 0x0e, 0x74, 0x72, 0x61, 0x6e,
	0x73, 0x66, 0x65, 0x72, 0x54, 0x79, 0x70, 0x65, 0x49, 0x64, 0x22, 0x92, 0x03, 0x0a, 0x13, 0x4d,
	0x61, 0x6b, 0x65, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x66, 0x65, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x12, 0x1f, 0x0a, 0x0b, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x66, 0x65, 0x72, 0x5f, 0x69,
	0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x0a, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x66, 0x65,
	0x72, 0x49, 0x64, 0x12, 0x55, 0x0a, 0x07, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x3b, 0x2e, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e,
	0x67, 0x2e, 0x6d, 0x75, 0x6c, 0x74, 0x69, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x69, 0x6e,
	0x67, 0x2e, 0x6d, 0x75, 0x6c, 0x74, 0x69, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x69, 0x6e,
	0x67, 0x2e, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x54, 0x65, 0x72, 0x6d, 0x69, 0x6e, 0x61,
	0x6c, 0x52, 0x07, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x32, 0x0a, 0x15, 0x65, 0x78,
	0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x5f, 0x72, 0x65, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65,
	0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x13, 0x65, 0x78, 0x74, 0x65, 0x72,
	0x6e, 0x61, 0x6c, 0x52, 0x65, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x49, 0x64, 0x12, 0x16,
	0x0a, 0x06, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x01, 0x52, 0x06,
	0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x1a, 0x0a, 0x08, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e,
	0x63, 0x79, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e,
	0x63, 0x79, 0x12, 0x2e, 0x0a, 0x13, 0x69, 0x73, 0x5f, 0x6e, 0x65, 0x65, 0x64, 0x5f, 0x61, 0x63,
	0x63, 0x65, 0x70, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x06, 0x20, 0x01, 0x28, 0x05, 0x52,
	0x11, 0x69, 0x73, 0x4e, 0x65, 0x65, 0x64, 0x41, 0x63, 0x63, 0x65, 0x70, 0x74, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x12, 0x6b, 0x0a, 0x12, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x70, 0x61,
	0x72, 0x61, 0x6d, 0x65, 0x74, 0x65, 0x72, 0x73, 0x18, 0x07, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x3c,
	0x2e, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x2e, 0x6d, 0x75, 0x6c, 0x74,
	0x69, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x69, 0x6e, 0x67, 0x2e, 0x6d, 0x75, 0x6c, 0x74,
	0x69, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x69, 0x6e, 0x67, 0x2e, 0x53, 0x65, 0x72, 0x76,
	0x69, 0x63, 0x65, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x65, 0x74, 0x65, 0x72, 0x52, 0x11, 0x73, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x65, 0x74, 0x65, 0x72, 0x73, 0x22,
	0xc8, 0x01, 0x0a, 0x18, 0x4d, 0x61, 0x6b, 0x65, 0x4d, 0x65, 0x72, 0x63, 0x68, 0x61, 0x6e, 0x74,
	0x43, 0x68, 0x65, 0x63, 0x6b, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x55, 0x0a, 0x07,
	0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x3b, 0x2e,
	0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x2e, 0x6d, 0x75, 0x6c, 0x74, 0x69,
	0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x69, 0x6e, 0x67, 0x2e, 0x6d, 0x75, 0x6c, 0x74, 0x69,
	0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x69, 0x6e, 0x67, 0x2e, 0x41, 0x63, 0x63, 0x6f, 0x75,
	0x6e, 0x74, 0x54, 0x65, 0x72, 0x6d, 0x69, 0x6e, 0x61, 0x6c, 0x52, 0x07, 0x61, 0x63, 0x63, 0x6f,
	0x75, 0x6e, 0x74, 0x12, 0x21, 0x0a, 0x0c, 0x6d, 0x65, 0x72, 0x63, 0x68, 0x61, 0x6e, 0x74, 0x5f,
	0x62, 0x69, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x6d, 0x65, 0x72, 0x63, 0x68,
	0x61, 0x6e, 0x74, 0x42, 0x69, 0x6e, 0x12, 0x32, 0x0a, 0x15, 0x6d, 0x65, 0x72, 0x63, 0x68, 0x61,
	0x6e, 0x74, 0x5f, 0x62, 0x61, 0x6e, 0x6b, 0x5f, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x13, 0x6d, 0x65, 0x72, 0x63, 0x68, 0x61, 0x6e, 0x74, 0x42,
	0x61, 0x6e, 0x6b, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x22, 0xc3, 0x01, 0x0a, 0x15, 0x41,
	0x63, 0x63, 0x65, 0x70, 0x74, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x66, 0x65, 0x72, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x12, 0x1f, 0x0a, 0x0b, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x66, 0x65, 0x72,
	0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x0a, 0x74, 0x72, 0x61, 0x6e, 0x73,
	0x66, 0x65, 0x72, 0x49, 0x64, 0x12, 0x55, 0x0a, 0x07, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x3b, 0x2e, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73,
	0x69, 0x6e, 0x67, 0x2e, 0x6d, 0x75, 0x6c, 0x74, 0x69, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74,
	0x69, 0x6e, 0x67, 0x2e, 0x6d, 0x75, 0x6c, 0x74, 0x69, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74,
	0x69, 0x6e, 0x67, 0x2e, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x54, 0x65, 0x72, 0x6d, 0x69,
	0x6e, 0x61, 0x6c, 0x52, 0x07, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x32, 0x0a, 0x15,
	0x65, 0x78, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x5f, 0x72, 0x65, 0x66, 0x65, 0x72, 0x65, 0x6e,
	0x63, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x13, 0x65, 0x78, 0x74,
	0x65, 0x72, 0x6e, 0x61, 0x6c, 0x52, 0x65, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x49, 0x64,
	0x22, 0xc4, 0x01, 0x0a, 0x16, 0x44, 0x65, 0x63, 0x6c, 0x69, 0x6e, 0x65, 0x54, 0x72, 0x61, 0x6e,
	0x73, 0x66, 0x65, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1f, 0x0a, 0x0b, 0x74,
	0x72, 0x61, 0x6e, 0x73, 0x66, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04,
	0x52, 0x0a, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x66, 0x65, 0x72, 0x49, 0x64, 0x12, 0x55, 0x0a, 0x07,
	0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x3b, 0x2e,
	0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x2e, 0x6d, 0x75, 0x6c, 0x74, 0x69,
	0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x69, 0x6e, 0x67, 0x2e, 0x6d, 0x75, 0x6c, 0x74, 0x69,
	0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x69, 0x6e, 0x67, 0x2e, 0x41, 0x63, 0x63, 0x6f, 0x75,
	0x6e, 0x74, 0x54, 0x65, 0x72, 0x6d, 0x69, 0x6e, 0x61, 0x6c, 0x52, 0x07, 0x61, 0x63, 0x63, 0x6f,
	0x75, 0x6e, 0x74, 0x12, 0x32, 0x0a, 0x15, 0x65, 0x78, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x5f,
	0x72, 0x65, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x13, 0x65, 0x78, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x52, 0x65, 0x66, 0x65,
	0x72, 0x65, 0x6e, 0x63, 0x65, 0x49, 0x64, 0x22, 0xc1, 0x01, 0x0a, 0x13, 0x52, 0x65, 0x64, 0x6f,
	0x54, 0x72, 0x61, 0x6e, 0x73, 0x66, 0x65, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12,
	0x1f, 0x0a, 0x0b, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x66, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x04, 0x52, 0x0a, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x66, 0x65, 0x72, 0x49, 0x64,
	0x12, 0x55, 0x0a, 0x07, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x3b, 0x2e, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x2e, 0x6d,
	0x75, 0x6c, 0x74, 0x69, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x69, 0x6e, 0x67, 0x2e, 0x6d,
	0x75, 0x6c, 0x74, 0x69, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x69, 0x6e, 0x67, 0x2e, 0x41,
	0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x54, 0x65, 0x72, 0x6d, 0x69, 0x6e, 0x61, 0x6c, 0x52, 0x07,
	0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x32, 0x0a, 0x15, 0x65, 0x78, 0x74, 0x65, 0x72,
	0x6e, 0x61, 0x6c, 0x5f, 0x72, 0x65, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x5f, 0x69, 0x64,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x13, 0x65, 0x78, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c,
	0x52, 0x65, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x49, 0x64, 0x22, 0xc7, 0x01, 0x0a, 0x19,
	0x47, 0x65, 0x74, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x66, 0x65, 0x72, 0x44, 0x65, 0x74, 0x61, 0x69,
	0x6c, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1f, 0x0a, 0x0b, 0x74, 0x72, 0x61,
	0x6e, 0x73, 0x66, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x0a,
	0x74, 0x72, 0x61, 0x6e, 0x73, 0x66, 0x65, 0x72, 0x49, 0x64, 0x12, 0x55, 0x0a, 0x07, 0x61, 0x63,
	0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x3b, 0x2e, 0x70, 0x72,
	0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x2e, 0x6d, 0x75, 0x6c, 0x74, 0x69, 0x61, 0x63,
	0x63, 0x6f, 0x75, 0x6e, 0x74, 0x69, 0x6e, 0x67, 0x2e, 0x6d, 0x75, 0x6c, 0x74, 0x69, 0x61, 0x63,
	0x63, 0x6f, 0x75, 0x6e, 0x74, 0x69, 0x6e, 0x67, 0x2e, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74,
	0x54, 0x65, 0x72, 0x6d, 0x69, 0x6e, 0x61, 0x6c, 0x52, 0x07, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e,
	0x74, 0x12, 0x32, 0x0a, 0x15, 0x65, 0x78, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x5f, 0x72, 0x65,
	0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x13, 0x65, 0x78, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x52, 0x65, 0x66, 0x65, 0x72, 0x65,
	0x6e, 0x63, 0x65, 0x49, 0x64, 0x22, 0xa6, 0x01, 0x0a, 0x17, 0x47, 0x65, 0x74, 0x54, 0x72, 0x61,
	0x6e, 0x73, 0x66, 0x65, 0x72, 0x73, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x12, 0x55, 0x0a, 0x07, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x3b, 0x2e, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x2e,
	0x6d, 0x75, 0x6c, 0x74, 0x69, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x69, 0x6e, 0x67, 0x2e,
	0x6d, 0x75, 0x6c, 0x74, 0x69, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x69, 0x6e, 0x67, 0x2e,
	0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x54, 0x65, 0x72, 0x6d, 0x69, 0x6e, 0x61, 0x6c, 0x52,
	0x07, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x1b, 0x0a, 0x09, 0x64, 0x61, 0x74, 0x65,
	0x5f, 0x66, 0x72, 0x6f, 0x6d, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x64, 0x61, 0x74,
	0x65, 0x46, 0x72, 0x6f, 0x6d, 0x12, 0x17, 0x0a, 0x07, 0x64, 0x61, 0x74, 0x65, 0x5f, 0x74, 0x6f,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x64, 0x61, 0x74, 0x65, 0x54, 0x6f, 0x22, 0x71,
	0x0a, 0x18, 0x47, 0x65, 0x74, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x42, 0x61, 0x6c, 0x61,
	0x6e, 0x63, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x55, 0x0a, 0x07, 0x61, 0x63,
	0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x3b, 0x2e, 0x70, 0x72,
	0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x2e, 0x6d, 0x75, 0x6c, 0x74, 0x69, 0x61, 0x63,
	0x63, 0x6f, 0x75, 0x6e, 0x74, 0x69, 0x6e, 0x67, 0x2e, 0x6d, 0x75, 0x6c, 0x74, 0x69, 0x61, 0x63,
	0x63, 0x6f, 0x75, 0x6e, 0x74, 0x69, 0x6e, 0x67, 0x2e, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74,
	0x54, 0x65, 0x72, 0x6d, 0x69, 0x6e, 0x61, 0x6c, 0x52, 0x07, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e,
	0x74, 0x22, 0x9b, 0x01, 0x0a, 0x1a, 0x47, 0x65, 0x74, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74,
	0x53, 0x74, 0x61, 0x74, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x12, 0x55, 0x0a, 0x07, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x3b, 0x2e, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x2e, 0x6d,
	0x75, 0x6c, 0x74, 0x69, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x69, 0x6e, 0x67, 0x2e, 0x6d,
	0x75, 0x6c, 0x74, 0x69, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x69, 0x6e, 0x67, 0x2e, 0x41,
	0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x54, 0x65, 0x72, 0x6d, 0x69, 0x6e, 0x61, 0x6c, 0x52, 0x07,
	0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x12, 0x0a, 0x04, 0x64, 0x61, 0x74, 0x65, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x64, 0x61, 0x74, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x70,
	0x61, 0x67, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x70, 0x61, 0x67, 0x65, 0x22,
	0x87, 0x05, 0x0a, 0x14, 0x4d, 0x61, 0x6b, 0x65, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x66, 0x65, 0x72,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x1f, 0x0a, 0x0b, 0x74, 0x72, 0x61, 0x6e,
	0x73, 0x66, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x0a, 0x74,
	0x72, 0x61, 0x6e, 0x73, 0x66, 0x65, 0x72, 0x49, 0x64, 0x12, 0x2c, 0x0a, 0x12, 0x74, 0x72, 0x61,
	0x6e, 0x73, 0x66, 0x65, 0x72, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x5f, 0x69, 0x64, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x04, 0x52, 0x10, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x66, 0x65, 0x72, 0x53,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x49, 0x64, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12,
	0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x05, 0x52, 0x02, 0x69, 0x64, 0x12,
	0x1d, 0x0a, 0x0a, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x05, 0x20,
	0x01, 0x28, 0x05, 0x52, 0x09, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x49, 0x64, 0x12, 0x16,
	0x0a, 0x06, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x06, 0x20, 0x01, 0x28, 0x01, 0x52, 0x06,
	0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x1a, 0x0a, 0x08, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e,
	0x63, 0x79, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e,
	0x63, 0x79, 0x12, 0x32, 0x0a, 0x15, 0x65, 0x78, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x5f, 0x72,
	0x65, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x08, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x13, 0x65, 0x78, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x52, 0x65, 0x66, 0x65, 0x72,
	0x65, 0x6e, 0x63, 0x65, 0x49, 0x64, 0x12, 0x27, 0x0a, 0x0f, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x66,
	0x65, 0x72, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0e, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x66, 0x65, 0x72, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12,
	0x30, 0x0a, 0x14, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x66, 0x65, 0x72, 0x5f, 0x73, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x12, 0x74,
	0x72, 0x61, 0x6e, 0x73, 0x66, 0x65, 0x72, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x4e, 0x61, 0x6d,
	0x65, 0x12, 0x23, 0x0a, 0x0d, 0x63, 0x72, 0x65, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x64, 0x61,
	0x74, 0x65, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x63, 0x72, 0x65, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x44, 0x61, 0x74, 0x65, 0x12, 0x25, 0x0a, 0x0e, 0x66, 0x69, 0x6e, 0x69, 0x73, 0x68,
	0x69, 0x6e, 0x67, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d,
	0x66, 0x69, 0x6e, 0x69, 0x73, 0x68, 0x69, 0x6e, 0x67, 0x44, 0x61, 0x74, 0x65, 0x12, 0x6b, 0x0a,
	0x12, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x70, 0x61, 0x72, 0x61, 0x6d, 0x65, 0x74,
	0x65, 0x72, 0x73, 0x18, 0x0d, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x3c, 0x2e, 0x70, 0x72, 0x6f, 0x63,
	0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x2e, 0x6d, 0x75, 0x6c, 0x74, 0x69, 0x61, 0x63, 0x63, 0x6f,
	0x75, 0x6e, 0x74, 0x69, 0x6e, 0x67, 0x2e, 0x6d, 0x75, 0x6c, 0x74, 0x69, 0x61, 0x63, 0x63, 0x6f,
	0x75, 0x6e, 0x74, 0x69, 0x6e, 0x67, 0x2e, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x50, 0x61,
	0x72, 0x61, 0x6d, 0x65, 0x74, 0x65, 0x72, 0x52, 0x11, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x50, 0x61, 0x72, 0x61, 0x6d, 0x65, 0x74, 0x65, 0x72, 0x73, 0x12, 0x10, 0x0a, 0x03, 0x72, 0x72,
	0x6e, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x72, 0x72, 0x6e, 0x12, 0x1b, 0x0a, 0x09,
	0x61, 0x75, 0x74, 0x68, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x08, 0x61, 0x75, 0x74, 0x68, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x61, 0x72,
	0x64, 0x18, 0x10, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x63, 0x61, 0x72, 0x64, 0x12, 0x1a, 0x0a,
	0x08, 0x74, 0x65, 0x72, 0x6d, 0x69, 0x6e, 0x61, 0x6c, 0x18, 0x11, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x08, 0x74, 0x65, 0x72, 0x6d, 0x69, 0x6e, 0x61, 0x6c, 0x22, 0x9b, 0x01, 0x0a, 0x19, 0x4d, 0x61,
	0x6b, 0x65, 0x4d, 0x65, 0x72, 0x63, 0x68, 0x61, 0x6e, 0x74, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12,
	0x66, 0x0a, 0x0d, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x5f, 0x64, 0x61, 0x74, 0x61,
	0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x41, 0x2e, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73,
	0x69, 0x6e, 0x67, 0x2e, 0x6d, 0x75, 0x6c, 0x74, 0x69, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74,
	0x69, 0x6e, 0x67, 0x2e, 0x6d, 0x75, 0x6c, 0x74, 0x69, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74,
	0x69, 0x6e, 0x67, 0x2e, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x50, 0x61, 0x72, 0x61, 0x6d,
	0x65, 0x74, 0x65, 0x72, 0x73, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x0c, 0x72, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x44, 0x61, 0x74, 0x61, 0x22, 0xf7, 0x04, 0x0a, 0x16, 0x41, 0x63, 0x63, 0x65,
	0x70, 0x74, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x66, 0x65, 0x72, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x12, 0x1f, 0x0a, 0x0b, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x66, 0x65, 0x72, 0x5f, 0x69,
	0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x0a, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x66, 0x65,
	0x72, 0x49, 0x64, 0x12, 0x2c, 0x0a, 0x12, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x66, 0x65, 0x72, 0x5f,
	0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x04, 0x52,
	0x10, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x66, 0x65, 0x72, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x49,
	0x64, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x05, 0x52, 0x02, 0x69, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x73, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x05, 0x52, 0x09, 0x73,
	0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x49, 0x64, 0x12, 0x16, 0x0a, 0x06, 0x61, 0x6d, 0x6f, 0x75,
	0x6e, 0x74, 0x18, 0x06, 0x20, 0x01, 0x28, 0x01, 0x52, 0x06, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74,
	0x12, 0x1a, 0x0a, 0x08, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x63, 0x79, 0x18, 0x07, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x08, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x63, 0x79, 0x12, 0x32, 0x0a, 0x15,
	0x65, 0x78, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x5f, 0x72, 0x65, 0x66, 0x65, 0x72, 0x65, 0x6e,
	0x63, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x13, 0x65, 0x78, 0x74,
	0x65, 0x72, 0x6e, 0x61, 0x6c, 0x52, 0x65, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x49, 0x64,
	0x12, 0x27, 0x0a, 0x0f, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x66, 0x65, 0x72, 0x5f, 0x73, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x74, 0x72, 0x61, 0x6e, 0x73,
	0x66, 0x65, 0x72, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x30, 0x0a, 0x14, 0x74, 0x72, 0x61,
	0x6e, 0x73, 0x66, 0x65, 0x72, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x5f, 0x6e, 0x61, 0x6d,
	0x65, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x12, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x66, 0x65,
	0x72, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x23, 0x0a, 0x0d, 0x63,
	0x72, 0x65, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x18, 0x0b, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0c, 0x63, 0x72, 0x65, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x44, 0x61, 0x74, 0x65,
	0x12, 0x25, 0x0a, 0x0e, 0x66, 0x69, 0x6e, 0x69, 0x73, 0x68, 0x69, 0x6e, 0x67, 0x5f, 0x64, 0x61,
	0x74, 0x65, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x66, 0x69, 0x6e, 0x69, 0x73, 0x68,
	0x69, 0x6e, 0x67, 0x44, 0x61, 0x74, 0x65, 0x12, 0x6b, 0x0a, 0x12, 0x73, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x5f, 0x70, 0x61, 0x72, 0x61, 0x6d, 0x65, 0x74, 0x65, 0x72, 0x73, 0x18, 0x0d, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x3c, 0x2e, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67,
	0x2e, 0x6d, 0x75, 0x6c, 0x74, 0x69, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x69, 0x6e, 0x67,
	0x2e, 0x6d, 0x75, 0x6c, 0x74, 0x69, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x69, 0x6e, 0x67,
	0x2e, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x65, 0x74, 0x65,
	0x72, 0x52, 0x11, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x65,
	0x74, 0x65, 0x72, 0x73, 0x12, 0x1b, 0x0a, 0x09, 0x61, 0x75, 0x74, 0x68, 0x5f, 0x63, 0x6f, 0x64,
	0x65, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x61, 0x75, 0x74, 0x68, 0x43, 0x6f, 0x64,
	0x65, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x61, 0x72, 0x64, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x04, 0x63, 0x61, 0x72, 0x64, 0x12, 0x1a, 0x0a, 0x08, 0x74, 0x65, 0x72, 0x6d, 0x69, 0x6e, 0x61,
	0x6c, 0x18, 0x10, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x74, 0x65, 0x72, 0x6d, 0x69, 0x6e, 0x61,
	0x6c, 0x22, 0xf8, 0x04, 0x0a, 0x17, 0x44, 0x65, 0x63, 0x6c, 0x69, 0x6e, 0x65, 0x54, 0x72, 0x61,
	0x6e, 0x73, 0x66, 0x65, 0x72, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x1f, 0x0a,
	0x0b, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x66, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x04, 0x52, 0x0a, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x66, 0x65, 0x72, 0x49, 0x64, 0x12, 0x2c,
	0x0a, 0x12, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x66, 0x65, 0x72, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x04, 0x52, 0x10, 0x74, 0x72, 0x61, 0x6e,
	0x73, 0x66, 0x65, 0x72, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x49, 0x64, 0x12, 0x16, 0x0a, 0x06,
	0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x73, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x05,
	0x52, 0x02, 0x69, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f,
	0x69, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x05, 0x52, 0x09, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x49, 0x64, 0x12, 0x16, 0x0a, 0x06, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x06, 0x20,
	0x01, 0x28, 0x01, 0x52, 0x06, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x1a, 0x0a, 0x08, 0x63,
	0x75, 0x72, 0x72, 0x65, 0x6e, 0x63, 0x79, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x63,
	0x75, 0x72, 0x72, 0x65, 0x6e, 0x63, 0x79, 0x12, 0x32, 0x0a, 0x15, 0x65, 0x78, 0x74, 0x65, 0x72,
	0x6e, 0x61, 0x6c, 0x5f, 0x72, 0x65, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x5f, 0x69, 0x64,
	0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x13, 0x65, 0x78, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c,
	0x52, 0x65, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x49, 0x64, 0x12, 0x27, 0x0a, 0x0f, 0x74,
	0x72, 0x61, 0x6e, 0x73, 0x66, 0x65, 0x72, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x09,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x66, 0x65, 0x72, 0x53, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x12, 0x30, 0x0a, 0x14, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x66, 0x65, 0x72,
	0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x0a, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x12, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x66, 0x65, 0x72, 0x53, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x23, 0x0a, 0x0d, 0x63, 0x72, 0x65, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x63,
	0x72, 0x65, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x44, 0x61, 0x74, 0x65, 0x12, 0x25, 0x0a, 0x0e, 0x66,
	0x69, 0x6e, 0x69, 0x73, 0x68, 0x69, 0x6e, 0x67, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x18, 0x0c, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0d, 0x66, 0x69, 0x6e, 0x69, 0x73, 0x68, 0x69, 0x6e, 0x67, 0x44, 0x61,
	0x74, 0x65, 0x12, 0x6b, 0x0a, 0x12, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x70, 0x61,
	0x72, 0x61, 0x6d, 0x65, 0x74, 0x65, 0x72, 0x73, 0x18, 0x0d, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x3c,
	0x2e, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x2e, 0x6d, 0x75, 0x6c, 0x74,
	0x69, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x69, 0x6e, 0x67, 0x2e, 0x6d, 0x75, 0x6c, 0x74,
	0x69, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x69, 0x6e, 0x67, 0x2e, 0x53, 0x65, 0x72, 0x76,
	0x69, 0x63, 0x65, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x65, 0x74, 0x65, 0x72, 0x52, 0x11, 0x73, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x65, 0x74, 0x65, 0x72, 0x73, 0x12,
	0x1b, 0x0a, 0x09, 0x61, 0x75, 0x74, 0x68, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x0e, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x08, 0x61, 0x75, 0x74, 0x68, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x12, 0x0a, 0x04,
	0x63, 0x61, 0x72, 0x64, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x63, 0x61, 0x72, 0x64,
	0x12, 0x1a, 0x0a, 0x08, 0x74, 0x65, 0x72, 0x6d, 0x69, 0x6e, 0x61, 0x6c, 0x18, 0x10, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x08, 0x74, 0x65, 0x72, 0x6d, 0x69, 0x6e, 0x61, 0x6c, 0x22, 0xf5, 0x04, 0x0a,
	0x14, 0x52, 0x65, 0x64, 0x6f, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x66, 0x65, 0x72, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x1f, 0x0a, 0x0b, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x66, 0x65,
	0x72, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x0a, 0x74, 0x72, 0x61, 0x6e,
	0x73, 0x66, 0x65, 0x72, 0x49, 0x64, 0x12, 0x2c, 0x0a, 0x12, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x66,
	0x65, 0x72, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x04, 0x52, 0x10, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x66, 0x65, 0x72, 0x53, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x49, 0x64, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x0e, 0x0a, 0x02,
	0x69, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x05, 0x52, 0x02, 0x69, 0x64, 0x12, 0x1d, 0x0a, 0x0a,
	0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x05,
	0x52, 0x09, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x49, 0x64, 0x12, 0x16, 0x0a, 0x06, 0x61,
	0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x06, 0x20, 0x01, 0x28, 0x01, 0x52, 0x06, 0x61, 0x6d, 0x6f,
	0x75, 0x6e, 0x74, 0x12, 0x1a, 0x0a, 0x08, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x63, 0x79, 0x18,
	0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x63, 0x79, 0x12,
	0x32, 0x0a, 0x15, 0x65, 0x78, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x5f, 0x72, 0x65, 0x66, 0x65,
	0x72, 0x65, 0x6e, 0x63, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x13,
	0x65, 0x78, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x52, 0x65, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63,
	0x65, 0x49, 0x64, 0x12, 0x27, 0x0a, 0x0f, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x66, 0x65, 0x72, 0x5f,
	0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x74, 0x72,
	0x61, 0x6e, 0x73, 0x66, 0x65, 0x72, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x30, 0x0a, 0x14,
	0x74, 0x72, 0x61, 0x6e, 0x73, 0x66, 0x65, 0x72, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x5f,
	0x6e, 0x61, 0x6d, 0x65, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x12, 0x74, 0x72, 0x61, 0x6e,
	0x73, 0x66, 0x65, 0x72, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x23,
	0x0a, 0x0d, 0x63, 0x72, 0x65, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x18,
	0x0b, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x63, 0x72, 0x65, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x44,
	0x61, 0x74, 0x65, 0x12, 0x25, 0x0a, 0x0e, 0x66, 0x69, 0x6e, 0x69, 0x73, 0x68, 0x69, 0x6e, 0x67,
	0x5f, 0x64, 0x61, 0x74, 0x65, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x66, 0x69, 0x6e,
	0x69, 0x73, 0x68, 0x69, 0x6e, 0x67, 0x44, 0x61, 0x74, 0x65, 0x12, 0x6b, 0x0a, 0x12, 0x73, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x70, 0x61, 0x72, 0x61, 0x6d, 0x65, 0x74, 0x65, 0x72, 0x73,
	0x18, 0x0d, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x3c, 0x2e, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73,
	0x69, 0x6e, 0x67, 0x2e, 0x6d, 0x75, 0x6c, 0x74, 0x69, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74,
	0x69, 0x6e, 0x67, 0x2e, 0x6d, 0x75, 0x6c, 0x74, 0x69, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74,
	0x69, 0x6e, 0x67, 0x2e, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x50, 0x61, 0x72, 0x61, 0x6d,
	0x65, 0x74, 0x65, 0x72, 0x52, 0x11, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x50, 0x61, 0x72,
	0x61, 0x6d, 0x65, 0x74, 0x65, 0x72, 0x73, 0x12, 0x1b, 0x0a, 0x09, 0x61, 0x75, 0x74, 0x68, 0x5f,
	0x63, 0x6f, 0x64, 0x65, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x61, 0x75, 0x74, 0x68,
	0x43, 0x6f, 0x64, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x61, 0x72, 0x64, 0x18, 0x0f, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x04, 0x63, 0x61, 0x72, 0x64, 0x12, 0x1a, 0x0a, 0x08, 0x74, 0x65, 0x72, 0x6d,
	0x69, 0x6e, 0x61, 0x6c, 0x18, 0x10, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x74, 0x65, 0x72, 0x6d,
	0x69, 0x6e, 0x61, 0x6c, 0x22, 0xfb, 0x04, 0x0a, 0x1a, 0x47, 0x65, 0x74, 0x54, 0x72, 0x61, 0x6e,
	0x73, 0x66, 0x65, 0x72, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x12, 0x1f, 0x0a, 0x0b, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x66, 0x65, 0x72, 0x5f,
	0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x0a, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x66,
	0x65, 0x72, 0x49, 0x64, 0x12, 0x2c, 0x0a, 0x12, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x66, 0x65, 0x72,
	0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x04,
	0x52, 0x10, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x66, 0x65, 0x72, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x49, 0x64, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x05, 0x52, 0x02, 0x69, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x73, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x05, 0x52, 0x09,
	0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x49, 0x64, 0x12, 0x16, 0x0a, 0x06, 0x61, 0x6d, 0x6f,
	0x75, 0x6e, 0x74, 0x18, 0x06, 0x20, 0x01, 0x28, 0x01, 0x52, 0x06, 0x61, 0x6d, 0x6f, 0x75, 0x6e,
	0x74, 0x12, 0x1a, 0x0a, 0x08, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x63, 0x79, 0x18, 0x07, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x08, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x63, 0x79, 0x12, 0x32, 0x0a,
	0x15, 0x65, 0x78, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x5f, 0x72, 0x65, 0x66, 0x65, 0x72, 0x65,
	0x6e, 0x63, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x13, 0x65, 0x78,
	0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x52, 0x65, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x49,
	0x64, 0x12, 0x27, 0x0a, 0x0f, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x66, 0x65, 0x72, 0x5f, 0x73, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x74, 0x72, 0x61, 0x6e,
	0x73, 0x66, 0x65, 0x72, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x30, 0x0a, 0x14, 0x74, 0x72,
	0x61, 0x6e, 0x73, 0x66, 0x65, 0x72, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x5f, 0x6e, 0x61,
	0x6d, 0x65, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x12, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x66,
	0x65, 0x72, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x23, 0x0a, 0x0d,
	0x63, 0x72, 0x65, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x18, 0x0b, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0c, 0x63, 0x72, 0x65, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x44, 0x61, 0x74,
	0x65, 0x12, 0x25, 0x0a, 0x0e, 0x66, 0x69, 0x6e, 0x69, 0x73, 0x68, 0x69, 0x6e, 0x67, 0x5f, 0x64,
	0x61, 0x74, 0x65, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x66, 0x69, 0x6e, 0x69, 0x73,
	0x68, 0x69, 0x6e, 0x67, 0x44, 0x61, 0x74, 0x65, 0x12, 0x6b, 0x0a, 0x12, 0x73, 0x65, 0x72, 0x76,
	0x69, 0x63, 0x65, 0x5f, 0x70, 0x61, 0x72, 0x61, 0x6d, 0x65, 0x74, 0x65, 0x72, 0x73, 0x18, 0x0d,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x3c, 0x2e, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e,
	0x67, 0x2e, 0x6d, 0x75, 0x6c, 0x74, 0x69, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x69, 0x6e,
	0x67, 0x2e, 0x6d, 0x75, 0x6c, 0x74, 0x69, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x69, 0x6e,
	0x67, 0x2e, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x65, 0x74,
	0x65, 0x72, 0x52, 0x11, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x50, 0x61, 0x72, 0x61, 0x6d,
	0x65, 0x74, 0x65, 0x72, 0x73, 0x12, 0x1b, 0x0a, 0x09, 0x61, 0x75, 0x74, 0x68, 0x5f, 0x63, 0x6f,
	0x64, 0x65, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x61, 0x75, 0x74, 0x68, 0x43, 0x6f,
	0x64, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x61, 0x72, 0x64, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x04, 0x63, 0x61, 0x72, 0x64, 0x12, 0x1a, 0x0a, 0x08, 0x74, 0x65, 0x72, 0x6d, 0x69, 0x6e,
	0x61, 0x6c, 0x18, 0x10, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x74, 0x65, 0x72, 0x6d, 0x69, 0x6e,
	0x61, 0x6c, 0x22, 0x8a, 0x01, 0x0a, 0x18, 0x47, 0x65, 0x74, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x66,
	0x65, 0x72, 0x73, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12,
	0x16, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x56, 0x0a, 0x04, 0x64, 0x61, 0x74, 0x61, 0x18,
	0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x42, 0x2e, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69,
	0x6e, 0x67, 0x2e, 0x6d, 0x75, 0x6c, 0x74, 0x69, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x69,
	0x6e, 0x67, 0x2e, 0x6d, 0x75, 0x6c, 0x74, 0x69, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x69,
	0x6e, 0x67, 0x2e, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x69, 0x74, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e,
	0x74, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x66, 0x65, 0x72, 0x52, 0x04, 0x64, 0x61, 0x74, 0x61, 0x22,
	0x89, 0x01, 0x0a, 0x19, 0x47, 0x65, 0x74, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x42, 0x61,
	0x6c, 0x61, 0x6e, 0x63, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x16, 0x0a,
	0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x73,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x18, 0x0a, 0x07, 0x62, 0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x01, 0x52, 0x07, 0x62, 0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65, 0x12,
	0x1c, 0x0a, 0x09, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x65, 0x64, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x01, 0x52, 0x09, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x65, 0x64, 0x12, 0x1c, 0x0a,
	0x09, 0x74, 0x68, 0x72, 0x65, 0x73, 0x68, 0x6f, 0x6c, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x01,
	0x52, 0x09, 0x74, 0x68, 0x72, 0x65, 0x73, 0x68, 0x6f, 0x6c, 0x64, 0x22, 0x8f, 0x02, 0x0a, 0x1b,
	0x47, 0x65, 0x74, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x53, 0x74, 0x61, 0x74, 0x65, 0x6d,
	0x65, 0x6e, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x73,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x73, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x12, 0x1d, 0x0a, 0x0a, 0x69, 0x6e, 0x5f, 0x62, 0x61, 0x6c, 0x61, 0x6e, 0x63,
	0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x01, 0x52, 0x09, 0x69, 0x6e, 0x42, 0x61, 0x6c, 0x61, 0x6e,
	0x63, 0x65, 0x12, 0x1f, 0x0a, 0x0b, 0x6f, 0x75, 0x74, 0x5f, 0x62, 0x61, 0x6c, 0x61, 0x6e, 0x63,
	0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x01, 0x52, 0x0a, 0x6f, 0x75, 0x74, 0x42, 0x61, 0x6c, 0x61,
	0x6e, 0x63, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x70, 0x61, 0x67, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x05, 0x52, 0x04, 0x70, 0x61, 0x67, 0x65, 0x12, 0x1f, 0x0a, 0x0b, 0x70, 0x61, 0x67, 0x65, 0x73,
	0x5f, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x05, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0a, 0x70, 0x61,
	0x67, 0x65, 0x73, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x63, 0x0a, 0x0a, 0x6f, 0x70, 0x65, 0x72,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x06, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x43, 0x2e, 0x70,
	0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x2e, 0x6d, 0x75, 0x6c, 0x74, 0x69, 0x61,
	0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x69, 0x6e, 0x67, 0x2e, 0x6d, 0x75, 0x6c, 0x74, 0x69, 0x61,
	0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x69, 0x6e, 0x67, 0x2e, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x69,
	0x74, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x4f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x52, 0x0a, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x32, 0xb3, 0x0c,
	0x0a, 0x0f, 0x4d, 0x75, 0x6c, 0x74, 0x69, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x69, 0x6e,
	0x67, 0x12, 0xa2, 0x01, 0x0a, 0x11, 0x4d, 0x61, 0x6b, 0x65, 0x4d, 0x65, 0x72, 0x63, 0x68, 0x61,
	0x6e, 0x74, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x12, 0x44, 0x2e, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73,
	0x73, 0x69, 0x6e, 0x67, 0x2e, 0x6d, 0x75, 0x6c, 0x74, 0x69, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e,
	0x74, 0x69, 0x6e, 0x67, 0x2e, 0x6d, 0x75, 0x6c, 0x74, 0x69, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e,
	0x74, 0x69, 0x6e, 0x67, 0x2e, 0x4d, 0x61, 0x6b, 0x65, 0x4d, 0x65, 0x72, 0x63, 0x68, 0x61, 0x6e,
	0x74, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x45, 0x2e,
	0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x2e, 0x6d, 0x75, 0x6c, 0x74, 0x69,
	0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x69, 0x6e, 0x67, 0x2e, 0x6d, 0x75, 0x6c, 0x74, 0x69,
	0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x69, 0x6e, 0x67, 0x2e, 0x4d, 0x61, 0x6b, 0x65, 0x4d,
	0x65, 0x72, 0x63, 0x68, 0x61, 0x6e, 0x74, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x93, 0x01, 0x0a, 0x0c, 0x4d, 0x61, 0x6b, 0x65, 0x54,
	0x72, 0x61, 0x6e, 0x73, 0x66, 0x65, 0x72, 0x12, 0x3f, 0x2e, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73,
	0x73, 0x69, 0x6e, 0x67, 0x2e, 0x6d, 0x75, 0x6c, 0x74, 0x69, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e,
	0x74, 0x69, 0x6e, 0x67, 0x2e, 0x6d, 0x75, 0x6c, 0x74, 0x69, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e,
	0x74, 0x69, 0x6e, 0x67, 0x2e, 0x4d, 0x61, 0x6b, 0x65, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x66, 0x65,
	0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x40, 0x2e, 0x70, 0x72, 0x6f, 0x63, 0x65,
	0x73, 0x73, 0x69, 0x6e, 0x67, 0x2e, 0x6d, 0x75, 0x6c, 0x74, 0x69, 0x61, 0x63, 0x63, 0x6f, 0x75,
	0x6e, 0x74, 0x69, 0x6e, 0x67, 0x2e, 0x6d, 0x75, 0x6c, 0x74, 0x69, 0x61, 0x63, 0x63, 0x6f, 0x75,
	0x6e, 0x74, 0x69, 0x6e, 0x67, 0x2e, 0x4d, 0x61, 0x6b, 0x65, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x66,
	0x65, 0x72, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x99, 0x01, 0x0a,
	0x0e, 0x41, 0x63, 0x63, 0x65, 0x70, 0x74, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x66, 0x65, 0x72, 0x12,
	0x41, 0x2e, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x2e, 0x6d, 0x75, 0x6c,
	0x74, 0x69, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x69, 0x6e, 0x67, 0x2e, 0x6d, 0x75, 0x6c,
	0x74, 0x69, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x69, 0x6e, 0x67, 0x2e, 0x41, 0x63, 0x63,
	0x65, 0x70, 0x74, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x66, 0x65, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x1a, 0x42, 0x2e, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x2e,
	0x6d, 0x75, 0x6c, 0x74, 0x69, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x69, 0x6e, 0x67, 0x2e,
	0x6d, 0x75, 0x6c, 0x74, 0x69, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x69, 0x6e, 0x67, 0x2e,
	0x41, 0x63, 0x63, 0x65, 0x70, 0x74, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x66, 0x65, 0x72, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x9c, 0x01, 0x0a, 0x0f, 0x44, 0x65, 0x63,
	0x6c, 0x69, 0x6e, 0x65, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x66, 0x65, 0x72, 0x12, 0x42, 0x2e, 0x70,
	0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x2e, 0x6d, 0x75, 0x6c, 0x74, 0x69, 0x61,
	0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x69, 0x6e, 0x67, 0x2e, 0x6d, 0x75, 0x6c, 0x74, 0x69, 0x61,
	0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x69, 0x6e, 0x67, 0x2e, 0x44, 0x65, 0x63, 0x6c, 0x69, 0x6e,
	0x65, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x66, 0x65, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x1a, 0x43, 0x2e, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x2e, 0x6d, 0x75,
	0x6c, 0x74, 0x69, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x69, 0x6e, 0x67, 0x2e, 0x6d, 0x75,
	0x6c, 0x74, 0x69, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x69, 0x6e, 0x67, 0x2e, 0x44, 0x65,
	0x63, 0x6c, 0x69, 0x6e, 0x65, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x66, 0x65, 0x72, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x93, 0x01, 0x0a, 0x0c, 0x52, 0x65, 0x64, 0x6f,
	0x54, 0x72, 0x61, 0x6e, 0x73, 0x66, 0x65, 0x72, 0x12, 0x3f, 0x2e, 0x70, 0x72, 0x6f, 0x63, 0x65,
	0x73, 0x73, 0x69, 0x6e, 0x67, 0x2e, 0x6d, 0x75, 0x6c, 0x74, 0x69, 0x61, 0x63, 0x63, 0x6f, 0x75,
	0x6e, 0x74, 0x69, 0x6e, 0x67, 0x2e, 0x6d, 0x75, 0x6c, 0x74, 0x69, 0x61, 0x63, 0x63, 0x6f, 0x75,
	0x6e, 0x74, 0x69, 0x6e, 0x67, 0x2e, 0x52, 0x65, 0x64, 0x6f, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x66,
	0x65, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x40, 0x2e, 0x70, 0x72, 0x6f, 0x63,
	0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x2e, 0x6d, 0x75, 0x6c, 0x74, 0x69, 0x61, 0x63, 0x63, 0x6f,
	0x75, 0x6e, 0x74, 0x69, 0x6e, 0x67, 0x2e, 0x6d, 0x75, 0x6c, 0x74, 0x69, 0x61, 0x63, 0x63, 0x6f,
	0x75, 0x6e, 0x74, 0x69, 0x6e, 0x67, 0x2e, 0x52, 0x65, 0x64, 0x6f, 0x54, 0x72, 0x61, 0x6e, 0x73,
	0x66, 0x65, 0x72, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0xa5, 0x01,
	0x0a, 0x12, 0x47, 0x65, 0x74, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x66, 0x65, 0x72, 0x44, 0x65, 0x74,
	0x61, 0x69, 0x6c, 0x73, 0x12, 0x45, 0x2e, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e,
	0x67, 0x2e, 0x6d, 0x75, 0x6c, 0x74, 0x69, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x69, 0x6e,
	0x67, 0x2e, 0x6d, 0x75, 0x6c, 0x74, 0x69, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x69, 0x6e,
	0x67, 0x2e, 0x47, 0x65, 0x74, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x66, 0x65, 0x72, 0x44, 0x65, 0x74,
	0x61, 0x69, 0x6c, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x46, 0x2e, 0x70, 0x72,
	0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x2e, 0x6d, 0x75, 0x6c, 0x74, 0x69, 0x61, 0x63,
	0x63, 0x6f, 0x75, 0x6e, 0x74, 0x69, 0x6e, 0x67, 0x2e, 0x6d, 0x75, 0x6c, 0x74, 0x69, 0x61, 0x63,
	0x63, 0x6f, 0x75, 0x6e, 0x74, 0x69, 0x6e, 0x67, 0x2e, 0x47, 0x65, 0x74, 0x54, 0x72, 0x61, 0x6e,
	0x73, 0x66, 0x65, 0x72, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x9f, 0x01, 0x0a, 0x10, 0x47, 0x65, 0x74, 0x54, 0x72, 0x61,
	0x6e, 0x73, 0x66, 0x65, 0x72, 0x73, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x43, 0x2e, 0x70, 0x72, 0x6f,
	0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x2e, 0x6d, 0x75, 0x6c, 0x74, 0x69, 0x61, 0x63, 0x63,
	0x6f, 0x75, 0x6e, 0x74, 0x69, 0x6e, 0x67, 0x2e, 0x6d, 0x75, 0x6c, 0x74, 0x69, 0x61, 0x63, 0x63,
	0x6f, 0x75, 0x6e, 0x74, 0x69, 0x6e, 0x67, 0x2e, 0x47, 0x65, 0x74, 0x54, 0x72, 0x61, 0x6e, 0x73,
	0x66, 0x65, 0x72, 0x73, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a,
	0x44, 0x2e, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x2e, 0x6d, 0x75, 0x6c,
	0x74, 0x69, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x69, 0x6e, 0x67, 0x2e, 0x6d, 0x75, 0x6c,
	0x74, 0x69, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x69, 0x6e, 0x67, 0x2e, 0x47, 0x65, 0x74,
	0x54, 0x72, 0x61, 0x6e, 0x73, 0x66, 0x65, 0x72, 0x73, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0xa2, 0x01, 0x0a, 0x11, 0x47, 0x65, 0x74, 0x41,
	0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x42, 0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65, 0x12, 0x44, 0x2e,
	0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x2e, 0x6d, 0x75, 0x6c, 0x74, 0x69,
	0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x69, 0x6e, 0x67, 0x2e, 0x6d, 0x75, 0x6c, 0x74, 0x69,
	0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x69, 0x6e, 0x67, 0x2e, 0x47, 0x65, 0x74, 0x41, 0x63,
	0x63, 0x6f, 0x75, 0x6e, 0x74, 0x42, 0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x1a, 0x45, 0x2e, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67,
	0x2e, 0x6d, 0x75, 0x6c, 0x74, 0x69, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x69, 0x6e, 0x67,
	0x2e, 0x6d, 0x75, 0x6c, 0x74, 0x69, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x69, 0x6e, 0x67,
	0x2e, 0x47, 0x65, 0x74, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x42, 0x61, 0x6c, 0x61, 0x6e,
	0x63, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0xa8, 0x01, 0x0a,
	0x13, 0x47, 0x65, 0x74, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x53, 0x74, 0x61, 0x74, 0x65,
	0x6d, 0x65, 0x6e, 0x74, 0x12, 0x46, 0x2e, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e,
	0x67, 0x2e, 0x6d, 0x75, 0x6c, 0x74, 0x69, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x69, 0x6e,
	0x67, 0x2e, 0x6d, 0x75, 0x6c, 0x74, 0x69, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x69, 0x6e,
	0x67, 0x2e, 0x47, 0x65, 0x74, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x53, 0x74, 0x61, 0x74,
	0x65, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x47, 0x2e, 0x70,
	0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x2e, 0x6d, 0x75, 0x6c, 0x74, 0x69, 0x61,
	0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x69, 0x6e, 0x67, 0x2e, 0x6d, 0x75, 0x6c, 0x74, 0x69, 0x61,
	0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x69, 0x6e, 0x67, 0x2e, 0x47, 0x65, 0x74, 0x41, 0x63, 0x63,
	0x6f, 0x75, 0x6e, 0x74, 0x53, 0x74, 0x61, 0x74, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x7a, 0x0a, 0x14, 0x47, 0x65, 0x74, 0x41, 0x63,
	0x63, 0x6f, 0x75, 0x6e, 0x74, 0x49, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x66, 0x69, 0x65, 0x72, 0x12,
	0x16, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75,
	0x66, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x1a, 0x48, 0x2e, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73,
	0x73, 0x69, 0x6e, 0x67, 0x2e, 0x6d, 0x75, 0x6c, 0x74, 0x69, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e,
	0x74, 0x69, 0x6e, 0x67, 0x2e, 0x6d, 0x75, 0x6c, 0x74, 0x69, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e,
	0x74, 0x69, 0x6e, 0x67, 0x2e, 0x47, 0x65, 0x74, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x49,
	0x64, 0x65, 0x6e, 0x74, 0x69, 0x66, 0x69, 0x65, 0x72, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x22, 0x00, 0x42, 0x2d, 0x5a, 0x2b, 0x67, 0x69, 0x74, 0x2e, 0x6c, 0x6f, 0x63, 0x61, 0x6c,
	0x2f, 0x73, 0x65, 0x6e, 0x73, 0x69, 0x74, 0x69, 0x76, 0x65, 0x2f, 0x69, 0x6e, 0x6e, 0x65, 0x72,
	0x70, 0x62, 0x2f, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x2f, 0x67, 0x72,
	0x70, 0x63, 0x62, 0x08, 0x65, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x70, 0xe8, 0x07,
})

var (
	file_inner_processing_grpc_multiaccounting_proto_rawDescOnce sync.Once
	file_inner_processing_grpc_multiaccounting_proto_rawDescData []byte
)

func file_inner_processing_grpc_multiaccounting_proto_rawDescGZIP() []byte {
	file_inner_processing_grpc_multiaccounting_proto_rawDescOnce.Do(func() {
		file_inner_processing_grpc_multiaccounting_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_inner_processing_grpc_multiaccounting_proto_rawDesc), len(file_inner_processing_grpc_multiaccounting_proto_rawDesc)))
	})
	return file_inner_processing_grpc_multiaccounting_proto_rawDescData
}

var file_inner_processing_grpc_multiaccounting_proto_msgTypes = make([]protoimpl.MessageInfo, 24)
var file_inner_processing_grpc_multiaccounting_proto_goTypes = []any{
	(*AccountTerminal)(nil),              // 0: processing.multiaccounting.multiaccounting.AccountTerminal
	(*ServiceParametersList)(nil),        // 1: processing.multiaccounting.multiaccounting.ServiceParametersList
	(*GetAccountIdentifierResponse)(nil), // 2: processing.multiaccounting.multiaccounting.GetAccountIdentifierResponse
	(*ServiceParameter)(nil),             // 3: processing.multiaccounting.multiaccounting.ServiceParameter
	(*TransitAccountTransfer)(nil),       // 4: processing.multiaccounting.multiaccounting.TransitAccountTransfer
	(*TransitAccountOperation)(nil),      // 5: processing.multiaccounting.multiaccounting.TransitAccountOperation
	(*MakeTransferRequest)(nil),          // 6: processing.multiaccounting.multiaccounting.MakeTransferRequest
	(*MakeMerchantCheckRequest)(nil),     // 7: processing.multiaccounting.multiaccounting.MakeMerchantCheckRequest
	(*AcceptTransferRequest)(nil),        // 8: processing.multiaccounting.multiaccounting.AcceptTransferRequest
	(*DeclineTransferRequest)(nil),       // 9: processing.multiaccounting.multiaccounting.DeclineTransferRequest
	(*RedoTransferRequest)(nil),          // 10: processing.multiaccounting.multiaccounting.RedoTransferRequest
	(*GetTransferDetailsRequest)(nil),    // 11: processing.multiaccounting.multiaccounting.GetTransferDetailsRequest
	(*GetTransfersListRequest)(nil),      // 12: processing.multiaccounting.multiaccounting.GetTransfersListRequest
	(*GetAccountBalanceRequest)(nil),     // 13: processing.multiaccounting.multiaccounting.GetAccountBalanceRequest
	(*GetAccountStatementRequest)(nil),   // 14: processing.multiaccounting.multiaccounting.GetAccountStatementRequest
	(*MakeTransferResponse)(nil),         // 15: processing.multiaccounting.multiaccounting.MakeTransferResponse
	(*MakeMerchantCheckResponse)(nil),    // 16: processing.multiaccounting.multiaccounting.MakeMerchantCheckResponse
	(*AcceptTransferResponse)(nil),       // 17: processing.multiaccounting.multiaccounting.AcceptTransferResponse
	(*DeclineTransferResponse)(nil),      // 18: processing.multiaccounting.multiaccounting.DeclineTransferResponse
	(*RedoTransferResponse)(nil),         // 19: processing.multiaccounting.multiaccounting.RedoTransferResponse
	(*GetTransferDetailsResponse)(nil),   // 20: processing.multiaccounting.multiaccounting.GetTransferDetailsResponse
	(*GetTransfersListResponse)(nil),     // 21: processing.multiaccounting.multiaccounting.GetTransfersListResponse
	(*GetAccountBalanceResponse)(nil),    // 22: processing.multiaccounting.multiaccounting.GetAccountBalanceResponse
	(*GetAccountStatementResponse)(nil),  // 23: processing.multiaccounting.multiaccounting.GetAccountStatementResponse
	(*structpb.Struct)(nil),              // 24: google.protobuf.Struct
	(*emptypb.Empty)(nil),                // 25: google.protobuf.Empty
}
var file_inner_processing_grpc_multiaccounting_proto_depIdxs = []int32{
	24, // 0: processing.multiaccounting.multiaccounting.AccountTerminal.config:type_name -> google.protobuf.Struct
	3,  // 1: processing.multiaccounting.multiaccounting.ServiceParametersList.service_params_list:type_name -> processing.multiaccounting.multiaccounting.ServiceParameter
	3,  // 2: processing.multiaccounting.multiaccounting.TransitAccountTransfer.service_parameters:type_name -> processing.multiaccounting.multiaccounting.ServiceParameter
	0,  // 3: processing.multiaccounting.multiaccounting.MakeTransferRequest.account:type_name -> processing.multiaccounting.multiaccounting.AccountTerminal
	3,  // 4: processing.multiaccounting.multiaccounting.MakeTransferRequest.service_parameters:type_name -> processing.multiaccounting.multiaccounting.ServiceParameter
	0,  // 5: processing.multiaccounting.multiaccounting.MakeMerchantCheckRequest.account:type_name -> processing.multiaccounting.multiaccounting.AccountTerminal
	0,  // 6: processing.multiaccounting.multiaccounting.AcceptTransferRequest.account:type_name -> processing.multiaccounting.multiaccounting.AccountTerminal
	0,  // 7: processing.multiaccounting.multiaccounting.DeclineTransferRequest.account:type_name -> processing.multiaccounting.multiaccounting.AccountTerminal
	0,  // 8: processing.multiaccounting.multiaccounting.RedoTransferRequest.account:type_name -> processing.multiaccounting.multiaccounting.AccountTerminal
	0,  // 9: processing.multiaccounting.multiaccounting.GetTransferDetailsRequest.account:type_name -> processing.multiaccounting.multiaccounting.AccountTerminal
	0,  // 10: processing.multiaccounting.multiaccounting.GetTransfersListRequest.account:type_name -> processing.multiaccounting.multiaccounting.AccountTerminal
	0,  // 11: processing.multiaccounting.multiaccounting.GetAccountBalanceRequest.account:type_name -> processing.multiaccounting.multiaccounting.AccountTerminal
	0,  // 12: processing.multiaccounting.multiaccounting.GetAccountStatementRequest.account:type_name -> processing.multiaccounting.multiaccounting.AccountTerminal
	3,  // 13: processing.multiaccounting.multiaccounting.MakeTransferResponse.service_parameters:type_name -> processing.multiaccounting.multiaccounting.ServiceParameter
	1,  // 14: processing.multiaccounting.multiaccounting.MakeMerchantCheckResponse.response_data:type_name -> processing.multiaccounting.multiaccounting.ServiceParametersList
	3,  // 15: processing.multiaccounting.multiaccounting.AcceptTransferResponse.service_parameters:type_name -> processing.multiaccounting.multiaccounting.ServiceParameter
	3,  // 16: processing.multiaccounting.multiaccounting.DeclineTransferResponse.service_parameters:type_name -> processing.multiaccounting.multiaccounting.ServiceParameter
	3,  // 17: processing.multiaccounting.multiaccounting.RedoTransferResponse.service_parameters:type_name -> processing.multiaccounting.multiaccounting.ServiceParameter
	3,  // 18: processing.multiaccounting.multiaccounting.GetTransferDetailsResponse.service_parameters:type_name -> processing.multiaccounting.multiaccounting.ServiceParameter
	4,  // 19: processing.multiaccounting.multiaccounting.GetTransfersListResponse.data:type_name -> processing.multiaccounting.multiaccounting.TransitAccountTransfer
	5,  // 20: processing.multiaccounting.multiaccounting.GetAccountStatementResponse.operations:type_name -> processing.multiaccounting.multiaccounting.TransitAccountOperation
	7,  // 21: processing.multiaccounting.multiaccounting.Multiaccounting.MakeMerchantCheck:input_type -> processing.multiaccounting.multiaccounting.MakeMerchantCheckRequest
	6,  // 22: processing.multiaccounting.multiaccounting.Multiaccounting.MakeTransfer:input_type -> processing.multiaccounting.multiaccounting.MakeTransferRequest
	8,  // 23: processing.multiaccounting.multiaccounting.Multiaccounting.AcceptTransfer:input_type -> processing.multiaccounting.multiaccounting.AcceptTransferRequest
	9,  // 24: processing.multiaccounting.multiaccounting.Multiaccounting.DeclineTransfer:input_type -> processing.multiaccounting.multiaccounting.DeclineTransferRequest
	10, // 25: processing.multiaccounting.multiaccounting.Multiaccounting.RedoTransfer:input_type -> processing.multiaccounting.multiaccounting.RedoTransferRequest
	11, // 26: processing.multiaccounting.multiaccounting.Multiaccounting.GetTransferDetails:input_type -> processing.multiaccounting.multiaccounting.GetTransferDetailsRequest
	12, // 27: processing.multiaccounting.multiaccounting.Multiaccounting.GetTransfersList:input_type -> processing.multiaccounting.multiaccounting.GetTransfersListRequest
	13, // 28: processing.multiaccounting.multiaccounting.Multiaccounting.GetAccountBalance:input_type -> processing.multiaccounting.multiaccounting.GetAccountBalanceRequest
	14, // 29: processing.multiaccounting.multiaccounting.Multiaccounting.GetAccountStatement:input_type -> processing.multiaccounting.multiaccounting.GetAccountStatementRequest
	25, // 30: processing.multiaccounting.multiaccounting.Multiaccounting.GetAccountIdentifier:input_type -> google.protobuf.Empty
	16, // 31: processing.multiaccounting.multiaccounting.Multiaccounting.MakeMerchantCheck:output_type -> processing.multiaccounting.multiaccounting.MakeMerchantCheckResponse
	15, // 32: processing.multiaccounting.multiaccounting.Multiaccounting.MakeTransfer:output_type -> processing.multiaccounting.multiaccounting.MakeTransferResponse
	17, // 33: processing.multiaccounting.multiaccounting.Multiaccounting.AcceptTransfer:output_type -> processing.multiaccounting.multiaccounting.AcceptTransferResponse
	18, // 34: processing.multiaccounting.multiaccounting.Multiaccounting.DeclineTransfer:output_type -> processing.multiaccounting.multiaccounting.DeclineTransferResponse
	19, // 35: processing.multiaccounting.multiaccounting.Multiaccounting.RedoTransfer:output_type -> processing.multiaccounting.multiaccounting.RedoTransferResponse
	20, // 36: processing.multiaccounting.multiaccounting.Multiaccounting.GetTransferDetails:output_type -> processing.multiaccounting.multiaccounting.GetTransferDetailsResponse
	21, // 37: processing.multiaccounting.multiaccounting.Multiaccounting.GetTransfersList:output_type -> processing.multiaccounting.multiaccounting.GetTransfersListResponse
	22, // 38: processing.multiaccounting.multiaccounting.Multiaccounting.GetAccountBalance:output_type -> processing.multiaccounting.multiaccounting.GetAccountBalanceResponse
	23, // 39: processing.multiaccounting.multiaccounting.Multiaccounting.GetAccountStatement:output_type -> processing.multiaccounting.multiaccounting.GetAccountStatementResponse
	2,  // 40: processing.multiaccounting.multiaccounting.Multiaccounting.GetAccountIdentifier:output_type -> processing.multiaccounting.multiaccounting.GetAccountIdentifierResponse
	31, // [31:41] is the sub-list for method output_type
	21, // [21:31] is the sub-list for method input_type
	21, // [21:21] is the sub-list for extension type_name
	21, // [21:21] is the sub-list for extension extendee
	0,  // [0:21] is the sub-list for field type_name
}

func init() { file_inner_processing_grpc_multiaccounting_proto_init() }
func file_inner_processing_grpc_multiaccounting_proto_init() {
	if File_inner_processing_grpc_multiaccounting_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_inner_processing_grpc_multiaccounting_proto_rawDesc), len(file_inner_processing_grpc_multiaccounting_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   24,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_inner_processing_grpc_multiaccounting_proto_goTypes,
		DependencyIndexes: file_inner_processing_grpc_multiaccounting_proto_depIdxs,
		MessageInfos:      file_inner_processing_grpc_multiaccounting_proto_msgTypes,
	}.Build()
	File_inner_processing_grpc_multiaccounting_proto = out.File
	file_inner_processing_grpc_multiaccounting_proto_goTypes = nil
	file_inner_processing_grpc_multiaccounting_proto_depIdxs = nil
}
