// Code generated by protoc-gen-go-mvp. DO NOT EDIT.
// versions:
// - protoc-gen-go-mvp v2.0.1
// created by mvp authors

package grpc

import (
	context "context"
	fmt "fmt"
	cntx "git.local/sensitive/mvp/pkg/cntx"
	ctxzap "github.com/grpc-ecosystem/go-grpc-middleware/logging/zap/ctxzap"
	zap "go.uber.org/zap"
	grpc "google.golang.org/grpc"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
)

func file_inner_processing_grpc_transaction_refund_proto_message_TimestampToZap(
	label string,
	in *timestamppb.Timestamp,
) zap.Field {
	if in == nil {
		return zap.Reflect(label, in)
	}

	return zap.Dict(
		label,
		zap.Any("Seconds", in.GetSeconds()),
		zap.Any("Nanos", in.GetNanos()),
	)
}

func file_inner_processing_grpc_transaction_refund_proto_message_TransactionRefundResponseV1ToZap(
	label string,
	in *TransactionRefundResponseV1,
) zap.Field {
	if in == nil {
		return zap.Reflect(label, in)
	}

	return zap.Dict(
		label,
		zap.Any("TransactionId", in.GetTransactionId()),
		zap.Any("ReasonName", in.GetReasonName()),
		zap.Any("Status", in.GetStatus()),
		zap.Any("Amount", in.GetAmount()),
		zap.Any("BankReferenceId", in.GetBankReferenceId()),
		zap.Any("BankMessage", in.GetBankMessage()),
		file_inner_processing_grpc_transaction_refund_proto_message_TimestampToZap("CreatedAt", in.GetCreatedAt()),
	)
}

func file_inner_processing_grpc_transaction_refund_proto_message_TransactionRefundResponseV1SliceToZap(
	label string,
	in []*TransactionRefundResponseV1,
) zap.Field {
	var res []zap.Field
	for index, val := range in {
		res = append(res, file_inner_processing_grpc_transaction_refund_proto_message_TransactionRefundResponseV1ToZap(fmt.Sprint(index), val))
	}

	return zap.Dict(label, res...)
}

func file_inner_processing_grpc_transaction_refund_proto_message_TransactionRequestDataV1ToZap(
	label string,
	in *TransactionRequestDataV1,
) zap.Field {
	if in == nil {
		return zap.Reflect(label, in)
	}

	return zap.Dict(
		label,
		zap.Any("TransactionId", in.GetTransactionId()),
	)
}

func file_inner_processing_grpc_transaction_refund_proto_message_TransactionResponseDataV1ToZap(
	label string,
	in *TransactionResponseDataV1,
) zap.Field {
	if in == nil {
		return zap.Reflect(label, in)
	}

	return zap.Dict(
		label,
		file_inner_processing_grpc_transaction_refund_proto_message_TransactionRefundResponseV1SliceToZap("Response", in.GetResponse()),
	)
}

var _ RefundServer = (*loggedRefundServer)(nil)

func NewLoggedRefundServer(srv RefundServer) RefundServer {
	return &loggedRefundServer{srv: srv}
}

type loggedRefundServer struct {
	UnimplementedRefundServer

	srv RefundServer
}

func (s *loggedRefundServer) GetByTransactionIDV1(
	ctx context.Context,
	request *TransactionRequestDataV1,
) (
	response *TransactionResponseDataV1,
	err error,
) {
	label := cntx.Begin(ctx, "RefundServer_GetByTransactionIDV1")
	defer cntx.End(ctx, label)

	defer func() {
		if err != nil {
			ctxzap.AddFields(ctx, zap.Any(label+"error", err))
			return
		}

		ctxzap.AddFields(
			ctx,
			file_inner_processing_grpc_transaction_refund_proto_message_TransactionResponseDataV1ToZap(label+"response", response),
		)
	}()

	ctxzap.AddFields(
		ctx,
		file_inner_processing_grpc_transaction_refund_proto_message_TransactionRequestDataV1ToZap(label+"request", request),
	)

	response, err = s.srv.GetByTransactionIDV1(ctx, request)

	return
}

var _ RefundClient = (*loggedRefundClient)(nil)

func NewLoggedRefundClient(client RefundClient) RefundClient {
	return &loggedRefundClient{client: client}
}

type loggedRefundClient struct {
	client RefundClient
}

func (s *loggedRefundClient) GetByTransactionIDV1(
	ctx context.Context,
	request *TransactionRequestDataV1,
	opts ...grpc.CallOption,
) (
	response *TransactionResponseDataV1,
	err error,
) {
	label := cntx.Begin(ctx, "RefundClient_GetByTransactionIDV1")
	defer cntx.End(ctx, label)

	defer func() {
		if err != nil {
			ctxzap.AddFields(ctx, zap.Any(label+"error", err))
			return
		}

		ctxzap.AddFields(
			ctx,
			file_inner_processing_grpc_transaction_refund_proto_message_TransactionResponseDataV1ToZap(label+"response", response),
		)
	}()

	ctxzap.AddFields(
		ctx,
		file_inner_processing_grpc_transaction_refund_proto_message_TransactionRequestDataV1ToZap(label+"request", request),
	)

	response, err = s.client.GetByTransactionIDV1(ctx, request, opts...)

	return
}
