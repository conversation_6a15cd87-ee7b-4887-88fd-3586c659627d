// Code generated by protoc-gen-go-mvp. DO NOT EDIT.
// versions:
// - protoc-gen-go-mvp v2.0.1
// created by mvp authors

package grpc

import (
	context "context"
	grpc "google.golang.org/grpc"
	metadata "google.golang.org/grpc/metadata"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
)

func NewIamFiscalizationServer(
	srv FiscalizationServer,
) FiscalizationServer {
	return &iamFiscalizationServer{
		srv: srv,
	}
}

var _ FiscalizationServer = (*iamFiscalizationServer)(nil)

type iamFiscalizationServer struct {
	UnimplementedFiscalizationServer

	srv FiscalizationServer
}

func (s *iamFiscalizationServer) MakeFiscalizationV1(
	ctx context.Context,
	req *MakeFiscalizationRequestV1,
) (
	*emptypb.Empty,
	error,
) {
	return s.srv.MakeFiscalizationV1(ctx, req)
}

func (s *iamFiscalizationServer) GetFiscalInfoByTransactionIDV1(
	ctx context.Context,
	req *GetFiscalInfoByTransactionIDRequestV1,
) (
	*GetFiscalInfoByTransactionIDResponseV1,
	error,
) {
	return s.srv.GetFiscalInfoByTransactionIDV1(ctx, req)
}

func (s *iamFiscalizationServer) ManageShifts(
	ctx context.Context,
	req *emptypb.Empty,
) (
	*emptypb.Empty,
	error,
) {
	return s.srv.ManageShifts(ctx, req)
}

func (s *iamFiscalizationServer) FinalizeFiscalizations(
	ctx context.Context,
	req *emptypb.Empty,
) (
	*emptypb.Empty,
	error,
) {
	return s.srv.FinalizeFiscalizations(ctx, req)
}

func NewIamFiscalizationClient(
	client FiscalizationClient,
) FiscalizationClient {
	return &iamFiscalizationClient{
		client: client,
	}
}

type iamFiscalizationClient struct {
	client FiscalizationClient
}

func (s *iamFiscalizationClient) MakeFiscalizationV1(
	ctx context.Context,
	req *MakeFiscalizationRequestV1,
	opts ...grpc.CallOption,
) (
	*emptypb.Empty,
	error,
) {
	md, _ := metadata.FromIncomingContext(ctx)
	return s.client.MakeFiscalizationV1(metadata.NewOutgoingContext(ctx, md), req)
}

func (s *iamFiscalizationClient) GetFiscalInfoByTransactionIDV1(
	ctx context.Context,
	req *GetFiscalInfoByTransactionIDRequestV1,
	opts ...grpc.CallOption,
) (
	*GetFiscalInfoByTransactionIDResponseV1,
	error,
) {
	md, _ := metadata.FromIncomingContext(ctx)
	return s.client.GetFiscalInfoByTransactionIDV1(metadata.NewOutgoingContext(ctx, md), req)
}

func (s *iamFiscalizationClient) ManageShifts(
	ctx context.Context,
	req *emptypb.Empty,
	opts ...grpc.CallOption,
) (
	*emptypb.Empty,
	error,
) {
	md, _ := metadata.FromIncomingContext(ctx)
	return s.client.ManageShifts(metadata.NewOutgoingContext(ctx, md), req)
}

func (s *iamFiscalizationClient) FinalizeFiscalizations(
	ctx context.Context,
	req *emptypb.Empty,
	opts ...grpc.CallOption,
) (
	*emptypb.Empty,
	error,
) {
	md, _ := metadata.FromIncomingContext(ctx)
	return s.client.FinalizeFiscalizations(metadata.NewOutgoingContext(ctx, md), req)
}
