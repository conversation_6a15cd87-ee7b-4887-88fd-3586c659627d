// Code generated by protoc-gen-go-mvp. DO NOT EDIT.
// versions:
// - protoc-gen-go-mvp v2.0.1
// created by mvp authors

package grpc

func (x EnumEpayStatusCode) Status() string {
	switch x {
	case EnumEpayStatusCode_Charge:
		return "CHARGE"
	case EnumEpayStatusCode_New:
		return "NEW"
	case EnumEpayStatusCode_Refund:
		return "REFUND"
	case EnumEpayStatusCode_Cancel:
		return "CANCEL"
	case EnumEpayStatusCode_Cancel_Old:
		return "CANCEL_OLD"
	case EnumEpayStatusCode_ThreeD:
		return "3D"
	case EnumEpayStatusCode_Reject:
		return "REJECT"
	case EnumEpayStatusCode_Auth:
		return "AUTH"
	case EnumEpayStatusCode_Failed:
		return "FAILED"
	default:
		return "undefined"
	}
}

func (x EnumEpayStatusCode) TransactionStatus() EnumTransactionStatus {
	switch x {
	case EnumEpayStatusCode_Charge:
		return EnumTransactionStatus_TransactionStatusSuccess
	case EnumEpayStatusCode_New:
		return EnumTransactionStatus_TransactionStatusHolded
	case EnumEpayStatusCode_Refund:
		return EnumTransactionStatus_TransactionStatusRefund
	case EnumEpayStatusCode_Cancel:
		return EnumTransactionStatus_TransactionStatusCanceled
	case EnumEpayStatusCode_Cancel_Old:
		return EnumTransactionStatus_TransactionStatusHolded
	case EnumEpayStatusCode_ThreeD:
		return EnumTransactionStatus_TransactionStatusThreeDSWaiting
	case EnumEpayStatusCode_Reject:
		return EnumTransactionStatus_TransactionStatusFailed
	case EnumEpayStatusCode_Auth:
		return EnumTransactionStatus_TransactionStatusAuthorized
	case EnumEpayStatusCode_Failed:
		return EnumTransactionStatus_TransactionStatusAuthorized
	default:
		return EnumTransactionStatus_TransactionStatusHolded
	}
}

// Created reference to EnumEpayStatusCode

//	|	EnumEpayStatusCode           	|	Status      	|	TransactionStatus                                    	|
//	|	EnumEpayStatusCode_Charge    	|	"CHARGE"    	|	EnumTransactionStatus_TransactionStatusSuccess       	|
//	|	EnumEpayStatusCode_New       	|	"NEW"       	|	EnumTransactionStatus_TransactionStatusHolded        	|
//	|	EnumEpayStatusCode_Refund    	|	"REFUND"    	|	EnumTransactionStatus_TransactionStatusRefund        	|
//	|	EnumEpayStatusCode_Cancel    	|	"CANCEL"    	|	EnumTransactionStatus_TransactionStatusCanceled      	|
//	|	EnumEpayStatusCode_Cancel_Old	|	"CANCEL_OLD"	|	EnumTransactionStatus_TransactionStatusHolded        	|
//	|	EnumEpayStatusCode_ThreeD    	|	"3D"        	|	EnumTransactionStatus_TransactionStatusThreeDSWaiting	|
//	|	EnumEpayStatusCode_Reject    	|	"REJECT"    	|	EnumTransactionStatus_TransactionStatusFailed        	|
//	|	EnumEpayStatusCode_Auth      	|	"AUTH"      	|	EnumTransactionStatus_TransactionStatusAuthorized    	|
//	|	EnumEpayStatusCode_Failed    	|	"FAILED"    	|	EnumTransactionStatus_TransactionStatusAuthorized    	|

var SliceEnumEpayStatusCodeRefs *sliceEnumEpayStatusCodeRefs

type sliceEnumEpayStatusCodeRefs struct{}

func (*sliceEnumEpayStatusCodeRefs) Status(slice ...EnumEpayStatusCode) []string {
	var result []string
	for _, val := range slice {
		result = append(result, val.Status())
	}

	return result
}

func (*sliceEnumEpayStatusCodeRefs) TransactionStatus(slice ...EnumEpayStatusCode) []EnumTransactionStatus {
	var result []EnumTransactionStatus
	for _, val := range slice {
		result = append(result, val.TransactionStatus())
	}

	return result
}
