package domain

import "time"

type MerchantWebhookBase struct {
	CreatedAt          time.Time              `json:"created_at"`
	FinishedAt         *time.Time             `json:"finished_at"`
	TransactionID      uint64                 `json:"transaction_id"`
	AcquirerCode       string                 `json:"acquirer_code,omitempty"`
	ProjectID          uint64                 `json:"project_id"`
	MerchantID         uint64                 `json:"merchant_id"`
	ProjectReferenceID string                 `json:"project_reference_id"`
	ProjectOrderID     string                 `json:"project_order_id,omitempty"`
	ProjectClientID    string                 `json:"project_client_id"`
	CardToken          string                 `json:"card_token,omitempty"`
	MaskedPan          string                 `json:"masked_pan,omitempty"`
	Year               string                 `json:"year,omitempty"`
	Month              string                 `json:"month,omitempty"`
	IpsName            string                 `json:"ips,omitempty"`
	IssuerName         string                 `json:"issuer,omitempty"`
	UserEmail          string                 `json:"user_email,omitempty"`
	UserPhone          string                 `json:"user_phone,omitempty"`
	StatusCode         string                 `json:"status_code"`
	TypeCode           string                 `json:"type_code"`
	Amount             float64                `json:"amount"`
	Description        string                 `json:"description"`
	AdditionalData     map[string]interface{} `json:"additional_data,omitempty"`
	//transaction_info.integration_error_message
	BankMessage string `json:"bank_message,omitempty"`
	//transaction_info.integration_error_code
	BankCode string `json:"bank_code,omitempty"`
}
