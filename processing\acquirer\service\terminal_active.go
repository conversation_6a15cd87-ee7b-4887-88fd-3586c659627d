package service

import (
	"context"
	"git.local/sensitive/processing/acquirer/model"
	"git.local/sensitive/processing/acquirer/repository/database"
	"git.local/sensitive/processing/acquirer/schema"
	"git.local/sensitive/sdk/dog"
	"go.uber.org/zap"
)

type TerminalActiveService struct {
	terminalRepo database.Terminaler
}

func NewTerminalActiveService(
	terminalRepo database.Terminaler,
) *TerminalActiveService {
	return &TerminalActiveService{
		terminalRepo: terminalRepo,
	}
}

func (s *TerminalActiveService) FindActiveTerminalsByProject(
	ctx context.Context,
	projectID uint64,
	paymentType uint64,
) (_ model.Terminals, err error) {
	ctx, span := dog.CreateSpan(ctx, "TerminalService_FindActiveTerminalsByProject")
	defer span.End()

	terminals, err := s.terminalRepo.FindActiveTerminalsByProject(ctx, projectID, paymentType)
	if err != nil {
		dog.L().Error("TerminalService_FindActiveTerminalsByProject", zap.Uint64("project_id", projectID),
			zap.Uint64("type_id", paymentType), zap.Error(err))
		return nil, err
	}

	for _, terminal := range terminals {
		decrypt, err := dog.AESDecrypt(terminal.EncryptedConfig, dog.ConfigString("SYMMETRIC_KEY"))
		if err != nil {
			return nil, err
		}

		terminal.EncryptedConfig = decrypt
	}

	return terminals, nil
}

func (s *TerminalActiveService) FindActiveTerminalsByIDs(
	ctx context.Context,
	request *schema.ActiveTerminalsByIDsReq,
) (_ []*model.Terminal, err error) {
	ctx, span := dog.CreateSpan(ctx, "TerminalActiveService_FindActiveTerminalsByIDs")
	defer span.End()

	terminals, err := s.terminalRepo.FindActiveTerminalsByIDs(
		ctx,
		request.TerminalIDs,
		request.ProjectID,
		request.TransactionTypeID,
	)
	if err != nil {
		dog.L().Error("TerminalActiveService_FindActiveTerminalsByIDs", zap.Uint64("project_id", request.ProjectID),
			zap.Uint64("type_id", request.TransactionTypeID), zap.Uint64s("terminal_ids", request.TerminalIDs), zap.Error(err))
		return nil, err
	}

	return terminals, nil
}
