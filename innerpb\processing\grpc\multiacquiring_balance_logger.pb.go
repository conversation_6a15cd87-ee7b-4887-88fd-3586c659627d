// Code generated by protoc-gen-go-mvp. DO NOT EDIT.
// versions:
// - protoc-gen-go-mvp v2.0.1
// created by mvp authors

package grpc

import (
	context "context"
	fmt "fmt"
	cntx "git.local/sensitive/mvp/pkg/cntx"
	ctxzap "github.com/grpc-ecosystem/go-grpc-middleware/logging/zap/ctxzap"
	zap "go.uber.org/zap"
	grpc "google.golang.org/grpc"
	structpb "google.golang.org/protobuf/types/known/structpb"
)

func file_inner_processing_grpc_multiacquiring_balance_proto_string_structpb_Value(
	label string,
	in map[string]*structpb.Value,
) zap.Field {
	var res []zap.Field
	for key, val := range in {
		res = append(res, file_inner_processing_grpc_multiacquiring_balance_proto_message_ValueToZap(fmt.Sprint(key), val))
	}

	return zap.Dict(label, res...)
}

func file_inner_processing_grpc_multiacquiring_balance_proto_message_ListValueToZap(
	label string,
	in *structpb.ListValue,
) zap.Field {
	if in == nil {
		return zap.Reflect(label, in)
	}

	return zap.Dict(
		label,
		file_inner_processing_grpc_multiacquiring_balance_proto_message_ValueSliceToZap("Values", in.GetValues()),
	)
}

func file_inner_processing_grpc_multiacquiring_balance_proto_enum_NullValueToZap(
	label string,
	in structpb.NullValue,
) zap.Field {
	str, ok := structpb.NullValue_name[int32(in)]
	if !ok {
		str = fmt.Sprintf("unknown structpb.NullValue value %d", in)
	}

	return zap.Dict(
		label,
		zap.Any("value", structpb.NullValue(in)),
		zap.Any("name", str),
	)
}

func file_inner_processing_grpc_multiacquiring_balance_proto_message_StructToZap(
	label string,
	in *structpb.Struct,
) zap.Field {
	if in == nil {
		return zap.Reflect(label, in)
	}

	return zap.Dict(
		label,
		file_inner_processing_grpc_multiacquiring_balance_proto_string_structpb_Value("Fields", in.GetFields()),
	)
}

func file_inner_processing_grpc_multiacquiring_balance_proto_message_ValueToZap(
	label string,
	in *structpb.Value,
) zap.Field {
	if in == nil {
		return zap.Reflect(label, in)
	}

	return zap.Dict(
		label,
		file_inner_processing_grpc_multiacquiring_balance_proto_enum_NullValueToZap("NullValue", in.GetNullValue()),
		zap.Any("NumberValue", in.GetNumberValue()),
		zap.Any("StringValue", in.GetStringValue()),
		zap.Any("BoolValue", in.GetBoolValue()),
		file_inner_processing_grpc_multiacquiring_balance_proto_message_StructToZap("StructValue", in.GetStructValue()),
		file_inner_processing_grpc_multiacquiring_balance_proto_message_ListValueToZap("ListValue", in.GetListValue()),
	)
}

func file_inner_processing_grpc_multiacquiring_balance_proto_message_ValueSliceToZap(
	label string,
	in []*structpb.Value,
) zap.Field {
	var res []zap.Field
	for index, val := range in {
		res = append(res, file_inner_processing_grpc_multiacquiring_balance_proto_message_ValueToZap(fmt.Sprint(index), val))
	}

	return zap.Dict(label, res...)
}

func file_inner_processing_grpc_multiacquiring_balance_proto_message_CheckBalanceRequestToZap(
	label string,
	in *CheckBalanceRequest,
) zap.Field {
	if in == nil {
		return zap.Reflect(label, in)
	}

	return zap.Dict(
		label,
		zap.Any("TerminalId", in.GetTerminalId()),
		zap.Any("AcquirerCode", in.GetAcquirerCode()),
		file_inner_processing_grpc_multiacquiring_balance_proto_message_StructToZap("Config", in.GetConfig()),
	)
}

func file_inner_processing_grpc_multiacquiring_balance_proto_message_CheckBalanceResponseToZap(
	label string,
	in *CheckBalanceResponse,
) zap.Field {
	if in == nil {
		return zap.Reflect(label, in)
	}

	return zap.Dict(
		label,
		zap.Any("Amount", in.GetAmount()),
		zap.Any("Message", in.GetMessage()),
		zap.Any("TerminalId", in.GetTerminalId()),
		zap.Any("AcquirerCode", in.GetAcquirerCode()),
	)
}

var _ MultiacquiringBalanceServer = (*loggedMultiacquiringBalanceServer)(nil)

func NewLoggedMultiacquiringBalanceServer(srv MultiacquiringBalanceServer) MultiacquiringBalanceServer {
	return &loggedMultiacquiringBalanceServer{srv: srv}
}

type loggedMultiacquiringBalanceServer struct {
	UnimplementedMultiacquiringBalanceServer

	srv MultiacquiringBalanceServer
}

func (s *loggedMultiacquiringBalanceServer) CheckBalance(
	ctx context.Context,
	request *CheckBalanceRequest,
) (
	response *CheckBalanceResponse,
	err error,
) {
	label := cntx.Begin(ctx, "MultiacquiringBalanceServer_CheckBalance")
	defer cntx.End(ctx, label)

	defer func() {
		if err != nil {
			ctxzap.AddFields(ctx, zap.Any(label+"error", err))
			return
		}

		ctxzap.AddFields(
			ctx,
			file_inner_processing_grpc_multiacquiring_balance_proto_message_CheckBalanceResponseToZap(label+"response", response),
		)
	}()

	ctxzap.AddFields(
		ctx,
		file_inner_processing_grpc_multiacquiring_balance_proto_message_CheckBalanceRequestToZap(label+"request", request),
	)

	response, err = s.srv.CheckBalance(ctx, request)

	return
}

var _ MultiacquiringBalanceClient = (*loggedMultiacquiringBalanceClient)(nil)

func NewLoggedMultiacquiringBalanceClient(client MultiacquiringBalanceClient) MultiacquiringBalanceClient {
	return &loggedMultiacquiringBalanceClient{client: client}
}

type loggedMultiacquiringBalanceClient struct {
	client MultiacquiringBalanceClient
}

func (s *loggedMultiacquiringBalanceClient) CheckBalance(
	ctx context.Context,
	request *CheckBalanceRequest,
	opts ...grpc.CallOption,
) (
	response *CheckBalanceResponse,
	err error,
) {
	label := cntx.Begin(ctx, "MultiacquiringBalanceClient_CheckBalance")
	defer cntx.End(ctx, label)

	defer func() {
		if err != nil {
			ctxzap.AddFields(ctx, zap.Any(label+"error", err))
			return
		}

		ctxzap.AddFields(
			ctx,
			file_inner_processing_grpc_multiacquiring_balance_proto_message_CheckBalanceResponseToZap(label+"response", response),
		)
	}()

	ctxzap.AddFields(
		ctx,
		file_inner_processing_grpc_multiacquiring_balance_proto_message_CheckBalanceRequestToZap(label+"request", request),
	)

	response, err = s.client.CheckBalance(ctx, request, opts...)

	return
}
