edition = "2023";

package epay;

option go_package = "git.local/sensitive/innerpb/processing/rest";

import "mvp/proto/rest.proto";
import "mvp/proto/logger.proto";

service EpayAuth {
  option(mvp.rest_service_options) = {
    hosts :[
      {
        stand: "local",
        base_uri: "https://testoauth.homebank.kz/epay2"
      },
      {
        stand: "dev",
        base_uri: "https://testoauth.homebank.kz/epay2"
      },
      {
        stand: "test",
        base_uri: "https://epay-oauth.homebank.kz"
      },
      {
        stand: "stage",
        base_uri: "https://epay-oauth.homebank.kz"
      },
      {
        stand: "prod",
        base_uri: "https://epay-oauth.homebank.kz"
      },
      {
        stand: "sandbox",
        base_uri: "https://testoauth.homebank.kz/epay2"
      }
    ]
  };

  rpc GetPaymentToken(EpayGetPaymentTokenRequest) returns (EpayGetPaymentTokenResponse) {
    option(mvp.rest_method_options) = {
      method: Post,
      authorization: None,
      path: "/oauth2/token",
      max_request_timeout: 60,
      request_marshal: FormData,
      response_unmarshal: JSON
    };
  }

  rpc GetStatusToken(EpayGetStatusTokenRequest) returns (EpayGetStatusTokenResponse) {
    option(mvp.rest_method_options) = {
      method: Post,
      authorization: None,
      path: "/oauth2/token",
      max_request_timeout: 60,
      request_marshal: FormData,
      response_unmarshal: JSON
    };
  }

  rpc GetGeneralToken(EpayGetGeneralTokenRequest) returns (EpayGetGeneralTokenResponse) {
    option(mvp.rest_method_options) = {
      method: Post,
      authorization: None,
      path: "/oauth2/token",
      max_request_timeout: 60,
      request_marshal: FormData,
      response_unmarshal: JSON
    };
  }

  rpc GetApplePayToken(EpayGetApplePayTokenRequest) returns (EpayGetApplePayTokenResponse) {
    option(mvp.rest_method_options) = {
      method: Post,
      authorization: None,
      path: "/token",
      max_request_timeout: 60,
      request_marshal: FormData,
      response_unmarshal: JSON,
    };
  }
}

message EpayGetPaymentTokenRequest {
  message GetTokenRequest {
    string grant_type = 1;
    string scope = 2;
    string client_id = 3 [(mvp.FieldLoggerLevel) = Hidden];
    string client_secret = 4 [(mvp.FieldLoggerLevel) = Hidden];
    string invoiceID = 5;
    string amount = 6;
    string currency = 7;
    string terminal = 8 [(mvp.FieldLoggerLevel) = Hidden];
  }

  GetTokenRequest body = 1;
}

message EpayGetPaymentTokenResponse {
  GetTokenResponse token_response = 1;

  message GetTokenResponse {
    string access_token = 1 [(mvp.FieldLoggerLevel) = Hidden];
    string expires_in = 2;
    string refresh_token = 3 [(mvp.FieldLoggerLevel) = Hidden];
    string scope = 4;
    string token_type = 5;
    int32 code = 6;
    string message = 7;
  }
}

message EpayGetStatusTokenRequest {
  message GetStatusTokenRequest {
    string grant_type = 1;
    string scope = 2;
    string client_id = 3 [(mvp.FieldLoggerLevel) = Hidden];
    string client_secret = 4 [(mvp.FieldLoggerLevel) = Hidden];
    string terminal = 5 [(mvp.FieldLoggerLevel) = Hidden];
  }

  GetStatusTokenRequest body = 1;
}

message EpayGetStatusTokenResponse {
  message GetStatusTokenResponse {
    string access_token = 1 [(mvp.FieldLoggerLevel) = Hidden];
    string expires_in = 2;
    string refresh_token = 3 [(mvp.FieldLoggerLevel) = Hidden];
    string scope = 4;
    string token_type = 5;
    int32 code = 6;
    string message = 7;
  }

  GetStatusTokenResponse token_response = 1;
}

message EpayGetGeneralTokenRequest {
  message GetGeneralTokenRequest {
    string grant_type = 1;
    string scope = 2;
    string client_id = 3 [(mvp.FieldLoggerLevel) = Hidden];
    string client_secret = 4 [(mvp.FieldLoggerLevel) = Hidden];
  }

  GetGeneralTokenRequest body = 1;
}

message EpayGetGeneralTokenResponse {
  message GetGeneralTokenResponse {
    string access_token = 1 [(mvp.FieldLoggerLevel) = Hidden];
    string expires_in = 2;
    string refresh_token = 3 [(mvp.FieldLoggerLevel) = Hidden];
    string scope = 4;
    string token_type = 5;
  }

  GetGeneralTokenResponse token_response = 1;
}

message EpayGetApplePayTokenRequest {
  GetApplePayTokenRequest body = 1;

  message GetApplePayTokenRequest {
    string grant_type = 1;
    string scope = 2;
    string client_id = 3 [(mvp.FieldLoggerLevel) = Hidden];
    string client_secret = 4 [(mvp.FieldLoggerLevel) = Hidden];
    string invoiceID = 5;
    string amount = 6;
    string currency = 7;
    string terminal = 8 [(mvp.FieldLoggerLevel) = Hidden];
  }
}

message EpayGetApplePayTokenResponse {
  GetApplePayTokenResponse apple_token = 1;

  message GetApplePayTokenResponse {
    string access_token = 1 [(mvp.FieldLoggerLevel) = Hidden];
    string expires_in = 2;
    string scope = 3;
    string token_type = 4;
  }
}