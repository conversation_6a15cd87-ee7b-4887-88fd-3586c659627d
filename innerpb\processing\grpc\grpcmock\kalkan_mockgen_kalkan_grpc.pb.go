// Code generated by MockGen. DO NOT EDIT.
// Source: kalkan_grpc.pb.go

// Package grpcmock is a generated GoMock package.
package grpcmock

import (
	context "context"
	reflect "reflect"

	grpc "git.local/sensitive/innerpb/processing/grpc"
	gomock "github.com/golang/mock/gomock"
	grpc0 "google.golang.org/grpc"
)

// MockKalkanClient is a mock of KalkanClient interface.
type MockKalkanClient struct {
	ctrl     *gomock.Controller
	recorder *MockKalkanClientMockRecorder
}

// MockKalkanClientMockRecorder is the mock recorder for MockKalkanClient.
type MockKalkanClientMockRecorder struct {
	mock *MockKalkanClient
}

// NewMockKalkanClient creates a new mock instance.
func NewMockKalkanClient(ctrl *gomock.Controller) *MockKalkanClient {
	mock := &MockKalkanClient{ctrl: ctrl}
	mock.recorder = &MockKalkanClientMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockKalkanClient) EXPECT() *MockKalkanClientMockRecorder {
	return m.recorder
}

// MakeSignatureV1 mocks base method.
func (m *MockKalkanClient) MakeSignatureV1(ctx context.Context, in *grpc.MakeSignatureRequestV1, opts ...grpc0.CallOption) (*grpc.MakeSignatureResponseV1, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "MakeSignatureV1", varargs...)
	ret0, _ := ret[0].(*grpc.MakeSignatureResponseV1)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// MakeSignatureV1 indicates an expected call of MakeSignatureV1.
func (mr *MockKalkanClientMockRecorder) MakeSignatureV1(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "MakeSignatureV1", reflect.TypeOf((*MockKalkanClient)(nil).MakeSignatureV1), varargs...)
}

// MockKalkanServer is a mock of KalkanServer interface.
type MockKalkanServer struct {
	ctrl     *gomock.Controller
	recorder *MockKalkanServerMockRecorder
}

// MockKalkanServerMockRecorder is the mock recorder for MockKalkanServer.
type MockKalkanServerMockRecorder struct {
	mock *MockKalkanServer
}

// NewMockKalkanServer creates a new mock instance.
func NewMockKalkanServer(ctrl *gomock.Controller) *MockKalkanServer {
	mock := &MockKalkanServer{ctrl: ctrl}
	mock.recorder = &MockKalkanServerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockKalkanServer) EXPECT() *MockKalkanServerMockRecorder {
	return m.recorder
}

// MakeSignatureV1 mocks base method.
func (m *MockKalkanServer) MakeSignatureV1(arg0 context.Context, arg1 *grpc.MakeSignatureRequestV1) (*grpc.MakeSignatureResponseV1, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "MakeSignatureV1", arg0, arg1)
	ret0, _ := ret[0].(*grpc.MakeSignatureResponseV1)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// MakeSignatureV1 indicates an expected call of MakeSignatureV1.
func (mr *MockKalkanServerMockRecorder) MakeSignatureV1(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "MakeSignatureV1", reflect.TypeOf((*MockKalkanServer)(nil).MakeSignatureV1), arg0, arg1)
}

// mustEmbedUnimplementedKalkanServer mocks base method.
func (m *MockKalkanServer) mustEmbedUnimplementedKalkanServer() {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "mustEmbedUnimplementedKalkanServer")
}

// mustEmbedUnimplementedKalkanServer indicates an expected call of mustEmbedUnimplementedKalkanServer.
func (mr *MockKalkanServerMockRecorder) mustEmbedUnimplementedKalkanServer() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "mustEmbedUnimplementedKalkanServer", reflect.TypeOf((*MockKalkanServer)(nil).mustEmbedUnimplementedKalkanServer))
}

// MockUnsafeKalkanServer is a mock of UnsafeKalkanServer interface.
type MockUnsafeKalkanServer struct {
	ctrl     *gomock.Controller
	recorder *MockUnsafeKalkanServerMockRecorder
}

// MockUnsafeKalkanServerMockRecorder is the mock recorder for MockUnsafeKalkanServer.
type MockUnsafeKalkanServerMockRecorder struct {
	mock *MockUnsafeKalkanServer
}

// NewMockUnsafeKalkanServer creates a new mock instance.
func NewMockUnsafeKalkanServer(ctrl *gomock.Controller) *MockUnsafeKalkanServer {
	mock := &MockUnsafeKalkanServer{ctrl: ctrl}
	mock.recorder = &MockUnsafeKalkanServerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockUnsafeKalkanServer) EXPECT() *MockUnsafeKalkanServerMockRecorder {
	return m.recorder
}

// mustEmbedUnimplementedKalkanServer mocks base method.
func (m *MockUnsafeKalkanServer) mustEmbedUnimplementedKalkanServer() {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "mustEmbedUnimplementedKalkanServer")
}

// mustEmbedUnimplementedKalkanServer indicates an expected call of mustEmbedUnimplementedKalkanServer.
func (mr *MockUnsafeKalkanServerMockRecorder) mustEmbedUnimplementedKalkanServer() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "mustEmbedUnimplementedKalkanServer", reflect.TypeOf((*MockUnsafeKalkanServer)(nil).mustEmbedUnimplementedKalkanServer))
}
