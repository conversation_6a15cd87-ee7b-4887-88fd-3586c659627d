package repository

import (
	"context"
	"errors"
	"git.local/sensitive/innerpb/processing/goerr"
	"git.local/sensitive/processing/account/model"
	"git.local/sensitive/sdk/dog"
	"gorm.io/gorm"
)

type ProcessedOrderStatusDB struct {
	db *gorm.DB
}

func NewProcessedOrderStatusDB(db *gorm.DB) ProcessedOrderStatuser {
	return &ProcessedOrderStatusDB{
		db: db,
	}
}

func (ps *ProcessedOrderStatusDB) GetByID(ctx context.Context, id uint64) (_ *model.ProcessedOrderStatus, err error) {
	ctx, span := dog.CreateSpan(ctx, "ProcessedOrderStatusDB_GetByID")
	defer span.End()

	processedOrderStatus := new(model.ProcessedOrderStatus)

	if err = ps.db.WithContext(ctx).
		Where("id = ?", id).
		First(processedOrderStatus).
		Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, goerr.ErrDbUnexpected.WithCtx(ctx)
		}

		return nil, goerr.ErrDbUnexpected.WithErr(err).WithCtx(ctx)
	}

	return processedOrderStatus, nil
}

func (ps *ProcessedOrderStatusDB) GetByCode(
	ctx context.Context,
	code model.ProcessedOrderStatusCode,
) (_ *model.ProcessedOrderStatus, err error) {
	ctx, span := dog.CreateSpan(ctx, "ProcessedOrderStatusDB_GetByCode")
	defer span.End()

	processedOrderStatus := new(model.ProcessedOrderStatus)

	if err = ps.db.WithContext(ctx).
		Where("code = ?", code).
		First(processedOrderStatus).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, goerr.ErrDbUnexpected.WithCtx(ctx)
		}

		return nil, goerr.ErrDbUnexpected.WithErr(err).WithCtx(ctx)
	}

	return processedOrderStatus, nil
}
