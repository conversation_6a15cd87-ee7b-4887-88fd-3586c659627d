// Code generated by protoc-gen-go-mvp. DO NOT EDIT.
// versions:
// - protoc-gen-go-mvp v2.0.1
// created by mvp authors

package grpc

import (
	protojson "google.golang.org/protobuf/encoding/protojson"
)

func (m *GetCardInfoByPanRequestV1) UnmarshalJSON(in []byte) error {
	val := new(GetCardInfoByPanRequestV1)
	if err := (&protojson.UnmarshalOptions{
		DiscardUnknown: true,
	}).Unmarshal(in, val); err != nil {
		return err
	}

	*m = *val
	return nil
}

func (m *GetCardInfoByPanRequestV1) MarshalJSON() ([]byte, error) {
	return (&protojson.MarshalOptions{
		UseProtoNames: true,
	}).Marshal(m)
}

func (m *GetCardInfoByPanResponseV1) UnmarshalJSON(in []byte) error {
	val := new(GetCardInfoByPanResponseV1)
	if err := (&protojson.UnmarshalOptions{
		DiscardUnknown: true,
	}).Unmarshal(in, val); err != nil {
		return err
	}

	*m = *val
	return nil
}

func (m *GetCardInfoByPanResponseV1) MarshalJSON() ([]byte, error) {
	return (&protojson.MarshalOptions{
		UseProtoNames: true,
	}).Marshal(m)
}
