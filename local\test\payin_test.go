package test

import (
	"bytes"
	"crypto/sha256"
	"encoding/base64"
	"encoding/hex"
	"encoding/json"
	"io"
	"net/http"
	"net/url"
	"strconv"
	"testing"

	"github.com/sethvargo/go-password/password"
	"github.com/stretchr/testify/assert"

	"git.local/sensitive/local/internal/domain"
	"git.local/sensitive/local/internal/smoketesting/core/payin"
)

func TestPayIn(t *testing.T) {
	t.Skip()

	testData := payin.TestData()

	for _, data := range testData {
		t.Run(data.Name, func(t *testing.T) {
			transactionId, hash := generatePayInTransaction(t, &domain.GenerateTransactionRequest{
				Amount:             10,
				ProjectID:          devProjectID,
				MerchantID:         devMerchantID,
				ProjectClientID:    devProjectClientID,
				FailureRedirectUrl: "https://www.youtube.com",
				SuccessRedirectUrl: "https://www.youtube.com",
				CallbackUrl:        devCallbackUrl,
				Description:        "afk_afk_afk",
				ProjectReferenceID: generateSecretKey(),
			}, domain.SmokeData[string]{
				Expected: domain.Expected[string]{
					HttpStatusCode: http.StatusOK,
				},
			})
			makePayIn(t, &domain.MakePayInRequest{
				TransactionID:   transactionId,
				TransactionHash: hash,
				UserEmail:       "<EMAIL>",
				UserPhone:       "+77771581078", // dont call pls
				EncryptedCard:   devEncryptedCard,
			}, &domain.Expected[*domain.MakePayInResponse]{
				HttpStatusCode: http.StatusOK,
				Data: &domain.MakePayInResponse{
					TransactionID: transactionId,
				},
			})
		})
	}
}

func generateSecretKey() string {
	return "qambar" + password.MustGenerate(20, 5, 0, false, true)
}

func generatePayInTransaction(t *testing.T, req *domain.GenerateTransactionRequest, expected domain.SmokeData[string]) (uint64, string) {
	body, err := json.Marshal(req)
	if err != nil {
		t.Fatal(err)
	}

	request, err := http.NewRequest(http.MethodPost, domainURL+CreateTrUrlPath, bytes.NewBuffer(body))
	if err != nil {
		t.Fatal(err)
	}

	request.Header.Add("Content-Type", "application/json")
	request.Header.Add("Authorization", "Bearer "+generateToken(req, ""))

	resp, err := http.DefaultClient.Do(request)
	if err != nil {
		t.Fatal(err)
	}

	assert.Equal(t, expected.Expected.HttpStatusCode, resp.StatusCode, "http status code")

	respBody, err := io.ReadAll(resp.Body)
	if err != nil {
		t.Fatal(err)
	}
	defer resp.Body.Close()

	var res domain.Response[any]
	err = json.Unmarshal(respBody, &res)
	if err != nil {
		t.Fatal(err)
	}

	assert.NotEmpty(t, res.Message, "message field emptiness")

	if expected.Expected.HttpStatusCode >= http.StatusBadRequest {
		assert.NotEmpty(t, res.StatusCode, "error code check")
		assert.Equal(t, res.Status, false)

		mp, ok := res.Result.(map[string]any) // checking error field existance
		if !ok {
			t.Fatal("different object came into error")
		}

		if _, ok = mp["error"]; !ok {
			t.Fatal("error tag does not exist in response")
		}

		return 0, ""
	}

	assert.Equal(t, res.Status, true)

	link, ok := res.Result.(string) // if it is success case, we will receive url, so we can assert it to string
	if !ok {
		t.Fatal("different object came into result")
	}

	u, err := url.Parse(link)
	if err != nil {
		t.Fatal(err)
	}

	params := u.Query()

	transactionId := params.Get("transaction_id")
	if transactionId == "" {
		t.Fatal("transactionId is empty")
	}

	trID, err := strconv.ParseUint(transactionId, 10, 64)
	if err != nil {
		t.Fatal(err)
	}

	hash := params.Get("hash")
	if hash == "" {
		t.Fatal("hash is empty")
	}

	return trID, hash
}

func makePayIn(t *testing.T, req *domain.MakePayInRequest, expected *domain.Expected[*domain.MakePayInResponse]) { // TODO: what kind of values we need to check?
	body, err := json.Marshal(req)
	if err != nil {
		t.Fatal(err)
	}

	request, err := http.NewRequest(http.MethodPost, domainURL+PayInUrlPath, bytes.NewBuffer(body))
	if err != nil {
		t.Fatal(err)
	}

	request.Header.Add("Content-Type", "application/json")

	resp, err := http.DefaultClient.Do(request)
	if err != nil {
		t.Fatal(err)
	}

	respBody, err := io.ReadAll(resp.Body)
	if err != nil {
		t.Fatal(err)
	}
	defer resp.Body.Close()

	var res domain.Response[*domain.MakePayInResponse]
	if err = json.Unmarshal(respBody, &res); err != nil {
		t.Fatal(err)
	}

	// need to assert data
	assert.Equal(t, expected.Data.TransactionID, res.Result.TransactionID, "transactionID")
	assert.NotEmpty(t, res.Result.TransactionStatusCode, "transactionStatusCode")
	assert.NotEmpty(t, res.Result.AcquirerCode, "acquirerCode")
}

func generateToken(req interface{}, environment string) string {
	secret := devSecret // need to determine environment using some func

	notSortedJson, err := json.Marshal(&req)
	if err != nil {
		panic(err)
	}

	var notSortedMap map[string]interface{}
	if err = json.Unmarshal(notSortedJson, &notSortedMap); err != nil {
		panic(err)
	}

	delete(notSortedMap, "additional_data")
	sortedJson, err := json.Marshal(&notSortedMap)
	if err != nil {
		panic(err)
	}

	signData := base64.StdEncoding.EncodeToString(sortedJson)

	sign := sha256.Sum256([]byte(signData + secret))

	return hex.EncodeToString(sign[:])
}
