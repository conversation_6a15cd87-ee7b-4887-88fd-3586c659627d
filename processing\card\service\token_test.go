package service

import (
	"context"
	"crypto/aes"
	"encoding/json"
	"errors"
	"git.local/sensitive/innerpb/processing/goerr"
	"testing"
	"time"

	"github.com/golang/mock/gomock"
	"github.com/stretchr/testify/require"

	goevents "git.local/sensitive/innerpb/processing/events"
	"git.local/sensitive/processing/card/model"
	"git.local/sensitive/processing/card/repository/database/databasemocks"
	"git.local/sensitive/processing/card/schema"
	"git.local/sensitive/sdk/dog/e2e"
)

var (
	badKey    = "123"
	normalKey = "2lgD_WlWfa3Z-BD9"
)

func TestCreateToken(t *testing.T) {
	testNow := time.Date(2009, time.November, 10, 23, 0, 0, 0, time.UTC)

	type createOp struct {
		input     model.Token
		outputErr error
	}

	tests := []struct {
		name    string
		req     schema.TokenRequest
		wantErr error
		create  createOp
	}{
		{
			name: "error",
			req: schema.TokenRequest{
				AcquirerId: 1,
				CardId:     2,
				Token:      "some token",
				TerminalId: 3,
			},
			wantErr: errors.New("some error"),
			create: createOp{
				input: model.Token{
					AcquirerId: 1,
					CardId:     2,
					Token:      "some token",
					TerminalId: 3,
					ApprovedAt: testNow,
				},
				outputErr: errors.New("some error"),
			},
		},
		{
			name: "success",
			req: schema.TokenRequest{
				AcquirerId: 1,
				CardId:     2,
				Token:      "some token",
				TerminalId: 3,
			},
			wantErr: nil,
			create: createOp{
				input: model.Token{
					AcquirerId: 1,
					CardId:     2,
					Token:      "some token",
					TerminalId: 3,
					ApprovedAt: testNow,
				},
				outputErr: nil,
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctrl := gomock.NewController(t)

			cardTokenerDBMock := databasemocks.NewMockCardTokener(ctrl)

			cardTokenerDBMock.EXPECT().Create(
				gomock.Any(),
				tt.create.input,
			).Return(
				tt.create.outputErr,
			)

			s := TokenService{
				cardTokenerRepo: cardTokenerDBMock,
				getCurrentTime: func() time.Time {
					return testNow
				},
			}

			err := s.Create(context.Background(), tt.req)
			if tt.wantErr != nil {
				require.Error(t, err)
				require.Equal(t, tt.wantErr, err)
			} else {
				require.NoError(t, err)
			}
		})
	}
}

func TestUpdateToken(t *testing.T) {
	type updateOp struct {
		isCalled     bool
		input        *goevents.SaveToken
		inputTokenID uint64
		outputErr    error
	}

	tests := []struct {
		name       string
		reqTokenID uint64
		req        *goevents.SaveToken
		wantErr    error
		update     updateOp
	}{
		{
			name:       "nil_request",
			req:        nil,
			reqTokenID: 1,
			wantErr:    goerr.ErrTokenNotFound,
		},
		{
			name: "update_error",
			req: &goevents.SaveToken{
				AcquirerId: 11,
				ProjectId:  10,
				TerminalId: 12,
				Token:      "some token",
			},
			reqTokenID: 1,
			wantErr:    errors.New("some error"),
			update: updateOp{
				isCalled: true,
				input: &goevents.SaveToken{
					AcquirerId: 11,
					ProjectId:  10,
					TerminalId: 12,
					Token:      "some token",
				},
				inputTokenID: 1,
				outputErr:    errors.New("some error"),
			},
		},
		{
			name: "success",
			req: &goevents.SaveToken{
				AcquirerId: 11,
				ProjectId:  10,
				TerminalId: 12,
				Token:      "some token",
			},
			reqTokenID: 1,
			wantErr:    nil,
			update: updateOp{
				isCalled:     true,
				inputTokenID: 1,
				input: &goevents.SaveToken{
					AcquirerId: 11,
					ProjectId:  10,
					TerminalId: 12,
					Token:      "some token",
				},
				outputErr: nil,
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctrl := gomock.NewController(t)

			tokenUpdatorDBMock := databasemocks.NewMockTokenUpdator(ctrl)

			s := TokenService{
				tokenUpdatorRepo: tokenUpdatorDBMock,
			}

			if tt.update.isCalled {
				tokenUpdatorDBMock.EXPECT().Update(
					gomock.Any(),
					tt.update.inputTokenID,
					tt.update.input,
				).Return(
					tt.update.outputErr,
				)
			}

			err := s.Update(context.Background(), tt.reqTokenID, tt.req)
			if tt.wantErr != nil {
				require.Error(t, err)
				require.Equal(t, tt.wantErr, err)
			} else {
				require.NoError(t, err)
			}
		})
	}
}

func TestGetTokenByCardAndAcquirer(t *testing.T) {
	type getTokenByCardAndAcquirerOp struct {
		inputCardId, inputAcquirerId uint64
		output                       model.Token
		outputErr                    error
	}

	tests := []struct {
		name                      string
		reqCardID, reqAcquirerID  uint64
		want                      model.Token
		wantErr                   error
		getTokenByCardAndAcquirer getTokenByCardAndAcquirerOp
	}{
		{
			name:          "error",
			reqCardID:     1,
			reqAcquirerID: 1,
			wantErr:       errors.New("some error"),
			want:          model.Token{},
			getTokenByCardAndAcquirer: getTokenByCardAndAcquirerOp{
				inputCardId:     1,
				inputAcquirerId: 1,
				output:          model.Token{},
				outputErr:       errors.New("some error"),
			},
		},
		{
			name:          "success",
			reqCardID:     1,
			reqAcquirerID: 1,
			wantErr:       nil,
			want: model.Token{
				Id:         22,
				CardId:     1,
				AcquirerId: 1,
				Token:      "some token",
				TerminalId: 2,
			},
			getTokenByCardAndAcquirer: getTokenByCardAndAcquirerOp{
				inputCardId:     1,
				inputAcquirerId: 1,
				output: model.Token{
					Id:         22,
					CardId:     1,
					AcquirerId: 1,
					Token:      "some token",
					TerminalId: 2,
				},
				outputErr: nil,
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctrl := gomock.NewController(t)

			cardTokenerDBMock := databasemocks.NewMockCardTokener(ctrl)

			s := TokenService{
				cardTokenerRepo: cardTokenerDBMock,
			}

			cardTokenerDBMock.EXPECT().GetByCardAndAcquirer(
				gomock.Any(),
				tt.getTokenByCardAndAcquirer.inputCardId,
				tt.getTokenByCardAndAcquirer.inputAcquirerId,
			).Return(
				tt.getTokenByCardAndAcquirer.output, tt.getTokenByCardAndAcquirer.outputErr)

			res, err := s.GetByCardAndAcquirer(context.Background(), tt.reqCardID, tt.reqAcquirerID)
			if tt.wantErr != nil {
				require.Error(t, err)
				require.Equal(t, tt.wantErr, err)
			} else {
				require.NoError(t, err)
			}

			require.Equal(t, tt.want, res)
		})
	}
}

func TestSaveToken(t *testing.T) {
	testNow := time.Date(2009, time.November, 10, 23, 0, 0, 0, time.UTC)

	type getByIDOp struct {
		input     uint64
		output    model.Card
		outputErr error
	}

	type getCardTokenByCardAndAcquirerOp struct {
		isCalled                     bool
		inputCardId, inputAcquirerId uint64
		output                       model.Token
		outputErr                    error
	}

	type createOp struct {
		isCalled  bool
		input     model.Token
		outputErr error
	}

	type updateOp struct {
		isCalled  bool
		inputID   uint64
		input     *goevents.SaveToken
		outputErr error
	}

	type updateApprovedOp struct {
		isCalled  bool
		inputID   uint64
		outputErr error
	}

	tests := []struct {
		name                     string
		req                      *goevents.SaveToken
		wantErr                  error
		getByID                  getByIDOp
		getCardByCardAndAcquirer getCardTokenByCardAndAcquirerOp
		create                   createOp
		update                   updateOp
		updateApproved           updateApprovedOp
	}{
		{
			name: "error_getting_by_id",
			req: &goevents.SaveToken{
				AcquirerId: 11,
				ProjectId:  10,
				TerminalId: 12,
				CardId:     13,
				Token:      "some token",
			},
			wantErr: errors.New("some error"),
			getByID: getByIDOp{
				input:     13,
				output:    model.Card{},
				outputErr: errors.New("some error"),
			},
		},
		{
			name: "save_access_is_false",
			req: &goevents.SaveToken{
				AcquirerId: 11,
				ProjectId:  10,
				TerminalId: 12,
				CardId:     13,
				Token:      "some token",
			},
			wantErr: nil,
			getByID: getByIDOp{
				input: 13,
				output: model.Card{
					Id:         22,
					SaveAccess: false,
				},
				outputErr: nil,
			},
		},
		{
			name: "get_by_card_and_acquirer_error",
			req: &goevents.SaveToken{
				AcquirerId: 11,
				ProjectId:  10,
				TerminalId: 12,
				CardId:     13,
				Token:      "some token",
			},
			wantErr: errors.New("some error"),
			getByID: getByIDOp{
				input: 13,
				output: model.Card{
					Id:         13,
					SaveAccess: true,
				},
				outputErr: nil,
			},
			getCardByCardAndAcquirer: getCardTokenByCardAndAcquirerOp{
				isCalled:        true,
				inputAcquirerId: 11,
				inputCardId:     13,
				output:          model.Token{},
				outputErr:       errors.New("some error"),
			},
		},
		{
			name: "token_id_is_0_error_creation",
			req: &goevents.SaveToken{
				AcquirerId: 11,
				ProjectId:  10,
				TerminalId: 12,
				CardId:     13,
				Token:      "some token",
			},
			wantErr: errors.New("some error"),
			getByID: getByIDOp{
				input: 13,
				output: model.Card{
					Id:         13,
					SaveAccess: true,
				},
				outputErr: nil,
			},
			getCardByCardAndAcquirer: getCardTokenByCardAndAcquirerOp{
				isCalled:        true,
				inputAcquirerId: 11,
				inputCardId:     13,
				output: model.Token{
					Id: 0,
				},
				outputErr: nil,
			},
			create: createOp{
				isCalled: true,
				input: model.Token{
					ApprovedAt: testNow,
					AcquirerId: 11,
					CardId:     13,
					Token:      "some token",
					TerminalId: 12,
				},
				outputErr: errors.New("some error"),
			},
		},
		{
			name: "update_approved_error",
			req: &goevents.SaveToken{
				AcquirerId: 11,
				ProjectId:  10,
				TerminalId: 12,
				CardId:     13,
				Token:      "some token",
			},
			wantErr: errors.New("some error"),
			getByID: getByIDOp{
				input: 13,
				output: model.Card{
					Id:         13,
					SaveAccess: true,
				},
				outputErr: nil,
			},
			getCardByCardAndAcquirer: getCardTokenByCardAndAcquirerOp{
				isCalled:        true,
				inputAcquirerId: 11,
				inputCardId:     13,
				output: model.Token{
					Id: 13,
				},
				outputErr: nil,
			},
			create: createOp{
				isCalled: false,
				input: model.Token{
					ApprovedAt: testNow,
					AcquirerId: 11,
					CardId:     13,
					Token:      "some token",
					TerminalId: 12,
				},
				outputErr: errors.New("some error"),
			},
			update: updateOp{
				isCalled: true,
				input: &goevents.SaveToken{
					AcquirerId: 11,
					ProjectId:  10,
					TerminalId: 12,
					CardId:     13,
					Token:      "some token",
				},
				inputID:   13,
				outputErr: nil,
			},
			updateApproved: updateApprovedOp{
				isCalled:  true,
				inputID:   13,
				outputErr: errors.New("some error"),
			},
		},
		{
			name: "token_id_is_not_0_error_creation",
			req: &goevents.SaveToken{
				AcquirerId: 11,
				ProjectId:  10,
				TerminalId: 12,
				CardId:     13,
				Token:      "some token",
			},
			wantErr: errors.New("some error"),
			getByID: getByIDOp{
				input: 13,
				output: model.Card{
					Id:         13,
					SaveAccess: true,
				},
				outputErr: nil,
			},
			getCardByCardAndAcquirer: getCardTokenByCardAndAcquirerOp{
				isCalled:        true,
				inputAcquirerId: 11,
				inputCardId:     13,
				output: model.Token{
					Id: 13,
				},
				outputErr: nil,
			},
			create: createOp{
				isCalled: false,
				input: model.Token{
					ApprovedAt: testNow,
					AcquirerId: 11,
					CardId:     13,
					Token:      "some token",
					TerminalId: 12,
				},
				outputErr: errors.New("some error"),
			},
			update: updateOp{
				isCalled: true,
				input: &goevents.SaveToken{
					AcquirerId: 11,
					ProjectId:  10,
					TerminalId: 12,
					CardId:     13,
					Token:      "some token",
				},
				inputID:   13,
				outputErr: errors.New("some error"),
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctrl := gomock.NewController(t)

			ctx, cancel := context.WithCancel(context.Background())
			e2e.InitAppE2E(t, ctx, cancel, nil, nil, nil)

			cardDBMock := databasemocks.NewMockCarder(ctrl)
			cardTokenerDBMock := databasemocks.NewMockCardTokener(ctrl)
			tokenUpdatorDBMock := databasemocks.NewMockTokenUpdator(ctrl)
			cardUpdatorDBMock := databasemocks.NewMockCardUpdator(ctrl)

			cardDBMock.EXPECT().GetByID(
				gomock.Any(),
				tt.getByID.input,
			).Return(
				tt.getByID.output, tt.getByID.outputErr,
			)

			if tt.getCardByCardAndAcquirer.isCalled {
				cardTokenerDBMock.EXPECT().GetByCardAndAcquirer(
					gomock.Any(),
					tt.getCardByCardAndAcquirer.inputCardId,
					tt.getCardByCardAndAcquirer.inputAcquirerId,
				).Return(
					tt.getCardByCardAndAcquirer.output,
					tt.getCardByCardAndAcquirer.outputErr,
				)
			}

			if tt.update.isCalled {
				tokenUpdatorDBMock.EXPECT().Update(
					gomock.Any(),
					tt.update.inputID,
					tt.update.input,
				).Return(
					tt.update.outputErr,
				)
			}

			if tt.create.isCalled {
				cardTokenerDBMock.EXPECT().Create(
					gomock.Any(),
					tt.create.input,
				).Return(
					tt.create.outputErr,
				)
			}

			if tt.updateApproved.isCalled {
				cardUpdatorDBMock.EXPECT().UpdateApproved(
					gomock.Any(),
					tt.updateApproved.inputID,
					true,
				).Return(
					tt.updateApproved.outputErr,
				)
			}

			s := TokenService{
				cardsRepo:        cardDBMock,
				cardTokenerRepo:  cardTokenerDBMock,
				tokenUpdatorRepo: tokenUpdatorDBMock,
				cardUpdatorRepo:  cardUpdatorDBMock,
				getCurrentTime: func() time.Time {
					return testNow
				},
			}

			err := s.SaveToken(context.Background(), tt.req)
			if tt.wantErr != nil {
				require.Error(t, err)
				require.Equal(t, tt.wantErr, err)
			} else {
				require.NoError(t, err)
			}

		})
	}
}

func TestDeactivateTokenByEncryptedID(t *testing.T) {
	type getCardByIDOp struct {
		isCalled  bool
		inputID   uint64
		output    model.Card
		outputErr error
	}

	type getClientByIDOp struct {
		isCalled  bool
		inputID   uint64
		output    *model.Client
		outputErr error
	}

	type deactivateClientCardOp struct {
		isCalled  bool
		input     uint64
		outputErr error
	}

	tests := []struct {
		name                 string
		reqEncryptedCardID   string
		reqProjectID         uint64
		wantErr              error
		appConfig            map[string]any
		getCardByID          getCardByIDOp
		getClientByID        getClientByIDOp
		deactivateClientCard deactivateClientCardOp
	}{
		{
			name:               "decryption_error",
			reqEncryptedCardID: "AAAAAAAAAAAAAAAAAAAAAAa1Z7+ZP7uEJjDtmP8f3pAGMN5i3Mol8TluUKlVYuAz",
			reqProjectID:       10,
			wantErr:            aes.KeySizeError(3),
			appConfig: map[string]any{
				"SYMMETRIC_KEY": badKey,
			},
		},
		{
			name:               "unmarshal_error",
			reqEncryptedCardID: "AAAAAAAAAAAAAAAAAAAAAO+CNg5cea7OIc5ASSdKjyM=",
			reqProjectID:       10,
			wantErr:            &json.SyntaxError{},
			appConfig: map[string]any{
				"SYMMETRIC_KEY": normalKey,
			},
		},
		{
			name:               "get_card_by_id_error",
			reqEncryptedCardID: "AAAAAAAAAAAAAAAAAAAAAAa1Z7+ZP7uEJjDtmP8f3pAGMN5i3Mol8TluUKlVYuAz",
			reqProjectID:       10,
			wantErr:            errors.New("some error"),
			appConfig: map[string]any{
				"SYMMETRIC_KEY": normalKey,
			},
			getCardByID: getCardByIDOp{
				isCalled:  true,
				inputID:   1,
				output:    model.Card{},
				outputErr: errors.New("some error"),
			},
		},
		{
			name:               "get_client_by_id_error",
			reqEncryptedCardID: "AAAAAAAAAAAAAAAAAAAAAAa1Z7+ZP7uEJjDtmP8f3pAGMN5i3Mol8TluUKlVYuAz",
			reqProjectID:       10,
			wantErr:            errors.New("some error"),
			appConfig: map[string]any{
				"SYMMETRIC_KEY": normalKey,
			},
			getCardByID: getCardByIDOp{
				isCalled: true,
				inputID:  1,
				output: model.Card{
					ClientId: 11,
				},
				outputErr: nil,
			},
			getClientByID: getClientByIDOp{
				isCalled:  true,
				inputID:   11,
				output:    nil,
				outputErr: errors.New("some error"),
			},
		},
		{
			name:               "project_id_is_not_same",
			reqEncryptedCardID: "AAAAAAAAAAAAAAAAAAAAAAa1Z7+ZP7uEJjDtmP8f3pAGMN5i3Mol8TluUKlVYuAz",
			reqProjectID:       10,
			wantErr:            goerr.ErrProjectNotFound,
			appConfig: map[string]any{
				"SYMMETRIC_KEY": normalKey,
			},
			getCardByID: getCardByIDOp{
				isCalled: true,
				inputID:  1,
				output: model.Card{
					ClientId: 11,
				},
				outputErr: nil,
			},
			getClientByID: getClientByIDOp{
				isCalled: true,
				inputID:  11,
				output: &model.Client{
					ProjectId: 12,
				},
				outputErr: nil,
			},
		},
		{
			name:               "deactivate_card_client_error",
			reqEncryptedCardID: "AAAAAAAAAAAAAAAAAAAAAAa1Z7+ZP7uEJjDtmP8f3pAGMN5i3Mol8TluUKlVYuAz",
			reqProjectID:       10,
			wantErr:            errors.New("some error"),
			appConfig: map[string]any{
				"SYMMETRIC_KEY": normalKey,
			},
			getCardByID: getCardByIDOp{
				isCalled: true,
				inputID:  1,
				output: model.Card{
					ClientId: 11,
				},
				outputErr: nil,
			},
			getClientByID: getClientByIDOp{
				isCalled: true,
				inputID:  11,
				output: &model.Client{
					ProjectId: 10,
				},
				outputErr: nil,
			},
			deactivateClientCard: deactivateClientCardOp{
				isCalled:  true,
				input:     1,
				outputErr: errors.New("some error"),
			},
		},
		{
			name:               "success",
			reqEncryptedCardID: "AAAAAAAAAAAAAAAAAAAAAAa1Z7+ZP7uEJjDtmP8f3pAGMN5i3Mol8TluUKlVYuAz",
			reqProjectID:       10,
			wantErr:            nil,
			appConfig: map[string]any{
				"SYMMETRIC_KEY": normalKey,
			},
			getCardByID: getCardByIDOp{
				isCalled: true,
				inputID:  1,
				output: model.Card{
					ClientId: 11,
				},
				outputErr: nil,
			},
			getClientByID: getClientByIDOp{
				isCalled: true,
				inputID:  11,
				output: &model.Client{
					ProjectId: 10,
				},
				outputErr: nil,
			},
			deactivateClientCard: deactivateClientCardOp{
				isCalled:  true,
				input:     1,
				outputErr: nil,
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctrl := gomock.NewController(t)

			ctx, cancel := context.WithCancel(context.Background())
			e2e.InitAppE2E(t, ctx, cancel, nil, nil, tt.appConfig)

			cardsDBMock := databasemocks.NewMockCarder(ctrl)
			clientsDBMock := databasemocks.NewMockClientor(ctrl)
			cardUpdatorDBMock := databasemocks.NewMockCardUpdator(ctrl)

			if tt.getCardByID.isCalled {
				cardsDBMock.EXPECT().GetByID(
					gomock.Any(),
					tt.getCardByID.inputID,
				).Return(
					tt.getCardByID.output, tt.getCardByID.outputErr,
				)
			}

			if tt.getClientByID.isCalled {
				clientsDBMock.EXPECT().GetByID(
					gomock.Any(),
					tt.getClientByID.inputID,
				).Return(
					tt.getClientByID.output,
					tt.getClientByID.outputErr,
				)
			}

			if tt.deactivateClientCard.isCalled {
				cardUpdatorDBMock.EXPECT().DeactivateClientCard(
					gomock.Any(),
					tt.deactivateClientCard.input,
				).Return(
					tt.deactivateClientCard.outputErr,
				)
			}

			s := TokenService{
				cardsRepo:       cardsDBMock,
				clientsRepo:     clientsDBMock,
				cardUpdatorRepo: cardUpdatorDBMock,
			}

			err := s.DeactivateTokenByEncryptedCardId(
				context.Background(),
				tt.reqEncryptedCardID,
				tt.reqProjectID,
			)
			if tt.wantErr != nil {
				require.Error(t, err)
				require.ErrorAs(t, err, &tt.wantErr)
			} else {
				require.NoError(t, err)
			}
		})
	}
}
