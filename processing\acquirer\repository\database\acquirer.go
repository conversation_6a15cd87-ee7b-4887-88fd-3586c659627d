package database

import (
	"context"

	"gorm.io/gorm"

	"git.local/sensitive/innerpb/processing/goerr"
	"git.local/sensitive/processing/acquirer/model"
	"git.local/sensitive/sdk/dog"
)

type AcquirerDB struct {
	db *gorm.DB
}

func NewAcquirerDB(db *gorm.DB) Acquirer {
	return &AcquirerDB{
		db: db,
	}
}

func (r *AcquirerDB) GetAcquirersById(ctx context.Context, acquirerIds []uint64) (_ []*model.Acquirer, err error) {
	ctx, span := dog.CreateSpan(ctx, "AcquirerDB_GetAcquirersById")
	defer span.End()

	acquirers := make([]*model.Acquirer, 0)

	request := r.db.WithContext(ctx)

	err = request.
		Where("id IN ?", acquirerIds).
		Find(&acquirers).
		Error
	if err != nil {
		return nil, goerr.ErrDbUnexpected.WithErr(err).WithCtx(ctx)
	}

	return acquirers, nil
}

func (r *AcquirerDB) UpdateStatus(ctx context.Context, id uint64, isActive bool) (err error) {
	ctx, span := dog.CreateSpan(ctx, "AcquirerDB_UpdateStatus")
	defer span.End()

	request := r.db.WithContext(ctx).
		Model(&model.Acquirer{})

	err = request.
		Where("id = ?", id).
		Update("is_active", isActive).
		Error
	if err != nil {
		return goerr.ErrDbUnexpected.WithErr(err).WithCtx(ctx)
	}

	return nil
}
