package database

import (
	"context"
	"errors"

	"gorm.io/gorm"

	"git.local/sensitive/innerpb/processing/goerr"
	"git.local/sensitive/pkg/middlewares"
	"git.local/sensitive/processing/acquirer/model"
	"git.local/sensitive/processing/acquirer/schema"
	"git.local/sensitive/sdk/dog"
)

type BankDB struct {
	db *gorm.DB
}

func NewBankDB(db *gorm.DB) Banker {
	return &BankDB{
		db: db,
	}
}

func (b *BankDB) Create(ctx context.Context, bank *model.Bank) (err error) {
	ctx, span := dog.CreateSpan(ctx, "BankDB_Create")
	defer span.End()

	if bank == nil {
		return goerr.ErrParseErrorBody.WithCtx(ctx)
	}

	err = b.db.WithContext(ctx).Create(&bank).Error
	if err != nil {
		return goerr.ErrDbUnexpected.WithErr(err).WithCtx(ctx)
	}

	return nil
}

func (b *BankDB) GetAll(
	ctx context.Context,
	pagination *middlewares.PaginationInfo,
	filter schema.BankFilter,
) (_ []*model.Bank, err error) {
	ctx, span := dog.CreateSpan(ctx, "BankDB_GetAll")
	defer span.End()

	result := make([]*model.Bank, 0)

	request := b.db.WithContext(ctx).
		Model(&model.Bank{})

	if filter.Search != "" {
		request.Where("name ILIKE ?", "%"+filter.Search+"%")
	}

	if pagination.Pagination {
		request = request.Offset((pagination.Page - 1) * pagination.PerPage).Limit(pagination.PerPage)
	}

	if err := request.Find(&result).Error; err != nil {
		return nil, goerr.ErrDbUnexpected.WithErr(err).WithCtx(ctx)
	}

	return result, nil
}

func (b *BankDB) Update(ctx context.Context, id uint64, data *model.Bank) (err error) {
	ctx, span := dog.CreateSpan(ctx, "BankDB_Update")
	defer span.End()

	if data == nil {
		return goerr.ErrParseErrorBody.WithCtx(ctx)
	}

	err = b.db.WithContext(ctx).
		Model(&model.Bank{}).
		Where("id = ?", id).
		Updates(model.Bank{
			Name:  data.Name,
			Bik:   data.Bik,
			Swift: data.Swift,
		}).First(&model.Bank{}).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return goerr.ErrBankNotFound.WithCtx(ctx)
		}

		return goerr.ErrDbUnexpected.WithErr(err).WithCtx(ctx)
	}

	return nil
}

func (b *BankDB) Delete(ctx context.Context, id uint64) (err error) {
	ctx, span := dog.CreateSpan(ctx, "BankDB_Update")
	defer span.End()

	err = b.db.WithContext(ctx).
		Where("id = ?", id).
		First(&model.Bank{}).
		Delete(&model.Bank{}, id).
		Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return goerr.ErrBankNotFound.WithCtx(ctx)
		}

		return goerr.ErrDbUnexpected.WithErr(err).WithCtx(ctx)
	}

	return nil
}

func (b *BankDB) GetById(ctx context.Context, id uint64) (bank *model.Bank, err error) {
	ctx, span := dog.CreateSpan(ctx, "BankDB_GetById")
	defer span.End()

	err = b.db.WithContext(ctx).
		Where("id = ?", id).
		Find(&bank).
		Error

	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, goerr.ErrBankNotFound.WithCtx(ctx)
		}

		return nil, goerr.ErrDbUnexpected.WithErr(err).WithCtx(ctx)
	}

	return bank, nil
}

func (b *BankDB) GetByName(
	ctx context.Context, name string, pagination *middlewares.PaginationInfo,
) (_ []*model.Bank, err error) {
	ctx, span := dog.CreateSpan(ctx, "BankDB_GetByName")

	defer span.End()

	bank := make([]*model.Bank, 0)

	request := b.db.WithContext(ctx).Model(&model.Bank{}).Where("name ILIKE  ?", "%"+name+"%")

	if pagination == nil || !pagination.Pagination {
		if err = request.Order("id ASC").Find(&bank).
			Error; err != nil {
			return nil, goerr.ErrDbUnexpected.WithErr(err).WithCtx(ctx)
		}

		return bank, nil
	}

	if err = request.WithContext(ctx).Model(&model.Bank{}).
		Count(&pagination.Total).Error; err != nil {
		return nil, goerr.ErrDbUnexpected.WithErr(err).WithCtx(ctx)
	}

	if err = request.
		Offset((pagination.Page - 1) * pagination.PerPage).
		Limit(pagination.PerPage).
		Find(&bank).Order("id ASC").Error; err != nil {
		return nil, goerr.ErrDbUnexpected.WithErr(err).WithCtx(ctx)
	}

	return bank, err
}
