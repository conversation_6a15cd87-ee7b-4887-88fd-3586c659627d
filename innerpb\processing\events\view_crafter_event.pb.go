// Code generated by protoc-gen-go-mvp. DO NOT EDIT.
// versions:
// - protoc-gen-go-mvp v2.0.1
// created by mvp authors

package events

import (
	context "context"
	errors "errors"
	fmt "fmt"
	pb "git.local/sensitive/mvp/pb"
	watermill "github.com/ThreeDotsLabs/watermill"
	nats "github.com/ThreeDotsLabs/watermill-nats/v2/pkg/nats"
	message "github.com/ThreeDotsLabs/watermill/message"
	uuid "github.com/google/uuid"
	nats_go "github.com/nats-io/nats.go"
	otel "go.opentelemetry.io/otel"
	attribute "go.opentelemetry.io/otel/attribute"
	propagation "go.opentelemetry.io/otel/propagation"
	zap "go.uber.org/zap"
	protojson "google.golang.org/protobuf/encoding/protojson"
	anypb "google.golang.org/protobuf/types/known/anypb"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	strings "strings"
	time "time"
)

const subjectNameCreateSettingV1 = "view_crafter.createsettingv1"

type eventCreateSettingV1 struct {
	jsContext         nats_go.JetStreamContext
	natsConn          *nats_go.Conn
	natsMessageRouter *message.Router
}

func newCreateSettingV1(
	jsContext nats_go.JetStreamContext,
	conn *nats_go.Conn,
	router *message.Router,
) *eventCreateSettingV1 {
	return &eventCreateSettingV1{
		jsContext:         jsContext,
		natsConn:          conn,
		natsMessageRouter: router,
	}
}

var singleCreateSettingV1 func(ctx context.Context) (<-chan *CreateSettingV1, error)

func NewCreateSettingV1Subscriber(
	js nats_go.JetStreamContext,
	conn *nats_go.Conn,
	router *message.Router,
) func(ctx context.Context) (<-chan *CreateSettingV1, error) {
	if singleCreateSettingV1 == nil {
		singleCreateSettingV1 = newCreateSettingV1(js, conn, router).Subscribe
	}

	return singleCreateSettingV1
}

func NewCreateSettingV1Publisher(
	js nats_go.JetStreamContext,
) func(ctx context.Context, src *CreateSettingV1) error {
	return newCreateSettingV1(js, nil, nil).Publish
}

func (s *eventCreateSettingV1) Publish(ctx context.Context, src *CreateSettingV1) error {
	var err error
	eventId := uuid.New().String()

	ctx, span := otel.Tracer("otel_trace").Start(ctx, "CreateSettingV1.Publish")
	defer span.End()
	defer func() {
		span.RecordError(err)
		span.SetAttributes(
			attribute.Bool("error", err != nil),
			attribute.String("nats_uuid", eventId),
		)
	}()

	m, err := anypb.New(src)
	if err != nil {
		return err
	}

	data := pb.EventInfo{
		EventId:   &eventId,
		EventTime: timestamppb.Now(),
		Data:      m,
	}
	marshal, err := protojson.Marshal(&data)
	if err != nil {
		return err
	}

	header := make(nats_go.Header)
	otel.GetTextMapPropagator().Inject(ctx, propagation.HeaderCarrier(header))

	_, err = s.jsContext.PublishMsg(&nats_go.Msg{
		Subject: subjectNameCreateSettingV1,
		Data:    marshal,
		Header:  header,
	})

	return err
}

func (s *eventCreateSettingV1) Subscribe(ctx context.Context) (<-chan *CreateSettingV1, error) {
	ch := make(chan *CreateSettingV1)

	streamCfg := newCreateSettingV1StreamConfig()
	consumerCfg := newCreateSettingV1ConsumerConfig()
	watermillCfg := newCreateSettingV1WatermillConfigs()

	fun := func(msg *message.Message) error {
		data := new(pb.EventInfo)
		resCreateSettingV1 := new(CreateSettingV1)
		if err := protojson.Unmarshal(msg.Payload, data); err != nil {
			return err
		}

		if err := data.GetData().UnmarshalTo(resCreateSettingV1); err != nil {
			return err
		}

		ch <- resCreateSettingV1

		return nil
	}

	err := s.createConsumerCreateSettingV1(streamCfg, consumerCfg)
	if err != nil {
		return nil, err
	}
	subscriber, err := nats.NewSubscriberWithNatsConn(
		s.natsConn,
		*watermillCfg,
		newZapLoggerAdapterCreateSettingV1(zap.L()),
	)
	if err != nil {
		return nil, fmt.Errorf("err watermillNats.NewSubscriberWithNatsConn: %w", err)
	}
	go func() {
		select {
		case <-ctx.Done():
			if err := subscriber.Close(); err != nil {
				zap.L().Error("subscriber.Close", zap.Error(err))
			}
		}
	}()

	s.natsMessageRouter.AddNoPublisherHandler(
		consumerCfg.Name,
		subjectNameCreateSettingV1,
		subscriber,
		fun,
	)

	return ch, nil
}

func newCreateSettingV1StreamConfig() *nats_go.StreamConfig {
	return &nats_go.StreamConfig{
		Name:         strings.ReplaceAll(subjectNameCreateSettingV1, ".", "_"),
		Subjects:     []string{subjectNameCreateSettingV1, fmt.Sprintf("%s.>", subjectNameCreateSettingV1)},
		Retention:    nats_go.LimitsPolicy,
		MaxConsumers: -1,
		Discard:      nats_go.DiscardOld,
		MaxAge:       time.Hour * 24 * 7,
		Storage:      nats_go.FileStorage,
		NoAck:        false,
		AllowRollup:  true,
	}
}

func newCreateSettingV1ConsumerConfig() *nats_go.ConsumerConfig {
	defaultAckWait := time.Second * 30

	streamNameWithoutDots := strings.ReplaceAll(subjectNameCreateSettingV1, ".", "_")

	consumerName := strings.ReplaceAll(fmt.Sprintf("%s_%s",
		"events",
		streamNameWithoutDots), ".", "_")

	return &nats_go.ConsumerConfig{
		Durable:        consumerName,
		Name:           consumerName,
		AckPolicy:      nats_go.AckExplicitPolicy,
		AckWait:        defaultAckWait,
		MaxDeliver:     -1,
		DeliverGroup:   consumerName,
		DeliverSubject: consumerName,
	}
}

func newCreateSettingV1WatermillConfigs() *nats.SubscriberSubscriptionConfig {
	defaultAckWait := time.Second * 30

	streamNameWithoutDots := strings.ReplaceAll(subjectNameCreateSettingV1, ".", "_")

	consumerName := strings.ReplaceAll(fmt.Sprintf("%s_%s",
		"events",
		streamNameWithoutDots), ".", "_")

	return &nats.SubscriberSubscriptionConfig{
		Unmarshaler:    &eventMarshalerCreateSettingV1{},
		AckWaitTimeout: defaultAckWait,
		CloseTimeout:   defaultAckWait,
		SubjectCalculator: func(queueGroupPrefix, topic string) *nats.SubjectDetail {
			return &nats.SubjectDetail{
				Primary:    subjectNameCreateSettingV1,
				QueueGroup: consumerName,
			}
		},
		JetStream: nats.JetStreamConfig{
			Disabled:      false,
			AutoProvision: false,
			SubscribeOptions: []nats_go.SubOpt{
				nats_go.ManualAck(),
				nats_go.DeliverAll(),
				nats_go.AckExplicit(),
				nats_go.Bind(streamNameWithoutDots, consumerName),
				nats_go.MaxDeliver(-1),
			},
			TrackMsgId:        true,
			AckAsync:          false,
			DurableCalculator: func(prefix, topic string) string { return consumerName },
		},
	}
}

func (s *eventCreateSettingV1) createConsumerCreateSettingV1(streamCfg *nats_go.StreamConfig, consumerCfg *nats_go.ConsumerConfig) error {
	_, err := s.jsContext.StreamInfo(streamCfg.Name)
	if err != nil {
		if errors.Is(err, nats_go.ErrStreamNotFound) {
			_, err = s.jsContext.AddStream(streamCfg)
			if err != nil {
				return fmt.Errorf("err jsContext.AddStream: %w", err)
			}
		} else {
			return fmt.Errorf("can't get NATS JetStream Stream: %w", err)
		}
	}

	_, err = s.jsContext.UpdateStream(streamCfg)
	if err != nil {
		return fmt.Errorf("err jsContext.UpdateStream: %w", err)
	}

	_, err = s.jsContext.ConsumerInfo(streamCfg.Name, streamCfg.Name)
	if err != nil {
		if errors.Is(err, nats_go.ErrConsumerNotFound) {
			_, err = s.jsContext.AddConsumer(streamCfg.Name, consumerCfg)
			if err != nil {
				return fmt.Errorf("err jsContext.AddConsumer: %w", err)
			}
		} else {
			return fmt.Errorf("can't get NATS JetStream Consumer %s: %w", consumerCfg.Name, err)
		}
	}

	_, err = s.jsContext.UpdateConsumer(streamCfg.Name, consumerCfg)
	if err != nil {
		return fmt.Errorf("err jsContext.UpdateConsumer: %w", err)
	}

	return nil
}

type eventMarshalerCreateSettingV1 struct{}

func (*eventMarshalerCreateSettingV1) Marshal(topic string, msg *message.Message) (*nats_go.Msg, error) {
	natsMsg := nats_go.NewMsg(topic)
	natsMsg.Data = msg.Payload

	return natsMsg, nil
}
func (*eventMarshalerCreateSettingV1) Unmarshal(msg *nats_go.Msg) (*message.Message, error) {
	pbMsg := &pb.EventInfo{}
	err := protojson.Unmarshal(msg.Data, pbMsg)
	if err != nil {
		return nil, err
	}

	wmMsg := message.NewMessage(pbMsg.GetEventId(), msg.Data)
	wmMsg.Metadata = map[string]string{}

	return wmMsg, nil
}

type zapLoggerAdapterCreateSettingV1 struct {
	logger *zap.Logger
}

func newZapLoggerAdapterCreateSettingV1(logger *zap.Logger) *zapLoggerAdapterCreateSettingV1 {
	return &zapLoggerAdapterCreateSettingV1{logger: logger}
}

func (z zapLoggerAdapterCreateSettingV1) Error(msg string, err error, fields watermill.LogFields) {
	z.logger.Error(msg, append(z.toZapFields(fields), zap.Error(err))...)
}

func (z zapLoggerAdapterCreateSettingV1) Info(msg string, fields watermill.LogFields) {
	z.logger.Info(msg, z.toZapFields(fields)...)
}

func (z zapLoggerAdapterCreateSettingV1) Debug(msg string, fields watermill.LogFields) {
	z.logger.Debug(msg, z.toZapFields(fields)...)
}

func (z zapLoggerAdapterCreateSettingV1) Trace(msg string, fields watermill.LogFields) {
	z.logger.Debug(msg, z.toZapFields(fields)...)
}

func (z zapLoggerAdapterCreateSettingV1) With(fields watermill.LogFields) watermill.LoggerAdapter {
	newLogger := z.logger.With(z.toZapFields(fields)...)
	return &zapLoggerAdapterCreateSettingV1{logger: newLogger}
}

func (z zapLoggerAdapterCreateSettingV1) toZapFields(fields watermill.LogFields) []zap.Field {
	zapFields := make([]zap.Field, 0, len(fields))
	for k, v := range fields {
		zapFields = append(zapFields, zap.Any(k, v))
	}

	return zapFields
}
