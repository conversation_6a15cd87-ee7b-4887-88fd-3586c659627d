// Code generated by protoc-gen-go-mvp. DO NOT EDIT.
// versions:
// - protoc-gen-go-mvp v2.0.1
// created by mvp authors

package grpc

import (
	context "context"
	grpc "google.golang.org/grpc"
	metadata "google.golang.org/grpc/metadata"
)

func NewIamTerminalCardsServer(
	srv TerminalCardsServer,
) TerminalCardsServer {
	return &iamTerminalCardsServer{
		srv: srv,
	}
}

var _ TerminalCardsServer = (*iamTerminalCardsServer)(nil)

type iamTerminalCardsServer struct {
	UnimplementedTerminalCardsServer

	srv TerminalCardsServer
}

func (s *iamTerminalCardsServer) GetCardInfoByPan(
	ctx context.Context,
	req *GetCardInfoByPanRequestV1,
) (
	*GetCardInfoByPanResponseV1,
	error,
) {
	return s.srv.GetCardInfoByPan(ctx, req)
}

func NewIamTerminalCardsClient(
	client TerminalCardsClient,
) TerminalCardsClient {
	return &iamTerminalCardsClient{
		client: client,
	}
}

type iamTerminalCardsClient struct {
	client TerminalCardsClient
}

func (s *iamTerminalCardsClient) GetCardInfoByPan(
	ctx context.Context,
	req *GetCardInfoByPanRequestV1,
	opts ...grpc.CallOption,
) (
	*GetCardInfoByPanResponseV1,
	error,
) {
	md, _ := metadata.FromIncomingContext(ctx)
	return s.client.GetCardInfoByPan(metadata.NewOutgoingContext(ctx, md), req)
}
