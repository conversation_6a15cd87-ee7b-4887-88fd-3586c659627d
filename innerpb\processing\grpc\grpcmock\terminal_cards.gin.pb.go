// Code generated by MockGen. DO NOT EDIT.
// Source: terminal_cards.gin.pb.go

// Package grpcmock is a generated GoMock package.
package grpcmock

import (
	reflect "reflect"

	gin "github.com/gin-gonic/gin"
	gomock "github.com/golang/mock/gomock"
)

// MockGinTerminalCardsServer is a mock of GinTerminalCardsServer interface.
type MockGinTerminalCardsServer struct {
	ctrl     *gomock.Controller
	recorder *MockGinTerminalCardsServerMockRecorder
}

// MockGinTerminalCardsServerMockRecorder is the mock recorder for MockGinTerminalCardsServer.
type MockGinTerminalCardsServerMockRecorder struct {
	mock *MockGinTerminalCardsServer
}

// NewMockGinTerminalCardsServer creates a new mock instance.
func NewMockGinTerminalCardsServer(ctrl *gomock.Controller) *MockGinTerminalCardsServer {
	mock := &MockGinTerminalCardsServer{ctrl: ctrl}
	mock.recorder = &MockGinTerminalCardsServerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockGinTerminalCardsServer) EXPECT() *MockGinTerminalCardsServerMockRecorder {
	return m.recorder
}

// GetCardInfoByPan mocks base method.
func (m *MockGinTerminalCardsServer) GetCardInfoByPan(c *gin.Context) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetCardInfoByPan", c)
	ret0, _ := ret[0].(error)
	return ret0
}

// GetCardInfoByPan indicates an expected call of GetCardInfoByPan.
func (mr *MockGinTerminalCardsServerMockRecorder) GetCardInfoByPan(c interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetCardInfoByPan", reflect.TypeOf((*MockGinTerminalCardsServer)(nil).GetCardInfoByPan), c)
}
