// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.5
// 	protoc        v5.29.3
// source: inner/processing/grpc/notification.proto

package grpc

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type GetMailReqDataV1 struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	UserEmail     *string                `protobuf:"bytes,1,opt,name=user_email,json=userEmail" json:"user_email,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetMailReqDataV1) Reset() {
	*x = GetMailReqDataV1{}
	mi := &file_inner_processing_grpc_notification_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetMailReqDataV1) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetMailReqDataV1) ProtoMessage() {}

func (x *GetMailReqDataV1) ProtoReflect() protoreflect.Message {
	mi := &file_inner_processing_grpc_notification_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetMailReqDataV1.ProtoReflect.Descriptor instead.
func (*GetMailReqDataV1) Descriptor() ([]byte, []int) {
	return file_inner_processing_grpc_notification_proto_rawDescGZIP(), []int{0}
}

func (x *GetMailReqDataV1) GetUserEmail() string {
	if x != nil && x.UserEmail != nil {
		return *x.UserEmail
	}
	return ""
}

type GetMailResDataV1 struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Sender        *string                `protobuf:"bytes,1,opt,name=sender" json:"sender,omitempty"`
	Body          *string                `protobuf:"bytes,2,opt,name=body" json:"body,omitempty"`
	Payload       map[string]string      `protobuf:"bytes,3,rep,name=payload" json:"payload,omitempty" protobuf_key:"bytes,1,opt,name=key" protobuf_val:"bytes,2,opt,name=value"`
	Receivers     []string               `protobuf:"bytes,4,rep,name=receivers" json:"receivers,omitempty"`
	CreatedAt     *timestamppb.Timestamp `protobuf:"bytes,5,opt,name=created_at,json=createdAt" json:"created_at,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetMailResDataV1) Reset() {
	*x = GetMailResDataV1{}
	mi := &file_inner_processing_grpc_notification_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetMailResDataV1) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetMailResDataV1) ProtoMessage() {}

func (x *GetMailResDataV1) ProtoReflect() protoreflect.Message {
	mi := &file_inner_processing_grpc_notification_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetMailResDataV1.ProtoReflect.Descriptor instead.
func (*GetMailResDataV1) Descriptor() ([]byte, []int) {
	return file_inner_processing_grpc_notification_proto_rawDescGZIP(), []int{1}
}

func (x *GetMailResDataV1) GetSender() string {
	if x != nil && x.Sender != nil {
		return *x.Sender
	}
	return ""
}

func (x *GetMailResDataV1) GetBody() string {
	if x != nil && x.Body != nil {
		return *x.Body
	}
	return ""
}

func (x *GetMailResDataV1) GetPayload() map[string]string {
	if x != nil {
		return x.Payload
	}
	return nil
}

func (x *GetMailResDataV1) GetReceivers() []string {
	if x != nil {
		return x.Receivers
	}
	return nil
}

func (x *GetMailResDataV1) GetCreatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.CreatedAt
	}
	return nil
}

type GetSMSReqDataV1 struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	UserPhone     *string                `protobuf:"bytes,1,opt,name=user_phone,json=userPhone" json:"user_phone,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetSMSReqDataV1) Reset() {
	*x = GetSMSReqDataV1{}
	mi := &file_inner_processing_grpc_notification_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetSMSReqDataV1) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetSMSReqDataV1) ProtoMessage() {}

func (x *GetSMSReqDataV1) ProtoReflect() protoreflect.Message {
	mi := &file_inner_processing_grpc_notification_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetSMSReqDataV1.ProtoReflect.Descriptor instead.
func (*GetSMSReqDataV1) Descriptor() ([]byte, []int) {
	return file_inner_processing_grpc_notification_proto_rawDescGZIP(), []int{2}
}

func (x *GetSMSReqDataV1) GetUserPhone() string {
	if x != nil && x.UserPhone != nil {
		return *x.UserPhone
	}
	return ""
}

type GetSMSResDataV1 struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Sender        *string                `protobuf:"bytes,1,opt,name=sender" json:"sender,omitempty"`
	Body          *string                `protobuf:"bytes,2,opt,name=body" json:"body,omitempty"`
	Payload       map[string]string      `protobuf:"bytes,3,rep,name=payload" json:"payload,omitempty" protobuf_key:"bytes,1,opt,name=key" protobuf_val:"bytes,2,opt,name=value"`
	Receiver      *string                `protobuf:"bytes,4,opt,name=receiver" json:"receiver,omitempty"`
	CreatedAt     *timestamppb.Timestamp `protobuf:"bytes,5,opt,name=created_at,json=createdAt" json:"created_at,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetSMSResDataV1) Reset() {
	*x = GetSMSResDataV1{}
	mi := &file_inner_processing_grpc_notification_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetSMSResDataV1) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetSMSResDataV1) ProtoMessage() {}

func (x *GetSMSResDataV1) ProtoReflect() protoreflect.Message {
	mi := &file_inner_processing_grpc_notification_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetSMSResDataV1.ProtoReflect.Descriptor instead.
func (*GetSMSResDataV1) Descriptor() ([]byte, []int) {
	return file_inner_processing_grpc_notification_proto_rawDescGZIP(), []int{3}
}

func (x *GetSMSResDataV1) GetSender() string {
	if x != nil && x.Sender != nil {
		return *x.Sender
	}
	return ""
}

func (x *GetSMSResDataV1) GetBody() string {
	if x != nil && x.Body != nil {
		return *x.Body
	}
	return ""
}

func (x *GetSMSResDataV1) GetPayload() map[string]string {
	if x != nil {
		return x.Payload
	}
	return nil
}

func (x *GetSMSResDataV1) GetReceiver() string {
	if x != nil && x.Receiver != nil {
		return *x.Receiver
	}
	return ""
}

func (x *GetSMSResDataV1) GetCreatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.CreatedAt
	}
	return nil
}

var File_inner_processing_grpc_notification_proto protoreflect.FileDescriptor

var file_inner_processing_grpc_notification_proto_rawDesc = string([]byte{
	0x0a, 0x28, 0x69, 0x6e, 0x6e, 0x65, 0x72, 0x2f, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69,
	0x6e, 0x67, 0x2f, 0x67, 0x72, 0x70, 0x63, 0x2f, 0x6e, 0x6f, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x24, 0x70, 0x72, 0x6f, 0x63,
	0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x2e, 0x6e, 0x6f, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x2e, 0x6e, 0x6f, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x1a, 0x1f, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75,
	0x66, 0x2f, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x22, 0x31, 0x0a, 0x10, 0x47, 0x65, 0x74, 0x4d, 0x61, 0x69, 0x6c, 0x52, 0x65, 0x71, 0x44,
	0x61, 0x74, 0x61, 0x56, 0x31, 0x12, 0x1d, 0x0a, 0x0a, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x65, 0x6d,
	0x61, 0x69, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x75, 0x73, 0x65, 0x72, 0x45,
	0x6d, 0x61, 0x69, 0x6c, 0x22, 0xb2, 0x02, 0x0a, 0x10, 0x47, 0x65, 0x74, 0x4d, 0x61, 0x69, 0x6c,
	0x52, 0x65, 0x73, 0x44, 0x61, 0x74, 0x61, 0x56, 0x31, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x65, 0x6e,
	0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x73, 0x65, 0x6e, 0x64, 0x65,
	0x72, 0x12, 0x12, 0x0a, 0x04, 0x62, 0x6f, 0x64, 0x79, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x04, 0x62, 0x6f, 0x64, 0x79, 0x12, 0x5d, 0x0a, 0x07, 0x70, 0x61, 0x79, 0x6c, 0x6f, 0x61, 0x64,
	0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x43, 0x2e, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73,
	0x69, 0x6e, 0x67, 0x2e, 0x6e, 0x6f, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x2e, 0x6e, 0x6f, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x47, 0x65,
	0x74, 0x4d, 0x61, 0x69, 0x6c, 0x52, 0x65, 0x73, 0x44, 0x61, 0x74, 0x61, 0x56, 0x31, 0x2e, 0x50,
	0x61, 0x79, 0x6c, 0x6f, 0x61, 0x64, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x07, 0x70, 0x61, 0x79,
	0x6c, 0x6f, 0x61, 0x64, 0x12, 0x1c, 0x0a, 0x09, 0x72, 0x65, 0x63, 0x65, 0x69, 0x76, 0x65, 0x72,
	0x73, 0x18, 0x04, 0x20, 0x03, 0x28, 0x09, 0x52, 0x09, 0x72, 0x65, 0x63, 0x65, 0x69, 0x76, 0x65,
	0x72, 0x73, 0x12, 0x39, 0x0a, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74,
	0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61,
	0x6d, 0x70, 0x52, 0x09, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x1a, 0x3a, 0x0a,
	0x0c, 0x50, 0x61, 0x79, 0x6c, 0x6f, 0x61, 0x64, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a,
	0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12,
	0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05,
	0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0x30, 0x0a, 0x0f, 0x47, 0x65, 0x74,
	0x53, 0x4d, 0x53, 0x52, 0x65, 0x71, 0x44, 0x61, 0x74, 0x61, 0x56, 0x31, 0x12, 0x1d, 0x0a, 0x0a,
	0x75, 0x73, 0x65, 0x72, 0x5f, 0x70, 0x68, 0x6f, 0x6e, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x09, 0x75, 0x73, 0x65, 0x72, 0x50, 0x68, 0x6f, 0x6e, 0x65, 0x22, 0xae, 0x02, 0x0a, 0x0f,
	0x47, 0x65, 0x74, 0x53, 0x4d, 0x53, 0x52, 0x65, 0x73, 0x44, 0x61, 0x74, 0x61, 0x56, 0x31, 0x12,
	0x16, 0x0a, 0x06, 0x73, 0x65, 0x6e, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x06, 0x73, 0x65, 0x6e, 0x64, 0x65, 0x72, 0x12, 0x12, 0x0a, 0x04, 0x62, 0x6f, 0x64, 0x79, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x62, 0x6f, 0x64, 0x79, 0x12, 0x5c, 0x0a, 0x07, 0x70,
	0x61, 0x79, 0x6c, 0x6f, 0x61, 0x64, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x42, 0x2e, 0x70,
	0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x2e, 0x6e, 0x6f, 0x74, 0x69, 0x66, 0x69,
	0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x6e, 0x6f, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x2e, 0x47, 0x65, 0x74, 0x53, 0x4d, 0x53, 0x52, 0x65, 0x73, 0x44, 0x61, 0x74,
	0x61, 0x56, 0x31, 0x2e, 0x50, 0x61, 0x79, 0x6c, 0x6f, 0x61, 0x64, 0x45, 0x6e, 0x74, 0x72, 0x79,
	0x52, 0x07, 0x70, 0x61, 0x79, 0x6c, 0x6f, 0x61, 0x64, 0x12, 0x1a, 0x0a, 0x08, 0x72, 0x65, 0x63,
	0x65, 0x69, 0x76, 0x65, 0x72, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x72, 0x65, 0x63,
	0x65, 0x69, 0x76, 0x65, 0x72, 0x12, 0x39, 0x0a, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64,
	0x5f, 0x61, 0x74, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65,
	0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x09, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74,
	0x1a, 0x3a, 0x0a, 0x0c, 0x50, 0x61, 0x79, 0x6c, 0x6f, 0x61, 0x64, 0x45, 0x6e, 0x74, 0x72, 0x79,
	0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b,
	0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x32, 0xa5, 0x02, 0x0a,
	0x0c, 0x4e, 0x6f, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x8a, 0x01,
	0x0a, 0x16, 0x47, 0x65, 0x74, 0x4c, 0x61, 0x73, 0x74, 0x4d, 0x61, 0x69, 0x6c, 0x42, 0x79, 0x55,
	0x73, 0x65, 0x72, 0x45, 0x6d, 0x61, 0x69, 0x6c, 0x12, 0x36, 0x2e, 0x70, 0x72, 0x6f, 0x63, 0x65,
	0x73, 0x73, 0x69, 0x6e, 0x67, 0x2e, 0x6e, 0x6f, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x2e, 0x6e, 0x6f, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e,
	0x47, 0x65, 0x74, 0x4d, 0x61, 0x69, 0x6c, 0x52, 0x65, 0x71, 0x44, 0x61, 0x74, 0x61, 0x56, 0x31,
	0x1a, 0x36, 0x2e, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x2e, 0x6e, 0x6f,
	0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x6e, 0x6f, 0x74, 0x69, 0x66,
	0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x47, 0x65, 0x74, 0x4d, 0x61, 0x69, 0x6c, 0x52,
	0x65, 0x73, 0x44, 0x61, 0x74, 0x61, 0x56, 0x31, 0x22, 0x00, 0x12, 0x87, 0x01, 0x0a, 0x15, 0x47,
	0x65, 0x74, 0x4c, 0x61, 0x73, 0x74, 0x53, 0x4d, 0x53, 0x42, 0x79, 0x55, 0x73, 0x65, 0x72, 0x50,
	0x68, 0x6f, 0x6e, 0x65, 0x12, 0x35, 0x2e, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e,
	0x67, 0x2e, 0x6e, 0x6f, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x6e,
	0x6f, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x47, 0x65, 0x74, 0x53,
	0x4d, 0x53, 0x52, 0x65, 0x71, 0x44, 0x61, 0x74, 0x61, 0x56, 0x31, 0x1a, 0x35, 0x2e, 0x70, 0x72,
	0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x2e, 0x6e, 0x6f, 0x74, 0x69, 0x66, 0x69, 0x63,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x6e, 0x6f, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x2e, 0x47, 0x65, 0x74, 0x53, 0x4d, 0x53, 0x52, 0x65, 0x73, 0x44, 0x61, 0x74, 0x61,
	0x56, 0x31, 0x22, 0x00, 0x42, 0x2d, 0x5a, 0x2b, 0x67, 0x69, 0x74, 0x2e, 0x6c, 0x6f, 0x63, 0x61,
	0x6c, 0x2f, 0x73, 0x65, 0x6e, 0x73, 0x69, 0x74, 0x69, 0x76, 0x65, 0x2f, 0x69, 0x6e, 0x6e, 0x65,
	0x72, 0x70, 0x62, 0x2f, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x2f, 0x67,
	0x72, 0x70, 0x63, 0x62, 0x08, 0x65, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x70, 0xe8, 0x07,
})

var (
	file_inner_processing_grpc_notification_proto_rawDescOnce sync.Once
	file_inner_processing_grpc_notification_proto_rawDescData []byte
)

func file_inner_processing_grpc_notification_proto_rawDescGZIP() []byte {
	file_inner_processing_grpc_notification_proto_rawDescOnce.Do(func() {
		file_inner_processing_grpc_notification_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_inner_processing_grpc_notification_proto_rawDesc), len(file_inner_processing_grpc_notification_proto_rawDesc)))
	})
	return file_inner_processing_grpc_notification_proto_rawDescData
}

var file_inner_processing_grpc_notification_proto_msgTypes = make([]protoimpl.MessageInfo, 6)
var file_inner_processing_grpc_notification_proto_goTypes = []any{
	(*GetMailReqDataV1)(nil),      // 0: processing.notification.notification.GetMailReqDataV1
	(*GetMailResDataV1)(nil),      // 1: processing.notification.notification.GetMailResDataV1
	(*GetSMSReqDataV1)(nil),       // 2: processing.notification.notification.GetSMSReqDataV1
	(*GetSMSResDataV1)(nil),       // 3: processing.notification.notification.GetSMSResDataV1
	nil,                           // 4: processing.notification.notification.GetMailResDataV1.PayloadEntry
	nil,                           // 5: processing.notification.notification.GetSMSResDataV1.PayloadEntry
	(*timestamppb.Timestamp)(nil), // 6: google.protobuf.Timestamp
}
var file_inner_processing_grpc_notification_proto_depIdxs = []int32{
	4, // 0: processing.notification.notification.GetMailResDataV1.payload:type_name -> processing.notification.notification.GetMailResDataV1.PayloadEntry
	6, // 1: processing.notification.notification.GetMailResDataV1.created_at:type_name -> google.protobuf.Timestamp
	5, // 2: processing.notification.notification.GetSMSResDataV1.payload:type_name -> processing.notification.notification.GetSMSResDataV1.PayloadEntry
	6, // 3: processing.notification.notification.GetSMSResDataV1.created_at:type_name -> google.protobuf.Timestamp
	0, // 4: processing.notification.notification.Notification.GetLastMailByUserEmail:input_type -> processing.notification.notification.GetMailReqDataV1
	2, // 5: processing.notification.notification.Notification.GetLastSMSByUserPhone:input_type -> processing.notification.notification.GetSMSReqDataV1
	1, // 6: processing.notification.notification.Notification.GetLastMailByUserEmail:output_type -> processing.notification.notification.GetMailResDataV1
	3, // 7: processing.notification.notification.Notification.GetLastSMSByUserPhone:output_type -> processing.notification.notification.GetSMSResDataV1
	6, // [6:8] is the sub-list for method output_type
	4, // [4:6] is the sub-list for method input_type
	4, // [4:4] is the sub-list for extension type_name
	4, // [4:4] is the sub-list for extension extendee
	0, // [0:4] is the sub-list for field type_name
}

func init() { file_inner_processing_grpc_notification_proto_init() }
func file_inner_processing_grpc_notification_proto_init() {
	if File_inner_processing_grpc_notification_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_inner_processing_grpc_notification_proto_rawDesc), len(file_inner_processing_grpc_notification_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   6,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_inner_processing_grpc_notification_proto_goTypes,
		DependencyIndexes: file_inner_processing_grpc_notification_proto_depIdxs,
		MessageInfos:      file_inner_processing_grpc_notification_proto_msgTypes,
	}.Build()
	File_inner_processing_grpc_notification_proto = out.File
	file_inner_processing_grpc_notification_proto_goTypes = nil
	file_inner_processing_grpc_notification_proto_depIdxs = nil
}
