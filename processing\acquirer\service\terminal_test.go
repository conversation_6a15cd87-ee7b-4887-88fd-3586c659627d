package service

import (
	"context"
	"crypto/aes"
	"errors"
	"testing"

	"github.com/golang/mock/gomock"
	"github.com/stretchr/testify/require"

	"git.local/sensitive/innerpb/processing/goerr"
	"git.local/sensitive/pkg/middlewares"
	"git.local/sensitive/processing/acquirer/model"
	"git.local/sensitive/processing/acquirer/repository/database/databasemocks"
	"git.local/sensitive/processing/acquirer/schema"
	"git.local/sensitive/sdk/dog/e2e"
)

var (
	badKey                 = "123"
	normalKey              = "2lgD_WlWfa3Z-BD9"
	defaultEncryptedConfig = "AAAAAAAAAAAAAAAAAAAAAKCya3wWGoWNw5yAfsU98p9AZt8SNhRHOAWe1JxTOPAD" //map[string]any{"my-name-is": "skrillex"}
)

func TestCreateTerminal(t *testing.T) {
	type createOp struct {
		isCalled  bool
		input     *model.Terminal
		output    *model.Terminal
		outputErr error
	}

	tests := []struct {
		name      string
		req       *schema.CreateTerminalRequest
		want      *schema.TerminalResponse
		wantErr   error
		create    createOp
		appConfig map[string]any
	}{
		{
			name: "encrypt error with invalid key",
			req: &schema.CreateTerminalRequest{
				AcquirerID: 1,
				Config: map[string]interface{}{
					"my-name-is": "skrillex",
				},
				TwoStageTimeout:      10,
				AccountNumber:        "********************",
				IsTransit:            false,
				TransitBankID:        19,
				AcquirerTerminalName: "test terminal",
			},
			appConfig: map[string]any{
				"SYMMETRIC_KEY": badKey,
			},
			want:    nil,
			wantErr: aes.KeySizeError(3),
		},
		{
			name: "error when creating terminal",
			req: &schema.CreateTerminalRequest{
				AcquirerID: 1,
				Config: map[string]interface{}{
					"my-name-is": "skrillex",
				},
				TwoStageTimeout:      10,
				AccountNumber:        "********************",
				IsTransit:            false,
				TransitBankID:        19,
				AcquirerTerminalName: "test terminal",
				Description:          "some desc",
			},
			appConfig: map[string]any{
				"SYMMETRIC_KEY": normalKey,
			},
			create: createOp{
				isCalled: true,
				input: &model.Terminal{
					EncryptedConfig:      defaultEncryptedConfig,
					AcquirerID:           1,
					TwoStageTimeout:      10,
					AccountNumber:        "********************",
					IsTransit:            false,
					AcquirerTerminalName: "test terminal",
					TransitBankID:        19,
					Description:          "some desc",
				},
				output:    nil,
				outputErr: errors.New("some error"),
			},
			want:    nil,
			wantErr: errors.New("some error"),
		},
		{
			name: "success",
			req: &schema.CreateTerminalRequest{
				AcquirerID: 1,
				Config: map[string]interface{}{
					"my-name-is": "skrillex",
				},
				TwoStageTimeout:      10,
				AccountNumber:        "********************",
				IsTransit:            false,
				TransitBankID:        19,
				AcquirerTerminalName: "test terminal",
				Description:          "some desc",
			},
			appConfig: map[string]any{
				"SYMMETRIC_KEY": normalKey,
			},
			create: createOp{
				isCalled: true,
				input: &model.Terminal{
					EncryptedConfig:      defaultEncryptedConfig,
					AcquirerID:           1,
					TwoStageTimeout:      10,
					AccountNumber:        "********************",
					IsTransit:            false,
					AcquirerTerminalName: "test terminal",
					TransitBankID:        19,
					Description:          "some desc",
				},
				output: &model.Terminal{
					ID:                   29,
					EncryptedConfig:      defaultEncryptedConfig,
					AcquirerID:           1,
					TwoStageTimeout:      10,
					AccountNumber:        "********************",
					IsTransit:            false,
					AcquirerTerminalName: "test terminal",
					TransitBankID:        19,
					Description:          "some desc",
				},
				outputErr: nil,
			},
			want: &schema.TerminalResponse{
				ID:         29,
				AcquirerID: 1,
				Config:     defaultEncryptedConfig,
			},
			wantErr: nil,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctrl := gomock.NewController(t)
			defer ctrl.Finish()

			c := context.Background()

			ctx, cancel := context.WithCancel(c)

			e2e.InitAppE2E(t, ctx, cancel, nil, nil, tt.appConfig)

			terminalBasicDBMock := databasemocks.NewMockTerminalBasicer(ctrl)

			if tt.create.isCalled {
				terminalBasicDBMock.EXPECT().Create(
					gomock.Any(),
					tt.create.input,
				).Return(tt.create.output, tt.create.outputErr).Times(1)
			}

			terminalService := NewTerminalService(terminalBasicDBMock, nil)

			resp, err := terminalService.Create(context.Background(), tt.req)
			if tt.wantErr != nil {
				require.Error(t, err)
				require.Equal(t, tt.wantErr, err)
			} else {
				require.NoError(t, err)
			}

			require.Equal(t, tt.want, resp)
		})
	}
}

func TestGetAllTerminals(t *testing.T) {
	type getAllOp struct {
		input     uint64
		output    []model.Terminal
		outputErr error
	}

	tests := []struct {
		name      string
		req       uint64
		want      []model.Terminal
		wantErr   error
		getAll    getAllOp
		appConfig map[string]any
	}{
		{
			name:    "error in get all operation",
			req:     24,
			want:    nil,
			wantErr: errors.New("some error"),
			getAll: getAllOp{
				input:     24,
				output:    nil,
				outputErr: errors.New("some error"),
			},
			appConfig: map[string]any{
				"SYMMETRIC_KEY": normalKey,
			},
		},
		{
			name:    "bad symmetric key",
			req:     24,
			want:    nil,
			wantErr: aes.KeySizeError(3),
			getAll: getAllOp{
				input: 24,
				output: []model.Terminal{
					{
						ID:                   22,
						AcquirerID:           1,
						Status:               2,
						EncryptedConfig:      defaultEncryptedConfig,
						TwoStageTimeout:      10,
						AccountNumber:        "********************",
						IsTransit:            false,
						Description:          "some desc",
						AcquirerTerminalName: "test terminal",
					},
					{
						ID:                   23,
						AcquirerID:           2,
						Status:               1,
						EncryptedConfig:      defaultEncryptedConfig,
						TwoStageTimeout:      100,
						AccountNumber:        "********************",
						IsTransit:            false,
						Description:          "some desc",
						AcquirerTerminalName: "test terminal",
					},
				},
				outputErr: nil,
			},
			appConfig: map[string]any{
				"SYMMETRIC_KEY": badKey,
			},
		},
		{
			name: "success",
			req:  24,
			want: []model.Terminal{
				{
					ID:                   22,
					AcquirerID:           1,
					Status:               2,
					EncryptedConfig:      "{\"my-name-is\":\"skrillex\"}",
					TwoStageTimeout:      10,
					AccountNumber:        "********************",
					IsTransit:            false,
					Description:          "some desc",
					AcquirerTerminalName: "test terminal",
				},
				{
					ID:                   23,
					AcquirerID:           2,
					Status:               1,
					EncryptedConfig:      "{\"my-name-is\":\"skrillex\"}",
					TwoStageTimeout:      100,
					AccountNumber:        "********************",
					IsTransit:            false,
					Description:          "some desc",
					AcquirerTerminalName: "test terminal",
				},
			},
			wantErr: nil,
			getAll: getAllOp{
				input: 24,
				output: []model.Terminal{
					{
						ID:                   22,
						AcquirerID:           1,
						Status:               2,
						EncryptedConfig:      defaultEncryptedConfig,
						TwoStageTimeout:      10,
						AccountNumber:        "********************",
						IsTransit:            false,
						Description:          "some desc",
						AcquirerTerminalName: "test terminal",
					},
					{
						ID:                   23,
						AcquirerID:           2,
						Status:               1,
						EncryptedConfig:      defaultEncryptedConfig,
						TwoStageTimeout:      100,
						AccountNumber:        "********************",
						IsTransit:            false,
						Description:          "some desc",
						AcquirerTerminalName: "test terminal",
					},
				},
				outputErr: nil,
			},
			appConfig: map[string]any{
				"SYMMETRIC_KEY": normalKey,
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctrl := gomock.NewController(t)
			defer ctrl.Finish()

			c := context.Background()

			ctx, cancel := context.WithCancel(c)

			e2e.InitAppE2E(t, ctx, cancel, nil, nil, tt.appConfig)

			terminalBasicDBMock := databasemocks.NewMockTerminalBasicer(ctrl)

			terminalBasicDBMock.EXPECT().GetAll(
				gomock.Any(),
				tt.getAll.input,
			).Return(tt.getAll.output, tt.getAll.outputErr).Times(1)

			terminalService := NewTerminalService(terminalBasicDBMock, nil)

			resp, err := terminalService.GetAll(ctx, tt.req)
			if tt.wantErr != nil {
				require.Error(t, err)
				require.Equal(t, tt.wantErr, err)
			} else {
				require.NoError(t, err)
			}

			require.Equal(t, tt.want, resp)
		})
	}
}

func TestUpdateStatusTerminal(t *testing.T) {
	type getAllInfoByID struct {
		input     uint64
		output    *model.Terminal
		outputErr error
	}

	type updateStatusOp struct {
		isCalled    bool
		inputID     uint64
		inputStatus model.TerminalStatus
		outputErr   error
	}

	tests := []struct {
		name         string
		req          uint64
		wantErr      error
		getAll       getAllInfoByID
		updateStatus updateStatusOp
	}{
		{
			name:    "error in get all operation",
			req:     12,
			wantErr: errors.New("some error"),
			getAll: getAllInfoByID{
				input:     12,
				output:    nil,
				outputErr: errors.New("some error"),
			},
		},
		{
			name:    "error when terminal status is off",
			req:     12,
			wantErr: goerr.ErrStatusCannotBeChanged,
			getAll: getAllInfoByID{
				input: 12,
				output: &model.Terminal{
					ID:                   22,
					AcquirerID:           1,
					Status:               model.TerminalGlobalOff,
					EncryptedConfig:      defaultEncryptedConfig,
					TwoStageTimeout:      10,
					AccountNumber:        "********************",
					IsTransit:            false,
					Description:          "some desc",
					AcquirerTerminalName: "test terminal",
				},
				outputErr: nil,
			},
		},
		{
			name:    "update from on to off with error",
			req:     12,
			wantErr: errors.New("some error"),
			getAll: getAllInfoByID{
				input: 12,
				output: &model.Terminal{
					ID:                   22,
					AcquirerID:           1,
					Status:               model.TerminalOn,
					EncryptedConfig:      defaultEncryptedConfig,
					TwoStageTimeout:      10,
					AccountNumber:        "********************",
					IsTransit:            false,
					Description:          "some desc",
					AcquirerTerminalName: "test terminal",
				},
				outputErr: nil,
			},
			updateStatus: updateStatusOp{
				isCalled:    true,
				inputID:     22,
				inputStatus: model.TerminalOff,
				outputErr:   errors.New("some error"),
			},
		},
		{
			name:    "update from off to on with success",
			req:     12,
			wantErr: nil,
			getAll: getAllInfoByID{
				input: 12,
				output: &model.Terminal{
					ID:                   22,
					AcquirerID:           1,
					Status:               model.TerminalOff,
					EncryptedConfig:      defaultEncryptedConfig,
					TwoStageTimeout:      10,
					AccountNumber:        "********************",
					IsTransit:            false,
					Description:          "some desc",
					AcquirerTerminalName: "test terminal",
				},
				outputErr: nil,
			},
			updateStatus: updateStatusOp{
				isCalled:    true,
				inputID:     22,
				inputStatus: model.TerminalOn,
				outputErr:   nil,
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctrl := gomock.NewController(t)
			defer ctrl.Finish()

			terminalBasicDBMock := databasemocks.NewMockTerminalBasicer(ctrl)

			terminalBasicDBMock.EXPECT().GetAllInfoByID(
				gomock.Any(),
				tt.getAll.input,
			).Return(tt.getAll.output, tt.getAll.outputErr).Times(1)

			if tt.updateStatus.isCalled {
				terminalBasicDBMock.EXPECT().UpdateStatus(
					gomock.Any(),
					tt.updateStatus.inputID,
					tt.updateStatus.inputStatus,
				).Return(tt.updateStatus.outputErr).Times(1)
			}

			terminalService := NewTerminalService(terminalBasicDBMock, nil)

			err := terminalService.UpdateStatus(context.Background(), tt.req)
			if tt.wantErr != nil {
				require.Error(t, err)
				require.Equal(t, tt.wantErr, err)
			} else {
				require.NoError(t, err)
			}
		})
	}
}

func TestGetAllInfoByIDTerminal(t *testing.T) {
	type getAllInfoByID struct {
		input     uint64
		output    *model.Terminal
		outputErr error
	}

	tests := []struct {
		name      string
		req       uint64
		want      *model.Terminal
		wantErr   error
		getAll    getAllInfoByID
		appConfig map[string]any
	}{
		{
			name:    "error in get all operation",
			req:     12,
			wantErr: errors.New("some error"),
			want:    nil,
			getAll: getAllInfoByID{
				input:     12,
				output:    nil,
				outputErr: errors.New("some error"),
			},
			appConfig: map[string]any{
				"SYMMETRIC_KEY": normalKey,
			},
		},
		{
			name:    "error with bad key",
			req:     12,
			wantErr: aes.KeySizeError(3),
			want:    nil,
			getAll: getAllInfoByID{
				input: 12,
				output: &model.Terminal{
					ID:                   12,
					AcquirerID:           1,
					Status:               2,
					EncryptedConfig:      defaultEncryptedConfig,
					TwoStageTimeout:      10,
					AccountNumber:        "********************",
					IsTransit:            false,
					Description:          "some desc",
					AcquirerTerminalName: "test terminal",
				},
				outputErr: nil,
			},
			appConfig: map[string]any{
				"SYMMETRIC_KEY": badKey,
			},
		},
		{
			name:    "success",
			req:     12,
			wantErr: nil,
			want: &model.Terminal{
				ID:                   12,
				AcquirerID:           1,
				Status:               2,
				EncryptedConfig:      "{\"my-name-is\":\"skrillex\"}",
				TwoStageTimeout:      10,
				AccountNumber:        "********************",
				IsTransit:            false,
				Description:          "some desc",
				AcquirerTerminalName: "test terminal",
			},
			getAll: getAllInfoByID{
				input: 12,
				output: &model.Terminal{
					ID:                   12,
					AcquirerID:           1,
					Status:               2,
					EncryptedConfig:      defaultEncryptedConfig,
					TwoStageTimeout:      10,
					AccountNumber:        "********************",
					IsTransit:            false,
					Description:          "some desc",
					AcquirerTerminalName: "test terminal",
				},
				outputErr: nil,
			},
			appConfig: map[string]any{
				"SYMMETRIC_KEY": normalKey,
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctrl := gomock.NewController(t)
			defer ctrl.Finish()

			c := context.Background()

			ctx, cancel := context.WithCancel(c)

			e2e.InitAppE2E(t, ctx, cancel, nil, nil, tt.appConfig)

			terminalBasicDBMock := databasemocks.NewMockTerminalBasicer(ctrl)

			terminalBasicDBMock.EXPECT().GetAllInfoByID(
				gomock.Any(),
				tt.getAll.input,
			).Return(tt.getAll.output, tt.getAll.outputErr).Times(1)

			terminalService := NewTerminalService(terminalBasicDBMock, nil)

			resp, err := terminalService.GetAllInfoByID(context.Background(), tt.req)
			if tt.wantErr != nil {
				require.Error(t, err)
				require.Equal(t, tt.wantErr, err)
			} else {
				require.NoError(t, err)
			}

			require.Equal(t, tt.want, resp)
		})
	}
}

func TestFindActiveTerminalsByProject(t *testing.T) {
	type findActiveTerminalsOp struct {
		inputProjectID   uint64
		inputPaymentType uint64
		output           model.Terminals
		outputErr        error
	}

	tests := []struct {
		name                string
		reqProjectID        uint64
		reqPaymentType      uint64
		want                model.Terminals
		wantErr             error
		appConfig           map[string]any
		findActiveTerminals findActiveTerminalsOp
	}{
		{
			name:           "error in finding active terminals",
			reqProjectID:   22,
			reqPaymentType: 1,
			want:           nil,
			wantErr:        errors.New("some error"),
			appConfig: map[string]any{
				"SYMMETRIC_KEY": normalKey,
			},
			findActiveTerminals: findActiveTerminalsOp{
				inputProjectID:   22,
				inputPaymentType: 1,
				output:           nil,
				outputErr:        errors.New("some error"),
			},
		},
		{
			name:           "bad key",
			reqProjectID:   22,
			reqPaymentType: 1,
			want:           nil,
			wantErr:        aes.KeySizeError(3),
			appConfig: map[string]any{
				"SYMMETRIC_KEY": badKey,
			},
			findActiveTerminals: findActiveTerminalsOp{
				inputProjectID:   22,
				inputPaymentType: 1,
				output: model.Terminals{
					{
						ID:                   22,
						AcquirerID:           1,
						Status:               model.TerminalOn,
						EncryptedConfig:      defaultEncryptedConfig,
						TwoStageTimeout:      10,
						AccountNumber:        "********************",
						IsTransit:            false,
						Description:          "some desc",
						AcquirerTerminalName: "test terminal",
					},
					{
						ID:                   23,
						AcquirerID:           1,
						Status:               model.TerminalOff,
						EncryptedConfig:      defaultEncryptedConfig,
						TwoStageTimeout:      10,
						AccountNumber:        "********************",
						IsTransit:            false,
						Description:          "some desc desc",
						AcquirerTerminalName: "test test terminal",
					},
				},
				outputErr: nil,
			},
		},
		{
			name:           "success",
			reqProjectID:   22,
			reqPaymentType: 1,
			want: model.Terminals{
				{
					ID:                   22,
					AcquirerID:           1,
					Status:               model.TerminalOn,
					EncryptedConfig:      "{\"my-name-is\":\"skrillex\"}",
					TwoStageTimeout:      10,
					AccountNumber:        "********************",
					IsTransit:            false,
					Description:          "some desc",
					AcquirerTerminalName: "test terminal",
				},
				{
					ID:                   23,
					AcquirerID:           1,
					Status:               model.TerminalOff,
					EncryptedConfig:      "{\"my-name-is\":\"skrillex\"}",
					TwoStageTimeout:      10,
					AccountNumber:        "********************",
					IsTransit:            false,
					Description:          "some desc desc",
					AcquirerTerminalName: "test test terminal",
				},
			},
			wantErr: nil,
			appConfig: map[string]any{
				"SYMMETRIC_KEY": normalKey,
			},
			findActiveTerminals: findActiveTerminalsOp{
				inputProjectID:   22,
				inputPaymentType: 1,
				output: model.Terminals{
					{
						ID:                   22,
						AcquirerID:           1,
						Status:               model.TerminalOn,
						EncryptedConfig:      defaultEncryptedConfig,
						TwoStageTimeout:      10,
						AccountNumber:        "********************",
						IsTransit:            false,
						Description:          "some desc",
						AcquirerTerminalName: "test terminal",
					},
					{
						ID:                   23,
						AcquirerID:           1,
						Status:               model.TerminalOff,
						EncryptedConfig:      defaultEncryptedConfig,
						TwoStageTimeout:      10,
						AccountNumber:        "********************",
						IsTransit:            false,
						Description:          "some desc desc",
						AcquirerTerminalName: "test test terminal",
					},
				},
				outputErr: nil,
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctrl := gomock.NewController(t)
			defer ctrl.Finish()

			terminalDBMock := databasemocks.NewMockTerminaler(ctrl)

			terminalDBMock.EXPECT().FindActiveTerminalsByProject(
				gomock.Any(),
				tt.findActiveTerminals.inputProjectID,
				tt.findActiveTerminals.inputPaymentType,
			).Return(tt.findActiveTerminals.output, tt.findActiveTerminals.outputErr).Times(1)

			terminalService := NewTerminalService(nil, terminalDBMock)

			c := context.Background()

			ctx, cancel := context.WithCancel(c)

			e2e.InitAppE2E(t, ctx, cancel, nil, nil, tt.appConfig)

			resp, err := terminalService.FindActiveTerminalsByProject(context.Background(), tt.reqProjectID, tt.reqPaymentType)
			if tt.wantErr != nil {
				require.Error(t, err)
				require.Equal(t, tt.wantErr, err)
			} else {
				require.NoError(t, err)
			}

			require.Equal(t, tt.want, resp)
		})
	}
}

func TestTerminalGetByFilters(t *testing.T) {
	var projectID uint64 = 24

	type getByFiltersOp struct {
		input           schema.FiltersTerminalRequest
		inputPagination *middlewares.PaginationInfo
		output          []model.Terminal
		outputErr       error
	}

	tests := []struct {
		name          string
		req           schema.FiltersTerminalRequest
		reqPagination *middlewares.PaginationInfo
		getByFilters  getByFiltersOp
		want          []*schema.TerminalResponse
		wantErr       error
	}{
		{
			name: "error when getting by filters",
			req: schema.FiltersTerminalRequest{
				ProjectID: &projectID,
			},
			reqPagination: &middlewares.PaginationInfo{
				PerPage:    5,
				Page:       1,
				Pagination: true,
			},
			getByFilters: getByFiltersOp{
				input: schema.FiltersTerminalRequest{
					ProjectID: &projectID,
				},
				inputPagination: &middlewares.PaginationInfo{
					PerPage:    5,
					Page:       1,
					Pagination: true,
				},
				output:    nil,
				outputErr: errors.New("some errors"),
			},
			want:    nil,
			wantErr: errors.New("some errors"),
		},
		{
			name: "success",
			req: schema.FiltersTerminalRequest{
				ProjectID: &projectID,
			},
			reqPagination: &middlewares.PaginationInfo{
				PerPage:    5,
				Page:       1,
				Pagination: true,
			},
			getByFilters: getByFiltersOp{
				input: schema.FiltersTerminalRequest{
					ProjectID: &projectID,
				},
				inputPagination: &middlewares.PaginationInfo{
					PerPage:    5,
					Page:       1,
					Pagination: true,
				},
				output: []model.Terminal{
					{
						ID:                   29,
						EncryptedConfig:      defaultEncryptedConfig,
						AcquirerID:           1,
						TwoStageTimeout:      10,
						Status:               model.TerminalOn,
						AccountNumber:        "********************",
						IsTransit:            false,
						AcquirerTerminalName: "test terminal",
						TransitBankID:        19,
						Description:          "some desc",
					},
					{
						ID:                   30,
						EncryptedConfig:      defaultEncryptedConfig,
						AcquirerID:           1,
						TwoStageTimeout:      10,
						Status:               model.TerminalOff,
						AccountNumber:        "********************",
						IsTransit:            false,
						AcquirerTerminalName: "test test terminal",
						TransitBankID:        19,
						Description:          "some desc desc",
					},
				},
				outputErr: nil,
			},
			want: []*schema.TerminalResponse{
				{
					ID:         29,
					AcquirerID: 1,
					Status:     model.TerminalOn,
					Config:     defaultEncryptedConfig,
				},
				{
					ID:         30,
					AcquirerID: 1,
					Status:     model.TerminalOff,
					Config:     defaultEncryptedConfig,
				},
			},
			wantErr: nil,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctrl := gomock.NewController(t)
			defer ctrl.Finish()

			terminalBasicDBMock := databasemocks.NewMockTerminalBasicer(ctrl)

			terminalService := NewTerminalService(terminalBasicDBMock, nil)

			terminalBasicDBMock.EXPECT().GetByFilters(
				gomock.Any(),
				tt.getByFilters.input,
				tt.getByFilters.inputPagination,
			).Return(tt.getByFilters.output, tt.getByFilters.outputErr).Times(1)

			resp, err := terminalService.GetByFilters(context.Background(), tt.req, tt.reqPagination)
			if tt.wantErr != nil {
				require.Error(t, err)
				require.Equal(t, tt.wantErr, err)
			} else {
				require.NoError(t, err)
			}

			require.Equal(t, tt.want, resp)
		})
	}
}
