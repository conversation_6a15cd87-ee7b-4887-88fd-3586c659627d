package getcard

import (
	"net/http"

	"git.local/sensitive/local/internal/domain"
)

const UrlPath = "/transaction/api/v1/system/client/cards"

func TestExpectedData() []domain.SmokeData[[]domain.GetCardResponse] {
	return []domain.SmokeData[[]domain.GetCardResponse]{
		{
			Name: "test_payin",
			Expected: domain.Expected[[]domain.GetCardResponse]{
				HttpStatusCode: http.StatusOK,
				Data: []domain.GetCardResponse{
					{
						CardToken: "AAAAAAAAAAAAAAAAAAAAAOV3HplX9zAuDTOlewBIEV79E01GeKngAYvBuZRG8fDJYH6AImkVesOCZhRPImg5T/Acc3MDbtTl2SpIr/bwnjWdD3BVgS3DkodpGhMbB1Hfs9abv+O8yrO1flDSmQtIXA==",
						MaskedPan: "4400-43XXXXXX-5314",
						Month:     "04",
						Year:      "25",
					},
				},
			},
		},
	}
}

func TestReqData() []domain.GetClientCardsRequest {
	return []domain.GetClientCardsRequest{
		{
			MerchantID:      1,
			ProjectID:       42,
			ProjectClientID: "kambar61",
		},
	}
}
