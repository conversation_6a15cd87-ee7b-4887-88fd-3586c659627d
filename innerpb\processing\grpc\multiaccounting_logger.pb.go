// Code generated by protoc-gen-go-mvp. DO NOT EDIT.
// versions:
// - protoc-gen-go-mvp v2.0.1
// created by mvp authors

package grpc

import (
	context "context"
	fmt "fmt"
	cntx "git.local/sensitive/mvp/pkg/cntx"
	ctxzap "github.com/grpc-ecosystem/go-grpc-middleware/logging/zap/ctxzap"
	zap "go.uber.org/zap"
	grpc "google.golang.org/grpc"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
)

func file_inner_processing_grpc_multiaccounting_proto_message_EmptyToZap(
	label string,
	in *emptypb.Empty,
) zap.Field {
	if in == nil {
		return zap.Reflect(label, in)
	}

	return zap.Dict(
		label,
	)
}

func file_inner_processing_grpc_multiaccounting_proto_message_AcceptTransferRequestToZap(
	label string,
	in *AcceptTransferRequest,
) zap.Field {
	if in == nil {
		return zap.Reflect(label, in)
	}

	return zap.Dict(
		label,
		zap.Any("TransferId", in.GetTransferId()),
		file_inner_processing_grpc_multiaccounting_proto_message_AccountTerminalToZap("Account", in.GetAccount()),
		zap.Any("ExternalReferenceId", in.GetExternalReferenceId()),
	)
}

func file_inner_processing_grpc_multiaccounting_proto_message_AcceptTransferResponseToZap(
	label string,
	in *AcceptTransferResponse,
) zap.Field {
	if in == nil {
		return zap.Reflect(label, in)
	}

	return zap.Dict(
		label,
		zap.Any("TransferId", in.GetTransferId()),
		zap.Any("TransferStatusId", in.GetTransferStatusId()),
		zap.Any("Status", in.GetStatus()),
		zap.Any("Id", in.GetId()),
		zap.Any("ServiceId", in.GetServiceId()),
		zap.Any("Amount", in.GetAmount()),
		zap.Any("Currency", in.GetCurrency()),
		zap.Any("ExternalReferenceId", in.GetExternalReferenceId()),
		zap.Any("TransferStatus", in.GetTransferStatus()),
		zap.Any("TransferStatusName", in.GetTransferStatusName()),
		zap.Any("CreationDate", in.GetCreationDate()),
		zap.Any("FinishingDate", in.GetFinishingDate()),
		file_inner_processing_grpc_multiaccounting_proto_message_ServiceParameterSliceToZap("ServiceParameters", in.GetServiceParameters()),
		zap.Any("AuthCode", in.GetAuthCode()),
		zap.Any("Card", in.GetCard()),
		zap.Any("Terminal", in.GetTerminal()),
	)
}

func file_inner_processing_grpc_multiaccounting_proto_message_AccountTerminalToZap(
	label string,
	in *AccountTerminal,
) zap.Field {
	if in == nil {
		return zap.Reflect(label, in)
	}

	return zap.Dict(
		label,
		zap.Any("Id", in.GetId()),
		zap.Any("BankCode", in.GetBankCode()),
		zap.Any("Config", "[***hidden***]"),
	)
}

func file_inner_processing_grpc_multiaccounting_proto_message_DeclineTransferRequestToZap(
	label string,
	in *DeclineTransferRequest,
) zap.Field {
	if in == nil {
		return zap.Reflect(label, in)
	}

	return zap.Dict(
		label,
		zap.Any("TransferId", in.GetTransferId()),
		file_inner_processing_grpc_multiaccounting_proto_message_AccountTerminalToZap("Account", in.GetAccount()),
		zap.Any("ExternalReferenceId", in.GetExternalReferenceId()),
	)
}

func file_inner_processing_grpc_multiaccounting_proto_message_DeclineTransferResponseToZap(
	label string,
	in *DeclineTransferResponse,
) zap.Field {
	if in == nil {
		return zap.Reflect(label, in)
	}

	return zap.Dict(
		label,
		zap.Any("TransferId", in.GetTransferId()),
		zap.Any("TransferStatusId", in.GetTransferStatusId()),
		zap.Any("Status", in.GetStatus()),
		zap.Any("Id", in.GetId()),
		zap.Any("ServiceId", in.GetServiceId()),
		zap.Any("Amount", in.GetAmount()),
		zap.Any("Currency", in.GetCurrency()),
		zap.Any("ExternalReferenceId", in.GetExternalReferenceId()),
		zap.Any("TransferStatus", in.GetTransferStatus()),
		zap.Any("TransferStatusName", in.GetTransferStatusName()),
		zap.Any("CreationDate", in.GetCreationDate()),
		zap.Any("FinishingDate", in.GetFinishingDate()),
		file_inner_processing_grpc_multiaccounting_proto_message_ServiceParameterSliceToZap("ServiceParameters", in.GetServiceParameters()),
		zap.Any("AuthCode", in.GetAuthCode()),
		zap.Any("Card", in.GetCard()),
		zap.Any("Terminal", in.GetTerminal()),
	)
}

func file_inner_processing_grpc_multiaccounting_proto_message_GetAccountBalanceRequestToZap(
	label string,
	in *GetAccountBalanceRequest,
) zap.Field {
	if in == nil {
		return zap.Reflect(label, in)
	}

	return zap.Dict(
		label,
		file_inner_processing_grpc_multiaccounting_proto_message_AccountTerminalToZap("Account", in.GetAccount()),
	)
}

func file_inner_processing_grpc_multiaccounting_proto_message_GetAccountBalanceResponseToZap(
	label string,
	in *GetAccountBalanceResponse,
) zap.Field {
	if in == nil {
		return zap.Reflect(label, in)
	}

	return zap.Dict(
		label,
		zap.Any("Status", in.GetStatus()),
		zap.Any("Balance", in.GetBalance()),
		zap.Any("Processed", in.GetProcessed()),
		zap.Any("Threshold", in.GetThreshold()),
	)
}

func file_inner_processing_grpc_multiaccounting_proto_message_GetAccountIdentifierResponseToZap(
	label string,
	in *GetAccountIdentifierResponse,
) zap.Field {
	if in == nil {
		return zap.Reflect(label, in)
	}

	return zap.Dict(
		label,
		zap.Any("AccountIdentifier", in.GetAccountIdentifier()),
	)
}

func file_inner_processing_grpc_multiaccounting_proto_message_GetAccountStatementRequestToZap(
	label string,
	in *GetAccountStatementRequest,
) zap.Field {
	if in == nil {
		return zap.Reflect(label, in)
	}

	return zap.Dict(
		label,
		file_inner_processing_grpc_multiaccounting_proto_message_AccountTerminalToZap("Account", in.GetAccount()),
		zap.Any("Date", in.GetDate()),
		zap.Any("Page", in.GetPage()),
	)
}

func file_inner_processing_grpc_multiaccounting_proto_message_GetAccountStatementResponseToZap(
	label string,
	in *GetAccountStatementResponse,
) zap.Field {
	if in == nil {
		return zap.Reflect(label, in)
	}

	return zap.Dict(
		label,
		zap.Any("Status", in.GetStatus()),
		zap.Any("InBalance", in.GetInBalance()),
		zap.Any("OutBalance", in.GetOutBalance()),
		zap.Any("Page", in.GetPage()),
		zap.Any("PagesCount", in.GetPagesCount()),
		file_inner_processing_grpc_multiaccounting_proto_message_TransitAccountOperationSliceToZap("Operations", in.GetOperations()),
	)
}

func file_inner_processing_grpc_multiaccounting_proto_message_GetTransferDetailsRequestToZap(
	label string,
	in *GetTransferDetailsRequest,
) zap.Field {
	if in == nil {
		return zap.Reflect(label, in)
	}

	return zap.Dict(
		label,
		zap.Any("TransferId", in.GetTransferId()),
		file_inner_processing_grpc_multiaccounting_proto_message_AccountTerminalToZap("Account", in.GetAccount()),
		zap.Any("ExternalReferenceId", in.GetExternalReferenceId()),
	)
}

func file_inner_processing_grpc_multiaccounting_proto_message_GetTransferDetailsResponseToZap(
	label string,
	in *GetTransferDetailsResponse,
) zap.Field {
	if in == nil {
		return zap.Reflect(label, in)
	}

	return zap.Dict(
		label,
		zap.Any("TransferId", in.GetTransferId()),
		zap.Any("TransferStatusId", in.GetTransferStatusId()),
		zap.Any("Status", in.GetStatus()),
		zap.Any("Id", in.GetId()),
		zap.Any("ServiceId", in.GetServiceId()),
		zap.Any("Amount", in.GetAmount()),
		zap.Any("Currency", in.GetCurrency()),
		zap.Any("ExternalReferenceId", in.GetExternalReferenceId()),
		zap.Any("TransferStatus", in.GetTransferStatus()),
		zap.Any("TransferStatusName", in.GetTransferStatusName()),
		zap.Any("CreationDate", in.GetCreationDate()),
		zap.Any("FinishingDate", in.GetFinishingDate()),
		file_inner_processing_grpc_multiaccounting_proto_message_ServiceParameterSliceToZap("ServiceParameters", in.GetServiceParameters()),
		zap.Any("AuthCode", in.GetAuthCode()),
		zap.Any("Card", in.GetCard()),
		zap.Any("Terminal", in.GetTerminal()),
	)
}

func file_inner_processing_grpc_multiaccounting_proto_message_GetTransfersListRequestToZap(
	label string,
	in *GetTransfersListRequest,
) zap.Field {
	if in == nil {
		return zap.Reflect(label, in)
	}

	return zap.Dict(
		label,
		file_inner_processing_grpc_multiaccounting_proto_message_AccountTerminalToZap("Account", in.GetAccount()),
		zap.Any("DateFrom", in.GetDateFrom()),
		zap.Any("DateTo", in.GetDateTo()),
	)
}

func file_inner_processing_grpc_multiaccounting_proto_message_GetTransfersListResponseToZap(
	label string,
	in *GetTransfersListResponse,
) zap.Field {
	if in == nil {
		return zap.Reflect(label, in)
	}

	return zap.Dict(
		label,
		zap.Any("Status", in.GetStatus()),
		file_inner_processing_grpc_multiaccounting_proto_message_TransitAccountTransferSliceToZap("Data", in.GetData()),
	)
}

func file_inner_processing_grpc_multiaccounting_proto_message_MakeMerchantCheckRequestToZap(
	label string,
	in *MakeMerchantCheckRequest,
) zap.Field {
	if in == nil {
		return zap.Reflect(label, in)
	}

	return zap.Dict(
		label,
		file_inner_processing_grpc_multiaccounting_proto_message_AccountTerminalToZap("Account", in.GetAccount()),
		zap.Any("MerchantBin", in.GetMerchantBin()),
		zap.Any("MerchantBankAccount", in.GetMerchantBankAccount()),
	)
}

func file_inner_processing_grpc_multiaccounting_proto_message_MakeMerchantCheckResponseToZap(
	label string,
	in *MakeMerchantCheckResponse,
) zap.Field {
	if in == nil {
		return zap.Reflect(label, in)
	}

	return zap.Dict(
		label,
		zap.Any("Status", in.GetStatus()),
		file_inner_processing_grpc_multiaccounting_proto_message_ServiceParametersListSliceToZap("ResponseData", in.GetResponseData()),
	)
}

func file_inner_processing_grpc_multiaccounting_proto_message_MakeTransferRequestToZap(
	label string,
	in *MakeTransferRequest,
) zap.Field {
	if in == nil {
		return zap.Reflect(label, in)
	}

	return zap.Dict(
		label,
		zap.Any("TransferId", in.GetTransferId()),
		file_inner_processing_grpc_multiaccounting_proto_message_AccountTerminalToZap("Account", in.GetAccount()),
		zap.Any("ExternalReferenceId", in.GetExternalReferenceId()),
		zap.Any("Amount", in.GetAmount()),
		zap.Any("Currency", in.GetCurrency()),
		zap.Any("IsNeedAcceptation", in.GetIsNeedAcceptation()),
		file_inner_processing_grpc_multiaccounting_proto_message_ServiceParameterSliceToZap("ServiceParameters", in.GetServiceParameters()),
	)
}

func file_inner_processing_grpc_multiaccounting_proto_message_MakeTransferResponseToZap(
	label string,
	in *MakeTransferResponse,
) zap.Field {
	if in == nil {
		return zap.Reflect(label, in)
	}

	return zap.Dict(
		label,
		zap.Any("TransferId", in.GetTransferId()),
		zap.Any("TransferStatusId", in.GetTransferStatusId()),
		zap.Any("Status", in.GetStatus()),
		zap.Any("Id", in.GetId()),
		zap.Any("ServiceId", in.GetServiceId()),
		zap.Any("Amount", in.GetAmount()),
		zap.Any("Currency", in.GetCurrency()),
		zap.Any("ExternalReferenceId", in.GetExternalReferenceId()),
		zap.Any("TransferStatus", in.GetTransferStatus()),
		zap.Any("TransferStatusName", in.GetTransferStatusName()),
		zap.Any("CreationDate", in.GetCreationDate()),
		zap.Any("FinishingDate", in.GetFinishingDate()),
		file_inner_processing_grpc_multiaccounting_proto_message_ServiceParameterSliceToZap("ServiceParameters", in.GetServiceParameters()),
		zap.Any("Rrn", in.GetRrn()),
		zap.Any("AuthCode", in.GetAuthCode()),
		zap.Any("Card", in.GetCard()),
		zap.Any("Terminal", in.GetTerminal()),
	)
}

func file_inner_processing_grpc_multiaccounting_proto_message_RedoTransferRequestToZap(
	label string,
	in *RedoTransferRequest,
) zap.Field {
	if in == nil {
		return zap.Reflect(label, in)
	}

	return zap.Dict(
		label,
		zap.Any("TransferId", in.GetTransferId()),
		file_inner_processing_grpc_multiaccounting_proto_message_AccountTerminalToZap("Account", in.GetAccount()),
		zap.Any("ExternalReferenceId", in.GetExternalReferenceId()),
	)
}

func file_inner_processing_grpc_multiaccounting_proto_message_RedoTransferResponseToZap(
	label string,
	in *RedoTransferResponse,
) zap.Field {
	if in == nil {
		return zap.Reflect(label, in)
	}

	return zap.Dict(
		label,
		zap.Any("TransferId", in.GetTransferId()),
		zap.Any("TransferStatusId", in.GetTransferStatusId()),
		zap.Any("Status", in.GetStatus()),
		zap.Any("Id", in.GetId()),
		zap.Any("ServiceId", in.GetServiceId()),
		zap.Any("Amount", in.GetAmount()),
		zap.Any("Currency", in.GetCurrency()),
		zap.Any("ExternalReferenceId", in.GetExternalReferenceId()),
		zap.Any("TransferStatus", in.GetTransferStatus()),
		zap.Any("TransferStatusName", in.GetTransferStatusName()),
		zap.Any("CreationDate", in.GetCreationDate()),
		zap.Any("FinishingDate", in.GetFinishingDate()),
		file_inner_processing_grpc_multiaccounting_proto_message_ServiceParameterSliceToZap("ServiceParameters", in.GetServiceParameters()),
		zap.Any("AuthCode", in.GetAuthCode()),
		zap.Any("Card", in.GetCard()),
		zap.Any("Terminal", in.GetTerminal()),
	)
}

func file_inner_processing_grpc_multiaccounting_proto_message_ServiceParameterToZap(
	label string,
	in *ServiceParameter,
) zap.Field {
	if in == nil {
		return zap.Reflect(label, in)
	}

	return zap.Dict(
		label,
		zap.Any("Code", in.GetCode()),
		zap.Any("Value", in.GetValue()),
		zap.Any("ParameterType", in.GetParameterType()),
	)
}

func file_inner_processing_grpc_multiaccounting_proto_message_ServiceParameterSliceToZap(
	label string,
	in []*ServiceParameter,
) zap.Field {
	var res []zap.Field
	for index, val := range in {
		res = append(res, file_inner_processing_grpc_multiaccounting_proto_message_ServiceParameterToZap(fmt.Sprint(index), val))
	}

	return zap.Dict(label, res...)
}

func file_inner_processing_grpc_multiaccounting_proto_message_ServiceParametersListToZap(
	label string,
	in *ServiceParametersList,
) zap.Field {
	if in == nil {
		return zap.Reflect(label, in)
	}

	return zap.Dict(
		label,
		file_inner_processing_grpc_multiaccounting_proto_message_ServiceParameterSliceToZap("ServiceParamsList", in.GetServiceParamsList()),
	)
}

func file_inner_processing_grpc_multiaccounting_proto_message_ServiceParametersListSliceToZap(
	label string,
	in []*ServiceParametersList,
) zap.Field {
	var res []zap.Field
	for index, val := range in {
		res = append(res, file_inner_processing_grpc_multiaccounting_proto_message_ServiceParametersListToZap(fmt.Sprint(index), val))
	}

	return zap.Dict(label, res...)
}

func file_inner_processing_grpc_multiaccounting_proto_message_TransitAccountOperationToZap(
	label string,
	in *TransitAccountOperation,
) zap.Field {
	if in == nil {
		return zap.Reflect(label, in)
	}

	return zap.Dict(
		label,
		zap.Any("ExternalReferenceId", in.GetExternalReferenceId()),
		zap.Any("Date", in.GetDate()),
		zap.Any("OperationType", in.GetOperationType()),
		zap.Any("MerchantAccount", in.GetMerchantAccount()),
		zap.Any("MerchantBank", in.GetMerchantBank()),
		zap.Any("MerchantBin", in.GetMerchantBin()),
		zap.Any("MerchantName", in.GetMerchantName()),
		zap.Any("Amount", in.GetAmount()),
		zap.Any("Currency", in.GetCurrency()),
		zap.Any("PaymentPurposeCode", in.GetPaymentPurposeCode()),
		zap.Any("Description", in.GetDescription()),
		zap.Any("TransferTypeId", in.GetTransferTypeId()),
	)
}

func file_inner_processing_grpc_multiaccounting_proto_message_TransitAccountOperationSliceToZap(
	label string,
	in []*TransitAccountOperation,
) zap.Field {
	var res []zap.Field
	for index, val := range in {
		res = append(res, file_inner_processing_grpc_multiaccounting_proto_message_TransitAccountOperationToZap(fmt.Sprint(index), val))
	}

	return zap.Dict(label, res...)
}

func file_inner_processing_grpc_multiaccounting_proto_message_TransitAccountTransferToZap(
	label string,
	in *TransitAccountTransfer,
) zap.Field {
	if in == nil {
		return zap.Reflect(label, in)
	}

	return zap.Dict(
		label,
		zap.Any("Id", in.GetId()),
		zap.Any("ServiceId", in.GetServiceId()),
		zap.Any("Amount", in.GetAmount()),
		zap.Any("Currency", in.GetCurrency()),
		zap.Any("ExternalReferenceId", in.GetExternalReferenceId()),
		zap.Any("TransferStatus", in.GetTransferStatus()),
		zap.Any("TransferStatusName", in.GetTransferStatusName()),
		zap.Any("CreationDate", in.GetCreationDate()),
		zap.Any("FinishingDate", in.GetFinishingDate()),
		file_inner_processing_grpc_multiaccounting_proto_message_ServiceParameterSliceToZap("ServiceParameters", in.GetServiceParameters()),
		zap.Any("AuthCode", in.GetAuthCode()),
		zap.Any("Card", in.GetCard()),
		zap.Any("Terminal", in.GetTerminal()),
	)
}

func file_inner_processing_grpc_multiaccounting_proto_message_TransitAccountTransferSliceToZap(
	label string,
	in []*TransitAccountTransfer,
) zap.Field {
	var res []zap.Field
	for index, val := range in {
		res = append(res, file_inner_processing_grpc_multiaccounting_proto_message_TransitAccountTransferToZap(fmt.Sprint(index), val))
	}

	return zap.Dict(label, res...)
}

var _ MultiaccountingServer = (*loggedMultiaccountingServer)(nil)

func NewLoggedMultiaccountingServer(srv MultiaccountingServer) MultiaccountingServer {
	return &loggedMultiaccountingServer{srv: srv}
}

type loggedMultiaccountingServer struct {
	UnimplementedMultiaccountingServer

	srv MultiaccountingServer
}

func (s *loggedMultiaccountingServer) MakeMerchantCheck(
	ctx context.Context,
	request *MakeMerchantCheckRequest,
) (
	response *MakeMerchantCheckResponse,
	err error,
) {
	label := cntx.Begin(ctx, "MultiaccountingServer_MakeMerchantCheck")
	defer cntx.End(ctx, label)

	defer func() {
		if err != nil {
			ctxzap.AddFields(ctx, zap.Any(label+"error", err))
			return
		}

		ctxzap.AddFields(
			ctx,
			file_inner_processing_grpc_multiaccounting_proto_message_MakeMerchantCheckResponseToZap(label+"response", response),
		)
	}()

	ctxzap.AddFields(
		ctx,
		file_inner_processing_grpc_multiaccounting_proto_message_MakeMerchantCheckRequestToZap(label+"request", request),
	)

	response, err = s.srv.MakeMerchantCheck(ctx, request)

	return
}

func (s *loggedMultiaccountingServer) MakeTransfer(
	ctx context.Context,
	request *MakeTransferRequest,
) (
	response *MakeTransferResponse,
	err error,
) {
	label := cntx.Begin(ctx, "MultiaccountingServer_MakeTransfer")
	defer cntx.End(ctx, label)

	defer func() {
		if err != nil {
			ctxzap.AddFields(ctx, zap.Any(label+"error", err))
			return
		}

		ctxzap.AddFields(
			ctx,
			file_inner_processing_grpc_multiaccounting_proto_message_MakeTransferResponseToZap(label+"response", response),
		)
	}()

	ctxzap.AddFields(
		ctx,
		file_inner_processing_grpc_multiaccounting_proto_message_MakeTransferRequestToZap(label+"request", request),
	)

	response, err = s.srv.MakeTransfer(ctx, request)

	return
}

func (s *loggedMultiaccountingServer) AcceptTransfer(
	ctx context.Context,
	request *AcceptTransferRequest,
) (
	response *AcceptTransferResponse,
	err error,
) {
	label := cntx.Begin(ctx, "MultiaccountingServer_AcceptTransfer")
	defer cntx.End(ctx, label)

	defer func() {
		if err != nil {
			ctxzap.AddFields(ctx, zap.Any(label+"error", err))
			return
		}

		ctxzap.AddFields(
			ctx,
			file_inner_processing_grpc_multiaccounting_proto_message_AcceptTransferResponseToZap(label+"response", response),
		)
	}()

	ctxzap.AddFields(
		ctx,
		file_inner_processing_grpc_multiaccounting_proto_message_AcceptTransferRequestToZap(label+"request", request),
	)

	response, err = s.srv.AcceptTransfer(ctx, request)

	return
}

func (s *loggedMultiaccountingServer) DeclineTransfer(
	ctx context.Context,
	request *DeclineTransferRequest,
) (
	response *DeclineTransferResponse,
	err error,
) {
	label := cntx.Begin(ctx, "MultiaccountingServer_DeclineTransfer")
	defer cntx.End(ctx, label)

	defer func() {
		if err != nil {
			ctxzap.AddFields(ctx, zap.Any(label+"error", err))
			return
		}

		ctxzap.AddFields(
			ctx,
			file_inner_processing_grpc_multiaccounting_proto_message_DeclineTransferResponseToZap(label+"response", response),
		)
	}()

	ctxzap.AddFields(
		ctx,
		file_inner_processing_grpc_multiaccounting_proto_message_DeclineTransferRequestToZap(label+"request", request),
	)

	response, err = s.srv.DeclineTransfer(ctx, request)

	return
}

func (s *loggedMultiaccountingServer) RedoTransfer(
	ctx context.Context,
	request *RedoTransferRequest,
) (
	response *RedoTransferResponse,
	err error,
) {
	label := cntx.Begin(ctx, "MultiaccountingServer_RedoTransfer")
	defer cntx.End(ctx, label)

	defer func() {
		if err != nil {
			ctxzap.AddFields(ctx, zap.Any(label+"error", err))
			return
		}

		ctxzap.AddFields(
			ctx,
			file_inner_processing_grpc_multiaccounting_proto_message_RedoTransferResponseToZap(label+"response", response),
		)
	}()

	ctxzap.AddFields(
		ctx,
		file_inner_processing_grpc_multiaccounting_proto_message_RedoTransferRequestToZap(label+"request", request),
	)

	response, err = s.srv.RedoTransfer(ctx, request)

	return
}

func (s *loggedMultiaccountingServer) GetTransferDetails(
	ctx context.Context,
	request *GetTransferDetailsRequest,
) (
	response *GetTransferDetailsResponse,
	err error,
) {
	label := cntx.Begin(ctx, "MultiaccountingServer_GetTransferDetails")
	defer cntx.End(ctx, label)

	defer func() {
		if err != nil {
			ctxzap.AddFields(ctx, zap.Any(label+"error", err))
			return
		}

		ctxzap.AddFields(
			ctx,
			file_inner_processing_grpc_multiaccounting_proto_message_GetTransferDetailsResponseToZap(label+"response", response),
		)
	}()

	ctxzap.AddFields(
		ctx,
		file_inner_processing_grpc_multiaccounting_proto_message_GetTransferDetailsRequestToZap(label+"request", request),
	)

	response, err = s.srv.GetTransferDetails(ctx, request)

	return
}

func (s *loggedMultiaccountingServer) GetTransfersList(
	ctx context.Context,
	request *GetTransfersListRequest,
) (
	response *GetTransfersListResponse,
	err error,
) {
	label := cntx.Begin(ctx, "MultiaccountingServer_GetTransfersList")
	defer cntx.End(ctx, label)

	defer func() {
		if err != nil {
			ctxzap.AddFields(ctx, zap.Any(label+"error", err))
			return
		}

		ctxzap.AddFields(
			ctx,
			file_inner_processing_grpc_multiaccounting_proto_message_GetTransfersListResponseToZap(label+"response", response),
		)
	}()

	ctxzap.AddFields(
		ctx,
		file_inner_processing_grpc_multiaccounting_proto_message_GetTransfersListRequestToZap(label+"request", request),
	)

	response, err = s.srv.GetTransfersList(ctx, request)

	return
}

func (s *loggedMultiaccountingServer) GetAccountBalance(
	ctx context.Context,
	request *GetAccountBalanceRequest,
) (
	response *GetAccountBalanceResponse,
	err error,
) {
	label := cntx.Begin(ctx, "MultiaccountingServer_GetAccountBalance")
	defer cntx.End(ctx, label)

	defer func() {
		if err != nil {
			ctxzap.AddFields(ctx, zap.Any(label+"error", err))
			return
		}

		ctxzap.AddFields(
			ctx,
			file_inner_processing_grpc_multiaccounting_proto_message_GetAccountBalanceResponseToZap(label+"response", response),
		)
	}()

	ctxzap.AddFields(
		ctx,
		file_inner_processing_grpc_multiaccounting_proto_message_GetAccountBalanceRequestToZap(label+"request", request),
	)

	response, err = s.srv.GetAccountBalance(ctx, request)

	return
}

func (s *loggedMultiaccountingServer) GetAccountStatement(
	ctx context.Context,
	request *GetAccountStatementRequest,
) (
	response *GetAccountStatementResponse,
	err error,
) {
	label := cntx.Begin(ctx, "MultiaccountingServer_GetAccountStatement")
	defer cntx.End(ctx, label)

	defer func() {
		if err != nil {
			ctxzap.AddFields(ctx, zap.Any(label+"error", err))
			return
		}

		ctxzap.AddFields(
			ctx,
			file_inner_processing_grpc_multiaccounting_proto_message_GetAccountStatementResponseToZap(label+"response", response),
		)
	}()

	ctxzap.AddFields(
		ctx,
		file_inner_processing_grpc_multiaccounting_proto_message_GetAccountStatementRequestToZap(label+"request", request),
	)

	response, err = s.srv.GetAccountStatement(ctx, request)

	return
}

func (s *loggedMultiaccountingServer) GetAccountIdentifier(
	ctx context.Context,
	request *emptypb.Empty,
) (
	response *GetAccountIdentifierResponse,
	err error,
) {
	label := cntx.Begin(ctx, "MultiaccountingServer_GetAccountIdentifier")
	defer cntx.End(ctx, label)

	defer func() {
		if err != nil {
			ctxzap.AddFields(ctx, zap.Any(label+"error", err))
			return
		}

		ctxzap.AddFields(
			ctx,
			file_inner_processing_grpc_multiaccounting_proto_message_GetAccountIdentifierResponseToZap(label+"response", response),
		)
	}()

	ctxzap.AddFields(
		ctx,
		file_inner_processing_grpc_multiaccounting_proto_message_EmptyToZap(label+"request", request),
	)

	response, err = s.srv.GetAccountIdentifier(ctx, request)

	return
}

var _ MultiaccountingClient = (*loggedMultiaccountingClient)(nil)

func NewLoggedMultiaccountingClient(client MultiaccountingClient) MultiaccountingClient {
	return &loggedMultiaccountingClient{client: client}
}

type loggedMultiaccountingClient struct {
	client MultiaccountingClient
}

func (s *loggedMultiaccountingClient) MakeMerchantCheck(
	ctx context.Context,
	request *MakeMerchantCheckRequest,
	opts ...grpc.CallOption,
) (
	response *MakeMerchantCheckResponse,
	err error,
) {
	label := cntx.Begin(ctx, "MultiaccountingClient_MakeMerchantCheck")
	defer cntx.End(ctx, label)

	defer func() {
		if err != nil {
			ctxzap.AddFields(ctx, zap.Any(label+"error", err))
			return
		}

		ctxzap.AddFields(
			ctx,
			file_inner_processing_grpc_multiaccounting_proto_message_MakeMerchantCheckResponseToZap(label+"response", response),
		)
	}()

	ctxzap.AddFields(
		ctx,
		file_inner_processing_grpc_multiaccounting_proto_message_MakeMerchantCheckRequestToZap(label+"request", request),
	)

	response, err = s.client.MakeMerchantCheck(ctx, request, opts...)

	return
}

func (s *loggedMultiaccountingClient) MakeTransfer(
	ctx context.Context,
	request *MakeTransferRequest,
	opts ...grpc.CallOption,
) (
	response *MakeTransferResponse,
	err error,
) {
	label := cntx.Begin(ctx, "MultiaccountingClient_MakeTransfer")
	defer cntx.End(ctx, label)

	defer func() {
		if err != nil {
			ctxzap.AddFields(ctx, zap.Any(label+"error", err))
			return
		}

		ctxzap.AddFields(
			ctx,
			file_inner_processing_grpc_multiaccounting_proto_message_MakeTransferResponseToZap(label+"response", response),
		)
	}()

	ctxzap.AddFields(
		ctx,
		file_inner_processing_grpc_multiaccounting_proto_message_MakeTransferRequestToZap(label+"request", request),
	)

	response, err = s.client.MakeTransfer(ctx, request, opts...)

	return
}

func (s *loggedMultiaccountingClient) AcceptTransfer(
	ctx context.Context,
	request *AcceptTransferRequest,
	opts ...grpc.CallOption,
) (
	response *AcceptTransferResponse,
	err error,
) {
	label := cntx.Begin(ctx, "MultiaccountingClient_AcceptTransfer")
	defer cntx.End(ctx, label)

	defer func() {
		if err != nil {
			ctxzap.AddFields(ctx, zap.Any(label+"error", err))
			return
		}

		ctxzap.AddFields(
			ctx,
			file_inner_processing_grpc_multiaccounting_proto_message_AcceptTransferResponseToZap(label+"response", response),
		)
	}()

	ctxzap.AddFields(
		ctx,
		file_inner_processing_grpc_multiaccounting_proto_message_AcceptTransferRequestToZap(label+"request", request),
	)

	response, err = s.client.AcceptTransfer(ctx, request, opts...)

	return
}

func (s *loggedMultiaccountingClient) DeclineTransfer(
	ctx context.Context,
	request *DeclineTransferRequest,
	opts ...grpc.CallOption,
) (
	response *DeclineTransferResponse,
	err error,
) {
	label := cntx.Begin(ctx, "MultiaccountingClient_DeclineTransfer")
	defer cntx.End(ctx, label)

	defer func() {
		if err != nil {
			ctxzap.AddFields(ctx, zap.Any(label+"error", err))
			return
		}

		ctxzap.AddFields(
			ctx,
			file_inner_processing_grpc_multiaccounting_proto_message_DeclineTransferResponseToZap(label+"response", response),
		)
	}()

	ctxzap.AddFields(
		ctx,
		file_inner_processing_grpc_multiaccounting_proto_message_DeclineTransferRequestToZap(label+"request", request),
	)

	response, err = s.client.DeclineTransfer(ctx, request, opts...)

	return
}

func (s *loggedMultiaccountingClient) RedoTransfer(
	ctx context.Context,
	request *RedoTransferRequest,
	opts ...grpc.CallOption,
) (
	response *RedoTransferResponse,
	err error,
) {
	label := cntx.Begin(ctx, "MultiaccountingClient_RedoTransfer")
	defer cntx.End(ctx, label)

	defer func() {
		if err != nil {
			ctxzap.AddFields(ctx, zap.Any(label+"error", err))
			return
		}

		ctxzap.AddFields(
			ctx,
			file_inner_processing_grpc_multiaccounting_proto_message_RedoTransferResponseToZap(label+"response", response),
		)
	}()

	ctxzap.AddFields(
		ctx,
		file_inner_processing_grpc_multiaccounting_proto_message_RedoTransferRequestToZap(label+"request", request),
	)

	response, err = s.client.RedoTransfer(ctx, request, opts...)

	return
}

func (s *loggedMultiaccountingClient) GetTransferDetails(
	ctx context.Context,
	request *GetTransferDetailsRequest,
	opts ...grpc.CallOption,
) (
	response *GetTransferDetailsResponse,
	err error,
) {
	label := cntx.Begin(ctx, "MultiaccountingClient_GetTransferDetails")
	defer cntx.End(ctx, label)

	defer func() {
		if err != nil {
			ctxzap.AddFields(ctx, zap.Any(label+"error", err))
			return
		}

		ctxzap.AddFields(
			ctx,
			file_inner_processing_grpc_multiaccounting_proto_message_GetTransferDetailsResponseToZap(label+"response", response),
		)
	}()

	ctxzap.AddFields(
		ctx,
		file_inner_processing_grpc_multiaccounting_proto_message_GetTransferDetailsRequestToZap(label+"request", request),
	)

	response, err = s.client.GetTransferDetails(ctx, request, opts...)

	return
}

func (s *loggedMultiaccountingClient) GetTransfersList(
	ctx context.Context,
	request *GetTransfersListRequest,
	opts ...grpc.CallOption,
) (
	response *GetTransfersListResponse,
	err error,
) {
	label := cntx.Begin(ctx, "MultiaccountingClient_GetTransfersList")
	defer cntx.End(ctx, label)

	defer func() {
		if err != nil {
			ctxzap.AddFields(ctx, zap.Any(label+"error", err))
			return
		}

		ctxzap.AddFields(
			ctx,
			file_inner_processing_grpc_multiaccounting_proto_message_GetTransfersListResponseToZap(label+"response", response),
		)
	}()

	ctxzap.AddFields(
		ctx,
		file_inner_processing_grpc_multiaccounting_proto_message_GetTransfersListRequestToZap(label+"request", request),
	)

	response, err = s.client.GetTransfersList(ctx, request, opts...)

	return
}

func (s *loggedMultiaccountingClient) GetAccountBalance(
	ctx context.Context,
	request *GetAccountBalanceRequest,
	opts ...grpc.CallOption,
) (
	response *GetAccountBalanceResponse,
	err error,
) {
	label := cntx.Begin(ctx, "MultiaccountingClient_GetAccountBalance")
	defer cntx.End(ctx, label)

	defer func() {
		if err != nil {
			ctxzap.AddFields(ctx, zap.Any(label+"error", err))
			return
		}

		ctxzap.AddFields(
			ctx,
			file_inner_processing_grpc_multiaccounting_proto_message_GetAccountBalanceResponseToZap(label+"response", response),
		)
	}()

	ctxzap.AddFields(
		ctx,
		file_inner_processing_grpc_multiaccounting_proto_message_GetAccountBalanceRequestToZap(label+"request", request),
	)

	response, err = s.client.GetAccountBalance(ctx, request, opts...)

	return
}

func (s *loggedMultiaccountingClient) GetAccountStatement(
	ctx context.Context,
	request *GetAccountStatementRequest,
	opts ...grpc.CallOption,
) (
	response *GetAccountStatementResponse,
	err error,
) {
	label := cntx.Begin(ctx, "MultiaccountingClient_GetAccountStatement")
	defer cntx.End(ctx, label)

	defer func() {
		if err != nil {
			ctxzap.AddFields(ctx, zap.Any(label+"error", err))
			return
		}

		ctxzap.AddFields(
			ctx,
			file_inner_processing_grpc_multiaccounting_proto_message_GetAccountStatementResponseToZap(label+"response", response),
		)
	}()

	ctxzap.AddFields(
		ctx,
		file_inner_processing_grpc_multiaccounting_proto_message_GetAccountStatementRequestToZap(label+"request", request),
	)

	response, err = s.client.GetAccountStatement(ctx, request, opts...)

	return
}

func (s *loggedMultiaccountingClient) GetAccountIdentifier(
	ctx context.Context,
	request *emptypb.Empty,
	opts ...grpc.CallOption,
) (
	response *GetAccountIdentifierResponse,
	err error,
) {
	label := cntx.Begin(ctx, "MultiaccountingClient_GetAccountIdentifier")
	defer cntx.End(ctx, label)

	defer func() {
		if err != nil {
			ctxzap.AddFields(ctx, zap.Any(label+"error", err))
			return
		}

		ctxzap.AddFields(
			ctx,
			file_inner_processing_grpc_multiaccounting_proto_message_GetAccountIdentifierResponseToZap(label+"response", response),
		)
	}()

	ctxzap.AddFields(
		ctx,
		file_inner_processing_grpc_multiaccounting_proto_message_EmptyToZap(label+"request", request),
	)

	response, err = s.client.GetAccountIdentifier(ctx, request, opts...)

	return
}
