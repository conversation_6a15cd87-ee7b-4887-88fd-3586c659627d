// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.5.1
// - protoc             v5.29.3
// source: inner/processing/grpc/transaction_status.proto

package grpc

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.64.0 or later.
const _ = grpc.SupportPackageIsVersion9

const (
	TransactionStatus_GetBatchTransactionWithStatuses_FullMethodName = "/processing.transaction.transaction_status.TransactionStatus/GetBatchTransactionWithStatuses"
)

// TransactionStatusClient is the client API for TransactionStatus service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type TransactionStatusClient interface {
	GetBatchTransactionWithStatuses(ctx context.Context, in *BatchTransactionRequestV1, opts ...grpc.CallOption) (*BatchTransactionResponseV1, error)
}

type transactionStatusClient struct {
	cc grpc.ClientConnInterface
}

func NewTransactionStatusClient(cc grpc.ClientConnInterface) TransactionStatusClient {
	return &transactionStatusClient{cc}
}

func (c *transactionStatusClient) GetBatchTransactionWithStatuses(ctx context.Context, in *BatchTransactionRequestV1, opts ...grpc.CallOption) (*BatchTransactionResponseV1, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(BatchTransactionResponseV1)
	err := c.cc.Invoke(ctx, TransactionStatus_GetBatchTransactionWithStatuses_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// TransactionStatusServer is the server API for TransactionStatus service.
// All implementations must embed UnimplementedTransactionStatusServer
// for forward compatibility.
type TransactionStatusServer interface {
	GetBatchTransactionWithStatuses(context.Context, *BatchTransactionRequestV1) (*BatchTransactionResponseV1, error)
	mustEmbedUnimplementedTransactionStatusServer()
}

// UnimplementedTransactionStatusServer must be embedded to have
// forward compatible implementations.
//
// NOTE: this should be embedded by value instead of pointer to avoid a nil
// pointer dereference when methods are called.
type UnimplementedTransactionStatusServer struct{}

func (UnimplementedTransactionStatusServer) GetBatchTransactionWithStatuses(context.Context, *BatchTransactionRequestV1) (*BatchTransactionResponseV1, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetBatchTransactionWithStatuses not implemented")
}
func (UnimplementedTransactionStatusServer) mustEmbedUnimplementedTransactionStatusServer() {}
func (UnimplementedTransactionStatusServer) testEmbeddedByValue()                           {}

// UnsafeTransactionStatusServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to TransactionStatusServer will
// result in compilation errors.
type UnsafeTransactionStatusServer interface {
	mustEmbedUnimplementedTransactionStatusServer()
}

func RegisterTransactionStatusServer(s grpc.ServiceRegistrar, srv TransactionStatusServer) {
	// If the following call pancis, it indicates UnimplementedTransactionStatusServer was
	// embedded by pointer and is nil.  This will cause panics if an
	// unimplemented method is ever invoked, so we test this at initialization
	// time to prevent it from happening at runtime later due to I/O.
	if t, ok := srv.(interface{ testEmbeddedByValue() }); ok {
		t.testEmbeddedByValue()
	}
	s.RegisterService(&TransactionStatus_ServiceDesc, srv)
}

func _TransactionStatus_GetBatchTransactionWithStatuses_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatchTransactionRequestV1)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TransactionStatusServer).GetBatchTransactionWithStatuses(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: TransactionStatus_GetBatchTransactionWithStatuses_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TransactionStatusServer).GetBatchTransactionWithStatuses(ctx, req.(*BatchTransactionRequestV1))
	}
	return interceptor(ctx, in, info, handler)
}

// TransactionStatus_ServiceDesc is the grpc.ServiceDesc for TransactionStatus service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var TransactionStatus_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "processing.transaction.transaction_status.TransactionStatus",
	HandlerType: (*TransactionStatusServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetBatchTransactionWithStatuses",
			Handler:    _TransactionStatus_GetBatchTransactionWithStatuses_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "inner/processing/grpc/transaction_status.proto",
}
