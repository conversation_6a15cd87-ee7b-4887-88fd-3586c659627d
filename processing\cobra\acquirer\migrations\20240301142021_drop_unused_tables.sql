-- +goose Up
-- +goose StatementBegin

drop table if exists "acquirer"."acquirer_currencies";
drop table if exists "acquirer"."acquirer_percentages";
drop table if exists "acquirer"."bank_limits";
drop table if exists "acquirer"."bank_services";
drop table if exists "acquirer"."country_currencies";
drop table if exists "acquirer"."currencies";
drop table if exists "acquirer"."ips_countries";
drop table if exists "acquirer"."ips_digit_codes";

-- +goose StatementEnd

-- +goose Down
-- +goose StatementBegin

-- CREATE TABLE IF NOT EXISTS "acquirer"."currencies"
-- (
--     "created_at"   TIMESTAMP DEFAULT NOW(),
--     "updated_at"   TIMESTAMP DEFAULT NOW(),
--     "id"           SERIAL PRIMARY KEY,
--     "name"         VARCHAR(255) NOT NULL,
--     "letter_code"  VARCHAR(255) NOT NULL,
--     "digital_code" VARCHAR(255) NOT NULL
-- );
--
-- CREATE TABLE IF NOT EXISTS "acquirer"."acquirer_percentages"
-- (
--     "created_at"        TIMESTAMP DEFAULT NOW(),
--     "updated_at"        TIMESTAMP DEFAULT NOW(),
--     "id"                SERIAL PRIMARY KEY,
--     "ips_id"            BIGINT NOT NULL REFERENCES "acquirer"."ips",
--     "mcc_id"            BIGINT NOT NULL,
--     "acquirer_id"       BIGINT NOT NULL REFERENCES "acquirer"."acquirers",
--     "issuer_id"         BIGINT NOT NULL REFERENCES "acquirer"."banks",
--     "issuer_country_id" BIGINT NOT NULL REFERENCES "acquirer"."countries",
--     "percentage"        FLOAT,
--     "min"               FLOAT,
--     "max"               FLOAT,
--     "fix"               INTEGER
--     );
--
-- CREATE INDEX IF NOT EXISTS idx_acquirer_percentage_issuer_country_id ON "acquirer"."countries" (id);
-- CREATE INDEX IF NOT EXISTS idx_acquirer_percentage_issuer_id ON "acquirer"."banks" (id);
-- CREATE INDEX IF NOT EXISTS idx_acquirer_percentage_acquirer_id ON "acquirer"."acquirers" (id);
-- CREATE INDEX IF NOT EXISTS idx_acquirer_percentage_ips_id ON "acquirer"."ips" (id);
--
-- CREATE TABLE IF NOT EXISTS "acquirer"."bank_services"
-- (
--     "created_at" TIMESTAMP DEFAULT NOW(),
--     "updated_at" TIMESTAMP DEFAULT NOW(),
--     "id"         SERIAL PRIMARY KEY,
--     "name"       VARCHAR(255) NOT NULL
-- );
--
-- CREATE INDEX IF NOT EXISTS idx_acquirer_bank_service_id ON "acquirer"."bank_services" (id);
--
-- CREATE TABLE IF NOT EXISTS "acquirer"."bank_limits"
-- (
--     "created_at"      TIMESTAMP DEFAULT NOW(),
--     "updated_at"      TIMESTAMP DEFAULT NOW(),
--     "id"              SERIAL PRIMARY KEY,
--     "other_limit"     VARCHAR(255),
--     "year"            INTEGER,
--     "month"           INTEGER,
--     "day"             INTEGER,
--     "week"            INTEGER,
--     "transaction"     INTEGER,
--     "bank_service_id" INTEGER NOT NULL REFERENCES "acquirer"."bank_services",
--     "acquirer_id"     INTEGER NOT NULL REFERENCES "acquirer"."acquirers"
-- );
--
-- CREATE INDEX IF NOT EXISTS idx_acquirer_bank_limits_bank_service_id ON "acquirer"."bank_services" (id);
-- CREATE INDEX IF NOT EXISTS idx_acquirer_bank_limits_acquirer_id ON "acquirer"."acquirers" (id);
--
-- CREATE TABLE IF NOT EXISTS "acquirer"."country_currencies"
-- (
--     "created_at"  TIMESTAMP DEFAULT NOW(),
--     "updated_at"  TIMESTAMP DEFAULT NOW(),
--     "country_id"  INTEGER NOT NULL REFERENCES "acquirer"."countries",
--     "currency_id" INTEGER NOT NULL REFERENCES "acquirer"."currencies",
--     PRIMARY KEY (country_id, currency_id)
-- );
-- CREATE INDEX IF NOT EXISTS idx_acquirer_country_currencies_country_id ON "acquirer"."countries" (id);
-- CREATE INDEX IF NOT EXISTS idx_acquirer_country_currencies_currency_id ON "acquirer"."currencies" (id);
--
-- CREATE TABLE IF NOT EXISTS "acquirer"."acquirer_currencies"
-- (
--     "created_at"  TIMESTAMP DEFAULT NOW(),
--     "updated_at"  TIMESTAMP DEFAULT NOW(),
--     "acquirer_id" BIGINT  NOT NULL REFERENCES "acquirer"."acquirers",
--     "currency_id" INTEGER NOT NULL REFERENCES "acquirer"."currencies",
--     PRIMARY KEY (acquirer_id, currency_id)
-- );
-- CREATE INDEX IF NOT EXISTS idx_acquirer_country_currencies_acquirer_id ON "acquirer"."acquirers" (id);
-- CREATE INDEX IF NOT EXISTS idx_acquirer_country_currencies_currency_id ON "acquirer"."currencies" (id);
--
-- CREATE TABLE IF NOT EXISTS "acquirer"."ips_countries"
-- (
--     "created_at" TIMESTAMP DEFAULT NOW(),
--     "updated_at" TIMESTAMP DEFAULT NOW(),
--     "ips_id"     INTEGER NOT NULL REFERENCES "acquirer"."ips",
--     "country_id" INTEGER NOT NULL REFERENCES "acquirer"."countries",
--     PRIMARY KEY (ips_id, country_id)
-- );
--
-- CREATE INDEX IF NOT EXISTS idx_acquirer_ips_countries_ips_id ON "acquirer"."ips" (id);
-- CREATE INDEX IF NOT EXISTS idx_acquirer_ips_countries_country_id ON "acquirer"."countries" (id);
--
-- CREATE TABLE IF NOT EXISTS "acquirer"."ips_digit_codes"
-- (
--     "created_at"   TIMESTAMP DEFAULT NOW(),
--     "updated_at"   TIMESTAMP DEFAULT NOW(),
--     "id"           SERIAL PRIMARY KEY,
--     "first_digit"  INT    NOT NULL,
--     "second_digit" INT       DEFAULT NULL,
--     "ips_id"       BIGINT NOT NULL REFERENCES "acquirer"."ips" (id)
-- );
--
-- CREATE INDEX IF NOT EXISTS idx_acquirer_ips_digit_codes_id ON "acquirer"."ips_digit_codes" (id);

-- +goose StatementEnd