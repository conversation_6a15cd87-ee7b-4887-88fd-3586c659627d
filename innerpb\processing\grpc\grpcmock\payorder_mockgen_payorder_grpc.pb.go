// Code generated by MockGen. DO NOT EDIT.
// Source: payorder_grpc.pb.go

// Package grpcmock is a generated GoMock package.
package grpcmock

import (
	context "context"
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
	grpc "google.golang.org/grpc"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
)

// MockPayorderClient is a mock of PayorderClient interface.
type MockPayorderClient struct {
	ctrl     *gomock.Controller
	recorder *MockPayorderClientMockRecorder
}

// MockPayorderClientMockRecorder is the mock recorder for MockPayorderClient.
type MockPayorderClientMockRecorder struct {
	mock *MockPayorderClient
}

// NewMockPayorderClient creates a new mock instance.
func NewMockPayorderClient(ctrl *gomock.Controller) *MockPayorderClient {
	mock := &MockPayorderClient{ctrl: ctrl}
	mock.recorder = &MockPayorderClientMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockPayorderClient) EXPECT() *MockPayorderClientMockRecorder {
	return m.recorder
}

// StartCheckInProcessOrdersWorker mocks base method.
func (m *MockPayorderClient) StartCheckInProcessOrdersWorker(ctx context.Context, in *emptypb.Empty, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "StartCheckInProcessOrdersWorker", varargs...)
	ret0, _ := ret[0].(*emptypb.Empty)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// StartCheckInProcessOrdersWorker indicates an expected call of StartCheckInProcessOrdersWorker.
func (mr *MockPayorderClientMockRecorder) StartCheckInProcessOrdersWorker(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "StartCheckInProcessOrdersWorker", reflect.TypeOf((*MockPayorderClient)(nil).StartCheckInProcessOrdersWorker), varargs...)
}

// StartCreateNewPaymentOrdersWorker mocks base method.
func (m *MockPayorderClient) StartCreateNewPaymentOrdersWorker(ctx context.Context, in *emptypb.Empty, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "StartCreateNewPaymentOrdersWorker", varargs...)
	ret0, _ := ret[0].(*emptypb.Empty)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// StartCreateNewPaymentOrdersWorker indicates an expected call of StartCreateNewPaymentOrdersWorker.
func (mr *MockPayorderClientMockRecorder) StartCreateNewPaymentOrdersWorker(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "StartCreateNewPaymentOrdersWorker", reflect.TypeOf((*MockPayorderClient)(nil).StartCreateNewPaymentOrdersWorker), varargs...)
}

// MockPayorderServer is a mock of PayorderServer interface.
type MockPayorderServer struct {
	ctrl     *gomock.Controller
	recorder *MockPayorderServerMockRecorder
}

// MockPayorderServerMockRecorder is the mock recorder for MockPayorderServer.
type MockPayorderServerMockRecorder struct {
	mock *MockPayorderServer
}

// NewMockPayorderServer creates a new mock instance.
func NewMockPayorderServer(ctrl *gomock.Controller) *MockPayorderServer {
	mock := &MockPayorderServer{ctrl: ctrl}
	mock.recorder = &MockPayorderServerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockPayorderServer) EXPECT() *MockPayorderServerMockRecorder {
	return m.recorder
}

// StartCheckInProcessOrdersWorker mocks base method.
func (m *MockPayorderServer) StartCheckInProcessOrdersWorker(arg0 context.Context, arg1 *emptypb.Empty) (*emptypb.Empty, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "StartCheckInProcessOrdersWorker", arg0, arg1)
	ret0, _ := ret[0].(*emptypb.Empty)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// StartCheckInProcessOrdersWorker indicates an expected call of StartCheckInProcessOrdersWorker.
func (mr *MockPayorderServerMockRecorder) StartCheckInProcessOrdersWorker(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "StartCheckInProcessOrdersWorker", reflect.TypeOf((*MockPayorderServer)(nil).StartCheckInProcessOrdersWorker), arg0, arg1)
}

// StartCreateNewPaymentOrdersWorker mocks base method.
func (m *MockPayorderServer) StartCreateNewPaymentOrdersWorker(arg0 context.Context, arg1 *emptypb.Empty) (*emptypb.Empty, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "StartCreateNewPaymentOrdersWorker", arg0, arg1)
	ret0, _ := ret[0].(*emptypb.Empty)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// StartCreateNewPaymentOrdersWorker indicates an expected call of StartCreateNewPaymentOrdersWorker.
func (mr *MockPayorderServerMockRecorder) StartCreateNewPaymentOrdersWorker(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "StartCreateNewPaymentOrdersWorker", reflect.TypeOf((*MockPayorderServer)(nil).StartCreateNewPaymentOrdersWorker), arg0, arg1)
}

// mustEmbedUnimplementedPayorderServer mocks base method.
func (m *MockPayorderServer) mustEmbedUnimplementedPayorderServer() {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "mustEmbedUnimplementedPayorderServer")
}

// mustEmbedUnimplementedPayorderServer indicates an expected call of mustEmbedUnimplementedPayorderServer.
func (mr *MockPayorderServerMockRecorder) mustEmbedUnimplementedPayorderServer() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "mustEmbedUnimplementedPayorderServer", reflect.TypeOf((*MockPayorderServer)(nil).mustEmbedUnimplementedPayorderServer))
}

// MockUnsafePayorderServer is a mock of UnsafePayorderServer interface.
type MockUnsafePayorderServer struct {
	ctrl     *gomock.Controller
	recorder *MockUnsafePayorderServerMockRecorder
}

// MockUnsafePayorderServerMockRecorder is the mock recorder for MockUnsafePayorderServer.
type MockUnsafePayorderServerMockRecorder struct {
	mock *MockUnsafePayorderServer
}

// NewMockUnsafePayorderServer creates a new mock instance.
func NewMockUnsafePayorderServer(ctrl *gomock.Controller) *MockUnsafePayorderServer {
	mock := &MockUnsafePayorderServer{ctrl: ctrl}
	mock.recorder = &MockUnsafePayorderServerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockUnsafePayorderServer) EXPECT() *MockUnsafePayorderServerMockRecorder {
	return m.recorder
}

// mustEmbedUnimplementedPayorderServer mocks base method.
func (m *MockUnsafePayorderServer) mustEmbedUnimplementedPayorderServer() {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "mustEmbedUnimplementedPayorderServer")
}

// mustEmbedUnimplementedPayorderServer indicates an expected call of mustEmbedUnimplementedPayorderServer.
func (mr *MockUnsafePayorderServerMockRecorder) mustEmbedUnimplementedPayorderServer() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "mustEmbedUnimplementedPayorderServer", reflect.TypeOf((*MockUnsafePayorderServer)(nil).mustEmbedUnimplementedPayorderServer))
}
