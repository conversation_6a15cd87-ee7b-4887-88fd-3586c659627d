// Code generated by MockGen. DO NOT EDIT.
// Source: fiscalization_grpc.pb.go

// Package grpcmock is a generated GoMock package.
package grpcmock

import (
	context "context"
	reflect "reflect"

	grpc "git.local/sensitive/innerpb/processing/grpc"
	gomock "github.com/golang/mock/gomock"
	grpc0 "google.golang.org/grpc"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
)

// MockFiscalizationClient is a mock of FiscalizationClient interface.
type MockFiscalizationClient struct {
	ctrl     *gomock.Controller
	recorder *MockFiscalizationClientMockRecorder
}

// MockFiscalizationClientMockRecorder is the mock recorder for MockFiscalizationClient.
type MockFiscalizationClientMockRecorder struct {
	mock *MockFiscalizationClient
}

// NewMockFiscalizationClient creates a new mock instance.
func NewMockFiscalizationClient(ctrl *gomock.Controller) *MockFiscalizationClient {
	mock := &MockFiscalizationClient{ctrl: ctrl}
	mock.recorder = &MockFiscalizationClientMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockFiscalizationClient) EXPECT() *MockFiscalizationClientMockRecorder {
	return m.recorder
}

// FinalizeFiscalizations mocks base method.
func (m *MockFiscalizationClient) FinalizeFiscalizations(ctx context.Context, in *emptypb.Empty, opts ...grpc0.CallOption) (*emptypb.Empty, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "FinalizeFiscalizations", varargs...)
	ret0, _ := ret[0].(*emptypb.Empty)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// FinalizeFiscalizations indicates an expected call of FinalizeFiscalizations.
func (mr *MockFiscalizationClientMockRecorder) FinalizeFiscalizations(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "FinalizeFiscalizations", reflect.TypeOf((*MockFiscalizationClient)(nil).FinalizeFiscalizations), varargs...)
}

// GetFiscalInfoByTransactionIDV1 mocks base method.
func (m *MockFiscalizationClient) GetFiscalInfoByTransactionIDV1(ctx context.Context, in *grpc.GetFiscalInfoByTransactionIDRequestV1, opts ...grpc0.CallOption) (*grpc.GetFiscalInfoByTransactionIDResponseV1, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetFiscalInfoByTransactionIDV1", varargs...)
	ret0, _ := ret[0].(*grpc.GetFiscalInfoByTransactionIDResponseV1)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetFiscalInfoByTransactionIDV1 indicates an expected call of GetFiscalInfoByTransactionIDV1.
func (mr *MockFiscalizationClientMockRecorder) GetFiscalInfoByTransactionIDV1(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetFiscalInfoByTransactionIDV1", reflect.TypeOf((*MockFiscalizationClient)(nil).GetFiscalInfoByTransactionIDV1), varargs...)
}

// MakeFiscalizationV1 mocks base method.
func (m *MockFiscalizationClient) MakeFiscalizationV1(ctx context.Context, in *grpc.MakeFiscalizationRequestV1, opts ...grpc0.CallOption) (*emptypb.Empty, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "MakeFiscalizationV1", varargs...)
	ret0, _ := ret[0].(*emptypb.Empty)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// MakeFiscalizationV1 indicates an expected call of MakeFiscalizationV1.
func (mr *MockFiscalizationClientMockRecorder) MakeFiscalizationV1(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "MakeFiscalizationV1", reflect.TypeOf((*MockFiscalizationClient)(nil).MakeFiscalizationV1), varargs...)
}

// ManageShifts mocks base method.
func (m *MockFiscalizationClient) ManageShifts(ctx context.Context, in *emptypb.Empty, opts ...grpc0.CallOption) (*emptypb.Empty, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "ManageShifts", varargs...)
	ret0, _ := ret[0].(*emptypb.Empty)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ManageShifts indicates an expected call of ManageShifts.
func (mr *MockFiscalizationClientMockRecorder) ManageShifts(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ManageShifts", reflect.TypeOf((*MockFiscalizationClient)(nil).ManageShifts), varargs...)
}

// MockFiscalizationServer is a mock of FiscalizationServer interface.
type MockFiscalizationServer struct {
	ctrl     *gomock.Controller
	recorder *MockFiscalizationServerMockRecorder
}

// MockFiscalizationServerMockRecorder is the mock recorder for MockFiscalizationServer.
type MockFiscalizationServerMockRecorder struct {
	mock *MockFiscalizationServer
}

// NewMockFiscalizationServer creates a new mock instance.
func NewMockFiscalizationServer(ctrl *gomock.Controller) *MockFiscalizationServer {
	mock := &MockFiscalizationServer{ctrl: ctrl}
	mock.recorder = &MockFiscalizationServerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockFiscalizationServer) EXPECT() *MockFiscalizationServerMockRecorder {
	return m.recorder
}

// FinalizeFiscalizations mocks base method.
func (m *MockFiscalizationServer) FinalizeFiscalizations(arg0 context.Context, arg1 *emptypb.Empty) (*emptypb.Empty, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "FinalizeFiscalizations", arg0, arg1)
	ret0, _ := ret[0].(*emptypb.Empty)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// FinalizeFiscalizations indicates an expected call of FinalizeFiscalizations.
func (mr *MockFiscalizationServerMockRecorder) FinalizeFiscalizations(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "FinalizeFiscalizations", reflect.TypeOf((*MockFiscalizationServer)(nil).FinalizeFiscalizations), arg0, arg1)
}

// GetFiscalInfoByTransactionIDV1 mocks base method.
func (m *MockFiscalizationServer) GetFiscalInfoByTransactionIDV1(arg0 context.Context, arg1 *grpc.GetFiscalInfoByTransactionIDRequestV1) (*grpc.GetFiscalInfoByTransactionIDResponseV1, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetFiscalInfoByTransactionIDV1", arg0, arg1)
	ret0, _ := ret[0].(*grpc.GetFiscalInfoByTransactionIDResponseV1)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetFiscalInfoByTransactionIDV1 indicates an expected call of GetFiscalInfoByTransactionIDV1.
func (mr *MockFiscalizationServerMockRecorder) GetFiscalInfoByTransactionIDV1(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetFiscalInfoByTransactionIDV1", reflect.TypeOf((*MockFiscalizationServer)(nil).GetFiscalInfoByTransactionIDV1), arg0, arg1)
}

// MakeFiscalizationV1 mocks base method.
func (m *MockFiscalizationServer) MakeFiscalizationV1(arg0 context.Context, arg1 *grpc.MakeFiscalizationRequestV1) (*emptypb.Empty, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "MakeFiscalizationV1", arg0, arg1)
	ret0, _ := ret[0].(*emptypb.Empty)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// MakeFiscalizationV1 indicates an expected call of MakeFiscalizationV1.
func (mr *MockFiscalizationServerMockRecorder) MakeFiscalizationV1(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "MakeFiscalizationV1", reflect.TypeOf((*MockFiscalizationServer)(nil).MakeFiscalizationV1), arg0, arg1)
}

// ManageShifts mocks base method.
func (m *MockFiscalizationServer) ManageShifts(arg0 context.Context, arg1 *emptypb.Empty) (*emptypb.Empty, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ManageShifts", arg0, arg1)
	ret0, _ := ret[0].(*emptypb.Empty)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ManageShifts indicates an expected call of ManageShifts.
func (mr *MockFiscalizationServerMockRecorder) ManageShifts(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ManageShifts", reflect.TypeOf((*MockFiscalizationServer)(nil).ManageShifts), arg0, arg1)
}

// mustEmbedUnimplementedFiscalizationServer mocks base method.
func (m *MockFiscalizationServer) mustEmbedUnimplementedFiscalizationServer() {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "mustEmbedUnimplementedFiscalizationServer")
}

// mustEmbedUnimplementedFiscalizationServer indicates an expected call of mustEmbedUnimplementedFiscalizationServer.
func (mr *MockFiscalizationServerMockRecorder) mustEmbedUnimplementedFiscalizationServer() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "mustEmbedUnimplementedFiscalizationServer", reflect.TypeOf((*MockFiscalizationServer)(nil).mustEmbedUnimplementedFiscalizationServer))
}

// MockUnsafeFiscalizationServer is a mock of UnsafeFiscalizationServer interface.
type MockUnsafeFiscalizationServer struct {
	ctrl     *gomock.Controller
	recorder *MockUnsafeFiscalizationServerMockRecorder
}

// MockUnsafeFiscalizationServerMockRecorder is the mock recorder for MockUnsafeFiscalizationServer.
type MockUnsafeFiscalizationServerMockRecorder struct {
	mock *MockUnsafeFiscalizationServer
}

// NewMockUnsafeFiscalizationServer creates a new mock instance.
func NewMockUnsafeFiscalizationServer(ctrl *gomock.Controller) *MockUnsafeFiscalizationServer {
	mock := &MockUnsafeFiscalizationServer{ctrl: ctrl}
	mock.recorder = &MockUnsafeFiscalizationServerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockUnsafeFiscalizationServer) EXPECT() *MockUnsafeFiscalizationServerMockRecorder {
	return m.recorder
}

// mustEmbedUnimplementedFiscalizationServer mocks base method.
func (m *MockUnsafeFiscalizationServer) mustEmbedUnimplementedFiscalizationServer() {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "mustEmbedUnimplementedFiscalizationServer")
}

// mustEmbedUnimplementedFiscalizationServer indicates an expected call of mustEmbedUnimplementedFiscalizationServer.
func (mr *MockUnsafeFiscalizationServerMockRecorder) mustEmbedUnimplementedFiscalizationServer() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "mustEmbedUnimplementedFiscalizationServer", reflect.TypeOf((*MockUnsafeFiscalizationServer)(nil).mustEmbedUnimplementedFiscalizationServer))
}
