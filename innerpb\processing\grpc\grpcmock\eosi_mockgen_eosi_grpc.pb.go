// Code generated by MockGen. DO NOT EDIT.
// Source: eosi_grpc.pb.go

// Package grpcmock is a generated GoMock package.
package grpcmock

import (
	context "context"
	reflect "reflect"

	grpc "git.local/sensitive/innerpb/processing/grpc"
	gomock "github.com/golang/mock/gomock"
	grpc0 "google.golang.org/grpc"
)

// MockEosiClient is a mock of EosiClient interface.
type MockEosiClient struct {
	ctrl     *gomock.Controller
	recorder *MockEosiClientMockRecorder
}

// MockEosiClientMockRecorder is the mock recorder for MockEosiClient.
type MockEosiClientMockRecorder struct {
	mock *MockEosiClient
}

// NewMockEosiClient creates a new mock instance.
func NewMockEosiClient(ctrl *gomock.Controller) *MockEosiClient {
	mock := &MockEosiClient{ctrl: ctrl}
	mock.recorder = &MockEosiClientMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockEosiClient) EXPECT() *MockEosiClientMockRecorder {
	return m.recorder
}

// GetOrderingIdentifier mocks base method.
func (m *MockEosiClient) GetOrderingIdentifier(ctx context.Context, in *grpc.GetOrderingIdentifierRequest, opts ...grpc0.CallOption) (*grpc.GetOrderingIdentifierResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetOrderingIdentifier", varargs...)
	ret0, _ := ret[0].(*grpc.GetOrderingIdentifierResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetOrderingIdentifier indicates an expected call of GetOrderingIdentifier.
func (mr *MockEosiClientMockRecorder) GetOrderingIdentifier(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetOrderingIdentifier", reflect.TypeOf((*MockEosiClient)(nil).GetOrderingIdentifier), varargs...)
}

// GetPaymentOrder mocks base method.
func (m *MockEosiClient) GetPaymentOrder(ctx context.Context, in *grpc.GetPaymentOrderRequest, opts ...grpc0.CallOption) (*grpc.GetPaymentOrderResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetPaymentOrder", varargs...)
	ret0, _ := ret[0].(*grpc.GetPaymentOrderResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetPaymentOrder indicates an expected call of GetPaymentOrder.
func (mr *MockEosiClientMockRecorder) GetPaymentOrder(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetPaymentOrder", reflect.TypeOf((*MockEosiClient)(nil).GetPaymentOrder), varargs...)
}

// MockEosiServer is a mock of EosiServer interface.
type MockEosiServer struct {
	ctrl     *gomock.Controller
	recorder *MockEosiServerMockRecorder
}

// MockEosiServerMockRecorder is the mock recorder for MockEosiServer.
type MockEosiServerMockRecorder struct {
	mock *MockEosiServer
}

// NewMockEosiServer creates a new mock instance.
func NewMockEosiServer(ctrl *gomock.Controller) *MockEosiServer {
	mock := &MockEosiServer{ctrl: ctrl}
	mock.recorder = &MockEosiServerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockEosiServer) EXPECT() *MockEosiServerMockRecorder {
	return m.recorder
}

// GetOrderingIdentifier mocks base method.
func (m *MockEosiServer) GetOrderingIdentifier(arg0 context.Context, arg1 *grpc.GetOrderingIdentifierRequest) (*grpc.GetOrderingIdentifierResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetOrderingIdentifier", arg0, arg1)
	ret0, _ := ret[0].(*grpc.GetOrderingIdentifierResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetOrderingIdentifier indicates an expected call of GetOrderingIdentifier.
func (mr *MockEosiServerMockRecorder) GetOrderingIdentifier(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetOrderingIdentifier", reflect.TypeOf((*MockEosiServer)(nil).GetOrderingIdentifier), arg0, arg1)
}

// GetPaymentOrder mocks base method.
func (m *MockEosiServer) GetPaymentOrder(arg0 context.Context, arg1 *grpc.GetPaymentOrderRequest) (*grpc.GetPaymentOrderResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetPaymentOrder", arg0, arg1)
	ret0, _ := ret[0].(*grpc.GetPaymentOrderResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetPaymentOrder indicates an expected call of GetPaymentOrder.
func (mr *MockEosiServerMockRecorder) GetPaymentOrder(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetPaymentOrder", reflect.TypeOf((*MockEosiServer)(nil).GetPaymentOrder), arg0, arg1)
}

// mustEmbedUnimplementedEosiServer mocks base method.
func (m *MockEosiServer) mustEmbedUnimplementedEosiServer() {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "mustEmbedUnimplementedEosiServer")
}

// mustEmbedUnimplementedEosiServer indicates an expected call of mustEmbedUnimplementedEosiServer.
func (mr *MockEosiServerMockRecorder) mustEmbedUnimplementedEosiServer() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "mustEmbedUnimplementedEosiServer", reflect.TypeOf((*MockEosiServer)(nil).mustEmbedUnimplementedEosiServer))
}

// MockUnsafeEosiServer is a mock of UnsafeEosiServer interface.
type MockUnsafeEosiServer struct {
	ctrl     *gomock.Controller
	recorder *MockUnsafeEosiServerMockRecorder
}

// MockUnsafeEosiServerMockRecorder is the mock recorder for MockUnsafeEosiServer.
type MockUnsafeEosiServerMockRecorder struct {
	mock *MockUnsafeEosiServer
}

// NewMockUnsafeEosiServer creates a new mock instance.
func NewMockUnsafeEosiServer(ctrl *gomock.Controller) *MockUnsafeEosiServer {
	mock := &MockUnsafeEosiServer{ctrl: ctrl}
	mock.recorder = &MockUnsafeEosiServerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockUnsafeEosiServer) EXPECT() *MockUnsafeEosiServerMockRecorder {
	return m.recorder
}

// mustEmbedUnimplementedEosiServer mocks base method.
func (m *MockUnsafeEosiServer) mustEmbedUnimplementedEosiServer() {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "mustEmbedUnimplementedEosiServer")
}

// mustEmbedUnimplementedEosiServer indicates an expected call of mustEmbedUnimplementedEosiServer.
func (mr *MockUnsafeEosiServerMockRecorder) mustEmbedUnimplementedEosiServer() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "mustEmbedUnimplementedEosiServer", reflect.TypeOf((*MockUnsafeEosiServer)(nil).mustEmbedUnimplementedEosiServer))
}
