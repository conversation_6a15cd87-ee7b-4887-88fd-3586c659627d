// Code generated by protoc-gen-go-mvp. DO NOT EDIT.
// versions:
// - protoc-gen-go-mvp v2.0.1
// created by mvp authors

package grpc

import (
	dog "git.local/sensitive/sdk/dog"
	gin "github.com/gin-gonic/gin"
	metadata "google.golang.org/grpc/metadata"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
	http "net/http"
)

func RegisterGinMultiaccountingRouterGroup(
	in *gin.RouterGroup,
	handler func(func(c *gin.Context) error) gin.HandlerFunc,
) error {
	service, err := NewGinMultiaccountingService()
	if err != nil {
		return err
	}

	routerGroup := in.Group("processing.multiaccounting.multiaccounting.Multiaccounting")
	routerGroup.PUT("/MakeMerchantCheck", handler(service.MakeMerchantCheck))
	routerGroup.PUT("/MakeTransfer", handler(service.MakeTransfer))
	routerGroup.PUT("/AcceptTransfer", handler(service.AcceptTransfer))
	routerGroup.PUT("/DeclineTransfer", handler(service.DeclineTransfer))
	routerGroup.PUT("/RedoTransfer", handler(service.RedoTransfer))
	routerGroup.PUT("/GetTransferDetails", handler(service.GetTransferDetails))
	routerGroup.PUT("/GetTransfersList", handler(service.GetTransfersList))
	routerGroup.PUT("/GetAccountBalance", handler(service.GetAccountBalance))
	routerGroup.PUT("/GetAccountStatement", handler(service.GetAccountStatement))
	routerGroup.PUT("/GetAccountIdentifier", handler(service.GetAccountIdentifier))
	return nil
}

func NewGinMultiaccountingService() (GinMultiaccountingServer, error) {
	client, err := NewPreparedMultiaccountingClient()
	if err != nil {
		return nil, err
	}

	return &ginMultiaccountingServer{
		client: NewLoggedMultiaccountingClient(
			NewIamMultiaccountingClient(client),
		),
	}, nil
}

//go:generate mockgen -destination=./grpcmock/multiaccounting.gin.pb.go -package=grpcmock -source=multiaccounting.gin.pb.go GinMultiaccountingServer
type GinMultiaccountingServer interface {
	MakeMerchantCheck(c *gin.Context) error
	MakeTransfer(c *gin.Context) error
	AcceptTransfer(c *gin.Context) error
	DeclineTransfer(c *gin.Context) error
	RedoTransfer(c *gin.Context) error
	GetTransferDetails(c *gin.Context) error
	GetTransfersList(c *gin.Context) error
	GetAccountBalance(c *gin.Context) error
	GetAccountStatement(c *gin.Context) error
	GetAccountIdentifier(c *gin.Context) error
}

var _ GinMultiaccountingServer = (*ginMultiaccountingServer)(nil)

type ginMultiaccountingServer struct {
	client MultiaccountingClient
}

type Multiaccounting_MakeMerchantCheck_Success struct {
	// Status is the status of the response. Set true
	Status bool `json:"status"`
	// Message is a human-readable explanation of the status
	Message string `json:"message"`
	// StatusCode is the GoErr status. Set 0
	StatusCode int `json:"status_code"`
	// Result is the result of the request
	Result *MakeMerchantCheckResponse `json:"result"`
}

type Multiaccounting_MakeMerchantCheck_Failure struct {
	// Status is the status of the response. Set false
	Status bool `json:"status"`
	// Message is a human-readable explanation of the status
	Message string `json:"message"`
	// StatusCode is the GoErr status
	StatusCode int `json:"status_code"`
}

// MakeMerchantCheck
// @Summary MakeMerchantCheck
// @Security bearerAuth
// @ID Multiaccounting_MakeMerchantCheck
// @Accept json
// @Param request body MakeMerchantCheckRequest true "MakeMerchantCheckRequest"
// @Success 200 {object} Multiaccounting_MakeMerchantCheck_Success
// @Failure 401 {object} Multiaccounting_MakeMerchantCheck_Failure "goerrpkg.ErrorTypeAuthorization"
// @Failure 404 {object} Multiaccounting_MakeMerchantCheck_Failure "goerrpkg.ErrorTypeNotFound"
// @Failure 400 {object} Multiaccounting_MakeMerchantCheck_Failure "goerrpkg.ErrorTypeBadRequest"
// @Failure 500 {object} Multiaccounting_MakeMerchantCheck_Failure "goerrpkg.ErrorTypeUnexpectedError"
// @Failure 400 {object} Multiaccounting_MakeMerchantCheck_Failure "gin.ErrorTypeBind"
// @Failure 500 {object} Multiaccounting_MakeMerchantCheck_Failure "Undefined error"
// @Produce json
// @Router /processing.multiaccounting.multiaccounting.Multiaccounting/MakeMerchantCheck [put]
// @tags Multiaccounting
func (s *ginMultiaccountingServer) MakeMerchantCheck(c *gin.Context) error {
	ctx, span := dog.CreateSpan(c.Request.Context(), "GinMultiaccountingServer_MakeMerchantCheck")
	defer span.End()

	var request MakeMerchantCheckRequest
	if err := c.ShouldBindJSON(&request); err != nil {
		return err
	}

	ctx = metadata.NewOutgoingContext(ctx, metadata.MD(c.Request.Header))

	response, err := s.client.MakeMerchantCheck(ctx, &request)
	if err != nil {
		return err
	}

	c.JSON(http.StatusOK, &Multiaccounting_MakeMerchantCheck_Success{
		Status:     true,
		Message:    "Success",
		StatusCode: 0,
		Result:     response,
	})
	return nil
}

type Multiaccounting_MakeTransfer_Success struct {
	// Status is the status of the response. Set true
	Status bool `json:"status"`
	// Message is a human-readable explanation of the status
	Message string `json:"message"`
	// StatusCode is the GoErr status. Set 0
	StatusCode int `json:"status_code"`
	// Result is the result of the request
	Result *MakeTransferResponse `json:"result"`
}

type Multiaccounting_MakeTransfer_Failure struct {
	// Status is the status of the response. Set false
	Status bool `json:"status"`
	// Message is a human-readable explanation of the status
	Message string `json:"message"`
	// StatusCode is the GoErr status
	StatusCode int `json:"status_code"`
}

// MakeTransfer
// @Summary MakeTransfer
// @Security bearerAuth
// @ID Multiaccounting_MakeTransfer
// @Accept json
// @Param request body MakeTransferRequest true "MakeTransferRequest"
// @Success 200 {object} Multiaccounting_MakeTransfer_Success
// @Failure 401 {object} Multiaccounting_MakeTransfer_Failure "goerrpkg.ErrorTypeAuthorization"
// @Failure 404 {object} Multiaccounting_MakeTransfer_Failure "goerrpkg.ErrorTypeNotFound"
// @Failure 400 {object} Multiaccounting_MakeTransfer_Failure "goerrpkg.ErrorTypeBadRequest"
// @Failure 500 {object} Multiaccounting_MakeTransfer_Failure "goerrpkg.ErrorTypeUnexpectedError"
// @Failure 400 {object} Multiaccounting_MakeTransfer_Failure "gin.ErrorTypeBind"
// @Failure 500 {object} Multiaccounting_MakeTransfer_Failure "Undefined error"
// @Produce json
// @Router /processing.multiaccounting.multiaccounting.Multiaccounting/MakeTransfer [put]
// @tags Multiaccounting
func (s *ginMultiaccountingServer) MakeTransfer(c *gin.Context) error {
	ctx, span := dog.CreateSpan(c.Request.Context(), "GinMultiaccountingServer_MakeTransfer")
	defer span.End()

	var request MakeTransferRequest
	if err := c.ShouldBindJSON(&request); err != nil {
		return err
	}

	ctx = metadata.NewOutgoingContext(ctx, metadata.MD(c.Request.Header))

	response, err := s.client.MakeTransfer(ctx, &request)
	if err != nil {
		return err
	}

	c.JSON(http.StatusOK, &Multiaccounting_MakeTransfer_Success{
		Status:     true,
		Message:    "Success",
		StatusCode: 0,
		Result:     response,
	})
	return nil
}

type Multiaccounting_AcceptTransfer_Success struct {
	// Status is the status of the response. Set true
	Status bool `json:"status"`
	// Message is a human-readable explanation of the status
	Message string `json:"message"`
	// StatusCode is the GoErr status. Set 0
	StatusCode int `json:"status_code"`
	// Result is the result of the request
	Result *AcceptTransferResponse `json:"result"`
}

type Multiaccounting_AcceptTransfer_Failure struct {
	// Status is the status of the response. Set false
	Status bool `json:"status"`
	// Message is a human-readable explanation of the status
	Message string `json:"message"`
	// StatusCode is the GoErr status
	StatusCode int `json:"status_code"`
}

// AcceptTransfer
// @Summary AcceptTransfer
// @Security bearerAuth
// @ID Multiaccounting_AcceptTransfer
// @Accept json
// @Param request body AcceptTransferRequest true "AcceptTransferRequest"
// @Success 200 {object} Multiaccounting_AcceptTransfer_Success
// @Failure 401 {object} Multiaccounting_AcceptTransfer_Failure "goerrpkg.ErrorTypeAuthorization"
// @Failure 404 {object} Multiaccounting_AcceptTransfer_Failure "goerrpkg.ErrorTypeNotFound"
// @Failure 400 {object} Multiaccounting_AcceptTransfer_Failure "goerrpkg.ErrorTypeBadRequest"
// @Failure 500 {object} Multiaccounting_AcceptTransfer_Failure "goerrpkg.ErrorTypeUnexpectedError"
// @Failure 400 {object} Multiaccounting_AcceptTransfer_Failure "gin.ErrorTypeBind"
// @Failure 500 {object} Multiaccounting_AcceptTransfer_Failure "Undefined error"
// @Produce json
// @Router /processing.multiaccounting.multiaccounting.Multiaccounting/AcceptTransfer [put]
// @tags Multiaccounting
func (s *ginMultiaccountingServer) AcceptTransfer(c *gin.Context) error {
	ctx, span := dog.CreateSpan(c.Request.Context(), "GinMultiaccountingServer_AcceptTransfer")
	defer span.End()

	var request AcceptTransferRequest
	if err := c.ShouldBindJSON(&request); err != nil {
		return err
	}

	ctx = metadata.NewOutgoingContext(ctx, metadata.MD(c.Request.Header))

	response, err := s.client.AcceptTransfer(ctx, &request)
	if err != nil {
		return err
	}

	c.JSON(http.StatusOK, &Multiaccounting_AcceptTransfer_Success{
		Status:     true,
		Message:    "Success",
		StatusCode: 0,
		Result:     response,
	})
	return nil
}

type Multiaccounting_DeclineTransfer_Success struct {
	// Status is the status of the response. Set true
	Status bool `json:"status"`
	// Message is a human-readable explanation of the status
	Message string `json:"message"`
	// StatusCode is the GoErr status. Set 0
	StatusCode int `json:"status_code"`
	// Result is the result of the request
	Result *DeclineTransferResponse `json:"result"`
}

type Multiaccounting_DeclineTransfer_Failure struct {
	// Status is the status of the response. Set false
	Status bool `json:"status"`
	// Message is a human-readable explanation of the status
	Message string `json:"message"`
	// StatusCode is the GoErr status
	StatusCode int `json:"status_code"`
}

// DeclineTransfer
// @Summary DeclineTransfer
// @Security bearerAuth
// @ID Multiaccounting_DeclineTransfer
// @Accept json
// @Param request body DeclineTransferRequest true "DeclineTransferRequest"
// @Success 200 {object} Multiaccounting_DeclineTransfer_Success
// @Failure 401 {object} Multiaccounting_DeclineTransfer_Failure "goerrpkg.ErrorTypeAuthorization"
// @Failure 404 {object} Multiaccounting_DeclineTransfer_Failure "goerrpkg.ErrorTypeNotFound"
// @Failure 400 {object} Multiaccounting_DeclineTransfer_Failure "goerrpkg.ErrorTypeBadRequest"
// @Failure 500 {object} Multiaccounting_DeclineTransfer_Failure "goerrpkg.ErrorTypeUnexpectedError"
// @Failure 400 {object} Multiaccounting_DeclineTransfer_Failure "gin.ErrorTypeBind"
// @Failure 500 {object} Multiaccounting_DeclineTransfer_Failure "Undefined error"
// @Produce json
// @Router /processing.multiaccounting.multiaccounting.Multiaccounting/DeclineTransfer [put]
// @tags Multiaccounting
func (s *ginMultiaccountingServer) DeclineTransfer(c *gin.Context) error {
	ctx, span := dog.CreateSpan(c.Request.Context(), "GinMultiaccountingServer_DeclineTransfer")
	defer span.End()

	var request DeclineTransferRequest
	if err := c.ShouldBindJSON(&request); err != nil {
		return err
	}

	ctx = metadata.NewOutgoingContext(ctx, metadata.MD(c.Request.Header))

	response, err := s.client.DeclineTransfer(ctx, &request)
	if err != nil {
		return err
	}

	c.JSON(http.StatusOK, &Multiaccounting_DeclineTransfer_Success{
		Status:     true,
		Message:    "Success",
		StatusCode: 0,
		Result:     response,
	})
	return nil
}

type Multiaccounting_RedoTransfer_Success struct {
	// Status is the status of the response. Set true
	Status bool `json:"status"`
	// Message is a human-readable explanation of the status
	Message string `json:"message"`
	// StatusCode is the GoErr status. Set 0
	StatusCode int `json:"status_code"`
	// Result is the result of the request
	Result *RedoTransferResponse `json:"result"`
}

type Multiaccounting_RedoTransfer_Failure struct {
	// Status is the status of the response. Set false
	Status bool `json:"status"`
	// Message is a human-readable explanation of the status
	Message string `json:"message"`
	// StatusCode is the GoErr status
	StatusCode int `json:"status_code"`
}

// RedoTransfer
// @Summary RedoTransfer
// @Security bearerAuth
// @ID Multiaccounting_RedoTransfer
// @Accept json
// @Param request body RedoTransferRequest true "RedoTransferRequest"
// @Success 200 {object} Multiaccounting_RedoTransfer_Success
// @Failure 401 {object} Multiaccounting_RedoTransfer_Failure "goerrpkg.ErrorTypeAuthorization"
// @Failure 404 {object} Multiaccounting_RedoTransfer_Failure "goerrpkg.ErrorTypeNotFound"
// @Failure 400 {object} Multiaccounting_RedoTransfer_Failure "goerrpkg.ErrorTypeBadRequest"
// @Failure 500 {object} Multiaccounting_RedoTransfer_Failure "goerrpkg.ErrorTypeUnexpectedError"
// @Failure 400 {object} Multiaccounting_RedoTransfer_Failure "gin.ErrorTypeBind"
// @Failure 500 {object} Multiaccounting_RedoTransfer_Failure "Undefined error"
// @Produce json
// @Router /processing.multiaccounting.multiaccounting.Multiaccounting/RedoTransfer [put]
// @tags Multiaccounting
func (s *ginMultiaccountingServer) RedoTransfer(c *gin.Context) error {
	ctx, span := dog.CreateSpan(c.Request.Context(), "GinMultiaccountingServer_RedoTransfer")
	defer span.End()

	var request RedoTransferRequest
	if err := c.ShouldBindJSON(&request); err != nil {
		return err
	}

	ctx = metadata.NewOutgoingContext(ctx, metadata.MD(c.Request.Header))

	response, err := s.client.RedoTransfer(ctx, &request)
	if err != nil {
		return err
	}

	c.JSON(http.StatusOK, &Multiaccounting_RedoTransfer_Success{
		Status:     true,
		Message:    "Success",
		StatusCode: 0,
		Result:     response,
	})
	return nil
}

type Multiaccounting_GetTransferDetails_Success struct {
	// Status is the status of the response. Set true
	Status bool `json:"status"`
	// Message is a human-readable explanation of the status
	Message string `json:"message"`
	// StatusCode is the GoErr status. Set 0
	StatusCode int `json:"status_code"`
	// Result is the result of the request
	Result *GetTransferDetailsResponse `json:"result"`
}

type Multiaccounting_GetTransferDetails_Failure struct {
	// Status is the status of the response. Set false
	Status bool `json:"status"`
	// Message is a human-readable explanation of the status
	Message string `json:"message"`
	// StatusCode is the GoErr status
	StatusCode int `json:"status_code"`
}

// GetTransferDetails
// @Summary GetTransferDetails
// @Security bearerAuth
// @ID Multiaccounting_GetTransferDetails
// @Accept json
// @Param request body GetTransferDetailsRequest true "GetTransferDetailsRequest"
// @Success 200 {object} Multiaccounting_GetTransferDetails_Success
// @Failure 401 {object} Multiaccounting_GetTransferDetails_Failure "goerrpkg.ErrorTypeAuthorization"
// @Failure 404 {object} Multiaccounting_GetTransferDetails_Failure "goerrpkg.ErrorTypeNotFound"
// @Failure 400 {object} Multiaccounting_GetTransferDetails_Failure "goerrpkg.ErrorTypeBadRequest"
// @Failure 500 {object} Multiaccounting_GetTransferDetails_Failure "goerrpkg.ErrorTypeUnexpectedError"
// @Failure 400 {object} Multiaccounting_GetTransferDetails_Failure "gin.ErrorTypeBind"
// @Failure 500 {object} Multiaccounting_GetTransferDetails_Failure "Undefined error"
// @Produce json
// @Router /processing.multiaccounting.multiaccounting.Multiaccounting/GetTransferDetails [put]
// @tags Multiaccounting
func (s *ginMultiaccountingServer) GetTransferDetails(c *gin.Context) error {
	ctx, span := dog.CreateSpan(c.Request.Context(), "GinMultiaccountingServer_GetTransferDetails")
	defer span.End()

	var request GetTransferDetailsRequest
	if err := c.ShouldBindJSON(&request); err != nil {
		return err
	}

	ctx = metadata.NewOutgoingContext(ctx, metadata.MD(c.Request.Header))

	response, err := s.client.GetTransferDetails(ctx, &request)
	if err != nil {
		return err
	}

	c.JSON(http.StatusOK, &Multiaccounting_GetTransferDetails_Success{
		Status:     true,
		Message:    "Success",
		StatusCode: 0,
		Result:     response,
	})
	return nil
}

type Multiaccounting_GetTransfersList_Success struct {
	// Status is the status of the response. Set true
	Status bool `json:"status"`
	// Message is a human-readable explanation of the status
	Message string `json:"message"`
	// StatusCode is the GoErr status. Set 0
	StatusCode int `json:"status_code"`
	// Result is the result of the request
	Result *GetTransfersListResponse `json:"result"`
}

type Multiaccounting_GetTransfersList_Failure struct {
	// Status is the status of the response. Set false
	Status bool `json:"status"`
	// Message is a human-readable explanation of the status
	Message string `json:"message"`
	// StatusCode is the GoErr status
	StatusCode int `json:"status_code"`
}

// GetTransfersList
// @Summary GetTransfersList
// @Security bearerAuth
// @ID Multiaccounting_GetTransfersList
// @Accept json
// @Param request body GetTransfersListRequest true "GetTransfersListRequest"
// @Success 200 {object} Multiaccounting_GetTransfersList_Success
// @Failure 401 {object} Multiaccounting_GetTransfersList_Failure "goerrpkg.ErrorTypeAuthorization"
// @Failure 404 {object} Multiaccounting_GetTransfersList_Failure "goerrpkg.ErrorTypeNotFound"
// @Failure 400 {object} Multiaccounting_GetTransfersList_Failure "goerrpkg.ErrorTypeBadRequest"
// @Failure 500 {object} Multiaccounting_GetTransfersList_Failure "goerrpkg.ErrorTypeUnexpectedError"
// @Failure 400 {object} Multiaccounting_GetTransfersList_Failure "gin.ErrorTypeBind"
// @Failure 500 {object} Multiaccounting_GetTransfersList_Failure "Undefined error"
// @Produce json
// @Router /processing.multiaccounting.multiaccounting.Multiaccounting/GetTransfersList [put]
// @tags Multiaccounting
func (s *ginMultiaccountingServer) GetTransfersList(c *gin.Context) error {
	ctx, span := dog.CreateSpan(c.Request.Context(), "GinMultiaccountingServer_GetTransfersList")
	defer span.End()

	var request GetTransfersListRequest
	if err := c.ShouldBindJSON(&request); err != nil {
		return err
	}

	ctx = metadata.NewOutgoingContext(ctx, metadata.MD(c.Request.Header))

	response, err := s.client.GetTransfersList(ctx, &request)
	if err != nil {
		return err
	}

	c.JSON(http.StatusOK, &Multiaccounting_GetTransfersList_Success{
		Status:     true,
		Message:    "Success",
		StatusCode: 0,
		Result:     response,
	})
	return nil
}

type Multiaccounting_GetAccountBalance_Success struct {
	// Status is the status of the response. Set true
	Status bool `json:"status"`
	// Message is a human-readable explanation of the status
	Message string `json:"message"`
	// StatusCode is the GoErr status. Set 0
	StatusCode int `json:"status_code"`
	// Result is the result of the request
	Result *GetAccountBalanceResponse `json:"result"`
}

type Multiaccounting_GetAccountBalance_Failure struct {
	// Status is the status of the response. Set false
	Status bool `json:"status"`
	// Message is a human-readable explanation of the status
	Message string `json:"message"`
	// StatusCode is the GoErr status
	StatusCode int `json:"status_code"`
}

// GetAccountBalance
// @Summary GetAccountBalance
// @Security bearerAuth
// @ID Multiaccounting_GetAccountBalance
// @Accept json
// @Param request body GetAccountBalanceRequest true "GetAccountBalanceRequest"
// @Success 200 {object} Multiaccounting_GetAccountBalance_Success
// @Failure 401 {object} Multiaccounting_GetAccountBalance_Failure "goerrpkg.ErrorTypeAuthorization"
// @Failure 404 {object} Multiaccounting_GetAccountBalance_Failure "goerrpkg.ErrorTypeNotFound"
// @Failure 400 {object} Multiaccounting_GetAccountBalance_Failure "goerrpkg.ErrorTypeBadRequest"
// @Failure 500 {object} Multiaccounting_GetAccountBalance_Failure "goerrpkg.ErrorTypeUnexpectedError"
// @Failure 400 {object} Multiaccounting_GetAccountBalance_Failure "gin.ErrorTypeBind"
// @Failure 500 {object} Multiaccounting_GetAccountBalance_Failure "Undefined error"
// @Produce json
// @Router /processing.multiaccounting.multiaccounting.Multiaccounting/GetAccountBalance [put]
// @tags Multiaccounting
func (s *ginMultiaccountingServer) GetAccountBalance(c *gin.Context) error {
	ctx, span := dog.CreateSpan(c.Request.Context(), "GinMultiaccountingServer_GetAccountBalance")
	defer span.End()

	var request GetAccountBalanceRequest
	if err := c.ShouldBindJSON(&request); err != nil {
		return err
	}

	ctx = metadata.NewOutgoingContext(ctx, metadata.MD(c.Request.Header))

	response, err := s.client.GetAccountBalance(ctx, &request)
	if err != nil {
		return err
	}

	c.JSON(http.StatusOK, &Multiaccounting_GetAccountBalance_Success{
		Status:     true,
		Message:    "Success",
		StatusCode: 0,
		Result:     response,
	})
	return nil
}

type Multiaccounting_GetAccountStatement_Success struct {
	// Status is the status of the response. Set true
	Status bool `json:"status"`
	// Message is a human-readable explanation of the status
	Message string `json:"message"`
	// StatusCode is the GoErr status. Set 0
	StatusCode int `json:"status_code"`
	// Result is the result of the request
	Result *GetAccountStatementResponse `json:"result"`
}

type Multiaccounting_GetAccountStatement_Failure struct {
	// Status is the status of the response. Set false
	Status bool `json:"status"`
	// Message is a human-readable explanation of the status
	Message string `json:"message"`
	// StatusCode is the GoErr status
	StatusCode int `json:"status_code"`
}

// GetAccountStatement
// @Summary GetAccountStatement
// @Security bearerAuth
// @ID Multiaccounting_GetAccountStatement
// @Accept json
// @Param request body GetAccountStatementRequest true "GetAccountStatementRequest"
// @Success 200 {object} Multiaccounting_GetAccountStatement_Success
// @Failure 401 {object} Multiaccounting_GetAccountStatement_Failure "goerrpkg.ErrorTypeAuthorization"
// @Failure 404 {object} Multiaccounting_GetAccountStatement_Failure "goerrpkg.ErrorTypeNotFound"
// @Failure 400 {object} Multiaccounting_GetAccountStatement_Failure "goerrpkg.ErrorTypeBadRequest"
// @Failure 500 {object} Multiaccounting_GetAccountStatement_Failure "goerrpkg.ErrorTypeUnexpectedError"
// @Failure 400 {object} Multiaccounting_GetAccountStatement_Failure "gin.ErrorTypeBind"
// @Failure 500 {object} Multiaccounting_GetAccountStatement_Failure "Undefined error"
// @Produce json
// @Router /processing.multiaccounting.multiaccounting.Multiaccounting/GetAccountStatement [put]
// @tags Multiaccounting
func (s *ginMultiaccountingServer) GetAccountStatement(c *gin.Context) error {
	ctx, span := dog.CreateSpan(c.Request.Context(), "GinMultiaccountingServer_GetAccountStatement")
	defer span.End()

	var request GetAccountStatementRequest
	if err := c.ShouldBindJSON(&request); err != nil {
		return err
	}

	ctx = metadata.NewOutgoingContext(ctx, metadata.MD(c.Request.Header))

	response, err := s.client.GetAccountStatement(ctx, &request)
	if err != nil {
		return err
	}

	c.JSON(http.StatusOK, &Multiaccounting_GetAccountStatement_Success{
		Status:     true,
		Message:    "Success",
		StatusCode: 0,
		Result:     response,
	})
	return nil
}

type Multiaccounting_GetAccountIdentifier_Success struct {
	// Status is the status of the response. Set true
	Status bool `json:"status"`
	// Message is a human-readable explanation of the status
	Message string `json:"message"`
	// StatusCode is the GoErr status. Set 0
	StatusCode int `json:"status_code"`
	// Result is the result of the request
	Result *GetAccountIdentifierResponse `json:"result"`
}

type Multiaccounting_GetAccountIdentifier_Failure struct {
	// Status is the status of the response. Set false
	Status bool `json:"status"`
	// Message is a human-readable explanation of the status
	Message string `json:"message"`
	// StatusCode is the GoErr status
	StatusCode int `json:"status_code"`
}

// GetAccountIdentifier
// @Summary GetAccountIdentifier
// @Security bearerAuth
// @ID Multiaccounting_GetAccountIdentifier
// @Accept json
// @Param request body emptypb.Empty true "Empty"
// @Success 200 {object} Multiaccounting_GetAccountIdentifier_Success
// @Failure 401 {object} Multiaccounting_GetAccountIdentifier_Failure "goerrpkg.ErrorTypeAuthorization"
// @Failure 404 {object} Multiaccounting_GetAccountIdentifier_Failure "goerrpkg.ErrorTypeNotFound"
// @Failure 400 {object} Multiaccounting_GetAccountIdentifier_Failure "goerrpkg.ErrorTypeBadRequest"
// @Failure 500 {object} Multiaccounting_GetAccountIdentifier_Failure "goerrpkg.ErrorTypeUnexpectedError"
// @Failure 400 {object} Multiaccounting_GetAccountIdentifier_Failure "gin.ErrorTypeBind"
// @Failure 500 {object} Multiaccounting_GetAccountIdentifier_Failure "Undefined error"
// @Produce json
// @Router /processing.multiaccounting.multiaccounting.Multiaccounting/GetAccountIdentifier [put]
// @tags Multiaccounting
func (s *ginMultiaccountingServer) GetAccountIdentifier(c *gin.Context) error {
	ctx, span := dog.CreateSpan(c.Request.Context(), "GinMultiaccountingServer_GetAccountIdentifier")
	defer span.End()

	var request emptypb.Empty
	if err := c.ShouldBindJSON(&request); err != nil {
		return err
	}

	ctx = metadata.NewOutgoingContext(ctx, metadata.MD(c.Request.Header))

	response, err := s.client.GetAccountIdentifier(ctx, &request)
	if err != nil {
		return err
	}

	c.JSON(http.StatusOK, &Multiaccounting_GetAccountIdentifier_Success{
		Status:     true,
		Message:    "Success",
		StatusCode: 0,
		Result:     response,
	})
	return nil
}
