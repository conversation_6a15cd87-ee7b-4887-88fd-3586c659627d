// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.5
// 	protoc        v5.29.3
// source: inner/processing/grpc/view_crafter.proto

package grpc

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	structpb "google.golang.org/protobuf/types/known/structpb"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type GetProjectFormInfoReqV1 struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	MerchantId    *uint64                `protobuf:"varint,1,opt,name=merchant_id,json=merchantId" json:"merchant_id,omitempty"`
	ProjectId     *uint64                `protobuf:"varint,2,opt,name=project_id,json=projectId" json:"project_id,omitempty"`
	Lang          *string                `protobuf:"bytes,3,opt,name=lang" json:"lang,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetProjectFormInfoReqV1) Reset() {
	*x = GetProjectFormInfoReqV1{}
	mi := &file_inner_processing_grpc_view_crafter_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetProjectFormInfoReqV1) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetProjectFormInfoReqV1) ProtoMessage() {}

func (x *GetProjectFormInfoReqV1) ProtoReflect() protoreflect.Message {
	mi := &file_inner_processing_grpc_view_crafter_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetProjectFormInfoReqV1.ProtoReflect.Descriptor instead.
func (*GetProjectFormInfoReqV1) Descriptor() ([]byte, []int) {
	return file_inner_processing_grpc_view_crafter_proto_rawDescGZIP(), []int{0}
}

func (x *GetProjectFormInfoReqV1) GetMerchantId() uint64 {
	if x != nil && x.MerchantId != nil {
		return *x.MerchantId
	}
	return 0
}

func (x *GetProjectFormInfoReqV1) GetProjectId() uint64 {
	if x != nil && x.ProjectId != nil {
		return *x.ProjectId
	}
	return 0
}

func (x *GetProjectFormInfoReqV1) GetLang() string {
	if x != nil && x.Lang != nil {
		return *x.Lang
	}
	return ""
}

type GetProjectFormInfoResV1 struct {
	state                 protoimpl.MessageState `protogen:"open.v1"`
	MerchantId            *uint64                `protobuf:"varint,1,opt,name=merchant_id,json=merchantId" json:"merchant_id,omitempty"`
	ProjectId             *uint64                `protobuf:"varint,2,opt,name=project_id,json=projectId" json:"project_id,omitempty"`
	StoreName             *string                `protobuf:"bytes,3,opt,name=store_name,json=storeName" json:"store_name,omitempty"`
	LogoFilePath          *string                `protobuf:"bytes,4,opt,name=logo_file_path,json=logoFilePath" json:"logo_file_path,omitempty"`
	HasEmail              *bool                  `protobuf:"varint,5,opt,name=has_email,json=hasEmail" json:"has_email,omitempty"`
	RequiredEmail         *bool                  `protobuf:"varint,6,opt,name=required_email,json=requiredEmail" json:"required_email,omitempty"`
	HasPhone              *bool                  `protobuf:"varint,7,opt,name=has_phone,json=hasPhone" json:"has_phone,omitempty"`
	RequiredPhone         *bool                  `protobuf:"varint,8,opt,name=required_phone,json=requiredPhone" json:"required_phone,omitempty"`
	DefaultLanguage       *string                `protobuf:"bytes,9,opt,name=default_language,json=defaultLanguage" json:"default_language,omitempty"`
	Timeout               *uint64                `protobuf:"varint,10,opt,name=timeout" json:"timeout,omitempty"`
	HasRedirect           *bool                  `protobuf:"varint,11,opt,name=has_redirect,json=hasRedirect" json:"has_redirect,omitempty"`
	HasDefaultCard        *bool                  `protobuf:"varint,12,opt,name=has_default_card,json=hasDefaultCard" json:"has_default_card,omitempty"`
	BannerDesktopFilePath *string                `protobuf:"bytes,13,opt,name=banner_desktop_file_path,json=bannerDesktopFilePath" json:"banner_desktop_file_path,omitempty"`
	BannerMobileFilePath  *string                `protobuf:"bytes,14,opt,name=banner_mobile_file_path,json=bannerMobileFilePath" json:"banner_mobile_file_path,omitempty"`
	BannerUrl             *string                `protobuf:"bytes,15,opt,name=banner_url,json=bannerUrl" json:"banner_url,omitempty"`
	ShowDescription       *bool                  `protobuf:"varint,16,opt,name=show_description,json=showDescription" json:"show_description,omitempty"`
	DescriptionLanguage   *structpb.Struct       `protobuf:"bytes,17,opt,name=description_language,json=descriptionLanguage" json:"description_language,omitempty"`
	unknownFields         protoimpl.UnknownFields
	sizeCache             protoimpl.SizeCache
}

func (x *GetProjectFormInfoResV1) Reset() {
	*x = GetProjectFormInfoResV1{}
	mi := &file_inner_processing_grpc_view_crafter_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetProjectFormInfoResV1) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetProjectFormInfoResV1) ProtoMessage() {}

func (x *GetProjectFormInfoResV1) ProtoReflect() protoreflect.Message {
	mi := &file_inner_processing_grpc_view_crafter_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetProjectFormInfoResV1.ProtoReflect.Descriptor instead.
func (*GetProjectFormInfoResV1) Descriptor() ([]byte, []int) {
	return file_inner_processing_grpc_view_crafter_proto_rawDescGZIP(), []int{1}
}

func (x *GetProjectFormInfoResV1) GetMerchantId() uint64 {
	if x != nil && x.MerchantId != nil {
		return *x.MerchantId
	}
	return 0
}

func (x *GetProjectFormInfoResV1) GetProjectId() uint64 {
	if x != nil && x.ProjectId != nil {
		return *x.ProjectId
	}
	return 0
}

func (x *GetProjectFormInfoResV1) GetStoreName() string {
	if x != nil && x.StoreName != nil {
		return *x.StoreName
	}
	return ""
}

func (x *GetProjectFormInfoResV1) GetLogoFilePath() string {
	if x != nil && x.LogoFilePath != nil {
		return *x.LogoFilePath
	}
	return ""
}

func (x *GetProjectFormInfoResV1) GetHasEmail() bool {
	if x != nil && x.HasEmail != nil {
		return *x.HasEmail
	}
	return false
}

func (x *GetProjectFormInfoResV1) GetRequiredEmail() bool {
	if x != nil && x.RequiredEmail != nil {
		return *x.RequiredEmail
	}
	return false
}

func (x *GetProjectFormInfoResV1) GetHasPhone() bool {
	if x != nil && x.HasPhone != nil {
		return *x.HasPhone
	}
	return false
}

func (x *GetProjectFormInfoResV1) GetRequiredPhone() bool {
	if x != nil && x.RequiredPhone != nil {
		return *x.RequiredPhone
	}
	return false
}

func (x *GetProjectFormInfoResV1) GetDefaultLanguage() string {
	if x != nil && x.DefaultLanguage != nil {
		return *x.DefaultLanguage
	}
	return ""
}

func (x *GetProjectFormInfoResV1) GetTimeout() uint64 {
	if x != nil && x.Timeout != nil {
		return *x.Timeout
	}
	return 0
}

func (x *GetProjectFormInfoResV1) GetHasRedirect() bool {
	if x != nil && x.HasRedirect != nil {
		return *x.HasRedirect
	}
	return false
}

func (x *GetProjectFormInfoResV1) GetHasDefaultCard() bool {
	if x != nil && x.HasDefaultCard != nil {
		return *x.HasDefaultCard
	}
	return false
}

func (x *GetProjectFormInfoResV1) GetBannerDesktopFilePath() string {
	if x != nil && x.BannerDesktopFilePath != nil {
		return *x.BannerDesktopFilePath
	}
	return ""
}

func (x *GetProjectFormInfoResV1) GetBannerMobileFilePath() string {
	if x != nil && x.BannerMobileFilePath != nil {
		return *x.BannerMobileFilePath
	}
	return ""
}

func (x *GetProjectFormInfoResV1) GetBannerUrl() string {
	if x != nil && x.BannerUrl != nil {
		return *x.BannerUrl
	}
	return ""
}

func (x *GetProjectFormInfoResV1) GetShowDescription() bool {
	if x != nil && x.ShowDescription != nil {
		return *x.ShowDescription
	}
	return false
}

func (x *GetProjectFormInfoResV1) GetDescriptionLanguage() *structpb.Struct {
	if x != nil {
		return x.DescriptionLanguage
	}
	return nil
}

var File_inner_processing_grpc_view_crafter_proto protoreflect.FileDescriptor

var file_inner_processing_grpc_view_crafter_proto_rawDesc = string([]byte{
	0x0a, 0x28, 0x69, 0x6e, 0x6e, 0x65, 0x72, 0x2f, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69,
	0x6e, 0x67, 0x2f, 0x67, 0x72, 0x70, 0x63, 0x2f, 0x76, 0x69, 0x65, 0x77, 0x5f, 0x63, 0x72, 0x61,
	0x66, 0x74, 0x65, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x24, 0x70, 0x72, 0x6f, 0x63,
	0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x69, 0x65, 0x77, 0x5f, 0x63, 0x72, 0x61, 0x66,
	0x74, 0x65, 0x72, 0x2e, 0x76, 0x69, 0x65, 0x77, 0x5f, 0x63, 0x72, 0x61, 0x66, 0x74, 0x65, 0x72,
	0x1a, 0x1c, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75,
	0x66, 0x2f, 0x73, 0x74, 0x72, 0x75, 0x63, 0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x6d,
	0x0a, 0x17, 0x47, 0x65, 0x74, 0x50, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x46, 0x6f, 0x72, 0x6d,
	0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65, 0x71, 0x56, 0x31, 0x12, 0x1f, 0x0a, 0x0b, 0x6d, 0x65, 0x72,
	0x63, 0x68, 0x61, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x0a,
	0x6d, 0x65, 0x72, 0x63, 0x68, 0x61, 0x6e, 0x74, 0x49, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x70, 0x72,
	0x6f, 0x6a, 0x65, 0x63, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x04, 0x52, 0x09,
	0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x49, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x6c, 0x61, 0x6e,
	0x67, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6c, 0x61, 0x6e, 0x67, 0x22, 0xbe, 0x05,
	0x0a, 0x17, 0x47, 0x65, 0x74, 0x50, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x46, 0x6f, 0x72, 0x6d,
	0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65, 0x73, 0x56, 0x31, 0x12, 0x1f, 0x0a, 0x0b, 0x6d, 0x65, 0x72,
	0x63, 0x68, 0x61, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x0a,
	0x6d, 0x65, 0x72, 0x63, 0x68, 0x61, 0x6e, 0x74, 0x49, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x70, 0x72,
	0x6f, 0x6a, 0x65, 0x63, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x04, 0x52, 0x09,
	0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x49, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x73, 0x74, 0x6f,
	0x72, 0x65, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x73,
	0x74, 0x6f, 0x72, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x24, 0x0a, 0x0e, 0x6c, 0x6f, 0x67, 0x6f,
	0x5f, 0x66, 0x69, 0x6c, 0x65, 0x5f, 0x70, 0x61, 0x74, 0x68, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0c, 0x6c, 0x6f, 0x67, 0x6f, 0x46, 0x69, 0x6c, 0x65, 0x50, 0x61, 0x74, 0x68, 0x12, 0x1b,
	0x0a, 0x09, 0x68, 0x61, 0x73, 0x5f, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x18, 0x05, 0x20, 0x01, 0x28,
	0x08, 0x52, 0x08, 0x68, 0x61, 0x73, 0x45, 0x6d, 0x61, 0x69, 0x6c, 0x12, 0x25, 0x0a, 0x0e, 0x72,
	0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x5f, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x18, 0x06, 0x20,
	0x01, 0x28, 0x08, 0x52, 0x0d, 0x72, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x45, 0x6d, 0x61,
	0x69, 0x6c, 0x12, 0x1b, 0x0a, 0x09, 0x68, 0x61, 0x73, 0x5f, 0x70, 0x68, 0x6f, 0x6e, 0x65, 0x18,
	0x07, 0x20, 0x01, 0x28, 0x08, 0x52, 0x08, 0x68, 0x61, 0x73, 0x50, 0x68, 0x6f, 0x6e, 0x65, 0x12,
	0x25, 0x0a, 0x0e, 0x72, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x5f, 0x70, 0x68, 0x6f, 0x6e,
	0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0d, 0x72, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65,
	0x64, 0x50, 0x68, 0x6f, 0x6e, 0x65, 0x12, 0x29, 0x0a, 0x10, 0x64, 0x65, 0x66, 0x61, 0x75, 0x6c,
	0x74, 0x5f, 0x6c, 0x61, 0x6e, 0x67, 0x75, 0x61, 0x67, 0x65, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0f, 0x64, 0x65, 0x66, 0x61, 0x75, 0x6c, 0x74, 0x4c, 0x61, 0x6e, 0x67, 0x75, 0x61, 0x67,
	0x65, 0x12, 0x18, 0x0a, 0x07, 0x74, 0x69, 0x6d, 0x65, 0x6f, 0x75, 0x74, 0x18, 0x0a, 0x20, 0x01,
	0x28, 0x04, 0x52, 0x07, 0x74, 0x69, 0x6d, 0x65, 0x6f, 0x75, 0x74, 0x12, 0x21, 0x0a, 0x0c, 0x68,
	0x61, 0x73, 0x5f, 0x72, 0x65, 0x64, 0x69, 0x72, 0x65, 0x63, 0x74, 0x18, 0x0b, 0x20, 0x01, 0x28,
	0x08, 0x52, 0x0b, 0x68, 0x61, 0x73, 0x52, 0x65, 0x64, 0x69, 0x72, 0x65, 0x63, 0x74, 0x12, 0x28,
	0x0a, 0x10, 0x68, 0x61, 0x73, 0x5f, 0x64, 0x65, 0x66, 0x61, 0x75, 0x6c, 0x74, 0x5f, 0x63, 0x61,
	0x72, 0x64, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0e, 0x68, 0x61, 0x73, 0x44, 0x65, 0x66,
	0x61, 0x75, 0x6c, 0x74, 0x43, 0x61, 0x72, 0x64, 0x12, 0x37, 0x0a, 0x18, 0x62, 0x61, 0x6e, 0x6e,
	0x65, 0x72, 0x5f, 0x64, 0x65, 0x73, 0x6b, 0x74, 0x6f, 0x70, 0x5f, 0x66, 0x69, 0x6c, 0x65, 0x5f,
	0x70, 0x61, 0x74, 0x68, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x09, 0x52, 0x15, 0x62, 0x61, 0x6e, 0x6e,
	0x65, 0x72, 0x44, 0x65, 0x73, 0x6b, 0x74, 0x6f, 0x70, 0x46, 0x69, 0x6c, 0x65, 0x50, 0x61, 0x74,
	0x68, 0x12, 0x35, 0x0a, 0x17, 0x62, 0x61, 0x6e, 0x6e, 0x65, 0x72, 0x5f, 0x6d, 0x6f, 0x62, 0x69,
	0x6c, 0x65, 0x5f, 0x66, 0x69, 0x6c, 0x65, 0x5f, 0x70, 0x61, 0x74, 0x68, 0x18, 0x0e, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x14, 0x62, 0x61, 0x6e, 0x6e, 0x65, 0x72, 0x4d, 0x6f, 0x62, 0x69, 0x6c, 0x65,
	0x46, 0x69, 0x6c, 0x65, 0x50, 0x61, 0x74, 0x68, 0x12, 0x1d, 0x0a, 0x0a, 0x62, 0x61, 0x6e, 0x6e,
	0x65, 0x72, 0x5f, 0x75, 0x72, 0x6c, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x62, 0x61,
	0x6e, 0x6e, 0x65, 0x72, 0x55, 0x72, 0x6c, 0x12, 0x29, 0x0a, 0x10, 0x73, 0x68, 0x6f, 0x77, 0x5f,
	0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x10, 0x20, 0x01, 0x28,
	0x08, 0x52, 0x0f, 0x73, 0x68, 0x6f, 0x77, 0x44, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69,
	0x6f, 0x6e, 0x12, 0x4a, 0x0a, 0x14, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f,
	0x6e, 0x5f, 0x6c, 0x61, 0x6e, 0x67, 0x75, 0x61, 0x67, 0x65, 0x18, 0x11, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x17, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62,
	0x75, 0x66, 0x2e, 0x53, 0x74, 0x72, 0x75, 0x63, 0x74, 0x52, 0x13, 0x64, 0x65, 0x73, 0x63, 0x72,
	0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x4c, 0x61, 0x6e, 0x67, 0x75, 0x61, 0x67, 0x65, 0x32, 0xa6,
	0x01, 0x0a, 0x0b, 0x56, 0x69, 0x65, 0x77, 0x43, 0x72, 0x61, 0x66, 0x74, 0x65, 0x72, 0x12, 0x96,
	0x01, 0x0a, 0x14, 0x47, 0x65, 0x74, 0x50, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x46, 0x6f, 0x72,
	0x6d, 0x49, 0x6e, 0x66, 0x6f, 0x56, 0x31, 0x12, 0x3d, 0x2e, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73,
	0x73, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x69, 0x65, 0x77, 0x5f, 0x63, 0x72, 0x61, 0x66, 0x74, 0x65,
	0x72, 0x2e, 0x76, 0x69, 0x65, 0x77, 0x5f, 0x63, 0x72, 0x61, 0x66, 0x74, 0x65, 0x72, 0x2e, 0x47,
	0x65, 0x74, 0x50, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x46, 0x6f, 0x72, 0x6d, 0x49, 0x6e, 0x66,
	0x6f, 0x52, 0x65, 0x71, 0x56, 0x31, 0x1a, 0x3d, 0x2e, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73,
	0x69, 0x6e, 0x67, 0x2e, 0x76, 0x69, 0x65, 0x77, 0x5f, 0x63, 0x72, 0x61, 0x66, 0x74, 0x65, 0x72,
	0x2e, 0x76, 0x69, 0x65, 0x77, 0x5f, 0x63, 0x72, 0x61, 0x66, 0x74, 0x65, 0x72, 0x2e, 0x47, 0x65,
	0x74, 0x50, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x46, 0x6f, 0x72, 0x6d, 0x49, 0x6e, 0x66, 0x6f,
	0x52, 0x65, 0x73, 0x56, 0x31, 0x22, 0x00, 0x42, 0x2d, 0x5a, 0x2b, 0x67, 0x69, 0x74, 0x2e, 0x6c,
	0x6f, 0x63, 0x61, 0x6c, 0x2f, 0x73, 0x65, 0x6e, 0x73, 0x69, 0x74, 0x69, 0x76, 0x65, 0x2f, 0x69,
	0x6e, 0x6e, 0x65, 0x72, 0x70, 0x62, 0x2f, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e,
	0x67, 0x2f, 0x67, 0x72, 0x70, 0x63, 0x62, 0x08, 0x65, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73,
	0x70, 0xe8, 0x07,
})

var (
	file_inner_processing_grpc_view_crafter_proto_rawDescOnce sync.Once
	file_inner_processing_grpc_view_crafter_proto_rawDescData []byte
)

func file_inner_processing_grpc_view_crafter_proto_rawDescGZIP() []byte {
	file_inner_processing_grpc_view_crafter_proto_rawDescOnce.Do(func() {
		file_inner_processing_grpc_view_crafter_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_inner_processing_grpc_view_crafter_proto_rawDesc), len(file_inner_processing_grpc_view_crafter_proto_rawDesc)))
	})
	return file_inner_processing_grpc_view_crafter_proto_rawDescData
}

var file_inner_processing_grpc_view_crafter_proto_msgTypes = make([]protoimpl.MessageInfo, 2)
var file_inner_processing_grpc_view_crafter_proto_goTypes = []any{
	(*GetProjectFormInfoReqV1)(nil), // 0: processing.view_crafter.view_crafter.GetProjectFormInfoReqV1
	(*GetProjectFormInfoResV1)(nil), // 1: processing.view_crafter.view_crafter.GetProjectFormInfoResV1
	(*structpb.Struct)(nil),         // 2: google.protobuf.Struct
}
var file_inner_processing_grpc_view_crafter_proto_depIdxs = []int32{
	2, // 0: processing.view_crafter.view_crafter.GetProjectFormInfoResV1.description_language:type_name -> google.protobuf.Struct
	0, // 1: processing.view_crafter.view_crafter.ViewCrafter.GetProjectFormInfoV1:input_type -> processing.view_crafter.view_crafter.GetProjectFormInfoReqV1
	1, // 2: processing.view_crafter.view_crafter.ViewCrafter.GetProjectFormInfoV1:output_type -> processing.view_crafter.view_crafter.GetProjectFormInfoResV1
	2, // [2:3] is the sub-list for method output_type
	1, // [1:2] is the sub-list for method input_type
	1, // [1:1] is the sub-list for extension type_name
	1, // [1:1] is the sub-list for extension extendee
	0, // [0:1] is the sub-list for field type_name
}

func init() { file_inner_processing_grpc_view_crafter_proto_init() }
func file_inner_processing_grpc_view_crafter_proto_init() {
	if File_inner_processing_grpc_view_crafter_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_inner_processing_grpc_view_crafter_proto_rawDesc), len(file_inner_processing_grpc_view_crafter_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   2,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_inner_processing_grpc_view_crafter_proto_goTypes,
		DependencyIndexes: file_inner_processing_grpc_view_crafter_proto_depIdxs,
		MessageInfos:      file_inner_processing_grpc_view_crafter_proto_msgTypes,
	}.Build()
	File_inner_processing_grpc_view_crafter_proto = out.File
	file_inner_processing_grpc_view_crafter_proto_goTypes = nil
	file_inner_processing_grpc_view_crafter_proto_depIdxs = nil
}
