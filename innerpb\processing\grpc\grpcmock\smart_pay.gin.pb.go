// Code generated by MockGen. DO NOT EDIT.
// Source: smart_pay.gin.pb.go

// Package grpcmock is a generated GoMock package.
package grpcmock

import (
	reflect "reflect"

	gin "github.com/gin-gonic/gin"
	gomock "github.com/golang/mock/gomock"
)

// MockGinSmartPayServer is a mock of GinSmartPayServer interface.
type MockGinSmartPayServer struct {
	ctrl     *gomock.Controller
	recorder *MockGinSmartPayServerMockRecorder
}

// MockGinSmartPayServerMockRecorder is the mock recorder for MockGinSmartPayServer.
type MockGinSmartPayServerMockRecorder struct {
	mock *MockGinSmartPayServer
}

// NewMockGinSmartPayServer creates a new mock instance.
func NewMockGinSmartPayServer(ctrl *gomock.Controller) *MockGinSmartPayServer {
	mock := &MockGinSmartPayServer{ctrl: ctrl}
	mock.recorder = &MockGinSmartPayServerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockGinSmartPayServer) EXPECT() *MockGinSmartPayServerMockRecorder {
	return m.recorder
}

// ApplePaySession mocks base method.
func (m *MockGinSmartPayServer) ApplePaySession(c *gin.Context) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ApplePaySession", c)
	ret0, _ := ret[0].(error)
	return ret0
}

// ApplePaySession indicates an expected call of ApplePaySession.
func (mr *MockGinSmartPayServerMockRecorder) ApplePaySession(c interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ApplePaySession", reflect.TypeOf((*MockGinSmartPayServer)(nil).ApplePaySession), c)
}

// DecodeToken mocks base method.
func (m *MockGinSmartPayServer) DecodeToken(c *gin.Context) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DecodeToken", c)
	ret0, _ := ret[0].(error)
	return ret0
}

// DecodeToken indicates an expected call of DecodeToken.
func (mr *MockGinSmartPayServerMockRecorder) DecodeToken(c interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DecodeToken", reflect.TypeOf((*MockGinSmartPayServer)(nil).DecodeToken), c)
}

// DecryptGPayToken mocks base method.
func (m *MockGinSmartPayServer) DecryptGPayToken(c *gin.Context) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DecryptGPayToken", c)
	ret0, _ := ret[0].(error)
	return ret0
}

// DecryptGPayToken indicates an expected call of DecryptGPayToken.
func (mr *MockGinSmartPayServerMockRecorder) DecryptGPayToken(c interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DecryptGPayToken", reflect.TypeOf((*MockGinSmartPayServer)(nil).DecryptGPayToken), c)
}

// GetGPayCredentials mocks base method.
func (m *MockGinSmartPayServer) GetGPayCredentials(c *gin.Context) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetGPayCredentials", c)
	ret0, _ := ret[0].(error)
	return ret0
}

// GetGPayCredentials indicates an expected call of GetGPayCredentials.
func (mr *MockGinSmartPayServerMockRecorder) GetGPayCredentials(c interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetGPayCredentials", reflect.TypeOf((*MockGinSmartPayServer)(nil).GetGPayCredentials), c)
}
