package main

import (
	"sort"

	"google.golang.org/protobuf/compiler/protogen"
	"google.golang.org/protobuf/proto"
	"google.golang.org/protobuf/reflect/protoreflect"

	"git.local/sensitive/mvp/pb"
)

func init() {
	RegisterGenerator(&LoggerGenerator{
		Linear:          map[protoreflect.FullName]*Uniq{},
		Mapper:          map[protoreflect.FullName]map[protoreflect.FullName]*Uniq{},
		FuncNameStorage: map[string]struct{}{},
	})
}

func LoggerLevelsToMap(levels []pb.LoggerLevel) map[pb.LoggerLevel]struct{} {
	m := map[pb.LoggerLevel]struct{}{}
	for _, level := range levels {
		m[level] = struct{}{}
	}

	if len(m) == 0 {
		m[pb.LoggerLevel_Full] = struct{}{}
	}

	return m
}

type LoggerGenerator struct {
	Linear          map[protoreflect.FullName]*Uniq
	Mapper          map[protoreflect.FullName]map[protoreflect.FullName]*Uniq
	FuncNameStorage map[string]struct{}
}

func (generator *LoggerGenerator) Print(g *protogen.GeneratedFile) {
	{
		var keys []protoreflect.FullName
		for k := range generator.Mapper {
			keys = append(keys, k)
		}
		sort.Slice(keys, func(i, j int) bool {
			return keys[i] < keys[j]
		})

		for _, key := range keys {
			uniq := generator.Mapper[key]

			var childrenKeys []protoreflect.FullName
			for k := range uniq {
				childrenKeys = append(childrenKeys, k)
			}
			sort.Slice(childrenKeys, func(i, j int) bool {
				return childrenKeys[i] < childrenKeys[j]
			})

			for _, childKey := range childrenKeys {
				u := uniq[childKey]
				for _, datum := range u.Data {
					g.P(datum...)
				}
				g.P()
			}
		}
	}

	{
		var keys []protoreflect.FullName
		for k := range generator.Linear {
			keys = append(keys, k)
		}
		sort.Slice(keys, func(i, j int) bool {
			return keys[i] < keys[j]
		})

		for k := range keys {
			uniq := generator.Linear[keys[k]]

			for _, datum := range uniq.Data {
				g.P(datum...)
			}
			g.P()

			if uniq.NeedSlice {
				for _, datum := range uniq.SliceData {
					g.P(datum...)
				}
				g.P()
			}
		}
	}
}

func (generator *LoggerGenerator) ProtoGenGenerate(gen *protogen.Plugin) {
	for _, file := range gen.Files {
		if !file.Generate {
			continue
		}

		if len(file.Services) == 0 {
			return
		}

		for _, enum := range file.Enums {
			_ = EnumInfo(file, enum, generator.Linear, generator.FuncNameStorage)
		}

		for _, message := range file.Messages {
			_ = MessageInfo(file, message, generator.Linear, generator.Mapper, generator.FuncNameStorage)
		}

		for _, service := range file.Services {
			for _, method := range service.Methods {
				_ = MessageInfo(file, method.Input, generator.Linear, generator.Mapper, generator.FuncNameStorage)
				_ = MessageInfo(file, method.Output, generator.Linear, generator.Mapper, generator.FuncNameStorage)
			}
		}

		g := gen.NewGeneratedFile(
			file.GeneratedFilenamePrefix+V2_LOGGER_SUFFIX,
			file.GoImportPath,
		)
		HeaderPrint(g, string(file.GoPackageName))

		generator.Print(g)

		for _, service := range file.Services {
			serviceExtension := proto.GetExtension(service.Desc.Options(), pb.E_ServiceLoggerLevel).([]pb.LoggerLevel)
			serviceExtensionMap := LoggerLevelsToMap(serviceExtension)
			_, okServiceSkipAll := serviceExtensionMap[pb.LoggerLevel_SkipAll]
			_, okServiceHidden := serviceExtensionMap[pb.LoggerLevel_Hidden]

			g.P("var _ ", service.GoName, "Server = (*logged", service.GoName, "Server)(nil)")
			g.P()
			g.P("func NewLogged", service.GoName, "Server(srv ", service.GoName, "Server) ", service.GoName, "Server {")
			g.P("return &logged", service.GoName, "Server{srv: srv}")
			g.P("}")
			g.P()
			g.P("type logged", service.GoName, "Server struct {")
			g.P("Unimplemented", service.GoName, "Server")
			g.P()
			g.P("srv ", service.GoName, "Server")
			g.P("}")
			g.P()

			for _, method := range service.Methods {
				methodExtension := proto.GetExtension(method.Desc.Options(), pb.E_MethodLoggerLevel).([]pb.LoggerLevel)
				methodExtensionMap := LoggerLevelsToMap(methodExtension)

				if _, ok := methodExtensionMap[pb.LoggerLevel_SkipAll]; okServiceSkipAll || ok {
					g.P("func (s *logged", service.GoName, "Server) ", method.GoName, "(")
					g.P("ctx ", contextContextIdent, ", ")
					g.P("request *", method.Input.GoIdent, ",")
					g.P(") (")
					g.P("response *", method.Output.GoIdent, ",")
					g.P("err error,")
					g.P(") {")
					g.P("return s.srv.", method.GoName, "(ctx, request)")
					g.P("}")
					g.P()
					continue
				}

				if _, ok := methodExtensionMap[pb.LoggerLevel_Hidden]; ok || okServiceHidden {
					g.P("func (s *logged", service.GoName, "Server) ", method.GoName, "(")
					g.P("ctx ", contextContextIdent, ", ")
					g.P("request *", method.Input.GoIdent, ",")
					g.P(") (")
					g.P("response *", method.Output.GoIdent, ",")
					g.P("err error,")
					g.P(") {")
					g.P("label := ", cntxBegin, "(ctx, \"", service.GoName, "Server_", method.GoName, "\")")
					g.P("defer ", cntxEnd, "(ctx, label)")
					g.P()
					g.P("defer ", ctxZapAddFields, "(ctx, ", zapAny, "(label+\"response\",  \"[***hidden***]\"))")
					g.P()
					g.P(ctxZapAddFields, "(ctx, ", zapAny, "(label+\"request\",  \"[***hidden***]\"))")
					g.P()
					g.P("return s.srv.", method.GoName, "(ctx, request)")
					g.P("}")
					g.P()
					continue
				}

				inputInfo := MessageInfo(file, method.Input, generator.Linear, generator.Mapper, generator.FuncNameStorage)
				outputInfo := MessageInfo(file, method.Output, generator.Linear, generator.Mapper, generator.FuncNameStorage)

				g.P("func (s *logged", service.GoName, "Server) ", method.GoName, "(")
				g.P("ctx ", contextContextIdent, ", ")
				g.P("request *", method.Input.GoIdent, ",")
				g.P(") (")
				g.P("response *", method.Output.GoIdent, ",")
				g.P("err error,")
				g.P(") {")
				g.P("label := ", cntxBegin, "(ctx, \"", service.GoName, "Server_", method.GoName, "\")")
				g.P("defer ", cntxEnd, "(ctx, label)")
				g.P()
				g.P("defer func() {")
				g.P("if err != nil {")
				g.P(ctxZapAddFields, "(ctx, ", zapAny, "(label+\"error\", err))")
				g.P("return")
				g.P("}")
				g.P()
				g.P(ctxZapAddFields, "(")
				g.P("ctx, ")
				g.P(outputInfo.FuncName, "(label+\"response\", response),")
				g.P(")")
				g.P("}()")
				g.P()
				g.P(ctxZapAddFields, "(")
				g.P("ctx, ")
				g.P(inputInfo.FuncName, "(label+\"request\", request),")
				g.P(")")
				g.P()
				g.P("response, err = s.srv.", method.GoName, "(ctx, request)")
				g.P()
				g.P("return")
				g.P("}")
				g.P()
			}

			g.P("var _ ", service.GoName, "Client = (*logged", service.GoName, "Client)(nil)")
			g.P()
			g.P("func NewLogged", service.GoName, "Client(client ", service.GoName, "Client) ", service.GoName, "Client {")
			g.P("return &logged", service.GoName, "Client{client: client}")
			g.P("}")
			g.P()
			g.P("type logged", service.GoName, "Client struct {")
			g.P("client ", service.GoName, "Client")
			g.P("}")
			g.P()

			for _, method := range service.Methods {
				methodExtension := proto.GetExtension(method.Desc.Options(), pb.E_MethodLoggerLevel).([]pb.LoggerLevel)
				methodExtensionMap := LoggerLevelsToMap(methodExtension)

				if _, ok := methodExtensionMap[pb.LoggerLevel_SkipAll]; okServiceSkipAll || ok {
					g.P("func (s *logged", service.GoName, "Client) ", method.GoName, "(")
					g.P("ctx ", contextContextIdent, ", ")
					g.P("request *", method.Input.GoIdent, ",")
					g.P("opts ...", grpcPackageCallOption, ",")
					g.P(") (")
					g.P("response *", method.Output.GoIdent, ",")
					g.P("err error,")
					g.P(") {")
					g.P("return s.client.", method.GoName, "(ctx, request, opts...)")
					g.P("}")
					g.P()
					continue
				}

				if _, ok := methodExtensionMap[pb.LoggerLevel_Hidden]; ok || okServiceHidden {
					g.P("func (s *logged", service.GoName, "Client) ", method.GoName, "(")
					g.P("ctx ", contextContextIdent, ", ")
					g.P("request *", method.Input.GoIdent, ",")
					g.P("opts ...", grpcPackageCallOption, ",")
					g.P(") (")
					g.P("response *", method.Output.GoIdent, ",")
					g.P("err error,")
					g.P(") {")
					g.P("label := ", cntxBegin, "(ctx, \"", service.GoName, "Client_", method.GoName, "\")")
					g.P("defer ", cntxEnd, "(ctx, label)")
					g.P()
					g.P("defer ", ctxZapAddFields, "(ctx, ", zapAny, "(label+\"response\",  \"[***hidden***]\"))")
					g.P()
					g.P(ctxZapAddFields, "(ctx, ", zapAny, "(label+\"request\",  \"[***hidden***]\"))")
					g.P()
					g.P("return s.client.", method.GoName, "(ctx, request, opts...)")
					g.P("}")
					g.P()
					continue
				}

				inputInfo := MessageInfo(file, method.Input, generator.Linear, generator.Mapper, generator.FuncNameStorage)
				outputInfo := MessageInfo(file, method.Output, generator.Linear, generator.Mapper, generator.FuncNameStorage)

				g.P("func (s *logged", service.GoName, "Client) ", method.GoName, "(")
				g.P("ctx ", contextContextIdent, ", ")
				g.P("request *", method.Input.GoIdent, ",")
				g.P("opts ...", grpcPackageCallOption, ",")
				g.P(") (")
				g.P("response *", method.Output.GoIdent, ",")
				g.P("err error,")
				g.P(") {")
				g.P("label := ", cntxBegin, "(ctx, \"", service.GoName, "Client_", method.GoName, "\")")
				g.P("defer ", cntxEnd, "(ctx, label)")
				g.P()
				g.P("defer func() {")
				g.P("if err != nil {")
				g.P(ctxZapAddFields, "(ctx, ", zapAny, "(label+\"error\", err))")
				g.P("return")
				g.P("}")
				g.P()
				g.P(ctxZapAddFields, "(")
				g.P("ctx, ")
				g.P(outputInfo.FuncName, "(label+\"response\", response),")
				g.P(")")
				g.P("}()")
				g.P()
				g.P(ctxZapAddFields, "(")
				g.P("ctx, ")
				g.P(inputInfo.FuncName, "(label+\"request\", request),")
				g.P(")")
				g.P()
				g.P("response, err = s.client.", method.GoName, "(ctx, request, opts...)")
				g.P()
				g.P("return")
				g.P("}")
				g.P()
			}
		}
	}
}
