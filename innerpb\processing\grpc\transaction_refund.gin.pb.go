// Code generated by protoc-gen-go-mvp. DO NOT EDIT.
// versions:
// - protoc-gen-go-mvp v2.0.1
// created by mvp authors

package grpc

import (
	dog "git.local/sensitive/sdk/dog"
	gin "github.com/gin-gonic/gin"
	metadata "google.golang.org/grpc/metadata"
	http "net/http"
)

func RegisterGinRefundRouterGroup(
	in *gin.RouterGroup,
	handler func(func(c *gin.Context) error) gin.HandlerFunc,
) error {
	service, err := NewGinRefundService()
	if err != nil {
		return err
	}

	routerGroup := in.Group("processing.transaction.transaction_refund.Refund")
	routerGroup.PUT("/GetByTransactionIDV1", handler(service.GetByTransactionIDV1))
	return nil
}

func NewGinRefundService() (GinRefundServer, error) {
	client, err := NewPreparedRefundClient()
	if err != nil {
		return nil, err
	}

	return &ginRefundServer{
		client: NewLoggedRefundClient(
			NewIamRefundClient(client),
		),
	}, nil
}

//go:generate mockgen -destination=./grpcmock/transaction_refund.gin.pb.go -package=grpcmock -source=transaction_refund.gin.pb.go GinRefundServer
type GinRefundServer interface {
	GetByTransactionIDV1(c *gin.Context) error
}

var _ GinRefundServer = (*ginRefundServer)(nil)

type ginRefundServer struct {
	client RefundClient
}

type Refund_GetByTransactionIDV1_Success struct {
	// Status is the status of the response. Set true
	Status bool `json:"status"`
	// Message is a human-readable explanation of the status
	Message string `json:"message"`
	// StatusCode is the GoErr status. Set 0
	StatusCode int `json:"status_code"`
	// Result is the result of the request
	Result *TransactionResponseDataV1 `json:"result"`
}

type Refund_GetByTransactionIDV1_Failure struct {
	// Status is the status of the response. Set false
	Status bool `json:"status"`
	// Message is a human-readable explanation of the status
	Message string `json:"message"`
	// StatusCode is the GoErr status
	StatusCode int `json:"status_code"`
}

// GetByTransactionIDV1
// @Summary GetByTransactionIDV1
// @Security bearerAuth
// @ID Refund_GetByTransactionIDV1
// @Accept json
// @Param request body TransactionRequestDataV1 true "TransactionRequestDataV1"
// @Success 200 {object} Refund_GetByTransactionIDV1_Success
// @Failure 401 {object} Refund_GetByTransactionIDV1_Failure "goerrpkg.ErrorTypeAuthorization"
// @Failure 404 {object} Refund_GetByTransactionIDV1_Failure "goerrpkg.ErrorTypeNotFound"
// @Failure 400 {object} Refund_GetByTransactionIDV1_Failure "goerrpkg.ErrorTypeBadRequest"
// @Failure 500 {object} Refund_GetByTransactionIDV1_Failure "goerrpkg.ErrorTypeUnexpectedError"
// @Failure 400 {object} Refund_GetByTransactionIDV1_Failure "gin.ErrorTypeBind"
// @Failure 500 {object} Refund_GetByTransactionIDV1_Failure "Undefined error"
// @Produce json
// @Router /processing.transaction.transaction_refund.Refund/GetByTransactionIDV1 [put]
// @tags Refund
func (s *ginRefundServer) GetByTransactionIDV1(c *gin.Context) error {
	ctx, span := dog.CreateSpan(c.Request.Context(), "GinRefundServer_GetByTransactionIDV1")
	defer span.End()

	var request TransactionRequestDataV1
	if err := c.ShouldBindJSON(&request); err != nil {
		return err
	}

	ctx = metadata.NewOutgoingContext(ctx, metadata.MD(c.Request.Header))

	response, err := s.client.GetByTransactionIDV1(ctx, &request)
	if err != nil {
		return err
	}

	c.JSON(http.StatusOK, &Refund_GetByTransactionIDV1_Success{
		Status:     true,
		Message:    "Success",
		StatusCode: 0,
		Result:     response,
	})
	return nil
}
