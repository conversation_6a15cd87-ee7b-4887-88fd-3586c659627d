package service

import (
	"context"
	"encoding/json"
	"git.local/sensitive/innerpb/processing/goerr"
	"git.local/sensitive/pkg/middlewares"
	"git.local/sensitive/processing/acquirer/model"
	"git.local/sensitive/processing/acquirer/repository/database"
	"git.local/sensitive/processing/acquirer/schema"
	"git.local/sensitive/sdk/dog"
	"go.uber.org/zap"
)

type TerminalService struct {
	terminalBasicRepo database.TerminalBasicer
	terminalRepo      database.Terminaler
}

func NewTerminalService(
	terminalBasicRepo database.TerminalBasicer,
	terminalRepo database.Terminaler,
) *TerminalService {
	return &TerminalService{
		terminalBasicRepo: terminalBasicRepo,
		terminalRepo:      terminalRepo,
	}
}

func (s *TerminalService) Create(
	ctx context.Context,
	terminal *schema.CreateTerminalRequest,
) (res *schema.TerminalResponse, err error) {
	ctx, span := dog.CreateSpan(ctx, "TerminalService_Create")
	defer span.End()

	jsonConfig, err := json.Marshal(terminal.Config)
	if err != nil {
		return nil, err
	}

	encryptedConfig, err := dog.AESEncrypt(string(jsonConfig), dog.ConfigString("SYMMETRIC_KEY"))
	if err != nil {
		return nil, err
	}

	response, err := s.terminalBasicRepo.Create(ctx, &model.Terminal{
		AcquirerID:           terminal.AcquirerID,
		EncryptedConfig:      encryptedConfig,
		TwoStageTimeout:      terminal.TwoStageTimeout,
		AccountNumber:        terminal.AccountNumber,
		IsTransit:            terminal.IsTransit,
		AcquirerTerminalName: terminal.AcquirerTerminalName,
		Description:          terminal.Description,
		TransitBankID:        terminal.TransitBankID,
	})
	if err != nil {
		return nil, err
	}

	return schema.NewTerminalResponse(response), nil
}

func (s *TerminalService) GetAll(ctx context.Context, projectID uint64) (terminals []model.Terminal, err error) {
	ctx, span := dog.CreateSpan(ctx, "TerminalService_GetAll")
	defer span.End()

	terminals, err = s.terminalBasicRepo.GetAll(ctx, projectID)
	if err != nil {
		return nil, err
	}

	for i := range terminals {
		decrypt, decryptionErr := dog.AESDecrypt(terminals[i].EncryptedConfig, dog.ConfigString("SYMMETRIC_KEY"))
		if decryptionErr != nil {
			return nil, decryptionErr
		}

		terminals[i].EncryptedConfig = decrypt
	}

	return terminals, nil
}

func (s *TerminalService) UpdateStatus(ctx context.Context, id uint64) (err error) {
	ctx, span := dog.CreateSpan(ctx, "TerminalService_UpdateStatus")
	defer span.End()

	terminal, err := s.terminalBasicRepo.GetAllInfoByID(ctx, id)
	if err != nil {
		return err
	}

	if terminal.Status == model.TerminalGlobalOff {
		return goerr.ErrStatusCannotBeChanged
	}

	var status model.TerminalStatus

	if terminal.Status == model.TerminalOff {
		status = model.TerminalOn
	} else {
		status = model.TerminalOff
	}

	return s.terminalBasicRepo.UpdateStatus(ctx, terminal.ID, status)
}

func (s *TerminalService) GetAllInfoByID(ctx context.Context, id uint64) (_ *model.Terminal, err error) {
	ctx, span := dog.CreateSpan(ctx, "TerminalService_GetAllInfoByID")
	defer span.End()

	terminal, err := s.terminalBasicRepo.GetAllInfoByID(ctx, id)
	if err != nil {
		dog.L().Error("TerminalService_GetAllInfoByID", zap.Uint64("terminal_id", id), zap.Error(err))
		return nil, err
	}

	decrypt, err := dog.AESDecrypt(terminal.EncryptedConfig, dog.ConfigString("SYMMETRIC_KEY"))
	if err != nil {
		return nil, err
	}

	terminal.EncryptedConfig = decrypt

	return terminal, nil
}

func (s *TerminalService) FindActiveTerminalsByProject(
	ctx context.Context,
	projectID uint64,
	paymentType uint64,
) (_ model.Terminals, err error) {
	ctx, span := dog.CreateSpan(ctx, "TerminalService_FindActiveTerminalsByProject")
	defer span.End()

	terminals, err := s.terminalRepo.FindActiveTerminalsByProject(ctx, projectID, paymentType)
	if err != nil {
		dog.L().Error("TerminalService_FindActiveTerminalsByProject", zap.Uint64("project_id", projectID), zap.Uint64("payment_type", paymentType),
			zap.Error(err))
		return nil, err
	}

	for _, terminal := range terminals {
		decrypt, err := dog.AESDecrypt(terminal.EncryptedConfig, dog.ConfigString("SYMMETRIC_KEY"))
		if err != nil {
			return nil, err
		}

		terminal.EncryptedConfig = decrypt
	}

	return terminals, nil
}

func (s *TerminalService) GetByFilters(
	ctx context.Context,
	filters schema.FiltersTerminalRequest,
	pagination *middlewares.PaginationInfo,
) (_ []*schema.TerminalResponse, err error) {
	ctx, span := dog.CreateSpan(ctx, "TerminalService_GetByFilters")
	defer span.End()

	terminals, err := s.terminalBasicRepo.GetByFilters(ctx, filters, pagination)
	if err != nil {
		return nil, err
	}

	return schema.NewTerminalsResponses(terminals), nil
}
