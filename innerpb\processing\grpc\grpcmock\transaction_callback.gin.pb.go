// Code generated by MockGen. DO NOT EDIT.
// Source: transaction_callback.gin.pb.go

// Package grpcmock is a generated GoMock package.
package grpcmock

import (
	reflect "reflect"

	gin "github.com/gin-gonic/gin"
	gomock "github.com/golang/mock/gomock"
)

// MockGinTransactionCallbackServer is a mock of GinTransactionCallbackServer interface.
type MockGinTransactionCallbackServer struct {
	ctrl     *gomock.Controller
	recorder *MockGinTransactionCallbackServerMockRecorder
}

// MockGinTransactionCallbackServerMockRecorder is the mock recorder for MockGinTransactionCallbackServer.
type MockGinTransactionCallbackServerMockRecorder struct {
	mock *MockGinTransactionCallbackServer
}

// NewMockGinTransactionCallbackServer creates a new mock instance.
func NewMockGinTransactionCallbackServer(ctrl *gomock.Controller) *MockGinTransactionCallbackServer {
	mock := &MockGinTransactionCallbackServer{ctrl: ctrl}
	mock.recorder = &MockGinTransactionCallbackServerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockGinTransactionCallbackServer) EXPECT() *MockGinTransactionCallbackServerMockRecorder {
	return m.recorder
}

// SendCallback mocks base method.
func (m *MockGinTransactionCallbackServer) SendCallback(c *gin.Context) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SendCallback", c)
	ret0, _ := ret[0].(error)
	return ret0
}

// SendCallback indicates an expected call of SendCallback.
func (mr *MockGinTransactionCallbackServerMockRecorder) SendCallback(c interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SendCallback", reflect.TypeOf((*MockGinTransactionCallbackServer)(nil).SendCallback), c)
}
