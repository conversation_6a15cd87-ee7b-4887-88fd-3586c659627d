// Code generated by protoc-gen-go-mvp. DO NOT EDIT.
// versions:
// - protoc-gen-go-mvp v2.0.1
// created by mvp authors

package grpc

import (
	dog "git.local/sensitive/sdk/dog"
	gin "github.com/gin-gonic/gin"
	metadata "google.golang.org/grpc/metadata"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
	http "net/http"
)

func RegisterGinPayorderRouterGroup(
	in *gin.RouterGroup,
	handler func(func(c *gin.Context) error) gin.HandlerFunc,
) error {
	service, err := NewGinPayorderService()
	if err != nil {
		return err
	}

	routerGroup := in.Group("processing.payorder.payorder.Payorder")
	routerGroup.PUT("/StartCheckInProcessOrdersWorker", handler(service.StartCheckInProcessOrdersWorker))
	routerGroup.PUT("/StartCreateNewPaymentOrdersWorker", handler(service.StartCreateNewPaymentOrdersWorker))
	return nil
}

func NewGinPayorderService() (GinPayorderServer, error) {
	client, err := NewPreparedPayorderClient()
	if err != nil {
		return nil, err
	}

	return &ginPayorderServer{
		client: NewLoggedPayorderClient(
			NewIamPayorderClient(client),
		),
	}, nil
}

//go:generate mockgen -destination=./grpcmock/payorder.gin.pb.go -package=grpcmock -source=payorder.gin.pb.go GinPayorderServer
type GinPayorderServer interface {
	StartCheckInProcessOrdersWorker(c *gin.Context) error
	StartCreateNewPaymentOrdersWorker(c *gin.Context) error
}

var _ GinPayorderServer = (*ginPayorderServer)(nil)

type ginPayorderServer struct {
	client PayorderClient
}

type Payorder_StartCheckInProcessOrdersWorker_Success struct {
	// Status is the status of the response. Set true
	Status bool `json:"status"`
	// Message is a human-readable explanation of the status
	Message string `json:"message"`
	// StatusCode is the GoErr status. Set 0
	StatusCode int `json:"status_code"`
	// Result is the result of the request
	Result *emptypb.Empty `json:"result"`
}

type Payorder_StartCheckInProcessOrdersWorker_Failure struct {
	// Status is the status of the response. Set false
	Status bool `json:"status"`
	// Message is a human-readable explanation of the status
	Message string `json:"message"`
	// StatusCode is the GoErr status
	StatusCode int `json:"status_code"`
}

// StartCheckInProcessOrdersWorker
// @Summary StartCheckInProcessOrdersWorker
// @Security bearerAuth
// @ID Payorder_StartCheckInProcessOrdersWorker
// @Accept json
// @Param request body emptypb.Empty true "Empty"
// @Success 200 {object} Payorder_StartCheckInProcessOrdersWorker_Success
// @Failure 401 {object} Payorder_StartCheckInProcessOrdersWorker_Failure "goerrpkg.ErrorTypeAuthorization"
// @Failure 404 {object} Payorder_StartCheckInProcessOrdersWorker_Failure "goerrpkg.ErrorTypeNotFound"
// @Failure 400 {object} Payorder_StartCheckInProcessOrdersWorker_Failure "goerrpkg.ErrorTypeBadRequest"
// @Failure 500 {object} Payorder_StartCheckInProcessOrdersWorker_Failure "goerrpkg.ErrorTypeUnexpectedError"
// @Failure 400 {object} Payorder_StartCheckInProcessOrdersWorker_Failure "gin.ErrorTypeBind"
// @Failure 500 {object} Payorder_StartCheckInProcessOrdersWorker_Failure "Undefined error"
// @Produce json
// @Router /processing.payorder.payorder.Payorder/StartCheckInProcessOrdersWorker [put]
// @tags Payorder
func (s *ginPayorderServer) StartCheckInProcessOrdersWorker(c *gin.Context) error {
	ctx, span := dog.CreateSpan(c.Request.Context(), "GinPayorderServer_StartCheckInProcessOrdersWorker")
	defer span.End()

	var request emptypb.Empty
	if err := c.ShouldBindJSON(&request); err != nil {
		return err
	}

	ctx = metadata.NewOutgoingContext(ctx, metadata.MD(c.Request.Header))

	response, err := s.client.StartCheckInProcessOrdersWorker(ctx, &request)
	if err != nil {
		return err
	}

	c.JSON(http.StatusOK, &Payorder_StartCheckInProcessOrdersWorker_Success{
		Status:     true,
		Message:    "Success",
		StatusCode: 0,
		Result:     response,
	})
	return nil
}

type Payorder_StartCreateNewPaymentOrdersWorker_Success struct {
	// Status is the status of the response. Set true
	Status bool `json:"status"`
	// Message is a human-readable explanation of the status
	Message string `json:"message"`
	// StatusCode is the GoErr status. Set 0
	StatusCode int `json:"status_code"`
	// Result is the result of the request
	Result *emptypb.Empty `json:"result"`
}

type Payorder_StartCreateNewPaymentOrdersWorker_Failure struct {
	// Status is the status of the response. Set false
	Status bool `json:"status"`
	// Message is a human-readable explanation of the status
	Message string `json:"message"`
	// StatusCode is the GoErr status
	StatusCode int `json:"status_code"`
}

// StartCreateNewPaymentOrdersWorker
// @Summary StartCreateNewPaymentOrdersWorker
// @Security bearerAuth
// @ID Payorder_StartCreateNewPaymentOrdersWorker
// @Accept json
// @Param request body emptypb.Empty true "Empty"
// @Success 200 {object} Payorder_StartCreateNewPaymentOrdersWorker_Success
// @Failure 401 {object} Payorder_StartCreateNewPaymentOrdersWorker_Failure "goerrpkg.ErrorTypeAuthorization"
// @Failure 404 {object} Payorder_StartCreateNewPaymentOrdersWorker_Failure "goerrpkg.ErrorTypeNotFound"
// @Failure 400 {object} Payorder_StartCreateNewPaymentOrdersWorker_Failure "goerrpkg.ErrorTypeBadRequest"
// @Failure 500 {object} Payorder_StartCreateNewPaymentOrdersWorker_Failure "goerrpkg.ErrorTypeUnexpectedError"
// @Failure 400 {object} Payorder_StartCreateNewPaymentOrdersWorker_Failure "gin.ErrorTypeBind"
// @Failure 500 {object} Payorder_StartCreateNewPaymentOrdersWorker_Failure "Undefined error"
// @Produce json
// @Router /processing.payorder.payorder.Payorder/StartCreateNewPaymentOrdersWorker [put]
// @tags Payorder
func (s *ginPayorderServer) StartCreateNewPaymentOrdersWorker(c *gin.Context) error {
	ctx, span := dog.CreateSpan(c.Request.Context(), "GinPayorderServer_StartCreateNewPaymentOrdersWorker")
	defer span.End()

	var request emptypb.Empty
	if err := c.ShouldBindJSON(&request); err != nil {
		return err
	}

	ctx = metadata.NewOutgoingContext(ctx, metadata.MD(c.Request.Header))

	response, err := s.client.StartCreateNewPaymentOrdersWorker(ctx, &request)
	if err != nil {
		return err
	}

	c.JSON(http.StatusOK, &Payorder_StartCreateNewPaymentOrdersWorker_Success{
		Status:     true,
		Message:    "Success",
		StatusCode: 0,
		Result:     response,
	})
	return nil
}
