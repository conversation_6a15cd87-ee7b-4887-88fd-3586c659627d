package service

import (
	"context"
	"git.local/sensitive/innerpb/processing/goerr"
	"git.local/sensitive/processing/acquirer/model"
	"git.local/sensitive/processing/acquirer/repository/database"
	"git.local/sensitive/sdk/dog"
)

type BasicTerminalService struct {
	terminalBalanceRepo database.TerminalBalancer
}

func NewBasicTerminalService(
	terminalBalanceRepo database.TerminalBalancer,
) BasicTerminaler {
	return &BasicTerminalService{
		terminalBalanceRepo: terminalBalanceRepo,
	}
}

func (b BasicTerminalService) GetByParams(
	ctx context.Context,
	projectID, acquirerID, transactionTypeID uint64,
) (_ model.Terminal, err error) {
	ctx, span := dog.CreateSpan(ctx, "BasicTerminalService_GetByParams")
	defer span.End()

	terminal, err := b.terminalBalanceRepo.GetExactTerminal(ctx, projectID, transactionTypeID, acquirerID)
	if err != nil {
		return model.Terminal{}, err
	}

	terminal.EncryptedConfig, err = dog.AESDecrypt(terminal.EncryptedConfig, dog.ConfigString("SYMMETRIC_KEY"))
	if err != nil {
		return model.Terminal{}, err
	}

	return terminal, nil
}

func (b BasicTerminalService) GetTerminalsByIDs(
	ctx context.Context,
	terminalIDs []uint64,
) ([]model.Terminal, error) {
	ctx, span := dog.CreateSpan(ctx, "BasicTerminalService_GetTerminalsByIDs")
	defer span.End()

	terminals, err := b.terminalBalanceRepo.GetTerminalsByIDs(ctx, terminalIDs)
	if err != nil {
		return nil, err
	}

	if len(terminals) == 0 {
		return nil, goerr.ErrTerminalNotFound.WithCtx(ctx)
	}

	return terminals, nil
}
