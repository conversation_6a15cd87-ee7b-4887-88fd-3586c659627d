// Code generated by MockGen. DO NOT EDIT.
// Source: bcc_account.gin.pb.go

// Package grpcmock is a generated GoMock package.
package grpcmock

import (
	reflect "reflect"

	gin "github.com/gin-gonic/gin"
	gomock "github.com/golang/mock/gomock"
)

// MockGinBCCAccountServer is a mock of GinBCCAccountServer interface.
type MockGinBCCAccountServer struct {
	ctrl     *gomock.Controller
	recorder *MockGinBCCAccountServerMockRecorder
}

// MockGinBCCAccountServerMockRecorder is the mock recorder for MockGinBCCAccountServer.
type MockGinBCCAccountServerMockRecorder struct {
	mock *MockGinBCCAccountServer
}

// NewMockGinBCCAccountServer creates a new mock instance.
func NewMockGinBCCAccountServer(ctrl *gomock.Controller) *MockGinBCCAccountServer {
	mock := &MockGinBCCAccountServer{ctrl: ctrl}
	mock.recorder = &MockGinBCCAccountServerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockGinBCCAccountServer) EXPECT() *MockGinBCCAccountServerMockRecorder {
	return m.recorder
}

// AcceptTransfer mocks base method.
func (m *MockGinBCCAccountServer) AcceptTransfer(c *gin.Context) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AcceptTransfer", c)
	ret0, _ := ret[0].(error)
	return ret0
}

// AcceptTransfer indicates an expected call of AcceptTransfer.
func (mr *MockGinBCCAccountServerMockRecorder) AcceptTransfer(c interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AcceptTransfer", reflect.TypeOf((*MockGinBCCAccountServer)(nil).AcceptTransfer), c)
}

// DeclineTransfer mocks base method.
func (m *MockGinBCCAccountServer) DeclineTransfer(c *gin.Context) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeclineTransfer", c)
	ret0, _ := ret[0].(error)
	return ret0
}

// DeclineTransfer indicates an expected call of DeclineTransfer.
func (mr *MockGinBCCAccountServerMockRecorder) DeclineTransfer(c interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeclineTransfer", reflect.TypeOf((*MockGinBCCAccountServer)(nil).DeclineTransfer), c)
}

// GetAccountBalance mocks base method.
func (m *MockGinBCCAccountServer) GetAccountBalance(c *gin.Context) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAccountBalance", c)
	ret0, _ := ret[0].(error)
	return ret0
}

// GetAccountBalance indicates an expected call of GetAccountBalance.
func (mr *MockGinBCCAccountServerMockRecorder) GetAccountBalance(c interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAccountBalance", reflect.TypeOf((*MockGinBCCAccountServer)(nil).GetAccountBalance), c)
}

// GetAccountIdentifier mocks base method.
func (m *MockGinBCCAccountServer) GetAccountIdentifier(c *gin.Context) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAccountIdentifier", c)
	ret0, _ := ret[0].(error)
	return ret0
}

// GetAccountIdentifier indicates an expected call of GetAccountIdentifier.
func (mr *MockGinBCCAccountServerMockRecorder) GetAccountIdentifier(c interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAccountIdentifier", reflect.TypeOf((*MockGinBCCAccountServer)(nil).GetAccountIdentifier), c)
}

// GetAccountStatement mocks base method.
func (m *MockGinBCCAccountServer) GetAccountStatement(c *gin.Context) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAccountStatement", c)
	ret0, _ := ret[0].(error)
	return ret0
}

// GetAccountStatement indicates an expected call of GetAccountStatement.
func (mr *MockGinBCCAccountServerMockRecorder) GetAccountStatement(c interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAccountStatement", reflect.TypeOf((*MockGinBCCAccountServer)(nil).GetAccountStatement), c)
}

// GetTransferDetails mocks base method.
func (m *MockGinBCCAccountServer) GetTransferDetails(c *gin.Context) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetTransferDetails", c)
	ret0, _ := ret[0].(error)
	return ret0
}

// GetTransferDetails indicates an expected call of GetTransferDetails.
func (mr *MockGinBCCAccountServerMockRecorder) GetTransferDetails(c interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetTransferDetails", reflect.TypeOf((*MockGinBCCAccountServer)(nil).GetTransferDetails), c)
}

// GetTransfersList mocks base method.
func (m *MockGinBCCAccountServer) GetTransfersList(c *gin.Context) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetTransfersList", c)
	ret0, _ := ret[0].(error)
	return ret0
}

// GetTransfersList indicates an expected call of GetTransfersList.
func (mr *MockGinBCCAccountServerMockRecorder) GetTransfersList(c interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetTransfersList", reflect.TypeOf((*MockGinBCCAccountServer)(nil).GetTransfersList), c)
}

// MakeMerchantCheck mocks base method.
func (m *MockGinBCCAccountServer) MakeMerchantCheck(c *gin.Context) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "MakeMerchantCheck", c)
	ret0, _ := ret[0].(error)
	return ret0
}

// MakeMerchantCheck indicates an expected call of MakeMerchantCheck.
func (mr *MockGinBCCAccountServerMockRecorder) MakeMerchantCheck(c interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "MakeMerchantCheck", reflect.TypeOf((*MockGinBCCAccountServer)(nil).MakeMerchantCheck), c)
}

// MakeTransfer mocks base method.
func (m *MockGinBCCAccountServer) MakeTransfer(c *gin.Context) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "MakeTransfer", c)
	ret0, _ := ret[0].(error)
	return ret0
}

// MakeTransfer indicates an expected call of MakeTransfer.
func (mr *MockGinBCCAccountServerMockRecorder) MakeTransfer(c interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "MakeTransfer", reflect.TypeOf((*MockGinBCCAccountServer)(nil).MakeTransfer), c)
}

// RedoTransfer mocks base method.
func (m *MockGinBCCAccountServer) RedoTransfer(c *gin.Context) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "RedoTransfer", c)
	ret0, _ := ret[0].(error)
	return ret0
}

// RedoTransfer indicates an expected call of RedoTransfer.
func (mr *MockGinBCCAccountServerMockRecorder) RedoTransfer(c interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "RedoTransfer", reflect.TypeOf((*MockGinBCCAccountServer)(nil).RedoTransfer), c)
}
