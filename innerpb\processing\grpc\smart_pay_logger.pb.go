// Code generated by protoc-gen-go-mvp. DO NOT EDIT.
// versions:
// - protoc-gen-go-mvp v2.0.1
// created by mvp authors

package grpc

import (
	context "context"
	cntx "git.local/sensitive/mvp/pkg/cntx"
	ctxzap "github.com/grpc-ecosystem/go-grpc-middleware/logging/zap/ctxzap"
	zap "go.uber.org/zap"
	grpc "google.golang.org/grpc"
)

func file_inner_processing_grpc_smart_pay_proto_message_ApplePaySessionRequestV1ToZap(
	label string,
	in *ApplePaySessionRequestV1,
) zap.Field {
	if in == nil {
		return zap.Reflect(label, in)
	}

	return zap.Dict(
		label,
		zap.Any("ValidationURL", in.GetValidationURL()),
	)
}

func file_inner_processing_grpc_smart_pay_proto_message_ApplePaySessionResponseV1ToZap(
	label string,
	in *ApplePaySessionResponseV1,
) zap.Field {
	if in == nil {
		return zap.Reflect(label, in)
	}

	return zap.Dict(
		label,
		zap.Any("Response", in.GetResponse()),
	)
}

func file_inner_processing_grpc_smart_pay_proto_message_AssuranceDetailsToZap(
	label string,
	in *AssuranceDetails,
) zap.Field {
	if in == nil {
		return zap.Reflect(label, in)
	}

	return zap.Dict(
		label,
		zap.Any("AccountVerified", in.GetAccountVerified()),
		zap.Any("CardHolderAuthenticated", in.GetCardHolderAuthenticated()),
	)
}

func file_inner_processing_grpc_smart_pay_proto_message_DecodeTokenRequestV1ToZap(
	label string,
	in *DecodeTokenRequestV1,
) zap.Field {
	if in == nil {
		return zap.Reflect(label, in)
	}

	return zap.Dict(
		label,
		zap.Any("TransactionIdentifier", in.GetTransactionIdentifier()),
		file_inner_processing_grpc_smart_pay_proto_message_PaymentMethodToZap("Method", in.GetMethod()),
		file_inner_processing_grpc_smart_pay_proto_message_PaymentDataToZap("Data", in.GetData()),
	)
}

func file_inner_processing_grpc_smart_pay_proto_message_DecodeTokenResponseV1ToZap(
	label string,
	in *DecodeTokenResponseV1,
) zap.Field {
	if in == nil {
		return zap.Reflect(label, in)
	}

	return zap.Dict(
		label,
		zap.Any("ApplicationPrimaryAccountNumber", "[***hidden***]"),
		zap.Any("ApplicationExpirationMonth", "[***hidden***]"),
		zap.Any("ApplicationExpirationYear", "[***hidden***]"),
		zap.Any("CurrencyCode", in.GetCurrencyCode()),
		zap.Any("TransactionAmount", in.GetTransactionAmount()),
		zap.Any("CardholderName", in.GetCardholderName()),
		zap.Any("DeviceManufacturerIdentifier", in.GetDeviceManufacturerIdentifier()),
		zap.Any("PaymentDataType", in.GetPaymentDataType()),
		file_inner_processing_grpc_smart_pay_proto_message_PaymentDataResponseToZap("PaymentData", in.GetPaymentData()),
		zap.Any("Version", in.GetVersion()),
	)
}

func file_inner_processing_grpc_smart_pay_proto_message_DecryptGPayTokenRequestV1ToZap(
	label string,
	in *DecryptGPayTokenRequestV1,
) zap.Field {
	if in == nil {
		return zap.Reflect(label, in)
	}

	return zap.Dict(
		label,
		zap.Any("Token", in.GetToken()),
	)
}

func file_inner_processing_grpc_smart_pay_proto_message_DecryptGPayTokenResponseV1ToZap(
	label string,
	in *DecryptGPayTokenResponseV1,
) zap.Field {
	if in == nil {
		return zap.Reflect(label, in)
	}

	return zap.Dict(
		label,
		zap.Any("GatewayMerchantId", in.GetGatewayMerchantId()),
		zap.Any("MessageExpiration", in.GetMessageExpiration()),
		zap.Any("MessageId", in.GetMessageId()),
		zap.Any("PaymentMethod", in.GetPaymentMethod()),
		file_inner_processing_grpc_smart_pay_proto_message_PaymentMethodDetailsToZap("PaymentMethodDetails", in.GetPaymentMethodDetails()),
	)
}

func file_inner_processing_grpc_smart_pay_proto_message_GetGPayCredentialsRequestV1ToZap(
	label string,
	in *GetGPayCredentialsRequestV1,
) zap.Field {
	if in == nil {
		return zap.Reflect(label, in)
	}

	return zap.Dict(
		label,
		zap.Any("ProjectId", in.GetProjectId()),
	)
}

func file_inner_processing_grpc_smart_pay_proto_message_GetGPayCredentialsResponseV1ToZap(
	label string,
	in *GetGPayCredentialsResponseV1,
) zap.Field {
	if in == nil {
		return zap.Reflect(label, in)
	}

	return zap.Dict(
		label,
		zap.Any("GooglePayGatewayMerchant", in.GetGooglePayGatewayMerchant()),
		zap.Any("GooglePayMerchantId", in.GetGooglePayMerchantId()),
	)
}

func file_inner_processing_grpc_smart_pay_proto_message_HeaderToZap(
	label string,
	in *Header,
) zap.Field {
	if in == nil {
		return zap.Reflect(label, in)
	}

	return zap.Dict(
		label,
		zap.Any("ApplicationName", in.GetApplicationName()),
		zap.Any("EphemeralPublicKey", in.GetEphemeralPublicKey()),
		zap.Any("WrappedKey", in.GetWrappedKey()),
		zap.Any("PublicKeyHash", in.GetPublicKeyHash()),
		zap.Any("TransactionId", in.GetTransactionId()),
	)
}

func file_inner_processing_grpc_smart_pay_proto_message_PaymentDataToZap(
	label string,
	in *PaymentData,
) zap.Field {
	if in == nil {
		return zap.Reflect(label, in)
	}

	return zap.Dict(
		label,
		zap.Any("Version", in.GetVersion()),
		zap.Any("Signature", in.GetSignature()),
		file_inner_processing_grpc_smart_pay_proto_message_HeaderToZap("Header", in.GetHeader()),
		zap.Any("Data", in.GetData()),
	)
}

func file_inner_processing_grpc_smart_pay_proto_message_PaymentDataResponseToZap(
	label string,
	in *PaymentDataResponse,
) zap.Field {
	if in == nil {
		return zap.Reflect(label, in)
	}

	return zap.Dict(
		label,
		zap.Any("OnlinePaymentCryptogram", in.GetOnlinePaymentCryptogram()),
		zap.Any("EciIndicator", in.GetEciIndicator()),
		zap.Any("EmvData", in.GetEmvData()),
		zap.Any("EncryptedPinData", in.GetEncryptedPinData()),
	)
}

func file_inner_processing_grpc_smart_pay_proto_message_PaymentMethodToZap(
	label string,
	in *PaymentMethod,
) zap.Field {
	if in == nil {
		return zap.Reflect(label, in)
	}

	return zap.Dict(
		label,
		zap.Any("Type", in.GetType()),
		zap.Any("Network", in.GetNetwork()),
		zap.Any("DisplayName", in.GetDisplayName()),
	)
}

func file_inner_processing_grpc_smart_pay_proto_message_PaymentMethodDetailsToZap(
	label string,
	in *PaymentMethodDetails,
) zap.Field {
	if in == nil {
		return zap.Reflect(label, in)
	}

	return zap.Dict(
		label,
		zap.Any("ExpirationYear", in.GetExpirationYear()),
		zap.Any("ExpirationMonth", in.GetExpirationMonth()),
		zap.Any("Pan", "[***hidden***]"),
		zap.Any("AuthMethod", in.GetAuthMethod()),
		zap.Any("EciIndicator", in.GetEciIndicator()),
		zap.Any("Cryptogram", in.GetCryptogram()),
		file_inner_processing_grpc_smart_pay_proto_message_AssuranceDetailsToZap("AssuranceDetails", in.GetAssuranceDetails()),
	)
}

var _ SmartPayServer = (*loggedSmartPayServer)(nil)

func NewLoggedSmartPayServer(srv SmartPayServer) SmartPayServer {
	return &loggedSmartPayServer{srv: srv}
}

type loggedSmartPayServer struct {
	UnimplementedSmartPayServer

	srv SmartPayServer
}

func (s *loggedSmartPayServer) ApplePaySession(
	ctx context.Context,
	request *ApplePaySessionRequestV1,
) (
	response *ApplePaySessionResponseV1,
	err error,
) {
	label := cntx.Begin(ctx, "SmartPayServer_ApplePaySession")
	defer cntx.End(ctx, label)

	defer func() {
		if err != nil {
			ctxzap.AddFields(ctx, zap.Any(label+"error", err))
			return
		}

		ctxzap.AddFields(
			ctx,
			file_inner_processing_grpc_smart_pay_proto_message_ApplePaySessionResponseV1ToZap(label+"response", response),
		)
	}()

	ctxzap.AddFields(
		ctx,
		file_inner_processing_grpc_smart_pay_proto_message_ApplePaySessionRequestV1ToZap(label+"request", request),
	)

	response, err = s.srv.ApplePaySession(ctx, request)

	return
}

func (s *loggedSmartPayServer) DecodeToken(
	ctx context.Context,
	request *DecodeTokenRequestV1,
) (
	response *DecodeTokenResponseV1,
	err error,
) {
	label := cntx.Begin(ctx, "SmartPayServer_DecodeToken")
	defer cntx.End(ctx, label)

	defer func() {
		if err != nil {
			ctxzap.AddFields(ctx, zap.Any(label+"error", err))
			return
		}

		ctxzap.AddFields(
			ctx,
			file_inner_processing_grpc_smart_pay_proto_message_DecodeTokenResponseV1ToZap(label+"response", response),
		)
	}()

	ctxzap.AddFields(
		ctx,
		file_inner_processing_grpc_smart_pay_proto_message_DecodeTokenRequestV1ToZap(label+"request", request),
	)

	response, err = s.srv.DecodeToken(ctx, request)

	return
}

func (s *loggedSmartPayServer) DecryptGPayToken(
	ctx context.Context,
	request *DecryptGPayTokenRequestV1,
) (
	response *DecryptGPayTokenResponseV1,
	err error,
) {
	label := cntx.Begin(ctx, "SmartPayServer_DecryptGPayToken")
	defer cntx.End(ctx, label)

	defer func() {
		if err != nil {
			ctxzap.AddFields(ctx, zap.Any(label+"error", err))
			return
		}

		ctxzap.AddFields(
			ctx,
			file_inner_processing_grpc_smart_pay_proto_message_DecryptGPayTokenResponseV1ToZap(label+"response", response),
		)
	}()

	ctxzap.AddFields(
		ctx,
		file_inner_processing_grpc_smart_pay_proto_message_DecryptGPayTokenRequestV1ToZap(label+"request", request),
	)

	response, err = s.srv.DecryptGPayToken(ctx, request)

	return
}

func (s *loggedSmartPayServer) GetGPayCredentials(
	ctx context.Context,
	request *GetGPayCredentialsRequestV1,
) (
	response *GetGPayCredentialsResponseV1,
	err error,
) {
	label := cntx.Begin(ctx, "SmartPayServer_GetGPayCredentials")
	defer cntx.End(ctx, label)

	defer func() {
		if err != nil {
			ctxzap.AddFields(ctx, zap.Any(label+"error", err))
			return
		}

		ctxzap.AddFields(
			ctx,
			file_inner_processing_grpc_smart_pay_proto_message_GetGPayCredentialsResponseV1ToZap(label+"response", response),
		)
	}()

	ctxzap.AddFields(
		ctx,
		file_inner_processing_grpc_smart_pay_proto_message_GetGPayCredentialsRequestV1ToZap(label+"request", request),
	)

	response, err = s.srv.GetGPayCredentials(ctx, request)

	return
}

var _ SmartPayClient = (*loggedSmartPayClient)(nil)

func NewLoggedSmartPayClient(client SmartPayClient) SmartPayClient {
	return &loggedSmartPayClient{client: client}
}

type loggedSmartPayClient struct {
	client SmartPayClient
}

func (s *loggedSmartPayClient) ApplePaySession(
	ctx context.Context,
	request *ApplePaySessionRequestV1,
	opts ...grpc.CallOption,
) (
	response *ApplePaySessionResponseV1,
	err error,
) {
	label := cntx.Begin(ctx, "SmartPayClient_ApplePaySession")
	defer cntx.End(ctx, label)

	defer func() {
		if err != nil {
			ctxzap.AddFields(ctx, zap.Any(label+"error", err))
			return
		}

		ctxzap.AddFields(
			ctx,
			file_inner_processing_grpc_smart_pay_proto_message_ApplePaySessionResponseV1ToZap(label+"response", response),
		)
	}()

	ctxzap.AddFields(
		ctx,
		file_inner_processing_grpc_smart_pay_proto_message_ApplePaySessionRequestV1ToZap(label+"request", request),
	)

	response, err = s.client.ApplePaySession(ctx, request, opts...)

	return
}

func (s *loggedSmartPayClient) DecodeToken(
	ctx context.Context,
	request *DecodeTokenRequestV1,
	opts ...grpc.CallOption,
) (
	response *DecodeTokenResponseV1,
	err error,
) {
	label := cntx.Begin(ctx, "SmartPayClient_DecodeToken")
	defer cntx.End(ctx, label)

	defer func() {
		if err != nil {
			ctxzap.AddFields(ctx, zap.Any(label+"error", err))
			return
		}

		ctxzap.AddFields(
			ctx,
			file_inner_processing_grpc_smart_pay_proto_message_DecodeTokenResponseV1ToZap(label+"response", response),
		)
	}()

	ctxzap.AddFields(
		ctx,
		file_inner_processing_grpc_smart_pay_proto_message_DecodeTokenRequestV1ToZap(label+"request", request),
	)

	response, err = s.client.DecodeToken(ctx, request, opts...)

	return
}

func (s *loggedSmartPayClient) DecryptGPayToken(
	ctx context.Context,
	request *DecryptGPayTokenRequestV1,
	opts ...grpc.CallOption,
) (
	response *DecryptGPayTokenResponseV1,
	err error,
) {
	label := cntx.Begin(ctx, "SmartPayClient_DecryptGPayToken")
	defer cntx.End(ctx, label)

	defer func() {
		if err != nil {
			ctxzap.AddFields(ctx, zap.Any(label+"error", err))
			return
		}

		ctxzap.AddFields(
			ctx,
			file_inner_processing_grpc_smart_pay_proto_message_DecryptGPayTokenResponseV1ToZap(label+"response", response),
		)
	}()

	ctxzap.AddFields(
		ctx,
		file_inner_processing_grpc_smart_pay_proto_message_DecryptGPayTokenRequestV1ToZap(label+"request", request),
	)

	response, err = s.client.DecryptGPayToken(ctx, request, opts...)

	return
}

func (s *loggedSmartPayClient) GetGPayCredentials(
	ctx context.Context,
	request *GetGPayCredentialsRequestV1,
	opts ...grpc.CallOption,
) (
	response *GetGPayCredentialsResponseV1,
	err error,
) {
	label := cntx.Begin(ctx, "SmartPayClient_GetGPayCredentials")
	defer cntx.End(ctx, label)

	defer func() {
		if err != nil {
			ctxzap.AddFields(ctx, zap.Any(label+"error", err))
			return
		}

		ctxzap.AddFields(
			ctx,
			file_inner_processing_grpc_smart_pay_proto_message_GetGPayCredentialsResponseV1ToZap(label+"response", response),
		)
	}()

	ctxzap.AddFields(
		ctx,
		file_inner_processing_grpc_smart_pay_proto_message_GetGPayCredentialsRequestV1ToZap(label+"request", request),
	)

	response, err = s.client.GetGPayCredentials(ctx, request, opts...)

	return
}
