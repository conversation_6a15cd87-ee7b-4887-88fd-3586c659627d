-- +goose Up
-- +goose StatementBegin
ALTER TABLE "acquirer"."terminals"
    ADD COLUMN IF NOT EXISTS "account_number" VARCHAR (255);
ALTER TABLE "acquirer"."terminals"
    ADD COLUMN IF NOT EXISTS "is_transit" BOOLEAN NOT NULL default false;
-- +goose StatementEnd

-- +goose Down
-- +goose StatementBegin
-- ALTER TABLE "acquirer"."terminals" DROP COLUMN "account_number" VARCHAR(255);
-- ALTER TABLE "acquirer"."terminals" DROP COLUMN "is_transit" BOOLEAN;
-- +goose StatementEnd