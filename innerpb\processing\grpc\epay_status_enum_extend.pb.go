// Code generated by protoc-gen-go-mvp. DO NOT EDIT.
// versions:
// - protoc-gen-go-mvp v2.0.1
// created by mvp authors

package grpc

import (
	math "math"
	strings "strings"
)

func (val EnumEpayStatusCode) Synonym() EnumEpayStatusCode {
	if _, ok := EnumEpayStatusCode_name[int32(val)]; ok {
		return val
	}

	return EnumEpayStatusCode(math.MinInt32)
}

func (val EnumEpayStatusCode) Int() int {
	return int(val.Synonym())
}

func (val EnumEpayStatusCode) IntPtr() *int {
	result := int(val.Synonym())
	return &result
}

func (val EnumEpayStatusCode) Int32() int32 {
	return int32(val.Synonym())
}

func (val EnumEpayStatusCode) Int32Ptr() *int32 {
	result := int32(val.Synonym())
	return &result
}

func (val EnumEpayStatusCode) Int64() int64 {
	return int64(val.Synonym())
}

func (val EnumEpayStatusCode) Int64Ptr() *int64 {
	result := int64(val.Synonym())
	return &result
}

func (val EnumEpayStatusCode) Uint() uint {
	return uint(val.Synonym())
}

func (val EnumEpayStatusCode) UintPtr() *uint {
	result := uint(val.Synonym())
	return &result
}

func (val EnumEpayStatusCode) Uint32() uint32 {
	return uint32(val.Synonym())
}

func (val EnumEpayStatusCode) Uint32Ptr() *uint32 {
	result := uint32(val.Synonym())
	return &result
}

func (val EnumEpayStatusCode) Uint64() uint64 {
	return uint64(val.Synonym())
}

func (val EnumEpayStatusCode) Uint64Ptr() *uint64 {
	result := uint64(val.Synonym())
	return &result
}

func (val EnumEpayStatusCode) IsKnown() bool {
	return val.Synonym() != EnumEpayStatusCode(math.MinInt32)
}

func ConvertIntToEnumEpayStatusCode(in int) EnumEpayStatusCode {
	return EnumEpayStatusCode(in).Synonym()
}

func ConvertUintToEnumEpayStatusCode(in uint) EnumEpayStatusCode {
	return EnumEpayStatusCode(in).Synonym()
}

func ConvertInt32ToEnumEpayStatusCode(in int32) EnumEpayStatusCode {
	return EnumEpayStatusCode(in).Synonym()
}

func ConvertUint32ToEnumEpayStatusCode(in uint32) EnumEpayStatusCode {
	return EnumEpayStatusCode(in).Synonym()
}

func ConvertInt64ToEnumEpayStatusCode(in int64) EnumEpayStatusCode {
	return EnumEpayStatusCode(in).Synonym()
}

func ConvertUint64ToEnumEpayStatusCode(in uint64) EnumEpayStatusCode {
	return EnumEpayStatusCode(in).Synonym()
}

var EnumEpayStatusCode_Lower_value = map[string]EnumEpayStatusCode{
	"charge":     0,
	"new":        1,
	"refund":     2,
	"cancel":     3,
	"cancel_old": 4,
	"3d":         5,
	"reject":     6,
	"auth":       7,
	"failed":     8,
}

func ConvertStringToEnumEpayStatusCode(in string) EnumEpayStatusCode {
	if result, ok := EnumEpayStatusCode_value[in]; ok {
		return EnumEpayStatusCode(result)
	}

	if result, ok := EnumEpayStatusCode_Lower_value[strings.ToLower(in)]; ok {
		return EnumEpayStatusCode(result)
	}

	return EnumEpayStatusCode(math.MinInt32)
}

var SliceEnumEpayStatusCodeConvert *sliceEnumEpayStatusCodeConvert

type sliceEnumEpayStatusCodeConvert struct{}

func (*sliceEnumEpayStatusCodeConvert) Synonym(in []EnumEpayStatusCode) []EnumEpayStatusCode {
	result := make([]EnumEpayStatusCode, len(in))
	for i, v := range in {
		result[i] = v.Synonym()
	}

	return result
}

func (*sliceEnumEpayStatusCodeConvert) Int32(in []EnumEpayStatusCode) []int32 {
	result := make([]int32, len(in))
	for i, v := range in {
		result[i] = v.Int32()
	}

	return result
}

func (*sliceEnumEpayStatusCodeConvert) Uint32(in []EnumEpayStatusCode) []uint32 {
	result := make([]uint32, len(in))
	for i, v := range in {
		result[i] = v.Uint32()
	}

	return result
}

func (*sliceEnumEpayStatusCodeConvert) Uint64(in []EnumEpayStatusCode) []uint64 {
	result := make([]uint64, len(in))
	for i, v := range in {
		result[i] = v.Uint64()
	}

	return result
}

func (*sliceEnumEpayStatusCodeConvert) Int64(in []EnumEpayStatusCode) []int64 {
	result := make([]int64, len(in))
	for i, v := range in {
		result[i] = v.Int64()
	}

	return result
}

func (*sliceEnumEpayStatusCodeConvert) Uint(in []EnumEpayStatusCode) []uint {
	result := make([]uint, len(in))
	for i, v := range in {
		result[i] = v.Uint()
	}

	return result
}

func (*sliceEnumEpayStatusCodeConvert) Int(in []EnumEpayStatusCode) []int {
	result := make([]int, len(in))
	for i, v := range in {
		result[i] = v.Int()
	}

	return result
}

func (*sliceEnumEpayStatusCodeConvert) IsKnown(in []EnumEpayStatusCode) []bool {
	result := make([]bool, len(in))
	for i, v := range in {
		result[i] = v.IsKnown()
	}

	return result
}

func (*sliceEnumEpayStatusCodeConvert) ConvertIntToEnumEpayStatusCode(in []int) []EnumEpayStatusCode {
	result := make([]EnumEpayStatusCode, len(in))
	for i, v := range in {
		result[i] = ConvertIntToEnumEpayStatusCode(v)
	}

	return result
}

func (*sliceEnumEpayStatusCodeConvert) ConvertUintToEnumEpayStatusCode(in []uint) []EnumEpayStatusCode {
	result := make([]EnumEpayStatusCode, len(in))
	for i, v := range in {
		result[i] = ConvertUintToEnumEpayStatusCode(v)
	}

	return result
}

func (*sliceEnumEpayStatusCodeConvert) ConvertInt32ToEnumEpayStatusCode(in []int32) []EnumEpayStatusCode {
	result := make([]EnumEpayStatusCode, len(in))
	for i, v := range in {
		result[i] = ConvertInt32ToEnumEpayStatusCode(v)
	}

	return result
}

func (*sliceEnumEpayStatusCodeConvert) ConvertUint32ToEnumEpayStatusCode(in []uint32) []EnumEpayStatusCode {
	result := make([]EnumEpayStatusCode, len(in))
	for i, v := range in {
		result[i] = ConvertUint32ToEnumEpayStatusCode(v)
	}

	return result
}

func (*sliceEnumEpayStatusCodeConvert) ConvertInt64ToEnumEpayStatusCode(in []int64) []EnumEpayStatusCode {
	result := make([]EnumEpayStatusCode, len(in))
	for i, v := range in {
		result[i] = ConvertInt64ToEnumEpayStatusCode(v)
	}

	return result
}

func (*sliceEnumEpayStatusCodeConvert) ConvertUint64ToEnumEpayStatusCode(in []uint64) []EnumEpayStatusCode {
	result := make([]EnumEpayStatusCode, len(in))
	for i, v := range in {
		result[i] = ConvertUint64ToEnumEpayStatusCode(v)
	}

	return result
}

func (*sliceEnumEpayStatusCodeConvert) ConvertStringToEnumEpayStatusCode(in []string) []EnumEpayStatusCode {
	result := make([]EnumEpayStatusCode, len(in))
	for i, v := range in {
		result[i] = ConvertStringToEnumEpayStatusCode(v)
	}

	return result
}

func NewEnumEpayStatusCodeUsage() *EnumEpayStatusCodeUsage {
	return &EnumEpayStatusCodeUsage{
		enumMap: map[EnumEpayStatusCode]bool{
			EnumEpayStatusCode_Charge:     false,
			EnumEpayStatusCode_New:        false,
			EnumEpayStatusCode_Refund:     false,
			EnumEpayStatusCode_Cancel:     false,
			EnumEpayStatusCode_Cancel_Old: false,
			EnumEpayStatusCode_ThreeD:     false,
			EnumEpayStatusCode_Reject:     false,
			EnumEpayStatusCode_Auth:       false,
			EnumEpayStatusCode_Failed:     false,
		},
	}
}

func IsEnumEpayStatusCode(target EnumEpayStatusCode, matches ...EnumEpayStatusCode) bool {
	for _, v := range matches {
		if target == v {
			return true
		}
	}

	return false
}

type EnumEpayStatusCodeUsage struct {
	enumMap map[EnumEpayStatusCode]bool
}

func (u *EnumEpayStatusCodeUsage) Use(slice ...EnumEpayStatusCode) *EnumEpayStatusCodeUsage {
	for _, in := range slice {
		u.enumMap[in] = true
	}

	return u
}

func (u *EnumEpayStatusCodeUsage) Check() []string {
	var result []string
	for k, v := range u.enumMap {
		if v {
			continue
		}

		result = append(result, k.String())
	}

	return result
}

func (u *EnumEpayStatusCodeUsage) EnumEpayStatusCode_Charge() EnumEpayStatusCode {
	u.Use(EnumEpayStatusCode_Charge)
	return EnumEpayStatusCode_Charge
}

func (u *EnumEpayStatusCodeUsage) EnumEpayStatusCode_New() EnumEpayStatusCode {
	u.Use(EnumEpayStatusCode_New)
	return EnumEpayStatusCode_New
}

func (u *EnumEpayStatusCodeUsage) EnumEpayStatusCode_Refund() EnumEpayStatusCode {
	u.Use(EnumEpayStatusCode_Refund)
	return EnumEpayStatusCode_Refund
}

func (u *EnumEpayStatusCodeUsage) EnumEpayStatusCode_Cancel() EnumEpayStatusCode {
	u.Use(EnumEpayStatusCode_Cancel)
	return EnumEpayStatusCode_Cancel
}

func (u *EnumEpayStatusCodeUsage) EnumEpayStatusCode_Cancel_Old() EnumEpayStatusCode {
	u.Use(EnumEpayStatusCode_Cancel_Old)
	return EnumEpayStatusCode_Cancel_Old
}

func (u *EnumEpayStatusCodeUsage) EnumEpayStatusCode_ThreeD() EnumEpayStatusCode {
	u.Use(EnumEpayStatusCode_ThreeD)
	return EnumEpayStatusCode_ThreeD
}

func (u *EnumEpayStatusCodeUsage) EnumEpayStatusCode_Reject() EnumEpayStatusCode {
	u.Use(EnumEpayStatusCode_Reject)
	return EnumEpayStatusCode_Reject
}

func (u *EnumEpayStatusCodeUsage) EnumEpayStatusCode_Auth() EnumEpayStatusCode {
	u.Use(EnumEpayStatusCode_Auth)
	return EnumEpayStatusCode_Auth
}

func (u *EnumEpayStatusCodeUsage) EnumEpayStatusCode_Failed() EnumEpayStatusCode {
	u.Use(EnumEpayStatusCode_Failed)
	return EnumEpayStatusCode_Failed
}
