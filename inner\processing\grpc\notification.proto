edition = "2023";

package processing.notification.notification;

option go_package = "git.local/sensitive/innerpb/processing/grpc";

import "google/protobuf/timestamp.proto";
service Notification{
  rpc GetLastMailByUserEmail(GetMailReqDataV1) returns (GetMailResDataV1){}
  rpc GetLastSMSByUserPhone(GetSMSReqDataV1) returns (GetSMSResDataV1){}
}

message GetMailReqDataV1{
  string user_email = 1;
}

message GetMailResDataV1{
  string sender = 1;
  string body = 2;
  map<string, string> payload = 3;
  repeated string receivers = 4;
  google.protobuf.Timestamp created_at = 5;
}

message GetSMSReqDataV1{
  string user_phone = 1;
}

message GetSMSResDataV1{
  string sender = 1;
  string body = 2;
  map<string, string> payload = 3;
  string receiver = 4;
  google.protobuf.Timestamp created_at = 5;
}