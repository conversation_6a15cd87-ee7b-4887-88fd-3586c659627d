package model

type Country struct {
	TimestampMixin
	ID    uint64  `gorm:"column:id" json:"id"`
	Name  string  `gorm:"column:name" json:"name"`
	Banks []*Bank `gorm:"many2many:acquirer.country_banks;" json:"banks"`
}

type CountryBasic struct {
	TimestampMixin
	ID   uint64 `gorm:"column:id" json:"id"`
	Name string `gorm:"column:name" json:"name"`
}

const (
	countiesTable = "acquirer.countries"
)

func (i Country) TableName() string {
	return countiesTable // database_schema.table_name
}

func (i CountryBasic) TableName() string {
	return countiesTable // database_schema.table_name
}
