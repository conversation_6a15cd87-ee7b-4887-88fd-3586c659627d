edition = "2023";

package processing.merchant.project;

import "inner/processing/grpc/merchant.proto";

option go_package = "git.local/sensitive/innerpb/processing/grpc";

service Project {
  rpc GetProjectsByMerchantID(ProjectsRequestV1) returns (ProjectsResponseV1) {}
  rpc IsSendEmail(processing.merchant.merchant.ProjectRequestV1) returns (IsSendEmailResponseV1) {}
  rpc GetProcessingProjectsByBUIDV1(GetProcessingProjectsByBUIDRequestV1) returns (GetProcessingProjectsByBUIDResponseV1) {}
}

message ProjectsRequestV1 {
  uint64 merchant_id = 1;
}

message ProjectsResponseV1 {
  repeated ProjectData projects = 1;
  processing.merchant.merchant.MerchantData merchant = 2;
}

message ProjectData {
  uint64 project_id = 1;
  string name = 2;
  string income_source = 3;
  string main_activity_type = 4;
  string first_activity_type = 6;
  string second_activity_type = 7;
  string third_activity_type = 8;
  string license_name = 9;
  string license_number = 10;
  bool is_license = 11;
  uint64 merchant_id = 12;
  string logo = 13;
}

message IsSendEmailResponseV1 {
  bool send_email_enabled = 1;
}

message GetProcessingProjectsByBUIDRequestV1 {
  uint64 business_unit_id = 1;
}

message GetProcessingProjectsByBUIDResponseV1 {
  uint64 business_unit_id = 1;
  repeated ProjectBasicData projects = 2;
}

message ProjectBasicData {
  string project_id = 1;
  string project_name = 2;
}