// Code generated by protoc-gen-go-mvp. DO NOT EDIT.
// versions:
// - protoc-gen-go-mvp v2.0.1
// created by mvp authors

package grpc

import (
	dog "git.local/sensitive/sdk/dog"
	gin "github.com/gin-gonic/gin"
	metadata "google.golang.org/grpc/metadata"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
	http "net/http"
)

func RegisterGinMultiacquiringEmissionRouterGroup(
	in *gin.RouterGroup,
	handler func(func(c *gin.Context) error) gin.HandlerFunc,
) error {
	service, err := NewGinMultiacquiringEmissionService()
	if err != nil {
		return err
	}

	routerGroup := in.Group("processing.multiacquiring.multiacquiring_emission.MultiacquiringEmission")
	routerGroup.PUT("/GetEmission", handler(service.GetEmission))
	routerGroup.PUT("/ConfirmEmission", handler(service.ConfirmEmission))
	return nil
}

func NewGinMultiacquiringEmissionService() (GinMultiacquiringEmissionServer, error) {
	client, err := NewPreparedMultiacquiringEmissionClient()
	if err != nil {
		return nil, err
	}

	return &ginMultiacquiringEmissionServer{
		client: NewLoggedMultiacquiringEmissionClient(
			NewIamMultiacquiringEmissionClient(client),
		),
	}, nil
}

//go:generate mockgen -destination=./grpcmock/multiacquiring_emission.gin.pb.go -package=grpcmock -source=multiacquiring_emission.gin.pb.go GinMultiacquiringEmissionServer
type GinMultiacquiringEmissionServer interface {
	GetEmission(c *gin.Context) error
	ConfirmEmission(c *gin.Context) error
}

var _ GinMultiacquiringEmissionServer = (*ginMultiacquiringEmissionServer)(nil)

type ginMultiacquiringEmissionServer struct {
	client MultiacquiringEmissionClient
}

type MultiacquiringEmission_GetEmission_Success struct {
	// Status is the status of the response. Set true
	Status bool `json:"status"`
	// Message is a human-readable explanation of the status
	Message string `json:"message"`
	// StatusCode is the GoErr status. Set 0
	StatusCode int `json:"status_code"`
	// Result is the result of the request
	Result *EmissionResponse `json:"result"`
}

type MultiacquiringEmission_GetEmission_Failure struct {
	// Status is the status of the response. Set false
	Status bool `json:"status"`
	// Message is a human-readable explanation of the status
	Message string `json:"message"`
	// StatusCode is the GoErr status
	StatusCode int `json:"status_code"`
}

// GetEmission
// @Summary GetEmission
// @Security bearerAuth
// @ID MultiacquiringEmission_GetEmission
// @Accept json
// @Param request body emptypb.Empty true "Empty"
// @Success 200 {object} MultiacquiringEmission_GetEmission_Success
// @Failure 401 {object} MultiacquiringEmission_GetEmission_Failure "goerrpkg.ErrorTypeAuthorization"
// @Failure 404 {object} MultiacquiringEmission_GetEmission_Failure "goerrpkg.ErrorTypeNotFound"
// @Failure 400 {object} MultiacquiringEmission_GetEmission_Failure "goerrpkg.ErrorTypeBadRequest"
// @Failure 500 {object} MultiacquiringEmission_GetEmission_Failure "goerrpkg.ErrorTypeUnexpectedError"
// @Failure 400 {object} MultiacquiringEmission_GetEmission_Failure "gin.ErrorTypeBind"
// @Failure 500 {object} MultiacquiringEmission_GetEmission_Failure "Undefined error"
// @Produce json
// @Router /processing.multiacquiring.multiacquiring_emission.MultiacquiringEmission/GetEmission [put]
// @tags MultiacquiringEmission
func (s *ginMultiacquiringEmissionServer) GetEmission(c *gin.Context) error {
	ctx, span := dog.CreateSpan(c.Request.Context(), "GinMultiacquiringEmissionServer_GetEmission")
	defer span.End()

	var request emptypb.Empty
	if err := c.ShouldBindJSON(&request); err != nil {
		return err
	}

	ctx = metadata.NewOutgoingContext(ctx, metadata.MD(c.Request.Header))

	response, err := s.client.GetEmission(ctx, &request)
	if err != nil {
		return err
	}

	c.JSON(http.StatusOK, &MultiacquiringEmission_GetEmission_Success{
		Status:     true,
		Message:    "Success",
		StatusCode: 0,
		Result:     response,
	})
	return nil
}

type MultiacquiringEmission_ConfirmEmission_Success struct {
	// Status is the status of the response. Set true
	Status bool `json:"status"`
	// Message is a human-readable explanation of the status
	Message string `json:"message"`
	// StatusCode is the GoErr status. Set 0
	StatusCode int `json:"status_code"`
	// Result is the result of the request
	Result *EmoneyResponse `json:"result"`
}

type MultiacquiringEmission_ConfirmEmission_Failure struct {
	// Status is the status of the response. Set false
	Status bool `json:"status"`
	// Message is a human-readable explanation of the status
	Message string `json:"message"`
	// StatusCode is the GoErr status
	StatusCode int `json:"status_code"`
}

// ConfirmEmission
// @Summary ConfirmEmission
// @Security bearerAuth
// @ID MultiacquiringEmission_ConfirmEmission
// @Accept json
// @Param request body EmoneyRequest true "EmoneyRequest"
// @Success 200 {object} MultiacquiringEmission_ConfirmEmission_Success
// @Failure 401 {object} MultiacquiringEmission_ConfirmEmission_Failure "goerrpkg.ErrorTypeAuthorization"
// @Failure 404 {object} MultiacquiringEmission_ConfirmEmission_Failure "goerrpkg.ErrorTypeNotFound"
// @Failure 400 {object} MultiacquiringEmission_ConfirmEmission_Failure "goerrpkg.ErrorTypeBadRequest"
// @Failure 500 {object} MultiacquiringEmission_ConfirmEmission_Failure "goerrpkg.ErrorTypeUnexpectedError"
// @Failure 400 {object} MultiacquiringEmission_ConfirmEmission_Failure "gin.ErrorTypeBind"
// @Failure 500 {object} MultiacquiringEmission_ConfirmEmission_Failure "Undefined error"
// @Produce json
// @Router /processing.multiacquiring.multiacquiring_emission.MultiacquiringEmission/ConfirmEmission [put]
// @tags MultiacquiringEmission
func (s *ginMultiacquiringEmissionServer) ConfirmEmission(c *gin.Context) error {
	ctx, span := dog.CreateSpan(c.Request.Context(), "GinMultiacquiringEmissionServer_ConfirmEmission")
	defer span.End()

	var request EmoneyRequest
	if err := c.ShouldBindJSON(&request); err != nil {
		return err
	}

	ctx = metadata.NewOutgoingContext(ctx, metadata.MD(c.Request.Header))

	response, err := s.client.ConfirmEmission(ctx, &request)
	if err != nil {
		return err
	}

	c.JSON(http.StatusOK, &MultiacquiringEmission_ConfirmEmission_Success{
		Status:     true,
		Message:    "Success",
		StatusCode: 0,
		Result:     response,
	})
	return nil
}
