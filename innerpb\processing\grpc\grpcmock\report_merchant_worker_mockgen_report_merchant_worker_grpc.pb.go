// Code generated by MockGen. DO NOT EDIT.
// Source: report_merchant_worker_grpc.pb.go

// Package grpcmock is a generated GoMock package.
package grpcmock

import (
	context "context"
	reflect "reflect"

	grpc "git.local/sensitive/innerpb/processing/grpc"
	gomock "github.com/golang/mock/gomock"
	grpc0 "google.golang.org/grpc"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
)

// MockReportMerchantWorkerClient is a mock of ReportMerchantWorkerClient interface.
type MockReportMerchantWorkerClient struct {
	ctrl     *gomock.Controller
	recorder *MockReportMerchantWorkerClientMockRecorder
}

// MockReportMerchantWorkerClientMockRecorder is the mock recorder for MockReportMerchantWorkerClient.
type MockReportMerchantWorkerClientMockRecorder struct {
	mock *MockReportMerchantWorkerClient
}

// NewMockReportMerchantWorkerClient creates a new mock instance.
func NewMockReportMerchantWorkerClient(ctrl *gomock.Controller) *MockReportMerchantWorkerClient {
	mock := &MockReportMerchantWorkerClient{ctrl: ctrl}
	mock.recorder = &MockReportMerchantWorkerClientMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockReportMerchantWorkerClient) EXPECT() *MockReportMerchantWorkerClientMockRecorder {
	return m.recorder
}

// ProcessReportScheduleByPeriodType mocks base method.
func (m *MockReportMerchantWorkerClient) ProcessReportScheduleByPeriodType(ctx context.Context, in *grpc.ProcessReportScheduleByPeriodTypeRequest, opts ...grpc0.CallOption) (*emptypb.Empty, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "ProcessReportScheduleByPeriodType", varargs...)
	ret0, _ := ret[0].(*emptypb.Empty)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ProcessReportScheduleByPeriodType indicates an expected call of ProcessReportScheduleByPeriodType.
func (mr *MockReportMerchantWorkerClientMockRecorder) ProcessReportScheduleByPeriodType(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ProcessReportScheduleByPeriodType", reflect.TypeOf((*MockReportMerchantWorkerClient)(nil).ProcessReportScheduleByPeriodType), varargs...)
}

// MockReportMerchantWorkerServer is a mock of ReportMerchantWorkerServer interface.
type MockReportMerchantWorkerServer struct {
	ctrl     *gomock.Controller
	recorder *MockReportMerchantWorkerServerMockRecorder
}

// MockReportMerchantWorkerServerMockRecorder is the mock recorder for MockReportMerchantWorkerServer.
type MockReportMerchantWorkerServerMockRecorder struct {
	mock *MockReportMerchantWorkerServer
}

// NewMockReportMerchantWorkerServer creates a new mock instance.
func NewMockReportMerchantWorkerServer(ctrl *gomock.Controller) *MockReportMerchantWorkerServer {
	mock := &MockReportMerchantWorkerServer{ctrl: ctrl}
	mock.recorder = &MockReportMerchantWorkerServerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockReportMerchantWorkerServer) EXPECT() *MockReportMerchantWorkerServerMockRecorder {
	return m.recorder
}

// ProcessReportScheduleByPeriodType mocks base method.
func (m *MockReportMerchantWorkerServer) ProcessReportScheduleByPeriodType(arg0 context.Context, arg1 *grpc.ProcessReportScheduleByPeriodTypeRequest) (*emptypb.Empty, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ProcessReportScheduleByPeriodType", arg0, arg1)
	ret0, _ := ret[0].(*emptypb.Empty)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ProcessReportScheduleByPeriodType indicates an expected call of ProcessReportScheduleByPeriodType.
func (mr *MockReportMerchantWorkerServerMockRecorder) ProcessReportScheduleByPeriodType(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ProcessReportScheduleByPeriodType", reflect.TypeOf((*MockReportMerchantWorkerServer)(nil).ProcessReportScheduleByPeriodType), arg0, arg1)
}

// mustEmbedUnimplementedReportMerchantWorkerServer mocks base method.
func (m *MockReportMerchantWorkerServer) mustEmbedUnimplementedReportMerchantWorkerServer() {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "mustEmbedUnimplementedReportMerchantWorkerServer")
}

// mustEmbedUnimplementedReportMerchantWorkerServer indicates an expected call of mustEmbedUnimplementedReportMerchantWorkerServer.
func (mr *MockReportMerchantWorkerServerMockRecorder) mustEmbedUnimplementedReportMerchantWorkerServer() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "mustEmbedUnimplementedReportMerchantWorkerServer", reflect.TypeOf((*MockReportMerchantWorkerServer)(nil).mustEmbedUnimplementedReportMerchantWorkerServer))
}

// MockUnsafeReportMerchantWorkerServer is a mock of UnsafeReportMerchantWorkerServer interface.
type MockUnsafeReportMerchantWorkerServer struct {
	ctrl     *gomock.Controller
	recorder *MockUnsafeReportMerchantWorkerServerMockRecorder
}

// MockUnsafeReportMerchantWorkerServerMockRecorder is the mock recorder for MockUnsafeReportMerchantWorkerServer.
type MockUnsafeReportMerchantWorkerServerMockRecorder struct {
	mock *MockUnsafeReportMerchantWorkerServer
}

// NewMockUnsafeReportMerchantWorkerServer creates a new mock instance.
func NewMockUnsafeReportMerchantWorkerServer(ctrl *gomock.Controller) *MockUnsafeReportMerchantWorkerServer {
	mock := &MockUnsafeReportMerchantWorkerServer{ctrl: ctrl}
	mock.recorder = &MockUnsafeReportMerchantWorkerServerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockUnsafeReportMerchantWorkerServer) EXPECT() *MockUnsafeReportMerchantWorkerServerMockRecorder {
	return m.recorder
}

// mustEmbedUnimplementedReportMerchantWorkerServer mocks base method.
func (m *MockUnsafeReportMerchantWorkerServer) mustEmbedUnimplementedReportMerchantWorkerServer() {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "mustEmbedUnimplementedReportMerchantWorkerServer")
}

// mustEmbedUnimplementedReportMerchantWorkerServer indicates an expected call of mustEmbedUnimplementedReportMerchantWorkerServer.
func (mr *MockUnsafeReportMerchantWorkerServerMockRecorder) mustEmbedUnimplementedReportMerchantWorkerServer() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "mustEmbedUnimplementedReportMerchantWorkerServer", reflect.TypeOf((*MockUnsafeReportMerchantWorkerServer)(nil).mustEmbedUnimplementedReportMerchantWorkerServer))
}
