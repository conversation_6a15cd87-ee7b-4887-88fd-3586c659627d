package domain

type Empty struct{}

type Agent struct {
	Agent     string
	Project   string
	<PERSON><PERSON><PERSON> string
}

type SmokeData[T any] struct {
	Agent    Agent
	Name     string
	Req      Req
	Expected Expected[T]
}

type Req struct {
	Username    string
	Amount      float64
	ServiceCode string
	ConfirmCode string
	AddData     map[string]interface{}
	ExternalID  string
}

type Expected[T any] struct {
	Message        string
	Data           T
	HttpStatusCode int
	Status         bool
}

type Response[T any] struct {
	Status     bool   `json:"status"`
	Message    string `json:"message"`
	StatusCode int    `json:"status_code"`
	Result     T      `json:"result"`
}
