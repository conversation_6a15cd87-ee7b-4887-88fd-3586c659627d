// Code generated by protoc-gen-go-mvp. DO NOT EDIT.
// versions:
// - protoc-gen-go-mvp v2.0.1
// created by mvp authors

package grpc

import (
	context "context"
	grpc "google.golang.org/grpc"
	metadata "google.golang.org/grpc/metadata"
)

func NewIamProjectServer(
	srv ProjectServer,
) ProjectServer {
	return &iamProjectServer{
		srv: srv,
	}
}

var _ ProjectServer = (*iamProjectServer)(nil)

type iamProjectServer struct {
	UnimplementedProjectServer

	srv ProjectServer
}

func (s *iamProjectServer) GetProjectsByMerchantID(
	ctx context.Context,
	req *ProjectsRequestV1,
) (
	*ProjectsResponseV1,
	error,
) {
	return s.srv.GetProjectsByMerchantID(ctx, req)
}

func (s *iamProjectServer) IsSendEmail(
	ctx context.Context,
	req *ProjectRequestV1,
) (
	*IsSendEmailResponseV1,
	error,
) {
	return s.srv.IsSendEmail(ctx, req)
}

func (s *iamProjectServer) GetProcessingProjectsByBUIDV1(
	ctx context.Context,
	req *GetProcessingProjectsByBUIDRequestV1,
) (
	*GetProcessingProjectsByBUIDResponseV1,
	error,
) {
	return s.srv.GetProcessingProjectsByBUIDV1(ctx, req)
}

func NewIamProjectClient(
	client ProjectClient,
) ProjectClient {
	return &iamProjectClient{
		client: client,
	}
}

type iamProjectClient struct {
	client ProjectClient
}

func (s *iamProjectClient) GetProjectsByMerchantID(
	ctx context.Context,
	req *ProjectsRequestV1,
	opts ...grpc.CallOption,
) (
	*ProjectsResponseV1,
	error,
) {
	md, _ := metadata.FromIncomingContext(ctx)
	return s.client.GetProjectsByMerchantID(metadata.NewOutgoingContext(ctx, md), req)
}

func (s *iamProjectClient) IsSendEmail(
	ctx context.Context,
	req *ProjectRequestV1,
	opts ...grpc.CallOption,
) (
	*IsSendEmailResponseV1,
	error,
) {
	md, _ := metadata.FromIncomingContext(ctx)
	return s.client.IsSendEmail(metadata.NewOutgoingContext(ctx, md), req)
}

func (s *iamProjectClient) GetProcessingProjectsByBUIDV1(
	ctx context.Context,
	req *GetProcessingProjectsByBUIDRequestV1,
	opts ...grpc.CallOption,
) (
	*GetProcessingProjectsByBUIDResponseV1,
	error,
) {
	md, _ := metadata.FromIncomingContext(ctx)
	return s.client.GetProcessingProjectsByBUIDV1(metadata.NewOutgoingContext(ctx, md), req)
}
