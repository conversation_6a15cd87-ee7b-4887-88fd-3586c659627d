package service

import (
	"context"
	"errors"
	"testing"

	"github.com/golang/mock/gomock"
	"github.com/stretchr/testify/require"

	"git.local/sensitive/innerpb/processing/goerr"
	"git.local/sensitive/pkg/middlewares"
	"git.local/sensitive/processing/acquirer/model"
	"git.local/sensitive/processing/acquirer/repository/database/databasemocks"
	"git.local/sensitive/processing/acquirer/schema"
)

func TestCreateCountry(t *testing.T) {
	type createOp struct {
		isCalled  bool
		input     *model.Country
		outputErr error
	}

	tests := []struct {
		name    string
		req     *schema.Country
		wantErr error
		create  createOp
	}{
		{
			name:    "nil request",
			req:     nil,
			wantErr: goerr.ErrParseErrorBody,
		},
		{
			name: "error when creating",
			req: &schema.Country{
				Name: "нарния",
			},
			create: createOp{
				isCalled:  true,
				input:     &model.Country{Name: "нарния"},
				outputErr: errors.New("some error"),
			},
			wantErr: errors.New("some error"),
		},
		{
			name: "success",
			req: &schema.Country{
				Name: "нарния",
			},
			create: createOp{
				isCalled:  true,
				input:     &model.Country{Name: "нарния"},
				outputErr: nil,
			},
			wantErr: nil,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctrl := gomock.NewController(t)
			defer ctrl.Finish()

			countryDBMock := databasemocks.NewMockCountrier(ctrl)

			countryService := NewCountryService(countryDBMock)

			if tt.create.isCalled {
				countryDBMock.EXPECT().Create(
					gomock.Any(),
					tt.create.input,
				).Return(tt.create.outputErr).Times(1)
			}

			err := countryService.Create(context.Background(), tt.req)
			if tt.wantErr != nil {
				require.Error(t, err)
				require.Equal(t, tt.wantErr, err)
			} else {
				require.NoError(t, err)
			}
		})
	}
}

func TestGetAllCountries(t *testing.T) {
	type getAllOp struct {
		input     *middlewares.PaginationInfo
		output    []*model.Country
		outputErr error
	}

	tests := []struct {
		name    string
		req     *middlewares.PaginationInfo
		want    []*model.Country
		wantErr error
		getAll  getAllOp
	}{
		{
			name: "error when getting",
			req: &middlewares.PaginationInfo{
				Page:       1,
				PerPage:    10,
				Pagination: true,
			},
			want:    nil,
			wantErr: errors.New("some error"),
			getAll: getAllOp{
				input: &middlewares.PaginationInfo{
					Page:       1,
					PerPage:    10,
					Pagination: true,
				},
				output:    nil,
				outputErr: errors.New("some error"),
			},
		},
		{
			name: "success",
			req: &middlewares.PaginationInfo{
				Page:       1,
				PerPage:    10,
				Pagination: true,
			},
			want: []*model.Country{
				{
					ID:   1,
					Name: "kz",
					Banks: []*model.Bank{
						{
							ID:    2,
							Name:  "some name",
							Bik:   "some bik",
							Swift: "some swift",
						},
					},
				},
			},
			wantErr: nil,
			getAll: getAllOp{
				input: &middlewares.PaginationInfo{
					Page:       1,
					PerPage:    10,
					Pagination: true,
				},
				output: []*model.Country{
					{
						ID:   1,
						Name: "kz",
						Banks: []*model.Bank{
							{
								ID:    2,
								Name:  "some name",
								Bik:   "some bik",
								Swift: "some swift",
							},
						},
					},
				},
				outputErr: nil,
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctrl := gomock.NewController(t)
			defer ctrl.Finish()

			countryDBMock := databasemocks.NewMockCountrier(ctrl)

			countryService := NewCountryService(countryDBMock)

			countryDBMock.EXPECT().GetAll(
				gomock.Any(),
				tt.getAll.input,
			).Return(tt.getAll.output, tt.getAll.outputErr).Times(1)

			resp, err := countryService.GetAll(context.Background(), tt.req)
			if tt.wantErr != nil {
				require.Error(t, err)
				require.Equal(t, tt.wantErr, err)
			} else {
				require.NoError(t, err)
			}

			require.Equal(t, tt.want, resp)
		})
	}
}

func TestUpdateCountry(t *testing.T) {
	type updateOp struct {
		isCalled  bool
		inputID   uint64
		input     *model.Country
		outputErr error
	}

	tests := []struct {
		name    string
		reqID   uint64
		req     *schema.Country
		wantErr error
		update  updateOp
	}{
		{
			name:  "nil request",
			req:   nil,
			reqID: 123,
			update: updateOp{
				isCalled: false,
			},
			wantErr: goerr.ErrParseErrorBody,
		},
		{
			name: "error when getting update",
			req: &schema.Country{
				Name: "нарния",
			},
			reqID: 123,
			update: updateOp{
				isCalled: true,
				inputID:  123,
				input: &model.Country{
					Name: "нарния",
				},
				outputErr: errors.New("some error"),
			},
			wantErr: errors.New("some error"),
		},
		{
			name: "success",
			req: &schema.Country{
				Name: "нарния",
			},
			reqID: 123,
			update: updateOp{
				isCalled: true,
				inputID:  123,
				input: &model.Country{
					Name: "нарния",
				},
				outputErr: nil,
			},
			wantErr: nil,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctrl := gomock.NewController(t)
			defer ctrl.Finish()

			countryDBMock := databasemocks.NewMockCountrier(ctrl)

			countryService := NewCountryService(countryDBMock)

			if tt.update.isCalled {
				countryDBMock.EXPECT().Update(
					gomock.Any(),
					tt.update.inputID,
					tt.update.input,
				).Return(tt.update.outputErr).Times(1)
			}

			err := countryService.Update(context.Background(), tt.reqID, tt.req)
			if tt.wantErr != nil {
				require.Error(t, err)
				require.Equal(t, tt.wantErr, err)
			} else {
				require.NoError(t, err)
			}
		})
	}
}

func TestDeleteCountry(t *testing.T) {
	type deleteOp struct {
		inputID   uint64
		outputErr error
	}

	tests := []struct {
		name    string
		req     uint64
		wantErr error
		delete  deleteOp
	}{
		{
			name:    "error when deleting",
			req:     123,
			wantErr: errors.New("deletion error"),
			delete: deleteOp{
				inputID:   123,
				outputErr: errors.New("deletion error"),
			},
		},
		{
			name:    "success",
			req:     123,
			wantErr: nil,
			delete: deleteOp{
				inputID:   123,
				outputErr: nil,
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {

			ctrl := gomock.NewController(t)
			defer ctrl.Finish()

			countryDBMock := databasemocks.NewMockCountrier(ctrl)

			countryService := NewCountryService(countryDBMock)

			countryDBMock.EXPECT().Delete(
				gomock.Any(),
				tt.delete.inputID,
			).Return(tt.delete.outputErr).Times(1)

			err := countryService.Delete(context.Background(), tt.req)
			if tt.wantErr != nil {
				require.Error(t, err)
				require.Equal(t, tt.wantErr, err)
			} else {
				require.NoError(t, err)
			}
		})
	}
}

func TestGetByIDCountry(t *testing.T) {
	type getByIDOp struct {
		inputID   uint64
		output    model.Country
		outputErr error
	}

	tests := []struct {
		name    string
		req     uint64
		want    model.Country
		wantErr error
		getByID getByIDOp
	}{
		{
			name:    "error when getting",
			req:     123,
			want:    model.Country{},
			wantErr: errors.New("some error"),
			getByID: getByIDOp{
				inputID:   123,
				output:    model.Country{},
				outputErr: errors.New("some error"),
			},
		},
		{
			name: "success",
			req:  123,
			want: model.Country{
				ID:   1,
				Name: "kz",
				Banks: []*model.Bank{
					{
						ID:    2,
						Name:  "some name",
						Bik:   "some bik",
						Swift: "some swift",
					},
				},
			},
			wantErr: nil,
			getByID: getByIDOp{
				inputID: 123,
				output: model.Country{
					ID:   1,
					Name: "kz",
					Banks: []*model.Bank{
						{
							ID:    2,
							Name:  "some name",
							Bik:   "some bik",
							Swift: "some swift",
						},
					},
				},
				outputErr: nil,
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctrl := gomock.NewController(t)
			defer ctrl.Finish()

			countryDBMock := databasemocks.NewMockCountrier(ctrl)

			countryDBMock.EXPECT().GetByID(
				gomock.Any(),
				tt.getByID.inputID,
			).Return(tt.getByID.output, tt.getByID.outputErr).Times(1)

			countryService := NewCountryService(countryDBMock)

			resp, err := countryService.GetByID(context.Background(), tt.req)
			if tt.wantErr != nil {
				require.Error(t, err)
				require.Equal(t, tt.wantErr, err)
			} else {
				require.NoError(t, err)
			}

			require.Equal(t, tt.want, resp)
		})
	}
}

func TestGetCountriesByName(t *testing.T) {
	type getByNameOp struct {
		inputName       string
		inputPagination *middlewares.PaginationInfo
		output          []*model.CountryBasic
		outputErr       error
	}

	tests := []struct {
		name          string
		reqName       string
		reqPagination *middlewares.PaginationInfo
		getByName     getByNameOp
		want          []*model.CountryBasic
		wantErr       error
	}{
		{
			name:    "error when getting",
			reqName: "my name is",
			reqPagination: &middlewares.PaginationInfo{
				Page:       5,
				Pagination: true,
				PerPage:    10,
			},
			want:    nil,
			wantErr: errors.New("some error"),
			getByName: getByNameOp{
				inputName: "my name is",
				inputPagination: &middlewares.PaginationInfo{
					Page:       5,
					Pagination: true,
					PerPage:    10,
				},
				output:    nil,
				outputErr: errors.New("some error"),
			},
		},
		{
			name:    "success",
			reqName: "my name is",
			reqPagination: &middlewares.PaginationInfo{
				Page:       5,
				Pagination: true,
				PerPage:    10,
			},
			want: []*model.CountryBasic{
				{
					ID:   123,
					Name: "asd",
				},
				{
					ID:   124,
					Name: "qwe",
				},
			},
			wantErr: nil,
			getByName: getByNameOp{
				inputName: "my name is",
				inputPagination: &middlewares.PaginationInfo{
					Page:       5,
					Pagination: true,
					PerPage:    10,
				},
				output: []*model.CountryBasic{
					{
						ID:   123,
						Name: "asd",
					},
					{
						ID:   124,
						Name: "qwe",
					},
				},
				outputErr: nil,
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctrl := gomock.NewController(t)
			defer ctrl.Finish()

			countryDBMock := databasemocks.NewMockCountrier(ctrl)

			countryService := NewCountryService(countryDBMock)

			countryDBMock.EXPECT().GetByName(
				gomock.Any(),
				tt.getByName.inputName,
				tt.getByName.inputPagination,
			).Return(tt.getByName.output, tt.getByName.outputErr).Times(1)

			resp, err := countryService.GetCountriesByName(context.Background(), tt.reqName, tt.reqPagination)
			if tt.wantErr != nil {
				require.Error(t, err)
				require.Equal(t, tt.wantErr, err)
			} else {
				require.NoError(t, err)
			}

			require.Equal(t, tt.want, resp)
		})
	}
}
