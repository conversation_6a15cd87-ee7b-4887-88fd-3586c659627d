package main

import (
	"google.golang.org/protobuf/compiler/protogen"
)

func init() {
	RegisterGenerator(&MessageExtendGenerator{})
}

type MessageExtendGenerator struct{}

func (generator *MessageExtendGenerator) ProtoGenGenerate(gen *protogen.Plugin) {
	for _, file := range gen.Files {
		if !file.Generate {
			continue
		}

		if len(file.Messages) == 0 {
			return
		}

		g := gen.NewGeneratedFile(
			file.GeneratedFilenamePrefix+V2_MESSAGE_EXTEND_SUFFIX,
			file.GoImportPath,
		)
		HeaderPrint(g, string(file.GoPackageName))

		for _, message := range file.Messages {
			g.P("func (m *", message.GoIdent, ") UnmarshalJSON(in []byte) error {")
			g.P("val := new(", message.GoIdent, ")")
			g.P("if err :=  (&", protoJsonPackageUnmarshalOptions, "{")
			g.P("DiscardUnknown: true,")
			g.P("}).Unmarshal(in, val); err != nil {")
			g.P("return err")
			g.P("}")
			g.P()
			g.P("*m = *val")
			g.P("return nil")
			g.P("}")
			g.P()

			g.P("func (m *", message.GoIdent, ") MarshalJSON() ([]byte, error) {")
			g.P("return (&", protoJsonPackageMarshalOptions, "{")
			g.P("UseProtoNames: true,")
			g.P("}).Marshal(m)")
			g.P("}")
			g.P()
		}
	}
}
