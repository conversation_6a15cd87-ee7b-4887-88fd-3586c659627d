// Code generated by MockGen. DO NOT EDIT.
// Source: database.go

// Package databasemocks is a generated GoMock package.
package databasemocks

import (
	context "context"
	reflect "reflect"
	time "time"

	model "git.local/sensitive/processing/account/model"
	schema "git.local/sensitive/processing/account/schema"
	gomock "github.com/golang/mock/gomock"
	uuid "github.com/google/uuid"
)

// MockTransferer is a mock of Transferer interface.
type MockTransferer struct {
	ctrl     *gomock.Controller
	recorder *MockTransfererMockRecorder
}

// MockTransfererMockRecorder is the mock recorder for MockTransferer.
type MockTransfererMockRecorder struct {
	mock *MockTransferer
}

// NewMockTransferer creates a new mock instance.
func NewMockTransferer(ctrl *gomock.Controller) *MockTransferer {
	mock := &MockTransferer{ctrl: ctrl}
	mock.recorder = &MockTransfererMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockTransferer) EXPECT() *MockTransfererMockRecorder {
	return m.recorder
}

// ExtractFailOrCanceledTransfersByForeignIDs mocks base method.
func (m *MockTransferer) ExtractFailOrCanceledTransfersByForeignIDs(ctx context.Context, foreignIDs []uuid.UUID) ([]model.Transfer, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ExtractFailOrCanceledTransfersByForeignIDs", ctx, foreignIDs)
	ret0, _ := ret[0].([]model.Transfer)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ExtractFailOrCanceledTransfersByForeignIDs indicates an expected call of ExtractFailOrCanceledTransfersByForeignIDs.
func (mr *MockTransfererMockRecorder) ExtractFailOrCanceledTransfersByForeignIDs(ctx, foreignIDs interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ExtractFailOrCanceledTransfersByForeignIDs", reflect.TypeOf((*MockTransferer)(nil).ExtractFailOrCanceledTransfersByForeignIDs), ctx, foreignIDs)
}

// ExtractSuccessTransfersByForeignIDs mocks base method.
func (m *MockTransferer) ExtractSuccessTransfersByForeignIDs(ctx context.Context, foreignIDs []uuid.UUID) ([]model.Transfer, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ExtractSuccessTransfersByForeignIDs", ctx, foreignIDs)
	ret0, _ := ret[0].([]model.Transfer)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ExtractSuccessTransfersByForeignIDs indicates an expected call of ExtractSuccessTransfersByForeignIDs.
func (mr *MockTransfererMockRecorder) ExtractSuccessTransfersByForeignIDs(ctx, foreignIDs interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ExtractSuccessTransfersByForeignIDs", reflect.TypeOf((*MockTransferer)(nil).ExtractSuccessTransfersByForeignIDs), ctx, foreignIDs)
}

// GetByFilters mocks base method.
func (m *MockTransferer) GetByFilters(ctx context.Context, filters schema.TransferFilters) ([]model.Transfer, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetByFilters", ctx, filters)
	ret0, _ := ret[0].([]model.Transfer)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetByFilters indicates an expected call of GetByFilters.
func (mr *MockTransfererMockRecorder) GetByFilters(ctx, filters interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetByFilters", reflect.TypeOf((*MockTransferer)(nil).GetByFilters), ctx, filters)
}

// GetByForeignID mocks base method.
func (m *MockTransferer) GetByForeignID(ctx context.Context, foreignID uuid.UUID) (*model.Transfer, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetByForeignID", ctx, foreignID)
	ret0, _ := ret[0].(*model.Transfer)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetByForeignID indicates an expected call of GetByForeignID.
func (mr *MockTransfererMockRecorder) GetByForeignID(ctx, foreignID interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetByForeignID", reflect.TypeOf((*MockTransferer)(nil).GetByForeignID), ctx, foreignID)
}

// GetByForeignIDs mocks base method.
func (m *MockTransferer) GetByForeignIDs(ctx context.Context, foreignIDs []uuid.UUID) ([]model.Transfer, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetByForeignIDs", ctx, foreignIDs)
	ret0, _ := ret[0].([]model.Transfer)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetByForeignIDs indicates an expected call of GetByForeignIDs.
func (mr *MockTransfererMockRecorder) GetByForeignIDs(ctx, foreignIDs interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetByForeignIDs", reflect.TypeOf((*MockTransferer)(nil).GetByForeignIDs), ctx, foreignIDs)
}

// GetByID mocks base method.
func (m *MockTransferer) GetByID(ctx context.Context, id uint64) (*model.Transfer, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetByID", ctx, id)
	ret0, _ := ret[0].(*model.Transfer)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetByID indicates an expected call of GetByID.
func (mr *MockTransfererMockRecorder) GetByID(ctx, id interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetByID", reflect.TypeOf((*MockTransferer)(nil).GetByID), ctx, id)
}

// GetByIDs mocks base method.
func (m *MockTransferer) GetByIDs(ctx context.Context, ids []uint64) ([]model.Transfer, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetByIDs", ctx, ids)
	ret0, _ := ret[0].([]model.Transfer)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetByIDs indicates an expected call of GetByIDs.
func (mr *MockTransfererMockRecorder) GetByIDs(ctx, ids interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetByIDs", reflect.TypeOf((*MockTransferer)(nil).GetByIDs), ctx, ids)
}

// GetByMerchantID mocks base method.
func (m *MockTransferer) GetByMerchantID(ctx context.Context, merchantID uint64) ([]model.Transfer, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetByMerchantID", ctx, merchantID)
	ret0, _ := ret[0].([]model.Transfer)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetByMerchantID indicates an expected call of GetByMerchantID.
func (mr *MockTransfererMockRecorder) GetByMerchantID(ctx, merchantID interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetByMerchantID", reflect.TypeOf((*MockTransferer)(nil).GetByMerchantID), ctx, merchantID)
}

// GetByPeriod mocks base method.
func (m *MockTransferer) GetByPeriod(ctx context.Context, createdDateFrom, createdDateTo time.Time, statusID uint64) ([]model.Transfer, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetByPeriod", ctx, createdDateFrom, createdDateTo, statusID)
	ret0, _ := ret[0].([]model.Transfer)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetByPeriod indicates an expected call of GetByPeriod.
func (mr *MockTransfererMockRecorder) GetByPeriod(ctx, createdDateFrom, createdDateTo, statusID interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetByPeriod", reflect.TypeOf((*MockTransferer)(nil).GetByPeriod), ctx, createdDateFrom, createdDateTo, statusID)
}

// GetByProjectID mocks base method.
func (m *MockTransferer) GetByProjectID(ctx context.Context, projectID uint64) ([]model.Transfer, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetByProjectID", ctx, projectID)
	ret0, _ := ret[0].([]model.Transfer)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetByProjectID indicates an expected call of GetByProjectID.
func (mr *MockTransfererMockRecorder) GetByProjectID(ctx, projectID interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetByProjectID", reflect.TypeOf((*MockTransferer)(nil).GetByProjectID), ctx, projectID)
}

// MockTransferManager is a mock of TransferManager interface.
type MockTransferManager struct {
	ctrl     *gomock.Controller
	recorder *MockTransferManagerMockRecorder
}

// MockTransferManagerMockRecorder is the mock recorder for MockTransferManager.
type MockTransferManagerMockRecorder struct {
	mock *MockTransferManager
}

// NewMockTransferManager creates a new mock instance.
func NewMockTransferManager(ctrl *gomock.Controller) *MockTransferManager {
	mock := &MockTransferManager{ctrl: ctrl}
	mock.recorder = &MockTransferManagerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockTransferManager) EXPECT() *MockTransferManagerMockRecorder {
	return m.recorder
}

// Create mocks base method.
func (m *MockTransferManager) Create(ctx context.Context, transfer model.Transfer) (*model.Transfer, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Create", ctx, transfer)
	ret0, _ := ret[0].(*model.Transfer)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Create indicates an expected call of Create.
func (mr *MockTransferManagerMockRecorder) Create(ctx, transfer interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Create", reflect.TypeOf((*MockTransferManager)(nil).Create), ctx, transfer)
}

// UpdateStatus mocks base method.
func (m *MockTransferManager) UpdateStatus(ctx context.Context, transferID, statusID uint64, finishedAt *time.Time) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateStatus", ctx, transferID, statusID, finishedAt)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpdateStatus indicates an expected call of UpdateStatus.
func (mr *MockTransferManagerMockRecorder) UpdateStatus(ctx, transferID, statusID, finishedAt interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateStatus", reflect.TypeOf((*MockTransferManager)(nil).UpdateStatus), ctx, transferID, statusID, finishedAt)
}

// UpdateTransfer mocks base method.
func (m *MockTransferManager) UpdateTransfer(ctx context.Context, transfer *schema.CreateTransferResponse) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateTransfer", ctx, transfer)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpdateTransfer indicates an expected call of UpdateTransfer.
func (mr *MockTransferManagerMockRecorder) UpdateTransfer(ctx, transfer interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateTransfer", reflect.TypeOf((*MockTransferManager)(nil).UpdateTransfer), ctx, transfer)
}

// MockTransferStatuser is a mock of TransferStatuser interface.
type MockTransferStatuser struct {
	ctrl     *gomock.Controller
	recorder *MockTransferStatuserMockRecorder
}

// MockTransferStatuserMockRecorder is the mock recorder for MockTransferStatuser.
type MockTransferStatuserMockRecorder struct {
	mock *MockTransferStatuser
}

// NewMockTransferStatuser creates a new mock instance.
func NewMockTransferStatuser(ctrl *gomock.Controller) *MockTransferStatuser {
	mock := &MockTransferStatuser{ctrl: ctrl}
	mock.recorder = &MockTransferStatuserMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockTransferStatuser) EXPECT() *MockTransferStatuserMockRecorder {
	return m.recorder
}

// AsCodeMap mocks base method.
func (m *MockTransferStatuser) AsCodeMap(ctx context.Context) (map[model.TransferStatusCode]uint64, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AsCodeMap", ctx)
	ret0, _ := ret[0].(map[model.TransferStatusCode]uint64)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// AsCodeMap indicates an expected call of AsCodeMap.
func (mr *MockTransferStatuserMockRecorder) AsCodeMap(ctx interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AsCodeMap", reflect.TypeOf((*MockTransferStatuser)(nil).AsCodeMap), ctx)
}

// GetAll mocks base method.
func (m *MockTransferStatuser) GetAll(ctx context.Context) ([]model.TransferStatus, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAll", ctx)
	ret0, _ := ret[0].([]model.TransferStatus)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAll indicates an expected call of GetAll.
func (mr *MockTransferStatuserMockRecorder) GetAll(ctx interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAll", reflect.TypeOf((*MockTransferStatuser)(nil).GetAll), ctx)
}

// GetByCode mocks base method.
func (m *MockTransferStatuser) GetByCode(ctx context.Context, code model.TransferStatusCode) (*model.TransferStatus, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetByCode", ctx, code)
	ret0, _ := ret[0].(*model.TransferStatus)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetByCode indicates an expected call of GetByCode.
func (mr *MockTransferStatuserMockRecorder) GetByCode(ctx, code interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetByCode", reflect.TypeOf((*MockTransferStatuser)(nil).GetByCode), ctx, code)
}

// GetByID mocks base method.
func (m *MockTransferStatuser) GetByID(ctx context.Context, id uint64) (*model.TransferStatus, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetByID", ctx, id)
	ret0, _ := ret[0].(*model.TransferStatus)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetByID indicates an expected call of GetByID.
func (mr *MockTransferStatuserMockRecorder) GetByID(ctx, id interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetByID", reflect.TypeOf((*MockTransferStatuser)(nil).GetByID), ctx, id)
}

// GetSuccess mocks base method.
func (m *MockTransferStatuser) GetSuccess(ctx context.Context) (*model.TransferStatus, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetSuccess", ctx)
	ret0, _ := ret[0].(*model.TransferStatus)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetSuccess indicates an expected call of GetSuccess.
func (mr *MockTransferStatuserMockRecorder) GetSuccess(ctx interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetSuccess", reflect.TypeOf((*MockTransferStatuser)(nil).GetSuccess), ctx)
}

// MockAccountInformer is a mock of AccountInformer interface.
type MockAccountInformer struct {
	ctrl     *gomock.Controller
	recorder *MockAccountInformerMockRecorder
}

// MockAccountInformerMockRecorder is the mock recorder for MockAccountInformer.
type MockAccountInformerMockRecorder struct {
	mock *MockAccountInformer
}

// NewMockAccountInformer creates a new mock instance.
func NewMockAccountInformer(ctrl *gomock.Controller) *MockAccountInformer {
	mock := &MockAccountInformer{ctrl: ctrl}
	mock.recorder = &MockAccountInformerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockAccountInformer) EXPECT() *MockAccountInformerMockRecorder {
	return m.recorder
}

// GetAll mocks base method.
func (m *MockAccountInformer) GetAll(ctx context.Context) ([]model.Account, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAll", ctx)
	ret0, _ := ret[0].([]model.Account)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAll indicates an expected call of GetAll.
func (mr *MockAccountInformerMockRecorder) GetAll(ctx interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAll", reflect.TypeOf((*MockAccountInformer)(nil).GetAll), ctx)
}

// GetAllUniqueBankCodes mocks base method.
func (m *MockAccountInformer) GetAllUniqueBankCodes(ctx context.Context) ([]string, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAllUniqueBankCodes", ctx)
	ret0, _ := ret[0].([]string)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAllUniqueBankCodes indicates an expected call of GetAllUniqueBankCodes.
func (mr *MockAccountInformerMockRecorder) GetAllUniqueBankCodes(ctx interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAllUniqueBankCodes", reflect.TypeOf((*MockAccountInformer)(nil).GetAllUniqueBankCodes), ctx)
}

// GetByBankID mocks base method.
func (m *MockAccountInformer) GetByBankID(ctx context.Context, bankID uint64) ([]model.Account, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetByBankID", ctx, bankID)
	ret0, _ := ret[0].([]model.Account)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetByBankID indicates an expected call of GetByBankID.
func (mr *MockAccountInformerMockRecorder) GetByBankID(ctx, bankID interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetByBankID", reflect.TypeOf((*MockAccountInformer)(nil).GetByBankID), ctx, bankID)
}

// GetById mocks base method.
func (m *MockAccountInformer) GetById(ctx context.Context, id uint64) (*model.Account, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetById", ctx, id)
	ret0, _ := ret[0].(*model.Account)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetById indicates an expected call of GetById.
func (mr *MockAccountInformerMockRecorder) GetById(ctx, id interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetById", reflect.TypeOf((*MockAccountInformer)(nil).GetById), ctx, id)
}

// GetByNumber mocks base method.
func (m *MockAccountInformer) GetByNumber(ctx context.Context, number string) (*model.Account, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetByNumber", ctx, number)
	ret0, _ := ret[0].(*model.Account)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetByNumber indicates an expected call of GetByNumber.
func (mr *MockAccountInformerMockRecorder) GetByNumber(ctx, number interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetByNumber", reflect.TypeOf((*MockAccountInformer)(nil).GetByNumber), ctx, number)
}

// MockAccountUpdater is a mock of AccountUpdater interface.
type MockAccountUpdater struct {
	ctrl     *gomock.Controller
	recorder *MockAccountUpdaterMockRecorder
}

// MockAccountUpdaterMockRecorder is the mock recorder for MockAccountUpdater.
type MockAccountUpdaterMockRecorder struct {
	mock *MockAccountUpdater
}

// NewMockAccountUpdater creates a new mock instance.
func NewMockAccountUpdater(ctrl *gomock.Controller) *MockAccountUpdater {
	mock := &MockAccountUpdater{ctrl: ctrl}
	mock.recorder = &MockAccountUpdaterMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockAccountUpdater) EXPECT() *MockAccountUpdaterMockRecorder {
	return m.recorder
}

// UpdateAccountConfig mocks base method.
func (m *MockAccountUpdater) UpdateAccountConfig(ctx context.Context, id uint64, encryptedConfig string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateAccountConfig", ctx, id, encryptedConfig)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpdateAccountConfig indicates an expected call of UpdateAccountConfig.
func (mr *MockAccountUpdaterMockRecorder) UpdateAccountConfig(ctx, id, encryptedConfig interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateAccountConfig", reflect.TypeOf((*MockAccountUpdater)(nil).UpdateAccountConfig), ctx, id, encryptedConfig)
}

// MockAccountStatementer is a mock of AccountStatementer interface.
type MockAccountStatementer struct {
	ctrl     *gomock.Controller
	recorder *MockAccountStatementerMockRecorder
}

// MockAccountStatementerMockRecorder is the mock recorder for MockAccountStatementer.
type MockAccountStatementerMockRecorder struct {
	mock *MockAccountStatementer
}

// NewMockAccountStatementer creates a new mock instance.
func NewMockAccountStatementer(ctrl *gomock.Controller) *MockAccountStatementer {
	mock := &MockAccountStatementer{ctrl: ctrl}
	mock.recorder = &MockAccountStatementerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockAccountStatementer) EXPECT() *MockAccountStatementerMockRecorder {
	return m.recorder
}

// Create mocks base method.
func (m *MockAccountStatementer) Create(ctx context.Context, accountStatement *model.AccountStatement) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Create", ctx, accountStatement)
	ret0, _ := ret[0].(error)
	return ret0
}

// Create indicates an expected call of Create.
func (mr *MockAccountStatementerMockRecorder) Create(ctx, accountStatement interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Create", reflect.TypeOf((*MockAccountStatementer)(nil).Create), ctx, accountStatement)
}

// MockAccountScheduler is a mock of AccountScheduler interface.
type MockAccountScheduler struct {
	ctrl     *gomock.Controller
	recorder *MockAccountSchedulerMockRecorder
}

// MockAccountSchedulerMockRecorder is the mock recorder for MockAccountScheduler.
type MockAccountSchedulerMockRecorder struct {
	mock *MockAccountScheduler
}

// NewMockAccountScheduler creates a new mock instance.
func NewMockAccountScheduler(ctrl *gomock.Controller) *MockAccountScheduler {
	mock := &MockAccountScheduler{ctrl: ctrl}
	mock.recorder = &MockAccountSchedulerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockAccountScheduler) EXPECT() *MockAccountSchedulerMockRecorder {
	return m.recorder
}

// GetAll mocks base method.
func (m *MockAccountScheduler) GetAll(ctx context.Context) ([]model.AccountSchedule, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAll", ctx)
	ret0, _ := ret[0].([]model.AccountSchedule)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAll indicates an expected call of GetAll.
func (mr *MockAccountSchedulerMockRecorder) GetAll(ctx interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAll", reflect.TypeOf((*MockAccountScheduler)(nil).GetAll), ctx)
}

// GetByCode mocks base method.
func (m *MockAccountScheduler) GetByCode(ctx context.Context, code string) (model.AccountSchedule, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetByCode", ctx, code)
	ret0, _ := ret[0].(model.AccountSchedule)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetByCode indicates an expected call of GetByCode.
func (mr *MockAccountSchedulerMockRecorder) GetByCode(ctx, code interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetByCode", reflect.TypeOf((*MockAccountScheduler)(nil).GetByCode), ctx, code)
}

// MockAccountTyper is a mock of AccountTyper interface.
type MockAccountTyper struct {
	ctrl     *gomock.Controller
	recorder *MockAccountTyperMockRecorder
}

// MockAccountTyperMockRecorder is the mock recorder for MockAccountTyper.
type MockAccountTyperMockRecorder struct {
	mock *MockAccountTyper
}

// NewMockAccountTyper creates a new mock instance.
func NewMockAccountTyper(ctrl *gomock.Controller) *MockAccountTyper {
	mock := &MockAccountTyper{ctrl: ctrl}
	mock.recorder = &MockAccountTyperMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockAccountTyper) EXPECT() *MockAccountTyperMockRecorder {
	return m.recorder
}

// GetByID mocks base method.
func (m *MockAccountTyper) GetByID(ctx context.Context, id uint64) (*model.AccountType, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetByID", ctx, id)
	ret0, _ := ret[0].(*model.AccountType)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetByID indicates an expected call of GetByID.
func (mr *MockAccountTyperMockRecorder) GetByID(ctx, id interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetByID", reflect.TypeOf((*MockAccountTyper)(nil).GetByID), ctx, id)
}

// MockTransferTyper is a mock of TransferTyper interface.
type MockTransferTyper struct {
	ctrl     *gomock.Controller
	recorder *MockTransferTyperMockRecorder
}

// MockTransferTyperMockRecorder is the mock recorder for MockTransferTyper.
type MockTransferTyperMockRecorder struct {
	mock *MockTransferTyper
}

// NewMockTransferTyper creates a new mock instance.
func NewMockTransferTyper(ctrl *gomock.Controller) *MockTransferTyper {
	mock := &MockTransferTyper{ctrl: ctrl}
	mock.recorder = &MockTransferTyperMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockTransferTyper) EXPECT() *MockTransferTyperMockRecorder {
	return m.recorder
}

// GetAll mocks base method.
func (m *MockTransferTyper) GetAll(ctx context.Context) ([]model.TransferType, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAll", ctx)
	ret0, _ := ret[0].([]model.TransferType)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAll indicates an expected call of GetAll.
func (mr *MockTransferTyperMockRecorder) GetAll(ctx interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAll", reflect.TypeOf((*MockTransferTyper)(nil).GetAll), ctx)
}

// GetByCode mocks base method.
func (m *MockTransferTyper) GetByCode(ctx context.Context, code model.TransferTypeCode) (*model.TransferType, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetByCode", ctx, code)
	ret0, _ := ret[0].(*model.TransferType)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetByCode indicates an expected call of GetByCode.
func (mr *MockTransferTyperMockRecorder) GetByCode(ctx, code interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetByCode", reflect.TypeOf((*MockTransferTyper)(nil).GetByCode), ctx, code)
}

// GetByID mocks base method.
func (m *MockTransferTyper) GetByID(ctx context.Context, id uint64) (*model.TransferType, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetByID", ctx, id)
	ret0, _ := ret[0].(*model.TransferType)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetByID indicates an expected call of GetByID.
func (mr *MockTransferTyperMockRecorder) GetByID(ctx, id interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetByID", reflect.TypeOf((*MockTransferTyper)(nil).GetByID), ctx, id)
}

// MockInTransferParsingScheduler is a mock of InTransferParsingScheduler interface.
type MockInTransferParsingScheduler struct {
	ctrl     *gomock.Controller
	recorder *MockInTransferParsingSchedulerMockRecorder
}

// MockInTransferParsingSchedulerMockRecorder is the mock recorder for MockInTransferParsingScheduler.
type MockInTransferParsingSchedulerMockRecorder struct {
	mock *MockInTransferParsingScheduler
}

// NewMockInTransferParsingScheduler creates a new mock instance.
func NewMockInTransferParsingScheduler(ctrl *gomock.Controller) *MockInTransferParsingScheduler {
	mock := &MockInTransferParsingScheduler{ctrl: ctrl}
	mock.recorder = &MockInTransferParsingSchedulerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockInTransferParsingScheduler) EXPECT() *MockInTransferParsingSchedulerMockRecorder {
	return m.recorder
}

// GetAll mocks base method.
func (m *MockInTransferParsingScheduler) GetAll(ctx context.Context) ([]model.InTransferParsingSchedule, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAll", ctx)
	ret0, _ := ret[0].([]model.InTransferParsingSchedule)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAll indicates an expected call of GetAll.
func (mr *MockInTransferParsingSchedulerMockRecorder) GetAll(ctx interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAll", reflect.TypeOf((*MockInTransferParsingScheduler)(nil).GetAll), ctx)
}

// MockProcessedOrderer is a mock of ProcessedOrderer interface.
type MockProcessedOrderer struct {
	ctrl     *gomock.Controller
	recorder *MockProcessedOrdererMockRecorder
}

// MockProcessedOrdererMockRecorder is the mock recorder for MockProcessedOrderer.
type MockProcessedOrdererMockRecorder struct {
	mock *MockProcessedOrderer
}

// NewMockProcessedOrderer creates a new mock instance.
func NewMockProcessedOrderer(ctrl *gomock.Controller) *MockProcessedOrderer {
	mock := &MockProcessedOrderer{ctrl: ctrl}
	mock.recorder = &MockProcessedOrdererMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockProcessedOrderer) EXPECT() *MockProcessedOrdererMockRecorder {
	return m.recorder
}

// Create mocks base method.
func (m *MockProcessedOrderer) Create(ctx context.Context, processedOrders model.ProcessedOrder) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Create", ctx, processedOrders)
	ret0, _ := ret[0].(error)
	return ret0
}

// Create indicates an expected call of Create.
func (mr *MockProcessedOrdererMockRecorder) Create(ctx, processedOrders interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Create", reflect.TypeOf((*MockProcessedOrderer)(nil).Create), ctx, processedOrders)
}

// GetByStatusID mocks base method.
func (m *MockProcessedOrderer) GetByStatusID(ctx context.Context, statusID uint64) ([]model.ProcessedOrder, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetByStatusID", ctx, statusID)
	ret0, _ := ret[0].([]model.ProcessedOrder)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetByStatusID indicates an expected call of GetByStatusID.
func (mr *MockProcessedOrdererMockRecorder) GetByStatusID(ctx, statusID interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetByStatusID", reflect.TypeOf((*MockProcessedOrderer)(nil).GetByStatusID), ctx, statusID)
}

// UpdateStatusByIDs mocks base method.
func (m *MockProcessedOrderer) UpdateStatusByIDs(ctx context.Context, orderIDs []string, statusID uint64) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateStatusByIDs", ctx, orderIDs, statusID)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpdateStatusByIDs indicates an expected call of UpdateStatusByIDs.
func (mr *MockProcessedOrdererMockRecorder) UpdateStatusByIDs(ctx, orderIDs, statusID interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateStatusByIDs", reflect.TypeOf((*MockProcessedOrderer)(nil).UpdateStatusByIDs), ctx, orderIDs, statusID)
}

// MockProcessedOrderStatuser is a mock of ProcessedOrderStatuser interface.
type MockProcessedOrderStatuser struct {
	ctrl     *gomock.Controller
	recorder *MockProcessedOrderStatuserMockRecorder
}

// MockProcessedOrderStatuserMockRecorder is the mock recorder for MockProcessedOrderStatuser.
type MockProcessedOrderStatuserMockRecorder struct {
	mock *MockProcessedOrderStatuser
}

// NewMockProcessedOrderStatuser creates a new mock instance.
func NewMockProcessedOrderStatuser(ctrl *gomock.Controller) *MockProcessedOrderStatuser {
	mock := &MockProcessedOrderStatuser{ctrl: ctrl}
	mock.recorder = &MockProcessedOrderStatuserMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockProcessedOrderStatuser) EXPECT() *MockProcessedOrderStatuserMockRecorder {
	return m.recorder
}

// GetByCode mocks base method.
func (m *MockProcessedOrderStatuser) GetByCode(ctx context.Context, code model.ProcessedOrderStatusCode) (*model.ProcessedOrderStatus, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetByCode", ctx, code)
	ret0, _ := ret[0].(*model.ProcessedOrderStatus)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetByCode indicates an expected call of GetByCode.
func (mr *MockProcessedOrderStatuserMockRecorder) GetByCode(ctx, code interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetByCode", reflect.TypeOf((*MockProcessedOrderStatuser)(nil).GetByCode), ctx, code)
}

// GetByID mocks base method.
func (m *MockProcessedOrderStatuser) GetByID(ctx context.Context, id uint64) (*model.ProcessedOrderStatus, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetByID", ctx, id)
	ret0, _ := ret[0].(*model.ProcessedOrderStatus)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetByID indicates an expected call of GetByID.
func (mr *MockProcessedOrderStatuserMockRecorder) GetByID(ctx, id interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetByID", reflect.TypeOf((*MockProcessedOrderStatuser)(nil).GetByID), ctx, id)
}

// MockAccountBalanceHistorier is a mock of AccountBalanceHistorier interface.
type MockAccountBalanceHistorier struct {
	ctrl     *gomock.Controller
	recorder *MockAccountBalanceHistorierMockRecorder
}

// MockAccountBalanceHistorierMockRecorder is the mock recorder for MockAccountBalanceHistorier.
type MockAccountBalanceHistorierMockRecorder struct {
	mock *MockAccountBalanceHistorier
}

// NewMockAccountBalanceHistorier creates a new mock instance.
func NewMockAccountBalanceHistorier(ctrl *gomock.Controller) *MockAccountBalanceHistorier {
	mock := &MockAccountBalanceHistorier{ctrl: ctrl}
	mock.recorder = &MockAccountBalanceHistorierMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockAccountBalanceHistorier) EXPECT() *MockAccountBalanceHistorierMockRecorder {
	return m.recorder
}

// Create mocks base method.
func (m *MockAccountBalanceHistorier) Create(ctx context.Context, balanceHistory *model.AccountBalanceHistory) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Create", ctx, balanceHistory)
	ret0, _ := ret[0].(error)
	return ret0
}

// Create indicates an expected call of Create.
func (mr *MockAccountBalanceHistorierMockRecorder) Create(ctx, balanceHistory interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Create", reflect.TypeOf((*MockAccountBalanceHistorier)(nil).Create), ctx, balanceHistory)
}
