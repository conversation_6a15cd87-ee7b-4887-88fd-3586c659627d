// Code generated by protoc-gen-go-mvp. DO NOT EDIT.
// versions:
// - protoc-gen-go-mvp v2.0.1
// created by mvp authors

package grpc

import (
	dog "git.local/sensitive/sdk/dog"
	gin "github.com/gin-gonic/gin"
	metadata "google.golang.org/grpc/metadata"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
	http "net/http"
)

func RegisterGinBCCAccountRouterGroup(
	in *gin.RouterGroup,
	handler func(func(c *gin.Context) error) gin.HandlerFunc,
) error {
	service, err := NewGinBCCAccountService()
	if err != nil {
		return err
	}

	routerGroup := in.Group("processing.bcc_account.bcc_account.BCCAccount")
	routerGroup.PUT("/MakeMerchantCheck", handler(service.MakeMerchantCheck))
	routerGroup.PUT("/MakeTransfer", handler(service.MakeTransfer))
	routerGroup.PUT("/AcceptTransfer", handler(service.AcceptTransfer))
	routerGroup.PUT("/DeclineTransfer", handler(service.DeclineTransfer))
	routerGroup.PUT("/RedoTransfer", handler(service.RedoTransfer))
	routerGroup.PUT("/GetTransferDetails", handler(service.GetTransferDetails))
	routerGroup.PUT("/GetTransfersList", handler(service.GetTransfersList))
	routerGroup.PUT("/GetAccountBalance", handler(service.GetAccountBalance))
	routerGroup.PUT("/GetAccountStatement", handler(service.GetAccountStatement))
	routerGroup.PUT("/GetAccountIdentifier", handler(service.GetAccountIdentifier))
	return nil
}

func NewGinBCCAccountService() (GinBCCAccountServer, error) {
	client, err := NewPreparedBCCAccountClient()
	if err != nil {
		return nil, err
	}

	return &ginBCCAccountServer{
		client: NewLoggedBCCAccountClient(
			NewIamBCCAccountClient(client),
		),
	}, nil
}

//go:generate mockgen -destination=./grpcmock/bcc_account.gin.pb.go -package=grpcmock -source=bcc_account.gin.pb.go GinBCCAccountServer
type GinBCCAccountServer interface {
	MakeMerchantCheck(c *gin.Context) error
	MakeTransfer(c *gin.Context) error
	AcceptTransfer(c *gin.Context) error
	DeclineTransfer(c *gin.Context) error
	RedoTransfer(c *gin.Context) error
	GetTransferDetails(c *gin.Context) error
	GetTransfersList(c *gin.Context) error
	GetAccountBalance(c *gin.Context) error
	GetAccountStatement(c *gin.Context) error
	GetAccountIdentifier(c *gin.Context) error
}

var _ GinBCCAccountServer = (*ginBCCAccountServer)(nil)

type ginBCCAccountServer struct {
	client BCCAccountClient
}

type BCCAccount_MakeMerchantCheck_Success struct {
	// Status is the status of the response. Set true
	Status bool `json:"status"`
	// Message is a human-readable explanation of the status
	Message string `json:"message"`
	// StatusCode is the GoErr status. Set 0
	StatusCode int `json:"status_code"`
	// Result is the result of the request
	Result *MakeMerchantCheckResponse `json:"result"`
}

type BCCAccount_MakeMerchantCheck_Failure struct {
	// Status is the status of the response. Set false
	Status bool `json:"status"`
	// Message is a human-readable explanation of the status
	Message string `json:"message"`
	// StatusCode is the GoErr status
	StatusCode int `json:"status_code"`
}

// MakeMerchantCheck
// @Summary MakeMerchantCheck
// @Security bearerAuth
// @ID BCCAccount_MakeMerchantCheck
// @Accept json
// @Param request body MakeMerchantCheckRequest true "MakeMerchantCheckRequest"
// @Success 200 {object} BCCAccount_MakeMerchantCheck_Success
// @Failure 401 {object} BCCAccount_MakeMerchantCheck_Failure "goerrpkg.ErrorTypeAuthorization"
// @Failure 404 {object} BCCAccount_MakeMerchantCheck_Failure "goerrpkg.ErrorTypeNotFound"
// @Failure 400 {object} BCCAccount_MakeMerchantCheck_Failure "goerrpkg.ErrorTypeBadRequest"
// @Failure 500 {object} BCCAccount_MakeMerchantCheck_Failure "goerrpkg.ErrorTypeUnexpectedError"
// @Failure 400 {object} BCCAccount_MakeMerchantCheck_Failure "gin.ErrorTypeBind"
// @Failure 500 {object} BCCAccount_MakeMerchantCheck_Failure "Undefined error"
// @Produce json
// @Router /processing.bcc_account.bcc_account.BCCAccount/MakeMerchantCheck [put]
// @tags BCCAccount
func (s *ginBCCAccountServer) MakeMerchantCheck(c *gin.Context) error {
	ctx, span := dog.CreateSpan(c.Request.Context(), "GinBCCAccountServer_MakeMerchantCheck")
	defer span.End()

	var request MakeMerchantCheckRequest
	if err := c.ShouldBindJSON(&request); err != nil {
		return err
	}

	ctx = metadata.NewOutgoingContext(ctx, metadata.MD(c.Request.Header))

	response, err := s.client.MakeMerchantCheck(ctx, &request)
	if err != nil {
		return err
	}

	c.JSON(http.StatusOK, &BCCAccount_MakeMerchantCheck_Success{
		Status:     true,
		Message:    "Success",
		StatusCode: 0,
		Result:     response,
	})
	return nil
}

type BCCAccount_MakeTransfer_Success struct {
	// Status is the status of the response. Set true
	Status bool `json:"status"`
	// Message is a human-readable explanation of the status
	Message string `json:"message"`
	// StatusCode is the GoErr status. Set 0
	StatusCode int `json:"status_code"`
	// Result is the result of the request
	Result *MakeTransferResponse `json:"result"`
}

type BCCAccount_MakeTransfer_Failure struct {
	// Status is the status of the response. Set false
	Status bool `json:"status"`
	// Message is a human-readable explanation of the status
	Message string `json:"message"`
	// StatusCode is the GoErr status
	StatusCode int `json:"status_code"`
}

// MakeTransfer
// @Summary MakeTransfer
// @Security bearerAuth
// @ID BCCAccount_MakeTransfer
// @Accept json
// @Param request body MakeTransferRequest true "MakeTransferRequest"
// @Success 200 {object} BCCAccount_MakeTransfer_Success
// @Failure 401 {object} BCCAccount_MakeTransfer_Failure "goerrpkg.ErrorTypeAuthorization"
// @Failure 404 {object} BCCAccount_MakeTransfer_Failure "goerrpkg.ErrorTypeNotFound"
// @Failure 400 {object} BCCAccount_MakeTransfer_Failure "goerrpkg.ErrorTypeBadRequest"
// @Failure 500 {object} BCCAccount_MakeTransfer_Failure "goerrpkg.ErrorTypeUnexpectedError"
// @Failure 400 {object} BCCAccount_MakeTransfer_Failure "gin.ErrorTypeBind"
// @Failure 500 {object} BCCAccount_MakeTransfer_Failure "Undefined error"
// @Produce json
// @Router /processing.bcc_account.bcc_account.BCCAccount/MakeTransfer [put]
// @tags BCCAccount
func (s *ginBCCAccountServer) MakeTransfer(c *gin.Context) error {
	ctx, span := dog.CreateSpan(c.Request.Context(), "GinBCCAccountServer_MakeTransfer")
	defer span.End()

	var request MakeTransferRequest
	if err := c.ShouldBindJSON(&request); err != nil {
		return err
	}

	ctx = metadata.NewOutgoingContext(ctx, metadata.MD(c.Request.Header))

	response, err := s.client.MakeTransfer(ctx, &request)
	if err != nil {
		return err
	}

	c.JSON(http.StatusOK, &BCCAccount_MakeTransfer_Success{
		Status:     true,
		Message:    "Success",
		StatusCode: 0,
		Result:     response,
	})
	return nil
}

type BCCAccount_AcceptTransfer_Success struct {
	// Status is the status of the response. Set true
	Status bool `json:"status"`
	// Message is a human-readable explanation of the status
	Message string `json:"message"`
	// StatusCode is the GoErr status. Set 0
	StatusCode int `json:"status_code"`
	// Result is the result of the request
	Result *AcceptTransferResponse `json:"result"`
}

type BCCAccount_AcceptTransfer_Failure struct {
	// Status is the status of the response. Set false
	Status bool `json:"status"`
	// Message is a human-readable explanation of the status
	Message string `json:"message"`
	// StatusCode is the GoErr status
	StatusCode int `json:"status_code"`
}

// AcceptTransfer
// @Summary AcceptTransfer
// @Security bearerAuth
// @ID BCCAccount_AcceptTransfer
// @Accept json
// @Param request body AcceptTransferRequest true "AcceptTransferRequest"
// @Success 200 {object} BCCAccount_AcceptTransfer_Success
// @Failure 401 {object} BCCAccount_AcceptTransfer_Failure "goerrpkg.ErrorTypeAuthorization"
// @Failure 404 {object} BCCAccount_AcceptTransfer_Failure "goerrpkg.ErrorTypeNotFound"
// @Failure 400 {object} BCCAccount_AcceptTransfer_Failure "goerrpkg.ErrorTypeBadRequest"
// @Failure 500 {object} BCCAccount_AcceptTransfer_Failure "goerrpkg.ErrorTypeUnexpectedError"
// @Failure 400 {object} BCCAccount_AcceptTransfer_Failure "gin.ErrorTypeBind"
// @Failure 500 {object} BCCAccount_AcceptTransfer_Failure "Undefined error"
// @Produce json
// @Router /processing.bcc_account.bcc_account.BCCAccount/AcceptTransfer [put]
// @tags BCCAccount
func (s *ginBCCAccountServer) AcceptTransfer(c *gin.Context) error {
	ctx, span := dog.CreateSpan(c.Request.Context(), "GinBCCAccountServer_AcceptTransfer")
	defer span.End()

	var request AcceptTransferRequest
	if err := c.ShouldBindJSON(&request); err != nil {
		return err
	}

	ctx = metadata.NewOutgoingContext(ctx, metadata.MD(c.Request.Header))

	response, err := s.client.AcceptTransfer(ctx, &request)
	if err != nil {
		return err
	}

	c.JSON(http.StatusOK, &BCCAccount_AcceptTransfer_Success{
		Status:     true,
		Message:    "Success",
		StatusCode: 0,
		Result:     response,
	})
	return nil
}

type BCCAccount_DeclineTransfer_Success struct {
	// Status is the status of the response. Set true
	Status bool `json:"status"`
	// Message is a human-readable explanation of the status
	Message string `json:"message"`
	// StatusCode is the GoErr status. Set 0
	StatusCode int `json:"status_code"`
	// Result is the result of the request
	Result *DeclineTransferResponse `json:"result"`
}

type BCCAccount_DeclineTransfer_Failure struct {
	// Status is the status of the response. Set false
	Status bool `json:"status"`
	// Message is a human-readable explanation of the status
	Message string `json:"message"`
	// StatusCode is the GoErr status
	StatusCode int `json:"status_code"`
}

// DeclineTransfer
// @Summary DeclineTransfer
// @Security bearerAuth
// @ID BCCAccount_DeclineTransfer
// @Accept json
// @Param request body DeclineTransferRequest true "DeclineTransferRequest"
// @Success 200 {object} BCCAccount_DeclineTransfer_Success
// @Failure 401 {object} BCCAccount_DeclineTransfer_Failure "goerrpkg.ErrorTypeAuthorization"
// @Failure 404 {object} BCCAccount_DeclineTransfer_Failure "goerrpkg.ErrorTypeNotFound"
// @Failure 400 {object} BCCAccount_DeclineTransfer_Failure "goerrpkg.ErrorTypeBadRequest"
// @Failure 500 {object} BCCAccount_DeclineTransfer_Failure "goerrpkg.ErrorTypeUnexpectedError"
// @Failure 400 {object} BCCAccount_DeclineTransfer_Failure "gin.ErrorTypeBind"
// @Failure 500 {object} BCCAccount_DeclineTransfer_Failure "Undefined error"
// @Produce json
// @Router /processing.bcc_account.bcc_account.BCCAccount/DeclineTransfer [put]
// @tags BCCAccount
func (s *ginBCCAccountServer) DeclineTransfer(c *gin.Context) error {
	ctx, span := dog.CreateSpan(c.Request.Context(), "GinBCCAccountServer_DeclineTransfer")
	defer span.End()

	var request DeclineTransferRequest
	if err := c.ShouldBindJSON(&request); err != nil {
		return err
	}

	ctx = metadata.NewOutgoingContext(ctx, metadata.MD(c.Request.Header))

	response, err := s.client.DeclineTransfer(ctx, &request)
	if err != nil {
		return err
	}

	c.JSON(http.StatusOK, &BCCAccount_DeclineTransfer_Success{
		Status:     true,
		Message:    "Success",
		StatusCode: 0,
		Result:     response,
	})
	return nil
}

type BCCAccount_RedoTransfer_Success struct {
	// Status is the status of the response. Set true
	Status bool `json:"status"`
	// Message is a human-readable explanation of the status
	Message string `json:"message"`
	// StatusCode is the GoErr status. Set 0
	StatusCode int `json:"status_code"`
	// Result is the result of the request
	Result *RedoTransferResponse `json:"result"`
}

type BCCAccount_RedoTransfer_Failure struct {
	// Status is the status of the response. Set false
	Status bool `json:"status"`
	// Message is a human-readable explanation of the status
	Message string `json:"message"`
	// StatusCode is the GoErr status
	StatusCode int `json:"status_code"`
}

// RedoTransfer
// @Summary RedoTransfer
// @Security bearerAuth
// @ID BCCAccount_RedoTransfer
// @Accept json
// @Param request body RedoTransferRequest true "RedoTransferRequest"
// @Success 200 {object} BCCAccount_RedoTransfer_Success
// @Failure 401 {object} BCCAccount_RedoTransfer_Failure "goerrpkg.ErrorTypeAuthorization"
// @Failure 404 {object} BCCAccount_RedoTransfer_Failure "goerrpkg.ErrorTypeNotFound"
// @Failure 400 {object} BCCAccount_RedoTransfer_Failure "goerrpkg.ErrorTypeBadRequest"
// @Failure 500 {object} BCCAccount_RedoTransfer_Failure "goerrpkg.ErrorTypeUnexpectedError"
// @Failure 400 {object} BCCAccount_RedoTransfer_Failure "gin.ErrorTypeBind"
// @Failure 500 {object} BCCAccount_RedoTransfer_Failure "Undefined error"
// @Produce json
// @Router /processing.bcc_account.bcc_account.BCCAccount/RedoTransfer [put]
// @tags BCCAccount
func (s *ginBCCAccountServer) RedoTransfer(c *gin.Context) error {
	ctx, span := dog.CreateSpan(c.Request.Context(), "GinBCCAccountServer_RedoTransfer")
	defer span.End()

	var request RedoTransferRequest
	if err := c.ShouldBindJSON(&request); err != nil {
		return err
	}

	ctx = metadata.NewOutgoingContext(ctx, metadata.MD(c.Request.Header))

	response, err := s.client.RedoTransfer(ctx, &request)
	if err != nil {
		return err
	}

	c.JSON(http.StatusOK, &BCCAccount_RedoTransfer_Success{
		Status:     true,
		Message:    "Success",
		StatusCode: 0,
		Result:     response,
	})
	return nil
}

type BCCAccount_GetTransferDetails_Success struct {
	// Status is the status of the response. Set true
	Status bool `json:"status"`
	// Message is a human-readable explanation of the status
	Message string `json:"message"`
	// StatusCode is the GoErr status. Set 0
	StatusCode int `json:"status_code"`
	// Result is the result of the request
	Result *GetTransferDetailsResponse `json:"result"`
}

type BCCAccount_GetTransferDetails_Failure struct {
	// Status is the status of the response. Set false
	Status bool `json:"status"`
	// Message is a human-readable explanation of the status
	Message string `json:"message"`
	// StatusCode is the GoErr status
	StatusCode int `json:"status_code"`
}

// GetTransferDetails
// @Summary GetTransferDetails
// @Security bearerAuth
// @ID BCCAccount_GetTransferDetails
// @Accept json
// @Param request body GetTransferDetailsRequest true "GetTransferDetailsRequest"
// @Success 200 {object} BCCAccount_GetTransferDetails_Success
// @Failure 401 {object} BCCAccount_GetTransferDetails_Failure "goerrpkg.ErrorTypeAuthorization"
// @Failure 404 {object} BCCAccount_GetTransferDetails_Failure "goerrpkg.ErrorTypeNotFound"
// @Failure 400 {object} BCCAccount_GetTransferDetails_Failure "goerrpkg.ErrorTypeBadRequest"
// @Failure 500 {object} BCCAccount_GetTransferDetails_Failure "goerrpkg.ErrorTypeUnexpectedError"
// @Failure 400 {object} BCCAccount_GetTransferDetails_Failure "gin.ErrorTypeBind"
// @Failure 500 {object} BCCAccount_GetTransferDetails_Failure "Undefined error"
// @Produce json
// @Router /processing.bcc_account.bcc_account.BCCAccount/GetTransferDetails [put]
// @tags BCCAccount
func (s *ginBCCAccountServer) GetTransferDetails(c *gin.Context) error {
	ctx, span := dog.CreateSpan(c.Request.Context(), "GinBCCAccountServer_GetTransferDetails")
	defer span.End()

	var request GetTransferDetailsRequest
	if err := c.ShouldBindJSON(&request); err != nil {
		return err
	}

	ctx = metadata.NewOutgoingContext(ctx, metadata.MD(c.Request.Header))

	response, err := s.client.GetTransferDetails(ctx, &request)
	if err != nil {
		return err
	}

	c.JSON(http.StatusOK, &BCCAccount_GetTransferDetails_Success{
		Status:     true,
		Message:    "Success",
		StatusCode: 0,
		Result:     response,
	})
	return nil
}

type BCCAccount_GetTransfersList_Success struct {
	// Status is the status of the response. Set true
	Status bool `json:"status"`
	// Message is a human-readable explanation of the status
	Message string `json:"message"`
	// StatusCode is the GoErr status. Set 0
	StatusCode int `json:"status_code"`
	// Result is the result of the request
	Result *GetTransfersListResponse `json:"result"`
}

type BCCAccount_GetTransfersList_Failure struct {
	// Status is the status of the response. Set false
	Status bool `json:"status"`
	// Message is a human-readable explanation of the status
	Message string `json:"message"`
	// StatusCode is the GoErr status
	StatusCode int `json:"status_code"`
}

// GetTransfersList
// @Summary GetTransfersList
// @Security bearerAuth
// @ID BCCAccount_GetTransfersList
// @Accept json
// @Param request body GetTransfersListRequest true "GetTransfersListRequest"
// @Success 200 {object} BCCAccount_GetTransfersList_Success
// @Failure 401 {object} BCCAccount_GetTransfersList_Failure "goerrpkg.ErrorTypeAuthorization"
// @Failure 404 {object} BCCAccount_GetTransfersList_Failure "goerrpkg.ErrorTypeNotFound"
// @Failure 400 {object} BCCAccount_GetTransfersList_Failure "goerrpkg.ErrorTypeBadRequest"
// @Failure 500 {object} BCCAccount_GetTransfersList_Failure "goerrpkg.ErrorTypeUnexpectedError"
// @Failure 400 {object} BCCAccount_GetTransfersList_Failure "gin.ErrorTypeBind"
// @Failure 500 {object} BCCAccount_GetTransfersList_Failure "Undefined error"
// @Produce json
// @Router /processing.bcc_account.bcc_account.BCCAccount/GetTransfersList [put]
// @tags BCCAccount
func (s *ginBCCAccountServer) GetTransfersList(c *gin.Context) error {
	ctx, span := dog.CreateSpan(c.Request.Context(), "GinBCCAccountServer_GetTransfersList")
	defer span.End()

	var request GetTransfersListRequest
	if err := c.ShouldBindJSON(&request); err != nil {
		return err
	}

	ctx = metadata.NewOutgoingContext(ctx, metadata.MD(c.Request.Header))

	response, err := s.client.GetTransfersList(ctx, &request)
	if err != nil {
		return err
	}

	c.JSON(http.StatusOK, &BCCAccount_GetTransfersList_Success{
		Status:     true,
		Message:    "Success",
		StatusCode: 0,
		Result:     response,
	})
	return nil
}

type BCCAccount_GetAccountBalance_Success struct {
	// Status is the status of the response. Set true
	Status bool `json:"status"`
	// Message is a human-readable explanation of the status
	Message string `json:"message"`
	// StatusCode is the GoErr status. Set 0
	StatusCode int `json:"status_code"`
	// Result is the result of the request
	Result *GetAccountBalanceResponse `json:"result"`
}

type BCCAccount_GetAccountBalance_Failure struct {
	// Status is the status of the response. Set false
	Status bool `json:"status"`
	// Message is a human-readable explanation of the status
	Message string `json:"message"`
	// StatusCode is the GoErr status
	StatusCode int `json:"status_code"`
}

// GetAccountBalance
// @Summary GetAccountBalance
// @Security bearerAuth
// @ID BCCAccount_GetAccountBalance
// @Accept json
// @Param request body GetAccountBalanceRequest true "GetAccountBalanceRequest"
// @Success 200 {object} BCCAccount_GetAccountBalance_Success
// @Failure 401 {object} BCCAccount_GetAccountBalance_Failure "goerrpkg.ErrorTypeAuthorization"
// @Failure 404 {object} BCCAccount_GetAccountBalance_Failure "goerrpkg.ErrorTypeNotFound"
// @Failure 400 {object} BCCAccount_GetAccountBalance_Failure "goerrpkg.ErrorTypeBadRequest"
// @Failure 500 {object} BCCAccount_GetAccountBalance_Failure "goerrpkg.ErrorTypeUnexpectedError"
// @Failure 400 {object} BCCAccount_GetAccountBalance_Failure "gin.ErrorTypeBind"
// @Failure 500 {object} BCCAccount_GetAccountBalance_Failure "Undefined error"
// @Produce json
// @Router /processing.bcc_account.bcc_account.BCCAccount/GetAccountBalance [put]
// @tags BCCAccount
func (s *ginBCCAccountServer) GetAccountBalance(c *gin.Context) error {
	ctx, span := dog.CreateSpan(c.Request.Context(), "GinBCCAccountServer_GetAccountBalance")
	defer span.End()

	var request GetAccountBalanceRequest
	if err := c.ShouldBindJSON(&request); err != nil {
		return err
	}

	ctx = metadata.NewOutgoingContext(ctx, metadata.MD(c.Request.Header))

	response, err := s.client.GetAccountBalance(ctx, &request)
	if err != nil {
		return err
	}

	c.JSON(http.StatusOK, &BCCAccount_GetAccountBalance_Success{
		Status:     true,
		Message:    "Success",
		StatusCode: 0,
		Result:     response,
	})
	return nil
}

type BCCAccount_GetAccountStatement_Success struct {
	// Status is the status of the response. Set true
	Status bool `json:"status"`
	// Message is a human-readable explanation of the status
	Message string `json:"message"`
	// StatusCode is the GoErr status. Set 0
	StatusCode int `json:"status_code"`
	// Result is the result of the request
	Result *GetAccountStatementResponse `json:"result"`
}

type BCCAccount_GetAccountStatement_Failure struct {
	// Status is the status of the response. Set false
	Status bool `json:"status"`
	// Message is a human-readable explanation of the status
	Message string `json:"message"`
	// StatusCode is the GoErr status
	StatusCode int `json:"status_code"`
}

// GetAccountStatement
// @Summary GetAccountStatement
// @Security bearerAuth
// @ID BCCAccount_GetAccountStatement
// @Accept json
// @Param request body GetAccountStatementRequest true "GetAccountStatementRequest"
// @Success 200 {object} BCCAccount_GetAccountStatement_Success
// @Failure 401 {object} BCCAccount_GetAccountStatement_Failure "goerrpkg.ErrorTypeAuthorization"
// @Failure 404 {object} BCCAccount_GetAccountStatement_Failure "goerrpkg.ErrorTypeNotFound"
// @Failure 400 {object} BCCAccount_GetAccountStatement_Failure "goerrpkg.ErrorTypeBadRequest"
// @Failure 500 {object} BCCAccount_GetAccountStatement_Failure "goerrpkg.ErrorTypeUnexpectedError"
// @Failure 400 {object} BCCAccount_GetAccountStatement_Failure "gin.ErrorTypeBind"
// @Failure 500 {object} BCCAccount_GetAccountStatement_Failure "Undefined error"
// @Produce json
// @Router /processing.bcc_account.bcc_account.BCCAccount/GetAccountStatement [put]
// @tags BCCAccount
func (s *ginBCCAccountServer) GetAccountStatement(c *gin.Context) error {
	ctx, span := dog.CreateSpan(c.Request.Context(), "GinBCCAccountServer_GetAccountStatement")
	defer span.End()

	var request GetAccountStatementRequest
	if err := c.ShouldBindJSON(&request); err != nil {
		return err
	}

	ctx = metadata.NewOutgoingContext(ctx, metadata.MD(c.Request.Header))

	response, err := s.client.GetAccountStatement(ctx, &request)
	if err != nil {
		return err
	}

	c.JSON(http.StatusOK, &BCCAccount_GetAccountStatement_Success{
		Status:     true,
		Message:    "Success",
		StatusCode: 0,
		Result:     response,
	})
	return nil
}

type BCCAccount_GetAccountIdentifier_Success struct {
	// Status is the status of the response. Set true
	Status bool `json:"status"`
	// Message is a human-readable explanation of the status
	Message string `json:"message"`
	// StatusCode is the GoErr status. Set 0
	StatusCode int `json:"status_code"`
	// Result is the result of the request
	Result *GetAccountIdentifierResponse `json:"result"`
}

type BCCAccount_GetAccountIdentifier_Failure struct {
	// Status is the status of the response. Set false
	Status bool `json:"status"`
	// Message is a human-readable explanation of the status
	Message string `json:"message"`
	// StatusCode is the GoErr status
	StatusCode int `json:"status_code"`
}

// GetAccountIdentifier
// @Summary GetAccountIdentifier
// @Security bearerAuth
// @ID BCCAccount_GetAccountIdentifier
// @Accept json
// @Param request body emptypb.Empty true "Empty"
// @Success 200 {object} BCCAccount_GetAccountIdentifier_Success
// @Failure 401 {object} BCCAccount_GetAccountIdentifier_Failure "goerrpkg.ErrorTypeAuthorization"
// @Failure 404 {object} BCCAccount_GetAccountIdentifier_Failure "goerrpkg.ErrorTypeNotFound"
// @Failure 400 {object} BCCAccount_GetAccountIdentifier_Failure "goerrpkg.ErrorTypeBadRequest"
// @Failure 500 {object} BCCAccount_GetAccountIdentifier_Failure "goerrpkg.ErrorTypeUnexpectedError"
// @Failure 400 {object} BCCAccount_GetAccountIdentifier_Failure "gin.ErrorTypeBind"
// @Failure 500 {object} BCCAccount_GetAccountIdentifier_Failure "Undefined error"
// @Produce json
// @Router /processing.bcc_account.bcc_account.BCCAccount/GetAccountIdentifier [put]
// @tags BCCAccount
func (s *ginBCCAccountServer) GetAccountIdentifier(c *gin.Context) error {
	ctx, span := dog.CreateSpan(c.Request.Context(), "GinBCCAccountServer_GetAccountIdentifier")
	defer span.End()

	var request emptypb.Empty
	if err := c.ShouldBindJSON(&request); err != nil {
		return err
	}

	ctx = metadata.NewOutgoingContext(ctx, metadata.MD(c.Request.Header))

	response, err := s.client.GetAccountIdentifier(ctx, &request)
	if err != nil {
		return err
	}

	c.JSON(http.StatusOK, &BCCAccount_GetAccountIdentifier_Success{
		Status:     true,
		Message:    "Success",
		StatusCode: 0,
		Result:     response,
	})
	return nil
}
