package model

type Acquirer struct {
	TimestampMixin
	ID          uint64  `gorm:"column:id" json:"id"`
	Code        string  `gorm:"column:code" json:"code"`
	Name        string  `gorm:"column:name" json:"name"`
	BankID      uint64  `gorm:"column:bank_id" json:"bank_id"`
	Description *string `gorm:"column:description" json:"description"`
	CountryID   uint64  `gorm:"column:country_id" json:"country_id"`
	IsActive    bool    `gorm:"column:is_active" json:"is_active"`
	CountryName string  `json:"country_name,omitempty"`
}

func (i Acquirer) TableName() string {
	return "acquirer.acquirers"
}

type AcquirerTerminal struct {
	Acquirers Acquirer  `json:"acquirer"`
	Terminals Terminals `json:"terminals"`
}
