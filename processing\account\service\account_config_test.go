package service

import (
	"context"
	"crypto/aes"
	"encoding/json"
	"git.local/sensitive/processing/account/repository/databasemocks"
	"github.com/golang/mock/gomock"
	"testing"

	"github.com/stretchr/testify/require"

	"git.local/sensitive/innerpb/processing/goerr"
	"git.local/sensitive/processing/account/model"
	"git.local/sensitive/processing/account/schema"
	"git.local/sensitive/sdk/dog/e2e"
)

var (
	badKey                    = "123"
	normalKey                 = "2lgD_WlWfa3Z-BD9"
	defaultEncryptedConfig    = "AAAAAAAAAAAAAAAAAAAAAKCya3wWGoWNw5yAfsU98p9AZt8SNhRHOAWe1JxTOPAD" // map[string]any{"my-name-is": "skrillex"}
	defaultBadEncryptedConfig = "AAAAAAAAAAAAAAAAAAAAAIBflMiji57eMFHwJHmsnS0yDAN6lJFw5LgKeBe7afjT" // some random bad payload
)

func TestGetAccountConfig(t *testing.T) {
	type getByIDOp struct {
		input     uint64
		output    *model.Account
		outputErr error
	}

	tests := []struct {
		name      string
		reqID     uint64
		want      schema.DecryptedAccountConfig
		wantErr   error
		getByID   getByIDOp
		appConfig map[string]interface{}
	}{
		{
			name:  "error when getting by id",
			reqID: 23,
			getByID: getByIDOp{
				input:     23,
				output:    nil,
				outputErr: goerr.ErrDbUnexpected,
			},
			wantErr: goerr.ErrDbUnexpected,
			appConfig: map[string]interface{}{
				"SYMMETRIC_KEY": normalKey,
			},
		},
		{
			name:  "bad config (could not be unmarshalled)",
			reqID: 23,
			getByID: getByIDOp{
				input: 23,
				output: &model.Account{
					ID:              23,
					Number:          "some number",
					CurrencyCode:    "kzt",
					AccountTypeID:   2,
					BankCode:        "bcc",
					BankID:          3,
					EncryptedConfig: defaultBadEncryptedConfig,
				},
				outputErr: nil,
			},
			wantErr: &json.UnmarshalTypeError{},
			appConfig: map[string]interface{}{
				"SYMMETRIC_KEY": normalKey,
			},
		},
		{
			name:  "bad symmetric key (config cannot be decrypted)",
			reqID: 23,
			getByID: getByIDOp{
				input: 23,
				output: &model.Account{
					ID:              23,
					Number:          "some number",
					CurrencyCode:    "kzt",
					AccountTypeID:   2,
					BankCode:        "bcc",
					BankID:          3,
					EncryptedConfig: defaultEncryptedConfig,
				},
				outputErr: nil,
			},
			wantErr: aes.KeySizeError(3),
			appConfig: map[string]interface{}{
				"SYMMETRIC_KEY": badKey,
			},
			want: nil,
		},
		{
			name:  "success",
			reqID: 23,
			getByID: getByIDOp{
				input: 23,
				output: &model.Account{
					ID:              23,
					Number:          "some number",
					CurrencyCode:    "kzt",
					AccountTypeID:   2,
					BankCode:        "bcc",
					BankID:          3,
					EncryptedConfig: defaultEncryptedConfig,
				},
				outputErr: nil,
			},
			wantErr: nil,
			appConfig: map[string]interface{}{
				"SYMMETRIC_KEY": normalKey,
			},
			want: schema.DecryptedAccountConfig{
				"my-name-is": "skrillex",
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctrl := gomock.NewController(t)
			defer ctrl.Finish()

			c := context.Background()
			ctx, cancel := context.WithCancel(c)

			e2e.InitAppE2E(t, ctx, cancel, nil, nil, tt.appConfig)

			accountInfoDBMock := databasemocks.NewMockAccountInformer(ctrl)
			accountUpdaterDBMock := databasemocks.NewMockAccountUpdater(ctrl)

			accountInfoDBMock.EXPECT().GetById(
				gomock.Any(),
				tt.getByID.input,
			).Return(tt.getByID.output, tt.getByID.outputErr).Times(1)

			s := NewAccountConfigService(accountInfoDBMock, accountUpdaterDBMock)

			res, err := s.GetAccountConfig(context.Background(), tt.reqID)
			if tt.wantErr != nil {
				require.Error(t, err)
				require.ErrorAs(t, err, &tt.wantErr)
			} else {
				require.NoError(t, err)
			}

			require.Equal(t, tt.want, res)
		})
	}
}

func TestUpdateAccountConfig(t *testing.T) {
	type updateAccountConfigOp struct {
		isCalled  bool
		inputID   uint64
		inputCfg  string
		outputErr error
	}

	tests := []struct {
		name                string
		reqID               uint64
		reqConfig           map[string]any
		appConfig           map[string]any
		updateAccountConfig updateAccountConfigOp
		wantErr             error
	}{
		{
			name:  "unmarshall error",
			reqID: 23,
			reqConfig: map[string]any{
				"ASD": make(chan int, 3),
			},
			appConfig: map[string]any{
				"SYMMETRIC_KEY": normalKey,
			},
			wantErr: goerr.ErrTerminalConfigEncryption,
		},
		{
			name:  "encryption error",
			reqID: 23,
			reqConfig: map[string]any{
				"my-name-is": "skrillex",
			},
			appConfig: map[string]any{
				"SYMMETRIC_KEY": badKey,
			},
			wantErr: goerr.ErrTerminalConfigEncryption,
		},
		{
			name:  "update error",
			reqID: 23,
			reqConfig: map[string]any{
				"my-name-is": "skrillex",
			},
			appConfig: map[string]any{
				"SYMMETRIC_KEY": normalKey,
			},
			wantErr: goerr.ErrDbUnexpected,
			updateAccountConfig: updateAccountConfigOp{
				isCalled:  true,
				inputCfg:  defaultEncryptedConfig,
				inputID:   23,
				outputErr: goerr.ErrDbUnexpected,
			},
		},
		{
			name:  "success",
			reqID: 23,
			reqConfig: map[string]any{
				"my-name-is": "skrillex",
			},
			appConfig: map[string]any{
				"SYMMETRIC_KEY": normalKey,
			},
			wantErr: nil,
			updateAccountConfig: updateAccountConfigOp{
				isCalled:  true,
				inputCfg:  defaultEncryptedConfig,
				inputID:   23,
				outputErr: nil,
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctrl := gomock.NewController(t)
			defer ctrl.Finish()

			c := context.Background()
			ctx, cancel := context.WithCancel(c)
			e2e.InitAppE2E(t, ctx, cancel, nil, nil, tt.appConfig)

			accountInfoDBMock := databasemocks.NewMockAccountInformer(ctrl)
			accountUpdaterDBMock := databasemocks.NewMockAccountUpdater(ctrl)

			if tt.updateAccountConfig.isCalled {
				accountUpdaterDBMock.EXPECT().UpdateAccountConfig(
					gomock.Any(),
					tt.updateAccountConfig.inputID,
					tt.updateAccountConfig.inputCfg,
				).Return(tt.updateAccountConfig.outputErr).Times(1)
			}

			s := NewAccountConfigService(accountInfoDBMock, accountUpdaterDBMock)

			err := s.UpdateAccountConfig(context.Background(), tt.reqID, tt.reqConfig)
			if tt.wantErr != nil {
				require.Error(t, err)
				require.ErrorAs(t, err, &tt.wantErr)
			} else {
				require.NoError(t, err)
			}
		})
	}
}
