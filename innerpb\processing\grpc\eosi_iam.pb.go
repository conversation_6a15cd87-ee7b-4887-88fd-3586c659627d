// Code generated by protoc-gen-go-mvp. DO NOT EDIT.
// versions:
// - protoc-gen-go-mvp v2.0.1
// created by mvp authors

package grpc

import (
	context "context"
	grpc "google.golang.org/grpc"
	metadata "google.golang.org/grpc/metadata"
)

func NewIamEosiServer(
	srv EosiServer,
) EosiServer {
	return &iamEosiServer{
		srv: srv,
	}
}

var _ EosiServer = (*iamEosiServer)(nil)

type iamEosiServer struct {
	UnimplementedEosiServer

	srv EosiServer
}

func (s *iamEosiServer) GetPaymentOrder(
	ctx context.Context,
	req *GetPaymentOrderRequest,
) (
	*GetPaymentOrderResponse,
	error,
) {
	return s.srv.GetPaymentOrder(ctx, req)
}

func (s *iamEosiServer) GetOrderingIdentifier(
	ctx context.Context,
	req *GetOrderingIdentifierRequest,
) (
	*GetOrderingIdentifierResponse,
	error,
) {
	return s.srv.GetOrderingIdentifier(ctx, req)
}

func NewIamEosiClient(
	client EosiClient,
) EosiClient {
	return &iamEosiClient{
		client: client,
	}
}

type iamEosiClient struct {
	client EosiClient
}

func (s *iamEosiClient) GetPaymentOrder(
	ctx context.Context,
	req *GetPaymentOrderRequest,
	opts ...grpc.CallOption,
) (
	*GetPaymentOrderResponse,
	error,
) {
	md, _ := metadata.FromIncomingContext(ctx)
	return s.client.GetPaymentOrder(metadata.NewOutgoingContext(ctx, md), req)
}

func (s *iamEosiClient) GetOrderingIdentifier(
	ctx context.Context,
	req *GetOrderingIdentifierRequest,
	opts ...grpc.CallOption,
) (
	*GetOrderingIdentifierResponse,
	error,
) {
	md, _ := metadata.FromIncomingContext(ctx)
	return s.client.GetOrderingIdentifier(metadata.NewOutgoingContext(ctx, md), req)
}
