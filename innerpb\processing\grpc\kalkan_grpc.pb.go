// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.5.1
// - protoc             v5.29.3
// source: inner/processing/grpc/kalkan.proto

package grpc

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.64.0 or later.
const _ = grpc.SupportPackageIsVersion9

const (
	Kalkan_MakeSignatureV1_FullMethodName = "/processing.kalkan.kalkan.Kalkan/MakeSignatureV1"
)

// KalkanClient is the client API for Kalkan service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type KalkanClient interface {
	MakeSignatureV1(ctx context.Context, in *MakeSignatureRequestV1, opts ...grpc.CallOption) (*MakeSignatureResponseV1, error)
}

type kalkanClient struct {
	cc grpc.ClientConnInterface
}

func NewKalkanClient(cc grpc.ClientConnInterface) KalkanClient {
	return &kalkanClient{cc}
}

func (c *kalkanClient) MakeSignatureV1(ctx context.Context, in *MakeSignatureRequestV1, opts ...grpc.CallOption) (*MakeSignatureResponseV1, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(MakeSignatureResponseV1)
	err := c.cc.Invoke(ctx, Kalkan_MakeSignatureV1_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// KalkanServer is the server API for Kalkan service.
// All implementations must embed UnimplementedKalkanServer
// for forward compatibility.
type KalkanServer interface {
	MakeSignatureV1(context.Context, *MakeSignatureRequestV1) (*MakeSignatureResponseV1, error)
	mustEmbedUnimplementedKalkanServer()
}

// UnimplementedKalkanServer must be embedded to have
// forward compatible implementations.
//
// NOTE: this should be embedded by value instead of pointer to avoid a nil
// pointer dereference when methods are called.
type UnimplementedKalkanServer struct{}

func (UnimplementedKalkanServer) MakeSignatureV1(context.Context, *MakeSignatureRequestV1) (*MakeSignatureResponseV1, error) {
	return nil, status.Errorf(codes.Unimplemented, "method MakeSignatureV1 not implemented")
}
func (UnimplementedKalkanServer) mustEmbedUnimplementedKalkanServer() {}
func (UnimplementedKalkanServer) testEmbeddedByValue()                {}

// UnsafeKalkanServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to KalkanServer will
// result in compilation errors.
type UnsafeKalkanServer interface {
	mustEmbedUnimplementedKalkanServer()
}

func RegisterKalkanServer(s grpc.ServiceRegistrar, srv KalkanServer) {
	// If the following call pancis, it indicates UnimplementedKalkanServer was
	// embedded by pointer and is nil.  This will cause panics if an
	// unimplemented method is ever invoked, so we test this at initialization
	// time to prevent it from happening at runtime later due to I/O.
	if t, ok := srv.(interface{ testEmbeddedByValue() }); ok {
		t.testEmbeddedByValue()
	}
	s.RegisterService(&Kalkan_ServiceDesc, srv)
}

func _Kalkan_MakeSignatureV1_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(MakeSignatureRequestV1)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(KalkanServer).MakeSignatureV1(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Kalkan_MakeSignatureV1_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(KalkanServer).MakeSignatureV1(ctx, req.(*MakeSignatureRequestV1))
	}
	return interceptor(ctx, in, info, handler)
}

// Kalkan_ServiceDesc is the grpc.ServiceDesc for Kalkan service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var Kalkan_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "processing.kalkan.kalkan.Kalkan",
	HandlerType: (*KalkanServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "MakeSignatureV1",
			Handler:    _Kalkan_MakeSignatureV1_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "inner/processing/grpc/kalkan.proto",
}
