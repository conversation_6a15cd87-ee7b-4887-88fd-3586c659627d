// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.5
// 	protoc        v5.29.3
// source: inner/processing/grpc/multiacquiring_balance.proto

package grpc

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	structpb "google.golang.org/protobuf/types/known/structpb"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type CheckBalanceRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	TerminalId    *uint64                `protobuf:"varint,1,opt,name=terminal_id,json=terminalId" json:"terminal_id,omitempty"`
	AcquirerCode  *string                `protobuf:"bytes,2,opt,name=acquirer_code,json=acquirerCode" json:"acquirer_code,omitempty"`
	Config        *structpb.Struct       `protobuf:"bytes,3,opt,name=config" json:"config,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CheckBalanceRequest) Reset() {
	*x = CheckBalanceRequest{}
	mi := &file_inner_processing_grpc_multiacquiring_balance_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CheckBalanceRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CheckBalanceRequest) ProtoMessage() {}

func (x *CheckBalanceRequest) ProtoReflect() protoreflect.Message {
	mi := &file_inner_processing_grpc_multiacquiring_balance_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CheckBalanceRequest.ProtoReflect.Descriptor instead.
func (*CheckBalanceRequest) Descriptor() ([]byte, []int) {
	return file_inner_processing_grpc_multiacquiring_balance_proto_rawDescGZIP(), []int{0}
}

func (x *CheckBalanceRequest) GetTerminalId() uint64 {
	if x != nil && x.TerminalId != nil {
		return *x.TerminalId
	}
	return 0
}

func (x *CheckBalanceRequest) GetAcquirerCode() string {
	if x != nil && x.AcquirerCode != nil {
		return *x.AcquirerCode
	}
	return ""
}

func (x *CheckBalanceRequest) GetConfig() *structpb.Struct {
	if x != nil {
		return x.Config
	}
	return nil
}

type CheckBalanceResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Amount        *float64               `protobuf:"fixed64,1,opt,name=amount" json:"amount,omitempty"`
	Message       *string                `protobuf:"bytes,2,opt,name=message" json:"message,omitempty"`
	TerminalId    *uint64                `protobuf:"varint,3,opt,name=terminal_id,json=terminalId" json:"terminal_id,omitempty"`
	AcquirerCode  *string                `protobuf:"bytes,4,opt,name=acquirer_code,json=acquirerCode" json:"acquirer_code,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CheckBalanceResponse) Reset() {
	*x = CheckBalanceResponse{}
	mi := &file_inner_processing_grpc_multiacquiring_balance_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CheckBalanceResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CheckBalanceResponse) ProtoMessage() {}

func (x *CheckBalanceResponse) ProtoReflect() protoreflect.Message {
	mi := &file_inner_processing_grpc_multiacquiring_balance_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CheckBalanceResponse.ProtoReflect.Descriptor instead.
func (*CheckBalanceResponse) Descriptor() ([]byte, []int) {
	return file_inner_processing_grpc_multiacquiring_balance_proto_rawDescGZIP(), []int{1}
}

func (x *CheckBalanceResponse) GetAmount() float64 {
	if x != nil && x.Amount != nil {
		return *x.Amount
	}
	return 0
}

func (x *CheckBalanceResponse) GetMessage() string {
	if x != nil && x.Message != nil {
		return *x.Message
	}
	return ""
}

func (x *CheckBalanceResponse) GetTerminalId() uint64 {
	if x != nil && x.TerminalId != nil {
		return *x.TerminalId
	}
	return 0
}

func (x *CheckBalanceResponse) GetAcquirerCode() string {
	if x != nil && x.AcquirerCode != nil {
		return *x.AcquirerCode
	}
	return ""
}

var File_inner_processing_grpc_multiacquiring_balance_proto protoreflect.FileDescriptor

var file_inner_processing_grpc_multiacquiring_balance_proto_rawDesc = string([]byte{
	0x0a, 0x32, 0x69, 0x6e, 0x6e, 0x65, 0x72, 0x2f, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69,
	0x6e, 0x67, 0x2f, 0x67, 0x72, 0x70, 0x63, 0x2f, 0x6d, 0x75, 0x6c, 0x74, 0x69, 0x61, 0x63, 0x71,
	0x75, 0x69, 0x72, 0x69, 0x6e, 0x67, 0x5f, 0x62, 0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x12, 0x30, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67,
	0x2e, 0x6d, 0x75, 0x6c, 0x74, 0x69, 0x61, 0x63, 0x71, 0x75, 0x69, 0x72, 0x69, 0x6e, 0x67, 0x2e,
	0x6d, 0x75, 0x6c, 0x74, 0x69, 0x61, 0x63, 0x71, 0x75, 0x69, 0x72, 0x69, 0x6e, 0x67, 0x5f, 0x62,
	0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65, 0x1a, 0x1c, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x73, 0x74, 0x72, 0x75, 0x63, 0x74, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x22, 0x8c, 0x01, 0x0a, 0x13, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x42, 0x61,
	0x6c, 0x61, 0x6e, 0x63, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1f, 0x0a, 0x0b,
	0x74, 0x65, 0x72, 0x6d, 0x69, 0x6e, 0x61, 0x6c, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x04, 0x52, 0x0a, 0x74, 0x65, 0x72, 0x6d, 0x69, 0x6e, 0x61, 0x6c, 0x49, 0x64, 0x12, 0x23, 0x0a,
	0x0d, 0x61, 0x63, 0x71, 0x75, 0x69, 0x72, 0x65, 0x72, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x61, 0x63, 0x71, 0x75, 0x69, 0x72, 0x65, 0x72, 0x43, 0x6f,
	0x64, 0x65, 0x12, 0x2f, 0x0a, 0x06, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x17, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x62, 0x75, 0x66, 0x2e, 0x53, 0x74, 0x72, 0x75, 0x63, 0x74, 0x52, 0x06, 0x63, 0x6f, 0x6e,
	0x66, 0x69, 0x67, 0x22, 0x8e, 0x01, 0x0a, 0x14, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x42, 0x61, 0x6c,
	0x61, 0x6e, 0x63, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x16, 0x0a, 0x06,
	0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x01, 0x52, 0x06, 0x61, 0x6d,
	0x6f, 0x75, 0x6e, 0x74, 0x12, 0x18, 0x0a, 0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x1f,
	0x0a, 0x0b, 0x74, 0x65, 0x72, 0x6d, 0x69, 0x6e, 0x61, 0x6c, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x04, 0x52, 0x0a, 0x74, 0x65, 0x72, 0x6d, 0x69, 0x6e, 0x61, 0x6c, 0x49, 0x64, 0x12,
	0x23, 0x0a, 0x0d, 0x61, 0x63, 0x71, 0x75, 0x69, 0x72, 0x65, 0x72, 0x5f, 0x63, 0x6f, 0x64, 0x65,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x61, 0x63, 0x71, 0x75, 0x69, 0x72, 0x65, 0x72,
	0x43, 0x6f, 0x64, 0x65, 0x32, 0xb9, 0x01, 0x0a, 0x15, 0x4d, 0x75, 0x6c, 0x74, 0x69, 0x61, 0x63,
	0x71, 0x75, 0x69, 0x72, 0x69, 0x6e, 0x67, 0x42, 0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65, 0x12, 0x9f,
	0x01, 0x0a, 0x0c, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x42, 0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65, 0x12,
	0x45, 0x2e, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x2e, 0x6d, 0x75, 0x6c,
	0x74, 0x69, 0x61, 0x63, 0x71, 0x75, 0x69, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x6d, 0x75, 0x6c, 0x74,
	0x69, 0x61, 0x63, 0x71, 0x75, 0x69, 0x72, 0x69, 0x6e, 0x67, 0x5f, 0x62, 0x61, 0x6c, 0x61, 0x6e,
	0x63, 0x65, 0x2e, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x42, 0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x46, 0x2e, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73,
	0x69, 0x6e, 0x67, 0x2e, 0x6d, 0x75, 0x6c, 0x74, 0x69, 0x61, 0x63, 0x71, 0x75, 0x69, 0x72, 0x69,
	0x6e, 0x67, 0x2e, 0x6d, 0x75, 0x6c, 0x74, 0x69, 0x61, 0x63, 0x71, 0x75, 0x69, 0x72, 0x69, 0x6e,
	0x67, 0x5f, 0x62, 0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65, 0x2e, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x42,
	0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00,
	0x42, 0x2d, 0x5a, 0x2b, 0x67, 0x69, 0x74, 0x2e, 0x6c, 0x6f, 0x63, 0x61, 0x6c, 0x2f, 0x73, 0x65,
	0x6e, 0x73, 0x69, 0x74, 0x69, 0x76, 0x65, 0x2f, 0x69, 0x6e, 0x6e, 0x65, 0x72, 0x70, 0x62, 0x2f,
	0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x2f, 0x67, 0x72, 0x70, 0x63, 0x62,
	0x08, 0x65, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x70, 0xe8, 0x07,
})

var (
	file_inner_processing_grpc_multiacquiring_balance_proto_rawDescOnce sync.Once
	file_inner_processing_grpc_multiacquiring_balance_proto_rawDescData []byte
)

func file_inner_processing_grpc_multiacquiring_balance_proto_rawDescGZIP() []byte {
	file_inner_processing_grpc_multiacquiring_balance_proto_rawDescOnce.Do(func() {
		file_inner_processing_grpc_multiacquiring_balance_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_inner_processing_grpc_multiacquiring_balance_proto_rawDesc), len(file_inner_processing_grpc_multiacquiring_balance_proto_rawDesc)))
	})
	return file_inner_processing_grpc_multiacquiring_balance_proto_rawDescData
}

var file_inner_processing_grpc_multiacquiring_balance_proto_msgTypes = make([]protoimpl.MessageInfo, 2)
var file_inner_processing_grpc_multiacquiring_balance_proto_goTypes = []any{
	(*CheckBalanceRequest)(nil),  // 0: processing.multiacquiring.multiacquiring_balance.CheckBalanceRequest
	(*CheckBalanceResponse)(nil), // 1: processing.multiacquiring.multiacquiring_balance.CheckBalanceResponse
	(*structpb.Struct)(nil),      // 2: google.protobuf.Struct
}
var file_inner_processing_grpc_multiacquiring_balance_proto_depIdxs = []int32{
	2, // 0: processing.multiacquiring.multiacquiring_balance.CheckBalanceRequest.config:type_name -> google.protobuf.Struct
	0, // 1: processing.multiacquiring.multiacquiring_balance.MultiacquiringBalance.CheckBalance:input_type -> processing.multiacquiring.multiacquiring_balance.CheckBalanceRequest
	1, // 2: processing.multiacquiring.multiacquiring_balance.MultiacquiringBalance.CheckBalance:output_type -> processing.multiacquiring.multiacquiring_balance.CheckBalanceResponse
	2, // [2:3] is the sub-list for method output_type
	1, // [1:2] is the sub-list for method input_type
	1, // [1:1] is the sub-list for extension type_name
	1, // [1:1] is the sub-list for extension extendee
	0, // [0:1] is the sub-list for field type_name
}

func init() { file_inner_processing_grpc_multiacquiring_balance_proto_init() }
func file_inner_processing_grpc_multiacquiring_balance_proto_init() {
	if File_inner_processing_grpc_multiacquiring_balance_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_inner_processing_grpc_multiacquiring_balance_proto_rawDesc), len(file_inner_processing_grpc_multiacquiring_balance_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   2,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_inner_processing_grpc_multiacquiring_balance_proto_goTypes,
		DependencyIndexes: file_inner_processing_grpc_multiacquiring_balance_proto_depIdxs,
		MessageInfos:      file_inner_processing_grpc_multiacquiring_balance_proto_msgTypes,
	}.Build()
	File_inner_processing_grpc_multiacquiring_balance_proto = out.File
	file_inner_processing_grpc_multiacquiring_balance_proto_goTypes = nil
	file_inner_processing_grpc_multiacquiring_balance_proto_depIdxs = nil
}
