package schema

type TerminalFilters struct {
	TerminaTarlanlId     *uint64 `form:"terminal_tarlan_id" validate:"omitempty"`
	AcquirerName         string  `form:"acquirer_name" validate:"omitempty"`
	ProjectName          string  `form:"project_name" validate:"omitempty"`
	TransactionTypeName  string  `form:"transaction_type_name" validate:"omitempty"`
	Transit              *bool   `form:"transit" validate:"omitempty"`
	Status               uint64  `form:"status" validate:"omitempty"`
	AcquirerTerminalName string  `form:"acquirer_terminal_name" validate:"omitempty"`
}
