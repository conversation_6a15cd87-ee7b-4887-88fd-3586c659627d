// Code generated by protoc-gen-go-mvp. DO NOT EDIT.
// versions:
// - protoc-gen-go-mvp v2.0.1
// created by mvp authors

package grpc

import (
	context "context"
	fmt "fmt"
	cntx "git.local/sensitive/mvp/pkg/cntx"
	ctxzap "github.com/grpc-ecosystem/go-grpc-middleware/logging/zap/ctxzap"
	zap "go.uber.org/zap"
	grpc "google.golang.org/grpc"
	structpb "google.golang.org/protobuf/types/known/structpb"
)

func file_inner_processing_grpc_view_crafter_proto_string_structpb_Value(
	label string,
	in map[string]*structpb.Value,
) zap.Field {
	var res []zap.Field
	for key, val := range in {
		res = append(res, file_inner_processing_grpc_view_crafter_proto_message_ValueToZap(fmt.Sprint(key), val))
	}

	return zap.Dict(label, res...)
}

func file_inner_processing_grpc_view_crafter_proto_message_ListValueToZap(
	label string,
	in *structpb.ListValue,
) zap.Field {
	if in == nil {
		return zap.Reflect(label, in)
	}

	return zap.Dict(
		label,
		file_inner_processing_grpc_view_crafter_proto_message_ValueSliceToZap("Values", in.GetValues()),
	)
}

func file_inner_processing_grpc_view_crafter_proto_enum_NullValueToZap(
	label string,
	in structpb.NullValue,
) zap.Field {
	str, ok := structpb.NullValue_name[int32(in)]
	if !ok {
		str = fmt.Sprintf("unknown structpb.NullValue value %d", in)
	}

	return zap.Dict(
		label,
		zap.Any("value", structpb.NullValue(in)),
		zap.Any("name", str),
	)
}

func file_inner_processing_grpc_view_crafter_proto_message_StructToZap(
	label string,
	in *structpb.Struct,
) zap.Field {
	if in == nil {
		return zap.Reflect(label, in)
	}

	return zap.Dict(
		label,
		file_inner_processing_grpc_view_crafter_proto_string_structpb_Value("Fields", in.GetFields()),
	)
}

func file_inner_processing_grpc_view_crafter_proto_message_ValueToZap(
	label string,
	in *structpb.Value,
) zap.Field {
	if in == nil {
		return zap.Reflect(label, in)
	}

	return zap.Dict(
		label,
		file_inner_processing_grpc_view_crafter_proto_enum_NullValueToZap("NullValue", in.GetNullValue()),
		zap.Any("NumberValue", in.GetNumberValue()),
		zap.Any("StringValue", in.GetStringValue()),
		zap.Any("BoolValue", in.GetBoolValue()),
		file_inner_processing_grpc_view_crafter_proto_message_StructToZap("StructValue", in.GetStructValue()),
		file_inner_processing_grpc_view_crafter_proto_message_ListValueToZap("ListValue", in.GetListValue()),
	)
}

func file_inner_processing_grpc_view_crafter_proto_message_ValueSliceToZap(
	label string,
	in []*structpb.Value,
) zap.Field {
	var res []zap.Field
	for index, val := range in {
		res = append(res, file_inner_processing_grpc_view_crafter_proto_message_ValueToZap(fmt.Sprint(index), val))
	}

	return zap.Dict(label, res...)
}

func file_inner_processing_grpc_view_crafter_proto_message_GetProjectFormInfoReqV1ToZap(
	label string,
	in *GetProjectFormInfoReqV1,
) zap.Field {
	if in == nil {
		return zap.Reflect(label, in)
	}

	return zap.Dict(
		label,
		zap.Any("MerchantId", in.GetMerchantId()),
		zap.Any("ProjectId", in.GetProjectId()),
		zap.Any("Lang", in.GetLang()),
	)
}

func file_inner_processing_grpc_view_crafter_proto_message_GetProjectFormInfoResV1ToZap(
	label string,
	in *GetProjectFormInfoResV1,
) zap.Field {
	if in == nil {
		return zap.Reflect(label, in)
	}

	return zap.Dict(
		label,
		zap.Any("MerchantId", in.GetMerchantId()),
		zap.Any("ProjectId", in.GetProjectId()),
		zap.Any("StoreName", in.GetStoreName()),
		zap.Any("LogoFilePath", in.GetLogoFilePath()),
		zap.Any("HasEmail", in.GetHasEmail()),
		zap.Any("RequiredEmail", in.GetRequiredEmail()),
		zap.Any("HasPhone", in.GetHasPhone()),
		zap.Any("RequiredPhone", in.GetRequiredPhone()),
		zap.Any("DefaultLanguage", in.GetDefaultLanguage()),
		zap.Any("Timeout", in.GetTimeout()),
		zap.Any("HasRedirect", in.GetHasRedirect()),
		zap.Any("HasDefaultCard", in.GetHasDefaultCard()),
		zap.Any("BannerDesktopFilePath", in.GetBannerDesktopFilePath()),
		zap.Any("BannerMobileFilePath", in.GetBannerMobileFilePath()),
		zap.Any("BannerUrl", in.GetBannerUrl()),
		zap.Any("ShowDescription", in.GetShowDescription()),
		file_inner_processing_grpc_view_crafter_proto_message_StructToZap("DescriptionLanguage", in.GetDescriptionLanguage()),
	)
}

var _ ViewCrafterServer = (*loggedViewCrafterServer)(nil)

func NewLoggedViewCrafterServer(srv ViewCrafterServer) ViewCrafterServer {
	return &loggedViewCrafterServer{srv: srv}
}

type loggedViewCrafterServer struct {
	UnimplementedViewCrafterServer

	srv ViewCrafterServer
}

func (s *loggedViewCrafterServer) GetProjectFormInfoV1(
	ctx context.Context,
	request *GetProjectFormInfoReqV1,
) (
	response *GetProjectFormInfoResV1,
	err error,
) {
	label := cntx.Begin(ctx, "ViewCrafterServer_GetProjectFormInfoV1")
	defer cntx.End(ctx, label)

	defer func() {
		if err != nil {
			ctxzap.AddFields(ctx, zap.Any(label+"error", err))
			return
		}

		ctxzap.AddFields(
			ctx,
			file_inner_processing_grpc_view_crafter_proto_message_GetProjectFormInfoResV1ToZap(label+"response", response),
		)
	}()

	ctxzap.AddFields(
		ctx,
		file_inner_processing_grpc_view_crafter_proto_message_GetProjectFormInfoReqV1ToZap(label+"request", request),
	)

	response, err = s.srv.GetProjectFormInfoV1(ctx, request)

	return
}

var _ ViewCrafterClient = (*loggedViewCrafterClient)(nil)

func NewLoggedViewCrafterClient(client ViewCrafterClient) ViewCrafterClient {
	return &loggedViewCrafterClient{client: client}
}

type loggedViewCrafterClient struct {
	client ViewCrafterClient
}

func (s *loggedViewCrafterClient) GetProjectFormInfoV1(
	ctx context.Context,
	request *GetProjectFormInfoReqV1,
	opts ...grpc.CallOption,
) (
	response *GetProjectFormInfoResV1,
	err error,
) {
	label := cntx.Begin(ctx, "ViewCrafterClient_GetProjectFormInfoV1")
	defer cntx.End(ctx, label)

	defer func() {
		if err != nil {
			ctxzap.AddFields(ctx, zap.Any(label+"error", err))
			return
		}

		ctxzap.AddFields(
			ctx,
			file_inner_processing_grpc_view_crafter_proto_message_GetProjectFormInfoResV1ToZap(label+"response", response),
		)
	}()

	ctxzap.AddFields(
		ctx,
		file_inner_processing_grpc_view_crafter_proto_message_GetProjectFormInfoReqV1ToZap(label+"request", request),
	)

	response, err = s.client.GetProjectFormInfoV1(ctx, request, opts...)

	return
}
