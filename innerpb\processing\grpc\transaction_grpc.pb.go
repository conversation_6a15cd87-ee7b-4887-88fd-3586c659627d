// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.5.1
// - protoc             v5.29.3
// source: inner/processing/grpc/transaction.proto

package grpc

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.64.0 or later.
const _ = grpc.SupportPackageIsVersion9

const (
	Transaction_UpdateTransactionStatus_FullMethodName                        = "/processing.transaction.transaction.Transaction/UpdateTransactionStatus"
	Transaction_UpdateCallbackStatus_FullMethodName                           = "/processing.transaction.transaction.Transaction/UpdateCallbackStatus"
	Transaction_GetTransactionsByPeriodAndStatus_FullMethodName               = "/processing.transaction.transaction.Transaction/GetTransactionsByPeriodAndStatus"
	Transaction_GetTransactionsByPeriodAndCallbackStatus_FullMethodName       = "/processing.transaction.transaction.Transaction/GetTransactionsByPeriodAndCallbackStatus"
	Transaction_GetTransactionsByFinalStatusAndPeriodWithLimit_FullMethodName = "/processing.transaction.transaction.Transaction/GetTransactionsByFinalStatusAndPeriodWithLimit"
	Transaction_GetPayInTransactionsByPeriodAndAcquirer_FullMethodName        = "/processing.transaction.transaction.Transaction/GetPayInTransactionsByPeriodAndAcquirer"
	Transaction_GetTransactionTypeByID_FullMethodName                         = "/processing.transaction.transaction.Transaction/GetTransactionTypeByID"
	Transaction_GetTransactionByID_FullMethodName                             = "/processing.transaction.transaction.Transaction/GetTransactionByID"
	Transaction_MakeAutoCharge_FullMethodName                                 = "/processing.transaction.transaction.Transaction/MakeAutoCharge"
	Transaction_SetRefundWaitingStatus_FullMethodName                         = "/processing.transaction.transaction.Transaction/SetRefundWaitingStatus"
	Transaction_GetTransactionsByProjectInfo_FullMethodName                   = "/processing.transaction.transaction.Transaction/GetTransactionsByProjectInfo"
	Transaction_CheckTransactionHash_FullMethodName                           = "/processing.transaction.transaction.Transaction/CheckTransactionHash"
	Transaction_CreateTransactionByPhone_FullMethodName                       = "/processing.transaction.transaction.Transaction/CreateTransactionByPhone"
	Transaction_GetByIDWithType_FullMethodName                                = "/processing.transaction.transaction.Transaction/GetByIDWithType"
	Transaction_UpdateStatus_FullMethodName                                   = "/processing.transaction.transaction.Transaction/UpdateStatus"
	Transaction_IncreaseTryCount_FullMethodName                               = "/processing.transaction.transaction.Transaction/IncreaseTryCount"
	Transaction_SaveAcquirerResponse_FullMethodName                           = "/processing.transaction.transaction.Transaction/SaveAcquirerResponse"
	Transaction_SendReceipt_FullMethodName                                    = "/processing.transaction.transaction.Transaction/SendReceipt"
	Transaction_SetAdditionalData_FullMethodName                              = "/processing.transaction.transaction.Transaction/SetAdditionalData"
	Transaction_CalculateAndUpdateTransactionAmount_FullMethodName            = "/processing.transaction.transaction.Transaction/CalculateAndUpdateTransactionAmount"
	Transaction_BillPayOut_FullMethodName                                     = "/processing.transaction.transaction.Transaction/BillPayOut"
)

// TransactionClient is the client API for Transaction service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type TransactionClient interface {
	UpdateTransactionStatus(ctx context.Context, in *UpdateTransactionStatusRequestV1, opts ...grpc.CallOption) (*emptypb.Empty, error)
	UpdateCallbackStatus(ctx context.Context, in *UpdateCallbackStatusRequestV1, opts ...grpc.CallOption) (*emptypb.Empty, error)
	GetTransactionsByPeriodAndStatus(ctx context.Context, in *GetTransactionsByStatusReqV1, opts ...grpc.CallOption) (*GetTransactionsResponse, error)
	GetTransactionsByPeriodAndCallbackStatus(ctx context.Context, in *GetTransactionsByCallbackStatusReqV1, opts ...grpc.CallOption) (*GetTransactionsResponse, error)
	GetTransactionsByFinalStatusAndPeriodWithLimit(ctx context.Context, in *GetTransactionsByFinalStatusAndPeriodWithLimitReqV1, opts ...grpc.CallOption) (*GetTransactionsResponse, error)
	GetPayInTransactionsByPeriodAndAcquirer(ctx context.Context, in *GetPayInTransactionsByPeriodAndAcquirerReqV1, opts ...grpc.CallOption) (*GetTransactionsResponse, error)
	GetTransactionTypeByID(ctx context.Context, in *GetTransactionTypeByIDRequestV1, opts ...grpc.CallOption) (*GetTransactionTypeByIDResponseV1, error)
	GetTransactionByID(ctx context.Context, in *GetTransactionByIDRequestV1, opts ...grpc.CallOption) (*TransactionDataV1, error)
	MakeAutoCharge(ctx context.Context, in *MakeAutoChargeRequestV1, opts ...grpc.CallOption) (*emptypb.Empty, error)
	SetRefundWaitingStatus(ctx context.Context, in *SetRefundWaitingStatusRequestV1, opts ...grpc.CallOption) (*SetRefundWaitingStatusResponseV1, error)
	GetTransactionsByProjectInfo(ctx context.Context, in *GetTransactionsByProjectInfoRequestV1, opts ...grpc.CallOption) (*GetTransactionsResponse, error)
	CheckTransactionHash(ctx context.Context, in *CheckTransactionHashRequestV1, opts ...grpc.CallOption) (*emptypb.Empty, error)
	CreateTransactionByPhone(ctx context.Context, in *CreateTransactionByPhoneRequest, opts ...grpc.CallOption) (*CreateTransactionByPhoneResponse, error)
	GetByIDWithType(ctx context.Context, in *GetByIDWithTypeRequest, opts ...grpc.CallOption) (*GetByIDWithTypeResponse, error)
	UpdateStatus(ctx context.Context, in *UpdateStatusRequest, opts ...grpc.CallOption) (*emptypb.Empty, error)
	IncreaseTryCount(ctx context.Context, in *IncreaseTryCountRequest, opts ...grpc.CallOption) (*emptypb.Empty, error)
	SaveAcquirerResponse(ctx context.Context, in *SaveAcquirerResponseRequest, opts ...grpc.CallOption) (*emptypb.Empty, error)
	SendReceipt(ctx context.Context, in *SendRequest, opts ...grpc.CallOption) (*emptypb.Empty, error)
	SetAdditionalData(ctx context.Context, in *SetAdditionalDataRequest, opts ...grpc.CallOption) (*emptypb.Empty, error)
	CalculateAndUpdateTransactionAmount(ctx context.Context, in *CalculateAndUpdateTransactionAmountRequest, opts ...grpc.CallOption) (*emptypb.Empty, error)
	BillPayOut(ctx context.Context, in *BillPayoutRequest, opts ...grpc.CallOption) (*emptypb.Empty, error)
}

type transactionClient struct {
	cc grpc.ClientConnInterface
}

func NewTransactionClient(cc grpc.ClientConnInterface) TransactionClient {
	return &transactionClient{cc}
}

func (c *transactionClient) UpdateTransactionStatus(ctx context.Context, in *UpdateTransactionStatusRequestV1, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, Transaction_UpdateTransactionStatus_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *transactionClient) UpdateCallbackStatus(ctx context.Context, in *UpdateCallbackStatusRequestV1, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, Transaction_UpdateCallbackStatus_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *transactionClient) GetTransactionsByPeriodAndStatus(ctx context.Context, in *GetTransactionsByStatusReqV1, opts ...grpc.CallOption) (*GetTransactionsResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetTransactionsResponse)
	err := c.cc.Invoke(ctx, Transaction_GetTransactionsByPeriodAndStatus_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *transactionClient) GetTransactionsByPeriodAndCallbackStatus(ctx context.Context, in *GetTransactionsByCallbackStatusReqV1, opts ...grpc.CallOption) (*GetTransactionsResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetTransactionsResponse)
	err := c.cc.Invoke(ctx, Transaction_GetTransactionsByPeriodAndCallbackStatus_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *transactionClient) GetTransactionsByFinalStatusAndPeriodWithLimit(ctx context.Context, in *GetTransactionsByFinalStatusAndPeriodWithLimitReqV1, opts ...grpc.CallOption) (*GetTransactionsResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetTransactionsResponse)
	err := c.cc.Invoke(ctx, Transaction_GetTransactionsByFinalStatusAndPeriodWithLimit_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *transactionClient) GetPayInTransactionsByPeriodAndAcquirer(ctx context.Context, in *GetPayInTransactionsByPeriodAndAcquirerReqV1, opts ...grpc.CallOption) (*GetTransactionsResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetTransactionsResponse)
	err := c.cc.Invoke(ctx, Transaction_GetPayInTransactionsByPeriodAndAcquirer_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *transactionClient) GetTransactionTypeByID(ctx context.Context, in *GetTransactionTypeByIDRequestV1, opts ...grpc.CallOption) (*GetTransactionTypeByIDResponseV1, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetTransactionTypeByIDResponseV1)
	err := c.cc.Invoke(ctx, Transaction_GetTransactionTypeByID_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *transactionClient) GetTransactionByID(ctx context.Context, in *GetTransactionByIDRequestV1, opts ...grpc.CallOption) (*TransactionDataV1, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(TransactionDataV1)
	err := c.cc.Invoke(ctx, Transaction_GetTransactionByID_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *transactionClient) MakeAutoCharge(ctx context.Context, in *MakeAutoChargeRequestV1, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, Transaction_MakeAutoCharge_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *transactionClient) SetRefundWaitingStatus(ctx context.Context, in *SetRefundWaitingStatusRequestV1, opts ...grpc.CallOption) (*SetRefundWaitingStatusResponseV1, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(SetRefundWaitingStatusResponseV1)
	err := c.cc.Invoke(ctx, Transaction_SetRefundWaitingStatus_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *transactionClient) GetTransactionsByProjectInfo(ctx context.Context, in *GetTransactionsByProjectInfoRequestV1, opts ...grpc.CallOption) (*GetTransactionsResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetTransactionsResponse)
	err := c.cc.Invoke(ctx, Transaction_GetTransactionsByProjectInfo_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *transactionClient) CheckTransactionHash(ctx context.Context, in *CheckTransactionHashRequestV1, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, Transaction_CheckTransactionHash_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *transactionClient) CreateTransactionByPhone(ctx context.Context, in *CreateTransactionByPhoneRequest, opts ...grpc.CallOption) (*CreateTransactionByPhoneResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(CreateTransactionByPhoneResponse)
	err := c.cc.Invoke(ctx, Transaction_CreateTransactionByPhone_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *transactionClient) GetByIDWithType(ctx context.Context, in *GetByIDWithTypeRequest, opts ...grpc.CallOption) (*GetByIDWithTypeResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetByIDWithTypeResponse)
	err := c.cc.Invoke(ctx, Transaction_GetByIDWithType_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *transactionClient) UpdateStatus(ctx context.Context, in *UpdateStatusRequest, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, Transaction_UpdateStatus_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *transactionClient) IncreaseTryCount(ctx context.Context, in *IncreaseTryCountRequest, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, Transaction_IncreaseTryCount_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *transactionClient) SaveAcquirerResponse(ctx context.Context, in *SaveAcquirerResponseRequest, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, Transaction_SaveAcquirerResponse_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *transactionClient) SendReceipt(ctx context.Context, in *SendRequest, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, Transaction_SendReceipt_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *transactionClient) SetAdditionalData(ctx context.Context, in *SetAdditionalDataRequest, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, Transaction_SetAdditionalData_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *transactionClient) CalculateAndUpdateTransactionAmount(ctx context.Context, in *CalculateAndUpdateTransactionAmountRequest, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, Transaction_CalculateAndUpdateTransactionAmount_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *transactionClient) BillPayOut(ctx context.Context, in *BillPayoutRequest, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, Transaction_BillPayOut_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// TransactionServer is the server API for Transaction service.
// All implementations must embed UnimplementedTransactionServer
// for forward compatibility.
type TransactionServer interface {
	UpdateTransactionStatus(context.Context, *UpdateTransactionStatusRequestV1) (*emptypb.Empty, error)
	UpdateCallbackStatus(context.Context, *UpdateCallbackStatusRequestV1) (*emptypb.Empty, error)
	GetTransactionsByPeriodAndStatus(context.Context, *GetTransactionsByStatusReqV1) (*GetTransactionsResponse, error)
	GetTransactionsByPeriodAndCallbackStatus(context.Context, *GetTransactionsByCallbackStatusReqV1) (*GetTransactionsResponse, error)
	GetTransactionsByFinalStatusAndPeriodWithLimit(context.Context, *GetTransactionsByFinalStatusAndPeriodWithLimitReqV1) (*GetTransactionsResponse, error)
	GetPayInTransactionsByPeriodAndAcquirer(context.Context, *GetPayInTransactionsByPeriodAndAcquirerReqV1) (*GetTransactionsResponse, error)
	GetTransactionTypeByID(context.Context, *GetTransactionTypeByIDRequestV1) (*GetTransactionTypeByIDResponseV1, error)
	GetTransactionByID(context.Context, *GetTransactionByIDRequestV1) (*TransactionDataV1, error)
	MakeAutoCharge(context.Context, *MakeAutoChargeRequestV1) (*emptypb.Empty, error)
	SetRefundWaitingStatus(context.Context, *SetRefundWaitingStatusRequestV1) (*SetRefundWaitingStatusResponseV1, error)
	GetTransactionsByProjectInfo(context.Context, *GetTransactionsByProjectInfoRequestV1) (*GetTransactionsResponse, error)
	CheckTransactionHash(context.Context, *CheckTransactionHashRequestV1) (*emptypb.Empty, error)
	CreateTransactionByPhone(context.Context, *CreateTransactionByPhoneRequest) (*CreateTransactionByPhoneResponse, error)
	GetByIDWithType(context.Context, *GetByIDWithTypeRequest) (*GetByIDWithTypeResponse, error)
	UpdateStatus(context.Context, *UpdateStatusRequest) (*emptypb.Empty, error)
	IncreaseTryCount(context.Context, *IncreaseTryCountRequest) (*emptypb.Empty, error)
	SaveAcquirerResponse(context.Context, *SaveAcquirerResponseRequest) (*emptypb.Empty, error)
	SendReceipt(context.Context, *SendRequest) (*emptypb.Empty, error)
	SetAdditionalData(context.Context, *SetAdditionalDataRequest) (*emptypb.Empty, error)
	CalculateAndUpdateTransactionAmount(context.Context, *CalculateAndUpdateTransactionAmountRequest) (*emptypb.Empty, error)
	BillPayOut(context.Context, *BillPayoutRequest) (*emptypb.Empty, error)
	mustEmbedUnimplementedTransactionServer()
}

// UnimplementedTransactionServer must be embedded to have
// forward compatible implementations.
//
// NOTE: this should be embedded by value instead of pointer to avoid a nil
// pointer dereference when methods are called.
type UnimplementedTransactionServer struct{}

func (UnimplementedTransactionServer) UpdateTransactionStatus(context.Context, *UpdateTransactionStatusRequestV1) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateTransactionStatus not implemented")
}
func (UnimplementedTransactionServer) UpdateCallbackStatus(context.Context, *UpdateCallbackStatusRequestV1) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateCallbackStatus not implemented")
}
func (UnimplementedTransactionServer) GetTransactionsByPeriodAndStatus(context.Context, *GetTransactionsByStatusReqV1) (*GetTransactionsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetTransactionsByPeriodAndStatus not implemented")
}
func (UnimplementedTransactionServer) GetTransactionsByPeriodAndCallbackStatus(context.Context, *GetTransactionsByCallbackStatusReqV1) (*GetTransactionsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetTransactionsByPeriodAndCallbackStatus not implemented")
}
func (UnimplementedTransactionServer) GetTransactionsByFinalStatusAndPeriodWithLimit(context.Context, *GetTransactionsByFinalStatusAndPeriodWithLimitReqV1) (*GetTransactionsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetTransactionsByFinalStatusAndPeriodWithLimit not implemented")
}
func (UnimplementedTransactionServer) GetPayInTransactionsByPeriodAndAcquirer(context.Context, *GetPayInTransactionsByPeriodAndAcquirerReqV1) (*GetTransactionsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetPayInTransactionsByPeriodAndAcquirer not implemented")
}
func (UnimplementedTransactionServer) GetTransactionTypeByID(context.Context, *GetTransactionTypeByIDRequestV1) (*GetTransactionTypeByIDResponseV1, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetTransactionTypeByID not implemented")
}
func (UnimplementedTransactionServer) GetTransactionByID(context.Context, *GetTransactionByIDRequestV1) (*TransactionDataV1, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetTransactionByID not implemented")
}
func (UnimplementedTransactionServer) MakeAutoCharge(context.Context, *MakeAutoChargeRequestV1) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method MakeAutoCharge not implemented")
}
func (UnimplementedTransactionServer) SetRefundWaitingStatus(context.Context, *SetRefundWaitingStatusRequestV1) (*SetRefundWaitingStatusResponseV1, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SetRefundWaitingStatus not implemented")
}
func (UnimplementedTransactionServer) GetTransactionsByProjectInfo(context.Context, *GetTransactionsByProjectInfoRequestV1) (*GetTransactionsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetTransactionsByProjectInfo not implemented")
}
func (UnimplementedTransactionServer) CheckTransactionHash(context.Context, *CheckTransactionHashRequestV1) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CheckTransactionHash not implemented")
}
func (UnimplementedTransactionServer) CreateTransactionByPhone(context.Context, *CreateTransactionByPhoneRequest) (*CreateTransactionByPhoneResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateTransactionByPhone not implemented")
}
func (UnimplementedTransactionServer) GetByIDWithType(context.Context, *GetByIDWithTypeRequest) (*GetByIDWithTypeResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetByIDWithType not implemented")
}
func (UnimplementedTransactionServer) UpdateStatus(context.Context, *UpdateStatusRequest) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateStatus not implemented")
}
func (UnimplementedTransactionServer) IncreaseTryCount(context.Context, *IncreaseTryCountRequest) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method IncreaseTryCount not implemented")
}
func (UnimplementedTransactionServer) SaveAcquirerResponse(context.Context, *SaveAcquirerResponseRequest) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SaveAcquirerResponse not implemented")
}
func (UnimplementedTransactionServer) SendReceipt(context.Context, *SendRequest) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SendReceipt not implemented")
}
func (UnimplementedTransactionServer) SetAdditionalData(context.Context, *SetAdditionalDataRequest) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SetAdditionalData not implemented")
}
func (UnimplementedTransactionServer) CalculateAndUpdateTransactionAmount(context.Context, *CalculateAndUpdateTransactionAmountRequest) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CalculateAndUpdateTransactionAmount not implemented")
}
func (UnimplementedTransactionServer) BillPayOut(context.Context, *BillPayoutRequest) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method BillPayOut not implemented")
}
func (UnimplementedTransactionServer) mustEmbedUnimplementedTransactionServer() {}
func (UnimplementedTransactionServer) testEmbeddedByValue()                     {}

// UnsafeTransactionServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to TransactionServer will
// result in compilation errors.
type UnsafeTransactionServer interface {
	mustEmbedUnimplementedTransactionServer()
}

func RegisterTransactionServer(s grpc.ServiceRegistrar, srv TransactionServer) {
	// If the following call pancis, it indicates UnimplementedTransactionServer was
	// embedded by pointer and is nil.  This will cause panics if an
	// unimplemented method is ever invoked, so we test this at initialization
	// time to prevent it from happening at runtime later due to I/O.
	if t, ok := srv.(interface{ testEmbeddedByValue() }); ok {
		t.testEmbeddedByValue()
	}
	s.RegisterService(&Transaction_ServiceDesc, srv)
}

func _Transaction_UpdateTransactionStatus_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateTransactionStatusRequestV1)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TransactionServer).UpdateTransactionStatus(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Transaction_UpdateTransactionStatus_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TransactionServer).UpdateTransactionStatus(ctx, req.(*UpdateTransactionStatusRequestV1))
	}
	return interceptor(ctx, in, info, handler)
}

func _Transaction_UpdateCallbackStatus_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateCallbackStatusRequestV1)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TransactionServer).UpdateCallbackStatus(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Transaction_UpdateCallbackStatus_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TransactionServer).UpdateCallbackStatus(ctx, req.(*UpdateCallbackStatusRequestV1))
	}
	return interceptor(ctx, in, info, handler)
}

func _Transaction_GetTransactionsByPeriodAndStatus_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetTransactionsByStatusReqV1)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TransactionServer).GetTransactionsByPeriodAndStatus(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Transaction_GetTransactionsByPeriodAndStatus_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TransactionServer).GetTransactionsByPeriodAndStatus(ctx, req.(*GetTransactionsByStatusReqV1))
	}
	return interceptor(ctx, in, info, handler)
}

func _Transaction_GetTransactionsByPeriodAndCallbackStatus_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetTransactionsByCallbackStatusReqV1)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TransactionServer).GetTransactionsByPeriodAndCallbackStatus(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Transaction_GetTransactionsByPeriodAndCallbackStatus_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TransactionServer).GetTransactionsByPeriodAndCallbackStatus(ctx, req.(*GetTransactionsByCallbackStatusReqV1))
	}
	return interceptor(ctx, in, info, handler)
}

func _Transaction_GetTransactionsByFinalStatusAndPeriodWithLimit_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetTransactionsByFinalStatusAndPeriodWithLimitReqV1)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TransactionServer).GetTransactionsByFinalStatusAndPeriodWithLimit(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Transaction_GetTransactionsByFinalStatusAndPeriodWithLimit_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TransactionServer).GetTransactionsByFinalStatusAndPeriodWithLimit(ctx, req.(*GetTransactionsByFinalStatusAndPeriodWithLimitReqV1))
	}
	return interceptor(ctx, in, info, handler)
}

func _Transaction_GetPayInTransactionsByPeriodAndAcquirer_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetPayInTransactionsByPeriodAndAcquirerReqV1)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TransactionServer).GetPayInTransactionsByPeriodAndAcquirer(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Transaction_GetPayInTransactionsByPeriodAndAcquirer_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TransactionServer).GetPayInTransactionsByPeriodAndAcquirer(ctx, req.(*GetPayInTransactionsByPeriodAndAcquirerReqV1))
	}
	return interceptor(ctx, in, info, handler)
}

func _Transaction_GetTransactionTypeByID_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetTransactionTypeByIDRequestV1)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TransactionServer).GetTransactionTypeByID(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Transaction_GetTransactionTypeByID_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TransactionServer).GetTransactionTypeByID(ctx, req.(*GetTransactionTypeByIDRequestV1))
	}
	return interceptor(ctx, in, info, handler)
}

func _Transaction_GetTransactionByID_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetTransactionByIDRequestV1)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TransactionServer).GetTransactionByID(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Transaction_GetTransactionByID_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TransactionServer).GetTransactionByID(ctx, req.(*GetTransactionByIDRequestV1))
	}
	return interceptor(ctx, in, info, handler)
}

func _Transaction_MakeAutoCharge_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(MakeAutoChargeRequestV1)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TransactionServer).MakeAutoCharge(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Transaction_MakeAutoCharge_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TransactionServer).MakeAutoCharge(ctx, req.(*MakeAutoChargeRequestV1))
	}
	return interceptor(ctx, in, info, handler)
}

func _Transaction_SetRefundWaitingStatus_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SetRefundWaitingStatusRequestV1)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TransactionServer).SetRefundWaitingStatus(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Transaction_SetRefundWaitingStatus_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TransactionServer).SetRefundWaitingStatus(ctx, req.(*SetRefundWaitingStatusRequestV1))
	}
	return interceptor(ctx, in, info, handler)
}

func _Transaction_GetTransactionsByProjectInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetTransactionsByProjectInfoRequestV1)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TransactionServer).GetTransactionsByProjectInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Transaction_GetTransactionsByProjectInfo_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TransactionServer).GetTransactionsByProjectInfo(ctx, req.(*GetTransactionsByProjectInfoRequestV1))
	}
	return interceptor(ctx, in, info, handler)
}

func _Transaction_CheckTransactionHash_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CheckTransactionHashRequestV1)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TransactionServer).CheckTransactionHash(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Transaction_CheckTransactionHash_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TransactionServer).CheckTransactionHash(ctx, req.(*CheckTransactionHashRequestV1))
	}
	return interceptor(ctx, in, info, handler)
}

func _Transaction_CreateTransactionByPhone_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateTransactionByPhoneRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TransactionServer).CreateTransactionByPhone(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Transaction_CreateTransactionByPhone_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TransactionServer).CreateTransactionByPhone(ctx, req.(*CreateTransactionByPhoneRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Transaction_GetByIDWithType_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetByIDWithTypeRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TransactionServer).GetByIDWithType(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Transaction_GetByIDWithType_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TransactionServer).GetByIDWithType(ctx, req.(*GetByIDWithTypeRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Transaction_UpdateStatus_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateStatusRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TransactionServer).UpdateStatus(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Transaction_UpdateStatus_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TransactionServer).UpdateStatus(ctx, req.(*UpdateStatusRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Transaction_IncreaseTryCount_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(IncreaseTryCountRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TransactionServer).IncreaseTryCount(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Transaction_IncreaseTryCount_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TransactionServer).IncreaseTryCount(ctx, req.(*IncreaseTryCountRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Transaction_SaveAcquirerResponse_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SaveAcquirerResponseRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TransactionServer).SaveAcquirerResponse(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Transaction_SaveAcquirerResponse_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TransactionServer).SaveAcquirerResponse(ctx, req.(*SaveAcquirerResponseRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Transaction_SendReceipt_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SendRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TransactionServer).SendReceipt(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Transaction_SendReceipt_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TransactionServer).SendReceipt(ctx, req.(*SendRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Transaction_SetAdditionalData_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SetAdditionalDataRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TransactionServer).SetAdditionalData(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Transaction_SetAdditionalData_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TransactionServer).SetAdditionalData(ctx, req.(*SetAdditionalDataRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Transaction_CalculateAndUpdateTransactionAmount_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CalculateAndUpdateTransactionAmountRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TransactionServer).CalculateAndUpdateTransactionAmount(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Transaction_CalculateAndUpdateTransactionAmount_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TransactionServer).CalculateAndUpdateTransactionAmount(ctx, req.(*CalculateAndUpdateTransactionAmountRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Transaction_BillPayOut_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BillPayoutRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TransactionServer).BillPayOut(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Transaction_BillPayOut_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TransactionServer).BillPayOut(ctx, req.(*BillPayoutRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// Transaction_ServiceDesc is the grpc.ServiceDesc for Transaction service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var Transaction_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "processing.transaction.transaction.Transaction",
	HandlerType: (*TransactionServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "UpdateTransactionStatus",
			Handler:    _Transaction_UpdateTransactionStatus_Handler,
		},
		{
			MethodName: "UpdateCallbackStatus",
			Handler:    _Transaction_UpdateCallbackStatus_Handler,
		},
		{
			MethodName: "GetTransactionsByPeriodAndStatus",
			Handler:    _Transaction_GetTransactionsByPeriodAndStatus_Handler,
		},
		{
			MethodName: "GetTransactionsByPeriodAndCallbackStatus",
			Handler:    _Transaction_GetTransactionsByPeriodAndCallbackStatus_Handler,
		},
		{
			MethodName: "GetTransactionsByFinalStatusAndPeriodWithLimit",
			Handler:    _Transaction_GetTransactionsByFinalStatusAndPeriodWithLimit_Handler,
		},
		{
			MethodName: "GetPayInTransactionsByPeriodAndAcquirer",
			Handler:    _Transaction_GetPayInTransactionsByPeriodAndAcquirer_Handler,
		},
		{
			MethodName: "GetTransactionTypeByID",
			Handler:    _Transaction_GetTransactionTypeByID_Handler,
		},
		{
			MethodName: "GetTransactionByID",
			Handler:    _Transaction_GetTransactionByID_Handler,
		},
		{
			MethodName: "MakeAutoCharge",
			Handler:    _Transaction_MakeAutoCharge_Handler,
		},
		{
			MethodName: "SetRefundWaitingStatus",
			Handler:    _Transaction_SetRefundWaitingStatus_Handler,
		},
		{
			MethodName: "GetTransactionsByProjectInfo",
			Handler:    _Transaction_GetTransactionsByProjectInfo_Handler,
		},
		{
			MethodName: "CheckTransactionHash",
			Handler:    _Transaction_CheckTransactionHash_Handler,
		},
		{
			MethodName: "CreateTransactionByPhone",
			Handler:    _Transaction_CreateTransactionByPhone_Handler,
		},
		{
			MethodName: "GetByIDWithType",
			Handler:    _Transaction_GetByIDWithType_Handler,
		},
		{
			MethodName: "UpdateStatus",
			Handler:    _Transaction_UpdateStatus_Handler,
		},
		{
			MethodName: "IncreaseTryCount",
			Handler:    _Transaction_IncreaseTryCount_Handler,
		},
		{
			MethodName: "SaveAcquirerResponse",
			Handler:    _Transaction_SaveAcquirerResponse_Handler,
		},
		{
			MethodName: "SendReceipt",
			Handler:    _Transaction_SendReceipt_Handler,
		},
		{
			MethodName: "SetAdditionalData",
			Handler:    _Transaction_SetAdditionalData_Handler,
		},
		{
			MethodName: "CalculateAndUpdateTransactionAmount",
			Handler:    _Transaction_CalculateAndUpdateTransactionAmount_Handler,
		},
		{
			MethodName: "BillPayOut",
			Handler:    _Transaction_BillPayOut_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "inner/processing/grpc/transaction.proto",
}
