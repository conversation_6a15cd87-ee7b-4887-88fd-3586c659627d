package v1

import (
	"context"
	"io"
	"strconv"

	"github.com/gin-gonic/gin"
	"google.golang.org/protobuf/types/known/timestamppb"

	goevents "git.local/sensitive/innerpb/processing/events"
	"git.local/sensitive/innerpb/processing/goerr"
	"git.local/sensitive/pkg/middlewares"
	"git.local/sensitive/processing/acquirer/schema"
	"git.local/sensitive/sdk/dog"
)

const (
	ruleEventlogCollection        = "acquirer_rule"
	createRuleEventlogMessage     = "Создание правила и выставление процентажа"
	createBaseRuleEventlogMessage = "Создание базового правила и выставление процентажа"
	activateRuleEventlogMessage   = "Активация правила"
	deactivateRuleEventlogMessage = "Деактивация правила"
	moveUpRuleEventlogMessage     = "Увеличение веса правила"
	moveDownRuleEventlogMessage   = "Уменьшение веса правила"
)

func (h *Handler) initRulesHandler(v1 *gin.RouterGroup) {
	v1.POST("rule", middlewares.GinErrorHandle(h.createRule))
	v1.POST("base-rule", middlewares.GinErrorHandle(h.createBaseRule))
	v1.PATCH("rule/:rule_id/activate", middlewares.GinErrorHandle(h.activate))
	v1.PATCH("rule/:rule_id/deactivate", middlewares.GinErrorHandle(h.deactivate))
	v1.GET("rules", middlewares.Pagination(), middlewares.GinErrorHandle(h.getList))
	v1.PATCH("rule/:rule_id/move-up", middlewares.GinErrorHandle(h.moveUp))
	v1.PATCH("rule/:rule_id/move-down", middlewares.GinErrorHandle(h.moveDown))
}

// createRule
// @Summary Создание правила и выставление процентажа
// @Description Для начала нужно создать базовое правило по роуту api/v1/base-rule
// @Accept json
// @Produce json
// @Param data body schema.CreateRuleRequest true "CreateRuleRequest Data"
// @Success 200 {object} middlewares.Response[model.Rule]
// @Failure 400 {object} middlewares.Response[middlewares.Empty]
// @Security bearerAuth
// @tags balancer
// @Router /api/v1/rule [post]
func (h *Handler) createRule(c *gin.Context) (err error) {
	ctx, span := dog.CreateSpan(c.Request.Context(), "Handler_createRule")
	defer span.End()

	var cr schema.CreateRuleRequest
	if err = c.ShouldBind(&cr); err != nil {
		return goerr.ErrRequestValidation.WithErr(err).WithResult(map[string]string{
			"error": err.Error(),
		})
	}

	if err = cr.Validate(); err != nil {
		return goerr.ErrRequestValidation.WithErr(err).WithResult(map[string]string{
			"error": err.Error(),
		})
	}

	if err = cr.Percentages.Validate(); err != nil {
		return goerr.ErrRequestValidation.WithErr(err).WithResult(map[string]string{
			"error": err.Error(),
		})
	}

	rule, err := h.services.Rule.Create(ctx, cr)
	if err != nil {
		return err
	}

	bodyBytes, err := io.ReadAll(c.Request.Body)
	if err != nil {
		dog.SendError(context.TODO(), err, dog.WithExt(map[string]interface{}{"eventlog": "save_log"}))
	}

	err = goevents.NewSaveLogPublisher(dog.JSContext())(ctx, &goevents.SaveLog{
		Method:     c.Request.Method,
		Url:        c.Request.URL.Path,
		EventAt:    timestamppb.Now(),
		EntityId:   c.Request.Header.Get("x-entity-id"),
		Request:    string(bodyBytes),
		UserEmail:  c.Request.Header.Get("x-email"),
		UserId:     c.Request.Header.Get("x-user-id"),
		Message:    createRuleEventlogMessage,
		Payload:    "",
		Collection: ruleEventlogCollection,
	})
	if err != nil {
		dog.SendError(context.TODO(), err, dog.WithExt(map[string]interface{}{"eventlog": "save_log"}))
	}

	return middlewares.Respond(rule, c)
}

// createBaseRule
// @Summary Создание базового правила и выставление процентажа
// @Description Базовое правило состоит только из project_id, payment_type (он всегда активен и is_base = true)
// @Accept json
// @Produce json
// @Param data body schema.CreateBaseRuleRequest true "CreateBaseRuleRequest Data"
// @Success 200 {object} middlewares.Response[model.Rule]
// @Failure 400 {object} middlewares.Response[middlewares.Empty]
// @tags balancer
// @Router /api/v1/base-rule [post]
func (h *Handler) createBaseRule(c *gin.Context) (err error) {
	ctx, span := dog.CreateSpan(c.Request.Context(), "Handler_createBaseRule")
	defer span.End()

	var cbr schema.CreateBaseRuleRequest
	if err = c.ShouldBind(&cbr); err != nil {
		return goerr.ErrRequestValidation.WithErr(err).WithResult(map[string]string{
			"error": err.Error(),
		})
	}

	if err = cbr.Validate(); err != nil {
		return goerr.ErrRequestValidation.WithErr(err).WithResult(map[string]string{
			"error": err.Error(),
		})
	}

	if err = cbr.Percentages.Validate(); err != nil {
		return goerr.ErrRequestValidation.WithErr(err).WithResult(map[string]string{
			"error": err.Error(),
		})
	}

	rule, err := h.services.Rule.CreateBase(ctx, cbr)
	if err != nil {
		return err
	}

	bodyBytes, err := io.ReadAll(c.Request.Body)
	if err != nil {
		dog.SendError(context.TODO(), err, dog.WithExt(map[string]interface{}{"eventlog": "save_log"}))
	}

	err = goevents.NewSaveLogPublisher(dog.JSContext())(ctx, &goevents.SaveLog{
		Method:     c.Request.Method,
		Url:        c.Request.URL.Path,
		EventAt:    timestamppb.Now(),
		EntityId:   c.Request.Header.Get("x-entity-id"),
		Request:    string(bodyBytes),
		UserEmail:  c.Request.Header.Get("x-email"),
		UserId:     c.Request.Header.Get("x-user-id"),
		Message:    createBaseRuleEventlogMessage,
		Payload:    "",
		Collection: ruleEventlogCollection,
	})
	if err != nil {
		dog.SendError(context.TODO(), err, dog.WithExt(map[string]interface{}{"eventlog": "save_log"}))
	}

	return middlewares.Respond(rule, c)
}

// getList
// @Summary Получение списка правил с эквайерами
// @Description Возвращает список с пагинацией и фильтрацией
// @Param date query schema.RuleListRequest true "Data"
// @Param per_page query int false "per_page"
// @Param page query int false "page"
// @Produce json
// @Success 200 {object} middlewares.Response[[]model.Rule]
// @Failure 400 {object} middlewares.Response[middlewares.Empty]
// @Header 200 {int} x-per-page "1"
// @Header 200 {int} x-page "1"
// @Header 200 {int} x-total "11"
// @Security bearerAuth
// @tags balancer
// @Router /api/v1/rules [get]
func (h *Handler) getList(c *gin.Context) (err error) {
	ctx, span := dog.CreateSpan(c.Request.Context(), "Handler_getList")
	defer span.End()

	var rl schema.RuleListRequest

	pagination := h.getPagination(c)

	if err = c.ShouldBindQuery(&rl); err != nil {
		return goerr.ErrRequestValidation.WithErr(err).WithResult(map[string]string{
			"error": err.Error(),
		})
	}

	if err = rl.RuleListValidation(); err != nil {
		return goerr.ErrRequestValidation.WithErr(err).WithResult(map[string]string{
			"error": err.Error(),
		})
	}

	list, err := h.services.Rule.GetList(ctx, pagination, rl)
	if err != nil {
		return err
	}

	pagination.SetHeader(c)

	return middlewares.Respond(list, c)
}

// activate
// @Summary Активация правила
// @Produce json
// @Param rule_id path int true "Rule ID"
// @Failure 400 {object} middlewares.Response[middlewares.Empty]
// @Success 200 {object} middlewares.Response[middlewares.Empty]
// @Security bearerAuth
// @tags balancer
// @Router /api/v1/rule/{rule_id}/activate [patch]
func (h *Handler) activate(c *gin.Context) (err error) {
	ctx, span := dog.CreateSpan(c.Request.Context(), "Handler_activate")
	defer span.End()

	ruleID, err := strconv.ParseUint(c.Param("rule_id"), 10, 64)

	if err != nil {
		return goerr.ErrInvalidPathParam.WithErr(err).WithCtx(ctx).WithResult(map[string]string{
			"param":   "rule_id",
			"message": "must be number",
		})
	}

	if err = h.services.RuleActivator.Activate(ctx, ruleID); err != nil {
		return err
	}

	bodyBytes, err := io.ReadAll(c.Request.Body)
	if err != nil {
		dog.SendError(context.TODO(), err, dog.WithExt(map[string]interface{}{"eventlog": "save_log"}))
	}

	err = goevents.NewSaveLogPublisher(dog.JSContext())(ctx, &goevents.SaveLog{
		Method:     c.Request.Method,
		Url:        c.Request.URL.Path,
		EventAt:    timestamppb.Now(),
		EntityId:   c.Request.Header.Get("x-entity-id"),
		Request:    string(bodyBytes),
		UserEmail:  c.Request.Header.Get("x-email"),
		UserId:     c.Request.Header.Get("x-user-id"),
		Message:    activateRuleEventlogMessage,
		Payload:    "",
		Collection: ruleEventlogCollection,
	})
	if err != nil {
		dog.SendError(context.TODO(), err, dog.WithExt(map[string]interface{}{"eventlog": "save_log"}))
	}

	return middlewares.Respond("success", c)
}

// deactivate
// @Summary Деактивация правила
// @Param rule_id path int true "Rule ID"
// @Produce json
// @Failure 400 {object} middlewares.Response[middlewares.Empty]
// @Success 200 {object} middlewares.Response[middlewares.Empty]
// @Security bearerAuth
// @tags balancer
// @Router /api/v1/rule/{rule_id}/deactivate [patch]
func (h *Handler) deactivate(c *gin.Context) (err error) {
	ctx, span := dog.CreateSpan(c.Request.Context(), "Handler_deactivate")
	defer span.End()

	ruleID, err := strconv.ParseUint(c.Param("rule_id"), 10, 64)
	if err != nil {
		return goerr.ErrInvalidPathParam.WithErr(err).WithCtx(ctx).WithResult(map[string]string{
			"param":   "rule_id",
			"message": "must be number",
		})
	}

	if err = h.services.RuleActivator.Deactivate(ctx, ruleID); err != nil {
		return err
	}

	bodyBytes, err := io.ReadAll(c.Request.Body)
	if err != nil {
		dog.SendError(context.TODO(), err, dog.WithExt(map[string]interface{}{"eventlog": "save_log"}))
	}

	err = goevents.NewSaveLogPublisher(dog.JSContext())(ctx, &goevents.SaveLog{
		Method:     c.Request.Method,
		Url:        c.Request.URL.Path,
		EventAt:    timestamppb.Now(),
		EntityId:   c.Request.Header.Get("x-entity-id"),
		Request:    string(bodyBytes),
		UserEmail:  c.Request.Header.Get("x-email"),
		UserId:     c.Request.Header.Get("x-user-id"),
		Message:    deactivateRuleEventlogMessage,
		Payload:    "",
		Collection: ruleEventlogCollection,
	})
	if err != nil {
		dog.SendError(context.TODO(), err, dog.WithExt(map[string]interface{}{"eventlog": "save_log"}))
	}

	return middlewares.Respond("success", c)
}

// RuleUp увеличивает вес правила, перемещая его вверх.
// @Summary Увеличение веса правила
// @Produce json
// @Param rule_id path int true "ID правила"
// @Failure 400 {object} middlewares.Response[middlewares.Empty]
// @Success 200 {object} middlewares.Response[middlewares.Empty]
// @Security bearerAuth
// @tags balancer
// @Router /api/v1/rule/{rule_id}/move-up [patch]
func (h *Handler) moveUp(c *gin.Context) (err error) {
	ctx, span := dog.CreateSpan(c.Request.Context(), "Handler_moveUp")
	defer span.End()

	ruleID, err := strconv.ParseUint(c.Param("rule_id"), 10, 64)
	if err != nil {
		return goerr.ErrRequestValidation.WithErr(err).WithResult(map[string]string{
			"error": err.Error(),
		})
	}

	if err = h.services.Balancer.RuleUp(ctx, ruleID); err != nil {
		return err
	}

	bodyBytes, err := io.ReadAll(c.Request.Body)
	if err != nil {
		dog.SendError(context.TODO(), err, dog.WithExt(map[string]interface{}{"eventlog": "save_log"}))
	}

	err = goevents.NewSaveLogPublisher(dog.JSContext())(ctx, &goevents.SaveLog{
		Method:     c.Request.Method,
		Url:        c.Request.URL.Path,
		EventAt:    timestamppb.Now(),
		EntityId:   c.Request.Header.Get("x-entity-id"),
		Request:    string(bodyBytes),
		UserEmail:  c.Request.Header.Get("x-email"),
		UserId:     c.Request.Header.Get("x-user-id"),
		Message:    moveUpRuleEventlogMessage,
		Payload:    "",
		Collection: ruleEventlogCollection,
	})
	if err != nil {
		dog.SendError(context.TODO(), err, dog.WithExt(map[string]interface{}{"eventlog": "save_log"}))
	}

	return middlewares.Respond("success", c)
}

// RuleDown уменьшает вес правила, перемещая его вниз.
// @Summary Уменьшение веса правила
// @Produce json
// @Param rule_id path int true "ID правила"
// @Failure 400 {object}  middlewares.Response[middlewares.Empty]
// @Success 200 {object}  middlewares.Response[middlewares.Empty]
// @Security bearerAuth
// @tags balancer
// @Router /api/v1/rule/{rule_id}/move-down [patch]
func (h *Handler) moveDown(c *gin.Context) (err error) {
	ctx, span := dog.CreateSpan(c.Request.Context(), "Handler_moveDown")
	defer span.End()

	ruleID, err := strconv.ParseUint(c.Param("rule_id"), 10, 64)
	if err != nil {
		return goerr.ErrRequestValidation.WithErr(err).WithResult(map[string]string{
			"error": err.Error(),
		})
	}

	if err = h.services.Balancer.RuleDown(ctx, ruleID); err != nil {
		return err
	}

	bodyBytes, err := io.ReadAll(c.Request.Body)
	if err != nil {
		dog.SendError(context.TODO(), err, dog.WithExt(map[string]interface{}{"eventlog": "save_log"}))
	}

	err = goevents.NewSaveLogPublisher(dog.JSContext())(ctx, &goevents.SaveLog{
		Method:     c.Request.Method,
		Url:        c.Request.URL.Path,
		EventAt:    timestamppb.Now(),
		EntityId:   c.Request.Header.Get("x-entity-id"),
		Request:    string(bodyBytes),
		UserEmail:  c.Request.Header.Get("x-email"),
		UserId:     c.Request.Header.Get("x-user-id"),
		Message:    moveDownRuleEventlogMessage,
		Payload:    "",
		Collection: ruleEventlogCollection,
	})
	if err != nil {
		dog.SendError(context.TODO(), err, dog.WithExt(map[string]interface{}{"eventlog": "save_log"}))
	}

	return middlewares.Respond("success", c)
}
