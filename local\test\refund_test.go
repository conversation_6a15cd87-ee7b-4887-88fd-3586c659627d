package test

import (
	"bytes"
	"encoding/json"
	"io"
	"net/http"
	"testing"

	"github.com/stretchr/testify/assert"

	"git.local/sensitive/local/internal/domain"
)

func TestRefund(t *testing.T) {
	t.Skip()

	testData := []domain.SmokeData[*domain.Empty]{
		{
			Name: "test_refund",
			Expected: domain.Expected[*domain.Empty]{
				Message:        "success",
				HttpStatusCode: http.StatusOK,
			},
		},
	}

	for _, data := range testData {
		t.Run(data.Name, func(t *testing.T) {
			transactionId, hash := generatePayInTransaction(t, &domain.GenerateTransactionRequest{
				Amount:             10,
				ProjectID:          devProjectID,
				MerchantID:         devMerchantID,
				ProjectClientID:    devProjectClientID,
				FailureRedirectUrl: "https://www.youtube.com",
				SuccessRedirectUrl: "https://www.youtube.com",
				CallbackUrl:        devCallbackUrl,
				Description:        "afk_afk_afk",
				ProjectReferenceID: generateSecretKey(),
			}, domain.SmokeData[string]{
				Expected: domain.Expected[string]{
					HttpStatusCode: http.StatusOK,
				},
			})
			makePayIn(t, &domain.MakePayInRequest{
				TransactionID:   transactionId,
				TransactionHash: hash,
				UserEmail:       "<EMAIL>",
				UserPhone:       "+77771581078", // dont call pls
				EncryptedCard:   devEncryptedCard,
			}, &domain.Expected[*domain.MakePayInResponse]{
				HttpStatusCode: http.StatusOK,
				Data: &domain.MakePayInResponse{
					TransactionID: transactionId,
				},
			})
			makeRefund(t, &domain.MakeRefundRequest{TransactionID: transactionId, Amount: 5}, &data.Expected)
		})
	}
}

func makeRefund(t *testing.T, req *domain.MakeRefundRequest, expected *domain.Expected[*domain.Empty]) {
	body, err := json.Marshal(req)
	if err != nil {
		t.Fatal(err)
	}

	request, err := http.NewRequest(http.MethodPost, domainURL+UrlPath, bytes.NewBuffer(body))
	if err != nil {
		t.Fatal(err)
	}

	request.Header.Add("Content-Type", "application/json")
	request.Header.Add("Authorization", "Bearer "+generateToken(req, ""))

	resp, err := http.DefaultClient.Do(request)
	if err != nil {
		t.Fatal(err)
	}

	resBody, err := io.ReadAll(resp.Body)
	if err != nil {
		t.Fatal(err)
	}
	defer resp.Body.Close()

	res := domain.Response[string]{}

	if err = json.Unmarshal(resBody, &res); err != nil {
		t.Fatal(err)
	}

	assert.Equal(t, expected.HttpStatusCode, resp.StatusCode, "http status code")
}
