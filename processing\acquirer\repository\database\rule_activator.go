package database

import (
	"context"
	"errors"

	"gorm.io/gorm"

	"git.local/sensitive/innerpb/processing/goerr"
	"git.local/sensitive/processing/acquirer/model"
	"git.local/sensitive/sdk/dog"
)

type RuleActivatorDB struct {
	db *gorm.DB
}

func NewRuleActivatorDB(db *gorm.DB) RuleActivator {
	return &RuleActivatorDB{
		db: db,
	}
}

func (r *RuleActivatorDB) Activate(ctx context.Context, ruleID uint64) (err error) {
	ctx, span := dog.CreateSpan(ctx, "RuleActivatorDB_Activate")
	defer span.End()

	if err = r.db.WithContext(ctx).
		Model(&model.Rule{}).
		Where("id = ?", ruleID).
		Update("is_active", true).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return goerr.ErrRuleNotFound.WithCtx(ctx)
		}

		return goerr.ErrDbUnexpected.WithErr(err).WithCtx(ctx)
	}

	return nil
}

func (r *RuleActivatorDB) Deactivate(ctx context.Context, ruleID uint64) (err error) {
	ctx, span := dog.CreateSpan(ctx, "RuleActivatorDB_Deactivate")
	defer span.End()

	if err = r.db.WithContext(ctx).
		Model(&model.Rule{}).
		Where("id = ?", ruleID).
		Update("is_active", false).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return goerr.ErrRuleNotFound
		}

		return goerr.ErrDbUnexpected.WithErr(err).WithCtx(ctx)
	}

	return nil
}
