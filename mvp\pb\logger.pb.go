// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.5
// 	protoc        v5.29.3
// source: proto/logger.proto

package pb

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	descriptorpb "google.golang.org/protobuf/types/descriptorpb"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type LoggerLevel int32

const (
	LoggerLevel_Full                LoggerLevel = 0
	LoggerLevel_SkipAll             LoggerLevel = 2
	LoggerLevel_SkipSlice           LoggerLevel = 3   // skips recursive logging fields if they is slice type
	LoggerLevel_SkipMap             LoggerLevel = 4   // skips recursive logging fields if they is map type
	LoggerLevel_SkipMessageChildren LoggerLevel = 5   // skips recursive logging fields if they is message type
	LoggerLevel_Hidden              LoggerLevel = 100 // skip logging
)

// Enum value maps for LoggerLevel.
var (
	LoggerLevel_name = map[int32]string{
		0:   "Full",
		2:   "SkipAll",
		3:   "SkipSlice",
		4:   "SkipMap",
		5:   "SkipMessageChildren",
		100: "Hidden",
	}
	LoggerLevel_value = map[string]int32{
		"Full":                0,
		"SkipAll":             2,
		"SkipSlice":           3,
		"SkipMap":             4,
		"SkipMessageChildren": 5,
		"Hidden":              100,
	}
)

func (x LoggerLevel) Enum() *LoggerLevel {
	p := new(LoggerLevel)
	*p = x
	return p
}

func (x LoggerLevel) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (LoggerLevel) Descriptor() protoreflect.EnumDescriptor {
	return file_proto_logger_proto_enumTypes[0].Descriptor()
}

func (LoggerLevel) Type() protoreflect.EnumType {
	return &file_proto_logger_proto_enumTypes[0]
}

func (x LoggerLevel) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use LoggerLevel.Descriptor instead.
func (LoggerLevel) EnumDescriptor() ([]byte, []int) {
	return file_proto_logger_proto_rawDescGZIP(), []int{0}
}

var file_proto_logger_proto_extTypes = []protoimpl.ExtensionInfo{
	{
		ExtendedType:  (*descriptorpb.EnumOptions)(nil),
		ExtensionType: ([]LoggerLevel)(nil),
		Field:         71000001,
		Name:          "mvp.EnumLoggerLevel",
		Tag:           "varint,71000001,rep,packed,name=EnumLoggerLevel,enum=mvp.LoggerLevel",
		Filename:      "proto/logger.proto",
	},
	{
		ExtendedType:  (*descriptorpb.MessageOptions)(nil),
		ExtensionType: ([]LoggerLevel)(nil),
		Field:         71000002,
		Name:          "mvp.MessageLoggerLevel",
		Tag:           "varint,71000002,rep,packed,name=MessageLoggerLevel,enum=mvp.LoggerLevel",
		Filename:      "proto/logger.proto",
	},
	{
		ExtendedType:  (*descriptorpb.FieldOptions)(nil),
		ExtensionType: ([]LoggerLevel)(nil),
		Field:         71000003,
		Name:          "mvp.FieldLoggerLevel",
		Tag:           "varint,71000003,rep,packed,name=FieldLoggerLevel,enum=mvp.LoggerLevel",
		Filename:      "proto/logger.proto",
	},
	{
		ExtendedType:  (*descriptorpb.ServiceOptions)(nil),
		ExtensionType: ([]LoggerLevel)(nil),
		Field:         71000004,
		Name:          "mvp.ServiceLoggerLevel",
		Tag:           "varint,71000004,rep,packed,name=ServiceLoggerLevel,enum=mvp.LoggerLevel",
		Filename:      "proto/logger.proto",
	},
	{
		ExtendedType:  (*descriptorpb.MethodOptions)(nil),
		ExtensionType: ([]LoggerLevel)(nil),
		Field:         71000005,
		Name:          "mvp.MethodLoggerLevel",
		Tag:           "varint,71000005,rep,packed,name=MethodLoggerLevel,enum=mvp.LoggerLevel",
		Filename:      "proto/logger.proto",
	},
}

// Extension fields to descriptorpb.EnumOptions.
var (
	// repeated mvp.LoggerLevel EnumLoggerLevel = 71000001;
	E_EnumLoggerLevel = &file_proto_logger_proto_extTypes[0]
)

// Extension fields to descriptorpb.MessageOptions.
var (
	// repeated mvp.LoggerLevel MessageLoggerLevel = 71000002;
	E_MessageLoggerLevel = &file_proto_logger_proto_extTypes[1]
)

// Extension fields to descriptorpb.FieldOptions.
var (
	// repeated mvp.LoggerLevel FieldLoggerLevel = 71000003;
	E_FieldLoggerLevel = &file_proto_logger_proto_extTypes[2]
)

// Extension fields to descriptorpb.ServiceOptions.
var (
	// repeated mvp.LoggerLevel ServiceLoggerLevel = 71000004;
	E_ServiceLoggerLevel = &file_proto_logger_proto_extTypes[3]
)

// Extension fields to descriptorpb.MethodOptions.
var (
	// repeated mvp.LoggerLevel MethodLoggerLevel = 71000005;
	E_MethodLoggerLevel = &file_proto_logger_proto_extTypes[4]
)

var File_proto_logger_proto protoreflect.FileDescriptor

var file_proto_logger_proto_rawDesc = string([]byte{
	0x0a, 0x12, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f, 0x6c, 0x6f, 0x67, 0x67, 0x65, 0x72, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x12, 0x03, 0x6d, 0x76, 0x70, 0x1a, 0x20, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x64, 0x65, 0x73, 0x63, 0x72,
	0x69, 0x70, 0x74, 0x6f, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2a, 0x6b, 0x0a, 0x0b, 0x4c,
	0x6f, 0x67, 0x67, 0x65, 0x72, 0x4c, 0x65, 0x76, 0x65, 0x6c, 0x12, 0x08, 0x0a, 0x04, 0x46, 0x75,
	0x6c, 0x6c, 0x10, 0x00, 0x12, 0x0b, 0x0a, 0x07, 0x53, 0x6b, 0x69, 0x70, 0x41, 0x6c, 0x6c, 0x10,
	0x02, 0x12, 0x0d, 0x0a, 0x09, 0x53, 0x6b, 0x69, 0x70, 0x53, 0x6c, 0x69, 0x63, 0x65, 0x10, 0x03,
	0x12, 0x0b, 0x0a, 0x07, 0x53, 0x6b, 0x69, 0x70, 0x4d, 0x61, 0x70, 0x10, 0x04, 0x12, 0x17, 0x0a,
	0x13, 0x53, 0x6b, 0x69, 0x70, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x43, 0x68, 0x69, 0x6c,
	0x64, 0x72, 0x65, 0x6e, 0x10, 0x05, 0x12, 0x0a, 0x0a, 0x06, 0x48, 0x69, 0x64, 0x64, 0x65, 0x6e,
	0x10, 0x64, 0x22, 0x04, 0x08, 0x06, 0x10, 0x63, 0x3a, 0x5b, 0x0a, 0x0f, 0x45, 0x6e, 0x75, 0x6d,
	0x4c, 0x6f, 0x67, 0x67, 0x65, 0x72, 0x4c, 0x65, 0x76, 0x65, 0x6c, 0x12, 0x1c, 0x2e, 0x67, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x45, 0x6e,
	0x75, 0x6d, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0xc1, 0xbf, 0xed, 0x21, 0x20, 0x03,
	0x28, 0x0e, 0x32, 0x10, 0x2e, 0x6d, 0x76, 0x70, 0x2e, 0x4c, 0x6f, 0x67, 0x67, 0x65, 0x72, 0x4c,
	0x65, 0x76, 0x65, 0x6c, 0x52, 0x0f, 0x45, 0x6e, 0x75, 0x6d, 0x4c, 0x6f, 0x67, 0x67, 0x65, 0x72,
	0x4c, 0x65, 0x76, 0x65, 0x6c, 0x3a, 0x64, 0x0a, 0x12, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65,
	0x4c, 0x6f, 0x67, 0x67, 0x65, 0x72, 0x4c, 0x65, 0x76, 0x65, 0x6c, 0x12, 0x1f, 0x2e, 0x67, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x4d, 0x65,
	0x73, 0x73, 0x61, 0x67, 0x65, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0xc2, 0xbf, 0xed,
	0x21, 0x20, 0x03, 0x28, 0x0e, 0x32, 0x10, 0x2e, 0x6d, 0x76, 0x70, 0x2e, 0x4c, 0x6f, 0x67, 0x67,
	0x65, 0x72, 0x4c, 0x65, 0x76, 0x65, 0x6c, 0x52, 0x12, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65,
	0x4c, 0x6f, 0x67, 0x67, 0x65, 0x72, 0x4c, 0x65, 0x76, 0x65, 0x6c, 0x3a, 0x5e, 0x0a, 0x10, 0x46,
	0x69, 0x65, 0x6c, 0x64, 0x4c, 0x6f, 0x67, 0x67, 0x65, 0x72, 0x4c, 0x65, 0x76, 0x65, 0x6c, 0x12,
	0x1d, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75,
	0x66, 0x2e, 0x46, 0x69, 0x65, 0x6c, 0x64, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0xc3,
	0xbf, 0xed, 0x21, 0x20, 0x03, 0x28, 0x0e, 0x32, 0x10, 0x2e, 0x6d, 0x76, 0x70, 0x2e, 0x4c, 0x6f,
	0x67, 0x67, 0x65, 0x72, 0x4c, 0x65, 0x76, 0x65, 0x6c, 0x52, 0x10, 0x46, 0x69, 0x65, 0x6c, 0x64,
	0x4c, 0x6f, 0x67, 0x67, 0x65, 0x72, 0x4c, 0x65, 0x76, 0x65, 0x6c, 0x3a, 0x64, 0x0a, 0x12, 0x53,
	0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x4c, 0x6f, 0x67, 0x67, 0x65, 0x72, 0x4c, 0x65, 0x76, 0x65,
	0x6c, 0x12, 0x1f, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x62, 0x75, 0x66, 0x2e, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x4f, 0x70, 0x74, 0x69, 0x6f,
	0x6e, 0x73, 0x18, 0xc4, 0xbf, 0xed, 0x21, 0x20, 0x03, 0x28, 0x0e, 0x32, 0x10, 0x2e, 0x6d, 0x76,
	0x70, 0x2e, 0x4c, 0x6f, 0x67, 0x67, 0x65, 0x72, 0x4c, 0x65, 0x76, 0x65, 0x6c, 0x52, 0x12, 0x53,
	0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x4c, 0x6f, 0x67, 0x67, 0x65, 0x72, 0x4c, 0x65, 0x76, 0x65,
	0x6c, 0x3a, 0x61, 0x0a, 0x11, 0x4d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x4c, 0x6f, 0x67, 0x67, 0x65,
	0x72, 0x4c, 0x65, 0x76, 0x65, 0x6c, 0x12, 0x1e, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x4d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x4f,
	0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0xc5, 0xbf, 0xed, 0x21, 0x20, 0x03, 0x28, 0x0e, 0x32,
	0x10, 0x2e, 0x6d, 0x76, 0x70, 0x2e, 0x4c, 0x6f, 0x67, 0x67, 0x65, 0x72, 0x4c, 0x65, 0x76, 0x65,
	0x6c, 0x52, 0x11, 0x4d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x4c, 0x6f, 0x67, 0x67, 0x65, 0x72, 0x4c,
	0x65, 0x76, 0x65, 0x6c, 0x42, 0x1c, 0x5a, 0x1a, 0x67, 0x69, 0x74, 0x2e, 0x6c, 0x6f, 0x63, 0x61,
	0x6c, 0x2f, 0x73, 0x65, 0x6e, 0x73, 0x69, 0x74, 0x69, 0x76, 0x65, 0x2f, 0x6d, 0x76, 0x70, 0x2f,
	0x70, 0x62, 0x62, 0x08, 0x65, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x70, 0xe8, 0x07,
})

var (
	file_proto_logger_proto_rawDescOnce sync.Once
	file_proto_logger_proto_rawDescData []byte
)

func file_proto_logger_proto_rawDescGZIP() []byte {
	file_proto_logger_proto_rawDescOnce.Do(func() {
		file_proto_logger_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_proto_logger_proto_rawDesc), len(file_proto_logger_proto_rawDesc)))
	})
	return file_proto_logger_proto_rawDescData
}

var file_proto_logger_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_proto_logger_proto_goTypes = []any{
	(LoggerLevel)(0),                    // 0: mvp.LoggerLevel
	(*descriptorpb.EnumOptions)(nil),    // 1: google.protobuf.EnumOptions
	(*descriptorpb.MessageOptions)(nil), // 2: google.protobuf.MessageOptions
	(*descriptorpb.FieldOptions)(nil),   // 3: google.protobuf.FieldOptions
	(*descriptorpb.ServiceOptions)(nil), // 4: google.protobuf.ServiceOptions
	(*descriptorpb.MethodOptions)(nil),  // 5: google.protobuf.MethodOptions
}
var file_proto_logger_proto_depIdxs = []int32{
	1,  // 0: mvp.EnumLoggerLevel:extendee -> google.protobuf.EnumOptions
	2,  // 1: mvp.MessageLoggerLevel:extendee -> google.protobuf.MessageOptions
	3,  // 2: mvp.FieldLoggerLevel:extendee -> google.protobuf.FieldOptions
	4,  // 3: mvp.ServiceLoggerLevel:extendee -> google.protobuf.ServiceOptions
	5,  // 4: mvp.MethodLoggerLevel:extendee -> google.protobuf.MethodOptions
	0,  // 5: mvp.EnumLoggerLevel:type_name -> mvp.LoggerLevel
	0,  // 6: mvp.MessageLoggerLevel:type_name -> mvp.LoggerLevel
	0,  // 7: mvp.FieldLoggerLevel:type_name -> mvp.LoggerLevel
	0,  // 8: mvp.ServiceLoggerLevel:type_name -> mvp.LoggerLevel
	0,  // 9: mvp.MethodLoggerLevel:type_name -> mvp.LoggerLevel
	10, // [10:10] is the sub-list for method output_type
	10, // [10:10] is the sub-list for method input_type
	5,  // [5:10] is the sub-list for extension type_name
	0,  // [0:5] is the sub-list for extension extendee
	0,  // [0:0] is the sub-list for field type_name
}

func init() { file_proto_logger_proto_init() }
func file_proto_logger_proto_init() {
	if File_proto_logger_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_proto_logger_proto_rawDesc), len(file_proto_logger_proto_rawDesc)),
			NumEnums:      1,
			NumMessages:   0,
			NumExtensions: 5,
			NumServices:   0,
		},
		GoTypes:           file_proto_logger_proto_goTypes,
		DependencyIndexes: file_proto_logger_proto_depIdxs,
		EnumInfos:         file_proto_logger_proto_enumTypes,
		ExtensionInfos:    file_proto_logger_proto_extTypes,
	}.Build()
	File_proto_logger_proto = out.File
	file_proto_logger_proto_goTypes = nil
	file_proto_logger_proto_depIdxs = nil
}
