package service

import (
	"context"
	"time"

	gorpc "git.local/sensitive/innerpb/processing/grpc"
	"git.local/sensitive/processing/account/model"
	"git.local/sensitive/processing/account/repository"
	"git.local/sensitive/processing/account/schema"
	"git.local/sensitive/sdk/dog"
)

const (
	debitOperation  = "debit"
	creditOperation = "credit"
	bankKbe         = "14"
)

type AccountStatementService struct {
	accountScheduleRepo       repository.AccountScheduler
	accountInfoRepo           repository.AccountInformer
	accountBalanceHistoryRepo repository.AccountBalanceHistorier
	accountStatementRepo      repository.AccountStatementer
	multiaccountingClient     gorpc.MultiaccountingClient
	getCurrentTime            func() time.Time
}

func NewAccountStatementService(
	accountScheduleRepo repository.AccountScheduler,
	accountInfoRepo repository.AccountInformer,
	accountBalanceHistoryRepo repository.AccountBalanceHistorier,
	accountStatementRepo repository.AccountStatementer,
	multiaccountingClient gorpc.MultiaccountingClient,
) AccountStatementer {
	return &AccountStatementService{
		accountScheduleRepo:       accountScheduleRepo,
		accountInfoRepo:           accountInfoRepo,
		accountBalanceHistoryRepo: accountBalanceHistoryRepo,
		accountStatementRepo:      accountStatementRepo,
		multiaccountingClient:     multiaccountingClient,
		getCurrentTime:            time.Now,
	}
}

func (s *AccountStatementService) ProcessAccountStatement(ctx context.Context) (err error) {
	ctx, span := dog.CreateSpan(ctx, "AccountStatementService_ProcessAccountStatement")
	defer span.End()

	schedules, err := s.accountScheduleRepo.GetAll(ctx)
	if err != nil {
		return err
	}

	for _, schedule := range schedules {
		hours, _, _ := s.getCurrentTime().Add(time.Duration(schedule.UTC) * time.Hour).Clock()
		currentHour := uint8(hours)

		if schedule.StartHour == currentHour {
			accounts, accountErr := s.accountInfoRepo.GetAll(ctx)
			if accountErr != nil {
				return accountErr
			}

			for _, account := range accounts {
				if accountErr = s.processAccount(ctx, account); accountErr != nil {
					return accountErr
				}
			}
		}
	}

	return nil
}

func (s *AccountStatementService) processAccount(ctx context.Context, acc model.Account) error {
	account, err := s.accountInfoRepo.GetByNumber(ctx, acc.Number)
	if err != nil {
		return err
	}

	convertedAccountInfo, err := schema.AccountInfoConverter(account)
	if err != nil {
		return err
	}

	yesterday := s.getCurrentTime().AddDate(0, 0, -1).Truncate(time.Hour)
	request := schema.NewRawGetAccountStatementRequest(convertedAccountInfo, schema.TimeToDateTime(yesterday))

	rawResponse, err := s.multiaccountingClient.GetAccountStatement(ctx, request)
	if err != nil {
		return err
	}

	return s.saveAccountStatements(ctx, rawResponse, yesterday, account)
}

func (s *AccountStatementService) saveAccountStatements(
	ctx context.Context,
	rawResponse *gorpc.GetAccountStatementResponse,
	date time.Time,
	account *model.Account,
) error {
	if err := s.accountBalanceHistoryRepo.Create(ctx, &model.AccountBalanceHistory{
		InBalance:  rawResponse.GetInBalance(),
		OutBalance: rawResponse.GetOutBalance(),
		FinishedAt: date,
		AccountID:  account.ID,
	}); err != nil {
		return err
	}

	for _, operation := range rawResponse.GetOperations() {
		var debitAmount, creditAmount float64
		if operation.GetOperationType() == debitOperation {
			debitAmount += operation.GetAmount()
		} else if operation.GetOperationType() == creditOperation {
			creditAmount += operation.GetAmount()
		}

		createdAt, convertErr := time.Parse("2006-01-02 15:04:05", operation.GetDate())
		if convertErr != nil {
			return convertErr
		}

		if err := s.accountStatementRepo.Create(ctx, &model.AccountStatement{
			ExternalReferenceID: operation.GetExternalReferenceId(),
			MerchantAccount:     operation.GetMerchantAccount(),
			MerchantBankIDCode:  operation.GetMerchantBank(),
			MerchantBIN:         operation.GetMerchantBin(),
			MerchantName:        operation.GetMerchantName(),
			Currency:            operation.GetCurrency(),
			PaymentPurposeCode:  operation.GetPaymentPurposeCode(),
			Description:         operation.GetDescription(),
			FinishedAt:          createdAt,
			CreditTurnover:      creditAmount,
			DebitTurnover:       debitAmount,
			BeneficiaryCode:     bankKbe,
			AccountID:           account.ID,
		}); err != nil {
			return err
		}
	}

	return nil
}
