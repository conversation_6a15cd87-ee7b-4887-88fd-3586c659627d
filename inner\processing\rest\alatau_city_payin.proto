edition = "2023";

package alatau_city_bank;

option go_package = "git.local/sensitive/innerpb/processing/rest";

import "mvp/proto/rest.proto";
import "mvp/proto/logger.proto";

service AlatauCityBankPayIn {
  option(mvp.rest_service_options) = {
    hosts :[
      {
        stand: "local",
        base_uri: "https://jpay-test.alataucitybank.kz"
      },
      {
        stand: "dev",
        base_uri: "https://jpay-test.alataucitybank.kz"
      },
      {
        stand: "test",
        base_uri: "https://jpay.alataucitybank.kz"
      },
      {
        stand: "stage",
        base_uri: "https://jpay.alataucitybank.kz"
      },
      {
        stand: "prod",
        base_uri: "https://jpay-test.alataucitybank.kz"
      },
      {
        stand: "sandbox",
        base_uri: "https://jpay-test.alataucitybank.kz"
      }
    ]
  };

  rpc ProcessPayIn(AlatauCityProcessPayInRequest) returns (AlatauCityProcessPayInResponse) {
    option(mvp.rest_method_options) = {
      method: Post,
      authorization: None,
      request_marshal: XWwwFormUrlencoded,
      max_request_timeout: 60,
      path: "/ecom/api",
    };
  }

  rpc ProcessNoAcceptPay(AlatauCityProcessNoAcceptPayRequest) returns (AlatauCityProcessNoAcceptPayResponse) {
    option(mvp.rest_method_options) = {
      method: Post,
      authorization: None,
      request_marshal: XWwwFormUrlencoded,
      max_request_timeout: 60,
      path: "/ecom/api",
    };
  }

  rpc GetStatus(AlatauCityGetStatusPayInRequest) returns (AlatauCityGetStatusPayInResponse) {
    option(mvp.rest_method_options) = {
      method: Post,
      authorization: None,
      request_marshal: XWwwFormUrlencoded,
      response_unmarshal: XML,
      max_request_timeout: 60,
      path: "/ecom/api"
    };
  }

  rpc ConfirmThreeDS(AlatauCityConfirmThreeDSRequest) returns (AlatauCityConfirmThreeDSResponse) {
    option(mvp.rest_method_options) = {
      method: Post,
      authorization: None,
      request_marshal: XWwwFormUrlencoded,
      response_unmarshal: Native,
      max_request_timeout: 60,
      path: "" //We are using mutatePath hook because we can't pass port
    };
  }

  rpc ResumeThreeDS(AlatauCityResumeThreeDSRequest) returns (AlatauCityResumeThreeDSResponse) {
    option(mvp.rest_method_options) = {
      method: Post,
      authorization: None,
      request_marshal: XWwwFormUrlencoded,
      response_unmarshal: Native,
      max_request_timeout: 60,
      path: "" //We are using mutatePath hook because we can't pass port
    };
  }

  rpc SendForm(AlatauCitySendFormRequest) returns (AlatauCitySendFormResponse) {
    option(mvp.rest_method_options) = {
      method: Post,
      authorization: None,
      request_marshal: XWwwFormUrlencoded,
      response_unmarshal: Native,
      max_request_timeout: 60,
      path: "" //We are using mutatePath hook because we can't pass port
    };
  }

  rpc CancelPayment(AlatauCityCancelPaymentRequest) returns (AlatauCityCancelPaymentResponse) {
    option(mvp.rest_method_options) = {
      method: Post,
      authorization: None,
      request_marshal: XWwwFormUrlencoded,
      response_unmarshal: XML,
      max_request_timeout: 60,
      path: "/ecom/api"
    };
  }

  rpc RegisterToken(AlatauCityRegisterTokenRequest) returns (AlatauCityRegisterTokenResponse) {
    option(mvp.rest_method_options) = {
      method: Post,
      authorization: None,
      request_marshal: XWwwFormUrlencoded,
      response_unmarshal: XML,
      max_request_timeout: 60,
      path: "/ecom/api"
    };
  }

  rpc Charge(AlatauCityChargeRequest) returns (AlatauCityChargeResponse) {
    option(mvp.rest_method_options) = {
      method: Post,
      authorization: None,
      request_marshal: XWwwFormUrlencoded,
      response_unmarshal: XML,
      max_request_timeout: 60,
      path: "/ecom/api"
    };
  }
}

message AlatauCityProcessPayInRequest {
  AlatauCityPayInRequest body = 1;
}

message AlatauCityPayInRequest {
  string AMOUNT = 1;
  string BACKREF = 2;
  string CLIENT_ID = 3;
  string CURRENCY = 4;
  string DESC = 5;
  string DESC_ORDER = 6;
  string EMAIL = 7;
  string EXT_MPI_ECI = 8;
  string INT_REF = 9;
  string LANGUAGE = 10;
  string MERCHANT = 11;
  string MERCH_PAYTO_TOKEN_ID = 12;
  string MERCH_RN_ID = 13;
  string MERCH_TOKEN_ID = 14;
  string MERCH_TRAN_STATE = 15;
  string MK_TOKEN = 16;
  string M_INFO = 17;
  string NAME = 18;
  string NONCE = 19;
  string ORDER = 20;
  string PAYMENT_TO = 21;
  string P_SIGN = 22;
  string RECUR_EXP = 23;
  string RECUR_FREQ = 24;
  string RECUR_REF = 25;
  string TAVV = 26;
  string TERMINAL = 27;
  string Ucaf_Authentication_Data = 28;
  string Ucaf_Flag = 29;
  string WTYPE = 30;
  string crd_cvc = 31 [(mvp.FieldLoggerLevel) = Hidden];
  string crd_exp = 32 [(mvp.FieldLoggerLevel) = Hidden];
  string crd_pan = 33 [(mvp.FieldLoggerLevel) = Hidden];
  string REF = 34;
  string MERCH_3D_TERM_URL = 35;
  string MERCH_SCA = 36;
}

message AlatauCityProcessPayInResponse {
  string response = 1;
}

message AlatauCityGetStatusPayInRequest {
  GetStatusRequest body = 1;

  message GetStatusRequest {
    string ORDER = 1;
    string MERCHANT = 2;
    string GETSTATUS = 3;
    string P_SIGN = 4;
  }
}

message AlatauCityGetStatusPayInResponse {
  StatusResponse status_response = 1;
  message StatusResponse {
    Result result = 1;
  }

  message Result {
    string code = 1;
    string description = 2;
    Operation operation = 3;
  }

  message Operation {
    string status = 1;
    string status_desc = 2;
    string amount = 3;
    string currency = 4;
    string description = 5;
    string desc_order = 6;
    string email = 7;
    string lang = 8;
    string mpi_order = 9;
    string terminal = 10;
    string phone = 11;
    string card_masked = 12 [(mvp.FieldLoggerLevel) = Hidden];
    string card_name = 13;
    string card_expdt = 14;
    string card_token = 15;
    string create_date = 16;
    string result = 17;
    string result_desc = 18;
    string rc = 19;
    string rrn = 20;
    string auth_code = 21;
    string inv_id = 22;
    string int_exp_date = 23;
    string rev_max_amount = 24;
    string recur_freq = 25;
    string recur_ref = 26;
    string recur_int_ref = 27;
    string client_id = 28;
    string card_to_masked = 29;
    string card_to_token = 30;
    Refunds refunds = 31;

    message Refunds {
      repeated Rec rec = 1;
    }

    message Rec {
      string status = 1;
      string status_desc = 2;
      string rev_rc = 3;
      string rev_amount = 4;
      string rev_description = 5;
      string rev_error = 6;
      string rev_date = 7;
    }
  }

}

message AlatauCityProcessNoAcceptPayRequest {
  AlatauCityPayInRequest body = 1;
}

message AlatauCityProcessNoAcceptPayResponse {
  string response = 1;
}

message AlatauCityConfirmThreeDSRequest {
  ConfirmThreeDSRequest body = 1;

  message ConfirmThreeDSRequest {
    string cres = 1;
    string threeDSSessionData = 2;
  }
}

message AlatauCityConfirmThreeDSResponse {
  string response = 1;
}

message AlatauCityResumeThreeDSRequest {
  ResumeThreeDSRequest body = 1;

  message ResumeThreeDSRequest {
    string threeDSMethodData = 1;
    string threeDSMethodState = 2;
  }
}

message AlatauCityResumeThreeDSResponse {
  string response = 1;
}

message AlatauCitySendFormRequest {
  SendFormRequest body = 1;

  message SendFormRequest {
    string threeDSMethodData = 1;
  }
}

message AlatauCitySendFormResponse {
  string response = 1;
}

message AlatauCityCancelPaymentRequest {
  CancelPaymentRequest body = 1;

  message CancelPaymentRequest {
    string ORDER = 1;
    string MERCHANT = 2;
    string P_SIGN = 3;
    string REV_AMOUNT = 4;
    string REV_DESC = 5;
  }
}

message AlatauCityCancelPaymentResponse {
  CancelPaymentResponse cancel_response = 1;

  message Result {
    string code = 1;
    string description = 2;
    Operation operation = 3;
  }

  message CancelPaymentResponse {
    Result result = 1;
  }

  message Operation {
    string status = 1;
    string result_desc = 2;
    string result = 3;
    string rc = 4;
    string ecode = 5;
    string edesc = 6;
    string amount = 7;
    string rrn = 8;
    string rev_desc = 9;
    string rev_date = 10;
  }
}


message AlatauCityRegisterTokenRequest {
  message RegisterTokenRequest {
    string ORDER = 1;
    string MERCHANT = 2;
    string TERMINAL = 3;
    string CLIENT_ID = 4;
    string crd_pan = 5 [(mvp.FieldLoggerLevel) = Hidden];
    string crd_exp = 6 [(mvp.FieldLoggerLevel) = Hidden];
    string crd_cvc = 7 [(mvp.FieldLoggerLevel) = Hidden];
    string NAME = 8 ;
    string P_SIGN = 9 ;
    string TOKEN_CMD = 10 ;
  }

  RegisterTokenRequest body = 1;
}

message AlatauCityRegisterTokenResponse {
  message RegisterTokenResponse {
    Result result = 1;
  }
  message Result {
    string code = 1;
    string description = 2;
    string token = 3;
  }

  RegisterTokenResponse token_response = 1;
}

message AlatauCityChargeRequest {
  message ChargeRequest {
    string APPROVE = 1;
    string ORDER = 2;
    string AMOUNT = 3;
    string MERCHANT = 4;
    string P_SIGN = 5;
  }

  ChargeRequest body = 1;
}

message AlatauCityChargeResponse {
  message ChargeResponse {
    Result result = 1;
  }

  message Result {
    string code = 1;
    string description = 2;
  }

  ChargeResponse charge_response = 1;
}