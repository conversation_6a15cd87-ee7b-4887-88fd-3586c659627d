package database

import (
	"context"

	"gorm.io/gorm"

	"git.local/sensitive/innerpb/processing/goerr"
	"git.local/sensitive/pkg/gtransaction"
	"git.local/sensitive/processing/acquirer/model"
	"git.local/sensitive/sdk/dog"
)

type RulePercentageDB struct {
	db *gorm.DB
}

func NewRulePercentageDB(db *gorm.DB) RulePercentager {
	return &RulePercentageDB{
		db: db,
	}
}

func (r *RulePercentageDB) Create(ctx context.Context, rulePercentages []*model.RulePercentage) (err error) {
	ctx, span := dog.CreateSpan(ctx, "RulePercentageDB_Create")
	defer span.End()

	if err := gtransaction.FromContextOrFallback(ctx, r.db).WithContext(ctx).
		Create(rulePercentages).Error; err != nil {
		return goerr.ErrDbUnexpected.WithErr(err).WithCtx(ctx)
	}

	return nil
}

func (r *RulePercentageDB) Delete(ctx context.Context, ruleID uint64) (err error) {
	ctx, span := dog.CreateSpan(ctx, "RulePercentageDB_Delete")
	defer span.End()

	res := gtransaction.FromContextOrFallback(ctx, r.db).WithContext(ctx).
		Where("rule_id = ?", ruleID).
		Delete(&model.RulePercentage{})

	if res.RowsAffected == 0 {
		return goerr.ErrBalancerNotFound
	}

	err = res.Error
	if err != nil {
		return goerr.ErrDbUnexpected.WithErr(err).WithCtx(ctx)
	}

	return nil
}

func (r *RulePercentageDB) GetAllByRuleID(
	ctx context.Context,
	ruleID uint64,
) (_ model.RulePercentages, err error) {
	ctx, span := dog.CreateSpan(ctx, "RulePercentageDB_GetAllByRuleID")
	defer span.End()

	var result model.RulePercentages

	if err := r.db.WithContext(ctx).
		Where(`rule_id = ?`, ruleID).
		Preload("Acquirer").
		Find(&result).
		Error; err != nil {
		return nil, goerr.ErrDbUnexpected.WithErr(err).WithCtx(ctx)
	}

	return result, nil
}
