// Code generated by MockGen. DO NOT EDIT.
// Source: kalkan.gin.pb.go

// Package grpcmock is a generated GoMock package.
package grpcmock

import (
	reflect "reflect"

	gin "github.com/gin-gonic/gin"
	gomock "github.com/golang/mock/gomock"
)

// MockGinKalkanServer is a mock of GinKalkanServer interface.
type MockGinKalkanServer struct {
	ctrl     *gomock.Controller
	recorder *MockGinKalkanServerMockRecorder
}

// MockGinKalkanServerMockRecorder is the mock recorder for MockGinKalkanServer.
type MockGinKalkanServerMockRecorder struct {
	mock *MockGinKalkanServer
}

// NewMockGinKalkanServer creates a new mock instance.
func NewMockGinKalkanServer(ctrl *gomock.Controller) *MockGinKalkanServer {
	mock := &MockGinKalkanServer{ctrl: ctrl}
	mock.recorder = &MockGinKalkanServerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockGinKalkanServer) EXPECT() *MockGinKalkanServerMockRecorder {
	return m.recorder
}

// MakeSignatureV1 mocks base method.
func (m *MockGinKalkanServer) MakeSignatureV1(c *gin.Context) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "MakeSignatureV1", c)
	ret0, _ := ret[0].(error)
	return ret0
}

// MakeSignatureV1 indicates an expected call of MakeSignatureV1.
func (mr *MockGinKalkanServerMockRecorder) MakeSignatureV1(c interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "MakeSignatureV1", reflect.TypeOf((*MockGinKalkanServer)(nil).MakeSignatureV1), c)
}
