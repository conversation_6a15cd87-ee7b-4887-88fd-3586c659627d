package schema

import (
	"git.local/sensitive/processing/acquirer/model"
	"github.com/go-playground/validator/v10"
)

const AcquirerCodeJusan = "jusan"

type CreateTerminalRequest struct {
	AcquirerID           uint64                 `json:"acquirer_id" validate:"required"`
	Config               map[string]interface{} `json:"config" validate:"required"`
	TwoStageTimeout      uint32                 `json:"two_stage_timeout" validate:"omitempty"`
	AccountNumber        string                 `json:"account_number" validate:"required_if=IsTransit true"`
	IsTransit            bool                   `json:"is_transit" validate:"omitempty"`
	TransitBankID        uint64                 `json:"transit_bank_id" validate:"required_if=IsTransit true"`
	AcquirerTerminalName string                 `json:"acquirer_terminal_name" validate:"omitempty"`
	Description          string                 `json:"description" validate:"omitempty"`
}

type TerminalResponse struct {
	ID                uint64               `json:"id"`
	AcquirerID        uint64               `json:"acquirer_id"`
	Acquirer          model.Acquirer       `json:"acquirer,omitempty"`
	ProjectID         uint64               `json:"project_id"`
	TransactionTypeID uint64               `json:"transaction_type_id"`
	Status            model.TerminalStatus `json:"status"`
	Config            string               `json:"config,omitempty"`
}

type DecryptedConfig map[string]any

type GetBalanceResponse struct {
	TerminalID   uint64  `json:"terminal_id"`
	AcquirerName string  `json:"acquirer_name"`
	Amount       float64 `json:"amount"`
	Message      string  `json:"message"`
}

func (t CreateTerminalRequest) Validate() error {
	validation := validator.New()

	return validation.Struct(t)
}

type FiltersTerminalRequest struct {
	ProjectID *uint64 `form:"project_id"`
}

func NewTerminalResponse(t *model.Terminal) *TerminalResponse {
	return &TerminalResponse{
		ID:         t.ID,
		AcquirerID: t.AcquirerID,
		Status:     t.Status,
		Config:     t.EncryptedConfig,
	}
}

func NewTerminalsResponses(terminals []model.Terminal) []*TerminalResponse {
	var terminalResponse = make([]*TerminalResponse, len(terminals))

	for i := range terminals {
		terminalResponse[i] = &TerminalResponse{
			ID:         terminals[i].ID,
			AcquirerID: terminals[i].AcquirerID,
			Status:     terminals[i].Status,
			Config:     terminals[i].EncryptedConfig,
		}
	}

	return terminalResponse
}

type UpdateTerminalsResponse struct {
	RowsAffected int64 `json:"rows_affected"`
	Total        int   `json:"total"`
}

type TerminalTwoStage struct {
	TwoStageTimeout uint32 `json:"two_stage_timeout" validate:"required,min=3,max=13"`
}

func (t TerminalTwoStage) Validate() error {
	return validator.New().Struct(t)
}

type ActiveTerminalsByIDsReq struct {
	TerminalIDs       []uint64
	ProjectID         uint64
	TransactionTypeID uint64
}

type ActiveTerminal struct {
	TerminalID  uint64
	AcquirerID  uint64
	AcquireCode string
}

type ActiveTerminals []*ActiveTerminal

func (ts ActiveTerminals) UniqueAcquirerIDs() (uniqueAcquirerIDs []uint64) {
	seen := make(map[uint64]bool)
	for _, terminal := range ts {
		if !seen[terminal.AcquirerID] {
			uniqueAcquirerIDs = append(uniqueAcquirerIDs, terminal.AcquirerID)
			seen[terminal.AcquirerID] = true
		}
	}

	return uniqueAcquirerIDs
}

func ToActiveTerminals(terminals model.Terminals) ActiveTerminals {
	activeTerminals := make([]*ActiveTerminal, len(terminals))

	for i := range terminals {
		activeTerminals[i] = &ActiveTerminal{
			TerminalID:  terminals[i].ID,
			AcquirerID:  terminals[i].Acquirer.ID,
			AcquireCode: terminals[i].Acquirer.Code,
		}
	}

	return activeTerminals
}
