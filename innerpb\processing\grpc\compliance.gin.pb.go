// Code generated by protoc-gen-go-mvp. DO NOT EDIT.
// versions:
// - protoc-gen-go-mvp v2.0.1
// created by mvp authors

package grpc

import (
	dog "git.local/sensitive/sdk/dog"
	gin "github.com/gin-gonic/gin"
	metadata "google.golang.org/grpc/metadata"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
	http "net/http"
)

func RegisterGinComplianceRouterGroup(
	in *gin.RouterGroup,
	handler func(func(c *gin.Context) error) gin.HandlerFunc,
) error {
	service, err := NewGinComplianceService()
	if err != nil {
		return err
	}

	routerGroup := in.Group("processing.compliance.compliance.Compliance")
	routerGroup.PUT("/UpdateSanctionFinanciersList", handler(service.UpdateSanctionFinanciersList))
	routerGroup.PUT("/UpdateSanctionInvolvedList", handler(service.UpdateSanctionInvolvedList))
	routerGroup.PUT("/UpdateSanctionUNSCList", handler(service.UpdateSanctionUNSCList))
	routerGroup.PUT("/UpdateSanctionWMDList", handler(service.UpdateSanctionWMDList))
	return nil
}

func NewGinComplianceService() (GinComplianceServer, error) {
	client, err := NewPreparedComplianceClient()
	if err != nil {
		return nil, err
	}

	return &ginComplianceServer{
		client: NewLoggedComplianceClient(
			NewIamComplianceClient(client),
		),
	}, nil
}

//go:generate mockgen -destination=./grpcmock/compliance.gin.pb.go -package=grpcmock -source=compliance.gin.pb.go GinComplianceServer
type GinComplianceServer interface {
	UpdateSanctionFinanciersList(c *gin.Context) error
	UpdateSanctionInvolvedList(c *gin.Context) error
	UpdateSanctionUNSCList(c *gin.Context) error
	UpdateSanctionWMDList(c *gin.Context) error
}

var _ GinComplianceServer = (*ginComplianceServer)(nil)

type ginComplianceServer struct {
	client ComplianceClient
}

type Compliance_UpdateSanctionFinanciersList_Success struct {
	// Status is the status of the response. Set true
	Status bool `json:"status"`
	// Message is a human-readable explanation of the status
	Message string `json:"message"`
	// StatusCode is the GoErr status. Set 0
	StatusCode int `json:"status_code"`
	// Result is the result of the request
	Result *emptypb.Empty `json:"result"`
}

type Compliance_UpdateSanctionFinanciersList_Failure struct {
	// Status is the status of the response. Set false
	Status bool `json:"status"`
	// Message is a human-readable explanation of the status
	Message string `json:"message"`
	// StatusCode is the GoErr status
	StatusCode int `json:"status_code"`
}

// UpdateSanctionFinanciersList
// @Summary jobs
// @Security bearerAuth
// @ID Compliance_UpdateSanctionFinanciersList
// @Accept json
// @Param request body emptypb.Empty true "Empty"
// @Success 200 {object} Compliance_UpdateSanctionFinanciersList_Success
// @Failure 401 {object} Compliance_UpdateSanctionFinanciersList_Failure "goerrpkg.ErrorTypeAuthorization"
// @Failure 404 {object} Compliance_UpdateSanctionFinanciersList_Failure "goerrpkg.ErrorTypeNotFound"
// @Failure 400 {object} Compliance_UpdateSanctionFinanciersList_Failure "goerrpkg.ErrorTypeBadRequest"
// @Failure 500 {object} Compliance_UpdateSanctionFinanciersList_Failure "goerrpkg.ErrorTypeUnexpectedError"
// @Failure 400 {object} Compliance_UpdateSanctionFinanciersList_Failure "gin.ErrorTypeBind"
// @Failure 500 {object} Compliance_UpdateSanctionFinanciersList_Failure "Undefined error"
// @Produce json
// @Router /processing.compliance.compliance.Compliance/UpdateSanctionFinanciersList [put]
// @tags Compliance
func (s *ginComplianceServer) UpdateSanctionFinanciersList(c *gin.Context) error {
	ctx, span := dog.CreateSpan(c.Request.Context(), "GinComplianceServer_UpdateSanctionFinanciersList")
	defer span.End()

	var request emptypb.Empty
	if err := c.ShouldBindJSON(&request); err != nil {
		return err
	}

	ctx = metadata.NewOutgoingContext(ctx, metadata.MD(c.Request.Header))

	response, err := s.client.UpdateSanctionFinanciersList(ctx, &request)
	if err != nil {
		return err
	}

	c.JSON(http.StatusOK, &Compliance_UpdateSanctionFinanciersList_Success{
		Status:     true,
		Message:    "Success",
		StatusCode: 0,
		Result:     response,
	})
	return nil
}

type Compliance_UpdateSanctionInvolvedList_Success struct {
	// Status is the status of the response. Set true
	Status bool `json:"status"`
	// Message is a human-readable explanation of the status
	Message string `json:"message"`
	// StatusCode is the GoErr status. Set 0
	StatusCode int `json:"status_code"`
	// Result is the result of the request
	Result *emptypb.Empty `json:"result"`
}

type Compliance_UpdateSanctionInvolvedList_Failure struct {
	// Status is the status of the response. Set false
	Status bool `json:"status"`
	// Message is a human-readable explanation of the status
	Message string `json:"message"`
	// StatusCode is the GoErr status
	StatusCode int `json:"status_code"`
}

// UpdateSanctionInvolvedList
// @Summary UpdateSanctionInvolvedList
// @Security bearerAuth
// @ID Compliance_UpdateSanctionInvolvedList
// @Accept json
// @Param request body emptypb.Empty true "Empty"
// @Success 200 {object} Compliance_UpdateSanctionInvolvedList_Success
// @Failure 401 {object} Compliance_UpdateSanctionInvolvedList_Failure "goerrpkg.ErrorTypeAuthorization"
// @Failure 404 {object} Compliance_UpdateSanctionInvolvedList_Failure "goerrpkg.ErrorTypeNotFound"
// @Failure 400 {object} Compliance_UpdateSanctionInvolvedList_Failure "goerrpkg.ErrorTypeBadRequest"
// @Failure 500 {object} Compliance_UpdateSanctionInvolvedList_Failure "goerrpkg.ErrorTypeUnexpectedError"
// @Failure 400 {object} Compliance_UpdateSanctionInvolvedList_Failure "gin.ErrorTypeBind"
// @Failure 500 {object} Compliance_UpdateSanctionInvolvedList_Failure "Undefined error"
// @Produce json
// @Router /processing.compliance.compliance.Compliance/UpdateSanctionInvolvedList [put]
// @tags Compliance
func (s *ginComplianceServer) UpdateSanctionInvolvedList(c *gin.Context) error {
	ctx, span := dog.CreateSpan(c.Request.Context(), "GinComplianceServer_UpdateSanctionInvolvedList")
	defer span.End()

	var request emptypb.Empty
	if err := c.ShouldBindJSON(&request); err != nil {
		return err
	}

	ctx = metadata.NewOutgoingContext(ctx, metadata.MD(c.Request.Header))

	response, err := s.client.UpdateSanctionInvolvedList(ctx, &request)
	if err != nil {
		return err
	}

	c.JSON(http.StatusOK, &Compliance_UpdateSanctionInvolvedList_Success{
		Status:     true,
		Message:    "Success",
		StatusCode: 0,
		Result:     response,
	})
	return nil
}

type Compliance_UpdateSanctionUNSCList_Success struct {
	// Status is the status of the response. Set true
	Status bool `json:"status"`
	// Message is a human-readable explanation of the status
	Message string `json:"message"`
	// StatusCode is the GoErr status. Set 0
	StatusCode int `json:"status_code"`
	// Result is the result of the request
	Result *emptypb.Empty `json:"result"`
}

type Compliance_UpdateSanctionUNSCList_Failure struct {
	// Status is the status of the response. Set false
	Status bool `json:"status"`
	// Message is a human-readable explanation of the status
	Message string `json:"message"`
	// StatusCode is the GoErr status
	StatusCode int `json:"status_code"`
}

// UpdateSanctionUNSCList
// @Summary UpdateSanctionUNSCList
// @Security bearerAuth
// @ID Compliance_UpdateSanctionUNSCList
// @Accept json
// @Param request body emptypb.Empty true "Empty"
// @Success 200 {object} Compliance_UpdateSanctionUNSCList_Success
// @Failure 401 {object} Compliance_UpdateSanctionUNSCList_Failure "goerrpkg.ErrorTypeAuthorization"
// @Failure 404 {object} Compliance_UpdateSanctionUNSCList_Failure "goerrpkg.ErrorTypeNotFound"
// @Failure 400 {object} Compliance_UpdateSanctionUNSCList_Failure "goerrpkg.ErrorTypeBadRequest"
// @Failure 500 {object} Compliance_UpdateSanctionUNSCList_Failure "goerrpkg.ErrorTypeUnexpectedError"
// @Failure 400 {object} Compliance_UpdateSanctionUNSCList_Failure "gin.ErrorTypeBind"
// @Failure 500 {object} Compliance_UpdateSanctionUNSCList_Failure "Undefined error"
// @Produce json
// @Router /processing.compliance.compliance.Compliance/UpdateSanctionUNSCList [put]
// @tags Compliance
func (s *ginComplianceServer) UpdateSanctionUNSCList(c *gin.Context) error {
	ctx, span := dog.CreateSpan(c.Request.Context(), "GinComplianceServer_UpdateSanctionUNSCList")
	defer span.End()

	var request emptypb.Empty
	if err := c.ShouldBindJSON(&request); err != nil {
		return err
	}

	ctx = metadata.NewOutgoingContext(ctx, metadata.MD(c.Request.Header))

	response, err := s.client.UpdateSanctionUNSCList(ctx, &request)
	if err != nil {
		return err
	}

	c.JSON(http.StatusOK, &Compliance_UpdateSanctionUNSCList_Success{
		Status:     true,
		Message:    "Success",
		StatusCode: 0,
		Result:     response,
	})
	return nil
}

type Compliance_UpdateSanctionWMDList_Success struct {
	// Status is the status of the response. Set true
	Status bool `json:"status"`
	// Message is a human-readable explanation of the status
	Message string `json:"message"`
	// StatusCode is the GoErr status. Set 0
	StatusCode int `json:"status_code"`
	// Result is the result of the request
	Result *emptypb.Empty `json:"result"`
}

type Compliance_UpdateSanctionWMDList_Failure struct {
	// Status is the status of the response. Set false
	Status bool `json:"status"`
	// Message is a human-readable explanation of the status
	Message string `json:"message"`
	// StatusCode is the GoErr status
	StatusCode int `json:"status_code"`
}

// UpdateSanctionWMDList
// @Summary UpdateSanctionWMDList
// @Security bearerAuth
// @ID Compliance_UpdateSanctionWMDList
// @Accept json
// @Param request body emptypb.Empty true "Empty"
// @Success 200 {object} Compliance_UpdateSanctionWMDList_Success
// @Failure 401 {object} Compliance_UpdateSanctionWMDList_Failure "goerrpkg.ErrorTypeAuthorization"
// @Failure 404 {object} Compliance_UpdateSanctionWMDList_Failure "goerrpkg.ErrorTypeNotFound"
// @Failure 400 {object} Compliance_UpdateSanctionWMDList_Failure "goerrpkg.ErrorTypeBadRequest"
// @Failure 500 {object} Compliance_UpdateSanctionWMDList_Failure "goerrpkg.ErrorTypeUnexpectedError"
// @Failure 400 {object} Compliance_UpdateSanctionWMDList_Failure "gin.ErrorTypeBind"
// @Failure 500 {object} Compliance_UpdateSanctionWMDList_Failure "Undefined error"
// @Produce json
// @Router /processing.compliance.compliance.Compliance/UpdateSanctionWMDList [put]
// @tags Compliance
func (s *ginComplianceServer) UpdateSanctionWMDList(c *gin.Context) error {
	ctx, span := dog.CreateSpan(c.Request.Context(), "GinComplianceServer_UpdateSanctionWMDList")
	defer span.End()

	var request emptypb.Empty
	if err := c.ShouldBindJSON(&request); err != nil {
		return err
	}

	ctx = metadata.NewOutgoingContext(ctx, metadata.MD(c.Request.Header))

	response, err := s.client.UpdateSanctionWMDList(ctx, &request)
	if err != nil {
		return err
	}

	c.JSON(http.StatusOK, &Compliance_UpdateSanctionWMDList_Success{
		Status:     true,
		Message:    "Success",
		StatusCode: 0,
		Result:     response,
	})
	return nil
}
