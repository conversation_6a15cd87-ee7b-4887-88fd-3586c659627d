// Code generated by protoc-gen-go-mvp. DO NOT EDIT.
// versions:
// - protoc-gen-go-mvp v2.0.1
// created by mvp authors

package grpc

import (
	context "context"
	cntx "git.local/sensitive/mvp/pkg/cntx"
	ctxzap "github.com/grpc-ecosystem/go-grpc-middleware/logging/zap/ctxzap"
	zap "go.uber.org/zap"
	grpc "google.golang.org/grpc"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
)

func file_inner_processing_grpc_payorder_proto_message_EmptyToZap(
	label string,
	in *emptypb.Empty,
) zap.Field {
	if in == nil {
		return zap.Reflect(label, in)
	}

	return zap.Dict(
		label,
	)
}

var _ PayorderServer = (*loggedPayorderServer)(nil)

func NewLoggedPayorderServer(srv PayorderServer) PayorderServer {
	return &loggedPayorderServer{srv: srv}
}

type loggedPayorderServer struct {
	UnimplementedPayorderServer

	srv PayorderServer
}

func (s *loggedPayorderServer) StartCheckInProcessOrdersWorker(
	ctx context.Context,
	request *emptypb.Empty,
) (
	response *emptypb.Empty,
	err error,
) {
	label := cntx.Begin(ctx, "PayorderServer_StartCheckInProcessOrdersWorker")
	defer cntx.End(ctx, label)

	defer func() {
		if err != nil {
			ctxzap.AddFields(ctx, zap.Any(label+"error", err))
			return
		}

		ctxzap.AddFields(
			ctx,
			file_inner_processing_grpc_payorder_proto_message_EmptyToZap(label+"response", response),
		)
	}()

	ctxzap.AddFields(
		ctx,
		file_inner_processing_grpc_payorder_proto_message_EmptyToZap(label+"request", request),
	)

	response, err = s.srv.StartCheckInProcessOrdersWorker(ctx, request)

	return
}

func (s *loggedPayorderServer) StartCreateNewPaymentOrdersWorker(
	ctx context.Context,
	request *emptypb.Empty,
) (
	response *emptypb.Empty,
	err error,
) {
	label := cntx.Begin(ctx, "PayorderServer_StartCreateNewPaymentOrdersWorker")
	defer cntx.End(ctx, label)

	defer func() {
		if err != nil {
			ctxzap.AddFields(ctx, zap.Any(label+"error", err))
			return
		}

		ctxzap.AddFields(
			ctx,
			file_inner_processing_grpc_payorder_proto_message_EmptyToZap(label+"response", response),
		)
	}()

	ctxzap.AddFields(
		ctx,
		file_inner_processing_grpc_payorder_proto_message_EmptyToZap(label+"request", request),
	)

	response, err = s.srv.StartCreateNewPaymentOrdersWorker(ctx, request)

	return
}

var _ PayorderClient = (*loggedPayorderClient)(nil)

func NewLoggedPayorderClient(client PayorderClient) PayorderClient {
	return &loggedPayorderClient{client: client}
}

type loggedPayorderClient struct {
	client PayorderClient
}

func (s *loggedPayorderClient) StartCheckInProcessOrdersWorker(
	ctx context.Context,
	request *emptypb.Empty,
	opts ...grpc.CallOption,
) (
	response *emptypb.Empty,
	err error,
) {
	label := cntx.Begin(ctx, "PayorderClient_StartCheckInProcessOrdersWorker")
	defer cntx.End(ctx, label)

	defer func() {
		if err != nil {
			ctxzap.AddFields(ctx, zap.Any(label+"error", err))
			return
		}

		ctxzap.AddFields(
			ctx,
			file_inner_processing_grpc_payorder_proto_message_EmptyToZap(label+"response", response),
		)
	}()

	ctxzap.AddFields(
		ctx,
		file_inner_processing_grpc_payorder_proto_message_EmptyToZap(label+"request", request),
	)

	response, err = s.client.StartCheckInProcessOrdersWorker(ctx, request, opts...)

	return
}

func (s *loggedPayorderClient) StartCreateNewPaymentOrdersWorker(
	ctx context.Context,
	request *emptypb.Empty,
	opts ...grpc.CallOption,
) (
	response *emptypb.Empty,
	err error,
) {
	label := cntx.Begin(ctx, "PayorderClient_StartCreateNewPaymentOrdersWorker")
	defer cntx.End(ctx, label)

	defer func() {
		if err != nil {
			ctxzap.AddFields(ctx, zap.Any(label+"error", err))
			return
		}

		ctxzap.AddFields(
			ctx,
			file_inner_processing_grpc_payorder_proto_message_EmptyToZap(label+"response", response),
		)
	}()

	ctxzap.AddFields(
		ctx,
		file_inner_processing_grpc_payorder_proto_message_EmptyToZap(label+"request", request),
	)

	response, err = s.client.StartCreateNewPaymentOrdersWorker(ctx, request, opts...)

	return
}
