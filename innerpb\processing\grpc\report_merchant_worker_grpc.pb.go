// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.5.1
// - protoc             v5.29.3
// source: inner/processing/grpc/report_merchant_worker.proto

package grpc

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.64.0 or later.
const _ = grpc.SupportPackageIsVersion9

const (
	ReportMerchantWorker_ProcessReportScheduleByPeriodType_FullMethodName = "/processing.report_merchant.report_merchant_worker.ReportMerchantWorker/ProcessReportScheduleByPeriodType"
)

// ReportMerchantWorkerClient is the client API for ReportMerchantWorker service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type ReportMerchantWorkerClient interface {
	ProcessReportScheduleByPeriodType(ctx context.Context, in *ProcessReportScheduleByPeriodTypeRequest, opts ...grpc.CallOption) (*emptypb.Empty, error)
}

type reportMerchantWorkerClient struct {
	cc grpc.ClientConnInterface
}

func NewReportMerchantWorkerClient(cc grpc.ClientConnInterface) ReportMerchantWorkerClient {
	return &reportMerchantWorkerClient{cc}
}

func (c *reportMerchantWorkerClient) ProcessReportScheduleByPeriodType(ctx context.Context, in *ProcessReportScheduleByPeriodTypeRequest, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, ReportMerchantWorker_ProcessReportScheduleByPeriodType_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// ReportMerchantWorkerServer is the server API for ReportMerchantWorker service.
// All implementations must embed UnimplementedReportMerchantWorkerServer
// for forward compatibility.
type ReportMerchantWorkerServer interface {
	ProcessReportScheduleByPeriodType(context.Context, *ProcessReportScheduleByPeriodTypeRequest) (*emptypb.Empty, error)
	mustEmbedUnimplementedReportMerchantWorkerServer()
}

// UnimplementedReportMerchantWorkerServer must be embedded to have
// forward compatible implementations.
//
// NOTE: this should be embedded by value instead of pointer to avoid a nil
// pointer dereference when methods are called.
type UnimplementedReportMerchantWorkerServer struct{}

func (UnimplementedReportMerchantWorkerServer) ProcessReportScheduleByPeriodType(context.Context, *ProcessReportScheduleByPeriodTypeRequest) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ProcessReportScheduleByPeriodType not implemented")
}
func (UnimplementedReportMerchantWorkerServer) mustEmbedUnimplementedReportMerchantWorkerServer() {}
func (UnimplementedReportMerchantWorkerServer) testEmbeddedByValue()                              {}

// UnsafeReportMerchantWorkerServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to ReportMerchantWorkerServer will
// result in compilation errors.
type UnsafeReportMerchantWorkerServer interface {
	mustEmbedUnimplementedReportMerchantWorkerServer()
}

func RegisterReportMerchantWorkerServer(s grpc.ServiceRegistrar, srv ReportMerchantWorkerServer) {
	// If the following call pancis, it indicates UnimplementedReportMerchantWorkerServer was
	// embedded by pointer and is nil.  This will cause panics if an
	// unimplemented method is ever invoked, so we test this at initialization
	// time to prevent it from happening at runtime later due to I/O.
	if t, ok := srv.(interface{ testEmbeddedByValue() }); ok {
		t.testEmbeddedByValue()
	}
	s.RegisterService(&ReportMerchantWorker_ServiceDesc, srv)
}

func _ReportMerchantWorker_ProcessReportScheduleByPeriodType_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ProcessReportScheduleByPeriodTypeRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ReportMerchantWorkerServer).ProcessReportScheduleByPeriodType(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ReportMerchantWorker_ProcessReportScheduleByPeriodType_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ReportMerchantWorkerServer).ProcessReportScheduleByPeriodType(ctx, req.(*ProcessReportScheduleByPeriodTypeRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// ReportMerchantWorker_ServiceDesc is the grpc.ServiceDesc for ReportMerchantWorker service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var ReportMerchantWorker_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "processing.report_merchant.report_merchant_worker.ReportMerchantWorker",
	HandlerType: (*ReportMerchantWorkerServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "ProcessReportScheduleByPeriodType",
			Handler:    _ReportMerchantWorker_ProcessReportScheduleByPeriodType_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "inner/processing/grpc/report_merchant_worker.proto",
}
