// Code generated by protoc-gen-go-mvp. DO NOT EDIT.
// versions:
// - protoc-gen-go-mvp v2.0.1
// created by mvp authors

package grpc

import (
	errors "git.local/sensitive/mvp/pkg/errors"
	grpc_middleware_sentry "github.com/johnbellone/grpc-middleware-sentry"
	otelgrpc "go.opentelemetry.io/contrib/instrumentation/google.golang.org/grpc/otelgrpc"
	grpc "google.golang.org/grpc"
	insecure "google.golang.org/grpc/credentials/insecure"
)

func NewPreparedTransactionClient(
	stands ...string,
) (TransactionClient, error) {
	stand := "dev"
	if len(stands) > 0 {
		stand = stands[0]
	}

	return NewPreparedTransactionClientWithStand(stand)
}

func NewPreparedTransactionClientWithStand(
	stand string,
) (TransactionClient, error) {
	var endpoint string
	switch stand {
	case "prod":
		endpoint = "processing-deployment-transaction:4040"
	default:
		endpoint = "processing-deployment-transaction:4040"
	}

	opts := []grpc.DialOption{
		grpc.WithTransportCredentials(insecure.NewCredentials()),
		grpc.WithStatsHandler(otelgrpc.NewClientHandler()),
		grpc.WithUnaryInterceptor(grpc_middleware_sentry.UnaryClientInterceptor()),
		grpc.WithUnaryInterceptor(errors.UnaryClientInterceptor()),
	}

	conn, err := grpc.NewClient(endpoint, opts...)
	if err != nil {
		return nil, err
	}

	return NewTransactionClient(conn), nil
}
