package main

import (
	"fmt"
	"strings"

	"google.golang.org/protobuf/compiler/protogen"
)

type SimpleTable struct {
	file *protogen.GeneratedFile
	data [][]any
}

func (s *SimpleTable) AddRow(row ...any) {
	s.data = append(s.data, row)
}

func (s *SimpleTable) AddRowWithPrefixes(prefixes []any, row ...any) {
	s.data = append(s.data, append(prefixes, row...))
}

func (s *SimpleTable) String() string {
	var columnCount int
	for _, row := range s.data {
		rowColumnCount := len(row)
		if rowColumnCount > columnCount {
			columnCount = rowColumnCount
		}
	}

	for index := 0; index < len(s.data); index++ {
		for i := len(s.data[index]); i < columnCount; i++ {
			s.data[index] = append(s.data[index], "")
		}
	}

	columnSizes := make([]int, columnCount)
	for _, row := range s.data {
		for j, column := range row {
			size := len(fmt.Sprint(column))
			if size > columnSizes[j] {
				columnSizes[j] = size
			}
		}
	}

	var format string
	for i := 0; i < columnCount; i++ {
		format += fmt.Sprintf("%%-%vv\t", columnSizes[i])
	}

	var result []string
	for _, row := range s.data {
		result = append(result, fmt.Sprintf(format, row...))
	}

	return strings.Join(result, "\n")
}

func (s *SimpleTable) ToPrint(
	in any,
) any {
	switch typed := in.(type) {
	case []any:
		for index, a := range typed {
			typed[index] = s.ToPrint(a)
		}

		return fmt.Sprint(typed)
	case protogen.GoIdent:
		return s.file.QualifiedGoIdent(typed)
	default:
		return fmt.Sprint(in)
	}
}

func NewSimpleTable(
	file *protogen.GeneratedFile,
) *SimpleTable {
	return &SimpleTable{
		file: file,
		data: make([][]any, 0),
	}
}
