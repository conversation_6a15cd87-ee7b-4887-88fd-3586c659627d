-- +goose Up
-- +goose StatementBegin
CREATE TABLE IF NOT EXISTS "antifraud"."pay_status"
(
    "created_at"
    TIMESTAMP
    DEFAULT
    NOW
(
),
    "updated_at" TIMESTAMP DEFAULT NOW
(
),
    "deleted_at" TIMESTAMP,
    "pay_status_id" SERIAL PRIMARY KEY,
    "pay_status_code" VARCHAR
(
    100
) UNIQUE,
    "pay_status_name" VARCHAR
(
    100
),
    );

CREATE TABLE IF NOT EXISTS "antifraud"."pay"
(
    "created_at"
    TIMESTAMP
    DEFAULT
    NOW
(
),
    "updated_at" TIMESTAMP DEFAULT NOW
(
),
    "deleted_at" TIMESTAMP,
    "pay_id" SERIAL PRIMARY KEY,
    "ip_address" VARCHAR
(
    100
),
    "pay_code" INT,
    "payments" VARCHAR
(
    100
)[],
    "comment" VARCHAR
(
    100
),
    "description" VARCHAR
(
    100
),
    );
-- +goose StatementEnd

-- +goose Down
-- +goose StatementBegin
DROP TABLE "antifraud"."pay";
DROP TABLE "antifraud"."pay_status";
-- +goose StatementEnd
