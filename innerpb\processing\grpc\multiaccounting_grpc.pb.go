// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.5.1
// - protoc             v5.29.3
// source: inner/processing/grpc/multiaccounting.proto

package grpc

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.64.0 or later.
const _ = grpc.SupportPackageIsVersion9

const (
	Multiaccounting_MakeMerchantCheck_FullMethodName    = "/processing.multiaccounting.multiaccounting.Multiaccounting/MakeMerchantCheck"
	Multiaccounting_MakeTransfer_FullMethodName         = "/processing.multiaccounting.multiaccounting.Multiaccounting/MakeTransfer"
	Multiaccounting_AcceptTransfer_FullMethodName       = "/processing.multiaccounting.multiaccounting.Multiaccounting/AcceptTransfer"
	Multiaccounting_DeclineTransfer_FullMethodName      = "/processing.multiaccounting.multiaccounting.Multiaccounting/DeclineTransfer"
	Multiaccounting_RedoTransfer_FullMethodName         = "/processing.multiaccounting.multiaccounting.Multiaccounting/RedoTransfer"
	Multiaccounting_GetTransferDetails_FullMethodName   = "/processing.multiaccounting.multiaccounting.Multiaccounting/GetTransferDetails"
	Multiaccounting_GetTransfersList_FullMethodName     = "/processing.multiaccounting.multiaccounting.Multiaccounting/GetTransfersList"
	Multiaccounting_GetAccountBalance_FullMethodName    = "/processing.multiaccounting.multiaccounting.Multiaccounting/GetAccountBalance"
	Multiaccounting_GetAccountStatement_FullMethodName  = "/processing.multiaccounting.multiaccounting.Multiaccounting/GetAccountStatement"
	Multiaccounting_GetAccountIdentifier_FullMethodName = "/processing.multiaccounting.multiaccounting.Multiaccounting/GetAccountIdentifier"
)

// MultiaccountingClient is the client API for Multiaccounting service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type MultiaccountingClient interface {
	MakeMerchantCheck(ctx context.Context, in *MakeMerchantCheckRequest, opts ...grpc.CallOption) (*MakeMerchantCheckResponse, error)
	MakeTransfer(ctx context.Context, in *MakeTransferRequest, opts ...grpc.CallOption) (*MakeTransferResponse, error)
	AcceptTransfer(ctx context.Context, in *AcceptTransferRequest, opts ...grpc.CallOption) (*AcceptTransferResponse, error)
	DeclineTransfer(ctx context.Context, in *DeclineTransferRequest, opts ...grpc.CallOption) (*DeclineTransferResponse, error)
	RedoTransfer(ctx context.Context, in *RedoTransferRequest, opts ...grpc.CallOption) (*RedoTransferResponse, error)
	GetTransferDetails(ctx context.Context, in *GetTransferDetailsRequest, opts ...grpc.CallOption) (*GetTransferDetailsResponse, error)
	GetTransfersList(ctx context.Context, in *GetTransfersListRequest, opts ...grpc.CallOption) (*GetTransfersListResponse, error)
	GetAccountBalance(ctx context.Context, in *GetAccountBalanceRequest, opts ...grpc.CallOption) (*GetAccountBalanceResponse, error)
	GetAccountStatement(ctx context.Context, in *GetAccountStatementRequest, opts ...grpc.CallOption) (*GetAccountStatementResponse, error)
	GetAccountIdentifier(ctx context.Context, in *emptypb.Empty, opts ...grpc.CallOption) (*GetAccountIdentifierResponse, error)
}

type multiaccountingClient struct {
	cc grpc.ClientConnInterface
}

func NewMultiaccountingClient(cc grpc.ClientConnInterface) MultiaccountingClient {
	return &multiaccountingClient{cc}
}

func (c *multiaccountingClient) MakeMerchantCheck(ctx context.Context, in *MakeMerchantCheckRequest, opts ...grpc.CallOption) (*MakeMerchantCheckResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(MakeMerchantCheckResponse)
	err := c.cc.Invoke(ctx, Multiaccounting_MakeMerchantCheck_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *multiaccountingClient) MakeTransfer(ctx context.Context, in *MakeTransferRequest, opts ...grpc.CallOption) (*MakeTransferResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(MakeTransferResponse)
	err := c.cc.Invoke(ctx, Multiaccounting_MakeTransfer_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *multiaccountingClient) AcceptTransfer(ctx context.Context, in *AcceptTransferRequest, opts ...grpc.CallOption) (*AcceptTransferResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(AcceptTransferResponse)
	err := c.cc.Invoke(ctx, Multiaccounting_AcceptTransfer_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *multiaccountingClient) DeclineTransfer(ctx context.Context, in *DeclineTransferRequest, opts ...grpc.CallOption) (*DeclineTransferResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(DeclineTransferResponse)
	err := c.cc.Invoke(ctx, Multiaccounting_DeclineTransfer_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *multiaccountingClient) RedoTransfer(ctx context.Context, in *RedoTransferRequest, opts ...grpc.CallOption) (*RedoTransferResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(RedoTransferResponse)
	err := c.cc.Invoke(ctx, Multiaccounting_RedoTransfer_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *multiaccountingClient) GetTransferDetails(ctx context.Context, in *GetTransferDetailsRequest, opts ...grpc.CallOption) (*GetTransferDetailsResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetTransferDetailsResponse)
	err := c.cc.Invoke(ctx, Multiaccounting_GetTransferDetails_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *multiaccountingClient) GetTransfersList(ctx context.Context, in *GetTransfersListRequest, opts ...grpc.CallOption) (*GetTransfersListResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetTransfersListResponse)
	err := c.cc.Invoke(ctx, Multiaccounting_GetTransfersList_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *multiaccountingClient) GetAccountBalance(ctx context.Context, in *GetAccountBalanceRequest, opts ...grpc.CallOption) (*GetAccountBalanceResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetAccountBalanceResponse)
	err := c.cc.Invoke(ctx, Multiaccounting_GetAccountBalance_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *multiaccountingClient) GetAccountStatement(ctx context.Context, in *GetAccountStatementRequest, opts ...grpc.CallOption) (*GetAccountStatementResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetAccountStatementResponse)
	err := c.cc.Invoke(ctx, Multiaccounting_GetAccountStatement_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *multiaccountingClient) GetAccountIdentifier(ctx context.Context, in *emptypb.Empty, opts ...grpc.CallOption) (*GetAccountIdentifierResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetAccountIdentifierResponse)
	err := c.cc.Invoke(ctx, Multiaccounting_GetAccountIdentifier_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// MultiaccountingServer is the server API for Multiaccounting service.
// All implementations must embed UnimplementedMultiaccountingServer
// for forward compatibility.
type MultiaccountingServer interface {
	MakeMerchantCheck(context.Context, *MakeMerchantCheckRequest) (*MakeMerchantCheckResponse, error)
	MakeTransfer(context.Context, *MakeTransferRequest) (*MakeTransferResponse, error)
	AcceptTransfer(context.Context, *AcceptTransferRequest) (*AcceptTransferResponse, error)
	DeclineTransfer(context.Context, *DeclineTransferRequest) (*DeclineTransferResponse, error)
	RedoTransfer(context.Context, *RedoTransferRequest) (*RedoTransferResponse, error)
	GetTransferDetails(context.Context, *GetTransferDetailsRequest) (*GetTransferDetailsResponse, error)
	GetTransfersList(context.Context, *GetTransfersListRequest) (*GetTransfersListResponse, error)
	GetAccountBalance(context.Context, *GetAccountBalanceRequest) (*GetAccountBalanceResponse, error)
	GetAccountStatement(context.Context, *GetAccountStatementRequest) (*GetAccountStatementResponse, error)
	GetAccountIdentifier(context.Context, *emptypb.Empty) (*GetAccountIdentifierResponse, error)
	mustEmbedUnimplementedMultiaccountingServer()
}

// UnimplementedMultiaccountingServer must be embedded to have
// forward compatible implementations.
//
// NOTE: this should be embedded by value instead of pointer to avoid a nil
// pointer dereference when methods are called.
type UnimplementedMultiaccountingServer struct{}

func (UnimplementedMultiaccountingServer) MakeMerchantCheck(context.Context, *MakeMerchantCheckRequest) (*MakeMerchantCheckResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method MakeMerchantCheck not implemented")
}
func (UnimplementedMultiaccountingServer) MakeTransfer(context.Context, *MakeTransferRequest) (*MakeTransferResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method MakeTransfer not implemented")
}
func (UnimplementedMultiaccountingServer) AcceptTransfer(context.Context, *AcceptTransferRequest) (*AcceptTransferResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method AcceptTransfer not implemented")
}
func (UnimplementedMultiaccountingServer) DeclineTransfer(context.Context, *DeclineTransferRequest) (*DeclineTransferResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeclineTransfer not implemented")
}
func (UnimplementedMultiaccountingServer) RedoTransfer(context.Context, *RedoTransferRequest) (*RedoTransferResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method RedoTransfer not implemented")
}
func (UnimplementedMultiaccountingServer) GetTransferDetails(context.Context, *GetTransferDetailsRequest) (*GetTransferDetailsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetTransferDetails not implemented")
}
func (UnimplementedMultiaccountingServer) GetTransfersList(context.Context, *GetTransfersListRequest) (*GetTransfersListResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetTransfersList not implemented")
}
func (UnimplementedMultiaccountingServer) GetAccountBalance(context.Context, *GetAccountBalanceRequest) (*GetAccountBalanceResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetAccountBalance not implemented")
}
func (UnimplementedMultiaccountingServer) GetAccountStatement(context.Context, *GetAccountStatementRequest) (*GetAccountStatementResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetAccountStatement not implemented")
}
func (UnimplementedMultiaccountingServer) GetAccountIdentifier(context.Context, *emptypb.Empty) (*GetAccountIdentifierResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetAccountIdentifier not implemented")
}
func (UnimplementedMultiaccountingServer) mustEmbedUnimplementedMultiaccountingServer() {}
func (UnimplementedMultiaccountingServer) testEmbeddedByValue()                         {}

// UnsafeMultiaccountingServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to MultiaccountingServer will
// result in compilation errors.
type UnsafeMultiaccountingServer interface {
	mustEmbedUnimplementedMultiaccountingServer()
}

func RegisterMultiaccountingServer(s grpc.ServiceRegistrar, srv MultiaccountingServer) {
	// If the following call pancis, it indicates UnimplementedMultiaccountingServer was
	// embedded by pointer and is nil.  This will cause panics if an
	// unimplemented method is ever invoked, so we test this at initialization
	// time to prevent it from happening at runtime later due to I/O.
	if t, ok := srv.(interface{ testEmbeddedByValue() }); ok {
		t.testEmbeddedByValue()
	}
	s.RegisterService(&Multiaccounting_ServiceDesc, srv)
}

func _Multiaccounting_MakeMerchantCheck_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(MakeMerchantCheckRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MultiaccountingServer).MakeMerchantCheck(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Multiaccounting_MakeMerchantCheck_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MultiaccountingServer).MakeMerchantCheck(ctx, req.(*MakeMerchantCheckRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Multiaccounting_MakeTransfer_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(MakeTransferRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MultiaccountingServer).MakeTransfer(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Multiaccounting_MakeTransfer_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MultiaccountingServer).MakeTransfer(ctx, req.(*MakeTransferRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Multiaccounting_AcceptTransfer_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AcceptTransferRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MultiaccountingServer).AcceptTransfer(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Multiaccounting_AcceptTransfer_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MultiaccountingServer).AcceptTransfer(ctx, req.(*AcceptTransferRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Multiaccounting_DeclineTransfer_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeclineTransferRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MultiaccountingServer).DeclineTransfer(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Multiaccounting_DeclineTransfer_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MultiaccountingServer).DeclineTransfer(ctx, req.(*DeclineTransferRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Multiaccounting_RedoTransfer_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RedoTransferRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MultiaccountingServer).RedoTransfer(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Multiaccounting_RedoTransfer_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MultiaccountingServer).RedoTransfer(ctx, req.(*RedoTransferRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Multiaccounting_GetTransferDetails_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetTransferDetailsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MultiaccountingServer).GetTransferDetails(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Multiaccounting_GetTransferDetails_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MultiaccountingServer).GetTransferDetails(ctx, req.(*GetTransferDetailsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Multiaccounting_GetTransfersList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetTransfersListRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MultiaccountingServer).GetTransfersList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Multiaccounting_GetTransfersList_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MultiaccountingServer).GetTransfersList(ctx, req.(*GetTransfersListRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Multiaccounting_GetAccountBalance_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetAccountBalanceRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MultiaccountingServer).GetAccountBalance(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Multiaccounting_GetAccountBalance_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MultiaccountingServer).GetAccountBalance(ctx, req.(*GetAccountBalanceRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Multiaccounting_GetAccountStatement_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetAccountStatementRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MultiaccountingServer).GetAccountStatement(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Multiaccounting_GetAccountStatement_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MultiaccountingServer).GetAccountStatement(ctx, req.(*GetAccountStatementRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Multiaccounting_GetAccountIdentifier_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(emptypb.Empty)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MultiaccountingServer).GetAccountIdentifier(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Multiaccounting_GetAccountIdentifier_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MultiaccountingServer).GetAccountIdentifier(ctx, req.(*emptypb.Empty))
	}
	return interceptor(ctx, in, info, handler)
}

// Multiaccounting_ServiceDesc is the grpc.ServiceDesc for Multiaccounting service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var Multiaccounting_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "processing.multiaccounting.multiaccounting.Multiaccounting",
	HandlerType: (*MultiaccountingServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "MakeMerchantCheck",
			Handler:    _Multiaccounting_MakeMerchantCheck_Handler,
		},
		{
			MethodName: "MakeTransfer",
			Handler:    _Multiaccounting_MakeTransfer_Handler,
		},
		{
			MethodName: "AcceptTransfer",
			Handler:    _Multiaccounting_AcceptTransfer_Handler,
		},
		{
			MethodName: "DeclineTransfer",
			Handler:    _Multiaccounting_DeclineTransfer_Handler,
		},
		{
			MethodName: "RedoTransfer",
			Handler:    _Multiaccounting_RedoTransfer_Handler,
		},
		{
			MethodName: "GetTransferDetails",
			Handler:    _Multiaccounting_GetTransferDetails_Handler,
		},
		{
			MethodName: "GetTransfersList",
			Handler:    _Multiaccounting_GetTransfersList_Handler,
		},
		{
			MethodName: "GetAccountBalance",
			Handler:    _Multiaccounting_GetAccountBalance_Handler,
		},
		{
			MethodName: "GetAccountStatement",
			Handler:    _Multiaccounting_GetAccountStatement_Handler,
		},
		{
			MethodName: "GetAccountIdentifier",
			Handler:    _Multiaccounting_GetAccountIdentifier_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "inner/processing/grpc/multiaccounting.proto",
}
