// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.5
// 	protoc        v5.29.3
// source: proto/goerr.proto

package pb

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	descriptorpb "google.golang.org/protobuf/types/descriptorpb"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type ErrorType int32

const (
	ErrorType_NOT_ErrorType ErrorType = 0
	// ErrorTypeAuthorization тип для ошибок авторизации
	ErrorType_ErrorTypeAuthorization ErrorType = 1
	// ErrorTypeBadRequest тип для ошибок валидации/некорректного ввода от пользователя
	ErrorType_ErrorTypeBadRequest ErrorType = 2
	// ErrorTypeNotFound тип для ошибок не найденного объекта (404)
	ErrorType_ErrorTypeNotFound ErrorType = 3
	// ErrorTypeForbidden тип для ошибок прав доступа
	ErrorType_ErrorTypeForbidden ErrorType = 4
	// ErrorTypeIntegration тип для интеграции со сторонними сервисами (прим. ошибки запросов)
	ErrorType_ErrorTypeIntegration ErrorType = 5
	// ErrorTypeDbViolation тип для ошибок целостности базы данных
	// (ошибки внешних ключей, нет записи которая должна быть и тд)
	ErrorType_ErrorTypeDbViolation ErrorType = 6
	// ErrorTypeUnexpectedError Тип для неожиданных/неконтролируемых ошибок.
	// Данные ошибки должны логироваться в глобальных обработчиках ошибок и
	// отправлены в Sentry при наличии его на проекте.
	ErrorType_ErrorTypeUnexpectedError ErrorType = 7
)

// Enum value maps for ErrorType.
var (
	ErrorType_name = map[int32]string{
		0: "NOT_ErrorType",
		1: "ErrorTypeAuthorization",
		2: "ErrorTypeBadRequest",
		3: "ErrorTypeNotFound",
		4: "ErrorTypeForbidden",
		5: "ErrorTypeIntegration",
		6: "ErrorTypeDbViolation",
		7: "ErrorTypeUnexpectedError",
	}
	ErrorType_value = map[string]int32{
		"NOT_ErrorType":            0,
		"ErrorTypeAuthorization":   1,
		"ErrorTypeBadRequest":      2,
		"ErrorTypeNotFound":        3,
		"ErrorTypeForbidden":       4,
		"ErrorTypeIntegration":     5,
		"ErrorTypeDbViolation":     6,
		"ErrorTypeUnexpectedError": 7,
	}
)

func (x ErrorType) Enum() *ErrorType {
	p := new(ErrorType)
	*p = x
	return p
}

func (x ErrorType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ErrorType) Descriptor() protoreflect.EnumDescriptor {
	return file_proto_goerr_proto_enumTypes[0].Descriptor()
}

func (ErrorType) Type() protoreflect.EnumType {
	return &file_proto_goerr_proto_enumTypes[0]
}

func (x ErrorType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ErrorType.Descriptor instead.
func (ErrorType) EnumDescriptor() ([]byte, []int) {
	return file_proto_goerr_proto_rawDescGZIP(), []int{0}
}

// from here "google.golang.org/grpc/codes"
type GrpcCode int32

const (
	GrpcCode_OK                 GrpcCode = 0
	GrpcCode_Canceled           GrpcCode = 1
	GrpcCode_Unknown            GrpcCode = 2
	GrpcCode_InvalidArgument    GrpcCode = 3
	GrpcCode_DeadlineExceeded   GrpcCode = 4
	GrpcCode_NotFound           GrpcCode = 5
	GrpcCode_AlreadyExists      GrpcCode = 6
	GrpcCode_PermissionDenied   GrpcCode = 7
	GrpcCode_ResourceExhausted  GrpcCode = 8
	GrpcCode_FailedPrecondition GrpcCode = 9
	GrpcCode_Aborted            GrpcCode = 10
	GrpcCode_OutOfRange         GrpcCode = 11
	GrpcCode_Unimplemented      GrpcCode = 12
	GrpcCode_Internal           GrpcCode = 13
	GrpcCode_Unavailable        GrpcCode = 14
	GrpcCode_DataLoss           GrpcCode = 15
	GrpcCode_Unauthenticated    GrpcCode = 16
	GrpcCode__maxCode           GrpcCode = 17
)

// Enum value maps for GrpcCode.
var (
	GrpcCode_name = map[int32]string{
		0:  "OK",
		1:  "Canceled",
		2:  "Unknown",
		3:  "InvalidArgument",
		4:  "DeadlineExceeded",
		5:  "NotFound",
		6:  "AlreadyExists",
		7:  "PermissionDenied",
		8:  "ResourceExhausted",
		9:  "FailedPrecondition",
		10: "Aborted",
		11: "OutOfRange",
		12: "Unimplemented",
		13: "Internal",
		14: "Unavailable",
		15: "DataLoss",
		16: "Unauthenticated",
		17: "_maxCode",
	}
	GrpcCode_value = map[string]int32{
		"OK":                 0,
		"Canceled":           1,
		"Unknown":            2,
		"InvalidArgument":    3,
		"DeadlineExceeded":   4,
		"NotFound":           5,
		"AlreadyExists":      6,
		"PermissionDenied":   7,
		"ResourceExhausted":  8,
		"FailedPrecondition": 9,
		"Aborted":            10,
		"OutOfRange":         11,
		"Unimplemented":      12,
		"Internal":           13,
		"Unavailable":        14,
		"DataLoss":           15,
		"Unauthenticated":    16,
		"_maxCode":           17,
	}
)

func (x GrpcCode) Enum() *GrpcCode {
	p := new(GrpcCode)
	*p = x
	return p
}

func (x GrpcCode) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (GrpcCode) Descriptor() protoreflect.EnumDescriptor {
	return file_proto_goerr_proto_enumTypes[1].Descriptor()
}

func (GrpcCode) Type() protoreflect.EnumType {
	return &file_proto_goerr_proto_enumTypes[1]
}

func (x GrpcCode) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use GrpcCode.Descriptor instead.
func (GrpcCode) EnumDescriptor() ([]byte, []int) {
	return file_proto_goerr_proto_rawDescGZIP(), []int{1}
}

type ErrorDescribe struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Code          *GrpcCode              `protobuf:"varint,2,opt,name=code,enum=mvp.GrpcCode" json:"code,omitempty"`
	Description   *string                `protobuf:"bytes,3,opt,name=description" json:"description,omitempty"`
	ErrorType     *ErrorType             `protobuf:"varint,4,opt,name=error_type,json=errorType,enum=mvp.ErrorType" json:"error_type,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ErrorDescribe) Reset() {
	*x = ErrorDescribe{}
	mi := &file_proto_goerr_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ErrorDescribe) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ErrorDescribe) ProtoMessage() {}

func (x *ErrorDescribe) ProtoReflect() protoreflect.Message {
	mi := &file_proto_goerr_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ErrorDescribe.ProtoReflect.Descriptor instead.
func (*ErrorDescribe) Descriptor() ([]byte, []int) {
	return file_proto_goerr_proto_rawDescGZIP(), []int{0}
}

func (x *ErrorDescribe) GetCode() GrpcCode {
	if x != nil && x.Code != nil {
		return *x.Code
	}
	return GrpcCode_OK
}

func (x *ErrorDescribe) GetDescription() string {
	if x != nil && x.Description != nil {
		return *x.Description
	}
	return ""
}

func (x *ErrorDescribe) GetErrorType() ErrorType {
	if x != nil && x.ErrorType != nil {
		return *x.ErrorType
	}
	return ErrorType_NOT_ErrorType
}

type GoErrMetadata struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	ErrorType     *string                `protobuf:"bytes,1,opt,name=error_type,json=errorType" json:"error_type,omitempty"`
	Message       *string                `protobuf:"bytes,2,opt,name=message" json:"message,omitempty"`
	Code          *int64                 `protobuf:"varint,3,opt,name=code" json:"code,omitempty"`
	IsExpected    *bool                  `protobuf:"varint,4,opt,name=is_expected,json=isExpected" json:"is_expected,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GoErrMetadata) Reset() {
	*x = GoErrMetadata{}
	mi := &file_proto_goerr_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GoErrMetadata) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GoErrMetadata) ProtoMessage() {}

func (x *GoErrMetadata) ProtoReflect() protoreflect.Message {
	mi := &file_proto_goerr_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GoErrMetadata.ProtoReflect.Descriptor instead.
func (*GoErrMetadata) Descriptor() ([]byte, []int) {
	return file_proto_goerr_proto_rawDescGZIP(), []int{1}
}

func (x *GoErrMetadata) GetErrorType() string {
	if x != nil && x.ErrorType != nil {
		return *x.ErrorType
	}
	return ""
}

func (x *GoErrMetadata) GetMessage() string {
	if x != nil && x.Message != nil {
		return *x.Message
	}
	return ""
}

func (x *GoErrMetadata) GetCode() int64 {
	if x != nil && x.Code != nil {
		return *x.Code
	}
	return 0
}

func (x *GoErrMetadata) GetIsExpected() bool {
	if x != nil && x.IsExpected != nil {
		return *x.IsExpected
	}
	return false
}

var file_proto_goerr_proto_extTypes = []protoimpl.ExtensionInfo{
	{
		ExtendedType:  (*descriptorpb.EnumValueOptions)(nil),
		ExtensionType: (*string)(nil),
		Field:         71000401,
		Name:          "mvp.error_type_description",
		Tag:           "bytes,71000401,opt,name=error_type_description",
		Filename:      "proto/goerr.proto",
	},
	{
		ExtendedType:  (*descriptorpb.EnumValueOptions)(nil),
		ExtensionType: (*ErrorDescribe)(nil),
		Field:         71000402,
		Name:          "mvp.error_describe",
		Tag:           "bytes,71000402,opt,name=error_describe",
		Filename:      "proto/goerr.proto",
	},
}

// Extension fields to descriptorpb.EnumValueOptions.
var (
	// optional string error_type_description = 71000401;
	E_ErrorTypeDescription = &file_proto_goerr_proto_extTypes[0]
	// optional mvp.ErrorDescribe error_describe = 71000402;
	E_ErrorDescribe = &file_proto_goerr_proto_extTypes[1]
)

var File_proto_goerr_proto protoreflect.FileDescriptor

var file_proto_goerr_proto_rawDesc = string([]byte{
	0x0a, 0x11, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f, 0x67, 0x6f, 0x65, 0x72, 0x72, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x12, 0x03, 0x6d, 0x76, 0x70, 0x1a, 0x20, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69,
	0x70, 0x74, 0x6f, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x83, 0x01, 0x0a, 0x0d, 0x45,
	0x72, 0x72, 0x6f, 0x72, 0x44, 0x65, 0x73, 0x63, 0x72, 0x69, 0x62, 0x65, 0x12, 0x21, 0x0a, 0x04,
	0x63, 0x6f, 0x64, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x0d, 0x2e, 0x6d, 0x76, 0x70,
	0x2e, 0x47, 0x72, 0x70, 0x63, 0x43, 0x6f, 0x64, 0x65, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x12,
	0x20, 0x0a, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f,
	0x6e, 0x12, 0x2d, 0x0a, 0x0a, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x0e, 0x2e, 0x6d, 0x76, 0x70, 0x2e, 0x45, 0x72, 0x72, 0x6f,
	0x72, 0x54, 0x79, 0x70, 0x65, 0x52, 0x09, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x54, 0x79, 0x70, 0x65,
	0x22, 0x7d, 0x0a, 0x0d, 0x47, 0x6f, 0x45, 0x72, 0x72, 0x4d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74,
	0x61, 0x12, 0x1d, 0x0a, 0x0a, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x54, 0x79, 0x70, 0x65,
	0x12, 0x18, 0x0a, 0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x6f,
	0x64, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x12, 0x1f,
	0x0a, 0x0b, 0x69, 0x73, 0x5f, 0x65, 0x78, 0x70, 0x65, 0x63, 0x74, 0x65, 0x64, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x08, 0x52, 0x0a, 0x69, 0x73, 0x45, 0x78, 0x70, 0x65, 0x63, 0x74, 0x65, 0x64, 0x2a,
	0xdd, 0x02, 0x0a, 0x09, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x54, 0x79, 0x70, 0x65, 0x12, 0x11, 0x0a,
	0x0d, 0x4e, 0x4f, 0x54, 0x5f, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x54, 0x79, 0x70, 0x65, 0x10, 0x00,
	0x12, 0x2f, 0x0a, 0x16, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x54, 0x79, 0x70, 0x65, 0x41, 0x75, 0x74,
	0x68, 0x6f, 0x72, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x10, 0x01, 0x1a, 0x13, 0x8a, 0x95,
	0xec, 0x8e, 0x02, 0x0d, 0x61, 0x75, 0x74, 0x68, 0x6f, 0x72, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x12, 0x2a, 0x0a, 0x13, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x54, 0x79, 0x70, 0x65, 0x42, 0x61,
	0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x10, 0x02, 0x1a, 0x11, 0x8a, 0x95, 0xec, 0x8e,
	0x02, 0x0b, 0x62, 0x61, 0x64, 0x2d, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x26, 0x0a,
	0x11, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x54, 0x79, 0x70, 0x65, 0x4e, 0x6f, 0x74, 0x46, 0x6f, 0x75,
	0x6e, 0x64, 0x10, 0x03, 0x1a, 0x0f, 0x8a, 0x95, 0xec, 0x8e, 0x02, 0x09, 0x6e, 0x6f, 0x74, 0x2d,
	0x66, 0x6f, 0x75, 0x6e, 0x64, 0x12, 0x27, 0x0a, 0x12, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x54, 0x79,
	0x70, 0x65, 0x46, 0x6f, 0x72, 0x62, 0x69, 0x64, 0x64, 0x65, 0x6e, 0x10, 0x04, 0x1a, 0x0f, 0x8a,
	0x95, 0xec, 0x8e, 0x02, 0x09, 0x66, 0x6f, 0x72, 0x62, 0x69, 0x64, 0x64, 0x65, 0x6e, 0x12, 0x2b,
	0x0a, 0x14, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x54, 0x79, 0x70, 0x65, 0x49, 0x6e, 0x74, 0x65, 0x67,
	0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x10, 0x05, 0x1a, 0x11, 0x8a, 0x95, 0xec, 0x8e, 0x02, 0x0b,
	0x69, 0x6e, 0x74, 0x65, 0x67, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x2c, 0x0a, 0x14, 0x45,
	0x72, 0x72, 0x6f, 0x72, 0x54, 0x79, 0x70, 0x65, 0x44, 0x62, 0x56, 0x69, 0x6f, 0x6c, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x10, 0x06, 0x1a, 0x12, 0x8a, 0x95, 0xec, 0x8e, 0x02, 0x0c, 0x64, 0x62, 0x2d,
	0x76, 0x69, 0x6f, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x34, 0x0a, 0x18, 0x45, 0x72, 0x72,
	0x6f, 0x72, 0x54, 0x79, 0x70, 0x65, 0x55, 0x6e, 0x65, 0x78, 0x70, 0x65, 0x63, 0x74, 0x65, 0x64,
	0x45, 0x72, 0x72, 0x6f, 0x72, 0x10, 0x07, 0x1a, 0x16, 0x8a, 0x95, 0xec, 0x8e, 0x02, 0x10, 0x75,
	0x6e, 0x65, 0x78, 0x70, 0x65, 0x63, 0x74, 0x65, 0x64, 0x2d, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x2a,
	0xbe, 0x02, 0x0a, 0x08, 0x47, 0x72, 0x70, 0x63, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x06, 0x0a, 0x02,
	0x4f, 0x4b, 0x10, 0x00, 0x12, 0x0c, 0x0a, 0x08, 0x43, 0x61, 0x6e, 0x63, 0x65, 0x6c, 0x65, 0x64,
	0x10, 0x01, 0x12, 0x0b, 0x0a, 0x07, 0x55, 0x6e, 0x6b, 0x6e, 0x6f, 0x77, 0x6e, 0x10, 0x02, 0x12,
	0x13, 0x0a, 0x0f, 0x49, 0x6e, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x41, 0x72, 0x67, 0x75, 0x6d, 0x65,
	0x6e, 0x74, 0x10, 0x03, 0x12, 0x14, 0x0a, 0x10, 0x44, 0x65, 0x61, 0x64, 0x6c, 0x69, 0x6e, 0x65,
	0x45, 0x78, 0x63, 0x65, 0x65, 0x64, 0x65, 0x64, 0x10, 0x04, 0x12, 0x0c, 0x0a, 0x08, 0x4e, 0x6f,
	0x74, 0x46, 0x6f, 0x75, 0x6e, 0x64, 0x10, 0x05, 0x12, 0x11, 0x0a, 0x0d, 0x41, 0x6c, 0x72, 0x65,
	0x61, 0x64, 0x79, 0x45, 0x78, 0x69, 0x73, 0x74, 0x73, 0x10, 0x06, 0x12, 0x14, 0x0a, 0x10, 0x50,
	0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x44, 0x65, 0x6e, 0x69, 0x65, 0x64, 0x10,
	0x07, 0x12, 0x15, 0x0a, 0x11, 0x52, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x45, 0x78, 0x68,
	0x61, 0x75, 0x73, 0x74, 0x65, 0x64, 0x10, 0x08, 0x12, 0x16, 0x0a, 0x12, 0x46, 0x61, 0x69, 0x6c,
	0x65, 0x64, 0x50, 0x72, 0x65, 0x63, 0x6f, 0x6e, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x10, 0x09,
	0x12, 0x0b, 0x0a, 0x07, 0x41, 0x62, 0x6f, 0x72, 0x74, 0x65, 0x64, 0x10, 0x0a, 0x12, 0x0e, 0x0a,
	0x0a, 0x4f, 0x75, 0x74, 0x4f, 0x66, 0x52, 0x61, 0x6e, 0x67, 0x65, 0x10, 0x0b, 0x12, 0x11, 0x0a,
	0x0d, 0x55, 0x6e, 0x69, 0x6d, 0x70, 0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x65, 0x64, 0x10, 0x0c,
	0x12, 0x0c, 0x0a, 0x08, 0x49, 0x6e, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x10, 0x0d, 0x12, 0x0f,
	0x0a, 0x0b, 0x55, 0x6e, 0x61, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x6c, 0x65, 0x10, 0x0e, 0x12,
	0x0c, 0x0a, 0x08, 0x44, 0x61, 0x74, 0x61, 0x4c, 0x6f, 0x73, 0x73, 0x10, 0x0f, 0x12, 0x13, 0x0a,
	0x0f, 0x55, 0x6e, 0x61, 0x75, 0x74, 0x68, 0x65, 0x6e, 0x74, 0x69, 0x63, 0x61, 0x74, 0x65, 0x64,
	0x10, 0x10, 0x12, 0x0c, 0x0a, 0x08, 0x5f, 0x6d, 0x61, 0x78, 0x43, 0x6f, 0x64, 0x65, 0x10, 0x11,
	0x3a, 0x5a, 0x0a, 0x16, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x5f, 0x64,
	0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x21, 0x2e, 0x67, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x45, 0x6e, 0x75,
	0x6d, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0xd1, 0xc2,
	0xed, 0x21, 0x20, 0x01, 0x28, 0x09, 0x52, 0x14, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x54, 0x79, 0x70,
	0x65, 0x44, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x3a, 0x5f, 0x0a, 0x0e,
	0x65, 0x72, 0x72, 0x6f, 0x72, 0x5f, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x62, 0x65, 0x12, 0x21,
	0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66,
	0x2e, 0x45, 0x6e, 0x75, 0x6d, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e,
	0x73, 0x18, 0xd2, 0xc2, 0xed, 0x21, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x6d, 0x76, 0x70,
	0x2e, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x44, 0x65, 0x73, 0x63, 0x72, 0x69, 0x62, 0x65, 0x52, 0x0d,
	0x65, 0x72, 0x72, 0x6f, 0x72, 0x44, 0x65, 0x73, 0x63, 0x72, 0x69, 0x62, 0x65, 0x42, 0x1c, 0x5a,
	0x1a, 0x67, 0x69, 0x74, 0x2e, 0x6c, 0x6f, 0x63, 0x61, 0x6c, 0x2f, 0x73, 0x65, 0x6e, 0x73, 0x69,
	0x74, 0x69, 0x76, 0x65, 0x2f, 0x6d, 0x76, 0x70, 0x2f, 0x70, 0x62, 0x62, 0x08, 0x65, 0x64, 0x69,
	0x74, 0x69, 0x6f, 0x6e, 0x73, 0x70, 0xe8, 0x07,
})

var (
	file_proto_goerr_proto_rawDescOnce sync.Once
	file_proto_goerr_proto_rawDescData []byte
)

func file_proto_goerr_proto_rawDescGZIP() []byte {
	file_proto_goerr_proto_rawDescOnce.Do(func() {
		file_proto_goerr_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_proto_goerr_proto_rawDesc), len(file_proto_goerr_proto_rawDesc)))
	})
	return file_proto_goerr_proto_rawDescData
}

var file_proto_goerr_proto_enumTypes = make([]protoimpl.EnumInfo, 2)
var file_proto_goerr_proto_msgTypes = make([]protoimpl.MessageInfo, 2)
var file_proto_goerr_proto_goTypes = []any{
	(ErrorType)(0),                        // 0: mvp.ErrorType
	(GrpcCode)(0),                         // 1: mvp.GrpcCode
	(*ErrorDescribe)(nil),                 // 2: mvp.ErrorDescribe
	(*GoErrMetadata)(nil),                 // 3: mvp.GoErrMetadata
	(*descriptorpb.EnumValueOptions)(nil), // 4: google.protobuf.EnumValueOptions
}
var file_proto_goerr_proto_depIdxs = []int32{
	1, // 0: mvp.ErrorDescribe.code:type_name -> mvp.GrpcCode
	0, // 1: mvp.ErrorDescribe.error_type:type_name -> mvp.ErrorType
	4, // 2: mvp.error_type_description:extendee -> google.protobuf.EnumValueOptions
	4, // 3: mvp.error_describe:extendee -> google.protobuf.EnumValueOptions
	2, // 4: mvp.error_describe:type_name -> mvp.ErrorDescribe
	5, // [5:5] is the sub-list for method output_type
	5, // [5:5] is the sub-list for method input_type
	4, // [4:5] is the sub-list for extension type_name
	2, // [2:4] is the sub-list for extension extendee
	0, // [0:2] is the sub-list for field type_name
}

func init() { file_proto_goerr_proto_init() }
func file_proto_goerr_proto_init() {
	if File_proto_goerr_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_proto_goerr_proto_rawDesc), len(file_proto_goerr_proto_rawDesc)),
			NumEnums:      2,
			NumMessages:   2,
			NumExtensions: 2,
			NumServices:   0,
		},
		GoTypes:           file_proto_goerr_proto_goTypes,
		DependencyIndexes: file_proto_goerr_proto_depIdxs,
		EnumInfos:         file_proto_goerr_proto_enumTypes,
		MessageInfos:      file_proto_goerr_proto_msgTypes,
		ExtensionInfos:    file_proto_goerr_proto_extTypes,
	}.Build()
	File_proto_goerr_proto = out.File
	file_proto_goerr_proto_goTypes = nil
	file_proto_goerr_proto_depIdxs = nil
}
