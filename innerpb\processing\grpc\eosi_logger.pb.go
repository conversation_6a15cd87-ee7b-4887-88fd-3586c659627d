// Code generated by protoc-gen-go-mvp. DO NOT EDIT.
// versions:
// - protoc-gen-go-mvp v2.0.1
// created by mvp authors

package grpc

import (
	context "context"
	fmt "fmt"
	cntx "git.local/sensitive/mvp/pkg/cntx"
	ctxzap "github.com/grpc-ecosystem/go-grpc-middleware/logging/zap/ctxzap"
	zap "go.uber.org/zap"
	grpc "google.golang.org/grpc"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
)

func file_inner_processing_grpc_eosi_proto_message_TimestampToZap(
	label string,
	in *timestamppb.Timestamp,
) zap.Field {
	if in == nil {
		return zap.Reflect(label, in)
	}

	return zap.Dict(
		label,
		zap.Any("Seconds", in.GetSeconds()),
		zap.Any("Nanos", in.GetNanos()),
	)
}

func file_inner_processing_grpc_eosi_proto_message_GetOrderingIdentifierRequestToZap(
	label string,
	in *GetOrderingIdentifierRequest,
) zap.Field {
	if in == nil {
		return zap.Reflect(label, in)
	}

	return zap.Dict(
		label,
	)
}

func file_inner_processing_grpc_eosi_proto_message_GetOrderingIdentifierResponseToZap(
	label string,
	in *GetOrderingIdentifierResponse,
) zap.Field {
	if in == nil {
		return zap.Reflect(label, in)
	}

	return zap.Dict(
		label,
		zap.Any("Identifier", in.GetIdentifier()),
	)
}

func file_inner_processing_grpc_eosi_proto_message_GetPaymentOrderRequestToZap(
	label string,
	in *GetPaymentOrderRequest,
) zap.Field {
	if in == nil {
		return zap.Reflect(label, in)
	}

	return zap.Dict(
		label,
		file_inner_processing_grpc_eosi_proto_message_TimestampToZap("Date", in.GetDate()),
	)
}

func file_inner_processing_grpc_eosi_proto_message_GetPaymentOrderResponseToZap(
	label string,
	in *GetPaymentOrderResponse,
) zap.Field {
	if in == nil {
		return zap.Reflect(label, in)
	}

	return zap.Dict(
		label,
		zap.Any("ExternalId", in.GetExternalId()),
		file_inner_processing_grpc_eosi_proto_message_GetPaymentOrderResponse_SubmerchantDataSliceToZap("Submerchants", in.GetSubmerchants()),
	)
}

func file_inner_processing_grpc_eosi_proto_message_GetPaymentOrderResponse_SubmerchantDataToZap(
	label string,
	in *GetPaymentOrderResponse_SubmerchantData,
) zap.Field {
	if in == nil {
		return zap.Reflect(label, in)
	}

	return zap.Dict(
		label,
		zap.Any("Bin", in.GetBin()),
		zap.Any("Name", in.GetName()),
		zap.Any("RecipientAccount", in.GetRecipientAccount()),
		zap.Any("PaymentPurposeCode", in.GetPaymentPurposeCode()),
		zap.Any("BeneficiaryCode", in.GetBeneficiaryCode()),
		zap.Any("Description", in.GetDescription()),
		zap.Any("Amount", in.GetAmount()),
	)
}

func file_inner_processing_grpc_eosi_proto_message_GetPaymentOrderResponse_SubmerchantDataSliceToZap(
	label string,
	in []*GetPaymentOrderResponse_SubmerchantData,
) zap.Field {
	var res []zap.Field
	for index, val := range in {
		res = append(res, file_inner_processing_grpc_eosi_proto_message_GetPaymentOrderResponse_SubmerchantDataToZap(fmt.Sprint(index), val))
	}

	return zap.Dict(label, res...)
}

var _ EosiServer = (*loggedEosiServer)(nil)

func NewLoggedEosiServer(srv EosiServer) EosiServer {
	return &loggedEosiServer{srv: srv}
}

type loggedEosiServer struct {
	UnimplementedEosiServer

	srv EosiServer
}

func (s *loggedEosiServer) GetPaymentOrder(
	ctx context.Context,
	request *GetPaymentOrderRequest,
) (
	response *GetPaymentOrderResponse,
	err error,
) {
	label := cntx.Begin(ctx, "EosiServer_GetPaymentOrder")
	defer cntx.End(ctx, label)

	defer func() {
		if err != nil {
			ctxzap.AddFields(ctx, zap.Any(label+"error", err))
			return
		}

		ctxzap.AddFields(
			ctx,
			file_inner_processing_grpc_eosi_proto_message_GetPaymentOrderResponseToZap(label+"response", response),
		)
	}()

	ctxzap.AddFields(
		ctx,
		file_inner_processing_grpc_eosi_proto_message_GetPaymentOrderRequestToZap(label+"request", request),
	)

	response, err = s.srv.GetPaymentOrder(ctx, request)

	return
}

func (s *loggedEosiServer) GetOrderingIdentifier(
	ctx context.Context,
	request *GetOrderingIdentifierRequest,
) (
	response *GetOrderingIdentifierResponse,
	err error,
) {
	label := cntx.Begin(ctx, "EosiServer_GetOrderingIdentifier")
	defer cntx.End(ctx, label)

	defer func() {
		if err != nil {
			ctxzap.AddFields(ctx, zap.Any(label+"error", err))
			return
		}

		ctxzap.AddFields(
			ctx,
			file_inner_processing_grpc_eosi_proto_message_GetOrderingIdentifierResponseToZap(label+"response", response),
		)
	}()

	ctxzap.AddFields(
		ctx,
		file_inner_processing_grpc_eosi_proto_message_GetOrderingIdentifierRequestToZap(label+"request", request),
	)

	response, err = s.srv.GetOrderingIdentifier(ctx, request)

	return
}

var _ EosiClient = (*loggedEosiClient)(nil)

func NewLoggedEosiClient(client EosiClient) EosiClient {
	return &loggedEosiClient{client: client}
}

type loggedEosiClient struct {
	client EosiClient
}

func (s *loggedEosiClient) GetPaymentOrder(
	ctx context.Context,
	request *GetPaymentOrderRequest,
	opts ...grpc.CallOption,
) (
	response *GetPaymentOrderResponse,
	err error,
) {
	label := cntx.Begin(ctx, "EosiClient_GetPaymentOrder")
	defer cntx.End(ctx, label)

	defer func() {
		if err != nil {
			ctxzap.AddFields(ctx, zap.Any(label+"error", err))
			return
		}

		ctxzap.AddFields(
			ctx,
			file_inner_processing_grpc_eosi_proto_message_GetPaymentOrderResponseToZap(label+"response", response),
		)
	}()

	ctxzap.AddFields(
		ctx,
		file_inner_processing_grpc_eosi_proto_message_GetPaymentOrderRequestToZap(label+"request", request),
	)

	response, err = s.client.GetPaymentOrder(ctx, request, opts...)

	return
}

func (s *loggedEosiClient) GetOrderingIdentifier(
	ctx context.Context,
	request *GetOrderingIdentifierRequest,
	opts ...grpc.CallOption,
) (
	response *GetOrderingIdentifierResponse,
	err error,
) {
	label := cntx.Begin(ctx, "EosiClient_GetOrderingIdentifier")
	defer cntx.End(ctx, label)

	defer func() {
		if err != nil {
			ctxzap.AddFields(ctx, zap.Any(label+"error", err))
			return
		}

		ctxzap.AddFields(
			ctx,
			file_inner_processing_grpc_eosi_proto_message_GetOrderingIdentifierResponseToZap(label+"response", response),
		)
	}()

	ctxzap.AddFields(
		ctx,
		file_inner_processing_grpc_eosi_proto_message_GetOrderingIdentifierRequestToZap(label+"request", request),
	)

	response, err = s.client.GetOrderingIdentifier(ctx, request, opts...)

	return
}
