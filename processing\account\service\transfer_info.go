package service

import (
	"context"
	"git.local/sensitive/processing/account/model"
	"git.local/sensitive/processing/account/repository"
	"git.local/sensitive/processing/account/schema"
	"git.local/sensitive/sdk/dog"
)

type TransferInfoService struct {
	transferRepo repository.Transferer
}

func NewTransferInfoService(transferRepo repository.Transferer) TransferInformationer {
	return &TransferInfoService{
		transferRepo: transferRepo,
	}
}

func (ts *TransferInfoService) GetTransferById(
	ctx context.Context,
	transferID uint64,
) (_ *model.Transfer, err error) {
	ctx, span := dog.CreateSpan(ctx, "TransferInfoService_GetTransferById")
	defer span.End()

	return ts.transferRepo.GetByID(ctx, transferID)
}

func (ts *TransferInfoService) GetTransfersByFilters(
	ctx context.Context,
	filters schema.TransferFilters,
) (_ []model.Transfer, err error) {
	ctx, span := dog.CreateSpan(ctx, "TransferInfoService_GetTransfersByFilters")
	defer span.End()

	return ts.transferRepo.GetByFilters(ctx, filters)
}

func (ts *TransferInfoService) GetTransfersByMerchantId(
	ctx context.Context,
	merchantID uint64,
) (_ []model.Transfer, err error) {
	ctx, span := dog.CreateSpan(ctx, "TransferInfoService_GetTransfersByMerchantId")
	defer span.End()

	return ts.transferRepo.GetByMerchantID(ctx, merchantID)
}

func (ts *TransferInfoService) GetTransfersByProjectId(
	ctx context.Context,
	projectID uint64,
) (_ []model.Transfer, err error) {
	ctx, span := dog.CreateSpan(ctx, "TransferService_GetTransfersByProjectId")
	defer span.End()

	return ts.transferRepo.GetByProjectID(ctx, projectID)
}
