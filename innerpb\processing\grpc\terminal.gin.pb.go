// Code generated by protoc-gen-go-mvp. DO NOT EDIT.
// versions:
// - protoc-gen-go-mvp v2.0.1
// created by mvp authors

package grpc

import (
	dog "git.local/sensitive/sdk/dog"
	gin "github.com/gin-gonic/gin"
	metadata "google.golang.org/grpc/metadata"
	http "net/http"
)

func RegisterGinTerminalRouterGroup(
	in *gin.RouterGroup,
	handler func(func(c *gin.Context) error) gin.HandlerFunc,
) error {
	service, err := NewGinTerminalService()
	if err != nil {
		return err
	}

	routerGroup := in.Group("processing.acquirer.terminal.Terminal")
	routerGroup.PUT("/GetByTerminalID", handler(service.GetByTerminalID))
	routerGroup.PUT("/FindActiveTerminalsByProject", handler(service.FindActiveTerminalsByProject))
	routerGroup.PUT("/SearchTerminal", handler(service.SearchTerminal))
	routerGroup.PUT("/ExtendedSearchTerminal", handler(service.ExtendedSearchTerminal))
	routerGroup.PUT("/GetTerminalsByProjectId", handler(service.GetTerminalsByProjectId))
	routerGroup.PUT("/GetTerminalWithJusan", handler(service.GetTerminalWithJusan))
	routerGroup.PUT("/GetRuleByActiveTerminals", handler(service.GetRuleByActiveTerminals))
	routerGroup.PUT("/GetPayInProjectTerminals", handler(service.GetPayInProjectTerminals))
	return nil
}

func NewGinTerminalService() (GinTerminalServer, error) {
	client, err := NewPreparedTerminalClient()
	if err != nil {
		return nil, err
	}

	return &ginTerminalServer{
		client: NewLoggedTerminalClient(
			NewIamTerminalClient(client),
		),
	}, nil
}

//go:generate mockgen -destination=./grpcmock/terminal.gin.pb.go -package=grpcmock -source=terminal.gin.pb.go GinTerminalServer
type GinTerminalServer interface {
	GetByTerminalID(c *gin.Context) error
	FindActiveTerminalsByProject(c *gin.Context) error
	SearchTerminal(c *gin.Context) error
	ExtendedSearchTerminal(c *gin.Context) error
	GetTerminalsByProjectId(c *gin.Context) error
	GetTerminalWithJusan(c *gin.Context) error
	GetRuleByActiveTerminals(c *gin.Context) error
	GetPayInProjectTerminals(c *gin.Context) error
}

var _ GinTerminalServer = (*ginTerminalServer)(nil)

type ginTerminalServer struct {
	client TerminalClient
}

type Terminal_GetByTerminalID_Success struct {
	// Status is the status of the response. Set true
	Status bool `json:"status"`
	// Message is a human-readable explanation of the status
	Message string `json:"message"`
	// StatusCode is the GoErr status. Set 0
	StatusCode int `json:"status_code"`
	// Result is the result of the request
	Result *TerminalResponseV1 `json:"result"`
}

type Terminal_GetByTerminalID_Failure struct {
	// Status is the status of the response. Set false
	Status bool `json:"status"`
	// Message is a human-readable explanation of the status
	Message string `json:"message"`
	// StatusCode is the GoErr status
	StatusCode int `json:"status_code"`
}

// GetByTerminalID
// @Summary GetByTerminalID Запрос на получение данных терминала и его эквайера
// @Security bearerAuth
// @ID Terminal_GetByTerminalID
// @Accept json
// @Param request body TerminalRequestV1 true "TerminalRequestV1"
// @Success 200 {object} Terminal_GetByTerminalID_Success
// @Failure 401 {object} Terminal_GetByTerminalID_Failure "goerrpkg.ErrorTypeAuthorization"
// @Failure 404 {object} Terminal_GetByTerminalID_Failure "goerrpkg.ErrorTypeNotFound"
// @Failure 400 {object} Terminal_GetByTerminalID_Failure "goerrpkg.ErrorTypeBadRequest"
// @Failure 500 {object} Terminal_GetByTerminalID_Failure "goerrpkg.ErrorTypeUnexpectedError"
// @Failure 400 {object} Terminal_GetByTerminalID_Failure "gin.ErrorTypeBind"
// @Failure 500 {object} Terminal_GetByTerminalID_Failure "Undefined error"
// @Produce json
// @Router /processing.acquirer.terminal.Terminal/GetByTerminalID [put]
// @tags Terminal
func (s *ginTerminalServer) GetByTerminalID(c *gin.Context) error {
	ctx, span := dog.CreateSpan(c.Request.Context(), "GinTerminalServer_GetByTerminalID")
	defer span.End()

	var request TerminalRequestV1
	if err := c.ShouldBindJSON(&request); err != nil {
		return err
	}

	ctx = metadata.NewOutgoingContext(ctx, metadata.MD(c.Request.Header))

	response, err := s.client.GetByTerminalID(ctx, &request)
	if err != nil {
		return err
	}

	c.JSON(http.StatusOK, &Terminal_GetByTerminalID_Success{
		Status:     true,
		Message:    "Success",
		StatusCode: 0,
		Result:     response,
	})
	return nil
}

type Terminal_FindActiveTerminalsByProject_Success struct {
	// Status is the status of the response. Set true
	Status bool `json:"status"`
	// Message is a human-readable explanation of the status
	Message string `json:"message"`
	// StatusCode is the GoErr status. Set 0
	StatusCode int `json:"status_code"`
	// Result is the result of the request
	Result *ActiveTerminalsByProjectResponseV1 `json:"result"`
}

type Terminal_FindActiveTerminalsByProject_Failure struct {
	// Status is the status of the response. Set false
	Status bool `json:"status"`
	// Message is a human-readable explanation of the status
	Message string `json:"message"`
	// StatusCode is the GoErr status
	StatusCode int `json:"status_code"`
}

// FindActiveTerminalsByProject
// @Summary GetAllActiveTerminals Запрос на получение всех активных терминалов
// @Security bearerAuth
// @ID Terminal_FindActiveTerminalsByProject
// @Accept json
// @Param request body ActiveTerminalsByProjectRequestV1 true "ActiveTerminalsByProjectRequestV1"
// @Success 200 {object} Terminal_FindActiveTerminalsByProject_Success
// @Failure 401 {object} Terminal_FindActiveTerminalsByProject_Failure "goerrpkg.ErrorTypeAuthorization"
// @Failure 404 {object} Terminal_FindActiveTerminalsByProject_Failure "goerrpkg.ErrorTypeNotFound"
// @Failure 400 {object} Terminal_FindActiveTerminalsByProject_Failure "goerrpkg.ErrorTypeBadRequest"
// @Failure 500 {object} Terminal_FindActiveTerminalsByProject_Failure "goerrpkg.ErrorTypeUnexpectedError"
// @Failure 400 {object} Terminal_FindActiveTerminalsByProject_Failure "gin.ErrorTypeBind"
// @Failure 500 {object} Terminal_FindActiveTerminalsByProject_Failure "Undefined error"
// @Produce json
// @Router /processing.acquirer.terminal.Terminal/FindActiveTerminalsByProject [put]
// @tags Terminal
func (s *ginTerminalServer) FindActiveTerminalsByProject(c *gin.Context) error {
	ctx, span := dog.CreateSpan(c.Request.Context(), "GinTerminalServer_FindActiveTerminalsByProject")
	defer span.End()

	var request ActiveTerminalsByProjectRequestV1
	if err := c.ShouldBindJSON(&request); err != nil {
		return err
	}

	ctx = metadata.NewOutgoingContext(ctx, metadata.MD(c.Request.Header))

	response, err := s.client.FindActiveTerminalsByProject(ctx, &request)
	if err != nil {
		return err
	}

	c.JSON(http.StatusOK, &Terminal_FindActiveTerminalsByProject_Success{
		Status:     true,
		Message:    "Success",
		StatusCode: 0,
		Result:     response,
	})
	return nil
}

type Terminal_SearchTerminal_Success struct {
	// Status is the status of the response. Set true
	Status bool `json:"status"`
	// Message is a human-readable explanation of the status
	Message string `json:"message"`
	// StatusCode is the GoErr status. Set 0
	StatusCode int `json:"status_code"`
	// Result is the result of the request
	Result *SearchTerminalResDataV1 `json:"result"`
}

type Terminal_SearchTerminal_Failure struct {
	// Status is the status of the response. Set false
	Status bool `json:"status"`
	// Message is a human-readable explanation of the status
	Message string `json:"message"`
	// StatusCode is the GoErr status
	StatusCode int `json:"status_code"`
}

// SearchTerminal
// @Summary SearchTerminal
// @Security bearerAuth
// @ID Terminal_SearchTerminal
// @Accept json
// @Param request body SearchTerminalReqDataV1 true "SearchTerminalReqDataV1"
// @Success 200 {object} Terminal_SearchTerminal_Success
// @Failure 401 {object} Terminal_SearchTerminal_Failure "goerrpkg.ErrorTypeAuthorization"
// @Failure 404 {object} Terminal_SearchTerminal_Failure "goerrpkg.ErrorTypeNotFound"
// @Failure 400 {object} Terminal_SearchTerminal_Failure "goerrpkg.ErrorTypeBadRequest"
// @Failure 500 {object} Terminal_SearchTerminal_Failure "goerrpkg.ErrorTypeUnexpectedError"
// @Failure 400 {object} Terminal_SearchTerminal_Failure "gin.ErrorTypeBind"
// @Failure 500 {object} Terminal_SearchTerminal_Failure "Undefined error"
// @Produce json
// @Router /processing.acquirer.terminal.Terminal/SearchTerminal [put]
// @tags Terminal
func (s *ginTerminalServer) SearchTerminal(c *gin.Context) error {
	ctx, span := dog.CreateSpan(c.Request.Context(), "GinTerminalServer_SearchTerminal")
	defer span.End()

	var request SearchTerminalReqDataV1
	if err := c.ShouldBindJSON(&request); err != nil {
		return err
	}

	ctx = metadata.NewOutgoingContext(ctx, metadata.MD(c.Request.Header))

	response, err := s.client.SearchTerminal(ctx, &request)
	if err != nil {
		return err
	}

	c.JSON(http.StatusOK, &Terminal_SearchTerminal_Success{
		Status:     true,
		Message:    "Success",
		StatusCode: 0,
		Result:     response,
	})
	return nil
}

type Terminal_ExtendedSearchTerminal_Success struct {
	// Status is the status of the response. Set true
	Status bool `json:"status"`
	// Message is a human-readable explanation of the status
	Message string `json:"message"`
	// StatusCode is the GoErr status. Set 0
	StatusCode int `json:"status_code"`
	// Result is the result of the request
	Result *SearchTerminalResDataV1 `json:"result"`
}

type Terminal_ExtendedSearchTerminal_Failure struct {
	// Status is the status of the response. Set false
	Status bool `json:"status"`
	// Message is a human-readable explanation of the status
	Message string `json:"message"`
	// StatusCode is the GoErr status
	StatusCode int `json:"status_code"`
}

// ExtendedSearchTerminal
// @Summary ExtendedSearchTerminal
// @Security bearerAuth
// @ID Terminal_ExtendedSearchTerminal
// @Accept json
// @Param request body ExtendedSearchTerminalReqDataV1 true "ExtendedSearchTerminalReqDataV1"
// @Success 200 {object} Terminal_ExtendedSearchTerminal_Success
// @Failure 401 {object} Terminal_ExtendedSearchTerminal_Failure "goerrpkg.ErrorTypeAuthorization"
// @Failure 404 {object} Terminal_ExtendedSearchTerminal_Failure "goerrpkg.ErrorTypeNotFound"
// @Failure 400 {object} Terminal_ExtendedSearchTerminal_Failure "goerrpkg.ErrorTypeBadRequest"
// @Failure 500 {object} Terminal_ExtendedSearchTerminal_Failure "goerrpkg.ErrorTypeUnexpectedError"
// @Failure 400 {object} Terminal_ExtendedSearchTerminal_Failure "gin.ErrorTypeBind"
// @Failure 500 {object} Terminal_ExtendedSearchTerminal_Failure "Undefined error"
// @Produce json
// @Router /processing.acquirer.terminal.Terminal/ExtendedSearchTerminal [put]
// @tags Terminal
func (s *ginTerminalServer) ExtendedSearchTerminal(c *gin.Context) error {
	ctx, span := dog.CreateSpan(c.Request.Context(), "GinTerminalServer_ExtendedSearchTerminal")
	defer span.End()

	var request ExtendedSearchTerminalReqDataV1
	if err := c.ShouldBindJSON(&request); err != nil {
		return err
	}

	ctx = metadata.NewOutgoingContext(ctx, metadata.MD(c.Request.Header))

	response, err := s.client.ExtendedSearchTerminal(ctx, &request)
	if err != nil {
		return err
	}

	c.JSON(http.StatusOK, &Terminal_ExtendedSearchTerminal_Success{
		Status:     true,
		Message:    "Success",
		StatusCode: 0,
		Result:     response,
	})
	return nil
}

type Terminal_GetTerminalsByProjectId_Success struct {
	// Status is the status of the response. Set true
	Status bool `json:"status"`
	// Message is a human-readable explanation of the status
	Message string `json:"message"`
	// StatusCode is the GoErr status. Set 0
	StatusCode int `json:"status_code"`
	// Result is the result of the request
	Result *ActiveTerminalsByProjectResponseV1 `json:"result"`
}

type Terminal_GetTerminalsByProjectId_Failure struct {
	// Status is the status of the response. Set false
	Status bool `json:"status"`
	// Message is a human-readable explanation of the status
	Message string `json:"message"`
	// StatusCode is the GoErr status
	StatusCode int `json:"status_code"`
}

// GetTerminalsByProjectId
// @Summary GetTerminalsByProjectId
// @Security bearerAuth
// @ID Terminal_GetTerminalsByProjectId
// @Accept json
// @Param request body GetTerminalsByProjectIdRequestV1 true "GetTerminalsByProjectIdRequestV1"
// @Success 200 {object} Terminal_GetTerminalsByProjectId_Success
// @Failure 401 {object} Terminal_GetTerminalsByProjectId_Failure "goerrpkg.ErrorTypeAuthorization"
// @Failure 404 {object} Terminal_GetTerminalsByProjectId_Failure "goerrpkg.ErrorTypeNotFound"
// @Failure 400 {object} Terminal_GetTerminalsByProjectId_Failure "goerrpkg.ErrorTypeBadRequest"
// @Failure 500 {object} Terminal_GetTerminalsByProjectId_Failure "goerrpkg.ErrorTypeUnexpectedError"
// @Failure 400 {object} Terminal_GetTerminalsByProjectId_Failure "gin.ErrorTypeBind"
// @Failure 500 {object} Terminal_GetTerminalsByProjectId_Failure "Undefined error"
// @Produce json
// @Router /processing.acquirer.terminal.Terminal/GetTerminalsByProjectId [put]
// @tags Terminal
func (s *ginTerminalServer) GetTerminalsByProjectId(c *gin.Context) error {
	ctx, span := dog.CreateSpan(c.Request.Context(), "GinTerminalServer_GetTerminalsByProjectId")
	defer span.End()

	var request GetTerminalsByProjectIdRequestV1
	if err := c.ShouldBindJSON(&request); err != nil {
		return err
	}

	ctx = metadata.NewOutgoingContext(ctx, metadata.MD(c.Request.Header))

	response, err := s.client.GetTerminalsByProjectId(ctx, &request)
	if err != nil {
		return err
	}

	c.JSON(http.StatusOK, &Terminal_GetTerminalsByProjectId_Success{
		Status:     true,
		Message:    "Success",
		StatusCode: 0,
		Result:     response,
	})
	return nil
}

type Terminal_GetTerminalWithJusan_Success struct {
	// Status is the status of the response. Set true
	Status bool `json:"status"`
	// Message is a human-readable explanation of the status
	Message string `json:"message"`
	// StatusCode is the GoErr status. Set 0
	StatusCode int `json:"status_code"`
	// Result is the result of the request
	Result *GetTerminalWithJusanResponseV1 `json:"result"`
}

type Terminal_GetTerminalWithJusan_Failure struct {
	// Status is the status of the response. Set false
	Status bool `json:"status"`
	// Message is a human-readable explanation of the status
	Message string `json:"message"`
	// StatusCode is the GoErr status
	StatusCode int `json:"status_code"`
}

// GetTerminalWithJusan
// @Summary GetTerminalWithJusan
// @Security bearerAuth
// @ID Terminal_GetTerminalWithJusan
// @Accept json
// @Param request body SearchTerminalReqDataV1 true "SearchTerminalReqDataV1"
// @Success 200 {object} Terminal_GetTerminalWithJusan_Success
// @Failure 401 {object} Terminal_GetTerminalWithJusan_Failure "goerrpkg.ErrorTypeAuthorization"
// @Failure 404 {object} Terminal_GetTerminalWithJusan_Failure "goerrpkg.ErrorTypeNotFound"
// @Failure 400 {object} Terminal_GetTerminalWithJusan_Failure "goerrpkg.ErrorTypeBadRequest"
// @Failure 500 {object} Terminal_GetTerminalWithJusan_Failure "goerrpkg.ErrorTypeUnexpectedError"
// @Failure 400 {object} Terminal_GetTerminalWithJusan_Failure "gin.ErrorTypeBind"
// @Failure 500 {object} Terminal_GetTerminalWithJusan_Failure "Undefined error"
// @Produce json
// @Router /processing.acquirer.terminal.Terminal/GetTerminalWithJusan [put]
// @tags Terminal
func (s *ginTerminalServer) GetTerminalWithJusan(c *gin.Context) error {
	ctx, span := dog.CreateSpan(c.Request.Context(), "GinTerminalServer_GetTerminalWithJusan")
	defer span.End()

	var request SearchTerminalReqDataV1
	if err := c.ShouldBindJSON(&request); err != nil {
		return err
	}

	ctx = metadata.NewOutgoingContext(ctx, metadata.MD(c.Request.Header))

	response, err := s.client.GetTerminalWithJusan(ctx, &request)
	if err != nil {
		return err
	}

	c.JSON(http.StatusOK, &Terminal_GetTerminalWithJusan_Success{
		Status:     true,
		Message:    "Success",
		StatusCode: 0,
		Result:     response,
	})
	return nil
}

type Terminal_GetRuleByActiveTerminals_Success struct {
	// Status is the status of the response. Set true
	Status bool `json:"status"`
	// Message is a human-readable explanation of the status
	Message string `json:"message"`
	// StatusCode is the GoErr status. Set 0
	StatusCode int `json:"status_code"`
	// Result is the result of the request
	Result *RuleByActiveTerminalsResponseV1 `json:"result"`
}

type Terminal_GetRuleByActiveTerminals_Failure struct {
	// Status is the status of the response. Set false
	Status bool `json:"status"`
	// Message is a human-readable explanation of the status
	Message string `json:"message"`
	// StatusCode is the GoErr status
	StatusCode int `json:"status_code"`
}

// GetRuleByActiveTerminals
// @Summary GetRuleByActiveTerminals
// @Security bearerAuth
// @ID Terminal_GetRuleByActiveTerminals
// @Accept json
// @Param request body RuleByActiveTerminalsReqV1 true "RuleByActiveTerminalsReqV1"
// @Success 200 {object} Terminal_GetRuleByActiveTerminals_Success
// @Failure 401 {object} Terminal_GetRuleByActiveTerminals_Failure "goerrpkg.ErrorTypeAuthorization"
// @Failure 404 {object} Terminal_GetRuleByActiveTerminals_Failure "goerrpkg.ErrorTypeNotFound"
// @Failure 400 {object} Terminal_GetRuleByActiveTerminals_Failure "goerrpkg.ErrorTypeBadRequest"
// @Failure 500 {object} Terminal_GetRuleByActiveTerminals_Failure "goerrpkg.ErrorTypeUnexpectedError"
// @Failure 400 {object} Terminal_GetRuleByActiveTerminals_Failure "gin.ErrorTypeBind"
// @Failure 500 {object} Terminal_GetRuleByActiveTerminals_Failure "Undefined error"
// @Produce json
// @Router /processing.acquirer.terminal.Terminal/GetRuleByActiveTerminals [put]
// @tags Terminal
func (s *ginTerminalServer) GetRuleByActiveTerminals(c *gin.Context) error {
	ctx, span := dog.CreateSpan(c.Request.Context(), "GinTerminalServer_GetRuleByActiveTerminals")
	defer span.End()

	var request RuleByActiveTerminalsReqV1
	if err := c.ShouldBindJSON(&request); err != nil {
		return err
	}

	ctx = metadata.NewOutgoingContext(ctx, metadata.MD(c.Request.Header))

	response, err := s.client.GetRuleByActiveTerminals(ctx, &request)
	if err != nil {
		return err
	}

	c.JSON(http.StatusOK, &Terminal_GetRuleByActiveTerminals_Success{
		Status:     true,
		Message:    "Success",
		StatusCode: 0,
		Result:     response,
	})
	return nil
}

type Terminal_GetPayInProjectTerminals_Success struct {
	// Status is the status of the response. Set true
	Status bool `json:"status"`
	// Message is a human-readable explanation of the status
	Message string `json:"message"`
	// StatusCode is the GoErr status. Set 0
	StatusCode int `json:"status_code"`
	// Result is the result of the request
	Result *GetPayInProjectTerminalsResponseV1 `json:"result"`
}

type Terminal_GetPayInProjectTerminals_Failure struct {
	// Status is the status of the response. Set false
	Status bool `json:"status"`
	// Message is a human-readable explanation of the status
	Message string `json:"message"`
	// StatusCode is the GoErr status
	StatusCode int `json:"status_code"`
}

// GetPayInProjectTerminals
// @Summary GetPayInProjectTerminals
// @Security bearerAuth
// @ID Terminal_GetPayInProjectTerminals
// @Accept json
// @Param request body GetPayInProjectTerminalsReqV1 true "GetPayInProjectTerminalsReqV1"
// @Success 200 {object} Terminal_GetPayInProjectTerminals_Success
// @Failure 401 {object} Terminal_GetPayInProjectTerminals_Failure "goerrpkg.ErrorTypeAuthorization"
// @Failure 404 {object} Terminal_GetPayInProjectTerminals_Failure "goerrpkg.ErrorTypeNotFound"
// @Failure 400 {object} Terminal_GetPayInProjectTerminals_Failure "goerrpkg.ErrorTypeBadRequest"
// @Failure 500 {object} Terminal_GetPayInProjectTerminals_Failure "goerrpkg.ErrorTypeUnexpectedError"
// @Failure 400 {object} Terminal_GetPayInProjectTerminals_Failure "gin.ErrorTypeBind"
// @Failure 500 {object} Terminal_GetPayInProjectTerminals_Failure "Undefined error"
// @Produce json
// @Router /processing.acquirer.terminal.Terminal/GetPayInProjectTerminals [put]
// @tags Terminal
func (s *ginTerminalServer) GetPayInProjectTerminals(c *gin.Context) error {
	ctx, span := dog.CreateSpan(c.Request.Context(), "GinTerminalServer_GetPayInProjectTerminals")
	defer span.End()

	var request GetPayInProjectTerminalsReqV1
	if err := c.ShouldBindJSON(&request); err != nil {
		return err
	}

	ctx = metadata.NewOutgoingContext(ctx, metadata.MD(c.Request.Header))

	response, err := s.client.GetPayInProjectTerminals(ctx, &request)
	if err != nil {
		return err
	}

	c.JSON(http.StatusOK, &Terminal_GetPayInProjectTerminals_Success{
		Status:     true,
		Message:    "Success",
		StatusCode: 0,
		Result:     response,
	})
	return nil
}
