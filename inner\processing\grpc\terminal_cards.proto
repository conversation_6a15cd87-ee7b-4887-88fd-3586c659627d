edition = "2023";

package processing.acquirer.terminal_cards;

option go_package = "git.local/sensitive/innerpb/processing/grpc";

import "mvp/proto/logger.proto";

service TerminalCards {
  //GetCardInfoByPan Запрос на получение данных карты по номеру карты
  rpc GetCardInfoByPan(GetCardInfoByPanRequestV1) returns (GetCardInfoByPanResponseV1) {}
}

message GetCardInfoByPanRequestV1 {
  string eightDigitBin = 1 [(mvp.FieldLoggerLevel) = Hidden];
  string sixDigitBin = 2 [(mvp.FieldLoggerLevel) = Hidden];
  string fiveDigitBin = 3 [(mvp.FieldLoggerLevel) = Hidden];
}

message GetCardInfoByPanResponseV1 {
  uint64 ips_id = 1;
  uint64 country_id = 2;
  uint64 issuer_id = 3;
}

