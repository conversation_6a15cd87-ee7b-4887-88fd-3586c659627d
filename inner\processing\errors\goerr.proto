syntax = "proto3";

package mvp;

option go_package = "git.local/sensitive/innerpb/processing/goerr";

import "google/protobuf/descriptor.proto";
import "mvp/proto/goerr.proto";


enum Error {
  NOT_ERROR = 0;
  // Ошибки доступа
  ErrOperationNotPermitted = 1601 [(mvp.error_describe) = {error_type: ErrorTypeForbidden, code: PermissionDenied, description:"operation not permitted"}];
  ErrPayNotFoundInMovementsLogic = 8007 [(mvp.error_describe) = {error_type: ErrorTypeForbidden, code: PermissionDenied, description:"pay not found in movements logic"}];

  ErrActionNotFound = 8100 [(mvp.error_describe) = {error_type: ErrorTypeNotFound, code: NotFound, description: "action not found"}];
  ErrActionReasonNotFound = 8101 [(mvp.error_describe) = {error_type: ErrorTypeNotFound, code: NotFound, description: "action's reason not found"}];
  ErrCommentaryNotFound = 8103 [(mvp.error_describe) = {error_type: ErrorTypeNotFound, code: NotFound, description: "commentary not found"}];
  ErrSupervisorNotFound = 8026 [(mvp.error_describe) = {error_type: ErrorTypeNotFound, code: NotFound, description: "supervisor doesn't exist"}];

  ErrSystemAuthentication = 5509 [(mvp.error_describe) = {error_type: ErrorTypeBadRequest, code: InvalidArgument, description: "system authentication is failed"}];
  ErrCheckAccountFailed = 1700 [(mvp.error_describe) = {error_type: ErrorTypeUnexpectedError, code: Unknown, description: "check account failed"}];
  ErrAccountDoesntExist = 8067 [(mvp.error_describe) = {error_type: ErrorTypeBadRequest, code: InvalidArgument, description: "account doesn't exist"}];

  // Ошибки, связанные с суммой
  ErrAmountWithSign = 3000 [(mvp.error_describe) = {error_type: ErrorTypeBadRequest, code: InvalidArgument, description: "amount must be with amount sign"}];
  ErrPercentage = 3001 [(mvp.error_describe) = {error_type: ErrorTypeBadRequest, code: InvalidArgument, description: "percentage must equal to 100"}];
  ErrAmountSign = 3002 [(mvp.error_describe) = {error_type: ErrorTypeBadRequest, code: InvalidArgument, description: "wrong amount sign"}];

  ErrAmountIsGreaterThanLimit = 8058 [(mvp.error_describe) = {error_type: ErrorTypeBadRequest, code: InvalidArgument, description: "transaction amount is greater than limit"}];
  ErrNotEnoughBalance = 1410 [(mvp.error_describe) = {error_type: ErrorTypeBadRequest, code: InvalidArgument, description: "not enough balance"}];
  ErrMonthlyLimitExceeded = 1411 [(mvp.error_describe) = {error_type: ErrorTypeBadRequest, code: InvalidArgument, description: "monthly limit exceeded"}];
  ErrDailyLimitExceeded = 1412 [(mvp.error_describe) = {error_type: ErrorTypeBadRequest, code: InvalidArgument, description: "daily limit exceeded"}];
  ErrEmptyAmount = 1400 [(mvp.error_describe) = {error_type: ErrorTypeBadRequest, code: InvalidArgument, description: "empty amount"}];
  ErrMustBeMoreThanZero = 5508 [(mvp.error_describe) = {error_type: ErrorTypeUnexpectedError, code: Unknown, description: "amount must be more than 0"}];
  ErrAmountLimit = 1600 [(mvp.error_describe) = {error_type: ErrorTypeBadRequest, code: InvalidArgument, description: "amount limit exceeded"}];

  // Ошибки аутентификации и авторизации
  ErrOTPExpired = 9000 [(mvp.error_describe) = {error_type: ErrorTypeBadRequest, code: InvalidArgument, description: "otp expired"}];
  ErrOTPMismatch = 9001 [(mvp.error_describe) = {error_type: ErrorTypeBadRequest, code: InvalidArgument, description: "otp is not matching"}];
  ErrRoleUsedByUsers = 9104 [(mvp.error_describe) = {error_type: ErrorTypeBadRequest, code: InvalidArgument, description: "role have users"}];
  ErrRoleNotFound = 9100 [(mvp.error_describe) = {error_type: ErrorTypeNotFound, code: NotFound, description: "role doesn't exist"}];
  ErrUserIsBlocked = 9101 [(mvp.error_describe) = {error_type: ErrorTypeAuthorization, code: Unauthenticated, description:  "user is blocked"}];
  ErrRoleIsBinded = 9102 [(mvp.error_describe) = {error_type: ErrorTypeBadRequest, code: InvalidArgument, description: "role is binded to user"}];
  ErrUserNotFound = 9103 [(mvp.error_describe) = {error_type: ErrorTypeNotFound, code: NotFound, description: "user doesn't exist"}];
  ErrInvalidPassword = 7201 [(mvp.error_describe) = {error_type: ErrorTypeAuthorization, code: Unauthenticated, description:  "the provided password is invalid"}];
  ErrJWSExpNotSatisfied = 1603 [(mvp.error_describe) = {error_type: ErrorTypeForbidden, code: PermissionDenied, description: "exp not satisfied"}];
  ErrLoginAlreadyExists = 1300 [(mvp.error_describe) = {error_type: ErrorTypeUnexpectedError, code: Unknown, description: "login already exists"}];
  ErrLoginIsNotVerified = 1301 [(mvp.error_describe) = {error_type: ErrorTypeUnexpectedError, code: Unknown, description: "login is not verified"}];
  ErrCredentialNotFound = 7101 [(mvp.error_describe) = {error_type: ErrorTypeNotFound, code: NotFound, description: "no credential meets the request"}];
  ErrInvalidCredentials = 8006 [(mvp.error_describe) = {error_type: ErrorTypeNotFound, code: NotFound, description: "invalid credentials"}];
  ErrDecodePEM = 7016 [(mvp.error_describe) = {error_type: ErrorTypeBadRequest, code: InvalidArgument, description: "failed to decode PEM block containing public key"}];
  ErrPublicKeyCasting = 7017 [(mvp.error_describe) = {error_type: ErrorTypeBadRequest, code: InvalidArgument, description: "could not cast the public key into the structure"}];

  // Ошибки конфигурации
  ErrTerminalConfigEncryption = 3300 [(mvp.error_describe) = {error_type: ErrorTypeUnexpectedError, code: Unknown, description: "terminal config encryption failed"}];
  ErrMessageNotFound = 3301 [(mvp.error_describe) = {error_type: ErrorTypeUnexpectedError, code: Unknown, description: "message doesn't exist"}];
  ErrSmsNotSent = 3302 [(mvp.error_describe) = {error_type: ErrorTypeUnexpectedError, code: Unknown, description: "failed to send sms"}];
  ErrInvalidValueDbReadDSN = 1414 [(mvp.error_describe) = {error_type: ErrorTypeUnexpectedError, code: Unknown, description: "could not read a valid string value from [DATABASE_READ_DSN]"}];
  ErrInvalidValueDbWriteDSN = 1415 [(mvp.error_describe) = {error_type: ErrorTypeUnexpectedError, code: Unknown, description: "could not read a valid string value from [DATABASE_WRITE_DSN]"}];
  ErrInvalidValueRunEchoWorker = 1416 [(mvp.error_describe) = {error_type: ErrorTypeUnexpectedError, code: Unknown, description: "could not read a valid string value from [RUN_ECHO_WORKER]"}];
  ErrEmptyDataToAuthorizeToVault = 1417 [(mvp.error_describe) = {error_type: ErrorTypeUnexpectedError, code: Unknown, description: "received empty data to process the authorization to vault"}];
  ErrInvalidAuthDetailsFromVault = 1418 [(mvp.error_describe) = {error_type: ErrorTypeUnexpectedError, code: Unknown, description: "did not receive valid auth details from vault"}];
  ErrInvalidTerminalConfig = 6010 [(mvp.error_describe) = {error_type: ErrorTypeUnexpectedError, code: Unknown, description: "invalid terminal config"}];
  ErrNilValueOfSyncOnce = 1413 [(mvp.error_describe) = {error_type: ErrorTypeUnexpectedError, code: Unknown, description: "received a nil value of sync.Once"}];
  ErrEmptyProviderConfig = 1100 [(mvp.error_describe) = {error_type: ErrorTypeBadRequest, code: InvalidArgument, description: "empty provider config"}];
  ErrInvalidProviderConfig = 1101 [(mvp.error_describe) = {error_type: ErrorTypeBadRequest, code: InvalidArgument, description: "invalid provider config"}];
  ErrEmptyProviderCode = 1102 [(mvp.error_describe) = {error_type: ErrorTypeBadRequest, code: InvalidArgument, description: "empty provider code"}];
  ErrEmptyProviderServiceId = 1103 [(mvp.error_describe) = {error_type: ErrorTypeBadRequest, code: InvalidArgument, description: "empty provider service id"}];
  ErrEmptyProviderName = 1104 [(mvp.error_describe) = {error_type: ErrorTypeBadRequest, code: InvalidArgument, description: "empty provider name"}];

  // Ошибки базы данных
  ErrDbUnexpected = 8301 [(mvp.error_describe) = {error_type: ErrorTypeUnexpectedError, code: Unknown, description: "unexpected db error"}];
  ErrDuplicatedKey = 8302 [(mvp.error_describe) = {error_type: ErrorTypeUnexpectedError, code: Unknown, description: "parameters must be unique"}];
  ErrEmailDuplicatedKey = 8303 [(mvp.error_describe) = {error_type: ErrorTypeUnexpectedError, code: Unknown, description: "email must be unique"}];
  ErrForeignKeyViolation = 8305 [(mvp.error_describe) = {error_type: ErrorTypeUnexpectedError, code: Unknown, description: "non-existent parameters"}];
  ErrCodeDuplicatedKey = 8306 [(mvp.error_describe) = {error_type: ErrorTypeUnexpectedError, code: Unknown, description: "code must be unique"}];
  ErrAccountNumDuplicatedKey = 8307 [(mvp.error_describe) = {error_type: ErrorTypeUnexpectedError, code: Unknown, description: "account number must be unique"}];
  ErrPayNull = 8308 [(mvp.error_describe) = {error_type: ErrorTypeUnexpectedError, code: Unknown, description: "pay code is null"}];
  ErrDublicateKeyValue = 8309 [(mvp.error_describe) = {error_type: ErrorTypeDbViolation, code: DataLoss, description:  "value already in use"}];
  ErrNoDataFound = 8310 [(mvp.error_describe) = {error_type: ErrorTypeBadRequest, code: InvalidArgument, description: "there is no data found by the key"}];
  ErrEntityNotFound = 8311 [(mvp.error_describe) = {error_type: ErrorTypeNotFound, code: NotFound, description: "entity does not exist"}];
  ErrActivationError = 8312 [(mvp.error_describe) = {error_type: ErrorTypeUnexpectedError, code: Unknown, description: "activation error"}];
  ErrDeactivationError = 8313 [(mvp.error_describe) = {error_type: ErrorTypeUnexpectedError, code: Unknown, description: "deactivation error"}];
  ErrEmailDublicate = 8314 [(mvp.error_describe) = {error_type: ErrorTypeUnexpectedError, code: Unknown, description: "email must be unique"}];

  // Ошибки, связанные с клиентами и картами
  ErrPanLengthValidation = 3200 [(mvp.error_describe) = {error_type: ErrorTypeBadRequest, code: InvalidArgument, description: "pan length must be greater than 10"}];
  ErrCardDecryption = 3201 [(mvp.error_describe) = {error_type: ErrorTypeUnexpectedError, code: Unknown, description: "card decryption err"}];
  ErrReceivingCardInformation = 3202 [(mvp.error_describe) = {error_type: ErrorTypeBadRequest, code: InvalidArgument, description: "couldn't get any card information"}];
  ErrCardNotFound = 3108 [(mvp.error_describe) = {error_type: ErrorTypeNotFound, code: NotFound, description: "card doesn't exist"}];
  ErrClientNotFound = 4000 [(mvp.error_describe) = {error_type: ErrorTypeNotFound, code: NotFound, description: "client doesn't exist"}];
  ErrClientAlreadyExists = 4001 [(mvp.error_describe) = {error_type: ErrorTypeBadRequest, code: InvalidArgument, description: "client already exists"}];
  ErrCardTransactionNotFound = 4002 [(mvp.error_describe) = {error_type: ErrorTypeNotFound, code: NotFound, description: "card transaction doesn't exist"}];
  ErrCardAlreadyExists = 4005 [(mvp.error_describe) = {error_type: ErrorTypeBadRequest, code: InvalidArgument, description: "card already exists"}];
  ErrCardTransactionAlreadyExists = 4006 [(mvp.error_describe) = {error_type: ErrorTypeBadRequest, code: InvalidArgument, description: "card transaction already exists"}];
  ErrTokenAlreadyExists = 4007 [(mvp.error_describe) = {error_type: ErrorTypeBadRequest, code: InvalidArgument, description: "token already exists"}];
  ErrTokenTransactionAlreadyExists = 4008 [(mvp.error_describe) = {error_type: ErrorTypeBadRequest, code: InvalidArgument, description: "token transaction already exists"}];
  ErrPanLength = 4009 [(mvp.error_describe) = {error_type: ErrorTypeBadRequest, code: InvalidArgument, description: "pan length must be equal 16"}];
  ErrEmptyTerminalID = 4010 [(mvp.error_describe) = {error_type: ErrorTypeBadRequest, code: InvalidArgument, description: "received an empty terminal ID value"}];
  ErrEmptyAcquirerID = 4011 [(mvp.error_describe) = {error_type: ErrorTypeBadRequest, code: InvalidArgument, description: "received an empty acquirer ID value"}];
  ErrEmptyToken = 4012 [(mvp.error_describe) = {error_type: ErrorTypeBadRequest, code: InvalidArgument, description: "received an empty token"}];
  ErrCardAlreadyDeactivated = 4013 [(mvp.error_describe) = {error_type: ErrorTypeBadRequest, code: InvalidArgument, description: "card already deactivated"}];
  ErrClientBlocked = 4014 [(mvp.error_describe) = {error_type: ErrorTypeBadRequest, code: InvalidArgument, description: "client is blocked"}];
  ErrWhileValidatingClientActiveness = 4015 [(mvp.error_describe) = {error_type: ErrorTypeBadRequest, code: InvalidArgument, description: "error while validating client activeness"}];
  ErrEmptyID = 7001 [(mvp.error_describe) = {error_type: ErrorTypeBadRequest, code: InvalidArgument, description: "received an empty ID value"}];
  ErrEmptyCredential = 7002 [(mvp.error_describe) = {error_type: ErrorTypeBadRequest, code: InvalidArgument, description: "received an empty credential body"}];
  ErrEmptyLogin = 7003 [(mvp.error_describe) = {error_type: ErrorTypeBadRequest, code: InvalidArgument, description: "received an empty login"}];
  ErrCannotCreateRequestForToken = 7004 [(mvp.error_describe) = {error_type: ErrorTypeBadRequest, code: InvalidArgument, description: "can not create request for token"}];
  ErrEmptyHolderName = 7005 [(mvp.error_describe) = {error_type: ErrorTypeBadRequest, code: InvalidArgument, description: "did not receive a card holder name"}];
  ErrEmptyCard = 7006 [(mvp.error_describe) = {error_type: ErrorTypeBadRequest, code: InvalidArgument, description: "did not receive a card information"}];
  ErrEmptyTerminal = 7007 [(mvp.error_describe) = {error_type: ErrorTypeBadRequest, code: InvalidArgument, description: "did not receive a terminal information"}];
  ErrEmptyUser = 7008 [(mvp.error_describe) = {error_type: ErrorTypeBadRequest, code: InvalidArgument, description: "did not receive an information about the user"}];
  ErrEmptyPaymentRequest = 7009 [(mvp.error_describe) = {error_type: ErrorTypeBadRequest, code: InvalidArgument, description: "did not receive a payment request information"}];
  ErrEmptyInvoiceID = 7010 [(mvp.error_describe) = {error_type: ErrorTypeBadRequest, code: InvalidArgument, description: "received an empty invoice ID"}];
  ErrEmptyTransactionID = 7011 [(mvp.error_describe) = {error_type: ErrorTypeBadRequest, code: InvalidArgument, description: "received an empty transaction ID"}];
  EmptyThreeDSRequest = 7013 [(mvp.error_describe) = {error_type: ErrorTypeBadRequest, code: InvalidArgument, description: "received an empty 3ds request data"}];
  ErrEmptyCheckStatusRequest = 7014 [(mvp.error_describe) = {error_type: ErrorTypeBadRequest, code: InvalidArgument, description: "did not receive a request for get transaction status method"}];
  ErrEmptyOnboardingRequest = 7021 [(mvp.error_describe) = {error_type: ErrorTypeBadRequest, code: InvalidArgument, description: "received empty onboarding request"}];
  ErrSavingClientToken = 4100 [(mvp.error_describe) = {error_type: ErrorTypeUnexpectedError, code: Unknown, description: "client card saving has been failed"}];
  ErrReceivingClientCard = 4101 [(mvp.error_describe) = {error_type: ErrorTypeUnexpectedError, code: Unknown, description: "could not receive client cards"}];
  ErrApprovingClientCard = 4102 [(mvp.error_describe) = {error_type: ErrorTypeUnexpectedError, code: Unknown, description: "client card approving has been failed"}];
  ErrEncryptingCardMonth = 4106 [(mvp.error_describe) = {error_type: ErrorTypeBadRequest, code: InvalidArgument, description: "an error occurred while encrypting card month"}];
  ErrEncryptingCardYear = 4107 [(mvp.error_describe) = {error_type: ErrorTypeBadRequest, code: InvalidArgument, description: "an error occurred while encrypting card year"}];
  ErrEncryptingCardName = 4108 [(mvp.error_describe) = {error_type: ErrorTypeBadRequest, code: InvalidArgument, description: "an error occurred while encrypting card name"}];
  ErrDecryptingCardMonth = 4109 [(mvp.error_describe) = {error_type: ErrorTypeBadRequest, code: InvalidArgument, description: "an error occurred while decrypting card month"}];
  ErrDecryptingCardYear = 4110 [(mvp.error_describe) = {error_type: ErrorTypeBadRequest, code: InvalidArgument, description: "an error occurred while decrypting card year"}];
  ErrDecryptingCardName = 4111 [(mvp.error_describe) = {error_type: ErrorTypeBadRequest, code: InvalidArgument, description: "an error occurred while decrypting card name"}];
  ErrEncryptingCardPan = 4112 [(mvp.error_describe) = {error_type: ErrorTypeBadRequest, code: InvalidArgument, description: "an error occurred while encrypting card pan"}];
  ErrDecryptingCardPan = 4113 [(mvp.error_describe) = {error_type: ErrorTypeBadRequest, code: InvalidArgument, description: "an error occurred while decrypting card pan"}];
  ErrInvalidCardToken = 4103 [(mvp.error_describe) = {error_type: ErrorTypeBadRequest, code: InvalidArgument, description: "invalid card token"}];
  ErrDecryptingCardToken = 4104 [(mvp.error_describe) = {error_type: ErrorTypeBadRequest, code: InvalidArgument, description: "an error occurred while decrypting card token"}];
  ErrEncryptingCardToken = 4105 [(mvp.error_describe) = {error_type: ErrorTypeBadRequest, code: InvalidArgument, description: "an error occurred while encrypting card token"}];
  ErrMakingPayment = 7018 [(mvp.error_describe) = {error_type: ErrorTypeUnexpectedError, code: Unknown, description: "received error while making a payment"}];
  ErrInvalidUIN = 2201 [(mvp.error_describe) = {error_type: ErrorTypeBadRequest, code: InvalidArgument, description: "invalid uin"}];
  ErrEmptyCardID = 7012 [(mvp.error_describe) = {error_type: ErrorTypeBadRequest, code: InvalidArgument, description: "received an empty card ID"}];
  ErrEmptyMerchantRequest = 8069 [(mvp.error_describe) = {error_type: ErrorTypeBadRequest, code: InvalidArgument, description: "received empty merchant request"}];
  ErrInvalidCardData = 8070 [(mvp.error_describe) = {error_type: ErrorTypeBadRequest, code: InvalidArgument, description:"invalid card data"}];

  // Ошибки клиентов и платежных систем
  ErrTransactionIsNotPayInType = 5500 [(mvp.error_describe) = {error_type: ErrorTypeBadRequest, code: InvalidArgument, description: "transaction is not pay-in type"}];
  ErrTransactionIsNotSuccess = 5501 [(mvp.error_describe) = {error_type: ErrorTypeBadRequest, code: InvalidArgument, description: "transaction is not in success status"}];
  ErrLimitTransactionRefundAmount = 5502 [(mvp.error_describe) = {error_type: ErrorTypeBadRequest, code: InvalidArgument, description: "refunded amount must be less than amount"}];
  ErrNumberMoreThanExpected = 5503 [(mvp.error_describe) = {error_type: ErrorTypeBadRequest, code: InvalidArgument, description: "the number of attempts is more than expected"}];
  ErrMoreThanInitialAmount = 5504 [(mvp.error_describe) = {error_type: ErrorTypeBadRequest, code: InvalidArgument, description: "can't request amount more than initial amount"}];
  ErrAmountDoesNotMatch = 5505 [(mvp.error_describe) = {error_type: ErrorTypeBadRequest, code: InvalidArgument, description: "the amount does not match"}];
  ErrNumberOfDaysIsOver = 5506 [(mvp.error_describe) = {error_type: ErrorTypeBadRequest, code: InvalidArgument, description: "number of days is over"}];
  ErrRefundNotAllowed = 5507 [(mvp.error_describe) = {error_type: ErrorTypeBadRequest, code: InvalidArgument, description: "not allowed to refund"}];
  ErrEmptyBankOrderID = 5510 [(mvp.error_describe) = {error_type: ErrorTypeBadRequest, code: InvalidArgument, description: "received an empty bank order ID value"}];
  ErrRefundSettingCreation = 5511 [(mvp.error_describe) = {error_type: ErrorTypeBadRequest, code: InvalidArgument, description: "create refund setting failed"}];
  ErrRefundReasonCreation = 5512 [(mvp.error_describe) = {error_type: ErrorTypeBadRequest, code: InvalidArgument, description: "create refund reason failed"}];
  ErrNoTransactionRefundToUpdate = 5513 [(mvp.error_describe) = {error_type: ErrorTypeNotFound, code: NotFound, description: "сould not find the appropriate refund transaction for the update"}];
  ErrTransactionRefundCreation = 5514 [(mvp.error_describe) = {error_type: ErrorTypeBadRequest, code: InvalidArgument, description: "create transaction refund failed"}];
  ErrCurrencyNotFound = 3100 [(mvp.error_describe) = {error_type: ErrorTypeNotFound, code: NotFound, description: "currency doesn't exist"}];
  ErrAcquirerNotFound = 3101 [(mvp.error_describe) = {error_type: ErrorTypeNotFound, code: NotFound, description: "acquirer doesn't exist"}];
  ErrAcquirerCardNotFound = 3102 [(mvp.error_describe) = {error_type: ErrorTypeNotFound, code: NotFound, description: "acquirer card doesn't exist"}];
  ErrAcquirerMccNotFound = 3103 [(mvp.error_describe) = {error_type: ErrorTypeNotFound, code: NotFound, description: "acquirer mcc doesn't exist"}];
  ErrBankNotFound = 3104 [(mvp.error_describe) = {error_type: ErrorTypeNotFound, code: NotFound, description: "bank doesn't exist"}];
  ErrBankBinNotFound = 3105 [(mvp.error_describe) = {error_type: ErrorTypeNotFound, code: NotFound, description: "bank bin doesn't exist"}];
  ErrBankLimitNotFound = 3106 [(mvp.error_describe) = {error_type: ErrorTypeNotFound, code: NotFound, description: "bank limit doesn't exist"}];
  ErrBankServiceNotFound = 3107 [(mvp.error_describe) = {error_type: ErrorTypeNotFound, code: NotFound, description: "bank service doesn't exist"}];
  ErrCountryNotFound = 3109 [(mvp.error_describe) = {error_type: ErrorTypeNotFound, code: NotFound, description: "country doesn't exist"}];
  ErrIpsNotFound = 3110 [(mvp.error_describe) = {error_type: ErrorTypeNotFound, code: NotFound, description: "ips doesn't exist"}];
  ErrBalancerNotFound = 3111 [(mvp.error_describe) = {error_type: ErrorTypeNotFound, code: NotFound, description: "balancer doesn't exist"}];
  ErrCountryCurrencyNotFound = 3112 [(mvp.error_describe) = {error_type: ErrorTypeNotFound, code: NotFound, description: "country-currency doesn't exist"}];
  ErrIpsCountryNotFound = 3113 [(mvp.error_describe) = {error_type: ErrorTypeNotFound, code: NotFound, description: "ips-country doesn't exist"}];
  ErrEmitentNotFound = 3114 [(mvp.error_describe) = {error_type: ErrorTypeNotFound, code: NotFound, description: "emitent doesn't exist"}];
  ErrAcquirerPercentageNotFound = 3115 [(mvp.error_describe) = {error_type: ErrorTypeNotFound, code: NotFound, description: "acquirer percentage doesn't exist"}];
  ErrCountryBankNotFound = 3116 [(mvp.error_describe) = {error_type: ErrorTypeNotFound, code: NotFound, description: "country bank doesn't exist"}];
  ErrIpsDigitCodeNotFound = 3117 [(mvp.error_describe) = {error_type: ErrorTypeNotFound, code: NotFound, description: "ips digit code doesn't exist"}];
  ErrTerminalProjectNotFound = 3118 [(mvp.error_describe) = {error_type: ErrorTypeNotFound, code: NotFound, description: "terminal project doesn't exist"}];
  ErrVisaAliasNotFound = 3119 [(mvp.error_describe) = {error_type: ErrorTypeNotFound, code: NotFound, description: "phone number doesn't exists"}];

  // Разные ошибки
  ErrSanctionInfoNotFound = 9600 [(mvp.error_describe) = {error_type: ErrorTypeNotFound, code: NotFound, description: "sanction info not found"}];
  ErrPaymentTypeNotFound = 8004 [(mvp.error_describe) = {error_type: ErrorTypeNotFound, code: NotFound, description: "payment type doesn't exist"}];
  ErrAcquirerCommissionNotFound = 2011 [(mvp.error_describe) = {error_type: ErrorTypeNotFound, code: NotFound, description: "acquirer commission not found"}];
  ErrAcquirerOptionNotFound = 2014 [(mvp.error_describe) = {error_type: ErrorTypeNotFound, code: NotFound, description: "acquirer option not found"}];
  ErrProjectCommissionNotFound = 2012 [(mvp.error_describe) = {error_type: ErrorTypeNotFound, code: NotFound, description: "project commission not found"}];


  // Ошибки финансовых сущностей
  ErrBrokenTransaction = 9700 [(mvp.error_describe) = {error_type: ErrorTypeDbViolation, code: DataLoss, description:  "transaction missing required fields"}];
  ErrUpdatingCurrentBalance = 1443 [(mvp.error_describe) = {error_type: ErrorTypeUnexpectedError, code: Unknown, description: "couldn't update current balance"}];
  ErrFinalizeTransactionStatus = 9709 [(mvp.error_describe) = {error_type: ErrorTypeBadRequest, code: InvalidArgument, description: "failed to finalize transaction status"}];
  ErrBankStatusNotChanged = 9716 [(mvp.error_describe) = {error_type: ErrorTypeBadRequest, code: InvalidArgument, description: "bank status has not been changed"}];
  ErrAggregatedTransactionTypeNotFound = 2016 [(mvp.error_describe) = {error_type: ErrorTypeNotFound, code: NotFound, description: "aggregated transaction type not found"}];
  ErrTransactionsAreNotFinalized = 5108 [(mvp.error_describe) = {error_type: ErrorTypeBadRequest, code: InvalidArgument, description: "transaction(s) is(are) not finalized"}];
  ErrTransactionsAreNotTheSame = 5109 [(mvp.error_describe) = {error_type: ErrorTypeBadRequest, code: InvalidArgument, description: "transactions are not the same"}];

  // Ошибки, связанные с проектами и мерчантами
  ErrProjectNotFound = 8008 [(mvp.error_describe) = {error_type: ErrorTypeNotFound, code: NotFound, description: "project doesn't exist"}];
  ErrProjectTerminalNotFound = 8018 [(mvp.error_describe) = {error_type: ErrorTypeNotFound, code: NotFound, description: "project-terminal doesn't exist"}];
  ErrProjectTerminalAlreadyExists = 8020 [(mvp.error_describe) = {error_type: ErrorTypeBadRequest, code: InvalidArgument, description: "project-terminal already exists"}];
  ErrProjectReportScheduleNotFound = 1508 [(mvp.error_describe) = {error_type: ErrorTypeNotFound, code: NotFound, description: "project report schedule doesn't exist"}];
  ErrProjectOptionNotFound = 2015 [(mvp.error_describe) = {error_type: ErrorTypeNotFound, code: NotFound, description: "project option not found"}];
  ErrProjectSupplierNotFound = 8030 [(mvp.error_describe) = {error_type: ErrorTypeNotFound, code: NotFound, description: "project-supplier doesn't exist"}];
  ErrMerchantNotFound = 8005 [(mvp.error_describe) = {error_type: ErrorTypeNotFound, code: NotFound, description: "merchant doesn't exist"}];
  ErrMerchantOwnerTypeRelationNotFound = 8206 [(mvp.error_describe) = {error_type: ErrorTypeNotFound, code: NotFound, description: "merchant owner type relation doesn't exist"}];
  ErrInvalidMerchantOwnerTypeRelation = 8207 [(mvp.error_describe) = {error_type: ErrorTypeBadRequest, code: InvalidArgument, description: "invalid merchant owner type relation"}];
  ErrMerchantInfoNotFound = 8016 [(mvp.error_describe) = {error_type: ErrorTypeNotFound, code: NotFound, description: "merchant info doesn't exist"}];
  ErrMerchantDocNotFound = 8025 [(mvp.error_describe) = {error_type: ErrorTypeNotFound, code: NotFound, description: "merchant document doesn't exist"}];
  ErrMerchantAlreadyExists = 8029 [(mvp.error_describe) = {error_type: ErrorTypeBadRequest, code: InvalidArgument, description: "merchant already exists"}];
  ErrMerchantDuplicatedKey = 8304 [(mvp.error_describe) = {error_type: ErrorTypeUnexpectedError, code: Unknown, description: "merchant must be unique"}];
  ErrBrokerageNotFound = 2003 [(mvp.error_describe) = {error_type: ErrorTypeNotFound, code: NotFound, description: "brokerage doesn't exist"}];
  ErrBrokerageProjectNotFound = 2006 [(mvp.error_describe) = {error_type: ErrorTypeNotFound, code: NotFound, description: "brokerage project doesn't exist"}];
  ErrChannelNotFound = 8001 [(mvp.error_describe) = {error_type: ErrorTypeNotFound, code: NotFound, description: "channel doesn't exist"}];
  ErrStatusNotFound = 8003 [(mvp.error_describe) = {error_type: ErrorTypeNotFound, code: NotFound, description: "status doesn't exist"}];
  ErrFilenameNotFound = 8009 [(mvp.error_describe) = {error_type: ErrorTypeNotFound, code: NotFound, description: "filename doesn't exist"}];
  ErrMsgIsEmpty = 8010 [(mvp.error_describe) = {error_type: ErrorTypeNotFound, code: NotFound, description: "message can't be empty"}];
  ErrTerminalNotFound = 8011 [(mvp.error_describe) = {error_type: ErrorTypeNotFound, code: NotFound, description: "terminal doesn't exist"}];
  ErrTerminalHasAnAccount = 8012 [(mvp.error_describe) = {error_type: ErrorTypeNotFound, code: NotFound, description: "this terminal already has such an account"}];
  ErrSupplierNotFound = 8013 [(mvp.error_describe) = {error_type: ErrorTypeNotFound, code: NotFound, description: "supplier doesn't exist"}];
  ErrAccountNotFound = 8015 [(mvp.error_describe) = {error_type: ErrorTypeNotFound, code: NotFound, description: "account doesn't exist"}];
  ErrBeneficiaryNotFound = 8017 [(mvp.error_describe) = {error_type: ErrorTypeNotFound, code: NotFound, description: "beneficiary doesn't exist"}];
  ErrMccNotFound = 8019 [(mvp.error_describe) = {error_type: ErrorTypeNotFound, code: NotFound, description: "mcc doesn't exist"}];
  ErrAffiliatedNotFound = 8021 [(mvp.error_describe) = {error_type: ErrorTypeNotFound, code: NotFound, description: "affiliated doesn't exist"}];
  ErrCommentNotFound = 8022 [(mvp.error_describe) = {error_type: ErrorTypeNotFound, code: NotFound, description: "comment doesn't exist"}];
  ErrTestCardNotFound = 8023 [(mvp.error_describe) = {error_type: ErrorTypeNotFound, code: NotFound, description: "test card doesn't exist"}];
  ErrOnboardingNotFound = 8024 [(mvp.error_describe) = {error_type: ErrorTypeNotFound, code: NotFound, description: "onboarding doesn't exist"}];
  ErrPublicOfficialNotFound = 8027 [(mvp.error_describe) = {error_type: ErrorTypeNotFound, code: NotFound, description: "public-official doesn't exist"}];
  ErrRequestNotFound = 8028 [(mvp.error_describe) = {error_type: ErrorTypeNotFound, code: NotFound, description: "request doesn't exist"}];
  ErrStakeholderNotFound = 8031 [(mvp.error_describe) = {error_type: ErrorTypeNotFound, code: NotFound, description: "stakeholder doesn't exist"}];
  ErrProjectStoreNotFound = 8032 [(mvp.error_describe) = {error_type: ErrorTypeNotFound, code: NotFound, description: "project-store doesn't exist"}];
  ErrTransferSplitTaxSettingsNotFound = 9105 [(mvp.error_describe) = {error_type: ErrorTypeNotFound, code: NotFound, description: "transfer split tax settings not found"}];
  ErrSystemTaxSettingsNotFound = 9106 [(mvp.error_describe) = {error_type: ErrorTypeNotFound, code: NotFound, description: "system tax settings not found"}];
  ErrRowOperationsNotFound = 9107 [(mvp.error_describe) = {error_type: ErrorTypeNotFound, code: NotFound, description:"row operations not found"}];
  ErrPaymentSplitTaxSettingNotFound = 9108 [(mvp.error_describe) = {error_type: ErrorTypeNotFound, code: NotFound, description:"payment split tax settings not found"}];

  // Ошибки проектов
  ErrProjectColorNotFound = 2008 [(mvp.error_describe) = {error_type: ErrorTypeBadRequest, code: InvalidArgument, description: "project colors does not exist"}];
  ErrProjectPayFormInfoNotFound = 2009 [(mvp.error_describe) = {error_type: ErrorTypeBadRequest, code: InvalidArgument, description: "project pay form info does not exist"}];
  ErrProjectBannerNotFound = 2010 [(mvp.error_describe) = {error_type: ErrorTypeBadRequest, code: InvalidArgument, description: "project banner does not exist"}];
  ErrCantCreateMoreOnboardings = 7000 [(mvp.error_describe) = {error_type: ErrorTypeBadRequest, code: InvalidArgument, description: "there are already 2 onboardings created"}];
  ErrServiceNotExist = 1900 [(mvp.error_describe) = {error_type: ErrorTypeBadRequest, code: InvalidArgument, description: "service doesn't exists"}];
  ErrDataKeysNotFound = 1507 [(mvp.error_describe) = {error_type: ErrorTypeNotFound, code: NotFound, description: "data keys don't exist"}];
  ErrReportNotFound = 1509 [(mvp.error_describe) = {error_type: ErrorTypeNotFound, code: NotFound, description: "report doesn't exist"}];

  // Ошибки HTTP-запросов
  ErrHttpRequestUnexpected = 6500 [(mvp.error_describe) = {error_type: ErrorTypeUnexpectedError, code: Unknown, description: "unexpected http request error"}];
  ErrBadStatusCode = 7020 [(mvp.error_describe) = {error_type: ErrorTypeUnexpectedError, code: Unknown, description: "bad status code"}];
  ErrInvalidBankApiURL = 7019 [(mvp.error_describe) = {error_type: ErrorTypeUnexpectedError, code: Unknown, description: "could not form the valid api url"}];
  ErrCreateCheckRequestURL = 1500 [(mvp.error_describe) = {error_type: ErrorTypeBadRequest, code: InvalidArgument, description: "could not form valid account check API URL"}];
  ErrFailedAccountCheck = 1501 [(mvp.error_describe) = {error_type: ErrorTypeBadRequest, code: InvalidArgument, description: "failed to check account"}];
  ErrCreateCashInRequestURL = 1502 [(mvp.error_describe) = {error_type: ErrorTypeBadRequest, code: InvalidArgument, description: "could not form valid cash in API URL"}];
  ErrFailedCashIn = 1503 [(mvp.error_describe) = {error_type: ErrorTypeBadRequest, code: InvalidArgument, description: "failed to cash in"}];
  ErrCreateStatusRequestURL = 1504 [(mvp.error_describe) = {error_type: ErrorTypeBadRequest, code: InvalidArgument, description: "could not form valid get status API URL"}];
  ErrFailedGetStatus = 1505 [(mvp.error_describe) = {error_type: ErrorTypeBadRequest, code: InvalidArgument, description: "failed to get status"}];
  ErrEmptyErrorResponse = 1506 [(mvp.error_describe) = {error_type: ErrorTypeBadRequest, code: InvalidArgument, description: "empty error response"}];
  ErrEmailIntegrationError = 8200 [(mvp.error_describe) = {error_type: ErrorTypeIntegration, code: FailedPrecondition, description:  "email integration error"}];
  ErrSmsIntegrationError = 8201 [(mvp.error_describe) = {error_type: ErrorTypeIntegration, code: FailedPrecondition, description:  "sms integration error"}];
  ErrBankResponseCodeNotFound = 1200 [(mvp.error_describe) = {error_type: ErrorTypeNotFound, code: NotFound, description: "bank response code doesn't exist"}];
  ErrBankResponseStatusNotFound = 1201 [(mvp.error_describe) = {error_type: ErrorTypeNotFound, code: NotFound, description: "bank response status doesn't exists"}];
  ErrIntegrationErrorNotFound = 1203 [(mvp.error_describe) = {error_type: ErrorTypeNotFound, code: NotFound, description: "integration error not found"}];

  // Ошибки обратных вызовов
  ErrCallbackStatusNotFound = 2007 [(mvp.error_describe) = {error_type: ErrorTypeNotFound, code: NotFound, description: "callback status doesn't exist"}];
  ErrFailedToSendCallback = 9715 [(mvp.error_describe) = {error_type: ErrorTypeBadRequest, code: InvalidArgument, description: "failed to send initial callback"}];
  ErrCanNotSaveFile = 8014 [(mvp.error_describe) = {error_type: ErrorTypeUnexpectedError, code: Unknown, description: "file can't be saved"}];
  ErrInfrastructureNotFound = 7103 [(mvp.error_describe) = {error_type: ErrorTypeNotFound, code: NotFound, description: "infrastructures don't exist"}];
  ErrBankResponseMessage = 9706 [(mvp.error_describe) = {error_type: ErrorTypeBadRequest, code: InvalidArgument, description: "failed to insert bank response message"}];
  ErrGetBankStatus = 9707 [(mvp.error_describe) = {error_type: ErrorTypeBadRequest, code: InvalidArgument, description: "failed to get bank status"}];
  ErrFileNotFound = 8102 [(mvp.error_describe) = {error_type: ErrorTypeNotFound, code: NotFound, description: "file not found"}];

  // Ошибки параметров запроса
  ErrInvalidQueryParam = 1017 [(mvp.error_describe) = {error_type: ErrorTypeBadRequest, code: InvalidArgument, description: "invalid query param"}];
  ErrInvalidPathParam = 1018 [(mvp.error_describe) = {error_type: ErrorTypeBadRequest, code: InvalidArgument, description: "invalid path param"}];
  ErrParseErrorBody = 1019 [(mvp.error_describe) = {error_type: ErrorTypeBadRequest, code: InvalidArgument, description: "can't parse request body"}];
  ErrLimitReached = 1020 [(mvp.error_describe) = {error_type: ErrorTypeBadRequest, code: InvalidArgument, description: "limit exceeded"}];
  ErrRequestValidation = 1021 [(mvp.error_describe) = {error_type: ErrorTypeBadRequest, code: InvalidArgument, description: "request validation error"}];
  ErrParseResponseBody = 1022 [(mvp.error_describe) = {error_type: ErrorTypeUnexpectedError, code: Unknown, description: "couldn't parse response body"}];
  ErrEndDateRequired = 1003 [(mvp.error_describe) = {error_type: ErrorTypeBadRequest, code: InvalidArgument, description: "end_date is required"}];
  ErrStartDateRequired = 1002 [(mvp.error_describe) = {error_type: ErrorTypeBadRequest, code: InvalidArgument, description: "start_date is required"}];
  ErrParametersRequired = 1001 [(mvp.error_describe) = {error_type: ErrorTypeBadRequest, code: InvalidArgument, description: "parameters are required"}];
  ErrExceededLimitNumber = 1004 [(mvp.error_describe) = {error_type: ErrorTypeBadRequest, code: InvalidArgument, description: "limit can't be more than 20"}];
  ErrTimeCompareValidation = 1007 [(mvp.error_describe) = {error_type: ErrorTypeBadRequest, code: InvalidArgument, description: "start date must comes before the end date"}];
  ErrTimeRangeValidation = 1006 [(mvp.error_describe) = {error_type: ErrorTypeBadRequest, code: InvalidArgument, description: "time range can not be more than 2 hours"}];
  ErrInvalidRequestParam = 1409 [(mvp.error_describe) = {error_type: ErrorTypeBadRequest, code: InvalidArgument, description: "invalid request parameters"}];
  ErrSourceCodeEnum = 8050 [(mvp.error_describe) = {error_type: ErrorTypeBadRequest, code: InvalidArgument, description: "source code unexpected value"}];
  ErrPayIDRequired = 8051 [(mvp.error_describe) = {error_type: ErrorTypeBadRequest, code: InvalidArgument, description: "pay_id is required"}];
  ErrPaymentsRequired = 8052 [(mvp.error_describe) = {error_type: ErrorTypeBadRequest, code: InvalidArgument, description: "payments is required"}];
  ErrCodeRequired = 8053 [(mvp.error_describe) = {error_type: ErrorTypeBadRequest, code: InvalidArgument, description: "code is required"}];
  ErrQuantityIsNegative = 8054 [(mvp.error_describe) = {error_type: ErrorTypeBadRequest, code: InvalidArgument, description: "quantity can't be lower than 1"}];
  ErrEmailRequired = 8055 [(mvp.error_describe) = {error_type: ErrorTypeBadRequest, code: InvalidArgument, description: "email is required"}];
  ErrPasswordRequired = 8056 [(mvp.error_describe) = {error_type: ErrorTypeBadRequest, code: InvalidArgument, description: "pass is required"}];
  ErrCreatingMerchant = 8057 [(mvp.error_describe) = {error_type: ErrorTypeBadRequest, code: InvalidArgument, description: "create merchant failed"}];
  ErrPasswordsDontMatch = 8059 [(mvp.error_describe) = {error_type: ErrorTypeBadRequest, code: InvalidArgument, description: "password is not matching"}];
  ErrEndBeforeStartDate = 8060 [(mvp.error_describe) = {error_type: ErrorTypeBadRequest, code: InvalidArgument, description: "end date is before start date"}];
  ErrNameRequired = 8061 [(mvp.error_describe) = {error_type: ErrorTypeBadRequest, code: InvalidArgument, description: "name is required"}];
  ErrBinRequired = 8062 [(mvp.error_describe) = {error_type: ErrorTypeBadRequest, code: InvalidArgument, description: "bin is required"}];
  ErrIsActiveRequired = 8063 [(mvp.error_describe) = {error_type: ErrorTypeBadRequest, code: InvalidArgument, description: "is_active is required"}];
  ErrBankRequired = 8064 [(mvp.error_describe) = {error_type: ErrorTypeBadRequest, code: InvalidArgument, description: "bank is required"}];
  ErrMerchantRequired = 8065 [(mvp.error_describe) = {error_type: ErrorTypeBadRequest, code: InvalidArgument, description: "merchant is required"}];
  ErrAccountNumberRequired = 8066 [(mvp.error_describe) = {error_type: ErrorTypeBadRequest, code: InvalidArgument, description: "account number is required"}];
  ErrIicRequired = 8068 [(mvp.error_describe) = {error_type: ErrorTypeBadRequest, code: InvalidArgument, description: "iic is required"}];

  // Ошибки, связанные с правилами
  ErrBaseRuleNotFound = 3003 [(mvp.error_describe) = {error_type: ErrorTypeNotFound, code: NotFound, description: "base rule not found"}];
  ErrBaseRuleAlreadyExists = 3004 [(mvp.error_describe) = {error_type: ErrorTypeBadRequest, code: InvalidArgument, description: "base rule already exists"}];
  ErrBaseRuleDeactivation = 3005 [(mvp.error_describe) = {error_type: ErrorTypeBadRequest, code: InvalidArgument, description: "base rule can not be deactivated"}];
  ErrRuleNotFound = 3006 [(mvp.error_describe) = {error_type: ErrorTypeNotFound, code: NotFound, description: "rule not found"}];
  ErrRuleAlreadyExists = 3007 [(mvp.error_describe) = {error_type: ErrorTypeBadRequest, code: InvalidArgument, description: "rule already exists"}];
  ErrRuleWhileSearch = 3008 [(mvp.error_describe) = {error_type: ErrorTypeUnexpectedError, code: Unknown, description: "something went wrong while searching the rule"}];
  ErrNoPreviousRule = 3013 [(mvp.error_describe) = {error_type: ErrorTypeNotFound, code: NotFound, description: "no previous rule"}];
  ErrNoNextRule = 3014 [(mvp.error_describe) = {error_type: ErrorTypeNotFound, code: NotFound, description: "no next rule"}];
  ErrBaseRuleActivation = 3015 [(mvp.error_describe) = {error_type: ErrorTypeBadRequest, code: InvalidArgument, description: "base rule can not be activated"}];
  ErrGetTerminal = 9710 [(mvp.error_describe) = {error_type: ErrorTypeBadRequest, code: InvalidArgument, description: "failed to get terminal"}];
  ErrCheckTransactionTimeout = 9704 [(mvp.error_describe) = {error_type: ErrorTypeBadRequest, code: InvalidArgument, description: "failed to check transaction timeout"}];
  ErrProcessedByOtherGoRoutine = 9713 [(mvp.error_describe) = {error_type: ErrorTypeBadRequest, code: InvalidArgument, description: "transaction processed by other go routine"}];
  ErrTooEarlyToFinalize = 9712 [(mvp.error_describe) = {error_type: ErrorTypeBadRequest, code: InvalidArgument, description: "too early to finalize transaction"}];

  ErrAutoRedirectInfoNotFound = 8104 [(mvp.error_describe) = {error_type: ErrorTypeNotFound, code: NotFound, description: "auto redirect info not found"}];
  ErrTerminalRefundSettingNotFound = 5110 [(mvp.error_describe) = {error_type: ErrorTypeNotFound, code: NotFound, description: "terminal refund setting not found"}];
  ErrRefundReasonNotFound = 5111 [(mvp.error_describe) = {error_type: ErrorTypeNotFound, code: NotFound, description: "refund reason not found"}];
  ErrOperationTypeNotFound = 5112 [(mvp.error_describe) = {error_type: ErrorTypeNotFound, code: NotFound, description: "operation type not found"}];
  ErrOperationNotFound = 5113 [(mvp.error_describe) = {error_type: ErrorTypeNotFound, code: NotFound, description: "operation not found"}];
  ErrProjectOrderNotFound = 5114 [(mvp.error_describe) = {error_type: ErrorTypeNotFound, code: NotFound, description: "project order not found"}];
  ErrTransactionCallbackStatusNotFound = 5115 [(mvp.error_describe) = {error_type: ErrorTypeNotFound, code: NotFound, description: "transaction callback status not found"}];
  ErrUnsuppportedTerminalType = 5116 [(mvp.error_describe) = {error_type: ErrorTypeBadRequest, code: InvalidArgument, description: "unsupported terminal type"}];

  // Ошибки кэширования транзакций
  ErrSetNewTransactionToCache = 9702 [(mvp.error_describe) = {error_type: ErrorTypeUnexpectedError, code: Unknown, description: "failed to set new transaction to cache"}];
  ErrUpdateTtlToCache = 9703 [(mvp.error_describe) = {error_type: ErrorTypeUnexpectedError, code: Unknown, description: "failed to update ttl in cache"}];
  ErrGetTransactionFromCache = 9714 [(mvp.error_describe) = {error_type: ErrorTypeUnexpectedError, code: Unknown, description: "failed to get item from cache"}];
  ErrStatusIsNotInProgress = 1043 [(mvp.error_describe) = {error_type: ErrorTypeBadRequest, code: InvalidArgument, description: "transaction status is not `in progress`"}];
  ErrCommissionNotFound = 1044 [(mvp.error_describe) = {error_type: ErrorTypeNotFound, code: NotFound, description: "commission not found"}];

  // Ошибки работы с токенами
  ErrTokenIsExpided = 9200 [(mvp.error_describe) = {error_type: ErrorTypeUnexpectedError, code: Unknown, description: "token is expired"}];
  ErrNoToken = 9201 [(mvp.error_describe) = {error_type: ErrorTypeBadRequest, code: InvalidArgument, description: "empty token"}];
  ErrCantGetClaimsFromToken = 9202 [(mvp.error_describe) = {error_type: ErrorTypeUnexpectedError, code: Unknown, description: "error get user claims from token"}];
  ErrFailedToParseRefresh = 9203 [(mvp.error_describe) = {error_type: ErrorTypeUnexpectedError, code: Unknown, description: "failed to parse refresh"}];
  ErrFailedToParseAccess = 9204 [(mvp.error_describe) = {error_type: ErrorTypeUnexpectedError, code: Unknown, description: "failed to parse access"}];
  ErrFailedToBuildToken = 9205 [(mvp.error_describe) = {error_type: ErrorTypeUnexpectedError, code: Unknown, description: "failed to build token"}];
  ErrFailedToSignToken = 9206 [(mvp.error_describe) = {error_type: ErrorTypeUnexpectedError, code: Unknown, description: "failed to sign token"}];
  ErrFailedToOpenToken = 9207 [(mvp.error_describe) = {error_type: ErrorTypeUnexpectedError, code: Unknown, description: "failed to open token"}];
  ErrFailedToGetPublicKey = 9208 [(mvp.error_describe) = {error_type: ErrorTypeUnexpectedError, code: Unknown, description: "failed to get public key"}];
  ErrFailedToVerifyJWS = 9209 [(mvp.error_describe) = {error_type: ErrorTypeUnexpectedError, code: Unknown, description: "failed to verify JWS"}];
  ErrFailedToParseJWK = 9210 [(mvp.error_describe) = {error_type: ErrorTypeUnexpectedError, code: Unknown, description: "failed to parse JWK"}];
  ErrTokenNotFound = 4003 [(mvp.error_describe) = {error_type: ErrorTypeNotFound, code: NotFound, description: "token doesn't exist"}];
  ErrTokenTransactionNotFound = 4004 [(mvp.error_describe) = {error_type: ErrorTypeNotFound, code: NotFound, description: "token transaction doesn't exist"}];
  ErrNoCredentialToUpdate = 7102 [(mvp.error_describe) = {error_type: ErrorTypeNotFound, code: NotFound, description: "could not find the corresponding documents to update"}];

  // Ошибки транзакций
  ErrTransactionAlreadyExists = 5000 [(mvp.error_describe) = {error_type: ErrorTypeBadRequest, code: InvalidArgument, description: "transaction already exists"}];
  ErrTransactionTryLimitIsOver = 5001 [(mvp.error_describe) = {error_type: ErrorTypeBadRequest, code: InvalidArgument, description: "transaction try limit is over"}];
  ErrTimeoutError = 5002 [(mvp.error_describe) = {error_type: ErrorTypeBadRequest, code: InvalidArgument, description: "timeout error"}];
  ErrTransactionCreation = 5003 [(mvp.error_describe) = {error_type: ErrorTypeUnexpectedError, code: Unknown, description: "an error occurred while creating transaction"}];
  ErrPanValidation = 5004 [(mvp.error_describe) = {error_type: ErrorTypeBadRequest, code: InvalidArgument, description: "the pan is incorrect"}];
  ErrCardExpiration = 5005 [(mvp.error_describe) = {error_type: ErrorTypeBadRequest, code: InvalidArgument, description: "the card is expired"}];
  ErrTransactionAmountLimitIsOver = 5006 [(mvp.error_describe) = {error_type: ErrorTypeBadRequest, code: InvalidArgument, description: "transaction amount limit is over"}];
  ErrAmountValidation = 5007 [(mvp.error_describe) = {error_type: ErrorTypeBadRequest, code: InvalidArgument, description: "inoperable amount for this operation type"}];
  ErrOperationTypeValidation = 5008 [(mvp.error_describe) = {error_type: ErrorTypeBadRequest, code: InvalidArgument, description: "inoperable operation type"}];
  ErrTransactionCallbackEmpty = 5009 [(mvp.error_describe) = {error_type: ErrorTypeBadRequest, code: InvalidArgument, description: "received an empty transaction callback request"}];
  ErrTransactionStatusNotFound = 5101 [(mvp.error_describe) = {error_type: ErrorTypeNotFound, code: NotFound, description: "undefined transaction status"}];
  ErrTransactionTypeNotFound = 5102 [(mvp.error_describe) = {error_type: ErrorTypeNotFound, code: NotFound, description: "undefined transaction type"}];
  ErrTransactionNotFound = 5103 [(mvp.error_describe) = {error_type: ErrorTypeNotFound, code: NotFound, description: "transaction not found"}];
  ErrTransactionInfoNotFound = 5104 [(mvp.error_describe) = {error_type: ErrorTypeNotFound, code: NotFound, description: "transaction has no information"}];
  ErrMerchantOrderStatusNotFound = 5105 [(mvp.error_describe) = {error_type: ErrorTypeNotFound, code: NotFound, description: "merchant transaction status not found"}];
  ErrMerchantOrderTypeNotFound = 5106 [(mvp.error_describe) = {error_type: ErrorTypeNotFound, code: NotFound, description: "merchant transaction type not found"}];
  ErrTransactionLimitNotFound = 5107 [(mvp.error_describe) = {error_type: ErrorTypeNotFound, code: NotFound, description: "transaction limit doesn't exist"}];
  ErrFailCallbackSending = 5201 [(mvp.error_describe) = {error_type: ErrorTypeUnexpectedError, code: Unknown, description: "something went wrong during callback sending"}];
  ErrRequestFailed = 5202 [(mvp.error_describe) = {error_type: ErrorTypeUnexpectedError, code: Unknown, description: "request failed"}];
  ErrTransactionAlreadyFinished = 5413 [(mvp.error_describe) = {error_type: ErrorTypeBadRequest, code: InvalidArgument, description: "transaction already finished"}];
  ErrStatusCannotBeChanged = 5203 [(mvp.error_describe) = {error_type: ErrorTypeUnexpectedError, code: Unknown, description: "status cannot be changed"}];
  ErrCallBackUrlIsEmpty = 5204 [(mvp.error_describe) = {error_type: ErrorTypeUnexpectedError, code: Unknown, description: "callback url is empty"}];
  ErrProjectTransactionConfirmation = 5205 [(mvp.error_describe) = {error_type: ErrorTypeUnexpectedError, code: Unknown, description: "unavailable project server"}];
  ErrUnPayableOrderStatus = 5300 [(mvp.error_describe) = {error_type: ErrorTypeBadRequest, code: InvalidArgument, description: "transaction has finalized status on the merchant side"}];
  ErrReceivingProjectSecretKey = 5400 [(mvp.error_describe) = {error_type: ErrorTypeBadRequest, code: InvalidArgument, description: "couldn't receive project data"}];
  ErrInvalidHash = 5401 [(mvp.error_describe) = {error_type: ErrorTypeBadRequest, code: InvalidArgument, description: "invalid hash"}];
  ErrCreatingClient = 5402 [(mvp.error_describe) = {error_type: ErrorTypeBadRequest, code: InvalidArgument, description: "create client failed"}];
  ErrSearchingTerminal = 5403 [(mvp.error_describe) = {error_type: ErrorTypeBadRequest, code: InvalidArgument, description: "search terminal failed"}];
  ErrMakingPayIn = 5404 [(mvp.error_describe) = {error_type: ErrorTypeUnexpectedError, code: Unknown, description: "an error occurred while making pay in"}];
  ErrConfirmingThreeDS = 5405 [(mvp.error_describe) = {error_type: ErrorTypeUnexpectedError, code: Unknown, description: "an error occurred while confirming three ds"}];
  ErrInvalidProjectSecret = 5406 [(mvp.error_describe) = {error_type: ErrorTypeBadRequest, code: InvalidArgument, description: "invalid project secret"}];
  ErrMakingOneClickPayIn = 5407 [(mvp.error_describe) = {error_type: ErrorTypeUnexpectedError, code: Unknown, description: "an error occurred while making one click pay in"}];
  ErrMakingPayOut = 5408 [(mvp.error_describe) = {error_type: ErrorTypeUnexpectedError, code: Unknown, description: "an error occurred while making pay out"}];
  ErrOrderNotFound = 1041 [(mvp.error_describe) = {error_type: ErrorTypeBadRequest, code: InvalidArgument, description: "Order not found"}];
  ErrUpdateTransactionStatus = 9708 [(mvp.error_describe) = {error_type: ErrorTypeBadRequest, code: InvalidArgument, description: "failed to update transaction status"}];
  ErrFutureTransaction = 9701 [(mvp.error_describe) = {error_type: ErrorTypeDbViolation, code: DataLoss, description:  "transaction created time can not be greater than current time"}];
  ErrProviderDriverNotFound = 9705 [(mvp.error_describe) = {error_type: ErrorTypeBadRequest, code: InvalidArgument, description: "provider driver not found"}];
  ErrEmptyDateFrom = 1045 [(mvp.error_describe) = {error_type: ErrorTypeBadRequest, code: InvalidArgument, description: "received an empty dateFrom parameter"}];
  ErrEmptyDateTo = 1046 [(mvp.error_describe) = {error_type: ErrorTypeBadRequest, code: InvalidArgument, description: "received an empty dateTo parameter"}];
  ErrBillingTransactionTypeNotFound = 1047 [(mvp.error_describe) = {error_type: ErrorTypeUnexpectedError, code: DataLoss, description: "billing transaction type not found"}];

  // Ошибки запросов
  ErrEmptyPayInRequest = 6000 [(mvp.error_describe) = {error_type: ErrorTypeBadRequest, code: InvalidArgument, description: "received empty payin request"}];
  ErrEmptyPayOutRequest = 6001 [(mvp.error_describe) = {error_type: ErrorTypeBadRequest, code: InvalidArgument, description: "received empty payout request"}];
  ErrEmptyCardRequest = 6002 [(mvp.error_describe) = {error_type: ErrorTypeBadRequest, code: InvalidArgument, description: "received empty card body"}];
  ErrEmptyUserRequest = 6003 [(mvp.error_describe) = {error_type: ErrorTypeBadRequest, code: InvalidArgument, description: "received empty user body"}];
  ErrEmptyAcquirerRequest = 6004 [(mvp.error_describe) = {error_type: ErrorTypeBadRequest, code: InvalidArgument, description: "received empty acquirer body"}];
  ErrEmptyTerminalRequest = 6005 [(mvp.error_describe) = {error_type: ErrorTypeBadRequest, code: InvalidArgument, description: "received empty terminal body"}];
  ErrEmptyPayInResponse = 6006 [(mvp.error_describe) = {error_type: ErrorTypeBadRequest, code: InvalidArgument, description: "received empty payin response"}];
  ErrEmptyPayOutResponse = 6007 [(mvp.error_describe) = {error_type: ErrorTypeBadRequest, code: InvalidArgument, description: "received empty payout response"}];
  ErrEmptyChargeRequest = 6008 [(mvp.error_describe) = {error_type: ErrorTypeBadRequest, code: InvalidArgument, description: "received empty charge request"}];
  ErrEmptyCancelRequest = 6009 [(mvp.error_describe) = {error_type: ErrorTypeBadRequest, code: InvalidArgument, description: "received empty cancel request"}];
  ErrEmptyRefundRequest = 6200 [(mvp.error_describe) = {error_type: ErrorTypeBadRequest, code: InvalidArgument, description: "received empty refund request"}];
  ErrEmptyRefundResponse = 6201 [(mvp.error_describe) = {error_type: ErrorTypeBadRequest, code: InvalidArgument, description: "received empty refund response"}];
  ErrEmptyAmountRequest = 6202 [(mvp.error_describe) = {error_type: ErrorTypeBadRequest, code: InvalidArgument, description: "received empty amount request"}];
  ErrEmptyTransactionIDRequest = 6203 [(mvp.error_describe) = {error_type: ErrorTypeBadRequest, code: InvalidArgument, description: "received empty transaction_id request"}];
  ErrThreeDSParametersNotFound = 6100 [(mvp.error_describe) = {error_type: ErrorTypeNotFound, code: NotFound, description: "threeDS parameters doesn't exist"}];
  ErrZoneNumOrCoordinatesRequired = 6101 [(mvp.error_describe) = {error_type: ErrorTypeBadRequest, code: InvalidArgument, description: "zone number or coordinates are required"}];
  ErrCommissionIsRequired = 6102 [(mvp.error_describe) = {error_type: ErrorTypeBadRequest, code: InvalidArgument, description: "commission is required"}];
  ErrEmptyDate = 6103 [(mvp.error_describe) = {error_type: ErrorTypeBadRequest, code: InvalidArgument, description: "empty date"}];
  ErrEmptyCustomerInfo = 6104 [(mvp.error_describe) = {error_type: ErrorTypeBadRequest, code: InvalidArgument, description: "empty customer info"}];
  ErrFiscalizationDataNotFound = 6105 [(mvp.error_describe) = {error_type: ErrorTypeNotFound, code: NotFound, description: "fiscalization data not found"}];

  // Ошибки валидации
  ErrIssuerValidation = 3009 [(mvp.error_describe) = {error_type: ErrorTypeBadRequest, code: InvalidArgument, description: "incompatible values of is_acquirer and issuer_id"}];
  ErrStatusValidation = 3010 [(mvp.error_describe) = {error_type: ErrorTypeBadRequest, code: InvalidArgument, description: "inoperable transaction status for this method"}];
  ErrDateValidation = 3011 [(mvp.error_describe) = {error_type: ErrorTypeBadRequest, code: InvalidArgument, description: "incorrect format of date"}];
  ErrCvcValidation = 3012 [(mvp.error_describe) = {error_type: ErrorTypeBadRequest, code: InvalidArgument, description: "incorrect format of cvc"}];
  ErrReportScheduleUnauthorized = 1304 [(mvp.error_describe) = {error_type: ErrorTypeBadRequest, code: InvalidArgument, description: "project report schedule unauthorized for your merchant"}];
  ErrInvalidThreeDSParams = 1202 [(mvp.error_describe) = {error_type: ErrorTypeBadRequest, code: InvalidArgument, description: "invalid secure params"}];
  ErrExceedsSizeFile = 1204 [(mvp.error_describe) = {error_type: ErrorTypeBadRequest, code: InvalidArgument, description: "file exceeds allowed size"}];
  ErrEmptyValidationRequest = 1205 [(mvp.error_describe) = {error_type: ErrorTypeBadRequest, code: InvalidArgument, description: "empty request"}];
  ErrEmptyUserLogin = 1206 [(mvp.error_describe) = {error_type: ErrorTypeBadRequest, code: InvalidArgument, description: "empty user login"}];
  ErrEmptyDescription = 1423 [(mvp.error_describe) = {error_type: ErrorTypeBadRequest, code: InvalidArgument, description: "empty description"}];
  ErrWrongEndDateFormat = 2013 [(mvp.error_describe) = {error_type: ErrorTypeBadRequest, code: InvalidArgument, description: "end date must be later than start date"}];
  ErrEmptyUINValue = 2202 [(mvp.error_describe) = {error_type: ErrorTypeBadRequest, code: InvalidArgument, description: "empty uin value"}];
  ErrInvalidUINLength = 2203 [(mvp.error_describe) = {error_type: ErrorTypeBadRequest, code: InvalidArgument, description: "invalid uin length"}];
  ErrEmptyUsername = 1401 [(mvp.error_describe) = {error_type: ErrorTypeBadRequest, code: InvalidArgument, description: "empty username"}];
  ErrEmptyShowCaseId = 1402 [(mvp.error_describe) = {error_type: ErrorTypeBadRequest, code: InvalidArgument, description: "empty show case id"}];
  ErrEmptyProjectId = 1403 [(mvp.error_describe) = {error_type: ErrorTypeBadRequest, code: InvalidArgument, description: "empty project id"}];
  ErrInvalidActionRequest = 1404 [(mvp.error_describe) = {error_type: ErrorTypeBadRequest, code: InvalidArgument, description: "invalid action request"}];
  ErrEndDateBeforeStartDate = 1419 [(mvp.error_describe) = {error_type: ErrorTypeBadRequest, code: InvalidArgument, description: "end date is before start date"}];
  ErrDateArrayLength = 1420 [(mvp.error_describe) = {error_type: ErrorTypeBadRequest, code: InvalidArgument, description: "the length of date array is not 2"}];
  ErrTimeValidation = 1421 [(mvp.error_describe) = {error_type: ErrorTypeBadRequest, code: InvalidArgument, description: "time validation error"}];
  ErrUpdateJobsMessage = 1422 [(mvp.error_describe) = {error_type: ErrorTypeBadRequest, code: InvalidArgument, description: "failed to update jobs message"}];
  ErrItemNotFound = 1407 [(mvp.error_describe) = {error_type: ErrorTypeNotFound, code: NotFound, description: "cache: item not found"}];
  ErrInvalidTerminalData = 7015 [(mvp.error_describe) = {error_type: ErrorTypeBadRequest, code: InvalidArgument, description: "received invalid terminal data"}];
  ErrDateArrayLengthMismatch = 1408 [(mvp.error_describe) = {error_type: ErrorTypeUnexpectedError, code: Unknown, description: "date array length is not equal to 2"}];
  ErrExternalIdDuplicate = 1042 [(mvp.error_describe) = {error_type: ErrorTypeUnexpectedError, code: Unknown, description: "duplicate external id"}];
  ErrProcessedTransferNotFound = 1147 [(mvp.error_describe) = {error_type: ErrorTypeNotFound, code: NotFound, description:"processed transfer doesn't exists"}];
  ErrTransferAlreadySplitted = 1145 [(mvp.error_describe) = {error_type: ErrorTypeBadRequest, code: InvalidArgument, description:"transfer is already splitted"}];
  ErrInoperableProcessedTransferStatus = 1146 [(mvp.error_describe) = {error_type: ErrorTypeBadRequest, code: InvalidArgument, description:"inoperable processed transfer status"}];
  ErrInvalidBalanceOwnerID = 1148[(mvp.error_describe) =  {error_type: ErrorTypeBadRequest, code: InvalidArgument, description:"invalid balance owner id"}];
  ErrInvalidFrequency = 1149[(mvp.error_describe) =  {error_type: ErrorTypeBadRequest, code: InvalidArgument, description:"invalid frequency"}];
  ErrInvalidAccountNumber = 1150[(mvp.error_describe) =  {error_type: ErrorTypeBadRequest, code: InvalidArgument, description:"invalid account number"}];
  ErrInvalidBeneficiaryCode = 1151[(mvp.error_describe) =  {error_type: ErrorTypeBadRequest, code: InvalidArgument, description:"invalid beneficiary code"}];
  ErrInvalidPaymentPurposeCode = 1152[(mvp.error_describe) =  {error_type: ErrorTypeBadRequest, code: InvalidArgument, description:"invalid payment purpose code"}];
  ErrInvalidRecipientAccount = 1153[(mvp.error_describe) =  {error_type: ErrorTypeBadRequest, code: InvalidArgument, description:"invalid recipient account"}];
  ErrInvalidAmount = 1154 [(mvp.error_describe) = {error_type: ErrorTypeBadRequest, code: InvalidArgument, description:"invalid amount"}];
  
  // Ошибки базы данных
  ErrBrokenTerminal = 9711 [(mvp.error_describe) = {error_type: ErrorTypeDbViolation, code: DataLoss, description:  "terminal missing required fields"}];
  ErrProviderServiceRelationNotFound = 9717 [(mvp.error_describe) = {error_type: ErrorTypeDbViolation, code: DataLoss, description:  "provider service relation not found"}];
  ErrProviderNotFound = 9718 [(mvp.error_describe) = {error_type: ErrorTypeDbViolation, code: DataLoss, description:  "provider not found"}];
  ErrProviderServiceNotFound = 9719 [(mvp.error_describe) = {error_type: ErrorTypeDbViolation, code: DataLoss, description:  "provider service not found"}];
  ErrProviderServiceStatusNotFound = 9720 [(mvp.error_describe) = {error_type: ErrorTypeDbViolation, code: DataLoss, description:  "provider service status not found"}];
  ErrShowcaseServiceNotFound = 9721 [(mvp.error_describe) = {error_type: ErrorTypeDbViolation, code: DataLoss, description:  "showcase service not found"}];
  ErrProjectStatusNotFound = 9722 [(mvp.error_describe) = {error_type: ErrorTypeDbViolation, code: DataLoss, description:  "project status not found"}];
  ErrShowcaseStatusNotFound = 9723 [(mvp.error_describe) = {error_type: ErrorTypeDbViolation, code: DataLoss, description:  "showcase status not found"}];
  ErrShowcaseNotFound = 9724 [(mvp.error_describe) = {error_type: ErrorTypeDbViolation, code: DataLoss, description:  "showcase not found"}];
  ErrShowcaseServiceStatusNotFound = 9725 [(mvp.error_describe) = {error_type: ErrorTypeDbViolation, code: DataLoss, description:  "showcase service status not found"}];
  ErrProjectLimitNotFound = 9726 [(mvp.error_describe) = {error_type: ErrorTypeDbViolation, code: DataLoss, description:  "project limit not found"}];
  ErrUserNotFoundAccessList = 9727 [(mvp.error_describe) = {error_type: ErrorTypeDbViolation, code: DataLoss, description:  "user not found in access list"}];

  // Ошибки валидации
  ErrValidation = 1010 [(mvp.error_describe) = {error_type: ErrorTypeBadRequest, code: InvalidArgument, description: "Request validation error"}];
  ErrUserNameFieldType = 1011 [(mvp.error_describe) = {error_type: ErrorTypeBadRequest, code: InvalidArgument, description: "Invalid username field type"}];
  ErrAgentField = 1012 [(mvp.error_describe) = {error_type: ErrorTypeBadRequest, code: InvalidArgument, description: "Invalid agent field type"}];
  ErrProjectField = 1013 [(mvp.error_describe) = {error_type: ErrorTypeBadRequest, code: InvalidArgument, description: "Invalid project field type"}];
  ErrInvalidSignature = 1014 [(mvp.error_describe) = {error_type: ErrorTypeBadRequest, code: InvalidArgument, description: "Invalid signature"}];
  ErrRootKeys = 5600 [(mvp.error_describe) = {error_type: ErrorTypeBadRequest, code: InvalidArgument, description: "an error is occured while filtering root keys"}];
  ErrParseJson = 5601 [(mvp.error_describe) = {error_type: ErrorTypeBadRequest, code: InvalidArgument, description: "parse json"}];
  ErrValidateTime = 5602 [(mvp.error_describe) = {error_type: ErrorTypeBadRequest, code: InvalidArgument, description: "failed checking expiration date"}];
  ErrPrivateKey = 5603 [(mvp.error_describe) = {error_type: ErrorTypeBadRequest, code: InvalidArgument, description: "failed loading private key"}];
  ErrPublicKey = 5604 [(mvp.error_describe) = {error_type: ErrorTypeBadRequest, code: InvalidArgument, description: "failed loading public key"}];
  ErrTyping = 5605 [(mvp.error_describe) = {error_type: ErrorTypeBadRequest, code: InvalidArgument, description: "error while typing"}];
  ErrVerifySignature = 5606 [(mvp.error_describe) = {error_type: ErrorTypeBadRequest, code: InvalidArgument, description: "could not verify intermediate signing key signature"}];
  ErrLengthDoesnotMatch = 5607 [(mvp.error_describe) = {error_type: ErrorTypeBadRequest, code: InvalidArgument, description: "length of key does not match"}];
  ErrInvalidAdditionalData = 5608 [(mvp.error_describe) = {error_type: ErrorTypeBadRequest, code: InvalidArgument, description: "invalid additional data"}];
  ErrInvalidServiceProviderConfig = 5609 [(mvp.error_describe) = {error_type: ErrorTypeBadRequest, code: InvalidArgument, description: "invalid service provider config"}];
  ErrInvalidProjectCode = 5610 [(mvp.error_describe) = {error_type: ErrorTypeBadRequest, code: InvalidArgument, description: "invalid project code"}];
  ErrInvalidShowcaseServiceCode = 5611 [(mvp.error_describe) = {error_type: ErrorTypeBadRequest, code: InvalidArgument, description: "invalid showcase service code"}];
  ErrInvalidShowcaseCode = 5612 [(mvp.error_describe) = {error_type: ErrorTypeBadRequest, code: InvalidArgument, description: "invalid showcase code"}];
  ErrInvalidDriverResponse = 5613 [(mvp.error_describe) = {error_type: ErrorTypeBadRequest, code: InvalidArgument, description: "invalid driver response"}];
  ErrDurationGreaterThan12 = 5614 [(mvp.error_describe) = {error_type: ErrorTypeBadRequest, code: InvalidArgument, description: "duration can't be greater than 12"}];
  ErrParseTime = 5615 [(mvp.error_describe) = {error_type: ErrorTypeBadRequest, code: InvalidArgument, description: "parse time"}];
  ErrCoordinatesNotInParkingZone = 5616 [(mvp.error_describe) = {error_type: ErrorTypeBadRequest, code: InvalidArgument, description: "coordinates are not in parking zone"}];
  ErrEmptyProviderServiceCode = 5617 [(mvp.error_describe) = {error_type: ErrorTypeBadRequest, code: InvalidArgument, description: "empty provider service code"}];
  ErrEmptyPayload = 5618 [(mvp.error_describe) = {error_type: ErrorTypeBadRequest, code: InvalidArgument, description: "empty payload"}];
  ErrUnexpectedFailedRestRequest = 5619 [(mvp.error_describe) = {error_type: ErrorTypeBadRequest, code: InvalidArgument, description: "unexpected failed rest request"}];
  ErrPaydalaResponseValidation = 5620 [(mvp.error_describe) = {error_type: ErrorTypeBadRequest, code: InvalidArgument, description: "paydala response validation"}];
  ErrEmptyBankName = 5621 [(mvp.error_describe) = {error_type: ErrorTypeBadRequest, code: InvalidArgument, description: "empty bank name"}];
  ErrInvalidReceiptFormat = 5622 [(mvp.error_describe) = {error_type: ErrorTypeBadRequest, code: InvalidArgument, description: "invalid receipt format"}];
  ErrEmptyReceipt = 5623 [(mvp.error_describe) = {error_type: ErrorTypeBadRequest, code: InvalidArgument, description: "empty receipt"}];
  ErrEmptyContractNumber = 5624 [(mvp.error_describe) = {error_type: ErrorTypeBadRequest, code: InvalidArgument, description: "empty contract number"}];
  ErrEmptyAuthCode = 5625 [(mvp.error_describe) = {error_type: ErrorTypeBadRequest, code: InvalidArgument, description: "empty auth code"}];
  ErrEmptyPhone = 5626 [(mvp.error_describe) = {error_type: ErrorTypeBadRequest, code: InvalidArgument, description: "empty phone"}];
  ErrInvalidPhoneFormat = 5627 [(mvp.error_describe) = {error_type: ErrorTypeBadRequest, code: InvalidArgument, description: "invalid phone format"}];
  ErrEmptyIIN = 5628 [(mvp.error_describe) = {error_type: ErrorTypeBadRequest, code: InvalidArgument, description: "empty iin"}];
  ErrEmptyServiceCode = 5629 [(mvp.error_describe) = {error_type: ErrorTypeBadRequest, code: InvalidArgument, description: "empty service code"}];
  ErrInvalidShowcaseServiceID = 5630 [(mvp.error_describe) = {error_type: ErrorTypeBadRequest, code: InvalidArgument, description: "invalid showcase service id"}];
  ErrEmptySignature = 5631 [(mvp.error_describe) = {error_type: ErrorTypeBadRequest, code: InvalidArgument, description: "empty signature"}];
  ErrEmptyCategory = 5632 [(mvp.error_describe) = {error_type: ErrorTypeBadRequest, code: InvalidArgument, description: "empty category"}];

  // duplicate number
  ErrOperationRequired = 11001 [(mvp.error_describe) = {error_type: ErrorTypeBadRequest, code: InvalidArgument, description: "operation is required"}];
  // duplicate number
  ErrResourceRequired = 11002 [(mvp.error_describe) = {error_type: ErrorTypeBadRequest, code: InvalidArgument, description: "resource is required"}];
  // duplicate number
  ErrInvalidTokenPayload = 11003 [(mvp.error_describe) = {error_type: ErrorTypeBadRequest, code: InvalidArgument, description: "invalid type in payload"}];
  ErrLoginDuplicate = 11004 [(mvp.error_describe) = {error_type: ErrorTypeUnexpectedError, code: Unknown, description: "login must be unique"}];
  ErrPhoneDuplicate = 1005 [(mvp.error_describe) = {error_type: ErrorTypeUnexpectedError, code: Unknown, description: "phone must be unique"}];
  // duplicate number
  ErrEmptyActionRequest = 11404 [(mvp.error_describe) = {error_type: ErrorTypeBadRequest, code: InvalidArgument, description: "invalid action request"}];
  ErrInvalidCheckAccountRequest = 1405 [(mvp.error_describe) = {error_type: ErrorTypeBadRequest, code: InvalidArgument, description: "invalid check account request"}];
  ErrInvalidGetStatusRequest = 1406 [(mvp.error_describe) = {error_type: ErrorTypeBadRequest, code: InvalidArgument, description: "invalid get status request"}];
  ErrApplicationNotFound = 2000 [(mvp.error_describe) = {error_type: ErrorTypeNotFound, code: NotFound, description: "application doesn't exist"}];
  ErrApplicationStatusNotFound = 2001 [(mvp.error_describe) = {error_type: ErrorTypeNotFound, code: NotFound, description: "application status doesn't exist"}];
  ErrApplicationTypeNotFound = 2002 [(mvp.error_describe) = {error_type: ErrorTypeNotFound, code: NotFound, description: "application type doesn't exist"}];
  ErrApplicationInfoNotFound = 2004 [(mvp.error_describe) = {error_type: ErrorTypeNotFound, code: NotFound, description: "application info doesn't exist"}];
  ErrApplicationDuplicate = 2100 [(mvp.error_describe) = {error_type: ErrorTypeUnexpectedError, code: Unknown, description: "application already exists"}];

  ErrInvalidOwnerForMerchant = 20000 [(mvp.error_describe) = {error_type: ErrorTypeBadRequest, code: InvalidArgument, description: "invalid balance owner for this merchant"}];
  ErrMerchantAccountNotFound = 20001 [(mvp.error_describe) = {error_type: ErrorTypeNotFound, code: NotFound, description: "merchant account doesn't exist"}];
  ErrAccountBalanceHistoryCreation = 20002 [(mvp.error_describe) = {error_type: ErrorTypeUnexpectedError, code: Internal, description: "account balance history creation failed"}];
  ErrProjectAccountNotFound = 20003 [(mvp.error_describe) = {error_type: ErrorTypeNotFound, code: NotFound, description: "project account doesn't exist"}];
  ErrInvalidProjectAccount = 20004 [(mvp.error_describe) = {error_type: ErrorTypeBadRequest, code: InvalidArgument, description: "invalid project for this account"}];
  ErrProcessedOrderCreation = 20005 [(mvp.error_describe) = {error_type: ErrorTypeUnexpectedError, code: Internal, description: "processed order creation failed"}];
  ErrOrderIDNotFound = 20006 [(mvp.error_describe) = {error_type: ErrorTypeNotFound, code: NotFound, description: "order id not found"}];
  ErrTransferNotFound = 20007 [(mvp.error_describe) = {error_type: ErrorTypeNotFound, code: NotFound, description: "transfer not found"}];
  ErrTransferCreation = 20008 [(mvp.error_describe) = {error_type: ErrorTypeUnexpectedError, code: Internal, description: "transfer creation failed"}];
  ErrTransferRuleCreation = 20032[(mvp.error_describe) = {error_type: ErrorTypeUnexpectedError, code: Internal, description: "transfer rule creation failed"}];
  ErrTransferHistoryCreation = 20033 [(mvp.error_describe) = {error_type: ErrorTypeUnexpectedError, code: Internal, description: "transfer history creation failed"}];
  ErrTransferStatusNotFound = 20009 [(mvp.error_describe) = {error_type:ErrorTypeNotFound, code: NotFound, description: "transfer status not found"}];
  ErrTransferTypeNotFound = 20010 [(mvp.error_describe) = {error_type:ErrorTypeNotFound, code: NotFound, description: "transfer type not found"}];
  ErrMerchantOrderNotFound = 20011 [(mvp.error_describe) = {error_type:ErrorTypeNotFound, code: NotFound, description: "merchant order not found"}];
  ErrInsufficientBalance = 20012 [(mvp.error_describe) = {error_type:ErrorTypeUnexpectedError, code: Internal, description: "insufficient balance"}];
  ErrAntifraudSessionNotFound = 20013 [(mvp.error_describe) = {error_type:ErrorTypeNotFound, code: NotFound, description: "antifraud session not found"}];
  ErrEmptyAgentCode = 20014 [(mvp.error_describe) = {error_type:ErrorTypeBadRequest, code: InvalidArgument, description: "agent code is required"}];
  ErrRequiredExternalReferenceID = 20015 [(mvp.error_describe) = {error_type:ErrorTypeBadRequest, code: InvalidArgument, description: "external reference is required"}];
  ErrRequiredTransferID = 20016 [(mvp.error_describe) = {error_type:ErrorTypeBadRequest, code: InvalidArgument, description: "transfer id is required"}];
  ErrBalanceNotFound = 20017 [(mvp.error_describe) = {error_type:ErrorTypeNotFound, code: NotFound, description: "balance doesn't exist"}];
  ErrBalanceAccountNotFound = 20018 [(mvp.error_describe) = {error_type:ErrorTypeNotFound, code: NotFound, description: "balance account doesn't exist"}];
  ErrNegativeCreditBalanceAmount = 20019 [(mvp.error_describe) = {error_type:ErrorTypeUnexpectedError, code: Internal, description: "credit balance cannot be negative"}];
  ErrCreditBalanceNotFound = 20020 [(mvp.error_describe) = {error_type:ErrorTypeNotFound, code: NotFound, description: "credit balance doesn't exist"}];
  ErrBalanceOwnerNotFound = 20021 [(mvp.error_describe) = {error_type:ErrorTypeNotFound, code: NotFound, description: "balance owner doesn't exist"}];
  ErrNegativeBalanceAmount = 20022 [(mvp.error_describe) = {error_type:ErrorTypeNotFound, code: NotFound, description: "balance cannot be negative"}];
  ErrFindAcquirerCommission = 20023 [(mvp.error_describe) = {error_type:ErrorTypeNotFound, code: NotFound, description: "could not find proper acquirer commission"}];
  ErrFindProjectLowerCommissions = 20024 [(mvp.error_describe) = {error_type:ErrorTypeNotFound, code: NotFound, description: "could not find proper project lower commission"}];
  ErrFindProjectUpperCommissions = 20025 [(mvp.error_describe) = {error_type:ErrorTypeNotFound, code: NotFound, description: "could not find proper project upper commission"}];
  ErrCommissionDateRangeOverlapping = 20026 [(mvp.error_describe) = {error_type:ErrorTypeUnexpectedError, code: Internal, description: "overlap in commission date ranges"}];
  ErrProjectOptionTypeNotFound = 20027 [(mvp.error_describe) = {error_type:ErrorTypeNotFound, code: NotFound, description: "undefined project option type"}];
  ErrDateTimeFormat = 20028 [(mvp.error_describe) = {error_type:ErrorTypeBadRequest, code: InvalidArgument, description: "wrong date format. Must be yyyy-mm-dd hh:mm:ss"}];
  ErrCommissionStartDate = 20029 [(mvp.error_describe) = {error_type:ErrorTypeBadRequest, code: InvalidArgument, description: "commission start date must be current or future"}];
  ErrCurrentCommission = 20030 [(mvp.error_describe) = {error_type:ErrorTypeBadRequest, code: InvalidArgument, description: "unable to change current commission"}];
  ErrPaymentOrderOwnerNotFound = 20031 [(mvp.error_describe) = {error_type:ErrorTypeNotFound, code: NotFound, description: "payment order owner not found"}];
}