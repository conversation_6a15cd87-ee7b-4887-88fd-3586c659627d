// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.5.1
// - protoc             v5.29.3
// source: inner/processing/grpc/compliance.proto

package grpc

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.64.0 or later.
const _ = grpc.SupportPackageIsVersion9

const (
	Compliance_UpdateSanctionFinanciersList_FullMethodName = "/processing.compliance.compliance.Compliance/UpdateSanctionFinanciersList"
	Compliance_UpdateSanctionInvolvedList_FullMethodName   = "/processing.compliance.compliance.Compliance/UpdateSanctionInvolvedList"
	Compliance_UpdateSanctionUNSCList_FullMethodName       = "/processing.compliance.compliance.Compliance/UpdateSanctionUNSCList"
	Compliance_UpdateSanctionWMDList_FullMethodName        = "/processing.compliance.compliance.Compliance/UpdateSanctionWMDList"
)

// ComplianceClient is the client API for Compliance service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type ComplianceClient interface {
	// jobs
	UpdateSanctionFinanciersList(ctx context.Context, in *emptypb.Empty, opts ...grpc.CallOption) (*emptypb.Empty, error)
	UpdateSanctionInvolvedList(ctx context.Context, in *emptypb.Empty, opts ...grpc.CallOption) (*emptypb.Empty, error)
	UpdateSanctionUNSCList(ctx context.Context, in *emptypb.Empty, opts ...grpc.CallOption) (*emptypb.Empty, error)
	UpdateSanctionWMDList(ctx context.Context, in *emptypb.Empty, opts ...grpc.CallOption) (*emptypb.Empty, error)
}

type complianceClient struct {
	cc grpc.ClientConnInterface
}

func NewComplianceClient(cc grpc.ClientConnInterface) ComplianceClient {
	return &complianceClient{cc}
}

func (c *complianceClient) UpdateSanctionFinanciersList(ctx context.Context, in *emptypb.Empty, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, Compliance_UpdateSanctionFinanciersList_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *complianceClient) UpdateSanctionInvolvedList(ctx context.Context, in *emptypb.Empty, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, Compliance_UpdateSanctionInvolvedList_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *complianceClient) UpdateSanctionUNSCList(ctx context.Context, in *emptypb.Empty, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, Compliance_UpdateSanctionUNSCList_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *complianceClient) UpdateSanctionWMDList(ctx context.Context, in *emptypb.Empty, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, Compliance_UpdateSanctionWMDList_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// ComplianceServer is the server API for Compliance service.
// All implementations must embed UnimplementedComplianceServer
// for forward compatibility.
type ComplianceServer interface {
	// jobs
	UpdateSanctionFinanciersList(context.Context, *emptypb.Empty) (*emptypb.Empty, error)
	UpdateSanctionInvolvedList(context.Context, *emptypb.Empty) (*emptypb.Empty, error)
	UpdateSanctionUNSCList(context.Context, *emptypb.Empty) (*emptypb.Empty, error)
	UpdateSanctionWMDList(context.Context, *emptypb.Empty) (*emptypb.Empty, error)
	mustEmbedUnimplementedComplianceServer()
}

// UnimplementedComplianceServer must be embedded to have
// forward compatible implementations.
//
// NOTE: this should be embedded by value instead of pointer to avoid a nil
// pointer dereference when methods are called.
type UnimplementedComplianceServer struct{}

func (UnimplementedComplianceServer) UpdateSanctionFinanciersList(context.Context, *emptypb.Empty) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateSanctionFinanciersList not implemented")
}
func (UnimplementedComplianceServer) UpdateSanctionInvolvedList(context.Context, *emptypb.Empty) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateSanctionInvolvedList not implemented")
}
func (UnimplementedComplianceServer) UpdateSanctionUNSCList(context.Context, *emptypb.Empty) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateSanctionUNSCList not implemented")
}
func (UnimplementedComplianceServer) UpdateSanctionWMDList(context.Context, *emptypb.Empty) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateSanctionWMDList not implemented")
}
func (UnimplementedComplianceServer) mustEmbedUnimplementedComplianceServer() {}
func (UnimplementedComplianceServer) testEmbeddedByValue()                    {}

// UnsafeComplianceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to ComplianceServer will
// result in compilation errors.
type UnsafeComplianceServer interface {
	mustEmbedUnimplementedComplianceServer()
}

func RegisterComplianceServer(s grpc.ServiceRegistrar, srv ComplianceServer) {
	// If the following call pancis, it indicates UnimplementedComplianceServer was
	// embedded by pointer and is nil.  This will cause panics if an
	// unimplemented method is ever invoked, so we test this at initialization
	// time to prevent it from happening at runtime later due to I/O.
	if t, ok := srv.(interface{ testEmbeddedByValue() }); ok {
		t.testEmbeddedByValue()
	}
	s.RegisterService(&Compliance_ServiceDesc, srv)
}

func _Compliance_UpdateSanctionFinanciersList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(emptypb.Empty)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ComplianceServer).UpdateSanctionFinanciersList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Compliance_UpdateSanctionFinanciersList_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ComplianceServer).UpdateSanctionFinanciersList(ctx, req.(*emptypb.Empty))
	}
	return interceptor(ctx, in, info, handler)
}

func _Compliance_UpdateSanctionInvolvedList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(emptypb.Empty)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ComplianceServer).UpdateSanctionInvolvedList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Compliance_UpdateSanctionInvolvedList_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ComplianceServer).UpdateSanctionInvolvedList(ctx, req.(*emptypb.Empty))
	}
	return interceptor(ctx, in, info, handler)
}

func _Compliance_UpdateSanctionUNSCList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(emptypb.Empty)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ComplianceServer).UpdateSanctionUNSCList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Compliance_UpdateSanctionUNSCList_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ComplianceServer).UpdateSanctionUNSCList(ctx, req.(*emptypb.Empty))
	}
	return interceptor(ctx, in, info, handler)
}

func _Compliance_UpdateSanctionWMDList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(emptypb.Empty)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ComplianceServer).UpdateSanctionWMDList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Compliance_UpdateSanctionWMDList_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ComplianceServer).UpdateSanctionWMDList(ctx, req.(*emptypb.Empty))
	}
	return interceptor(ctx, in, info, handler)
}

// Compliance_ServiceDesc is the grpc.ServiceDesc for Compliance service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var Compliance_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "processing.compliance.compliance.Compliance",
	HandlerType: (*ComplianceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "UpdateSanctionFinanciersList",
			Handler:    _Compliance_UpdateSanctionFinanciersList_Handler,
		},
		{
			MethodName: "UpdateSanctionInvolvedList",
			Handler:    _Compliance_UpdateSanctionInvolvedList_Handler,
		},
		{
			MethodName: "UpdateSanctionUNSCList",
			Handler:    _Compliance_UpdateSanctionUNSCList_Handler,
		},
		{
			MethodName: "UpdateSanctionWMDList",
			Handler:    _Compliance_UpdateSanctionWMDList_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "inner/processing/grpc/compliance.proto",
}
