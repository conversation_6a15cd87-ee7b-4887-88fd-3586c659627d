// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.5
// 	protoc        v5.29.3
// source: inner/processing/events/card.proto

package events

import (
	_ "git.local/sensitive/mvp/pb"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type TestMessage struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	UserId        uint64                 `protobuf:"varint,1,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	Card          string                 `protobuf:"bytes,2,opt,name=Card,proto3" json:"Card,omitempty"`
	IsActive      bool                   `protobuf:"varint,3,opt,name=is_active,json=isActive,proto3" json:"is_active,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *TestMessage) Reset() {
	*x = TestMessage{}
	mi := &file_inner_processing_events_card_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *TestMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TestMessage) ProtoMessage() {}

func (x *TestMessage) ProtoReflect() protoreflect.Message {
	mi := &file_inner_processing_events_card_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TestMessage.ProtoReflect.Descriptor instead.
func (*TestMessage) Descriptor() ([]byte, []int) {
	return file_inner_processing_events_card_proto_rawDescGZIP(), []int{0}
}

func (x *TestMessage) GetUserId() uint64 {
	if x != nil {
		return x.UserId
	}
	return 0
}

func (x *TestMessage) GetCard() string {
	if x != nil {
		return x.Card
	}
	return ""
}

func (x *TestMessage) GetIsActive() bool {
	if x != nil {
		return x.IsActive
	}
	return false
}

type ApproveCard struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	CardId        uint64                 `protobuf:"varint,1,opt,name=card_id,json=cardId,proto3" json:"card_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ApproveCard) Reset() {
	*x = ApproveCard{}
	mi := &file_inner_processing_events_card_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ApproveCard) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ApproveCard) ProtoMessage() {}

func (x *ApproveCard) ProtoReflect() protoreflect.Message {
	mi := &file_inner_processing_events_card_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ApproveCard.ProtoReflect.Descriptor instead.
func (*ApproveCard) Descriptor() ([]byte, []int) {
	return file_inner_processing_events_card_proto_rawDescGZIP(), []int{1}
}

func (x *ApproveCard) GetCardId() uint64 {
	if x != nil {
		return x.CardId
	}
	return 0
}

type ModifyPan struct {
	state           protoimpl.MessageState `protogen:"open.v1"`
	ProjectId       uint64                 `protobuf:"varint,1,opt,name=project_id,json=projectId,proto3" json:"project_id,omitempty"`
	Pan             []byte                 `protobuf:"bytes,2,opt,name=pan,proto3" json:"pan,omitempty"`
	ProjectClientId string                 `protobuf:"bytes,3,opt,name=project_client_id,json=projectClientId,proto3" json:"project_client_id,omitempty"`
	unknownFields   protoimpl.UnknownFields
	sizeCache       protoimpl.SizeCache
}

func (x *ModifyPan) Reset() {
	*x = ModifyPan{}
	mi := &file_inner_processing_events_card_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ModifyPan) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ModifyPan) ProtoMessage() {}

func (x *ModifyPan) ProtoReflect() protoreflect.Message {
	mi := &file_inner_processing_events_card_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ModifyPan.ProtoReflect.Descriptor instead.
func (*ModifyPan) Descriptor() ([]byte, []int) {
	return file_inner_processing_events_card_proto_rawDescGZIP(), []int{2}
}

func (x *ModifyPan) GetProjectId() uint64 {
	if x != nil {
		return x.ProjectId
	}
	return 0
}

func (x *ModifyPan) GetPan() []byte {
	if x != nil {
		return x.Pan
	}
	return nil
}

func (x *ModifyPan) GetProjectClientId() string {
	if x != nil {
		return x.ProjectClientId
	}
	return ""
}

type DeactivateToken struct {
	state           protoimpl.MessageState `protogen:"open.v1"`
	EncryptedCardId string                 `protobuf:"bytes,1,opt,name=encrypted_card_id,json=encryptedCardId,proto3" json:"encrypted_card_id,omitempty"`
	ProjectId       uint64                 `protobuf:"varint,2,opt,name=project_id,json=projectId,proto3" json:"project_id,omitempty"`
	unknownFields   protoimpl.UnknownFields
	sizeCache       protoimpl.SizeCache
}

func (x *DeactivateToken) Reset() {
	*x = DeactivateToken{}
	mi := &file_inner_processing_events_card_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DeactivateToken) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeactivateToken) ProtoMessage() {}

func (x *DeactivateToken) ProtoReflect() protoreflect.Message {
	mi := &file_inner_processing_events_card_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeactivateToken.ProtoReflect.Descriptor instead.
func (*DeactivateToken) Descriptor() ([]byte, []int) {
	return file_inner_processing_events_card_proto_rawDescGZIP(), []int{3}
}

func (x *DeactivateToken) GetEncryptedCardId() string {
	if x != nil {
		return x.EncryptedCardId
	}
	return ""
}

func (x *DeactivateToken) GetProjectId() uint64 {
	if x != nil {
		return x.ProjectId
	}
	return 0
}

type SaveToken struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	AcquirerId    uint64                 `protobuf:"varint,1,opt,name=acquirer_id,json=acquirerId,proto3" json:"acquirer_id,omitempty"`
	CardId        uint64                 `protobuf:"varint,2,opt,name=card_id,json=cardId,proto3" json:"card_id,omitempty"`
	TerminalId    uint64                 `protobuf:"varint,3,opt,name=terminal_id,json=terminalId,proto3" json:"terminal_id,omitempty"`
	Token         string                 `protobuf:"bytes,4,opt,name=token,proto3" json:"token,omitempty"`
	ProjectId     uint64                 `protobuf:"varint,5,opt,name=project_id,json=projectId,proto3" json:"project_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SaveToken) Reset() {
	*x = SaveToken{}
	mi := &file_inner_processing_events_card_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SaveToken) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SaveToken) ProtoMessage() {}

func (x *SaveToken) ProtoReflect() protoreflect.Message {
	mi := &file_inner_processing_events_card_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SaveToken.ProtoReflect.Descriptor instead.
func (*SaveToken) Descriptor() ([]byte, []int) {
	return file_inner_processing_events_card_proto_rawDescGZIP(), []int{4}
}

func (x *SaveToken) GetAcquirerId() uint64 {
	if x != nil {
		return x.AcquirerId
	}
	return 0
}

func (x *SaveToken) GetCardId() uint64 {
	if x != nil {
		return x.CardId
	}
	return 0
}

func (x *SaveToken) GetTerminalId() uint64 {
	if x != nil {
		return x.TerminalId
	}
	return 0
}

func (x *SaveToken) GetToken() string {
	if x != nil {
		return x.Token
	}
	return ""
}

func (x *SaveToken) GetProjectId() uint64 {
	if x != nil {
		return x.ProjectId
	}
	return 0
}

type SaveTokenwithCard struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	AcquirerId    uint64                 `protobuf:"varint,1,opt,name=acquirer_id,json=acquirerId,proto3" json:"acquirer_id,omitempty"`
	CardId        uint64                 `protobuf:"varint,2,opt,name=card_id,json=cardId,proto3" json:"card_id,omitempty"`
	TerminalId    uint64                 `protobuf:"varint,3,opt,name=terminal_id,json=terminalId,proto3" json:"terminal_id,omitempty"`
	Token         string                 `protobuf:"bytes,4,opt,name=token,proto3" json:"token,omitempty"`
	ProjectId     uint64                 `protobuf:"varint,5,opt,name=project_id,json=projectId,proto3" json:"project_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SaveTokenwithCard) Reset() {
	*x = SaveTokenwithCard{}
	mi := &file_inner_processing_events_card_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SaveTokenwithCard) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SaveTokenwithCard) ProtoMessage() {}

func (x *SaveTokenwithCard) ProtoReflect() protoreflect.Message {
	mi := &file_inner_processing_events_card_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SaveTokenwithCard.ProtoReflect.Descriptor instead.
func (*SaveTokenwithCard) Descriptor() ([]byte, []int) {
	return file_inner_processing_events_card_proto_rawDescGZIP(), []int{5}
}

func (x *SaveTokenwithCard) GetAcquirerId() uint64 {
	if x != nil {
		return x.AcquirerId
	}
	return 0
}

func (x *SaveTokenwithCard) GetCardId() uint64 {
	if x != nil {
		return x.CardId
	}
	return 0
}

func (x *SaveTokenwithCard) GetTerminalId() uint64 {
	if x != nil {
		return x.TerminalId
	}
	return 0
}

func (x *SaveTokenwithCard) GetToken() string {
	if x != nil {
		return x.Token
	}
	return ""
}

func (x *SaveTokenwithCard) GetProjectId() uint64 {
	if x != nil {
		return x.ProjectId
	}
	return 0
}

var File_inner_processing_events_card_proto protoreflect.FileDescriptor

var file_inner_processing_events_card_proto_rawDesc = string([]byte{
	0x0a, 0x22, 0x69, 0x6e, 0x6e, 0x65, 0x72, 0x2f, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69,
	0x6e, 0x67, 0x2f, 0x65, 0x76, 0x65, 0x6e, 0x74, 0x73, 0x2f, 0x63, 0x61, 0x72, 0x64, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x16, 0x6d, 0x76, 0x70, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f,
	0x65, 0x76, 0x65, 0x6e, 0x74, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x5e, 0x0a, 0x0b,
	0x54, 0x65, 0x73, 0x74, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x17, 0x0a, 0x07, 0x75,
	0x73, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x06, 0x75, 0x73,
	0x65, 0x72, 0x49, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x43, 0x61, 0x72, 0x64, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x04, 0x43, 0x61, 0x72, 0x64, 0x12, 0x1b, 0x0a, 0x09, 0x69, 0x73, 0x5f, 0x61,
	0x63, 0x74, 0x69, 0x76, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x08, 0x52, 0x08, 0x69, 0x73, 0x41,
	0x63, 0x74, 0x69, 0x76, 0x65, 0x3a, 0x05, 0xb8, 0xe9, 0xe4, 0x03, 0x01, 0x22, 0x2d, 0x0a, 0x0b,
	0x41, 0x70, 0x70, 0x72, 0x6f, 0x76, 0x65, 0x43, 0x61, 0x72, 0x64, 0x12, 0x17, 0x0a, 0x07, 0x63,
	0x61, 0x72, 0x64, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x06, 0x63, 0x61,
	0x72, 0x64, 0x49, 0x64, 0x3a, 0x05, 0xb8, 0xe9, 0xe4, 0x03, 0x01, 0x22, 0x6f, 0x0a, 0x09, 0x4d,
	0x6f, 0x64, 0x69, 0x66, 0x79, 0x50, 0x61, 0x6e, 0x12, 0x1d, 0x0a, 0x0a, 0x70, 0x72, 0x6f, 0x6a,
	0x65, 0x63, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x09, 0x70, 0x72,
	0x6f, 0x6a, 0x65, 0x63, 0x74, 0x49, 0x64, 0x12, 0x10, 0x0a, 0x03, 0x70, 0x61, 0x6e, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x0c, 0x52, 0x03, 0x70, 0x61, 0x6e, 0x12, 0x2a, 0x0a, 0x11, 0x70, 0x72, 0x6f,
	0x6a, 0x65, 0x63, 0x74, 0x5f, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0f, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x43, 0x6c, 0x69,
	0x65, 0x6e, 0x74, 0x49, 0x64, 0x3a, 0x05, 0xb8, 0xe9, 0xe4, 0x03, 0x01, 0x22, 0x63, 0x0a, 0x0f,
	0x44, 0x65, 0x61, 0x63, 0x74, 0x69, 0x76, 0x61, 0x74, 0x65, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x12,
	0x2a, 0x0a, 0x11, 0x65, 0x6e, 0x63, 0x72, 0x79, 0x70, 0x74, 0x65, 0x64, 0x5f, 0x63, 0x61, 0x72,
	0x64, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f, 0x65, 0x6e, 0x63, 0x72,
	0x79, 0x70, 0x74, 0x65, 0x64, 0x43, 0x61, 0x72, 0x64, 0x49, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x70,
	0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x04, 0x52,
	0x09, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x49, 0x64, 0x3a, 0x05, 0xb8, 0xe9, 0xe4, 0x03,
	0x01, 0x22, 0xa2, 0x01, 0x0a, 0x09, 0x53, 0x61, 0x76, 0x65, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x12,
	0x1f, 0x0a, 0x0b, 0x61, 0x63, 0x71, 0x75, 0x69, 0x72, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x04, 0x52, 0x0a, 0x61, 0x63, 0x71, 0x75, 0x69, 0x72, 0x65, 0x72, 0x49, 0x64,
	0x12, 0x17, 0x0a, 0x07, 0x63, 0x61, 0x72, 0x64, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x04, 0x52, 0x06, 0x63, 0x61, 0x72, 0x64, 0x49, 0x64, 0x12, 0x1f, 0x0a, 0x0b, 0x74, 0x65, 0x72,
	0x6d, 0x69, 0x6e, 0x61, 0x6c, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x04, 0x52, 0x0a,
	0x74, 0x65, 0x72, 0x6d, 0x69, 0x6e, 0x61, 0x6c, 0x49, 0x64, 0x12, 0x14, 0x0a, 0x05, 0x74, 0x6f,
	0x6b, 0x65, 0x6e, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x74, 0x6f, 0x6b, 0x65, 0x6e,
	0x12, 0x1d, 0x0a, 0x0a, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x05,
	0x20, 0x01, 0x28, 0x04, 0x52, 0x09, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x49, 0x64, 0x3a,
	0x05, 0xb8, 0xe9, 0xe4, 0x03, 0x01, 0x22, 0xa3, 0x01, 0x0a, 0x11, 0x53, 0x61, 0x76, 0x65, 0x54,
	0x6f, 0x6b, 0x65, 0x6e, 0x77, 0x69, 0x74, 0x68, 0x43, 0x61, 0x72, 0x64, 0x12, 0x1f, 0x0a, 0x0b,
	0x61, 0x63, 0x71, 0x75, 0x69, 0x72, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x04, 0x52, 0x0a, 0x61, 0x63, 0x71, 0x75, 0x69, 0x72, 0x65, 0x72, 0x49, 0x64, 0x12, 0x17, 0x0a,
	0x07, 0x63, 0x61, 0x72, 0x64, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x04, 0x52, 0x06,
	0x63, 0x61, 0x72, 0x64, 0x49, 0x64, 0x12, 0x1f, 0x0a, 0x0b, 0x74, 0x65, 0x72, 0x6d, 0x69, 0x6e,
	0x61, 0x6c, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x04, 0x52, 0x0a, 0x74, 0x65, 0x72,
	0x6d, 0x69, 0x6e, 0x61, 0x6c, 0x49, 0x64, 0x12, 0x14, 0x0a, 0x05, 0x74, 0x6f, 0x6b, 0x65, 0x6e,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x12, 0x1d, 0x0a,
	0x0a, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28,
	0x04, 0x52, 0x09, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x49, 0x64, 0x42, 0x2f, 0x5a, 0x2d,
	0x67, 0x69, 0x74, 0x2e, 0x6c, 0x6f, 0x63, 0x61, 0x6c, 0x2f, 0x73, 0x65, 0x6e, 0x73, 0x69, 0x74,
	0x69, 0x76, 0x65, 0x2f, 0x69, 0x6e, 0x6e, 0x65, 0x72, 0x70, 0x62, 0x2f, 0x70, 0x72, 0x6f, 0x63,
	0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x2f, 0x65, 0x76, 0x65, 0x6e, 0x74, 0x73, 0x62, 0x06, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x33,
})

var (
	file_inner_processing_events_card_proto_rawDescOnce sync.Once
	file_inner_processing_events_card_proto_rawDescData []byte
)

func file_inner_processing_events_card_proto_rawDescGZIP() []byte {
	file_inner_processing_events_card_proto_rawDescOnce.Do(func() {
		file_inner_processing_events_card_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_inner_processing_events_card_proto_rawDesc), len(file_inner_processing_events_card_proto_rawDesc)))
	})
	return file_inner_processing_events_card_proto_rawDescData
}

var file_inner_processing_events_card_proto_msgTypes = make([]protoimpl.MessageInfo, 6)
var file_inner_processing_events_card_proto_goTypes = []any{
	(*TestMessage)(nil),       // 0: TestMessage
	(*ApproveCard)(nil),       // 1: ApproveCard
	(*ModifyPan)(nil),         // 2: ModifyPan
	(*DeactivateToken)(nil),   // 3: DeactivateToken
	(*SaveToken)(nil),         // 4: SaveToken
	(*SaveTokenwithCard)(nil), // 5: SaveTokenwithCard
}
var file_inner_processing_events_card_proto_depIdxs = []int32{
	0, // [0:0] is the sub-list for method output_type
	0, // [0:0] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_inner_processing_events_card_proto_init() }
func file_inner_processing_events_card_proto_init() {
	if File_inner_processing_events_card_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_inner_processing_events_card_proto_rawDesc), len(file_inner_processing_events_card_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   6,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_inner_processing_events_card_proto_goTypes,
		DependencyIndexes: file_inner_processing_events_card_proto_depIdxs,
		MessageInfos:      file_inner_processing_events_card_proto_msgTypes,
	}.Build()
	File_inner_processing_events_card_proto = out.File
	file_inner_processing_events_card_proto_goTypes = nil
	file_inner_processing_events_card_proto_depIdxs = nil
}
