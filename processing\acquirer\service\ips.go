package service

import (
	"context"
	"git.local/sensitive/innerpb/processing/goerr"
	middleware "git.local/sensitive/pkg/middlewares"
	"git.local/sensitive/processing/acquirer/model"
	"git.local/sensitive/processing/acquirer/repository/database"
	"git.local/sensitive/processing/acquirer/schema"
	"git.local/sensitive/sdk/dog"
)

type IpsService struct {
	ipsRepo database.Ipser
}

func NewIpsService(
	ipsRepo database.Ipser,
) Ipser {
	return &IpsService{
		ipsRepo: ipsRepo,
	}
}

func (b *IpsService) Create(ctx context.Context, request *schema.Ips) (err error) {
	ctx, span := dog.CreateSpan(ctx, "IpsService_Create")
	defer span.End()

	if request == nil {
		return goerr.ErrParseErrorBody
	}

	err = b.ipsRepo.Create(ctx, request.ToModel())
	if err != nil {
		return err
	}

	return nil
}

func (b *IpsService) GetAll(ctx context.Context, pagination *middleware.PaginationInfo) (_ []*model.Ips, err error) {
	ctx, span := dog.CreateSpan(ctx, "IpsService_GetAll")
	defer span.End()

	res, err := b.ipsRepo.GetAll(ctx, pagination)

	if err != nil {
		return nil, err
	}

	return res, nil
}

func (b *IpsService) Update(ctx context.Context, id uint64, request *schema.Ips) (err error) {
	ctx, span := dog.CreateSpan(ctx, "IpsService_Update")
	defer span.End()

	if request == nil {
		return goerr.ErrParseErrorBody
	}

	if err := b.ipsRepo.Update(ctx, id, request.ToModel()); err != nil {
		return err
	}

	return nil
}

func (b *IpsService) Delete(ctx context.Context, id uint64) (err error) {
	ctx, span := dog.CreateSpan(ctx, "IpsService_Delete")
	defer span.End()

	if err := b.ipsRepo.Delete(ctx, id); err != nil {
		return err
	}

	return nil
}

func (b *IpsService) GetByID(ctx context.Context, id uint64) (_ model.Ips, err error) {
	ctx, span := dog.CreateSpan(ctx, "IpsService_GetByID")
	defer span.End()

	return b.ipsRepo.GetByID(ctx, id)
}
