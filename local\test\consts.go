package test

const (
	domainURL     = "http://localhost:3000"
	devSecret     = "12345"
	testSecret    = "qateam1313"
	prodSecret    = "cC5Q4j87BNTe3vXgyd2t"
	stageSecret   = "cC5Q4j87BNTe3vXgyd2t"
	sandBoxSecret = "cC5Q4j87BNTe3vXgyd2t"
)

const (
	devCallbackUrl     = "https://webhook.site/7fb9e488-8128-4bee-bfcd-f085f9f89422"
	devMerchantID      = 1
	devProjectID       = 42
	devProjectClientID = "kambar6122"
	testCardToken      = "AAAAAAAAAAAAAAAAAAAAAOV3HplX9zAuDTOlewBIEV79E01GeKngAYvBuZRG8fDJYH6AImkVesOCZhRPImg5T/Acc3MDbtTl2SpIr/bwnjWdD3BVgS3DkodpGhMbB1Hfs9abv+O8yrO1flDSmQtIXA=="

	devEncryptedCard   = "XRW1X0bg+7ehMGXFzp7aVaYQ4W8ebehTlJG4EK19Bzjx+kFS7tn7bU7yh0VhlVLjRgUcoViJ9+89Tqxp5yRXefRKKeiChbjzSw9EEGuuQ8Ok5XspnClo6BnFYbXOizkSV9qZzI7sulZa9AzWJqp/z/b66LSGlCfVBoTWqc4nQPcOmR3L3wwK7DBH5HMokqb0k0KlY+LbkZDqCXyWUYee/jKvbie7STO8BVRCYhUjg3xGwT5uSLLo6bg7ZxCCsgOU0I8qU3MK8Mll7ONih/2DTlZvlRfWpDjB2X5aZCIm8xLmtZMTISf2yPU0MMxqQOWeIKbrqN3fs7Gw3DA7Em1NgQ=="
	devBccCard         = "nS3BV1gKFYFZuJE7lFzy/xivqsXxfWb3ANn+kQbgNWsuzad2r3GYvtqoNkRSwlNoFWEUqNj+BqamN2XpQXWQA/+eEYSeU7VO5y1iNtDCZpGhxZE/HcK1ZKqlH8zCHOEvcaVKZnbQ1b5R1o+gPob9ksR5ZOVSvztHs6T2KY9eGMtJ6DzaiIPjkjxZOe6jSa0qBcoyCV9QmSCf/Lr56X07RuSnMk/L5kzd8hy7XnEViEQskmUfoFY6tONoxmFG1LSzQbwZN7nBEy/SDFNZDojqGGEWWbZLh5qRAWvzxNVLbfNHlXXOTsV8xq0K3RIcIHw/e0s/UsI3WOALYk4F3uBZ7w=="
	devFingerPrintCard = "jD7yoFfLtyCV9hMzU/ORPXuFTr1ZPRiRtqRY6QgGMB3dt+1bv7BKSulQSBTYc5ajKu1AjEb3NxNnUma00sewGIEfV7Yux6VlZdpORgmrHwi653TIZ6xBeCc9QaT9ffF2BqYLNDqxYXfRO5WKbpAE1h9HCeU1LFkHR1cJXiniHHKUK7Slprot1A9b67rsJNELkmhGRKdfsayLJQhrhJlv6H0jbIUggiNHBfLve65hKZFKe0iWwJOiNTC0QxkzTYS7sxtuqCgOQb2XwNaWUAZ8Lm3FHCockBg62i3cna5LFVGXd0MpcuaoxMEsMkP+6CK7lYBfznGw8TnY5gnj9NhJeA=="
)

const (
	UrlPath           = "/refund/api/v1/system/refund/partial"
	CreateTrUrlPath   = "/transaction/api/v1/transaction/primal/pay-in"
	PayInUrlPath      = "/transaction/api/v1/transaction/pay-in"
	PrimalCardLinkUrl = "/transaction/api/v1/transaction/primal/card-link"
	PrimalPayOut      = "/transaction/api/v1/transaction/primal/pay-out"
	DeactivateCardUrl = "/card/api/v1/system/client/card"
)
