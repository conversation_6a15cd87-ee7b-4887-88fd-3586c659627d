package main

import (
	"embed"
	"fmt"
	"io"
	"path/filepath"
	"strconv"
	"strings"

	"google.golang.org/protobuf/compiler/protogen"
	"gopkg.in/yaml.v3"
)

//go:embed stands
var embedStands embed.FS

var endpointsDefaultWeight = map[string]struct{}{
	"dev":   {},
	"prod":  {},
	"local": {},
}

func init() {
	RegisterGenerator(&StandClientGenerator{})
}

type StandClientGenerator struct{}

func (generator *StandClientGenerator) ProtoGenGenerate(gen *protogen.Plugin) {
	generator.Services(gen)
}

func (generator *StandClientGenerator) Services(gen *protogen.Plugin) {
	for _, file := range gen.Files {
		if !file.Generate {
			continue
		}

		if len(file.Services) == 0 {
			return
		}

		entries, err := embedStands.ReadDir("stands")
		if err != nil {
			panic(err)
		}

		var (
			stands    []string
			endpoints = map[string]map[string]string{}
		)

		for _, entry := range entries {
			if entry.IsDir() {
				continue
			}

			f, err := embedStands.Open("stands/" + entry.Name())
			if err != nil {
				panic(err)
			}

			data, err := io.ReadAll(f)
			if err != nil {
				panic(err)
			}

			var endpointFileData map[string]string
			if err := yaml.Unmarshal(data, &endpointFileData); err != nil {
				panic(err)
			}

			if err := f.Close(); err != nil {
				panic(err)
			}

			stand := strings.Split(filepath.Base(entry.Name()), ".")[0]
			stands = append(stands, stand)
			endpoints[stand] = endpointFileData
		}

		var defaultStand = func() string {
			for _, stand := range stands {
				if _, ok := endpointsDefaultWeight[stand]; ok {
					return stand
				}
			}

			return ""
		}()

		if defaultStand == "" {
			panic(fmt.Sprint("default stand not found: ", endpointsDefaultWeight))
		}

		for _, service := range file.Services {
			if _, ok := endpoints[defaultStand][string(service.Desc.FullName())]; !ok {
				panic("at default stand not found service name" + " " + string(service.Desc.FullName()) + " " + defaultStand)
			}
		}

		g := gen.NewGeneratedFile(
			file.GeneratedFilenamePrefix+V2_PREPARED_CLIENTS,
			file.GoImportPath,
		)

		HeaderPrint(g, string(file.GoPackageName))

		for _, service := range file.Services {
			g.P("func NewPrepared", service.GoName, "Client(")
			g.P("stands ...string,")
			g.P(") (", service.GoName, "Client, error) {")
			g.P("stand := ", strconv.Quote(defaultStand))
			g.P("if len(stands) > 0 {")
			g.P("stand = stands[0]")
			g.P("}")
			g.P()
			g.P("return NewPrepared", service.GoName, "ClientWithStand(stand)")
			g.P("}")
			g.P()

			g.P("func NewPrepared", service.GoName, "ClientWithStand(")
			g.P("stand string,")
			g.P(") (", service.GoName, "Client, error) {")
			g.P("var endpoint string")
			g.P("switch stand {")
			for _, stand := range stands {
				if stand == defaultStand {
					continue
				}

				if _, ok := endpoints[stand][string(service.Desc.FullName())]; !ok {
					continue
				}

				g.P("case ", strconv.Quote(stand), ":")
				g.P("endpoint = ", strconv.Quote(endpoints[stand][string(service.Desc.FullName())]))
			}
			g.P("default:")
			g.P("endpoint = ", strconv.Quote(endpoints[defaultStand][string(service.Desc.FullName())]))
			g.P("}")
			g.P()
			g.P("opts := []", grpcPackageDialOption, "{")
			g.P(grpcPackageWithTransportCredentials, "(", insecurePackageNewCredentials, "()),")
			g.P(grpcPackageWithStatsHandler, "(", otelgrpcPackageNewClientHandler, "()),")
			g.P(grpcPackageWithUnaryInterceptor, "(", grpcMiddlewareSentryPackageUnaryClientInterceptor, "()),")
			g.P(grpcPackageWithUnaryInterceptor, "(", pkgerrorPackageUnaryClientInterceptor, "()),")
			g.P("}")
			g.P()
			g.P("conn, err := ", grpcPackageNewClient, "(endpoint, opts...)")
			g.P("if err != nil {")
			g.P("return nil, err")
			g.P("}")
			g.P()
			g.P("return New", service.GoName, "Client(conn), nil")
			g.P("}")
			g.P()
		}
	}
}
