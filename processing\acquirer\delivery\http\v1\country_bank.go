package v1

import (
	"strconv"

	"github.com/gin-gonic/gin"

	"git.local/sensitive/innerpb/processing/goerr"
	"git.local/sensitive/pkg/middlewares"
	"git.local/sensitive/processing/acquirer/schema"
	"git.local/sensitive/sdk/dog"
)

func (h *Handler) initCountryBankHandler(v1 *gin.RouterGroup) {
	v1.POST("country/bank", middlewares.GinErrorHandle(h.CreateCountryBank))
	v1.GET("country/:country_id/banks", middlewares.GinErrorHandle(h.GetBanksByCountryID))
	v1.DELETE("country/:country_id/bank/:bank_id", middlewares.GinErrorHandle(h.DeleteCountryBank))
}

// CreateCountryBank
// WhoAmi godoc
// @Summary Создание связи страны и банка
// @Accept json
// @Produce json
// @Param data body schema.CountryBank true "CountryBank Data"
// @Success 200 {object} middlewares.Response[middlewares.Empty]
// @Failure 400 {object} middlewares.Response[middlewares.Empty]
// @Security bearerAuth
// @tags country_bank
// @Router /api/v1/country/bank [post]
func (h *Handler) CreateCountryBank(c *gin.Context) (err error) {
	ctx, span := dog.CreateSpan(c.Request.Context(), "Handler_CreateCountryBank")
	defer span.End()

	request := &schema.CountryBank{}

	if err = c.ShouldBind(request); err != nil {
		return goerr.ErrRequestValidation.WithErr(err).WithResult(map[string]string{
			"error": err.Error(),
		})
	}

	if err = request.Validate(); err != nil {
		return goerr.ErrRequestValidation.WithErr(err).WithResult(map[string]string{
			"error": err.Error(),
		})
	}

	err = h.services.CountryBank.Create(ctx, request)
	if err != nil {
		return err
	}

	return middlewares.Respond("success", c)
}

// GetBanksByCountryID
// @Summary Получение списка банков по айди страны
// @Produce json
// @Param country_id path int true "Country ID"
// @Success 200 {object} middlewares.Response[[]model.Bank]
// @Failure 400 {object} middlewares.Response[middlewares.Empty]
// @Security bearerAuth
// @tags country_bank
// @Router /api/v1/country/{country_id}/banks [get]
func (h *Handler) GetBanksByCountryID(c *gin.Context) (err error) {
	ctx, span := dog.CreateSpan(c.Request.Context(), "Handler_GetBanksByCountryID")
	defer span.End()

	countryID, err := strconv.ParseUint(c.Param("country_id"), 10, 64)
	if err != nil {
		return goerr.ErrInvalidPathParam.WithErr(err).WithCtx(ctx).WithResult(map[string]string{
			"param":   "country_id",
			"message": "must be number",
		})
	}

	res, err := h.services.CountryBank.GetBanksByCountryID(ctx, countryID)
	if err != nil {
		return err
	}

	return middlewares.Respond(res, c)
}

// DeleteCountryBank
// WhoAmi godoc
// @Summary Удаление связи страны и банка
// @Produce json
// @Param country_id path int true "Country ID"
// @Param bank_id path int true "Bank ID"
// @Success 200 {object} middlewares.Response[string]
// @Failure 400 {object} middlewares.Response[middlewares.Empty]
// @Security bearerAuth
// @tags country_bank
// @Router /api/v1/country/{country_id}/bank/{bank_id} [delete]
func (h *Handler) DeleteCountryBank(c *gin.Context) (err error) {
	ctx, span := dog.CreateSpan(c.Request.Context(), "Handler_DeleteCountryBank")
	defer span.End()

	countryId, err := strconv.ParseUint(c.Param("country_id"), 10, 64)
	if err != nil {
		return goerr.ErrInvalidPathParam.WithErr(err).WithCtx(ctx).WithResult(map[string]string{
			"param":   "country_id",
			"message": "must be number",
		})
	}

	bankId, err := strconv.ParseUint(c.Param("bank_id"), 10, 64)
	if err != nil {
		return goerr.ErrInvalidPathParam.WithErr(err).WithCtx(ctx).WithResult(map[string]string{
			"param":   "bank_id",
			"message": "must be number",
		})
	}

	err = h.services.CountryBank.Delete(ctx, countryId, bankId)
	if err != nil {
		return err
	}

	return middlewares.Respond("success", c)
}
