// Code generated by protoc-gen-go-mvp. DO NOT EDIT.
// versions:
// - protoc-gen-go-mvp v2.0.1
// created by mvp authors

package grpc

func (x JusanResponseCodeTokenize) Code() string {
	switch x {
	case JusanResponseCodeTokenize_TokenizeServiceUnavailable:
		return "11"
	case JusanResponseCodeTokenize_TokenizeIncorrectFieldOrder:
		return "12"
	case JusanResponseCodeTokenize_TokenizeIncorrectAmount:
		return "13"
	case JusanResponseCodeTokenize_TokenizeIncorrectCurrency:
		return "14"
	case JusanResponseCodeTokenize_TokenizeUnavailableMPI:
		return "15"
	case JusanResponseCodeTokenize_TokenizeUnavailableDb:
		return "16"
	case JusanResponseCodeTokenize_TokenizeOperationForbidden:
		return "171"
	case JusanResponseCodeTokenize_TokenizeOperationForbiddenByLaw:
		return "172 "
	case JusanResponseCodeTokenize_TokenizeRequestAlreadyCompleted:
		return "18"
	case JusanResponseCodeTokenize_TokenizeIncorrectCardExpDate:
		return "19"
	case JusanResponseCodeTokenize_TokenizeIncorrectFieldTerminal:
		return "20"
	case JusanResponseCodeTokenize_TokenizeIncorrectSign:
		return "21"
	case JusanResponseCodeTokenize_TokenizeCurrencyNotFound:
		return "22"
	case JusanResponseCodeTokenize_TokenizeLimitExceeded:
		return "23"
	case JusanResponseCodeTokenize_TokenizeEmptyField:
		return "24"
	case JusanResponseCodeTokenize_TokenizeFieldSizeLessSymbols:
		return "25"
	case JusanResponseCodeTokenize_TokenizeFieldSizeMoreSymbols:
		return "26"
	case JusanResponseCodeTokenize_TokenizeInvalidField:
		return "27"
	case JusanResponseCodeTokenize_TokenizeMPIError3DS:
		return "28"
	case JusanResponseCodeTokenize_TokenizeUnacceptableCardType:
		return "29"
	case JusanResponseCodeTokenize_TokenizePaymentNotFound:
		return "30"
	case JusanResponseCodeTokenize_TokenizeClientKeyNotFound:
		return "31"
	case JusanResponseCodeTokenize_TokenizeForbidden:
		return "32"
	case JusanResponseCodeTokenize_TokenizeTokenNotFound:
		return "33"
	case JusanResponseCodeTokenize_TokenizeIncorrectBlockAmount:
		return "34"
	case JusanResponseCodeTokenize_TokenizeUnknownError:
		return "99"
	case JusanResponseCodeTokenize_TokenizeServiceUnavailableTryLater:
		return "41"
	case JusanResponseCodeTokenize_TokenizeInvalidAmount:
		return "42"
	case JusanResponseCodeTokenize_TokenizeServiceDbUnavailable:
		return "43"
	case JusanResponseCodeTokenize_TokenizeIncorrectFieldMerchant:
		return "44"
	case JusanResponseCodeTokenize_TokenizeMerchantNotFound:
		return "17"
	case JusanResponseCodeTokenize_TokenizeOrderRequestNotFound:
		return "45"
	case JusanResponseCodeTokenize_TokenizeInvalidSign:
		return "46"
	case JusanResponseCodeTokenize_TokenizeIncorrectRefundSum:
		return "47 "
	case JusanResponseCodeTokenize_TokenizeIncorrectStatus:
		return "48"
	case JusanResponseCodeTokenize_TokenizeIncorrectValue:
		return "50"
	case JusanResponseCodeTokenize_TokenizeIncorrectTerminalStatus:
		return "51"
	case JusanResponseCodeTokenize_TokenizeForbiddenOperation:
		return "52"
	case JusanResponseCodeTokenize_TokenizeDuplicateDescription:
		return "53"
	case JusanResponseCodeTokenize_TokenizeRefundError:
		return "F"
	case JusanResponseCodeTokenize_TokenizePayError:
		return "E"
	case JusanResponseCodeTokenize_TokenizePaymentExpired:
		return "c"
	case JusanResponseCodeTokenize_TokenizeTransactionHandleError:
		return "411"
	default:
		return "0"
	}
}

func (x JusanResponseCodeTokenize) Description() string {
	switch x {
	case JusanResponseCodeTokenize_TokenizeServiceUnavailable:
		return "Сервис временно недоступен, попробуйте позже"
	case JusanResponseCodeTokenize_TokenizeIncorrectFieldOrder:
		return "Неправильное значение в поле ORDER:"
	case JusanResponseCodeTokenize_TokenizeIncorrectAmount:
		return "Неправильная сумма: "
	case JusanResponseCodeTokenize_TokenizeIncorrectCurrency:
		return "Неправильная валюта:"
	case JusanResponseCodeTokenize_TokenizeUnavailableMPI:
		return "Сервис MPI временно недоступен, попробуйте позже"
	case JusanResponseCodeTokenize_TokenizeUnavailableDb:
		return "Сервис Db временно недоступен, попробуйте позже"
	case JusanResponseCodeTokenize_TokenizeOperationForbidden:
		return "Коммерсанту запрещено выполнение операций"
	case JusanResponseCodeTokenize_TokenizeOperationForbiddenByLaw:
		return "Коммерсанту запрещено проведение операций в соответствии с Законом о ПОД/ФТ"
	case JusanResponseCodeTokenize_TokenizeRequestAlreadyCompleted:
		return "Запрос уже выполнялся"
	case JusanResponseCodeTokenize_TokenizeIncorrectCardExpDate:
		return "Неправильная дата дейстия карты (MM/ГГ)"
	case JusanResponseCodeTokenize_TokenizeIncorrectFieldTerminal:
		return "Неправильное значение в поле TERMINAL:"
	case JusanResponseCodeTokenize_TokenizeIncorrectSign:
		return "Неправильная подпись!"
	case JusanResponseCodeTokenize_TokenizeCurrencyNotFound:
		return "Не найден курс валюты"
	case JusanResponseCodeTokenize_TokenizeLimitExceeded:
		return "Превышен лимит!"
	case JusanResponseCodeTokenize_TokenizeEmptyField:
		return "Не указано значение в поле"
	case JusanResponseCodeTokenize_TokenizeFieldSizeLessSymbols:
		return "Размер значения в поле менее симоволов"
	case JusanResponseCodeTokenize_TokenizeFieldSizeMoreSymbols:
		return "Размер значения в поле больше симоволов"
	case JusanResponseCodeTokenize_TokenizeInvalidField:
		return "Введите валидное значение в поле"
	case JusanResponseCodeTokenize_TokenizeMPIError3DS:
		return "Ошибка MPI при выполнении проверки 3DS:"
	case JusanResponseCodeTokenize_TokenizeUnacceptableCardType:
		return "Недопустимый тип карты"
	case JusanResponseCodeTokenize_TokenizePaymentNotFound:
		return "Счет на оплату не найден"
	case JusanResponseCodeTokenize_TokenizeClientKeyNotFound:
		return "Не передан ключ указанного клиента"
	case JusanResponseCodeTokenize_TokenizeForbidden:
		return "Для терминала запрещена токенизация"
	case JusanResponseCodeTokenize_TokenizeTokenNotFound:
		return "Для данного клиента в вашей организации не зарегистрирован токен"
	case JusanResponseCodeTokenize_TokenizeIncorrectBlockAmount:
		return "Неверная сумма блокирования, заявка отменена!"
	case JusanResponseCodeTokenize_TokenizeUnknownError:
		return "Неизвестная ошибка: "
	case JusanResponseCodeTokenize_TokenizeServiceUnavailableTryLater:
		return "Сервис временно недоступен, попробуйте позже"
	case JusanResponseCodeTokenize_TokenizeInvalidAmount:
		return "Неправильная сумма"
	case JusanResponseCodeTokenize_TokenizeServiceDbUnavailable:
		return "Сервис Db временно недоступен, попробуйте позже "
	case JusanResponseCodeTokenize_TokenizeIncorrectFieldMerchant:
		return "Неправильное значение в поле MERCHANT"
	case JusanResponseCodeTokenize_TokenizeMerchantNotFound:
		return "Коммерсант не найден"
	case JusanResponseCodeTokenize_TokenizeOrderRequestNotFound:
		return "Заявка ORDER не найдена"
	case JusanResponseCodeTokenize_TokenizeInvalidSign:
		return "Неправильная подпись!"
	case JusanResponseCodeTokenize_TokenizeIncorrectRefundSum:
		return "Сумма возврта '%s' больше чем сумма заказа"
	case JusanResponseCodeTokenize_TokenizeIncorrectStatus:
		return "Текущий статус заказа не позволяет делать возврат/отмену"
	case JusanResponseCodeTokenize_TokenizeIncorrectValue:
		return "Неправильное значение"
	case JusanResponseCodeTokenize_TokenizeIncorrectTerminalStatus:
		return "Текущий статус терминала не позволяет производить операции"
	case JusanResponseCodeTokenize_TokenizeForbiddenOperation:
		return "Операция отмены/возврата через API для терминала запрещена"
	case JusanResponseCodeTokenize_TokenizeDuplicateDescription:
		return "Дублирование описания отмены"
	case JusanResponseCodeTokenize_TokenizeRefundError:
		return "Ошибка при обработке возврата"
	case JusanResponseCodeTokenize_TokenizePayError:
		return "Ошибка при оплате"
	case JusanResponseCodeTokenize_TokenizePaymentExpired:
		return "Счет на оплату устарел"
	case JusanResponseCodeTokenize_TokenizeTransactionHandleError:
		return "Ошибка при обработке транзакции"
	default:
		return "default"
	}
}

func (x JusanResponseCodeTokenize) IntegrationError() IntegrationError {
	switch x {
	case JusanResponseCodeTokenize_TokenizeServiceUnavailable:
		return IntegrationError_UnavailableAcquirer
	case JusanResponseCodeTokenize_TokenizeIncorrectFieldOrder:
		return IntegrationError_TransactionDeclinedByAcquirer
	case JusanResponseCodeTokenize_TokenizeIncorrectAmount:
		return IntegrationError_TransactionDeclinedByAcquirer
	case JusanResponseCodeTokenize_TokenizeIncorrectCurrency:
		return IntegrationError_TransactionDeclinedByAcquirer
	case JusanResponseCodeTokenize_TokenizeUnavailableMPI:
		return IntegrationError_UnavailableAcquirer
	case JusanResponseCodeTokenize_TokenizeUnavailableDb:
		return IntegrationError_UnavailableAcquirer
	case JusanResponseCodeTokenize_TokenizeOperationForbidden:
		return IntegrationError_PaymentForbiddenForMerchant
	case JusanResponseCodeTokenize_TokenizeOperationForbiddenByLaw:
		return IntegrationError_PaymentForbiddenForMerchant
	case JusanResponseCodeTokenize_TokenizeRequestAlreadyCompleted:
		return IntegrationError_TransactionDeclinedByAcquirer
	case JusanResponseCodeTokenize_TokenizeIncorrectCardExpDate:
		return IntegrationError_IncorrectCardExpDate
	case JusanResponseCodeTokenize_TokenizeIncorrectFieldTerminal:
		return IntegrationError_TransactionDeclinedByAcquirer
	case JusanResponseCodeTokenize_TokenizeIncorrectSign:
		return IntegrationError_TransactionDeclinedByAcquirer
	case JusanResponseCodeTokenize_TokenizeCurrencyNotFound:
		return IntegrationError_TransactionDeclinedByAcquirer
	case JusanResponseCodeTokenize_TokenizeLimitExceeded:
		return IntegrationError_ExceedsAmountLimit
	case JusanResponseCodeTokenize_TokenizeEmptyField:
		return IntegrationError_TransactionDeclinedByAcquirer
	case JusanResponseCodeTokenize_TokenizeFieldSizeLessSymbols:
		return IntegrationError_TransactionDeclinedByAcquirer
	case JusanResponseCodeTokenize_TokenizeFieldSizeMoreSymbols:
		return IntegrationError_TransactionDeclinedByAcquirer
	case JusanResponseCodeTokenize_TokenizeInvalidField:
		return IntegrationError_TransactionDeclinedByAcquirer
	case JusanResponseCodeTokenize_TokenizeMPIError3DS:
		return IntegrationError_ThreeDSAuthFailed
	case JusanResponseCodeTokenize_TokenizeUnacceptableCardType:
		return IntegrationError_InvalidCard
	case JusanResponseCodeTokenize_TokenizePaymentNotFound:
		return IntegrationError_TransactionDeclinedByAcquirer
	case JusanResponseCodeTokenize_TokenizeClientKeyNotFound:
		return IntegrationError_TransactionDeclinedByAcquirer
	case JusanResponseCodeTokenize_TokenizeForbidden:
		return IntegrationError_PaymentForbiddenForMerchant
	case JusanResponseCodeTokenize_TokenizeTokenNotFound:
		return IntegrationError_InvalidCard
	case JusanResponseCodeTokenize_TokenizeIncorrectBlockAmount:
		return IntegrationError_TransactionDeclinedByAcquirer
	case JusanResponseCodeTokenize_TokenizeUnknownError:
		return IntegrationError_TransactionDeclinedByAcquirer
	case JusanResponseCodeTokenize_TokenizeServiceUnavailableTryLater:
		return IntegrationError_UnavailableAcquirer
	case JusanResponseCodeTokenize_TokenizeInvalidAmount:
		return IntegrationError_TransactionDeclinedByAcquirer
	case JusanResponseCodeTokenize_TokenizeServiceDbUnavailable:
		return IntegrationError_UnavailableAcquirer
	case JusanResponseCodeTokenize_TokenizeIncorrectFieldMerchant:
		return IntegrationError_TransactionDeclinedByAcquirer
	case JusanResponseCodeTokenize_TokenizeMerchantNotFound:
		return IntegrationError_PaymentForbiddenForMerchant
	case JusanResponseCodeTokenize_TokenizeOrderRequestNotFound:
		return IntegrationError_TransactionDeclinedByAcquirer
	case JusanResponseCodeTokenize_TokenizeInvalidSign:
		return IntegrationError_TransactionDeclinedByAcquirer
	case JusanResponseCodeTokenize_TokenizeIncorrectRefundSum:
		return IntegrationError_TransactionDeclinedByAcquirer
	case JusanResponseCodeTokenize_TokenizeIncorrectStatus:
		return IntegrationError_TransactionDeclinedByAcquirer
	case JusanResponseCodeTokenize_TokenizeIncorrectValue:
		return IntegrationError_TransactionDeclinedByAcquirer
	case JusanResponseCodeTokenize_TokenizeIncorrectTerminalStatus:
		return IntegrationError_PaymentForbiddenForMerchant
	case JusanResponseCodeTokenize_TokenizeForbiddenOperation:
		return IntegrationError_PaymentForbiddenForMerchant
	case JusanResponseCodeTokenize_TokenizeDuplicateDescription:
		return IntegrationError_TransactionDeclinedByAcquirer
	case JusanResponseCodeTokenize_TokenizeRefundError:
		return IntegrationError_TransactionDeclinedByAcquirer
	case JusanResponseCodeTokenize_TokenizePayError:
		return IntegrationError_TransactionDeclinedByAcquirer
	case JusanResponseCodeTokenize_TokenizePaymentExpired:
		return IntegrationError_TransactionDeclinedByAcquirer
	case JusanResponseCodeTokenize_TokenizeTransactionHandleError:
		return IntegrationError_TransactionDeclinedByAcquirer
	default:
		return IntegrationError_None
	}
}

func (x JusanResponseCodeTokenize) TransactionStatus() EnumTransactionStatus {
	switch x {
	case JusanResponseCodeTokenize_TokenizeServiceUnavailable:
		return EnumTransactionStatus_TransactionStatusFailed
	case JusanResponseCodeTokenize_TokenizeIncorrectFieldOrder:
		return EnumTransactionStatus_TransactionStatusFailed
	case JusanResponseCodeTokenize_TokenizeIncorrectAmount:
		return EnumTransactionStatus_TransactionStatusFailed
	case JusanResponseCodeTokenize_TokenizeIncorrectCurrency:
		return EnumTransactionStatus_TransactionStatusFailed
	case JusanResponseCodeTokenize_TokenizeUnavailableMPI:
		return EnumTransactionStatus_TransactionStatusFailed
	case JusanResponseCodeTokenize_TokenizeUnavailableDb:
		return EnumTransactionStatus_TransactionStatusFailed
	case JusanResponseCodeTokenize_TokenizeOperationForbidden:
		return EnumTransactionStatus_TransactionStatusFailed
	case JusanResponseCodeTokenize_TokenizeOperationForbiddenByLaw:
		return EnumTransactionStatus_TransactionStatusFailed
	case JusanResponseCodeTokenize_TokenizeRequestAlreadyCompleted:
		return EnumTransactionStatus_TransactionStatusFailed
	case JusanResponseCodeTokenize_TokenizeIncorrectCardExpDate:
		return EnumTransactionStatus_TransactionStatusFailed
	case JusanResponseCodeTokenize_TokenizeIncorrectFieldTerminal:
		return EnumTransactionStatus_TransactionStatusFailed
	case JusanResponseCodeTokenize_TokenizeIncorrectSign:
		return EnumTransactionStatus_TransactionStatusFailed
	case JusanResponseCodeTokenize_TokenizeCurrencyNotFound:
		return EnumTransactionStatus_TransactionStatusFailed
	case JusanResponseCodeTokenize_TokenizeLimitExceeded:
		return EnumTransactionStatus_TransactionStatusFailed
	case JusanResponseCodeTokenize_TokenizeEmptyField:
		return EnumTransactionStatus_TransactionStatusFailed
	case JusanResponseCodeTokenize_TokenizeFieldSizeLessSymbols:
		return EnumTransactionStatus_TransactionStatusFailed
	case JusanResponseCodeTokenize_TokenizeFieldSizeMoreSymbols:
		return EnumTransactionStatus_TransactionStatusFailed
	case JusanResponseCodeTokenize_TokenizeInvalidField:
		return EnumTransactionStatus_TransactionStatusFailed
	case JusanResponseCodeTokenize_TokenizeMPIError3DS:
		return EnumTransactionStatus_TransactionStatusFailed
	case JusanResponseCodeTokenize_TokenizeUnacceptableCardType:
		return EnumTransactionStatus_TransactionStatusFailed
	case JusanResponseCodeTokenize_TokenizePaymentNotFound:
		return EnumTransactionStatus_TransactionStatusFailed
	case JusanResponseCodeTokenize_TokenizeClientKeyNotFound:
		return EnumTransactionStatus_TransactionStatusFailed
	case JusanResponseCodeTokenize_TokenizeForbidden:
		return EnumTransactionStatus_TransactionStatusFailed
	case JusanResponseCodeTokenize_TokenizeTokenNotFound:
		return EnumTransactionStatus_TransactionStatusFailed
	case JusanResponseCodeTokenize_TokenizeIncorrectBlockAmount:
		return EnumTransactionStatus_TransactionStatusFailed
	case JusanResponseCodeTokenize_TokenizeUnknownError:
		return EnumTransactionStatus_TransactionStatusHolded
	case JusanResponseCodeTokenize_TokenizeServiceUnavailableTryLater:
		return EnumTransactionStatus_TransactionStatusFailed
	case JusanResponseCodeTokenize_TokenizeInvalidAmount:
		return EnumTransactionStatus_TransactionStatusFailed
	case JusanResponseCodeTokenize_TokenizeServiceDbUnavailable:
		return EnumTransactionStatus_TransactionStatusFailed
	case JusanResponseCodeTokenize_TokenizeIncorrectFieldMerchant:
		return EnumTransactionStatus_TransactionStatusFailed
	case JusanResponseCodeTokenize_TokenizeMerchantNotFound:
		return EnumTransactionStatus_TransactionStatusFailed
	case JusanResponseCodeTokenize_TokenizeOrderRequestNotFound:
		return EnumTransactionStatus_TransactionStatusFailed
	case JusanResponseCodeTokenize_TokenizeInvalidSign:
		return EnumTransactionStatus_TransactionStatusFailed
	case JusanResponseCodeTokenize_TokenizeIncorrectRefundSum:
		return EnumTransactionStatus_TransactionStatusFailed
	case JusanResponseCodeTokenize_TokenizeIncorrectStatus:
		return EnumTransactionStatus_TransactionStatusFailed
	case JusanResponseCodeTokenize_TokenizeIncorrectValue:
		return EnumTransactionStatus_TransactionStatusFailed
	case JusanResponseCodeTokenize_TokenizeIncorrectTerminalStatus:
		return EnumTransactionStatus_TransactionStatusFailed
	case JusanResponseCodeTokenize_TokenizeForbiddenOperation:
		return EnumTransactionStatus_TransactionStatusFailed
	case JusanResponseCodeTokenize_TokenizeDuplicateDescription:
		return EnumTransactionStatus_TransactionStatusFailed
	case JusanResponseCodeTokenize_TokenizeRefundError:
		return EnumTransactionStatus_TransactionStatusFailed
	case JusanResponseCodeTokenize_TokenizePayError:
		return EnumTransactionStatus_TransactionStatusFailed
	case JusanResponseCodeTokenize_TokenizePaymentExpired:
		return EnumTransactionStatus_TransactionStatusFailed
	case JusanResponseCodeTokenize_TokenizeTransactionHandleError:
		return EnumTransactionStatus_TransactionStatusFailed
	default:
		return EnumTransactionStatus_TransactionStatusError
	}
}

// Created reference to JusanResponseCodeTokenize

//	|	JusanResponseCodeTokenize                                   	|	Code  	|	Description                                                                                                                                   	|	IntegrationError                              	|	TransactionStatus                            	|
//	|	JusanResponseCodeTokenize_TokenizeServiceUnavailable        	|	"11"  	|	"Сервис временно недоступен, попробуйте позже"                                                                                                	|	IntegrationError_UnavailableAcquirer          	|	EnumTransactionStatus_TransactionStatusFailed	|
//	|	JusanResponseCodeTokenize_TokenizeIncorrectFieldOrder       	|	"12"  	|	"Неправильное значение в поле ORDER:"                                                                                                         	|	IntegrationError_TransactionDeclinedByAcquirer	|	EnumTransactionStatus_TransactionStatusFailed	|
//	|	JusanResponseCodeTokenize_TokenizeIncorrectAmount           	|	"13"  	|	"Неправильная сумма: "                                                                                                                        	|	IntegrationError_TransactionDeclinedByAcquirer	|	EnumTransactionStatus_TransactionStatusFailed	|
//	|	JusanResponseCodeTokenize_TokenizeIncorrectCurrency         	|	"14"  	|	"Неправильная валюта:"                                                                                                                        	|	IntegrationError_TransactionDeclinedByAcquirer	|	EnumTransactionStatus_TransactionStatusFailed	|
//	|	JusanResponseCodeTokenize_TokenizeUnavailableMPI            	|	"15"  	|	"Сервис MPI временно недоступен, попробуйте позже"                                                                                            	|	IntegrationError_UnavailableAcquirer          	|	EnumTransactionStatus_TransactionStatusFailed	|
//	|	JusanResponseCodeTokenize_TokenizeUnavailableDb             	|	"16"  	|	"Сервис Db временно недоступен, попробуйте позже"                                                                                             	|	IntegrationError_UnavailableAcquirer          	|	EnumTransactionStatus_TransactionStatusFailed	|
//	|	JusanResponseCodeTokenize_TokenizeOperationForbidden        	|	"171" 	|	"Коммерсанту запрещено выполнение операций"                                                                                                   	|	IntegrationError_PaymentForbiddenForMerchant  	|	EnumTransactionStatus_TransactionStatusFailed	|
//	|	JusanResponseCodeTokenize_TokenizeOperationForbiddenByLaw   	|	"172 "	|	"Коммерсанту запрещено проведение операций в соответствии с Законом о ПОД/ФТ"                                                                 	|	IntegrationError_PaymentForbiddenForMerchant  	|	EnumTransactionStatus_TransactionStatusFailed	|
//	|	JusanResponseCodeTokenize_TokenizeRequestAlreadyCompleted   	|	"18"  	|	"Запрос уже выполнялся"                                                                                                                       	|	IntegrationError_TransactionDeclinedByAcquirer	|	EnumTransactionStatus_TransactionStatusFailed	|
//	|	JusanResponseCodeTokenize_TokenizeIncorrectCardExpDate      	|	"19"  	|	"Неправильная дата дейстия карты (MM/ГГ)"                                                                                                     	|	IntegrationError_IncorrectCardExpDate         	|	EnumTransactionStatus_TransactionStatusFailed	|
//	|	JusanResponseCodeTokenize_TokenizeIncorrectFieldTerminal    	|	"20"  	|	"Неправильное значение в поле TERMINAL:"                                                                                                      	|	IntegrationError_TransactionDeclinedByAcquirer	|	EnumTransactionStatus_TransactionStatusFailed	|
//	|	JusanResponseCodeTokenize_TokenizeIncorrectSign             	|	"21"  	|	"Неправильная подпись!"                                                                                                                       	|	IntegrationError_TransactionDeclinedByAcquirer	|	EnumTransactionStatus_TransactionStatusFailed	|
//	|	JusanResponseCodeTokenize_TokenizeCurrencyNotFound          	|	"22"  	|	"Не найден курс валюты"                                                                                                                       	|	IntegrationError_TransactionDeclinedByAcquirer	|	EnumTransactionStatus_TransactionStatusFailed	|
//	|	JusanResponseCodeTokenize_TokenizeLimitExceeded             	|	"23"  	|	"Превышен лимит!"                                                                                                                             	|	IntegrationError_ExceedsAmountLimit           	|	EnumTransactionStatus_TransactionStatusFailed	|
//	|	JusanResponseCodeTokenize_TokenizeEmptyField                	|	"24"  	|	"Не указано значение в поле"                                                                                                                  	|	IntegrationError_TransactionDeclinedByAcquirer	|	EnumTransactionStatus_TransactionStatusFailed	|
//	|	JusanResponseCodeTokenize_TokenizeFieldSizeLessSymbols      	|	"25"  	|	"Размер значения в поле менее симоволов"                                                                                                      	|	IntegrationError_TransactionDeclinedByAcquirer	|	EnumTransactionStatus_TransactionStatusFailed	|
//	|	JusanResponseCodeTokenize_TokenizeFieldSizeMoreSymbols      	|	"26"  	|	"Размер значения в поле больше симоволов"                                                                                                     	|	IntegrationError_TransactionDeclinedByAcquirer	|	EnumTransactionStatus_TransactionStatusFailed	|
//	|	JusanResponseCodeTokenize_TokenizeInvalidField              	|	"27"  	|	"Введите валидное значение в поле"                                                                                                            	|	IntegrationError_TransactionDeclinedByAcquirer	|	EnumTransactionStatus_TransactionStatusFailed	|
//	|	JusanResponseCodeTokenize_TokenizeMPIError3DS               	|	"28"  	|	"Ошибка MPI при выполнении проверки 3DS:"                                                                                                     	|	IntegrationError_ThreeDSAuthFailed            	|	EnumTransactionStatus_TransactionStatusFailed	|
//	|	JusanResponseCodeTokenize_TokenizeUnacceptableCardType      	|	"29"  	|	"Недопустимый тип карты"                                                                                                                      	|	IntegrationError_InvalidCard                  	|	EnumTransactionStatus_TransactionStatusFailed	|
//	|	JusanResponseCodeTokenize_TokenizePaymentNotFound           	|	"30"  	|	"Счет на оплату не найден"                                                                                                                    	|	IntegrationError_TransactionDeclinedByAcquirer	|	EnumTransactionStatus_TransactionStatusFailed	|
//	|	JusanResponseCodeTokenize_TokenizeClientKeyNotFound         	|	"31"  	|	"Не передан ключ указанного клиента"                                                                                                          	|	IntegrationError_TransactionDeclinedByAcquirer	|	EnumTransactionStatus_TransactionStatusFailed	|
//	|	JusanResponseCodeTokenize_TokenizeForbidden                 	|	"32"  	|	"Для терминала запрещена токенизация"                                                                                                         	|	IntegrationError_PaymentForbiddenForMerchant  	|	EnumTransactionStatus_TransactionStatusFailed	|
//	|	JusanResponseCodeTokenize_TokenizeTokenNotFound             	|	"33"  	|	"Для данного клиента в вашей организации не зарегистрирован токен"                                                                            	|	IntegrationError_InvalidCard                  	|	EnumTransactionStatus_TransactionStatusFailed	|
//	|	JusanResponseCodeTokenize_TokenizeIncorrectBlockAmount      	|	"34"  	|	"Неверная сумма блокирования, заявка отменена!"                                                                                               	|	IntegrationError_TransactionDeclinedByAcquirer	|	EnumTransactionStatus_TransactionStatusFailed	|
//	|	JusanResponseCodeTokenize_TokenizeUnknownError              	|	"99"  	|	"Неизвестная ошибка: "                                                                                                                        	|	IntegrationError_TransactionDeclinedByAcquirer	|	EnumTransactionStatus_TransactionStatusHolded	|
//	|	JusanResponseCodeTokenize_TokenizeServiceUnavailableTryLater	|	"41"  	|	"Сервис временно недоступен, попробуйте позже"                                                                                                	|	IntegrationError_UnavailableAcquirer          	|	EnumTransactionStatus_TransactionStatusFailed	|
//	|	JusanResponseCodeTokenize_TokenizeInvalidAmount             	|	"42"  	|	"Неправильная сумма"                                                                                                                          	|	IntegrationError_TransactionDeclinedByAcquirer	|	EnumTransactionStatus_TransactionStatusFailed	|
//	|	JusanResponseCodeTokenize_TokenizeServiceDbUnavailable      	|	"43"  	|	"Сервис Db временно недоступен, попробуйте позже "                                                                                            	|	IntegrationError_UnavailableAcquirer          	|	EnumTransactionStatus_TransactionStatusFailed	|
//	|	JusanResponseCodeTokenize_TokenizeIncorrectFieldMerchant    	|	"44"  	|	"Неправильное значение в поле MERCHANT"                                                                                                       	|	IntegrationError_TransactionDeclinedByAcquirer	|	EnumTransactionStatus_TransactionStatusFailed	|
//	|	JusanResponseCodeTokenize_TokenizeMerchantNotFound          	|	"17"  	|	"Коммерсант не найден"                                                                                                                        	|	IntegrationError_PaymentForbiddenForMerchant  	|	EnumTransactionStatus_TransactionStatusFailed	|
//	|	JusanResponseCodeTokenize_TokenizeOrderRequestNotFound      	|	"45"  	|	"Заявка ORDER не найдена"                                                                                                                     	|	IntegrationError_TransactionDeclinedByAcquirer	|	EnumTransactionStatus_TransactionStatusFailed	|
//	|	JusanResponseCodeTokenize_TokenizeInvalidSign               	|	"46"  	|	"Неправильная подпись!"                                                                                                                       	|	IntegrationError_TransactionDeclinedByAcquirer	|	EnumTransactionStatus_TransactionStatusFailed	|
//	|	JusanResponseCodeTokenize_TokenizeIncorrectRefundSum        	|	"47 " 	|	"Сумма возврта '%s' больше чем сумма заказа"                                                                                                  	|	IntegrationError_TransactionDeclinedByAcquirer	|	EnumTransactionStatus_TransactionStatusFailed	|
//	|	JusanResponseCodeTokenize_TokenizeIncorrectStatus           	|	"48"  	|	"Текущий статус заказа не позволяет делать возврат/отмену"                                                                                    	|	IntegrationError_TransactionDeclinedByAcquirer	|	EnumTransactionStatus_TransactionStatusFailed	|
//	|	JusanResponseCodeTokenize_TokenizeIncorrectValue            	|	"50"  	|	"Неправильное значение"                                                                                                                       	|	IntegrationError_TransactionDeclinedByAcquirer	|	EnumTransactionStatus_TransactionStatusFailed	|
//	|	JusanResponseCodeTokenize_TokenizeIncorrectTerminalStatus   	|	"51"  	|	"Текущий статус терминала не позволяет производить операции"                                                                                  	|	IntegrationError_PaymentForbiddenForMerchant  	|	EnumTransactionStatus_TransactionStatusFailed	|
//	|	JusanResponseCodeTokenize_TokenizeForbiddenOperation        	|	"52"  	|	"Операция отмены/возврата через API для терминала запрещена"                                                                                  	|	IntegrationError_PaymentForbiddenForMerchant  	|	EnumTransactionStatus_TransactionStatusFailed	|
//	|	JusanResponseCodeTokenize_TokenizeDuplicateDescription      	|	"53"  	|	"Дублирование описания отмены"                                                                                                                	|	IntegrationError_TransactionDeclinedByAcquirer	|	EnumTransactionStatus_TransactionStatusFailed	|
//	|	JusanResponseCodeTokenize_TokenizeRefundError               	|	"F"   	|	"Ошибка при обработке возврата"                                                                                                               	|	IntegrationError_TransactionDeclinedByAcquirer	|	EnumTransactionStatus_TransactionStatusFailed	|
//	|	JusanResponseCodeTokenize_TokenizePayError                  	|	"E"   	|	"Ошибка при оплате"                                                                                                                           	|	IntegrationError_TransactionDeclinedByAcquirer	|	EnumTransactionStatus_TransactionStatusFailed	|
//	|	JusanResponseCodeTokenize_TokenizePaymentExpired            	|	"c"   	|	"Счет на оплату устарел"                                                                                                                      	|	IntegrationError_TransactionDeclinedByAcquirer	|	EnumTransactionStatus_TransactionStatusFailed	|
//	|	JusanResponseCodeTokenize_TokenizeTransactionHandleError    	|	"411" 	|	"Ошибка при обработке транзакции"                                                                                                             	|	IntegrationError_TransactionDeclinedByAcquirer	|	EnumTransactionStatus_TransactionStatusFailed	|

var SliceJusanResponseCodeTokenizeRefs *sliceJusanResponseCodeTokenizeRefs

type sliceJusanResponseCodeTokenizeRefs struct{}

func (*sliceJusanResponseCodeTokenizeRefs) Code(slice ...JusanResponseCodeTokenize) []string {
	var result []string
	for _, val := range slice {
		result = append(result, val.Code())
	}

	return result
}

func (*sliceJusanResponseCodeTokenizeRefs) Description(slice ...JusanResponseCodeTokenize) []string {
	var result []string
	for _, val := range slice {
		result = append(result, val.Description())
	}

	return result
}

func (*sliceJusanResponseCodeTokenizeRefs) IntegrationError(slice ...JusanResponseCodeTokenize) []IntegrationError {
	var result []IntegrationError
	for _, val := range slice {
		result = append(result, val.IntegrationError())
	}

	return result
}

func (*sliceJusanResponseCodeTokenizeRefs) TransactionStatus(slice ...JusanResponseCodeTokenize) []EnumTransactionStatus {
	var result []EnumTransactionStatus
	for _, val := range slice {
		result = append(result, val.TransactionStatus())
	}

	return result
}
