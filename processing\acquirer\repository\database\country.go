package database

import (
	"context"
	"errors"

	"gorm.io/gorm"

	"git.local/sensitive/innerpb/processing/goerr"
	"git.local/sensitive/pkg/middlewares"
	"git.local/sensitive/processing/acquirer/model"
	"git.local/sensitive/sdk/dog"
)

type CountryDB struct {
	db *gorm.DB
}

func NewCountryDB(db *gorm.DB) Countrier {
	return &CountryDB{
		db: db,
	}
}

func (c *CountryDB) Create(ctx context.Context, country *model.Country) (err error) {
	ctx, span := dog.CreateSpan(ctx, "CountryDB_Create")
	defer span.End()

	if country == nil {
		return goerr.ErrParseErrorBody.WithCtx(ctx)
	}

	err = c.db.WithContext(ctx).Create(&country).Error
	if err != nil {
		return goerr.ErrDbUnexpected.WithErr(err).WithCtx(ctx)
	}

	return nil
}

func (c *CountryDB) GetAll(
	ctx context.Context, pagination *middlewares.PaginationInfo) (_ []*model.Country,
	err error) {
	ctx, span := dog.CreateSpan(ctx, "CountryDB_GetAll")
	defer span.End()

	result := make([]*model.Country, 0)

	request := c.db.WithContext(ctx).
		Model(&model.Country{})

	if pagination.Pagination {
		request = request.Offset((pagination.Page - 1) * pagination.PerPage).Limit(pagination.PerPage)
	}

	if err := request.Find(
		&result).Error; err != nil {
		return nil, goerr.ErrDbUnexpected.WithErr(err).WithCtx(ctx)
	}

	return result, nil
}

func (c *CountryDB) Update(ctx context.Context, id uint64, data *model.Country) (err error) {
	ctx, span := dog.CreateSpan(ctx, "CountryDB_Update")
	defer span.End()

	if data == nil {
		return goerr.ErrParseErrorBody.WithCtx(ctx)
	}

	err = c.db.WithContext(ctx).
		Model(&model.Country{}).
		Where("id = ?", id).
		Updates(model.Country{
			Name: data.Name,
		}).
		First(&model.Country{}).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return goerr.ErrCountryNotFound.WithCtx(ctx)
		}

		return goerr.ErrDbUnexpected.WithErr(err).WithCtx(ctx)
	}

	return nil
}

func (c *CountryDB) GetByName(
	ctx context.Context, name string, pagination *middlewares.PaginationInfo,
) (_ []*model.CountryBasic, err error) {
	ctx, span := dog.CreateSpan(ctx, "CountryDB_GetByName")
	defer span.End()

	country := make([]*model.CountryBasic, 0)

	request := c.db.WithContext(ctx).Where("name ILIKE  ?", "%"+name+"%")

	if err := request.Error; err != nil {
		return nil, goerr.ErrDbUnexpected.WithErr(err).WithCtx(ctx)
	}

	if pagination == nil || !pagination.Pagination {
		if err = request.Order("id ASC").Find(&country).
			Error; err != nil {
			return nil, goerr.ErrDbUnexpected.WithErr(err).WithCtx(ctx)
		}

		return country, nil
	}

	if err = request.WithContext(ctx).Model(&country).
		Count(&pagination.Total).Error; err != nil {
		return nil, goerr.ErrDbUnexpected.WithErr(err).WithCtx(ctx)
	}

	if err = request.
		Offset((pagination.Page - 1) * pagination.PerPage).
		Limit(pagination.PerPage).
		Find(&country).Order("id ASC").Error; err != nil {
		return nil, goerr.ErrDbUnexpected.WithErr(err).WithCtx(ctx)
	}

	return country, err
}

func (c *CountryDB) Delete(ctx context.Context, id uint64) (err error) {
	ctx, span := dog.CreateSpan(ctx, "CountryDB_Delete")
	defer span.End()

	err = c.db.WithContext(ctx).
		Where("id = ?", id).
		First(&model.Country{}).
		Delete(&model.Country{}, id).
		Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return goerr.ErrCountryNotFound
		}

		return goerr.ErrDbUnexpected.WithErr(err).WithCtx(ctx)
	}

	return nil
}

func (c *CountryDB) GetByID(
	ctx context.Context,
	id uint64) (_ model.Country, err error) {
	ctx, span := dog.CreateSpan(ctx, "CountryDB_GetByID")
	defer span.End()

	country := new(model.Country)

	if err := c.db.WithContext(ctx).Where("id = ?", id).First(country).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return model.Country{}, goerr.ErrCountryNotFound
		}

		return model.Country{}, err
	}

	return *country, nil
}
