package cntx

import (
	"context"
	"testing"

	"github.com/stretchr/testify/require"
)

func TestBegin(t *testing.T) {
	tests := []string{
		"test1",
		"test2",
		"test3",
	}

	labelFunc = func(ctx context.Context, name string) string {
		return name
	}

	for _, tt := range tests {
		require.Equal(t, tt, Begin(context.TODO(), tt))
	}
}

func TestLabelFunc(t *testing.T) {
	tests := []string{
		"test1",
		"test2",
		"test3",
	}

	labelFunc = func(ctx context.Context, name string) string {
		return name
	}

	for _, tt := range tests {
		require.Equal(t, tt, LabelFunc(context.TODO(), tt))
	}
}

func TestLabelFunc2(t *testing.T) {
	tests := []string{
		"test1",
		"test2",
		"test3",
	}

	labelFunc = func(ctx context.Context, name string) string {
		return name
	}

	for _, tt := range tests {
		require.Equal(t, tt, LabelFunc(context.TODO(), tt))
	}
}
