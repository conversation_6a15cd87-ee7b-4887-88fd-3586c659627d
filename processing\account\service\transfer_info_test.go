package service

import (
	"context"
	"errors"
	"git.local/sensitive/processing/account/repository/databasemocks"
	"github.com/golang/mock/gomock"
	"testing"

	"git.local/sensitive/processing/account/model"
	"git.local/sensitive/processing/account/schema"
	"github.com/stretchr/testify/require"
)

func TestGetTransferByID(t *testing.T) {
	type getByIDOp struct {
		input     uint64
		output    *model.Transfer
		outputErr error
	}

	tests := []struct {
		name    string
		req     uint64
		want    *model.Transfer
		wantErr error
		getByID getByIDOp
	}{
		{
			name:    "error",
			req:     23,
			want:    nil,
			wantErr: errors.New("error in GetTransfer operation"),
			getByID: getByIDOp{
				input:     23,
				outputErr: errors.New("error in GetTransfer operation"),
				output:    nil,
			},
		},
		{
			name: "success",
			req:  23,
			want: &model.Transfer{
				ID:         23,
				AccountID:  32,
				AcquirerID: 33,
				Amount:     300,
				MerchantID: 44,
			},
			wantErr: nil,
			getByID: getByIDOp{
				input:     23,
				outputErr: nil,
				output: &model.Transfer{
					ID:         23,
					AccountID:  32,
					AcquirerID: 33,
					Amount:     300,
					MerchantID: 44,
				},
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctrl := gomock.NewController(t)
			defer ctrl.Finish()

			transferDBMock := databasemocks.NewMockTransferer(ctrl)

			transferDBMock.EXPECT().GetByID(
				gomock.Any(),
				tt.getByID.input,
			).Return(tt.getByID.output, tt.getByID.outputErr).Times(1)

			s := NewTransferInfoService(transferDBMock)

			res, err := s.GetTransferById(context.Background(), tt.req)
			if tt.wantErr != nil {
				require.Error(t, err)
				require.Equal(t, tt.wantErr, err)
			} else {
				require.NoError(t, err)
			}

			require.Equal(t, tt.want, res)
		})
	}
}

func TestGetTransferByFilters(t *testing.T) {
	type getByFiltersOp struct {
		input     schema.TransferFilters
		output    []model.Transfer
		outputErr error
	}

	tests := []struct {
		name         string
		req          schema.TransferFilters
		want         []model.Transfer
		wantErr      error
		getByFilters getByFiltersOp
	}{
		{
			name: "error when getting by filters",
			req: schema.TransferFilters{
				MerchantID: "23",
				ProjectID:  "10",
				StatusID:   "3",
			},
			want:    nil,
			wantErr: errors.New("some error"),
			getByFilters: getByFiltersOp{
				input: schema.TransferFilters{
					MerchantID: "23",
					ProjectID:  "10",
					StatusID:   "3",
				},
				output:    nil,
				outputErr: errors.New("some error"),
			},
		},
		{
			name: "success",
			req: schema.TransferFilters{
				MerchantID: "23",
				ProjectID:  "10",
				StatusID:   "3",
			},
			want: []model.Transfer{
				{
					ID:         1,
					MerchantID: 23,
					ProjectID:  10,
					StatusID:   3,
				},
				{
					ID:         2,
					MerchantID: 23,
					ProjectID:  10,
					StatusID:   3,
				},
			},
			wantErr: nil,
			getByFilters: getByFiltersOp{
				input: schema.TransferFilters{
					MerchantID: "23",
					ProjectID:  "10",
					StatusID:   "3",
				},
				output: []model.Transfer{
					{
						ID:         1,
						MerchantID: 23,
						ProjectID:  10,
						StatusID:   3,
					},
					{
						ID:         2,
						MerchantID: 23,
						ProjectID:  10,
						StatusID:   3,
					},
				},
				outputErr: nil,
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctrl := gomock.NewController(t)
			defer ctrl.Finish()

			transferDBMock := databasemocks.NewMockTransferer(ctrl)

			transferDBMock.EXPECT().GetByFilters(
				gomock.Any(),
				tt.getByFilters.input,
			).Return(tt.getByFilters.output, tt.getByFilters.outputErr).Times(1)

			s := NewTransferInfoService(transferDBMock)

			res, err := s.GetTransfersByFilters(context.Background(), tt.req)
			if tt.wantErr != nil {
				require.Error(t, err)
				require.Equal(t, tt.wantErr, err)
			} else {
				require.NoError(t, err)
			}

			require.Equal(t, tt.want, res)
		})
	}
}

func TestGetTransfersByMerchantID(t *testing.T) {
	type getByMerchantIDOp struct {
		input     uint64
		output    []model.Transfer
		outputErr error
	}

	tests := []struct {
		name         string
		reqMerchID   uint64
		want         []model.Transfer
		wantErr      error
		getByMerchID getByMerchantIDOp
	}{
		{
			name:       "error when getting by merch id",
			reqMerchID: 2,
			wantErr:    errors.New("some error"),
			want:       nil,
			getByMerchID: getByMerchantIDOp{
				input:     2,
				outputErr: errors.New("some error"),
				output:    nil,
			},
		},
		{
			name:       "success",
			reqMerchID: 2,
			wantErr:    nil,
			want: []model.Transfer{
				{
					ID:         1,
					MerchantID: 2,
					ProjectID:  10,
					StatusID:   3,
				},
				{
					ID:         2,
					MerchantID: 2,
					ProjectID:  10,
					StatusID:   3,
				},
			},
			getByMerchID: getByMerchantIDOp{
				input:     2,
				outputErr: nil,
				output: []model.Transfer{
					{
						ID:         1,
						MerchantID: 2,
						ProjectID:  10,
						StatusID:   3,
					},
					{
						ID:         2,
						MerchantID: 2,
						ProjectID:  10,
						StatusID:   3,
					},
				},
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctrl := gomock.NewController(t)
			defer ctrl.Finish()

			transferDBMock := databasemocks.NewMockTransferer(ctrl)

			transferDBMock.EXPECT().GetByMerchantID(
				gomock.Any(),
				tt.getByMerchID.input,
			).Return(tt.getByMerchID.output, tt.getByMerchID.outputErr).Times(1)

			s := NewTransferInfoService(transferDBMock)

			res, err := s.GetTransfersByMerchantId(context.Background(), tt.reqMerchID)
			if tt.wantErr != nil {
				require.Error(t, err)
				require.Equal(t, tt.wantErr, err)
			} else {
				require.NoError(t, err)
			}

			require.Equal(t, tt.want, res)
		})
	}
}

func TestGetTransfersByProjectID(t *testing.T) {
	type getByProjectIDOp struct {
		input     uint64
		output    []model.Transfer
		outputErr error
	}

	tests := []struct {
		name           string
		req            uint64
		want           []model.Transfer
		wantErr        error
		getByProjectID getByProjectIDOp
	}{
		{
			name: "error when getting by project id",
			req:  23,
			want: nil,
			getByProjectID: getByProjectIDOp{
				input:     23,
				outputErr: errors.New("some error"),
				output:    nil,
			},
			wantErr: errors.New("some error"),
		},
		{
			name: "success",
			req:  23,
			want: []model.Transfer{
				{
					ID:         1,
					MerchantID: 23,
					ProjectID:  23,
					StatusID:   3,
				},
				{
					ID:         2,
					MerchantID: 23,
					ProjectID:  23,
					StatusID:   3,
				},
			},
			getByProjectID: getByProjectIDOp{
				input:     23,
				outputErr: nil,
				output: []model.Transfer{
					{
						ID:         1,
						MerchantID: 23,
						ProjectID:  23,
						StatusID:   3,
					},
					{
						ID:         2,
						MerchantID: 23,
						ProjectID:  23,
						StatusID:   3,
					},
				},
			},
			wantErr: nil,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctrl := gomock.NewController(t)
			defer ctrl.Finish()

			transferDBMock := databasemocks.NewMockTransferer(ctrl)

			transferDBMock.EXPECT().GetByProjectID(
				gomock.Any(),
				tt.getByProjectID.input,
			).Return(tt.getByProjectID.output, tt.getByProjectID.outputErr).Times(1)

			s := NewTransferInfoService(transferDBMock)

			res, err := s.GetTransfersByProjectId(context.Background(), tt.req)
			if tt.wantErr != nil {
				require.Error(t, err)
				require.Equal(t, tt.wantErr, err)
			} else {
				require.NoError(t, err)
			}

			require.Equal(t, tt.want, res)
		})
	}
}
