// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.5
// 	protoc        v5.29.3
// source: inner/processing/grpc/jusan_statuser.proto

package grpc

import (
	_ "git.local/sensitive/mvp/pb"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	descriptorpb "google.golang.org/protobuf/types/descriptorpb"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type JusanResponseCodeStatuser int32

const (
	JusanResponseCodeStatuser_StatuserServiceUnavailable      JusanResponseCodeStatuser = 0
	JusanResponseCodeStatuser_StatuserIncorrectFieldOrder     JusanResponseCodeStatuser = 1
	JusanResponseCodeStatuser_StatuserIncorrectAmount         JusanResponseCodeStatuser = 2
	JusanResponseCodeStatuser_StatuserIncorrectCurrency       JusanResponseCodeStatuser = 3
	JusanResponseCodeStatuser_StatuserNoSuchCard              JusanResponseCodeStatuser = 4
	JusanResponseCodeStatuser_StatuserDbUnavailable           JusanResponseCodeStatuser = 5
	JusanResponseCodeStatuser_StatuserForbiddenForMerchant    JusanResponseCodeStatuser = 6
	JusanResponseCodeStatuser_StatuserForbiddenMerchantByLaw  JusanResponseCodeStatuser = 7
	JusanResponseCodeStatuser_StatuserRequestAlreadeCompleted JusanResponseCodeStatuser = 8
	JusanResponseCodeStatuser_StatuserIncorrectCardExpDate    JusanResponseCodeStatuser = 9
	JusanResponseCodeStatuser_StatuserIncorrectFieldTerminal  JusanResponseCodeStatuser = 10
	JusanResponseCodeStatuser_StatuserIncorrectSign           JusanResponseCodeStatuser = 11
	JusanResponseCodeStatuser_StatuserCurrencyNotFound        JusanResponseCodeStatuser = 12
	JusanResponseCodeStatuser_StatuserLimitExceeded           JusanResponseCodeStatuser = 13
	JusanResponseCodeStatuser_StatuserEmptyField              JusanResponseCodeStatuser = 14
	JusanResponseCodeStatuser_StatuserSizeLessSymbol          JusanResponseCodeStatuser = 15
	JusanResponseCodeStatuser_StatuserSizeMoreSymbol          JusanResponseCodeStatuser = 16
	JusanResponseCodeStatuser_StatuserInvalidValue            JusanResponseCodeStatuser = 17
	JusanResponseCodeStatuser_StatuserMPIError3DS             JusanResponseCodeStatuser = 18
	JusanResponseCodeStatuser_StatuserInvalidCardType         JusanResponseCodeStatuser = 19
	JusanResponseCodeStatuser_StatuserPaymentNotFound         JusanResponseCodeStatuser = 20
	JusanResponseCodeStatuser_StatuserClientKeyNotFound       JusanResponseCodeStatuser = 21
	JusanResponseCodeStatuser_StatuserForbiddenTerminal       JusanResponseCodeStatuser = 22
	JusanResponseCodeStatuser_StatuserTokenNotFound           JusanResponseCodeStatuser = 23
	JusanResponseCodeStatuser_StatuserIncorrectBlockAmount    JusanResponseCodeStatuser = 24
	JusanResponseCodeStatuser_StatuserUnknownError            JusanResponseCodeStatuser = 25
	JusanResponseCodeStatuser_StatuserUnavailableService      JusanResponseCodeStatuser = 26
	JusanResponseCodeStatuser_StatuserSumIncorrect            JusanResponseCodeStatuser = 27
	JusanResponseCodeStatuser_StatuserUnavailableDb           JusanResponseCodeStatuser = 28
	JusanResponseCodeStatuser_StatuserIncorrectMerchant       JusanResponseCodeStatuser = 29
	JusanResponseCodeStatuser_StatuserNotFoundMerchant        JusanResponseCodeStatuser = 30
	JusanResponseCodeStatuser_StatuserOrderNotFound           JusanResponseCodeStatuser = 31
	JusanResponseCodeStatuser_StatuserInvalidSign             JusanResponseCodeStatuser = 32
	JusanResponseCodeStatuser_StatuserIncorrectRefundAmount   JusanResponseCodeStatuser = 33
	JusanResponseCodeStatuser_StatuserIncorrectStatus         JusanResponseCodeStatuser = 34
	JusanResponseCodeStatuser_StatuserIncorrectValue          JusanResponseCodeStatuser = 35
	JusanResponseCodeStatuser_StatuserTerminalStatusIncorrect JusanResponseCodeStatuser = 36
	JusanResponseCodeStatuser_StatuserTerminalForbidden       JusanResponseCodeStatuser = 37
	JusanResponseCodeStatuser_StatuserDuplicateDescription    JusanResponseCodeStatuser = 38
	JusanResponseCodeStatuser_StatuserRefundHandleError       JusanResponseCodeStatuser = 39
	JusanResponseCodeStatuser_StatuserPaymentError            JusanResponseCodeStatuser = 40
	JusanResponseCodeStatuser_StatuserPaymentExpired          JusanResponseCodeStatuser = 41
)

// Enum value maps for JusanResponseCodeStatuser.
var (
	JusanResponseCodeStatuser_name = map[int32]string{
		0:  "StatuserServiceUnavailable",
		1:  "StatuserIncorrectFieldOrder",
		2:  "StatuserIncorrectAmount",
		3:  "StatuserIncorrectCurrency",
		4:  "StatuserNoSuchCard",
		5:  "StatuserDbUnavailable",
		6:  "StatuserForbiddenForMerchant",
		7:  "StatuserForbiddenMerchantByLaw",
		8:  "StatuserRequestAlreadeCompleted",
		9:  "StatuserIncorrectCardExpDate",
		10: "StatuserIncorrectFieldTerminal",
		11: "StatuserIncorrectSign",
		12: "StatuserCurrencyNotFound",
		13: "StatuserLimitExceeded",
		14: "StatuserEmptyField",
		15: "StatuserSizeLessSymbol",
		16: "StatuserSizeMoreSymbol",
		17: "StatuserInvalidValue",
		18: "StatuserMPIError3DS",
		19: "StatuserInvalidCardType",
		20: "StatuserPaymentNotFound",
		21: "StatuserClientKeyNotFound",
		22: "StatuserForbiddenTerminal",
		23: "StatuserTokenNotFound",
		24: "StatuserIncorrectBlockAmount",
		25: "StatuserUnknownError",
		26: "StatuserUnavailableService",
		27: "StatuserSumIncorrect",
		28: "StatuserUnavailableDb",
		29: "StatuserIncorrectMerchant",
		30: "StatuserNotFoundMerchant",
		31: "StatuserOrderNotFound",
		32: "StatuserInvalidSign",
		33: "StatuserIncorrectRefundAmount",
		34: "StatuserIncorrectStatus",
		35: "StatuserIncorrectValue",
		36: "StatuserTerminalStatusIncorrect",
		37: "StatuserTerminalForbidden",
		38: "StatuserDuplicateDescription",
		39: "StatuserRefundHandleError",
		40: "StatuserPaymentError",
		41: "StatuserPaymentExpired",
	}
	JusanResponseCodeStatuser_value = map[string]int32{
		"StatuserServiceUnavailable":      0,
		"StatuserIncorrectFieldOrder":     1,
		"StatuserIncorrectAmount":         2,
		"StatuserIncorrectCurrency":       3,
		"StatuserNoSuchCard":              4,
		"StatuserDbUnavailable":           5,
		"StatuserForbiddenForMerchant":    6,
		"StatuserForbiddenMerchantByLaw":  7,
		"StatuserRequestAlreadeCompleted": 8,
		"StatuserIncorrectCardExpDate":    9,
		"StatuserIncorrectFieldTerminal":  10,
		"StatuserIncorrectSign":           11,
		"StatuserCurrencyNotFound":        12,
		"StatuserLimitExceeded":           13,
		"StatuserEmptyField":              14,
		"StatuserSizeLessSymbol":          15,
		"StatuserSizeMoreSymbol":          16,
		"StatuserInvalidValue":            17,
		"StatuserMPIError3DS":             18,
		"StatuserInvalidCardType":         19,
		"StatuserPaymentNotFound":         20,
		"StatuserClientKeyNotFound":       21,
		"StatuserForbiddenTerminal":       22,
		"StatuserTokenNotFound":           23,
		"StatuserIncorrectBlockAmount":    24,
		"StatuserUnknownError":            25,
		"StatuserUnavailableService":      26,
		"StatuserSumIncorrect":            27,
		"StatuserUnavailableDb":           28,
		"StatuserIncorrectMerchant":       29,
		"StatuserNotFoundMerchant":        30,
		"StatuserOrderNotFound":           31,
		"StatuserInvalidSign":             32,
		"StatuserIncorrectRefundAmount":   33,
		"StatuserIncorrectStatus":         34,
		"StatuserIncorrectValue":          35,
		"StatuserTerminalStatusIncorrect": 36,
		"StatuserTerminalForbidden":       37,
		"StatuserDuplicateDescription":    38,
		"StatuserRefundHandleError":       39,
		"StatuserPaymentError":            40,
		"StatuserPaymentExpired":          41,
	}
)

func (x JusanResponseCodeStatuser) Enum() *JusanResponseCodeStatuser {
	p := new(JusanResponseCodeStatuser)
	*p = x
	return p
}

func (x JusanResponseCodeStatuser) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (JusanResponseCodeStatuser) Descriptor() protoreflect.EnumDescriptor {
	return file_inner_processing_grpc_jusan_statuser_proto_enumTypes[0].Descriptor()
}

func (JusanResponseCodeStatuser) Type() protoreflect.EnumType {
	return &file_inner_processing_grpc_jusan_statuser_proto_enumTypes[0]
}

func (x JusanResponseCodeStatuser) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use JusanResponseCodeStatuser.Descriptor instead.
func (JusanResponseCodeStatuser) EnumDescriptor() ([]byte, []int) {
	return file_inner_processing_grpc_jusan_statuser_proto_rawDescGZIP(), []int{0}
}

type JusanResponseCodeStatuserRef struct {
	state             protoimpl.MessageState `protogen:"open.v1"`
	Code              *string                `protobuf:"bytes,1,opt,name=code" json:"code,omitempty"`
	Message           *string                `protobuf:"bytes,2,opt,name=message" json:"message,omitempty"`
	TransactionStatus *EnumTransactionStatus `protobuf:"varint,3,opt,name=transaction_status,json=transactionStatus,enum=processing.transaction.transaction_status.EnumTransactionStatus" json:"transaction_status,omitempty"`
	IntegrationError  *IntegrationError      `protobuf:"varint,4,opt,name=integration_error,json=integrationError,enum=processing.integration.integration.IntegrationError" json:"integration_error,omitempty"`
	unknownFields     protoimpl.UnknownFields
	sizeCache         protoimpl.SizeCache
}

func (x *JusanResponseCodeStatuserRef) Reset() {
	*x = JusanResponseCodeStatuserRef{}
	mi := &file_inner_processing_grpc_jusan_statuser_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *JusanResponseCodeStatuserRef) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*JusanResponseCodeStatuserRef) ProtoMessage() {}

func (x *JusanResponseCodeStatuserRef) ProtoReflect() protoreflect.Message {
	mi := &file_inner_processing_grpc_jusan_statuser_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use JusanResponseCodeStatuserRef.ProtoReflect.Descriptor instead.
func (*JusanResponseCodeStatuserRef) Descriptor() ([]byte, []int) {
	return file_inner_processing_grpc_jusan_statuser_proto_rawDescGZIP(), []int{0}
}

func (x *JusanResponseCodeStatuserRef) GetCode() string {
	if x != nil && x.Code != nil {
		return *x.Code
	}
	return ""
}

func (x *JusanResponseCodeStatuserRef) GetMessage() string {
	if x != nil && x.Message != nil {
		return *x.Message
	}
	return ""
}

func (x *JusanResponseCodeStatuserRef) GetTransactionStatus() EnumTransactionStatus {
	if x != nil && x.TransactionStatus != nil {
		return *x.TransactionStatus
	}
	return EnumTransactionStatus_Unknown
}

func (x *JusanResponseCodeStatuserRef) GetIntegrationError() IntegrationError {
	if x != nil && x.IntegrationError != nil {
		return *x.IntegrationError
	}
	return IntegrationError_None
}

var file_inner_processing_grpc_jusan_statuser_proto_extTypes = []protoimpl.ExtensionInfo{
	{
		ExtendedType:  (*descriptorpb.EnumValueOptions)(nil),
		ExtensionType: (*JusanResponseCodeStatuserRef)(nil),
		Field:         100115,
		Name:          "processing.jusan_statuser.jusan_statuser.jusan_response_code_statuser_value",
		Tag:           "bytes,100115,opt,name=jusan_response_code_statuser_value",
		Filename:      "inner/processing/grpc/jusan_statuser.proto",
	},
	{
		ExtendedType:  (*descriptorpb.EnumOptions)(nil),
		ExtensionType: (*JusanResponseCodeStatuserRef)(nil),
		Field:         100116,
		Name:          "processing.jusan_statuser.jusan_statuser.default_jusan_response_code_statuser_value",
		Tag:           "bytes,100116,opt,name=default_jusan_response_code_statuser_value",
		Filename:      "inner/processing/grpc/jusan_statuser.proto",
	},
}

// Extension fields to descriptorpb.EnumValueOptions.
var (
	// optional processing.jusan_statuser.jusan_statuser.JusanResponseCodeStatuserRef jusan_response_code_statuser_value = 100115;
	E_JusanResponseCodeStatuserValue = &file_inner_processing_grpc_jusan_statuser_proto_extTypes[0]
)

// Extension fields to descriptorpb.EnumOptions.
var (
	// optional processing.jusan_statuser.jusan_statuser.JusanResponseCodeStatuserRef default_jusan_response_code_statuser_value = 100116;
	E_DefaultJusanResponseCodeStatuserValue = &file_inner_processing_grpc_jusan_statuser_proto_extTypes[1]
)

var File_inner_processing_grpc_jusan_statuser_proto protoreflect.FileDescriptor

var file_inner_processing_grpc_jusan_statuser_proto_rawDesc = string([]byte{
	0x0a, 0x2a, 0x69, 0x6e, 0x6e, 0x65, 0x72, 0x2f, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69,
	0x6e, 0x67, 0x2f, 0x67, 0x72, 0x70, 0x63, 0x2f, 0x6a, 0x75, 0x73, 0x61, 0x6e, 0x5f, 0x73, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x65, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x28, 0x70, 0x72,
	0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x2e, 0x6a, 0x75, 0x73, 0x61, 0x6e, 0x5f, 0x73,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x65, 0x72, 0x2e, 0x6a, 0x75, 0x73, 0x61, 0x6e, 0x5f, 0x73, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x65, 0x72, 0x1a, 0x2e, 0x69, 0x6e, 0x6e, 0x65, 0x72, 0x2f, 0x70, 0x72,
	0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x2f, 0x67, 0x72, 0x70, 0x63, 0x2f, 0x74, 0x72,
	0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x27, 0x69, 0x6e, 0x6e, 0x65, 0x72, 0x2f, 0x70, 0x72,
	0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x2f, 0x67, 0x72, 0x70, 0x63, 0x2f, 0x69, 0x6e,
	0x74, 0x65, 0x67, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a,
	0x14, 0x6d, 0x76, 0x70, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f, 0x72, 0x65, 0x66, 0x73, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x14, 0x6d, 0x76, 0x70, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x2f, 0x65, 0x6e, 0x75, 0x6d, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x20, 0x67, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x64, 0x65, 0x73,
	0x63, 0x72, 0x69, 0x70, 0x74, 0x6f, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0xa0, 0x02,
	0x0a, 0x1c, 0x4a, 0x75, 0x73, 0x61, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x43,
	0x6f, 0x64, 0x65, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x65, 0x72, 0x52, 0x65, 0x66, 0x12, 0x12,
	0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x63, 0x6f,
	0x64, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x6f, 0x0a, 0x12,
	0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x73, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x40, 0x2e, 0x70, 0x72, 0x6f, 0x63, 0x65,
	0x73, 0x73, 0x69, 0x6e, 0x67, 0x2e, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f,
	0x6e, 0x2e, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x73, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x2e, 0x45, 0x6e, 0x75, 0x6d, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63,
	0x74, 0x69, 0x6f, 0x6e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x11, 0x74, 0x72, 0x61, 0x6e,
	0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x61, 0x0a,
	0x11, 0x69, 0x6e, 0x74, 0x65, 0x67, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x65, 0x72, 0x72,
	0x6f, 0x72, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x34, 0x2e, 0x70, 0x72, 0x6f, 0x63, 0x65,
	0x73, 0x73, 0x69, 0x6e, 0x67, 0x2e, 0x69, 0x6e, 0x74, 0x65, 0x67, 0x72, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x2e, 0x69, 0x6e, 0x74, 0x65, 0x67, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x49, 0x6e,
	0x74, 0x65, 0x67, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x52, 0x10,
	0x69, 0x6e, 0x74, 0x65, 0x67, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x45, 0x72, 0x72, 0x6f, 0x72,
	0x2a, 0x91, 0x27, 0x0a, 0x19, 0x4a, 0x75, 0x73, 0x61, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x43, 0x6f, 0x64, 0x65, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x65, 0x72, 0x12, 0x89,
	0x01, 0x0a, 0x1a, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x65, 0x72, 0x53, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x55, 0x6e, 0x61, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x6c, 0x65, 0x10, 0x00, 0x1a,
	0x69, 0x9a, 0xf1, 0x30, 0x5d, 0x0a, 0x02, 0x31, 0x31, 0x12, 0x53, 0xd0, 0xa1, 0xd0, 0xb5, 0xd1,
	0x80, 0xd0, 0xb2, 0xd0, 0xb8, 0xd1, 0x81, 0x20, 0xd0, 0xb2, 0xd1, 0x80, 0xd0, 0xb5, 0xd0, 0xbc,
	0xd0, 0xb5, 0xd0, 0xbd, 0xd0, 0xbd, 0xd0, 0xbe, 0x20, 0xd0, 0xbd, 0xd0, 0xb5, 0xd0, 0xb4, 0xd0,
	0xbe, 0xd1, 0x81, 0xd1, 0x82, 0xd1, 0x83, 0xd0, 0xbf, 0xd0, 0xb5, 0xd0, 0xbd, 0x2c, 0x20, 0xd0,
	0xbf, 0xd0, 0xbe, 0xd0, 0xbf, 0xd1, 0x80, 0xd0, 0xbe, 0xd0, 0xb1, 0xd1, 0x83, 0xd0, 0xb9, 0xd1,
	0x82, 0xd0, 0xb5, 0x20, 0xd0, 0xbf, 0xd0, 0xbe, 0xd0, 0xb7, 0xd0, 0xb6, 0xd0, 0xb5, 0x18, 0x05,
	0x20, 0x6c, 0xca, 0xa1, 0xec, 0x8e, 0x02, 0x02, 0x31, 0x31, 0x12, 0x73, 0x0a, 0x1b, 0x53, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x65, 0x72, 0x49, 0x6e, 0x63, 0x6f, 0x72, 0x72, 0x65, 0x63, 0x74, 0x46,
	0x69, 0x65, 0x6c, 0x64, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x10, 0x01, 0x1a, 0x52, 0x9a, 0xf1, 0x30,
	0x46, 0x0a, 0x02, 0x31, 0x32, 0x12, 0x3c, 0xd0, 0x9d, 0xd0, 0xb5, 0xd0, 0xbf, 0xd1, 0x80, 0xd0,
	0xb0, 0xd0, 0xb2, 0xd0, 0xb8, 0xd0, 0xbb, 0xd1, 0x8c, 0xd0, 0xbd, 0xd0, 0xbe, 0xd0, 0xb5, 0x20,
	0xd0, 0xb7, 0xd0, 0xbd, 0xd0, 0xb0, 0xd1, 0x87, 0xd0, 0xb5, 0xd0, 0xbd, 0xd0, 0xb8, 0xd0, 0xb5,
	0x20, 0xd0, 0xb2, 0x20, 0xd0, 0xbf, 0xd0, 0xbe, 0xd0, 0xbb, 0xd0, 0xb5, 0x20, 0x4f, 0x52, 0x44,
	0x45, 0x52, 0x3a, 0x18, 0x05, 0x20, 0x6a, 0xca, 0xa1, 0xec, 0x8e, 0x02, 0x02, 0x31, 0x32, 0x12,
	0x58, 0x0a, 0x17, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x65, 0x72, 0x49, 0x6e, 0x63, 0x6f, 0x72,
	0x72, 0x65, 0x63, 0x74, 0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x10, 0x02, 0x1a, 0x3b, 0x9a, 0xf1,
	0x30, 0x2f, 0x0a, 0x02, 0x31, 0x33, 0x12, 0x25, 0xd0, 0x9d, 0xd0, 0xb5, 0xd0, 0xbf, 0xd1, 0x80,
	0xd0, 0xb0, 0xd0, 0xb2, 0xd0, 0xb8, 0xd0, 0xbb, 0xd1, 0x8c, 0xd0, 0xbd, 0xd0, 0xb0, 0xd1, 0x8f,
	0x20, 0xd1, 0x81, 0xd1, 0x83, 0xd0, 0xbc, 0xd0, 0xbc, 0xd0, 0xb0, 0x3a, 0x20, 0x18, 0x05, 0x20,
	0x6a, 0xca, 0xa1, 0xec, 0x8e, 0x02, 0x02, 0x31, 0x33, 0x12, 0x5b, 0x0a, 0x19, 0x53, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x65, 0x72, 0x49, 0x6e, 0x63, 0x6f, 0x72, 0x72, 0x65, 0x63, 0x74, 0x43, 0x75,
	0x72, 0x72, 0x65, 0x6e, 0x63, 0x79, 0x10, 0x03, 0x1a, 0x3c, 0x9a, 0xf1, 0x30, 0x30, 0x0a, 0x02,
	0x31, 0x34, 0x12, 0x26, 0xd0, 0x9d, 0xd0, 0xb5, 0xd0, 0xbf, 0xd1, 0x80, 0xd0, 0xb0, 0xd0, 0xb2,
	0xd0, 0xb8, 0xd0, 0xbb, 0xd1, 0x8c, 0xd0, 0xbd, 0xd0, 0xb0, 0xd1, 0x8f, 0x20, 0xd0, 0xb2, 0xd0,
	0xb0, 0xd0, 0xbb, 0xd1, 0x8e, 0xd1, 0x82, 0xd0, 0xb0, 0x3a, 0x18, 0x05, 0x20, 0x6a, 0xca, 0xa1,
	0xec, 0x8e, 0x02, 0x02, 0x31, 0x34, 0x12, 0x3a, 0x0a, 0x12, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x65, 0x72, 0x4e, 0x6f, 0x53, 0x75, 0x63, 0x68, 0x43, 0x61, 0x72, 0x64, 0x10, 0x04, 0x1a, 0x22,
	0x9a, 0xf1, 0x30, 0x16, 0x0a, 0x02, 0x31, 0x35, 0x12, 0x0c, 0x4e, 0x6f, 0x20, 0x73, 0x75, 0x63,
	0x68, 0x20, 0x63, 0x61, 0x72, 0x64, 0x18, 0x05, 0x20, 0x66, 0xca, 0xa1, 0xec, 0x8e, 0x02, 0x02,
	0x31, 0x35, 0x12, 0x87, 0x01, 0x0a, 0x15, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x65, 0x72, 0x44,
	0x62, 0x55, 0x6e, 0x61, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x6c, 0x65, 0x10, 0x05, 0x1a, 0x6c,
	0x9a, 0xf1, 0x30, 0x60, 0x0a, 0x02, 0x31, 0x36, 0x12, 0x56, 0xd0, 0xa1, 0xd0, 0xb5, 0xd1, 0x80,
	0xd0, 0xb2, 0xd0, 0xb8, 0xd1, 0x81, 0x20, 0x44, 0x62, 0x20, 0xd0, 0xb2, 0xd1, 0x80, 0xd0, 0xb5,
	0xd0, 0xbc, 0xd0, 0xb5, 0xd0, 0xbd, 0xd0, 0xbd, 0xd0, 0xbe, 0x20, 0xd0, 0xbd, 0xd0, 0xb5, 0xd0,
	0xb4, 0xd0, 0xbe, 0xd1, 0x81, 0xd1, 0x82, 0xd1, 0x83, 0xd0, 0xbf, 0xd0, 0xb5, 0xd0, 0xbd, 0x2c,
	0x20, 0xd0, 0xbf, 0xd0, 0xbe, 0xd0, 0xbf, 0xd1, 0x80, 0xd0, 0xbe, 0xd0, 0xb1, 0xd1, 0x83, 0xd0,
	0xb9, 0xd1, 0x82, 0xd0, 0xb5, 0x20, 0xd0, 0xbf, 0xd0, 0xbe, 0xd0, 0xb7, 0xd0, 0xb6, 0xd0, 0xb5,
	0x18, 0x05, 0x20, 0x6c, 0xca, 0xa1, 0xec, 0x8e, 0x02, 0x02, 0x31, 0x36, 0x12, 0x89, 0x01, 0x0a,
	0x1c, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x65, 0x72, 0x46, 0x6f, 0x72, 0x62, 0x69, 0x64, 0x64,
	0x65, 0x6e, 0x46, 0x6f, 0x72, 0x4d, 0x65, 0x72, 0x63, 0x68, 0x61, 0x6e, 0x74, 0x10, 0x06, 0x1a,
	0x67, 0x9a, 0xf1, 0x30, 0x5a, 0x0a, 0x03, 0x31, 0x37, 0x31, 0x12, 0x4f, 0xd0, 0x9a, 0xd0, 0xbe,
	0xd0, 0xbc, 0xd0, 0xbc, 0xd0, 0xb5, 0xd1, 0x80, 0xd1, 0x81, 0xd0, 0xb0, 0xd0, 0xbd, 0xd1, 0x82,
	0xd1, 0x83, 0x20, 0xd0, 0xb7, 0xd0, 0xb0, 0xd0, 0xbf, 0xd1, 0x80, 0xd0, 0xb5, 0xd1, 0x89, 0xd0,
	0xb5, 0xd0, 0xbd, 0xd0, 0xbe, 0x20, 0xd0, 0xb2, 0xd1, 0x8b, 0xd0, 0xbf, 0xd0, 0xbe, 0xd0, 0xbb,
	0xd0, 0xbd, 0xd0, 0xb5, 0xd0, 0xbd, 0xd0, 0xb8, 0xd0, 0xb5, 0x20, 0xd0, 0xbe, 0xd0, 0xbf, 0xd0,
	0xb5, 0xd1, 0x80, 0xd0, 0xb0, 0xd1, 0x86, 0xd0, 0xb8, 0xd0, 0xb9, 0x18, 0x05, 0x20, 0x6d, 0xca,
	0xa1, 0xec, 0x8e, 0x02, 0x03, 0x31, 0x37, 0x31, 0x12, 0xcd, 0x01, 0x0a, 0x1e, 0x53, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x65, 0x72, 0x46, 0x6f, 0x72, 0x62, 0x69, 0x64, 0x64, 0x65, 0x6e, 0x4d, 0x65,
	0x72, 0x63, 0x68, 0x61, 0x6e, 0x74, 0x42, 0x79, 0x4c, 0x61, 0x77, 0x10, 0x07, 0x1a, 0xa8, 0x01,
	0x9a, 0xf1, 0x30, 0x99, 0x01, 0x0a, 0x04, 0x31, 0x37, 0x32, 0x20, 0x12, 0x8c, 0x01, 0xd0, 0x9a,
	0xd0, 0xbe, 0xd0, 0xbc, 0xd0, 0xbc, 0xd0, 0xb5, 0xd1, 0x80, 0xd1, 0x81, 0xd0, 0xb0, 0xd0, 0xbd,
	0xd1, 0x82, 0xd1, 0x83, 0x20, 0xd0, 0xb7, 0xd0, 0xb0, 0xd0, 0xbf, 0xd1, 0x80, 0xd0, 0xb5, 0xd1,
	0x89, 0xd0, 0xb5, 0xd0, 0xbd, 0xd0, 0xbe, 0x20, 0xd0, 0xbf, 0xd1, 0x80, 0xd0, 0xbe, 0xd0, 0xb2,
	0xd0, 0xb5, 0xd0, 0xb4, 0xd0, 0xb5, 0xd0, 0xbd, 0xd0, 0xb8, 0xd0, 0xb5, 0x20, 0xd0, 0xbe, 0xd0,
	0xbf, 0xd0, 0xb5, 0xd1, 0x80, 0xd0, 0xb0, 0xd1, 0x86, 0xd0, 0xb8, 0xd0, 0xb9, 0x20, 0xd0, 0xb2,
	0x20, 0xd1, 0x81, 0xd0, 0xbe, 0xd0, 0xbe, 0xd1, 0x82, 0xd0, 0xb2, 0xd0, 0xb5, 0xd1, 0x82, 0xd1,
	0x81, 0xd1, 0x82, 0xd0, 0xb2, 0xd0, 0xb8, 0xd0, 0xb8, 0x20, 0xd1, 0x81, 0x20, 0xd0, 0x97, 0xd0,
	0xb0, 0xd0, 0xba, 0xd0, 0xbe, 0xd0, 0xbd, 0xd0, 0xbe, 0xd0, 0xbc, 0x20, 0xd0, 0xbe, 0x20, 0xd0,
	0x9f, 0xd0, 0x9e, 0xd0, 0x94, 0x2f, 0xd0, 0xa4, 0xd0, 0xa2, 0x18, 0x05, 0x20, 0x6d, 0xca, 0xa1,
	0xec, 0x8e, 0x02, 0x04, 0x31, 0x37, 0x32, 0x20, 0x12, 0x63, 0x0a, 0x1f, 0x53, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x65, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x41, 0x6c, 0x72, 0x65, 0x61,
	0x64, 0x65, 0x43, 0x6f, 0x6d, 0x70, 0x6c, 0x65, 0x74, 0x65, 0x64, 0x10, 0x08, 0x1a, 0x3e, 0x9a,
	0xf1, 0x30, 0x32, 0x0a, 0x02, 0x31, 0x38, 0x12, 0x28, 0xd0, 0x97, 0xd0, 0xb0, 0xd0, 0xbf, 0xd1,
	0x80, 0xd0, 0xbe, 0xd1, 0x81, 0x20, 0xd1, 0x83, 0xd0, 0xb6, 0xd0, 0xb5, 0x20, 0xd0, 0xb2, 0xd1,
	0x8b, 0xd0, 0xbf, 0xd0, 0xbe, 0xd0, 0xbb, 0xd0, 0xbd, 0xd1, 0x8f, 0xd0, 0xbb, 0xd1, 0x81, 0xd1,
	0x8f, 0x18, 0x05, 0x20, 0x6a, 0xca, 0xa1, 0xec, 0x8e, 0x02, 0x02, 0x31, 0x38, 0x12, 0x7d, 0x0a,
	0x1c, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x65, 0x72, 0x49, 0x6e, 0x63, 0x6f, 0x72, 0x72, 0x65,
	0x63, 0x74, 0x43, 0x61, 0x72, 0x64, 0x45, 0x78, 0x70, 0x44, 0x61, 0x74, 0x65, 0x10, 0x09, 0x1a,
	0x5b, 0x9a, 0xf1, 0x30, 0x4f, 0x0a, 0x02, 0x31, 0x39, 0x12, 0x45, 0xd0, 0x9d, 0xd0, 0xb5, 0xd0,
	0xbf, 0xd1, 0x80, 0xd0, 0xb0, 0xd0, 0xb2, 0xd0, 0xb8, 0xd0, 0xbb, 0xd1, 0x8c, 0xd0, 0xbd, 0xd0,
	0xb0, 0xd1, 0x8f, 0x20, 0xd0, 0xb4, 0xd0, 0xb0, 0xd1, 0x82, 0xd0, 0xb0, 0x20, 0xd0, 0xb4, 0xd0,
	0xb5, 0xd0, 0xb9, 0xd1, 0x81, 0xd1, 0x82, 0xd0, 0xb8, 0xd1, 0x8f, 0x20, 0xd0, 0xba, 0xd0, 0xb0,
	0xd1, 0x80, 0xd1, 0x82, 0xd1, 0x8b, 0x20, 0x28, 0x4d, 0x4d, 0x2f, 0xd0, 0x93, 0xd0, 0x93, 0x29,
	0x18, 0x05, 0x20, 0x75, 0xca, 0xa1, 0xec, 0x8e, 0x02, 0x02, 0x31, 0x39, 0x12, 0x79, 0x0a, 0x1e,
	0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x65, 0x72, 0x49, 0x6e, 0x63, 0x6f, 0x72, 0x72, 0x65, 0x63,
	0x74, 0x46, 0x69, 0x65, 0x6c, 0x64, 0x54, 0x65, 0x72, 0x6d, 0x69, 0x6e, 0x61, 0x6c, 0x10, 0x0a,
	0x1a, 0x55, 0x9a, 0xf1, 0x30, 0x49, 0x0a, 0x02, 0x32, 0x30, 0x12, 0x3f, 0xd0, 0x9d, 0xd0, 0xb5,
	0xd0, 0xbf, 0xd1, 0x80, 0xd0, 0xb0, 0xd0, 0xb2, 0xd0, 0xb8, 0xd0, 0xbb, 0xd1, 0x8c, 0xd0, 0xbd,
	0xd0, 0xbe, 0xd0, 0xb5, 0x20, 0xd0, 0xb7, 0xd0, 0xbd, 0xd0, 0xb0, 0xd1, 0x87, 0xd0, 0xb5, 0xd0,
	0xbd, 0xd0, 0xb8, 0xd0, 0xb5, 0x20, 0xd0, 0xb2, 0x20, 0xd0, 0xbf, 0xd0, 0xbe, 0xd0, 0xbb, 0xd0,
	0xb5, 0x20, 0x54, 0x45, 0x52, 0x4d, 0x49, 0x4e, 0x41, 0x4c, 0x3a, 0x18, 0x05, 0x20, 0x6a, 0xca,
	0xa1, 0xec, 0x8e, 0x02, 0x02, 0x32, 0x30, 0x12, 0x59, 0x0a, 0x15, 0x53, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x65, 0x72, 0x49, 0x6e, 0x63, 0x6f, 0x72, 0x72, 0x65, 0x63, 0x74, 0x53, 0x69, 0x67, 0x6e,
	0x10, 0x0b, 0x1a, 0x3e, 0x9a, 0xf1, 0x30, 0x32, 0x0a, 0x02, 0x32, 0x31, 0x12, 0x28, 0xd0, 0x9d,
	0xd0, 0xb5, 0xd0, 0xbf, 0xd1, 0x80, 0xd0, 0xb0, 0xd0, 0xb2, 0xd0, 0xb8, 0xd0, 0xbb, 0xd1, 0x8c,
	0xd0, 0xbd, 0xd0, 0xb0, 0xd1, 0x8f, 0x20, 0xd0, 0xbf, 0xd0, 0xbe, 0xd0, 0xb4, 0xd0, 0xbf, 0xd0,
	0xb8, 0xd1, 0x81, 0xd1, 0x8c, 0x21, 0x18, 0x05, 0x20, 0x6a, 0xca, 0xa1, 0xec, 0x8e, 0x02, 0x02,
	0x32, 0x31, 0x12, 0x5b, 0x0a, 0x18, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x65, 0x72, 0x43, 0x75,
	0x72, 0x72, 0x65, 0x6e, 0x63, 0x79, 0x4e, 0x6f, 0x74, 0x46, 0x6f, 0x75, 0x6e, 0x64, 0x10, 0x0c,
	0x1a, 0x3d, 0x9a, 0xf1, 0x30, 0x31, 0x0a, 0x02, 0x32, 0x32, 0x12, 0x27, 0xd0, 0x9d, 0xd0, 0xb5,
	0x20, 0xd0, 0xbd, 0xd0, 0xb0, 0xd0, 0xb9, 0xd0, 0xb4, 0xd0, 0xb5, 0xd0, 0xbd, 0x20, 0xd0, 0xba,
	0xd1, 0x83, 0xd1, 0x80, 0xd1, 0x81, 0x20, 0xd0, 0xb2, 0xd0, 0xb0, 0xd0, 0xbb, 0xd1, 0x8e, 0xd1,
	0x82, 0xd1, 0x8b, 0x18, 0x05, 0x20, 0x6a, 0xca, 0xa1, 0xec, 0x8e, 0x02, 0x02, 0x32, 0x32, 0x12,
	0x4d, 0x0a, 0x15, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x65, 0x72, 0x4c, 0x69, 0x6d, 0x69, 0x74,
	0x45, 0x78, 0x63, 0x65, 0x65, 0x64, 0x65, 0x64, 0x10, 0x0d, 0x1a, 0x32, 0x9a, 0xf1, 0x30, 0x26,
	0x0a, 0x02, 0x32, 0x33, 0x12, 0x1c, 0xd0, 0x9f, 0xd1, 0x80, 0xd0, 0xb5, 0xd0, 0xb2, 0xd1, 0x8b,
	0xd1, 0x88, 0xd0, 0xb5, 0xd0, 0xbd, 0x20, 0xd0, 0xbb, 0xd0, 0xb8, 0xd0, 0xbc, 0xd0, 0xb8, 0xd1,
	0x82, 0x21, 0x18, 0x05, 0x20, 0x67, 0xca, 0xa1, 0xec, 0x8e, 0x02, 0x02, 0x32, 0x33, 0x12, 0x5e,
	0x0a, 0x12, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x65, 0x72, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x46,
	0x69, 0x65, 0x6c, 0x64, 0x10, 0x0e, 0x1a, 0x46, 0x9a, 0xf1, 0x30, 0x3a, 0x0a, 0x02, 0x32, 0x34,
	0x12, 0x30, 0xd0, 0x9d, 0xd0, 0xb5, 0x20, 0xd1, 0x83, 0xd0, 0xba, 0xd0, 0xb0, 0xd0, 0xb7, 0xd0,
	0xb0, 0xd0, 0xbd, 0xd0, 0xbe, 0x20, 0xd0, 0xb7, 0xd0, 0xbd, 0xd0, 0xb0, 0xd1, 0x87, 0xd0, 0xb5,
	0xd0, 0xbd, 0xd0, 0xb8, 0xd0, 0xb5, 0x20, 0xd0, 0xb2, 0x20, 0xd0, 0xbf, 0xd0, 0xbe, 0xd0, 0xbb,
	0xd0, 0xb5, 0x18, 0x05, 0x20, 0x6a, 0xca, 0xa1, 0xec, 0x8e, 0x02, 0x02, 0x32, 0x34, 0x12, 0x79,
	0x0a, 0x16, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x65, 0x72, 0x53, 0x69, 0x7a, 0x65, 0x4c, 0x65,
	0x73, 0x73, 0x53, 0x79, 0x6d, 0x62, 0x6f, 0x6c, 0x10, 0x0f, 0x1a, 0x5d, 0x9a, 0xf1, 0x30, 0x51,
	0x0a, 0x02, 0x32, 0x35, 0x12, 0x47, 0xd0, 0xa0, 0xd0, 0xb0, 0xd0, 0xb7, 0xd0, 0xbc, 0xd0, 0xb5,
	0xd1, 0x80, 0x20, 0xd0, 0xb7, 0xd0, 0xbd, 0xd0, 0xb0, 0xd1, 0x87, 0xd0, 0xb5, 0xd0, 0xbd, 0xd0,
	0xb8, 0xd1, 0x8f, 0x20, 0xd0, 0xb2, 0x20, 0xd0, 0xbf, 0xd0, 0xbe, 0xd0, 0xbb, 0xd0, 0xb5, 0x20,
	0xd0, 0xbc, 0xd0, 0xb5, 0xd0, 0xbd, 0xd0, 0xb5, 0xd0, 0xb5, 0x20, 0xd1, 0x81, 0xd0, 0xb8, 0xd0,
	0xbc, 0xd0, 0xbe, 0xd0, 0xb2, 0xd0, 0xbe, 0xd0, 0xbb, 0xd0, 0xbe, 0xd0, 0xb2, 0x18, 0x05, 0x20,
	0x6a, 0xca, 0xa1, 0xec, 0x8e, 0x02, 0x02, 0x32, 0x35, 0x12, 0x7b, 0x0a, 0x16, 0x53, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x65, 0x72, 0x53, 0x69, 0x7a, 0x65, 0x4d, 0x6f, 0x72, 0x65, 0x53, 0x79, 0x6d,
	0x62, 0x6f, 0x6c, 0x10, 0x10, 0x1a, 0x5f, 0x9a, 0xf1, 0x30, 0x53, 0x0a, 0x02, 0x32, 0x36, 0x12,
	0x49, 0xd0, 0xa0, 0xd0, 0xb0, 0xd0, 0xb7, 0xd0, 0xbc, 0xd0, 0xb5, 0xd1, 0x80, 0x20, 0xd0, 0xb7,
	0xd0, 0xbd, 0xd0, 0xb0, 0xd1, 0x87, 0xd0, 0xb5, 0xd0, 0xbd, 0xd0, 0xb8, 0xd1, 0x8f, 0x20, 0xd0,
	0xb2, 0x20, 0xd0, 0xbf, 0xd0, 0xbe, 0xd0, 0xbb, 0xd0, 0xb5, 0x20, 0xd0, 0xb1, 0xd0, 0xbe, 0xd0,
	0xbb, 0xd1, 0x8c, 0xd1, 0x88, 0xd0, 0xb5, 0x20, 0xd1, 0x81, 0xd0, 0xb8, 0xd0, 0xbc, 0xd0, 0xbe,
	0xd0, 0xb2, 0xd0, 0xbe, 0xd0, 0xbb, 0xd0, 0xbe, 0xd0, 0xb2, 0x18, 0x05, 0x20, 0x6a, 0xca, 0xa1,
	0xec, 0x8e, 0x02, 0x02, 0x32, 0x36, 0x12, 0x6c, 0x0a, 0x14, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x65, 0x72, 0x49, 0x6e, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x10, 0x11,
	0x1a, 0x52, 0x9a, 0xf1, 0x30, 0x46, 0x0a, 0x02, 0x32, 0x37, 0x12, 0x3c, 0xd0, 0x92, 0xd0, 0xb2,
	0xd0, 0xb5, 0xd0, 0xb4, 0xd0, 0xb8, 0xd1, 0x82, 0xd0, 0xb5, 0x20, 0xd0, 0xb2, 0xd0, 0xb0, 0xd0,
	0xbb, 0xd0, 0xb8, 0xd0, 0xb4, 0xd0, 0xbd, 0xd0, 0xbe, 0xd0, 0xb5, 0x20, 0xd0, 0xb7, 0xd0, 0xbd,
	0xd0, 0xb0, 0xd1, 0x87, 0xd0, 0xb5, 0xd0, 0xbd, 0xd0, 0xb8, 0xd0, 0xb5, 0x20, 0xd0, 0xb2, 0x20,
	0xd0, 0xbf, 0xd0, 0xbe, 0xd0, 0xbb, 0xd0, 0xb5, 0x18, 0x05, 0x20, 0x6a, 0xca, 0xa1, 0xec, 0x8e,
	0x02, 0x02, 0x32, 0x37, 0x12, 0x71, 0x0a, 0x13, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x65, 0x72,
	0x4d, 0x50, 0x49, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x33, 0x44, 0x53, 0x10, 0x12, 0x1a, 0x58, 0x9a,
	0xf1, 0x30, 0x4c, 0x0a, 0x02, 0x32, 0x38, 0x12, 0x42, 0xd0, 0x9e, 0xd1, 0x88, 0xd0, 0xb8, 0xd0,
	0xb1, 0xd0, 0xba, 0xd0, 0xb0, 0x20, 0x4d, 0x50, 0x49, 0x20, 0xd0, 0xbf, 0xd1, 0x80, 0xd0, 0xb8,
	0x20, 0xd0, 0xb2, 0xd1, 0x8b, 0xd0, 0xbf, 0xd0, 0xbe, 0xd0, 0xbb, 0xd0, 0xbd, 0xd0, 0xb5, 0xd0,
	0xbd, 0xd0, 0xb8, 0xd0, 0xb8, 0x20, 0xd0, 0xbf, 0xd1, 0x80, 0xd0, 0xbe, 0xd0, 0xb2, 0xd0, 0xb5,
	0xd1, 0x80, 0xd0, 0xba, 0xd0, 0xb8, 0x20, 0x33, 0x44, 0x53, 0x3a, 0x18, 0x05, 0x20, 0x65, 0xca,
	0xa1, 0xec, 0x8e, 0x02, 0x02, 0x32, 0x38, 0x12, 0x5d, 0x0a, 0x17, 0x53, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x65, 0x72, 0x49, 0x6e, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x43, 0x61, 0x72, 0x64, 0x54, 0x79,
	0x70, 0x65, 0x10, 0x13, 0x1a, 0x40, 0x9a, 0xf1, 0x30, 0x34, 0x0a, 0x02, 0x32, 0x39, 0x12, 0x2a,
	0xd0, 0x9d, 0xd0, 0xb5, 0xd0, 0xb4, 0xd0, 0xbe, 0xd0, 0xbf, 0xd1, 0x83, 0xd1, 0x81, 0xd1, 0x82,
	0xd0, 0xb8, 0xd0, 0xbc, 0xd1, 0x8b, 0xd0, 0xb9, 0x20, 0xd1, 0x82, 0xd0, 0xb8, 0xd0, 0xbf, 0x20,
	0xd0, 0xba, 0xd0, 0xb0, 0xd1, 0x80, 0xd1, 0x82, 0xd1, 0x8b, 0x18, 0x05, 0x20, 0x66, 0xca, 0xa1,
	0xec, 0x8e, 0x02, 0x02, 0x32, 0x39, 0x12, 0x5f, 0x0a, 0x17, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x65, 0x72, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x4e, 0x6f, 0x74, 0x46, 0x6f, 0x75, 0x6e,
	0x64, 0x10, 0x14, 0x1a, 0x42, 0x9a, 0xf1, 0x30, 0x36, 0x0a, 0x02, 0x33, 0x30, 0x12, 0x2c, 0xd0,
	0xa1, 0xd1, 0x87, 0xd0, 0xb5, 0xd1, 0x82, 0x20, 0xd0, 0xbd, 0xd0, 0xb0, 0x20, 0xd0, 0xbe, 0xd0,
	0xbf, 0xd0, 0xbb, 0xd0, 0xb0, 0xd1, 0x82, 0xd1, 0x83, 0x20, 0xd0, 0xbd, 0xd0, 0xb5, 0x20, 0xd0,
	0xbd, 0xd0, 0xb0, 0xd0, 0xb9, 0xd0, 0xb4, 0xd0, 0xb5, 0xd0, 0xbd, 0x18, 0x05, 0x20, 0x6a, 0xca,
	0xa1, 0xec, 0x8e, 0x02, 0x02, 0x33, 0x30, 0x12, 0x75, 0x0a, 0x19, 0x53, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x65, 0x72, 0x43, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x4b, 0x65, 0x79, 0x4e, 0x6f, 0x74, 0x46,
	0x6f, 0x75, 0x6e, 0x64, 0x10, 0x15, 0x1a, 0x56, 0x9a, 0xf1, 0x30, 0x4a, 0x0a, 0x02, 0x33, 0x31,
	0x12, 0x40, 0xd0, 0x9d, 0xd0, 0xb5, 0x20, 0xd0, 0xbf, 0xd0, 0xb5, 0xd1, 0x80, 0xd0, 0xb5, 0xd0,
	0xb4, 0xd0, 0xb0, 0xd0, 0xbd, 0x20, 0xd0, 0xba, 0xd0, 0xbb, 0xd1, 0x8e, 0xd1, 0x87, 0x20, 0xd1,
	0x83, 0xd0, 0xba, 0xd0, 0xb0, 0xd0, 0xb7, 0xd0, 0xb0, 0xd0, 0xbd, 0xd0, 0xbd, 0xd0, 0xbe, 0xd0,
	0xb3, 0xd0, 0xbe, 0x20, 0xd0, 0xba, 0xd0, 0xbb, 0xd0, 0xb8, 0xd0, 0xb5, 0xd0, 0xbd, 0xd1, 0x82,
	0xd0, 0xb0, 0x18, 0x05, 0x20, 0x6a, 0xca, 0xa1, 0xec, 0x8e, 0x02, 0x02, 0x33, 0x31, 0x12, 0x78,
	0x0a, 0x19, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x65, 0x72, 0x46, 0x6f, 0x72, 0x62, 0x69, 0x64,
	0x64, 0x65, 0x6e, 0x54, 0x65, 0x72, 0x6d, 0x69, 0x6e, 0x61, 0x6c, 0x10, 0x16, 0x1a, 0x59, 0x9a,
	0xf1, 0x30, 0x4d, 0x0a, 0x02, 0x33, 0x32, 0x12, 0x43, 0xd0, 0x94, 0xd0, 0xbb, 0xd1, 0x8f, 0x20,
	0xd1, 0x82, 0xd0, 0xb5, 0xd1, 0x80, 0xd0, 0xbc, 0xd0, 0xb8, 0xd0, 0xbd, 0xd0, 0xb0, 0xd0, 0xbb,
	0xd0, 0xb0, 0x20, 0xd0, 0xb7, 0xd0, 0xb0, 0xd0, 0xbf, 0xd1, 0x80, 0xd0, 0xb5, 0xd1, 0x89, 0xd0,
	0xb5, 0xd0, 0xbd, 0xd0, 0xb0, 0x20, 0xd1, 0x82, 0xd0, 0xbe, 0xd0, 0xba, 0xd0, 0xb5, 0xd0, 0xbd,
	0xd0, 0xb8, 0xd0, 0xb7, 0xd0, 0xb0, 0xd1, 0x86, 0xd0, 0xb8, 0xd1, 0x8f, 0x18, 0x05, 0x20, 0x6d,
	0xca, 0xa1, 0xec, 0x8e, 0x02, 0x02, 0x33, 0x32, 0x12, 0xab, 0x01, 0x0a, 0x15, 0x53, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x65, 0x72, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x4e, 0x6f, 0x74, 0x46, 0x6f, 0x75,
	0x6e, 0x64, 0x10, 0x17, 0x1a, 0x8f, 0x01, 0x9a, 0xf1, 0x30, 0x82, 0x01, 0x0a, 0x02, 0x33, 0x33,
	0x12, 0x78, 0xd0, 0x94, 0xd0, 0xbb, 0xd1, 0x8f, 0x20, 0xd0, 0xb4, 0xd0, 0xb0, 0xd0, 0xbd, 0xd0,
	0xbd, 0xd0, 0xbe, 0xd0, 0xb3, 0xd0, 0xbe, 0x20, 0xd0, 0xba, 0xd0, 0xbb, 0xd0, 0xb8, 0xd0, 0xb5,
	0xd0, 0xbd, 0xd1, 0x82, 0xd0, 0xb0, 0x20, 0xd0, 0xb2, 0x20, 0xd0, 0xb2, 0xd0, 0xb0, 0xd1, 0x88,
	0xd0, 0xb5, 0xd0, 0xb9, 0x20, 0xd0, 0xbe, 0xd1, 0x80, 0xd0, 0xb3, 0xd0, 0xb0, 0xd0, 0xbd, 0xd0,
	0xb8, 0xd0, 0xb7, 0xd0, 0xb0, 0xd1, 0x86, 0xd0, 0xb8, 0xd0, 0xb8, 0x20, 0xd0, 0xbd, 0xd0, 0xb5,
	0x20, 0xd0, 0xb7, 0xd0, 0xb0, 0xd1, 0x80, 0xd0, 0xb5, 0xd0, 0xb3, 0xd0, 0xb8, 0xd1, 0x81, 0xd1,
	0x82, 0xd1, 0x80, 0xd0, 0xb8, 0xd1, 0x80, 0xd0, 0xbe, 0xd0, 0xb2, 0xd0, 0xb0, 0xd0, 0xbd, 0x20,
	0xd1, 0x82, 0xd0, 0xbe, 0xd0, 0xba, 0xd0, 0xb5, 0xd0, 0xbd, 0x18, 0x05, 0x20, 0x66, 0xca, 0xa1,
	0xec, 0x8e, 0x02, 0x02, 0x33, 0x33, 0x12, 0x8c, 0x01, 0x0a, 0x1c, 0x53, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x65, 0x72, 0x49, 0x6e, 0x63, 0x6f, 0x72, 0x72, 0x65, 0x63, 0x74, 0x42, 0x6c, 0x6f, 0x63,
	0x6b, 0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x10, 0x18, 0x1a, 0x6a, 0x9a, 0xf1, 0x30, 0x5e, 0x0a,
	0x02, 0x33, 0x34, 0x12, 0x54, 0xd0, 0x9d, 0xd0, 0xb5, 0xd0, 0xb2, 0xd0, 0xb5, 0xd1, 0x80, 0xd0,
	0xbd, 0xd0, 0xb0, 0xd1, 0x8f, 0x20, 0xd1, 0x81, 0xd1, 0x83, 0xd0, 0xbc, 0xd0, 0xbc, 0xd0, 0xb0,
	0x20, 0xd0, 0xb1, 0xd0, 0xbb, 0xd0, 0xbe, 0xd0, 0xba, 0xd0, 0xb8, 0xd1, 0x80, 0xd0, 0xbe, 0xd0,
	0xb2, 0xd0, 0xb0, 0xd0, 0xbd, 0xd0, 0xb8, 0xd1, 0x8f, 0x2c, 0x20, 0xd0, 0xb7, 0xd0, 0xb0, 0xd1,
	0x8f, 0xd0, 0xb2, 0xd0, 0xba, 0xd0, 0xb0, 0x20, 0xd0, 0xbe, 0xd1, 0x82, 0xd0, 0xbc, 0xd0, 0xb5,
	0xd0, 0xbd, 0xd0, 0xb5, 0xd0, 0xbd, 0xd0, 0xb0, 0x21, 0x18, 0x05, 0x20, 0x6a, 0xca, 0xa1, 0xec,
	0x8e, 0x02, 0x02, 0x33, 0x34, 0x12, 0x55, 0x0a, 0x14, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x65,
	0x72, 0x55, 0x6e, 0x6b, 0x6e, 0x6f, 0x77, 0x6e, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x10, 0x19, 0x1a,
	0x3b, 0x9a, 0xf1, 0x30, 0x2f, 0x0a, 0x02, 0x39, 0x39, 0x12, 0x25, 0xd0, 0x9d, 0xd0, 0xb5, 0xd0,
	0xb8, 0xd0, 0xb7, 0xd0, 0xb2, 0xd0, 0xb5, 0xd1, 0x81, 0xd1, 0x82, 0xd0, 0xbd, 0xd0, 0xb0, 0xd1,
	0x8f, 0x20, 0xd0, 0xbe, 0xd1, 0x88, 0xd0, 0xb8, 0xd0, 0xb1, 0xd0, 0xba, 0xd0, 0xb0, 0x3a, 0x20,
	0x18, 0x0b, 0x20, 0x6a, 0xca, 0xa1, 0xec, 0x8e, 0x02, 0x02, 0x39, 0x39, 0x12, 0x89, 0x01, 0x0a,
	0x1a, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x65, 0x72, 0x55, 0x6e, 0x61, 0x76, 0x61, 0x69, 0x6c,
	0x61, 0x62, 0x6c, 0x65, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x10, 0x1a, 0x1a, 0x69, 0x9a,
	0xf1, 0x30, 0x5d, 0x0a, 0x02, 0x34, 0x31, 0x12, 0x53, 0xd0, 0xa1, 0xd0, 0xb5, 0xd1, 0x80, 0xd0,
	0xb2, 0xd0, 0xb8, 0xd1, 0x81, 0x20, 0xd0, 0xb2, 0xd1, 0x80, 0xd0, 0xb5, 0xd0, 0xbc, 0xd0, 0xb5,
	0xd0, 0xbd, 0xd0, 0xbd, 0xd0, 0xbe, 0x20, 0xd0, 0xbd, 0xd0, 0xb5, 0xd0, 0xb4, 0xd0, 0xbe, 0xd1,
	0x81, 0xd1, 0x82, 0xd1, 0x83, 0xd0, 0xbf, 0xd0, 0xb5, 0xd0, 0xbd, 0x2c, 0x20, 0xd0, 0xbf, 0xd0,
	0xbe, 0xd0, 0xbf, 0xd1, 0x80, 0xd0, 0xbe, 0xd0, 0xb1, 0xd1, 0x83, 0xd0, 0xb9, 0xd1, 0x82, 0xd0,
	0xb5, 0x20, 0xd0, 0xbf, 0xd0, 0xbe, 0xd0, 0xb7, 0xd0, 0xb6, 0xd0, 0xb5, 0x18, 0x05, 0x20, 0x6c,
	0xca, 0xa1, 0xec, 0x8e, 0x02, 0x02, 0x34, 0x31, 0x12, 0x53, 0x0a, 0x14, 0x53, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x65, 0x72, 0x53, 0x75, 0x6d, 0x49, 0x6e, 0x63, 0x6f, 0x72, 0x72, 0x65, 0x63, 0x74,
	0x10, 0x1b, 0x1a, 0x39, 0x9a, 0xf1, 0x30, 0x2d, 0x0a, 0x02, 0x34, 0x32, 0x12, 0x23, 0xd0, 0x9d,
	0xd0, 0xb5, 0xd0, 0xbf, 0xd1, 0x80, 0xd0, 0xb0, 0xd0, 0xb2, 0xd0, 0xb8, 0xd0, 0xbb, 0xd1, 0x8c,
	0xd0, 0xbd, 0xd0, 0xb0, 0xd1, 0x8f, 0x20, 0xd1, 0x81, 0xd1, 0x83, 0xd0, 0xbc, 0xd0, 0xbc, 0xd0,
	0xb0, 0x18, 0x05, 0x20, 0x6a, 0xca, 0xa1, 0xec, 0x8e, 0x02, 0x02, 0x34, 0x32, 0x12, 0x88, 0x01,
	0x0a, 0x15, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x65, 0x72, 0x55, 0x6e, 0x61, 0x76, 0x61, 0x69,
	0x6c, 0x61, 0x62, 0x6c, 0x65, 0x44, 0x62, 0x10, 0x1c, 0x1a, 0x6d, 0x9a, 0xf1, 0x30, 0x61, 0x0a,
	0x02, 0x34, 0x33, 0x12, 0x57, 0xd0, 0xa1, 0xd0, 0xb5, 0xd1, 0x80, 0xd0, 0xb2, 0xd0, 0xb8, 0xd1,
	0x81, 0x20, 0x44, 0x62, 0x20, 0xd0, 0xb2, 0xd1, 0x80, 0xd0, 0xb5, 0xd0, 0xbc, 0xd0, 0xb5, 0xd0,
	0xbd, 0xd0, 0xbd, 0xd0, 0xbe, 0x20, 0xd0, 0xbd, 0xd0, 0xb5, 0xd0, 0xb4, 0xd0, 0xbe, 0xd1, 0x81,
	0xd1, 0x82, 0xd1, 0x83, 0xd0, 0xbf, 0xd0, 0xb5, 0xd0, 0xbd, 0x2c, 0x20, 0xd0, 0xbf, 0xd0, 0xbe,
	0xd0, 0xbf, 0xd1, 0x80, 0xd0, 0xbe, 0xd0, 0xb1, 0xd1, 0x83, 0xd0, 0xb9, 0xd1, 0x82, 0xd0, 0xb5,
	0x20, 0xd0, 0xbf, 0xd0, 0xbe, 0xd0, 0xb7, 0xd0, 0xb6, 0xd0, 0xb5, 0x20, 0x18, 0x05, 0x20, 0x6c,
	0xca, 0xa1, 0xec, 0x8e, 0x02, 0x02, 0x34, 0x33, 0x12, 0x73, 0x0a, 0x19, 0x53, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x65, 0x72, 0x49, 0x6e, 0x63, 0x6f, 0x72, 0x72, 0x65, 0x63, 0x74, 0x4d, 0x65, 0x72,
	0x63, 0x68, 0x61, 0x6e, 0x74, 0x10, 0x1d, 0x1a, 0x54, 0x9a, 0xf1, 0x30, 0x48, 0x0a, 0x02, 0x34,
	0x34, 0x12, 0x3e, 0xd0, 0x9d, 0xd0, 0xb5, 0xd0, 0xbf, 0xd1, 0x80, 0xd0, 0xb0, 0xd0, 0xb2, 0xd0,
	0xb8, 0xd0, 0xbb, 0xd1, 0x8c, 0xd0, 0xbd, 0xd0, 0xbe, 0xd0, 0xb5, 0x20, 0xd0, 0xb7, 0xd0, 0xbd,
	0xd0, 0xb0, 0xd1, 0x87, 0xd0, 0xb5, 0xd0, 0xbd, 0xd0, 0xb8, 0xd0, 0xb5, 0x20, 0xd0, 0xb2, 0x20,
	0xd0, 0xbf, 0xd0, 0xbe, 0xd0, 0xbb, 0xd0, 0xb5, 0x20, 0x4d, 0x45, 0x52, 0x43, 0x48, 0x41, 0x4e,
	0x54, 0x18, 0x05, 0x20, 0x6a, 0xca, 0xa1, 0xec, 0x8e, 0x02, 0x02, 0x34, 0x34, 0x12, 0x5a, 0x0a,
	0x18, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x65, 0x72, 0x4e, 0x6f, 0x74, 0x46, 0x6f, 0x75, 0x6e,
	0x64, 0x4d, 0x65, 0x72, 0x63, 0x68, 0x61, 0x6e, 0x74, 0x10, 0x1e, 0x1a, 0x3c, 0x9a, 0xf1, 0x30,
	0x30, 0x0a, 0x02, 0x31, 0x37, 0x12, 0x26, 0xd0, 0x9a, 0xd0, 0xbe, 0xd0, 0xbc, 0xd0, 0xbc, 0xd0,
	0xb5, 0xd1, 0x80, 0xd1, 0x81, 0xd0, 0xb0, 0xd0, 0xbd, 0xd1, 0x82, 0x20, 0xd0, 0xbd, 0xd0, 0xb5,
	0x20, 0xd0, 0xbd, 0xd0, 0xb0, 0xd0, 0xb9, 0xd0, 0xb4, 0xd0, 0xb5, 0xd0, 0xbd, 0x18, 0x05, 0x20,
	0x6d, 0xca, 0xa1, 0xec, 0x8e, 0x02, 0x02, 0x31, 0x37, 0x12, 0x57, 0x0a, 0x15, 0x53, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x65, 0x72, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x4e, 0x6f, 0x74, 0x46, 0x6f, 0x75,
	0x6e, 0x64, 0x10, 0x1f, 0x1a, 0x3c, 0x9a, 0xf1, 0x30, 0x30, 0x0a, 0x02, 0x34, 0x35, 0x12, 0x26,
	0xd0, 0x97, 0xd0, 0xb0, 0xd1, 0x8f, 0xd0, 0xb2, 0xd0, 0xba, 0xd0, 0xb0, 0x20, 0x4f, 0x52, 0x44,
	0x45, 0x52, 0x20, 0xd0, 0xbd, 0xd0, 0xb5, 0x20, 0xd0, 0xbd, 0xd0, 0xb0, 0xd0, 0xb9, 0xd0, 0xb4,
	0xd0, 0xb5, 0xd0, 0xbd, 0xd0, 0xb0, 0x18, 0x05, 0x20, 0x6a, 0xca, 0xa1, 0xec, 0x8e, 0x02, 0x02,
	0x34, 0x35, 0x12, 0x57, 0x0a, 0x13, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x65, 0x72, 0x49, 0x6e,
	0x76, 0x61, 0x6c, 0x69, 0x64, 0x53, 0x69, 0x67, 0x6e, 0x10, 0x20, 0x1a, 0x3e, 0x9a, 0xf1, 0x30,
	0x32, 0x0a, 0x02, 0x34, 0x36, 0x12, 0x28, 0xd0, 0x9d, 0xd0, 0xb5, 0xd0, 0xbf, 0xd1, 0x80, 0xd0,
	0xb0, 0xd0, 0xb2, 0xd0, 0xb8, 0xd0, 0xbb, 0xd1, 0x8c, 0xd0, 0xbd, 0xd0, 0xb0, 0xd1, 0x8f, 0x20,
	0xd0, 0xbf, 0xd0, 0xbe, 0xd0, 0xb4, 0xd0, 0xbf, 0xd0, 0xb8, 0xd1, 0x81, 0xd1, 0x8c, 0x21, 0x18,
	0x05, 0x20, 0x6a, 0xca, 0xa1, 0xec, 0x8e, 0x02, 0x02, 0x34, 0x36, 0x12, 0x85, 0x01, 0x0a, 0x1d,
	0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x65, 0x72, 0x49, 0x6e, 0x63, 0x6f, 0x72, 0x72, 0x65, 0x63,
	0x74, 0x52, 0x65, 0x66, 0x75, 0x6e, 0x64, 0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x10, 0x21, 0x1a,
	0x62, 0x9a, 0xf1, 0x30, 0x55, 0x0a, 0x03, 0x34, 0x37, 0x20, 0x12, 0x4a, 0xd0, 0xa1, 0xd1, 0x83,
	0xd0, 0xbc, 0xd0, 0xbc, 0xd0, 0xb0, 0x20, 0xd0, 0xb2, 0xd0, 0xbe, 0xd0, 0xb7, 0xd0, 0xb2, 0xd1,
	0x80, 0xd1, 0x82, 0xd0, 0xb0, 0x20, 0x27, 0x25, 0x73, 0x27, 0x20, 0xd0, 0xb1, 0xd0, 0xbe, 0xd0,
	0xbb, 0xd1, 0x8c, 0xd1, 0x88, 0xd0, 0xb5, 0x20, 0xd1, 0x87, 0xd0, 0xb5, 0xd0, 0xbc, 0x20, 0xd1,
	0x81, 0xd1, 0x83, 0xd0, 0xbc, 0xd0, 0xbc, 0xd0, 0xb0, 0x20, 0xd0, 0xb7, 0xd0, 0xb0, 0xd0, 0xba,
	0xd0, 0xb0, 0xd0, 0xb7, 0xd0, 0xb0, 0x18, 0x05, 0x20, 0x6a, 0xca, 0xa1, 0xec, 0x8e, 0x02, 0x03,
	0x34, 0x37, 0x20, 0x12, 0x9c, 0x01, 0x0a, 0x17, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x65, 0x72,
	0x49, 0x6e, 0x63, 0x6f, 0x72, 0x72, 0x65, 0x63, 0x74, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x10,
	0x22, 0x1a, 0x7f, 0x9a, 0xf1, 0x30, 0x73, 0x0a, 0x02, 0x34, 0x38, 0x12, 0x69, 0xd0, 0xa2, 0xd0,
	0xb5, 0xd0, 0xba, 0xd1, 0x83, 0xd1, 0x89, 0xd0, 0xb8, 0xd0, 0xb9, 0x20, 0xd1, 0x81, 0xd1, 0x82,
	0xd0, 0xb0, 0xd1, 0x82, 0xd1, 0x83, 0xd1, 0x81, 0x20, 0xd0, 0xb7, 0xd0, 0xb0, 0xd0, 0xba, 0xd0,
	0xb0, 0xd0, 0xb7, 0xd0, 0xb0, 0x20, 0xd0, 0xbd, 0xd0, 0xb5, 0x20, 0xd0, 0xbf, 0xd0, 0xbe, 0xd0,
	0xb7, 0xd0, 0xb2, 0xd0, 0xbe, 0xd0, 0xbb, 0xd1, 0x8f, 0xd0, 0xb5, 0xd1, 0x82, 0x20, 0xd0, 0xb4,
	0xd0, 0xb5, 0xd0, 0xbb, 0xd0, 0xb0, 0xd1, 0x82, 0xd1, 0x8c, 0x20, 0xd0, 0xb2, 0xd0, 0xbe, 0xd0,
	0xb7, 0xd0, 0xb2, 0xd1, 0x80, 0xd0, 0xb0, 0xd1, 0x82, 0x2f, 0xd0, 0xbe, 0xd1, 0x82, 0xd0, 0xbc,
	0xd0, 0xb5, 0xd0, 0xbd, 0xd1, 0x83, 0x18, 0x05, 0x20, 0x6a, 0xca, 0xa1, 0xec, 0x8e, 0x02, 0x02,
	0x34, 0x38, 0x12, 0x5b, 0x0a, 0x16, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x65, 0x72, 0x49, 0x6e,
	0x63, 0x6f, 0x72, 0x72, 0x65, 0x63, 0x74, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x10, 0x23, 0x1a, 0x3f,
	0x9a, 0xf1, 0x30, 0x33, 0x0a, 0x02, 0x35, 0x30, 0x12, 0x29, 0xd0, 0x9d, 0xd0, 0xb5, 0xd0, 0xbf,
	0xd1, 0x80, 0xd0, 0xb0, 0xd0, 0xb2, 0xd0, 0xb8, 0xd0, 0xbb, 0xd1, 0x8c, 0xd0, 0xbd, 0xd0, 0xbe,
	0xd0, 0xb5, 0x20, 0xd0, 0xb7, 0xd0, 0xbd, 0xd0, 0xb0, 0xd1, 0x87, 0xd0, 0xb5, 0xd0, 0xbd, 0xd0,
	0xb8, 0xd0, 0xb5, 0x18, 0x05, 0x20, 0x6a, 0xca, 0xa1, 0xec, 0x8e, 0x02, 0x02, 0x35, 0x30, 0x12,
	0xaa, 0x01, 0x0a, 0x1f, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x65, 0x72, 0x54, 0x65, 0x72, 0x6d,
	0x69, 0x6e, 0x61, 0x6c, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x49, 0x6e, 0x63, 0x6f, 0x72, 0x72,
	0x65, 0x63, 0x74, 0x10, 0x24, 0x1a, 0x84, 0x01, 0x9a, 0xf1, 0x30, 0x78, 0x0a, 0x02, 0x35, 0x31,
	0x12, 0x6e, 0xd0, 0xa2, 0xd0, 0xb5, 0xd0, 0xba, 0xd1, 0x83, 0xd1, 0x89, 0xd0, 0xb8, 0xd0, 0xb9,
	0x20, 0xd1, 0x81, 0xd1, 0x82, 0xd0, 0xb0, 0xd1, 0x82, 0xd1, 0x83, 0xd1, 0x81, 0x20, 0xd1, 0x82,
	0xd0, 0xb5, 0xd1, 0x80, 0xd0, 0xbc, 0xd0, 0xb8, 0xd0, 0xbd, 0xd0, 0xb0, 0xd0, 0xbb, 0xd0, 0xb0,
	0x20, 0xd0, 0xbd, 0xd0, 0xb5, 0x20, 0xd0, 0xbf, 0xd0, 0xbe, 0xd0, 0xb7, 0xd0, 0xb2, 0xd0, 0xbe,
	0xd0, 0xbb, 0xd1, 0x8f, 0xd0, 0xb5, 0xd1, 0x82, 0x20, 0xd0, 0xbf, 0xd1, 0x80, 0xd0, 0xbe, 0xd0,
	0xb8, 0xd0, 0xb7, 0xd0, 0xb2, 0xd0, 0xbe, 0xd0, 0xb4, 0xd0, 0xb8, 0xd1, 0x82, 0xd1, 0x8c, 0x20,
	0xd0, 0xbe, 0xd0, 0xbf, 0xd0, 0xb5, 0xd1, 0x80, 0xd0, 0xb0, 0xd1, 0x86, 0xd0, 0xb8, 0xd0, 0xb8,
	0x18, 0x05, 0x20, 0x6d, 0xca, 0xa1, 0xec, 0x8e, 0x02, 0x02, 0x35, 0x31, 0x12, 0xa0, 0x01, 0x0a,
	0x19, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x65, 0x72, 0x54, 0x65, 0x72, 0x6d, 0x69, 0x6e, 0x61,
	0x6c, 0x46, 0x6f, 0x72, 0x62, 0x69, 0x64, 0x64, 0x65, 0x6e, 0x10, 0x25, 0x1a, 0x80, 0x01, 0x9a,
	0xf1, 0x30, 0x74, 0x0a, 0x02, 0x35, 0x32, 0x12, 0x6a, 0xd0, 0x9e, 0xd0, 0xbf, 0xd0, 0xb5, 0xd1,
	0x80, 0xd0, 0xb0, 0xd1, 0x86, 0xd0, 0xb8, 0xd1, 0x8f, 0x20, 0xd0, 0xbe, 0xd1, 0x82, 0xd0, 0xbc,
	0xd0, 0xb5, 0xd0, 0xbd, 0xd1, 0x8b, 0x2f, 0xd0, 0xb2, 0xd0, 0xbe, 0xd0, 0xb7, 0xd0, 0xb2, 0xd1,
	0x80, 0xd0, 0xb0, 0xd1, 0x82, 0xd0, 0xb0, 0x20, 0xd1, 0x87, 0xd0, 0xb5, 0xd1, 0x80, 0xd0, 0xb5,
	0xd0, 0xb7, 0x20, 0x41, 0x50, 0x49, 0x20, 0xd0, 0xb4, 0xd0, 0xbb, 0xd1, 0x8f, 0x20, 0xd1, 0x82,
	0xd0, 0xb5, 0xd1, 0x80, 0xd0, 0xbc, 0xd0, 0xb8, 0xd0, 0xbd, 0xd0, 0xb0, 0xd0, 0xbb, 0xd0, 0xb0,
	0x20, 0xd0, 0xb7, 0xd0, 0xb0, 0xd0, 0xbf, 0xd1, 0x80, 0xd0, 0xb5, 0xd1, 0x89, 0xd0, 0xb5, 0xd0,
	0xbd, 0xd0, 0xb0, 0x18, 0x05, 0x20, 0x6d, 0xca, 0xa1, 0xec, 0x8e, 0x02, 0x02, 0x35, 0x32, 0x12,
	0x6e, 0x0a, 0x1c, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x65, 0x72, 0x44, 0x75, 0x70, 0x6c, 0x69,
	0x63, 0x61, 0x74, 0x65, 0x44, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x10,
	0x26, 0x1a, 0x4c, 0x9a, 0xf1, 0x30, 0x40, 0x0a, 0x02, 0x35, 0x33, 0x12, 0x36, 0xd0, 0x94, 0xd1,
	0x83, 0xd0, 0xb1, 0xd0, 0xbb, 0xd0, 0xb8, 0xd1, 0x80, 0xd0, 0xbe, 0xd0, 0xb2, 0xd0, 0xb0, 0xd0,
	0xbd, 0xd0, 0xb8, 0xd0, 0xb5, 0x20, 0xd0, 0xbe, 0xd0, 0xbf, 0xd0, 0xb8, 0xd1, 0x81, 0xd0, 0xb0,
	0xd0, 0xbd, 0xd0, 0xb8, 0xd1, 0x8f, 0x20, 0xd0, 0xbe, 0xd1, 0x82, 0xd0, 0xbc, 0xd0, 0xb5, 0xd0,
	0xbd, 0xd1, 0x8b, 0x18, 0x05, 0x20, 0x6a, 0xca, 0xa1, 0xec, 0x8e, 0x02, 0x02, 0x35, 0x33, 0x12,
	0x6a, 0x0a, 0x19, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x65, 0x72, 0x52, 0x65, 0x66, 0x75, 0x6e,
	0x64, 0x48, 0x61, 0x6e, 0x64, 0x6c, 0x65, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x10, 0x27, 0x1a, 0x4b,
	0x9a, 0xf1, 0x30, 0x40, 0x0a, 0x01, 0x46, 0x12, 0x37, 0xd0, 0x9e, 0xd1, 0x88, 0xd0, 0xb8, 0xd0,
	0xb1, 0xd0, 0xba, 0xd0, 0xb0, 0x20, 0xd0, 0xbf, 0xd1, 0x80, 0xd0, 0xb8, 0x20, 0xd0, 0xbe, 0xd0,
	0xb1, 0xd1, 0x80, 0xd0, 0xb0, 0xd0, 0xb1, 0xd0, 0xbe, 0xd1, 0x82, 0xd0, 0xba, 0xd0, 0xb5, 0x20,
	0xd0, 0xb2, 0xd0, 0xbe, 0xd0, 0xb7, 0xd0, 0xb2, 0xd1, 0x80, 0xd0, 0xb0, 0xd1, 0x82, 0xd0, 0xb0,
	0x18, 0x05, 0x20, 0x6a, 0xca, 0xa1, 0xec, 0x8e, 0x02, 0x01, 0x46, 0x12, 0x4e, 0x0a, 0x14, 0x53,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x65, 0x72, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x45, 0x72,
	0x72, 0x6f, 0x72, 0x10, 0x28, 0x1a, 0x34, 0x9a, 0xf1, 0x30, 0x29, 0x0a, 0x01, 0x45, 0x12, 0x20,
	0xd0, 0x9e, 0xd1, 0x88, 0xd0, 0xb8, 0xd0, 0xb1, 0xd0, 0xba, 0xd0, 0xb0, 0x20, 0xd0, 0xbf, 0xd1,
	0x80, 0xd0, 0xb8, 0x20, 0xd0, 0xbe, 0xd0, 0xbf, 0xd0, 0xbb, 0xd0, 0xb0, 0xd1, 0x82, 0xd0, 0xb5,
	0x18, 0x05, 0x20, 0x6a, 0xca, 0xa1, 0xec, 0x8e, 0x02, 0x01, 0x45, 0x12, 0x59, 0x0a, 0x16, 0x53,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x65, 0x72, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x45, 0x78,
	0x70, 0x69, 0x72, 0x65, 0x64, 0x10, 0x29, 0x1a, 0x3d, 0x9a, 0xf1, 0x30, 0x32, 0x0a, 0x01, 0x63,
	0x12, 0x29, 0xd0, 0xa1, 0xd1, 0x87, 0xd0, 0xb5, 0xd1, 0x82, 0x20, 0xd0, 0xbd, 0xd0, 0xb0, 0x20,
	0xd0, 0xbe, 0xd0, 0xbf, 0xd0, 0xbb, 0xd0, 0xb0, 0xd1, 0x82, 0xd1, 0x83, 0x20, 0xd1, 0x83, 0xd1,
	0x81, 0xd1, 0x82, 0xd0, 0xb0, 0xd1, 0x80, 0xd0, 0xb5, 0xd0, 0xbb, 0x18, 0x05, 0x20, 0x6a, 0xca,
	0xa1, 0xec, 0x8e, 0x02, 0x01, 0x63, 0x1a, 0x6c, 0xa2, 0xf1, 0x30, 0x10, 0x0a, 0x01, 0x30, 0x12,
	0x07, 0x64, 0x65, 0x66, 0x61, 0x75, 0x6c, 0x74, 0x18, 0x0f, 0x20, 0x00, 0xaa, 0x82, 0xec, 0x8e,
	0x02, 0x2a, 0x64, 0x65, 0x66, 0x61, 0x75, 0x6c, 0x74, 0x5f, 0x6a, 0x75, 0x73, 0x61, 0x6e, 0x5f,
	0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x5f, 0x73, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x76, 0x61, 0x6c, 0x75, 0x65, 0xb2, 0x82, 0xec, 0x8e,
	0x02, 0x22, 0x6a, 0x75, 0x73, 0x61, 0x6e, 0x5f, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x5f, 0x63, 0x6f, 0x64, 0x65, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x76,
	0x61, 0x6c, 0x75, 0x65, 0x3a, 0xb7, 0x01, 0x0a, 0x22, 0x6a, 0x75, 0x73, 0x61, 0x6e, 0x5f, 0x72,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x5f, 0x73, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x12, 0x21, 0x2e, 0x67, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x45, 0x6e,
	0x75, 0x6d, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x93,
	0x8e, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x46, 0x2e, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73,
	0x69, 0x6e, 0x67, 0x2e, 0x6a, 0x75, 0x73, 0x61, 0x6e, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x65, 0x72, 0x2e, 0x6a, 0x75, 0x73, 0x61, 0x6e, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x65,
	0x72, 0x2e, 0x4a, 0x75, 0x73, 0x61, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x43,
	0x6f, 0x64, 0x65, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x65, 0x72, 0x52, 0x65, 0x66, 0x52, 0x1e,
	0x6a, 0x75, 0x73, 0x61, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x43, 0x6f, 0x64,
	0x65, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x65, 0x72, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0xc1,
	0x01, 0x0a, 0x2a, 0x64, 0x65, 0x66, 0x61, 0x75, 0x6c, 0x74, 0x5f, 0x6a, 0x75, 0x73, 0x61, 0x6e,
	0x5f, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x5f, 0x73,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x12, 0x1c, 0x2e,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e,
	0x45, 0x6e, 0x75, 0x6d, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x94, 0x8e, 0x06, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x46, 0x2e, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67,
	0x2e, 0x6a, 0x75, 0x73, 0x61, 0x6e, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x65, 0x72, 0x2e,
	0x6a, 0x75, 0x73, 0x61, 0x6e, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x65, 0x72, 0x2e, 0x4a,
	0x75, 0x73, 0x61, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x43, 0x6f, 0x64, 0x65,
	0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x65, 0x72, 0x52, 0x65, 0x66, 0x52, 0x25, 0x64, 0x65, 0x66,
	0x61, 0x75, 0x6c, 0x74, 0x4a, 0x75, 0x73, 0x61, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x43, 0x6f, 0x64, 0x65, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x65, 0x72, 0x56, 0x61, 0x6c,
	0x75, 0x65, 0x42, 0x2d, 0x5a, 0x2b, 0x67, 0x69, 0x74, 0x2e, 0x6c, 0x6f, 0x63, 0x61, 0x6c, 0x2f,
	0x73, 0x65, 0x6e, 0x73, 0x69, 0x74, 0x69, 0x76, 0x65, 0x2f, 0x69, 0x6e, 0x6e, 0x65, 0x72, 0x70,
	0x62, 0x2f, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x2f, 0x67, 0x72, 0x70,
	0x63, 0x62, 0x08, 0x65, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x70, 0xe8, 0x07,
})

var (
	file_inner_processing_grpc_jusan_statuser_proto_rawDescOnce sync.Once
	file_inner_processing_grpc_jusan_statuser_proto_rawDescData []byte
)

func file_inner_processing_grpc_jusan_statuser_proto_rawDescGZIP() []byte {
	file_inner_processing_grpc_jusan_statuser_proto_rawDescOnce.Do(func() {
		file_inner_processing_grpc_jusan_statuser_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_inner_processing_grpc_jusan_statuser_proto_rawDesc), len(file_inner_processing_grpc_jusan_statuser_proto_rawDesc)))
	})
	return file_inner_processing_grpc_jusan_statuser_proto_rawDescData
}

var file_inner_processing_grpc_jusan_statuser_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_inner_processing_grpc_jusan_statuser_proto_msgTypes = make([]protoimpl.MessageInfo, 1)
var file_inner_processing_grpc_jusan_statuser_proto_goTypes = []any{
	(JusanResponseCodeStatuser)(0),        // 0: processing.jusan_statuser.jusan_statuser.JusanResponseCodeStatuser
	(*JusanResponseCodeStatuserRef)(nil),  // 1: processing.jusan_statuser.jusan_statuser.JusanResponseCodeStatuserRef
	(EnumTransactionStatus)(0),            // 2: processing.transaction.transaction_status.EnumTransactionStatus
	(IntegrationError)(0),                 // 3: processing.integration.integration.IntegrationError
	(*descriptorpb.EnumValueOptions)(nil), // 4: google.protobuf.EnumValueOptions
	(*descriptorpb.EnumOptions)(nil),      // 5: google.protobuf.EnumOptions
}
var file_inner_processing_grpc_jusan_statuser_proto_depIdxs = []int32{
	2, // 0: processing.jusan_statuser.jusan_statuser.JusanResponseCodeStatuserRef.transaction_status:type_name -> processing.transaction.transaction_status.EnumTransactionStatus
	3, // 1: processing.jusan_statuser.jusan_statuser.JusanResponseCodeStatuserRef.integration_error:type_name -> processing.integration.integration.IntegrationError
	4, // 2: processing.jusan_statuser.jusan_statuser.jusan_response_code_statuser_value:extendee -> google.protobuf.EnumValueOptions
	5, // 3: processing.jusan_statuser.jusan_statuser.default_jusan_response_code_statuser_value:extendee -> google.protobuf.EnumOptions
	1, // 4: processing.jusan_statuser.jusan_statuser.jusan_response_code_statuser_value:type_name -> processing.jusan_statuser.jusan_statuser.JusanResponseCodeStatuserRef
	1, // 5: processing.jusan_statuser.jusan_statuser.default_jusan_response_code_statuser_value:type_name -> processing.jusan_statuser.jusan_statuser.JusanResponseCodeStatuserRef
	6, // [6:6] is the sub-list for method output_type
	6, // [6:6] is the sub-list for method input_type
	4, // [4:6] is the sub-list for extension type_name
	2, // [2:4] is the sub-list for extension extendee
	0, // [0:2] is the sub-list for field type_name
}

func init() { file_inner_processing_grpc_jusan_statuser_proto_init() }
func file_inner_processing_grpc_jusan_statuser_proto_init() {
	if File_inner_processing_grpc_jusan_statuser_proto != nil {
		return
	}
	file_inner_processing_grpc_transaction_status_proto_init()
	file_inner_processing_grpc_integration_proto_init()
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_inner_processing_grpc_jusan_statuser_proto_rawDesc), len(file_inner_processing_grpc_jusan_statuser_proto_rawDesc)),
			NumEnums:      1,
			NumMessages:   1,
			NumExtensions: 2,
			NumServices:   0,
		},
		GoTypes:           file_inner_processing_grpc_jusan_statuser_proto_goTypes,
		DependencyIndexes: file_inner_processing_grpc_jusan_statuser_proto_depIdxs,
		EnumInfos:         file_inner_processing_grpc_jusan_statuser_proto_enumTypes,
		MessageInfos:      file_inner_processing_grpc_jusan_statuser_proto_msgTypes,
		ExtensionInfos:    file_inner_processing_grpc_jusan_statuser_proto_extTypes,
	}.Build()
	File_inner_processing_grpc_jusan_statuser_proto = out.File
	file_inner_processing_grpc_jusan_statuser_proto_goTypes = nil
	file_inner_processing_grpc_jusan_statuser_proto_depIdxs = nil
}
