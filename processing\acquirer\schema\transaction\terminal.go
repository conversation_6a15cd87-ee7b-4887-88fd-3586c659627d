package transaction

type (
	TerminalRequest struct {
		TerminalID uint64
	}

	TerminalResponse struct {
		TerminalID        uint64
		ProjectID         uint64
		BankTerminalID    string
		Login             *string
		Password          *string
		TransactionTypeID uint64
		AccountID         uint64
		Acquirer          AcquirerData
	}

	AcquirerData struct {
		AcquirerId   uint64
		AcquirerCode string
		BankId       uint64
		Description  *string
		CountryId    uint64
	}
)
