package database

import (
	"context"
	"errors"

	"github.com/jackc/pgx/v5/pgconn"
	"gorm.io/gorm"

	"git.local/sensitive/innerpb/processing/goerr"
	"git.local/sensitive/pkg/middlewares"
	"git.local/sensitive/processing/acquirer/model"
	"git.local/sensitive/processing/acquirer/schema"
	"git.local/sensitive/sdk/dog"
)

type BasicAcquirerDB struct {
	db *gorm.DB
}

func NewBasicAcquirerDB(db *gorm.DB) AcquirerBasicer {
	return &BasicAcquirerDB{
		db: db,
	}
}

func (b *BasicAcquirerDB) Create(ctx context.Context, acquirer *model.Acquirer) (err error) {
	ctx, span := dog.CreateSpan(ctx, "BasicAcquirerDB_Create")
	defer span.End()

	if acquirer == nil {
		return goerr.ErrParseErrorBody
	}

	err = b.db.WithContext(ctx).Create(&acquirer).Error
	if err != nil {
		if errors.Is(err, gorm.ErrDuplicatedKey) {
			return goerr.ErrCodeDuplicatedKey.WithErr(err).WithCtx(ctx)
		}

		pgErr, ok := err.(*pgconn.PgError)
		if !ok {
			return goerr.ErrDbUnexpected.WithErr(err).WithCtx(ctx)
		}

		if pgErr.Code == ForeignKeyViolationCode {
			return goerr.ErrForeignKeyViolation.WithErr(err).WithCtx(ctx)
		}

		return goerr.ErrDbUnexpected.WithErr(err).WithCtx(ctx)
	}

	return nil
}

func (b *BasicAcquirerDB) GetAllByFilter(
	ctx context.Context,
	filter schema.AcquirerFilter,
	pagination *middlewares.PaginationInfo,
) (_ []*model.Acquirer, err error) {
	ctx, span := dog.CreateSpan(ctx, "BasicAcquirerDB_GetAllByFilter")
	defer span.End()

	result := make([]*model.Acquirer, 0)

	request := b.db.WithContext(ctx).
		Select("a.*, c.name AS country_name").
		Table(model.Acquirer{}.TableName() + " AS a").
		Joins("INNER JOIN acquirer.countries AS c ON c.id = a.country_id")
	if filter.AcquirerID != 0 {
		request.Where(`a.id = ?`, filter.AcquirerID)
	}

	if filter.IsActive != nil {
		request.Where(`a.is_active = ?`, filter.IsActive)
	}

	if pagination == nil || !pagination.Pagination {
		if err = request.Order("a.id ASC").Find(&result).
			Error; err != nil {
			return nil, goerr.ErrDbUnexpected.WithErr(err).WithCtx(ctx)
		}

		return result, nil
	}

	if err = request.WithContext(ctx).Model(&result).
		Count(&pagination.Total).Error; err != nil {
		return nil, goerr.ErrDbUnexpected.WithErr(err).WithCtx(ctx)
	}

	if err = request.
		Offset((pagination.Page - 1) * pagination.PerPage).
		Limit(pagination.PerPage).
		Find(&result).Order("a.id ASC").Error; err != nil {
		return nil, goerr.ErrDbUnexpected.WithErr(err).WithCtx(ctx)
	}

	return result, nil
}

func (b *BasicAcquirerDB) GetAllActive(
	ctx context.Context,
	pagination *middlewares.PaginationInfo,
) (_ []*model.Acquirer, err error) {
	ctx, span := dog.CreateSpan(ctx, "BasicAcquirerDB_GetAllActive")
	defer span.End()

	result := make([]*model.Acquirer, 0)

	request := b.db.WithContext(ctx).
		Where("is_active = ?", true)

	if pagination == nil || !pagination.Pagination {
		if err = request.Find(&result).
			Error; err != nil {
			return nil, goerr.ErrDbUnexpected.WithErr(err).WithCtx(ctx)
		}

		return result, nil
	}

	if err = request.
		Offset((pagination.Page - 1) * pagination.PerPage).
		Limit(pagination.PerPage).
		Find(&result).Error; err != nil {
		return nil, goerr.ErrDbUnexpected.WithErr(err).WithCtx(ctx)
	}

	return result, nil
}

func (b *BasicAcquirerDB) Update(ctx context.Context, id uint64, data *model.Acquirer) (err error) {
	ctx, span := dog.CreateSpan(ctx, "BasicAcquirerDB_Update")
	defer span.End()

	if data == nil {
		return goerr.ErrParseErrorBody.WithCtx(ctx)
	}

	err = b.db.WithContext(ctx).
		Model(&model.Acquirer{}).
		Where("id = ?", id).
		Updates(model.Acquirer{
			Code:        data.Code,
			BankID:      data.BankID,
			Description: data.Description,
			CountryID:   data.CountryID,
		}).First(&model.Acquirer{}).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return goerr.ErrAcquirerNotFound.WithCtx(ctx)
		}

		if errors.Is(err, gorm.ErrDuplicatedKey) {
			return goerr.ErrEmailDuplicatedKey.WithErr(err).WithCtx(ctx)
		}

		pgErr, ok := err.(*pgconn.PgError)
		if !ok {
			return goerr.ErrDbUnexpected.WithErr(err).WithCtx(ctx)
		}

		if pgErr.Code == ForeignKeyViolationCode {
			return goerr.ErrBankNotFound.WithErr(err).WithCtx(ctx)
		}

		return goerr.ErrDbUnexpected.WithErr(err).WithCtx(ctx)
	}

	return nil
}

func (b *BasicAcquirerDB) GetByCode(ctx context.Context, acquirerCode string) (_ *model.Acquirer, err error) {
	ctx, span := dog.CreateSpan(ctx, "BasicAcquirerDB_GetByCode")
	defer span.End()

	acquirer := new(model.Acquirer)

	err = b.db.WithContext(ctx).
		Where("code = ?", acquirerCode).
		First(acquirer).
		Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, goerr.ErrAcquirerNotFound.WithCtx(ctx)
		}

		return nil, err
	}

	return acquirer, nil
}

func (b *BasicAcquirerDB) GetByID(ctx context.Context, acquirerID uint64) (_ *model.Acquirer, err error) {
	ctx, span := dog.CreateSpan(ctx, "BasicAcquirerDB_GetByID")
	defer span.End()

	var acquirer *model.Acquirer

	err = b.db.WithContext(ctx).
		Model(&model.Acquirer{}).
		Where("id = ?", acquirerID).
		First(&acquirer).
		Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, goerr.ErrAcquirerNotFound.WithCtx(ctx)
		}

		return nil, goerr.ErrDbUnexpected.WithErr(err).WithCtx(ctx)
	}

	return acquirer, nil
}
