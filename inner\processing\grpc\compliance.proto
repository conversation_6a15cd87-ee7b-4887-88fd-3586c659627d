edition = "2023";

package processing.compliance.compliance;

option go_package = "git.local/sensitive/innerpb/processing/grpc";

import "google/protobuf/empty.proto";

service Compliance {
  //jobs
  rpc UpdateSanctionFinanciersList(google.protobuf.Empty) returns (google.protobuf.Empty){}
  rpc UpdateSanctionInvolvedList(google.protobuf.Empty) returns (google.protobuf.Empty){}
  rpc UpdateSanctionUNSCList(google.protobuf.Empty) returns (google.protobuf.Empty){}
  rpc UpdateSanctionWMDList(google.protobuf.Empty) returns (google.protobuf.Empty){}
}

message TestRequestV1 {
    string name = 1;
}