edition = "2023";

package processing.account.account;

option go_package = "git.local/sensitive/innerpb/processing/grpc";

import "google/protobuf/struct.proto";
import "google/protobuf/empty.proto";


service Account {
  rpc GetTransferByIDV1(GetTransferByIDRequestV1) returns (GetTransferByIDResponseV1) {}
  rpc GetAccountByID(GetAccountByIDRequestV1) returns (AccountData) {}
  rpc CreateTransfer(CreateTransferRequest) returns (CreateTransferResponse) {}
  rpc ExtractFailOrCanceledTransfersByForeignIDs(ExtractFailOrCanceledTransfersByForeignIDsRequest) returns (ExtractTransfersByForeignIDsResponse) {}
  rpc ExtractSuccessTransfersByForeignIDs(ExtractSuccessTransfersByForeignIDsRequest) returns (ExtractTransfersByForeignIDsResponse) {}
  rpc GetTransferByForeignID(GetTransferByForeignIDRequest) returns (GetTransferResponse) {}
  rpc GetSuccessTransferStatus(google.protobuf.Empty) returns (GetStatusResponse) {}
  rpc GetAccountByNumber(GetAccountByNumberRequest) returns (GetAccountByNumberResponse) {}


  //jobs
  rpc StartFinalizingOutTransfersWorker (google.protobuf.Empty) returns (google.protobuf.Empty) {}
  rpc StartParsingIncomingTransfersWorker(google.protobuf.Empty) returns (google.protobuf.Empty) {}
  rpc StartProcessingAccountStatementWorker (google.protobuf.Empty) returns (google.protobuf.Empty) {}
  rpc StartFinalizingOldTransfersWorker(google.protobuf.Empty) returns (google.protobuf.Empty) {}
}

message DateTime {
  string year = 1;
  string month = 2;
  string day = 3;
  string hour = 4;
  string minute = 5;
  string second = 6;
}

message AccountData {
  uint64 id = 1;
  string number = 2;
  string currency_code = 3;
  google.protobuf.Struct config = 4;
  string bank_code = 5;
  uint64 bank_id = 6;
}

// requests
message GetAccountByIDRequestV1 {
  uint64 account_id = 1;
}

message GetAccountByNumberRequest {
  string account_number = 1;
}

// responses
message GetAccountByNumberResponse {
  string created_at = 1;
  string updated_at = 2;
  uint64 account_id = 3;
  string account_number = 4;
  string currency_code = 5;
  string encrypted_config = 6;
  string bank_code = 7;
  uint64 bank_id = 8;
}

message GetTransferByIDRequestV1{
  uint64 transfer_id = 1;
}

message GetTransferByIDResponseV1{
  uint64 transfer_id = 1;
  string account_number = 2;
  uint64 project_id = 3;
  uint64 merchant_id = 4;
  string status_code = 5;
  double amount = 6;
}

message CreateTransferRequest {
  uint64 balance_owner_id = 1;
  string balance_owner_bin = 2;
  string account_number = 5;
  string recipient_account = 6;
  string payment_purpose_code = 7;
  string beneficiary_code = 8;
  double amount = 9;
  string description = 10;
  string foreign_id = 11;
}

message CreateTransferResponse {
  uint64 id = 1;
}

message ExtractFailOrCanceledTransfersByForeignIDsRequest {
  repeated string foreign_ids = 1;
}

message ExtractSuccessTransfersByForeignIDsRequest {
  repeated string foreign_ids = 1;
}

message ExtractTransfersByForeignIDsResponse {
  repeated string foreign_ids = 1;
}

message GetTransferByForeignIDRequest {
  string foreign_id = 1;
}

message GetTransferResponse {
  uint64 id = 1;
  uint64 acquirer_id = 2;
  uint64 account_id = 3;
  uint64 project_id = 4;
  uint64 merchant_id = 5;
  GetStatusResponse status = 6;
  double amount = 7;
  string merchant_bin = 8;
  string merchant_account = 9;
  string beneficiary_code = 10;
  string description = 11;
  string payment_purpose_code = 12;
  uint64 transfer_type_id = 13;
  uint64 entity_type_id = 14;
  uint64 country_code_id = 15;
  string foreign_id = 16;
}

message GetStatusResponse {
  uint64 id = 1;
  string code = 2;
  string name = 3;
}
