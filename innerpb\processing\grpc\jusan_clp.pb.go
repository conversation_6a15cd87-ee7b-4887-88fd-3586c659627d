// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.5
// 	protoc        v5.29.3
// source: inner/processing/grpc/jusan_clp.proto

package grpc

import (
	_ "git.local/sensitive/mvp/pb"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	descriptorpb "google.golang.org/protobuf/types/descriptorpb"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type JusanResponseCodeClp int32

const (
	JusanResponseCodeClp_ClpResponseUnavailable     JusanResponseCodeClp = 0
	JusanResponseCodeClp_ClpIncorrectOrder          JusanResponseCodeClp = 1
	JusanResponseCodeClp_ClpIncorrectAmount         JusanResponseCodeClp = 2
	JusanResponseCodeClp_ClpIncorrectCurrency       JusanResponseCodeClp = 3
	JusanResponseCodeClp_ClpMPIUnavailable          JusanResponseCodeClp = 4
	JusanResponseCodeClp_ClpDbUnavailable           JusanResponseCodeClp = 5
	JusanResponseCodeClp_ClpForbiddenMerchant       JusanResponseCodeClp = 6
	JusanResponseCodeClp_ClpMerchantForbidden       JusanResponseCodeClp = 7
	JusanResponseCodeClp_ClpRequestAlreadyCompleted JusanResponseCodeClp = 8
	JusanResponseCodeClp_ClpIncorrectCardExpDate    JusanResponseCodeClp = 9
	JusanResponseCodeClp_ClpIncorrectTerminal       JusanResponseCodeClp = 10
	JusanResponseCodeClp_ClpInvalidSign             JusanResponseCodeClp = 11
	JusanResponseCodeClp_ClpCurrencyNotFound        JusanResponseCodeClp = 12
	JusanResponseCodeClp_ClpLimitExceeds            JusanResponseCodeClp = 13
	JusanResponseCodeClp_ClpEmptyField              JusanResponseCodeClp = 14
	JusanResponseCodeClp_ClpSizeLessSymbols         JusanResponseCodeClp = 15
	JusanResponseCodeClp_ClpSizeMoreSymbols         JusanResponseCodeClp = 16
	JusanResponseCodeClp_ClpInvalidValue            JusanResponseCodeClp = 17
	JusanResponseCodeClp_ClpMPIError3DS             JusanResponseCodeClp = 18
	JusanResponseCodeClp_ClpInvalidCardType         JusanResponseCodeClp = 19
	JusanResponseCodeClp_ClpPaymentNotFound         JusanResponseCodeClp = 20
	JusanResponseCodeClp_ClpEmptyClientKey          JusanResponseCodeClp = 21
	JusanResponseCodeClp_ClpForbiddenTerminal       JusanResponseCodeClp = 22
	JusanResponseCodeClp_ClpTokenNotFound           JusanResponseCodeClp = 23
	JusanResponseCodeClp_ClpIncorrectBlockAmount    JusanResponseCodeClp = 24
	JusanResponseCodeClp_ClpUnknownError            JusanResponseCodeClp = 25
	JusanResponseCodeClp_ClpUnavailableService      JusanResponseCodeClp = 26
	JusanResponseCodeClp_ClpAmountIncorrect         JusanResponseCodeClp = 27
	JusanResponseCodeClp_ClpUnavailableDb           JusanResponseCodeClp = 28
	JusanResponseCodeClp_ClpIncorrectMerchant       JusanResponseCodeClp = 29
	JusanResponseCodeClp_ClpMerchantNotFound        JusanResponseCodeClp = 30
	JusanResponseCodeClp_ClpOrderNotFound           JusanResponseCodeClp = 31
	JusanResponseCodeClp_ClpSignInvalid             JusanResponseCodeClp = 32
	JusanResponseCodeClp_ClpRefundAmountIncorrect   JusanResponseCodeClp = 33
	JusanResponseCodeClp_ClpIncorrectStatus         JusanResponseCodeClp = 34
	JusanResponseCodeClp_ClpIncorrectValue          JusanResponseCodeClp = 35
	JusanResponseCodeClp_ClpTerminalForbidden       JusanResponseCodeClp = 36
	JusanResponseCodeClp_ClpForbiddenOperation      JusanResponseCodeClp = 37
	JusanResponseCodeClp_ClpDuplicateDescription    JusanResponseCodeClp = 38
	JusanResponseCodeClp_ClpRefundHandleError       JusanResponseCodeClp = 39
	JusanResponseCodeClp_ClpPaymentError            JusanResponseCodeClp = 40
	JusanResponseCodeClp_ClpPaymentExpired          JusanResponseCodeClp = 41
	JusanResponseCodeClp_ClpAuthError               JusanResponseCodeClp = 43
	JusanResponseCodeClp_ClpIncorrectCardNum        JusanResponseCodeClp = 44
	JusanResponseCodeClp_ClpSuspectedFraud          JusanResponseCodeClp = 45
	JusanResponseCodeClp_ClpUndefinedError          JusanResponseCodeClp = 47
	JusanResponseCodeClp_ClpExceedsLimit            JusanResponseCodeClp = 49
	JusanResponseCodeClp_ClpLimitedCard             JusanResponseCodeClp = 50
	JusanResponseCodeClp_ClpTransactionDeclined     JusanResponseCodeClp = 51
	JusanResponseCodeClp_ClpNotEnough               JusanResponseCodeClp = 52
	JusanResponseCodeClp_ClpLimitExceeded           JusanResponseCodeClp = 53
	JusanResponseCodeClp_ClpErrorAuth               JusanResponseCodeClp = 54
	JusanResponseCodeClp_ClpCardInactive            JusanResponseCodeClp = 55
)

// Enum value maps for JusanResponseCodeClp.
var (
	JusanResponseCodeClp_name = map[int32]string{
		0:  "ClpResponseUnavailable",
		1:  "ClpIncorrectOrder",
		2:  "ClpIncorrectAmount",
		3:  "ClpIncorrectCurrency",
		4:  "ClpMPIUnavailable",
		5:  "ClpDbUnavailable",
		6:  "ClpForbiddenMerchant",
		7:  "ClpMerchantForbidden",
		8:  "ClpRequestAlreadyCompleted",
		9:  "ClpIncorrectCardExpDate",
		10: "ClpIncorrectTerminal",
		11: "ClpInvalidSign",
		12: "ClpCurrencyNotFound",
		13: "ClpLimitExceeds",
		14: "ClpEmptyField",
		15: "ClpSizeLessSymbols",
		16: "ClpSizeMoreSymbols",
		17: "ClpInvalidValue",
		18: "ClpMPIError3DS",
		19: "ClpInvalidCardType",
		20: "ClpPaymentNotFound",
		21: "ClpEmptyClientKey",
		22: "ClpForbiddenTerminal",
		23: "ClpTokenNotFound",
		24: "ClpIncorrectBlockAmount",
		25: "ClpUnknownError",
		26: "ClpUnavailableService",
		27: "ClpAmountIncorrect",
		28: "ClpUnavailableDb",
		29: "ClpIncorrectMerchant",
		30: "ClpMerchantNotFound",
		31: "ClpOrderNotFound",
		32: "ClpSignInvalid",
		33: "ClpRefundAmountIncorrect",
		34: "ClpIncorrectStatus",
		35: "ClpIncorrectValue",
		36: "ClpTerminalForbidden",
		37: "ClpForbiddenOperation",
		38: "ClpDuplicateDescription",
		39: "ClpRefundHandleError",
		40: "ClpPaymentError",
		41: "ClpPaymentExpired",
		43: "ClpAuthError",
		44: "ClpIncorrectCardNum",
		45: "ClpSuspectedFraud",
		47: "ClpUndefinedError",
		49: "ClpExceedsLimit",
		50: "ClpLimitedCard",
		51: "ClpTransactionDeclined",
		52: "ClpNotEnough",
		53: "ClpLimitExceeded",
		54: "ClpErrorAuth",
		55: "ClpCardInactive",
	}
	JusanResponseCodeClp_value = map[string]int32{
		"ClpResponseUnavailable":     0,
		"ClpIncorrectOrder":          1,
		"ClpIncorrectAmount":         2,
		"ClpIncorrectCurrency":       3,
		"ClpMPIUnavailable":          4,
		"ClpDbUnavailable":           5,
		"ClpForbiddenMerchant":       6,
		"ClpMerchantForbidden":       7,
		"ClpRequestAlreadyCompleted": 8,
		"ClpIncorrectCardExpDate":    9,
		"ClpIncorrectTerminal":       10,
		"ClpInvalidSign":             11,
		"ClpCurrencyNotFound":        12,
		"ClpLimitExceeds":            13,
		"ClpEmptyField":              14,
		"ClpSizeLessSymbols":         15,
		"ClpSizeMoreSymbols":         16,
		"ClpInvalidValue":            17,
		"ClpMPIError3DS":             18,
		"ClpInvalidCardType":         19,
		"ClpPaymentNotFound":         20,
		"ClpEmptyClientKey":          21,
		"ClpForbiddenTerminal":       22,
		"ClpTokenNotFound":           23,
		"ClpIncorrectBlockAmount":    24,
		"ClpUnknownError":            25,
		"ClpUnavailableService":      26,
		"ClpAmountIncorrect":         27,
		"ClpUnavailableDb":           28,
		"ClpIncorrectMerchant":       29,
		"ClpMerchantNotFound":        30,
		"ClpOrderNotFound":           31,
		"ClpSignInvalid":             32,
		"ClpRefundAmountIncorrect":   33,
		"ClpIncorrectStatus":         34,
		"ClpIncorrectValue":          35,
		"ClpTerminalForbidden":       36,
		"ClpForbiddenOperation":      37,
		"ClpDuplicateDescription":    38,
		"ClpRefundHandleError":       39,
		"ClpPaymentError":            40,
		"ClpPaymentExpired":          41,
		"ClpAuthError":               43,
		"ClpIncorrectCardNum":        44,
		"ClpSuspectedFraud":          45,
		"ClpUndefinedError":          47,
		"ClpExceedsLimit":            49,
		"ClpLimitedCard":             50,
		"ClpTransactionDeclined":     51,
		"ClpNotEnough":               52,
		"ClpLimitExceeded":           53,
		"ClpErrorAuth":               54,
		"ClpCardInactive":            55,
	}
)

func (x JusanResponseCodeClp) Enum() *JusanResponseCodeClp {
	p := new(JusanResponseCodeClp)
	*p = x
	return p
}

func (x JusanResponseCodeClp) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (JusanResponseCodeClp) Descriptor() protoreflect.EnumDescriptor {
	return file_inner_processing_grpc_jusan_clp_proto_enumTypes[0].Descriptor()
}

func (JusanResponseCodeClp) Type() protoreflect.EnumType {
	return &file_inner_processing_grpc_jusan_clp_proto_enumTypes[0]
}

func (x JusanResponseCodeClp) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use JusanResponseCodeClp.Descriptor instead.
func (JusanResponseCodeClp) EnumDescriptor() ([]byte, []int) {
	return file_inner_processing_grpc_jusan_clp_proto_rawDescGZIP(), []int{0}
}

type JusanResponseCodeClpRef struct {
	state             protoimpl.MessageState `protogen:"open.v1"`
	Code              *string                `protobuf:"bytes,1,opt,name=code" json:"code,omitempty"`
	Message           *string                `protobuf:"bytes,2,opt,name=message" json:"message,omitempty"`
	TransactionStatus *EnumTransactionStatus `protobuf:"varint,3,opt,name=transaction_status,json=transactionStatus,enum=processing.transaction.transaction_status.EnumTransactionStatus" json:"transaction_status,omitempty"`
	IntegrationError  *IntegrationError      `protobuf:"varint,4,opt,name=integration_error,json=integrationError,enum=processing.integration.integration.IntegrationError" json:"integration_error,omitempty"`
	unknownFields     protoimpl.UnknownFields
	sizeCache         protoimpl.SizeCache
}

func (x *JusanResponseCodeClpRef) Reset() {
	*x = JusanResponseCodeClpRef{}
	mi := &file_inner_processing_grpc_jusan_clp_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *JusanResponseCodeClpRef) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*JusanResponseCodeClpRef) ProtoMessage() {}

func (x *JusanResponseCodeClpRef) ProtoReflect() protoreflect.Message {
	mi := &file_inner_processing_grpc_jusan_clp_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use JusanResponseCodeClpRef.ProtoReflect.Descriptor instead.
func (*JusanResponseCodeClpRef) Descriptor() ([]byte, []int) {
	return file_inner_processing_grpc_jusan_clp_proto_rawDescGZIP(), []int{0}
}

func (x *JusanResponseCodeClpRef) GetCode() string {
	if x != nil && x.Code != nil {
		return *x.Code
	}
	return ""
}

func (x *JusanResponseCodeClpRef) GetMessage() string {
	if x != nil && x.Message != nil {
		return *x.Message
	}
	return ""
}

func (x *JusanResponseCodeClpRef) GetTransactionStatus() EnumTransactionStatus {
	if x != nil && x.TransactionStatus != nil {
		return *x.TransactionStatus
	}
	return EnumTransactionStatus_Unknown
}

func (x *JusanResponseCodeClpRef) GetIntegrationError() IntegrationError {
	if x != nil && x.IntegrationError != nil {
		return *x.IntegrationError
	}
	return IntegrationError_None
}

var file_inner_processing_grpc_jusan_clp_proto_extTypes = []protoimpl.ExtensionInfo{
	{
		ExtendedType:  (*descriptorpb.EnumValueOptions)(nil),
		ExtensionType: (*JusanResponseCodeClpRef)(nil),
		Field:         100121,
		Name:          "processing.jusan_tokenize.jusan_tokenize.jusan_response_code_clp_value",
		Tag:           "bytes,100121,opt,name=jusan_response_code_clp_value",
		Filename:      "inner/processing/grpc/jusan_clp.proto",
	},
	{
		ExtendedType:  (*descriptorpb.EnumOptions)(nil),
		ExtensionType: (*JusanResponseCodeClpRef)(nil),
		Field:         100122,
		Name:          "processing.jusan_tokenize.jusan_tokenize.default_jusan_response_code_clp_value",
		Tag:           "bytes,100122,opt,name=default_jusan_response_code_clp_value",
		Filename:      "inner/processing/grpc/jusan_clp.proto",
	},
}

// Extension fields to descriptorpb.EnumValueOptions.
var (
	// optional processing.jusan_tokenize.jusan_tokenize.JusanResponseCodeClpRef jusan_response_code_clp_value = 100121;
	E_JusanResponseCodeClpValue = &file_inner_processing_grpc_jusan_clp_proto_extTypes[0]
)

// Extension fields to descriptorpb.EnumOptions.
var (
	// optional processing.jusan_tokenize.jusan_tokenize.JusanResponseCodeClpRef default_jusan_response_code_clp_value = 100122;
	E_DefaultJusanResponseCodeClpValue = &file_inner_processing_grpc_jusan_clp_proto_extTypes[1]
)

var File_inner_processing_grpc_jusan_clp_proto protoreflect.FileDescriptor

var file_inner_processing_grpc_jusan_clp_proto_rawDesc = string([]byte{
	0x0a, 0x25, 0x69, 0x6e, 0x6e, 0x65, 0x72, 0x2f, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69,
	0x6e, 0x67, 0x2f, 0x67, 0x72, 0x70, 0x63, 0x2f, 0x6a, 0x75, 0x73, 0x61, 0x6e, 0x5f, 0x63, 0x6c,
	0x70, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x28, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73,
	0x69, 0x6e, 0x67, 0x2e, 0x6a, 0x75, 0x73, 0x61, 0x6e, 0x5f, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x69,
	0x7a, 0x65, 0x2e, 0x6a, 0x75, 0x73, 0x61, 0x6e, 0x5f, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x69, 0x7a,
	0x65, 0x1a, 0x2e, 0x69, 0x6e, 0x6e, 0x65, 0x72, 0x2f, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73,
	0x69, 0x6e, 0x67, 0x2f, 0x67, 0x72, 0x70, 0x63, 0x2f, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63,
	0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x1a, 0x27, 0x69, 0x6e, 0x6e, 0x65, 0x72, 0x2f, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73,
	0x69, 0x6e, 0x67, 0x2f, 0x67, 0x72, 0x70, 0x63, 0x2f, 0x69, 0x6e, 0x74, 0x65, 0x67, 0x72, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x14, 0x6d, 0x76, 0x70, 0x2f,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f, 0x72, 0x65, 0x66, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x1a, 0x14, 0x6d, 0x76, 0x70, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f, 0x65, 0x6e, 0x75, 0x6d,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x20, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74,
	0x6f, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x9b, 0x02, 0x0a, 0x17, 0x4a, 0x75, 0x73,
	0x61, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x43, 0x6f, 0x64, 0x65, 0x43, 0x6c,
	0x70, 0x52, 0x65, 0x66, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x6d, 0x65, 0x73, 0x73,
	0x61, 0x67, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6d, 0x65, 0x73, 0x73, 0x61,
	0x67, 0x65, 0x12, 0x6f, 0x0a, 0x12, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f,
	0x6e, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x40,
	0x2e, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x2e, 0x74, 0x72, 0x61, 0x6e,
	0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74,
	0x69, 0x6f, 0x6e, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x2e, 0x45, 0x6e, 0x75, 0x6d, 0x54,
	0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x52, 0x11, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x12, 0x61, 0x0a, 0x11, 0x69, 0x6e, 0x74, 0x65, 0x67, 0x72, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x5f, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x34,
	0x2e, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x2e, 0x69, 0x6e, 0x74, 0x65,
	0x67, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x69, 0x6e, 0x74, 0x65, 0x67, 0x72, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x2e, 0x49, 0x6e, 0x74, 0x65, 0x67, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x45,
	0x72, 0x72, 0x6f, 0x72, 0x52, 0x10, 0x69, 0x6e, 0x74, 0x65, 0x67, 0x72, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x2a, 0x97, 0x2d, 0x0a, 0x14, 0x4a, 0x75, 0x73, 0x61, 0x6e,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x43, 0x6f, 0x64, 0x65, 0x43, 0x6c, 0x70, 0x12,
	0x85, 0x01, 0x0a, 0x16, 0x43, 0x6c, 0x70, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x55,
	0x6e, 0x61, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x6c, 0x65, 0x10, 0x00, 0x1a, 0x69, 0xca, 0xf1,
	0x30, 0x5d, 0x0a, 0x02, 0x31, 0x31, 0x12, 0x53, 0xd0, 0xa1, 0xd0, 0xb5, 0xd1, 0x80, 0xd0, 0xb2,
	0xd0, 0xb8, 0xd1, 0x81, 0x20, 0xd0, 0xb2, 0xd1, 0x80, 0xd0, 0xb5, 0xd0, 0xbc, 0xd0, 0xb5, 0xd0,
	0xbd, 0xd0, 0xbd, 0xd0, 0xbe, 0x20, 0xd0, 0xbd, 0xd0, 0xb5, 0xd0, 0xb4, 0xd0, 0xbe, 0xd1, 0x81,
	0xd1, 0x82, 0xd1, 0x83, 0xd0, 0xbf, 0xd0, 0xb5, 0xd0, 0xbd, 0x2c, 0x20, 0xd0, 0xbf, 0xd0, 0xbe,
	0xd0, 0xbf, 0xd1, 0x80, 0xd0, 0xbe, 0xd0, 0xb1, 0xd1, 0x83, 0xd0, 0xb9, 0xd1, 0x82, 0xd0, 0xb5,
	0x20, 0xd0, 0xbf, 0xd0, 0xbe, 0xd0, 0xb7, 0xd0, 0xb6, 0xd0, 0xb5, 0x18, 0x05, 0x20, 0x6c, 0xca,
	0xa1, 0xec, 0x8e, 0x02, 0x02, 0x31, 0x31, 0x12, 0x69, 0x0a, 0x11, 0x43, 0x6c, 0x70, 0x49, 0x6e,
	0x63, 0x6f, 0x72, 0x72, 0x65, 0x63, 0x74, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x10, 0x01, 0x1a, 0x52,
	0xca, 0xf1, 0x30, 0x46, 0x0a, 0x02, 0x31, 0x32, 0x12, 0x3c, 0xd0, 0x9d, 0xd0, 0xb5, 0xd0, 0xbf,
	0xd1, 0x80, 0xd0, 0xb0, 0xd0, 0xb2, 0xd0, 0xb8, 0xd0, 0xbb, 0xd1, 0x8c, 0xd0, 0xbd, 0xd0, 0xbe,
	0xd0, 0xb5, 0x20, 0xd0, 0xb7, 0xd0, 0xbd, 0xd0, 0xb0, 0xd1, 0x87, 0xd0, 0xb5, 0xd0, 0xbd, 0xd0,
	0xb8, 0xd0, 0xb5, 0x20, 0xd0, 0xb2, 0x20, 0xd0, 0xbf, 0xd0, 0xbe, 0xd0, 0xbb, 0xd0, 0xb5, 0x20,
	0x4f, 0x52, 0x44, 0x45, 0x52, 0x3a, 0x18, 0x05, 0x20, 0x6a, 0xca, 0xa1, 0xec, 0x8e, 0x02, 0x02,
	0x31, 0x32, 0x12, 0x53, 0x0a, 0x12, 0x43, 0x6c, 0x70, 0x49, 0x6e, 0x63, 0x6f, 0x72, 0x72, 0x65,
	0x63, 0x74, 0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x10, 0x02, 0x1a, 0x3b, 0xca, 0xf1, 0x30, 0x2f,
	0x0a, 0x02, 0x31, 0x33, 0x12, 0x25, 0xd0, 0x9d, 0xd0, 0xb5, 0xd0, 0xbf, 0xd1, 0x80, 0xd0, 0xb0,
	0xd0, 0xb2, 0xd0, 0xb8, 0xd0, 0xbb, 0xd1, 0x8c, 0xd0, 0xbd, 0xd0, 0xb0, 0xd1, 0x8f, 0x20, 0xd1,
	0x81, 0xd1, 0x83, 0xd0, 0xbc, 0xd0, 0xbc, 0xd0, 0xb0, 0x3a, 0x20, 0x18, 0x05, 0x20, 0x6a, 0xca,
	0xa1, 0xec, 0x8e, 0x02, 0x02, 0x31, 0x33, 0x12, 0x56, 0x0a, 0x14, 0x43, 0x6c, 0x70, 0x49, 0x6e,
	0x63, 0x6f, 0x72, 0x72, 0x65, 0x63, 0x74, 0x43, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x63, 0x79, 0x10,
	0x03, 0x1a, 0x3c, 0xca, 0xf1, 0x30, 0x30, 0x0a, 0x02, 0x31, 0x34, 0x12, 0x26, 0xd0, 0x9d, 0xd0,
	0xb5, 0xd0, 0xbf, 0xd1, 0x80, 0xd0, 0xb0, 0xd0, 0xb2, 0xd0, 0xb8, 0xd0, 0xbb, 0xd1, 0x8c, 0xd0,
	0xbd, 0xd0, 0xb0, 0xd1, 0x8f, 0x20, 0xd0, 0xb2, 0xd0, 0xb0, 0xd0, 0xbb, 0xd1, 0x8e, 0xd1, 0x82,
	0xd0, 0xb0, 0x3a, 0x18, 0x05, 0x20, 0x6a, 0xca, 0xa1, 0xec, 0x8e, 0x02, 0x02, 0x31, 0x34, 0x12,
	0x84, 0x01, 0x0a, 0x11, 0x43, 0x6c, 0x70, 0x4d, 0x50, 0x49, 0x55, 0x6e, 0x61, 0x76, 0x61, 0x69,
	0x6c, 0x61, 0x62, 0x6c, 0x65, 0x10, 0x04, 0x1a, 0x6d, 0xca, 0xf1, 0x30, 0x61, 0x0a, 0x02, 0x31,
	0x35, 0x12, 0x57, 0xd0, 0xa1, 0xd0, 0xb5, 0xd1, 0x80, 0xd0, 0xb2, 0xd0, 0xb8, 0xd1, 0x81, 0x20,
	0x4d, 0x50, 0x49, 0x20, 0xd0, 0xb2, 0xd1, 0x80, 0xd0, 0xb5, 0xd0, 0xbc, 0xd0, 0xb5, 0xd0, 0xbd,
	0xd0, 0xbd, 0xd0, 0xbe, 0x20, 0xd0, 0xbd, 0xd0, 0xb5, 0xd0, 0xb4, 0xd0, 0xbe, 0xd1, 0x81, 0xd1,
	0x82, 0xd1, 0x83, 0xd0, 0xbf, 0xd0, 0xb5, 0xd0, 0xbd, 0x2c, 0x20, 0xd0, 0xbf, 0xd0, 0xbe, 0xd0,
	0xbf, 0xd1, 0x80, 0xd0, 0xbe, 0xd0, 0xb1, 0xd1, 0x83, 0xd0, 0xb9, 0xd1, 0x82, 0xd0, 0xb5, 0x20,
	0xd0, 0xbf, 0xd0, 0xbe, 0xd0, 0xb7, 0xd0, 0xb6, 0xd0, 0xb5, 0x18, 0x05, 0x20, 0x6c, 0xca, 0xa1,
	0xec, 0x8e, 0x02, 0x02, 0x31, 0x35, 0x12, 0x82, 0x01, 0x0a, 0x10, 0x43, 0x6c, 0x70, 0x44, 0x62,
	0x55, 0x6e, 0x61, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x6c, 0x65, 0x10, 0x05, 0x1a, 0x6c, 0xca,
	0xf1, 0x30, 0x60, 0x0a, 0x02, 0x31, 0x36, 0x12, 0x56, 0xd0, 0xa1, 0xd0, 0xb5, 0xd1, 0x80, 0xd0,
	0xb2, 0xd0, 0xb8, 0xd1, 0x81, 0x20, 0x44, 0x62, 0x20, 0xd0, 0xb2, 0xd1, 0x80, 0xd0, 0xb5, 0xd0,
	0xbc, 0xd0, 0xb5, 0xd0, 0xbd, 0xd0, 0xbd, 0xd0, 0xbe, 0x20, 0xd0, 0xbd, 0xd0, 0xb5, 0xd0, 0xb4,
	0xd0, 0xbe, 0xd1, 0x81, 0xd1, 0x82, 0xd1, 0x83, 0xd0, 0xbf, 0xd0, 0xb5, 0xd0, 0xbd, 0x2c, 0x20,
	0xd0, 0xbf, 0xd0, 0xbe, 0xd0, 0xbf, 0xd1, 0x80, 0xd0, 0xbe, 0xd0, 0xb1, 0xd1, 0x83, 0xd0, 0xb9,
	0xd1, 0x82, 0xd0, 0xb5, 0x20, 0xd0, 0xbf, 0xd0, 0xbe, 0xd0, 0xb7, 0xd0, 0xb6, 0xd0, 0xb5, 0x18,
	0x05, 0x20, 0x6c, 0xca, 0xa1, 0xec, 0x8e, 0x02, 0x02, 0x31, 0x36, 0x12, 0x81, 0x01, 0x0a, 0x14,
	0x43, 0x6c, 0x70, 0x46, 0x6f, 0x72, 0x62, 0x69, 0x64, 0x64, 0x65, 0x6e, 0x4d, 0x65, 0x72, 0x63,
	0x68, 0x61, 0x6e, 0x74, 0x10, 0x06, 0x1a, 0x67, 0xca, 0xf1, 0x30, 0x5a, 0x0a, 0x03, 0x31, 0x37,
	0x31, 0x12, 0x4f, 0xd0, 0x9a, 0xd0, 0xbe, 0xd0, 0xbc, 0xd0, 0xbc, 0xd0, 0xb5, 0xd1, 0x80, 0xd1,
	0x81, 0xd0, 0xb0, 0xd0, 0xbd, 0xd1, 0x82, 0xd1, 0x83, 0x20, 0xd0, 0xb7, 0xd0, 0xb0, 0xd0, 0xbf,
	0xd1, 0x80, 0xd0, 0xb5, 0xd1, 0x89, 0xd0, 0xb5, 0xd0, 0xbd, 0xd0, 0xbe, 0x20, 0xd0, 0xb2, 0xd1,
	0x8b, 0xd0, 0xbf, 0xd0, 0xbe, 0xd0, 0xbb, 0xd0, 0xbd, 0xd0, 0xb5, 0xd0, 0xbd, 0xd0, 0xb8, 0xd0,
	0xb5, 0x20, 0xd0, 0xbe, 0xd0, 0xbf, 0xd0, 0xb5, 0xd1, 0x80, 0xd0, 0xb0, 0xd1, 0x86, 0xd0, 0xb8,
	0xd0, 0xb9, 0x18, 0x05, 0x20, 0x6d, 0xca, 0xa1, 0xec, 0x8e, 0x02, 0x03, 0x31, 0x37, 0x31, 0x12,
	0xc3, 0x01, 0x0a, 0x14, 0x43, 0x6c, 0x70, 0x4d, 0x65, 0x72, 0x63, 0x68, 0x61, 0x6e, 0x74, 0x46,
	0x6f, 0x72, 0x62, 0x69, 0x64, 0x64, 0x65, 0x6e, 0x10, 0x07, 0x1a, 0xa8, 0x01, 0xca, 0xf1, 0x30,
	0x99, 0x01, 0x0a, 0x04, 0x31, 0x37, 0x32, 0x20, 0x12, 0x8c, 0x01, 0xd0, 0x9a, 0xd0, 0xbe, 0xd0,
	0xbc, 0xd0, 0xbc, 0xd0, 0xb5, 0xd1, 0x80, 0xd1, 0x81, 0xd0, 0xb0, 0xd0, 0xbd, 0xd1, 0x82, 0xd1,
	0x83, 0x20, 0xd0, 0xb7, 0xd0, 0xb0, 0xd0, 0xbf, 0xd1, 0x80, 0xd0, 0xb5, 0xd1, 0x89, 0xd0, 0xb5,
	0xd0, 0xbd, 0xd0, 0xbe, 0x20, 0xd0, 0xbf, 0xd1, 0x80, 0xd0, 0xbe, 0xd0, 0xb2, 0xd0, 0xb5, 0xd0,
	0xb4, 0xd0, 0xb5, 0xd0, 0xbd, 0xd0, 0xb8, 0xd0, 0xb5, 0x20, 0xd0, 0xbe, 0xd0, 0xbf, 0xd0, 0xb5,
	0xd1, 0x80, 0xd0, 0xb0, 0xd1, 0x86, 0xd0, 0xb8, 0xd0, 0xb9, 0x20, 0xd0, 0xb2, 0x20, 0xd1, 0x81,
	0xd0, 0xbe, 0xd0, 0xbe, 0xd1, 0x82, 0xd0, 0xb2, 0xd0, 0xb5, 0xd1, 0x82, 0xd1, 0x81, 0xd1, 0x82,
	0xd0, 0xb2, 0xd0, 0xb8, 0xd0, 0xb8, 0x20, 0xd1, 0x81, 0x20, 0xd0, 0x97, 0xd0, 0xb0, 0xd0, 0xba,
	0xd0, 0xbe, 0xd0, 0xbd, 0xd0, 0xbe, 0xd0, 0xbc, 0x20, 0xd0, 0xbe, 0x20, 0xd0, 0x9f, 0xd0, 0x9e,
	0xd0, 0x94, 0x2f, 0xd0, 0xa4, 0xd0, 0xa2, 0x18, 0x05, 0x20, 0x6d, 0xca, 0xa1, 0xec, 0x8e, 0x02,
	0x04, 0x31, 0x37, 0x32, 0x20, 0x12, 0x5e, 0x0a, 0x1a, 0x43, 0x6c, 0x70, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x41, 0x6c, 0x72, 0x65, 0x61, 0x64, 0x79, 0x43, 0x6f, 0x6d, 0x70, 0x6c, 0x65,
	0x74, 0x65, 0x64, 0x10, 0x08, 0x1a, 0x3e, 0xca, 0xf1, 0x30, 0x32, 0x0a, 0x02, 0x31, 0x38, 0x12,
	0x28, 0xd0, 0x97, 0xd0, 0xb0, 0xd0, 0xbf, 0xd1, 0x80, 0xd0, 0xbe, 0xd1, 0x81, 0x20, 0xd1, 0x83,
	0xd0, 0xb6, 0xd0, 0xb5, 0x20, 0xd0, 0xb2, 0xd1, 0x8b, 0xd0, 0xbf, 0xd0, 0xbe, 0xd0, 0xbb, 0xd0,
	0xbd, 0xd1, 0x8f, 0xd0, 0xbb, 0xd1, 0x81, 0xd1, 0x8f, 0x18, 0x05, 0x20, 0x6a, 0xca, 0xa1, 0xec,
	0x8e, 0x02, 0x02, 0x31, 0x38, 0x12, 0x78, 0x0a, 0x17, 0x43, 0x6c, 0x70, 0x49, 0x6e, 0x63, 0x6f,
	0x72, 0x72, 0x65, 0x63, 0x74, 0x43, 0x61, 0x72, 0x64, 0x45, 0x78, 0x70, 0x44, 0x61, 0x74, 0x65,
	0x10, 0x09, 0x1a, 0x5b, 0xca, 0xf1, 0x30, 0x4f, 0x0a, 0x02, 0x31, 0x39, 0x12, 0x45, 0xd0, 0x9d,
	0xd0, 0xb5, 0xd0, 0xbf, 0xd1, 0x80, 0xd0, 0xb0, 0xd0, 0xb2, 0xd0, 0xb8, 0xd0, 0xbb, 0xd1, 0x8c,
	0xd0, 0xbd, 0xd0, 0xb0, 0xd1, 0x8f, 0x20, 0xd0, 0xb4, 0xd0, 0xb0, 0xd1, 0x82, 0xd0, 0xb0, 0x20,
	0xd0, 0xb4, 0xd0, 0xb5, 0xd0, 0xb9, 0xd1, 0x81, 0xd1, 0x82, 0xd0, 0xb8, 0xd1, 0x8f, 0x20, 0xd0,
	0xba, 0xd0, 0xb0, 0xd1, 0x80, 0xd1, 0x82, 0xd1, 0x8b, 0x20, 0x28, 0x4d, 0x4d, 0x2f, 0xd0, 0x93,
	0xd0, 0x93, 0x29, 0x18, 0x05, 0x20, 0x75, 0xca, 0xa1, 0xec, 0x8e, 0x02, 0x02, 0x31, 0x39, 0x12,
	0x6f, 0x0a, 0x14, 0x43, 0x6c, 0x70, 0x49, 0x6e, 0x63, 0x6f, 0x72, 0x72, 0x65, 0x63, 0x74, 0x54,
	0x65, 0x72, 0x6d, 0x69, 0x6e, 0x61, 0x6c, 0x10, 0x0a, 0x1a, 0x55, 0xca, 0xf1, 0x30, 0x49, 0x0a,
	0x02, 0x32, 0x30, 0x12, 0x3f, 0xd0, 0x9d, 0xd0, 0xb5, 0xd0, 0xbf, 0xd1, 0x80, 0xd0, 0xb0, 0xd0,
	0xb2, 0xd0, 0xb8, 0xd0, 0xbb, 0xd1, 0x8c, 0xd0, 0xbd, 0xd0, 0xbe, 0xd0, 0xb5, 0x20, 0xd0, 0xb7,
	0xd0, 0xbd, 0xd0, 0xb0, 0xd1, 0x87, 0xd0, 0xb5, 0xd0, 0xbd, 0xd0, 0xb8, 0xd0, 0xb5, 0x20, 0xd0,
	0xb2, 0x20, 0xd0, 0xbf, 0xd0, 0xbe, 0xd0, 0xbb, 0xd0, 0xb5, 0x20, 0x54, 0x45, 0x52, 0x4d, 0x49,
	0x4e, 0x41, 0x4c, 0x3a, 0x18, 0x05, 0x20, 0x6a, 0xca, 0xa1, 0xec, 0x8e, 0x02, 0x02, 0x32, 0x30,
	0x12, 0x52, 0x0a, 0x0e, 0x43, 0x6c, 0x70, 0x49, 0x6e, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x53, 0x69,
	0x67, 0x6e, 0x10, 0x0b, 0x1a, 0x3e, 0xca, 0xf1, 0x30, 0x32, 0x0a, 0x02, 0x32, 0x31, 0x12, 0x28,
	0xd0, 0x9d, 0xd0, 0xb5, 0xd0, 0xbf, 0xd1, 0x80, 0xd0, 0xb0, 0xd0, 0xb2, 0xd0, 0xb8, 0xd0, 0xbb,
	0xd1, 0x8c, 0xd0, 0xbd, 0xd0, 0xb0, 0xd1, 0x8f, 0x20, 0xd0, 0xbf, 0xd0, 0xbe, 0xd0, 0xb4, 0xd0,
	0xbf, 0xd0, 0xb8, 0xd1, 0x81, 0xd1, 0x8c, 0x21, 0x18, 0x05, 0x20, 0x6a, 0xca, 0xa1, 0xec, 0x8e,
	0x02, 0x02, 0x32, 0x31, 0x12, 0x56, 0x0a, 0x13, 0x43, 0x6c, 0x70, 0x43, 0x75, 0x72, 0x72, 0x65,
	0x6e, 0x63, 0x79, 0x4e, 0x6f, 0x74, 0x46, 0x6f, 0x75, 0x6e, 0x64, 0x10, 0x0c, 0x1a, 0x3d, 0xca,
	0xf1, 0x30, 0x31, 0x0a, 0x02, 0x32, 0x32, 0x12, 0x27, 0xd0, 0x9d, 0xd0, 0xb5, 0x20, 0xd0, 0xbd,
	0xd0, 0xb0, 0xd0, 0xb9, 0xd0, 0xb4, 0xd0, 0xb5, 0xd0, 0xbd, 0x20, 0xd0, 0xba, 0xd1, 0x83, 0xd1,
	0x80, 0xd1, 0x81, 0x20, 0xd0, 0xb2, 0xd0, 0xb0, 0xd0, 0xbb, 0xd1, 0x8e, 0xd1, 0x82, 0xd1, 0x8b,
	0x18, 0x05, 0x20, 0x6a, 0xca, 0xa1, 0xec, 0x8e, 0x02, 0x02, 0x32, 0x32, 0x12, 0x47, 0x0a, 0x0f,
	0x43, 0x6c, 0x70, 0x4c, 0x69, 0x6d, 0x69, 0x74, 0x45, 0x78, 0x63, 0x65, 0x65, 0x64, 0x73, 0x10,
	0x0d, 0x1a, 0x32, 0xca, 0xf1, 0x30, 0x26, 0x0a, 0x02, 0x32, 0x33, 0x12, 0x1c, 0xd0, 0x9f, 0xd1,
	0x80, 0xd0, 0xb5, 0xd0, 0xb2, 0xd1, 0x8b, 0xd1, 0x88, 0xd0, 0xb5, 0xd0, 0xbd, 0x20, 0xd0, 0xbb,
	0xd0, 0xb8, 0xd0, 0xbc, 0xd0, 0xb8, 0xd1, 0x82, 0x21, 0x18, 0x05, 0x20, 0x67, 0xca, 0xa1, 0xec,
	0x8e, 0x02, 0x02, 0x32, 0x33, 0x12, 0x59, 0x0a, 0x0d, 0x43, 0x6c, 0x70, 0x45, 0x6d, 0x70, 0x74,
	0x79, 0x46, 0x69, 0x65, 0x6c, 0x64, 0x10, 0x0e, 0x1a, 0x46, 0xca, 0xf1, 0x30, 0x3a, 0x0a, 0x02,
	0x32, 0x34, 0x12, 0x30, 0xd0, 0x9d, 0xd0, 0xb5, 0x20, 0xd1, 0x83, 0xd0, 0xba, 0xd0, 0xb0, 0xd0,
	0xb7, 0xd0, 0xb0, 0xd0, 0xbd, 0xd0, 0xbe, 0x20, 0xd0, 0xb7, 0xd0, 0xbd, 0xd0, 0xb0, 0xd1, 0x87,
	0xd0, 0xb5, 0xd0, 0xbd, 0xd0, 0xb8, 0xd0, 0xb5, 0x20, 0xd0, 0xb2, 0x20, 0xd0, 0xbf, 0xd0, 0xbe,
	0xd0, 0xbb, 0xd0, 0xb5, 0x18, 0x05, 0x20, 0x6a, 0xca, 0xa1, 0xec, 0x8e, 0x02, 0x02, 0x32, 0x34,
	0x12, 0x75, 0x0a, 0x12, 0x43, 0x6c, 0x70, 0x53, 0x69, 0x7a, 0x65, 0x4c, 0x65, 0x73, 0x73, 0x53,
	0x79, 0x6d, 0x62, 0x6f, 0x6c, 0x73, 0x10, 0x0f, 0x1a, 0x5d, 0xca, 0xf1, 0x30, 0x51, 0x0a, 0x02,
	0x32, 0x35, 0x12, 0x47, 0xd0, 0xa0, 0xd0, 0xb0, 0xd0, 0xb7, 0xd0, 0xbc, 0xd0, 0xb5, 0xd1, 0x80,
	0x20, 0xd0, 0xb7, 0xd0, 0xbd, 0xd0, 0xb0, 0xd1, 0x87, 0xd0, 0xb5, 0xd0, 0xbd, 0xd0, 0xb8, 0xd1,
	0x8f, 0x20, 0xd0, 0xb2, 0x20, 0xd0, 0xbf, 0xd0, 0xbe, 0xd0, 0xbb, 0xd0, 0xb5, 0x20, 0xd0, 0xbc,
	0xd0, 0xb5, 0xd0, 0xbd, 0xd0, 0xb5, 0xd0, 0xb5, 0x20, 0xd1, 0x81, 0xd0, 0xb8, 0xd0, 0xbc, 0xd0,
	0xbe, 0xd0, 0xb2, 0xd0, 0xbe, 0xd0, 0xbb, 0xd0, 0xbe, 0xd0, 0xb2, 0x18, 0x05, 0x20, 0x6a, 0xca,
	0xa1, 0xec, 0x8e, 0x02, 0x02, 0x32, 0x35, 0x12, 0x77, 0x0a, 0x12, 0x43, 0x6c, 0x70, 0x53, 0x69,
	0x7a, 0x65, 0x4d, 0x6f, 0x72, 0x65, 0x53, 0x79, 0x6d, 0x62, 0x6f, 0x6c, 0x73, 0x10, 0x10, 0x1a,
	0x5f, 0xca, 0xf1, 0x30, 0x53, 0x0a, 0x02, 0x32, 0x36, 0x12, 0x49, 0xd0, 0xa0, 0xd0, 0xb0, 0xd0,
	0xb7, 0xd0, 0xbc, 0xd0, 0xb5, 0xd1, 0x80, 0x20, 0xd0, 0xb7, 0xd0, 0xbd, 0xd0, 0xb0, 0xd1, 0x87,
	0xd0, 0xb5, 0xd0, 0xbd, 0xd0, 0xb8, 0xd1, 0x8f, 0x20, 0xd0, 0xb2, 0x20, 0xd0, 0xbf, 0xd0, 0xbe,
	0xd0, 0xbb, 0xd0, 0xb5, 0x20, 0xd0, 0xb1, 0xd0, 0xbe, 0xd0, 0xbb, 0xd1, 0x8c, 0xd1, 0x88, 0xd0,
	0xb5, 0x20, 0xd1, 0x81, 0xd0, 0xb8, 0xd0, 0xbc, 0xd0, 0xbe, 0xd0, 0xb2, 0xd0, 0xbe, 0xd0, 0xbb,
	0xd0, 0xbe, 0xd0, 0xb2, 0x18, 0x05, 0x20, 0x6a, 0xca, 0xa1, 0xec, 0x8e, 0x02, 0x02, 0x32, 0x36,
	0x12, 0x67, 0x0a, 0x0f, 0x43, 0x6c, 0x70, 0x49, 0x6e, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x56, 0x61,
	0x6c, 0x75, 0x65, 0x10, 0x11, 0x1a, 0x52, 0xca, 0xf1, 0x30, 0x46, 0x0a, 0x02, 0x32, 0x37, 0x12,
	0x3c, 0xd0, 0x92, 0xd0, 0xb2, 0xd0, 0xb5, 0xd0, 0xb4, 0xd0, 0xb8, 0xd1, 0x82, 0xd0, 0xb5, 0x20,
	0xd0, 0xb2, 0xd0, 0xb0, 0xd0, 0xbb, 0xd0, 0xb8, 0xd0, 0xb4, 0xd0, 0xbd, 0xd0, 0xbe, 0xd0, 0xb5,
	0x20, 0xd0, 0xb7, 0xd0, 0xbd, 0xd0, 0xb0, 0xd1, 0x87, 0xd0, 0xb5, 0xd0, 0xbd, 0xd0, 0xb8, 0xd0,
	0xb5, 0x20, 0xd0, 0xb2, 0x20, 0xd0, 0xbf, 0xd0, 0xbe, 0xd0, 0xbb, 0xd0, 0xb5, 0x18, 0x05, 0x20,
	0x6a, 0xca, 0xa1, 0xec, 0x8e, 0x02, 0x02, 0x32, 0x37, 0x12, 0x6c, 0x0a, 0x0e, 0x43, 0x6c, 0x70,
	0x4d, 0x50, 0x49, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x33, 0x44, 0x53, 0x10, 0x12, 0x1a, 0x58, 0xca,
	0xf1, 0x30, 0x4c, 0x0a, 0x02, 0x32, 0x38, 0x12, 0x42, 0xd0, 0x9e, 0xd1, 0x88, 0xd0, 0xb8, 0xd0,
	0xb1, 0xd0, 0xba, 0xd0, 0xb0, 0x20, 0x4d, 0x50, 0x49, 0x20, 0xd0, 0xbf, 0xd1, 0x80, 0xd0, 0xb8,
	0x20, 0xd0, 0xb2, 0xd1, 0x8b, 0xd0, 0xbf, 0xd0, 0xbe, 0xd0, 0xbb, 0xd0, 0xbd, 0xd0, 0xb5, 0xd0,
	0xbd, 0xd0, 0xb8, 0xd0, 0xb8, 0x20, 0xd0, 0xbf, 0xd1, 0x80, 0xd0, 0xbe, 0xd0, 0xb2, 0xd0, 0xb5,
	0xd1, 0x80, 0xd0, 0xba, 0xd0, 0xb8, 0x20, 0x33, 0x44, 0x53, 0x3a, 0x18, 0x05, 0x20, 0x6a, 0xca,
	0xa1, 0xec, 0x8e, 0x02, 0x02, 0x32, 0x38, 0x12, 0x58, 0x0a, 0x12, 0x43, 0x6c, 0x70, 0x49, 0x6e,
	0x76, 0x61, 0x6c, 0x69, 0x64, 0x43, 0x61, 0x72, 0x64, 0x54, 0x79, 0x70, 0x65, 0x10, 0x13, 0x1a,
	0x40, 0xca, 0xf1, 0x30, 0x34, 0x0a, 0x02, 0x32, 0x39, 0x12, 0x2a, 0xd0, 0x9d, 0xd0, 0xb5, 0xd0,
	0xb4, 0xd0, 0xbe, 0xd0, 0xbf, 0xd1, 0x83, 0xd1, 0x81, 0xd1, 0x82, 0xd0, 0xb8, 0xd0, 0xbc, 0xd1,
	0x8b, 0xd0, 0xb9, 0x20, 0xd1, 0x82, 0xd0, 0xb8, 0xd0, 0xbf, 0x20, 0xd0, 0xba, 0xd0, 0xb0, 0xd1,
	0x80, 0xd1, 0x82, 0xd1, 0x8b, 0x18, 0x05, 0x20, 0x66, 0xca, 0xa1, 0xec, 0x8e, 0x02, 0x02, 0x32,
	0x39, 0x12, 0x5a, 0x0a, 0x12, 0x43, 0x6c, 0x70, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x4e,
	0x6f, 0x74, 0x46, 0x6f, 0x75, 0x6e, 0x64, 0x10, 0x14, 0x1a, 0x42, 0xca, 0xf1, 0x30, 0x36, 0x0a,
	0x02, 0x33, 0x30, 0x12, 0x2c, 0xd0, 0xa1, 0xd1, 0x87, 0xd0, 0xb5, 0xd1, 0x82, 0x20, 0xd0, 0xbd,
	0xd0, 0xb0, 0x20, 0xd0, 0xbe, 0xd0, 0xbf, 0xd0, 0xbb, 0xd0, 0xb0, 0xd1, 0x82, 0xd1, 0x83, 0x20,
	0xd0, 0xbd, 0xd0, 0xb5, 0x20, 0xd0, 0xbd, 0xd0, 0xb0, 0xd0, 0xb9, 0xd0, 0xb4, 0xd0, 0xb5, 0xd0,
	0xbd, 0x18, 0x05, 0x20, 0x6a, 0xca, 0xa1, 0xec, 0x8e, 0x02, 0x02, 0x33, 0x30, 0x12, 0x6d, 0x0a,
	0x11, 0x43, 0x6c, 0x70, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x43, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x4b,
	0x65, 0x79, 0x10, 0x15, 0x1a, 0x56, 0xca, 0xf1, 0x30, 0x4a, 0x0a, 0x02, 0x33, 0x31, 0x12, 0x40,
	0xd0, 0x9d, 0xd0, 0xb5, 0x20, 0xd0, 0xbf, 0xd0, 0xb5, 0xd1, 0x80, 0xd0, 0xb5, 0xd0, 0xb4, 0xd0,
	0xb0, 0xd0, 0xbd, 0x20, 0xd0, 0xba, 0xd0, 0xbb, 0xd1, 0x8e, 0xd1, 0x87, 0x20, 0xd1, 0x83, 0xd0,
	0xba, 0xd0, 0xb0, 0xd0, 0xb7, 0xd0, 0xb0, 0xd0, 0xbd, 0xd0, 0xbd, 0xd0, 0xbe, 0xd0, 0xb3, 0xd0,
	0xbe, 0x20, 0xd0, 0xba, 0xd0, 0xbb, 0xd0, 0xb8, 0xd0, 0xb5, 0xd0, 0xbd, 0xd1, 0x82, 0xd0, 0xb0,
	0x18, 0x05, 0x20, 0x6a, 0xca, 0xa1, 0xec, 0x8e, 0x02, 0x02, 0x33, 0x31, 0x12, 0x73, 0x0a, 0x14,
	0x43, 0x6c, 0x70, 0x46, 0x6f, 0x72, 0x62, 0x69, 0x64, 0x64, 0x65, 0x6e, 0x54, 0x65, 0x72, 0x6d,
	0x69, 0x6e, 0x61, 0x6c, 0x10, 0x16, 0x1a, 0x59, 0xca, 0xf1, 0x30, 0x4d, 0x0a, 0x02, 0x33, 0x32,
	0x12, 0x43, 0xd0, 0x94, 0xd0, 0xbb, 0xd1, 0x8f, 0x20, 0xd1, 0x82, 0xd0, 0xb5, 0xd1, 0x80, 0xd0,
	0xbc, 0xd0, 0xb8, 0xd0, 0xbd, 0xd0, 0xb0, 0xd0, 0xbb, 0xd0, 0xb0, 0x20, 0xd0, 0xb7, 0xd0, 0xb0,
	0xd0, 0xbf, 0xd1, 0x80, 0xd0, 0xb5, 0xd1, 0x89, 0xd0, 0xb5, 0xd0, 0xbd, 0xd0, 0xb0, 0x20, 0xd1,
	0x82, 0xd0, 0xbe, 0xd0, 0xba, 0xd0, 0xb5, 0xd0, 0xbd, 0xd0, 0xb8, 0xd0, 0xb7, 0xd0, 0xb0, 0xd1,
	0x86, 0xd0, 0xb8, 0xd1, 0x8f, 0x18, 0x05, 0x20, 0x6d, 0xca, 0xa1, 0xec, 0x8e, 0x02, 0x02, 0x33,
	0x32, 0x12, 0xa6, 0x01, 0x0a, 0x10, 0x43, 0x6c, 0x70, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x4e, 0x6f,
	0x74, 0x46, 0x6f, 0x75, 0x6e, 0x64, 0x10, 0x17, 0x1a, 0x8f, 0x01, 0xca, 0xf1, 0x30, 0x82, 0x01,
	0x0a, 0x02, 0x33, 0x33, 0x12, 0x78, 0xd0, 0x94, 0xd0, 0xbb, 0xd1, 0x8f, 0x20, 0xd0, 0xb4, 0xd0,
	0xb0, 0xd0, 0xbd, 0xd0, 0xbd, 0xd0, 0xbe, 0xd0, 0xb3, 0xd0, 0xbe, 0x20, 0xd0, 0xba, 0xd0, 0xbb,
	0xd0, 0xb8, 0xd0, 0xb5, 0xd0, 0xbd, 0xd1, 0x82, 0xd0, 0xb0, 0x20, 0xd0, 0xb2, 0x20, 0xd0, 0xb2,
	0xd0, 0xb0, 0xd1, 0x88, 0xd0, 0xb5, 0xd0, 0xb9, 0x20, 0xd0, 0xbe, 0xd1, 0x80, 0xd0, 0xb3, 0xd0,
	0xb0, 0xd0, 0xbd, 0xd0, 0xb8, 0xd0, 0xb7, 0xd0, 0xb0, 0xd1, 0x86, 0xd0, 0xb8, 0xd0, 0xb8, 0x20,
	0xd0, 0xbd, 0xd0, 0xb5, 0x20, 0xd0, 0xb7, 0xd0, 0xb0, 0xd1, 0x80, 0xd0, 0xb5, 0xd0, 0xb3, 0xd0,
	0xb8, 0xd1, 0x81, 0xd1, 0x82, 0xd1, 0x80, 0xd0, 0xb8, 0xd1, 0x80, 0xd0, 0xbe, 0xd0, 0xb2, 0xd0,
	0xb0, 0xd0, 0xbd, 0x20, 0xd1, 0x82, 0xd0, 0xbe, 0xd0, 0xba, 0xd0, 0xb5, 0xd0, 0xbd, 0x18, 0x05,
	0x20, 0x66, 0xca, 0xa1, 0xec, 0x8e, 0x02, 0x02, 0x33, 0x33, 0x12, 0x87, 0x01, 0x0a, 0x17, 0x43,
	0x6c, 0x70, 0x49, 0x6e, 0x63, 0x6f, 0x72, 0x72, 0x65, 0x63, 0x74, 0x42, 0x6c, 0x6f, 0x63, 0x6b,
	0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x10, 0x18, 0x1a, 0x6a, 0xca, 0xf1, 0x30, 0x5e, 0x0a, 0x02,
	0x33, 0x34, 0x12, 0x54, 0xd0, 0x9d, 0xd0, 0xb5, 0xd0, 0xb2, 0xd0, 0xb5, 0xd1, 0x80, 0xd0, 0xbd,
	0xd0, 0xb0, 0xd1, 0x8f, 0x20, 0xd1, 0x81, 0xd1, 0x83, 0xd0, 0xbc, 0xd0, 0xbc, 0xd0, 0xb0, 0x20,
	0xd0, 0xb1, 0xd0, 0xbb, 0xd0, 0xbe, 0xd0, 0xba, 0xd0, 0xb8, 0xd1, 0x80, 0xd0, 0xbe, 0xd0, 0xb2,
	0xd0, 0xb0, 0xd0, 0xbd, 0xd0, 0xb8, 0xd1, 0x8f, 0x2c, 0x20, 0xd0, 0xb7, 0xd0, 0xb0, 0xd1, 0x8f,
	0xd0, 0xb2, 0xd0, 0xba, 0xd0, 0xb0, 0x20, 0xd0, 0xbe, 0xd1, 0x82, 0xd0, 0xbc, 0xd0, 0xb5, 0xd0,
	0xbd, 0xd0, 0xb5, 0xd0, 0xbd, 0xd0, 0xb0, 0x21, 0x18, 0x05, 0x20, 0x6a, 0xca, 0xa1, 0xec, 0x8e,
	0x02, 0x02, 0x33, 0x34, 0x12, 0x50, 0x0a, 0x0f, 0x43, 0x6c, 0x70, 0x55, 0x6e, 0x6b, 0x6e, 0x6f,
	0x77, 0x6e, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x10, 0x19, 0x1a, 0x3b, 0xca, 0xf1, 0x30, 0x2f, 0x0a,
	0x02, 0x39, 0x39, 0x12, 0x25, 0xd0, 0x9d, 0xd0, 0xb5, 0xd0, 0xb8, 0xd0, 0xb7, 0xd0, 0xb2, 0xd0,
	0xb5, 0xd1, 0x81, 0xd1, 0x82, 0xd0, 0xbd, 0xd0, 0xb0, 0xd1, 0x8f, 0x20, 0xd0, 0xbe, 0xd1, 0x88,
	0xd0, 0xb8, 0xd0, 0xb1, 0xd0, 0xba, 0xd0, 0xb0, 0x3a, 0x20, 0x18, 0x0b, 0x20, 0x6a, 0xca, 0xa1,
	0xec, 0x8e, 0x02, 0x02, 0x39, 0x39, 0x12, 0x84, 0x01, 0x0a, 0x15, 0x43, 0x6c, 0x70, 0x55, 0x6e,
	0x61, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x6c, 0x65, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x10, 0x1a, 0x1a, 0x69, 0xca, 0xf1, 0x30, 0x5d, 0x0a, 0x02, 0x34, 0x31, 0x12, 0x53, 0xd0, 0xa1,
	0xd0, 0xb5, 0xd1, 0x80, 0xd0, 0xb2, 0xd0, 0xb8, 0xd1, 0x81, 0x20, 0xd0, 0xb2, 0xd1, 0x80, 0xd0,
	0xb5, 0xd0, 0xbc, 0xd0, 0xb5, 0xd0, 0xbd, 0xd0, 0xbd, 0xd0, 0xbe, 0x20, 0xd0, 0xbd, 0xd0, 0xb5,
	0xd0, 0xb4, 0xd0, 0xbe, 0xd1, 0x81, 0xd1, 0x82, 0xd1, 0x83, 0xd0, 0xbf, 0xd0, 0xb5, 0xd0, 0xbd,
	0x2c, 0x20, 0xd0, 0xbf, 0xd0, 0xbe, 0xd0, 0xbf, 0xd1, 0x80, 0xd0, 0xbe, 0xd0, 0xb1, 0xd1, 0x83,
	0xd0, 0xb9, 0xd1, 0x82, 0xd0, 0xb5, 0x20, 0xd0, 0xbf, 0xd0, 0xbe, 0xd0, 0xb7, 0xd0, 0xb6, 0xd0,
	0xb5, 0x18, 0x05, 0x20, 0x6c, 0xca, 0xa1, 0xec, 0x8e, 0x02, 0x02, 0x34, 0x31, 0x12, 0x51, 0x0a,
	0x12, 0x43, 0x6c, 0x70, 0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x49, 0x6e, 0x63, 0x6f, 0x72, 0x72,
	0x65, 0x63, 0x74, 0x10, 0x1b, 0x1a, 0x39, 0xca, 0xf1, 0x30, 0x2d, 0x0a, 0x02, 0x34, 0x32, 0x12,
	0x23, 0xd0, 0x9d, 0xd0, 0xb5, 0xd0, 0xbf, 0xd1, 0x80, 0xd0, 0xb0, 0xd0, 0xb2, 0xd0, 0xb8, 0xd0,
	0xbb, 0xd1, 0x8c, 0xd0, 0xbd, 0xd0, 0xb0, 0xd1, 0x8f, 0x20, 0xd1, 0x81, 0xd1, 0x83, 0xd0, 0xbc,
	0xd0, 0xbc, 0xd0, 0xb0, 0x18, 0x05, 0x20, 0x6a, 0xca, 0xa1, 0xec, 0x8e, 0x02, 0x02, 0x34, 0x32,
	0x12, 0x83, 0x01, 0x0a, 0x10, 0x43, 0x6c, 0x70, 0x55, 0x6e, 0x61, 0x76, 0x61, 0x69, 0x6c, 0x61,
	0x62, 0x6c, 0x65, 0x44, 0x62, 0x10, 0x1c, 0x1a, 0x6d, 0xca, 0xf1, 0x30, 0x61, 0x0a, 0x02, 0x34,
	0x33, 0x12, 0x57, 0xd0, 0xa1, 0xd0, 0xb5, 0xd1, 0x80, 0xd0, 0xb2, 0xd0, 0xb8, 0xd1, 0x81, 0x20,
	0x44, 0x62, 0x20, 0xd0, 0xb2, 0xd1, 0x80, 0xd0, 0xb5, 0xd0, 0xbc, 0xd0, 0xb5, 0xd0, 0xbd, 0xd0,
	0xbd, 0xd0, 0xbe, 0x20, 0xd0, 0xbd, 0xd0, 0xb5, 0xd0, 0xb4, 0xd0, 0xbe, 0xd1, 0x81, 0xd1, 0x82,
	0xd1, 0x83, 0xd0, 0xbf, 0xd0, 0xb5, 0xd0, 0xbd, 0x2c, 0x20, 0xd0, 0xbf, 0xd0, 0xbe, 0xd0, 0xbf,
	0xd1, 0x80, 0xd0, 0xbe, 0xd0, 0xb1, 0xd1, 0x83, 0xd0, 0xb9, 0xd1, 0x82, 0xd0, 0xb5, 0x20, 0xd0,
	0xbf, 0xd0, 0xbe, 0xd0, 0xb7, 0xd0, 0xb6, 0xd0, 0xb5, 0x20, 0x18, 0x05, 0x20, 0x6c, 0xca, 0xa1,
	0xec, 0x8e, 0x02, 0x02, 0x34, 0x33, 0x12, 0x6e, 0x0a, 0x14, 0x43, 0x6c, 0x70, 0x49, 0x6e, 0x63,
	0x6f, 0x72, 0x72, 0x65, 0x63, 0x74, 0x4d, 0x65, 0x72, 0x63, 0x68, 0x61, 0x6e, 0x74, 0x10, 0x1d,
	0x1a, 0x54, 0xca, 0xf1, 0x30, 0x48, 0x0a, 0x02, 0x34, 0x34, 0x12, 0x3e, 0xd0, 0x9d, 0xd0, 0xb5,
	0xd0, 0xbf, 0xd1, 0x80, 0xd0, 0xb0, 0xd0, 0xb2, 0xd0, 0xb8, 0xd0, 0xbb, 0xd1, 0x8c, 0xd0, 0xbd,
	0xd0, 0xbe, 0xd0, 0xb5, 0x20, 0xd0, 0xb7, 0xd0, 0xbd, 0xd0, 0xb0, 0xd1, 0x87, 0xd0, 0xb5, 0xd0,
	0xbd, 0xd0, 0xb8, 0xd0, 0xb5, 0x20, 0xd0, 0xb2, 0x20, 0xd0, 0xbf, 0xd0, 0xbe, 0xd0, 0xbb, 0xd0,
	0xb5, 0x20, 0x4d, 0x45, 0x52, 0x43, 0x48, 0x41, 0x4e, 0x54, 0x18, 0x05, 0x20, 0x6a, 0xca, 0xa1,
	0xec, 0x8e, 0x02, 0x02, 0x34, 0x34, 0x12, 0x55, 0x0a, 0x13, 0x43, 0x6c, 0x70, 0x4d, 0x65, 0x72,
	0x63, 0x68, 0x61, 0x6e, 0x74, 0x4e, 0x6f, 0x74, 0x46, 0x6f, 0x75, 0x6e, 0x64, 0x10, 0x1e, 0x1a,
	0x3c, 0xca, 0xf1, 0x30, 0x30, 0x0a, 0x02, 0x31, 0x37, 0x12, 0x26, 0xd0, 0x9a, 0xd0, 0xbe, 0xd0,
	0xbc, 0xd0, 0xbc, 0xd0, 0xb5, 0xd1, 0x80, 0xd1, 0x81, 0xd0, 0xb0, 0xd0, 0xbd, 0xd1, 0x82, 0x20,
	0xd0, 0xbd, 0xd0, 0xb5, 0x20, 0xd0, 0xbd, 0xd0, 0xb0, 0xd0, 0xb9, 0xd0, 0xb4, 0xd0, 0xb5, 0xd0,
	0xbd, 0x18, 0x05, 0x20, 0x6d, 0xca, 0xa1, 0xec, 0x8e, 0x02, 0x02, 0x31, 0x37, 0x12, 0x52, 0x0a,
	0x10, 0x43, 0x6c, 0x70, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x4e, 0x6f, 0x74, 0x46, 0x6f, 0x75, 0x6e,
	0x64, 0x10, 0x1f, 0x1a, 0x3c, 0xca, 0xf1, 0x30, 0x30, 0x0a, 0x02, 0x34, 0x35, 0x12, 0x26, 0xd0,
	0x97, 0xd0, 0xb0, 0xd1, 0x8f, 0xd0, 0xb2, 0xd0, 0xba, 0xd0, 0xb0, 0x20, 0x4f, 0x52, 0x44, 0x45,
	0x52, 0x20, 0xd0, 0xbd, 0xd0, 0xb5, 0x20, 0xd0, 0xbd, 0xd0, 0xb0, 0xd0, 0xb9, 0xd0, 0xb4, 0xd0,
	0xb5, 0xd0, 0xbd, 0xd0, 0xb0, 0x18, 0x05, 0x20, 0x6a, 0xca, 0xa1, 0xec, 0x8e, 0x02, 0x02, 0x34,
	0x35, 0x12, 0x52, 0x0a, 0x0e, 0x43, 0x6c, 0x70, 0x53, 0x69, 0x67, 0x6e, 0x49, 0x6e, 0x76, 0x61,
	0x6c, 0x69, 0x64, 0x10, 0x20, 0x1a, 0x3e, 0xca, 0xf1, 0x30, 0x32, 0x0a, 0x02, 0x34, 0x36, 0x12,
	0x28, 0xd0, 0x9d, 0xd0, 0xb5, 0xd0, 0xbf, 0xd1, 0x80, 0xd0, 0xb0, 0xd0, 0xb2, 0xd0, 0xb8, 0xd0,
	0xbb, 0xd1, 0x8c, 0xd0, 0xbd, 0xd0, 0xb0, 0xd1, 0x8f, 0x20, 0xd0, 0xbf, 0xd0, 0xbe, 0xd0, 0xb4,
	0xd0, 0xbf, 0xd0, 0xb8, 0xd1, 0x81, 0xd1, 0x8c, 0x21, 0x18, 0x05, 0x20, 0x6a, 0xca, 0xa1, 0xec,
	0x8e, 0x02, 0x02, 0x34, 0x36, 0x12, 0x7e, 0x0a, 0x18, 0x43, 0x6c, 0x70, 0x52, 0x65, 0x66, 0x75,
	0x6e, 0x64, 0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x49, 0x6e, 0x63, 0x6f, 0x72, 0x72, 0x65, 0x63,
	0x74, 0x10, 0x21, 0x1a, 0x60, 0xca, 0xf1, 0x30, 0x54, 0x0a, 0x02, 0x34, 0x37, 0x12, 0x4a, 0xd0,
	0xa1, 0xd1, 0x83, 0xd0, 0xbc, 0xd0, 0xbc, 0xd0, 0xb0, 0x20, 0xd0, 0xb2, 0xd0, 0xbe, 0xd0, 0xb7,
	0xd0, 0xb2, 0xd1, 0x80, 0xd1, 0x82, 0xd0, 0xb0, 0x20, 0x27, 0x25, 0x73, 0x27, 0x20, 0xd0, 0xb1,
	0xd0, 0xbe, 0xd0, 0xbb, 0xd1, 0x8c, 0xd1, 0x88, 0xd0, 0xb5, 0x20, 0xd1, 0x87, 0xd0, 0xb5, 0xd0,
	0xbc, 0x20, 0xd1, 0x81, 0xd1, 0x83, 0xd0, 0xbc, 0xd0, 0xbc, 0xd0, 0xb0, 0x20, 0xd0, 0xb7, 0xd0,
	0xb0, 0xd0, 0xba, 0xd0, 0xb0, 0xd0, 0xb7, 0xd0, 0xb0, 0x18, 0x05, 0x20, 0x6a, 0xca, 0xa1, 0xec,
	0x8e, 0x02, 0x02, 0x34, 0x37, 0x12, 0x97, 0x01, 0x0a, 0x12, 0x43, 0x6c, 0x70, 0x49, 0x6e, 0x63,
	0x6f, 0x72, 0x72, 0x65, 0x63, 0x74, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x10, 0x22, 0x1a, 0x7f,
	0xca, 0xf1, 0x30, 0x73, 0x0a, 0x02, 0x34, 0x38, 0x12, 0x69, 0xd0, 0xa2, 0xd0, 0xb5, 0xd0, 0xba,
	0xd1, 0x83, 0xd1, 0x89, 0xd0, 0xb8, 0xd0, 0xb9, 0x20, 0xd1, 0x81, 0xd1, 0x82, 0xd0, 0xb0, 0xd1,
	0x82, 0xd1, 0x83, 0xd1, 0x81, 0x20, 0xd0, 0xb7, 0xd0, 0xb0, 0xd0, 0xba, 0xd0, 0xb0, 0xd0, 0xb7,
	0xd0, 0xb0, 0x20, 0xd0, 0xbd, 0xd0, 0xb5, 0x20, 0xd0, 0xbf, 0xd0, 0xbe, 0xd0, 0xb7, 0xd0, 0xb2,
	0xd0, 0xbe, 0xd0, 0xbb, 0xd1, 0x8f, 0xd0, 0xb5, 0xd1, 0x82, 0x20, 0xd0, 0xb4, 0xd0, 0xb5, 0xd0,
	0xbb, 0xd0, 0xb0, 0xd1, 0x82, 0xd1, 0x8c, 0x20, 0xd0, 0xb2, 0xd0, 0xbe, 0xd0, 0xb7, 0xd0, 0xb2,
	0xd1, 0x80, 0xd0, 0xb0, 0xd1, 0x82, 0x2f, 0xd0, 0xbe, 0xd1, 0x82, 0xd0, 0xbc, 0xd0, 0xb5, 0xd0,
	0xbd, 0xd1, 0x83, 0x18, 0x05, 0x20, 0x6a, 0xca, 0xa1, 0xec, 0x8e, 0x02, 0x02, 0x34, 0x38, 0x12,
	0x56, 0x0a, 0x11, 0x43, 0x6c, 0x70, 0x49, 0x6e, 0x63, 0x6f, 0x72, 0x72, 0x65, 0x63, 0x74, 0x56,
	0x61, 0x6c, 0x75, 0x65, 0x10, 0x23, 0x1a, 0x3f, 0xca, 0xf1, 0x30, 0x33, 0x0a, 0x02, 0x35, 0x30,
	0x12, 0x29, 0xd0, 0x9d, 0xd0, 0xb5, 0xd0, 0xbf, 0xd1, 0x80, 0xd0, 0xb0, 0xd0, 0xb2, 0xd0, 0xb8,
	0xd0, 0xbb, 0xd1, 0x8c, 0xd0, 0xbd, 0xd0, 0xbe, 0xd0, 0xb5, 0x20, 0xd0, 0xb7, 0xd0, 0xbd, 0xd0,
	0xb0, 0xd1, 0x87, 0xd0, 0xb5, 0xd0, 0xbd, 0xd0, 0xb8, 0xd0, 0xb5, 0x18, 0x05, 0x20, 0x6a, 0xca,
	0xa1, 0xec, 0x8e, 0x02, 0x02, 0x35, 0x30, 0x12, 0x9f, 0x01, 0x0a, 0x14, 0x43, 0x6c, 0x70, 0x54,
	0x65, 0x72, 0x6d, 0x69, 0x6e, 0x61, 0x6c, 0x46, 0x6f, 0x72, 0x62, 0x69, 0x64, 0x64, 0x65, 0x6e,
	0x10, 0x24, 0x1a, 0x84, 0x01, 0xca, 0xf1, 0x30, 0x78, 0x0a, 0x02, 0x35, 0x31, 0x12, 0x6e, 0xd0,
	0xa2, 0xd0, 0xb5, 0xd0, 0xba, 0xd1, 0x83, 0xd1, 0x89, 0xd0, 0xb8, 0xd0, 0xb9, 0x20, 0xd1, 0x81,
	0xd1, 0x82, 0xd0, 0xb0, 0xd1, 0x82, 0xd1, 0x83, 0xd1, 0x81, 0x20, 0xd1, 0x82, 0xd0, 0xb5, 0xd1,
	0x80, 0xd0, 0xbc, 0xd0, 0xb8, 0xd0, 0xbd, 0xd0, 0xb0, 0xd0, 0xbb, 0xd0, 0xb0, 0x20, 0xd0, 0xbd,
	0xd0, 0xb5, 0x20, 0xd0, 0xbf, 0xd0, 0xbe, 0xd0, 0xb7, 0xd0, 0xb2, 0xd0, 0xbe, 0xd0, 0xbb, 0xd1,
	0x8f, 0xd0, 0xb5, 0xd1, 0x82, 0x20, 0xd0, 0xbf, 0xd1, 0x80, 0xd0, 0xbe, 0xd0, 0xb8, 0xd0, 0xb7,
	0xd0, 0xb2, 0xd0, 0xbe, 0xd0, 0xb4, 0xd0, 0xb8, 0xd1, 0x82, 0xd1, 0x8c, 0x20, 0xd0, 0xbe, 0xd0,
	0xbf, 0xd0, 0xb5, 0xd1, 0x80, 0xd0, 0xb0, 0xd1, 0x86, 0xd0, 0xb8, 0xd0, 0xb8, 0x18, 0x05, 0x20,
	0x6d, 0xca, 0xa1, 0xec, 0x8e, 0x02, 0x02, 0x35, 0x31, 0x12, 0x9c, 0x01, 0x0a, 0x15, 0x43, 0x6c,
	0x70, 0x46, 0x6f, 0x72, 0x62, 0x69, 0x64, 0x64, 0x65, 0x6e, 0x4f, 0x70, 0x65, 0x72, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x10, 0x25, 0x1a, 0x80, 0x01, 0xca, 0xf1, 0x30, 0x74, 0x0a, 0x02, 0x35, 0x32,
	0x12, 0x6a, 0xd0, 0x9e, 0xd0, 0xbf, 0xd0, 0xb5, 0xd1, 0x80, 0xd0, 0xb0, 0xd1, 0x86, 0xd0, 0xb8,
	0xd1, 0x8f, 0x20, 0xd0, 0xbe, 0xd1, 0x82, 0xd0, 0xbc, 0xd0, 0xb5, 0xd0, 0xbd, 0xd1, 0x8b, 0x2f,
	0xd0, 0xb2, 0xd0, 0xbe, 0xd0, 0xb7, 0xd0, 0xb2, 0xd1, 0x80, 0xd0, 0xb0, 0xd1, 0x82, 0xd0, 0xb0,
	0x20, 0xd1, 0x87, 0xd0, 0xb5, 0xd1, 0x80, 0xd0, 0xb5, 0xd0, 0xb7, 0x20, 0x41, 0x50, 0x49, 0x20,
	0xd0, 0xb4, 0xd0, 0xbb, 0xd1, 0x8f, 0x20, 0xd1, 0x82, 0xd0, 0xb5, 0xd1, 0x80, 0xd0, 0xbc, 0xd0,
	0xb8, 0xd0, 0xbd, 0xd0, 0xb0, 0xd0, 0xbb, 0xd0, 0xb0, 0x20, 0xd0, 0xb7, 0xd0, 0xb0, 0xd0, 0xbf,
	0xd1, 0x80, 0xd0, 0xb5, 0xd1, 0x89, 0xd0, 0xb5, 0xd0, 0xbd, 0xd0, 0xb0, 0x18, 0x05, 0x20, 0x6d,
	0xca, 0xa1, 0xec, 0x8e, 0x02, 0x02, 0x35, 0x32, 0x12, 0x69, 0x0a, 0x17, 0x43, 0x6c, 0x70, 0x44,
	0x75, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x74, 0x65, 0x44, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74,
	0x69, 0x6f, 0x6e, 0x10, 0x26, 0x1a, 0x4c, 0xca, 0xf1, 0x30, 0x40, 0x0a, 0x02, 0x35, 0x33, 0x12,
	0x36, 0xd0, 0x94, 0xd1, 0x83, 0xd0, 0xb1, 0xd0, 0xbb, 0xd0, 0xb8, 0xd1, 0x80, 0xd0, 0xbe, 0xd0,
	0xb2, 0xd0, 0xb0, 0xd0, 0xbd, 0xd0, 0xb8, 0xd0, 0xb5, 0x20, 0xd0, 0xbe, 0xd0, 0xbf, 0xd0, 0xb8,
	0xd1, 0x81, 0xd0, 0xb0, 0xd0, 0xbd, 0xd0, 0xb8, 0xd1, 0x8f, 0x20, 0xd0, 0xbe, 0xd1, 0x82, 0xd0,
	0xbc, 0xd0, 0xb5, 0xd0, 0xbd, 0xd1, 0x8b, 0x18, 0x05, 0x20, 0x6a, 0xca, 0xa1, 0xec, 0x8e, 0x02,
	0x02, 0x35, 0x33, 0x12, 0x65, 0x0a, 0x14, 0x43, 0x6c, 0x70, 0x52, 0x65, 0x66, 0x75, 0x6e, 0x64,
	0x48, 0x61, 0x6e, 0x64, 0x6c, 0x65, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x10, 0x27, 0x1a, 0x4b, 0xca,
	0xf1, 0x30, 0x40, 0x0a, 0x01, 0x46, 0x12, 0x37, 0xd0, 0x9e, 0xd1, 0x88, 0xd0, 0xb8, 0xd0, 0xb1,
	0xd0, 0xba, 0xd0, 0xb0, 0x20, 0xd0, 0xbf, 0xd1, 0x80, 0xd0, 0xb8, 0x20, 0xd0, 0xbe, 0xd0, 0xb1,
	0xd1, 0x80, 0xd0, 0xb0, 0xd0, 0xb1, 0xd0, 0xbe, 0xd1, 0x82, 0xd0, 0xba, 0xd0, 0xb5, 0x20, 0xd0,
	0xb2, 0xd0, 0xbe, 0xd0, 0xb7, 0xd0, 0xb2, 0xd1, 0x80, 0xd0, 0xb0, 0xd1, 0x82, 0xd0, 0xb0, 0x18,
	0x05, 0x20, 0x6a, 0xca, 0xa1, 0xec, 0x8e, 0x02, 0x01, 0x46, 0x12, 0x49, 0x0a, 0x0f, 0x43, 0x6c,
	0x70, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x10, 0x28, 0x1a,
	0x34, 0xca, 0xf1, 0x30, 0x29, 0x0a, 0x01, 0x45, 0x12, 0x20, 0xd0, 0x9e, 0xd1, 0x88, 0xd0, 0xb8,
	0xd0, 0xb1, 0xd0, 0xba, 0xd0, 0xb0, 0x20, 0xd0, 0xbf, 0xd1, 0x80, 0xd0, 0xb8, 0x20, 0xd0, 0xbe,
	0xd0, 0xbf, 0xd0, 0xbb, 0xd0, 0xb0, 0xd1, 0x82, 0xd0, 0xb5, 0x18, 0x05, 0x20, 0x6a, 0xca, 0xa1,
	0xec, 0x8e, 0x02, 0x01, 0x45, 0x12, 0x54, 0x0a, 0x11, 0x43, 0x6c, 0x70, 0x50, 0x61, 0x79, 0x6d,
	0x65, 0x6e, 0x74, 0x45, 0x78, 0x70, 0x69, 0x72, 0x65, 0x64, 0x10, 0x29, 0x1a, 0x3d, 0xca, 0xf1,
	0x30, 0x32, 0x0a, 0x01, 0x63, 0x12, 0x29, 0xd0, 0xa1, 0xd1, 0x87, 0xd0, 0xb5, 0xd1, 0x82, 0x20,
	0xd0, 0xbd, 0xd0, 0xb0, 0x20, 0xd0, 0xbe, 0xd0, 0xbf, 0xd0, 0xbb, 0xd0, 0xb0, 0xd1, 0x82, 0xd1,
	0x83, 0x20, 0xd1, 0x83, 0xd1, 0x81, 0xd1, 0x82, 0xd0, 0xb0, 0xd1, 0x80, 0xd0, 0xb5, 0xd0, 0xbb,
	0x18, 0x05, 0x20, 0x6a, 0xca, 0xa1, 0xec, 0x8e, 0x02, 0x01, 0x63, 0x12, 0x58, 0x0a, 0x0c, 0x43,
	0x6c, 0x70, 0x41, 0x75, 0x74, 0x68, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x10, 0x2b, 0x1a, 0x46, 0xca,
	0xf1, 0x30, 0x3b, 0x0a, 0x01, 0x33, 0x12, 0x32, 0xd0, 0x9e, 0xd1, 0x88, 0xd0, 0xb8, 0xd0, 0xb1,
	0xd0, 0xba, 0xd0, 0xb0, 0x20, 0xd0, 0xb0, 0xd0, 0xb2, 0xd1, 0x82, 0xd0, 0xbe, 0xd1, 0x80, 0xd0,
	0xb8, 0xd0, 0xb7, 0xd0, 0xb0, 0xd1, 0x86, 0xd0, 0xb8, 0xd0, 0xb8, 0x20, 0xd0, 0xbf, 0xd0, 0xbb,
	0xd0, 0xb0, 0xd1, 0x82, 0xd0, 0xb5, 0xd0, 0xb6, 0xd0, 0xb0, 0x18, 0x05, 0x20, 0x6a, 0xca, 0xa1,
	0xec, 0x8e, 0x02, 0x01, 0x33, 0x12, 0x5d, 0x0a, 0x13, 0x43, 0x6c, 0x70, 0x49, 0x6e, 0x63, 0x6f,
	0x72, 0x72, 0x65, 0x63, 0x74, 0x43, 0x61, 0x72, 0x64, 0x4e, 0x75, 0x6d, 0x10, 0x2c, 0x1a, 0x44,
	0xca, 0xf1, 0x30, 0x38, 0x0a, 0x02, 0x33, 0x37, 0x12, 0x2e, 0xd0, 0x9d, 0xd0, 0xb5, 0xd0, 0xbf,
	0xd1, 0x80, 0xd0, 0xb0, 0xd0, 0xb2, 0xd0, 0xb8, 0xd0, 0xbb, 0xd1, 0x8c, 0xd0, 0xbd, 0xd1, 0x8b,
	0xd0, 0xb9, 0x20, 0xd0, 0xbd, 0xd0, 0xbe, 0xd0, 0xbc, 0xd0, 0xb5, 0xd1, 0x80, 0x20, 0xd0, 0xba,
	0xd0, 0xb0, 0xd1, 0x80, 0xd1, 0x82, 0xd1, 0x8b, 0x18, 0x05, 0x20, 0x75, 0xca, 0xa1, 0xec, 0x8e,
	0x02, 0x02, 0x33, 0x37, 0x12, 0x3c, 0x0a, 0x11, 0x43, 0x6c, 0x70, 0x53, 0x75, 0x73, 0x70, 0x65,
	0x63, 0x74, 0x65, 0x64, 0x46, 0x72, 0x61, 0x75, 0x64, 0x10, 0x2d, 0x1a, 0x25, 0xca, 0xf1, 0x30,
	0x19, 0x0a, 0x02, 0x35, 0x39, 0x12, 0x0f, 0x53, 0x75, 0x73, 0x70, 0x65, 0x63, 0x74, 0x65, 0x64,
	0x20, 0x66, 0x72, 0x61, 0x75, 0x64, 0x18, 0x05, 0x20, 0x77, 0xca, 0xa1, 0xec, 0x8e, 0x02, 0x02,
	0x35, 0x39, 0x12, 0x4e, 0x0a, 0x11, 0x43, 0x6c, 0x70, 0x55, 0x6e, 0x64, 0x65, 0x66, 0x69, 0x6e,
	0x65, 0x64, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x10, 0x2f, 0x1a, 0x37, 0xca, 0xf1, 0x30, 0x2c, 0x0a,
	0x01, 0x31, 0x12, 0x23, 0xd0, 0x9d, 0xd0, 0xb5, 0xd0, 0xb8, 0xd0, 0xb7, 0xd0, 0xb2, 0xd0, 0xb5,
	0xd1, 0x81, 0xd1, 0x82, 0xd0, 0xbd, 0xd0, 0xb0, 0xd1, 0x8f, 0x20, 0xd0, 0xbe, 0xd1, 0x88, 0xd0,
	0xb8, 0xd0, 0xb1, 0xd0, 0xba, 0xd0, 0xb0, 0x18, 0x05, 0x20, 0x64, 0xca, 0xa1, 0xec, 0x8e, 0x02,
	0x01, 0x31, 0x12, 0x53, 0x0a, 0x0f, 0x43, 0x6c, 0x70, 0x45, 0x78, 0x63, 0x65, 0x65, 0x64, 0x73,
	0x4c, 0x69, 0x6d, 0x69, 0x74, 0x10, 0x31, 0x1a, 0x3e, 0xca, 0xf1, 0x30, 0x32, 0x0a, 0x02, 0x36,
	0x31, 0x12, 0x28, 0xd0, 0x9f, 0xd1, 0x80, 0xd0, 0xb5, 0xd0, 0xb2, 0xd1, 0x8b, 0xd1, 0x88, 0xd0,
	0xb0, 0xd0, 0xb5, 0xd1, 0x82, 0x20, 0xd0, 0xbb, 0xd0, 0xb8, 0xd0, 0xbc, 0xd0, 0xb8, 0xd1, 0x82,
	0x20, 0xd1, 0x81, 0xd1, 0x83, 0xd0, 0xbc, 0xd0, 0xbc, 0xd1, 0x8b, 0x18, 0x05, 0x20, 0x67, 0xca,
	0xa1, 0xec, 0x8e, 0x02, 0x02, 0x36, 0x31, 0x12, 0x61, 0x0a, 0x0e, 0x43, 0x6c, 0x70, 0x4c, 0x69,
	0x6d, 0x69, 0x74, 0x65, 0x64, 0x43, 0x61, 0x72, 0x64, 0x10, 0x32, 0x1a, 0x4d, 0xca, 0xf1, 0x30,
	0x41, 0x0a, 0x02, 0x36, 0x32, 0x12, 0x37, 0xd0, 0x9a, 0xd0, 0xb0, 0xd1, 0x80, 0xd1, 0x82, 0xd0,
	0xb0, 0x20, 0xd1, 0x81, 0x20, 0xd0, 0xbe, 0xd0, 0xb3, 0xd1, 0x80, 0xd0, 0xb0, 0xd0, 0xbd, 0xd0,
	0xb8, 0xd1, 0x87, 0xd0, 0xb5, 0xd0, 0xbd, 0xd0, 0xbd, 0xd1, 0x8b, 0xd0, 0xbc, 0x20, 0xd0, 0xb4,
	0xd0, 0xbe, 0xd1, 0x81, 0xd1, 0x82, 0xd1, 0x83, 0xd0, 0xbf, 0xd0, 0xbe, 0xd0, 0xbc, 0x18, 0x05,
	0x20, 0x6f, 0xca, 0xa1, 0xec, 0x8e, 0x02, 0x02, 0x36, 0x32, 0x12, 0x74, 0x0a, 0x16, 0x43, 0x6c,
	0x70, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x44, 0x65, 0x63, 0x6c,
	0x69, 0x6e, 0x65, 0x64, 0x10, 0x33, 0x1a, 0x58, 0xca, 0xf1, 0x30, 0x4c, 0x0a, 0x02, 0x30, 0x35,
	0x12, 0x42, 0xd0, 0xa2, 0xd1, 0x80, 0xd0, 0xb0, 0xd0, 0xbd, 0xd0, 0xb7, 0xd0, 0xb0, 0xd0, 0xba,
	0xd1, 0x86, 0xd0, 0xb8, 0xd1, 0x8f, 0x20, 0xd0, 0xbe, 0xd1, 0x82, 0xd0, 0xba, 0xd0, 0xbb, 0xd0,
	0xbe, 0xd0, 0xbd, 0xd0, 0xb5, 0xd0, 0xbd, 0xd0, 0xb0, 0x20, 0xd0, 0xb1, 0xd0, 0xb0, 0xd0, 0xbd,
	0xd0, 0xba, 0xd0, 0xbe, 0xd0, 0xbc, 0x20, 0xd0, 0xb8, 0xd0, 0xbb, 0xd0, 0xb8, 0x20, 0xd0, 0x9c,
	0xd0, 0x9f, 0xd0, 0xa1, 0x18, 0x05, 0x20, 0x6a, 0xca, 0xa1, 0xec, 0x8e, 0x02, 0x02, 0x30, 0x35,
	0x12, 0x4e, 0x0a, 0x0c, 0x43, 0x6c, 0x70, 0x4e, 0x6f, 0x74, 0x45, 0x6e, 0x6f, 0x75, 0x67, 0x68,
	0x10, 0x34, 0x1a, 0x3c, 0xca, 0xf1, 0x30, 0x31, 0x0a, 0x01, 0x32, 0x12, 0x28, 0xd0, 0x9d, 0xd0,
	0xb5, 0x20, 0xd0, 0xb4, 0xd0, 0xbe, 0xd1, 0x81, 0xd1, 0x82, 0xd0, 0xb0, 0xd1, 0x82, 0xd0, 0xbe,
	0xd1, 0x87, 0xd0, 0xbd, 0xd0, 0xbe, 0x20, 0xd1, 0x81, 0xd1, 0x80, 0xd0, 0xb5, 0xd0, 0xb4, 0xd1,
	0x81, 0xd1, 0x82, 0xd0, 0xb2, 0x18, 0x05, 0x20, 0x76, 0xca, 0xa1, 0xec, 0x8e, 0x02, 0x01, 0x32,
	0x12, 0x47, 0x0a, 0x10, 0x43, 0x6c, 0x70, 0x4c, 0x69, 0x6d, 0x69, 0x74, 0x45, 0x78, 0x63, 0x65,
	0x65, 0x64, 0x65, 0x64, 0x10, 0x35, 0x1a, 0x31, 0xca, 0xf1, 0x30, 0x25, 0x0a, 0x02, 0x36, 0x35,
	0x12, 0x1b, 0xd0, 0x9f, 0xd1, 0x80, 0xd0, 0xb5, 0xd0, 0xb2, 0xd1, 0x8b, 0xd1, 0x88, 0xd0, 0xb5,
	0xd0, 0xbd, 0x20, 0xd0, 0xbb, 0xd0, 0xb8, 0xd0, 0xbc, 0xd0, 0xb8, 0xd1, 0x82, 0x18, 0x05, 0x20,
	0x68, 0xca, 0xa1, 0xec, 0x8e, 0x02, 0x02, 0x36, 0x35, 0x12, 0x4d, 0x0a, 0x0c, 0x43, 0x6c, 0x70,
	0x45, 0x72, 0x72, 0x6f, 0x72, 0x41, 0x75, 0x74, 0x68, 0x10, 0x36, 0x1a, 0x3b, 0xca, 0xf1, 0x30,
	0x2e, 0x0a, 0x03, 0x2d, 0x31, 0x39, 0x12, 0x23, 0xd0, 0x9e, 0xd1, 0x88, 0xd0, 0xb8, 0xd0, 0xb1,
	0xd0, 0xba, 0xd0, 0xb0, 0x20, 0xd0, 0xb0, 0xd0, 0xb2, 0xd1, 0x82, 0xd0, 0xbe, 0xd1, 0x80, 0xd0,
	0xb8, 0xd0, 0xb7, 0xd0, 0xb0, 0xd1, 0x86, 0xd0, 0xb8, 0xd0, 0xb8, 0x18, 0x05, 0x20, 0x6a, 0xca,
	0xa1, 0xec, 0x8e, 0x02, 0x03, 0x2d, 0x31, 0x39, 0x12, 0x52, 0x0a, 0x0f, 0x43, 0x6c, 0x70, 0x43,
	0x61, 0x72, 0x64, 0x49, 0x6e, 0x61, 0x63, 0x74, 0x69, 0x76, 0x65, 0x10, 0x37, 0x1a, 0x3d, 0xca,
	0xf1, 0x30, 0x31, 0x0a, 0x02, 0x30, 0x37, 0x12, 0x27, 0xd0, 0x92, 0xd0, 0xb0, 0xd1, 0x88, 0xd0,
	0xb0, 0x20, 0xd0, 0xba, 0xd0, 0xb0, 0xd1, 0x80, 0xd1, 0x82, 0xd0, 0xb0, 0x20, 0xd0, 0xbd, 0xd0,
	0xb5, 0x20, 0xd0, 0xb0, 0xd0, 0xba, 0xd1, 0x82, 0xd0, 0xb8, 0xd0, 0xb2, 0xd0, 0xbd, 0xd0, 0xb0,
	0x18, 0x05, 0x20, 0x72, 0xca, 0xa1, 0xec, 0x8e, 0x02, 0x02, 0x30, 0x37, 0x1a, 0x62, 0xd2, 0xf1,
	0x30, 0x10, 0x0a, 0x01, 0x30, 0x12, 0x07, 0x64, 0x65, 0x66, 0x61, 0x75, 0x6c, 0x74, 0x18, 0x0f,
	0x20, 0x00, 0xaa, 0x82, 0xec, 0x8e, 0x02, 0x25, 0x64, 0x65, 0x66, 0x61, 0x75, 0x6c, 0x74, 0x5f,
	0x6a, 0x75, 0x73, 0x61, 0x6e, 0x5f, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x5f, 0x63,
	0x6f, 0x64, 0x65, 0x5f, 0x63, 0x6c, 0x70, 0x5f, 0x76, 0x61, 0x6c, 0x75, 0x65, 0xb2, 0x82, 0xec,
	0x8e, 0x02, 0x1d, 0x6a, 0x75, 0x73, 0x61, 0x6e, 0x5f, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x5f, 0x63, 0x6c, 0x70, 0x5f, 0x76, 0x61, 0x6c, 0x75, 0x65,
	0x3a, 0xa8, 0x01, 0x0a, 0x1d, 0x6a, 0x75, 0x73, 0x61, 0x6e, 0x5f, 0x72, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x5f, 0x63, 0x6c, 0x70, 0x5f, 0x76, 0x61, 0x6c,
	0x75, 0x65, 0x12, 0x21, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x62, 0x75, 0x66, 0x2e, 0x45, 0x6e, 0x75, 0x6d, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x4f, 0x70,
	0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x99, 0x8e, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x41, 0x2e,
	0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x2e, 0x6a, 0x75, 0x73, 0x61, 0x6e,
	0x5f, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x69, 0x7a, 0x65, 0x2e, 0x6a, 0x75, 0x73, 0x61, 0x6e, 0x5f,
	0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x69, 0x7a, 0x65, 0x2e, 0x4a, 0x75, 0x73, 0x61, 0x6e, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x43, 0x6f, 0x64, 0x65, 0x43, 0x6c, 0x70, 0x52, 0x65, 0x66,
	0x52, 0x19, 0x6a, 0x75, 0x73, 0x61, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x43,
	0x6f, 0x64, 0x65, 0x43, 0x6c, 0x70, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0xb2, 0x01, 0x0a, 0x25,
	0x64, 0x65, 0x66, 0x61, 0x75, 0x6c, 0x74, 0x5f, 0x6a, 0x75, 0x73, 0x61, 0x6e, 0x5f, 0x72, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x5f, 0x63, 0x6c, 0x70, 0x5f,
	0x76, 0x61, 0x6c, 0x75, 0x65, 0x12, 0x1c, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x45, 0x6e, 0x75, 0x6d, 0x4f, 0x70, 0x74, 0x69,
	0x6f, 0x6e, 0x73, 0x18, 0x9a, 0x8e, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x41, 0x2e, 0x70, 0x72,
	0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x2e, 0x6a, 0x75, 0x73, 0x61, 0x6e, 0x5f, 0x74,
	0x6f, 0x6b, 0x65, 0x6e, 0x69, 0x7a, 0x65, 0x2e, 0x6a, 0x75, 0x73, 0x61, 0x6e, 0x5f, 0x74, 0x6f,
	0x6b, 0x65, 0x6e, 0x69, 0x7a, 0x65, 0x2e, 0x4a, 0x75, 0x73, 0x61, 0x6e, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x43, 0x6f, 0x64, 0x65, 0x43, 0x6c, 0x70, 0x52, 0x65, 0x66, 0x52, 0x20,
	0x64, 0x65, 0x66, 0x61, 0x75, 0x6c, 0x74, 0x4a, 0x75, 0x73, 0x61, 0x6e, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x43, 0x6f, 0x64, 0x65, 0x43, 0x6c, 0x70, 0x56, 0x61, 0x6c, 0x75, 0x65,
	0x42, 0x2d, 0x5a, 0x2b, 0x67, 0x69, 0x74, 0x2e, 0x6c, 0x6f, 0x63, 0x61, 0x6c, 0x2f, 0x73, 0x65,
	0x6e, 0x73, 0x69, 0x74, 0x69, 0x76, 0x65, 0x2f, 0x69, 0x6e, 0x6e, 0x65, 0x72, 0x70, 0x62, 0x2f,
	0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x2f, 0x67, 0x72, 0x70, 0x63, 0x62,
	0x08, 0x65, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x70, 0xe8, 0x07,
})

var (
	file_inner_processing_grpc_jusan_clp_proto_rawDescOnce sync.Once
	file_inner_processing_grpc_jusan_clp_proto_rawDescData []byte
)

func file_inner_processing_grpc_jusan_clp_proto_rawDescGZIP() []byte {
	file_inner_processing_grpc_jusan_clp_proto_rawDescOnce.Do(func() {
		file_inner_processing_grpc_jusan_clp_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_inner_processing_grpc_jusan_clp_proto_rawDesc), len(file_inner_processing_grpc_jusan_clp_proto_rawDesc)))
	})
	return file_inner_processing_grpc_jusan_clp_proto_rawDescData
}

var file_inner_processing_grpc_jusan_clp_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_inner_processing_grpc_jusan_clp_proto_msgTypes = make([]protoimpl.MessageInfo, 1)
var file_inner_processing_grpc_jusan_clp_proto_goTypes = []any{
	(JusanResponseCodeClp)(0),             // 0: processing.jusan_tokenize.jusan_tokenize.JusanResponseCodeClp
	(*JusanResponseCodeClpRef)(nil),       // 1: processing.jusan_tokenize.jusan_tokenize.JusanResponseCodeClpRef
	(EnumTransactionStatus)(0),            // 2: processing.transaction.transaction_status.EnumTransactionStatus
	(IntegrationError)(0),                 // 3: processing.integration.integration.IntegrationError
	(*descriptorpb.EnumValueOptions)(nil), // 4: google.protobuf.EnumValueOptions
	(*descriptorpb.EnumOptions)(nil),      // 5: google.protobuf.EnumOptions
}
var file_inner_processing_grpc_jusan_clp_proto_depIdxs = []int32{
	2, // 0: processing.jusan_tokenize.jusan_tokenize.JusanResponseCodeClpRef.transaction_status:type_name -> processing.transaction.transaction_status.EnumTransactionStatus
	3, // 1: processing.jusan_tokenize.jusan_tokenize.JusanResponseCodeClpRef.integration_error:type_name -> processing.integration.integration.IntegrationError
	4, // 2: processing.jusan_tokenize.jusan_tokenize.jusan_response_code_clp_value:extendee -> google.protobuf.EnumValueOptions
	5, // 3: processing.jusan_tokenize.jusan_tokenize.default_jusan_response_code_clp_value:extendee -> google.protobuf.EnumOptions
	1, // 4: processing.jusan_tokenize.jusan_tokenize.jusan_response_code_clp_value:type_name -> processing.jusan_tokenize.jusan_tokenize.JusanResponseCodeClpRef
	1, // 5: processing.jusan_tokenize.jusan_tokenize.default_jusan_response_code_clp_value:type_name -> processing.jusan_tokenize.jusan_tokenize.JusanResponseCodeClpRef
	6, // [6:6] is the sub-list for method output_type
	6, // [6:6] is the sub-list for method input_type
	4, // [4:6] is the sub-list for extension type_name
	2, // [2:4] is the sub-list for extension extendee
	0, // [0:2] is the sub-list for field type_name
}

func init() { file_inner_processing_grpc_jusan_clp_proto_init() }
func file_inner_processing_grpc_jusan_clp_proto_init() {
	if File_inner_processing_grpc_jusan_clp_proto != nil {
		return
	}
	file_inner_processing_grpc_transaction_status_proto_init()
	file_inner_processing_grpc_integration_proto_init()
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_inner_processing_grpc_jusan_clp_proto_rawDesc), len(file_inner_processing_grpc_jusan_clp_proto_rawDesc)),
			NumEnums:      1,
			NumMessages:   1,
			NumExtensions: 2,
			NumServices:   0,
		},
		GoTypes:           file_inner_processing_grpc_jusan_clp_proto_goTypes,
		DependencyIndexes: file_inner_processing_grpc_jusan_clp_proto_depIdxs,
		EnumInfos:         file_inner_processing_grpc_jusan_clp_proto_enumTypes,
		MessageInfos:      file_inner_processing_grpc_jusan_clp_proto_msgTypes,
		ExtensionInfos:    file_inner_processing_grpc_jusan_clp_proto_extTypes,
	}.Build()
	File_inner_processing_grpc_jusan_clp_proto = out.File
	file_inner_processing_grpc_jusan_clp_proto_goTypes = nil
	file_inner_processing_grpc_jusan_clp_proto_depIdxs = nil
}
