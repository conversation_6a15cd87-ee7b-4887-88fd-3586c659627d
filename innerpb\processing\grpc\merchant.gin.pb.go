// Code generated by protoc-gen-go-mvp. DO NOT EDIT.
// versions:
// - protoc-gen-go-mvp v2.0.1
// created by mvp authors

package grpc

import (
	dog "git.local/sensitive/sdk/dog"
	gin "github.com/gin-gonic/gin"
	metadata "google.golang.org/grpc/metadata"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
	http "net/http"
)

func RegisterGinMerchantRouterGroup(
	in *gin.RouterGroup,
	handler func(func(c *gin.Context) error) gin.HandlerFunc,
) error {
	service, err := NewGinMerchantService()
	if err != nil {
		return err
	}

	routerGroup := in.Group("processing.merchant.merchant.Merchant")
	routerGroup.PUT("/CheckProjectAuth", handler(service.CheckProjectAuth))
	routerGroup.PUT("/CheckProjectAuthSHA256", handler(service.CheckProjectAuthSHA256))
	routerGroup.PUT("/GetProject", handler(service.GetProject))
	routerGroup.PUT("/GenerateProjectHash", handler(service.GenerateProjectHash))
	routerGroup.PUT("/GenerateProjectSHA256Hash", handler(service.GenerateProjectSHA256Hash))
	routerGroup.PUT("/CheckProject", handler(service.CheckProject))
	routerGroup.PUT("/GetMerchantInfo", handler(service.GetMerchantInfo))
	routerGroup.PUT("/GetMerchantProjectsByBin", handler(service.GetMerchantProjectsByBin))
	routerGroup.PUT("/GetMerchantDataByID", handler(service.GetMerchantDataByID))
	return nil
}

func NewGinMerchantService() (GinMerchantServer, error) {
	client, err := NewPreparedMerchantClient()
	if err != nil {
		return nil, err
	}

	return &ginMerchantServer{
		client: NewLoggedMerchantClient(
			NewIamMerchantClient(client),
		),
	}, nil
}

//go:generate mockgen -destination=./grpcmock/merchant.gin.pb.go -package=grpcmock -source=merchant.gin.pb.go GinMerchantServer
type GinMerchantServer interface {
	CheckProjectAuth(c *gin.Context) error
	CheckProjectAuthSHA256(c *gin.Context) error
	GetProject(c *gin.Context) error
	GenerateProjectHash(c *gin.Context) error
	GenerateProjectSHA256Hash(c *gin.Context) error
	CheckProject(c *gin.Context) error
	GetMerchantInfo(c *gin.Context) error
	GetMerchantProjectsByBin(c *gin.Context) error
	GetMerchantDataByID(c *gin.Context) error
}

var _ GinMerchantServer = (*ginMerchantServer)(nil)

type ginMerchantServer struct {
	client MerchantClient
}

type Merchant_CheckProjectAuth_Success struct {
	// Status is the status of the response. Set true
	Status bool `json:"status"`
	// Message is a human-readable explanation of the status
	Message string `json:"message"`
	// StatusCode is the GoErr status. Set 0
	StatusCode int `json:"status_code"`
	// Result is the result of the request
	Result *CheckProjectAuthResponseV1 `json:"result"`
}

type Merchant_CheckProjectAuth_Failure struct {
	// Status is the status of the response. Set false
	Status bool `json:"status"`
	// Message is a human-readable explanation of the status
	Message string `json:"message"`
	// StatusCode is the GoErr status
	StatusCode int `json:"status_code"`
}

// CheckProjectAuth
// @Summary CheckProjectAuth
// @Security bearerAuth
// @ID Merchant_CheckProjectAuth
// @Accept json
// @Param request body CheckProjectAuthRequestV1 true "CheckProjectAuthRequestV1"
// @Success 200 {object} Merchant_CheckProjectAuth_Success
// @Failure 401 {object} Merchant_CheckProjectAuth_Failure "goerrpkg.ErrorTypeAuthorization"
// @Failure 404 {object} Merchant_CheckProjectAuth_Failure "goerrpkg.ErrorTypeNotFound"
// @Failure 400 {object} Merchant_CheckProjectAuth_Failure "goerrpkg.ErrorTypeBadRequest"
// @Failure 500 {object} Merchant_CheckProjectAuth_Failure "goerrpkg.ErrorTypeUnexpectedError"
// @Failure 400 {object} Merchant_CheckProjectAuth_Failure "gin.ErrorTypeBind"
// @Failure 500 {object} Merchant_CheckProjectAuth_Failure "Undefined error"
// @Produce json
// @Router /processing.merchant.merchant.Merchant/CheckProjectAuth [put]
// @tags Merchant
func (s *ginMerchantServer) CheckProjectAuth(c *gin.Context) error {
	ctx, span := dog.CreateSpan(c.Request.Context(), "GinMerchantServer_CheckProjectAuth")
	defer span.End()

	var request CheckProjectAuthRequestV1
	if err := c.ShouldBindJSON(&request); err != nil {
		return err
	}

	ctx = metadata.NewOutgoingContext(ctx, metadata.MD(c.Request.Header))

	response, err := s.client.CheckProjectAuth(ctx, &request)
	if err != nil {
		return err
	}

	c.JSON(http.StatusOK, &Merchant_CheckProjectAuth_Success{
		Status:     true,
		Message:    "Success",
		StatusCode: 0,
		Result:     response,
	})
	return nil
}

type Merchant_CheckProjectAuthSHA256_Success struct {
	// Status is the status of the response. Set true
	Status bool `json:"status"`
	// Message is a human-readable explanation of the status
	Message string `json:"message"`
	// StatusCode is the GoErr status. Set 0
	StatusCode int `json:"status_code"`
	// Result is the result of the request
	Result *CheckProjectAuthResponseV1 `json:"result"`
}

type Merchant_CheckProjectAuthSHA256_Failure struct {
	// Status is the status of the response. Set false
	Status bool `json:"status"`
	// Message is a human-readable explanation of the status
	Message string `json:"message"`
	// StatusCode is the GoErr status
	StatusCode int `json:"status_code"`
}

// CheckProjectAuthSHA256
// @Summary CheckProjectAuthSHA256
// @Security bearerAuth
// @ID Merchant_CheckProjectAuthSHA256
// @Accept json
// @Param request body CheckProjectAuthSHA256RequestV1 true "CheckProjectAuthSHA256RequestV1"
// @Success 200 {object} Merchant_CheckProjectAuthSHA256_Success
// @Failure 401 {object} Merchant_CheckProjectAuthSHA256_Failure "goerrpkg.ErrorTypeAuthorization"
// @Failure 404 {object} Merchant_CheckProjectAuthSHA256_Failure "goerrpkg.ErrorTypeNotFound"
// @Failure 400 {object} Merchant_CheckProjectAuthSHA256_Failure "goerrpkg.ErrorTypeBadRequest"
// @Failure 500 {object} Merchant_CheckProjectAuthSHA256_Failure "goerrpkg.ErrorTypeUnexpectedError"
// @Failure 400 {object} Merchant_CheckProjectAuthSHA256_Failure "gin.ErrorTypeBind"
// @Failure 500 {object} Merchant_CheckProjectAuthSHA256_Failure "Undefined error"
// @Produce json
// @Router /processing.merchant.merchant.Merchant/CheckProjectAuthSHA256 [put]
// @tags Merchant
func (s *ginMerchantServer) CheckProjectAuthSHA256(c *gin.Context) error {
	ctx, span := dog.CreateSpan(c.Request.Context(), "GinMerchantServer_CheckProjectAuthSHA256")
	defer span.End()

	var request CheckProjectAuthSHA256RequestV1
	if err := c.ShouldBindJSON(&request); err != nil {
		return err
	}

	ctx = metadata.NewOutgoingContext(ctx, metadata.MD(c.Request.Header))

	response, err := s.client.CheckProjectAuthSHA256(ctx, &request)
	if err != nil {
		return err
	}

	c.JSON(http.StatusOK, &Merchant_CheckProjectAuthSHA256_Success{
		Status:     true,
		Message:    "Success",
		StatusCode: 0,
		Result:     response,
	})
	return nil
}

type Merchant_GetProject_Success struct {
	// Status is the status of the response. Set true
	Status bool `json:"status"`
	// Message is a human-readable explanation of the status
	Message string `json:"message"`
	// StatusCode is the GoErr status. Set 0
	StatusCode int `json:"status_code"`
	// Result is the result of the request
	Result *ProjectResponseV1 `json:"result"`
}

type Merchant_GetProject_Failure struct {
	// Status is the status of the response. Set false
	Status bool `json:"status"`
	// Message is a human-readable explanation of the status
	Message string `json:"message"`
	// StatusCode is the GoErr status
	StatusCode int `json:"status_code"`
}

// GetProject
// @Summary GetProject
// @Security bearerAuth
// @ID Merchant_GetProject
// @Accept json
// @Param request body ProjectRequestV1 true "ProjectRequestV1"
// @Success 200 {object} Merchant_GetProject_Success
// @Failure 401 {object} Merchant_GetProject_Failure "goerrpkg.ErrorTypeAuthorization"
// @Failure 404 {object} Merchant_GetProject_Failure "goerrpkg.ErrorTypeNotFound"
// @Failure 400 {object} Merchant_GetProject_Failure "goerrpkg.ErrorTypeBadRequest"
// @Failure 500 {object} Merchant_GetProject_Failure "goerrpkg.ErrorTypeUnexpectedError"
// @Failure 400 {object} Merchant_GetProject_Failure "gin.ErrorTypeBind"
// @Failure 500 {object} Merchant_GetProject_Failure "Undefined error"
// @Produce json
// @Router /processing.merchant.merchant.Merchant/GetProject [put]
// @tags Merchant
func (s *ginMerchantServer) GetProject(c *gin.Context) error {
	ctx, span := dog.CreateSpan(c.Request.Context(), "GinMerchantServer_GetProject")
	defer span.End()

	var request ProjectRequestV1
	if err := c.ShouldBindJSON(&request); err != nil {
		return err
	}

	ctx = metadata.NewOutgoingContext(ctx, metadata.MD(c.Request.Header))

	response, err := s.client.GetProject(ctx, &request)
	if err != nil {
		return err
	}

	c.JSON(http.StatusOK, &Merchant_GetProject_Success{
		Status:     true,
		Message:    "Success",
		StatusCode: 0,
		Result:     response,
	})
	return nil
}

type Merchant_GenerateProjectHash_Success struct {
	// Status is the status of the response. Set true
	Status bool `json:"status"`
	// Message is a human-readable explanation of the status
	Message string `json:"message"`
	// StatusCode is the GoErr status. Set 0
	StatusCode int `json:"status_code"`
	// Result is the result of the request
	Result *GenerateProjectHashResponseV1 `json:"result"`
}

type Merchant_GenerateProjectHash_Failure struct {
	// Status is the status of the response. Set false
	Status bool `json:"status"`
	// Message is a human-readable explanation of the status
	Message string `json:"message"`
	// StatusCode is the GoErr status
	StatusCode int `json:"status_code"`
}

// GenerateProjectHash
// @Summary GenerateProjectHash
// @Security bearerAuth
// @ID Merchant_GenerateProjectHash
// @Accept json
// @Param request body GenerateProjectHashRequestV1 true "GenerateProjectHashRequestV1"
// @Success 200 {object} Merchant_GenerateProjectHash_Success
// @Failure 401 {object} Merchant_GenerateProjectHash_Failure "goerrpkg.ErrorTypeAuthorization"
// @Failure 404 {object} Merchant_GenerateProjectHash_Failure "goerrpkg.ErrorTypeNotFound"
// @Failure 400 {object} Merchant_GenerateProjectHash_Failure "goerrpkg.ErrorTypeBadRequest"
// @Failure 500 {object} Merchant_GenerateProjectHash_Failure "goerrpkg.ErrorTypeUnexpectedError"
// @Failure 400 {object} Merchant_GenerateProjectHash_Failure "gin.ErrorTypeBind"
// @Failure 500 {object} Merchant_GenerateProjectHash_Failure "Undefined error"
// @Produce json
// @Router /processing.merchant.merchant.Merchant/GenerateProjectHash [put]
// @tags Merchant
func (s *ginMerchantServer) GenerateProjectHash(c *gin.Context) error {
	ctx, span := dog.CreateSpan(c.Request.Context(), "GinMerchantServer_GenerateProjectHash")
	defer span.End()

	var request GenerateProjectHashRequestV1
	if err := c.ShouldBindJSON(&request); err != nil {
		return err
	}

	ctx = metadata.NewOutgoingContext(ctx, metadata.MD(c.Request.Header))

	response, err := s.client.GenerateProjectHash(ctx, &request)
	if err != nil {
		return err
	}

	c.JSON(http.StatusOK, &Merchant_GenerateProjectHash_Success{
		Status:     true,
		Message:    "Success",
		StatusCode: 0,
		Result:     response,
	})
	return nil
}

type Merchant_GenerateProjectSHA256Hash_Success struct {
	// Status is the status of the response. Set true
	Status bool `json:"status"`
	// Message is a human-readable explanation of the status
	Message string `json:"message"`
	// StatusCode is the GoErr status. Set 0
	StatusCode int `json:"status_code"`
	// Result is the result of the request
	Result *GenerateProjectHashResponseV1 `json:"result"`
}

type Merchant_GenerateProjectSHA256Hash_Failure struct {
	// Status is the status of the response. Set false
	Status bool `json:"status"`
	// Message is a human-readable explanation of the status
	Message string `json:"message"`
	// StatusCode is the GoErr status
	StatusCode int `json:"status_code"`
}

// GenerateProjectSHA256Hash
// @Summary GenerateProjectSHA256Hash
// @Security bearerAuth
// @ID Merchant_GenerateProjectSHA256Hash
// @Accept json
// @Param request body GeneratePayloadHashRequestV1 true "GeneratePayloadHashRequestV1"
// @Success 200 {object} Merchant_GenerateProjectSHA256Hash_Success
// @Failure 401 {object} Merchant_GenerateProjectSHA256Hash_Failure "goerrpkg.ErrorTypeAuthorization"
// @Failure 404 {object} Merchant_GenerateProjectSHA256Hash_Failure "goerrpkg.ErrorTypeNotFound"
// @Failure 400 {object} Merchant_GenerateProjectSHA256Hash_Failure "goerrpkg.ErrorTypeBadRequest"
// @Failure 500 {object} Merchant_GenerateProjectSHA256Hash_Failure "goerrpkg.ErrorTypeUnexpectedError"
// @Failure 400 {object} Merchant_GenerateProjectSHA256Hash_Failure "gin.ErrorTypeBind"
// @Failure 500 {object} Merchant_GenerateProjectSHA256Hash_Failure "Undefined error"
// @Produce json
// @Router /processing.merchant.merchant.Merchant/GenerateProjectSHA256Hash [put]
// @tags Merchant
func (s *ginMerchantServer) GenerateProjectSHA256Hash(c *gin.Context) error {
	ctx, span := dog.CreateSpan(c.Request.Context(), "GinMerchantServer_GenerateProjectSHA256Hash")
	defer span.End()

	var request GeneratePayloadHashRequestV1
	if err := c.ShouldBindJSON(&request); err != nil {
		return err
	}

	ctx = metadata.NewOutgoingContext(ctx, metadata.MD(c.Request.Header))

	response, err := s.client.GenerateProjectSHA256Hash(ctx, &request)
	if err != nil {
		return err
	}

	c.JSON(http.StatusOK, &Merchant_GenerateProjectSHA256Hash_Success{
		Status:     true,
		Message:    "Success",
		StatusCode: 0,
		Result:     response,
	})
	return nil
}

type Merchant_CheckProject_Success struct {
	// Status is the status of the response. Set true
	Status bool `json:"status"`
	// Message is a human-readable explanation of the status
	Message string `json:"message"`
	// StatusCode is the GoErr status. Set 0
	StatusCode int `json:"status_code"`
	// Result is the result of the request
	Result *emptypb.Empty `json:"result"`
}

type Merchant_CheckProject_Failure struct {
	// Status is the status of the response. Set false
	Status bool `json:"status"`
	// Message is a human-readable explanation of the status
	Message string `json:"message"`
	// StatusCode is the GoErr status
	StatusCode int `json:"status_code"`
}

// CheckProject
// @Summary CheckProject
// @Security bearerAuth
// @ID Merchant_CheckProject
// @Accept json
// @Param request body CheckMerchantProjectRequestV1 true "CheckMerchantProjectRequestV1"
// @Success 200 {object} Merchant_CheckProject_Success
// @Failure 401 {object} Merchant_CheckProject_Failure "goerrpkg.ErrorTypeAuthorization"
// @Failure 404 {object} Merchant_CheckProject_Failure "goerrpkg.ErrorTypeNotFound"
// @Failure 400 {object} Merchant_CheckProject_Failure "goerrpkg.ErrorTypeBadRequest"
// @Failure 500 {object} Merchant_CheckProject_Failure "goerrpkg.ErrorTypeUnexpectedError"
// @Failure 400 {object} Merchant_CheckProject_Failure "gin.ErrorTypeBind"
// @Failure 500 {object} Merchant_CheckProject_Failure "Undefined error"
// @Produce json
// @Router /processing.merchant.merchant.Merchant/CheckProject [put]
// @tags Merchant
func (s *ginMerchantServer) CheckProject(c *gin.Context) error {
	ctx, span := dog.CreateSpan(c.Request.Context(), "GinMerchantServer_CheckProject")
	defer span.End()

	var request CheckMerchantProjectRequestV1
	if err := c.ShouldBindJSON(&request); err != nil {
		return err
	}

	ctx = metadata.NewOutgoingContext(ctx, metadata.MD(c.Request.Header))

	response, err := s.client.CheckProject(ctx, &request)
	if err != nil {
		return err
	}

	c.JSON(http.StatusOK, &Merchant_CheckProject_Success{
		Status:     true,
		Message:    "Success",
		StatusCode: 0,
		Result:     response,
	})
	return nil
}

type Merchant_GetMerchantInfo_Success struct {
	// Status is the status of the response. Set true
	Status bool `json:"status"`
	// Message is a human-readable explanation of the status
	Message string `json:"message"`
	// StatusCode is the GoErr status. Set 0
	StatusCode int `json:"status_code"`
	// Result is the result of the request
	Result *GetMerchantInfoResponseV1 `json:"result"`
}

type Merchant_GetMerchantInfo_Failure struct {
	// Status is the status of the response. Set false
	Status bool `json:"status"`
	// Message is a human-readable explanation of the status
	Message string `json:"message"`
	// StatusCode is the GoErr status
	StatusCode int `json:"status_code"`
}

// GetMerchantInfo
// @Summary GetMerchantInfo
// @Security bearerAuth
// @ID Merchant_GetMerchantInfo
// @Accept json
// @Param request body GetMerchantInfoRequestV1 true "GetMerchantInfoRequestV1"
// @Success 200 {object} Merchant_GetMerchantInfo_Success
// @Failure 401 {object} Merchant_GetMerchantInfo_Failure "goerrpkg.ErrorTypeAuthorization"
// @Failure 404 {object} Merchant_GetMerchantInfo_Failure "goerrpkg.ErrorTypeNotFound"
// @Failure 400 {object} Merchant_GetMerchantInfo_Failure "goerrpkg.ErrorTypeBadRequest"
// @Failure 500 {object} Merchant_GetMerchantInfo_Failure "goerrpkg.ErrorTypeUnexpectedError"
// @Failure 400 {object} Merchant_GetMerchantInfo_Failure "gin.ErrorTypeBind"
// @Failure 500 {object} Merchant_GetMerchantInfo_Failure "Undefined error"
// @Produce json
// @Router /processing.merchant.merchant.Merchant/GetMerchantInfo [put]
// @tags Merchant
func (s *ginMerchantServer) GetMerchantInfo(c *gin.Context) error {
	ctx, span := dog.CreateSpan(c.Request.Context(), "GinMerchantServer_GetMerchantInfo")
	defer span.End()

	var request GetMerchantInfoRequestV1
	if err := c.ShouldBindJSON(&request); err != nil {
		return err
	}

	ctx = metadata.NewOutgoingContext(ctx, metadata.MD(c.Request.Header))

	response, err := s.client.GetMerchantInfo(ctx, &request)
	if err != nil {
		return err
	}

	c.JSON(http.StatusOK, &Merchant_GetMerchantInfo_Success{
		Status:     true,
		Message:    "Success",
		StatusCode: 0,
		Result:     response,
	})
	return nil
}

type Merchant_GetMerchantProjectsByBin_Success struct {
	// Status is the status of the response. Set true
	Status bool `json:"status"`
	// Message is a human-readable explanation of the status
	Message string `json:"message"`
	// StatusCode is the GoErr status. Set 0
	StatusCode int `json:"status_code"`
	// Result is the result of the request
	Result *GetMerchantProjectsByBinResponseV1 `json:"result"`
}

type Merchant_GetMerchantProjectsByBin_Failure struct {
	// Status is the status of the response. Set false
	Status bool `json:"status"`
	// Message is a human-readable explanation of the status
	Message string `json:"message"`
	// StatusCode is the GoErr status
	StatusCode int `json:"status_code"`
}

// GetMerchantProjectsByBin
// @Summary GetMerchantProjectsByBin
// @Security bearerAuth
// @ID Merchant_GetMerchantProjectsByBin
// @Accept json
// @Param request body GetMerchantProjectsByBinRequestV1 true "GetMerchantProjectsByBinRequestV1"
// @Success 200 {object} Merchant_GetMerchantProjectsByBin_Success
// @Failure 401 {object} Merchant_GetMerchantProjectsByBin_Failure "goerrpkg.ErrorTypeAuthorization"
// @Failure 404 {object} Merchant_GetMerchantProjectsByBin_Failure "goerrpkg.ErrorTypeNotFound"
// @Failure 400 {object} Merchant_GetMerchantProjectsByBin_Failure "goerrpkg.ErrorTypeBadRequest"
// @Failure 500 {object} Merchant_GetMerchantProjectsByBin_Failure "goerrpkg.ErrorTypeUnexpectedError"
// @Failure 400 {object} Merchant_GetMerchantProjectsByBin_Failure "gin.ErrorTypeBind"
// @Failure 500 {object} Merchant_GetMerchantProjectsByBin_Failure "Undefined error"
// @Produce json
// @Router /processing.merchant.merchant.Merchant/GetMerchantProjectsByBin [put]
// @tags Merchant
func (s *ginMerchantServer) GetMerchantProjectsByBin(c *gin.Context) error {
	ctx, span := dog.CreateSpan(c.Request.Context(), "GinMerchantServer_GetMerchantProjectsByBin")
	defer span.End()

	var request GetMerchantProjectsByBinRequestV1
	if err := c.ShouldBindJSON(&request); err != nil {
		return err
	}

	ctx = metadata.NewOutgoingContext(ctx, metadata.MD(c.Request.Header))

	response, err := s.client.GetMerchantProjectsByBin(ctx, &request)
	if err != nil {
		return err
	}

	c.JSON(http.StatusOK, &Merchant_GetMerchantProjectsByBin_Success{
		Status:     true,
		Message:    "Success",
		StatusCode: 0,
		Result:     response,
	})
	return nil
}

type Merchant_GetMerchantDataByID_Success struct {
	// Status is the status of the response. Set true
	Status bool `json:"status"`
	// Message is a human-readable explanation of the status
	Message string `json:"message"`
	// StatusCode is the GoErr status. Set 0
	StatusCode int `json:"status_code"`
	// Result is the result of the request
	Result *MerchantData `json:"result"`
}

type Merchant_GetMerchantDataByID_Failure struct {
	// Status is the status of the response. Set false
	Status bool `json:"status"`
	// Message is a human-readable explanation of the status
	Message string `json:"message"`
	// StatusCode is the GoErr status
	StatusCode int `json:"status_code"`
}

// GetMerchantDataByID
// @Summary GetMerchantDataByID
// @Security bearerAuth
// @ID Merchant_GetMerchantDataByID
// @Accept json
// @Param request body GetMerchantDataByIDRequestV1 true "GetMerchantDataByIDRequestV1"
// @Success 200 {object} Merchant_GetMerchantDataByID_Success
// @Failure 401 {object} Merchant_GetMerchantDataByID_Failure "goerrpkg.ErrorTypeAuthorization"
// @Failure 404 {object} Merchant_GetMerchantDataByID_Failure "goerrpkg.ErrorTypeNotFound"
// @Failure 400 {object} Merchant_GetMerchantDataByID_Failure "goerrpkg.ErrorTypeBadRequest"
// @Failure 500 {object} Merchant_GetMerchantDataByID_Failure "goerrpkg.ErrorTypeUnexpectedError"
// @Failure 400 {object} Merchant_GetMerchantDataByID_Failure "gin.ErrorTypeBind"
// @Failure 500 {object} Merchant_GetMerchantDataByID_Failure "Undefined error"
// @Produce json
// @Router /processing.merchant.merchant.Merchant/GetMerchantDataByID [put]
// @tags Merchant
func (s *ginMerchantServer) GetMerchantDataByID(c *gin.Context) error {
	ctx, span := dog.CreateSpan(c.Request.Context(), "GinMerchantServer_GetMerchantDataByID")
	defer span.End()

	var request GetMerchantDataByIDRequestV1
	if err := c.ShouldBindJSON(&request); err != nil {
		return err
	}

	ctx = metadata.NewOutgoingContext(ctx, metadata.MD(c.Request.Header))

	response, err := s.client.GetMerchantDataByID(ctx, &request)
	if err != nil {
		return err
	}

	c.JSON(http.StatusOK, &Merchant_GetMerchantDataByID_Success{
		Status:     true,
		Message:    "Success",
		StatusCode: 0,
		Result:     response,
	})
	return nil
}
