// Code generated by protoc-gen-go-mvp. DO NOT EDIT.
// versions:
// - protoc-gen-go-mvp v2.0.1
// created by mvp authors

package grpc

import (
	context "context"
	cntx "git.local/sensitive/mvp/pkg/cntx"
	ctxzap "github.com/grpc-ecosystem/go-grpc-middleware/logging/zap/ctxzap"
	zap "go.uber.org/zap"
	grpc "google.golang.org/grpc"
)

func file_inner_processing_grpc_terminal_cards_proto_message_GetCardInfoByPanRequestV1ToZap(
	label string,
	in *GetCardInfoByPanRequestV1,
) zap.Field {
	if in == nil {
		return zap.Reflect(label, in)
	}

	return zap.Dict(
		label,
		zap.Any("EightDigitBin", "[***hidden***]"),
		zap.Any("SixDigitBin", "[***hidden***]"),
		zap.Any("FiveDigitBin", "[***hidden***]"),
	)
}

func file_inner_processing_grpc_terminal_cards_proto_message_GetCardInfoByPanResponseV1ToZap(
	label string,
	in *GetCardInfoByPanResponseV1,
) zap.Field {
	if in == nil {
		return zap.Reflect(label, in)
	}

	return zap.Dict(
		label,
		zap.Any("IpsId", in.GetIpsId()),
		zap.Any("CountryId", in.GetCountryId()),
		zap.Any("IssuerId", in.GetIssuerId()),
	)
}

var _ TerminalCardsServer = (*loggedTerminalCardsServer)(nil)

func NewLoggedTerminalCardsServer(srv TerminalCardsServer) TerminalCardsServer {
	return &loggedTerminalCardsServer{srv: srv}
}

type loggedTerminalCardsServer struct {
	UnimplementedTerminalCardsServer

	srv TerminalCardsServer
}

func (s *loggedTerminalCardsServer) GetCardInfoByPan(
	ctx context.Context,
	request *GetCardInfoByPanRequestV1,
) (
	response *GetCardInfoByPanResponseV1,
	err error,
) {
	label := cntx.Begin(ctx, "TerminalCardsServer_GetCardInfoByPan")
	defer cntx.End(ctx, label)

	defer func() {
		if err != nil {
			ctxzap.AddFields(ctx, zap.Any(label+"error", err))
			return
		}

		ctxzap.AddFields(
			ctx,
			file_inner_processing_grpc_terminal_cards_proto_message_GetCardInfoByPanResponseV1ToZap(label+"response", response),
		)
	}()

	ctxzap.AddFields(
		ctx,
		file_inner_processing_grpc_terminal_cards_proto_message_GetCardInfoByPanRequestV1ToZap(label+"request", request),
	)

	response, err = s.srv.GetCardInfoByPan(ctx, request)

	return
}

var _ TerminalCardsClient = (*loggedTerminalCardsClient)(nil)

func NewLoggedTerminalCardsClient(client TerminalCardsClient) TerminalCardsClient {
	return &loggedTerminalCardsClient{client: client}
}

type loggedTerminalCardsClient struct {
	client TerminalCardsClient
}

func (s *loggedTerminalCardsClient) GetCardInfoByPan(
	ctx context.Context,
	request *GetCardInfoByPanRequestV1,
	opts ...grpc.CallOption,
) (
	response *GetCardInfoByPanResponseV1,
	err error,
) {
	label := cntx.Begin(ctx, "TerminalCardsClient_GetCardInfoByPan")
	defer cntx.End(ctx, label)

	defer func() {
		if err != nil {
			ctxzap.AddFields(ctx, zap.Any(label+"error", err))
			return
		}

		ctxzap.AddFields(
			ctx,
			file_inner_processing_grpc_terminal_cards_proto_message_GetCardInfoByPanResponseV1ToZap(label+"response", response),
		)
	}()

	ctxzap.AddFields(
		ctx,
		file_inner_processing_grpc_terminal_cards_proto_message_GetCardInfoByPanRequestV1ToZap(label+"request", request),
	)

	response, err = s.client.GetCardInfoByPan(ctx, request, opts...)

	return
}
