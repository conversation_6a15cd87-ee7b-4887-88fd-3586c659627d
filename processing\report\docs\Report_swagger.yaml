basePath: /report
definitions:
  datatypes.JSONMap:
    additionalProperties: true
    type: object
  delivery_http_v1.createOrUpdateBalanceHistoriesUserFields.request:
    properties:
      fields:
        items:
          $ref: '#/definitions/model.FieldList'
        type: array
        uniqueItems: true
    required:
    - fields
    type: object
  delivery_http_v1.createOrUpdatePaymentOrdersCustomization.request:
    properties:
      fields:
        items:
          $ref: '#/definitions/model.FieldList'
        type: array
        uniqueItems: true
    required:
    - fields
    type: object
  delivery_http_v1.createOrUpdateSplittingSettingCustomization.request:
    properties:
      fields:
        items:
          $ref: '#/definitions/model.FieldList'
        type: array
        uniqueItems: true
    required:
    - fields
    type: object
  delivery_http_v1.createOrUpdateUserFields.request:
    properties:
      fields:
        items:
          $ref: '#/definitions/model.FieldList'
        type: array
        uniqueItems: true
    required:
    - fields
    type: object
  delivery_http_v1.getBalanceHistories.response:
    properties:
      balance_histories:
        items:
          $ref: '#/definitions/delivery_http_v1.historyResponse'
        type: array
      total_info:
        $ref: '#/definitions/delivery_http_v1.totalInfo'
    type: object
  delivery_http_v1.getTransferDetails.response:
    properties:
      amount:
        type: number
      created_at:
        type: string
      external_project_id:
        type: string
      processor_name:
        type: string
      row_operation_id:
        type: integer
    type: object
  delivery_http_v1.getTransfers.data:
    properties:
      account:
        properties:
          account_bank_name:
            type: string
          account_number:
            type: string
        type: object
      balance_owner:
        properties:
          balance_owner_bin:
            type: string
          balance_owner_id:
            type: integer
          balance_owner_name:
            type: string
        type: object
      transfer:
        properties:
          beneficiary_code:
            type: string
          created_at:
            type: string
          description:
            type: string
          finished_at:
            type: string
          payment_purpose_code:
            type: string
          recipient_account:
            type: string
          transfer_amount:
            type: number
          transfer_id:
            type: integer
          transfer_status_code:
            type: string
          transfer_type_code:
            type: string
        type: object
    type: object
  delivery_http_v1.historyResponse:
    properties:
      account:
        properties:
          account_bank_name:
            type: string
          account_number:
            type: string
        required:
        - account_bank_name
        - account_number
        type: object
      balance_calculated_date:
        type: string
      balance_main_amount:
        type: number
      balance_minimal_amount:
        type: number
      balance_owner:
        properties:
          balance_owner_bin:
            type: string
          balance_owner_earn_type_code:
            type: string
          balance_owner_id:
            type: integer
          balance_owner_name:
            type: string
          balance_owner_status_code:
            type: string
        required:
        - balance_owner_bin
        - balance_owner_earn_type_code
        - balance_owner_id
        - balance_owner_name
        - balance_owner_status_code
        type: object
      merchant:
        properties:
          merchant_id:
            type: integer
          merchant_name:
            type: string
        required:
        - merchant_id
        - merchant_name
        type: object
    required:
    - account
    - balance_calculated_date
    - balance_main_amount
    - balance_minimal_amount
    - balance_owner
    - merchant
    type: object
  delivery_http_v1.splittingSettingsResponse:
    properties:
      merchant:
        properties:
          merchant_id:
            type: integer
          merchant_name:
            type: string
        required:
        - merchant_id
        - merchant_name
        type: object
      payer_project:
        properties:
          payer_project_id:
            type: integer
          payer_project_name:
            type: string
          payer_tax_percentage:
            type: number
        required:
        - payer_project_id
        - payer_project_name
        - payer_tax_percentage
        type: object
      recipient_project:
        properties:
          recipient_project_id:
            type: integer
          recipient_project_name:
            type: string
        required:
        - recipient_project_id
        - recipient_project_name
        type: object
    required:
    - merchant
    - payer_project
    - recipient_project
    type: object
  delivery_http_v1.totalInfo:
    properties:
      total_amount:
        type: number
      total_histories:
        type: integer
    required:
    - total_amount
    - total_histories
    type: object
  delivery_http_v1.updateTransferCustomization.request:
    properties:
      fields:
        items:
          $ref: '#/definitions/model.FieldList'
        type: array
        uniqueItems: true
    required:
    - fields
    type: object
  middlewares.Empty:
    type: object
  middlewares.Response-array_delivery_http_v1_getTransferDetails_response:
    properties:
      message:
        type: string
      result:
        items:
          $ref: '#/definitions/delivery_http_v1.getTransferDetails.response'
        type: array
      status:
        type: boolean
    required:
    - message
    - result
    - status
    type: object
  middlewares.Response-array_delivery_http_v1_getTransfers_data:
    properties:
      message:
        type: string
      result:
        items:
          $ref: '#/definitions/delivery_http_v1.getTransfers.data'
        type: array
      status:
        type: boolean
    required:
    - message
    - result
    - status
    type: object
  middlewares.Response-array_delivery_http_v1_splittingSettingsResponse:
    properties:
      message:
        type: string
      result:
        items:
          $ref: '#/definitions/delivery_http_v1.splittingSettingsResponse'
        type: array
      status:
        type: boolean
    required:
    - message
    - result
    - status
    type: object
  middlewares.Response-array_model_AcquirerCommission:
    properties:
      message:
        type: string
      result:
        items:
          $ref: '#/definitions/model.AcquirerCommission'
        type: array
      status:
        type: boolean
    required:
    - message
    - result
    - status
    type: object
  middlewares.Response-array_model_ProjectLowerCommission:
    properties:
      message:
        type: string
      result:
        items:
          $ref: '#/definitions/model.ProjectLowerCommission'
        type: array
      status:
        type: boolean
    required:
    - message
    - result
    - status
    type: object
  middlewares.Response-array_model_ProjectUpperCommission:
    properties:
      message:
        type: string
      result:
        items:
          $ref: '#/definitions/model.ProjectUpperCommission'
        type: array
      status:
        type: boolean
    required:
    - message
    - result
    - status
    type: object
  middlewares.Response-array_model_Terminal:
    properties:
      message:
        type: string
      result:
        items:
          $ref: '#/definitions/model.Terminal'
        type: array
      status:
        type: boolean
    required:
    - message
    - result
    - status
    type: object
  middlewares.Response-array_schema_ClientOperationResponse:
    properties:
      message:
        type: string
      result:
        items:
          $ref: '#/definitions/schema.ClientOperationResponse'
        type: array
      status:
        type: boolean
    required:
    - message
    - result
    - status
    type: object
  middlewares.Response-array_schema_OverdraftOperationResponse:
    properties:
      message:
        type: string
      result:
        items:
          $ref: '#/definitions/schema.OverdraftOperationResponse'
        type: array
      status:
        type: boolean
    required:
    - message
    - result
    - status
    type: object
  middlewares.Response-array_schema_PaymentOrderResponse:
    properties:
      message:
        type: string
      result:
        items:
          $ref: '#/definitions/schema.PaymentOrderResponse'
        type: array
      status:
        type: boolean
    required:
    - message
    - result
    - status
    type: object
  middlewares.Response-array_schema_RuleResponse:
    properties:
      message:
        type: string
      result:
        items:
          $ref: '#/definitions/schema.RuleResponse'
        type: array
      status:
        type: boolean
    required:
    - message
    - result
    - status
    type: object
  middlewares.Response-array_schema_Transaction:
    properties:
      message:
        type: string
      result:
        items:
          $ref: '#/definitions/schema.Transaction'
        type: array
      status:
        type: boolean
    required:
    - message
    - result
    - status
    type: object
  middlewares.Response-array_schema_TransferRuleResponse:
    properties:
      message:
        type: string
      result:
        items:
          $ref: '#/definitions/schema.TransferRuleResponse'
        type: array
      status:
        type: boolean
    required:
    - message
    - result
    - status
    type: object
  middlewares.Response-array_schema_UserAmountResponse:
    properties:
      message:
        type: string
      result:
        items:
          $ref: '#/definitions/schema.UserAmountResponse'
        type: array
      status:
        type: boolean
    required:
    - message
    - result
    - status
    type: object
  middlewares.Response-delivery_http_v1_getBalanceHistories_response:
    properties:
      message:
        type: string
      result:
        $ref: '#/definitions/delivery_http_v1.getBalanceHistories.response'
      status:
        type: boolean
    required:
    - message
    - result
    - status
    type: object
  middlewares.Response-middlewares_Empty:
    properties:
      message:
        type: string
      result:
        $ref: '#/definitions/middlewares.Empty'
      status:
        type: boolean
    required:
    - message
    - result
    - status
    type: object
  middlewares.Response-model_Field:
    properties:
      message:
        type: string
      result:
        $ref: '#/definitions/model.Field'
      status:
        type: boolean
    required:
    - message
    - result
    - status
    type: object
  middlewares.Response-model_Transactions:
    properties:
      message:
        type: string
      result:
        items:
          $ref: '#/definitions/model.Transaction'
        type: array
      status:
        type: boolean
    required:
    - message
    - result
    - status
    type: object
  middlewares.Response-schema_AggregatedInfoResponse:
    properties:
      message:
        type: string
      result:
        $ref: '#/definitions/schema.AggregatedInfoResponse'
      status:
        type: boolean
    required:
    - message
    - result
    - status
    type: object
  middlewares.Response-schema_RuleResponse:
    properties:
      message:
        type: string
      result:
        $ref: '#/definitions/schema.RuleResponse'
      status:
        type: boolean
    required:
    - message
    - result
    - status
    type: object
  middlewares.Response-string:
    properties:
      message:
        type: string
      result:
        type: string
      status:
        type: boolean
    required:
    - message
    - result
    - status
    type: object
  model.AcquirerCommission:
    properties:
      acquirer_id:
        type: integer
      acquirer_name:
        type: string
      acquirer_option_id:
        type: integer
      aggregated_type_id:
        type: integer
      aggregated_type_name:
        type: string
      commission_id:
        type: integer
      commission_percentage:
        type: number
      country_id:
        type: integer
      country_name:
        type: string
      end_date:
        type: string
      fixed_amount:
        type: number
      ips_id:
        type: integer
      ips_name:
        type: string
      issuer_id:
        type: integer
      issuer_name:
        type: string
      last_transaction_date:
        type: string
      merchant_id:
        type: integer
      merchant_name:
        type: string
      min_amount_commission:
        type: number
      project_id:
        type: integer
      project_name:
        type: string
      start_date:
        type: string
    required:
    - acquirer_id
    - acquirer_name
    - acquirer_option_id
    - aggregated_type_id
    - aggregated_type_name
    - merchant_id
    - merchant_name
    - project_id
    - project_name
    type: object
  model.Field:
    properties:
      fields:
        description: ReportID identity of report.reports these fields will used for
        items:
          $ref: '#/definitions/model.FieldList'
        type: array
      id:
        type: integer
      source:
        description: Source defines url path from which fields are requested
        type: string
      user_id:
        description: UserID identity of auth.user these fields will be bound to
        type: integer
    required:
    - fields
    - id
    - source
    - user_id
    type: object
  model.FieldList:
    properties:
      id:
        description: 'ID unique field code ex: transaction_id'
        type: string
      is_required:
        description: IsRequired pointer is used because validator won't pass if value
          is false
        type: boolean
      is_visible:
        description: IsVisible pointer is used because validator won't pass if value
          is false
        type: boolean
      sequence:
        description: Sequence is used to order this field
        type: integer
    required:
    - id
    - is_required
    - is_visible
    - sequence
    type: object
  model.ProjectLowerCommission:
    properties:
      acquirer_id:
        type: integer
      acquirer_name:
        type: string
      aggregated_type_id:
        type: integer
      aggregated_type_name:
        type: string
      commission_id:
        type: integer
      commission_percentage:
        type: number
      country_id:
        type: integer
      country_name:
        type: string
      end_date:
        type: string
      fixed_amount:
        type: number
      ips_id:
        type: integer
      ips_name:
        type: string
      issuer_id:
        type: integer
      issuer_name:
        type: string
      last_transaction_date:
        type: string
      merchant_id:
        type: integer
      merchant_name:
        type: string
      min_amount_commission:
        type: number
      project_id:
        type: integer
      project_lower_option_type_id:
        type: integer
      project_lower_option_type_name:
        type: string
      project_name:
        type: string
      project_option_id:
        type: integer
      start_date:
        type: string
    required:
    - acquirer_name
    - aggregated_type_name
    - merchant_name
    - project_lower_option_type_id
    - project_lower_option_type_name
    - project_name
    - project_option_id
    type: object
  model.ProjectUpperCommission:
    properties:
      acquirer_name:
        type: string
      aggregated_type_id:
        type: integer
      aggregated_type_name:
        type: string
      amount:
        type: number
      commission_id:
        type: integer
      commission_percentage:
        type: number
      end_date:
        type: string
      fixed_amount:
        type: number
      last_transaction_date:
        type: string
      merchant_id:
        type: integer
      merchant_name:
        type: string
      min_amount_commission:
        type: number
      project_id:
        type: integer
      project_name:
        type: string
      project_option_id:
        type: integer
      sign:
        type: string
      start_date:
        type: string
    required:
    - acquirer_name
    - aggregated_type_name
    - amount
    - merchant_id
    - merchant_name
    - project_id
    - project_name
    - project_option_id
    - sign
    type: object
  model.Terminal:
    properties:
      acquirer_id:
        type: integer
      acquirer_name:
        type: string
      acquirer_terminal_name:
        type: string
      description:
        type: string
      id:
        type: integer
      project:
        type: string
      status:
        type: integer
      terminal_bank_id:
        type: integer
      transaction_type_name:
        type: string
      transit:
        type: boolean
      transit_account_bank:
        type: string
      transit_account_number:
        type: string
      two_stage_payment_timeout:
        type: integer
    type: object
  model.Transaction:
    properties:
      acquirer:
        type: string
      acquirer_code:
        type: string
      acquirer_commission_amount:
        type: number
      acquirer_id:
        type: integer
      additional_data:
        $ref: '#/definitions/datatypes.JSONMap'
      amount:
        type: number
      callback_status:
        type: integer
      callback_url:
        type: string
      created_date:
        type: string
      end_date:
        type: string
      issuer:
        type: string
      issuer_id:
        type: integer
      last_refund_date:
        type: string
      lower_commission:
        type: number
      masked_pan:
        type: string
      merchant:
        type: string
      merchant_bin:
        type: string
      merchant_id:
        type: integer
      order_number:
        type: string
      project_client_id:
        type: string
      project_id:
        type: integer
      project_name:
        type: string
      refund_count:
        type: integer
      refund_total_amount:
        type: number
      rrn:
        type: string
      transaction_id:
        type: integer
      transaction_status:
        type: string
      transaction_status_id:
        type: integer
      transaction_type:
        type: string
      transaction_type_id:
        type: integer
      updated_date:
        type: string
      upper_commission:
        type: number
    type: object
  schema.Account:
    properties:
      account_bank_name:
        type: string
      account_number:
        type: string
    required:
    - account_bank_name
    - account_number
    type: object
  schema.AggregatedInfoResponse:
    properties:
      total_amount:
        type: number
      total_count:
        type: integer
    type: object
  schema.BalanceOwner:
    properties:
      balance_owner_bin:
        type: string
      balance_owner_id:
        type: integer
      balance_owner_name:
        type: string
    required:
    - balance_owner_bin
    - balance_owner_id
    - balance_owner_name
    type: object
  schema.BalanceOwnerTr:
    properties:
      balance_country_code:
        type: string
      balance_owner_ID:
        type: integer
      balance_owner_bin:
        type: string
      balance_owner_name:
        type: string
      balance_owner_tag:
        type: string
    type: object
  schema.ClientAccount:
    properties:
      account_bank_name:
        type: string
      account_number:
        type: string
    required:
    - account_bank_name
    - account_number
    type: object
  schema.ClientBalanceOwner:
    properties:
      balance_owner_bin:
        type: string
      balance_owner_id:
        type: integer
      balance_owner_name:
        type: string
    required:
    - balance_owner_bin
    - balance_owner_id
    - balance_owner_name
    type: object
  schema.ClientMerchant:
    properties:
      merchant_id:
        type: integer
      merchant_name:
        type: string
    required:
    - merchant_id
    - merchant_name
    type: object
  schema.ClientOperation:
    properties:
      operation_amount:
        type: number
      operation_date:
        type: string
      operation_id:
        type: integer
      operation_status_code:
        type: string
      operation_type_code:
        type: string
    required:
    - operation_amount
    - operation_date
    - operation_id
    - operation_status_code
    - operation_type_code
    type: object
  schema.ClientOperationResponse:
    properties:
      account:
        $ref: '#/definitions/schema.ClientAccount'
      balance_owner:
        $ref: '#/definitions/schema.ClientBalanceOwner'
      balance_type_code:
        type: string
      merchant:
        $ref: '#/definitions/schema.ClientMerchant'
      operation:
        $ref: '#/definitions/schema.ClientOperation'
      transaction_id:
        type: integer
    required:
    - balance_type_code
    - transaction_id
    type: object
  schema.Merchant:
    properties:
      merchant_id:
        type: integer
      merchant_name:
        type: string
    required:
    - merchant_id
    - merchant_name
    type: object
  schema.Operation:
    properties:
      created_at:
        type: string
      finished_at:
        type: string
      operation_amount:
        type: number
      operation_id:
        type: integer
      operation_type_code:
        type: string
    required:
    - created_at
    - finished_at
    - operation_amount
    - operation_id
    - operation_type_code
    type: object
  schema.Overdraft:
    properties:
      overdraft_amount:
        type: number
      overdraft_expired_at:
        type: string
      overdraft_started_at:
        type: string
    type: object
  schema.OverdraftBalanceOwner:
    properties:
      balance_owner_bin:
        type: string
      balance_owner_earn_type_code:
        type: string
      balance_owner_id:
        type: integer
      balance_owner_name:
        type: string
      balance_owner_status_code:
        type: string
    type: object
  schema.OverdraftOperationResponse:
    properties:
      account:
        $ref: '#/definitions/schema.Account'
      balance_calculated_date:
        type: string
      balance_credit_amount:
        type: number
      balance_owner:
        $ref: '#/definitions/schema.OverdraftBalanceOwner'
      merchant:
        $ref: '#/definitions/schema.Merchant'
      overdraft:
        $ref: '#/definitions/schema.Overdraft'
    type: object
  schema.PaymentOrderAccount:
    properties:
      bank_id:
        type: integer
      bank_name:
        type: string
      id:
        type: integer
      number:
        type: string
    type: object
  schema.PaymentOrderBalanceOwner:
    properties:
      bin:
        type: string
      country:
        type: string
      id:
        type: integer
      name:
        type: string
      tag:
        type: string
    type: object
  schema.PaymentOrderResp:
    properties:
      date:
        type: string
      end_date:
        type: string
      id:
        type: integer
      start_date:
        type: string
      status_id:
        type: integer
      status_name:
        type: string
    type: object
  schema.PaymentOrderResponse:
    properties:
      account:
        $ref: '#/definitions/schema.PaymentOrderAccount'
      balance_owner:
        $ref: '#/definitions/schema.PaymentOrderBalanceOwner'
      payment_order:
        $ref: '#/definitions/schema.PaymentOrderResp'
    type: object
  schema.RuleResponse:
    properties:
      amount_from:
        type: number
      amount_to:
        type: number
      bank_id:
        type: integer
      bank_name:
        type: string
      country_id:
        type: integer
      country_name:
        type: string
      created_at:
        type: string
      id:
        type: integer
      ips_id:
        type: integer
      ips_name:
        type: string
      is_base:
        type: boolean
      project_id:
        type: integer
      project_name:
        type: string
      status:
        type: boolean
      transaction_type_name:
        type: string
      updated_at:
        type: string
      weight:
        type: integer
    type: object
  schema.RuleTr:
    properties:
      created_at:
        type: string
      is_active:
        type: boolean
      rule_id:
        type: integer
    type: object
  schema.Transaction:
    properties:
      account:
        $ref: '#/definitions/schema.Account'
      balance_owner:
        $ref: '#/definitions/schema.BalanceOwner'
      merchant:
        $ref: '#/definitions/schema.Merchant'
      operation:
        $ref: '#/definitions/schema.Operation'
      transfer:
        $ref: '#/definitions/schema.Transfer'
    type: object
  schema.Transfer:
    properties:
      beneficiary_code:
        type: string
      description:
        type: string
      payment_purpose_code:
        type: string
      recipient_account:
        type: string
      transfer_id:
        type: integer
    required:
    - beneficiary_code
    - description
    - payment_purpose_code
    - recipient_account
    - transfer_id
    type: object
  schema.TransferRuleResponse:
    properties:
      balance_owner:
        $ref: '#/definitions/schema.BalanceOwnerTr'
      rule:
        $ref: '#/definitions/schema.RuleTr'
      transfer:
        $ref: '#/definitions/schema.TransferTr'
      transit_account:
        $ref: '#/definitions/schema.TransitAccountTr'
    type: object
  schema.TransferTr:
    properties:
      amount:
        type: number
      balance_main_total_withdrawals:
        type: boolean
      beneficiary_code:
        type: string
      description:
        type: string
      frequency:
        type: string
      payment_purpose_code:
        type: string
      recipient_account:
        type: string
    type: object
  schema.TransitAccountTr:
    properties:
      account_bank_name:
        type: string
      account_number:
        type: string
    type: object
  schema.UserAmountResponse:
    properties:
      id:
        type: integer
      merchant_name:
        type: string
      project_client_amount_max:
        type: number
      project_client_amount_sum:
        type: number
      project_client_id:
        type: string
      project_name:
        type: string
      transaction_status_name:
        type: string
      transaction_type_name:
        type: string
      verification_user_id:
        type: integer
    type: object
host: prapi.dev-tarlanpayments.kz
info:
  contact: {}
  description: report
  title: report
paths:
  /api/v1/acquirer/options/with-commissions:
    get:
      consumes:
      - application/json
      description: Запрос на получение списка опций эквайера с актульной комиссией
        на текущий момент
      parameters:
      - description: per_page
        in: query
        name: per_page
        type: integer
      - description: page
        in: query
        name: page
        type: integer
      - in: query
        minLength: 1
        name: acquirer_id
        type: string
      - in: query
        minLength: 1
        name: aggregated_type_id
        type: string
      - in: query
        name: country_id
        type: string
      - in: query
        name: ips_id
        type: string
      - in: query
        name: issuer_id
        type: string
      - in: query
        name: last_transaction_date_from
        type: string
      - in: query
        name: last_transaction_date_to
        type: string
      - in: query
        minLength: 1
        name: merchant_id
        type: string
      - in: query
        minLength: 1
        name: project_id
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          headers:
            x-page:
              description: "1"
              type: int
            x-per-page:
              description: "1"
              type: int
            x-total:
              description: "11"
              type: int
          schema:
            $ref: '#/definitions/middlewares.Response-array_model_AcquirerCommission'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/middlewares.Response-middlewares_Empty'
      security:
      - bearerAuth: []
      summary: Получение списка опций эквайера с актуальной комиссией
      tags:
      - commissions
  /api/v1/acquirer/options/with-commissions/download/csv:
    get:
      consumes:
      - application/json
      description: Запрос на получение списка опций эквайера с актульной комиссией
        на текущий момент
      parameters:
      - description: per_page
        in: query
        name: per_page
        type: integer
      - description: page
        in: query
        name: page
        type: integer
      - in: query
        minLength: 1
        name: acquirer_id
        type: string
      - in: query
        minLength: 1
        name: aggregated_type_id
        type: string
      - in: query
        name: country_id
        type: string
      - in: query
        name: ips_id
        type: string
      - in: query
        name: issuer_id
        type: string
      - in: query
        name: language
        required: true
        type: string
      - in: query
        name: last_transaction_date_from
        type: string
      - in: query
        name: last_transaction_date_to
        type: string
      - in: query
        minLength: 1
        name: merchant_id
        type: string
      - in: query
        minLength: 1
        name: project_id
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/middlewares.Response-string'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/middlewares.Response-middlewares_Empty'
      security:
      - bearerAuth: []
      summary: Выгрузка списка опций эквайера с актуальной комиссией в формате csv
      tags:
      - commissions
  /api/v1/acquirer/options/with-commissions/download/xlsx:
    get:
      consumes:
      - application/json
      description: Запрос на получение списка опций эквайера с актульной комиссией
        на текущий момент
      parameters:
      - description: per_page
        in: query
        name: per_page
        type: integer
      - description: page
        in: query
        name: page
        type: integer
      - in: query
        minLength: 1
        name: acquirer_id
        type: string
      - in: query
        minLength: 1
        name: aggregated_type_id
        type: string
      - in: query
        name: country_id
        type: string
      - in: query
        name: ips_id
        type: string
      - in: query
        name: issuer_id
        type: string
      - in: query
        name: language
        required: true
        type: string
      - in: query
        name: last_transaction_date_from
        type: string
      - in: query
        name: last_transaction_date_to
        type: string
      - in: query
        minLength: 1
        name: merchant_id
        type: string
      - in: query
        minLength: 1
        name: project_id
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/middlewares.Response-string'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/middlewares.Response-middlewares_Empty'
      security:
      - bearerAuth: []
      summary: Выгрузка списка опций эквайера с актуальной комиссией в формате xlsx
      tags:
      - commissions
  /api/v1/amount:
    get:
      description: Получение списка суммарной и максимальной транзакции по пользователю
        за определенный период времени
      parameters:
      - in: query
        name: created_at
        type: string
      - in: query
        name: finished_at
        type: string
      - in: query
        name: max_amount_lower_bound
        type: number
      - in: query
        name: max_amount_upper_bound
        type: number
      - in: query
        name: project_client_id
        type: string
      - in: query
        name: sum_amount_lower_bound
        type: number
      - in: query
        name: sum_amount_upper_bound
        type: number
      - in: query
        name: transaction_status_id
        type: integer
      - collectionFormat: csv
        in: query
        items:
          type: integer
        name: transaction_type_ids
        type: array
      - description: per_page
        in: query
        name: per_page
        type: integer
      - description: page
        in: query
        name: page
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/middlewares.Response-array_schema_UserAmountResponse'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/middlewares.Response-middlewares_Empty'
      security:
      - bearerAuth: []
      summary: Получение списка пользователей с пороговыми суммами
      tags:
      - amounts
  /api/v1/billing/balance/histories:
    get:
      description: |-
        Берет данные из view report.vw_balance_history_details
        Данные отображаются на странице CRM "выписка транзитного счета"
      parameters:
      - in: query
        name: account_bank_id
        type: string
      - in: query
        name: balance_calculated_date_from
        type: string
      - in: query
        name: balance_calculated_date_to
        type: string
      - in: query
        name: balance_owner_bin
        type: string
      - in: query
        name: balance_owner_earn_type_id
        type: integer
      - in: query
        name: balance_owner_id
        type: string
      - in: query
        name: balance_owner_status_id
        type: integer
      - in: query
        name: merchant_id
        type: string
      - description: per_page
        in: query
        name: per_page
        type: integer
      - description: page
        in: query
        name: page
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/middlewares.Response-delivery_http_v1_getBalanceHistories_response'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/middlewares.Response-middlewares_Empty'
      security:
      - bearerAuth: []
      summary: Получение данных по движению денежных средств на балансах транзитных
        счетов
      tags:
      - billing-balance-histories
  /api/v1/billing/balance/histories/customize:
    get:
      description: Используется для получения полей которые нужно отобразить для пользователя
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/middlewares.Response-model_Field'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/middlewares.Response-middlewares_Empty'
      security:
      - bearerAuth: []
      summary: Получение сохранённых полей
      tags:
      - billing-balance-histories
    put:
      description: Используется для обновление полей которые юзер бы хотел видеть
        у себя
      parameters:
      - description: Field Data
        in: body
        name: data
        required: true
        schema:
          $ref: '#/definitions/delivery_http_v1.createOrUpdateBalanceHistoriesUserFields.request'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/middlewares.Response-string'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/middlewares.Response-middlewares_Empty'
      security:
      - bearerAuth: []
      summary: Обновление полей пользователя
      tags:
      - billing-balance-histories
  /api/v1/billing/balance/histories/download/csv:
    get:
      description: |-
        Берет данные из view report.vw_balance_history_details
        Данные отображаются на странице CRM "выписка транзитного счета"
      parameters:
      - in: query
        name: account_bank_id
        type: string
      - in: query
        name: balance_calculated_date_from
        type: string
      - in: query
        name: balance_calculated_date_to
        type: string
      - in: query
        name: balance_owner_bin
        type: string
      - in: query
        name: balance_owner_earn_type_id
        type: integer
      - in: query
        name: balance_owner_id
        type: string
      - in: query
        name: balance_owner_status_id
        type: integer
      - in: query
        name: language
        required: true
        type: string
      - in: query
        name: merchant_id
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/middlewares.Response-string'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/middlewares.Response-middlewares_Empty'
      security:
      - bearerAuth: []
      summary: Выгрузка данных по движению денежных средств на балансах транзитных
        счетов в формате csv
      tags:
      - billing-balance-histories
  /api/v1/billing/balance/histories/download/xlsx:
    get:
      description: |-
        Берет данные из view report.vw_balance_history_details
        Данные отображаются на странице CRM "выписка транзитного счета"
      parameters:
      - in: query
        name: account_bank_id
        type: string
      - in: query
        name: balance_calculated_date_from
        type: string
      - in: query
        name: balance_calculated_date_to
        type: string
      - in: query
        name: balance_owner_bin
        type: string
      - in: query
        name: balance_owner_earn_type_id
        type: integer
      - in: query
        name: balance_owner_id
        type: string
      - in: query
        name: balance_owner_status_id
        type: integer
      - in: query
        name: language
        required: true
        type: string
      - in: query
        name: merchant_id
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/middlewares.Response-string'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/middlewares.Response-middlewares_Empty'
      security:
      - bearerAuth: []
      summary: Выгрузка данных по движению денежных средств на балансах транзитных
        счетов в формате xlsx
      tags:
      - billing-balance-histories
  /api/v1/billing/client/operations:
    get:
      description: |-
        Отображение клиентских операций по фильтру на фронте
        Достает данные с view client_operations
      parameters:
      - in: query
        name: account_bank_id
        type: string
      - in: query
        name: account_number
        type: string
      - in: query
        name: balance_owner_bin
        type: string
      - in: query
        name: balance_owner_id
        type: string
      - in: query
        name: balance_type_code
        type: string
      - in: query
        name: created_at_from
        type: string
      - in: query
        name: created_at_to
        type: string
      - in: query
        name: merchant_id
        type: string
      - in: query
        name: operation_amount_from
        type: number
      - in: query
        name: operation_amount_to
        type: number
      - in: query
        name: operation_id
        type: string
      - in: query
        name: operation_status_id
        type: string
      - in: query
        name: operation_type_id
        type: string
      - in: query
        name: transaction_id
        type: string
      - description: per_page
        in: query
        name: per_page
        type: integer
      - description: page
        in: query
        name: page
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/middlewares.Response-array_schema_ClientOperationResponse'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/middlewares.Response-middlewares_Empty'
      security:
      - bearerAuth: []
      summary: Получение списка клиентских операций из ресурса billing
      tags:
      - billing-client-operations
  /api/v1/billing/client/operations/customize:
    get:
      description: Используется для получения полей которые нужно отобразить для пользователя
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/middlewares.Response-model_Field'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/middlewares.Response-middlewares_Empty'
      security:
      - bearerAuth: []
      summary: Получение сохранённых полей в клеинетских операциях
      tags:
      - billing-client-operations
    put:
      description: Используется для обновление полей которые юзер бы хотел видеть
        у себя
      parameters:
      - description: Field Data
        in: body
        name: data
        required: true
        schema:
          $ref: '#/definitions/delivery_http_v1.createOrUpdateUserFields.request'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/middlewares.Response-string'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/middlewares.Response-middlewares_Empty'
      security:
      - bearerAuth: []
      summary: Обновление полей пользователя по клиентским операциям
      tags:
      - billing-client-operations
  /api/v1/billing/client/operations/download/csv:
    get:
      consumes:
      - application/json
      description: Фильтры находятся в теле запроса
      parameters:
      - in: query
        name: account_bank_id
        type: string
      - in: query
        name: account_number
        type: string
      - in: query
        name: balance_owner_bin
        type: string
      - in: query
        name: balance_owner_id
        type: string
      - in: query
        name: balance_type_code
        type: string
      - in: query
        name: create_at_from
        type: string
      - in: query
        name: create_at_to
        type: string
      - in: query
        name: language
        required: true
        type: string
      - in: query
        name: merchant_id
        type: string
      - in: query
        name: operation_amount_from
        type: number
      - in: query
        name: operation_amount_to
        type: number
      - in: query
        name: operation_id
        type: string
      - in: query
        name: operation_status_id
        type: string
      - in: query
        name: operation_type_id
        type: string
      - in: query
        name: transaction_id
        type: string
      - description: per_page
        in: query
        name: per_page
        type: integer
      - description: page
        in: query
        name: page
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/middlewares.Response-string'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/middlewares.Response-middlewares_Empty'
      security:
      - bearerAuth: []
      summary: Выгрузка списка клиентских операций по фильтрам в формате csv
      tags:
      - billing-client-operations
  /api/v1/billing/client/operations/download/xlsx:
    get:
      consumes:
      - application/json
      description: Фильтры находятся в теле запроса
      parameters:
      - in: query
        name: account_bank_id
        type: string
      - in: query
        name: account_number
        type: string
      - in: query
        name: balance_owner_bin
        type: string
      - in: query
        name: balance_owner_id
        type: string
      - in: query
        name: balance_type_code
        type: string
      - in: query
        name: create_at_from
        type: string
      - in: query
        name: create_at_to
        type: string
      - in: query
        name: language
        required: true
        type: string
      - in: query
        name: merchant_id
        type: string
      - in: query
        name: operation_amount_from
        type: number
      - in: query
        name: operation_amount_to
        type: number
      - in: query
        name: operation_id
        type: string
      - in: query
        name: operation_status_id
        type: string
      - in: query
        name: operation_type_id
        type: string
      - in: query
        name: transaction_id
        type: string
      - description: per_page
        in: query
        name: per_page
        type: integer
      - description: page
        in: query
        name: page
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/middlewares.Response-string'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/middlewares.Response-middlewares_Empty'
      security:
      - bearerAuth: []
      summary: Выгрузка списка клиентских операций по фильтрам в формате xlsx
      tags:
      - billing-client-operations
  /api/v1/billing/finance/operations:
    get:
      description: |-
        Используется на фронте на странице финансовые операций.
        Берет данные из view Finance_operations
      parameters:
      - description: per_page
        in: query
        name: per_page
        type: integer
      - description: page
        in: query
        name: page
        type: integer
      - in: query
        name: account_bank_id
        type: string
      - in: query
        name: account_number
        type: string
      - in: query
        name: balance_owner_bin
        type: string
      - in: query
        name: balance_owner_id
        type: string
      - in: query
        name: created_at_from
        type: string
      - in: query
        name: created_at_to
        type: string
      - in: query
        name: merchant_id
        type: string
      - in: query
        name: operation_amount_from
        type: number
      - in: query
        name: operation_amount_to
        type: number
      - in: query
        name: operation_id
        type: integer
      - in: query
        name: operation_type_id
        type: integer
      - in: query
        name: transfer_id
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/middlewares.Response-array_schema_Transaction'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/middlewares.Response-middlewares_Empty'
      security:
      - bearerAuth: []
      summary: Получение списка финансовых операций из ресурса billing
      tags:
      - billing-finance-operations
  /api/v1/billing/finance/operations/customize:
    get:
      description: Используется для получения полей которые нужно отобразить для пользователя
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/middlewares.Response-model_Field'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/middlewares.Response-middlewares_Empty'
      security:
      - bearerAuth: []
      summary: Получение сохранённых полей в финансовых операциях
      tags:
      - billing-finance-operations
    put:
      description: Используется для обновление полей которые юзер бы хотел видеть
        у себя
      parameters:
      - description: Field Data
        in: body
        name: data
        required: true
        schema:
          $ref: '#/definitions/delivery_http_v1.createOrUpdateUserFields.request'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/middlewares.Response-string'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/middlewares.Response-middlewares_Empty'
      security:
      - bearerAuth: []
      summary: Обновление полей пользователя по финансовым операциям
      tags:
      - billing-finance-operations
  /api/v1/billing/finance/operations/download/csv:
    get:
      consumes:
      - application/json
      description: Фильтры находятся в теле запроса
      parameters:
      - in: query
        name: account_bank_id
        type: string
      - in: query
        name: account_number
        type: string
      - in: query
        name: balance_owner_bin
        type: string
      - in: query
        name: balance_owner_id
        type: string
      - in: query
        name: created_at_from
        type: string
      - in: query
        name: created_at_to
        type: string
      - in: query
        name: language
        required: true
        type: string
      - in: query
        name: merchant_id
        type: string
      - in: query
        name: operation_amount_from
        type: number
      - in: query
        name: operation_amount_to
        type: number
      - in: query
        name: operation_id
        type: integer
      - in: query
        name: operation_type_id
        type: integer
      - in: query
        name: transfer_id
        type: integer
      - description: per_page
        in: query
        name: per_page
        type: integer
      - description: page
        in: query
        name: page
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/middlewares.Response-string'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/middlewares.Response-middlewares_Empty'
      security:
      - bearerAuth: []
      summary: Выгрузка списка финансовых операций по фильтрам в формате csv
      tags:
      - billing-finance-operations
  /api/v1/billing/finance/operations/download/xlsx:
    get:
      consumes:
      - application/json
      description: Фильтры находятся в теле запроса
      parameters:
      - in: query
        name: account_bank_id
        type: string
      - in: query
        name: account_number
        type: string
      - in: query
        name: balance_owner_bin
        type: string
      - in: query
        name: balance_owner_id
        type: string
      - in: query
        name: created_at_from
        type: string
      - in: query
        name: created_at_to
        type: string
      - in: query
        name: language
        required: true
        type: string
      - in: query
        name: merchant_id
        type: string
      - in: query
        name: operation_amount_from
        type: number
      - in: query
        name: operation_amount_to
        type: number
      - in: query
        name: operation_id
        type: integer
      - in: query
        name: operation_type_id
        type: integer
      - in: query
        name: transfer_id
        type: integer
      - description: per_page
        in: query
        name: per_page
        type: integer
      - description: page
        in: query
        name: page
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/middlewares.Response-string'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/middlewares.Response-middlewares_Empty'
      security:
      - bearerAuth: []
      summary: Выгрузка списка финансовых операций по фильтрам в формате xlsx
      tags:
      - billing-finance-operations
  /api/v1/billing/overdraft/operations:
    get:
      description: |-
        Отображение списка данных по овердрафту
        Достает список данных по овердрафту
      parameters:
      - in: query
        name: account_bank_id
        type: string
      - in: query
        name: account_number_id
        type: string
      - in: query
        name: balance_owner_bin
        type: string
      - in: query
        name: balance_owner_earn_type_id
        type: string
      - in: query
        name: balance_owner_id
        type: string
      - in: query
        name: balance_owner_status_id
        type: string
      - in: query
        name: merchant_id
        type: string
      - description: per_page
        in: query
        name: per_page
        type: integer
      - description: page
        in: query
        name: page
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/middlewares.Response-array_schema_OverdraftOperationResponse'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/middlewares.Response-middlewares_Empty'
      security:
      - bearerAuth: []
      summary: Получение списка данных по овердрафту
      tags:
      - billing-overdraft-operations
  /api/v1/billing/overdraft/operations/customize:
    get:
      description: Используется для получения полей которые нужно отобразить для пользователя
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/middlewares.Response-model_Field'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/middlewares.Response-middlewares_Empty'
      security:
      - bearerAuth: []
      summary: Получение сохранённых полей в списках овердрафта
      tags:
      - billing-overdraft-operations
    put:
      description: Используется для обновление полей которые юзер бы хотел видеть
        у себя
      parameters:
      - description: Field Data
        in: body
        name: data
        required: true
        schema:
          $ref: '#/definitions/delivery_http_v1.createOrUpdateUserFields.request'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/middlewares.Response-string'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/middlewares.Response-middlewares_Empty'
      security:
      - bearerAuth: []
      summary: Обновление полей пользователя по списку овердрафта
      tags:
      - billing-overdraft-operations
  /api/v1/billing/overdraft/operations/download/csv:
    get:
      consumes:
      - application/json
      description: Фильтры находятся в теле запроса
      parameters:
      - in: query
        name: account_bank_id
        type: string
      - in: query
        name: account_number_id
        type: string
      - in: query
        name: balance_owner_bin
        type: string
      - in: query
        name: balance_owner_earn_type_id
        type: string
      - in: query
        name: balance_owner_id
        type: string
      - in: query
        name: balance_owner_status_id
        type: string
      - in: query
        name: language
        required: true
        type: string
      - in: query
        name: merchant_id
        type: string
      - description: per_page
        in: query
        name: per_page
        type: integer
      - description: page
        in: query
        name: page
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/middlewares.Response-string'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/middlewares.Response-middlewares_Empty'
      security:
      - bearerAuth: []
      summary: Выгрузка списка данных по овердрафту
      tags:
      - billing-overdraft-operations
  /api/v1/billing/overdraft/operations/download/xlsx:
    get:
      consumes:
      - application/json
      description: Фильтры находятся в теле запроса
      parameters:
      - in: query
        name: account_bank_id
        type: string
      - in: query
        name: account_number_id
        type: string
      - in: query
        name: balance_owner_bin
        type: string
      - in: query
        name: balance_owner_earn_type_id
        type: string
      - in: query
        name: balance_owner_id
        type: string
      - in: query
        name: balance_owner_status_id
        type: string
      - in: query
        name: language
        required: true
        type: string
      - in: query
        name: merchant_id
        type: string
      - description: per_page
        in: query
        name: per_page
        type: integer
      - description: page
        in: query
        name: page
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/middlewares.Response-string'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/middlewares.Response-middlewares_Empty'
      security:
      - bearerAuth: []
      summary: Выгрузка списка данных по овердрафту
      tags:
      - billing-overdraft-operations
  /api/v1/payment/orders:
    get:
      description: Get Payment Orders
      parameters:
      - in: query
        name: account_bank_id
        type: string
      - in: query
        name: account_number
        type: string
      - in: query
        name: balance_owner_bin
        type: string
      - in: query
        name: balance_owner_id
        type: string
      - in: query
        name: payment_order_date_from
        type: string
      - in: query
        name: payment_order_date_to
        type: string
      - in: query
        name: payment_order_end_date_from
        type: string
      - in: query
        name: payment_order_end_date_to
        type: string
      - in: query
        name: payment_order_id
        type: integer
      - in: query
        name: payment_order_status_id
        type: string
      - description: per_page
        in: query
        name: per_page
        type: integer
      - description: page
        in: query
        name: page
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/middlewares.Response-array_schema_PaymentOrderResponse'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/middlewares.Response-middlewares_Empty'
      security:
      - bearerAuth: []
      summary: Get Payment Orders
      tags:
      - payment-orders
  /api/v1/payment/orders/customize:
    get:
      description: Используется для получения полей которые нужно отобразить для пользователя
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/middlewares.Response-model_Field'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/middlewares.Response-middlewares_Empty'
      security:
      - bearerAuth: []
      summary: Получение сохранённых полей
      tags:
      - payment-orders
    put:
      description: Используется для обновление полей которые юзер бы хотел видеть
        у себя
      parameters:
      - description: Field Data
        in: body
        name: data
        required: true
        schema:
          $ref: '#/definitions/delivery_http_v1.createOrUpdatePaymentOrdersCustomization.request'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/middlewares.Response-string'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/middlewares.Response-middlewares_Empty'
      security:
      - bearerAuth: []
      summary: Обновление полей пользователя
      tags:
      - payment-orders
  /api/v1/payment/orders/download/csv:
    get:
      description: download payment orders report in csv format
      parameters:
      - in: query
        name: account_bank_id
        type: string
      - in: query
        name: account_number
        type: string
      - in: query
        name: balance_owner_bin
        type: string
      - in: query
        name: balance_owner_id
        type: string
      - in: query
        name: language
        type: string
      - in: query
        name: payment_order_date_from
        type: string
      - in: query
        name: payment_order_date_to
        type: string
      - in: query
        name: payment_order_end_date_from
        type: string
      - in: query
        name: payment_order_end_date_to
        type: string
      - in: query
        name: payment_order_id
        type: integer
      - in: query
        name: payment_order_status_id
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/middlewares.Response-string'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/middlewares.Response-middlewares_Empty'
      security:
      - bearerAuth: []
      summary: download payment orders report in csv format
      tags:
      - payment-orders
  /api/v1/payment/orders/download/xlsx:
    get:
      description: download payment orders report in xlsx format
      parameters:
      - in: query
        name: account_bank_id
        type: string
      - in: query
        name: account_number
        type: string
      - in: query
        name: balance_owner_bin
        type: string
      - in: query
        name: balance_owner_id
        type: string
      - in: query
        name: language
        type: string
      - in: query
        name: payment_order_date_from
        type: string
      - in: query
        name: payment_order_date_to
        type: string
      - in: query
        name: payment_order_end_date_from
        type: string
      - in: query
        name: payment_order_end_date_to
        type: string
      - in: query
        name: payment_order_id
        type: integer
      - in: query
        name: payment_order_status_id
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/middlewares.Response-string'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/middlewares.Response-middlewares_Empty'
      security:
      - bearerAuth: []
      summary: download payment orders report in xlsx format
      tags:
      - payment-orders
  /api/v1/project/lower/options/with-commissions:
    get:
      consumes:
      - application/json
      description: Запрос на получение списка нижних опций проекта с актульной комиссией
        на текущий момент
      parameters:
      - description: per_page
        in: query
        name: per_page
        type: integer
      - description: page
        in: query
        name: page
        type: integer
      - in: query
        minLength: 1
        name: acquirer_id
        type: string
      - in: query
        minLength: 1
        name: aggregated_type_id
        type: string
      - in: query
        name: country_id
        type: string
      - in: query
        name: ips_id
        type: string
      - in: query
        name: issuer_id
        type: string
      - in: query
        name: last_transaction_date_from
        type: string
      - in: query
        name: last_transaction_date_to
        type: string
      - in: query
        minLength: 1
        name: merchant_id
        type: string
      - in: query
        minLength: 1
        name: project_id
        type: string
      - in: query
        name: project_lower_option_type
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          headers:
            x-page:
              description: "1"
              type: int
            x-per-page:
              description: "1"
              type: int
            x-total:
              description: "11"
              type: int
          schema:
            $ref: '#/definitions/middlewares.Response-array_model_ProjectLowerCommission'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/middlewares.Response-middlewares_Empty'
      security:
      - bearerAuth: []
      summary: Получение списка нижних опций проекта с актуальной комиссией
      tags:
      - commissions
  /api/v1/project/lower/options/with-commissions/download/csv:
    get:
      consumes:
      - application/json
      description: Запрос на получение списка нижних опций проекта с актульной комиссией
        на текущий момент
      parameters:
      - description: per_page
        in: query
        name: per_page
        type: integer
      - description: page
        in: query
        name: page
        type: integer
      - in: query
        minLength: 1
        name: acquirer_id
        type: string
      - in: query
        minLength: 1
        name: aggregated_type_id
        type: string
      - in: query
        name: country_id
        type: string
      - in: query
        name: ips_id
        type: string
      - in: query
        name: issuer_id
        type: string
      - in: query
        name: language
        required: true
        type: string
      - in: query
        name: last_transaction_date_from
        type: string
      - in: query
        name: last_transaction_date_to
        type: string
      - in: query
        minLength: 1
        name: merchant_id
        type: string
      - in: query
        minLength: 1
        name: project_id
        type: string
      - in: query
        name: project_lower_option_type
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/middlewares.Response-string'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/middlewares.Response-middlewares_Empty'
      security:
      - bearerAuth: []
      summary: Выгрузка списка нижних опций проекта с актуальной комиссией в формате
        csv
      tags:
      - commissions
  /api/v1/project/lower/options/with-commissions/download/xlsx:
    get:
      consumes:
      - application/json
      description: Запрос на получение списка нижних опций проекта с актульной комиссией
        на текущий момент
      parameters:
      - description: per_page
        in: query
        name: per_page
        type: integer
      - description: page
        in: query
        name: page
        type: integer
      - in: query
        minLength: 1
        name: acquirer_id
        type: string
      - in: query
        minLength: 1
        name: aggregated_type_id
        type: string
      - in: query
        name: country_id
        type: string
      - in: query
        name: ips_id
        type: string
      - in: query
        name: issuer_id
        type: string
      - in: query
        name: language
        required: true
        type: string
      - in: query
        name: last_transaction_date_from
        type: string
      - in: query
        name: last_transaction_date_to
        type: string
      - in: query
        minLength: 1
        name: merchant_id
        type: string
      - in: query
        minLength: 1
        name: project_id
        type: string
      - in: query
        name: project_lower_option_type
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/middlewares.Response-string'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/middlewares.Response-middlewares_Empty'
      security:
      - bearerAuth: []
      summary: Выгрузка списка нижних опций проекта с актуальной комиссией в формате
        xlsx
      tags:
      - commissions
  /api/v1/project/upper/options/with-commissions:
    get:
      consumes:
      - application/json
      description: Запрос на получение списка верхних опций проекта с актульной комиссией
        на текущий момент
      parameters:
      - description: per_page
        in: query
        name: per_page
        type: integer
      - description: page
        in: query
        name: page
        type: integer
      - in: query
        minLength: 1
        name: acquirer_id
        type: string
      - in: query
        minLength: 1
        name: aggregated_type_id
        type: string
      - in: query
        name: last_transaction_date_from
        type: string
      - in: query
        name: last_transaction_date_to
        type: string
      - in: query
        minLength: 1
        name: merchant_id
        type: string
      - in: query
        minLength: 1
        name: project_id
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          headers:
            x-page:
              description: "1"
              type: int
            x-per-page:
              description: "1"
              type: int
            x-total:
              description: "11"
              type: int
          schema:
            $ref: '#/definitions/middlewares.Response-array_model_ProjectUpperCommission'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/middlewares.Response-middlewares_Empty'
      security:
      - bearerAuth: []
      summary: Получение списка верхних опций проекта с актуальной комиссией
      tags:
      - commissions
  /api/v1/project/upper/options/with-commissions/download/csv:
    get:
      consumes:
      - application/json
      description: Запрос на получение списка верхних опций проекта с актульной комиссией
        на текущий момент
      parameters:
      - description: per_page
        in: query
        name: per_page
        type: integer
      - description: page
        in: query
        name: page
        type: integer
      - in: query
        minLength: 1
        name: acquirer_id
        type: string
      - in: query
        minLength: 1
        name: aggregated_type_id
        type: string
      - in: query
        name: language
        required: true
        type: string
      - in: query
        name: last_transaction_date_from
        type: string
      - in: query
        name: last_transaction_date_to
        type: string
      - in: query
        minLength: 1
        name: merchant_id
        type: string
      - in: query
        minLength: 1
        name: project_id
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/middlewares.Response-array_model_ProjectUpperCommission'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/middlewares.Response-string'
      security:
      - bearerAuth: []
      summary: Выгрузка списка верхних опций проекта с актуальной комиссией в формате
        csv
      tags:
      - commissions
  /api/v1/project/upper/options/with-commissions/download/xlsx:
    get:
      consumes:
      - application/json
      description: Запрос на получение списка верхних опций проекта с актульной комиссией
        на текущий момент
      parameters:
      - description: per_page
        in: query
        name: per_page
        type: integer
      - description: page
        in: query
        name: page
        type: integer
      - in: query
        minLength: 1
        name: acquirer_id
        type: string
      - in: query
        minLength: 1
        name: aggregated_type_id
        type: string
      - in: query
        name: language
        required: true
        type: string
      - in: query
        name: last_transaction_date_from
        type: string
      - in: query
        name: last_transaction_date_to
        type: string
      - in: query
        minLength: 1
        name: merchant_id
        type: string
      - in: query
        minLength: 1
        name: project_id
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/middlewares.Response-array_model_ProjectUpperCommission'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/middlewares.Response-string'
      security:
      - bearerAuth: []
      summary: Выгрузка списка верхних опций проекта с актуальной комиссией в формате
        xlsx
      tags:
      - commissions
  /api/v1/rules:
    get:
      description: Возвращает список с пагинацией и фильтрацией
      parameters:
      - in: query
        name: is_base
        type: string
      - in: query
        name: project_id
        type: string
      - in: query
        name: transaction_type_id
        type: string
      - default: DESC
        in: query
        name: weight_sort_by
        type: string
      - description: per_page
        in: query
        name: per_page
        type: integer
      - description: page
        in: query
        name: page
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/middlewares.Response-array_schema_RuleResponse'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/middlewares.Response-middlewares_Empty'
      security:
      - bearerAuth: []
      summary: Получение списка правил с эквайерами
      tags:
      - acquirer-rules
  /api/v1/rules/{rule_id}/show:
    get:
      parameters:
      - description: Rule ID
        in: path
        name: rule_id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/middlewares.Response-schema_RuleResponse'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/middlewares.Response-middlewares_Empty'
      security:
      - bearerAuth: []
      summary: Получение правила по id
      tags:
      - acquirer-rules
  /api/v1/rules/customize:
    get:
      description: Используется для получения полей которые нужно отобразить для пользователя
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/middlewares.Response-model_Field'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/middlewares.Response-middlewares_Empty'
      security:
      - bearerAuth: []
      summary: Получение сохранённых полей по списку правил экваеров
      tags:
      - acquirer-rules
    put:
      description: Используется для обновление полей которые юзер бы хотел видеть
        у себя
      parameters:
      - description: Field Data
        in: body
        name: data
        required: true
        schema:
          $ref: '#/definitions/delivery_http_v1.createOrUpdateUserFields.request'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/middlewares.Response-string'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/middlewares.Response-middlewares_Empty'
      security:
      - bearerAuth: []
      summary: Обновление полей пользователя по списку правил экваеров
      tags:
      - acquirer-rules
  /api/v1/splitting/settings:
    get:
      description: Отображение сплиттинга платежа по фильтру на фронте
      parameters:
      - in: query
        name: merchant_id
        type: string
      - in: query
        name: payer_project_id
        type: string
      - in: query
        name: recipient_project_id
        type: string
      - description: per_page
        in: query
        name: per_page
        type: integer
      - description: page
        in: query
        name: page
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/middlewares.Response-array_delivery_http_v1_splittingSettingsResponse'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/middlewares.Response-middlewares_Empty'
      security:
      - bearerAuth: []
      summary: Получение настроек сплиттинга платежа
      tags:
      - splitting-settings
  /api/v1/splitting/settings/customize:
    get:
      description: Используется для получения полей которые нужно отобразить для пользователя
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/middlewares.Response-model_Field'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/middlewares.Response-middlewares_Empty'
      security:
      - bearerAuth: []
      summary: Получение сохранённых полей настроек сплиттинга платежа
      tags:
      - splitting-settings
    put:
      description: Используется для обновление полей которые юзер бы хотел видеть
        у себя
      parameters:
      - description: Field Data
        in: body
        name: data
        required: true
        schema:
          $ref: '#/definitions/delivery_http_v1.createOrUpdateSplittingSettingCustomization.request'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/middlewares.Response-string'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/middlewares.Response-middlewares_Empty'
      security:
      - bearerAuth: []
      summary: Обновление полей пользователя по настрокам сплиттинга платежа
      tags:
      - splitting-settings
  /api/v1/splitting/transfers/{transfer_id}/row-operations:
    get:
      description: |-
        Используется на фронте на странице детализаций операций.
        Берет данные из view transfer_row_operations
      parameters:
      - description: TransferID
        in: path
        name: transfer_id
        required: true
        type: integer
      - description: per_page
        in: query
        name: per_page
        type: integer
      - description: page
        in: query
        name: page
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/middlewares.Response-array_delivery_http_v1_getTransferDetails_response'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/middlewares.Response-middlewares_Empty'
      security:
      - bearerAuth: []
      summary: Получение списка финансовых операций из ресурса splitting
      tags:
      - splitting-finance-operations
  /api/v1/terminals:
    get:
      consumes:
      - application/json
      description: Фильтры находятся в теле запроса
      parameters:
      - in: query
        name: acquirer_name
        type: string
      - in: query
        name: acquirer_terminal_name
        type: string
      - in: query
        name: project_name
        type: string
      - in: query
        name: status
        type: integer
      - in: query
        name: terminal_tarlan_id
        type: integer
      - in: query
        name: transaction_type_name
        type: string
      - in: query
        name: transit
        type: boolean
      - description: per_page
        in: query
        name: per_page
        type: integer
      - description: page
        in: query
        name: page
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          headers:
            x-page:
              description: "1"
              type: int
            x-per-page:
              description: "1"
              type: int
            x-total:
              description: "11"
              type: int
          schema:
            $ref: '#/definitions/middlewares.Response-array_model_Terminal'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/middlewares.Response-middlewares_Empty'
      security:
      - bearerAuth: []
      summary: Получение списка терминалов по фильтрам
      tags:
      - terminals
  /api/v1/transactions:
    get:
      consumes:
      - application/json
      description: Фильтры находятся в теле запроса
      parameters:
      - in: query
        name: acquirer_ids
        type: string
      - in: query
        name: amount
        type: number
      - description: date,date
        in: query
        name: created_at_from
        type: string
      - in: query
        name: created_at_to
        type: string
      - in: query
        name: finished_at_from
        type: string
      - in: query
        name: finished_at_to
        type: string
      - in: query
        name: issuer_ids
        type: string
      - in: query
        name: last_refund_date_from
        type: string
      - in: query
        name: last_refund_date_to
        type: string
      - in: query
        name: masked_pan
        type: string
      - in: query
        name: merchant_id
        type: integer
      - in: query
        name: project_client_id
        type: string
      - in: query
        name: project_ids
        type: string
      - in: query
        name: project_reference_id
        type: string
      - in: query
        name: rrn
        type: string
      - in: query
        name: sort_by
        type: string
      - in: query
        name: transaction_ids
        type: string
      - in: query
        name: transaction_status_ids
        type: string
      - in: query
        name: transaction_type_ids
        type: string
      - description: per_page
        in: query
        name: per_page
        type: integer
      - description: page
        in: query
        name: page
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          headers:
            x-page:
              description: "1"
              type: int
            x-per-page:
              description: "1"
              type: int
            x-total:
              description: "11"
              type: int
          schema:
            $ref: '#/definitions/middlewares.Response-model_Transactions'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/middlewares.Response-middlewares_Empty'
      security:
      - bearerAuth: []
      summary: Получение списка транзакций по фильтрам
      tags:
      - orders
  /api/v1/transactions/aggregated-info:
    get:
      description: Сумма и кол-во высчитываются с учетом фильтров
      parameters:
      - collectionFormat: csv
        in: query
        items:
          type: string
        name: acquirer_ids
        type: array
      - in: query
        name: amount
        type: number
      - description: date,date
        in: query
        name: created_at_from
        type: string
      - in: query
        name: created_at_to
        type: string
      - in: query
        name: finished_at_from
        type: string
      - in: query
        name: finished_at_to
        type: string
      - collectionFormat: csv
        in: query
        items:
          type: string
        name: issuer_ids
        type: array
      - in: query
        name: last_refund_date_from
        type: string
      - in: query
        name: last_refund_date_to
        type: string
      - in: query
        name: masked_pan
        type: string
      - in: query
        name: merchant_id
        type: integer
      - in: query
        name: project_client_id
        type: string
      - collectionFormat: csv
        in: query
        items:
          type: string
        name: project_ids
        type: array
      - in: query
        name: project_reference_id
        type: string
      - in: query
        name: rrn
        type: string
      - in: query
        name: sort_by
        type: string
      - collectionFormat: csv
        in: query
        items:
          type: string
        name: transaction_ids
        type: array
      - collectionFormat: csv
        in: query
        items:
          type: string
        name: transaction_status_ids
        type: array
      - collectionFormat: csv
        in: query
        items:
          type: string
        name: transaction_type_ids
        type: array
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/middlewares.Response-schema_AggregatedInfoResponse'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/middlewares.Response-middlewares_Empty'
      security:
      - bearerAuth: []
      summary: Получение суммы и кол-ва транзакций
      tags:
      - orders
  /api/v1/transactions/download/csv:
    get:
      consumes:
      - application/json
      description: Фильтры находятся в теле запроса
      parameters:
      - in: query
        name: acquirer_ids
        type: string
      - in: query
        name: amount
        type: number
      - description: date,date
        in: query
        name: created_at_from
        type: string
      - in: query
        name: created_at_to
        type: string
      - in: query
        name: finished_at_from
        type: string
      - in: query
        name: finished_at_to
        type: string
      - in: query
        name: issuer_ids
        type: string
      - in: query
        name: language
        required: true
        type: string
      - in: query
        name: last_refund_date_from
        type: string
      - in: query
        name: last_refund_date_to
        type: string
      - in: query
        name: masked_pan
        type: string
      - in: query
        name: merchant_id
        type: integer
      - in: query
        name: project_client_id
        type: string
      - in: query
        name: project_ids
        type: string
      - in: query
        name: project_reference_id
        type: string
      - in: query
        name: rrn
        type: string
      - in: query
        name: sort_by
        type: string
      - in: query
        name: transaction_ids
        type: string
      - in: query
        name: transaction_status_ids
        type: string
      - in: query
        name: transaction_type_ids
        type: string
      - description: per_page
        in: query
        name: per_page
        type: integer
      - description: page
        in: query
        name: page
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/middlewares.Response-string'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/middlewares.Response-middlewares_Empty'
      security:
      - bearerAuth: []
      summary: Выгрузка списка транзакций по фильтрам в формате csv
      tags:
      - orders
  /api/v1/transactions/download/xlsx:
    get:
      consumes:
      - application/json
      description: Фильтры находятся в теле запроса
      parameters:
      - in: query
        name: acquirer_ids
        type: string
      - in: query
        name: amount
        type: number
      - description: date,date
        in: query
        name: created_at_from
        type: string
      - in: query
        name: created_at_to
        type: string
      - in: query
        name: finished_at_from
        type: string
      - in: query
        name: finished_at_to
        type: string
      - in: query
        name: issuer_ids
        type: string
      - in: query
        name: language
        required: true
        type: string
      - in: query
        name: last_refund_date_from
        type: string
      - in: query
        name: last_refund_date_to
        type: string
      - in: query
        name: masked_pan
        type: string
      - in: query
        name: merchant_id
        type: integer
      - in: query
        name: project_client_id
        type: string
      - in: query
        name: project_ids
        type: string
      - in: query
        name: project_reference_id
        type: string
      - in: query
        name: rrn
        type: string
      - in: query
        name: sort_by
        type: string
      - in: query
        name: transaction_ids
        type: string
      - in: query
        name: transaction_status_ids
        type: string
      - in: query
        name: transaction_type_ids
        type: string
      - description: per_page
        in: query
        name: per_page
        type: integer
      - description: page
        in: query
        name: page
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/middlewares.Response-string'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/middlewares.Response-middlewares_Empty'
      security:
      - bearerAuth: []
      summary: Выгрузка списка транзакций по фильтрам в формате xlsx
      tags:
      - orders
  /api/v1/transfer/rules:
    get:
      description: |-
        Отображение трансфер с правилами по фильтру на фронте
        Достает данные с view transfer_rule
      parameters:
      - in: query
        name: account_bank_id
        type: string
      - in: query
        name: account_number
        type: string
      - in: query
        name: balance_owner_bin
        type: string
      - in: query
        name: balance_owner_id
        type: string
      - in: query
        name: is_active
        type: boolean
      - description: per_page
        in: query
        name: per_page
        type: integer
      - description: page
        in: query
        name: page
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/middlewares.Response-array_schema_TransferRuleResponse'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/middlewares.Response-middlewares_Empty'
      security:
      - bearerAuth: []
      summary: Получение списка трансферов по правилу
      tags:
      - transfer-rules
  /api/v1/transfers:
    get:
      description: |-
        Используется на фронте на странице перевод(трансфер).
        Берет данные из view Transfers
      parameters:
      - description: per_page
        in: query
        name: per_page
        type: integer
      - description: page
        in: query
        name: page
        type: integer
      - in: query
        name: account_bank_id
        type: string
      - in: query
        name: account_number
        type: string
      - in: query
        name: balance_owner_bin
        type: string
      - in: query
        name: balance_owner_id
        type: string
      - in: query
        name: created_at_from
        type: string
      - in: query
        name: created_at_to
        type: string
      - in: query
        name: transfer_amount_from
        type: number
      - in: query
        name: transfer_amount_to
        type: number
      - in: query
        name: transfer_id
        type: integer
      - in: query
        name: transfer_status_id
        type: string
      - in: query
        name: transfer_type_id
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/middlewares.Response-array_delivery_http_v1_getTransfers_data'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/middlewares.Response-middlewares_Empty'
      security:
      - bearerAuth: []
      summary: Получение списка трансферов
      tags:
      - transfers
  /api/v1/transfers/customize:
    get:
      description: Используется для получения полей которые нужно отобразить для пользователя
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/middlewares.Response-model_Field'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/middlewares.Response-middlewares_Empty'
      security:
      - bearerAuth: []
      summary: Получение сохранённых полей в трансферах
      tags:
      - transfers
    put:
      description: Используется для обновление полей которые юзер бы хотел видеть
        у себя
      parameters:
      - description: Field Data
        in: body
        name: data
        required: true
        schema:
          $ref: '#/definitions/delivery_http_v1.updateTransferCustomization.request'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/middlewares.Response-string'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/middlewares.Response-middlewares_Empty'
      security:
      - bearerAuth: []
      summary: Обновление полей пользователя по трансферам
      tags:
      - transfers
  /api/v1/transfers/download/csv:
    get:
      consumes:
      - application/json
      description: Фильтры находятся в теле запроса
      parameters:
      - in: query
        name: account_bank_id
        type: string
      - in: query
        name: account_number
        type: string
      - in: query
        name: balance_owner_bin
        type: string
      - in: query
        name: balance_owner_id
        type: string
      - in: query
        name: created_at_from
        type: string
      - in: query
        name: created_at_to
        type: string
      - in: query
        name: language
        required: true
        type: string
      - in: query
        name: transfer_amount_from
        type: number
      - in: query
        name: transfer_amount_to
        type: number
      - in: query
        name: transfer_id
        type: integer
      - in: query
        name: transfer_status_id
        type: string
      - in: query
        name: transfer_type_id
        type: string
      - description: per_page
        in: query
        name: per_page
        type: integer
      - description: page
        in: query
        name: page
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/middlewares.Response-string'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/middlewares.Response-middlewares_Empty'
      security:
      - bearerAuth: []
      summary: Выгрузка списка трансферов по фильтрам в формате csv
      tags:
      - transfers
  /api/v1/transfers/download/xlsx:
    get:
      consumes:
      - application/json
      description: Фильтры находятся в теле запроса
      parameters:
      - in: query
        name: account_bank_id
        type: string
      - in: query
        name: account_number
        type: string
      - in: query
        name: balance_owner_bin
        type: string
      - in: query
        name: balance_owner_id
        type: string
      - in: query
        name: created_at_from
        type: string
      - in: query
        name: created_at_to
        type: string
      - in: query
        name: language
        required: true
        type: string
      - in: query
        name: transfer_amount_from
        type: number
      - in: query
        name: transfer_amount_to
        type: number
      - in: query
        name: transfer_id
        type: integer
      - in: query
        name: transfer_status_id
        type: string
      - in: query
        name: transfer_type_id
        type: string
      - description: per_page
        in: query
        name: per_page
        type: integer
      - description: page
        in: query
        name: page
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/middlewares.Response-string'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/middlewares.Response-middlewares_Empty'
      security:
      - bearerAuth: []
      summary: Выгрузка списка трансферов по фильтрам в формате xlsx
      tags:
      - transfers
  /api/v1/transfers/rule/customize:
    get:
      description: Используется для получения полей которые нужно отобразить для пользователя
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/middlewares.Response-model_Field'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/middlewares.Response-middlewares_Empty'
      security:
      - bearerAuth: []
      summary: Получение сохранённых полей в правилах трансферов
      tags:
      - transfer-rules
    put:
      description: Используется для обновление полей которые юзер бы хотел видеть
        у себя
      parameters:
      - description: Field Data
        in: body
        name: data
        required: true
        schema:
          $ref: '#/definitions/delivery_http_v1.updateTransferCustomization.request'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/middlewares.Response-string'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/middlewares.Response-middlewares_Empty'
      security:
      - bearerAuth: []
      summary: Обновление полей пользователя по трансферам
      tags:
      - transfer-rules
schemes:
- https
- http
securityDefinitions:
  bearerAuth:
    in: header
    name: Authorization
    type: apiKey
swagger: "2.0"
