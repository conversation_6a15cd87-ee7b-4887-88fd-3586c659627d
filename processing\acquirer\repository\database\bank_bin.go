package database

import (
	"context"
	"errors"

	"gorm.io/gorm"

	"git.local/sensitive/innerpb/processing/goerr"
	"git.local/sensitive/pkg/middlewares"
	"git.local/sensitive/processing/acquirer/model"
	"git.local/sensitive/sdk/dog"
)

type BankBinDB struct {
	db *gorm.DB
}

func NewBankBinDB(db *gorm.DB) BankBiner {
	return &BankBinDB{
		db: db,
	}
}

func (bb *BankBinDB) Create(ctx context.Context, bankBin model.BankBin) (err error) {
	ctx, span := dog.CreateSpan(ctx, "BankBinDB_Create")
	defer span.End()

	if err := bb.db.WithContext(ctx).
		Create(&bankBin).
		Error; err != nil {
		return goerr.ErrDbUnexpected.WithErr(err).WithCtx(ctx)
	}

	return nil
}

func (bb *BankBinDB) GetAll(
	ctx context.Context,
	pagination *middlewares.PaginationInfo,
) (_ []*model.BankBin, err error) {
	ctx, span := dog.CreateSpan(ctx, "BankBinDB_GetAll")
	defer span.End()

	result := make([]*model.BankBin, 0)

	request := bb.db.WithContext(ctx).
		Model(&model.BankBin{})

	if pagination.Pagination {
		request = request.Offset((pagination.Page - 1) * pagination.PerPage).Limit(pagination.PerPage)
	}

	if err := request.Find(&result).Error; err != nil {
		return nil, goerr.ErrDbUnexpected.WithErr(err).WithCtx(ctx)
	}

	return result, nil
}

func (bb *BankBinDB) Update(ctx context.Context, id uint64, data model.BankBin) (err error) {
	ctx, span := dog.CreateSpan(ctx, "BankBinDB_Update")
	defer span.End()

	err = bb.db.WithContext(ctx).
		Where("id = ?", id).
		Updates(data).
		First(&model.BankBin{}).
		Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return goerr.ErrBankBinNotFound.WithCtx(ctx)
		}

		return goerr.ErrDbUnexpected.WithErr(err).WithCtx(ctx)
	}

	return nil
}

func (bb *BankBinDB) Delete(ctx context.Context, id uint64) (err error) {
	ctx, span := dog.CreateSpan(ctx, "BankBinDB_Delete")
	defer span.End()

	err = bb.db.WithContext(ctx).
		Where("id = ?", id).
		First(&model.BankBin{}).
		Delete(&model.BankBin{}, id).
		Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return goerr.ErrBankBinNotFound.WithCtx(ctx)
		}

		return goerr.ErrDbUnexpected.WithErr(err).WithCtx(ctx)
	}

	return nil
}

func (bb *BankBinDB) GetByBin(
	ctx context.Context,
	eightDigitBin, sixDigitBin, fiveDigitBin string,
) (_ *model.BankBin, err error) {
	ctx, span := dog.CreateSpan(ctx, "BankBinDB_GetByBin")
	defer span.End()

	bankBin := new(model.BankBin)

	if err := bb.db.WithContext(ctx).Where("bin = ?", eightDigitBin).Find(bankBin).Error; err != nil {
		return nil, err
	}

	if bankBin.ID != 0 {
		return bankBin, nil
	}

	if err := bb.db.WithContext(ctx).Where("bin = ?", sixDigitBin).Find(bankBin).Error; err != nil {
		return nil, err
	}

	if bankBin.ID != 0 {
		return bankBin, nil
	}

	if err := bb.db.WithContext(ctx).Where("bin = ?", fiveDigitBin).Find(bankBin).Error; err != nil {
		return nil, err
	}

	if bankBin.ID != 0 {
		return bankBin, nil
	}

	return &model.BankBin{}, nil
}
