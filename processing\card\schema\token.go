package schema

import (
	"context"
	"encoding/json"
	"fmt"
	"time"

	"git.local/sensitive/innerpb/processing/goerr"

	"golang.org/x/sync/errgroup"

	gorpc "git.local/sensitive/innerpb/processing/grpc"
	"git.local/sensitive/processing/card/model"
	"git.local/sensitive/sdk/dog"
)

type CardTokenRequest struct {
	ProjectId       uint64 `json:"project_id" validate:"required"`
	ProjectClientId string `json:"project_client_id" validate:"required"`
	CardId          uint64 `json:"card_id" validate:"required"`
}

type EncryptedCardToken struct {
	CardToken  string
	MaskedPan  string
	IpsName    string
	IssuerName string
	Year       string
	Month      string
}

func NewEncryptedCardToken(
	ctx context.Context,
	card model.Card,
	ips, issuer, key string,
	maskFormat *model.ProjectMaskFormat,
) (*EncryptedCardToken, error) {
	var decryptedPan, encryptedKey, decryptYear, decryptMonth string

	encryptKey, err := dog.AESDecrypt(key, dog.ConfigString("SYMMETRIC_KEY"))
	if err != nil {
		return nil, err
	}

	jsonKey, err := json.Marshal(CardInfoForView{
		CardID: card.Id,
		Key:    card.HashedPan,
	})
	if err != nil {
		return nil, err
	}

	errsGroup, _ := errgroup.WithContext(ctx)

	errsGroup.Go(func() error {
		decryptedPan, err = dog.AESDecrypt(card.EncryptPan, encryptKey)
		if err != nil {
			return fmt.Errorf("error while decrypting pan: %s", err)
		}

		return nil
	})

	if card.EncryptYear != "" {
		errsGroup.Go(func() error {
			decryptYear, err = dog.AESDecrypt(card.EncryptYear, encryptKey)
			if err != nil {
				return fmt.Errorf("error while decrypting year: %s", err)
			}

			return nil
		})
	}

	if card.EncryptMonth != "" {
		errsGroup.Go(func() error {
			decryptMonth, err = dog.AESDecrypt(card.EncryptMonth, encryptKey)
			if err != nil {
				return fmt.Errorf("error while decrypting month: %s", err)
			}

			return nil
		})
	}

	errsGroup.Go(func() error {
		encryptedKey, err = dog.AESEncrypt(string(jsonKey), dog.ConfigString("SYMMETRIC_KEY"))
		if err != nil {
			return fmt.Errorf("error while encrypting key: %s", err)
		}

		return nil
	})

	err = errsGroup.Wait()
	if err != nil {
		return nil, err
	}

	maskedPan, err := maskFormat.GetMaskedCard(decryptedPan)
	if err != nil {
		return nil, err
	}

	return &EncryptedCardToken{
		CardToken:  encryptedKey,
		MaskedPan:  maskedPan,
		IpsName:    ips,
		IssuerName: issuer,
		Year:       decryptYear,
		Month:      decryptMonth,
	}, nil
}

func NewCardTokenFromRaw(v1 *gorpc.GetCardTokensRequestV1) *CardTokenRequest {
	return &CardTokenRequest{
		ProjectId:       v1.GetProjectId(),
		ProjectClientId: v1.GetProjectClientId(),
		CardId:          v1.GetCardId(),
	}
}

func NewEncryptedCardTokenResponse(cards []*EncryptedCardToken) (*gorpc.GetOneClickPayInCardsResponseV1, error) {
	responseCards := make([]*gorpc.GetEncryptedCardResponseV1, len(cards))

	for i, c := range cards {
		card := &gorpc.GetEncryptedCardResponseV1{
			CardToken: &c.CardToken,
			MaskedPan: &c.MaskedPan,
			Ips:       &c.IpsName,
			Issuer:    &c.IssuerName,
			Year:      &c.Year,
			Month:     &c.Month,
		}

		responseCards[i] = card
	}

	return &gorpc.GetOneClickPayInCardsResponseV1{Cards: responseCards}, nil
}

func CardTokenResponse(card *model.Card) *gorpc.GetCardTokensResponseV1 {
	responseTokens := make([]*gorpc.CardTokenV1, len(card.Tokens))

	for i, t := range card.Tokens {
		token := &gorpc.CardTokenV1{
			Id:         &t.Id,
			AcquirerId: &t.AcquirerId,
			Token:      &t.Token,
			TerminalId: &t.TerminalId,
		}
		responseTokens[i] = token
	}

	return &gorpc.GetCardTokensResponseV1{
		Card: &gorpc.ClientCard{
			Id:        &card.Id,
			MaskedPan: &card.MaskedPan,
			IpsId:     &card.IpsId,
			CountryId: &card.CountryId,
			IssuerId:  &card.IssuerId,
		},
		Tokens: responseTokens,
	}
}

type TokenRequest struct {
	CardId     uint64 `json:"card_id"`
	AcquirerId uint64 `json:"acquirer_id"`
	Token      string `json:"token"`
	TerminalId uint64 `json:"terminal_id"`
	ProjectId  uint64 `json:"project_id"`
}

type TokenResponse struct {
	CreatedAt  time.Time `json:"created_at"`
	UpdatedAt  time.Time `json:"updated_at"`
	DeletedAt  time.Time `json:"deleted_at"`
	ID         uint64    `json:"id"`
	AcquirerID uint64    `json:"acquirer_id"`
	TerminalID uint64    `json:"terminal_id"`
	CardID     uint64    `json:"card_id"`
	Token      string    `json:"token"`
}

// Validate validates the incoming token's information
func (ar *TokenRequest) Validate() error {
	if ar == nil {
		return goerr.ErrParseErrorBody
	}

	if ar.CardId == 0 {
		return goerr.ErrEmptyCardID
	}

	if ar.AcquirerId == 0 {
		return goerr.ErrEmptyAcquirerID
	}

	if ar.TerminalId == 0 {
		return goerr.ErrEmptyTerminalID
	}

	if ar.Token == "" {
		return goerr.ErrEmptyToken
	}

	return nil
}

func (ar *TokenRequest) ToModel() *model.Token {
	if ar == nil {
		return nil
	}

	return &model.Token{
		AcquirerId: ar.AcquirerId,
		TerminalId: ar.TerminalId,
		Token:      ar.Token,
		CardId:     ar.CardId,
	}
}

type DeactivateTokenRequest struct {
	CardToken  string `json:"card_token"`
	MerchantID uint64 `json:"merchant_id"`
	ProjectID  uint64 `json:"project_id"`
}
