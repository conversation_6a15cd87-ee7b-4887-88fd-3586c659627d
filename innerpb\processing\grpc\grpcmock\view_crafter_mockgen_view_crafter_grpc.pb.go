// Code generated by MockGen. DO NOT EDIT.
// Source: view_crafter_grpc.pb.go

// Package grpcmock is a generated GoMock package.
package grpcmock

import (
	context "context"
	reflect "reflect"

	grpc "git.local/sensitive/innerpb/processing/grpc"
	gomock "github.com/golang/mock/gomock"
	grpc0 "google.golang.org/grpc"
)

// MockViewCrafterClient is a mock of ViewCrafterClient interface.
type MockViewCrafterClient struct {
	ctrl     *gomock.Controller
	recorder *MockViewCrafterClientMockRecorder
}

// MockViewCrafterClientMockRecorder is the mock recorder for MockViewCrafterClient.
type MockViewCrafterClientMockRecorder struct {
	mock *MockViewCrafterClient
}

// NewMockViewCrafterClient creates a new mock instance.
func NewMockViewCrafterClient(ctrl *gomock.Controller) *MockViewCrafterClient {
	mock := &MockViewCrafterClient{ctrl: ctrl}
	mock.recorder = &MockViewCrafterClientMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockViewCrafterClient) EXPECT() *MockViewCrafterClientMockRecorder {
	return m.recorder
}

// GetProjectFormInfoV1 mocks base method.
func (m *MockViewCrafterClient) GetProjectFormInfoV1(ctx context.Context, in *grpc.GetProjectFormInfoReqV1, opts ...grpc0.CallOption) (*grpc.GetProjectFormInfoResV1, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetProjectFormInfoV1", varargs...)
	ret0, _ := ret[0].(*grpc.GetProjectFormInfoResV1)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetProjectFormInfoV1 indicates an expected call of GetProjectFormInfoV1.
func (mr *MockViewCrafterClientMockRecorder) GetProjectFormInfoV1(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetProjectFormInfoV1", reflect.TypeOf((*MockViewCrafterClient)(nil).GetProjectFormInfoV1), varargs...)
}

// MockViewCrafterServer is a mock of ViewCrafterServer interface.
type MockViewCrafterServer struct {
	ctrl     *gomock.Controller
	recorder *MockViewCrafterServerMockRecorder
}

// MockViewCrafterServerMockRecorder is the mock recorder for MockViewCrafterServer.
type MockViewCrafterServerMockRecorder struct {
	mock *MockViewCrafterServer
}

// NewMockViewCrafterServer creates a new mock instance.
func NewMockViewCrafterServer(ctrl *gomock.Controller) *MockViewCrafterServer {
	mock := &MockViewCrafterServer{ctrl: ctrl}
	mock.recorder = &MockViewCrafterServerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockViewCrafterServer) EXPECT() *MockViewCrafterServerMockRecorder {
	return m.recorder
}

// GetProjectFormInfoV1 mocks base method.
func (m *MockViewCrafterServer) GetProjectFormInfoV1(arg0 context.Context, arg1 *grpc.GetProjectFormInfoReqV1) (*grpc.GetProjectFormInfoResV1, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetProjectFormInfoV1", arg0, arg1)
	ret0, _ := ret[0].(*grpc.GetProjectFormInfoResV1)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetProjectFormInfoV1 indicates an expected call of GetProjectFormInfoV1.
func (mr *MockViewCrafterServerMockRecorder) GetProjectFormInfoV1(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetProjectFormInfoV1", reflect.TypeOf((*MockViewCrafterServer)(nil).GetProjectFormInfoV1), arg0, arg1)
}

// mustEmbedUnimplementedViewCrafterServer mocks base method.
func (m *MockViewCrafterServer) mustEmbedUnimplementedViewCrafterServer() {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "mustEmbedUnimplementedViewCrafterServer")
}

// mustEmbedUnimplementedViewCrafterServer indicates an expected call of mustEmbedUnimplementedViewCrafterServer.
func (mr *MockViewCrafterServerMockRecorder) mustEmbedUnimplementedViewCrafterServer() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "mustEmbedUnimplementedViewCrafterServer", reflect.TypeOf((*MockViewCrafterServer)(nil).mustEmbedUnimplementedViewCrafterServer))
}

// MockUnsafeViewCrafterServer is a mock of UnsafeViewCrafterServer interface.
type MockUnsafeViewCrafterServer struct {
	ctrl     *gomock.Controller
	recorder *MockUnsafeViewCrafterServerMockRecorder
}

// MockUnsafeViewCrafterServerMockRecorder is the mock recorder for MockUnsafeViewCrafterServer.
type MockUnsafeViewCrafterServerMockRecorder struct {
	mock *MockUnsafeViewCrafterServer
}

// NewMockUnsafeViewCrafterServer creates a new mock instance.
func NewMockUnsafeViewCrafterServer(ctrl *gomock.Controller) *MockUnsafeViewCrafterServer {
	mock := &MockUnsafeViewCrafterServer{ctrl: ctrl}
	mock.recorder = &MockUnsafeViewCrafterServerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockUnsafeViewCrafterServer) EXPECT() *MockUnsafeViewCrafterServerMockRecorder {
	return m.recorder
}

// mustEmbedUnimplementedViewCrafterServer mocks base method.
func (m *MockUnsafeViewCrafterServer) mustEmbedUnimplementedViewCrafterServer() {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "mustEmbedUnimplementedViewCrafterServer")
}

// mustEmbedUnimplementedViewCrafterServer indicates an expected call of mustEmbedUnimplementedViewCrafterServer.
func (mr *MockUnsafeViewCrafterServerMockRecorder) mustEmbedUnimplementedViewCrafterServer() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "mustEmbedUnimplementedViewCrafterServer", reflect.TypeOf((*MockUnsafeViewCrafterServer)(nil).mustEmbedUnimplementedViewCrafterServer))
}
