syntax = "proto3";

option go_package = "git.local/sensitive/innerpb/processing/events";

import "mvp/proto/events.proto";

message TestMessage {
  option (mvp.events.is_event) = true;
  uint64 user_id = 1;
  string Card = 2;
  bool is_active = 3;
}

message ApproveCard {
  option (mvp.events.is_event) = true;
  uint64 card_id = 1;
}

message ModifyPan {
  option (mvp.events.is_event) = true;
  uint64 project_id = 1;
  bytes pan = 2;
  string project_client_id = 3;
}

message DeactivateToken {
  option (mvp.events.is_event) = true;
  string encrypted_card_id = 1;
  uint64 project_id = 2;
}

message SaveToken {
  option (mvp.events.is_event) = true;
  uint64 acquirer_id = 1;
  uint64 card_id = 2;
  uint64 terminal_id = 3;
  string token = 4;
  uint64 project_id = 5;
}

message SaveTokenwithCard {
  uint64 acquirer_id = 1;
  uint64 card_id = 2;
  uint64 terminal_id = 3;
  string token = 4;
  uint64 project_id = 5;
}

