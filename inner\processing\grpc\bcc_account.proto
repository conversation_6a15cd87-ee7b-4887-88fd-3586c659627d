edition = "2023";

package processing.bcc_account.bcc_account;

option go_package = "git.local/sensitive/innerpb/processing/grpc";

import "inner/processing/grpc/multiaccounting.proto";
import "google/protobuf/empty.proto";

service BCCAccount {
  rpc MakeMerchantCheck(processing.multiaccounting.multiaccounting.MakeMerchantCheckRequest)returns (processing.multiaccounting.multiaccounting.MakeMerchantCheckResponse) {}
  rpc MakeTransfer(processing.multiaccounting.multiaccounting.MakeTransferRequest) returns (processing.multiaccounting.multiaccounting.MakeTransferResponse) {}
  rpc AcceptTransfer(processing.multiaccounting.multiaccounting.AcceptTransferRequest) returns (processing.multiaccounting.multiaccounting.AcceptTransferResponse) {}
  rpc DeclineTransfer(processing.multiaccounting.multiaccounting.DeclineTransferRequest) returns (processing.multiaccounting.multiaccounting.DeclineTransferResponse) {}
  rpc RedoTransfer(processing.multiaccounting.multiaccounting.RedoTransferRequest) returns (processing.multiaccounting.multiaccounting.RedoTransferResponse) {}
  rpc GetTransferDetails(processing.multiaccounting.multiaccounting.GetTransferDetailsRequest) returns (processing.multiaccounting.multiaccounting.GetTransferDetailsResponse) {}
  rpc GetTransfersList(processing.multiaccounting.multiaccounting.GetTransfersListRequest) returns (processing.multiaccounting.multiaccounting.GetTransfersListResponse) {}
  rpc GetAccountBalance(processing.multiaccounting.multiaccounting.GetAccountBalanceRequest) returns (processing.multiaccounting.multiaccounting.GetAccountBalanceResponse) {}
  rpc GetAccountStatement(processing.multiaccounting.multiaccounting.GetAccountStatementRequest) returns (processing.multiaccounting.multiaccounting.GetAccountStatementResponse) {}
  rpc GetAccountIdentifier(google.protobuf.Empty) returns (processing.multiaccounting.multiaccounting.GetAccountIdentifierResponse) {}
}


