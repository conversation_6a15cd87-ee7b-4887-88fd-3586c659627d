// Code generated by protoc-gen-go-mvp. DO NOT EDIT.
// versions:
// - protoc-gen-go-mvp v2.0.1
// created by mvp authors

package events

import (
	context "context"
	errors "errors"
	fmt "fmt"
	pb "git.local/sensitive/mvp/pb"
	watermill "github.com/ThreeDotsLabs/watermill"
	nats "github.com/ThreeDotsLabs/watermill-nats/v2/pkg/nats"
	message "github.com/ThreeDotsLabs/watermill/message"
	uuid "github.com/google/uuid"
	nats_go "github.com/nats-io/nats.go"
	otel "go.opentelemetry.io/otel"
	attribute "go.opentelemetry.io/otel/attribute"
	propagation "go.opentelemetry.io/otel/propagation"
	zap "go.uber.org/zap"
	protojson "google.golang.org/protobuf/encoding/protojson"
	anypb "google.golang.org/protobuf/types/known/anypb"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	strings "strings"
	time "time"
)

const subjectNameRefundSettings = "mutiacquiring.refundsettings"

type eventRefundSettings struct {
	jsContext         nats_go.JetStreamContext
	natsConn          *nats_go.Conn
	natsMessageRouter *message.Router
}

func newRefundSettings(
	jsContext nats_go.JetStreamContext,
	conn *nats_go.Conn,
	router *message.Router,
) *eventRefundSettings {
	return &eventRefundSettings{
		jsContext:         jsContext,
		natsConn:          conn,
		natsMessageRouter: router,
	}
}

var singleRefundSettings func(ctx context.Context) (<-chan *RefundSettings, error)

func NewRefundSettingsSubscriber(
	js nats_go.JetStreamContext,
	conn *nats_go.Conn,
	router *message.Router,
) func(ctx context.Context) (<-chan *RefundSettings, error) {
	if singleRefundSettings == nil {
		singleRefundSettings = newRefundSettings(js, conn, router).Subscribe
	}

	return singleRefundSettings
}

func NewRefundSettingsPublisher(
	js nats_go.JetStreamContext,
) func(ctx context.Context, src *RefundSettings) error {
	return newRefundSettings(js, nil, nil).Publish
}

func (s *eventRefundSettings) Publish(ctx context.Context, src *RefundSettings) error {
	var err error
	eventId := uuid.New().String()

	ctx, span := otel.Tracer("otel_trace").Start(ctx, "RefundSettings.Publish")
	defer span.End()
	defer func() {
		span.RecordError(err)
		span.SetAttributes(
			attribute.Bool("error", err != nil),
			attribute.String("nats_uuid", eventId),
		)
	}()

	m, err := anypb.New(src)
	if err != nil {
		return err
	}

	data := pb.EventInfo{
		EventId:   &eventId,
		EventTime: timestamppb.Now(),
		Data:      m,
	}
	marshal, err := protojson.Marshal(&data)
	if err != nil {
		return err
	}

	header := make(nats_go.Header)
	otel.GetTextMapPropagator().Inject(ctx, propagation.HeaderCarrier(header))

	_, err = s.jsContext.PublishMsg(&nats_go.Msg{
		Subject: subjectNameRefundSettings,
		Data:    marshal,
		Header:  header,
	})

	return err
}

func (s *eventRefundSettings) Subscribe(ctx context.Context) (<-chan *RefundSettings, error) {
	ch := make(chan *RefundSettings)

	streamCfg := newRefundSettingsStreamConfig()
	consumerCfg := newRefundSettingsConsumerConfig()
	watermillCfg := newRefundSettingsWatermillConfigs()

	fun := func(msg *message.Message) error {
		data := new(pb.EventInfo)
		resRefundSettings := new(RefundSettings)
		if err := protojson.Unmarshal(msg.Payload, data); err != nil {
			return err
		}

		if err := data.GetData().UnmarshalTo(resRefundSettings); err != nil {
			return err
		}

		ch <- resRefundSettings

		return nil
	}

	err := s.createConsumerRefundSettings(streamCfg, consumerCfg)
	if err != nil {
		return nil, err
	}
	subscriber, err := nats.NewSubscriberWithNatsConn(
		s.natsConn,
		*watermillCfg,
		newZapLoggerAdapterRefundSettings(zap.L()),
	)
	if err != nil {
		return nil, fmt.Errorf("err watermillNats.NewSubscriberWithNatsConn: %w", err)
	}
	go func() {
		select {
		case <-ctx.Done():
			if err := subscriber.Close(); err != nil {
				zap.L().Error("subscriber.Close", zap.Error(err))
			}
		}
	}()

	s.natsMessageRouter.AddNoPublisherHandler(
		consumerCfg.Name,
		subjectNameRefundSettings,
		subscriber,
		fun,
	)

	return ch, nil
}

func newRefundSettingsStreamConfig() *nats_go.StreamConfig {
	return &nats_go.StreamConfig{
		Name:         strings.ReplaceAll(subjectNameRefundSettings, ".", "_"),
		Subjects:     []string{subjectNameRefundSettings, fmt.Sprintf("%s.>", subjectNameRefundSettings)},
		Retention:    nats_go.LimitsPolicy,
		MaxConsumers: -1,
		Discard:      nats_go.DiscardOld,
		MaxAge:       time.Hour * 24 * 7,
		Storage:      nats_go.FileStorage,
		NoAck:        false,
		AllowRollup:  true,
	}
}

func newRefundSettingsConsumerConfig() *nats_go.ConsumerConfig {
	defaultAckWait := time.Second * 30

	streamNameWithoutDots := strings.ReplaceAll(subjectNameRefundSettings, ".", "_")

	consumerName := strings.ReplaceAll(fmt.Sprintf("%s_%s",
		"events",
		streamNameWithoutDots), ".", "_")

	return &nats_go.ConsumerConfig{
		Durable:        consumerName,
		Name:           consumerName,
		AckPolicy:      nats_go.AckExplicitPolicy,
		AckWait:        defaultAckWait,
		MaxDeliver:     -1,
		DeliverGroup:   consumerName,
		DeliverSubject: consumerName,
	}
}

func newRefundSettingsWatermillConfigs() *nats.SubscriberSubscriptionConfig {
	defaultAckWait := time.Second * 30

	streamNameWithoutDots := strings.ReplaceAll(subjectNameRefundSettings, ".", "_")

	consumerName := strings.ReplaceAll(fmt.Sprintf("%s_%s",
		"events",
		streamNameWithoutDots), ".", "_")

	return &nats.SubscriberSubscriptionConfig{
		Unmarshaler:    &eventMarshalerRefundSettings{},
		AckWaitTimeout: defaultAckWait,
		CloseTimeout:   defaultAckWait,
		SubjectCalculator: func(queueGroupPrefix, topic string) *nats.SubjectDetail {
			return &nats.SubjectDetail{
				Primary:    subjectNameRefundSettings,
				QueueGroup: consumerName,
			}
		},
		JetStream: nats.JetStreamConfig{
			Disabled:      false,
			AutoProvision: false,
			SubscribeOptions: []nats_go.SubOpt{
				nats_go.ManualAck(),
				nats_go.DeliverAll(),
				nats_go.AckExplicit(),
				nats_go.Bind(streamNameWithoutDots, consumerName),
				nats_go.MaxDeliver(-1),
			},
			TrackMsgId:        true,
			AckAsync:          false,
			DurableCalculator: func(prefix, topic string) string { return consumerName },
		},
	}
}

func (s *eventRefundSettings) createConsumerRefundSettings(streamCfg *nats_go.StreamConfig, consumerCfg *nats_go.ConsumerConfig) error {
	_, err := s.jsContext.StreamInfo(streamCfg.Name)
	if err != nil {
		if errors.Is(err, nats_go.ErrStreamNotFound) {
			_, err = s.jsContext.AddStream(streamCfg)
			if err != nil {
				return fmt.Errorf("err jsContext.AddStream: %w", err)
			}
		} else {
			return fmt.Errorf("can't get NATS JetStream Stream: %w", err)
		}
	}

	_, err = s.jsContext.UpdateStream(streamCfg)
	if err != nil {
		return fmt.Errorf("err jsContext.UpdateStream: %w", err)
	}

	_, err = s.jsContext.ConsumerInfo(streamCfg.Name, streamCfg.Name)
	if err != nil {
		if errors.Is(err, nats_go.ErrConsumerNotFound) {
			_, err = s.jsContext.AddConsumer(streamCfg.Name, consumerCfg)
			if err != nil {
				return fmt.Errorf("err jsContext.AddConsumer: %w", err)
			}
		} else {
			return fmt.Errorf("can't get NATS JetStream Consumer %s: %w", consumerCfg.Name, err)
		}
	}

	_, err = s.jsContext.UpdateConsumer(streamCfg.Name, consumerCfg)
	if err != nil {
		return fmt.Errorf("err jsContext.UpdateConsumer: %w", err)
	}

	return nil
}

type eventMarshalerRefundSettings struct{}

func (*eventMarshalerRefundSettings) Marshal(topic string, msg *message.Message) (*nats_go.Msg, error) {
	natsMsg := nats_go.NewMsg(topic)
	natsMsg.Data = msg.Payload

	return natsMsg, nil
}
func (*eventMarshalerRefundSettings) Unmarshal(msg *nats_go.Msg) (*message.Message, error) {
	pbMsg := &pb.EventInfo{}
	err := protojson.Unmarshal(msg.Data, pbMsg)
	if err != nil {
		return nil, err
	}

	wmMsg := message.NewMessage(pbMsg.GetEventId(), msg.Data)
	wmMsg.Metadata = map[string]string{}

	return wmMsg, nil
}

type zapLoggerAdapterRefundSettings struct {
	logger *zap.Logger
}

func newZapLoggerAdapterRefundSettings(logger *zap.Logger) *zapLoggerAdapterRefundSettings {
	return &zapLoggerAdapterRefundSettings{logger: logger}
}

func (z zapLoggerAdapterRefundSettings) Error(msg string, err error, fields watermill.LogFields) {
	z.logger.Error(msg, append(z.toZapFields(fields), zap.Error(err))...)
}

func (z zapLoggerAdapterRefundSettings) Info(msg string, fields watermill.LogFields) {
	z.logger.Info(msg, z.toZapFields(fields)...)
}

func (z zapLoggerAdapterRefundSettings) Debug(msg string, fields watermill.LogFields) {
	z.logger.Debug(msg, z.toZapFields(fields)...)
}

func (z zapLoggerAdapterRefundSettings) Trace(msg string, fields watermill.LogFields) {
	z.logger.Debug(msg, z.toZapFields(fields)...)
}

func (z zapLoggerAdapterRefundSettings) With(fields watermill.LogFields) watermill.LoggerAdapter {
	newLogger := z.logger.With(z.toZapFields(fields)...)
	return &zapLoggerAdapterRefundSettings{logger: newLogger}
}

func (z zapLoggerAdapterRefundSettings) toZapFields(fields watermill.LogFields) []zap.Field {
	zapFields := make([]zap.Field, 0, len(fields))
	for k, v := range fields {
		zapFields = append(zapFields, zap.Any(k, v))
	}

	return zapFields
}
