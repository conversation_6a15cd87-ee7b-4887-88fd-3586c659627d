// Code generated by protoc-gen-go-mvp. DO NOT EDIT.
// versions:
// - protoc-gen-go-mvp v2.0.1
// created by mvp authors

package grpc

import (
	context "context"
	grpc "google.golang.org/grpc"
	metadata "google.golang.org/grpc/metadata"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
)

func NewIamCommissionServer(
	srv CommissionServer,
) CommissionServer {
	return &iamCommissionServer{
		srv: srv,
	}
}

var _ CommissionServer = (*iamCommissionServer)(nil)

type iamCommissionServer struct {
	UnimplementedCommissionServer

	srv CommissionServer
}

func (s *iamCommissionServer) CalculateAndSaveUpperCommission(
	ctx context.Context,
	req *CalculateAndSaveUpperCommissionRequestV1,
) (
	*CalculateAndSaveUpperCommissionResponseV1,
	error,
) {
	return s.srv.CalculateAndSaveUpperCommission(ctx, req)
}

func (s *iamCommissionServer) GetCommissionForMainBalance(
	ctx context.Context,
	req *GetCommissionForMainBalanceRequestV1,
) (
	*GetCommissionForMainBalanceResponseV1,
	error,
) {
	return s.srv.GetCommissionForMainBalance(ctx, req)
}

func (s *iamCommissionServer) UpdateCommissionForCreditBalance(
	ctx context.Context,
	req *UpdateCommissionForCreditBalanceRequestV1,
) (
	*UpdateCommissionForCreditBalanceResponseV1,
	error,
) {
	return s.srv.UpdateCommissionForCreditBalance(ctx, req)
}

func (s *iamCommissionServer) CalculatePayInPrimalAmount(
	ctx context.Context,
	req *CalculatePrimalAmountRequestV1,
) (
	*CalculatePrimalAmountResponseV1,
	error,
) {
	return s.srv.CalculatePayInPrimalAmount(ctx, req)
}

func (s *iamCommissionServer) CalculatePayOutPrimalAmount(
	ctx context.Context,
	req *CalculatePrimalAmountRequestV1,
) (
	*CalculatePrimalAmountResponseV1,
	error,
) {
	return s.srv.CalculatePayOutPrimalAmount(ctx, req)
}

func (s *iamCommissionServer) FiscalizeUpperCommission(
	ctx context.Context,
	req *FiscalizeUpperCommissionRequestV1,
) (
	*emptypb.Empty,
	error,
) {
	return s.srv.FiscalizeUpperCommission(ctx, req)
}

func (s *iamCommissionServer) GetCommissionByTransactionID(
	ctx context.Context,
	req *GetCommissionByTransactionIDRequestV1,
) (
	*GetCommissionByTransactionIDResponseV1,
	error,
) {
	return s.srv.GetCommissionByTransactionID(ctx, req)
}

func NewIamCommissionClient(
	client CommissionClient,
) CommissionClient {
	return &iamCommissionClient{
		client: client,
	}
}

type iamCommissionClient struct {
	client CommissionClient
}

func (s *iamCommissionClient) CalculateAndSaveUpperCommission(
	ctx context.Context,
	req *CalculateAndSaveUpperCommissionRequestV1,
	opts ...grpc.CallOption,
) (
	*CalculateAndSaveUpperCommissionResponseV1,
	error,
) {
	md, _ := metadata.FromIncomingContext(ctx)
	return s.client.CalculateAndSaveUpperCommission(metadata.NewOutgoingContext(ctx, md), req)
}

func (s *iamCommissionClient) GetCommissionForMainBalance(
	ctx context.Context,
	req *GetCommissionForMainBalanceRequestV1,
	opts ...grpc.CallOption,
) (
	*GetCommissionForMainBalanceResponseV1,
	error,
) {
	md, _ := metadata.FromIncomingContext(ctx)
	return s.client.GetCommissionForMainBalance(metadata.NewOutgoingContext(ctx, md), req)
}

func (s *iamCommissionClient) UpdateCommissionForCreditBalance(
	ctx context.Context,
	req *UpdateCommissionForCreditBalanceRequestV1,
	opts ...grpc.CallOption,
) (
	*UpdateCommissionForCreditBalanceResponseV1,
	error,
) {
	md, _ := metadata.FromIncomingContext(ctx)
	return s.client.UpdateCommissionForCreditBalance(metadata.NewOutgoingContext(ctx, md), req)
}

func (s *iamCommissionClient) CalculatePayInPrimalAmount(
	ctx context.Context,
	req *CalculatePrimalAmountRequestV1,
	opts ...grpc.CallOption,
) (
	*CalculatePrimalAmountResponseV1,
	error,
) {
	md, _ := metadata.FromIncomingContext(ctx)
	return s.client.CalculatePayInPrimalAmount(metadata.NewOutgoingContext(ctx, md), req)
}

func (s *iamCommissionClient) CalculatePayOutPrimalAmount(
	ctx context.Context,
	req *CalculatePrimalAmountRequestV1,
	opts ...grpc.CallOption,
) (
	*CalculatePrimalAmountResponseV1,
	error,
) {
	md, _ := metadata.FromIncomingContext(ctx)
	return s.client.CalculatePayOutPrimalAmount(metadata.NewOutgoingContext(ctx, md), req)
}

func (s *iamCommissionClient) FiscalizeUpperCommission(
	ctx context.Context,
	req *FiscalizeUpperCommissionRequestV1,
	opts ...grpc.CallOption,
) (
	*emptypb.Empty,
	error,
) {
	md, _ := metadata.FromIncomingContext(ctx)
	return s.client.FiscalizeUpperCommission(metadata.NewOutgoingContext(ctx, md), req)
}

func (s *iamCommissionClient) GetCommissionByTransactionID(
	ctx context.Context,
	req *GetCommissionByTransactionIDRequestV1,
	opts ...grpc.CallOption,
) (
	*GetCommissionByTransactionIDResponseV1,
	error,
) {
	md, _ := metadata.FromIncomingContext(ctx)
	return s.client.GetCommissionByTransactionID(metadata.NewOutgoingContext(ctx, md), req)
}
