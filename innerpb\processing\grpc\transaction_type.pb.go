// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.5
// 	protoc        v5.29.3
// source: inner/processing/grpc/transaction_type.proto

package grpc

import (
	_ "git.local/sensitive/mvp/pb"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	descriptorpb "google.golang.org/protobuf/types/descriptorpb"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type EnumTransactionType int32

const (
	EnumTransactionType_Unknown                       EnumTransactionType = 0
	EnumTransactionType_TransactionTypePayIn          EnumTransactionType = 1
	EnumTransactionType_TransactionTypePayOut         EnumTransactionType = 2
	EnumTransactionType_TransactionTypeOneClickPayIn  EnumTransactionType = 3
	EnumTransactionType_TransactionTypeOneClickPayOut EnumTransactionType = 4
	EnumTransactionType_TransactionTypeApplePay       EnumTransactionType = 5
	EnumTransactionType_TransactionTypeCardLink       EnumTransactionType = 6
	EnumTransactionType_TransactionTypeRefund         EnumTransactionType = 7
	EnumTransactionType_TransactionTypeGooglePay      EnumTransactionType = 8
	EnumTransactionType_TransactionTypeTwoStagePayIn  EnumTransactionType = 9
)

// Enum value maps for EnumTransactionType.
var (
	EnumTransactionType_name = map[int32]string{
		0: "Unknown",
		1: "TransactionTypePayIn",
		2: "TransactionTypePayOut",
		3: "TransactionTypeOneClickPayIn",
		4: "TransactionTypeOneClickPayOut",
		5: "TransactionTypeApplePay",
		6: "TransactionTypeCardLink",
		7: "TransactionTypeRefund",
		8: "TransactionTypeGooglePay",
		9: "TransactionTypeTwoStagePayIn",
	}
	EnumTransactionType_value = map[string]int32{
		"Unknown":                       0,
		"TransactionTypePayIn":          1,
		"TransactionTypePayOut":         2,
		"TransactionTypeOneClickPayIn":  3,
		"TransactionTypeOneClickPayOut": 4,
		"TransactionTypeApplePay":       5,
		"TransactionTypeCardLink":       6,
		"TransactionTypeRefund":         7,
		"TransactionTypeGooglePay":      8,
		"TransactionTypeTwoStagePayIn":  9,
	}
)

func (x EnumTransactionType) Enum() *EnumTransactionType {
	p := new(EnumTransactionType)
	*p = x
	return p
}

func (x EnumTransactionType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (EnumTransactionType) Descriptor() protoreflect.EnumDescriptor {
	return file_inner_processing_grpc_transaction_type_proto_enumTypes[0].Descriptor()
}

func (EnumTransactionType) Type() protoreflect.EnumType {
	return &file_inner_processing_grpc_transaction_type_proto_enumTypes[0]
}

func (x EnumTransactionType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use EnumTransactionType.Descriptor instead.
func (EnumTransactionType) EnumDescriptor() ([]byte, []int) {
	return file_inner_processing_grpc_transaction_type_proto_rawDescGZIP(), []int{0}
}

type TransactionTypeRef struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Name          *string                `protobuf:"bytes,2,opt,name=name" json:"name,omitempty"`
	Code          *string                `protobuf:"bytes,3,opt,name=code" json:"code,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *TransactionTypeRef) Reset() {
	*x = TransactionTypeRef{}
	mi := &file_inner_processing_grpc_transaction_type_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *TransactionTypeRef) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TransactionTypeRef) ProtoMessage() {}

func (x *TransactionTypeRef) ProtoReflect() protoreflect.Message {
	mi := &file_inner_processing_grpc_transaction_type_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TransactionTypeRef.ProtoReflect.Descriptor instead.
func (*TransactionTypeRef) Descriptor() ([]byte, []int) {
	return file_inner_processing_grpc_transaction_type_proto_rawDescGZIP(), []int{0}
}

func (x *TransactionTypeRef) GetName() string {
	if x != nil && x.Name != nil {
		return *x.Name
	}
	return ""
}

func (x *TransactionTypeRef) GetCode() string {
	if x != nil && x.Code != nil {
		return *x.Code
	}
	return ""
}

type TransactionTypeResponseV1 struct {
	state         protoimpl.MessageState        `protogen:"open.v1"`
	Data          []*TransactionTypesResponseV1 `protobuf:"bytes,1,rep,name=data" json:"data,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *TransactionTypeResponseV1) Reset() {
	*x = TransactionTypeResponseV1{}
	mi := &file_inner_processing_grpc_transaction_type_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *TransactionTypeResponseV1) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TransactionTypeResponseV1) ProtoMessage() {}

func (x *TransactionTypeResponseV1) ProtoReflect() protoreflect.Message {
	mi := &file_inner_processing_grpc_transaction_type_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TransactionTypeResponseV1.ProtoReflect.Descriptor instead.
func (*TransactionTypeResponseV1) Descriptor() ([]byte, []int) {
	return file_inner_processing_grpc_transaction_type_proto_rawDescGZIP(), []int{1}
}

func (x *TransactionTypeResponseV1) GetData() []*TransactionTypesResponseV1 {
	if x != nil {
		return x.Data
	}
	return nil
}

type TransactionTypesResponseV1 struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            *uint64                `protobuf:"varint,1,opt,name=id" json:"id,omitempty"`
	Name          *string                `protobuf:"bytes,2,opt,name=name" json:"name,omitempty"`
	Code          *string                `protobuf:"bytes,3,opt,name=code" json:"code,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *TransactionTypesResponseV1) Reset() {
	*x = TransactionTypesResponseV1{}
	mi := &file_inner_processing_grpc_transaction_type_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *TransactionTypesResponseV1) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TransactionTypesResponseV1) ProtoMessage() {}

func (x *TransactionTypesResponseV1) ProtoReflect() protoreflect.Message {
	mi := &file_inner_processing_grpc_transaction_type_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TransactionTypesResponseV1.ProtoReflect.Descriptor instead.
func (*TransactionTypesResponseV1) Descriptor() ([]byte, []int) {
	return file_inner_processing_grpc_transaction_type_proto_rawDescGZIP(), []int{2}
}

func (x *TransactionTypesResponseV1) GetId() uint64 {
	if x != nil && x.Id != nil {
		return *x.Id
	}
	return 0
}

func (x *TransactionTypesResponseV1) GetName() string {
	if x != nil && x.Name != nil {
		return *x.Name
	}
	return ""
}

func (x *TransactionTypesResponseV1) GetCode() string {
	if x != nil && x.Code != nil {
		return *x.Code
	}
	return ""
}

type GetAggregatedTransactionTypeRequestV1 struct {
	state             protoimpl.MessageState `protogen:"open.v1"`
	TransactionTypeId *uint64                `protobuf:"varint,1,opt,name=transaction_type_id,json=transactionTypeId" json:"transaction_type_id,omitempty"`
	AggregatedTypeId  *uint64                `protobuf:"varint,2,opt,name=aggregated_type_id,json=aggregatedTypeId" json:"aggregated_type_id,omitempty"`
	unknownFields     protoimpl.UnknownFields
	sizeCache         protoimpl.SizeCache
}

func (x *GetAggregatedTransactionTypeRequestV1) Reset() {
	*x = GetAggregatedTransactionTypeRequestV1{}
	mi := &file_inner_processing_grpc_transaction_type_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetAggregatedTransactionTypeRequestV1) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAggregatedTransactionTypeRequestV1) ProtoMessage() {}

func (x *GetAggregatedTransactionTypeRequestV1) ProtoReflect() protoreflect.Message {
	mi := &file_inner_processing_grpc_transaction_type_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAggregatedTransactionTypeRequestV1.ProtoReflect.Descriptor instead.
func (*GetAggregatedTransactionTypeRequestV1) Descriptor() ([]byte, []int) {
	return file_inner_processing_grpc_transaction_type_proto_rawDescGZIP(), []int{3}
}

func (x *GetAggregatedTransactionTypeRequestV1) GetTransactionTypeId() uint64 {
	if x != nil && x.TransactionTypeId != nil {
		return *x.TransactionTypeId
	}
	return 0
}

func (x *GetAggregatedTransactionTypeRequestV1) GetAggregatedTypeId() uint64 {
	if x != nil && x.AggregatedTypeId != nil {
		return *x.AggregatedTypeId
	}
	return 0
}

type GetAggregatedTransactionTypeResponseV1 struct {
	state              protoimpl.MessageState `protogen:"open.v1"`
	TransactionTypeId  *uint64                `protobuf:"varint,1,opt,name=transaction_type_id,json=transactionTypeId" json:"transaction_type_id,omitempty"`
	AggregatedTypeId   *uint64                `protobuf:"varint,2,opt,name=aggregated_type_id,json=aggregatedTypeId" json:"aggregated_type_id,omitempty"`
	AggregatedTypeCode *string                `protobuf:"bytes,3,opt,name=aggregated_type_code,json=aggregatedTypeCode" json:"aggregated_type_code,omitempty"`
	unknownFields      protoimpl.UnknownFields
	sizeCache          protoimpl.SizeCache
}

func (x *GetAggregatedTransactionTypeResponseV1) Reset() {
	*x = GetAggregatedTransactionTypeResponseV1{}
	mi := &file_inner_processing_grpc_transaction_type_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetAggregatedTransactionTypeResponseV1) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAggregatedTransactionTypeResponseV1) ProtoMessage() {}

func (x *GetAggregatedTransactionTypeResponseV1) ProtoReflect() protoreflect.Message {
	mi := &file_inner_processing_grpc_transaction_type_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAggregatedTransactionTypeResponseV1.ProtoReflect.Descriptor instead.
func (*GetAggregatedTransactionTypeResponseV1) Descriptor() ([]byte, []int) {
	return file_inner_processing_grpc_transaction_type_proto_rawDescGZIP(), []int{4}
}

func (x *GetAggregatedTransactionTypeResponseV1) GetTransactionTypeId() uint64 {
	if x != nil && x.TransactionTypeId != nil {
		return *x.TransactionTypeId
	}
	return 0
}

func (x *GetAggregatedTransactionTypeResponseV1) GetAggregatedTypeId() uint64 {
	if x != nil && x.AggregatedTypeId != nil {
		return *x.AggregatedTypeId
	}
	return 0
}

func (x *GetAggregatedTransactionTypeResponseV1) GetAggregatedTypeCode() string {
	if x != nil && x.AggregatedTypeCode != nil {
		return *x.AggregatedTypeCode
	}
	return ""
}

type GetAggregatedTypeByIDRequestV1 struct {
	state            protoimpl.MessageState `protogen:"open.v1"`
	AggregatedTypeId *uint64                `protobuf:"varint,1,opt,name=aggregated_type_id,json=aggregatedTypeId" json:"aggregated_type_id,omitempty"`
	unknownFields    protoimpl.UnknownFields
	sizeCache        protoimpl.SizeCache
}

func (x *GetAggregatedTypeByIDRequestV1) Reset() {
	*x = GetAggregatedTypeByIDRequestV1{}
	mi := &file_inner_processing_grpc_transaction_type_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetAggregatedTypeByIDRequestV1) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAggregatedTypeByIDRequestV1) ProtoMessage() {}

func (x *GetAggregatedTypeByIDRequestV1) ProtoReflect() protoreflect.Message {
	mi := &file_inner_processing_grpc_transaction_type_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAggregatedTypeByIDRequestV1.ProtoReflect.Descriptor instead.
func (*GetAggregatedTypeByIDRequestV1) Descriptor() ([]byte, []int) {
	return file_inner_processing_grpc_transaction_type_proto_rawDescGZIP(), []int{5}
}

func (x *GetAggregatedTypeByIDRequestV1) GetAggregatedTypeId() uint64 {
	if x != nil && x.AggregatedTypeId != nil {
		return *x.AggregatedTypeId
	}
	return 0
}

type GetAggregatedTypeByIDResponseV1 struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            *uint64                `protobuf:"varint,1,opt,name=id" json:"id,omitempty"`
	Name          *string                `protobuf:"bytes,2,opt,name=name" json:"name,omitempty"`
	Code          *string                `protobuf:"bytes,3,opt,name=code" json:"code,omitempty"`
	Description   *string                `protobuf:"bytes,4,opt,name=description" json:"description,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetAggregatedTypeByIDResponseV1) Reset() {
	*x = GetAggregatedTypeByIDResponseV1{}
	mi := &file_inner_processing_grpc_transaction_type_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetAggregatedTypeByIDResponseV1) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAggregatedTypeByIDResponseV1) ProtoMessage() {}

func (x *GetAggregatedTypeByIDResponseV1) ProtoReflect() protoreflect.Message {
	mi := &file_inner_processing_grpc_transaction_type_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAggregatedTypeByIDResponseV1.ProtoReflect.Descriptor instead.
func (*GetAggregatedTypeByIDResponseV1) Descriptor() ([]byte, []int) {
	return file_inner_processing_grpc_transaction_type_proto_rawDescGZIP(), []int{6}
}

func (x *GetAggregatedTypeByIDResponseV1) GetId() uint64 {
	if x != nil && x.Id != nil {
		return *x.Id
	}
	return 0
}

func (x *GetAggregatedTypeByIDResponseV1) GetName() string {
	if x != nil && x.Name != nil {
		return *x.Name
	}
	return ""
}

func (x *GetAggregatedTypeByIDResponseV1) GetCode() string {
	if x != nil && x.Code != nil {
		return *x.Code
	}
	return ""
}

func (x *GetAggregatedTypeByIDResponseV1) GetDescription() string {
	if x != nil && x.Description != nil {
		return *x.Description
	}
	return ""
}

type GetAggregatedTransactionTypeByTypeIDRequest struct {
	state             protoimpl.MessageState `protogen:"open.v1"`
	TransactionTypeId *uint64                `protobuf:"varint,1,opt,name=transaction_type_id,json=transactionTypeId" json:"transaction_type_id,omitempty"`
	unknownFields     protoimpl.UnknownFields
	sizeCache         protoimpl.SizeCache
}

func (x *GetAggregatedTransactionTypeByTypeIDRequest) Reset() {
	*x = GetAggregatedTransactionTypeByTypeIDRequest{}
	mi := &file_inner_processing_grpc_transaction_type_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetAggregatedTransactionTypeByTypeIDRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAggregatedTransactionTypeByTypeIDRequest) ProtoMessage() {}

func (x *GetAggregatedTransactionTypeByTypeIDRequest) ProtoReflect() protoreflect.Message {
	mi := &file_inner_processing_grpc_transaction_type_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAggregatedTransactionTypeByTypeIDRequest.ProtoReflect.Descriptor instead.
func (*GetAggregatedTransactionTypeByTypeIDRequest) Descriptor() ([]byte, []int) {
	return file_inner_processing_grpc_transaction_type_proto_rawDescGZIP(), []int{7}
}

func (x *GetAggregatedTransactionTypeByTypeIDRequest) GetTransactionTypeId() uint64 {
	if x != nil && x.TransactionTypeId != nil {
		return *x.TransactionTypeId
	}
	return 0
}

type GetAggregatedTransactionTypeByTypeIDResponse struct {
	state             protoimpl.MessageState `protogen:"open.v1"`
	AggregatedTypeId  *uint64                `protobuf:"varint,1,opt,name=aggregated_type_id,json=aggregatedTypeId" json:"aggregated_type_id,omitempty"`
	TransactionTypeId *uint64                `protobuf:"varint,2,opt,name=transaction_type_id,json=transactionTypeId" json:"transaction_type_id,omitempty"`
	unknownFields     protoimpl.UnknownFields
	sizeCache         protoimpl.SizeCache
}

func (x *GetAggregatedTransactionTypeByTypeIDResponse) Reset() {
	*x = GetAggregatedTransactionTypeByTypeIDResponse{}
	mi := &file_inner_processing_grpc_transaction_type_proto_msgTypes[8]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetAggregatedTransactionTypeByTypeIDResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAggregatedTransactionTypeByTypeIDResponse) ProtoMessage() {}

func (x *GetAggregatedTransactionTypeByTypeIDResponse) ProtoReflect() protoreflect.Message {
	mi := &file_inner_processing_grpc_transaction_type_proto_msgTypes[8]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAggregatedTransactionTypeByTypeIDResponse.ProtoReflect.Descriptor instead.
func (*GetAggregatedTransactionTypeByTypeIDResponse) Descriptor() ([]byte, []int) {
	return file_inner_processing_grpc_transaction_type_proto_rawDescGZIP(), []int{8}
}

func (x *GetAggregatedTransactionTypeByTypeIDResponse) GetAggregatedTypeId() uint64 {
	if x != nil && x.AggregatedTypeId != nil {
		return *x.AggregatedTypeId
	}
	return 0
}

func (x *GetAggregatedTransactionTypeByTypeIDResponse) GetTransactionTypeId() uint64 {
	if x != nil && x.TransactionTypeId != nil {
		return *x.TransactionTypeId
	}
	return 0
}

var file_inner_processing_grpc_transaction_type_proto_extTypes = []protoimpl.ExtensionInfo{
	{
		ExtendedType:  (*descriptorpb.EnumValueOptions)(nil),
		ExtensionType: (*TransactionTypeRef)(nil),
		Field:         200002,
		Name:          "processing.transaction.transaction_type.transaction_type_value",
		Tag:           "bytes,200002,opt,name=transaction_type_value",
		Filename:      "inner/processing/grpc/transaction_type.proto",
	},
	{
		ExtendedType:  (*descriptorpb.EnumOptions)(nil),
		ExtensionType: (*TransactionTypeRef)(nil),
		Field:         200003,
		Name:          "processing.transaction.transaction_type.default_transaction_type_value",
		Tag:           "bytes,200003,opt,name=default_transaction_type_value",
		Filename:      "inner/processing/grpc/transaction_type.proto",
	},
}

// Extension fields to descriptorpb.EnumValueOptions.
var (
	// optional processing.transaction.transaction_type.TransactionTypeRef transaction_type_value = 200002;
	E_TransactionTypeValue = &file_inner_processing_grpc_transaction_type_proto_extTypes[0]
)

// Extension fields to descriptorpb.EnumOptions.
var (
	// optional processing.transaction.transaction_type.TransactionTypeRef default_transaction_type_value = 200003;
	E_DefaultTransactionTypeValue = &file_inner_processing_grpc_transaction_type_proto_extTypes[1]
)

var File_inner_processing_grpc_transaction_type_proto protoreflect.FileDescriptor

var file_inner_processing_grpc_transaction_type_proto_rawDesc = string([]byte{
	0x0a, 0x2c, 0x69, 0x6e, 0x6e, 0x65, 0x72, 0x2f, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69,
	0x6e, 0x67, 0x2f, 0x67, 0x72, 0x70, 0x63, 0x2f, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74,
	0x69, 0x6f, 0x6e, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x27,
	0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x2e, 0x74, 0x72, 0x61, 0x6e, 0x73,
	0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69,
	0x6f, 0x6e, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x1a, 0x1b, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x65, 0x6d, 0x70, 0x74, 0x79, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x20, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x6f, 0x72,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x14, 0x6d, 0x76, 0x70, 0x2f, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x2f, 0x72, 0x65, 0x66, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x3c, 0x0a, 0x12,
	0x54, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x52,
	0x65, 0x66, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x22, 0x74, 0x0a, 0x19, 0x54, 0x72,
	0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x56, 0x31, 0x12, 0x57, 0x0a, 0x04, 0x64, 0x61, 0x74, 0x61, 0x18,
	0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x43, 0x2e, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69,
	0x6e, 0x67, 0x2e, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x74,
	0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x2e,
	0x54, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x73,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x56, 0x31, 0x52, 0x04, 0x64, 0x61, 0x74, 0x61,
	0x22, 0x54, 0x0a, 0x1a, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x54,
	0x79, 0x70, 0x65, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x56, 0x31, 0x12, 0x0e,
	0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x02, 0x69, 0x64, 0x12, 0x12,
	0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61,
	0x6d, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x22, 0x85, 0x01, 0x0a, 0x25, 0x47, 0x65, 0x74, 0x41, 0x67,
	0x67, 0x72, 0x65, 0x67, 0x61, 0x74, 0x65, 0x64, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74,
	0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x56, 0x31,
	0x12, 0x2e, 0x0a, 0x13, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x5f,
	0x74, 0x79, 0x70, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x11, 0x74,
	0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x49, 0x64,
	0x12, 0x2c, 0x0a, 0x12, 0x61, 0x67, 0x67, 0x72, 0x65, 0x67, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x74,
	0x79, 0x70, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x04, 0x52, 0x10, 0x61, 0x67,
	0x67, 0x72, 0x65, 0x67, 0x61, 0x74, 0x65, 0x64, 0x54, 0x79, 0x70, 0x65, 0x49, 0x64, 0x22, 0xb8,
	0x01, 0x0a, 0x26, 0x47, 0x65, 0x74, 0x41, 0x67, 0x67, 0x72, 0x65, 0x67, 0x61, 0x74, 0x65, 0x64,
	0x54, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x56, 0x31, 0x12, 0x2e, 0x0a, 0x13, 0x74, 0x72, 0x61,
	0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x5f, 0x69, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x11, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74,
	0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x49, 0x64, 0x12, 0x2c, 0x0a, 0x12, 0x61, 0x67, 0x67,
	0x72, 0x65, 0x67, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x5f, 0x69, 0x64, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x04, 0x52, 0x10, 0x61, 0x67, 0x67, 0x72, 0x65, 0x67, 0x61, 0x74, 0x65,
	0x64, 0x54, 0x79, 0x70, 0x65, 0x49, 0x64, 0x12, 0x30, 0x0a, 0x14, 0x61, 0x67, 0x67, 0x72, 0x65,
	0x67, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x12, 0x61, 0x67, 0x67, 0x72, 0x65, 0x67, 0x61, 0x74, 0x65,
	0x64, 0x54, 0x79, 0x70, 0x65, 0x43, 0x6f, 0x64, 0x65, 0x22, 0x4e, 0x0a, 0x1e, 0x47, 0x65, 0x74,
	0x41, 0x67, 0x67, 0x72, 0x65, 0x67, 0x61, 0x74, 0x65, 0x64, 0x54, 0x79, 0x70, 0x65, 0x42, 0x79,
	0x49, 0x44, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x56, 0x31, 0x12, 0x2c, 0x0a, 0x12, 0x61,
	0x67, 0x67, 0x72, 0x65, 0x67, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x5f, 0x69,
	0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x10, 0x61, 0x67, 0x67, 0x72, 0x65, 0x67, 0x61,
	0x74, 0x65, 0x64, 0x54, 0x79, 0x70, 0x65, 0x49, 0x64, 0x22, 0x7b, 0x0a, 0x1f, 0x47, 0x65, 0x74,
	0x41, 0x67, 0x67, 0x72, 0x65, 0x67, 0x61, 0x74, 0x65, 0x64, 0x54, 0x79, 0x70, 0x65, 0x42, 0x79,
	0x49, 0x44, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x56, 0x31, 0x12, 0x0e, 0x0a, 0x02,
	0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x02, 0x69, 0x64, 0x12, 0x12, 0x0a, 0x04,
	0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65,
	0x12, 0x12, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04,
	0x63, 0x6f, 0x64, 0x65, 0x12, 0x20, 0x0a, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74,
	0x69, 0x6f, 0x6e, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72,
	0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x22, 0x5d, 0x0a, 0x2b, 0x47, 0x65, 0x74, 0x41, 0x67, 0x67,
	0x72, 0x65, 0x67, 0x61, 0x74, 0x65, 0x64, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69,
	0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x42, 0x79, 0x54, 0x79, 0x70, 0x65, 0x49, 0x44, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x2e, 0x0a, 0x13, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63,
	0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x04, 0x52, 0x11, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x54,
	0x79, 0x70, 0x65, 0x49, 0x64, 0x22, 0x8c, 0x01, 0x0a, 0x2c, 0x47, 0x65, 0x74, 0x41, 0x67, 0x67,
	0x72, 0x65, 0x67, 0x61, 0x74, 0x65, 0x64, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69,
	0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x42, 0x79, 0x54, 0x79, 0x70, 0x65, 0x49, 0x44, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x2c, 0x0a, 0x12, 0x61, 0x67, 0x67, 0x72, 0x65, 0x67,
	0x61, 0x74, 0x65, 0x64, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x04, 0x52, 0x10, 0x61, 0x67, 0x67, 0x72, 0x65, 0x67, 0x61, 0x74, 0x65, 0x64, 0x54, 0x79,
	0x70, 0x65, 0x49, 0x64, 0x12, 0x2e, 0x0a, 0x13, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74,
	0x69, 0x6f, 0x6e, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x04, 0x52, 0x11, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x79,
	0x70, 0x65, 0x49, 0x64, 0x2a, 0x91, 0x06, 0x0a, 0x13, 0x45, 0x6e, 0x75, 0x6d, 0x54, 0x72, 0x61,
	0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x12, 0x23, 0x0a, 0x07,
	0x55, 0x6e, 0x6b, 0x6e, 0x6f, 0x77, 0x6e, 0x10, 0x00, 0x1a, 0x16, 0x92, 0xd4, 0x61, 0x12, 0x12,
	0x07, 0x55, 0x6e, 0x6b, 0x6e, 0x6f, 0x77, 0x6e, 0x1a, 0x07, 0x55, 0x6e, 0x6b, 0x6e, 0x6f, 0x77,
	0x6e, 0x12, 0x2e, 0x0a, 0x14, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e,
	0x54, 0x79, 0x70, 0x65, 0x50, 0x61, 0x79, 0x49, 0x6e, 0x10, 0x01, 0x1a, 0x14, 0x92, 0xd4, 0x61,
	0x10, 0x12, 0x0a, 0xd0, 0x9f, 0xd1, 0x80, 0xd0, 0xb8, 0xd1, 0x91, 0xd0, 0xbc, 0x1a, 0x02, 0x69,
	0x6e, 0x12, 0x30, 0x0a, 0x15, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e,
	0x54, 0x79, 0x70, 0x65, 0x50, 0x61, 0x79, 0x4f, 0x75, 0x74, 0x10, 0x02, 0x1a, 0x15, 0x92, 0xd4,
	0x61, 0x11, 0x12, 0x0a, 0xd0, 0x92, 0xd1, 0x8b, 0xd0, 0xb2, 0xd0, 0xbe, 0xd0, 0xb4, 0x1a, 0x03,
	0x6f, 0x75, 0x74, 0x12, 0x5d, 0x0a, 0x1c, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69,
	0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x4f, 0x6e, 0x65, 0x43, 0x6c, 0x69, 0x63, 0x6b, 0x50, 0x61,
	0x79, 0x49, 0x6e, 0x10, 0x03, 0x1a, 0x3b, 0x92, 0xd4, 0x61, 0x37, 0x12, 0x23, 0xd0, 0xa0, 0xd0,
	0xb5, 0xd0, 0xba, 0xd1, 0x83, 0xd1, 0x80, 0xd1, 0x80, 0xd0, 0xb5, 0xd0, 0xbd, 0xd1, 0x82, 0xd0,
	0xbd, 0xd1, 0x8b, 0xd0, 0xb9, 0x20, 0xd0, 0xbf, 0xd1, 0x80, 0xd0, 0xb8, 0xd1, 0x91, 0xd0, 0xbc,
	0x1a, 0x10, 0x6f, 0x6e, 0x65, 0x5f, 0x63, 0x6c, 0x69, 0x63, 0x6b, 0x5f, 0x70, 0x61, 0x79, 0x5f,
	0x69, 0x6e, 0x12, 0x5f, 0x0a, 0x1d, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f,
	0x6e, 0x54, 0x79, 0x70, 0x65, 0x4f, 0x6e, 0x65, 0x43, 0x6c, 0x69, 0x63, 0x6b, 0x50, 0x61, 0x79,
	0x4f, 0x75, 0x74, 0x10, 0x04, 0x1a, 0x3c, 0x92, 0xd4, 0x61, 0x38, 0x12, 0x23, 0xd0, 0xa0, 0xd0,
	0xb5, 0xd0, 0xba, 0xd1, 0x83, 0xd1, 0x80, 0xd1, 0x80, 0xd0, 0xb5, 0xd0, 0xbd, 0xd1, 0x82, 0xd0,
	0xbd, 0xd1, 0x8b, 0xd0, 0xb9, 0x20, 0xd0, 0xb2, 0xd1, 0x8b, 0xd0, 0xb2, 0xd0, 0xbe, 0xd0, 0xb4,
	0x1a, 0x11, 0x6f, 0x6e, 0x65, 0x5f, 0x63, 0x6c, 0x69, 0x63, 0x6b, 0x5f, 0x70, 0x61, 0x79, 0x5f,
	0x6f, 0x75, 0x74, 0x12, 0x37, 0x0a, 0x17, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69,
	0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x41, 0x70, 0x70, 0x6c, 0x65, 0x50, 0x61, 0x79, 0x10, 0x05,
	0x1a, 0x1a, 0x92, 0xd4, 0x61, 0x16, 0x12, 0x09, 0x41, 0x70, 0x70, 0x6c, 0x65, 0x20, 0x50, 0x61,
	0x79, 0x1a, 0x09, 0x61, 0x70, 0x70, 0x6c, 0x65, 0x5f, 0x70, 0x61, 0x79, 0x12, 0x49, 0x0a, 0x17,
	0x54, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x43,
	0x61, 0x72, 0x64, 0x4c, 0x69, 0x6e, 0x6b, 0x10, 0x06, 0x1a, 0x2c, 0x92, 0xd4, 0x61, 0x28, 0x12,
	0x1b, 0xd0, 0x9f, 0xd1, 0x80, 0xd0, 0xb8, 0xd0, 0xb2, 0xd1, 0x8f, 0xd0, 0xb7, 0xd0, 0xba, 0xd0,
	0xb0, 0x20, 0xd0, 0xba, 0xd0, 0xb0, 0xd1, 0x80, 0xd1, 0x82, 0xd1, 0x8b, 0x1a, 0x09, 0x63, 0x61,
	0x72, 0x64, 0x5f, 0x6c, 0x69, 0x6e, 0x6b, 0x12, 0x37, 0x0a, 0x15, 0x54, 0x72, 0x61, 0x6e, 0x73,
	0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x52, 0x65, 0x66, 0x75, 0x6e, 0x64,
	0x10, 0x07, 0x1a, 0x1c, 0x92, 0xd4, 0x61, 0x18, 0x12, 0x0e, 0xd0, 0x92, 0xd0, 0xbe, 0xd0, 0xb7,
	0xd0, 0xb2, 0xd1, 0x80, 0xd0, 0xb0, 0xd1, 0x82, 0x1a, 0x06, 0x72, 0x65, 0x66, 0x75, 0x6e, 0x64,
	0x12, 0x3f, 0x0a, 0x18, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x54,
	0x79, 0x70, 0x65, 0x47, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x50, 0x61, 0x79, 0x10, 0x08, 0x1a, 0x21,
	0x92, 0xd4, 0x61, 0x1d, 0x12, 0x0f, 0xd0, 0x93, 0xd1, 0x83, 0xd0, 0xb3, 0xd0, 0xbb, 0x20, 0xd0,
	0x9f, 0xd1, 0x8d, 0xd0, 0xb9, 0x1a, 0x0a, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x5f, 0x70, 0x61,
	0x79, 0x12, 0x5d, 0x0a, 0x1c, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e,
	0x54, 0x79, 0x70, 0x65, 0x54, 0x77, 0x6f, 0x53, 0x74, 0x61, 0x67, 0x65, 0x50, 0x61, 0x79, 0x49,
	0x6e, 0x10, 0x09, 0x1a, 0x3b, 0x92, 0xd4, 0x61, 0x37, 0x12, 0x23, 0xd0, 0x94, 0xd0, 0xb2, 0xd1,
	0x83, 0xd1, 0x81, 0xd1, 0x82, 0xd0, 0xb0, 0xd0, 0xb4, 0xd0, 0xb8, 0xd0, 0xb9, 0xd0, 0xbd, 0xd1,
	0x8b, 0xd0, 0xb9, 0x20, 0xd0, 0xbf, 0xd1, 0x80, 0xd0, 0xb8, 0xd0, 0xb5, 0xd0, 0xbc, 0x1a, 0x10,
	0x74, 0x77, 0x6f, 0x5f, 0x73, 0x74, 0x61, 0x67, 0x65, 0x5f, 0x70, 0x61, 0x79, 0x5f, 0x69, 0x6e,
	0x1a, 0x56, 0x9a, 0xd4, 0x61, 0x12, 0x12, 0x07, 0x55, 0x6e, 0x6b, 0x6e, 0x6f, 0x77, 0x6e, 0x1a,
	0x07, 0x55, 0x6e, 0x6b, 0x6e, 0x6f, 0x77, 0x6e, 0xaa, 0x82, 0xec, 0x8e, 0x02, 0x1e, 0x64, 0x65,
	0x66, 0x61, 0x75, 0x6c, 0x74, 0x5f, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f,
	0x6e, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x5f, 0x76, 0x61, 0x6c, 0x75, 0x65, 0xb2, 0x82, 0xec, 0x8e,
	0x02, 0x16, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x74, 0x79,
	0x70, 0x65, 0x5f, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x32, 0xb9, 0x07, 0x0a, 0x0f, 0x54, 0x72, 0x61,
	0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x12, 0x66, 0x0a, 0x06,
	0x47, 0x65, 0x74, 0x41, 0x6c, 0x6c, 0x12, 0x16, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x1a, 0x42,
	0x2e, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x2e, 0x74, 0x72, 0x61, 0x6e,
	0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74,
	0x69, 0x6f, 0x6e, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63,
	0x74, 0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x56, 0x31, 0x22, 0x00, 0x12, 0x79, 0x0a, 0x19, 0x47, 0x65, 0x74, 0x54, 0x72, 0x61, 0x6e, 0x73,
	0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x50, 0x61, 0x79, 0x4f, 0x75, 0x74, 0x54, 0x79, 0x70, 0x65,
	0x73, 0x12, 0x16, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x62, 0x75, 0x66, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x1a, 0x42, 0x2e, 0x70, 0x72, 0x6f, 0x63,
	0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x2e, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69,
	0x6f, 0x6e, 0x2e, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x74,
	0x79, 0x70, 0x65, 0x2e, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x54,
	0x79, 0x70, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x56, 0x31, 0x22, 0x00, 0x12,
	0x78, 0x0a, 0x18, 0x47, 0x65, 0x74, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f,
	0x6e, 0x50, 0x61, 0x79, 0x49, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x73, 0x12, 0x16, 0x2e, 0x67, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x45, 0x6d,
	0x70, 0x74, 0x79, 0x1a, 0x42, 0x2e, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67,
	0x2e, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x74, 0x72, 0x61,
	0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x54, 0x72,
	0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x56, 0x31, 0x22, 0x00, 0x12, 0xc1, 0x01, 0x0a, 0x1c, 0x47, 0x65,
	0x74, 0x41, 0x67, 0x67, 0x72, 0x65, 0x67, 0x61, 0x74, 0x65, 0x64, 0x54, 0x72, 0x61, 0x6e, 0x73,
	0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x12, 0x4e, 0x2e, 0x70, 0x72, 0x6f,
	0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x2e, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74,
	0x69, 0x6f, 0x6e, 0x2e, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x5f,
	0x74, 0x79, 0x70, 0x65, 0x2e, 0x47, 0x65, 0x74, 0x41, 0x67, 0x67, 0x72, 0x65, 0x67, 0x61, 0x74,
	0x65, 0x64, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70,
	0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x56, 0x31, 0x1a, 0x4f, 0x2e, 0x70, 0x72, 0x6f,
	0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x2e, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74,
	0x69, 0x6f, 0x6e, 0x2e, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x5f,
	0x74, 0x79, 0x70, 0x65, 0x2e, 0x47, 0x65, 0x74, 0x41, 0x67, 0x67, 0x72, 0x65, 0x67, 0x61, 0x74,
	0x65, 0x64, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70,
	0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x56, 0x31, 0x22, 0x00, 0x12, 0xac, 0x01,
	0x0a, 0x15, 0x47, 0x65, 0x74, 0x41, 0x67, 0x67, 0x72, 0x65, 0x67, 0x61, 0x74, 0x65, 0x64, 0x54,
	0x79, 0x70, 0x65, 0x42, 0x79, 0x49, 0x44, 0x12, 0x47, 0x2e, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73,
	0x73, 0x69, 0x6e, 0x67, 0x2e, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e,
	0x2e, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x74, 0x79, 0x70,
	0x65, 0x2e, 0x47, 0x65, 0x74, 0x41, 0x67, 0x67, 0x72, 0x65, 0x67, 0x61, 0x74, 0x65, 0x64, 0x54,
	0x79, 0x70, 0x65, 0x42, 0x79, 0x49, 0x44, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x56, 0x31,
	0x1a, 0x48, 0x2e, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x2e, 0x74, 0x72,
	0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61,
	0x63, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x47, 0x65, 0x74, 0x41, 0x67,
	0x67, 0x72, 0x65, 0x67, 0x61, 0x74, 0x65, 0x64, 0x54, 0x79, 0x70, 0x65, 0x42, 0x79, 0x49, 0x44,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x56, 0x31, 0x22, 0x00, 0x12, 0xd5, 0x01, 0x0a,
	0x24, 0x47, 0x65, 0x74, 0x41, 0x67, 0x67, 0x72, 0x65, 0x67, 0x61, 0x74, 0x65, 0x64, 0x54, 0x72,
	0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x42, 0x79, 0x54,
	0x79, 0x70, 0x65, 0x49, 0x44, 0x12, 0x54, 0x2e, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69,
	0x6e, 0x67, 0x2e, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x74,
	0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x2e,
	0x47, 0x65, 0x74, 0x41, 0x67, 0x67, 0x72, 0x65, 0x67, 0x61, 0x74, 0x65, 0x64, 0x54, 0x72, 0x61,
	0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x42, 0x79, 0x54, 0x79,
	0x70, 0x65, 0x49, 0x44, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x55, 0x2e, 0x70, 0x72,
	0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x2e, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63,
	0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e,
	0x5f, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x47, 0x65, 0x74, 0x41, 0x67, 0x67, 0x72, 0x65, 0x67, 0x61,
	0x74, 0x65, 0x64, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x79,
	0x70, 0x65, 0x42, 0x79, 0x54, 0x79, 0x70, 0x65, 0x49, 0x44, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x22, 0x00, 0x3a, 0x96, 0x01, 0x0a, 0x16, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63,
	0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x5f, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x12,
	0x21, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75,
	0x66, 0x2e, 0x45, 0x6e, 0x75, 0x6d, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x4f, 0x70, 0x74, 0x69, 0x6f,
	0x6e, 0x73, 0x18, 0xc2, 0x9a, 0x0c, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x3b, 0x2e, 0x70, 0x72, 0x6f,
	0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x2e, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74,
	0x69, 0x6f, 0x6e, 0x2e, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x5f,
	0x74, 0x79, 0x70, 0x65, 0x2e, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e,
	0x54, 0x79, 0x70, 0x65, 0x52, 0x65, 0x66, 0x52, 0x14, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63,
	0x74, 0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0xa0, 0x01,
	0x0a, 0x1e, 0x64, 0x65, 0x66, 0x61, 0x75, 0x6c, 0x74, 0x5f, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61,
	0x63, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x5f, 0x76, 0x61, 0x6c, 0x75, 0x65,
	0x12, 0x1c, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62,
	0x75, 0x66, 0x2e, 0x45, 0x6e, 0x75, 0x6d, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0xc3,
	0x9a, 0x0c, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x3b, 0x2e, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73,
	0x69, 0x6e, 0x67, 0x2e, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x2e,
	0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x74, 0x79, 0x70, 0x65,
	0x2e, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65,
	0x52, 0x65, 0x66, 0x52, 0x1b, 0x64, 0x65, 0x66, 0x61, 0x75, 0x6c, 0x74, 0x54, 0x72, 0x61, 0x6e,
	0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x56, 0x61, 0x6c, 0x75, 0x65,
	0x42, 0x2d, 0x5a, 0x2b, 0x67, 0x69, 0x74, 0x2e, 0x6c, 0x6f, 0x63, 0x61, 0x6c, 0x2f, 0x73, 0x65,
	0x6e, 0x73, 0x69, 0x74, 0x69, 0x76, 0x65, 0x2f, 0x69, 0x6e, 0x6e, 0x65, 0x72, 0x70, 0x62, 0x2f,
	0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x2f, 0x67, 0x72, 0x70, 0x63, 0x62,
	0x08, 0x65, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x70, 0xe8, 0x07,
})

var (
	file_inner_processing_grpc_transaction_type_proto_rawDescOnce sync.Once
	file_inner_processing_grpc_transaction_type_proto_rawDescData []byte
)

func file_inner_processing_grpc_transaction_type_proto_rawDescGZIP() []byte {
	file_inner_processing_grpc_transaction_type_proto_rawDescOnce.Do(func() {
		file_inner_processing_grpc_transaction_type_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_inner_processing_grpc_transaction_type_proto_rawDesc), len(file_inner_processing_grpc_transaction_type_proto_rawDesc)))
	})
	return file_inner_processing_grpc_transaction_type_proto_rawDescData
}

var file_inner_processing_grpc_transaction_type_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_inner_processing_grpc_transaction_type_proto_msgTypes = make([]protoimpl.MessageInfo, 9)
var file_inner_processing_grpc_transaction_type_proto_goTypes = []any{
	(EnumTransactionType)(0),                             // 0: processing.transaction.transaction_type.EnumTransactionType
	(*TransactionTypeRef)(nil),                           // 1: processing.transaction.transaction_type.TransactionTypeRef
	(*TransactionTypeResponseV1)(nil),                    // 2: processing.transaction.transaction_type.TransactionTypeResponseV1
	(*TransactionTypesResponseV1)(nil),                   // 3: processing.transaction.transaction_type.TransactionTypesResponseV1
	(*GetAggregatedTransactionTypeRequestV1)(nil),        // 4: processing.transaction.transaction_type.GetAggregatedTransactionTypeRequestV1
	(*GetAggregatedTransactionTypeResponseV1)(nil),       // 5: processing.transaction.transaction_type.GetAggregatedTransactionTypeResponseV1
	(*GetAggregatedTypeByIDRequestV1)(nil),               // 6: processing.transaction.transaction_type.GetAggregatedTypeByIDRequestV1
	(*GetAggregatedTypeByIDResponseV1)(nil),              // 7: processing.transaction.transaction_type.GetAggregatedTypeByIDResponseV1
	(*GetAggregatedTransactionTypeByTypeIDRequest)(nil),  // 8: processing.transaction.transaction_type.GetAggregatedTransactionTypeByTypeIDRequest
	(*GetAggregatedTransactionTypeByTypeIDResponse)(nil), // 9: processing.transaction.transaction_type.GetAggregatedTransactionTypeByTypeIDResponse
	(*descriptorpb.EnumValueOptions)(nil),                // 10: google.protobuf.EnumValueOptions
	(*descriptorpb.EnumOptions)(nil),                     // 11: google.protobuf.EnumOptions
	(*emptypb.Empty)(nil),                                // 12: google.protobuf.Empty
}
var file_inner_processing_grpc_transaction_type_proto_depIdxs = []int32{
	3,  // 0: processing.transaction.transaction_type.TransactionTypeResponseV1.data:type_name -> processing.transaction.transaction_type.TransactionTypesResponseV1
	10, // 1: processing.transaction.transaction_type.transaction_type_value:extendee -> google.protobuf.EnumValueOptions
	11, // 2: processing.transaction.transaction_type.default_transaction_type_value:extendee -> google.protobuf.EnumOptions
	1,  // 3: processing.transaction.transaction_type.transaction_type_value:type_name -> processing.transaction.transaction_type.TransactionTypeRef
	1,  // 4: processing.transaction.transaction_type.default_transaction_type_value:type_name -> processing.transaction.transaction_type.TransactionTypeRef
	12, // 5: processing.transaction.transaction_type.TransactionType.GetAll:input_type -> google.protobuf.Empty
	12, // 6: processing.transaction.transaction_type.TransactionType.GetTransactionPayOutTypes:input_type -> google.protobuf.Empty
	12, // 7: processing.transaction.transaction_type.TransactionType.GetTransactionPayInTypes:input_type -> google.protobuf.Empty
	4,  // 8: processing.transaction.transaction_type.TransactionType.GetAggregatedTransactionType:input_type -> processing.transaction.transaction_type.GetAggregatedTransactionTypeRequestV1
	6,  // 9: processing.transaction.transaction_type.TransactionType.GetAggregatedTypeByID:input_type -> processing.transaction.transaction_type.GetAggregatedTypeByIDRequestV1
	8,  // 10: processing.transaction.transaction_type.TransactionType.GetAggregatedTransactionTypeByTypeID:input_type -> processing.transaction.transaction_type.GetAggregatedTransactionTypeByTypeIDRequest
	2,  // 11: processing.transaction.transaction_type.TransactionType.GetAll:output_type -> processing.transaction.transaction_type.TransactionTypeResponseV1
	2,  // 12: processing.transaction.transaction_type.TransactionType.GetTransactionPayOutTypes:output_type -> processing.transaction.transaction_type.TransactionTypeResponseV1
	2,  // 13: processing.transaction.transaction_type.TransactionType.GetTransactionPayInTypes:output_type -> processing.transaction.transaction_type.TransactionTypeResponseV1
	5,  // 14: processing.transaction.transaction_type.TransactionType.GetAggregatedTransactionType:output_type -> processing.transaction.transaction_type.GetAggregatedTransactionTypeResponseV1
	7,  // 15: processing.transaction.transaction_type.TransactionType.GetAggregatedTypeByID:output_type -> processing.transaction.transaction_type.GetAggregatedTypeByIDResponseV1
	9,  // 16: processing.transaction.transaction_type.TransactionType.GetAggregatedTransactionTypeByTypeID:output_type -> processing.transaction.transaction_type.GetAggregatedTransactionTypeByTypeIDResponse
	11, // [11:17] is the sub-list for method output_type
	5,  // [5:11] is the sub-list for method input_type
	3,  // [3:5] is the sub-list for extension type_name
	1,  // [1:3] is the sub-list for extension extendee
	0,  // [0:1] is the sub-list for field type_name
}

func init() { file_inner_processing_grpc_transaction_type_proto_init() }
func file_inner_processing_grpc_transaction_type_proto_init() {
	if File_inner_processing_grpc_transaction_type_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_inner_processing_grpc_transaction_type_proto_rawDesc), len(file_inner_processing_grpc_transaction_type_proto_rawDesc)),
			NumEnums:      1,
			NumMessages:   9,
			NumExtensions: 2,
			NumServices:   1,
		},
		GoTypes:           file_inner_processing_grpc_transaction_type_proto_goTypes,
		DependencyIndexes: file_inner_processing_grpc_transaction_type_proto_depIdxs,
		EnumInfos:         file_inner_processing_grpc_transaction_type_proto_enumTypes,
		MessageInfos:      file_inner_processing_grpc_transaction_type_proto_msgTypes,
		ExtensionInfos:    file_inner_processing_grpc_transaction_type_proto_extTypes,
	}.Build()
	File_inner_processing_grpc_transaction_type_proto = out.File
	file_inner_processing_grpc_transaction_type_proto_goTypes = nil
	file_inner_processing_grpc_transaction_type_proto_depIdxs = nil
}
