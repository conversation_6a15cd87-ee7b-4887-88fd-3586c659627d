-- +goose Up
-- +goose StatementBegin
ALTER TABLE "account"."account_balance_history"
    ADD COLUMN account_id integer;
ALTER TABLE "account"."bcc_account_statement"
    ADD COLUMN account_id integer;

ALTER TABLE "account"."account_balance_history"
    ADD CONSTRAINT fk_account_id
        FOREIGN KEY (account_id)
            REFERENCES "account"."accounts" (id);

ALTER TABLE "account"."bcc_account_statement"
    ADD CONSTRAINT fk_account_id
        FOREIGN KEY (account_id)
            REFERENCES "account"."accounts" (id);
-- +goose StatementEnd

-- +goose Down
-- +goose StatementBegin
-- ALTER TABLE "account"."account_balance_history" DROP COLUMN IF EXISTS "account_id";
-- ALTER TABLE "account"."account_balance_history" DROP CONSTRAINT IF EXISTS fk_account_id;
-- ALTER TABLE "account"."bcc_account_statement" DROP COLUMN IF EXISTS "account_id";
-- ALTER TABLE "account"."bcc_account_statement" DROP CONSTRAINT IF EXISTS fk_account_id;
-- +goose StatementEnd