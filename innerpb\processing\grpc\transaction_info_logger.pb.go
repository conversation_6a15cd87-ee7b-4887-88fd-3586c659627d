// Code generated by protoc-gen-go-mvp. DO NOT EDIT.
// versions:
// - protoc-gen-go-mvp v2.0.1
// created by mvp authors

package grpc

import (
	context "context"
	fmt "fmt"
	cntx "git.local/sensitive/mvp/pkg/cntx"
	ctxzap "github.com/grpc-ecosystem/go-grpc-middleware/logging/zap/ctxzap"
	zap "go.uber.org/zap"
	grpc "google.golang.org/grpc"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
	structpb "google.golang.org/protobuf/types/known/structpb"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
)

func file_inner_processing_grpc_transaction_info_proto_string_structpb_Value(
	label string,
	in map[string]*structpb.Value,
) zap.Field {
	var res []zap.Field
	for key, val := range in {
		res = append(res, file_inner_processing_grpc_transaction_info_proto_message_ValueToZap(fmt.Sprint(key), val))
	}

	return zap.Dict(label, res...)
}

func file_inner_processing_grpc_transaction_info_proto_message_EmptyToZap(
	label string,
	in *emptypb.Empty,
) zap.Field {
	if in == nil {
		return zap.Reflect(label, in)
	}

	return zap.Dict(
		label,
	)
}

func file_inner_processing_grpc_transaction_info_proto_message_ListValueToZap(
	label string,
	in *structpb.ListValue,
) zap.Field {
	if in == nil {
		return zap.Reflect(label, in)
	}

	return zap.Dict(
		label,
		file_inner_processing_grpc_transaction_info_proto_message_ValueSliceToZap("Values", in.GetValues()),
	)
}

func file_inner_processing_grpc_transaction_info_proto_enum_NullValueToZap(
	label string,
	in structpb.NullValue,
) zap.Field {
	str, ok := structpb.NullValue_name[int32(in)]
	if !ok {
		str = fmt.Sprintf("unknown structpb.NullValue value %d", in)
	}

	return zap.Dict(
		label,
		zap.Any("value", structpb.NullValue(in)),
		zap.Any("name", str),
	)
}

func file_inner_processing_grpc_transaction_info_proto_message_StructToZap(
	label string,
	in *structpb.Struct,
) zap.Field {
	if in == nil {
		return zap.Reflect(label, in)
	}

	return zap.Dict(
		label,
		file_inner_processing_grpc_transaction_info_proto_string_structpb_Value("Fields", in.GetFields()),
	)
}

func file_inner_processing_grpc_transaction_info_proto_message_TimestampToZap(
	label string,
	in *timestamppb.Timestamp,
) zap.Field {
	if in == nil {
		return zap.Reflect(label, in)
	}

	return zap.Dict(
		label,
		zap.Any("Seconds", in.GetSeconds()),
		zap.Any("Nanos", in.GetNanos()),
	)
}

func file_inner_processing_grpc_transaction_info_proto_message_ValueToZap(
	label string,
	in *structpb.Value,
) zap.Field {
	if in == nil {
		return zap.Reflect(label, in)
	}

	return zap.Dict(
		label,
		file_inner_processing_grpc_transaction_info_proto_enum_NullValueToZap("NullValue", in.GetNullValue()),
		zap.Any("NumberValue", in.GetNumberValue()),
		zap.Any("StringValue", in.GetStringValue()),
		zap.Any("BoolValue", in.GetBoolValue()),
		file_inner_processing_grpc_transaction_info_proto_message_StructToZap("StructValue", in.GetStructValue()),
		file_inner_processing_grpc_transaction_info_proto_message_ListValueToZap("ListValue", in.GetListValue()),
	)
}

func file_inner_processing_grpc_transaction_info_proto_message_ValueSliceToZap(
	label string,
	in []*structpb.Value,
) zap.Field {
	var res []zap.Field
	for index, val := range in {
		res = append(res, file_inner_processing_grpc_transaction_info_proto_message_ValueToZap(fmt.Sprint(index), val))
	}

	return zap.Dict(label, res...)
}

func file_inner_processing_grpc_transaction_info_proto_message_TransactionDataV1ToZap(
	label string,
	in *TransactionDataV1,
) zap.Field {
	if in == nil {
		return zap.Reflect(label, in)
	}

	return zap.Dict(
		label,
		file_inner_processing_grpc_transaction_info_proto_message_TimestampToZap("CreatedAt", in.GetCreatedAt()),
		file_inner_processing_grpc_transaction_info_proto_message_TimestampToZap("UpdatedAt", in.GetUpdatedAt()),
		file_inner_processing_grpc_transaction_info_proto_message_TimestampToZap("FinishedAt", in.GetFinishedAt()),
		zap.Any("Id", in.GetId()),
		zap.Any("AcquirerId", in.GetAcquirerId()),
		zap.Any("TerminalId", in.GetTerminalId()),
		zap.Any("BankReferenceId", in.GetBankReferenceId()),
		zap.Any("CardId", in.GetCardId()),
		zap.Any("ProjectId", in.GetProjectId()),
		zap.Any("MerchantId", in.GetMerchantId()),
		zap.Any("ProjectReferenceId", in.GetProjectReferenceId()),
		zap.Any("ProjectClientId", in.GetProjectClientId()),
		zap.Any("StatusId", in.GetStatusId()),
		zap.Any("TypeId", in.GetTypeId()),
		zap.Any("Amount", in.GetAmount()),
		zap.Any("Try", in.GetTry()),
		zap.Any("Description", in.GetDescription()),
		zap.Any("TypeCode", in.GetTypeCode()),
		zap.Any("StatusCode", in.GetStatusCode()),
		zap.Any("IsFinalStatus", in.GetIsFinalStatus()),
		file_inner_processing_grpc_transaction_info_proto_message_TransactionInfoToZap("TransactionInfo", in.GetTransactionInfo()),
		zap.Any("AggregatedTypeId", in.GetAggregatedTypeId()),
	)
}

func file_inner_processing_grpc_transaction_info_proto_message_TransactionDataV1SliceToZap(
	label string,
	in []*TransactionDataV1,
) zap.Field {
	var res []zap.Field
	for index, val := range in {
		res = append(res, file_inner_processing_grpc_transaction_info_proto_message_TransactionDataV1ToZap(fmt.Sprint(index), val))
	}

	return zap.Dict(label, res...)
}

func file_inner_processing_grpc_transaction_info_proto_message_TransactionInfoToZap(
	label string,
	in *TransactionInfo,
) zap.Field {
	if in == nil {
		return zap.Reflect(label, in)
	}

	return zap.Dict(
		label,
		file_inner_processing_grpc_transaction_info_proto_message_TimestampToZap("CreatedAt", in.GetCreatedAt()),
		file_inner_processing_grpc_transaction_info_proto_message_TimestampToZap("UpdatedAt", in.GetUpdatedAt()),
		zap.Any("TransactionId", in.GetTransactionId()),
		file_inner_processing_grpc_transaction_info_proto_message_TimestampToZap("TransactionCreatedAt", in.GetTransactionCreatedAt()),
		zap.Any("CallbackUrl", in.GetCallbackUrl()),
		zap.Any("ConfirmUrl", in.GetConfirmUrl()),
		zap.Any("SuccessRedirectUrl", in.GetSuccessRedirectUrl()),
		zap.Any("FailureRedirectUrl", in.GetFailureRedirectUrl()),
		zap.Any("CallbackStatus", in.GetCallbackStatus()),
		zap.Any("UserPhone", in.GetUserPhone()),
		zap.Any("UserEmail", in.GetUserEmail()),
		zap.Any("Shipment", in.GetShipment()),
		zap.Any("BankOrderId", in.GetBankOrderId()),
		file_inner_processing_grpc_transaction_info_proto_message_StructToZap("JobsMessage", in.GetJobsMessage()),
	)
}

func file_inner_processing_grpc_transaction_info_proto_message_GetTransactionsWithEmptyBankReferenceIDResponseV1ToZap(
	label string,
	in *GetTransactionsWithEmptyBankReferenceIDResponseV1,
) zap.Field {
	if in == nil {
		return zap.Reflect(label, in)
	}

	return zap.Dict(
		label,
		file_inner_processing_grpc_transaction_info_proto_message_TransactionDataV1SliceToZap("TransactionIds", in.GetTransactionIds()),
	)
}

func file_inner_processing_grpc_transaction_info_proto_message_UpdateBankReferenceIDRequestV1ToZap(
	label string,
	in *UpdateBankReferenceIDRequestV1,
) zap.Field {
	if in == nil {
		return zap.Reflect(label, in)
	}

	return zap.Dict(
		label,
		zap.Any("TransactionId", in.GetTransactionId()),
		zap.Any("BankReferenceId", in.GetBankReferenceId()),
	)
}

func file_inner_processing_grpc_transaction_info_proto_message_UpdateBankResponseMessageRequestV1ToZap(
	label string,
	in *UpdateBankResponseMessageRequestV1,
) zap.Field {
	if in == nil {
		return zap.Reflect(label, in)
	}

	return zap.Dict(
		label,
		zap.Any("TransactionId", in.GetTransactionId()),
		zap.Any("BankCode", in.GetBankCode()),
		zap.Any("BankMessage", in.GetBankMessage()),
		zap.Any("IntegrationErrorCode", in.GetIntegrationErrorCode()),
		zap.Any("IntegrationErrorMessage", in.GetIntegrationErrorMessage()),
	)
}

func file_inner_processing_grpc_transaction_info_proto_message_UpdateJobsMessageRequestV1ToZap(
	label string,
	in *UpdateJobsMessageRequestV1,
) zap.Field {
	if in == nil {
		return zap.Reflect(label, in)
	}

	return zap.Dict(
		label,
		zap.Any("TransactionId", in.GetTransactionId()),
		file_inner_processing_grpc_transaction_info_proto_message_StructToZap("JobsMessage", in.GetJobsMessage()),
	)
}

var _ TransactionInfServer = (*loggedTransactionInfServer)(nil)

func NewLoggedTransactionInfServer(srv TransactionInfServer) TransactionInfServer {
	return &loggedTransactionInfServer{srv: srv}
}

type loggedTransactionInfServer struct {
	UnimplementedTransactionInfServer

	srv TransactionInfServer
}

func (s *loggedTransactionInfServer) UpdateJobsMessage(
	ctx context.Context,
	request *UpdateJobsMessageRequestV1,
) (
	response *emptypb.Empty,
	err error,
) {
	label := cntx.Begin(ctx, "TransactionInfServer_UpdateJobsMessage")
	defer cntx.End(ctx, label)

	defer func() {
		if err != nil {
			ctxzap.AddFields(ctx, zap.Any(label+"error", err))
			return
		}

		ctxzap.AddFields(
			ctx,
			file_inner_processing_grpc_transaction_info_proto_message_EmptyToZap(label+"response", response),
		)
	}()

	ctxzap.AddFields(
		ctx,
		file_inner_processing_grpc_transaction_info_proto_message_UpdateJobsMessageRequestV1ToZap(label+"request", request),
	)

	response, err = s.srv.UpdateJobsMessage(ctx, request)

	return
}

func (s *loggedTransactionInfServer) UpdateBankResponseMessage(
	ctx context.Context,
	request *UpdateBankResponseMessageRequestV1,
) (
	response *emptypb.Empty,
	err error,
) {
	label := cntx.Begin(ctx, "TransactionInfServer_UpdateBankResponseMessage")
	defer cntx.End(ctx, label)

	defer func() {
		if err != nil {
			ctxzap.AddFields(ctx, zap.Any(label+"error", err))
			return
		}

		ctxzap.AddFields(
			ctx,
			file_inner_processing_grpc_transaction_info_proto_message_EmptyToZap(label+"response", response),
		)
	}()

	ctxzap.AddFields(
		ctx,
		file_inner_processing_grpc_transaction_info_proto_message_UpdateBankResponseMessageRequestV1ToZap(label+"request", request),
	)

	response, err = s.srv.UpdateBankResponseMessage(ctx, request)

	return
}

func (s *loggedTransactionInfServer) GetTransactionsWithEmptyBankReferenceID(
	ctx context.Context,
	request *emptypb.Empty,
) (
	response *GetTransactionsWithEmptyBankReferenceIDResponseV1,
	err error,
) {
	label := cntx.Begin(ctx, "TransactionInfServer_GetTransactionsWithEmptyBankReferenceID")
	defer cntx.End(ctx, label)

	defer func() {
		if err != nil {
			ctxzap.AddFields(ctx, zap.Any(label+"error", err))
			return
		}

		ctxzap.AddFields(
			ctx,
			file_inner_processing_grpc_transaction_info_proto_message_GetTransactionsWithEmptyBankReferenceIDResponseV1ToZap(label+"response", response),
		)
	}()

	ctxzap.AddFields(
		ctx,
		file_inner_processing_grpc_transaction_info_proto_message_EmptyToZap(label+"request", request),
	)

	response, err = s.srv.GetTransactionsWithEmptyBankReferenceID(ctx, request)

	return
}

func (s *loggedTransactionInfServer) UpdateBankReferenceID(
	ctx context.Context,
	request *UpdateBankReferenceIDRequestV1,
) (
	response *emptypb.Empty,
	err error,
) {
	label := cntx.Begin(ctx, "TransactionInfServer_UpdateBankReferenceID")
	defer cntx.End(ctx, label)

	defer func() {
		if err != nil {
			ctxzap.AddFields(ctx, zap.Any(label+"error", err))
			return
		}

		ctxzap.AddFields(
			ctx,
			file_inner_processing_grpc_transaction_info_proto_message_EmptyToZap(label+"response", response),
		)
	}()

	ctxzap.AddFields(
		ctx,
		file_inner_processing_grpc_transaction_info_proto_message_UpdateBankReferenceIDRequestV1ToZap(label+"request", request),
	)

	response, err = s.srv.UpdateBankReferenceID(ctx, request)

	return
}

var _ TransactionInfClient = (*loggedTransactionInfClient)(nil)

func NewLoggedTransactionInfClient(client TransactionInfClient) TransactionInfClient {
	return &loggedTransactionInfClient{client: client}
}

type loggedTransactionInfClient struct {
	client TransactionInfClient
}

func (s *loggedTransactionInfClient) UpdateJobsMessage(
	ctx context.Context,
	request *UpdateJobsMessageRequestV1,
	opts ...grpc.CallOption,
) (
	response *emptypb.Empty,
	err error,
) {
	label := cntx.Begin(ctx, "TransactionInfClient_UpdateJobsMessage")
	defer cntx.End(ctx, label)

	defer func() {
		if err != nil {
			ctxzap.AddFields(ctx, zap.Any(label+"error", err))
			return
		}

		ctxzap.AddFields(
			ctx,
			file_inner_processing_grpc_transaction_info_proto_message_EmptyToZap(label+"response", response),
		)
	}()

	ctxzap.AddFields(
		ctx,
		file_inner_processing_grpc_transaction_info_proto_message_UpdateJobsMessageRequestV1ToZap(label+"request", request),
	)

	response, err = s.client.UpdateJobsMessage(ctx, request, opts...)

	return
}

func (s *loggedTransactionInfClient) UpdateBankResponseMessage(
	ctx context.Context,
	request *UpdateBankResponseMessageRequestV1,
	opts ...grpc.CallOption,
) (
	response *emptypb.Empty,
	err error,
) {
	label := cntx.Begin(ctx, "TransactionInfClient_UpdateBankResponseMessage")
	defer cntx.End(ctx, label)

	defer func() {
		if err != nil {
			ctxzap.AddFields(ctx, zap.Any(label+"error", err))
			return
		}

		ctxzap.AddFields(
			ctx,
			file_inner_processing_grpc_transaction_info_proto_message_EmptyToZap(label+"response", response),
		)
	}()

	ctxzap.AddFields(
		ctx,
		file_inner_processing_grpc_transaction_info_proto_message_UpdateBankResponseMessageRequestV1ToZap(label+"request", request),
	)

	response, err = s.client.UpdateBankResponseMessage(ctx, request, opts...)

	return
}

func (s *loggedTransactionInfClient) GetTransactionsWithEmptyBankReferenceID(
	ctx context.Context,
	request *emptypb.Empty,
	opts ...grpc.CallOption,
) (
	response *GetTransactionsWithEmptyBankReferenceIDResponseV1,
	err error,
) {
	label := cntx.Begin(ctx, "TransactionInfClient_GetTransactionsWithEmptyBankReferenceID")
	defer cntx.End(ctx, label)

	defer func() {
		if err != nil {
			ctxzap.AddFields(ctx, zap.Any(label+"error", err))
			return
		}

		ctxzap.AddFields(
			ctx,
			file_inner_processing_grpc_transaction_info_proto_message_GetTransactionsWithEmptyBankReferenceIDResponseV1ToZap(label+"response", response),
		)
	}()

	ctxzap.AddFields(
		ctx,
		file_inner_processing_grpc_transaction_info_proto_message_EmptyToZap(label+"request", request),
	)

	response, err = s.client.GetTransactionsWithEmptyBankReferenceID(ctx, request, opts...)

	return
}

func (s *loggedTransactionInfClient) UpdateBankReferenceID(
	ctx context.Context,
	request *UpdateBankReferenceIDRequestV1,
	opts ...grpc.CallOption,
) (
	response *emptypb.Empty,
	err error,
) {
	label := cntx.Begin(ctx, "TransactionInfClient_UpdateBankReferenceID")
	defer cntx.End(ctx, label)

	defer func() {
		if err != nil {
			ctxzap.AddFields(ctx, zap.Any(label+"error", err))
			return
		}

		ctxzap.AddFields(
			ctx,
			file_inner_processing_grpc_transaction_info_proto_message_EmptyToZap(label+"response", response),
		)
	}()

	ctxzap.AddFields(
		ctx,
		file_inner_processing_grpc_transaction_info_proto_message_UpdateBankReferenceIDRequestV1ToZap(label+"request", request),
	)

	response, err = s.client.UpdateBankReferenceID(ctx, request, opts...)

	return
}
