edition = "2023";

package processing.jusan_statuser.jusan_statuser;

option go_package = "git.local/sensitive/innerpb/processing/grpc";

import "inner/processing/grpc/transaction_status.proto";
import "inner/processing/grpc/integration.proto";
import "mvp/proto/refs.proto";
import "mvp/proto/enum.proto";
import "google/protobuf/descriptor.proto";

message JusanResponseCodeStatuserRef {
  string code = 1;
  string message = 2;
  transaction.transaction_status.EnumTransactionStatus transaction_status = 3;
  integration.integration.IntegrationError integration_error = 4;
}

extend google.protobuf.EnumValueOptions {
  JusanResponseCodeStatuserRef jusan_response_code_statuser_value = 100115;
}

extend google.protobuf.EnumOptions {
  JusanResponseCodeStatuserRef default_jusan_response_code_statuser_value = 100116;
}

enum JusanResponseCodeStatuser {
  option(mvp.default_ref) = "default_jusan_response_code_statuser_value";
  option(mvp.ref) = "jusan_response_code_statuser_value";
  option(default_jusan_response_code_statuser_value) = {
    code: "0"
    message: "default"
    transaction_status: TransactionStatusError
    integration_error: None
  };

  StatuserServiceUnavailable = 0 [(jusan_response_code_statuser_value) = {
    code: "11"
    transaction_status: TransactionStatusFailed
    integration_error: UnavailableAcquirer
    message: "Сервис временно недоступен, попробуйте позже"
  }, (mvp.from_string) = "11"];

  StatuserIncorrectFieldOrder = 1 [(jusan_response_code_statuser_value) = {
    code: "12"
    transaction_status: TransactionStatusFailed
    integration_error: TransactionDeclinedByAcquirer
    message: "Неправильное значение в поле ORDER:"
  }, (mvp.from_string) = "12"];

  StatuserIncorrectAmount = 2 [(jusan_response_code_statuser_value) = {
    code: "13"
    transaction_status: TransactionStatusFailed
    integration_error: TransactionDeclinedByAcquirer
    message: "Неправильная сумма: "
  }, (mvp.from_string) = "13"];

  StatuserIncorrectCurrency = 3 [(jusan_response_code_statuser_value) = {
    code: "14"
    transaction_status: TransactionStatusFailed
    integration_error: TransactionDeclinedByAcquirer
    message: "Неправильная валюта:"
  }, (mvp.from_string) = "14"];

  StatuserNoSuchCard = 4 [(jusan_response_code_statuser_value) = {
    code: "15"
    transaction_status: TransactionStatusFailed
    integration_error: InvalidCard
    message: "No such card"
  }, (mvp.from_string) = "15"];

  StatuserDbUnavailable = 5 [(jusan_response_code_statuser_value) = {
    code: "16"
    transaction_status: TransactionStatusFailed
    integration_error: UnavailableAcquirer
    message: "Сервис Db временно недоступен, попробуйте позже"
  }, (mvp.from_string) = "16"];

  StatuserForbiddenForMerchant = 6 [(jusan_response_code_statuser_value) = {
    code: "171"
    transaction_status: TransactionStatusFailed
    integration_error: PaymentForbiddenForMerchant
    message: "Коммерсанту запрещено выполнение операций"
  }, (mvp.from_string) = "171"];

  StatuserForbiddenMerchantByLaw = 7 [(jusan_response_code_statuser_value) = {
    code: "172 "
    transaction_status: TransactionStatusFailed
    integration_error: PaymentForbiddenForMerchant
    message: "Коммерсанту запрещено проведение операций в соответствии с Законом о ПОД/ФТ"
  }, (mvp.from_string) = "172 "];

  StatuserRequestAlreadeCompleted = 8 [(jusan_response_code_statuser_value) = {
    code: "18"
    transaction_status: TransactionStatusFailed
    integration_error: TransactionDeclinedByAcquirer
    message: "Запрос уже выполнялся"
  }, (mvp.from_string) = "18"];

  StatuserIncorrectCardExpDate = 9 [(jusan_response_code_statuser_value) = {
    code: "19"
    transaction_status: TransactionStatusFailed
    integration_error: IncorrectCardExpDate
    message: "Неправильная дата дейстия карты (MM/ГГ)"
  }, (mvp.from_string) = "19"];

  StatuserIncorrectFieldTerminal= 10 [(jusan_response_code_statuser_value) = {
    code: "20"
    transaction_status: TransactionStatusFailed
    integration_error: TransactionDeclinedByAcquirer
    message: "Неправильное значение в поле TERMINAL:"
  }, (mvp.from_string) = "20"];

  StatuserIncorrectSign = 11 [(jusan_response_code_statuser_value) = {
    code: "21"
    transaction_status: TransactionStatusFailed
    integration_error: TransactionDeclinedByAcquirer
    message: "Неправильная подпись!"
  }, (mvp.from_string) = "21"];

  StatuserCurrencyNotFound = 12 [(jusan_response_code_statuser_value) = {
    code: "22"
    transaction_status: TransactionStatusFailed
    integration_error: TransactionDeclinedByAcquirer
    message: "Не найден курс валюты"
  }, (mvp.from_string) = "22"];

  StatuserLimitExceeded = 13 [(jusan_response_code_statuser_value) = {
    code: "23"
    transaction_status: TransactionStatusFailed
    integration_error: ExceedsAmountLimit
    message: "Превышен лимит!"
  }, (mvp.from_string) = "23"];

  StatuserEmptyField = 14 [(jusan_response_code_statuser_value) = {
    code: "24"
    transaction_status: TransactionStatusFailed
    integration_error: TransactionDeclinedByAcquirer
    message: "Не указано значение в поле"
  }, (mvp.from_string) = "24"];

  StatuserSizeLessSymbol = 15 [(jusan_response_code_statuser_value) = {
    code: "25"
    transaction_status: TransactionStatusFailed
    integration_error: TransactionDeclinedByAcquirer
    message: "Размер значения в поле менее симоволов"
  }, (mvp.from_string) = "25"];

  StatuserSizeMoreSymbol = 16 [(jusan_response_code_statuser_value) = {
    code: "26"
    transaction_status: TransactionStatusFailed
    integration_error: TransactionDeclinedByAcquirer
    message: "Размер значения в поле больше симоволов"
  }, (mvp.from_string) = "26"];

  StatuserInvalidValue = 17 [(jusan_response_code_statuser_value) = {
    code: "27"
    transaction_status: TransactionStatusFailed
    integration_error: TransactionDeclinedByAcquirer
    message: "Введите валидное значение в поле"
  }, (mvp.from_string) = "27"];

  StatuserMPIError3DS = 18 [(jusan_response_code_statuser_value) = {
    code: "28"
    transaction_status: TransactionStatusFailed
    integration_error: ThreeDSAuthFailed
    message: "Ошибка MPI при выполнении проверки 3DS:"
  }, (mvp.from_string) = "28"];

  StatuserInvalidCardType = 19 [(jusan_response_code_statuser_value) = {
    code: "29"
    transaction_status: TransactionStatusFailed
    integration_error: InvalidCard
    message: "Недопустимый тип карты"
  }, (mvp.from_string) = "29"];

  StatuserPaymentNotFound = 20 [(jusan_response_code_statuser_value) = {
    code: "30"
    transaction_status: TransactionStatusFailed
    integration_error: TransactionDeclinedByAcquirer
    message: "Счет на оплату не найден"
  }, (mvp.from_string) = "30"];

  StatuserClientKeyNotFound = 21 [(jusan_response_code_statuser_value) = {
    code: "31"
    transaction_status: TransactionStatusFailed
    integration_error: TransactionDeclinedByAcquirer
    message: "Не передан ключ указанного клиента"
  }, (mvp.from_string) = "31"];

  StatuserForbiddenTerminal = 22 [(jusan_response_code_statuser_value) = {
    code: "32"
    transaction_status: TransactionStatusFailed
    integration_error: PaymentForbiddenForMerchant
    message: "Для терминала запрещена токенизация"
  }, (mvp.from_string) = "32"];

  StatuserTokenNotFound = 23 [(jusan_response_code_statuser_value) = {
    code: "33"
    transaction_status: TransactionStatusFailed
    integration_error: InvalidCard
    message: "Для данного клиента в вашей организации не зарегистрирован токен"
  }, (mvp.from_string) = "33"];

  StatuserIncorrectBlockAmount = 24 [(jusan_response_code_statuser_value) = {
    code: "34"
    transaction_status: TransactionStatusFailed
    integration_error: TransactionDeclinedByAcquirer
    message: "Неверная сумма блокирования, заявка отменена!"
  }, (mvp.from_string) = "34"];

  StatuserUnknownError = 25 [(jusan_response_code_statuser_value) = {
    code: "99"
    transaction_status: TransactionStatusHolded
    integration_error: TransactionDeclinedByAcquirer
    message: "Неизвестная ошибка: "
  }, (mvp.from_string) = "99"];

  StatuserUnavailableService = 26 [(jusan_response_code_statuser_value) = {
    code: "41"
    transaction_status: TransactionStatusFailed
    integration_error: UnavailableAcquirer
    message: "Сервис временно недоступен, попробуйте позже"
  }, (mvp.from_string) = "41"];

  StatuserSumIncorrect = 27 [(jusan_response_code_statuser_value) = {
    code: "42"
    transaction_status: TransactionStatusFailed
    integration_error: TransactionDeclinedByAcquirer
    message: "Неправильная сумма"
  }, (mvp.from_string) = "42"];

  StatuserUnavailableDb = 28 [(jusan_response_code_statuser_value) = {
    code: "43"
    transaction_status: TransactionStatusFailed
    integration_error: UnavailableAcquirer
    message: "Сервис Db временно недоступен, попробуйте позже "
  }, (mvp.from_string) = "43"];

  StatuserIncorrectMerchant = 29 [(jusan_response_code_statuser_value) = {
    code: "44"
    transaction_status: TransactionStatusFailed
    integration_error: TransactionDeclinedByAcquirer
    message: "Неправильное значение в поле MERCHANT"
  }, (mvp.from_string) = "44"];

  StatuserNotFoundMerchant = 30 [(jusan_response_code_statuser_value) = {
    code: "17"
    transaction_status: TransactionStatusFailed
    integration_error: PaymentForbiddenForMerchant
    message: "Коммерсант не найден"
  }, (mvp.from_string) = "17"];

  StatuserOrderNotFound = 31 [(jusan_response_code_statuser_value) = {
    code: "45"
    transaction_status: TransactionStatusFailed
    integration_error: TransactionDeclinedByAcquirer
    message: "Заявка ORDER не найдена"
  }, (mvp.from_string) = "45"];

  StatuserInvalidSign = 32 [(jusan_response_code_statuser_value) = {
    code: "46"
    transaction_status: TransactionStatusFailed
    integration_error: TransactionDeclinedByAcquirer
    message: "Неправильная подпись!"
  }, (mvp.from_string) = "46"];

  StatuserIncorrectRefundAmount = 33 [(jusan_response_code_statuser_value) = {
    code: "47 "
    transaction_status: TransactionStatusFailed
    integration_error: TransactionDeclinedByAcquirer
    message: "Сумма возврта '%s' больше чем сумма заказа"
  }, (mvp.from_string) = "47 "];

  StatuserIncorrectStatus = 34 [(jusan_response_code_statuser_value) = {
    code: "48"
    transaction_status: TransactionStatusFailed
    integration_error: TransactionDeclinedByAcquirer
    message: "Текущий статус заказа не позволяет делать возврат/отмену"
  }, (mvp.from_string) = "48"];

  StatuserIncorrectValue = 35 [(jusan_response_code_statuser_value) = {
    code: "50"
    transaction_status: TransactionStatusFailed
    integration_error: TransactionDeclinedByAcquirer
    message: "Неправильное значение"
  }, (mvp.from_string) = "50"];

  StatuserTerminalStatusIncorrect = 36 [(jusan_response_code_statuser_value) = {
    code: "51"
    transaction_status: TransactionStatusFailed
    integration_error: PaymentForbiddenForMerchant
    message: "Текущий статус терминала не позволяет производить операции"
  }, (mvp.from_string) = "51"];

  StatuserTerminalForbidden = 37 [(jusan_response_code_statuser_value) = {
    code: "52"
    transaction_status: TransactionStatusFailed
    integration_error: PaymentForbiddenForMerchant
    message: "Операция отмены/возврата через API для терминала запрещена"
  }, (mvp.from_string) = "52"];

  StatuserDuplicateDescription = 38 [(jusan_response_code_statuser_value) = {
    code: "53"
    transaction_status: TransactionStatusFailed
    integration_error: TransactionDeclinedByAcquirer
    message: "Дублирование описания отмены"
  }, (mvp.from_string) = "53"];

  StatuserRefundHandleError = 39 [(jusan_response_code_statuser_value) = {
    code: "F"
    transaction_status: TransactionStatusFailed
    integration_error: TransactionDeclinedByAcquirer
    message: "Ошибка при обработке возврата"
  }, (mvp.from_string) = "F"];

  StatuserPaymentError = 40 [(jusan_response_code_statuser_value) = {
    code: "E"
    transaction_status: TransactionStatusFailed
    integration_error: TransactionDeclinedByAcquirer
    message: "Ошибка при оплате"
  }, (mvp.from_string) = "E"];

  StatuserPaymentExpired = 41 [(jusan_response_code_statuser_value) = {
    code: "c"
    transaction_status: TransactionStatusFailed
    integration_error: TransactionDeclinedByAcquirer
    message: "Счет на оплату устарел"
  }, (mvp.from_string) = "c"];
}
