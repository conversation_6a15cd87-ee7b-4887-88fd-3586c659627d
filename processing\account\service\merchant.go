package service

import (
	"context"

	"git.local/sensitive/innerpb/processing/goerr"
	gorpc "git.local/sensitive/innerpb/processing/grpc"
	"git.local/sensitive/processing/account/schema"
)

type MerchantService struct {
	billingClient gorpc.BillingClient
}

func NewMerchantService(
	billingClient gorpc.BillingClient,
) Merchanter {
	return &MerchantService{
		billingClient: billingClient,
	}
}

func (ms *MerchantService) CheckMerchantByOwnerID(ctx context.Context, request schema.CheckMerchantRequest) error {
	balanceOwnerInfo, err := ms.billingClient.GetMerchantByBalanceOwnerIDV1(
		ctx, &gorpc.GetMerchantByBalanceOwnerRequestV1{
			BalanceOwnerId: &request.BalanceOwnerID,
		})
	if err != nil {
		return err
	}

	if balanceOwnerInfo.GetMerchantId() != request.MerchantID {
		return goerr.ErrInvalidOwnerForMerchant
	}

	return nil
}
