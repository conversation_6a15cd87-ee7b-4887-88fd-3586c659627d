package rest

import (
	"bytes"
	"context"
	"encoding/json"
	"git.local/sensitive/innerpb/processing/goerr"
	"git.local/sensitive/pkg/http_logger"
	"git.local/sensitive/processing/fiscalization/schema/authorize"
	"git.local/sensitive/sdk/dog"
	"go.uber.org/zap"
	"net/http"
	"time"
)

type GreenAuthorizeRest struct {
	client                   http.Client
	greenCashierAuthorizeURL string
	greenCashBoxAuthorizeURL string
	httpLogger               http_logger.HttpLogger
}

func NewGreenAuthorizeRest(
	greenCashierAuthorizeURL string,
	greenCashBoxAuthorizeURL string,
	httpLogger http_logger.HttpLogger,
) GreenAuthorizer {
	return GreenAuthorizeRest{
		client: http.Client{
			Timeout: time.Duration(clientDefaultTimeOut) * time.Second,
		},
		greenCashierAuthorizeURL: greenCashierAuthorizeURL,
		httpLogger:               httpLogger,
		greenCashBoxAuthorizeURL: greenCashBoxAuthorizeURL,
	}
}

func (ga GreenAuthorizeRest) GetOrganizationToken(
	ctx context.Context,
	login, password string,
) (_ authorize.CashierAuthorizationResponse, err error) {
	_, span := dog.CreateSpan(ctx, "GreenAuthorize_GetOrganizationToken")
	defer span.End()

	requestData, err := json.Marshal(struct {
		Login    string `json:"login"`
		Password string `json:"password"`
	}{
		Login:    login,
		Password: password,
	})
	if err != nil {
		return
	}

	req, err := http.NewRequest(http.MethodPut, ga.greenCashierAuthorizeURL, bytes.NewBuffer(requestData))
	if err != nil {
		return
	}

	req.Header.Set("Content-Type", "application/json")

	res, err := ga.client.Do(req)
	if res != nil {
		ga.httpLogger.Infow(context.Background(), res,
			"method", "GetOrganizationToken")
	}

	if err != nil {
		return
	}

	defer func() {
		_ = res.Body.Close()
	}()

	errMessage := map[string]interface{}{}

	if res.StatusCode != http.StatusOK {
		if err = json.NewDecoder(res.Body).Decode(&errMessage); err != nil {
			return
		}

		dog.L().Error("GetOrganizationToken_Error", zap.Error(err))

		return authorize.CashierAuthorizationResponse{}, goerr.ErrRequestFailed.WithResult(errMessage)
	}

	result := authorize.CashierAuthorizationResponse{}

	if err = json.NewDecoder(res.Body).Decode(&result); err != nil {
		return
	}

	return result, nil
}

func (ga GreenAuthorizeRest) GetCashboxToken(
	ctx context.Context,
	cashierToken string,
	cashboxID uint64,
) (_ authorize.CashboxResponse, err error) {
	_, span := dog.CreateSpan(ctx, "GreenAuthorize_GetCashboxToken")
	defer span.End()

	requestData, err := json.Marshal(authorize.CashboxAuthorizationRequest{
		TemporaryToken: cashierToken,
		CashboxID:      cashboxID,
	})
	if err != nil {
		return
	}

	dog.L().Info("GetCashboxToken", zap.String("requestData", string(requestData)))

	req, err := http.NewRequest(http.MethodPut, ga.greenCashBoxAuthorizeURL, bytes.NewBuffer(requestData))
	if err != nil {
		return
	}

	req.Header.Set("Content-Type", "application/json")

	res, err := ga.client.Do(req)
	if res != nil {
		ga.httpLogger.Infow(context.Background(), res, "method", "GetCashboxToken")
	}

	if err != nil {
		return
	}

	defer func() {
		_ = res.Body.Close()
	}()

	if res.StatusCode != http.StatusOK {
		errMessage := map[string]interface{}{}

		if err = json.NewDecoder(res.Body).Decode(&errMessage); err != nil {
			return
		}

		dog.L().Error("GetCashboxToken_Error", zap.Error(err))

		return authorize.CashboxResponse{}, goerr.ErrRequestFailed.WithResult(errMessage)
	}

	result := authorize.CashboxResponse{}

	if err = json.NewDecoder(res.Body).Decode(&result); err != nil {
		return
	}

	return result, nil
}
