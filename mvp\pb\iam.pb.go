// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.5
// 	protoc        v5.29.3
// source: proto/iam.proto

package pb

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	descriptorpb "google.golang.org/protobuf/types/descriptorpb"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type Role int32

const (
	Role_unknownRole    Role = 0
	Role_NoneRole       Role = 1
	Role_SampleRole     Role = 2
	Role_AnyRole        Role = 3
	Role_AgwsSuperAdmin Role = 4
	Role_AgwsUser       Role = 5
	Role_AgwsBinderRole Role = 6
)

// Enum value maps for Role.
var (
	Role_name = map[int32]string{
		0: "unknownRole",
		1: "NoneRole",
		2: "SampleRole",
		3: "AnyRole",
		4: "AgwsSuperAdmin",
		5: "AgwsUser",
		6: "AgwsBinderRole",
	}
	Role_value = map[string]int32{
		"unknownRole":    0,
		"NoneRole":       1,
		"SampleRole":     2,
		"AnyRole":        3,
		"AgwsSuperAdmin": 4,
		"AgwsUser":       5,
		"AgwsBinderRole": 6,
	}
)

func (x Role) Enum() *Role {
	p := new(Role)
	*p = x
	return p
}

func (x Role) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (Role) Descriptor() protoreflect.EnumDescriptor {
	return file_proto_iam_proto_enumTypes[0].Descriptor()
}

func (Role) Type() protoreflect.EnumType {
	return &file_proto_iam_proto_enumTypes[0]
}

func (x Role) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use Role.Descriptor instead.
func (Role) EnumDescriptor() ([]byte, []int) {
	return file_proto_iam_proto_rawDescGZIP(), []int{0}
}

type Permission int32

const (
	Permission_UnknownPermission            Permission = 0
	Permission_Transaction                  Permission = 1
	Permission_AnyPermission                Permission = 2
	Permission_AgwsShowcaseInfoV1Permission Permission = 3
)

// Enum value maps for Permission.
var (
	Permission_name = map[int32]string{
		0: "UnknownPermission",
		1: "Transaction",
		2: "AnyPermission",
		3: "AgwsShowcaseInfoV1Permission",
	}
	Permission_value = map[string]int32{
		"UnknownPermission":            0,
		"Transaction":                  1,
		"AnyPermission":                2,
		"AgwsShowcaseInfoV1Permission": 3,
	}
)

func (x Permission) Enum() *Permission {
	p := new(Permission)
	*p = x
	return p
}

func (x Permission) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (Permission) Descriptor() protoreflect.EnumDescriptor {
	return file_proto_iam_proto_enumTypes[1].Descriptor()
}

func (Permission) Type() protoreflect.EnumType {
	return &file_proto_iam_proto_enumTypes[1]
}

func (x Permission) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use Permission.Descriptor instead.
func (Permission) EnumDescriptor() ([]byte, []int) {
	return file_proto_iam_proto_rawDescGZIP(), []int{1}
}

type IamVersion int32

const (
	IamVersion_VUndefined IamVersion = 0
	IamVersion_V1         IamVersion = 1 // X-Signature
	IamVersion_V2         IamVersion = 2 // now
)

// Enum value maps for IamVersion.
var (
	IamVersion_name = map[int32]string{
		0: "VUndefined",
		1: "V1",
		2: "V2",
	}
	IamVersion_value = map[string]int32{
		"VUndefined": 0,
		"V1":         1,
		"V2":         2,
	}
)

func (x IamVersion) Enum() *IamVersion {
	p := new(IamVersion)
	*p = x
	return p
}

func (x IamVersion) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (IamVersion) Descriptor() protoreflect.EnumDescriptor {
	return file_proto_iam_proto_enumTypes[2].Descriptor()
}

func (IamVersion) Type() protoreflect.EnumType {
	return &file_proto_iam_proto_enumTypes[2]
}

func (x IamVersion) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use IamVersion.Descriptor instead.
func (IamVersion) EnumDescriptor() ([]byte, []int) {
	return file_proto_iam_proto_rawDescGZIP(), []int{2}
}

type RoleSpec struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Children      []Role                 `protobuf:"varint,1,rep,packed,name=children,enum=mvp.Role" json:"children,omitempty"`
	Permissions   []Permission           `protobuf:"varint,2,rep,packed,name=permissions,enum=mvp.Permission" json:"permissions,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *RoleSpec) Reset() {
	*x = RoleSpec{}
	mi := &file_proto_iam_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RoleSpec) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RoleSpec) ProtoMessage() {}

func (x *RoleSpec) ProtoReflect() protoreflect.Message {
	mi := &file_proto_iam_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RoleSpec.ProtoReflect.Descriptor instead.
func (*RoleSpec) Descriptor() ([]byte, []int) {
	return file_proto_iam_proto_rawDescGZIP(), []int{0}
}

func (x *RoleSpec) GetChildren() []Role {
	if x != nil {
		return x.Children
	}
	return nil
}

func (x *RoleSpec) GetPermissions() []Permission {
	if x != nil {
		return x.Permissions
	}
	return nil
}

type IAM struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Roles         []Role                 `protobuf:"varint,1,rep,packed,name=roles,enum=mvp.Role" json:"roles,omitempty"`
	Permissions   []Permission           `protobuf:"varint,2,rep,packed,name=permissions,enum=mvp.Permission" json:"permissions,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *IAM) Reset() {
	*x = IAM{}
	mi := &file_proto_iam_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *IAM) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*IAM) ProtoMessage() {}

func (x *IAM) ProtoReflect() protoreflect.Message {
	mi := &file_proto_iam_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use IAM.ProtoReflect.Descriptor instead.
func (*IAM) Descriptor() ([]byte, []int) {
	return file_proto_iam_proto_rawDescGZIP(), []int{1}
}

func (x *IAM) GetRoles() []Role {
	if x != nil {
		return x.Roles
	}
	return nil
}

func (x *IAM) GetPermissions() []Permission {
	if x != nil {
		return x.Permissions
	}
	return nil
}

type CheckGrantResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Ok            *bool                  `protobuf:"varint,1,opt,name=ok" json:"ok,omitempty"`
	Message       *string                `protobuf:"bytes,2,opt,name=message" json:"message,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CheckGrantResponse) Reset() {
	*x = CheckGrantResponse{}
	mi := &file_proto_iam_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CheckGrantResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CheckGrantResponse) ProtoMessage() {}

func (x *CheckGrantResponse) ProtoReflect() protoreflect.Message {
	mi := &file_proto_iam_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CheckGrantResponse.ProtoReflect.Descriptor instead.
func (*CheckGrantResponse) Descriptor() ([]byte, []int) {
	return file_proto_iam_proto_rawDescGZIP(), []int{2}
}

func (x *CheckGrantResponse) GetOk() bool {
	if x != nil && x.Ok != nil {
		return *x.Ok
	}
	return false
}

func (x *CheckGrantResponse) GetMessage() string {
	if x != nil && x.Message != nil {
		return *x.Message
	}
	return ""
}

type Identity struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	ID            *int64                 `protobuf:"varint,1,opt,name=ID" json:"ID,omitempty"`
	UUID          []byte                 `protobuf:"bytes,2,opt,name=UUID" json:"UUID,omitempty"`
	IsID          *bool                  `protobuf:"varint,3,opt,name=IsID" json:"IsID,omitempty"`
	Version       *IamVersion            `protobuf:"varint,4,opt,name=Version,enum=mvp.IamVersion" json:"Version,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Identity) Reset() {
	*x = Identity{}
	mi := &file_proto_iam_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Identity) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Identity) ProtoMessage() {}

func (x *Identity) ProtoReflect() protoreflect.Message {
	mi := &file_proto_iam_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Identity.ProtoReflect.Descriptor instead.
func (*Identity) Descriptor() ([]byte, []int) {
	return file_proto_iam_proto_rawDescGZIP(), []int{3}
}

func (x *Identity) GetID() int64 {
	if x != nil && x.ID != nil {
		return *x.ID
	}
	return 0
}

func (x *Identity) GetUUID() []byte {
	if x != nil {
		return x.UUID
	}
	return nil
}

func (x *Identity) GetIsID() bool {
	if x != nil && x.IsID != nil {
		return *x.IsID
	}
	return false
}

func (x *Identity) GetVersion() IamVersion {
	if x != nil && x.Version != nil {
		return *x.Version
	}
	return IamVersion_VUndefined
}

var file_proto_iam_proto_extTypes = []protoimpl.ExtensionInfo{
	{
		ExtendedType:  (*descriptorpb.EnumValueOptions)(nil),
		ExtensionType: (*RoleSpec)(nil),
		Field:         71000201,
		Name:          "mvp.role_spec",
		Tag:           "bytes,71000201,opt,name=role_spec",
		Filename:      "proto/iam.proto",
	},
	{
		ExtendedType:  (*descriptorpb.FileOptions)(nil),
		ExtensionType: (*bool)(nil),
		Field:         71000202,
		Name:          "mvp.generate_iam_functions",
		Tag:           "varint,71000202,opt,name=generate_iam_functions",
		Filename:      "proto/iam.proto",
	},
	{
		ExtendedType:  (*descriptorpb.MethodOptions)(nil),
		ExtensionType: (*IAM)(nil),
		Field:         71000203,
		Name:          "mvp.method_iam",
		Tag:           "bytes,71000203,opt,name=method_iam",
		Filename:      "proto/iam.proto",
	},
	{
		ExtendedType:  (*descriptorpb.ServiceOptions)(nil),
		ExtensionType: (*IAM)(nil),
		Field:         71000204,
		Name:          "mvp.service_iam",
		Tag:           "bytes,71000204,opt,name=service_iam",
		Filename:      "proto/iam.proto",
	},
}

// Extension fields to descriptorpb.EnumValueOptions.
var (
	// optional mvp.RoleSpec role_spec = 71000201;
	E_RoleSpec = &file_proto_iam_proto_extTypes[0]
)

// Extension fields to descriptorpb.FileOptions.
var (
	// optional bool generate_iam_functions = 71000202;
	E_GenerateIamFunctions = &file_proto_iam_proto_extTypes[1]
)

// Extension fields to descriptorpb.MethodOptions.
var (
	// optional mvp.IAM method_iam = 71000203;
	E_MethodIam = &file_proto_iam_proto_extTypes[2]
)

// Extension fields to descriptorpb.ServiceOptions.
var (
	// optional mvp.IAM service_iam = 71000204;
	E_ServiceIam = &file_proto_iam_proto_extTypes[3]
)

var File_proto_iam_proto protoreflect.FileDescriptor

var file_proto_iam_proto_rawDesc = string([]byte{
	0x0a, 0x0f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f, 0x69, 0x61, 0x6d, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x12, 0x03, 0x6d, 0x76, 0x70, 0x1a, 0x20, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74,
	0x6f, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1b, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x65, 0x6d, 0x70, 0x74, 0x79, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x64, 0x0a, 0x08, 0x52, 0x6f, 0x6c, 0x65, 0x53, 0x70, 0x65,
	0x63, 0x12, 0x25, 0x0a, 0x08, 0x63, 0x68, 0x69, 0x6c, 0x64, 0x72, 0x65, 0x6e, 0x18, 0x01, 0x20,
	0x03, 0x28, 0x0e, 0x32, 0x09, 0x2e, 0x6d, 0x76, 0x70, 0x2e, 0x52, 0x6f, 0x6c, 0x65, 0x52, 0x08,
	0x63, 0x68, 0x69, 0x6c, 0x64, 0x72, 0x65, 0x6e, 0x12, 0x31, 0x0a, 0x0b, 0x70, 0x65, 0x72, 0x6d,
	0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0e, 0x32, 0x0f, 0x2e,
	0x6d, 0x76, 0x70, 0x2e, 0x50, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x52, 0x0b,
	0x70, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x73, 0x22, 0x59, 0x0a, 0x03, 0x49,
	0x41, 0x4d, 0x12, 0x1f, 0x0a, 0x05, 0x72, 0x6f, 0x6c, 0x65, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28,
	0x0e, 0x32, 0x09, 0x2e, 0x6d, 0x76, 0x70, 0x2e, 0x52, 0x6f, 0x6c, 0x65, 0x52, 0x05, 0x72, 0x6f,
	0x6c, 0x65, 0x73, 0x12, 0x31, 0x0a, 0x0b, 0x70, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f,
	0x6e, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0e, 0x32, 0x0f, 0x2e, 0x6d, 0x76, 0x70, 0x2e, 0x50,
	0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x52, 0x0b, 0x70, 0x65, 0x72, 0x6d, 0x69,
	0x73, 0x73, 0x69, 0x6f, 0x6e, 0x73, 0x22, 0x3e, 0x0a, 0x12, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x47,
	0x72, 0x61, 0x6e, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x0e, 0x0a, 0x02,
	0x6f, 0x6b, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x52, 0x02, 0x6f, 0x6b, 0x12, 0x18, 0x0a, 0x07,
	0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6d,
	0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x22, 0x6d, 0x0a, 0x08, 0x49, 0x64, 0x65, 0x6e, 0x74, 0x69,
	0x74, 0x79, 0x12, 0x0e, 0x0a, 0x02, 0x49, 0x44, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02,
	0x49, 0x44, 0x12, 0x12, 0x0a, 0x04, 0x55, 0x55, 0x49, 0x44, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0c,
	0x52, 0x04, 0x55, 0x55, 0x49, 0x44, 0x12, 0x12, 0x0a, 0x04, 0x49, 0x73, 0x49, 0x44, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x08, 0x52, 0x04, 0x49, 0x73, 0x49, 0x44, 0x12, 0x29, 0x0a, 0x07, 0x56, 0x65,
	0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x0f, 0x2e, 0x6d, 0x76,
	0x70, 0x2e, 0x49, 0x61, 0x6d, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x52, 0x07, 0x56, 0x65,
	0x72, 0x73, 0x69, 0x6f, 0x6e, 0x2a, 0xc2, 0x01, 0x0a, 0x04, 0x52, 0x6f, 0x6c, 0x65, 0x12, 0x1b,
	0x0a, 0x0b, 0x75, 0x6e, 0x6b, 0x6e, 0x6f, 0x77, 0x6e, 0x52, 0x6f, 0x6c, 0x65, 0x10, 0x00, 0x1a,
	0x0a, 0xca, 0x88, 0xec, 0x8e, 0x02, 0x04, 0x0a, 0x02, 0x02, 0x01, 0x12, 0x17, 0x0a, 0x08, 0x4e,
	0x6f, 0x6e, 0x65, 0x52, 0x6f, 0x6c, 0x65, 0x10, 0x01, 0x1a, 0x09, 0xca, 0x88, 0xec, 0x8e, 0x02,
	0x03, 0x12, 0x01, 0x01, 0x12, 0x1d, 0x0a, 0x0a, 0x53, 0x61, 0x6d, 0x70, 0x6c, 0x65, 0x52, 0x6f,
	0x6c, 0x65, 0x10, 0x02, 0x1a, 0x0d, 0xca, 0x88, 0xec, 0x8e, 0x02, 0x07, 0x0a, 0x01, 0x01, 0x12,
	0x02, 0x00, 0x02, 0x12, 0x19, 0x0a, 0x07, 0x41, 0x6e, 0x79, 0x52, 0x6f, 0x6c, 0x65, 0x10, 0x03,
	0x1a, 0x0c, 0xca, 0x88, 0xec, 0x8e, 0x02, 0x06, 0x0a, 0x01, 0x00, 0x12, 0x01, 0x02, 0x12, 0x1d,
	0x0a, 0x0e, 0x41, 0x67, 0x77, 0x73, 0x53, 0x75, 0x70, 0x65, 0x72, 0x41, 0x64, 0x6d, 0x69, 0x6e,
	0x10, 0x04, 0x1a, 0x09, 0xca, 0x88, 0xec, 0x8e, 0x02, 0x03, 0x0a, 0x01, 0x05, 0x12, 0x17, 0x0a,
	0x08, 0x41, 0x67, 0x77, 0x73, 0x55, 0x73, 0x65, 0x72, 0x10, 0x05, 0x1a, 0x09, 0xca, 0x88, 0xec,
	0x8e, 0x02, 0x03, 0x0a, 0x01, 0x06, 0x12, 0x12, 0x0a, 0x0e, 0x41, 0x67, 0x77, 0x73, 0x42, 0x69,
	0x6e, 0x64, 0x65, 0x72, 0x52, 0x6f, 0x6c, 0x65, 0x10, 0x06, 0x2a, 0x69, 0x0a, 0x0a, 0x50, 0x65,
	0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x15, 0x0a, 0x11, 0x55, 0x6e, 0x6b, 0x6e,
	0x6f, 0x77, 0x6e, 0x50, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x10, 0x00, 0x12,
	0x0f, 0x0a, 0x0b, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x10, 0x01,
	0x12, 0x11, 0x0a, 0x0d, 0x41, 0x6e, 0x79, 0x50, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f,
	0x6e, 0x10, 0x02, 0x12, 0x20, 0x0a, 0x1c, 0x41, 0x67, 0x77, 0x73, 0x53, 0x68, 0x6f, 0x77, 0x63,
	0x61, 0x73, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x56, 0x31, 0x50, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73,
	0x69, 0x6f, 0x6e, 0x10, 0x03, 0x2a, 0x2c, 0x0a, 0x0a, 0x49, 0x61, 0x6d, 0x56, 0x65, 0x72, 0x73,
	0x69, 0x6f, 0x6e, 0x12, 0x0e, 0x0a, 0x0a, 0x56, 0x55, 0x6e, 0x64, 0x65, 0x66, 0x69, 0x6e, 0x65,
	0x64, 0x10, 0x00, 0x12, 0x06, 0x0a, 0x02, 0x56, 0x31, 0x10, 0x01, 0x12, 0x06, 0x0a, 0x02, 0x56,
	0x32, 0x10, 0x02, 0x32, 0x79, 0x0a, 0x0d, 0x41, 0x63, 0x63, 0x65, 0x73, 0x73, 0x43, 0x6f, 0x6e,
	0x74, 0x72, 0x6f, 0x6c, 0x12, 0x2e, 0x0a, 0x0a, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x47, 0x72, 0x61,
	0x6e, 0x74, 0x12, 0x08, 0x2e, 0x6d, 0x76, 0x70, 0x2e, 0x49, 0x41, 0x4d, 0x1a, 0x16, 0x2e, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x45,
	0x6d, 0x70, 0x74, 0x79, 0x12, 0x38, 0x0a, 0x06, 0x57, 0x68, 0x6f, 0x41, 0x6d, 0x69, 0x12, 0x16,
	0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66,
	0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x1a, 0x16, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x3a, 0x50,
	0x0a, 0x09, 0x72, 0x6f, 0x6c, 0x65, 0x5f, 0x73, 0x70, 0x65, 0x63, 0x12, 0x21, 0x2e, 0x67, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x45, 0x6e,
	0x75, 0x6d, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x89,
	0xc1, 0xed, 0x21, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0d, 0x2e, 0x6d, 0x76, 0x70, 0x2e, 0x52, 0x6f,
	0x6c, 0x65, 0x53, 0x70, 0x65, 0x63, 0x52, 0x08, 0x72, 0x6f, 0x6c, 0x65, 0x53, 0x70, 0x65, 0x63,
	0x3a, 0x55, 0x0a, 0x16, 0x67, 0x65, 0x6e, 0x65, 0x72, 0x61, 0x74, 0x65, 0x5f, 0x69, 0x61, 0x6d,
	0x5f, 0x66, 0x75, 0x6e, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x12, 0x1c, 0x2e, 0x67, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x46, 0x69, 0x6c,
	0x65, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x8a, 0xc1, 0xed, 0x21, 0x20, 0x01, 0x28,
	0x08, 0x52, 0x14, 0x67, 0x65, 0x6e, 0x65, 0x72, 0x61, 0x74, 0x65, 0x49, 0x61, 0x6d, 0x46, 0x75,
	0x6e, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x3a, 0x4a, 0x0a, 0x0a, 0x6d, 0x65, 0x74, 0x68, 0x6f,
	0x64, 0x5f, 0x69, 0x61, 0x6d, 0x12, 0x1e, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x4d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x4f, 0x70,
	0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x8b, 0xc1, 0xed, 0x21, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x08,
	0x2e, 0x6d, 0x76, 0x70, 0x2e, 0x49, 0x41, 0x4d, 0x52, 0x09, 0x6d, 0x65, 0x74, 0x68, 0x6f, 0x64,
	0x49, 0x61, 0x6d, 0x3a, 0x4d, 0x0a, 0x0b, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x69,
	0x61, 0x6d, 0x12, 0x1f, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x62, 0x75, 0x66, 0x2e, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x4f, 0x70, 0x74, 0x69,
	0x6f, 0x6e, 0x73, 0x18, 0x8c, 0xc1, 0xed, 0x21, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x08, 0x2e, 0x6d,
	0x76, 0x70, 0x2e, 0x49, 0x41, 0x4d, 0x52, 0x0a, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x49,
	0x61, 0x6d, 0x42, 0x1c, 0x5a, 0x1a, 0x67, 0x69, 0x74, 0x2e, 0x6c, 0x6f, 0x63, 0x61, 0x6c, 0x2f,
	0x73, 0x65, 0x6e, 0x73, 0x69, 0x74, 0x69, 0x76, 0x65, 0x2f, 0x6d, 0x76, 0x70, 0x2f, 0x70, 0x62,
	0x62, 0x08, 0x65, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x70, 0xe8, 0x07,
})

var (
	file_proto_iam_proto_rawDescOnce sync.Once
	file_proto_iam_proto_rawDescData []byte
)

func file_proto_iam_proto_rawDescGZIP() []byte {
	file_proto_iam_proto_rawDescOnce.Do(func() {
		file_proto_iam_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_proto_iam_proto_rawDesc), len(file_proto_iam_proto_rawDesc)))
	})
	return file_proto_iam_proto_rawDescData
}

var file_proto_iam_proto_enumTypes = make([]protoimpl.EnumInfo, 3)
var file_proto_iam_proto_msgTypes = make([]protoimpl.MessageInfo, 4)
var file_proto_iam_proto_goTypes = []any{
	(Role)(0),                             // 0: mvp.Role
	(Permission)(0),                       // 1: mvp.Permission
	(IamVersion)(0),                       // 2: mvp.IamVersion
	(*RoleSpec)(nil),                      // 3: mvp.RoleSpec
	(*IAM)(nil),                           // 4: mvp.IAM
	(*CheckGrantResponse)(nil),            // 5: mvp.CheckGrantResponse
	(*Identity)(nil),                      // 6: mvp.Identity
	(*descriptorpb.EnumValueOptions)(nil), // 7: google.protobuf.EnumValueOptions
	(*descriptorpb.FileOptions)(nil),      // 8: google.protobuf.FileOptions
	(*descriptorpb.MethodOptions)(nil),    // 9: google.protobuf.MethodOptions
	(*descriptorpb.ServiceOptions)(nil),   // 10: google.protobuf.ServiceOptions
	(*emptypb.Empty)(nil),                 // 11: google.protobuf.Empty
}
var file_proto_iam_proto_depIdxs = []int32{
	0,  // 0: mvp.RoleSpec.children:type_name -> mvp.Role
	1,  // 1: mvp.RoleSpec.permissions:type_name -> mvp.Permission
	0,  // 2: mvp.IAM.roles:type_name -> mvp.Role
	1,  // 3: mvp.IAM.permissions:type_name -> mvp.Permission
	2,  // 4: mvp.Identity.Version:type_name -> mvp.IamVersion
	7,  // 5: mvp.role_spec:extendee -> google.protobuf.EnumValueOptions
	8,  // 6: mvp.generate_iam_functions:extendee -> google.protobuf.FileOptions
	9,  // 7: mvp.method_iam:extendee -> google.protobuf.MethodOptions
	10, // 8: mvp.service_iam:extendee -> google.protobuf.ServiceOptions
	3,  // 9: mvp.role_spec:type_name -> mvp.RoleSpec
	4,  // 10: mvp.method_iam:type_name -> mvp.IAM
	4,  // 11: mvp.service_iam:type_name -> mvp.IAM
	4,  // 12: mvp.AccessControl.CheckGrant:input_type -> mvp.IAM
	11, // 13: mvp.AccessControl.WhoAmi:input_type -> google.protobuf.Empty
	11, // 14: mvp.AccessControl.CheckGrant:output_type -> google.protobuf.Empty
	11, // 15: mvp.AccessControl.WhoAmi:output_type -> google.protobuf.Empty
	14, // [14:16] is the sub-list for method output_type
	12, // [12:14] is the sub-list for method input_type
	9,  // [9:12] is the sub-list for extension type_name
	5,  // [5:9] is the sub-list for extension extendee
	0,  // [0:5] is the sub-list for field type_name
}

func init() { file_proto_iam_proto_init() }
func file_proto_iam_proto_init() {
	if File_proto_iam_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_proto_iam_proto_rawDesc), len(file_proto_iam_proto_rawDesc)),
			NumEnums:      3,
			NumMessages:   4,
			NumExtensions: 4,
			NumServices:   1,
		},
		GoTypes:           file_proto_iam_proto_goTypes,
		DependencyIndexes: file_proto_iam_proto_depIdxs,
		EnumInfos:         file_proto_iam_proto_enumTypes,
		MessageInfos:      file_proto_iam_proto_msgTypes,
		ExtensionInfos:    file_proto_iam_proto_extTypes,
	}.Build()
	File_proto_iam_proto = out.File
	file_proto_iam_proto_goTypes = nil
	file_proto_iam_proto_depIdxs = nil
}
