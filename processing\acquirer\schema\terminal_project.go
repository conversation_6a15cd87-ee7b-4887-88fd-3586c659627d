package schema

import (
	"git.local/sensitive/processing/acquirer/model"
	"github.com/go-playground/validator/v10"
	"time"
)

type TerminalProjectRequest struct {
	ProjectID         uint64 `json:"project_id" validate:"required"`
	TransactionTypeID uint64 `json:"transaction_type_id" validate:"required"`
	IsActive          bool   `json:"is_active"`
	TerminalID        uint64 `json:"terminal_id" validate:"required"`
}

type UpdateTerminalProjectStatusRequest struct {
	IsActive bool `json:"is_active" validate:"required"`
}

type UpdateTerminalProjectRequest struct {
	ProjectID         uint64 `json:"project_id" validate:"required"`
	TransactionTypeID uint64 `json:"transaction_type_id" validate:"required"`
	IsActive          bool   `json:"is_active" validate:"required"`
}

type ProjectTerminalResponse struct {
	ID                uint64 `json:"id"`
	TerminalID        uint64 `json:"terminal_id"`
	ProjectID         uint64 `json:"project_id"`
	TransactionTypeID uint64 `json:"transaction_type_id"`
	IsActive          bool   `json:"is_active"`
}

func (a UpdateTerminalProjectRequest) Validate() error {
	validation := validator.New()

	return validation.Struct(a)
}

func (a TerminalProjectRequest) Validate() error {
	validation := validator.New()

	return validation.Struct(a)
}

func (t TerminalProjectRequest) ToModel() *model.TerminalProject {
	return &model.TerminalProject{
		ProjectID:         t.ProjectID,
		TransactionTypeID: t.TransactionTypeID,
		IsActive:          t.IsActive,
		TerminalID:        t.TerminalID,
	}
}

type GetTerminalProjectPayIn struct {
	ID                uint64    `json:"id" validate:"required"`
	ProjectID         uint64    `json:"project_id" validate:"required"`
	TransactionTypeID uint64    `json:"transaction_type_id" validate:"required"`
	TerminalID        uint64    `json:"terminal_id" validate:"required"`
	IsActive          bool      `json:"is_active" validate:"required"`
	Name              string    `json:"name" validate:"required"`
	Code              string    `json:"code" validate:"required"`
	CreatedAt         time.Time `json:"created_at" validate:"required"`
	UpdatedAt         time.Time `json:"updated_at" validate:"required"`
}
