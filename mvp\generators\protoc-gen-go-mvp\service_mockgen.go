package main

import (
	"path/filepath"

	"google.golang.org/protobuf/compiler/protogen"
)

func init() {
	RegisterGenerator(&MockGenGenerator{})
}

type MockGenGenerator struct{}

func (generator *MockGenGenerator) ProtoGenGenerate(gen *protogen.Plugin) {
	generator.Services(gen)
}

func (generator *MockGenGenerator) Services(gen *protogen.Plugin) {
	for _, file := range gen.Files {
		if !file.Generate {
			continue
		}

		if len(file.Services) == 0 {
			return
		}

		dir := filepath.Dir(file.GeneratedFilenamePrefix)
		mockGenNamePackage := filepath.Base(dir)
		fileName := filepath.Base(file.GeneratedFilenamePrefix + "_grpc.pb.go")
		mockGenFileName := filepath.Base(file.GeneratedFilenamePrefix + V2_MOCKGEN_SUFFIX)

		g := gen.NewGeneratedFile(
			file.GeneratedFilenamePrefix+V2_MOCKGEN_FULL_SUFFIX,
			file.GoImportPath,
		)

		HeaderPrint(g, string(file.GoPackageName))

		g.P("//go:generate mockgen -destination=./", mockGenNamePackage, "mock/", mockGenFileName, "_", fileName, " -package=", mockGenNamePackage, "mock -source=", fileName)
	}
}
