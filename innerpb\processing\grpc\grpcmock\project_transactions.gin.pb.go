// Code generated by MockGen. DO NOT EDIT.
// Source: project_transactions.gin.pb.go

// Package grpcmock is a generated GoMock package.
package grpcmock

import (
	reflect "reflect"

	gin "github.com/gin-gonic/gin"
	gomock "github.com/golang/mock/gomock"
)

// MockGinProjectTransactionsServer is a mock of GinProjectTransactionsServer interface.
type MockGinProjectTransactionsServer struct {
	ctrl     *gomock.Controller
	recorder *MockGinProjectTransactionsServerMockRecorder
}

// MockGinProjectTransactionsServerMockRecorder is the mock recorder for MockGinProjectTransactionsServer.
type MockGinProjectTransactionsServerMockRecorder struct {
	mock *MockGinProjectTransactionsServer
}

// NewMockGinProjectTransactionsServer creates a new mock instance.
func NewMockGinProjectTransactionsServer(ctrl *gomock.Controller) *MockGinProjectTransactionsServer {
	mock := &MockGinProjectTransactionsServer{ctrl: ctrl}
	mock.recorder = &MockGinProjectTransactionsServerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockGinProjectTransactionsServer) EXPECT() *MockGinProjectTransactionsServerMockRecorder {
	return m.recorder
}

// CheckAmountLimit mocks base method.
func (m *MockGinProjectTransactionsServer) CheckAmountLimit(c *gin.Context) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CheckAmountLimit", c)
	ret0, _ := ret[0].(error)
	return ret0
}

// CheckAmountLimit indicates an expected call of CheckAmountLimit.
func (mr *MockGinProjectTransactionsServerMockRecorder) CheckAmountLimit(c interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CheckAmountLimit", reflect.TypeOf((*MockGinProjectTransactionsServer)(nil).CheckAmountLimit), c)
}

// CheckAttemptsWithinTimeout mocks base method.
func (m *MockGinProjectTransactionsServer) CheckAttemptsWithinTimeout(c *gin.Context) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CheckAttemptsWithinTimeout", c)
	ret0, _ := ret[0].(error)
	return ret0
}

// CheckAttemptsWithinTimeout indicates an expected call of CheckAttemptsWithinTimeout.
func (mr *MockGinProjectTransactionsServerMockRecorder) CheckAttemptsWithinTimeout(c interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CheckAttemptsWithinTimeout", reflect.TypeOf((*MockGinProjectTransactionsServer)(nil).CheckAttemptsWithinTimeout), c)
}

// GetTransactionLimit mocks base method.
func (m *MockGinProjectTransactionsServer) GetTransactionLimit(c *gin.Context) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetTransactionLimit", c)
	ret0, _ := ret[0].(error)
	return ret0
}

// GetTransactionLimit indicates an expected call of GetTransactionLimit.
func (mr *MockGinProjectTransactionsServerMockRecorder) GetTransactionLimit(c interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetTransactionLimit", reflect.TypeOf((*MockGinProjectTransactionsServer)(nil).GetTransactionLimit), c)
}
