// Code generated by protoc-gen-go-mvp. DO NOT EDIT.
// versions:
// - protoc-gen-go-mvp v2.0.1
// created by mvp authors

package grpc

import (
	context "context"
	grpc "google.golang.org/grpc"
	metadata "google.golang.org/grpc/metadata"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
)

func NewIamMultiacquiringEmissionServer(
	srv MultiacquiringEmissionServer,
) MultiacquiringEmissionServer {
	return &iamMultiacquiringEmissionServer{
		srv: srv,
	}
}

var _ MultiacquiringEmissionServer = (*iamMultiacquiringEmissionServer)(nil)

type iamMultiacquiringEmissionServer struct {
	UnimplementedMultiacquiringEmissionServer

	srv MultiacquiringEmissionServer
}

func (s *iamMultiacquiringEmissionServer) GetEmission(
	ctx context.Context,
	req *emptypb.Empty,
) (
	*EmissionResponse,
	error,
) {
	return s.srv.GetEmission(ctx, req)
}

func (s *iamMultiacquiringEmissionServer) ConfirmEmission(
	ctx context.Context,
	req *EmoneyRequest,
) (
	*EmoneyResponse,
	error,
) {
	return s.srv.ConfirmEmission(ctx, req)
}

func NewIamMultiacquiringEmissionClient(
	client MultiacquiringEmissionClient,
) MultiacquiringEmissionClient {
	return &iamMultiacquiringEmissionClient{
		client: client,
	}
}

type iamMultiacquiringEmissionClient struct {
	client MultiacquiringEmissionClient
}

func (s *iamMultiacquiringEmissionClient) GetEmission(
	ctx context.Context,
	req *emptypb.Empty,
	opts ...grpc.CallOption,
) (
	*EmissionResponse,
	error,
) {
	md, _ := metadata.FromIncomingContext(ctx)
	return s.client.GetEmission(metadata.NewOutgoingContext(ctx, md), req)
}

func (s *iamMultiacquiringEmissionClient) ConfirmEmission(
	ctx context.Context,
	req *EmoneyRequest,
	opts ...grpc.CallOption,
) (
	*EmoneyResponse,
	error,
) {
	md, _ := metadata.FromIncomingContext(ctx)
	return s.client.ConfirmEmission(metadata.NewOutgoingContext(ctx, md), req)
}
