// Code generated by protoc-gen-go-mvp. DO NOT EDIT.
// versions:
// - protoc-gen-go-mvp v2.0.1
// created by mvp authors

package grpc

import (
	dog "git.local/sensitive/sdk/dog"
	gin "github.com/gin-gonic/gin"
	metadata "google.golang.org/grpc/metadata"
	http "net/http"
)

func RegisterGinNotificationRouterGroup(
	in *gin.RouterGroup,
	handler func(func(c *gin.Context) error) gin.HandlerFunc,
) error {
	service, err := NewGinNotificationService()
	if err != nil {
		return err
	}

	routerGroup := in.Group("processing.notification.notification.Notification")
	routerGroup.PUT("/GetLastMailByUserEmail", handler(service.GetLastMailByUserEmail))
	routerGroup.PUT("/GetLastSMSByUserPhone", handler(service.GetLastSMSByUserPhone))
	return nil
}

func NewGinNotificationService() (GinNotificationServer, error) {
	client, err := NewPreparedNotificationClient()
	if err != nil {
		return nil, err
	}

	return &ginNotificationServer{
		client: NewLoggedNotificationClient(
			NewIamNotificationClient(client),
		),
	}, nil
}

//go:generate mockgen -destination=./grpcmock/notification.gin.pb.go -package=grpcmock -source=notification.gin.pb.go GinNotificationServer
type GinNotificationServer interface {
	GetLastMailByUserEmail(c *gin.Context) error
	GetLastSMSByUserPhone(c *gin.Context) error
}

var _ GinNotificationServer = (*ginNotificationServer)(nil)

type ginNotificationServer struct {
	client NotificationClient
}

type Notification_GetLastMailByUserEmail_Success struct {
	// Status is the status of the response. Set true
	Status bool `json:"status"`
	// Message is a human-readable explanation of the status
	Message string `json:"message"`
	// StatusCode is the GoErr status. Set 0
	StatusCode int `json:"status_code"`
	// Result is the result of the request
	Result *GetMailResDataV1 `json:"result"`
}

type Notification_GetLastMailByUserEmail_Failure struct {
	// Status is the status of the response. Set false
	Status bool `json:"status"`
	// Message is a human-readable explanation of the status
	Message string `json:"message"`
	// StatusCode is the GoErr status
	StatusCode int `json:"status_code"`
}

// GetLastMailByUserEmail
// @Summary GetLastMailByUserEmail
// @Security bearerAuth
// @ID Notification_GetLastMailByUserEmail
// @Accept json
// @Param request body GetMailReqDataV1 true "GetMailReqDataV1"
// @Success 200 {object} Notification_GetLastMailByUserEmail_Success
// @Failure 401 {object} Notification_GetLastMailByUserEmail_Failure "goerrpkg.ErrorTypeAuthorization"
// @Failure 404 {object} Notification_GetLastMailByUserEmail_Failure "goerrpkg.ErrorTypeNotFound"
// @Failure 400 {object} Notification_GetLastMailByUserEmail_Failure "goerrpkg.ErrorTypeBadRequest"
// @Failure 500 {object} Notification_GetLastMailByUserEmail_Failure "goerrpkg.ErrorTypeUnexpectedError"
// @Failure 400 {object} Notification_GetLastMailByUserEmail_Failure "gin.ErrorTypeBind"
// @Failure 500 {object} Notification_GetLastMailByUserEmail_Failure "Undefined error"
// @Produce json
// @Router /processing.notification.notification.Notification/GetLastMailByUserEmail [put]
// @tags Notification
func (s *ginNotificationServer) GetLastMailByUserEmail(c *gin.Context) error {
	ctx, span := dog.CreateSpan(c.Request.Context(), "GinNotificationServer_GetLastMailByUserEmail")
	defer span.End()

	var request GetMailReqDataV1
	if err := c.ShouldBindJSON(&request); err != nil {
		return err
	}

	ctx = metadata.NewOutgoingContext(ctx, metadata.MD(c.Request.Header))

	response, err := s.client.GetLastMailByUserEmail(ctx, &request)
	if err != nil {
		return err
	}

	c.JSON(http.StatusOK, &Notification_GetLastMailByUserEmail_Success{
		Status:     true,
		Message:    "Success",
		StatusCode: 0,
		Result:     response,
	})
	return nil
}

type Notification_GetLastSMSByUserPhone_Success struct {
	// Status is the status of the response. Set true
	Status bool `json:"status"`
	// Message is a human-readable explanation of the status
	Message string `json:"message"`
	// StatusCode is the GoErr status. Set 0
	StatusCode int `json:"status_code"`
	// Result is the result of the request
	Result *GetSMSResDataV1 `json:"result"`
}

type Notification_GetLastSMSByUserPhone_Failure struct {
	// Status is the status of the response. Set false
	Status bool `json:"status"`
	// Message is a human-readable explanation of the status
	Message string `json:"message"`
	// StatusCode is the GoErr status
	StatusCode int `json:"status_code"`
}

// GetLastSMSByUserPhone
// @Summary GetLastSMSByUserPhone
// @Security bearerAuth
// @ID Notification_GetLastSMSByUserPhone
// @Accept json
// @Param request body GetSMSReqDataV1 true "GetSMSReqDataV1"
// @Success 200 {object} Notification_GetLastSMSByUserPhone_Success
// @Failure 401 {object} Notification_GetLastSMSByUserPhone_Failure "goerrpkg.ErrorTypeAuthorization"
// @Failure 404 {object} Notification_GetLastSMSByUserPhone_Failure "goerrpkg.ErrorTypeNotFound"
// @Failure 400 {object} Notification_GetLastSMSByUserPhone_Failure "goerrpkg.ErrorTypeBadRequest"
// @Failure 500 {object} Notification_GetLastSMSByUserPhone_Failure "goerrpkg.ErrorTypeUnexpectedError"
// @Failure 400 {object} Notification_GetLastSMSByUserPhone_Failure "gin.ErrorTypeBind"
// @Failure 500 {object} Notification_GetLastSMSByUserPhone_Failure "Undefined error"
// @Produce json
// @Router /processing.notification.notification.Notification/GetLastSMSByUserPhone [put]
// @tags Notification
func (s *ginNotificationServer) GetLastSMSByUserPhone(c *gin.Context) error {
	ctx, span := dog.CreateSpan(c.Request.Context(), "GinNotificationServer_GetLastSMSByUserPhone")
	defer span.End()

	var request GetSMSReqDataV1
	if err := c.ShouldBindJSON(&request); err != nil {
		return err
	}

	ctx = metadata.NewOutgoingContext(ctx, metadata.MD(c.Request.Header))

	response, err := s.client.GetLastSMSByUserPhone(ctx, &request)
	if err != nil {
		return err
	}

	c.JSON(http.StatusOK, &Notification_GetLastSMSByUserPhone_Success{
		Status:     true,
		Message:    "Success",
		StatusCode: 0,
		Result:     response,
	})
	return nil
}
