// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.5
// 	protoc        v5.29.3
// source: inner/processing/grpc/multiacquiring_emission.proto

package grpc

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type Value struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            *uint64                `protobuf:"varint,1,opt,name=id" json:"id,omitempty"`
	Dscr          *string                `protobuf:"bytes,2,opt,name=dscr" json:"dscr,omitempty"`
	Clibin        *string                `protobuf:"bytes,3,opt,name=clibin" json:"clibin,omitempty"`
	Iban          *string                `protobuf:"bytes,4,opt,name=iban" json:"iban,omitempty"`
	AgentName     *string                `protobuf:"bytes,5,opt,name=agent_name,json=agentName" json:"agent_name,omitempty"`
	Agent         *string                `protobuf:"bytes,6,opt,name=agent" json:"agent,omitempty"`
	Amount        *float64               `protobuf:"fixed64,7,opt,name=amount" json:"amount,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Value) Reset() {
	*x = Value{}
	mi := &file_inner_processing_grpc_multiacquiring_emission_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Value) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Value) ProtoMessage() {}

func (x *Value) ProtoReflect() protoreflect.Message {
	mi := &file_inner_processing_grpc_multiacquiring_emission_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Value.ProtoReflect.Descriptor instead.
func (*Value) Descriptor() ([]byte, []int) {
	return file_inner_processing_grpc_multiacquiring_emission_proto_rawDescGZIP(), []int{0}
}

func (x *Value) GetId() uint64 {
	if x != nil && x.Id != nil {
		return *x.Id
	}
	return 0
}

func (x *Value) GetDscr() string {
	if x != nil && x.Dscr != nil {
		return *x.Dscr
	}
	return ""
}

func (x *Value) GetClibin() string {
	if x != nil && x.Clibin != nil {
		return *x.Clibin
	}
	return ""
}

func (x *Value) GetIban() string {
	if x != nil && x.Iban != nil {
		return *x.Iban
	}
	return ""
}

func (x *Value) GetAgentName() string {
	if x != nil && x.AgentName != nil {
		return *x.AgentName
	}
	return ""
}

func (x *Value) GetAgent() string {
	if x != nil && x.Agent != nil {
		return *x.Agent
	}
	return ""
}

func (x *Value) GetAmount() float64 {
	if x != nil && x.Amount != nil {
		return *x.Amount
	}
	return 0
}

type EmissionResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Code          *string                `protobuf:"bytes,1,opt,name=code" json:"code,omitempty"`
	Message       *string                `protobuf:"bytes,2,opt,name=message" json:"message,omitempty"`
	Value         []*Value               `protobuf:"bytes,3,rep,name=Value" json:"Value,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *EmissionResponse) Reset() {
	*x = EmissionResponse{}
	mi := &file_inner_processing_grpc_multiacquiring_emission_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *EmissionResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EmissionResponse) ProtoMessage() {}

func (x *EmissionResponse) ProtoReflect() protoreflect.Message {
	mi := &file_inner_processing_grpc_multiacquiring_emission_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EmissionResponse.ProtoReflect.Descriptor instead.
func (*EmissionResponse) Descriptor() ([]byte, []int) {
	return file_inner_processing_grpc_multiacquiring_emission_proto_rawDescGZIP(), []int{1}
}

func (x *EmissionResponse) GetCode() string {
	if x != nil && x.Code != nil {
		return *x.Code
	}
	return ""
}

func (x *EmissionResponse) GetMessage() string {
	if x != nil && x.Message != nil {
		return *x.Message
	}
	return ""
}

func (x *EmissionResponse) GetValue() []*Value {
	if x != nil {
		return x.Value
	}
	return nil
}

type EmoneyResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Code          *string                `protobuf:"bytes,1,opt,name=code" json:"code,omitempty"`
	Message       *string                `protobuf:"bytes,2,opt,name=message" json:"message,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *EmoneyResponse) Reset() {
	*x = EmoneyResponse{}
	mi := &file_inner_processing_grpc_multiacquiring_emission_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *EmoneyResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EmoneyResponse) ProtoMessage() {}

func (x *EmoneyResponse) ProtoReflect() protoreflect.Message {
	mi := &file_inner_processing_grpc_multiacquiring_emission_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EmoneyResponse.ProtoReflect.Descriptor instead.
func (*EmoneyResponse) Descriptor() ([]byte, []int) {
	return file_inner_processing_grpc_multiacquiring_emission_proto_rawDescGZIP(), []int{2}
}

func (x *EmoneyResponse) GetCode() string {
	if x != nil && x.Code != nil {
		return *x.Code
	}
	return ""
}

func (x *EmoneyResponse) GetMessage() string {
	if x != nil && x.Message != nil {
		return *x.Message
	}
	return ""
}

type EmoneyRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            *string                `protobuf:"bytes,1,opt,name=id" json:"id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *EmoneyRequest) Reset() {
	*x = EmoneyRequest{}
	mi := &file_inner_processing_grpc_multiacquiring_emission_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *EmoneyRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EmoneyRequest) ProtoMessage() {}

func (x *EmoneyRequest) ProtoReflect() protoreflect.Message {
	mi := &file_inner_processing_grpc_multiacquiring_emission_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EmoneyRequest.ProtoReflect.Descriptor instead.
func (*EmoneyRequest) Descriptor() ([]byte, []int) {
	return file_inner_processing_grpc_multiacquiring_emission_proto_rawDescGZIP(), []int{3}
}

func (x *EmoneyRequest) GetId() string {
	if x != nil && x.Id != nil {
		return *x.Id
	}
	return ""
}

var File_inner_processing_grpc_multiacquiring_emission_proto protoreflect.FileDescriptor

var file_inner_processing_grpc_multiacquiring_emission_proto_rawDesc = string([]byte{
	0x0a, 0x33, 0x69, 0x6e, 0x6e, 0x65, 0x72, 0x2f, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69,
	0x6e, 0x67, 0x2f, 0x67, 0x72, 0x70, 0x63, 0x2f, 0x6d, 0x75, 0x6c, 0x74, 0x69, 0x61, 0x63, 0x71,
	0x75, 0x69, 0x72, 0x69, 0x6e, 0x67, 0x5f, 0x65, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x31, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e,
	0x67, 0x2e, 0x6d, 0x75, 0x6c, 0x74, 0x69, 0x61, 0x63, 0x71, 0x75, 0x69, 0x72, 0x69, 0x6e, 0x67,
	0x2e, 0x6d, 0x75, 0x6c, 0x74, 0x69, 0x61, 0x63, 0x71, 0x75, 0x69, 0x72, 0x69, 0x6e, 0x67, 0x5f,
	0x65, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x1a, 0x1b, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x65, 0x6d, 0x70, 0x74, 0x79, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0xa4, 0x01, 0x0a, 0x05, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x12,
	0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x02, 0x69, 0x64, 0x12,
	0x12, 0x0a, 0x04, 0x64, 0x73, 0x63, 0x72, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x64,
	0x73, 0x63, 0x72, 0x12, 0x16, 0x0a, 0x06, 0x63, 0x6c, 0x69, 0x62, 0x69, 0x6e, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x06, 0x63, 0x6c, 0x69, 0x62, 0x69, 0x6e, 0x12, 0x12, 0x0a, 0x04, 0x69,
	0x62, 0x61, 0x6e, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x69, 0x62, 0x61, 0x6e, 0x12,
	0x1d, 0x0a, 0x0a, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x05, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x09, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x14,
	0x0a, 0x05, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x61,
	0x67, 0x65, 0x6e, 0x74, 0x12, 0x16, 0x0a, 0x06, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x07,
	0x20, 0x01, 0x28, 0x01, 0x52, 0x06, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x22, 0x90, 0x01, 0x0a,
	0x10, 0x45, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x04, 0x63, 0x6f, 0x64, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12,
	0x4e, 0x0a, 0x05, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x38,
	0x2e, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x2e, 0x6d, 0x75, 0x6c, 0x74,
	0x69, 0x61, 0x63, 0x71, 0x75, 0x69, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x6d, 0x75, 0x6c, 0x74, 0x69,
	0x61, 0x63, 0x71, 0x75, 0x69, 0x72, 0x69, 0x6e, 0x67, 0x5f, 0x65, 0x6d, 0x69, 0x73, 0x73, 0x69,
	0x6f, 0x6e, 0x2e, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x05, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x22,
	0x3e, 0x0a, 0x0e, 0x45, 0x6d, 0x6f, 0x6e, 0x65, 0x79, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x04, 0x63, 0x6f, 0x64, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x22,
	0x1f, 0x0a, 0x0d, 0x45, 0x6d, 0x6f, 0x6e, 0x65, 0x79, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64,
	0x32, 0xa1, 0x02, 0x0a, 0x16, 0x4d, 0x75, 0x6c, 0x74, 0x69, 0x61, 0x63, 0x71, 0x75, 0x69, 0x72,
	0x69, 0x6e, 0x67, 0x45, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x6c, 0x0a, 0x0b, 0x47,
	0x65, 0x74, 0x45, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x16, 0x2e, 0x67, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x45, 0x6d, 0x70,
	0x74, 0x79, 0x1a, 0x43, 0x2e, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x2e,
	0x6d, 0x75, 0x6c, 0x74, 0x69, 0x61, 0x63, 0x71, 0x75, 0x69, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x6d,
	0x75, 0x6c, 0x74, 0x69, 0x61, 0x63, 0x71, 0x75, 0x69, 0x72, 0x69, 0x6e, 0x67, 0x5f, 0x65, 0x6d,
	0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x2e, 0x45, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x98, 0x01, 0x0a, 0x0f, 0x43, 0x6f,
	0x6e, 0x66, 0x69, 0x72, 0x6d, 0x45, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x40, 0x2e,
	0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x2e, 0x6d, 0x75, 0x6c, 0x74, 0x69,
	0x61, 0x63, 0x71, 0x75, 0x69, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x6d, 0x75, 0x6c, 0x74, 0x69, 0x61,
	0x63, 0x71, 0x75, 0x69, 0x72, 0x69, 0x6e, 0x67, 0x5f, 0x65, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f,
	0x6e, 0x2e, 0x45, 0x6d, 0x6f, 0x6e, 0x65, 0x79, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a,
	0x41, 0x2e, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x2e, 0x6d, 0x75, 0x6c,
	0x74, 0x69, 0x61, 0x63, 0x71, 0x75, 0x69, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x6d, 0x75, 0x6c, 0x74,
	0x69, 0x61, 0x63, 0x71, 0x75, 0x69, 0x72, 0x69, 0x6e, 0x67, 0x5f, 0x65, 0x6d, 0x69, 0x73, 0x73,
	0x69, 0x6f, 0x6e, 0x2e, 0x45, 0x6d, 0x6f, 0x6e, 0x65, 0x79, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x22, 0x00, 0x42, 0x2d, 0x5a, 0x2b, 0x67, 0x69, 0x74, 0x2e, 0x6c, 0x6f, 0x63, 0x61,
	0x6c, 0x2f, 0x73, 0x65, 0x6e, 0x73, 0x69, 0x74, 0x69, 0x76, 0x65, 0x2f, 0x69, 0x6e, 0x6e, 0x65,
	0x72, 0x70, 0x62, 0x2f, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x2f, 0x67,
	0x72, 0x70, 0x63, 0x62, 0x08, 0x65, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x70, 0xe8, 0x07,
})

var (
	file_inner_processing_grpc_multiacquiring_emission_proto_rawDescOnce sync.Once
	file_inner_processing_grpc_multiacquiring_emission_proto_rawDescData []byte
)

func file_inner_processing_grpc_multiacquiring_emission_proto_rawDescGZIP() []byte {
	file_inner_processing_grpc_multiacquiring_emission_proto_rawDescOnce.Do(func() {
		file_inner_processing_grpc_multiacquiring_emission_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_inner_processing_grpc_multiacquiring_emission_proto_rawDesc), len(file_inner_processing_grpc_multiacquiring_emission_proto_rawDesc)))
	})
	return file_inner_processing_grpc_multiacquiring_emission_proto_rawDescData
}

var file_inner_processing_grpc_multiacquiring_emission_proto_msgTypes = make([]protoimpl.MessageInfo, 4)
var file_inner_processing_grpc_multiacquiring_emission_proto_goTypes = []any{
	(*Value)(nil),            // 0: processing.multiacquiring.multiacquiring_emission.Value
	(*EmissionResponse)(nil), // 1: processing.multiacquiring.multiacquiring_emission.EmissionResponse
	(*EmoneyResponse)(nil),   // 2: processing.multiacquiring.multiacquiring_emission.EmoneyResponse
	(*EmoneyRequest)(nil),    // 3: processing.multiacquiring.multiacquiring_emission.EmoneyRequest
	(*emptypb.Empty)(nil),    // 4: google.protobuf.Empty
}
var file_inner_processing_grpc_multiacquiring_emission_proto_depIdxs = []int32{
	0, // 0: processing.multiacquiring.multiacquiring_emission.EmissionResponse.Value:type_name -> processing.multiacquiring.multiacquiring_emission.Value
	4, // 1: processing.multiacquiring.multiacquiring_emission.MultiacquiringEmission.GetEmission:input_type -> google.protobuf.Empty
	3, // 2: processing.multiacquiring.multiacquiring_emission.MultiacquiringEmission.ConfirmEmission:input_type -> processing.multiacquiring.multiacquiring_emission.EmoneyRequest
	1, // 3: processing.multiacquiring.multiacquiring_emission.MultiacquiringEmission.GetEmission:output_type -> processing.multiacquiring.multiacquiring_emission.EmissionResponse
	2, // 4: processing.multiacquiring.multiacquiring_emission.MultiacquiringEmission.ConfirmEmission:output_type -> processing.multiacquiring.multiacquiring_emission.EmoneyResponse
	3, // [3:5] is the sub-list for method output_type
	1, // [1:3] is the sub-list for method input_type
	1, // [1:1] is the sub-list for extension type_name
	1, // [1:1] is the sub-list for extension extendee
	0, // [0:1] is the sub-list for field type_name
}

func init() { file_inner_processing_grpc_multiacquiring_emission_proto_init() }
func file_inner_processing_grpc_multiacquiring_emission_proto_init() {
	if File_inner_processing_grpc_multiacquiring_emission_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_inner_processing_grpc_multiacquiring_emission_proto_rawDesc), len(file_inner_processing_grpc_multiacquiring_emission_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   4,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_inner_processing_grpc_multiacquiring_emission_proto_goTypes,
		DependencyIndexes: file_inner_processing_grpc_multiacquiring_emission_proto_depIdxs,
		MessageInfos:      file_inner_processing_grpc_multiacquiring_emission_proto_msgTypes,
	}.Build()
	File_inner_processing_grpc_multiacquiring_emission_proto = out.File
	file_inner_processing_grpc_multiacquiring_emission_proto_goTypes = nil
	file_inner_processing_grpc_multiacquiring_emission_proto_depIdxs = nil
}
