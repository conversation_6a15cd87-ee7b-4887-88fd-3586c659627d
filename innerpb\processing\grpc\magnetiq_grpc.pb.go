// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.5.1
// - protoc             v5.29.3
// source: inner/processing/grpc/magnetiq.proto

package grpc

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.64.0 or later.
const _ = grpc.SupportPackageIsVersion9

const (
	Magnetiq_PayIn_FullMethodName                              = "/processing.magnetiq.magnetiq.Magnetiq/PayIn"
	Magnetiq_OneClickPayIn_FullMethodName                      = "/processing.magnetiq.magnetiq.Magnetiq/OneClickPayIn"
	Magnetiq_ThreeDSConfirm_FullMethodName                     = "/processing.magnetiq.magnetiq.Magnetiq/ThreeDSConfirm"
	Magnetiq_ThreeDSResume_FullMethodName                      = "/processing.magnetiq.magnetiq.Magnetiq/ThreeDSResume"
	Magnetiq_PayOut_FullMethodName                             = "/processing.magnetiq.magnetiq.Magnetiq/PayOut"
	Magnetiq_GetBankTransactionStatus_FullMethodName           = "/processing.magnetiq.magnetiq.Magnetiq/GetBankTransactionStatus"
	Magnetiq_GetBankTransactionStatusUnformated_FullMethodName = "/processing.magnetiq.magnetiq.Magnetiq/GetBankTransactionStatusUnformated"
	Magnetiq_Refund_FullMethodName                             = "/processing.magnetiq.magnetiq.Magnetiq/Refund"
	Magnetiq_GooglePay_FullMethodName                          = "/processing.magnetiq.magnetiq.Magnetiq/GooglePay"
	Magnetiq_ApplePay_FullMethodName                           = "/processing.magnetiq.magnetiq.Magnetiq/ApplePay"
	Magnetiq_TwoStagePayIn_FullMethodName                      = "/processing.magnetiq.magnetiq.Magnetiq/TwoStagePayIn"
	Magnetiq_Charge_FullMethodName                             = "/processing.magnetiq.magnetiq.Magnetiq/Charge"
	Magnetiq_Cancel_FullMethodName                             = "/processing.magnetiq.magnetiq.Magnetiq/Cancel"
	Magnetiq_MakeToken_FullMethodName                          = "/processing.magnetiq.magnetiq.Magnetiq/MakeToken"
	Magnetiq_GetAcquirerIdentifier_FullMethodName              = "/processing.magnetiq.magnetiq.Magnetiq/GetAcquirerIdentifier"
	Magnetiq_ResolveVisaAlias_FullMethodName                   = "/processing.magnetiq.magnetiq.Magnetiq/ResolveVisaAlias"
	Magnetiq_PayOutByPhone_FullMethodName                      = "/processing.magnetiq.magnetiq.Magnetiq/PayOutByPhone"
)

// MagnetiqClient is the client API for Magnetiq service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type MagnetiqClient interface {
	PayIn(ctx context.Context, in *PayInRequestData, opts ...grpc.CallOption) (*PayInResponseData, error)
	OneClickPayIn(ctx context.Context, in *OneClickPayInRequestData, opts ...grpc.CallOption) (*PayInResponseData, error)
	ThreeDSConfirm(ctx context.Context, in *ThreeDSRequestData, opts ...grpc.CallOption) (*ThreeDSResponseData, error)
	ThreeDSResume(ctx context.Context, in *ThreeDSResumeRequest, opts ...grpc.CallOption) (*ThreeDSResumeResponse, error)
	PayOut(ctx context.Context, in *PayOutRequestData, opts ...grpc.CallOption) (*PayOutResponseData, error)
	GetBankTransactionStatus(ctx context.Context, in *BankTransactionStatusRequest, opts ...grpc.CallOption) (*BankTransactionStatusResponse, error)
	GetBankTransactionStatusUnformated(ctx context.Context, in *BankTransactionStatusUnformatedRequest, opts ...grpc.CallOption) (*BankTransactionStatusUnformatedResponse, error)
	Refund(ctx context.Context, in *RefundRequest, opts ...grpc.CallOption) (*RefundResponse, error)
	GooglePay(ctx context.Context, in *GooglePayRequestData, opts ...grpc.CallOption) (*GooglePayResponseData, error)
	ApplePay(ctx context.Context, in *ApplePayRequestData, opts ...grpc.CallOption) (*ApplePayResponseData, error)
	TwoStagePayIn(ctx context.Context, in *TwoStagePayInRequest, opts ...grpc.CallOption) (*TwoStagePayInResponse, error)
	Charge(ctx context.Context, in *ChargeRequest, opts ...grpc.CallOption) (*ChargeResponse, error)
	Cancel(ctx context.Context, in *CancelRequest, opts ...grpc.CallOption) (*CancelResponse, error)
	MakeToken(ctx context.Context, in *PayInRequestData, opts ...grpc.CallOption) (*PayInResponseData, error)
	GetAcquirerIdentifier(ctx context.Context, in *emptypb.Empty, opts ...grpc.CallOption) (*GetAcquirerIdentifierResponse, error)
	ResolveVisaAlias(ctx context.Context, in *ResolveVisaAliasRequest, opts ...grpc.CallOption) (*ResolveVisaAliasResponse, error)
	PayOutByPhone(ctx context.Context, in *PayOutByPhoneRequestData, opts ...grpc.CallOption) (*PayOutResponseByPhoneData, error)
}

type magnetiqClient struct {
	cc grpc.ClientConnInterface
}

func NewMagnetiqClient(cc grpc.ClientConnInterface) MagnetiqClient {
	return &magnetiqClient{cc}
}

func (c *magnetiqClient) PayIn(ctx context.Context, in *PayInRequestData, opts ...grpc.CallOption) (*PayInResponseData, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(PayInResponseData)
	err := c.cc.Invoke(ctx, Magnetiq_PayIn_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *magnetiqClient) OneClickPayIn(ctx context.Context, in *OneClickPayInRequestData, opts ...grpc.CallOption) (*PayInResponseData, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(PayInResponseData)
	err := c.cc.Invoke(ctx, Magnetiq_OneClickPayIn_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *magnetiqClient) ThreeDSConfirm(ctx context.Context, in *ThreeDSRequestData, opts ...grpc.CallOption) (*ThreeDSResponseData, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ThreeDSResponseData)
	err := c.cc.Invoke(ctx, Magnetiq_ThreeDSConfirm_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *magnetiqClient) ThreeDSResume(ctx context.Context, in *ThreeDSResumeRequest, opts ...grpc.CallOption) (*ThreeDSResumeResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ThreeDSResumeResponse)
	err := c.cc.Invoke(ctx, Magnetiq_ThreeDSResume_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *magnetiqClient) PayOut(ctx context.Context, in *PayOutRequestData, opts ...grpc.CallOption) (*PayOutResponseData, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(PayOutResponseData)
	err := c.cc.Invoke(ctx, Magnetiq_PayOut_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *magnetiqClient) GetBankTransactionStatus(ctx context.Context, in *BankTransactionStatusRequest, opts ...grpc.CallOption) (*BankTransactionStatusResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(BankTransactionStatusResponse)
	err := c.cc.Invoke(ctx, Magnetiq_GetBankTransactionStatus_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *magnetiqClient) GetBankTransactionStatusUnformated(ctx context.Context, in *BankTransactionStatusUnformatedRequest, opts ...grpc.CallOption) (*BankTransactionStatusUnformatedResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(BankTransactionStatusUnformatedResponse)
	err := c.cc.Invoke(ctx, Magnetiq_GetBankTransactionStatusUnformated_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *magnetiqClient) Refund(ctx context.Context, in *RefundRequest, opts ...grpc.CallOption) (*RefundResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(RefundResponse)
	err := c.cc.Invoke(ctx, Magnetiq_Refund_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *magnetiqClient) GooglePay(ctx context.Context, in *GooglePayRequestData, opts ...grpc.CallOption) (*GooglePayResponseData, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GooglePayResponseData)
	err := c.cc.Invoke(ctx, Magnetiq_GooglePay_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *magnetiqClient) ApplePay(ctx context.Context, in *ApplePayRequestData, opts ...grpc.CallOption) (*ApplePayResponseData, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ApplePayResponseData)
	err := c.cc.Invoke(ctx, Magnetiq_ApplePay_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *magnetiqClient) TwoStagePayIn(ctx context.Context, in *TwoStagePayInRequest, opts ...grpc.CallOption) (*TwoStagePayInResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(TwoStagePayInResponse)
	err := c.cc.Invoke(ctx, Magnetiq_TwoStagePayIn_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *magnetiqClient) Charge(ctx context.Context, in *ChargeRequest, opts ...grpc.CallOption) (*ChargeResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ChargeResponse)
	err := c.cc.Invoke(ctx, Magnetiq_Charge_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *magnetiqClient) Cancel(ctx context.Context, in *CancelRequest, opts ...grpc.CallOption) (*CancelResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(CancelResponse)
	err := c.cc.Invoke(ctx, Magnetiq_Cancel_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *magnetiqClient) MakeToken(ctx context.Context, in *PayInRequestData, opts ...grpc.CallOption) (*PayInResponseData, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(PayInResponseData)
	err := c.cc.Invoke(ctx, Magnetiq_MakeToken_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *magnetiqClient) GetAcquirerIdentifier(ctx context.Context, in *emptypb.Empty, opts ...grpc.CallOption) (*GetAcquirerIdentifierResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetAcquirerIdentifierResponse)
	err := c.cc.Invoke(ctx, Magnetiq_GetAcquirerIdentifier_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *magnetiqClient) ResolveVisaAlias(ctx context.Context, in *ResolveVisaAliasRequest, opts ...grpc.CallOption) (*ResolveVisaAliasResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ResolveVisaAliasResponse)
	err := c.cc.Invoke(ctx, Magnetiq_ResolveVisaAlias_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *magnetiqClient) PayOutByPhone(ctx context.Context, in *PayOutByPhoneRequestData, opts ...grpc.CallOption) (*PayOutResponseByPhoneData, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(PayOutResponseByPhoneData)
	err := c.cc.Invoke(ctx, Magnetiq_PayOutByPhone_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// MagnetiqServer is the server API for Magnetiq service.
// All implementations must embed UnimplementedMagnetiqServer
// for forward compatibility.
type MagnetiqServer interface {
	PayIn(context.Context, *PayInRequestData) (*PayInResponseData, error)
	OneClickPayIn(context.Context, *OneClickPayInRequestData) (*PayInResponseData, error)
	ThreeDSConfirm(context.Context, *ThreeDSRequestData) (*ThreeDSResponseData, error)
	ThreeDSResume(context.Context, *ThreeDSResumeRequest) (*ThreeDSResumeResponse, error)
	PayOut(context.Context, *PayOutRequestData) (*PayOutResponseData, error)
	GetBankTransactionStatus(context.Context, *BankTransactionStatusRequest) (*BankTransactionStatusResponse, error)
	GetBankTransactionStatusUnformated(context.Context, *BankTransactionStatusUnformatedRequest) (*BankTransactionStatusUnformatedResponse, error)
	Refund(context.Context, *RefundRequest) (*RefundResponse, error)
	GooglePay(context.Context, *GooglePayRequestData) (*GooglePayResponseData, error)
	ApplePay(context.Context, *ApplePayRequestData) (*ApplePayResponseData, error)
	TwoStagePayIn(context.Context, *TwoStagePayInRequest) (*TwoStagePayInResponse, error)
	Charge(context.Context, *ChargeRequest) (*ChargeResponse, error)
	Cancel(context.Context, *CancelRequest) (*CancelResponse, error)
	MakeToken(context.Context, *PayInRequestData) (*PayInResponseData, error)
	GetAcquirerIdentifier(context.Context, *emptypb.Empty) (*GetAcquirerIdentifierResponse, error)
	ResolveVisaAlias(context.Context, *ResolveVisaAliasRequest) (*ResolveVisaAliasResponse, error)
	PayOutByPhone(context.Context, *PayOutByPhoneRequestData) (*PayOutResponseByPhoneData, error)
	mustEmbedUnimplementedMagnetiqServer()
}

// UnimplementedMagnetiqServer must be embedded to have
// forward compatible implementations.
//
// NOTE: this should be embedded by value instead of pointer to avoid a nil
// pointer dereference when methods are called.
type UnimplementedMagnetiqServer struct{}

func (UnimplementedMagnetiqServer) PayIn(context.Context, *PayInRequestData) (*PayInResponseData, error) {
	return nil, status.Errorf(codes.Unimplemented, "method PayIn not implemented")
}
func (UnimplementedMagnetiqServer) OneClickPayIn(context.Context, *OneClickPayInRequestData) (*PayInResponseData, error) {
	return nil, status.Errorf(codes.Unimplemented, "method OneClickPayIn not implemented")
}
func (UnimplementedMagnetiqServer) ThreeDSConfirm(context.Context, *ThreeDSRequestData) (*ThreeDSResponseData, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ThreeDSConfirm not implemented")
}
func (UnimplementedMagnetiqServer) ThreeDSResume(context.Context, *ThreeDSResumeRequest) (*ThreeDSResumeResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ThreeDSResume not implemented")
}
func (UnimplementedMagnetiqServer) PayOut(context.Context, *PayOutRequestData) (*PayOutResponseData, error) {
	return nil, status.Errorf(codes.Unimplemented, "method PayOut not implemented")
}
func (UnimplementedMagnetiqServer) GetBankTransactionStatus(context.Context, *BankTransactionStatusRequest) (*BankTransactionStatusResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetBankTransactionStatus not implemented")
}
func (UnimplementedMagnetiqServer) GetBankTransactionStatusUnformated(context.Context, *BankTransactionStatusUnformatedRequest) (*BankTransactionStatusUnformatedResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetBankTransactionStatusUnformated not implemented")
}
func (UnimplementedMagnetiqServer) Refund(context.Context, *RefundRequest) (*RefundResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Refund not implemented")
}
func (UnimplementedMagnetiqServer) GooglePay(context.Context, *GooglePayRequestData) (*GooglePayResponseData, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GooglePay not implemented")
}
func (UnimplementedMagnetiqServer) ApplePay(context.Context, *ApplePayRequestData) (*ApplePayResponseData, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ApplePay not implemented")
}
func (UnimplementedMagnetiqServer) TwoStagePayIn(context.Context, *TwoStagePayInRequest) (*TwoStagePayInResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method TwoStagePayIn not implemented")
}
func (UnimplementedMagnetiqServer) Charge(context.Context, *ChargeRequest) (*ChargeResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Charge not implemented")
}
func (UnimplementedMagnetiqServer) Cancel(context.Context, *CancelRequest) (*CancelResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Cancel not implemented")
}
func (UnimplementedMagnetiqServer) MakeToken(context.Context, *PayInRequestData) (*PayInResponseData, error) {
	return nil, status.Errorf(codes.Unimplemented, "method MakeToken not implemented")
}
func (UnimplementedMagnetiqServer) GetAcquirerIdentifier(context.Context, *emptypb.Empty) (*GetAcquirerIdentifierResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetAcquirerIdentifier not implemented")
}
func (UnimplementedMagnetiqServer) ResolveVisaAlias(context.Context, *ResolveVisaAliasRequest) (*ResolveVisaAliasResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ResolveVisaAlias not implemented")
}
func (UnimplementedMagnetiqServer) PayOutByPhone(context.Context, *PayOutByPhoneRequestData) (*PayOutResponseByPhoneData, error) {
	return nil, status.Errorf(codes.Unimplemented, "method PayOutByPhone not implemented")
}
func (UnimplementedMagnetiqServer) mustEmbedUnimplementedMagnetiqServer() {}
func (UnimplementedMagnetiqServer) testEmbeddedByValue()                  {}

// UnsafeMagnetiqServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to MagnetiqServer will
// result in compilation errors.
type UnsafeMagnetiqServer interface {
	mustEmbedUnimplementedMagnetiqServer()
}

func RegisterMagnetiqServer(s grpc.ServiceRegistrar, srv MagnetiqServer) {
	// If the following call pancis, it indicates UnimplementedMagnetiqServer was
	// embedded by pointer and is nil.  This will cause panics if an
	// unimplemented method is ever invoked, so we test this at initialization
	// time to prevent it from happening at runtime later due to I/O.
	if t, ok := srv.(interface{ testEmbeddedByValue() }); ok {
		t.testEmbeddedByValue()
	}
	s.RegisterService(&Magnetiq_ServiceDesc, srv)
}

func _Magnetiq_PayIn_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(PayInRequestData)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MagnetiqServer).PayIn(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Magnetiq_PayIn_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MagnetiqServer).PayIn(ctx, req.(*PayInRequestData))
	}
	return interceptor(ctx, in, info, handler)
}

func _Magnetiq_OneClickPayIn_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(OneClickPayInRequestData)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MagnetiqServer).OneClickPayIn(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Magnetiq_OneClickPayIn_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MagnetiqServer).OneClickPayIn(ctx, req.(*OneClickPayInRequestData))
	}
	return interceptor(ctx, in, info, handler)
}

func _Magnetiq_ThreeDSConfirm_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ThreeDSRequestData)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MagnetiqServer).ThreeDSConfirm(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Magnetiq_ThreeDSConfirm_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MagnetiqServer).ThreeDSConfirm(ctx, req.(*ThreeDSRequestData))
	}
	return interceptor(ctx, in, info, handler)
}

func _Magnetiq_ThreeDSResume_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ThreeDSResumeRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MagnetiqServer).ThreeDSResume(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Magnetiq_ThreeDSResume_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MagnetiqServer).ThreeDSResume(ctx, req.(*ThreeDSResumeRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Magnetiq_PayOut_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(PayOutRequestData)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MagnetiqServer).PayOut(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Magnetiq_PayOut_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MagnetiqServer).PayOut(ctx, req.(*PayOutRequestData))
	}
	return interceptor(ctx, in, info, handler)
}

func _Magnetiq_GetBankTransactionStatus_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BankTransactionStatusRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MagnetiqServer).GetBankTransactionStatus(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Magnetiq_GetBankTransactionStatus_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MagnetiqServer).GetBankTransactionStatus(ctx, req.(*BankTransactionStatusRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Magnetiq_GetBankTransactionStatusUnformated_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BankTransactionStatusUnformatedRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MagnetiqServer).GetBankTransactionStatusUnformated(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Magnetiq_GetBankTransactionStatusUnformated_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MagnetiqServer).GetBankTransactionStatusUnformated(ctx, req.(*BankTransactionStatusUnformatedRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Magnetiq_Refund_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RefundRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MagnetiqServer).Refund(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Magnetiq_Refund_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MagnetiqServer).Refund(ctx, req.(*RefundRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Magnetiq_GooglePay_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GooglePayRequestData)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MagnetiqServer).GooglePay(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Magnetiq_GooglePay_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MagnetiqServer).GooglePay(ctx, req.(*GooglePayRequestData))
	}
	return interceptor(ctx, in, info, handler)
}

func _Magnetiq_ApplePay_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ApplePayRequestData)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MagnetiqServer).ApplePay(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Magnetiq_ApplePay_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MagnetiqServer).ApplePay(ctx, req.(*ApplePayRequestData))
	}
	return interceptor(ctx, in, info, handler)
}

func _Magnetiq_TwoStagePayIn_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(TwoStagePayInRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MagnetiqServer).TwoStagePayIn(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Magnetiq_TwoStagePayIn_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MagnetiqServer).TwoStagePayIn(ctx, req.(*TwoStagePayInRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Magnetiq_Charge_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ChargeRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MagnetiqServer).Charge(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Magnetiq_Charge_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MagnetiqServer).Charge(ctx, req.(*ChargeRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Magnetiq_Cancel_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CancelRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MagnetiqServer).Cancel(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Magnetiq_Cancel_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MagnetiqServer).Cancel(ctx, req.(*CancelRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Magnetiq_MakeToken_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(PayInRequestData)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MagnetiqServer).MakeToken(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Magnetiq_MakeToken_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MagnetiqServer).MakeToken(ctx, req.(*PayInRequestData))
	}
	return interceptor(ctx, in, info, handler)
}

func _Magnetiq_GetAcquirerIdentifier_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(emptypb.Empty)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MagnetiqServer).GetAcquirerIdentifier(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Magnetiq_GetAcquirerIdentifier_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MagnetiqServer).GetAcquirerIdentifier(ctx, req.(*emptypb.Empty))
	}
	return interceptor(ctx, in, info, handler)
}

func _Magnetiq_ResolveVisaAlias_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ResolveVisaAliasRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MagnetiqServer).ResolveVisaAlias(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Magnetiq_ResolveVisaAlias_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MagnetiqServer).ResolveVisaAlias(ctx, req.(*ResolveVisaAliasRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Magnetiq_PayOutByPhone_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(PayOutByPhoneRequestData)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MagnetiqServer).PayOutByPhone(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Magnetiq_PayOutByPhone_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MagnetiqServer).PayOutByPhone(ctx, req.(*PayOutByPhoneRequestData))
	}
	return interceptor(ctx, in, info, handler)
}

// Magnetiq_ServiceDesc is the grpc.ServiceDesc for Magnetiq service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var Magnetiq_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "processing.magnetiq.magnetiq.Magnetiq",
	HandlerType: (*MagnetiqServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "PayIn",
			Handler:    _Magnetiq_PayIn_Handler,
		},
		{
			MethodName: "OneClickPayIn",
			Handler:    _Magnetiq_OneClickPayIn_Handler,
		},
		{
			MethodName: "ThreeDSConfirm",
			Handler:    _Magnetiq_ThreeDSConfirm_Handler,
		},
		{
			MethodName: "ThreeDSResume",
			Handler:    _Magnetiq_ThreeDSResume_Handler,
		},
		{
			MethodName: "PayOut",
			Handler:    _Magnetiq_PayOut_Handler,
		},
		{
			MethodName: "GetBankTransactionStatus",
			Handler:    _Magnetiq_GetBankTransactionStatus_Handler,
		},
		{
			MethodName: "GetBankTransactionStatusUnformated",
			Handler:    _Magnetiq_GetBankTransactionStatusUnformated_Handler,
		},
		{
			MethodName: "Refund",
			Handler:    _Magnetiq_Refund_Handler,
		},
		{
			MethodName: "GooglePay",
			Handler:    _Magnetiq_GooglePay_Handler,
		},
		{
			MethodName: "ApplePay",
			Handler:    _Magnetiq_ApplePay_Handler,
		},
		{
			MethodName: "TwoStagePayIn",
			Handler:    _Magnetiq_TwoStagePayIn_Handler,
		},
		{
			MethodName: "Charge",
			Handler:    _Magnetiq_Charge_Handler,
		},
		{
			MethodName: "Cancel",
			Handler:    _Magnetiq_Cancel_Handler,
		},
		{
			MethodName: "MakeToken",
			Handler:    _Magnetiq_MakeToken_Handler,
		},
		{
			MethodName: "GetAcquirerIdentifier",
			Handler:    _Magnetiq_GetAcquirerIdentifier_Handler,
		},
		{
			MethodName: "ResolveVisaAlias",
			Handler:    _Magnetiq_ResolveVisaAlias_Handler,
		},
		{
			MethodName: "PayOutByPhone",
			Handler:    _Magnetiq_PayOutByPhone_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "inner/processing/grpc/magnetiq.proto",
}
