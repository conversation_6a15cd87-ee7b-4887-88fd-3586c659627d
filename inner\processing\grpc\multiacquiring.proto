edition = "2023";

package processing.multiacquiring.multiacquiring;

option go_package = "git.local/sensitive/innerpb/processing/grpc";

import "mvp/proto/logger.proto";
import "google/protobuf/struct.proto";
import "google/protobuf/timestamp.proto";
import "google/protobuf/empty.proto";
import "inner/processing/grpc/transaction_status.proto";
import "inner/processing/grpc/transaction.proto";

service MultiAcquiring {
  rpc PayIn(PayInRequestData) returns (PayInResponseData) {}
  rpc OneClickPayIn(OneClickPayInRequestData) returns (PayInResponseData) {}
  rpc ThreeDSConfirm(ThreeDSRequestData) returns (ThreeDSResponseData) {}
  rpc ThreeDSResume(ThreeDSResumeRequest) returns (ThreeDSResumeResponse) {}
  rpc PayOut(PayOutRequestData) returns (PayOutResponseData) {}
  rpc GetBankTransactionStatus(BankTransactionStatusRequest) returns (BankTransactionStatusResponse) {}
  rpc GetBankTransactionStatusUnformated(BankTransactionStatusUnformatedRequest) returns (BankTransactionStatusUnformatedResponse) {}
  rpc Refund(RefundRequest) returns (RefundResponse) {}
  rpc GooglePay(GooglePayRequestData) returns (GooglePayResponseData) {}
  rpc ApplePay(ApplePayRequestData) returns (ApplePayResponseData) {}
  rpc TwoStagePayIn(TwoStagePayInRequest) returns (TwoStagePayInResponse) {}
  rpc Charge(ChargeRequest) returns (ChargeResponse) {}
  rpc Cancel(CancelRequest) returns (CancelResponse) {}
  rpc MakeToken(PayInRequestData) returns (PayInResponseData) {}
  rpc ResolveVisaAlias(ResolveVisaAliasRequest) returns (ResolveVisaAliasResponse) {}
  rpc GetAcquirerIdentifier(google.protobuf.Empty) returns (GetAcquirerIdentifierResponse) {}
  rpc PayOutByPhone(PayOutByPhoneRequestData) returns (PayOutResponseByPhoneData) {}
}

message GetAcquirerIdentifierResponse{
  string acquirer_identifier = 1;
}

message PayInRequestData {
  uint64 transaction_id = 1;
  bool is_hold = 2;
  double amount = 3;
  string description = 4;
  CardDataForBank card = 5;
  UserDataForBank user = 6;
  TerminalDataForBank terminal = 7;
  uint64 aggregated_type_id = 8;
  CardInfo card_info = 9;
  MerchantInfo merchant_info = 10;
  google.protobuf.Timestamp transaction_created_at = 11;
  bool get_form = 12;
  string user_ip_address = 13;
}

message OneClickPayInRequestData {
  uint64 transaction_id = 1;
  string card_id = 2;
  double amount = 3;
  string description = 4;
  UserDataForBank user = 5;
  TerminalDataForBank terminal = 6;
  uint64 aggregated_type_id = 7;
  CardInfo card_info = 8;
  MerchantInfo merchant_info = 9;
  google.protobuf.Timestamp transaction_created_at = 10;
  string user_ip_address = 11;
}

message UserDataForBank {
  string id = 1;
  string email = 2;
  string name = 3;
  string phone = 4;
}

message TerminalDataForBank {
  uint64 id = 1;
  uint64 acquirer_id = 2;
  string acquirer_code = 3;
  google.protobuf.Struct config = 4 [(mvp.FieldLoggerLevel) = Hidden];
}

message CardDataForBank {
  bytes pan = 1 [(mvp.FieldLoggerLevel) = Hidden];
  bytes exp_month = 2;
  bytes exp_year = 3;
  bytes cvc = 4 [(mvp.FieldLoggerLevel) = Hidden];
  bytes full_name = 5;
  string token = 6;
  bool save = 7;
}

message PayInResponseData {
  uint64 transaction_id = 1;
  string bank_reference_id = 2;
  processing.transaction.transaction_status.EnumTransactionStatus transaction_status = 3;
  string card_id = 4;
  ThreeDSDataFromBank threeDS = 5;
  BankResponse bank_response = 6;
  string bank_order_id = 7;
  Fingerprint fingerprint = 8;
}

message ThreeDSDataFromBank {
  string action = 1;
  google.protobuf.Struct params = 2;
  bytes template = 3;
}

message ThreeDSRequestData {
  uint64 transaction_id = 1;
  string bank_reference_id = 2;
  string pares = 3;
  string md = 4;
  double amount = 5;
  string cres = 6;
  TerminalDataForBank terminal = 7;
  string bank_order_id = 8;
  bool is_hold = 9;
}

message ThreeDSResponseData {
  uint64 transaction_id = 1;
  string bank_reference_id = 2;
  string bank_order_id = 3;
  processing.transaction.transaction_status.EnumTransactionStatus transaction_status = 4;
  string card_token = 5;
  BankResponse bank_response = 6;
}

message PayOutRequestData {
  uint64 transaction_id = 1;
  bytes pan = 2 [(mvp.FieldLoggerLevel) = Hidden];
  double amount = 3;
  UserDataForBank user = 4;
  TerminalDataForBank terminal = 5;
  PayOutCard card = 6;
  uint64 aggregated_type_id = 7;
  CardInfo card_info = 8;
  MerchantInfo merchant_info = 9;
  google.protobuf.Timestamp transaction_created_at = 10;
  string c_info = 11;
}

message PayOutByPhoneRequestData {
  uint64 transaction_id = 1;
  string visa_alias_token = 2;
  double amount = 3;
  UserDataForBank user = 4;
  TerminalDataForBank terminal = 5;
  PayOutCard card = 6;
  uint64 aggregated_type_id = 7;
  CardInfo card_info = 8;
  MerchantInfo merchant_info = 9;
  google.protobuf.Timestamp transaction_created_at = 10;
  string c_info = 11;
}

message PayOutResponseByPhoneData {
  uint64 transaction_id = 1;
  string bank_reference_id = 2;
  string bank_order_id = 3;
  processing.transaction.transaction_status.EnumTransactionStatus transaction_status = 4;
  BankResponse bank_response = 5;
}

message PayOutCard {
  bytes pan = 1 [(mvp.FieldLoggerLevel) = Hidden];
  bytes exp_month = 2;
  bytes exp_year = 3;
  bytes cvc = 4 [(mvp.FieldLoggerLevel) = Hidden];
}

message PayOutResponseData {
  uint64 transaction_id = 1;
  string bank_reference_id = 2;
  string bank_order_id = 3;
  processing.transaction.transaction_status.EnumTransactionStatus transaction_status = 4;
  BankResponse bank_response = 5;
}

message BankTransactionStatusRequest{
  uint64 transaction_id = 1;
  string transaction_type = 2;
  string acquirer_code = 3;
  google.protobuf.Struct config = 4;
  string bank_reference_id = 5;
  string bank_order_id = 6;
  TerminalDataForBank terminal = 7;
  google.protobuf.Timestamp transaction_created_at = 8;
}

message BankTransactionStatusResponse{
  uint64 transaction_id = 1;
  string bank_reference_id = 2;
  processing.transaction.transaction_status.EnumTransactionStatus transaction_status = 3;
  BankResponse bank_response = 4;
}

message BankTransactionStatusUnformatedRequest{
  uint64 transaction_id = 1;
  string transaction_type = 2;
  string acquirer_code = 3;
  google.protobuf.Struct config = 4;
  string bank_reference_id = 5;
  string bank_order_id = 6;
  TerminalDataForBank terminal = 7;
  google.protobuf.Timestamp transaction_created_at = 8;
}

message BankTransactionStatusUnformatedResponse{
  uint64 transaction_id = 1;
  string bank_message = 2;
}

message RefundRequest {
  uint64 transaction_id = 1;
  double refund_amount = 2;
  TerminalDataForBank terminal = 3;
  string bank_reference_id = 4;
  string bank_order_id = 5;
  google.protobuf.Timestamp transaction_created_at = 6;
  double transaction_amount = 7;
}

message RefundResponse {
  uint64 transaction_id = 1;
  BankResponse bank_response = 2;
  string bank_reference_id = 3;
}

message CardInfo {
  uint64 ips_id = 1;
  uint64 issuer_id = 2;
  uint64 country_id = 3;
}

message MerchantInfo {
  uint64 project_id = 1;
  uint64 merchant_id = 2;
}

message GooglePayRequestData {
  uint64 transaction_id = 1;
  double amount = 2;
  string description = 3;
  CardDataForBank card = 4;
  UserDataForBank user = 5;
  TerminalDataForBank terminal = 6;
  CardInfo card_info = 7;
  MerchantInfo merchant_info = 8;
  uint64 aggregated_type_id = 9;
  string token = 10;
  string currency = 11;
  string eci_indicator = 12;
  string cryptogram = 13;
  google.protobuf.Timestamp transaction_created_at = 14;
  string user_ip_address = 15;
}

message GooglePayResponseData {
  uint64 transaction_id = 1;
  string bank_reference_id = 2;
  processing.transaction.transaction_status.EnumTransactionStatus transaction_status = 3;
  ThreeDSDataFromBank threeDS = 4;
  BankResponse bank_response = 5;
  string bank_order_id = 6;
  Fingerprint fingerprint = 7;
}

message ApplePayRequestData {
  uint64 transaction_id = 1;
  bool is_hold = 2;
  double amount = 3;
  string description = 4;
  CardDataForBank card = 5;
  UserDataForBank user = 6;
  TerminalDataForBank terminal = 7;
  uint64 transaction_type_id = 8;
  CardInfo card_info = 9;
  MerchantInfo merchant_info = 10;
  bytes Tavv = 11;
  string Eci = 12;
  google.protobuf.Timestamp transaction_created_at = 13;
  uint64 aggregated_type_id = 14;
  string user_ip_address = 15;
}

message ApplePayResponseData {
  uint64 transaction_id = 1;
  string bank_reference_id = 2;
  processing.transaction.transaction_status.EnumTransactionStatus transaction_status = 3;
  string card_id = 4;
  ThreeDSDataFromBank threeDS = 5;
  BankResponse bank_response = 6;
  string bank_order_id = 7;
}

message TwoStagePayInRequest {
  uint64 transaction_id = 1;
  bool is_hold = 2;
  double amount = 3;
  string description = 4;
  CardDataForBank card = 5;
  UserDataForBank user = 6;
  TerminalDataForBank terminal = 7;
  uint64 transaction_type_id = 8;
  CardInfo card_info = 9;
  MerchantInfo merchant_info = 10;
  uint64 aggregated_type_id = 11;
}

message ChargeRequest {
  uint64 transaction_id = 1;
  double amount = 2;
  TerminalDataForBank terminal = 3;
  string bank_reference_id = 4;
  string bank_order_id = 5;
  google.protobuf.Timestamp transaction_created_at = 6;
}

message CancelRequest {
  uint64 transaction_id = 1;
  double amount = 2;
  TerminalDataForBank terminal = 3;
  string bank_reference_id = 4;
  string bank_order_id = 5;
  string description = 6;
  google.protobuf.Timestamp transaction_created_at = 7;
}

message TwoStagePayInResponse {
  uint64 transaction_id = 1;
  string bank_reference_id = 2;
  processing.transaction.transaction_status.EnumTransactionStatus transaction_status = 3;
  string card_id = 4;
  ThreeDSDataFromBank threeDS = 5;
  BankResponse bank_response = 6;
  string bank_order_id = 7;
}

message ChargeResponse {
  uint64 transaction_id = 1;
  string bank_reference_id = 2;
  string bank_order_id = 3;
  BankResponse bank_response = 4;
}

message CancelResponse {
  uint64 transaction_id = 1;
  string bank_reference_id = 2;
  string bank_order_id = 3;
  BankResponse bank_response = 4;
}

message Fingerprint {
  string method_url = 1;
  string method_data = 2;
}

message ThreeDSResumeRequest{
  uint64 transaction_id = 1;
  string bank_reference_id = 2;
  TerminalDataForBank terminal = 3;
  string bank_order_id = 4;
  bool is_hold = 5;
}

message ThreeDSResumeResponse{
  uint64 transaction_id = 1;
  string bank_order_id = 2;
  string bank_reference_id = 3;
  processing.transaction.transaction_status.EnumTransactionStatus transaction_status = 4;
  string card_token = 5;
  ThreeDSDataFromBank threeDS = 6;
  BankResponse bank_response = 7;
}

message BankResponse {
  string message  = 1;
  string code = 2;
  string integration_code = 3;
  string integration_message = 4;
}

message ResolveVisaAliasRequest {
  string phone_number = 1;
  TerminalDataForBank terminal = 3;
}

message ResolveVisaAliasResponse {
  string issuer_name = 1;
  string recipient_name = 2;
  string card_type = 3;
  BankResponse bank_response = 4;
  string visa_alias_token = 5;
}