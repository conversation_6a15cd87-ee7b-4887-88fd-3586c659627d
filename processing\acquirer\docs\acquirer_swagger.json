{"schemes": ["https", "http"], "swagger": "2.0", "info": {"description": "acquirer processing", "title": "acquirer", "contact": {}, "version": "1.0.0"}, "host": "api-dev.processing.kz", "basePath": "/acquirer", "paths": {"/api/v1/acquirer": {"post": {"security": [{"bearerAuth": []}], "consumes": ["application/json"], "produces": ["application/json"], "tags": ["acquirers"], "summary": "Создание эквайера", "parameters": [{"description": "Acquirer Data", "name": "data", "in": "body", "required": true, "schema": {"$ref": "#/definitions/schema.Acquirer"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/middlewares.Response-string"}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/middlewares.Response-middlewares_Empty"}}}}}, "/api/v1/acquirer/{acquirer_id}": {"put": {"security": [{"bearerAuth": []}], "consumes": ["application/json"], "produces": ["application/json"], "tags": ["acquirers"], "summary": "Обновление эквайера по айди", "parameters": [{"type": "integer", "description": "Acquirer ID", "name": "acquirer_id", "in": "path", "required": true}, {"description": "Acquirer Data", "name": "data", "in": "body", "required": true, "schema": {"$ref": "#/definitions/schema.Acquirer"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/middlewares.Response-middlewares_Empty"}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/middlewares.Response-middlewares_Empty"}}}}}, "/api/v1/acquirers": {"get": {"security": [{"bearerAuth": []}], "produces": ["application/json"], "tags": ["acquirers"], "summary": "Получение списка эквайеров", "parameters": [{"type": "integer", "description": "per_page", "name": "per_page", "in": "query"}, {"type": "integer", "description": "page", "name": "page", "in": "query"}, {"type": "integer", "name": "acquirer_id", "in": "query"}, {"type": "boolean", "name": "is_active", "in": "query"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/middlewares.Response-array_schema_AcquirerTerminal"}, "headers": {"x-page": {"type": "int", "description": "1"}, "x-per-page": {"type": "int", "description": "1"}, "x-total": {"type": "int", "description": "11"}}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/middlewares.Response-middlewares_Empty"}}}}}, "/api/v1/acquirers/active": {"get": {"security": [{"bearerAuth": []}], "produces": ["application/json"], "tags": ["acquirers"], "summary": "Получение списка активных эквайеров", "parameters": [{"type": "integer", "description": "per_page", "name": "per_page", "in": "query"}, {"type": "integer", "description": "page", "name": "page", "in": "query"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/middlewares.Response-array_model_Acquirer"}, "headers": {"x-page": {"type": "int", "description": "1"}, "x-per-page": {"type": "int", "description": "1"}, "x-total": {"type": "int", "description": "11"}}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/middlewares.Response-middlewares_Empty"}}}}}, "/api/v1/acquirers/{acquirer_id}/activate-terminals": {"put": {"security": [{"bearerAuth": []}], "description": "Включить все терминалы (статус - 1), смотрит где терминал отключен (статус - 3)", "produces": ["application/json"], "tags": ["acquirers/global-on/off"], "summary": "Обновить статус на \"активен\" и включить все связанные терминалы", "parameters": [{"type": "integer", "description": "Acquirer id", "name": "acquirer_id", "in": "path", "required": true}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/middlewares.Response-schema_UpdateTerminalsResponse"}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/middlewares.Response-middlewares_Empty"}}}}}, "/api/v1/acquirers/{acquirer_id}/deactivate-terminals": {"put": {"security": [{"bearerAuth": []}], "description": "Отключить все терминалы (статус - 3), смотрит где терминал включен (статус - 1)", "produces": ["application/json"], "tags": ["acquirers/global-on/off"], "summary": "Обновить статус на \"неактивен\" и отключить все связанные терминалы", "parameters": [{"type": "integer", "description": "Acquirer id", "name": "acquirer_id", "in": "path", "required": true}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/middlewares.Response-schema_UpdateTerminalsResponse"}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/middlewares.Response-middlewares_Empty"}}}}}, "/api/v1/acquirers/{acquirer_id}/show": {"get": {"security": [{"bearerAuth": []}], "produces": ["application/json"], "tags": ["acquirers"], "summary": "Получение эквайера по айди", "parameters": [{"type": "integer", "description": "Acquirer ID", "name": "acquirer_id", "in": "path", "required": true}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/middlewares.Response-array_model_Acquirer"}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/middlewares.Response-middlewares_Empty"}}}}}, "/api/v1/bank": {"post": {"security": [{"bearerAuth": []}], "consumes": ["application/json"], "produces": ["application/json"], "tags": ["banks"], "summary": "Создание банка", "parameters": [{"description": "Bank Data", "name": "data", "in": "body", "required": true, "schema": {"$ref": "#/definitions/schema.Bank"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/middlewares.Response-middlewares_Empty"}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/middlewares.Response-middlewares_Empty"}}}}}, "/api/v1/bank-bin": {"post": {"security": [{"bearerAuth": []}], "consumes": ["application/json"], "produces": ["application/json"], "tags": ["bank-bins"], "summary": "Создание бина", "parameters": [{"description": "Bin Data", "name": "data", "in": "body", "required": true, "schema": {"$ref": "#/definitions/schema.BankBin"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/middlewares.Response-middlewares_Empty"}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/middlewares.Response-middlewares_Empty"}}}}}, "/api/v1/bank-bin/{id}": {"put": {"security": [{"bearerAuth": []}], "consumes": ["application/json"], "produces": ["application/json"], "tags": ["bank-bins"], "summary": "Обновление бина по айди", "parameters": [{"type": "integer", "description": "Bank Bin ID", "name": "id", "in": "path", "required": true}, {"description": "Bin Data", "name": "data", "in": "body", "required": true, "schema": {"$ref": "#/definitions/schema.BankBin"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/middlewares.Response-middlewares_Empty"}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/middlewares.Response-middlewares_Empty"}}}}, "delete": {"security": [{"bearerAuth": []}], "produces": ["application/json"], "tags": ["bank-bins"], "summary": "Удаление бина по айди", "parameters": [{"type": "integer", "description": "Bank Bin ID", "name": "id", "in": "path", "required": true}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/middlewares.Response-middlewares_Empty"}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/middlewares.Response-middlewares_Empty"}}}}}, "/api/v1/bank-bins": {"get": {"security": [{"bearerAuth": []}], "produces": ["application/json"], "tags": ["bank-bins"], "summary": "Получение списка всех бинов", "parameters": [{"type": "integer", "description": "per_page", "name": "per_page", "in": "query"}, {"type": "integer", "description": "page", "name": "page", "in": "query"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/middlewares.Response-array_model_BankBin"}, "headers": {"x-page": {"type": "int", "description": "1"}, "x-per-page": {"type": "int", "description": "1"}, "x-total": {"type": "int", "description": "11"}}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/middlewares.Response-middlewares_Empty"}}}}}, "/api/v1/bank/{bank_id}": {"put": {"security": [{"bearerAuth": []}], "consumes": ["application/json"], "produces": ["application/json"], "tags": ["banks"], "summary": "Обновление данных банка по айди", "parameters": [{"type": "integer", "description": "Bank ID", "name": "bank_id", "in": "path", "required": true}, {"description": "Bank Data", "name": "data", "in": "body", "required": true, "schema": {"$ref": "#/definitions/schema.Bank"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/middlewares.Response-middlewares_Empty"}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/middlewares.Response-middlewares_Empty"}}}}, "delete": {"security": [{"bearerAuth": []}], "produces": ["application/json"], "tags": ["banks"], "summary": "Удаление банка по айди", "parameters": [{"type": "integer", "description": "Bank ID", "name": "bank_id", "in": "path", "required": true}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/middlewares.Response-middlewares_Empty"}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/middlewares.Response-middlewares_Empty"}}}}}, "/api/v1/bank/{bank_id}/countries": {"get": {"security": [{"bearerAuth": []}], "produces": ["application/json"], "tags": ["banks"], "summary": "Получение списка все страны банка", "parameters": [{"type": "integer", "description": "Bank ID", "name": "bank_id", "in": "path", "required": true}, {"type": "integer", "description": "per_page", "name": "per_page", "in": "query"}, {"type": "integer", "description": "page", "name": "page", "in": "query"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/middlewares.Response-array_model_Country"}, "headers": {"x-page": {"type": "int", "description": "1"}, "x-per-page": {"type": "int", "description": "1"}, "x-total": {"type": "int", "description": "11"}}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/middlewares.Response-middlewares_Empty"}}}}}, "/api/v1/banks": {"get": {"security": [{"bearerAuth": []}], "produces": ["application/json"], "tags": ["banks"], "summary": "Получение списка всех банков", "operationId": "getAllBanks", "parameters": [{"type": "integer", "description": "per_page", "name": "per_page", "in": "query"}, {"type": "integer", "description": "page", "name": "page", "in": "query"}, {"type": "string", "name": "search", "in": "query"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/middlewares.Response-array_model_Bank"}, "headers": {"x-page": {"type": "int", "description": "1"}, "x-per-page": {"type": "int", "description": "1"}, "x-total": {"type": "int", "description": "11"}}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/middlewares.Response-middlewares_Empty"}}}}}, "/api/v1/banks/{name}": {"get": {"security": [{"bearerAuth": []}], "produces": ["application/json"], "tags": ["banks"], "summary": "Получение списка банков по имени", "operationId": "getBanksByName", "parameters": [{"type": "string", "description": "name", "name": "name", "in": "path", "required": true}, {"type": "integer", "description": "per_page", "name": "per_page", "in": "query"}, {"type": "integer", "description": "page", "name": "page", "in": "query"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/middlewares.Response-array_model_Bank"}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/middlewares.Response-middlewares_Empty"}}}}}, "/api/v1/base-rule": {"post": {"description": "Базовое правило состоит только из project_id, payment_type (он всегда активен и is_base = true)", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["balancer"], "summary": "Создание базового правила и выставление процентажа", "parameters": [{"description": "CreateBaseRuleRequest Data", "name": "data", "in": "body", "required": true, "schema": {"$ref": "#/definitions/schema.CreateBaseRuleRequest"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/middlewares.Response-model_Rule"}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/middlewares.Response-middlewares_Empty"}}}}}, "/api/v1/countries": {"get": {"security": [{"bearerAuth": []}], "produces": ["application/json"], "tags": ["countries"], "summary": "Получение списка всех стран", "operationId": "getAllCountries", "parameters": [{"type": "integer", "description": "per_page", "name": "per_page", "in": "query"}, {"type": "integer", "description": "page", "name": "page", "in": "query"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/middlewares.Response-array_model_Country"}, "headers": {"x-page": {"type": "int", "description": "1"}, "x-per-page": {"type": "int", "description": "1"}, "x-total": {"type": "int", "description": "11"}}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/middlewares.Response-middlewares_Empty"}}}}}, "/api/v1/countries/{name}": {"get": {"security": [{"bearerAuth": []}], "produces": ["application/json"], "tags": ["countries"], "summary": "Получение списка стран по названию", "operationId": "getCountriesByName", "parameters": [{"type": "string", "description": "name", "name": "name", "in": "path", "required": true}, {"type": "integer", "description": "per_page", "name": "per_page", "in": "query"}, {"type": "integer", "description": "page", "name": "page", "in": "query"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/middlewares.Response-array_model_CountryBasic"}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/middlewares.Response-middlewares_Empty"}}}}}, "/api/v1/country": {"post": {"security": [{"bearerAuth": []}], "consumes": ["application/json"], "produces": ["application/json"], "tags": ["countries"], "summary": "Создание страны", "parameters": [{"description": "Country Data", "name": "data", "in": "body", "required": true, "schema": {"$ref": "#/definitions/schema.Country"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/middlewares.Response-middlewares_Empty"}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/middlewares.Response-middlewares_Empty"}}}}}, "/api/v1/country/bank": {"post": {"security": [{"bearerAuth": []}], "consumes": ["application/json"], "produces": ["application/json"], "tags": ["country_bank"], "summary": "Создание связи страны и банка", "parameters": [{"description": "CountryBank Data", "name": "data", "in": "body", "required": true, "schema": {"$ref": "#/definitions/schema.CountryBank"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/middlewares.Response-middlewares_Empty"}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/middlewares.Response-middlewares_Empty"}}}}}, "/api/v1/country/{country_id}": {"put": {"security": [{"bearerAuth": []}], "consumes": ["application/json"], "produces": ["application/json"], "tags": ["countries"], "summary": "Обновление страны по айди", "parameters": [{"type": "integer", "description": "Country ID", "name": "country_id", "in": "path", "required": true}, {"description": "Country Data", "name": "data", "in": "body", "required": true, "schema": {"$ref": "#/definitions/schema.Country"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/middlewares.Response-middlewares_Empty"}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/middlewares.Response-middlewares_Empty"}}}}, "delete": {"security": [{"bearerAuth": []}], "produces": ["application/json"], "tags": ["countries"], "summary": "Удаление страны по айди", "parameters": [{"type": "integer", "description": "Country ID", "name": "country_id", "in": "path", "required": true}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/middlewares.Response-middlewares_Empty"}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/middlewares.Response-middlewares_Empty"}}}}}, "/api/v1/country/{country_id}/bank/{bank_id}": {"delete": {"security": [{"bearerAuth": []}], "produces": ["application/json"], "tags": ["country_bank"], "summary": "Удаление связи страны и банка", "parameters": [{"type": "integer", "description": "Country ID", "name": "country_id", "in": "path", "required": true}, {"type": "integer", "description": "Bank ID", "name": "bank_id", "in": "path", "required": true}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/middlewares.Response-string"}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/middlewares.Response-middlewares_Empty"}}}}}, "/api/v1/country/{country_id}/banks": {"get": {"security": [{"bearerAuth": []}], "produces": ["application/json"], "tags": ["country_bank"], "summary": "Получение списка банков по айди страны", "parameters": [{"type": "integer", "description": "Country ID", "name": "country_id", "in": "path", "required": true}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/middlewares.Response-array_model_Bank"}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/middlewares.Response-middlewares_Empty"}}}}}, "/api/v1/ips": {"get": {"security": [{"bearerAuth": []}], "produces": ["application/json"], "tags": ["ips"], "summary": "Получение списка всех МПС", "parameters": [{"type": "integer", "description": "per_page", "name": "per_page", "in": "query"}, {"type": "integer", "description": "page", "name": "page", "in": "query"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/middlewares.Response-array_model_Ips"}, "headers": {"x-page": {"type": "int", "description": "1"}, "x-per-page": {"type": "int", "description": "1"}, "x-total": {"type": "int", "description": "11"}}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/middlewares.Response-middlewares_Empty"}}}}, "post": {"security": [{"bearerAuth": []}], "consumes": ["application/json"], "produces": ["application/json"], "tags": ["ips"], "summary": "Создание МПС", "parameters": [{"description": "Ips Data", "name": "data", "in": "body", "required": true, "schema": {"$ref": "#/definitions/schema.Ips"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/middlewares.Response-middlewares_Empty"}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/middlewares.Response-middlewares_Empty"}}}}}, "/api/v1/ips/{ips_id}": {"put": {"security": [{"bearerAuth": []}], "consumes": ["application/json"], "produces": ["application/json"], "tags": ["ips"], "summary": "Обновление МПС по айди", "parameters": [{"type": "integer", "description": "Ips ID", "name": "ips_id", "in": "path", "required": true}, {"description": "Ips Data", "name": "data", "in": "body", "required": true, "schema": {"$ref": "#/definitions/schema.Ips"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/middlewares.Response-middlewares_Empty"}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/middlewares.Response-middlewares_Empty"}}}}, "delete": {"security": [{"bearerAuth": []}], "produces": ["application/json"], "tags": ["ips"], "summary": "Удаление МПС по айди", "parameters": [{"type": "integer", "description": "Ips ID", "name": "ips_id", "in": "path", "required": true}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/middlewares.Response-middlewares_Empty"}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/middlewares.Response-middlewares_Empty"}}}}}, "/api/v1/issuers": {"get": {"security": [{"bearerAuth": []}], "produces": ["application/json"], "tags": ["banks"], "summary": "Получение списка всех банков-эмитентов", "parameters": [{"type": "integer", "description": "per_page", "name": "per_page", "in": "query"}, {"type": "integer", "description": "page", "name": "page", "in": "query"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/middlewares.Response-array_model_BankBins"}, "headers": {"x-page": {"type": "int", "description": "1"}, "x-per-page": {"type": "int", "description": "1"}, "x-total": {"type": "int", "description": "11"}}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/middlewares.Response-middlewares_Empty"}}}}}, "/api/v1/project/{project_id}/acquirers": {"get": {"security": [{"bearerAuth": []}], "produces": ["application/json"], "tags": ["projects"], "summary": "Получение списка эквайеров проекта", "parameters": [{"type": "integer", "description": "Project ID", "name": "project_id", "in": "path", "required": true}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/middlewares.Response-array_model_Acquirer"}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/middlewares.Response-middlewares_Empty"}}}}}, "/api/v1/project/{project_id}/acquirers/payout/balance": {"get": {"security": [{"bearerAuth": []}], "produces": ["application/json"], "tags": ["projects"], "summary": "Получить список всех терминалов на вывод с текущим остатком денежных средств", "parameters": [{"type": "integer", "description": "Project ID", "name": "project_id", "in": "path", "required": true}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/middlewares.Response-array_schema_GetBalanceResponse"}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/middlewares.Response-middlewares_Empty"}}}}}, "/api/v1/project/{project_id}/payment-types": {"get": {"security": [{"bearerAuth": []}], "produces": ["application/json"], "tags": ["projects"], "summary": "Получить список всех типов платежей", "parameters": [{"type": "integer", "description": "Project ID", "name": "project_id", "in": "path", "required": true}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/middlewares.Response-array_schema_ResponsePaymentType"}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/middlewares.Response-_Empty"}}}}}, "/api/v1/project/{project_id}/terminals": {"get": {"security": [{"bearerAuth": []}], "produces": ["application/json"], "tags": ["projects"], "summary": "Получить список всех терминалов", "parameters": [{"type": "integer", "description": "Project ID", "name": "project_id", "in": "path", "required": true}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/middlewares.Response-array_schema_TerminalResponse"}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/middlewares.Response-middlewares_Empty"}}}}}, "/api/v1/project/{project_id}/transaction-type/{transaction_type_id}/acquirers": {"get": {"security": [{"bearerAuth": []}], "produces": ["application/json"], "tags": ["projects"], "summary": "Получение списка эквайеров проекта", "parameters": [{"type": "integer", "description": "Project ID", "name": "project_id", "in": "path", "required": true}, {"type": "integer", "description": "Payment type", "name": "transaction_type_id", "in": "path", "required": true}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/middlewares.Response-array_model_Acquirer"}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/middlewares.Response-middlewares_Empty"}}}}}, "/api/v1/rule": {"post": {"security": [{"bearerAuth": []}], "description": "Для начала нужно создать базовое правило по роуту api/v1/base-rule", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["balancer"], "summary": "Создание правила и выставление процентажа", "parameters": [{"description": "CreateRuleRequest Data", "name": "data", "in": "body", "required": true, "schema": {"$ref": "#/definitions/schema.CreateRuleRequest"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/middlewares.Response-model_Rule"}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/middlewares.Response-middlewares_Empty"}}}}}, "/api/v1/rule/{rule_id}/activate": {"patch": {"security": [{"bearerAuth": []}], "produces": ["application/json"], "tags": ["balancer"], "summary": "Активация правила", "parameters": [{"type": "integer", "description": "Rule ID", "name": "rule_id", "in": "path", "required": true}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/middlewares.Response-middlewares_Empty"}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/middlewares.Response-middlewares_Empty"}}}}}, "/api/v1/rule/{rule_id}/deactivate": {"patch": {"security": [{"bearerAuth": []}], "produces": ["application/json"], "tags": ["balancer"], "summary": "Деактивация правила", "parameters": [{"type": "integer", "description": "Rule ID", "name": "rule_id", "in": "path", "required": true}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/middlewares.Response-middlewares_Empty"}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/middlewares.Response-middlewares_Empty"}}}}}, "/api/v1/rule/{rule_id}/move-down": {"patch": {"security": [{"bearerAuth": []}], "produces": ["application/json"], "tags": ["balancer"], "summary": "Уменьшение веса правила", "parameters": [{"type": "integer", "description": "ID правила", "name": "rule_id", "in": "path", "required": true}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/middlewares.Response-middlewares_Empty"}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/middlewares.Response-middlewares_Empty"}}}}}, "/api/v1/rule/{rule_id}/move-up": {"patch": {"security": [{"bearerAuth": []}], "produces": ["application/json"], "tags": ["balancer"], "summary": "Увеличение веса правила", "parameters": [{"type": "integer", "description": "ID правила", "name": "rule_id", "in": "path", "required": true}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/middlewares.Response-middlewares_Empty"}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/middlewares.Response-middlewares_Empty"}}}}}, "/api/v1/rule/{rule_id}/percentage": {"get": {"security": [{"bearerAuth": []}], "consumes": ["application/json"], "produces": ["application/json"], "tags": ["rule-percentages"], "summary": "Получение процентажа для балансировки", "parameters": [{"type": "integer", "description": "Rule ID", "name": "rule_id", "in": "path", "required": true}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/middlewares.Response-array_model_RulePercentage"}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/middlewares.Response-middlewares_Empty"}}}}, "put": {"security": [{"bearerAuth": []}], "description": "Старые записи удаляются при добавлении новой", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["rule-percentages"], "summary": "Добавляются новые банки и обновляются процентажа балансироваки по добавленным и существующем банкам", "parameters": [{"description": "CreateBalancerRequest Data", "name": "data", "in": "body", "required": true, "schema": {"type": "array", "items": {"type": "object", "required": ["acquirer_id"], "properties": {"acquirer_id": {"type": "integer"}, "percentage": {"type": "integer"}}}}}, {"type": "integer", "description": "Rule ID", "name": "rule_id", "in": "path", "required": true}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/middlewares.Response-middlewares_Empty"}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/middlewares.Response-middlewares_Empty"}}}}}, "/api/v1/rules": {"get": {"security": [{"bearerAuth": []}], "description": "Возвращает список с пагинацией и фильтрацией", "produces": ["application/json"], "tags": ["balancer"], "summary": "Получение списка правил с эквайерами", "parameters": [{"type": "boolean", "name": "is_base", "in": "query"}, {"type": "integer", "name": "project_id", "in": "query"}, {"type": "integer", "name": "transaction_type_id", "in": "query"}, {"type": "string", "default": "ASC", "name": "weight_sort_by", "in": "query"}, {"type": "integer", "description": "per_page", "name": "per_page", "in": "query"}, {"type": "integer", "description": "page", "name": "page", "in": "query"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/middlewares.Response-array_model_Rule"}, "headers": {"x-page": {"type": "int", "description": "1"}, "x-per-page": {"type": "int", "description": "1"}, "x-total": {"type": "int", "description": "11"}}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/middlewares.Response-middlewares_Empty"}}}}}, "/api/v1/terminal": {"post": {"security": [{"bearerAuth": []}], "consumes": ["application/json"], "produces": ["application/json"], "tags": ["terminals"], "summary": "Создание терминала", "parameters": [{"description": "BankTerminalID Data", "name": "data", "in": "body", "required": true, "schema": {"$ref": "#/definitions/schema.CreateTerminalRequest"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/middlewares.Response-schema_TerminalResponse"}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/middlewares.Response-middlewares_Empty"}}}}}, "/api/v1/terminal-projects": {"post": {"security": [{"bearerAuth": []}], "consumes": ["application/json"], "produces": ["application/json"], "tags": ["terminal-project"], "summary": "Создание проект терминала", "parameters": [{"description": "TerminalProject Data", "name": "data", "in": "body", "required": true, "schema": {"$ref": "#/definitions/schema.TerminalProjectRequest"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/middlewares.Response-schema_TerminalResponse"}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/middlewares.Response-middlewares_Empty"}}}}}, "/api/v1/terminal-projects/projects/{project_id}/pay-in": {"get": {"security": [{"bearerAuth": []}], "consumes": ["application/json"], "produces": ["application/json"], "tags": ["terminal-project"], "summary": "Получение терминалов по типу \"прием\"", "parameters": [{"type": "integer", "description": "Terminal Project ID", "name": "project_id", "in": "path", "required": true}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/middlewares.Response-array_schema_GetTerminalProjectPayIn"}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/middlewares.Response-middlewares_Empty"}}}}}, "/api/v1/terminal-projects/{id}": {"put": {"security": [{"bearerAuth": []}], "consumes": ["application/json"], "produces": ["application/json"], "tags": ["terminal-project"], "summary": "Изменение проекта терминала", "parameters": [{"type": "integer", "description": "Terminal Project ID", "name": "id", "in": "path", "required": true}, {"description": "TerminalProject Data", "name": "data", "in": "body", "required": true, "schema": {"$ref": "#/definitions/schema.TerminalProjectRequest"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/middlewares.Response-middlewares_Empty"}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/middlewares.Response-middlewares_Empty"}}}}}, "/api/v1/terminal-projects/{id}/status": {"put": {"security": [{"bearerAuth": []}], "consumes": ["application/json"], "produces": ["application/json"], "tags": ["terminal-project"], "summary": "Изменение статус проекта терминала", "parameters": [{"type": "integer", "description": "Terminal Project ID", "name": "id", "in": "path", "required": true}, {"description": "TerminalProject Data", "name": "data", "in": "body", "required": true, "schema": {"$ref": "#/definitions/schema.UpdateTerminalProjectStatusRequest"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/middlewares.Response-middlewares_Empty"}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/middlewares.Response-middlewares_Empty"}}}}}, "/api/v1/terminals": {"get": {"security": [{"bearerAuth": []}], "description": "С фильтрами", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["terminals"], "summary": "Получение списка всех терминалов", "operationId": "getTerminalsByFilters", "parameters": [{"type": "integer", "description": "project ID", "name": "project_id", "in": "query"}, {"type": "integer", "description": "per_page", "name": "per_page", "in": "query"}, {"type": "integer", "description": "page", "name": "page", "in": "query"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/middlewares.Response-array_schema_TerminalResponse"}, "headers": {"x-page": {"type": "int", "description": "1"}, "x-per-page": {"type": "int", "description": "1"}, "x-total": {"type": "int", "description": "11"}}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/middlewares.Response-middlewares_Empty"}}}}}, "/api/v1/terminals/{id}": {"get": {"security": [{"bearerAuth": []}], "consumes": ["application/json"], "produces": ["application/json"], "tags": ["terminals"], "summary": "Получение терминала", "operationId": "getTerminalByID", "parameters": [{"type": "integer", "description": "Terminal ID", "name": "id", "in": "path", "required": true}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/middlewares.Response-model_Terminal"}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/middlewares.Response-middlewares_Empty"}}}}}, "/api/v1/terminals/{id}/config": {"get": {"security": [{"bearerAuth": []}], "consumes": ["application/json"], "produces": ["application/json"], "tags": ["terminals"], "summary": "Получение конфигурации терминала в расшифрованном виде", "parameters": [{"type": "integer", "description": "Terminal ID", "name": "id", "in": "path", "required": true}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/middlewares.Response-schema_DecryptedConfig"}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/middlewares.Response-middlewares_Empty"}}}}, "patch": {"security": [{"bearerAuth": []}], "description": "Конфигурация должна содержать в себе поле is_two_stage (bool)", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["terminals"], "summary": "Сохранение конфигурации терминала в зашифрованном виде", "parameters": [{"description": "Key and Value", "name": "data", "in": "body", "required": true, "schema": {"type": "object", "additionalProperties": true}}, {"type": "integer", "description": "Terminal ID", "name": "id", "in": "path", "required": true}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/middlewares.Response-string"}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/middlewares.Response-middlewares_Empty"}}}}}, "/api/v1/terminals/{id}/status": {"put": {"consumes": ["application/json"], "produces": ["application/json"], "tags": ["terminals"], "summary": "Обновление статус терминала (включаем/выключаем)", "parameters": [{"type": "integer", "description": "TerminalID ID", "name": "id", "in": "path", "required": true}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/middlewares.Response-string"}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/middlewares.Response-middlewares_Empty"}}}}}, "/api/v1/terminals/{id}/timeout": {"patch": {"security": [{"bearerAuth": []}], "description": "Добавление таймаута для двухстадийного платежа для проведения автоматического", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["terminals"], "summary": "Добавление таймаута для двухстадийного платежа", "parameters": [{"description": "TerminalTwoStage request", "name": "data", "in": "body", "required": true, "schema": {"$ref": "#/definitions/schema.TerminalTwoStage"}}, {"type": "integer", "description": "Terminal ID", "name": "id", "in": "path", "required": true}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/middlewares.Response-string"}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/middlewares.Response-middlewares_Empty"}}}}}}, "definitions": {"middlewares.Empty": {"type": "object"}, "middlewares.Response-_Empty": {"type": "object", "required": ["message", "result", "status"], "properties": {"message": {"type": "string"}, "result": {"$ref": "#/definitions/middlewares.Empty"}, "status": {"type": "boolean"}}}, "middlewares.Response-array_model_Acquirer": {"type": "object", "required": ["message", "result", "status"], "properties": {"message": {"type": "string"}, "result": {"type": "array", "items": {"$ref": "#/definitions/model.Acquirer"}}, "status": {"type": "boolean"}}}, "middlewares.Response-array_model_Bank": {"type": "object", "required": ["message", "result", "status"], "properties": {"message": {"type": "string"}, "result": {"type": "array", "items": {"$ref": "#/definitions/model.Bank"}}, "status": {"type": "boolean"}}}, "middlewares.Response-array_model_BankBin": {"type": "object", "required": ["message", "result", "status"], "properties": {"message": {"type": "string"}, "result": {"type": "array", "items": {"$ref": "#/definitions/model.BankBin"}}, "status": {"type": "boolean"}}}, "middlewares.Response-array_model_BankBins": {"type": "object", "required": ["message", "result", "status"], "properties": {"message": {"type": "string"}, "result": {"type": "array", "items": {"$ref": "#/definitions/model.BankBins"}}, "status": {"type": "boolean"}}}, "middlewares.Response-array_model_Country": {"type": "object", "required": ["message", "result", "status"], "properties": {"message": {"type": "string"}, "result": {"type": "array", "items": {"$ref": "#/definitions/model.Country"}}, "status": {"type": "boolean"}}}, "middlewares.Response-array_model_CountryBasic": {"type": "object", "required": ["message", "result", "status"], "properties": {"message": {"type": "string"}, "result": {"type": "array", "items": {"$ref": "#/definitions/model.CountryBasic"}}, "status": {"type": "boolean"}}}, "middlewares.Response-array_model_Ips": {"type": "object", "required": ["message", "result", "status"], "properties": {"message": {"type": "string"}, "result": {"type": "array", "items": {"$ref": "#/definitions/model.Ips"}}, "status": {"type": "boolean"}}}, "middlewares.Response-array_model_Rule": {"type": "object", "required": ["message", "result", "status"], "properties": {"message": {"type": "string"}, "result": {"type": "array", "items": {"$ref": "#/definitions/model.Rule"}}, "status": {"type": "boolean"}}}, "middlewares.Response-array_model_RulePercentage": {"type": "object", "required": ["message", "result", "status"], "properties": {"message": {"type": "string"}, "result": {"type": "array", "items": {"$ref": "#/definitions/model.RulePercentage"}}, "status": {"type": "boolean"}}}, "middlewares.Response-array_schema_AcquirerTerminal": {"type": "object", "required": ["message", "result", "status"], "properties": {"message": {"type": "string"}, "result": {"type": "array", "items": {"$ref": "#/definitions/schema.AcquirerTerminal"}}, "status": {"type": "boolean"}}}, "middlewares.Response-array_schema_GetBalanceResponse": {"type": "object", "required": ["message", "result", "status"], "properties": {"message": {"type": "string"}, "result": {"type": "array", "items": {"$ref": "#/definitions/schema.GetBalanceResponse"}}, "status": {"type": "boolean"}}}, "middlewares.Response-array_schema_GetTerminalProjectPayIn": {"type": "object", "required": ["message", "result", "status"], "properties": {"message": {"type": "string"}, "result": {"type": "array", "items": {"$ref": "#/definitions/schema.GetTerminalProjectPayIn"}}, "status": {"type": "boolean"}}}, "middlewares.Response-array_schema_ResponsePaymentType": {"type": "object", "required": ["message", "result", "status"], "properties": {"message": {"type": "string"}, "result": {"type": "array", "items": {"$ref": "#/definitions/schema.ResponsePaymentType"}}, "status": {"type": "boolean"}}}, "middlewares.Response-array_schema_TerminalResponse": {"type": "object", "required": ["message", "result", "status"], "properties": {"message": {"type": "string"}, "result": {"type": "array", "items": {"$ref": "#/definitions/schema.TerminalResponse"}}, "status": {"type": "boolean"}}}, "middlewares.Response-middlewares_Empty": {"type": "object", "required": ["message", "result", "status"], "properties": {"message": {"type": "string"}, "result": {"$ref": "#/definitions/middlewares.Empty"}, "status": {"type": "boolean"}}}, "middlewares.Response-model_Rule": {"type": "object", "required": ["message", "result", "status"], "properties": {"message": {"type": "string"}, "result": {"$ref": "#/definitions/model.Rule"}, "status": {"type": "boolean"}}}, "middlewares.Response-model_Terminal": {"type": "object", "required": ["message", "result", "status"], "properties": {"message": {"type": "string"}, "result": {"$ref": "#/definitions/model.Terminal"}, "status": {"type": "boolean"}}}, "middlewares.Response-schema_DecryptedConfig": {"type": "object", "required": ["message", "result", "status"], "properties": {"message": {"type": "string"}, "result": {"$ref": "#/definitions/schema.DecryptedConfig"}, "status": {"type": "boolean"}}}, "middlewares.Response-schema_TerminalResponse": {"type": "object", "required": ["message", "result", "status"], "properties": {"message": {"type": "string"}, "result": {"$ref": "#/definitions/schema.TerminalResponse"}, "status": {"type": "boolean"}}}, "middlewares.Response-schema_UpdateTerminalsResponse": {"type": "object", "required": ["message", "result", "status"], "properties": {"message": {"type": "string"}, "result": {"$ref": "#/definitions/schema.UpdateTerminalsResponse"}, "status": {"type": "boolean"}}}, "middlewares.Response-string": {"type": "object", "required": ["message", "result", "status"], "properties": {"message": {"type": "string"}, "result": {"type": "string"}, "status": {"type": "boolean"}}}, "model.Acquirer": {"type": "object", "properties": {"bank_id": {"type": "integer"}, "code": {"type": "string"}, "country_id": {"type": "integer"}, "country_name": {"type": "string"}, "created_at": {"type": "string"}, "description": {"type": "string"}, "id": {"type": "integer"}, "is_active": {"type": "boolean"}, "name": {"type": "string"}, "updated_at": {"type": "string"}}}, "model.Bank": {"type": "object", "properties": {"bik": {"type": "string"}, "created_at": {"type": "string"}, "id": {"type": "integer"}, "name": {"type": "string"}, "swift": {"type": "string"}, "updated_at": {"type": "string"}}}, "model.BankBin": {"type": "object", "properties": {"bank_id": {"type": "integer"}, "bin": {"type": "string"}, "country_id": {"type": "integer"}, "created_at": {"type": "string"}, "id": {"type": "integer"}, "ips_id": {"type": "integer"}, "updated_at": {"type": "string"}}}, "model.BankBins": {"type": "object", "properties": {"bank_id": {"type": "integer"}, "banks": {"$ref": "#/definitions/model.Bank"}, "bin": {"type": "string"}, "created_at": {"type": "string"}, "id": {"type": "integer"}, "updated_at": {"type": "string"}}}, "model.Country": {"type": "object", "properties": {"banks": {"type": "array", "items": {"$ref": "#/definitions/model.Bank"}}, "created_at": {"type": "string"}, "id": {"type": "integer"}, "name": {"type": "string"}, "updated_at": {"type": "string"}}}, "model.CountryBasic": {"type": "object", "properties": {"created_at": {"type": "string"}, "id": {"type": "integer"}, "name": {"type": "string"}, "updated_at": {"type": "string"}}}, "model.Ips": {"type": "object", "properties": {"created_at": {"type": "string"}, "id": {"type": "integer"}, "name": {"type": "string"}, "updated_at": {"type": "string"}}}, "model.Rule": {"type": "object", "properties": {"amount_from": {"type": "number"}, "amount_to": {"type": "number"}, "country_id": {"type": "integer"}, "created_at": {"type": "string"}, "id": {"type": "integer"}, "ips_id": {"type": "integer"}, "is_active": {"type": "boolean"}, "is_base": {"type": "boolean"}, "issuer": {"$ref": "#/definitions/model.Bank"}, "issuer_id": {"type": "integer"}, "project_id": {"type": "integer"}, "rule_percentages": {"type": "array", "items": {"$ref": "#/definitions/model.RulePercentage"}}, "transaction_type_id": {"type": "integer"}, "updated_at": {"type": "string"}, "weight": {"type": "integer"}}}, "model.RulePercentage": {"type": "object", "properties": {"acquirer": {"$ref": "#/definitions/model.Acquirer"}, "acquirer_id": {"type": "integer"}, "percentage": {"type": "integer"}}}, "model.Terminal": {"type": "object", "properties": {"account_number": {"type": "string"}, "acquirer": {"$ref": "#/definitions/model.Acquirer"}, "acquirer_id": {"type": "integer"}, "acquirer_terminal_name": {"type": "string"}, "created_at": {"type": "string"}, "description": {"type": "string"}, "id": {"type": "integer"}, "is_transit": {"type": "boolean"}, "status": {"$ref": "#/definitions/model.TerminalStatus"}, "terminal_projects": {"type": "array", "items": {"$ref": "#/definitions/model.TerminalProject"}}, "transit_bank_id": {"type": "integer"}, "two_stage_timeout": {"type": "integer"}, "updated_at": {"type": "string"}}}, "model.TerminalProject": {"type": "object", "properties": {"created_at": {"type": "string"}, "id": {"type": "integer"}, "is_active": {"type": "boolean"}, "project_id": {"type": "integer"}, "terminal_id": {"type": "integer"}, "transaction_type_id": {"type": "integer"}, "updated_at": {"type": "string"}}}, "model.TerminalStatus": {"type": "integer", "enum": [1, 2, 3], "x-enum-varnames": ["TerminalOn", "TerminalOff", "TerminalGlobalOff"]}, "schema.Acquirer": {"type": "object", "required": ["bank_id", "code", "country_id", "description", "name"], "properties": {"bank_id": {"type": "integer"}, "code": {"type": "string"}, "country_id": {"type": "integer"}, "description": {"description": "Contract    *string `json:\"contract\" validate:\"required\"`", "type": "string"}, "name": {"type": "string"}}}, "schema.AcquirerTerminal": {"type": "object", "required": ["code", "id", "is_active", "name"], "properties": {"bank_id": {"type": "integer"}, "code": {"type": "string"}, "country_id": {"type": "integer"}, "country_name": {"type": "string"}, "description": {"type": "string"}, "id": {"type": "integer"}, "is_active": {"type": "boolean"}, "name": {"type": "string"}, "terminals": {"type": "integer"}}}, "schema.Bank": {"type": "object", "required": ["name"], "properties": {"bik": {"type": "string"}, "name": {"type": "string"}, "swift": {"type": "string"}}}, "schema.BankBin": {"type": "object", "required": ["bank_id", "bin"], "properties": {"bank_id": {"type": "integer"}, "bin": {"type": "string"}, "country_id": {"type": "integer"}, "ips_id": {"type": "integer"}}}, "schema.Country": {"type": "object", "required": ["name"], "properties": {"name": {"type": "string"}}}, "schema.CountryBank": {"type": "object", "required": ["bank_id", "country_id"], "properties": {"bank_id": {"type": "integer"}, "country_id": {"type": "integer"}}}, "schema.CreateBaseRuleRequest": {"type": "object", "required": ["acquirers", "project_id", "transaction_type_id"], "properties": {"acquirers": {"type": "array", "items": {"type": "object", "required": ["acquirer_id"], "properties": {"acquirer_id": {"type": "integer"}, "percentage": {"type": "integer"}}}}, "project_id": {"type": "integer"}, "transaction_type_id": {"type": "integer"}}}, "schema.CreateRuleRequest": {"type": "object", "required": ["acquirers", "project_id", "transaction_type_id"], "properties": {"acquirers": {"type": "array", "items": {"type": "object", "required": ["acquirer_id"], "properties": {"acquirer_id": {"type": "integer"}, "percentage": {"type": "integer"}}}}, "amount_from": {"type": "number"}, "amount_to": {"type": "number"}, "country_id": {"type": "integer"}, "ips_id": {"type": "integer"}, "issuer_id": {"type": "integer"}, "project_id": {"type": "integer"}, "transaction_type_id": {"type": "integer"}}}, "schema.CreateTerminalRequest": {"type": "object", "required": ["acquirer_id", "config"], "properties": {"account_number": {"type": "string"}, "acquirer_id": {"type": "integer"}, "acquirer_terminal_name": {"type": "string"}, "config": {"type": "object", "additionalProperties": true}, "description": {"type": "string"}, "is_transit": {"type": "boolean"}, "transit_bank_id": {"type": "integer"}, "two_stage_timeout": {"type": "integer"}}}, "schema.DecryptedConfig": {"type": "object", "additionalProperties": {}}, "schema.GetBalanceResponse": {"type": "object", "properties": {"acquirer_name": {"type": "string"}, "amount": {"type": "number"}, "message": {"type": "string"}, "terminal_id": {"type": "integer"}}}, "schema.GetTerminalProjectPayIn": {"type": "object", "required": ["code", "created_at", "id", "is_active", "name", "project_id", "terminal_id", "transaction_type_id", "updated_at"], "properties": {"code": {"type": "string"}, "created_at": {"type": "string"}, "id": {"type": "integer"}, "is_active": {"type": "boolean"}, "name": {"type": "string"}, "project_id": {"type": "integer"}, "terminal_id": {"type": "integer"}, "transaction_type_id": {"type": "integer"}, "updated_at": {"type": "string"}}}, "schema.Ips": {"type": "object", "required": ["name"], "properties": {"name": {"type": "string"}}}, "schema.ResponsePaymentType": {"type": "object", "properties": {"payment_type": {"type": "integer"}, "project_id": {"type": "integer"}, "transaction_type": {"$ref": "#/definitions/schema.TransactionType"}}}, "schema.TerminalProjectRequest": {"type": "object", "required": ["project_id", "terminal_id", "transaction_type_id"], "properties": {"is_active": {"type": "boolean"}, "project_id": {"type": "integer"}, "terminal_id": {"type": "integer"}, "transaction_type_id": {"type": "integer"}}}, "schema.TerminalResponse": {"type": "object", "properties": {"acquirer": {"$ref": "#/definitions/model.Acquirer"}, "acquirer_id": {"type": "integer"}, "config": {"type": "string"}, "id": {"type": "integer"}, "project_id": {"type": "integer"}, "status": {"$ref": "#/definitions/model.TerminalStatus"}, "transaction_type_id": {"type": "integer"}}}, "schema.TerminalTwoStage": {"type": "object", "required": ["two_stage_timeout"], "properties": {"two_stage_timeout": {"type": "integer", "maximum": 13, "minimum": 3}}}, "schema.TransactionType": {"type": "object", "properties": {"code": {"type": "string"}, "id": {"type": "integer"}, "name": {"type": "string"}}}, "schema.UpdateTerminalProjectStatusRequest": {"type": "object", "required": ["is_active"], "properties": {"is_active": {"type": "boolean"}}}, "schema.UpdateTerminalsResponse": {"type": "object", "properties": {"rows_affected": {"type": "integer"}, "total": {"type": "integer"}}}}, "securityDefinitions": {"bearerAuth": {"type": "<PERSON><PERSON><PERSON><PERSON>", "name": "Authorization", "in": "header"}}}