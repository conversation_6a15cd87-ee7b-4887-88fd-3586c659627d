// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.5
// 	protoc        v5.29.3
// source: inner/processing/grpc/integration.proto

package grpc

import (
	_ "git.local/sensitive/mvp/pb"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	descriptorpb "google.golang.org/protobuf/types/descriptorpb"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type IntegrationError int32

const (
	IntegrationError_None                             IntegrationError = 0
	IntegrationError_IncorrectCardExpDate             IntegrationError = 117
	IntegrationError_InvalidCard                      IntegrationError = 102
	IntegrationError_ExceedsAmountLimit               IntegrationError = 103
	IntegrationError_TransactionDeclinedByIssuer      IntegrationError = 105
	IntegrationError_UnavailableIssuer                IntegrationError = 107
	IntegrationError_IncorrectCardNumber              IntegrationError = 116
	IntegrationError_PaymentForbiddenForMerchant      IntegrationError = 109
	IntegrationError_TransactionDeclinedByAcquirer    IntegrationError = 106
	IntegrationError_UnavailableAcquirer              IntegrationError = 108
	IntegrationError_IncorrectCVVCVC                  IntegrationError = 115
	IntegrationError_ThreeDSAuthFailed                IntegrationError = 101
	IntegrationError_ExceedsTransactionFrequencyLimit IntegrationError = 104
	IntegrationError_LostCard                         IntegrationError = 113
	IntegrationError_SuspiciousClient                 IntegrationError = 119
	IntegrationError_NonExistentCard                  IntegrationError = 112
	IntegrationError_CardHasExpired                   IntegrationError = 114
	IntegrationError_InsufficientFunds                IntegrationError = 118
	IntegrationError_UndefinedError                   IntegrationError = 100
	IntegrationError_StolenCard                       IntegrationError = 110
	IntegrationError_BlockedCard                      IntegrationError = 111
	IntegrationError_UserDidNotPay                    IntegrationError = 120
	IntegrationError_InvalidThreeDSecureParameters    IntegrationError = 121
)

// Enum value maps for IntegrationError.
var (
	IntegrationError_name = map[int32]string{
		0:   "None",
		117: "IncorrectCardExpDate",
		102: "InvalidCard",
		103: "ExceedsAmountLimit",
		105: "TransactionDeclinedByIssuer",
		107: "UnavailableIssuer",
		116: "IncorrectCardNumber",
		109: "PaymentForbiddenForMerchant",
		106: "TransactionDeclinedByAcquirer",
		108: "UnavailableAcquirer",
		115: "IncorrectCVVCVC",
		101: "ThreeDSAuthFailed",
		104: "ExceedsTransactionFrequencyLimit",
		113: "LostCard",
		119: "SuspiciousClient",
		112: "NonExistentCard",
		114: "CardHasExpired",
		118: "InsufficientFunds",
		100: "UndefinedError",
		110: "StolenCard",
		111: "BlockedCard",
		120: "UserDidNotPay",
		121: "InvalidThreeDSecureParameters",
	}
	IntegrationError_value = map[string]int32{
		"None":                             0,
		"IncorrectCardExpDate":             117,
		"InvalidCard":                      102,
		"ExceedsAmountLimit":               103,
		"TransactionDeclinedByIssuer":      105,
		"UnavailableIssuer":                107,
		"IncorrectCardNumber":              116,
		"PaymentForbiddenForMerchant":      109,
		"TransactionDeclinedByAcquirer":    106,
		"UnavailableAcquirer":              108,
		"IncorrectCVVCVC":                  115,
		"ThreeDSAuthFailed":                101,
		"ExceedsTransactionFrequencyLimit": 104,
		"LostCard":                         113,
		"SuspiciousClient":                 119,
		"NonExistentCard":                  112,
		"CardHasExpired":                   114,
		"InsufficientFunds":                118,
		"UndefinedError":                   100,
		"StolenCard":                       110,
		"BlockedCard":                      111,
		"UserDidNotPay":                    120,
		"InvalidThreeDSecureParameters":    121,
	}
)

func (x IntegrationError) Enum() *IntegrationError {
	p := new(IntegrationError)
	*p = x
	return p
}

func (x IntegrationError) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (IntegrationError) Descriptor() protoreflect.EnumDescriptor {
	return file_inner_processing_grpc_integration_proto_enumTypes[0].Descriptor()
}

func (IntegrationError) Type() protoreflect.EnumType {
	return &file_inner_processing_grpc_integration_proto_enumTypes[0]
}

func (x IntegrationError) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use IntegrationError.Descriptor instead.
func (IntegrationError) EnumDescriptor() ([]byte, []int) {
	return file_inner_processing_grpc_integration_proto_rawDescGZIP(), []int{0}
}

type IntegrationErrorRef struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Message       *string                `protobuf:"bytes,1,opt,name=message" json:"message,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *IntegrationErrorRef) Reset() {
	*x = IntegrationErrorRef{}
	mi := &file_inner_processing_grpc_integration_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *IntegrationErrorRef) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*IntegrationErrorRef) ProtoMessage() {}

func (x *IntegrationErrorRef) ProtoReflect() protoreflect.Message {
	mi := &file_inner_processing_grpc_integration_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use IntegrationErrorRef.ProtoReflect.Descriptor instead.
func (*IntegrationErrorRef) Descriptor() ([]byte, []int) {
	return file_inner_processing_grpc_integration_proto_rawDescGZIP(), []int{0}
}

func (x *IntegrationErrorRef) GetMessage() string {
	if x != nil && x.Message != nil {
		return *x.Message
	}
	return ""
}

var file_inner_processing_grpc_integration_proto_extTypes = []protoimpl.ExtensionInfo{
	{
		ExtendedType:  (*descriptorpb.EnumValueOptions)(nil),
		ExtensionType: (*IntegrationErrorRef)(nil),
		Field:         100210,
		Name:          "processing.integration.integration.integration_error_value",
		Tag:           "bytes,100210,opt,name=integration_error_value",
		Filename:      "inner/processing/grpc/integration.proto",
	},
	{
		ExtendedType:  (*descriptorpb.EnumOptions)(nil),
		ExtensionType: (*IntegrationErrorRef)(nil),
		Field:         100211,
		Name:          "processing.integration.integration.default_integration_error_value",
		Tag:           "bytes,100211,opt,name=default_integration_error_value",
		Filename:      "inner/processing/grpc/integration.proto",
	},
}

// Extension fields to descriptorpb.EnumValueOptions.
var (
	// optional processing.integration.integration.IntegrationErrorRef integration_error_value = 100210;
	E_IntegrationErrorValue = &file_inner_processing_grpc_integration_proto_extTypes[0]
)

// Extension fields to descriptorpb.EnumOptions.
var (
	// optional processing.integration.integration.IntegrationErrorRef default_integration_error_value = 100211;
	E_DefaultIntegrationErrorValue = &file_inner_processing_grpc_integration_proto_extTypes[1]
)

var File_inner_processing_grpc_integration_proto protoreflect.FileDescriptor

var file_inner_processing_grpc_integration_proto_rawDesc = string([]byte{
	0x0a, 0x27, 0x69, 0x6e, 0x6e, 0x65, 0x72, 0x2f, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69,
	0x6e, 0x67, 0x2f, 0x67, 0x72, 0x70, 0x63, 0x2f, 0x69, 0x6e, 0x74, 0x65, 0x67, 0x72, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x22, 0x70, 0x72, 0x6f, 0x63, 0x65,
	0x73, 0x73, 0x69, 0x6e, 0x67, 0x2e, 0x69, 0x6e, 0x74, 0x65, 0x67, 0x72, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x2e, 0x69, 0x6e, 0x74, 0x65, 0x67, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x1a, 0x20, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x64,
	0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x6f, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a,
	0x14, 0x6d, 0x76, 0x70, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f, 0x72, 0x65, 0x66, 0x73, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x2f, 0x0a, 0x13, 0x49, 0x6e, 0x74, 0x65, 0x67, 0x72, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x52, 0x65, 0x66, 0x12, 0x18, 0x0a, 0x07,
	0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6d,
	0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x2a, 0x8a, 0x0a, 0x0a, 0x10, 0x49, 0x6e, 0x74, 0x65, 0x67,
	0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x12, 0x14, 0x0a, 0x04, 0x4e,
	0x6f, 0x6e, 0x65, 0x10, 0x00, 0x1a, 0x0a, 0x92, 0xf7, 0x30, 0x06, 0x0a, 0x04, 0x4e, 0x6f, 0x6e,
	0x65, 0x12, 0x3e, 0x0a, 0x14, 0x49, 0x6e, 0x63, 0x6f, 0x72, 0x72, 0x65, 0x63, 0x74, 0x43, 0x61,
	0x72, 0x64, 0x45, 0x78, 0x70, 0x44, 0x61, 0x74, 0x65, 0x10, 0x75, 0x1a, 0x24, 0x92, 0xf7, 0x30,
	0x20, 0x0a, 0x1e, 0x69, 0x6e, 0x63, 0x6f, 0x72, 0x72, 0x65, 0x63, 0x74, 0x20, 0x63, 0x61, 0x72,
	0x64, 0x20, 0x65, 0x78, 0x70, 0x69, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x20, 0x64, 0x61, 0x74,
	0x65, 0x12, 0x23, 0x0a, 0x0b, 0x49, 0x6e, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x43, 0x61, 0x72, 0x64,
	0x10, 0x66, 0x1a, 0x12, 0x92, 0xf7, 0x30, 0x0e, 0x0a, 0x0c, 0x69, 0x6e, 0x76, 0x61, 0x6c, 0x69,
	0x64, 0x20, 0x63, 0x61, 0x72, 0x64, 0x12, 0x32, 0x0a, 0x12, 0x45, 0x78, 0x63, 0x65, 0x65, 0x64,
	0x73, 0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x4c, 0x69, 0x6d, 0x69, 0x74, 0x10, 0x67, 0x1a, 0x1a,
	0x92, 0xf7, 0x30, 0x16, 0x0a, 0x14, 0x65, 0x78, 0x63, 0x65, 0x65, 0x64, 0x73, 0x20, 0x61, 0x6d,
	0x6f, 0x75, 0x6e, 0x74, 0x20, 0x6c, 0x69, 0x6d, 0x69, 0x74, 0x12, 0x48, 0x0a, 0x1b, 0x54, 0x72,
	0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x44, 0x65, 0x63, 0x6c, 0x69, 0x6e, 0x65,
	0x64, 0x42, 0x79, 0x49, 0x73, 0x73, 0x75, 0x65, 0x72, 0x10, 0x69, 0x1a, 0x27, 0x92, 0xf7, 0x30,
	0x23, 0x0a, 0x21, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x20, 0x64,
	0x65, 0x63, 0x6c, 0x69, 0x6e, 0x65, 0x64, 0x20, 0x62, 0x79, 0x20, 0x61, 0x6e, 0x20, 0x69, 0x73,
	0x73, 0x75, 0x65, 0x72, 0x12, 0x2f, 0x0a, 0x11, 0x55, 0x6e, 0x61, 0x76, 0x61, 0x69, 0x6c, 0x61,
	0x62, 0x6c, 0x65, 0x49, 0x73, 0x73, 0x75, 0x65, 0x72, 0x10, 0x6b, 0x1a, 0x18, 0x92, 0xf7, 0x30,
	0x14, 0x0a, 0x12, 0x75, 0x6e, 0x61, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x6c, 0x65, 0x20, 0x69,
	0x73, 0x73, 0x75, 0x65, 0x72, 0x12, 0x34, 0x0a, 0x13, 0x49, 0x6e, 0x63, 0x6f, 0x72, 0x72, 0x65,
	0x63, 0x74, 0x43, 0x61, 0x72, 0x64, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x10, 0x74, 0x1a, 0x1b,
	0x92, 0xf7, 0x30, 0x17, 0x0a, 0x15, 0x69, 0x6e, 0x63, 0x6f, 0x72, 0x72, 0x65, 0x63, 0x74, 0x20,
	0x63, 0x61, 0x72, 0x64, 0x20, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x12, 0x4c, 0x0a, 0x1b, 0x50,
	0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x46, 0x6f, 0x72, 0x62, 0x69, 0x64, 0x64, 0x65, 0x6e, 0x46,
	0x6f, 0x72, 0x4d, 0x65, 0x72, 0x63, 0x68, 0x61, 0x6e, 0x74, 0x10, 0x6d, 0x1a, 0x2b, 0x92, 0xf7,
	0x30, 0x27, 0x0a, 0x25, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x20, 0x69, 0x73, 0x20, 0x66,
	0x6f, 0x72, 0x62, 0x69, 0x64, 0x64, 0x65, 0x6e, 0x20, 0x66, 0x6f, 0x72, 0x20, 0x74, 0x68, 0x65,
	0x20, 0x6d, 0x65, 0x72, 0x63, 0x68, 0x61, 0x6e, 0x74, 0x12, 0x4c, 0x0a, 0x1d, 0x54, 0x72, 0x61,
	0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x44, 0x65, 0x63, 0x6c, 0x69, 0x6e, 0x65, 0x64,
	0x42, 0x79, 0x41, 0x63, 0x71, 0x75, 0x69, 0x72, 0x65, 0x72, 0x10, 0x6a, 0x1a, 0x29, 0x92, 0xf7,
	0x30, 0x25, 0x0a, 0x23, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x20,
	0x64, 0x65, 0x63, 0x6c, 0x69, 0x6e, 0x65, 0x64, 0x20, 0x62, 0x79, 0x20, 0x61, 0x6e, 0x20, 0x61,
	0x63, 0x71, 0x75, 0x69, 0x72, 0x65, 0x72, 0x12, 0x33, 0x0a, 0x13, 0x55, 0x6e, 0x61, 0x76, 0x61,
	0x69, 0x6c, 0x61, 0x62, 0x6c, 0x65, 0x41, 0x63, 0x71, 0x75, 0x69, 0x72, 0x65, 0x72, 0x10, 0x6c,
	0x1a, 0x1a, 0x92, 0xf7, 0x30, 0x16, 0x0a, 0x14, 0x75, 0x6e, 0x61, 0x76, 0x61, 0x69, 0x6c, 0x61,
	0x62, 0x6c, 0x65, 0x20, 0x61, 0x63, 0x71, 0x75, 0x69, 0x72, 0x65, 0x72, 0x12, 0x2c, 0x0a, 0x0f,
	0x49, 0x6e, 0x63, 0x6f, 0x72, 0x72, 0x65, 0x63, 0x74, 0x43, 0x56, 0x56, 0x43, 0x56, 0x43, 0x10,
	0x73, 0x1a, 0x17, 0x92, 0xf7, 0x30, 0x13, 0x0a, 0x11, 0x69, 0x6e, 0x63, 0x6f, 0x72, 0x72, 0x65,
	0x63, 0x74, 0x20, 0x43, 0x56, 0x56, 0x2f, 0x43, 0x56, 0x43, 0x12, 0x36, 0x0a, 0x11, 0x54, 0x68,
	0x72, 0x65, 0x65, 0x44, 0x53, 0x41, 0x75, 0x74, 0x68, 0x46, 0x61, 0x69, 0x6c, 0x65, 0x64, 0x10,
	0x65, 0x1a, 0x1f, 0x92, 0xf7, 0x30, 0x1b, 0x0a, 0x19, 0x33, 0x44, 0x53, 0x20, 0x61, 0x75, 0x74,
	0x68, 0x65, 0x6e, 0x74, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x20, 0x66, 0x61, 0x69, 0x6c,
	0x65, 0x64, 0x12, 0x4f, 0x0a, 0x20, 0x45, 0x78, 0x63, 0x65, 0x65, 0x64, 0x73, 0x54, 0x72, 0x61,
	0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x46, 0x72, 0x65, 0x71, 0x75, 0x65, 0x6e, 0x63,
	0x79, 0x4c, 0x69, 0x6d, 0x69, 0x74, 0x10, 0x68, 0x1a, 0x29, 0x92, 0xf7, 0x30, 0x25, 0x0a, 0x23,
	0x65, 0x78, 0x63, 0x65, 0x65, 0x64, 0x73, 0x20, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74,
	0x69, 0x6f, 0x6e, 0x20, 0x66, 0x72, 0x65, 0x71, 0x75, 0x65, 0x6e, 0x63, 0x79, 0x20, 0x6c, 0x69,
	0x6d, 0x69, 0x74, 0x12, 0x1d, 0x0a, 0x08, 0x4c, 0x6f, 0x73, 0x74, 0x43, 0x61, 0x72, 0x64, 0x10,
	0x71, 0x1a, 0x0f, 0x92, 0xf7, 0x30, 0x0b, 0x0a, 0x09, 0x6c, 0x6f, 0x73, 0x74, 0x20, 0x63, 0x61,
	0x72, 0x64, 0x12, 0x2d, 0x0a, 0x10, 0x53, 0x75, 0x73, 0x70, 0x69, 0x63, 0x69, 0x6f, 0x75, 0x73,
	0x43, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x10, 0x77, 0x1a, 0x17, 0x92, 0xf7, 0x30, 0x13, 0x0a, 0x11,
	0x73, 0x75, 0x73, 0x70, 0x69, 0x63, 0x69, 0x6f, 0x75, 0x73, 0x20, 0x63, 0x6c, 0x69, 0x65, 0x6e,
	0x74, 0x12, 0x2c, 0x0a, 0x0f, 0x4e, 0x6f, 0x6e, 0x45, 0x78, 0x69, 0x73, 0x74, 0x65, 0x6e, 0x74,
	0x43, 0x61, 0x72, 0x64, 0x10, 0x70, 0x1a, 0x17, 0x92, 0xf7, 0x30, 0x13, 0x0a, 0x11, 0x6e, 0x6f,
	0x6e, 0x20, 0x65, 0x78, 0x69, 0x73, 0x74, 0x65, 0x6e, 0x74, 0x20, 0x63, 0x61, 0x72, 0x64, 0x12,
	0x2a, 0x0a, 0x0e, 0x43, 0x61, 0x72, 0x64, 0x48, 0x61, 0x73, 0x45, 0x78, 0x70, 0x69, 0x72, 0x65,
	0x64, 0x10, 0x72, 0x1a, 0x16, 0x92, 0xf7, 0x30, 0x12, 0x0a, 0x10, 0x63, 0x61, 0x72, 0x64, 0x20,
	0x68, 0x61, 0x73, 0x20, 0x65, 0x78, 0x70, 0x69, 0x72, 0x65, 0x64, 0x12, 0x2f, 0x0a, 0x11, 0x49,
	0x6e, 0x73, 0x75, 0x66, 0x66, 0x69, 0x63, 0x69, 0x65, 0x6e, 0x74, 0x46, 0x75, 0x6e, 0x64, 0x73,
	0x10, 0x76, 0x1a, 0x18, 0x92, 0xf7, 0x30, 0x14, 0x0a, 0x12, 0x69, 0x6e, 0x73, 0x75, 0x66, 0x66,
	0x69, 0x63, 0x69, 0x65, 0x6e, 0x74, 0x20, 0x66, 0x75, 0x6e, 0x64, 0x73, 0x12, 0x29, 0x0a, 0x0e,
	0x55, 0x6e, 0x64, 0x65, 0x66, 0x69, 0x6e, 0x65, 0x64, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x10, 0x64,
	0x1a, 0x15, 0x92, 0xf7, 0x30, 0x11, 0x0a, 0x0f, 0x75, 0x6e, 0x64, 0x65, 0x66, 0x69, 0x6e, 0x65,
	0x64, 0x20, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x12, 0x21, 0x0a, 0x0a, 0x53, 0x74, 0x6f, 0x6c, 0x65,
	0x6e, 0x43, 0x61, 0x72, 0x64, 0x10, 0x6e, 0x1a, 0x11, 0x92, 0xf7, 0x30, 0x0d, 0x0a, 0x0b, 0x73,
	0x74, 0x6f, 0x6c, 0x65, 0x6e, 0x20, 0x63, 0x61, 0x72, 0x64, 0x12, 0x23, 0x0a, 0x0b, 0x42, 0x6c,
	0x6f, 0x63, 0x6b, 0x65, 0x64, 0x43, 0x61, 0x72, 0x64, 0x10, 0x6f, 0x1a, 0x12, 0x92, 0xf7, 0x30,
	0x0e, 0x0a, 0x0c, 0x62, 0x6c, 0x6f, 0x63, 0x6b, 0x65, 0x64, 0x20, 0x63, 0x61, 0x72, 0x64, 0x12,
	0x29, 0x0a, 0x0d, 0x55, 0x73, 0x65, 0x72, 0x44, 0x69, 0x64, 0x4e, 0x6f, 0x74, 0x50, 0x61, 0x79,
	0x10, 0x78, 0x1a, 0x16, 0x92, 0xf7, 0x30, 0x12, 0x0a, 0x10, 0x75, 0x73, 0x65, 0x72, 0x20, 0x64,
	0x69, 0x64, 0x20, 0x6e, 0x6f, 0x74, 0x20, 0x70, 0x61, 0x79, 0x12, 0x49, 0x0a, 0x1d, 0x49, 0x6e,
	0x76, 0x61, 0x6c, 0x69, 0x64, 0x54, 0x68, 0x72, 0x65, 0x65, 0x44, 0x53, 0x65, 0x63, 0x75, 0x72,
	0x65, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x65, 0x74, 0x65, 0x72, 0x73, 0x10, 0x79, 0x1a, 0x26, 0x92,
	0xf7, 0x30, 0x22, 0x0a, 0x20, 0x69, 0x6e, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x20, 0x74, 0x68, 0x72,
	0x65, 0x65, 0x44, 0x20, 0x73, 0x65, 0x63, 0x75, 0x72, 0x65, 0x20, 0x70, 0x61, 0x72, 0x61, 0x6d,
	0x65, 0x74, 0x65, 0x72, 0x73, 0x1a, 0x4c, 0x9a, 0xf7, 0x30, 0x06, 0x0a, 0x04, 0x4e, 0x6f, 0x6e,
	0x65, 0xaa, 0x82, 0xec, 0x8e, 0x02, 0x1f, 0x64, 0x65, 0x66, 0x61, 0x75, 0x6c, 0x74, 0x5f, 0x69,
	0x6e, 0x74, 0x65, 0x67, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x65, 0x72, 0x72, 0x6f, 0x72,
	0x5f, 0x76, 0x61, 0x6c, 0x75, 0x65, 0xb2, 0x82, 0xec, 0x8e, 0x02, 0x17, 0x69, 0x6e, 0x74, 0x65,
	0x67, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x5f, 0x76, 0x61,
	0x6c, 0x75, 0x65, 0x3a, 0x94, 0x01, 0x0a, 0x17, 0x69, 0x6e, 0x74, 0x65, 0x67, 0x72, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x5f, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x5f, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x12,
	0x21, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75,
	0x66, 0x2e, 0x45, 0x6e, 0x75, 0x6d, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x4f, 0x70, 0x74, 0x69, 0x6f,
	0x6e, 0x73, 0x18, 0xf2, 0x8e, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x37, 0x2e, 0x70, 0x72, 0x6f,
	0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x2e, 0x69, 0x6e, 0x74, 0x65, 0x67, 0x72, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x2e, 0x69, 0x6e, 0x74, 0x65, 0x67, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e,
	0x49, 0x6e, 0x74, 0x65, 0x67, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x45, 0x72, 0x72, 0x6f, 0x72,
	0x52, 0x65, 0x66, 0x52, 0x15, 0x69, 0x6e, 0x74, 0x65, 0x67, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x45, 0x72, 0x72, 0x6f, 0x72, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x9e, 0x01, 0x0a, 0x1f, 0x64,
	0x65, 0x66, 0x61, 0x75, 0x6c, 0x74, 0x5f, 0x69, 0x6e, 0x74, 0x65, 0x67, 0x72, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x5f, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x5f, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x12, 0x1c,
	0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66,
	0x2e, 0x45, 0x6e, 0x75, 0x6d, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0xf3, 0x8e, 0x06,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x37, 0x2e, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e,
	0x67, 0x2e, 0x69, 0x6e, 0x74, 0x65, 0x67, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x69, 0x6e,
	0x74, 0x65, 0x67, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x49, 0x6e, 0x74, 0x65, 0x67, 0x72,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x52, 0x65, 0x66, 0x52, 0x1c, 0x64,
	0x65, 0x66, 0x61, 0x75, 0x6c, 0x74, 0x49, 0x6e, 0x74, 0x65, 0x67, 0x72, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x42, 0x2d, 0x5a, 0x2b, 0x67,
	0x69, 0x74, 0x2e, 0x6c, 0x6f, 0x63, 0x61, 0x6c, 0x2f, 0x73, 0x65, 0x6e, 0x73, 0x69, 0x74, 0x69,
	0x76, 0x65, 0x2f, 0x69, 0x6e, 0x6e, 0x65, 0x72, 0x70, 0x62, 0x2f, 0x70, 0x72, 0x6f, 0x63, 0x65,
	0x73, 0x73, 0x69, 0x6e, 0x67, 0x2f, 0x67, 0x72, 0x70, 0x63, 0x62, 0x08, 0x65, 0x64, 0x69, 0x74,
	0x69, 0x6f, 0x6e, 0x73, 0x70, 0xe8, 0x07,
})

var (
	file_inner_processing_grpc_integration_proto_rawDescOnce sync.Once
	file_inner_processing_grpc_integration_proto_rawDescData []byte
)

func file_inner_processing_grpc_integration_proto_rawDescGZIP() []byte {
	file_inner_processing_grpc_integration_proto_rawDescOnce.Do(func() {
		file_inner_processing_grpc_integration_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_inner_processing_grpc_integration_proto_rawDesc), len(file_inner_processing_grpc_integration_proto_rawDesc)))
	})
	return file_inner_processing_grpc_integration_proto_rawDescData
}

var file_inner_processing_grpc_integration_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_inner_processing_grpc_integration_proto_msgTypes = make([]protoimpl.MessageInfo, 1)
var file_inner_processing_grpc_integration_proto_goTypes = []any{
	(IntegrationError)(0),                 // 0: processing.integration.integration.IntegrationError
	(*IntegrationErrorRef)(nil),           // 1: processing.integration.integration.IntegrationErrorRef
	(*descriptorpb.EnumValueOptions)(nil), // 2: google.protobuf.EnumValueOptions
	(*descriptorpb.EnumOptions)(nil),      // 3: google.protobuf.EnumOptions
}
var file_inner_processing_grpc_integration_proto_depIdxs = []int32{
	2, // 0: processing.integration.integration.integration_error_value:extendee -> google.protobuf.EnumValueOptions
	3, // 1: processing.integration.integration.default_integration_error_value:extendee -> google.protobuf.EnumOptions
	1, // 2: processing.integration.integration.integration_error_value:type_name -> processing.integration.integration.IntegrationErrorRef
	1, // 3: processing.integration.integration.default_integration_error_value:type_name -> processing.integration.integration.IntegrationErrorRef
	4, // [4:4] is the sub-list for method output_type
	4, // [4:4] is the sub-list for method input_type
	2, // [2:4] is the sub-list for extension type_name
	0, // [0:2] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_inner_processing_grpc_integration_proto_init() }
func file_inner_processing_grpc_integration_proto_init() {
	if File_inner_processing_grpc_integration_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_inner_processing_grpc_integration_proto_rawDesc), len(file_inner_processing_grpc_integration_proto_rawDesc)),
			NumEnums:      1,
			NumMessages:   1,
			NumExtensions: 2,
			NumServices:   0,
		},
		GoTypes:           file_inner_processing_grpc_integration_proto_goTypes,
		DependencyIndexes: file_inner_processing_grpc_integration_proto_depIdxs,
		EnumInfos:         file_inner_processing_grpc_integration_proto_enumTypes,
		MessageInfos:      file_inner_processing_grpc_integration_proto_msgTypes,
		ExtensionInfos:    file_inner_processing_grpc_integration_proto_extTypes,
	}.Build()
	File_inner_processing_grpc_integration_proto = out.File
	file_inner_processing_grpc_integration_proto_goTypes = nil
	file_inner_processing_grpc_integration_proto_depIdxs = nil
}
