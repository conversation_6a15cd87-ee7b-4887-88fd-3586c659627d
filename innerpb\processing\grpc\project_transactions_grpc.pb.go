// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.5.1
// - protoc             v5.29.3
// source: inner/processing/grpc/project_transactions.proto

package grpc

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.64.0 or later.
const _ = grpc.SupportPackageIsVersion9

const (
	ProjectTransactions_CheckAmountLimit_FullMethodName           = "/processing.merchant.project_transactions.ProjectTransactions/CheckAmountLimit"
	ProjectTransactions_CheckAttemptsWithinTimeout_FullMethodName = "/processing.merchant.project_transactions.ProjectTransactions/CheckAttemptsWithinTimeout"
	ProjectTransactions_GetTransactionLimit_FullMethodName        = "/processing.merchant.project_transactions.ProjectTransactions/GetTransactionLimit"
)

// ProjectTransactionsClient is the client API for ProjectTransactions service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type ProjectTransactionsClient interface {
	CheckAmountLimit(ctx context.Context, in *CheckAmountLimitRequestV1, opts ...grpc.CallOption) (*CheckAmountLimitResponseV1, error)
	CheckAttemptsWithinTimeout(ctx context.Context, in *CheckAttemptsWithinTimeoutRequestV1, opts ...grpc.CallOption) (*CheckAttemptsWithinTimeoutResponseV1, error)
	GetTransactionLimit(ctx context.Context, in *GetTransactionLimitRequestV1, opts ...grpc.CallOption) (*GetTransactionLimitResponseV1, error)
}

type projectTransactionsClient struct {
	cc grpc.ClientConnInterface
}

func NewProjectTransactionsClient(cc grpc.ClientConnInterface) ProjectTransactionsClient {
	return &projectTransactionsClient{cc}
}

func (c *projectTransactionsClient) CheckAmountLimit(ctx context.Context, in *CheckAmountLimitRequestV1, opts ...grpc.CallOption) (*CheckAmountLimitResponseV1, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(CheckAmountLimitResponseV1)
	err := c.cc.Invoke(ctx, ProjectTransactions_CheckAmountLimit_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *projectTransactionsClient) CheckAttemptsWithinTimeout(ctx context.Context, in *CheckAttemptsWithinTimeoutRequestV1, opts ...grpc.CallOption) (*CheckAttemptsWithinTimeoutResponseV1, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(CheckAttemptsWithinTimeoutResponseV1)
	err := c.cc.Invoke(ctx, ProjectTransactions_CheckAttemptsWithinTimeout_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *projectTransactionsClient) GetTransactionLimit(ctx context.Context, in *GetTransactionLimitRequestV1, opts ...grpc.CallOption) (*GetTransactionLimitResponseV1, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetTransactionLimitResponseV1)
	err := c.cc.Invoke(ctx, ProjectTransactions_GetTransactionLimit_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// ProjectTransactionsServer is the server API for ProjectTransactions service.
// All implementations must embed UnimplementedProjectTransactionsServer
// for forward compatibility.
type ProjectTransactionsServer interface {
	CheckAmountLimit(context.Context, *CheckAmountLimitRequestV1) (*CheckAmountLimitResponseV1, error)
	CheckAttemptsWithinTimeout(context.Context, *CheckAttemptsWithinTimeoutRequestV1) (*CheckAttemptsWithinTimeoutResponseV1, error)
	GetTransactionLimit(context.Context, *GetTransactionLimitRequestV1) (*GetTransactionLimitResponseV1, error)
	mustEmbedUnimplementedProjectTransactionsServer()
}

// UnimplementedProjectTransactionsServer must be embedded to have
// forward compatible implementations.
//
// NOTE: this should be embedded by value instead of pointer to avoid a nil
// pointer dereference when methods are called.
type UnimplementedProjectTransactionsServer struct{}

func (UnimplementedProjectTransactionsServer) CheckAmountLimit(context.Context, *CheckAmountLimitRequestV1) (*CheckAmountLimitResponseV1, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CheckAmountLimit not implemented")
}
func (UnimplementedProjectTransactionsServer) CheckAttemptsWithinTimeout(context.Context, *CheckAttemptsWithinTimeoutRequestV1) (*CheckAttemptsWithinTimeoutResponseV1, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CheckAttemptsWithinTimeout not implemented")
}
func (UnimplementedProjectTransactionsServer) GetTransactionLimit(context.Context, *GetTransactionLimitRequestV1) (*GetTransactionLimitResponseV1, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetTransactionLimit not implemented")
}
func (UnimplementedProjectTransactionsServer) mustEmbedUnimplementedProjectTransactionsServer() {}
func (UnimplementedProjectTransactionsServer) testEmbeddedByValue()                             {}

// UnsafeProjectTransactionsServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to ProjectTransactionsServer will
// result in compilation errors.
type UnsafeProjectTransactionsServer interface {
	mustEmbedUnimplementedProjectTransactionsServer()
}

func RegisterProjectTransactionsServer(s grpc.ServiceRegistrar, srv ProjectTransactionsServer) {
	// If the following call pancis, it indicates UnimplementedProjectTransactionsServer was
	// embedded by pointer and is nil.  This will cause panics if an
	// unimplemented method is ever invoked, so we test this at initialization
	// time to prevent it from happening at runtime later due to I/O.
	if t, ok := srv.(interface{ testEmbeddedByValue() }); ok {
		t.testEmbeddedByValue()
	}
	s.RegisterService(&ProjectTransactions_ServiceDesc, srv)
}

func _ProjectTransactions_CheckAmountLimit_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CheckAmountLimitRequestV1)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ProjectTransactionsServer).CheckAmountLimit(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ProjectTransactions_CheckAmountLimit_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ProjectTransactionsServer).CheckAmountLimit(ctx, req.(*CheckAmountLimitRequestV1))
	}
	return interceptor(ctx, in, info, handler)
}

func _ProjectTransactions_CheckAttemptsWithinTimeout_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CheckAttemptsWithinTimeoutRequestV1)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ProjectTransactionsServer).CheckAttemptsWithinTimeout(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ProjectTransactions_CheckAttemptsWithinTimeout_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ProjectTransactionsServer).CheckAttemptsWithinTimeout(ctx, req.(*CheckAttemptsWithinTimeoutRequestV1))
	}
	return interceptor(ctx, in, info, handler)
}

func _ProjectTransactions_GetTransactionLimit_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetTransactionLimitRequestV1)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ProjectTransactionsServer).GetTransactionLimit(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ProjectTransactions_GetTransactionLimit_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ProjectTransactionsServer).GetTransactionLimit(ctx, req.(*GetTransactionLimitRequestV1))
	}
	return interceptor(ctx, in, info, handler)
}

// ProjectTransactions_ServiceDesc is the grpc.ServiceDesc for ProjectTransactions service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var ProjectTransactions_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "processing.merchant.project_transactions.ProjectTransactions",
	HandlerType: (*ProjectTransactionsServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "CheckAmountLimit",
			Handler:    _ProjectTransactions_CheckAmountLimit_Handler,
		},
		{
			MethodName: "CheckAttemptsWithinTimeout",
			Handler:    _ProjectTransactions_CheckAttemptsWithinTimeout_Handler,
		},
		{
			MethodName: "GetTransactionLimit",
			Handler:    _ProjectTransactions_GetTransactionLimit_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "inner/processing/grpc/project_transactions.proto",
}
