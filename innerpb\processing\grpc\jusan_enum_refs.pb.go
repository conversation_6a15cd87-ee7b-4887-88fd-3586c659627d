// Code generated by protoc-gen-go-mvp. DO NOT EDIT.
// versions:
// - protoc-gen-go-mvp v2.0.1
// created by mvp authors

package grpc

func (x JusanResponseStatus) Description() string {
	switch x {
	case JusanResponseStatus_Stage1Status1:
		return "Ошибка"
	case JusanResponseStatus_Stage2Status1:
		return "Ошибка"
	case JusanResponseStatus_Stage3Status1:
		return "Ошибка"
	case JusanResponseStatus_Stage1Status0:
		return "В процессе"
	case JusanResponseStatus_Stage2Status0:
		return "В процессе"
	case JusanResponseStatus_Stage1Status2:
		return "В процессе"
	case JusanResponseStatus_Stage2Status2:
		return "В процессе"
	case JusanResponseStatus_Stage3Status2:
		return "В процессе"
	case JusanResponseStatus_Stage4Status0:
		return "Успешно"
	case JusanResponseStatus_Stage4Status1:
		return "Успешно"
	case JusanResponseStatus_Stage4Status2:
		return "Успешно"
	default:
		return "0"
	}
}

func (x JusanResponseStatus) Stage() string {
	switch x {
	case JusanResponseStatus_Stage1Status1:
		return "1"
	case JusanResponseStatus_Stage2Status1:
		return "2"
	case JusanResponseStatus_Stage3Status1:
		return "3"
	case JusanResponseStatus_Stage1Status0:
		return "1"
	case JusanResponseStatus_Stage2Status0:
		return "2"
	case JusanResponseStatus_Stage1Status2:
		return "1"
	case JusanResponseStatus_Stage2Status2:
		return "2"
	case JusanResponseStatus_Stage3Status2:
		return "3"
	case JusanResponseStatus_Stage4Status0:
		return "4"
	case JusanResponseStatus_Stage4Status1:
		return "4"
	case JusanResponseStatus_Stage4Status2:
		return "4"
	default:
		return "0"
	}
}

func (x JusanResponseStatus) Status() string {
	switch x {
	case JusanResponseStatus_Stage1Status1:
		return "1"
	case JusanResponseStatus_Stage2Status1:
		return "1"
	case JusanResponseStatus_Stage3Status1:
		return "1"
	case JusanResponseStatus_Stage1Status0:
		return "0"
	case JusanResponseStatus_Stage2Status0:
		return "0"
	case JusanResponseStatus_Stage1Status2:
		return "2"
	case JusanResponseStatus_Stage2Status2:
		return "2"
	case JusanResponseStatus_Stage3Status2:
		return "2"
	case JusanResponseStatus_Stage4Status0:
		return "0"
	case JusanResponseStatus_Stage4Status1:
		return "1"
	case JusanResponseStatus_Stage4Status2:
		return "2"
	default:
		return "0"
	}
}

func (x JusanResponseStatus) TransactionStatus() EnumTransactionStatus {
	switch x {
	case JusanResponseStatus_Stage1Status1:
		return EnumTransactionStatus_TransactionStatusFailed
	case JusanResponseStatus_Stage2Status1:
		return EnumTransactionStatus_TransactionStatusFailed
	case JusanResponseStatus_Stage3Status1:
		return EnumTransactionStatus_TransactionStatusFailed
	case JusanResponseStatus_Stage1Status0:
		return EnumTransactionStatus_TransactionStatusProcessed
	case JusanResponseStatus_Stage2Status0:
		return EnumTransactionStatus_TransactionStatusProcessed
	case JusanResponseStatus_Stage1Status2:
		return EnumTransactionStatus_TransactionStatusProcessed
	case JusanResponseStatus_Stage2Status2:
		return EnumTransactionStatus_TransactionStatusProcessed
	case JusanResponseStatus_Stage3Status2:
		return EnumTransactionStatus_TransactionStatusProcessed
	case JusanResponseStatus_Stage4Status0:
		return EnumTransactionStatus_TransactionStatusSuccess
	case JusanResponseStatus_Stage4Status1:
		return EnumTransactionStatus_TransactionStatusSuccess
	case JusanResponseStatus_Stage4Status2:
		return EnumTransactionStatus_TransactionStatusSuccess
	default:
		return EnumTransactionStatus_TransactionStatusError
	}
}

// Created reference to JusanResponseStatus

//	|	JusanResponseStatus              	|	Description          	|	Stage	|	Status	|	TransactionStatus                               	|
//	|	JusanResponseStatus_Stage1Status1	|	"Ошибка"             	|	"1"  	|	"1"   	|	EnumTransactionStatus_TransactionStatusFailed   	|
//	|	JusanResponseStatus_Stage2Status1	|	"Ошибка"             	|	"2"  	|	"1"   	|	EnumTransactionStatus_TransactionStatusFailed   	|
//	|	JusanResponseStatus_Stage3Status1	|	"Ошибка"             	|	"3"  	|	"1"   	|	EnumTransactionStatus_TransactionStatusFailed   	|
//	|	JusanResponseStatus_Stage1Status0	|	"В процессе"         	|	"1"  	|	"0"   	|	EnumTransactionStatus_TransactionStatusProcessed	|
//	|	JusanResponseStatus_Stage2Status0	|	"В процессе"         	|	"2"  	|	"0"   	|	EnumTransactionStatus_TransactionStatusProcessed	|
//	|	JusanResponseStatus_Stage1Status2	|	"В процессе"         	|	"1"  	|	"2"   	|	EnumTransactionStatus_TransactionStatusProcessed	|
//	|	JusanResponseStatus_Stage2Status2	|	"В процессе"         	|	"2"  	|	"2"   	|	EnumTransactionStatus_TransactionStatusProcessed	|
//	|	JusanResponseStatus_Stage3Status2	|	"В процессе"         	|	"3"  	|	"2"   	|	EnumTransactionStatus_TransactionStatusProcessed	|
//	|	JusanResponseStatus_Stage4Status0	|	"Успешно"            	|	"4"  	|	"0"   	|	EnumTransactionStatus_TransactionStatusSuccess  	|
//	|	JusanResponseStatus_Stage4Status1	|	"Успешно"            	|	"4"  	|	"1"   	|	EnumTransactionStatus_TransactionStatusSuccess  	|
//	|	JusanResponseStatus_Stage4Status2	|	"Успешно"            	|	"4"  	|	"2"   	|	EnumTransactionStatus_TransactionStatusSuccess  	|

var SliceJusanResponseStatusRefs *sliceJusanResponseStatusRefs

type sliceJusanResponseStatusRefs struct{}

func (*sliceJusanResponseStatusRefs) Description(slice ...JusanResponseStatus) []string {
	var result []string
	for _, val := range slice {
		result = append(result, val.Description())
	}

	return result
}

func (*sliceJusanResponseStatusRefs) Stage(slice ...JusanResponseStatus) []string {
	var result []string
	for _, val := range slice {
		result = append(result, val.Stage())
	}

	return result
}

func (*sliceJusanResponseStatusRefs) Status(slice ...JusanResponseStatus) []string {
	var result []string
	for _, val := range slice {
		result = append(result, val.Status())
	}

	return result
}

func (*sliceJusanResponseStatusRefs) TransactionStatus(slice ...JusanResponseStatus) []EnumTransactionStatus {
	var result []EnumTransactionStatus
	for _, val := range slice {
		result = append(result, val.TransactionStatus())
	}

	return result
}
