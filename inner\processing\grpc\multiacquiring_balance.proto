edition = "2023";

package processing.multiacquiring.multiacquiring_balance;

option go_package = "git.local/sensitive/innerpb/processing/grpc";

import "google/protobuf/struct.proto";

service MultiacquiringBalance {
  rpc CheckBalance(CheckBalanceRequest) returns (CheckBalanceResponse) {}
}

message CheckBalanceRequest {
  uint64 terminal_id = 1;
  string acquirer_code = 2;
  google.protobuf.Struct config = 3;
}

message CheckBalanceResponse {
  double amount = 1;
  string message = 2;
  uint64 terminal_id = 3;
  string acquirer_code = 4;
}